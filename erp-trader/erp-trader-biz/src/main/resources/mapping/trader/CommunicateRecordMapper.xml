<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.CommunicateRecordMapper">
    <sql id="Base_Column_List">
        COMMUNICATE_RECORD_ID,
        COMMUNICATE_TYPE,
        COMPANY_ID,
        RELATED_ID,
        TRADER_ID,
        TRADER_TYPE,
        BEGINTIME,
        ENDTIME,
        TRADER_CONTACT_ID,
        COMMUNICATE_MODE,
        COMMUNICATE_GOAL,
        NEXT_CONTACT_DATE,
        PHONE,
        COID,
        COID_TYPE,
        COID_LENGTH,
        COID_DOMAIN,
        COID_URI,
        NEXT_CONTACT_CONTENT,
        COMMENTS,
        IS_DONE,
        AFTER_SALES_TRADER_ID,
        RELATE_COMMUNICATE_RECORD_ID,
        CONTACT_CONTENT,
        CONTACT,
        CONTACT_MOB,
        ADD_TIME,
        CREATOR,
        MOD_TIME,
        UPDATER,
        SYNC_STATUS,
        IS_LFASR,
        IS_COMMENT,
        TT_NUMBER,
        CONTENT_SUFFIX,
        NONE_NEXT_DATE,
        TELEPHONE,
        FOLLOW_UP_TYPE,
        BELONG_USER_ID,
        BELONG_USER_NAME,
        TRADER_NAME
    </sql>
    <resultMap id="tagMap" type="com.vedeng.erp.system.dto.TagDto" >
        <result column="TAG_ID" property="tagId" jdbcType="INTEGER" />
        <result column="TAG_TYPE" property="tagType" />
        <result column="IS_RECOMMEND" property="isRecommend" />
        <result column="TAG_NAME" property="tagName" />
    </resultMap>

    <resultMap id="BaseResultMap1" type="com.vedeng.erp.trader.domain.entity.CommunicateRecordEntity">
        <!--@mbg.generated-->
        <!--@Table T_COMMUNICATE_RECORD-->
        <id column="COMMUNICATE_RECORD_ID" jdbcType="INTEGER" property="communicateRecordId" />
        <result column="COMMUNICATE_TYPE" jdbcType="INTEGER" property="communicateType" />
        <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
        <result column="RELATED_ID" jdbcType="INTEGER" property="relatedId" />
        <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
        <result column="TRADER_TYPE" jdbcType="TINYINT" property="traderType" />
        <result column="BEGINTIME" jdbcType="BIGINT" property="begintime" />
        <result column="ENDTIME" jdbcType="BIGINT" property="endtime" />
        <result column="TRADER_CONTACT_ID" jdbcType="INTEGER" property="traderContactId" />
        <result column="COMMUNICATE_MODE" jdbcType="INTEGER" property="communicateMode" />
        <result column="COMMUNICATE_GOAL" jdbcType="INTEGER" property="communicateGoal" />
        <result column="NEXT_CONTACT_DATE" jdbcType="DATE" property="nextContactDate" />
        <result column="PHONE" jdbcType="VARCHAR" property="phone" />
        <result column="COID" jdbcType="VARCHAR" property="coid" />
        <result column="COID_TYPE" jdbcType="BOOLEAN" property="coidType" />
        <result column="COID_LENGTH" jdbcType="INTEGER" property="coidLength" />
        <result column="COID_DOMAIN" jdbcType="VARCHAR" property="coidDomain" />
        <result column="COID_URI" jdbcType="VARCHAR" property="coidUri" />
        <result column="NEXT_CONTACT_CONTENT" jdbcType="VARCHAR" property="nextContactContent" />
        <result column="COMMENTS" jdbcType="LONGVARCHAR" property="comments" />
        <result column="IS_DONE" jdbcType="TINYINT" property="isDone" />
        <result column="AFTER_SALES_TRADER_ID" jdbcType="INTEGER" property="afterSalesTraderId" />
        <result column="RELATE_COMMUNICATE_RECORD_ID" jdbcType="INTEGER" property="relateCommunicateRecordId" />
        <result column="CONTACT_CONTENT" jdbcType="VARCHAR" property="contactContent" />
        <result column="CONTACT" jdbcType="VARCHAR" property="contact" />
        <result column="CONTACT_MOB" jdbcType="VARCHAR" property="contactMob" />
        <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
        <result column="CREATOR" jdbcType="INTEGER" property="creator" />
        <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
        <result column="UPDATER" jdbcType="INTEGER" property="updater" />
        <result column="SYNC_STATUS" jdbcType="TINYINT" property="syncStatus" />
        <result column="IS_LFASR" jdbcType="TINYINT" property="isLfasr" />
        <result column="IS_COMMENT" jdbcType="TINYINT" property="isComment" />
        <result column="TT_NUMBER" jdbcType="VARCHAR" property="ttNumber" />
        <result column="CONTENT_SUFFIX" jdbcType="VARCHAR" property="contentSuffix" />
        <result column="NONE_NEXT_DATE" jdbcType="TINYINT" property="noneNextDate" />
        <result column="TELEPHONE" jdbcType="VARCHAR" property="telephone" />
        <result column="FOLLOW_UP_TYPE" jdbcType="INTEGER" property="followUpType" />
        <result column="BELONG_USER_ID" jdbcType="INTEGER" property="belongUserId" />
        <result column="BELONG_USER_NAME" jdbcType="VARCHAR" property="belongUserName" />
        <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName" />
    </resultMap>


    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap1">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List" />
        from T_COMMUNICATE_RECORD
        where COMMUNICATE_RECORD_ID = #{communicateRecordId,jdbcType=INTEGER}
    </select>

    <select id="selectByCoid" parameterType="java.lang.String" resultMap="BaseResultMap1">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List" />
        from T_COMMUNICATE_RECORD
        where COID = #{coid,jdbcType=VARCHAR}
        order by COMMUNICATE_RECORD_ID desc limit 1
    </select>
    <!--查询指定行数据-->
    <select id="findByAll" resultType="com.vedeng.erp.trader.dto.CommunicateRecordDto">
        select
        TCR.COMMUNICATE_RECORD_ID,
        TCR.COMMUNICATE_TYPE,
        TCR.COMPANY_ID,
        TCR.RELATED_ID,
        TCR.TRADER_ID,
        TCR.TRADER_TYPE,
        TCR.BEGINTIME,
        TCR.ENDTIME,
        TCR.TRADER_CONTACT_ID,
        TCR.COMMUNICATE_MODE,
        TCR.COMMUNICATE_GOAL,
        TCR.NEXT_CONTACT_DATE,
        TCR.PHONE,
        TCR.COID,
        TCR.COID_TYPE,
        TCR.COID_LENGTH,
        TCR.COID_DOMAIN,
        TCR.COID_URI,
        TCR.NEXT_CONTACT_CONTENT,
        TCR.COMMENTS,
        TCR.IS_DONE,
        TCR.AFTER_SALES_TRADER_ID,
        TCR.RELATE_COMMUNICATE_RECORD_ID,
        TCR.CONTACT_CONTENT,
        TCR.CONTACT,
        TCR.CONTACT_MOB,
        TCR.ADD_TIME,
        TCR.CREATOR,
        TCR.MOD_TIME,
        TCR.UPDATER,
        TCR.SYNC_STATUS,
        TCR.IS_LFASR,
        TCR.IS_COMMENT,
        TCR.TT_NUMBER,
        TCR.CONTENT_SUFFIX,
        TCR.NONE_NEXT_DATE,
        TCR.TELEPHONE,
        TCR.FOLLOW_UP_TYPE,
        TCR.BELONG_USER_ID,
        TCR.BELONG_USER_NAME,
        TCR.TRADER_NAME,
        TCS.COMMUNICATE_SUMMARY_ID 'communicateAiSummaryApiDto.communicateSummaryId',
        TCS.CUSTOMER_INTENTIONS 'communicateAiSummaryApiDto.customerIntentions',
        TCS.INTENTION_GOODS 'communicateAiSummaryApiDto.intentionGoods',
        TCS.BRANDS 'communicateAiSummaryApiDto.brands',
        TCS.MODELS 'communicateAiSummaryApiDto.models',
        TCS.CUSTOMER_TYPES 'communicateAiSummaryApiDto.customerTypes',
        TCS.IS_INTENTION 'communicateAiSummaryApiDto.isIntention',
        TCS.IS_ADD_WECHAT 'communicateAiSummaryApiDto.isAddWechat',
        TCS.IS_EFFECTIVE_COMMUNICATION 'communicateAiSummaryApiDto.isEffectiveCommunication'
        from T_COMMUNICATE_RECORD TCR
        left join  T_COMMUNICATE_SUMMARY TCS on TCR.COMMUNICATE_RECORD_ID = TCS.COMMUNICATE_RECORD_ID
        <where>
            <if test="communicateRecordId != null">
                and TCR.COMMUNICATE_RECORD_ID = #{communicateRecordId}
            </if>
            <if test="communicateType != null">
                and TCR.COMMUNICATE_TYPE = #{communicateType}
            </if>
            <if test="companyId != null">
                and TCR.COMPANY_ID = #{companyId}
            </if>
            <if test="relatedId != null">
                and TCR.RELATED_ID = #{relatedId}
            </if>
            <if test="traderId != null">
                and TCR.TRADER_ID = #{traderId}
            </if>
            <if test="traderType != null">
                and TCR.TRADER_TYPE = #{traderType}
            </if>
            <if test="begintime != null">
                and TCR.BEGINTIME = #{begintime}
            </if>
            <if test="endtime != null">
                and TCR.ENDTIME = #{endtime}
            </if>
            <if test="traderContactId != null">
                and TCR.TRADER_CONTACT_ID = #{traderContactId}
            </if>
            <if test="communicateMode != null">
                and TCR.COMMUNICATE_MODE = #{communicateMode}
            </if>
            <if test="communicateGoal != null">
                and TCR.COMMUNICATE_GOAL = #{communicateGoal}
            </if>
            <if test="nextContactDate != null">
                and TCR.NEXT_CONTACT_DATE = #{nextContactDate}
            </if>
            <if test="phone != null and phone != ''">
                and TCR.PHONE = #{phone}
            </if>
            <if test="coid != null and coid != ''">
                and TCR.COID = #{coid}
            </if>
            <if test="coidType != null">
                and TCR.COID_TYPE = #{coidType}
            </if>
            <if test="coidLength != null">
                and TCR.COID_LENGTH = #{coidLength}
            </if>
            <if test="coidDomain != null and coidDomain != ''">
                and TCR.COID_DOMAIN = #{coidDomain}
            </if>
            <if test="coidUri != null and coidUri != ''">
                and TCR.COID_URI = #{coidUri}
            </if>
            <if test="nextContactContent != null and nextContactContent != ''">
                and TCR.NEXT_CONTACT_CONTENT = #{nextContactContent}
            </if>
            <if test="comments != null and comments != ''">
                and TCR.COMMENTS = #{comments}
            </if>
            <if test="isDone != null">
                and TCR.IS_DONE = #{isDone}
            </if>
            <if test="afterSalesTraderId != null">
                and TCR.AFTER_SALES_TRADER_ID = #{afterSalesTraderId}
            </if>
            <if test="relateCommunicateRecordId != null">
                and TCR.RELATE_COMMUNICATE_RECORD_ID = #{relateCommunicateRecordId}
            </if>
            <if test="contactContent != null and contactContent != ''">
                and TCR.CONTACT_CONTENT = #{contactContent}
            </if>
            <if test="contact != null and contact != ''">
                and TCR.CONTACT = #{contact}
            </if>
            <if test="contactMob != null and contactMob != ''">
                and TCR.CONTACT_MOB = #{contactMob}
            </if>
            <if test="addTime != null">
                and TCR.ADD_TIME = #{addTime}
            </if>
            <if test="creator != null">
                and TCR.CREATOR = #{creator}
            </if>
            <if test="modTime != null">
                and TCR.MOD_TIME = #{modTime}
            </if>
            <if test="updater != null">
                and TCR.UPDATER = #{updater}
            </if>
            <if test="syncStatus != null">
                and TCR.SYNC_STATUS = #{syncStatus}
            </if>
            <if test="isLfasr != null">
                and TCR.IS_LFASR = #{isLfasr}
            </if>
            <if test="isComment != null">
                and TCR.IS_COMMENT = #{isComment}
            </if>
            <if test="ttNumber != null and ttNumber != ''">
                and TCR.TT_NUMBER = #{ttNumber}
            </if>
            <if test="contentSuffix != null and contentSuffix != ''">
                and TCR.CONTENT_SUFFIX = #{contentSuffix}
            </if>
            <if test="noneNextDate != null">
                and TCR.NONE_NEXT_DATE = #{noneNextDate}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="communicateRecordId" useGeneratedKeys="true">
        insert into T_COMMUNICATE_RECORD(COMMUNICATE_TYPE, COMPANY_ID, RELATED_ID, TRADER_ID, TRADER_TYPE, BEGINTIME,
                                         ENDTIME, TRADER_CONTACT_ID, COMMUNICATE_MODE, COMMUNICATE_GOAL,
                                         NEXT_CONTACT_DATE, PHONE, COID, COID_TYPE, COID_LENGTH, COID_DOMAIN, COID_URI,
                                         NEXT_CONTACT_CONTENT, COMMENTS, IS_DONE, AFTER_SALES_TRADER_ID,
                                         RELATE_COMMUNICATE_RECORD_ID, CONTACT_CONTENT, CONTACT, CONTACT_MOB, ADD_TIME,
                                         CREATOR, MOD_TIME, UPDATER, SYNC_STATUS, IS_LFASR, IS_COMMENT, TT_NUMBER,
                                         CONTENT_SUFFIX, NONE_NEXT_DATE, TELEPHONE, FOLLOW_UP_TYPE, BELONG_USER_ID, BELONG_USER_NAME, TRADER_NAME)
        values (#{communicateType}, #{companyId}, #{relatedId}, #{traderId}, #{traderType}, #{begintime}, #{endtime},
                #{traderContactId}, #{communicateMode}, #{communicateGoal}, #{nextContactDate}, #{phone}, #{coid},
                #{coidType}, #{coidLength}, #{coidDomain}, #{coidUri}, #{nextContactContent}, #{comments}, #{isDone},
                #{afterSalesTraderId}, #{relateCommunicateRecordId}, #{contactContent}, #{contact}, #{contactMob},
                #{addTime}, #{creator}, #{modTime}, #{updater}, #{syncStatus}, #{isLfasr}, #{isComment}, #{ttNumber},
                #{contentSuffix}, #{noneNextDate}, #{telephone}, #{followUpType}, #{belongUserId,jdbcType=INTEGER}, #{belongUserName,jdbcType=VARCHAR}), #{traderName,jdbcType=VARCHAR}
    </insert>

    <insert id="insertSelective" keyColumn="COMMUNICATE_RECORD_ID" keyProperty="communicateRecordId" parameterType="com.vedeng.erp.trader.domain.entity.CommunicateRecordEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_COMMUNICATE_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="communicateType != null">
                COMMUNICATE_TYPE,
            </if>
            <if test="companyId != null">
                COMPANY_ID,
            </if>
            <if test="relatedId != null">
                RELATED_ID,
            </if>
            <if test="traderId != null">
                TRADER_ID,
            </if>
            <if test="traderType != null">
                TRADER_TYPE,
            </if>
            <if test="begintime != null">
                BEGINTIME,
            </if>
            <if test="endtime != null">
                ENDTIME,
            </if>
            <if test="traderContactId != null">
                TRADER_CONTACT_ID,
            </if>
            <if test="communicateMode != null">
                COMMUNICATE_MODE,
            </if>
            <if test="communicateGoal != null">
                COMMUNICATE_GOAL,
            </if>
            <if test="nextContactDate != null">
                NEXT_CONTACT_DATE,
            </if>
            <if test="phone != null">
                PHONE,
            </if>
            <if test="coid != null">
                COID,
            </if>
            <if test="coidType != null">
                COID_TYPE,
            </if>
            <if test="coidLength != null">
                COID_LENGTH,
            </if>
            <if test="coidDomain != null">
                COID_DOMAIN,
            </if>
            <if test="coidUri != null">
                COID_URI,
            </if>
            <if test="nextContactContent != null">
                NEXT_CONTACT_CONTENT,
            </if>
            <if test="comments != null">
                COMMENTS,
            </if>
            <if test="isDone != null">
                IS_DONE,
            </if>
            <if test="afterSalesTraderId != null">
                AFTER_SALES_TRADER_ID,
            </if>
            <if test="relateCommunicateRecordId != null">
                RELATE_COMMUNICATE_RECORD_ID,
            </if>
            <if test="contactContent != null">
                CONTACT_CONTENT,
            </if>
            <if test="contact != null">
                CONTACT,
            </if>
            <if test="contactMob != null">
                CONTACT_MOB,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="syncStatus != null">
                SYNC_STATUS,
            </if>
            <if test="isLfasr != null">
                IS_LFASR,
            </if>
            <if test="isComment != null">
                IS_COMMENT,
            </if>
            <if test="ttNumber != null">
                TT_NUMBER,
            </if>
            <if test="contentSuffix != null">
                CONTENT_SUFFIX,
            </if>
            <if test="noneNextDate != null">
                NONE_NEXT_DATE,
            </if>
            <if test="telephone != null and telephone != ''">
                TELEPHONE,
            </if>
            <if test="followUpType != null">
                FOLLOW_UP_TYPE,
            </if>
            <if test="belongUserId != null">
                BELONG_USER_ID,
            </if>
            <if test="belongUserName != null and belongUserName != ''">
                BELONG_USER_NAME,
            </if>
            <if test="traderName != null and traderName != ''">
                TRADER_NAME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="communicateType != null">
                #{communicateType,jdbcType=INTEGER},
            </if>
            <if test="companyId != null">
                #{companyId,jdbcType=INTEGER},
            </if>
            <if test="relatedId != null">
                #{relatedId,jdbcType=INTEGER},
            </if>
            <if test="traderId != null">
                #{traderId,jdbcType=INTEGER},
            </if>
            <if test="traderType != null">
                #{traderType,jdbcType=BOOLEAN},
            </if>
            <if test="begintime != null">
                #{begintime,jdbcType=BIGINT},
            </if>
            <if test="endtime != null">
                #{endtime,jdbcType=BIGINT},
            </if>
            <if test="traderContactId != null">
                #{traderContactId,jdbcType=INTEGER},
            </if>
            <if test="communicateMode != null">
                #{communicateMode,jdbcType=INTEGER},
            </if>
            <if test="communicateGoal != null">
                #{communicateGoal,jdbcType=INTEGER},
            </if>
            <if test="nextContactDate != null">
                #{nextContactDate,jdbcType=DATE},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="coid != null">
                #{coid,jdbcType=VARCHAR},
            </if>
            <if test="coidType != null">
                #{coidType,jdbcType=BOOLEAN},
            </if>
            <if test="coidLength != null">
                #{coidLength,jdbcType=INTEGER},
            </if>
            <if test="coidDomain != null">
                #{coidDomain,jdbcType=VARCHAR},
            </if>
            <if test="coidUri != null">
                #{coidUri,jdbcType=VARCHAR},
            </if>
            <if test="nextContactContent != null">
                #{nextContactContent,jdbcType=VARCHAR},
            </if>
            <if test="comments != null">
                #{comments,jdbcType=LONGVARCHAR},
            </if>
            <if test="isDone != null">
                #{isDone,jdbcType=BOOLEAN},
            </if>
            <if test="afterSalesTraderId != null">
                #{afterSalesTraderId,jdbcType=INTEGER},
            </if>
            <if test="relateCommunicateRecordId != null">
                #{relateCommunicateRecordId,jdbcType=INTEGER},
            </if>
            <if test="contactContent != null">
                #{contactContent,jdbcType=VARCHAR},
            </if>
            <if test="contact != null">
                #{contact,jdbcType=VARCHAR},
            </if>
            <if test="contactMob != null">
                #{contactMob,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="syncStatus != null">
                #{syncStatus,jdbcType=BOOLEAN},
            </if>
            <if test="isLfasr != null">
                #{isLfasr,jdbcType=BOOLEAN},
            </if>
            <if test="isComment != null">
                #{isComment,jdbcType=BOOLEAN},
            </if>
            <if test="ttNumber != null">
                #{ttNumber,jdbcType=VARCHAR},
            </if>
            <if test="contentSuffix != null">
                #{contentSuffix,jdbcType=VARCHAR},
            </if>
            <if test="noneNextDate != null">
                #{noneNextDate,jdbcType=BOOLEAN},
            </if>
            <if test="telephone != null and telephone != ''">
                #{telephone,jdbcType=VARCHAR},
            </if>
            <if test="followUpType != null">
                #{followUpType,jdbcType=INTEGER},
            </if>
            <if test="belongUserId != null">
                #{belongUserId,jdbcType=INTEGER},
            </if>
            <if test="belongUserName != null and belongUserName != ''">
                #{belongUserName,jdbcType=VARCHAR},
            </if>
            <if test="traderName != null and traderName != ''">
                #{traderName,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>



<!--auto generated by MybatisCodeHelper on 2022-07-22-->
    <update id="updateIsDoneByNextContactDateAndRelatedIdAndCommunicateType">
        update T_COMMUNICATE_RECORD
        set IS_DONE=#{isDone,jdbcType=INTEGER}
        where NEXT_CONTACT_DATE <![CDATA[<=]]> #{nextContactDate,jdbcType=TIMESTAMP} and RELATED_ID=#{relatedId,jdbcType=INTEGER}
        and COMMUNICATE_TYPE = #{communicateType,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.CommunicateRecordEntity">
        <!--@mbg.generated-->
        update T_COMMUNICATE_RECORD
        <set>
            <if test="communicateType != null">
                COMMUNICATE_TYPE = #{communicateType,jdbcType=INTEGER},
            </if>
            <if test="companyId != null">
                COMPANY_ID = #{companyId,jdbcType=INTEGER},
            </if>
            <if test="relatedId != null">
                RELATED_ID = #{relatedId,jdbcType=INTEGER},
            </if>
            <if test="traderId != null">
                TRADER_ID = #{traderId,jdbcType=INTEGER},
            </if>
            <if test="traderType != null">
                TRADER_TYPE = #{traderType,jdbcType=BOOLEAN},
            </if>
            <if test="begintime != null">
                BEGINTIME = #{begintime,jdbcType=BIGINT},
            </if>
            <if test="endtime != null">
                ENDTIME = #{endtime,jdbcType=BIGINT},
            </if>
            <if test="traderContactId != null">
                TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
            </if>
            <if test="communicateMode != null">
                COMMUNICATE_MODE = #{communicateMode,jdbcType=INTEGER},
            </if>
            <if test="communicateGoal != null">
                COMMUNICATE_GOAL = #{communicateGoal,jdbcType=INTEGER},
            </if>
            <if test="nextContactDate != null">
                NEXT_CONTACT_DATE = #{nextContactDate,jdbcType=DATE},
            </if>
            <if test="phone != null">
                PHONE = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="coid != null">
                COID = #{coid,jdbcType=VARCHAR},
            </if>
            <if test="coidType != null">
                COID_TYPE = #{coidType,jdbcType=BOOLEAN},
            </if>
            <if test="coidLength != null">
                COID_LENGTH = #{coidLength,jdbcType=INTEGER},
            </if>
            <if test="coidDomain != null">
                COID_DOMAIN = #{coidDomain,jdbcType=VARCHAR},
            </if>
            <if test="coidUri != null">
                COID_URI = #{coidUri,jdbcType=VARCHAR},
            </if>
            <if test="nextContactContent != null">
                NEXT_CONTACT_CONTENT = #{nextContactContent,jdbcType=VARCHAR},
            </if>
            <if test="comments != null">
                COMMENTS = #{comments,jdbcType=LONGVARCHAR},
            </if>
            <if test="isDone != null">
                IS_DONE = #{isDone,jdbcType=BOOLEAN},
            </if>
            <if test="afterSalesTraderId != null">
                AFTER_SALES_TRADER_ID = #{afterSalesTraderId,jdbcType=INTEGER},
            </if>
            <if test="relateCommunicateRecordId != null">
                RELATE_COMMUNICATE_RECORD_ID = #{relateCommunicateRecordId,jdbcType=INTEGER},
            </if>
            <if test="contactContent != null">
                CONTACT_CONTENT = #{contactContent,jdbcType=VARCHAR},
            </if>
            <if test="contact != null">
                CONTACT = #{contact,jdbcType=VARCHAR},
            </if>
            <if test="contactMob != null">
                CONTACT_MOB = #{contactMob,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="syncStatus != null">
                SYNC_STATUS = #{syncStatus,jdbcType=BOOLEAN},
            </if>
            <if test="isLfasr != null">
                IS_LFASR = #{isLfasr,jdbcType=BOOLEAN},
            </if>
            <if test="isComment != null">
                IS_COMMENT = #{isComment,jdbcType=BOOLEAN},
            </if>
            <if test="ttNumber != null">
                TT_NUMBER = #{ttNumber,jdbcType=VARCHAR},
            </if>
            <if test="contentSuffix != null">
                CONTENT_SUFFIX = #{contentSuffix,jdbcType=VARCHAR},
            </if>
            <if test="noneNextDate != null">
                NONE_NEXT_DATE = #{noneNextDate,jdbcType=BOOLEAN},
            </if>
            <if test="telephone != null and telephone != ''">
                TELEPHONE = #{telephone,jdbcType=VARCHAR},
            </if>
            <if test="followUpType != null">
                FOLLOW_UP_TYPE = #{followUpType,jdbcType=INTEGER},
            </if>
            <if test="belongUserId != null">
                BELONG_USER_ID = #{belongUserId,jdbcType=INTEGER},
            </if>
            <if test="belongUserName != null and belongUserName != ''">
                BELONG_USER_NAME = #{belongUserName,jdbcType=VARCHAR},
            </if>
            <if test="traderName != null and traderName != ''">
                TRADER_NAME = #{traderName,jdbcType=VARCHAR},
            </if>
        </set>
        where COMMUNICATE_RECORD_ID = #{communicateRecordId,jdbcType=INTEGER}
    </update>

    <update id="updateByCoid" parameterType="com.vedeng.erp.trader.domain.entity.CommunicateRecordEntity">
        <!--@mbg.generated-->
        update T_COMMUNICATE_RECORD
        <set>
            <if test="communicateType != null">
                COMMUNICATE_TYPE = #{communicateType,jdbcType=INTEGER},
            </if>
            <if test="companyId != null">
                COMPANY_ID = #{companyId,jdbcType=INTEGER},
            </if>
            <if test="relatedId != null">
                RELATED_ID = #{relatedId,jdbcType=INTEGER},
            </if>
            <if test="traderId != null">
                TRADER_ID = #{traderId,jdbcType=INTEGER},
            </if>
            <if test="traderType != null">
                TRADER_TYPE = #{traderType,jdbcType=BOOLEAN},
            </if>
            <if test="begintime != null">
                BEGINTIME = #{begintime,jdbcType=BIGINT},
            </if>
            <if test="endtime != null">
                ENDTIME = #{endtime,jdbcType=BIGINT},
            </if>
            <if test="traderContactId != null">
                TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
            </if>
            <if test="communicateMode != null">
                COMMUNICATE_MODE = #{communicateMode,jdbcType=INTEGER},
            </if>
            <if test="communicateGoal != null">
                COMMUNICATE_GOAL = #{communicateGoal,jdbcType=INTEGER},
            </if>
            <if test="nextContactDate != null">
                NEXT_CONTACT_DATE = #{nextContactDate,jdbcType=DATE},
            </if>
            <if test="phone != null">
                PHONE = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="coidType != null">
                COID_TYPE = #{coidType,jdbcType=BOOLEAN},
            </if>
            <if test="coidLength != null">
                COID_LENGTH = #{coidLength,jdbcType=INTEGER},
            </if>
            <if test="coidDomain != null">
                COID_DOMAIN = #{coidDomain,jdbcType=VARCHAR},
            </if>
            <if test="coidUri != null">
                COID_URI = #{coidUri,jdbcType=VARCHAR},
            </if>
            <if test="nextContactContent != null">
                NEXT_CONTACT_CONTENT = #{nextContactContent,jdbcType=VARCHAR},
            </if>
            <if test="comments != null">
                COMMENTS = #{comments,jdbcType=LONGVARCHAR},
            </if>
            <if test="isDone != null">
                IS_DONE = #{isDone,jdbcType=BOOLEAN},
            </if>
            <if test="afterSalesTraderId != null">
                AFTER_SALES_TRADER_ID = #{afterSalesTraderId,jdbcType=INTEGER},
            </if>
            <if test="relateCommunicateRecordId != null">
                RELATE_COMMUNICATE_RECORD_ID = #{relateCommunicateRecordId,jdbcType=INTEGER},
            </if>
            <if test="contactContent != null">
                CONTACT_CONTENT = #{contactContent,jdbcType=VARCHAR},
            </if>
            <if test="contact != null">
                CONTACT = #{contact,jdbcType=VARCHAR},
            </if>
            <if test="contactMob != null">
                CONTACT_MOB = #{contactMob,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="syncStatus != null">
                SYNC_STATUS = #{syncStatus,jdbcType=BOOLEAN},
            </if>
            <if test="isLfasr != null">
                IS_LFASR = #{isLfasr,jdbcType=BOOLEAN},
            </if>
            <if test="isComment != null">
                IS_COMMENT = #{isComment,jdbcType=BOOLEAN},
            </if>
            <if test="ttNumber != null">
                TT_NUMBER = #{ttNumber,jdbcType=VARCHAR},
            </if>
            <if test="contentSuffix != null">
                CONTENT_SUFFIX = #{contentSuffix,jdbcType=VARCHAR},
            </if>
            <if test="noneNextDate != null">
                NONE_NEXT_DATE = #{noneNextDate,jdbcType=BOOLEAN},
            </if>
            <if test="telephone != null and telephone != ''">
                TELEPHONE = #{telephone,jdbcType=VARCHAR},
            </if>
            <if test="followUpType != null">
                FOLLOW_UP_TYPE = #{followUpType,jdbcType=INTEGER},
            </if>
            <if test="belongUserId != null">
                BELONG_USER_ID = #{belongUserId,jdbcType=INTEGER},
            </if>
            <if test="belongUserName != null and belongUserName != ''">
                BELONG_USER_NAME = #{belongUserName,jdbcType=VARCHAR},
            </if>
            <if test="traderName != null and traderName != ''">
                TRADER_NAME = #{traderName,jdbcType=VARCHAR},
            </if>
        </set>
        where COID = #{coid,jdbcType=VARCHAR}
    </update>

<!--auto generated by MybatisCodeHelper on 2022-07-26-->
    <select id="findByRelatedIdAndCommunicateTypeOrderByCommunicateRecordIdDesc" resultMap="BaseResultMap1">
        select
        <include refid="Base_Column_List"/>
        from T_COMMUNICATE_RECORD
        where RELATED_ID=#{relatedId,jdbcType=INTEGER} and COMMUNICATE_TYPE=#{communicateType,jdbcType=INTEGER} order by
        COMMUNICATE_RECORD_ID desc
        limit 1
    </select>

<!--auto generated by MybatisCodeHelper on 2022-07-29-->
    <select id="selectByRelatedIdAndCommunicateType" resultType="java.lang.Integer">
        select
        COMMUNICATE_RECORD_ID
        from T_COMMUNICATE_RECORD
        where RELATED_ID=#{relatedId,jdbcType=INTEGER} and COMMUNICATE_TYPE=#{communicateType,jdbcType=INTEGER} limit 1
    </select>


    <select id="getCommunicateTags" resultType="com.vedeng.erp.trader.dto.TagDto">
        select
            b.TAG_ID, b.TAG_TYPE, b.IS_RECOMMEND, b.TAG_NAME
        from
            T_TRADER_TAG a
                left join
            T_TAG b on a.TAG_ID=b.TAG_ID
        where
            a.TRADER_TYPE = 3
          and
            a.TRADER_ID =#{communicateRecordId}
    </select>

<!--auto generated by MybatisCodeHelper on 2024-05-11-->
    <select id="findByBegintimeBetween" resultMap="BaseResultMap1">
        select
        <include refid="Base_Column_List"/>
        from T_COMMUNICATE_RECORD
        where ADD_TIME <![CDATA[>]]> #{minBegintime,jdbcType=BIGINT} and ADD_TIME <![CDATA[<]]>
        #{maxBegintime,jdbcType=BIGINT}
        order by ADD_TIME desc
    </select>

<!--auto generated by MybatisCodeHelper on 2024-05-15-->
    <select id="findByCommunicateRecordId" resultMap="BaseResultMap1">
        select
        <include refid="Base_Column_List"/>
        from T_COMMUNICATE_RECORD
        where COMMUNICATE_RECORD_ID=#{communicateRecordId,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2024-05-17-->
    <select id="findByCommunicateRecordIdIn" resultMap="BaseResultMap1">
        select
        <include refid="Base_Column_List"/>
        from T_COMMUNICATE_RECORD
        where COMMUNICATE_RECORD_ID in
        <foreach item="item" index="index" collection="communicateRecordIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>

<!--auto generated by MybatisCodeHelper on 2024-05-20-->
    <select id="findByAddTimeBetweenAndCreator" resultMap="BaseResultMap1">
        select
        <include refid="Base_Column_List"/>
        from T_COMMUNICATE_RECORD
        where ADD_TIME <![CDATA[>]]> #{minAddTime,jdbcType=BIGINT} and ADD_TIME <![CDATA[<]]>
        #{maxAddTime,jdbcType=BIGINT} and CREATOR=#{creator,jdbcType=INTEGER}
        And COID is not null And COID <![CDATA[<>]]> ''
        order by ADD_TIME desc
    </select>
    
    <select id="getLastCommunicateRecord" resultMap="BaseResultMap1">
        select
        <include refid="Base_Column_List"/>
        from T_COMMUNICATE_RECORD
        where RELATED_ID=#{relatedId,jdbcType=INTEGER} and COMMUNICATE_TYPE=#{communicateType,jdbcType=INTEGER} 
        order by COMMUNICATE_RECORD_ID desc
        limit 1
    </select>
    
    <select id="getTelList" resultType="com.vedeng.erp.trader.dto.CommunicateTelRecord" parameterType="com.vedeng.erp.trader.dto.CommunicateTelRecordParams">
        SELECT 
		    a.COMMUNICATE_RECORD_ID communicateRecordId,
		    DATE_FORMAT(FROM_UNIXTIME(a.ADD_TIME / 1000),'%Y-%m-%d %H:%i:%s') addTime,
		    a.COID_TYPE coidType,
		    a.PHONE phone,
		    a.TRADER_ID traderId,
		    tt.TRADER_NAME traderName,
		    a.TRADER_TYPE traderType,
		    a.CREATOR creator,
		    a.COID_LENGTH coidLength,
		    a.COID_URI coidUri,
		    a.COID coid,
		    b.USERNAME AS creatorName,
		    a.AVATAR_URL avatarUrl,
		    a.CONTACT,
		    t.USERNAME saleName
		FROM
		    T_COMMUNICATE_RECORD a
		        LEFT JOIN
		    T_USER b ON a.CREATOR = b.USER_ID
		        LEFT JOIN
		    T_TRADER tt ON tt.TRADER_ID = a.TRADER_ID
		        LEFT JOIN
		    T_R_TRADER_J_USER trtju ON trtju.TRADER_ID = tt.TRADER_ID
		        LEFT JOIN
		    T_USER t ON t.USER_ID = trtju.USER_ID
		where	a.COID is not null and a.COID !=''
				AND a.RELATED_ID = 0
		        AND a.company_id=1
		        AND a.COID_LENGTH >0 
				<if test="keywords != null and keywords != ''">
					AND ((a.PHONE like CONCAT('%',#{keywords,jdbcType=VARCHAR},'%') and a.COID_TYPE in (1,2))or a.CONTACT like CONCAT('%',#{keywords,jdbcType=VARCHAR},'%') )
				</if>
				<if test="coidTypeList != null and coidTypeList.size() >0">
					AND a.COID_TYPE in 
					<foreach collection="coidTypeList" item="coidType" separator="," open="(" close=")">
						 #{coidType,jdbcType=INTEGER}
					</foreach>
				</if>
		        <if test="communicateRecordId != null">
					AND COMMUNICATE_RECORD_ID = #{communicateRecordId,jdbcType=INTEGER}
				</if>
				
				<if test="createTimeStart != null and createTimeStart != ''">
					AND DATE(FROM_UNIXTIME(a.ADD_TIME/1000)) <![CDATA[>=]]> #{createTimeStart,jdbcType=VARCHAR}
				</if>
				
				<if test="createTimeEnd != null and createTimeEnd != ''">
					AND DATE(FROM_UNIXTIME(a.ADD_TIME/1000)) <![CDATA[<=]]>  #{createTimeEnd,jdbcType=VARCHAR}
				</if>
		        <if test="userIdList != null and userIdList.size() >0">
					AND a.CREATOR in 
					<foreach collection="userIdList" item="id" separator="," open="(" close=")">
						#{id,jdbcType=INTEGER}
					</foreach>
				</if>
				order by a.COMMUNICATE_RECORD_ID DESC
    </select>
    
</mapper>

