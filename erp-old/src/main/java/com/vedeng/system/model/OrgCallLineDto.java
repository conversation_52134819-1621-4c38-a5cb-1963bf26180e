package com.vedeng.system.model;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 部门号码配置运输类
 *
 * <AUTHOR>
 */
@Data
public class OrgCallLineDto {

    private Integer orgId;

    private String telecomLine;

    private String mobileLine;

    private String unicomLine;

    private String customerLine;

    private List<Integer> userIds;

    public static String trimLine(String line) {
        return StringUtils.isBlank(line) ? null : line.trim();
    }

    public static boolean isUnValidData(OrgCallLineDto orgCallLine) {
        if (orgCallLine == null) {
            return true;
        }
        return trimLine(orgCallLine.getTelecomLine()) == null && trimLine(orgCallLine.getMobileLine()) == null &&
                trimLine(orgCallLine.getUnicomLine()) == null && trimLine(orgCallLine.getCustomerLine()) == null;
    }

    public static void trimAllLine(OrgCallLineDto orgCallLine) {
        if (orgCallLine == null) {
            return;
        }
        orgCallLine.setTelecomLine(trimLine(orgCallLine.getTelecomLine()));
        orgCallLine.setMobileLine(trimLine(orgCallLine.getMobileLine()));
        orgCallLine.setUnicomLine(trimLine(orgCallLine.getUnicomLine()));
        orgCallLine.setCustomerLine(trimLine(orgCallLine.getCustomerLine()));
    }
}
