<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="售后详情-退货" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%= basePath %>static/js/orderstream/aftersales/view_afterSales.js?rnd=${resourceVersionKey}'></script>
<script src="<%= basePath %>static/js/orderstream/jquery.js"></script>
<script src="${pageContext.request.contextPath}/static/js/orderstream/openfile.js?rnd=${resourceVersionKey}"></script>
<script src="<%= basePath %>static/js/orderstream/afterSales_index.js?rnd=${resourceVersionKey}"></script>
<link rel="stylesheet" href="<%= basePath %>static/css/orderstream/style.css">
<script type="text/javascript" src='<%= basePath %>static/js/order/saleorder/saleorder_invoice_common.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript">
    $(function(){
        var	url = page_url + '/order/aftersalesUpgrade/viewAfterSalesDetail.do?afterSalesId='+$("#afterSalesId").val();
        if($(window.frameElement).attr('src').indexOf("viewAfterSalesDetail")<0){
            $(window.frameElement).attr('data-url',url);
        }
    });
</script>
<div class="main-container">
    <div class="t-line-wrap J-line-wrap"
         data-json='[
     {"label":"确认","status":${topStatusList.get(0)}},
     {"label":"入库","status":${topStatusList.get(1)}},
     {"label":"退票","status":${topStatusList.get(2)}},
     {"label":"退款","status":${topStatusList.get(3)}},
     {"label":"开票","status":${topStatusList.get(4)}},
     <c:choose>
        <c:when test="${afterSalesVo.atferSalesStatus eq 3}">
            {"label":"关闭","status":${topStatusList.get(5)}}
        </c:when>
        <c:otherwise>
            {"label":"完结","status":${topStatusList.get(5)}}
        </c:otherwise>
     </c:choose>]'>
    </div>
    <div class="table-buttons">
       <form action="" method="post" id="myform">
            <input type="hidden" name="afterSalesId" id="afterSalesId" value="${afterSalesVo.afterSalesId}"/>
            <input type="hidden" name="subjectType" value="${afterSalesVo.subjectType}"/>
            <input type="hidden" name="type" value="${afterSalesVo.type}"/>
            <input type="hidden" name="formToken" value="${formToken}"/>
           <input type="hidden" name="taskId" value="${taskInfo.id == null ?0: taskInfo.id}"/>
                   <%--人员权限保持历史，状态判定-根据订单流升级需求--%>
               <c:choose>
                   <c:when test="${afterSalesVo.atferSalesStatus eq 0 && afterSalesVo.status ne 1
                   && (user.positType eq 310 || user.positType eq 312 || user.positType eq 589)}">
<%--                           待确认状态可编辑订单，申请审核--%>
                       <button type="button" class="bt-bg-style bg-light-blue bt-small" onclick="editAfterSales(1);">
                               编辑订单
                       </button>
                       <button type="button" class="bt-bg-style bg-light-blue bt-small" onclick="applyAudit(${empty afterSalesVo.afterSalesGoodsList ? 1 : 0});">提交审核</button>
                   </c:when>
                   <c:otherwise>
                       <button type="button" class="bt-bg-style bg-light-grey bt-small">
                           编辑订单
                       </button>
                       <button type="button" class="bt-bg-style bg-light-grey bt-small">
                           提交审核
                       </button>
                   </c:otherwise>
               </c:choose>
               <c:choose>
                   <c:when test="${
                   ((null!=taskInfo and null!=taskInfo.getProcessInstanceId() and null!=taskInfo.assignee) or !empty candidateUserMap[taskInfo.id])
                   && (taskInfo.assignee == user.username or candidateUserMap['belong'])}">
                       <button type="button" class="bt-bg-style bg-light-blue bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"240px","title":"操作确认","link":"./complement.do?taskId=${taskInfo.id}&pass=true&type=2&saleorderId=${afterSalesVo.orderId}&afterSalesId=${afterSalesVo.afterSalesId}&afterSaleorderId=${afterSalesVo.afterSalesId}"}'>审核通过</button>
                       <button type="button" class="bt-bg-style bg-light-blue bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?taskId=${taskInfo.id}&pass=false&type=2&afterSalesId=${afterSalesVo.afterSalesId}"}'>审核不通过</button>
                   </c:when>
                   <c:otherwise>
                       <button type="button" class="bt-bg-style bg-light-grey bt-small">
                           审核通过
                       </button>
                       <button type="button" class="bt-bg-style bg-light-grey bt-small">
                           审核不通过
                       </button>
                   </c:otherwise>
               </c:choose>
                <c:choose>
                    <c:when test="${afterSalesVo.atferSalesStatus eq 1
                    &&(afterSalesVo.invoiceRefundStatus == 3 or afterSalesVo.invoiceRefundStatus == 0)
                    && (afterSalesVo.inStockStatus eq 4 or afterSalesVo.inStockStatus eq 7)
                    && ((null==taskInfoOver and null==taskInfoOver.getProcessInstanceId())
                    or (null!=taskInfoOver and taskInfoOver.assignee==null and empty candidateUserMapOver[taskInfoOver.id]))
                    && user.positType eq 312
                    && afterSalesVo.isCalRefund ne 1}">
<%--                        入库状态为“全部入库/无入库”，退票状态为“全部退票/无退票--%>
                        <button type="button" class="bt-bg-style bg-light-blue bt-small" onclick="executeRefundOperation();">执行退款运算</button>
                    </c:when>
                    <c:otherwise>
                        <button type="button" class="bt-bg-style bg-light-grey bt-small">
                            执行退款运算
                        </button>
                    </c:otherwise>
                </c:choose>
                <c:choose>
                    <c:when test="${afterSalesVo.status ne 1 && afterSalesVo.closeStatus eq 1 && empty payPlayList
                    && (user.positType eq 310 || user.positType eq 312 || user.positType eq 589 || (afterSalesVo.atferSalesStatus eq 0 && user.userId eq afterSalesVo.creator))
                    && ((null==taskInfoOver and null==taskInfoOver.getProcessInstanceId())
                    or (null!=taskInfoOver and taskInfoOver.assignee==null and empty candidateUserMapOver[taskInfoOver.id]))
                    && relatedBuyVerify eq 1}">
                        <button type="button" class="bt-bg-style bg-light-blue bt-small" onclick="colse();">关闭订单</button>
                    </c:when>
                    <c:otherwise>
                        <button type="button" class="bt-bg-style bg-light-grey bt-small">
                            关闭订单
                        </button>
                    </c:otherwise>
                </c:choose>
                <c:choose>
                    <c:when test="${afterSalesVo.atferSalesStatus eq 1
                    &&(afterSalesVo.invoiceRefundStatus == 3 or afterSalesVo.invoiceRefundStatus == 0)
                    && (afterSalesVo.inStockStatus == 4 or afterSalesVo.inStockStatus == 7)
                    && (afterSalesVo.amountRefundStatus == 0 or afterSalesVo.amountRefundStatus == 3)
                    && (afterSalesVo.invoiceMakeoutStatus == 0 or afterSalesVo.invoiceMakeoutStatus == 2)
                    && ((null==taskInfoOver and null==taskInfoOver.getProcessInstanceId())
                    or (null!=taskInfoOver and taskInfoOver.assignee==null and empty candidateUserMapOver[taskInfoOver.id]))
                    && user.positType eq 312}">
<%--                        入库状态为“无入库/全部入库”、退票状态为“无退票/全部退票”退款状态为“无退款/全部退款”开票状态为“全部开票/无开票”--%>
                        <button type="button" class="bt-bg-style bg-light-blue bt-small pop-new-data"
                                layerParams='{"width":"780px","height":"310px","title":"选择售后原因","link":"/order/aftersalesUpgrade/saleorderComplete.do?afterSalesId=${afterSalesVo.afterSalesId}&afterSalesType=756&type=${afterSalesVo.type}&orderId=${afterSalesVo.orderId }&subjectType=${afterSalesVo.subjectType }&formToken=${formToken }&traderId=${afterSalesVo.traderId }"}'>申请完结</button>
                    </c:when>
                    <c:otherwise>
                        <button type="button" class="bt-bg-style bg-light-grey bt-small">
                            申请完结
                        </button>
                    </c:otherwise>
                </c:choose>
                <c:choose>
                    <c:when test="${((null!=taskInfoOver and null!=taskInfoOver.getProcessInstanceId() and null!=taskInfoOver.assignee) or !empty candidateUserMapOver[taskInfoOver.id])
                    && (taskInfoOver.assignee == user.username or candidateUserMapOver['belong'])}">
                        <button type="button" class="bt-bg-style bg-light-blue bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?taskId=${taskInfoOver.id}&pass=true&type=2&sku=${sku}&orderId=${afterSalesVo.orderId}&afterSalesId=${afterSalesVo.afterSalesId}"}'>完结/关闭审核通过</button>
                        <button type="button" class="bt-bg-style bg-light-blue bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?taskId=${taskInfoOver.id}&pass=false&type=2&afterSalesId=${afterSalesVo.afterSalesId}"}'>完结/关闭审核不通过</button>
                    </c:when>
                    <c:otherwise>
                        <button type="button" class="bt-bg-style bg-light-grey bt-small">
                            完结/关闭审核通过
                        </button>
                        <button type="button" class="bt-bg-style bg-light-grey bt-small">
                            完结/关闭审核通过
                        </button>
                    </c:otherwise>
                </c:choose>
                <c:choose>
                    <c:when test="${afterSalesVo.isCanApplyInvoice eq 1
                    && user.positType eq 312
                    && (afterSalesVo.atferSalesStatus eq 1 or afterSalesVo.atferSalesStatus eq 2)
                    && ((null==taskInfoOver and null==taskInfoOver.getProcessInstanceId())or (null!=taskInfoOver and taskInfoOver.assignee==null and empty candidateUserMapOver[taskInfoOver.id]))}">
                        <button type="button" class="bt-bg-style bg-light-blue bt-small mr10" onclick="isTraderAllowInvoiceAfter(${afterSalesVo.afterSalesId},${afterSalesVo.invoiceType})">申请开票</button>
                        <span id="invoiceApplyAfter" class="pop-new-data-noclose" layerParams='{"width":"70%","height":"800px","title":"","link":"/invoice/invoiceApply/afterSale.do?afterSalesId=${afterSalesVo.afterSalesId}"}'></span>
                    </c:when>
                    <c:otherwise>
                        <button type="button" class="bt-bg-style bg-light-grey bt-small">
                            申请开票
                        </button>
                    </c:otherwise>
                </c:choose>
                <c:choose>
                    <c:when test="${(afterSalesVo.inStockStatus == 4 or afterSalesVo.inStockStatus == 7)
                    && (afterSalesVo.invoiceRefundStatus == 1 or afterSalesVo.invoiceRefundStatus == 2)
                    && user.positType eq 312}">
                        <button type="button" class="bt-bg-style bg-light-blue bt-small" onclick="confirmBackInvoiceStatus();">确认全部退票</button>
                    </c:when>
                    <c:otherwise>
                        <button type="button" class="bt-bg-style bg-light-grey bt-small">
                            确认全部退票
                        </button>
                    </c:otherwise>
                </c:choose>
        </form>
    </div>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                基本信息
            </div>
        </div>
        <table class="table">
            <tbody>
            <tr>
                <td class="wid20">订单号</td>
                <td>${afterSalesVo.afterSalesNo}</td>
                <td class="wid20">售后类型</td>
                <td>
                    销售退货
                </td>
            </tr>
            <tr>
                <td class="wid20">订单状态</td>
                <td>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 0}">待确认</c:if>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 1}">进行中</c:if>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 2}">已完结</c:if>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 3}">已关闭</c:if>
                </td>
                <td>审核状态</td>
                <td>
                    <c:if test="${afterSalesVo.status eq 0}">待审核</c:if>
                    <c:if test="${afterSalesVo.status eq 1}">审核中</c:if>
                    <c:if test="${afterSalesVo.status eq 2}">审核通过</c:if>
                    <c:if test="${afterSalesVo.status eq 3}">审核不通过</c:if>
                </td>
            </tr>
            <tr>
                <td>生效状态</td>
                <td>
                    <c:if test="${afterSalesVo.validStatus eq 0}">未生效</c:if>
                    <c:if test="${afterSalesVo.validStatus eq 1}">已生效</c:if>
                </td>
                <td>售后处理人</td>
                <td>${afterSalesVo.serviceUserName}</td>
            </tr>
            <tr>
                <td>申请人</td>
                <td>${afterSalesVo.creatorName}</td>
                <td>申请时间</td>
                <td><date:date value ="${afterSalesVo.addTime}"/></td>
            </tr>
            <tr>
                <td>生效时间</td>
                <td>
                    <c:if test="${afterSalesVo.validStatus eq 1}">
                        <date:date value ="${afterSalesVo.validTime}"/>
                    </c:if>
                </td>
                <td>完结时间</td>
                <td>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 2 || afterSalesVo.atferSalesStatus eq 3}">
                        <date:date value ="${afterSalesVo.modTime}"/>
                    </c:if>
                </td>
            </tr>
            <tr>
                <td>完结/关闭原因</td>
                <td>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 2 || afterSalesVo.atferSalesStatus eq 3}">
                        ${afterSalesVo.afterSalesStatusResonName}
                    </c:if>
                </td>
                <td>完结/关闭人</td>
                <td>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 2 || afterSalesVo.atferSalesStatus eq 3}">
                        ${afterSalesVo.afterSalesStatusUserName}
                    </c:if>
                </td>
            </tr>
            <tr>
                <td>完结/关闭备注</td>
                <td rowspan="3">${afterSalesVo.afterSalesStatusComments}</td>
            </tr>
            </tbody>
        </table>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                售后信息
            </div>
            <c:if test="${(afterSalesVo.atferSalesStatus eq 0 || afterSalesVo.atferSalesStatus eq 1)
            && (user.positType eq 310 || user.positType eq 312 || user.positType eq 589)
            && afterSalesVo.status ne 1}">
                <div class="title-click nobor pop-new-data"
                     layerParams='{"width":"700px","height":"480px","title":"编辑售后信息","link":"/order/afterSalesCommon/editAfterSalesInfo.do?afterSalesId=${afterSalesVo.afterSalesId}&type=${afterSalesVo.type}"}'>编辑售后信息</div>
            </c:if>
        </div>

        <table class="table">
            <tbody>
            <tr>
                <td class="wid20">售后原因</td>
                <td>${afterSalesVo.reasonName}</td>
                <td class="wid20">第一责任部门</td>
                <td>${afterSalesVo.firstResponsibleDepartmentStr}</td>
            </tr>
            <tr>
                <td class="wid20">售后联系人</td>
                <td>${afterSalesVo.afterConnectUserName}</td>
                <td>售后联系人电话</td>
                <td>
                    ${afterSalesVo.afterConnectPhone}
                </td>
            </tr>
            <tr>
                <td class="wid20">售后报单人</td>
                <td>${afterSalesVo.traderContactName}</td>
                <td>售后报单人电话</td>
                <td>
                    ${afterSalesVo.traderContactTelephone}
                    <c:if test="${not empty afterSalesVo.traderContactTelephone}">
                        <i class="icontel cursor-pointer" title="点击拨号" onclick="callout('${afterSalesVo.traderContactTelephone}',${afterSalesVo.traderId},1,4,${afterSalesVo.afterSalesId},${afterSalesVo.traderContactId});"></i>
                    </c:if>
                </td>
            </tr>
            <tr>
                <td>售后报单人手机号</td>
                <td>
                    ${afterSalesVo.traderContactMobile}
                    <c:if test="${not empty afterSalesVo.traderContactMobile}">
                        <i class="icontel cursor-pointer" title="点击拨号" onclick="callout('${afterSalesVo.traderContactMobile}',${afterSalesVo.traderId},1,4,${afterSalesVo.afterSalesId},${afterSalesVo.traderContactId});"></i>
                    </c:if>
                </td>
            </tr>
            <tr>
                <td>详情说明</td>
                <td colspan="3" class="text-left">${afterSalesVo.comments}</td>

            </tr>
            <tr>
                <td>附件</td>
                <td colspan="3" class="text-left">
                    <c:if test="${not empty afterSalesVo.attachmentList }">
                        <%@ include file="view_afterSales_files.jsp"%>

                    </c:if>
                </td>
            </tr>
            </tbody>
        </table>
    </div>

    <form action="" id="editAfterSaleOrderForm">
        <input type="hidden" name="afterSalesId" value="${afterSalesVo.afterSalesId}"/>
        <input type="hidden" name="afterSalesDetailId" value="${afterSalesVo.afterSalesDetailId}">
        <input type="hidden" name="refund" value="${afterSalesVo.refund}">
        <input type="hidden" name="traderSubject" value="${afterSalesVo.traderSubject}">
        <input type="hidden" name="payee" value="${afterSalesVo.payee}">
        <input type="hidden" name="bank" value="${afterSalesVo.bank}">
        <input type="hidden" name="bankCode" value="${afterSalesVo.bankCode}">
        <input type="hidden" name="bankAccount" value="${afterSalesVo.bankAccount}">
        <input type="hidden" name="traderMode" value="${afterSalesVo.traderMode}">
        <input type="hidden" name="refundAmountStatus" value="${afterSalesVo.refundAmountStatus}">
        <input class="child" type="hidden" id="layerIndex">
        <input type="hidden" id="typeFlag" value="0">
    </form>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                款项退还信息
            </div>
            <c:if test="${(afterSalesVo.atferSalesStatus eq 0 || afterSalesVo.atferSalesStatus eq 1)
            && (user.positType eq 310 || user.positType eq 312 || user.positType eq 589)
            && updateRefundCheck}">
<%--                <div class="title-click nobor"  onclick="editAfterSalesBackFundInfo(${afterSalesVo.afterSalesId},${afterSalesVo.type},${afterSalesVo.refund},${afterSalesVo.finalRefundableAmount})">编辑款项退还信息</div>--%>
                <span class="title-click nobor pop-new-data-noclose" layerParams='{"width":"80%","height":"600px","title":"退还款项分配","link":"/afterSalesOrder/refundInfo/edit.do?afterSalesId=${afterSalesVo.afterSalesId}&type=${afterSalesVo.type}&refund=${afterSalesVo.refund}&finalRefundableAmount=${afterSalesVo.finalRefundableAmount}"}'>退还款项分配</span>
            </c:if>
            <c:if test="${detailRefundCheck}">
                <span class="title-click nobor pop-new-data" layerParams='{"width":"80%","height":"500px","title":"查看退还款项分配","link":"/afterSalesOrder/refundInfo/detail.do?afterSalesId=${afterSalesVo.afterSalesId}&type=${afterSalesVo.type}&refund=${afterSalesVo.refund}&finalRefundableAmount=${afterSalesVo.finalRefundableAmount}"}'>查看退还款项分配</span>
            </c:if>
        </div>
        <table class="table">
                <tbody>
                <tr>
                    <td class="wid20">款项退还</td>
                    <td>
                        <c:if test="${afterSalesVo.refund eq 0}">无</c:if>
                        <c:if test="${afterSalesVo.refund eq 1}">退到余额</c:if>
                        <c:if test="${afterSalesVo.refund eq 2}">退回账户</c:if>
                    </td>
                    <td class="wid20">交易方式</td>
                    <td>
                        <c:if test="${afterSalesVo.traderMode eq 520}">支付宝</c:if>
                        <c:if test="${afterSalesVo.traderMode eq 521}">银行</c:if>
                    </td>
                </tr>
                <tr>
                    <td>主体交易方式</td>
                    <td>
                        <c:if test="${afterSalesVo.traderSubject eq 1}">对公</c:if>
                        <c:if test="${afterSalesVo.traderSubject eq 2}">对私</c:if>
                    </td>
                    <td>收款名称</td>
                    <td>${afterSalesVo.payee}</td>
                </tr>
                <tr>
                    <td>开户银行</td>
                    <td>${afterSalesVo.bank}</td>
                    <td>银行/支付宝账号</td>
                    <td>${afterSalesVo.bankAccount}</td>
                </tr>
                <tr>
                    <td>开户行支付联行号</td>
                    <td>${afterSalesVo.bankCode}</td>
                    <td>特别提醒</td>
                    <td><font style="color: red">${afterSalesVo.refundComment}</font></td>
                </tr>
                </tbody>
            </table>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">退货信息</div>
        </div>
        <c:set var="afterSalesGoodsVoListPage" value="${afterSalesVo.afterSalesGoodsList}"></c:set>
        <%@ include file="view_afterSales_th_goods.jsp"%>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                所属订单信息
            </div>
        </div>
        <table class="table">
            <tbody>
            <tr>
                <td class="wid20">所属销售单号</td>
                <td >
                    <div class="customername pos_rel">
                        <span class="brand-color1 addtitle" style="float:none;" tabTitle='{"num":"viewsaleorder${afterSalesVo.orderNo}","title":"订单信息",
                        "link":"./orderstream/saleorder/detail.do?saleOrderId=${afterSalesVo.orderId}"}'>${afterSalesVo.orderNo}</span>
                        <i class="iconbluemouth"></i>
                        <div class="pos_abs customernameshow" style="display: none;">
                            付款状态：<c:if test="${afterSalesVo.paymentStatus eq 0}">未付款</c:if>
                            <c:if test="${afterSalesVo.paymentStatus eq 1}">部分付款</c:if>
                            <c:if test="${afterSalesVo.paymentStatus eq 2}">全部付款</c:if><br>
                            发货状态：<c:if test="${afterSalesVo.deliveryStatus eq 0}">未发货</c:if>
                            <c:if test="${afterSalesVo.deliveryStatus eq 1}">部分发货</c:if>
                            <c:if test="${afterSalesVo.deliveryStatus eq 2}">全部发货</c:if><br>
                            开票状态：<c:if test="${afterSalesVo.invoiceStatus eq 0}">未开票</c:if>
                            <c:if test="${afterSalesVo.invoiceStatus eq 1}">部分开票</c:if>
                            <c:if test="${afterSalesVo.invoiceStatus eq 2}">全部开票</c:if><br>
                            收货状态：<c:if test="${afterSalesVo.arrivalStatus eq 0}">未收货</c:if>
                            <c:if test="${afterSalesVo.arrivalStatus eq 1}">部分收货</c:if>
                            <c:if test="${afterSalesVo.arrivalStatus eq 2}">全部收货</c:if>
                        </div>
                    </div>
                </td>
                <td class="wid20">订单金额</td>
                <td><fmt:formatNumber type="number" value="${afterSalesVo.totalAmount}" pattern="0.00" maxFractionDigits="2" /></td>
            </tr>
            <tr>
                <td>归属销售</td>
                <td>${afterSalesVo.userName}</td>
                <td>归属部门</td>
                <td>${afterSalesVo.orgName}</td>
            </tr>
            <tr>
                <td>订单状态</td>
                <td>
                    <c:if test="${afterSalesVo.saleorderStatus eq 0}">待确认</c:if>
                    <c:if test="${afterSalesVo.saleorderStatus eq 1}">进行中</c:if>
                    <c:if test="${afterSalesVo.saleorderStatus eq 2}">已完结</c:if>
                    <c:if test="${afterSalesVo.saleorderStatus eq 3}">已关闭</c:if>
                </td>
                <td>生效时间</td>
                <td>
                    <date:date value ="${afterSalesVo.saleorderValidTime}"/>
                </td>
            </tr>
            <tr>
                <td>客户名称</td>
                <td>
                    <div class="customername pos_rel">
                                  <span class="brand-color1 addtitle"  style="float:none;" tabTitle='{"num":"viewcustomer${afterSalesVo.traderId}","title":"客户信息",
										"link":"<%= basePath %>/trader/customer/baseinfo.do?traderId=${afterSalesVo.traderId}"}'>${afterSalesVo.traderName}</span><i class="iconbluemouth"></i>
                        <div class="pos_abs customernameshow" style="display: none;">
                            客户性质：<c:if test="${afterSalesVo.customerNature eq 465}">分销</c:if>
                            <c:if test="${afterSalesVo.customerNature eq 466}">终端</c:if><br>
                            交易次数：${afterSalesVo.orderCount}<br>
                            交易金额：<fmt:formatNumber type="number" value="${afterSalesVo.orderTotalAmount}" pattern="0.00" maxFractionDigits="2" /><br>
                            上次交易日期：<date:date value ="${afterSalesVo.lastOrderTime}"/>
                        </div>
                    </div>
                </td>
                <td>客户等级</td>
                <td>
                    ${afterSalesVo.customerLevelStr}
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                跟进记录
            </div>
            <c:if test="${afterSalesVo.atferSalesStatus eq 1
            && user.positType eq 312}">
                <div class="title-click nobor pop-new-data"
                     layerParams='{"width":"600px","height":"400px","title":"新增跟进记录","link":"/order/afterSalesCommon/addFollowUpRecord.do?afterSalesId=${afterSalesVo.afterSalesId}"}'>新增跟进记录</div>
            </c:if>
        </div>
        <%@ include file="add_followUp_common.jsp"%>
    </div>
    <div class="parts content1">
        <div class="title-container">
            <div class="table-title nobor">
                合同回传
            </div>
            <c:choose>
                <c:when test="${(afterSalesVo.atferSalesStatus eq 1 || afterSalesVo.atferSalesStatus eq 2) && (user.positType eq 310 || user.positType eq 312 || user.positType eq 589)}">
                    <div class="title-click nobor pop-new-data"
                         layerParams='{"width":"600px","height":"300px","title":"合同回传","link":"/order/afterSalesCommon/contractReturnInit.do?afterSalesId=${afterSalesVo.afterSalesId}"}'>上传合同</div>
                </c:when>
                <c:otherwise>
                    <button type="button" class="bt-bg-style bg-light-grey bt-small" style="float: right;">上传合同</button>
                </c:otherwise>
            </c:choose>

        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th>合同</th>
                <th class="table-small">操作人</th>
                <th class="table-small">时间</th>
                <th class="table-small">操作</th>
            </tr>
            </thead>
            <tbody>
            <c:forEach items="${afterSalesVo.afterContractAttachmentList}" var="list" varStatus="status">
                <tr>
                    <td class="font-blue"><a href="http://${list.domain}${list.uri}" target="_blank">${list.name}</a></td>
                    <td>${list.username}</td>
                    <td><date:date value ="${list.addTime}"/></td>
                    <td>
                        <div class="caozuo">
                            <span class="caozuo-red" onclick="contractReturnDel(${list.attachmentId})">删除</span>
                        </div>
                    </td>
                </tr>
            </c:forEach>
            <c:if test="${empty afterSalesVo.afterContractAttachmentList}">
                <tr>
                    <td colspan="4">暂无记录！</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">退货入库记录</div>
        </div>
        <table class="table  table-style6">
            <c:if test="${not empty afterSalesVo.normalGoodsList}">
                <c:forEach items="${afterSalesVo.normalGoodsList}" var="arg" varStatus="num_index">
            <thead>
            <tr>
                <td colspan="12">
                    <div style="float: left; margin-left: 100px;">该商品实际应退数量：${arg.rknum}</div>
                    <div style="float: left; margin-left: 100px;">该商品已退货入库数量：${arg.arrivalNum}</div>
                    <div style="float: left; margin-left: 100px;">该商品退货入库状态：
                        <c:if test="${arg.inStockStatus eq 4}">无入库</c:if>
                        <c:if test="${arg.inStockStatus eq 5}">未入库</c:if>
                        <c:if test="${arg.inStockStatus eq 6}">部分入库</c:if>
                        <c:if test="${arg.inStockStatus eq 7}">全部入库</c:if>
                    </div>
                </td>
            </tr>
            <tr>
                <th class="wid5">序号</th>
                <th>订货号</th>
                <th>产品名称</th>
                <th>品牌</th>
                <th>制造商型号</th>
                <th>单位</th>
                <th>退货入库数量</th>
                <th>生产日期</th>
                <th>贝登批次码</th>
                <th>有效期至</th>
                <th class="wid15">入库时间</th>
                <th>厂家批次号</th>
            </tr>
            </thead>
            <tbody>
                <c:forEach items="${arg.aftersalesGoodsNormalDeliveryInList}" var="normalArg" varStatus="normal_num">
                    <tr>
                        <td>${normal_num.count }</td>
                        <td>${normalArg.sku}</td>
                        <td class="text-left">
                            <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewgoods${normalArg.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${normalArg.goodsId}","title":"产品信息"}'>${newSkuInfosMap[normalArg.sku].SHOW_NAME}</a>
                        </td>
                        <td>${newSkuInfosMap[normalArg.sku].BRAND_NAME}</td>
                        <td>${newSkuInfosMap[normalArg.sku].MODEL}</td>
                        <td>${newSkuInfosMap[normalArg.sku].UNIT_NAME}</td>
                        <td>${normalArg.arrivalNum}</td>
                            <%--生产日期--%>
                        <td><date:date value ="${normalArg.productDate}" format="yyyy-MM-dd"/></td>
                        <td>${normalArg.vedengBatchNumer}</td>
                        <td><date:date value ="${normalArg.expirationDate}" format="yyyy-MM-dd"/></td>
                        <td><date:date value ="${normalArg.inStockTime}" format="yyyy-MM-dd"/></td>
                        <td>${normalArg.batchNumber}</td>
                    </tr>
                </c:forEach>
            </c:forEach>
            </c:if>
            <c:if test="${empty afterSalesVo.normalGoodsList}">
                <tr>
                    <td colspan="12">暂无记录</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">直发退货记录</div>
        </div>
        <table class="table  table-style6">
            <c:if test="${not empty afterSalesVo.directGoodsList}">
            <c:forEach items="${afterSalesVo.directGoodsList}" var="arg" varStatus="num_index">
            <thead>
            <tr>
                <td colspan="10">
                    <div style="float: left; margin-left: 100px;">该商品实际应退数量：${arg.rknum}</div>
                    <div style="float: left; margin-left: 100px;">该商品已退货入库数量：${arg.arrivalNum}</div>
                    <div style="float: left; margin-left: 100px;">该商品退货入库状态：
                        <c:if test="${arg.inStockStatus eq 4}">无入库</c:if>
                        <c:if test="${arg.inStockStatus eq 5}">未入库</c:if>
                        <c:if test="${arg.inStockStatus eq 6}">部分入库</c:if>
                        <c:if test="${arg.inStockStatus eq 7}">全部入库</c:if>
                    </div>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 1
                    && (arg.inStockStatus == 5 || arg.inStockStatus == 6)
                    && user.positType eq 312}">
                        <div class="title-click nobor"  onclick="editAfterSaleDirectInfo(${num_index.count},2)">更新已直发退货商品的记录</div>
                    </c:if>
                    <form action="" id="editDirectIn${num_index.count}">
                        <input type="hidden" name="afterSalesId" value="${afterSalesVo.afterSalesId}"/>
                        <input type="hidden" name="afterSalesGoodsId" value="${arg.afterSalesGoodsId}">
                        <input type="hidden" name="type" value="2"><%--1出库，2入库--%>
                        <input type="hidden" name="sku" value="${arg.sku}">
                        <input type="hidden" name="goodsName" value="${newSkuInfosMap[arg.sku].SHOW_NAME}">
                        <input type="hidden" name="brandName" value="${newSkuInfosMap[arg.sku].BRAND_NAME}">
                        <input type="hidden" name="unitName" value="${newSkuInfosMap[arg.sku].UNIT_NAME}">
                        <input type="hidden" name="model" value="${newSkuInfosMap[arg.sku].MODEL}">
                        <input type="hidden" name="rknum" value="${arg.rknum}">
                        <input type="hidden" name="deliveryNum" value="${arg.arrivalNum}">
                        <input type="hidden" name="formToken" value="${formToken}"/>
                    </form>
                </td>
            </tr>
            <tr>
                <th class="wid5">序号</th>
                <th>订货号</th>
                <th>产品名称</th>
                <th>品牌</th>
                <th>制造商型号</th>
                <th>单位</th>
                <th>客户退货数量</th>
                <th>生产日期</th>
                <th>有效期至</th>
                <th>厂家批次号/SN码</th>
            </tr>
            </thead>
            <tbody>
                <c:forEach items="${arg.aftersalesGoodsDirectDeliveryInList}" var="directArg" varStatus="direct_num">
                    <tr>
                        <td>${direct_num.count }</td>
                        <td>${directArg.sku}</td>
                        <td class="text-left">
                            <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewgoods${directArg.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${directArg.goodsId}","title":"产品信息"}'>${newSkuInfosMap[directArg.sku].SHOW_NAME}</a>
                        </td>
                        <td>${newSkuInfosMap[directArg.sku].BRAND_NAME}</td>
                        <td>${newSkuInfosMap[directArg.sku].MODEL}</td>
                        <td>${newSkuInfosMap[directArg.sku].UNIT_NAME}</td>
                        <td>${directArg.arrivalNum}</td>
                            <%--生产日期--%>
                        <td><date:date value ="${directArg.productDate}" format="yyyy-MM-dd"/></td>
                        <td><date:date value ="${directArg.expirationDate}" format="yyyy-MM-dd"/></td>
                        <td>${directArg.batchNumber}</td>
                    </tr>
                </c:forEach>
            </c:forEach>
            </c:if>
            <c:if test="${empty afterSalesVo.directGoodsList}">
                <tr>
                    <input type="hidden" id="haveAfterSalesDirectInfo" value="1"/>
                    <td colspan="10">暂无记录</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                退票信息与记录
            </div>
        </div>
        <table class="table">
            <c:if test="${not empty afterSalesVo.afterSalesInvoiceVoList}">
            <c:forEach items="${afterSalesVo.afterSalesInvoiceVoList}" var="asi" >
            <thead>
            <tr>
                <td colspan="9">
<%--                    销售单开票状态：--%>
<%--                    <div style="float: left; margin-left: 100px;">--%>
<%--                        <c:if test="${afterSalesVo.createInvoiceStatus == 12}">无入库</c:if>--%>
<%--                        <c:if test="${afterSalesVo.createInvoiceStatus == 13}">未入库</c:if>--%>
<%--                        <c:if test="${afterSalesVo.createInvoiceStatus == 14}">部分入库</c:if>--%>
<%--                        <c:if test="${afterSalesVo.createInvoiceStatus == 15}">全部入库</c:if>--%>
<%--                    </div>--%>
                    <div style="float: left; margin-left: 100px;">
                        寄送状态：
                        <c:if test="${asi.expressId != null && asi.expressId != 0}">已寄送</c:if>
                        <c:if test="${asi.expressId == null || asi.expressId == 0}">未寄送</c:if>
                    </div>
                    <div style="float: left; margin-left: 100px;">
                        是否需寄回：
                        <c:if test="${asi.isSendInvoice eq 1}">是</c:if>
                        <c:if test="${asi.isSendInvoice eq 0}">否</c:if>
                    </div>
                    <div style="float: left; margin-left: 100px;">
                        售后单退票状态：
                        <c:if test="${afterSalesVo.invoiceRefundStatus == 0}">无退票</c:if>
                        <c:if test="${afterSalesVo.invoiceRefundStatus == 1}">未退票</c:if>
                        <c:if test="${afterSalesVo.invoiceRefundStatus == 2}">部分退票</c:if>
                        <c:if test="${afterSalesVo.invoiceRefundStatus == 3}">全部退票</c:if>
                    </div>
                </td>
            </tr>
            <tr>
                <th>发票号</th>
                <th>发票代码</th>
                <th>票种</th>
                <th>红蓝字</th>
                <th>发票金额</th>
                <th>开票日期</th>
                <th>操作时间</th>
                <th>操作人</th>
                <th>该发票退票状态</th>

            </tr>
            </thead>
            <tbody>
                <tr>
                    <td>${asi.invoiceNo}</td>
                    <td>${asi.invoiceCode}</td>
                    <td>
                        <c:if test="${asi.invoiceType eq 429}">17%增值税专用发票</c:if>
                        <c:if test="${asi.invoiceType eq 430}">17%增值税普通发票</c:if>
                        <c:if test="${asi.invoiceType eq 682}">16%增值税专用发票</c:if>
                        <c:if test="${asi.invoiceType eq 681}">16%增值税普通发票</c:if>
                        <c:if test="${asi.invoiceType eq 972}">13%增值税专用发票</c:if>
                        <c:if test="${asi.invoiceType eq 971}">13%增值税普通发票</c:if>
                        <c:if test="${asi.invoiceType eq 683}">6%增值税普通发票</c:if>
                        <c:if test="${asi.invoiceType eq 684}">6%增值税专用发票</c:if>
                        <c:if test="${asi.invoiceType eq 685}">3%增值税普通发票</c:if>
                        <c:if test="${asi.invoiceType eq 686}">3%增值税专用发票</c:if>
                        <c:if test="${asi.invoiceType eq 687}">0%增值税普通发票</c:if>
                        <c:if test="${asi.invoiceType eq 688}">0%增值税专用发票</c:if>
                    </td>
                    <td>
                        <c:if test="${asi.colorType == 1 and asi.isEnable == 1}">红字有效</c:if>
                        <c:if test="${asi.colorType == 1 and asi.isEnable == 0}">红字作废</c:if>
                        <c:if test="${asi.colorType == 2 and asi.isEnable == 1}">蓝字有效</c:if>
                        <c:if test="${asi.colorType == 2 and asi.isEnable == 0}">蓝字作废</c:if>
                    </td>
                    <td><fmt:formatNumber type="number" value="${asi.amount}" pattern="0.00" maxFractionDigits="2" /></td>
                    <td>
                        <date:date value ="${asi.addTime}" format="yyyy-MM-dd"/>
                    </td>
                    <td>
                        <date:date value ="${asi.modTime}" format="yyyy-MM-dd"/>
                    </td>
                    <td>
                            ${asi.updaterName}
                    </td>
                    <td>
                        <c:if test="${asi.status eq 0}">未退票</c:if>
                        <c:if test="${asi.status eq 1}">已退票</c:if>
                    </td>
                </tr>
                </c:forEach>
            </c:if>
            <c:if test="${empty afterSalesVo.afterSalesInvoiceVoList}">
                <!-- 查询无结果弹出 -->
                <tr>
                    <td colspan='9'>暂无记录！</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">退票材料</div>

        <c:if test="${afterSalesVo.atferSalesStatus eq 1
            && (user.positType eq 310 || user.positType eq 312 || user.positType eq 589)}">
            <div class="title-click nobor pop-new-data"
                 layerParams='{"width":"600px","height":"300px","title":"上传退票材料","link":"/order/afterSalesCommon/ticketReturnInit.do?afterSalesId=${afterSalesVo.afterSalesId}"}'>上传退票材料</div>
        </c:if>
        </div>
        <table class="table">
            <thead>
            <tr>
                <th>操作人</th>
                <th>上传材料</th>
                <th>操作时间</th>
                <th>操作</th>
            </tr>
            </thead>
            <tbody>
            <c:if test="${not empty afterSalesVo.afterInvoiceAttachmentList}">
                <c:forEach items="${afterSalesVo.afterInvoiceAttachmentList}" var="aia">
                    <tr>
                        <td>${aia.username}</td>
                        <td class="font-blue"><a href="http://${aia.domain}${aia.uri}" target="_blank">${aia.name}</a></td>
                        <td><date:date value="${aia.addTime}" /></td>
                        <td>
                            <div class="caozuo">
                                <span class="caozuo-red" onclick="contractReturnDel2(${aia.attachmentId})">删除</span>
                            </div>
                        </td>
                    </tr>
                </c:forEach>
            </c:if>
            <c:if test="${empty afterSalesVo.afterInvoiceAttachmentList}">
                <tr>
                    <td colspan="4">暂无记录！</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                售后服务信息
            </div>
            <c:if test="${afterSalesVo.isCalRefund == 0 && afterSalesVo.amountRefundStatus eq null}">
                <div class="title-click nobor  pop-new-data"
                     layerParams='{"width":"600px","height":"400px","title":"编辑售后服务费","link":"/order/afterSalesCommon/editInstallstionPage.do?afterSalesDetailId=${afterSalesVo.afterSalesDetailId}&&afterSalesId=${afterSalesVo.afterSalesId}&&serviceAmount=${afterSalesVo.serviceAmount}&&isSendInvoice=${afterSalesVo.isSendInvoice}&&invoiceType=${afterSalesVo.invoiceType}&flag=th"}'>
                    编辑服务费</div>
            </c:if>
        </div>
        <table class="table">
            <tbody>
            <tr>
                <td>维修费</td>
                <td><fmt:formatNumber type="number" value="${afterSalesVo.serviceAmount }" pattern="0.00" maxFractionDigits="2" /></td>
                <td>SKU</td>
                <td>
                    ${goodIdsStr}
                </td>
            </tr>
            <tr>
                <td>收票客户</td>
                <td>${afterSalesVo.invoiceTraderName }</td>
                <td>是否寄送</td>
                <td><c:if test="${afterSalesVo.isSendInvoice eq 1}">（寄送）</c:if>
                    <c:if test="${afterSalesVo.isSendInvoice eq 0}">（不寄送）</c:if></td>
            </tr>
            <tr>
                <td>发票类型</td>
                <td>
                    <c:if test="${afterSalesVo.invoiceType eq 429}">17%增值税专用发票
                    </c:if>
                    <c:if test="${afterSalesVo.invoiceType eq 430}">17%增值税普通发票
                    </c:if>
                    <c:if test="${afterSalesVo.invoiceType eq 682}">16%增值税专用发票
                    </c:if>
                    <c:if test="${afterSalesVo.invoiceType eq 681}">16%增值税普通发票
                    </c:if>
                    <c:if test="${afterSalesVo.invoiceType eq 972}">13%增值税专用发票
                    </c:if>
                    <c:if test="${afterSalesVo.invoiceType eq 971}">13%增值税普通发票
                    </c:if>
                    <c:if test="${afterSalesVo.invoiceType eq 683}">6%增值税普通发票
                    </c:if>
                    <c:if test="${afterSalesVo.invoiceType eq 684}">6%增值税专用发票
                    </c:if>
                    <c:if test="${afterSalesVo.invoiceType eq 685}">3%增值税普通发票
                    </c:if>
                    <c:if test="${afterSalesVo.invoiceType eq 686}">3%增值税专用发票
                    </c:if>
                    <c:if test="${afterSalesVo.invoiceType eq 687}">0%增值税普通发票
                    </c:if>
                    <c:if test="${afterSalesVo.invoiceType eq 688}">0%增值税专用发票
                    </c:if>
                </td>
                <td>收票联系人</td>
                <td>${afterSalesVo.invoiceTraderContactName }</td>
            </tr>
            <tr>
                <td>联系方式</td>
                <td>${afterSalesVo.invoiceTraderContactName }-${afterSalesVo.invoiceTraderContactMobile }</td>
                <td>收票地址</td>
                <td>${afterSalesVo.invoiceTraderArea } ${afterSalesVo.invoiceTraderAddress }</td>
            </tr>
            </tbody>
        </table>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                退款信息
            </div>
        </div>
        <table class="table">
            <thead>
            <tr>
                <th>已付款金额（不含信用支付）</th>
                <th>退货金额</th>
                <th>偿还账期</th>
                <th>售后服务费</th>
                <th>款项退还</th>
                <th>实际应退金额</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><fmt:formatNumber type="number" value="${afterSalesVo.payAmount}" pattern="0.00" maxFractionDigits="2" /></td>
                <td><fmt:formatNumber type="number" value="${afterSalesVo.refundAmount}" pattern="0.00" maxFractionDigits="2" /></td>
                <td class="warning-color1">${afterSalesVo.payPeriodAmount}</td>
                <td class="warning-color1">${afterSalesVo.serviceAmount}</td>
                <td class="warning-color1">
                    <c:if test="${afterSalesVo.refund eq 0}">无</c:if>
                    <c:if test="${afterSalesVo.refund eq 1}">退到余额</c:if>
                    <c:if test="${afterSalesVo.refund eq 2}">退回账户</c:if>
                </td>
                <td class="warning-color1"><fmt:formatNumber type="number" value="${afterSalesVo.finalRefundableAmount}" pattern="0.00" maxFractionDigits="2" /></td>
            </tr>
            </tbody>
        </table>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">付款申请</div>
            <c:if test="${shouldPayApplyButton}">
                <div class="title-click nobor  pop-new-data"
                     layerParams='{"id":"payApply","width":"80%","height":"600px","title":"新增退款付款申请","link":"/afterSalesOrder/payApply.do?afterSalesId=${afterSalesVo.afterSalesId}"}'>
                    新增退款付款申请
                </div>
            </c:if>
        </div>
        <table class="table  table-style6">
            <thead>
            <tr>
                <th class="wid6">申请金额</th>
                <th class="wid18">申请时间</th>
                <th class="wid12">申请人</th>
                <th class="wid8">交易名称</th>
                <th class="wid8">开户行及联行号</th>
                <th class="wid10">银行帐号</th>
                <th class="wid10">付款备注</th>
                <th class="wid5">审核状态</th>
                <th class="wid5">查看</th>
            </tr>
            </thead>
            <tbody>
            <c:if test="${not empty afterSalesVo.afterPayApplyList}">
                <c:forEach items="${afterSalesVo.afterPayApplyList}" var="apal" varStatus="num_index">
                    <tr>
                        <td><fmt:formatNumber type="number" value="${apal.amount}" pattern="0.00" maxFractionDigits="2" /></td>
                        <td><date:date value="${apal.addTime}" format="yyyy.MM.dd HH:mm:ss"/></td>
                        <td>
                            ${apal.creatorName}
                        </td>
                        <td>${apal.traderName}</td>
                        <td>
                                ${apal.bank}<br/>${apal.bankCode}
                        </td>
                        <td>${apal.bankAccount}</td>
                        <td>${apal.comments}</td>
                        <td>
                            <c:choose>
                                <c:when test="${apal.validStatus eq 0}">待审核</c:when>
                                <c:when test="${apal.validStatus eq 1}">通过</c:when>
                                <c:when test="${apal.validStatus eq 2}">不通过</c:when>
                                <c:otherwise>--</c:otherwise>
                            </c:choose>
                        </td>
                        <td>
                            <div class="caozuo">
                                <span class="caozuo-blue pop-new-data"
                                      layerparams='{"width":"50%","height":"30%","title":"付款申请审核信息","link":"<%=basePath%>finance/after/paymentVerify.do?payApplyId=${apal.payApplyId}"}'>查看</span>
                            </div>
                        </td>
                    </tr>
                </c:forEach>
            </c:if>
            <c:if test="${empty afterSalesVo.afterPayApplyList}">
                <tr>
                    <td colspan="9">暂无记录</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">交易记录</div>
        </div>
        <table class="table">
            <thead>
            <tr>
                <td colspan="11">
                    <div style="float: left; margin-left: 100px;">实际应退金额：
                        <fmt:formatNumber type="number" value="${afterSalesVo.finalRefundableAmount}" pattern="0.00" maxFractionDigits="2" />
                    </div>
                    <div style="float: left; margin-left: 100px;">客户已退金额：
                        <fmt:formatNumber type="number" value="${refundAmount}" pattern="0.00" maxFractionDigits="2" />
                    </div>
                    <div style="float: left; margin-left: 100px;">退款状态：
                        <c:if test="${afterSalesVo.amountRefundStatus == 0}">无退款</c:if>
                        <c:if test="${afterSalesVo.amountRefundStatus == 1}">未退款</c:if>
                        <c:if test="${afterSalesVo.amountRefundStatus == 2}">部分退款</c:if>
                        <c:if test="${afterSalesVo.amountRefundStatus == 3}">全部退款</c:if>
                    </div>
                </td>
            </tr>
            <tr>
                <th>记账编号</th>
                <th>业务类型</th>
                <th>交易时间</th>
                <th>主体交易方式</th>
                <th>交易金额</th>
                <th>交易方式</th>
                <th>交易名称</th>
                <th>交易备注</th>
                <th>操作时间</th>
                <th>操作人</th>
                <th>操作</th>
            </tr>
            </thead>
            <tbody>
            <c:if test="${not empty afterSalesVo.afterCapitalBillList}">
                <c:forEach items="${afterSalesVo.afterCapitalBillList}" var="acb">
                    <tr>
                        <td>${acb.capitalBillNo}</td>
                        <td>
                            <c:if test="${acb.capitalBillDetail.bussinessType eq 525}">订单付款</c:if>
                            <c:if test="${acb.capitalBillDetail.bussinessType eq 526}">订单收款</c:if>
                            <c:if test="${acb.capitalBillDetail.bussinessType eq 531}">退款</c:if>
                            <c:if test="${acb.capitalBillDetail.bussinessType eq 532}">资金转移</c:if>
                            <c:if test="${acb.capitalBillDetail.bussinessType eq 533}">信用还款</c:if>
                        </td>
                        <td>
                            <c:if test="${acb.traderTime != 0}">
                                <date:date value="${acb.traderTime}" />
                            </c:if>
                        </td>
                        <td>
                            <c:if test="${acb.traderSubject == 1}">
                                对公
                            </c:if>
                            <c:if test="${acb.traderSubject == 2}">
                                对私
                            </c:if>
                        </td>
                        <td><fmt:formatNumber type="number" value="${acb.amount}" pattern="0.00" maxFractionDigits="2" /></td>
                        <td>
                            <c:if test="${acb.traderMode eq 520}">支付宝</c:if>
                            <c:if test="${acb.traderMode eq 521}">银行</c:if>
                            <c:if test="${acb.traderMode eq 522}">微信</c:if>
                            <c:if test="${acb.traderMode eq 522}">现金</c:if>
                            <c:if test="${acb.traderMode eq 527}">信用支付</c:if>
                            <c:if test="${acb.traderMode eq 528}">余额支付</c:if>
                            <c:if test="${acb.traderMode eq 529}">退还信用</c:if>
                            <c:if test="${acb.traderMode eq 530}">退还余额</c:if>
                        </td>
                        <td>${acb.payer}</td>
                        <td class="text-left">${acb.comments}</td>
                        <td>
                            <c:if test="${acb.addTime != 0}">
                                <date:date value="${acb.addTime}" />
                            </c:if>
                        </td>
                        <td>${acb.creatorName}</td>
                        <td>
                            <c:if test="${(acb.traderType == 2 || acb.traderType == 5) && acb.bankBillId != 0}">
                                <div class="caozuo">
                                    <c:choose>
                                        <c:when test="${not empty acb.receiptUrl}">
                                            <span class="caozuo-blue" onclick="openFile('${acb.receiptUrl}')">回单</span>
                                        </c:when>
                                        <c:otherwise>
                                        <span class="caozuo-blue addtitle"
                                              tabTitle='{"num":"credentials${acb.bankBillId}", "link":"<%=basePath%>finance/capitalbill/credentials.do?bankBillId=${acb.bankBillId}","title":"电子回执单"}'>预览</span>
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                            </c:if>
                        </td>
                    </tr>
                </c:forEach>
            </c:if>
            </tbody>
        </table>
    </div>

    <c:if test="${afterSalesVo.status eq 2}">
        <%--开票申请模块--%>
        <tags:aftersale_invoice_apply_info invoiceApplyList="${invoiceApplyList}"/>
    </c:if>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">发票记录</div>
        </div>
        <table class="table">
            <thead>
            <tr>
                <td colspan="12">
                    <div style="float: left; margin-left: 100px;">开票状态：
                        <c:if test="${afterSalesVo.invoiceMakeoutStatus == 0}">无开票</c:if>
                        <c:if test="${afterSalesVo.invoiceMakeoutStatus == 1}">未开票</c:if>
                        <c:if test="${afterSalesVo.invoiceMakeoutStatus == 2}">全部开票</c:if>
                    </div>
                </td>
            </tr>
            <tr>
                <th>发票号</th>
                <th>发票代码</th>
                <th>票种</th>
                <th>红蓝字</th>
                <th>发票金额</th>
                <th>开票时间</th>
                <th>操作时间</th>
                <th>操作人</th>
                <th>快递公司</th>
                <th>快递单号</th>
                <th>快递状态</th>
                <th>操作</th>
            </tr>
            </thead>
            <tbody>
            <c:if test="${not empty afterSalesVo.afterOpenInvoiceList}">
                <c:forEach items="${afterSalesVo.afterOpenInvoiceList}" var="aoi">
                    <tr>
                        <td>${aoi.invoiceNo}</td>
                        <td>${aoi.invoiceCode}</td>
                        <td>
                            <c:if test="${aoi.invoiceType eq 429}">17%增值税专用发票</c:if>
                            <c:if test="${aoi.invoiceType eq 430}">17%增值税普通发票</c:if>
                            <c:if test="${aoi.invoiceType eq 682}">16%增值税专用发票</c:if>
                            <c:if test="${aoi.invoiceType eq 681}">16%增值税普通发票</c:if>
                            <c:if test="${aoi.invoiceType eq 972}">13%增值税专用发票</c:if>
                            <c:if test="${aoi.invoiceType eq 971}">13%增值税普通发票</c:if>
                            <c:if test="${aoi.invoiceType eq 683}">6%增值税普通发票</c:if>
                            <c:if test="${aoi.invoiceType eq 684}">6%增值税专用发票</c:if>
                            <c:if test="${aoi.invoiceType eq 685}">3%增值税普通发票</c:if>
                            <c:if test="${aoi.invoiceType eq 686}">3%增值税专用发票</c:if>
                            <c:if test="${aoi.invoiceType eq 687}">0%增值税普通发票</c:if>
                            <c:if test="${aoi.invoiceType eq 688}">0%增值税专用发票</c:if>
                        </td>

                        <td>
                            <c:choose>
                                <c:when test="${aoi.colorType eq 1}">
                                    <c:choose>
                                        <c:when test="${aoi.isEnable eq 0}">
                                            <span style="color: red">红字作废</span>
                                        </c:when>
                                        <c:otherwise>
                                            <span style="color: red">红字有效</span>
                                        </c:otherwise>
                                    </c:choose>
                                </c:when>
                                <c:otherwise>
                                    <c:choose>
                                        <c:when test="${aoi.isEnable eq 0}">
                                            <span style="color: red">蓝字作废</span>
                                        </c:when>
                                        <c:otherwise>
                                            蓝字有效
                                        </c:otherwise>
                                    </c:choose>
                                </c:otherwise>
                            </c:choose>
                        </td>
                        <td><fmt:formatNumber type="number" value="${aoi.amount}" pattern="0.00" maxFractionDigits="2" /></td>
                        <td><date:date value="${aoi.validTime}" /></td>
                        <td><date:date value="${aoi.addTime}" /></td>
                        <td>${aoi.creatorName}</td>
                        <td>${aoi.logisticsName}</td>
                        <td>${aoi.logisticsNo}</td>
                        <td>
                            <c:choose>
                                <c:when test="${aoi.arrivalStatus eq 0}">
                                    未收货
                                </c:when>
                                <c:when test="${aoi.arrivalStatus eq 1}">
                                    部分收货
                                </c:when>
                                <c:when test="${aoi.arrivalStatus eq 2}">
                                    全部收货
                                </c:when>
                            </c:choose>
                        </td>
                        <td>
                            <a href= "javascript:void(0);"
                               style="color: rgb(51, 132, 239)"
                               onclick="viewAndDownloadInvoice('${aoi.ossFileUrl}','${aoi.invoiceCode}',
                                       '${aoi.invoiceNo}','${aoi.invoiceId}')">查看发票</a>
                        </td>
                    </tr>
                </c:forEach>
            </c:if>
            <c:if test="${empty afterSalesVo.afterOpenInvoiceList}">
                <!-- 查询无结果弹出 -->
                <tr>
                    <td colspan='12'>暂无记录！</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">支出记录</div>
            <c:if test="${afterSalesVo.atferSalesStatus eq 1
            && user.positType eq 312}">
                <div class="title-click nobor  pop-new-data"
                     layerParams='{"width":"600px","height":"400px","title":"新增支出记录","link":"/order/afterSalesCommon/addExpenditureRecord.do?afterSalesId=${afterSalesVo.afterSalesId}"}'>
                    新增支出记录</div>
            </c:if>
        </div>
        <%@ include file="add_expenditure_common.jsp"%>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">收入记录</div>
            <c:if test="${afterSalesVo.atferSalesStatus eq 1
            && user.positType eq 312}">
                <div class="title-click nobor  pop-new-data"
                     layerParams='{"width":"600px","height":"400px","title":"新增收入记录","link":"/order/afterSalesCommon/addRevenueRecord.do?afterSalesId=${afterSalesVo.afterSalesId}"}'>
                    新增收入记录</div>
            </c:if>
        </div>
        <%@ include file="add_revenue_common.jsp"%>
    </div>

    <!-- 沟通记录只能让售后看 -->
    <c:if test="${sessionScope.curr_user.positType eq 312}">
        <div class="parts">
            <div class="title-container">
                <div class="table-title nobor">
                    沟通记录
                </div>
                <c:if test="${afterSalesVo.status eq 2}">
                    <div class="title-click nobor  pop-new-data"
                         layerParams='{"width":"850px","height":"460px","title":"新增沟通记录","link":"<%= basePath %>/aftersales/order/addCommunicatePage.do?afterSalesId=${afterSalesVo.afterSalesId}&&traderId=${afterSalesVo.traderId }&&traderType=1"}'>
                        新增
                    </div>
                </c:if>
            </div>
            <table class="table table-bordered table-striped table-condensed table-centered">
                <thead>
                <tr>
                    <th class="wid10">沟通时间</th>
                    <th class="">录音</th>
                    <th class="">联系人</th>
                    <th class="">联系方式</th>
                    <th class="">沟通方式</th>
                    <th class="wid30">沟通内容（AI分析整理）</th>
                    <th class="">操作人</th>
                    <th class="wid8">下次联系日期</th>
                    <th class="wid15">下次沟通内容</th>
                    <th class="">备注</th>
                    <th class="wid10">创建时间</th>
                    <th class="wid6">操作</th>
                </tr>
                </thead>
                <tbody>
                <c:if test="${not empty communicateList}">
                    <c:forEach items="${communicateList}" var="communicateRecord" varStatus="">
                        <tr>
                            <td><date:date value ="${communicateRecord.begintime} "/>~<date:date value ="${communicateRecord.endtime}" format="HH:mm:ss"/></td>
                            <td><c:if test="${not empty communicateRecord.coidUri }">${communicateRecord.communicateRecordId }</c:if></td>
                            <td>${communicateRecord.contactName}</td>
                            <td>${communicateRecord.phone}</td>
                            <td>${communicateRecord.communicateModeName}</td>
                            <td>
                                <ul class="communicatecontent ml0">
                                    <c:choose>
                                        <c:when test="${not empty communicateRecord.tag }">
                                            <c:forEach items="${communicateRecord.tag }" var="tag">
                                                <li class="bluetag" title="${tag.tagName}">${tag.tagName}</li>
                                            </c:forEach>
                                        </c:when>
                                        <c:otherwise>
                                            <li>${communicateRecord.contactContent }</li>
                                        </c:otherwise>
                                    </c:choose>
                                </ul>
                            </td>
                            <td>${communicateRecord.user.username}</td>
                            <c:choose>
                                <c:when test="${communicateRecord.isDone == 0 }">
                                    <td class="font-red">${communicateRecord.nextContactDate }</td>
                                </c:when>
                                <c:otherwise>
                                    <td>${communicateRecord.nextContactDate }</td>
                                </c:otherwise>
                            </c:choose>
                            <td>${communicateRecord.nextContactContent}</td>
                            <td>${communicateRecord.comments}</td>
                            <td><date:date value ="${communicateRecord.addTime} "/></td>

                            <td class="caozuo">
                                <c:if test="${isSupply ne 1}">
                                    <c:if test="${afterSalesVo.atferSalesStatus ne 3 && afterSalesVo.atferSalesStatus ne 2 && afterSalesVo.verifyStatus ne 0}">
		                        	<span class="border-blue pop-new-data"
                                          layerParams='{"width":"60%","height":"63%","title":"编辑沟通记录","link":"<%= basePath %>/aftersales/order/editcommunicate.do?orderFlag=${afterSalesVo.atferSalesStatus }&flag=${afterSalesVo.status }&communicateRecordId=${communicateRecord.communicateRecordId}&afterSalesId=${afterSalesVo.afterSalesId}&&traderId=${afterSalesVo.traderId }&&traderType=1"}'>编辑</span>
                                    </c:if>
                                </c:if>
                            </td>
                        </tr>
                    </c:forEach>
                </c:if>
                <c:if test="${empty communicateList}">
                    <!-- 查询无结果弹出 -->
                    <tr>
                        <td colspan='12'>暂无记录！</td>
                    </tr>
                </c:if>
                </tbody>
            </table>
        </div>
    </c:if>

    <%--回访记录模块--%>
    <div class="parts">
        <%@ include file="afterSales_return_visit.jsp"%>
    </div>


    <%--各项审核记录模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                各项审核记录
            </div>
        </div>
        <%@ include file="add_auditRecords_common.jsp"%>
    </div>
</div>
</body>

</html>
