package com.vedeng.erp.settlement.service.impl.api;

import cn.hutool.core.util.StrUtil;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.finance.dto.PayApplyPayBankResultDto;
import com.vedeng.erp.finance.dto.PayVedengBankDto;
import com.vedeng.erp.finance.service.PayVedengBankApiService;
import com.vedeng.erp.settlement.mapper.PayVedengBankMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class PayVedengBankApiServiceImpl implements PayVedengBankApiService {

    @Value("${ignore_trade_name}")
    private String ignoreTradeName;

    @Resource
    private PayVedengBankMapper payVedengBankMapper;

    @Override
    public PayVedengBankDto queryInfoByPayVedengBankId(Integer payVedengBankId) {
        return payVedengBankMapper.queryInfoByPayVedengBankId(payVedengBankId);
    }

    @Override
    public PayVedengBankDto queryInfoByPayBankNo(String payBankNo) {
        if (StrUtil.isEmpty(payBankNo)) {
            return new PayVedengBankDto();
        }
        return payVedengBankMapper.queryInfoByPayBankNo(payBankNo);
    }

    @Override
    public List<String> getIgnoreTraderName() {
        if (StringUtils.isBlank(ignoreTradeName)){
           return null;
        }
        String[] split = ignoreTradeName.split(",");
        return Arrays.stream(split).collect(Collectors.toList());
    }

    @Override
    public PayVedengBankDto queryBankInfo(Integer bankTag) {
        PayVedengBankDto kingDeePayVedengBankDto = new PayVedengBankDto();
        switch (bankTag){
            case 1:
                //建设银行
                kingDeePayVedengBankDto = payVedengBankMapper.queryInfoByPayVedengBankId(ErpConstant.SIX);
                break;
            case 2:
                //南京银行
                kingDeePayVedengBankDto = payVedengBankMapper.queryInfoByPayVedengBankId(ErpConstant.TWO);
                break;
            case 3:
                //中国银行
                kingDeePayVedengBankDto = payVedengBankMapper.queryInfoByPayVedengBankId(ErpConstant.ONE);
                break;
            case 4:
                //支付宝
                kingDeePayVedengBankDto = payVedengBankMapper.queryInfoByPayVedengBankId(ErpConstant.FOUR);
                break;
            case 5:
                //微信
                kingDeePayVedengBankDto = payVedengBankMapper.queryInfoByPayVedengBankId(ErpConstant.FIVE);
                break;
            case 6:
                //交通银行
                kingDeePayVedengBankDto = payVedengBankMapper.queryInfoByPayVedengBankId(ErpConstant.THREE);
                break;
            case 7:
                //民生银行
                kingDeePayVedengBankDto = payVedengBankMapper.queryInfoByPayVedengBankId(ErpConstant.SEVEN);
                break;
            default:
                return null;
        }
        return kingDeePayVedengBankDto;
    }

    @Override
    public String getKingDeeBankCodeByBankNo(String payBankNo) {
        if (StringUtils.isBlank(payBankNo)){
            return null;
        }
        return payVedengBankMapper.getKingDeeBankCodeByBankNo(payBankNo);
    }

    @Override
    public List<PayApplyPayBankResultDto> queryPayBankByBuyOrderNo(String buyOrderNo) {
        List<PayApplyPayBankResultDto> payApplyPayBankResultDtos = payVedengBankMapper.queryPayBankByBuyOrder(buyOrderNo);
        return payApplyPayBankResultDtos;
    }
}
