package com.vedeng.erp.kingdee.task.data;

import cn.hutool.core.collection.CollectionUtil;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.dto.KingDeeCustomerDto;
import com.vedeng.erp.kingdee.service.KingDeeCustomerApiService;
import com.vedeng.erp.kingdee.service.KingDeeCustomerService;
import com.vedeng.erp.trader.dto.TraderCustomerDto;
import com.vedeng.erp.trader.dto.TraderFinanceDto;
import com.vedeng.erp.trader.service.TraderCustomerApiService;
import com.vedeng.erp.trader.service.TraderFinanceApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 历史客户数据推送金蝶
 * @date 2022/9/7 13:23
 **/
@Component
@JobHandler(value = "KingDeeTraderCustomerDateTask")
public class KingDeeTraderCustomerDateTask extends AbstractJobHandler {

    @Autowired
    private KingDeeCustomerService kingDeeCustomerService;
    @Autowired
    private KingDeeCustomerApiService kingDeeCustomerApiService;
    @Autowired
    private TraderCustomerApiService traderCustomerApiService;
    @Autowired
    private TraderFinanceApiService traderFinanceApiService;


    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        List<Integer> idList = Arrays.stream(param.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        idList.forEach(id -> {
            KingDeeCustomerDto kingDeeCustomerInfo = traderCustomerApiService.getKingDeeCustomerInfo(id);
            kingDeeCustomerApiService.register(kingDeeCustomerInfo);
        });
        return ReturnT.SUCCESS;
    }



    /**
     * 所有符合条件的客户信息推送金蝶
     */
    private void allSuitTraderPushKingDeeCreate() {
        boolean flag = true;
        //推送审核通过的客户
        while (flag) {
            List<TraderCustomerDto> traderCustomerDtos = traderCustomerApiService.selectPushKingDeeTraderCustomerData(null, null, 1000);
            batchPushKingDee(traderCustomerDtos);
            if (CollectionUtil.isEmpty(traderCustomerDtos)) {
                flag = false;
            }
        }
        //推送资质审核通过的客户
        flag = true;
        while (flag) {
            List<TraderCustomerDto> traderCustomerDtos = traderCustomerApiService.selectPushKingDeeTraderCustomerlicenceData(null, null, 1000);
            batchPushKingDee(traderCustomerDtos);
            if (CollectionUtil.isEmpty(traderCustomerDtos)) {
                flag = false;
            }
        }
    }

    /**
     * 推送金蝶
     *
     * @param traderSupplierDtos 数据
     */
    private void batchPushKingDee(List<TraderCustomerDto> traderSupplierDtos) {
        if (CollectionUtil.isNotEmpty(traderSupplierDtos)) {
            for (TraderCustomerDto data : traderSupplierDtos) {
                KingDeeCustomerDto kingDeeCustomerDto = bindKingDeeData(data);
                kingDeeCustomerService.save(kingDeeCustomerDto);
            }
        }
    }


    /**
     * 绑定对象
     *
     * @param data 查询的数据
     * @return KingDeeCustomerDto
     */
    private KingDeeCustomerDto bindKingDeeData(TraderCustomerDto data) {

        TraderFinanceDto traderFinanceDto = traderFinanceApiService.selectCustomerFiance(data.getTraderCustomerId(), data.getTraderId());

        KingDeeCustomerDto kingDeeCustomerDto = new KingDeeCustomerDto(KingDeeBizEnums.saveCustomer, data.getTraderCustomerId(), data.getTraderName());
        kingDeeCustomerDto.setFInvoiceBankAccount(traderFinanceDto.getBankAccount());
        kingDeeCustomerDto.setFInvoiceBankName(traderFinanceDto.getBank());
        kingDeeCustomerDto.setFTaxRegisterCode(traderFinanceDto.getTaxNum());

        return kingDeeCustomerDto;
    }

}
