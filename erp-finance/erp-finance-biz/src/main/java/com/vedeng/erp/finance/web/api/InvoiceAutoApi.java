package com.vedeng.erp.finance.web.api;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.redis.redission.RedissonLockUtils;
import com.vedeng.common.redis.utils.RedisUtil;
import com.vedeng.erp.finance.service.InvoiceAutoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/18 9:33
 **/
@ExceptionController
@RestController
@Slf4j
@RequestMapping("/invoiceAuto/api")
public class InvoiceAutoApi {


    private static final int CORE_POOL_SIZE = 1;
    private static final int MAX_POOL_SIZE = CORE_POOL_SIZE * 2;
    private static final long KEEP_ALIVE_TIME = 60L;

    private final ThreadPoolExecutor executor = new ThreadPoolExecutor(CORE_POOL_SIZE,
            MAX_POOL_SIZE, KEEP_ALIVE_TIME, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(MAX_POOL_SIZE * 100, true),
            new ThreadFactoryBuilder().setNameFormat("invoiceOpen-pool-%d").build(),
            new ThreadPoolExecutor.AbortPolicy());


    @Autowired
    private InvoiceAutoService invoiceAutoService;


    @ResponseBody
    @RequestMapping(value = "/open")
    @NoNeedAccessAuthorization
    public R<?> open() throws InterruptedException {

        boolean hasKey = RedisUtil.KeyOps.hasKey(ErpConstant.INVOICING_OPEN_TASK);
        boolean hasAfterKey = RedisUtil.KeyOps.hasKey(ErpConstant.INVOICING_AFTERMARKET_OPEN_TASK);
        if (hasKey||hasAfterKey) {
            log.info("加锁失败，手动触开票,存在处理中的开票业务");
            return R.error("存在处理中的开票业务,请稍后再试");
        }


        executor.submit(() -> {

            try {
                invoiceAutoService.doPageOpen();
            } catch (Exception e) {
                log.error("触发开票,异常",e);
            }finally {
                // 避免数据丢失
                RedisUtil.KeyOps.delete(ErpConstant.INVOICING_OPEN_TOTAL);
            }

        });

        return R.success();
    }


    @ResponseBody
    @RequestMapping(value = "/openLoading")
    @NoNeedAccessAuthorization
    public R<?> openLoading() {

        long total = RedisUtil.StringOps.incrBy(ErpConstant.INVOICING_OPEN_TOTAL, 0);
        long done = RedisUtil.StringOps.incrBy(ErpConstant.INVOICING_OPEN_LOAD, 0);
        if (total == 0) {
            return R.success(100);
        }
        if (total == done) {
            return R.success(100);
        }
        long result = done * 100 / total;
        return R.success(result);
    }



}
