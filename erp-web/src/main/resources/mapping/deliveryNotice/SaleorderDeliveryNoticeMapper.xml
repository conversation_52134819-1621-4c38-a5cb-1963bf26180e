<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.order.dao.SaleorderDeliveryNoticeMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.order.model.SaleorderDeliveryNotice">
    <id column="DELIVERY_NOTICE_ID" jdbcType="INTEGER" property="deliveryNoticeId" />
    <result column="DELIVERY_NOTICE_NO" jdbcType="VARCHAR" property="deliveryNoticeNo" />
    <result column="ORDER_ID" jdbcType="INTEGER" property="orderId" />
    <result column="ORDER_NO" jdbcType="VARCHAR" property="orderNo" />
    <result column="DELIVERY_STATUS" jdbcType="TINYINT" property="deliveryStatus" />
    <result column="STATUS" jdbcType="BIT" property="status" />
    <result column="VALID_TIME" jdbcType="BIGINT" property="validTime" />
    <result column="AUDIT_STATUS" jdbcType="BIT" property="auditStatus" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName" />
    <result column="TRADER_CONTACT_ID" jdbcType="INTEGER" property="traderContactId" />
    <result column="TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="traderContactName" />
    <result column="TRADER_CONTACT_MOBILE" jdbcType="VARCHAR" property="traderContactMobile" />
    <result column="TRADER_CONTACT_TELEPHONE" jdbcType="VARCHAR" property="traderContactTelephone" />
    <result column="TRADER_ADDRESS_ID" jdbcType="INTEGER" property="traderAddressId" />
    <result column="TRADER_AREA_ID" jdbcType="INTEGER" property="traderAreaId" />
    <result column="TRADER_AREA" jdbcType="VARCHAR" property="traderArea" />
    <result column="TRADER_ADDRESS" jdbcType="VARCHAR" property="traderAddress" />
    <result column="TRADER_COMMENTS" jdbcType="VARCHAR" property="traderComments" />
    <result column="TAKE_TRADER_ID" jdbcType="INTEGER" property="takeTraderId" />
    <result column="TAKE_TRADER_NAME" jdbcType="VARCHAR" property="takeTraderName" />
    <result column="TAKE_TRADER_CONTACT_ID" jdbcType="INTEGER" property="takeTraderContactId" />
    <result column="TAKE_TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="takeTraderContactName" />
    <result column="TAKE_TRADER_CONTACT_MOBILE" jdbcType="VARCHAR" property="takeTraderContactMobile" />
    <result column="TAKE_TRADER_CONTACT_TELEPHONE" jdbcType="VARCHAR" property="takeTraderContactTelephone" />
    <result column="TAKE_TRADER_ADDRESS_ID" jdbcType="INTEGER" property="takeTraderAddressId" />
    <result column="TAKE_TRADER_AREA_ID" jdbcType="INTEGER" property="takeTraderAreaId" />
    <result column="TAKE_TRADER_AREA" jdbcType="VARCHAR" property="takeTraderArea" />
    <result column="TAKE_TRADER_ADDRESS" jdbcType="VARCHAR" property="takeTraderAddress" />
    <result column="FREIGHT_DESCRIPTION" jdbcType="INTEGER" property="freightDescription" />
    <result column="DELIVERY_TYPE" jdbcType="INTEGER" property="deliveryType" />
    <result column="LOGISTICS_ID" jdbcType="INTEGER" property="logisticsId" />
    <result column="LOGISTICS_COMMENTS" jdbcType="VARCHAR" property="logisticsComments" />
    <result column="IS_PRINTOUT" jdbcType="BIT" property="isPrintout" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    DELIVERY_NOTICE_ID, DELIVERY_NOTICE_NO, ORDER_ID, ORDER_NO, DELIVERY_STATUS, `STATUS`,
    VALID_TIME, AUDIT_STATUS, ADD_TIME, CREATOR, MOD_TIME, UPDATER, TRADER_NAME, TRADER_CONTACT_ID,
    TRADER_CONTACT_NAME, TRADER_CONTACT_MOBILE, TRADER_CONTACT_TELEPHONE, TRADER_ADDRESS_ID,
    TRADER_AREA_ID, TRADER_AREA, TRADER_ADDRESS, TRADER_COMMENTS, TAKE_TRADER_ID, TAKE_TRADER_NAME,
    TAKE_TRADER_CONTACT_ID, TAKE_TRADER_CONTACT_NAME, TAKE_TRADER_CONTACT_MOBILE, TAKE_TRADER_CONTACT_TELEPHONE,
    TAKE_TRADER_ADDRESS_ID, TAKE_TRADER_AREA_ID, TAKE_TRADER_AREA, TAKE_TRADER_ADDRESS,
    FREIGHT_DESCRIPTION, DELIVERY_TYPE, LOGISTICS_ID, LOGISTICS_COMMENTS, IS_PRINTOUT
  </sql>
  <select id="selectByExample" parameterType="com.vedeng.order.model.SaleorderDeliveryNoticeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_SALEORDER_DELIVERY_NOTICE
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_SALEORDER_DELIVERY_NOTICE
    where DELIVERY_NOTICE_ID = #{deliveryNoticeId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_SALEORDER_DELIVERY_NOTICE
    where DELIVERY_NOTICE_ID = #{deliveryNoticeId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.order.model.SaleorderDeliveryNoticeExample">
    delete from T_SALEORDER_DELIVERY_NOTICE
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="DELIVERY_NOTICE_ID" keyProperty="deliveryNoticeId" parameterType="com.vedeng.order.model.SaleorderDeliveryNotice" useGeneratedKeys="true">
    insert into T_SALEORDER_DELIVERY_NOTICE (DELIVERY_NOTICE_NO, ORDER_ID, ORDER_NO,
      DELIVERY_STATUS, `STATUS`, VALID_TIME,
      AUDIT_STATUS, ADD_TIME, CREATOR,
      MOD_TIME, UPDATER, TRADER_NAME,
      TRADER_CONTACT_ID, TRADER_CONTACT_NAME, TRADER_CONTACT_MOBILE,
      TRADER_CONTACT_TELEPHONE, TRADER_ADDRESS_ID,
      TRADER_AREA_ID, TRADER_AREA, TRADER_ADDRESS,
      TRADER_COMMENTS, TAKE_TRADER_ID, TAKE_TRADER_NAME,
      TAKE_TRADER_CONTACT_ID, TAKE_TRADER_CONTACT_NAME,
      TAKE_TRADER_CONTACT_MOBILE, TAKE_TRADER_CONTACT_TELEPHONE,
      TAKE_TRADER_ADDRESS_ID, TAKE_TRADER_AREA_ID,
      TAKE_TRADER_AREA, TAKE_TRADER_ADDRESS, FREIGHT_DESCRIPTION,
      DELIVERY_TYPE, LOGISTICS_ID, LOGISTICS_COMMENTS,
      IS_PRINTOUT)
    values (#{deliveryNoticeNo,jdbcType=VARCHAR}, #{orderId,jdbcType=INTEGER}, #{orderNo,jdbcType=VARCHAR},
      #{deliveryStatus,jdbcType=TINYINT}, #{status,jdbcType=BIT}, #{validTime,jdbcType=BIGINT},
      #{auditStatus,jdbcType=BIT}, #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER},
      #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}, #{traderName,jdbcType=VARCHAR},
      #{traderContactId,jdbcType=INTEGER}, #{traderContactName,jdbcType=VARCHAR}, #{traderContactMobile,jdbcType=VARCHAR},
      #{traderContactTelephone,jdbcType=VARCHAR}, #{traderAddressId,jdbcType=INTEGER},
      #{traderAreaId,jdbcType=INTEGER}, #{traderArea,jdbcType=VARCHAR}, #{traderAddress,jdbcType=VARCHAR},
      #{traderComments,jdbcType=VARCHAR}, #{takeTraderId,jdbcType=INTEGER}, #{takeTraderName,jdbcType=VARCHAR},
      #{takeTraderContactId,jdbcType=INTEGER}, #{takeTraderContactName,jdbcType=VARCHAR},
      #{takeTraderContactMobile,jdbcType=VARCHAR}, #{takeTraderContactTelephone,jdbcType=VARCHAR},
      #{takeTraderAddressId,jdbcType=INTEGER}, #{takeTraderAreaId,jdbcType=INTEGER},
      #{takeTraderArea,jdbcType=VARCHAR}, #{takeTraderAddress,jdbcType=VARCHAR}, #{freightDescription,jdbcType=INTEGER},
      #{deliveryType,jdbcType=INTEGER}, #{logisticsId,jdbcType=INTEGER}, #{logisticsComments,jdbcType=VARCHAR},
      #{isPrintout,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" keyColumn="DELIVERY_NOTICE_ID" keyProperty="deliveryNoticeId" parameterType="com.vedeng.order.model.SaleorderDeliveryNotice" useGeneratedKeys="true">
    insert into T_SALEORDER_DELIVERY_NOTICE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deliveryNoticeNo != null">
        DELIVERY_NOTICE_NO,
      </if>
      <if test="orderId != null">
        ORDER_ID,
      </if>
      <if test="orderNo != null">
        ORDER_NO,
      </if>
      <if test="deliveryStatus != null">
        DELIVERY_STATUS,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
      <if test="validTime != null">
        VALID_TIME,
      </if>
      <if test="auditStatus != null">
        AUDIT_STATUS,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="traderName != null">
        TRADER_NAME,
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID,
      </if>
      <if test="traderContactName != null">
        TRADER_CONTACT_NAME,
      </if>
      <if test="traderContactMobile != null">
        TRADER_CONTACT_MOBILE,
      </if>
      <if test="traderContactTelephone != null">
        TRADER_CONTACT_TELEPHONE,
      </if>
      <if test="traderAddressId != null">
        TRADER_ADDRESS_ID,
      </if>
      <if test="traderAreaId != null">
        TRADER_AREA_ID,
      </if>
      <if test="traderArea != null">
        TRADER_AREA,
      </if>
      <if test="traderAddress != null">
        TRADER_ADDRESS,
      </if>
      <if test="traderComments != null">
        TRADER_COMMENTS,
      </if>
      <if test="takeTraderId != null">
        TAKE_TRADER_ID,
      </if>
      <if test="takeTraderName != null">
        TAKE_TRADER_NAME,
      </if>
      <if test="takeTraderContactId != null">
        TAKE_TRADER_CONTACT_ID,
      </if>
      <if test="takeTraderContactName != null">
        TAKE_TRADER_CONTACT_NAME,
      </if>
      <if test="takeTraderContactMobile != null">
        TAKE_TRADER_CONTACT_MOBILE,
      </if>
      <if test="takeTraderContactTelephone != null">
        TAKE_TRADER_CONTACT_TELEPHONE,
      </if>
      <if test="takeTraderAddressId != null">
        TAKE_TRADER_ADDRESS_ID,
      </if>
      <if test="takeTraderAreaId != null">
        TAKE_TRADER_AREA_ID,
      </if>
      <if test="takeTraderArea != null">
        TAKE_TRADER_AREA,
      </if>
      <if test="takeTraderAddress != null">
        TAKE_TRADER_ADDRESS,
      </if>
      <if test="freightDescription != null">
        FREIGHT_DESCRIPTION,
      </if>
      <if test="deliveryType != null">
        DELIVERY_TYPE,
      </if>
      <if test="logisticsId != null">
        LOGISTICS_ID,
      </if>
      <if test="logisticsComments != null">
        LOGISTICS_COMMENTS,
      </if>
      <if test="isPrintout != null">
        IS_PRINTOUT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="deliveryNoticeNo != null">
        #{deliveryNoticeNo,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryStatus != null">
        #{deliveryStatus,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=BIT},
      </if>
      <if test="validTime != null">
        #{validTime,jdbcType=BIGINT},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=BIT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="traderName != null">
        #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactId != null">
        #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null">
        #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactMobile != null">
        #{traderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="traderContactTelephone != null">
        #{traderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="traderAddressId != null">
        #{traderAddressId,jdbcType=INTEGER},
      </if>
      <if test="traderAreaId != null">
        #{traderAreaId,jdbcType=INTEGER},
      </if>
      <if test="traderArea != null">
        #{traderArea,jdbcType=VARCHAR},
      </if>
      <if test="traderAddress != null">
        #{traderAddress,jdbcType=VARCHAR},
      </if>
      <if test="traderComments != null">
        #{traderComments,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderId != null">
        #{takeTraderId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderName != null">
        #{takeTraderName,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactId != null">
        #{takeTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderContactName != null">
        #{takeTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactMobile != null">
        #{takeTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactTelephone != null">
        #{takeTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderAddressId != null">
        #{takeTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderAreaId != null">
        #{takeTraderAreaId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderArea != null">
        #{takeTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderAddress != null">
        #{takeTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="freightDescription != null">
        #{freightDescription,jdbcType=INTEGER},
      </if>
      <if test="deliveryType != null">
        #{deliveryType,jdbcType=INTEGER},
      </if>
      <if test="logisticsId != null">
        #{logisticsId,jdbcType=INTEGER},
      </if>
      <if test="logisticsComments != null">
        #{logisticsComments,jdbcType=VARCHAR},
      </if>
      <if test="isPrintout != null">
        #{isPrintout,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.order.model.SaleorderDeliveryNoticeExample" resultType="java.lang.Long">
    select count(*) from T_SALEORDER_DELIVERY_NOTICE
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
    <select id="getMaxDeliveryNoticeNoBySaleorderId" resultType="java.lang.String">
SELECT
	max(cast(DELIVERY_NOTICE_NO as UNSIGNED))
FROM
	T_SALEORDER_DELIVERY_NOTICE
WHERE
	ORDER_ID = #{saleorderId,jdbcType=INTEGER}

    </select>
    <select id="getDeliveryNoticeGoodNum" resultType="java.lang.Integer">
SELECT
	IFNULL( sum( num ), 0 )
FROM
	T_SALEORDER_DELIVERY_NOTICE tsdn
	LEFT JOIN T_SALEORDER_DELIVERY_NOTICE_GOODS tsdng ON tsdn.DELIVERY_NOTICE_ID = tsdng.DELIVERY_NOTICE_ID
WHERE
	tsdn.`STATUS` != 3 and tsdng.delete_state != 1
	AND tsdn.ORDER_ID = #{saleorderId,jdbcType=INTEGER}
	AND tsdng.SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER}
	<if test="deliveryNoticeId !=null ">
      AND tsdn.DELIVERY_NOTICE_ID != #{deliveryNoticeId,jdbcType=INTEGER}
    </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
    update T_SALEORDER_DELIVERY_NOTICE
    <set>
      <if test="record.deliveryNoticeId != null">
        DELIVERY_NOTICE_ID = #{record.deliveryNoticeId,jdbcType=INTEGER},
      </if>
      <if test="record.deliveryNoticeNo != null">
        DELIVERY_NOTICE_NO = #{record.deliveryNoticeNo,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        ORDER_ID = #{record.orderId,jdbcType=INTEGER},
      </if>
      <if test="record.orderNo != null">
        ORDER_NO = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryStatus != null">
        DELIVERY_STATUS = #{record.deliveryStatus,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        `STATUS` = #{record.status,jdbcType=BIT},
      </if>
      <if test="record.validTime != null">
        VALID_TIME = #{record.validTime,jdbcType=BIGINT},
      </if>
      <if test="record.auditStatus != null">
        AUDIT_STATUS = #{record.auditStatus,jdbcType=BIT},
      </if>
      <if test="record.addTime != null">
        ADD_TIME = #{record.addTime,jdbcType=BIGINT},
      </if>
      <if test="record.creator != null">
        CREATOR = #{record.creator,jdbcType=INTEGER},
      </if>
      <if test="record.modTime != null">
        MOD_TIME = #{record.modTime,jdbcType=BIGINT},
      </if>
      <if test="record.updater != null">
        UPDATER = #{record.updater,jdbcType=INTEGER},
      </if>
      <if test="record.traderName != null">
        TRADER_NAME = #{record.traderName,jdbcType=VARCHAR},
      </if>
      <if test="record.traderContactId != null">
        TRADER_CONTACT_ID = #{record.traderContactId,jdbcType=INTEGER},
      </if>
      <if test="record.traderContactName != null">
        TRADER_CONTACT_NAME = #{record.traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="record.traderContactMobile != null">
        TRADER_CONTACT_MOBILE = #{record.traderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="record.traderContactTelephone != null">
        TRADER_CONTACT_TELEPHONE = #{record.traderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="record.traderAddressId != null">
        TRADER_ADDRESS_ID = #{record.traderAddressId,jdbcType=INTEGER},
      </if>
      <if test="record.traderAreaId != null">
        TRADER_AREA_ID = #{record.traderAreaId,jdbcType=INTEGER},
      </if>
      <if test="record.traderArea != null">
        TRADER_AREA = #{record.traderArea,jdbcType=VARCHAR},
      </if>
      <if test="record.traderAddress != null">
        TRADER_ADDRESS = #{record.traderAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.traderComments != null">
        TRADER_COMMENTS = #{record.traderComments,jdbcType=VARCHAR},
      </if>
      <if test="record.takeTraderId != null">
        TAKE_TRADER_ID = #{record.takeTraderId,jdbcType=INTEGER},
      </if>
      <if test="record.takeTraderName != null">
        TAKE_TRADER_NAME = #{record.takeTraderName,jdbcType=VARCHAR},
      </if>
      <if test="record.takeTraderContactId != null">
        TAKE_TRADER_CONTACT_ID = #{record.takeTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="record.takeTraderContactName != null">
        TAKE_TRADER_CONTACT_NAME = #{record.takeTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="record.takeTraderContactMobile != null">
        TAKE_TRADER_CONTACT_MOBILE = #{record.takeTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="record.takeTraderContactTelephone != null">
        TAKE_TRADER_CONTACT_TELEPHONE = #{record.takeTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="record.takeTraderAddressId != null">
        TAKE_TRADER_ADDRESS_ID = #{record.takeTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="record.takeTraderAreaId != null">
        TAKE_TRADER_AREA_ID = #{record.takeTraderAreaId,jdbcType=INTEGER},
      </if>
      <if test="record.takeTraderArea != null">
        TAKE_TRADER_AREA = #{record.takeTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="record.takeTraderAddress != null">
        TAKE_TRADER_ADDRESS = #{record.takeTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.freightDescription != null">
        FREIGHT_DESCRIPTION = #{record.freightDescription,jdbcType=INTEGER},
      </if>
      <if test="record.deliveryType != null">
        DELIVERY_TYPE = #{record.deliveryType,jdbcType=INTEGER},
      </if>
      <if test="record.logisticsId != null">
        LOGISTICS_ID = #{record.logisticsId,jdbcType=INTEGER},
      </if>
      <if test="record.logisticsComments != null">
        LOGISTICS_COMMENTS = #{record.logisticsComments,jdbcType=VARCHAR},
      </if>
      <if test="record.isPrintout != null">
        IS_PRINTOUT = #{record.isPrintout,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update T_SALEORDER_DELIVERY_NOTICE
    set DELIVERY_NOTICE_ID = #{record.deliveryNoticeId,jdbcType=INTEGER},
    DELIVERY_NOTICE_NO = #{record.deliveryNoticeNo,jdbcType=VARCHAR},
    ORDER_ID = #{record.orderId,jdbcType=INTEGER},
    ORDER_NO = #{record.orderNo,jdbcType=VARCHAR},
    DELIVERY_STATUS = #{record.deliveryStatus,jdbcType=TINYINT},
    `STATUS` = #{record.status,jdbcType=BIT},
    VALID_TIME = #{record.validTime,jdbcType=BIGINT},
    AUDIT_STATUS = #{record.auditStatus,jdbcType=BIT},
    ADD_TIME = #{record.addTime,jdbcType=BIGINT},
    CREATOR = #{record.creator,jdbcType=INTEGER},
    MOD_TIME = #{record.modTime,jdbcType=BIGINT},
    UPDATER = #{record.updater,jdbcType=INTEGER},
    TRADER_NAME = #{record.traderName,jdbcType=VARCHAR},
    TRADER_CONTACT_ID = #{record.traderContactId,jdbcType=INTEGER},
    TRADER_CONTACT_NAME = #{record.traderContactName,jdbcType=VARCHAR},
    TRADER_CONTACT_MOBILE = #{record.traderContactMobile,jdbcType=VARCHAR},
    TRADER_CONTACT_TELEPHONE = #{record.traderContactTelephone,jdbcType=VARCHAR},
    TRADER_ADDRESS_ID = #{record.traderAddressId,jdbcType=INTEGER},
    TRADER_AREA_ID = #{record.traderAreaId,jdbcType=INTEGER},
    TRADER_AREA = #{record.traderArea,jdbcType=VARCHAR},
    TRADER_ADDRESS = #{record.traderAddress,jdbcType=VARCHAR},
    TRADER_COMMENTS = #{record.traderComments,jdbcType=VARCHAR},
    TAKE_TRADER_ID = #{record.takeTraderId,jdbcType=INTEGER},
    TAKE_TRADER_NAME = #{record.takeTraderName,jdbcType=VARCHAR},
    TAKE_TRADER_CONTACT_ID = #{record.takeTraderContactId,jdbcType=INTEGER},
    TAKE_TRADER_CONTACT_NAME = #{record.takeTraderContactName,jdbcType=VARCHAR},
    TAKE_TRADER_CONTACT_MOBILE = #{record.takeTraderContactMobile,jdbcType=VARCHAR},
    TAKE_TRADER_CONTACT_TELEPHONE = #{record.takeTraderContactTelephone,jdbcType=VARCHAR},
    TAKE_TRADER_ADDRESS_ID = #{record.takeTraderAddressId,jdbcType=INTEGER},
    TAKE_TRADER_AREA_ID = #{record.takeTraderAreaId,jdbcType=INTEGER},
    TAKE_TRADER_AREA = #{record.takeTraderArea,jdbcType=VARCHAR},
    TAKE_TRADER_ADDRESS = #{record.takeTraderAddress,jdbcType=VARCHAR},
    FREIGHT_DESCRIPTION = #{record.freightDescription,jdbcType=INTEGER},
    DELIVERY_TYPE = #{record.deliveryType,jdbcType=INTEGER},
    LOGISTICS_ID = #{record.logisticsId,jdbcType=INTEGER},
    LOGISTICS_COMMENTS = #{record.logisticsComments,jdbcType=VARCHAR},
    IS_PRINTOUT = #{record.isPrintout,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.order.model.SaleorderDeliveryNotice">
    update T_SALEORDER_DELIVERY_NOTICE
    <set>
      <if test="deliveryNoticeNo != null">
        DELIVERY_NOTICE_NO = #{deliveryNoticeNo,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        ORDER_ID = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryStatus != null">
        DELIVERY_STATUS = #{deliveryStatus,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `STATUS` = #{status,jdbcType=BIT},
      </if>
      <if test="validTime != null">
        VALID_TIME = #{validTime,jdbcType=BIGINT},
      </if>
      <if test="auditStatus != null">
        AUDIT_STATUS = #{auditStatus,jdbcType=BIT},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="traderName != null">
        TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null">
        TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactMobile != null">
        TRADER_CONTACT_MOBILE = #{traderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="traderContactTelephone != null">
        TRADER_CONTACT_TELEPHONE = #{traderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="traderAddressId != null">
        TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER},
      </if>
      <if test="traderAreaId != null">
        TRADER_AREA_ID = #{traderAreaId,jdbcType=INTEGER},
      </if>
      <if test="traderArea != null">
        TRADER_AREA = #{traderArea,jdbcType=VARCHAR},
      </if>
      <if test="traderAddress != null">
        TRADER_ADDRESS = #{traderAddress,jdbcType=VARCHAR},
      </if>
      <if test="traderComments != null">
        TRADER_COMMENTS = #{traderComments,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderId != null">
        TAKE_TRADER_ID = #{takeTraderId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderName != null">
        TAKE_TRADER_NAME = #{takeTraderName,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactId != null">
        TAKE_TRADER_CONTACT_ID = #{takeTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderContactName != null">
        TAKE_TRADER_CONTACT_NAME = #{takeTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactMobile != null">
        TAKE_TRADER_CONTACT_MOBILE = #{takeTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactTelephone != null">
        TAKE_TRADER_CONTACT_TELEPHONE = #{takeTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderAddressId != null">
        TAKE_TRADER_ADDRESS_ID = #{takeTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderAreaId != null">
        TAKE_TRADER_AREA_ID = #{takeTraderAreaId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderArea != null">
        TAKE_TRADER_AREA = #{takeTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderAddress != null">
        TAKE_TRADER_ADDRESS = #{takeTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="freightDescription != null">
        FREIGHT_DESCRIPTION = #{freightDescription,jdbcType=INTEGER},
      </if>
      <if test="deliveryType != null">
        DELIVERY_TYPE = #{deliveryType,jdbcType=INTEGER},
      </if>
      <if test="logisticsId != null">
        LOGISTICS_ID = #{logisticsId,jdbcType=INTEGER},
      </if>
      <if test="logisticsComments != null">
        LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
      </if>
      <if test="isPrintout != null">
        IS_PRINTOUT = #{isPrintout,jdbcType=BIT},
      </if>
    </set>
    where DELIVERY_NOTICE_ID = #{deliveryNoticeId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.order.model.SaleorderDeliveryNotice">
    update T_SALEORDER_DELIVERY_NOTICE
    set DELIVERY_NOTICE_NO = #{deliveryNoticeNo,jdbcType=VARCHAR},
      ORDER_ID = #{orderId,jdbcType=INTEGER},
      ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      DELIVERY_STATUS = #{deliveryStatus,jdbcType=TINYINT},
      `STATUS` = #{status,jdbcType=BIT},
      VALID_TIME = #{validTime,jdbcType=BIGINT},
      AUDIT_STATUS = #{auditStatus,jdbcType=BIT},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      TRADER_CONTACT_MOBILE = #{traderContactMobile,jdbcType=VARCHAR},
      TRADER_CONTACT_TELEPHONE = #{traderContactTelephone,jdbcType=VARCHAR},
      TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER},
      TRADER_AREA_ID = #{traderAreaId,jdbcType=INTEGER},
      TRADER_AREA = #{traderArea,jdbcType=VARCHAR},
      TRADER_ADDRESS = #{traderAddress,jdbcType=VARCHAR},
      TRADER_COMMENTS = #{traderComments,jdbcType=VARCHAR},
      TAKE_TRADER_ID = #{takeTraderId,jdbcType=INTEGER},
      TAKE_TRADER_NAME = #{takeTraderName,jdbcType=VARCHAR},
      TAKE_TRADER_CONTACT_ID = #{takeTraderContactId,jdbcType=INTEGER},
      TAKE_TRADER_CONTACT_NAME = #{takeTraderContactName,jdbcType=VARCHAR},
      TAKE_TRADER_CONTACT_MOBILE = #{takeTraderContactMobile,jdbcType=VARCHAR},
      TAKE_TRADER_CONTACT_TELEPHONE = #{takeTraderContactTelephone,jdbcType=VARCHAR},
      TAKE_TRADER_ADDRESS_ID = #{takeTraderAddressId,jdbcType=INTEGER},
      TAKE_TRADER_AREA_ID = #{takeTraderAreaId,jdbcType=INTEGER},
      TAKE_TRADER_AREA = #{takeTraderArea,jdbcType=VARCHAR},
      TAKE_TRADER_ADDRESS = #{takeTraderAddress,jdbcType=VARCHAR},
      FREIGHT_DESCRIPTION = #{freightDescription,jdbcType=INTEGER},
      DELIVERY_TYPE = #{deliveryType,jdbcType=INTEGER},
      LOGISTICS_ID = #{logisticsId,jdbcType=INTEGER},
      LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
      IS_PRINTOUT = #{isPrintout,jdbcType=BIT}
    where DELIVERY_NOTICE_ID = #{deliveryNoticeId,jdbcType=INTEGER}
  </update>
</mapper>