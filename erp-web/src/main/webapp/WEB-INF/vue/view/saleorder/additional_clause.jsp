<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>

<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/vd-ui/common.css">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/vd-ui/common.css">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/vd-ui/ui.css">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/orderstream/additional_clause.css">

<div class="page-container" id="page-container">
    <div class="page-wrap" v-if="!loading">
        <div class="page-block" v-for="(block, bIndex) in blockList" :key="bIndex">
            <div class="page-block-title">{{ block.title }}</div>
            <div class="page-block-list" v-if="block.list && block.list.length">
                <div class="page-block-cnt" :class="{active: cnt.checked}" v-for="(cnt, cindex) in block.list" :key="cindex">
                    <div class="page-block-detail">
                        <ui-checkbox :checked.sync="cnt.checked" @change="getSelectedNum"></ui-checkbox>
                        <div class="page-block-txt" v-html="cnt.txt.replace(/xxx/g, '<span class=\'strong\'> ※※※ </span>')"></div>
                    </div>
                    <div class="page-block-form" v-if="cnt.checked && cnt.customInfo && cnt.customInfo.length">
                        <div class="page-block-form-item" v-for="(formItem, fIndex) in cnt.customInfo">
                            <div class="page-form-label"><span class="must">*</span>{{formItem.label}}：</div>
                            <div class="page-form-field" :ref="formItem.name">
                                <template v-if="formItem.type === 'address'">
                                    <div class="address-wrap">
                                        <ui-cascader 
                                            class="margin" 
                                            :data="addressData" 
                                            v-model="formItem.defalutValue" 
                                            clearable 
                                            filterable
                                            @change="handleFilterAddressChange(formItem, cnt, $event)"
                                            width="100%"
                                            v-if="addressData && addressData.length"
                                            :errorable="!!formItem.errorMessage" 
                                            :error-msg="formItem.errorMessage"
                                        ></ui-cascader>
                                    </div>
                                </template>
                                <template v-else-if="formItem.type === 'price'">
                                    <div class="price-wrap">
                                        <ui-input :errorable="!!formItem.errorMessage" :error-msg="formItem.errorMessage" v-model="formItem.value" maxlength="10" width="146px" @blur="validItem(formItem, cnt)"></ui-input>
                                        <span class="unit">元</span>
                                    </div>
                                </template>
                                <template v-else>
                                    <ui-input :placeholder="formItem.placeholder" :errorable="!!formItem.errorMessage" :error-msg="formItem.errorMessage" v-model="formItem.value" :maxlength="formItem.maxlength" :width="formItem.width || '510px'" @blur="validItem(formItem, cnt)"></ui-input>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="page-block custom-block" :class="{active: customChecked}">
            <div class="page-block-title-wrap">
                <ui-checkbox :checked.sync="customChecked" @change="getSelectedNum"></ui-checkbox>
                <div class="page-block-title">自定义条款</div>
                <div class="page-block-title-tip" v-if="customChecked"><i class="vd-ui_icon icon-caution1"></i>编写自定义条款注意法务风险，订单将会提交法务审批</div>
            </div>
            <div class="page-custom-form" ref="customValue" v-if="customChecked">
                <ui-input type="textarea" :errorable="!!customErrorMessage" show-word-limit :error-msg="customErrorMessage" v-model="customValue" maxlength="500" width="640px" height-auto @blur="validCustomItem"></ui-input>
            </div>
        </div>
        <div class="page-block-button">
            <ui-button type="primary" @click="submitAdditional">确定</ui-button>
            <ui-button  @click="hideDialog">取消</ui-button>
            <div class="select-num" v-if="selectedNum">已选{{ selectedNum }}条</div>
        </div>
    </div>
</div>
<script src="${pageContext.request.contextPath}/static/vue/element-ui/js/vue.js"></script>
<script src="${pageContext.request.contextPath}/static/vue/element-ui/js/axios.min.js"></script>
<script src="${pageContext.request.contextPath}/static/vue/vd-ui/ui.js"></script>
<script src="${pageContext.request.contextPath}/static/js/orderstream/saleorder/additional_clause.js"></script>
