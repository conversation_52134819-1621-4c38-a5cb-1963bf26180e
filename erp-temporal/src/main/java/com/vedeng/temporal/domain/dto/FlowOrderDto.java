package com.vedeng.temporal.domain.dto;

import java.util.Date;

public class FlowOrderDto {
    /** 主键 */
    private Long flowOrderId;
    /** 业务流转单编号 */
    private String flowOrderNo;
    /** 基础业务订单ID */
    private Integer baseOrderId;
    /** 基础业务订单编号 */
    private String baseOrderNo;
    /** 基础业务类型 1.采购 2.销售 */
    private Integer baseBusinessType;
    /** 审核状态，0:未审核, 1:已审核 */
    private Integer auditStatus;
    /** 审核人id */
    private Integer auditUserId;
    /** 审核人 */
    private String auditUsername;
    /** 审核时间 */
    private Date auditTime;
    /** 是否删除 */
    private Integer isDelete;
    /** 创建者 */
    private Integer creator;
    /** 修改者 */
    private Integer updater;
    /** 创建者名称 */
    private String creatorName;
    /** 修改者名称 */
    private String updaterName;
    /** 创建时间 */
    private Date addTime;
    /** 修改时间 */
    private Date modTime;
    /** 末级合同状态 */
    private Integer contractStatus;
    /** 推送方向 1:金蝶, 2:ERP */
    private Integer pushDirection;
    /** 来源ERP系统 */
    private String sourceErp;

    // getter/setter
    public Long getFlowOrderId() { return flowOrderId; }
    public void setFlowOrderId(Long flowOrderId) { this.flowOrderId = flowOrderId; }
    public String getFlowOrderNo() { return flowOrderNo; }
    public void setFlowOrderNo(String flowOrderNo) { this.flowOrderNo = flowOrderNo; }
    public Integer getBaseOrderId() { return baseOrderId; }
    public void setBaseOrderId(Integer baseOrderId) { this.baseOrderId = baseOrderId; }
    public String getBaseOrderNo() { return baseOrderNo; }
    public void setBaseOrderNo(String baseOrderNo) { this.baseOrderNo = baseOrderNo; }
    public Integer getBaseBusinessType() { return baseBusinessType; }
    public void setBaseBusinessType(Integer baseBusinessType) { this.baseBusinessType = baseBusinessType; }
    public Integer getAuditStatus() { return auditStatus; }
    public void setAuditStatus(Integer auditStatus) { this.auditStatus = auditStatus; }
    public Integer getAuditUserId() { return auditUserId; }
    public void setAuditUserId(Integer auditUserId) { this.auditUserId = auditUserId; }
    public String getAuditUsername() { return auditUsername; }
    public void setAuditUsername(String auditUsername) { this.auditUsername = auditUsername; }
    public Date getAuditTime() { return auditTime; }
    public void setAuditTime(Date auditTime) { this.auditTime = auditTime; }
    public Integer getIsDelete() { return isDelete; }
    public void setIsDelete(Integer isDelete) { this.isDelete = isDelete; }
    public Integer getCreator() { return creator; }
    public void setCreator(Integer creator) { this.creator = creator; }
    public Integer getUpdater() { return updater; }
    public void setUpdater(Integer updater) { this.updater = updater; }
    public String getCreatorName() { return creatorName; }
    public void setCreatorName(String creatorName) { this.creatorName = creatorName; }
    public String getUpdaterName() { return updaterName; }
    public void setUpdaterName(String updaterName) { this.updaterName = updaterName; }
    public Date getAddTime() { return addTime; }
    public void setAddTime(Date addTime) { this.addTime = addTime; }
    public Date getModTime() { return modTime; }
    public void setModTime(Date modTime) { this.modTime = modTime; }
    public Integer getContractStatus() { return contractStatus; }
    public void setContractStatus(Integer contractStatus) { this.contractStatus = contractStatus; }
    public Integer getPushDirection() { return pushDirection; }
    public void setPushDirection(Integer pushDirection) { this.pushDirection = pushDirection; }
    public String getSourceErp() { return sourceErp; }
    public void setSourceErp(String sourceErp) { this.sourceErp = sourceErp; }
} 