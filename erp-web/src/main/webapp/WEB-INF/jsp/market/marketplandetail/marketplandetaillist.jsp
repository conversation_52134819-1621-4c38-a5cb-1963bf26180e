<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="任务清单" scope="application" />
<%@ include file="../../common/common.jsp"%>
<style>
	ul.header li{
		width: 300px;
		margin-left: 50px !important;
	}
	ul.header li span{
		padding-top: 3px;
	}
	.hs {
		margin-left: 50px;
		margin-top: 10px;
		width: 500px;
		float: left;
	}

	.hs label {
		vertical-align: top;
	}

	.hs span {
		padding-top: 3px; /* 或者与label元素的值相同 */
		padding-left: 100px;
		vertical-align: top;
		display: block;
	}
</style>
	<div class="searchfunc">
		<form  id="search" method="get" action="<%=basePath%>market/sale/marketplandetail.do"	>
            <input type="hidden" name="sendMsg" id="sendMsg"  value="${sendMsg}"/>
            <input type="hidden" name="planId" id="planId"  value="${planId}"/>
			<ul class="header">
				<li>
					<label class="infor_name">任务名称：</label>
					<span>${marketPlan.planName}</span>
				</li>
				<li>
					<label class="infor_name">营销类型：</label>
					<span>
						<c:choose>
							<c:when test="${marketPlan.contentType eq 1}">
								商品推广
							</c:when>
							<c:when test="${marketPlan.contentType eq 2}">
								客户运营
							</c:when>
							<c:when test="${marketPlan.contentType eq 3}">
								品牌推广
							</c:when>
							<c:when test="${marketPlan.contentType eq 4}">
								用户运营
							</c:when>
						</c:choose>
					</span>
				</li>
				<li>
					<label class="infor_name">生效范围：</label>
					<span><fmt:formatDate value="${marketPlan.planStartTime}" pattern="yyyy/MM/dd"/>
							-
						   <fmt:formatDate value="${marketPlan.planEndTime}" pattern="yyyy/MM/dd"/>
					</span>
				</li>
				<li>
					<label class="infor_name">推广渠道：</label>
					<span>
						<c:choose>
							<c:when test="${marketPlan.promotionChannels eq 1}">
								1对1沟通
							</c:when>
							<c:when test="${marketPlan.promotionChannels eq 2}">
								${marketPlan.promotionChannels}
							</c:when>
						</c:choose>
					</span>
				</li>
				<li>
					<label class="infor_name">素材链接：</label>
					<span><c:if test="${marketPlan.posterUrl != null}"><a href="${marketPlan.posterUrl}" target="_blank" >${marketPlan.posterUrl}</a></c:if></span>
				</li>

			</ul>

			<div style="margin-bottom: 20px;">

				<c:if test="${scriptList!=null && null!=scriptList}">
					<c:forEach items="${scriptList}" var="script"  varStatus="status">
 							<div class="hs">
								<label class="infor_name">话术${status.index+1}：</label>
								<span>${script}</span>
							</div>
 					</c:forEach>
				</c:if>
			</div>
		</form>
	</div>
	<div class="content">
		<div class="fixdiv" style="    padding-top: 20px;">
			<div class="superdiv" style="width: auto;" >
				<table
					class="table"  >
					<thead>
						<tr>
							<th class="wid4" style="width:160px;">公司名称</th>
							<th class="wid4" style="width:80px;">推广优先级</th>
							<th class="wid4" style="width:120px;">最近联系人</th>
							<th class="wid4" style="width:120px;">最近联系电话</th>
							<th class="wid4" style="width:120px;">最近一次沟通时间</th>
							<th class="wid8" style="width:200px;">最近一次沟通内容</th>
							<th class="wid2" style="width:80px;">同期待办</th>
							<th class="wid2" style="width:60px;">是否触达
                                <select id="selectSendMsg" name="selectSendMsg" onchange="javascript:$('#sendMsg').val($(this).val());$('#search').submit();">
                                    <option value=""    >全部</option>
                                    <option value="0" <c:if test="${sendMsg !=null && sendMsg == 0}">selected="selected"</c:if>>未触达</option>
                                    <option value="1" <c:if test="${sendMsg !=null && sendMsg == 1}">selected="selected"</c:if>>已触达</option>
                                </select>
                            </th>
							<th class="wid2" style="width:60px;">操作</th>

						</tr>
					</thead>
					<tbody>
						<c:forEach var="item" items="${planTraderResponseVoList}" varStatus="num">
							<tr>
								<td align="left">
									<a class="addtitle" href="javascript:void(0);" tabtitle='{"num":"viewcustomer${item.traderId}", "link":"./trader/customer/baseinfo.do?traderId=${item.traderId}", "title":"客户信息"}'>${item.traderName}</a>
								</td>

								<td>
									${item.promotionPriority}
								</td>

								<td>
										${item.contact}
								</td>

								<td>
									<c:set var="phones" value="${fn:split(item.contactMob, '|')}" />
									<c:forEach var="phone" items="${phones}">
										<c:if test="${not empty phone}">
										<i class="icontel cursor-pointer" title="点击拨号" style="margin-top: 5px;"
										   onclick="callout('${phone}',${item.traderId},1,9,${planId},0);"></i>${phone}
										<br/>
										</c:if>
									</c:forEach>
									<%--${item.contactMob}--%>
								</td>

								<td>
									${item.lastCommTime}
								</td>

								<td>
									<ul class="communicatecontent ml0">
										<c:if test="${not empty item.tag }">
											<c:forEach items="${item.tag }" var="tag">
												<li class="bluetag" title="${tag.tagName}">${tag.tagName}</li>
											</c:forEach>
										</c:if>
									</ul>
									<div style="float: left">${item.lastCommContent}</div>
								</td>

								<td>
									<c:if test="${item.relatePlanList!=null }">
										<c:forEach items="${item.relatePlanList}" var="relate"  varStatus="status">

											<a class="addtitle" href="javascript:void(0);" tabtitle='{"num":"viewmarketplandetail${relate.id}", "link":"./market/sale/marketplandetail.do?planId=${relate.id}", "title":"任务清单"}'>${relate.planName}</a>
											<c:if test="${!status.last}">
												<c:out value="/ " />
											</c:if>
										</c:forEach>
									</c:if>
								</td>

								<td>
									<c:choose>
										<c:when test="${item.sendMsg eq 1}">
											是
										</c:when>
										<c:when test="${item.sendMsg eq 0}">
											否
										</c:when>
									</c:choose>
								</td>

								<td>
									<a href="javascript:void(0);" onclick="javascript:showAddCommu(${item.traderId},${item.traderCustomerId})" >添加沟通记录</a>
								</td>
							</tr>
						</c:forEach>

						<c:if test="${empty planTraderResponseVoList}">
			          		<tr>
			          			<td colspan="7">查询无结果！请尝试使用其他搜索条件。</td>
			          		</tr>
				       	</c:if>
					</tbody>
				</table>
			</div>
		</div>
       	<tags:page page="${page}"/>
	</div>
<script>
	function showAddCommu(traderId,traderCustomerId){
		layer.open({
			type: 2,
			shadeClose: false,
			closeBtn: true,
			area: ['880px','545px'],
			title: false,
			content: ['/trader/customer/addcommunicate.do?pop=Y&traderId='+traderId+'&traderCustomerId='+traderCustomerId],
			success: function(layero, index) {
				//layer.iframeAuto(index);
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	}


</script>
<%@ include file="../../common/footer.jsp"%>
