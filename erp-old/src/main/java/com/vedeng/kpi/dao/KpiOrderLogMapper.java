package com.vedeng.kpi.dao;

import com.vedeng.kpi.model.DO.KpiOrderLogDo;
import com.vedeng.kpi.model.DTO.KpiDataQueryDto;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

public interface KpiOrderLogMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(KpiOrderLogDo record);

    int insertSelective(KpiOrderLogDo record);

    KpiOrderLogDo selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(KpiOrderLogDo record);

    int updateByPrimaryKey(KpiOrderLogDo record);

    List<KpiDataQueryDto> getGroupOrderAmountMonth(KpiDataQueryDto query);

    List<KpiDataQueryDto> getGroupCoCustomers(KpiDataQueryDto query);

    List<KpiDataQueryDto> getGroupChanceTrans(KpiDataQueryDto query);

    List<KpiDataQueryDto> getGroupBdNewCustomers(KpiDataQueryDto query);

    List<KpiDataQueryDto> getGroupBdTraderMonth(KpiDataQueryDto query);

    List<KpiOrderLogDo> selectUnUsedLogs(KpiDataQueryDto query);

    List<KpiDataQueryDto> getKpiDetail(KpiDataQueryDto query);

    List<KpiOrderLogDo> getKpiLogByOrderNoAndOperation(@Param("orderNo") String orderNo, @Param("operation") Integer operation);

    List<KpiOrderLogDo> getKpiLogByAfterSalesNo(@Param("afterSalesNo") String afterSalesNo);

    List<KpiDataQueryDto> getKpiBdCoDetail(KpiDataQueryDto query);

    List<KpiDataQueryDto> getKpiBdLostDetail(KpiDataQueryDto query);

    List<KpiDataQueryDto> getUserCoCustomers(KpiDataQueryDto query);

    List<KpiDataQueryDto> getUserCoCustomersDetail(KpiDataQueryDto query);

    List<KpiDataQueryDto> getTraderLastTimes(KpiDataQueryDto query);

    List<KpiDataQueryDto> getSendNotPaid(KpiDataQueryDto query);

    List<KpiDataQueryDto> getPaidNotKpi(KpiDataQueryDto query);

    int updateLogUsed(KpiDataQueryDto query);

    List<KpiDataQueryDto> getFirstChanceLog(KpiDataQueryDto query);

    List<KpiOrderLogDo> getKpiLogByPage(@Param("offset") Integer offset, @Param("limit") Integer limit);

    List<KpiOrderLogDo> getKpiLogOfAfterSalesByPage(@Param("offset") Integer offset, @Param("limit") Integer limit);

    void updateChanceTransferFlag2Null(String orderNo);

    void updateChanceTransferFlagByOrderNo(@Param("saleorderNo") String saleorderNo, @Param("operation") Integer operation, @Param("flag") Integer flag);

    void updateKpiDateByAfterSalesNo(@Param("afterSalesNo") String afterSalesNo, @Param("kpiDate") Date kpiDate);

    List<KpiOrderLogDo> getKpiOrderOfSaleorderNo(String saleorderNo);

    Integer getKpiOrderLogNum(@Param("kpiDate") LocalDate kpiDate);

    Integer getKpiDailyCount(@Param("kpiDate") LocalDate kpiDate);
}