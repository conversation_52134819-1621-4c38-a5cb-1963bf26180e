<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增订单" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/order/orderstream/customer_info.js?rnd=${resourceVersionKey}"></script>

<div class="content mt10 ">
	<div class="parts">
		<div class="title-container title-container-blue">
			<div class="table-title nobor">客户信息</div>
		</div>
		<div class="formpublic formpublic1" id="updateTerminalInfo">
			<div>
					<form method="post" id="search"
						  action="${pageContext.request.contextPath}/trader/customer/searchCustomerList.do?indexId=${indexId}">
						<div>
							<!-- 初始模式 -->
							<ul class="searchTable visible">
								<li>
									<div class="infor_name  infor_name6 ">
										<span>*</span> <label>客户名称</label>
									</div>

									<div class="f_left table-larger" style="width: 80%">
										<div class="mb10">
											<input type="text" class="input-larger mr5"
												name="searchTraderName" id="searchTraderName"
												value="${traderCustomerVo.searchTraderName}">
											<input type="hidden" name="belongPlatform" value="${traderCustomerVo.belongPlatform}">
											<span
												class="bt-bg-style bt-small bg-light-blue" onclick="search();"
												id="searchError">搜索</span>

										</div>
									</div>
									<div>
										<table
											class="table table-bordered table-striped table-condensed table-centered mb10">
											<thead>

												<th>客户名称</th>
												<th>地区</th>
												<th>创建时间</th>
												<th>归属销售</th>
												<th class="table-smallest6">选择</th>
											</thead>
											<tbody>

												<c:if test="${not empty searchCustomerList}">
													<c:forEach items="${searchCustomerList}" var="list"
														varStatus="status">
														<tr>
															<td>${list.traderName}</td>
															<td>${list.address}</td>
															<td><date:date value="${list.addTime}" /></td>
															<td>${list.personal}</td>
															<td width="5%" style="text-align: center"><a
																href="javaScript:void(0)"
																onclick="selectCustomer('${indexId}','${list.traderId}','${list.traderName}','${list.traderCustomerId}','${list.customerType}', '${list.customerNature}',
																		${list.belongPlatform})">选择</a>
															</td>
														</tr>
													</c:forEach>
												</c:if>
												<c:if test="${empty searchCustomerList}">
													<tr>
														<td colspan="5">查询无结果！请尝试使用其他关键词搜索。</td>
													</tr>
												</c:if>
											</tbody>
										</table>
									</div>
								</li>
								<li class="visible"><tags:page page="${page}" />
									<div class="clear"></div></li>
								<div class="clear"></div>
							</ul>
							<!-- 搜索最后结果lastResult -->
						</div>
					</form>
			</div>
		</div>
		<div  id="desc_div" class="formpublic formpublic1 none">
			<form method="post" id="addForm" action="${pageContext.request.contextPath}/order/saleorder/saveaddsaleorderinfo.do">
				<ul class="payplan">
					<li>
						<div class="infor_name infor_name72">
							<span>*</span>
							<label>客户名称</label>
						</div>
						<div class="f_left inputfloat">
							<span class=" mr10 mt3" id="trader_name_span_1"></span>
							<input type="hidden" name="traderId" id="trader_id_1" value="">
							<input type="hidden" name="traderName" id="trader_name_1" value="">
							<input type="hidden" name="customerType" id="customer_type_1" value="">
							<input type="hidden" name="customerNature" id="customer_nature_1" value="">
							<span class="bt-bg-style bt-small bg-light-blue pop-new-data" layerParams='{"width":"800px","height":"300px","title":"编辑收货信息","link":"${pageContext.request.contextPath}/trader/customer/searchCustomerList.do?indexId=1&searchTraderName="}'>重新搜索</span>
						</div>
					</li>
					<li>
						<div class="infor_name infor_name72 mt0">
							<span>*</span>
							<label>客户类型</label>
						</div>
						<div class="f_left inputfloat" id="customer_type_nature_div">
						</div>
					</li>
					<li>
						<div class="infor_name infor_name72">
							<span>*</span>
							<label>联系人</label>
						</div>
						<div class="f_left inputfloat">
							<select class="input-xx" id="trader_contact_1" name="traderContactId">
								<option value="0">请选择</option>

							</select>
							<input type="hidden" name="traderContactName">
							<input type="hidden" name="traderContactTelephone">
							<input type="hidden" name="traderContactMobile">
							<div id="traderContactIdMsg" style="clear:both"></div>
						</div>
					</li>
					<li>
						<div class="infor_name infor_name72">
							<span>*</span>
							<label>联系地址</label>
						</div>
						<div class="f_left inputfloat">
							<select class="input-xx" id="address_1" name="traderAddressId">
								<option value="0">请选择</option>
							</select>
							<input type="hidden" name="traderArea">
							<input type="hidden" name="traderAddress">
							<div id="traderAddressIdMsg" style="clear:both"></div>
						</div>
					</li>
				</ul>
				<div class="add-tijiao tcenter mt10">
					<!-- <input type="hidden" id="extraType" name="extraType" value="add"> -->
					<button type="button" class="bt-bg-style bg-deep-green" onclick="addSubmit();">下一步</button>
				</div>
			</form>
		</div>
	</div>
</div>
</body>
</html>
