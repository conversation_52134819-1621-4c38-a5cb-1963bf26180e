package com.wms.service.processor;

import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.core.utils.numgenerator.BillNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.logistics.dao.WarehouseGoodsOperateLogMapper;
import com.vedeng.logistics.dao.WarehouseGoodsOperateLogVirtualMapper;
import com.vedeng.logistics.eums.WarehouseOutInSourceEnum;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.logistics.model.WarehouseGoodsOutIn;
import com.vedeng.logistics.model.WarehouseGoodsOutInItem;
import com.vedeng.logistics.service.WarehouseGoodsOperateLogService;
import com.vedeng.logistics.service.WarehouseGoodsOutService;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.order.model.Saleorder;
import com.vedeng.stock.api.stock.dto.StockInfoDto;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.model.vo.UrlToPdfParam;
import com.vedeng.system.service.OssUtilsService;
import com.wms.constant.LogicalEnum;
import com.wms.dao.WmsLogicalOrdergoodsMapper;
import com.wms.dto.OutputDto;
import com.wms.dto.OutputGoodDto;
import com.wms.dto.StockCalculateDto;
import com.wms.model.po.WmsLogicalOrdergoods;
import com.wms.service.util.GlobalThreadPool;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 抽象的出库单通用处理器
 */
@Service
public abstract class AbstractOutputOrderProcessor extends AbstractInputOrOutputProcessor<OutputDto> {

    private Logger logger = LoggerFactory.getLogger(AbstractOutputOrderProcessor.class);

    @Resource
    private WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

    @Resource
    private WarehouseGoodsOperateLogVirtualMapper warehouseGoodsOperateLogVirtualMapper;

    @Resource
    private WmsLogicalOrdergoodsMapper wmsLogicalOrdergoodsMapper;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Autowired
    private OssUtilsService ossUtilsService;

    @Value("${html2Pdf.domain}")
    private String html2PdfDomain;

    @Resource
    private AttachmentMapper attachmentMapper;

    @Autowired
    private WarehouseGoodsOutService warehouseGoodsOutService;

    @Value("${oss_http}")
    private String ossHttp;

    @Value("${erp_url}")
    private String erpUrl;

    @Value("${wms_client_key}")
    private String wmsClientKey;

    @Resource
    private WarehouseGoodsOperateLogService warehouseGoodsOperateLogService;

    @Override
    protected String getBusinessKey(OutputDto outputDto) {
        return outputDto.getOrderNo();
    }

    @Override
    protected boolean needIdempotentValidator() {
        return true;
    }

    @Override
    protected void insertOrUpdateLogicalOrderGoods(OutputDto outputDto) throws Exception {
        for (OutputGoodDto outputGoodDto : outputDto.getDetails()) {
            WmsLogicalOrdergoods queryCon = new WmsLogicalOrdergoods();

            int relateId = getRelateId(outputGoodDto);
            if (relateId == 0) {
                throw new Exception("出库处理器 关联Relateid为空,单号" + outputDto.getSOReference1() + "goodsInfo:" + outputGoodDto.toString());
            }
            queryCon.setSku(outputGoodDto.getSKU());
            queryCon.setOperateType(getWmsLogicalOperateType());
            queryCon.setRelatedId(relateId);
            queryCon.setLogicalWarehouseId(LogicalEnum.getLogicalWarehouseIdByCode(outputGoodDto.getLotAtt08()));
            WmsLogicalOrdergoods wmsLogicalOrdergoods = wmsLogicalOrdergoodsMapper.getOutOrderByCon(queryCon);

            if (wmsLogicalOrdergoods != null && wmsLogicalOrdergoods.getOccupyNum() > 0) {
                WmsLogicalOrdergoods updateOrder = new WmsLogicalOrdergoods();
                updateOrder.setLogicalOrderGoodsId(wmsLogicalOrdergoods.getLogicalOrderGoodsId());

                //占用数量减少
                updateOrder.setOccupyNum(wmsLogicalOrdergoods.getOccupyNum() - outputGoodDto.getQtyShipped().intValue());
                //发货数量增加
                updateOrder.setDeliveryNum(wmsLogicalOrdergoods.getDeliveryNum() + outputGoodDto.getQtyShipped().intValue());
                updateOrder.setModeTime(new Date());

                logger.info("WMS单据出库，更新逻辑订单表数据:" + outputDto.getSOReference1() + "  " + JSON.toJSONString(updateOrder));
                wmsLogicalOrdergoodsMapper.updateByPrimaryKeySelective(updateOrder);
            } else {
                throw new Exception("出库处理器 未查到选择逻辑仓数据,单号" + outputDto.getSOReference1() + "goodsInfo:" + outputGoodDto.toString());
            }
        }
    }

    @Override
    protected void insertOrUpdateWarehouseLog(OutputDto orderDto) throws Exception {
        long currentTimeMillis = System.currentTimeMillis();
        //出入库日志主表新增
        int operateType = getOperateType(orderDto);
        WarehouseGoodsOutIn warehouse=new WarehouseGoodsOutIn();

        warehouse.setRelateNo(orderDto.getSOReference1());
        warehouse.setWmsNo(orderDto.getOrderNo());
        warehouse.setOutInType(operateType);
        warehouse.setSource(WarehouseOutInSourceEnum.NORMAL_DELIVERY.getSource());
        warehouse.setCreator(2);
        warehouse.setCreator(2);
        WarehouseGoodsOutIn warehouseGoodsOutIn=warehouseGoodsOutService.insertWarehouseGoodsOutIN(warehouse);
        List<OutputGoodDto> details = orderDto.getDetails();
        List<WarehouseGoodsOutInItem> warehouseGoodsOutInItemList=new ArrayList<>();
        for (OutputGoodDto outputGoodDto : details) {

            if (StringUtil.isBlank(outputGoodDto.getLotAtt11())) {
                throw new RuntimeException(orderDto.getSOReference1() + "出库类型没有贝登批次码,消费失败");
            }

            int realateId = getRelateId(outputGoodDto);

            WarehouseGoodsOperateLog queryCon = new WarehouseGoodsOperateLog();
            queryCon.setLogicalWarehouseId(LogicalEnum.getLogicalWarehouseIdByCode(outputGoodDto.getLotAtt08()));
            queryCon.setVedengBatchNumer(outputGoodDto.getLotAtt11());
            queryCon.setGoodsId(Integer.valueOf(outputGoodDto.getSKU().substring(1)));

            List<WarehouseGoodsOperateLog> availableInputLogs = new ArrayList<>();
            availableInputLogs = warehouseGoodsOperateLogMapper.getAvailableLogicalGoods(queryCon);
            if (CollectionUtils.isEmpty(availableInputLogs)) {
                logger.error("出库错误 出库单号:{},查询入库记录条件:{}",
                        orderDto.getSOReference1(), outputGoodDto.getLotAtt11() + "," + outputGoodDto.getLotAtt08() + "," + outputGoodDto.getSKU());
                throw new Exception("出库错误 出库单号:" + orderDto.getSOReference1() + "未查询到贝登批次码" + outputGoodDto.getLotAtt11() + "," + outputGoodDto.getLotAtt08() + "," + outputGoodDto.getSKU());
            }

            int outNum = outputGoodDto.getQtyShipped().intValue();
            Integer erpStocknum = availableInputLogs.stream().collect(Collectors.summingInt(WarehouseGoodsOperateLog::getLastStockNum));
            if (outNum > erpStocknum) {
                logger.error("出库错误 erp在库数量小于出库数量 出库单号:{},erpNum:{},wmsNum:{},查询入库记录条件:{}",
                        orderDto.getSOReference1(), erpStocknum, outNum, outputGoodDto.getLotAtt11() + "," + outputGoodDto.getLotAtt08() + "," + outputGoodDto.getSKU());
                throw new Exception("出库错误 erp在库数量小于出库数量 出库单号:" + orderDto.getSOReference1() + "查询到贝登批次码" + outputGoodDto.getLotAtt11() + "," + outputGoodDto.getLotAtt08() + "," + outputGoodDto.getSKU());
            }
            int snFlag = 0;
            //允许的sn码遍历的最大限制
            int nowMax = 0;
            for (WarehouseGoodsOperateLog inlog : availableInputLogs) {
                if (outNum == 0) {
                    break;
                }
                Integer lastStockNum = inlog.getLastStockNum();
                //出库数量
                if (outNum >= lastStockNum) {
                    inlog.setNum(-lastStockNum);
                    outNum = outNum - lastStockNum;
                } else if (outNum < lastStockNum) {
                    inlog.setNum(-outNum);
                    outNum = 0;
                }
                inlog.setModTime(currentTimeMillis);
//                logger.info("更新入库记录  单号:{},inlog:{}",orderDto.getSOReference1(), JSON.toJSONString(inlog));
                //更新入库条码字段
                warehouseGoodsOperateLogMapper.updateOutIsUseAndLastStockNumById(inlog);
                inlog.setBarcodeFactory("");
                //批次码
                inlog.setBatchNumber("NO-LOT".equals(outputGoodDto.getLotAtt04()) ? "" : outputGoodDto.getLotAtt04());
                //生产日期
                inlog.setProductDate(DateUtil.convertLong(outputGoodDto.getLotAtt01() + " 00:00:00", "yyyy-MM-dd HH:mm:ss"));
                //效期
                inlog.setExpirationDate(DateUtil.convertLong(outputGoodDto.getLotAtt02() + " 00:00:00", "yyyy-MM-dd HH:mm:ss"));
                inlog.setSterilizationBatchNo(outputGoodDto.getLotAtt05());
                inlog.setOperateType(getOperateType(orderDto));
                inlog.setRelatedId(realateId);
                inlog.setAddTime(currentTimeMillis);
                inlog.setModTime(currentTimeMillis);
                inlog.setCompanyId(1);
                inlog.setIsEnable(1);
                inlog.setLastStockNum(0);
                inlog.setUpdater(2);
                inlog.setCreator(2);
                inlog.setLogType(1);
                inlog.setIsUse(2);
                inlog.setComments(orderDto.getSOReference1() + "/" + orderDto.getOrderNo());
                logger.info("专项发货SKU回传需关联VP单号：{}", outputGoodDto.getLotAtt07());
                inlog.setDedicatedBuyorderNo(outputGoodDto.getLotAtt07());

//                VDERP-6355出库单回调根据SN码新增出库记录
                if (outputGoodDto.getUserDefine3() != null && !outputGoodDto.getUserDefine3().equals("")) {
                    int wmsOutNum = outputGoodDto.getQtyShipped().intValue();
                    String snArray[] = outputGoodDto.getUserDefine3().split(",");
                    //SN码数超过发货数量,抛出异常
                    if (snArray.length > wmsOutNum) {
                        throw new Exception("出库错误 SN码数量大于实际出库数量，与实际情况不符，烦请重新确认后重新触发回传 出库单号:" + orderDto.getSOReference1() + "查询到贝登批次码" + outputGoodDto.getLotAtt11() + "," + outputGoodDto.getLotAtt08() + "," + outputGoodDto.getSKU());
                    } else {
                        //SN码数少于发货数量，则将剩余数量合为一条出库记录
                        if (snFlag == 0) {
                            //确保只计算一次
                            if (snArray.length < wmsOutNum) {
                                //按照SN码划分后，一个SN码一条出库记录
                                inlog.setNum(snArray.length - wmsOutNum);
                                //新增退货出库记录
                                logger.info("存入出入库日志表数据：{}", JSON.toJSONString(inlog));
                                warehouseGoodsOperateLogService.insertSelective(inlog);
                                //出入库明细表保存T_WAREHOUSE_GOODS_OUT_IN_ITEM
                                List<WarehouseGoodsOutInItem> warehouseGoodsOutInItems = saveWarehouseGoodsOutInItem(realateId, operateType, inlog, warehouseGoodsOutIn);
                                warehouseGoodsOutInItemList.addAll(warehouseGoodsOutInItems);
                            }
                        }
                        //根据sn码分割，新增出库记录，不足的部分直接合并到一起出库
                        nowMax += lastStockNum;//当前允许的sn码遍历的最大限制
                        for (; snFlag < snArray.length && snFlag < nowMax; snFlag++) {
                            //防止重复循环
                            //按照SN码划分后，一个SN码一条出库记录
                            inlog.setNum(-1);
                            //SN码
                            inlog.setBarcodeFactory(snArray[snFlag]);
                            //新增退货出库记录
                            logger.info("存入出入库日志表数据：{}", JSON.toJSONString(inlog));
                            warehouseGoodsOperateLogService.insertSelective(inlog);
                            //出入库明细表T_WAREHOUSE_GOODS_OUT_IN_ITEM
                            WarehouseGoodsOutInItem warehouseGoodsOutInItem =warehouseGoodsOutService.insertWarehouseGoodsOutInItem(inlog,warehouseGoodsOutIn);
                            warehouseGoodsOutInItemList.add(warehouseGoodsOutInItem);
                        }
                    }
                } else {
                    logger.info("存入出入库日志表数据：{}", JSON.toJSONString(inlog));
                    warehouseGoodsOperateLogService.insertSelective(inlog);
                    //出入库明细表保存T_WAREHOUSE_GOODS_OUT_IN_ITEM
                    List<WarehouseGoodsOutInItem> warehouseGoodsOutInItems = saveWarehouseGoodsOutInItem(realateId, operateType, inlog, warehouseGoodsOutIn);
                    warehouseGoodsOutInItemList.addAll(warehouseGoodsOutInItems);
                }
            }
        }
        try {
            warehouseGoodsOutService.createWarehouseGoodsOutReport(warehouseGoodsOutIn,warehouseGoodsOutInItemList);
        }catch (Exception e){
            logger.error("生成出库复核单失败",e);
        }
    }

    @Override
    protected void synchronizeStockData(OutputDto outputOrderDto) throws Exception {
        List<StockCalculateDto> stockCalculateList = new ArrayList<>();

        outputOrderDto.getDetails().stream().forEach(outputOrderGoodsDto -> {
            StockCalculateDto stockCalculateDto = new StockCalculateDto();
            stockCalculateDto.setSku(outputOrderGoodsDto.getSKU());
            stockCalculateDto.setLogicalWarehouseId(LogicalEnum.getLogicalWarehouseIdByCode(outputOrderGoodsDto.getLotAtt08()));
            stockCalculateDto.setStockNum(outputOrderGoodsDto.getQtyShipped().intValue());
            stockCalculateDto.setOccupyNum(outputOrderGoodsDto.getQtyShipped().intValue());
            stockCalculateList.add(stockCalculateDto);
        });

        //库存策略计算
        StockInfoDto stockInfoDto = new StockInfoDto();
        stockInfoDto.setRelatedNo(outputOrderDto.getSOReference1());
        stockInfoDto.setWarehouseStockList(getWarehouseStockList(stockCalculateList));

        logger.info("单据出库，更新库存服务数据:" + JSON.toJSONString(stockInfoDto));
        //更新库存服务
        warehouseStockService.updateStockInfo(stockInfoDto);
    }

    protected abstract int getOperateType(OutputDto requestBean);

    protected abstract int getWmsLogicalOperateType();

    protected abstract int getRelateId(OutputGoodDto outputGoodDto);

    protected abstract List<WarehouseDto> getWarehouseStockList(List<StockCalculateDto> stockCalculateList);

    /**
     * 生成带价格和不带价格出库单
     */
    public void generatePrintOutOrder(Saleorder saleorder) {
        try {
            Map<String, String> printOutOrderUrl = getPrintOutOrderUrl(saleorder);

            if (Objects.nonNull(printOutOrderUrl)){
                //出库单带价格信息上传OSS
                GlobalThreadPool.submitMessage(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            String uuid = UUID.randomUUID().toString().replace("-", "");
                            logger.info("开始执行generatePDF,uuid:{}",uuid);
                            generatePDF(saleorder, printOutOrderUrl.get("showPrice"),Boolean.TRUE);
                            logger.info("结束执行generatePDF,uuid:{}",uuid);
                        } catch (Exception e) {
                            logger.error("html2PdfUrl error", e);
                        }
                    }
                });
                //出库单不带价格信息上传OSS
                GlobalThreadPool.submitMessage(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            String uuid = UUID.randomUUID().toString().replace("-", "");
                            logger.info("开始执行generatePDF,uuid:{}",uuid);
                            generatePDF(saleorder, printOutOrderUrl.get("notShowPrice"),Boolean.FALSE);
                            logger.info("结束执行generatePDF,uuid:{}",uuid);
                        } catch (Exception e) {
                            logger.error("html2PdfUrl error", e);
                        }
                    }
                });
            }
        } catch (Exception e) {
            logger.error("生成带价格和不带价格出库单失败！{}",JSON.toJSONString(saleorder),e);
        }


    }

    /**
     * 出库单信息上传OSS
     * @param order
     * @param printOutOrderUrl
     * @param priceFlag
     */
    private void generatePDF(Saleorder order, String printOutOrderUrl,Boolean priceFlag) {
        if (StringUtils.isBlank(printOutOrderUrl)){
            return;
        }
        printOutOrderUrl = printOutOrderUrl + "&wms_client_key=" + wmsClientKey + "&wmsClientUserName=vedeng";
        logger.info("出库单信息上传OSS start saleOrderNo:{},printOutOrderUrl:{}", order.getSaleorderNo(), printOutOrderUrl);
        String html2PdfUrl = html2PdfDomain + "/api/render";
        UrlToPdfParam urlToPdfParam = new UrlToPdfParam();
        urlToPdfParam.setUrl(printOutOrderUrl);
        UrlToPdfParam.Pdf pdf = new UrlToPdfParam.Pdf();
        UrlToPdfParam.Pdf.PdfMargin margin = new UrlToPdfParam.Pdf.PdfMargin("1cm", "1cm", "1cm", "1cm");
        pdf.setMargin(margin);
        urlToPdfParam.setPdf(pdf);
        String ossUrl = ossUtilsService.migrateFile2Oss(html2PdfUrl, "pdf", "出库单" + order.getSaleorderNo(), urlToPdfParam);
        if (StringUtil.isBlank(ossUrl)) {
            logger.warn("html2PdfUrl warn，saleOrderNo:{}", order.getSaleorderNo());
            return;
        }
        logger.info("出库单信息上传成功 saleOrderNo:{},ossUrl:{}", order.getSaleorderNo(), ossUrl);
        Attachment attachment = new Attachment();
        attachment.setAttachmentType(ErpConst.OUTBOUND_ORDER_TYPE);
        if (priceFlag){
            attachment.setAttachmentFunction(ErpConst.OUTBOUND_ORDER_FUNCTION_PRICE);
        }else {
            attachment.setAttachmentFunction(ErpConst.OUTBOUND_ORDER_FUNCTION_NO_PRICE);
        }
        attachment.setRelatedId(order.getSaleorderId());
        String domainAndUri = ossUrl.split(ossHttp)[1];
        int domainIndex = domainAndUri.indexOf("/");
        String domain = domainAndUri.substring(0, domainIndex);
        String uri = domainAndUri.substring(domainIndex);
        attachment.setDomain(domain);
        attachment.setUri(uri);
        attachment.setOssResourceId(uri);
        attachment.setName(order.getSaleorderNo());
        attachment.setAddTime(System.currentTimeMillis());
        logger.info("保存出库单路径信息 orderNo:{},attachment:{}",
                order.getSaleorderNo(), JSON.toJSONString(attachment));
        attachmentMapper.insertSelective(attachment);
    }

    /**
     * 获取出库单页面url 带价格和不带价格
     * @param saleorder
     * @return
     */
    private Map<String,String> getPrintOutOrderUrl(Saleorder saleorder) {
        StringBuffer path = new StringBuffer(erpUrl)
                .append("/warehouse/warehousesout/printOutOrder.do?")
                .append("orderId=" + saleorder.getSaleorderId())
                .append("&printerButtonFlag=no")
                .append("&bussinessType=496&bussinessNo=" + saleorder.getSaleorderNo())
                .append("&type_f=");

        //获取带价格
        String printOutType = warehouseStockService.getPrintOutType(saleorder, Boolean.TRUE);

        //获取不带价格
        String printOutTypeNoPrice = warehouseStockService.getPrintOutType(saleorder, Boolean.FALSE);

        if(StringUtils.isBlank(printOutType) && StringUtils.isBlank(printOutTypeNoPrice)){
            logger.info("前台生成出库单失败，无匹配的出库单类型,{}",JSON.toJSONString(saleorder));
            return null;
        }

        Map<String, String> printOutQueryParms = warehouseStockService.getQueryParmsWdlIdsAndDirect(saleorder);

        String wdlIds = printOutQueryParms.get("wdlIds");
        String directWlogIds = printOutQueryParms.get("directWlogIds");

        if (StringUtils.isBlank(wdlIds) && StringUtils.isBlank(directWlogIds)){
            logger.info("前台生成出库单失败，无出库记录,{}",JSON.toJSONString(saleorder));
            return null;
        }

        HashMap<String, String> pathMap = new HashMap<>();

        if(StringUtils.isNotBlank(printOutType)){
            StringBuilder showPricePath = new StringBuilder(path);
            String encode1 = URLUtil.encode(wdlIds + "#" + printOutType);
            String encode2 = URLUtil.encode(directWlogIds + "#" + printOutType);
            pathMap.put("showPrice",showPricePath.append(printOutType)
                    .append("&wdlIds=")
                    .append(encode1)
                    .append("&directWlogIds=")
                    .append(encode2)
                    .toString());
        }

        if(StringUtils.isNotBlank(printOutTypeNoPrice)){
            StringBuilder notShowPricePath = new StringBuilder(path);
            String encode3 = URLUtil.encode(wdlIds + "#" + printOutTypeNoPrice);
            String encode4 = URLUtil.encode(directWlogIds + "#" + printOutTypeNoPrice);
            pathMap.put("notShowPrice",notShowPricePath.append(printOutTypeNoPrice)
                    .append("&wdlIds=")
                    .append(encode3)
                    .append("&directWlogIds=")
                    .append(encode4)
                    .toString());
        }

        return pathMap;
    }

    /**
     * 出入库明细入库（销售出库安调添加拆行逻辑）
     *
     * @param realateId
     * @param operateType
     * @return
     */
    private List<WarehouseGoodsOutInItem> saveWarehouseGoodsOutInItem(int realateId, int operateType, WarehouseGoodsOperateLog inlog, WarehouseGoodsOutIn warehouseGoodsOutIn) {
        List<WarehouseGoodsOutInItem> warehouseGoodsOutInItemList = new ArrayList<>();
        //判断是否满足销售出库安调拆行条件
        Boolean haveInstallationBreakLine = warehouseGoodsOutService.haveInstallationBreakLine(realateId, operateType, inlog);
        //判断是否满足销售换货出库安调拆行条件
        Boolean haveInstallationExchangeBreakLine = warehouseGoodsOutService.haveInstallationExchangeBreakLine(realateId, operateType, inlog);
        int itemNum = inlog.getNum() < 0 ? -1 : 1;
        if (Boolean.TRUE.equals(haveInstallationBreakLine) || Boolean.TRUE.equals(haveInstallationExchangeBreakLine)) {

            int num = Math.abs(inlog.getNum());
            //分条件打不同的日志
            if (Boolean.TRUE.equals(haveInstallationBreakLine)) {
                logger.info("满足销售安调拆行条件开始拆行realateId：{}，num：{}", realateId, num);
            } else {
                logger.info("满足销售换货安调拆行条件开始拆行realateId：{}，num：{}", realateId, num);
            }
            for (int i = 0; i < num; i++) {
                inlog.setNum(itemNum);
                //出入库明细表T_WAREHOUSE_GOODS_OUT_IN_ITEM写入
                WarehouseGoodsOutInItem warehouseGoodsOutInItem = warehouseGoodsOutService.insertWarehouseGoodsOutInItem(inlog, warehouseGoodsOutIn);
                warehouseGoodsOutInItemList.add(warehouseGoodsOutInItem);
            }
        } else {
            //出入库明细表T_WAREHOUSE_GOODS_OUT_IN_ITEM写入
            WarehouseGoodsOutInItem warehouseGoodsOutInItem = warehouseGoodsOutService.insertWarehouseGoodsOutInItem(inlog, warehouseGoodsOutIn);
            warehouseGoodsOutInItemList.add(warehouseGoodsOutInItem);
        }
        return warehouseGoodsOutInItemList;
    }
}
