@import (less) '../common/mixin.css';

.file-dialog-top {
    display: flex;
    align-items: center;

    .btn {
        margin-right: 10px;
    }

    > span {
        color: #999;
    }
}

.file-dialog-panel {
    position: relative;
    height: 100%;
    overflow: hidden;

    .panel-wrap {
        height: calc(100% - 56px);
        overflow-y: auto;
        .scrollbar;

        &.has-fixed-btn {
            height: calc(100% - 56px - 53px);
        }

        &.dialog-inner {
            max-height: 510px;
            margin-top: 20px;
        }

        .file-list {

            .file-name {
                > img {
                    width: 16px;
                    height: 20px;
                    margin-right: 10px;
                }
            }

            .file-deal-btn {
                .btn {
                    position: relative;
                    color: #09f;
                    cursor: pointer;
                    margin-right: 10px;
                    padding-right: 10px;

                    &::after {
                        content: "";
                        display: block;
                        width: 2px;
                        height: 15px;
                        background: #ededed;
                        position: absolute;
                        right: 0;
                        top: 3px;
                    }

                    &:hover {
                        color: #f60;
                    }

                    &:last-child {
                        margin-right: 0;
                        padding-right: 0;

                        &::after {display: none;
                        }
                    }
                }
            }
        }
        .null-data {
            padding: 0;
            display: flex;
            flex-direction: column;
            align-items: center;

            .empty-img {
                width: 110px;
                height: 95px;
                background: url('../../image/common/no-data2.svg') no-repeat;
                background-size: 110px 95px;
                background-position: center;
                margin-bottom: 20px;
            }
        }
    }
}