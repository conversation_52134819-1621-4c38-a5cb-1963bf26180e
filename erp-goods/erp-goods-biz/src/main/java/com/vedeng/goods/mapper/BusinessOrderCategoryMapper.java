package com.vedeng.goods.mapper;

import com.vedeng.goods.domain.entity.BusinessOrderCategoryEntity;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface BusinessOrderCategoryMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(BusinessOrderCategoryEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(BusinessOrderCategoryEntity record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    BusinessOrderCategoryEntity selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(BusinessOrderCategoryEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(BusinessOrderCategoryEntity record);

    int updateBatchSelective(List<BusinessOrderCategoryEntity> list);

    int batchInsert(@Param("list") List<BusinessOrderCategoryEntity> list);

    List<BusinessOrderCategoryEntity> findByBusinessIdAndBusinessType(@Param("businessId")Integer businessId,@Param("businessType")Integer businessType);

    /**
     * 根据业务ID和业务类型删除记录
     *
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @return 删除的记录数
     */
    int deleteByBusinessIdAndBusinessType(@Param("businessId")Integer businessId,@Param("businessType")Integer businessType);
}