Vue.component('ui-table', {
    template: `<div class="vd-ui-table-wrap" v-if="!isLoading">
        <div class="vd-ui-table-container" :class="{'vd-table-width-border': widthBorder, 'vd-table-border': border, 'vd-table-border-nofix': !autoScroll && !containerHeight}" ref="container">
            <div
                class="vd-ui-table-header"
                v-if="scroll || isHeaderFixed"
                :class="{
                    'vd-ui-wrap-is-left': scroll && isLeftFixed,
                    'vd-ui-wrap-is-right': scroll && isRightFixed,
                    'vd-ui-wrap-right-scroll-fixed': scroll && isRightFixed && scrollBarWidth,
                    'header-fixed': isHeaderFixed
                }"
                :style="isHeaderFixed ? 'width:' + containerWidth + 'px;top:' + fixedTop + 'px;' : ''"
                ref="header"
            >
                <table
                    class="vd-ui-table"
                    :class="{
                        'vd-ui-table-left-fixed': scroll && isLeftFixed,
                        'vd-ui-table-right-fixed': scroll && isRightFixed,
                    }"
                    :style="tableStyle"
                    ref="table"
                >
                    <colgroup>
                        <template v-if="canChoose">
                            <col :width="customList ? '36px' : '56px'"/>
                        </template>
                        <template v-for="(item, index) in headers">
                            <col :key="index" :width="item.width || ''" />
                        </template>
                        <template v-if="scrollBarWidth">
                            <col :width="scrollBarWidth + 'px'" />
                        </template>
                    </colgroup>
                    <thead>
                        <tr class="vd-ui-tr">
                            <th class="vd-ui-th" :class="{'sticky-item': leftFixedNumber, 'last-fixed': leftFixedNumber == 1}" v-if="canChoose">
                                <div class="vd-ui-checkbox-wrap">
                                    <ui-checkbox :checked.sync="isSelectedAll" @change="selectAllChange"></ui-checkbox>
                                </div>
                            </th>
                            <template v-for="(item, index) in headers">
                                <th
                                    class="vd-ui-th"
                                    :key="index"
                                    :class="{
                                        'vd-ui-th-bar-prev':
                                            scrollBarWidth &&
                                            index == headers.length - 1,
                                        'vd-ui-th-sortable': item.sortable,
                                        'sticky-item': canChoose ? leftFixedNumber - 1 > index : leftFixedNumber > index,
                                        'last-fixed': canChoose ? leftFixedNumber - 2 == index : leftFixedNumber - 1 == index,
                                        'align-right': item.align === 'right',
                                        'align-center': item.align === 'center'
                                    }"
                                    :style="
                                        (isRightFixed
                                            ? scrollBarWidth &&
                                            index == headers.length - 1
                                                ? 'right:' +
                                                (scrollBarWidth +
                                                    (isRightFixed ? 1 : 0)) +
                                                'px;padding-left:' +
                                                (10 +
                                                    (isRightFixed ? 1 : 0)) +
                                                'px'
                                                : ''
                                            : '') + stickyStyle(index)
                                    "
                                >
                                    <template v-if="item.sortable || item.filter || item.sum">
                                        <div class="vd-ui-table-th-cnt" :class="item.sum ? 'th-cnt-sum' : ''">
                                            <div class="vd-ui-table-th-inner" @click="sortItem(item)">
                                                <div class="vd-ui-table-th-txt">{{ item.label }}</div>
                                                <div class="vd-ui-table-th-sum" v-if="item.sum">
                                                    <ui-tip icon="sum" position="tr">
                                                        {{ item.sum.replace('%sum%', calcTotal(item.key)) }}
                                                    </ui-tip>
                                                </div>
                                                <div
                                                    class="vd-ui-table-sort"
                                                    v-if="item.sortable"
                                                >
                                                    <span
                                                        class="vd-ui-sort-icon"
                                                        :class="{
                                                            'vd-ui-sort-icon-active':
                                                                sortObj.key === item.key &&
                                                                sortObj.sort === 'up',
                                                        }"
                                                    ></span>
                                                    <span
                                                        class="
                                                            vd-ui-sort-icon
                                                            vd-ui-sort-icon-down
                                                        "
                                                        :class="{
                                                            'vd-ui-sort-icon-active':
                                                                sortObj.key === item.key &&
                                                                sortObj.sort === 'down',
                                                        }"
                                                    ></span>
                                                </div>
                                            </div>
                                            <div class="vd-ui-table-filter" :class="{active:filterObj.key === item.key, selected: hasFilterObj[item.key]}" v-if="item.filter">
                                                <i class="vd-ui_icon icon-filter" @click.stop="showFilterItem(item, $event)"></i>
                                            </div>
                                        </div>
                                    </template>
                                    <template v-else>
                                        {{ item.label }}
                                    </template>
                                    <div
                                        class="vd-ui-table-setting"
                                        @click="handlerSetting"
                                        :class="{
                                            'vd-ui-table-setting-right':
                                                scrollBarWidth,
                                        }"
                                        v-if="
                                            setting &&
                                            index == headers.length - 1
                                        "
                                    >
                                        <i
                                            class="
                                                vd-ui-table-setting-icon vd-ui_icon
                                                icon-app-more
                                            "
                                        ></i>
                                    </div>
                                </th>
                            </template>
                            <template v-if="scrollBarWidth">
                                <th class="vd-ui-th vd-ui-th-bar"></th>
                            </template>
                        </tr>
                    </thead>
                </table>
            </div>
            <div class="placeolder" v-if="isHeaderFixed && (!autoScroll || (autoScroll && scroll))" style="height:38px;width:100%"></div>
            <div
                class="vd-ui-table-body"
                :style="'max-height:'+containerHeight"
                :class="{
                    'vd-ui-wrap-is-left': isLeftFixed,
                    'vd-ui-wrap-is-right': isRightFixed,
                    'vd-ui-wrap-scroll': containerHeight,
                }"
                @scroll="handlerTableTrueScroll"
                ref="body"
            >
                <table
                    class="vd-ui-table"
                    :class="{
                        'vd-ui-table-left-fixed': isLeftFixed,
                        'vd-ui-table-right-fixed': isRightFixed,
                    }"
                    :style="'width:' + (scroll && !(!autoScroll && containerHeight) ? tableWidth + 'px' : '')"
                    ref="table"
                >
                    <colgroup>
                        <template v-if="canChoose">
                            <col :width="customList ? '36px' : '56px'"/>
                        </template>
                        <template v-for="(item, index) in headers">
                            <col :key="index" :width="item.width || ''" />
                        </template>
                    </colgroup>
                    <thead v-if="!scroll">
                        <tr class="vd-ui-tr">
                            <th class="vd-ui-th" :class="{'sticky-item': leftFixedNumber, 'last-fixed': leftFixedNumber == 1}" v-if="canChoose">
                                <div class="vd-ui-checkbox-wrap">
                                    <ui-checkbox :checked.sync="isSelectedAll" @change="selectAllChange"></ui-checkbox>
                                </div>
                            </th>
                            <template v-for="(item, index) in headers">
                                <th
                                    class="vd-ui-th"
                                    :class="{
                                        'vd-ui-th-sortable': item.sortable,
                                        'sticky-item': canChoose ? leftFixedNumber - 1 > index : leftFixedNumber > index,
                                        'last-fixed': canChoose ? leftFixedNumber - 2 == index : leftFixedNumber - 1 == index,
                                        'align-right': item.align === 'right',
                                        'align-center': item.align === 'center'
                                    }"
                                    :key="index"
                                    :style="stickyStyle(index)"
                                >   
                                    <template v-if="item.sortable || item.filter || item.sum">
                                        <div class="vd-ui-table-th-cnt" :class="item.sum ? 'th-cnt-sum' : ''">
                                            <div class="vd-ui-table-th-inner" @click="sortItem(item)">
                                                <div class="vd-ui-table-th-txt">{{ item.label }}</div>
                                                <div class="vd-ui-table-th-sum" v-if="item.sum">
                                                    <ui-tip icon="sum" position="br">
                                                        {{ item.sum.replace('%sum%', calcTotal(item.key)) }}
                                                    </ui-tip>
                                                </div>
                                                <div
                                                    class="vd-ui-table-sort"
                                                    v-if="item.sortable"
                                                >
                                                    <span
                                                        class="vd-ui-sort-icon"
                                                        :class="{
                                                            'vd-ui-sort-icon-active':
                                                                sortObj.key === item.key &&
                                                                sortObj.sort === 'up',
                                                        }"
                                                    ></span>
                                                    <span
                                                        class="
                                                            vd-ui-sort-icon
                                                            vd-ui-sort-icon-down
                                                        "
                                                        :class="{
                                                            'vd-ui-sort-icon-active':
                                                                sortObj.key === item.key &&
                                                                sortObj.sort === 'down',
                                                        }"
                                                    ></span>
                                                </div>
                                            </div>
                                            <div class="vd-ui-table-filter" :class="{active:filterObj.key === item.key, selected: hasFilterObj[item.key]}" v-if="item.filter">
                                                <i class="vd-ui_icon icon-filter"  @click.stop="showFilterItem(item, $event)"></i>
                                            </div>
                                        </div>
                                    </template>
                                    <template v-else-if="item.tip">
                                        <div class="vd-ui-th-tip-wrap">
                                            <span class="vd-ui-th-txt">{{ item.label }}</span>
                                            <ui-title-tip :title="item.tip" position="top" :y="-38">
                                                <i class="vd-ui_icon icon-info1"></i>
                                            </ui-title-tip>
                                        </div>
                                    </template>
                                    <template v-else>
                                        {{ item.label }}
                                    </template>
                                    <div
                                        class="vd-ui-table-setting"
                                        @click="handlerSetting"
                                        :class="{
                                            'vd-ui-table-setting-right':
                                                scrollBarWidth,
                                        }"
                                        v-if="
                                            setting &&
                                            index == headers.length - 1
                                        "
                                    >
                                        <i
                                            class="
                                                vd-ui-table-setting-icon vd-ui_icon
                                                icon-app-more
                                            "
                                        ></i>
                                    </div>
                                </th>
                            </template>
                        </tr>
                    </thead>
                    <tbody>
                        <template v-if="tableList.length">
                            <template v-for="(tr, index) in tableList">
                                <tr class="vd-ui-tr" :key="index" :class="{'on-select': index == tableSelectedIndex}" @click="tableSelectedIndex = index">
                                    <td class="vd-ui-td vertical-center" v-if="canChoose" :class="{'sticky-item': leftFixedNumber, 'last-fixed': leftFixedNumber == 1}">
                                        <slot name="left_tip" v-bind:row="tr"></slot>
                                        <div class="vd-ui-checkbox-wrap">
                                            <ui-checkbox :checked.sync="tr.checked" :disabled="tr.disabled" @change="checkSelectedAll"></ui-checkbox>
                                        </div>
                                    </td>
                                    <template v-if="customList">
                                        <slot
                                            name="tr"
                                            v-bind:row="tr"
                                        ></slot>
                                    </template>
                                    <template v-else>
                                        <template
                                            v-for="(header, kIndex) in headers"
                                        >
                                            <td 
                                                class="vd-ui-td" 
                                                :class="{
                                                    'sticky-item': canChoose ? leftFixedNumber - 1 > kIndex : leftFixedNumber > kIndex,
                                                    'last-fixed': canChoose ? leftFixedNumber - 2 == kIndex : leftFixedNumber - 1 == kIndex,
                                                    'can-edit': header.edit,
                                                    'align-right': header.align === 'right',
                                                    'align-center': header.align === 'center',
                                                    'vertical-center': header.vertical === 'center',
                                                    'td-copy-wrap': header.copy
                                                }" 
                                                :key="kIndex" 
                                                :style="stickyStyle(kIndex)"
                                            >
                                                <template
                                                    v-if="!customSlot[header.key.toLowerCase()]"
                                                >
                                                    <template v-if="header.avatar">
                                                        <div class="avatar-item-wrap" v-if="tr[header.key]">
                                                            <div class="avatar-img">
                                                                <img :src="tr[header.avatar] || GLOBAL.defaultAvatar" onerror="this.classList.add('error')"/>
                                                            </div>
                                                            <div class="item-text" :class="{'text-line-1': oneline}" :title="tr[header.key]">{{tr[header.key]}}</div>
                                                        </div>
                                                        <template v-else>-</template>
                                                    </template>
                                                    <template v-else-if="header.tel">
                                                        <div class="tel-item-wrap" :class="{normal: !layout_hidden_value}" v-if="tr[header.key]" @click="callNumber(tr[header.key], tr)">
                                                            <i class="vd-ui_icon icon-call"></i>
                                                            <div class="item-text" :class="{'text-line-1': oneline}" :title="tr[header.key]">{{tr[header.key]}}</div>
                                                        </div>
                                                        <template v-else>-</template>
                                                    </template>
                                                    <template v-else-if="oneline">
                                                        <span class="text-line-1" :title="tr[header.key]">{{ tr[header.key] || "-" }}</span>
                                                    </template>
                                                    <template v-else>
                                                        {{ tr[header.key] || "-" }}
                                                    </template>
                                                </template>
                                                <template v-else>
                                                    <slot
                                                        :name="header.key.toLowerCase()"
                                                        v-bind:row="tr"
                                                    ></slot>
                                                </template>
                                                <template  v-if="header.edit">
                                                    <div class="ui-table-td-edit-wrap" @click="handlerTableEdit(tr, header.key, $event)" v-if="!editValid || (editValid && editValid(tr))" title="编辑">
                                                        <i class="vd-ui_icon icon-edit"></i>
                                                    </div>
                                                    <div class="ui-table-td-edit-wrap disabled" :title="editValidMsg" v-else>
                                                        <i class="vd-ui_icon icon-edit"></i>
                                                    </div>
                                                </template>
                                                <div class="td-copy-option" v-if="header.copy" title="复制" @click="copy(tr[header.key] || '-')">
                                                    <i class="vd-ui_icon icon-popup"></i>
                                                </div>
                                            </td>
                                        </template>
                                    </template>
                                </tr>
                            </template>
                        </template>
                    </tbody>
                </table>
            </div>
            <div class="ui-table-list-empty" v-if="!tableList || !tableList.length">
                <template v-if="customEmpty">
                    <slot name="customempty"></slot>
                </template>
                <template v-else>
                    <div class="empty-img"></div>
                    <div class="empty-txt">暂无数据</div>
                </template>
            </div>
        </div>
        <div class="ui-table-footer-scroll" :class="{ hidden: !isFooterFixed }" ref="footerBar" @scroll="footerBarScroll" :style="'width:' + containerWidth + 'px;'">
            <div class="ui-table-footer-scroll-inner" :style="'width:' + tableWidth + 'px;'"></div>
        </div>
        <div class="ui-table-filter-drop J-table_drop_wrap" v-show="filterList && filterList.length" @click.stop ref="tableFilterDrop" :style="'left:' + dropX + 'px;top:' + dropY + 'px;'">
            <div class="ui-table-filter-drop-search">
                <ui-input placeholder="请输入关键词搜索" @input="handlerDropSearch" v-model="filterDropSearchValue" suffixIcon="icon-search"></ui-input>
            </div>
            <div class="ui-table-filter-drop-list" v-if="filerShowList.length">
                <div class="ui-table-filter-drop-inner">
                    <div class="ui-table-filter-item">
                        <ui-checkbox :checked.sync="isFilterSelectedAll" :label="filterSelectedAllTitle" @change="hadlerFilerChangeAll"></ui-checkbox>
                    </div>                               
                    <div class="ui-table-filter-item" v-for="(item, index) in filerShowList":key="index">
                        <ui-checkbox :checked.sync="item.checked" :label="item.showLabel || item.label" @change="checkFilterSelectAll"></ui-checkbox>
                    </div>
                </div>
            </div>
            <div class="ui-table-filter-drop-empty" v-else>暂无匹配数据</div>
            <div class="ui-table-filter-drop-footer">
                <ui-button type="primary" @click="setFilterData">确定</ui-button>
                <ui-button @click="clearDrop">取消</ui-button>
            </div>
        </div>
    </div>`,
    data() {
        return {
            isLeftFixed: false,
            isRightFixed: false,
            customSlot: {},
            tableWidth: 0,
            scrollBarWidth: 0,
            sortObj: {},
            filterObj: {},
            filterList: [],
            filerShowList: [],
            hasFilterObj: {},
            scroll: false,
            tableList: [],
            isSelectedAll: false,
            isLoading: true,
            isHeaderFixed: false,
            isFooterFixed: false,
            containerWidth: 0,
            leftFixed: true,
            currentDropTarget: null, //当前表头筛选的Dom target，计算定位用
            dropX: 0, //筛选下拉定位left
            dropY: 0, //筛选下拉定位top
            isFilterSelectedAll: true,
            filterSelectedAllTitle: '全选',
            filterDropSearchValue: '',
            tableSelectedIndex: -1 //点击单行选中样式
        };
    },
    props: {
        list: {
            type: Array,
            default: () => {
                return [];
            },
        },
        fixedTop: {
            type: Number,
            default: 0,
        },
        headers: {
            type: Array,
            default: () => {
                return [];
            },
        },
        leftFixedNumber: {
            type: Number,
            default: 1,
        },
        rightFixed: {
            type: Boolean,
            default: false,
        },
        autoScroll: {
            type: Boolean,
            default: true,
        },
        containerHeight: {
            type: String,
            default: "",
        },
        setting: {
            type: Boolean,
            default: false,
        },
        canChoose: {
            type: Boolean,
            default: false,
        },
        //是否开启列表模式。开启后，表头会吸顶，同时页面下方会有悬浮滚动条。
        isFullPage: {
            type: Boolean,
            default: false
        },
        //td,th边框
        border: {
            type: Boolean,
            default: false
        },
        //外边框
        widthBorder: {
            type: Boolean,
            default: false
        },
        //表格数据是不是都是一行展示
        oneline: {
            type: Boolean,
            default: false
        },
        //自定义单行内容
        customList: {
            type: Boolean,
            default: false
        },
        //是否全局排序
        listSort: {
            type: Boolean,
            default: true
        },
        editValid: {
            type: Function,
            default: null
        },
        editValidMsg: {
            type: String,
            default: ''
        },
        // 自定义空样式
        customEmpty: {
            type: Boolean,
            default: false,
        },
    },
    computed: {
        tableStyle() {
            if (this.scroll) {
                if (!this.autoScroll && this.containerHeight) {
                    return `width:${this.containerWidth}px;table-layout: fixed;`
                } else {
                    return `width:${this.tableWidth}px;table-layout: fixed;`
                }
            } else {
                return '';
            }
        }
    },
    watch: {
        list() {
            this.initListData();
        }
    },
    create() {
    },
    mounted() {
        let cunstomObj = {};
        for (let item in this.$scopedSlots) {
            cunstomObj[item] = 1;
        }
        this.customSlot = cunstomObj;


        this.initListData();

        if (this.canChoose) {
            this.checkSelectedAll();
        }

        this.isLoading = false;

        this.$nextTick(() => {
            this.initTable();
            if (this.autoScroll) {
                window.addEventListener('resize', this.checkTableScroll)
                window.addEventListener('scroll', this.checkBodyScroll)
            }
            document.addEventListener('click', () => {
                this.clearDrop();
            })
            this.checkScrollBarWidth();
        })

        // if (this.scroll) {
        //     this.handlerTableScroll();
        // }
    },
    methods: {
        initListData() {
            this.list.forEach((item, index) => {
                item.listUiIndex = index;
                if (this.canChoose) {
                    item.checked = item.checked || false;
                    item.disabled = item.disabled || false;
                }

                this.checkSelectedAll();
            })

            this.tableList = JSON.parse(JSON.stringify(this.list));
            this.tableSelectedIndex = -1;

            if(Object.keys(this.hasFilterObj).length) {
                this.getFilterList();
            } 

            if(Object.keys(this.sortObj).length) {
                this.sortItem(this.sortObj.item, 1);
            }

            this.$nextTick(() => {
                this.initTable()
            })
        },
        stickyStyle(index) {
            if (this.isLeftFixed) {
                if (this.canChoose) {
                    if (this.leftFixedNumber == 1) {
                        return "";
                    }

                    if (index == 0) {
                        return this.customList ? 'left: 36px' : 'left: 56px';
                    } else if (index < this.leftFixedNumber - 1) {
                        return 'left:' + (parseInt(this.headers[index - 1].width.replace('px', '')) + (this.customList ? 36 : 56)) + 'px';
                    }
                } else if (!this.canChoose && index > 0 && index < this.leftFixedNumber) {
                    return 'left:' + parseInt(this.headers[index - 1].width.replace('px', '')) + 'px';
                }

                return ''
            }

            return ''
        },
        initTable() {
            this.setTableWidth();
            if (this.autoScroll) {
                this.checkTableScroll();
                this.checkSelectedAll();
                setTimeout(() => {
                    this.checkBodyScroll();
                })
            }
            if (this.containerHeight && !this.autoScroll) {
                this.scroll = true;
                this.containerWidth = this.$refs.container.offsetWidth;
            }
        },
        checkSelectedAll() {
            let flag = true;

            if (!this.tableList.length) {
                flag = false;
            } else {
                this.tableList.forEach(item => {
                    if (!item.disabled && !item.checked) {
                        flag = false;
                    }
                })
            }

            this.isSelectedAll = flag;
            this.$forceUpdate();

            this.handlerSelectChange();
        },
        selectAllChange() {
            this.tableList.forEach(item => {
                if(!item.disabled) {
                    item.checked = this.isSelectedAll;
                }
            })
            this.$forceUpdate();
            this.handlerSelectChange();
        },
        handlerSelectChange() {
            this.$emit("selectchange", this.getSelectData());
        },
        getSelectData() {
            let selected = [];
            this.tableList.forEach(item => {
                if (item.checked) {
                    selected.push(item);
                }
            })

            return selected;
        },
        handlerTableTrueScroll(){
            this.$emit('truescroll')
            this.handlerTableScroll();
        },
        handlerTableScroll(e) {
            if (this.scroll) {
                this.$nextTick(() => {
                    let target = (e && e.target) || this.$refs.body;
                    if (this.leftFixedNumber) {
                        this.isLeftFixed = target.scrollLeft > 0;
                    }

                    if (this.rightFixed) {
                        this.isRightFixed =
                            target.offsetWidth +
                            target.scrollLeft -
                            this.scrollBarWidth <
                            this.$refs.table.offsetWidth - 2;
                    }

                    this.$refs.header.scrollLeft = target.scrollLeft;
                    this.$refs.footerBar.scrollLeft = target.scrollLeft;
                });
                this.$emit('tablescroll')
            }
            this.clearDrop();

        },
        checkTableScroll() {
            if (!this.$refs.container) {
                return;
            }
            let containerWidth = this.$refs.container.offsetWidth;
            this.containerWidth = containerWidth;

            if (containerWidth >= this.tableWidth) {
                this.scroll = false;
                this.isLeftFixed = false;
                this.isRightFixed = false;
            } else {
                this.scroll = true;
                this.handlerTableScroll();
            }
            this.clearDrop();
        },
        checkBodyScroll() {
            if (!this.$refs.container) {
                return;
            }
            
            let containerTop = this.$refs.container.getBoundingClientRect().top;
            let containerBottom = this.$refs.container.getBoundingClientRect().bottom;

            if (containerTop < this.fixedTop) {
                if (!this.isHeaderFixed) {
                    this.clearDrop()
                }
                this.isHeaderFixed = true;
            } else {
                if (this.isHeaderFixed) {
                    this.clearDrop()
                }
                this.isHeaderFixed = false;
            }

            if (this.tableList.length && containerBottom > window.innerHeight + 10 && containerTop < window.innerHeight - 90) {
                this.isFooterFixed = true;
            } else {
                this.isFooterFixed = false;
            }

            this.calcDropPotion();
        },
        setTableWidth() {
            let width = 0;
            this.headers.forEach((item, index) => {
                width += parseInt(item.width);
            });

            if (this.canChoose) {
                width += (this.customList ? 36 : 56);
            }

            this.tableWidth = width;
        },
        checkScrollBarWidth() {
            this.scrollBarWidth =
                this.$refs.body.offsetWidth - this.$refs.body.clientWidth;

                console.log(this.$refs.body.offsetWidth, this.$refs.body.clientWidth)
        },
        handlerSetting() {
            this.$emit("handlerSetting");
        },
        sortItem(item, refresh) {
            if (!item.sortable) {
                return false;
            }

            let sortObj = JSON.parse(JSON.stringify(this.sortObj));

            if(!refresh == 1) {
                if (sortObj.key !== item.key) {
                    sortObj.sort = "down";
                } else {
                    if (!sortObj.sort) {
                        sortObj.sort = "down";
                    } else if (sortObj.sort === "up") {
                        sortObj.sort = "";
                    } else if (sortObj.sort === "down") {
                        sortObj.sort = "up";
                    }
                }
            }


            if(!this.listSort) {
    
                let sortType = item.sortType || 'normal';
    
                if (!sortObj.sort) {
                    this.tableList = this.tableList.sort((a, b) => {
                        return a.listUiIndex - b.listUiIndex;
                    })
                } else {
                    if (sortType == 'normal') {
                        this.tableList = this.tableList.sort((a, b) => {
                            if (sortObj.sort === "up") {
                                return (a[item.sortId || item.key] || '').toString().localeCompare((b[item.sortId || item.key] || '').toString());
                            } else {
                                return (b[item.sortId || item.key] || '').toString().localeCompare((a[item.sortId || item.key] || '').toString());
                            }
                        })
                    } else if (sortType == 'number') {
                        this.tableList = this.tableList.sort((a, b) => {
                            if (sortObj.sort === "up") {
                                return parseFloat(a[item.sortId || item.key]) - parseFloat(b[item.sortId || item.key]);
                            } else {
                                return parseFloat(b[item.sortId || item.key]) - parseFloat(a[item.sortId || item.key]);
                            }
                        })
                    } else if (sortType == 'time') {
                        this.tableList = this.tableList.sort((a, b) => {
                            if (sortObj.sort === "up") {
                                return new Date(a[item.sortId || item.key]).getTime() - new Date(b[item.sortId || item.key]).getTime();
                            } else {
                                return new Date(b[item.sortId || item.key]).getTime() - new Date(a[item.sortId || item.key]).getTime();
                            }
                        })
                    } else if (sortType == 'SABC') {
                        this.tableList = this.tableList.sort((a, b) => {
                            const level = {
                                S: 1,
                                A: 2,
                                B: 3,
                                C: 4
                            };
    
                            if (sortObj.sort === "up") {
                                return (level[b[item.key]] || 99) - (level[a[item.key]] || 99)
                            } else {
                                return (level[a[item.key]] || 99) - (level[b[item.key]] || 99)
                            }
                        })
                    } else if (sortType == 'address') {
                        this.tableList = this.tableList.sort((a, b) => {
                            let aValue = a[item.sortId || item.key] || '';
                            let bValue = b[item.sortId || item.key] || '';

                            let aAreaId = parseInt(aValue ? aValue.split(',')[2] : '0');
                            let bAreaId = parseInt(bValue ? bValue.split(',')[2] : '0');
    
                            if (sortObj.sort === "up") {
                                return aAreaId - bAreaId;
                            } else {
                                return bAreaId - aAreaId;
                            }
                        })
                    }
                }
            }

            sortObj.key = item.key;
            sortObj.item = item;
            if(!refresh == 1) {
                this.sortObj = sortObj;
                this.$emit("handlersort", this.sortObj);
            }
        },
        clearSort(){
            this.sortObj = {};
        },
        clearFilter(){
            this.filterObj = {};
            this.filterList = [];
            this.filerShowList = [];
            this.hasFilterObj = {};
        },
        showFilterItem(item, e) {
            let isSlef = this.filterObj.key == item.key;
            this.clearDrop();

            if (!isSlef) {
                let filterSet = new Set();
                this.list.forEach(data => {
                    if (item.filterType === 'array') {
                        let list = data[item.key] || [];

                        if(list.length) {
                            list.forEach(listItem => {
                                filterSet.add(listItem[item.filterKey]);
                            })
                        } else {
                            filterSet.add('(空值)')
                        }
                    } else {
                        if (data[item.key]) {
                            filterSet.add(data[item.key])
                        } else {
                            filterSet.add('(空值)')
                        }
                    }
                })

                let keyFilterObj = this.hasFilterObj[item.key];

                let filterList = []
                filterSet.forEach(label => {
                    filterList.push({
                        label: label,
                        checked: keyFilterObj ? !!keyFilterObj.data[label] : true
                    })
                })

                this.filterObj = item;
                this.filterList = filterList;
                this.currentDropTarget = e.target;

                this.calcDropPotion();

                this.filerShowList = this.filterList;
                this.checkFilterSelectAll();

                document.body.append(this.$refs.tableFilterDrop);
            }
        },
        calcDropPotion() {
            if (!this.currentDropTarget) {
                return;
            }
            let targetPosition = this.currentDropTarget.getBoundingClientRect();

            let winTop = window.scrollY;
            let winLeft = window.scrollX;

            if (window.innerWidth - targetPosition.x > 320) {
                this.dropX = winLeft + targetPosition.x;
            } else {
                this.dropX = winLeft + targetPosition.x - 300 + targetPosition.width;
            }

            this.dropY = winTop + targetPosition.y + 37;
        },
        footerBarScroll() {
            let scrollLeft = this.$refs.footerBar.scrollLeft;

            this.$refs.body.scrollLeft = scrollLeft;
            this.handlerTableScroll();
        },
        hadlerFilerChangeAll() {
            this.filerShowList.forEach(item => {
                item.checked = this.isFilterSelectedAll;
            })
        },
        checkFilterSelectAll() {
            let flag = true;

            this.filerShowList.forEach(item => {
                if (!item.checked) {
                    flag = false;
                }
            })

            this.isFilterSelectedAll = flag;
        },
        handlerDropSearch() {
            let value = this.filterDropSearchValue.trim();

            if (value) {
                let searchList = [];

                this.filterList.forEach(item => {
                    if (item.label.toUpperCase().indexOf(value.toUpperCase()) != -1) {
                        // let searchItem = JSON.parse(JSON.stringify(item));
                        let searchItem = item;
                        let reg = new RegExp('(' + value + ')', 'ig');
                        searchItem.showLabel = item.label.replace(reg, '<span class="strong">$1</span>');
                        searchList.push(searchItem);
                    }
                })

                this.filerShowList = searchList;
                this.filterSelectedAllTitle = "选中全部搜索项";
            } else {
                this.filterList.forEach(item => {
                    item.showLabel = '';
                })
                this.filterSelectedAllTitle = "全部";
                this.filerShowList = this.filterList;
            }

            this.checkFilterSelectAll();
        },
        setFilterData() {
            let filterItemObj = {};
            this.filterList.forEach(item => {
                if (item.checked) {
                    filterItemObj[item.label] = 1;
                }
            })

            if (Object.keys(filterItemObj).length !== this.filterList.length) {
                this.hasFilterObj[this.filterObj.key] = {
                    type: this.filterObj,
                    data: filterItemObj
                };
            } else {
                delete this.hasFilterObj[this.filterObj.key];
            }
            this.clearDrop();
            this.$forceUpdate();

            this.getFilterList();
        },
        getFilterList() {
            let filterList = [];

            if (Object.keys(this.hasFilterObj).length) {
                this.list.forEach(item => {
                    let isInFilter = true;

                    for (let key in this.hasFilterObj) {
                        let filterItem = this.hasFilterObj[key];

                        if (!item[key] && !filterItem.data['(空值)']) {
                            isInFilter = false;
                            return;
                        }

                        if(item[key]) {
                            if (filterItem.type.filterType === 'array') {
                                let hasInArray = false;
                                if (item[key].length) {
                                    item[key].forEach(ki => {
                                        if (filterItem.data[ki[filterItem.type.filterKey]]) {
                                            hasInArray = true;
                                        }
                                    })
                                } else if(filterItem.data['(空值)']){
                                    hasInArray = true;
                                }
    
                                if(!hasInArray) {
                                    isInFilter = hasInArray;
                                }
                            } else if (!filterItem.data[item[key]]) {
                                isInFilter = false;
                            }
                        }
                    }

                    if (isInFilter) {
                        filterList.push(item);
                    }
                })

                this.tableList = filterList;
            } else {
                this.tableList = JSON.parse(JSON.stringify(this.list));
            }

            if(Object.keys(this.sortObj).length) {
                this.sortItem(this.sortObj.item, 1);
            }
        },
        clearDrop() {
            if (document.querySelector('.J-table_drop_wrap')) {
                document.querySelector('.J-table_drop_wrap').remove();
            }
            this.filterObj = {};
            this.filterList = [];
            this.currentDropTarget = null;
            this.filterDropSearchValue = "";
        },
        handlerTableEdit(item, key, e) {
            let $td = e.target.parentElement;

            this.$emit('tableItemEdit', {
                data: item,
                el: $td,
                key: key
            });
        },
        callNumber(tel, data) {
            this.$emit('call', {
                phone: tel,
                data: data
            })
        },
        copy(txt) {
            GLOBAL.copyTextToClipboard(txt, this);
        },
        calcTotal(key) {
            let sum = 0;
            this.tableList.forEach(item => {
                sum += parseFloat(item[key] || '0');
            })

            return Math.round(sum * 100) / 100;
        }
    },
})