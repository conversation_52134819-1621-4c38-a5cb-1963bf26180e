package com.vedeng.erp.buyorder.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.buyorder.domain.entity.BuyOrderRebateChargeApplyEntity;
import com.vedeng.erp.buyorder.dto.BuyOrderRebateChargeApplyApiDto;
import com.vedeng.erp.buyorder.dto.BuyOrderRebateChargeApplyDto;
import com.vedeng.erp.buyorder.web.api.BuyOrderRebateChargeApplyApi;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;


@Mapper(componentModel = "spring")
public interface BuyOrderRebateChargeApplyApiConvertor {

    BuyOrderRebateChargeApplyApiDto to(BuyOrderRebateChargeApplyDto dto);
}
