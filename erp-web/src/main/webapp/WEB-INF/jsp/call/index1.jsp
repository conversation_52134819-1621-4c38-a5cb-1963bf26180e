<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" >
<head>
    <meta content="text/html; charset=GB18030" http-equiv="content-type" />
    <title>电话操控按健</title>
    <link rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/static/css/content.css?rnd=${resourceVersionKey}">
    <link rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/static/css/call-panel-layer.css?rnd=${resourceVersionKey}">
    <link rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/static/css/general.css?rnd=${resourceVersionKey}">
    <link rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/static/css/manage.css">
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/js/jquery.min.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/mylayer.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/js/call/wsSoftPhone/SPCommand.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/js/call/wsSoftPhone/WSSoftPhone.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/js/call/wsSoftPhone/locale/wssoftphone-lang-zh_CN.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/js/call/call.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/js/common.js?rnd=${resourceVersionKey}"></script>
</head>
<body onload="window_onload();" style="height: 70px;overflow: hidden;">
<div class="layer-content call-panel">
    <div class="call-queue">
        <div class="call-queue-list-title">呼入队列</div>
        <ul class="call-queue-list">
        </ul>
    </div>
    <div class="panel-content">
        <div class="title-bar">
            <div class="button-bar">
                <ul class="button1-pos">
                    <li>
                        <a href="javascript:void(0);" class="button button2 bg-light-blue bt-bg-style bt-smaller"
                           onclick="window_onload_btn();"  >刷新登录
                        </a>
                        <a href="javascript:void(0);" class="button button2 bg-light-blue bt-bg-style bt-smaller" onclick="connectWsServer();" id="btn_logon">登录
                        </a>
<%--                        <a href="javascript:void(0);" class="button button2 bg-light-red bt-bg-style bt-smaller" onclick="cmdSetParam_offClick();"--%>
<%--                           style="display: none;" id="btn_logoff">退出</a>--%>
                        <input type="hidden" id="serverIp" value="${callParams.callServerIp}" />
                        <input type="hidden" id="devNum" value="${user.userDetail.ccNumber}" />
                        <input type="hidden" id="agentID" value="${user.number}" />
                        <input type="hidden" id="agentName" value="${user.username}" />
                        <input type="hidden" id="agentType" value="${agentType}" />
                        <input type="hidden"  id="orderTypeCallFlag" name="orderTypeCallFlag"/>
                        <input type="hidden"  id="orderIdCallFlag" name="orderIdCallFlag"/>
                        <input type="hidden" id="communicateTime" name="communicateTime" value="0"/>
                        <input type="hidden" id="newCallCenterFlag" value="${newCallCenterFlag == true ? 1 : 0}"/>
                        <input type="hidden" id="lineCode"/>
                        <input type="hidden" id="callingNumber"/>
                        <input type="hidden" id="canCloseCommunicate" value="${canCloseCommunicate}"/>
                        <input type="hidden" id="txtSK" value="${txtSK }" />
                        <input type="hidden" id="ttNumber" value="${ttNumber}" />
                        <input type="hidden" id="ttNumberCount" value="${ttNumberCount}" />
                        <input type="hidden" id="positType" value="${user.positType}" />
                        <input type="hidden" id="delegateId"/>
                        <!-- 配置的电信号码 -->
                        <input type="hidden" id="telecomLine" value="${telecomLine}" />

                    </li>
                    <li class="only-text mr10">工号:<span class="value">${user.number}</span></li>
                    <li class="only-text mr10">名称:<span class="value">${user.username}</span></li>
                    <li class="only-text mr10">分机号:<span class="value">${user.userDetail.ccNumber}</span></li>
                    <li id="model" class="mr10" <c:if test="${newCallCenterFlag}">style="display: none;"</c:if>>模式
                        <select name="model" onchange="changeModel();">
                            <option value="0">普通模式</option>
                            <option value="1">下班模式</option>
                            <option value="3">外拨模式</option>
                            <c:if test="${agentType == 1 }">
                                <option value="4">班长模式</option>
                            </c:if>
                        </select>
                    </li>
                    <li id="status"  class="mr10" <c:if test="${newCallCenterFlag}">style="display: none;"</c:if>>工作状态
                        <select name="status" onchange="changeStatus();">
                            <option value="4">正常工作</option>
                            <option value="0">小休</option>
                            <option value="1">开会</option>
                            <option value="2">就餐</option>
                            <option value="3">其它工作</option>
                        </select>
                    </li>
                    <li id="mycall"   class="mr10" <c:if test="${newCallCenterFlag}">style="display: none;"</c:if> >主叫号码
                        <select name="mycall" id="myCallSelect" onchange="changeMycall();">
                            <option value="0" 	<c:if test="${mycall == 0 }"> selected </c:if>>固话</option>
                            <c:if test="${ttNumber != '' && ttNumber != null}">
                                <option   value="2" <c:if test="${ttNumberCount <=  0 }"> disabled="true" </c:if> <c:if test="${mycall == 2 }"> selected </c:if>>${ttNumber}</option>
                            </c:if>
                            <option value="1" <c:if test="${mycall == 1 }"> selected </c:if>>号码池</option>
                        </select>
                    </li>
                    <li id="consult" style="display: none;"><a href="javascript:void(0);" onclick="consult();" class="button button2 icon icon2 bg-light-green bt-bg-style bt-smaller">内部咨询</a></li>
                    <li id="cancelPrepareConsult" style="display: none;"><a href="javascript:void(0);" onclick="cancelConsult();" class="button button3 icon icon2 bg-light-red bt-bg-style bt-smaller">取消咨询</a></li>
                    <li id="cancelConsult" style="display: none;"><a href="javascript:void(0);" onclick="finishConsult();" class="button button3 icon icon2 bg-light-red bt-bg-style bt-smaller">结束咨询</a></li>

                    <li id="transfer" style="display: none;"><a href="javascript:void(0);" onclick="transfer();" class="button button1 icon icon6 bg-light-green bt-bg-style bt-smaller">转接</a></li>

                    <li id="consultTransfer" style="display: none;"><a href="javascript:void(0);" onclick="consultTransfer();" class="button button2 icon icon6 bg-light-green bt-bg-style bt-smaller">咨询转接</a></li>

                    <li id="cancelDial" style="display: none;"><a href="javascript:void(0);" onclick="cancelDial();" class="button button4 icon icon8 bg-light-red bt-bg-style bt-smaller">取消</a></li>

                    <li id="hold" style="display: none;"><a href="javascript:void(0);" onclick="hold();" class="button button1 icon icon5 bg-light-green bt-bg-style bt-smaller">保持</a></li>
                    <li id="fetchHold" style="display: none;"><a href="javascript:void(0);" onclick="fetchHold();" class="button button3 icon icon5 bg-light-red bt-bg-style bt-smaller">取回</a></li>

                    <li id="dropCall" style="display: none;"><a href="javascript:void(0);" onclick="dropCall();" class="button button4 icon icon3 bg-light-red bt-bg-style bt-smaller">结束</a></li>
                    <li id="finishWrapup" style="display: none;"><a href="javascript:void(0);" onclick="finishWrapup();" class="button button1 icon icon7 bg-light-green bt-bg-style bt-smaller">完成</a></li>

                    <li id="callInner" style="display: none;"><a href="javascript:void(0);" onclick="prepareCallInner();" class="button button1 icon icon1 bg-light-green bt-bg-style bt-smaller">内呼</a></li>
                    <li id="cancelPrepareCallInner" style="display: none;"><a href="javascript:void(0);" class="button button3 icon icon1 bg-light-red bt-bg-style bt-smaller">取消内呼</a></li>
                    <li id="cancelCallInner" style="display: none;"><a href="javascript:void(0);" onclick="cancelCallInner();" class="button button3 icon icon1 bg-light-red bt-bg-style bt-smaller">取消内呼</a></li>

                    <li id="monitor" style="display: none;"><a href="javascript:void(0);" onclick="prepareMonitor();" class="button button3 icon icon4 bg-light-red bt-bg-style bt-smaller">监听</a></li>
                    <li id="cancelPrepareMonitor" style="display: none;"><a href="javascript:void(0);" class="button button3 icon icon4 bg-light-red bt-bg-style bt-smaller">取消监听</a></li>
                    <li id="finishMonitor" style="display: none;"><a href="javascript:void(0);" onclick="finishMonitor();" class="button button3 icon icon4 bg-light-red bt-bg-style bt-smaller">取消监听</a></li>
                    <li    >
                        <input class="input-middle" placeholder="号码无需前缀"  id="selfCallPhoneNo" value="" type="text">
                        <a href="javascript:void(0);" onclick="calloutSelf($('#selfCallPhoneNo').val())" class="button button2 icon icon2
                        bg-light-green bt-bg-style bt-smaller">自主拨号</a>
                    </li>
                </ul>
            </div>
            <div class="clear"></div>
        </div>

        <div class="title-bar">
            <div class="panel-message">
                <ul>
                    <li><span id="model_span"></span>：<span class="value2" id="trace_model">未连接</span></li>
                    <c:if test="${newCallCenterFlag}">
                        <li>主叫号码：<span class="value2" id="callLine"></span></li>
                    </c:if>
                    <li>号码池号码：<span class="value2" id="phonepool"></span></li>
                    <li>提示信息：<span class="value2" id="trace_msg"></span></li>
                    <li id="call" style="display: none;"><span id="phone_type">呼入/呼出电话</span>：<span class="value" id="phone_span"></span></li>
                    <li id="time" style="display: none;">通话时长：<span class="value" id="time_span"></span></li>
                    <li style="display: none;">正在咨询分机：<span class="value"></span></li>
                </ul>
                <div class="clear"></div>
            </div>
        </div>
    </div>
    <div class="clear"></div>
    <div style="height: 0;overflow: hidden;">
        <div id='iptooldiv'></div>
        <div id='softphonediv'></div>
    </div>
</div>
<script type="text/javascript">

    // 埋点，统计每个按钮使用的情况
    function callRecord(type){
        try {
            let agentID = document.getElementById("agentID").value;
            $.ajax({
                url: page_url+'/checkpreload.jsp?type='+type+"&agentID="+agentID,
                type:"GET",
                async: true, // 异步调用，只做记录，不影响功能使用
                success: function (data) {
                    console.log(data);
                }
            });
        }catch (e) {
            console.log('callRecord error');
        }

    }

    let callCenterServerUrl = '${ccWsServer}';

    function calloutSelf(phone){
        $.ajax({
            url: page_url+'/checkSession.do?tag=self&phone='+phone,
            dataType: "json",
            type:"POST",
            async: false,
            success: function (data) {
                if (data.code==0) {
                } else {
                    layer.confirm("登录超时，请重新登录", {
                            btn : [ '关闭' ]
                    }, function() {
                            top.location.href=page_url+'/login.do';
                    });
                }
            }
        });
        if (typeof phone == 'undefined' || phone == null || phone == ""){
            $("#trace_msg").html('外拨号码不能为空');
        } else {
            callout(phone,0,0,0,0,0);
        }
    }

    //拨号
    function serviceDial(phoneNum,ani,sourceTag,serviceParam1,serviceParam2,useDateRollAni){
        wssp.dial(phoneNum,ani,sourceTag,serviceParam1,serviceParam2,useDateRollAni)
    }


    function serviceSetCoInfos(key,value){
        wssp.setCOInfo(key,value)
    }

    function serviceGetCoInfo(key){
        return  wssp.coInfos[key]
    }

    /**
     * 内呼
     * @param destSPType 目标类型，AGT 座席，EXT 分机，OUT：外呼
     * @param destSPIns 座席实例或者分机号
     */
    function serviceCallInner(destSPType, destSPIns){
        wssp.callInner(destSPType,destSPIns)
    }


    function serviceMonitorAgent(destSPType, destSPIns){
        wssp.monitorAgent(destSPType, destSPIns)
    }


    function serviceOneStepTransfer(destSPType, destSPIns){
        let transferInfo = new SPOneStepTransferInfo();
        transferInfo.destType = destSPType;
        transferInfo.destIns = destSPIns;
        wssp.oneStepTransfer(transferInfo);
    }


    function serviceConsult(destSPType, destSPIns){
        wssp.consult(destSPType, destSPIns);
    }


    function serviceCallOut(phoneNum, ani){
        wssp.callout(phoneNum,ani);
    }

    function serviceGetCurrentAgentState(){
        return wssp.agentState;
    }


    function serviceCancelPrepareMonitor(){
        wssp.cancelPrepareMonitor();
    }


    function serviceCancelPrepareCallInner(){
        console.log("123")
        wssp.cancelPrepareCallInner();
    }

</script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/call/wsSoftPhone/wssp.js?rnd=${resourceVersionKey}"></script>
</body>


</html>