<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="图层_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 width="371px" height="200px" viewBox="0 0 371 200" enable-background="new 0 0 371 200" xml:space="preserve">
<g>
	<g>
		<g>
			
				<linearGradient id="路径_9_" gradientUnits="userSpaceOnUse" x1="-118.7646" y1="493.4424" x2="-117.6058" y2="490.5827" gradientTransform="matrix(63.9 0 0 -65.4 7615.1318 32247.7617)">
				<stop  offset="0" style="stop-color:#D2D2D2"/>
				<stop  offset="1" style="stop-color:#D2D2D2;stop-opacity:0"/>
			</linearGradient>
			<path id="路径" fill="url(#路径_9_)" d="M73.3,159.6l-3-1.6l-2.9-2.2l-2.7-2.5l-2.4-2.8l-1.8-3.1l-1.2-3.301l-0.5-2.199
				l0.4-4.601L61,137.4l1.4,1.199l3.4,4.801l3,3.399l1.5,1.3l1.7,0.5l0.5-0.5l0.4-1.1v-2.2l-0.6-4.5L68.1,121.9l-1-9.4l0.1-3.5
				l0.5-2.9l0.9-2.5l1.2-2.3l1.4-1.7l1.5-1.2l1.7-0.9l1.8-0.4l1.8,0.1l1.8,0.6l1.8,1.1l1.7,1.8l1.5,2.1l1.3,2.9l1.1,3.8l1.7,9.1
				l1.5,6.101l1.1,2.8l1.1,1.8l0.9,0.9l0.9,0.3l1-0.2l1-0.7l5.3-10.3l2.2-2.2l1.5-0.699l2.9-0.2l3.3,1.3l1.8,1.5l2.9,3.6l2.9,5.9
				l2.4,7.6l0.8,4l0.3,3.9l-0.4,5.1l-1.6,4.2l-1.3,1.8l-1.7,1.5l-4.5,2.301l-7.3,2.1l-5,0.9l-10.2,0.6l-5.1-0.2l-4.6-0.6l-4.3-0.9
				L73.3,159.6z"/>
			
				<linearGradient id="路径_10_" gradientUnits="userSpaceOnUse" x1="-116.9473" y1="492.6797" x2="-117.5922" y2="491.0152" gradientTransform="matrix(59.2 0 0 -64.9 7263.3291 32037.0117)">
				<stop  offset="0" style="stop-color:#D2D2D2"/>
				<stop  offset="1" style="stop-color:#D2D2D2;stop-opacity:0"/>
			</linearGradient>
			<path id="路径_1_" fill="url(#路径_10_)" d="M273.2,180.4L272.1,177.3l-0.8-3.6L271,170.1l0.2-3.699l0.399-2.4l1.601-4.4
				l2.7-3.699l1.8-1.4l0.7,0.6l0.8,1.601l-0.7,6.8l-0.1,4.5l0.3,2l0.399,1.1l0.5,0.4h0.7l1-0.6l1.5-1.601l2.601-3.8l9.3-16.4l2.6-4
				l2.9-3.699l2.399-2.5l2.4-1.801l2.2-1.3l2.399-0.899l2.2-0.301h2l1.9,0.601l1.5,0.899l1.3,1.301l1,1.6l0.5,2l0.1,2.6l-0.3,2.5
				l-1,3l-1.7,3.601l-6.399,10.7l-2.4,5.5l-0.5,2.1l0.101,1.3l0.5,0.8l0.8,0.601l1.2,0.1l10.899-4.2l3.2-0.1l1.5,0.5l1.3,0.7
				l1.8,2.2l0.7,2.1l0.4,4.6l-0.3,2.301l-1,3.399l-1.601,3.3l-2.1,3.2l-2.601,2.8l-4.3,2.9l-4.899,1.7l-2.7,0.3l-2.9-0.1l-9.7-2.2
				l-5.199-1.8l-5.101-2.2l-6.2-3.601l-5.1-4.3l-1.9-2.3L273.2,180.4z"/>
			
				<linearGradient id="路径_11_" gradientUnits="userSpaceOnUse" x1="-119.1978" y1="492.5371" x2="-119.1978" y2="489.3255" gradientTransform="matrix(370.5 0 0 -51.9 44348.25 25595.3867)">
				<stop  offset="0" style="stop-color:#D2D2D2"/>
				<stop  offset="1" style="stop-color:#D2D2D2;stop-opacity:0"/>
			</linearGradient>
			<path id="路径_2_" fill="url(#路径_11_)" d="M370.5,199.4l-8.8-5.801l-9.3-5.5l-9.9-5.3l-10.3-5l-10.5-4.6l-10.8-4.2
				l-11.4-3.9l-11.7-3.6l-11.8-3.2l-12.2-2.8l-12.5-2.4l-12.899-2l-12.801-1.5L212.4,148.4l-13.4-0.7l-13.7-0.2l-13.8,0.2l-13.4,0.7
				L145,149.6l-12.9,1.5l-12.9,2l-12.5,2.4l-12.2,2.8l-11.8,3.2L71,165.1L59.7,169l-10.9,4.2l-10.5,4.6l-10.3,5l-9.8,5.3l-9.4,5.5
				L0,199.4H370.5z"/>
			
				<linearGradient id="形状结合_1_" gradientUnits="userSpaceOnUse" x1="-115.8896" y1="491.7314" x2="-116.5022" y2="489.7063" gradientTransform="matrix(35.6 0 0 -55.7 4419.7427 27437.6934)">
				<stop  offset="0" style="stop-color:#D2D2D2"/>
				<stop  offset="1" style="stop-color:#D2D2D2;stop-opacity:0"/>
			</linearGradient>
			<path id="形状结合" fill="url(#形状结合_1_)" d="M293.5,103.1L291,107.2l-0.8,2.3l-0.4,2.4v2.399l0.601,2.4l0.8,1.399
				l3,3.101l0.6,1.3l0.2,2l-0.4,2.2l-1.8,4.1l-4.8,6.8l-2.8,3.101l1.6,1.7l1.101,2l0.6,2.1v2.4l-1.4,4.399l-1.3,1.9l-2.6,2.8
				l-1.3,0.8H279l-4.5-1l-4.4-1.5l-4.3-1.8l-3.8-2.3l-1.4-1.5l-1-1.8L259.4,146.3l1.3-1.8l3-1.8l1.1-1.101l0.7-1.699l1.1-5.7
				l0.601-6.4l1.2-3.2l0.899-0.899l1.3-0.7l5.2-1l3.101-1.8l2.399-3.7l2.8-6.1l1.7-2.601l1.101-1L293.5,103.1z M282.8,123.8
				l-6.5,9.4l-3.8,7.6l-1.4,4.2l-0.699,2.9l-0.5,5.699h4.3l1.7-10.199l1.1-4.5l3.4-10.2L282.8,123.8z"/>
			
				<linearGradient id="路径_12_" gradientUnits="userSpaceOnUse" x1="-116.0952" y1="484.6836" x2="-116.0952" y2="481.3824" gradientTransform="matrix(32.8 0 0 -22.7 3901.2322 10962.2871)">
				<stop  offset="0" style="stop-color:#D2D2D2"/>
				<stop  offset="1" style="stop-color:#D2D2D2;stop-opacity:0"/>
			</linearGradient>
			<path id="路径_3_" fill="url(#路径_12_)" d="M104.1,22l-0.2-2.3l-0.8-2.2l-1.2-1.8l-1.6-1.6l-2-1.1l-2.2-0.7l-2.4-0.1
				l-2.2,0.4l-2.1,0.9l-1.7,1.3l-1.5,1.8l-1,2.1L83,19l-2,0.8L79.3,21L78,22.8l-0.8,2L76.9,27l0.3,2.1l0.9,2l1.4,1.7l1.7,1.2
				l2.1,0.8l20,0.1l3.2-0.8l2.2-2.2l0.7-1.4l0.3-1.7l-0.1-1.6l-0.5-1.5l-2-2.5L104.1,22z"/>
			
				<linearGradient id="路径_13_" gradientUnits="userSpaceOnUse" x1="-116.75" y1="486.9082" x2="-116.75" y2="484.2162" gradientTransform="matrix(40.6 0 0 -28.4 4904.8364 13780.1377)">
				<stop  offset="0" style="stop-color:#D2D2D2"/>
				<stop  offset="1" style="stop-color:#D2D2D2;stop-opacity:0"/>
			</linearGradient>
			<path id="路径_4_" fill="url(#路径_13_)" d="M178.1,12.2l-0.6-3.8l-0.8-1.8l-1.5-2.3l-2-1.9l-1.6-1L168,0.2L166,0l-3.7,0.5
				l-2.5,1.2l-2.3,1.6l-1.2,1.4l-1.8,3.4l-3.6,0.7l-3,1.8l-2.1,2.7l-1.2,3.2l-0.1,1.9l0.7,3.5l1.8,3l2.6,2.2l3.3,1.2l1.9,0.1H177
				l2.1-0.3l1.8-0.7l1.7-1.2l1.2-1.5l0.9-1.8l0.4-2.1l-0.1-2.1l-0.7-1.9l-1.1-1.7l-1.4-1.3l-1.8-1L178.1,12.2z"/>
		</g>
		<g>
			
				<linearGradient id="路径_14_" gradientUnits="userSpaceOnUse" x1="-115.0342" y1="486.7178" x2="-115.0342" y2="485.9427" gradientTransform="matrix(25 0 0 -33 3102.3438 16130.0313)">
				<stop  offset="0" style="stop-color:#D7D7D7"/>
				<stop  offset="1" style="stop-color:#E4E4E4"/>
			</linearGradient>
			<path id="路径_5_" fill="url(#路径_14_)" d="M214,66l23,0.1l0.4,0.7L239,99h-25V66z"/>
			
				<linearGradient id="路径_15_" gradientUnits="userSpaceOnUse" x1="-118.4658" y1="494.0605" x2="-118.4707" y2="493.122" gradientTransform="matrix(108 0 0 -132 12978.3125 65271.5)">
				<stop  offset="0" style="stop-color:#F5F5F5"/>
				<stop  offset="0.96" style="stop-color:#E6E6E6"/>
			</linearGradient>
			<path id="路径_6_" fill="url(#路径_15_)" d="M130,72.2V186h71.5l3.1-0.3l2.801-0.8l2.699-1.4l2.301-1.8l2-2.2l1.699-2.5
				l1.301-2.8l0.699-3.101l0.301-3.3V78.9l0.3-3.2l0.899-2.8l0.9-1.7l2.3-2.6l3.2-1.5l1.9-0.3l2,0.2l1.699,0.6l1.5,1l3,3.2l1,2.1
				l0.7,2.1l0.2,2.4l-0.2-9.5l-0.6-3.1l-1-2.8l-1.4-2.6l-1.7-2.1l-2-1.8l-2.3-1.3l-2.7-0.9l-3-0.3h-76.2l-3,0.3l-2.9,0.8l-2.6,1.4
				l-2.4,1.8l-2,2.2l-1.7,2.5l-1.2,2.8l-0.8,3.1L130,72.2z"/>
			
				<linearGradient id="路径_16_" gradientUnits="userSpaceOnUse" x1="-118.4048" y1="484.9043" x2="-118.4048" y2="485.7498" gradientTransform="matrix(102 0 0 -30 12245.3125 14730.5313)">
				<stop  offset="0" style="stop-color:#E6E6E6"/>
				<stop  offset="1" style="stop-color:#F5F5F5"/>
			</linearGradient>
			<path id="路径_7_" fill="url(#路径_16_)" d="M207.9,175.5l-1.9-0.2l-3.4-1.6L199.9,171l-1.101-1.7l-1.2-3.8l-0.1-9.5H117
				v13.4l0.3,3l0.7,2.8l1.1,2.6l1.5,2.3l1.9,2l2.1,1.601l2.4,1.3l2.6,0.7l2.8,0.3h71.2l2.801-0.3l2.6-0.7l2.4-1.3l2.1-1.601l1.9-2
				l1.5-2.3l1.1-2.6l0.7-2.8l0.3-8.5l-0.2,2.3l-0.6,2.2l-1.101,2l-1.399,1.8l-1.7,1.5l-1.9,1l-2,0.7L207.9,175.5z"/>
			<path id="矩形" fill="#FFFFFF" d="M146,77h55l1.4,0.6L203,79l-0.2,2.8l-1,1L146,83l-1.4-0.6L144,81l0.2-2.8l1-1L146,77z"/>
			<path id="矩形_1_" fill="#FFFFFF" d="M146,96h55l1.4,0.6L203,98l-0.2,2.8l-1,1L146,102l-1.4-0.6L144,100l0.2-2.8l1-1L146,96z"
				/>
			<path id="矩形_2_" fill="#FFFFFF" d="M146,115h55l1.4,0.6l0.6,1.4l-0.2,2.8l-1,1L146,121l-1.4-0.6L144,119l0.2-2.8l1-1L146,115
				z"/>
			<path id="矩形_3_" fill="#FFFFFF" d="M146,134h33l1.4,0.6l0.6,1.4l-0.2,2.8l-1,1L179,140h-33l-1.4-0.6L144,138l0.2-2.8l1-1
				L146,134z"/>
		</g>
		<g>
			<g>
				
					<linearGradient id="椭圆形_2_" gradientUnits="userSpaceOnUse" x1="-117.6758" y1="489.1553" x2="-116.6758" y2="489.1553" gradientTransform="matrix(46.7 0 0 -46.8 5687.832 23035.6504)">
					<stop  offset="0" style="stop-color:#F5F5F5"/>
					<stop  offset="1" style="stop-color:#C8C8C8"/>
				</linearGradient>
				<path id="椭圆形_1_" fill="url(#椭圆形_2_)" fill-opacity="0.4003" d="M239.101,143.199l-0.2,3.2l-0.6,3l-1,2.9
					l-1.4,2.699l-3.6,4.7l-4.7,3.7l-2.8,1.4l-2.801,1l-3,0.6l-3.3,0.2l-3.2-0.2l-3-0.6l-2.899-1l-5.101-3.101l-4.3-4.2l-1.6-2.5
					l-2.4-5.6l-0.6-3l-0.2-3.2l0.2-3.2l0.6-3l1-2.899l3-5.2l4.3-4.2l5.101-3l2.899-1l3-0.7l3.2-0.199l3.3,0.199l3,0.7l2.801,1
					l2.8,1.3l4.7,3.7l3.6,4.7l1.4,2.7l1,2.899l0.6,3L239.101,143.199z"/>
			</g>
			<path id="路径_8_" fill="#FFFFFF" d="M202.2,146.3l-2.399-0.601l-1.601-1.7l-0.5-1.1v-3.8l1.4-4.9l2-3.5l1.5-1.8l1.899-1.6
				l2.2-1.301l2.4-0.899l2.7-0.601h4.3l2,1.2l1.2,2.101l0.199,1.3l-0.699,2.399l-1.7,1.7L216,133.6l-3.399,0.399l-2.5,0.9l-2,2.3
				l-0.8,2.101l-0.5,3.6l-0.5,1.1l-1.7,1.7L202.2,146.3z"/>
			<path id="矩形_5_" fill="#C4C4C4" d="M235.8,165.9l3.5-3.7l6.8,6.3l-3.5,3.8L235.8,165.9z"/>
		</g>
	</g>
</g>
<linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="185.5" y1="143.2002" x2="246" y2="143.2002">
	<stop  offset="0" style="stop-color:#CCCCCC"/>
	<stop  offset="1" style="stop-color:#A3A3A3"/>
</linearGradient>
<path fill="url(#SVGID_1_)" d="M215.75,112.95c-16.707,0-30.25,13.543-30.25,30.25s13.543,30.25,30.25,30.25
	c16.706,0,30.25-13.543,30.25-30.25S232.456,112.95,215.75,112.95z M215.75,166.47c-12.851,0-23.27-10.419-23.27-23.27
	s10.419-23.27,23.27-23.27s23.27,10.419,23.27,23.27S228.601,166.47,215.75,166.47z"/>
<path fill="#C4C4C4" d="M260.12,189.507c-1.05,1.076-2.809,1.063-3.929-0.028l-16.212-15.816c-1.12-1.092-1.177-2.85-0.126-3.926
	l3.802-3.897c1.05-1.076,2.809-1.063,3.929,0.028l16.212,15.815c1.119,1.093,1.177,2.851,0.126,3.927L260.12,189.507z"/>
</svg>
