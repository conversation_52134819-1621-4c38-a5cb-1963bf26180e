package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * 销售退货单 dto  https://www.yuque.com/manhuo/gf1570/vprm9l
 * <AUTHOR>
 * @version 1.0
 * @description: 预处理金蝶销售退货单 dto  由erp实际业务转换
 * @date
 */
@NoArgsConstructor
@Data
@Getter
@Setter
public class KingDeeSaleBackCommand {


    /**
     * fid
     */
    private Integer fid;
    /**
     * fBillTypeID
     */
    private KingDeeNumberCommand FBillTypeID = new KingDeeNumberCommand();
    /**
     * fBillNo
     */
    private String FBillNo;
    /**
     * fQzokBddjtid
     */
    private String f_QZOK_BDDJTID;
    /**
     * fDate
     */
    private String FDate;
    /**
     * fSaleOrgId
     */
    private KingDeeNumberCommand FSaleOrgId = new KingDeeNumberCommand();
    /**
     * fStockOrgId
     */
    private KingDeeNumberCommand FStockOrgId = new KingDeeNumberCommand();
    /**
     * fRetcustId
     */
    private KingDeeNumberCommand FRetcustId = new KingDeeNumberCommand();
    /**
     * fEntity
     */
    private List<FEntity> FEntity;

    /**
     * FEntity
     */
    @NoArgsConstructor
    @Data
    public static class FEntity {
        /**
         * fmaterialid
         */
        private KingDeeNumberCommand FMATERIALID = new KingDeeNumberCommand();
        /**
         * fRealQty
         */
        private BigDecimal FRealQty;
        /**
         * fstockid
         */
        private KingDeeNumberCommand FSTOCKID = new KingDeeNumberCommand();
        /**
         * ftaxprice
         */
        private BigDecimal FTAXPRICE;
        /**
         * fentrytaxrate
         */
        private BigDecimal FENTRYTAXRATE;
        /**
         * fQzokYsddh
         */
        private String f_QZOK_YSDDH;
        /**
         * fQzokGsywdh
         */
        private String f_QZOK_GSYWDH;
        /**
         * fQzokYwlx
         */
        private String f_QZOK_YWLX;
        /**
         * fQzokPch
         */
        private String f_QZOK_PCH;
        /**
         * fQzokXlh
         */
        private String f_QZOK_XLH;
        /**
         * fQzokBddjhid
         */
        private String f_QZOK_BDDJHID;
        /**
         * fQzokSfzf
         */
        private String f_QZOK_SFZF;
        /**
         * fSrcBillTypeID
         */
        private String FSrcBillTypeID;
        /**
         * fentityLink
         */
        private List<FEntityLink> FEntity_Link;

        /**
         * FEntityLink
         */
        @NoArgsConstructor
        @Data
        public static class FEntityLink {
            /**
             * fLinkId
             */
            private Integer FLinkId;
            /**
             * fentityLinkFruleid
             */
            private String FEntity_Link_FRuleId;
            /**
             * fentityLinkFflowlineid
             */
            private Integer FEntity_Link_FFlowLineId;
            /**
             * fentityLinkFstableid
             */
            private Integer FEntity_Link_FSTableId;
            /**
             * fentityLinkFstablename
             */
            private String FEntity_Link_FSTableName;
            /**
             * fentityLinkFsbillid
             */
            private Integer FEntity_Link_FSBillId;
            /**
             * fentityLinkFsid
             */
            private Integer FEntity_Link_FSId;
            /**
             * fentityLinkFbaseunitqtyold
             */
            private BigDecimal FEntity_Link_FBASEUNITQTYOLD;
            /**
             * fentityLinkFbaseunitqty
             */
            private BigDecimal FEntity_Link_FBASEUNITQTY;
            /**
             * fentityLinkFsalbaseqtyold
             */
            private BigDecimal FEntity_Link_FSALBASEQTYOLD;
            /**
             * fentityLinkFsalbaseqty
             */
            private BigDecimal FEntity_Link_FSALBASEQTY;
        }
    }
}
