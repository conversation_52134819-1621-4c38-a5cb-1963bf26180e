<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="查看修改前的价格" scope="application" />
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<%@ include file="../common/common.jsp"%>

<div class="content">
    <div class="formtitle"></div>
    <div  id="desc_div">
        <form method="post" id="addForm" action="${pageContext.request.contextPath}/price/basePriceMaintain/editBasePrice.do">

            <input type="hidden" name="skuPriceChangeApplyId" id="skuPriceChangeApplyId" value="${skuPriceChangeApplyDto.id}"/>
            <input type="hidden" name="skuNo" id="skuNo" value="${skuPriceChangeApplyDto.skuNo}"/>

            <ul class="payplan">

                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">基本信息</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                            <tr>
                                <th style="width:50px">订货号</th>
                                <th style="width:80px">商品名称</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>${skuPriceChangeApplyDto.skuNo}</td>
                                <td>${skuPriceChangeApplyDto.skuName}</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </li>

                <li>
                    <div class="title-container title-container-blue">

                        <div class="table-title nobor">成本价</div>

                        <a class="title-click addtitle" href="javascript:void(0);" tabTitle='{"num":"customer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
					            "link":"./price/basePriceMaintain/purchasePriceTrace.do?skuId=${skuPriceChangeApplyDto.skuId}","title":"查看轨迹"}'>查看轨迹</a>
                    </div>
                </li>

                <li>
                    <div class="parts ">
                        <div class="title-container">
                            <div class="table-title nobor">已生效</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                            <tr>
                                <th style="width:50px">供应商名称</th>
                                <th style="width:80px">采购成本</th>
                                <th style="width:80px">生效时间</th>
                            </tr>
                            </thead>
                            <tbody>
                                <c:forEach var="purchaseDetail" items="${skuPriceChangeApplyDto.effectPurchaseList}" varStatus="staut">
                                    <tr>
                                        <td>${purchaseDetail.traderName}</td>
                                        <td>${myfn:toString(purchaseDetail.purchasePrice)}</td>
                                        <td>${purchaseDetail.modTime}</td>
                                    </tr>
                                </c:forEach>
                            </tbody>
                        </table>
                    </div>
                </li>

                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">销售价</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                            <tr>
                                <th style="width:50px">市场价</th>
                                <th style="width:80px">终端价</th>
                                <th style="width:80px">经销价</th>
                                <th style="width:80px">集团价</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>
                                    <c:choose>
                                        <c:when test="${skuPriceChangeApplyDto.marketPrice==null}">
                                            ""
                                        </c:when>
                                        <c:otherwise>
                                            ${myfn:toString(skuPriceChangeApplyDto.marketPrice)}
                                        </c:otherwise>
                                    </c:choose>
                                </td>
                                <td>
                                    <c:choose>
                                        <c:when test="${skuPriceChangeApplyDto.terminalPrice==null}">
                                            ""
                                        </c:when>
                                        <c:otherwise>
                                            ${myfn:toString(skuPriceChangeApplyDto.terminalPrice)}
                                        </c:otherwise>
                                    </c:choose>
                                </td>
                                <td>
                                    <c:choose>
                                        <c:when test="${skuPriceChangeApplyDto.distributionPrice==null}">
                                            ""
                                        </c:when>
                                        <c:otherwise>
                                            ${myfn:toString(skuPriceChangeApplyDto.distributionPrice)}
                                        </c:otherwise>
                                    </c:choose>
                                </td>
                                <td>
                                    <c:choose>
                                        <c:when test="${skuPriceChangeApplyDto.groupPrice==null}">

                                        </c:when>
                                        <c:otherwise>
                                            ${myfn:toString(skuPriceChangeApplyDto.groupPrice)}
                                        </c:otherwise>
                                    </c:choose>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </li>

                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">审核记录</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                            <tr>
                                <th style="width:50px">操作人</th>
                                <th style="width:80px">操作时间</th>
                                <th style="width:80px">操作事项</th>
                                <th style="width:80px">备注</th>
                            </tr>
                            </thead>
                            <tbody>
                            <c:forEach var="hi" items="${historicActivityInstance}" varStatus="status">
                                <c:if test="${not empty  hi.activityName}">
                                    <tr>
                                        <td>
                                            <c:choose>
                                                <c:when test="${hi.activityType == 'startEvent'}">
                                                    ${startUser}
                                                </c:when>
                                                <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                                </c:when>
                                                <c:otherwise>
                                                    <c:if test="${historicActivityInstance.size() == status.count}">
                                                        ${verifyUsers}
                                                    </c:if>
                                                    <c:if test="${historicActivityInstance.size() != status.count}">
                                                        ${hi.assignee}
                                                    </c:if>
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                        <td><fmt:formatDate value="${hi.endTime}" pattern="yyyy-MM-dd HH:mm:ss" /></td>
                                        <td>
                                            <c:choose>
                                                <c:when test="${hi.activityType == 'startEvent'}">
                                                    开始
                                                </c:when>
                                                <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                                    结束
                                                </c:when>
                                                <c:otherwise>
                                                    ${hi.activityName}
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                        <td class="font-red">${commentMap[hi.taskId]}</td>
                                    </tr>
                                </c:if>
                            </c:forEach>
                            </tbody>
                        </table>
                    </div>
                </li>
            </ul>
        </form>
    </div>
</div>
<%@ include file="../common/footer.jsp"%>