package com.vedeng.erp.finance.mapper;

import com.vedeng.erp.finance.domain.entity.RPayApplyJCapitalBill;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RPayApplyJCapitalBillMapper {
    int deleteByPrimaryKey(Integer rPayApplyJCapitalBillId);

    int insert(RPayApplyJCapitalBill record);

    int insertOrUpdate(RPayApplyJCapitalBill record);

    int insertOrUpdateSelective(RPayApplyJCapitalBill record);

    int insertSelective(RPayApplyJCapitalBill record);

    RPayApplyJCapitalBill selectByPrimaryKey(Integer rPayApplyJCapitalBillId);

    int updateByPrimaryKeySelective(RPayApplyJCapitalBill record);

    int updateByPrimaryKey(RPayApplyJCapitalBill record);

    int updateBatch(List<RPayApplyJCapitalBill> list);

    int updateBatchSelective(List<RPayApplyJCapitalBill> list);

    int batchInsert(@Param("list") List<RPayApplyJCapitalBill> list);
}