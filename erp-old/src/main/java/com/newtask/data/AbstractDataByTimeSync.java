package com.newtask.data;

import com.newtask.data.DataSyncTemplate;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;

public abstract class AbstractDataByTimeSync implements DataByTimeSync {
    /**
     * 更新状态
     * @param Time
     */
    public void updateStatusProcess(String Time){
        //根据时间加载数据
        String [] splitTime =  Time.split("-");
        Long startTime = System.currentTimeMillis()-10*60*1000;
        Long endTime = System.currentTimeMillis();
        if(splitTime.length>=2){
            startTime =Long.parseLong(splitTime[0]) ;
            endTime =Long.parseLong(splitTime[1]) ;
        }
        XxlJobLogger.log(new Date(startTime)+"--"+new Date(endTime));
        List<Map<String,Object>> map=loadBizByTimeData(startTime,endTime);
        if(!CollectionUtils.isNotEmpty(map)){
            return;
        }
        XxlJobLogger.log("当前业务数据量："+map.size());
        updateData(map);
    }
}
