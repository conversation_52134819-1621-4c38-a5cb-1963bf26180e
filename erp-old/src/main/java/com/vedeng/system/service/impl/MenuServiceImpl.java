package com.vedeng.system.service.impl;

import com.vedeng.authorization.model.Action;
import com.vedeng.authorization.model.Actiongroup;
import com.vedeng.authorization.model.User;
import com.vedeng.common.shiro.cas.CasClientHelper;
import com.vedeng.common.shiro.cas.service.UacAccountFacadeService;
import com.vedeng.system.model.vo.MenuVo;
import com.vedeng.system.service.ActionService;
import com.vedeng.system.service.ActiongroupService;
import com.vedeng.system.service.MenuService;
import com.vedeng.system.service.UserService;
import com.vedeng.uac.api.dto.MenuNodeDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.xml.ws.soap.Addressing;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Service
public class MenuServiceImpl implements MenuService {
    @Resource
    private UserService userService;
    @Autowired(required = false)
    private UacAccountFacadeService uacAccountFacadeService;
    @Resource
    private ActionService actionService;
    @Resource
    private ActiongroupService actiongroupService;

    @Override
    @Transactional(readOnly = true, rollbackFor = Exception.class)
    public List<MenuVo> listMenu(User user) {
        if (user == null) {
            return Collections.emptyList();
        }

        boolean ssoFlag = CasClientHelper.enableSingleSignOn();
        List<MenuVo> resultList = null;
        if (ssoFlag) {
            // sso
            MenuNodeDTO menu = uacAccountFacadeService.getMenu(user.getUserId());
            if (menu != null && menu.getChildren() != null) {
                List<MenuNodeDTO> children = menu.getChildren();
                // 类型转换
                resultList = transformUACMenuNode(children);
            }
        } else {
            // old biz

            List<MenuVo> menu1List = actiongroupService.getMenuActionGroupList(user).stream().map(this::transformActionGroup).collect(Collectors.toList());
            List<MenuVo> menu2List = actionService.getActionList(user,null).stream().map(this::transformAction).collect(Collectors.toList());
            resultList = new ArrayList<>(menu1List.size());
            // 组装树结构
            for (MenuVo menu1 : menu1List) {
                List<MenuVo> children = new LinkedList<>();
                for (MenuVo menu2 : menu2List) {
                    if (!menu2.getLink().endsWith(".do") && !menu2.getLink().contains("ezlist")) {
                        menu2.setLink(menu2.getLink() + ".do");
                    }
                    if (menu2.getParentId().equals(menu1.getMenuId())) {
                        children.add(menu2);
                    }

                }
                menu1.setChildren(children);

                resultList.add(menu1);
            }
            //todo 加载菜单

        }

        return resultList;
    }

    private List<MenuVo> transformUACMenuNode(List<MenuNodeDTO> list) {
        if (list.size() > 0) {
            List<MenuVo> tempList = new ArrayList<>(list.size());
            for (MenuNodeDTO node : list) {
                MenuVo menuVo = new MenuVo();
                menuVo.setMenuId(node.getId());
                menuVo.setName(node.getName());
                menuVo.setLink(node.getLink());
                menuVo.setParentId(node.getParentId());
                menuVo.setSort(node.getSort());
                List<MenuNodeDTO> children = node.getChildren();
                if (children.size() > 0) {
                    List<MenuVo> menuVos = transformUACMenuNode(children);
                    if (CollectionUtils.isNotEmpty(menuVos)) {
                        menuVo.setChildren(menuVos);
                    }
                }

                tempList.add(menuVo);
            }
            return tempList;
        }

        return null;

    }

    private MenuVo transformAction(Action action) {
        MenuVo menuVo = new MenuVo();
        if (Objects.isNull(action)){
            return menuVo;
        }
        menuVo.setMenuId(action.getActionId());
        menuVo.setName(action.getActionDesc());
        menuVo.setParentId(action.getActiongroupId());
        menuVo.setSort(action.getSort());
        StringBuilder builder = new StringBuilder();
        if (StringUtils.startsWith(action.getModuleName(), "http://") || StringUtils.startsWith(action.getModuleName(), "/ezlist/")) {
            menuVo.setLink(action.getModuleName());
        } else {
            menuVo.setLink(builder.append(action.getModuleName()).append("/").append(action.getControllerName()).append("/").append(action.getActionName()).toString());
        }

        return menuVo;
    }

    private MenuVo transformActionGroup(Actiongroup actiongroup) {
        MenuVo menuVo = new MenuVo();
        if (Objects.isNull(actiongroup)){
            return menuVo;
        }
        menuVo.setMenuId(actiongroup.getActiongroupId());
        menuVo.setName(actiongroup.getName());
        menuVo.setParentId(actiongroup.getParentId());
        menuVo.setSort(actiongroup.getOrderNo());
        menuVo.setIconStyle(actiongroup.getIconStyle());
        return menuVo;
    }


}
