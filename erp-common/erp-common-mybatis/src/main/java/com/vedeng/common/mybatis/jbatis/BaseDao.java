package com.vedeng.common.mybatis.jbatis;


import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.vedeng.common.mybatis.jbatis.constant.OperateConstant;
import com.vedeng.common.mybatis.jbatis.db.SqlGenerator;
import com.vedeng.common.mybatis.jbatis.exception.ListIsNullException;
import com.vedeng.common.mybatis.jbatis.exception.PrimaryKeyNotFoundException;
import com.vedeng.common.mybatis.jbatis.utils.EntityUtil;
import com.vedeng.common.mybatis.jbatis.utils.ReflectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * baseDao接口实现
 * T  实体类泛型
 * Pk 实体类主键类型
 *
 * <AUTHOR>
 * @version V1.0
 */
@Slf4j
public class BaseDao<T, PK extends Serializable> implements IBaseDao<T, PK> {

    @Autowired
    private SqlMapper sqlMapper;

    @Autowired
    protected SqlSessionTemplate sqlSessionTemplate;

    /**
     * 实体类类型
     */
    private Class<T> entityClass;

    /**
     * 实体类对应表配置信息
     */
    private final EntityUtil.EntityTable entityTable;

    /**
     * 主键生成策略
     */
    private final String seq;

    /**
     * 对应实体类主键名称
     */
    private final String idName;

    /**
     * 对应实体类主键javaType
     */
    private final Class<?> idJavaType;

    /**
     * 正在使用的dao类
     */
    private final Class<?> dao;

    /**
     * 实体类对应主键的信息
     */
    private final EntityUtil.EntityColumn entityClassPKColumn;


    @SuppressWarnings("unchecked")
    public BaseDao() {
        super();

        this.entityClass = (Class<T>) ReflectionUtils.getClassGenricType(this.getClass());
        this.entityTable = EntityUtil.getEntityTable(this.entityClass);
        this.seq = EntityUtil.getSeqPkType(this.entityClass);
        this.idName = EntityUtil.getIdName(this.entityClass);
        this.idJavaType = EntityUtil.getPkJavaType(this.entityClass);
        if (this.getClass().getInterfaces().length != 0) {
            this.dao = this.getClass().getInterfaces()[0];
        } else {
            this.dao = this.getClass();
        }
        this.entityClassPKColumn = entityTable.getEntityClassPKColumn();

        if (log.isDebugEnabled()) {
            log.debug("the " + this.dao.getSimpleName() + " has been initialized ");
        }

    }


    private String getMsId(String operate) {
        return this.dao.getName() + OperateConstant.SQLNAME_SEPARATOR + operate;
    }

    /**
     * 获取对应的xml对应的Id值
     *
     * @param id id
     * @return String
     */
    protected String getMapperMethodId(String id) {
        return this.dao.getName() + OperateConstant.SQLNAME_SEPARATOR + id;
    }

    @SuppressWarnings("unchecked")
    @Override
    public PK create(T object) {

        if (OperateConstant.SEQ_TYPE_UUID.equals(seq)) {
            Object value = ReflectionUtils.invokeGetterMethod(object, idName);
            if (ObjectUtil.isEmpty(value)) {
                ReflectionUtils.invokeSetterMethod(object, idName, IdUtil.fastSimpleUUID());
            }
            sqlMapper.insert(SqlGenerator.operateSqlByType(OperateConstant.SQLID_INSERT, entityTable), object, getMsId(OperateConstant.SQLID_INSERT));
        }
        if (OperateConstant.SEQ_TYPE_JDBC.equals(seq)) {
            sqlMapper.insertSelectKey(SqlGenerator.operateSqlByType(OperateConstant.SQLID_INSERT, entityTable),
                    object, getMsId(OperateConstant.SQLID_INSERT),
                    entityClassPKColumn);
        }
        if (seq == null) {
            Object value = ReflectionUtils.invokeGetterMethod(object, idName);
            if (ObjectUtil.isEmpty(value)) {
                throw new PrimaryKeyNotFoundException("主键为空，插入失败");
            }
            sqlMapper.insert(SqlGenerator.operateSqlByType(OperateConstant.SQLID_INSERT, entityTable), object, getMsId(OperateConstant.SQLID_INSERT));
        }

        return (PK) ReflectionUtils.getPkObj(object, idName);
    }

    @Override
    public int update(T object) {
        Object value = ReflectionUtils.invokeGetterMethod(object, idName);
        if (ObjectUtil.isEmpty(value)) {
            throw new PrimaryKeyNotFoundException("主键为空，更新失败");
        }
        return sqlMapper.update(SqlGenerator.operateSqlByType(OperateConstant.SQLID_UPDATE, entityTable), object, getMsId(OperateConstant.SQLID_UPDATE));
    }


    @Override
    public int remove(T object) {
        return sqlMapper.update(SqlGenerator.operateSqlByType(OperateConstant.SQLID_DELETE, entityTable), object, getMsId(OperateConstant.SQLID_DELETE));
    }


    @Override
    public int count(T object) {
        return sqlMapper.selectOne(SqlGenerator.operateSqlByType(OperateConstant.SQLID_COUNT_BATCH, entityTable), object, Integer.class, getMsId(OperateConstant.SQLID_COUNT_BATCH));
    }


    @Override
    public boolean createOfBatch(List<T> list) {

        if (list == null || list.size() == 0) {
            throw new ListIsNullException("参数为空，插入失败");
        }
        if (OperateConstant.SEQ_TYPE_UUID.equals(seq)) {
            for (T object : list) {
                Object value = ReflectionUtils.invokeGetterMethod(object, idName);
                if (ObjectUtil.isEmpty(value)) {
                    ReflectionUtils.invokeSetterMethod(object, idName, IdUtil.fastSimpleUUID());
                }
            }
        }
        int count = sqlMapper.insert(SqlGenerator.operateSqlByType(OperateConstant.SQLID_INSERT_BATCH, entityTable), list, getMsId(OperateConstant.SQLID_INSERT_BATCH));
        return count > 0;
    }


    @Override
    public int updateOfBatch(List<T> list) {

        if (list == null || list.size() == 0) {
            throw new ListIsNullException("参数为空，更新失败");
        }
        return sqlMapper.update(SqlGenerator.operateSqlByType(OperateConstant.SQLID_UPDATE_BATCH, entityTable), list, getMsId(OperateConstant.SQLID_UPDATE_BATCH)) / 2;
    }


    @Override
    public int removeOfBatch(List<T> list) {
        return sqlMapper.update(SqlGenerator.operateSqlByType(OperateConstant.SQLID_DELETE_BATCH, entityTable), list, getMsId(OperateConstant.SQLID_DELETE_BATCH));
    }


    @Override
    public List<T> queryByObject(T object) {
        Map<String, Object> criteriaMap = new HashMap<>();
        criteriaMap.put("object", object);
        return sqlMapper.selectList(SqlGenerator.operateSqlByType(OperateConstant.SQLID_SELECT, entityTable), criteriaMap, this.entityClass, getMsId(OperateConstant.SQLID_SELECT));
    }


    @Override
    public List<T> queryByList(List<T> list) {
        Map<String, Object> criteriaMap = new HashMap<>();
        criteriaMap.put("list", list);
        return sqlMapper.selectList(SqlGenerator.operateSqlByType(OperateConstant.SQLID_SELECT_BATCH, entityTable), criteriaMap, this.entityClass, getMsId(OperateConstant.SQLID_SELECT_BATCH));
    }


    @Override
    public List<T> queryListByCriteria(T object, String orderby, Integer offset, Integer limit) {
        Map<String, Object> criteriaMap = new HashMap<>();
        criteriaMap.put("object", object);
        criteriaMap.put("orderby", orderby);
        criteriaMap.put("offset", offset);
        criteriaMap.put("limit", limit);
        return sqlMapper.selectList(SqlGenerator.operateSqlByType(OperateConstant.SQLID_SELECT_CRITERIA, entityTable), criteriaMap, this.entityClass, getMsId(OperateConstant.SQLID_SELECT_CRITERIA));
    }


    @Override
    public List<T> queryListByBatchCriteria(List<T> list, String orderby, Integer offset, Integer limit) {
        Map<String, Object> criteriaMap = new HashMap<>();
        criteriaMap.put("list", list);
        criteriaMap.put("orderby", orderby);
        criteriaMap.put("offset", offset);
        criteriaMap.put("limit", limit);
        return sqlMapper.selectList(SqlGenerator.operateSqlByType(OperateConstant.SQLID_SELECT_BATCH_CRITERIA, entityTable), criteriaMap, this.entityClass, getMsId(OperateConstant.SQLID_SELECT_BATCH_CRITERIA));
    }

    @Override
    public T findById(PK id) {
        if (id == null) {
            throw new PrimaryKeyNotFoundException("主键为空，查询失败");
        }
        return sqlMapper.selectOne(SqlGenerator.operateSqlByType(OperateConstant.SQLID_SELECT_ONEBYID, entityTable), id, this.entityClass, getMsId(OperateConstant.SQLID_SELECT_ONEBYID));
    }

    @Override
    public boolean removeById(PK id) {
        if (id == null) {
            throw new PrimaryKeyNotFoundException("主键为空，删除失败");
        }
        int del = sqlMapper.delete(SqlGenerator.operateSqlByType(OperateConstant.SQLID_DELETE_ONEBYID, entityTable), id, getMsId(OperateConstant.SQLID_DELETE_ONEBYID));
        return del > 0;
    }


}
