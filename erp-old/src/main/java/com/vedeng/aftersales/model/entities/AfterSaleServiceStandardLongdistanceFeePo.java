package com.vedeng.aftersales.model.entities;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @describe 售后服务标准长途费实体类
 * <AUTHOR>
 * @date 2020/12/11
 */
public class AfterSaleServiceStandardLongdistanceFeePo implements Serializable {

    /**
     * 售后长途费主键ID
     */
    private Integer longdistanceFeeId;

    /**
     * 售后长途费类型1.不收取长途费2.收取长途费3.长途费不确定
     */
    private Integer longdistanceFeeType;

    /**
     * 省级ID
     */
    private Integer province;

    /**
     * 市级ID
     */
    private Integer city;

    /**
     * 区级ID
     */
    private Integer region;

    /**
     * 长途费
     */
    private BigDecimal distanceFee;

    /**
     * 是否作废 0 否   1 是
     */
    private Integer isDelete;

    /**
     * 添加人
     */
    private Integer creator;

    /**
     * 最近一次编辑人
     */
    private Integer updator;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 更新时间
     */
    private Long modTime;


    public Integer getLongdistanceFeeId() {
        return longdistanceFeeId;
    }

    public void setLongdistanceFeeId(Integer longdistanceFeeId) {
        this.longdistanceFeeId = longdistanceFeeId;
    }

    public Integer getLongdistanceFeeType() {
        return longdistanceFeeType;
    }

    public void setLongdistanceFeeType(Integer longdistanceFeeType) {
        this.longdistanceFeeType = longdistanceFeeType;
    }

    public Integer getProvince() {
        return province;
    }

    public void setProvince(Integer province) {
        this.province = province;
    }

    public Integer getCity() {
        return city;
    }

    public void setCity(Integer city) {
        this.city = city;
    }

    public Integer getRegion() {
        return region;
    }

    public void setRegion(Integer region) {
        this.region = region;
    }

    public BigDecimal getDistanceFee() {
        return distanceFee;
    }

    public void setDistanceFee(BigDecimal distanceFee) {
        this.distanceFee = distanceFee;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    public Long getModTime() {
        return modTime;
    }

    public void setModTime(Long modTime) {
        this.modTime = modTime;
    }
}
