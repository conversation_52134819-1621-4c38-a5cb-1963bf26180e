package com.vedeng.order.dao;

import com.vedeng.order.model.SaleorderModifyApply;
import com.vedeng.order.model.SaleorderModifyApplyGoods;

import java.util.List;

public interface SaleorderModifyApplyGoodsMapper {
    int deleteByPrimaryKey(Integer saleorderModifyApplyGoodsId);

    int insert(SaleorderModifyApplyGoods record);

    int insertSelective(SaleorderModifyApplyGoods record);

    SaleorderModifyApplyGoods selectByPrimaryKey(Integer saleorderModifyApplyGoodsId);

    int updateByPrimaryKeySelective(SaleorderModifyApplyGoods record);

    int updateByPrimaryKey(SaleorderModifyApplyGoods record);

    /**
     * <b>Description:</b><br> 获取订单修改产品信息
     * @param saleorderModifyApply
     * @return
     * @Note
     * <b>Author:</b> leo.yang
     * <br><b>Date:</b> 2018年1月17日 下午4:23:36
     */
	List<SaleorderModifyApplyGoods> getSaleorderModifyApplyGoodsById(SaleorderModifyApply saleorderModifyApply);

    int updateModifyApplyGoodsByIdAndGoodsId(SaleorderModifyApplyGoods saleorderModifyApplyGoods);
}