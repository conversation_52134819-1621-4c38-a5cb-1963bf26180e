<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="商机列表" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<div class="tcenter" style="background-color: #f3f3f3;padding:10px">
    <div style="font-size: 18px;font-weight: 700;display: inline-block;">商机关联</div>
</div>
<div class="searchfunc">
    <form method="post"
          action="${pageContext.request.contextPath}/order/bussinesschance/linkBncChoosePage.do"
          id="search">
        <input type="hidden" id="traderId" name="traderId" value="${bussinessChanceVo.traderId}">
        <input type="hidden" id="linkStr" name="bncLink" value="${bussinessChanceVo.bncLink}">
        <input type="hidden" id="first" name="first" value="0">
        <input type="hidden" id="exceptId" name="exceptId" value="${exceptId}">
        <ul>
            <li>
                <label class="infor_name">商机编号</label>
                <input type="text"
                       class="input-middle" name="bussinessChanceNo"
                       id="bussinessChanceNo"
                       value="${bussinessChanceVo.bussinessChanceNo }"/>
            </li>
            <li><label class="infor_name">商机类型</label> <select
                    class="input-middle" name="type">
                <option value="0">全部</option>
                <c:forEach items="${typeList }" var="type">
                    <option value="${type.sysOptionDefinitionId }"
                            <c:if test="${type.sysOptionDefinitionId == bussinessChanceVo.type}">selected="selected"</c:if>>${type.title }</option>
                </c:forEach>
            </select>
            </li>
            <li>
                <label class="infor_name">商机来源</label>
                <select class="input-middle" name="source">
                    <option value="0">全部</option>
                    <c:forEach items="${sourceList }" var="source">
                        <option value="${source.sysOptionDefinitionId }"
                                <c:if test="${source.sysOptionDefinitionId == bussinessChanceVo.source}">selected="selected"</c:if>>${source.title }</option>
                    </c:forEach>
                </select>
            </li>
            <li><label class="searchItem infor_name">商机阶段</label> <select
                    class="input-middle" name="bussinessStage">
                <option value="">全部</option>
                <c:forEach items="${bussinessStageList }" var="bussinessStage">
                    <option value="${bussinessStage.sysOptionDefinitionId }"
                            <c:if test="${bussinessStage.sysOptionDefinitionId == bussinessChanceVo.bussinessStage}">selected="selected"</c:if>>${bussinessStage.title }</option>
                </c:forEach>
            </select></li>
            <li>
                <label class="searchItem infor_name">客户名称</label>
                <input type="text"
                       class="input-middle" name="traderName" id="traderName"
                       value="${bussinessChanceVo.traderName }" />
            </li>
            <li><label class="searchItem infor_name">联系方式</label>
                <input type="text"
                       class="input-middle" name="traderContactName" id="traderContactName"
                       value="${bussinessChanceVo.traderContactName }"
                       placeholder="联系人/手机号/电话/其他联系方式" /></li>
            <li><label class="searchItem infor_name">产品名称</label>
                <input type="text"
                       class="input-middle" name="goodsName" id="goodsName"
                       value="${bussinessChanceVo.goodsName }" /></li>
            <li><label class="searchItem infor_name">询价产品</label>
                <input type="text"
                       class="input-middle" name="content" id="content"
                       value="${bussinessChanceVo.content }" /></li>
            <li>
                <div class="infor_name specialinfor">
                    <select name="timeType">
                        <option value="1"
                                <c:if test="${bussinessChanceVo.timeType == 1 }">selected="selected"</c:if>>创建时间</option>
                        <option value="2"
                                <c:if test="${bussinessChanceVo.timeType == 2 }">selected="selected"</c:if>>分配时间</option>
                        <option value="3"
                                <c:if test="${bussinessChanceVo.timeType == 3 }">selected="selected"</c:if>>报价时间</option>
                        <option value="4"
                                <c:if test="${bussinessChanceVo.timeType == 4 }">selected="selected"</c:if>>订单时间</option>
                        <option value="5"
                                <c:if test="${bussinessChanceVo.timeType == 5 }">selected="selected"</c:if>>关闭时间</option>
                    </select>
                </div> <input class="Wdate f_left input-smaller96 m0" type="text"
                              placeholder="请选择日期"
                              onClick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endtime\')}'})" autocomplete="off"
                              name="starttime" id="starttime"
                              value="${bussinessChanceVo.starttime }">
                <div class="f_left ml1 mr1 mt4">-</div> <input
                    class="Wdate f_left input-smaller96" type="text" placeholder="请选择日期"
                    onClick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'starttime\')}'})" autocomplete="off"
                    name="endtime" id="endtime" value="${bussinessChanceVo.endtime }">
            </li>
            <li>
                <label class="searchItem infor_name">预计成单时间</label>
                <input class="Wdate f_left input-smaller96 m0" type="text"
                       placeholder="请选择日期"
                       onClick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'cdendtime\')}'})" autocomplete="off"
                       name="cdstarttime" id="cdstarttime"
                       value="${bussinessChanceVo.cdstarttime }">
                <div class="f_left ml1 mr1 mt4">-</div> <input
                    class="Wdate f_left input-smaller96" type="text" placeholder="请选择日期"
                    onClick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'cdstarttime\')}'})" autocomplete="off"
                    name="cdendtime" id="cdendtime" value="${bussinessChanceVo.cdendtime }">
            </li>
        </ul>
        <div class="tcenter">
			<span class="bt-small bg-light-blue bt-bg-style mr20 "
                  onclick="search();" id="searchSpan">搜索</span> <span
                class="bt-small bg-light-blue bt-bg-style mr20" onclick="reset();">重置</span>

            <!-- <span class="bg-light-blue bt-bg-style bt-small" onclick="exportList()">导出列表</span> -->
        </div>
    </form>
</div>
<div class="content">
    <div >
        <div style="width:100%">
            <table
                    class="table table-bordered table-striped table-condensed table-centered">
                <thead>
                <tr>
                    <th class="wid2">选择</th>
                    <th class="wid4">商机编号</th>
                    <th class="wid3">商机类型</th>
                    <th class="wid3">商机来源</th>
                    <th class="wid1">商机等级</th>
                    <th class="wid3">预计金额</th>
                    <th class="wid2">成单几率</th>
                    <th class="wid6" >客户名称</th>
                    <th class="wid4">手机号</th>
                    <th class="wid8">询价产品</th>
                    <th class="wid3">商机阶段</th>
                    <th class="wid3">商机状态</th>
                    <th class="wid3">分配时间</th>
                    <th class="wid3">商机初次查看时间</th>
                    <th class="wid3">报价单号</th>
                </tr>
                </thead>
                <tbody>
                <c:if test="${not empty bussinessChanceList}">
                    <c:forEach items="${bussinessChanceList }" var="bussinessChance">
                        <tr>
                            <td>
                                <c:if test="${(bussinessChance.status ==0 || bussinessChance.status ==6)&& bussinessChance.bussinessChanceId ne exceptId }">
                                <input type="checkbox" name="bussinessChanceId" value="${bussinessChance.bussinessChanceId }" id="${bussinessChance.bussinessChanceNo}">
                                </c:if>
                                <c:if test="${bussinessChance.status ==1 || bussinessChance.status ==2 || bussinessChance.bussinessChanceId eq exceptId}">
                                    <input type="checkbox" disabled="disabled">
                                </c:if>
                            </td>
                            <td>
                                <div class="font-blue">
                                    <c:if test="${bussinessChance.flag eq 1}">
                                        <span style="color: red;font-size: 16px">！</span>
                                    </c:if>
                                    <c:if test="${bussinessChance.mergeStatus==2}">
                                        <font color="red">[合]</font>
                                    </c:if>
                                    <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"view<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
												"link":"./order/bussinesschance/toSalesDetailPage.do?bussinessChanceId=${bussinessChance.bussinessChanceId}&traderId=${bussinessChance.traderId }",
												"title":"销售商机详情"}'>${bussinessChance.bussinessChanceNo }${bussinessChance.verifyStatus == 0 && fn:contains(bussinessChance.verifyUsername, curr_user.username) ?"<font color='red'>[审]</font>":""}</a>
                                </div>
                            </td>
                            <td>${bussinessChance.typeName }</td>
                            <!-- 2018-8-22 新增(商机来源)-->
                            <td>${bussinessChance.sourceName }</td>

                            <td>
                                <c:forEach items="${bussinessLevelList }" var="bussinessLevel">
                                    <c:if test="${bussinessLevel.sysOptionDefinitionId == bussinessChance.bussinessLevel}">${bussinessLevel.title }</c:if>
                                </c:forEach>
                            </td>
                            <td>${bussinessChance.amount }</td>
                            <td>
                                <c:forEach items="${orderRateList }" var="orderRate">
                                    <c:if test="${orderRate.sysOptionDefinitionId == bussinessChance.orderRate}">${orderRate.title }</c:if>
                                </c:forEach>
                            </td>
                            <td>
                                <c:choose>
                                    <c:when test="${bussinessChance.traderId != null and bussinessChance.traderId > 0}">
											<span class="font-blue">
												<a class="addtitle" href="javascript:void(0);"
                                                   tabTitle='{"num":"viewcustomer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
														"link":"./trader/customer/baseinfo.do?traderId=${bussinessChance.traderId}",
														"title":"客户信息"}'>${bussinessChance.traderName}</a>
											</span>
                                    </c:when>
                                    <c:otherwise>
                                        ${bussinessChance.traderName}
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td>
                                <c:choose>
                                    <c:when test="${bussinessChance.checkMobile != null}">
                                        <i class="icontel cursor-pointer" title="点击拨号" onclick="callout('${bussinessChance.checkMobile}',${bussinessChance.traderId},1,1,${bussinessChance.bussinessChanceId},${bussinessChance.traderContactId});"></i>
                                        ${bussinessChance.checkMobile}
                                    </c:when>
                                    <c:when test="${bussinessChance.mobile != null}">
                                        <i class="icontel cursor-pointer" title="点击拨号" onclick="callout('${bussinessChance.mobile}',0,1,1,${bussinessChance.bussinessChanceId},0);"></i>
                                        ${bussinessChance.mobile}
                                    </c:when>
                                </c:choose>
                            </td>
                            <td style="word-wrap:break-word;">${bussinessChance.content }</td>
                            <td>
                                <c:choose>
                                    <c:when test="${bussinessChance.bussinessStage == 944}">
                                        预算阶段
                                    </c:when>
                                    <c:when test="${bussinessChance.bussinessStage == 945}">
                                        方案制定
                                    </c:when>
                                    <c:when test="${bussinessChance.bussinessStage == 946}">
                                        比价阶段
                                    </c:when>
                                    <c:when test="${bussinessChance.bussinessStage == 947}">
                                        合同签署
                                    </c:when>
                                </c:choose>
                            </td>
                            <td>
                                <c:choose>
                                    <c:when test="${bussinessChance.status eq '0'}">
                                        <span class="warning-color1">未处理</span>
                                    </c:when>
                                    <c:when test="${bussinessChance.status eq '1'}">
                                        <span class="success-color1">报价中</span>
                                    </c:when>
                                    <c:when test="${bussinessChance.status eq '2'}">
                                        <span class="success-color1">已报价</span>
                                    </c:when>
                                    <c:when test="${bussinessChance.status eq '3'}">
                                        <span class="success-color1">已订单</span>
                                    </c:when>
                                    <c:when test="${bussinessChance.status eq '4'}">
                                        已关闭
                                    </c:when>
                                </c:choose>
                            </td>
                            <td><date:date value="${bussinessChance.assignTime} " /></td>
                            <td>
                                <c:choose>
                                    <c:when test="${bussinessChance.type == 392}">
                                        <date:date value="${bussinessChance.addTime} " />
                                    </c:when>
                                    <c:otherwise>
                                        <date:date value="${bussinessChance.firstViewTime} " />
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td>
                                <c:choose>
                                    <c:when test="${bussinessChance.quoteValidStatus eq 0}">
                                        <a class="addtitle"
                                           tabtitle="{&quot;num&quot;:&quot;viewQuote2<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>&quot;,&quot;link&quot;:&quot;./order/quote/getQuoteDetail.do?quoteorderId=${bussinessChance.quoteorderId}&viewType=2&quot;,&quot;title&quot;:&quot;编辑报价&quot;}">${bussinessChance.quoteorderNo}</a>
                                    </c:when>
                                    <c:otherwise>
                                        <a class="addtitle"
                                           tabtitle="{&quot;num&quot;:&quot;viewQuote3<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>&quot;,&quot;link&quot;:&quot;./order/quote/getQuoteDetail.do?quoteorderId=${bussinessChance.quoteorderId}&viewType=3&quot;,&quot;title&quot;:&quot;报价信息&quot;}">${bussinessChance.quoteorderNo}</a>
                                    </c:otherwise>
                                </c:choose>
                                    <%-- <a class="addtitle"
                                        tabtitle="{&quot;num&quot;:&quot;viewQuote3<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>&quot;,&quot;link&quot;:&quot;./order/quote/getQuoteDetail.do?quoteorderId=${bussinessChance.quoteorderId}&viewType=3&quot;,&quot;title&quot;:&quot;报价信息&quot;}">${bussinessChance.quoteorderNo }</a> --%>
                            </td>
                        </tr>
                    </c:forEach>
                </c:if>
                <c:if test="${empty bussinessChanceList }">
                    <!-- 查询无结果弹出 -->
                    <tr>
                        <td colspan="14">查询无结果！</td>
                    </tr>
                </c:if>
                </tbody>
            </table>

        </div>
    </div>
    <div>
        <tags:page page="${page}" />
    </div>
    <div class="clear"></div>
    <div class="add-tijiao tcenter" style="background-color: #f3f3f3;padding:10px">
        <span  class="bt-small bg-light-blue bt-bg-style mr20" onclick="chooseCallBack()">提交</span>
        <button class="dele" id="close-layer" type="button" >取消</button>
    </div>
</div>
<script>
    $(function () {


        $("input[name='bussinessChanceId']").click(function(){
            var linkArr=window.parent.linkArr;
            if(!$(this).prop("checked")){
                    // var linkArr=JSON.parse(linkStr);
                    if(linkArr==undefined||linkArr.length==0){
                        return;
                    }
                    for(var i=0;i<linkArr.length;i++){
                        var id = parseInt($(this).val());
                        if(id==linkArr[i].id){
                            linkArr.splice(i,1);
                        }
                    }
                    if(linkArr.length==0){
                        window.parent.linkArr=[];
                        $("#linkStr").val(undefined)
                    }else{
                        $("#linkStr").val(JSON.stringify(linkArr))
                        window.parent.linkArr=linkArr;
                    }


            }else{
                // var linkArr=[];
                // if(linkStr&&linkStr.length>4) {
                //      linkArr = JSON.parse(linkStr);
                // }
                var id = parseInt($(this).val());
                var no = $(this).attr("id");
                var bnc={"id":id,"no":no};
                linkArr.push(bnc);
                $("#linkStr").val(JSON.stringify(linkArr));
                window.parent.linkArr=linkArr;
            }
        });
        initChecked();

    })

    function initChecked(){
        // if($("input[name='bussinessChanceId']:checked").length == 0){
        //     return;
        // }
        // var first=$("#first").val();
        // var linkStr
        // if("1"==first) {
        //     linkStr = decodeURIComponent(decodeURI($("#linkStr").val()), "UTF-8")
        //     $("#linkStr").val(linkStr);
        //     $("#first").val(undefined)
        // }else{
        //     linkStr =$("#linkStr").val();
        // }
        // if(linkStr&&linkStr.length>5){
            var linkArr=window.parent.linkArr;
            $.each($("input[name='bussinessChanceId']"),function(i,n){
                var id = parseInt($(this).val());
                _this=this;
                linkArr.forEach(function (e) {
                    if(e.id==id){
                        $(_this).prop("checked",true);
                    }
                })
            });

        // }
    }
    function chooseCallBack() {
        // var linkStr=$("#linkStr").val();
        // if(!linkStr||linkStr.length<5){
        //     layer.alert("请勾选商机");
        //     return false;
        // }
        var linkArr=window.parent.linkArr;
        if(linkArr==undefined||linkArr.length==0){
            layer.alert("请勾选商机");
            return false;
        }
        window.parent.linkBnc(JSON.stringify(window.parent.linkArr));
        layer.closeAll()
    }
</script>
