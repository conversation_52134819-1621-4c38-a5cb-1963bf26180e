function page(data) {
    return axios({
        url: '/buyOrder/rebateChargeApply/api/page.do',
        method: 'post',
        data: data
    })
}

function add(data) {
    return axios({
        url: '/buyOrder/rebateChargeApply/api/add.do',
        method: 'post',
        data: data
    })
}

function getDetailById(param) {
    return axios({
        url: '/buyOrder/rebateChargeApply/api/findById.do',
        method: 'post',
        params: param
    })
}

function doAudit(data) {
    return axios({
        url: '/buyOrder/rebateChargeApply/api/audit.do',
        method: 'post',
        data: data
    })
}

function getAllHeadUser(param) {
    return axios({
        url: '/user/getByPositionType.do',
        method: 'post',
        params: param
    })
}

function getBrand(param) {
    return axios({
        url: '/goodsBandApi/queryBrand.do',
        method: 'post',
        params: param
    })
}