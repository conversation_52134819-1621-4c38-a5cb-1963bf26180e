<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.DwhCustomerTagCpmAddDfMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.DwhCustomerTagCpmAddDfEntity">
    <!--@mbg.generated-->
    <!--@Table DWH_CUSTOMER_TAG_CPM_ADD_DF-->
    <result column="ID" jdbcType="BIGINT" property="id" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_CUSTOMER_ID" jdbcType="INTEGER" property="traderCustomerId" />
    <result column="AREA_IDS" jdbcType="VARCHAR" property="areaIds" />
    <result column="SKU_TYPES" jdbcType="VARCHAR" property="skuTypes" />
    <result column="RELATIONS" jdbcType="VARCHAR" property="relations" />
    <result column="ETL_DAY" jdbcType="DATE" property="etlDay" />
  </resultMap>

  <resultMap id="BaseResultMapDto" type="com.vedeng.erp.trader.dto.DwhCustomerTagCpmAddDfDto">
    <!--@mbg.generated-->
    <!--@Table DWH_CUSTOMER_TAG_CPM_ADD_DF-->
    <result column="ID" jdbcType="BIGINT" property="id" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_CUSTOMER_ID" jdbcType="INTEGER" property="traderCustomerId" />
    <result column="AREA_IDS" jdbcType="VARCHAR" property="areaIds" />
    <result column="SKU_TYPES" jdbcType="VARCHAR" property="skuTypes" />
    <result column="RELATIONS" jdbcType="VARCHAR" property="relations" />
    <result column="ETL_DAY" jdbcType="DATE" property="etlDay" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, TRADER_ID, TRADER_CUSTOMER_ID, AREA_IDS, SKU_TYPES, RELATIONS, ETL_DAY
  </sql>
  <insert id="insert" parameterType="com.vedeng.erp.trader.domain.entity.DwhCustomerTagCpmAddDfEntity">
    <!--@mbg.generated-->
    insert into DWH_CUSTOMER_TAG_CPM_ADD_DF (ID, TRADER_ID, TRADER_CUSTOMER_ID, 
      AREA_IDS, SKU_TYPES, RELATIONS, 
      ETL_DAY)
    values (#{id,jdbcType=BIGINT}, #{traderId,jdbcType=INTEGER}, #{traderCustomerId,jdbcType=INTEGER}, 
      #{areaIds,jdbcType=VARCHAR}, #{skuTypes,jdbcType=VARCHAR}, #{relations,jdbcType=VARCHAR}, 
      #{etlDay,jdbcType=DATE})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.erp.trader.domain.entity.DwhCustomerTagCpmAddDfEntity">
    <!--@mbg.generated-->
    insert into DWH_CUSTOMER_TAG_CPM_ADD_DF
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderCustomerId != null">
        TRADER_CUSTOMER_ID,
      </if>
      <if test="areaIds != null">
        AREA_IDS,
      </if>
      <if test="skuTypes != null">
        SKU_TYPES,
      </if>
      <if test="relations != null">
        RELATIONS,
      </if>
      <if test="etlDay != null">
        ETL_DAY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderCustomerId != null">
        #{traderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="areaIds != null">
        #{areaIds,jdbcType=VARCHAR},
      </if>
      <if test="skuTypes != null">
        #{skuTypes,jdbcType=VARCHAR},
      </if>
      <if test="relations != null">
        #{relations,jdbcType=VARCHAR},
      </if>
      <if test="etlDay != null">
        #{etlDay,jdbcType=DATE},
      </if>
    </trim>
  </insert>

<!--auto generated by MybatisCodeHelper on 2024-04-01-->
  <select id="findByAll" resultMap="BaseResultMapDto">
    select
    <include refid="Base_Column_List"/>
    from DWH_CUSTOMER_TAG_CPM_ADD_DF
    <where>
      <if test="id != null">
        and ID=#{id,jdbcType=BIGINT}
      </if>
      <if test="traderId != null">
        and TRADER_ID=#{traderId,jdbcType=INTEGER}
      </if>
      <if test="traderCustomerId != null">
        and TRADER_CUSTOMER_ID=#{traderCustomerId,jdbcType=INTEGER}
      </if>
      <if test="areaIds != null">
        and AREA_IDS=#{areaIds,jdbcType=VARCHAR}
      </if>
      <if test="skuTypes != null">
        and SKU_TYPES=#{skuTypes,jdbcType=VARCHAR}
      </if>
      <if test="relations != null">
        and RELATIONS=#{relations,jdbcType=VARCHAR}
      </if>
      <if test="etlDay != null">
        and ETL_DAY=#{etlDay,jdbcType=DATE}
      </if>
    </where>
  </select>
</mapper>