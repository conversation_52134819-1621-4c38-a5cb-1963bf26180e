<%--
  Created by IntelliJ IDEA.
  User: lecon
  Date: 2020-08-13
  Time: 04:50
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<html>
<head>
    <title>属性迁移列表</title>
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/css/meinian/layui.css">
</head>
<style type="text/css">
    .div-spu {
        margin-top: 20px;
        margin-bottom: 10px;
        margin-left: 5px;
        margin-right: 5px;
    }
</style>
<body>
<div class="div-spu">
    <div>
        <div class="div-spu" style="overflow:auto;">
            <table id="spuConfirmTable" class="layui-table">
                <colgroup>
                    <col width="100">
                    <col width="150">
                    <col>
                </colgroup>
                <thead>
                <tr>
                    <th>属性ID</th>
                    <th>属性名</th>
                    <th>属性值</th>
                </tr>
                </thead>
                <tbody>
                <c:if test="${not empty spuRemovalDetailList}">
                    <c:forEach var="spuRemovalDetail" items="${spuRemovalDetailList}" varStatus="status">
                        <tr>
                            <td>${spuRemovalDetail.attributeId}</td>
                            <td>${spuRemovalDetail.attributeName}</td>
                            <td>${spuRemovalDetail.ownAttributeValueNames}</td>
                        </tr>
                    </c:forEach>
                </c:if>
                <c:if test="${ empty spuRemovalDetailList}">
                    <tr>
                        <td colspan="3"  align='center' valian='middle'>
                            <div><i class="vd-icon icon-caution1"></i></div>
                            当前无增补属性
                        </td>
                    </tr>
                </c:if>
                </tbody>
            </table>
        </div>
        <%--<div class="div-spu">--%>
            <%--<div style="float: right;">--%>
                <%--<label>合计：</label>&nbsp;&nbsp;<label>属性：</label><span id="attrFinalCount">0</span>&nbsp;&nbsp;<label>属性值：</label><span--%>
                    <%--id="attrValueFinalCount">0</span>--%>
            <%--</div>--%>
        <%--</div>--%>
    </div>
</div>
</body>
</html>
