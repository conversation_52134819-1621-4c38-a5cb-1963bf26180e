<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smallhospital.dao.ElContractSkuMapper">
  <resultMap id="BaseResultMap" type="com.smallhospital.model.ElContractSku">
    <id column="EL_CONTRACT_SKU_ID" jdbcType="INTEGER" property="elContractSkuId" />
    <result column="CONTRACT_ID" jdbcType="INTEGER" property="contractId" />
    <result column="SKU_ID" jdbcType="INTEGER" property="skuId" />
    <result column="CONTRACT_PRICE" jdbcType="DECIMAL" property="contractPrice" />
    <result column="SPU_ID" jdbcType="INTEGER" property="spuId" />
    <result column="PRICE" jdbcType="DECIMAL" property="price" />
    <result column="SHOW_NAME" jdbcType="VARCHAR" property="showName" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />

    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="UPDATE_TIME" jdbcType="BIGINT" property="updateTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATOR" jdbcType="INTEGER" property="updator" />
  </resultMap>


  <sql id="Base_Column_List">
    EL_CONTRACT_SKU_ID, CONTRACT_ID, SKU_ID, CONTRACT_PRICE, REMARK,  ADD_TIME, UPDATE_TIME, CREATOR, UPDATOR
  </sql>

  <select id="findByContractId" parameterType="java.lang.Integer" resultType="com.smallhospital.model.vo.ElContractSkuVO">
    select
    T.EL_CONTRACT_SKU_ID, T.CONTRACT_ID, T.SKU_ID,T.PRICE, T.CONTRACT_PRICE, T.REMARK,  T.ADD_TIME, T.UPDATE_TIME, T.CREATOR, T.UPDATOR, V.SPU_ID,V.SHOW_NAME
    from T_EL_CONTRACT_SKU T
    LEFT JOIN V_CORE_SKU V ON T.SKU_ID=V.SKU_ID
    where T.CONTRACT_ID = #{contractId,jdbcType=INTEGER}
  </select>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_EL_CONTRACT_SKU
    where EL_CONTRACT_SKU_ID = #{elContractSkuId,jdbcType=INTEGER}
  </select>

    <select id="getElContractSkuBySkuId" resultType="com.smallhospital.model.ElContractSku">
      SELECT
        *
      FROM
          T_EL_CONTRACT_SKU
      WHERE
          sku_id in
        <foreach collection="skuList" index="index" item="item" separator="," open="(" close=")">
          #{item}
        </foreach>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_EL_CONTRACT_SKU
    where EL_CONTRACT_SKU_ID = #{elContractSkuId,jdbcType=INTEGER}
  </delete>

  <delete id="deleteByContractId" parameterType="java.lang.Integer">
    delete from T_EL_CONTRACT_SKU
    where CONTRACT_ID = #{contractId,jdbcType=INTEGER}
  </delete>


  <insert id="batchAddContractSkus" parameterType="java.util.List">
    INSERT INTO T_EL_CONTRACT_SKU (CONTRACT_ID, SKU_ID, PRICE,CONTRACT_PRICE, REMARK,ADD_TIME, UPDATE_TIME, CREATOR, UPDATOR)
    VALUES
    <foreach collection="list" item="sku" separator=",">
      (
        #{sku.contractId},
        #{sku.skuId},
        #{sku.price},
        #{sku.contractPrice},
        #{sku.remark},
        #{sku.addTime},
        #{sku.updateTime},
        #{sku.creator},
        #{sku.updator}
      )
    </foreach>
  </insert>

  <insert id="insert" keyColumn="EL_CONTRACT_SKU_ID" keyProperty="elContractSkuId" parameterType="com.smallhospital.model.ElContractSku" useGeneratedKeys="true">
    insert into T_EL_CONTRACT_SKU (CONTRACT_ID, SKU_ID,CONTRACT_PRICE,
      REMARK, ADD_TIME, UPDATE_TIME,CREATOR, UPDATOR)
    values (#{contractId,jdbcType=INTEGER}, #{skuId,jdbcType=INTEGER}, #{contractPrice,jdbcType=DECIMAL}, 
      #{remark,jdbcType=VARCHAR}, #{addTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT},#{creator,jdbcType=INTEGER}, #{updator,jdbcType=INTEGER})
  </insert>

  <insert id="insertSelective" keyColumn="EL_CONTRACT_SKU_ID" keyProperty="elContractSkuId" parameterType="com.smallhospital.model.ElContractSku" useGeneratedKeys="true">
    insert into T_EL_CONTRACT_SKU
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="contractId != null">
        CONTRACT_ID,
      </if>
      <if test="skuId != null">
        SKU_ID,
      </if>
      <if test="contractPrice != null">
        CONTRACT_PRICE,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updator != null">
        UPDATOR,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="contractId != null">
        #{contractId,jdbcType=INTEGER},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=INTEGER},
      </if>
      <if test="contractPrice != null">
        #{contractPrice,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updator != null">
        #{updator,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.smallhospital.model.ElContractSku">
    update T_EL_CONTRACT_SKU
    <set>
      <if test="contractId != null">
        CONTRACT_ID = #{contractId,jdbcType=INTEGER},
      </if>
      <if test="skuId != null">
        SKU_ID = #{skuId,jdbcType=INTEGER},
      </if>
      <if test="contractPrice != null">
        CONTRACT_PRICE = #{contractPrice,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updator != null">
        UPDATOR = #{updator,jdbcType=INTEGER},
      </if>
    </set>
    where EL_CONTRACT_SKU_ID = #{elContractSkuId,jdbcType=INTEGER}
  </update>

  <update id="batchUpdateContractSkus" parameterType="java.util.List">
    update T_EL_CONTRACT_SKU SET PRICE=
    <foreach collection="list" item="item" index="index"
             separator=" " open="case" close="end">
      when SKU_ID=#{item.productId} then #{item.price}
    </foreach>
    where SKU_ID in
    <foreach collection="list" index="index" item="item"
             separator="," open="(" close=")">
      #{item.productId,jdbcType=INTEGER}
    </foreach>
  </update>

</mapper>