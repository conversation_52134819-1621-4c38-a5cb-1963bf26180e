<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.billsync.dao.generate.BankBillMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.billsync.task.model.entity.generate.BankBillDo">
    <id column="BANK_BILL_ID" jdbcType="INTEGER" property="bankBillId" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="BANK_TAG" jdbcType="BOOLEAN" property="bankTag" />
    <result column="TRAN_FLOW" jdbcType="VARCHAR" property="tranFlow" />
    <result column="TRANDATE" jdbcType="DATE" property="trandate" />
    <result column="TRANTIME" jdbcType="TIME" property="trantime" />
    <result column="REAL_TRANDATE" jdbcType="DATE" property="realTrandate" />
    <result column="REAL_TRANDATETIME" jdbcType="TIMESTAMP" property="realTrandatetime" />
    <result column="CRE_TYP" jdbcType="VARCHAR" property="creTyp" />
    <result column="CRE_NO" jdbcType="VARCHAR" property="creNo" />
    <result column="MESSAGE" jdbcType="VARCHAR" property="message" />
    <result column="AMT" jdbcType="DECIMAL" property="amt" />
    <result column="AMT1" jdbcType="VARCHAR" property="amt1" />
    <result column="FLAG1" jdbcType="BOOLEAN" property="flag1" />
    <result column="ACCNO2" jdbcType="VARCHAR" property="accno2" />
    <result column="ACC_BANKNO" jdbcType="VARCHAR" property="accBankno" />
    <result column="ACC_NAME1" jdbcType="VARCHAR" property="accName1" />
    <result column="FLAG2" jdbcType="BOOLEAN" property="flag2" />
    <result column="BFLOW" jdbcType="VARCHAR" property="bflow" />
    <result column="DET_NO" jdbcType="VARCHAR" property="detNo" />
    <result column="DET" jdbcType="VARCHAR" property="det" />
    <result column="RLTV_ACCNO" jdbcType="VARCHAR" property="rltvAccno" />
    <result column="CADBANK_NM" jdbcType="VARCHAR" property="cadbankNm" />
    <result column="STATUS" jdbcType="BOOLEAN" property="status" />
    <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
    <result column="MATCHED_AMOUNT" jdbcType="DECIMAL" property="matchedAmount" />
    <result column="OVRLSTTN_TRCK_NO" jdbcType="VARCHAR" property="ovrlsttnTrckNo" />
    <result column="IS_CONSISTENCY" jdbcType="BOOLEAN" property="isConsistency" />
    <result column="MATCHED_OBJECT" jdbcType="INTEGER" property="matchedObject" />
    <result column="ORDER_NO" jdbcType="VARCHAR" property="orderNo" />
    <result column="RELATED_BILL" jdbcType="VARCHAR" property="relatedBill" />
    <result column="IS_FEE" jdbcType="BOOLEAN" property="isFee" />
    <result column="SYNC_DATE" jdbcType="DATE" property="syncDate" />
    <result column="RECEIPT_NAME" jdbcType="VARCHAR" property="receiptName" />
    <result column="RECEIPT_URL" jdbcType="VARCHAR" property="receiptUrl" />
  </resultMap>
  <sql id="Base_Column_List">
    BANK_BILL_ID, COMPANY_ID, BANK_TAG, TRAN_FLOW, TRANDATE, TRANTIME, REAL_TRANDATE, 
    REAL_TRANDATETIME, CRE_TYP, CRE_NO, MESSAGE, AMT, AMT1, FLAG1, ACCNO2, ACC_BANKNO, 
    ACC_NAME1, FLAG2, BFLOW, DET_NO, DET, RLTV_ACCNO, CADBANK_NM, `STATUS`, COMMENTS, 
    MATCHED_AMOUNT, OVRLSTTN_TRCK_NO, IS_CONSISTENCY, MATCHED_OBJECT, ORDER_NO, RELATED_BILL, 
    IS_FEE, SYNC_DATE,RECEIPT_NAME,RECEIPT_URL
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_BANK_BILL
    where BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_BANK_BILL
    where BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="BANK_BILL_ID" keyProperty="bankBillId" parameterType="com.vedeng.billsync.task.model.entity.generate.BankBillDo" useGeneratedKeys="true">
    insert into T_BANK_BILL (COMPANY_ID, BANK_TAG, TRAN_FLOW, 
      TRANDATE, TRANTIME, REAL_TRANDATE, 
      REAL_TRANDATETIME, CRE_TYP, CRE_NO, 
      MESSAGE, AMT, AMT1, 
      FLAG1, ACCNO2, ACC_BANKNO, 
      ACC_NAME1, FLAG2, BFLOW, 
      DET_NO, DET, RLTV_ACCNO, 
      CADBANK_NM, `STATUS`, COMMENTS, 
      MATCHED_AMOUNT, OVRLSTTN_TRCK_NO, IS_CONSISTENCY, 
      MATCHED_OBJECT, ORDER_NO, RELATED_BILL, 
      IS_FEE, SYNC_DATE)
    values (#{companyId,jdbcType=INTEGER}, #{bankTag,jdbcType=BOOLEAN}, #{tranFlow,jdbcType=VARCHAR}, 
      #{trandate,jdbcType=DATE}, #{trantime,jdbcType=TIME}, #{realTrandate,jdbcType=DATE}, 
      #{realTrandatetime,jdbcType=TIMESTAMP}, #{creTyp,jdbcType=VARCHAR}, #{creNo,jdbcType=VARCHAR}, 
      #{message,jdbcType=VARCHAR}, #{amt,jdbcType=DECIMAL}, #{amt1,jdbcType=VARCHAR}, 
      #{flag1,jdbcType=BOOLEAN}, #{accno2,jdbcType=VARCHAR}, #{accBankno,jdbcType=VARCHAR}, 
      #{accName1,jdbcType=VARCHAR}, #{flag2,jdbcType=BOOLEAN}, #{bflow,jdbcType=VARCHAR}, 
      #{detNo,jdbcType=VARCHAR}, #{det,jdbcType=VARCHAR}, #{rltvAccno,jdbcType=VARCHAR}, 
      #{cadbankNm,jdbcType=VARCHAR}, #{status,jdbcType=BOOLEAN}, #{comments,jdbcType=VARCHAR}, 
      #{matchedAmount,jdbcType=DECIMAL}, #{ovrlsttnTrckNo,jdbcType=VARCHAR}, #{isConsistency,jdbcType=BOOLEAN}, 
      #{matchedObject,jdbcType=INTEGER}, #{orderNo,jdbcType=VARCHAR}, #{relatedBill,jdbcType=VARCHAR}, 
      #{isFee,jdbcType=BOOLEAN}, #{syncDate,jdbcType=DATE})
  </insert>
  <insert id="insertSelective" keyColumn="BANK_BILL_ID" keyProperty="bankBillId" parameterType="com.vedeng.billsync.task.model.entity.generate.BankBillDo" useGeneratedKeys="true">
    insert into T_BANK_BILL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="bankTag != null">
        BANK_TAG,
      </if>
      <if test="tranFlow != null">
        TRAN_FLOW,
      </if>
      <if test="trandate != null">
        TRANDATE,
      </if>
      <if test="trantime != null">
        TRANTIME,
      </if>
      <if test="realTrandate != null">
        REAL_TRANDATE,
      </if>
      <if test="realTrandatetime != null">
        REAL_TRANDATETIME,
      </if>
      <if test="creTyp != null">
        CRE_TYP,
      </if>
      <if test="creNo != null">
        CRE_NO,
      </if>
      <if test="message != null">
        MESSAGE,
      </if>
      <if test="amt != null">
        AMT,
      </if>
      <if test="amt1 != null">
        AMT1,
      </if>
      <if test="flag1 != null">
        FLAG1,
      </if>
      <if test="accno2 != null">
        ACCNO2,
      </if>
      <if test="accBankno != null">
        ACC_BANKNO,
      </if>
      <if test="accName1 != null">
        ACC_NAME1,
      </if>
      <if test="flag2 != null">
        FLAG2,
      </if>
      <if test="bflow != null">
        BFLOW,
      </if>
      <if test="detNo != null">
        DET_NO,
      </if>
      <if test="det != null">
        DET,
      </if>
      <if test="rltvAccno != null">
        RLTV_ACCNO,
      </if>
      <if test="cadbankNm != null">
        CADBANK_NM,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
      <if test="comments != null">
        COMMENTS,
      </if>
      <if test="matchedAmount != null">
        MATCHED_AMOUNT,
      </if>
      <if test="ovrlsttnTrckNo != null">
        OVRLSTTN_TRCK_NO,
      </if>
      <if test="isConsistency != null">
        IS_CONSISTENCY,
      </if>
      <if test="matchedObject != null">
        MATCHED_OBJECT,
      </if>
      <if test="orderNo != null">
        ORDER_NO,
      </if>
      <if test="relatedBill != null">
        RELATED_BILL,
      </if>
      <if test="isFee != null">
        IS_FEE,
      </if>
      <if test="syncDate != null">
        SYNC_DATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="bankTag != null">
        #{bankTag,jdbcType=BOOLEAN},
      </if>
      <if test="tranFlow != null">
        #{tranFlow,jdbcType=VARCHAR},
      </if>
      <if test="trandate != null">
        #{trandate,jdbcType=DATE},
      </if>
      <if test="trantime != null">
        #{trantime,jdbcType=TIME},
      </if>
      <if test="realTrandate != null">
        #{realTrandate,jdbcType=DATE},
      </if>
      <if test="realTrandatetime != null">
        #{realTrandatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="creTyp != null">
        #{creTyp,jdbcType=VARCHAR},
      </if>
      <if test="creNo != null">
        #{creNo,jdbcType=VARCHAR},
      </if>
      <if test="message != null">
        #{message,jdbcType=VARCHAR},
      </if>
      <if test="amt != null">
        #{amt,jdbcType=DECIMAL},
      </if>
      <if test="amt1 != null">
        #{amt1,jdbcType=VARCHAR},
      </if>
      <if test="flag1 != null">
        #{flag1,jdbcType=BOOLEAN},
      </if>
      <if test="accno2 != null">
        #{accno2,jdbcType=VARCHAR},
      </if>
      <if test="accBankno != null">
        #{accBankno,jdbcType=VARCHAR},
      </if>
      <if test="accName1 != null">
        #{accName1,jdbcType=VARCHAR},
      </if>
      <if test="flag2 != null">
        #{flag2,jdbcType=BOOLEAN},
      </if>
      <if test="bflow != null">
        #{bflow,jdbcType=VARCHAR},
      </if>
      <if test="detNo != null">
        #{detNo,jdbcType=VARCHAR},
      </if>
      <if test="det != null">
        #{det,jdbcType=VARCHAR},
      </if>
      <if test="rltvAccno != null">
        #{rltvAccno,jdbcType=VARCHAR},
      </if>
      <if test="cadbankNm != null">
        #{cadbankNm,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=BOOLEAN},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="matchedAmount != null">
        #{matchedAmount,jdbcType=DECIMAL},
      </if>
      <if test="ovrlsttnTrckNo != null">
        #{ovrlsttnTrckNo,jdbcType=VARCHAR},
      </if>
      <if test="isConsistency != null">
        #{isConsistency,jdbcType=BOOLEAN},
      </if>
      <if test="matchedObject != null">
        #{matchedObject,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="relatedBill != null">
        #{relatedBill,jdbcType=VARCHAR},
      </if>
      <if test="isFee != null">
        #{isFee,jdbcType=BOOLEAN},
      </if>
      <if test="syncDate != null">
        #{syncDate,jdbcType=DATE},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.billsync.task.model.entity.generate.BankBillDo">
    update T_BANK_BILL
    <set>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="bankTag != null">
        BANK_TAG = #{bankTag,jdbcType=BOOLEAN},
      </if>
      <if test="tranFlow != null">
        TRAN_FLOW = #{tranFlow,jdbcType=VARCHAR},
      </if>
      <if test="trandate != null">
        TRANDATE = #{trandate,jdbcType=DATE},
      </if>
      <if test="trantime != null">
        TRANTIME = #{trantime,jdbcType=TIME},
      </if>
      <if test="realTrandate != null">
        REAL_TRANDATE = #{realTrandate,jdbcType=DATE},
      </if>
      <if test="realTrandatetime != null">
        REAL_TRANDATETIME = #{realTrandatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="creTyp != null">
        CRE_TYP = #{creTyp,jdbcType=VARCHAR},
      </if>
      <if test="creNo != null">
        CRE_NO = #{creNo,jdbcType=VARCHAR},
      </if>
      <if test="message != null">
        MESSAGE = #{message,jdbcType=VARCHAR},
      </if>
      <if test="amt != null">
        AMT = #{amt,jdbcType=DECIMAL},
      </if>
      <if test="amt1 != null">
        AMT1 = #{amt1,jdbcType=VARCHAR},
      </if>
      <if test="flag1 != null">
        FLAG1 = #{flag1,jdbcType=BOOLEAN},
      </if>
      <if test="accno2 != null">
        ACCNO2 = #{accno2,jdbcType=VARCHAR},
      </if>
      <if test="accBankno != null">
        ACC_BANKNO = #{accBankno,jdbcType=VARCHAR},
      </if>
      <if test="accName1 != null">
        ACC_NAME1 = #{accName1,jdbcType=VARCHAR},
      </if>
      <if test="flag2 != null">
        FLAG2 = #{flag2,jdbcType=BOOLEAN},
      </if>
      <if test="bflow != null">
        BFLOW = #{bflow,jdbcType=VARCHAR},
      </if>
      <if test="detNo != null">
        DET_NO = #{detNo,jdbcType=VARCHAR},
      </if>
      <if test="det != null">
        DET = #{det,jdbcType=VARCHAR},
      </if>
      <if test="rltvAccno != null">
        RLTV_ACCNO = #{rltvAccno,jdbcType=VARCHAR},
      </if>
      <if test="cadbankNm != null">
        CADBANK_NM = #{cadbankNm,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `STATUS` = #{status,jdbcType=BOOLEAN},
      </if>
      <if test="comments != null">
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="matchedAmount != null">
        MATCHED_AMOUNT = #{matchedAmount,jdbcType=DECIMAL},
      </if>
      <if test="ovrlsttnTrckNo != null">
        OVRLSTTN_TRCK_NO = #{ovrlsttnTrckNo,jdbcType=VARCHAR},
      </if>
      <if test="isConsistency != null">
        IS_CONSISTENCY = #{isConsistency,jdbcType=BOOLEAN},
      </if>
      <if test="matchedObject != null">
        MATCHED_OBJECT = #{matchedObject,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="relatedBill != null">
        RELATED_BILL = #{relatedBill,jdbcType=VARCHAR},
      </if>
      <if test="isFee != null">
        IS_FEE = #{isFee,jdbcType=BOOLEAN},
      </if>
      <if test="syncDate != null">
        SYNC_DATE = #{syncDate,jdbcType=DATE},
      </if>
      <if test="receiptUrl != null">
        RECEIPT_URL = #{receiptUrl,jdbcType=VARCHAR},
      </if>
    </set>
    where BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.billsync.task.model.entity.generate.BankBillDo">
    update T_BANK_BILL
    set COMPANY_ID = #{companyId,jdbcType=INTEGER},
      BANK_TAG = #{bankTag,jdbcType=BOOLEAN},
      TRAN_FLOW = #{tranFlow,jdbcType=VARCHAR},
      TRANDATE = #{trandate,jdbcType=DATE},
      TRANTIME = #{trantime,jdbcType=TIME},
      REAL_TRANDATE = #{realTrandate,jdbcType=DATE},
      REAL_TRANDATETIME = #{realTrandatetime,jdbcType=TIMESTAMP},
      CRE_TYP = #{creTyp,jdbcType=VARCHAR},
      CRE_NO = #{creNo,jdbcType=VARCHAR},
      MESSAGE = #{message,jdbcType=VARCHAR},
      AMT = #{amt,jdbcType=DECIMAL},
      AMT1 = #{amt1,jdbcType=VARCHAR},
      FLAG1 = #{flag1,jdbcType=BOOLEAN},
      ACCNO2 = #{accno2,jdbcType=VARCHAR},
      ACC_BANKNO = #{accBankno,jdbcType=VARCHAR},
      ACC_NAME1 = #{accName1,jdbcType=VARCHAR},
      FLAG2 = #{flag2,jdbcType=BOOLEAN},
      BFLOW = #{bflow,jdbcType=VARCHAR},
      DET_NO = #{detNo,jdbcType=VARCHAR},
      DET = #{det,jdbcType=VARCHAR},
      RLTV_ACCNO = #{rltvAccno,jdbcType=VARCHAR},
      CADBANK_NM = #{cadbankNm,jdbcType=VARCHAR},
      `STATUS` = #{status,jdbcType=BOOLEAN},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      MATCHED_AMOUNT = #{matchedAmount,jdbcType=DECIMAL},
      OVRLSTTN_TRCK_NO = #{ovrlsttnTrckNo,jdbcType=VARCHAR},
      IS_CONSISTENCY = #{isConsistency,jdbcType=BOOLEAN},
      MATCHED_OBJECT = #{matchedObject,jdbcType=INTEGER},
      ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      RELATED_BILL = #{relatedBill,jdbcType=VARCHAR},
      IS_FEE = #{isFee,jdbcType=BOOLEAN},
      SYNC_DATE = #{syncDate,jdbcType=DATE}
    where BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeySelectiveNew" parameterType="com.vedeng.finance.model.BankBill">
    update T_BANK_BILL
    <set >
      <if test="companyId != null" >
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="tranFlow != null" >
        TRAN_FLOW = #{tranFlow,jdbcType=VARCHAR},
      </if>
      <if test="trandate != null" >
        TRANDATE = #{trandate,jdbcType=DATE},
      </if>
      <if test="trantime != null" >
        TRANTIME = #{trantime,jdbcType=TIME},
      </if>
      <if test="realTrandate != null" >
        REAL_TRANDATE = #{realTrandate,jdbcType=DATE},
      </if>
      <if test="realTrandatetime != null" >
        REAL_TRANDATETIME = #{realTrandatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="creTyp != null" >
        CRE_TYP = #{creTyp,jdbcType=VARCHAR},
      </if>
      <if test="creNo != null" >
        CRE_NO = #{creNo,jdbcType=VARCHAR},
      </if>
      <if test="message != null" >
        MESSAGE = #{message,jdbcType=VARCHAR},
      </if>
      <if test="amt != null" >
        AMT = #{amt,jdbcType=DECIMAL},
      </if>
      <if test="amt1 != null" >
        AMT1 = #{amt1,jdbcType=VARCHAR},
      </if>
      <if test="flag1 != null" >
        FLAG1 = #{flag1,jdbcType=BIT},
      </if>
      <if test="accno2 != null" >
        ACCNO2 = #{accno2,jdbcType=VARCHAR},
      </if>
      <if test="accName1 != null" >
        ACC_NAME1 = #{accName1,jdbcType=VARCHAR},
      </if>
      <if test="flag2 != null" >
        FLAG2 = #{flag2,jdbcType=BIT},
      </if>
      <if test="bflow != null" >
        BFLOW = #{bflow,jdbcType=VARCHAR},
      </if>
      <if test="detNo != null" >
        DET_NO = #{detNo,jdbcType=VARCHAR},
      </if>
      <if test="det != null" >
        DET = #{det,jdbcType=VARCHAR},
      </if>
      <if test="rltvAccno != null" >
        RLTV_ACCNO = #{rltvAccno,jdbcType=VARCHAR},
      </if>
      <if test="cadbankNm != null" >
        CADBANK_NM = #{cadbankNm,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        STATUS = #{status,jdbcType=BIT},
      </if>
      <if test="matchedAmount != null" >
        MATCHED_AMOUNT = #{matchedAmount,jdbcType=DECIMAL},
      </if>
      <if test="comments != null">
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="matchedObject != null" >
        MATCHED_OBJECT = #{matchedObject,jdbcType=INTEGER},
      </if>
    </set>
    where BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
  </update>
</mapper>