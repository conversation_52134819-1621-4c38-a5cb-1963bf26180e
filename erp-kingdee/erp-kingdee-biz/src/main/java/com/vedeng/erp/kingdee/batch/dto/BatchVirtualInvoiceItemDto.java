package com.vedeng.erp.kingdee.batch.dto;

import com.vedeng.common.core.base.BaseDto;
import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.*;

import java.math.BigDecimal;


/**
 * @description 虚拟发票明细
 * <AUTHOR>
 * @date 2023/11/27 9:33
 **/

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class BatchVirtualInvoiceItemDto extends BaseDto {
    /**
    * 主键
    */
    private Integer virtualInvoiceItemId;


    /**
    * 虚拟发票ID
    */
    private Integer virtualInvoiceId;

    /**
    * 源发票明细ID
    */
    private Integer sourceInvoiceItemId;

    /**
    * 源单业务单据明细ID
    */
    private Integer businessOrderItemId;

    /**
    * 开票单价
    */
    private BigDecimal price;

    /**
    * 开票数量
    */
    private BigDecimal num;

    /**
    * 开票总额
    */
    private BigDecimal totalAmount;

    private String sku;
}