<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>

<div id="app" style="display: none">
    <el-form ref="form" :model="form" label-width="80px" :rules="rules">
        <el-row>
            <el-col :span="24">
                <el-form-item label="审核备注" prop="auditComment">
                    <el-input v-model="form.auditComment"></el-input>
                </el-form-item>
            </el-col>
        </el-row>


        <el-form-item>
            <el-button @click="closeThis">取消</el-button>
            <el-button type="primary" @click="onSubmit('form')" :loading="onSubmitLoading">保存</el-button>
        </el-form-item>
    </el-form>
</div>

<script type="text/javascript">
    const ID = '${invoiceReversalId}';
    const isPass = '${isPass}';
    const formToken = '${formToken}';
    let isVerify = true;
    if(isPass == 'false'){
        isVerify = true;
    }else {
        isVerify = false;
    }

    new Vue({
        el: '#app',
        data() {
            return {
                onSubmitLoading: false,
                //表单校验
                rules: {
                    auditComment: [
                        {required: isVerify, message: '审核不通过，备注必填', trigger: 'blur'}
                    ]
                },
                form: {
                    invoiceReversalId: ID,
                    isPass: isPass,
                    auditComment: null,
                    formToken: formToken
                }
            };
        },
        mounted(){
          loadingApp();
        },
        created(){
        },
        methods: {
            closeThis(){
                parent.layer.close(index);
            },
            onSubmit(form) {
                this.onSubmitLoading = true;
                this.$refs[form].validate((valid) => {
                    if(valid){
                        //校验通过提交保存
                        verifyFormSubmit(this.form).then(res => {
                            debugger;
                            if(res.data.code == 0){
                                window.parent.location.reload();
                                parent.layer.close(index);
                            }else {
                                this.$message({
                                    message: res.data.message,
                                    duration: 1000,
                                    showClose:true,
                                    type: 'error',
                                });
                            }
                            this.onSubmitLoading = false
                        });
                    }
                    this.onSubmitLoading = false
                })
            }
        }
    });

    /**
     * 提交审核通过/不通过信息
     * @param data
     */
    function verifyFormSubmit(data){
        return axios({
            url: '/after/newBuyorder/verifyCoverSave.do',
            method: 'post',
            params: data
        })
    }
</script>