package com.newtask.filemove.theadpool;

import com.newtask.filemove.dto.FileMoveDto;
import com.vedeng.common.model.FileInfo;
import com.vedeng.filemove.dao.FileMoveDao;
import com.vedeng.goods.model.Brand;
import com.vedeng.goods.model.GoodsAttachment;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import javax.annotation.Resource;
import java.util.List;

/**
 * 商品附件批处理任务
 */
public class GoodsAttachmentTask extends FileMoveTask {

    @Resource
    private FileMoveDao fileMoveDao;

    public GoodsAttachmentTask(List<FileMoveDto> fileMoveList) {
        super(fileMoveList);
        // 运行时注入service
        WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
        fileMoveDao = (FileMoveDao) context.getBean("fileMoveDao");
    }

    @Override
    protected void updateErrorInfo(FileMoveDto fileMoveDto) {
        GoodsAttachment toUpdate = new GoodsAttachment();
        toUpdate.setGoodsAttachmentId(fileMoveDto.getId());
        toUpdate.setSynSuccess(2);
        fileMoveDao.updateGoodsAttachment(toUpdate);
    }

    @Override
    protected void updateOriginalFileInfo(FileMoveDto fileMoveDto, FileInfo fileInfo) {
        //更新uri、domain、resourceId
        GoodsAttachment toUpdate = new GoodsAttachment();
        toUpdate.setGoodsAttachmentId(fileMoveDto.getId());
        toUpdate.setDomain(fileInfo.getHttpUrl());
        toUpdate.setUri(fileInfo.getFilePath());
        toUpdate.setOriginalFilepath(fileMoveDto.getDomain() + fileMoveDto.getUri());
        toUpdate.setSynSuccess(1);
        toUpdate.setOssResourceId(fileInfo.getOssResourceId());

        if(fileInfo.getPrefix() != null){
            toUpdate.setCostTime(Long.valueOf(fileInfo.getPrefix()));
        }

        fileMoveDao.updateGoodsAttachment(toUpdate);
    }
}
