package com.vedeng.erp.finance.web.controller;

import com.vedeng.common.core.annotation.ExcludeAuthorization;
import com.vedeng.common.core.base.BaseController;
import com.vedeng.erp.finance.domain.dto.InvoiceRedConfirmationDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 发票红字确认单页面路由
 * @date 2023/10/13 9:20
 */
@RequestMapping("/invoice/redConfirmation")
@Controller
@Slf4j
public class InvoiceRedConfirmationController extends BaseController {


    @RequestMapping(value = "/list")
    public ModelAndView list(InvoiceRedConfirmationDto param) {
        ModelAndView mv = new ModelAndView("vue/view/finance/invoice_redconfirmation_list");
        mv.addObject("viewData", param);
        return mv;
    }

    /**
     * 红字确认单申请页
     *
     * @param invoiceRedConfirmationId 确认单id
     * @return ModelAndView
     */
    @RequestMapping(value = "/apply")
    @ExcludeAuthorization
    public ModelAndView apply(Integer invoiceRedConfirmationId) {
        ModelAndView mv = new ModelAndView("vue/view/finance/red_confirm_apply");
        mv.addObject("invoiceRedConfirmationId", invoiceRedConfirmationId);
        return mv;
    }

    /**
     * 红字确认单 详情页
     *
     * @param invoiceRedConfirmationId 确认单id
     * @return ModelAndView
     */
    @RequestMapping(value = "/detail")
    @ExcludeAuthorization
    public ModelAndView detail(Integer invoiceRedConfirmationId) {
        ModelAndView mv = new ModelAndView("vue/view/finance/red_confirm_detail");
        mv.addObject("invoiceRedConfirmationId", invoiceRedConfirmationId);
        return mv;
    }

    /**
     * 红字确认单关联售后
     *
     * @param invoiceRedConfirmationId 确认单id
     * @return ModelAndView
     */
    @RequestMapping(value = "/associate")
    @ExcludeAuthorization
    public ModelAndView associate(Integer invoiceRedConfirmationId) {
        ModelAndView mv = new ModelAndView("vue/view/finance/red_confirm_associate");
        mv.addObject("invoiceRedConfirmationId", invoiceRedConfirmationId);
        return mv;
    }

}
