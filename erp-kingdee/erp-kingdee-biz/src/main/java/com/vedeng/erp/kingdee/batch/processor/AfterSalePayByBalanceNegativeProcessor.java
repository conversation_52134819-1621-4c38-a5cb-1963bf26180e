package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.utils.numgenerator.BillNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.bean.NoGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import com.vedeng.erp.kingdee.batch.dto.BatchCapitalBillDto;
import com.vedeng.erp.kingdee.domain.entity.CapitalBillKingDeeCreate;
import com.vedeng.erp.kingdee.dto.KingDeeCustomerDto;
import com.vedeng.erp.kingdee.dto.KingDeeNeedReceiveDto;
import com.vedeng.erp.kingdee.dto.KingDeeNeedReceiveEntityDto;
import com.vedeng.erp.kingdee.repository.mappers.CapitalBillKingDeeCreateMapper;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeCustomerMapper;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 销售售后余额支付手续费(负向) processor
 * （应收单调整单）
 */
@Service
@Slf4j
public class AfterSalePayByBalanceNegativeProcessor implements ItemProcessor<BatchCapitalBillDto, KingDeeNeedReceiveDto> {

    @Autowired
    KingDeeCustomerMapper kingDeeCustomerMapper;
    @Autowired
    KingDeeBaseApi kingDeeBaseApi;
    @Autowired
    CapitalBillKingDeeCreateMapper capitalBillKingDeeCreateMapper;

    /**
     * 售后类型
     */
    private static final Integer ORDER_TYPE_AFTER_SALE = 3;

    @Override
    public KingDeeNeedReceiveDto process(BatchCapitalBillDto dto) throws Exception {

        log.info("(负向) 销售售后余额支付手续费，金蝶应收单调整单，参数:{}", JSON.toJSONString(dto));
        // 根据erp客户id查询金蝶客户id
        KingDeeCustomerDto kingDeeCustomerDto = kingDeeCustomerMapper.queryInfoByCustomerId(dto.getTraderCustomerId());
        if (ObjectUtil.isEmpty(kingDeeCustomerDto)){
            log.error("(负向) 销售售后余额支付手续费，金蝶应收单调整单，未查询到金蝶客户id，参数:{}",JSON.toJSONString(dto));
            return null;
        }
        BigDecimal amount = dto.getAmount().abs();
        Long fromTraderTime = dto.getTraderTime();
        // 流水的交易日期取余额支付的流水时间-1秒
        long traderTime = fromTraderTime - 1000L;
        CapitalBillKingDeeCreate create;
        // 判断是否已生成负向记录
        List<CapitalBillKingDeeCreate> capitalBillKingDeeCreates = capitalBillKingDeeCreateMapper.selectByOrderNoAndType(dto.getOrderNo(), ORDER_TYPE_AFTER_SALE);
        if (CollUtil.isEmpty(capitalBillKingDeeCreates)){
            // 生成新单号
            BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.CAPITAL_NO, NoGeneratorBean.builder().dateFormat(ErpConstant.YYYYMMDDHHMMSS).id(generateRandomNumber()).numberOfDigits(9).build());
            String capitalBillNo = new BillNumGenerator().distribution(billGeneratorBean);
            create = new CapitalBillKingDeeCreate();
            create.setFromCapitalBillId(dto.getCapitalBillId());
            create.setCapitalBillNo(capitalBillNo);
            create.setTraderTime(traderTime);
            create.setAmount(amount.negate());
            create.setOrderNo(dto.getOrderNo());
            create.setOrderType(ORDER_TYPE_AFTER_SALE);
            capitalBillKingDeeCreateMapper.insertSelective(create);
        }else {
            create = capitalBillKingDeeCreates.get(0);
        }

        // 金蝶应收单调整单 needReceiveDto
        KingDeeNeedReceiveDto needReceiveDto = new KingDeeNeedReceiveDto();
        needReceiveDto.setFid("0");
        needReceiveDto.setFBillNo(create.getCapitalBillNo());
        // 判断是否数据已存在
        if (CollUtil.isNotEmpty(capitalBillKingDeeCreates)) {
            boolean old = kingDeeBaseApi.isExist(needReceiveDto);
            if (old) {
                log.info("(负向) 销售售后余额支付手续费,数据已存在:{}", JSON.toJSONString(dto));
                return null;
            }
        }
        needReceiveDto.setFVpfnDate(DateUtil.formatDate(DateUtil.date(create.getTraderTime())));
        needReceiveDto.setFVpfnJg(KingDeeConstant.ORG_ID.toString());
        needReceiveDto.setFVpfnKh(Convert.toStr(kingDeeCustomerDto.getFNumber()));
        List<KingDeeNeedReceiveEntityDto> FEntity = new ArrayList<>();
        KingDeeNeedReceiveEntityDto needReceiveEntityDto = new KingDeeNeedReceiveEntityDto();
        needReceiveEntityDto.setFVpfnYsddh(dto.getOrderNo());
        needReceiveEntityDto.setFVpfnGsywdh(dto.getAfterSaleNo());
        // 销售售后余额支付手续费-应收单调整单 业务类型
        needReceiveEntityDto.setFVpfnYwlx("订单收款");
        needReceiveEntityDto.setFVpfnTzje(create.getAmount());
        FEntity.add(needReceiveEntityDto);
        needReceiveDto.setFEntityList(FEntity);
        return needReceiveDto;
    }

    /**
     * 随机产生一个8位数字
     * @return
     */
    public int generateRandomNumber() {
        Random random = new Random();
        int min = 10000000;
        int max = 99999999;
        return random.nextInt(max - min + 1) + min;
    }
}
