package com.vedeng.order.model.dto;

import com.vedeng.order.model.RemarkComponentTree;
import com.vedeng.order.model.query.LabelQuery;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description:
 * @Author:       davis
 * @Date:         2021/4/16 上午10:38
 * @Version:      1.0
 */
@Data
public class RemarkComponentDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 组件集合
     */
    private List<LabelQueryDto> data;

    /**
     * 人工备注
     */
    private String remark;

    /**
     * 组件参数
     */
    private LabelQuery labelQuery;

}
