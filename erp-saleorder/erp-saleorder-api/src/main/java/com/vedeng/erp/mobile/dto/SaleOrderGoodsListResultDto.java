package com.vedeng.erp.mobile.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 销售订单列表查询返回商品明细类
 */
@Data
public class SaleOrderGoodsListResultDto {

    /**
     * 销售订单商品id
     */
    private Integer saleordergoodsId;

    /**
     * 销售订单id
     */
    private Integer saleorderId;

    /**
     * 商品id
     */
    private Integer goodsId;

    /**
     * SKU
     */
    private String sku;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品单价
     */
    private BigDecimal price;

    /**
     * 商品数量
     */
    private Integer num;

}
