<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.buyorderexpense.mapper.BuyorderExpenseDetailMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseDetailEntity">
    <!--@mbg.generated-->
    <!--@Table T_BUYORDER_EXPENSE_DETAIL-->
    <id column="BUYORDER_EXPENSE_DETAIL_ID" jdbcType="INTEGER" property="buyorderExpenseDetailId" />
    <result column="BUYORDER_EXPENSE_ID" jdbcType="INTEGER" property="buyorderExpenseId" />
    <result column="PAYMENT_TYPE" jdbcType="INTEGER" property="paymentType" />
    <result column="PAYMENT_COMMENTS" jdbcType="VARCHAR" property="paymentComments" />
    <result column="INVOICE_TYPE" jdbcType="INTEGER" property="invoiceType" />
    <result column="INVOICE_COMMENTS" jdbcType="VARCHAR" property="invoiceComments" />
    <result column="TOTAL_AMOUNT" jdbcType="DECIMAL" property="totalAmount" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName" />
    <result column="TRADER_CONTACT_ID" jdbcType="INTEGER" property="traderContactId" />
    <result column="TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="traderContactName" />
    <result column="TRADER_CONTACT_MOBILE" jdbcType="VARCHAR" property="traderContactMobile" />
    <result column="TRADER_CONTACT_TELEPHONE" jdbcType="VARCHAR" property="traderContactTelephone" />
    <result column="TRADER_ADDRESS_ID" jdbcType="INTEGER" property="traderAddressId" />
    <result column="TRADER_AREA" jdbcType="VARCHAR" property="traderArea" />
    <result column="TRADER_ADDRESS" jdbcType="VARCHAR" property="traderAddress" />
    <result column="TRADER_COMMENTS" jdbcType="VARCHAR" property="traderComments" />
    <result column="PREPAID_AMOUNT" jdbcType="DECIMAL" property="prepaidAmount" />
    <result column="ACCOUNT_PERIOD_AMOUNT" jdbcType="DECIMAL" property="accountPeriodAmount" />
    <result column="PERIOD_DAY" jdbcType="INTEGER" property="periodDay" />
    <result column="RETAINAGE_AMOUNT" jdbcType="DECIMAL" property="retainageAmount" />
    <result column="RETAINAGE_AMOUNT_MONTH" jdbcType="INTEGER" property="retainageAmountMonth" />
    <result column="EXPENSE_SOURCE" jdbcType="VARCHAR" property="expenseSource" />
    <result column="ORG_ID" jdbcType="INTEGER" property="orgId" />
    <result column="CONTRACT_URL" jdbcType="VARCHAR" property="contractUrl" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ORDER_DESC" jdbcType="VARCHAR"
            javaType="com.vedeng.erp.buyorder.dto.OrderRemarkDto"
            property="orderDesc"
            typeHandler="com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    BUYORDER_EXPENSE_DETAIL_ID, BUYORDER_EXPENSE_ID, PAYMENT_TYPE, PAYMENT_COMMENTS, 
    INVOICE_TYPE, INVOICE_COMMENTS, TOTAL_AMOUNT, TRADER_ID, TRADER_NAME, TRADER_CONTACT_ID, 
    TRADER_CONTACT_NAME, TRADER_CONTACT_MOBILE, TRADER_CONTACT_TELEPHONE, TRADER_ADDRESS_ID, 
    TRADER_AREA, TRADER_ADDRESS, TRADER_COMMENTS, PREPAID_AMOUNT, ACCOUNT_PERIOD_AMOUNT, 
    PERIOD_DAY, RETAINAGE_AMOUNT, RETAINAGE_AMOUNT_MONTH, EXPENSE_SOURCE, ORG_ID, CONTRACT_URL, 
    ADD_TIME, CREATOR, CREATOR_NAME, MOD_TIME, UPDATER, UPDATER_NAME, IS_DELETE, ORDER_DESC
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_BUYORDER_EXPENSE_DETAIL
    where BUYORDER_EXPENSE_DETAIL_ID = #{buyorderExpenseDetailId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_BUYORDER_EXPENSE_DETAIL
    where BUYORDER_EXPENSE_DETAIL_ID = #{buyorderExpenseDetailId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="BUYORDER_EXPENSE_DETAIL_ID" keyProperty="buyorderExpenseDetailId" parameterType="com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseDetailEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUYORDER_EXPENSE_DETAIL (BUYORDER_EXPENSE_ID, PAYMENT_TYPE, PAYMENT_COMMENTS, 
      INVOICE_TYPE, INVOICE_COMMENTS, TOTAL_AMOUNT, 
      TRADER_ID, TRADER_NAME, TRADER_CONTACT_ID, 
      TRADER_CONTACT_NAME, TRADER_CONTACT_MOBILE, 
      TRADER_CONTACT_TELEPHONE, TRADER_ADDRESS_ID, 
      TRADER_AREA, TRADER_ADDRESS, TRADER_COMMENTS, 
      PREPAID_AMOUNT, ACCOUNT_PERIOD_AMOUNT, PERIOD_DAY, 
      RETAINAGE_AMOUNT, RETAINAGE_AMOUNT_MONTH, 
      EXPENSE_SOURCE, ORG_ID, CONTRACT_URL, 
      ADD_TIME, CREATOR, CREATOR_NAME, 
      MOD_TIME, UPDATER, UPDATER_NAME, 
      IS_DELETE, ORDER_DESC)
    values (#{buyorderExpenseId,jdbcType=INTEGER}, #{paymentType,jdbcType=INTEGER}, #{paymentComments,jdbcType=VARCHAR}, 
      #{invoiceType,jdbcType=INTEGER}, #{invoiceComments,jdbcType=VARCHAR}, #{totalAmount,jdbcType=DECIMAL}, 
      #{traderId,jdbcType=INTEGER}, #{traderName,jdbcType=VARCHAR}, #{traderContactId,jdbcType=INTEGER}, 
      #{traderContactName,jdbcType=VARCHAR}, #{traderContactMobile,jdbcType=VARCHAR}, 
      #{traderContactTelephone,jdbcType=VARCHAR}, #{traderAddressId,jdbcType=INTEGER}, 
      #{traderArea,jdbcType=VARCHAR}, #{traderAddress,jdbcType=VARCHAR}, #{traderComments,jdbcType=VARCHAR}, 
      #{prepaidAmount,jdbcType=DECIMAL}, #{accountPeriodAmount,jdbcType=DECIMAL}, #{periodDay,jdbcType=INTEGER}, 
      #{retainageAmount,jdbcType=DECIMAL}, #{retainageAmountMonth,jdbcType=INTEGER}, 
      #{expenseSource,jdbcType=VARCHAR}, #{orgId,jdbcType=INTEGER}, #{contractUrl,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{modTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, 
      #{isDelete,jdbcType=INTEGER},
    #{orderDesc,jdbcType=OTHER,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler})
  </insert>
  <insert id="insertSelective" keyColumn="BUYORDER_EXPENSE_DETAIL_ID" keyProperty="buyorderExpenseDetailId" parameterType="com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseDetailEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUYORDER_EXPENSE_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="buyorderExpenseId != null">
        BUYORDER_EXPENSE_ID,
      </if>
      <if test="paymentType != null">
        PAYMENT_TYPE,
      </if>
      <if test="paymentComments != null">
        PAYMENT_COMMENTS,
      </if>
      <if test="invoiceType != null">
        INVOICE_TYPE,
      </if>
      <if test="invoiceComments != null">
        INVOICE_COMMENTS,
      </if>
      <if test="totalAmount != null">
        TOTAL_AMOUNT,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderName != null">
        TRADER_NAME,
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID,
      </if>
      <if test="traderContactName != null">
        TRADER_CONTACT_NAME,
      </if>
      <if test="traderContactMobile != null">
        TRADER_CONTACT_MOBILE,
      </if>
      <if test="traderContactTelephone != null">
        TRADER_CONTACT_TELEPHONE,
      </if>
      <if test="traderAddressId != null">
        TRADER_ADDRESS_ID,
      </if>
      <if test="traderArea != null">
        TRADER_AREA,
      </if>
      <if test="traderAddress != null">
        TRADER_ADDRESS,
      </if>
      <if test="traderComments != null">
        TRADER_COMMENTS,
      </if>
      <if test="prepaidAmount != null">
        PREPAID_AMOUNT,
      </if>
      <if test="accountPeriodAmount != null">
        ACCOUNT_PERIOD_AMOUNT,
      </if>
      <if test="periodDay != null">
        PERIOD_DAY,
      </if>
      <if test="retainageAmount != null">
        RETAINAGE_AMOUNT,
      </if>
      <if test="retainageAmountMonth != null">
        RETAINAGE_AMOUNT_MONTH,
      </if>
      <if test="expenseSource != null">
        EXPENSE_SOURCE,
      </if>
      <if test="orgId != null">
        ORG_ID,
      </if>
      <if test="contractUrl != null">
        CONTRACT_URL,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="orderDesc != null">
        ORDER_DESC,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="buyorderExpenseId != null">
        #{buyorderExpenseId,jdbcType=INTEGER},
      </if>
      <if test="paymentType != null">
        #{paymentType,jdbcType=INTEGER},
      </if>
      <if test="paymentComments != null">
        #{paymentComments,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null">
        #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="invoiceComments != null">
        #{invoiceComments,jdbcType=VARCHAR},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderName != null">
        #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactId != null">
        #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null">
        #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactMobile != null">
        #{traderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="traderContactTelephone != null">
        #{traderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="traderAddressId != null">
        #{traderAddressId,jdbcType=INTEGER},
      </if>
      <if test="traderArea != null">
        #{traderArea,jdbcType=VARCHAR},
      </if>
      <if test="traderAddress != null">
        #{traderAddress,jdbcType=VARCHAR},
      </if>
      <if test="traderComments != null">
        #{traderComments,jdbcType=VARCHAR},
      </if>
      <if test="prepaidAmount != null">
        #{prepaidAmount,jdbcType=DECIMAL},
      </if>
      <if test="accountPeriodAmount != null">
        #{accountPeriodAmount,jdbcType=DECIMAL},
      </if>
      <if test="periodDay != null">
        #{periodDay,jdbcType=INTEGER},
      </if>
      <if test="retainageAmount != null">
        #{retainageAmount,jdbcType=DECIMAL},
      </if>
      <if test="retainageAmountMonth != null">
        #{retainageAmountMonth,jdbcType=INTEGER},
      </if>
      <if test="expenseSource != null">
        #{expenseSource,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="contractUrl != null">
        #{contractUrl,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="orderDesc != null">
        #{orderDesc,jdbcType=OTHER,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseDetailEntity">
    <!--@mbg.generated-->
    update T_BUYORDER_EXPENSE_DETAIL
    <set>
      <if test="buyorderExpenseId != null">
        BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER},
      </if>
      <if test="paymentType != null">
        PAYMENT_TYPE = #{paymentType,jdbcType=INTEGER},
      </if>
      <if test="paymentComments != null">
        PAYMENT_COMMENTS = #{paymentComments,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null">
        INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="invoiceComments != null">
        INVOICE_COMMENTS = #{invoiceComments,jdbcType=VARCHAR},
      </if>
      <if test="totalAmount != null">
        TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderName != null">
        TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null">
        TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactMobile != null">
        TRADER_CONTACT_MOBILE = #{traderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="traderContactTelephone != null">
        TRADER_CONTACT_TELEPHONE = #{traderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="traderAddressId != null">
        TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER},
      </if>
      <if test="traderArea != null">
        TRADER_AREA = #{traderArea,jdbcType=VARCHAR},
      </if>
      <if test="traderAddress != null">
        TRADER_ADDRESS = #{traderAddress,jdbcType=VARCHAR},
      </if>
      <if test="traderComments != null">
        TRADER_COMMENTS = #{traderComments,jdbcType=VARCHAR},
      </if>
      <if test="prepaidAmount != null">
        PREPAID_AMOUNT = #{prepaidAmount,jdbcType=DECIMAL},
      </if>
      <if test="accountPeriodAmount != null">
        ACCOUNT_PERIOD_AMOUNT = #{accountPeriodAmount,jdbcType=DECIMAL},
      </if>
      <if test="periodDay != null">
        PERIOD_DAY = #{periodDay,jdbcType=INTEGER},
      </if>
      <if test="retainageAmount != null">
        RETAINAGE_AMOUNT = #{retainageAmount,jdbcType=DECIMAL},
      </if>
      <if test="retainageAmountMonth != null">
        RETAINAGE_AMOUNT_MONTH = #{retainageAmountMonth,jdbcType=INTEGER},
      </if>
      <if test="expenseSource != null">
        EXPENSE_SOURCE = #{expenseSource,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        ORG_ID = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="contractUrl != null">
        CONTRACT_URL = #{contractUrl,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="orderDesc != null">
        ORDER_DESC = #{orderDesc,jdbcType=OTHER,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler},
      </if>
    </set>
    where BUYORDER_EXPENSE_DETAIL_ID = #{buyorderExpenseDetailId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseDetailEntity">
    <!--@mbg.generated-->
    update T_BUYORDER_EXPENSE_DETAIL
    set BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER},
      PAYMENT_TYPE = #{paymentType,jdbcType=INTEGER},
      PAYMENT_COMMENTS = #{paymentComments,jdbcType=VARCHAR},
      INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
      INVOICE_COMMENTS = #{invoiceComments,jdbcType=VARCHAR},
      TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      TRADER_CONTACT_MOBILE = #{traderContactMobile,jdbcType=VARCHAR},
      TRADER_CONTACT_TELEPHONE = #{traderContactTelephone,jdbcType=VARCHAR},
      TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER},
      TRADER_AREA = #{traderArea,jdbcType=VARCHAR},
      TRADER_ADDRESS = #{traderAddress,jdbcType=VARCHAR},
      TRADER_COMMENTS = #{traderComments,jdbcType=VARCHAR},
      PREPAID_AMOUNT = #{prepaidAmount,jdbcType=DECIMAL},
      ACCOUNT_PERIOD_AMOUNT = #{accountPeriodAmount,jdbcType=DECIMAL},
      PERIOD_DAY = #{periodDay,jdbcType=INTEGER},
      RETAINAGE_AMOUNT = #{retainageAmount,jdbcType=DECIMAL},
      RETAINAGE_AMOUNT_MONTH = #{retainageAmountMonth,jdbcType=INTEGER},
      EXPENSE_SOURCE = #{expenseSource,jdbcType=VARCHAR},
      ORG_ID = #{orgId,jdbcType=INTEGER},
      CONTRACT_URL = #{contractUrl,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ORDER_DESC = #{orderDesc,jdbcType=OTHER,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler}
    where BUYORDER_EXPENSE_DETAIL_ID = #{buyorderExpenseDetailId,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="BUYORDER_EXPENSE_DETAIL_ID" keyProperty="buyorderExpenseDetailId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUYORDER_EXPENSE_DETAIL
    (BUYORDER_EXPENSE_ID, PAYMENT_TYPE, PAYMENT_COMMENTS, INVOICE_TYPE, INVOICE_COMMENTS, 
      TOTAL_AMOUNT, TRADER_ID, TRADER_NAME, TRADER_CONTACT_ID, TRADER_CONTACT_NAME, TRADER_CONTACT_MOBILE, 
      TRADER_CONTACT_TELEPHONE, TRADER_ADDRESS_ID, TRADER_AREA, TRADER_ADDRESS, TRADER_COMMENTS, 
      PREPAID_AMOUNT, ACCOUNT_PERIOD_AMOUNT, PERIOD_DAY, RETAINAGE_AMOUNT, RETAINAGE_AMOUNT_MONTH, 
      EXPENSE_SOURCE, ORG_ID, CONTRACT_URL, ADD_TIME, CREATOR, CREATOR_NAME, MOD_TIME, 
      UPDATER, UPDATER_NAME, IS_DELETE, ORDER_DESC)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.buyorderExpenseId,jdbcType=INTEGER}, #{item.paymentType,jdbcType=INTEGER}, 
        #{item.paymentComments,jdbcType=VARCHAR}, #{item.invoiceType,jdbcType=INTEGER}, 
        #{item.invoiceComments,jdbcType=VARCHAR}, #{item.totalAmount,jdbcType=DECIMAL}, 
        #{item.traderId,jdbcType=INTEGER}, #{item.traderName,jdbcType=VARCHAR}, #{item.traderContactId,jdbcType=INTEGER}, 
        #{item.traderContactName,jdbcType=VARCHAR}, #{item.traderContactMobile,jdbcType=VARCHAR}, 
        #{item.traderContactTelephone,jdbcType=VARCHAR}, #{item.traderAddressId,jdbcType=INTEGER}, 
        #{item.traderArea,jdbcType=VARCHAR}, #{item.traderAddress,jdbcType=VARCHAR}, #{item.traderComments,jdbcType=VARCHAR}, 
        #{item.prepaidAmount,jdbcType=DECIMAL}, #{item.accountPeriodAmount,jdbcType=DECIMAL}, 
        #{item.periodDay,jdbcType=INTEGER}, #{item.retainageAmount,jdbcType=DECIMAL}, #{item.retainageAmountMonth,jdbcType=INTEGER}, 
        #{item.expenseSource,jdbcType=VARCHAR}, #{item.orgId,jdbcType=INTEGER}, #{item.contractUrl,jdbcType=VARCHAR}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, 
        #{item.modTime,jdbcType=TIMESTAMP}, #{item.updater,jdbcType=INTEGER}, #{item.updaterName,jdbcType=VARCHAR}, 
        #{item.isDelete,jdbcType=INTEGER}, #{item.orderDesc,jdbcType=OTHER,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler})
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2022-10-17-->
  <select id="selectByBuyorderExpenseId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_BUYORDER_EXPENSE_DETAIL
    where BUYORDER_EXPENSE_ID=#{buyorderExpenseId,jdbcType=INTEGER}
  </select>
    <select id="selectByBuyorderId" resultMap="BaseResultMap">
      SELECT
          tbed.*
      FROM
          T_BUYORDER_EXPENSE_DETAIL tbed
      LEFT JOIN T_BUYORDER_EXPENSE tbe ON
          tbed.BUYORDER_EXPENSE_ID = tbe.BUYORDER_EXPENSE_ID
      WHERE
          tbe.BUYORDER_ID = #{buyorderId,jdbcType=INTEGER}
          AND tbe.IS_DELETE = 0
          AND tbed.IS_DELETE = 0
          AND tbe.ORDER_TYPE = 0
      LIMIT 1
    </select>

    <update id="deleteExpenseDetailByExpenseId" parameterType="com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseDetailEntity">
    update T_BUYORDER_EXPENSE_DETAIL
    set IS_DELETE = #{isDelete,jdbcType=INTEGER}
    where BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER}
    and IS_DELETE = 0
  </update>

  <select id="selectByBuyorderExpenseIdList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_BUYORDER_EXPENSE_DETAIL
    where IS_DELETE = 0 AND BUYORDER_EXPENSE_ID in
    <foreach item="buyorderExpenseId" index="index" collection="buyorderExpenseIdList" open="(" separator="," close=")">
      #{buyorderExpenseId,jdbcType=INTEGER}
    </foreach>
  </select>

  <update id="updateOrderDescByBuyorderExpenseId" parameterType="com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseDetailEntity">
    <!--@mbg.generated-->
    update T_BUYORDER_EXPENSE_DETAIL
    set
    ORDER_DESC = #{orderDesc,jdbcType=OTHER,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler}
    where BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER}
  </update>

  <select id="selectByBuyorderExpenseItemId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_BUYORDER_EXPENSE_DETAIL
    where BUYORDER_EXPENSE_ITEM_ID = #{buyorderExpenseItemId,jdbcType=INTEGER}
  </select>
</mapper>