.ft-tour {
    display: none;
}

.ft-tour .ft-cover {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9998;
    background: #000;
    opacity: .7;
    filter: alpha(opacity=70);
}

.ft-tour .ft-layer {
    position: absolute;
    z-index: 9999;
    background: #fff;
    transition: all .3s;
}

.ft-tour .ft-highlight {
    width: 100%;
    height: 100%;
    z-index: 10001;
    background: transparent;
}

.ft-tour .ft-box {
    position: absolute;
    top: -10px;
    left: -10px;
    width: 100%;
    background: transparent;
    height: 100%;
    padding: 9px;
    border: 1px dashed #fff;
    z-index: 10001;
}

.ft-tour .ft-box .ft-close {
    position: absolute;
    display: block;
    width: 16px;
    height: 16px;
    top: -9px;
    right: -9px;
    color: #666;
    font-size: 14px;
    line-height: 16px;
    text-align: center;
    margin: 0;
    background: #fff;
    border-radius: 50%;
    cursor: pointer;
    z-index: 10003;
}

.ft-tour .ft-box .ft-inner {
    position: absolute;
    color: #fff;
    width: 250px;
}

.ft-tour .ft-box .ft-inner .ft-words {
    width: 210px;
    padding: 0 20px;
}

.ft-tour .ft-box .ft-inner .ft-arr {
    height: 80px;
    width: 80px;
    background: url("../../img/tour-arrow.png") no-repeat;
}

.ft-tour .ft-box .ft-inner .ft-txt {
    margin: 0;
    font-size: 14px;
    line-height: 24px;
}

.ft-tour .ft-box .ft-inner .ft-btnwrap {
    margin-top: 10px;
}

.ft-tour .ft-box .ft-inner .tour-btn {
    float: left;
    height: 30px;
    margin: 0 10px 0 0;
    padding: 0 15px;
    font-size: 12px;
    line-height: 30px;
    text-align: center;
    background: #f5f7fa;
    border: 0;
    color: #333;
    border-radius: 3px;
}

.ft-tour .ft-box .ft-inner .ft-close {
    position: relative;
    top: auto;
    right: auto;
    width: auto;
}

.ft-tour .ft-box .ft-inner .main-btn {
    background: #e64545;
    color: #fff;
}

.ft-tour .ft-box .ft-inner .tour-dot {
    float: left;
    height: 30px;
}

.ft-tour .ft-box .ft-inner .tour-dot i {
    float: left;
    width: 4px;
    height: 4px;
    border: 1px solid #ced3d9;
    margin: 12px 5px 0 0;
    border-radius: 50%;
    cursor: pointer;
}

.ft-tour .ft-box .ft-inner .tour-dot i.on {
    background: #ced3d9;
}

.ft-tour .ft-box .ft-dir-cb {
    top: 100%;
    left: 50%;
    margin-left: -125px;
}

.ft-tour .ft-box .ft-dir-cb .ft-arr {
    margin: 0 auto;
}

.ft-tour .ft-box .ft-dir-ct {
    bottom: 100%;
    left: 50%;
    margin-left: -125px;
}

.ft-tour .ft-box .ft-dir-ct .ft-arr {
    margin: 0 auto;
    background-position: -80px 0;
}

.ft-tour .ft-box .ft-dir-lt {
    bottom: 100%;
    left: -250px;
}

.ft-tour .ft-box .ft-dir-lt .ft-arr {
    position: relative;
    left: 100%;
    margin-left: -80px;
    background-position: -160px 0;
}

.ft-tour .ft-box .ft-dir-lc {
    right: 100%;
    width: 290px;
    top: 50%;
    margin-top: -40px;
}

.ft-tour .ft-box .ft-dir-lc .ft-arr {
    float: right;
    background-position: -240px 0;
}

.ft-tour .ft-box .ft-dir-lc .ft-words {
    padding: 0;
    float: left;
}

.ft-tour .ft-box .ft-dir-lb {
    top: 100%;
    left: -250px;
}

.ft-tour .ft-box .ft-dir-lb .ft-arr {
    position: relative;
    left: 100%;
    margin-left: -80px;
    background-position: 0 -80px;
}

.ft-tour .ft-box .ft-dir-rt {
    bottom: 100%;
    right: -250px;
}

.ft-tour .ft-box .ft-dir-rt .ft-arr {
    background-position: -80px -80px;
}

.ft-tour .ft-box .ft-dir-rc {
    left: 100%;
    width: 290px;
    top: 50%;
    margin-top: -40px;
}

.ft-tour .ft-box .ft-dir-rc .ft-arr {
    float: left;
    background-position: -160px -80px;
}

.ft-tour .ft-box .ft-dir-rc .ft-words {
    float: right;
    padding: 0;
}

.ft-tour .ft-box .ft-dir-rb {
    top: 100%;
    right: -250px;
}

.ft-tour .ft-box .ft-dir-rb .ft-arr {
    background-position: -240px -80px;
}