package com.vedeng.infrastructure.bank.api.domain;

import lombok.*;
import sun.tools.jar.SignatureFile;

import javax.xml.bind.annotation.*;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 融资申请提交请求体
 * @date 2024/10/12 11:26
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class B2eDraftApplyReq {

    /**
     * 客户业务请求流水号，同一业务请求请勿重复
     */
    private String insId;

    /**
     * 客户技术请求流水号，同一客户请勿重复
     */
    private String trnId;

    /**
     * 外部融资申请编号
     */
    private String outApplicationNo;

    /**
     * 借据号
     */
    private String creditCode;


    /**
     * 融资预申请或融资申请提交二选一上送
     */
    @XmlElementWrapper(name = "fileListInfo")
    @XmlElement(name = "Map")
    private List<FileInfo> fileListInfo;

    /**
     * 签名文件信息列表
     */
    @XmlElementWrapper(name = "signatureFileList")
    @XmlElement(name = "Map")
    private List<SignatureFile> signatureFileList = new ArrayList<>();



    @XmlAccessorType(XmlAccessType.FIELD)
    public static class FileInfo {

        /**
         * 文件类型
         * 04 贸易合同附件
         * 135 增值税纳税申报表附件(低风险自动授信)
         * 136 审计报告附件(低风险自动授信)
         * 139 担保资金证明材料附件
         * 140 其他材料附件
         */
        private String fileType;

        /**
         * 附件文件名
         */
        private String fileName;

        /**
         * 影像 id
         */
        private String imageId;

        /**
         * 系统编号(上送 734_354)
         */
        private String sysNo;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class SignatureFile {

        /**
         * 盒子签名结果数据
         */
        private String signature;

        /**
         * 影像文件编号
         */
        private String imageId;

        /**
         * 文件名称
         */
        private String fileName;

        /**
         * 文件类型
         * 119 授信申请书
         * 18 承兑协议
         * 48 信用信息查询使用授权书
         */
        private String fileType;

        /**
         * 系统编号（上送 734_734）
         */
        private String sysNo;

        /**
         * 合同号
         */
        private String cntrNum;
        /**
         * 文件 id
         */
        private String id;

    }


}
