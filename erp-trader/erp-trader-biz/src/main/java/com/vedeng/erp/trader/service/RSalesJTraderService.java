package com.vedeng.erp.trader.service;

import com.vedeng.erp.trader.dto.RSalesJTraderDto;
import com.vedeng.erp.trader.dto.TraderBatchShareDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 销售人员和客户关系
 * @Date 2024/1/4 15:39
 */
public interface RSalesJTraderService {

    /**
     * 保存客户分享
     *
     * @param salesJoinTraderDto RSalesJTraderDto
     */
    void saveTraderShare(RSalesJTraderDto salesJoinTraderDto);

    /**
     * 保存取消分享
     *
     * @param id id
     */
    int saveCancelShare(Integer id);

    int saveBatchShare(TraderBatchShareDto dto);

    List<Map<String,Object>> queryShardUserList(Integer userId);
    
    List<RSalesJTraderDto> getBySaleUserAndRegion(Integer userId, Integer regionId);
}
