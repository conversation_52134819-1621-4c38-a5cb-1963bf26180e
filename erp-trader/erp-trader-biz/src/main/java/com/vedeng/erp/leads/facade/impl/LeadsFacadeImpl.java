package com.vedeng.erp.leads.facade.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.erp.business.domain.dto.BusinessLeadsDto;
import com.vedeng.erp.business.service.BusinessLeadsService;
import com.vedeng.erp.leads.dto.LeadsToEditAndAddDto;
import com.vedeng.erp.leads.facade.LeadsFacade;
import com.vedeng.erp.system.dto.SysOptionDefinitionDto;
import com.vedeng.erp.system.service.SysOptionDefinitionApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.ModelAndView;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 线索接口实现
 */
@Service
public class LeadsFacadeImpl implements LeadsFacade {

    @Autowired
    private SysOptionDefinitionApiService sysOptionDefinitionApiService;

    @Autowired
    private BusinessLeadsService businessLeadsService;

    @Value("${bnc_service_type_ids}")
    protected String bncServiceTypeIds;

    @Override
    public ModelAndView toEditAndAdd(LeadsToEditAndAddDto dto,CurrentUser currentUser) {
        ModelAndView mv = new ModelAndView("vue/view/businessleads/edit");
        mv.addObject("creatername", currentUser.getUsername());
        mv.addObject("creater", currentUser.getId());
        BusinessLeadsDto businessLeadsDto = new BusinessLeadsDto();
        if (Objects.nonNull(dto.getLeadsId())) {
            businessLeadsDto = businessLeadsService.getOne(dto.getLeadsId());
            businessLeadsDto.setClueType(ErpConstant.ZERO.equals(businessLeadsDto.getClueType()) ? 391 : businessLeadsDto.getClueType());
            businessLeadsDto.setInquiry(ErpConstant.ZERO.equals(businessLeadsDto.getInquiry()) ? 4002 : businessLeadsDto.getInquiry());
        }else {
            businessLeadsDto.setClueType(391);
            businessLeadsDto.setInquiry(4002);
        }
        mv.addObject("businessLeadsDto", businessLeadsDto);
        // 线索类型
        mv.addObject("clueType", 391);
        // 询价行为
        List<SysOptionDefinitionDto> inquiryData = sysOptionDefinitionApiService.getByParentIdList(Collections.singletonList(391));
        mv.addObject("inquiryData", inquiryData);
        // 渠道类型
        List<SysOptionDefinitionDto> sourceData = sysOptionDefinitionApiService.getByParentIdList(Collections.singletonList(390));
        List<SysOptionDefinitionDto> typeList = getSysOptionIncluded(sourceData,bncServiceTypeIds);
        List<SysOptionDefinitionDto> allInquiryList = sysOptionDefinitionApiService.getByParentIdList(typeList.stream().map(SysOptionDefinitionDto::getSysOptionDefinitionId).collect(Collectors.toList()));
        List<Integer> newInquiryIdList = allInquiryList.stream().map(SysOptionDefinitionDto::getSysOptionDefinitionId).collect(Collectors.toList());
        List<SysOptionDefinitionDto> newSourceList = sysOptionDefinitionApiService.getByParentIdList(newInquiryIdList);
        mv.addObject("bussSource", newSourceList);
        // 渠道名称
        List<SysOptionDefinitionDto> newCommunicationList = sysOptionDefinitionApiService.getByParentIdList(Collections.singletonList(Objects.nonNull(businessLeadsDto.getSource()) && !ErpConstant.ZERO.equals(businessLeadsDto.getSource()) ? businessLeadsDto.getSource() : newSourceList.get(0).getSysOptionDefinitionId()));
        mv.addObject("communications", newCommunicationList);

        mv.addObject("leadsId", Objects.isNull(dto.getLeadsId()) ? 0 : dto.getLeadsId());
        mv.addObject("callInTraderId",Objects.isNull(dto.getTraderId()) ? 0 : dto.getTraderId());
        return mv;
    }


    /**
     * 筛选在范围内的字典选项
     * Gatlin
     */
    private List<SysOptionDefinitionDto> getSysOptionIncluded(List<SysOptionDefinitionDto> options,String ids){
        List<Integer> idsList= JSON.parseArray(ids,Integer.class);
        return options.stream()
                .filter(e->idsList.contains(e.getSysOptionDefinitionId()))
                .collect(Collectors.toList());
    }
}
