package com.newtask.goods;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.DateUtil;
import com.vedeng.goods.domain.entity.GoodsFinance;
import com.vedeng.goods.mapper.GoodsFinanceMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商品信息财务审计 每天同步前一天的商品信息到 T_GOODS_FINANCE 表中
 * @date 2022/11/28 9:42
 */
@JobHandler(value = "SyncSaleorderGoodSkuTask")
@Component
@Slf4j
public class SyncSaleorderGoodSkuTask extends AbstractJobHandler {

    @Autowired
    private SaleorderMapper saleorderMapper;

    @Autowired
    private GoodsFinanceMapper goodsFinanceMapper;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        XxlJobLogger.log("同步商品信息财务审计start-----------");
        if (StringUtils.isEmpty(s)) {
            s = DateUtil.convertString(DateUtil.getDateBefore(new Date(), 0), "yyyy-MM-dd");
        }
        long time = DateUtil.convertLong(s, DateUtil.DATE_FORMAT);
        while (true) {
            PageHelper.startPage(1, 1000);
            List<GoodsFinance> list = goodsFinanceMapper.getSkuNoListByValidTime(time);
            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            XxlJobLogger.log("批量插入T_GOODS_FINANCE，skuList："+ JSON.toJSONString(list));
            log.info("批量插入T_GOODS_FINANCE，skuList：{}", JSON.toJSONString(list));
            goodsFinanceMapper.batchInsert(list);
            Thread.sleep(5000);
        }
        XxlJobLogger.log("同步商品信息财务审计end-----------");
        return SUCCESS;
    }

}
