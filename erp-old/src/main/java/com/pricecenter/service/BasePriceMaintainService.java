package com.pricecenter.service;

import com.pricecenter.dto.*;
import com.vedeng.price.api.price.dto.price.SkuPriceContainsFeeApi;

import java.util.List;

public interface BasePriceMaintainService {

    PageResultDto<SkuPriceChangeApplyDto> findByPage(SkuPriceChangeApplyPageQueryDto skuPriceChangeApplyPageQueryDto);

    SkuPriceChangeApplyDto findBasePriceMaintainById(Integer priceInfoId);

    List<PurchaseHistoryDto> findPurchaseHistoryListBySkuId(Integer skuId);

    boolean informPriceChangeApply(EditBasePriceDto editBasePriceDto);

    SkuPriceChangeApplyDto getBasicPriceChangeInfoById(Integer skuPriceChangeApplyId);

    boolean updatePriceChangeStatus(Integer skuPriceChangeApplyId, int status);

    List<PriceInfoUploadResponseDto> batchAddPriceInfo(List<PriceInfoUploadDto> priceInfoUploadDtoList);

    List<Long> batchGetSkuPricing();

    boolean updatePriceChangeAuditorByIds(List<Long> skuChangeApplyIdList, List<Integer> userIds);

    Long addPriceChange(String skuNo);

    boolean enablePrice(Integer skuPriceChangeApplyId);

    boolean disablePrice(Integer skuPriceChangeApplyId,String disableReason);

    void batchUpdateGroupPrice(List<PriceSkuGroupPriceDto> pricedSkuList);

    PageResultDto findPricedSkuPageQuery(PricedSkuPageQueryDto pricedSkuPageQueryDto);

    /**
     * 更新商城可见状态
     * @param skuPriceChangeApplyId 价格变更申请ID
     * @param isVisible 可见状态（1-可见，0-不可见）
     * @return 更新是否成功
     */
    boolean updateVdVisibility(Integer skuPriceChangeApplyId, Integer isVdVisible);
    
    /**
     * 根据skuNo获取运费信息
     * @param skuNos
     * @return
     */
    List<SkuPriceContainsFeeApi> selectPriceInfoBySkuNos(List<String> skuNos);
}
