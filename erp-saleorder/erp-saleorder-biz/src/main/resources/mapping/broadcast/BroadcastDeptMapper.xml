<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.broadcast.mapdao.BroadcastDeptMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.erp.broadcast.domain.entity.BroadcastDeptEntity">
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="DEPT_NAME" jdbcType="VARCHAR" property="deptName"/>
        <result column="PARENT_ID" jdbcType="INTEGER" property="parentId"/>
        <result column="AED_USER_ID" jdbcType="VARCHAR" property="aedUserId"/>
        <result column="IS_DELETED" jdbcType="TINYINT" property="isDeleted"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
    </resultMap>

    <!-- 播报部门列表查询结果映射 -->
    <resultMap id="BroadCastDeptListResultMap" type="com.vedeng.erp.broadcast.domain.dto.BroadCastDeptListDto">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="dept_id" jdbcType="INTEGER" property="deptId" />
        <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
        <result column="group_name" jdbcType="VARCHAR" property="groupName" />
        <result column="aed_user_ids" jdbcType="VARCHAR" property="aedUserIds" />
        <result column="aed_usernames" jdbcType="VARCHAR" property="aedUsernames" />
        <result column="creator_real_name" jdbcType="VARCHAR" property="creatorRealName" />
        <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
        <result column="mod_time" jdbcType="TIMESTAMP" property="modTime" />
        <result column="creator" jdbcType="INTEGER" property="creator" />
        <result column="updater" jdbcType="INTEGER" property="updater" />
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, DEPT_NAME, PARENT_ID, AED_USER_ID, IS_DELETED, ADD_TIME, MOD_TIME, CREATOR, UPDATER
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_BROADCAST_DEPT
        where ID = #{id,jdbcType=INTEGER}
        and IS_DELETED = 0
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        update T_BROADCAST_DEPT
        set IS_DELETED = 1, MOD_TIME = now()
        where ID = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastDeptEntity">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into T_BROADCAST_DEPT (DEPT_NAME, PARENT_ID, AED_USER_ID,
        IS_DELETED, ADD_TIME, MOD_TIME,
        CREATOR, UPDATER)
        values (#{deptName,jdbcType=VARCHAR}, #{parentId,jdbcType=INTEGER}, #{aedUserId,jdbcType=VARCHAR},
        #{isDeleted,jdbcType=TINYINT}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP},
        #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER})
    </insert>

    <insert id="insertSelective" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastDeptEntity">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into T_BROADCAST_DEPT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptName != null">
                DEPT_NAME,
            </if>
            <if test="parentId != null">
                PARENT_ID,
            </if>
            <if test="aedUserId != null">
                AED_USER_ID,
            </if>
            <if test="isDeleted != null">
                IS_DELETED,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptName != null">
                #{deptName,jdbcType=VARCHAR},
            </if>
            <if test="parentId != null">
                #{parentId,jdbcType=INTEGER},
            </if>
            <if test="aedUserId != null">
                #{aedUserId,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastDeptEntity">
        update T_BROADCAST_DEPT
        <set>
            <if test="deptName != null">
                DEPT_NAME = #{deptName,jdbcType=VARCHAR},
            </if>
            <if test="parentId != null">
                PARENT_ID = #{parentId,jdbcType=INTEGER},
            </if>
            <if test="aedUserId != null">
                AED_USER_ID = #{aedUserId,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                IS_DELETED = #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
        </set>
        where ID = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastDeptEntity">
        update T_BROADCAST_DEPT
        set DEPT_NAME = #{deptName,jdbcType=VARCHAR},
        PARENT_ID = #{parentId,jdbcType=INTEGER},
        AED_USER_ID = #{aedUserId,jdbcType=VARCHAR},
        IS_DELETED = #{isDeleted,jdbcType=TINYINT},
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
        CREATOR = #{creator,jdbcType=INTEGER},
        UPDATER = #{updater,jdbcType=INTEGER}
        where ID = #{id,jdbcType=INTEGER}
    </update>
    <select id="listByParentId" resultType="com.vedeng.erp.broadcast.domain.entity.BroadcastDeptEntity">
        select
        <include refid="Base_Column_List" />
        from T_BROADCAST_DEPT
        where IS_DELETED = 0
        <if test="parentId != null">
            and PARENT_ID = #{parentId,jdbcType=INTEGER}
        </if>
        <if test="deptName != null and deptName != ''">
            and DEPT_NAME like concat('%',#{deptName,jdbcType=VARCHAR},'%')
        </if>
    </select>

    <!-- ========== 自定义SQL方法 ========== -->

    <!-- 根据部门名称查询部门（精确匹配） -->
    <select id="selectByDeptName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_BROADCAST_DEPT
        where IS_DELETED = 0
        and DEPT_NAME = #{deptName,jdbcType=VARCHAR}
        <choose>
            <when test="parentId == null">
                and (PARENT_ID is null or PARENT_ID = 0)
            </when>
            <otherwise>
                and PARENT_ID is not null and PARENT_ID != 0
            </otherwise>
        </choose>
    </select>

    <!-- 查询所有小组（parentId不为null且不为0） -->
    <select id="selectAllGroups" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_BROADCAST_DEPT
        where IS_DELETED = 0
        and PARENT_ID is not null
        and PARENT_ID != 0
        order by DEPT_NAME
    </select>

    <!-- 查询所有部门（parentId为null或为0） -->
    <select id="selectAllDepts" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_BROADCAST_DEPT
        where IS_DELETED = 0
        and (PARENT_ID is null or PARENT_ID = 0)
        order by DEPT_NAME
    </select>

    <!-- 分页查询播报部门列表 - 主要查询小组，顺带展示部门信息，按部门排序 -->
    <select id="selectBroadcastDeptListPage" resultMap="BroadCastDeptListResultMap">
        SELECT 
            g.ID as id,
            g.PARENT_ID as dept_id,
            p.DEPT_NAME as dept_name,
            g.DEPT_NAME as group_name,
            g.AED_USER_ID as aed_user_ids,
            GROUP_CONCAT(DISTINCT u.USERNAME ORDER BY u.USER_ID SEPARATOR ',') as aed_usernames,
            g.ADD_TIME as add_time,
            g.MOD_TIME as mod_time,
            g.CREATOR as creator,
            g.UPDATER as updater,
            g.IS_DELETED as is_deleted,
            (SELECT ud.REAL_NAME FROM T_USER_DETAIL ud WHERE ud.USER_ID = g.CREATOR) as creator_real_name
        FROM T_BROADCAST_DEPT g
        INNER JOIN T_BROADCAST_DEPT p ON g.PARENT_ID = p.ID AND p.IS_DELETED = 0
        LEFT JOIN T_USER u ON FIND_IN_SET(u.USER_ID, g.AED_USER_ID) > 0 AND u.IS_DISABLED = 0
        WHERE g.IS_DELETED = 0
        AND g.PARENT_ID > 0
        <if test="deptName != null and deptName != ''">
            AND p.DEPT_NAME LIKE CONCAT('%', #{deptName}, '%')
        </if>
        <if test="groupName != null and groupName != ''">
            AND g.DEPT_NAME LIKE CONCAT('%', #{groupName}, '%')
        </if>
        <if test="aedUserId != null">
            AND FIND_IN_SET(#{aedUserId}, g.AED_USER_ID) > 0
        </if>
        GROUP BY g.ID, g.DEPT_NAME, g.PARENT_ID, g.AED_USER_ID, g.ADD_TIME, g.MOD_TIME, g.CREATOR, g.UPDATER, g.IS_DELETED, p.DEPT_NAME
        ORDER BY p.DEPT_NAME ASC, g.DEPT_NAME ASC, g.ID ASC
    </select>

</mapper>
