package com.vedeng.erp.saleorder.buzlogic.create.impl;

import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.saleorder.buzlogic.create.EditSaleOrderBuzLogic;
import com.vedeng.order.model.Saleorder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class VsEditSaleOrderBuzLogic extends EditSaleOrderBuzLogic {

    public VsEditSaleOrderBuzLogic(){
        List<String> sequence = new ArrayList<>();
        sequence.add("saveEditSaleOrderInfo");
        super.setSequence(sequence);
    }


    public ResultInfo run(Saleorder saleorder){
        ResultInfo result = new ResultInfo(0,"操作成功");
        new VsEditSaleOrderBuzLogic();
        for (String methodName : getSequence()){
            switch (methodName) {
                case "saveEditSaleOrderInfo":
                    if(ErpConst.ZERO.equals(result.getCode())) {
                        result = super.saveEditSaleOrderInfo(saleorder);
                    }
                    break;
                default:
                    break;
            }
        }
        return result;
    }
}
