package com.vedeng.erp.aftersale.mapper;

import com.vedeng.erp.aftersale.domain.entity.AfterSalesDetailEntity;

import java.math.BigDecimal;
import java.util.List;

import com.vedeng.erp.aftersale.dto.AfterSaleSkuInfoDto;
import com.vedeng.erp.saleorder.dto.AfterSalesGoodsDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository("newAfterSalesDetailMapper")
public interface AfterSalesDetailMapper {
    int deleteByPrimaryKey(Integer afterSalesDetailId);

    int insert(AfterSalesDetailEntity record);

    int insertOrUpdate(AfterSalesDetailEntity record);

    int insertOrUpdateSelective(AfterSalesDetailEntity record);

    int insertSelective(AfterSalesDetailEntity record);

    AfterSalesDetailEntity selectByPrimaryKey(Integer afterSalesDetailId);

    int updateByPrimaryKeySelective(AfterSalesDetailEntity record);

    int updateByPrimaryKey(AfterSalesDetailEntity record);

    int updateBatch(List<AfterSalesDetailEntity> list);

    int updateBatchSelective(List<AfterSalesDetailEntity> list);

    int batchInsert(@Param("list") List<AfterSalesDetailEntity> list);

    AfterSalesDetailEntity findByAfterSalesId(@Param("afterSalesId")Integer afterSalesId);

    AfterSaleSkuInfoDto getGoodsNameModelAndSpecByAfterSalesGoodsId(Integer detailGoodsId);

    List<AfterSalesGoodsDto> getAfterSalesNum(Integer saleOrderId);

    BigDecimal getPublicRefundAmount(@Param("afterSalesId") Integer afterSalesId, 
                                     @Param("orderId") Integer orderId, 
                                     @Param("afterSalesStatus") Integer afterSalesStatus);

    BigDecimal getPrivateRefundAmount(@Param("afterSalesId") Integer afterSalesId,
                                     @Param("orderId") Integer orderId,
                                     @Param("afterSalesStatus") Integer afterSalesStatus);
    
    int updateRefundByAfterSalesDetailId(@Param("updatedRefund")Integer updatedRefund,@Param("afterSalesDetailId")Integer afterSalesDetailId);

    int updatePublicAndPrivateRefundAmount(@Param("refund") Integer refund, @Param("updatedPublicRefundAmount") BigDecimal updatedPublicRefundAmount, @Param("updatedPrivateRefundAmount") BigDecimal updatedPrivateRefundAmount, @Param("afterSalesDetailId") Integer afterSalesDetailId);
    
}