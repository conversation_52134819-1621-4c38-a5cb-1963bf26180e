package com.vedeng.erp.finance.api;

import com.vedeng.erp.finance.dto.CustomerAccountReqDto;
import com.vedeng.erp.finance.dto.RPayApplyJBankReceiptDto;
import com.vedeng.erp.trader.dto.CustomerBankAccountApiDto;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/26 15:15
 */
public interface CustomerAccountApiService {

    /**
     * 创建客户账户
     * @param customerAccountReqDto
     */
    void tryCreate(CustomerAccountReqDto customerAccountReqDto);

    /**
     * 建立付款申请和回单关系
     * @param rPayApplyJBankReceiptDto
     */
    void createPayApplyRelation(RPayApplyJBankReceiptDto rPayApplyJBankReceiptDto);

    void tryCreateBankAlias(Integer payApplyId,Integer bankId);

    /**
     * 获取客户账户信息
     *
     * @param customerAccountReqDto
     * @return
     */
    List<CustomerBankAccountApiDto> getCustomerAccount(CustomerAccountReqDto customerAccountReqDto);

    void updateLastUseTime(Long customerBankAccountId);

}
