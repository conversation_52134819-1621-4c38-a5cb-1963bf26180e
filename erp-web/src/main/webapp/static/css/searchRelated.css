.scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}
.scrollbar::-webkit-scrollbar-track {
    background: transparent;
    width: 6px;
    height: 6px;
}
.scrollbar::-webkit-scrollbar-thumb {
    background: #D7DADE;
    width: 6px;
    height: 6px;
    border-radius: 3px;
}
.scrollbar::-webkit-scrollbar-thumb:hover {
    background: #BABFC2;
}
.scrollbar::-webkit-scrollbar-thumb:active {
    background: #969B9E;
}



.vd-ui-search-related {
    position: relative;
    display: flex;
}

.vd-ui-search-related i {
    background: none;
}

.vd-ui-search-related>.vd-ui-input {
    width: 100%;
    display: inline-block;
    vertical-align: top;
    position: relative;
}

.vd-ui-search-related>.vd-ui-input>input {
    box-sizing: border-box;
    width: 100%;
    color: #333;
    background-color: #fff;
    border: solid 1px #BABFC2;
    border-radius: 3px;
    height: 33px;
    padding: 0px 10px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.1s linear;
    cursor: pointer;
}

.vd-ui-search-related>.vd-ui-input>input.focus {
    border-color: #09f;
}

.vd-ui-search-related>.vd-ui-input>input::-webkit-input-placeholder {
    color: #999;
}

.vd-ui-search-related>.vd-ui-related-wrap {
    width: 100%;
    position: absolute;
    top: 33px;
    z-index: 9;
    text-align: left;
}

.vd-ui-search-related>.vd-ui-related-wrap .related-ul {
    margin: 0;
    padding: 0;
    list-style: none;
    border-radius: 3px;
    background: #fff;
    border: solid 1px #BABFC2;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
    max-height: 370px;
    overflow-y: auto;
    display: none;
}

.vd-ui-search-related>.vd-ui-related-wrap .related-ul .loading {
    font-size: 14px;
    color: #666;
    height: 39px;
    display: flex;
    align-items: center;
    padding: 0px 10px;
    overflow: hidden;
}

.vd-ui-search-related>.vd-ui-related-wrap .related-ul .loading i {
    font-size: 16px;
    color: #09F;
    width: 16px;
    height: 16px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    position: relative;
    top: -1px;
    margin-right: 5px;
    animation: loading 1.8s linear infinite;
}

.vd-ui-search-related>.vd-ui-related-wrap .related-ul .failed-li {
    font-size: 14px;
    height: 39px;
    line-height: 39px;
    padding: 0px 10px;
}

.vd-ui-search-related>.vd-ui-related-wrap .related-ul .failed-li i {
    position: relative;
    top: 2px;
    font-size: 16px;
    color: #E64545;
    margin-right: 5px;
}

.vd-ui-search-related>.vd-ui-related-wrap .related-ul .failed-li .reload {
    color: #09f;
    cursor: pointer;
}

.vd-ui-search-related>.vd-ui-related-wrap .related-ul .failed-li .reload:hover {
    color: #f60;
}

.vd-ui-search-related>.vd-ui-related-wrap .related-ul .empty-li {
    font-size: 14px;
    height: 39px;
    line-height: 39px;
    padding: 0px 10px;
    text-align: center;
    color: #999;
    background: #fff !important;
    border-top: 0 !important;
}

.vd-ui-search-related>.vd-ui-related-wrap .related-ul .empty-li>span {
    color: #09f;
    cursor: pointer;
}

.vd-ui-search-related>.vd-ui-related-wrap .related-ul .empty-li>span>i {
    font-size: 16px;
    position: relative;
    top: 1.5px;
}

.vd-ui-search-related>.vd-ui-related-wrap .related-ul .empty-li>span:hover {
    color: #f60;
}

.vd-ui-search-related>.vd-ui-related-wrap .related-ul .search-list {
    padding: 5px 10px;
}

.vd-ui-search-related>.vd-ui-related-wrap .related-ul .search-list .local-data {
    font-size: 12px;
    color: #999;
    line-height: 30px;
}

.vd-ui-search-related>.vd-ui-related-wrap .related-ul .search-list .sr-item {
    padding: 6px 0;
    display: flex;
    justify-content: space-between;
    cursor: pointer;
}

.vd-ui-search-related>.vd-ui-related-wrap .related-ul .search-list .sr-item .sr-item-left {
    flex: 1;
    min-width: 0;
    margin-right: 10px;
}

.vd-ui-search-related>.vd-ui-related-wrap .related-ul .search-list .sr-item .sr-item-left>p {
    font-size: 14px;
    color: #333;
    margin: 0;
    padding: 0;
}

.vd-ui-search-related>.vd-ui-related-wrap .related-ul .search-list .sr-item .sr-item-right {
    width: 120px;
    flex-shrink: 0;
    text-align: right;
    color: #999;
}

.vd-ui-search-related>.vd-ui-related-wrap .related-ul .search-list .sr-item:hover .sr-item-left {
    color: #f60;
}

.vd-ui-search-related>.vd-ui-related-wrap .related-ul .search-list .sr-item:hover .sr-item-right {
    color: #f60;
}

.vd-ui-search-related>.vd-ui-related-wrap .related-ul .search-list .sr-item.disabled {
    color: #999;
    pointer-events: none;
}