<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.dao.FirstEngageGenerateMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.model.FirstEngageGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <id column="FIRST_ENGAGE_ID" jdbcType="INTEGER" property="firstEngageId" />
    <result column="REGISTRATION_NUMBER_ID" jdbcType="INTEGER" property="registrationNumberId" />
    <result column="GOODS_TYPE" jdbcType="INTEGER" property="goodsType" />
    <result column="BRAND_TYPE" jdbcType="TINYINT" property="brandType" />
    <result column="STANDARD_CATEGORY_TYPE" jdbcType="TINYINT" property="standardCategoryType" />
    <result column="NEW_STANDARD_CATEGORY_ID" jdbcType="INTEGER" property="newStandardCategoryId" />
    <result column="OLD_STANDARD_CATEGORY_ID" jdbcType="INTEGER" property="oldStandardCategoryId" />
    <result column="EFFECTIVE_DAY_UNIT" jdbcType="TINYINT" property="effectiveDayUnit" />
    <result column="EFFECTIVE_DAYS" jdbcType="INTEGER" property="effectiveDays" />
    <result column="SORT_DAYS" jdbcType="INTEGER" property="sortDays" />
    <result column="CONDITION_ONE" jdbcType="INTEGER" property="conditionOne" />
    <result column="TEMPERATURE" jdbcType="VARCHAR" property="temperature" />
    <result column="CHECK_AGAIN" jdbcType="TINYINT" property="checkAgain" />
    <result column="STATUS" jdbcType="TINYINT" property="status" />
    <result column="IS_DELETED" jdbcType="TINYINT" property="isDeleted" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    FIRST_ENGAGE_ID, REGISTRATION_NUMBER_ID, GOODS_TYPE, BRAND_TYPE, STANDARD_CATEGORY_TYPE, 
    NEW_STANDARD_CATEGORY_ID, OLD_STANDARD_CATEGORY_ID, EFFECTIVE_DAY_UNIT, EFFECTIVE_DAYS, 
    SORT_DAYS, CONDITION_ONE, TEMPERATURE, CHECK_AGAIN, `STATUS`, IS_DELETED, ADD_TIME, 
    CREATOR, MOD_TIME, UPDATER, COMMENTS
  </sql>
  <select id="selectByExample" parameterType="com.vedeng.goods.model.FirstEngageGenerateExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_FIRST_ENGAGE
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from T_FIRST_ENGAGE
    where FIRST_ENGAGE_ID = #{firstEngageId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    delete from T_FIRST_ENGAGE
    where FIRST_ENGAGE_ID = #{firstEngageId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.goods.model.FirstEngageGenerateExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    delete from T_FIRST_ENGAGE
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.goods.model.FirstEngageGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <selectKey keyProperty="firstEngageId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_FIRST_ENGAGE (REGISTRATION_NUMBER_ID, GOODS_TYPE, 
      BRAND_TYPE, STANDARD_CATEGORY_TYPE, NEW_STANDARD_CATEGORY_ID, 
      OLD_STANDARD_CATEGORY_ID, EFFECTIVE_DAY_UNIT, 
      EFFECTIVE_DAYS, SORT_DAYS, CONDITION_ONE, 
      TEMPERATURE, CHECK_AGAIN, `STATUS`, 
      IS_DELETED, ADD_TIME, CREATOR, 
      MOD_TIME, UPDATER, COMMENTS
      )
    values (#{registrationNumberId,jdbcType=INTEGER}, #{goodsType,jdbcType=INTEGER}, 
      #{brandType,jdbcType=TINYINT}, #{standardCategoryType,jdbcType=TINYINT}, #{newStandardCategoryId,jdbcType=INTEGER}, 
      #{oldStandardCategoryId,jdbcType=INTEGER}, #{effectiveDayUnit,jdbcType=TINYINT}, 
      #{effectiveDays,jdbcType=INTEGER}, #{sortDays,jdbcType=INTEGER}, #{conditionOne,jdbcType=INTEGER}, 
      #{temperature,jdbcType=VARCHAR}, #{checkAgain,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, 
      #{isDeleted,jdbcType=TINYINT}, #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, 
      #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}, #{comments,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.goods.model.FirstEngageGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <selectKey keyProperty="firstEngageId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_FIRST_ENGAGE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="registrationNumberId != null">
        REGISTRATION_NUMBER_ID,
      </if>
      <if test="goodsType != null">
        GOODS_TYPE,
      </if>
      <if test="brandType != null">
        BRAND_TYPE,
      </if>
      <if test="standardCategoryType != null">
        STANDARD_CATEGORY_TYPE,
      </if>
      <if test="newStandardCategoryId != null">
        NEW_STANDARD_CATEGORY_ID,
      </if>
      <if test="oldStandardCategoryId != null">
        OLD_STANDARD_CATEGORY_ID,
      </if>
      <if test="effectiveDayUnit != null">
        EFFECTIVE_DAY_UNIT,
      </if>
      <if test="effectiveDays != null">
        EFFECTIVE_DAYS,
      </if>
      <if test="sortDays != null">
        SORT_DAYS,
      </if>
      <if test="conditionOne != null">
        CONDITION_ONE,
      </if>
      <if test="temperature != null">
        TEMPERATURE,
      </if>
      <if test="checkAgain != null">
        CHECK_AGAIN,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
      <if test="isDeleted != null">
        IS_DELETED,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="comments != null">
        COMMENTS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="registrationNumberId != null">
        #{registrationNumberId,jdbcType=INTEGER},
      </if>
      <if test="goodsType != null">
        #{goodsType,jdbcType=INTEGER},
      </if>
      <if test="brandType != null">
        #{brandType,jdbcType=TINYINT},
      </if>
      <if test="standardCategoryType != null">
        #{standardCategoryType,jdbcType=TINYINT},
      </if>
      <if test="newStandardCategoryId != null">
        #{newStandardCategoryId,jdbcType=INTEGER},
      </if>
      <if test="oldStandardCategoryId != null">
        #{oldStandardCategoryId,jdbcType=INTEGER},
      </if>
      <if test="effectiveDayUnit != null">
        #{effectiveDayUnit,jdbcType=TINYINT},
      </if>
      <if test="effectiveDays != null">
        #{effectiveDays,jdbcType=INTEGER},
      </if>
      <if test="sortDays != null">
        #{sortDays,jdbcType=INTEGER},
      </if>
      <if test="conditionOne != null">
        #{conditionOne,jdbcType=INTEGER},
      </if>
      <if test="temperature != null">
        #{temperature,jdbcType=VARCHAR},
      </if>
      <if test="checkAgain != null">
        #{checkAgain,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.goods.model.FirstEngageGenerateExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    select count(*) from T_FIRST_ENGAGE
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update T_FIRST_ENGAGE
    <set>
      <if test="record.firstEngageId != null">
        FIRST_ENGAGE_ID = #{record.firstEngageId,jdbcType=INTEGER},
      </if>
      <if test="record.registrationNumberId != null">
        REGISTRATION_NUMBER_ID = #{record.registrationNumberId,jdbcType=INTEGER},
      </if>
      <if test="record.goodsType != null">
        GOODS_TYPE = #{record.goodsType,jdbcType=INTEGER},
      </if>
      <if test="record.brandType != null">
        BRAND_TYPE = #{record.brandType,jdbcType=TINYINT},
      </if>
      <if test="record.standardCategoryType != null">
        STANDARD_CATEGORY_TYPE = #{record.standardCategoryType,jdbcType=TINYINT},
      </if>
      <if test="record.newStandardCategoryId != null">
        NEW_STANDARD_CATEGORY_ID = #{record.newStandardCategoryId,jdbcType=INTEGER},
      </if>
      <if test="record.oldStandardCategoryId != null">
        OLD_STANDARD_CATEGORY_ID = #{record.oldStandardCategoryId,jdbcType=INTEGER},
      </if>
      <if test="record.effectiveDayUnit != null">
        EFFECTIVE_DAY_UNIT = #{record.effectiveDayUnit,jdbcType=TINYINT},
      </if>
      <if test="record.effectiveDays != null">
        EFFECTIVE_DAYS = #{record.effectiveDays,jdbcType=INTEGER},
      </if>
      <if test="record.sortDays != null">
        SORT_DAYS = #{record.sortDays,jdbcType=INTEGER},
      </if>
      <if test="record.conditionOne != null">
        CONDITION_ONE = #{record.conditionOne,jdbcType=INTEGER},
      </if>
      <if test="record.temperature != null">
        TEMPERATURE = #{record.temperature,jdbcType=VARCHAR},
      </if>
      <if test="record.checkAgain != null">
        CHECK_AGAIN = #{record.checkAgain,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        `STATUS` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null">
        IS_DELETED = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.addTime != null">
        ADD_TIME = #{record.addTime,jdbcType=BIGINT},
      </if>
      <if test="record.creator != null">
        CREATOR = #{record.creator,jdbcType=INTEGER},
      </if>
      <if test="record.modTime != null">
        MOD_TIME = #{record.modTime,jdbcType=BIGINT},
      </if>
      <if test="record.updater != null">
        UPDATER = #{record.updater,jdbcType=INTEGER},
      </if>
      <if test="record.comments != null">
        COMMENTS = #{record.comments,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update T_FIRST_ENGAGE
    set FIRST_ENGAGE_ID = #{record.firstEngageId,jdbcType=INTEGER},
      REGISTRATION_NUMBER_ID = #{record.registrationNumberId,jdbcType=INTEGER},
      GOODS_TYPE = #{record.goodsType,jdbcType=INTEGER},
      BRAND_TYPE = #{record.brandType,jdbcType=TINYINT},
      STANDARD_CATEGORY_TYPE = #{record.standardCategoryType,jdbcType=TINYINT},
      NEW_STANDARD_CATEGORY_ID = #{record.newStandardCategoryId,jdbcType=INTEGER},
      OLD_STANDARD_CATEGORY_ID = #{record.oldStandardCategoryId,jdbcType=INTEGER},
      EFFECTIVE_DAY_UNIT = #{record.effectiveDayUnit,jdbcType=TINYINT},
      EFFECTIVE_DAYS = #{record.effectiveDays,jdbcType=INTEGER},
      SORT_DAYS = #{record.sortDays,jdbcType=INTEGER},
      CONDITION_ONE = #{record.conditionOne,jdbcType=INTEGER},
      TEMPERATURE = #{record.temperature,jdbcType=VARCHAR},
      CHECK_AGAIN = #{record.checkAgain,jdbcType=TINYINT},
      `STATUS` = #{record.status,jdbcType=TINYINT},
      IS_DELETED = #{record.isDeleted,jdbcType=TINYINT},
      ADD_TIME = #{record.addTime,jdbcType=BIGINT},
      CREATOR = #{record.creator,jdbcType=INTEGER},
      MOD_TIME = #{record.modTime,jdbcType=BIGINT},
      UPDATER = #{record.updater,jdbcType=INTEGER},
      COMMENTS = #{record.comments,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.model.FirstEngageGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update T_FIRST_ENGAGE
    <set>
      <if test="registrationNumberId != null">
        REGISTRATION_NUMBER_ID = #{registrationNumberId,jdbcType=INTEGER},
      </if>
      <if test="goodsType != null">
        GOODS_TYPE = #{goodsType,jdbcType=INTEGER},
      </if>
      <if test="brandType != null">
        BRAND_TYPE = #{brandType,jdbcType=TINYINT},
      </if>
      <if test="standardCategoryType != null">
        STANDARD_CATEGORY_TYPE = #{standardCategoryType,jdbcType=TINYINT},
      </if>
      <if test="newStandardCategoryId != null">
        NEW_STANDARD_CATEGORY_ID = #{newStandardCategoryId,jdbcType=INTEGER},
      </if>
      <if test="oldStandardCategoryId != null">
        OLD_STANDARD_CATEGORY_ID = #{oldStandardCategoryId,jdbcType=INTEGER},
      </if>
      <if test="effectiveDayUnit != null">
        EFFECTIVE_DAY_UNIT = #{effectiveDayUnit,jdbcType=TINYINT},
      </if>
      <if test="effectiveDays != null">
        EFFECTIVE_DAYS = #{effectiveDays,jdbcType=INTEGER},
      </if>
      <if test="sortDays != null">
        SORT_DAYS = #{sortDays,jdbcType=INTEGER},
      </if>
      <if test="conditionOne != null">
        CONDITION_ONE = #{conditionOne,jdbcType=INTEGER},
      </if>
      <if test="temperature != null">
        TEMPERATURE = #{temperature,jdbcType=VARCHAR},
      </if>
      <if test="checkAgain != null">
        CHECK_AGAIN = #{checkAgain,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `STATUS` = #{status,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        IS_DELETED = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="comments != null">
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
    </set>
    where FIRST_ENGAGE_ID = #{firstEngageId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.model.FirstEngageGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update T_FIRST_ENGAGE
    set REGISTRATION_NUMBER_ID = #{registrationNumberId,jdbcType=INTEGER},
      GOODS_TYPE = #{goodsType,jdbcType=INTEGER},
      BRAND_TYPE = #{brandType,jdbcType=TINYINT},
      STANDARD_CATEGORY_TYPE = #{standardCategoryType,jdbcType=TINYINT},
      NEW_STANDARD_CATEGORY_ID = #{newStandardCategoryId,jdbcType=INTEGER},
      OLD_STANDARD_CATEGORY_ID = #{oldStandardCategoryId,jdbcType=INTEGER},
      EFFECTIVE_DAY_UNIT = #{effectiveDayUnit,jdbcType=TINYINT},
      EFFECTIVE_DAYS = #{effectiveDays,jdbcType=INTEGER},
      SORT_DAYS = #{sortDays,jdbcType=INTEGER},
      CONDITION_ONE = #{conditionOne,jdbcType=INTEGER},
      TEMPERATURE = #{temperature,jdbcType=VARCHAR},
      CHECK_AGAIN = #{checkAgain,jdbcType=TINYINT},
      `STATUS` = #{status,jdbcType=TINYINT},
      IS_DELETED = #{isDeleted,jdbcType=TINYINT},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      COMMENTS = #{comments,jdbcType=VARCHAR}
    where FIRST_ENGAGE_ID = #{firstEngageId,jdbcType=INTEGER}
  </update>

  <select id="getFirstEngageInfoBySkuNo" parameterType="java.lang.String" resultType="com.vedeng.order.model.vo.FirstEngageInfoDto">
    SELECT
        company.PRODUCT_COMPANY_CHINESE_NAME COMPANY_NAME,
	    number.REGISTRATION_NUMBER
    FROM V_CORE_SKU K
    LEFT JOIN V_CORE_SPU spu ON K.SPU_ID = spu.SPU_ID
    LEFT JOIN T_FIRST_ENGAGE engage ON spu.FIRST_ENGAGE_ID = engage.FIRST_ENGAGE_ID
    LEFT JOIN T_REGISTRATION_NUMBER number ON engage.REGISTRATION_NUMBER_ID = number.REGISTRATION_NUMBER_ID
    LEFT JOIN T_PRODUCT_COMPANY company ON number.PRODUCT_COMPANY_ID = company.PRODUCT_COMPANY_ID
    WHERE K.SKU_NO = #{skuNo,jdbcType=VARCHAR}
  </select>
    <select id="getInfoByFirstEngageId" resultType="com.vedeng.order.model.vo.FirstEngageInfoDto">
      select FE.FIRST_ENGAGE_ID firstEngageId, RN.REGISTRATION_NUMBER registrationNumber
      from T_FIRST_ENGAGE FE
      LEFT JOIN T_REGISTRATION_NUMBER RN on RN.REGISTRATION_NUMBER_ID = FE.REGISTRATION_NUMBER_ID
      WHERE FE.FIRST_ENGAGE_ID = #{logBizId,jdbcType = INTEGER}
    </select>
</mapper>