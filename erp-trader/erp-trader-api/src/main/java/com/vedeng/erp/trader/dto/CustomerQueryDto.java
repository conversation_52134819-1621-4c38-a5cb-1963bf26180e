package com.vedeng.erp.trader.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 */
@Data
public class CustomerQueryDto {

    public static final List<Integer> NOT_FOUND_SENTINEL = Collections.singletonList(-1);



    private String customerName;

    /**
     * 0 临床医疗 终端 1 临床医疗 分销
     */
    private Integer customerTypeQuery;

    private Integer customerLevel;//客户等级

    private List<Integer> userIdList;

    private Integer attributableUserId;


    //经销商有效性
    private Integer effectiveness;
    //终端机构性质
    private String institutionNature;
    //终端机构评级
    private List<Integer> institutionLevel;
    //主营商品范畴类别
    private String traderCustomerMainCategoryType;
    //主营商品类型
    private Integer skuType;
    //销售类别
    private String traderCustomerOwnership;
    //核心资源
    private Integer traderCustomerDevelopLevel;
    //客户等级
    private String traderCustomerLevelGrade;


    /**
     * 原归属销售
     */
    private List<Integer> originUserList;

    /**
     * 营销客户类型
     */
    private List<String> traderCustomerMarketingTypeList = new ArrayList<>();

    /**
     * 机构类型
     */
    private List<String> institutionTypeList = new ArrayList<>();


    /**
     * 默认新增时间倒叙倒叙 1 倒排 0 正序
     */
    private Integer addTimeOrderBy;

    /**
     * 修改时间顺序
     */
    private Integer modTimeOrderBy;



    /**
     * T_TRADER_CUSTOMER_MARKETING_PRINCIPAL 其他政府关系
     */
    private String otherGovernmentRelation;



}
