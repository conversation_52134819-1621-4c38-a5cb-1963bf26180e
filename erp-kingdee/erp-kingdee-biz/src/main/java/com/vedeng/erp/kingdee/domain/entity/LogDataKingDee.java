package com.vedeng.erp.kingdee.domain.entity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 审计数据金蝶推送表
 */
public class LogDataKingDee {
    /**
     * 主键
     */
    private Long logDataId;

    /**
     * ERP出入库单号
     */
    private String outInNo;

    /**
     * 商品ID
     */
    private Integer goodsId;

    /**
     * ERP订单号
     */
    private String orderNo;

    /**
     * 关联单号
     */
    private String assOrderNo;

    /**
     * 单据类型
     */
    private String orderType;

    /**
     * 0 入库  1出库
     */
    private Integer logType;

    /**
     * 不含税单价
     */
    private BigDecimal realPrice;

    /**
     * 不含税总金额
     */
    private BigDecimal realTotalAmount;

    /**
     * 是否删除 0否 1是
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 添加人ID
     */
    private Integer creator;

    /**
     * 修改时间
     */
    private Date modTime;

    /**
     * 更新人ID
     */
    private Integer updater;

    public Long getLogDataId() {
        return logDataId;
    }

    public void setLogDataId(Long logDataId) {
        this.logDataId = logDataId;
    }

    public String getOutInNo() {
        return outInNo;
    }

    public void setOutInNo(String outInNo) {
        this.outInNo = outInNo;
    }

    public Integer getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getAssOrderNo() {
        return assOrderNo;
    }

    public void setAssOrderNo(String assOrderNo) {
        this.assOrderNo = assOrderNo;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public Integer getLogType() {
        return logType;
    }

    public void setLogType(Integer logType) {
        this.logType = logType;
    }

    public BigDecimal getRealPrice() {
        return realPrice;
    }

    public void setRealPrice(BigDecimal realPrice) {
        this.realPrice = realPrice;
    }

    public BigDecimal getRealTotalAmount() {
        return realTotalAmount;
    }

    public void setRealTotalAmount(BigDecimal realTotalAmount) {
        this.realTotalAmount = realTotalAmount;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Date getModTime() {
        return modTime;
    }

    public void setModTime(Date modTime) {
        this.modTime = modTime;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }
}