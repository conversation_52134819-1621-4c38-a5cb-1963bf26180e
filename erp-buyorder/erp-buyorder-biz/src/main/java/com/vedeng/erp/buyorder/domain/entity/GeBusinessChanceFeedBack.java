package com.vedeng.erp.buyorder.domain.entity;

import java.util.Date;

public class GeBusinessChanceFeedBack {
    /**   GE_BUSSINESS_CHANCE_FEEDBACK_ID **/
    private Integer geBussinessChanceFeedbackId;

    /** GE商机ID  GE_BUSSINESS_CHANCE_ID **/
    private Integer geBussinessChanceId;

    /** 报单状态 1可跟进 2不可跟进  STATUS **/
    private Integer status;

    /** 跟进状态备注  CONTENT **/
    private String content;

    /** 是否有account 0无 1有  IS_HAVING_ACCOUNT **/
    private Integer isHavingAccount;

    /** account名  ACCOUNT_NAME **/
    private String accountName;

    /** account销售区域ID  ACCOUNT_AREA_ID **/
    private Integer accountAreaId;

    /** account销售区域  ACCOUNT_AREA **/
    private String accountArea;

    /** account详细地址  ACCOUNT_ADDRESS **/
    private String accountAddress;

    /** 是否有MPC 0无 1有  IS_HAVING_MPC **/
    private Integer isHavingMpc;

    /** MPC详情  MPC_DETAIL **/
    private String mpcDetail;

    /** 创建时间  ADD_TIME **/
    private Date addTime;

    /** 更新时间  MOD_TIME **/
    private Date modTime;

    /** 创建人  CREATOR **/
    private Integer creator;

    /** 更新人  UPDATER **/
    private Integer updater;

    /** 创建人真实姓名  CREATOR_NAME **/
    private String creatorName;

    /** 修改人真实姓名  UPDATER_NAME **/
    private String updaterName;

    /** 是否删除 0否 1是  IS_DELETE **/
    private Integer isDelete;

    /**     GE_BUSSINESS_CHANCE_FEEDBACK_ID   **/
    public Integer getGeBussinessChanceFeedbackId() {
        return geBussinessChanceFeedbackId;
    }

    /**     GE_BUSSINESS_CHANCE_FEEDBACK_ID   **/
    public void setGeBussinessChanceFeedbackId(Integer geBussinessChanceFeedbackId) {
        this.geBussinessChanceFeedbackId = geBussinessChanceFeedbackId;
    }

    /**   GE商机ID  GE_BUSSINESS_CHANCE_ID   **/
    public Integer getGeBussinessChanceId() {
        return geBussinessChanceId;
    }

    /**   GE商机ID  GE_BUSSINESS_CHANCE_ID   **/
    public void setGeBussinessChanceId(Integer geBussinessChanceId) {
        this.geBussinessChanceId = geBussinessChanceId;
    }

    /**   报单状态 1可跟进 2不可跟进  STATUS   **/
    public Integer getStatus() {
        return status;
    }

    /**   报单状态 1可跟进 2不可跟进  STATUS   **/
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**   跟进状态备注  CONTENT   **/
    public String getContent() {
        return content;
    }

    /**   跟进状态备注  CONTENT   **/
    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }

    /**   是否有account 0无 1有  IS_HAVING_ACCOUNT   **/
    public Integer getIsHavingAccount() {
        return isHavingAccount;
    }

    /**   是否有account 0无 1有  IS_HAVING_ACCOUNT   **/
    public void setIsHavingAccount(Integer isHavingAccount) {
        this.isHavingAccount = isHavingAccount;
    }

    /**   account名  ACCOUNT_NAME   **/
    public String getAccountName() {
        return accountName;
    }

    /**   account名  ACCOUNT_NAME   **/
    public void setAccountName(String accountName) {
        this.accountName = accountName == null ? null : accountName.trim();
    }

    /**   account销售区域ID  ACCOUNT_AREA_ID   **/
    public Integer getAccountAreaId() {
        return accountAreaId;
    }

    /**   account销售区域ID  ACCOUNT_AREA_ID   **/
    public void setAccountAreaId(Integer accountAreaId) {
        this.accountAreaId = accountAreaId;
    }

    /**   account销售区域  ACCOUNT_AREA   **/
    public String getAccountArea() {
        return accountArea;
    }

    /**   account销售区域  ACCOUNT_AREA   **/
    public void setAccountArea(String accountArea) {
        this.accountArea = accountArea == null ? null : accountArea.trim();
    }

    /**   account详细地址  ACCOUNT_ADDRESS   **/
    public String getAccountAddress() {
        return accountAddress;
    }

    /**   account详细地址  ACCOUNT_ADDRESS   **/
    public void setAccountAddress(String accountAddress) {
        this.accountAddress = accountAddress == null ? null : accountAddress.trim();
    }

    /**   是否有MPC 0无 1有  IS_HAVING_MPC   **/
    public Integer getIsHavingMpc() {
        return isHavingMpc;
    }

    /**   是否有MPC 0无 1有  IS_HAVING_MPC   **/
    public void setIsHavingMpc(Integer isHavingMpc) {
        this.isHavingMpc = isHavingMpc;
    }

    /**   MPC详情  MPC_DETAIL   **/
    public String getMpcDetail() {
        return mpcDetail;
    }

    /**   MPC详情  MPC_DETAIL   **/
    public void setMpcDetail(String mpcDetail) {
        this.mpcDetail = mpcDetail == null ? null : mpcDetail.trim();
    }

    /**   创建时间  ADD_TIME   **/
    public Date getAddTime() {
        return addTime;
    }

    /**   创建时间  ADD_TIME   **/
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**   更新时间  MOD_TIME   **/
    public Date getModTime() {
        return modTime;
    }

    /**   更新时间  MOD_TIME   **/
    public void setModTime(Date modTime) {
        this.modTime = modTime;
    }

    /**   创建人  CREATOR   **/
    public Integer getCreator() {
        return creator;
    }

    /**   创建人  CREATOR   **/
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**   更新人  UPDATER   **/
    public Integer getUpdater() {
        return updater;
    }

    /**   更新人  UPDATER   **/
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    /**   创建人真实姓名  CREATOR_NAME   **/
    public String getCreatorName() {
        return creatorName;
    }

    /**   创建人真实姓名  CREATOR_NAME   **/
    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName == null ? null : creatorName.trim();
    }

    /**   修改人真实姓名  UPDATER_NAME   **/
    public String getUpdaterName() {
        return updaterName;
    }

    /**   修改人真实姓名  UPDATER_NAME   **/
    public void setUpdaterName(String updaterName) {
        this.updaterName = updaterName == null ? null : updaterName.trim();
    }

    /**   是否删除 0否 1是  IS_DELETE   **/
    public Integer getIsDelete() {
        return isDelete;
    }

    /**   是否删除 0否 1是  IS_DELETE   **/
    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }
}