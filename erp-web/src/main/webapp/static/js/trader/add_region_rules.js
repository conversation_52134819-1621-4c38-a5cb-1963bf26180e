var index=0;
$(function() {

    var cansubmit = true;
    $("#submit").click(function() {
        checkLogin();
        layer.confirm('是否保存修改?', {icon: 3, title:'提示'}, function(index){
            //do something
            layer.close(index);
            if(checkAllData()){
                if (cansubmit) {
                    cansubmit = false;
                    // $("form").attr("enctype", "multipart/form-data");
                    var regionId =  $('select[name="zone"] option:selected').val();
                    var userId =  $("#userId option:selected").val();
                    var publicCustomerRegionRulesId =  $("#publicCustomerRegionRulesId").val();
                    var aaa = new FormData($("form")[0]);
                    console.log(aaa)
                    $.ajax({
                        type: "POST",
                        url: "/trader/customer/saveRegionRules.do",
                        data: aaa,
                        dataType:'json',
                        contentType: false,
                        processData:false,
                        success: function(data){
                            if (data.code == 1) {
                                // alert(data.data)
                                $("#message").html(data.message);
                                flag=false
                                cansubmit = true;
                                // layer.msg('操作成功', {
                                //     icon: 1,
                                //     time: 1000 //2秒关闭（如果不配置，默认是3秒）
                                // }, function(){
                                //     //do something
                                //
                                //     $("#search").submit();
                                // });
                                // debugger

                                // layer.alert("操作成功失败");
                            } else if (data.code == 2) {
                                $("#message").html(data.data.provinceName+"-"+data.data.cityName+"-"+data.data.zoneName+"已分配给"+data.data.username);
                                flag=false;
                                cansubmit = true;
                                // layer.msg('操作失败', {
                                //     icon: 2,
                                //     time: 1000 //2秒关闭（如果不配置，默认是3秒）
                                // }, function () {
                                //     //do something
                                //     // $("#search").submit();
                                // });
                            }else if(data.code == 0){

                                window.location.href='/trader/customer/regionRulesList.do?flag=1';
                            } else {
                                layer.msg('操作失败', {
                                    icon: 2,
                                    time: 1000 //2秒关闭（如果不配置，默认是3秒）
                                }, function(){
                                    //do something
                                    // $("#search").submit();
                                });
                                cansubmit = true;
                            }

                        },
                        error:function(data){
                            if (data.status == 1001) {
                                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
                            }
                            cansubmit = true;
                        }
                    });
                } else {
                    console.log("请勿重复提交");
                    return false;
                }
            }else{
                return false;
            }
        },function (index){
            layer.close(index);
            return false;
        });

    })
});
var callbackdata = function (){

}
function del(obj){
    $(obj).parent().parent().parent().remove();
}

function con_add(pre,aa){
    var kind=0;
    // debugger
    var index = $("#index0").val();
    if (index == null || index == undefined) {
        index = 0;
    }
    // kind=id;
    //
    // if($(".c_"+id)!=null) {
    //     var length = $(".c_" + id).length
    //     if (length > 4) {
    //         layer.alert("该种资质最多可上传5张，无法继续添加");
    //         return;
    //     }
    // }
    $option = "<option value='0'>请选择</option>";
    // $option += "<option value='-1'>全部</option>";
    $.ajax
    ({
        type : "POST",
        url : "/system/region/getregion.do",
        data :{'regionId':1},
        dataType : 'json',
        async:false,
        success : function(data) {
            $.each(data.listData,function(i,n){
                $option += "<option value='"+data.listData[i]['regionId']+"'>"+data.listData[i]['regionName']+"</option>";
            });
            // $("#" + selectIdPrefix).empty();
            // $("#" + selectIdPrefix).html($option);
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
            }
        }
    });
    var html = '<ul>\n' +
        '            <li>\n' +
        '                <!--<label class="infor_name ">&lt;!&ndash;<span class="font-red">*</span>区域：&ndash;&gt;</label>-->\n' +
        '                <ul class="inputfloat f_left xx">\n' +
        '                    <li>\n' +
        '                        <select class="wid16 selector" name="province" onchange="changeAreaMy(\''+pre+index+'\',\'\',1);checkAllData(\''+pre+index+'\')" id="'+pre+index+'-province">\n' +
        $option+
        '                        </select>\n' +
        '                    </li>\n' +
        '                    <li>\n' +
        '                        <select class="wid16 selector" name="city" style="display: none" onchange="changeAreaMy(\''+pre+index+'\',\'\',2);checkAllData(\''+pre+index+'\')" id="'+pre+index+'-city">\n' +
        '                            <option value="0">请选择</option>\n' +
        '                        </select>\n' +
        '                    </li>\n' +
        '                    <li>\n' +
        '                        <select class="wid16 selector" name="zone" style="display: none" onchange="changeAreaMy(\''+pre+index+'\',\'\',3);checkAllData(\''+pre+index+'\')" id="'+pre+index+'-zone"> \n' +
        '                            <option value="0">请选择</option>\n' +
        '                        </select>\n' +
        '                    </li>\n' +
        '                    <li>\n' +
        '                        <span class="font-red iconfont icon-cha cursor-pointer" style="margin-top: 3px;" onclick="del(this);checkAllData()" ></span>\n' +
        '                    </li>\n' +
        '\n' +
        '                </ul>\n' +
        '            </li>\n' +
        '        </ul>';
    $("#addHtml").before(html);
    index++;
    // alert(index)
    $("#index0").val(index);
}


function custom_close(){
    window.location.href='/trader/customer/regionRulesList.do';
}
function getUserData(){
    var userId =  $("#userId option:selected").val();
    // alert(userId);
    if (userId == null || userId == -1 || userId == undefined) {
        $("#u-message").css("display","");
        $("#checkData").val(0);
        $("#user-message").html("");
        return;
    }else {
        $("#u-message").css("display","none");
    }
    // alert(1)
    $.ajax({
        type: "POST",
        url: "/trader/customer/getUserInfo.do",
        data:  {"userId":userId},
        dataType:'json',
        success: function(data){
            if (data.code == 0) {
                // alert(data.data)
                $("#user-message").html("归属部门："+data.data.orgName+"&nbsp;&nbsp;"+"主管："+data.data.userBossName);
                // layer.msg('操作成功', {
                //     icon: 1,
                //     time: 1000 //2秒关闭（如果不配置，默认是3秒）
                // }, function(){
                //     //do something
                //
                //     $("#search").submit();
                // });
                // debugger

                // layer.alert("操作成功失败");
            } else {
                layer.msg('操作失败', {
                    icon: 2,
                    time: 1000 //2秒关闭（如果不配置，默认是3秒）
                }, function(){
                    //do something
                    // $("#search").submit();
                });
            }

        },
        error:function(data){
            if (data.status == 1001) {
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
            }
        }
    });

    checkAllData();
}

function checkAllData(str){
    debugger
    var state=[];
    var citys = [];
    var province = [];
    var userId =  $("#userId option:selected").val();
    var publicCustomerRegionRulesId =  $("#publicCustomerRegionRulesId").val();
    if (userId == null || userId == -1 || userId == undefined) {
        $("#u-message").css("display","");
        $("#checkData").val(0);
        return false;
    }

    $('select[name="zone"] option:selected').each(function (){
        if ($(this).val()!=0) {
            state.push($(this).val());
        }
    })
    $('select[name="province"] option:selected').each(function (){
        if ($(this).val()!=0) {
            province.push($(this).val());
        }

    })
    $('select[name="city"] option:selected').each(function (){
        if ($(this).val()!=0) {
            citys.push($(this).val());
        }

    });
    var aa = true;
    $(state).each(function (i){
        if (state[i]==-1){
            $(citys).each(function (k) {
                let j = k+1;
                let a = citys[k];
                for (let x = j;x<citys.length;x++){
                    citys[x];
                    if (a == citys[x]&&a!=-1&&a!=0) {
                        $("#message").html("区域不可重复");
                        aa = false;
                    }
                }
            })
        }
    })

    $(citys).each(function (i){
        if (citys[i]==-1){
            $(province).each(function (k) {
                let j = k+1;
                let a = province[k];
                for (let x = j;x<province.length;x++){
                    province[x];
                    if (a == province[x]&&a!=-1&&a!=0) {
                        $("#message").html("区域不可重复");
                        aa = false;
                    }
                }
            })
        }
    })
    if (!aa) {
        return false;
    }
    if (aa) {
        $("#message").html("");
    }

    var lat = false;
    $(state).each(function (i){
        if (state[i]!=0&&state[i]!=null&&state[i]!=undefined){
            lat =true;
        }
    })
    // debugger
    $(citys).each(function (i){
        if (citys[i]==-1){
            lat =true;
        }
    })
    if (!lat) {
        $("#message").html("请选择区域");
        $("#checkData").val(0);
        return lat;
    }else {
        $("#message").html("");
    }

    var ll = true;
    if(state.length>1){
        $(state).each(function (i){
            let j = i+1;
            let a = state[i];
            for (let x = j;x<state.length;x++){
                state[x];
                if (a == state[x]&&a!=-1) {
                    $("#message").html("区域不可重复");
                    ll= false;
                }
            }
        })
    }
    if (!ll) {
        return false;
    }
    var flag=true;
    $(state).each(function (i){
        var regionId = state[i];// alert(regionId);
        // alert(1);
        $.ajax({
            type: "POST",
            url: "/trader/customer/doCheckData.do",
            data:  {"userId":userId,"publicCustomerRegionRulesId":publicCustomerRegionRulesId,"regionId":regionId},
            dataType:'json',
            async:false,
            success: function(data){
                if (data.code == 1) {
                    // alert(data.data)
                    $("#message").html(data.message);
                    flag=false;

                } else if (data.code == 2) {
                    $("#message").html(data.data.provinceName+"-"+data.data.cityName+"-"+data.data.zoneName+"已分配给"+data.data.username);
                    flag=false;

                }else if(data.code == 0){
                    // $("#message").html("")
                }else {
                    layer.alert(data.message);
                }

            },
            error:function(data){
                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
                    flag=false;
                }
            }
        });
        if(!flag){
            return flag;
        }
    })

    if (flag) {
        $("#message").html("")

    }
    // alert(flag);
    return flag;
}

function changeAreaMy(selectIdPrefix, areaId, regionType)
{
    console.log("select的id前缀: " + selectIdPrefix + ", regionType: " + regionType);
    debugger
    var regionId = 0;

    var nowSelectId = selectIdPrefix;
    // 重置地区最小地址ID
    $("#" + nowSelectId).val("0");

    // 地区
    // var area = $("#" + areaId).val();

    // 改变省
    if(1 == regionType)
    {
        regionId = $("#"+selectIdPrefix +"-province").val();

        var province = $("#"+selectIdPrefix +"-province").find("option:selected").text();
        // 设置省
        // $("#" + areaId).val(province);
        $("#"+selectIdPrefix +"-city").css("display","");
        // 清空
        // $("#" + selectIdPrefix + "-zone option:gt(0)").remove();
        // $("#" + selectIdPrefix + "-city option:gt(0)").remove();
        $option = "<option value='0'>请选择</option>";
        $("#" + selectIdPrefix+"-zone").html($option);
        $("#" + selectIdPrefix+"-city").html($option);


        // 随之改变市
        selectIdPrefix += '-city';

    }
    // 改变市
    else if(2 == regionType)
    {
        regionId = $("#"+selectIdPrefix +"-city").val();
        var city = $("#"+selectIdPrefix +"-city").find("option:selected").val();
        // 设置市
        // $("#" + areaId).val(area + " " + city);
        // debugger
        if (city!=-1) {
            $("#"+selectIdPrefix +"-zone").css("display","");
        }
        // $("#" + selectIdPrefix + "-zone option:gt(0)").remove();
        $option = "<option value='0'>请选择</option>";
        $("#" + selectIdPrefix+"-zone").html($option);

        // 随之改变区
        selectIdPrefix += '-zone';

    }
    // 改变区
    else if(3 == regionType)
    {
        regionId = $("#"+selectIdPrefix +"-zone").val();

        var zone = $("#"+selectIdPrefix +"-zone").find("option:selected").text();
        // 设置区
        // $("#" + areaId).val(area + " " + zone);

        // 随之改变区的值
        // $("#" + nowSelectId).val(regionId);
    }

    if (regionId == -1 && 2 == regionType) {
        $option = "<option value='-1' selected>全部</option>";
        $("#" + selectIdPrefix).html($option);
    }
    if(regionId > 0 && (1 == regionType || 2 == regionType))
    {
        $.ajax
        ({
            type : "POST",
            url : page_url+"/system/region/getregion.do",
            data :{'regionId':regionId},
            dataType : 'json',
            success : function(data) {
                $option = "<option value='0'>请选择</option>";
                $option += "<option value='-1'>全部</option>";
                $.each(data.listData,function(i,n){
                    $option += "<option value='"+data.listData[i]['regionId']+"'>"+data.listData[i]['regionName']+"</option>";
                });
                $("#" + selectIdPrefix).empty();
                $("#" + selectIdPrefix).html($option);
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
                }
            }
        });

    }

    checkAllData();

}
