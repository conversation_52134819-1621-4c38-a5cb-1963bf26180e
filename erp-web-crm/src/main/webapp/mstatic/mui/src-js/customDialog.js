Vue.component('ui-custom-dialog', {
    template: `
            <div class="mui-custom-dialog-container" v-show="isShow" @click="handlerMaskClick">
                <div class="nui-custom-dislog-center">
                    <div class="mui-custom-dialog-wrapper" :style="{'width': width}" v-show="isShow" @click.stop>
                        <div class="mui-custom-dialog-content">
                            <div class="mui-custom-dialog-title" v-if="title" v-html="title"></div>
                            <div class="mui-custom-dialog-msg" v-if="message" v-html="message"></div>
                            <slot></slot>
                        </div>

                        <div class="mui-custom-dialog-button-choice">
                            <slot name="footer">
                                <button 
                                    :class="['mui-cdb', leftBtn.class]" 
                                    class="mui-cdb"
                                    @click="clickLeftBtn"
                                >{{ leftBtn.txt }}</button>
                                <button 
                                    :class="['mui-cdb', rightBtn.class, rightBtnDisable ? 'disabled' : 'no-no']"
                                    @click="clickRightBtn"
                                >{{ rightBtn.txt }}</button>
                            </slot>
                        </div>
                    </div>
                </div>
            </div>
    `,
    props: {
        isShow: {
            type: Boolean,
            default: false
        },
        lock: {
            type: Boolean,
            default: false //点击遮罩是否关闭弹层
        },
        title: String,
        message: String,
        width: {
            type: String,
            default: '270px'
        },
        leftBtn: {
            type: Object,
            default: ()=> {
                return {
                    txt: '取消',
                    class: 'cancel'
                }
            }
        },
        rightBtn: {
            type: Object,
            default: ()=> {
                return {
                    txt: '保存',
                    class: 'confirm',
                }
            }
        },
        // 右侧按钮置灰
        rightBtnDisable: {
            type: Boolean,
            default: false,
        }
    },
    watch: {
        isShow(newV) {
            console.log(' isShow ===>', newV);
        },
        rightBtnDisable (newV) {
            console.log(' rightBtnDisable ===>', newV);
        }
    },
    data() {
        return {
            // isShow: false,
            type: '',
            title: '',
            message: '',
            buttons: [],
            defaultClass: '',  // 按钮默认class
            iconType: {
                'success': 'icon-yes2',
                'error': 'icon-error2',
                'warn': 'icon-caution2',
                'info': 'icon-info2'
            }
            // btnClass： disabled:置灰色  confirm:蓝色  delete:红色  cannel:默认色
        }
    },
    methods: {
        handlerMaskClick() {
            if(this.lock) {
                return;
            }
            this.hide();
        },
        hide() {
            this.$emit('update:isShow', false);
        },
        clickLeftBtn () {
            this.hide();
            this.$emit('handleLeftBtn');
        },
        clickRightBtn () {
            this.hide();
            this.$emit('handleRightBtn');
        }
    }
})
