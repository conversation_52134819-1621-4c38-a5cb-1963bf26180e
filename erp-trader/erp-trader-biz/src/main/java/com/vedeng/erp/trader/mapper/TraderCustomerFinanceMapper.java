package com.vedeng.erp.trader.mapper;

import com.vedeng.erp.trader.domain.entity.TraderCustomerFinance;

public interface TraderCustomerFinanceMapper {
    /**
     * delete by primary key
     * @param traderCustomerFinanceId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer traderCustomerFinanceId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(TraderCustomerFinance record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(TraderCustomerFinance record);

    /**
     * select by primary key
     * @param traderCustomerFinanceId primary key
     * @return object by primary key
     */
    TraderCustomerFinance selectByPrimaryKey(Integer traderCustomerFinanceId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(TraderCustomerFinance record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(TraderCustomerFinance record);
}