package com.vedeng.filedelivery.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.ArrayStack;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.kuaidi100.sdk.api.LabelV2;
import com.kuaidi100.sdk.contant.ApiInfoConstant;
import com.kuaidi100.sdk.contant.PrintType;
import com.kuaidi100.sdk.core.IBaseClient;
import com.kuaidi100.sdk.pojo.HttpResult;
import com.kuaidi100.sdk.request.ManInfo;
import com.kuaidi100.sdk.request.PrintReq;
import com.kuaidi100.sdk.request.labelV2.OrderReq;
import com.kuaidi100.sdk.utils.SignUtils;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.mapper.UserMapper;
import com.vedeng.erp.system.service.UserWorkApiService;
import com.vedeng.filedelivery.model.FileDeliveryApolloCommon;
import com.vedeng.filedelivery.service.LogisticsOrderService;
import com.vedeng.goods.api.utils.HttpClientUtil;
import com.vedeng.infrastructure.feign.uac.UacWxUserInfoApiService;
import com.vedeng.oa.dao.FileDeliveryAddressMapper;
import com.vedeng.oa.dao.FileDeliveryConfigMapper;
import com.vedeng.oa.dao.FileDeliveryNewMapper;
import com.vedeng.oa.service.filedelivery.api.FileDeliveryAddressApiService;
import com.vedeng.oa.service.filedelivery.api.FileDeliveryNewApiService;
import com.vedeng.oa.service.filedelivery.vo.FileDeliveryAddressVO;
import com.vedeng.oa.service.filedelivery.vo.FileDeliveryVO;
import com.vedeng.system.model.vo.AddressVo;
import com.vedeng.system.model.vo.ParamsConfigVo;
import com.vedeng.system.service.AddressService;
import com.vedeng.uac.api.dto.WxUserDto;

import lombok.Data;


/**
 * 面单下单服务
 * <AUTHOR>
 *
 */
@Service
public class LogisticsOrderServiceImpl implements LogisticsOrderService {
	
	public static Logger logger = LoggerFactory.getLogger(LogisticsOrderServiceImpl.class);
	
	@Resource
    FileDeliveryConfigMapper fileDeliveryConfigMapper;
	
    @Resource
    FileDeliveryNewMapper fileDeliveryNewMapper;
    
    @Resource
    FileDeliveryAddressMapper fileDeliveryAddressMapper;
    
    @Resource
    FileDeliveryAddressApiService fileDeliveryAddressApiService;
    
    @Autowired 
    AddressService addressService;
    
    @Autowired 
    private FileDeliveryNewApiService fileDeliveryNewApiService;
    
    @Autowired 
    private FileDeliveryApolloCommon fileDeliveryApolloCommon;
    
    @Autowired 
	private UserWorkApiService userWorkApiService;
    
    @Resource
    private RedissonClient redissonClient;
    
    @Autowired 
    private PrintCodeGenerator printCodeGenerator;
    
    @Autowired 
	private UacWxUserInfoApiService uacWxUserInfoApiService ;
    
    @Value("${redis_dbtype}")
	private String redisDbtype;
    
    @Autowired
    private UserMapper userMapper;
    
    /**
     * 审核通过，选择的部门为申请人所在部门时，进行面单下单操作，并存储到redis、发送企微通知到指定申请人
     * 请求系统来源 systemSource【ERP:"erp",打印应用："express"】
     * erp生成打印码，可能存在失败的情况，express应用使用打印码进行打印时，需要判断打印条数是否都满足，如果不满足
     * 需要重新生成打印链接，并更新表数据
     */
	@Override
	public List<String> submit(Integer fileDeliveryId,Integer userId,Integer printType,String systemSource,Integer invokeType) {
		logger.info("面单下单接口：入参：fileDeliveryId:{}，userId:{}，printType:{}，systemSource：{}",fileDeliveryId,userId,printType,systemSource);
		
		//获取文件寄送主表记录
		FileDeliveryVO fileDeliveryVO = fileDeliveryNewApiService.getFileDeliveryNew(fileDeliveryId);
		List<FileDeliveryAddressVO> fileDeliveryAddressVOList = fileDeliveryAddressApiService.getFileDeliveryAddress(fileDeliveryVO.getFileDeliveryId());
		if(CollectionUtils.isEmpty(fileDeliveryAddressVOList)) {
			logger.info("==fileDelivery== 发起面单下单操作，地址信息不存在，直接返回");
			return null;
		}
		
		//成功条数
		Integer successInt = 0;
		Integer totalInt = 0;
		Integer deliveryDept=0;
		StringBuilder sbSuffix = new StringBuilder();
		String prefixStr = "";
		try {
			List<Integer> userIdList = Arrays.asList(userId,fileDeliveryVO.getApplyUserId());
			List<UserDto> userDtoList = userMapper.getUserInfoByUserIds(userIdList);
			Map<Integer, UserDto> userDtoMap = userDtoList.stream().collect(Collectors.toMap(UserDto::getUserId, user -> user));
			prefixStr = "<font color=\"red\">物品寄送面单下单失败</font>，请相关同事注意。\n> 时间： <font color=\"comment\">"+DateUtil.DateToString(new Date(), "yyyy-mm-dd HH:mm:ss")+"</font>\n> 物品寄送单号：<font color=\"warning\">"+fileDeliveryVO.getFileDeliveryNo()+
					"</font>\n> 申请人：<font color=\"comment\">"+userDtoMap.get(fileDeliveryVO.getApplyUserId()).getUsername()+"</font>\n> 操作人：<font color=\"comment\">"+userDtoMap.get(userId).getUsername()+"</font>\n> 错误原因： ";
		}catch(Exception e) {
			logger.error("拼接群监控报错信息失败,不影响业务执行",e);
		}
		//存储redis对象
		PrintCodeRedisDTO printCodeRedisDTO = new PrintCodeRedisDTO();
		List<String> printUrlList = new ArrayList<String>();
		try {
			//寄送时间
			Date deliveryTime = null;
			//寄送状态
			Integer deliveryStatus = 1;
			//快递单号
			StringBuilder expressNos = new StringBuilder();
			//寄送类型
			if(Objects.isNull(printType)) {
				printType = 5;// VDERP-18025 getPrintType(fileDeliveryVO);
			}
			//根据printType来判断快递公司名称
			Integer logisticsType = getLogisticTypeByPrintType(printType);
			//执行下标
			Integer index = 1;
			//地址总数
			totalInt = fileDeliveryAddressVOList.size();
			logger.info("==fileDelivery== 发起面单下单操作，地址信息总数{}条",totalInt);
			for (FileDeliveryAddressVO fileDeliveryAddressVO : fileDeliveryAddressVOList) {
				//其他，更新快递公司为"其他"，不做下单打印，寄送状态更新为已寄送，收件列表快递单号默认空，可编辑
				//更新快递地址表
				FileDeliveryAddressVO update=new FileDeliveryAddressVO();
				if(printType==4) {
					//成功+1
					successInt++;
					update.setId(fileDeliveryAddressVO.getId());
					update.setUpdater(userId);
					//寄送状态 0未寄送 1已寄送 
					update.setDeliveryStatus(1);
					logger.info("更快递地址信息");
					fileDeliveryNewApiService.fillExpressNoAndLabelUrlById(update);
					if(index==1 && Objects.isNull(fileDeliveryVO.getDeliveryTime())) {
						deliveryTime = new Date();
					}
					index++;
					continue;
				}
				//如果已经有下单地址，则直接跳过
				if(StringUtils.isNotBlank(fileDeliveryAddressVO.getExpressLabelurl())) {
					successInt++;
					if(index==totalInt) {
						expressNos.append(fileDeliveryAddressVO.getLogisticsNo());
					}else {
						expressNos.append(fileDeliveryAddressVO.getLogisticsNo()+"| ");
					}
					String labelUrl = fileDeliveryAddressVO.getExpressLabelurl();
					List<String> labelUrlList = Arrays.asList(labelUrl.split(","));
					printUrlList.addAll(labelUrlList);
					index++;
					continue;
				}
				logger.info("==fileDelivery== 发起面单下单操作，遍历地址信息第{}条：{}",index,JSON.toJSONString(fileDeliveryAddressVO));
				PrintReq printReq = combimParams(printType, fileDeliveryAddressVO,fileDeliveryVO);
				IBaseClient baseClient = new LabelV2();
				baseClient.setTimeOut(5000, 5000);			
				HttpResult result = null;
				try {
					logger.info("==fileDelivery== 发起面单下单操作，组装接口请求信息：{}",JSON.toJSONString(printReq));
					if(fileDeliveryApolloCommon.canSubmitOrder) {
						logger.info("==fileDelivery== 发起面单下单操作,调用快递100入参printReq:{}",JSON.toJSONString(printReq));
						result = baseClient.execute(printReq);
					}else {
						result = new HttpResult();
						result.setStatus(-1);
						result.setError("面单下单收费，且测试环境无打印机，默认下单失败");
					}
					logger.info("==fileDelivery== 发起面单下单操作，接口请求返回值：{}",JSON.toJSONString(result));
				} catch (Exception e) {
					logger.error("==fileDelivery== 发起面单下单操作,调用快递100接口请求报错：入参：fileDeliveryId:{}，userId:{}，printType:{}，systemSource：{},errorMessage:",fileDeliveryId,userId,printType,systemSource,e);
					sbSuffix.append("<font color=\"warning\">收件单位["+fileDeliveryAddressVO.getTraderName()+"]的面单下单失败，原因：系统异常；</font>");
					continue;
				}
				if(Objects.isNull(result) || result.getStatus()!=200) {
					logger.error("==fileDelivery== 发起面单下单操作,调用快递100接口请求报错：入参：fileDeliveryId:{}，userId:{}，printType:{}，systemSource：{},errorMessage:{}",fileDeliveryId,userId,printType,systemSource,result.getError());
					sbSuffix.append("<font color=\"warning\">收件单位["+fileDeliveryAddressVO.getTraderName()+"]的面单下单失败，原因："+result.getError()+"；</font>");
					continue;
				}
				JSONObject body=(JSONObject)JSON.parseObject(result.getBody());
				if(body.getInteger("code")!=200) {
					logger.error("==fileDelivery== 发起面单下单操作,调用快递100接口请求报错：入参：fileDeliveryId:{}，userId:{}，printType:{}，systemSource：{},errorMessage:{}",fileDeliveryId,userId,printType,systemSource,body.getString("message"));
					sbSuffix.append("<font color=\"warning\">收件单位["+fileDeliveryAddressVO.getTraderName()+"]的面单下单失败，原因："+body.getString("message")+"；</font>");
					continue;
				}
				JSONObject data=(JSONObject)body.get("data");
				//成功+1
				successInt++;
				String orderNum = data.get("kuaidinum")+"";
				update.setLogisticsNo(orderNum);
				if(index==totalInt) {
					expressNos.append(orderNum);
				}else {
					expressNos.append(orderNum+"| ");
				}
				String orderUrl = ""+data.get("label");
				//如果存在多个
				List<String> urlList = new ArrayList<>();
				urlList = Arrays.asList(orderUrl.split(","));
				printUrlList.addAll(urlList);
				update.setExpressLabelurl(orderUrl);
				update.setId(fileDeliveryAddressVO.getId());
				update.setUpdater(userId);
				//寄送状态 0未寄送 1已寄送 
				update.setDeliveryStatus(1);
				logger.info("更快递地址信息");
				fileDeliveryNewApiService.fillExpressNoAndLabelUrlById(update);
				if(index==1 && Objects.isNull(fileDeliveryVO.getDeliveryTime())) {
					deliveryTime = new Date();
				}
				index++;
			}
			//更新寄送主表
			String expressNosToSave = expressNos.toString();
			if(expressNosToSave.endsWith(",")) {
				expressNosToSave.subSequence(0, expressNosToSave.length()-1);
			}
			if(successInt>0) {
				fileDeliveryNewApiService.updateFileDeliveryDeliveryStatus(fileDeliveryId,deliveryStatus,deliveryTime,expressNosToSave, userId,logisticsType);
			}
		}catch(Exception e) {
			if(successInt == 0) {
				logger.error("==fileDelivery== 发起面单下单操作,调用快递100接口请求报错：入参：fileDeliveryId:{}，userId:{}，printType:{}，systemSource：{},errorMessage:",fileDeliveryId,userId,printType,systemSource,e);
				sbSuffix.append("<font color=\"warning\">面单下单失败，原因：系统异常；</font>");
			}
			else {
				logger.error("==fileDelivery== 发起面单下单操作成功：{}条，共：{}条",successInt,totalInt,e);
			}
		}
		
		//如果选择的部门为申请人所在部门时，需要存4位码到redis，并在打印时进行设置过期时间，并且发送企业微信消息
		deliveryDept = fileDeliveryVO.getDeliveryDept();
		//部门为申请人所在部门、来源erp、调用来源为审核通过，执行发送打印码操作
		if(deliveryDept==3 && systemSource.equals("erp") && invokeType==1) {
			//无论成功失败，审核通过只会执行一次，打印码都会进行发送并保存reids
			String printCode = printCodeGenerator.generateUniqueCode();
			if(StringUtils.isEmpty(printCode)) {
				//出现此打印码说明打印码已耗尽
				printCode = "999999";
			}
			//由于审核已执行通过，此处无论发送成功失败，都需要通知到申请人 打印码
			printCodeRedisDTO.setPrintCode(printCode);
			printCodeRedisDTO.setFileDeliveryId(fileDeliveryId);
			printCodeRedisDTO.setAllOrdersubmit(successInt.equals(totalInt)?true:false);
			printCodeRedisDTO.setPrintUrlList(printUrlList);
			UserDto userDto = userMapper.getUserByUserId(fileDeliveryVO.getApplyUserId());
			if(Objects.nonNull(userDto) && StringUtils.isNotEmpty(userDto.getNumber())){
				printCodeRedisDTO.setApplyId(String.valueOf(userDto.getUserId()));
				printCodeRedisDTO.setApplyName(userDto.getUsername());
			}
			RBucket<String> fileIdAndCodeRBucket = redissonClient.getBucket(redisDbtype+"file_delivery:printCodeAndId:"+fileDeliveryId);
			fileIdAndCodeRBucket.set(printCode);
			RBucket<String> rBucket = redissonClient.getBucket(redisDbtype+"file_delivery:printCode:"+printCode);
			//存储redis数据
			String printCodeStr = JSON.toJSONString(printCodeRedisDTO);
			logger.info("==fileDelivery== 发起面单，打印码存储redis:{}",printCodeStr);
			rBucket.set(printCodeStr);
			if(systemSource.equals("erp") && fileDeliveryApolloCommon.sendQwMessage==1) {
				//发送企业微信消息给申请人
				String content = "物品寄送打印通知  \n --------------------------------------------- \n  "
						+ "您申请的物品寄送单：<font color=\"info\"> "+fileDeliveryVO.getFileDeliveryNo()+" </font> "
						+ "已审核完成， 请凭打印码 至 10楼西文印区打印面单 \n \n ## "+printCode+" ## \n ";
				try {
					if(Objects.nonNull(userDto) && Objects.nonNull(userDto.getUserId())){
						userWorkApiService.sendInvoiceMsg(userDto.getUserId() ,content);
					}
				}catch(Exception e) {
					//企微通知失败，发送站内信
					Map<String,String> map=new HashMap<>();
			        map.put("printCode",printCode);
			        String[] startUser=new String[]{"admin"};
			        List<Integer> userIds = Arrays.asList(fileDeliveryVO.getApplyUserId());
			        String url = "./logistics/filedeliveryNew/getFileDeliveryDetail.do?fileDeliveryId=" + fileDeliveryId;
					MessageUtil.sendMessage(322, userIds, map, url, startUser);
				}
			}
		}
		logger.info("==fileDelivery== 发起面单下单完成，执行成功：{}条,共：{}条",successInt,totalInt);
		
		//有错误信息，发送企微监控群
		if(StringUtils.isNotBlank(sbSuffix.toString())) {
			try {
				sendQwMessage(prefixStr,sbSuffix.toString());
			}catch(Exception e) {
				logger.error("发送群监控报错信息失败,不影响业务执行",e);
			}
		}
		
		if(successInt.equals(successInt)) {
			return printUrlList;
		}else {
			return null;
		}
	}
	
	private void sendQwMessage(String prefixStr, String sbSuffix) {
		Map<String,Object> params = new HashMap<>();
		params.put("msgtype", "markdown");
		Map<String,Object> contentMap = new HashMap<>();
		contentMap.put("content", prefixStr+sbSuffix);
		params.put("markdown", contentMap);
		HttpClientUtil.post(fileDeliveryApolloCommon.qwMessageUrl,params);
	}


	private Integer getLogisticTypeByPrintType(Integer printType) {
		//默认其他
		Integer logisticsType = 4;
		if(printType==1) {
			logisticsType = 1;
		}else if(printType==2) {
			logisticsType = 2;
		}else if(printType==3) {
			logisticsType = 3;
		}else if(printType==5) {
			logisticsType = 5;
		}
		return logisticsType;
	}

	
	/**
	 * 根据重量判断快递公司
	 * @param fileDeliveryVO
	 * @return
	 */
	private Integer getPrintType(FileDeliveryVO fileDeliveryVO) {
		//判断寄件重量
		BigDecimal deliveryTotalWeight =  fileDeliveryVO.getDeliveryTotalWeight();
		BigDecimal minddleWeight = new BigDecimal("5");
		//寄件中重量 >= 5KG：快递公司为 "邮政电商标快"
		//寄件总重量 < 5KG：快递公司为 "邮政EMS"
		Integer printType = 0;
		if(deliveryTotalWeight.compareTo(minddleWeight)>=0) {
			printType = 2;
		}else {
			printType = 1;
		}
		return printType;
	}

	
	/**
	 * 组装下单参数信息
	 * @param printType
	 * @param fileDeliveryAddressVO
	 * @param fileDeliveryVO 
	 * @return
	 */
	private PrintReq combimParams(Integer printType,FileDeliveryAddressVO fileDeliveryAddressVO, FileDeliveryVO fileDeliveryVO) {
		ManInfo recManInfo  = new ManInfo();
		recManInfo.setName(fileDeliveryAddressVO.getTraderContactName());
		recManInfo.setMobile(fileDeliveryAddressVO.getTraderContactMobile());
		recManInfo.setPrintAddr(fileDeliveryAddressVO.getTraderContactAddress());
		recManInfo.setCompany(fileDeliveryAddressVO.getTraderName());
		logger.info("==fileDelivery== 发起面单下单操作，组装收件人信息：{}",JSON.toJSONString(recManInfo));
		ManInfo sendManInfo = new ManInfo();
		ParamsConfigVo paramsConfigVo = new ParamsConfigVo();
		paramsConfigVo.setCompanyId(1);
		paramsConfigVo.setParamsKey(100);
		AddressVo delivery = addressService.getDeliveryAddress(paramsConfigVo);
		sendManInfo.setPrintAddr(delivery.getAreas()+delivery.getAddress());
		if(Objects.nonNull(fileDeliveryVO.getSendUserType()) && fileDeliveryVO.getSendUserType()==2) {
			try {
				RestfulResult<WxUserDto> userDtoResult = uacWxUserInfoApiService.getByAccountId(fileDeliveryVO.getApplyUserId());
				logger.info("用户信息返回：{}",JSON.toJSONString(userDtoResult));
				if(userDtoResult.isSuccess() && Objects.nonNull(userDtoResult.getData())) {
					WxUserDto wxUserDto = userDtoResult.getData();
					sendManInfo.setName(wxUserDto.getRealName());
					sendManInfo.setTel(wxUserDto.getMobileNo());
				}
			}catch(Exception e) {
				UserDto userDto = userMapper.getUserByUserId(fileDeliveryVO.getApplyUserId());
				sendManInfo.setName(userDto.getUsername());
				sendManInfo.setTel(userDto.getMobile());
				logger.error("调用uac失败",e);
			}
		}else {
			sendManInfo.setCompany(delivery.getCompanyName());
			sendManInfo.setName(fileDeliveryApolloCommon.fileDeliverySendName);
			sendManInfo.setTel(fileDeliveryApolloCommon.fileDeliverySendMobile);
		}
		logger.info("==fileDelivery== 发起面单下单操作，组装寄件人信息：{}",JSON.toJSONString(sendManInfo));
		OrderReq orderReq = new OrderReq();
		//TODO 如果已寄送 ，则始终使用 主表的快递类别
		orderReq.setWeight(fileDeliveryVO.getDeliveryTotalWeight().doubleValue());
		orderReq.setCargo(fileDeliveryVO.getDeliveryProdName());
		int count = fileDeliveryVO.getDeliveryExpressNum();
		switch (printType)
		{
			case 1://ems
				orderReq.setKuaidicom(fileDeliveryApolloCommon.fileDeliveryEmsCom);
				orderReq.setPartnerId(fileDeliveryApolloCommon.fileDeliveryEmsId);
				orderReq.setPartnerKey(fileDeliveryApolloCommon.fileDeliveryEmsKey);
				orderReq.setTempId(fileDeliveryApolloCommon.fileDeliveryEmsTempId);
				if(count>1) {
					orderReq.setChildTempId(fileDeliveryApolloCommon.fileDeliveryEmsTempId);
				}
				break;
			case 2://emsdsbk
				orderReq.setKuaidicom(fileDeliveryApolloCommon.fileDeliveryEmsdsbkCom);
				orderReq.setPartnerId(fileDeliveryApolloCommon.fileDeliveryEmsdsbkId);
				orderReq.setPartnerKey(fileDeliveryApolloCommon.fileDeliveryEmsdsbkKey);
				orderReq.setTempId(fileDeliveryApolloCommon.fileDeliveryEmsdsbkTempId);
				if(count>1) {
					orderReq.setChildTempId(fileDeliveryApolloCommon.fileDeliveryEmsdsbkTempId);
				}
				break;
			case 3://sf
				orderReq.setKuaidicom(fileDeliveryApolloCommon.fileDeliverySfCom);
				orderReq.setPartnerId(fileDeliveryApolloCommon.fileDeliverySfId);
				orderReq.setPartnerKey(fileDeliveryApolloCommon.fileDeliverySfKey);
			    orderReq.setPartnerSecret(fileDeliveryApolloCommon.fileDeliverySfSecret);
				orderReq.setTempId(fileDeliveryApolloCommon.fileDeliverySfTempId);
				if(count>1) {
					orderReq.setChildTempId(fileDeliveryApolloCommon.fileDeliverySfChildTempId);
				}
				orderReq.setCode("sf_secret"); 
				break;
			case 5://zhongtong
				orderReq.setKuaidicom(fileDeliveryApolloCommon.fileDeliveryZtCom);
				orderReq.setPartnerId(fileDeliveryApolloCommon.fileDeliveryZtId);
				orderReq.setPartnerKey(fileDeliveryApolloCommon.fileDeliveryZtKey);
				orderReq.setTempId(fileDeliveryApolloCommon.fileDeliveryZtTempId);
				if(count>1) {
					orderReq.setChildTempId(fileDeliveryApolloCommon.fileDeliveryZtTempId);
				}
				break;
		}
		orderReq.setCount(count);
		if(count>1) {
			orderReq.setNeedChild("1");
		}
		orderReq.setSendMan(sendManInfo);
		orderReq.setRecMan(recManInfo);
		orderReq.setPrintType(PrintType.HTML);
		String param = new Gson().toJson(orderReq);
		String t = System.currentTimeMillis() + "";
		PrintReq printReq = new PrintReq();
		printReq.setT(t);
		printReq.setKey(fileDeliveryApolloCommon.fileDeliveryKd100Key);
		printReq.setSign(SignUtils.printSign(param,t,fileDeliveryApolloCommon.fileDeliveryKd100Key,fileDeliveryApolloCommon.fileDeliveryKd100Secret));
		printReq.setMethod(ApiInfoConstant.ORDER);
		printReq.setParam(param);
		return printReq;
	} 
	
	@Data
	public static class PrintCodeRedisDTO{
		
		/**申请人企微ID*/
		private String applyId;
		
		/**申请人名称*/
		private String applyName;
		
		/**文件寄送主键*/
		private Integer fileDeliveryId;
		
		/**文件打印码*/
		private String printCode;
		
		/**面单下单url地址列表*/
		private List<String> printUrlList;
		
		/**是否全部下单成功*/
		private boolean allOrdersubmit;
	}

}
