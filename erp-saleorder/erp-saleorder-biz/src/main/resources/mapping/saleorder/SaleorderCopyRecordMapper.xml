<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.saleorder.dao.SaleorderCopyRecordMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.saleorder.domain.entity.SaleorderCopyRecordEntity">
    <!--@mbg.generated-->
    <!--@Table T_SALEORDER_COPY_RECORD-->
    <id column="SALEORDER_COPY_RECORD_ID" jdbcType="BIGINT" property="saleorderCopyRecordId" />
    <result column="SALEORDER_ID" jdbcType="INTEGER" property="saleorderId" />
    <result column="FROM_SALEORDER_ID" jdbcType="INTEGER" property="fromSaleorderId" />
    <result column="CHANNEL" jdbcType="INTEGER" property="channel" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SALEORDER_COPY_RECORD_ID, SALEORDER_ID, FROM_SALEORDER_ID, CHANNEL, IS_DELETE, ADD_TIME,
    MOD_TIME, CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, UPDATE_REMARK
  </sql>
  <insert id="insertSelective" keyColumn="SALEORDER_COPY_RECORD_ID" keyProperty="saleorderCopyRecordId" parameterType="com.vedeng.erp.saleorder.domain.entity.SaleorderCopyRecordEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SALEORDER_COPY_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="saleorderId != null">
        SALEORDER_ID,
      </if>
      <if test="fromSaleorderId != null">
        FROM_SALEORDER_ID,
      </if>
      <if test="channel != null">
        CHANNEL,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="saleorderId != null">
        #{saleorderId,jdbcType=INTEGER},
      </if>
      <if test="fromSaleorderId != null">
        #{fromSaleorderId,jdbcType=INTEGER},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>
