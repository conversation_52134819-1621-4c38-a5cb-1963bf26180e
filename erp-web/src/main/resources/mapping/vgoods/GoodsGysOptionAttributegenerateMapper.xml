<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.dao.GoodsGysOptionAttributegenerateMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.model.GoodsGysOptionAttributegenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <id column="GOODS_SYS_OPTION_ATTRIBUTE_ID" jdbcType="INTEGER" property="goodsSysOptionAttributeId" />
    <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId" />
    <result column="ATTRIBUTE_TYPE" jdbcType="INTEGER" property="attributeType" />
    <result column="ATTRIBUTE_ID" jdbcType="INTEGER" property="attributeId" />
    <result column="ATTRIBUTE_OTHER" jdbcType="VARCHAR" property="attributeOther" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    GOODS_SYS_OPTION_ATTRIBUTE_ID, GOODS_ID, ATTRIBUTE_TYPE, ATTRIBUTE_ID, ATTRIBUTE_OTHER
  </sql>
  <select id="selectByExample" parameterType="com.vedeng.goods.model.GoodsGysOptionAttributegenerateExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_GOODS_SYS_OPTION_ATTRIBUTE
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from T_GOODS_SYS_OPTION_ATTRIBUTE
    where GOODS_SYS_OPTION_ATTRIBUTE_ID = #{goodsSysOptionAttributeId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    delete from T_GOODS_SYS_OPTION_ATTRIBUTE
    where GOODS_SYS_OPTION_ATTRIBUTE_ID = #{goodsSysOptionAttributeId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.goods.model.GoodsGysOptionAttributegenerateExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    delete from T_GOODS_SYS_OPTION_ATTRIBUTE
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.goods.model.GoodsGysOptionAttributegenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <selectKey keyProperty="goodsSysOptionAttributeId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_GOODS_SYS_OPTION_ATTRIBUTE (GOODS_ID, ATTRIBUTE_TYPE, ATTRIBUTE_ID, 
      ATTRIBUTE_OTHER)
    values (#{goodsId,jdbcType=INTEGER}, #{attributeType,jdbcType=INTEGER}, #{attributeId,jdbcType=INTEGER}, 
      #{attributeOther,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.goods.model.GoodsGysOptionAttributegenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <selectKey keyProperty="goodsSysOptionAttributeId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_GOODS_SYS_OPTION_ATTRIBUTE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="goodsId != null">
        GOODS_ID,
      </if>
      <if test="attributeType != null">
        ATTRIBUTE_TYPE,
      </if>
      <if test="attributeId != null">
        ATTRIBUTE_ID,
      </if>
      <if test="attributeOther != null">
        ATTRIBUTE_OTHER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="goodsId != null">
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="attributeType != null">
        #{attributeType,jdbcType=INTEGER},
      </if>
      <if test="attributeId != null">
        #{attributeId,jdbcType=INTEGER},
      </if>
      <if test="attributeOther != null">
        #{attributeOther,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.goods.model.GoodsGysOptionAttributegenerateExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    select count(*) from T_GOODS_SYS_OPTION_ATTRIBUTE
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update T_GOODS_SYS_OPTION_ATTRIBUTE
    <set>
      <if test="record.goodsSysOptionAttributeId != null">
        GOODS_SYS_OPTION_ATTRIBUTE_ID = #{record.goodsSysOptionAttributeId,jdbcType=INTEGER},
      </if>
      <if test="record.goodsId != null">
        GOODS_ID = #{record.goodsId,jdbcType=INTEGER},
      </if>
      <if test="record.attributeType != null">
        ATTRIBUTE_TYPE = #{record.attributeType,jdbcType=INTEGER},
      </if>
      <if test="record.attributeId != null">
        ATTRIBUTE_ID = #{record.attributeId,jdbcType=INTEGER},
      </if>
      <if test="record.attributeOther != null">
        ATTRIBUTE_OTHER = #{record.attributeOther,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update T_GOODS_SYS_OPTION_ATTRIBUTE
    set GOODS_SYS_OPTION_ATTRIBUTE_ID = #{record.goodsSysOptionAttributeId,jdbcType=INTEGER},
      GOODS_ID = #{record.goodsId,jdbcType=INTEGER},
      ATTRIBUTE_TYPE = #{record.attributeType,jdbcType=INTEGER},
      ATTRIBUTE_ID = #{record.attributeId,jdbcType=INTEGER},
      ATTRIBUTE_OTHER = #{record.attributeOther,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.model.GoodsGysOptionAttributegenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update T_GOODS_SYS_OPTION_ATTRIBUTE
    <set>
      <if test="goodsId != null">
        GOODS_ID = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="attributeType != null">
        ATTRIBUTE_TYPE = #{attributeType,jdbcType=INTEGER},
      </if>
      <if test="attributeId != null">
        ATTRIBUTE_ID = #{attributeId,jdbcType=INTEGER},
      </if>
      <if test="attributeOther != null">
        ATTRIBUTE_OTHER = #{attributeOther,jdbcType=VARCHAR},
      </if>
    </set>
    where GOODS_SYS_OPTION_ATTRIBUTE_ID = #{goodsSysOptionAttributeId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.model.GoodsGysOptionAttributegenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update T_GOODS_SYS_OPTION_ATTRIBUTE
    set GOODS_ID = #{goodsId,jdbcType=INTEGER},
      ATTRIBUTE_TYPE = #{attributeType,jdbcType=INTEGER},
      ATTRIBUTE_ID = #{attributeId,jdbcType=INTEGER},
      ATTRIBUTE_OTHER = #{attributeOther,jdbcType=VARCHAR}
    where GOODS_SYS_OPTION_ATTRIBUTE_ID = #{goodsSysOptionAttributeId,jdbcType=INTEGER}
  </update>
</mapper>