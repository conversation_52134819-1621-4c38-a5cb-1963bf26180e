package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.BatchBuyorderExpenseItemDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDetailDtoMapper;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.dto.result.KingDeePayExpensesQueryResultDto;
import com.vedeng.erp.kingdee.service.KingDeePayExpensesApiService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 21-22 采购非用兼容
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchPurchaseInvoiceExpenseProcessor implements ItemProcessor<BatchInvoiceDto, BatchPayExpensesDto> {

    public static final String SPECIAL_INVOICE = "专用发票";
    public static final String TYPE_NAME = "运费";;
    private static final String TYPE_008 = "008";
    private static final String TYPE_021 = "021";

    @Autowired
    private BatchInvoiceDetailDtoMapper batchInvoiceDetailDtoMapper;


    @Autowired
    private KingDeePayExpensesApiService kingDeePayExpensesApiService;

    @Autowired
    private BatchBuyorderExpenseItemDtoMapper batchBuyorderExpenseItemDtoMapper;

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Override
    public BatchPayExpensesDto process(BatchInvoiceDto batchInvoiceDto)throws Exception {
        // 应付单对象

        log.info("处理采购费用单 票{}", JSON.toJSONString(batchInvoiceDto));
        boolean special;
        if (StrUtil.isNotEmpty(batchInvoiceDto.getInvoiceTypeName()) && batchInvoiceDto.getInvoiceTypeName().contains(SPECIAL_INVOICE)) {
            special = true;
            // 过滤未认证的专票
            if (!batchInvoiceDto.getIsAuth().equals(1)) {
                return null;
            }
        } else {
            special = false;
        }

        List<BatchInvoiceDetailDto> byInvoiceId = batchInvoiceDto.getBatchInvoiceDetailDtoList();
        List<BuyOrderExpenseExcelDto> excelDtoList = batchInvoiceDto.getExcelDtoList();
        if (CollUtil.isEmpty(excelDtoList)) {
            log.warn("无表格采购费用明细单,{}", excelDtoList);
            return null;
        }
        excelDtoList.forEach(x->{
            String s = batchBuyorderExpenseItemDtoMapper.selectUnitKingDeeNo(x.getSku());
            if (StrUtil.isEmpty(s)) {
                if (x.getType().equals(TYPE_NAME)) {
                    s = TYPE_008;
                } else {
                    s = TYPE_021;
                }
            }
            x.setUnitKingDeeNo(s);
        });

        List<KingDeePayExpensesQueryResultDto> kingDeePayExpenses = kingDeePayExpensesApiService.getKingDeePayExpenses(batchInvoiceDto.getInvoiceId());
        if (CollUtil.isEmpty(kingDeePayExpenses)) {
            log.warn("采购应付单未在金蝶查到数据{}",batchInvoiceDto.getInvoiceId());
            return null;
        }


        DecimalFormat decimalFormat = new DecimalFormat("0.00#");
        String taxRate = Objects.isNull(batchInvoiceDto.getRatio()) ? "0.00" : special ? decimalFormat.format(batchInvoiceDto.getRatio().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)) : "0.00";
        InPutFeeSpecialInvoiceDto inPutFeeSpecialInvoiceDto = null;
        InPutFeePlainInvoiceDto inPutFeePlainInvoiceDto = null;

        if (special) {
            //专用发票对象
            inPutFeeSpecialInvoiceDto = new InPutFeeSpecialInvoiceDto();
            inPutFeeSpecialInvoiceDto.setFQzokBddjtid(batchInvoiceDto.getInvoiceId().toString());
            Boolean exist = kingDeeBaseApi.isExist(inPutFeeSpecialInvoiceDto);
            if (exist) {
                return null;
            }
            inPutFeeSpecialInvoiceDto.setFid("0");
            inPutFeeSpecialInvoiceDto.setFdate(DateUtil.formatDateTime(DateUtil.date(batchInvoiceDto.getValidTime())));
            inPutFeeSpecialInvoiceDto.setFinvoicedate(DateUtil.formatDateTime(DateUtil.date(batchInvoiceDto.getValidTime())));
            inPutFeeSpecialInvoiceDto.setFinvoiceno(batchInvoiceDto.getInvoiceNo());
            inPutFeeSpecialInvoiceDto.setFsupplierid(batchInvoiceDto.getTraderSupplierId().toString());
            inPutFeeSpecialInvoiceDto.setFRedBlue("0");
            inPutFeeSpecialInvoiceDto.setFQzokFpdm(batchInvoiceDto.getInvoiceCode());
        }

        if (!special) {
            //普通蓝字发票对象
            inPutFeePlainInvoiceDto = new InPutFeePlainInvoiceDto();
            inPutFeePlainInvoiceDto.setFQzokBddjtid(batchInvoiceDto.getInvoiceId().toString());
            Boolean exist = kingDeeBaseApi.isExist(inPutFeePlainInvoiceDto);
            if (exist) {
                return null;
            }
            inPutFeePlainInvoiceDto.setFid("0");
            inPutFeePlainInvoiceDto.setFdate(DateUtil.formatDateTime(DateUtil.date(batchInvoiceDto.getValidTime())));
            inPutFeePlainInvoiceDto.setFinvoicedate(DateUtil.formatDateTime(DateUtil.date(batchInvoiceDto.getValidTime())));
            inPutFeePlainInvoiceDto.setFinvoiceno(batchInvoiceDto.getInvoiceNo());
            inPutFeePlainInvoiceDto.setFsupplierid(batchInvoiceDto.getTraderSupplierId().toString());
            inPutFeePlainInvoiceDto.setFRedBlue("0");
            inPutFeePlainInvoiceDto.setFQzokFpdm(batchInvoiceDto.getInvoiceCode());
        }

        boolean finalSpecial = special;
        InPutFeeSpecialInvoiceDto finalInPutFeeSpecialInvoiceDto = inPutFeeSpecialInvoiceDto;
        InPutFeePlainInvoiceDto finalInPutFeePlainInvoiceDto = inPutFeePlainInvoiceDto;
        batchInvoiceDto.getExcelDtoList().forEach(x -> {

            // 总金额
            if (finalSpecial) {
                this.buildSpecialInvoice( finalInPutFeeSpecialInvoiceDto, x,taxRate,kingDeePayExpenses,byInvoiceId);
            }
            if (!finalSpecial) {
                this.buildPlainInvoice( finalInPutFeePlainInvoiceDto, x,taxRate,kingDeePayExpenses,byInvoiceId);
            }
        });

        return BatchPayExpensesDto.builder()
                .inPutFeePlainInvoiceDto(inPutFeePlainInvoiceDto)
                .inPutFeeSpecialInvoiceDto(inPutFeeSpecialInvoiceDto)
                .build();
    }

    private void buildPlainInvoice(InPutFeePlainInvoiceDto inPutFeePlainInvoiceDto,
                                   BuyOrderExpenseExcelDto detailDto,String taxRate,List<KingDeePayExpensesQueryResultDto> kingDeePayExpenses,List<BatchInvoiceDetailDto> batchInvoiceDetailDtoList) {

        Optional<BatchInvoiceDetailDto> first = batchInvoiceDetailDtoList.stream().filter(a -> a.getSku().equals(detailDto.getSku())).findFirst();
        // 发票明细
        InPutFeePlainInvoiceDetailDto inPutFeePlainInvoiceDetailDto = new InPutFeePlainInvoiceDetailDto();
        inPutFeePlainInvoiceDetailDto.setFsourcetype("AP_Payable");
        inPutFeePlainInvoiceDetailDto.setFexpenseid(detailDto.getUnitKingDeeNo());
        inPutFeePlainInvoiceDetailDto.setFQty(first.isPresent()?first.get().getNum().toString():detailDto.getNum().toString());
        inPutFeePlainInvoiceDetailDto.setFunitprice(first.isPresent()?first.get().getPrice() : detailDto.getPrice());
        inPutFeePlainInvoiceDetailDto.setFtaxrate(taxRate);
        List<BatchInvoiceDetailDto> bySkus = batchInvoiceDetailDtoList.stream().filter(x -> x.getSku().equals(detailDto.getSku())).collect(Collectors.toList());
        String detailIds = bySkus.stream().map(BatchInvoiceDetailDto::getInvoiceDetailId).sorted().map(String::valueOf).collect(Collectors.joining(StrUtil.DASHED));
        inPutFeePlainInvoiceDetailDto.setFQzokBddjhid(detailIds);
        inPutFeePlainInvoiceDetailDto.setFQzokBdsku(detailDto.getSku());
        inPutFeePlainInvoiceDetailDto.setFQzokYwlx("采购费用");
        // 蓝票场景 1.采购费用单  2.售后手续费业务费用单
        inPutFeePlainInvoiceDetailDto.setFQzokGsywdh(detailDto.getBuyOrderNo());
        //采购费用单：该费用单关联的采购订单号，无关联则取费用单号
        //售后手续费业务费用单：该费用单关联的 采购售后单 的父级采购单号
        String sourceOrderNo =  detailDto.getBuyOrderNo();
        inPutFeePlainInvoiceDetailDto.setFQzokYsddh(sourceOrderNo);
        inPutFeePlainInvoiceDto.getFPUREXPINVENTRY().add(inPutFeePlainInvoiceDetailDto);
        List<KingDeePayExpensesQueryResultDto> collect =  kingDeePayExpenses.stream().filter(x ->StrUtil.split(x.getF_QZOK_BDDJHID(),StrUtil.DASHED).get(0).equals(bySkus.get(0).getInvoiceDetailId().toString())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            collect.forEach(x -> {
                String fPriceQty = x.getFPriceQty();
                BigDecimal fTaxPrice = x.getFTaxPrice();
                BigDecimal thisTotalAmount = fTaxPrice.multiply(new BigDecimal(fPriceQty)).setScale(2, RoundingMode.HALF_UP);
                // 发票来源订单信息
                InPutFeePlainInvoiceDetailLinkDto inPutFeePlainInvoiceDetailLinkDto = new InPutFeePlainInvoiceDetailLinkDto();
                inPutFeePlainInvoiceDetailLinkDto.setFLinkId("0");
                inPutFeePlainInvoiceDetailLinkDto.setFpurexpinventryLinkFsbillid(x.getFID());
                inPutFeePlainInvoiceDetailLinkDto.setFpurexpinventryLinkFsid(x.getFEntityDetail_FEntryId());
                inPutFeePlainInvoiceDetailLinkDto.setFpurexpinventryLinkFstableid("0");
                inPutFeePlainInvoiceDetailLinkDto.setFpurexpinventryLinkFruleid("IV_PayableToPUREXVATIN");
                inPutFeePlainInvoiceDetailLinkDto.setFpurexpinventryLinkFstablename("T_AP_PAYABLEENTRY");
                inPutFeePlainInvoiceDetailLinkDto.setFpurexpinventryLinkFamountforDold(thisTotalAmount.toString());
                inPutFeePlainInvoiceDetailLinkDto.setFpurexpinventryLinkFamountforD(thisTotalAmount.toString());
                inPutFeePlainInvoiceDetailDto.getFPUREXPINVENTRY_Link().add(inPutFeePlainInvoiceDetailLinkDto);
            });

        }

        log.info("普票信息:{}", JSON.toJSONString(inPutFeePlainInvoiceDetailDto));
    }

    private void buildSpecialInvoice(InPutFeeSpecialInvoiceDto inPutFeeSpecialInvoiceDto,
                                     BuyOrderExpenseExcelDto detailDto, String taxRate,List<KingDeePayExpensesQueryResultDto> kingDeePayExpenses,List<BatchInvoiceDetailDto> batchInvoiceDetailDtoList) {
        // 发票明细
        Optional<BatchInvoiceDetailDto> first = batchInvoiceDetailDtoList.stream().filter(a -> a.getSku().equals(detailDto.getSku())).findFirst();
        InPutFeeSpecialInvoiceDetailDto inPutFeeSpecialInvoiceDetailDto = new InPutFeeSpecialInvoiceDetailDto();
        inPutFeeSpecialInvoiceDetailDto.setFsourcetype("AP_Payable");
        inPutFeeSpecialInvoiceDetailDto.setFexpenseid(detailDto.getUnitKingDeeNo());
        inPutFeeSpecialInvoiceDetailDto.setFQty(first.isPresent()?first.get().getNum().toString():detailDto.getNum().toString());
        inPutFeeSpecialInvoiceDetailDto.setFunitprice(first.isPresent()?first.get().getPrice() : detailDto.getPrice());
        inPutFeeSpecialInvoiceDetailDto.setFtaxrate(taxRate);
        List<BatchInvoiceDetailDto> bySkus = batchInvoiceDetailDtoList.stream().filter(x -> x.getSku().equals(detailDto.getSku())).collect(Collectors.toList());
        String detailIds = bySkus.stream().map(BatchInvoiceDetailDto::getInvoiceDetailId).sorted().map(String::valueOf).collect(Collectors.joining(StrUtil.DASHED));
        inPutFeeSpecialInvoiceDetailDto.setFQzokBddjhid(detailIds);
        inPutFeeSpecialInvoiceDetailDto.setFQzokBdsku(detailDto.getSku());
        inPutFeeSpecialInvoiceDetailDto.setFQzokYwlx("采购费用");

        // 蓝票场景 1.采购费用单  2.售后手续费业务费用单
        inPutFeeSpecialInvoiceDetailDto.setFQzokGsywdh(detailDto.getBuyOrderNo());
        String sourceOrderNo =detailDto.getBuyOrderNo();
        inPutFeeSpecialInvoiceDetailDto.setFQzokYsddh(sourceOrderNo);
        inPutFeeSpecialInvoiceDto.getFPUREXPINVENTRY().add(inPutFeeSpecialInvoiceDetailDto);
        List<KingDeePayExpensesQueryResultDto> collect = kingDeePayExpenses.stream().filter(x ->StrUtil.split(x.getF_QZOK_BDDJHID(),StrUtil.DASHED).get(0).equals(bySkus.get(0).getInvoiceDetailId().toString())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            collect.forEach(x->{
                String fPriceQty = x.getFPriceQty();
                BigDecimal fTaxPrice = x.getFTaxPrice();
                BigDecimal thisTotalAmount = fTaxPrice.multiply(new BigDecimal(fPriceQty)).setScale(2, RoundingMode.HALF_UP);
                // 发票来源订单信息
                InPutFeeSpecialInvoiceDetailLinkDto inPutFeeSpecialInvoiceDetailLinkDto = new InPutFeeSpecialInvoiceDetailLinkDto();
                inPutFeeSpecialInvoiceDetailLinkDto.setFLinkId("0");
                inPutFeeSpecialInvoiceDetailLinkDto.setFpurexpinventryLinkFsbillid(x.getFID());
                inPutFeeSpecialInvoiceDetailLinkDto.setFpurexpinventryLinkFsid(x.getFEntityDetail_FEntryId());
                inPutFeeSpecialInvoiceDetailLinkDto.setFpurexpinventryLinkFstableid("0");
                inPutFeeSpecialInvoiceDetailLinkDto.setFpurexpinventryLinkFruleid("IV_PayableToPUREXVATIN");
                inPutFeeSpecialInvoiceDetailLinkDto.setFpurexpinventryLinkFstablename("T_AP_PAYABLEENTRY");
                inPutFeeSpecialInvoiceDetailLinkDto.setFpurexpinventryLinkFamountforDold(thisTotalAmount.toString());
                inPutFeeSpecialInvoiceDetailLinkDto.setFpurexpinventryLinkFamountforD(thisTotalAmount.toString());
                inPutFeeSpecialInvoiceDetailDto.getFPUREXPINVENTRY_Link().add(inPutFeeSpecialInvoiceDetailLinkDto);
            });

        }

        log.info("专票信息:{}", JSON.toJSONString(inPutFeeSpecialInvoiceDetailDto));
    }




}
