package com.vedeng.flash.service.message;

import com.vedeng.flash.dto.SendMessageDto;
import com.vedeng.flash.model.EarlyWarningTask;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.model.BuyorderGoods;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 催票任务创建时候的消息发送器
 */
@Service
public class ExpeditingTicketsCreateMessageSender extends AbstractMessageSender{

    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Override
    protected String getUrl(SendMessageDto sendMessageDto) {
        return "./flash/earlyWarningTicksTask/earlyWarningTicksTask.do";
    }

    @Override
    protected List<Integer> getMessageUserIdList(SendMessageDto sendMessageDto) {
        List<Integer> messageUserId = new ArrayList<>();
        messageUserId.add(Integer.valueOf(sendMessageDto.getEarlyWarningTask().getTaskDealer().split(",")[0]));
        return messageUserId;
    }

    @Override
    protected Integer getMessageTemplateId() {
        //催票任务生成消息在message_template表里的ID
        return 175;
    }

    @Override
    protected Map getTemplateVariableMap(SendMessageDto sendMessageDto) {
        Map<String,String> params = new HashMap<>();
        BuyorderGoods buyorderGoods = this.buyorderGoodsMapper.selectByPrimaryKey(sendMessageDto.getEarlyWarningTask().getRelateBusinessId());
        params.put("buyOrderNo",sendMessageDto.getEarlyWarningTask().getBusinessExtra1());
        params.put("sku",buyorderGoods.getSku());
        params.put("thisArrivalNum",sendMessageDto.getEarlyWarningTask().getUrgingTicketNum().toString());
        return params;
    }
}
