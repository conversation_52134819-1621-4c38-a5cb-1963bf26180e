package com.vedeng.erp.kingdee.batch.processor;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.BatchCapitalBillDto;
import com.vedeng.erp.kingdee.domain.entity.KingDeeSupplierEntity;
import com.vedeng.erp.kingdee.dto.KingDeeNeedPayDto;
import com.vedeng.erp.kingdee.dto.KingDeeNeedPayEntityDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeSupplierMapper;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 余额支付
 * @version 1.0
 * @date 2022/12/2 13:36
 */
@Service
@Slf4j
public class BatchCapitalBillProcessor implements ItemProcessor<BatchCapitalBillDto, KingDeeNeedPayDto> {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private KingDeeSupplierMapper kingDeeSupplierMapper;

    @Override
    public KingDeeNeedPayDto process(BatchCapitalBillDto batchCapitalBillDto) throws Exception {
        log.info("BatchCapitalBillProcessorService.KingDeeNeedPayDto,余额支付参数：" + JSON.toJSONString(batchCapitalBillDto));
        KingDeeSupplierEntity kingDeeSupplierEntity = kingDeeSupplierMapper.selectByFNumber(batchCapitalBillDto.getTraderSupplierId());
        if (kingDeeSupplierEntity == null) {
            log.error("该流水对应的供应商信息还未推送至金蝶，流水号:{}", batchCapitalBillDto.getCapitalBillNo());
            return null;
        }

        KingDeeNeedPayDto kingDeeDto = new KingDeeNeedPayDto();
        // 流水这边只能新增，不存在编辑的情况
        kingDeeDto.setFid("0");
        kingDeeDto.setFBillNo(batchCapitalBillDto.getCapitalBillNo());

        // 判断是否数据已存在
        boolean old = kingDeeBaseApi.isExist(kingDeeDto);
        if(old){
            log.info("余额支付,数据已存在:{}", JSON.toJSONString(batchCapitalBillDto));
            return null;
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        kingDeeDto.setFQzokDate(sdf.format(batchCapitalBillDto.getTraderTime()));
        kingDeeDto.setFQzokJg(KingDeeConstant.ORG_ID.toString());
        kingDeeDto.setFQzokBase(batchCapitalBillDto.getTraderSupplierId().toString());
        List<KingDeeNeedPayEntityDto> fEntity = new ArrayList<>();
        KingDeeNeedPayEntityDto temp = new KingDeeNeedPayEntityDto();
        temp.setFQzokYsddh(batchCapitalBillDto.getOrderNo());
        temp.setFQzokGsywdh(batchCapitalBillDto.getOrderNo());
        boolean equals = batchCapitalBillDto.getTraderMode().equals(10000);
        temp.setFQzokYwlx(equals?"返利余额付款":"订单付款");
        temp.setFQzokTzje(batchCapitalBillDto.getAmount());
        fEntity.add(temp);
        kingDeeDto.setFEntity(fEntity);
        return kingDeeDto;
    }
}