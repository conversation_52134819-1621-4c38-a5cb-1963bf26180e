<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.TraderCertificateErpMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.TraderCertificateErpEntity">
    <!--@mbg.generated-->
    <!--@Table T_TRADER_CERTIFICATE-->
    <id column="TRADER_CERTIFICATE_ID" jdbcType="INTEGER" property="traderCertificateId" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="SYS_OPTION_DEFINITION_ID" jdbcType="INTEGER" property="sysOptionDefinitionId" />
    <result column="IS_MEDICAL" jdbcType="INTEGER" property="isMedical" />
    <result column="BEGINTIME" jdbcType="BIGINT" property="begintime" />
    <result column="ENDTIME" jdbcType="BIGINT" property="endtime" />
    <result column="SN" jdbcType="VARCHAR" property="sn" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="DOMAIN" jdbcType="VARCHAR" property="domain" />
    <result column="URI" jdbcType="VARCHAR" property="uri" />
    <result column="EXTRA" jdbcType="VARCHAR" property="extra" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="AUTH_POST" jdbcType="VARCHAR" property="authPost" />
    <result column="AUTH_USERNAME" jdbcType="VARCHAR" property="authUsername" />
    <result column="AUTH_CONTACTINFO" jdbcType="VARCHAR" property="authContactinfo" />
    <result column="OSS_RESOURCE_ID" jdbcType="VARCHAR" property="ossResourceId" />
    <result column="ORIGINAL_FILEPATH" jdbcType="VARCHAR" property="originalFilepath" />
    <result column="SYN_SUCCESS" jdbcType="INTEGER" property="synSuccess" />
    <result column="COST_TIME" jdbcType="BIGINT" property="costTime" />
    <result column="RECORD_NO" jdbcType="VARCHAR" property="recordNo" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ISSUE_DATE" jdbcType="BIGINT" property="issueDate" />
    <result column="SUFFIX" jdbcType="VARCHAR" property="suffix" />
    <result column="TRADER_TYPE" jdbcType="INTEGER" property="traderType" />
    <result column="RELATED_ID" jdbcType="INTEGER" property="relatedId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    TRADER_CERTIFICATE_ID, TRADER_ID, SYS_OPTION_DEFINITION_ID, IS_MEDICAL, BEGINTIME, 
    ENDTIME, SN, `NAME`, `DOMAIN`, URI, EXTRA, ADD_TIME, CREATOR, MOD_TIME, UPDATER, 
    AUTH_POST, AUTH_USERNAME, AUTH_CONTACTINFO, OSS_RESOURCE_ID, ORIGINAL_FILEPATH, SYN_SUCCESS, 
    COST_TIME, RECORD_NO, IS_DELETE, ISSUE_DATE, SUFFIX, TRADER_TYPE, RELATED_ID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_TRADER_CERTIFICATE
    where TRADER_CERTIFICATE_ID = #{traderCertificateId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_TRADER_CERTIFICATE
    where TRADER_CERTIFICATE_ID = #{traderCertificateId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="TRADER_CERTIFICATE_ID" keyProperty="traderCertificateId" parameterType="com.vedeng.erp.trader.domain.entity.TraderCertificateErpEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_CERTIFICATE (TRADER_ID, SYS_OPTION_DEFINITION_ID, 
      IS_MEDICAL, BEGINTIME, ENDTIME, 
      SN, `NAME`, `DOMAIN`, URI, 
      EXTRA, ADD_TIME, CREATOR, 
      MOD_TIME, UPDATER, AUTH_POST, 
      AUTH_USERNAME, AUTH_CONTACTINFO, OSS_RESOURCE_ID, 
      ORIGINAL_FILEPATH, SYN_SUCCESS, COST_TIME, 
      RECORD_NO, IS_DELETE, ISSUE_DATE, 
      SUFFIX, TRADER_TYPE, RELATED_ID
      )
    values (#{traderId,jdbcType=INTEGER}, #{sysOptionDefinitionId,jdbcType=INTEGER}, 
      #{isMedical,jdbcType=INTEGER}, #{begintime,jdbcType=BIGINT}, #{endtime,jdbcType=BIGINT}, 
      #{sn,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{domain,jdbcType=VARCHAR}, #{uri,jdbcType=VARCHAR}, 
      #{extra,jdbcType=VARCHAR}, #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, 
      #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}, #{authPost,jdbcType=VARCHAR}, 
      #{authUsername,jdbcType=VARCHAR}, #{authContactinfo,jdbcType=VARCHAR}, #{ossResourceId,jdbcType=VARCHAR}, 
      #{originalFilepath,jdbcType=VARCHAR}, #{synSuccess,jdbcType=INTEGER}, #{costTime,jdbcType=BIGINT}, 
      #{recordNo,jdbcType=VARCHAR}, #{isDelete,jdbcType=INTEGER}, #{issueDate,jdbcType=BIGINT}, 
      #{suffix,jdbcType=VARCHAR}, #{traderType,jdbcType=INTEGER}, #{relatedId,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="TRADER_CERTIFICATE_ID" keyProperty="traderCertificateId" parameterType="com.vedeng.erp.trader.domain.entity.TraderCertificateErpEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_CERTIFICATE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="sysOptionDefinitionId != null">
        SYS_OPTION_DEFINITION_ID,
      </if>
      <if test="isMedical != null">
        IS_MEDICAL,
      </if>
      <if test="begintime != null">
        BEGINTIME,
      </if>
      <if test="endtime != null">
        ENDTIME,
      </if>
      <if test="sn != null">
        SN,
      </if>
      <if test="name != null">
        `NAME`,
      </if>
      <if test="domain != null">
        `DOMAIN`,
      </if>
      <if test="uri != null">
        URI,
      </if>
      <if test="extra != null">
        EXTRA,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="authPost != null">
        AUTH_POST,
      </if>
      <if test="authUsername != null">
        AUTH_USERNAME,
      </if>
      <if test="authContactinfo != null">
        AUTH_CONTACTINFO,
      </if>
      <if test="ossResourceId != null">
        OSS_RESOURCE_ID,
      </if>
      <if test="originalFilepath != null">
        ORIGINAL_FILEPATH,
      </if>
      <if test="synSuccess != null">
        SYN_SUCCESS,
      </if>
      <if test="costTime != null">
        COST_TIME,
      </if>
      <if test="recordNo != null">
        RECORD_NO,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="issueDate != null">
        ISSUE_DATE,
      </if>
      <if test="suffix != null">
        SUFFIX,
      </if>
      <if test="traderType != null">
        TRADER_TYPE,
      </if>
      <if test="relatedId != null">
        RELATED_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="sysOptionDefinitionId != null">
        #{sysOptionDefinitionId,jdbcType=INTEGER},
      </if>
      <if test="isMedical != null">
        #{isMedical,jdbcType=INTEGER},
      </if>
      <if test="begintime != null">
        #{begintime,jdbcType=BIGINT},
      </if>
      <if test="endtime != null">
        #{endtime,jdbcType=BIGINT},
      </if>
      <if test="sn != null">
        #{sn,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="domain != null">
        #{domain,jdbcType=VARCHAR},
      </if>
      <if test="uri != null">
        #{uri,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="authPost != null">
        #{authPost,jdbcType=VARCHAR},
      </if>
      <if test="authUsername != null">
        #{authUsername,jdbcType=VARCHAR},
      </if>
      <if test="authContactinfo != null">
        #{authContactinfo,jdbcType=VARCHAR},
      </if>
      <if test="ossResourceId != null">
        #{ossResourceId,jdbcType=VARCHAR},
      </if>
      <if test="originalFilepath != null">
        #{originalFilepath,jdbcType=VARCHAR},
      </if>
      <if test="synSuccess != null">
        #{synSuccess,jdbcType=INTEGER},
      </if>
      <if test="costTime != null">
        #{costTime,jdbcType=BIGINT},
      </if>
      <if test="recordNo != null">
        #{recordNo,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="issueDate != null">
        #{issueDate,jdbcType=BIGINT},
      </if>
      <if test="suffix != null">
        #{suffix,jdbcType=VARCHAR},
      </if>
      <if test="traderType != null">
        #{traderType,jdbcType=INTEGER},
      </if>
      <if test="relatedId != null">
        #{relatedId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.TraderCertificateErpEntity">
    <!--@mbg.generated-->
    update T_TRADER_CERTIFICATE
    <set>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="sysOptionDefinitionId != null">
        SYS_OPTION_DEFINITION_ID = #{sysOptionDefinitionId,jdbcType=INTEGER},
      </if>
      <if test="isMedical != null">
        IS_MEDICAL = #{isMedical,jdbcType=INTEGER},
      </if>
      <if test="begintime != null">
        BEGINTIME = #{begintime,jdbcType=BIGINT},
      </if>
      <if test="endtime != null">
        ENDTIME = #{endtime,jdbcType=BIGINT},
      </if>
      <if test="sn != null">
        SN = #{sn,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `NAME` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="domain != null">
        `DOMAIN` = #{domain,jdbcType=VARCHAR},
      </if>
      <if test="uri != null">
        URI = #{uri,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        EXTRA = #{extra,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="authPost != null">
        AUTH_POST = #{authPost,jdbcType=VARCHAR},
      </if>
      <if test="authUsername != null">
        AUTH_USERNAME = #{authUsername,jdbcType=VARCHAR},
      </if>
      <if test="authContactinfo != null">
        AUTH_CONTACTINFO = #{authContactinfo,jdbcType=VARCHAR},
      </if>
      <if test="ossResourceId != null">
        OSS_RESOURCE_ID = #{ossResourceId,jdbcType=VARCHAR},
      </if>
      <if test="originalFilepath != null">
        ORIGINAL_FILEPATH = #{originalFilepath,jdbcType=VARCHAR},
      </if>
      <if test="synSuccess != null">
        SYN_SUCCESS = #{synSuccess,jdbcType=INTEGER},
      </if>
      <if test="costTime != null">
        COST_TIME = #{costTime,jdbcType=BIGINT},
      </if>
      <if test="recordNo != null">
        RECORD_NO = #{recordNo,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="issueDate != null">
        ISSUE_DATE = #{issueDate,jdbcType=BIGINT},
      </if>
      <if test="suffix != null">
        SUFFIX = #{suffix,jdbcType=VARCHAR},
      </if>
      <if test="traderType != null">
        TRADER_TYPE = #{traderType,jdbcType=INTEGER},
      </if>
      <if test="relatedId != null">
        RELATED_ID = #{relatedId,jdbcType=INTEGER},
      </if>
    </set>
    where TRADER_CERTIFICATE_ID = #{traderCertificateId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.TraderCertificateErpEntity">
    <!--@mbg.generated-->
    update T_TRADER_CERTIFICATE
    set TRADER_ID = #{traderId,jdbcType=INTEGER},
      SYS_OPTION_DEFINITION_ID = #{sysOptionDefinitionId,jdbcType=INTEGER},
      IS_MEDICAL = #{isMedical,jdbcType=INTEGER},
      BEGINTIME = #{begintime,jdbcType=BIGINT},
      ENDTIME = #{endtime,jdbcType=BIGINT},
      SN = #{sn,jdbcType=VARCHAR},
      `NAME` = #{name,jdbcType=VARCHAR},
      `DOMAIN` = #{domain,jdbcType=VARCHAR},
      URI = #{uri,jdbcType=VARCHAR},
      EXTRA = #{extra,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      AUTH_POST = #{authPost,jdbcType=VARCHAR},
      AUTH_USERNAME = #{authUsername,jdbcType=VARCHAR},
      AUTH_CONTACTINFO = #{authContactinfo,jdbcType=VARCHAR},
      OSS_RESOURCE_ID = #{ossResourceId,jdbcType=VARCHAR},
      ORIGINAL_FILEPATH = #{originalFilepath,jdbcType=VARCHAR},
      SYN_SUCCESS = #{synSuccess,jdbcType=INTEGER},
      COST_TIME = #{costTime,jdbcType=BIGINT},
      RECORD_NO = #{recordNo,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ISSUE_DATE = #{issueDate,jdbcType=BIGINT},
      SUFFIX = #{suffix,jdbcType=VARCHAR},
      TRADER_TYPE = #{traderType,jdbcType=INTEGER},
      RELATED_ID = #{relatedId,jdbcType=INTEGER}
    where TRADER_CERTIFICATE_ID = #{traderCertificateId,jdbcType=INTEGER}
  </update>

  <update id="updatTraderBusinessCard" parameterType="com.vedeng.erp.trader.dto.TraderCertificateErpDto">
    update T_TRADER_CERTIFICATE set IS_DELETE = 1
    where RELATED_ID = #{relatedId,jdbcType=INTEGER}
    AND TRADER_TYPE = #{traderType}
    AND SYS_OPTION_DEFINITION_ID = #{sysOptionDefinitionId,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2024-02-21-->
  <select id="selectByRelatedId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_TRADER_CERTIFICATE
    where RELATED_ID=#{relatedId,jdbcType=INTEGER}
    and IS_DELETE = 0
  </select>
</mapper>