package com.vedeng.trader.dao;


import com.vedeng.trader.model.TraderFinance;
import com.vedeng.trader.model.vo.TraderFinanceVo;

import java.util.List;

public interface TraderFinanceMapper {
    int deleteByPrimaryKey(Integer traderFinanceId);

    int insert(TraderFinance record);

    int insertSelective(TraderFinance record);

    TraderFinance selectByPrimaryKey(Integer traderFinanceId);
    
    TraderFinance selectByTraderId(Integer traderFinanceId);

    int updateByPrimaryKeySelective(TraderFinance record);

    int updateByPrimaryKey(TraderFinance record);
    
    /**
     * <b>Description:</b><br> 查询客户的财务信息
     * @param traderFinance
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2017年5月31日 下午1:15:29
     */
    TraderFinanceVo getTraderCustomerFinance(TraderFinanceVo traderFinance);
    
    /**
     * <b>Description:</b><br> 查询供应商的财务信息
     * @param traderFinance
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2017年5月31日 下午1:15:29
     */
    TraderFinanceVo getTraderSupplierFinance(TraderFinanceVo traderFinance);
    
    /**
     * <b>Description:</b><br> 查询客户的银行帐号
     * @param traderFinance
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2017年5月31日 下午1:15:29
     */
    List<TraderFinance> getTraderCustomerFinanceList(TraderFinance traderFinance);

    /**
     * <b>Description:</b>查询客户财务信息<br>
     * @param
     * @return
     * @Note
     * <b>@Author:calvin</b>
     * <br><b>@Date:</b> 2020/10/23
     */
    TraderFinance getCustomerFinanceByTraderId(Integer traderId);

    /**
     * 在线更新客户资质信息
     *
     * @param record
     * @return
     */
    int updateTraderFinanceSelective(TraderFinance record);
}