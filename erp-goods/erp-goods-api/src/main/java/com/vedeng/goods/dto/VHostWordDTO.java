package com.vedeng.goods.dto;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * V_HOST_WORD
 * <AUTHOR>
@Data
public class VHostWordDTO implements Serializable {
    /**
     * 热词Id
     */
    private Integer hostWordId;

    /**
     * op热词Id
     */
    private Integer opHostWordId;

    /**
     * 热词内容
     */
    private String wordName;

    /**
     * 0上架   1下架
     */
    private Integer operateStatus;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 更新时间
     */
    private Date modeTime;

    /**
     * 0 未删除  1删除
     */
    private Integer isDelete;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;

    private Boolean checked;

    private static final long serialVersionUID = 1L;

}
