<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.infrastructure.file.mapper.AttachmentMapper" >
    <resultMap id="BaseResultMap" type="com.vedeng.infrastructure.file.domain.Attachment" >
        <id column="ATTACHMENT_ID" property="attachmentId" jdbcType="INTEGER" />
        <result column="ATTACHMENT_TYPE" property="attachmentType" jdbcType="INTEGER" />
        <result column="ATTACHMENT_FUNCTION" property="attachmentFunction" jdbcType="INTEGER" />
        <result column="RELATED_ID" property="relatedId" jdbcType="INTEGER" />
        <result column="NAME" property="name" jdbcType="VARCHAR" />
        <result column="DOMAIN" property="domain" jdbcType="VARCHAR" />
        <result column="URI" property="uri" jdbcType="VARCHAR" />
        <result column="ALT" property="alt" jdbcType="VARCHAR" />
        <result column="SORT" property="sort" jdbcType="INTEGER" />
        <result column="IS_DEFAULT" property="isDefault" jdbcType="BIT" />
        <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
        <result column="CREATOR" property="creator" jdbcType="INTEGER" />
        <result column="IS_DELETED" property="isDeleted" jdbcType="INTEGER" />
        <result column="SUFFIX" property="suffix" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        ATTACHMENT_ID, ATTACHMENT_TYPE, ATTACHMENT_FUNCTION, RELATED_ID, NAME, DOMAIN, URI,
    ALT, SORT, IS_DEFAULT, ADD_TIME, CREATOR, IS_DELETED, SUFFIX, SYN_SUCCESS
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select
        <include refid="Base_Column_List" />
        from T_ATTACHMENT
        where ATTACHMENT_ID = #{attachmentId,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
        delete from T_ATTACHMENT
        where ATTACHMENT_ID = #{attachmentId,jdbcType=INTEGER}
    </delete>

    <insert id="insertSelective" parameterType="com.vedeng.infrastructure.file.domain.Attachment" useGeneratedKeys="true" keyProperty="attachmentId">
        insert into T_ATTACHMENT
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="attachmentId != null" >
                ATTACHMENT_ID,
            </if>
            <if test="attachmentType != null" >
                ATTACHMENT_TYPE,
            </if>
            <if test="attachmentFunction != null" >
                ATTACHMENT_FUNCTION,
            </if>
            <if test="relatedId != null" >
                RELATED_ID,
            </if>
            <if test="name != null" >
                NAME,
            </if>
            <if test="domain != null" >
                DOMAIN,
            </if>
            <if test="uri != null" >
                URI,
            </if>
            <if test="alt != null" >
                ALT,
            </if>
            <if test="sort != null" >
                SORT,
            </if>
            <if test="isDefault != null" >
                IS_DEFAULT,
            </if>
            <if test="addTime != null" >
                ADD_TIME,
            </if>
            <if test="creator != null" >
                CREATOR,
            </if>
            <if test="ossResourceId != null" >
                OSS_RESOURCE_ID,
            </if>
            <if test="originalFilepath != null" >
                ORIGINAL_FILEPATH,
            </if>
            <if test="isDeleted != null" >
                IS_DELETED,
            </if>

            <if test="suffix != null" >
                SUFFIX,
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="attachmentId != null" >
                #{attachmentId,jdbcType=INTEGER},
            </if>
            <if test="attachmentType != null" >
                #{attachmentType,jdbcType=INTEGER},
            </if>
            <if test="attachmentFunction != null" >
                #{attachmentFunction,jdbcType=INTEGER},
            </if>
            <if test="relatedId != null" >
                #{relatedId,jdbcType=INTEGER},
            </if>
            <if test="name != null" >
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="domain != null" >
                #{domain,jdbcType=VARCHAR},
            </if>
            <if test="uri != null" >
                #{uri,jdbcType=VARCHAR},
            </if>
            <if test="alt != null" >
                #{alt,jdbcType=VARCHAR},
            </if>
            <if test="sort != null" >
                #{sort,jdbcType=INTEGER},
            </if>
            <if test="isDefault != null" >
                #{isDefault,jdbcType=BIT},
            </if>
            <if test="addTime != null" >
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null" >
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="ossResourceId != null" >
                #{ossResourceId,jdbcType=VARCHAR},
            </if>
            <if test="originalFilepath != null" >
                #{originalFilepath,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null" >
                #{isDeleted,jdbcType=INTEGER},
            </if>
            <if test="suffix != null" >
                #{suffix,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.infrastructure.file.domain.Attachment" >
        update T_ATTACHMENT
        <set >
            <if test="attachmentType != null" >
                ATTACHMENT_TYPE = #{attachmentType,jdbcType=INTEGER},
            </if>
            <if test="attachmentFunction != null" >
                ATTACHMENT_FUNCTION = #{attachmentFunction,jdbcType=INTEGER},
            </if>
            <if test="relatedId != null" >
                RELATED_ID = #{relatedId,jdbcType=INTEGER},
            </if>
            <if test="name != null" >
                NAME = #{name,jdbcType=VARCHAR},
            </if>
            <if test="domain != null" >
                DOMAIN = #{domain,jdbcType=VARCHAR},
            </if>
            <if test="uri != null" >
                URI = #{uri,jdbcType=VARCHAR},
            </if>
            <if test="alt != null" >
                ALT = #{alt,jdbcType=VARCHAR},
            </if>
            <if test="sort != null" >
                SORT = #{sort,jdbcType=INTEGER},
            </if>
            <if test="isDefault != null" >
                IS_DEFAULT = #{isDefault,jdbcType=BIT},
            </if>
            <if test="addTime != null" >
                ADD_TIME = #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null" >
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="ossResourceId != null" >
                OSS_RESOURCE_ID = #{ossResourceId,jdbcType=VARCHAR},
            </if>
            <if test="originalFilepath != null" >
                ORIGINAL_FILEPATH = #{originalFilepath,jdbcType=VARCHAR},
            </if>
            <if test="suffix != null" >
                SUFFIX = #{suffix,jdbcType=VARCHAR},
            </if>
            <if test="synSuccess != null" >
                SYN_SUCCESS = #{synSuccess,jdbcType=INTEGER},
            </if>
            <if test="isDeleted != null" >
                IS_DELETED = #{isDeleted,jdbcType=INTEGER},
            </if>
        </set>
        where ATTACHMENT_ID = #{attachmentId,jdbcType=INTEGER}
    </update>


    <select id="queryListByRelatedIdAndFunction" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM T_ATTACHMENT
        WHERE IS_DELETED = 0
            AND RELATED_ID = #{relatedId}
            AND ATTACHMENT_FUNCTION = #{attachmentFunction}
        ORDER BY SORT DESC
    </select>
    <update id="delAttachmentByPrimaryKey">
        UPDATE T_ATTACHMENT
        SET IS_DELETED = 1
        WHERE ATTACHMENT_ID = #{attachmentId,jdbcType=INTEGER}
    </update>

    <insert id="batchInsertAttachmentSelective" keyColumn="ATTACHMENT_ID" keyProperty="attachmentId" parameterType="com.vedeng.infrastructure.file.domain.Attachment" useGeneratedKeys="true">
        insert into T_ATTACHMENT (ATTACHMENT_TYPE, ATTACHMENT_FUNCTION,
        RELATED_ID,  `DOMAIN`,
        URI,ADD_TIME, CREATOR,SUFFIX)
        values
        <foreach collection="list" item="item" separator=",">
            <trim prefix=" (" suffix=")" suffixOverrides=",">
                #{item.attachmentType,jdbcType=INTEGER}, #{item.attachmentFunction,jdbcType=INTEGER},
                #{item.relatedId,jdbcType=INTEGER}, #{item.domain,jdbcType=VARCHAR},
                #{item.uri,jdbcType=VARCHAR},
                #{item.addTime,jdbcType=BIGINT}, #{item.creator,jdbcType=INTEGER},#{item.suffix}
            </trim>
        </foreach>

    </insert>

    <insert id="batchInsertSelective" keyColumn="ATTACHMENT_ID" keyProperty="attachmentId" parameterType="com.vedeng.infrastructure.file.domain.Attachment" useGeneratedKeys="true">
        insert into T_ATTACHMENT (ATTACHMENT_TYPE, ATTACHMENT_FUNCTION,
        RELATED_ID, NAME, `DOMAIN`,
        URI,SUFFIX, OSS_RESOURCE_ID, ADD_TIME, CREATOR)
        values
        <foreach collection="list" item="item" separator=",">
            <trim prefix=" (" suffix=")" suffixOverrides=",">
                #{item.attachmentType,jdbcType=INTEGER}, #{item.attachmentFunction,jdbcType=INTEGER},
                #{item.relatedId,jdbcType=INTEGER}, #{item.name,jdbcType=VARCHAR}, #{item.domain,jdbcType=VARCHAR},
                #{item.uri,jdbcType=VARCHAR},#{item.suffix,jdbcType=VARCHAR}, #{item.ossResourceId,jdbcType=VARCHAR},
                #{item.addTime,jdbcType=BIGINT}, #{item.creator,jdbcType=INTEGER}
            </trim>
        </foreach>

    </insert>

    <select id="queryByRelatedIdAndAttachmentTypeAndAttachmentFunction"
            resultType="com.vedeng.infrastructure.file.domain.Attachment">
        SELECT *
        FROM T_ATTACHMENT
        WHERE ATTACHMENT_TYPE = #{attachment.attachmentType,jdbcType=INTEGER}
        <if test="attachment.relatedId != null">
            AND RELATED_ID = #{attachment.relatedId,jdbcType=INTEGER}
        </if>
        AND ATTACHMENT_FUNCTION = #{attachment.attachmentFunction,jdbcType=INTEGER}
        AND IS_DELETED = 0
        ORDER BY SORT DESC
    </select>

    <select id="selectAllByIds" resultType="com.vedeng.infrastructure.file.domain.Attachment">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_ATTACHMENT a
        WHERE 1=1
          AND a.IS_DELETED = 0
        <if test="ids !=null and ids.size()>0">
            and a.ATTACHMENT_ID in
            <foreach collection="ids" item="id" index="index"
                     open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>
</mapper>
