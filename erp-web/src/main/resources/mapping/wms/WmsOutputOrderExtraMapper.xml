<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wms.dao.WmsOutputOrderExtraMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.wms.dto.WmsOutputOrderExtra">
    <!--@mbg.generated-->
    <!--@Table T_WMS_OUTPUT_ORDER_EXTRA-->
    <id column="OUTPUT_ORDER_EXTRA_ID" jdbcType="BIGINT" property="outputOrderExtraId" />
    <result column="WMS_OUTPUT_ORDER_ID" jdbcType="BIGINT" property="wmsOutputOrderId" />
    <result column="APPLY_TYPE" jdbcType="INTEGER" property="applyType" />
    <result column="APPLY_AMOUNT" jdbcType="DECIMAL" property="applyAmount" />
    <result column="ACTIVITY_CODE" jdbcType="VARCHAR" property="activityCode" />
    <result column="ACTIVITY_NAME" jdbcType="VARCHAR" property="activityName" />
    <result column="RECEIVER_ID" jdbcType="INTEGER" property="receiverId" />
    <result column="RECEIVER_ADDRESS_ID" jdbcType="INTEGER" property="receiverAddressId" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    OUTPUT_ORDER_EXTRA_ID, WMS_OUTPUT_ORDER_ID, APPLY_TYPE, APPLY_AMOUNT, ACTIVITY_CODE, 
    ACTIVITY_NAME, RECEIVER_ID, RECEIVER_ADDRESS_ID, IS_DELETE, ADD_TIME, CREATOR, CREATOR_NAME, 
    MOD_TIME, UPDATER, UPDATER_NAME, REMARK, UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_WMS_OUTPUT_ORDER_EXTRA
    where OUTPUT_ORDER_EXTRA_ID = #{outputOrderExtraId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_WMS_OUTPUT_ORDER_EXTRA
    where OUTPUT_ORDER_EXTRA_ID = #{outputOrderExtraId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="OUTPUT_ORDER_EXTRA_ID" keyProperty="outputOrderExtraId" parameterType="com.vedeng.erp.wms.dto.WmsOutputOrderExtra" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WMS_OUTPUT_ORDER_EXTRA (WMS_OUTPUT_ORDER_ID, APPLY_TYPE, APPLY_AMOUNT, 
      ACTIVITY_CODE, ACTIVITY_NAME, RECEIVER_ID, 
      RECEIVER_ADDRESS_ID, IS_DELETE, ADD_TIME, 
      CREATOR, CREATOR_NAME, MOD_TIME, 
      UPDATER, UPDATER_NAME, REMARK, 
      UPDATE_REMARK)
    values (#{wmsOutputOrderId,jdbcType=BIGINT}, #{applyType,jdbcType=INTEGER}, #{applyAmount,jdbcType=DECIMAL}, 
      #{activityCode,jdbcType=VARCHAR}, #{activityName,jdbcType=VARCHAR}, #{receiverId,jdbcType=INTEGER}, 
      #{receiverAddressId,jdbcType=INTEGER}, #{isDelete,jdbcType=BOOLEAN}, #{addTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{modTime,jdbcType=TIMESTAMP}, 
      #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{updateRemark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="OUTPUT_ORDER_EXTRA_ID" keyProperty="outputOrderExtraId" parameterType="com.vedeng.erp.wms.dto.WmsOutputOrderExtra" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WMS_OUTPUT_ORDER_EXTRA
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="wmsOutputOrderId != null">
        WMS_OUTPUT_ORDER_ID,
      </if>
      <if test="applyType != null">
        APPLY_TYPE,
      </if>
      <if test="applyAmount != null">
        APPLY_AMOUNT,
      </if>
      <if test="activityCode != null">
        ACTIVITY_CODE,
      </if>
      <if test="activityName != null">
        ACTIVITY_NAME,
      </if>
      <if test="receiverId != null">
        RECEIVER_ID,
      </if>
      <if test="receiverAddressId != null">
        RECEIVER_ADDRESS_ID,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="wmsOutputOrderId != null">
        #{wmsOutputOrderId,jdbcType=BIGINT},
      </if>
      <if test="applyType != null">
        #{applyType,jdbcType=INTEGER},
      </if>
      <if test="applyAmount != null">
        #{applyAmount,jdbcType=DECIMAL},
      </if>
      <if test="activityCode != null">
        #{activityCode,jdbcType=VARCHAR},
      </if>
      <if test="activityName != null">
        #{activityName,jdbcType=VARCHAR},
      </if>
      <if test="receiverId != null">
        #{receiverId,jdbcType=INTEGER},
      </if>
      <if test="receiverAddressId != null">
        #{receiverAddressId,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.wms.dto.WmsOutputOrderExtra">
    <!--@mbg.generated-->
    update T_WMS_OUTPUT_ORDER_EXTRA
    <set>
      <if test="wmsOutputOrderId != null">
        WMS_OUTPUT_ORDER_ID = #{wmsOutputOrderId,jdbcType=BIGINT},
      </if>
      <if test="applyType != null">
        APPLY_TYPE = #{applyType,jdbcType=INTEGER},
      </if>
      <if test="applyAmount != null">
        APPLY_AMOUNT = #{applyAmount,jdbcType=DECIMAL},
      </if>
      <if test="activityCode != null">
        ACTIVITY_CODE = #{activityCode,jdbcType=VARCHAR},
      </if>
      <if test="activityName != null">
        ACTIVITY_NAME = #{activityName,jdbcType=VARCHAR},
      </if>
      <if test="receiverId != null">
        RECEIVER_ID = #{receiverId,jdbcType=INTEGER},
      </if>
      <if test="receiverAddressId != null">
        RECEIVER_ADDRESS_ID = #{receiverAddressId,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where OUTPUT_ORDER_EXTRA_ID = #{outputOrderExtraId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.wms.dto.WmsOutputOrderExtra">
    <!--@mbg.generated-->
    update T_WMS_OUTPUT_ORDER_EXTRA
    set WMS_OUTPUT_ORDER_ID = #{wmsOutputOrderId,jdbcType=BIGINT},
      APPLY_TYPE = #{applyType,jdbcType=INTEGER},
      APPLY_AMOUNT = #{applyAmount,jdbcType=DECIMAL},
      ACTIVITY_CODE = #{activityCode,jdbcType=VARCHAR},
      ACTIVITY_NAME = #{activityName,jdbcType=VARCHAR},
      RECEIVER_ID = #{receiverId,jdbcType=INTEGER},
      RECEIVER_ADDRESS_ID = #{receiverAddressId,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where OUTPUT_ORDER_EXTRA_ID = #{outputOrderExtraId,jdbcType=BIGINT}
  </update>


  <!--auto generated by MybatisCodeHelper on 2023-02-22-->
  <select id="selectByWmsOutputOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_WMS_OUTPUT_ORDER_EXTRA
    where WMS_OUTPUT_ORDER_ID=#{wmsOutputOrderId,jdbcType=BIGINT}
    and IS_DELETE = 0  limit 1
  </select>

  <update id="deleteWmsOutputOrderExtraByOrderId">
    UPDATE T_WMS_OUTPUT_ORDER_EXTRA SET IS_DELETE = 1 WHERE  WMS_OUTPUT_ORDER_ID = #{wmsOutputOrderId,jdbcType=BIGINT}
    </update>
</mapper>