package com.vedeng.dwh.externaldb.dao.generate;

import com.vedeng.dwh.model.generate.DwmNjUserOrgDfDo;
import org.springframework.stereotype.Repository;

@Repository
public interface DwmNjUserOrgDfMapper {
    int deleteByPrimaryKey(Long vdOdsPk);

    int insert(DwmNjUserOrgDfDo record);

    int insertSelective(DwmNjUserOrgDfDo record);

    DwmNjUserOrgDfDo selectByPrimaryKey(Long vdOdsPk);

    int updateByPrimaryKeySelective(DwmNjUserOrgDfDo record);

    int updateByPrimaryKey(DwmNjUserOrgDfDo record);
}