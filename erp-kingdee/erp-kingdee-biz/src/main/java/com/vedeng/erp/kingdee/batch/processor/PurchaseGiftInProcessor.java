package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInItemDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchBuyorderDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchBuyorderGoodsDtoMapper;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryExtParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryFilterDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 采购赠品入库
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class PurchaseGiftInProcessor implements ItemProcessor<BatchWarehouseGoodsOutInDto, KingDeeStorageInDto> {


    @Autowired
    private BatchBuyorderDtoMapper buyorderDtoMapper;

    @Autowired
    private BatchBuyorderGoodsDtoMapper batchBuyorderGoodsDtoMapper;

    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;
    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Override
    public KingDeeStorageInDto process(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto) throws Exception {
        log.info("PurchaseGiftInProcessorService.process：{}" , JSON.toJSONString(batchWarehouseGoodsOutInDto));

        KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        queryParam.setFormId(KingDeeFormConstant.STK_MISCELLANEOUS);
        List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("fBillNo").compare("=").left("(").right(")").value(batchWarehouseGoodsOutInDto.getOutInNo()).logic("AND").build());
        queryParam.setFilterString(queryFilterDtos);
        List<KingDeeStorageInResultDto> query = kingDeeBaseApi.query(queryParam, KingDeeStorageInResultDto.class);
        log.info("采购赠品入库单查询金蝶结果：{}",JSON.toJSONString(query));
        if (CollUtil.isNotEmpty(query)) {
            log.info("已推送金蝶数据，无需推送");
            return null;
        }
        BatchBuyorderDto batchBuyorderDto = buyorderDtoMapper.selectByBuyorderNo(batchWarehouseGoodsOutInDto.getRelateNo());
        if (Objects.isNull(batchBuyorderDto)) {
            throw new KingDeeException("未查到此赠品采购单"+batchWarehouseGoodsOutInDto.getRelateNo()+"信息");
        }
        List<BatchBuyorderGoodsDto> batchBuyorderGoodsDtos = batchBuyorderGoodsDtoMapper.selectByBuyorderIdNotDelete(batchBuyorderDto.getBuyorderId());
        if (CollUtil.isEmpty(batchBuyorderGoodsDtos)) {
            throw new KingDeeException("赠品采购单未查询到商品信息");
        }

        String isDirect = batchBuyorderDto.getDeliveryDirect().equals(0) ? "否" : "是";

        Map<Integer, BatchBuyorderGoodsDto> map = batchBuyorderGoodsDtos
                .stream().collect(Collectors.toMap(BatchBuyorderGoodsDto::getBuyorderGoodsId, c -> c, (k1, k2) -> k1));

        KingDeeStorageInDto dto = new KingDeeStorageInDto();
        dto.setFId("0");
        dto.setFBillNo(batchWarehouseGoodsOutInDto.getOutInNo());
        dto.setFQzokBddjtId(batchWarehouseGoodsOutInDto.getWarehouseGoodsOutInId().toString());
        dto.setFDate(DateUtil.formatDate(batchWarehouseGoodsOutInDto.getOutInTime()));
        dto.setFSupplierId(batchBuyorderDto.getTraderSupplierId().toString());
        dto.setFStockDirect("GENERAL");


        List<KingDeeStorageInDetailDto> detailDtoList = new ArrayList<>();
        List<BatchWarehouseGoodsOutInItemDto> byOutInNo = batchWarehouseGoodsOutInItemDtoMapper.findByOutInNo(batchWarehouseGoodsOutInDto.getOutInNo());
        if (CollUtil.isEmpty(byOutInNo)) {
            log.error("未能查到出库单no:{}子单信息",JSON.toJSONString(batchWarehouseGoodsOutInDto.getOutInNo()));
            throw new KingDeeException("未能查到出库单no:" + JSON.toJSONString(batchWarehouseGoodsOutInDto.getOutInNo())+"子单信息");
        }
        batchWarehouseGoodsOutInDto.setBatchWarehouseGoodsOutInItemDtos(byOutInNo);
        batchWarehouseGoodsOutInDto.getBatchWarehouseGoodsOutInItemDtos().forEach(l -> {
            KingDeeStorageInDetailDto detailDto = new KingDeeStorageInDetailDto();
            BatchBuyorderGoodsDto batchBuyorderGoodsDto = map.get(l.getRelatedId());
            if (Objects.isNull(batchBuyorderGoodsDto)) {
                log.error("采购赠品未能查到具体的商品明细：{}",JSON.toJSONString(l));
                throw new KingDeeException("采购赠品售后未能查到具体的商品明细");
            }
            detailDto.setFMaterialId(batchBuyorderGoodsDto.getSku());
            detailDto.setFStockId("CK9999");
            BigDecimal num = l.getNum().abs();
            detailDto.setFQty(num.toString());
            detailDto.setFPrice("0.01");
            detailDto.setFAmount(new BigDecimal("0.01").multiply(num).toString());

            detailDto.setFQzokYsddh(batchBuyorderDto.getBuyorderNo());
            detailDto.setFQzokGsywdh(batchBuyorderDto.getBuyorderNo());
            detailDto.setFQzokYwlx("采购赠品");
            detailDto.setFQzokPch(l.getBatchNumber());
            detailDto.setFQzokXlh(l.getBarcodeFactory());
            detailDto.setFQzokSfzf(isDirect);
            detailDto.setFQzokBddjhId(l.getWarehouseGoodsOutInDetailId().toString());

            detailDtoList.add(detailDto);
        });
        dto.setFEntity(detailDtoList);

        return dto;
    }

}
