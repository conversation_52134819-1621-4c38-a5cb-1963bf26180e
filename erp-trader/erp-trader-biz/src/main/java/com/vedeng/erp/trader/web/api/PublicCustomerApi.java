package com.vedeng.erp.trader.web.api;

import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.annotation.NoRepeatSubmit;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.erp.trader.domain.dto.CustomerAssignDto;
import com.vedeng.erp.trader.facade.CustomerAssignFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpSession;

@ExceptionController
@RestController
@RequestMapping("/publicCustomer")
@Slf4j
public class PublicCustomerApi {
    
    @Autowired
    private CustomerAssignFacade customerAssignFacade;

    @RequestMapping(value = "/assign", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    @NoRepeatSubmit
    public R<Boolean> getTraderAddress(@RequestBody CustomerAssignDto customerAssignDto, HttpSession session) {
        log.info("客户分配请求参数：{}", customerAssignDto);
        // 当前登录人
        User sessionUser = (User) session.getAttribute(ErpConst.CURR_USER);
        customerAssignDto.setSessionUser(sessionUser);
        customerAssignFacade.assign(customerAssignDto);
        return R.success(Boolean.TRUE);
    }
}
