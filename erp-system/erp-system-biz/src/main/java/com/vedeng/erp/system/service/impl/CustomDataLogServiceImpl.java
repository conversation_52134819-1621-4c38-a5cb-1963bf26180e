package com.vedeng.erp.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.system.dto.CustomDataLogDto;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.mapper.CustomDataLogMapper;
import com.vedeng.erp.system.service.CustomDataLogApiService;
import com.vedeng.erp.system.service.CustomDataLogService;
import com.vedeng.erp.system.service.UserApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 自定义操作service
 * @date 2022/7/15 12:51
 */
@Service
@Slf4j
public class CustomDataLogServiceImpl implements CustomDataLogService, CustomDataLogApiService {

    @Autowired
    private CustomDataLogMapper customDataLogMapper;

    @Autowired
    private UserApiService userApiService;


    @Override
    public CustomDataLogDto getByAllOrderByAddTime(CustomDataLogDto customDataLogDto) {
        return customDataLogMapper.getByAllOrderByAddTime(customDataLogDto);
    }

    @Override
    public PageInfo<CustomDataLogDto> page(PageParam<CustomDataLogDto> customDataLogPageParam) {

        CurrentUser currentUser = CurrentUser.getCurrentUser();
        UserDto userDto = UserDto.builder().isAdmin(currentUser.getIsAdmin())
                .companyId(currentUser.getCompanyId())
                .positType(currentUser.getPositType())
                .userId(currentUser.getId())
                .build();
        if (customDataLogPageParam.getParam().getBelongerId() == null) {
            List<UserDto> allSubUserList = userApiService.getAllSubUserList(userDto, Collections.singletonList(310), false);
            customDataLogPageParam.getParam().setBelongers(allSubUserList.stream().map(UserDto::getUserId).collect(Collectors.toList()));
        }
        return PageHelper.startPage(customDataLogPageParam)
                .doSelectPageInfo(() -> customDataLogMapper.findByAll(customDataLogPageParam.getParam()));
    }
}
