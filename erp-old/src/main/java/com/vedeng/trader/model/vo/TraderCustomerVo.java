package com.vedeng.trader.model.vo;

import com.vedeng.customerbillperiod.dto.BillPeriodItem;
import com.vedeng.order.model.Quoteorder;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.vo.BussinessChanceVo;
import com.vedeng.trader.model.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <b>Description:</b><br>
 * vo类
 *
 * <AUTHOR>
 * @Note <b>ProjectName:</b> erp <br>
 * <b>PackageName:</b> com.vedeng.trader.model.vo <br>
 * <b>ClassName:</b> TraderCustomerVo <br>
 * <b>Date:</b> 2017年5月17日 下午2:45:28
 */
public class TraderCustomerVo extends TraderCustomer {
    /**
     * @Fields serialVersionUID : TODO
     */
    private static final long serialVersionUID = 1L;

    private String name;// 名称
    private String area;// 所在地
    private String address;// 地址
    private Integer aptitudeStatus; //资质审核的状态：0为审核中，1为审核通过，2为审核不通过，3为待审核
    private Integer lockStatus;// 锁定状态
    private String lockUserName; // 锁定人
    private Integer customerStatus;// 审核状态：状态0请求审核1审核通过2审核不通过
    private Integer customerProperty;// 客户性质
    
    private String systemJudge;
    
    private String userJudge;

    private String customerPropertys;// 搜索不选客户类型，直接选客户性质

    private Integer bussinessChanceCount;//商机次数
    private Integer buyCount;// 交易次数
    private BigDecimal buyMoney;// 交易金额
    private Integer quoteCount;// 报价次数
    private Integer orderCount;// 交易次数
    private BigDecimal orderTotalAmount;// 交易金额
    private Integer communcateCount;// 沟通次数
    private BigDecimal averagePrice;//均单价
    private BigDecimal transactionFrequency;//交易频次
    private Long lastCommuncateTime;//上次沟通时间
    private Integer partnerId;// 页面搜索id
    private String partners;// 战略合作伙伴
    private Long startTime;// 开始时间，用于页面搜索
    private Long endTime;// 结束时间，用于页面搜索
    private String contactWay;// 联系方式

    private Integer areaId;// 所属区域id

    private String areaIds;//多级区域

    private String personal;// 归属销售
    private Integer personalId;//归属销售ID
    private String cooperate;// 合作

    private Integer timeType;// 页面搜索时间：1、创建时间；2、交易时间3、更新时间

    private List<Integer> traderList;// 交易者id

    private Integer wxStatus;// 微信状态

    private Integer smallScore;// 页面搜索条件，客户得分小分数
    private Integer bigScore;// 页面搜索条件，客户得分大分数

    private Integer quote;// 有报价：1、是；2、否

    private List<Integer> categoryList;//分类属性

    private Long lastBussinessTime;//上次交易时间
    private Long firstBussinessTime;//第一次交易时间

    private String customerTypeStr;// 客户类型
    private String customerPropertyStr;// 客户性质

    private String customerNatureStr;// 客户性质

    private String traderCustomerCategoryNames;// 客户分类类型

    private Integer customerPropertyType;//1分销 2终端 0其它

    private String traderName;//客户名称

    private String searchTraderName;//搜索内容

    private String optType;//操作类型

    private List<Integer> enquiryOrderIds;//商机

    private List<Integer> quoteOrderIds;//报价

    private List<Integer> saleOrderIds;//销售

    private List<Integer> buyOrderIds;//采购订单

    private List<Integer> serviceOrderIds;//售后

    private TraderSupplierVo traderSupplierVo;//供应商 

    private String phone;//电话

    private Integer lastCommunicateType;//上次沟通类型

    private Integer lastRelatedId;//上次沟通主表ID

    private String contactName;// 联系人名称
    
    private String showInvalidReason;

    private List<BussinessChanceVo> bussinessChanceList;//商机列表

    private List<Quoteorder> quoteorderList;//报价列表

    private List<Saleorder> saleorderList;//订单列表

    private Integer traderContactId;//联系人ID

    private String searchMsg;//信息搜索 ：沟通记录内容，商机的询价产品

    private List<CommunicateRecord> communicateRecordList;//沟通记录

    private Integer originUserId;// 公海列表原销售id
    private String originUserName;
    private Integer homePurchasing;//归属销售--页面搜索条件
    private Integer province;//省份id
    private Integer city;//市id
    private Integer zone;//区县id
    private String queryStartTime;//查询开始时间
    private String queryEndTime;//查询结束时间
    private Integer isView;//列表也是否能查看0-否；1-是
    private Integer companyId;//客户所属公司

    private BigDecimal grossRate;//毛利率

    private Integer traderGoodsTypeNum;//交易商品种类

    private Integer traderGoodsNum;//交易商品数量

    private Integer traderBrandNum;//交易品牌数量

    private BigDecimal saleorderRate;//有限订单率

    private BigDecimal refundGoodsAmount;//退货金额

    private Integer refundGoodsTimes;//退货次数

    private String firstTraderTimeStr;//初次交易时间

    private String lastTraderTimeStr;//上次交易时间

    private String lastCommuncateTimeStr;//上次沟通时间

    private String modTimeStr;//更新时间

    private String provinceName;//省

    private String cityName;//市

    private String zoneName;//区

    private String addTimeStr;//添加时间格式化

    private String orgName;//部门

    private String traderContactName;//默认联系人的名称

    private String traderContactMobile;//默认联系人的手机号

    /**
     * 默认联系人电话
     */
    private String traderContactTelephone;

    /**
     * 默认联系人其他联系方式
     */
    private String traderContactMobile2;

    private Integer traderAreaId;

    private String traderAddress;

    private CommunicateRecord communicateRecord;//上次沟通记录

    private String lastVerifyUsermae;//最后审核人

    private String verifyUsername;//当前审核人

    private Integer verifyStatus;//审核状态

    private List<String> verifyUsernameList;//当前审核人

    private String[] threeMedicalType;//

    private String[] twoMedicalType;//

    private Integer traderAddressId;//联系地址ID

    private List<TraderOrderGoods> traderOrderGoodsList;

    private List<Integer> userIdList;//客户id集合

    private Integer userId;//客户归属的用户

    private Integer communicateRecordId;

    private String registeredDateStr;

    private List<String> categoryNameList;//订单覆盖品类

    private List<String> brandNameList;//订单覆盖品牌

    private List<String> areaList;//订单覆盖产品

    private String traderLevelStr;//合作等级

    private Integer customerCategory;

    private String officeStr;
    // 财务审核状态
    private Integer financeCheckStatus;
    //客户分群id
    private Integer traderGroupId;
    //客户分群名称多个
    private String traderGroupStr;

    private List<Integer> traderGroupIdList;

    private Integer saleorderId;

    //商品品牌
    private String brandName;

    //规格/型号
    private String typeSpecification;

    //单位
    private String unitName;

    /**
     * 所有的有效账期
     */
    private List<BillPeriodItem> allBillPeriod;
    /**
     * 集采客户属性
     */
    private Integer traderType;

    /**
     * 公海 挖掘价值条件类型
     */
    private Integer conditionType;

    //客户提醒
    private Integer customerAlert;

    //公海客户原归属销售
    private List<Integer> originUserList;
    //经销商有效性
    private Integer effectiveness;
    //终端机构性质
    private Integer institutionNature;
    //终端机构评级
    private List<Integer> institutionLevel;
    //经营终端大类&类型
    private List<String> businessTerminalCategoriesAndType;
    //主营商品类型
    private Integer skuType;
    //主营商品范畴
    private List<Integer> traderCustomerMainCategory;
    //主营商品范畴类别
    private Integer traderCustomerMainCategoryType;
    //销售类别
    private Integer traderCustomerOwnership;
    //核心资源
    private Integer traderCustomerDevelopLevel;
    //客户等级
    private String traderCustomerLevelGrade;
    //生命周期
    private String traderCustomerLifeCycle;
    private String invalidReason;
    //交易分类
    private List<Integer> traderCustomerTrade;

    //交易分类类别
    private Integer traderCustomerTradeType;
    //交易品牌
    private List<Integer> traderCustomerBrand;
    //交易品牌类型
    private Integer traderCustomerBrandType;
    //沟通记录
    private Integer traderCustomerCommunicate;
    //历史沟通次数
    private Integer traderCustomerCommunicateTimes;
    //有无手机
    private Integer traderCustomerIsMobile;
    //是否公海
    private Integer traderCustomerIsSea;
    //贝登会员
    private Integer traderCustomerIsMember;
    //地区类型
    private Integer areaType;
    //历史沟通次数列表回显
    private Integer historyCommunicationNum;
    // 标签完善度 类型
    private Integer selectLabelPerfectionValue;
    // 标签完善度 多选
    private List<Integer> selectLabelPerfectionChildValue;
    // 标签完善度
    private Integer selectLabelPerfectionChildValueOne;
    // 人工更新标签
    private Integer manualUpdate;

    /**
     * 公海客户等级
     */
    private Integer gonghaiLevel;

    /**
     * 查询对象
     */
    private List<Integer> queryGongHaiLevelList;
    /**
     * 查询对象
     */
    private List<Integer> queryInvalidReasonList;



    /**
     * T_TRADER_CUSTOMER_MARKETING_PRINCIPAL 代理品牌id集合
     */
    private List<Integer> agencyBrandIdList;

    /**
     * T_TRADER_CUSTOMER_MARKETING_PRINCIPAL 其他代理品牌
     */
    private String otherAgencyBrand;

    /**
     * T_TRADER_CUSTOMER_MARKETING_PRINCIPAL 代理商品
     */
    private List<Integer> agencySkuList;

    /**
     * T_TRADER_CUSTOMER_MARKETING_PRINCIPAL 其他代理商品
     */
    private String otherAgencySku;

    /**
     * T_TRADER_CUSTOMER_MARKETING_PRINCIPAL 政府关系
     */
    private String governmentRelation;

    /**
     * T_TRADER_CUSTOMER_MARKETING_PRINCIPAL 其他政府关系
     */
    private String otherGovernmentRelation;

    /**
     * 分享的销售id
     */
    private Integer sharedSaleId;

    public String getLockUserName() {
        return lockUserName;
    }

    public void setLockUserName(String lockUserName) {
        this.lockUserName = lockUserName;
    }

    public Integer getLockStatus() {
        return lockStatus;
    }

    public void setLockStatus(Integer lockStatus) {
        this.lockStatus = lockStatus;
    }

    public String getSystemJudge() {
        return systemJudge;
    }

    public void setSystemJudge(String systemJudge) {
        this.systemJudge = systemJudge;
    }

    public String getUserJudge() {
        return userJudge;
    }

    public void setUserJudge(String userJudge) {
        this.userJudge = userJudge;
    }

    public Integer getManualUpdate() {
        return manualUpdate;
    }

    public void setManualUpdate(Integer manualUpdate) {
        this.manualUpdate = manualUpdate;
    }

    public List<Integer> getQueryInvalidReasonList() {
        return queryInvalidReasonList;
    }

    public void setQueryInvalidReasonList(List<Integer> queryInvalidReasonList) {
        this.queryInvalidReasonList = queryInvalidReasonList;
    }

    public Integer getSelectLabelPerfectionValue() {
        return selectLabelPerfectionValue;
    }

    public void setSelectLabelPerfectionValue(Integer selectLabelPerfectionValue) {
        this.selectLabelPerfectionValue = selectLabelPerfectionValue;
    }

    public List<Integer> getSelectLabelPerfectionChildValue() {
        return selectLabelPerfectionChildValue;
    }

    public void setSelectLabelPerfectionChildValue(List<Integer> selectLabelPerfectionChildValue) {
        this.selectLabelPerfectionChildValue = selectLabelPerfectionChildValue;
    }

    public Integer getSelectLabelPerfectionChildValueOne() {
        return selectLabelPerfectionChildValueOne;
    }

    public void setSelectLabelPerfectionChildValueOne(Integer selectLabelPerfectionChildValueOne) {
        this.selectLabelPerfectionChildValueOne = selectLabelPerfectionChildValueOne;
    }

    public Integer getHistoryCommunicationNum() {
        return historyCommunicationNum;
    }

    public void setHistoryCommunicationNum(Integer historyCommunicationNum) {
        this.historyCommunicationNum = historyCommunicationNum;
    }

    public Integer getAreaType() {
        return areaType;
    }

    public void setAreaType(Integer areaType) {
        this.areaType = areaType;
    }

    public List<String> getBusinessTerminalCategoriesAndType() { return businessTerminalCategoriesAndType;}

    public Integer getTraderCustomerBrandType() {
        return traderCustomerBrandType;
    }

    public void setTraderCustomerBrandType(Integer traderCustomerBrandType) {
        this.traderCustomerBrandType = traderCustomerBrandType;
    }

    public Integer getTraderCustomerTradeType() {
        return traderCustomerTradeType;
    }

    public void setTraderCustomerTradeType(Integer traderCustomerTradeType) {
        this.traderCustomerTradeType = traderCustomerTradeType;
    }

    public Integer getTraderCustomerMainCategoryType() {
        return traderCustomerMainCategoryType;
    }

    public void setTraderCustomerMainCategoryType(Integer traderCustomerMainCategoryType) {
        this.traderCustomerMainCategoryType = traderCustomerMainCategoryType;
    }

    public void setBusinessTerminalCategoriesAndType(List<String> businessTerminalCategoriesAndType) {
        this.businessTerminalCategoriesAndType = businessTerminalCategoriesAndType;
    }

    public String getInvalidReason() {
        return invalidReason;
    }

    public void setInvalidReason(String invalidReason) {
        this.invalidReason = invalidReason;
    }

    public Integer getSkuType() {
        return skuType;
    }

    public void setSkuType(Integer skuType) {
        this.skuType = skuType;
    }

    public List<Integer> getTraderCustomerMainCategory() {
        return traderCustomerMainCategory;
    }

    public void setTraderCustomerMainCategory(List<Integer> traderCustomerMainCategory) {
        this.traderCustomerMainCategory = traderCustomerMainCategory;
    }

    public Integer getTraderCustomerOwnership() {
        return traderCustomerOwnership;
    }

    public void setTraderCustomerOwnership(Integer traderCustomerOwnership) {
        this.traderCustomerOwnership = traderCustomerOwnership;
    }

    public Integer getTraderCustomerDevelopLevel() {
        return traderCustomerDevelopLevel;
    }

    public void setTraderCustomerDevelopLevel(Integer traderCustomerDevelopLevel) {
        this.traderCustomerDevelopLevel = traderCustomerDevelopLevel;
    }

    public String getTraderCustomerLevelGrade() {
        return traderCustomerLevelGrade;
    }

    public void setTraderCustomerLevelGrade(String traderCustomerLevelGrade) {
        this.traderCustomerLevelGrade = traderCustomerLevelGrade;
    }

    public String getTraderCustomerLifeCycle() {
        return traderCustomerLifeCycle;
    }

    public void setTraderCustomerLifeCycle(String traderCustomerLifeCycle) {
        this.traderCustomerLifeCycle = traderCustomerLifeCycle;
    }

    public List<Integer> getTraderCustomerTrade() {
        return traderCustomerTrade;
    }

    public void setTraderCustomerTrade(List<Integer> traderCustomerTrade) {
        this.traderCustomerTrade = traderCustomerTrade;
    }

    public List<Integer> getTraderCustomerBrand() {
        return traderCustomerBrand;
    }

    public void setTraderCustomerBrand(List<Integer> traderCustomerBrand) {
        this.traderCustomerBrand = traderCustomerBrand;
    }

    public Integer getTraderCustomerCommunicate() {
        return traderCustomerCommunicate;
    }

    public void setTraderCustomerCommunicate(Integer traderCustomerCommunicate) {
        this.traderCustomerCommunicate = traderCustomerCommunicate;
    }

    public Integer getTraderCustomerCommunicateTimes() {
        return traderCustomerCommunicateTimes;
    }

    public void setTraderCustomerCommunicateTimes(Integer traderCustomerCommunicateTimes) {
        this.traderCustomerCommunicateTimes = traderCustomerCommunicateTimes;
    }

    public Integer getTraderCustomerIsMobile() {
        return traderCustomerIsMobile;
    }

    public void setTraderCustomerIsMobile(Integer traderCustomerIsMobile) {
        this.traderCustomerIsMobile = traderCustomerIsMobile;
    }

    public Integer getTraderCustomerIsMember() {
        return traderCustomerIsMember;
    }

    public void setTraderCustomerIsMember(Integer traderCustomerIsMember) {
        this.traderCustomerIsMember = traderCustomerIsMember;
    }

    public Integer getTraderCustomerIsSea() {
        return traderCustomerIsSea;
    }

    public void setTraderCustomerIsSea(Integer traderCustomerIsSea) {
        this.traderCustomerIsSea = traderCustomerIsSea;
    }


    public String getTraderCustomerCategoryNames() {
        return traderCustomerCategoryNames;
    }

    public void setTraderCustomerCategoryNames(String traderCustomerCategoryNames) {
        this.traderCustomerCategoryNames = traderCustomerCategoryNames;
    }

    public Integer getCustomerAlert() {
        return customerAlert;
    }

    public void setCustomerAlert(Integer customerAlert) {
        this.customerAlert = customerAlert;
    }

    public List<BillPeriodItem> getAllBillPeriod() {
        return allBillPeriod;
    }

    public void setAllBillPeriod(List<BillPeriodItem> allBillPeriod) {
        this.allBillPeriod = allBillPeriod;
    }

    public Integer getOriginUserId() {
        return originUserId;
    }

    public void setOriginUserId(Integer originUserId) {
        this.originUserId = originUserId;
    }

    public Integer getConditionType() {
        return conditionType;
    }

    public void setConditionType(Integer conditionType) {
        this.conditionType = conditionType;
    }

    public List<Integer> getOriginUserList() {
        return originUserList;
    }

    public void setOriginUserList(List<Integer> originUserList) {
        this.originUserList = originUserList;
    }

    private List<Integer> wxTraderIdList;//添加微信的客户

    private List<Integer> parenterTraderIdList;//战略合作伙伴客户

    private List<Integer> cooperateTraderIds;//有合作查询客户集合id集合

    private List<Integer> contactTraderIds;//联系方式客户集合id集合

    private List<Integer> capitalBillTraderIdList;//交易流水中的客户主键值

    private List<Integer> searchTraderGroupList;

    private Integer publicCustomerRecordId;
    private Integer isPrivatized;

    public Integer getIsPrivatized() {
        return isPrivatized;
    }

    public void setIsPrivatized(Integer isPrivatized) {
        this.isPrivatized = isPrivatized;
    }

    public Integer getPublicCustomerRecordId() {
        return publicCustomerRecordId;
    }

    public void setPublicCustomerRecordId(Integer publicCustomerRecordId) {
        this.publicCustomerRecordId = publicCustomerRecordId;
    }

    /**
     * 公私海规则下的TraderId集合
     */
    private List<Integer> publicTraderCustomerIdList;

    public List<Integer> getPublicTraderCustomerIdList() {
        return publicTraderCustomerIdList;
    }

    public void setPublicTraderCustomerIdList(List<Integer> publicTraderCustomerIdList) {
        this.publicTraderCustomerIdList = publicTraderCustomerIdList;
    }

    private String traderGroupIdStr;

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getTypeSpecification() {
        return typeSpecification;
    }

    public void setTypeSpecification(String typeSpecification) {
        this.typeSpecification = typeSpecification;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Integer getSaleorderId() {
        return saleorderId;
    }

    public String getShowInvalidReason() {
        return showInvalidReason;
    }

    public void setShowInvalidReason(String showInvalidReason) {
        this.showInvalidReason = showInvalidReason;
    }

    public void setSaleorderId(Integer saleorderId) {
        this.saleorderId = saleorderId;
    }

    public List<Integer> getTraderGroupIdList() {
        return traderGroupIdList;
    }

    public void setTraderGroupIdList(List<Integer> traderGroupIdList) {
        this.traderGroupIdList = traderGroupIdList;
    }

    public Integer getTraderGroupId() {
        return traderGroupId;
    }

    public void setTraderGroupId(Integer traderGroupId) {
        this.traderGroupId = traderGroupId;
    }

    public String getTraderGroupStr() {
        return traderGroupStr;
    }

    public void setTraderGroupStr(String traderGroupStr) {
        this.traderGroupStr = traderGroupStr;
    }

    //是否有有效优惠标识
    private Integer couponFlag;

    public Integer getCouponFlag() {
        return couponFlag;
    }

    public void setCouponFlag(Integer couponFlag) {
        this.couponFlag = couponFlag;
    }

    //是否归属当前销售 0不是 1是
    private Integer isBelong;
    //客户联系人
    private List<TraderContact> traderContactList;

    public List<TraderContact> getTraderContactList() {
        return traderContactList;
    }

    public void setTraderContactList(List<TraderContact> traderContactList) {
        this.traderContactList = traderContactList;
    }

    public Integer getIsBelong() {
        return isBelong;
    }

    public void setIsBelong(Integer isBelong) {
        this.isBelong = isBelong;
    }

    public Integer getFinanceCheckStatus() {
        return financeCheckStatus;
    }

    public void setFinanceCheckStatus(Integer financeCheckStatus) {
        this.financeCheckStatus = financeCheckStatus;
    }

    public String getOfficeStr() {
        return officeStr;
    }

    public void setOfficeStr(String officeStr) {
        this.officeStr = officeStr;
    }

    @Override
    public Integer getCustomerCategory() {
        return customerCategory;
    }

    @Override
    public void setCustomerCategory(Integer customerCategory) {
        this.customerCategory = customerCategory;
    }


    public Integer getAptitudeStatus() {
        return aptitudeStatus;
    }

    public void setAptitudeStatus(Integer aptitudeStatus) {
        this.aptitudeStatus = aptitudeStatus;
    }

    public String getTraderLevelStr() {
        return traderLevelStr;
    }

    public void setTraderLevelStr(String traderLevelStr) {
        this.traderLevelStr = traderLevelStr;
    }

    public List<String> getCategoryNameList() {
        return categoryNameList;
    }

    public void setCategoryNameList(List<String> categoryNameList) {
        this.categoryNameList = categoryNameList;
    }

    public List<String> getBrandNameList() {
        return brandNameList;
    }

    public void setBrandNameList(List<String> brandNameList) {
        this.brandNameList = brandNameList;
    }

    public Integer getEffectiveness() {
        return effectiveness;
    }

    public void setEffectiveness(Integer effectiveness) {
        this.effectiveness = effectiveness;
    }

    public Integer getInstitutionNature() {
        return institutionNature;
    }

    public void setInstitutionNature(Integer institutionNature) {
        this.institutionNature = institutionNature;
    }

    public List<Integer> getInstitutionLevel() {
        return institutionLevel;
    }

    public void setInstitutionLevel(List<Integer> institutionLevel) {
        this.institutionLevel = institutionLevel;
    }

    public List<String> getAreaList() {
        return areaList;
    }

    public void setAreaList(List<String> areaList) {
        this.areaList = areaList;
    }

    public String getRegisteredDateStr() {
        return registeredDateStr;
    }

    public void setRegisteredDateStr(String registeredDateStr) {
        this.registeredDateStr = registeredDateStr;
    }

    public Integer getCommunicateRecordId() {
        return communicateRecordId;
    }

    public void setCommunicateRecordId(Integer communicateRecordId) {
        this.communicateRecordId = communicateRecordId;
    }

    public String[] getThreeMedicalType() {
        return threeMedicalType;
    }

    public void setThreeMedicalType(String[] threeMedicalType) {
        this.threeMedicalType = threeMedicalType;
    }

    public String[] getTwoMedicalType() {
        return twoMedicalType;
    }

    public void setTwoMedicalType(String[] twoMedicalType) {
        this.twoMedicalType = twoMedicalType;
    }

    public CommunicateRecord getCommunicateRecord() {
        return communicateRecord;
    }

    public void setCommunicateRecord(CommunicateRecord communicateRecord) {
        this.communicateRecord = communicateRecord;
    }

    private List<Integer> communicateTraderIds;//沟通客户集合

    private List<Integer> searchMsgTraderIds;//信息搜索客户ID

    public String getCustomerNatureStr() {
        return customerNatureStr;
    }

    public void setCustomerNatureStr(String customerNatureStr) {
        this.customerNatureStr = customerNatureStr;
    }

    public String getOptType() {
        return optType;
    }

    public void setOptType(String optType) {
        this.optType = optType;
    }

    public String getSearchTraderName() {
        return searchTraderName;
    }

    public void setSearchTraderName(String searchTraderName) {
        this.searchTraderName = searchTraderName == null ? null : searchTraderName.replaceAll("\\(", "（").replaceAll("\\)", "）").trim();
    }

    public String getName() {
        return name;
    }

    public String getTraderName() {
        return traderName;
    }

    public void setTraderName(String traderName) {
        this.traderName = traderName == null ? null : traderName.replaceAll("\\(", "（").replaceAll("\\)", "）").trim();
    }

    public void setName(String name) {
        this.name = name == null ? null : name.replaceAll("\\(", "（").replaceAll("\\)", "）").trim();
    }

    public Integer getTraderType() {
        return traderType;
    }

    public void setTraderType(Integer traderType) {
        this.traderType = traderType;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getCustomerStatus() {
        return customerStatus;
    }

    public void setCustomerStatus(Integer customerStatus) {
        this.customerStatus = customerStatus;
    }

    public Integer getCustomerProperty() {
        return customerProperty;
    }

    public void setCustomerProperty(Integer customerProperty) {
        this.customerProperty = customerProperty;
    }

    public Integer getBuyCount() {
        return buyCount;
    }

    public void setBuyCount(Integer buyCount) {
        this.buyCount = buyCount;
    }

    public BigDecimal getBuyMoney() {
        return buyMoney;
    }

    public void setBuyMoney(BigDecimal buyMoney) {
        this.buyMoney = buyMoney;
    }

    public Integer getQuoteCount() {
        return quoteCount;
    }

    public void setQuoteCount(Integer quoteCount) {
        this.quoteCount = quoteCount;
    }

    public Integer getCommuncateCount() {
        return communcateCount;
    }

    public void setCommuncateCount(Integer communcateCount) {
        this.communcateCount = communcateCount;
    }

    public String getPartners() {
        return partners;
    }

    public void setPartners(String partners) {
        this.partners = partners;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public String getContactWay() {
        return contactWay;
    }

    public void setContactWay(String contactWay) {
        this.contactWay = contactWay;
    }

    public Integer getAreaId() {
        return areaId;
    }

    public void setAreaId(Integer areaId) {
        this.areaId = areaId;
    }

    public String getPersonal() {
        return personal;
    }

    public void setPersonal(String personal) {
        this.personal = personal;
    }

    public String getCooperate() {
        return cooperate;
    }

    public void setCooperate(String cooperate) {
        this.cooperate = cooperate;
    }

    public Integer getTimeType() {
        return timeType;
    }

    public void setTimeType(Integer timeType) {
        this.timeType = timeType;
    }

    public List<Integer> getTraderList() {
        return traderList;
    }

    public void setTraderList(List<Integer> traderList) {
        this.traderList = traderList;
    }

    public Integer getWxStatus() {
        return wxStatus;
    }

    public void setWxStatus(Integer wxStatus) {
        this.wxStatus = wxStatus;
    }

    public Integer getSmallScore() {
        return smallScore;
    }

    public void setSmallScore(Integer smallScore) {
        this.smallScore = smallScore;
    }

    public Integer getBigScore() {
        return bigScore;
    }

    public void setBigScore(Integer bigScore) {
        this.bigScore = bigScore;
    }

    public Integer getQuote() {
        return quote;
    }

    public void setQuote(Integer quote) {
        this.quote = quote;
    }

    public Integer getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    public List<Integer> getCategoryList() {
        return categoryList;
    }

    public void setCategoryList(List<Integer> categoryList) {
        this.categoryList = categoryList;
    }

    public Long getLastBussinessTime() {
        return lastBussinessTime;
    }

    public void setLastBussinessTime(Long lastBussinessTime) {
        this.lastBussinessTime = lastBussinessTime;
    }

    public String getCustomerTypeStr() {
        return customerTypeStr;
    }

    public void setCustomerTypeStr(String customerTypeStr) {
        this.customerTypeStr = customerTypeStr;
    }

    public String getCustomerPropertyStr() {
        return customerPropertyStr;
    }

    public void setCustomerPropertyStr(String customerPropertyStr) {
        this.customerPropertyStr = customerPropertyStr;
    }

    public Integer getCustomerPropertyType() {
        return customerPropertyType;
    }

    public void setCustomerPropertyType(Integer customerPropertyType) {
        this.customerPropertyType = customerPropertyType;
    }

    public String getAreaIds() {
        return areaIds;
    }

    public void setAreaIds(String areaIds) {
        this.areaIds = areaIds;
    }

    public Integer getBussinessChanceCount() {
        return bussinessChanceCount;
    }

    public void setBussinessChanceCount(Integer bussinessChanceCount) {
        this.bussinessChanceCount = bussinessChanceCount;
    }

    public BigDecimal getAveragePrice() {
        return averagePrice;
    }

    public void setAveragePrice(BigDecimal averagePrice) {
        this.averagePrice = averagePrice;
    }

    public BigDecimal getTransactionFrequency() {
        return transactionFrequency;
    }

    public void setTransactionFrequency(BigDecimal transactionFrequency) {
        this.transactionFrequency = transactionFrequency;
    }

    public Long getLastCommuncateTime() {
        return lastCommuncateTime;
    }

    public void setLastCommuncateTime(Long lastCommuncateTime) {
        this.lastCommuncateTime = lastCommuncateTime;
    }

    public Long getFirstBussinessTime() {
        return firstBussinessTime;
    }

    public void setFirstBussinessTime(Long firstBussinessTime) {
        this.firstBussinessTime = firstBussinessTime;
    }

    public List<Integer> getEnquiryOrderIds() {
        return enquiryOrderIds;
    }

    public void setEnquiryOrderIds(List<Integer> enquiryOrderIds) {
        this.enquiryOrderIds = enquiryOrderIds;
    }

    public List<Integer> getQuoteOrderIds() {
        return quoteOrderIds;
    }

    public void setQuoteOrderIds(List<Integer> quoteOrderIds) {
        this.quoteOrderIds = quoteOrderIds;
    }

    public List<Integer> getSaleOrderIds() {
        return saleOrderIds;
    }

    public void setSaleOrderIds(List<Integer> saleOrderIds) {
        this.saleOrderIds = saleOrderIds;
    }

    public List<Integer> getBuyOrderIds() {
        return buyOrderIds;
    }

    public void setBuyOrderIds(List<Integer> buyOrderIds) {
        this.buyOrderIds = buyOrderIds;
    }

    public List<Integer> getServiceOrderIds() {
        return serviceOrderIds;
    }

    public void setServiceOrderIds(List<Integer> serviceOrderIds) {
        this.serviceOrderIds = serviceOrderIds;
    }

    public TraderSupplierVo getTraderSupplierVo() {
        return traderSupplierVo;
    }

    public void setTraderSupplierVo(TraderSupplierVo traderSupplierVo) {
        this.traderSupplierVo = traderSupplierVo;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getLastCommunicateType() {
        return lastCommunicateType;
    }

    public void setLastCommunicateType(Integer lastCommunicateType) {
        this.lastCommunicateType = lastCommunicateType;
    }

    public Integer getLastRelatedId() {
        return lastRelatedId;
    }

    public void setLastRelatedId(Integer lastRelatedId) {
        this.lastRelatedId = lastRelatedId;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public List<BussinessChanceVo> getBussinessChanceList() {
        return bussinessChanceList;
    }

    public void setBussinessChanceList(List<BussinessChanceVo> bussinessChanceList) {
        this.bussinessChanceList = bussinessChanceList;
    }

    public List<Quoteorder> getQuoteorderList() {
        return quoteorderList;
    }

    public void setQuoteorderList(List<Quoteorder> quoteorderList) {
        this.quoteorderList = quoteorderList;
    }

    public List<Saleorder> getSaleorderList() {
        return saleorderList;
    }

    public void setSaleorderList(List<Saleorder> saleorderList) {
        this.saleorderList = saleorderList;
    }

    public Integer getTraderContactId() {
        return traderContactId;
    }

    public void setTraderContactId(Integer traderContactId) {
        this.traderContactId = traderContactId;
    }

    public List<CommunicateRecord> getCommunicateRecordList() {
        return communicateRecordList;
    }

    public void setCommunicateRecordList(List<CommunicateRecord> communicateRecordList) {
        this.communicateRecordList = communicateRecordList;
    }

    public String getSearchMsg() {
        return searchMsg;
    }

    public void setSearchMsg(String searchMsg) {
        this.searchMsg = searchMsg;
    }

    public Integer getHomePurchasing() {
        return homePurchasing;
    }

    public void setHomePurchasing(Integer homePurchasing) {
        this.homePurchasing = homePurchasing;
    }

    public Integer getProvince() {
        return province;
    }

    public void setProvince(Integer province) {
        this.province = province;
    }

    public Integer getCity() {
        return city;
    }

    public void setCity(Integer city) {
        this.city = city;
    }

    public Integer getZone() {
        return zone;
    }

    public void setZone(Integer zone) {
        this.zone = zone;
    }

    public String getQueryStartTime() {
        return queryStartTime;
    }

    public void setQueryStartTime(String queryStartTime) {
        this.queryStartTime = queryStartTime;
    }

    public String getQueryEndTime() {
        return queryEndTime;
    }

    public void setQueryEndTime(String queryEndTime) {
        this.queryEndTime = queryEndTime;
    }

    public Integer getIsView() {
        return isView;
    }

    public void setIsView(Integer isView) {
        this.isView = isView;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public Integer getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }

    public BigDecimal getOrderTotalAmount() {
        return orderTotalAmount;
    }

    public void setOrderTotalAmount(BigDecimal orderTotalAmount) {
        this.orderTotalAmount = orderTotalAmount;
    }

    public BigDecimal getGrossRate() {
        return grossRate;
    }

    public void setGrossRate(BigDecimal grossRate) {
        this.grossRate = grossRate;
    }

    public Integer getTraderGoodsNum() {
        return traderGoodsNum;
    }

    public void setTraderGoodsNum(Integer traderGoodsNum) {
        this.traderGoodsNum = traderGoodsNum;
    }

    public Integer getTraderGoodsTypeNum() {
        return traderGoodsTypeNum;
    }

    public void setTraderGoodsTypeNum(Integer traderGoodsTypeNum) {
        this.traderGoodsTypeNum = traderGoodsTypeNum;
    }

    public Integer getTraderBrandNum() {
        return traderBrandNum;
    }

    public void setTraderBrandNum(Integer traderBrandNum) {
        this.traderBrandNum = traderBrandNum;
    }

    public BigDecimal getSaleorderRate() {
        return saleorderRate;
    }

    public void setSaleorderRate(BigDecimal saleorderRate) {
        this.saleorderRate = saleorderRate;
    }

    public BigDecimal getRefundGoodsAmount() {
        return refundGoodsAmount;
    }

    public void setRefundGoodsAmount(BigDecimal refundGoodsAmount) {
        this.refundGoodsAmount = refundGoodsAmount;
    }

    public Integer getRefundGoodsTimes() {
        return refundGoodsTimes;
    }

    public void setRefundGoodsTimes(Integer refundGoodsTimes) {
        this.refundGoodsTimes = refundGoodsTimes;
    }

    public String getFirstTraderTimeStr() {
        return firstTraderTimeStr;
    }

    public void setFirstTraderTimeStr(String firstTraderTimeStr) {
        this.firstTraderTimeStr = firstTraderTimeStr;
    }

    public String getLastTraderTimeStr() {
        return lastTraderTimeStr;
    }

    public void setLastTraderTimeStr(String lastTraderTimeStr) {
        this.lastTraderTimeStr = lastTraderTimeStr;
    }

    public String getLastCommuncateTimeStr() {
        return lastCommuncateTimeStr;
    }

    public void setLastCommuncateTimeStr(String lastCommuncateTimeStr) {
        this.lastCommuncateTimeStr = lastCommuncateTimeStr;
    }

    public String getModTimeStr() {
        return modTimeStr;
    }

    public void setModTimeStr(String modTimeStr) {
        this.modTimeStr = modTimeStr;
    }


    public String getCustomerPropertys() {
        return customerPropertys;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setCustomerPropertys(String customerPropertys) {
        this.customerPropertys = customerPropertys;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getZoneName() {
        return zoneName;
    }

    public void setZoneName(String zoneName) {
        this.zoneName = zoneName;
    }

    public String getAddTimeStr() {
        return addTimeStr;
    }

    public void setAddTimeStr(String addTimeStr) {
        this.addTimeStr = addTimeStr;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }


    public String getTraderContactName() {
        return traderContactName;
    }

    public void setTraderContactName(String traderContactName) {
        this.traderContactName = traderContactName;
    }

    public String getTraderContactMobile() {
        return traderContactMobile;
    }

    public void setTraderContactMobile(String traderContactMobile) {
        this.traderContactMobile = traderContactMobile;
    }

    public List<Integer> getCommunicateTraderIds() {
        return communicateTraderIds;
    }

    public void setCommunicateTraderIds(List<Integer> communicateTraderIds) {
        this.communicateTraderIds = communicateTraderIds;
    }

    public List<Integer> getSearchMsgTraderIds() {
        return searchMsgTraderIds;
    }

    public void setSearchMsgTraderIds(List<Integer> searchMsgTraderIds) {
        this.searchMsgTraderIds = searchMsgTraderIds;
    }

    public String getLastVerifyUsermae() {
        return lastVerifyUsermae;
    }

    public void setLastVerifyUsermae(String lastVerifyUsermae) {
        this.lastVerifyUsermae = lastVerifyUsermae;
    }

    public String getVerifyUsername() {
        return verifyUsername;
    }

    public void setVerifyUsername(String verifyUsername) {
        this.verifyUsername = verifyUsername;
    }

    public Integer getVerifyStatus() {
        return verifyStatus;
    }

    public void setVerifyStatus(Integer verifyStatus) {
        this.verifyStatus = verifyStatus;
    }

    public List<String> getVerifyUsernameList() {
        return verifyUsernameList;
    }

    public void setVerifyUsernameList(List<String> verifyUsernameList) {
        this.verifyUsernameList = verifyUsernameList;
    }

    public Integer getTraderAddressId() {
        return traderAddressId;
    }

    public void setTraderAddressId(Integer traderAddressId) {
        this.traderAddressId = traderAddressId;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public List<TraderOrderGoods> getTraderOrderGoodsList() {
        return traderOrderGoodsList;
    }

    public void setTraderOrderGoodsList(List<TraderOrderGoods> traderOrderGoodsList) {
        this.traderOrderGoodsList = traderOrderGoodsList;
    }

    public List<Integer> getUserIdList() {
        return userIdList;
    }

    public void setUserIdList(List<Integer> userIdList) {
        this.userIdList = userIdList;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getTraderContactTelephone() {
        return traderContactTelephone;
    }

    public void setTraderContactTelephone(String traderContactTelephone) {
        this.traderContactTelephone = traderContactTelephone;
    }

    public String getTraderContactMobile2() {
        return traderContactMobile2;
    }

    public void setTraderContactMobile2(String traderContactMobile2) {
        this.traderContactMobile2 = traderContactMobile2;
    }

    public Integer getPersonalId() {
        return personalId;
    }

    public void setPersonalId(Integer personalId) {
        this.personalId = personalId;
    }

    public List<Integer> getWxTraderIdList() {
        return wxTraderIdList;
    }

    public void setWxTraderIdList(List<Integer> wxTraderIdList) {
        this.wxTraderIdList = wxTraderIdList;
    }

    public List<Integer> getParenterTraderIdList() {
        return parenterTraderIdList;
    }

    public void setParenterTraderIdList(List<Integer> parenterTraderIdList) {
        this.parenterTraderIdList = parenterTraderIdList;
    }

    public List<Integer> getCooperateTraderIds() {
        return cooperateTraderIds;
    }

    public void setCooperateTraderIds(List<Integer> cooperateTraderIds) {
        this.cooperateTraderIds = cooperateTraderIds;
    }

    public List<Integer> getContactTraderIds() {
        return contactTraderIds;
    }

    public void setContactTraderIds(List<Integer> contactTraderIds) {
        this.contactTraderIds = contactTraderIds;
    }

    public List<Integer> getCapitalBillTraderIdList() {
        return capitalBillTraderIdList;
    }

    public void setCapitalBillTraderIdList(List<Integer> capitalBillTraderIdList) {
        this.capitalBillTraderIdList = capitalBillTraderIdList;
    }

    public List<Integer> getSearchTraderGroupList() {
        return searchTraderGroupList;
    }

    public void setSearchTraderGroupList(List<Integer> searchTraderGroupList) {
        this.searchTraderGroupList = searchTraderGroupList;
    }

    public String getTraderGroupIdStr() {
        return traderGroupIdStr;
    }

    public void setTraderGroupIdStr(String traderGroupIdStr) {
        this.traderGroupIdStr = traderGroupIdStr;
    }

    public Integer getTraderAreaId() {
        return traderAreaId;
    }

    public void setTraderAreaId(Integer traderAreaId) {
        this.traderAreaId = traderAreaId;
    }

    public String getTraderAddress() {
        return traderAddress;
    }

    public void setTraderAddress(String traderAddress) {
        this.traderAddress = traderAddress;
    }

    public String getOriginUserName() {
        return originUserName;
    }

    public void setOriginUserName(String originUserName) {
        this.originUserName = originUserName;
    }

    public List<Integer> getAgencyBrandIdList() {
        return agencyBrandIdList;
    }

    public void setAgencyBrandIdList(List<Integer> agencyBrandIdList) {
        this.agencyBrandIdList = agencyBrandIdList;
    }

    public String getOtherAgencyBrand() {
        return otherAgencyBrand;
    }

    public void setOtherAgencyBrand(String otherAgencyBrand) {
        this.otherAgencyBrand = otherAgencyBrand;
    }

    public List<Integer> getAgencySkuList() {
        return agencySkuList;
    }

    public void setAgencySkuList(List<Integer> agencySkuList) {
        this.agencySkuList = agencySkuList;
    }

    public String getOtherAgencySku() {
        return otherAgencySku;
    }

    public void setOtherAgencySku(String otherAgencySku) {
        this.otherAgencySku = otherAgencySku;
    }

    public String getGovernmentRelation() {
        return governmentRelation;
    }

    public void setGovernmentRelation(String governmentRelation) {
        this.governmentRelation = governmentRelation;
    }

    public String getOtherGovernmentRelation() {
        return otherGovernmentRelation;
    }

    public void setOtherGovernmentRelation(String otherGovernmentRelation) {
        this.otherGovernmentRelation = otherGovernmentRelation;
    }

    public Integer getSharedSaleId() {
        return sharedSaleId;
    }

    public void setSharedSaleId(Integer sharedSaleId) {
        this.sharedSaleId = sharedSaleId;
    }


    public Integer getGonghaiLevel() {
        return gonghaiLevel;
    }

    public void setGonghaiLevel(Integer gonghaiLevel) {
        this.gonghaiLevel = gonghaiLevel;
    }

    public List<Integer> getQueryGongHaiLevelList() {
        return queryGongHaiLevelList;
    }

    public void setQueryGongHaiLevelList(List<Integer> queryGongHaiLevelList) {
        this.queryGongHaiLevelList = queryGongHaiLevelList;
    }
}
