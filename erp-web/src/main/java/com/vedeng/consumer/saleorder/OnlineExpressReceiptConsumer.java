package com.vedeng.consumer.saleorder;

import com.alibaba.fastjson.JSON;
import com.rabbitmq.client.Channel;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.redis.RedisUtils;
import com.vedeng.common.trace.mq.AbstractMessageListener;
import com.vedeng.erp.saleorder.constant.OnlineBusinessTypeEnum;
import com.vedeng.erp.saleorder.dao.OnlineInvoiceMessageMapper;
import com.vedeng.erp.saleorder.model.dto.ExpressReceiptDto;
import com.vedeng.erp.saleorder.model.po.OnlineInvoiceMessagePo;
import com.vedeng.erp.saleorder.service.OnlineInvoiceOpenService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 包裹在线签收消费者
 *
 * <AUTHOR>
 */
public class OnlineExpressReceiptConsumer extends AbstractMessageListener {

    private static final Integer EXPRESS_ONLINE_RECEIPT_TIME = 7200000;

    private static final String LOCK_KEY_FLAG = "expressReceiptLockKey_";

    public static final Logger logger = LoggerFactory.getLogger(OnlineExpressReceiptConsumer.class);

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private OnlineInvoiceOpenService onlineInvoiceOpenService;

    @Resource
    private OnlineInvoiceMessageMapper onlineInvoiceMessageMapper;

    @Override
    public void doBusiness(Message message, Channel channel) throws Exception {
        String jsonString = new String(message.getBody(), StandardCharsets.UTF_8);
        logger.info("在线签收前台推送消息 jsonString:{}", jsonString);

        if (StringUtils.isBlank(jsonString)) {
            logger.error("要处理，在线签收前台推送消息数据异常 jsonString:{}", jsonString);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            return;
        }

        List<ExpressReceiptDto> receiptDtoList = null;
        try {
            receiptDtoList = JSON.parseArray(jsonString, ExpressReceiptDto.class);
        } catch (Exception e) {
            logger.error("在线签收接收报文格式错误", e);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        }

        OnlineInvoiceMessagePo onlineInvoiceMessagePo = new OnlineInvoiceMessagePo();
        onlineInvoiceMessagePo.setBusinessKey(OnlineBusinessTypeEnum.ONLINE_EXPRESS_RECEIPT.getCode());
        onlineInvoiceMessagePo.setMessageBody(jsonString);
        onlineInvoiceMessagePo.setAddTime(System.currentTimeMillis());

        if (CollectionUtils.isEmpty(receiptDtoList)) {
            logger.error("要处理，在线签收前台推送消息数据异常 jsonString:{}", jsonString);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            return;
        }

        onlineInvoiceMessagePo.setMessageId(receiptDtoList.get(0).getMessageId());

        String expressReceiptLockFlag = LOCK_KEY_FLAG;

        try {
            expressReceiptLockFlag = expressReceiptLockFlag + receiptDtoList.get(0).getOrderNo();

            boolean expressReceiptLock = redisUtils.tryGetDistributedLock(expressReceiptLockFlag, expressReceiptLockFlag, EXPRESS_ONLINE_RECEIPT_TIME);

            if (expressReceiptLock) {
                onlineInvoiceOpenService.dealExpressReceiptBusiness(receiptDtoList);
            } else {
                logger.warn("在线签收消息消费获取锁失败,lockKey:{},jsonString:{}", expressReceiptLockFlag, jsonString);
            }
            onlineInvoiceMessagePo.setConsumeResult(expressReceiptLock ? ErpConst.ONE : ErpConst.ZERO);
        } catch (RuntimeException e) {
            logger.error("在线签收信息消费出现消息类型异常 jsonString:{}, e:{}", jsonString, e);
            onlineInvoiceMessagePo.setConsumeResult(ErpConst.TWO);
        } catch (Exception e) {
            logger.error("在线签收消息出现本地处理异常 jsonString:{},e:{}", jsonString, e);
            onlineInvoiceMessagePo.setConsumeResult(ErpConst.ZERO);
        } finally {
            try {
                redisUtils.releaseDistributedLock(expressReceiptLockFlag, expressReceiptLockFlag);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                OnlineInvoiceMessagePo messageInfoByMessageId = onlineInvoiceMessageMapper.getMessageInfoByMessageId(receiptDtoList.get(0).getMessageId());
                if (messageInfoByMessageId == null) {
                    logger.info("在线签收消息本地未保存信息,新增消息记录,onlineInvoiceMessagePo:{}", JSON.toJSONString(onlineInvoiceMessagePo));
                    onlineInvoiceMessageMapper.insert(onlineInvoiceMessagePo);
                } else {
                    logger.info("在线签收消息本地已保存信息,更新消息记录,onlineInvoiceMessagePo:{}", JSON.toJSONString(onlineInvoiceMessagePo));
                    onlineInvoiceMessageMapper.updateOnlineInvoiceMessage(messageInfoByMessageId.getId(), onlineInvoiceMessagePo.getConsumeResult(), System.currentTimeMillis());
                }
            } catch (Exception e) {
                logger.error("保存在线开票消息记录异常 onlineInvoiceMessagePo:{},e:{}", JSON.toJSONString(onlineInvoiceMessagePo), e);
            }
        }
    }
}
