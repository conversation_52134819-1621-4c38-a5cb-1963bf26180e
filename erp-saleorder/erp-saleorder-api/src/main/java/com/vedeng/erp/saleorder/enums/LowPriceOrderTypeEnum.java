package com.vedeng.erp.saleorder.enums;

/**
 * 改低价类型枚举
 *
 */
public enum LowPriceOrderTypeEnum {
    /**
     * 销售单
     */
    SALE_ORDER(1,"销售单"),

    /**
     * 报价单
     */
    QUOTATION(2,"报价单"),

    ;

    private Integer orderType;

    private String desc;

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    LowPriceOrderTypeEnum(Integer orderType, String desc) {
        this.orderType = orderType;
        this.desc = desc;
    }
}
