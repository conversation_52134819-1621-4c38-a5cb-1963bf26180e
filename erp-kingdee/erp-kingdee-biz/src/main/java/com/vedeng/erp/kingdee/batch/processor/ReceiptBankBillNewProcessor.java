package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.kingdee.batch.common.enums.AfterSalesProcessEnum;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.*;
import com.vedeng.erp.kingdee.domain.entity.KingDeeCustomerEntity;
import com.vedeng.erp.kingdee.domain.entity.KingDeePayVedengBankDto;
import com.vedeng.erp.kingdee.domain.entity.KingDeeSupplierEntity;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveBillDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveBillEntryDto;
import com.vedeng.erp.kingdee.enums.KingDeeBankTagEnum;
import com.vedeng.erp.kingdee.enums.KingDeeBaseEnums;
import com.vedeng.erp.kingdee.enums.KingDeeReceiveBillTypeEnum;
import com.vedeng.erp.kingdee.repository.KingDeePayVedengBankRepository;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeCustomerMapper;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeSupplierMapper;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 已结算收款银行流水推送金蝶Processor
 */
@Service
@Slf4j
public class ReceiptBankBillNewProcessor implements ItemProcessor<BatchBankBillDto, KingDeeReceiveBillDto> {

    @Autowired
    private BatchCapitalBillDtoMapper batchCapitalBillDtoMapper;
    @Resource
    private KingDeePayVedengBankRepository kingDeePayVedengBankRepository;
    @Autowired
    private BatchSupplierFinanceDtoMapper batchSupplierFinanceDtoMapper;
    @Autowired
    private BatchCustomerFinanceDtoMapper batchCustomerFinanceDtoMapper;
    @Autowired
    private BatchBankBillDtoMapper batchBankBillDtoMapper;
    @Autowired
    private BatchAfterSalesDtoMapper afterSalesMapper;
    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    /**
     * 交易类型
     */
    private static final String FQZOKJYLX = "收入";
    /**
     * 交易主体对公
     */
    private static final String FQZOKJYZT_PUBLIC = "对公";
    /**
     * 交易主体对私
     */
    private static final String FQZOKJYZT_PRIVACY = "对私";
    /**
     * 用途
     */
    private static final String FPURPOSEID = "SFKYT01_SYS";
    /**
     * 收款业务
     */
    private static final String FQZOKSKYW = "HL008";

    @Override
    public KingDeeReceiveBillDto process(BatchBankBillDto batchBankBillDto) throws Exception {
        log.info("已结算收款银行流水推送金蝶Processor,参数{}", JSON.toJSONString(batchBankBillDto));

        String fBillNo = KingDeeConstant.SETTLE_ACCOUNT + batchBankBillDto.getBankBillId();
        log.info("已结算收款银行流水推送金蝶Processor,fBillNo:{}", fBillNo);
        KingDeeReceiveBillDto queryDto = KingDeeReceiveBillDto.builder().fBillNo(fBillNo).build();
        if (kingDeeBaseApi.isExist(queryDto)) {
            log.info("已结算收款银行流水推送金蝶Processor,数据已推送金蝶:{}", JSON.toJSONString(batchBankBillDto));
            return null;
        }

        Integer orderType = batchBankBillDto.getOrderType();

        String orderNo = batchBankBillDto.getOrderNo();

        if (Objects.isNull(orderType) || StringUtils.isBlank(orderNo)) {
            log.error("已结算收款银行流水推送金蝶processor执行失败，订单类型或订单号为空：{}", JSON.toJSONString(batchBankBillDto));
            return null;
        }

        if (!KingDeeConstant.ONE.equals(orderType) && !KingDeeConstant.THREE.equals(orderType)) {
            log.error("已结算收款银行流水推送金蝶processor执行失败，订单类型不是销售订单或者售后订单：{}", JSON.toJSONString(batchBankBillDto));
            return null;
        }

        if (KingDeeConstant.THREE.equals(orderType)) {
            BatchAfterSalesDto afterSales = afterSalesMapper.findByAfterSalesNo(orderNo);
            if (Objects.isNull(afterSales)) {
                log.error("已结算收款银行流水推送金蝶processor执行失败，售后单不存在：{}", JSON.toJSONString(batchBankBillDto));
                return null;
            }
            if (AfterSalesProcessEnum.BUY_ORDER.getCode().equals(afterSales.getSubjectType())) {
                log.error("已结算收款银行流水推送金蝶processor执行失败，售后单为采购售后单：{}", JSON.toJSONString(afterSales));
                return null;
            }
        }

        KingDeeReceiveBillDto kingDeeReceiveBillDto = new KingDeeReceiveBillDto();

        kingDeeReceiveBillDto.setFId(ErpConstant.ZERO.toString());

        kingDeeReceiveBillDto.setFBillNo(KingDeeConstant.SETTLE_ACCOUNT + batchBankBillDto.getBankBillId());

        kingDeeReceiveBillDto.setFBillTypeId(KingDeeReceiveBillTypeEnum.SALE_RECEIVE.getCode());

        kingDeeReceiveBillDto.setFDate(DateUtil.formatDateTime(batchBankBillDto.getRealTrandatetime())); //
        kingDeeReceiveBillDto.setFQzokPzgsywdh(batchBankBillDto.getOrderNo());
        kingDeeReceiveBillDto.setFQzokLsh(batchBankBillDto.getTranFlow());

        kingDeeReceiveBillDto.setFContactUnitType(KingDeeFormConstant.BD_CUSTOMER);

        kingDeeReceiveBillDto.setFContactUnit(getContactUnitByTraderId(KingDeeFormConstant.BD_CUSTOMER, batchBankBillDto.getTraderId()));

        if (StringUtils.isBlank(kingDeeReceiveBillDto.getFContactUnit())) {
            log.error("已结算收款银行流水推送金蝶processor执行失败，客户在erp系统不存在：{}", JSON.toJSONString(batchBankBillDto));
            return null;
        }

        kingDeeReceiveBillDto.setFQzokJyzt(ErpConstant.ONE.equals(batchBankBillDto.getTraderSubject()) ? FQZOKJYZT_PUBLIC : FQZOKJYZT_PRIVACY);

        kingDeeReceiveBillDto.setFQzokJylx(FQZOKJYLX);

        kingDeeReceiveBillDto.setFQzokJyfs(KingDeeBankTagEnum.matchFQzokJyfsByCode(batchBankBillDto.getBankTag()));

        KingDeePayVedengBankDto kingDeePayVedengBankDto = queryBankInfo(batchBankBillDto.getBankTag());
        if(Objects.isNull(kingDeePayVedengBankDto)){
            log.error("已结算收款银行流水推送金蝶processor执行失败，银行信息不存在：{}", JSON.toJSONString(batchBankBillDto));
            return null;
        }
        // 查询银行流水关联的所有资金流水
        List<BatchCapitalBillDto> capitalBillDtoList = batchCapitalBillDtoMapper.queryByTraderTypeAndBankBillId(KingDeeConstant.ONE, batchBankBillDto.getBankBillId());
        if (CollUtil.isEmpty(capitalBillDtoList)) {
            log.error("已结算收款银行流水推送金蝶processor执行失败，银行流水关联的资金流水不存在：{}", JSON.toJSONString(batchBankBillDto));
            return null;
        }

        List<KingDeeReceiveBillEntryDto> kingDeeReceiveBillEntryDtos = new ArrayList<>();

        for (BatchCapitalBillDto batchCapitalBillDto : capitalBillDtoList) {
            KingDeeReceiveBillEntryDto kingDeeReceiveBillEntryDto = new KingDeeReceiveBillEntryDto();
            kingDeeReceiveBillEntryDto.setFSettleTypeId(KingDeeBankTagEnum.matchFSettleTypeIdByCode(batchBankBillDto.getBankTag()));
            kingDeeReceiveBillEntryDto.setFPurposeId(FPURPOSEID);
            kingDeeReceiveBillEntryDto.setFQzokSkyw(FQZOKSKYW);
            kingDeeReceiveBillEntryDto.setFAccountId(kingDeePayVedengBankDto.getPayBankNo());
            kingDeeReceiveBillEntryDto.setFRecTotalAmountFor(batchCapitalBillDto.getAmount().toString());
            kingDeeReceiveBillEntryDto.setFQzokYsddh("");
            kingDeeReceiveBillEntryDto.setFQzokGsywdh(batchCapitalBillDto.getOrderNo());
            kingDeeReceiveBillEntryDto.setFQzokYwlx(getFQzokYwlx(batchCapitalBillDto.getOrderType()));
            // 微信和支付宝需要将手续费加上
            //if (KingDeeConstant.FOUR.equals(batchBankBillDto.getBankTag()) || KingDeeConstant.FIVE.equals(batchBankBillDto.getBankTag())) {
            //    kingDeeReceiveBillEntryDto.setFHandlingChargeFor(getFee(batchBankBillDto));
            //}
            kingDeeReceiveBillEntryDto.setFHandlingChargeFor("0");

            kingDeeReceiveBillEntryDtos.add(kingDeeReceiveBillEntryDto);
        }

        kingDeeReceiveBillDto.setFReceiveBillEntry(kingDeeReceiveBillEntryDtos);

        return kingDeeReceiveBillDto;

    }

    private String getFee(BatchBankBillDto batchBankBillDto) {
        BatchBankBillDto fee = null;
        if (KingDeeConstant.FOUR.equals(batchBankBillDto.getBankTag()) && StringUtils.isNotBlank(batchBankBillDto.getCapitalSearchFlow())) {
            fee = batchBankBillDtoMapper.getAliFeeBankBillByCapitalSearchFlow(batchBankBillDto.getCapitalSearchFlow());
        }
        if (KingDeeConstant.FIVE.equals(batchBankBillDto.getBankTag())) {
            fee = batchBankBillDtoMapper.getWeChatFeeBankBillByTranFlow(batchBankBillDto.getTranFlow() + "_fee");
        }
        return Objects.isNull(fee) ? null : fee.getAmt().toPlainString();
    }

    /**
     * 根据traderId查询erp的客户和供应商的主键id
     */
    private String getContactUnitByTraderId(String contactUnitType, Integer traderId) {
        if (KingDeeFormConstant.BD_SUPPLIER.equals(contactUnitType)) {
            BatchSupplierFinanceDto supplierDto = batchSupplierFinanceDtoMapper.getSupplierIdByTraderId(traderId);
            if (Objects.isNull(supplierDto) || Objects.isNull(supplierDto.getTraderSupplierId())) {
                log.info("已结算收款银行流水推送金蝶processor执行失败，供应商在erp系统不存在");
                return null;
            }
            return supplierDto.getTraderSupplierId().toString();
        } else if (KingDeeFormConstant.BD_CUSTOMER.equals(contactUnitType)) {
            BatchCustomerFinanceDto customerDto = batchCustomerFinanceDtoMapper.getCustomerIdByTraderId(traderId);
            if (Objects.isNull(customerDto) || Objects.isNull(customerDto.getTraderCustomerId())) {
                log.info("已结算收款银行流水推送金蝶processor执行失败，客户在erp系统不存在");
                return null;
            }
            return customerDto.getTraderCustomerId().toString();
        } else {
            return null;
        }
    }

    /**
     * 根据流水类型查询银行信息
     * @param bankType
     * @return
     */
    private KingDeePayVedengBankDto queryBankInfo(Integer bankType){
        KingDeePayVedengBankDto kingDeePayVedengBankDto = new KingDeePayVedengBankDto();
        switch (bankType){
            case 1:
                //建设银行
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.SIX);
                break;
            case 2:
                //南京银行
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.TWO);
                break;
            case 3:
                //中国银行
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.ONE);
                break;
            case 4:
                //支付宝
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.FOUR);
                break;
            case 5:
                //微信
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.FIVE);
                break;
            case 6:
                //交通银行
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.THREE);
                break;
            case 7:
                //民生银行
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.SEVEN);
                break;
            default:
                log.info("银行流水类型未查对应银行编码");
                return null;
        }
        return kingDeePayVedengBankDto;
    }

    private String getFQzokYwlx(Integer orderType) {
        switch (orderType) {
            case 1:
                return KingDeeBaseEnums.SALES_COLLECTION.getName();
            case 3:
                return KingDeeBaseEnums.INSTALLATION_COLLECTION.getName();
            default:
                log.error("已结算收款银行流水推送金蝶processor执行失败，订单类型不是销售订单或者售后订单：{}", orderType);
                return "";
        }
    }

}
