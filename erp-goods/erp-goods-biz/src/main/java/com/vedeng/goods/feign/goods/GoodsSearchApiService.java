package com.vedeng.goods.feign.goods;


import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.common.feign.constants.ServerConstants;
import com.vedeng.search.api.dto.requestparam.ComplexQueryParam;
import com.vedeng.search.api.dto.responseparam.SearchResult;
import feign.Headers;
import feign.RequestLine;

/**
 * 商品搜索API接口
 *
 * <AUTHOR>
 */
@FeignApi(serverName = ServerConstants.GOODS_SEARCH_SERVER)
public interface GoodsSearchApiService {

    /**
     * 前台商品搜索接口
     *
     * @param complexQueryParam
     * @return
     */
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /search")
    RestfulResult<SearchResult> search(ComplexQueryParam complexQueryParam);
}

