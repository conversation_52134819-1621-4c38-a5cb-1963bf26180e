package com.vedeng.erp.system.web.api;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.common.core.annotation.ExcludeAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.utils.validator.ValidatorUtils;
import com.vedeng.common.core.utils.validator.group.AddGroup;
import com.vedeng.common.core.utils.validator.group.UpdateGroup;
import com.vedeng.erp.system.domain.dto.BankDto;
import com.vedeng.erp.system.service.BankService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 银行信息
 * @date 2022/8/15 15:26
 */
@ExceptionController
@RestController
@RequestMapping("/bank")
@Slf4j
public class BankApi {

    @Autowired
    private BankService bankService;

    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public R<?> add(@RequestBody BankDto bankDto) {
        ValidatorUtils.validate(bankDto, AddGroup.class);
        bankService.add(bankDto);
        return R.success();
    }

    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public R<?> update(@RequestBody BankDto bankDto) {
        ValidatorUtils.validate(bankDto, UpdateGroup.class);
        bankService.update(bankDto);
        return R.success();
    }


    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    public R<?> delete(@RequestParam Integer bankId) {
        bankService.delete(CollUtil.newHashSet(bankId));
        return R.success();
    }


    @RequestMapping(value = "/getBank", method = RequestMethod.POST)
    public R<BankDto> getBank(@RequestParam Integer bankId) {
        return R.success(bankService.getBank(bankId));
    }

    @RequestMapping(value = "/getBankByBankName", method = RequestMethod.POST)
    @ExcludeAuthorization
    public R<List<BankDto>> getBankByBankName(@RequestParam String bankName) {
        return R.success(bankService.getBankByBankName(bankName));
    }

    /**
     * 根据关键字搜索
     * @param keywords
     * @return
     */
    @RequestMapping(value = "/getBankByKeywords", method = RequestMethod.POST)
    @ExcludeAuthorization
    public R<List<BankDto>> getBankByKeywords(@RequestParam String keywords) {
        return R.success(bankService.getBankByKeywords(keywords));
    }
}
