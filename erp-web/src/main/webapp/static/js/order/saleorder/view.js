$(function(){
	$("#toCustomerInfo").hide();
	document.onkeyup = function(e){
	    if(!e){
	     e = window.event;
	    }
	    if((e.keyCode || e.which) == 13){
	   	 	return false;
	    }
	}


	//复制按钮 发送短信按钮展示 隐藏
		$.ajax({
			url:page_url+'/order/confirm/copyAndSendMessageButtonIsShow.do',
			data:{"saleorderId":$("input[name='saleorderId']").val()},
			type:"POST",
			dataType : "json",
			async: false,
			success:function(data){
				if(data.data.copyButton ){
					$("#copyLink").show();
				}else{
					$("#copyLink").hide();
				}
				if(data.data.sendMesButton){
					$("#sendMessage").show();
				}else{
					$("#sendMessage").hide();
				}
			},
			error:function(data) {
				if (data.status == 1001) {
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});


	//动态加载客户签收记录
	$.ajax({
		url:page_url+'/order/confirm/customerSignature.do',
		data:{"saleorderId":$("input[name='saleorderId']").val()},
		type:"POST",
		dataType : "json",
		async: false,
		success:function(data)
		{
			if(data.code==0){
					let confrimList=data.data;
					if(confrimList&& confrimList.length!=0){
					for(var i=0;i<confrimList.length;i++){
						var tr=document.createElement("tr");
						var tbody =$("#customerSignature");
						tbody.append (tr);

						var td1=document.createElement("td");
						tr.appendChild(td1);
						td1.innerHTML=i+1;

						var td2=document.createElement("td");
						tr.appendChild(td2);
						td2.innerHTML=confrimList[i].goodsName+"<br>"+confrimList[i].sku;

						var td3=document.createElement("td");
						tr.appendChild(td3);
						td3.innerHTML=confrimList[i].confirmNumber;

						var td4=document.createElement("td");
						tr.appendChild(td4);

						if(confrimList[i].sendTime){
							td4.innerHTML=formatDate(new Date(confrimList[i].sendTime));
						}else{
							td4.innerHTML="";
						}

					}
				}else{
					var tbody =$("#customerSignature");
					tbody.append ('<tr><td colspan="4" >查询无结果！</td></tr>');
				}
			}
		},
		error:function(data) {
			if (data.status == 1001) {
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});
	//动态加载订单确认模块
	$.ajax({
		url:page_url+'/order/confirm/getConfirmRecord.do',
		data:{"saleorderId":$("input[name='saleorderId']").val()},
		type:"POST",
		dataType : "json",
		async: false,
		success:function(data) {
			if(data.code==0){
				//确认时间
				if(data.data.confirmTime!=null && data.data.confirmTime!=""){
					var date = formatDate(new Date(data.data.confirmTime))   ;
					$("#confirmTime").html(date)
				}

				if(data.data.confirmRecordList!=null && data.data.confirmRecordList!=""){
					let confirmRecordList = data.data.confirmRecordList;
					for(let i=0;i<confirmRecordList.length;i++){
						var tr=document.createElement("tr");
						var tbody =$("#orderConfirm");
						tbody.append (tr);


							var td1=document.createElement("td");
							tr.appendChild(td1);
							td1.innerHTML=confirmRecordList[i].userName;

							var td2=document.createElement("td");
							tr.appendChild(td2);

							if(confirmRecordList[i].sendTime){
								td2.innerHTML=formatDate(new Date(confirmRecordList[i].sendTime));
							}else{
								td2.innerHTML="";
							}


							var td3=document.createElement("td");
							tr.appendChild(td3);
							if(confirmRecordList[i].sendType ==0 ){
								td3.innerHTML="系统发送";
							}else if(confirmRecordList[i].sendType ==1){
								td3.innerHTML="手动发送";
							}

					}

				}else{
					var tbody =$("#orderConfirm");
					tbody.append ('<tr><td colspan="3" >查询无结果！</td></tr>');
				}


			}else{

			}

		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});

	//点击复制按钮
	$("#copyLink").click(function() {
			$.ajax({
				url:page_url+'/order/confirm/copyUrlLink.do',
				data:{"saleorderId":$("input[name='saleorderId']").val()},
				type:"POST",
				dataType : "json",
				async: false,
				success:function(data){
					if (data.code == 0) {
						$("#copyUrl").val(data.data)
						var Url2=document.getElementById("copyUrl");
						Url2.style.display="block"
						Url2.select(); // 选择对象
						document.execCommand("Copy"); // 执行浏览器复制命令
						Url2.style.display="none"
					}

					layer.alert(data.message);

				},
				error:function(data) {
					if (data.status == 1001) {
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
	});


	//发送短信
	$("#sendMessage").click(function() {
		layer.confirm("确定发送订单确认短信给客户？", {
				btn: ['确定', '取消'] //按钮
			},function () {
			$.ajax({
				url:page_url+'/order/confirm/sendMessage.do',
				data:{"saleorderId":$("input[name='saleorderId']").val()},
				type:"POST",
				dataType : "json",
				async: false,
				success:function(data){
					layer.alert(data.message);
					if(data.code==0){
						//刷新当前页面
						location.reload();
					}
				},
				error:function(data) {
					if (data.status == 1001) {
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
		},
			function () {
			layer.close()
		});




	});
});

function isTraderAllowInvoice(traderId,traderName,invoiceType,type,saleorderId) {
	checkLogin();
	var url=page_url+"/trader/customer/isTraderAllowInvoice.do"
	$.ajax({
		type: "POST",
		url: url,
		data: {'traderId':traderId,'traderName':traderName,'invoiceType':invoiceType,'saleorderId':saleorderId},
		dataType:'json',
		success: function(data){
			console.log(data.data);
			if(data.code != 0){
				layer.alert(data.message);
				return;
			}
			if(data.data.passCredential && data.data.passPeerCheck && data.data.passRuleCheck){
				console.log("全部通过");
				$("#invoiceApply").click();
				return;
			}else if (!data.data.passCredential){
				layer.open({
					title: '财务资质信息',
					type: 2,
					area: ['600px', '350px'],
					shadeClose: true,
					content: '/invoice/invoiceApply/invoiceApplyCheckCustomer.do?traderId='+traderId+'&invoiceType='+invoiceType,
					success: function(layer, index){

					}
				});
			}else if (!data.data.passPeerCheck){
					layer.confirm("订单满足票货同行，手动申请后将不在执行票货同行，是否继续？",
						{btn: ['是','否']},
						function(index){

							$.ajax({
								url:page_url+'/trader/customer/ruleCheck.do',
								type:"POST",
								data: {'saleorderId':saleorderId},
								dataType : "json",
								success:function(data) {
									if(data.code == 0){
										$("#invoiceApply").click();
										layer.close(index);
										return
									}
									if(data.code == -1){
										checkRuleConfirm(saleorderId);
										layer.close(index);
										return
									}
									alert(data.message)
								},
								error:function(data){
									if(data.status ==1001){
										layer.alert("当前操作无权限")
									}
								}
							});
							layer.close(index);
							return
						}, function(index){
							layer.close(index);
							return
						});
			}else if (!data.data.passRuleCheck){
				checkRuleConfirm(saleorderId);
			}

		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				return;
			}
			layer.alert("验证客户能否开票失败")
		}
	});
}

function checkRuleConfirm(saleorderId){
	layer.open({
		title: '申请检查',
		type: 2,
		area: ['600px', '350px'],
		shadeClose: true,
		content: '/invoice/invoiceApply/checkCheckRule.do?saleorderId='+saleorderId,
		btn: ['是', '否'],
		yes: function(index, layero){
			$("#invoiceApply").click();
		},
		btn2: function(index, layero){
			layer.close(index);
			return
		}
	});
}


function closeSaleorder(saleorderId, bussinessChanceNo) {
	let titleStr = "订单被关闭后无法修改和跟进，是否确认？";
	if (bussinessChanceNo && bussinessChanceNo !== '') {
		titleStr = "已关联商机" + bussinessChanceNo + "，操作后会同步关闭商机。";
	}
	checkLogin();
	layer.confirm(titleStr, {
		  btn: ['确定','取消'] //按钮
		}, function(){
			$.ajax({
				type: "POST",
				url: "/order/saleorder/closeSaleorder.do",
				data: {'saleorderId':saleorderId,'status':3},
				dataType:'json',
				success: function(data){
					if (data.code == 0) {
						window.location.reload();
					} else {
						layer.alert(data.message);
					}
					/*layer.alert(data.message,{
						closeBtn: 0,
						btn: ['确定'] //按钮
					}, function(){
						window.location.reload();
					});*/

				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
		}, function(){
	});
}

function operationCheck(saleorderId){
	var taskName=$("#taskName").val();
	if(taskName=='质量管理部审核' || taskName == '运营部审核') {
		$.ajax({
			type: "POST",
			url: "/order/saleorder/getOrderTraderAptitudeStatus.do",
			data: {'saleorderId': saleorderId},
			dataType: 'json',
			success: function (data) {
				if (data.code == 0) {
					var customer = data.data;
					if (customer.aptitudeStatus == null || customer.aptitudeStatus == 3 || customer.aptitudeStatus == 2) {
						var index1 = layer.confirm("客户资质尚未提交审核，确认审核通过订单吗？", {
							btn: ['确定', '取消'] //按钮
						}, function () {
							layer.close(index1)
							$("#orderComplement").click();
						}, function () {
							layer.close(index1)
						});

					} else if (customer.aptitudeStatus == 0) {
						var index1 = layer.confirm("客户资质还在审核中，确认审核通过订单吗？", {
							btn: ['确定', '取消'] //按钮
						}, function () {
							layer.close(index1)
							$("#orderComplement").click();
						}, function () {
							layer.close(index1)
						});
					} else {
						$("#orderComplement").click();
					}
				} else {
					layer.alert(data.message);
				}
			},
			error: function (data) {
				if (data.status == 1001) {
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	}else {
		$("#orderComplement").click();
	}
}

function editApplyValidSaleorder(saleorderId,taskId,formToken) {
	$.ajax({
		type: "POST",
		url: "/order/saleorder/editApplyValidSaleorder.do",
		data: {'saleorderId':saleorderId,'taskId':taskId,'formToken':formToken},
		dataType:'json',
		success: function(data){
			if (data.code == 0) {
				window.location.reload();
			} else {
				layer.alert(data.message);
			}
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});
}

function applySaleorder(saleorderId,taskId,isZeroPrice,type) {
	var formToken = $("input[name='formToken']").val();
	var _self=self;
	$.ajax({
		url: "/order/saleorder/checkSaleorder.do",
		type: "post",
		data: {'saleorderId':saleorderId},
		dataType:'json',
		success: function(data){
			if (data.code == 0) {
				var msg = "";
				if(isZeroPrice == 1){
					msg = "有产品价格为0,是否确认操作";
				}else{
					msg = "您是否确认申请审核该订单？";
				}
				if(type==1) {
					layer.confirm(msg, {
						btn: ['确定', '取消'] //按钮
					}, function () {
						editApplyValidSaleorder(saleorderId, taskId, formToken);
					}, function () {
					});
				}else{
					editApplyValidSaleorder(saleorderId, taskId, formToken);
				}
			} else {
				var riskmodel = data.data;
				if(riskmodel != null && riskmodel.isRisk != null && data.code == -2){
					layer.confirm(data.message,{
							btn: ['确认']
					},function () {
							if(riskmodel.isRedirect == 1){
								var frontPageId = $(window.parent.parent.document).find('.active').eq(1).attr('id');
								var url=riskmodel.url;
								var item = { 'name':"客户信息", 'url': url, 'closable': true };
								// _self.parent.parent.closableTab.addTab(item);
								// _self.parent.parent.closableTab.resizeMove();
								// $(window.parent.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
								if (typeof(_self.parent.parent.closableTab) != 'undefined') {
									_self.parent.parent.closableTab.addTab(item);
									_self.parent.parent.closableTab.resizeMove();
									$(window.parent.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
								}else{
									try{
										var uniqueName = url.replace('./', '').replace(/["&'./:=%?[\]]/gi, '-').replace(/(--)/gi, '');
										window.parent.parent.postMessage({
											from:'ez',
											name: title,
											url:url,
											id:"tab-"+uniqueName
										}, '*');
									}catch (e){}
								}
							}
						window.location.reload();
						}
					);
					return false;
				} else if (data.code === -3) {

					var period_html = '<div class="checkBillPeriodForOrder"> <br/>' +
						'<p style="color:red">当前客户账期额度不足，请至客户详情/财务与资质信息中申请 ! </p> ' +
						'<br/>' +
						'<table class="table table-bordered table-striped table-condensed table-centered">\n' +
						'    <thead>\n' +
						'    <tr>\n' +
						'        <th>账期类型</th>\n' +
						'        <th>可用额度（元）</th>\n' +
						'        <th>结算标准</th>\n' +
						'        <th>结算周期（天）</th>\n' +
						'    </tr>\n' +
						'    </thead>\n' +
						'    <tbody>\n' +
						' ';
					var responseData = data.data.allBillPeriod;
					for (let i = 0; i < responseData.length; i++) {
						if (responseData[i].billPeriodType === 1) {
							period_html += "<tr> <td>正式账期</td>";
						} else if (responseData[i].billPeriodType === 2) {
							period_html += "<tr> <td>临时账期</td>";
						} else if (responseData[i].billPeriodType === 3) {
							period_html += "<tr> <td>订单账期</td>";
						} else {
							period_html += "<tr> <td></td> "
						}
						period_html += "<td>" + responseData[i].availableAmount + "</td>";
						if (responseData[i].billPeriodSettlementType === 1) {
							period_html += "<td>订单发货</td>";
						} else if (responseData[i].billPeriodSettlementType === 2) {
							period_html += "<td>订单开票</td>";
						} else {
							period_html += "<td></td> "
						}
						period_html += "<td>" + responseData[i].settlementPeriod + "</td> </tr>";
					}

					period_html += '</tbody></table></div>'

					layer.open({
						type: 1,
						id: 'checkBillPeriodForOrder',
						title: '温馨提示:',
						area: ['460px', '250px'],
						resize: false,
						btn: ['前往', '取消'],
						content: period_html,
						yes: function (index, layero) {
							// 请至客户详情/财务与资质信息中申请 跳转
							layer.close(index);
							$("#toCustomerInfo").attr("tabtitle",'{"num":"viewcustomer'+data.data.traderCustomerId+'","link":"/trader/customer/getFinanceAndAptitude.do?traderId='+data.data.traderId+'&traderCustomerId='+data.data.traderCustomerId+'","title":"客户信息"}');
							$("#toCustomerInfo").click();
							// window.location.href = page_url+"/trader/customer/getFinanceAndAptitude.do?traderId="+data.data.traderId +"&traderCustomerId="+data.data.traderCustomerId;

						},

					});

					return false;

				} else {
					layer.alert(data.message);
					return false;
				}
			}

		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});
}
function applyValidSaleorder(saleorderId,taskId,isZeroPrice){
	checkLogin();
	// 校验产品是否配置内部备注标签
	let specialSkuList = getSpecialGoodsList();
	let remarkError = 0
	$('#goodsTbody').children('tr').each(function(i, v){
		if (i === $('#goodsTbody').children('tr') - 1) {
			return false;
		}
		$(v).children('td').each(function(_i, _v){
			if ($(_v).hasClass('c-comments') && !$(_v).children('.customername').children('.customernameshow').html()) {
				let skuId = parseInt($(_v).attr('skuId'));
				let skuList = specialSkuList.filter(function (sku) {
					return skuId === sku;
				})
				if (skuList.length > 0) {
					return false;
				}
				remarkError++;
				$(_v).children('.no_remark_error').css('display','block');
			}
		})
	})
	if (remarkError > 0) {
		return false;
	}
	var _self=self;
	$.ajax({
		type: "POST",
		url: "/order/saleorder/getOrderTraderAptitudeStatus.do",
		data: {'saleorderId':saleorderId},
		dataType:'json',
		success: function(data){
			if (data.code == 0) {
				var customer=data.data;
				if(customer.aptitudeStatus==null||customer.aptitudeStatus==3||customer.aptitudeStatus==2){
					layer.confirm("客户资质尚未提交审核，请前往维护", {
						btn: ['继续提交','去维护'] //按钮
					}, function(){
						applySaleorder(saleorderId,taskId,isZeroPrice,2)
					}, function(){
						window.location.reload();
						var frontPageId = $(window.parent.parent.document).find('.active').eq(1).attr('id');
						var url=page_url+"/trader/customer/getFinanceAndAptitude.do?traderId="+customer.traderId+"&traderCustomerId="+customer.traderCustomerId;
						var item = { 'id': customer.traderCustomerId, 'name':"财务与资质信息", 'url': url, 'closable': true };
						// _self.parent.parent.closableTab.addTab(item);
						// _self.parent.parent.closableTab.resizeMove();
						// $(window.parent.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
						if (typeof(_self.parent.parent.closableTab) != 'undefined') {
							_self.parent.parent.closableTab.addTab(item);
							_self.parent.parent.closableTab.resizeMove();
							$(window.parent.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
						}else{
							try{
								var uniqueName = url.replace('./', '').replace(/["&'./:=%?[\]]/gi, '-').replace(/(--)/gi, '');
								window.parent.parent.postMessage({
									from:'ez',
									name: title,
									url:url,
									id:"tab-"+uniqueName
								}, '*');
							}catch (e){}
						}
					});

				}else{
					applySaleorder(saleorderId,taskId,isZeroPrice,1)
				}
			} else {
				layer.alert(data.message);
			}
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});
}


function validSaleorder(saleorderId){
	checkLogin();
	layer.confirm("您是否确认生效该订单？", {
		  btn: ['确定','取消'] //按钮
		}, function(){
			var isNotDelPriceZero = $("#isNotDelPriceZero").val();
			if (isNotDelPriceZero == 1) {
				layer.confirm("订单中包含单价为0的产品，是否确认生效？", {
					  btn: ['确定','取消'] //按钮
					},function(){
						$.ajax({
							type: "POST",
							url: "/order/saleorder/validSaleorder.do",
							data: {'saleorderId':saleorderId,'validStatus':1},
							dataType:'json',
							success: function(data){
								/*layer.alert(data.message,{
									closeBtn: 0,
									btn: ['确定'] //按钮
								}, function(){
									window.location.reload();
								});*/
								if (data.code == 0) {
									window.location.reload();
								} else {
									layer.alert(data.message);
								}
							},
							error:function(data){
								if(data.status ==1001){
									layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
								}
							}
						});
					}, function(){
					});
			} else {
				$.ajax({
					type: "POST",
					url: "/order/saleorder/validSaleorder.do",
					data: {'saleorderId':saleorderId,'validStatus':1},
					dataType:'json',
					success: function(data){
						/*layer.alert(data.message,{
							closeBtn: 0,
							btn: ['确定'] //按钮
						}, function(){
							window.location.reload();
						});*/
						if (data.code == 0) {
							window.location.reload();
						} else {
							layer.alert(data.message);
						}
					},
					error:function(data){
						if(data.status ==1001){
							layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
						}
					}
				});
			}
		}, function(){
	});
}

function noValidSaleorder(saleorderId){
	checkLogin();
	layer.confirm("您是否确认撤销该订单生效状态？", {
		  btn: ['确定','取消'] //按钮
		}, function(){
			$.ajax({
				type: "POST",
				url: "/order/saleorder/noValidSaleorder.do",
				data: {'saleorderId':saleorderId,'validStatus':0},
				dataType:'json',
				success: function(data){
					/*layer.alert(data.message,{
						closeBtn: 0,
						btn: ['确定'] //按钮
					}, function(){
						window.location.reload();
					});*/
					window.location.reload();
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
		}, function(){
	});
}
function returnBDStatus(saleorderId) {
//	checkLogin();
	layer.confirm("您是否确认撤销该订单待用户确认状态？", {
		  btn: ['确定','取消'] //按钮
		}, function(){
			$.ajax({
				type: "GET",
				url: "/order/saleorder/returnBDStatus.do",
				data: {'saleorderId':saleorderId},
				dataType:'json',
				success: function(data){
					/*layer.alert(data.message,{
						closeBtn: 0,
						btn: ['确定'] //按钮
					}, function(){
						window.location.reload();
					});*/
					window.location.reload();
				},
				error:function(data){
//					if(data.status ==1001){
//						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
//					}
				}
			});
		}, function(){
	});
}

function contractReturnDelNew(attachmentId){
	checkLogin();
	var formToken = $("input[name='formToken']").val();
	layer.confirm("确认删除该条合同回传吗？", {
		btn: ['确定','取消'] //按钮
	}, function(){
		$.ajax({
			type: "POST",
			url: "/order/saleorder/contractReturnDelNew.do",
			data: {'attachmentId':attachmentId,'formToken':formToken},
			dataType:'json',
			success: function(data){
				if (data.code == -1) {
					layer.alert(data.message);
				} else {
					window.location.reload();
				}
				/*
                layer.alert(data.message,{
                    closeBtn: 0,
                    btn: ['确定'] //按钮
                }, function(){
                    window.location.reload();
                });*/
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	}, function(){
	});
}
function contractReturnDel(attachmentId){
	checkLogin();
	var formToken = $("input[name='formToken']").val();
	layer.confirm("确认删除该条合同回传吗？", {
		  btn: ['确定','取消'] //按钮
		}, function(){
			$.ajax({
				type: "POST",
				url: "/order/saleorder/contractReturnDel.do",
				data: {'attachmentId':attachmentId,'formToken':formToken},
				dataType:'json',
				success: function(data){
					if (data.code == -1) {
						layer.alert(data.message);
					} else {
						window.location.reload();
					}
					/*
					layer.alert(data.message,{
						closeBtn: 0,
						btn: ['确定'] //按钮
					}, function(){
						window.location.reload();
					});*/
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
		}, function(){
	});
}
function requestCheck(saleorderId,taskId){
	checkLogin();
	var alertCount=$("#alertCount").val();
	if(alertCount==0){
		layer.alert("如有合同审核不合格，请先删除不合格合同，重新上传后申请审核！");
		$("#alertCount").val(Number(alertCount)+1);
	}else{
		layer.confirm("您是否确认申请审核合同？", {
			  btn: ['确定','取消'] //按钮
			}, function(){
				//提交审核方法
				var formToken = $("input[name='formToken']").val();
				$.ajax({
					type: "POST",
					url: "/order/saleorder/editApplyValidContractReturn.do",
					data: {'saleorderId':saleorderId,'taskId':taskId,'formToken':formToken},
					dataType:'json',
					success: function(data){
						if (data.code == 0) {
							window.location.reload();
						} else {
							layer.alert(data.message);
						}
						
					},
					error:function(data){
						if(data.status ==1001){
							layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
						}
					}
				});
			}, function(){
		});
		
	}
}
function deliveryOrderReturnDel(attachmentId){
	checkLogin();
	var formToken = $("input[name='formToken']").val();
	layer.confirm("确认删除该条送货单回传吗？", {
		  btn: ['确定','取消'] //按钮
		}, function(){
			$.ajax({
				type: "POST",
				url: "/order/saleorder/deliveryOrderReturnDel.do",
				data: {'attachmentId':attachmentId,'formToken':formToken},
				dataType:'json',
				success: function(data){
					if (data.code == -1) {
						layer.alert(data.message);
					} else {
						window.location.reload();
					}
					/*
					layer.alert(data.message,{
						closeBtn: 0,
						btn: ['确定'] //按钮
					}, function(){
						window.location.reload();
					});*/
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
		}, function(){
	});
}

function syncNewTraderName(saleorderId,invoiceStatus,traderId) {
	checkLogin();
	if(invoiceStatus==1||invoiceStatus==2){
		layer.alert("已开票的订单，无法更新客户名称")
		return;
	}
	var data={'saleorderId':saleorderId,'traderId':traderId};
	var url=page_url+"/order/saleorder/syncNewTraderName.do"
	var index=layer.confirm('确认更新订单中的客户名称吗？', {
		btn: ['确定','取消'] //按钮
	}, function(){
		$.ajax({
			type: "POST",
			url: url,
			data: data,
			dataType:'json',
			success: function(data){
				if (data.code == 0) {
					window.location.reload();
				} else {
					layer.alert(data.message);
					layer.close(index)
				}

			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	}, function(){
		layer.close(index);
	});
}

function vailNum(obj){
	checkLogin();
	if($(obj).val().length>14){
		layer.alert("价格长度最大不允许14个字符。", {
			icon : 2
		}, function(index) {
			$(obj).addClass("errorbor");
			layer.close(index);
			$(obj).focus();
		});
		return false;
	}
	var isNum = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;
	if($(obj).val() != undefined && $(obj).val()!="" && !isNum.test($(obj).val())){
		if($(obj).val() == '0'){
			layer.alert("参考成本不能为0或者为空", {
				icon : 2
			}, function(index) {
				$(obj).addClass("errorbor");
				layer.close(index);
				$(obj).focus();
			});
		}else{
			layer.alert("价格输入格式错误！仅允许使用数字，最多精确到小数点后两位。", {
				icon : 2
			}, function(index) {
				$(obj).addClass("errorbor");
				layer.close(index);
				$(obj).focus();
			});
		}
		return false;
	}else{
		$(obj).removeClass("errorbor");
		return true;
	}
}


function vailNumCost(obj){
	checkLogin();
	if($(obj).val().length>14){
		layer.alert("价格长度最大不允许14个字符。", {
			icon : 2
		}, function(index) {
			$(obj).addClass("errorbor");
			layer.close(index);
			$(obj).focus();
		});
		return false;
	}
	var isNum = /^(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;
	if($(obj).val() != undefined && $(obj).val()!="" && !isNum.test($(obj).val())){
		if($(obj).val() == '0'){
			layer.alert("参考成本不能为0或者为空", {
				icon : 2
			}, function(index) {
				$(obj).addClass("errorbor");
				layer.close(index);
				$(obj).focus();
			});
		}else{
			layer.alert("价格输入格式错误！仅允许使用数字，最多精确到小数点后两位。", {
				icon : 2
			}, function(index) {
				$(obj).addClass("errorbor");
				layer.close(index);
				$(obj).focus();
			});
		}
		return false;
	}else{
		$(obj).removeClass("errorbor");
		return true;
	}
}





function editReferenceCostPrice(saleorderGoodsId,obj){
	var referenceCostPrice = $(obj).val();
	var saleorderGoodsIdArr = new Array();
	saleorderGoodsIdArr[saleorderGoodsIdArr.length] = saleorderGoodsId;
	var referenceCostPriceArr = new Array();
	referenceCostPriceArr[referenceCostPriceArr.length] = $(obj).val();
	var saleorderId = $("input[name='saleorderId']").val();
	//修改销售订单中对应产品的参考成本
    
	$.ajax({
		async : false,
		url : '/order/saleorder/saveReferenceCostPrice.do',
		data : {"referenceCostPriceArr":JSON.stringify(referenceCostPriceArr),"saleorderGoodsIdArr":JSON.stringify(saleorderGoodsIdArr),"saleorderId":saleorderId},
		type : "POST",
		dataType : "json",
		success : function(data) {
			
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	})
}

function checkCostPrice(obj){
		var saleorderId = $("input[name='saleorderId']").val();
		//判断归属当前登陆人的产品的成本价有没有填写
		var flag = true;
		//是否有为0的的参考成本
		var isZero = false;
		$("input[goodscategoryuser^='y']").each(function(i){
				if($(this).val() == '0.00' || $(this).val() == '0'|| $(this).val() == ""){
					isZero = true;
				}
		});
		var updateStr = "";
		//循环验证哪一行数据有改动
		$("input[name^='referenceCostPrice_']").each(function(i){
			//if($(this).val() != $(this).attr("alt")){
				updateStr += i;
			//}
		});
		//参考成本
		var referenceCostPriceArr = new Array();
		$("input[name^='referenceCostPrice_']").each(function(i){
			if(updateStr.indexOf(i) != -1){
				referenceCostPriceArr[referenceCostPriceArr.length] = $(this).val();
				if(!vailNumCost(this)){
					flag = false;
					return flag;
				}
			}
		});
		if(flag==false){
			return false;
		}
		var saleorderGoodsIdArr = new Array();
		$("input[name='saleorderGoodsId']").each(function(i){
			if(updateStr.indexOf(i) != -1){
				saleorderGoodsIdArr[saleorderGoodsIdArr.length] = $(this).val();
			}
		});
	    
	    //修改销售订单中对应产品的参考成本
	    if(isZero==false){
	    	$.ajax({
	    		async : false,
	    		url : '/order/saleorder/saveReferenceCostPrice.do',
	    		data : {"referenceCostPriceArr":JSON.stringify(referenceCostPriceArr),"saleorderGoodsIdArr":JSON.stringify(saleorderGoodsIdArr),"saleorderId":saleorderId},
	    		type : "POST",
	    		dataType : "json",
	    		success : function(data) {
	    			layer.alert("提交成功");
	    			window.location.reload();
	    		},
	    		error:function(data){
	    			if(data.status ==1001){
	    				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
	    			}
	    		}
	    	})
	    }else{
	    	index = layer.confirm("当前参考成本为0或者为空，若跳过</br>请务必在本月底填写完整", {
	    		btn: ['确定','取消'] //按钮
	    	}, function(){
	    		layer.close(index);
	    		$.ajax({
		    		async : false,
		    		url : '/order/saleorder/saveReferenceCostPrice.do',
		    		data : {"referenceCostPriceArr":JSON.stringify(referenceCostPriceArr),"saleorderGoodsIdArr":JSON.stringify(saleorderGoodsIdArr),"saleorderId":saleorderId},
		    		type : "POST",
		    		dataType : "json",
		    		success : function(data) {
		    			layer.alert("提交成功");
		    			window.location.reload();
		    		},
		    		error:function(data){
		    			if(data.status ==1001){
		    				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
		    			}
		    		}
		    	})
	    	});
	    }
	    
		return  false;
}

function saveCostPrice(obj){
	var saleorderId = $("input[name='saleorderId']").val();
	//判断归属当前登陆人的产品的成本价有没有填写
	var flag = true;
//	$("input[goodscategoryuser^='y']").each(function(i){
//			if($(this).val() == '0.00' || $(this).val() == ""){
//				layer.alert("参考成本不能为0或者为空", {
//					icon : 2
//				}, function(index) {
//					$(this).addClass("errorbor");
//					layer.close(index);
//					$(this).focus();
//				});
//				flag = false;
//			}
//	});
	var updateStr = "";
	//循环验证哪一行数据有改动
	$("input[name^='referenceCostPrice_']").each(function(i){
		if($(this).val() != $(this).attr("alt")){
			updateStr += i;
		}
	});
	//参考成本
	var referenceCostPriceArr = new Array();
	$("input[name^='referenceCostPrice_']").each(function(i){
		if(updateStr.indexOf(i) != -1){
			referenceCostPriceArr[referenceCostPriceArr.length] = $(this).val();
			if(!vailNumCost(this)){
				flag = false;
				return flag;
			}
		}
	});
	if(flag==false){
		return false;
	}
	var saleorderGoodsIdArr = new Array();
	$("input[name='saleorderGoodsId']").each(function(i){
		if(updateStr.indexOf(i) != -1){
			saleorderGoodsIdArr[saleorderGoodsIdArr.length] = $(this).val();
		}
	});
    //修改销售订单中对应产品的参考成本
	$.ajax({
		async : false,
		url : '/order/saleorder/saveReferenceCostPrice.do',
		data : {"referenceCostPriceArr":JSON.stringify(referenceCostPriceArr),"saleorderGoodsIdArr":JSON.stringify(saleorderGoodsIdArr),"saleorderId":saleorderId},
		type : "POST",
		dataType : "json",
		success : function(data) {
			layer.alert("提交成功");
			window.location.reload();
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	})
    
	return  false;
}

function confirmMsg(saleorderId){
	checkLogin();
	index = layer.confirm("销售直发订单退货售后前请确认收货，是否继续？", {
		btn: ['确定','取消'] //按钮
	}, function(){
		var timestamp = Date.parse(new Date());
		var	url = page_url+"/order/aftersalesUpgrade/addAfterSalesPage.do?flag=th&&saleorderId="+saleorderId;
		$("#addAfter").attr('tabTitle','{"num":"view'+timestamp+'","title":"新增售后","link":"'+url+'"}');
		$("#addAfter").click();
		layer.close(index);
	});
}

function confirmMsgNew(saleorderId){
	checkLogin();
	index = layer.confirm("销售直发订单退货售后前请确认收货，是否继续？", {
		btn: ['确定','取消'] //按钮
	}, function(){
		var timestamp = Date.parse(new Date());
		var	url = page_url+"/order/aftersalesUpgrade/addAfterSalesPage.do?flag=th&&saleorderId="+saleorderId;
		$("#addAfter").attr('tabTitle','{"num":"view'+timestamp+'","title":"新增售后","link":"'+url+'"}');
		$("#addAfter").click();
		layer.close(index);
	});
}

function batchDownEInvoice(){
	checkLogin();
	var div = '<div class="tip-loadingNewData" style="position:fixed;width:100%;height:100%; z-index:100; background:rgba(0,0,0,0.3)"><i class="iconloadingblue" style="position:absolute;left:50%;top:32%;"></i></div>';
	$("body").prepend(div); //jq获取上一页框架的父级框架；
	$.ajax({
		type: "POST",
		url: page_url + "/finance/invoice/batchDownEInvoice.do",
		dataType:'json',
		success: function(data){
			$(".tip-loadingNewData").remove();
			window.location.reload();
			//refreshNowPageList(data);//刷新列表数据
		},
		error:function(data){
			$(".tip-loadingNewData").remove();
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});
}

function sendMeinianExpress(expressId,logisticsNo){
	checkLogin();
	layer.confirm("确认供货单同步给美年？", {
		  btn: ['确定','取消'] //按钮
		}, function(){
			$.ajax({
				type: "POST",
				url: "/order/saleorder/sendMeinianExpress.do",
				data: {'expressId':expressId,'logisticsNo':logisticsNo},
				dataType:'json',
				success: function(data){
					layer.alert(data.message);
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
					}
				}
			});
		}, function(){
	});
}

function deleteConfirmationById(confirmationId){
	checkLogin();
	layer.confirm("若此确认单涉及多个批次和快递，选择删除后，将删除此确认单下所有批次关联关系以及审核信息，是否全部删除。", {
		btn: ['确定','取消'] //按钮
	}, function(){
		$.ajax({
			type: "POST",
			url: "/order/saleorder/deleteConfirmationById.do",
			data: {'confirmationId':confirmationId},
			dataType:'json',
			success: function(data){
				layer.alert(data.message);
				window.location.reload();
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
				}
			}
		});
	}, function(){
	});
}

function associativeRecording(expressId){
	checkLogin();
	layer.open({
		type: 2,
		title: '关联录音',
		shadeClose: true,
		area: ['400px', '500px'],
		content: page_url+'/saleCall/addCommunicateRecorderIds.do?expressId='+expressId
	});
}

//时间戳转时间
function formatDate(datetime) {
	var year = datetime.getFullYear();
	var month = datetime.getMonth() + 1;
	var date = datetime.getDate();
	var hour = datetime.getHours();
	if(hour<=9){
		hour="0"+hour;
	}
	var minute = datetime.getMinutes();
	if(minute<=9){
		minute="0"+minute;
	}
	var second = datetime.getSeconds();
	if(second<=9){
		second="0"+second;
	}
	return year + "-" + month + "-" + date+" "+hour+":"+minute+":"+second;//+"."+mseconds;

}



