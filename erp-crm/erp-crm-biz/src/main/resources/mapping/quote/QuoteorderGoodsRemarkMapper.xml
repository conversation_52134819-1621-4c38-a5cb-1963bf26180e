<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.crm.business.quote.mapper.QuoteorderGoodsRemarkMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.crm.business.quote.domain.entity.QuoteorderGoodsRemarkEntity">
    <!--@mbg.generated-->
    <!--@Table T_QUOTEORDER_GOODS_REMARK-->
    <id column="QUOTEORDER_GOODS_REMARK_ID" jdbcType="BIGINT" property="quoteorderGoodsRemarkId" />
    <result column="QUOTEORDER_GOODS_ID" jdbcType="INTEGER" property="quoteorderGoodsId" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    QUOTEORDER_GOODS_REMARK_ID, QUOTEORDER_GOODS_ID, REMARK, IS_DELETE, ADD_TIME, MOD_TIME,
    CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_QUOTEORDER_GOODS_REMARK
    where QUOTEORDER_GOODS_REMARK_ID = #{quoteorderGoodsRemarkId,jdbcType=BIGINT}
  </select>

  <select id="selectByQuoteGoodsId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_QUOTEORDER_GOODS_REMARK
    where QUOTEORDER_GOODS_ID = #{quoteorderGoodsId,jdbcType=INTEGER} and IS_DELETE = 0 ORDER BY QUOTEORDER_GOODS_REMARK_ID DESC
  </select>


  <select id="selectByQuoteId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select a.*
    from T_QUOTEORDER_GOODS_REMARK a
    left join T_QUOTEORDER_GOODS b on a.QUOTEORDER_GOODS_ID = b.QUOTEORDER_GOODS_ID
    where a.IS_DELETE = 0
    and b.IS_DELETE = 0
    and b.QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER}
    ORDER BY QUOTEORDER_GOODS_REMARK_ID DESC
  </select>


  <insert id="insertSelective" keyColumn="QUOTEORDER_GOODS_REMARK_ID" keyProperty="quoteorderGoodsRemarkId" parameterType="com.vedeng.crm.business.quote.domain.entity.QuoteorderGoodsRemarkEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_QUOTEORDER_GOODS_REMARK
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="quoteorderGoodsId != null">
        QUOTEORDER_GOODS_ID,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="quoteorderGoodsId != null">
        #{quoteorderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.crm.business.quote.domain.entity.QuoteorderGoodsRemarkEntity">
    <!--@mbg.generated-->
    update T_QUOTEORDER_GOODS_REMARK
    <set>
      <if test="quoteorderGoodsId != null">
        QUOTEORDER_GOODS_ID = #{quoteorderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where QUOTEORDER_GOODS_REMARK_ID = #{quoteorderGoodsRemarkId,jdbcType=BIGINT}
  </update>
</mapper>
