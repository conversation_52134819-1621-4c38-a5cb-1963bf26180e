package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class KingDeeReceiveBillCommand {
    /**
     * 单据内码
     */
    private String fid;
    /**
     * 单据编号
     */
    private String fBillNo;
    /**
     * 单据类型
     */
    private KingDeeNumberCommand fBillTypeId = new KingDeeNumberCommand();
    /**
     * 收款组织
     */
    private KingDeeNumberCommand fPayOrgId = new KingDeeNumberCommand();
    /**
     * 单据日期
     */
    private String fDate;
    /**
     * 银行流水号
     */
    private String f_qzok_lsh;
    /**
     * 往来单位类型
     */
    private String fContactUnitType;
    /**
     * 往来单位
     */
    private KingDeeNumberCommand fContactUnit = new KingDeeNumberCommand();
    /**
     * 交易主体
     */
    private String f_qzok_jyzt;
    /**
     * 交易类型
     */
    private String f_qzok_jylx;
    /**
     * 交易方式
     */
    private String f_qzok_jyfs;

    private String f_QZOK_PZGSYWDH;

    private List<KingDeeReceiveBillEntryCommand> fReceiveBillEntry;

    @NoArgsConstructor
    @Data
    public static class KingDeeReceiveBillEntryCommand{
        /**
         * 结算方式
         */

        private KingDeeNumberCommand fSettleTypeId = new KingDeeNumberCommand();
        /**
         * 用途
         */
        private KingDeeNumberCommand fPurposeId = new KingDeeNumberCommand();
        /**
         * 我方银行账号
         */
        private KingDeeNumberCommand fAccountId = new KingDeeNumberCommand();
        /**
         * 收款业务分类
         */
        private KingDeeNumberCommand f_qzok_skyw = new KingDeeNumberCommand();
        /**
         * 收款金额
         */
        private String fRecTotalAmountFor;
        /**
         * 贝登单据行id
         */
        private String f_qzok_bddjhid;
        /**
         * 原始订单号
         */
        private String f_qzok_ysddh;
        /**
         * 归属业务单号
         */
        private String f_qzok_gsywdh;
        /**
         * 业务类型
         */
        private String f_qzok_ywlx;

        /**
         * 手续费
         */
        private String fHANDLINGCHARGEFOR;
    }
}
