package com.vedeng.common.listener.process;

import com.alibaba.fastjson.JSONObject;
import com.google.common.eventbus.Subscribe;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.common.core.config.IObserver;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.finance.dto.event.CreateBillEvent;
import com.vedeng.finance.model.PayApply;
import com.vedeng.finance.service.PayApplyService;
import com.vedeng.system.service.VerifiesRecordService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.TaskService;
import org.activiti.engine.task.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;


/**
 * 创建付款申请单事件监听
 */
@Component
@Slf4j
public class CreateBillListener implements IObserver {

    @Autowired
    private ActionProcdefService actionProcdefService;

    @Autowired
    private ProcessEngine processEngine;

    @Autowired
    private PayApplyService payApplyService;

    @Autowired
    private VerifiesRecordService verifiesRecordService;

    /**
     * 创建付款申请单事件
     * @param createBillEvent
     */
    @Subscribe
    @Transactional(rollbackFor = Throwable.class)
    public void createBillBuyOrder(CreateBillEvent createBillEvent) {
        log.info("创建付款申请单事件监听:{}",JSONObject.toJSONString(createBillEvent));
        TaskService taskService = processEngine.getTaskService();
        Task taskInfoPay = taskService.createTaskQuery()
                .processInstanceBusinessKey("paymentVerify_" + createBillEvent.getPayApplyId())
                .singleResult();
        String taskId = taskInfoPay.getId();

        String id = (String) taskService.getVariable(taskId, "id");
        Integer idValue = (Integer) taskService.getVariable(taskId, "idValue");
        String key = (String) taskService.getVariable(taskId, "key");
        String tableName = (String) taskService.getVariable(taskId, "tableName");
        // 使用任务id,获取任务对象，获取流程实例id
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        String taskName = task.getName();

        Map<String, Object> variables = new HashMap<>();
        variables.put("pass", true);

        if (!"T_PAY_APPLY".equals(tableName) || !"财务制单".equals(taskName)) {
            log.info("不是财务制单，不处理,tableName:{},taskName:{}",tableName,taskName);
            return;
        }
        // 制单
        log.info("制单updateInfo,payApplyId:{}", idValue);
        actionProcdefService.updateInfo(tableName, id, idValue, "IS_BILL", 1, 2);
        //更新制单信息
        log.info("制单updatePayApplyIsBillInfo,payApplyId:{}, PayVedengBankId:{}", idValue, createBillEvent.getPayVedengBankId());
        payApplyService.updatePayApplyIsBillInfo(idValue, "", createBillEvent.getPayVedengBankId());

        PayApply payApply = new PayApply();
        payApply.setPayApplyId(idValue);
        payApply.setBillTime(new Date());
        payApply.setBillMethod(1);
        log.info("更新制单信息:{}", JSONObject.toJSONString(payApply));
        payApplyService.updateBillMethod(payApply);
        // 审核通过
        log.info("付款申请审批通过：{}", JSONObject.toJSONString(variables));
        ResultInfo<?> complementStatus = actionProcdefService.complementTask(null, taskId, "", "njadmin", variables);
        // 如果未结束添加审核对应主表的审核状态
        if (!"endEvent".equals(complementStatus.getData())) {
            verifiesRecordService.saveVerifiesInfo(taskId, 0);
        }
        log.info("制单完成");
    }

}
