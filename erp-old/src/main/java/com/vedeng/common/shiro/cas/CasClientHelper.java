package com.vedeng.common.shiro.cas;

import com.ctrip.framework.apollo.ConfigService;
import com.vedeng.common.shiro.constant.SecurityConstants;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class CasClientHelper {

    private final static String SSO_CLIENT_NAMESPACE = "vedeng-sso-client";

    private static final List<Integer> LEGACY_ADMIN_FLAGS = Collections.unmodifiableList(Arrays.asList(SecurityConstants.SUPER_ADMIN_NO,
            SecurityConstants.NJ_ADMIN_NO));

    public static boolean enableSingleSignOn() {
        return ConfigService.getConfig(SSO_CLIENT_NAMESPACE).getBooleanProperty("sso.enabled", false);
    }

    public static boolean isAdminUser(Integer adminFlag) {
        return adminFlag != null && LEGACY_ADMIN_FLAGS.contains(adminFlag);
    }

    public static String getProperty(String name) {
        return ConfigService.getConfig(SSO_CLIENT_NAMESPACE).getProperty(name,"");
    }
}
