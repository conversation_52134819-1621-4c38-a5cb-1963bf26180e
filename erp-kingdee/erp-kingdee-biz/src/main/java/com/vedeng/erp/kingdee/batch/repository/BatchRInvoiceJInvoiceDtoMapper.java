package com.vedeng.erp.kingdee.batch.repository;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2022/12/6 14:38
 **/
public interface BatchRInvoiceJInvoiceDtoMapper {

    /**
     * 冲销票查询原始蓝票
     * @param relateInvoiceId
     * @return
     */
    List<BatchInvoiceDto> findOriginalBlueEnable(@Param("relateInvoiceId")Integer relateInvoiceId);

    /**
     * 原票查找是否有冲销票
     * @param invoiceId
     * @return
     */
    List<BatchInvoiceDto> findReversalInvoice(Integer invoiceId);


}