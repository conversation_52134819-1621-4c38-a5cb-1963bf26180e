package com.vedeng.erp.kingdee.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import java.util.Date;

import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
    * 金蝶采购普票
    */
@Getter
@Setter
@ToString
@Table(name = "KING_DEE_PURCHASE_VAT_PLAIN_INVOICE")
public class KingDeePurchaseVatPlainInvoiceEntity extends BaseEntity {
    /**
    * 主键
    */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer purchaseVatPlainInvoiceId;

    /**
    * 单据内码
    */
    private String fid;

    /**
    * 业务日期  录票时间
    */
    private String fdate;

    /**
    * 贝登erp对应的单据头ID
    */
    private String fQzokBddjtid;

    /**
    * 发票号23456
    */
    private String finvoiceno;

    /**
    * 发票日期 2022-09-22 00:00:00
    */
    private String finvoicedate;

    /**
    * 供应商
    */
    private String fsupplierid;

    /**
    * 单据状态 默认:Z
    */
    private String fdocumentstatus;

    /**
    * 单据类型 CGPTFP01_SYS            采购增值税普通发票
    */
    private String fBillTypeId;

    /**
    * 结算组织 默认101,配置化
    */
    private String fsettleorgid;

    /**
    * 采购组织 默认101,配置化
    */
    private String fpurchaseorgid;

    /**
    * 作废状态 A （正常）
    */
    private String fCancelStatus;

    /**
    * 红蓝字标识 0 蓝字  1 红字
    */
    private String fRedBlue;

    /**
    * 发票代码12345
    */
    private String fQzokFpdm;

    /**
    * 发票类型 1电票 2 纸票
    */
    private String fQzokFplx;

    /**
    * 发票明细
    */
    private String fpurchaseicentry;
}