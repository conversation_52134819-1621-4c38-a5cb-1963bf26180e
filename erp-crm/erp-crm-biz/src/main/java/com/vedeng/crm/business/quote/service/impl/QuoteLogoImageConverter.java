package com.vedeng.crm.business.quote.service.impl;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.metadata.data.ImageData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.util.IoUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;

/**
 * 从本地加载图片资源
 */
@Slf4j
public class QuoteLogoImageConverter implements Converter<String> {

    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<String> context) throws Exception {
        InputStream inputStream = getClass().getClassLoader().getResourceAsStream(context.getValue());
        byte[] bytes = IoUtils.toByteArray(inputStream);
        WriteCellData<Object> writeCellData = new WriteCellData<>(bytes);
        ImageData imageData = writeCellData.getImageDataList().get(0);
        // logo算上自已的单元格，共横跨6个单元格
        imageData.setRelativeLastColumnIndex(5);
        return writeCellData;

    }
}
