package com.vedeng.erp.buyorder.mapper;

import com.vedeng.erp.buyorder.domain.entity.GeActionLog;

import java.util.List;

public interface GeActionLogMapper {
    int deleteByPrimaryKey(Integer authorizationLogId);

    int insert(GeActionLog record);

    int insertSelective(GeActionLog record);

    GeActionLog selectByPrimaryKey(Integer authorizationLogId);

    int updateByPrimaryKeySelective(GeActionLog record);

    int updateByPrimaryKey(GeActionLog record);

    List<GeActionLog> queryByRelatedTypeAndRelatedId(GeActionLog record);
}