<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.trader.dao.TraderFinanceMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.trader.model.TraderFinance" >
    <id column="TRADER_FINANCE_ID" property="traderFinanceId" jdbcType="INTEGER" />
    <result column="TRADER_ID" property="traderId" jdbcType="INTEGER" />
    <result column="TRADER_TYPE" property="traderType" jdbcType="BIT" />
    <result column="CREDIT_RATING" property="creditRating" jdbcType="INTEGER" />
    <result column="REG_ADDRESS" property="regAddress" jdbcType="VARCHAR" />
    <result column="REG_TEL" property="regTel" jdbcType="VARCHAR" />
    <result column="TAX_NUM" property="taxNum" jdbcType="VARCHAR" />
    <result column="AVERAGE_TAXPAYER_DOMAIN" property="averageTaxpayerDomain" jdbcType="VARCHAR" />
    <result column="AVERAGE_TAXPAYER_URI" property="averageTaxpayerUri" jdbcType="VARCHAR" />
    <result column="BANK" property="bank" jdbcType="VARCHAR" />
    <result column="BANK_CODE" property="bankCode" jdbcType="VARCHAR" />
    <result column="BANK_ACCOUNT" property="bankAccount" jdbcType="VARCHAR" />
    <result column="COMMENTS" property="comments" jdbcType="VARCHAR" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="CHECK_STATUS" property="checkStatus" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
  </resultMap>
  
  <resultMap type="com.vedeng.trader.model.vo.TraderFinanceVo" id="VoResultMap" extends="BaseResultMap">
  	<result column="AMOUNT" property="amount" jdbcType="DECIMAL" />
    <result column="PERIOD_AMOUNT" property="periodAmount" jdbcType="DECIMAL" />
    <result column="PERIOD_DAY" property="periodDay" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    TRADER_FINANCE_ID, TRADER_ID, TRADER_TYPE, CREDIT_RATING, REG_ADDRESS, REG_TEL, TAX_NUM, 
    AVERAGE_TAXPAYER_DOMAIN, AVERAGE_TAXPAYER_URI, BANK, BANK_CODE, BANK_ACCOUNT,COMMENTS,ADD_TIME, 
    CREATOR, MOD_TIME, UPDATER
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_TRADER_FINANCE
    where TRADER_FINANCE_ID = #{traderFinanceId,jdbcType=INTEGER}
  </select>
  <select id="selectByTraderId" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_TRADER_FINANCE
    where TRADER_ID = #{traderId,jdbcType=INTEGER}
  </select>

  <select id="getCustomerFinanceByTraderId" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from T_TRADER_FINANCE
    where TRADER_TYPE=1 AND TRADER_ID = #{traderId,jdbcType=INTEGER}
    limit 1
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_TRADER_FINANCE
    where TRADER_FINANCE_ID = #{traderFinanceId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.trader.model.TraderFinance" >
    insert into T_TRADER_FINANCE (TRADER_FINANCE_ID, TRADER_ID, TRADER_TYPE, 
      CREDIT_RATING, REG_ADDRESS, REG_TEL, 
      TAX_NUM, AVERAGE_TAXPAYER_DOMAIN, AVERAGE_TAXPAYER_URI, 
      BANK, BANK_CODE, BANK_ACCOUNT, COMMENTS,
      ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER)
    values (#{traderFinanceId,jdbcType=INTEGER}, #{traderId,jdbcType=INTEGER}, #{traderType,jdbcType=BIT}, 
      #{creditRating,jdbcType=INTEGER}, #{regAddress,jdbcType=VARCHAR}, #{regTel,jdbcType=VARCHAR}, 
      #{taxNum,jdbcType=VARCHAR}, #{averageTaxpayerDomain,jdbcType=VARCHAR}, #{averageTaxpayerUri,jdbcType=VARCHAR}, 
      #{bank,jdbcType=VARCHAR}, #{bankCode,jdbcType=VARCHAR}, #{bankAccount,jdbcType=VARCHAR}, #{comments,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, 
      #{updater,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.trader.model.TraderFinance" >
    insert into T_TRADER_FINANCE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="traderFinanceId != null" >
        TRADER_FINANCE_ID,
      </if>
      <if test="traderId != null" >
        TRADER_ID,
      </if>
      <if test="traderType != null" >
        TRADER_TYPE,
      </if>
      <if test="creditRating != null" >
        CREDIT_RATING,
      </if>
      <if test="regAddress != null" >
        REG_ADDRESS,
      </if>
      <if test="regTel != null" >
        REG_TEL,
      </if>
      <if test="taxNum != null" >
        TAX_NUM,
      </if>
      <if test="averageTaxpayerDomain != null" >
        AVERAGE_TAXPAYER_DOMAIN,
      </if>
      <if test="averageTaxpayerUri != null" >
        AVERAGE_TAXPAYER_URI,
      </if>
      <if test="bank != null" >
        BANK,
      </if>
      <if test="bankCode != null" >
        BANK_CODE,
      </if>
      <if test="bankAccount != null" >
        BANK_ACCOUNT,
      </if>
      <if test="comments != null" >
        COMMENTS,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="traderFinanceId != null" >
        #{traderFinanceId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null" >
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderType != null" >
        #{traderType,jdbcType=BIT},
      </if>
      <if test="creditRating != null" >
        #{creditRating,jdbcType=INTEGER},
      </if>
      <if test="regAddress != null" >
        #{regAddress,jdbcType=VARCHAR},
      </if>
      <if test="regTel != null" >
        #{regTel,jdbcType=VARCHAR},
      </if>
      <if test="taxNum != null" >
        #{taxNum,jdbcType=VARCHAR},
      </if>
      <if test="averageTaxpayerDomain != null" >
        #{averageTaxpayerDomain,jdbcType=VARCHAR},
      </if>
      <if test="averageTaxpayerUri != null" >
        #{averageTaxpayerUri,jdbcType=VARCHAR},
      </if>
      <if test="bank != null" >
        #{bank,jdbcType=VARCHAR},
      </if>
      <if test="bankCode != null" >
        #{bankCode,jdbcType=VARCHAR},
      </if>
      <if test="bankAccount != null" >
        #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="comments != null" >
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.trader.model.TraderFinance" >
    update T_TRADER_FINANCE
    <set >
      <if test="traderId != null" >
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderType != null" >
        TRADER_TYPE = #{traderType,jdbcType=BIT},
      </if>
      <if test="creditRating != null" >
        CREDIT_RATING = #{creditRating,jdbcType=INTEGER},
      </if>
      <if test="regAddress != null" >
        REG_ADDRESS = #{regAddress,jdbcType=VARCHAR},
      </if>
      <if test="regTel != null" >
        REG_TEL = #{regTel,jdbcType=VARCHAR},
      </if>
      <if test="taxNum != null" >
        TAX_NUM = #{taxNum,jdbcType=VARCHAR},
      </if>
      <if test="averageTaxpayerDomain != null" >
        AVERAGE_TAXPAYER_DOMAIN = #{averageTaxpayerDomain,jdbcType=VARCHAR},
      </if>
      <if test="averageTaxpayerUri != null" >
        AVERAGE_TAXPAYER_URI = #{averageTaxpayerUri,jdbcType=VARCHAR},
      </if>
      <if test="bank != null" >
        BANK = #{bank,jdbcType=VARCHAR},
      </if>
      <if test="bankCode != null" >
        BANK_CODE = #{bankCode,jdbcType=VARCHAR},
      </if>
      <if test="bankAccount != null" >
        BANK_ACCOUNT = #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="comments != null" >
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where 1=1 
    <if test="traderFinanceId != null" >
        and TRADER_FINANCE_ID = #{traderFinanceId,jdbcType=INTEGER}
    </if>
    <if test="traderId != null" >
        and TRADER_ID = #{traderId,jdbcType=INTEGER}
      </if>
    
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.trader.model.TraderFinance" >
    update T_TRADER_FINANCE
    set TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_TYPE = #{traderType,jdbcType=BIT},
      CREDIT_RATING = #{creditRating,jdbcType=INTEGER},
      REG_ADDRESS = #{regAddress,jdbcType=VARCHAR},
      REG_TEL = #{regTel,jdbcType=VARCHAR},
      TAX_NUM = #{taxNum,jdbcType=VARCHAR},
      AVERAGE_TAXPAYER_DOMAIN = #{averageTaxpayerDomain,jdbcType=VARCHAR},
      AVERAGE_TAXPAYER_URI = #{averageTaxpayerUri,jdbcType=VARCHAR},
      BANK = #{bank,jdbcType=VARCHAR},
      BANK_CODE = #{bankCode,jdbcType=VARCHAR},
      BANK_ACCOUNT = #{bankAccount,jdbcType=VARCHAR},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER}
    where TRADER_FINANCE_ID = #{traderFinanceId,jdbcType=INTEGER}
  </update>
  <!-- getTraderFinance -->
  <select id="getTraderCustomerFinance" resultMap="VoResultMap" parameterType="com.vedeng.trader.model.vo.TraderFinanceVo" >
    select 
    	tf.TRADER_FINANCE_ID, tf.TRADER_ID, tf.TRADER_TYPE, tf.CREDIT_RATING, tf.REG_ADDRESS, tf.REG_TEL, tf.TAX_NUM, 
   		tf.AVERAGE_TAXPAYER_DOMAIN, tf.AVERAGE_TAXPAYER_URI, tf.BANK, tf.BANK_CODE, tf.BANK_ACCOUNT,tf.COMMENTS,tf.ADD_TIME, 
    	tf.CREATOR, tf.MOD_TIME, tf.UPDATER, t.AMOUNT,v.STATUS as CHECK_STATUS
    from T_TRADER_FINANCE tf
    left join T_TRADER_CUSTOMER t on tf.TRADER_ID = t.TRADER_ID
    left join T_VERIFIES_INFO v ON v.RELATE_TABLE_KEY=tf.TRADER_FINANCE_ID AND v.relate_table='T_TRADER_FINANCE'
    where 1=1
    <if test="traderFinanceId != null" >
        and tf.TRADER_FINANCE_ID = #{traderFinanceId,jdbcType=INTEGER}
     </if>
    <if test="traderId!=null and traderId!=0">
    	and tf.TRADER_ID = #{traderId,jdbcType=INTEGER}
    </if>
    <if test="traderType !=null ">
    	and tf.TRADER_TYPE = #{traderType,jdbcType=BIT}
    </if>
    order by tf.ADD_TIME desc limit 1
  </select>
  
  <select id="getTraderSupplierFinance" resultMap="VoResultMap" parameterType="com.vedeng.trader.model.vo.TraderFinanceVo" >
    select 
    	tf.TRADER_FINANCE_ID, tf.TRADER_ID, tf.TRADER_TYPE, tf.CREDIT_RATING, tf.REG_ADDRESS, tf.REG_TEL, tf.TAX_NUM, 
   		tf.AVERAGE_TAXPAYER_DOMAIN, tf.AVERAGE_TAXPAYER_URI, tf.BANK, tf.BANK_CODE, tf.BANK_ACCOUNT,tf.COMMENTS,tf.ADD_TIME, 
    	tf.CREATOR, tf.MOD_TIME, tf.UPDATER
    from T_TRADER_FINANCE tf
    where 1=1
     <if test="traderFinanceId != null" >
        and tf.TRADER_FINANCE_ID = #{traderFinanceId,jdbcType=INTEGER}
     </if>
    <if test="traderId!=null and traderId!=0">
    	and tf.TRADER_ID = #{traderId,jdbcType=INTEGER}
    </if>
    <if test="traderType !=null ">
    	and tf.TRADER_TYPE = #{traderType,jdbcType=BIT}
    </if>
    order by tf.ADD_TIME desc limit 1
  </select>
  
  <select id="getTraderCustomerFinanceList" resultMap="BaseResultMap" parameterType="com.vedeng.trader.model.TraderFinance" >
    select 
    	tf.TRADER_FINANCE_ID, tf.TRADER_ID, tf.TRADER_TYPE, tf.CREDIT_RATING, tf.REG_ADDRESS, tf.REG_TEL, tf.TAX_NUM, 
   		tf.AVERAGE_TAXPAYER_DOMAIN, tf.AVERAGE_TAXPAYER_URI, tf.BANK, tf.BANK_CODE, tf.BANK_ACCOUNT,tf.COMMENTS,tf.ADD_TIME, 
    	tf.CREATOR, tf.MOD_TIME, tf.UPDATER
    from T_TRADER_FINANCE tf
    where 1=1

    	and tf.TRADER_ID = #{traderId,jdbcType=INTEGER}

    <if test="traderType !=null ">
    	and tf.TRADER_TYPE = #{traderType,jdbcType=BIT}
    </if>
    order by tf.ADD_TIME desc
  </select>

  <update id="updateTraderFinanceSelective">
    update T_TRADER_FINANCE
    <set >
      <if test="regAddress != null" >
        REG_ADDRESS = #{regAddress,jdbcType=VARCHAR},
      </if>
      <if test="regTel != null" >
        REG_TEL = #{regTel,jdbcType=VARCHAR},
      </if>
      <if test="taxNum != null" >
        TAX_NUM = #{taxNum,jdbcType=VARCHAR},
      </if>
      <if test="averageTaxpayerDomain != null" >
        AVERAGE_TAXPAYER_DOMAIN = #{averageTaxpayerDomain,jdbcType=VARCHAR},
      </if>
      <if test="averageTaxpayerUri != null" >
        AVERAGE_TAXPAYER_URI = #{averageTaxpayerUri,jdbcType=VARCHAR},
      </if>
      <if test="bank != null" >
        BANK = #{bank,jdbcType=VARCHAR},
      </if>
      <if test="bankCode != null" >
        BANK_CODE = #{bankCode,jdbcType=VARCHAR},
      </if>
      <if test="bankAccount != null" >
        BANK_ACCOUNT = #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="comments != null" >
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where
      TRADER_ID = #{traderId,jdbcType=INTEGER}
      AND TRADER_TYPE = #{traderType,jdbcType=INTEGER}
  </update>
</mapper>