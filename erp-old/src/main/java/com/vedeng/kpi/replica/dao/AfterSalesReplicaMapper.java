package com.vedeng.kpi.replica.dao;

import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesGoods;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;
import java.util.List;

/**
 * <AUTHOR>
 * @date created in 2020/6/17 19:53
 */
@Named("afterSalesReplicaMapper")
public interface AfterSalesReplicaMapper {

    /**
     * 根据订单id获取其计入业绩时间之前的已经退款或已经完成的销售订单退货的售后产品
     * @param saleorderNo 订单号
     * @param executeTime 计入业绩的时间
     * @return 售后产品
     */
    List<AfterSalesGoods> getAfterSalesGoodIdBySaleorderBeforeExecuteTime(@Param("saleorderNo") String saleorderNo, @Param("executeTime") Long executeTime);

    AfterSales getAfterSalesByAfterSalesDetailId(Integer afterSalesDetailId);


    AfterSales getAfterSalesByAfterSalesId(Integer afterSalesId);

    Integer getRefundStatusOfAfterSales(Integer afterSalesId);


    /**
     * 获取订单计入业绩之后的所有售后单
     * @param saleorderNo 订单号
     * @param executeTime 订单计入业绩时间
     * @return 售后单列表
     */
    List<AfterSales> getAfterSalesBySaleorderNo(@Param("saleorderNo") String saleorderNo, @Param("executeTime") Long executeTime);

    List<AfterSales> getAfterSalesByStartTime(@Param("startTimestamp") Long startTimestamp, @Param("offset") Integer offset, @Param("limit") Integer limit);
}
