<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="申请修改" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript"
        src='${pageContext.request.contextPath}/static/js/orderstream/buyorder/new_apply_edit_buyorder.js?rnd=${resourceVersionKey}'></script>
<div class="form-list  form-tips5">
    <form method="post" action="${pageContext.request.contextPath}/order/newBuyorder/saveBuyorderEditApply.do"
          id="myform">
        <div class="formtitle mt10">
            收货信息
        </div>
        <ul>

            <%--VDERP-8808计算采购单总金额--%>
            <input type="hidden" name="oldTotalAmount" value="${bv.totalAmount}">

            <li>
                <div class="form-tips">
                    <lable>是否直发:</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <ul>
                            <li>
                                <input type="hidden" id="isUpdateDeliveryDirect" value="${bv.isUpdateDeliveryDirect}">
                                <input type="hidden" id="currentDeliveryDirect" value="${bv.deliveryDirect}">
                                <input type="radio" name="deliveryDirect" value="0"
                                       <c:if test="${bv.deliveryDirect eq 0}">checked="checked"</c:if>>
                                <label><span>普发</span></label>
                            </li>
                            <li>
                                <input type="radio" name="deliveryDirect" value="1"
                                       <c:if test="${bv.deliveryDirect eq 1}">checked="checked"</c:if>>
                                <label><span>直发</span></label>
                            </li>
                            <li>
                                <span>
                                    <div class="customername pos_rel">
                                        <i class="iconredsigh contorlIcon"></i>
                                        <div class="pos_abs customernameshow mouthControlPos">
                                            <div>1、普发改直发，采购单仅关联1个销售单且采购单未发货且采购单未收货时可修改</div>
                                            <div>2、直发改普发，采购单未发货且采购单未收货时可修改</div>
                                        </div>
                                    </div>
                                </span>
                            </li>
                        </ul>
                        <div id="isUpdateDeliveryDirectError"></div>
                        <input type="text" id="deliveryDirectChangeReason" name="deliveryDirectChangeReason"
                               class="input-xx" style="margin-top: 5px;display: none"
                               placeholder="请填写修改发货方式的原因"/>
                        <div id="deliveryDirectChangeReasonError"></div>
                    </div>
                </div>
            </li>

            <li class="zf <c:if test="${bv.deliveryDirect eq 0}">none</c:if>">
                <div class="form-tips">
                    <lable>收货客户:</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <span>${bv.takeTraderName}</span>
                    </div>
                </div>
            </li>
            <li class="zf <c:if test="${bv.deliveryDirect eq 0}">none</c:if>">
                <div class="form-tips">
                    <lable>收货联系人:</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <span>${bv.takeTraderContactName}/${bv.takeTraderContactTelephone}/${bv.takeTraderContactMobile}</span>
                    </div>
                </div>
            </li>
            <li class="zf <c:if test="${bv.deliveryDirect eq 0}">none</c:if>">
                <div class="form-tips">
                    <lable>收货地区:</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <span>${bv.takeTraderArea}</span>
                    </div>
                </div>
            </li>
            <li class="zf <c:if test="${bv.deliveryDirect eq 0}">none</c:if>">
                <div class="form-tips">
                    <lable>收货地址:</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <span>${bv.takeTraderAddress}</span>
                    </div>
                </div>
            </li>

            <li class="ptz none">
                <div class="form-tips">
                    <lable>收货客户:</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <span>${bv.saleTakeTraderName}</span>
                    </div>
                </div>

            </li>
            <li class="ptz none">
                <div class="form-tips">
                    <lable>收货联系人:</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <span>${bv.saleTakeTraderContactName}/${bv.saleTakeTraderContactTelephone}/${bv.saleTakeTraderContactMobile}</span>
                    </div>
                </div>
            </li>
            <li class="ptz none">
                <div class="form-tips">
                    <lable>收货地区:</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <span>${bv.saleTakeTraderArea}</span>
                    </div>
                </div>
            </li>
            <li class="ptz none">
                <div class="form-tips">
                    <lable>收货地址:</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <span>${bv.saleTakeTraderAddress}</span>
                    </div>
                </div>
            </li>

            <li class="pf <c:if test="${bv.deliveryDirect eq 1}">none</c:if>">
                <div class="form-tips">
                    <lable>收货客户:</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <span>${companyName}</span>
                    </div>
                </div>
            </li>
            <li class="pf <c:if test="${bv.deliveryDirect eq 1}">none</c:if>">
                <div class="form-tips">
                    <lable>收货地址:</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <select class="input-xx" id="address_pf" onchange="init()">
                            <c:forEach var="list" items="${addressList}">
                                <option value="${list.addressId}|${list.areas}|${list.address}|${list.contactName}|${list.mobile}|${list.telephone}"
                                        <c:if test="${list.addressId eq bv.takeTraderAddressId }">selected="selected"</c:if>>
                                        ${list.areas}/${list.address}/${list.contactName}/${list.mobile}/${list.telephone}</option>
                            </c:forEach>
                        </select>
                    </div>
                </div>
            </li>
            <li class="ztp none">
                <div class="form-tips">
                    <lable>收货客户:</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <span>${companyName}</span>
                    </div>
                </div>

            </li>
            <li class="ztp none">
                <div class="form-tips">
                    <lable>收货地址:</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <select class="input-xx" id="address_ztp" onchange="init()">
                            <c:forEach var="list" items="${addressList}">
                                <option value="${list.addressId}|${list.areas}|${list.address}|${list.contactName}|${list.mobile}|${list.telephone}"
                                        <c:if test="${list.addressId eq bv.takeTraderAddressId }">selected="selected"</c:if>>
                                        ${list.areas}/${list.address}/${list.contactName}/${list.mobile}</option>
                            </c:forEach>
                        </select>
                    </div>
                </div>
            </li>
            <li>
                <div class="form-tips">
                    <lable>物流备注:</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <input type="text" name="logisticsComments" placeholder="对内使用，适用于向物流部同事告知发货要求，默认同步客户信息中物流备注"
                               id="logisticsComments" class="input-xx" value="${bv.logisticsComments }"/>
                        <input type="hidden" id="oldLogisticsComments" value="${bv.logisticsComments }"/>
                    </div>
                    <div id="logisticsCommentsError"></div>
                </div>
            </li>
        </ul>
        <div class="formtitle ">
            收票信息
        </div>
        <ul>
            <li>
                <div class="form-tips">
                    <lable>收票类型:</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <select class="input-middle" name="invoiceType">
                            <c:forEach var="list" items="${receiptTypes}" varStatus="status">
                                <option value="${list.sysOptionDefinitionId}"
                                        <c:if test="${list.sysOptionDefinitionId eq bv.invoiceType}">selected="selected"</c:if>>${list.title}</option>
                            </c:forEach>
                        </select>
                    </div>
                </div>
            </li>
            <li>
                <div class="form-tips">
                    <lable>收票备注:</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <input type="text" placeholder="对内使用，适用于向财务部同事告知收票要求" id="invoiceComments"
                               class="input-xx" name="invoiceComments" value="${bv.invoiceComments }"/>
                    </div>
                    <div id="invoiceCommentsError"></div>
                </div>
            </li>
        </ul>
        <div class="parts">
            <div class="title-container">
                <div class="table-title nobor">产品信息</div>
            </div>
            <table class="table  table-bordered table-striped table-condensed table-centered">
                <thead>
                <tr>
                    <th class="wid2">序号</th>
                    <th class="wid8">订货号</th>
                    <th class="wid15">产品名称</th>
                    <th class="wid8">品牌</th>
                    <th class="wid8">规格/型号</th>
                    <th class="wid5">采购数量</th>
                    <th class="wid8">单位</th>
                    <c:choose>
                        <c:when test="${bv.isGift  == 1}">
                        <th class="wid8">赠品参考价</th>
                        </c:when>
                        <c:otherwise>
                            <th class="wid8">单价</th>
                        </c:otherwise>
                    </c:choose>
                    <th class="wid9">采购预计发货日</th>
                    <th class="wid9">采购预计到货日</th>
                    <th class="wid5">货期</th>
                    <th class="wid8">是否有授权</th>
                    <c:if test="${bv.usedTotalRebate > 0}">
                    <th class="wid15 rebate_class">
                        <div>
                            返利信息
                        </div>
                    </th>
                    </c:if>
                    <th class="wid25">采购备注</th>
                </tr>
                </thead>
                <tbody id="test">
                <c:forEach var="bgv" items="${bv.bgvList}" varStatus="staut">
                    <tr>
                        <input type="hidden" name="buyorderModifyApplyGoodsList[${staut.index}].buyorderGoodsId"
                               value="${bgv.buyorderGoodsId}"/>
                        <td>${staut.count}</td>
                        <td>${bgv.sku}</td>
                        <td class="text-left">
                            <c:if test="${bgv.isGift == 1}"><span style="color:#FF0000">赠品</span></c:if>
                            <div class="customername pos_rel" style="display:inline;">
										<span class="font-blue">
											<a class="addtitle" href="javascript:void(0);"
                                               tabTitle='{"num":"viewgoods${bgv.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${bgv.goodsId}","title":"产品信息"}'>${newSkuInfosMap[bgv.sku].SHOW_NAME}</a>&nbsp;
												<i class="iconbluemouth contorlIcon"></i><br/>
										</span>

                                <c:set var="skuNo" value="${bgv.sku}"></c:set>
                                <%@ include file="../../common/new_sku_common_tip.jsp" %>
                                <div>${bgv.sku}</div>
                            </div>
                        </td>
                        <td>${newSkuInfosMap[bgv.sku].BRAND_NAME}</td>
                        <td>${newSkuInfosMap[bgv.sku].MODEL}</td>

                        <td>
                            ${bgv.num}
                                <input type="hidden" name="buyorderModifyApplyGoodsList[${staut.index}].num"
                                       value="${bgv.num}">
                        </td>
                        <td>${newSkuInfosMap[bgv.sku].UNIT_NAME}</td>
                        <td>
                            <c:choose>
                            <c:when test="${bv.isGift  == 1}">
                                <input name="buyorderModifyApplyGoodsList[${staut.index}].referPrice"
                                       style="margin:0;width: 100%;" value="<fmt:formatNumber type="number" value="${bgv.referPrice}" pattern="0.00" maxFractionDigits="2" />"/>
                                <input type="hidden" name="buyorderModifyApplyGoodsList[${staut.index}].oldReferPrice"
                                       value="${bgv.referPrice}">

                                <input type="hidden" name="buyorderModifyApplyGoodsList[${staut.index}].price"
                                       value="${bgv.price}">
                                <input type="hidden" name="buyorderModifyApplyGoodsList[${staut.index}].oldPrice"
                                       value="${bgv.price}">
                        </c:when>
                        <c:otherwise>
                            <c:choose>
                                <c:when test="${bv.paymentStatus eq 0 and  (bgv.rebateAmount == null or bgv.rebateAmount <= 0)}">
                                    <input name="buyorderModifyApplyGoodsList[${staut.index}].price"
                                           style="margin:0;width: 100%;" value="${bgv.price}"/>
                                    <input type="hidden" name="buyorderModifyApplyGoodsList[${staut.index}].oldPrice"
                                           value="${bgv.price}">
                                </c:when>
                                <c:otherwise>
                                        ${bgv.price}
                                    <input type="hidden" name="buyorderModifyApplyGoodsList[${staut.index}].price"
                                           value="${bgv.price}">
                                    <input type="hidden" name="buyorderModifyApplyGoodsList[${staut.index}].oldPrice"
                                           value="${bgv.price}">
                                </c:otherwise>
                            </c:choose>
                        </c:otherwise>
                        </c:choose>
                        </td>

                        <td>
                            <input name="buyorderModifyApplyGoodsList[${staut.index}].sendGoodsTimeShow"
                                   class="Wdate f_left m0 input-smaller96 warning-color1"
                                   autocomplete="off" type="text"
                                   placeholder="请选择日期"
                                   onClick="WdatePicker()"
                                   value='<date:date value ="${bgv.sendGoodsTime}" format="yyyy-MM-dd"/>'/>
                            <input type="hidden"
                                   name="buyorderModifyApplyGoodsList[${staut.index}].oldSendGoodsTimeShow"
                                   value='<date:date value ="${bgv.sendGoodsTime}" format="yyyy-MM-dd"/>'>
                        </td>

                        <td>
                            <input name="buyorderModifyApplyGoodsList[${staut.index}].receiveGoodsTimeShow"
                                   class="Wdate f_left input-smaller96 warning-color1"
                                   autocomplete="off" type="text"
                                   placeholder="请选择日期"
                                   onClick="WdatePicker()"
                                   value='<date:date value ="${bgv.receiveGoodsTime}" format="yyyy-MM-dd"/>'/>
                            <input type="hidden"
                                   name="buyorderModifyApplyGoodsList[${staut.index}].oldReceiveGoodsTimeShow"
                                   value='<date:date value ="${bgv.receiveGoodsTime}" format="yyyy-MM-dd"/>'>
                        </td>
                        <td>
                            <input name="buyorderModifyApplyGoodsList[${staut.index}].deliveryCycle"
                                   style="margin:0;width: 100%;" value="${bgv.deliveryCycle}"/>
                            <input type="hidden" name="buyorderModifyApplyGoodsList[${staut.index}].oldDeliveryCycle"
                                   value="${bgv.deliveryCycle}">
                        </td>

                        <td>
                            <c:choose>
                                <c:when test="${bgv.sku ne 'V127063'}">
                                    <select name="buyorderModifyApplyGoodsList[${staut.index}].isHaveAuth" style="margin:0;width: 100%;">
                                        <option value="1" <c:if test="${bgv.isHaveAuth eq 1}">selected</c:if>>是</option>
                                        <option value="0" <c:if test="${bgv.isHaveAuth eq 0}">selected</c:if>>否</option>
                                    </select>
                                </c:when>
                                <c:otherwise>
                                    <input type="hidden" name="buyorderModifyApplyGoodsList[${staut.index}].isHaveAuth" value="0">
                                </c:otherwise>
                            </c:choose>
                            <input type="hidden" name="buyorderModifyApplyGoodsList[${staut.index}].oldIsHaveAuth"
                                   value="${bgv.isHaveAuth}">
                        </td>
                        <%--返利信息--%>
                        <c:if test="${bv.usedTotalRebate > 0}">
                        <td class="text-left">
                            <span style="display: block">返利总额:<span name="totalRebate" id="totalRebate${bgv.goodsId}">${bgv.rebateAmount}</span></span>
                            <span style="display: block">返利单价:<span name="rebatePrice" id="rebatePrice${bgv.goodsId}">${bgv.rebatePrice}</span></span>
                            <span style="display: block">返利后单价:<span name="afterRebatePrice" id="afterRebatePrice${bgv.goodsId}">${bgv.rebateAfterPrice}</span></span>
                        </td>
                        </c:if>
                        <td>
                            <textarea name="buyorderModifyApplyGoodsList[${staut.index}].insideComments"
                                      style="margin:0px;width: 100%;">${bgv.insideComments}</textarea>
                            <input type="hidden" name="buyorderModifyApplyGoodsList[${staut.index}].oldInsideComments"
                                   value="${bgv.insideComments}">
                        </td>

                    </tr>
                </c:forEach>
                </tbody>
            </table>
        </div>

        <%-------------------------------------------------------虚拟商品-------------------------------------------------------------%>
        <div class="parts">
            <div class="title-container">
                <div class="table-title nobor">虚拟商品</div>
            </div>
            <table class="table  table-bordered table-striped table-condensed table-centered">
                <thead>
                <tr>
                    <th class="wid2">序号</th>
                    <th class="wid8">订货号</th>
                    <th class="wid15">产品名称</th>
                    <th class="wid8">费用类别</th>
                    <th class="wid8">是否可库存管理</th>
                    <th class="wid5">采购数量</th>
                    <th class="wid8">单价</th>
                    <th class="wid8">总额</th>
                    <th class="wid25">采购备注</th>
                </tr>
                </thead>
                <tbody>
                    <c:if test="${not empty buyOrderExpenseGoodsList}">
                        <c:forEach var="expenseItem" items="${buyOrderExpenseGoodsList}" varStatus="staut">
                            <tr>
                                <input type="hidden" name="buyorderExpenseItemDtos[${staut.index}].buyorderExpenseItemId" value="${expenseItem.buyorderExpenseItemId}"/>
                                <td>${staut.count}</td>
                                <td>${expenseItem.buyorderExpenseItemDetailDto.sku}</td>
                                <td>${expenseItem.buyorderExpenseItemDetailDto.goodsName}</td>
                                <td>${expenseItem.buyorderExpenseItemDetailDto.expenseCategoryName}</td>
                                <td>${expenseItem.buyorderExpenseItemDetailDto.haveStockManage}</td>
                                <td>${expenseItem.num}</td>
                                <td>
                                    <fmt:formatNumber type="number" value="${expenseItem.buyorderExpenseItemDetailDto.price}" pattern="0.00" maxFractionDigits="2"/>
                                </td>
                                <td>
                                    <fmt:formatNumber type="number" value="${expenseItem.num * expenseItem.buyorderExpenseItemDetailDto.price}" pattern="0.00" maxFractionDigits="2"/>
                                </td>
                                <td>
                                    <textarea name="buyorderExpenseItemDtos[${staut.index}].buyorderExpenseItemDetailDto.insideComments"
                                              style="margin:0;width: 100%;">${expenseItem.buyorderExpenseItemDetailDto.insideComments}</textarea>
                                    <input type="hidden" name="buyorderExpenseItemDtos[${staut.index}].buyorderExpenseItemDetailDto.oldInsideComments"
                                           value="${expenseItem.buyorderExpenseItemDetailDto.insideComments}">
                                </td>

                            </tr>
                        </c:forEach>
                    </c:if>

                    <c:if test="${empty buyOrderExpenseGoodsList}">
                        <tr>
                            <td colspan='9'>暂无记录！</td>
                        </tr>
                    </c:if>
                </tbody>
            </table>
        </div>



        <div class="add-tijiao" style="margin-bottom:50px;">

            <input type="hidden" id="synWmsCancel" name="synWmsCancel" value="0">

            <input type="hidden" name="takeTraderId" value="">
            <input type="hidden" name="takeTraderName" value="">
            <input type="hidden" name="takeTraderContactId" value="0">
            <input type="hidden" name="takeTraderContactName" value="">
            <input type="hidden" name="takeTraderContactMobile" value="">
            <input type="hidden" name="takeTraderContactTelephone" value="">
            <input type="hidden" name="takeTraderAddressId" value="">
            <input type="hidden" name="takeTraderArea" value="">
            <input type="hidden" name="takeTraderAddress" value="">

            <input type="hidden" id="companyName" value="${companyName}">

            <input type="hidden" id="ptz_takeTraderId" value="${bv.saleTakeTraderId}">
            <input type="hidden" id="ptz_takeTraderName" value="${bv.saleTakeTraderName}">
            <input type="hidden" id="ptz_takeTraderContactId" value="${bv.saleTakeTraderContactId}">
            <input type="hidden" id="ptz_takeTraderContactName" value="${bv.saleTakeTraderContactName}">
            <input type="hidden" id="ptz_takeTraderContactMobile" value="${bv.saleTakeTraderContactMobile}">
            <input type="hidden" id="ptz_takeTraderContactTelephone" value="${bv.saleTakeTraderContactTelephone}">
            <input type="hidden" id="ptz_takeTraderAddressId" value="${bv.saleTakeTraderAddressId}">
            <input type="hidden" id="ptz_takeTraderArea" value="${bv.saleTakeTraderArea}">
            <input type="hidden" id="ptz_takeTraderAddress" value="${bv.saleTakeTraderAddress}">

            <input type="hidden" name="formToken" value="${formToken}"/>

            <input type="hidden" name="buyorderId" value="${bv.buyorderId}">
            <input type="hidden" id="buyorderNo" name="buyorderNo" value="${bv.buyorderNo}">
            <input type="hidden" name="oldDeliveryDirect" value="${bv.deliveryDirect}">
            <input type="hidden" name="oldTakeTraderId" value="${bv.takeTraderId}">
            <input type="hidden" name="oldTakeTraderName" value="${bv.takeTraderName}">
            <input type="hidden" name="oldTakeTraderContactId" value="${bv.takeTraderContactId}">
            <input type="hidden" name="oldTakeTraderContactName" value="${bv.takeTraderContactName}">
            <input type="hidden" name="oldTakeTraderContactMobile" value="${bv.takeTraderContactMobile}">
            <input type="hidden" name="oldTakeTraderContactTelephone" value="${bv.takeTraderContactTelephone}">
            <input type="hidden" name="oldTakeTraderAddressId" value="${bv.takeTraderAddressId}">
            <input type="hidden" name="oldTakeTraderArea" value="${bv.takeTraderArea}">
            <input type="hidden" name="oldTakeTraderAddress" value="${bv.takeTraderAddress}">
            <input type="hidden" name="oldLogisticsComments" value="${bv.logisticsComments}">
            <input type="hidden" name="oldInvoiceType" value="${bv.invoiceType}">
            <input type="hidden" name="oldInvoiceComments" value="${bv.invoiceComments}">
            <input type="hidden" id="isGiftOrder" value="${bv.isGift}">

            <button type="button" class="bt-bg-style bg-deep-green" onclick="editSubmit();">提交审核</button>
        </div>
    </form>
</div>


<%@ include file="../../common/footer.jsp" %>
