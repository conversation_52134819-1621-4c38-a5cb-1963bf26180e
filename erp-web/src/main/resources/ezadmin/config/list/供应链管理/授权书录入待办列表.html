<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>授权书录入待办列表</title> 
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="yTEP52ObJGo" datasource="erp-reportdatasource" fixednumber="2" fixednumberright="0" append_column_url="" append_row_url="" empty_show="" firstcol="numbers">
  <div id="appendHead"></div> 
  <div class="layui-fluid"> 
   <div class="layui-tab"> 
    <ul class="layui-tab-title" id="tab"> 
    </ul> 
   </div> 
   <form class="layui-form" id="search"> 
    <div class=" layui-inline "><label class="layui-form-label">产品名称</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="SKU_NAME" placeholder="请输入产品名称" style="" alias="G" jdbctype="" data="" datatype="" oper="LIKE" ></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">订货号</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="SKU_NO" placeholder="请输入订货号" style="" alias="G" jdbctype="" data="" datatype="" oper="" ></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">规格/型号</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="MODEL" placeholder="请输入规格/型号" style="" alias="CONCAT('-',G.MODEL,'-,-',G.SPEC,'-')" jdbctype="" data="" datatype="" oper="LIKE" ></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">品牌</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="BRAND_NAME" placeholder="请输入品牌" style="" alias="I" jdbctype="" data="" datatype="" oper="LIKE" ></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">供应商</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="TRADER_NAME" placeholder="请输入供应商名称" style="" alias="C" jdbctype="" data="" datatype="" oper="LIKE" ></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">产品归属</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="20" name="USER_ID_NAME" placeholder="" style="" alias="CONCAT('-',H.ASSIGNMENT_ASSISTANT_ID,'-,-',H.ASSIGNMENT_MANAGER_ID,'-')" jdbctype="NUMBER" data="SELECT DISTINCT 	CONCAT('-',u.USER_ID,'-') K, 	lower( u.USERNAME ) V  FROM 	V_CORE_SPU a 	LEFT JOIN T_USER u ON a.ASSIGNMENT_MANAGER_ID = u.USER_ID  WHERE 	u.USER_ID IS NOT NULL UNION SELECT DISTINCT 	CONCAT('-',u.USER_ID,'-') K, 	lower( u.USERNAME ) V  FROM 	V_CORE_SPU a 	LEFT JOIN T_USER u ON a.ASSIGNMENT_ASSISTANT_ID = u.USER_ID  WHERE 	u.USER_ID IS NOT NULL" datatype="KVSQLCACHE" oper="LIKE" ></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">关联采购单生效日期</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="daterange" name="VTIME" placeholder="" style="" alias="FROM_UNIXTIME(A.VALID_TIME / 1000, '%Y-%m-%d %H:%i:%S')" jdbctype="DATE" data="" datatype="" oper="BETWEEN" ></object>
     </div>
    </div>
   </form> 
   <hr class="layui-border-blue"> 
   <div class="btn-group   bd-highlight" id="tableButton">
       <button item_name="导出" name="导出" url="/ezadmin/list/export-yTEP52ObJGo"
               opentype="_BLANK_PARAM_COLUMN" windowname="" style type="table">导出</button>
   </div> 
   <table id="table" class="layui-table" style=" width:100%"> 
    <thead> 
     <tr id="column"> 
      <th type="rowbutton" id="rowbutton">
          <button item_name="前往维护" name="前往维护" url="/trader/tradersupplier/authList.do?traderId=${TRADER_ID}&amp;traderSupplierId=${TRADER_SUPPLIER_ID}" opentype="PARENT" windowname="" style area="" type="single"  item_label="前往维护" open_type="PARENT" item_url="/trader/tradersupplier/authList.do?traderId=${TRADER_ID}&amp;traderSupplierId=${TRADER_SUPPLIER_ID}" window_name=""  >前往维护</button></th>
      <th item_name="SKU_NAME" name="SKU_NAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">产品名称</th>
      <th item_name="SKU_NO" name="SKU_NO" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订货号</th>
      <th item_name="MODEL" name="MODEL" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">规格/型号</th>
      <th item_name="BRAND_NAME" name="BRAND_NAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">品牌</th>
      <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">供应商</th>
     </tr> 
    </thead> 
    <tbody> 
     <tr> 
      <td> express:</td> 
      <td colspan="100"> <pre id="express" class="layui-code" orderby="" groupby="GROUP BY	B.SKU,	A.TRADER_ID,	D.TRADER_SUPPLIER_ID">StringBuilder sql=new StringBuilder("

SELECT G.SKU_NAME  , G.SKU_NO  ,
       (case H.SPU_TYPE WHEN 316 THEN G.MODEL WHEN 1008 THEN G.MODEL ELSE G.SPEC END )   MODEL,
       I.BRAND_NAME  , C.TRADER_NAME  , D.TRADER_ID, D.TRADER_SUPPLIER_ID,

       FROM_UNIXTIME(MIN(A.VALID_TIME)/1000,'%Y-%m-%d') VTIME_MAX ,
       FROM_UNIXTIME(MAX(A.VALID_TIME)/1000,'%Y-%m-%d')  VTIME_MIN,
       FROM_UNIXTIME(A.VALID_TIME / 1000, '%Y-%m-%d') VTIME,H.SKU_SUPPLY_AUTH_ID,H.TRADER_SUPPLY_ID
FROM T_BUYORDER A
           INNER JOIN T_BUYORDER_GOODS B ON A.BUYORDER_ID = B.BUYORDER_ID
           inner JOIN T_TRADER C ON C.TRADER_ID = A.TRADER_ID
           LEFT JOIN T_TRADER_SUPPLIER D ON D.TRADER_ID = C.TRADER_ID
           INNER JOIN V_CORE_SKU G ON B.GOODS_ID = G.SKU_ID
           LEFT JOIN V_CORE_SPU H ON H.SPU_ID = G.SPU_ID
           LEFT JOIN T_BRAND I ON I.BRAND_ID = H.BRAND_ID
            LEFT JOIN
            (select E.TRADER_SUPPLY_ID,E.VALID_START_TIME,E.VALID_END_TIME,F.SKU_NO,F.SKU_ID,E.SKU_SUPPLY_AUTH_ID from T_SKU_SUPPLY_AUTH E
    LEFT JOIN T_SKU_SUPPLY_AUTH_DETAIL F ON E.SKU_SUPPLY_AUTH_ID = F.SKU_SUPPLY_AUTH_ID AND F.IS_DELETE = 0
WHERE E.IS_DELETE = 0) H ON H.TRADER_SUPPLY_ID = D.TRADER_SUPPLIER_ID AND H.SKU_NO = B.SKU
                                AND H.VALID_END_TIME &gt;= FROM_UNIXTIME(A.VALID_TIME/1000,'%Y-%m-%d')
                                AND H.VALID_START_TIME &lt;= FROM_UNIXTIME(A.VALID_TIME/1000,'%Y-%m-%d')
WHERE A.VALID_STATUS = 1 AND G.SKU_NO not in ( 'V127063','V251526','V256675','V253620','V251462','V140633')
  AND B.IS_DELETE = 0
  AND H.SKU_SUPPLY_AUTH_ID is null
"); 


 list=search(sql);
 return list;</pre> </td> 
     </tr> 
     <tr> 
      <td> count:</td> 
      <td colspan="100"> <pre id="count" class="layui-code"></pre> </td> 
     </tr> 
    </tbody> 
   </table> 
  </div> 
  <div id="appendFoot"></div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
 </body>
</html>