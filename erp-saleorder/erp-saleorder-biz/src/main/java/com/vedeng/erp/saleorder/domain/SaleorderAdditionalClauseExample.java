package com.vedeng.erp.saleorder.domain;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SaleorderAdditionalClauseExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_SALEORDER_ADDITIONAL_CLAUSE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_SALEORDER_ADDITIONAL_CLAUSE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_SALEORDER_ADDITIONAL_CLAUSE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_ADDITIONAL_CLAUSE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public SaleorderAdditionalClauseExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_ADDITIONAL_CLAUSE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_ADDITIONAL_CLAUSE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_ADDITIONAL_CLAUSE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_ADDITIONAL_CLAUSE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_ADDITIONAL_CLAUSE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_ADDITIONAL_CLAUSE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_ADDITIONAL_CLAUSE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_ADDITIONAL_CLAUSE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_ADDITIONAL_CLAUSE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_ADDITIONAL_CLAUSE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_SALEORDER_ADDITIONAL_CLAUSE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("ID is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("ID is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("ID =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("ID <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("ID >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ID >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("ID <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("ID <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("ID in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("ID not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("ID between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("ID not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdIsNull() {
            addCriterion("SALEORDER_ID is null");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdIsNotNull() {
            addCriterion("SALEORDER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdEqualTo(Integer value) {
            addCriterion("SALEORDER_ID =", value, "saleorderId");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdNotEqualTo(Integer value) {
            addCriterion("SALEORDER_ID <>", value, "saleorderId");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdGreaterThan(Integer value) {
            addCriterion("SALEORDER_ID >", value, "saleorderId");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("SALEORDER_ID >=", value, "saleorderId");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdLessThan(Integer value) {
            addCriterion("SALEORDER_ID <", value, "saleorderId");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdLessThanOrEqualTo(Integer value) {
            addCriterion("SALEORDER_ID <=", value, "saleorderId");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdIn(List<Integer> values) {
            addCriterion("SALEORDER_ID in", values, "saleorderId");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdNotIn(List<Integer> values) {
            addCriterion("SALEORDER_ID not in", values, "saleorderId");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdBetween(Integer value1, Integer value2) {
            addCriterion("SALEORDER_ID between", value1, value2, "saleorderId");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdNotBetween(Integer value1, Integer value2) {
            addCriterion("SALEORDER_ID not between", value1, value2, "saleorderId");
            return (Criteria) this;
        }

        public Criteria andSaleorderNoIsNull() {
            addCriterion("SALEORDER_NO is null");
            return (Criteria) this;
        }

        public Criteria andSaleorderNoIsNotNull() {
            addCriterion("SALEORDER_NO is not null");
            return (Criteria) this;
        }

        public Criteria andSaleorderNoEqualTo(String value) {
            addCriterion("SALEORDER_NO =", value, "saleorderNo");
            return (Criteria) this;
        }

        public Criteria andSaleorderNoNotEqualTo(String value) {
            addCriterion("SALEORDER_NO <>", value, "saleorderNo");
            return (Criteria) this;
        }

        public Criteria andSaleorderNoGreaterThan(String value) {
            addCriterion("SALEORDER_NO >", value, "saleorderNo");
            return (Criteria) this;
        }

        public Criteria andSaleorderNoGreaterThanOrEqualTo(String value) {
            addCriterion("SALEORDER_NO >=", value, "saleorderNo");
            return (Criteria) this;
        }

        public Criteria andSaleorderNoLessThan(String value) {
            addCriterion("SALEORDER_NO <", value, "saleorderNo");
            return (Criteria) this;
        }

        public Criteria andSaleorderNoLessThanOrEqualTo(String value) {
            addCriterion("SALEORDER_NO <=", value, "saleorderNo");
            return (Criteria) this;
        }

        public Criteria andSaleorderNoLike(String value) {
            addCriterion("SALEORDER_NO like", value, "saleorderNo");
            return (Criteria) this;
        }

        public Criteria andSaleorderNoNotLike(String value) {
            addCriterion("SALEORDER_NO not like", value, "saleorderNo");
            return (Criteria) this;
        }

        public Criteria andSaleorderNoIn(List<String> values) {
            addCriterion("SALEORDER_NO in", values, "saleorderNo");
            return (Criteria) this;
        }

        public Criteria andSaleorderNoNotIn(List<String> values) {
            addCriterion("SALEORDER_NO not in", values, "saleorderNo");
            return (Criteria) this;
        }

        public Criteria andSaleorderNoBetween(String value1, String value2) {
            addCriterion("SALEORDER_NO between", value1, value2, "saleorderNo");
            return (Criteria) this;
        }

        public Criteria andSaleorderNoNotBetween(String value1, String value2) {
            addCriterion("SALEORDER_NO not between", value1, value2, "saleorderNo");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseNosIsNull() {
            addCriterion("ADDITIONAL_CLAUSE_NOS is null");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseNosIsNotNull() {
            addCriterion("ADDITIONAL_CLAUSE_NOS is not null");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseNosEqualTo(String value) {
            addCriterion("ADDITIONAL_CLAUSE_NOS =", value, "additionalClauseNos");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseNosNotEqualTo(String value) {
            addCriterion("ADDITIONAL_CLAUSE_NOS <>", value, "additionalClauseNos");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseNosGreaterThan(String value) {
            addCriterion("ADDITIONAL_CLAUSE_NOS >", value, "additionalClauseNos");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseNosGreaterThanOrEqualTo(String value) {
            addCriterion("ADDITIONAL_CLAUSE_NOS >=", value, "additionalClauseNos");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseNosLessThan(String value) {
            addCriterion("ADDITIONAL_CLAUSE_NOS <", value, "additionalClauseNos");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseNosLessThanOrEqualTo(String value) {
            addCriterion("ADDITIONAL_CLAUSE_NOS <=", value, "additionalClauseNos");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseNosLike(String value) {
            addCriterion("ADDITIONAL_CLAUSE_NOS like", value, "additionalClauseNos");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseNosNotLike(String value) {
            addCriterion("ADDITIONAL_CLAUSE_NOS not like", value, "additionalClauseNos");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseNosIn(List<String> values) {
            addCriterion("ADDITIONAL_CLAUSE_NOS in", values, "additionalClauseNos");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseNosNotIn(List<String> values) {
            addCriterion("ADDITIONAL_CLAUSE_NOS not in", values, "additionalClauseNos");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseNosBetween(String value1, String value2) {
            addCriterion("ADDITIONAL_CLAUSE_NOS between", value1, value2, "additionalClauseNos");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseNosNotBetween(String value1, String value2) {
            addCriterion("ADDITIONAL_CLAUSE_NOS not between", value1, value2, "additionalClauseNos");
            return (Criteria) this;
        }

        public Criteria andInstallationAddressIsNull() {
            addCriterion("INSTALLATION_ADDRESS is null");
            return (Criteria) this;
        }

        public Criteria andInstallationAddressIsNotNull() {
            addCriterion("INSTALLATION_ADDRESS is not null");
            return (Criteria) this;
        }

        public Criteria andInstallationAddressEqualTo(String value) {
            addCriterion("INSTALLATION_ADDRESS =", value, "installationAddress");
            return (Criteria) this;
        }

        public Criteria andInstallationAddressNotEqualTo(String value) {
            addCriterion("INSTALLATION_ADDRESS <>", value, "installationAddress");
            return (Criteria) this;
        }

        public Criteria andInstallationAddressGreaterThan(String value) {
            addCriterion("INSTALLATION_ADDRESS >", value, "installationAddress");
            return (Criteria) this;
        }

        public Criteria andInstallationAddressGreaterThanOrEqualTo(String value) {
            addCriterion("INSTALLATION_ADDRESS >=", value, "installationAddress");
            return (Criteria) this;
        }

        public Criteria andInstallationAddressLessThan(String value) {
            addCriterion("INSTALLATION_ADDRESS <", value, "installationAddress");
            return (Criteria) this;
        }

        public Criteria andInstallationAddressLessThanOrEqualTo(String value) {
            addCriterion("INSTALLATION_ADDRESS <=", value, "installationAddress");
            return (Criteria) this;
        }

        public Criteria andInstallationAddressLike(String value) {
            addCriterion("INSTALLATION_ADDRESS like", value, "installationAddress");
            return (Criteria) this;
        }

        public Criteria andInstallationAddressNotLike(String value) {
            addCriterion("INSTALLATION_ADDRESS not like", value, "installationAddress");
            return (Criteria) this;
        }

        public Criteria andInstallationAddressIn(List<String> values) {
            addCriterion("INSTALLATION_ADDRESS in", values, "installationAddress");
            return (Criteria) this;
        }

        public Criteria andInstallationAddressNotIn(List<String> values) {
            addCriterion("INSTALLATION_ADDRESS not in", values, "installationAddress");
            return (Criteria) this;
        }

        public Criteria andInstallationAddressBetween(String value1, String value2) {
            addCriterion("INSTALLATION_ADDRESS between", value1, value2, "installationAddress");
            return (Criteria) this;
        }

        public Criteria andInstallationAddressNotBetween(String value1, String value2) {
            addCriterion("INSTALLATION_ADDRESS not between", value1, value2, "installationAddress");
            return (Criteria) this;
        }

        public Criteria andProdName103IsNull() {
            addCriterion("PROD_NAME_103 is null");
            return (Criteria) this;
        }

        public Criteria andProdName103IsNotNull() {
            addCriterion("PROD_NAME_103 is not null");
            return (Criteria) this;
        }

        public Criteria andProdName103EqualTo(String value) {
            addCriterion("PROD_NAME_103 =", value, "prodName103");
            return (Criteria) this;
        }

        public Criteria andProdName103NotEqualTo(String value) {
            addCriterion("PROD_NAME_103 <>", value, "prodName103");
            return (Criteria) this;
        }

        public Criteria andProdName103GreaterThan(String value) {
            addCriterion("PROD_NAME_103 >", value, "prodName103");
            return (Criteria) this;
        }

        public Criteria andProdName103GreaterThanOrEqualTo(String value) {
            addCriterion("PROD_NAME_103 >=", value, "prodName103");
            return (Criteria) this;
        }

        public Criteria andProdName103LessThan(String value) {
            addCriterion("PROD_NAME_103 <", value, "prodName103");
            return (Criteria) this;
        }

        public Criteria andProdName103LessThanOrEqualTo(String value) {
            addCriterion("PROD_NAME_103 <=", value, "prodName103");
            return (Criteria) this;
        }

        public Criteria andProdName103Like(String value) {
            addCriterion("PROD_NAME_103 like", value, "prodName103");
            return (Criteria) this;
        }

        public Criteria andProdName103NotLike(String value) {
            addCriterion("PROD_NAME_103 not like", value, "prodName103");
            return (Criteria) this;
        }

        public Criteria andProdName103In(List<String> values) {
            addCriterion("PROD_NAME_103 in", values, "prodName103");
            return (Criteria) this;
        }

        public Criteria andProdName103NotIn(List<String> values) {
            addCriterion("PROD_NAME_103 not in", values, "prodName103");
            return (Criteria) this;
        }

        public Criteria andProdName103Between(String value1, String value2) {
            addCriterion("PROD_NAME_103 between", value1, value2, "prodName103");
            return (Criteria) this;
        }

        public Criteria andProdName103NotBetween(String value1, String value2) {
            addCriterion("PROD_NAME_103 not between", value1, value2, "prodName103");
            return (Criteria) this;
        }

        public Criteria andProdName105IsNull() {
            addCriterion("PROD_NAME_105 is null");
            return (Criteria) this;
        }

        public Criteria andProdName105IsNotNull() {
            addCriterion("PROD_NAME_105 is not null");
            return (Criteria) this;
        }

        public Criteria andProdName105EqualTo(String value) {
            addCriterion("PROD_NAME_105 =", value, "prodName105");
            return (Criteria) this;
        }

        public Criteria andProdName105NotEqualTo(String value) {
            addCriterion("PROD_NAME_105 <>", value, "prodName105");
            return (Criteria) this;
        }

        public Criteria andProdName105GreaterThan(String value) {
            addCriterion("PROD_NAME_105 >", value, "prodName105");
            return (Criteria) this;
        }

        public Criteria andProdName105GreaterThanOrEqualTo(String value) {
            addCriterion("PROD_NAME_105 >=", value, "prodName105");
            return (Criteria) this;
        }

        public Criteria andProdName105LessThan(String value) {
            addCriterion("PROD_NAME_105 <", value, "prodName105");
            return (Criteria) this;
        }

        public Criteria andProdName105LessThanOrEqualTo(String value) {
            addCriterion("PROD_NAME_105 <=", value, "prodName105");
            return (Criteria) this;
        }

        public Criteria andProdName105Like(String value) {
            addCriterion("PROD_NAME_105 like", value, "prodName105");
            return (Criteria) this;
        }

        public Criteria andProdName105NotLike(String value) {
            addCriterion("PROD_NAME_105 not like", value, "prodName105");
            return (Criteria) this;
        }

        public Criteria andProdName105In(List<String> values) {
            addCriterion("PROD_NAME_105 in", values, "prodName105");
            return (Criteria) this;
        }

        public Criteria andProdName105NotIn(List<String> values) {
            addCriterion("PROD_NAME_105 not in", values, "prodName105");
            return (Criteria) this;
        }

        public Criteria andProdName105Between(String value1, String value2) {
            addCriterion("PROD_NAME_105 between", value1, value2, "prodName105");
            return (Criteria) this;
        }

        public Criteria andProdName105NotBetween(String value1, String value2) {
            addCriterion("PROD_NAME_105 not between", value1, value2, "prodName105");
            return (Criteria) this;
        }

        public Criteria andTraderNameIsNull() {
            addCriterion("TRADER_NAME is null");
            return (Criteria) this;
        }

        public Criteria andTraderNameIsNotNull() {
            addCriterion("TRADER_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andTraderNameEqualTo(String value) {
            addCriterion("TRADER_NAME =", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameNotEqualTo(String value) {
            addCriterion("TRADER_NAME <>", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameGreaterThan(String value) {
            addCriterion("TRADER_NAME >", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameGreaterThanOrEqualTo(String value) {
            addCriterion("TRADER_NAME >=", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameLessThan(String value) {
            addCriterion("TRADER_NAME <", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameLessThanOrEqualTo(String value) {
            addCriterion("TRADER_NAME <=", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameLike(String value) {
            addCriterion("TRADER_NAME like", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameNotLike(String value) {
            addCriterion("TRADER_NAME not like", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameIn(List<String> values) {
            addCriterion("TRADER_NAME in", values, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameNotIn(List<String> values) {
            addCriterion("TRADER_NAME not in", values, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameBetween(String value1, String value2) {
            addCriterion("TRADER_NAME between", value1, value2, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameNotBetween(String value1, String value2) {
            addCriterion("TRADER_NAME not between", value1, value2, "traderName");
            return (Criteria) this;
        }

        public Criteria andSaleCityIdIsNull() {
            addCriterion("SALE_CITY_ID is null");
            return (Criteria) this;
        }

        public Criteria andSaleCityIdIsNotNull() {
            addCriterion("SALE_CITY_ID is not null");
            return (Criteria) this;
        }

        public Criteria andSaleCityIdEqualTo(Integer value) {
            addCriterion("SALE_CITY_ID =", value, "saleCityId");
            return (Criteria) this;
        }

        public Criteria andSaleCityIdNotEqualTo(Integer value) {
            addCriterion("SALE_CITY_ID <>", value, "saleCityId");
            return (Criteria) this;
        }

        public Criteria andSaleCityIdGreaterThan(Integer value) {
            addCriterion("SALE_CITY_ID >", value, "saleCityId");
            return (Criteria) this;
        }

        public Criteria andSaleCityIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("SALE_CITY_ID >=", value, "saleCityId");
            return (Criteria) this;
        }

        public Criteria andSaleCityIdLessThan(Integer value) {
            addCriterion("SALE_CITY_ID <", value, "saleCityId");
            return (Criteria) this;
        }

        public Criteria andSaleCityIdLessThanOrEqualTo(Integer value) {
            addCriterion("SALE_CITY_ID <=", value, "saleCityId");
            return (Criteria) this;
        }

        public Criteria andSaleCityIdIn(List<Integer> values) {
            addCriterion("SALE_CITY_ID in", values, "saleCityId");
            return (Criteria) this;
        }

        public Criteria andSaleCityIdNotIn(List<Integer> values) {
            addCriterion("SALE_CITY_ID not in", values, "saleCityId");
            return (Criteria) this;
        }

        public Criteria andSaleCityIdBetween(Integer value1, Integer value2) {
            addCriterion("SALE_CITY_ID between", value1, value2, "saleCityId");
            return (Criteria) this;
        }

        public Criteria andSaleCityIdNotBetween(Integer value1, Integer value2) {
            addCriterion("SALE_CITY_ID not between", value1, value2, "saleCityId");
            return (Criteria) this;
        }

        public Criteria andSaleProvinceIdIsNull() {
            addCriterion("SALE_PROVINCE_ID is null");
            return (Criteria) this;
        }

        public Criteria andSaleProvinceIdIsNotNull() {
            addCriterion("SALE_PROVINCE_ID is not null");
            return (Criteria) this;
        }

        public Criteria andSaleProvinceIdEqualTo(Integer value) {
            addCriterion("SALE_PROVINCE_ID =", value, "saleProvinceId");
            return (Criteria) this;
        }

        public Criteria andSaleProvinceIdNotEqualTo(Integer value) {
            addCriterion("SALE_PROVINCE_ID <>", value, "saleProvinceId");
            return (Criteria) this;
        }

        public Criteria andSaleProvinceIdGreaterThan(Integer value) {
            addCriterion("SALE_PROVINCE_ID >", value, "saleProvinceId");
            return (Criteria) this;
        }

        public Criteria andSaleProvinceIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("SALE_PROVINCE_ID >=", value, "saleProvinceId");
            return (Criteria) this;
        }

        public Criteria andSaleProvinceIdLessThan(Integer value) {
            addCriterion("SALE_PROVINCE_ID <", value, "saleProvinceId");
            return (Criteria) this;
        }

        public Criteria andSaleProvinceIdLessThanOrEqualTo(Integer value) {
            addCriterion("SALE_PROVINCE_ID <=", value, "saleProvinceId");
            return (Criteria) this;
        }

        public Criteria andSaleProvinceIdIn(List<Integer> values) {
            addCriterion("SALE_PROVINCE_ID in", values, "saleProvinceId");
            return (Criteria) this;
        }

        public Criteria andSaleProvinceIdNotIn(List<Integer> values) {
            addCriterion("SALE_PROVINCE_ID not in", values, "saleProvinceId");
            return (Criteria) this;
        }

        public Criteria andSaleProvinceIdBetween(Integer value1, Integer value2) {
            addCriterion("SALE_PROVINCE_ID between", value1, value2, "saleProvinceId");
            return (Criteria) this;
        }

        public Criteria andSaleProvinceIdNotBetween(Integer value1, Integer value2) {
            addCriterion("SALE_PROVINCE_ID not between", value1, value2, "saleProvinceId");
            return (Criteria) this;
        }

        public Criteria andSaleProvinceNameIsNull() {
            addCriterion("SALE_PROVINCE_NAME is null");
            return (Criteria) this;
        }

        public Criteria andSaleProvinceNameIsNotNull() {
            addCriterion("SALE_PROVINCE_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andSaleProvinceNameEqualTo(String value) {
            addCriterion("SALE_PROVINCE_NAME =", value, "saleProvinceName");
            return (Criteria) this;
        }

        public Criteria andSaleProvinceNameNotEqualTo(String value) {
            addCriterion("SALE_PROVINCE_NAME <>", value, "saleProvinceName");
            return (Criteria) this;
        }

        public Criteria andSaleProvinceNameGreaterThan(String value) {
            addCriterion("SALE_PROVINCE_NAME >", value, "saleProvinceName");
            return (Criteria) this;
        }

        public Criteria andSaleProvinceNameGreaterThanOrEqualTo(String value) {
            addCriterion("SALE_PROVINCE_NAME >=", value, "saleProvinceName");
            return (Criteria) this;
        }

        public Criteria andSaleProvinceNameLessThan(String value) {
            addCriterion("SALE_PROVINCE_NAME <", value, "saleProvinceName");
            return (Criteria) this;
        }

        public Criteria andSaleProvinceNameLessThanOrEqualTo(String value) {
            addCriterion("SALE_PROVINCE_NAME <=", value, "saleProvinceName");
            return (Criteria) this;
        }

        public Criteria andSaleProvinceNameLike(String value) {
            addCriterion("SALE_PROVINCE_NAME like", value, "saleProvinceName");
            return (Criteria) this;
        }

        public Criteria andSaleProvinceNameNotLike(String value) {
            addCriterion("SALE_PROVINCE_NAME not like", value, "saleProvinceName");
            return (Criteria) this;
        }

        public Criteria andSaleProvinceNameIn(List<String> values) {
            addCriterion("SALE_PROVINCE_NAME in", values, "saleProvinceName");
            return (Criteria) this;
        }

        public Criteria andSaleProvinceNameNotIn(List<String> values) {
            addCriterion("SALE_PROVINCE_NAME not in", values, "saleProvinceName");
            return (Criteria) this;
        }

        public Criteria andSaleProvinceNameBetween(String value1, String value2) {
            addCriterion("SALE_PROVINCE_NAME between", value1, value2, "saleProvinceName");
            return (Criteria) this;
        }

        public Criteria andSaleProvinceNameNotBetween(String value1, String value2) {
            addCriterion("SALE_PROVINCE_NAME not between", value1, value2, "saleProvinceName");
            return (Criteria) this;
        }

        public Criteria andSaleCityNameIsNull() {
            addCriterion("SALE_CITY_NAME is null");
            return (Criteria) this;
        }

        public Criteria andSaleCityNameIsNotNull() {
            addCriterion("SALE_CITY_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andSaleCityNameEqualTo(String value) {
            addCriterion("SALE_CITY_NAME =", value, "saleCityName");
            return (Criteria) this;
        }

        public Criteria andSaleCityNameNotEqualTo(String value) {
            addCriterion("SALE_CITY_NAME <>", value, "saleCityName");
            return (Criteria) this;
        }

        public Criteria andSaleCityNameGreaterThan(String value) {
            addCriterion("SALE_CITY_NAME >", value, "saleCityName");
            return (Criteria) this;
        }

        public Criteria andSaleCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("SALE_CITY_NAME >=", value, "saleCityName");
            return (Criteria) this;
        }

        public Criteria andSaleCityNameLessThan(String value) {
            addCriterion("SALE_CITY_NAME <", value, "saleCityName");
            return (Criteria) this;
        }

        public Criteria andSaleCityNameLessThanOrEqualTo(String value) {
            addCriterion("SALE_CITY_NAME <=", value, "saleCityName");
            return (Criteria) this;
        }

        public Criteria andSaleCityNameLike(String value) {
            addCriterion("SALE_CITY_NAME like", value, "saleCityName");
            return (Criteria) this;
        }

        public Criteria andSaleCityNameNotLike(String value) {
            addCriterion("SALE_CITY_NAME not like", value, "saleCityName");
            return (Criteria) this;
        }

        public Criteria andSaleCityNameIn(List<String> values) {
            addCriterion("SALE_CITY_NAME in", values, "saleCityName");
            return (Criteria) this;
        }

        public Criteria andSaleCityNameNotIn(List<String> values) {
            addCriterion("SALE_CITY_NAME not in", values, "saleCityName");
            return (Criteria) this;
        }

        public Criteria andSaleCityNameBetween(String value1, String value2) {
            addCriterion("SALE_CITY_NAME between", value1, value2, "saleCityName");
            return (Criteria) this;
        }

        public Criteria andSaleCityNameNotBetween(String value1, String value2) {
            addCriterion("SALE_CITY_NAME not between", value1, value2, "saleCityName");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderNameIsNull() {
            addCriterion("TERMINAL_TRADER_NAME is null");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderNameIsNotNull() {
            addCriterion("TERMINAL_TRADER_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderNameEqualTo(String value) {
            addCriterion("TERMINAL_TRADER_NAME =", value, "terminalTraderName");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderNameNotEqualTo(String value) {
            addCriterion("TERMINAL_TRADER_NAME <>", value, "terminalTraderName");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderNameGreaterThan(String value) {
            addCriterion("TERMINAL_TRADER_NAME >", value, "terminalTraderName");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderNameGreaterThanOrEqualTo(String value) {
            addCriterion("TERMINAL_TRADER_NAME >=", value, "terminalTraderName");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderNameLessThan(String value) {
            addCriterion("TERMINAL_TRADER_NAME <", value, "terminalTraderName");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderNameLessThanOrEqualTo(String value) {
            addCriterion("TERMINAL_TRADER_NAME <=", value, "terminalTraderName");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderNameLike(String value) {
            addCriterion("TERMINAL_TRADER_NAME like", value, "terminalTraderName");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderNameNotLike(String value) {
            addCriterion("TERMINAL_TRADER_NAME not like", value, "terminalTraderName");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderNameIn(List<String> values) {
            addCriterion("TERMINAL_TRADER_NAME in", values, "terminalTraderName");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderNameNotIn(List<String> values) {
            addCriterion("TERMINAL_TRADER_NAME not in", values, "terminalTraderName");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderNameBetween(String value1, String value2) {
            addCriterion("TERMINAL_TRADER_NAME between", value1, value2, "terminalTraderName");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderNameNotBetween(String value1, String value2) {
            addCriterion("TERMINAL_TRADER_NAME not between", value1, value2, "terminalTraderName");
            return (Criteria) this;
        }

        public Criteria andSnCodeIsNull() {
            addCriterion("SN_CODE is null");
            return (Criteria) this;
        }

        public Criteria andSnCodeIsNotNull() {
            addCriterion("SN_CODE is not null");
            return (Criteria) this;
        }

        public Criteria andSnCodeEqualTo(String value) {
            addCriterion("SN_CODE =", value, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeNotEqualTo(String value) {
            addCriterion("SN_CODE <>", value, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeGreaterThan(String value) {
            addCriterion("SN_CODE >", value, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeGreaterThanOrEqualTo(String value) {
            addCriterion("SN_CODE >=", value, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeLessThan(String value) {
            addCriterion("SN_CODE <", value, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeLessThanOrEqualTo(String value) {
            addCriterion("SN_CODE <=", value, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeLike(String value) {
            addCriterion("SN_CODE like", value, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeNotLike(String value) {
            addCriterion("SN_CODE not like", value, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeIn(List<String> values) {
            addCriterion("SN_CODE in", values, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeNotIn(List<String> values) {
            addCriterion("SN_CODE not in", values, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeBetween(String value1, String value2) {
            addCriterion("SN_CODE between", value1, value2, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeNotBetween(String value1, String value2) {
            addCriterion("SN_CODE not between", value1, value2, "snCode");
            return (Criteria) this;
        }

        public Criteria andPrepaidReagentAmountIsNull() {
            addCriterion("PREPAID_REAGENT_AMOUNT is null");
            return (Criteria) this;
        }

        public Criteria andPrepaidReagentAmountIsNotNull() {
            addCriterion("PREPAID_REAGENT_AMOUNT is not null");
            return (Criteria) this;
        }

        public Criteria andPrepaidReagentAmountEqualTo(BigDecimal value) {
            addCriterion("PREPAID_REAGENT_AMOUNT =", value, "prepaidReagentAmount");
            return (Criteria) this;
        }

        public Criteria andPrepaidReagentAmountNotEqualTo(BigDecimal value) {
            addCriterion("PREPAID_REAGENT_AMOUNT <>", value, "prepaidReagentAmount");
            return (Criteria) this;
        }

        public Criteria andPrepaidReagentAmountGreaterThan(BigDecimal value) {
            addCriterion("PREPAID_REAGENT_AMOUNT >", value, "prepaidReagentAmount");
            return (Criteria) this;
        }

        public Criteria andPrepaidReagentAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("PREPAID_REAGENT_AMOUNT >=", value, "prepaidReagentAmount");
            return (Criteria) this;
        }

        public Criteria andPrepaidReagentAmountLessThan(BigDecimal value) {
            addCriterion("PREPAID_REAGENT_AMOUNT <", value, "prepaidReagentAmount");
            return (Criteria) this;
        }

        public Criteria andPrepaidReagentAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("PREPAID_REAGENT_AMOUNT <=", value, "prepaidReagentAmount");
            return (Criteria) this;
        }

        public Criteria andPrepaidReagentAmountIn(List<BigDecimal> values) {
            addCriterion("PREPAID_REAGENT_AMOUNT in", values, "prepaidReagentAmount");
            return (Criteria) this;
        }

        public Criteria andPrepaidReagentAmountNotIn(List<BigDecimal> values) {
            addCriterion("PREPAID_REAGENT_AMOUNT not in", values, "prepaidReagentAmount");
            return (Criteria) this;
        }

        public Criteria andPrepaidReagentAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("PREPAID_REAGENT_AMOUNT between", value1, value2, "prepaidReagentAmount");
            return (Criteria) this;
        }

        public Criteria andPrepaidReagentAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("PREPAID_REAGENT_AMOUNT not between", value1, value2, "prepaidReagentAmount");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseIsNull() {
            addCriterion("ADDITIONAL_CLAUSE is null");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseIsNotNull() {
            addCriterion("ADDITIONAL_CLAUSE is not null");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseEqualTo(String value) {
            addCriterion("ADDITIONAL_CLAUSE =", value, "additionalClause");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseNotEqualTo(String value) {
            addCriterion("ADDITIONAL_CLAUSE <>", value, "additionalClause");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseGreaterThan(String value) {
            addCriterion("ADDITIONAL_CLAUSE >", value, "additionalClause");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseGreaterThanOrEqualTo(String value) {
            addCriterion("ADDITIONAL_CLAUSE >=", value, "additionalClause");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseLessThan(String value) {
            addCriterion("ADDITIONAL_CLAUSE <", value, "additionalClause");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseLessThanOrEqualTo(String value) {
            addCriterion("ADDITIONAL_CLAUSE <=", value, "additionalClause");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseLike(String value) {
            addCriterion("ADDITIONAL_CLAUSE like", value, "additionalClause");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseNotLike(String value) {
            addCriterion("ADDITIONAL_CLAUSE not like", value, "additionalClause");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseIn(List<String> values) {
            addCriterion("ADDITIONAL_CLAUSE in", values, "additionalClause");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseNotIn(List<String> values) {
            addCriterion("ADDITIONAL_CLAUSE not in", values, "additionalClause");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseBetween(String value1, String value2) {
            addCriterion("ADDITIONAL_CLAUSE between", value1, value2, "additionalClause");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseNotBetween(String value1, String value2) {
            addCriterion("ADDITIONAL_CLAUSE not between", value1, value2, "additionalClause");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("CREATOR is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("CREATOR is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(Integer value) {
            addCriterion("CREATOR =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(Integer value) {
            addCriterion("CREATOR <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(Integer value) {
            addCriterion("CREATOR >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("CREATOR >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(Integer value) {
            addCriterion("CREATOR <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(Integer value) {
            addCriterion("CREATOR <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<Integer> values) {
            addCriterion("CREATOR in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<Integer> values) {
            addCriterion("CREATOR not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNull() {
            addCriterion("UPDATER is null");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNotNull() {
            addCriterion("UPDATER is not null");
            return (Criteria) this;
        }

        public Criteria andUpdaterEqualTo(Integer value) {
            addCriterion("UPDATER =", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotEqualTo(Integer value) {
            addCriterion("UPDATER <>", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThan(Integer value) {
            addCriterion("UPDATER >", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThanOrEqualTo(Integer value) {
            addCriterion("UPDATER >=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThan(Integer value) {
            addCriterion("UPDATER <", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThanOrEqualTo(Integer value) {
            addCriterion("UPDATER <=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterIn(List<Integer> values) {
            addCriterion("UPDATER in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotIn(List<Integer> values) {
            addCriterion("UPDATER not in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER not between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("ADD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("ADD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("ADD_TIME =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("ADD_TIME <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("ADD_TIME >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("ADD_TIME >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("ADD_TIME <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("ADD_TIME <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("ADD_TIME in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("ADD_TIME not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("ADD_TIME between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("ADD_TIME not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andModeTimeIsNull() {
            addCriterion("MODE_TIME is null");
            return (Criteria) this;
        }

        public Criteria andModeTimeIsNotNull() {
            addCriterion("MODE_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andModeTimeEqualTo(Date value) {
            addCriterion("MODE_TIME =", value, "modeTime");
            return (Criteria) this;
        }

        public Criteria andModeTimeNotEqualTo(Date value) {
            addCriterion("MODE_TIME <>", value, "modeTime");
            return (Criteria) this;
        }

        public Criteria andModeTimeGreaterThan(Date value) {
            addCriterion("MODE_TIME >", value, "modeTime");
            return (Criteria) this;
        }

        public Criteria andModeTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("MODE_TIME >=", value, "modeTime");
            return (Criteria) this;
        }

        public Criteria andModeTimeLessThan(Date value) {
            addCriterion("MODE_TIME <", value, "modeTime");
            return (Criteria) this;
        }

        public Criteria andModeTimeLessThanOrEqualTo(Date value) {
            addCriterion("MODE_TIME <=", value, "modeTime");
            return (Criteria) this;
        }

        public Criteria andModeTimeIn(List<Date> values) {
            addCriterion("MODE_TIME in", values, "modeTime");
            return (Criteria) this;
        }

        public Criteria andModeTimeNotIn(List<Date> values) {
            addCriterion("MODE_TIME not in", values, "modeTime");
            return (Criteria) this;
        }

        public Criteria andModeTimeBetween(Date value1, Date value2) {
            addCriterion("MODE_TIME between", value1, value2, "modeTime");
            return (Criteria) this;
        }

        public Criteria andModeTimeNotBetween(Date value1, Date value2) {
            addCriterion("MODE_TIME not between", value1, value2, "modeTime");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNull() {
            addCriterion("IS_DELETE is null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNotNull() {
            addCriterion("IS_DELETE is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteEqualTo(Integer value) {
            addCriterion("IS_DELETE =", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotEqualTo(Integer value) {
            addCriterion("IS_DELETE <>", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThan(Integer value) {
            addCriterion("IS_DELETE >", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThanOrEqualTo(Integer value) {
            addCriterion("IS_DELETE >=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThan(Integer value) {
            addCriterion("IS_DELETE <", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThanOrEqualTo(Integer value) {
            addCriterion("IS_DELETE <=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIn(List<Integer> values) {
            addCriterion("IS_DELETE in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotIn(List<Integer> values) {
            addCriterion("IS_DELETE not in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteBetween(Integer value1, Integer value2) {
            addCriterion("IS_DELETE between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotBetween(Integer value1, Integer value2) {
            addCriterion("IS_DELETE not between", value1, value2, "isDelete");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_SALEORDER_ADDITIONAL_CLAUSE
     *
     * @mbg.generated do_not_delete_during_merge Mon Apr 07 16:24:30 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_SALEORDER_ADDITIONAL_CLAUSE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}