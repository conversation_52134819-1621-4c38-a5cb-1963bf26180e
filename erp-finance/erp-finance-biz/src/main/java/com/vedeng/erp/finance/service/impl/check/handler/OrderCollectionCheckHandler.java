package com.vedeng.erp.finance.service.impl.check.handler;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.aftersale.dto.AfterSalesDetailApiDto;
import com.vedeng.erp.aftersale.dto.AfterSalesDto;
import com.vedeng.erp.aftersale.service.AfterSalesApiService;
import com.vedeng.erp.finance.service.AbstractCheckHandler;
import com.vedeng.erp.finance.dto.InvoiceCheckRequestDto;
import com.vedeng.erp.finance.dto.InvoiceCheckResultDto;
import com.vedeng.erp.finance.enums.CheckHandlerEnum;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.saleorder.enums.AfterSalesCollectionStatusEnum;
import com.vedeng.erp.saleorder.enums.AfterSalesReceivePaymentStatusEnum;
import com.vedeng.erp.saleorder.enums.SaleOrderPaymentStatusEnum;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 订单收款校验
 */
@Service
@Slf4j
public class OrderCollectionCheckHandler extends AbstractCheckHandler {

    @Autowired
    private SaleOrderApiService saleOrderApiService;
    @Autowired
    private AfterSalesApiService afterSalesApiService;

    @Override
    public void handleSalesCheck(InvoiceCheckRequestDto invoiceCheckRequestDto, InvoiceCheckResultDto invoiceCheckResultDto) {
        log.info("订单收款校验-销售,invoiceCheckRequestDto:{}", JSON.toJSONString(invoiceCheckRequestDto));
        SaleorderInfoDto saleOrder = saleOrderApiService.getBySaleOrderId(invoiceCheckRequestDto.getRelatedId());
        if (Objects.isNull(saleOrder)) {
            throw new ServiceException("订单收款校验-销售,订单不存在");
        }
        Integer paymentStatus = saleOrder.getPaymentStatus();
        if (!SaleOrderPaymentStatusEnum.ALL.getCode().equals(paymentStatus)) {
            CheckHandlerEnum checkHandlerEnum = getCheckHandlerEnum();
            String paymentStatusStr = SaleOrderPaymentStatusEnum.getDesc(paymentStatus);
            String promptText = StrUtil.format(checkHandlerEnum.getPromptText(), paymentStatusStr);
            buildResult(invoiceCheckResultDto, promptText);
        }
    }

    @Override
    public void handleAfterCheck(InvoiceCheckRequestDto invoiceCheckRequestDto, InvoiceCheckResultDto invoiceCheckResultDto) {
        log.info("订单收款校验-售后,invoiceCheckRequestDto:{}", JSON.toJSONString(invoiceCheckRequestDto));
        AfterSalesDto afterSales = afterSalesApiService.getAfterSalesById(invoiceCheckRequestDto.getRelatedId());
        if (Objects.isNull(afterSales)) {
            throw new ServiceException("订单收款校验-售后,售后单不存在");
        }
        Integer amountCollectionStatus = afterSales.getAmountCollectionStatus();
        if (Objects.isNull(amountCollectionStatus)) {
            AfterSalesDetailApiDto afterSalesDetailApiDto = afterSalesApiService.getAfterSalesDetailByAfterSalesId(invoiceCheckRequestDto.getRelatedId());
            Integer receivePaymentStatus = afterSalesDetailApiDto.getReceivePaymentStatus();
            if (!AfterSalesReceivePaymentStatusEnum.ALL.getCode().equals(receivePaymentStatus)) {
                CheckHandlerEnum checkHandlerEnum = getCheckHandlerEnum();
                String receivePaymentStatusStr = AfterSalesCollectionStatusEnum.getDesc(receivePaymentStatus);
                String promptText = StrUtil.format(checkHandlerEnum.getPromptText(), receivePaymentStatusStr);
                buildResult(invoiceCheckResultDto, promptText);
            }
        } else {
            if (!AfterSalesCollectionStatusEnum.ALL.getCode().equals(amountCollectionStatus)) {
                CheckHandlerEnum checkHandlerEnum = getCheckHandlerEnum();
                String amountCollectionStatusStr = AfterSalesCollectionStatusEnum.getDesc(amountCollectionStatus);
                String promptText = StrUtil.format(checkHandlerEnum.getPromptText(), amountCollectionStatusStr);
                buildResult(invoiceCheckResultDto, promptText);
            }
        }
    }
}
