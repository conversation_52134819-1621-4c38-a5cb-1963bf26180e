package com.vedeng.erp.system.feign;

import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.common.feign.constants.ServerConstants;
import com.vedeng.erp.system.domain.dto.BankBodyDto;
import feign.Headers;
import feign.Param;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

import javax.inject.Named;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/15 13:37
 **/
@FeignApi(serverName = ServerConstants.YXB_ADMIN_SERVER)
public interface YxbApi {


    @Headers({"Content-Type: application/json", "Accept: application/json","token: {token}"})
    @RequestLine("POST /api/syncBaseBank")
    RestfulResult<String> syncBaseBank(@RequestBody BankBodyDto data, @Param("token") String token);
}
