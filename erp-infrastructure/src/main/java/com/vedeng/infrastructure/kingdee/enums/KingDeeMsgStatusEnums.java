package com.vedeng.infrastructure.kingdee.enums;

import com.vedeng.common.core.exception.ServiceException;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶消息枚举类
 * @date 2022/8/29 11:32
 */
public enum KingDeeMsgStatusEnums {

    /**
     * 消息类型
     */
    SEND(1, "已发送"),
    CONSUME(2, "已消费"),
    RETRY(3, "重新消费"),
    NO_HANDLE(4, "无需处理");

    /**
     * 状态值
     */
    private final Integer status;

    /**
     * 描述
     */
    private final String desc;

    KingDeeMsgStatusEnums(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 获取金蝶业务枚举
     *
     * @param status 状态
     * @return KingDeeBizEnums
     */
    public static KingDeeMsgStatusEnums getEnum(Integer status) {
        return Arrays.stream(KingDeeMsgStatusEnums.values())
                .filter(enums -> enums.getStatus().equals(status))
                .findFirst().orElseThrow(() -> new ServiceException("无法获取对应的金蝶消息状态"));
    }



}
