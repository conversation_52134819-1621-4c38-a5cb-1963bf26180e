package com.vedeng.goods.service.impl;

import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.common.constants.Contant;
import com.github.pagehelper.PageHelper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.shiro.JedisUtils;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.goods.dao.BrandGenerateMapper;
import com.vedeng.goods.model.BrandGenerate;
import com.vedeng.goods.model.BrandGenerateExample;
import net.sf.json.JSONArray;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.util.ObjectUtils;
import com.vedeng.goods.model.Brand;
import com.vedeng.goods.service.BrandService;

import javax.annotation.Resource;

@Service("brandService")
public class BrandServiceImpl extends BaseServiceimpl implements BrandService {

	@Resource
	private BrandGenerateMapper brandGenerateMapper;

	public static Logger logger = LoggerFactory.getLogger(BrandServiceImpl.class);

	@Override
	public List<Brand> getAllBrand(Brand brand) {
		String url = httpUrl + "goods/brand/getallbrand.htm";
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<List<Brand>>> TypeRef2 = new TypeReference<ResultInfo<List<Brand>>>() {};
		try {
			ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, brand,clientId,clientKey, TypeRef2);
			List<Brand> brandList = (List<Brand>) result2.getData();
			return brandList;
		} catch (IOException e) {
			return null;
		}
	}

	@Override
	public Map<String, Object> getBrandListPage(Brand brand, Page page) {
		List<Brand> list = null;Map<String,Object> map = new HashMap<>();
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<List<Brand>>> TypeRef = new TypeReference<ResultInfo<List<Brand>>>() {};
			String url=httpUrl + "goods/brand/getbrandlistpage.htm";
			
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, brand,clientId,clientKey, TypeRef,page);
			
			list = (List<Brand>) result.getData();
			page = result.getPage();
			if(list != null && list.size() > 0){
				for (Brand b : list) {
					if(ObjectUtils.notEmpty(b.getLogoUri())){
						b.setLogoUriName(b.getFileName());
					}
				}
			}
			map.put("list", list);map.put("page", page);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return map;
	}

	
	@Override
	public ResultInfo<?> saveBrand(Brand brand) {
		ResultInfo<?> result = null;
		
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Brand>> TypeRef = new TypeReference<ResultInfo<Brand>>() {};
		String url=httpUrl + "goods/brand/addbrand.htm";
		
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, brand,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}

	@Override
	public Brand getBrandByKey(Brand brand) {
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Brand>> TypeRef = new TypeReference<ResultInfo<Brand>>() {};
		String url=httpUrl + "goods/brand/getbrandbykey.htm";
		
		ResultInfo<?> result;
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, brand,clientId,clientKey, TypeRef);
			brand = (Brand)result.getData();
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return brand;
	}

	@Override
	public ResultInfo<?> editBrand(Brand brand) {
		ResultInfo<?> result = null;
		
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Brand>> TypeRef = new TypeReference<ResultInfo<Brand>>() {};
		String url=httpUrl + "goods/brand/editbrand.htm";
		
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, brand,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}

	@Override
	public ResultInfo<?> delBrand(Brand brand) {
		ResultInfo<?> result = null;
		
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Brand>> TypeRef = new TypeReference<ResultInfo<Brand>>() {};
		String url=httpUrl + "goods/brand/delbrand.htm";
		
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, brand,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			logger.error(Contant.ERROR_MSG, e);
			return new ResultInfo<>();
		}
		return result;
	}

	@Override
	public List<BrandGenerate> getBrandInfoByParam() {
		try {
			List<BrandGenerate> list = null;
			String ALL_BRAND = dbType + ErpConst.ALL_BRAND;
			if (JedisUtils.exists(ALL_BRAND)) {
				String json = JedisUtils.get(ALL_BRAND);
				JSONArray jsonArray = JSONArray.fromObject(json);
				list = (List<BrandGenerate>) JSONArray.toCollection(jsonArray, BrandGenerate.class);
			} else {
				list = brandGenerateMapper.getBrandInfoByParam(null);

				JedisUtils.set(ALL_BRAND, JsonUtils.convertConllectionToJsonStr(list), 86400);
			}
			return list;
		}catch (Exception e){
			logger.error("",e);
			//redis异常
			return brandGenerateMapper.getBrandInfoByParam(null);
		}
	}

	@Override
	public List<BrandGenerate> searchBrand(String keyword) {
		PageHelper.startPage(1,100);
		BrandGenerateExample example= new BrandGenerateExample();
		//按照品牌名称搜索
		example.createCriteria().andBrandNameLike("%" + keyword + "%");
		return brandGenerateMapper.selectByExample(example);
	}
}
