package com.vedeng.crm.business.quote.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 报价单商品表尾导出vo
 */
@Data
@ContentRowHeight(40)
public class QuoteOrderGoodsTailExportVo {

    @ExcelProperty(value = "总价")
    @ColumnWidth(10)
    private BigDecimal totalAmount;

    @ExcelProperty(value = "sku种类数量")
    @ColumnWidth(20)
    private Integer skuCount;

    @ExcelProperty(value = "产品件数")
    @ColumnWidth(20)
    private Integer productCount;
}


