<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<div id="app">
    <el-container>
        <el-main>
            <el-row type="flex" justify="center">
                <el-col :span="2" style="width: 100px; text-align: right; line-height: 36px;padding-right:10px;font-size:14px;">
                    制单客户:
                </el-col>
                <el-col :span="22" style="width: 300px;">
                    <el-select v-model="selectedTraderId" size="small" placeholder="请选择制单客户" @change="handleTraderChange" style="width: 100%;">
                        <el-option
                                v-for="trader in traders"
                                :key="trader.traderId"
                                :label="trader.traderName"
                                :value="trader.traderId">
                        </el-option>
                    </el-select>
                </el-col>
            </el-row>

            <el-row type="flex" justify="center" style="margin-top: 15px;">
                <el-col :span="2" style="width: 100px; text-align: right; line-height: 36px;padding-right:10px;font-size:14px;">
                    导入文件:
                </el-col>
                <el-col :span="22" style="width: 300px;">
                    <el-upload
                            class="upload-demo"
                            ref="upload"
                            multiple="false"
                            action="/orderstream/jdsaleorder/import.do"
                            show-file-list="false"
                            accept=".xlsx,.xls"
                            limit="1"
                            :before-upload="beforeUpload"
                            :on-change="onChangeToolFile"
                            :on-remove="onRemoveFile"
                            :on-success="onSuccess"
                            :file-list="fileList"
                            :auto-upload="false">
                        <el-button slot="trigger" size="small" type="primary">添加文件</el-button>
                    </el-upload>
                </el-col>
            </el-row>

            <el-row type="flex" justify="center" style="margin-top: 15px;">
                <el-col :span="2" style="width: 100px;"></el-col>
                <el-col :span="22" style="font-size:12px; width: 300px; text-align: left;">
                    <div>友情提示：<br>
                        1.上传文件格式仅支持Excel格式<br>
                        2.上传文件不要超过10M<br>
                        3.点击下载 <el-link href="/static/template/订单模板.xls" :underline="false" type="primary">导入模板</el-link>
                    </div>
                </el-col>
            </el-row>

            <el-row type="flex" justify="center" style="margin-top: 35px;">
                <el-col :span="2" style="width: 100px;"></el-col>
                <el-col :span="22" style="font-size:12px; width: 300px; text-align: left;">
                    <el-button size="small" type="primary" @click="submitUpload">提交导入</el-button>
<%--                    <el-link :underline="false" type="primary" class="addtitle" href="javascript:void(0);" tabTitle='{"num":"1","link":"/ezadmin/list/list-jd_saleorder","title":"制单结果"}'>查看所有制单结果</el-link>--%>
                </el-col>
            </el-row>

        </el-main>

    </el-container>
</div>

<script type="text/javascript">

    new Vue({
        el: '#app',

        data() {
            return {
                url: '',
                fileList: [],
                list: [],
                traders: [],
                selectedTraderId: '',
            };

        },
        mounted() {
            this.fetchTraders();
        },

        methods: {

            closeThis() {
                parent.layer.close(index);
            },

            async fetchTraders() {
                try {
                    const response = await fetch('/orderstream/jdsaleorder/getTraderListForImport.do');
                    const data = await response.json();
                    this.traders = data.data;
                } catch (error) {
                    console.error('获取制单客户数据失败', error);
                }
            },

            handleTraderChange(value) {
                this.selectedTraderId = value;
            },

            beforeUpload(file) {
                let size = file.size / 1024 / 1024;
                console.log("上传文件大小为: " + size)
                if (size > 10) {
                    this.$message.warning('文件大小不得超过10M');
                    return false;
                }
            },

            onChangeToolFile(file, fileList) {
                if (fileList.length > 0) {
                    this.fileList = [fileList[fileList.length - 1]]  // 这一步，是 展示最后一次选择的文件
                }
            },
            onRemoveFile(file, fileList){
                this.fileList = fileList;
            },
            submitUpload() {
                if (this.selectedTraderId === '') {
                    this.$message({
                        message: '请选择制单客户！',
                        duration: 1000,
                        showClose: true,
                        type: 'error'
                    });
                    return;
                }
                if (this.fileList.length === 0) {
                    this.$message({
                        message: '请选择文件！',
                        duration: 1000,
                        showClose: true,
                        type: 'error'
                    });
                    return;
                }
                const formData = new FormData();
                formData.append('file', this.fileList[0].raw);
                formData.append('traderId', this.selectedTraderId);
                // 手动发送 formData
                fetch('/orderstream/jdsaleorder/import.do', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code == 0) {
                        this.$message({
                            message: '导入成功',
                            type: 'success',
                            duration: 1000,
                            showClose: true,
                            onClose: () => {
                                layer.alert("订单正在生成中，点击确认查看制单结果", function (index) {
                                    // window.location.reload();
                                    // goJdSaleorderlist();
                                    if (window.top !== window.self) {
                                        window.parent.location.reload();
                                        console.log("当前页面被嵌入在 iframe 中");
                                    } else {
                                        // console.log("当前页面没有被嵌入在 iframe 中");
                                        window.location.reload();
                                    }
                                });
                            }
                        });
                    } else {
                        this.$message({
                            message: data.message,
                            type: 'error'
                        });
                    }
                })
                .catch(error => {
                    console.error('导入失败', error);
                    this.$message({
                        message: '导入失败',
                        type: 'error'
                    });
                });
            },
            handleRemove(file, fileList) {
                console.log(file, fileList);
            },
            handlePreview(file) {
                console.log(file);
            },
            onSuccess(data) {

            },

        }

    });

</script>
</body>
</html>