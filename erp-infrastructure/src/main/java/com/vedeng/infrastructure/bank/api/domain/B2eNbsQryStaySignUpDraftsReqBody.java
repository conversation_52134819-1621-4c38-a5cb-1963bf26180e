package com.vedeng.infrastructure.bank.api.domain;

import cn.hutool.core.util.StrUtil;
import com.vedeng.infrastructure.bank.api.domain.base.BankResponse;
import lombok.Data;

/**
 * @Description 签收对应的Dto see:http://wiki.ivedeng.com/pages/viewpage.action?pageId=*********
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/10/11
 */
@Data
public class B2eNbsQryStaySignUpDraftsReqBody {
    /**
     * 客户基数请求流水号
     */
    private String trnId;
    /**
     * 签约账号
     */
    private String custAccount;

    /**
     * 查询类型(不传查全部)
     *
     * 01:提示承兑签收（承兑申请签收）
     * 02:提示收票签收（出票交付签收）
     * 03:背书转让签收
     * 04:质押签收（质押申请签收）
     * 05:质押解除签收
     * 06:提示付款签收（委托收款申请签收）
     * 07:保证签收（保证申请签收）
     * 08:同意清偿申请签收
     * 09:不可转让撤销签收（不得转让撤销申请签收）
     */
    private String queryType;

    /**
     * 当前页码(从1开始)，不传默认为1
     */
    private String pageNo;

    /**
     * 每页数据条数（默认10条，最大每页100条）
     */
    private String pageSize;

    /**
     * 票号
     */
    private String billNo;

    /**
     * 票据类型
     */
    private String billType;

    /**
     * 票据金额范围起
     */
    private String  minBillMoney;

    /**
     * 票据金额范围止	15,2
     */
    private String maxBillMoney;
    /**
     *出票日起 yyyy-MM-dd
     */
    private String beginAcptDt;
    /**
     *出票日止 yyyy-MM-dd
     */
    private String endAcptDt;
    /**
     *票面到期日起yyyy-MM-dd
     */
    private String beginEndDate;
    /**
     *票面到期日止yyyy-MM-dd
     */
    private String endDate;

    /**
     * 校验必填参数
     *
     * @param response
     * @return true 如果必填参数有效，false 如果缺失
     */
    public boolean validate(BankResponse response) {
        if (StrUtil.isBlank(trnId)) {
            //参数校验失败，通用返回
            response.checkParamResponseHeader(response, "trnId不能为空");
            return false;
        }
        if (StrUtil.isBlank(custAccount)) {
            //参数校验失败，通用返回
            response.checkParamResponseHeader(response, "custAccount不能为空");
            return false;
        }
        if (StrUtil.isBlank(queryType)) {
            //参数校验失败，通用返回
            response.checkParamResponseHeader(response, "queryType不能为空");
            return false;
        }
        if (StrUtil.isBlank(billNo)) {
            //参数校验失败，通用返回
            response.checkParamResponseHeader(response, "billNo不能为空");
            return false;
        }
        if (StrUtil.isBlank(billType)) {
            //参数校验失败，通用返回
            response.checkParamResponseHeader(response, "billType不能为空");
            return false;
        }


        return true;
    }




}
