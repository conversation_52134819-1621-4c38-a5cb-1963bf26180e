package com.vedeng.erp.trader.domain.dto;

import lombok.Data;

/**
 * 终端360详情
 */
@Data
public class TraderCustomerTerminalPortraitDto {

    /**
     * 终端名称
     */
    private String hosName;
    /**
     * 终端别名
     */
    private String aliasName;
    /**
     * 终端ID
     */
    private String uniqueId;
    /**
     * 终端客户类型
     */
    private String hosTerminalType;
    /**
     * 终端类型
     */
    private String hosModel;
    /**
     * 机构评级
     */
    private String hosLevel;
    /**
     * 机构类型
     */
    private String hosType;
    /**
     * 机构类型-子集类 eg. 0康复、1医美、2口腔、3眼科、4骨科、5妇产、6其他
     */
    private String hosSmallType;
    /**
     * 经营状态
     */
    private String businessStatus;
    /**
     * 法人
     */
    private String legalRepresentative;
    /**
     * 床位
     */
    private String bedCount;
    /**
     * 科室
     */
    private String hosDepartmentList;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区
     */
    private String area;
    /**
     * 统一社会信用代码
     */
    private String unifiedSocialCreditCode;
    /**
     * 经营范围
     */
    private String businessScope;
    /**
     * 注册资本
     */
    private String funding;
    /**
     * 注册地址
     */
    private String regAddr;
    /**
     * 电话
     */
    private String tel;
    /**
     * 手机
     */
    private String mobile;
    /**
     * 注册日期
     */
    private String firstRegDate;
}
