package com.vedeng.erp.system.common.aspectj;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.erp.system.common.annotation.CustomDataOperAnnotation;
import com.vedeng.erp.system.common.enums.CustomDataOperTypeEnums;
import com.vedeng.erp.system.dto.CustomDataOperDto;
import com.vedeng.erp.system.service.CustomDataOperApiService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.CodeSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 自定义操作aop
 * @date 2022/7/15 23:43
 */
@Component
@Aspect
@Slf4j
public class CustomDataOperAspectj {


    @Autowired
    private CustomDataOperApiService customDataOperApiService;


    /**
     * 配置织入点
     */
    @Pointcut("@annotation(operAnnotation)")
    public void logPointCut(CustomDataOperAnnotation operAnnotation) {
    }


    @Around(value = "logPointCut(operAnnotation)", argNames = "pjp,operAnnotation")
    public Object around(ProceedingJoinPoint pjp, CustomDataOperAnnotation operAnnotation) throws Throwable {
        log.info("自定义新增数据日志-->执行方法前处理");
        //1.获取参数，根据注解参数查询出订单原始数据
        try {
            Map<String, Object> map = getNameAndValue(pjp);
            String parameter = operAnnotation.parameter();
            Object value = map.get(parameter);
            Set<Integer> idSets = new HashSet<>();
            if (value instanceof List) {
                List<Integer> ids = (List<Integer>) value;
                idSets.addAll(new HashSet<>(ids));
            }
            if (value instanceof Integer) {
                idSets.add((Integer) value);
            }
            this.byOperTypeSave(idSets, operAnnotation);
        } catch (Exception e) {
            log.error("参数类型异常", e);
        }
        return pjp.proceed();
    }

    private void byOperTypeSave(Set<Integer> ids, CustomDataOperAnnotation operAnnotation) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        ids.forEach(id -> {
            CustomDataOperDto customDataOperDto = CustomDataOperDto.builder()
                    .belongerId(currentUser.getId())
                    .relatedId(id)
                    .bizType(operAnnotation.operBizType().getType())
                    .build();
            switch (operAnnotation.dataOperType()) {
                case TOP:
                    customDataOperDto.setCreator(currentUser.getId());
                    customDataOperDto.setUpdater(currentUser.getId());
                    customDataOperDto.setBelonger(currentUser.getUsername());
                    customDataOperDto.setOperTime(DateUtil.date());
                    customDataOperDto.setOperType(CustomDataOperTypeEnums.TOP.getType());
                    customDataOperApiService.save(customDataOperDto);
                    break;
                case UN_TOP:
                    // 取消置顶相当于删除置顶的数据
                    customDataOperDto.setOperType(CustomDataOperTypeEnums.TOP.getType());
                    customDataOperApiService.delete(customDataOperDto);
                    break;
                case ATTENTION:
                    customDataOperDto.setCreator(currentUser.getId());
                    customDataOperDto.setUpdater(currentUser.getId());
                    customDataOperDto.setBelonger(currentUser.getUsername());
                    customDataOperDto.setOperTime(DateUtil.date());
                    customDataOperDto.setOperType(CustomDataOperTypeEnums.ATTENTION.getType());
                    customDataOperApiService.save(customDataOperDto);
                    break;
                case CANCEL_ATTENTION:
                    // 取消关注相当于删除关注的数据
                    customDataOperDto.setOperType(CustomDataOperTypeEnums.ATTENTION.getType());
                    customDataOperApiService.delete(customDataOperDto);
                    break;
                default:
                    break;
            }

        });

    }

    /**
     * 获取参数Map集合
     *
     * @param joinPoint
     * @return
     */
    Map<String, Object> getNameAndValue(ProceedingJoinPoint joinPoint) {
        Object[] paramValues = joinPoint.getArgs();
        String[] paramNames = ((CodeSignature) joinPoint.getSignature()).getParameterNames();
        Map<String, Object> param = new HashMap<>(paramValues.length);
        for (int i = 0; i < paramNames.length; i++) {
            param.put(paramNames[i], paramValues[i]);
        }
        return param;
    }
}
