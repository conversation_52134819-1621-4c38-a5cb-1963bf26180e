package com.vedeng.goods.manager.validator;

import com.baidu.unbiz.fluentvalidator.FluentValidator;
import com.baidu.unbiz.fluentvalidator.Result;
import com.baidu.unbiz.fluentvalidator.ResultCollectors;
import com.vedeng.common.validator.MaxLengthValidate;
import com.vedeng.common.validator.NumberValidatorHandler;
import com.vedeng.common.validator.StringValidatorHandler;
import com.vedeng.goods.manager.rule.GoodsValidTypeEnum;
import com.vedeng.goods.manager.validator.model.SpuValidObject;
import com.vedeng.goods.utils.NamingUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.beans.PropertyDescriptor;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class SpuBasicValidator extends GoodsValidator<SpuValidObject> {

    private static final List<String> IGNORE_PROP_NAMES = Arrays.asList("class", "spuType", "showName", "spuName",
            "specsModel", "assignmentAssistantId", "assignmentManagerId");

    private SpuBasicValidator() {
        super(GoodsValidTypeEnum.SPU);
    }

    public static SpuBasicValidator getInstance() {
        return new SpuBasicValidator();
    }

    @Override
    public Result validate(SpuValidObject requestData) {
        FluentValidator validator = getValidator();

        PropertyDescriptor[] propertyDescriptors = GoodsValidator.getPropertyDesc(requestData.getClass());
        goodsValidContext.addProperties(propertyDescriptors, requestData, IGNORE_PROP_NAMES);

        if (Boolean.TRUE.equals(requestData.getHasRegistrationCert())) {
            validator.on(requestData.getSpuName(), new StringValidatorHandler("请输入产品名称（注册证/备案凭证）"));
            validator.on(requestData.getSpecsModel(), new StringValidatorHandler("请输入规格、型号（注册证/备案凭证）"));
        }

        if (StringUtils.isNotEmpty(requestData.getName())) {
            validator.on(requestData.getShowName(), new MaxLengthValidate(NamingUtils.MAX_GOODS_NAME_LENGTH, "SPU名称超过长度限制"));
        }

        if (ArrayUtils.isNotEmpty(requestData.getBaseAttributeIds())) {
            validator.onEach(requestData.getBaseAttributeIds(), new NumberValidatorHandler("商品属性为空"));
        }
        if (requestData.isNew()) {
            validator.on(requestData.getAssignmentManagerId(), new NumberValidatorHandler("请选择产品经理"));
            validator.on(requestData.getAssignmentAssistantId(), new NumberValidatorHandler("请选择产品助理"));
        }

        onValidCommonProperties(requestData);

        return validator.doValidate().result(ResultCollectors.toSimple());
    }
}
