<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="公海规则" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css" media="all">
<script src="/webjars/ezadmin/plugins/layui/layui.js" charset="utf-8"></script>
<div class="layui-tab layui-tab-brief" lay-filter="test" style="height: 100%;margin: 0;padding: 10px 0;">
    <ul class="layui-tab-title">
        <li class="layui-this">区域规则维护</li>
        <li>公海条件配置</li>
        <li>公海豁免人员</li>
    </ul>
    <div class="layui-tab-content" style="min-height: calc(100% - 40px);display: flex;">
        <iframe style="width: 100%;" frameborder="no" src="./regionRulesList.do"></iframe>
    </div>
</div>
<script>
    layui.use('element', function () {
        var element = layui.element; //Tab的切换功能，切换事件监听等，需要依赖element模块

        element.on('tab(test)', function (data) {
            var index = data.index;
            if (index == 0){
                $(".layui-tab-content").find("iframe").attr("src", './regionRulesList.do');
            }else if (index == 1) {
                $(".layui-tab-content").find("iframe").attr("src", '/trader/customer/view/calculate/rules.do');
            } else {
                $(".layui-tab-content").find("iframe").attr("src", '/trader/customer/view/calculate/rules/exemption.do');
            }

        });

    });
</script>
</body>

</html>