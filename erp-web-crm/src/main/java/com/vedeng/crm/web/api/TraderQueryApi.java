package com.vedeng.crm.web.api;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.sun.org.apache.xpath.internal.operations.Bool;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.crm.visitrecord.domain.vo.VisitCustomerVo;
import com.vedeng.crm.visitrecord.service.CrmVisitRecordService;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.RegionApiService;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.domain.vo.TraderCustomerInfoVo;
import com.vedeng.erp.trader.service.TraderCustomerBaseService;
import com.vedeng.infrastructure.feign.tyc.TycApiService;
import com.vedeng.infrastructure.tyc.domain.dto.TraderInfoTyc;
import com.vedeng.infrastructure.tyc.domain.dto.TycResultDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/7/15
 */
@ExceptionController
@RestController
@RequestMapping(value={"/crm/trader/profile","/crm/trader/m"})
@Slf4j
public class TraderQueryApi {

//    @Autowired
//    private HttpSendUtil httpSendUtil;
    @Autowired
    private CrmVisitRecordService crmVisitRecordService;

    @Autowired
    private TraderCustomerBaseService traderCustomerBaseService;

    @Autowired
    private TycApiService tycApiService;
    @Autowired
    private UserApiService userApiService;

    /**
     * 查询客户的交易等信息
     * @param traderId
     * @return
     */
    @RequestMapping(value = "/queryTraderDetail")
    @NoNeedAccessAuthorization
    public R<VisitCustomerVo> queryTraderDetail(@RequestParam(value = "traderId" ) Integer traderId) {
        VisitCustomerVo visitCustomerVo =  crmVisitRecordService.getVisitCustomerForVisit(traderId,null,null,null);
        return R.success(visitCustomerVo);
    }

    /**
     * 查询客户是否可点击
     * @param traderId
     * @return
     */
    @RequestMapping(value = "/queryTraderForClick")
    @NoNeedAccessAuthorization
    public R<Boolean> queryTraderForClick(@RequestParam(value = "traderId" ) Integer traderId) {
        String errorMsg =  crmVisitRecordService.checkVisitCustomerCanClick(traderId);
        if(StringUtils.isNotBlank(errorMsg)){
            return R.error(errorMsg);
        }
        return R.success( );
    }


    /**
     * 根据名字模糊查询
     * @param limit 限制条数
     * @param name 客户名
     * @return 信息
     */
    @RequestMapping(value = "/queryTrader")
    @NoNeedAccessAuthorization
    public R<List<TraderCustomerInfoVo>> queryTraderCustomerInfoByName(@RequestParam(value = "limit", required = false,defaultValue = "15") Integer limit, String name, @RequestParam(required = false,value="self") String self) {
        if(StringUtils.isEmpty(name)){
            return R.success("请输入客户名称");
        }
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        if("Y".equals(self)){ //如果只查归属自己的客户
            List<TraderCustomerInfoVo> result1 = traderCustomerBaseService.queryTraderCustomerInfoByNameAndUserIdForCrm(limit,name,currentUser.getId(),true);
            if(CollectionUtils.isNotEmpty(result1)){
                result1.stream().forEach(item->{
                    item.setBelong(true);
                    item.setSubUser(false);//设置为下属
                });
            }
            return R.success(result1);
        }

        //如果查包含非自己的客户，先查归属自己的，不够limit数量的，再继续查一次，不带归属自己的条件
        List<TraderCustomerInfoVo> result1 = traderCustomerBaseService.queryTraderCustomerInfoByNameAndUserIdForCrm(limit,name,currentUser.getId(),true);
        int resultCount = CollectionUtils.size(result1);//先取归属自己的客户数量
        //已在结果集traderIds，下一轮查询时要排除，因为要取固定数量，所以sql要not in
        List<Integer> traderIds = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(result1)){
            result1.stream().forEach(item->{
                traderIds.add(item.getTraderId());
                item.setBelong(true);
                item.setSubUser(false);//设置为下属
            });
        }


        if(resultCount < limit){ //如果不足当前这第一页，则取归属的补足
            List<TraderCustomerInfoVo> result2 = traderCustomerBaseService.queryTraderCustomerInfoByNameAndUserIdForCrmShared(limit-CollectionUtils.size(result1),name,currentUser.getId(),traderIds,true);
            if(CollectionUtils.isNotEmpty(result2)){
                resultCount = resultCount + CollectionUtils.size(result2);//累加结果的数量
                result2.stream().forEach(item->{
                    traderIds.add(item.getTraderId());
                    item.setBelong(false);//设置为归属当前人
                    item.setShare(true);
                    item.setSubUser(false);//设置为下属
                });
                result1.addAll(result2);
            }

        }

        //获取这个人的所有下属，
        if(resultCount < limit) {
            List<UserDto> userDtoList = userApiService.queryUserSubFromUac(currentUser.getId());
            if (CollectionUtils.isNotEmpty(userDtoList)) {
                List<Integer> allSubUserIdsList = userDtoList.stream().map(UserDto::getUserId)
                        .filter(id -> !currentUser.getId().equals(id))
                        .collect(Collectors.toList());
                List<TraderCustomerInfoVo> resultSub = traderCustomerBaseService.queryTraderCustomerInfoByNameAndSubUserId(limit-CollectionUtils.size(result1),name,allSubUserIdsList,traderIds);
                if(CollectionUtils.isNotEmpty(resultSub)){
                    resultSub.stream().forEach(item->{
                        traderIds.add(item.getTraderId());
                        item.setBelong(false);//设置为归属当前人
                        item.setShare(false);//设置为分享给了当前人
                        item.setSubUser(true);//设置为下属
                    });
                    result1.addAll(resultSub);
                }
            }
        }

        //如果result1的数量小于limit ，则继续查询
        if(resultCount < limit){
             List<TraderCustomerInfoVo> result3 = traderCustomerBaseService.queryTraderCustomerInfoByNameAndUserIdForCrmShared(limit-CollectionUtils.size(result1),name,currentUser.getId(),traderIds,false);
             if(CollectionUtils.isNotEmpty(result3)){
                 result3.stream().forEach(item->{
                     traderIds.add(item.getTraderId());
                    item.setBelong(false);//设置为归属当前人
                    item.setShare(false);//设置为分享给了当前人
                 });
                 result1.addAll(result3);
            }

        }
        return R.success(result1);

    }


    /**
     * 模糊查询天眼查列表
     *
     * @param traderName
     * @return
     * @Note <b>Author:</b> scott
     * <br><b>Date:</b> 2018年1月11日 上午9:08:35
     */
    @ResponseBody
    @RequestMapping(value = "/queryTycList")
    public R<?> queryTycList( String traderName) {
        try{
            RestfulResult<PageInfo<TycResultDto>> result =  tycApiService.list(traderName);
            PageInfo<TycResultDto> resultData=  result.getData();
            if(CollectionUtils.isNotEmpty(resultData.getList())){
                List<TycResultDto> list = resultData.getList();
                return R.success(list);
            }else{
                return R.success(new ArrayList<>());
            }
        }catch ( Exception e){
            log.error("天眼查查询失败",e);
            return R.error("未查询到相关数据");
        }

    }

    /**
     * 天眼查信息精确查询
     *
     * @param traderName
     * @return
     * @Note <b>Author:</b> scott
     * <br><b>Date:</b> 2018年1月11日 上午9:08:35
     */
    @ResponseBody
    @RequestMapping(value = "/queryTycDetail")
    public R<?> queryTycDetail( String traderName) {
        RestfulResult<TraderInfoTyc> result =  tycApiService.detail(traderName);
        if(result != null && result.getData() != null){
            TraderInfoTyc detail = result.getData();
            if(detail.getEstiblishTime() !=null && detail.getEstiblishTime() >0){
                Instant instant = Instant.ofEpochMilli(detail.getEstiblishTime());
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
                        .withZone(ZoneId.systemDefault());
                String formattedDate = formatter.format(instant);
                detail.setEstiblishTimeStr(formattedDate);
            }
            getTags(detail);
            return R.success(detail);
        }else{
            return R.error("未查询到相关数据");
        }
    }


    public void getTags(TraderInfoTyc detail){
        if(detail !=null){
            try{
                String jsonData = detail.getJsonData();
                //先判断jsonData是否为空，不为空时按json进行解析
                if(StringUtils.isNotEmpty(jsonData)){
                    JSONObject tagsJson= JSONObject.parseObject(jsonData);
                    String tags = tagsJson.getString("tags");
                    List<String> tagList = Arrays.asList(tags.split(";"));
                    //return tagList;
                    detail.setTags(tagList);


                    String city = tagsJson.getString("city");
                    detail.setCity(city);

                    String district = tagsJson.getString("district");
                    detail.setDistrict(district);

                    JSONObject jsonIndustryAll = tagsJson.getJSONObject("industryAll");
                    if(jsonIndustryAll != null){
                        // {
                        //        "categoryMiddle": "软件开发",
                        //        "categoryBig": "软件和信息技术服务业",
                        //        "category": "信息传输、软件和信息技术服务业",
                        //        "categorySmall": ""
                        //    },
                        //分别定义以上字段并取出
                        String category = jsonIndustryAll.getString("category");
                        String categoryBig = jsonIndustryAll.getString("categoryBig");
                        String categoryMiddle = jsonIndustryAll.getString("categoryMiddle");
                        String categorySmall = jsonIndustryAll.getString("categorySmall");
                        //分别赋值给detail
                        detail.setCategory(category);
                        detail.setCategoryBig(categoryBig);
                        detail.setCategoryMiddle(categoryMiddle);
                        detail.setCategorySmall(categorySmall);
                    }

                    if(StringUtils.isNotBlank(detail.getCity())){
                        String provinceName = regionApiService.getRegionProvinceByCityName(detail.getCity());
                        if(StringUtils.isNotBlank(provinceName)){
                            detail.setBase(provinceName);
                        }
                    }

                }

            }catch (Exception e){
                log.error("获取天眼查 tags失败",e);
            }
        }

    }

    @Autowired
    private RegionApiService regionApiService;



}
