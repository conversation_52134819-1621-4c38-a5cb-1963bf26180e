<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.repository.mappers.KingDeeSalesVatSpecialInvoiceMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.domain.entity.KingDeeSalesVatSpecialInvoiceEntity">
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="FID" jdbcType="VARCHAR" property="fid" />
    <result column="F_BILL_NO" jdbcType="VARCHAR" property="fBillNo" />
    <result column="FDATE" jdbcType="VARCHAR" property="fdate" />
    <result column="F_QZOK_BDDJTID" jdbcType="VARCHAR" property="fQzokBddjtid" />
    <result column="FINVOICENO" jdbcType="VARCHAR" property="finvoiceno" />
    <result column="FINVOICEDATE" jdbcType="VARCHAR" property="finvoicedate" />
    <result column="F_QZOK_FPDM" jdbcType="VARCHAR" property="fQzokFpdm" />
    <result column="FBILLINGWAY" jdbcType="VARCHAR" property="fbillingway" />
    <result column="FCUSTOMERID" jdbcType="VARCHAR" property="fcustomerid" />
    <result column="FDOCUMENTSTATUS" jdbcType="VARCHAR" property="fdocumentstatus" />
    <result column="F_BILL_TYPE_ID" jdbcType="VARCHAR" property="fBillTypeId" />
    <result column="FSETTLEORGID" jdbcType="VARCHAR" property="fsettleorgid" />
    <result column="F_CANCEL_STATUS" jdbcType="VARCHAR" property="fCancelStatus" />
    <result column="FISTAX" jdbcType="VARCHAR" property="fIsTax" />
    <result column="FRedBlue" jdbcType="INTEGER" property="fRedBlue" />
    <result column="FSALESICENTRY" property="fsalesicentry"
            typeHandler="com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler"/>
  </resultMap>
  <sql id="Base_Column_List">
    ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, FID, F_BILL_NO, 
    FDATE, F_QZOK_BDDJTID, FINVOICENO, FINVOICEDATE, F_QZOK_FPDM, FBILLINGWAY, FCUSTOMERID, 
    FDOCUMENTSTATUS, F_BILL_TYPE_ID, FSETTLEORGID, F_CANCEL_STATUS, FISTAX, FRedBlue, FSALESICENTRY
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from KING_DEE_SALES_VAT_SPECIAL_INVOICE
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from KING_DEE_SALES_VAT_SPECIAL_INVOICE
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeSalesVatSpecialInvoiceEntity" useGeneratedKeys="true">
    insert into KING_DEE_SALES_VAT_SPECIAL_INVOICE (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      FID, F_BILL_NO, FDATE, 
      F_QZOK_BDDJTID, FINVOICENO, FINVOICEDATE, 
      F_QZOK_FPDM, FBILLINGWAY, FCUSTOMERID, 
      FDOCUMENTSTATUS, F_BILL_TYPE_ID, FSETTLEORGID, 
      F_CANCEL_STATUS, FISTAX, FRedBlue, FSALESICENTRY)
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{fid,jdbcType=VARCHAR}, #{fBillNo,jdbcType=VARCHAR}, #{fdate,jdbcType=VARCHAR}, 
      #{fQzokBddjtid,jdbcType=VARCHAR}, #{finvoiceno,jdbcType=VARCHAR}, #{finvoicedate,jdbcType=VARCHAR}, 
      #{fQzokFpdm,jdbcType=VARCHAR}, #{fbillingway,jdbcType=VARCHAR}, #{fcustomerid,jdbcType=VARCHAR}, 
      #{fdocumentstatus,jdbcType=VARCHAR}, #{fBillTypeId,jdbcType=VARCHAR}, #{fsettleorgid,jdbcType=VARCHAR}, 
      #{fCancelStatus,jdbcType=VARCHAR}, #{fIsTax,jdbcType=VARCHAR}, #{fRedBlue,jdbcType=INTEGER}, #{fsalesicentry,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeSalesVatSpecialInvoiceEntity" useGeneratedKeys="true">
    insert into KING_DEE_SALES_VAT_SPECIAL_INVOICE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="fid != null">
        FID,
      </if>
      <if test="fBillNo != null">
        F_BILL_NO,
      </if>
      <if test="fdate != null">
        FDATE,
      </if>
      <if test="fQzokBddjtid != null">
        F_QZOK_BDDJTID,
      </if>
      <if test="finvoiceno != null">
        FINVOICENO,
      </if>
      <if test="finvoicedate != null">
        FINVOICEDATE,
      </if>
      <if test="fQzokFpdm != null">
        F_QZOK_FPDM,
      </if>
      <if test="fbillingway != null">
        FBILLINGWAY,
      </if>
      <if test="fcustomerid != null">
        FCUSTOMERID,
      </if>
      <if test="fdocumentstatus != null">
        FDOCUMENTSTATUS,
      </if>
      <if test="fBillTypeId != null">
        F_BILL_TYPE_ID,
      </if>
      <if test="fsettleorgid != null">
        FSETTLEORGID,
      </if>
      <if test="fCancelStatus != null">
        F_CANCEL_STATUS,
      </if>
      <if test="fIsTax != null">
        FISTAX,
      </if>
      <if test="fRedBlue != null">
        FRedBlue,
      </if>
      <if test="fsalesicentry != null">
        FSALESICENTRY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fid != null">
        #{fid,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null">
        #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fdate != null">
        #{fdate,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBddjtid != null">
        #{fQzokBddjtid,jdbcType=VARCHAR},
      </if>
      <if test="finvoiceno != null">
        #{finvoiceno,jdbcType=VARCHAR},
      </if>
      <if test="finvoicedate != null">
        #{finvoicedate,jdbcType=VARCHAR},
      </if>
      <if test="fQzokFpdm != null">
        #{fQzokFpdm,jdbcType=VARCHAR},
      </if>
      <if test="fbillingway != null">
        #{fbillingway,jdbcType=VARCHAR},
      </if>
      <if test="fcustomerid != null">
        #{fcustomerid,jdbcType=VARCHAR},
      </if>
      <if test="fdocumentstatus != null">
        #{fdocumentstatus,jdbcType=VARCHAR},
      </if>
      <if test="fBillTypeId != null">
        #{fBillTypeId,jdbcType=VARCHAR},
      </if>
      <if test="fsettleorgid != null">
        #{fsettleorgid,jdbcType=VARCHAR},
      </if>
      <if test="fCancelStatus != null">
        #{fCancelStatus,jdbcType=VARCHAR},
      </if>
      <if test="fIsTax != null">
        #{fIsTax,jdbcType=VARCHAR},
      </if>
      <if test="fRedBlue != null">
        #{fRedBlue,jdbcType=INTEGER},
      </if>
      <if test="fsalesicentry != null">
        #{fsalesicentry,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeSalesVatSpecialInvoiceEntity">
    update KING_DEE_SALES_VAT_SPECIAL_INVOICE
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fid != null">
        FID = #{fid,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null">
        F_BILL_NO = #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fdate != null">
        FDATE = #{fdate,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBddjtid != null">
        F_QZOK_BDDJTID = #{fQzokBddjtid,jdbcType=VARCHAR},
      </if>
      <if test="finvoiceno != null">
        FINVOICENO = #{finvoiceno,jdbcType=VARCHAR},
      </if>
      <if test="finvoicedate != null">
        FINVOICEDATE = #{finvoicedate,jdbcType=VARCHAR},
      </if>
      <if test="fQzokFpdm != null">
        F_QZOK_FPDM = #{fQzokFpdm,jdbcType=VARCHAR},
      </if>
      <if test="fbillingway != null">
        FBILLINGWAY = #{fbillingway,jdbcType=VARCHAR},
      </if>
      <if test="fcustomerid != null">
        FCUSTOMERID = #{fcustomerid,jdbcType=VARCHAR},
      </if>
      <if test="fdocumentstatus != null">
        FDOCUMENTSTATUS = #{fdocumentstatus,jdbcType=VARCHAR},
      </if>
      <if test="fBillTypeId != null">
        F_BILL_TYPE_ID = #{fBillTypeId,jdbcType=VARCHAR},
      </if>
      <if test="fsettleorgid != null">
        FSETTLEORGID = #{fsettleorgid,jdbcType=VARCHAR},
      </if>
      <if test="fCancelStatus != null">
        F_CANCEL_STATUS = #{fCancelStatus,jdbcType=VARCHAR},
      </if>
      <if test="fIsTax != null">
        FISTAX = #{fIsTax,jdbcType=VARCHAR},
      </if>
      <if test="fRedBlue != null">
        FRedBlue = #{fRedBlue,jdbcType=INTEGER},
      </if>
      <if test="fsalesicentry != null">
        FSALESICENTRY = #{fsalesicentry,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeSalesVatSpecialInvoiceEntity">
    update KING_DEE_SALES_VAT_SPECIAL_INVOICE
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      FID = #{fid,jdbcType=VARCHAR},
      F_BILL_NO = #{fBillNo,jdbcType=VARCHAR},
      FDATE = #{fdate,jdbcType=VARCHAR},
      F_QZOK_BDDJTID = #{fQzokBddjtid,jdbcType=VARCHAR},
      FINVOICENO = #{finvoiceno,jdbcType=VARCHAR},
      FINVOICEDATE = #{finvoicedate,jdbcType=VARCHAR},
      F_QZOK_FPDM = #{fQzokFpdm,jdbcType=VARCHAR},
      FBILLINGWAY = #{fbillingway,jdbcType=VARCHAR},
      FCUSTOMERID = #{fcustomerid,jdbcType=VARCHAR},
      FDOCUMENTSTATUS = #{fdocumentstatus,jdbcType=VARCHAR},
      F_BILL_TYPE_ID = #{fBillTypeId,jdbcType=VARCHAR},
      FSETTLEORGID = #{fsettleorgid,jdbcType=VARCHAR},
      F_CANCEL_STATUS = #{fCancelStatus,jdbcType=VARCHAR},
      FISTAX = #{fIsTax,jdbcType=VARCHAR},
      FRedBlue = #{fRedBlue,jdbcType=INTEGER},
      FSALESICENTRY = #{fsalesicentry,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=INTEGER}
  </update>
</mapper>