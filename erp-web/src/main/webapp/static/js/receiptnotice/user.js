void function(){
    new Vue({
        el: '#page-container',
        data () {
            return {
                loadingEl: null,
                // 筛选项
                filter_user: '',
                filter_sale: '',
                filter_saleName: '',
                saleRemoteInfo: {
                    url: '/broadcast/user/getAllAedUserList.do',
                    paramsType: 'url',
                    hasPage: true,
                    paramsKey: 'username', // 接口入参
                    parseLabel: 'username', // 面板label
                    parseValue: 'aedUserId', // 面板value
                },
                // 列表数据
                list: [],
                total: 0,
                pageSize: 20,
                pageNum: 1,
                // 编辑/新建弹窗
                isShowEditUser: false,
                editData: null, // 编辑对象记录  编辑:有object值 新建:null
                userRremoteInfo: {
                    url: '/system/user/searchUserListForSelectLimit.do',
                    paramsType: 'url',
                    hasPage: true,
                    paramsKey: 'username', // 接口入参
                    parseLabel: 'label', // 面板label
                    parseValue: 'value', // 面板value
                },
                user: '',
                defaultUserName: '',
                userDisabled: false,
                sale: '',
                defaultSale: '',
            }
        },
        mounted() {
            this.getList();
        },
        methods: {
            getList() {
                this.showloading();
                return axios.post('/broadcast/user/getUserListPage.do', {
                    "pageNum": this.pageNum,
                    "pageSize": this.pageSize,
                    "param": {
                        "username": this.filter_user,
                        "aedUserId": this.filter_sale
                    }
                }).then(({data}) => {
                    this.hideloading();
                    console.log('data:', data);
                    if (data.success) {
                        this.list = data.data.list || [];
                        this.total = data.data.total || [];
                    }
                })
            },
            handleSizeChange(val) {
                console.log(`每页 ${val} 条`);
                this.pageSize = val;
                this.getList();
            },
            handleCurrentChange(val) {
                console.log(`当前页: ${val}`);
                this.pageNum = val;
                this.getList();
            },
            handlerSearch() {
                this.pageNum = 1;
                console.log('query', this.filter_user, this.filter_sale);
                this.getList();
            },
            handlerReset() {
                this.filter_user = '';
                this.filter_sale = '';
                this.filter_saleName = '';
                this.getList();
            },
            handlerChange(item) {
                this.filter_saleName = item.selected.label;
            },


            showEditUser() { // type：edit编辑
                this.isShowEditUser = true;

                this.$form.rules({
                    user: {
                        required: '请选择用户'
                    },
                    sale: {
                        required: '请选择AED销售'
                    },
                }, 'editUser', this);
            },
            addRow () {
                this.editData = null;
                this.user = '';
                this.defaultUserName = '';
                this.sale = '';
                this.defaultSale = '';
                this.showEditUser();
            },
            editRow(item) {
                console.log(item);
                this.editData = item;
                this.user = item.erpUserId;
                this.defaultUserName = item.erpUsername;
                this.sale = item.aedUserId;
                this.defaultSale = item.aedUsername;
                this.showEditUser();
            },
            deleteRow(item) {
                console.log(item);
                let _this = this;
                this.$popup.warn({
                    message: '确认删除？',
                    buttons: [{
                        txt: '是',
                        btnClass: 'delete',
                        callback() {
                            axios.get(`/broadcast/user/delete.do?id=${item.id}`).then(({data}) => {
                                console.log('del user:', data);
                                if (data.code === 0) {
                                    _this.$message({
                                        message: data.message,
                                        type: 'success'
                                    });
                                    _this.getList();
                                } else {
                                    _this.$message({
                                        message: data.message || '删除失败',
                                        type: 'error'
                                    });
                                }
                            });
                        }
                    }, { txt: '否' }]
                })
            },
            confirm() {
                if (this.$form.validForm('editUser')) {
                    this.showloading();

                    let reqData = {
                        "erpUserId": this.user,
                        "aedUserId": this.sale,
                    };
                    if (this.editData) {
                        reqData['id'] = this.editData.id;
                    }
                    axios.post('/broadcast/user/saveOrUpdate.do', reqData).then(({data}) => {
                        this.hideloading();

                        console.log('data:', data);
                        if (data.code === 0) {
                            this.$message({
                                message: data.message,
                                type: 'success'
                            });
                            this.isShowEditUser = false;
                            this.getList();
                        } else {
                            this.$message({
                                message: data.message || '保存失败',
                                type: 'error'
                            });
                        }
                    })
                }
            },


            showloading() {
                this.loadingEl = this.$loading({
                    lock: true,
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
            },
            hideloading() {
                this.loadingEl && this.loadingEl.close();
            },
        }
    })
}.call(this);