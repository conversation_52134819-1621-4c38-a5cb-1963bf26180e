package com.vedeng.erp.system.mapper;

import com.vedeng.erp.system.domain.entity.BaseCompanyInfoEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BaseCompanyInfoMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(BaseCompanyInfoEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(BaseCompanyInfoEntity record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    BaseCompanyInfoEntity selectByPrimaryKey(Integer id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(BaseCompanyInfoEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(BaseCompanyInfoEntity record);

    /**
     * 查询单个客户名称
     *
     * @param companyName
     * @return
     */
    BaseCompanyInfoEntity selectByCompanyName(String companyName);


    /**
     * 使用简称查询
     * @param shortName
     * @return
     */
    BaseCompanyInfoEntity selectByShortName(String shortName);



    /**
     * 查询多个主体的名称
     *
     * @param companyNames
     * @return
     */
    List<BaseCompanyInfoEntity> selectByCompanyNames(@Param("companyNames") List<String> companyNames);

    List<BaseCompanyInfoEntity> findAll();
}