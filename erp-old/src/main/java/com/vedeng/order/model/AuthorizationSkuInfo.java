package com.vedeng.order.model;

public class AuthorizationSkuInfo {
    private Integer skuId;

    private String skuName;

    private String brandName;

    private String skuModel;

    private Integer isChance;

    /**
     * 生产厂家-中文或英文字符
     */
    private String productCompany;

    /**
     * 生产1还是代理销售2
     */
    private Integer natureOfOperation;

    /**
     * 独家经销商1 经销商2 代理商3
     */
    private Integer distributionsType;

    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getSkuModel() {
        return skuModel;
    }

    public void setSkuModel(String skuModel) {
        this.skuModel = skuModel;
    }

    public Integer getIsChance() {
        return isChance;
    }

    public void setIsChance(Integer isChance) {
        this.isChance = isChance;
    }

    public Integer getDistributionsType() {
        return distributionsType;
    }

    public void setDistributionsType(Integer distributionsType) {
        this.distributionsType = distributionsType;
    }

    public Integer getNatureOfOperation() {
        return natureOfOperation;
    }

    public void setNatureOfOperation(Integer natureOfOperation) {
        this.natureOfOperation = natureOfOperation;
    }

    public String getProductCompany() {
        return productCompany;
    }

    public void setProductCompany(String productCompany) {
        this.productCompany = productCompany;
    }
}
