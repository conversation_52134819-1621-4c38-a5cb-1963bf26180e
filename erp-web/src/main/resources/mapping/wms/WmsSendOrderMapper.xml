<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wms.dao.WmsSendOrderMapper" >
  <resultMap id="BaseResultMap" type="com.wms.model.po.WmsSendOrder" >
    <!--          -->
    <id column="WMS_SEND_ORDER_ID" property="wmsSendOrderId" jdbcType="INTEGER" />
    <result column="ORDER_TYPE" property="orderType" jdbcType="BIT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="ORDER_ID" property="orderId" jdbcType="INTEGER" />
    <result column="SEND_STATUS" property="sendStatus" jdbcType="BIT" />
    <result column="ADD_TIME" property="addTime" jdbcType="TIMESTAMP" />
    <result column="MODE_TIME" property="modeTime" jdbcType="TIMESTAMP" />
    <result column="IS_DELETE" property="isDelete" jdbcType="BIT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--          -->
    WMS_SEND_ORDER_ID, ORDER_TYPE, ORDER_NO, ORDER_ID, SEND_STATUS, ADD_TIME, MODE_TIME, 
    IS_DELETE, CREATOR, UPDATER
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--          -->
    select 
    <include refid="Base_Column_List" />
    from V_WMS_SEND_ORDER
    where WMS_SEND_ORDER_ID = #{wmsSendOrderId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--          -->
    delete from V_WMS_SEND_ORDER
    where WMS_SEND_ORDER_ID = #{wmsSendOrderId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.wms.model.po.WmsSendOrder" >
    <!--          -->
    insert into V_WMS_SEND_ORDER (WMS_SEND_ORDER_ID, ORDER_TYPE, ORDER_NO, 
      ORDER_ID, SEND_STATUS, ADD_TIME, 
      MODE_TIME, IS_DELETE, CREATOR, 
      UPDATER)
    values (#{wmsSendOrderId,jdbcType=INTEGER}, #{orderType,jdbcType=BIT}, #{orderNo,jdbcType=VARCHAR}, 
      #{orderId,jdbcType=INTEGER}, #{sendStatus,jdbcType=BIT}, #{addTime,jdbcType=TIMESTAMP}, 
      #{modeTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=BIT}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER})
  </insert>
  
  <insert id="insertSelective" parameterType="com.wms.model.po.WmsSendOrder" useGeneratedKeys="true" keyProperty="wmsSendOrderId">
    <!--          -->
    insert into V_WMS_SEND_ORDER
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="wmsSendOrderId != null" >
        WMS_SEND_ORDER_ID,
      </if>
      <if test="orderType != null" >
        ORDER_TYPE,
      </if>
      <if test="orderNo != null" >
        ORDER_NO,
      </if>
      <if test="orderId != null" >
        ORDER_ID,
      </if>
      <if test="sendStatus != null" >
        SEND_STATUS,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="modeTime != null" >
        MODE_TIME,
      </if>
      <if test="isDelete != null" >
        IS_DELETE,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="wmsSendOrderId != null" >
        #{wmsSendOrderId,jdbcType=INTEGER},
      </if>
      <if test="orderType != null" >
        #{orderType,jdbcType=BIT},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null" >
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="sendStatus != null" >
        #{sendStatus,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modeTime != null" >
        #{modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null" >
        #{isDelete,jdbcType=BIT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wms.model.po.WmsSendOrder" >
    <!--          -->
    update V_WMS_SEND_ORDER
    <set >
      <if test="orderType != null" >
        ORDER_TYPE = #{orderType,jdbcType=BIT},
      </if>
      <if test="orderNo != null" >
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null" >
        ORDER_ID = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="sendStatus != null" >
        SEND_STATUS = #{sendStatus,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modeTime != null" >
        MODE_TIME = #{modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null" >
        IS_DELETE = #{isDelete,jdbcType=BIT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where WMS_SEND_ORDER_ID = #{wmsSendOrderId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wms.model.po.WmsSendOrder" >
    <!--          -->
    update V_WMS_SEND_ORDER
    set ORDER_TYPE = #{orderType,jdbcType=BIT},
      ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      ORDER_ID = #{orderId,jdbcType=INTEGER},
      SEND_STATUS = #{sendStatus,jdbcType=BIT},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MODE_TIME = #{modeTime,jdbcType=TIMESTAMP},
      IS_DELETE = #{isDelete,jdbcType=BIT},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER}
    where WMS_SEND_ORDER_ID = #{wmsSendOrderId,jdbcType=INTEGER}
  </update>

  <select id="getSendFailSaleorder" resultType="com.wms.model.po.WmsSendOrder">
    SELECT
        <include refid="Base_Column_List" />
    FROM
      V_WMS_SEND_ORDER A
    WHERE
      A.ORDER_TYPE = #{orderType,jdbcType=INTEGER}
      AND A.SEND_STATUS = 0
      AND A.IS_DELETE = 0
  </select>
  <select id="getWmsSendOrderInfo" resultType="com.wms.model.po.WmsSendOrder">
    SELECT
    <include refid="Base_Column_List" />
    FROM
    V_WMS_SEND_ORDER A
    WHERE
        A.ORDER_TYPE = #{orderType,jdbcType=BIT}
    AND A.ORDER_NO = #{orderNo,jdbcType=VARCHAR}
    AND A.ORDER_ID = #{orderId,jdbcType=INTEGER}
    AND A.IS_DELETE = 0
  </select>

</mapper>