<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<c:set var="title" value="详情" scope="application" />
<%@ include file="../common/common.jsp"%>
<script type="text/javascript">

    function toEdit() {
        debugger;
        $.ajax({
            async:true,
            url:page_url+'/price/basePriceMaintain/toEditValidator.do',
            data:{"skuPriceChangeApplyId":'${skuPriceChangeApplyDto.id}'},
            type:"POST",
            dataType : "json",
            success:function(data){
                if(data.code==0){
                    var verifyStatus = data.data;
                    if(verifyStatus == '审核中'){
                        layer.alert("该商品核价正在审核中，不可进行编辑");
                    }else{
                        window.location.href = "${pageContext.request.contextPath}/price/basePriceMaintain/edit.do?skuPriceChangeApplyId=${skuPriceChangeApplyDto.id}";
                    }
                }else{
                    layer.alert(data.message)
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        })
    }

</script>
<div class="content">
    <div class="formtitle"></div>
    <div  id="desc_div">
        <form method="post" id="addForm" action="${pageContext.request.contextPath}/price/basePriceMaintain/detail.do">
            <input type="hidden" name="skuPriceChangeApplyId" id="skuPriceChangeApplyId" value="${skuPriceChangeApplyDto.id}"/>
            <ul class="payplan">

                <li>
                    <div class="parts">
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                                <tr>
                                    <th style="width:50px">订货号</th>
                                    <th style="width:80px">商品名称</th>
                                    <th style="width:80px">主销售部门</th>
                                    <th style="width:80px">核价审核状态</th>
                                    <th style="width:80px">成本价是否含运费</th>
                                    <th style="width:80px">销售价是否含运费</th>
                                    <th style="width:80px">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>${skuPriceChangeApplyDto.skuNo}</td>
                                    <td>${skuPriceChangeApplyDto.skuName}</td>
                                    <td>${skuPriceChangeApplyDto.mainDept}</td>
                                    <td>${skuPriceChangeApplyDto.verifyStatusName}</td>
                                    <td>${skuPriceChangeApplyDto.purchaseContainsFeeStr}</td>
                                    <td>${skuPriceChangeApplyDto.saleContainsFeeStr}</td>
                                    <td>
                                        <span class="edit-user addtitle" tabTitle='{"num":"customer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                               "link":"./price/basePriceMaintain/viewEffectPrice.do?skuPriceChangeApplyId=${skuPriceChangeApplyDto.id}","title":"查看修改前价格"}'>查看修改前价格</span>

                                        <c:if test="${skuPriceChangeApplyDto.verifyStatusName!='审核中'}">
                                            <button type="button" class="bt-bg-style bg-light-green bt-small mr10" onclick="toEdit();">编辑</button>
                                        </c:if>

                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </li>

                <li>
                    <div class="parts">
                        <div class="title-container">
                            <div class="table-title nobor">成本价</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                            <tr>
                                <th style="width:50px">采购价</th>
                                <th style="width:80px">操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>${skuPriceChangeApplyDto.middlePrice}</td>
                                <td><span class="edit-user addtitle" tabTitle='{"num":"customer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
					    "link":"./price/basePriceMaintain/purchasePriceTrace.do?skuId=${skuPriceChangeApplyDto.skuId}","title":"查看轨迹"}'>查看轨迹</span></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </li>

                <li>
                    <div class="parts">
                        <div class="title-container">
                            <div class="table-title nobor">已生效</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                                <tr>
                                    <th style="width:50px">供应商名称</th>
                                    <th style="width:80px">采购成本</th>
                                    <th style="width:80px">生效时间</th>
                                </tr>
                            </thead>
                            <tbody>
                            <c:forEach var="purchaseDetail" items="${skuPriceChangeApplyDto.effectPurchaseList}" varStatus="staut">
                                <tr>
                                    <td>${purchaseDetail.traderName}</td>
                                    <td>${purchaseDetail.purchasePrice}</td>
                                    <td>${purchaseDetail.modTime}</td>
                                </tr>
                            </c:forEach>
                            </tbody>
                        </table>
                    </div>
                </li>

                <li>
                    <div class="parts">
                        <div class="title-container">
                            <div class="table-title nobor">本次更新</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                            <tr>
                                <th style="width:50px">供应商名称</th>
                                <th style="width:80px">采购成本</th>
                                <th style="width:80px">变动原因</th>
                                <th style="width:80px">毛利率(终端价)</th>
                                <th style="width:80px">毛利率(经销价)</th>
                                <th style="width:80px">毛利率(电商价)</th>
                                <th style="width:80px">毛利率(科研终端价)</th>
                            </tr>
                            </thead>
                            <tbody>
                            <c:forEach var="purchaseInfo" items="${skuPriceChangeApplyDto.purchaseInfoList}" varStatus="staut">
                                <tr>
                                    <td>${purchaseInfo.traderName}</td>
                                    <td>${purchaseInfo.purchasePrice}</td>
                                    <td>${purchaseInfo.changeReason}</td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                            </c:forEach>
                            </tbody>
                        </table>
                    </div>
                </li>

                <li>
                    <div class="parts">
                        <div class="title-container">
                            <div class="table-title nobor">销售价</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                            <tr>
                                <th style="width:50px">市场价</th>
                                <th style="width:80px">终端价</th>
                                <th style="width:80px">经销价</th>
                                <th style="width:80px">集团价</th>
                                <th style="width:80px">电商价</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td> ${myfn:toString(skuPriceChangeApplyDto.marketPrice)}</td>
                                <td> ${skuPriceChangeApplyDto.terminalPrice}</td>
                                <td> ${skuPriceChangeApplyDto.distributionPrice}</td>
                                <td> ${skuPriceChangeApplyDto.groupPrice}</td>
                                <td> ${skuPriceChangeApplyDto.electronicCommercePrice}</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </li>

                <li>
                    <div class="parts">
                        <div class="title-container">
                            <div class="table-title nobor">审核记录</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                                <tr>
                                    <th style="width:50px">操作人</th>
                                    <th style="width:80px">操作时间</th>
                                    <th style="width:80px">操作事项</th>
                                    <th style="width:80px">备注</th>
                                </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td colspan="4">
                                    <button type="button" class="bt-bg-style bg-light-green bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./auditResult.do?taskId=${taskId}&skuPriceChangeApplyId=${skuPriceChangeApplyDto.id}&pass=true"}'>审核通过</button>
                                    <button type="button" class="bt-bg-style bg-light-orange bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./auditResult.do?taskId=${taskId}&skuPriceChangeApplyId=${skuPriceChangeApplyDto.id}&pass=false"}'>审核不通过</button>
                                </td>
                            </tr>
                                <c:forEach var="hi" items="${historicActivityInstance}" varStatus="status">
                                    <c:if test="${not empty  hi.activityName}">

                                        <tr>
                                            <td>
                                                <c:choose>
                                                    <c:when test="${hi.activityType == 'startEvent'}">
                                                        ${startUser}
                                                    </c:when>
                                                    <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                                    </c:when>
                                                    <c:otherwise>
                                                        <c:if test="${historicActivityInstance.size() == 1}">
                                                            <c:forEach var="vs" items="${verifyUsersList}" varStatus="status">
                                                                <c:if test="${fn:contains(verifyUserList, vs)}">
                                                                    <span class="font-green">${vs}</span>&nbsp;
                                                                </c:if>
                                                                <c:if test="${!fn:contains(verifyUserList, vs)}">
                                                                    <span>${vs}</span>&nbsp;
                                                                </c:if>
                                                            </c:forEach>

                                                            <c:if test="${empty verifyUsersList && empty hi.assignee}">
                                                                ${verifyUsers}
                                                            </c:if>
                                                        </c:if>
                                                        <c:if test="${historicActivityInstance.size() != 1}">
                                                            ${hi.assignee}
                                                        </c:if>
                                                    </c:otherwise>
                                                </c:choose>


                                            </td>
                                            <td><fmt:formatDate value="${hi.endTime}" pattern="yyyy-MM-dd HH:mm:ss" /></td>
                                            <td>
                                                <c:choose>
                                                    <c:when test="${hi.activityType == 'startEvent'}">
                                                        开始
                                                    </c:when>
                                                    <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                                        结束
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${hi.activityName}
                                                    </c:otherwise>
                                                </c:choose>
                                            </td>
                                            <td class="font-red">${commentMap[hi.taskId]}</td>
                                        </tr>
                                    </c:if>
                                </c:forEach>
                                <!-- 查询无结果弹出 -->

                                <c:if test="${empty historicActivityInstance}">
                                    <!-- 查询无结果弹出 -->
                                    <tr>
                                        <td colspan="4">暂无审核记录。</td>
                                    </tr>
                                </c:if>


                            </tbody>
                        </table>
                    </div>
                </li>

            </ul>
        </form>
    </div>
</div>
<%@ include file="../common/footer.jsp"%>