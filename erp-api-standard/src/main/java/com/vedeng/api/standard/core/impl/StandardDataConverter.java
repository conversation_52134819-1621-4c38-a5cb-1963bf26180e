package com.vedeng.api.standard.core.impl;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vedeng.api.standard.core.DataConverter;
import com.vedeng.api.standard.core.exception.ApiStandardException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 标准数据转换器实现
 * 提供通用的数据转换功能
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-27
 */
@Component
public class StandardDataConverter implements DataConverter {
    
    private static final Logger logger = LoggerFactory.getLogger(StandardDataConverter.class);
    
    private final ObjectMapper objectMapper;
    
    public StandardDataConverter() {
        this.objectMapper = new ObjectMapper();
        // 配置忽略未知属性，避免上游携带额外字段时转换失败
        this.objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }
    
    /**
     * 通用转换方法
     * 
     * @param source 源数据
     * @param targetClass 目标类型
     * @param <T> 目标类型泛型
     * @return 转换后的对象
     */
    @Override
    public <T> T convert(Object source, Class<T> targetClass) {
        if (source == null) {
            return null;
        }
        
        try {
            if (targetClass.isAssignableFrom(source.getClass())) {
                // 如果源对象已经是目标类型，直接返回
                return targetClass.cast(source);
            }
            
            if (source instanceof Map) {
                // 从Map转换
                return convertFromMap((Map<?, ?>) source, targetClass);
            } else {
                // 使用Jackson进行转换
                return objectMapper.convertValue(source, targetClass);
            }
            
        } catch (Exception e) {
            logger.error("数据转换失败: source={}, targetClass={}",
                source.getClass().getSimpleName(), targetClass.getSimpleName(), e);
            throw ApiStandardException.dataConversionError("数据转换失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 从Map转换为指定类型
     */
    private <T> T convertFromMap(Map<?, ?> sourceMap, Class<T> targetClass) {
        // 使用Jackson进行通用转换
        return objectMapper.convertValue(sourceMap, targetClass);
    }
}
