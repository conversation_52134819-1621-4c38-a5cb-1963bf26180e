package com.vedeng.system.controller;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.common.dto.SelectDto;
import com.github.pagehelper.PageHelper;
import com.vedeng.aftersales.model.dto.TraderAssociatedLogDto;
import com.vedeng.aftersales.service.WebAccountService;
import com.vedeng.authorization.model.*;
import com.vedeng.common.annotation.MethodLock;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.constant.BelongPlatformEnum;
import com.vedeng.common.constant.OrderDataUpdateConstant;
import com.vedeng.common.core.base.R;
import com.vedeng.common.exception.BusinessException;
import com.vedeng.common.orderstrategy.StrategyContext;
import com.vedeng.common.shiro.cas.CasClientHelper;
import com.vedeng.common.util.DateUtil;
import com.vedeng.infrastructure.sms.service.SmsService;
import com.vedeng.crm.api.enums.BelongPlatform;
import com.vedeng.erp.saleorder.api.AutoRegistrationService;
import com.vedeng.erp.saleorder.enums.WebAccountFromEnum;
import com.vedeng.order.model.Saleorder;
import com.vedeng.soap.ApiSoap;
import com.vedeng.system.model.OrgCallLineDto;
import com.vedeng.system.service.*;
import com.vedeng.trader.dao.RTraderJUserMapper;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.dao.WebAccountInvitationLogMapper;
import com.vedeng.trader.dao.WebAccountMapper;
import com.vedeng.trader.enums.TraderAssociatedLogEnum;
import com.vedeng.trader.model.RTraderJUser;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.WebAccount;
import com.vedeng.trader.model.WebAccountInvitationLog;
import com.vedeng.trader.model.vo.WebAccountVo;
import com.vedeng.trader.service.TraderAssociatedLogService;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import com.vedeng.common.annotation.SystemControllerLog;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.shiro.JedisUtils;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.common.util.Salt;
import com.vedeng.common.validator.FormToken;

/**
 * <b>Description:</b><br>
 * 员工管理
 *
 * <AUTHOR>
 * @Note <b>ProjectName:</b> erp <br>
 *       <b>PackageName:</b> com.vedeng.system.controller <br>
 *       <b>ClassName:</b> UserController <br>
 *       <b>Date:</b> 2017年4月25日 上午11:21:34
 */
@Controller
@RequestMapping("/system/user")
public class UserController extends BaseController {
	@Autowired
	@Qualifier("userService")
	private UserService userService;// 自动注入userService

	@Autowired
	@Qualifier("orgService")
	private OrgService orgService;// 自动注入orgService

	@Autowired
	@Qualifier("positService")
	private PositService positService;// 自动注入positService

	@Autowired
	@Qualifier("regionService")
	private RegionService regionService;// 自动注入regionService

	@Autowired
	@Qualifier("roleService")
	private RoleService roleService;// 自动注入roleService

	@Autowired
	@Qualifier("companyService")
	private CompanyService companyService;

	@Autowired
	@Qualifier("platformService")
	private PlatformService platformService;

	@Autowired
	@Qualifier("userBelongCompanyService")
	private UserBelongCompanyService userBelongCompanyService;

	@Autowired
	@Qualifier("apiSoap")
    private ApiSoap apiSoap;

	@Autowired
	private WebAccountService webAccountService;

	@Resource
	TraderMapper traderMapper;

	@Autowired
	private RTraderJUserMapper rTraderJUserMapper;

	@Resource
	WebAccountMapper webAccountMapper;

	@Autowired
	private WebAccountInvitationLogMapper webAccountInvitationLogMapper;

	@Autowired
	private AutoRegistrationService autoRegistrationService;

	@Value("${is_production_environment}")
	protected Integer isProductionEnvironment;

	@Value("${line_number_administrators}")
	protected String lineNumberAdministrators;

	@Resource
	private TraderAssociatedLogService traderAssociatedLogService;

	@Autowired
	private SmsService smsService;

	private final String DEFAULT_NAME = "南京贝登医疗股份有限公司";

	private final Integer DAFULT_ID_NO=0;//否
	private final Integer DAFULT_ID_YES=1;//是

	/**
	 * <b>Description:</b><br>
	 * 员工列表
	 *
	 * @param request
	 *            请求
	 * @param user
	 *            员工bean
	 * @param pageNo
	 *            当前页 默认1
	 * @param pageSize
	 *            每页条数
	 * @return
	 * @Note <b>Author:</b> Jerry <br>
	 *       <b>Date:</b> 2017年4月25日 上午11:21:44
	 */
	@ResponseBody
	@RequestMapping(value = "/index")
	public ModelAndView index(HttpServletRequest request, User user,
			@RequestParam(required = false, defaultValue = "1") Integer pageNo, // required
																				// =
																				// false:可不传入pageNo这个参数，true必须传入，defaultValue默认值，若无默认值，使用Page类中的默认值
			@RequestParam(required = false) Integer pageSize, HttpSession session,String flag) {
		User session_user = (User) session.getAttribute(ErpConst.CURR_USER);
		// 所有分公司
		List<Company> companyList = companyService.getAll();
		// 获取部门
		List<Organization> orgList = orgService.getOrgListNotDelete(0, session_user.getCompanyId(), true);

		// 获取职位
		List<Position> positList = new ArrayList<Position>();
		if (user.getOrgId() != null && user.getOrgId() > 0) {
			positList = positService.getPositByOrgId(user.getOrgId());
		}

		ModelAndView mv = new ModelAndView();
		mv.addObject("isLineAdmin", JSON.parseArray(lineNumberAdministrators, Integer.class).contains(session_user.getUserId()));
		// 查询集合
		Page page = getPageTag(request, pageNo, pageSize);
		if (null == user.getCompanyId()) {
			user.setCompanyId(session_user.getCompanyId());
		}

		List<User> userList = new ArrayList<>();

		List<Platform> platformList = platformService.queryList();

		if("1".equals(flag)){
			//当前是搜索
			if(StringUtils.isEmpty(user.getBelongCompanyName())){
				//直接查询
				userList  = userService.querylistPage(user, page);
			}else {
				UserBelongCompany exist = userBelongCompanyService.getUserCompanyByName(user.getBelongCompanyName());
				if(exist == null){

					//没有该所属公司，直接返回空
					mv.addObject("platformList", platformList);
					mv.addObject("companyList", companyList);
					mv.addObject("orgList", orgList);
					mv.addObject("positList", positList);
					mv.addObject("user", user);
					mv.addObject("users", userList);
					mv.addObject("page", page);
					mv.addObject("editStatus", "false");
					mv.setViewName("system/user/index");
					return mv;
				}else {
					//正常搜索
					user.setUserBelongCompanyId(exist.getUserBelongCompanyId());
					userList  = userService.querylistPage(user, page);
				}
			}
		}else {
			//当前不是索搜，直接查询
			userList  = userService.querylistPage(user, page);
		}

		List<UserBelongCompany> listCompany = userBelongCompanyService.queryAll();

		Map<Integer,String> mapCompany = listCompany.stream().
				collect(Collectors.toMap(UserBelongCompany::getUserBelongCompanyId, UserBelongCompany::getCompanyName));

		for(User each : userList){
			//id,id,id转name,name,name
			if(each.getSystems() != null) {
				each.setSystems(String.join(",", getSystems(Arrays.asList(each.getSystems().split(",")))));
			}
			each.setBelongCompanyName(mapCompany.get(each.getUserBelongCompanyId()) == null
					? DEFAULT_NAME : mapCompany.get(each.getUserBelongCompanyId()) );
		}

		// 页面传值
		mv.addObject("platformList", platformList);
		mv.addObject("companyList", companyList);
		mv.addObject("orgList", orgList);
		mv.addObject("positList", positList);
		mv.addObject("user", user);

		mv.addObject("editStatus", "false");
		mv.addObject("users", userList);
		mv.addObject("page", page);
		mv.setViewName("system/user/index");
		return mv;
	}

	/**
	 * 线路号码维护初始化
	 *
	 * @return
	 */
	@ResponseBody
	@NoNeedAccessAuthorization
	@RequestMapping(value = "/callLineInit")
	public ModelAndView callLineInit(){
		ModelAndView modelAndView = new ModelAndView("system/user/call_line");
		modelAndView.addObject("orgList", orgService.getOrgList(0, ErpConst.NJ_COMPANY_ID, true));
		return modelAndView;
	};


	/**
	 * 线路号码信息保存
	 *
	 * @param orgCallLine
	 * @return
	 */
	@ResponseBody
	@NoNeedAccessAuthorization
	@RequestMapping(value = "/callingLineSave")
	public ResultInfo callingLineSave(OrgCallLineDto orgCallLine) {
		logger.info("线路号码信息保存 orgCallLine:{}", JSON.toJSONString(orgCallLine));
		if (orgCallLine == null) {
			return ResultInfo.error("请求参数有误");
		}
		if (orgCallLine.getOrgId() == null){
			return ResultInfo.error("部门信息不能为空");
		}
		if (OrgCallLineDto.isUnValidData(orgCallLine)){
			return ResultInfo.error("至少要更新一种号码");
		}
		List<Integer> userIdsByOrgId = orgService.getUserIdsByOrgId(orgCallLine.getOrgId());
		if (CollectionUtils.isEmpty(userIdsByOrgId)){
			return ResultInfo.error("部门下无有效员工信息");
		}
		orgCallLine.setUserIds(userIdsByOrgId);
		userService.callLineInfoSave(orgCallLine);
		return ResultInfo.success();
	}

	/**
	 * <b>Description:</b><br>
	 * 新增管理员
	 *
	 * @param request
	 * @param session
	 * @return
	 * @Note <b>Author:</b> Jerry <br>
	 *       <b>Date:</b> 2017年8月9日 上午10:28:55
	 */
	@FormToken(save=true)
	@ResponseBody
	@RequestMapping(value = "/addmanager")
	public ModelAndView addManager(HttpServletRequest request, HttpSession session) {
		User session_user = (User) session.getAttribute(ErpConst.CURR_USER);
		ModelAndView mv = new ModelAndView();
		mv.setViewName("system/user/add_manager");
		// 所有分公司
		List<Company> companyList = companyService.getAll();
		Role role = new Role();
		role.setCompanyId(session_user.getCompanyId()); // 公司ID session中获取
		// 角色
		List<Role> roleList = roleService.getAllRoles(role);
		mv.addObject("companyList", companyList);
		mv.addObject("roleList", roleList);
		return mv;
	}

	/**
	 * <b>Description:</b><br>
	 * 保存管理员
	 *
	 * @param request
	 * @param session
	 * @param user
	 * @return
	 * @Note <b>Author:</b> Jerry <br>
	 * 		<b>Date:</b> 2017年8月9日 上午10:58:51
	 */
	@FormToken(remove=true)
	@ResponseBody
	@RequestMapping(value = "/savemanager")
	@SystemControllerLog(operationType = "add",desc = "保存新增管理员")
	public ResultInfo saveManager(HttpServletRequest request, HttpSession session, User user, UserDetail userDetail) {
		User session_user = (User) session.getAttribute(ErpConst.CURR_USER);
		ResultInfo resultInfo = new ResultInfo<>();
		try {
			user.setIsAdmin(ErpConst.ONE);
			Integer userId = userService.addUser(session, user, userDetail);
			if (userId > 0) {
				JedisUtils.del(dbType + ErpConst.KEY_PREFIX_MENU + userId);
				resultInfo.setCode(0);
				resultInfo.setMessage("操作成功");
			}
			return resultInfo;
		} catch (Exception e) {
			logger.error("savemanager:", e);
			return resultInfo;
		}
	}

	/**
	 * <b>Description:</b><br>
	 * 编辑管理员
	 *
	 * @param request
	 * @param session
	 * @param user
	 * @return
	 * @throws IOException
	 * @Note <b>Author:</b> Jerry <br>
	 * 		<b>Date:</b> 2017年8月10日 上午9:15:56
	 */
	@ResponseBody
	@RequestMapping(value = "/editmanager")
	public ModelAndView editManager(HttpServletRequest request, HttpSession session, User user, UserDetail userDetail) throws IOException {
		if (null == user.getUserId() || user.getUserId() <= 0) {
			return pageNotFound(request);
		}
		User session_user = (User) session.getAttribute(ErpConst.CURR_USER);
		ModelAndView mv = new ModelAndView();
		mv.setViewName("system/user/edit_manager");
		User userInfo = userService.getUserById(user.getUserId());
		// 所有分公司
		List<Company> companyList = companyService.getAll();
		Role role = new Role();
		role.setCompanyId(session_user.getCompanyId()); // 公司ID session中获取
		// 角色
		List<Role> roleList = roleService.getAllRoles(role);

		// 角色处理
		List<Role> userRoles = roleService.getUserRoles(user.getUserId());

		mv.addObject("user", userInfo);
		mv.addObject("userRoles", userRoles);
		mv.addObject("companyList", companyList);
		mv.addObject("roleList", roleList);
		mv.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(userInfo)));
		return mv;
	}

	/**
	 * <b>Description:</b><br>
	 * 保存编辑管理员
	 *
	 * @param request
	 * @param session
	 * @param user
	 * @param userDetail
	 * @return
	 * @Note <b>Author:</b> Jerry <br>
	 * 		<b>Date:</b> 2017年8月10日 上午9:27:07
	 */
	@ResponseBody
	@RequestMapping(value = "/saveeditmanager")
	@SystemControllerLog(operationType = "edit",desc = "保存编辑管理员")
	public ResultInfo saveEditManager(HttpServletRequest request, HttpSession session, User user,
			UserDetail userDetail,String beforeParams) {
		User session_user = (User) session.getAttribute(ErpConst.CURR_USER);
		ResultInfo resultInfo = new ResultInfo<>();
		try {
			if (null == user.getUserId() || user.getUserId() <= 0) {
				return resultInfo;
			}
			Integer userId = userService.editUser(session, user, userDetail);
			if (userId > 0) {
				JedisUtils.del(dbType + ErpConst.KEY_PREFIX_MENU + userId);
				resultInfo.setCode(0);
				resultInfo.setMessage("操作成功");
			}
			return resultInfo;
		} catch (Exception e) {
			logger.error("user saveeditmanager:", e);
			return resultInfo;
		}
	}

	/**
	 * <b>Description:</b><br>
	 * 添加/编辑员工
	 *
	 * @param request
	 *            请求
	 * @param user
	 *            员工bean
	 * @return
	 * @throws IOException
	 * @Note <b>Author:</b> Jerry <br>
	 *       <b>Date:</b> 2017年4月25日 上午11:22:17
	 */
	@FormToken(save=true)
	@ResponseBody
	@RequestMapping(value = "/modifyuser")
	public ModelAndView modifyUser(HttpServletRequest request, HttpSession session, User user) throws IOException {
		User session_user = (User) session.getAttribute(ErpConst.CURR_USER);
		ModelAndView mv = new ModelAndView();

		Integer companyId = 0;
		User userInfo = new User();
		if (!StringUtils.isEmpty(user) && null != user.getUserId() && user.getUserId() > 0) {
			userInfo = userService.getUserById(user.getUserId());



			companyId = userInfo.getCompanyId();

			mv.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(userInfo)));
			if(userInfo.getSystems() == null){
				mv.addObject("loginSystemList",null);
				mv.addObject("belongCompany", null);
			}else {
				UserBelongCompany exist = userBelongCompanyService.getUserCompanyById(userInfo.getUserBelongCompanyId());
				mv.addObject("loginSystemList", Arrays.asList(userInfo.getSystems().split(",")));
				mv.addObject("belongCompany", exist == null ? null : exist.getCompanyName());
			}
		} else {
			companyId = session_user.getCompanyId();
		}
		// 获取部门
		List<Organization> orgList = orgService.getOrgListNotDelete(0, companyId, true);

		// 获取人员
		user.setCompanyId(companyId);
		user.setIsDisabled(ErpConst.ZERO);
		List<User> userList = userService.getAllUser(user);

		// 地区
		List<Region> provinceList = regionService.getRegionByParentId(1);

		Role role = new Role();
		role.setCompanyId(companyId); // 公司ID session中获取
		// 角色
		List<Role> roleList = roleService.getAllRoles(role);

		if (!StringUtils.isEmpty(user) && null != user.getUserId() && user.getUserId() > 0) {
			// 编辑
			// 获取职位
			List<Position> userPositionList = positService.getPositionByUserId(user.getUserId());
			List<Object> orgPositList = new ArrayList<Object>();

			for (Position p : userPositionList) {
				Map map = new HashMap();

				// 获取职位
				List<Position> positList = positService.getPositByOrgId(p.getOrgId());

				map.put("orgList", orgList);
				map.put("positList", positList);
				map.put("orgId", p.getOrgId());
				map.put("positId", p.getPositionId());

				orgPositList.add(map);
			}

			// 地区处理
			if (!StringUtils.isEmpty(userInfo.getUserAddress())) {
				Integer areaId = userInfo.getUserAddress().getAreaId();
				List<Region> regionList = (List<Region>) regionService.getRegion(areaId, 1);

				if (!StringUtils.isEmpty(regionList)) {
					for (Region r : regionList) {
						switch (r.getRegionType()) {
						case 1:
							List<Region> cityList = regionService.getRegionByParentId(r.getRegionId());
							mv.addObject("provinceRegion", r);
							mv.addObject("cityList", cityList);
							break;
						case 2:
							List<Region> zoneList = regionService.getRegionByParentId(r.getRegionId());
							mv.addObject("cityRegion", r);
							mv.addObject("zoneList", zoneList);
							break;
						case 3:
							mv.addObject("zoneRegion", r);
							break;
						default:
							mv.addObject("countryRegion", r);
							break;
						}
					}
				}
			}

			// 角色处理
			List<Role> userRoles = roleService.getUserRoles(user.getUserId());

			mv.addObject("userRoles", userRoles);
			mv.addObject("orgPositList", orgPositList);
		}


		mv.addObject("orgList", orgList);
		mv.addObject("userList", userList);
		mv.addObject("provinceList", provinceList);
		mv.addObject("roleList", roleList);
		mv.addObject("user", userInfo);

		if (null != request.getParameter("allErrors")) {
			mv.addObject("allErrors", request.getParameter("allErrors"));
		}
		mv.setViewName("system/user/modify_user");
		mv.addObject("newCallCenterFlag", isNewCallCenterFlag(user));

		List<Platform> platforms = platformService.queryList();
		mv.addObject("platformList", platforms);

		return mv;
	}

	/**
	 * <b>Description:</b><br>
	 * 保存添加/编辑员工
	 *
	 * @param request
	 * @param user
	 *            员工bean
	 * @return
	 * @throws IOException
	 * @Note <b>Author:</b> Jerry <br>
	 *       <b>Date:</b> 2017年4月25日 上午11:22:40
	 */
	@FormToken(remove=true)
	@ResponseBody
	@RequestMapping(value = "/saveuser")
	@SystemControllerLog(operationType = "add",desc = "保存新增用户")
	public ModelAndView saveUser(HttpServletRequest request, HttpSession session, @Validated User user,
			BindingResult bindingResult, @Validated UserDetail userDetail, BindingResult bindingResultUserDetail,
			@Validated UserAddress userAddress, BindingResult bindingResultUserAddress) throws IOException {
		ModelAndView mv = new ModelAndView();
		List<ObjectError> allErrors = new ArrayList<>();
		if (bindingResult.hasErrors()) {
			List<ObjectError> Errors = bindingResult.getAllErrors();
			for (ObjectError o : Errors) {
				allErrors.add(o);
			}
		}
		if (bindingResultUserDetail.hasErrors()) {
			List<ObjectError> Errors = bindingResultUserDetail.getAllErrors();
			for (ObjectError o : Errors) {
				allErrors.add(o);
			}
		}
		if (bindingResultUserAddress.hasErrors()) {
			List<ObjectError> Errors = bindingResultUserAddress.getAllErrors();
			for (ObjectError o : Errors) {
				allErrors.add(o);
			}
		}
		if (allErrors.size() > 0) {
			request.setAttribute("allErrors", allErrors);
			return this.modifyUser(request, session, user);
		}
		try {
			Integer userId = userService.modifyUser(session, user, userDetail, userAddress);
			if (userId > 0) {
				mv.addObject("url", "./viewuser.do?userId=" + userId);
				//删除用户菜单
				JedisUtils.del(dbType + ErpConst.KEY_PREFIX_MENU + userId);
				return success(mv);
			} else {
				mv.addObject("message","数据未更新");
				return fail(mv);
			}
		} catch (Exception e) {
			logger.error("user saveuser:", e);
			mv.addObject("message",e.getMessage());
			return fail(mv);
		}
	}
	/**
	 * <b>Description:</b><br> 保存编辑用户信息
	 * @param request
	 * @param session
	 * @param user
	 * @param bindingResult
	 * @param userDetail
	 * @param bindingResultUserDetail
	 * @param userAddress
	 * @param bindingResultUserAddress
	 * @return
	 * @throws IOException
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年9月18日 下午2:23:02
	 */
	@ResponseBody
	@RequestMapping(value = "/saveedituser")
	@SystemControllerLog(operationType = "edit",desc = "保存编辑用户")
	public ModelAndView saveEditUser(HttpServletRequest request, HttpSession session, @Validated User user,
			BindingResult bindingResult, @Validated UserDetail userDetail, BindingResult bindingResultUserDetail,
			@Validated UserAddress userAddress, BindingResult bindingResultUserAddress,String beforeParams) throws IOException {
		ModelAndView mv = new ModelAndView();
		List<ObjectError> allErrors = new ArrayList<>();
		if (bindingResult.hasErrors()) {
			List<ObjectError> Errors = bindingResult.getAllErrors();
			for (ObjectError o : Errors) {
				allErrors.add(o);
			}
		}
		if (bindingResultUserDetail.hasErrors()) {
			List<ObjectError> Errors = bindingResultUserDetail.getAllErrors();
			for (ObjectError o : Errors) {
				allErrors.add(o);
			}
		}
		if (bindingResultUserAddress.hasErrors()) {
			List<ObjectError> Errors = bindingResultUserAddress.getAllErrors();
			for (ObjectError o : Errors) {
				allErrors.add(o);
			}
		}
		if (allErrors.size() > 0) {
			request.setAttribute("allErrors", allErrors);
			return this.modifyUser(request, session, user);
		}
		try {
			Integer userId = userService.modifyUser(session, user, userDetail, userAddress);
			if (userId > 0) {
//				 int i=updateTraderBelongFormPlat(userId);
//				 if(i==0){
//					 mv.addObject("url", "./viewuser.do?userId=" + userId);
//					 mv.addObject("message","userId为空");
//					 return fail(mv);
//				 }
				mv.addObject("url", "./viewuser.do?userId=" + userId);
				//删除用户菜单
				JedisUtils.del(dbType + ErpConst.KEY_PREFIX_MENU + userId);
				JedisUtils.del(dbType + ErpConst.KEY_PREFIX_GROUP_MENU + userId);
				return success(mv);
			} else {
				return fail(mv);
			}
		} catch (Exception e) {
			logger.error("saveedituser:", e);
			return fail(mv);
		}
	}


	public Integer updateTraderBelongFormPlat(Integer userId){
		if(userId==null){
			return 0;
		}
		RTraderJUser rTraderJUser = new RTraderJUser();
		rTraderJUser.setUserId(userId);
		rTraderJUser.setTraderType(1);
		rTraderJUserMapper.getRTraderJUserListByUserId(rTraderJUser).parallelStream().forEach(
				item -> {
					Trader trader = traderMapper.getTraderByTraderId(item);
					//VDERP-5598 如果客户目前归属平台是集采，那么变更归属销售时，不同步更新其归属平台
					if (!BelongPlatformEnum.JC.getBelong().equals(trader.getBelongPlatform())){
						Integer belongPlatform= userService.getBelongPlatformOfUser(userId,ErpConst.ONE);
						Trader traderToUpdate=new Trader();
						traderToUpdate.setBelongPlatform(belongPlatform);
						traderToUpdate.setUserId(userId);
						traderToUpdate.setTraderId(trader.getTraderId());
						int update =traderMapper.updateBelongPlatformOfTrader(traderToUpdate);

						if(update > 0){
							WebAccountVo webAccountVo = new WebAccountVo();
							webAccountVo.setUserTraderId(userId);
							webAccountVo.setBelongPlatform(belongPlatform);
							webAccountVo.setModTime(new Date());
							webAccountVo.setTraderId(trader.getTraderId());
							int i = webAccountMapper.updateErpUserId(webAccountVo);
						}
					}
				}
		);
		return 1;
	}
	/**
	 *
	 * <b>Description:</b><br>
	 * 启用/禁用员工
	 *
	 * @param user
	 *            员工bean
	 * @return ResultInfo<User> json
	 * @Note <b>Author:</b> Jerry <br>
	 *       <b>Date:</b> 2017年4月25日 上午11:23:04
	 */
	@ResponseBody
	@RequestMapping(value = "/changedisabled")
	@SystemControllerLog(operationType = "edit",desc = "启用/禁用用户")
	public ResultInfo<User> changeDisabled(User user) {
		Boolean suc = userService.changDisabled(user);
		if (1 == user.getIsDisabled()) {
			if (JedisUtils.exists(dbType + ErpConst.KEY_PREFIX_USERID_SESSIONID + user.getUserId())) {
				String sessionId = JedisUtils.get(dbType + ErpConst.KEY_PREFIX_USERID_SESSIONID + user.getUserId());
				JedisUtils.del(ErpConst.KEY_PREFIX_SESSION + sessionId);
			}
		}
		ResultInfo<User> result = new ResultInfo<User>();

		if (suc) {// 成功
			result.setCode(0);
			result.setMessage("操作成功");
		}
		return result;
	}

	/**
	 * <b>Description:</b><br>
	 * 查看员工
	 *
	 * @param user
	 * @return ModelAndView
	 * @Note <b>Author:</b> Jerry <br>
	 *       <b>Date:</b> 2017年4月26日 上午8:42:09
	 */
	@ResponseBody
	@RequestMapping(value = "viewuser")
	public ModelAndView viewUser(HttpServletRequest request, User user) {
		if (StringUtils.isEmpty(user) || null == user.getUserId() || user.getUserId() <= 0) {
			return pageNotFound(request);
		}
		ModelAndView mv = new ModelAndView();

		User userInfo = userService.getUserById(user.getUserId());

		if (null == userInfo) {
			return pageNotFound(request);
		}

		// 地区
		if (!StringUtils.isEmpty(userInfo.getUserAddress()) && null != userInfo.getUserAddress().getAreaId()
				&& userInfo.getUserAddress().getAreaId() > 0) {
			Integer areaId = userInfo.getUserAddress().getAreaId();
			String region = (String) regionService.getRegion(areaId, 2);

			mv.addObject("region", region);
		}

		// 角色处理
		List<Role> userRoles = roleService.getUserRoles(user.getUserId());

		UserBelongCompany exist = userBelongCompanyService.getUserCompanyById(userInfo.getUserBelongCompanyId());

		mv.addObject("belongCompany", exist == null ? DEFAULT_NAME : exist.getCompanyName());
		mv.addObject("showSystems",
				String.join(",",getSystems(Arrays.asList(userInfo.getSystems().split(",")))));
		mv.addObject("userRoles", userRoles);
		mv.addObject("user", userInfo);
		mv.setViewName("system/user/view_user");
		return mv;
	}

	/**
	 * <b>Description:</b><br>
	 * 异步查询员工
	 *
	 * @param user
	 *            员工bean
	 * @return ResultInfo<User>
	 * @Note <b>Author:</b> Jerry <br>
	 *       <b>Date:</b> 2017年4月27日 上午9:25:45
	 */
	@ResponseBody
	@RequestMapping(value = "getuser")
	public ResultInfo<User> getUser(User user, HttpSession session) {
		ResultInfo<User> resultInfo = new ResultInfo<User>();
		User session_user = (User) session.getAttribute(ErpConst.CURR_USER);
		// 用户名判断
		if (user.getUsername() != null && user.getUsername() != "") {
			User u = new User();
			if (user.getCompanyId() != null && user.getCompanyId() > 0) {
				u.setCompanyId(user.getCompanyId());
			} else {
				u.setCompanyId(session_user.getCompanyId());
			}
			u.setUsername(user.getUsername());

			User info = userService.getUserByName(u);

			if (null != info) {
				if (null != user.getUserId() && !user.getUserId().equals(0)) {// 编辑用户
					if (!user.getUserId().equals(info.getUserId())) {
						resultInfo.setMessage("用户名不允许重复");
						return resultInfo;
					}
				} else {// 新增
					resultInfo.setMessage("用户名不允许重复");
					return resultInfo;
				}
			}
			// if(null != info && null != user.getUserId() &&
			// !user.getUserId().equals(info.getUserId())){
			//
			// resultInfo.setMessage("用户名不允许重复");
			// return resultInfo;
			// }
			// if ((null != user.getUserId() && user.getUserId() > 0 &&
			// !StringUtils.isEmpty(info)
			// && user.getUserId() != info.getUserId())
			// || (null == user.getUserId() && !StringUtils.isEmpty(info))) {
			// if (null != user.getUsername() && user.getUsername() != ""
			// &&
			// (user.getUsername().toLowerCase()).equals(info.getUsername().toLowerCase()))
			// {
			// }
			// }
		}
		// 工号判断
		if (user.getNumber() != null && user.getNumber() != "") {
			User u = new User();
			u.setCompanyId(session_user.getCompanyId());
			u.setNumber(user.getNumber());

			User info = userService.getUserByNumber(u);
			if (null != info) {
				if (null != user.getUserId() && !user.getUserId().equals(0)) {// 编辑用户
					if (!user.getUserId().equals(info.getUserId())) {
						resultInfo.setMessage("工号不允许重复");

						return resultInfo;
					}
				} else {// 新增
					resultInfo.setMessage("工号不允许重复");

					return resultInfo;
				}
			}
			// if(null != info && null != user.getUserId() &&
			// !user.getUserId().equals(info.getUserId())){
			//
			// resultInfo.setMessage("工号不允许重复");
			//
			// return resultInfo;
			// }
			// if ((null != user.getUserId() && user.getUserId() > 0 &&
			// !StringUtils.isEmpty(info)
			// && user.getUserId() != info.getUserId())
			// || (null == user.getUserId() && !StringUtils.isEmpty(info))) {
			// if (null != user.getNumber() && user.getNumber() != "" &&
			// user.getNumber().equals(info.getNumber())) {
			// }
			// }
		}

		resultInfo.setCode(0);
		resultInfo.setMessage("操作成功");
		return resultInfo;
	}

	/**
	 * <b>Description:</b><br> 获取部门用户
	 * @param request
	 * @param orgId
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2018年1月2日 上午11:33:59
	 */
	@ResponseBody
	@RequestMapping(value = "getUserListByOrgId")
	public ResultInfo<?> getUserListByOrgId(HttpServletRequest request, @RequestParam("orgId") Integer orgId) {
		ResultInfo<User> result = new ResultInfo<>();
		List<User> userList = userService.getUserListByOrgId(orgId);
		result.setCode(0);
		result.setMessage("操作成功");
		result.setListData(userList);
		return result;
	}

	/**
	 * <b>Description:</b><br>
	 * 个人设置
	 *
	 * @param request
	 * @param user
	 * @param session
	 * @return
	 * @Note <b>Author:</b> Jerry <br>
	 *       <b>Date:</b> 2017年6月28日 上午10:23:11
	 */
	@ResponseBody
	@RequestMapping(value = "myinfo")
	public ModelAndView myInfo(HttpServletRequest request, User user, HttpSession session) {
		ModelAndView mv = new ModelAndView("system/user/my_info");
		User session_user = (User) session.getAttribute(ErpConst.CURR_USER);
		if (StringUtils.isEmpty(user) || null == user.getUserId() || user.getUserId() <= 0
				|| !user.getUserId().equals(session_user.getUserId())) {
			return pageNotFound(request);
		}

		User userInfo = userService.getUserById(user.getUserId());

		if (null == userInfo) {
			return pageNotFound(request);
		}
		// 地区
		if (!StringUtils.isEmpty(userInfo.getUserAddress()) && null != userInfo.getUserAddress().getAreaId()
				&& userInfo.getUserAddress().getAreaId() > 0) {
			Integer areaId = userInfo.getUserAddress().getAreaId();
			String region = (String) regionService.getRegion(areaId, 2);

			mv.addObject("region", region);
		}

		// 角色处理
		List<Role> userRoles = roleService.getUserRoles(user.getUserId());

		if (CasClientHelper.enableSingleSignOn()) {
			String property = CasClientHelper.getProperty("common.uacPrefixUrl")+ "/account/toEditPassWord?accountId=" + user.getUserId();
			mv.addObject("commonModifyPasswordUrl", property);
		}
		mv.addObject("userRoles", userRoles);
		mv.addObject("user", userInfo);
		return mv;
	}

	/**
	 * <b>Description:</b><br>
	 * 修改密码
	 *
	 * @param request
	 * @param user
	 * @param session
	 * @return
	 * @Note <b>Author:</b> Jerry <br>
	 *       <b>Date:</b> 2017年6月28日 上午10:56:15
	 */
	@ResponseBody
	@RequestMapping(value = "modifypassword")
	public ModelAndView modifyPassword(HttpServletRequest request, User user, HttpSession session) {
		ModelAndView mv = new ModelAndView("system/user/modify_password");
		User session_user = (User) session.getAttribute(ErpConst.CURR_USER);
		if (StringUtils.isEmpty(user) || null == user.getUserId() || user.getUserId() <= 0
				|| !user.getUserId().equals(session_user.getUserId())) {
			return pageNotFound(request);
		}
		return mv;
	}

	/**
	 * <b>Description:</b><br>
	 * 保存修改密码
	 *
	 * @param request
	 * @param user
	 * @param session
	 * @return
	 * @Note <b>Author:</b> Jerry <br>
	 *       <b>Date:</b> 2017年6月28日 上午11:32:22
	 */
	@ResponseBody
	@RequestMapping(value = "savemodifypassword")
	@SystemControllerLog(operationType = "edit",desc = "保存修改密码")
	public ResultInfo<?> saveModifyPassword(HttpServletRequest request, User user, HttpSession session) {
		User session_user = (User) session.getAttribute(ErpConst.CURR_USER);
		ResultInfo resultInfo = new ResultInfo<>();
		if (StringUtils.isEmpty(user) || null == user.getUserId() || user.getUserId() <= 0
				|| !user.getUserId().equals(session_user.getUserId())) {
			return resultInfo;
		}
		// 旧密码是否正确
		User userInfo = userService.getUserById(user.getUserId());
		if (!DigestUtils.md5Hex(request.getParameter("oldpassword") + userInfo.getSalt()).toString()
				.equals(userInfo.getPassword())) {
			resultInfo.setMessage("旧密码不正确");
			return resultInfo;
		}
		//生产密码禁止使用123456
		if (isProductionEnvironment != null && isProductionEnvironment == 1 &&
				ErpConst.SIMPLE_PASSWORD.equals(user.getPassword())){
			resultInfo.setMessage("密码不允许使用'123456'");
			return resultInfo;
		}
		// 修改密码
		int res = updatePassword(user);
		if (res > 0) {
			resultInfo.setCode(0);
			resultInfo.setMessage("操作成功");
		}
		return resultInfo;
	}

	/**
	 * <b>Description:</b>配置列表字段
	 * @return ModelAndView
	 * @Note
	 * <b>Author：</b> lijie
	 * <b>Date:</b> 2019年3月12日 下午1:47:32
	 */
	@ResponseBody
	@RequestMapping(value = "/define")
	public ModelAndView define(){
		ModelAndView modelAndView = new ModelAndView();
		modelAndView.setViewName("system/user/define");
		return modelAndView;
	}

	/**
	  * ids -> names
	  * <AUTHOR>
	  * @date  2019/6/12 14:59
	  * @param
	  * @return
	  */
	private List<String> getSystems(List<String> systems){
		List<Platform> platforms = platformService.queryList();
		List<String> showSystems = new ArrayList<>();
		for(Platform platform : platforms){
			if(systems.contains(String.valueOf(platform.getPlatformId()))){
				showSystems.add(platform.getPlatformName());
			}
		}
		return showSystems;
	}
	/**
	 * <b>Description:</b>根据手机号查询手机用户是否是老用户  是修改申请加入贝登精选的状态
	 * @return resultInfo
	 * @Note
	 * <b>Author：</b> Addis
	 * <b>Date:</b> 2019年7月1日 下午1:47:32
	 */

	@ResponseBody
	@RequestMapping(value = "/updateIsVedengState",method = RequestMethod.POST)
	public ResultInfo updateIsVedengState(@RequestBody(required = false) WebAccount webAccount){

		try {
			logger.info("updateIsVedengState:接收到数据" + DateUtil.getNowDate("yyyy-MM-dd HH:mm:ss") + JSONObject.toJSONString(webAccount));
		int modelNumCount = apiSoap.getMobileCount(webAccount);    //根据手机号查询是否有该用户
		if (modelNumCount >= DAFULT_ID_YES) {
			WebAccount webAccountRecond = apiSoap.selectMobileResult(webAccount);    //根据手机号查询该用户失效状态，是否申请加入贝登精选，是否是会员
			if (webAccountRecond.getIsEnable() == DAFULT_ID_NO) {
			return new ResultInfo(-1, "该用户已失效", JSON.toJSON(webAccountRecond));
			}
			if (webAccountRecond.getIsEnable() == DAFULT_ID_YES) {                //判断是否有效
			if (webAccountRecond.getIsVedengJoin() != null && webAccountRecond.getIsVedengJoin() == DAFULT_ID_YES) {
				if (webAccountRecond.getWebAccountId().equals(webAccount.getWebAccountId())){
					apiSoap.updateisVedengJoin(webAccount);
				}
			return new ResultInfo(-1, "该用户已申请", JSON.toJSON(webAccountRecond));
			} else {
				webAccount.setModTimeJoin(DateUtil.gainNowDate());
				if (webAccountRecond.getWebAccountId().equals(webAccount.getWebAccountId())){
					if (apiSoap.updateisVedengJoin(webAccount) > DAFULT_ID_NO) { //如果有该用户，则更改该用户申请加入状态
						WebAccount webAccountThree = apiSoap.selectMobileResult(webAccount);
						return new ResultInfo(0, "返回数据成功", JSON.toJSON(webAccountThree));
					}
				}
			}
			}
		} else {
			//注册平台默认为贝登医疗
			if (StringUtils.isEmpty(webAccount.getRegisterPlatform())){
				webAccount.setRegisterPlatform(1);
			}
			webAccount.setBelongPlatform(webAccountService.getBelongPlatformOfAccount(webAccount.getUserId(),webAccount.getTraderId(),webAccount.getRegisterPlatform()));
			webAccount.setModTimeJoin(DateUtil.gainNowDate());
			webAccount.setIsEnable(DAFULT_ID_YES);
			webAccount.setAddTime(DateUtil.gainNowDate());
			if (apiSoap.insert(webAccount) > DAFULT_ID_NO) {     //如果没有该用户，则添加一条新用户
			WebAccount webAccountFour = apiSoap.selectMobileResult(webAccount);
			return new ResultInfo(0, "返回数据成功", JSON.toJSON(webAccountFour));
			}
		}
	}catch (Exception e){
		   logger.error("execute fail",e);
	}
		   return new ResultInfo(-1,"执行失败");
	}

	/**
	 * 贝登、科研购账号同步接口
	 * <b>Description:</b>根据手机号查询手机用户是否是老用户
	 * @return resultInfo
	 * @Note
	 * <b>Author：</b> Addis
	 * <b>Date:</b> 2019年7月1日 下午1:47:32
	 */

	@ResponseBody
	@RequestMapping(value = "/doJxAcountData",method = RequestMethod.POST)
	@MethodLock(className = WebAccount.class,field = "mobile")
	public ResultInfo doJxAcountData(@RequestBody(required = false) WebAccount webAccount){
		logger.info("doJxAcountData:接收到数据" + DateUtil.getNowDate("yyyy-MM-dd HH:mm:ss") + JSONObject.toJSONString(webAccount));
		try{
			webAccount.setCompanyName(StringEscapeUtils.escapeHtml4(webAccount.getCompanyName()));
			webAccount.setName(StringEscapeUtils.escapeHtml4(webAccount.getName()));

		int modelNumCount = apiSoap.getMobileCount(webAccount);
		    //根据手机号查询该用户失效状态，是否申请加入贝登精选，是否是会员
		Integer result = 0;
		if (modelNumCount >= DAFULT_ID_YES) {
			//判断账号是否来自同一平台，只有来自同一平台才去更新账号信息
			WebAccount webAccountRecond = apiSoap.selectMobileResult(webAccount);
			if (webAccountRecond.getWebAccountId().equals(webAccount.getWebAccountId()) || webAccountRecond.getSsoAccountId().equals(webAccount.getSsoAccountId())){
				logger.info("doJxAcountData:更新WebAccount信息,"+ JSONUtil.toJsonStr(webAccount));
				result = apiSoap.updateisVedengJoin(webAccount);
			}
		}else {
			webAccount.setIsEnable(DAFULT_ID_YES);
			webAccount.setAddTime(DateUtil.gainNowDate());
			//默认注册平台为贝登医疗
			// VDERP6719 当注册平台为区域商城的时候，RegisterPlatform设置为7（和原有逻辑保持一致，防止设置为null导致的异常）
			if (StringUtils.isEmpty(webAccount.getRegisterPlatform())){
				webAccount.setRegisterPlatform(1);
			}

			// 区域商城的注册用户，归属平台规则为：设置为首次登录的平台id
			if (webAccount.getRegisterPlatform() != 7) {
				webAccount.setBelongPlatform(webAccountService.getBelongPlatformOfAccount(webAccount.getUserId(),webAccount.getTraderId(),webAccount.getRegisterPlatform()));
			}

			//    VDERP-2520【在线化项目】VS订单线上化-相关的ERP改动、消息推送
			//    某手机号在贝登医疗新注册时，若该手机号存在2020.6.16之后生效的VS订单（指：订单详情页-客户信息-手机，与该注册账号一致），
			//    则将该手机号与这些订单中，生效时间最近的VS订单对应的客户自动创建关联。（已有的注册账号不再自动关联）
			List<Saleorder> saleorders=webAccountService.selectLatestSaleOrderByMobile(webAccount.getMobile());
			if (webAccount.getFrom() != null && (Objects.equals(webAccount.getFrom(), WebAccountFromEnum.AUTO_BUSINESS.getCode()) || Objects.equals(webAccount.getFrom(), WebAccountFromEnum.AUTO_ORDER.getCode()))){
				if (webAccount.getTraderId()==null||webAccount.getTraderContactId()==null) {
					log.error("自动注册回传必要信息不全：traderId:{},traderContactId:{}",webAccount.getTraderId(),webAccount.getTraderContactId());
					throw new BusinessException("自动注册回传必要信息不全!");
				}
				// 销售分配
				webAccount.setUserId(autoRegistrationService.relatedSellerByTraderId(webAccount.getTraderId()));
			}else{
				if(!CollectionUtils.isEmpty(saleorders)){

					webAccount.setTraderId(saleorders.get(0).getTraderId());
					webAccount.setUserId(autoRegistrationService.relatedSellerByTraderId(webAccount.getTraderId()));

					Organization org=orgService.getOrgNameByUserId(webAccount.getUserId());
					if(org!=null){
						webAccount.setOrgId(org.getOrgId());
					}
					logger.info("pushVSOrder 注册用户包含VS订单 {} ",JsonUtils.convertObjectToJsonStr(webAccount));
				}

			}
			//VDERP-3856 S2B2C渠道邀请需提供客户信息 保存注册账号时，判断该账号是否是被邀请的
			if (org.apache.commons.lang3.StringUtils.isNotBlank(webAccount.getInviteeMobile()) && webAccount.getInviteeTraderId() != null && webAccount.getInviteeTraderId() > 0){
				//保存邀请记录
				WebAccountInvitationLog webAccountInvitationLog = new WebAccountInvitationLog();
				webAccountInvitationLog.setInviterMobile(webAccount.getInviteeMobile());
				webAccountInvitationLog.setInviteeTraderId(webAccount.getInviteeTraderId());
				webAccountInvitationLog.setRegisterMobile(webAccount.getMobile());
				webAccountInvitationLog.setRerigterPlatform(1);
				webAccountInvitationLog.setCreateTime(DateUtil.sysTimeMillis());
				webAccountInvitationLogMapper.insertSelective(webAccountInvitationLog);
			}

			result = apiSoap.insert(webAccount);

			//记录贝登关联客户信息
			if(webAccount.getTraderId()!=null && webAccount.getTraderId()> 0
					&& BelongPlatform.BD.getBelongPlatform().equals(webAccount.getBelongPlatform())){

				TraderAssociatedLogDto traderAssociatedLogDto = new TraderAssociatedLogDto();
				traderAssociatedLogDto.setErpAccountId(webAccount.getErpAccountId());
				traderAssociatedLogDto.setWebAccountToBelongPlatformNo(BelongPlatform.BD.getBelongPlatform());
				traderAssociatedLogDto.setOperationType(TraderAssociatedLogEnum.BIND.getType());
				traderAssociatedLogDto.setTraderId(webAccount.getTraderId());
				final Trader traderQuery = traderMapper.getTraderByTraderId(webAccount.getTraderId());
				if( traderQuery != null){
					traderAssociatedLogDto.setRemark("关联公司:"+traderQuery.getTraderName());
				}
				traderAssociatedLogDto.setReason("系统自动关联");
				traderAssociatedLogDto.setOperatorId(ErpConst.NJ_ADMIN_ID);
				traderAssociatedLogDto.setOperatorName("njadmin(管理员)");
				traderAssociatedLogDto.setOperatorOrganization("B2B事业部");
				traderAssociatedLogService.recordTraderAssociatedLog(traderAssociatedLogDto);
			}

			//推送历史的vs订单

			if(!CollectionUtils.isEmpty(saleorders)){
				logger.info("pushVSOrder 注册用户 推送VS订单"+webAccount.getMobile());

				saleorders.forEach(item->{
					StrategyContext strategyContext = new StrategyContext();
					strategyContext.add(StrategyContext.PUSH_PC_STRATEGY, OrderDataUpdateConstant.SALE_ORDER_VAILD);
					strategyContext.executeAll(item.getSaleorderId());
				});
			}

			// ----- 商机关联客户成功后&用户未注册，经ERP推送该用户信息到前台完成自动注册，后台收到前台回传的用户信息时同步向该用户发送短信。----
			if (webAccount.getFrom() != null && webAccount.getFrom().equals(WebAccountFromEnum.AUTO_BUSINESS.getCode())) {
				Boolean sendTplSms = smsService.sendTplSms(webAccount.getMobile(), "JSM40187-0076", "");
				if (Boolean.TRUE.equals(sendTplSms)) {
					log.info("短信发送成功！mobile:{},tplId:{}", webAccount.getMobile(), "JSM40187-0076");
				} else {
					log.error("短信发送失败！mobile:{},tplId:{}", webAccount.getMobile(), "JSM40187-0076");
				}
			}


		}
			if (result > 0){
				return new ResultInfo(0, "操作成功");
			}
			return new ResultInfo(-1,"执行失败");
		}catch(Exception e){
			logger.error("",e);
			return new ResultInfo(-1,"执行失败"+e.getMessage());
		}
	}
    /**
    * @Description: 发送申请通过消息
    * @Param: [webAccountRecond]
    * @return: void
    * @Author: addis
    * @Date: 2019/9/27
    */
/*	public  void booleanSend(WebAccount webAccountRecond){
		if(webAccountRecond.getIsSendMessage()==0){
			Boolean flag=passReminderMsg(webAccountRecond);
			if(flag==true){
				WebAccount webAccount1=new WebAccount();
				webAccount1.setMobile(webAccountRecond.getMobile());
				webAccount1.setIsSendMessage(1);
				apiSoap.updateisVedengJoin(webAccount1);
			}
		}
	}
}*/

	/**
	 * <b>Description:</b><br>
	 * 重置密码
	 *
	 * @param user
	 * @param session
	 * @return
	 * @Note <b>Author:</b> hugo <br>
	 *       <b>Date:</b> 2020年5月20日 上午11:32:22
	 */
	@RequestMapping(value = "resetPassword")
	@SystemControllerLog(operationType = "edit",desc = "重置密码")
	public String resetPassword(String oldPassword, User user, Model model, HttpSession session) {
		User session_user = (User) session.getAttribute(ErpConst.CURR_USER);
		user.setUserId(session_user != null ? session_user.getUserId() : 0);
		if (StringUtils.isEmpty(user) || null == user.getUserId() || user.getUserId() <= 0
				|| !user.getUserId().equals(user.getUserId())) {
			model.addAttribute("msg","用户未登陆");
			return "/login";
		}
		// 旧密码是否正确
		User userInfo = userService.getUserById(user.getUserId());
		if (!DigestUtils.md5Hex(oldPassword + userInfo.getSalt()).toString()
				.equals(userInfo.getPassword())) {
			model.addAttribute("msg","旧密码不正确");
			return "system/user/reset_password";
		}
		//密码禁止使用‘123456’
		if (ErpConst.SIMPLE_PASSWORD.equals(user.getPassword())){
			model.addAttribute("msg","密码不允许使用'123456'");
			return "system/user/reset_password";
		}
		// 修改密码
		int res = updatePassword(user);
		if (res > 0) {
			session.removeAttribute("is_need_reset");
			return "redirect:/index.do";
		}
		return "system/user/reset_password";
	}

	private int updatePassword(User user) {
		Salt salt = new Salt();
		String p_salt = salt.createSalt(false);
		user.setSalt(p_salt);
		user.setPassword(DigestUtils.md5Hex(user.getPassword() + p_salt).toString());

		return userService.modifyPassowrd(user);
	}

	/**
	 * 根据用户名和权限uri判断该用户是否拥有这个权限
	 *
	 * @param uri 权限uri
	 * @return Boolean
	 */
	@RequestMapping(value = "/hasPermission")
	@NoNeedAccessAuthorization
	@ResponseBody
	public R<Boolean> hasPermission(@RequestParam String uri) {
		return R.success(userService.judgeHasPermission(uri));
	}

	/**
	 * 根据用户名模糊搜索所有启用的用户集合（不包括自己）
	 *
	 * @param username 用户名
	 * @return List<User>
	 */
	@RequestMapping(value = "/search")
	@NoNeedAccessAuthorization
	@ResponseBody
	public R<List<User>> searchUserList(@RequestParam String username,@RequestParam(defaultValue = "",required = false) String type) {
		return R.success(userService.searchUserList(username,"ALL".equals(type.toUpperCase())?false:true));
	}

	/**
	 * 根据用户名模糊搜索所有启用的用户集合，含删除用户
	 *
	 * @param username 用户名
	 * @return List<User>
	 */
	@RequestMapping(value = "/searchHasDelete")
	@NoNeedAccessAuthorization
	@ResponseBody
	public R<List<User>> searchUserListHasDelete(@RequestParam String username) {
		return R.success(userService.searchUserListHas(username));
	}

	/**
	 * 根据用户名模糊搜索所有启用的用户集合（不包括自己）
	 *
	 * @param userId userId
	 * @return List<User>
	 */
	@RequestMapping(value = "/searchByUserId")
	@NoNeedAccessAuthorization
	@ResponseBody
	public R<List<User>> searchByUserId(@RequestParam Integer userId) {
		return R.success(userService.searchUserListById(userId));
	}
	/**
	 * 根据用户名模糊搜索所有启用的用户集合（不包括自己）
	 *
	 * @param username 用户名
	 * @return List<User>
	 */
	@RequestMapping(value = "/searchUserListForSelect")
	@NoNeedAccessAuthorization
	@ResponseBody
	public R<List<User>> searchUserListForSelect(@RequestParam String username) {
		return R.success(userService.searchUserListForSelect(username));
	}
	/**
	 * 根据用户名模糊搜索所有启用的用户集合（不包括自己）
	 *
	 * @param username 用户名
	 * @return List<User>
	 */
	@RequestMapping(value = "/searchUserListForSelectLimit")
	@NoNeedAccessAuthorization
	@ResponseBody
	public R<List<SelectDto>> searchUserListForSelectLimit(  String username) {
		PageHelper.startPage(1,100);
		List<User> list= userService.searchUserListForSelect(username);
		List<SelectDto> result = list.stream().map(sku -> {
			SelectDto map = new SelectDto(sku.getUserId()+"",sku.getUsername());
			return map;
		}).collect(Collectors.toList());
		return R.success(result);
	}
}
