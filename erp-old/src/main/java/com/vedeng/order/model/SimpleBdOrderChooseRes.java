package com.vedeng.order.model;

import com.vedeng.authorization.model.User;

import java.math.BigDecimal;
import java.util.List;

public class SimpleBdOrderChooseRes {
    private Integer saleorderId;
    private String saleorderNo;
    private Integer traderId;
    private String traderName;
    private String productName;
    private String skuNo;
    private Long addTime;
    private Long beginTime;
    private Long endTime;
    private Integer status;
    private String optUserName;
    private String traderContactName;
    private String traderContactMobile;
    private BigDecimal totalAmount;
    private BigDecimal realAmount;
    /**
     * 优惠券信息
     */
    private SaleorderCoupon couponInfo;
    /**
     * 对应销售人员列表
     */
    private List<User> saleUserList;

    private List<Goods> goodsList;

    public List<Goods> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<Goods> goodsList) {
        this.goodsList = goodsList;
    }

    public static class Goods{
        private String sku;
        private String goodsName;

        public String getSku() {
            return sku;
        }

        public void setSku(String sku) {
            this.sku = sku;
        }

        public String getGoodsName() {
            return goodsName;
        }

        public void setGoodsName(String goodsName) {
            this.goodsName = goodsName;
        }
    }
    public List<User> getSaleUserList() {
        return saleUserList;
    }

    public void setSaleUserList(List<User> saleUserList) {
        this.saleUserList = saleUserList;
    }

    public Long getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Long beginTime) {
        this.beginTime = beginTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public Integer getSaleorderId() {
        return saleorderId;
    }

    public void setSaleorderId(Integer saleorderId) {
        this.saleorderId = saleorderId;
    }

    public String getSaleorderNo() {
        return saleorderNo;
    }

    public void setSaleorderNo(String saleorderNo) {
        this.saleorderNo = saleorderNo;
    }

    public Integer getTraderId() {
        return traderId;
    }

    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }

    public String getTraderName() {
        return traderName;
    }

    public void setTraderName(String traderName) {
        this.traderName = traderName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSkuNo() {
        return skuNo;
    }

    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getOptUserName() {
        return optUserName;
    }

    public void setOptUserName(String optUserName) {
        this.optUserName = optUserName;
    }

    public String getTraderContactName() {
        return traderContactName;
    }

    public void setTraderContactName(String traderContactName) {
        this.traderContactName = traderContactName;
    }

    public String getTraderContactMobile() {
        return traderContactMobile;
    }

    public void setTraderContactMobile(String traderContactMobile) {
        this.traderContactMobile = traderContactMobile;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getRealAmount() {
        return realAmount;
    }

    public void setRealAmount(BigDecimal realAmount) {
        this.realAmount = realAmount;
    }

    public SaleorderCoupon getCouponInfo() {
        return couponInfo;
    }

    public void setCouponInfo(SaleorderCoupon couponInfo) {
        this.couponInfo = couponInfo;
    }
}
