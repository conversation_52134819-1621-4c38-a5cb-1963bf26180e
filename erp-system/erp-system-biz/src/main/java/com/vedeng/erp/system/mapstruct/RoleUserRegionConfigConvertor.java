package com.vedeng.erp.system.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.system.domain.entity.RoleUserRegionConfigEntity;
import com.vedeng.erp.system.dto.RoleUserRegionConfigDto;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * 人员与产线区域配置转换器
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface RoleUserRegionConfigConvertor extends BaseMapStruct<RoleUserRegionConfigEntity, RoleUserRegionConfigDto> {
} 