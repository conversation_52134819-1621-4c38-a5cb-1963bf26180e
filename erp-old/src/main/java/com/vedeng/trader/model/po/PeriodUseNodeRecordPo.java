package com.vedeng.trader.model.po;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 账期使用节点记录PO
 * <AUTHOR>
 */

@Data
public class PeriodUseNodeRecordPo {

    /**
     * 记录ID
     */
    private Long recordId;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 关联业务ID
     */
    private Long relatedId;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 处理状态
     */
    private Integer handlingStatus;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 修改时间
     */
    private Long modeTime;

    /**
     * 创建人
     */
    private Integer creator;


    /**
     * 更新人
     */
    private Integer updater;
}
