package com.vedeng.erp.saleorder.buzlogic.create.impl;

import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.saleorder.buzlogic.create.EditSaleOrderBuzLogic;
import com.vedeng.erp.saleorder.service.BaseSaleOrderService;
import com.vedeng.order.model.Saleorder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class ZxfEditSaleOrderBuzLogic extends EditSaleOrderBuzLogic {
    Logger logger= LoggerFactory.getLogger(ZxfEditSaleOrderBuzLogic.class);
    @Autowired
    private BaseSaleOrderService baseSaleOrderService;

    public ZxfEditSaleOrderBuzLogic(){
        List<String> sequence = new ArrayList<>();
        sequence.add("saveEditSaleOrderInfo");
//        sequence.add("syncCreateQuote");
        super.setSequence(sequence);
    }

    public ResultInfo syncCreateQuote(Saleorder saleorder){
        ResultInfo resultInfo = baseSaleOrderService.createOrderSyncCreateQuote(saleorder.getSaleorderId());
        return resultInfo;
    }

    public ResultInfo run(Saleorder saleorder){
        new ZxfEditSaleOrderBuzLogic();
        ResultInfo result = new ResultInfo(0,"操作成功");
        for (String methodName : getSequence()){
            switch (methodName) {
                case "saveEditSaleOrderInfo":
                    if(ErpConst.ZERO.equals(result.getCode())) {
                        result = super.saveEditSaleOrderInfo(saleorder);
                    }
                    break;
                case "syncCreateQuote":
                    if(ErpConst.ZERO.equals(result.getCode())) {
//                        result = this.syncCreateQuote(saleorder);
                        logger.info("同步创建销售单{},报价单反馈{},",saleorder.getSaleorderId(),result.getMessage());
                    }
                    break;
                default:
                    break;
            }
        }
        return result;
    }
}
