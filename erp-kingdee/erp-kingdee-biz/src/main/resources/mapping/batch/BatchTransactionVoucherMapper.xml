<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchTransactionVoucherMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchTransactionVoucherDto">
    <id column="TRANSACTION_VOUCHER_ID" jdbcType="BIGINT" property="transactionVoucherId" />
    <result column="BILL_NO" jdbcType="VARCHAR" property="billNo" />
    <result column="VOUCHER_NO" jdbcType="VARCHAR" property="voucherNo" />
    <result column="TRANSACTION_TYPE" jdbcType="INTEGER" property="transactionType" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
  </resultMap>
  <sql id="Base_Column_List">
    TRANSACTION_VOUCHER_ID, BILL_NO, VOUCHER_NO, TRANSACTION_TYPE, IS_DELETE, ADD_TIME, 
    CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, MOD_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_TRANSACTION_VOUCHER
    where TRANSACTION_VOUCHER_ID = #{transactionVoucherId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from T_TRANSACTION_VOUCHER
    where TRANSACTION_VOUCHER_ID = #{transactionVoucherId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchTransactionVoucherDto">
    insert into T_TRANSACTION_VOUCHER (TRANSACTION_VOUCHER_ID, BILL_NO, VOUCHER_NO, 
      TRANSACTION_TYPE, IS_DELETE, ADD_TIME, 
      CREATOR, CREATOR_NAME, UPDATER, 
      UPDATER_NAME, MOD_TIME)
    values (#{transactionVoucherId,jdbcType=BIGINT}, #{billNo,jdbcType=VARCHAR}, #{voucherNo,jdbcType=VARCHAR}, 
      #{transactionType,jdbcType=INTEGER}, #{isDelete,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updater,jdbcType=INTEGER}, 
      #{updaterName,jdbcType=VARCHAR}, #{modTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchTransactionVoucherDto">
    insert into T_TRANSACTION_VOUCHER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="transactionVoucherId != null">
        TRANSACTION_VOUCHER_ID,
      </if>
      <if test="billNo != null">
        BILL_NO,
      </if>
      <if test="voucherNo != null">
        VOUCHER_NO,
      </if>
      <if test="transactionType != null">
        TRANSACTION_TYPE,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="transactionVoucherId != null">
        #{transactionVoucherId,jdbcType=BIGINT},
      </if>
      <if test="billNo != null">
        #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="voucherNo != null">
        #{voucherNo,jdbcType=VARCHAR},
      </if>
      <if test="transactionType != null">
        #{transactionType,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchTransactionVoucherDto">
    update T_TRANSACTION_VOUCHER
    <set>
      <if test="billNo != null">
        BILL_NO = #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="voucherNo != null">
        VOUCHER_NO = #{voucherNo,jdbcType=VARCHAR},
      </if>
      <if test="transactionType != null">
        TRANSACTION_TYPE = #{transactionType,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where TRANSACTION_VOUCHER_ID = #{transactionVoucherId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchTransactionVoucherDto">
    update T_TRANSACTION_VOUCHER
    set BILL_NO = #{billNo,jdbcType=VARCHAR},
      VOUCHER_NO = #{voucherNo,jdbcType=VARCHAR},
      TRANSACTION_TYPE = #{transactionType,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP}
    where TRANSACTION_VOUCHER_ID = #{transactionVoucherId,jdbcType=BIGINT}
  </update>
</mapper>