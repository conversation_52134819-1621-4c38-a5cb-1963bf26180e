package com.vedeng.erp.saleorder.feign;

import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.onedataapi.api.terminal.TerminalInfoServiceApi;
import com.vedeng.onedataapi.api.terminal.req.TerminalListReqDto;
import com.vedeng.onedataapi.api.terminal.req.TerminalReqDto;
import com.vedeng.onedataapi.api.terminal.res.TerminalDataRes;
import com.vedeng.onedataapi.api.terminal.res.TerminalPageRes;
import com.vedeng.onedataapi.api.terminal.res.TerminalRes;
import feign.Headers;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.saleorder.feign
 * @Date 2023/9/6 10:06
 */
@FeignApi(serverName = "onedataapi")
public interface OneDataTerminalFeignApi extends TerminalInfoServiceApi {

    /**
     * 终端查询服务接口
     * @param terminalReqDto
     * @return
     */
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /terminal/detail")
    @Override
    RestfulResult<TerminalDataRes> getTerminalDetail(@RequestBody TerminalReqDto terminalReqDto);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /terminal/searchDetail")
    @Override
    RestfulResult<TerminalDataRes> getTerminalSearchDetail(@RequestBody TerminalReqDto terminalReqDto);

    /**
     * 终端360详情接口
     * @param terminalReqDto
     * @return
     */
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /terminalLib/detail")
    @Override
    RestfulResult<TerminalRes> getTerminalDetailInfo(@RequestBody TerminalReqDto terminalReqDto);

    /**
     * 终端列表接口
     * @param terminalListReqDto
     * @return
     */
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /terminal/list")
    @Override
    RestfulResult<TerminalPageRes> listTerminal(TerminalListReqDto terminalListReqDto);
}
