<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.trader.dao.YxgTraderAptitudeMapper">
    <resultMap id="BaseResultMap" type="com.newtask.model.YXGTraderAptitude">
        <id column="APTITUDE_ID" property="aptitudeId" jdbcType="INTEGER"/>
        <result column="TRADER_ID" property="traderId" jdbcType="INTEGER"/>
        <result column="STATUS" property="status" jdbcType="INTEGER"/>
    </resultMap>

    <select id="getAptitudeListPage" parameterType="Map" resultMap="BaseResultMap">
        SELECT * FROM YXG_TRADER_APTITUDE_STATUS
    </select>
</mapper>