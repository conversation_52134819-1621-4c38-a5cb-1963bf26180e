$(function () {
    $('.J-line-wrap').each(function () {
        var $lineWrap = $(this);
        var lineData = $lineWrap.data('json');
        var extra = 0;

        $.each(lineData, function (i, item) {
            var status = item.status || 0;
            var statusClass = ['', 't-line-done', 't-line-doing', 't-line-async',''][status];
            var jumpDom = item.jump ? 'data-jump="' + item.jump + '"' : '';
            var tipDom = item.tip ? '<div class="t-line-item-tip">' + item.tip + '</div>' : '';
            var lockDom = item.lock ? '<div class="t-line-lock"><i class="lock-icon"></i><div class="t-line-lock-txt">'+ item.lock +'</div></div>' : '';

            if (status == 4){
                extra = extra + 1;
                $lineWrap.append(
                    '<div class="t-line-item ' + '" '+ jumpDom +'>' +
                    '<div class="t-line-item-cnt">' +
                    '<div class="t-line-item-txt">' + item.label + '</div>' +
                    tipDom +
                    lockDom +
                    '</div>' +
                    '</div>'
                )
            }else {
                $lineWrap.append(
                   '<div class="t-line-item ' + statusClass + '" '+ jumpDom +'>' +
                   /*  '<div class="t-line-item-icon">' + (i+1-extra) + '</div>' +*/
                    '<div class="t-line-item-icon">' + '</div>' +
                    '<div class="t-line-item-cnt">' +
                    '<div class="t-line-item-txt">' + item.label + '</div>' +
                    tipDom +
                    lockDom +
                    '</div>' +
                    '</div>'
                )
            }
        })

        $('[data-jump]', $lineWrap).click(function () {
            var id = $(this).data('jump');
            console.log(id);
            var top = $(id).offset().top;

            $('html,body').animate({ scrollTop: top - 50 }, 300);
        })
    })
});