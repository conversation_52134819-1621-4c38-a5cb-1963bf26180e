
package com.vedeng.erp.finance.domain.dto;

import com.vedeng.common.core.base.BaseDto;
import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 承兑汇票记录表
 * <AUTHOR>
 */
@Getter
@Setter
public class AcceptanceBillDto extends BaseDto {
    /**
     * 主键
     */
    private Long acceptanceBilId;

    /**
     * 银行流水id
     */
    private Integer bankBillId;

    /**
     * 承兑银行 1.民生银行 2.交通银行
     */
    private Integer acceptanceBank;

    /**
     * 票据号码
     */
    private String billNumber;

    /**
     * 子票区间起始
     */
    private String subBillIntervalStart;

    /**
     * 子票区间截止
     */
    private String subBillIntervalEnd;

    /**
     * 票据类型
     */
    private String billType;

    /**
     * 出票日期
     */
    private Date issueDate;

    /**
     * 票面到期日
     */
    private Date maturityDate;

    /**
     * 票据金额
     */
    private BigDecimal billAmount;

    /**
     * 承兑日期
     */
    private Date acceptanceDate;

    /**
     * 交易合同号
     */
    private String transactionContractNo;

    /**
     * 出票人名称
     */
    private String drawerName;

    /**
     * 出票人账号
     */
    private String drawerAccount;

    /**
     * 出票人开户行名称
     */
    private String drawerBankName;

    /**
     * 出票人开户行行号
     */
    private String drawerBankCode;

    /**
     * 收票人名称
     */
    private String payeeName;

    /**
     * 收票人账号
     */
    private String payeeAccount;

    /**
     * 收票人开户行名称
     */
    private String payeeBankName;

    /**
     * 收票人开户行行号
     */
    private String payeeBankCode;

    /**
     * 承兑人名称
     */
    private String acceptorName;

    /**
     * 承兑人账号
     */
    private String acceptorAccount;

    /**
     * 承兑人开户行名称
     */
    private String acceptorBankName;

    /**
     * 承兑人开户行行号
     */
    private String acceptorBankCode;

    /**
     * 状态 1.已出票 2.已承兑 3.已收票 4.已到期 5.已终止 6.已结清
     */
    private Integer billStatus;

    /**
     * 贴现状态 1.未贴现，2.贴现中，3.已贴现，4.贴现失败
     */
    private Integer discountStatus;

}