package com.vedeng.goods.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.goods.domain.entity.BusinessOrderCategoryEntity;
import com.vedeng.goods.dto.BusinessOrderCategoryDto;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * 业务单据与商品分类关联转换器
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface BusinessOrderCategoryConvertor extends BaseMapStruct<BusinessOrderCategoryEntity, BusinessOrderCategoryDto> {
} 