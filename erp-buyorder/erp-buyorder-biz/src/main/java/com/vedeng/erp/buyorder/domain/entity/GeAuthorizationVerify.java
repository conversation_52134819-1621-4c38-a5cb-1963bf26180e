package com.vedeng.erp.buyorder.domain.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * T_GE_AUTHORIZATION_VERIFY
 * <AUTHOR>
public class GeAuthorizationVerify implements Serializable {
    /**
     * 审核记录表id
     */
    private Integer authorizationVerifyId;

    /**
     * 授权书id
     */
    private Integer authorizationId;

    /**
     * 0 未通过，1 通过
     */
    private Integer stats;

    /**
     * 备注
     */
    private String content;

    /**
     * 申请人 T_USER_DETAIL REAL_NAME 中文名
     */
    private String creatorName;

    /**
     * 审批人id T_USER id
     */
    private Integer creator;

    /**
     * 添加时间
     */
    private Date addTime;

    private static final long serialVersionUID = 1L;

    public Integer getAuthorizationVerifyId() {
        return authorizationVerifyId;
    }

    public void setAuthorizationVerifyId(Integer authorizationVerifyId) {
        this.authorizationVerifyId = authorizationVerifyId;
    }

    public Integer getAuthorizationId() {
        return authorizationId;
    }

    public void setAuthorizationId(Integer authorizationId) {
        this.authorizationId = authorizationId;
    }

    public Integer getStats() {
        return stats;
    }

    public void setStats(Integer stats) {
        this.stats = stats;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }
}