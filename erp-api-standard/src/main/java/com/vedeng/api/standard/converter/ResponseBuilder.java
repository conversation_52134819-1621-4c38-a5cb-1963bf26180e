package com.vedeng.api.standard.converter;

import com.vedeng.api.standard.internal.InternalCallResult;

import java.util.Map;

/**
 * 响应构建器函数式接口
 * 用于自定义响应对象的构建逻辑
 * 
 * @param <T> 响应对象类型
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-27
 */
@FunctionalInterface
public interface ResponseBuilder<T> {
    
    /**
     * 构建响应对象
     * 
     * @param callResult 内部调用结果
     * @param dataMap 数据Map（从callResult.getData()提取，如果不是Map则为null）
     * @return 构建的响应对象
     */
    T build(InternalCallResult callResult, Map<String, Object> dataMap);
}
