package com.vedeng.common.listener;

import com.alibaba.fastjson.JSON;
import com.google.common.eventbus.Subscribe;
import com.vedeng.common.core.config.IObserver;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.listenerEvent.BuyorderAttachmentEvent;
import com.vedeng.order.model.dto.PurchaseContractDto;
import com.vedeng.order.service.BuyorderService;
import com.vedeng.system.model.Attachment;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购单相关业务监听器
 * @date 2023/10/20 16:56
 */
@Component
@Slf4j
public class BuyorderListener implements IObserver {

    @Autowired
    private BuyorderService buyorderService;

    /**
     * 处理采购单附件上传事件
     *
     * @param buyorderAttachmentEvent BuyorderAttachmentEvent
     */
    @Subscribe
    @Transactional(rollbackFor = Throwable.class)
    public void handleBuyorderAttachmentUpload(BuyorderAttachmentEvent buyorderAttachmentEvent) {
        log.info("采购单附件上传事件处理 -> {}", JSON.toJSONString(buyorderAttachmentEvent));

        try {
            // 直接调用采购单服务的新方法保存附件
            buyorderService.saveBuyorderAttachmentForAadmin(
                buyorderAttachmentEvent.getBuyorderId(),
                buyorderAttachmentEvent.getBuyorderNo(),
                buyorderAttachmentEvent.getFileName(),
                buyorderAttachmentEvent.getFilePath(),
                buyorderAttachmentEvent.getDomain(),
                buyorderAttachmentEvent.getAttachmentType(),
                buyorderAttachmentEvent.getAttachmentFunction(),
                buyorderAttachmentEvent.getCreator(),
                buyorderAttachmentEvent.getOssResourceId()
            );

            log.info("采购单附件上传事件处理完成，采购单ID: {}", buyorderAttachmentEvent.getBuyorderId());
        } catch (Exception e) {
            log.error("采购单附件上传事件处理异常", e);
            throw new ServiceException("采购单附件上传事件处理异常: " + e.getMessage());
        }
    }
}
