$(function () {
})

function getQueryVariable(variable) {
    var query = window.location.search.substring(1);
    var vars = query.split("&");
    for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split("=");
        if (pair[0] == variable) {
            return pair[1];
        }
    }
    return (false);
}

function replaceParamVal(url, key, val) {
    var params = url.split('?')[1].split('&');

    $.each(params, function (i, obj) {
        var objItem = obj.split('=');
        if(objItem[0] == key){
            params[i] = key + '=' + val;
        }
    });

    return url.split('?')[0] + "?" + params.join('&');
}

function searchTyc() {
    checkLogin();
    var bType = 0;
    var traderName = $("input[type='radio']:checked").val();
    if (typeof (traderName) == "undefined") {
        /*traderName = $("#customerTraderName").val();
        bType = 1;*/
        layer.alert("请选择客户！");
        return false;
    }
    if (window.parent.setBusinessLeadsTraderNameByEye) {
        window.parent.setBusinessLeadsTraderNameByEye(traderName)
        return;
    }
    $.ajax({
        async: false,
        url: page_url + '/trader/customer/eyeCheckInfo.do',
        data: {
            "traderName": traderName
        },
        type: "POST",
        dataType: "json",
        success: function (data) {
            if (data.code == -1) {
                layer.alert("接口错误！");
            } else if (data.code == 2) {
                layer.alert("没有查询到" + data.data + "的信息！");
            } else if (data.code == 3) {
                layer.alert("余额不足！");
            } else {
                var index = 0;
                var flag = -1;
                var url = "";
                /*if(bType==1){
                    url = $(window.frameElement).attr('src');
                    alert(url);
                    $(window.frameElement).attr('src',url+"&traderName="+data.data);
                }else{
                    url = $(window.parent.frameElement).attr('src');
                    alert(url);
                    $(window.parent.frameElement).attr('src',url);
                }*/
                url = $(window.parent.frameElement).attr('src');
                var param = "";
                if (url.indexOf("?") == -1) {
                    param = "?traderName=" + traderName;
                    url = url + param;
                } else if (getQueryVariable('traderName')) {
                    url = replaceParamVal(url, 'traderName', traderName);
                }else{
                    url = url + "&traderName=" + traderName;
                }
                $(window.parent.frameElement).attr('src', url);

                /*if(url.indexOf("traderId") > 0){
                    index = url.indexOf("&");
                    flag = 1;
                }else{
                    index = url.indexOf("?");
                }
                alert(url);
                if(index>0){
                    url  = url.substring(0,index);
                    if(flag==1){

                    }else{

                    }
                }else{
                    $(window.frameElement).attr('src',url+"?traderName="+data.data);
                }*/
            }
        },
        error: function (data) {
            if (data.status == 1001) {
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    });
}

function hideDiv() {
    $("#eyeDiv").hide();
}

function getAllCustomers() {
    checkLogin();
    location.href = page_url + '/order/miannian/getAllCustomers.do?' + $("#search").serialize();
}