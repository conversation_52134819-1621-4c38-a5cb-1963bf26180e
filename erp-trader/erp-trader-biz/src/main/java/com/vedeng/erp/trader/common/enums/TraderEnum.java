package com.vedeng.erp.trader.common.enums;

/**
 * <AUTHOR>
 * @description 客户相关枚举类
 * @date 2023/8/8 18:45
 */
public enum TraderEnum {

    /**
     * 科研医疗
     */
    SCIENTIFIC_MEDICAL(426, "科研医疗"),

    /**
     * 临床医疗
     */
    CLINICAL_MEDICAL(427, "临床医疗"),

    /**
     * 分销
     */
    DISTRIBUTION(465, "分销"),

    /**
     * 终端
     */
    TERMINAL(466, "终端")
    ;

    private Integer sysOptionDefinitionId;

    private String title;

    public Integer getSysOptionDefinitionId() {
        return sysOptionDefinitionId;
    }

    public void setSysOptionDefinitionId(Integer sysOptionDefinitionId) {
        this.sysOptionDefinitionId = sysOptionDefinitionId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    TraderEnum(Integer sysOptionDefinitionId, String title) {
        this.sysOptionDefinitionId = sysOptionDefinitionId;
        this.title = title;
    }

    public static String getDefinitionNameById(Integer sysOptionDefinitionId) {
        for (TraderEnum v : values()) {
            if (v.getSysOptionDefinitionId().equals(sysOptionDefinitionId)) {
                return v.getTitle();
            }
        }
        return "";
    }
}
