<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>销售订单审核待处理</title> 
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="FokpvM0r71Q" datasource="erp-reportdatasource" fixednumber="2" append_column_url="" append_row_url="" empty_show="" firstcol="numbers">
  <div id="appendHead"></div> 
  <div class="layui-fluid"> 
   <div class="layui-tab"> 
    <ul class="layui-tab-title" id="tab"> 
    </ul> 
   </div> 
   <form class="layui-form" id="search">
    <div class=" layui-inline "><label class="layui-form-label">送达时间</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="daterange" name="ADD_TIME" placeholder="" style="" alias="FROM_UNIXTIME( L.ADD_TIME / 1000 )" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">销售部门</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="20" name="ORG_ID" placeholder="" style="" alias="" jdbctype="" data="SELECT 	A.ORG_ID AS 'K', 	A.ORG_NAME AS 'V'  FROM 	T_ORGANIZATION A 	LEFT JOIN T_POSITION B ON A.ORG_ID = B.ORG_ID  WHERE 	B.TYPE = 310  	AND A.COMPANY_ID = 1  GROUP BY 	A.ORG_ID" datatype="" oper=""></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">归属销售</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="20" name="USERNAME" placeholder="" style="" alias="" jdbctype="" data="SELECT 	U.USERNAME AS 'K', 	USERNAME AS 'V'  FROM 	T_USER U 	JOIN T_R_USER_POSIT UP ON U.USER_ID = UP.USER_ID  	AND U.IS_DISABLED = 0 AND U.COMPANY_ID = 1 	JOIN T_POSITION P ON UP.POSITION_ID = P.POSITION_ID  WHERE 	P.TYPE = 310" datatype="KVSQLCACHE" oper=""></object> 
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">客户名称</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="TRADER_NAME" placeholder="" style="" alias="T" jdbctype="VARCHAR" data="" datatype="" oper="LIKE"></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">订单号</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="SALEORDER_NO" placeholder="" style="" alias="S" jdbctype="" data="" datatype="" oper="LIKE"></object> 
     </div>
    </div>
   </form> 
   <hr class="layui-border-blue"> 
   <div class="btn-group   bd-highlight" id="tableButton"> 
   </div> 
   <table id="table" class="layui-table" style=" width:100%"> 
    <thead> 
     <tr id="column">
      <th type="rowbutton" id="rowbutton"></th>
      <th item_name="SALEORDER_NO" name="SALEORDER_NO" body="td-link-color" order="0" url="/order/saleorder/view.do?saleorderId=${SALEORDER_ID}" opentype="PARENT" windowname="" datatype="" data="" style="" head="18">订单号</th>
      <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-link-color" order="0" url="/trader/customer/baseinfo.do?traderId=${TRADER_ID}" opentype="PARENT" windowname="" datatype="" data="" style="" head="18">客户名称</th>
      <th item_name="CUSTOMER_NATURE" name="CUSTOMER_NATURE" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">客户性质</th>
      <th item_name="USERNAME" name="USERNAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">归属销售</th>
      <th item_name="ORG_NAME" name="ORG_NAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">销售部门</th>
      <th item_name="ADD_TIME" name="ADD_TIME" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">送达时间</th>
      <th item_name="OPERATION" name="OPERATION" body="td-link-color" order="0" url="/order/saleorder/view.do?saleorderId=${SALEORDER_ID}" opentype="PARENT" windowname="" datatype="" data="" style="" head="18">操作</th>
     </tr> 
    </thead> 
    <tbody> 
     <tr> 
      <td colspan="100"> express: <pre id="express" class="layui-code" orderby="ORDER BY 	L.ADD_TIME DESC" groupby="">
          SELECT
	S.SALEORDER_ID,
	S.SALEORDER_NO,
	T.TRADER_ID,
	T.TRADER_NAME,
CASE
		S.CUSTOMER_NATURE
		WHEN 465 THEN
		'分销'
		WHEN 466 THEN
		'终端'
	END CUSTOMER_NATURE,
	U.USERNAME,
	U.USER_ID,
	O.ORG_ID,
	O.ORG_NAME,
	FROM_UNIXTIME( L.ADD_TIME / 1000 ) ADD_TIME,
CASE S.ORDER_TYPE WHEN 5 THEN '/order/hc/hcOrderDetailsPage.do?saleorderId=' ELSE '/order/saleorder/view.do?saleorderId=' END URI,
	'去审核' OPERATION
FROM
	T_TODO_LIST L
	JOIN T_SALEORDER S ON L.BUZ_ID = S.SALEORDER_ID
	AND L.`STATUS` = 0
	AND L.BUZ_TYPE = 2 and S.company_id=1
	JOIN T_TRADER T ON S.TRADER_ID = T.TRADER_ID
	JOIN T_R_TRADER_J_USER TU ON T.TRADER_ID = TU.TRADER_ID and TU.TRADER_TYPE=1
	JOIN T_USER U ON TU.USER_ID = U.USER_ID
	JOIN T_R_USER_POSIT UP ON U.USER_ID = UP.USER_ID
	JOIN T_POSITION P ON UP.POSITION_ID = P.POSITION_ID
	JOIN T_ORGANIZATION O ON P.ORG_ID = O.ORG_ID
 WHERE  S.VALID_STATUS!=1 AND S.`STATUS`!=3 AND S.`STATUS`!=4

      </pre> count: <pre id="count" class="layui-code"></pre></td>
     </tr> 
    </tbody> 
   </table> 
  </div> 
  <div id="appendFoot"></div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
 </body>
</html>