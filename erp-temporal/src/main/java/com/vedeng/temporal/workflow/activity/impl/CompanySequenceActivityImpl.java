package com.vedeng.temporal.workflow.activity.impl;

import com.vedeng.temporal.exception.ExceptionHandler;
import com.vedeng.temporal.workflow.activity.CompanySequenceActivity;
import com.vedeng.temporal.service.FlowNodeBasedCompanyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

/**
 * 公司执行顺序活动实现类
 * 基于T_FLOW_NODE表的实现
 * 提供动态的公司执行顺序查询和流程节点ID获取功能
 *
 * <AUTHOR>
 * @version 4.1
 * @since 2024-12-20
 */
@Component
@Slf4j
public class CompanySequenceActivityImpl implements CompanySequenceActivity {

    @Autowired
    private FlowNodeBasedCompanyService flowNodeBasedCompanyService;

    @Autowired
    private ExceptionHandler exceptionHandler;

    @Override
    public List<String> getCompanySequenceByFlowOrder(Long flowOrderId) {
        return executeWithExceptionHandling(
            "getCompanySequenceByFlowOrder",
            String.valueOf(flowOrderId),
            null,
            () -> {
                validateFlowOrderId(flowOrderId);
                log.info("获取公司执行顺序: flowOrderId={}", flowOrderId);
                return flowNodeBasedCompanyService.getCompanySequence(flowOrderId);
            }
        );
    }

    @Override
    public Long getFlowNodeId(String businessId, String companyCode) {
        return executeWithExceptionHandling(
            "getFlowNodeId",
            businessId,
            companyCode,
            () -> {
                log.info("获取流程节点ID: businessId={}, companyCode={}", businessId, companyCode);
                return flowNodeBasedCompanyService.getFlowNodeId(businessId, companyCode);
            }
        );
    }

    @Override
    public Map<String, Long> getFlowNodeIds(String businessId, List<String> companyCodes) {
        return executeWithExceptionHandling(
            "getFlowNodeIds",
            businessId,
            null,
            () -> {
                validateBusinessId(businessId);
                if (companyCodes == null || companyCodes.isEmpty()) {
                    log.info("公司代码列表为空，返回空映射: businessId={}", businessId);
                    return new HashMap<>();
                }

                log.info("批量获取流程节点ID: businessId={}, 公司数量={}", businessId, companyCodes.size());
                
                Map<String, Long> result = new HashMap<>();
                for (String companyCode : companyCodes) {
                    if (companyCode != null && !companyCode.trim().isEmpty()) {
                        try {
                            Long flowNodeId = flowNodeBasedCompanyService.getFlowNodeId(businessId, companyCode);
                            result.put(companyCode, flowNodeId);
                        } catch (Exception e) {
                            log.warn("获取单个公司流程节点ID失败: companyCode={}, 错误={}", companyCode, e.getMessage());
                            result.put(companyCode, null);
                        }
                    } else {
                        result.put(companyCode, null);
                    }
                }
                
                return result;
            }
        );
    }

    /**
     * 统一的异常处理执行模板
     */
    private <T> T executeWithExceptionHandling(String operationName, String businessId, 
                                             String companyCode, Supplier<T> supplier) {
        try {
            return supplier.get();
        } catch (Exception e) {
            exceptionHandler.handleAndThrowActivityException(e, operationName, businessId, companyCode);
            return null; // 永远不会执行到这里
        }
    }

    /**
     * 校验流程订单ID
     */
    private void validateFlowOrderId(Long flowOrderId) {
        if (flowOrderId == null) {
            throw new IllegalArgumentException("flowOrderId 不能为空");
        }
    }

    /**
     * 校验业务ID
     */
    private void validateBusinessId(String businessId) {
        if (businessId == null || businessId.trim().isEmpty()) {
            throw new IllegalArgumentException("businessId 不能为空");
        }
    }
}
