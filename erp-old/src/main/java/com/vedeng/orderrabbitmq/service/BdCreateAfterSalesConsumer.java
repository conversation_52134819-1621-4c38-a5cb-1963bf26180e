package com.vedeng.orderrabbitmq.service;

import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.vedeng.aftersales.component.AfterSalesOrderService;
import com.vedeng.aftersales.component.AfterSalesOrderServiceFactory;
import com.vedeng.aftersales.component.dto.AfterSaleOrderAddDto;
import com.vedeng.common.trace.mq.AbstractMessageListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * @Author: daniel
 * @Date: 2021/6/21 17 37
 * @Description:
 */
@Component
@Deprecated
public class BdCreateAfterSalesConsumer extends AbstractMessageListener {

    public static final Logger LOGGER = LoggerFactory.getLogger(AfterSaleorderConsumer.class);

    @Autowired
    private AfterSalesOrderServiceFactory afterSalesOfSaleOrderFactory;

    @Override
    public void doBusiness(Message message, Channel channel) throws Exception {


        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);

        LOGGER.info("前台新增售后申请：{}",messageBody);

        try {
            AfterSaleOrderAddDto afterSaleOrderAddDto = JSONObject.parseObject(messageBody,AfterSaleOrderAddDto.class);

            if(afterSaleOrderAddDto == null){
                throw new RuntimeException("前台新增售后申请为空");
            }

            AfterSalesOrderService afterSalesOrderService = afterSalesOfSaleOrderFactory.createAfterSalesInstance(afterSaleOrderAddDto.getAfterSalesType());

            afterSalesOrderService.createAfterSalesOrder(afterSaleOrderAddDto);

            channel.basicAck(message.getMessageProperties().getDeliveryTag(),false);

        } catch (Exception e) {

            LOGGER.error("AfterSaleorderConsumer 前台提交售后申请 error:",e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(),false,false);
        }



    }

}
