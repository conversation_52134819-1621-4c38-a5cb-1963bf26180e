package com.vedeng.todolist.model;

import java.io.Serializable;

import com.vedeng.todolist.constant.TodoListBuzSceneEnum;
import lombok.Data;

/**
 * T_TODO_LIST
 * <AUTHOR>
@Data
public class TodoList implements Serializable {

    private Integer id;

    /**
     * 待办事项业务类型
     */
    private Integer buzType;

    /**
     * 业务ID
     */
    private Integer buzId;

    /**
     * 业务冗余字段
     */
    private String buzExtra;

    /**
     * 业务相关属性
     */
    private String buzProperty;

    /**
     * 待办事项业务调整链接
     */
    private String buzRedirect;

    /**
     * 审核状态，0审核不通过，1审核通过
     */
    private Integer checkStatus;

    /**
     * 备注
     */
    private String comments;

    /**
     * 待办事项完成状态，0未完成，1已完成，默认0
     */
    private Integer status;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 更新人
     */
    private Integer updater;

    /**
     * 是否删除，0未删除，1已删除
     */
    private Integer delete;

    private Integer assignmentManagerId;

    private Integer assignmentAssistantId;

    private Integer managerOrgId;

    private Integer assistantOrgId;

    private Long goodsLevelNo;

    private Long processTime;

    private static final long serialVersionUID = 1L;

    public TodoList() {
    }

    public TodoList(TodoListBuzSceneEnum buzScene, Integer buzId, String buzExtra, String buzProperty, String comments, Long addTime,
                    Integer creator) {
        this.buzType = buzScene.getBuzSceneId();
        this.buzId = buzId;
        this.buzExtra = buzExtra;
        this.buzRedirect = buzScene.getBuzRedirect();
        this.buzProperty = buzProperty;
        this.comments = comments;
        this.addTime = addTime;
        this.creator = creator;
    }
}