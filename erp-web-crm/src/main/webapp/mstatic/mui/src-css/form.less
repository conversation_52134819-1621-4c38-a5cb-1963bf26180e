/* form */
.form-wrap {
    position: relative;
}

.form-fixed-bottom {
    width: 100%;
    max-width: 768px;
    min-width: 320px;
    background: #fff;

    position: fixed;
    bottom: 0;
    padding-bottom: env(safe-area-inset-bottom);
    left: 50%;
    transform: translateX(-50%);
    z-index: 99;
    border-top: solid 1px #EBEFF2;

    .submit-btn {
        height: 53px;

        &.padding {
            padding: 10px;
        }
    }


    &.z-index-top {
        z-index: 9999;
    }
}


.form-section {
    padding: 0 10px;
    margin-bottom: 10px;

    .form-section-title {
        position: relative;
        height: 41px;
        padding: 10px 5px;
        font-size: 14px;
        font-weight: 700;

        .section-title-btn {
            height: 100%;
            color: #09f;
            display: flex;
            align-items: center;
            position: absolute;
            right: 0;
            top: 0px;

            > i {
                font-size: 16px;
                margin-right: 5px;
            }
            > span {
                font-size: 12px;
                font-weight: 400;
            }
        }
    }
}

.form-card {
    background: #fff;
    border-radius: 5px;

    &.mb10 {
        margin-bottom: 10px;
    }
}


// 表单item
.form-item {
    display: flex;
    border-bottom: solid 1px #F5F7FA;

    &:last-child {
        border: none;
    }

    .form-label {
        width: 100px;
        flex-shrink: 0;
        color: #000;
        line-height: 18px;
        white-space: nowrap;
        padding: 10px;
        padding-right: 0px;
        // padding-right: 5px;
        // margin-right: 10px;

        &.middle {
            display: flex;
            align-items: center;
        }

        .must {
            color: #e64545;
        }
    }

    .form-fields {
        position: relative;
        flex: 1;
        min-width: 0;
        word-break: break-all;
        padding: 10px;
        // padding: 10px 0 10px 10px;
        // margin-left: 10px;

        &.no-padding {
            padding: 0;
        }

        .vd-ui-input-error {
            color: #E64545;
            margin-top: 5px;
            font-size: 0;
            white-space: nowrap;

            .vd-ui-input-error--icon {
                font-size: 16px;
                margin-right: 5px;
                line-height: 1;
                vertical-align: -2px;
            }

            .vd-ui-input-error--errmsg {
                font-size: 14px;
                margin: 0px;
                display: inline-block;
            }
        }
    }

    &.vertical {
        display: block;

        .form-label {
            width: 100%;
            padding-bottom: 5px;
        }
        
        .form-fields {
            width: 100%;
            margin: 0;
        }
    }
}


// 提示信息
.form-tip {
    display: flex;
    font-size: 12px;

    i {
        height: 16px;
        font-size: 16px;
        margin-right: 5px;
        padding-top: 2px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    
    &.success {
        color: #13BF13;
    }
    &.warn {
        color: #F60;
    }
    &.error {
        color: #E64545;
    }
    &.info {
        color: #09f;
    }

    // 业务逻辑相关
    &.map, &.tel {
        color: #09F;
    }

    &.style2 {
        padding: 10px;

        &.success {
            color: #000;
            background: #c5ffc5;
            i {
                color: #13BF13;
            }
        }
        &.warn {
            color: #000;
            background: #ffbd91;
            i {
                color: #F60;
            }
        }
        &.error {
            color: #000;
            background: #FCE9E9;
            i {
                color: #E64545;
            }
        }
        &.info {
            color: #000;
            background: #E0F3FF;
            i {
                color: #09F;
            }
        }
    }
    
}




/* 表单逻辑相关 */
// placeholder
.form-placeholder {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;

    > span {
        font-size: 12px;
        color: #ccc;
    }

    > i {
        width: 36px;
        height: 18px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 16px;
        color: #666;
    }
}
