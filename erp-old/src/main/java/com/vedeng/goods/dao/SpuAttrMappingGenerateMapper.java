package com.vedeng.goods.dao;

import com.vedeng.goods.model.SpuAttrMappingGenerate;
import com.vedeng.goods.model.SpuAttrMappingGenerateExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SpuAttrMappingGenerateMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int countByExample(SpuAttrMappingGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int deleteByExample(SpuAttrMappingGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int deleteByPrimaryKey(Integer spuAttrId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int insert(SpuAttrMappingGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int insertSelective(SpuAttrMappingGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    List<SpuAttrMappingGenerate> selectByExample(SpuAttrMappingGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    SpuAttrMappingGenerate selectByPrimaryKey(Integer spuAttrId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByExampleSelective(@Param("record") SpuAttrMappingGenerate record, @Param("example") SpuAttrMappingGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByExample(@Param("record") SpuAttrMappingGenerate record, @Param("example") SpuAttrMappingGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByPrimaryKeySelective(SpuAttrMappingGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByPrimaryKey(SpuAttrMappingGenerate record);
}