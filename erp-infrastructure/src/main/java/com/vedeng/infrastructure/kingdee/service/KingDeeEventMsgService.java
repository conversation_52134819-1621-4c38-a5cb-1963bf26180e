package com.vedeng.infrastructure.kingdee.service;

import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeEventMsgDto;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶事件消息接口
 * @date 2022/8/27 12:40
 */
public interface KingDeeEventMsgService {


    /**
     * 生成事件消息（未消费）
     *
     * @param kingDeeEventMsgDto kingDeeEventMsgDto
     */
    void create(KingDeeEventMsgDto kingDeeEventMsgDto);

    /**
     * 消费消息
     *
     * @param kingDeeEventMsgDto kingDeeEventMsgDto
     */
    void msgConsume(KingDeeEventMsgDto kingDeeEventMsgDto);

    /**
     * 取消消费
     * @param kingDeeEventMsgDto
     */
    void msgNoHandleByLtId(KingDeeEventMsgDto kingDeeEventMsgDto);

    /**
     * 消息变成重试状态
     *
     * @param kingDeeEventMsgDto kingDeeEventMsgDto
     */
    void msgRetry(KingDeeEventMsgDto kingDeeEventMsgDto);

    /**
     * 重试次数+1
     * @param kingDeeEventMsgDto
     */
    void msgRetryNumAdd(KingDeeEventMsgDto kingDeeEventMsgDto);


    /**
     * 发送通知
     * @param kingDeeEventMsgDto
     */
    void sendMsg(KingDeeEventMsgDto kingDeeEventMsgDto);

    /**
     * 获取未消费消息
     *
     * @return List<KingDeeEventMsgDto>
     */
    List<KingDeeEventMsgDto> getRetryMsg(Integer id);


    List<KingDeeEventMsgDto> getRetryMsg(List<String> eventTypeList,Integer retryNum);

    /**
     * 获取重试超过多少次的消息
     *
     * @return List<KingDeeEventMsgDto>
     */
    List<KingDeeEventMsgDto> getNeedSendMsg();

    /**
     * 将消息变成无需处理
     *
     * @param kingDeeEventMsgDto kingDeeEventMsgDto
     */
    void msgNoHandle(KingDeeEventMsgDto kingDeeEventMsgDto);


    List<KingDeeEventMsgDto> getNoConsumeMsgById(String businessId, String eventType);

    List<KingDeeEventMsgDto> getNoConsumeMsgLtId(String businessId, String eventType,Integer msgId);
}
