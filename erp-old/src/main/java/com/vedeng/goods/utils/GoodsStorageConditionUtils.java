package com.vedeng.goods.utils;

import com.vedeng.goods.enums.GoodsStorageConditionOthersEnum;
import com.vedeng.goods.enums.GoodsStorageConditionTemperatureEnum;
import com.vedeng.goods.manager.validator.model.GoodsValidObject;
import com.vedeng.goods.model.CoreSkuGenerate;
import com.vedeng.goods.model.CoreSpuGenerate;
import com.vedeng.goods.model.vo.GoodsStorageConditionVo;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * Utility class for validating the storage conditions of goods.
 *
 * <AUTHOR> [<EMAIL>]
 */
public class GoodsStorageConditionUtils {

    private static final float MIN_TEMPERATURE_VALUE = -100f;
    private static final float MAX_TEMPERATURE_VALUE = 100f;

    private static final float MIN_HUMIDITY_VALUE = 0f;
    private static final float MAX_HUMIDITY_VALUE = 100f;

    public static final String NON_OTHERS = "";

    private static final GoodsStorageConditionVo DEFAULT_STORAGE_CONDITION = new GoodsStorageConditionVo();

    static {
        DEFAULT_STORAGE_CONDITION.setStorageConditionTemperature(GoodsStorageConditionTemperatureEnum.NORMAL.getCode());
        DEFAULT_STORAGE_CONDITION.setStorageConditionHumidityLowerValue(40f);
        DEFAULT_STORAGE_CONDITION.setStorageConditionHumidityUpperValue(80f);
    }


    public static boolean validateTemperatureRange(Float lowerValue, Float upperValue) {
        if (lowerValue == null || upperValue == null) {
            return false;
        }

        return doCompare(lowerValue, upperValue, MIN_TEMPERATURE_VALUE, MAX_TEMPERATURE_VALUE);
    }

    public static boolean validateHumidityRange(Float lowerValue, Float upperValue) {
        if (lowerValue == null || upperValue == null) {
            return false;
        }

        return doCompare(lowerValue, upperValue, MIN_HUMIDITY_VALUE, MAX_HUMIDITY_VALUE);
    }


    public static boolean validateOthersConditionIfPresent(Integer... others) {
        return GoodsStorageConditionOthersEnum.containAllInputs(others);
    }


    /**
     * Create a GoodsStorageConditionVo instance.
     *
     * @param spuDo
     * @return
     */
    public static GoodsStorageConditionVo createGoodsStorageConditionVo(CoreSpuGenerate spuDo) {
        GoodsStorageConditionVo goodsStorageConditionVo = new GoodsStorageConditionVo();
        //set 商品存储条件
        if (spuDo.getStorageConditionTemperature() != null && spuDo.getStorageConditionTemperature() != 0) {
            goodsStorageConditionVo.setStorageConditionTemperature(spuDo.getStorageConditionTemperature());
        }
        if (spuDo.getStorageConditionTemperatureLowerValue() != null) {
            goodsStorageConditionVo.setStorageConditionTemperatureLowerValue(spuDo.getStorageConditionTemperatureLowerValue());
        }
        if (spuDo.getStorageConditionTemperatureUpperValue() != null) {
            goodsStorageConditionVo.setStorageConditionTemperatureUpperValue(spuDo.getStorageConditionTemperatureUpperValue());
        }
        if (spuDo.getStorageConditionHumidityLowerValue() != null) {
            goodsStorageConditionVo.setStorageConditionHumidityLowerValue(spuDo.getStorageConditionHumidityLowerValue());
        }
        if (spuDo.getStorageConditionHumidityUpperValue() != null) {
            goodsStorageConditionVo.setStorageConditionHumidityUpperValue(spuDo.getStorageConditionHumidityUpperValue());
        }
        if (StringUtils.isNotEmpty(spuDo.getStorageConditionOthers())) {
            Integer[] others = GoodsStorageConditionOthersEnum.covert2OthersCodes(spuDo.getStorageConditionOthers());
            goodsStorageConditionVo.setStorageConditionOthersArray(others);
        }

        return goodsStorageConditionVo;
    }


    /**
     * Create a GoodsStorageConditionVo instance.
     *
     * @param skuDo
     * @return
     */
    public static GoodsStorageConditionVo createGoodsStorageConditionVo(CoreSkuGenerate skuDo) {
        GoodsStorageConditionVo goodsStorageConditionVo = new GoodsStorageConditionVo();
        //set 商品存储条件
        if (skuDo.getStorageConditionOne() != null && skuDo.getStorageConditionOne() != 0) {
            goodsStorageConditionVo.setStorageConditionTemperature(skuDo.getStorageConditionOne());
        }
        if (skuDo.getStorageConditionOneLowerValue() != null) {
            goodsStorageConditionVo.setStorageConditionTemperatureLowerValue(skuDo.getStorageConditionOneLowerValue());
        }
        if (skuDo.getStorageConditionOneUpperValue() != null) {
            goodsStorageConditionVo.setStorageConditionTemperatureUpperValue(skuDo.getStorageConditionOneUpperValue());
        }
        if (skuDo.getStorageConditionHumidityLowerValue() != null && (skuDo.getStorageConditionHumidityUpperValue() != null)) {
            goodsStorageConditionVo.setStorageConditionHumidityLowerValue(skuDo.getStorageConditionHumidityLowerValue());
            goodsStorageConditionVo.setStorageConditionHumidityUpperValue(skuDo.getStorageConditionHumidityUpperValue());
        }

        if (StringUtils.isNotEmpty(skuDo.getStorageConditionTwo())) {
            Integer[] others = GoodsStorageConditionOthersEnum.covert2OthersCodes(skuDo.getStorageConditionTwo());
            goodsStorageConditionVo.setStorageConditionOthersArray(others);
        }

        return goodsStorageConditionVo;
    }


    public static void populateStorageCondition(CoreSkuGenerate skuDo, GoodsStorageConditionVo goodsStorageConditionVo) {
        //set 商品存储条件
        skuDo.setStorageConditionOne(GoodsStorageConditionTemperatureEnum.getTemperatureCode(goodsStorageConditionVo.getStorageConditionTemperature()));
        if (GoodsStorageConditionTemperatureEnum.OTHERS.getCode().equals(goodsStorageConditionVo.getStorageConditionTemperature())) {
            skuDo.setStorageConditionOneLowerValue(goodsStorageConditionVo.getStorageConditionTemperatureLowerValue());
            skuDo.setStorageConditionOneUpperValue(goodsStorageConditionVo.getStorageConditionTemperatureUpperValue());
        }
        if (goodsStorageConditionVo.getStorageConditionHumidityLowerValue() != null && goodsStorageConditionVo.getStorageConditionHumidityUpperValue() != null) {
            skuDo.setStorageConditionHumidityLowerValue(goodsStorageConditionVo.getStorageConditionHumidityLowerValue());
            skuDo.setStorageConditionHumidityUpperValue(goodsStorageConditionVo.getStorageConditionHumidityUpperValue());
        } else {
            skuDo.setStorageConditionHumidityLowerValue(DEFAULT_STORAGE_CONDITION.getStorageConditionHumidityLowerValue());
            skuDo.setStorageConditionHumidityUpperValue(DEFAULT_STORAGE_CONDITION.getStorageConditionHumidityUpperValue());
        }

        if (ArrayUtils.isNotEmpty(goodsStorageConditionVo.getStorageConditionOthersArray())) {
            skuDo.setStorageConditionTwo(Strings.join(Arrays.stream(goodsStorageConditionVo.getStorageConditionOthersArray()).map(String::valueOf).collect(Collectors.toList()), ','));
        } else {
            skuDo.setStorageConditionTwo(NON_OTHERS);
        }
    }


    public static void populateStorageCondition(CoreSpuGenerate spuDo, GoodsStorageConditionVo goodsStorageConditionVo) {
        //set 商品存储条件
        spuDo.setStorageConditionTemperature(GoodsStorageConditionTemperatureEnum.getTemperatureCode(goodsStorageConditionVo.getStorageConditionTemperature()));
        if (GoodsStorageConditionTemperatureEnum.OTHERS.getCode().equals(goodsStorageConditionVo.getStorageConditionTemperature())) {
            spuDo.setStorageConditionTemperatureLowerValue(goodsStorageConditionVo.getStorageConditionTemperatureLowerValue());
            spuDo.setStorageConditionTemperatureUpperValue(goodsStorageConditionVo.getStorageConditionTemperatureUpperValue());
        }
        if (goodsStorageConditionVo.getStorageConditionHumidityLowerValue() != null && goodsStorageConditionVo.getStorageConditionHumidityUpperValue() != null) {
            spuDo.setStorageConditionHumidityLowerValue(goodsStorageConditionVo.getStorageConditionHumidityLowerValue());
            spuDo.setStorageConditionHumidityUpperValue(goodsStorageConditionVo.getStorageConditionHumidityUpperValue());
        }else {
            spuDo.setStorageConditionHumidityLowerValue(DEFAULT_STORAGE_CONDITION.getStorageConditionHumidityLowerValue());
            spuDo.setStorageConditionHumidityUpperValue(DEFAULT_STORAGE_CONDITION.getStorageConditionHumidityUpperValue());
        }

        if (ArrayUtils.isNotEmpty(goodsStorageConditionVo.getStorageConditionOthersArray())) {
            spuDo.setStorageConditionOthers(Strings.join(Arrays.stream(goodsStorageConditionVo.getStorageConditionOthersArray()).map(String::valueOf).collect(Collectors.toList()), ','));
        } else {
            spuDo.setStorageConditionOthers(NON_OTHERS);
        }
    }

    public static void populateStorageCondition(GoodsValidObject goodsValidObject, GoodsStorageConditionVo goodsStorageConditionVo) {
        //商品存储条件
        goodsValidObject.setStorageConditionTemperature(goodsStorageConditionVo.getStorageConditionTemperature());
        goodsValidObject.setStorageConditionTemperatureLowerValue(goodsStorageConditionVo.getStorageConditionTemperatureLowerValue());
        goodsValidObject.setStorageConditionTemperatureUpperValue(goodsStorageConditionVo.getStorageConditionTemperatureUpperValue());
        goodsValidObject.setStorageConditionHumidityLowerValue(goodsStorageConditionVo.getStorageConditionHumidityLowerValue());
        goodsValidObject.setStorageConditionHumidityUpperValue(goodsStorageConditionVo.getStorageConditionHumidityUpperValue());
        goodsValidObject.setStorageConditionOthersArray(goodsStorageConditionVo.getStorageConditionOthersArray());
    }

    //~================================================================================= Private methods


    private static boolean doCompare(Float lowerValue, Float upperValue, float minValue, float maxValue) {
        if (lowerValue < minValue || lowerValue > maxValue) {
            return false;
        }

        if (upperValue < minValue || upperValue > maxValue) {
            return false;
        }

        return Float.compare(lowerValue, upperValue) <= 0;
    }

}
