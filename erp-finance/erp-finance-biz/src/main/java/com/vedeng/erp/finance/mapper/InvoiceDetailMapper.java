package com.vedeng.erp.finance.mapper;
import java.util.Collection;

import com.vedeng.erp.finance.domain.entity.InvoiceDetailEntity;
import java.util.List;
import java.util.Map;

import com.vedeng.erp.finance.dto.InvoiceDetailDto;
import com.vedeng.erp.finance.dto.InvoiceDto;
import com.vedeng.erp.finance.dto.InvoiceGoodsDto;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2022/10/13 10:57
 **/
@Named("newInvoiceDetailMapper")
public interface InvoiceDetailMapper {
    /**
     * delete by primary key
     * @param invoiceDetailId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer invoiceDetailId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(InvoiceDetailEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(InvoiceDetailEntity record);

    /**
     * select by primary key
     * @param invoiceDetailId primary key
     * @return object by primary key
     */
    InvoiceDetailEntity selectByPrimaryKey(Integer invoiceDetailId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(InvoiceDetailEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(InvoiceDetailEntity record);

    int batchInsert(@Param("list") List<InvoiceDetailEntity> list);

    /**
     * 根据发票id查询明细信息
     * <AUTHOR>
     * @param invoiceId
     * @return
     */
    List<InvoiceDetailEntity> queryAllByInvoiceId(Integer invoiceId);

    /**
     * 根据发票id查询发票明细汇总信息
     * <AUTHOR>
     * @param invoiceIds
     * @return
     */
    List<InvoiceDetailDto> querySumDetailByInvoiceIds(@Param("invoiceIds")List<Integer> invoiceIds);

    /**
     * 根据发票号和明细商品id查询蓝字有效发票录入商品信息
     * <AUTHOR>
     * @param detailGoodsId
     * @param invoiceNo
     * @return
     */
    InvoiceDetailDto queryDetailByInvoiceInfo(@Param("detailGoodsId")Integer detailGoodsId,@Param("invoiceNo")String invoiceNo);

    /**
     * 所有有效的发票详情内容
     * @param invoiceIds
     * @return
     */
    List<InvoiceDetailEntity> getAllValidInvoiceDetailByInvoiceIds(@Param("invoiceIds")List<Integer> invoiceIds);

    /**
     * 根据发票id和订单商品id查询发票明细
     *
     * @param invoiceId     invoiceId
     * @param detailGoodsId detailGoodsId
     * @return InvoiceDetailDto
     */
    InvoiceDetailDto findByInvoiceIdAndDetailGoodsId(@Param("invoiceId") Integer invoiceId, @Param("detailGoodsId") Integer detailGoodsId);

    /**
     * 根据发票id集合查询发票明细
     * @param invoiceIdCollection
     * @return
     */
    List<InvoiceDetailEntity> findByInvoiceIdIn(@Param("invoiceIdCollection") Collection<Integer> invoiceIdCollection);


    List<InvoiceDetailDto> findRealInvoiceNum(@Param("salesOrderDetailIds")List<Integer> salesOrderDetailIds);

    List<Map<String, Object>> getInvoicedTaxNum(@Param("saleorderId") Integer saleorderId, @Param("saleorderNo") String saleorderNo);

    List<Map<String, Object>> getInvoicedDataOld(@Param("saleorderId") Integer saleorderId, @Param("saleorderNo") String saleorderNo);

    List<Map<String, Object>> getAppliedTaxNum(@Param("saleorderId") Integer saleorderId, @Param("saleorderNo") String saleorderNo);

    List<InvoiceGoodsDto> queryInvoiceGoods(@Param("invoiceNo") String invoiceNo);
    
    String queryInvoiceNo(@Param("saleorderNo") String saleorderNo);
}
