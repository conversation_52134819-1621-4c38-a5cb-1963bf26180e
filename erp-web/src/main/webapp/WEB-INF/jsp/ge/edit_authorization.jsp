<%--
  Created by IntelliJ IDEA.
  User: vedeng
  Date: 2022/2/14
  Time: 13:24
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html; charset=UTF-8" language="java" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="编辑授权书" scope="application"/>
<%@ include file="../common/common.jsp" %>
<script type="text/javascript" src="<%= basePath %>static/js/jquery/ajaxfileupload.js?rnd=${resourceVersionKey}"></script>

<style>
    .add-detail .border-blue {
        color: #3384ef;
    }

    .add-detail .border-blue:hover {
        color: #fff;
    }
</style>
<div class="addElement">
    <div class="add-main">
        <form action="<%= basePath %>/ge/authorization/saveAuthorization.do" method="post"id="myform">

            <input type="hidden" name="domain" value="${domain}" id="domain">
            <ul class="add-detail add-detail1">

                <li class="table-large">
                    <div class="infor_name" style="width: 150px">
                        <span style="font-red">*</span><label>产品类型</label>
                    </div>
                    <div class="f_left inputfloat mt3">
                        <div class="item-fields">
                            <select name="type" id="my-type" class="J-select" onchange="changeInfo(0)">
                                <option value="-1">选择</option>
                                <option value="1" <c:if test="${geAuthorization.type eq 1 }">selected</c:if> >CT</option>
                                <option value="2" <c:if test="${geAuthorization.type eq 2 }">selected</c:if> >超声</option>
                            </select>
                        </div>
                    </div>
                    <div class="font-red f_left inputfloat mt3" id="type-message" style="display: none;">请选择产品类型</div>
                </li>
                <li>
                    <div class="infor_name sex_name" style="width: 150px">
                        <span style="font-red">*</span><label>营业执照</label>
                    </div>
                    <div class="f_left insertli insertli1">
                        <ul>
                            <li style="margin-bottom:0;">
                                <c:choose>
                                    <c:when test="${!empty yy }">
                                        <c:forEach items="${yy }" var="bus" varStatus="st">
                                            <div class="mb8 c_1">
                                                <div class="pos_rel f_left ">
                                                    <input type="file" class="upload_file" name="lwfile"
                                                           id="file_1_${st.index}" style="display: none;"
                                                           onchange="uploadFile(this,1);changeInfo(1)"/>
                                                    <c:choose>
                                                        <c:when test="${st.index == 0 }">
                                                            <input type="text" class="input-middle" id="name_1"
                                                                   readonly="readonly"
                                                                   placeholder="请上传营业执照" name="yyName"
                                                                   onclick="file_1_${st.index}.click();"
                                                                   value="${bus.name}">
                                                            <input type="hidden" id="uri_1_${st.index}" name="yybusUri"
                                                                   value="${bus.uri}">

                                                        </c:when>
                                                        <c:otherwise>
                                                            <input type="text" class="input-middle"
                                                                   id="name_1_${st.index}" readonly="readonly"
                                                                   placeholder="请上传营业执照" name="name_1"
                                                                   onclick="file_1_${st.index}.click();"
                                                                   value="${bus.name}">
                                                            <input type="hidden" id="uri_1_${st.index}" name="uri_1"
                                                                   value="${bus.uri}">
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                           id="busUpload"
                                                           onclick="return $('#file_1_${st.index}').click();">浏览</label>
                                                    <div class="font-red " id="yy-message" style="display: none;">请上传营业执照</div>
                                                </div>

                                                <c:choose>
                                                    <c:when test="${bus.uri ne null && bus.uri ne ''}">
                                                        <div class="f_left ">
                                                            <i class="iconsuccesss ml7" id="img_icon_4"></i>
                                                            <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                               class="font-blue cursor-pointer mr5 ml10 mt4"
                                                               id="img_view_1">查看</a>
                                                            <c:choose>
                                                                <c:when test="${st.index == 0 }">
                                                                    <span class="font-red cursor-pointer mt4"
                                                                          onclick="del(1);changeInfo(1)" id="img_del_1">删除</span>
                                                                </c:when>
                                                                <c:otherwise>
                                                                    <span class="font-red cursor-pointer mt4"
                                                                          onclick="delAttachment(this);changeInfo(1)" id="img_del_1">删除</span>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </div>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <div class="f_left ">
                                                            <i class="iconsuccesss ml7 none" id="img_icon_4"></i>
                                                            <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                               class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                               id="img_view_1">查看</a>
                                                            <c:choose>
                                                                <c:when test="${st.index == 0 }">
                                                                    <span class="font-red cursor-pointer mt4 none"
                                                                          onclick="del(1);changeInfo(1)" id="img_del_4">删除</span>
                                                                </c:when>
                                                                <c:otherwise>
                                                                    <span class="font-red cursor-pointer mt4 none"
                                                                          onclick="delAttachment(this);changeInfo(1)" id="img_del_4">删除</span>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </div>
                                                    </c:otherwise>
                                                </c:choose>
                                                <div class="clear"></div>
                                            </div>
                                        </c:forEach>
                                    </c:when>
                                    <c:otherwise>
                                        <div class="mb8 c_1">
                                            <div class="f_left">
                                                <input type="file" class="upload_file" name="lwfile" id="file_1"
                                                       style="display: none;" onchange="uploadFile(this,1);changeInfo(1)"/>
                                                <input type="text" class="input-middle" id="name_1" readonly="readonly"
                                                       placeholder="请上传营业执照" name="yyName" onclick="file_1.click();"
                                                       value="${bus.name}">
                                                <input type="hidden" id="yybusUri" name="yybusUri" value="${bus.uri}">
                                                <div class="font-red " id="yy-message" style="display: none;">请上传营业执照</div>
                                            </div>
                                            <label class="bt-bg-style bt-middle bg-light-blue ml10" id="busUpload"
                                                   onclick="return $('#file_1').click();">浏览</label>
                                            <!-- 上传成功出现 -->
                                            <c:choose>
                                                <c:when test="${!empty bus.uri}">
                                                    <i class="iconsuccesss ml7" id="img_icon_4"></i>
                                                    <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                       class="font-blue cursor-pointer mr5 ml10 mt4"
                                                       id="img_view_1">查看</a>
                                                    <span class="font-red cursor-pointer mt4" onclick="del(1);changeInfo(1)"
                                                          id="img_del_1">删除</span>
                                                </c:when>
                                                <c:otherwise>
                                                    <i class="iconsuccesss ml7 none" id="img_icon_4"></i>
                                                    <a href="" target="_blank"
                                                       class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                       id="img_view_1">查看</a>
                                                    <span class="font-red cursor-pointer mt4 none" onclick="del(1);changeInfo(1)"
                                                          id="img_del_1">删除</span>
                                                </c:otherwise>
                                            </c:choose>
                                            <div class="clear"></div>
                                        </div>
                                    </c:otherwise>
                                </c:choose>

                                <div class="clear" id="conadd1">
                                    <span class="bt-border-style bt-small border-blue mt8"
                                          onclick="con_add(1,'请上传营业执照');">继续添加</span>
                                </div>
                            </li>
                            <li>
                                <div class="f_left">
                                    <div class="font-red " style="display: none;">营业执照不能为空</div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </li>
                <li>
                    <div class="infor_name sex_name" style="width: 150px">
                        <label>二类医疗资质</label>
                    </div>
                    <div class="f_left insertli insertli1">
                        <ul>
                            <li style="margin-bottom:0;">
                                <c:choose>
                                    <c:when test="${!empty el }">
                                        <c:forEach items="${el }" var="bus" varStatus="st">
                                            <div class="mb8 c_2">
                                                <div class="pos_rel f_left ">
                                                    <input type="file" class="upload_file" name="lwfile"
                                                           id="file_2_${st.index}" style="display: none;"
                                                           onchange="uploadFile(this,2);changeInfo(2)"/>
                                                    <c:choose>
                                                        <c:when test="${st.index == 0 }">
                                                            <input type="text" class="input-middle" id="name_2"
                                                                   readonly="readonly"
                                                                   placeholder="请上传二类医疗资质" name="elName"
                                                                   onclick="file_2_${st.index}.click();"
                                                                   value="${bus.name}">
                                                            <input type="hidden" id="uri_2_${st.index}" name="elUri"
                                                                   value="${bus.uri}">

                                                        </c:when>
                                                        <c:otherwise>
                                                            <input type="text" class="input-middle"
                                                                   id="name_2_${st.index}" readonly="readonly"
                                                                   placeholder="请上传二类医疗资质" name="name_2"
                                                                   onclick="file_2_${st.index}.click();"
                                                                   value="${bus.name}">
                                                            <input type="hidden" id="uri_2_${st.index}" name="uri_2"
                                                                   value="${bus.uri}">
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                           id="busUpload"
                                                           onclick="return $('#file_2_${st.index}').click();">浏览</label>
                                                    <div class="font-red " style="display: none;">请选择二类医疗资质</div>
                                                </div>

                                                <c:choose>
                                                    <c:when test="${bus.uri ne null && bus.uri ne ''}">
                                                        <div class="f_left ">
                                                            <i class="iconsuccesss ml7" id="img_icon_4"></i>
                                                            <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                               class="font-blue cursor-pointer mr5 ml10 mt4"
                                                               id="img_view_2">查看</a>
                                                            <c:choose>
                                                                <c:when test="${st.index == 0 }">
                                                                    <span class="font-red cursor-pointer mt4"
                                                                          onclick="del(2);changeInfo(2)" id="img_del_2">删除</span>
                                                                </c:when>
                                                                <c:otherwise>
                                                                    <span class="font-red cursor-pointer mt4"
                                                                          onclick="delAttachment(this);changeInfo(2)" id="img_del_2">删除</span>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </div>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <div class="f_left ">
                                                            <i class="iconsuccesss ml7 none" id="img_icon_4"></i>
                                                            <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                               class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                               id="img_view_2">查看</a>
                                                            <c:choose>
                                                                <c:when test="${st.index == 0 }">
                                                                    <span class="font-red cursor-pointer mt4 none"
                                                                          onclick="del(2);changeInfo(2)" id="img_del_2">删除</span>
                                                                </c:when>
                                                                <c:otherwise>
                                                                    <span class="font-red cursor-pointer mt4 none"
                                                                          onclick="delAttachment(this);changeInfo(2)" id="img_del_2">删除</span>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </div>
                                                    </c:otherwise>
                                                </c:choose>
                                                <div class="clear"></div>
                                            </div>
                                        </c:forEach>
                                    </c:when>
                                    <c:otherwise>
                                        <div class="mb8 c_2">
                                            <div class="f_left">
                                                <input type="file" class="upload_file" name="lwfile" id="file_2"
                                                       style="display: none;" onchange="uploadFile(this,2);changeInfo(2)"/>
                                                <input type="text" class="input-middle" id="name_2" readonly="readonly"
                                                       placeholder="请上传二类医疗资质" name="elName" onclick="file_2.click();"
                                                       value="${bus.name}">
                                                <input type="hidden" id="busUri" name="elUri" value="${bus.uri}">
                                                <div class="font-red " style="display: none;">请上传二类医疗资质</div>
                                            </div>
                                            <label class="bt-bg-style bt-middle bg-light-blue ml10" id="busUpload"
                                                   onclick="return $('#file_2').click();">浏览</label>
                                            <!-- 上传成功出现 -->
                                            <c:choose>
                                                <c:when test="${!empty bus.uri}">
                                                    <i class="iconsuccesss ml7" id="img_icon_4"></i>
                                                    <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                       class="font-blue cursor-pointer mr5 ml10 mt4"
                                                       id="img_view_2">查看</a>
                                                    <span class="font-red cursor-pointer mt4" onclick="del(2);changeInfo(2)"
                                                          id="img_del_2">删除</span>
                                                </c:when>
                                                <c:otherwise>
                                                    <i class="iconsuccesss ml7 none" id="img_icon_4"></i>
                                                    <a href="" target="_blank"
                                                       class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                       id="img_view_2">查看</a>
                                                    <span class="font-red cursor-pointer mt4 none" onclick="del(2);changeInfo(2)"
                                                          id="img_del_2">删除</span>
                                                </c:otherwise>
                                            </c:choose>
                                            <div class="clear"></div>
                                        </div>
                                    </c:otherwise>
                                </c:choose>

                                <div class="clear" id="conadd2">
                                    <span class="bt-border-style bt-small border-blue mt8"
                                          onclick="con_add(2,'请上传二类医疗资质');">继续添加</span>
                                </div>
                            </li>
                            <li>
                                <div class="f_left">
                                    <div class="font-red " style="display: none;">开始时间不能为空</div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </li>
                <li>
                    <div class="infor_name sex_name" style="width: 150px">
                        <label>三类医疗资质</label>
                    </div>
                    <div class="f_left insertli insertli1">
                        <ul>
                            <li style="margin-bottom:0;">
                                <c:choose>
                                    <c:when test="${!empty sl }">
                                        <c:forEach items="${sl }" var="bus" varStatus="st">
                                            <div class="mb8 c_3">
                                                <div class="pos_rel f_left ">
                                                    <input type="file" class="upload_file" name="lwfile"
                                                           id="file_3_${st.index}" style="display: none;"
                                                           onchange="uploadFile(this,3);changeInfo(3)"/>
                                                    <c:choose>
                                                        <c:when test="${st.index == 0 }">
                                                            <input type="text" class="input-middle" id="name_3"
                                                                   readonly="readonly"
                                                                   placeholder="请上传三类医疗资质" name="slName"
                                                                   onclick="file_3_${st.index}.click();"
                                                                   value="${bus.name}">
                                                            <input type="hidden" id="uri_3_${st.index}" name="slUri"
                                                                   value="${bus.uri}">

                                                        </c:when>
                                                        <c:otherwise>
                                                            <input type="text" class="input-middle"
                                                                   id="name_3_${st.index}" readonly="readonly"
                                                                   placeholder="请上传三类医疗资质" name="name_3"
                                                                   onclick="file_3_${st.index}.click();"
                                                                   value="${bus.name}">
                                                            <input type="hidden" id="uri_3_${st.index}" name="uri_3"
                                                                   value="${bus.uri}">
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                           id="busUpload"
                                                           onclick="return $('#file_3_${st.index}').click();">浏览</label>
                                                    <div class="font-red " style="display: none;">请选择三类医疗资质</div>
                                                </div>

                                                <c:choose>
                                                    <c:when test="${bus.uri ne null && bus.uri ne ''}">
                                                        <div class="f_left ">
                                                            <i class="iconsuccesss ml7" id="img_icon_4"></i>
                                                            <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                               class="font-blue cursor-pointer mr5 ml10 mt4"
                                                               id="img_view_3">查看</a>
                                                            <c:choose>
                                                                <c:when test="${st.index == 0 }">
                                                                    <span class="font-red cursor-pointer mt4"
                                                                          onclick="del(3);changeInfo(3)" id="img_del_3">删除</span>
                                                                </c:when>
                                                                <c:otherwise>
                                                                    <span class="font-red cursor-pointer mt4"
                                                                          onclick="delAttachment(this);changeInfo(3)" id="img_del_3">删除</span>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </div>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <div class="f_left ">
                                                            <i class="iconsuccesss ml7 none" id="img_icon_4"></i>
                                                            <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                               class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                               id="img_view_3">查看</a>
                                                            <c:choose>
                                                                <c:when test="${st.index == 0 }">
                                                                    <span class="font-red cursor-pointer mt4 none"
                                                                          onclick="del(3);changeInfo(3)" id="img_del_3">删除</span>
                                                                </c:when>
                                                                <c:otherwise>
                                                                    <span class="font-red cursor-pointer mt4 none"
                                                                          onclick="delAttachment(this);changeInfo(3)" id="img_del_3">删除</span>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </div>
                                                    </c:otherwise>
                                                </c:choose>
                                                <div class="clear"></div>
                                            </div>
                                        </c:forEach>
                                    </c:when>
                                    <c:otherwise>
                                        <div class="mb8 c_3">
                                            <div class="f_left">
                                                <input type="file" class="upload_file" name="lwfile" id="file_3"
                                                       style="display: none;" onchange="uploadFile(this,3);changeInfo(3)"/>
                                                <input type="text" class="input-middle" id="name_3" readonly="readonly"
                                                       placeholder="请上传三类医疗资质" name="slName" onclick="file_3.click();"
                                                       value="${bus.name}">
                                                <input type="hidden" id="busUri" name="slUri" value="${bus.uri}">
                                                <div class="font-red " style="display: none;">请上传三类医疗资质</div>
                                            </div>
                                            <label class="bt-bg-style bt-middle bg-light-blue ml10" id="busUpload"
                                                   onclick="return $('#file_3').click();">浏览</label>
                                            <!-- 上传成功出现 -->
                                            <c:choose>
                                                <c:when test="${!empty bus.uri}">
                                                    <i class="iconsuccesss ml7" id="img_icon_4"></i>
                                                    <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                       class="font-blue cursor-pointer mr5 ml10 mt4"
                                                       id="img_view_3">查看</a>
                                                    <span class="font-red cursor-pointer mt4" onclick="del(3);changeInfo(3)"
                                                          id="img_del_3">删除</span>
                                                </c:when>
                                                <c:otherwise>
                                                    <i class="iconsuccesss ml7 none" id="img_icon_4"></i>
                                                    <a href="" target="_blank"
                                                       class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                       id="img_view_3">查看</a>
                                                    <span class="font-red cursor-pointer mt4 none" onclick="del(3);changeInfo(3)"
                                                          id="img_del_3">删除</span>
                                                </c:otherwise>
                                            </c:choose>
                                            <div class="clear"></div>
                                        </div>
                                    </c:otherwise>
                                </c:choose>

                                <div class="clear" id="conadd3">
                                    <span class="bt-border-style bt-small border-blue mt8"
                                          onclick="con_add(3,'请上传三类医疗资质');">继续添加</span>
                                </div>
                            </li>
                            <li>
                                <div class="f_left">
                                    <div class="font-red " style="display: none;">开始时间不能为空</div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </li>
                <li>
                    <div class="infor_name sex_name" style="width: 150px">
                        <label>诚信声明</label>
                    </div>
                    <div class="f_left insertli insertli1">
                        <ul>
                            <li style="margin-bottom:0;">
                                <c:choose>
                                    <c:when test="${!empty cx }">
                                        <c:forEach items="${cx }" var="bus" varStatus="st">
                                            <div class="mb8 c_5">
                                                <div class="pos_rel f_left ">
                                                    <input type="file" class="upload_file" name="lwfile"
                                                           id="file_5_${st.index}" style="display: none;"
                                                           onchange="uploadFile(this,5);changeInfo(5)"/>
                                                    <c:choose>
                                                        <c:when test="${st.index == 0 }">
                                                            <input type="text" class="input-middle" id="name_5"
                                                                   readonly="readonly"
                                                                   placeholder="请上传诚信声明" name="cxName"
                                                                   onclick="file_5_${st.index}.click();"
                                                                   value="${bus.name}">
                                                            <input type="hidden" id="uri_5_${st.index}" name="cxUri"
                                                                   value="${bus.uri}">

                                                        </c:when>
                                                        <c:otherwise>
                                                            <input type="text" class="input-middle"
                                                                   id="name_5_${st.index}" readonly="readonly"
                                                                   placeholder="请上传诚信声明" name="name_5"
                                                                   onclick="file_5_${st.index}.click();"
                                                                   value="${bus.name}">
                                                            <input type="hidden" id="uri_5_${st.index}" name="uri_5"
                                                                   value="${bus.uri}">
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                           id="busUpload"
                                                           onclick="return $('#file_5_${st.index}').click();">浏览</label>
                                                    <div class="font-red " style="display: none;">请选择诚信声明</div>
                                                </div>

                                                <c:choose>
                                                    <c:when test="${bus.uri ne null && bus.uri ne ''}">
                                                        <div class="f_left ">
                                                            <i class="iconsuccesss ml7" id="img_icon_4"></i>
                                                            <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                               class="font-blue cursor-pointer mr5 ml10 mt4"
                                                               id="img_view_5">查看</a>
                                                            <c:choose>
                                                                <c:when test="${st.index == 0 }">
                                                                                <span class="font-red cursor-pointer mt4"
                                                                                      onclick="del(5);changeInfo(5)" id="img_del_5">删除</span>
                                                                </c:when>
                                                                <c:otherwise>
                                                                                <span class="font-red cursor-pointer mt4"
                                                                                      onclick="delAttachment(this);changeInfo(5)" id="img_del_5">删除</span>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </div>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <div class="f_left ">
                                                            <i class="iconsuccesss ml7 none" id="img_icon_4"></i>
                                                            <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                               class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                               id="img_view_5">查看</a>
                                                            <c:choose>
                                                                <c:when test="${st.index == 0 }">
                                                                                <span class="font-red cursor-pointer mt4 none"
                                                                                      onclick="del(5);changeInfo(5)" id="img_del_5">删除</span>
                                                                </c:when>
                                                                <c:otherwise>
                                                                                <span class="font-red cursor-pointer mt4 none"
                                                                                      onclick="delAttachment(this);changeInfo(5)" id="img_del_5">删除</span>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </div>
                                                    </c:otherwise>
                                                </c:choose>
                                                <div class="clear"></div>
                                            </div>
                                        </c:forEach>
                                    </c:when>
                                    <c:otherwise>
                                        <div class="mb8 c_5">
                                            <div class="f_left">
                                                <input type="file" class="upload_file" name="lwfile" id="file_5"
                                                       style="display: none;" onchange="uploadFile(this,5);changeInfo(5)"/>
                                                <input type="text" class="input-middle" id="name_5" readonly="readonly"
                                                       placeholder="请上传诚信声明" name="cxName" onclick="file_5.click();"
                                                       value="${bus.name}">
                                                <input type="hidden" id="busUri" name="cxUri" value="${bus.uri}">
                                                <div class="font-red " style="display: none;">请上传诚信声明</div>
                                            </div>
                                            <label class="bt-bg-style bt-middle bg-light-blue ml10" id="busUpload"
                                                   onclick="return $('#file_5').click();">浏览</label>
                                            <!-- 上传成功出现 -->
                                            <c:choose>
                                                <c:when test="${!empty bus.uri}">
                                                    <i class="iconsuccesss ml7" id="img_icon_4"></i>
                                                    <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                       class="font-blue cursor-pointer mr5 ml10 mt4"
                                                       id="img_view_5">查看</a>
                                                    <span class="font-red cursor-pointer mt4" onclick="del(5);changeInfo(5)"
                                                          id="img_del_5">删除</span>
                                                </c:when>
                                                <c:otherwise>
                                                    <i class="iconsuccesss ml7 none" id="img_icon_4"></i>
                                                    <a href="" target="_blank"
                                                       class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                       id="img_view_5">查看</a>
                                                    <span class="font-red cursor-pointer mt4 none" onclick="del(5);changeInfo(5)"
                                                          id="img_del_5">删除</span>
                                                </c:otherwise>
                                            </c:choose>
                                            <div class="clear"></div>
                                        </div>
                                    </c:otherwise>
                                </c:choose>

                                <div class="clear" id="conadd5">
                                                <span class="bt-border-style bt-small border-blue mt8"
                                                      onclick="con_add(5,'请上传诚信声明');">继续添加</span>
                                </div>
                            </li>
                            <li>
                                <div class="f_left">
                                    <div class="font-red " style="display: none;">开始时间不能为空</div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </li>
                <li>
                    <div class="infor_name sex_name" style="width: 150px">
                        <span style="font-red">*</span><label>使用二级分销商声明</label>
                    </div>
                    <div class="f_left insertli insertli1">
                        <ul>
                            <li style="margin-bottom:0;">
                                <c:choose>
                                    <c:when test="${!empty sy }">
                                        <c:forEach items="${sy }" var="bus" varStatus="st">
                                            <div class="mb8 c_6">
                                                <div class="pos_rel f_left ">
                                                    <input type="file" class="upload_file" name="lwfile"
                                                           id="file_6_${st.index}" style="display: none;"
                                                           onchange="uploadFile(this,6);changeInfo(6)"/>
                                                    <c:choose>
                                                        <c:when test="${st.index == 0 }">
                                                            <input type="text" class="input-middle" id="name_6"
                                                                   readonly="readonly"
                                                                   placeholder="请上传使用二级分销商声明" name="syName"
                                                                   onclick="file_6_${st.index}.click();"
                                                                   value="${bus.name}">
                                                            <input type="hidden" id="uri_6_${st.index}" name="syUri"
                                                                   value="${bus.uri}">
                                                            <div class="font-red " id ="sy-message" style="display: none;">请上使用二级分销商声明</div>

                                                        </c:when>
                                                        <c:otherwise>
                                                            <input type="text" class="input-middle"
                                                                   id="name_6_${st.index}" readonly="readonly"
                                                                   placeholder="请上传使用二级分销商声明" name="name_6"
                                                                   onclick="file_6_${st.index}.click();"
                                                                   value="${bus.name}">
                                                            <input type="hidden" id="uri_6_${st.index}" name="uri_6"
                                                                   value="${bus.uri}">
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                           id="busUpload"
                                                           onclick="return $('#file_6_${st.index}').click();">浏览</label>
                                                    <div class="font-red " style="display: none;">请上传使用二级分销商声明</div>
                                                </div>

                                                <c:choose>
                                                    <c:when test="${bus.uri ne null && bus.uri ne ''}">
                                                        <div class="f_left ">
                                                            <i class="iconsuccesss ml7" id="img_icon_4"></i>
                                                            <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                               class="font-blue cursor-pointer mr5 ml10 mt4"
                                                               id="img_view_6">查看</a>
                                                            <c:choose>
                                                                <c:when test="${st.index == 0 }">
                                                                                            <span class="font-red cursor-pointer mt4"
                                                                                                  onclick="del(6);changeInfo(6)" id="img_del_6">删除</span>
                                                                </c:when>
                                                                <c:otherwise>
                                                                                            <span class="font-red cursor-pointer mt4"
                                                                                                  onclick="delAttachment(this);changeInfo(6)" id="img_del_6">删除</span>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </div>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <div class="f_left ">
                                                            <i class="iconsuccesss ml7 none" id="img_icon_4"></i>
                                                            <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                               class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                               id="img_view_6">查看</a>
                                                            <c:choose>
                                                                <c:when test="${st.index == 0 }">
                                                                                            <span class="font-red cursor-pointer mt4 none"
                                                                                                  onclick="del(6);changeInfo(6)" id="img_del_6">删除</span>
                                                                </c:when>
                                                                <c:otherwise>
                                                                                            <span class="font-red cursor-pointer mt4 none"
                                                                                                  onclick="delAttachment(this);changeInfo(6)" id="img_del_6">删除</span>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </div>
                                                    </c:otherwise>
                                                </c:choose>
                                                <div class="clear"></div>
                                            </div>
                                        </c:forEach>
                                    </c:when>
                                    <c:otherwise>
                                        <div class="mb8 c_6">
                                            <div class="f_left">
                                                <input type="file" class="upload_file" name="lwfile" id="file_6"
                                                       style="display: none;" onchange="uploadFile(this,6);changeInfo(6)"/>
                                                <input type="text" class="input-middle" id="name_6" readonly="readonly"
                                                       placeholder="请上传使用二级分销商声明" name="syName" onclick="file_6.click();"
                                                       value="${bus.name}">
                                                <input type="hidden" id="busUri" name="syUri" value="${bus.uri}">
                                                <div class="font-red " id ="sy-message" style="display: none;">请上传使用二级分销商声明</div>
                                            </div>
                                            <label class="bt-bg-style bt-middle bg-light-blue ml10" id="busUpload"
                                                   onclick="return $('#file_6').click();">浏览</label>
                                            <!-- 上传成功出现 -->
                                            <c:choose>
                                                <c:when test="${!empty bus.uri}">
                                                    <i class="iconsuccesss ml7" id="img_icon_4"></i>
                                                    <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                       class="font-blue cursor-pointer mr5 ml10 mt4"
                                                       id="img_view_6">查看</a>
                                                    <span class="font-red cursor-pointer mt4" onclick="del(6);changeInfo(6)"
                                                          id="img_del_6">删除</span>
                                                </c:when>
                                                <c:otherwise>
                                                    <i class="iconsuccesss ml7 none" id="img_icon_4"></i>
                                                    <a href="" target="_blank"
                                                       class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                       id="img_view_6">查看</a>
                                                    <span class="font-red cursor-pointer mt4 none" onclick="del(6);changeInfo(6)"
                                                          id="img_del_6">删除</span>
                                                </c:otherwise>
                                            </c:choose>
                                            <div class="clear"></div>
                                        </div>
                                    </c:otherwise>
                                </c:choose>

                                <div class="clear" id="conadd6">
                                                            <span class="bt-border-style bt-small border-blue mt8"
                                                                  onclick="con_add(6,'请上传使用二级分销商声明');">继续添加</span>
                                </div>
                            </li>
                            <li>
                                <div class="f_left">
                                    <div class="font-red " style="display: none;">开始时间不能为空</div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </li>
                <li>
                    <div class="infor_name sex_name" style="width: 150px">
                        <label>信用查询结果</label>
                    </div>
                    <div class="f_left insertli insertli1">
                        <ul>
                            <li style="margin-bottom:0;">
                                <c:choose>
                                    <c:when test="${!empty xy }">
                                        <c:forEach items="${xy }" var="bus" varStatus="st">
                                            <div class="mb8 c_7">
                                                <div class="pos_rel f_left ">
                                                    <input type="file" class="upload_file" name="lwfile"
                                                           id="file_7_${st.index}" style="display: none;"
                                                           onchange="uploadFile(this,7);changeInfo(7)"/>
                                                    <c:choose>
                                                        <c:when test="${st.index == 0 }">
                                                            <input type="text" class="input-middle" id="name_7"
                                                                   readonly="readonly"
                                                                   placeholder="请上传信用查询结果" name="xyName"
                                                                   onclick="file_7_${st.index}.click();"
                                                                   value="${bus.name}">
                                                            <input type="hidden" id="uri_7_${st.index}" name="xyUri"
                                                                   value="${bus.uri}">

                                                        </c:when>
                                                        <c:otherwise>
                                                            <input type="text" class="input-middle"
                                                                   id="name_7_${st.index}" readonly="readonly"
                                                                   placeholder="请上传信用查询结果" name="name_7"
                                                                   onclick="file_7_${st.index}.click();"
                                                                   value="${bus.name}">
                                                            <input type="hidden" id="uri_7_${st.index}" name="uri_7"
                                                                   value="${bus.uri}">
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                           id="busUpload"
                                                           onclick="return $('#file_7_${st.index}').click();">浏览</label>
                                                    <div class="font-red " style="display: none;">请选择传信用查询结果</div>
                                                </div>

                                                <c:choose>
                                                    <c:when test="${bus.uri ne null && bus.uri ne ''}">
                                                        <div class="f_left ">
                                                            <i class="iconsuccesss ml7" id="img_icon_4"></i>
                                                            <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                               class="font-blue cursor-pointer mr5 ml10 mt4"
                                                               id="img_view_7">查看</a>
                                                            <c:choose>
                                                                <c:when test="${st.index == 0 }">
                                                                                                        <span class="font-red cursor-pointer mt4"
                                                                                                              onclick="del(7);changeInfo(7)" id="img_del_7">删除</span>
                                                                </c:when>
                                                                <c:otherwise>
                                                                                                        <span class="font-red cursor-pointer mt4"
                                                                                                              onclick="delAttachment(this);changeInfo(7)" id="img_del_7">删除</span>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </div>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <div class="f_left ">
                                                            <i class="iconsuccesss ml7 none" id="img_icon_4"></i>
                                                            <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                               class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                               id="img_view_7">查看</a>
                                                            <c:choose>
                                                                <c:when test="${st.index == 0 }">
                                                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                                                              onclick="del(7);changeInfo(7)" id="img_del_7">删除</span>
                                                                </c:when>
                                                                <c:otherwise>
                                                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                                                              onclick="delAttachment(this);changeInfo(7)" id="img_del_7">删除</span>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </div>
                                                    </c:otherwise>
                                                </c:choose>
                                                <div class="clear"></div>
                                            </div>
                                        </c:forEach>
                                    </c:when>
                                    <c:otherwise>
                                        <div class="mb8 c_7">
                                            <div class="f_left">
                                                <input type="file" class="upload_file" name="lwfile" id="file_7"
                                                       style="display: none;" onchange="uploadFile(this,7);changeInfo(7)"/>
                                                <input type="text" class="input-middle" id="name_7" readonly="readonly"
                                                       placeholder="请上传信用查询结果" name="xyName" onclick="file_7.click();"
                                                       value="${bus.name}">
                                                <input type="hidden" id="busUri" name="xyUri" value="${bus.uri}">
                                                <div class="font-red " style="display: none;">请上传信用查询结果</div>
                                            </div>
                                            <label class="bt-bg-style bt-middle bg-light-blue ml10" id="busUpload"
                                                   onclick="return $('#file_7').click();">浏览</label>
                                            <!-- 上传成功出现 -->
                                            <c:choose>
                                                <c:when test="${!empty bus.uri}">
                                                    <i class="iconsuccesss ml7" id="img_icon_4"></i>
                                                    <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                       class="font-blue cursor-pointer mr5 ml10 mt4"
                                                       id="img_view_7">查看</a>
                                                    <span class="font-red cursor-pointer mt4" onclick="del(7);changeInfo(7)"
                                                          id="img_del_7">删除</span>
                                                </c:when>
                                                <c:otherwise>
                                                    <i class="iconsuccesss ml7 none" id="img_icon_4"></i>
                                                    <a href="" target="_blank"
                                                       class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                       id="img_view_7">查看</a>
                                                    <span class="font-red cursor-pointer mt4 none" onclick="del(7);changeInfo(7)"
                                                          id="img_del_7">删除</span>
                                                </c:otherwise>
                                            </c:choose>
                                            <div class="clear"></div>
                                        </div>
                                    </c:otherwise>
                                </c:choose>

                                <div class="clear" id="conadd7">
                                                                        <span class="bt-border-style bt-small border-blue mt8"
                                                                              onclick="con_add(7,'请上传信用查询结果');">继续添加</span>
                                </div>
                            </li>
                            <li>
                                <div class="f_left">
                                    <div class="font-red " style="display: none;">开始时间不能为空</div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </li>
                <li>
                    <div class="infor_name sex_name" style="width: 150px">
                        <label>招标文件</label>
                    </div>
                    <div class="f_left insertli insertli1">
                        <ul>
                            <li style="margin-bottom:0;">
                                <c:choose>
                                    <c:when test="${!empty zb }">
                                        <c:forEach items="${zb }" var="bus" varStatus="st">
                                            <div class="mb8 c_8">
                                                <div class="pos_rel f_left ">
                                                    <input type="file" class="upload_file" name="lwfile"
                                                           id="file_8_${st.index}" style="display: none;"
                                                           onchange="uploadFile(this,8);changeInfo(8)"/>
                                                    <c:choose>
                                                        <c:when test="${st.index == 0 }">
                                                            <input type="text" class="input-middle" id="name_8"
                                                                   readonly="readonly"
                                                                   placeholder="请上传招标文件" name="zbName"
                                                                   onclick="file_8_${st.index}.click();"
                                                                   value="${bus.name}">
                                                            <input type="hidden" id="uri_8_${st.index}" name="zbUri"
                                                                   value="${bus.uri}">

                                                        </c:when>
                                                        <c:otherwise>
                                                            <input type="text" class="input-middle"
                                                                   id="name_8_${st.index}" readonly="readonly"
                                                                   placeholder="请上招标文件" name="name_8"
                                                                   onclick="file_8_${st.index}.click();"
                                                                   value="${bus.name}">
                                                            <input type="hidden" id="uri_8_${st.index}" name="uri_8"
                                                                   value="${bus.uri}">
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                           id="busUpload"
                                                           onclick="return $('#file_8_${st.index}').click();">浏览</label>
                                                    <div class="font-red " style="display: none;">请选择招标文件</div>
                                                </div>

                                                <c:choose>
                                                    <c:when test="${bus.uri ne null && bus.uri ne ''}">
                                                        <div class="f_left ">
                                                            <i class="iconsuccesss ml7" id="img_icon_4"></i>
                                                            <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                               class="font-blue cursor-pointer mr5 ml10 mt4"
                                                               id="img_view_8">查看</a>
                                                            <c:choose>
                                                                <c:when test="${st.index == 0 }">
                                                                                                                    <span class="font-red cursor-pointer mt4"
                                                                                                                          onclick="del(8);changeInfo(8)" id="img_del_8">删除</span>
                                                                </c:when>
                                                                <c:otherwise>
                                                                                                                    <span class="font-red cursor-pointer mt4"
                                                                                                                          onclick="delAttachment(this);changeInfo(8)" id="img_del_8">删除</span>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </div>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <div class="f_left ">
                                                            <i class="iconsuccesss ml7 none" id="img_icon_4"></i>
                                                            <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                               class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                               id="img_view_8">查看</a>
                                                            <c:choose>
                                                                <c:when test="${st.index == 0 }">
                                                                                                                    <span class="font-red cursor-pointer mt4 none"
                                                                                                                          onclick="del(8);changeInfo(8)" id="img_del_8">删除</span>
                                                                </c:when>
                                                                <c:otherwise>
                                                                                                                    <span class="font-red cursor-pointer mt4 none"
                                                                                                                          onclick="delAttachment(this);changeInfo(8)" id="img_del_8">删除</span>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </div>
                                                    </c:otherwise>
                                                </c:choose>
                                                <div class="clear"></div>
                                            </div>
                                        </c:forEach>
                                    </c:when>
                                    <c:otherwise>
                                        <div class="mb8 c_8">
                                            <div class="f_left">
                                                <input type="file" class="upload_file" name="lwfile" id="file_8"
                                                       style="display: none;" onchange="uploadFile(this,8);changeInfo(8)"/>
                                                <input type="text" class="input-middle" id="name_8" readonly="readonly"
                                                       placeholder="请上传招标文件" name="zbName" onclick="file_8.click();"
                                                       value="${bus.name}">
                                                <input type="hidden" id="busUri" name="zbUri" value="${bus.uri}">
                                                <div class="font-red " style="display: none;">请上传招标文件</div>
                                            </div>
                                            <label class="bt-bg-style bt-middle bg-light-blue ml10" id="busUpload"
                                                   onclick="return $('#file_8').click();">浏览</label>
                                            <!-- 上传成功出现 -->
                                            <c:choose>
                                                <c:when test="${!empty bus.uri}">
                                                    <i class="iconsuccesss ml7" id="img_icon_4"></i>
                                                    <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                       class="font-blue cursor-pointer mr5 ml10 mt4"
                                                       id="img_view_8">查看</a>
                                                    <span class="font-red cursor-pointer mt4" onclick="del(8);changeInfo(8)"
                                                          id="img_del_8">删除</span>
                                                </c:when>
                                                <c:otherwise>
                                                    <i class="iconsuccesss ml7 none" id="img_icon_4"></i>
                                                    <a href="" target="_blank"
                                                       class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                       id="img_view_8">查看</a>
                                                    <span class="font-red cursor-pointer mt4 none" onclick="del(8);changeInfo(8)"
                                                          id="img_del_8">删除</span>
                                                </c:otherwise>
                                            </c:choose>
                                            <div class="clear"></div>
                                        </div>
                                    </c:otherwise>
                                </c:choose>

                                <div class="clear" id="conadd8">
                                                                                    <span class="bt-border-style bt-small border-blue mt8"
                                                                                          onclick="con_add(8,'请上传招标文件');">继续添加</span>
                                </div>
                            </li>
                            <li>
                                <div class="f_left">
                                    <div class="font-red " style="display: none;">开始时间不能为空</div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </li>
                <li>
                    <div class="infor_name sex_name" style="width: 150px">
                        <label>其他附件</label>
                    </div>
                    <div class="f_left insertli insertli1">
                        <ul>
                            <li style="margin-bottom:0;">
                                <c:choose>
                                    <c:when test="${!empty qt }">
                                        <c:forEach items="${qt }" var="bus" varStatus="st">
                                            <div class="mb8 c_9">
                                                <div class="pos_rel f_left ">
                                                    <input type="file" class="upload_file" name="lwfile"
                                                           id="file_9_${st.index}" style="display: none;"
                                                           onchange="uploadFile(this,9);changeInfo(9)"/>
                                                    <c:choose>
                                                        <c:when test="${st.index == 0 }">
                                                            <input type="text" class="input-middle" id="name_9"
                                                                   readonly="readonly"
                                                                   placeholder="请上传其他附件" name="qtName"
                                                                   onclick="file_9_${st.index}.click();"
                                                                   value="${bus.name}">
                                                            <input type="hidden" id="uri_9_${st.index}" name="qtUri"
                                                                   value="${bus.uri}">

                                                        </c:when>
                                                        <c:otherwise>
                                                            <input type="text" class="input-middle"
                                                                   id="name_9_${st.index}" readonly="readonly"
                                                                   placeholder="请上传其他附件" name="name_9"
                                                                   onclick="file_9_${st.index}.click();"
                                                                   value="${bus.name}">
                                                            <input type="hidden" id="uri_9_${st.index}" name="uri_9"
                                                                   value="${bus.uri}">
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                           id="busUpload"
                                                           onclick="return $('#file_9_${st.index}').click();">浏览</label>
                                                    <div class="font-red " style="display: none;">请选择其他附件</div>
                                                </div>

                                                <c:choose>
                                                    <c:when test="${bus.uri ne null && bus.uri ne ''}">
                                                        <div class="f_left ">
                                                            <i class="iconsuccesss ml7" id="img_icon_4"></i>
                                                            <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                               class="font-blue cursor-pointer mr5 ml10 mt4"
                                                               id="img_view_9">查看</a>
                                                            <c:choose>
                                                                <c:when test="${st.index == 0 }">
                                                                                                                                <span class="font-red cursor-pointer mt4"
                                                                                                                                      onclick="del(9);changeInfo(9)" id="img_del_9">删除</span>
                                                                </c:when>
                                                                <c:otherwise>
                                                                                                                                <span class="font-red cursor-pointer mt4"
                                                                                                                                      onclick="delAttachment(this);changeInfo(9)" id="img_del_9">删除</span>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </div>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <div class="f_left ">
                                                            <i class="iconsuccesss ml7 none" id="img_icon_4"></i>
                                                            <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                               class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                               id="img_view_9">查看</a>
                                                            <c:choose>
                                                                <c:when test="${st.index == 0 }">
                                                                                                                                <span class="font-red cursor-pointer mt4 none"
                                                                                                                                      onclick="del(9);changeInfo(9)" id="img_del_9">删除</span>
                                                                </c:when>
                                                                <c:otherwise>
                                                                                                                                <span class="font-red cursor-pointer mt4 none"
                                                                                                                                      onclick="delAttachment(this);changeInfo(9)" id="img_del_9">删除</span>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </div>
                                                    </c:otherwise>
                                                </c:choose>
                                                <div class="clear"></div>
                                            </div>
                                        </c:forEach>
                                    </c:when>
                                    <c:otherwise>
                                        <div class="mb8 c_9">
                                            <div class="f_left">
                                                <input type="file" class="upload_file" name="lwfile" id="file_9"
                                                       style="display: none;" onchange="uploadFile(this,9);changeInfo(9)"/>
                                                <input type="text" class="input-middle" id="name_9" readonly="readonly"
                                                       placeholder="请上传其他附件" name="qtName" onclick="file_9.click();"
                                                       value="${bus.name}">
                                                <input type="hidden" id="busUri" name="qtUri" value="${bus.uri}">
                                                <div class="font-red " style="display: none;">请上传其他附件</div>
                                            </div>
                                            <label class="bt-bg-style bt-middle bg-light-blue ml10" id="busUpload"
                                                   onclick="return $('#file_9').click();">浏览</label>
                                            <!-- 上传成功出现 -->
                                            <c:choose>
                                                <c:when test="${!empty bus.uri}">
                                                    <i class="iconsuccesss ml7" id="img_icon_4"></i>
                                                    <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                       class="font-blue cursor-pointer mr5 ml10 mt4"
                                                       id="img_view_9">查看</a>
                                                    <span class="font-red cursor-pointer mt4" onclick="del(9),changeInfo(9)"
                                                          id="img_del_9">删除</span>
                                                </c:when>
                                                <c:otherwise>
                                                    <i class="iconsuccesss ml7 none" id="img_icon_4"></i>
                                                    <a href="" target="_blank"
                                                       class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                       id="img_view_9">查看</a>
                                                    <span class="font-red cursor-pointer mt4 none" onclick="del(9),changeInfo(9)"
                                                          id="img_del_9">删除</span>
                                                </c:otherwise>
                                            </c:choose>
                                            <div class="clear"></div>
                                        </div>
                                    </c:otherwise>
                                </c:choose>

                                <div class="clear" id="conadd9">
                                                                                                <span class="bt-border-style bt-small border-blue mt8"
                                                                                                      onclick="con_add(9,'请上传其他附件');">继续添加</span>
                                </div>
                            </li>
                            <li>
                                <div class="f_left">
                                    <div class="font-red " style="display: none;">开始时间不能为空</div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </li>
            </ul>
            <div class="font-grey9 ml120 line20 font-red">
                友情提醒
                <br/> 以上文件，仅支持JPG、PNG、PDF格式
                <br/>上传资料，每种资料最多上传5个文件，不要超过10MB
            </div>
            <br/>
            <div class="font-grey9 ml120 line20 font-red " id="content-message" style="display: none;">最多输入200字符，请重新编辑并提交</div>
            <div class="infor_name sex_name" style="width: 150px" >
                <label>备注</label>
            </div>
            <div class="">
                <textarea name="content" rows="10" cols="100" onchange="changeInfo(11)" placeholder="最多输入200字符">${geAuthorization.content}</textarea>
            </div>
            <div class="add-tijiao tcenter mt20">
                <button type="submit" class="bt-bg-style bg-light-blue bt-small mr10" id='submit'>保存</button>
                <button type="button" class="bt-bg-style bg-light-red bt-small mr10" onclick="custom_close()" id='cancel0'>取消</button>
            </div>

            <input type="hidden" name="geBussinessChanceId" value="${geBussinessChanceId}">
            <input type="hidden" name="authorizationId" value="${authorizationId}">
            <input type="hidden" name="change" id="change0" value="">
            <input type="hidden" name="change" id="change1" value="">
            <input type="hidden" name="change" id="change2" value="">
            <input type="hidden" name="change" id="change3" value="">
            <input type="hidden" name="change" id="change4" value="">
            <input type="hidden" name="change" id="change5" value="">
            <input type="hidden" name="change" id="change6" value="">
            <input type="hidden" name="change" id="change7" value="">
            <input type="hidden" name="change" id="change8" value="">
            <input type="hidden" name="change" id="change11" value="">
            <input type="hidden" name="formToken" value="${formToken}"/>
        </form>
    </div>
</div>
</body>
<script type="text/javascript" src="<%= basePath %>static/js/ge/edit_authorization.js?rnd=${resourceVersionKey}"></script>
</html>
