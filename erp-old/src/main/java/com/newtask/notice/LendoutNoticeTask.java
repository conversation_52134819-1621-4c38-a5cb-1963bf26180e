package com.newtask.notice;

import com.vedeng.common.activiti.entity.ActivitiTaskUnDoEntity;
import com.vedeng.common.activiti.service.ActivitiNoticeService;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.wms.service.WMSLendOutService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/10/21
 */
@Component
@JobHandler(value = "LendoutNoticeTask")
public class LendoutNoticeTask extends AbstractJobHandler {

    @Autowired
    private WMSLendOutService wmsLendOutService;


    @Autowired
    private ActivitiNoticeService activitiNoticeService;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        List<ActivitiTaskUnDoEntity> taskUnDoEntities =  activitiNoticeService.selectTimeoutTaskForNotice("lendOutAudit_");
        for(ActivitiTaskUnDoEntity taskUnDoEntity : taskUnDoEntities){
            String  x = taskUnDoEntity.getBusinessKey();
            Long lendoutId =Long.parseLong(x.split("_")[1]);

            wmsLendOutService.sendWeixinNoticeForActiviti(lendoutId,x,taskUnDoEntity.getTaskId(),taskUnDoEntity.getUserName());

            XxlJobLogger.log("处理中"+taskUnDoEntity.getBusinessKey());
        }
        return ReturnT.SUCCESS;
    }



}
