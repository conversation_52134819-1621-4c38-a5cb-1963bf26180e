package com.vedeng.api.standard.validation;

import cn.hutool.core.util.StrUtil;
import com.vedeng.api.standard.validation.exception.ValidationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.*;

/**
 * 标准验证器
 * 提供统一的参数验证功能，支持注解验证和自定义规则验证
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-27
 */
@Component
public class StandardValidator {
    
    private static final Logger logger = LoggerFactory.getLogger(StandardValidator.class);
    
    @Autowired
    private ApplicationContext applicationContext;
    
    @Autowired(required = false)
    private Validator beanValidator;
    
    /**
     * 验证请求对象（使用指定的验证规则）
     * 
     * @param request 请求对象
     * @param ruleClasses 验证规则类数组
     * @param <T> 请求对象类型
     * @return 验证结果
     */
    @SafeVarargs
    public final <T> ValidationResult validateRequest(T request, Class<? extends ValidationRule<T>>... ruleClasses) {
        return validateRequest(request, new HashMap<>(), ruleClasses);
    }
    
    /**
     * 验证请求对象（使用指定的验证规则和上下文）
     * 
     * @param request 请求对象
     * @param context 验证上下文
     * @param ruleClasses 验证规则类数组
     * @param <T> 请求对象类型
     * @return 验证结果
     */
    @SafeVarargs
    public final <T> ValidationResult validateRequest(T request, Map<String, Object> context, 
                                                      Class<? extends ValidationRule<T>>... ruleClasses) {
        if (request == null) {
            return ValidationResult.failure("请求对象不能为空");
        }
        
        ValidationResult result = ValidationResult.success();
        
        try {
            // 1. 执行Bean Validation（JSR-303注解验证）
            ValidationResult beanValidationResult = validateWithBeanValidation(request);
            result.merge(beanValidationResult);
            
            // 2. 执行自定义验证规则
            if (ruleClasses != null && ruleClasses.length > 0) {
                ValidationResult customValidationResult = validateWithCustomRules(request, context, ruleClasses);
                result.merge(customValidationResult);
            }
            
            logger.debug("验证完成: request={}, valid={}, errors={}", 
                    request.getClass().getSimpleName(), result.isValid(), result.getErrorMessages());
            
            return result;
            
        } catch (Exception e) {
            logger.error("验证过程中发生异常", e);
            return ValidationResult.failure("验证过程中发生异常: " + e.getMessage());
        }
    }
    
    /**
     * 验证请求对象并在失败时抛出异常
     * 
     * @param request 请求对象
     * @param ruleClasses 验证规则类数组
     * @param <T> 请求对象类型
     * @throws ValidationException 验证失败时抛出
     */
    @SafeVarargs
    public final <T> void validateAndThrow(T request, Class<? extends ValidationRule<T>>... ruleClasses) 
            throws ValidationException {
        ValidationResult result = validateRequest(request, ruleClasses);
        if (!result.isValid()) {
            throw new ValidationException(result.getErrorMessagesAsString(), result);
        }
    }
    
    /**
     * 验证请求对象并在失败时抛出异常（带上下文）
     * 
     * @param request 请求对象
     * @param context 验证上下文
     * @param ruleClasses 验证规则类数组
     * @param <T> 请求对象类型
     * @throws ValidationException 验证失败时抛出
     */
    @SafeVarargs
    public final <T> void validateAndThrow(T request, Map<String, Object> context,
                                           Class<? extends ValidationRule<T>>... ruleClasses) 
            throws ValidationException {
        ValidationResult result = validateRequest(request, context, ruleClasses);
        if (!result.isValid()) {
            throw new ValidationException(result.getErrorMessagesAsString(), result);
        }
    }
    
    /**
     * 使用Bean Validation进行验证
     * 
     * @param request 请求对象
     * @param <T> 请求对象类型
     * @return 验证结果
     */
    private <T> ValidationResult validateWithBeanValidation(T request) {
        if (beanValidator == null) {
            logger.debug("Bean Validator未配置，跳过注解验证");
            return ValidationResult.success("BeanValidation");
        }
        
        Set<ConstraintViolation<T>> violations = beanValidator.validate(request);
        if (violations.isEmpty()) {
            return ValidationResult.success("BeanValidation");
        }
        
        ValidationResult result = ValidationResult.failure("BeanValidation", "Bean验证失败");
        for (ConstraintViolation<T> violation : violations) {
            String errorMessage = violation.getPropertyPath() + ": " + violation.getMessage();
            result.addError(errorMessage);
        }
        
        return result;
    }
    
    /**
     * 使用自定义规则进行验证
     * 
     * @param request 请求对象
     * @param context 验证上下文
     * @param ruleClasses 验证规则类数组
     * @param <T> 请求对象类型
     * @return 验证结果
     */
    @SafeVarargs
    private final <T> ValidationResult validateWithCustomRules(T request, Map<String, Object> context,
                                                               Class<? extends ValidationRule<T>>... ruleClasses) {
        ValidationResult result = ValidationResult.success("CustomRules");
        
        for (Class<? extends ValidationRule<T>> ruleClass : ruleClasses) {
            try {
                ValidationRule<T> rule = getValidationRule(ruleClass);
                if (rule != null && rule.supports(request.getClass())) {
                    ValidationResult ruleResult = rule.validate(request, context);
                    result.merge(ruleResult);
                    
                    logger.debug("验证规则执行完成: rule={}, valid={}", 
                            rule.getRuleName(), ruleResult.isValid());
                } else {
                    logger.warn("验证规则不支持当前请求类型: rule={}, requestType={}", 
                            ruleClass.getSimpleName(), request.getClass().getSimpleName());
                }
            } catch (Exception e) {
                logger.error("执行验证规则时发生异常: rule={}", ruleClass.getSimpleName(), e);
                result.addError("验证规则执行异常: " + ruleClass.getSimpleName() + " - " + e.getMessage());
            }
        }
        
        return result;
    }
    
    /**
     * 获取验证规则实例
     * 
     * @param ruleClass 验证规则类
     * @param <T> 验证规则类型
     * @return 验证规则实例
     */
    @SuppressWarnings("unchecked")
    private <T> ValidationRule<T> getValidationRule(Class<? extends ValidationRule<T>> ruleClass) {
        try {
            // 优先从Spring容器中获取
            Map<String, ? extends ValidationRule> beans = applicationContext.getBeansOfType(ruleClass);
            if (!beans.isEmpty()) {
                return (ValidationRule<T>) beans.values().iterator().next();
            }
            
            // 如果容器中没有，则创建新实例
            return (ValidationRule<T>) ruleClass.newInstance();
            
        } catch (Exception e) {
            logger.error("创建验证规则实例失败: {}", ruleClass.getSimpleName(), e);
            return null;
        }
    }
    
    /**
     * 创建验证上下文
     * 
     * @param params 参数键值对
     * @return 验证上下文
     */
    public static Map<String, Object> createContext(Object... params) {
        Map<String, Object> context = new HashMap<>();
        if (params != null && params.length % 2 == 0) {
            for (int i = 0; i < params.length; i += 2) {
                String key = String.valueOf(params[i]);
                Object value = params[i + 1];
                context.put(key, value);
            }
        }
        return context;
    }
}
