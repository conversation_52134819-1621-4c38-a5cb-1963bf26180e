package com.vedeng.common.shiro.cas.session;

import com.vedeng.authorization.model.User;
import com.vedeng.common.shiro.constant.SecurityConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.session.UnknownSessionException;
import org.apache.shiro.session.mgt.SessionKey;
import org.apache.shiro.session.mgt.SimpleSession;
import org.apache.shiro.web.session.mgt.DefaultWebSessionManager;
import org.apache.shiro.web.session.mgt.WebSessionKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletRequest;
import java.io.Serializable;
import java.util.Collection;
import java.util.Collections;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class MessagingEventSessionManager extends DefaultWebSessionManager {

    private final static Logger LOGGER = LoggerFactory.getLogger(MessagingEventSessionManager.class);

    private final EvictAccountIdRegistry evictAccountIdQueue = EvictAccountIdRegistry.getInstance();

    private static final String PRIFIX_REQUEST_SESSION  = "PRIFIX_REQUEST_SESSION_";

    @Override
    protected Collection<Session> getActiveSessions() {
        Collection<Session> activeSessions = sessionDAO.getActiveSessions();

        if (CollectionUtils.isNotEmpty(activeSessions)) {
            Collection<Integer> accountIdToUse = evictAccountIdQueue.takeAll();
            if (CollectionUtils.isNotEmpty(accountIdToUse)) {
                for (Session session : activeSessions) {
                    if (session instanceof SimpleSession) {
                        SimpleSession sessionToUse = (SimpleSession) session;
                        Object attribute = session.getAttribute(SecurityConstants.CURRENT_USER_ATTRIBUTE_NAME);
                        if (attribute instanceof User) {
                            if (accountIdToUse.contains(((User) attribute).getUserId())) {
                                sessionToUse.stop();
                            }
                        }
                    }
                }
            }
        }

        return activeSessions != null ? activeSessions : Collections.emptySet();
    }

     /**
      * 获取session
      * 优化单次请求需要多次访问redis的问题
      * @param sessionKey
      */
//     @Override
//     protected Session retrieveSession(SessionKey sessionKey) throws UnknownSessionException {
//         Serializable sessionId = getSessionId(sessionKey);
//
//         ServletRequest request = null;
//         if (sessionKey instanceof WebSessionKey) {
//             request = ((WebSessionKey) sessionKey).getServletRequest();
//         }
//
//         if (request != null && null != sessionId) {
//             Object sessionObj = request.getAttribute(PRIFIX_REQUEST_SESSION+sessionId.toString());
//             if (sessionObj != null) {
//                 Session tempSession = (Session) sessionObj;
//                 LOGGER.debug("read session from request");
//                 return tempSession;
//             }
//         }
//
//         Session session = super.retrieveSession(sessionKey);
//         if (request != null && null != sessionId) {
//             LOGGER.debug("read session from request");
//             request.setAttribute(PRIFIX_REQUEST_SESSION+sessionId.toString(), session);
//         }
//         return session;
//     }

}
