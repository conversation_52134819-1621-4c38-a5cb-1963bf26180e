<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchSaleSettlementAdjustmentItemDtoMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchSaleSettlementAdjustmentItemDto">
        <!--@mbg.generated-->
        <!--@Table T_SALE_SETTLEMENT_ADJUSTMENT_ITEM-->
        <id column="SALE_SETTLEMENT_ADJUSTMENT_ITEM_ID" jdbcType="INTEGER" property="saleSettlementAdjustmentItemId"/>
        <result column="SALE_SETTLEMENT_ADJUSTMENT_ID" jdbcType="INTEGER" property="saleSettlementAdjustmentId"/>
        <result column="APPORTION_AMOUNT" jdbcType="DECIMAL" property="apportionAmount"/>
        <result column="ADJUSTMENT_AMOUNT" jdbcType="DECIMAL" property="adjustmentAmount"/>
        <result column="DIFFERENCE_AMOUNT" jdbcType="DECIMAL" property="differenceAmount"/>
        <result column="ACTUAL_AMOUNT" jdbcType="DECIMAL" property="actualAmount"/>
        <result column="SKU_ID" jdbcType="INTEGER" property="skuId"/>
        <result column="SKU" jdbcType="VARCHAR" property="sku"/>
        <result column="SKU_NAME" jdbcType="VARCHAR" property="skuName"/>
        <result column="NUM" jdbcType="INTEGER" property="num"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="SALEORDER_GOODS_ID" jdbcType="INTEGER" property="saleorderGoodsId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        SALE_SETTLEMENT_ADJUSTMENT_ITEM_ID,
        SALE_SETTLEMENT_ADJUSTMENT_ID,
        APPORTION_AMOUNT,
        ADJUSTMENT_AMOUNT,
        DIFFERENCE_AMOUNT,
        ACTUAL_AMOUNT,
        SKU_ID,
        SKU,
        SKU_NAME,
        NUM,
        ADD_TIME,
        CREATOR,
        CREATOR_NAME,
        UPDATER,
        UPDATER_NAME,
        MOD_TIME,
        SALEORDER_GOODS_ID
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from T_SALE_SETTLEMENT_ADJUSTMENT_ITEM
        where SALE_SETTLEMENT_ADJUSTMENT_ITEM_ID = #{saleSettlementAdjustmentItemId,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete
        from T_SALE_SETTLEMENT_ADJUSTMENT_ITEM
        where SALE_SETTLEMENT_ADJUSTMENT_ITEM_ID = #{saleSettlementAdjustmentItemId,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="SALE_SETTLEMENT_ADJUSTMENT_ITEM_ID" keyProperty="saleSettlementAdjustmentItemId"
            parameterType="com.vedeng.erp.kingdee.domain.entity.SaleSettlementAdjustmentItemEntity"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_SALE_SETTLEMENT_ADJUSTMENT_ITEM (SALE_SETTLEMENT_ADJUSTMENT_ID, APPORTION_AMOUNT,
                                                       ADJUSTMENT_AMOUNT, DIFFERENCE_AMOUNT, ACTUAL_AMOUNT,
                                                       SKU_ID, SKU, SKU_NAME,
                                                       NUM, ADD_TIME, CREATOR,
                                                       CREATOR_NAME, UPDATER, UPDATER_NAME,
                                                       MOD_TIME, SALEORDER_GOODS_ID)
        values (#{saleSettlementAdjustmentId,jdbcType=INTEGER}, #{apportionAmount,jdbcType=DECIMAL},
                #{adjustmentAmount,jdbcType=DECIMAL}, #{differenceAmount,jdbcType=DECIMAL},
                #{actualAmount,jdbcType=DECIMAL},
                #{skuId,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, #{skuName,jdbcType=VARCHAR},
                #{num,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER},
                #{creatorName,jdbcType=VARCHAR}, #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR},
                #{modTime,jdbcType=TIMESTAMP}, #{saleorderGoodsId,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" keyColumn="SALE_SETTLEMENT_ADJUSTMENT_ITEM_ID"
            keyProperty="saleSettlementAdjustmentItemId"
            parameterType="com.vedeng.erp.kingdee.domain.entity.SaleSettlementAdjustmentItemEntity"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_SALE_SETTLEMENT_ADJUSTMENT_ITEM
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="saleSettlementAdjustmentId != null">
                SALE_SETTLEMENT_ADJUSTMENT_ID,
            </if>
            <if test="apportionAmount != null">
                APPORTION_AMOUNT,
            </if>
            <if test="adjustmentAmount != null">
                ADJUSTMENT_AMOUNT,
            </if>
            <if test="differenceAmount != null">
                DIFFERENCE_AMOUNT,
            </if>
            <if test="actualAmount != null">
                ACTUAL_AMOUNT,
            </if>
            <if test="skuId != null">
                SKU_ID,
            </if>
            <if test="sku != null and sku != ''">
                SKU,
            </if>
            <if test="skuName != null and skuName != ''">
                SKU_NAME,
            </if>
            <if test="num != null">
                NUM,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="creatorName != null and creatorName != ''">
                CREATOR_NAME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="updaterName != null and updaterName != ''">
                UPDATER_NAME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="saleorderGoodsId != null">
                SALEORDER_GOODS_ID,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="saleSettlementAdjustmentId != null">
                #{saleSettlementAdjustmentId,jdbcType=INTEGER},
            </if>
            <if test="apportionAmount != null">
                #{apportionAmount,jdbcType=DECIMAL},
            </if>
            <if test="adjustmentAmount != null">
                #{adjustmentAmount,jdbcType=DECIMAL},
            </if>
            <if test="differenceAmount != null">
                #{differenceAmount,jdbcType=DECIMAL},
            </if>
            <if test="actualAmount != null">
                #{actualAmount,jdbcType=DECIMAL},
            </if>
            <if test="skuId != null">
                #{skuId,jdbcType=INTEGER},
            </if>
            <if test="sku != null and sku != ''">
                #{sku,jdbcType=VARCHAR},
            </if>
            <if test="skuName != null and skuName != ''">
                #{skuName,jdbcType=VARCHAR},
            </if>
            <if test="num != null">
                #{num,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null and creatorName != ''">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="updaterName != null and updaterName != ''">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="saleorderGoodsId != null">
                #{saleorderGoodsId,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.vedeng.erp.kingdee.domain.entity.SaleSettlementAdjustmentItemEntity">
        <!--@mbg.generated-->
        update T_SALE_SETTLEMENT_ADJUSTMENT_ITEM
        <set>
            <if test="saleSettlementAdjustmentId != null">
                SALE_SETTLEMENT_ADJUSTMENT_ID = #{saleSettlementAdjustmentId,jdbcType=INTEGER},
            </if>
            <if test="apportionAmount != null">
                APPORTION_AMOUNT = #{apportionAmount,jdbcType=DECIMAL},
            </if>
            <if test="adjustmentAmount != null">
                ADJUSTMENT_AMOUNT = #{adjustmentAmount,jdbcType=DECIMAL},
            </if>
            <if test="differenceAmount != null">
                DIFFERENCE_AMOUNT = #{differenceAmount,jdbcType=DECIMAL},
            </if>
            <if test="actualAmount != null">
                ACTUAL_AMOUNT = #{actualAmount,jdbcType=DECIMAL},
            </if>
            <if test="skuId != null">
                SKU_ID = #{skuId,jdbcType=INTEGER},
            </if>
            <if test="sku != null and sku != ''">
                SKU = #{sku,jdbcType=VARCHAR},
            </if>
            <if test="skuName != null and skuName != ''">
                SKU_NAME = #{skuName,jdbcType=VARCHAR},
            </if>
            <if test="num != null">
                NUM = #{num,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null and creatorName != ''">
                CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="updaterName != null and updaterName != ''">
                UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="saleorderGoodsId != null">
                SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER},
            </if>
        </set>
        where SALE_SETTLEMENT_ADJUSTMENT_ITEM_ID = #{saleSettlementAdjustmentItemId,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.vedeng.erp.kingdee.domain.entity.SaleSettlementAdjustmentItemEntity">
        <!--@mbg.generated-->
        update T_SALE_SETTLEMENT_ADJUSTMENT_ITEM
        set SALE_SETTLEMENT_ADJUSTMENT_ID = #{saleSettlementAdjustmentId,jdbcType=INTEGER},
            APPORTION_AMOUNT              = #{apportionAmount,jdbcType=DECIMAL},
            ADJUSTMENT_AMOUNT             = #{adjustmentAmount,jdbcType=DECIMAL},
            DIFFERENCE_AMOUNT             = #{differenceAmount,jdbcType=DECIMAL},
            ACTUAL_AMOUNT                 = #{actualAmount,jdbcType=DECIMAL},
            SKU_ID                        = #{skuId,jdbcType=INTEGER},
            SKU                           = #{sku,jdbcType=VARCHAR},
            SKU_NAME                      = #{skuName,jdbcType=VARCHAR},
            NUM                           = #{num,jdbcType=INTEGER},
            ADD_TIME                      = #{addTime,jdbcType=TIMESTAMP},
            CREATOR                       = #{creator,jdbcType=INTEGER},
            CREATOR_NAME                  = #{creatorName,jdbcType=VARCHAR},
            UPDATER                       = #{updater,jdbcType=INTEGER},
            UPDATER_NAME                  = #{updaterName,jdbcType=VARCHAR},
            MOD_TIME                      = #{modTime,jdbcType=TIMESTAMP},
            SALEORDER_GOODS_ID            = #{saleorderGoodsId,jdbcType=INTEGER}
        where SALE_SETTLEMENT_ADJUSTMENT_ITEM_ID = #{saleSettlementAdjustmentItemId,jdbcType=INTEGER}
    </update>


    <select id="findByAdjustmentId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_SALE_SETTLEMENT_ADJUSTMENT_ITEM
        WHERE SALE_SETTLEMENT_ADJUSTMENT_ID = #{adjustmentId,jdbcType=INTEGER}
    </select>

    <insert id="batchInsert" keyColumn="SALE_SETTLEMENT_ADJUSTMENT_ITEM_ID" keyProperty="saleSettlementAdjustmentItemId" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_SALE_SETTLEMENT_ADJUSTMENT_ITEM
        (SALE_SETTLEMENT_ADJUSTMENT_ID, APPORTION_AMOUNT, ADJUSTMENT_AMOUNT, DIFFERENCE_AMOUNT,
        ACTUAL_AMOUNT, SKU_ID, SKU, SKU_NAME, NUM, ADD_TIME, CREATOR, CREATOR_NAME, UPDATER,
        UPDATER_NAME, MOD_TIME, SALEORDER_GOODS_ID)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.saleSettlementAdjustmentId,jdbcType=INTEGER}, #{item.apportionAmount,jdbcType=DECIMAL},
            #{item.adjustmentAmount,jdbcType=DECIMAL}, #{item.differenceAmount,jdbcType=DECIMAL},
            #{item.actualAmount,jdbcType=DECIMAL}, #{item.skuId,jdbcType=INTEGER}, #{item.sku,jdbcType=VARCHAR},
            #{item.skuName,jdbcType=VARCHAR}, #{item.num,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP},
            #{item.creator,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.updater,jdbcType=INTEGER},
            #{item.updaterName,jdbcType=VARCHAR}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.saleorderGoodsId,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

</mapper>


