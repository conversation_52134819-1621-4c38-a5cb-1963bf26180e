package com.vedeng.erp.aftersale.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 销售售后产品审核信息
 * <AUTHOR>
 */
@Getter
@Setter
public class AfterSaleAuditInfoEntity extends BaseEntity {
    /**
     * 主键
     */
    private Integer afterSaleAuditInfoId;

    /**
     * 销售售后单id
     */
    private Integer afterSaleId;

    /**
     * 销售售后单商品id
     */
    private Integer afterSaleGoodsId;

    /**
     * 是否可退 0否1是
     */
    private Integer isReturn;

    /**
     * 审核状态 0待审核1审核通过2审核不通过
     */
    private Integer  auditStatus;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 审核人名称
     */
    private String userName;

    /**
     * 审核人id，字符类型可存多个 逗号分割
     */
    private String userId;

    /**
     * skuId
     */
    private Integer skuId;

    /**
     * sku编号
     */
    private String skuNo;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 是否删除0否1是
     */
    private Integer isDelete;
}