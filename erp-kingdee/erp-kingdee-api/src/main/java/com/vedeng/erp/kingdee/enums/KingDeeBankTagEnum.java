package com.vedeng.erp.kingdee.enums;

import java.util.Arrays;
import java.util.Optional;

public enum KingDeeBankTagEnum {

    ALI_PAY(4, "支付宝", "支付宝", "JSFS32_SYS"),
    WE_CHAT_PAY(5, "微信", "微信", "JSFS31_SYS"),
    BANK_PAY(-1, "其他", "银行", "JSFS04_SYS"),
    ;


    /**
     * 银行标识
     */
    private Integer code;

    /**
     * 描述
     */
    private String desc;

    /**
     * 交易方式
     */
    private String fQzokJyfs;

    /**
     * 结算方式
     */
    private String fSettleTypeId;

    KingDeeBankTagEnum(Integer code, String desc, String fQzokJyfs, String fSettleTypeId) {
        this.code = code;
        this.desc = desc;
        this.fQzokJyfs = fQzokJyfs;
        this.fSettleTypeId = fSettleTypeId;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getfQzokJyfs() {
        return fQzokJyfs;
    }

    public String getfSettleTypeId() {
        return fSettleTypeId;
    }

    public static String matchFQzokJyfsByCode(Integer code) {
        return findByCode(code)
                .map(KingDeeBankTagEnum::getfQzokJyfs)
                .orElse(BANK_PAY.getfQzokJyfs());
    }

    public static String matchFSettleTypeIdByCode(Integer code) {
        return findByCode(code)
                .map(KingDeeBankTagEnum::getfSettleTypeId)
                .orElse(BANK_PAY.getfSettleTypeId());
    }

    private static Optional<KingDeeBankTagEnum> findByCode(Integer code) {
        return Arrays.stream(KingDeeBankTagEnum.values())
                .filter(entry -> entry.getCode().equals(code))
                .findFirst();
    }

}
