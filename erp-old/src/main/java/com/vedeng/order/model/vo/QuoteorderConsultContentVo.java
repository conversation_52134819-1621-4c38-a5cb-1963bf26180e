package com.vedeng.order.model.vo;

import java.math.BigDecimal;
import java.util.List;

/**
 * 报价咨询内容
 * <AUTHOR>
 * @date created in 2020/7/9 14:52
 */
public class QuoteorderConsultContentVo {

    /**
     * 咨询类型，1订单，2报价单
     */
    private Integer consultType;

    private Integer consultRelatedId;

    /**
     * 咨询主管的内容
     */
    private String consultContent;

    private List<SkuConsult> skuConsults;

    public Integer getConsultType() {
        return consultType;
    }

    public void setConsultType(Integer consultType) {
        this.consultType = consultType;
    }

    public Integer getConsultRelatedId() {
        return consultRelatedId;
    }

    public void setConsultRelatedId(Integer consultRelatedId) {
        this.consultRelatedId = consultRelatedId;
    }

    public String getConsultContent() {
        return consultContent;
    }

    public void setConsultContent(String consultContent) {
        this.consultContent = consultContent;
    }

    public List<SkuConsult> getSkuConsults() {
        return skuConsults;
    }

    public void setSkuConsults(List<SkuConsult> skuConsults) {
        this.skuConsults = skuConsults;
    }

    @Override
    public String toString() {
        return "QuoteConsultContentVo{" +
                "consultType=" + consultType +
                ", consultRelatedId=" + consultRelatedId +
                ", consultContent='" + consultContent + '\'' +
                ", skuConsults=" + skuConsults +
                '}';
    }

    public static class SkuConsult{

        private String sku;

        private Integer quoteorderGoodsId;

        /**
         * 咨询参考报价
         */
        private Boolean consultReferencePrice;

        private String referencePrice;

        /**
         * 咨询货期
         */
        private Boolean consultDeliveryCycle;

        private String deliveryCycle;

        /**
         * 报备情况
         */
        private Boolean consultReport;

        /**
         * 报备：0失败，1成功
         */
        private Integer report;

        /**
         * 报备失败原因
         */
        private String reportContet;

        /**
         * 咨询其他
         */
        private Boolean consultOther;

        private String consultOtherReplyContent;

        /**
         * 咨询其他的内容
         */
        private String consultOtherContent;

        public Boolean getConsultReferencePrice() {
            return consultReferencePrice;
        }

        public void setConsultReferencePrice(Boolean consultReferencePrice) {
            this.consultReferencePrice = consultReferencePrice;
        }

        public Boolean getConsultDeliveryCycle() {
            return consultDeliveryCycle;
        }

        public void setConsultDeliveryCycle(Boolean consultDeliveryCycle) {
            this.consultDeliveryCycle = consultDeliveryCycle;
        }

        public Integer getQuoteorderGoodsId() {
            return quoteorderGoodsId;
        }

        public void setQuoteorderGoodsId(Integer quoteorderGoodsId) {
            this.quoteorderGoodsId = quoteorderGoodsId;
        }

        public Boolean getConsultReport() {
            return consultReport;
        }

        public void setConsultReport(Boolean consultReport) {
            this.consultReport = consultReport;
        }

        public Boolean getConsultOther() {
            return consultOther;
        }

        public void setConsultOther(Boolean consultOther) {
            this.consultOther = consultOther;
        }

        public String getConsultOtherContent() {
            return consultOtherContent;
        }

        public void setConsultOtherContent(String consultOtherContent) {
            this.consultOtherContent = consultOtherContent;
        }

        public String getSku() {
            return sku;
        }

        public void setSku(String sku) {
            this.sku = sku;
        }

        public String getReferencePrice() {
            return referencePrice;
        }

        public void setReferencePrice(String referencePrice) {
            this.referencePrice = referencePrice;
        }

        public String getDeliveryCycle() {
            return deliveryCycle;
        }

        public void setDeliveryCycle(String deliveryCycle) {
            this.deliveryCycle = deliveryCycle;
        }

        public Integer getReport() {
            return report;
        }

        public void setReport(Integer report) {
            this.report = report;
        }

        public String getReportContet() {
            return reportContet;
        }

        public void setReportContet(String reportContet) {
            this.reportContet = reportContet;
        }

        public String getConsultOtherReplyContent() {
            return consultOtherReplyContent;
        }

        public void setConsultOtherReplyContent(String consultOtherReplyContent) {
            this.consultOtherReplyContent = consultOtherReplyContent;
        }

        @Override
        public String toString() {
            return "SkuConsult{" +
                    "sku='" + sku + '\'' +
                    ", quoteorderGoodsId=" + quoteorderGoodsId +
                    ", consultReferencePrice=" + consultReferencePrice +
                    ", referencePrice='" + referencePrice + '\'' +
                    ", consultDeliveryCycle=" + consultDeliveryCycle +
                    ", deliveryCycle='" + deliveryCycle + '\'' +
                    ", consultReport=" + consultReport +
                    ", report=" + report +
                    ", reportContet='" + reportContet + '\'' +
                    ", consultOther=" + consultOther +
                    ", consultOtherReplyContent='" + consultOtherReplyContent + '\'' +
                    ", consultOtherContent='" + consultOtherContent + '\'' +
                    '}';
        }
    }
}
