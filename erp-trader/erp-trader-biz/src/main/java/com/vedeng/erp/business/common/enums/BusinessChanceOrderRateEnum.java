package com.vedeng.erp.business.common.enums;

/**
 * <AUTHOR>
 * @description 商机成单几率枚举类
 * @date 2022/7/14 11:07
 **/
public enum BusinessChanceOrderRateEnum {

    /**
     * 100%
     */
    HUNDRED(956,"100%"),
    /**
     * 80%
     */
    EIGHTY(955,"80%"),
    /**
     * 60%
     */
    SIXTY(954,"60%"),
    /**
     * 40%
     */
    FORTY(953,"40%"),
    /**
     * 20%
     */
    TWENTY(952,"20%");

    /**
     * 标号
     */
    private final Integer code;

    /**
     * 值
     */
    private final String rate;


    BusinessChanceOrderRateEnum(Integer code, String rate) {
        this.code = code;
        this.rate = rate;
    }

    public Integer getCode() {
        return code;
    }

    public String getRate() {
        return rate;
    }
}
