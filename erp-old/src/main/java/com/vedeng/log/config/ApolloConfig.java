package com.vedeng.log.config;

import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.joran.JoranConfigurator;
import ch.qos.logback.core.joran.spi.JoranException;
import ch.qos.logback.core.util.StatusPrinter;
import com.ctrip.framework.apollo.model.ConfigChange;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.vedeng.apollo.ApolloConstant;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.xml.sax.InputSource;

import java.io.StringReader;

/**
 * 动态日志组件代码提交
 * <AUTHOR>
 * @param
 */
@Configuration
public class ApolloConfig {

    static{

    }



    private static final String LOG_NAME_IN_APOLLO = "log.config";

    private static final String resourceVersionKey = "resourceVersionKey";


    private static Logger logger  = LoggerFactory.getLogger(ApolloConfig.class );

    private static String lxcrmUrl = "lxcrmUrl";

    @ApolloConfigChangeListener(value = {"application"})
    private void onChangeToAll(ConfigChangeEvent changeEvent) {
        ConfigChange change;
        for (String key : changeEvent.changedKeys()) {
            change = changeEvent.getChange(key);
            if(LOG_NAME_IN_APOLLO.equals(key) && !StringUtils.equals(change.getOldValue(),change.getNewValue())){
                doLoLogconfig(change.getNewValue());
            }

            if(resourceVersionKey.equals(key) && !StringUtils.equals(change.getOldValue(),change.getNewValue())){
                ApolloConstant.setResourceVersionKey(change.getNewValue())  ;
            }

            if(lxcrmUrl.equals(key) && !StringUtils.equals(change.getOldValue(),change.getNewValue())){
                ApolloConstant.setLxcrmUrl(change.getNewValue());  ;
            }
            // 打印出新增或者变化的配置 相关信息
            logger.info(String.format("Change - key: %s, oldValue: %s, newValue: %s, changeType: %s",
                    change.getPropertyName(), change.getOldValue(), change.getNewValue(),
                    change.getChangeType()));
        }
    }

    public void doLoLogconfig(String logConfig){
        if(StringUtils.isEmpty(logConfig)){
            logger.warn("no logconfig .log reset failure" );
            return ;
        }

        StringReader reader = new StringReader(logConfig);
        InputSource is = new InputSource(reader);

        LoggerContext context = (LoggerContext) LoggerFactory.getILoggerFactory();
        try {
            JoranConfigurator configurator = new JoranConfigurator();
            configurator.setContext(context);
            // Call context.reset() to clear any previous configuration, e.g. default
            // configuration. For multi-step configuration, omit calling context.reset().
            context.reset();
            configurator.doConfigure(is);
            logger.info("logger reset success");
        } catch (JoranException je) {
            // StatusPrinter will handle this
            logger.error("logger reset failure");
        }
        StatusPrinter.printInCaseOfErrorsOrWarnings(context);

    }

}
