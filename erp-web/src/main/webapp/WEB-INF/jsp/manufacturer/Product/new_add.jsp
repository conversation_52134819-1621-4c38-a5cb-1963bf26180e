<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>
        <c:if test="${null ne manufacturer.manufacturerId }">
            编辑生产企业信息
        </c:if>
        <c:if test="${null eq manufacturer.manufacturerId }">
            新增生产企业信息
        </c:if>
    </title>
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/new/css/common/global.css">
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/new/css/pages/firstengage/first/add.css?rnd=${resourceVersionKey}">
    <link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">
</head>

<body>
<form action="${pageContext.request.contextPath}/goods/manufacturer/addProductInfo.do" id="form_submit"
      class="J-form" method="POST">
    <input type="hidden" name="formToken" value="${formToken}"/>
    <div class="form-wrap">
        <div class="form-container base-form form-span-8">
            <c:if test="${null ne manufacturer.manufacturerId }">
                <div class="form-title">编辑生产企业信息</div>
            </c:if>
            <c:if test="${null eq manufacturer.manufacturerId }">
                <div class="form-title">新增生产企业信息</div>
            </c:if>
            <!-- 后台报错的区域 -->
            <c:forEach var="error" items="${manufacturer.errors}" varStatus="status">
                <div class="vd-tip tip-red">
                    <i class="vd-tip-icon vd-icon icon-error2"></i>
                    <div class="vd-tip-cnt">${error}</div>
                </div>
            </c:forEach>

            <div style="clear: both"></div>
            <input type="hidden" name="manufacturerId" id="manufacturerId" value="${manufacturer.manufacturerId }">
            <div class="form-block">
                <div class="form-block-title">生产企业资质</div>
                <div class="form-cnt">


                    <div class="form-item">
                        <div class="form-label">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                            <span class="c-title" data-a="生产企业名称："
                                                                                 data-b="受委托生产的企业名称">生产企业名称：</span>
                        </div>
                        <div class="form-fields" style="margin-left: 20px">

                            <div class="form-fields">
                                <div class="form-col col-6">
                                    <input type="text" name="manufacturerName"
                                           id = "manufacturerName"
                                           class="input-text" maxlength="128"
                                           value="${manufacturer.manufacturerName}" >
                                </div>
                            </div>

                        </div>
                    </div>
                    <c:if test="${null ne manufacturer.manufacturerId }">
                        <div class="form-item">
                            <div class="form-label">
                            <span class="c-title" data-a="审核状态："
                                  data-b="审核状态">审核状态：</span>
                            </div>
                            <div class="form-fields" style="margin-left: 20px">

                                <div class="form-fields">
                                    <div class="form-col col-6">
                                        <c:choose>
                                            <c:when test="${manufacturer.status == 5}">
                                                <span style="color: red; ">待提交审核</span>
                                            </c:when>
                                            <c:when test="${manufacturer.status == 1}">
                                                <span style="color: red; ">审核中</span>
                                            </c:when>
                                            <c:when test="${manufacturer.status == 2}">
                                                <span style="color: red; ">审核不通过</span>
                                            </c:when>
                                            <c:when test="${manufacturer.status == 3}">
                                                审核通过
                                            </c:when>
                                        </c:choose>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </c:if>
                    <c:if test="${null eq manufacturer.manufacturerId }">
                    </c:if>
                    <div class="form-item one yz">
                        <div class="form-label"><span class="c-title" data-a="营业执照："
                                                                                 data-b="受委托生产企业营业执照">营业执照：</span></div>
                        <div class="form-fields">
                            <div class="J-upload"></div>
                            <input type="hidden" class="J-upload-data" value='${yzMapList}'>
                            <div class="form-fields-tip">
                                -仅支持上传JPG PNG JEPG三种格式文件；<br>
                                -每个文件大小不大于5M；<br>
                                -若变更上传格式，请联系研发，变更WMS格式处理方式；<br>
                                <%---横版资质请上传纵向图片。示例如下：<br>--%>
                                <%--<img src="${pageContext.request.contextPath}/static/images/tips1.png"  width="300px">--%>
                            </div>
                            <%--<div class="form-fields-tip">-最多上传5张。</div>--%>
                            <div class="feedback-block" wrapfor="upload1"></div>
                            <div class="feedback-block J-upload-error">
                                <label class="error" for="" style="display: none;"></label>
                            </div>
                        </div>
                    </div>

                    <div class="form-item one yz">
                        <div class="form-label"><span class="c-title" data-a="营业执照发证日期："
                                                                                 data-b="受委托生产企业营业执照发证日期：">营业执照发证日期：</span>
                        </div>
                        <div class="form-fields">
                            <div class="form-col span-6 input-date">
                               <%-- <input type="text" class="input-text J-date J-date-3"
                                       name="bcIssueDate" placeholder="请选择" readonly
                                       value="${manufacturer.bcIssueDate}">--%>
                                   <input type="text" name="bcIssueDate"  class="layui-input" id="bcIssueDate" placeholder="请选择" readonly
                                          value="<fmt:formatDate value="${manufacturer.bcIssueDate}" pattern="yyyy-MM-dd"/>">
                            </div>
                        </div>
                    </div>

                    <div class="form-item one yz">
                        <div class="form-label"><span class="c-title" data-a="营业执照有效期："
                                                                                 data-b="受委托生产企业营业执照有效期：">营业执照有效期：</span>
                        </div>
                        <div class="form-fields">
                            <div class="form-col span-6 input-date">
                                <%--<input type="text" class="input-text J-date J-date-4"  data-min="5"
                                       name="bcStartTime" placeholder="请选择" readonly
                                       value="${manufacturer.bcStartTime}">--%>
                                    <input type="text" name="bcStartTime" class="layui-input" id="bcStartTime" placeholder="请选择" readonly
                                           value="<fmt:formatDate value="${manufacturer.bcStartTime}" pattern="yyyy-MM-dd"/>">
                            </div>
                            -
                            <div class="form-col span-6 input-date">
                  <%--              <input type="text" class="input-text J-date J-date-5" data-max="4"
                                       name="bcEndTime" placeholder="请选择" readonly
                                       value="${manufacturer.bcEndTime}">--%>
                                    <input type="text" name="bcEndTime" class="layui-input" id="bcEndTime" placeholder="请选择" readonly
                                           value="<fmt:formatDate value="${manufacturer.bcEndTime}" pattern="yyyy-MM-dd"/>">
                            </div>
                        </div>
                    </div>


                    <div class="form-item one pl">
                        <div class="form-label">
                            <span class="c-title"
                                                                                 data-a="生产企业生产许可证："
                                                                                 data-b="受委托生产企业生产许可证：">生产企业生产许可证：</span>
                        </div>
                        <div class="form-fields">
                            <div class="J-upload"></div>
                            <input type="hidden" class="J-upload-data" value='${scMapList}'>
                            <div class="form-fields-tip">
                                -仅支持上传JPG PNG JEPG三种格式文件；<br>
                                -每个文件大小不大于5M；<br>
                                -若变更上传格式，请联系研发，变更WMS格式处理方式；<br>
                                <%---横版资质请上传纵向图片。示例如下：<br>--%>
                                <%--<img src="${pageContext.request.contextPath}/static/images/tips1.png"  width="300px">--%>
                            </div>
                            <div class="feedback-block" wrapfor="upload2"></div>
                            <div class="feedback-block J-upload-error">
                                <label class="error" for="" style="display: none;"></label>
                            </div>
                        </div>
                    </div>

                    <div class="form-item one ple">
                        <div class="form-label">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                            <span class="c-title"
                                                                                 data-a="生产企业生产许可证编号："
                                                                                 data-b="受委托生产企业生产许可证编号：">生产企业生产许可证编号：</span>
                        </div>
                        <div class="form-fields">
                            <div class="form-col col-6">
                                <input type="text" name="productCompanyLicence"
                                       class="input-text" valid-max="50"
                                       value="${manufacturer.productCompanyLicence}">
                            </div>
                        </div>
                    </div>


                    <div class="form-item one ple">
                        <div class="form-label"><span class="c-title"
                                                                                 data-a="生产企业生产许可证有效期："
                                                                                 data-b="受委托生产企业生产许可证有效期：">生产企业生产许可证有效期：</span>
                        </div>
                        <div class="form-fields">
                            <div class="form-col span-6 input-date">
                          <%--      <input type="text" class="input-text J-date J-date-6" data-min="7"
                                       name="peStartTime" placeholder="请选择" readonly
                                       value="${manufacturer.peStartTime }">--%>
                                <input type="text" name="peStartTime" class="layui-input" id="peStartTime" placeholder="请选择" readonly
                                       value="<fmt:formatDate value="${manufacturer.peStartTime}" pattern="yyyy-MM-dd"/>">
                            </div>
                            -
                            <div class="form-col span-6 input-date">
                            <%--    <input type="text" class="input-text J-date J-date-7" data-max="6"
                                       name="peEndTime" placeholder="请选择" readonly
                                       value="${manufacturer.peEndTime}">--%>
                                <input type="text" name="peEndTime" class="layui-input" id="peEndTime" placeholder="请选择" readonly
                                       value="<fmt:formatDate value="${manufacturer.peEndTime}" pattern="yyyy-MM-dd"/>">
                            </div>
                        </div>
                    </div>

                    <div class="form-item one pl">
                        <div class="form-label">
                            <span class="c-title"
                                  data-a="第一类医疗器械生产备案凭证："
                                  data-b="第一类医疗器械生产备案凭证：">第一类医疗器械生产备案凭证：</span>
                        </div>
                        <div class="form-fields">
                            <div class="J-upload"></div>
                            <input type="hidden" class="J-upload-data" value='${rcMapList}'>
                            <div class="form-fields-tip">
                                -仅支持上传JPG PNG JEPG三种格式文件；<br>
                                -每个文件大小不大于5M；<br>
                                -若变更上传格式，请联系研发，变更WMS格式处理方式；<br>
                                <%---横版资质请上传纵向图片。示例如下：<br>--%>
                                <%--<img src="${pageContext.request.contextPath}/static/images/tips1.png"  width="300px">--%>
                            </div>
                            <div class="feedback-block" wrapfor="upload2"></div>
                            <div class="feedback-block J-upload-error">
                                <label class="error" for="" style="display: none;"></label>
                            </div>
                        </div>
                    </div>

                    <div class="form-item one ple">
                        <div class="form-label">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                            <span class="c-title"
                                  data-a="第一类医疗器械生产备案凭证编号："
                                  data-b="第一类医疗器械生产备案凭证编号：">第一类医疗器械生产备案凭证编号：</span>
                        </div>
                        <div class="form-fields">
                            <div class="form-col col-6">
                                <input type="text" name="recordCertificateLicence"
                                       class="input-text" valid-max="50"
                                       value="${manufacturer.recordCertificateLicence}">
                            </div>
                        </div>
                    </div>


                    <div class="form-item one ple">
                        <div class="form-label"><span class="c-title"
                                                      data-a="第一类医疗器械生产备案凭证有效期："
                                                      data-b="第一类医疗器械生产备案凭证有效期：">第一类医疗器械生产备案凭证有效期：</span>
                        </div>
                        <div class="form-fields">
                            <div class="form-col span-6 input-date">
                              <%--  <input type="text" class="input-text J-date J-date-6" data-min="7"
                                       name="rcStartTime" placeholder="请选择" readonly
                                       value="${manufacturer.rcStartTime }">--%>
                                  <input type="text" name="rcStartTime" class="layui-input" id="rcStartTime" placeholder="请选择" readonly
                                         value="<fmt:formatDate value="${manufacturer.rcStartTime}" pattern="yyyy-MM-dd"/>">
                            </div>
                            -
                            <div class="form-col span-6 input-date">
                             <%--   <input type="text" class="input-text J-date J-date-7" data-max="6"
                                       name="rcEndTime" placeholder="请选择" readonly
                                       value="${manufacturer.rcEndTime}">--%>
                                 <input type="text" name="rcEndTime" class="layui-input" id="rcEndTime" placeholder="请选择" readonly
                                        value="<fmt:formatDate value="${manufacturer.rcEndTime}" pattern="yyyy-MM-dd"/>">
                            </div>
                        </div>
                    </div>


                    <div class="form-item one cl ">
                        <div class="form-label"><span class="c-title" data-a="生产企业生产产品登记表："
                                                                                 data-b="受委托生产企业生产产品登记表：">生产企业生产产品登记表：</span>
                        </div>
                        <div class="form-fields">
                            <div class="J-upload"></div>
                            <input type="hidden" class="J-upload-data" id="djb_upload" value='${djbMapList}'>
                            <div class="form-fields-tip">
                                -仅支持上传JPG PNG JEPG三种格式文件；<br>
                                -每个文件大小不大于5M；<br>
                                -若变更上传格式，请联系研发，变更WMS格式处理方式；<br>
                                <%---横版资质请上传纵向图片。示例如下：<br>--%>
                                <%--<img src="${pageContext.request.contextPath}/static/images/tips1.png"  width="300px">--%>
                            </div>
                            <div class="feedback-block" wrapfor="upload3"></div>
                            <div class="feedback-block J-upload-error">
                                <label class="error" for="" style="display: none;"></label>
                            </div>
                        </div>
                    </div>

                    <div class="form-item one cl">
                        <div class="form-label"><span class="c-title"
                                                                                 data-a="生产企业生产产品登记表有效期："
                                                                                 data-b="受委托生产企业生产产品登记表有效期：">生产企业生产产品登记表有效期：</span>
                        </div>
                        <div class="form-fields">
                            <div class="form-col span-6 input-date">
                                <%--<input type="text" class="input-text J-date J-date-8" data-min="9"
                                       name="pepStartTime" placeholder="请选择" readonly
                                       value="${manufacturer.pepStartTime }">--%>
                                    <input type="text" name="pepStartTime" class="layui-input" id="pepStartTime" placeholder="请选择" readonly
                                           value="<fmt:formatDate value="${manufacturer.pepStartTime}" pattern="yyyy-MM-dd"/>">
                            </div>
                            -
                            <div class="form-col span-6 input-date">
                                <%--<input type="text" class="input-text J-date J-date-9" data-max="8"
                                       name="pepEndTime" placeholder="请选择" readonly
                                       value="${manufacturer.pepEndTime }">--%>
                                <input type="text" name="pepEndTime" class="layui-input" id="pepEndTime" placeholder="请选择" readonly
                                       value="<fmt:formatDate value="${manufacturer.pepEndTime}" pattern="yyyy-MM-dd"/>">
                            </div>
                        </div>
                    </div>

                </div>
                <div class="form-btn" style="margin-top: 30px">
                    <div class="form-item">
                        <div class="form-fields">
                            <button class="btn btn-blue btn-large" type="submit">保存</button>

                            <a href="javascript:void(0)" onclick="cancel()"
                               class="btn btn-large">取消</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>


<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/pikaday.2.1.0.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/util.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/select.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.validate.js"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/upload.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/lv-select.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/inputSuggest.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/artDialog/2.0.0/artDialog.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/dialogSearch.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/suggestSelect.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/pages/manufacturer/product/new_add.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/pages/goods/commons/goodsStorageCondition.js?rnd=${resourceVersionKey}"></script>
</body>