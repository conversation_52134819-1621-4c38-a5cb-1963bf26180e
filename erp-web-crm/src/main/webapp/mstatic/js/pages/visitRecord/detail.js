void function () {
    new Vue({
        el: '#page-container',
        data: {
            id: '',
            pageLoading: true,
            showNav: 1, // 1:详情页  2:操作记录

            detail: {},
            detailBtns: [], // 操作按钮
            visitCustomerVo: {}, // 相关客户信息
            otherContactList: [], // 其他联系方式
            areaStr: '', // 省市区名称
            // 打卡信息
            visitUserInfo: {},
            tongxingUserList: [],
            cardPicList: [],
            // 相关单据
            businessLeadsInfo: '',
            businessChanceInfo: '',
            // 拜访记录
            recordOtherContactList: [], // 拜访记录·其他联系方式
            communicateList: [], // 沟通记录列表

            // 操作记录
            pageNum: 1,
            pageSize: 20,
            total: 0,
            isEnd: false,
            isLoading: false,
            operationList: [],

            // 按钮弹层
            showBtnDialog: false,
            /* 添加沟通记录弹层 */
            connectValue: '',
            /* 关闭 */
            CloseReasonTypes: [
                { label: '客户临时出差/不在场', value: 1 },
                { label: '客户主动取消预约', value: 2 },
                { label: '客户需求已解决', value: 3 },
                { label: '线索/商机已关闭', value: 4 },
                { label: '个人时间冲突', value: 5 },
                { label: '其他', value: 6 },
            ],
            closeReasonType: '',
            closeReason: '',

            // 进度条数据
            stepList: [],
            stepActive: '',
            stepStatus: '',
            cansubmit: true,
            contactMobileStatus: 0, //客户信息手机号注册状态 0:未判断 1：已注册 2：未注册
            recordContactMobileStatus: 0, //拜访记录手机号注册状态
        },
        async created() {
            this.getDetailInfo();
            GLOBAL.wxregister(['openEnterpriseChat', 'openLocation', 'previewImage']);
        },
        mounted () {
            window.addEventListener('pageshow', function(event) {
                // 返回此页后·刷新页面
                if (window.localStorage.getItem('crm_visit_detail_refresh') == 1) {
                    window.localStorage.removeItem('crm_visit_detail_refresh');
                    window.location.reload();
                }
            });
        },
        methods: {
            getDetailInfo() {
                this.id = document.getElementById('visitRecordId').value || '';

                this.$axios.get('/crm/visitrecord/profile/detail?id=' + this.id).then(({ data }) => {
                    if (data.success) {
                        this.pageLoading = false;
                        this.detail = data.data || {};
                        this.areaStr = `${this.detail.provinceName||''}${this.detail.cityName||''}${this.detail.areaName||''}`;
                        console.log('detail::', this.detail);
                        this.visitCustomerVo = this.detail.visitCustomerVo || {};
                        this.communicateList = this.detail.communicateList || [];

                        // 其他联系方式
                        if (this.detail.otherContact) {
                            this.getOtherContact(1, this.detail.otherContact);
                        }
                        if (this.detail.recordOtherContact) {
                            this.getOtherContact(2, this.detail.recordOtherContact);
                        }

                        this.businessLeadsInfo = data.data.businessLeadsForVisitDto;
                        this.businessChanceInfo = data.data.businessChanceForVisitDto;

                        if (this.detail.cardList && this.detail.cardList.length) {
                            let tongxingUser = [];
                            this.detail.cardList.forEach(item => {
                                if (item.visitUserId == this.detail.visitorId) {
                                    this.visitUserInfo = item || {};
                                } else {
                                    tongxingUser.push(item);
                                }
                                if (item.cardPhotoUrls) {
                                    this.cardPicList = this.cardPicList.concat(item.cardPhotoUrls.split(','))
                                }
                            })
                            this.tongxingUserList = tongxingUser;
                            console.log('-------------------- this.visitUserInfo -----------------', this.visitUserInfo);
                            console.log('-------------------- this.tongxingUserList -----------------', this.tongxingUserList);
                        }

                        this.getStepList();

                        // 手机号注册状态校验
                        if (this.detail.contactMobile) {
                            this.checkTelStatus(1, this.detail.contactMobile)
                        }
                        if (this.detail.recordContactMobile) {
                            this.checkTelStatus(2, this.detail.recordContactMobile)
                        }

                        this.checkBtnStatus(); // 操作按钮状态
                    } else {
                        this.$message({message: data.message});
                    }
                })

                this.getList();
            },
            // 校验页面按钮权限
            checkBtnStatus() {
                let visitDetailButtonDto = this.detail.visitDetailButtonDto || {};
                let allBtns = [
                    { id: 'addCardBtn', txt: '打卡' },
                    { id: 'addVisitRecordBtn', txt: '添加拜访记录' }, // 添加拜访记录
                    { id: 'addCommuncateRecordBtn', txt: '添加沟通记录'}, // 添加沟通记录
                    { id: 'editPlanBtn', txt: '编辑计划' }, // 编辑计划
                    { id: 'editCardBtn', txt: '编辑打卡信息' },
                    { id: 'createNextVisitBtn', txt: '创建下次拜访' }, // 创建下次拜访
                    { id: 'createBusinessChanceBtn', txt: '创建商机' }, // 创建商机
                    { id: 'closeVisitBtn', txt: '关闭', class: 'red' }, // 关闭
                ];
                function delItem (name) {
                    let btn = allBtns.filter(item => item.id == name);
                    if (btn.length) {
                        let index = allBtns.indexOf(btn[0]);
                        allBtns.splice(index, 1);
                    }
                }

                if (!visitDetailButtonDto.addCardBtn) {
                    delItem('addCardBtn');
                }
                if (!visitDetailButtonDto.editCardBtn) {
                    delItem('editCardBtn');
                }
                if (!visitDetailButtonDto.addVisitRecordBtn) {
                    delItem('addVisitRecordBtn');
                }
                if (!visitDetailButtonDto.createBusinessChanceBtn) {
                    delItem('createBusinessChanceBtn');
                }
                if (!visitDetailButtonDto.addCommuncateRecordBtn) {
                    delItem('addCommuncateRecordBtn');
                }
                if (!visitDetailButtonDto.editPlanBtn) {
                    delItem('editPlanBtn');
                }
                if (!visitDetailButtonDto.createNextVisitBtn) {
                    delItem('createNextVisitBtn');
                }
                if (!visitDetailButtonDto.closeVisitBtn) {
                    delItem('closeVisitBtn');
                }

                this.detailBtns = allBtns;
                console.log('detailBtns:', visitDetailButtonDto, this.detailBtns);
            },
            // 操作按钮
            dealBtns(item) {
                // 打卡 / 编辑打卡
                if (item.id == 'addCardBtn' || item.id == 'editCardBtn') {
                    this.clockIn();
                }
                // 添加拜访记录
                else if (item.id == 'addVisitRecordBtn') {
                    this.toVisitAdd();
                }
                // 编辑计划
                else if (item.id == 'editPlanBtn') {
                    this.editPlan();
                }
                // 创建下次拜访
                else if (item.id == 'createNextVisitBtn') {
                    this.addNextPlan();
                }
                // 创建商机
                else if (item.id == 'createBusinessChanceBtn') {
                    this.addBusinessChance();
                }
                // 添加沟通记录
                else if (item.id == 'addCommuncateRecordBtn') {
                    this.showConnectDialog();
                }
                // 关闭
                else if (item.id == 'closeVisitBtn') {
                    this.showCloseDialog();
                }
            },

            // 操作记录
            getList () {
                console.log('获取第'+ this.pageNum +'页');
                this.$axios.post('/crm/visitrecord/m/logs', {
                    pageNum: this.pageNum,
                    pageSize: this.pageSize,
                    param: this.id
                }).then(({ data }) => {
                    if (data.success) {
                        console.log('data:', data);
                        let total = data.data && data.data.total || 0;
                        let list = data.data && data.data.list || [];
                        if (this.pageNum == 1) {
                            this.operationList = list;
                            this.total = total;
                        } else {
                            this.operationList = this.operationList.concat(list);
                        }
                        console.log('操作记录：', this.operationList);
                        this.isEnd = (this.operationList.length >= this.total) || (list.length < this.pageSize);
                    } else {
                        this.$message({message: data.message});
                    }
                })
            },
            bodyScroll () {
                if (this.showNav == 1) return;
                let scrollTop = document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop; // 滑出屏幕之外的高度
                let scrollH = document.documentElement.scrollHeight || document.body.scrollHeight; // 文档总高度
                let screenH = document.documentElement.clientHeight || document.body.clientHeight; // 屏幕可视高度
                if (scrollH - scrollTop - screenH < 200 && !this.isLoading && !this.isEnd) {
                    console.log('请求下一页');
                    this.isLoading = true;
                    this.pageNum++;
                    this.getList();
                }
            },
            changeNav(index) {
                if (index == 1) {
                    // 清空滑动事件
                    let scrollEle = document.getElementById('operation');
                    scrollEle.removeEventListener('scroll', this.bodyScroll);
                } else if (index == 2) {
                    // 绑定滑动
                    let scrollEle = document.getElementById('operation');
                    scrollEle && scrollEle.addEventListener('scroll', this.bodyScroll);
                }
                this.showNav = index;
            },


            // 其他联系方式回显
            getOtherContact (type, contact) {
                let items = contact.split('##');
                let arr = [];
                items.forEach((item)=> {
                    let label = item.split(':')[0];
                    let value = item.split(':')[1];
                    arr.push({label, value})
                })

                if (type == 1) {
                    this.otherContactList = arr;
                } else if (type == 2) {
                    this.recordOtherContactList = arr;
                }
            },
            getStepList() {
                let list = [{
                    label: '创建计划',
                    id: 1
                }]

                let status = this.detail.visitRecordStatus;

                if (status != 4) {
                    list = list.concat([{
                        label: '上门打卡',
                        id: 2
                    }, {
                        label: '编写拜访记录',
                        id: 3
                    }, {
                        label: '拜访完成',
                        id: 3
                    },])

                    if (status == 3) {
                        this.stepStatus = 'finish';
                    }
                    if (status == 1) {
                        this.stepStatus = 'wait';
                    } 
                } else {
                    this.stepStatus = 'close';

                    if (this.detail.cardList && this.detail.cardList.length) {
                        list.push({
                            label: '上门打卡',
                            id: 2
                        })
                    }

                    list.push({
                        label: '已关闭',
                        id: 4
                    })
                }

                this.stepList = list;
                this.stepActive = status;
            },
            checkTelStatus(type, mobile) {
                this.$axios.post('/crm/visitrecord/profile/checkMobileExists?mobile=' + mobile).then(({ data }) => {
                    if (data.success) {
                        if (type == 1) {
                            this.contactMobileStatus = data.data ? 1 : 2;
                        } else {
                            this.recordContactMobileStatus = data.data ? 1 : 2;
                        }
                    }
                })
            },

            // 查看大图
            previewImg (index) {
                ww.previewImage({
                    current: this.cardPicList[index],
                    urls: this.cardPicList
                });
            },

            // 打开按钮弹层
            openBtnDialog () {
                this.showBtnDialog = true;
                this.$refs.btnsDialog.show();
            },
            hidnBtnDialog () {
                this.showBtnDialog = false;
                this.$refs.btnsDialog.hide();
            },

            /* 添加拜访记录 */
            toVisitAdd () {
                if (this.detail.visitRecordStatus == 1) {
                    this.$dialog({
                        type: 'warn',
                        message: '请完成打卡之后再进行记录填写',
                        buttons: [ {
                            txt: '我知道了',
                            btnClass: 'confirm'
                        }]
                    })
                    return;
                }
                this.hidnBtnDialog();
                window.location.href = '/crm/visitRecord/m/addVisit?visitRecordId=' + this.id;
            },

            /* 沟通记录弹层 */
            showConnectDialog() {
                this.hidnBtnDialog();
                this.connectValue = "";
                this.$refs.connectDialog.show();
            },
            hideConnectDialog() {
                this.connectValue = "";
                this.$refs.connectDialog.hide();
            },
            addConnectRecord() {
                if (!this.connectValue) {
                    this.$message({
                        type: 'error',
                        message: '请输入沟通记录'
                    })
                    return;
                }

                if (!this.cansubmit) return;

                this.cansubmit = false;
                this.$axios.post('/crm/visitrecord/m/addCommunicateRecord', {
                    recordId: this.id,
                    communicateContent: this.connectValue
                }).then(({ data }) => {
                    if (data.success) {
                        this.hideConnectDialog();
                        this.$message({
                            type: 'success',
                            message: '添加成功'
                        });

                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        this.cansubmit = true;
                        this.$message({message: data.message});
                    }
                })
            },


            /* 关闭计划 */
            showCloseDialog() {
                this.hidnBtnDialog();
                if (this.detail.visitRecordStatus == 2) {
                    this.$message.warn('当前状态为拜访中，如特殊原因无法完成拜访，可在拜访记录中进行详细描述');
                    return;
                }
                this.$refs.closeDialog.show();
            },
            hideCloseDialog () {
                this.closeReasonType = "";
                this.closeReason = "";
                this.$refs.closeDialog.hide();
            },
            submitClose() {
                if (!this.closeReasonType) {
                    this.$message({message: '请选择关闭原因'});
                    return;
                }
                if (!this.cansubmit) return;

                this.cansubmit = false;
                this.$axios.post('/crm/visitrecord/m/close', {
                    id: this.id,
                    closeReasonType: this.closeReasonType,
                    closeReasonContent: this.closeReason,
                }).then(({ data }) => {
                    if (data.success) {
                        window.location.reload();
                    } else {
                        this.cansubmit = true;
                        this.$message({message: data.message});
                    }
                    this.hideCloseDialog();
                }).catch(err=> {
                    this.hideCloseDialog();
                })
            },


            /* 编辑计划 */
            editPlan() {
                this.hidnBtnDialog();
                window.location.href = "/crm/visitRecord/m/add?id=" + this.id;
            },
            /* 创建下次拜访 */
            addNextPlan() {
                this.hidnBtnDialog();
                window.location.href = "/crm/visitRecord/m/add?copy=" + this.id;
            },
            /* 创建商机 */
            addBusinessChance() {
                this.hidnBtnDialog();
                window.location.href = "/crm/visitRecord/m/createBusinessChance?visitRecordId=" + this.id;
            },
            /* 打卡 */
            clockIn () {
                window.location.href = "/crm/visitRecord/m/card?id=" + this.id;
            },
            /* 打开企微聊天框 */
            openChat (userid, qwUserid) {
                if (qwUserid) {
                    GLOBAL.openQwChat(this, qwUserid, true);
                } else {
                    GLOBAL.openQwChat(this, userid, false);
                }
            },

            onCopy (txt) {
                GLOBAL.copyTextToClipboard(txt, this, 3000);
            },
            gotoList() {
                window.location.href = './index';
            }
        }
    })
}.call(this);