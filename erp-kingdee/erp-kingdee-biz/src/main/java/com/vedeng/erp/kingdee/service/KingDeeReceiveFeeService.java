package com.vedeng.erp.kingdee.service;

import com.vedeng.infrastructure.kingdee.service.KingDeeBaseService;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveFeeDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeReceiveQueryResultDto;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 销售费用应收单
 * @Date 2023/3/7 15:19
 */
public interface KingDeeReceiveFeeService extends KingDeeBaseService<KingDeeReceiveFeeDto> {

    /**
     * 根据ERP发票id获取金蝶销售费用应收单
     *
     * @param invoiceId 发票id
     * @return List<KingDeeReceiveQueryResultDto>
     */
    List<KingDeeReceiveQueryResultDto> getKingDeeReceiveFee(String invoiceId);

}
