package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.*;
import com.vedeng.erp.kingdee.dto.KingDeeSaleSettlementAdjustmentDto;
import com.vedeng.erp.kingdee.dto.KingDeeSaleSettlementAdjustmentEntityDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeSaleOutStockQueryResultDto;
import com.vedeng.erp.kingdee.service.KingDeeSaleOutStockService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 销售售后调整单推送金蝶（逆向）
 * @author: Suqin
 * @date: 2023/3/3 9:58
 **/
@Slf4j
@Service
public class BatchAfterSaleSettlementAdjustmentPushProcessor implements ItemProcessor<BatchWarehouseGoodsOutInDto, BatchSaleSettlementAdjustmentAndItemDto> {
    @Autowired
    BatchSaleorderGoodsDtoMapper batchSaleorderGoodsDtoMapper;

    @Autowired
    BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;

    @Autowired
    BatchSaleorderDtoMapper batchSaleorderDtoMapper;

    @Autowired
    BatchSaleSettlementAdjustmentDtoMapper batchSaleSettlementAdjustmentDtoMapper;

    @Autowired
    BatchAfterSalesDtoMapper batchAfterSalesDtoMapper;

    @Autowired
    BatchSaleSettlementAdjustmentItemDtoMapper batchSaleSettlementAdjustmentItemDtoMapper;

    @Autowired
    KingDeeSaleOutStockService kingDeeSaleOutStockService;

    @Autowired
    KingDeeBaseApi kingDeeBaseApi;

    private static final String AFTER_SALES_BACK = "销售退货";

    @Override
    public BatchSaleSettlementAdjustmentAndItemDto process(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto) throws Exception {
        log.info("售后调整单根据出库单推送金蝶处理数据{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));
        Integer saleSettlementAdjustmentId = batchWarehouseGoodsOutInDto.getSaleSettlementAdjustmentId();
        BatchSaleSettlementAdjustmentDto afterSaleAdjustment = batchSaleSettlementAdjustmentDtoMapper.selectByPrimaryKey(saleSettlementAdjustmentId);
        if (Objects.isNull(afterSaleAdjustment)) {
            log.info("出库单对应的售后单未生成逆向调整单，出库单id={}", batchWarehouseGoodsOutInDto.getWarehouseGoodsOutInId());
            return null;
        }
        // 售后订单
        BatchAfterSalesDto afterSales = batchAfterSalesDtoMapper.findByAfterSalesId(afterSaleAdjustment.getAfterSaleId());
        List<KingDeeSaleOutStockQueryResultDto> queryOut = kingDeeSaleOutStockService.getKingDeeSaleOutStock(batchWarehouseGoodsOutInDto.getOutInNo());
        log.info("销售订单生成正向调整单，查询金蝶出库单结果：{}", queryOut);
        if (CollUtil.isEmpty(queryOut)) {
            log.error("销售订单生成正向调整单，未查询到金蝶出库单，fBillNo={}", batchWarehouseGoodsOutInDto.getOutInNo());
            throw new KingDeeException("调销售订单生成正向调整单，未查询到金蝶出库单");
        }
        // 金蝶出库单id
        KingDeeSaleOutStockQueryResultDto result = queryOut.get(0);
        // 金蝶校验
        KingDeeSaleSettlementAdjustmentDto query = new KingDeeSaleSettlementAdjustmentDto();
        // 单据id = “调整单id-出库单id”
        String fQzokBddjtid = afterSaleAdjustment.getSaleSettlementAdjustmentId() + "-" + batchWarehouseGoodsOutInDto.getWarehouseGoodsOutInId();
        query.setFQzokBddjtid(fQzokBddjtid);
        Boolean exist = kingDeeBaseApi.isExist(query);
        if (exist) {
            log.info("已存在金蝶调整单，F_QZOK_BDDJTID={}", afterSaleAdjustment.getSaleSettlementAdjustmentId() + batchWarehouseGoodsOutInDto.getWarehouseGoodsOutInId());
            return null;
        }
        // 出库单明细
        List<BatchWarehouseGoodsOutInItemDto> outItems = batchWarehouseGoodsOutInItemDtoMapper.findByOutInNo(batchWarehouseGoodsOutInDto.getOutInNo());
        // 调整单明细
        List<BatchSaleSettlementAdjustmentItemDto> adjustItems = batchSaleSettlementAdjustmentItemDtoMapper.findByAdjustmentId(afterSaleAdjustment.getSaleSettlementAdjustmentId());
        if (CollUtil.isEmpty(adjustItems)) {
            log.error("调整单无对应的调整单明细：{}", JSON.toJSONString(afterSaleAdjustment));
            throw new KingDeeException("调整单无对应的调整单明细");
        }
        // 封装推送数据
        KingDeeSaleSettlementAdjustmentDto kingDeeSaleSettlementAdjustmentDto = new KingDeeSaleSettlementAdjustmentDto();
        kingDeeSaleSettlementAdjustmentDto.setFid(0);
        kingDeeSaleSettlementAdjustmentDto.setFQzokBddjtid(fQzokBddjtid);
        kingDeeSaleSettlementAdjustmentDto.setFQzokJdxsckdid(Integer.valueOf(result.getFID()));
        kingDeeSaleSettlementAdjustmentDto.setFQzokDate(DateUtil.formatDateTime(new Date(afterSales.getValidTime())));
        // 税率
        BatchSaleorderDto saleorder = batchSaleorderDtoMapper.selectBySaleorderNo(batchWarehouseGoodsOutInDto.getRelateNo());
        BigDecimal rate = new BigDecimal(batchSaleorderDtoMapper.getTaxRateByInvoiceType(saleorder.getInvoiceType()));
        ArrayList<KingDeeSaleSettlementAdjustmentEntityDto> entities = new ArrayList<>();
        Map<Integer, BatchSaleSettlementAdjustmentItemDto> adjustmentItemDtoMap = adjustItems.stream().collect(Collectors.toMap(BatchSaleSettlementAdjustmentItemDto::getSaleorderGoodsId, m -> m, (k1, k2) -> k1));
        BigDecimal differenceAmount = afterSaleAdjustment.getIsPushed() ? BigDecimal.ZERO : afterSaleAdjustment.getDifferenceAmount();
        boolean isFirstItem = true;
        for (BatchWarehouseGoodsOutInItemDto outItem : outItems) {
            BatchSaleSettlementAdjustmentItemDto adjustmentItemDto = adjustmentItemDtoMap.get(outItem.getRelatedId());
            if (Objects.nonNull(adjustmentItemDto)) {
                KingDeeSaleSettlementAdjustmentEntityDto entity = null;
                // 计算金额（第一个明细需要扣除尾差）
                if (isFirstItem) {
                    entity = adjustPrice(adjustmentItemDto.getAdjustmentAmount(), outItem.getNum(), rate, differenceAmount);
                    isFirstItem = false;
                } else {
                    entity = adjustPrice(adjustmentItemDto.getAdjustmentAmount(), outItem.getNum(), rate, BigDecimal.ZERO);
                }
                entity.setFQzokYsddh(afterSaleAdjustment.getSaleorderNo());
                entity.setFQzokGsywdh(afterSaleAdjustment.getAfterSaleNo());
                entity.setFGzokYwlx(AFTER_SALES_BACK);
                entity.setFQzokSpdm(adjustmentItemDto.getSku());
                entity.setFQzokSpmc(adjustmentItemDto.getSkuName());
                entity.setFQzokPch(outItem.getBatchNumber());
                entity.setFQzokSn(outItem.getBarcodeFactory());
                entity.setFQzokJdckdentryid(getFEntityId(outItem, queryOut));
                entity.setFQzokBddjhid(adjustmentItemDto.getSaleSettlementAdjustmentItemId());
                entities.add(entity);
            }
        }
        kingDeeSaleSettlementAdjustmentDto.setFEntityList(entities);
        BatchSaleSettlementAdjustmentAndItemDto batchSaleSettlementAdjustmentAndItemDto = new BatchSaleSettlementAdjustmentAndItemDto();
        batchSaleSettlementAdjustmentAndItemDto.setKingDeeSaleSettlementAdjustmentDto(kingDeeSaleSettlementAdjustmentDto);
        BatchSaleSettlementAdjustmentDto update = new BatchSaleSettlementAdjustmentDto();
        update.setSaleSettlementAdjustmentId(afterSaleAdjustment.getSaleSettlementAdjustmentId());
        update.setIsPushed(true);
        batchSaleSettlementAdjustmentAndItemDto.setBatchSaleSettlementAdjustmentDto(update);
        return batchSaleSettlementAdjustmentAndItemDto;
    }

    /**
     * 返回计算调整价后的entity
     *
     * @param adjustmentAmount
     * @param num
     * @param rate
     * @return
     */
    private KingDeeSaleSettlementAdjustmentEntityDto adjustPrice(BigDecimal adjustmentAmount, BigDecimal num, BigDecimal rate, BigDecimal differenceAmount) {
        // 调整价税合计：含税调整金额（即含税调整单价*入库该行的数量）- 尾差
        BigDecimal tzjshj = adjustmentAmount.multiply(num.abs()).setScale(2, RoundingMode.HALF_UP).add(differenceAmount.setScale(2, RoundingMode.HALF_UP));
        // 调整金额（不含税）：调整价税合计/(1+ 税率)
        BigDecimal tzje = tzjshj.divide(rate.add(BigDecimal.ONE), 2, RoundingMode.HALF_UP);
        // 调整税额：调整价税合计- 调整金额
        BigDecimal tzse = tzjshj.subtract(tzje);

        KingDeeSaleSettlementAdjustmentEntityDto entity = new KingDeeSaleSettlementAdjustmentEntityDto();
        entity.setFQzokTzje(tzje);
        entity.setFQzokTzse(tzse);
        entity.setFQzokTzjshj(tzjshj);
        return entity;
    }

    /**
     * 获取金蝶出库单行id
     *
     * @param queryOut
     * @return
     */
    private Integer getFEntityId(BatchWarehouseGoodsOutInItemDto outItem, List<KingDeeSaleOutStockQueryResultDto> queryOut) {
        KingDeeSaleOutStockQueryResultDto kingDeeSaleOutStockQueryResultDto = queryOut.stream().filter(b -> b.getF_QZOK_BDDJHID().equals(outItem.getWarehouseGoodsOutInDetailId().toString())).findFirst().orElse(null);
        if (Objects.isNull(kingDeeSaleOutStockQueryResultDto)) {
            log.error("售后生成调整单，出库单明细未找到对应金蝶出库单明细：{}", JSON.toJSONString(outItem));
            throw new KingDeeException("售后生成调整单，出库单明细未找到对应金蝶出库单明细");
        }
        return Integer.valueOf(kingDeeSaleOutStockQueryResultDto.getFEntity_FENTRYID());
    }
}
