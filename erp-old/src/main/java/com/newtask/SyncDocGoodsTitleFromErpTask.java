package com.newtask;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.docSync.dao.DocSyncMapper;
import com.vedeng.docSync.model.pojo.DocOfGoodsExtDo;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.dao.CoreSpuMapper;
import com.vedeng.goods.model.CoreSku;
import com.vedeng.goods.model.CoreSpu;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @program: erp.vedeng.com
 * @description: 抓取所有资料库商品资料 通过spu sku去查询erp更新对应doc_title
 * @author: Pusan
 * @create: 2021-08-25 10:56
 **/
@Slf4j
@JobHandler(value = "syncDocGoodsTitleFromErpTask")
@Component
public class SyncDocGoodsTitleFromErpTask extends AbstractJobHandler {

    @Autowired
    private DocSyncMapper docSyncMapper;

    @Autowired
    private CoreSkuMapper coreSkuMapper;

    @Autowired
    private CoreSpuMapper coreSpuMapper;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        log.info("同步资料库商品资料标题名称 - 标识开始,时间:{}",param);
        XxlJobLogger.log("同步资料库商品资料标题名称 - 标识开始,时间:{}",param);

        // 首先获取doc所有商品资料
        List<DocOfGoodsExtDo> docOfGoodsExtDoList = docSyncMapper.listDocOfGoods();
        List<CoreSku> coreSkuList = coreSkuMapper.listSku();
        List<CoreSpu> coreSpuList = coreSpuMapper.listSpu();
        decideDocType(docOfGoodsExtDoList);

        AtomicInteger handleCount = new AtomicInteger(0);
        docOfGoodsExtDoList.parallelStream().forEach(item->{

            // 资料既不是SPU也不是SKU
            if(!Integer.valueOf(0).equals(item.getDocType()) && !Integer.valueOf(1).equals(item.getDocType())){
                return;
            }
            handleSyncDocGoodsTitle(item,coreSkuList,coreSpuList,handleCount);
        });

        log.info("同步资料库商品资料标题名称 - 处理数:{}",handleCount.get());
        XxlJobLogger.log("同步资料库商品资料标题名称 - 处理数:{}",handleCount.get());
        log.info("同步资料库商品资料标题名称 - 总待处理数:{}",docOfGoodsExtDoList.size());
        XxlJobLogger.log("同步资料库商品资料标题名称 - 总待处理数:{}",docOfGoodsExtDoList.size());

        log.info("同步资料库商品资料标题名称 - 标识结束,时间:{}",param);
        XxlJobLogger.log("同步资料库商品资料标题名称 - 标识结束,时间:{}",param);

        return SUCCESS;
    }

    /**
     * 针对每个资料库商品资料操作
     * @param docOfGoodsExtDo
     * @param handleCount
     */
    private void handleSyncDocGoodsTitle(DocOfGoodsExtDo docOfGoodsExtDo, List<CoreSku> coreSkuList, List<CoreSpu> coreSpuList, AtomicInteger handleCount) {
        // SKU
        if(Integer.valueOf(1).equals(docOfGoodsExtDo.getDocType())){
            CoreSku coreSku = coreSkuList.stream().filter(item -> docOfGoodsExtDo.getSkuId().equals(item.getSkuId())).findAny().orElse(null);
            if(coreSku == null){
                log.info("同步资料库商品资料标题名称 - 过程中,skuId:{},查询不到该SKU",docOfGoodsExtDo.getSkuId());
                return;
            }
            docOfGoodsExtDo.setDocTitle((coreSku.getSkuName()!=null?coreSku.getSkuName():"")+"-资料");
            docSyncMapper.updateDocGoodsTitle(docOfGoodsExtDo);
            handleCount.incrementAndGet();
            return;
        }
        // SPU
        if(Integer.valueOf(0).equals(docOfGoodsExtDo.getDocType())){
            CoreSpu coreSpu = coreSpuList.stream().filter(item -> docOfGoodsExtDo.getSpuId().equals(item.getSpuId())).findAny().orElse(null);
            if(coreSpu == null){
                log.info("同步资料库商品资料标题名称 - 过程中,spuId:{},查询不到该SPU",docOfGoodsExtDo.getSpuId());
                return;
            }
            docOfGoodsExtDo.setDocTitle((coreSpu.getShowName()!=null?coreSpu.getShowName():"")+"-聚合页");
            docSyncMapper.updateDocGoodsTitle(docOfGoodsExtDo);
            handleCount.incrementAndGet();
        }
    }

    /**
     * 推断资料类型 1 - SKU/ 0 - SPU/ 2 - 自建
     * @param docOfGoodsExtDoList
     */
    private void decideDocType(List<DocOfGoodsExtDo> docOfGoodsExtDoList) {
        docOfGoodsExtDoList.parallelStream().forEach(docOfGoodsExtDo->{
            if(docOfGoodsExtDo.getSkuId() != null && docOfGoodsExtDo.getSpuId() != null){
                docOfGoodsExtDo.setDocType(Integer.valueOf(1));
                return;
            }
            if(docOfGoodsExtDo.getSkuId() == null && docOfGoodsExtDo.getSpuId() != null){
                docOfGoodsExtDo.setDocType(Integer.valueOf(0));
                return;
            }
            if(docOfGoodsExtDo.getSkuId() == null && docOfGoodsExtDo.getSpuId() == null){
                docOfGoodsExtDo.setDocType(Integer.valueOf(2));
            }
        });
    }
}
