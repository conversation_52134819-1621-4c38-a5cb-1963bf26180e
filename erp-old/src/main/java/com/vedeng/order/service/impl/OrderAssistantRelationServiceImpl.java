package com.vedeng.order.service.impl;

import com.vedeng.common.page.Page;
import com.vedeng.common.util.DateUtil;
import com.vedeng.order.dao.OrderAssistantRelationMapper;
import com.vedeng.order.model.OrderAssistantRelationDo;
import com.vedeng.order.model.dto.OrderAssistantRelationDto;
import com.vedeng.order.service.OrderAssistantRelationService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("orderAssistantRelationService")
public class OrderAssistantRelationServiceImpl implements OrderAssistantRelationService {

    @Autowired
    OrderAssistantRelationMapper orderAssistantRelationMapper;

    @Override
    public List<OrderAssistantRelationDo> getBingdedInfoByOrderAssId(Integer orderAssitantUserId) {
        return orderAssistantRelationMapper.getBingdedInfoByOrderAssId(orderAssitantUserId);
    }

    @Override
    public Map<String, Object> getAllBindedInfoByOrderAssIdSelectiveListPage(Integer orderAssitantUserId, Page page) {
        Map<String, Object> queryMap = new HashMap<String, Object>();
        queryMap.put("page", page);
        queryMap.put("orderAssitantUserId", orderAssitantUserId);

        List<OrderAssistantRelationDto> list = orderAssistantRelationMapper.getAllBindedInfoByOrderAssIdSelectiveListPage(queryMap);
        if(CollectionUtils.isNotEmpty(list)){
            for (OrderAssistantRelationDto orderAssistantRelationDto : list) {
                if(orderAssistantRelationDto != null && orderAssistantRelationDto.getAddTime() != null){
                    orderAssistantRelationDto.setAddTimeStr(DateUtil.convertString(orderAssistantRelationDto.getAddTime(),DateUtil.TIME_FORMAT));
                }
            }
        }
        Map<String,Object> resultMap = new HashMap<String,Object>();
        resultMap.put("list", list);
        resultMap.put("page", page);
        return  resultMap;
    }
}
