<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>traderAddressSearch</title> 
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="traderAddressSearch" datasource="erp-datasourcetarget" fixednumber="" fixednumberright="" append_column_url="" append_row_url="" empty_show="" firstcol="radio">
  <div id="appendHead"></div> 
  <div class="layui-fluid"> 
   <div class="layui-tab"> 
    <ul class="layui-tab-title" id="tab"> 
    </ul> 
   </div> 
   <form class="layui-form" id="search">
       <div class=" layui-inline ">
           <label class="layui-form-label">trader_id</label>
           <div class="layui-input-inline">
               <object class=" layui-input list-search-item " type="hidden" name="TRADER_ID" placeholder style alias="" jdbctype data datatype oper validate_rules validate_messages>
               </object>  </div>
       </div>
    <div class=" layui-inline "><label class="layui-form-label">地区</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="REGION" placeholder style alias="concat(R3.REGION_NAME, '/', R2.REGION_NAME, '/', R1.REGION_NAME)" jdbctype data datatype oper validate_rules validate_messages>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">详细地址</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="ADDRESS" placeholder style alias="A" jdbctype data datatype oper validate_rules validate_messages> 
     </div>
    </div>
   </form> 
   <hr class="layui-border-blue"> 
   <div class="btn-group   bd-highlight" id="tableButton">
       <button item_name="ID,REGION,ADDRESS" item_id="ID-REGION-ADDRESS"  url="traderAddressCallBack" opentype="" windowname="" style type="tableselectradio">选择</button>

   </div> 
   <table id="table" class="layui-table" style=" width:100%"> 
    <thead> 
     <tr id="column"> 
      <th type="rowbutton" id="rowbutton"></th> 
      <th item_name="ID" name="ID" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">ID</th>
      <th item_name="REGION" name="REGION" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">地区</th>
      <th item_name="ADDRESS" name="ADDRESS" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">详细地址</th>
     </tr> 
    </thead> 
    <tbody> 
     <tr> 
      <td> express:</td> 
      <td colspan="100"> <pre id="express" class="layui-code" orderby=" ORDER BY
        IS_DEFAULT DESC,
        ADD_TIME DESC " groupby="">StringBuilder sql=new StringBuilder();
TRADER_ID=$$("TRADER_ID");
sql.append("
    SELECT
        A.TRADER_ADDRESS_ID ID,TRADER_ID,
        concat(R3.REGION_NAME,
        '/',
        R2.REGION_NAME,
        '/',
        R1.REGION_NAME) REGION,
        A.ADDRESS 
    FROM
        T_TRADER_ADDRESS A 
    LEFT JOIN
        T_REGION R1 
            ON R1.REGION_ID = A.AREA_ID 
            AND R1.REGION_ID &gt; 100000 
    LEFT JOIN
        T_REGION R2 
            ON R1.PARENT_ID = R2.REGION_ID 
            AND R2.REGION_ID &gt; 100000 
    LEFT JOIN
        T_REGION R3 
            ON R2.PARENT_ID = R3.REGION_ID 
            AND R3.REGION_ID &gt; 100000 
    WHERE
        IS_ENABLE = 1 
        AND TRADER_TYPE = 1 
   ");
if(isBlank("TRADER_ID")){
              sql.append(" AND 1=2 " );
              }
return search(sql);</pre> </td> 
     </tr> 
     <tr> 
      <td> count:</td> 
      <td colspan="100"> <pre id="count" class="layui-code"></pre> </td> 
     </tr> 
    </tbody> 
   </table> 
  </div> 
  <div id="appendFoot"></div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
 </body>
</html>