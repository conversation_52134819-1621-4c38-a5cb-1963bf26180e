package com.pricecenter.controller;

import com.pricecenter.dto.PriceChangeAffectOrderIndexDto;
import com.pricecenter.dto.SkuPriceModifyRecordIndexDto;
import com.pricecenter.service.SkuPriceModifyRecordService;
import com.vedeng.authorization.model.User;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.price.dto.SkuPriceModifyRecordSearchDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Controller
@RequestMapping("/price/skuPriceModifyRecord")
public class SkuPriceModifyRecordController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(SkuPriceModifyRecordController.class);

    @Autowired
    private SkuPriceModifyRecordService skuPriceModifyRecordService;


    /**
     * 调价列表页
     * @Rock
     */
    @ResponseBody
    @RequestMapping(value = "/index",produces = "application/json;charset=UTF-8")
    public ModelAndView index(HttpServletRequest request, SkuPriceModifyRecordSearchDto skuPriceModifyRecordSearchDto,
                              @RequestParam(required = false, defaultValue = "1") Integer fromMenuFlag,
                              @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                              @RequestParam(required = false) Integer pageSize) {
        ModelAndView mav = new ModelAndView("price/priceModifyRecord/priceModifyRecord_list");
        Page page = getPageTag(request, pageNo, pageSize);
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        skuPriceModifyRecordSearchDto.setCurrentUserId(user.getUserId());
        List<SkuPriceModifyRecordIndexDto> skuPriceModifyRecordsList = this.skuPriceModifyRecordService.findByPage(skuPriceModifyRecordSearchDto,page);
        mav.addObject("skuPriceModifyRecordsList",skuPriceModifyRecordsList);
        mav.addObject("skuPriceModifyRecordSearchDto",skuPriceModifyRecordSearchDto);
        mav.addObject("fromMenuFlag",fromMenuFlag);
        mav.addObject("page",page);
        return mav;
    }
    
    /**
     * @Description 打开可处理订单弹框
     * <AUTHOR>
     * @Date 10:28 2021/8/25
     * @Param [request, searchVBByGoodVo, pageNo, pageSize]
     * @return org.springframework.web.servlet.ModelAndView
     **/
    @ResponseBody
    @RequestMapping(value = "/showAffectOrderListView")
    public ModelAndView searchVB(HttpServletRequest request, Integer skuPriceModifyRecordId,
                                 @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                 @RequestParam(required = false, defaultValue = "5") Integer pageSize){
        ModelAndView mav = new ModelAndView("price/priceModifyRecord/affectOrderList");
        mav.addObject("skuPriceModifyRecordId", skuPriceModifyRecordId);
        Page page = getPageTag(request, pageNo, pageSize);
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        List<PriceChangeAffectOrderIndexDto> priceChangeAffectOrderIndexDtoList =
                skuPriceModifyRecordService.getAffectOrderList(skuPriceModifyRecordId,user.getUserId(),page);
        mav.addObject("list", priceChangeAffectOrderIndexDtoList);
        mav.addObject("page", page);
        return mav;
    }
    
    /**
     * @Description 确定价格变更影响订单
     * <AUTHOR>
     * @Date 13:25 2021/8/25
     * @Param [request, followUpDto]
     * @return com.vedeng.common.model.ResultInfo<?>
     **/
    @ResponseBody
    @RequestMapping(value = "/dealAffrctOrder")
    public ResultInfo<?> dealAffrctOrder(HttpServletRequest request, Integer priceChangeAffectOrderId) {
        //User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        //TODO 记录一下处理人
        return skuPriceModifyRecordService.dealAffrctOrder(priceChangeAffectOrderId);
    }

    /**
     * @Description 打开商品信息查询界面判断是否弹窗提示昨日价格变动
     * <AUTHOR>
     * @Date 15:29 2021/8/25
     * @Param [request, priceChangeAffectOrderId]
     * @return com.vedeng.common.model.ResultInfo<?>
     **/
    @ResponseBody
    @RequestMapping(value = "/isNeedNotify")
    public ResultInfo<?> isNeedNotify(HttpServletRequest request) {
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        LOGGER.info("isNeedNotify userId:{}", user.getUserId());
        if(skuPriceModifyRecordService.isNeedNotify(user.getUserId(),user.getPositType()) > 0) {
            LOGGER.info("isNeedNotify userId:{} show dialog", user.getUserId());
            return new ResultInfo<>(1,"弹窗");
        } else{
            return new ResultInfo<>(0,"不弹窗");
        }
    }

    /**
     * @Description 查看调价列表，记录状态
     * <AUTHOR>
     * @Date 9:39 2021/8/26
     * @Param [request]
     * @return com.vedeng.common.model.ResultInfo<?>
     **/
    @ResponseBody
    @RequestMapping(value = "/readPriceChangeRecord")
    public ResultInfo<?> readPriceChangeRecord(HttpServletRequest request) {
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        skuPriceModifyRecordService.read(user.getUserId());
        return new ResultInfo<>(0,"增加阅读记录成功");
    }

}