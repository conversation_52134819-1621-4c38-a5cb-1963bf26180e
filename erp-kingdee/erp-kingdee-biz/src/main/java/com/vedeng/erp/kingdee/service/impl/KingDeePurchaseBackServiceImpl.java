package com.vedeng.erp.kingdee.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeePurchaseBackCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseBackEntity;
import com.vedeng.erp.kingdee.dto.KingDeePurchaseBackDto;
import com.vedeng.erp.kingdee.dto.result.KingDeePurchaseBackQueryResultDto;
import com.vedeng.erp.kingdee.dto.result.KingDeePurchaseReceiptQueryResultDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeePurchaseBackCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeePurchaseBackConvertor;
import com.vedeng.erp.kingdee.repository.KingDeePurchaseBackRepository;
import com.vedeng.erp.kingdee.service.KingDeePurchaseBackApiService;
import com.vedeng.erp.kingdee.service.KingDeePurchaseBackService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryExtParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryFilterDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/3/7 16:11
 **/
@Service
@Slf4j
public class KingDeePurchaseBackServiceImpl extends KingDeeBaseServiceImpl<
        KingDeePurchaseBackEntity,
        KingDeePurchaseBackDto,
        KingDeePurchaseBackCommand,
        KingDeePurchaseBackRepository,
        KingDeePurchaseBackConvertor,
        KingDeePurchaseBackCommandConvertor> implements KingDeePurchaseBackService, KingDeePurchaseBackApiService {

    @Override
    public List<KingDeePurchaseBackQueryResultDto> queryByOutInNo(String outInNo) {
        KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        queryParam.setFormId(KingDeeFormConstant.PURCHASE_BACK);
        List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("fBillNo").value(outInNo).build());
        queryParam.setFilterString(queryFilterDtos);
        log.info("金蝶采购入库单查询入参：{}", JSON.toJSONString(queryParam));
        return kingDeeBaseApi.query(queryParam, KingDeePurchaseBackQueryResultDto.class);
    }
}
