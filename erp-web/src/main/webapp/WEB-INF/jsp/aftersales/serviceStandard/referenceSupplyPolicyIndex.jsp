<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="参考供应商售后政策列表" scope="application" />
<%@ include file="../../common/common.jsp"%>
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/tag.css?rnd=${resourceVersionKey}">
<script type="text/javascript">
    function submit(){
        debugger;
        var supplyPolicyId = $("input[name='supplyPolicyId']:checked").val();
        var copyType = $("input[name='supplyPolicyId']:checked").next().val();
        var policyType = $("input[name='supplyPolicyId']:checked").next().next().val();
        var formerSupplyPolicyId = $("input[name='supplyPolicyId']:checked").next().next().next().val();
        if(supplyPolicyId == undefined){
            layer.alert("请选择要参考的供应商!")
            return;
        }
        var alertStr = '';
        if(copyType == 0){
            alertStr = "贝登";
        }else if(copyType == 1){
            alertStr = "供应商";
        }
        var alterStr = "";
        if(policyType == 2){
            alterStr="提交后该售后政策即会更新保存，若在复制的政策上有细节修改，请点击编辑进入编辑页";
        }else {
            alterStr = "提交后将带入该sku"+alertStr+"的售后服务标准，并覆盖原有信息，确认继续操作吗？";
        }

        index=layer.confirm(alterStr, {
            btn : [ '确定', '取消' ]
            //按钮
        }, function() {
            $.ajax({
                url:page_url+'/aftersale/serviceStandard/copySupplyAfterSalePolicy.do',
                data:{"supplyPolicyId":supplyPolicyId,"skuNo":"${skuNo}","copyType":copyType,"policyType":policyType,"formerSupplyPolicyId":formerSupplyPolicyId},
                type:"POST",
                dataType : "json",
                async: false,
                success:function(data)
                {
                    if(data.code==0){
                        layer.close(index);
                        window.parent.location.reload();
                    }else{
                        layer.alert(data.message);
                    }

                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });
        }, function() {
        });



    }
</script>
<div class="main-container">
    <div class="list-pages-search">
        <form method="post" id="search" action="<%=basePath%>/aftersale/serviceStandard/referenceSupplyAfterSalePolicy.do">
            </br></br>
            <ul>
                <li>
                    <label class="infor_name">商品名称</label>
                    <input type="text" style="width:200px" class="input-small" placeholder="请输入订货号/商品名称/品牌/制造商型号" name="searchInfo" id="searchInfo" value="${searchInfo}"/>
                    <input type="hidden" name="policyType" value="${policyType}">
                    <input type="hidden" name="formerSupplyPolicyId" value="${formerSupplyPolicyId}">
                    <input type="hidden" name="skuNo" value="${skuNo}">
                    <span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();" id="searchSpan">查询</span>
                    <span class="bt-small bg-light-blue bt-bg-style mr20" onclick="reset();">重置</span>
                </li>

            </ul>
        </form>
    </div>
    <div class="normal-list-page">
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th class="wid2">选择</th>
                <th class="wid3">订货号</th>
                <th class="wid6">商品名称</th>
                <th class="wid6">品牌</th>
                <th class="wid6">制造商型号</th>
                <th class="wid4">贝登售后标准</th>
                <th class="wid6">供应商名称</th>
                <th class="wid5">售后服务商</th>
            </tr>
            </thead>
            <tbody>
                <c:forEach var="supplyPolicy" items="${supplyPolicyList}"  varStatus="num">
                    <tr>
                        <td>
                            <input type="radio" name="supplyPolicyId" value="${supplyPolicy.supplyPolicyId}"/>
                            <input type="hidden" name="copyType" id="copyType" value="${supplyPolicy.bdAfterSaleServiceStandard eq '-' ? 1 : 0}">
                            <input type="hidden" name="policyType" id="policyType" value="${policyType}">
                            <input type="hidden" name="formerSupplyPolicyId" id="formerSupplyPolicyId" value="${formerSupplyPolicyId}">
                        </td>
                        <td>${supplyPolicy.skuNo}</td>
                        <td>${supplyPolicy.skuName}</td>
                        <td>${supplyPolicy.brandName}</td>
                        <td>${supplyPolicy.model}</td>
                        <td>${supplyPolicy.bdAfterSaleServiceStandard}</td>
                        <td>${supplyPolicy.traderName}</td>
                        <td>${supplyPolicy.supplyAfterSaleServiceStandard}</td>
                    </tr>
                </c:forEach>
                <c:if test="${empty supplyPolicyList}">
                    <tr>
                        <td colspan='5'>查询无结果！请尝试使用其他搜索条件。</td>
                    </tr>
                </c:if>
            </tbody>
        </table>
    </div>
    <tags:page page="${page}" />
    <table class="table table-bordered table-striped table-condensed table-centered">
        <tr class="add-tijiao tcenter">
            <td colspan="5">
                <button class="dele" type="button" id="close-layer">取消</button>
                <c:if test="${!empty supplyPolicyList}">
                    <button class="bt-bg-style bt-small bg-light-blue" type="button"  onclick="submit();">提交</button>
                </c:if>
            </td>
        </tr>
    </table>
</div>

<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/price/index.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp"%>
