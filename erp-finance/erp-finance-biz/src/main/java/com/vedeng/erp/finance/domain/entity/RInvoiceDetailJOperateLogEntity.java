package com.vedeng.erp.finance.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
    * 发票详情日志表关系
    */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RInvoiceDetailJOperateLogEntity extends BaseEntity {
    private Integer rInvoiceDetailJOperateLogId;

    /**
    * 发票详情ID
    */
    private Integer invoiceDetailId;

    /**
    * 出入库日志ID
    */
    private Integer operateLogId;

    /**
    * 发票ID
    */
    private Integer invoiceId;

    /**
    * 订单详情ID
    */
    private Integer detailGoodsId;

    /**
    * 商品ID
    */
    private Integer goodsId;

    /**
    * 唯一编码
    */
    private String sku;

    /**
    * 关联数量
    */
    private BigDecimal num;

    /**
    * 操作类型 1采购入库 2销售出库 3销售换货入库 4销售换货出库 5销售退货入库 6采购退货出库 7采购换货出库 8采购换货入库 9外借入库 10外借出库  12盘盈入库 13报废出库 14领用出库 16 盘亏出库,
    */
    private Integer operateType;

    /**
    * 是否删除0否1是
    */
    private Integer isDelete;
}