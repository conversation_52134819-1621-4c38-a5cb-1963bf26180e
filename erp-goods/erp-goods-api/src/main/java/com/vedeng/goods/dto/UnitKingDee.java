package com.vedeng.goods.dto;

import com.vedeng.common.core.base.BaseDto;
import lombok.Data;

import java.io.Serializable;

/**
    * 金蝶单位表
    */
@Data
public class UnitKingDee extends BaseDto implements Serializable {
    /**
    * 主键
    */
    private Integer unitKingDeeId;

    /**
    * 金蝶单位编号
    */
    private String unitKingDeeNo;

    /**
    * 金蝶单位名称
    */
    private String unitKingDeeName;

    /**
    * 是否删除 0否 1是
    */
    private Integer isDelete;

    /**
    * 备注
    */
    private String remark;

    /**
    * 更新备注
    */
    private String updateRemark;

    private static final long serialVersionUID = 1L;
}