<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.finance.mapper.BankBillSettlementMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.BankBillSettlementEntity">
    <!--@mbg.generated-->
    <!--@Table T_BANK_BILL_SETTLEMENT-->
    <id column="BANK_BILL_SETTLEMENT_ID" jdbcType="BIGINT" property="bankBillSettlementId" />
    <result column="BANK_BILL_ID" jdbcType="INTEGER" property="bankBillId" />
    <result column="SETTLEMENT_METHOD" jdbcType="INTEGER" property="settlementMethod" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    BANK_BILL_SETTLEMENT_ID, BANK_BILL_ID, SETTLEMENT_METHOD, ADD_TIME, MOD_TIME, CREATOR, 
    UPDATER, CREATOR_NAME, UPDATER_NAME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_BANK_BILL_SETTLEMENT
    where BANK_BILL_SETTLEMENT_ID = #{bankBillSettlementId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_BANK_BILL_SETTLEMENT
    where BANK_BILL_SETTLEMENT_ID = #{bankBillSettlementId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="BANK_BILL_SETTLEMENT_ID" keyProperty="bankBillSettlementId" parameterType="com.vedeng.erp.finance.domain.entity.BankBillSettlementEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BANK_BILL_SETTLEMENT (BANK_BILL_ID, SETTLEMENT_METHOD, ADD_TIME, 
      MOD_TIME, CREATOR, UPDATER, 
      CREATOR_NAME, UPDATER_NAME)
    values (#{bankBillId,jdbcType=INTEGER}, #{settlementMethod,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, 
      #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, 
      #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="BANK_BILL_SETTLEMENT_ID" keyProperty="bankBillSettlementId" parameterType="com.vedeng.erp.finance.domain.entity.BankBillSettlementEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BANK_BILL_SETTLEMENT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bankBillId != null">
        BANK_BILL_ID,
      </if>
      <if test="settlementMethod != null">
        SETTLEMENT_METHOD,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bankBillId != null">
        #{bankBillId,jdbcType=INTEGER},
      </if>
      <if test="settlementMethod != null">
        #{settlementMethod,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.finance.domain.entity.BankBillSettlementEntity">
    <!--@mbg.generated-->
    update T_BANK_BILL_SETTLEMENT
    <set>
      <if test="bankBillId != null">
        BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER},
      </if>
      <if test="settlementMethod != null">
        SETTLEMENT_METHOD = #{settlementMethod,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
    </set>
    where BANK_BILL_SETTLEMENT_ID = #{bankBillSettlementId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.finance.domain.entity.BankBillSettlementEntity">
    <!--@mbg.generated-->
    update T_BANK_BILL_SETTLEMENT
    set BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER},
      SETTLEMENT_METHOD = #{settlementMethod,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR}
    where BANK_BILL_SETTLEMENT_ID = #{bankBillSettlementId,jdbcType=BIGINT}
  </update>

</mapper>