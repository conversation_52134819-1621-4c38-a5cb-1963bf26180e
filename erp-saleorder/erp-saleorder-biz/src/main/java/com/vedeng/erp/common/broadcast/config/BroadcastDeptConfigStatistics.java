package com.vedeng.erp.common.broadcast.config;

import lombok.Data;

/**
 * 播报的目标播报项目配置
 * @ClassName:  BroadcastDeptConfigStatistics   
 * @author: <PERSON>.yang
 * @date:   2025年6月9日 上午11:16:36    
 * @Copyright:
 */
@Data
public class BroadcastDeptConfigStatistics {

	/**一级通知部门ID*/
    private Integer broadcastDeptId;
    
    /**一级通知部门名称*/
    private String broadcastDeptName;

    /**日常标志（0/1）*/
    private Integer dayFlag;

    /**周标志（0/1）*/
    private Integer weekFlag;

    /**月标志（0/1）*/
    private Integer monthFlag;

    /**AED标志（0/1）*/
    private Integer aedFlag;

    /**自有品牌标志（0/1）*/
    private Integer zyFlag;

    /**自定义标志（0/1）*/
    private Integer customFlag;

    /**到款阶梯 单位万元*/
    private Integer amountStep;

    /**通知URL*/
    private String webhook;

}
