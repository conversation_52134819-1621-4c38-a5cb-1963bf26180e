package com.newtask.data;

import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 **/
public abstract  class AbstractDataSync implements DataSyncTemplate {

    public Logger logger = LoggerFactory.getLogger(AbstractDataSync.class);
        public void process(String Time){
            String [] splitTime =  Time.split("-");
            //默认取前10分钟数据
            Long startTime = System.currentTimeMillis()-10*60*1000;
            Long endTime = System.currentTimeMillis();
            if(splitTime.length>=2){
                startTime =Long.parseLong(splitTime[0]) ;
                endTime =Long.parseLong(splitTime[1]) ;
            }
            XxlJobLogger.log(new Date(startTime)+"--"+new Date(endTime));
            List<Integer> list=loadBizId(startTime,endTime);
            if(!CollectionUtils.isNotEmpty(list)){
                return;
            }
            XxlJobLogger.log("当前业务数据量："+list.size());
            List<Map<String,Object>> map=loadBizData(list);
            updateData(map);
        }

    /**
     * @desc 根据销售单id同步销售单信息
     * <AUTHOR>
     * @param saleorderId
     */
    public void process(Integer saleorderId){
            List<Integer> list=new ArrayList<>();
            list.add(saleorderId);
            List<Map<String,Object>> map=loadBizData(list);
            updateData(map);
    }
}
