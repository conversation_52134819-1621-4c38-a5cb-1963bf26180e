package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeePayCommonAndInvoiceDto;
import com.vedeng.erp.kingdee.dto.PurchaseVatPlainInvoiceDto;
import com.vedeng.erp.kingdee.dto.PurchaseVatSpecialInvoiceDto;
import com.vedeng.erp.kingdee.service.KingDeePurchaseInvoicePlainApiService;
import com.vedeng.erp.kingdee.service.KingDeePurchaseInvoiceSpecialApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购仅退票新蓝票推送金蝶
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchPurchaseOnlyNewInvoiceWriter extends BaseWriter<KingDeePayCommonAndInvoiceDto> {

    @Autowired
    private KingDeePurchaseInvoiceSpecialApiService kingDeePurchaseInvoiceSpecialApiService;

    @Autowired
    private KingDeePurchaseInvoicePlainApiService kingDeePurchaseInvoicePlainApiService;

    @Override
    public void doWrite(KingDeePayCommonAndInvoiceDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("采购仅退票新蓝票发票推送：{}", JSON.toJSONString(item));
        Integer invoiceId = 0;
        if (item.getSpecial()) {
            if (Objects.isNull(item.getPurchaseVatSpecialInvoiceDto())) {
                return;
            }
        } else {
            if (Objects.isNull(item.getPurchaseVatPlainInvoiceDto())) {
                return;
            }
        }
        // 推票
        if (item.getSpecial()) {
            // 专票推送
            PurchaseVatSpecialInvoiceDto purchaseVatSpecialInvoiceDto = item.getPurchaseVatSpecialInvoiceDto();
            invoiceId = Integer.valueOf(purchaseVatSpecialInvoiceDto.getFQzokBddjtid());
            specialWrite(purchaseVatSpecialInvoiceDto);
        } else {
            // 普票推送
            PurchaseVatPlainInvoiceDto purchaseVatPlainInvoiceDto = item.getPurchaseVatPlainInvoiceDto();
            invoiceId = Integer.valueOf(purchaseVatPlainInvoiceDto.getFQzokBddjtid());
            plainWrite(purchaseVatPlainInvoiceDto);

        }


    }

    /**
     * 专票
     * @param dto
     */
    private void specialWrite(PurchaseVatSpecialInvoiceDto dto) {
        log.info("采购仅退票新蓝票专票入参-->{}",JSON.toJSON(dto));
        dto.setKingDeeBizEnums(KingDeeBizEnums.savePurchaseInvoiceSpecial);
        kingDeePurchaseInvoiceSpecialApiService.register(dto,true);
    }


    /**
     * 普票
     * @param dto
     */
    private void plainWrite(PurchaseVatPlainInvoiceDto dto) {
        log.info("采购仅退票新蓝票普票入参-->{}",JSON.toJSON(dto));
        dto.setKingDeeBizEnums(KingDeeBizEnums.savePurchaseInvoicePlain);
        kingDeePurchaseInvoicePlainApiService.register(dto,true);
    }

}
