<?xml version="1.0" encoding="UTF-8"?>
<taglib xmlns="http://java.sun.com/xml/ns/j2ee"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee http://java.sun.com/xml/ns/j2ee/web-jsptaglibrary_2_0.xsd" version="2.0">
    <tlib-version>1.0</tlib-version>
    <short-name>myfn</short-name>
    <uri>/WEB-INF/tld/myfn.tld</uri>
    <function><!-- 定义函数 -->
        <name>rounding</name> <!-- 调用名称 -->
         <function-class>com.vedeng.common.util.ElFunction</function-class> <!-- 类全名 -->
         <function-signature>java.lang.String rounding(java.lang.Object)</function-signature>
     </function>

    <function><!-- 定义函数 -->
        <name>toString</name> <!-- 调用名称 -->
        <function-class>com.vedeng.common.util.ElFunction</function-class> <!-- 类全名 -->
        <function-signature>java.lang.String toString(java.math.BigDecimal))</function-signature>
    </function>

    <function><!-- 定义函数 -->
         <name>object2Json</name> <!-- 调用名称 -->
         <function-class>com.vedeng.common.util.ElFunction</function-class> <!-- 类全名 -->
         <function-signature>java.lang.String object2Json(java.lang.Object)</function-signature>
     </function>
</taglib>