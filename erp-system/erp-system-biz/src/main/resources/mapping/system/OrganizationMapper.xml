<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.system.mapper.OrganizationMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.system.domain.entity.OrganizationEntity">
    <!--@mbg.generated-->
    <!--@Table T_ORGANIZATION-->
    <id column="ORG_ID" jdbcType="INTEGER" property="orgId" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="PARENT_ID" jdbcType="INTEGER" property="parentId" />
    <result column="ORG_NAME" jdbcType="VARCHAR" property="orgName" />
    <result column="LEVEL" jdbcType="TINYINT" property="level" />
    <result column="TYPE" jdbcType="INTEGER" property="type" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="IS_DELETED" jdbcType="TINYINT" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ORG_ID, COMPANY_ID, PARENT_ID, ORG_NAME, `LEVEL`, `TYPE`, ADD_TIME, CREATOR, MOD_TIME, 
    UPDATER, IS_DELETED
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_ORGANIZATION
    where ORG_ID = #{orgId,jdbcType=INTEGER}
  </select>
  <select id="getAllOrganizationOnly" parameterType="java.lang.Integer" resultMap="BaseResultMap">
 		SELECT ORG_ID,PARENT_ID,ORG_NAME FROM T_ORGANIZATION WHERE IS_DELETED = 0 and COMPANY_ID = 1;
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_ORGANIZATION
    where ORG_ID = #{orgId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ORG_ID" keyProperty="orgId" parameterType="com.vedeng.erp.system.domain.entity.OrganizationEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_ORGANIZATION (COMPANY_ID, PARENT_ID, ORG_NAME, 
      `LEVEL`, `TYPE`, ADD_TIME, 
      CREATOR, MOD_TIME, UPDATER, 
      IS_DELETED)
    values (#{companyId,jdbcType=INTEGER}, #{parentId,jdbcType=INTEGER}, #{orgName,jdbcType=VARCHAR}, 
      #{level,jdbcType=TINYINT}, #{type,jdbcType=INTEGER}, #{addTime,jdbcType=BIGINT}, 
      #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}, 
      #{isDeleted,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" keyColumn="ORG_ID" keyProperty="orgId" parameterType="com.vedeng.erp.system.domain.entity.OrganizationEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_ORGANIZATION
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="parentId != null">
        PARENT_ID,
      </if>
      <if test="orgName != null">
        ORG_NAME,
      </if>
      <if test="level != null">
        `LEVEL`,
      </if>
      <if test="type != null">
        `TYPE`,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="isDeleted != null">
        IS_DELETED,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=INTEGER},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        #{level,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.system.domain.entity.OrganizationEntity">
    <!--@mbg.generated-->
    update T_ORGANIZATION
    <set>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="parentId != null">
        PARENT_ID = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="orgName != null">
        ORG_NAME = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        `LEVEL` = #{level,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        `TYPE` = #{type,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        IS_DELETED = #{isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    where ORG_ID = #{orgId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.system.domain.entity.OrganizationEntity">
    <!--@mbg.generated-->
    update T_ORGANIZATION
    set COMPANY_ID = #{companyId,jdbcType=INTEGER},
      PARENT_ID = #{parentId,jdbcType=INTEGER},
      ORG_NAME = #{orgName,jdbcType=VARCHAR},
      `LEVEL` = #{level,jdbcType=TINYINT},
      `TYPE` = #{type,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      IS_DELETED = #{isDeleted,jdbcType=TINYINT}
    where ORG_ID = #{orgId,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="ORG_ID" keyProperty="orgId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_ORGANIZATION
    (COMPANY_ID, PARENT_ID, ORG_NAME, `LEVEL`, `TYPE`, ADD_TIME, CREATOR, MOD_TIME, UPDATER, 
      IS_DELETED)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.companyId,jdbcType=INTEGER}, #{item.parentId,jdbcType=INTEGER}, #{item.orgName,jdbcType=VARCHAR}, 
        #{item.level,jdbcType=TINYINT}, #{item.type,jdbcType=INTEGER}, #{item.addTime,jdbcType=BIGINT}, 
        #{item.creator,jdbcType=INTEGER}, #{item.modTime,jdbcType=BIGINT}, #{item.updater,jdbcType=INTEGER}, 
        #{item.isDeleted,jdbcType=TINYINT})
    </foreach>
  </insert>

  <select id="getParentIdById" resultType="java.lang.Integer">
		select  PARENT_ID
		from T_ORGANIZATION
		WHERE ORG_ID = #{orgId,jdbcType=INTEGER}
		AND IS_DELETED = 0
	</select>

  <select id="getParentIdsById" resultType="java.lang.Integer">
    <![CDATA[
    SELECT TORG.ORG_ID
    FROM (
    SELECT @ids                                 AS ids,
    (SELECT @ids := GROUP_CONCAT(PARENT_ID)
    FROM T_ORGANIZATION
    WHERE FIND_IN_SET(ORG_ID, @ids)) AS pids,
    @l := @l + 1                         AS LEVEL
    FROM T_ORGANIZATION,
    (SELECT @ids := #{orgId,jdbcType=INTEGER}, @l := 0) b
    WHERE @ids IS NOT NULL AND @l < 10
    ) ID,
    T_ORGANIZATION TORG
    WHERE FIND_IN_SET(TORG.ORG_ID, ID.ids)
    ORDER BY ID.LEVEL
    ]]>
  </select>

<!--auto generated by MybatisCodeHelper on 2023-09-06-->
  <select id="findByParentId" resultMap="BaseResultMap">
    select
    ORG_ID,ORG_NAME,PARENT_ID
    from T_ORGANIZATION
    where PARENT_ID=#{parentId,jdbcType=INTEGER}
    and IS_DELETED = 0
  </select>

  <select id="findIdByParentId" resultType="java.lang.Integer">
    select
      ORG_ID
    from T_ORGANIZATION
    where PARENT_ID=#{parentId,jdbcType=INTEGER}
      and IS_DELETED = 0
  </select>
  
  <select id="getOrgNameList" resultType="java.lang.String">
    SELECT IFNULL(E.ORG_NAME,'')
    FROM T_USER A
           LEFT JOIN
         T_R_USER_POSIT B
         ON A.USER_ID = B.USER_ID
           LEFT JOIN
         T_POSITION C
         ON B.POSITION_ID = C.POSITION_ID
           LEFT JOIN
         T_ORGANIZATION E
         ON C.ORG_ID = E.ORG_ID
    WHERE A.USER_ID = #{userId,jdbcType=INTEGER}
  </select>
</mapper>
