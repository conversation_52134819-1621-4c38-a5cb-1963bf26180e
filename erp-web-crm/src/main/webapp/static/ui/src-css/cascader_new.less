@import (less) '../common.css';

.ui-cas-new-wrap {
    width: 100%;

    .ui-cas-new-select {
        display: flex;
        align-items: center;
        border: solid 1px #BABFC2;
        border-radius: 3px;
        padding: 3px 10px;
        position: relative;

        &:hover {
            border-color: #969B9E;
        }

        .ui-cas-new-multi-label {
            display: flex;

            .ui-cas-new-multi-label-item {
                display: flex;
                height: 22px;
                padding-left: 5px;
                background: #F5F7FA;
                border: solid 1px #E1E5E8;
                border-radius: 2px;
                font-size: 12px;
                line-height: 19px;
                color: #333;
                margin-right: 5px;

                &.more {
                    padding-right: 5px;
                }

                .icon-delete {
                    font-size: 16px;
                    width: 20px;
                    height: 20px;
                    color: #999;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    &:hover {
                        color: #f60;
                    }
                }
            }
        }

        .ui-cas-new-search-input-wrap {
            max-width: 100%;
            min-width: 25%;
            flex: 1;

            input {
                line-height: 22px;
            }

            .icon-search {
                font-size: 16px;
                width: 40px;
                height: 28px;
                position: absolute;
                right: 0;
                top: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #666;
            }
        }

        .ui-cas-new-search-input {
            border: 0;
            width: 100%;
            font-size: 12px;

            &::placeholder {
                color: #999;
                font-size: 12px;
            }
        }

        .ui-cas-new-clear {
            font-size: 16px;
            width: 40px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            cursor: pointer;
            position: absolute;
            right: 0;
            top: 0;
            display: none;

            &:hover {
                color: #333;
            }
        }

        &.on-select {
            &:hover {
                .ui-cas-new-search-input-wrap .icon-search {
                    display: none;
                }

                .ui-cas-new-clear {
                    display: flex;
                }
            }
        }

        &.open {
            border-color: #09f;

            &.on-select {
                .ui-cas-new-search-input-wrap .icon-search {
                    display: none;
                }
                
                .ui-cas-new-clear {
                    display: flex;
                }
            }
        }
    }

}

.ui-cas-new-drop {
    display: flex;

    .ui-cas-new-option-wrap {
        box-sizing: border-box;
        overflow: auto;
        border: solid 1px #BABFC2;
        border-radius: 3px;
        box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
        background-color: #ffffff;
        padding: 5px 0px;
        list-style-type: none;
        margin: 0;
        width: 200px;
        height: 300px;
        margin-right: -1px;
        .scrollbar;
    }

    .ui-cas-new-option-layout {
        display: flex;
    }

    .ui-cas-option-item {
        width: 100%;
        cursor: pointer;
        padding: 0px 10px;
        padding-right: 36px;
        height: 33px;
        box-sizing: border-box;
        line-height: 33px;
        position: relative;
        display: flex;
        align-items: center;

        &:hover {
            background: #F5F7FA;
        }


        .ui-cas-option-item-checkbox {
            display: flex;
            align-items: center;
        }

        .icon-app-right {
            width: 36px;
            height: 33px;
            position: absolute;
            display: flex;
            align-items: center;
            justify-content: center;
            top: 0;
            right: 0;
            font-size: 16px;
        }

        .ui-cas-option-item-checkbox {
            font-size: 16px;
            margin-right: 5px;
            color: #969B9E;

            &:hover {
                .icon-checkbox1 {
                    color: #09f;
                }
            }

            .icon-checkbox2 {
                color: #09f;
            }

            .icon-deduct {
                font-size: 12px;
                width: 14px;
                height: 14px;
                border-radius: 3px;
                background: #09f;
                color: #fff;
                display: flex;
                align-items: center;
                justify-content: center;
                line-height: 1;
                margin-right: 1px;
                margin-left: 1px;
                margin-top: -1px;
            }
        }

        &.active {
            .ui-cas-option-item-txt {
                color: #09f;
            }
        }
    }
}

.ui-cas-new-search-wrap {
    box-sizing: border-box;
    border: solid 1px #BABFC2;
    border-radius: 3px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
    background-color: #ffffff;
    padding: 5px 0;
    width: 100%;

    .ui-cas-new-search-empty {
        padding: 5px 0;
        text-align: center;
        color: #999;
    }

    .ui-cas-new-search-list {
        max-height: 264px;
        overflow: auto;
        overscroll-behavior: contain;
        .scrollbar;

        .ui-cas-new-search-item {
            display: flex;
            align-items: center;
            height: 33px;
            cursor: pointer;
            padding: 0 10px;

            &:hover {
                background: #f5f7fa;
            }

            .vd-ui_icon {
                font-size: 16px;
                margin-right: 5px;

                &.icon-checkbox1 {
                    color: #666;
                }

                &.icon-checkbox2 {
                    color: #09f;
                }
            }
        }
    }
}