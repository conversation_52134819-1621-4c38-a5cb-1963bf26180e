package com.vedeng.erp.express.service.impl;

import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.buyorder.service.BuyorderApiService;
import com.vedeng.erp.express.dto.ExpressCommunicationBindDto;
import com.vedeng.erp.express.dto.SaleOrderCommunicationDto;
import com.vedeng.erp.express.dto.SaleOrderExpressDto;
import com.vedeng.erp.express.enums.ExpressArrivalStatusEnum;
import com.vedeng.erp.express.service.SaleOrderExpressApiService;
import com.vedeng.erp.saleorder.dao.SaleOrderGoodsDetailMapper;
import com.vedeng.erp.saleorder.dto.SaleOrderGoodsDetailDto;
import com.vedeng.erp.trader.dto.CommunicateRecordApiDto;
import com.vedeng.erp.trader.service.CommunicateRecordApiService;
import com.vedeng.logistics.dao.ExpressMapper;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.ExpressDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SaleOrderExpressApiServiceImpl implements SaleOrderExpressApiService {

    @Autowired
    private ExpressMapper expressMapper;

    @Autowired
    private SaleOrderGoodsDetailMapper saleOrderGoodsDetailMapper;

    @Autowired
    private CommunicateRecordApiService communicateRecordApiService;

    @Autowired
    private BuyorderApiService buyorderApiService;

    /**
     * 绑定沟通记录和签收单
     * @param saleOrderId
     */
    @Override
    public List<SaleOrderExpressDto> querySaleDirectExpress(Integer saleOrderId) {
        List<SaleOrderGoodsDetailDto> goodsDetailByOrderId = saleOrderGoodsDetailMapper.findGoodsDetailByOrderId(saleOrderId);
        List<Integer> saleOrderGoodsIdList = goodsDetailByOrderId.stream().map(SaleOrderGoodsDetailDto::getSaleorderGoodsId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(saleOrderGoodsIdList)){
            return Lists.newArrayList();
        }
        Express express = new Express();
        express.setRelatedIds(saleOrderGoodsIdList);
        // 销售单下的直发采购单的快递单信息
        List<Express> buyExpressList = expressMapper.getBuyExpressList(express);
        List<SaleOrderExpressDto> result = Lists.newArrayList();
        for (Express expressDto : buyExpressList) {
            SaleOrderExpressDto saleOrderExpressDto = new SaleOrderExpressDto();
            saleOrderExpressDto.setExpressId(expressDto.getExpressId());
            saleOrderExpressDto.setLogisticsName(expressDto.getLogisticsName());
            saleOrderExpressDto.setLogisticsNo(expressDto.getLogisticsNo());
            saleOrderExpressDto.setDeliveryTime(DatePattern.NORM_DATE_FORMAT.format(expressDto.getDeliveryTime()));
            saleOrderExpressDto.setArriveStatus(ExpressArrivalStatusEnum.getExpressArrivalNameByCode(expressDto.getArrivalStatus()));
            List<ExpressDetail> expressDetail = expressDto.getExpressDetail();
            String goodsName = expressDetail.stream()
                    .map(e -> e.getGoodName() + " " + e.getNum() + " " + e.getUnitName()).collect(Collectors.joining(","));

            saleOrderExpressDto.setGoodsName(goodsName);
            saleOrderExpressDto.setCommunicateRecordIds(expressDto.getCommunicateRecorderIds());
            result.add(saleOrderExpressDto);
        }
        // 发货时间降序
        result = result.stream().sorted(Comparator.comparing(SaleOrderExpressDto::getDeliveryTime).reversed()).collect(Collectors.toList());
        return result;
    }

    /**
     * 绑定通话记录和签收单
     * @param list
     */
    @Override
    @Transactional
    public void bindCommunicationIdAndSignFor(List<ExpressCommunicationBindDto> list, Integer currentUserId) {
        for (ExpressCommunicationBindDto expressCommunicationBindDto : list) {
            // 绑定通话记录
            bindCommunicationId(expressCommunicationBindDto,currentUserId);
            // 签收
            buyorderApiService.deliveryDirectConfirmArrival(Collections.singletonList(expressCommunicationBindDto.getExpressId()));
        }
    }

    /**
     * 绑定通话记录
     * @param expressCommunicationBindDto
     */
    @Override
    @Transactional
    public void bindCommunicationId(ExpressCommunicationBindDto expressCommunicationBindDto,Integer currentUserId){
        log.info("开始处理：{}",JSONObject.toJSONString(expressCommunicationBindDto));
        Integer expressId = expressCommunicationBindDto.getExpressId();
        // 快递id为空时不更新
        if (Objects.nonNull(expressId)){
            Express express = new Express();
            express.setExpressId(expressId);
            Express dbExpress = expressMapper.getExpressById(express);
            if (Objects.isNull(dbExpress)){
                log.error("未查到快递信息:{}",expressId);
                throw new ServiceException("未查到快递信息");
            }
            String existIds = dbExpress.getCommunicateRecorderIds();
            List<Integer> existList = Arrays.asList(existIds.split(","))
                    .stream()
                    .filter(str -> !str.isEmpty())
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());

            String currentIds = expressCommunicationBindDto.getCommunicateRecorderIds();

            if (StringUtils.isNotBlank(currentIds)){
                List<Integer> currentList = Arrays.asList(currentIds.split(","))
                        .stream()
                        .filter(str -> !str.isEmpty())
                        .map(Integer::parseInt)
                        .distinct()
                        .collect(Collectors.toList());
                List<Integer> error1 = Lists.newArrayList();
                List<Integer> error2 = Lists.newArrayList();
                for (Integer id : currentList) {
                    // 判断是否存在
                    CommunicateRecordApiDto communicateRecord = communicateRecordApiService.getCommunicateRecord(id);
                    if (Objects.isNull(communicateRecord)){
                        log.error("录音ID不存在，id={}",id);
                        error1.add(id);
                    }

                    if (existList.contains(id)){
                        log.error("录音ID已关联当前快递,对应id={}", id);
                        error2.add(id);
                    }
                }
                if (CollectionUtils.isNotEmpty(error1)){
                    String err1 = error1.stream().map(Object::toString).collect(Collectors.joining(","));
                    throw new ServiceException("录音ID不存在："+err1);
                }
                if (CollectionUtils.isNotEmpty(error2)){
                    String err2 = error2.stream().map(Object::toString).collect(Collectors.joining(","));
                    throw new ServiceException("录音ID已关联当前快递："+err2);
                }

                existList.addAll(currentList);
                String collect = existList.stream().map(Object::toString).collect(Collectors.joining(","));
                express.setCommunicateRecorderIds(collect);
                log.info("更新快递：{}", JSONObject.toJSONString(express));
                expressMapper.updateByPrimaryKeySelective(express);
            }
        }

        // 通话备注非必填，也会更新基本信息
        if (StringUtils.isNotBlank(expressCommunicationBindDto.getContents())
                || Objects.nonNull(expressCommunicationBindDto.getEndTime())){
            CommunicateRecordApiDto communicateRecordApiDto = new CommunicateRecordApiDto();
            communicateRecordApiDto.setCoid(expressCommunicationBindDto.getCoid());
            // 通话备注
            communicateRecordApiDto.setContentSuffix(expressCommunicationBindDto.getContents());
            communicateRecordApiDto.setEndtime(expressCommunicationBindDto.getEndTime());
            communicateRecordApiDto.setContact(expressCommunicationBindDto.getContactName());
            communicateRecordApiDto.setTraderId((Objects.isNull(expressCommunicationBindDto.getTraderId()) || expressCommunicationBindDto.getTraderId() == 0) ? null : expressCommunicationBindDto.getTraderId());
            communicateRecordApiDto.setTraderContactId((Objects.isNull(expressCommunicationBindDto.getTraderContactId()) || expressCommunicationBindDto.getTraderContactId() == 0) ? null : expressCommunicationBindDto.getTraderContactId());
            communicateRecordApiDto.setUpdater(currentUserId);
            log.info("更新通话记录：{}",JSONObject.toJSONString(communicateRecordApiDto));
            communicateRecordApiService.updateCommunicateRecord(communicateRecordApiDto);
        }
    }

    /**
     * 获取快递关联的通话记录ID
     * @param expressId
     * @return
     */
    @Override
    public List<Integer> getExpressCommunicationId(Integer expressId) {
        Express express = new Express();
        express.setExpressId(expressId);
        Express dbExpress = expressMapper.getExpressById(express);
        List<Integer> list = Arrays.asList(dbExpress.getCommunicateRecorderIds().split(","))
                .stream()
                .filter(str -> !str.isEmpty())
                .map(Integer::parseInt)
                .collect(Collectors.toList());

        return list;
    }

    /**
     * 获取通话记录
     * @param communicationRecordId
     * @return
     */
    @Override
    public SaleOrderCommunicationDto getSaleOrderCommunication(Integer communicationRecordId) {
        CommunicateRecordApiDto communicateRecord = communicateRecordApiService.getCommunicateRecord(communicationRecordId);
        if (Objects.isNull(communicateRecord)){
            log.error("通话记录不存在，id={}",communicationRecordId);
            throw new ServiceException("通话记录不存在");
        }
        SaleOrderCommunicationDto result = new SaleOrderCommunicationDto();
        result.setBegindate(DatePattern.NORM_DATE_FORMAT.format(communicateRecord.getAddTime()));
        result.setEnddate(DatePattern.NORM_DATE_FORMAT.format(communicateRecord.getAddTime()));
        return result;
    }
}
