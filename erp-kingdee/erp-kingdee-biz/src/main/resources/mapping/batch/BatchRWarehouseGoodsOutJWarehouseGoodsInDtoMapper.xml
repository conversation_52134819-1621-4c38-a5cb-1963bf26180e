<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchRWarehouseGoodsOutJWarehouseGoodsInDto">
    <!--@mbg.generated-->
    <!--@Table T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN-->
    <id column="T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID" jdbcType="BIGINT" property="tRWarehouseGoodsOutJWarehouseGoodsInId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="WAREHOUSE_GOODS_OUT_ID" jdbcType="BIGINT" property="warehouseGoodsOutId" />
    <result column="WAREHOUSE_GOODS_IN_ID" jdbcType="BIGINT" property="warehouseGoodsInId" />
    <result column="WAREHOUSE_GOODS_OUT_ITEM_ID" jdbcType="BIGINT" property="warehouseGoodsOutItemId" />
    <result column="WAREHOUSE_GOODS_IN_ITEM_ID" jdbcType="BIGINT" property="warehouseGoodsInItemId" />
    <result column="RELATION_TYPE" jdbcType="INTEGER" property="relationType" />
    <result column="SKU_ID" jdbcType="INTEGER" property="skuId" />
    <result column="SKU_NO" jdbcType="VARCHAR" property="skuNo" />
    <result column="NUM" jdbcType="DECIMAL" property="num" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER,
    CREATOR_NAME, UPDATER_NAME, WAREHOUSE_GOODS_OUT_ID, WAREHOUSE_GOODS_IN_ID, WAREHOUSE_GOODS_OUT_ITEM_ID,
    WAREHOUSE_GOODS_IN_ITEM_ID, RELATION_TYPE, SKU_ID, SKU_NO, NUM
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN
    where T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN
    where T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID" keyProperty="tRWarehouseGoodsOutJWarehouseGoodsInId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchRWarehouseGoodsOutJWarehouseGoodsInDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN (ADD_TIME, MOD_TIME, CREATOR,
      UPDATER, CREATOR_NAME, UPDATER_NAME,
      WAREHOUSE_GOODS_OUT_ID, WAREHOUSE_GOODS_IN_ID,
      WAREHOUSE_GOODS_OUT_ITEM_ID, WAREHOUSE_GOODS_IN_ITEM_ID,
      RELATION_TYPE, SKU_ID, SKU_NO,
      NUM)
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER},
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR},
      #{warehouseGoodsOutId,jdbcType=BIGINT}, #{warehouseGoodsInId,jdbcType=BIGINT},
      #{warehouseGoodsOutItemId,jdbcType=BIGINT}, #{warehouseGoodsInItemId,jdbcType=BIGINT},
      #{relationType,jdbcType=INTEGER}, #{skuId,jdbcType=INTEGER}, #{skuNo,jdbcType=VARCHAR},
      #{num,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" keyColumn="T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID" keyProperty="tRWarehouseGoodsOutJWarehouseGoodsInId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchRWarehouseGoodsOutJWarehouseGoodsInDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="warehouseGoodsOutId != null">
        WAREHOUSE_GOODS_OUT_ID,
      </if>
      <if test="warehouseGoodsInId != null">
        WAREHOUSE_GOODS_IN_ID,
      </if>
      <if test="warehouseGoodsOutItemId != null">
        WAREHOUSE_GOODS_OUT_ITEM_ID,
      </if>
      <if test="warehouseGoodsInItemId != null">
        WAREHOUSE_GOODS_IN_ITEM_ID,
      </if>
      <if test="relationType != null">
        RELATION_TYPE,
      </if>
      <if test="skuId != null">
        SKU_ID,
      </if>
      <if test="skuNo != null">
        SKU_NO,
      </if>
      <if test="num != null">
        NUM,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="warehouseGoodsOutId != null">
        #{warehouseGoodsOutId,jdbcType=BIGINT},
      </if>
      <if test="warehouseGoodsInId != null">
        #{warehouseGoodsInId,jdbcType=BIGINT},
      </if>
      <if test="warehouseGoodsOutItemId != null">
        #{warehouseGoodsOutItemId,jdbcType=BIGINT},
      </if>
      <if test="warehouseGoodsInItemId != null">
        #{warehouseGoodsInItemId,jdbcType=BIGINT},
      </if>
      <if test="relationType != null">
        #{relationType,jdbcType=INTEGER},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=INTEGER},
      </if>
      <if test="skuNo != null">
        #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        #{num,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchRWarehouseGoodsOutJWarehouseGoodsInDto">
    <!--@mbg.generated-->
    update T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="warehouseGoodsOutId != null">
        WAREHOUSE_GOODS_OUT_ID = #{warehouseGoodsOutId,jdbcType=BIGINT},
      </if>
      <if test="warehouseGoodsInId != null">
        WAREHOUSE_GOODS_IN_ID = #{warehouseGoodsInId,jdbcType=BIGINT},
      </if>
      <if test="warehouseGoodsOutItemId != null">
        WAREHOUSE_GOODS_OUT_ITEM_ID = #{warehouseGoodsOutItemId,jdbcType=BIGINT},
      </if>
      <if test="warehouseGoodsInItemId != null">
        WAREHOUSE_GOODS_IN_ITEM_ID = #{warehouseGoodsInItemId,jdbcType=BIGINT},
      </if>
      <if test="relationType != null">
        RELATION_TYPE = #{relationType,jdbcType=INTEGER},
      </if>
      <if test="skuId != null">
        SKU_ID = #{skuId,jdbcType=INTEGER},
      </if>
      <if test="skuNo != null">
        SKU_NO = #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=DECIMAL},
      </if>
    </set>
    where T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchRWarehouseGoodsOutJWarehouseGoodsInDto">
    <!--@mbg.generated-->
    update T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      WAREHOUSE_GOODS_OUT_ID = #{warehouseGoodsOutId,jdbcType=BIGINT},
      WAREHOUSE_GOODS_IN_ID = #{warehouseGoodsInId,jdbcType=BIGINT},
      WAREHOUSE_GOODS_OUT_ITEM_ID = #{warehouseGoodsOutItemId,jdbcType=BIGINT},
      WAREHOUSE_GOODS_IN_ITEM_ID = #{warehouseGoodsInItemId,jdbcType=BIGINT},
      RELATION_TYPE = #{relationType,jdbcType=INTEGER},
      SKU_ID = #{skuId,jdbcType=INTEGER},
      SKU_NO = #{skuNo,jdbcType=VARCHAR},
      NUM = #{num,jdbcType=DECIMAL}
    where T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{item.tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{item.tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{item.tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{item.tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{item.tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{item.tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="WAREHOUSE_GOODS_OUT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{item.tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT} then #{item.warehouseGoodsOutId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="WAREHOUSE_GOODS_IN_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{item.tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT} then #{item.warehouseGoodsInId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="WAREHOUSE_GOODS_OUT_ITEM_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{item.tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT} then #{item.warehouseGoodsOutItemId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="WAREHOUSE_GOODS_IN_ITEM_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{item.tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT} then #{item.warehouseGoodsInItemId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="RELATION_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{item.tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT} then #{item.relationType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="SKU_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{item.tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT} then #{item.skuId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="SKU_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{item.tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT} then #{item.skuNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{item.tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT} then #{item.num,jdbcType=DECIMAL}
        </foreach>
      </trim>
    </trim>
    where T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{item.tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{item.tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{item.tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{item.tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{item.tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{item.tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="WAREHOUSE_GOODS_OUT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.warehouseGoodsOutId != null">
            when T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{item.tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT} then #{item.warehouseGoodsOutId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="WAREHOUSE_GOODS_IN_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.warehouseGoodsInId != null">
            when T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{item.tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT} then #{item.warehouseGoodsInId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="WAREHOUSE_GOODS_OUT_ITEM_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.warehouseGoodsOutItemId != null">
            when T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{item.tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT} then #{item.warehouseGoodsOutItemId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="WAREHOUSE_GOODS_IN_ITEM_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.warehouseGoodsInItemId != null">
            when T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{item.tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT} then #{item.warehouseGoodsInItemId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="RELATION_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.relationType != null">
            when T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{item.tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT} then #{item.relationType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="SKU_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.skuId != null">
            when T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{item.tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT} then #{item.skuId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="SKU_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.skuNo != null">
            when T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{item.tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT} then #{item.skuNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.num != null">
            when T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{item.tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT} then #{item.num,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
    </trim>
    where T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID" keyProperty="tRWarehouseGoodsOutJWarehouseGoodsInId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN
    (ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, WAREHOUSE_GOODS_OUT_ID,
      WAREHOUSE_GOODS_IN_ID, WAREHOUSE_GOODS_OUT_ITEM_ID, WAREHOUSE_GOODS_IN_ITEM_ID,
      RELATION_TYPE, SKU_ID, SKU_NO, NUM)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER},
        #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR},
        #{item.warehouseGoodsOutId,jdbcType=BIGINT}, #{item.warehouseGoodsInId,jdbcType=BIGINT},
        #{item.warehouseGoodsOutItemId,jdbcType=BIGINT}, #{item.warehouseGoodsInItemId,jdbcType=BIGINT},
        #{item.relationType,jdbcType=INTEGER}, #{item.skuId,jdbcType=INTEGER}, #{item.skuNo,jdbcType=VARCHAR},
        #{item.num,jdbcType=DECIMAL})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID" keyProperty="tRWarehouseGoodsOutJWarehouseGoodsInId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchRWarehouseGoodsOutJWarehouseGoodsInDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tRWarehouseGoodsOutJWarehouseGoodsInId != null">
        T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID,
      </if>
      ADD_TIME,
      MOD_TIME,
      CREATOR,
      UPDATER,
      CREATOR_NAME,
      UPDATER_NAME,
      WAREHOUSE_GOODS_OUT_ID,
      WAREHOUSE_GOODS_IN_ID,
      WAREHOUSE_GOODS_OUT_ITEM_ID,
      WAREHOUSE_GOODS_IN_ITEM_ID,
      RELATION_TYPE,
      SKU_ID,
      SKU_NO,
      NUM,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tRWarehouseGoodsOutJWarehouseGoodsInId != null">
        #{tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT},
      </if>
      #{addTime,jdbcType=TIMESTAMP},
      #{modTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=INTEGER},
      #{updater,jdbcType=INTEGER},
      #{creatorName,jdbcType=VARCHAR},
      #{updaterName,jdbcType=VARCHAR},
      #{warehouseGoodsOutId,jdbcType=BIGINT},
      #{warehouseGoodsInId,jdbcType=BIGINT},
      #{warehouseGoodsOutItemId,jdbcType=BIGINT},
      #{warehouseGoodsInItemId,jdbcType=BIGINT},
      #{relationType,jdbcType=INTEGER},
      #{skuId,jdbcType=INTEGER},
      #{skuNo,jdbcType=VARCHAR},
      #{num,jdbcType=DECIMAL},
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="tRWarehouseGoodsOutJWarehouseGoodsInId != null">
        T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT},
      </if>
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      WAREHOUSE_GOODS_OUT_ID = #{warehouseGoodsOutId,jdbcType=BIGINT},
      WAREHOUSE_GOODS_IN_ID = #{warehouseGoodsInId,jdbcType=BIGINT},
      WAREHOUSE_GOODS_OUT_ITEM_ID = #{warehouseGoodsOutItemId,jdbcType=BIGINT},
      WAREHOUSE_GOODS_IN_ITEM_ID = #{warehouseGoodsInItemId,jdbcType=BIGINT},
      RELATION_TYPE = #{relationType,jdbcType=INTEGER},
      SKU_ID = #{skuId,jdbcType=INTEGER},
      SKU_NO = #{skuNo,jdbcType=VARCHAR},
      NUM = #{num,jdbcType=DECIMAL},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID" keyProperty="tRWarehouseGoodsOutJWarehouseGoodsInId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchRWarehouseGoodsOutJWarehouseGoodsInDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tRWarehouseGoodsOutJWarehouseGoodsInId != null">
        T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="warehouseGoodsOutId != null">
        WAREHOUSE_GOODS_OUT_ID,
      </if>
      <if test="warehouseGoodsInId != null">
        WAREHOUSE_GOODS_IN_ID,
      </if>
      <if test="warehouseGoodsOutItemId != null">
        WAREHOUSE_GOODS_OUT_ITEM_ID,
      </if>
      <if test="warehouseGoodsInItemId != null">
        WAREHOUSE_GOODS_IN_ITEM_ID,
      </if>
      <if test="relationType != null">
        RELATION_TYPE,
      </if>
      <if test="skuId != null">
        SKU_ID,
      </if>
      <if test="skuNo != null">
        SKU_NO,
      </if>
      <if test="num != null">
        NUM,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tRWarehouseGoodsOutJWarehouseGoodsInId != null">
        #{tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="warehouseGoodsOutId != null">
        #{warehouseGoodsOutId,jdbcType=BIGINT},
      </if>
      <if test="warehouseGoodsInId != null">
        #{warehouseGoodsInId,jdbcType=BIGINT},
      </if>
      <if test="warehouseGoodsOutItemId != null">
        #{warehouseGoodsOutItemId,jdbcType=BIGINT},
      </if>
      <if test="warehouseGoodsInItemId != null">
        #{warehouseGoodsInItemId,jdbcType=BIGINT},
      </if>
      <if test="relationType != null">
        #{relationType,jdbcType=INTEGER},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=INTEGER},
      </if>
      <if test="skuNo != null">
        #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        #{num,jdbcType=DECIMAL},
      </if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="tRWarehouseGoodsOutJWarehouseGoodsInId != null">
        T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN_ID = #{tRWarehouseGoodsOutJWarehouseGoodsInId,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="warehouseGoodsOutId != null">
        WAREHOUSE_GOODS_OUT_ID = #{warehouseGoodsOutId,jdbcType=BIGINT},
      </if>
      <if test="warehouseGoodsInId != null">
        WAREHOUSE_GOODS_IN_ID = #{warehouseGoodsInId,jdbcType=BIGINT},
      </if>
      <if test="warehouseGoodsOutItemId != null">
        WAREHOUSE_GOODS_OUT_ITEM_ID = #{warehouseGoodsOutItemId,jdbcType=BIGINT},
      </if>
      <if test="warehouseGoodsInItemId != null">
        WAREHOUSE_GOODS_IN_ITEM_ID = #{warehouseGoodsInItemId,jdbcType=BIGINT},
      </if>
      <if test="relationType != null">
        RELATION_TYPE = #{relationType,jdbcType=INTEGER},
      </if>
      <if test="skuId != null">
        SKU_ID = #{skuId,jdbcType=INTEGER},
      </if>
      <if test="skuNo != null">
        SKU_NO = #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>

<!--auto generated by MybatisCodeHelper on 2023-01-10-->
  <select id="findByWarehouseGoodsInIdAndWarehouseGoodsInItemIdAndRelationType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN
    where WAREHOUSE_GOODS_IN_ID=#{warehouseGoodsInId,jdbcType=BIGINT} and
    WAREHOUSE_GOODS_IN_ITEM_ID=#{warehouseGoodsInItemId,jdbcType=BIGINT} and
    RELATION_TYPE=#{relationType,jdbcType=INTEGER}
  </select>


  <select id="findSkuByAfterSaleNo" resultMap="BaseResultMap">
    select VCS.SKU_ID as SKU_ID, VCS.SKU_NO as SKU_NO
    from T_WAREHOUSE_GOODS_OUT_IN TWGOI
           left join T_AFTER_SALES TAS on TAS.AFTER_SALES_NO = TWGOI.RELATE_NO
           left join T_AFTER_SALES_GOODS TASG on TASG.AFTER_SALES_ID = TAS.AFTER_SALES_ID
           left join V_CORE_SKU VCS on TASG.GOODS_ID = VCS.SKU_ID
    where TWGOI.RELATE_NO = #{relateNo,jdbcType=VARCHAR}
      and TASG.AFTER_SALES_GOODS_ID = #{relatedId,jdbcType=INTEGER}
    limit 1
  </select>

  <select id="getUnboundOutInAmountByItemId" resultType="java.math.BigDecimal">
    select ABS(IFNULL(TWGOII.NUM,0)) - (select IFNULL(SUM(NUM),0) as NUM
                                   from T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN
                                   where
                                     <if test="isOut == true">
                                       T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN.WAREHOUSE_GOODS_OUT_ITEM_ID = #{warehouseGoodsOutItemId,jdbcType=BIGINT}
                                     </if>
                                     <if test="isOut == false">
                                       T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN.WAREHOUSE_GOODS_IN_ITEM_ID = #{warehouseGoodsOutItemId,jdbcType=BIGINT}
                                     </if>
                                   )
    from T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII
    where TWGOII.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID = #{warehouseGoodsOutItemId,jdbcType=BIGINT}
  </select>
  <select id="getByWarehouseGoodsInItemId"
          resultType="com.vedeng.erp.kingdee.batch.dto.BatchRWarehouseGoodsOutJWarehouseGoodsInDto">
    select
    <include refid="Base_Column_List" />
    from T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN
    where
    WAREHOUSE_GOODS_IN_ITEM_ID=#{warehouseGoodsInItemId,jdbcType=BIGINT} and
    RELATION_TYPE=1
  </select>


  <select id="findByWarehouseGetBatchRWarehouseGoodsOutJWarehouseGoodsInDto"
          resultType="com.vedeng.erp.kingdee.batch.dto.BatchRWarehouseGoodsOutJWarehouseGoodsInDto">
    SELECT out_item.OUT_IN_NO,
           TWGOI.RELATE_NO,
           in_item.OUT_IN_NO,
           in_item.RELATE_NO,

           out_item.GOODS_ID                            skuId,
           CONCAT('V', out_item.GOODS_ID)               SKU_NO,
           in_item.RELATED_ID,
           out_item.RELATED_ID,
           TWGOI.WAREHOUSE_GOODS_OUT_IN_ID           AS warehouseGoodsOutId,
           in_item.WAREHOUSE_GOODS_OUT_IN_ID         AS warehouseGoodsInId,
           out_item.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID AS warehouseGoodsOutItemId,
           in_item.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID  AS warehouseGoodsInItemId,
           CASE
             WHEN in_item.NUM &lt;= out_item.NUM THEN in_item.NUM
             ELSE out_item.NUM
             END                                     AS num
    FROM T_WAREHOUSE_GOODS_OUT_IN_ITEM out_item
           JOIN (
      SELECT TWGOII.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID,
             TWGOII.RELATED_ID,
             TWGOII.NUM,
             TWGOII.ADD_TIME,
             TWGOII.OUT_IN_NO,
             TWGOI.RELATE_NO,
             TWGOI.WAREHOUSE_GOODS_OUT_IN_ID,
             @sum_quantity :=
                     IF(@prev_related = TWGOII.RELATED_ID, @sum_quantity + TWGOII.NUM,
                        TWGOII.NUM) AS sum_quantity,
             @prev_related := TWGOII.RELATED_ID
      FROM T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII
             left join T_WAREHOUSE_GOODS_OUT_IN TWGOI on TWGOII.OUT_IN_NO = TWGOI.OUT_IN_NO,
           (SELECT @prev_related := NULL, @sum_quantity := 0) AS vars
      WHERE OPERATE_TYPE = 5
        and TWGOI.RELATE_NO = #{aftersaleNo,jdbcType=VARCHAR}
        and TWGOI.IS_VIRTUAL = 1
      ORDER BY TWGOII.RELATED_ID,
               TWGOII.ADD_TIME,
               TWGOII.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID
    ) AS in_item ON out_item.RELATED_ID = in_item.RELATED_ID AND out_item.NUM &lt;= in_item.sum_quantity
           left join T_WAREHOUSE_GOODS_OUT_IN TWGOI on out_item.OUT_IN_NO = TWGOI.OUT_IN_NO
    WHERE out_item.OPERATE_TYPE = 2
      AND out_item.NUM > 0
      and TWGOI.IS_VIRTUAL = 1
      and TWGOI.RELATE_NO = #{saleorderNo,jdbcType=VARCHAR}
      AND NOT EXISTS(
            SELECT 1
            FROM T_R_WAREHOUSE_GOODS_OUT_J_WAREHOUSE_GOODS_IN
            WHERE WAREHOUSE_GOODS_OUT_ITEM_ID = out_item.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID
              AND WAREHOUSE_GOODS_IN_ITEM_ID = in_item.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID
      )
    ORDER BY out_item.RELATED_ID,
             out_item.ADD_TIME,
             out_item.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID;
  </select>
</mapper>