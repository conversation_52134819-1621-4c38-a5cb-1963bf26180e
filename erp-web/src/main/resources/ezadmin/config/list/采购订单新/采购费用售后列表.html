<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>(订单流)采购费用售后列表</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="purchaseexpenseaftersales" datasource="erp-datasourcetarget" fixednumber="2" append_column_url="" append_row_url="" empty_show="" firstcol="numbers">
<div id="appendHead"></div>
<div class="layui-fluid">
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">
            <li ><a href="/ezadmin/list/list-uwTrWkUDzfA">采购售后单</a></li>
            <li class="layui-this"><a href="/ezadmin/list/list-purchaseexpenseaftersales">费用售后单</a></li>
        </ul>
    </div>
    <form class="layui-form" id="search">

        <div class=" layui-inline ">
            <label class="layui-form-label">售后单号</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="EXPENSE_AFTER_SALES_NO" placeholder="请输入" style="" alias="TEAS" jdbctype="" data="" datatype="" oper="LIKE"></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">费用单号</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="19" name="BUYORDER_EXPENSE_NO" placeholder="请输入" style="" alias="TBE" jdbctype="" data="" datatype="" oper="LIKE"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">审核状态</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="21" name="AUDIT_STATUS" placeholder="" style="" alias="COALESCE ( TEASS.AUDIT_STATUS, 3 )" jdbctype="" data="[{&quot;V&quot;:&quot;待审核&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" oper=""></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">售后状态</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="21" name="AFTER_SALES_STATUS" placeholder="" style="" alias="TEASS" jdbctype="" data="[{&quot;V&quot;:&quot;待确认&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;进行中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;已完结&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;已关闭&quot;,&quot;K&quot;:&quot;3&quot;}]" datatype="JSON" oper=""></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">售后类型</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="21" name="EXPENSE_AFTER_SALES_TYPE" placeholder="" style="" alias="TEAS" jdbctype="" data="[{&quot;V&quot;:&quot;采购费用退货&quot;,&quot;K&quot;:&quot;4121&quot;},{&quot;V&quot;:&quot;采购费用退票&quot;,&quot;K&quot;:&quot;4122&quot;}]" datatype="JSON" oper=""></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">供应商名称</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="19" name="TRADER_NAME" placeholder="请输入" style="" alias="TBED" jdbctype="" data="" datatype="" oper="LIKE"></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">创建人</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="20" name="CREATOR" placeholder="" style="" alias="TEAS" jdbctype="" data="SELECT DISTINCT a.CREATOR K, lower(u.USERNAME) V
FROM T_EXPENSE_AFTER_SALES a
         LEFT JOIN T_USER u ON a.CREATOR = u.USER_ID
order by u.USERNAME" datatype="KVSQLCACHE" oper=""></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">生效时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="VALID_TIME" placeholder="" style=""  alias="DATE_FORMAT(TEASS.VALID_TIME, '%Y-%m-%d')" jdbctype="DATE" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">创建时间</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="daterange" name="ADD_TIME" placeholder="" style="" alias="TEASS" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>
    </form>
    <hr class="layui-border-blue">
    <div class="btn-group   bd-highlight" id="tableButton">
    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">
            <th type="rowbutton" id="rowbutton"></th>
            <th item_name="EXPENSE_AFTER_SALES_NO" name="EXPENSE_AFTER_SALES_NO" body="td-link-color" order="1" url="/buyorderExpense/aftersale/detail.do?expenseAfterSalesId=${EXPENSE_AFTER_SALES_ID}" opentype="PARENT" windowname="采购费用售后详情" datatype="" data="" style="" head="18">售后单号</th>
            <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-link" order="1" url="/trader/supplier/baseinfo.do?traderId=${TRADER_ID}" opentype="PARENT" windowname="" datatype="" data="" style="" head="18">供应商名称</th>
            <th item_name="BUYORDER_EXPENSE_NO" name="BUYORDER_EXPENSE_NO" body="td-link" order="1" url="/buyorderExpense/details.do?buyorderExpenseId=${BUYORDER_EXPENSE_ID}" opentype="PARENT" windowname="费用订单详情"  datatype="" data="" style="" head="18">费用单号</th>
            <th item_name="EXPENSE_AFTER_SALES_TYPE" name="EXPENSE_AFTER_SALES_TYPE" body="td-select" order="1" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="select
   SYS_OPTION_DEFINITION_ID K,TITLE V
    from T_SYS_OPTION_DEFINITION
    where STATUS = 1 AND PARENT_ID = 4120" style="" head="18">售后类型</th>
            <th item_name="AFTER_SALES_STATUS" name="AFTER_SALES_STATUS" body="td-select" order="1" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;已关闭&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;待确认&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;已完结&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;进行中&quot;,&quot;K&quot;:&quot;1&quot;}]" style="" head="18">售后状态</th>
            <th item_name="AUDIT_STATUS" name="AUDIT_STATUS" body="td-select" order="1" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;待审核&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" head="18">审核状态</th>
            <th item_name="CREATOR" name="CREATOR" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="select USER_ID K,USERNAME V FROM T_USER" style="" head="18">创建人</th>
            <th item_name="ADD_TIME" name="ADD_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">创建时间</th>
            <th item_name="VALID_TIME" name="VALID_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">生效时间</th>
        </tr>
        <tbody>
        </thead>

        <tr>
            <td colspan="100"> express:<pre id="express" class="layui-code" orderby="order by TEAS.ADD_TIME DESC" groupby="">
select CASE
           WHEN
               TEASS.AFTER_SALES_STATUS = 0 THEN
               'orangecircle'
           WHEN TEASS.AFTER_SALES_STATUS = 1 THEN
               'greencircle'
           WHEN TEASS.AFTER_SALES_STATUS = 2 THEN
               'bluecircle'
           WHEN TEASS.AFTER_SALES_STATUS = 3 THEN
               'greycircle'
           END COLOR_CIRCLE,
       TEAS.EXPENSE_AFTER_SALES_ID,
       TEAS.BUYORDER_EXPENSE_ID,
       TEAS.EXPENSE_AFTER_SALES_NO,
       TBE.BUYORDER_EXPENSE_NO,
       0       CREATE_TYPE,
       TBED.TRADER_NAME,
       TBED.TRADER_ID,
       TEAS.EXPENSE_AFTER_SALES_TYPE,
       TEASS.AFTER_SALES_STATUS*1 AFTER_SALES_STATUS,
       COALESCE ( TEASS.AUDIT_STATUS, 3 )  AS AUDIT_STATUS,
       TEAS.CREATOR,
       DATE_FORMAT(TEAS.ADD_TIME,'%Y-%m-%d %H:%i:%s') ADD_TIME,
       case TEASS.VALID_TIME
           when '2099-01-01 00:00:00' THEN '' else TEASS.VALID_TIME
           end VALID_TIME
from T_EXPENSE_AFTER_SALES TEAS
         left join T_EXPENSE_AFTER_SALES_STATUS TEASS on TEASS.EXPENSE_AFTER_SALES_ID = TEAS.EXPENSE_AFTER_SALES_ID
         left join T_BUYORDER_EXPENSE TBE on TEAS.BUYORDER_EXPENSE_ID = TBE.BUYORDER_EXPENSE_ID
         left join T_BUYORDER_EXPENSE_DETAIL TBED on TBE.BUYORDER_EXPENSE_ID = TBED.BUYORDER_EXPENSE_ID
WHERE TEAS.IS_DELETE = 0
  and TEASS.IS_DELETE = 0
  and TBED.IS_DELETE = 0
            </pre> count: <pre id="count" class="layui-code"></pre></td>
        </tr>
        </tbody>
    </table>
</div>
<div id="appendFoot">
    <script>

        $("input[name='row_data_hidden_VERIFY_USERNAME']").each(function(e){
            var curUser = $("#EZ_SESSION_USER_NAME_KEY").val();
            var verifyUser = $(this).val();
            debugger
            var varifyUserChar = verifyUser.split(",");
            var flag = false;
            if (varifyUserChar[0] == '0'){
                for (var i = 1; i < varifyUserChar.length; i++) {
                    if (varifyUserChar[i] == curUser) {
                        flag = true;
                        break;
                    }
                }
            }
            if (flag){
                var shen = "<font color='red'>[审]</font>";
                $(this).parent().next().children("a").after(shen);
            }
        })
    </script>
</div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
</body>
</html>