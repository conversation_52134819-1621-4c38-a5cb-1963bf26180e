package com.vedeng.customerbillperiod.dao;

import com.vedeng.customerbillperiod.dto.BillPeriodItem;
import com.vedeng.customerbillperiod.model.CustomerBillPeriod;
import org.apache.ibatis.annotations.Param;
import java.util.List;

public interface CustomerBillPeriodMapper {
    int deleteByPrimaryKey(Long billPeriodId);

    int insert(CustomerBillPeriod record);

    int insertSelective(CustomerBillPeriod record);

    CustomerBillPeriod selectByPrimaryKey(Long billPeriodId);

    int updateByPrimaryKeySelective(CustomerBillPeriod record);

    /**
     * 查询客户指定账期类型的账期集合
     * @param companyId 公司id
     * @param customerId 客户id
     * @param type 账期类型
     * @return 账期集合
     */
    List<CustomerBillPeriod> getCustomerBillPeriodListByCustomerIdAndType(@Param("companyId") Integer companyId,
                                                                          @Param("customerId") Long customerId,
                                                                          @Param("type") Integer type);

    /**
     * 获取客户的账期集合
     * @param companyId 公司id
     * @param customerId 客户id
     * @return 账期集合
     */
    List<CustomerBillPeriod> getCustomerBillPeriodListByCustomerId(@Param("companyId") Integer companyId, @Param("customerId") Long customerId);

    /**
     * 获取客户指定订单的订单账期集合
     * @param companyId 公司id
     * @param customerId 客户id
     * @param relatedOrderId 关联的订单id
     * @return 订单账期集合
     */
    List<CustomerBillPeriod> getCustomerBillPeriodListByCustomerIdAndRelatedOrderId(@Param("companyId") Integer companyId,
                                                                                    @Param("customerId") Long customerId,
                                                                                    @Param("relatedOrderId") Integer relatedOrderId);


    /**
     * 根据账期创建者查询账期集合
     * @param companyId 公司id
     * @param creators 创建者集合
     * @param startTime 申请开始时间
     * @param endTime 申请截止时间
     * @return 账期集合
     */
    List<CustomerBillPeriod> getCustomerBillPeriodListByCreator(@Param("companyId") Integer companyId, @Param("creators") List<Integer> creators,
                                                                @Param("startTime") Long startTime, @Param("endTime") Long endTime);


    /**
     * 根据创建者查询账期客户集合
     * @param companyId 公司id
     * @param creators 创建者
     * @return 客户集合
     */
    List<Long> getCustomerIdListByCreator(@Param("companyId") Integer companyId, @Param("creators") List<Integer> creators);


    /**
     * 查询客户各账期类型的可用账期余额
     * @param companyId 公司id
     * @param customerId 客户id
     * @param orderId 订单id
     * @param currentTime 当前时间
     * @return 可用余额
     */
    List<BillPeriodItem> getAvailableAmountGroupByType(@Param("companyId") Integer companyId, @Param("customerId") Long customerId,
                                                       @Param("orderId") Long orderId, @Param("currentTime") Long currentTime);


    /**
     * 获取客户各个账期的可用余额
     * @param companyId 公司id
     * @param customerId 客户id
     * @param orderId 订单id
     * @param currentTime 当前时间
     * @return 账期可用余额
     */
    List<CustomerBillPeriod> getAvailableAmountGroupByBillPeriod(@Param("companyId") Integer companyId, @Param("customerId") Long customerId,
                                                       @Param("orderId") Long orderId, @Param("currentTime") Long currentTime);


    /**
     * 批量查询客户账期
     * @param idList 账期id集合
     * @return 账期集合
     */
    List<CustomerBillPeriod> batchGetCustomerBillPeriodById(@Param("idList") List<Long> idList);


    /**
     * 查询指定类型的所有未失效客户账期（账期生效截止时间大于等于当前时间）
     * @param companyId 公司id
     * @param customerId 客户id
     * @param type 账期类型
     * @param currentTime 当前时间
     * @return 有效客户账期集合
     */
    List<CustomerBillPeriod> getValidCustomerBillPeriodByType(@Param("companyId") Integer companyId, @Param("customerId") Long customerId,
                                                              @Param("type") Integer type, @Param("currentTime") Long currentTime);


    /**
     * 查询账期的当前可用余额（申请金额-冻结金额）
     * @param billPeriodIdList 账期集合
     * @param currentTime 当前时间
     * @return 账期可用余额
     */
    List<CustomerBillPeriod> getAvailableAmountByBillPeriodList(@Param("billPeriodIdList") List<Long> billPeriodIdList, @Param("currentTime") Long currentTime);
}