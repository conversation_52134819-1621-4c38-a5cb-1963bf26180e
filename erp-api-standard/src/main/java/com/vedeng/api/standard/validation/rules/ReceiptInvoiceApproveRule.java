package com.vedeng.api.standard.validation.rules;

import com.vedeng.api.standard.adapter.receiptInvoice.dto.ReceiptInvoiceRequest;
import com.vedeng.api.standard.validation.ValidationContextKeys;
import com.vedeng.api.standard.validation.ValidationResult;
import com.vedeng.api.standard.validation.ValidationRule;
import com.vedeng.erp.buyorder.dto.BuyOrderApiDto;
import com.vedeng.erp.buyorder.service.BuyorderApiService;
import com.vedeng.erp.finance.dto.InvoiceDto;
import com.vedeng.erp.finance.service.InvoiceApiService;
import com.vedeng.finance.model.Invoice;
import com.vedeng.finance.service.InvoiceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class ReceiptInvoiceApproveRule implements ValidationRule<ReceiptInvoiceRequest> {
    
    @Autowired
    private InvoiceApiService invoiceApiService;
    
    @Autowired
    private InvoiceService invoiceService;
    
    @Autowired
    private BuyorderApiService buyorderApiService;

    @Override
    public ValidationResult validate(ReceiptInvoiceRequest request, Map<String, Object> context) {
        log.debug("开始验证收票审核: invoiceNo={}", request.getInvoiceNo());
        InvoiceDto invoiceDto = invoiceApiService.queryReceiptInvoiceRecord(request.getBuyOrderNo(), request.getInvoiceNo());
        if (!Objects.equals(invoiceDto.getValidStatus(),0)){
            return ValidationResult.failure(getRuleName(), request.getInvoiceNo()+"未查询到待审核数据");
        }

        //  设置参数
        Invoice invoice = new Invoice();
        invoice.setColorType(invoiceDto.getColorType());
        invoice.setInvoiceId(invoiceDto.getInvoiceId());
        invoice.setInvoiceNo(request.getInvoiceNo());
        invoice.setIsEnable(invoiceDto.getIsEnable());
        invoice.setModTime(System.currentTimeMillis());
        invoice.setType(invoiceDto.getType());
        invoice.setValidStatus(1);
        invoice.setValidTime(System.currentTimeMillis());
        invoice.setValidUserId(1);
        invoice.setUpdater(1);
        invoice.setValidComments("业务流转自动审核");

        context.put(ValidationContextKeys.INVOICE, invoice);
        
        return ValidationResult.success();
    }

    @Override
    public String getRuleName() {
        return "ReceiptInvoiceEnableRule";
    }
}
