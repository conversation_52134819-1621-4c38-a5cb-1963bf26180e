
/**
 * 呼出电话前数据处理
 * @param phone 被叫号码
 * @param traderId 交易者id
 * @param traderType 交易者类型 1客户2供应商
 * @param callType 呼出订单类型 1商机2销售订单3报价4售后5采购订单//没有就传 0
 * @param orderId 订单id 没有就传 0
 * @param traderContactId 联系人ID 没有就传 0
 * @returns
 */
function calloutWS(phone,traderId,traderType,callType,orderId,traderContactId){
    console.log('calloutWS phone:' + phone);
    checkLogin();

    //呼叫中心优化相关start
    var ccWindow=$(window.parent.document).find("#callcenter").contents();
    var newCallCenterFlag = ccWindow.find("input[id='newCallCenterFlag']").val();
    var lineCode;
    var callingNumber;

    debugger;
    if (newCallCenterFlag == 1){
        $.ajax({
            type:'post',
            url:page_url+'/system/call/getCallingLine.do',
            data:{phone:phone},
            dataType:'json',
            async: false,
            success:function(callingData){
                if (callingData.code == -1){
                    alert(callingData.message);
                    return ;
                }
                lineCode = callingData.data.lineCode;
                callingNumber =  callingData.data.callingNumber;
                console.log("新呼叫中心呼出lineCode:{}" + lineCode + ", callingNumber:{}" + callingNumber);
                ccWindow.find("input[id='lineCode']").val(lineCode)
                ccWindow.find("input[id='callingNumber']").val(callingNumber)
                ccWindow.find("span[id='callLine']").html(callingNumber)
                var myCallSelectValue = 0;
                if (4 == lineCode){
                    myCallSelectValue = 1;
                }
                ccWindow.find("span[id='trace_msg']").html("主叫号码：" + callingNumber);
                ccWindow.find("select[id='myCallSelect']").val(myCallSelectValue);

                // changeMycall();
                $.ajax({
                    url:"/system/call/static/changeMyCall.do?type=" + myCallSelectValue,
                    async:false ,
                    success:function(d){
                        console.log('切换呼出页面标记成功 myCallSelectValue:{}' + myCallSelectValue);
                    },error:function(e){
                        alert("切换呼出页面标记失败,请等会再试");
                        return;
                    }
                })
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
                console.log("获取固话主机号码错误信息" + data.message);
            }
        });
        if (lineCode == undefined){
            return;
        }
    }
    else{
        if(self.parent.document.getElementById("callcenter") != null) {
            var callCenterWindow = $(window.parent.document).find("#callcenter").contents();
            if (callCenterWindow.find("select[name='mycall']").val() == 0) {
                lineCode = 3;
            }
        }
        //非newCallCenterFlag == 1表示未切换到新的呼叫规则下，lineCode默认为电信
    }
    //呼叫中心优化相关end



    traderId=default0(traderId);
    traderType=default0(traderType);
    callType=default0(callType);
    orderId=default0(orderId);
    traderContactId=default0(traderContactId);
    if(self.parent.document.getElementById("callcenter") != null){
        var obj = self.parent.document.getElementById("callcenter").contentWindow;
        $.ajax({
            type:'post',
            url:page_url+'/system/call/getphoneinfo.do',
            data:{'phone':phone,'lineCode':lineCode},
            dataType:'json',
            async: false,
            success:function(data){
                if(data.code ==403){
                    layer.alert("登录已经过期，您没有操作权限，申请开通权限请联系研发部Aadi")
                    return;
                }
                var callCenterWindow=$(window.parent.document).find("#callcenter").contents();
                //用于各类通话弹框参数 开始-----------------
                $(window.parent.document).find("#call_traderId").val(traderId);
                $(window.parent.document).find("#call_traderType").val(traderType);
                $(window.parent.document).find("#call_callType").val(callType);
                $(window.parent.document).find("#call_orderId").val(orderId);
                callCenterWindow.find("input[name='orderTypeCallFlag']").val(callType);
                callCenterWindow.find("input[name='orderIdCallFlag']").val(orderId);
                $(window.parent.document).find("#call_traderContactId").val(traderContactId);
                //用于各类通话弹框参数 结束-----------------


                if (newCallCenterFlag == 1){
                    if (lineCode == 3){
                        callingNumber = '+8625' + callingNumber;
                    }
                    if (4 == lineCode){
                        $.ajax({
                            url:"http://************:8180/WSAgentServer5/softphone/getRollingAni.do",
                            async:false ,
                            success:function(d){
                                callCenterWindow.find("#phonepool").text(d.data);
                                callCenterWindow.find("#ttNumber").val('');
                                $("#ttNumberCount").val(0);
                                signPpNumber(d.data);
                                callCenterWindow.find("#currentTtNumber").val(d.data);
                                console.log('号码池最终呼出 p1:{}' + data.data + 'p2:{}' + d.data);
                                obj.serviceDial(data.data,d.data,null,null,null,1);
                                return;
                            },error:function(e){
                                alert("获取号码池号码失败,请等会再试");
                                return;
                            }
                        })
                        return;
                    }
                    //固话线路呼出
                    callCenterWindow.find("#currentTtNumber").val('');
                    console.log('新呼叫中心固话呼出 phone:{}' + data.data + "callingNumber:{}" + callingNumber);
                    obj.serviceDial(data.data,callingNumber,null,null,null,1);
                    return;

                }

                console.log('旧呼叫中心呼出 phone:' + data.data);
                //号码池
                if(callCenterWindow.find("select[name='mycall']").val()==1){
                    $.ajax({
                        url:"http://************:8180/WSAgentServer5/softphone/getRollingAni.do",
                        async:false ,
                        success:function(d){
                            callCenterWindow.find("#phonepool").text(d.data);
                            callCenterWindow.find("#ttNumber").val('');
                            $("#ttNumberCount").val(0);
                            signPpNumber(d.data);
                            callCenterWindow.find("#currentTtNumber").val(d.data);
                            obj.serviceDial(data.data,d.data,null,null,null,1);
                            return;
                        },error:function(e){
                            alert("获取号码池号码失败,请等会再试");
                            return;
                        }
                    })
                }
                //主叫号码
                if(callCenterWindow.find("select[name='mycall']").val()==2){
                    var ttnumber=callCenterWindow.find("#ttNumber").val();
                    if( ttnumber!=''){
                        console.log('getRollingANI from erp ' +ttnumber)
                    }else{
                        alert("无主叫号码，有弹窗")
                        console.log('getRollingANI 号码池内号码 ' + ttnumber)
                    }
                    signPpNumber(ttnumber);
                    callCenterWindow.find("#currentTtNumber").val(ttnumber);
                    console.log('旧呼叫中心主叫号码呼出 phone:' + data.data + ',ttnumber:' + ttnumber);
                    obj.serviceDial(data.data,ttnumber,null,null,null,0);
                    return;
                }
                var telecomLine = callCenterWindow.find("input[id='telecomLine']").val();
                if(callCenterWindow.find("select[name='mycall']").val()==0 && telecomLine !=""){
                    //固话拨号
                    lineCode  = 3;
                    callingNumber = '+8625' +   callCenterWindow.find("input[id='telecomLine']").val();  // $("#telecomLine").val();
                    callCenterWindow.find("#currentTtNumber").val('');
                    console.log('非销售岗位固话呼出 phone:{}' + data.data + "callingNumber:{}" + callingNumber);
                    obj.serviceDial(data.data,callingNumber,null,null,null,1);
                    return ;
                }

                //常规拨号
                callCenterWindow.find("#currentTtNumber").val('');
                obj.serviceDial(data.data);
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    }

}


function default0(param){
    if(param === '' || param === null){
        param = 0;
    }
    return param;
}


/**
 * 呼出电话前数据处理(无弹窗)
 * @param phone 被叫号码
 * @param traderId 交易者id
 * @param traderType 交易者类型 1客户2供应商
 * @param callType 呼出订单类型 1商机2销售订单3报价4售后5采购订单//没有就传 0
 * @param orderId 订单id 没有就传 0
 * @param traderContactId 联系人ID 没有就传 0
 * @returns
 */

//TODO 暂时不做升级，因为是内部通话快捷方式
function calloutNoScreenWS(phone,traderId,traderType,callType,orderId,traderContactId){
    checkLogin();
    phone = phone.replace(/^025/,"");
    if(traderId == '' || traderId == null){
        traderId = 0;
    }
    if(traderType == '' || traderType == null){
        traderType = 0;
    }
    if(callType == '' || callType == null){
        callType = 0;
    }
    if(orderId == '' || orderId == null){
        orderId = 0;
    }
    if(traderContactId == '' || traderContactId == null){
        traderContactId = 0;
    }
    console.log("不打开窗口"+call_screen)
    $.get("/static/js/call/test.js?calloutNoScreenWS="+$('#agentID').val()+Math.random());
    if(self.parent.document.getElementById("callcenter") != null){
        var obj = self.parent.document.getElementById("callcenter").contentWindow;
        $.ajax({
            type:'post',
            url:page_url+'/system/call/getphoneinfo.do',
            data:{phone:phone},
            dataType:'json',
            success:function(data){
                self.parent.document.getElementById("call_traderId").value=traderId;
                self.parent.document.getElementById("call_traderType").value=traderType;
                self.parent.document.getElementById("call_callType").value=callType;
                self.parent.document.getElementById("call_orderId").value=orderId;
                self.parent.document.getElementById("call_traderContactId").value=traderContactId;
                self.parent.document.getElementById("call_screen").value='0';
                console.log('calloutNoScreen'+data.data)


                //获取主叫 设置主叫
                var pp='';
                console.log("calloutNoScreen current select:"+$(window.parent.document).find("#callcenter").contents().find("select[name='mycall']").val())
                if($(window.parent.document).find("#callcenter").contents().find("select[name='mycall']").val()==1){
                    //  Trace("主叫号码：号码池内号码");
                    //TODO 号码池
                    pp = obj.document.getElementById("csSoftPhone").getRollingANI();
                    console.log('calloutNoScreen getRollingANI  号码池内号码 ' + pp)
                    $(window.parent.document).find("#callcenter").contents().find("#ttNumber").val('');
                    $(window.parent.document).find("#callcenter").contents().find("#ttNumberCount").val(0);
                }
                if($(window.parent.document).find("#callcenter").contents().find("select[name='mycall']").val()==2){
                    if( $(window.parent.document).find("#callcenter").contents().find("#ttNumber").val()!=''){
                        pp=$(window.parent.document).find("#callcenter").contents().find("#ttNumber").val();
                        console.log('calloutNoScreen getRollingANI from erp ' + pp)
                    }else{
                        alert("无主叫号码，无弹窗")
                        console.log('calloutNoScreen getRollingANI 号码池内号码 ' + pp)
                    }
                }
                console.log('calloutNoScreen getRollingANI from erp2 '  + pp)
                //$.get('/getRollingANI_from_erp_'+pp)

                $(window.parent.document).find("#callcenter").contents().find("#currentTtNumber").val(pp);

                if($(window.parent.document).find("#callcenter").contents().find("select[name='mycall']").val()==1){
                    $(window.parent.document).find("#callcenter").contents().find("#phonepool").html(pp);
                }

                obj.serviceSetCoInfos("DIALPREPARE_ANI", pp);
                obj.serviceDial(data.data,"");
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    }
}
/**
 * 呼出电话弹屏
 * @param phone 被叫号码
 * @param coid 录音coid
 * @returns
 */
//TODO 新版本 迁移至 wssp.js/csSoftPhone_evtDialBegin ，因为此js只在 call/index.jsp中用到
function calloutScreenWS(phone,coid){
    checkLogin();
    var obj = self.parent.document.getElementById("callcenter").contentWindow;

    var coid = obj.serviceGetCoInfo("COID");
    var call_traderId = self.parent.document.getElementById("call_traderId").value != undefined ? self.parent.document.getElementById("call_traderId").value : 0;
    var call_traderType = self.parent.document.getElementById("call_traderType").value != undefined ? self.parent.document.getElementById("call_traderType").value: 0;
    var call_callType = self.parent.document.getElementById("call_callType").value != undefined ? self.parent.document.getElementById("call_callType").value: 0;
    var call_orderId = self.parent.document.getElementById("call_orderId").value != undefined ? self.parent.document.getElementById("call_orderId").value: 0;
    var call_traderContactId = self.parent.document.getElementById("call_traderContactId").val != undefined ? self.parent.document.getElementById("call_traderContactId").value: 0;
    var call_screen = self.parent.document.getElementById("call_screen").value != undefined ? self.parent.document.getElementById("call_screen").value: 0;

    self.parent.document.getElementById("call_traderId").value=0;
    self.parent.document.getElementById("call_traderType").value=0;
    self.parent.document.getElementById("call_callType").value=0;
    self.parent.document.getElementById("call_orderId").value=0;
    self.parent.document.getElementById("call_traderContactId").value=0;
    self.parent.document.getElementById("call_screen").value = 1;
    // if(phone.startsWith('09')){
    // 	$(window.parent.document).find("#callcenter").contents().find("select[name='mycall']").val(1)
    // }else{
   	//$(window.parent.document).find("#callcenter").contents().find("select[name='mycall']").val(0)
    // }
    console.log("ws打开窗口"+call_screen+"\t"+self.parent.document.getElementById("call_screen").val+"\t"+self.parent.document.getElementById("call_screen").value)
    var timestamp = Date.parse(new Date());
    $('#communicateTime').val(timestamp);
    var lineCode = $('#lineCode').val();
    var callingNumber = $('#callingNumber').val();
    if(call_screen == 1){
        self.parent.callIndex = self.parent.layer.myopen({
            type: 2,
            shadeClose: false, //点击遮罩关闭
            closeBtn: false,
            area: ['880px','531px'],
            title: false,
            content: ['./system/call/getcallout.do?traderId='+call_traderId+'&traderType='+call_traderType+'&callType='+call_callType+'&orderId='+call_orderId+'&coid='+coid+'&traderContactId='+call_traderContactId+'&phone='+phone+'&communicateTime='+timestamp+'&lineCode='+lineCode+'&callingNumber='+callingNumber,'no'],
            success: function(layero, index) {
                //layer.iframeAuto(index);
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    }else{
        $.ajax({
            type:'get',
            url:page_url+'/system/call/getcallout.do?traderId='+call_traderId+'&traderType='+call_traderType+'&callType='+call_callType+'&orderId='+call_orderId+'&coid='+coid+'&traderContactId='+call_traderContactId+'&phone='+phone,
            dataType:'json',
            success:function(data){
                //
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    }
}

/**
 * 来电弹屏
 * @param phone
 * @returns
 */
function callinWS(phone){
    checkLogin();
    var obj = self.parent.document.getElementById("callcenter").contentWindow
    var coid = obj.serviceGetCoInfo("COID");
    self.parent.callIndex = self.parent.layer.myopen({
        type: 2,
        shadeClose: false, //点击遮罩关闭
        closeBtn: false,
        area: ['880px','531px'],
        title: false,
        content: ['./system/call/getcallin.do?callType=1&callFrom=1&coid='+coid+'&phone='+phone,'no'],
        success: function(layero, index) {
            //layer.iframeAuto(index);
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }

    });
}

/**
 * 新增联系
 * @param phone 被叫号码
 * @param traderId 交易者id
 * @param traderType 交易者类型
 * @param callType 呼出订单类型
 * @param orderId 订单id
 * @param traderContactId 联系人ID
 * @param callFrom 沟通方式 0呼入1呼出
 * @returns
 */
function addCommWS(phone,traderId,traderType,callType,orderId,traderContactId,callFrom,coid){
    checkLogin();
    if(traderId == '' || traderId == null){
        traderId = 0;
    }
    if(traderType == '' || traderType == null){
        traderType = 0;
    }
    if(callType == '' || callType == null){
        callType = 0;
    }
    if(orderId == '' || orderId == null){
        orderId = 0;
    }
    if(traderContactId == '' || traderContactId == null){
        traderContactId = 0;
    }
    if(callFrom == '' || callFrom == null){
        callFrom = 0;
    }
    var obj = self.parent.document.getElementById("callcenter").contentWindow
    self.parent.callComm = self.parent.layer.myopen({
        type: 2,
        shadeClose: false, //点击遮罩关闭
        closeBtn: false,
        area: ['880px','531px'],
        title: false,
        content: ['./system/call/getaddcomm.do?traderId='+traderId+'&traderType='+traderType+'&callType='+callType+'&orderId='+orderId+'&coid='+coid+'&traderContactId='+traderContactId+'&callFrom='+callFrom+'&phone='+phone],
        success: function(layero, index) {
            //layer.iframeAuto(index);
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    });
}



/**
 * 添加沟通记录
 *
 */
function addComrecordWS() {
    var callInOrOut = wssp.getCoInfos()[17];//呼叫方向，0-呼入 1-呼出 2-内呼
    //{4: '8130', 9: '2024-6-19 14:55:38', 10: '', 13: '0', 15: 'AGT', 17: '1', 19: '4', 20: '4', 21: 'AGT', 23: 'OUT', 26: '0', 27: 'SIPVCD_40', 67: '2024-6-19 14:55:38', 68: '', 76: '8130', 94: '0', 95: '', 97: '9', 107: '1', 108: '1', 111: '1338', 211: '1', 212: '8130', 213: '1338_Kerwin.wang', 214: '0', 308: '************', 309: '16020', EDRS: '400', HI: null, CADIP: '************', INVOKEMT: 'AGT', DEVID: '20', …}
    var orderTypeCallFlag = $('#orderTypeCallFlag').val();
    var orderIdCallFlag = $('#orderIdCallFlag').val();
    var obj = self.parent.document.getElementById("callcenter").contentWindow
    var phone = obj.serviceGetCoInfo("DNIS");//先出被叫号码，呼出的时候，取被叫号码
    if(callInOrOut == 0){
        phone = obj.serviceGetCoInfo("ANI");//主叫号码，呼入的时候，取主叫号码
    }
    var coid = obj.serviceGetCoInfo("COID");
    var communicateTime = $('#communicateTime').val();
    var positType = $('#positType').val();//313 物流
    console.log("communicateTime："+communicateTime)
    console.log("start phone："+phone)
    phone = phone.replace("#", "");

    console.log('csSoftPhone_evtDialBegin' + phone);
    //是否可关闭沟通记录弹窗
    var canCloseCommunicate = $("#canCloseCommunicate").val();
    console.log('canCloseCommunicate:' + canCloseCommunicate);
    var canCloseCommunicateFlag = canCloseCommunicate == true || canCloseCommunicate == 'true';

    $.ajax({
        type:'post',
        url:page_url+'/system/call/getTraderIdByPhone.do',
        data:{phone:phone,coid:coid,traderType:1},
        dataType:'json',
        success:function(data){
            console.log('返回结果:' + data);
            console.log('listData:' + data.listData );
            console.log('data:' + data.data );
            if(phone.length <6 || callInOrOut == 2){//判断是否为内呼
                console.log("内呼结束，不弹屏");
                return;
            }

            if(positType && positType == "313"){//如果是物流打电话的情况
                console.log('物流拨号逻辑');
                self.parent.callIndex = self.parent.layer.myopen({
                    type: 2,
                    shadeClose: false,
                    closeBtn: canCloseCommunicateFlag,
                    area: ['880px','531px'],
                    title: false,
                    content: ['/saleCall/callWithExpress.do?phone='+phone+'&coid='+coid+'&callType='+
                    orderTypeCallFlag+'&orderId='+orderIdCallFlag+'&communicateTime='+communicateTime],//物流弹框架的地址
                    success: function(layero, index) {
                        //layer.iframeAuto(index);
                    },
                    error:function(data){
                        if(data.status ==1001){
                            layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                        }
                    }
                });
                return;
            }



            if (data.listData == null || data.listData == '' || data.listData .size == 0){
                //电话类型是商机并且不存在联系人的情况
                if (orderTypeCallFlag != undefined && orderTypeCallFlag == 1){
                    self.parent.callIndex = self.parent.layer.myopen({
                        type: 2,
                        shadeClose: false,
                        closeBtn: canCloseCommunicateFlag,
                        area: ['880px','531px'],
                        title: false,
                        content: ['/system/call/bussinessCommunicate.do?phone='+phone+'&coid='+coid+'&callType='+
                        orderTypeCallFlag+'&orderId='+orderIdCallFlag+'&communicateTime='+communicateTime],
                        success: function(layero, index) {
                            //layer.iframeAuto(index);
                        },
                        error:function(data){
                            if(data.status ==1001){
                                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                            }
                        }
                    });
                    return;
                }
                //return;
            }
            self.parent.callIndex = self.parent.layer.myopen({
                type: 2,
                shadeClose: false, //点击遮罩关闭
                closeBtn: canCloseCommunicateFlag,
                area: ['880px','531px'],
                title: false,
                content: ['/system/call/addCommunicate.do?phone='+phone+'&coid='+coid+'&callType='+orderTypeCallFlag+
                '&orderId='+orderIdCallFlag+'&communicateTime='+communicateTime],
                success: function(layero, index) {
                    //layer.iframeAuto(index);
                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    });
    $('#orderTypeCallFlag').val('');
    $('#orderIdCallFlag').val('');
}

