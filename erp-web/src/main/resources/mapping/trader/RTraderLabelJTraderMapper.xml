<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.trader.dao.RTraderLabelJTraderMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.trader.model.RTraderLabelJTrader">
    <id column="R_TRADER_LABEL_J_TRADER_ID" jdbcType="BIGINT" property="rTraderLabelJTraderId" />
    <result column="TRADER_LABEL_ID" jdbcType="INTEGER" property="traderLabelId" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
  </resultMap>
  <sql id="Base_Column_List">
    R_TRADER_LABEL_J_TRADER_ID, TRADER_LABEL_ID, TRADER_ID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_R_TRADER_LABEL_J_TRADER
    where R_TRADER_LABEL_J_TRADER_ID = #{rTraderLabelJTraderId,jdbcType=BIGINT}
  </select>

  <select id="getTraderIdsByLabelIds" parameterType="java.util.List" resultType="java.lang.Integer">
    select
      DISTINCT  TRADER_ID
    from T_R_TRADER_LABEL_J_TRADER
    where TRADER_LABEL_ID IN
    <foreach collection="list" item="id" separator="," open="(" close=")">
      #{id}
    </foreach>
  </select>

  <select id="getTraderIdsListPage" parameterType="map" resultType="java.lang.Integer">
    select
      DISTINCT  TRADER_ID
    from T_R_TRADER_LABEL_J_TRADER
    where TRADER_LABEL_ID =#{labelId}
  </select>

  <delete id="deleteByLabelIdAndTraderIds" parameterType="java.lang.Long">
    delete from T_R_TRADER_LABEL_J_TRADER
    where TRADER_LABEL_ID = #{labelId}
    <if test="traderIds!=null">
      AND TRADER_ID IN
      <foreach collection="traderIds" item="id" separator="," open="(" close=")">
        #{id}
      </foreach>
    </if>
  </delete>


  <delete id="deleteByTraderIdAndLabelIds" parameterType="java.lang.Long">
    delete from T_R_TRADER_LABEL_J_TRADER
    where TRADER_ID = #{traderId}
    <if test="labelIds!=null">
      AND TRADER_LABEL_ID IN
      <foreach collection="labelIds" item="id" separator="," open="(" close=")">
        #{id}
      </foreach>
    </if>
  </delete>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from T_R_TRADER_LABEL_J_TRADER
    where R_TRADER_LABEL_J_TRADER_ID = #{rTraderLabelJTraderId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="R_TRADER_LABEL_J_TRADER_ID" keyProperty="rTraderLabelJTraderId" parameterType="com.vedeng.trader.model.RTraderLabelJTrader" useGeneratedKeys="true">
    insert into T_R_TRADER_LABEL_J_TRADER (TRADER_LABEL_ID, TRADER_ID)
    values (#{traderLabelId,jdbcType=INTEGER}, #{traderId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="R_TRADER_LABEL_J_TRADER_ID" keyProperty="rTraderLabelJTraderId" parameterType="com.vedeng.trader.model.RTraderLabelJTrader" useGeneratedKeys="true">
    insert into T_R_TRADER_LABEL_J_TRADER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="traderLabelId != null">
        TRADER_LABEL_ID,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="traderLabelId != null">
        #{traderLabelId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.trader.model.RTraderLabelJTrader">
    update T_R_TRADER_LABEL_J_TRADER
    <set>
      <if test="traderLabelId != null">
        TRADER_LABEL_ID = #{traderLabelId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
    </set>
    where R_TRADER_LABEL_J_TRADER_ID = #{rTraderLabelJTraderId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.trader.model.RTraderLabelJTrader">
    update T_R_TRADER_LABEL_J_TRADER
    set TRADER_LABEL_ID = #{traderLabelId,jdbcType=INTEGER},
      TRADER_ID = #{traderId,jdbcType=INTEGER}
    where R_TRADER_LABEL_J_TRADER_ID = #{rTraderLabelJTraderId,jdbcType=BIGINT}
  </update>
</mapper>