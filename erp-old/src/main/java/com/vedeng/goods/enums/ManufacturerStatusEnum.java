package com.vedeng.goods.enums;

import lombok.Getter;

/**
 * @Author: Cherny.chen
 * @Create: 2021/6/8 14:34
 */
@Getter
public enum ManufacturerStatusEnum {

    /**
     * 审核状态 5 待提交审核 1待审核 2审核不通过 3审核通过
     */
    UN_SUBMIT(5, "待提交审核"),
    UN_CHECK(1,"待审核"),
    CHECK_FAIL(2,"审核不通过"),
    CHECK_SUCCESS(3, "审核通过")
    ;

    private final Integer status;

    private final String desc;

    ManufacturerStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static ManufacturerStatusEnum oneByStatus(Integer status) {
        ManufacturerStatusEnum[] values = ManufacturerStatusEnum.values();

        for (ManufacturerStatusEnum value : values) {
            if (value.status.equals(status)) {
                return value;
            }
        }

        throw  new RuntimeException("为查询到对应生产企业审核状态, status : " + status);
    }
}
