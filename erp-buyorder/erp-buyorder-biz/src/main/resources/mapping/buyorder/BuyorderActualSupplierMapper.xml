<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.buyorder.mapper.BuyorderActualSupplierMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.buyorder.domain.entity.BuyorderActualSupplier">
    <!--@mbg.generated-->
    <!--@Table T_BUYORDER_ACTUAL_SUPPLIER-->
    <id column="BUYORDER_ACTUAL_SUPPLIER_ID" jdbcType="BIGINT" property="buyorderActualSupplierId" />
    <result column="BUYORDER_ID" jdbcType="INTEGER" property="buyorderId" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName" />
    <result column="TRADER_CONTACT_ID" jdbcType="INTEGER" property="traderContactId" />
    <result column="TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="traderContactName" />
    <result column="TRADER_CONTACT_MOBILE" jdbcType="VARCHAR" property="traderContactMobile" />
    <result column="TRADER_CONTACT_TELEPHONE" jdbcType="VARCHAR" property="traderContactTelephone" />
    <result column="PAYMENT_TYPE" jdbcType="INTEGER" property="paymentType" />
    <result column="PREPAID_AMOUNT" jdbcType="DECIMAL" property="prepaidAmount" />
    <result column="ACCOUNT_PERIOD_AMOUNT" jdbcType="DECIMAL" property="accountPeriodAmount" />
    <result column="RETAINAGE_AMOUNT" jdbcType="DECIMAL" property="retainageAmount" />
    <result column="RETAINAGE_AMOUNT_MONTH" jdbcType="INTEGER" property="retainageAmountMonth" />
    <result column="INVOICE_TYPE" jdbcType="INTEGER" property="invoiceType" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
    <result column="NEED_INVOICE" jdbcType="INTEGER" property="needInvoice" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    BUYORDER_ACTUAL_SUPPLIER_ID, BUYORDER_ID, TRADER_ID, TRADER_NAME, TRADER_CONTACT_ID, 
    TRADER_CONTACT_NAME, TRADER_CONTACT_MOBILE, TRADER_CONTACT_TELEPHONE, PAYMENT_TYPE, 
    PREPAID_AMOUNT, ACCOUNT_PERIOD_AMOUNT, RETAINAGE_AMOUNT, RETAINAGE_AMOUNT_MONTH, 
    INVOICE_TYPE, IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, 
    UPDATE_REMARK, NEED_INVOICE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_BUYORDER_ACTUAL_SUPPLIER
    where BUYORDER_ACTUAL_SUPPLIER_ID = #{buyorderActualSupplierId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_BUYORDER_ACTUAL_SUPPLIER
    where BUYORDER_ACTUAL_SUPPLIER_ID = #{buyorderActualSupplierId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="BUYORDER_ACTUAL_SUPPLIER_ID" keyProperty="buyorderActualSupplierId" parameterType="com.vedeng.erp.buyorder.domain.entity.BuyorderActualSupplier" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUYORDER_ACTUAL_SUPPLIER (BUYORDER_ID, TRADER_ID, TRADER_NAME, 
      TRADER_CONTACT_ID, TRADER_CONTACT_NAME, TRADER_CONTACT_MOBILE, 
      TRADER_CONTACT_TELEPHONE, PAYMENT_TYPE, PREPAID_AMOUNT, 
      ACCOUNT_PERIOD_AMOUNT, RETAINAGE_AMOUNT, RETAINAGE_AMOUNT_MONTH, 
      INVOICE_TYPE, IS_DELETE, ADD_TIME, 
      MOD_TIME, CREATOR, CREATOR_NAME, 
      UPDATER, UPDATER_NAME, UPDATE_REMARK, 
      NEED_INVOICE)
    values (#{buyorderId,jdbcType=INTEGER}, #{traderId,jdbcType=INTEGER}, #{traderName,jdbcType=VARCHAR}, 
      #{traderContactId,jdbcType=INTEGER}, #{traderContactName,jdbcType=VARCHAR}, #{traderContactMobile,jdbcType=VARCHAR}, 
      #{traderContactTelephone,jdbcType=VARCHAR}, #{paymentType,jdbcType=INTEGER}, #{prepaidAmount,jdbcType=DECIMAL}, 
      #{accountPeriodAmount,jdbcType=DECIMAL}, #{retainageAmount,jdbcType=DECIMAL}, #{retainageAmountMonth,jdbcType=INTEGER}, 
      #{invoiceType,jdbcType=INTEGER}, #{isDelete,jdbcType=BOOLEAN}, #{addTime,jdbcType=TIMESTAMP}, 
      #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR}, 
      #{needInvoice,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="BUYORDER_ACTUAL_SUPPLIER_ID" keyProperty="buyorderActualSupplierId" parameterType="com.vedeng.erp.buyorder.domain.entity.BuyorderActualSupplier" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUYORDER_ACTUAL_SUPPLIER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="buyorderId != null">
        BUYORDER_ID,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderName != null and traderName != ''">
        TRADER_NAME,
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID,
      </if>
      <if test="traderContactName != null and traderContactName != ''">
        TRADER_CONTACT_NAME,
      </if>
      <if test="traderContactMobile != null and traderContactMobile != ''">
        TRADER_CONTACT_MOBILE,
      </if>
      <if test="traderContactTelephone != null and traderContactTelephone != ''">
        TRADER_CONTACT_TELEPHONE,
      </if>
      <if test="paymentType != null">
        PAYMENT_TYPE,
      </if>
      <if test="prepaidAmount != null">
        PREPAID_AMOUNT,
      </if>
      <if test="accountPeriodAmount != null">
        ACCOUNT_PERIOD_AMOUNT,
      </if>
      <if test="retainageAmount != null">
        RETAINAGE_AMOUNT,
      </if>
      <if test="retainageAmountMonth != null">
        RETAINAGE_AMOUNT_MONTH,
      </if>
      <if test="invoiceType != null">
        INVOICE_TYPE,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null and updateRemark != ''">
        UPDATE_REMARK,
      </if>
      <if test="needInvoice != null">
        NEED_INVOICE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="buyorderId != null">
        #{buyorderId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderName != null and traderName != ''">
        #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactId != null">
        #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null and traderContactName != ''">
        #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactMobile != null and traderContactMobile != ''">
        #{traderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="traderContactTelephone != null and traderContactTelephone != ''">
        #{traderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="paymentType != null">
        #{paymentType,jdbcType=INTEGER},
      </if>
      <if test="prepaidAmount != null">
        #{prepaidAmount,jdbcType=DECIMAL},
      </if>
      <if test="accountPeriodAmount != null">
        #{accountPeriodAmount,jdbcType=DECIMAL},
      </if>
      <if test="retainageAmount != null">
        #{retainageAmount,jdbcType=DECIMAL},
      </if>
      <if test="retainageAmountMonth != null">
        #{retainageAmountMonth,jdbcType=INTEGER},
      </if>
      <if test="invoiceType != null">
        #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null and updateRemark != ''">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
      <if test="needInvoice != null">
        #{needInvoice,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.buyorder.domain.entity.BuyorderActualSupplier">
    <!--@mbg.generated-->
    update T_BUYORDER_ACTUAL_SUPPLIER
    <set>
      <if test="buyorderId != null">
        BUYORDER_ID = #{buyorderId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderName != null and traderName != ''">
        TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null and traderContactName != ''">
        TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactMobile != null and traderContactMobile != ''">
        TRADER_CONTACT_MOBILE = #{traderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="traderContactTelephone != null and traderContactTelephone != ''">
        TRADER_CONTACT_TELEPHONE = #{traderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="paymentType != null">
        PAYMENT_TYPE = #{paymentType,jdbcType=INTEGER},
      </if>
      <if test="prepaidAmount != null">
        PREPAID_AMOUNT = #{prepaidAmount,jdbcType=DECIMAL},
      </if>
      <if test="accountPeriodAmount != null">
        ACCOUNT_PERIOD_AMOUNT = #{accountPeriodAmount,jdbcType=DECIMAL},
      </if>
      <if test="retainageAmount != null">
        RETAINAGE_AMOUNT = #{retainageAmount,jdbcType=DECIMAL},
      </if>
      <if test="retainageAmountMonth != null">
        RETAINAGE_AMOUNT_MONTH = #{retainageAmountMonth,jdbcType=INTEGER},
      </if>
      <if test="invoiceType != null">
        INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null and updateRemark != ''">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
      <if test="needInvoice != null">
        NEED_INVOICE = #{needInvoice,jdbcType=INTEGER},
      </if>
    </set>
    where BUYORDER_ACTUAL_SUPPLIER_ID = #{buyorderActualSupplierId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.buyorder.domain.entity.BuyorderActualSupplier">
    <!--@mbg.generated-->
    update T_BUYORDER_ACTUAL_SUPPLIER
    set BUYORDER_ID = #{buyorderId,jdbcType=INTEGER},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      TRADER_CONTACT_MOBILE = #{traderContactMobile,jdbcType=VARCHAR},
      TRADER_CONTACT_TELEPHONE = #{traderContactTelephone,jdbcType=VARCHAR},
      PAYMENT_TYPE = #{paymentType,jdbcType=INTEGER},
      PREPAID_AMOUNT = #{prepaidAmount,jdbcType=DECIMAL},
      ACCOUNT_PERIOD_AMOUNT = #{accountPeriodAmount,jdbcType=DECIMAL},
      RETAINAGE_AMOUNT = #{retainageAmount,jdbcType=DECIMAL},
      RETAINAGE_AMOUNT_MONTH = #{retainageAmountMonth,jdbcType=INTEGER},
      INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      NEED_INVOICE = #{needInvoice,jdbcType=INTEGER}
    where BUYORDER_ACTUAL_SUPPLIER_ID = #{buyorderActualSupplierId,jdbcType=BIGINT}
  </update>
    
<!--auto generated by MybatisCodeHelper on 2024-05-21-->
  <select id="findByBuyorderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_BUYORDER_ACTUAL_SUPPLIER
        where BUYORDER_ID=#{buyorderId,jdbcType=INTEGER}
    </select>
</mapper>