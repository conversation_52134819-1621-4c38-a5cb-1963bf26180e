<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="向供应链询价" scope="application" />
<%@ include file="../../common/common.jsp"%>

<div class="form-list form-tips7">
    <input id="consultRelatedId" value="${consultRelatedId}" type="hidden" />
    <input id="consultType" value="${consultType}" type="hidden" />
    <form method="post" id="myform">
        <ul>

            <li>
                <div class="form-tips">
                    <span>*</span>
                    <lable>咨询内容：</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <input type="text" class="input-largest" name="consultContent" id="consultContent" />
                    </div>
                    <div id="advancePurchaseCommentsError"></div>
                </div>
            </li>

        </ul>
        <div class="add-tijiao tcenter">
            <button type="button" class="bt-bg-style bg-light-green bt-small" onclick="saveConsultManager()">确定</button>
            <button class="dele" type="button" id="close-layer">取消</button>
        </div>
    </form>
</div>

<script>
    function saveConsultManager(){
        if ($("#consultContent").val().length == 0){
            layer.alert("咨询内容不得为空")
            return false;
        }

        $.ajax({
            url: '/order/quote/consult/save.do',
            contentType: 'application/json',
            data: JSON.stringify({
                consultRelatedId: Number($("#consultRelatedId").val()),
                consultType: Number($("#consultType").val()),
                consultContent: $("#consultContent").val()
            }),
            type:"POST",
            dataType : "json",
            success: function(data){
                layer.alert(data.message, {
                    icon : (data.code == 0 ? 1 : 2)
                }, function() {
                    if (parent.layer != undefined) {
                        parent.layer.close(index);
                    }
                    parent.location.reload();
                });
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        })
    }

</script>