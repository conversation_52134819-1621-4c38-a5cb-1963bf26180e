package com.vedeng.activiti.taskassign;

import java.util.List;
import java.util.Objects;

import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.apache.shiro.util.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import com.pricecenter.constant.VerifyStatusEnum;
import com.pricecenter.dto.SkuPriceChangeApplyDto;
import com.pricecenter.dto.SkuPriceChangeSaleInfoDto;
import com.pricecenter.service.BasePriceMaintainService;
import com.vedeng.goods.dao.CoreSkuGenerateMapper;

/**
 * 价格变更审核完成时候的监听器
 */
public class PricechangeFinishExecutionListenr implements ExecutionListener {

	private static final long serialVersionUID = -6456486497643180579L;

	//定义日志
	private static final Logger LOGGER = LoggerFactory.getLogger(PricechangeFinishExecutionListenr.class);
	
    // 运行时注入service
    public static WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();

    private BasePriceMaintainService basePriceMaintainService;

    private CoreSkuGenerateMapper coreSkuGenerateMapper;

    public PricechangeFinishExecutionListenr(){
        basePriceMaintainService = (BasePriceMaintainService) context.getBean(BasePriceMaintainService.class);
        coreSkuGenerateMapper = (CoreSkuGenerateMapper) context.getBean(CoreSkuGenerateMapper.class);
    }

    @Override
    public void notify(DelegateExecution execution) throws Exception {
    	LOGGER.info("审核结束，执行监听器");
        String businessKey = execution.getVariable("businessKey").toString();
        int priceChangeApplyId =  Integer.valueOf(businessKey.split("_")[1]);
        boolean pass = Boolean.valueOf(execution.getVariable("pass").toString()) ;
        LOGGER.info("审核结果：{}",pass?"通过":"不通过");
        basePriceMaintainService.updatePriceChangeStatus(priceChangeApplyId,pass ? VerifyStatusEnum.Approved.getValue() : VerifyStatusEnum.Reject.getValue());
        //设置启用
        if(pass) {
        	basePriceMaintainService.enablePrice(priceChangeApplyId);
        	//第一次更新价格,审核通过时，设置商城可见
        	SkuPriceChangeApplyDto skuPriceChangeApplyDto = basePriceMaintainService.findBasePriceMaintainById(priceChangeApplyId);
        	List<SkuPriceChangeSaleInfoDto> skuPriceChangeSaleInfoDtoList = skuPriceChangeApplyDto.getSaleInfoList();
        	if(!CollectionUtils.isEmpty(skuPriceChangeSaleInfoDtoList) && skuPriceChangeApplyDto.getEffectSaleInfoHistoryList().size()==1) {
        		basePriceMaintainService.updateVdVisibility(priceChangeApplyId,1);
        	}
        }else {
        	basePriceMaintainService.disablePrice(priceChangeApplyId,"");
        }
        SkuPriceChangeApplyDto skuPriceChangeApplyDto = basePriceMaintainService.getBasicPriceChangeInfoById(priceChangeApplyId);
        coreSkuGenerateMapper.updateSkuPriceVerifyStatusBySkuNo(skuPriceChangeApplyDto.getSkuNo(),pass ? VerifyStatusEnum.Approved.getValue() : VerifyStatusEnum.Reject.getValue());
    }
}
