<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>在线开票申请列表</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="invoiceApplyOnline" datasource="erp-datasourcetarget" fixednumber="2" append_column_url="" append_row_url="" empty_show="" firstcol="numbers">
  <div id="appendHead">
   <style>
.COLOR_RISK{
    background-image: url(http://erp.ivedeng.com/static/images/risk.png);
    width: 28px;
    height: 24px;
   left:0;
    position: absolute;
}

</style>
  </div> 
  <div class="layui-fluid"> 
   <div class="layui-tab"> 
    <ul class="layui-tab-title" id="tab">
<!--        <li class="layui-this"><a href="/ezadmin/list/list-R8XBgDSwsUc">订单列表</a></li>-->
<!--        <li><a href="/ezadmin/list/list-Jb8cQU_twaQ">售后列表</a></li>-->

    </ul> 
   </div> 
   <form class="layui-form" id="search">
    <div class=" layui-inline "><label class="layui-form-label">订单号</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="SALEORDER_NO" placeholder="" style="" alias="a" jdbctype="VARCHAR" data="" datatype="" oper="LIKE">
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">客户名称</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="TRADER_NAME" placeholder="模糊搜索" style="" alias="a" jdbctype="" data="" datatype="" oper="LIKE">
     </div>
    </div>
       <div class=" layui-inline "><label class="layui-form-label">合并日期搜索</label>
           <div class="layui-input-inline">
               <object class=" layui-input list-search-item " type="search-date" name="ADD_TIME,VALID_TIME,PAYMENT_TIME,DELIVERY_TIME,INVOICE_TIME,ARRIVAL_TIME" placeholder="" style="" alias="" jdbctype="" data="" datatype="" oper="between"></object>
           </div>
       </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">归属销售</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="xm-select" name="CURRENT_USER_ID" placeholder="" style="" alias="b" jdbctype="VARCHAR" data="${session.EZ_SESSION_MY_USER_MAP_KEY}" datatype="KVSQLCACHE" oper="IN"></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">销售部门</label>
     <div class="layui-input-inline">
      <object type="search-org" class=" layui-input list-search-item " name="CURRENT_ORG_ID" placeholder="" style="" alias="b" jdbctype="NUMBER" data="" datatype="" oper="IN"></object>
     </div>
    </div>

       <div class=" layui-inline "><label class="layui-form-label">收货时间</label>
           <div class="layui-input-inline">
               <object class=" layui-input list-search-item " type="hidden" name="ARRIVAL_TIME" placeholder="" style="" alias="FROM_UNIXTIME( IF ( a.ARRIVAL_TIME= 0,         NULL,         a.ARRIVAL_TIME) / 1000,         '%Y-%m-%d %H:%i:%s' )" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
           </div>
       </div>
       <div class=" layui-inline ">
           <label class="layui-form-label">开票时间</label>
           <div class="layui-input-inline"><object class=" layui-input list-search-item " type="hidden" name="INVOICE_TIME" placeholder="" style="" alias="FROM_UNIXTIME( IF ( a.INVOICE_TIME= 0,         NULL,         a.INVOICE_TIME) / 1000,         '%Y-%m-%d %H:%i:%s' )" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
           </div>
       </div>
       <div class=" layui-inline "><label class="layui-form-label">发货时间</label>
           <div class="layui-input-inline">
               <object class=" layui-input list-search-item " type="hidden" name="DELIVERY_TIME" placeholder="" style="" alias="FROM_UNIXTIME( IF ( a.DELIVERY_TIME= 0,         NULL,         a.DELIVERY_TIME) / 1000,        '%Y-%m-%d %H:%i:%s' )" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
           </div>
       </div>
       <div class=" layui-inline ">
           <label class="layui-form-label">付款时间</label>
           <div class="layui-input-inline"><object class=" layui-input list-search-item " type="hidden" name="PAYMENT_TIME" placeholder="" style="" alias="FROM_UNIXTIME( IF ( a.PAYMENT_TIME= 0,         NULL,         a.PAYMENT_TIME) / 1000,         '%Y-%m-%d %H:%i:%s' )" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
           </div>
       </div>
       <div class=" layui-inline "><label class="layui-form-label">生效时间</label>
           <div class="layui-input-inline">
               <object class=" layui-input list-search-item " type="hidden" name="VALID_TIME" placeholder="" style="" alias="FROM_UNIXTIME( IF ( a.VALID_TIME= 0,         NULL,         a.VALID_TIME) / 1000,         '%Y-%m-%d %H:%i:%s' )" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
           </div>
       </div>
       <div class=" layui-inline ">
           <label class="layui-form-label">创建时间</label>
           <div class="layui-input-inline"><object class=" layui-input list-search-item " type="hidden" name="ADD_TIME" placeholder="请选择日期" style="" alias="FROM_UNIXTIME( IF ( a.ADD_TIME= 0,         NULL,         a.ADD_TIME) / 1000,        '%Y-%m-%d %H:%i:%s' )" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
           </div>
       </div>
   </form>
   <hr class="layui-border-blue"> 
   <div class="btn-group   bd-highlight" id="tableButton">
       <button item_name="导出" name="导出" url="/ezadmin/list/export-invoiceApplyOnline" opentype="_BLANK_PARAM" windowname="调价列表" style type="table">导出</button>
   </div> 
   <table id="table" class="layui-table" style=" width:100%"> 
    <thead> 
     <tr id="column">
      <th item_name="SALEORDER_NO" name="SALEORDER_NO" body="td-spanlink" url="/orderstream/saleorder/detail.do?saleOrderId=${SALEORDER_ID}&amp;scene=0" opentype="PARENT" windowname="${SALEORDER_NO}" datatype="" data="" style="min-width:200px;
word-break: break-all;position: sticky;" head="18">订单号</th>
      <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-link" url="/trader/customer/baseinfo.do?traderId=${TRADER_ID}" opentype="PARENT" windowname="${TRADER_NAME}" datatype="" data="" style="min-width:200px;
word-break: break-all;" head="18">客户名称</th>
      <th item_name="CURRENT_USER_ID" name="CURRENT_USER_ID" body="td-select" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="SELECT A.USER_ID K,A.USERNAME V  FROM T_USER A
where  IS_DISABLED=0  AND COMPANY_ID=1" style="width:110px" head="th">归属销售</th>

         <th item_name="CURRENT_ORG_ID" name="CURRENT_ORG_ID" body="td-select" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="select ORG_ID K,ORG_NAME V from T_ORGANIZATION where IS_DELETED = 0" style="" head="18">销售部门</th>

         <th item_name="REAL_TOTAL_AMOUNT" name="REAL_TOTAL_AMOUNT" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:right" head="18" jdbctype="NUMBER">订单实际金额</th>


     </tr> 
    </thead> 
    <tbody> 
     <tr> 
      <td colspan="100"> express: <pre id="express" class="layui-code" orderby="order by a.SALEORDER_ID DESC" groupby="">StringBuilder sql=new StringBuilder(" ");



	sql.append(" SELECT
	a.SALEORDER_NO,
	a.SALEORDER_ID,
	a.TRADER_NAME,
	a.TRADER_ID,
	ifnull( a.REAL_TOTAL_AMOUNT, 0.00 ) REAL_TOTAL_AMOUNT,
	b.CURRENT_ORG_ID,
	b.CURRENT_USER_ID");
              sql.append(" from T_SALEORDER a
	LEFT JOIN T_SALEORDER_DATA b ON a.SALEORDER_ID = b.SALEORDER_ID
WHERE a.COMPANY_ID = 1
	AND a.`ORDER_TYPE` IN ( 0, 1, 5, 7, 8, 9 )
AND
IF
	((
			a.CONFIRM_STATUS IS NULL
			OR a.CONFIRM_STATUS = 0
			)
		AND ( a.STATUS = 1 OR a.STATUS = 2 ),
		0,
		1
	) = 1
	AND a.ARRIVAL_STATUS = 2
	AND IFNULL( b.INVOICE_APPLY_FLAY, '0' )= 1
	AND a.INVOICE_STATUS !=2

");






list=search(sql);
return list;</pre> count: <pre id="count" class="layui-code">count(1)</pre></td> 
     </tr> 
    </tbody> 
   </table> 
  </div> 
  <div id="appendFoot">

   <script>

</script>
  </div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
 </body>
</html>