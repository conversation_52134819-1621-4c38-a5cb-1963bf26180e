package com.vedeng.kpi.model.VO;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

public class KpiLogDetailVo {

    private static final long serialVersionUID = 3877352958211794764L;

    /**
     * 销售ID
     */
    private Integer userId;

    /**
     * 公司ID
     */
    private Integer companyId;

    /**
     * 小组ID
     */
    private Integer teamId;

    /**
     * 团队(部门)ID
     */
    private Integer groupId;

    /**
     * 操作类型 0普通付款1付款且满足绩效2普通退款3退款且满足全部退款
     */
    private Integer operation;

    /**
     * 计入kpi时间
     */
    private Date kpiDate;

    /**
     * 订单ID
     */
    private Integer orderId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 客户ID
     */
    private Integer traderId;

    /**
     * 客户名称
     */
    private String traderName;

    /**
     * 售后单ID
     */
    private Integer afterSaleId;

    /**
     * 售后单号
     */
    private String afterSaleNo;

    /**
     * 售后备注
     */
    private String afterSaleComment;

    /**
     * 发生金额(正/负)
     */
    private BigDecimal amount;

    /**
     * 订单实际金额(计入业绩时快照)
     */
    private BigDecimal orderRealAmount;

    /**
     * 订单累计付款金额(不包含账期)
     */
    private BigDecimal orderSumAmount;

    /**
     * 计入业绩的金额
     */
    private BigDecimal kpiAmount;

    /**
     * 商机编号
     */
    private String chanceNo;

    /**
     * 商机ID
     */
    private Integer chanceId;

    /**
     * 商机类型
     */
    private Integer chanceType;

    /**
     * 商机来源
     */
    private Integer chanceSource;

    /**
     * 是否转化商机(0失败1成功)
     */
    private Integer chanceTransFlag;

    /**
     * 商机转化天数
     */
    private Integer chanceTransDays;

    /**
     * 备注
     */
    private String comment;

    private Date addTime;

    private Date updateTime;

    private Integer customersNums;

    private Date lastOrderTime;

    private Date lastChatTime;

    private Long chatApartDays;

    private Date validDate;

    private BigDecimal orderPaidAmount;

    private Date shValidDate;

    private Long assignTime;

    public Long getAssignTime() {
        return assignTime;
    }

    public void setAssignTime(Long assignTime) {
        this.assignTime = assignTime;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public Integer getTeamId() {
        return teamId;
    }

    public void setTeamId(Integer teamId) {
        this.teamId = teamId;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public Integer getOperation() {
        return operation;
    }

    public void setOperation(Integer operation) {
        this.operation = operation;
    }

    public Date getKpiDate() {
        return kpiDate;
    }

    public void setKpiDate(Date kpiDate) {
        this.kpiDate = kpiDate;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Integer getTraderId() {
        return traderId;
    }

    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }

    public String getTraderName() {
        return traderName;
    }

    public void setTraderName(String traderName) {
        this.traderName = traderName;
    }

    public Integer getAfterSaleId() {
        return afterSaleId;
    }

    public void setAfterSaleId(Integer afterSaleId) {
        this.afterSaleId = afterSaleId;
    }

    public String getAfterSaleNo() {
        return afterSaleNo;
    }

    public void setAfterSaleNo(String afterSaleNo) {
        this.afterSaleNo = afterSaleNo;
    }

    public String getAfterSaleComment() {
        return afterSaleComment;
    }

    public void setAfterSaleComment(String afterSaleComment) {
        this.afterSaleComment = afterSaleComment;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getOrderRealAmount() {
        return orderRealAmount;
    }

    public void setOrderRealAmount(BigDecimal orderRealAmount) {
        this.orderRealAmount = orderRealAmount;
    }

    public BigDecimal getOrderSumAmount() {
        return orderSumAmount;
    }

    public void setOrderSumAmount(BigDecimal orderSumAmount) {
        this.orderSumAmount = orderSumAmount;
    }

    public BigDecimal getKpiAmount() {
        return kpiAmount;
    }

    public void setKpiAmount(BigDecimal kpiAmount) {
        this.kpiAmount = kpiAmount;
    }

    public String getChanceNo() {
        return chanceNo;
    }

    public void setChanceNo(String chanceNo) {
        this.chanceNo = chanceNo;
    }

    public Integer getChanceId() {
        return chanceId;
    }

    public void setChanceId(Integer chanceId) {
        this.chanceId = chanceId;
    }

    public Integer getChanceType() {
        return chanceType;
    }

    public void setChanceType(Integer chanceType) {
        this.chanceType = chanceType;
    }

    public Integer getChanceSource() {
        return chanceSource;
    }

    public void setChanceSource(Integer chanceSource) {
        this.chanceSource = chanceSource;
    }

    public Integer getChanceTransFlag() {
        return chanceTransFlag;
    }

    public void setChanceTransFlag(Integer chanceTransFlag) {
        this.chanceTransFlag = chanceTransFlag;
    }

    public Integer getChanceTransDays() {
        return chanceTransDays;
    }

    public void setChanceTransDays(Integer chanceTransDays) {
        this.chanceTransDays = chanceTransDays;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getCustomersNums() {
        return customersNums;
    }

    public void setCustomersNums(Integer customersNums) {
        this.customersNums = customersNums;
    }

    public Date getLastOrderTime() {
        return lastOrderTime;
    }

    public void setLastOrderTime(Date lastOrderTime) {
        this.lastOrderTime = lastOrderTime;
    }

    public Date getLastChatTime() {
        return lastChatTime;
    }

    public void setLastChatTime(Date lastChatTime) {
        this.lastChatTime = lastChatTime;
    }

    public Long getChatApartDays() {
        return chatApartDays;
    }

    public void setChatApartDays(Long chatApartDays) {
        this.chatApartDays = chatApartDays;
    }

    public Date getValidDate() {
        return validDate;
    }

    public void setValidDate(Date validDate) {
        this.validDate = validDate;
    }

    public BigDecimal getOrderPaidAmount() {
        return orderPaidAmount;
    }

    public void setOrderPaidAmount(BigDecimal orderPaidAmount) {
        this.orderPaidAmount = orderPaidAmount;
    }

    public Date getShValidDate() {
        return shValidDate;
    }

    public void setShValidDate(Date shValidDate) {
        this.shValidDate = shValidDate;
    }
}
