package com.vedeng.workbench.model.vo;

import com.vedeng.workbench.model.vo.base.WorkbenchOverview;
import com.vedeng.workbench.model.vo.summary.*;
import lombok.Data;

/**
 * 商机概况(团队).
 * @jira: VDERP-3073【商机管理中心】ERP销售工作台.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/10/25 8:01 下午.
 * @author: Tomcat.Hui.
 */
@Data
public class WorkbenchSummaryOverview extends WorkbenchOverview {

    /**
     * 页头展示数据
     */
    private NumAmounteSummary pageHeaderData;

    /**
     * 数据总览
     */
    private NumAmounteSummary dataOverview;

    /**
     * 待沟通商机
     */
    private CommunicateSummary communicateOverview;

    /**
     * 重点商机
     */
    private ImportentSummary importentOverview;

    /**
     * 预计本周成单
     */
    private ExpectOrderSummary expectOrderOverview;

    /**
     * 预警商机
     */
    private WarningSummary warningOverview;

}
