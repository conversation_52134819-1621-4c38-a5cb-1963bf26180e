package com.vedeng.erp.finance.dto;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * <b>Description:</b><br> 售后开票申请
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AfterSaleApplyDto extends AfterSaleApplyInfoDto {

    private BigDecimal totalAmount;

    private Integer invoiceInfoType;

    private List<InvoiceCheckResultDto.InvoiceCheckResultDetailDto> invoiceCheckResultDetailDtoList;

    private String advanceValidReason;

}