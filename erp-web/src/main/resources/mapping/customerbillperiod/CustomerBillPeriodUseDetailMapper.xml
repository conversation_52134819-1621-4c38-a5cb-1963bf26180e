<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.customerbillperiod.dao.CustomerBillPeriodUseDetailMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.customerbillperiod.model.CustomerBillPeriodUseDetail">
    <id column="BILL_PERIOD_USE_DETAIL_ID" jdbcType="BIGINT" property="billPeriodUseDetailId" />
    <result column="BILL_PERIOD_ID" jdbcType="BIGINT" property="billPeriodId" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="CUSTOMER_ID" jdbcType="BIGINT" property="customerId" />
    <result column="SETTLEMENT_PERIOD" jdbcType="INTEGER" property="settlementPeriod" />
    <result column="SETTLEMENT_TYPE" jdbcType="TINYINT" property="settlementType" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="UNRETURNED_AMOUNT" jdbcType="DECIMAL" property="unreturnedAmount" />
    <result column="OCCUPANCY" jdbcType="TINYINT" property="occupancy" />
    <result column="USE_TYPE" jdbcType="TINYINT" property="useType" />
    <result column="RELATED_ID" jdbcType="BIGINT" property="relatedId" />
    <result column="PARENT_USE_DETAIL_ID" jdbcType="BIGINT" property="parentUseDetailId" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
  </resultMap>
  <sql id="Base_Column_List">
    BILL_PERIOD_USE_DETAIL_ID, BILL_PERIOD_ID, COMPANY_ID, CUSTOMER_ID, SETTLEMENT_PERIOD, 
    SETTLEMENT_TYPE, AMOUNT, UNRETURNED_AMOUNT, OCCUPANCY, USE_TYPE, RELATED_ID, PARENT_USE_DETAIL_ID, ADD_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_CUSTOMER_BILL_PERIOD_USE_DETAIL
    where BILL_PERIOD_USE_DETAIL_ID = #{billPeriodUseDetailId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from T_CUSTOMER_BILL_PERIOD_USE_DETAIL
    where BILL_PERIOD_USE_DETAIL_ID = #{billPeriodUseDetailId,jdbcType=BIGINT}
  </delete>
  <delete id="removeCustomerBillPeriodFrozenUseDetail">
    delete from T_CUSTOMER_BILL_PERIOD_USE_DETAIL
    where COMPANY_ID = #{companyId}
      and CUSTOMER_ID = #{customerId}
      and RELATED_ID = #{relatedId}
      and OCCUPANCY = 0
      and USE_TYPE = 1
  </delete>
  <insert id="insert" keyColumn="BILL_PERIOD_USE_DETAIL_ID" keyProperty="billPeriodUseDetailId" parameterType="com.vedeng.customerbillperiod.model.CustomerBillPeriodUseDetail" useGeneratedKeys="true">
    insert into T_CUSTOMER_BILL_PERIOD_USE_DETAIL (BILL_PERIOD_ID, COMPANY_ID, CUSTOMER_ID, 
      SETTLEMENT_PERIOD, SETTLEMENT_TYPE, AMOUNT, UNRETURNED_AMOUNT,
      OCCUPANCY, USE_TYPE, RELATED_ID, 
      PARENT_USE_DETAIL_ID, ADD_TIME)
    values (#{billPeriodId,jdbcType=BIGINT}, #{companyId,jdbcType=INTEGER}, #{customerId,jdbcType=BIGINT}, 
      #{settlementPeriod,jdbcType=INTEGER}, #{settlementType,jdbcType=TINYINT}, #{amount,jdbcType=DECIMAL},
      #{unreturnedAmount,jdbcType=DECIMAL}, #{occupancy,jdbcType=TINYINT}, #{useType,jdbcType=TINYINT}, #{relatedId,jdbcType=BIGINT},
      #{parentUseDetailId,jdbcType=BIGINT}, #{addTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="BILL_PERIOD_USE_DETAIL_ID" keyProperty="billPeriodUseDetailId" parameterType="com.vedeng.customerbillperiod.model.CustomerBillPeriodUseDetail" useGeneratedKeys="true">
    insert into T_CUSTOMER_BILL_PERIOD_USE_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="billPeriodId != null">
        BILL_PERIOD_ID,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="customerId != null">
        CUSTOMER_ID,
      </if>
      <if test="settlementPeriod != null">
        SETTLEMENT_PERIOD,
      </if>
      <if test="settlementType != null">
        SETTLEMENT_TYPE,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="unreturnedAmount != null">
        UNRETURNED_AMOUNT,
      </if>
      <if test="occupancy != null">
        OCCUPANCY,
      </if>
      <if test="useType != null">
        USE_TYPE,
      </if>
      <if test="relatedId != null">
        RELATED_ID,
      </if>
      <if test="parentUseDetailId != null">
        PARENT_USE_DETAIL_ID,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="billPeriodId != null">
        #{billPeriodId,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=BIGINT},
      </if>
      <if test="settlementPeriod != null">
        #{settlementPeriod,jdbcType=INTEGER},
      </if>
      <if test="settlementType != null">
        #{settlementType,jdbcType=TINYINT},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="unreturnedAmount != null">
        #{unreturnedAmount,jdbcType=DECIMAL},
      </if>
      <if test="occupancy != null">
        #{occupancy,jdbcType=TINYINT},
      </if>
      <if test="useType != null">
        #{useType,jdbcType=TINYINT},
      </if>
      <if test="relatedId != null">
        #{relatedId,jdbcType=BIGINT},
      </if>
      <if test="parentUseDetailId != null">
        #{parentUseDetailId,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.customerbillperiod.model.CustomerBillPeriodUseDetail">
    update T_CUSTOMER_BILL_PERIOD_USE_DETAIL
    <set>
      <if test="billPeriodId != null">
        BILL_PERIOD_ID = #{billPeriodId,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        CUSTOMER_ID = #{customerId,jdbcType=BIGINT},
      </if>
      <if test="settlementPeriod != null">
        SETTLEMENT_PERIOD = #{settlementPeriod,jdbcType=INTEGER},
      </if>
      <if test="settlementType != null">
        SETTLEMENT_TYPE = #{settlementType,jdbcType=TINYINT},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="unreturnedAmount != null">
        UNRETURNED_AMOUNT = #{unreturnedAmount,jdbcType=DECIMAL},
      </if>
      <if test="occupancy != null">
        OCCUPANCY = #{occupancy,jdbcType=TINYINT},
      </if>
      <if test="useType != null">
        USE_TYPE = #{useType,jdbcType=TINYINT},
      </if>
      <if test="relatedId != null">
        RELATED_ID = #{relatedId,jdbcType=BIGINT},
      </if>
      <if test="parentUseDetailId != null">
        PARENT_USE_DETAIL_ID = #{parentUseDetailId,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
    </set>
    where BILL_PERIOD_USE_DETAIL_ID = #{billPeriodUseDetailId,jdbcType=BIGINT}
  </update>

  <update id="occupancyCustomerBillPeriodUseDetailList">
    update T_CUSTOMER_BILL_PERIOD_USE_DETAIL

    set OCCUPANCY = 1
    where COMPANY_ID = #{companyId} and CUSTOMER_ID = #{customerId} and RELATED_ID = #{orderId} and USE_TYPE = 1
  </update>

  <select id="getFreezingAmountByBillPeriodId" resultType="java.math.BigDecimal">
    select ifnull(sum(UNRETURNED_AMOUNT),0)
    from T_CUSTOMER_BILL_PERIOD_USE_DETAIL where BILL_PERIOD_ID = #{billPeriodId} and USE_TYPE = 1
  </select>
  <select id="getCustomerBillPeriodUseDetailListByBillPeriodId" resultType="com.vedeng.customerbillperiod.model.CustomerBillPeriodUseDetail">
    select *
    from T_CUSTOMER_BILL_PERIOD_USE_DETAIL where BILL_PERIOD_ID = #{billPeriodId}
  </select>
  <select id="getCustomerBillPeriodUseDetailListByOrderId" resultType="com.vedeng.customerbillperiod.model.CustomerBillPeriodUseDetail">
    select *
    from T_CUSTOMER_BILL_PERIOD_USE_DETAIL
    where COMPANY_ID = #{companyId}
      and CUSTOMER_ID = #{customerId}
      and RELATED_ID = #{orderId}
      and USE_TYPE = 1
    union all
    select b.*
    from T_CUSTOMER_BILL_PERIOD_USE_DETAIL a
           join T_CUSTOMER_BILL_PERIOD_USE_DETAIL b on a.BILL_PERIOD_USE_DETAIL_ID = b.PARENT_USE_DETAIL_ID
    where a.COMPANY_ID = #{companyId}
      and a.CUSTOMER_ID = #{customerId}
      and a.RELATED_ID = #{orderId}
      and a.USE_TYPE = 1
  </select>
  <select id="batchGetCustomerBillPeriodUseDetailListByBillPeriodIdList"
          resultType="com.vedeng.customerbillperiod.model.CustomerBillPeriodUseDetail">
    select *
    from T_CUSTOMER_BILL_PERIOD_USE_DETAIL where COMPANY_ID = #{companyId} and BILL_PERIOD_ID in
    <foreach collection="billPeriodIdList" index="index" item="item" open="(" close=")" separator=",">
      #{item,jdbcType=BIGINT}
    </foreach>
    <if test="useDetailStart != null and useDetailStart > 0">
      and ADD_TIME &gt; #{useDetailStart}
    </if>
    <if test="useDetailEnd != null and useDetailEnd > 0">
      and ADD_TIME &lt; #{useDetailEnd}
    </if>
  </select>
  <select id="getUseDetailListByRelatedIdAndType" resultType="com.vedeng.customerbillperiod.model.CustomerBillPeriodUseDetail">
    select *
    from T_CUSTOMER_BILL_PERIOD_USE_DETAIL where COMPANY_ID = #{companyId} and CUSTOMER_ID = #{customerId} and RELATED_ID = #{relatedId} and
      USE_TYPE = #{useType}
  </select>
  <select id="getUnreturnedAmountByBillPeriodIdList" resultType="com.vedeng.customerbillperiod.model.CustomerBillPeriodUseDetail">
    select BILL_PERIOD_ID, sum(ifnull(UNRETURNED_AMOUNT, 0)) UNRETURNED_AMOUNT
    from T_CUSTOMER_BILL_PERIOD_USE_DETAIL
    where BILL_PERIOD_ID in
    <foreach collection="billPeriodIdList" index="index" item="item" open="(" close=")" separator=",">
      #{item,jdbcType=BIGINT}
    </foreach>
      and OCCUPANCY = 1
    group by BILL_PERIOD_ID
  </select>
  <select id="getCountUsedGroupByBillPeriod" resultType="com.vedeng.customerbillperiod.model.CustomerBillPeriodUseDetail">
    select BILL_PERIOD_ID, count(1) as OCCUPANCY
    from T_CUSTOMER_BILL_PERIOD_USE_DETAIL
    where BILL_PERIOD_ID in
      <foreach collection="billPeriodIdList" index="index" item="item" open="(" close=")" separator="," >
        #{item,jdbcType=BIGINT}
      </foreach>
      and OCCUPANCY = 1
      and USE_TYPE = 1
    group by BILL_PERIOD_ID
  </select>
  <select id="getRevertBillPeriodInfo" resultType="com.vedeng.customerbillperiod.model.CustomerBillPeriodUseDetail">
    SELECT * FROM T_CUSTOMER_BILL_PERIOD_USE_DETAIL
    WHERE USE_TYPE IN (3,4) AND PARENT_USE_DETAIL_ID=#{parentUseDetailId,jdbcType=BIGINT}
    AND BILL_PERIOD_ID=#{billPeriodId,jdbcType=BIGINT}
  </select>
  <select id="getBillPeriodDetailManage" resultType="com.vedeng.customerbillperiod.dto.CustomerBillPeriodDetailManageDto">
        SELECT
      A.BILL_PERIOD_ID,
      A.RELATED_ID AS orderRelatedId,
      B.BILL_PERIOD_OVERDUE_MANAGEMENT_CODE,
      A.SETTLEMENT_TYPE,
      B.TYPE,
      B.RELATED_ID,
      B.ADD_TIME,
      A.SETTLEMENT_PERIOD,
      (B.AMOUNT + (CASE WHEN D.AMOUNT IS NULL THEN 0 ELSE D.AMOUNT END)) AS CALAMOUNT,
      B.AMOUNT AS AMOUNT,
      B.OVERDUE_DAYS
  FROM
      T_CUSTOMER_BILL_PERIOD_USE_DETAIL A
      LEFT JOIN T_CUSTOMER_BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL B ON A.BILL_PERIOD_USE_DETAIL_ID = B.BILL_PERIOD_USE_DETAIL_ID AND (B.TYPE=1 OR B.TYPE = 2)
      LEFT JOIN (SELECT SUM(C.AMOUNT) AS AMOUNT,C.PARENT_MANAGEMENT_DETAIL_ID
      FROM T_CUSTOMER_BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL C WHERE C.BILL_PERIOD_USE_DETAIL_ID = #{parentUseDetailId,jdbcType=BIGINT} and C.TYPE
        in (4,6)
      GROUP BY C.PARENT_MANAGEMENT_DETAIL_ID) D ON D.PARENT_MANAGEMENT_DETAIL_ID = B.BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL_ID
  WHERE
      A.BILL_PERIOD_USE_DETAIL_ID = #{parentUseDetailId,jdbcType=BIGINT}
  </select>
    <select id="getCustomerBillPeriodUseDetailListOfUnOverdueManagedByOrderId"
            resultType="com.vedeng.customerbillperiod.model.CustomerBillPeriodUseDetail">
      select ud.BILL_PERIOD_USE_DETAIL_ID,
             ud.BILL_PERIOD_ID,
             ud.COMPANY_ID,
             ud.CUSTOMER_ID,
             ud.SETTLEMENT_PERIOD,
             ud.SETTLEMENT_TYPE,
             ud.USE_TYPE,
             ud.RELATED_ID,
             ud.AMOUNT,
             (ud.UNRETURNED_AMOUNT - ifnull(sum(md.AMOUNT), 0)) UNRETURNED_AMOUNT
      from T_CUSTOMER_BILL_PERIOD_USE_DETAIL ud
             left join T_CUSTOMER_BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL md
                       on ud.BILL_PERIOD_USE_DETAIL_ID = md.BILL_PERIOD_USE_DETAIL_ID
      where ud.COMPANY_ID = #{companyId}
        and ud.CUSTOMER_ID = #{customerId}
        and ud.RELATED_ID = #{orderId}
        and ud.USE_TYPE = 1
        and ud.OCCUPANCY = 1
      group by ud.BILL_PERIOD_USE_DETAIL_ID
    </select>
  <select id="getCustomerBillPeriodUseDetailListOfUnRiskManagedByOrderId"
          resultType="com.vedeng.customerbillperiod.model.CustomerBillPeriodUseDetail">
    select ud.BILL_PERIOD_USE_DETAIL_ID,
           ud.BILL_PERIOD_ID,
           ud.COMPANY_ID,
           ud.CUSTOMER_ID,
           ud.SETTLEMENT_PERIOD,
           ud.SETTLEMENT_TYPE,
           ud.USE_TYPE,
           ud.RELATED_ID,
           ud.AMOUNT,
           (ud.UNRETURNED_AMOUNT - ifnull(sum(md.AMOUNT), 0)) UNRETURNED_AMOUNT
    from T_CUSTOMER_BILL_PERIOD_USE_DETAIL ud
           left join T_CUSTOMER_BILL_PERIOD_RISK_MANAGEMENT_DETAIL md
                     on ud.BILL_PERIOD_USE_DETAIL_ID = md.BILL_PERIOD_USE_DETAIL_ID
    where ud.COMPANY_ID = #{companyId}
      and ud.CUSTOMER_ID = #{customerId}
      and ud.RELATED_ID = #{orderId}
      and ud.USE_TYPE = 1
      and ud.OCCUPANCY = 1
    group by ud.BILL_PERIOD_USE_DETAIL_ID
  </select>
  <select id="getOccupancyCustomerBillPeriodUseDetail" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_CUSTOMER_BILL_PERIOD_USE_DETAIL
    where COMPANY_ID = #{companyId} and CUSTOMER_ID = #{customerId} and RELATED_ID = #{orderId} and USE_TYPE = 1 and
    OCCUPANCY = 1
  </select>

    <select id="getUnReturnedBillPeriodCustomerIds" resultType="java.lang.Integer">
      SELECT DISTINCT
          CUSTOMER_ID
      FROM
          T_CUSTOMER_BILL_PERIOD_USE_DETAIL
      GROUP BY
          CUSTOMER_ID
      HAVING
          SUM( UNRETURNED_AMOUNT ) > 0
    </select>


  <select id="getCustomerBillPeriodUseDetailLisOrderId"
          resultType="com.vedeng.customerbillperiod.model.CustomerBillPeriodUseDetail">
    select ud.BILL_PERIOD_USE_DETAIL_ID,
    ud.BILL_PERIOD_ID,
    ud.COMPANY_ID,
    ud.CUSTOMER_ID,
    ud.SETTLEMENT_PERIOD,
    ud.SETTLEMENT_TYPE,
    ud.USE_TYPE,
    ud.RELATED_ID,
    ud.AMOUNT
    from T_CUSTOMER_BILL_PERIOD_USE_DETAIL ud
    where ud.COMPANY_ID = #{companyId}
    and ud.CUSTOMER_ID = #{customerId}
    and ud.RELATED_ID = #{orderId}
    and ud.USE_TYPE = 1
    and ud.OCCUPANCY = 1
    group by ud.BILL_PERIOD_USE_DETAIL_ID
  </select>
</mapper>