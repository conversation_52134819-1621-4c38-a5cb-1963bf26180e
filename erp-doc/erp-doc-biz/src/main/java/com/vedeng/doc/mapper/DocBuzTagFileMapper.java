package com.vedeng.doc.mapper;

import com.vedeng.doc.dto.DocBuzTagFileDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Entity com.vedeng.doc.domain.entity.DocBuzTagFile
 */
public interface DocBuzTagFileMapper{

    /**
     * 查询商品下载文件信息
     * @param docBuzIdList
     * @return
     */
    List<DocBuzTagFileDto> selectDocGoodsListByBuzIdList(@Param("docBuzIdList") List<Integer> docBuzIdList);

    /**
     * 查询厂商下载文件信息
     * @param docBuzIdList
     * @return
     */
    List<DocBuzTagFileDto> selectDocSupplierListByBuzIdList(@Param("docBuzIdList") List<Integer> docBuzIdList);
}




