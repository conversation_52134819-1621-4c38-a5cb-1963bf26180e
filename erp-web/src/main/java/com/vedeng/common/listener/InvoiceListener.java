package com.vedeng.common.listener;

import com.alibaba.fastjson.JSON;
import com.google.common.eventbus.Subscribe;
import com.vedeng.common.core.config.IObserver;
import com.vedeng.erp.finance.domain.dto.InvoiceRedConfirmationDto;
import com.vedeng.erp.finance.dto.InvoiceRedConfirmationApiDto;
import com.vedeng.erp.finance.dto.InvoiceRedConfirmationItemApiDto;
import com.vedeng.finance.service.InvoiceAfterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 发票相关业务监听器
 * @date 2023/10/20 16:56
 */
@Component
@Slf4j
public class InvoiceListener implements IObserver {


    @Autowired
    InvoiceAfterService invoiceAfterService;

    /**
     * 开红票注册监听事件
     *
     * @param invoiceRedConfirmationDto invoiceRedConfirmationDto
     */
    @Subscribe
    @Transactional(rollbackFor = Throwable.class)
    public void openRedInvoice(InvoiceRedConfirmationApiDto invoiceRedConfirmationDto) {
        log.info("红字确认单信息 -> {}", JSON.toJSONString(invoiceRedConfirmationDto));
        invoiceAfterService.preSaveFullyDigitalRedInvoice(invoiceRedConfirmationDto);
    }
}
