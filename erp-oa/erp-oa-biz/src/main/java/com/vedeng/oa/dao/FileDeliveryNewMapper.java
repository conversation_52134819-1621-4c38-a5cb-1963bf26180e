package com.vedeng.oa.dao;

import com.vedeng.oa.model.FileDeliveryNew;
import com.vedeng.oa.model.FileDeliveryNewExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FileDeliveryNewMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    long countByExample(FileDeliveryNewExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    int deleteByExample(FileDeliveryNewExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    int deleteByPrimaryKey(Integer fileDeliveryId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    int insert(FileDeliveryNew record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    int insertSelective(FileDeliveryNew record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    List<FileDeliveryNew> selectByExampleWithBLOBs(FileDeliveryNewExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    List<FileDeliveryNew> selectByExample(FileDeliveryNewExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    FileDeliveryNew selectByPrimaryKey(Integer fileDeliveryId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    int updateByExampleSelective(@Param("record") FileDeliveryNew record, @Param("example") FileDeliveryNewExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    int updateByExampleWithBLOBs(@Param("record") FileDeliveryNew record, @Param("example") FileDeliveryNewExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    int updateByExample(@Param("record") FileDeliveryNew record, @Param("example") FileDeliveryNewExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    int updateByPrimaryKeySelective(FileDeliveryNew record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    int updateByPrimaryKeyWithBLOBs(FileDeliveryNew record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    int updateByPrimaryKey(FileDeliveryNew record);
}