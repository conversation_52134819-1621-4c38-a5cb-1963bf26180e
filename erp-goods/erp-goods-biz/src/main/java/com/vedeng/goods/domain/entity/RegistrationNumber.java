package com.vedeng.goods.domain.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 注册证号信息表
 *
 * @TableName T_REGISTRATION_NUMBER
 */
@Data
public class RegistrationNumber implements Serializable {

    private Integer registrationNumberId;

    /**
     * 注册证号/备案凭证号
     */
    private String registrationNumber;

    /**
     * 管理类别:一类,二类,三类
     */
    private Integer manageCategoryLevel;

    /**
     * 生产厂家（企业）ID
     */
    private Integer productCompanyId;

    /**
     * 生产地址
     */
    private String productionAddress;

    /**
     * 产品名称(中文)
     */
    private String productChineseName;

    /**
     * 产品名称(英文)
     */
    private String productEnglishName;

    /**
     * 产品分类ID[旧国标]
     */
    private Integer productCategoryId;

    /**
     * 产品分类名称[旧国标分类名称]
     */
    private String productCategoryName;

    /**
     * 规格型号
     */
    private String model;

    /**
     * 发证日期（批准日期）
     */
    private Long issuingDate;

    /**
     * 有效期(截止日期)
     */
    private Long effectiveDate;

    /**
     * 过期处理状态 0 未到期 1待处理 2已处理
     */
    private Integer dealStatus;

    /**
     * 审批部门（发证机关）
     */
    private String approvalDepartment;

    /**
     * 注册商标
     */
    private String trademark;

    /**
     * 邮编
     */
    private String zipCode;

    /**
     * 注册代理(人,公司名称)
     */
    private String registeredAgent;

    /**
     * 注册代理(人,公司名称)地址
     */
    private String registeredAgentAddress;

    /**
     * 结构与组成[产品性能结构与组成]
     */
    private String proPerfStruAndComp;

    /**
     * 适用范围
     */
    private String productUseRange;

    /**
     * 其他内容
     */
    private String otherContents;

    /**
     * 产品标准
     */
    private String productStandards;

    /**
     * 类型：1：进口器械 2：国产器械
     */
    private Integer type;

    /**
     * 生产国或地区
     */
    private String productionOrCountry;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 售后服务机构
     */
    private String aftersaleServiceOrg;

    /**
     * 变更时间
     */
    private Long changeDate;

    /**
     * 变更情况
     */
    private String changeContents;

    /**
     * 是否发布:1 是/ 0 否
     */
    private Integer isRelease;

    /**
     * 预期用途（体外诊断试剂）
     */
    private String expectedUsage;

    /**
     * 产品存储条件及有效期（体外诊断试剂）
     */
    private String storageCondAndEffectiveDate;

    /**
     * 主要组成成分（体外诊断试剂）
     */
    private String mainProPerfStruAndComp;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Long modTime;

    /**
     * 更新人
     */
    private Integer updater;

    /**
     * 附件
     */
    private String attachment;

    /**
     * 注册证类型，0国产注册证，1国产备案，2进口注册证，3进口备案
     */
    private Byte category;

    /**
     * 产品存储及有效期
     */
    private String storageAndExpiryDate;

    /**
     * 是否委托生产,0否,1是
     */
    private Byte isSubcontractProduction;

    /**
     * 生产企业名称
     */
    private String productionEnterpriseName;

    /**
     * 营业执照发证日期
     */
    private Long bcIssueDate;

    /**
     * 生产许可证
     */
    private String productCompanyLicence;

    /**
     * 制造商 ID
     */
    private Integer manufacturerId;

    private static final long serialVersionUID = 1L;
}