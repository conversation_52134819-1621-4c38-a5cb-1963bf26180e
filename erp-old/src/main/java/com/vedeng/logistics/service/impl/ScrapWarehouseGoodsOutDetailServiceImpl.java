package com.vedeng.logistics.service.impl;

import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.logistics.eums.WarehouseGoodsOutEnum;
import com.vedeng.logistics.model.vo.WarehouseGoodsOutLogVo;
import com.vedeng.logistics.model.vo.WarehouseGoodsOutVo;
import com.wms.model.po.WmsOutputOrder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @description: 报废出库
 * @author: Zeno.zuo
 * @email: <EMAIL>
 * @date: 2022/11/16 15:34
 **/
@Service
@Slf4j
public class ScrapWarehouseGoodsOutDetailServiceImpl extends BaseWarehouseGoodsOutDetailService{

    /**
     * wms出库单类型-报废出库
     */
    private final Integer WMS_OUTPUT_ORDER_TYPE_SCRAP = 2;

    private final String DETAIL_URL_SCRAP_OUT = "/wms/scrapOut/scrapDetail.do?scrappedOutId=";


    @Override
    public WarehouseGoodsOutVo detailWarehouseGoodsOut(String outInNo) {
        WarehouseGoodsOutVo warehouseGoodsOutVo = commonDetailWarehouseGoodsOut(outInNo, WarehouseGoodsOutEnum.SCRAP_WAREHOUSE_OUT.getErpCode());
        if (warehouseGoodsOutVo != null){
            String wmsScrapOutOrderNo = warehouseGoodsOutVo.getRelateNo();
            if (StringUtils.isNotBlank(wmsScrapOutOrderNo)){
                WmsOutputOrder wmsScrapOutOrder = wmsOutputOrderMapper.getWmsOutputOrderByOrderNoAndType(wmsScrapOutOrderNo, WMS_OUTPUT_ORDER_TYPE_SCRAP);
                if(Objects.nonNull(wmsScrapOutOrder)){
                    warehouseGoodsOutVo.setBelongUserName(wmsScrapOutOrder.getCreator());
                    if(StringUtils.isNotBlank(wmsScrapOutOrder.getCreator())){
                        User scrapOutCreator = userMapper.getUserByUserName(wmsScrapOutOrder.getCreator());
                        if(scrapOutCreator != null && StringUtils.isNotBlank(scrapOutCreator.getOrgName())){
                            warehouseGoodsOutVo.setBelongUserOrgName(scrapOutCreator.getOrgName());
                        }
                    }
                }

            }
        }
        return warehouseGoodsOutVo;
    }

    @Override
    public List<WarehouseGoodsOutLogVo> relatedNoWarehouseGoodsOutLogList(String relatedNo) {
        List<WarehouseGoodsOutLogVo> warehouseGoodsOutLogVoList = warehouseGoodsOutInMapper.selectWarehouseGoodsOutLogListByRelatedNo(WarehouseGoodsOutEnum.SCRAP_WAREHOUSE_OUT.getErpCode(), relatedNo);
        return CollectionUtils.isNotEmpty(warehouseGoodsOutLogVoList) ? warehouseGoodsOutLogVoList : new ArrayList<>();
    }

    @Override
    public String relatedNoDetailRedirect(String relatedNo) {
        WmsOutputOrder wmsOutputOrder = wmsOutputOrderMapper.getWmsOutputOrderByOrderNoAndType(relatedNo,WMS_OUTPUT_ORDER_TYPE_SCRAP);
        String url = "";
        if (wmsOutputOrder != null){
            url = REDIRECT_URL_PREFIX + DETAIL_URL_SCRAP_OUT + wmsOutputOrder.getId();
        }
        return url;
    }
}
