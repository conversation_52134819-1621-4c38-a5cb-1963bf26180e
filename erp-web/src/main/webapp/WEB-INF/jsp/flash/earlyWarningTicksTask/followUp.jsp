<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="跟进" scope="application" />
<%@ include file="../../common/common.jsp"%>

<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/tag.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src='<%= basePath %>static/js/flash/earlyWarningTicksTask/followUp.js?rnd=${resourceVersionKey}'></script>

<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<div class="formpublic">

    <form action="${pageContext.request.contextPath}/flash/earlyWarningTicksTask/followUp.do" id="myform1" method="post">
        <div>
            <ul>
                <li>
                    <table class="table table-bordered table-striped table-condensed table-centered">
                        <tbody>
                        <tr>
                            <td class="table-smaller">催票金额</td>
                            <td>${totalPrice}</td>
                            <td class="table-smaller">可开金额</td>
                            <td>
                                <input  name="canTicketAmount" id="canTicketAmount" value="" readonly="readonly"/>
                                <input type="hidden" name="totalPrice" id="totalPrice" value="${totalPrice}" readonly="readonly"/>
                                <input type="hidden" name="earlyWarningTaskIds" id="earlyWarningTaskIds" value="${earlyWarningTaskIds}" readonly="readonly"/>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </li>
                <li>
                    <label class="infor_name" style="width: 105px;">跟进结果</label>
                    <select class="input-middle" name="followUpStatus" id="followUpStatus" onchange="changeCanTicket()">
                        <option value="1">可开票</option>
                        <option value="0">不可开票</option>
                    </select>
                </li>
                <li id="canTicketTimeLi">
                    <label class="infor_name" style="width: 105px;"><span>*</span>可开时间</label>
                    <input class="Wdate input-small " placeholder="请选择时间" value="" autocomplete="off"
                           onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',minDate:'%y-%M-%d %H:%m:%s'})" name="canTicketTime" id="canTicketTime"/>
                </li>
                <li id="cantTicketReson">
                    <label class="infor_name" style="width: 105px;"><span>*</span>不可开原因</label>
                    <textarea name="reson" id="reson" cols="40" rows="5"></textarea>
                </li>
                <li>
                    <table class="table table-bordered table-striped table-condensed table-centered">
                        <thead>
                            <tr>
                                <th class="wid10">供应商</th>
                                <th class="wid10">采购单号</th>
                                <th class="wid10">产品名称</th>
                                <th class="wid5">单位</th>
                                <th class="wid5">采购单价</th>
                                <th class="wid7">催票数量/催票金额</th>
                                <th class="wid10">本次到货时间</th>
                                <th class="wid5">可开金额</th>
                            </tr>
                        </thead>
                        <tbody>
                        <c:choose>
                            <c:when test="${not empty earlyWarningTicksList}">
                                <c:forEach var="earlyWarningTicksList" items="${earlyWarningTicksList}" varStatus="num">
                                    <tr>
                                        <td>
                                            ${earlyWarningTicksList.traderName}
                                        </td>
                                        <td>${earlyWarningTicksList.saleorderNo}</td>
                                        <td>${earlyWarningTicksList.skuName}</td>
                                        <td>${earlyWarningTicksList.skuUnit}</td>
                                        <td>${earlyWarningTicksList.price}</td>
                                        <td>${earlyWarningTicksList.urgingTicketNum-earlyWarningTicksList.alreadyInputNum}/
                                                ${earlyWarningTicksList.urgingTicketAmount-earlyWarningTicksList.alreadyInputAmount}</td>
                                        <td>${earlyWarningTicksList.addtime}</td>
                                        <td>
                                            <input name="canTicketAmountInput" style="width: 80%" value="${earlyWarningTicksList.urgingTicketAmount-earlyWarningTicksList.alreadyInputAmount}"
                                                   autocomplete="off" onchange="computeTotalCanTicketAmount()">
                                            <input type="hidden" name="earlyWarningTaskId" value="${earlyWarningTicksList.earlyWarningTaskId}" >
                                            <input type="hidden" name="nowUrgingTicketAmount" value="${earlyWarningTicksList.urgingTicketAmount-earlyWarningTicksList.alreadyInputAmount}" >
                                            <input type="hidden" name="nowUrgingTicketNum" value="${earlyWarningTicksList.urgingTicketNum-earlyWarningTicksList.alreadyInputNum}" >
                                        </td>
                                    </tr>
                                </c:forEach>
                            </c:when>
                            <c:otherwise>
                                <tr><td colspan="8">读取数据出错</td></tr>
                            </c:otherwise>
                        </c:choose>
                        </tbody>
                    </table>
                </li>
                <li>
                    <div class="table-buttons">
                        <button type="button" class="bt-bg-style bg-light-green bt-small mr10" id="saveFollowUp" >保存</button>
                    </div>
                </li>
            </ul>
        </div>
    </form>
</div>
