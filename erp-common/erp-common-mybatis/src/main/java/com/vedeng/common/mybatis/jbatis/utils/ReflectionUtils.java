package com.vedeng.common.mybatis.jbatis.utils;


import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * <p>Description: 反射工具类</p>
 *
 * <AUTHOR>
 * @version V1.0
 */
@Slf4j
public class ReflectionUtils {


    /**
     * 获得参数化类型的泛型类型，取第一个参数的泛型类型，（默认去的第一个）
     *
     * @param clazz 参数化类型
     * @return 泛型类型
     */
    @SuppressWarnings("rawtypes")
    public static Class getClassGenricType(final Class clazz) {
        return getClassGenricType(clazz, 0);
    }

    /**
     * 根据参数索引获得参数化类型的泛型类型，（通过索引取得）
     *
     * @param clazz 参数化类型
     * @param index 参数索引
     * @return 泛型类型
     */
    @SuppressWarnings("rawtypes")
    public static Class getClassGenricType(
            final Class clazz, final int index) {
        Type genType = clazz.getGenericSuperclass();
        if (!(genType instanceof ParameterizedType)) {
            return Object.class;
        }
        Type[] params = ((ParameterizedType) genType).getActualTypeArguments();
        if (index >= params.length || index < 0) {
            return Object.class;
        }
        if (!(params[index] instanceof Class)) {
            return Object.class;
        }
        return (Class) params[index];
    }

    /**
     * 反射 取值、设值,合并两个对象(Field same only )
     *
     * @param from
     * @param to
     */
    public static <T> void copyProperties(T fromobj, T toobj) {
        try {
            BeanUtil.copyProperties(fromobj, toobj);
        } catch (Exception e) {
            log.error("【copyProperties】处理异常",e);
        }
    }


    /**
     * 调用Getter方法
     *
     * @param obj          对象
     * @param propertyName 属性名
     * @return
     */
    public static Object invokeGetterMethod(Object obj, String propertyName) {
        return BeanUtil.getProperty(obj, propertyName);
    }


    /**
     * 调用Setter方法,不指定参数的类型
     *
     * @param obj
     * @param propertyName
     * @param value
     */
    public static void invokeSetterMethod(Object obj, String propertyName,
                                          Object value) {
        BeanUtil.setProperty(obj, propertyName, value);
    }

    /**
     * 根据属性类型做响应的转换
     *
     * @param obj
     * @param propertyName
     * @return Object
     */
    public static Object getPkObj(Object obj, String propertyName) {
        return invokeGetterMethod(obj, propertyName);
    }
}
