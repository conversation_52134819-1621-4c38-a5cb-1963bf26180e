package com.vedeng.erp.saleorder.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.saleorder.model.dto.BaseDataInfoDto;
import com.vedeng.erp.saleorder.model.dto.ext.BusinessChanceDateInfo;
import com.vedeng.erp.saleorder.service.WorkbenchQueryStep;
import com.vedeng.kpi.model.DTO.KpiDailyCountExtDto;
import com.vedeng.kpi.model.DTO.KpiUserInfoDto;
import com.vedeng.kpi.service.KpiDailyCountService;
import com.vedeng.kpi.share.KpiCommonConstant;
import com.vedeng.order.model.dto.SaleorderUserInfoDto;
import com.vedeng.workbench.model.HeaddataSummaryDto;
import com.vedeng.workbench.model.dto.WorkbenchDataQueryDto;
import com.vedeng.workbench.service.WorkbenchBussinessChanceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.vedeng.order.model.dto.SaleorderUserInfoDto.STAFF_LEVEL;

/**
 * 查询销售工作台商机模块的信息
 *
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2021/11/29 14:27.
 * @author: Randy.Xu.
 */

@Slf4j
@Service
public class BusinessChanceQueryStep implements WorkbenchQueryStep {

    @Autowired
    WorkbenchBussinessChanceService workbenchBussinessChanceService;

    @Autowired
    KpiDailyCountService kpiDailyCountService;

    @Override
    public BaseDataInfoDto doQuery(SaleorderUserInfoDto userInfoDto) {
        log.info("查询销售对应的商机，销售信息：{}", JSON.toJSONString(userInfoDto));
        BusinessChanceDateInfo businessChanceDateInfo = new BusinessChanceDateInfo();
        WorkbenchDataQueryDto workbenchDataQueryDto = new WorkbenchDataQueryDto();
        workbenchDataQueryDto.setUserId(userInfoDto.getUserId());
        workbenchDataQueryDto.setPositionName(userInfoDto.getPositionName());
        HeaddataSummaryDto headdataSummaryDto = workbenchBussinessChanceService.getHeaddataSummaryDto(workbenchDataQueryDto);
        businessChanceDateInfo.setTotalBussinessAmount(headdataSummaryDto.getTotalBussinessAmount());
        businessChanceDateInfo.setTotalBussinessNum(headdataSummaryDto.getTotalBussinessNum());
        businessChanceDateInfo.setYesterdayTotalBussinessAmount(headdataSummaryDto.getYesterdayTotalBussinessAmount());
        businessChanceDateInfo.setYesterdayTotalBussinessNum(headdataSummaryDto.getYesterdayTotalBussinessNum());
        businessChanceDateInfo.setThisMouthTotalAmount(headdataSummaryDto.getThisMouthTotalAmount());
        businessChanceDateInfo.setThisMouthBussinessNum(headdataSummaryDto.getThisMouthBussinessNum());
        if(STAFF_LEVEL.equals(userInfoDto.getLevelType())){
            //设置当月时间
            Date thisMonthStartDay = DateUtil.getBeginDayOfMonth();
            KpiUserInfoDto kpiUserInfoDto = new KpiUserInfoDto();
            kpiUserInfoDto.setUserId(userInfoDto.getUserId());
            if(ErpConst.ONE.equals(userInfoDto.getIsB2B())){
                KpiDailyCountExtDto kpiCountVoByUser = kpiDailyCountService.getKpiCountVoByUser(kpiUserInfoDto, thisMonthStartDay, new Date(), KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_YJ);
                Integer sort = kpiCountVoByUser.getSort();
                businessChanceDateInfo.setSort(sort);
            }
        }
        log.info("查询销售对应的商机，返回结果：{}", JSON.toJSONString(businessChanceDateInfo));
        return businessChanceDateInfo;
    }
}
