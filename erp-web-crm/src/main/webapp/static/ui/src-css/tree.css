* {
  box-sizing: border-box;
}
.vd-ui-tree .vd-ui-tree-wrap .vd-ui-tree-select {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
}
.vd-ui-tree .vd-ui-tree-wrap .vd-ui-tree-select .vd-ui-select-item {
  height: 33px;
  display: flex;
  align-items: center;
  padding-left: 5px;
  border-radius: 3px;
  background: #F5F7FA;
  margin-right: 5px;
  margin-bottom: 5px;
}
.vd-ui-tree .vd-ui-tree-wrap .vd-ui-tree-select .vd-ui-select-item .vd-ui-select-avatar {
  width: 20px;
  height: 20px;
  object-fit: cover;
  flex-shrink: 0;
  border-radius: 2px;
  overflow: hidden;
}
.vd-ui-tree .vd-ui-tree-wrap .vd-ui-tree-select .vd-ui-select-item .vd-ui-select-username {
  padding: 5px 0;
  margin-left: 5px;
}
.vd-ui-tree .vd-ui-tree-wrap .vd-ui-tree-select .vd-ui-select-item .vd-ui-select-del {
  width: 30px;
  height: 33px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.vd-ui-tree .vd-ui-tree-wrap .vd-ui-tree-select .vd-ui-select-item .vd-ui-select-del > i {
  font-size: 16px;
  color: #999;
}
.vd-ui-tree .vd-ui-tree-wrap .vd-ui-tree-select .vd-ui-select-item .vd-ui-select-del:hover i {
  color: #F60;
}
.vd-ui-tree .vd-ui-tree-wrap .vd-ui-add-wrap {
  display: flex;
  align-items: center;
  padding-top: 3px;
}
.vd-ui-tree .vd-ui-tree-wrap .vd-ui-add-wrap .vd-ui-add {
  display: inline-flex;
  color: #09F;
  align-items: center;
  cursor: pointer;
}
.vd-ui-tree .vd-ui-tree-wrap .vd-ui-add-wrap .vd-ui-add .icon-add {
  font-size: 16px;
}
.vd-ui-tree .vd-ui-tree-wrap .vd-ui-add-wrap .vd-ui-add:hover {
  color: #f60;
}
.vd-ui-tree .vd-ui-tree-wrap .vd-ui-add-wrap .vd-ui-select-num {
  color: #999;
  margin-left: 5px;
}
.vd-ui-tree .vd-ui-tree-modal {
  width: 100%;
  height: 100%;
  position: fixed;
  left: 0;
  top: 0;
}
.vd-ui-tree.vd-ui-tree-vertical .vd-ui-tree-wrap .vd-ui-tree-select {
  display: block;
  margin-top: 5px;
}
.vd-ui-tree.vd-ui-tree-vertical .vd-ui-tree-wrap .vd-ui-tree-select .vd-ui-select-item {
  background: #fff;
  border-bottom: solid 1px #F5F7FA;
  margin-right: 0;
  margin-bottom: 0;
  display: flex;
  padding-right: 10px;
  padding-left: 10px;
}
.vd-ui-tree.vd-ui-tree-vertical .vd-ui-tree-wrap .vd-ui-tree-select .vd-ui-select-item .vd-ui-select-username {
  flex: 1;
  margin-left: 0;
}
.vd-ui-tree.vd-ui-tree-vertical .vd-ui-tree-wrap .vd-ui-tree-select .vd-ui-select-item .vd-ui-select-del {
  display: none;
  width: 36px;
}
.vd-ui-tree.vd-ui-tree-vertical .vd-ui-tree-wrap .vd-ui-tree-select .vd-ui-select-item:hover {
  background: #F5F7FA;
}
.vd-ui-tree.vd-ui-tree-vertical .vd-ui-tree-wrap .vd-ui-tree-select .vd-ui-select-item:hover .vd-ui-select-del {
  display: flex;
}
.vd-ui-tree-model-content {
  overflow: hidden;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap {
  height: 606px;
  flex-shrink: 0;
  background: #fff;
  overflow: hidden;
  box-shadow: 0 3px 14px rgba(0, 0, 0, 0.1);
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-modal-title {
  position: relative;
  height: 44px;
  font-size: 16px;
  color: #333333;
  line-height: 44px;
  text-align: left;
  padding: 0 20px;
  background: #F5F7FA;
  border-radius: 5px 5px 0px 0px;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-modal-title .vd-ui-modal-close {
  width: 44px;
  height: 44px;
  position: absolute;
  right: 0;
  top: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #ccc;
  cursor: pointer;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-modal-title .vd-ui-modal-close > i {
  font-size: 24px;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-modal-title .vd-ui-modal-close:hover {
  color: #666;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-nav {
  display: flex;
  border-bottom: solid 1px #E1E5E8;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-nav .vd-ui-tree-search {
  flex: 1;
  min-width: 0;
  height: 54px;
  padding: 10px 20px;
  border-right: solid 1px #E1E5E8;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-nav .vd-ui-tree-search .input-wrap {
  height: 33px;
  padding-left: 10px;
  display: flex;
  align-items: center;
  border-radius: 3px;
  border: solid 1px #BABFC2;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-nav .vd-ui-tree-search .input-wrap:hover {
  border-color: #09f;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-nav .vd-ui-tree-search .input-wrap > input {
  flex: 1;
  min-width: 0;
  height: 31px;
  border: none;
  background: none;
  outline: none;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-nav .vd-ui-tree-search .input-wrap > i {
  font-size: 16px;
  color: #666;
  width: 30px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-nav .vd-ui-tree-search .input-wrap > i.icon-error2 {
  width: 36px;
  cursor: pointer;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-nav .vd-ui-tree-search .input-wrap > i.icon-error2:hover {
  color: #f60;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-nav .vd-ui-tree-search .input-wrap > i.icon-search {
  width: 36px;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-nav .vd-ui-tree-select-num {
  flex: 1;
  min-width: 0;
  line-height: 54px;
  padding: 0 20px;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-panel {
  display: flex;
  height: 551px;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-panel .vd-ui-tree-panel-left {
  flex: 1;
  min-width: 0;
  border-right: solid 1px #E1E5E8;
  padding-top: 5px;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-panel .vd-ui-tree-panel-left .vd-ui-tree-select-list {
  margin: 0;
  list-style: none;
  padding: 0 2px;
  height: 546px;
  overflow-y: auto;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-panel .vd-ui-tree-panel-left .vd-ui-tree-select-list::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-panel .vd-ui-tree-panel-left .vd-ui-tree-select-list::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-panel .vd-ui-tree-panel-left .vd-ui-tree-select-list::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-panel .vd-ui-tree-panel-left .vd-ui-tree-select-list::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-panel .vd-ui-tree-panel-left .vd-ui-tree-select-list::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-panel .vd-ui-tree-panel-right {
  flex: 1;
  min-width: 0;
  padding-top: 5px;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-panel .vd-ui-tree-panel-right .vd-ui-tree-choose-wrap {
  padding: 0 10px;
  height: 490px;
  overflow-y: auto;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-panel .vd-ui-tree-panel-right .vd-ui-tree-choose-wrap::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-panel .vd-ui-tree-panel-right .vd-ui-tree-choose-wrap::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-panel .vd-ui-tree-panel-right .vd-ui-tree-choose-wrap::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-panel .vd-ui-tree-panel-right .vd-ui-tree-choose-wrap::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-panel .vd-ui-tree-panel-right .vd-ui-tree-choose-wrap::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-panel .vd-ui-tree-panel-right .vd-ui-tree-choose-wrap .ui-tree-choose-item {
  height: 33px;
  padding: 0 10px;
  display: flex;
  align-items: center;
  position: relative;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-panel .vd-ui-tree-panel-right .vd-ui-tree-choose-wrap .ui-tree-choose-item .ui-tree-choose-avatar {
  width: 20px;
  height: 20px;
  object-fit: cover;
  border-radius: 2px;
  overflow: hidden;
  margin-right: 10px;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-panel .vd-ui-tree-panel-right .vd-ui-tree-choose-wrap .ui-tree-choose-item .icon-delete {
  width: 36px;
  height: 33px;
  display: none;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  color: #999;
  cursor: pointer;
  position: absolute;
  right: 0;
  top: 0;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-panel .vd-ui-tree-panel-right .vd-ui-tree-choose-wrap .ui-tree-choose-item .icon-delete:hover {
  color: #f60;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-panel .vd-ui-tree-panel-right .vd-ui-tree-choose-wrap .ui-tree-choose-item:hover {
  border-radius: 3px;
  background: #efefef;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-panel .vd-ui-tree-panel-right .vd-ui-tree-choose-wrap .ui-tree-choose-item:hover .icon-delete {
  display: flex;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-panel .vd-ui-tree-panel-right .vd-ui-tree-choose-btns {
  height: 55px;
  padding: 10px;
  background: #fff;
  text-align: right;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-panel .vd-ui-tree-panel-right .vd-ui-tree-choose-btns > button {
  width: 58px;
  height: 35px;
  border-radius: 3px;
  font-size: 14px;
  margin-right: 10px;
  cursor: pointer;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-panel .vd-ui-tree-panel-right .vd-ui-tree-choose-btns > button.confirm {
  color: #FFF;
  background-color: #09F;
  border: solid 1px #09F;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-panel .vd-ui-tree-panel-right .vd-ui-tree-choose-btns > button.confirm:hover {
  border-color: #0087e0;
  background-color: #0087e0;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-panel .vd-ui-tree-panel-right .vd-ui-tree-choose-btns > button.cancel {
  color: #333;
  background-color: #F5F7FA;
  border: solid 1px #BABFC2;
}
.vd-ui-tree-model-content .vd-ui-modal-wrap .vd-ui-tree-panel .vd-ui-tree-panel-right .vd-ui-tree-choose-btns > button.cancel:hover {
  background: #EBEFF2;
}
.vd-ui-tree-node {
  position: relative;
  padding-left: 8px;
}
.vd-ui-tree-node .ui-tree-node-department {
  height: 30px;
  padding-left: 10px;
  border-radius: 3px;
  display: flex;
  align-items: center;
  margin-bottom: 3px;
  cursor: pointer;
}
.vd-ui-tree-node .ui-tree-node-department:hover {
  background: #efefef;
}
.vd-ui-tree-node .ui-tree-node-department .ui-tree-node-dept-checkbox {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  border: solid 1px #ddd;
  border-radius: 3px;
  margin-right: 10px;
}
.vd-ui-tree-node .ui-tree-node-department .ui-tree-node-dept-checkbox.active {
  border: solid 1px #09f;
  background: #09f;
  display: flex;
  justify-content: center;
  align-items: center;
}
.vd-ui-tree-node .ui-tree-node-department .ui-tree-node-dept-checkbox.active .icon-selected2 {
  font-size: 12px;
  color: #fff;
}
.vd-ui-tree-node .ui-tree-node-department .ui-tree-node-dept-checkbox.active .icon-deduct {
  font-size: 12px;
  color: #fff;
}
.vd-ui-tree-node .ui-tree-node-department .icon-arrow {
  font-size: 16px;
  color: #666;
}
.vd-ui-tree-node .ui-tree-node-department .icon-file {
  width: 20px;
  height: 20px;
  margin-left: 2px;
}
.vd-ui-tree-node .ui-tree-node-department .department-name {
  font-size: 14px;
  margin-left: 5px;
}
.vd-ui-tree-node .ui-tree-node-user {
  padding-left: 8px;
}
.vd-ui-tree-node .ui-tree-node-user .ul-tree-node-user-list {
  padding: 0;
  margin: 0;
  list-style: none;
}
.vd-ui-tree-node .ui-tree-node-user .ul-tree-node-user-list .ul-tree-node-user-item {
  height: 33px;
  border-radius: 3px;
  display: flex;
  align-items: center;
  padding-left: 10px;
  cursor: pointer;
}
.vd-ui-tree-node .ui-tree-node-user .ul-tree-node-user-list .ul-tree-node-user-item:hover {
  background: #efefef;
}
.vd-ui-tree-node .ui-tree-node-user .ul-tree-node-user-list .ul-tree-node-user-item .ui-tree-node-user-checkbox {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  border: solid 1px #ddd;
  border-radius: 3px;
  margin-right: 10px;
}
.vd-ui-tree-node .ui-tree-node-user .ul-tree-node-user-list .ul-tree-node-user-item .ui-tree-node-user-checkbox .icon-selected2 {
  font-size: 12px;
  color: #fff;
  display: none;
}
.vd-ui-tree-node .ui-tree-node-user .ul-tree-node-user-list .ul-tree-node-user-item .ui-tree-node-user-checkbox.active {
  border: solid 1px #09f;
  background: #09f;
  display: flex;
  justify-content: center;
  align-items: center;
}
.vd-ui-tree-node .ui-tree-node-user .ul-tree-node-user-list .ul-tree-node-user-item .ui-tree-node-user-checkbox.active .icon-selected2 {
  display: block;
}
.vd-ui-tree-node .ui-tree-node-user .ul-tree-node-user-list .ul-tree-node-user-item .ui-tree-node-user-avatar {
  width: 20px;
  height: 20px;
  border-radius: 2px;
  overflow: hidden;
  margin-right: 5px;
}
.vd-ui-tree-node .ui-tree-node-user .ul-tree-node-user-list .ul-tree-node-user-item .ui-tree-node-user-name {
  font-size: 14px;
  color: #333;
}
.vd-ui-tree-node .ui-tree-node-child {
  padding-left: 18px;
}
.vd-ui-filter-wrap {
  height: 100%;
  padding: 0 8px;
}
.vd-ui-filter-wrap .ui-filter-loading {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.vd-ui-filter-wrap .ui-filter-loading .icon-loading {
  display: inline-block;
  font-size: 32px;
  color: #09f;
  animation: loading 1.8s linear infinite;
}
.vd-ui-filter-wrap .ui-filter-loading .icon-info1 {
  font-size: 32px;
  color: #09f;
}
.vd-ui-filter-wrap .ui-filter-loading > span {
  color: #999;
  margin-top: 9px;
}
@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.vd-ui-filter-wrap .ui-filter-list .ui-filter-item {
  height: 33px;
  padding: 0 10px;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.vd-ui-filter-wrap .ui-filter-list .ui-filter-item:hover {
  border-radius: 3px;
  background: #efefef;
}
.vd-ui-filter-wrap .ui-filter-list .ui-filter-item .ui-filter-item-checkbox {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  border: solid 1px #ddd;
  border-radius: 3px;
  margin-right: 12px;
}
.vd-ui-filter-wrap .ui-filter-list .ui-filter-item .ui-filter-item-checkbox .icon-selected2 {
  display: none;
  font-size: 12px;
  color: #fff;
}
.vd-ui-filter-wrap .ui-filter-list .ui-filter-item .ui-filter-item-checkbox.active {
  border: solid 1px #09f;
  background: #09f;
  display: flex;
  justify-content: center;
  align-items: center;
}
.vd-ui-filter-wrap .ui-filter-list .ui-filter-item .ui-filter-item-checkbox.active .icon-selected2 {
  display: block;
}
.vd-ui-filter-wrap .ui-filter-list .ui-filter-item .ui-filter-user {
  display: flex;
  align-items: center;
}
.vd-ui-filter-wrap .ui-filter-list .ui-filter-item .ui-filter-user .ui-filter-avatar {
  width: 20px;
  height: 20px;
  border-radius: 2px;
  overflow: hidden;
  margin-right: 10px;
}
.vd-ui-filter-wrap .ui-filter-list .ui-filter-item .ui-filter-user .ui-filter-name {
  font-size: 14px;
  color: #333;
}
