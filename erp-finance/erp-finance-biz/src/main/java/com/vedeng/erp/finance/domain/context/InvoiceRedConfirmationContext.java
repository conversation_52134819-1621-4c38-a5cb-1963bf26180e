package com.vedeng.erp.finance.domain.context;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.finance.domain.dto.InvoiceRedConfirmationDto;
import com.vedeng.erp.finance.domain.dto.InvoiceRedConfirmationItemDto;
import com.vedeng.erp.finance.domain.dto.SaleInvoiceRedConfirmDetailResponseDto;
import com.vedeng.erp.finance.enums.InvoiceRedConfirmationEvent;
import com.vedeng.erp.finance.enums.InvoiceRedConfirmationStateEnum;
import com.vedeng.erp.finance.enums.YNEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

import static com.vedeng.erp.finance.enums.InvoiceRedConfirmationTaxesStateEnum.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 红字确认单上下文类
 * @date 2023/10/17 13:36
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class InvoiceRedConfirmationContext {

    /**
     * 红字确认单主键
     */
    private Integer id;

    /**
     * 红字确认单数据
     */
    private InvoiceRedConfirmationDto invoiceRedConfirmationDto;

    /**
     * 原始红字确认单状态
     */
    private InvoiceRedConfirmationStateEnum sourceInvoiceRedConfirmationStateEnum;

    /**
     * 转换红字后确认单状态
     */
    private InvoiceRedConfirmationStateEnum invoiceRedConfirmationStateEnum;

    /**
     * 执行事件
     */
    private InvoiceRedConfirmationEvent invoiceRedConfirmationEvent;

    /**
     * 已申请：02、03
     */
    private static final List<String> STATUS_APPLY = Arrays.asList(WAIT_BUYER_CONFIRM.getCode(), WAIT_SELLER_CONFIRM.getCode());

    /**
     * 已确认：01、04
     */
    private static final List<String> STATUS_CONFIRM = Arrays.asList(NO_NEED_CONFIRM.getCode(), HAVE_BEEN_CONFIRM.getCode());

    /**
     * 已作废：05、06、07、08、09
     */
    private static final List<String> STATUS_INVALID = Arrays.asList(
            BUYER_CONFIRM.getCode(),
            SELLER_CONFIRM.getCode(),
            NO_CONFIRM.getCode(),
            PROMOTER_REVOKE.getCode(),
            AFTER_CONFIRM_REVOKE.getCode());

    /**
     * 销售折让
     */
    private static final String SALES_ALLOWANCE = "04";


    public InvoiceRedConfirmationContext(InvoiceRedConfirmationDto invoiceRedConfirmationDto,
                                         InvoiceRedConfirmationEvent invoiceRedConfirmationEvent) {
        this.id = invoiceRedConfirmationDto.getInvoiceRedConfirmationId();
        this.invoiceRedConfirmationDto = invoiceRedConfirmationDto;
        Integer orgStatus = invoiceRedConfirmationDto.getRedConfirmationStatus();
        if (orgStatus == null) {
            log.error("确认单状态不能为空,事件{},确认单信息{}", invoiceRedConfirmationEvent.getName(), JSON.toJSON(invoiceRedConfirmationDto));
            throw new ServiceException("确认单状态不能为空");
        }
        this.sourceInvoiceRedConfirmationStateEnum = InvoiceRedConfirmationStateEnum.getInstanceByCode(orgStatus);
        this.invoiceRedConfirmationEvent = invoiceRedConfirmationEvent;
    }

    /**
     * 通过详情构建状态机上下文
     */
    public InvoiceRedConfirmationContext buildInvoiceRedConfirmationContext(SaleInvoiceRedConfirmDetailResponseDto detailResponseDto) {
        if (this.invoiceRedConfirmationEvent.equals(InvoiceRedConfirmationEvent.USER_INIT)) {
            this.invoiceRedConfirmationStateEnum = InvoiceRedConfirmationStateEnum.INIT;
        } else {
            if (!detailResponseDto.getIsSuccess()) {
                log.info("查询税金详情返回结果:{}", JSON.toJSONString(detailResponseDto));
                throw new ServiceException(detailResponseDto.getReturnMessage());
            }
            this.invoiceRedConfirmationStateEnum = getNewStatus(detailResponseDto.getHzqrxxztDm(), detailResponseDto.getYkjhzfpbz());
            this.assembleRedConfirmationDto(invoiceRedConfirmationDto, detailResponseDto);
        }
        return this;
    }

    /**
     * http://wiki.ivedeng.com/pages/viewpage.action?pageId=214925654
     *
     * @param hzqrxxztDm
     * @param ykjhzfpbz
     * @return
     */
    private InvoiceRedConfirmationStateEnum getNewStatus(String hzqrxxztDm, String ykjhzfpbz) {
        log.info("红字确认信息状态：{},已开具红字发票标志：{}", hzqrxxztDm, ykjhzfpbz);
        if (STATUS_APPLY.contains(hzqrxxztDm) && YNEnum.N.name().equals(ykjhzfpbz)) {
            return InvoiceRedConfirmationStateEnum.HAVE_BEEN_APPLY;
        }
        if (STATUS_CONFIRM.contains(hzqrxxztDm) && YNEnum.N.name().equals(ykjhzfpbz)) {
            return InvoiceRedConfirmationStateEnum.HAVE_BEEN_CONFIRM;
        }
        if (YNEnum.Y.name().equals(ykjhzfpbz)) {
            return InvoiceRedConfirmationStateEnum.HAVE_BEEN_INVOICE;
        }
        if (STATUS_INVALID.contains(hzqrxxztDm) && YNEnum.N.name().equals(ykjhzfpbz)) {
            return InvoiceRedConfirmationStateEnum.HAVE_BEEN_CANCEL;
        }
        return InvoiceRedConfirmationStateEnum.INIT;
    }

    /**
     * 更新值转换
     *
     * @param dto
     * @param detailResponseDto
     */
    private void assembleRedConfirmationDto(InvoiceRedConfirmationDto dto, SaleInvoiceRedConfirmDetailResponseDto detailResponseDto) {
        log.info("本地确认单:{},税金确认单详情:{}", JSON.toJSONString(dto), JSON.toJSONString(detailResponseDto));
        dto.setRedConfirmationStatus(this.invoiceRedConfirmationStateEnum.getCode());
        dto.setApplyReason(detailResponseDto.getChyyDm());
        dto.setBlueInvoiceNo(detailResponseDto.getLzfphm());
        dto.setSeller(detailResponseDto.getXsfmc());
        dto.setSellerTaxNumber(detailResponseDto.getXsfnsrsbh());
        dto.setBuyer(detailResponseDto.getGmfmc());
        dto.setBuyerTaxNumber(detailResponseDto.getGmfnsrsbh());
        dto.setApplyTime(DateUtil.parse(detailResponseDto.getLrrq()));
        dto.setApplyAmount(detailResponseDto.getHzcxje());
        dto.setApplyTaxAmount(detailResponseDto.getHzcxse());
        dto.setApplyPricePlusTaxes(dto.getApplyAmount().add(dto.getApplyTaxAmount()));
        dto.setInputPersonType(Integer.valueOf(detailResponseDto.getLrfsf()));
        dto.setSellerBuyerType(0);
        dto.setUuid(detailResponseDto.getUuid());
        dto.setInvoiceRedConfirmationNo(detailResponseDto.getHzfpxxqrdbh());
        dto.setTaxesRedConfirmationStatus(detailResponseDto.getHzqrxxztDm());
        dto.setConfirmTime(DateUtil.parse(detailResponseDto.getQrrq()));
        dto.setConfirmPersonType(1 == Integer.parseInt(detailResponseDto.getLrfsf()) ? 0 : 1);
        dto.setValueAddedTaxStatus(detailResponseDto.getZzsytDm());
        dto.setConsumptionTaxStatus(detailResponseDto.getXfsytDm());
        dto.setEnterAccountStatus(detailResponseDto.getFprzztDm());
        dto.setAlreadyRedInvoiceFlag(detailResponseDto.getYkjhzfpbz());
        dto.setRedInvoiceNo(detailResponseDto.getHzfphm());
        dto.setOpenRedInvoiceTime(DateUtil.parse(detailResponseDto.getHzkprq()));
        dto.setApiDataUpdateTime(new Date());

        List<InvoiceRedConfirmationItemDto> itemDtoList = new ArrayList<>();
        List<InvoiceRedConfirmationItemDto> invoiceRedConfirmationItemDtoList = dto.getInvoiceRedConfirmationItemDtoList();

        Map<Integer, List<InvoiceRedConfirmationItemDto>> listMap = null;
        if (CollUtil.isNotEmpty(invoiceRedConfirmationItemDtoList)) {
            listMap = invoiceRedConfirmationItemDtoList.stream().collect(Collectors.groupingBy(InvoiceRedConfirmationItemDto::getSerialNumber));
        }
        Map<Integer, List<InvoiceRedConfirmationItemDto>> finalListMap = listMap;

        detailResponseDto.getHzqrxxmxList().forEach(d -> {
            InvoiceRedConfirmationItemDto itemDto = new InvoiceRedConfirmationItemDto();
            if (CollUtil.isNotEmpty(invoiceRedConfirmationItemDtoList)) {
                // 非申请事件下存明细数据
                itemDto = invoiceRedConfirmationItemDtoList.stream().filter(i ->
                                i.getInvoiceRedConfirmationItemId() != null
                                        && StrUtil.isNotEmpty(i.getUuid())
                                        && i.getUuid().equals(d.getUuid()))
                        .findFirst()
                        .orElse(new InvoiceRedConfirmationItemDto());
            }
            // 根据序号获取对应明细行
            if (CollUtil.isNotEmpty(finalListMap)) {
                log.info("根据序号获取对应明细行map:{}", JSON.toJSONString(finalListMap));
                List<InvoiceRedConfirmationItemDto> oldItem = finalListMap.get(d.getLzmxxh());
                BeanUtil.copyProperties(CollUtil.getFirst(oldItem), itemDto);
            }

            itemDto.setUuid(d.getUuid());
            itemDto.setInvoiceRedConfirmationNo(detailResponseDto.getHzfpxxqrdbh());
            itemDto.setSerialNumber(d.getLzmxxh());
            itemDto.setProjectName(d.getXmmc());
            itemDto.setProjectSimpleName(d.getSpfwjc());
            itemDto.setProjectFullName(d.getHwhyslwfwmc());
            itemDto.setTaxCategoryNo(d.getSphfwssflhbbm());
            itemDto.setSpecifications(d.getGgxh());
            itemDto.setUnit(d.getDw());
            itemDto.setTaxRate(new BigDecimal(d.getSl1()));
            itemDto.setAmount(new BigDecimal(d.getJe()));
            if (SALES_ALLOWANCE.equals(invoiceRedConfirmationDto.getApplyReason())) {
                itemDto.setUnitPrice(BigDecimal.ZERO);
                itemDto.setQuantity(BigDecimal.ZERO);
            } else {
                itemDto.setUnitPrice(new BigDecimal(d.getFpspdj()));
                itemDto.setQuantity(new BigDecimal(d.getFpspsl()));
            }
            itemDto.setUaxAmount(new BigDecimal(d.getSe()));
            itemDto.setPricePlusTaxes(itemDto.getAmount().add(itemDto.getUaxAmount()));
            itemDtoList.add(itemDto);
        });
        dto.setInvoiceRedConfirmationItemDtoList(itemDtoList);
        log.info("组装后的本地确认单:{}", JSON.toJSONString(dto));
    }
}
