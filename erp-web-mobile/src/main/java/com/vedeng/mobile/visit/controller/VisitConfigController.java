package com.vedeng.mobile.visit.controller;

import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.core.base.R;
import com.vedeng.mobile.system.dto.VisitConfigDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2023/12/5
 */
@Slf4j
@RestController
@RequestMapping("/mobile/visit/config")
@RequiredArgsConstructor
public class VisitConfigController {


    @Value("${mobile.visitConfigList:[]}")
    private String visitConfigList;

    /**
     * 返回拜访配置列表
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/queryVisitConfigList")
    public R<List<VisitConfigDto>> queryVisitConfigList() {
//        List<VisitConfigDto> visitConfigDtoList = JSONObject.parseArray(visitConfigList, VisitConfigDto.class);
//        return R.success(visitConfigDtoList);

        return R.error("请使用【企微-灵犀CRM】操作拜访") ;
    }


}
