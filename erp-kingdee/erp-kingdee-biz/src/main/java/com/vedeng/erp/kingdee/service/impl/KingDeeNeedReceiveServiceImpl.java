package com.vedeng.erp.kingdee.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.erp.kingdee.dto.KingDeeNeedReceiveDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeNeedReceiveCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeNeedReceiveConvertor;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeNeedReceiveAdjustMapper;
import com.vedeng.erp.kingdee.service.KingDeeNeedReceiveService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.RepoStatus;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

@Service
public class KingDeeNeedReceiveServiceImpl implements KingDeeNeedReceiveService {
    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;
    @Autowired
    private KingDeeNeedReceiveCommandConvertor needReceiveCommandConvertor;
    @Autowired
    private KingDeeNeedReceiveAdjustMapper kingDeeNeedReceiveMapper;
    @Autowired
    private KingDeeNeedReceiveConvertor kingDeeNeedReceiveConvertor;

    @Override
    public void save(KingDeeNeedReceiveDto item) {
        RepoStatus save = kingDeeBaseApi.save(new SaveExtCommand<>(needReceiveCommandConvertor.toCommand(item), item.getFormId()));
        if (CollUtil.isNotEmpty(save.getSuccessEntitys())) {
            SuccessEntity successEntity = CollUtil.getFirst(save.getSuccessEntitys());
            item.setFid(successEntity.getId());
            kingDeeNeedReceiveMapper.insertSelective(kingDeeNeedReceiveConvertor.toEntity(item));
        }
    }
}
