package com.vedeng.common.listener.process;

import com.google.common.eventbus.Subscribe;
import com.vedeng.common.core.config.IObserver;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.StringUtil;
import com.vedeng.erp.finance.dto.event.CheckGoodAptitudeEvent;
import com.vedeng.order.service.SaleorderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
@Slf4j
public class CheckGoodAptitudeListener implements IObserver {
    
    @Autowired
    private SaleorderService saleorderService;

    @Subscribe
    @Transactional(rollbackFor = Throwable.class)
    public void checkGoodAptitude(CheckGoodAptitudeEvent checkGoodAptitudeEvent) {
        log.info("开始执行 aptitude checkGoodAptitude");
        List<Integer> traderIds = checkGoodAptitudeEvent.getTraderIds();
        List<String> skuList = checkGoodAptitudeEvent.getSkuNos();
        for (Integer traderId : traderIds) {
            ResultInfo resultInfo = saleorderService.checkGoodAptitude(traderId, skuList);
            if(resultInfo.getCode()==-1){
                String message = resultInfo.getMessage();
                if (StringUtil.isBlank( message)){
                    message = "资质校验失败";
                }
                checkGoodAptitudeEvent.setMessage(message);
                throw new ServiceException(message);
            }
        }
        log.info("结束执行 aptitude checkGoodAptitude");
    }
}
