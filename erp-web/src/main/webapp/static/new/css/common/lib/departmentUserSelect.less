.scrollbar() {
    &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }

    &::-webkit-scrollbar-track {
        background: transparent;
        width: 6px;
        height: 6px;
    }

    &::-webkit-scrollbar-thumb {
        background: #D7DADE;
        width: 6px;
        height: 6px;
        border-radius: 3px;

        &:hover {
            background: #BABFC2;
        }

        &:active {
            background: #969B9E;
        }
    }
}

.dlg-depart-wrap {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, .6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;

    .vd-icon {
        line-height: 1;
    }

    .dlg-depart-container {
        width: 720px;
        position: relative;

        .dlg-depart-title {
            border-radius: 5px 5px 0 0;
            background: #F5F7FA;
            padding: 0 20px;
            line-height: 44px;
            border-bottom: solid 1px #E1E5E8;
            font-size: 16px;
        }

        .dlg-depart-close {
            font-size: 24px;
            color: #ccc;
            cursor: pointer;
            width: 44px;
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            right: 0;
            top: 0;

            &:hover {
                color: #666;
            }
        }

        .dlg-depart-cnt {
            display: flex;
            background: #fff;
            border-radius: 0 0 5px 5px;
            font-size: 14px;

            .dlg-depart-cnt-block {
                flex: 1;
                border-right: solid 1px #E1E5E8;

                &:last-child {
                    border-right: 0;
                }

                .dlg-depart-cnt-top {
                    height: 54px;
                    border-bottom: solid 1px #E1E5E8;

                    &.top-l {
                        padding: 0 20px;
                        display: flex;
                        align-items: center;
                    }

                    &.top-r {
                        padding: 0 20px;
                        line-height: 54px;
                    }
                }

                .dlg-depart-search-wrap {
                    position: relative;
                    width: 100%;
                
                    .dlg-depart-search-input {
                        width: 100%;
                        border: 1px solid #BABFC2;
                        border-radius: 3px;
                        line-height: 33px;
                        height: 33px;
                        padding: 0 36px 0 10px;
                        
                        &:hover {
                            border-color: #969B9E;
                        }

                        &:focus {
                            border-color: #09f;
                        }

                        &::placeholder {
                            color: #999;
                        }
                    }

                    .vd-icon {
                        font-size: 16px;
                        color: #666;
                        width: 36px;
                        height: 33px;
                        position: absolute;
                        top: 0;
                        right: 0;
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        &.icon-error2 {
                            display: none;
                        }
                    }

                    &.can-clear {
                        .icon-search {
                            display: none;
                        }

                        .icon-error2 {
                            display: flex;
                            cursor: pointer;

                            &:hover {
                                color: #f60;
                            }
                        }
                    }
                }

                .dlg-depart-tree {
                    margin: 5px 3px 5px 5px;
                    height: 555px;
                    overflow: auto;
                    .scrollbar();
                    
                    .dlg-tree-node-item {
                        display: flex;
                        align-items: center;
                        line-height: 33px;
                        cursor: pointer;
                        border-radius: 3px;
                        margin-right: 2px;
                        padding: 0 15px;

                        &:hover {
                            background: #efefef;
                        }

                        .loop(@i) when (@i > 1) {
                            &.lv-@{i} {
                                padding-left: (15px + 18 * (@i - 1));
                            }
                    
                            .loop((@i - 1));
                        }
                    
                        .loop(8);

                        .tree-node-checkbox {
                            margin-right: 10px;
                            display: flex;
                            align-items: center;

                            .icon-checkbox1 {
                                font-size: 16px;
                                color: #969B9E;
                            }

                            .icon-checkbox2, .icon-deduct {
                                display: none;
                            }

                            &.on-select {
                                .icon-deduct {
                                    width: 16px;
                                    height: 16px;
                                    background: #09f;
                                    color: #fff;
                                    display: flex;
                                    justify-content: center;
                                    align-items: center;
                                    font-size: 12px;
                                    border-radius: 3px;
                                    padding-left: 1px;
                                }

                                .icon-checkbox1, .icon-checkbox2 {
                                    display: none;
                                }
                            }

                            &.all-select {
                                .icon-checkbox2 {
                                    display: block;
                                    color: #09f;
                                }

                                .icon-checkbox1, .icon-deduct {
                                    display: none;
                                }
                            }
                        }

                        .icon-right {
                            font-size: 16px;
                            margin-right: 2px;
                        }

                        .tree-node-icon-file {
                            width: 20px;
                            height: 20px;
                            background-image: url(../../../img/file.svg);
                            background-size: 100% 100%;
                            margin-right: 2px;
                        }

                        .tree-node-avatar {
                            width: 20px;
                            height: 20px;
                            border-radius: 2px;
                            overflow: hidden;
                            object-fit: cover;
                            margin-right: 10px;
                        }
                    }
                }

                .dlg-depart-search-list {
                    margin: 5px 3px 5px 5px;
                    height: 555px;
                    overflow: auto;
                    .scrollbar();

                    .dlg-depart-search-item {
                        display: flex;
                        align-items: center;
                        border-radius: 3px;
                        height: 33px;
                        margin-right: 2px;
                        padding-left: 15px;
                        cursor: pointer;
                        
                        &:hover {
                            background: #efefef;
                        }

                        .dlg-depart-search-checkbox {
                            font-size: 16px;
                            margin-right: 10px;
                            line-height: 1;

                            .icon-checkbox1 {
                                color: #969B9E;
                            }

                            .icon-checkbox2 {
                                color: #09f;
                                display: none;
                            }
                        }

                        .dlg-depart-search-avatar {
                            width: 20px;
                            height: 20px;
                            border-radius: 2px;
                            overflow: hidden;
                            object-fit: cover;
                            margin-right: 10px;
                        }

                        .dlg-depart-search-name {
                            .strong {
                                color: #f60;
                                font-weight: normal;
                            }
                        }

                        &.checked {
                            .dlg-depart-search-checkbox {
                                .icon-checkbox1 {
                                    display: none;
                                }

                                .icon-checkbox2 {
                                    display: block;
                                }
                            }
                        }
                    }

                    .dlg-depart-search-empty {
                        padding-top: 226px;
                        text-align: center;

                        .icon-info1 {
                            color: #09f;
                            margin-bottom: 10px;
                            font-size: 32px;
                            display: inline-block;
                        }

                        .dlg-depart-search-empty-txt {
                            color: #999;
                        }
                    }
                }

                .dlg-depart-selected-list {
                    margin: 5px 3px 5px 10px;
                    height: 500px;
                    overflow: auto;
                    .scrollbar();

                    .dlg-depart-selected-item {
                        display: flex;
                        align-items: center;
                        height: 33px;
                        padding-left: 10px;
                        border-radius: 3px;
                        margin-right: 7px;

                        &:hover {
                            background: #efefef;

                            .icon-delete {
                                display: flex;
                            }
                        }

                        .dlg-depart-selected-avatar {
                            width: 20px;
                            height: 20px;
                            border-radius: 2px;
                            overflow: hidden;
                            object-fit: cover;
                            margin-right: 10px;
                        }

                        .dlg-depart-selected-name {
                            flex: 1;
                        }

                        .icon-delete {
                            font-size: 16px;
                            width: 33px;
                            height: 33px;
                            display: none;
                            align-items: center;
                            justify-content: center;
                            color: #999;
                            cursor: pointer;

                            &:hover {
                                color: #f60;
                            }
                        }
                    }
                }

                .dlg-depart-cnt-footer {
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                    padding: 10px 20px;

                    .dlg-depart-btn {
                        height: 35px;
                        line-height: 33px;
                        background: #F5F7FA;
                        border: 1px solid #BABFC2;
                        padding: 0 15px;
                        margin-left: 10px;
                        cursor: pointer;
                        border-radius: 3px;

                        &:hover {
                            background: #EBEFF2;
                        }

                        &.btn-primary {
                            background: #09f;
                            color: #fff;
                            border-color: #09f;

                            &:hover {
                                background: #0087e0;
                                border-color: #0087e0;
                            }
                        }
                    }
                }
            }
        }
    }
}

.dus-select-trigger-wrap {
    display: flex;
    align-items: center;
    height: 30px;
    border: 1px solid #BABFC2;
    border-radius: 3px;
    font-size: 12px;
    padding: 0 10px;
    width: 100%;
    text-align: left;
    cursor: pointer;
    max-width: 350px;

    .dus-select-trigger-label {
        flex: 1;
        display: none;
        flex-wrap: wrap;
        align-items: flex-start;
        height: 30px;
        overflow: hidden;
        padding-top: 4px;
        position: relative;
    
        .dus-select-trigger-label-item {
            display: flex;
            align-items: center;
            border: solid 1px #E1E5E8;
            border-radius: 3px;
            background: #f5f7fa;
            height: 22px;
            margin-bottom: 5px;
            margin-right: 5px;
            padding: 0 6px;

            .icon-delete {
                font-size: 14px;
                color: #999;
                margin-left: 5px;
                line-height: 22px;

                &:hover {
                    color: #f60;
                }
            }
        }
    }

    .dus-select-trigger-placeholder {
        flex: 1;
        color: #999;
    }

    .icon-down {
        font-size: 13px;
        margin-left: 5px;
    }
}