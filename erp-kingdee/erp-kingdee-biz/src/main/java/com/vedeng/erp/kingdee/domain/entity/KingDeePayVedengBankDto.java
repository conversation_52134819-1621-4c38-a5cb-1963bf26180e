package com.vedeng.erp.kingdee.domain.entity;

import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2022/8/29 14:05
 **/
@Getter
@Setter
@Table(name = "T_PAY_VEDENG_BANK")
public class KingDeePayVedengBankDto {
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer payVedengBankId;

    /**
    * 付款银行开户行
    */
    private String payBankName;

    /**
    * 付款银行账号
    */
    private String payBankNo;

    /**
     * 金蝶系统银行编码
     */
    private String kingDeeBankCode;

    /**
    * 是否删除0否1是
    */
    private Integer isDelete;
}