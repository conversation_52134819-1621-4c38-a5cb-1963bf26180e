/**
 * @describe 选择添加暂存录票信息
 * <AUTHOR>
 * @date 2020/6/2 10:02:18
 */
function addInvoiceEntryStash() {
    //1.封装选择商品的数据信息；
    var details = [];
    if ($("#buyorderInfo").find("input[id^=check_box]:checked").length > 0) {
        $("#buyorderInfo").find("input[id^=check_box]:checked").each(function (i) {
            //发票详情ID
            var hxInvoiceDetailId = $(this).attr('hxInvoiceDetailIdFlag');
            //商品ID
            var buyorderGoodsId = $(this).attr("idFlag");
            //已录票数量
            var hasEntryCount = $("#invoice_num" + buyorderGoodsId).val();
            //航信发票ID
            var hxInvoiceId = $('#hxInvoiceId').val();
            details.push({
                hxInvoiceDetailId: hxInvoiceDetailId,
                buyorderGoodsId: buyorderGoodsId,
                hasEntryCount: hasEntryCount,
                hxInvoiceId: hxInvoiceId
            });
        })
        //2.请求添加商品到发票暂存
        $.ajax({
            url: '/supplyChain/invoice/saveInvoiceEntryStash.do',
            data: JSON.stringify(details),
            type: 'post',
            dataType: 'json',
            contentType: 'application/json;charset=utf-8',
            success: function (res) {
                if (res.code == 0) {
                    layer.alert('暂存发票信息成功', function () {
                        window.parent && window.parent.location.reload();
                    });
                } else {
                    layer.alert(res.message);
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        })
    }else {
        layer.alert('请选择需要录票的商品！')
    }
}

/**
 * @describe 复选框权限以及验证
 * @param self
 * <AUTHOR>
 * @date 2020/6/2 15:36:28
 */
function selectAll(self, buyorderId) {
    var prefixStr = 'check_box' + buyorderId;
    var buyorderCheckBoxes = $("input[id^= " + prefixStr + "]")
    //var checkBoxes = document.getElementsByName('selectInvoiceName');
    //1.实现该订单列表中的复选框的选中状态
    for (var i = 0; i < buyorderCheckBoxes.length; i++) {
        buyorderCheckBoxes[i].checked = $(self).prop('checked')
    }
    //2.验证选中的信息
    if ($(self).prop('checked')) {
        for (var i = 0; i < buyorderCheckBoxes.length; i++) {
            var buyorderGoodsId = buyorderCheckBoxes[i].getAttribute('idFlag');
            selectBuyOrder(buyorderCheckBoxes[i], $('#max_num' + buyorderGoodsId).val(), buyorderGoodsId, buyorderId);
        }
    } else {
        var choiceAmount = 0.00;
        var canRecordAmount = $('#canRecordAmount').val();
        var checkBoxes = $("input[id^=check_box]");
        for (var i = 0; i < checkBoxes.length; i++) {
            var goodsId = checkBoxes[i].getAttribute('idFlag');
            if (checkBoxes[i].checked) {
                choiceAmount += parseFloat($('#invoice_totle_amount' + goodsId).val());
            }
        }
        //已录票金额
        $('#choiceAmount').html(choiceAmount.toFixed(2));
        //剩余可录票金额
        $('#canChoiceAmmount').html((canRecordAmount - choiceAmount).toFixed(2));
    }

}

/**
 *@describe 选中采购单商品
 * @param obj
 * @param maxNum
 * @param buyorderGoodsId
 * @returns {boolean}
 * <AUTHOR>
 * @date 2020/6/2 18:26:29
 */
function selectBuyOrder(obj, maxNum, buyorderGoodsId, buyorderId) {
    var id = $(obj).attr("id");
    var invoice_num = parseFloat($('#invoice_num' + buyorderGoodsId).val());
    var canRecordAmount = $('#canRecordAmount').val();
    var canChoiceAmmount = parseFloat($('#canChoiceAmmount').html());
    var choiceAmount = 0.00;

    //1.验证本次需录票数量 必须大于 0
    if (Number(maxNum) == 0) {
        layer.alert("已入库数量小于等于已录票数量，请验证！");
        $("#" + id).prop("checked", false);
        return false;
    }
    //2.验证本次录票数量需要大于0并且小于最大录票数量
    if (invoice_num < 0 || invoice_num > maxNum) {
        layer.alert("本次录票数量需要大于0并且小于最大录票数量");
        $("#" + id).prop("checked", false);
        return false;
    }
    //3.验证该条录票价格不能大于剩余可录票金额
    if ($('#invoice_totle_amount' + buyorderGoodsId).val() > canChoiceAmmount) {
        layer.alert("录票总价格不允许超过剩余最大录票金额");
        $("#" + id).prop("checked", false);
        return false;
    }
    //4.展示本次录票金额与剩余可录票金额
    var checkBoxes = $("input[id^=check_box]");
    for (var i = 0; i < checkBoxes.length; i++) {
        var goodsId = checkBoxes[i].getAttribute('idFlag');
        if (checkBoxes[i].checked) {
            choiceAmount += parseFloat($('#invoice_totle_amount' + goodsId).val());
        }
    }
    //5.展示已录票金额
    $('#choiceAmount').html(choiceAmount.toFixed(2));
    //6.剩余可录票金额
    $('#canChoiceAmmount').html((canRecordAmount - choiceAmount).toFixed(2));

    //7.判断复选框是否全部选中
    var prefixStr = 'check_box' + buyorderId;
    var buyorderCheckBoxes = $("input[id^= " + prefixStr + "]");

    var checkFlag = 0;
    for (var i = 0; i < buyorderCheckBoxes.length; i++) {
        if (!(buyorderCheckBoxes[i].checked)) {
            checkFlag = 1;
            break;
        }
    }
    if (checkFlag == 0) {
        $('#allcheck' + buyorderId).prop('checked', true);
    } else {
        $('#allcheck' + buyorderId).prop('checked', false);
    }
}

/**
 * @describe 本次录票数量更改
 * <AUTHOR>
 * @date 2020/6/3 11:43:22
 */
function invoiceNumChange(buyorderGoodsId, price, buyorderId) {
    //1.判断输入是否为合法（整数）
    var boolean = isInteger($('#invoice_num' + buyorderGoodsId).val());
    if (boolean) {
        //2.同步修改该采购商品对应数量的金额
        $('#invoice_totle_amount' + buyorderGoodsId).val((parseFloat($('#invoice_num' + buyorderGoodsId).val()) * parseFloat(price)).toFixed(2));
    } else {
        layer.alert("录票数量为正整数");
        $('#invoice_num' + buyorderGoodsId).val(0);
        $('#invoice_totle_amount' + buyorderGoodsId).val(0);
    }
    //3.监测是否被选中来修改已录商品金额信息
    var choiceAmount = 0.00;
    var canRecordAmount = $('#canRecordAmount').val();
    var checkBoxes = $("input[id^=check_box]");
    for (var i = 0; i < checkBoxes.length; i++) {
        var goodsId = checkBoxes[i].getAttribute('idFlag');
        if (checkBoxes[i].checked) {
            choiceAmount += parseFloat($('#invoice_totle_amount' + goodsId).val());
        }
    }
    //已录票金额
    $('#choiceAmount').html(choiceAmount.toFixed(2));
    //剩余可录票金额
    $('#canChoiceAmmount').html((canRecordAmount - choiceAmount).toFixed(2));
}

/**
 * @describe 判断是否为整数
 * @param obj
 * @returns {boolean}
 * <AUTHOR>
 * @date 2020/6/4 14:17:28
 */
function isInteger(obj) {
    reg = /(^[1-9]\d*$)/;
    if (!reg.test(obj)) {
        return false;
    } else {
        if (obj * 1 > 0)
            return true;
        else
            return false;
    }
}
