@font-face {
  font-family: 'BD';
  src: url('../new/css/common/bd-font/BD.eot?9hlrem');
  src: url('../new/css/common/bd-font/BD.eot?9hlrem#iefix') format('embedded-opentype'), url('../new/css/common/bd-font/BD.ttf?9hlrem') format('truetype'), url('../new/css/common/bd-font/BD.woff?9hlrem') format('woff'), url('../new/css/common/bd-font/BD.svg') format('svg');
  font-weight: normal;
  font-style: normal;
}
.vd-icon {
  font-family: 'BD' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1;
}
.vd-icon.icon-password:before {
  content: "\e92b";
}
.vd-icon.icon-info1:before {
  content: "\e91d";
}
.vd-icon.icon-caution1:before {
  content: "\e91b";
}
.vd-icon.icon-loading:before {
  content: "\e9a4";
}
.vd-icon.icon-error2:before {
  content: "\e918";
}
.goods-info-table {
  font-size: 12px;
  line-height: 1.5;
}
.goods-info-table thead th {
  padding: 8px 10px;
  background: #F5F7FA;
  color: #333;
  text-align: left;
  font-size: 12px;
  border-color: #E1E5E8;
}
.goods-info-table thead th.middle {
  vertical-align: middle;
}
.goods-info-table thead th.center {
  text-align: center;
}
.goods-info-table tbody td {
  padding: 10px;
  background: #fff;
  color: #333;
  text-align: left;
  font-size: 12px;
  vertical-align: top;
  border-color: #E1E5E8;
}
.goods-info-table tbody td.middle {
  vertical-align: middle;
}
.goods-info-table tbody td.center {
  text-align: center;
}
.goods-info-table tbody tr:hover td {
  background: #fffaf2;
}
.goods-info-table tbody tr.total td {
  background: #F5F7FA;
}
.goods-info-table tbody tr.total td:hover {
  background: #F5F7FA;
}
.goods-info-table td input[type="checkbox"],
.goods-info-table th input[type="checkbox"] {
  width: 16px;
  height: 16px;
  margin: 0;
  vertical-align: middle;
  cursor: pointer;
}
.goods-info-table .goods-info-wrap {
  display: flex;
}
.goods-info-table .goods-info-wrap .info-pic {
  margin-right: 10px;
  position: relative;
}
.goods-info-table .goods-info-wrap .info-pic .info-pic-inner {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.goods-info-table .goods-info-wrap .info-pic img {
  max-width: 100%;
  max-height: 100%;
}
.goods-info-table .goods-info-wrap .info-pic .prod-tag-wrap {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
}
.goods-info-table .goods-info-wrap .info-pic .prod-tag-wrap .tag-item-inner {
  position: relative;
}
.goods-info-table .goods-info-wrap .info-pic .prod-tag-wrap .tag-item {
  width: 16px;
  height: 16px;
  margin-right: 2px;
  vertical-align: top;
}
.goods-info-table .goods-info-wrap .info-pic .prod-tag-wrap .tag-item.tag-fk {
  cursor: pointer;
}
.goods-info-table .goods-info-wrap .info-detail {
  flex: 1;
}
.goods-info-table .goods-info-wrap .info-detail .goods-name {
  margin-bottom: 5px;
}
.goods-info-table .goods-info-wrap .info-detail .goods-name a {
  color: #09f;
}
.goods-info-table .goods-info-wrap .info-detail .goods-name a:hover {
  color: #f60;
}
.goods-info-table .goods-info-wrap .info-detail .goods-sku {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}
.goods-info-table .goods-info-wrap .info-detail .goods-sku .sku-txt {
  color: #999;
}
.goods-info-table .goods-info-wrap .info-detail .goods-sku .goods-tip-cnt {
  width: 300px;
}
.goods-info-table .detail-info-field {
  display: flex;
  margin-bottom: 5px;
}
.goods-info-table .detail-info-field:last-child {
  margin-bottom: 0;
}
.goods-info-table .detail-info-field .detail-info-label {
  color: #999;
}
.goods-info-table .detail-info-field .detail-info-txt {
  color: #333;
  flex: 1;
  display: flex;
  align-items: center;
}
.goods-info-table .detail-info-field .red {
  color: #E64545;
}
.goods-info-table .detail-info-field .green {
  color: #11A811;
}
.goods-info-table .detail-info-field.width1 .detail-info-label {
  width: 50px;
  text-align: right;
}
.goods-info-table .detail-info-field.width2 .detail-info-label {
  width: 84px;
  text-align: right;
}
.goods-info-table .detail-info-field.width3 .detail-info-label {
  width: 72px;
  text-align: right;
}
.goods-info-table .detail-info-field.wrap {
  flex-wrap: wrap;
}
.goods-info-table .detail-info-field.wrap .detail-info-txt {
  white-space: nowrap;
}
.goods-info-table .detail-info-field .line1 {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.goods-info-table .detail-info-field .warning {
  padding-top: 0;
}
.goods-info-table .goods-tip-wrap {
  position: relative;
}
.goods-info-table .goods-tip-wrap .vd-icon {
  cursor: pointer;
  margin-left: 5px;
  vertical-align: -2px;
}
.goods-info-table .goods-tip-wrap .vd-icon.icon-info1 {
  color: #09f;
}
.goods-info-table .goods-tip-wrap .vd-icon.icon-caution1 {
  color: #E64545;
}
.goods-info-table .goods-tip-wrap.width10 .goods-tip-cnt {
  width: 300px;
}
.goods-info-table .goods-tip-wrap.width12 .goods-tip-cnt {
  width: 360px;
}
.goods-info-table .goods-tip-wrap .goods-tip-cnt {
  position: absolute;
  background: #fff;
  max-width: 360px;
  border-radius: 3px;
  box-shadow: rgba(0, 0, 0, 0.12) 0px 3px 6px;
  padding: 10px 15px;
  left: 27px;
  top: -10px;
  display: none;
  z-index: 11;
}
.goods-info-table .goods-tip-wrap .goods-tip-cnt.nowrap {
  white-space: nowrap;
}
.goods-info-table .goods-tip-wrap .goods-tip-cnt::before {
  content: "";
  width: 0;
  height: 0;
  border: 5px solid transparent;
  border-right: 5px solid #fff;
  top: 13px;
  left: -10px;
  position: absolute;
}
.goods-info-table .goods-tip-wrap .goods-tip-cnt.right {
  right: 27px;
  left: auto;
}
.goods-info-table .goods-tip-wrap .goods-tip-cnt.right::before {
  border: 5px solid transparent;
  border-left: 5px solid #fff;
  left: auto;
  right: -10px;
}
.goods-info-table .goods-tip-wrap .goods-tip-cnt .tip-cnt-item {
  display: flex;
}
.goods-info-table .goods-tip-wrap .goods-tip-cnt .tip-cnt-item .tip-item-txt {
  flex: 1;
}
.goods-info-table .goods-tip-wrap .goods-tip-cnt .tip-cnt-title {
  font-weight: 700;
  margin-bottom: 5px;
  margin-top: 10px;
}
.goods-info-table .goods-tip-wrap .goods-tip-cnt .tip-cnt-title:first-child {
  margin-top: 0;
}
.goods-info-table .goods-tip-wrap .goods-tip-cnt .tips-cnt-loading {
  display: flex;
  align-items: center;
  width: 130px;
}
@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.goods-info-table .goods-tip-wrap .goods-tip-cnt .tips-cnt-loading .icon-loading {
  color: #09f;
  margin-right: 5px;
  animation: loading 2s linear infinite;
  margin-left: 0;
}
.goods-info-table .goods-tip-wrap:hover .goods-tip-cnt {
  display: block;
}
.goods-info-table .goods-total-wrap {
  display: flex;
  align-items: center;
}
.goods-info-table .goods-total-wrap .total-txt-item {
  display: flex;
  align-items: center;
  margin-right: 21px;
  position: relative;
}
.goods-info-table .goods-total-wrap .total-txt-item::before {
  content: "";
  position: absolute;
  width: 1px;
  height: 12px;
  background: #e1e5e8;
  right: -11px;
  top: 3px;
}
.goods-info-table .goods-total-wrap .total-txt-item:last-child::before {
  display: none;
}
.goods-info-table .goods-total-wrap .total-txt-item .total-item-label {
  color: #999;
}
.goods-info-table .goods-total-wrap .total-txt-item .total-item-label {
  color: #999;
}
.goods-info-table .goods-total-wrap .total-txt-item .total-item-txt {
  display: flex;
  align-items: center;
}
.goods-info-table .goods-total-wrap .total-txt-item .total-item-txt.red {
  color: #E64545;
  font-weight: 700;
}
.goods-info-table .goods-total-wrap .total-txt-item .total-item-txt .red {
  color: #E64545;
}
.goods-info-table .goods-total-wrap .total-txt-item .total-item-txt .green {
  color: #11A811;
}
.goods-info-table .goods-total-wrap .total-txt-item .total-item-tip {
  color: #999;
}
.goods-info-table .goods-list-num-wrap {
  position: relative;
  text-align: center;
}
.goods-info-table .goods-list-num-wrap .goods-tip-wrap {
  position: absolute;
  top: 10px;
  left: 14.5px;
}
.goods-info-table .goods-list-num-wrap .goods-tip-wrap .goods-tip-cnt {
  left: 22px;
}
.goods-info-table .goods-list-num-wrap .icon-password {
  color: #E64545;
  margin-left: 0;
}
.goods-info-table .caozuo {
  display: flex;
  align-items: center;
}
.goods-info-table .caozuo span {
  border: 0;
  padding: 0;
  color: #09f;
  background: transparent;
  line-height: 1.5;
  height: auto;
  margin-left: 21px;
  position: relative;
}
.goods-info-table .caozuo span::before {
  content: "";
  width: 1px;
  height: 12px;
  position: absolute;
  left: -11px;
  top: 3px;
  background: #e1e5e8;
}
.goods-info-table .caozuo span:first-child {
  margin-left: 0;
}
.goods-info-table .caozuo span:first-child::before {
  display: none;
}
.goods-info-table .caozuo span:hover {
  background: transparent;
  color: #f60;
}
.goods-info-table .caozuo span.disabled {
  color: #999;
  cursor: not-allowed;
}
.goods-table-error {
  color: #E64545;
  margin-bottom: 15px;
  margin-top: -9px;
  display: none;
  line-height: 1.5;
}
.goods-table-error .icon-error2 {
  margin-right: 2px;
  vertical-align: -3px;
}
