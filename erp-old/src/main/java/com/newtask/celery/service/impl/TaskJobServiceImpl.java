package com.newtask.celery.service.impl;

import com.newtask.celery.dao.TaskJobMapper;
import com.newtask.celery.model.JobStatusEnum;
import com.newtask.celery.model.TaskJob;
import com.newtask.celery.service.TaskJobService;
import com.newtask.celery.utils.RedisKeyUtils;
import com.vedeng.common.redis.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.newtask.celery.AsyncConstants.MAX_ERROR_LENGTH;

/**
 * @Author: Cherny.chen
 * @Create: 2021/7/12 13:00
 */
@Slf4j
@Service
public class TaskJobServiceImpl implements TaskJobService {
    @Resource
    private TaskJobMapper taskJobMapper;
    @Resource
    private RedisUtils redisUtils;

    private final static String K_ACTIVE_QUEUE_SET = RedisKeyUtils.activeQueueSetKey();

    private long lastSyncActiveQueueTime = 0;

    private final static long SYNC_ACTIVE_QUEUE_TIME = 60 * 60 * 1000;

    @Override
    public void addTaskJob(TaskJob taskJob) {
        log.info("async add taskJob :{} ", taskJob);
        redisUtils.sadd(K_ACTIVE_QUEUE_SET, taskJob.getQueue());
        taskJobMapper.save(taskJob);
    }

    @Override
    public void updateTaskJob(TaskJob taskJob) {
        log.info("async update taskJob :{} ", taskJob);
        if (JobStatusEnum.SUCCESS.getKey().equals(taskJob.getStatus())) {
            taskJob.setErrorMsg(null);
        } else if (StringUtils.isNotEmpty(taskJob.getErrorMsg())) {
            taskJob.setErrorMsg(StringUtils.substring(taskJob.getErrorMsg(), 0, MAX_ERROR_LENGTH));
        }
        taskJobMapper.update(taskJob);
    }

    @Override
    public List<TaskJob> listTaskJobByQueue(Integer queueKey, Long lastId) {
        log.info("async check queue({}), lastId :{} ", queueKey, lastId);
        return taskJobMapper.listTaskByQueue(queueKey, lastId);
    }

    /**
     *  后期可以优化为redis缓存取,时间到1小时进行补刀操作
     * @return
     */
    @Override
    public Set<Integer> getActiveQueueSet() {

        Set<Integer> redisActiveQueueSet = transRedisActiveQueue();

        if ((System.currentTimeMillis() - lastSyncActiveQueueTime) > SYNC_ACTIVE_QUEUE_TIME) {

            List<Integer> dbActiveList = taskJobMapper.listActiveQueue();

            for (Integer key : redisActiveQueueSet) {
                if (!dbActiveList.contains(key)) {
                    log.info("remove queue({}) from redis ...", key);
                    redisUtils.srem(K_ACTIVE_QUEUE_SET, key);
                }
            }

            for (Integer key : dbActiveList) {
                if (!redisActiveQueueSet.contains(key)) {
                    log.info("add active queue({}) to redis ...", key);
                    redisUtils.sadd(K_ACTIVE_QUEUE_SET, key);
                }
            }

            lastSyncActiveQueueTime = System.currentTimeMillis();

            redisActiveQueueSet = new HashSet<>(dbActiveList);
        }

        return redisActiveQueueSet;
    }


    private Set<Integer> transRedisActiveQueue() {
        Set<String> redisActiveQueueSet = redisUtils.smembers(K_ACTIVE_QUEUE_SET);
        Set<Integer> result = new HashSet<>();
        if (CollectionUtils.isNotEmpty(redisActiveQueueSet)) {
            result = redisActiveQueueSet.stream().map(Integer::parseInt).collect(Collectors.toSet());
        }
        return result;
    }

    @Override
    public boolean isRunSuccess(String uuid) {
        return taskJobMapper.countRunSuccess(uuid) > 0;
    }

    @Override
    public void removeFinishQueue(Integer queueKey) {
        log.info("remove finish queue({}) ...", queueKey);
        redisUtils.srem(K_ACTIVE_QUEUE_SET, queueKey);
    }
}
