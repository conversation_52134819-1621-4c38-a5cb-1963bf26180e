package com.vedeng.crm.business.quote.domain.dto;

import com.vedeng.common.core.utils.validator.group.DefaultGroup;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class QuoteSyncRequestDto {

    @NotNull(message = "报价单id不能为空", groups = DefaultGroup.class)
    private Integer quoteorderId;

    @NotNull(message = "时间戳不能为空", groups = DefaultGroup.class)
    private Long timestamp;

    private String local;
}
