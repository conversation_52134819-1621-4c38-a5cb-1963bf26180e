package com.newtask.trader;


import com.google.common.collect.Lists;
import com.report.dao.PublicCustomerCalculateMapper;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.BitsetUtils;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.erp.trader.domain.dto.TraderCustomerDto;
import com.vedeng.erp.trader.mapper.PublicCustomerEarlyWarningRecordMapper;
import com.vedeng.erp.trader.mapper.PublicTraderMapper;
import com.vedeng.erp.trader.service.PublicCustomerCalculateService;
import com.vedeng.trader.dao.TraderCustomerMapper;
import com.vedeng.trader.model.dto.TraderCustomerAssociateInfoDto;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * PublicCustomerEarlyWarningTask
 * 根据公海规则，计算出符合预警的客户，进行公海预警
 * 每天上午9点开始公海预警计算  0 0 9 * * ？
 *
 * <AUTHOR>
 */
@Component
@JobHandler(value = "publicCustomerEarlyWarningTask")
public class PublicCustomerEarlyWarningTask extends AbstractJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(PublicCustomerEarlyWarningTask.class);

    /**
     * 公海预警消息模板ID
     */
    public static final Integer PUBLIC_CUSTOMER_EARLY_WARNING_TEMPLATE_ID = 220;

    /**
     * 一次执行保存或更新条数
     */
    private static final Integer OPERATION_COUNT = 100;

    @Autowired
    private PublicCustomerCalculateService publicCustomerCalculateService;

    @Resource
    private PublicCustomerEarlyWarningRecordMapper publicCustomerEarlyWarningRecordMapper;

    @Resource
    private TraderCustomerMapper traderCustomerMapper;

    @Resource
    private PublicTraderMapper publicTraderMapper;

    @Autowired
    private PublicCustomerCalculateMapper publicCustomerCalculateMapper;


    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("PublicCustomerEarlyWarningTask-JOB, START");

        logger.info("publicCustomerEarlyWarningTask start param:{}", param);

        List<Integer> traderCustomerIdList = new ArrayList<>();

        if (StringUtils.isNotEmpty(param)){
            List<Integer> customerIdList = Arrays.stream(param.split(",")).map(Integer::valueOf).collect(Collectors.toList());
            List<TraderCustomerDto> traderCustomerDtoList = publicTraderMapper.getTraderCustomerByTraderCustomerId(customerIdList);
            List<Long> associateGroupList =
                    traderCustomerDtoList
                            .stream()
                            .map(TraderCustomerDto::getAssociatedCustomerGroup)
                            .filter(item -> item > 0)
                            .distinct()
                            .collect(Collectors.toList());
            if (associateGroupList.size() > 0) {
                traderCustomerIdList.addAll(publicTraderMapper.getTraderCustomerIdListByAssociateGroup(associateGroupList));
            } else {
                traderCustomerIdList.addAll(customerIdList);
            }
        }


        List<TraderCustomerAssociateInfoDto> b2bCustomer = publicCustomerCalculateMapper.getB2bCustomer(traderCustomerIdList);

        List<Integer> b2bCustomerId = b2bCustomer.stream().map(TraderCustomerAssociateInfoDto::getTraderCustomerId).distinct().collect(Collectors.toList());

        BitSet oldTraderCustomerBitSet = publicCustomerCalculateService.calculatePublicCustomer(BitsetUtils.collection2BitSet(b2bCustomerId),1);

        //最终更符合条件的客户ID集合
        List<Integer> customerIdListResult = BitsetUtils.bitSet2List(oldTraderCustomerBitSet);


        if (CollectionUtils.isEmpty(customerIdListResult)) {
            XxlJobLogger.log("剔除处理关联群组不符合预警规则后无需要预警客户。");
            logger.info("剔除处理关联群组不符合预警规则后无需要预警客户");
            return SUCCESS;
        }

        /**
         * 10.保存公海预警记录
         */
        long currentTime = System.currentTimeMillis();
        Lists.partition(customerIdListResult, OPERATION_COUNT).forEach(operationList ->
                publicCustomerEarlyWarningRecordMapper.batchAddRecord(operationList,currentTime));


        /**
         * 11.更新客户预警次数
         */
        Lists.partition(customerIdListResult, OPERATION_COUNT).forEach(operationList ->
                traderCustomerMapper.updatePublicCustomerEarlyWarningCount(operationList));


        /**
         * 12.发送站内信
         */
        List<Integer> userList = b2bCustomer.stream()
                .filter(customer -> customerIdListResult.contains(customer.getTraderCustomerId()))
                .map(TraderCustomerAssociateInfoDto::getUserId)
                .filter(Objects::nonNull)
                .filter(userId -> userId != 0)
                .distinct()
                .collect(Collectors.toList());


        HashMap<String, String> paramsMap = new HashMap<>(1);
        paramsMap.put("param", "earlyWarning");

        userList.forEach(userId -> MessageUtil.sendMessage(PUBLIC_CUSTOMER_EARLY_WARNING_TEMPLATE_ID, Collections.singletonList(userId),paramsMap,
                "./trader/customer/index.do?customerAlert=1&search=1&homePurchasing=" + userId));
        return SUCCESS;
    }
}
