package com.vedeng.flash.dao;

import com.vedeng.flash.dto.PrepareStockCommand;
import com.vedeng.flash.model.RegularOperateLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RegularOperateLogMapper {

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REGULAR_OPERATE_LOG
     *
     * @mbggenerated Tue May 18 19:29:46 CST 2021
     */
    int deleteByPrimaryKey(Integer logId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REGULAR_OPERATE_LOG
     *
     * @mbggenerated Tue May 18 19:29:46 CST 2021
     */
    int insert(RegularOperateLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REGULAR_OPERATE_LOG
     *
     * @mbggenerated Tue May 18 19:29:46 CST 2021
     */
    int insertSelective(RegularOperateLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REGULAR_OPERATE_LOG
     *
     * @mbggenerated Tue May 18 19:29:46 CST 2021
     */
    RegularOperateLog selectByPrimaryKey(Integer logId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REGULAR_OPERATE_LOG
     *
     * @mbggenerated Tue May 18 19:29:46 CST 2021
     */
    int updateByPrimaryKeySelective(RegularOperateLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REGULAR_OPERATE_LOG
     *
     * @mbggenerated Tue May 18 19:29:46 CST 2021
     */
    int updateByPrimaryKey(RegularOperateLog record);

    /**
     * 查询日志
     * @param command
     * @return
     */
    List<RegularOperateLog> getRegularOperateLogList(PrepareStockCommand command);

    /**
     * 获取子日志（替换、转换日志）
     * @param logId
     * @return
     */
    RegularOperateLog getChileRegularSku(Integer logId);

    /**
     * 查询日志
     * @param skuId
     * @return
     */
    List<RegularOperateLog> selectBySkuId(Integer skuId);
}
