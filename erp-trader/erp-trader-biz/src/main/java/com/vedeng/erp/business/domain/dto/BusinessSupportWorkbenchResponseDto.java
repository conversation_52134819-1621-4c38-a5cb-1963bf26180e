package com.vedeng.erp.business.domain.dto;

import lombok.Data;

import java.util.List;

@Data
public class BusinessSupportWorkbenchResponseDto {

    /**
     * 咨询待处理数量
     */
    private String consultationPendingNum;

    /**
     * 今日需跟进数量
     */
    private String followUpTodayNum;

    /**
     * 商机转换率
     */
    private String businessConversionRate;

    /**
     * 统计数据
     */
    private List<StatisticalData> statisticalDataList;


    @Data
    public static class StatisticalData {

        /**
         * 标题
         */
        private String title;

        /**
         * S值
         */
        private String sData;

        /**
         * A值
         */
        private String aData;

        /**
         * B值
         */
        private String bData;

        /**
         * C值
         */
        private String cData;
        
    }


}
