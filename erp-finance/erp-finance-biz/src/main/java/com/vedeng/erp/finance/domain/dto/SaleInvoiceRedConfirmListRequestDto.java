package com.vedeng.erp.finance.domain.dto;

import com.vedeng.infrastructure.taxes.base.ITaxesParam;
import com.vedeng.infrastructure.taxes.utils.TaxesUtil;
import lombok.Data;

/**
 * 红字确认单列表入参
 */
@Data
public class SaleInvoiceRedConfirmListRequestDto implements ITaxesParam {

    /** 纳税人识别号
     *  参：贝登税务信息
     *  长度：15/18/20
     */
    private String nsrsbh;

    /** 购销方性质
     *  固定为 0-销售方
     */
    private int gxfxz;

    /** 对方纳税人名称 */
    private String dfnsrmc;

    /** 开票日期起
     *  格式: YYYY-mm-dd
     */
    private String kprqq;

    /** 开票日期止
     *  格式: YYYY-mm-dd
     */
    private String kprqz;

    /** 开票状态
     *  Y - 已开具
     *  N - 未开具
     */
    private String kpzt;

    /** 录入方身份
     *  1-购买方
     *  0-销售方
     */
    private int lrfsf;

    /** 确认单状态
     *  01 无需确认
     *  02 销方录入待购方确认
     *  03 购方录入待销方确认
     *  04 购销双方已确认
     *  05 作废（销方录入购方否认）
     *  06 作废（购方录入销方否认）
     *  07 作废（超 72 小时未确认）
     *  08 作废（发起方已撤销）
     *  09 作废（确认后撤销）
     */
    private String qrdzt;

    /** 页码
     *  最小为1
     */
    private int pageNumber;

    /** 大小
     *  最大支持500
     */
    private int pageSize;

    public SaleInvoiceRedConfirmListRequestDto() {
        this.nsrsbh = TaxesUtil.taxesConfig.taxNo;
        this.gxfxz = 0;
        this.kprqq = "2020-01-01";
        this.kpzt = "N";
        this.lrfsf = 1;
    }

}
