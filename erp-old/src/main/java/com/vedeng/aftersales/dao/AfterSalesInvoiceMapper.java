package com.vedeng.aftersales.dao;


import com.vedeng.aftersales.model.AfterSalesInvoice;
import com.vedeng.aftersales.model.vo.AfterSalesInvoiceVo;
import com.vedeng.erp.aftersale.dto.ReturnBuyorderInvoiceDto;
import com.vedeng.finance.model.Invoice;
import com.vedeng.finance.model.PayApply;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AfterSalesInvoiceMapper {


	int insertSelective(AfterSalesInvoice record);

    int update(AfterSalesInvoice record);

    /**
     * <b>Description:</b><br> 查询销售售后退货产品关联到的所有发票
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2017年10月18日 下午1:26:03
     */
    List<AfterSalesInvoiceVo> getAfterSalesInvoiceVos(AfterSalesInvoiceVo afterSalesInvoiceVo);

    Integer delAfterSalesInvoiceByAfterSalesId(Integer afterSalesId);

    /**
     * <b>Description:</b><br> 查询销售售后的退票列表信息--售后退货产品关联到的所有发票
     * @param afterSalesInvoiceVo
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2017年10月18日 下午1:26:03
     */
    List<AfterSalesInvoiceVo> getAfterSalesInvoiceVosByParam(AfterSalesInvoiceVo afterSalesInvoiceVo);


    /**
     *  获取无效票列表
     * @param afterSalesId 售后单id
     * @return List<AfterSalesInvoiceVo>
     */
    List<AfterSalesInvoiceVo> getAfterSalesInvoiceForInValid(Integer afterSalesId);

    /**
     * <b>Description:</b><br> 财务-售后-安调-开票信息
     * @param afterSalesId
     * @return
     * @Note
     * <b>Author:</b> duke
     * <br><b>Date:</b> 2017年10月31日 下午3:40:29
     */
    List<AfterSalesInvoiceVo> getAfterInvoiceList(@Param(value="afterSalesId")Integer afterSalesId,
                                                  @Param(value="type")Integer type, @Param(value="tag")Integer tag);

    /**
     * <b>Description:</b><br> //根据售后单号获取付款申请记录
     * @param afterSalesId
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2017年11月23日 上午11:17:40
     */
    List<PayApply> getAfterAtPaymentApply(Integer afterSalesId);

    Integer getIsReturnAfterInvoice(Integer afterSalesId);

    /**
     * 根据afterSalesId查询安调维修录票
     *
     * @param afterSalesId
     * @return
     */
    List<AfterSalesInvoiceVo> getAfterSalesAWInvoiceVoList(Integer afterSalesId);

    /**
     * 根据afterSalesId查询--需要退票记录（已开蓝字作废或红字有效的票）
     *
     * @param afterSalesInvoice
     * @return
     */
    List<AfterSalesInvoiceVo> getAfterSalesInvoiceVoList(AfterSalesInvoice afterSalesInvoice);

    /**
     * 查询采购售后订单的发票信息（扣除蓝字作废和虚拟冲销后的蓝字有效发票）
     * @param afterSalesInvoiceVo 采购售后信息
     * @return List<AfterSalesInvoiceVo>
     */
    List<AfterSalesInvoiceVo> getAfterSalesInvoiceVosByParamNew(AfterSalesInvoiceVo afterSalesInvoiceVo);

    /**
     * 处理售后发票的操作信息
     *
     * @param afterSalesInvoiceId
     * @param handleStatus
     * @param handleComments
     * @return
     */
    int saveInvoiceHandleInfo(@Param("afterSalesInvoiceId") Integer afterSalesInvoiceId,
                              @Param("handleStatus") Integer handleStatus,
                              @Param("handleComments") String handleComments);

    /**
     * 获取售后单退票信息相关
     *
     * @param afterSaleId
     * @return
     */
    List<AfterSalesInvoice> getAfterSalesInvoiceByAfterSaleId(Integer afterSaleId);


    /**
     * 根据售后id和发票id查询退票状态
     */
    AfterSalesInvoice selectOneByRelatedId(@Param("afterSalesId") Integer afterSalesId, @Param("invoiceId") Integer invoiceId);

    /**
     * 查询符合条件的 同一个发票号的集合
     * @param afterSalesInvoiceVo
     * @return
     */
    List<AfterSalesInvoiceVo> getAllInvoiceNoBgZero(AfterSalesInvoiceVo afterSalesInvoiceVo);

    /**
     * 来自航信没有处理的退票信息
     *
     * @param invoiceNo
     * @param invoiceCode
     * @return
     */
    List<AfterSalesInvoice> getUnHandleRecordByInvoiceNoAndCode(@Param("invoiceNo")String invoiceNo, @Param("invoiceCode") String invoiceCode);

    /**
     * 根据售后id查询采购退票售后发票信息
     * <AUTHOR>
     * @param afterSalesId
     * @return
     */
    List<ReturnBuyorderInvoiceDto> queryAfterBuyorderInvoice(@Param("afterSalesId") Integer afterSalesId);

    /**
     * 根据售后id查询售后发票信息
     * @param afterSalesId
     * @return
     */
    List<AfterSalesInvoiceVo> getPushInvoiceByAfterSalesId(@Param("afterSalesId") Integer afterSalesId);
}