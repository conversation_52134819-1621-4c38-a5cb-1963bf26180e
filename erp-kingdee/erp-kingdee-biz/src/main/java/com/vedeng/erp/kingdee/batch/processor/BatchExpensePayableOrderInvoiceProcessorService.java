package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.*;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.dto.result.KingDeePayExpensesQueryResultDto;
import com.vedeng.erp.kingdee.service.KingDeePayExpensesApiService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购费用的应付单 以及 票的数据推送
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchExpensePayableOrderInvoiceProcessorService extends BaseProcessor<BatchInvoiceDto, BatchPayExpensesDto> {

    public static final String SPECIAL_INVOICE = "专用发票";


    @Autowired
    private BatchBuyorderExpenseDtoMapper batchExpenseBuyorderExpenseDtoMapper;

    @Autowired
    private BatchBuyorderExpenseItemDtoMapper batchBuyorderExpenseItemDtoMapper;

    @Autowired
    private BatchInvoiceDetailDtoMapper batchInvoiceDetailDtoMapper;


    @Autowired
    private KingDeePayExpensesApiService kingDeePayExpensesApiService;

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Override
    public BatchPayExpensesDto doProcess(BatchInvoiceDto batchInvoiceDto, JobParameters params, ExecutionContext stepContext) throws Exception {
        // 应付单对象

        log.info("处理采购费用单 票{}", JSON.toJSONString(batchInvoiceDto));
        boolean special;
        if (StrUtil.isNotEmpty(batchInvoiceDto.getInvoiceTypeName()) && batchInvoiceDto.getInvoiceTypeName().contains(SPECIAL_INVOICE)) {
            special = true;
            // 过滤未认证的专票
            if (!batchInvoiceDto.getIsAuth().equals(1)) {
                return null;
            }
        } else {
            special = false;
        }

        batchInvoiceDto.setBatchInvoiceDetailDtoList(batchInvoiceDetailDtoMapper.findByInvoiceId(batchInvoiceDto.getInvoiceId()));

        // 根据发票的订单详情ID查询所有的采购费用详情数据
        Set<Integer> invoiceDetailDtoSet = batchInvoiceDto.getBatchInvoiceDetailDtoList().stream().map(BatchInvoiceDetailDto::getDetailgoodsId).collect(Collectors.toSet());
        List<BatchBuyorderExpenseItemDto> buyorderExpenseItemDtoList = batchBuyorderExpenseItemDtoMapper.findByBuyorderExpenseItemIdIn(invoiceDetailDtoSet);
        if (CollUtil.isEmpty(buyorderExpenseItemDtoList)) {
            log.warn("无采购费用明细单,费用详细单据ids{}", invoiceDetailDtoSet);
            return null;
        }

        List<KingDeePayExpensesQueryResultDto> kingDeePayExpenses = kingDeePayExpensesApiService.getKingDeePayExpenses(batchInvoiceDto.getInvoiceId());
        if (CollUtil.isEmpty(kingDeePayExpenses)) {
            log.warn("采购应付单未在金蝶查到数据{}",batchInvoiceDto.getInvoiceId());
            return null;
        }
        // 将发票价格和数量合并到费用单信息中
        Map<Integer, List<BatchInvoiceDetailDto>> listMap = batchInvoiceDto.getBatchInvoiceDetailDtoList()
                .stream().collect(Collectors.groupingBy(BatchInvoiceDetailDto::getDetailgoodsId));
        buyorderExpenseItemDtoList.forEach(b -> {
            List<BatchInvoiceDetailDto> batchInvoiceDetailDtos = listMap.get(b.getBuyorderExpenseItemId());
            if (CollUtil.isNotEmpty(batchInvoiceDetailDtos)) {
                BatchInvoiceDetailDto batchInvoiceDetailDto = CollUtil.getFirst(batchInvoiceDetailDtos);
                b.setInvoicePrice(batchInvoiceDetailDto.getPrice());
                b.setPreciseNum(batchInvoiceDetailDto.getNum());
                List<Integer> ids = batchInvoiceDetailDtos.stream().map(BatchInvoiceDetailDto::getInvoiceDetailId).collect(Collectors.toList());
                b.setInvoiceDetailIds(ids);

            }
        });


        BatchBuyorderExpenseDto build = BatchBuyorderExpenseDto.builder().buyorderExpenseId(batchInvoiceDto.getRelatedId()).build();
        List<BatchBuyorderExpenseDto> batchBuyorderExpenseDtoList = batchExpenseBuyorderExpenseDtoMapper.findByAll(build);
        if (CollUtil.isEmpty(batchBuyorderExpenseDtoList)) {
            log.warn("无采购费用单:{}", JSON.toJSONString(build));
            return null;
        }
        BatchBuyorderExpenseDto batchBuyorderExpenseDto = CollUtil.getFirst(batchBuyorderExpenseDtoList);
        log.info("采购费用单信息:{}", JSON.toJSONString(batchBuyorderExpenseDto));


        DecimalFormat decimalFormat = new DecimalFormat("0.00#");
        String taxRate = Objects.isNull(batchInvoiceDto.getRatio()) ? "0.00" : special?decimalFormat.format(batchInvoiceDto.getRatio().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)):"0.00";
        InPutFeeSpecialInvoiceDto inPutFeeSpecialInvoiceDto = null;
        InPutFeePlainInvoiceDto inPutFeePlainInvoiceDto = null;

        if (special) {
            //专用发票对象
            inPutFeeSpecialInvoiceDto = new InPutFeeSpecialInvoiceDto();
            inPutFeeSpecialInvoiceDto.setFQzokBddjtid(batchInvoiceDto.getInvoiceId().toString());
            Boolean exist = kingDeeBaseApi.isExist(inPutFeeSpecialInvoiceDto);
            if (exist) {
                return null;
            }
            inPutFeeSpecialInvoiceDto.setFid("0");
            inPutFeeSpecialInvoiceDto.setFdate(DateUtil.formatDateTime(DateUtil.date(batchInvoiceDto.getValidTime())));
            inPutFeeSpecialInvoiceDto.setFinvoicedate(DateUtil.formatDateTime(DateUtil.date(batchInvoiceDto.getValidTime())));
            inPutFeeSpecialInvoiceDto.setFinvoiceno(batchInvoiceDto.getInvoiceNo());
            inPutFeeSpecialInvoiceDto.setFsupplierid(batchInvoiceDto.getTraderSupplierId().toString());
            inPutFeeSpecialInvoiceDto.setFRedBlue("0");
            inPutFeeSpecialInvoiceDto.setFQzokFpdm(batchInvoiceDto.getInvoiceCode());
        }

        if (!special) {
            //普通蓝字发票对象
            inPutFeePlainInvoiceDto = new InPutFeePlainInvoiceDto();
            inPutFeePlainInvoiceDto.setFQzokBddjtid(batchInvoiceDto.getInvoiceId().toString());
            Boolean exist = kingDeeBaseApi.isExist(inPutFeePlainInvoiceDto);
            if (exist) {
                return null;
            }
            inPutFeePlainInvoiceDto.setFid("0");
            inPutFeePlainInvoiceDto.setFdate(DateUtil.formatDateTime(DateUtil.date(batchInvoiceDto.getValidTime())));
            inPutFeePlainInvoiceDto.setFinvoicedate(DateUtil.formatDateTime(DateUtil.date(batchInvoiceDto.getValidTime())));
            inPutFeePlainInvoiceDto.setFinvoiceno(batchInvoiceDto.getInvoiceNo());
            inPutFeePlainInvoiceDto.setFsupplierid(batchInvoiceDto.getTraderSupplierId().toString());
            inPutFeePlainInvoiceDto.setFRedBlue("0");
            inPutFeePlainInvoiceDto.setFQzokFpdm(batchInvoiceDto.getInvoiceCode());
        }

        boolean finalSpecial = special;
        InPutFeeSpecialInvoiceDto finalInPutFeeSpecialInvoiceDto = inPutFeeSpecialInvoiceDto;
        InPutFeePlainInvoiceDto finalInPutFeePlainInvoiceDto = inPutFeePlainInvoiceDto;
        buyorderExpenseItemDtoList.forEach(x -> {

            // 总金额
            if (finalSpecial) {
                this.buildSpecialInvoice(batchBuyorderExpenseDto, finalInPutFeeSpecialInvoiceDto, x,taxRate,kingDeePayExpenses);
            }
            if (!finalSpecial) {
                this.buildPlainInvoice(batchBuyorderExpenseDto, finalInPutFeePlainInvoiceDto, x,taxRate,kingDeePayExpenses);
            }
        });

        return BatchPayExpensesDto.builder()
                .inPutFeePlainInvoiceDto(inPutFeePlainInvoiceDto)
                .inPutFeeSpecialInvoiceDto(inPutFeeSpecialInvoiceDto)
                .build();
    }

    private void buildPlainInvoice(BatchBuyorderExpenseDto batchBuyorderExpenseDto, InPutFeePlainInvoiceDto inPutFeePlainInvoiceDto,
                                   BatchBuyorderExpenseItemDto detailDto,String taxRate,List<KingDeePayExpensesQueryResultDto> kingDeePayExpenses) {
        // 发票明细
        InPutFeePlainInvoiceDetailDto inPutFeePlainInvoiceDetailDto = new InPutFeePlainInvoiceDetailDto();
        inPutFeePlainInvoiceDetailDto.setFsourcetype("AP_Payable");
        inPutFeePlainInvoiceDetailDto.setFexpenseid(detailDto.getBuyorderExpenseItemDetailDto().getUnitKingDeeNo());
        inPutFeePlainInvoiceDetailDto.setFQty(detailDto.getPreciseNum().toString());
        inPutFeePlainInvoiceDetailDto.setFunitprice(detailDto.getInvoicePrice());
        inPutFeePlainInvoiceDetailDto.setFtaxrate(taxRate);
        String detailIds = detailDto.getInvoiceDetailIds().stream().sorted().map(String::valueOf).collect(Collectors.joining(StrUtil.DASHED));
        inPutFeePlainInvoiceDetailDto.setFQzokBddjhid(detailIds);
        inPutFeePlainInvoiceDetailDto.setFQzokBdsku(detailDto.getBuyorderExpenseItemDetailDto().getSku());
        inPutFeePlainInvoiceDetailDto.setFQzokYwlx("采购费用");
        // 蓝票场景 1.采购费用单  2.售后手续费业务费用单
        inPutFeePlainInvoiceDetailDto.setFQzokGsywdh(batchBuyorderExpenseDto.getBuyorderExpenseNo());
        //采购费用单：该费用单关联的采购订单号，无关联则取费用单号
        //售后手续费业务费用单：该费用单关联的 采购售后单 的父级采购单号
        String sourceOrderNo =  StrUtil.isEmpty(batchBuyorderExpenseDto.getBuyorderNo()) ? batchBuyorderExpenseDto.getBuyorderExpenseNo()
                : batchBuyorderExpenseDto.getBuyorderNo();
        inPutFeePlainInvoiceDetailDto.setFQzokYsddh(sourceOrderNo);
        inPutFeePlainInvoiceDto.getFPUREXPINVENTRY().add(inPutFeePlainInvoiceDetailDto);
        List<KingDeePayExpensesQueryResultDto> collect = kingDeePayExpenses.stream().filter(x ->StrUtil.split(x.getF_QZOK_BDDJHID(),StrUtil.DASHED).get(0).equals(detailDto.getInvoiceDetailIds().get(0).toString())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            collect.forEach(x -> {
                String fPriceQty = x.getFPriceQty();
                BigDecimal fTaxPrice = x.getFTaxPrice();
                BigDecimal thisTotalAmount = fTaxPrice.multiply(new BigDecimal(fPriceQty)).setScale(2, RoundingMode.HALF_UP);
                // 发票来源订单信息
                InPutFeePlainInvoiceDetailLinkDto inPutFeePlainInvoiceDetailLinkDto = new InPutFeePlainInvoiceDetailLinkDto();
                inPutFeePlainInvoiceDetailLinkDto.setFLinkId("0");
                inPutFeePlainInvoiceDetailLinkDto.setFpurexpinventryLinkFsbillid(x.getFID());
                inPutFeePlainInvoiceDetailLinkDto.setFpurexpinventryLinkFsid(x.getFEntityDetail_FEntryId());
                inPutFeePlainInvoiceDetailLinkDto.setFpurexpinventryLinkFstableid("0");
                inPutFeePlainInvoiceDetailLinkDto.setFpurexpinventryLinkFruleid("IV_PayableToPUREXVATIN");
                inPutFeePlainInvoiceDetailLinkDto.setFpurexpinventryLinkFstablename("T_AP_PAYABLEENTRY");
                inPutFeePlainInvoiceDetailLinkDto.setFpurexpinventryLinkFamountforDold(thisTotalAmount.toString());
                inPutFeePlainInvoiceDetailLinkDto.setFpurexpinventryLinkFamountforD(thisTotalAmount.toString());
                inPutFeePlainInvoiceDetailDto.getFPUREXPINVENTRY_Link().add(inPutFeePlainInvoiceDetailLinkDto);
            });

        }

        log.info("普票信息:{}", JSON.toJSONString(inPutFeePlainInvoiceDetailDto));
    }

    private void buildSpecialInvoice(BatchBuyorderExpenseDto batchBuyorderExpenseDto, InPutFeeSpecialInvoiceDto inPutFeeSpecialInvoiceDto,
                                     BatchBuyorderExpenseItemDto detailDto, String taxRate,List<KingDeePayExpensesQueryResultDto> kingDeePayExpenses) {
        // 发票明细
        InPutFeeSpecialInvoiceDetailDto inPutFeeSpecialInvoiceDetailDto = new InPutFeeSpecialInvoiceDetailDto();
        inPutFeeSpecialInvoiceDetailDto.setFsourcetype("AP_Payable");
        inPutFeeSpecialInvoiceDetailDto.setFexpenseid(detailDto.getBuyorderExpenseItemDetailDto().getUnitKingDeeNo());
        inPutFeeSpecialInvoiceDetailDto.setFQty(detailDto.getPreciseNum().toString());
        inPutFeeSpecialInvoiceDetailDto.setFunitprice(detailDto.getInvoicePrice());
        inPutFeeSpecialInvoiceDetailDto.setFtaxrate(taxRate);
        String detailIds = detailDto.getInvoiceDetailIds().stream().sorted().map(String::valueOf).collect(Collectors.joining(StrUtil.DASHED));
        inPutFeeSpecialInvoiceDetailDto.setFQzokBddjhid(detailIds);
        inPutFeeSpecialInvoiceDetailDto.setFQzokBdsku(detailDto.getBuyorderExpenseItemDetailDto().getSku());
        inPutFeeSpecialInvoiceDetailDto.setFQzokYwlx("采购费用");

        // 蓝票场景 1.采购费用单  2.售后手续费业务费用单
        inPutFeeSpecialInvoiceDetailDto.setFQzokGsywdh(batchBuyorderExpenseDto.getBuyorderExpenseNo());
        //采购费用单：该费用单关联的采购订单号，无关联则取费用单号
        //售后手续费业务费用单：该费用单关联的 采购售后单 的父级采购单号
        String sourceOrderNo = StrUtil.isEmpty(batchBuyorderExpenseDto.getBuyorderNo()) ? batchBuyorderExpenseDto.getBuyorderExpenseNo()
                : batchBuyorderExpenseDto.getBuyorderNo();
        inPutFeeSpecialInvoiceDetailDto.setFQzokYsddh(sourceOrderNo);
        inPutFeeSpecialInvoiceDto.getFPUREXPINVENTRY().add(inPutFeeSpecialInvoiceDetailDto);
        List<KingDeePayExpensesQueryResultDto> collect = kingDeePayExpenses.stream().filter(x ->StrUtil.split(x.getF_QZOK_BDDJHID(),StrUtil.DASHED).get(0).equals(detailDto.getInvoiceDetailIds().get(0).toString())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            collect.forEach(x->{
                String fPriceQty = x.getFPriceQty();
                BigDecimal fTaxPrice = x.getFTaxPrice();
                BigDecimal thisTotalAmount = fTaxPrice.multiply(new BigDecimal(fPriceQty)).setScale(2, RoundingMode.HALF_UP);
                // 发票来源订单信息
                InPutFeeSpecialInvoiceDetailLinkDto inPutFeeSpecialInvoiceDetailLinkDto = new InPutFeeSpecialInvoiceDetailLinkDto();
                inPutFeeSpecialInvoiceDetailLinkDto.setFLinkId("0");
                inPutFeeSpecialInvoiceDetailLinkDto.setFpurexpinventryLinkFsbillid(x.getFID());
                inPutFeeSpecialInvoiceDetailLinkDto.setFpurexpinventryLinkFsid(x.getFEntityDetail_FEntryId());
                inPutFeeSpecialInvoiceDetailLinkDto.setFpurexpinventryLinkFstableid("0");
                inPutFeeSpecialInvoiceDetailLinkDto.setFpurexpinventryLinkFruleid("IV_PayableToPUREXVATIN");
                inPutFeeSpecialInvoiceDetailLinkDto.setFpurexpinventryLinkFstablename("T_AP_PAYABLEENTRY");
                inPutFeeSpecialInvoiceDetailLinkDto.setFpurexpinventryLinkFamountforDold(thisTotalAmount.toString());
                inPutFeeSpecialInvoiceDetailLinkDto.setFpurexpinventryLinkFamountforD(thisTotalAmount.toString());
                inPutFeeSpecialInvoiceDetailDto.getFPUREXPINVENTRY_Link().add(inPutFeeSpecialInvoiceDetailLinkDto);
            });

        }

        log.info("专票信息:{}", JSON.toJSONString(inPutFeeSpecialInvoiceDetailDto));
    }




}
