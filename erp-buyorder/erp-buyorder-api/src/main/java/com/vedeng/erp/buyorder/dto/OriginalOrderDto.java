package com.vedeng.erp.buyorder.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 原单信息
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class OriginalOrderDto  implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 销售单号
     */
    private String no;
    /**
     * 销售单id
     */
    private Integer orderId;

    /**
     * 销售售后单id
     */
    private Integer afterSalesOrderId;

    /**
     * 销售单商品列表
     */
    private List<OriginalOrderGoodsDto> goodsDtos;

}
