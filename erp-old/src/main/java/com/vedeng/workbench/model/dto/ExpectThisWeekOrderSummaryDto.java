package com.vedeng.workbench.model.dto;

import com.vedeng.dwh.model.dto.DwhErpUserDto;
import lombok.Data;

import java.math.BigDecimal;


@Data
public class ExpectThisWeekOrderSummaryDto {


    /**
     * 统计对象名称
     */
    private String name;

    /**
     * 统计对象id
     */
    private Integer id;

    /**
     * 统计销售对象信息
     */
    private DwhErpUserDto userInfo;

    /**
     * 上周预计成单数量
     */
    private Integer lastWeekSuccessNum;

    /**
     * 上周未成单数量
     */
    private Integer lastWeekFailNum;

    /**
     * 打靶率
     */
    private BigDecimal hitRate;

    /**
     * 预计本周成单数量
     */
    private Integer thisWeekSuccessNum;

    /**
     * 预计本周成单金额
     */
    private BigDecimal thisWeekAmount;
}
