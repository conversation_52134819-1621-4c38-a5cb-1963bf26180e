package com.vedeng.goodsSearch.constant;

/**
 * 品牌来源枚举
 *
 * <AUTHOR>
 */
public enum BrandNatureEnum {

    /**
     * 进口
     */
    DOMESTIC(1, "进口"),

    /**
     * 国产
     */
    IMPORT(2, "国产");

    /**
     * 代码
     */
    private Integer code;

    /**
     * 描述
     */
    private String desc;

    BrandNatureEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
