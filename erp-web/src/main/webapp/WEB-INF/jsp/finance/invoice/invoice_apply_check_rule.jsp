<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<style>
    table {
        width: 100%;
        border-collapse: collapse;

    }
    .tip{
        margin-top: 20px;
        margin-left: 20px;
        text-align: left;
        height: 12%;
        font-size: 14px;
    }
</style>
<div>
    <div class="tip">申请检查不通过，将发起提前开票申请，是否继续？</div>
    <div>
        <table class="layui-table" >
            <thead style="font-weight: bold;text-align: center;">
            <tr>
                <td>规则名称</td>
                <td>规则内容</td>
                <td>不通过原因</td>
            </tr>
            </thead>
            <tbody>
            <c:forEach var="list" items="${invoiceCheckResultDto.invoiceCheckResultDetailDtoList}" varStatus="status">
                <tr>
                    <td>${list.ruleName}</td>
                    <td>${list.ruleContent}</td>
                    <td>${list.promptText}</td>
                </tr>
            </c:forEach>
            </tbody>
        </table>
    </div>
    <div class="tip">注：以上检查项目未包含与商品相关的内容，最终提交申请时，不通过项目可能会发生变化。如有疑问请联系财务处理</div>

</div>

</body>
