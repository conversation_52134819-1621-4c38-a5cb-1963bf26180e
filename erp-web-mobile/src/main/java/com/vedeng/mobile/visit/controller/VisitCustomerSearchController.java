package com.vedeng.mobile.visit.controller;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.base.BaseResponseCode;
import com.vedeng.common.core.base.Page;
import com.vedeng.common.core.base.R;
import com.vedeng.erp.system.common.enums.UserOrgEnums;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.service.VisitRecordApiService;
import com.vedeng.mobile.system.dto.*;
import com.vedeng.mobile.system.model.VisitRecord;
import com.vedeng.mobile.system.model.WebAccount;
import com.vedeng.mobile.system.service.RegionService;
import com.vedeng.mobile.system.service.TraderCustomerSearchService;
import com.vedeng.mobile.system.service.VisitRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2023/12/5
 */
@Slf4j
@RestController
@RequestMapping("/mobile/visit/customer")
@RequiredArgsConstructor
public class VisitCustomerSearchController {



    @Autowired
    private TraderCustomerSearchService traderCustomerSearchService;

    @Autowired
    private RegionService regionService;

    @Autowired
    private VisitRecordService visitRecordService;

    @Autowired
    private VisitRecordApiService visitRecordApiService;

    @Autowired
    private UserApiService userService;

    private static final Integer CUSTOMER_TYPE_ERP = 1;//erp
    private static final Integer CUSTOMER_TYPE_ZDK = 2;//终端库
    private static final Integer CUSTOMER_TYPE_TYC = 3;//天眼查
    /**
     * 查询客户列表
     * 此处获取两个参数，一个是客户名称，一个是客户类型
     * @return
     */
    @RequestMapping(value = "/searchCustomerList")
    @ResponseBody
    public R<PageResult<TraderCustomerSearchResultDto>> searchCustomerList(
            @RequestParam(required = false, defaultValue = "1") Integer pageNo,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize,
            @RequestParam(required = false)String customerName,
            @RequestParam(required = false) Integer customerType,
            @RequestParam(required = true) Integer userId) {
        UserOrgEnums userOrgEnums = userService.getUserOrgEnum(userId);
        if(userOrgEnums.equals(UserOrgEnums.ORG_OTHER)){
            return R.error("目前仅对营销中心部分部门开放，如有需求请联系研发产品；");
        }
        Page page = new Page(pageNo, pageSize);
        PageResult<TraderCustomerSearchResultDto> resultPage = new PageResult<>();
        switch (customerType){
            case 1: {
                TraderCustomerSearchApiDto traderCustomerVo = new TraderCustomerSearchApiDto();
                traderCustomerVo.setSearchTraderName(customerName);
                traderCustomerVo.setCompanyId(1);
                traderCustomerVo.setIsEnable(1);
                PageInfo<TraderCustomerSearchResultDto> pageInfo = null;
                switch (userOrgEnums){
                    case ORG_DAQU:  //大区用户
                        //获取该用户下的所有归属销售userId
                        //区域内ERP客户为自己、下属对应区域内ERP客户+归属自己客户+归属下属客户-客户在区域内，
                        // 但归属销售不为区域内线上销售的客户-归属销售为非公集团客户部的销售业务组的客户；区域对应关系通过客户办公区域和线下销售负责区域相匹配；
                        List<Integer> userIds = userService.getAllSubordinateByUserIdForVisit(userId);
                        traderCustomerVo.setUserIdList(userIds);
                        pageInfo = traderCustomerSearchService.searchTraderCustomerListPage(traderCustomerVo, page);
                        break;
                    case ORG_FEIGONG://营销中心非公集团客户部
                        // 非公集团客户部的销售业务组：ERP客户（只可选择归属自己+下属的客户信息）》终端库终端》天眼查用户，后
                        // 再将终端库和天眼查的客户名称和erp名称进行匹配，如果一致的记为ERP客户并记下客户ID；
                        List<Integer> userIds2 = userService.getAllSubordinateByUserIdForVisit(userId);
                        traderCustomerVo.setUserIdList(userIds2);
                        pageInfo = traderCustomerSearchService.searchTraderCustomerListPageForFeigong(traderCustomerVo, page);
                        break;
                    case ORG_YINGJI:  //营销中心应急业务部、营销中心产品方案部
                        //应急业务部、产品方案取全量客户数据》终端库终端》天眼查用户，后再将终端库和天眼查的客户名称和erp名称进行匹配，如果一致的记为ERP客户并记下客户ID；
                        pageInfo = traderCustomerSearchService.searchTraderCustomerListPageForYingji(traderCustomerVo, page);
                        break;
                    default:
                        return R.error("目前仅对营销中心部分部门开放，如有需求请联系研发产品；");
                }

                resultPage.setDataList(pageInfo.getList());
                resultPage.setPageSize(pageInfo.getPageSize());
                resultPage.setPageNo(pageInfo.getPageNum());
                resultPage.setTotalPage(pageInfo.getPages());
                resultPage.setTotalRecord(pageInfo.getTotal());
                doPageType(resultPage,CUSTOMER_TYPE_ERP);
                return R.success(resultPage);
            }
            case 2:{
                PageInfo<TraderCustomerSearchResultDto> pageInfo = traderCustomerSearchService.getOneDataTerminalInfo(customerName,page.getPageSize(),page.getPageNo());
                resultPage.setDataList(pageInfo.getList());
                resultPage.setPageSize(pageInfo.getPageSize());
                resultPage.setPageNo(pageInfo.getPageNum());
                resultPage.setTotalPage(pageInfo.getPages());
                resultPage.setTotalRecord(pageInfo.getTotal());
                doPageType(resultPage,CUSTOMER_TYPE_ZDK);
                return R.success(resultPage);
            }
            case 3:{
                PageInfo<TraderCustomerSearchResultDto> pageInfo = traderCustomerSearchService.getTycTerminalInfo(customerName,page.getPageSize(),page.getPageNo());
                resultPage.setDataList(pageInfo.getList());
                resultPage.setPageSize(pageInfo.getPageSize());
                resultPage.setPageNo(pageInfo.getPageNum());
                resultPage.setTotalPage(pageInfo.getPages());
                resultPage.setTotalRecord(pageInfo.getTotal());
                doPageType(resultPage,CUSTOMER_TYPE_TYC);
                return R.success(resultPage);
            }
        }
        return R.error("参数错误");
    }



    public void doPageType(PageResult<TraderCustomerSearchResultDto> resultPage,Integer type){
        if(resultPage!= null && CollectionUtils.isNotEmpty(resultPage.getDataList())){
List<TraderCustomerSearchResultDto> dataList = resultPage.getDataList();
            dataList.stream().forEach(item -> {
                item.setCustomerFrom(type);
                if(item.getAreaId() != null && item.getAreaId()>0){
                    RegionInnerDto regionInnerDto = regionService.queryRegionByThreeId(item.getAreaId());
                    if(regionInnerDto != null){
                        //item.setAreaName(regionInnerDto.getRegionName());
                        if(regionInnerDto.getRegionName().equals("中国")){ //兼容老数据,可能存在维护中国/浙江的数据
                            if(CollectionUtils.isNotEmpty(regionInnerDto.getRegionList())){
                                RegionInnerDto province = regionInnerDto.getRegionList().get(0);
                                item.setProvinceId(province.getRegionId());
                                item.setProvinceName(province.getRegionName());
                                if(CollectionUtils.isNotEmpty(province.getRegionList())){
                                    item.setCityId(province.getRegionList().get(0).getRegionId());
                                    item.setCityName(province.getRegionList().get(0).getRegionName());
                                    RegionInnerDto city = province.getRegionList().get(0);
                                    if(CollectionUtils.isNotEmpty(city.getRegionList())) {
                                        item.setAreaId(city.getRegionList().get(0).getRegionId());
                                        item.setAreaName(city.getRegionList().get(0).getRegionName());
                                    }
                                }
                            }
                        }else{
                            item.setProvinceId(regionInnerDto.getRegionId());
                            item.setProvinceName(regionInnerDto.getRegionName());
                            if(CollectionUtils.isNotEmpty(regionInnerDto.getRegionList())){
                                item.setCityId(regionInnerDto.getRegionList().get(0).getRegionId());
                                item.setCityName(regionInnerDto.getRegionList().get(0).getRegionName());
                                RegionInnerDto city = regionInnerDto.getRegionList().get(0);
                                if(CollectionUtils.isNotEmpty(city.getRegionList())) {
                                    item.setAreaId(city.getRegionList().get(0).getRegionId());
                                    item.setAreaName(city.getRegionList().get(0).getRegionName());
                                }
                            }
                        }
                    }
                }

            });
        }

    }

    public TraderCustomerSearchResultDto getLastVisitRecord(TraderCustomerInputDto traderCustomerInputDto) {
        VisitRecord visitRecord = visitRecordService.queryLastVisitRecord(traderCustomerInputDto.getTraderName());
        if (visitRecord != null) {
            TraderCustomerSearchResultDto resultDto = new TraderCustomerSearchResultDto();
            resultDto.setTraderId(traderCustomerInputDto.getTraderId());
            resultDto.setTraderCustomerId(traderCustomerInputDto.getTraderCustomerId());
            resultDto.setTraderName(traderCustomerInputDto.getTraderName());
            resultDto.setUserId(traderCustomerInputDto.getUserId());
            resultDto.setCustomerNature(traderCustomerInputDto.getCustomerNature());
            resultDto.setTzCustomer(traderCustomerInputDto.getTzCustomer());
            //将resultDto的字段逐个赋值，取自traderCustomerInputDto
            resultDto.setTzCustomerLevel(traderCustomerInputDto.getTzCustomerLevel());
            resultDto.setCustomerFrom(traderCustomerInputDto.getCustomerFrom());

            resultDto.setProvinceId(visitRecord.getProvinceCode());
            resultDto.setProvinceName(visitRecord.getProvinceName());
            resultDto.setCityId(visitRecord.getCityCode());
            resultDto.setCityName(visitRecord.getCityName());
            resultDto.setAreaId(visitRecord.getAreaCode());
            resultDto.setAreaName(visitRecord.getAreaName());
            return resultDto;
        }
        return null;
    }

    public TraderCustomerSearchResultDto getTraderForInput(TraderCustomerInputDto traderCustomerInputDto) {
        TraderCustomerSearchResultDto resultDto = new TraderCustomerSearchResultDto();
        resultDto.setTraderId(traderCustomerInputDto.getTraderId());
        resultDto.setTraderCustomerId(traderCustomerInputDto.getTraderCustomerId());
        resultDto.setTraderName(traderCustomerInputDto.getTraderName());
        resultDto.setUserId(traderCustomerInputDto.getUserId());
        resultDto.setCustomerNature(traderCustomerInputDto.getCustomerNature());
        resultDto.setTzCustomer(traderCustomerInputDto.getTzCustomer());
        //将resultDto的字段逐个赋值，取自traderCustomerInputDto
        resultDto.setTzCustomerLevel(traderCustomerInputDto.getTzCustomerLevel());
        resultDto.setCustomerFrom(traderCustomerInputDto.getCustomerFrom());

        resultDto.setProvinceId(traderCustomerInputDto.getProvinceCode());
        resultDto.setProvinceName(traderCustomerInputDto.getProvinceName());
        resultDto.setCityId(traderCustomerInputDto.getCityCode());
        resultDto.setCityName(traderCustomerInputDto.getCityName());
        resultDto.setAreaId(traderCustomerInputDto.getAreaCode());
        resultDto.setAreaName(traderCustomerInputDto.getAreaName());
        return resultDto;

    }

    @ResponseBody
    @RequestMapping(value = "/checkTraderCustomer")
    public R<TraderCustomerSearchResultDto> checkTraderCustomer(@RequestBody TraderCustomerInputDto traderCustomerInputDto) {
        log.info("checkTraderCustomer :"+ JSONObject.toJSONString(traderCustomerInputDto));
        try {
            UserOrgEnums userOrgEnums = userService.getUserOrgEnum(traderCustomerInputDto.getUserId());
            switch (userOrgEnums){
                case ORG_DAQU:
                    //return R.success(userOrgEnums.getOrgGroupName());
                    //大区的客户，需要根据省市区进行匹配。
                    //根据传入的选择的客户信息，判断是否在自己所负责的区域内。
                    if(traderCustomerInputDto.getAreaCode()!=null && traderCustomerInputDto.getAreaCode() >0 ){// 如果这个数字不为空。即有对应的区域
                        VisitRecord  checkTraderCanCreate = visitRecordService.checkTraderCanCreate(traderCustomerInputDto);
                        if(checkTraderCanCreate!=null){
                            String date = DateUtil.formatDate(checkTraderCanCreate.getAddTime());
                            UserDto user = userService.getUserById(checkTraderCanCreate.getAddUserId());
                            R r = new R(BaseResponseCode.FAILURE.getCode(),"该客户已被"+user.getUsername()+"于"+date+"创建拜访计划，创建半年内不可重复创建拜访计划",getTraderForInput(traderCustomerInputDto));
                            return r;
                        }
                        boolean checkCanSelect = traderCustomerSearchService.checkTraderCanSelected(traderCustomerInputDto);
                        if(!checkCanSelect){
                            TraderCustomerSearchResultDto lastVisitRecord = getLastVisitRecord(traderCustomerInputDto);
                            if(lastVisitRecord!=null){
                                return R.success(userOrgEnums.getOrgGroupName()+"可选择",lastVisitRecord);
                            }
                            R r = new R(BaseResponseCode.CONFIRM.getCode(),"该客户不在您所属区域，是否选择后，重新填写地区信息？",getTraderForInput(traderCustomerInputDto));
                            return r;
                        }
                        return R.success(userOrgEnums.getOrgGroupName()+"可选择",getTraderForInput(traderCustomerInputDto));

                    }else{
                        TraderCustomerSearchResultDto lastVisitRecord = getLastVisitRecord(traderCustomerInputDto);
                        if(lastVisitRecord!=null){
                            return R.success(userOrgEnums.getOrgGroupName()+"可选择",lastVisitRecord);
                        }
                        R r = new R(BaseResponseCode.CONFIRM.getCode(),"该客户不在您所属区域，是否选择后，重新填写地区信息？",traderCustomerInputDto);
                        return r;
                    }
                case ORG_YINGJI:
                    return R.success(userOrgEnums.getOrgGroupName()+"可选择",getTraderForInput(traderCustomerInputDto));
                case ORG_FEIGONG:
                    return R.success(userOrgEnums.getOrgGroupName()+"可选择",getTraderForInput(traderCustomerInputDto));
                default:
                    return R.error("目前仅对营销中心部分部门开放，如有需求请联系研发产品；");
            }
            //return R.success(userOrgEnums.getOrgGroupName());
        }catch (Exception e){
            log.error("checkPermission failure", e);
            return R.error("查询失败");
        }

    }


    @RequestMapping(value = "/searchUserList")
    @ResponseBody
    public R<List<MobileUserDto>> searchUserList(@RequestParam(required = true) Integer userId) {
        List<VisitRecordApiService.VisitUser> visitUsers = visitRecordApiService.queryVisitUserListByBelongUser();
        if(CollectionUtils.isNotEmpty(visitUsers)){
            List<MobileUserDto> resultList = visitUsers.stream().map(item -> MobileUserDto.builder()
                    .userId(item.getUserId())
                    .username(item.getUsername())
                    .build()).collect(Collectors.toList());
            return R.success(resultList);
        }
        return R.success(new ArrayList<>());
    }

    @RequestMapping(value = "/getVisitRegList")
    @ResponseBody
    public R<List<VisitRegMobileDto>> getVisitRegList(@RequestBody TraderCustomerSearchResultDto traderCustomerSearchResultDto) {
        try{
            List<WebAccount> list = traderCustomerSearchService.queryWebAccountByTraderId(traderCustomerSearchResultDto.getTraderId());
            if(CollectionUtils.isNotEmpty(list)){
                List<VisitRegMobileDto> visitRegMobileDtoList = list.stream().map(item ->  VisitRegMobileDto.builder()
                        .traderContactId(item.getTraderContactId())
                        .traderId(item.getTraderId())
                        .mobile(item.getMobile()).build()).collect(Collectors.toList());
                return R.success("获取客户注册账号成功",visitRegMobileDtoList);
            }
            return R.success("客户名下无注册账号");
        }catch (Exception e){
            log.error("获取客户注册账号失败",e);
            return R.error("获取客户注册账号失败");
        }
    }


}
