package com.vedeng.erp.trader.domain.entity;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/11 13:40
 */
@Getter
@Setter
public class TraderCustomerAttributeEntity {

    /**
     * 主键id
     */
    private Integer traderCustomerAttributeId;

    /**
     * 客户ID
     */
    private Integer traderCustomerId;

    /**
     * 属性分类ID
     */
    private Integer attributeCategoryId;

    /**
     * 属性ID SYS_OPTION_DEFINITION_ID
     */
    private Integer attributeId;

    /**
     * 其它属性
     */
    private String attributeOther;

    /**
     * 子分类控制
     */
    private String subCategoryIds;
}