package com.vedeng.erp.trader.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 客户联系人转移记录
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class TraderContactTransferRecordEntity extends BaseEntity {


    private Integer traderContactTransferRecordId;

    /**
     * 交易者ID
     */
    private Integer traderId;

    /**
     * 转移前客户ID
     */
    private Integer traderCustomerId;

    /**
     * 转移前客户名称
     */
    private String traderCustomerName;

    /**
     * 转移后客户ID
     */
    private Integer afterTraderCustomerId;

    /**
     * 转移后客户名称
     */
    private String afterTraderCustomerName;

    /**
     * 客户联系人id
     */
    private Integer traderContactId;

}