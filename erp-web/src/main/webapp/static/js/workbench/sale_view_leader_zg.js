$(function () {
    var userPostionName = $("#position").val();
    var userPostionId = $("#id").val();
    var pathUrl = $("#pathUrl").val();
    var thisWeekStartTimeStr = $("#thisWeekStartTimeStr").val();
    var thisWeekEndTimeStr = $("#thisWeekEndTimeStr").val();
    var yesterdayEndtimeStr = $("#yesterdayEndtimeStr").val();
    var todayStartTimeStr = $("#todayStartTimeStr").val();
    var preWeekStartTimeStr = $("#preWeekStartTimeStr").val();
    var preWeekEndTimeStr = $("#preWeekEndTimeStr").val();

    // 管理数据总览
    $.ajax({
        type: "POST",
        data: {userId: userPostionId, positionName: userPostionName},
        url: pathUrl + "/workbench/bussinesschance/leaderDataOverView.do",
        dataType: "json",
        success: function (result) {
            if (result.code == 0) {
                var dataOverviewList = result.data;
                var OverviewhtmlStr = "";
                if (dataOverviewList == null || dataOverviewList.length == 0) {
                    OverviewhtmlStr += "<tr><td colspan='8'>暂无数据</td></tr>";
                    $("#overviewTable").html(OverviewhtmlStr);
                } else {
                    for (var i = 0; i < dataOverviewList.length; i++) {
                        OverviewhtmlStr += "<tr>";

                        if (dataOverviewList[i].name == null) {
                            OverviewhtmlStr += "<td>-</td>"
                        } else {
                            OverviewhtmlStr += "<td>" + dataOverviewList[i].name + "</td>";
                        }

                        if (dataOverviewList[i].totalBussinessAmount == null) {
                            OverviewhtmlStr += "<td>-</td>"
                        } else {
                            OverviewhtmlStr += "<td>" + dataOverviewList[i].totalBussinessAmount.toFixed(2) + "</td>";
                        }

                        //商机总数
                        if (dataOverviewList[i].totalBussinessNum == null) {
                            OverviewhtmlStr += "<td>-</td>"
                        } else {
                            OverviewhtmlStr += "<td><div class =\"jump\"><a class=\"addtitle_1\" href=\"javascript:void(0); \"tabTitle=\'{\"num\":\"view" + Math.floor(Math.random() * 10000 + 1) + "\", " +
                            "\"link\":\"" + pathUrl + "/order/bussinesschance/expectChance.do?jumpType=1&searchType=0&searchId=" + dataOverviewList[i].userId + "&starttime=2019-11-1&endtime=" + yesterdayEndtimeStr + "\", " +
                            "\"title\":\"销售商机详情\"}\'>" + dataOverviewList[i].totalBussinessNum + "</a></div></td>";
                        }

                        if (dataOverviewList[i].avrBussinessAmount == null) {
                            OverviewhtmlStr += "<td>-</td>"
                        } else {
                            OverviewhtmlStr += "<td>" + dataOverviewList[i].avrBussinessAmount.toFixed(2) + "</td>";
                        }


                        if (dataOverviewList[i].yesterdayBussinessAmount == null) {
                            OverviewhtmlStr += "<td>-</td>"
                        } else {
                            OverviewhtmlStr += "<td>" + dataOverviewList[i].yesterdayBussinessAmount.toFixed(2) + "</td>";
                        }

                        //昨日新增商机数量
                        if (dataOverviewList[i].yesterdayBussinessNum == null) {
                            OverviewhtmlStr += "<td>-</td>"
                        } else {
                            OverviewhtmlStr += "<td><div class =\"jump\"><a class=\"addtitle_1\" href=\"javascript:void(0); \"tabTitle=\'{\"num\":\"view" + Math.floor(Math.random() * 10000 + 1) + "\", " +
                                "\"link\":\"" + pathUrl + "/order/bussinesschance/expectChance.do?jumpType=1&searchType=0&searchId=" + dataOverviewList[i].userId + "&starttime="+yesterdayEndtimeStr+"&endtime=" + yesterdayEndtimeStr + "\", " +
                                "\"title\":\"销售商机详情\"}\'>" + dataOverviewList[i].yesterdayBussinessNum + "</a></div></td>";
                        }

                        if (dataOverviewList[i].thisWeekBussinessAmount == null) {
                            OverviewhtmlStr += "<td>-</td>"
                        } else {
                            OverviewhtmlStr += "<td>" + dataOverviewList[i].thisWeekBussinessAmount.toFixed(2) + "</td>";
                        }

                        if (dataOverviewList[i].thisWeekBussinessNum == null) {
                            OverviewhtmlStr += "<td>-</td>"
                        } else {
                            OverviewhtmlStr += "<td><div class =\"jump\"><a class=\"addtitle_1\" href=\"javascript:void(0); \"tabTitle=\'{\"num\":\"view" + Math.floor(Math.random() * 10000 + 1) + "\", " +
                                "\"link\":\"" + pathUrl + "/order/bussinesschance/expectChance.do?jumpType=1&searchType=0&searchId=" + dataOverviewList[i].userId + "&starttime="+thisWeekStartTimeStr+"&endtime=" + thisWeekEndTimeStr + "\", " +
                                "\"title\":\"销售商机详情\"}\'>" + dataOverviewList[i].thisWeekBussinessNum + "</a></div></td>";
                        }

                        OverviewhtmlStr += "</tr>";
                    }
                    $("#overviewTable").html(OverviewhtmlStr);
                    $("#overviewTable .addtitle_1").css("color", "#3384ef");
                    $("#overviewTable .addtitle_1").css("font-size", "12px");
                }
            }
        }

    });

    $.ajax({
        //管理待沟通商机
        type: "POST",
        data: {userId: userPostionId, positionName: userPostionName},
        url: pathUrl + "/workbench/bussinesschance/leaderCommunicate.do",
        dataType: "json",
        success: function (result) {
            if (result.code == 0) {
                var commnunicateList = result.data;
                var communicateHtmlStr = "";
                if (commnunicateList == null || commnunicateList.length == 0) {
                    communicateHtmlStr += "<tr><td colspan='6'>暂无数据</td></tr>";
                    $("#communicateTable").html(communicateHtmlStr);
                } else {
                    for (var i = 0; i < commnunicateList.length; i++) {
                        communicateHtmlStr += "<tr>";

                        if (commnunicateList[i].name == null) {
                            communicateHtmlStr += "<td>-</td>"
                        } else {
                            communicateHtmlStr += "<td>" + commnunicateList[i].name + "</td>";
                        }

                        if (commnunicateList[i].planingCommunicateNum == null) {
                            communicateHtmlStr += "<td>-</td>"
                        } else {
                            communicateHtmlStr += "<td><div class =\"jump\"><a class=\"addtitle_1\" href=\"javascript:void(0); \"tabTitle=\'{\"num\":\"view" + Math.floor(Math.random() * 10000 + 1) + "\", " +
                                "\"link\":\"" + pathUrl + "/order/bussinesschance/expectChance.do?jumpType=1&searchType=0&searchId=" + commnunicateList[i].userId + "&nextStartTime="+ yesterdayEndtimeStr +"&nextEndTime=" + yesterdayEndtimeStr + "\", " +
                                "\"title\":\"销售商机详情\"}\'>" + commnunicateList[i].planingCommunicateNum + "</a></div></td>";
                        }

                        if (commnunicateList[i].inPlaningNum == null) {
                            communicateHtmlStr += "<td>-</td>"
                        } else {
                            communicateHtmlStr += "<td>" + commnunicateList[i].inPlaningNum + "</td>";
                        }

                        if (commnunicateList[i].completionRate == null) {
                            communicateHtmlStr += "<td>-</td>"
                        } else {
                            communicateHtmlStr += "<td>" + commnunicateList[i].completionRate + "%</td>";
                        }

                        if (commnunicateList[i].outPlaningNum == null) {
                            communicateHtmlStr += "<td>-</td>"
                        } else {
                            communicateHtmlStr += "<td>" + commnunicateList[i].outPlaningNum + "</td>";
                        }

                        if (commnunicateList[i].todayToCommunicateNum == null) {
                            communicateHtmlStr += "<td>-</td>"
                        } else {
                            communicateHtmlStr += "<td><div class =\"jump\"><a class=\"addtitle_1\" href=\"javascript:void(0); \"tabTitle=\'{\"num\":\"view" + Math.floor(Math.random() * 10000 + 1) + "\", " +
                                "\"link\":\"" + pathUrl + "/order/bussinesschance/expectChance.do?jumpType=1&searchType=0&searchId=" + commnunicateList[i].userId + "&nextStartTime="+ todayStartTimeStr +"&nextEndTime=" + todayStartTimeStr + "\", " +
                                "\"title\":\"销售商机详情\"}\'>" + commnunicateList[i].todayToCommunicateNum + "</a></div></td>";
                        }
                        communicateHtmlStr += "</tr>";
                    }
                    $("#communicateTable").html(communicateHtmlStr);
                    $("#communicateTable .addtitle_1").css("color", "#3384ef");
                    $("#communicateTable .addtitle_1").css("font-size", "12px");
                }
            }

        }
    });


    $.ajax({
        //预计本周成单
        type: "POST",
        data: {userId: userPostionId, positionName: userPostionName},
        url: pathUrl + "/workbench/bussinesschance/leaderExpect.do",
        dataType: "json",
        success: function (result) {
            if (result.code == 0) {
                var expectList = result.data;
                var expectHtmlStr = "";
                if (expectList == null || expectList.length == 0) {
                    expectHtmlStr += "<tr><td colspan='6'>暂无数据</td></tr>";
                    $("#expectTable").html(expectHtmlStr);
                } else {
                    for (var i = 0; i < expectList.length; i++) {
                        expectHtmlStr += "<tr>";

                        if (expectList[i].name == null) {
                            expectHtmlStr += "<td>-</td>"
                        } else {
                            expectHtmlStr += "<td>" + expectList[i].name + "</td>";
                        }

                        if (expectList[i].lastWeekSuccessNum == null) {
                            expectHtmlStr += "<td>-</td>"
                        } else {
                            if (i != expectList.length - 1) {
                                expectHtmlStr += "<td><div class =\"jump\"><a class=\"addtitle_1\" href=\"javascript:void(0); \"tabTitle=\'{\"num\":\"view" + Math.floor(Math.random() * 10000 + 1) + "\", " +
                                    "\"link\":\"" + pathUrl + "/order/bussinesschance/expectChance.do?jumpType=1&searchType=0&searchId=" + expectList[i].id + "&cdstarttime=" + preWeekStartTimeStr + "&cdendtime=" + preWeekEndTimeStr + "\", " +
                                    "\"title\":\"销售商机详情\"}\'>" + expectList[i].lastWeekSuccessNum + "</a></div></td>";
                            } else {
                                expectHtmlStr += "<td><div class =\"jump\"><a class=\"addtitle_1\" href=\"javascript:void(0); \"tabTitle=\'{\"num\":\"view" + Math.floor(Math.random() * 10000 + 1) + "\", " +
                                    "\"link\":\"" + pathUrl + "/order/bussinesschance/expectChance.do?jumpType=1&searchType=0&cdstarttime=" + preWeekStartTimeStr + "&cdendtime=" + preWeekEndTimeStr + "\", " +
                                    "\"title\":\"销售商机详情\"}\'>" + expectList[i].lastWeekSuccessNum + "</a></div></td>";

                            }
                        }

                        if (expectList[i].lastWeekFailNum == null) {
                            expectHtmlStr += "<td>-</td>"
                        } else {
                            expectHtmlStr += "<td>" + expectList[i].lastWeekFailNum + "</td>";
                        }

                        if (expectList[i].hitRate == null) {
                            expectHtmlStr += "<td>-</td>"
                        } else {
                            expectHtmlStr += "<td>" + expectList[i].hitRate + "%</td>";
                        }

                        if (expectList[i].thisWeekSuccessNum == null) {
                            expectHtmlStr += "<td>-</td>"
                        } else {
                            if (userPostionName == "销售主管") {
                                if (i != expectList.length - 1) {
                                    expectHtmlStr += "<td><div class =\"jump\"><a class=\"addtitle_1\" href=\"javascript:void(0); \"tabTitle=\'{\"num\":\"view" + Math.floor(Math.random() * 10000 + 1) + "\", " +
                                        "\"link\":\"" + pathUrl + "/order/bussinesschance/expectChance.do?jumpType=1&searchType=0&searchId=" + expectList[i].id + "&cdstarttime=" + thisWeekStartTimeStr + "&cdendtime=" + thisWeekEndTimeStr + "\", " +
                                        "\"title\":\"销售商机详情\"}\'>" + expectList[i].thisWeekSuccessNum + "</a></div></td>";
                                } else {
                                    expectHtmlStr += "<td><div class =\"jump\"><a class=\"addtitle_1\" href=\"javascript:void(0); \"tabTitle=\'{\"num\":\"view" + Math.floor(Math.random() * 10000 + 1) + "\", " +
                                        "\"link\":\"" + pathUrl + "/order/bussinesschance/expectChance.do?jumpType=1&searchType=0&cdstarttime=" + thisWeekStartTimeStr + "&cdendtime=" + thisWeekEndTimeStr + "\", " +
                                        "\"title\":\"销售商机详情\"}\'>" + expectList[i].thisWeekSuccessNum + "</a></div></td>";

                                }

                            } else {
                                if (i != expectList.length - 1) {
                                    expectHtmlStr += "<td><div class =\"jump\"><a class=\"addtitle_1\" href=\"javascript:void(0); \"tabTitle=\'{\"num\":\"view" + Math.floor(Math.random() * 10000 + 1) + "\", " +
                                        "\"link\":\"" + pathUrl + "/order/bussinesschance/expectChance.do?jumpType=1&searchType=1&searchId=" + expectList[i].id + "&cdstarttime=" + thisWeekStartTimeStr + "&cdendtime=" + thisWeekEndTimeStr + "\", " +
                                        "\"title\":\"销售商机详情\"}\'>" + expectList[i].thisWeekSuccessNum + "</a></div></td>";

                                } else {
                                    expectHtmlStr += "<td><div class =\"jump\"><a class=\"addtitle_1\" href=\"javascript:void(0); \"tabTitle=\'{\"num\":\"view" + Math.floor(Math.random() * 10000 + 1) + "\", " +
                                        "\"link\":\"" + pathUrl + "/order/bussinesschance/expectChance.do?jumpType=1&searchType=1&cdstarttime=" + thisWeekStartTimeStr + "&cdendtime=" + thisWeekEndTimeStr + "\", " +
                                        "\"title\":\"销售商机详情\"}\'>" + expectList[i].thisWeekSuccessNum + "</a></div></td>";
                                }

                            }
                        }

                        if (expectList[i].thisWeekAmount == null) {
                            expectHtmlStr += "<td>-</td>"
                        } else {
                            expectHtmlStr += "<td>" + expectList[i].thisWeekAmount.toFixed(2) + "</td>";
                        }
                        expectHtmlStr += "</tr>";


                    }
                    $("#expectTable").html(expectHtmlStr);
                    $("#expectTable .addtitle_1").css("color", "#3384ef");
                    $("#expectTable .addtitle_1").css("font-size", "12px");
                }
            }
        }
    });


    //主管重点商机
    $.ajax({
        type: "POST",
        data: {userId: userPostionId, positionName: userPostionName},
        url: pathUrl + "/workbench/bussinesschance/leaderImportant/directLeader.do",
        dataType: "json",
        success: function (result) {
            if (result.code == 0) {
                var directLeaderImportantList = result.data;
                var directLeaderImportantHtmlStr = "";
                if (directLeaderImportantList == null || directLeaderImportantList.length == 0) {
                    directLeaderImportantHtmlStr += "<tr><td colspan='5'>暂无数据</td></tr>";
                    $("#importantTable").html(directLeaderImportantHtmlStr);
                } else {
                    for (var i = 0; i < directLeaderImportantList.length; i++) {
                        directLeaderImportantHtmlStr += "<tr>";

                        if (directLeaderImportantList[i].salesName == null) {
                            directLeaderImportantHtmlStr += "<td>-</td>"
                        } else {
                            directLeaderImportantHtmlStr += "<td>" + directLeaderImportantList[i].salesName + "</td>";
                        }

                        if (directLeaderImportantList[i].ssTotalNum == null) {
                            directLeaderImportantHtmlStr += "<td>-</td>"
                        } else {
                            if (i != directLeaderImportantList.length - 1) {
                                directLeaderImportantHtmlStr += "<td><div class =\"jump\"><a class=\"addtitle_1\" href=\"javascript:void(0); \"tabTitle=\'{\"num\":\"view" + Math.floor(Math.random() * 10000 + 1) + "\", " +
                                    "\"link\":\"" + pathUrl + "/order/bussinesschance/expectChance.do?jumpType=1&bussinessLevel=939&searchType=0&searchId=" + directLeaderImportantList[i].salesId + "&starttime=2019-11-1&endtime=" + yesterdayEndtimeStr + "\", " +
                                    "\"title\":\"销售商机详情\"}\'>" + directLeaderImportantList[i].ssTotalNum + "</a></div></td>";
                            } else {
                                directLeaderImportantHtmlStr += "<td><div class =\"jump\"><a class=\"addtitle_1\" href=\"javascript:void(0); \"tabTitle=\'{\"num\":\"view" + Math.floor(Math.random() * 10000 + 1) + "\", " +
                                    "\"link\":\"" + pathUrl + "/order/bussinesschance/expectChance.do?jumpType=1&bussinessLevel=939&searchType=0&starttime=2019-11-1&endtime=" + yesterdayEndtimeStr + "\", " +
                                    "\"title\":\"销售商机详情\"}\'>" + directLeaderImportantList[i].ssTotalNum + "</a></div></td>";

                            }
                        }

                        if (directLeaderImportantList[i].ssTotalAmount == null) {
                            directLeaderImportantHtmlStr += "<td>-</td>"
                        } else {
                            directLeaderImportantHtmlStr += "<td>" + directLeaderImportantList[i].ssTotalAmount.toFixed(2) + "</td>";
                        }

                        if (directLeaderImportantList[i].aaTotalNum == null) {
                            directLeaderImportantHtmlStr += "<td>-</td>"
                        } else {
                            if (i != directLeaderImportantList.length - 1) {
                                directLeaderImportantHtmlStr += "<td><div class =\"jump\"><a class=\"addtitle_1\" href=\"javascript:void(0); \"tabTitle=\'{\"num\":\"view" + Math.floor(Math.random() * 10000 + 1) + "\", " +
                                    "\"link\":\"" + pathUrl + "/order/bussinesschance/expectChance.do?jumpType=1&bussinessLevel=940&searchType=0&searchId=" + directLeaderImportantList[i].salesId + "&starttime=2019-11-1&endtime=" + yesterdayEndtimeStr + "\", " +
                                    "\"title\":\"销售商机详情\"}\'>" + directLeaderImportantList[i].aaTotalNum + "</a></div></td>";
                            } else {
                                directLeaderImportantHtmlStr += "<td><div class =\"jump\"><a class=\"addtitle_1\" href=\"javascript:void(0); \"tabTitle=\'{\"num\":\"view" + Math.floor(Math.random() * 10000 + 1) + "\", " +
                                    "\"link\":\"" + pathUrl + "/order/bussinesschance/expectChance.do?jumpType=1&bussinessLevel=940&searchType=0&starttime=2019-11-1&endtime=" + yesterdayEndtimeStr + "\", " +
                                    "\"title\":\"销售商机详情\"}\'>" + directLeaderImportantList[i].aaTotalNum + "</a></div></td>";

                            }
                        }

                        if (directLeaderImportantList[i].aaTotalAmount == null) {
                            directLeaderImportantHtmlStr += "<td>-</td>"
                        } else {
                            directLeaderImportantHtmlStr += "<td>" + directLeaderImportantList[i].aaTotalAmount.toFixed(2) + "</td>";
                        }

                        directLeaderImportantHtmlStr += "</tr>";
                    }
                    $("#importantTable").html(directLeaderImportantHtmlStr);
                    $("#importantTable .addtitle_1").css("color", "#3384ef");
                    $("#importantTable .addtitle_1").css("font-size", "12px");
                }
            }

        },
        error:function (result) {
            console.log("重点商机获取失败")
        }
    });

});