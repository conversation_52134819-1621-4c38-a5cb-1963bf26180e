package com.vedeng.erp.buyorder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.vedeng.erp.buyorder.domain.entity.BuyOrderRebateChargeApplyEntity;
import com.vedeng.erp.buyorder.dto.BuyOrderRebateApplyDto;
import com.vedeng.erp.buyorder.mapper.BuyorderRebateChargeApplyMapper;
import com.vedeng.erp.buyorder.service.BuyOrderRebateApplyApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class BuyOrderRebateApplyApiServiceImpl implements BuyOrderRebateApplyApiService {
    @Autowired
    private BuyorderRebateChargeApplyMapper buyorderRebateChargeApplyMapper;

    @Override
    public BuyOrderRebateApplyDto selectById(String id) {
        BuyOrderRebateChargeApplyEntity entity = buyorderRebateChargeApplyMapper.selectByPrimaryKey(Integer.valueOf(id));
        BuyOrderRebateApplyDto dto = new BuyOrderRebateApplyDto();
        dto.setBuyOrderRebateChargeNo(entity.getBuyOrderRebateChargeNo());
        dto.setBuyOrderRebateChargeId(entity.getBuyOrderRebateChargeId());
        return dto;
    }
}
