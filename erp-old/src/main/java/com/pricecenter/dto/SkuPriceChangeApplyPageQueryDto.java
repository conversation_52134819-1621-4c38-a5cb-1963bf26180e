package com.pricecenter.dto;

import java.io.Serializable;
import java.util.List;

public class SkuPriceChangeApplyPageQueryDto extends AbstractPageDto implements Serializable {
    //关键词
    private String priceKeyWord;
    //审核状态
    private Integer priceAuditStatus;
    //归属产品经理
    private String productManagerName;
    //归属产品助理
    private String productAssistantName;
    //审核人
    private String checkPersonId;

    //是否已核价
    private Integer alreadyPriced;

    //是否禁用
    private Integer disabled;

    /**
     * 是否需报备
     */
    private Integer isNeedReport;

    /**
     * 价格中心过滤 只包含在这个list里面的数据
     */

    private String includeSkuNosStr;
    
    /**经销价是否含运费:0 未维护 1 含运费 2 不含运费*/
    private Integer isSaleContainsFee;
    

    public Integer getIsSaleContainsFee() {
		return isSaleContainsFee;
	}

	public void setIsSaleContainsFee(Integer isSaleContainsFee) {
		this.isSaleContainsFee = isSaleContainsFee;
	}

	public String getIncludeSkuNosStr() {
        return includeSkuNosStr;
    }

    public void setIncludeSkuNosStr(String includeSkuNosStr) {
        this.includeSkuNosStr = includeSkuNosStr;
    }

    public Integer getIsNeedReport() {
        return isNeedReport;
    }

    public void setIsNeedReport(Integer isNeedReport) {
        this.isNeedReport = isNeedReport;
    }

    public String getPriceKeyWord() {
        return priceKeyWord;
    }

    public void setPriceKeyWord(String priceKeyWord) {
        this.priceKeyWord = priceKeyWord;
    }

    public Integer getPriceAuditStatus() {
        return priceAuditStatus;
    }

    public void setPriceAuditStatus(Integer priceAuditStatus) {
        this.priceAuditStatus = priceAuditStatus;
    }

    public String getProductManagerName() {
        return productManagerName;
    }

    public void setProductManagerName(String productManagerName) {
        this.productManagerName = productManagerName;
    }

    public String getProductAssistantName() {
        return productAssistantName;
    }

    public void setProductAssistantName(String productAssistantName) {
        this.productAssistantName = productAssistantName;
    }

    public String getCheckPersonId() {
        return checkPersonId;
    }

    public void setCheckPersonId(String checkPersonId) {
        this.checkPersonId = checkPersonId;
    }

    public Integer getAlreadyPriced() {
        return alreadyPriced;
    }

    public void setAlreadyPriced(Integer alreadyPriced) {
        this.alreadyPriced = alreadyPriced;
    }

    public Integer getDisabled() {
        return disabled;
    }

    public void setDisabled(Integer disabled) {
        this.disabled = disabled;
    }

}
