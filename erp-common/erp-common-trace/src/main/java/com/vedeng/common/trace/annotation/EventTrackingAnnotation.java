package com.vedeng.common.trace.annotation;

import com.vedeng.common.trace.enums.EventTrackingEnum;

import java.lang.annotation.*;

/**
 * 需要埋点的方法注解
 * @ClassName:  EventTrackingAnnotation
 * @author: <PERSON><PERSON>yang
 * @date:   2024年6月3日 下午2:59:33
 * @Copyright:
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface EventTrackingAnnotation {

    EventTrackingEnum eventTrackingEnum() ;


}