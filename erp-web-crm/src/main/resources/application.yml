server:
  port: 8072
  servlet:
    context-path: /
    jsp:
      init-parameters:
        development: true


spring:
  main:
    allow-bean-definition-overriding: true
  # 数据源配置
  datasource:
#    url: ${jdbc_url}
#    username: ${jdbc_username}
#    password: ${jdbc_password}
    url: ***************************************************************************************************************************************************
    username: fatwrite
    password: fatwrite

    # 使用druid数据源连接池
    type:  com.alibaba.druid.pool.DruidDataSource
    # 加载驱动
    driverClassName: com.mysql.jdbc.Driver
    druid:
      maxActive: 20
      initialSize: 1
      maxWait: 60000
      minIdle: 1
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      validationQuery: select 1
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxOpenPreparedStatements: 20
      webStatFilter:
        enabled: false
  mvc:
    # static文件夹下的静态文件访问路径
    static-path-pattern: /**
    view:
      prefix: /WEB-INF/jsp/
      suffix: .jsp

  # redis
  redis:
    password: ${redis.sentinelPassword}
    jedis:
      pool:
        max-active: ${redis.maxTotal}
        max-wait: ${redis.MaxWaitMillis}
        max-idle: ${redis.maxIdle}
        min-idle: 0
    sentinel:
      #哨兵监听的master名称
      master: ${masterName}
      #哨兵地址列表，多个以,分割
      nodes: ${redis.sentinel1},${redis.sentinel2},${redis.sentinel3}

  servlet:
    multipart:
      enabled: true
      max-file-size: 5MB
      max-request-size: 5MB

  jackson.date-format: yyyy-MM-dd HH:mm:ss
  jackson.time-zone: GMT+8

mybatis:
  executorType: REUSE
  mapper-locations: classpath*:/mapping/*/*Mapper.xml
  typeHandlersPackage: com.vedeng.common.mybatis.handler
  configuration:
    cache-enabled: false
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

pagehelper:
  helper-dialect: mysql
  reasonable: false
  support-methods-arguments: true


eureka:
  client:
    fetch-registry: false # 取消从注册中心获取服务信息
    register-with-eureka: false # 取消注册服务到注册中心

logging:
  config: classpath:logback-spring.xml
  level:
    org:
      springframework:
        boot:
          autoconfigure: ERROR


management:
  health:
    elasticsearch:
      enabled: false
