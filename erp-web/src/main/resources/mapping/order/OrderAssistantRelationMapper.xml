<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.order.dao.OrderAssistantRelationMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.order.model.OrderAssistantRelationDo">
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="ORDER_ASSISTANT_USER_ID" jdbcType="INTEGER" property="orderAssistantUserId" />
    <result column="PRODUCT_MANAGER_USER_ID" jdbcType="INTEGER" property="productManagerUserId" />
    <result column="PRODUCT_ASSITANT_USER_ID" jdbcType="INTEGER" property="productAssitantUserId" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, ORDER_ASSISTANT_USER_ID, PRODUCT_MANAGER_USER_ID, PRODUCT_ASSITANT_USER_ID, ADD_TIME, 
    CREATOR, IS_DELETE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_ORDER_ASSISTANT_RELATION
    where ID = #{id,jdbcType=INTEGER}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_ORDER_ASSISTANT_RELATION
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.order.model.OrderAssistantRelationDo" useGeneratedKeys="true">
    insert into T_ORDER_ASSISTANT_RELATION (ORDER_ASSISTANT_USER_ID, PRODUCT_MANAGER_USER_ID, 
      PRODUCT_ASSITANT_USER_ID, ADD_TIME, CREATOR, 
      IS_DELETE)
    values (#{orderAssistantUserId,jdbcType=INTEGER}, #{productManagerUserId,jdbcType=INTEGER}, 
      #{productAssitantUserId,jdbcType=INTEGER}, #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, 
      #{isDelete,jdbcType=BOOLEAN})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.order.model.OrderAssistantRelationDo" useGeneratedKeys="true">
    insert into T_ORDER_ASSISTANT_RELATION
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderAssistantUserId != null">
        ORDER_ASSISTANT_USER_ID,
      </if>
      <if test="productManagerUserId != null">
        PRODUCT_MANAGER_USER_ID,
      </if>
      <if test="productAssitantUserId != null">
        PRODUCT_ASSITANT_USER_ID,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderAssistantUserId != null">
        #{orderAssistantUserId,jdbcType=INTEGER},
      </if>
      <if test="productManagerUserId != null">
        #{productManagerUserId,jdbcType=INTEGER},
      </if>
      <if test="productAssitantUserId != null">
        #{productAssitantUserId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.order.model.OrderAssistantRelationDo">
    update T_ORDER_ASSISTANT_RELATION
    <set>
      <if test="orderAssistantUserId != null">
        ORDER_ASSISTANT_USER_ID = #{orderAssistantUserId,jdbcType=INTEGER},
      </if>
      <if test="productManagerUserId != null">
        PRODUCT_MANAGER_USER_ID = #{productManagerUserId,jdbcType=INTEGER},
      </if>
      <if test="productAssitantUserId != null">
        PRODUCT_ASSITANT_USER_ID = #{productAssitantUserId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.order.model.OrderAssistantRelationDo">
    update T_ORDER_ASSISTANT_RELATION
    set ORDER_ASSISTANT_USER_ID = #{orderAssistantUserId,jdbcType=INTEGER},
      PRODUCT_MANAGER_USER_ID = #{productManagerUserId,jdbcType=INTEGER},
      PRODUCT_ASSITANT_USER_ID = #{productAssitantUserId,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN}
    where ID = #{id,jdbcType=INTEGER}
  </update>

  <select id="getBingdedInfoByOrderAssId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_ORDER_ASSISTANT_RELATION
    where ORDER_ASSISTANT_USER_ID = #{orderAssitantUserId,jdbcType=INTEGER}
    and IS_DELETE = 0
  </select>
  <select id="getAllBindedInfoByOrderAssIdSelectiveListPage" resultType="com.vedeng.order.model.dto.OrderAssistantRelationDto">
    select OAR.ID id, OAR.ORDER_ASSISTANT_USER_ID orderAssistantUserId, OAR.PRODUCT_MANAGER_USER_ID productManagerUserId, OAR.PRODUCT_ASSITANT_USER_ID productAssitantUserId, OAR.ADD_TIME addTime,
    OAR.CREATOR creator, OAR.IS_DELETE isDelete,U1.USERNAME productManagerName,U2.USERNAME productAssistantName,U3.USERNAME creatorName,U4.USERNAME orderAssistantName
    from T_ORDER_ASSISTANT_RELATION OAR
    LEFT JOIN T_USER U1 ON U1.USER_ID = OAR.PRODUCT_MANAGER_USER_ID
    LEFT JOIN T_USER U2 ON U2.USER_ID = OAR.PRODUCT_ASSITANT_USER_ID
    LEFT JOIN T_USER U3 ON U3.USER_ID = OAR.CREATOR
    LEFT JOIN T_USER U4 ON U4.USER_ID = OAR.ORDER_ASSISTANT_USER_ID
    where 1=1
    <if test="orderAssitantUserId != null">
    and OAR.ORDER_ASSISTANT_USER_ID = #{orderAssitantUserId,jdbcType=INTEGER}
    </if>
    and OAR.IS_DELETE = 0
    ORDER BY OAR.ID DESC
  </select>
  <select id="getBingdedInfoByOrderAssIds" resultType="com.vedeng.order.model.OrderAssistantRelationDo">
    select
    <include refid="Base_Column_List"/>
    from T_ORDER_ASSISTANT_RELATION
    where ORDER_ASSISTANT_USER_ID IN
    <foreach item="orderAssitantUserId" index="index" collection="orderAssitantUserIds" separator="," open="(" close=")">
      #{orderAssitantUserId,jdbcType=INTEGER}
    </foreach>
    and IS_DELETE = 0
  </select>
  <select id="getBingdedInfoByUser" resultType="java.lang.Integer">
    select count(1) from T_ORDER_ASSISTANT_RELATION
    WHERE ORDER_ASSISTANT_USER_ID = #{orderAssistantUserId,jdbcType = INTEGER}
    AND PRODUCT_MANAGER_USER_ID = #{productManagerUserId,jdbcType = INTEGER}
    AND PRODUCT_ASSITANT_USER_ID = #{productAssitantUserId,jdbcType = INTEGER}
    AND IS_DELETE = 0
  </select>
  <select id="getAllBindedInfo" resultType="com.vedeng.order.model.OrderAssistantRelationDo">
    select
    <include refid="Base_Column_List"/>
    from T_ORDER_ASSISTANT_RELATION
    where  IS_DELETE = 0
  </select>
  <update id="unbindOrderAssRelation">
    update T_ORDER_ASSISTANT_RELATION set IS_DELETE = 1
    where ID = #{id,jdbcType = INTEGER}
  </update>
</mapper>