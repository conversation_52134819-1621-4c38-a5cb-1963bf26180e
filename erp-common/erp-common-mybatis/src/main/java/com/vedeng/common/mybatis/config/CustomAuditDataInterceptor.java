package com.vedeng.common.mybatis.config;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.binding.MapperMethod;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.defaults.DefaultSqlSession;

import java.util.Date;
import java.util.List;
import java.util.Properties;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 自定义审计数据sql拦截器
 * @date 2022/2/18 17:41
 */
@Slf4j
@Intercepts({@Signature(method = "update", type = Executor.class, args = {MappedStatement.class, Object.class})})
public class CustomAuditDataInterceptor implements Interceptor {

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        // 从上下文中获取用户名
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        Object[] args = invocation.getArgs();

        Object parameter = args[1];
        MappedStatement statement = (MappedStatement) args[0];
        SqlCommandType sqlCommandType = statement.getSqlCommandType();

        // 判断参数是否是BaseEntity类型
        buildBaseEntity(currentUser, sqlCommandType, parameter);

        // 多参数
        if (parameter instanceof MapperMethod.ParamMap) {
            log.debug("mybatis arg: {}", parameter);
            @SuppressWarnings("unchecked")
            MapperMethod.ParamMap<Object> parasMap = (MapperMethod.ParamMap<Object>) parameter;
            String key = "record";
            if (parasMap.containsKey(key)) {
                Object paraObject = parasMap.get(key);
                if (paraObject instanceof BaseEntity && SqlCommandType.UPDATE == sqlCommandType) {
                    Date updateTime = DateUtil.date();
                    BeanUtil.setProperty(paraObject, "updater", currentUser.getId());
                    BeanUtil.setProperty(paraObject, "updaterName", currentUser.getUsername());
                    BeanUtil.setProperty(paraObject, "modTime", updateTime);
                }
            }
        }

        // 单参数
        if (parameter instanceof DefaultSqlSession.StrictMap) {
            log.debug("mybatis arg: {}", parameter);
            @SuppressWarnings("unchecked")
            DefaultSqlSession.StrictMap<List<Object>> map = (DefaultSqlSession.StrictMap<List<Object>>) parameter;
            String key = "collection";
            if (map.containsKey(key)) {
                List<Object> objs = map.get(key);
                objs.forEach(obj -> buildBaseEntity(currentUser, sqlCommandType, obj));
            }
        }

        return invocation.proceed();
    }

    private void buildBaseEntity(CurrentUser currentUser, SqlCommandType sqlCommandType, Object obj) {
        // 非空检查
        if (currentUser == null || !(obj instanceof BaseEntity)) {
            return;
        }

        BaseEntity entity = (BaseEntity) obj;
        Date currentTime = DateUtil.date();

        if (SqlCommandType.INSERT == sqlCommandType) {
            // 设置创建人和创建时间（如果未设置）
            setPropertyIfNull(entity, "creator", currentUser.getId());
            setPropertyIfNull(entity, "creatorName", currentUser.getUsername());
            setPropertyIfNull(entity, "addTime", currentTime);

            // 设置更新人和更新时间（如果未设置）
            setPropertyIfNull(entity, "updater", currentUser.getId());
            setPropertyIfNull(entity, "updaterName", currentUser.getUsername());
            setPropertyIfNull(entity, "modTime", currentTime);
        } else if (SqlCommandType.UPDATE == sqlCommandType) {
            // 设置更新人和更新时间（如果未设置）
            setPropertyIfNull(entity, "updater", currentUser.getId());
            setPropertyIfNull(entity, "updaterName", currentUser.getUsername());
            setPropertyIfNull(entity, "modTime", currentTime);
        }
    }

    /**
     * 如果属性值为空，则设置属性值
     */
    private void setPropertyIfNull(BaseEntity entity, String propertyName, Object value) {
        if (ObjectUtil.isNull(BeanUtil.getProperty(entity, propertyName))) {
            BeanUtil.setProperty(entity, propertyName, value);
        }
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
    }
}
