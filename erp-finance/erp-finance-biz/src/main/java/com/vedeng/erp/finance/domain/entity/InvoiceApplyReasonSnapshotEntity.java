package com.vedeng.erp.finance.domain.entity;

import java.util.Date;
import lombok.Data;

/**
 * 提前申请开票原因快照表
 */
@Data
public class InvoiceApplyReasonSnapshotEntity {
    /**
    * 主键
    */
    private Long invoiceApplyReasonId;

    /**
    * 开票申请ID
    */
    private Integer invoiceApplyId;

    /**
    * 规则编码
    */
    private String ruleCode;

    /**
    * 规则名称
    */
    private String ruleName;

    /**
    * 规则内容
    */
    private String ruleContent;

    /**
    * 不满足规则提示文本（快照）
    */
    private String promptText;

    /**
    * 提前申请原因
    */
    private String applyReason;

    /**
    * 是否删除 0否 1是
    */
    private Integer isDelete;

    /**
    * 创建时间
    */
    private Date addTime;

    /**
    * 修改时间
    */
    private Date modTime;

    /**
    * 添加人ID
    */
    private Integer creator;

    /**
    * 添加人名称
    */
    private String creatorName;

    /**
    * 更新人ID
    */
    private Integer updater;

    /**
    * 更新人名称
    */
    private String updaterName;

    /**
    * 更新备注
    */
    private String updateRemark;
}