package com.vedeng.mobile.dto;

import lombok.Data;

@Data
public class InvoiceInfoDto {

    /**
     * 发票抬头(发票客户名称)
     */
    private String invoiceTraderName;

    /**
     * 发票类型
     */
    private String invoiceType;

    /**
     * 收票联系人
     */
    private String invoiceTraderContactName;

    /**
     * 收票联系人电话
     */
    private String invoiceTraderContactMobile;

    /**
     * 收票联系人邮箱
     */
    private String invoiceTraderContactEmail;

    /**
     * 收票地区
     */
    private String invoiceTraderArea;

    /**
     * 收票地址
     */
    private String invoiceTraderAddress;

    /**
     * 开票方式:1手动纸质开票、2自动纸质开票、3自动电子发票
     */
    private Integer invoiceMethod;

    /**
     * 是否寄送发票 0否 1是
     */
    private Integer isSendInvoice;

}
