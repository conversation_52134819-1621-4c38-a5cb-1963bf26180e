package com.vedeng.todolist.service.visitor;

import cn.hutool.core.date.DateUtil;
import com.vedeng.authorization.model.User;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.todolist.dto.EnableReceiveToDoDto;
import com.vedeng.todolist.dto.SupplyChainStaffTodoDtoBuild;
import com.wms.service.context.ThreadLocalContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Date;

@Service
public class EnableReceiveStaffVisitor implements TodoVisitor<SupplyChainStaffTodoDtoBuild>{

    @Resource
    BuyorderMapper buyorderMapper;

    @Override
    public void visitor(SupplyChainStaffTodoDtoBuild todoDtoBuild) {
        User user = ThreadLocalContext.get("userInfo");
        EnableReceiveToDoDto dto = new EnableReceiveToDoDto();
        //时间
        dto.setNowDate(DateUtil.today());
        dto.setFirstOfMonth(DateUtil.beginOfMonth(new Date()).toString("yyyy-MM-dd"));
        dto.setRecent30DaysDate(LocalDate.now().minusMonths(1L).toString());
        //数量
        dto.setTotalNum(buyorderMapper.countEnableReceiveNum(user.getUserId(),null,null));
        dto.setRecent30DaysNum(buyorderMapper.countEnableReceiveNum(user.getUserId(),dto.getRecent30DaysDate(),dto.getNowDate()));
        dto.setCurrentMonthNum(buyorderMapper.countEnableReceiveNum(user.getUserId(),dto.getFirstOfMonth(),dto.getNowDate()));
        todoDtoBuild.get().setEnableReceiveToDoDto(dto);
    }
}
