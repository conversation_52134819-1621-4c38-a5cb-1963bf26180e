package com.vedeng.goods.web.api;

import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.core.annotation.ExcludeAuthorization;
import com.vedeng.common.core.base.R;
import com.vedeng.goods.domain.dto.RegistrationFeedbackRecordDto;
import com.vedeng.goods.domain.dto.RegistrationMessageReplyDto;
import com.vedeng.goods.service.RegistrationFeedbackRecordService;
import com.vedeng.goods.service.RegistrationFeedbackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;

/**
 * <AUTHOR>
 * @create 2022−11-01 上午10:02
 * @description
 */
@RequestMapping("/registrationFeedback")
@RestController
@Slf4j
public class RegistrationFeedbackApi{

    @Resource
    RegistrationFeedbackRecordService feedbackRecordService;

    @Resource
    RegistrationFeedbackService feedbackService;


    /**
     * 新增注册证反馈记录
     * @param registrationFeedbackDto
     * @return
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ExcludeAuthorization
    public R<?> add(@RequestBody RegistrationFeedbackRecordDto registrationFeedbackDto, HttpSession session){
        User curr_user = (User)session.getAttribute(ErpConst.CURR_USER);
        Integer userId = curr_user.getUserId();
        //保存到数据库
        feedbackRecordService.addFeedbackRecord(registrationFeedbackDto,userId);
        //发送站内信
        log.info("开始发送注册证问题反馈站内信,快照信息:{}",registrationFeedbackDto);
        Boolean messageResult = feedbackService.sendMessage(registrationFeedbackDto);
        if (!messageResult){
            log.info("发送注册证问题反馈站内信失败!,快照信息:{}",registrationFeedbackDto);
            return R.error("发送站内信失败");
        }
        return R.success();
    }

    /**
     * 新增注册证问题处理答复
     */
    @RequestMapping(value = "/messageReply", method = RequestMethod.POST)
    @ExcludeAuthorization
    public R<?> messageReply(@RequestBody RegistrationMessageReplyDto registrationMessageReplyDto,HttpSession session){
        int updateNum = feedbackService.messageReply(registrationMessageReplyDto, session);
        if (updateNum == 1){
            return R.success();
        }
        if (updateNum == 0){
            return R.error(999,"该问题反馈已被处理");
        }
        else {
            return R.error("处理答复失败");
        }
    }
}
