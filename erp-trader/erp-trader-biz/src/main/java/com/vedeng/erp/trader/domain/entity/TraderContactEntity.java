package com.vedeng.erp.trader.domain.entity;

import java.util.Date;

import lombok.Data;

/**
 * 交易者联系人
 *
 * <AUTHOR>
 */
@Data
public class TraderContactEntity {
    /**
     * 联系人ID
     */
    private Integer traderContactId;

    /**
     * 交易者ID
     */
    private Integer traderId;

    /**
     * 所属类型 1::经销商（包含终端）2:供应商
     */
    private Integer traderType;

    /**
     * 是否有效 0否 1是
     */
    private Integer isEnable;

    /**
     * 性别:0女 1 男 2 保密
     */
    private Integer sex;

    /**
     * 姓名
     */
    private String name;

    /**
     * 部门
     */
    private String department;

    /**
     * 职位
     */
    private String position;

    /**
     * 电话
     */
    private String telephone;

    /**
     * 传真
     */
    private String fax;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 手机2
     */
    private String mobile2;

    /**
     * 邮箱
     */
    private String email;

    /**
     * QQ
     */
    private String qq;

    /**
     * 微信
     */
    private String weixin;

    /**
     * 是否在职1是0否
     */
    private Integer isOnJob;

    /**
     * 默认联系人1是0否
     */
    private Integer isDefault;

    /**
     * 出生日期
     */
    private Date birthday;

    /**
     * 婚否0未知1是2否
     */
    private Integer isMarried;

    /**
     * 有子女0未知1是2否
     */
    private Integer haveChildren;

    /**
     * 学历 SYS_OPTION_DEFINITION_ID
     */
    private Integer education;

    /**
     * 性格 SYS_OPTION_DEFINITION_ID 逗号集合
     */
    private String character;

    /**
     * 备注
     */
    private String comments;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 添加人
     */
    private Integer creator;

    /**
     * 最近一次编辑时间
     */
    private Long modTime;

    /**
     * 最近一次编辑人
     */
    private Integer updater;

    /**
     * 是否置顶0否 1是
     */
    private Integer isTop;
}