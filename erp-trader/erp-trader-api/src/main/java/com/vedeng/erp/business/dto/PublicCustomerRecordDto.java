package com.vedeng.erp.business.dto;

import lombok.Data;

@Data
public class PublicCustomerRecordDto {

    /**
     * 进入公海的客户ID
     */
    private Long publicCustomerRecordId;

    /**
     * 进入公海的客户ID
     */
    private Integer traderId;
    
    private String traderName;
    /**
     * 进入公海的客户ID
     */
    private Integer traderCustomerId;

    /**
     * 进入公海前的归属销售ID
     */
    private Integer originUserId;
    
    private String originUserName;
    private String originUserNumber;

    /**
     * 公海的客户是否被私有（锁定/撤销），0：未，1：锁定，2：撤销
     */
    private Integer isPrivatized;

    /**
     * 公海客户锁定/撤销的时间
     */
    private Long privatizedTime;

    /**
     * 公海客户撤销保护期截止日期
     */
    private Long revocationProtectionDeadline;

    /**
     * 是否被解锁，0：未被解锁，1：新增商机解锁，2：新增通话解锁
     */
    private Integer isUnlock;

    /**
     * 解锁时间
     */
    private Long unLockTime;

    /**
     * 解锁时的商机ID/沟通记录ID
     */
    private Integer unlockRelatedId;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 更新时间
     */
    private Long modTime;

    /**
     * 更新者
     */
    private Integer updater;
}
