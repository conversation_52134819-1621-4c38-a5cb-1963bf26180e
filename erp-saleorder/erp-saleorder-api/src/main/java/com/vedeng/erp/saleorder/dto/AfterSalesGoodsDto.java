package com.vedeng.erp.saleorder.dto;

import cn.hutool.core.util.StrUtil;
import com.vedeng.erp.finance.enums.InvoiceTaxTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;


@Getter
@Setter
public class AfterSalesGoodsDto {

    private Integer afterSalesGoodsId;

    private Integer afterSalesId;

    /**
     * 销售/采购订单详情ID
     */
    private Integer orderDetailId;

    /**
     * 产品类型0普通产品1特殊产品（手续费）
     */
    private Integer goodsType;

    /**
     * 产品ID
     */
    private Integer goodsId;

    /**
     * sku
     */
    private String sku;


    /**
     * 售后数量
     */
    private Integer num;

    private BigDecimal price;

    private BigDecimal afterSaleNum;

    /**
     * 是否直发 0否 1是
     */
    private Integer deliveryDirect;

    /**
     * 已到货数量
     */
    private Integer arrivalNum;

    /**
     * 收货时间
     */
    private Long arrivalTime;

    /**
     * 确认收货人
     */
    private Integer arrivalUserId;

    /**
     * 收货状态0未收货 1部分收货 2全部收货
     */
    private Integer arrivalStatus;

    /**
     * 已发货数量
     */
    private Integer deliveryNum;

    /**
     * 发货状态0未发货 1部分发货 2全部发货
     */
    private Integer deliveryStatus;

    /**
     * 发货时间
     */
    private Long deliveryTime;

    private BigDecimal skuRefundAmount;

    private BigDecimal skuOldRefundAmount;

    /**
     * 是否为活动商品   0否  1是
     */
    private Integer isActionGoods;

    /**
     * 售后单关联业务更新时间
     */
    private Date updateDataTime;

    /**
     * 厂家批次号/SN码
     */
    private String factoryCode;

    /**
     * 生产日期
     */
    private Long goodCreateTime;

    /**
     * 有效期至
     */
    private Long goodVaildTime;

    /**
     * 实际应退数量
     */
    private Integer rknum;

    /**
     * 退票数量(采购仅退票)
     */
    private BigDecimal afterInvoiceNum;

    /**
     * 通用名
     */
    private String skuName;

    /**
     * 规格
     */
    private String spec;

    /**
     * 单位
     */
    private String unitName;

    /**
     * 税收分类编码
     */
    private String taxCategoryNo;

    /**
     * 售后金额
     * 售后税额 = （退货金额/（1+销售订单税率））*销售订单税率
     * 售后金额 = 售后价税合计 - 售后税额
     */
    private BigDecimal afterSalesAmount;

    /**
     * 售后价税合计 数量*价格
     */
    private BigDecimal afterSalesAmountAndTax;

    /**
     * 售后税额
     */
    private BigDecimal tax;

    /**
     * 蓝票明细id
     */
    private Integer blueInvoiceDetailId;

    public void handleInfo() {
        // 补0到19位
        this.taxCategoryNo = StrUtil.padAfter(this.taxCategoryNo, 19, "0");
    }
}
