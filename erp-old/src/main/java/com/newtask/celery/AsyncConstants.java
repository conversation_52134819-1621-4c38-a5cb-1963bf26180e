package com.newtask.celery;


import lombok.extern.slf4j.Slf4j;

/**
 * @Author: Cherny.chen
 * @Create: 2021/7/9 13:45
 */
@Slf4j
public class AsyncConstants {

    public static final boolean ASYNC_WORKER = false;

    public static final int ERROR_SLEEP_TIME = 5 * 1000;

    public static final int MAX_RETRY_SIZE = 3;

    public static final String HANDLE_REG = "_";

    public static final int MAX_ERROR_LENGTH = 255;

    /**
     * java
     */
    public static void mySleep(long ms) {
        try {
            Thread.sleep(ms);
        } catch (InterruptedException e) {
            log.error("【mySleep】处理异常",e);
        }
    }

}
