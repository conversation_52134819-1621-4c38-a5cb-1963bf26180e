package com.vedeng.goods.dao;

import com.vedeng.goods.model.CategoryDepartmentDo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CategoryDepartmentMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(CategoryDepartmentDo record);

    CategoryDepartmentDo selectByPrimaryKey(Integer id);

    List<CategoryDepartmentDo> selectAll();

    int updateByPrimaryKey(CategoryDepartmentDo record);

    /**
     * 根据三级分类id和删除状态获取关联的科室信息
     *
     * @param thirdLevelCategoryId
     * @param deleted
     * @return
     */
    List<CategoryDepartmentDo> listByCategoryId(@Param("thirdLevelCategoryId") Integer thirdLevelCategoryId, @Param("deleted") Integer deleted);

    void batchInsert(List<CategoryDepartmentDo> records);

    void batchUpdate(List<CategoryDepartmentDo> records);

    /**
     * 科室关联sku的数量
     *
     * @param departmentId
     * @param deleted
     * @return
     */
    int countGoodsAssociatedWithDepartment(@Param("departmentId") Integer departmentId, @Param("deleted") Integer deleted);

    /**
     * 根据分类id和科室id查询
     * @param categoryId 分类id
     * @param departmentId  科室id
     * @return CategoryDepartmentDo
     */
    CategoryDepartmentDo findByCategoryIdAndDepartmentId(@Param("categoryId")Integer categoryId,@Param("departmentId")Integer departmentId);



}