package com.vedeng.erp.system.service.impl;

import org.springframework.stereotype.Service;

import org.springframework.beans.factory.annotation.Autowired;

import com.vedeng.erp.system.mapper.BaseCompanyInfoMapper;
import com.vedeng.erp.system.domain.entity.BaseCompanyInfoEntity;
import com.vedeng.erp.system.service.BaseCompanyInfoService;

import java.util.Collections;
import java.util.List;

@Service
public class BaseCompanyInfoServiceImpl implements BaseCompanyInfoService{

    @Autowired
    private BaseCompanyInfoMapper baseCompanyInfoMapper;

    @Override
    public int deleteByPrimaryKey(Integer id) {
        return baseCompanyInfoMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(BaseCompanyInfoEntity record) {
        return baseCompanyInfoMapper.insert(record);
    }

    @Override
    public BaseCompanyInfoEntity selectByCompanyName(String companyName) {
        return baseCompanyInfoMapper.selectByCompanyName(companyName);
    }

    @Override
    public BaseCompanyInfoEntity selectByShortName(String shortName) {
        return baseCompanyInfoMapper.selectByShortName(shortName);
    }



    @Override
    public List<BaseCompanyInfoEntity> selectByCompanyNames(List<String> companyNames) {
        return baseCompanyInfoMapper.selectByCompanyNames(companyNames);
    }

    @Override
    public int insertSelective(BaseCompanyInfoEntity record) {
        return baseCompanyInfoMapper.insertSelective(record);
    }

    @Override
    public BaseCompanyInfoEntity selectByPrimaryKey(Integer id) {
        return baseCompanyInfoMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(BaseCompanyInfoEntity record) {
        return baseCompanyInfoMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(BaseCompanyInfoEntity record) {
        return baseCompanyInfoMapper.updateByPrimaryKey(record);
    }

    @Override
    public List<BaseCompanyInfoEntity> findAll() {
        List<BaseCompanyInfoEntity> baseCompanyInfoEntities = baseCompanyInfoMapper.findAll();
        return baseCompanyInfoEntities;
    }

}
