package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.date.DateUtil;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import com.vedeng.erp.kingdee.batch.dto.BatchPayExpensesDto;
import com.vedeng.erp.kingdee.batch.processor.BatchExpenseAfterSalePayableOrderInvoiceProcessorService;
import com.vedeng.erp.kingdee.batch.processor.BatchExpenseAfterSalePayableOrderProcessorService;
import com.vedeng.erp.kingdee.batch.writer.BatchExpenseAfterSalePayableOrderInvoiceWriterService;
import com.vedeng.erp.kingdee.batch.writer.BatchExpenseAfterSalePayableOrderWriterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购费用单售后 退货退票job 包含退票，仅退票，冲销
 * @date 2022/10/25 16:00
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class BuyOrderExpenseAfterSaleBatchJob extends BaseJob {

    @Autowired
    private BatchExpenseAfterSalePayableOrderProcessorService batchExpenseAfterSalePayableOrderProcessorService;
    @Autowired
    private BatchExpenseAfterSalePayableOrderWriterService batchExpenseAfterSalePayableOrderWriterService;

    @Autowired
    private BatchExpenseAfterSalePayableOrderInvoiceProcessorService batchExpenseAfterSalePayableOrderInvoiceProcessorService;

    @Autowired
    private BatchExpenseAfterSalePayableOrderInvoiceWriterService batchExpenseAfterSalePayableOrderInvoiceWriterService;

    public Job buyOrderExpenseAfterSaleFlowJob() {
        return jobBuilderFactory.get("buyOrderExpenseAfterSaleFlowJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(afterExpensePayableOrder())
                .next(afterExpenseInvoice())
                .build();
    }


    /**
     * 费用负向应付单
     */
    private Step afterExpensePayableOrder() {
        return stepBuilderFactory.get("费用负向应付单")
                // <输入对象, 输出对象> chunk通俗的讲类似于SQL的commit; 这里表示处理(processor)100条后写入(writer)一次
                .<BatchInvoiceDto, BatchPayExpensesDto>chunk(1)
                .faultTolerant()
                .retryLimit(1)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchBatchInvoiceDtoItemReader(null,null))
                .processor(batchExpenseAfterSalePayableOrderProcessorService)
                .writer(batchExpenseAfterSalePayableOrderWriterService)
                .listener(baseProcessListener)
                .build();
    }

    /**
     * 采购费用红票
     * @return
     */
    private Step afterExpenseInvoice() {
        return stepBuilderFactory.get("采购费用红票")
                // <输入对象, 输出对象> chunk通俗的讲类似于SQL的commit; 这里表示处理(processor)100条后写入(writer)一次
                .<BatchInvoiceDto, BatchPayExpensesDto>chunk(1)
                .faultTolerant()
                .retryLimit(1)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchBatchInvoiceDtoItemReader(null,null))
                .processor(batchExpenseAfterSalePayableOrderInvoiceProcessorService)
                .writer(batchExpenseAfterSalePayableOrderInvoiceWriterService)
                .listener(baseProcessListener)
                .build();
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> batchBatchInvoiceDtoItemReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                   @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                // 红字有效
                .colorType(1)
                .isEnable(1)
                // 审核通过
                .validStatus(1)
                .companyId(1)
                // 采购开票
                .type(4126)
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() :
                        DateUtil.beginOfDay(DateUtil.parseDateTime(beginTime)).getTime())
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() :
                        DateUtil.endOfDay(DateUtil.parseDateTime(endTime)).getTime())
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), batchInvoiceDto);
    }
}

