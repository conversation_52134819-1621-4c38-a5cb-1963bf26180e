<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>精准营销运营活动明细</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="marketplandetail" datasource="erp-datasourcetarget" success_url="reload">
<div id="APPEND_HEAD" class="append"></div>
<div class="layui-container">
    <form id="inputForm" method="post" class="layui-form">
        <div class="layui-card" group_name="EZ_DEFAULT_GROUP">
            <div class="layui-card-header">
                EZ_DEFAULT_GROUP
            </div>
            <div class="layui-card-body">
                <div class="layui-form-item "><label class="layui-form-label">任务名称</label>
                    <div class="layui-input-block form-group">
                        <span class="form-item" name="PLAN_NAME" type="span" value="" item_name="PLAN_NAME" placeholder="" style="" alias="" jdbctype="" data="" datatype="" lay_verify="" item_desc="" group_data="" valid_rule="" valid_msg="" item_max_upload=""></span>
                    </div>
                </div>
                <div class="layui-form-item "><label class="layui-form-label">营销类型</label>
                    <div class="layui-input-block form-group">
                        <span class="form-item" name="CONTENT_TYPE" type="span" value="" item_name="CONTENT_TYPE" placeholder="" style="" alias="" jdbctype="" data="" datatype="" lay_verify="" item_desc="" group_data="" valid_rule="" valid_msg="" item_max_upload=""></span>
                    </div>
                </div>
                <div class="layui-form-item "><label class="layui-form-label">生效范围</label>
                    <div class="layui-input-block form-group">
                        <span class="form-item" name="EFFORT_TIME" type="span" value="" item_name="EFFORT_TIME" placeholder="" style="" alias="" jdbctype="" data="" datatype="" lay_verify="" item_desc="" group_data="" valid_rule="" valid_msg="" item_max_upload=""></span>
                    </div>
                </div>
                <div class="layui-form-item "><label class="layui-form-label">推广渠道</label>
                    <div class="layui-input-block form-group">
                        <span class="form-item" name="PROMOTION_CHANNELS" type="span" value="" item_name="PROMOTION_CHANNELS" placeholder="" style="" alias="" jdbctype="" data="" datatype="" lay_verify="" item_desc="" group_data="" valid_rule="" valid_msg="" item_max_upload=""></span>
                    </div>
                </div>
                <div class=" layui-inline "><label class="layui-form-label">plan_id</label>
                    <div class="layui-input-inline"><object class=" layui-input list-search-item " type="hidden" name="PLAN_ID" placeholder style alias="tmpt" jdbctype data datatype oper validate_rules validate_messages> </object>
                    </div>
                </div>

            </div>
        </div>
    </form>
    <form id="configForm" class="layui-form">
        <div class="layui-card" group_name="表单数据配置">
            <div class="layui-card-header">
                表单数据配置
            </div>
            <div class="layui-card-body">
            </div>
        </div>
        <div class="layui-form-item ">
            <label class="layui-form-label">初始表达式</label>
            <div class="layui-input-block form-group">
      <pre class="form-item layui-code" id="init" name="INIT_EXPRESS" type="">StringBuilder sql=new StringBuilder();

sql.append("SELECT PLAN_NAME ,
	CASE CONTENT_TYPE
	WHEN 1 THEN '商品推广'
	WHEN 2 THEN '客户运营'
	WHEN 3 THEN '品牌推广'
	WHEN 4 THEN '用户运营'
	ELSE CONTENT_TYPE END AS CONTENT_TYPE,
	CONCAT(DATE_FORMAT(PLAN_CREATE_TIME, '%Y/%m/%d'),' ',DATE_FORMAT(PLAN_END_TIME, '%Y/%m/%d'))  AS EFFORT_TIME,
	CASE PROMOTION_CHANNELS
		WHEN 1 THEN '1对1沟通'
		WHEN 2 THEN '图片'
		ELSE PROMOTION_CHANNELS
	END AS PROMOTION_CHANNELS
FROM T_MARKETING_PLAN tmp
WHERE ID=${PLAN_ID}  ");

return select(sql).get(0);</pre>
            </div>
        </div>
        <div class="layui-form-item "><label class="layui-form-label">提交表达式</label>
            <div class="layui-input-block form-group">
      <pre class="form-item layui-code" id="submit" name="SUBMIT_EXPRESS" type="">companyId=$$("COMPANY_ID");
sessionUserId=$$("EZ_SESSION_USER_ID_KEY");
sessionUserName=$$("EZ_SESSION_USER_NAME_KEY");
if(!isNotBlank("ID")){

 	return 0;
}else{
	StringBuilder updateSql=new StringBuilder();
	updateSql.append("update T_SALEORDER_GOODS set ");
	updateSql.append("
 		REFERENCE_COST_PRICE = #{REFERENCE_COST_PRICE}
");

	updateSql.append(" where SALEORDER_GOODS_ID=#{ID} ");

	update(updateSql);
	return ID;
}</pre>
            </div>
        </div>
        <div class="layui-form-item "><label class="layui-form-label">删除表达式</label>
            <div class="layui-input-block form-group">
                <pre class="form-item layui-code" id="delete" name="DELETE_EXPRESS" type=""></pre>
            </div>
        </div>
        <div class="layui-form-item ">
            <label class="layui-form-label">分组表达式</label>
            <div class="layui-input-block form-group">
                <pre class="form-item layui-code" name="GROUP_DATA" type=""></pre>
            </div>
        </div>
    </form>
</div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
<div id="APPEND_FOOT" class="append">
</div>
</body>
</html>