package com.vedeng.erp.saleorder.service;

import com.vedeng.erp.saleorder.domain.SaleorderGoodsMaoliBuyprice;
    /**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/11/8
 */
public interface SaleorderGoodsMaoliBuypriceService{

    int deleteByPrimaryKey(Integer saleorderGoodsMaoliBuypriceId);

    int insert(SaleorderGoodsMaoliBuyprice record);

    int insertSelective(SaleorderGoodsMaoliBuyprice record);

    SaleorderGoodsMaoliBuyprice selectByPrimaryKey(Integer saleorderGoodsMaoliBuypriceId);

    int updateByPrimaryKeySelective(SaleorderGoodsMaoliBuyprice record);

    int updateByPrimaryKey(SaleorderGoodsMaoliBuyprice record);

}
