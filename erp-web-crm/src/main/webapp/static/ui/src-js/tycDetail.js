Vue.component('ui-tyc-detail', {
    template: `
        <div class="vd-ui-tianyancha">
            <transition>
                <ui-dialog
                    :visible.sync="rotate"
                    width="960px"
                    title="天眼查查询"
                >
                    <div class="tyc-info-wrap">
                        <div class="tyc-info-cnt J-tyc-info-cnt">
                            <template v-if="companyInfo && Object.keys(companyInfo).length">
                                <div class="tyc-info-title">
                                    <div class="tyc-info-title-txt">{{ companyInfo.name }}</div>
                                    <div class="tyc-info-tags" v-if="companyInfo.tags && companyInfo.tags.length">
                                        <div class="tag-item" v-for="tag in companyInfo.tags" :key="tag">{{ tag }}</div>
                                    </div>
                                </div>
                                <div class="tyc-info-list">
                                    <div class="tyc-info-item">
                                        <div class="tyc-info-label">地区</div>
                                        <div class="tyc-info-txt">
                                            <template v-if="companyInfo.base">{{ companyInfo.base }}</template>
                                            <template v-if="companyInfo.base && companyInfo.city"> / </template>
                                            <template v-if="companyInfo.city">{{ companyInfo.city }}</template>
                                            <template v-if="companyInfo.city && companyInfo.district"> / </template>
                                            <template v-if="companyInfo.district">{{ companyInfo.district }}</template>
                                        </div>
                                    </div>
                                    <div class="tyc-info-item">
                                        <div class="tyc-info-label">注册地址</div>
                                        <div class="tyc-info-txt">{{ companyInfo.regLocation || '-' }}</div>
                                    </div>
                                    <div class="tyc-info-item">
                                        <div class="tyc-info-label">曾用名</div>
                                        <div class="tyc-info-txt">{{ companyInfo.historyNames || '-' }}</div>
                                    </div>
                                    <div class="tyc-info-item">
                                        <div class="tyc-info-label">法人</div>
                                        <div class="tyc-info-txt">{{companyInfo.legalPersonName || '-'}}</div>
                                    </div>
                                    <div class="tyc-info-item">
                                        <div class="tyc-info-label">注册资本</div>
                                        <div class="tyc-info-txt">{{companyInfo.regCapital || '-'}}</div>
                                    </div>
                                    <div class="tyc-info-item">
                                        <div class="tyc-info-label">纳税人识别号</div>
                                        <div class="tyc-info-txt">{{companyInfo.taxNumber || '-'}}</div>
                                    </div>
                                    <div class="tyc-info-item">
                                        <div class="tyc-info-label">企业联系方式</div>
                                        <div class="tyc-info-txt">{{companyInfo.phoneNumber || '-'}}</div>
                                    </div>
                                    <div class="tyc-info-item">
                                        <div class="tyc-info-label">行业</div>
                                        <div class="tyc-info-txt">{{companyInfo.industry || '-'}}</div>
                                    </div>
                                    <div class="tyc-info-item">
                                        <div class="tyc-info-label">国民经济行业分类</div>
                                        <div class="tyc-info-txt">{{ category_Big_Middle_Small || '-' }}</div>
                                    </div>
                                    <div class="tyc-info-item">
                                        <div class="tyc-info-label">成立日期</div>
                                        <div class="tyc-info-txt">{{companyInfo.estiblishTime | filterDetailDateTime}}</div>
                                    </div>
                                    <div class="tyc-info-item">
                                        <div class="tyc-info-label">网址</div>
                                        <div class="tyc-info-txt">{{companyInfo.websiteList || '-'}}</div>
                                    </div>
                                    <div class="tyc-info-item">
                                        <div class="tyc-info-label">经营范围</div>
                                        <div class="tyc-info-txt">{{companyInfo.businessScope || '-'}}</div>
                                    </div>
                                </div>
                            </template>
                            <div class="tyc-info-empty" v-else>
                                <div class="empty-img"></div>
                                <div class="empty-txt">抱歉，未能匹配到天眼查公司数据</div>
                            </div>
                        </div>
                        <div class="tyc-info-loading J-tyc-info-loading" style="display: none;">
                            <i class="bd-icon icon-loading"></i>
                        </div>
                    </div>
                </ui-dialog>
            </transition>
        </div>
    `,

    props: {
        name: {
            type: String,
            default: ''
        },
        width: {
            type: String,
            default: '300px'
        },
    },
    data() {
        return {
            traderName: '', // 名称
            companyInfo: null, // 公司信息-确定选择后
            rotate: false,
        }
    },
    computed: {
        // 国民经济行业分类
        category_Big_Middle_Small () {
            let arr = [];
            this.companyInfo.category && arr.push(this.companyInfo.category); 
            this.companyInfo.categoryBig && arr.push(this.companyInfo.categoryBig); 
            this.companyInfo.categoryMiddle && arr.push(this.companyInfo.categoryMiddle); 
            this.companyInfo.categorySmall && arr.push(this.companyInfo.categorySmall);
            return arr.join(' / ')
        }
    },
    filters: {
        filterDetailDateTime (val) {
            if (/\d{10,}/.test(val)) {
                return moment(val).format('YYYY-MM-DD');
            } else {
                return '-'
            }
        },
    },
    mounted() {
    },
    methods: {
        open (name) {
            this.traderName = name;
            this.showDetail();
            this.rotate = true;
        },
        showDetail () {
            GLOBAL.showGlobalLoading();
            this.$axios.post(`/crm/trader/profile/queryTycDetail?traderName=${this.traderName}`).then(({data}) => {
                GLOBAL.hideGlobalLoading();
                if (data.success) {
                    this.companyInfo = data.data || {};
                }
            }).catch(errr=> {
                GLOBAL.hideGlobalLoading();
            })
        }
    }
})