package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDetailDto;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import com.vedeng.erp.kingdee.batch.dto.BatchRInvoiceDetailJOperateLogDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInItemDto;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDetailDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchRInvoiceDetailJOperateLogDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInItemDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveCommonDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveCommonDetailLinkDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveCommonDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeReceiveQueryResultDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeSaleOutInitStockQueryResultDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeSaleOutStockQueryResultDto;
import com.vedeng.erp.kingdee.service.KingDeeReceiveCommonService;
import com.vedeng.erp.kingdee.service.KingDeeSaleInitOutStockService;
import com.vedeng.erp.kingdee.service.KingDeeSaleOutStockService;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 销售单 标准应收单推送
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/11 9:47
 */
@Service
@Slf4j
public class BatchSaleOrderReceiveProcessor implements ItemProcessor<BatchInvoiceDto, KingDeeReceiveCommonDto> {
    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;

    @Autowired
    private BatchInvoiceDetailDtoMapper batchInvoiceDetailDtoMapper;

    @Autowired
    private KingDeeSaleOutStockService kingDeeSaleOutStockService;

    @Autowired
    private KingDeeSaleInitOutStockService kingDeeSaleInitOutStockService;

    @Autowired
    private KingDeeReceiveCommonService kingDeeReceiveCommonService;

    @Autowired
    private BatchRInvoiceDetailJOperateLogDtoMapper detailJOperateLogDtoMapper;

    @Autowired
    private BatchInvoiceDtoMapper batchInvoiceDtoMapper;

    @Override
    public KingDeeReceiveCommonDto process(BatchInvoiceDto batchInvoiceDto) throws Exception {
        if (StrUtil.isBlank(batchInvoiceDto.getInvoiceNo()) || StrUtil.isBlank(batchInvoiceDto.getInvoiceCode())) {
            log.warn("销售单实物商品蓝票无发票号或者发票代码{}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }

        // 排除蓝字作废
        List<BatchInvoiceDto> blueDisEnableByInvoiceCodeAndInvoiceNo = batchInvoiceDtoMapper.findBlueDisEnableByInvoiceCodeAndInvoiceNo(batchInvoiceDto.getInvoiceCode(), batchInvoiceDto.getInvoiceNo(), 505);
        if (CollUtil.isNotEmpty(blueDisEnableByInvoiceCodeAndInvoiceNo)) {
            log.info("当前蓝票存在蓝字作废{}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }

        log.info("开始构造销售单标准应收单信息：{}", JSON.toJSONString(batchInvoiceDto));
        List<BatchInvoiceDetailDto> invoiceDetailList;
        List<BatchInvoiceDetailDto> physicalGoodsDetailList;
        if (CollectionUtils.isEmpty(batchInvoiceDto.getBatchInvoiceDetailDtoList())) {
            // 发票明细为空，则表示是非历史数据，直接取数据库查询发票明细
            log.info("开始处理销售实物标准应收单蓝票明细：{}", JSON.toJSONString(batchInvoiceDto));
            invoiceDetailList = batchInvoiceDetailDtoMapper.getSaleOrderInvoiceDetailList(batchInvoiceDto.getInvoiceId());
        } else {
            // 否则，则证明是从Excel中读取的发票明细
            log.info("开始处理21-22历史销售实物标准应收单蓝票明细：{}", JSON.toJSONString(batchInvoiceDto));
            invoiceDetailList = batchInvoiceDto.getBatchInvoiceDetailDtoList();
        }
        physicalGoodsDetailList = invoiceDetailList.stream().filter(invoiceDetailDto -> invoiceDetailDto.getIsVirtureSku() == null || invoiceDetailDto.getIsVirtureSku() == 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(physicalGoodsDetailList)) {
            // 当前发票关联的销售单商品全是费用商品，不符合标准应收单推送条件，直接返回
            log.info("当前发票关联的销售单商品全是费用商品，不符合标准应收单推送条件:{}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }
        // 判断标准应收数据是否存在
        List<KingDeeReceiveQueryResultDto> kingDeeSaleReceivable = kingDeeReceiveCommonService.getKingDeeReceiveCommon(batchInvoiceDto.getInvoiceId().toString());
        if (CollUtil.isNotEmpty(kingDeeSaleReceivable)) {
            log.info("销售实物标准应收单,金蝶数据已存在:{}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }

        List<BatchRInvoiceDetailJOperateLogDto> invoiceAndWarehouse = detailJOperateLogDtoMapper.findByInvoiceIdAndOperateType(batchInvoiceDto.getInvoiceId(), 2);
        if (CollUtil.isEmpty(invoiceAndWarehouse)) {
            log.error("当前销售蓝未关联到出库单信息:{}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }
        // 第一层
        KingDeeReceiveCommonDto kingDeeReceiveCommonDto = new KingDeeReceiveCommonDto();
        kingDeeReceiveCommonDto.setFId("0");
        // VDERP-15040
        Date outInTime = detailJOperateLogDtoMapper.findWarehouseOutByInvoiceId(batchInvoiceDto.getInvoiceId(), 2);
        kingDeeReceiveCommonDto.setFDate(DateUtil.formatDate(new Date(batchInvoiceDto.getAddTime()).compareTo(outInTime) < 0 ? outInTime : new Date(batchInvoiceDto.getAddTime())));
        kingDeeReceiveCommonDto.setFQzokBddjtId(batchInvoiceDto.getInvoiceId().toString());

        kingDeeReceiveCommonDto.setFCustomerId(batchInvoiceDto.getTraderCustomerId().toString());
        BigDecimal taxRatio = batchInvoiceDto.getRatio().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
        // 非赠品数据处理 第二、三层
        List<KingDeeReceiveCommonDetailDto> detailDtoList = new ArrayList<>();
        // 查询已经分摊好的 蓝票和出库单关系

        Map<Integer, List<BatchRInvoiceDetailJOperateLogDto>> listMap = invoiceAndWarehouse.stream().collect(Collectors.groupingBy(BatchRInvoiceDetailJOperateLogDto::getInvoiceDetailId));

        for (BatchInvoiceDetailDto detail : physicalGoodsDetailList) {
            this.handleNonGiftInvoiceDetail(detail, detailDtoList, batchInvoiceDto, taxRatio, listMap);
        }

        if (detailDtoList.size() == 0) {
            log.error("销售单标准应收单第二层信息为空，构造失败：{}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }
        kingDeeReceiveCommonDto.setFEntityDetail(detailDtoList);
        log.info("销售单标准应收单信息构造成功：{}", JSON.toJSONString(kingDeeReceiveCommonDto));
        return kingDeeReceiveCommonDto;
    }

    /**
     * 处理非赠品的应收单第二层信息
     *
     * @param detail          发票明细
     * @param detailDtoList   应收单明细
     * @param batchInvoiceDto 发票信息
     * @param taxRatio        税率
     */
    private void handleNonGiftInvoiceDetail(BatchInvoiceDetailDto detail, List<KingDeeReceiveCommonDetailDto> detailDtoList, BatchInvoiceDto batchInvoiceDto, BigDecimal taxRatio, Map<Integer, List<BatchRInvoiceDetailJOperateLogDto>> listMap) {
        // 查询当前发票明细对应的erp出库单信息
        // 此处默认 ：某个商品的出库总数必然是 大于等于 录票数量的。（必须先出库了，才能录票）
        List<BatchRInvoiceDetailJOperateLogDto> accessLogList = listMap.get(detail.getInvoiceDetailId());
        if (CollectionUtils.isEmpty(accessLogList)) {
            log.info("当前发票明细行无法查询到出库记录，整张票均不推送标准应收单:{}", JSON.toJSONString(detail));
            throw new KingDeeException("当前发票明细行无法查询到出库记录，整张票均不推送标准应收单");
        }

        // 校验发票明细中的数量和 BatchRInvoiceDetailJOperateLogDto中的数量之和是否相等
        BigDecimal outRelationNum = accessLogList.stream().map(BatchRInvoiceDetailJOperateLogDto::getNum).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (detail.getNum().abs().compareTo(outRelationNum) != 0) {
            log.info("当前发票明细行中的数量大于已绑定出库关系的数量，整张票均不推送标准应收单:{}", JSON.toJSONString(detail));
            throw new KingDeeException("当前发票明细行中的数量不等于已绑定出库关系的数量，整张票均不推送标准应收单");
        }

        accessLogList.forEach(outItem -> {
            KingDeeReceiveCommonDetailDto tempDetailDto = new KingDeeReceiveCommonDetailDto();
            tempDetailDto.setFMaterialId(detail.getSku());
            // 计价数量
            BigDecimal priceQuantity = outItem.getNum();
            tempDetailDto.setFPriceQty(priceQuantity);
            // 含税单价=含税总价/数量
            BigDecimal taxIncludePrice = detail.getTotalAmount().divide(detail.getNum(), 6, RoundingMode.HALF_UP);
            tempDetailDto.setFTaxPrice(taxIncludePrice);
            tempDetailDto.setFEntryTaxRate(taxRatio);
            // 价税合计=含税单价*计价数量
            BigDecimal priceAndTax = taxIncludePrice.multiply(priceQuantity);
            tempDetailDto.setFAllAmountForD(priceAndTax);
            // 税额=[价税合计/（1+税率）]*税率 (此处需要先乘再除才能保留两位小数)
            BigDecimal ratioAmount = priceAndTax.multiply(batchInvoiceDto.getRatio()).divide(BigDecimal.ONE.add(batchInvoiceDto.getRatio()),
                    BigDecimal.ROUND_CEILING, RoundingMode.HALF_UP);
            tempDetailDto.setFTaxAmountForD(ratioAmount);
            // 不含税金额=价税合计-税额（四舍五入保留2位小数）
            BigDecimal noTaxAmount = priceAndTax.subtract(ratioAmount);
            tempDetailDto.setFNoTaxAmountForD(noTaxAmount);
            tempDetailDto.setFIsFree(false);
            tempDetailDto.setFQzokBddjhId(detail.getInvoiceDetailId().toString() + "-" + outItem.getOperateLogId());
            tempDetailDto.setFSourceType("SAL_OUTSTOCK");

            // 第三层
            // 票明细总额
            BigDecimal totalAmount = Objects.isNull(detail.getTotalAmount()) ? BigDecimal.ZERO : detail.getTotalAmount().abs();
            // 出库含税单价
            BigDecimal priceAverage = totalAmount.divide(detail.getNum(), 6, RoundingMode.HALF_UP);
            KingDeeReceiveCommonDetailLinkDto tempLink = this.handleNonGiftThirdLevel(outItem, priceQuantity);
            String outAmount = priceAverage.multiply(outItem.getNum()).toString();
            tempLink.setFEntityDetailLinkFallamountforDold(outAmount);
            tempLink.setFEntityDetailLinkFallamountforD(outAmount);
            if(tempLink.getFEntityDetailLinkFstableName().equals("T_SAL_INITOUTSTOCKENTRY")){
                tempDetailDto.setFSourceType("SAL_INITOUTSTOCK");
            }
            tempDetailDto.setFEntityDetail_Link(Collections.singletonList(tempLink));
            detailDtoList.add(tempDetailDto);
        });
    }

    /**
     * 非赠品第三层信息
     *
     * @param itemDto       出库明细信息
     * @param priceQuantity 计价数量，取的出库明细里的数量
     * @return KingDeeReceiveCommonDetailLinkDto
     */
    private KingDeeReceiveCommonDetailLinkDto handleNonGiftThirdLevel(BatchRInvoiceDetailJOperateLogDto itemDto, BigDecimal priceQuantity) {
        KingDeeReceiveCommonDetailLinkDto tempLink = new KingDeeReceiveCommonDetailLinkDto();
        tempLink.setFEntityDetailLinkFruleId("AR_OutStockToReceivableMap");
        tempLink.setFEntityDetailLinkFstableName("T_SAL_OUTSTOCKENTRY");

        // 根据erp的出库单号查询金蝶系统中的出库单号
        List<KingDeeSaleOutStockQueryResultDto> saleOutStockQueryResultDtoList = kingDeeSaleOutStockService.getKingDeeSaleOutStock(itemDto.getOutInNo());
        if (CollectionUtils.isEmpty(saleOutStockQueryResultDtoList)) {
            List<KingDeeSaleOutInitStockQueryResultDto> kingDeeSaleInitOutStock = kingDeeSaleInitOutStockService.getKingDeeSaleInitOutStock(itemDto.getOutInNo());
            saleOutStockQueryResultDtoList = BeanUtil.copyToList(kingDeeSaleInitOutStock, KingDeeSaleOutStockQueryResultDto.class);
            tempLink.setFEntityDetailLinkFruleId("INITOUTSTOCK_TO_ReceivableMap");
            tempLink.setFEntityDetailLinkFstableName("T_SAL_INITOUTSTOCKENTRY");
        }
        if(CollectionUtils.isEmpty(saleOutStockQueryResultDtoList)){
            log.error("当前发票存在商品无法查询到金蝶出库单，不能推送应收单：{}", JSON.toJSONString(itemDto));
            throw new KingDeeException(StrUtil.format("无法查询销售出库单:{}", itemDto.getOutInNo()));
        }
        // 出库主单
        KingDeeSaleOutStockQueryResultDto saleOutStockQueryResultDto = CollUtil.getFirst(saleOutStockQueryResultDtoList);
        // 出库明细
        KingDeeSaleOutStockQueryResultDto kingDeeSaleInStockQueryResultDtoDetail = saleOutStockQueryResultDtoList.stream()
                .filter(q -> q.getF_QZOK_BDDJHID().equals(itemDto.getOperateLogId().toString())).findFirst().orElse(null);
        if (Objects.isNull(kingDeeSaleInStockQueryResultDtoDetail)) {
            log.error("无法查询销售出库单明细单:{}", JSON.toJSONString(itemDto));
            throw new KingDeeException(StrUtil.format("无法查询销售出库单明细单:{}", itemDto.getOperateLogId()));
        }

        tempLink.setFLinkId("0");
        tempLink.setFEntityDetailLinkFflowlineId("0");
        tempLink.setFEntityDetailLinkFstableId("0");
        tempLink.setFEntityDetailLinkFsbillId(saleOutStockQueryResultDto.getFID());
        tempLink.setFEntityDetailLinkFsId(kingDeeSaleInStockQueryResultDtoDetail.getFEntity_FENTRYID());
        tempLink.setFEntityDetailLinkFbasicunitqtyold(priceQuantity.toString());
        tempLink.setFEntityDetailLinkFbasicunitqty(priceQuantity.toString());
        tempLink.setFEntityDetailLinkFsalbaseqtyold(priceQuantity.toString());
        tempLink.setFEntityDetailLinkFsalbaseqty(priceQuantity.toString());
        tempLink.setFEntityDetailLinkFstockbaseqtyold(priceQuantity.toString());
        tempLink.setFEntityDetailLinkFstockbaseqty(priceQuantity.toString());
        return tempLink;
    }


}