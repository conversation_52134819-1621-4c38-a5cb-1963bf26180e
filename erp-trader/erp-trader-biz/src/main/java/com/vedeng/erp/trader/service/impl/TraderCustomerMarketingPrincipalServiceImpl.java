package com.vedeng.erp.trader.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.Constants;
import com.vedeng.erp.trader.common.enums.TraderCustomerMarketingPrincipalEnum;
import com.vedeng.erp.trader.domain.entity.TraderCustomerMarketingPrincipalEntity;
import com.vedeng.erp.trader.dto.TraderCustomerMarketingPrincipalDto;
import com.vedeng.erp.trader.dto.TraderDealerFrontDto;
import com.vedeng.erp.trader.mapper.TraderCustomerMarketingPrincipalMapper;
import com.vedeng.erp.trader.mapstruct.TraderCustomerMarketingPrincipalConvertor;
import com.vedeng.erp.trader.service.TraderCustomerMarketingPrincipalApiService;
import com.vedeng.erp.trader.service.TraderCustomerTagChangeRecordService;
import com.vedeng.goods.dto.BaseCategoryDto;
import com.vedeng.goods.dto.BrandFrontDto;
import com.vedeng.goods.service.BaseCategoryApiService;
import com.vedeng.goods.service.BrandApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/8/16 15:27
 * @version 1.0
 */
@Service
@Slf4j
public class TraderCustomerMarketingPrincipalServiceImpl implements TraderCustomerMarketingPrincipalApiService {

    @Autowired
    private TraderCustomerMarketingPrincipalConvertor traderCustomerMarketingPrincipalConvertor;

    @Autowired
    private TraderCustomerMarketingPrincipalMapper traderCustomerMarketingPrincipalMapper;

    @Autowired
    private BaseCategoryApiService baseCategoryApiService;

    @Autowired
    private BrandApiService brandApiService;

    @Autowired
    private TraderCustomerTagChangeRecordService traderCustomerTagChangeRecordService;

    @Override
    public TraderCustomerMarketingPrincipalDto processFrontData(TraderDealerFrontDto traderDealerFrontDto) {

        TraderDealerFrontDto.Principal principal = traderDealerFrontDto.getPrincipal();

        if (Objects.isNull(principal)) {
            return null;
        }

        TraderCustomerMarketingPrincipalDto data = new TraderCustomerMarketingPrincipalDto();

        data.setTraderId(traderDealerFrontDto.getTraderId());
        data.setTraderCustomerId(traderDealerFrontDto.getTraderCustomerId());
        data.setSkuType(CollUtil.isNotEmpty(principal.getSkuTypeChecked()) ? String.join(StrUtil.COMMA, principal.getSkuTypeChecked()) : "");
        data.setSkuScope(principal.getSkuScope());

        List<List<Integer>> skuCategoryChecked = principal.getSkuCategoryChecked();
        if (CollUtil.isNotEmpty(skuCategoryChecked)) {

            List<String> collect = skuCategoryChecked.stream().filter(x -> x.size() == 3).map(x -> x.get(x.size() - 1).toString()).collect(Collectors.toList());
            data.setSkuCategory(CollUtil.isNotEmpty(collect)? String.join(StrUtil.COMMA, collect) : "");
        }

        data.setSalesType(principal.getSalesType());
        //VDERP-17064 CPM标签核心资源产品型渠道商增加有无代理的选项
        boolean customerHasAgency = Constants.ONE.toString().equals(principal.getCustomerHasAgency());
        data.setAgencyBrand((CollUtil.isNotEmpty(principal.getAgencyBrandChecked()) && customerHasAgency) ? principal.getAgencyBrandChecked().stream().map(Object::toString).collect(Collectors.joining(StrUtil.COMMA)) : "");
        data.setGovernmentRelation(principal.isChannelCustomer()? String.join(StrUtil.COMMA, principal.getGovernmentRelationChecked()) : "");
        data.setOtherAgencyBrand(customerHasAgency?principal.getOtherAgencyBrand():"");
        data.setEffectiveness(principal.getEffectiveness());
        data.setOtherGovernmentRelation(principal.getOtherGovernmentRelation());
        data.setOtherAgencySku(customerHasAgency?principal.getOtherAgencySku():"");
        data.setIsProductCustomer(principal.isProductCustomer()?1:0);
        data.setIsChannelCustomer(principal.isChannelCustomer()?1:0);
        if (CollUtil.isNotEmpty(principal.getAgencySkuChecked()) && customerHasAgency) {
            List<String> collect = principal.getAgencySkuChecked().stream().filter(x -> x.size() == 3).map(x -> x.get(x.size() - 1).toString()).collect(Collectors.toList());
            data.setAgencySku(CollUtil.isNotEmpty(collect)? String.join(StrUtil.COMMA, collect) : "");
        }
        return data;
    }

    @Override
    public TraderDealerFrontDto.Principal stealthFrontData(Integer traderCustomerId) {

        Assert.notNull(traderCustomerId, "客户id不可为空");

        List<TraderCustomerMarketingPrincipalEntity> traderCustomerMarketingPrincipalEntities = traderCustomerMarketingPrincipalMapper.selectByTraderCustomerId(traderCustomerId);

        if (CollUtil.isEmpty(traderCustomerMarketingPrincipalEntities)) {
            return null;
        }
        // 只有一个
        TraderCustomerMarketingPrincipalEntity data = traderCustomerMarketingPrincipalEntities.get(0);

        TraderDealerFrontDto.Principal principal = new TraderDealerFrontDto.Principal();
        principal.setEffectiveness(data.getEffectiveness());
        principal.setGovernmentRelationChecked(StrUtil.isNotEmpty(data.getGovernmentRelation()) ? StrUtil.split(data.getGovernmentRelation(), StrUtil.COMMA) : Collections.emptyList());
        principal.setOtherAgencyBrand(data.getOtherAgencyBrand());
        principal.setOtherAgencySku(data.getOtherAgencySku());
        principal.setSkuScope(data.getSkuScope());
        principal.setOtherGovernmentRelation(data.getOtherGovernmentRelation());
        principal.setSkuTypeChecked(StrUtil.isNotEmpty(data.getSkuType()) ? StrUtil.split(data.getSkuType(), StrUtil.COMMA) : Collections.emptyList());
        principal.setAgencyBrandChecked(StrUtil.isNotEmpty(data.getAgencyBrand()) ? StrUtil.split(data.getAgencyBrand(), StrUtil.COMMA)
                .stream().map(Integer::valueOf).collect(Collectors.toList()) : Collections.emptyList());
        principal.setSalesType(data.getSalesType());
        principal.setProductCustomer(data.getIsProductCustomer()==1?true:false);
        principal.setChannelCustomer(data.getIsChannelCustomer()==1?true:false);
        if (StrUtil.isNotEmpty(data.getSkuCategory())) {

            List<String> category = StrUtil.split(data.getSkuCategory(), StrUtil.COMMA);
            List<Integer> collect = category.stream().map(Integer::valueOf).collect(Collectors.toList());
            List<List<Integer>> lists = baseCategoryApiService.bindParentIdByChild(collect);
            principal.setSkuCategoryChecked(lists);
        }
        if (StrUtil.isNotEmpty(data.getAgencySku())) {
            List<String> agencySku = StrUtil.split(data.getAgencySku(), StrUtil.COMMA);
            List<Integer> collect = agencySku.stream().map(Integer::valueOf).collect(Collectors.toList());
            List<List<Integer>> lists = baseCategoryApiService.bindParentIdByChild(collect);
            principal.setAgencySkuChecked(lists);
        }
        log.info("stealthFrontData,traderCustomerId:{},客户经销商，经营大类对象:{}", traderCustomerId,JSON.toJSONString(principal));
        return principal;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void add(TraderCustomerMarketingPrincipalDto traderCustomerMarketingPrincipalDto) {

        traderCustomerMarketingPrincipalMapper.insertSelective(traderCustomerMarketingPrincipalConvertor.toEntity(traderCustomerMarketingPrincipalDto));
        Integer save = 1;
        traderCustomerTagChangeRecordService.calcTraderCustomerMarketingPrincipalDiffAndSave(save, null, traderCustomerMarketingPrincipalDto);

    }

    @Override
    public TraderCustomerMarketingPrincipalDto getByTraderId(Integer traderId) {
        Optional<TraderCustomerMarketingPrincipalEntity> principalMapperByTraderId = Optional.ofNullable(traderCustomerMarketingPrincipalMapper.getByTraderId(traderId));
        if (principalMapperByTraderId.isPresent()) {
            TraderCustomerMarketingPrincipalDto marketingPrincipalDto = traderCustomerMarketingPrincipalConvertor.toDto(principalMapperByTraderId.get());
            marketingPrincipalDto.setSkuTypeName(Arrays.stream(marketingPrincipalDto.getSkuType().split(",")).filter(StringUtils::isNotBlank).map(item -> TraderCustomerMarketingPrincipalEnum.getEnumName("SKU_TYPE", Integer.valueOf(item))).collect(Collectors.joining("、")));

            // scope需要特殊处理，如果父级下的所有子集都被选中，则只展示父级。
            String scopeName = Arrays.stream(marketingPrincipalDto.getSkuScope().split(",")).filter(StringUtils::isNotBlank).map(item -> TraderCustomerMarketingPrincipalEnum.getEnumName("SKU_SCOPE", Integer.valueOf(item))).collect(Collectors.joining("、"));

            // 此处为了兼容既是综合，但也有商品分类的场景
            if (!Objects.isNull(marketingPrincipalDto.getSkuCategory())) {
                List<Integer> thirdCategoryIdList = Arrays.stream(marketingPrincipalDto.getSkuCategory().split(",")).filter(StringUtils::isNotBlank).map(Integer::valueOf).collect(Collectors.toList());
                List<BaseCategoryDto> categoryResult = baseCategoryApiService.getByIdList(thirdCategoryIdList, new ArrayList<>());
                if (TraderCustomerMarketingPrincipalEnum.SKU_SCOPE_COMPREHENSIVE.getLabel().equals(scopeName) || CollectionUtils.isEmpty(categoryResult)) {
                    // 有sku分类，但还是综合
                    marketingPrincipalDto.setSkuScopeName(scopeName);
                } else {
                    // 专业，且有sku分类 只展示分类拼接
                    marketingPrincipalDto.setSkuScopeName(categoryResult.stream().map(BaseCategoryDto::getBaseCategoryName).collect(Collectors.joining("、")));
                }
                marketingPrincipalDto.setSkuCategoryNameList(categoryResult.stream().map(BaseCategoryDto::getBaseCategoryName).collect(Collectors.toList()));
            } else {
                // 无sku分类，综合或/专业
                marketingPrincipalDto.setSkuScopeName(scopeName);
            }

            // 处理代理商品信息,拼接上“其他”
            if (StringUtils.isNotBlank(marketingPrincipalDto.getAgencySku())) {
                List<Integer> agencySkuList = Arrays.stream(marketingPrincipalDto.getAgencySku().split(",")).filter(StringUtils::isNotBlank).map(Integer::valueOf).collect(Collectors.toList());
                List<BaseCategoryDto> categoryResult = baseCategoryApiService.getByIdList(agencySkuList, new ArrayList<>());
                String agencySku = categoryResult.stream().map(BaseCategoryDto::getBaseCategoryName).collect(Collectors.joining("、"));
                marketingPrincipalDto.setAgencySkuName(agencySku);
            }
            String otherAgencySkuName = !StrUtil.isBlank(marketingPrincipalDto.getOtherAgencySku()) ? "其他(" + marketingPrincipalDto.getOtherAgencySku() + ")" : "";
            marketingPrincipalDto.setAgencySkuName(StringUtils.isNotBlank(marketingPrincipalDto.getAgencySkuName()) && StringUtils.isNotBlank(otherAgencySkuName) ?
                    marketingPrincipalDto.getAgencySkuName() + "、" + otherAgencySkuName : (StringUtils.isNotBlank(marketingPrincipalDto.getAgencySkuName()) ? marketingPrincipalDto.getAgencySkuName() : otherAgencySkuName));

            marketingPrincipalDto.setSalesTypeName(Arrays.stream(marketingPrincipalDto.getSalesType().split(",")).filter(StringUtils::isNotBlank).map(item -> TraderCustomerMarketingPrincipalEnum.getEnumName("SALES_TYPE", Integer.valueOf(item))).collect(Collectors.joining("、")));
            // 处理品牌信息
            List<BrandFrontDto> brandList = brandApiService.getByBrandIdList(Arrays.stream(marketingPrincipalDto.getAgencyBrand().split(",")).filter(StringUtils::isNotBlank).map(Integer::parseInt).collect(Collectors.toList()));

            String agencyBrandName = brandList.stream().map(BrandFrontDto::getBrandName).collect(Collectors.joining(","));
            String otherAgencyBrandName = !StrUtil.isBlank(marketingPrincipalDto.getOtherAgencyBrand()) ? "其他(" + marketingPrincipalDto.getOtherAgencyBrand() + ")" : "";
            agencyBrandName = !StrUtil.isBlank(agencyBrandName) && !StrUtil.isBlank(otherAgencyBrandName) ? agencyBrandName + "、" + otherAgencyBrandName : agencyBrandName + otherAgencyBrandName;
            marketingPrincipalDto.setAgencyBrandName(agencyBrandName);
            String governmentRelation = Arrays.stream(marketingPrincipalDto.getGovernmentRelation().split(",")).filter(StringUtils::isNotBlank).map(item -> TraderCustomerMarketingPrincipalEnum.getEnumName("GOVERNMENT_RELATION", Integer.valueOf(item))).filter(StringUtils::isNotBlank).collect(Collectors.joining("、"));
            governmentRelation = StrUtil.isBlank(marketingPrincipalDto.getOtherGovernmentRelation()) ? governmentRelation : governmentRelation.replace("其他关系", "其他关系(" + marketingPrincipalDto.getOtherGovernmentRelation() + ")");
            marketingPrincipalDto.setGovernmentRelationName(governmentRelation);
            return marketingPrincipalDto;
        }
        return new TraderCustomerMarketingPrincipalDto();
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void update(TraderCustomerMarketingPrincipalDto principalDto) {

        List<TraderCustomerMarketingPrincipalEntity> traderCustomerMarketingPrincipalEntities = traderCustomerMarketingPrincipalMapper.selectByTraderCustomerId(principalDto.getTraderCustomerId());

        TraderCustomerMarketingPrincipalDto oldValue = null;
        if (CollUtil.isNotEmpty(traderCustomerMarketingPrincipalEntities)) {
            traderCustomerMarketingPrincipalEntities.forEach(x-> traderCustomerMarketingPrincipalMapper.deleteByPrimaryKey(x.getTraderCustomerMarketingPrincipalId()));
            oldValue = traderCustomerMarketingPrincipalConvertor.toDto(traderCustomerMarketingPrincipalEntities.get(0));
        }

        // 可能为null 删除场景
        if (Objects.nonNull(principalDto)) {
            traderCustomerMarketingPrincipalMapper.insertSelective(traderCustomerMarketingPrincipalConvertor.toEntity(principalDto));
        }
    	Integer update = 2;
    	traderCustomerTagChangeRecordService.calcTraderCustomerMarketingPrincipalDiffAndSave(update,oldValue, principalDto);


    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void clearByTraderCustomer(Integer traderCustomerId) {

        List<TraderCustomerMarketingPrincipalEntity> data = traderCustomerMarketingPrincipalMapper.selectByTraderCustomerId(traderCustomerId);
        if (CollUtil.isNotEmpty(data)) {
            log.info("clearByTraderCustomer，清除，TraderCustomerMarketingPrincipalEntity：{}", JSON.toJSONString(data));
            data.forEach(x-> traderCustomerMarketingPrincipalMapper.deleteByPrimaryKey(x.getTraderCustomerMarketingPrincipalId()));
        }
    }
}