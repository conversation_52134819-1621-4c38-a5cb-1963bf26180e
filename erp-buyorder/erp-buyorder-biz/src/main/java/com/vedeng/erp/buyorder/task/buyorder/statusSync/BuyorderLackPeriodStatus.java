package com.vedeng.erp.buyorder.task.buyorder.statusSync;

import com.newtask.data.AbstractDataByTimeSync;
import com.newtask.data.dao.BuyorderDataMapper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description com.newtask.data.buyorder.statusSync
 * @Date 2021/10/20 10:44
 */
@Component
public class BuyorderLackPeriodStatus extends AbstractDataByTimeSync {

    @Resource
    BuyorderDataMapper buyorderDataMapper;

    @Override
    public List<Map<String, Object>> loadBizByTimeData(Long startTime, Long endTime) {
        return buyorderDataMapper.getOrderLackPeriodByTime(startTime, endTime);
    }


    @Override
    public void updateData(List<Map<String, Object>> dataList) {
        if (CollectionUtils.isNotEmpty(dataList)) {
            List<Integer> orderIds = new ArrayList<>();
            long nowTime = System.currentTimeMillis();
            dataList.forEach(item -> {
                Integer buyorderId = Integer.valueOf(item.get("buyorderId").toString());
                makeExist(buyorderId, nowTime);
                orderIds.add(buyorderId);
            });
            buyorderDataMapper.updateOrderLackPeriodByTime(orderIds, nowTime);
        }
    }

    @Override
    public void makeExist(Integer id, Long nowTime) {
        int buyorderDataId = buyorderDataMapper.getBuyorderDataById(id);
        if (buyorderDataId <= 0) {
            buyorderDataMapper.insertBuyorderData(id, nowTime);
        }
    }
}
