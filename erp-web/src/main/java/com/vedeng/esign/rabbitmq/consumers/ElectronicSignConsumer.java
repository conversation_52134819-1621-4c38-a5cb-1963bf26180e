package com.vedeng.esign.rabbitmq.consumers;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.rabbitmq.client.Channel;
import com.vedeng.base.api.dto.reqParam.SignCallbackDto;
import com.vedeng.common.redis.utils.RedisUtil;
import com.vedeng.common.trace.mq.AbstractMessageListener;
import com.vedeng.erp.buyorder.manager.esign.MultBuyOrderElectronicSignHandle;
import com.vedeng.erp.system.handler.FlowOrderElectronicSignHandler;
import com.vedeng.infrastructure.esign.enums.ElectronicSignBusinessEnums;
import com.vedeng.infrastructure.esign.handle.AbstractElectronicSignHandle;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.util.EnumMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 电子签章回传消息消费者
 * @date 2021/12/10 16:03
 */
@Component
@Slf4j
public class ElectronicSignConsumer extends AbstractMessageListener {

    @Autowired
    @Qualifier("saleOrderElectronicSignHandle")
    private AbstractElectronicSignHandle saleOrderElectronicSignHandle;

    @Autowired
    @Qualifier("manufacturerElectronicSignHandle")
    private AbstractElectronicSignHandle manufacturerElectronicSignHandle;

    @Autowired
    @Qualifier("buyOrderElectronicSignHandle")
    private AbstractElectronicSignHandle buyOrderElectronicSignHandle;

    @Autowired
    @Qualifier("buyOrderExpenseElectronicSignHandle")
    private AbstractElectronicSignHandle buyOrderExpenseElectronicSignHandle;

    @Autowired
    @Qualifier("authorizationElectronicSignHandle")
    private AbstractElectronicSignHandle authorizationElectronicSignHandle;

    @Autowired
    @Qualifier("multBuyOrderElectronicSignHandle")
    private MultBuyOrderElectronicSignHandle multBuyOrderElectronicSignHandle;

    @Autowired
    @Qualifier("flowOrderElectronicSignHandler")
    private FlowOrderElectronicSignHandler flowOrderElectronicSignHandler;


    private final EnumMap<ElectronicSignBusinessEnums, Consumer<SignCallbackDto>> electronicSignDispatcherMap = new EnumMap<>(ElectronicSignBusinessEnums.class);

    /**
     * redis过期时间
     */
    private static final int TIMEOUT = 30;

    /**
     * 初始化电子签章分派逻辑调用处理 map
     */
    @PostConstruct
    private void electronicSignDispatcherMapInit() {
        electronicSignDispatcherMap.put(ElectronicSignBusinessEnums.SALE_ORDER, dto -> saleOrderElectronicSignHandle.parseElectronicSignMq(dto));
        electronicSignDispatcherMap.put(ElectronicSignBusinessEnums.MULT_SALEORDER_SEAL, dto -> saleOrderElectronicSignHandle.parseElectronicSignMq(dto));
        electronicSignDispatcherMap.put(ElectronicSignBusinessEnums.BUY_ORDER, dto -> buyOrderElectronicSignHandle.parseElectronicSignMq(dto));
        electronicSignDispatcherMap.put(ElectronicSignBusinessEnums.VENDOR, dto -> manufacturerElectronicSignHandle.parseElectronicSignMq(dto));
        electronicSignDispatcherMap.put(ElectronicSignBusinessEnums.BUY_ORDER_EXPENSE, dto -> buyOrderExpenseElectronicSignHandle.parseElectronicSignMq(dto));
        electronicSignDispatcherMap.put(ElectronicSignBusinessEnums.SALE_AUTHORIZATION, dto -> authorizationElectronicSignHandle.parseElectronicSignMq(dto));
        electronicSignDispatcherMap.put(ElectronicSignBusinessEnums.CROSS_SHOUQUANSHU, dto -> authorizationElectronicSignHandle.parseElectronicSignMq(dto));
        electronicSignDispatcherMap.put(ElectronicSignBusinessEnums.MULT_BUYORDER_SEAL, dto -> multBuyOrderElectronicSignHandle.parseElectronicSignMq(dto));
        electronicSignDispatcherMap.put(ElectronicSignBusinessEnums.MULT_LIUZHUANG_SEAL, dto -> flowOrderElectronicSignHandler.parseElectronicSignMq(dto));
    }


    @Override
    public void doBusiness(Message message, Channel channel) {
        SignCallbackDto signCallbackDto = null;
        String redisSignKey = null;
        try {
            String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("电子签章：回传消息:[{}]", messageBody);
            signCallbackDto = JSON.parseObject(messageBody, SignCallbackDto.class);

            // 当回传消息flowId为null，自动确认
            if (StrUtil.isBlank(signCallbackDto.getFlowId())) {
                log.error("电子签章：回传消息flowId为null");
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            redisSignKey = "ERP:SIGN:" + signCallbackDto.getFlowId();
            log.info("电子签章：redis幂等key:[{}]", redisSignKey);
            boolean hasKey = RedisUtil.KeyOps.hasKey(redisSignKey);
            if (hasKey) {
                log.info("电子签章：重复接收消息redis幂等key:[{}]", redisSignKey);
            } else {
                RedisUtil.StringOps.set(redisSignKey, messageBody);
                RedisUtil.KeyOps.expire(redisSignKey, TIMEOUT, TimeUnit.MINUTES);
                ElectronicSignBusinessEnums enums = ElectronicSignBusinessEnums.getEnum(Integer.parseInt(signCallbackDto.getBusinessName()));
                Consumer<SignCallbackDto> consumer = electronicSignDispatcherMap.get(enums);
                consumer.accept(signCallbackDto);
                log.info("消费成功");
            }
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("电子签章：消费电子签章回传的信息发生错误，回传合同内容：[{}]，错误信息：[{}]", JSON.toJSONString(signCallbackDto), e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            } catch (Exception ex) {
                log.error("电子签章：消费电子签章回传的信息basicNack发生错误，回传合同内容：[{}]，错误信息：[{}]", JSON.toJSONString(signCallbackDto), e);
            }
            log.info("电子签章：移除redis幂等key[{}]", redisSignKey);
            try {
                RedisUtil.KeyOps.delete(redisSignKey);
            } catch (Throwable ex) {
                log.error("电子签章: redis删除异常");
            }
        }
    }
}
