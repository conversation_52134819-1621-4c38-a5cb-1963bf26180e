package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeStorageInCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeStorageInConvertor;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeStorageInMapper;
import com.vedeng.erp.kingdee.service.KingDeeStorageInApiService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购赠品售后出库
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class PurchaseGiftOutWriter extends BaseWriter<KingDeeStorageInDto> {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;
    @Autowired
    private KingDeeStorageInCommandConvertor commandConvertor;
    @Autowired
    private KingDeeStorageInConvertor kingDeeStorageInConvertor;
    @Autowired
    private KingDeeStorageInMapper kingDeeStorageInMapper;

    @Autowired
    private KingDeeStorageInApiService kingDeeStorageInApiService;

    @Override
    public void doWrite(KingDeeStorageInDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {

        log.info("采购赠品售后退货出库:{}", JSON.toJSONString(dto));

        dto.setKingDeeBizEnums(KingDeeBizEnums.saveStorageIn);
        kingDeeStorageInApiService.register(dto,true);
       /* ArrayList<String> needReturnFields = new ArrayList<>();
        needReturnFields.add("FEntity.FEntryId");
        needReturnFields.add("FEntity.F_QZOK_BDDJHID");

        ArrayList<SuccessEntity> successEntities = kingDeeBaseApi.save(new SaveExtCommand<>(commandConvertor.toCommand(dto),dto.getFormId(),needReturnFields));
        if (CollUtil.isNotEmpty(successEntities)) {
            SuccessEntity successEntity = CollUtil.getFirst(successEntities);
            ArrayList<JsonObject> needReturnData = successEntity.getNeedReturnData();
            Map<String, String> fInStockEntry2Map = new HashMap<>();
            dto.setFId(successEntity.getId());
            if (CollUtil.isNotEmpty(needReturnData)) {
                JsonArray fInStockEntry = needReturnData.get(0).getAsJsonArray("FEntity");
                for (JsonElement a : fInStockEntry) {
                    JsonObject asJsonObject = a.getAsJsonObject();
                    JsonElement fEntryID = asJsonObject.get("FEntryID");
                    JsonElement f_qzok_bddjhid = asJsonObject.get("F_QZOK_BDDJHID");
                    if (Objects.isNull(fEntryID) || Objects.isNull(f_qzok_bddjhid)) {
                        log.warn("采购赠品售后出库，金蝶未返回明细行id");
                    } else {
                        String fEntryIDs = fEntryID.getAsString();
                        String F_QZOK_BDDJHID = f_qzok_bddjhid.getAsString();
                        fInStockEntry2Map.put(F_QZOK_BDDJHID, fEntryIDs);
                    }

                }
            }

            List<KingDeeStorageInDetailDto> fInStockEntry = dto.getFEntity();
            fInStockEntry.forEach(c-> c.setFEntryId(fInStockEntry2Map.get(c.getFQzokBddjhId())));

            KingDeeStorageInEntity kingDeeStorageInEntity = kingDeeStorageInConvertor.toEntity(dto);
            log.info("采购赠品售后出库->数据库:{}",JSON.toJSONString(kingDeeStorageInEntity));
            kingDeeStorageInMapper.insertSelective(kingDeeStorageInEntity);


        }*/
    }


}
