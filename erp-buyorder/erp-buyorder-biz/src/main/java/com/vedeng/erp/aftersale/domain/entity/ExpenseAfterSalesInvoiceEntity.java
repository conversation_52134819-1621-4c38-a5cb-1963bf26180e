package com.vedeng.erp.aftersale.domain.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.*;

/**
 * @description 费用售后退票表
 * <AUTHOR>
 * @date 2022/11/11 13:32
 **/


@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExpenseAfterSalesInvoiceEntity extends BaseEntity {
    /**
     * 主键
     */
    private Long expenseAfterSalesInvoiceId;

    /**
     * 费用售后表主键ID
     */
    private Long expenseAfterSalesId;

    /**
     * 费用单明细表主键ID
     */
    private Integer buyorderExpenseItemId;

    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 发票号码
     */
    private String invoiceNo;

    /**
     * 订货号
     */
    private String sku;

    /**
     * 本次退票数
     */
    private BigDecimal returnNum;

    /**
     * 是否需退票 0否 1是
     */
    private Integer isRefundInvoice;

    /**
     * 退票状态 0未退票 1已退票 2退票中
     */
    private Integer returnInvoiceStatus;

    /**
     * 此记录生成的退票红票id
     */
    private Integer invoiceId;

    /**
     * 是否删除 0否 1是
     */
    private Integer isDelete;

    /**
     * 备注
     */
    private String remark;

    /**
     * 更新备注
     */
    private String updateRemark;

}