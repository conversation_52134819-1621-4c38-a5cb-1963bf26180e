<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ include file="../common/common.jsp" %>
<!-- 引入样式 -->
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/element-ui/css/index.css">
<!-- 引入脚本 -->
<script src="${pageContext.request.contextPath}/static/vue/element-ui/js/vue.js"></script>
<script src="${pageContext.request.contextPath}/static/vue/element-ui/js/ele-index.js"></script>
<style>

</style>

<div id="app">
    <el-form ref="form" :rules="rules" :model="form" label-width="80px">
        <el-form-item label="处理意见" style="padding-top: 9%" prop="text">
            <el-input
                    type="text"
                    v-model="form.text"
                    style="width: 70%"
                    maxlength="50"
                    show-word-limit="true"></el-input>
        </el-form-item>
        <el-form-item style="padding-left: 60%;padding-top: 45px">
            <el-button type="warning" plain @click="cancel">取消</el-button>
            <el-button type="warning" style="margin-left: 20px" @click="submit('form')" :disabled="disableSubmit">确认</el-button>
        </el-form-item>
    </el-form>
</div>

<script type="text/javascript">
    var goodsLowerPriceId = ${goodsLowerPriceId};
    let vm = new Vue({
        el: '#app',
        data() {
            return {
                form: {
                    text: '已处理'
                },
                rules: {
                    text: [
                        {required: true,message: '请填写处理意见！',trigger: 'change'}
                    ]
                },
                disableSubmit: false
            }},
        methods: {
            cancel() {
                layer.closeAll();
                parent.layer.closeAll();
            },
            submit(formName) {
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        this.disableSubmit=true;
                        axios({
                            url: '/order/quote/doAddHandleOpinion.do',
                            method: 'post',
                            data: {
                                goodsLowerPriceId: goodsLowerPriceId,
                                handleOpinion: this.form.text
                            }
                        }).then(res => {
                            if (res.data.code === 0) {
                                this.$message({
                                    message: '处理意见提交成功',
                                    type: 'success'
                                });
                                layer.closeAll();
                                parent.layer.closeAll();
                                parent.window.location.reload();
                            }else {
                                this.$message({
                                    message: '处理失败',
                                    type: 'error'
                                })
                            }
                        }).finally(() =>{
                            setTimeout(() =>{
                                this.disableSubmit = false;
                            },1000)
                        });
                    } else {
                        return false;
                    }
                });
            }
        }
    });
</script>
