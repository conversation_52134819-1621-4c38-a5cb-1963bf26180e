package com.vedeng.order.service;

import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.TraderSupplier;
import com.vedeng.trader.model.vo.TraderCertificateVo;

import java.util.List;

/**
 * 共用方法接口.
 * @jira: VDERP-4324【分级分档】供应链工作台(结合分级分档).
 * @notes: .
 * @version: 1.0.
 * @date: 2020/12/23 下午3:03.
 * @author: Tomcat.Hui.
 */ 
public interface SupplierCommonService {

    Trader getTrader(Integer traderId);

    TraderSupplier getTraderSupplier(Integer traderId);

    List<TraderCertificateVo> getTraderCertificateVos(TraderCertificateVo traderCertificate);
}
