package com.vedeng.trader.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.model.Tag;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TraderSupplier implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer traderSupplierId;

    private Integer traderId;

    private BigDecimal amount;

    private BigDecimal periodAmount;

    private Integer periodDay;

    private Integer isEnable;

    private Integer isTop;

    private String supplyBrand;

    private String supplyProduct;

    private Integer grade;

    private Long disableTime;

    private String disableReason;

    private String comments;

    private String brief;

    private Long addTime;

    private Integer creator;

    private Long modTime;

    private Integer updater;

    private List<TraderSupplierSupplyBrand> traderSupplierSupplyBrands;
    
    private Trader trader;
    
    private String ownerSale;
    
    private List<Tag> tag;
    
    private List<TraderOrderGoods> orderGoods;
    
    private SysOptionDefinition sysOptionDefinition;
    
    private List<String> tagName;
    
    private Integer orderTimes;
    
    private Integer companyId;
    
    private String hotTelephone;
    
    private String serviceTelephone;

    private String afterSaleManager;
    private String installServiceContactName;
    private String installServiceContactWay;
    private String technicalDirectContactName;
    private String technicalDirectContactWay;
    private String maintenanceContactName;
    private String maintenanceContactWay;
    private String exchangeContactName;
    private String exchangeContactWay;
    private String otherContactName;
    private String otherContactWay;

    private String logisticsName;
    
    private String website;
    
    private String[] companyUris;

    /**
     * 仓库地区最小级ID
     */
    private Integer warehouseAreaId;

    /**
     * 仓库多级地址逗号“,”拼接（冗余字段）
     */
    private String warehouseAreaIds;

    /**
     * 仓库地址
     */
    private String warehouseAddress;

    /**
     * 1 生产厂家  2 经销商
     */
    private Integer traderType;

    /**
     * 纳税人分类 1：一般纳税人 2 :小规模纳税人
     */
    private Integer taxPayerType;

    /**
     * 客户最后一次审核通过时间
     */
    private Date lastValidTime;

    /**
     * 供应商名称
     */
    private String traderSupplierName;
}