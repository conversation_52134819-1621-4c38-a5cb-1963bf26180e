package com.vedeng.erp.finance.service;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.finance.domain.dto.*;
import com.vedeng.erp.finance.dto.*;
import com.vedeng.erp.finance.enums.InvoiceRedConfirmationEvent;
import com.vedeng.erp.finance.enums.InvoiceRedConfirmationStateEnum;
import com.vedeng.erp.saleorder.dto.AfterSalesGoodsDto;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 财务发票红字确认单
 * @date 2023/9/20 13:03
 */
public interface InvoiceRedConfirmationService {


    /**
     * 列表信息
     * @param query
     * @return
     */
    PageInfo<InvoiceRedConfirmationDto> page(PageParam<InvoiceRedConfirmationDto> query,boolean needButton);

    /**
     * 获取当前红字确认单可执行的按钮
     * @param invoiceRedConfirmationId id
     * @return List<ViewButton> 按钮集合
     */
    List<InvoiceRedConfirmationServiceViewButton> getButton(Integer invoiceRedConfirmationId);


    /**
     * 执行红字确认单状态机
     * @param invoiceRedConfirmationDto 确认单信息
     * @param event 执行事件
     * @return 当前确认单状态
     */
    InvoiceRedConfirmationStateEnum executeInvoiceRedConfirmationOperaMachine(InvoiceRedConfirmationDto invoiceRedConfirmationDto,
                                                                              InvoiceRedConfirmationEvent event);


    /**
     * 调用税金确认单详情接口
     */
    SaleInvoiceRedConfirmDetailResponseDto getTaxesInvoiceRedConfirmation(InvoiceRedConfirmationDto invoiceRedConfirmationDto);

    /**
     * 初始化
     * @param invoiceRedConfirmationInitDto
     */
    void init(InvoiceRedConfirmationDto invoiceRedConfirmationInitDto);


    /**
     * 系统初始化
     */
    void sysInit();

    /**
     * 同意或拒绝
     * @param requestDtoList
     * @return
     */
    List<RedConfirmResponseDto> audit(RedConfirmReqAuditRequestDto requestDtoList);

    /**
     * 申请
     * @param redConfirmReqApplyRequestDto
     * @return
     */
    List<RedConfirmResponseDto> apply(RedConfirmReqApplyRequestDto redConfirmReqApplyRequestDto);

    /**
     * 撤销
     * @param requestDto
     * @return
     */
    List<RedConfirmResponseDto> cancel(RedConfirmReqCommonRequestDto requestDto);

    /**
     * 开票
     * @param requestDto
     * @return
     */
    List<RedConfirmResponseDto> openInvoice(RedConfirmReqCommonRequestDto requestDto);

    /**
     * 作废
     * @param requestDto
     * @return
     */
    List<RedConfirmResponseDto> invalid(RedConfirmReqCommonRequestDto requestDto);

    /**
     * 更新
     */
    List<RedConfirmResponseDto> renew(RedConfirmReqCommonRequestDto requestDto);

    /**
     * 新增
     */
    void add(InvoiceRedConfirmationDto invoiceRedConfirmationDto);

    /**
     * 修改
     */
    void modify(InvoiceRedConfirmationDto invoiceRedConfirmationDto);

    /**
     * 查询红字明细列表
     * @param invoiceRedConfirmationId 确认单id
     * @return List<AfterSalesGoodsDto>
     */
    List<AfterSalesGoodsDto> getRedDetailList(Integer invoiceRedConfirmationId);

    /**
     * 根据id查询确认单详情
     *
     * @param invoiceRedConfirmationId invoiceRedConfirmationId
     * @return InvoiceRedConfirmationDto
     */
    InvoiceRedConfirmationDto getInvoiceRedConfirmationById(Integer invoiceRedConfirmationId);

    /**
     * 根据蓝票id查询蓝票详情并组装成确认单明细信息(调用初始化接口)
     *
     * @param blueInvoiceId 蓝票id
     * @return List<InvoiceRedConfirmationItemDto>
     */
    List<InvoiceRedConfirmationItemDto> getBlueInvoiceDetail(Integer blueInvoiceId);

    /**
     * 关联售后红字确认单主体信息
     * @param invoiceRedConfirmationId
     * @return
     */
    AssociateSubjectInfoDto associateSubjectInfo(Integer invoiceRedConfirmationId);

    /**
     * 红字确认单人工申请检查初始化
     * @param initCheckDto
     */
    void redLetterConfirmInitCheck(InvoiceRedConfirmationDto initCheckDto);

    /**
     * 分类查询数量
     * @return
     */
    RedConfirmTypeNumDto typeNum();

    /**
     * 购方红字确认单关联售后
     * @param invoiceRedConfirmationDto
     */
    void associateAftersale(InvoiceRedConfirmationDto invoiceRedConfirmationDto);

    /**
     * 获取关联售后明细
     * @param afterSaleId
     * @param saleorderId
     * @param blueInvoiceNo
     * @return
     */
    List<AssociateAfterSaleDetailDto> associateAfterSaleDetailInfo(Integer afterSaleId, Integer saleorderId, String blueInvoiceNo);


    /**
     * 获取红字确认单包含明细
     * @param invoiceRedConfirmationId id
     * @return InvoiceRedConfirmationDto
     */
    InvoiceRedConfirmationDto getInvoiceRedConfirmationDto(Integer invoiceRedConfirmationId);
}
