<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.oa.dao.FileDeliveryConfigMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.oa.model.FileDeliveryConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Nov 14 10:20:59 CST 2024.
    -->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="IS_DELETE" jdbcType="TINYINT" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.vedeng.oa.model.FileDeliveryConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Nov 14 10:20:59 CST 2024.
    -->
    <result column="DELI_CONFIG" jdbcType="LONGVARCHAR" property="deliConfig" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Nov 14 10:20:59 CST 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Nov 14 10:20:59 CST 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Nov 14 10:20:59 CST 2024.
    -->
    ID, IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, UPDATER, UPDATE_REMARK
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Nov 14 10:20:59 CST 2024.
    -->
    DELI_CONFIG
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.vedeng.oa.model.FileDeliveryConfigExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Nov 14 10:20:59 CST 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from T_FILE_DELIVERY_CONFIG
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.vedeng.oa.model.FileDeliveryConfigExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Nov 14 10:20:59 CST 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_FILE_DELIVERY_CONFIG
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Nov 14 10:20:59 CST 2024.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from T_FILE_DELIVERY_CONFIG
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Nov 14 10:20:59 CST 2024.
    -->
    delete from T_FILE_DELIVERY_CONFIG
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.oa.model.FileDeliveryConfigExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Nov 14 10:20:59 CST 2024.
    -->
    delete from T_FILE_DELIVERY_CONFIG
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.oa.model.FileDeliveryConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Nov 14 10:20:59 CST 2024.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_FILE_DELIVERY_CONFIG (IS_DELETE, ADD_TIME, MOD_TIME, 
      CREATOR, UPDATER, UPDATE_REMARK, 
      DELI_CONFIG)
    values (#{isDelete,jdbcType=TINYINT}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, #{updateRemark,jdbcType=VARCHAR}, 
      #{deliConfig,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.oa.model.FileDeliveryConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Nov 14 10:20:59 CST 2024.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_FILE_DELIVERY_CONFIG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
      <if test="deliConfig != null">
        DELI_CONFIG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
      <if test="deliConfig != null">
        #{deliConfig,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.oa.model.FileDeliveryConfigExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Nov 14 10:20:59 CST 2024.
    -->
    select count(*) from T_FILE_DELIVERY_CONFIG
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Nov 14 10:20:59 CST 2024.
    -->
    update T_FILE_DELIVERY_CONFIG
    <set>
      <if test="record.id != null">
        ID = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.isDelete != null">
        IS_DELETE = #{record.isDelete,jdbcType=TINYINT},
      </if>
      <if test="record.addTime != null">
        ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modTime != null">
        MOD_TIME = #{record.modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creator != null">
        CREATOR = #{record.creator,jdbcType=INTEGER},
      </if>
      <if test="record.updater != null">
        UPDATER = #{record.updater,jdbcType=INTEGER},
      </if>
      <if test="record.updateRemark != null">
        UPDATE_REMARK = #{record.updateRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.deliConfig != null">
        DELI_CONFIG = #{record.deliConfig,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Nov 14 10:20:59 CST 2024.
    -->
    update T_FILE_DELIVERY_CONFIG
    set ID = #{record.id,jdbcType=INTEGER},
      IS_DELETE = #{record.isDelete,jdbcType=TINYINT},
      ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{record.modTime,jdbcType=TIMESTAMP},
      CREATOR = #{record.creator,jdbcType=INTEGER},
      UPDATER = #{record.updater,jdbcType=INTEGER},
      UPDATE_REMARK = #{record.updateRemark,jdbcType=VARCHAR},
      DELI_CONFIG = #{record.deliConfig,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Nov 14 10:20:59 CST 2024.
    -->
    update T_FILE_DELIVERY_CONFIG
    set ID = #{record.id,jdbcType=INTEGER},
      IS_DELETE = #{record.isDelete,jdbcType=TINYINT},
      ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{record.modTime,jdbcType=TIMESTAMP},
      CREATOR = #{record.creator,jdbcType=INTEGER},
      UPDATER = #{record.updater,jdbcType=INTEGER},
      UPDATE_REMARK = #{record.updateRemark,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.oa.model.FileDeliveryConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Nov 14 10:20:59 CST 2024.
    -->
    update T_FILE_DELIVERY_CONFIG
    <set>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
      <if test="deliConfig != null">
        DELI_CONFIG = #{deliConfig,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.vedeng.oa.model.FileDeliveryConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Nov 14 10:20:59 CST 2024.
    -->
    update T_FILE_DELIVERY_CONFIG
    set IS_DELETE = #{isDelete,jdbcType=TINYINT},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      DELI_CONFIG = #{deliConfig,jdbcType=LONGVARCHAR}
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.oa.model.FileDeliveryConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Nov 14 10:20:59 CST 2024.
    -->
    update T_FILE_DELIVERY_CONFIG
    set IS_DELETE = #{isDelete,jdbcType=TINYINT},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=INTEGER}
  </update>
</mapper>