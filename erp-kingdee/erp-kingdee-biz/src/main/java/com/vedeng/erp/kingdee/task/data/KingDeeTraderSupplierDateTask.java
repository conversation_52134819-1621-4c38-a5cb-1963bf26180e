package com.vedeng.erp.kingdee.task.data;

import cn.hutool.core.collection.CollectionUtil;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.dto.KingDeeSupplierDto;
import com.vedeng.erp.kingdee.service.KingDeeSupplierApiService;
import com.vedeng.erp.kingdee.service.KingDeeSupplierService;
import com.vedeng.erp.trader.dto.TraderSupplierDto;
import com.vedeng.erp.trader.service.TraderSupplierApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 历史供应商数据推送金蝶
 * @date 2022/9/7 13:23
 **/
@Component
@JobHandler(value = "KingDeeTraderSupplierDateTask")
public class KingDeeTraderSupplierDateTask extends AbstractJobHandler {

    @Autowired
    private TraderSupplierApiService traderSupplierApiService;
    @Autowired
    private KingDeeSupplierService kingDeeSupplierService;
    @Autowired
    private KingDeeSupplierApiService kingDeeSupplierApiService;


    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        List<Integer> idList = Arrays.stream(param.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        idList.forEach(id -> {
            KingDeeSupplierDto kingDeeSupplierInfo = traderSupplierApiService.getKingDeeSupplierInfo(id);
            kingDeeSupplierApiService.register(kingDeeSupplierInfo);
        });
        return ReturnT.SUCCESS;
    }


    private void allPushKingDee() {
        boolean flag = true;
        while (flag) {
            List<TraderSupplierDto> traderSupplierDtos = traderSupplierApiService.selectPushKingDeeTraderSupplierData(null, null, 1000);
            batchPushKingDee(traderSupplierDtos);
            if (CollectionUtil.isEmpty(traderSupplierDtos)) {
                flag = false;
            }
        }
    }

    /**
     * 推送金蝶
     *
     * @param traderSupplierDtos 数据
     */
    private void batchPushKingDee(List<TraderSupplierDto> traderSupplierDtos) {
        if (CollectionUtil.isNotEmpty(traderSupplierDtos)) {
            for (TraderSupplierDto data : traderSupplierDtos) {
                KingDeeSupplierDto kingDeeSupplierDto = bindKingDeeData(data);
                kingDeeSupplierService.save(kingDeeSupplierDto);
            }
        }
    }


    /**
     * 绑定对象
     *
     * @param data 查询的数据
     * @return KingDeeSupplierDto
     */
    private KingDeeSupplierDto bindKingDeeData(TraderSupplierDto data) {
        return new KingDeeSupplierDto(KingDeeBizEnums.saveSupplier, data.getTraderSupplierId(), data.getTraderName());
    }

}
