<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="批量维护报备信息" scope="application"/>
<%--<%@ include file="../../common/common.jsp"%>--%>
<link rel="stylesheet"
      href="${pageContext.request.contextPath}/static/new/css/common/global.css">
<link rel="stylesheet"
      href="${pageContext.request.contextPath}/static/new/css/common/lib/suggestSelect.css?rnd=${resourceVersionKey}">
<link rel="stylesheet"
      href="${pageContext.request.contextPath}/static/new/css/pages/goods/vgoods/sku/sku_edit.css?rnd=${resourceVersionKey}">
<style>
    body {
        background: #fff;
    }

    .form-block {
        border: 0 !important;
    }

    .fanwei-item {
        max-width: 300px;
    }
</style>
<form class="form-wrap J-form" method="post" id="myForm">
    <div class="form-container base-form form-span-7">
        <div class="form-item">
            <div class="form-label"><span class="must">*</span>是否需报备：</div>
            <div class="form-fields">
                <div class="input-radio">
                    <label class="input-wrap">
                        <input type="radio" name="isNeedReport" value="1" onclick="changeauthorization()" <c:if
                                test="${skuGenerate.isNeedReport == 1}"> CHECKED
                        </c:if>>
                        <span class="input-ctnr"></span>需报备
                    </label>
                    <label class="input-wrap">
                        <input type="radio" name="isNeedReport" value="0" onclick="changeauthorization()" <c:if
                                test="${skuGenerate.isNeedReport == 0}"> CHECKED </c:if>>
                        <span class="input-ctnr"></span>无需报备
                    </label>
                </div>
                <input type="hidden"  id="isNeedReport" value="${skuGenerate.isNeedReport}">

                <div class="feedback-block" wrapfor="storageConditionOne"></div>
            </div>
            <c:forEach items="${skuAuthorizationInfo.skuAuthorizationItemVoList}" var="skuAuthorizationItem">
                <c:forEach items="${skuAuthorizationItem.regionIds}" var="regionId">
                    <c:forEach items="${regions}" var="region">
                        <c:if test="${region.regionId eq regionId}">
                            ${region.regionName}、
                        </c:if>
                    </c:forEach>
                </c:forEach>
                <c:forEach items="${skuAuthorizationItem.terminalTypeIds}" var="terminalTypeId">
                    <c:forEach items="${terminalTypes}" var="terminalType">
                        <c:if test="${terminalTypes.sysOptionDefinitionId eq terminalTypeId}">
                            ${terminalType.title}、
                        </c:if>
                    </c:forEach>
                </c:forEach>
            </c:forEach>
        </div>
        <div class="form-item" id="isAuthorizedItem">
            <div class="form-label"><span class="must">*</span>是否获得授权：</div>
            <div class="form-fields">
                <div class="input-radio">
                    <label class="input-wrap">
                        <input type="radio" name="isAuthorized" value="1" onclick="changeauthorization()"
                        <c:if test="${skuGenerate.isAuthorized == 1}"> checked </c:if> >
                        <span class="input-ctnr"></span>有授权
                    </label>
                    <label class="input-wrap">
                        <input type="radio" name="isAuthorized" value="0" onclick="changeauthorization()"
                        <c:if test="${skuGenerate.isAuthorized ==0  }"> checked </c:if> >
                        <span class="input-ctnr"></span>无授权
                    </label>
                </div>
                <input type="hidden" id="isAuthorized" value="${skuGenerate.isAuthorized}">
                <div class="feedback-block" wrapfor="isEnableValidityPeriod"></div>
                <span style="color:red;" id="isNeedReportMsg"></span>
            </div>
        </div>
        <div class="form-item" id="authorizationItem">
                <div class="form-label"><span class="must">*</span>授权范围：</div>
                <c:forEach items="${skuAuthorizationResponses}" var="skuAuthorizationResponse" varStatus="index">
                    <div class="form-fields J-shouquan-item" style="display: flex">
                        <div class="fanwei-item">
                            <div class="J-muiti-select fields-select-suggest"></div>
                            <input type="hidden" class="J-value J-shouquan-input-1" name="regionsStr"
                                   value="${skuAuthorizationResponse.regionsStr}">
                            <input type="hidden" class="J-shouquan-input-2" name="snowFlakeId"
                                   value="${skuAuthorizationResponse.snowFlakeId}">
                            <select class="J-select" name="" id="">
                                <c:forEach items="${regions}" var="region">
                                    <option value="${region.regionId}">${region.regionName}</option>
                                </c:forEach>
                            </select>
                        </div>
                        <div class="fanwei-item">
                            <div class="J-muiti-select fields-select-suggest"></div>
                            <input type="hidden" class="J-value J-shouquan-input-3" name="terminalTypesStr"
                                   value="${skuAuthorizationResponse.terminalTypesStr}">
                            <select class="J-select" name="terminalType">
                                <c:forEach items="${terminalTypes}" var="terminalType">
                                    <option value="${terminalType.sysOptionDefinitionId}">${terminalType.title}</option>
                                </c:forEach>
                            </select>
                        </div>
                        <i class="vd-icon icon-recycle J-sort-del authorization-del" style="display: inline;"></i>
                        <div class="feedback-block" wrapfor="curingType"></div>
                        <span style="color:red;" id="isAuthorizedMsg"></span>
                    </div>
                </c:forEach>

                <div class="sort-add J-sort-add-option form-fields" style="display: flex">
                    <a href="javascript:void(0);" class="sort-add-btn J-sort-add authorization_add">
                        <i class="vd-icon icon-add authorization_add"></i>新增范围
                    </a>
                </div>
                <div class="sort-add  form-fields" style="display: flex">
                    <span style="color:red;" id="skuAuthorizationMsg"></span>

                </div>
                <div class="form-fields">
                    <input type="hidden" id="authorizationValue" name="authorizationValue">
                </div>
            </div>
        <input type="hidden" id="skuIdsStr" name="skuIdsStr" value="${skuIdsStr}">
        <div class="form-btn" style="padding-left: 0">
            <div class="form-fields" id="submitButton">
                <button type="submit" class="btn btn-blue btn-large">确定</button>
                <a class="btn btn-large J-cancel">取消</a>
            </div>
        </div>
    </div>
</form>
<script type="text/tmpl" class="J-shouquan-tmpl">
        <div class="form-fields J-shouquan-item" style="display: flex">
            <div class="fanwei-item">
                <div class="J-muiti-select fields-select-suggest"></div>
                <input type="hidden" class="J-value J-shouquan-input-1" name="region_value"
                       value="">
                <input type="hidden" class="J-shouquan-input-2" name="snowFlake_value"
                       value="">
                <select class="J-select" name="" id="">
                    <c:forEach items="${regions}" var="region">
    <option value="${region.regionId}">${region.regionName}</option>
</c:forEach>
                </select>
            </div>
            <div class="fanwei-item">
                <div class="J-muiti-select fields-select-suggest"></div>
                <input type="hidden" class="J-value J-shouquan-input-3" name="terminalType_value"
                       value="">
                <select class="J-select" name="terminalType">
                    <c:forEach items="${terminalTypes}" var="terminalType">
    <option value="${terminalType.sysOptionDefinitionId}">${terminalType.title}</option>
</c:forEach>
                </select>
            </div>
            <i class="vd-icon icon-recycle J-sort-del authorization-del"  style="display: inline;"></i>
            <div class="feedback-block" wrapfor="curingType"></div>
        </div>
    </script>
<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/suggestSelect.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.validate.js"></script>
<script>
    $(function(){
        var resetShouquanNames = function () {
            $('.J-shouquan-item').each(function(i){
                $(this).find('.J-shouquan-input-1').attr('name', 'skuAuthorizationDtos[' + i + '].regionsStr');
                $(this).find('.J-shouquan-input-2').attr('name', 'skuAuthorizationDtos[' + i + '].snowFlakeId');
                $(this).find('.J-shouquan-input-3').attr('name', 'skuAuthorizationDtos[' + i + '].terminalTypesStr');
            })

            if($('.J-shouquan-item').length > 1){
                $('.J-shouquan-item .J-sort-del').show();
            }else{
                $('.J-shouquan-item .J-sort-del').hide();
            }
        };
        resetShouquanNames();
        var getMultiData = function ($item) {
            var data = [];
            $item.siblings('select').find('option').each(function () {
                data.push({
                    label: $.trim($(this).html()),
                    value: $(this).val()
                })
            });
            return data;
        };

        $('.J-muiti-select').each(function () {
            var data = getMultiData($(this));

            $(this).siblings('select,.select').remove();

            //初始化品牌信息
            new SuggestSelect({
                placeholder: '请选择',
                wrap: $(this),
                data: data,
                multi: true,
                multiAll: true,
                input: $(this).siblings('.J-value'),
            })
        });

        $('.authorization_add').click(function () {
            $(this).parent().before($('.J-shouquan-tmpl').html());

            $('.J-shouquan-item').last().find('.J-muiti-select').each(function () {
                var data = getMultiData($(this));

                $(this).siblings('select,.select').remove();

                //初始化品牌信息
                new SuggestSelect({
                    placeholder: '请选择',
                    wrap: $(this),
                    data: data,
                    multi: true,
                    multiAll: true,
                    input: $(this).siblings('.J-value'),
                })
            });

            resetShouquanNames();
        });

        $(document).on('click', '.authorization-del', function(){
            $(this).parent().remove();
            resetShouquanNames();
        })

        $('.J-cancel').click(function(){
            window.parent && window.parent.closeLayer();
        });

        $.validator.addMethod('needSelected', function () {
            flag = true;

            $('.J-shouquan-input-1, .J-shouquan-input-3').each(function(){
                if(!$(this).val()){
                    flag = false;
                }
            });
            return flag;
        });

       /* $('.J-form').validate({
            rules: {
                authorizationValue: {
                    needSelected: true
                }
            },
            messages: {
                authorizationValue: {
                    needSelected: '请选择省份和终端'
                }
            }
        })*/
        if ($('#isNeedReport').val() == 0){
            $('#isAuthorizedItem').hide();
            $('#authorizationItem').hide();
        }
        
        if ($('#isAuthorized').val() == 0){
            $('#authorizationItem').hide();
        }
    })

    /**
     * SKU报备信息的动态展示
     */
    function changeauthorization() {
        var isNeedReport = $("input[name='isNeedReport']:checked").val();
        if (isNeedReport == 0){
            $('#authorizationItem').hide();
            $('#isAuthorizedItem').hide();
            return;
        } else {
            $('#isAuthorizedItem').show();
            $('#authorizationItem').hide();
        }
        var isAuthorized = $("input[name='isAuthorized']:checked").val();
        if (isAuthorized == 0){
            $('#authorizationItem').hide();
        } else if (isAuthorized == 1) {
            $('#authorizationItem').show();
        }
    }

    $('#myForm').submit(function () {
            jQuery.ajax({
                url:'./batchSaveSkuAuthorizationInfo.do',
                data:$('#myForm').serialize(),
                type:"POST",
                dataType : "json",
                beforeSend:function() {
                    var check = checkSkuAuthorization();
                    if (check){
                        $('#submitButton').hide();
                    }
                    return check;
                },
                success:function(result)
                {
                    if (result.code == 0){
                        layer.alert('保存成功!', function () {
                            parent.location.reload();
                        })
                    } else {
                        layer.alert('保存失败', function () {
                            parent.location.reload();
                        })
                    }
                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }

            });
            return false;
    });

    /**
     * 检查sku报备信息
     * 1.区域和类型必须匹配一边不为空；
     * 2.不同的item之间多个区域不能重复；
     * 3.不允许存在空的item;
     */
    function checkSkuAuthorization() {
        var isNeedReport = $("input[name='isNeedReport']:checked").val();
        if (isNeedReport != 0 && isNeedReport != 1){
            $('#isNeedReportMsg').html('是否需报备不能为空');
            return false;
        } else if (isNeedReport == 0){
            $('#isNeedReportMsg').html('');
            return true;
        }else if (isNeedReport == 1) {
            $('#isNeedReportMsg').html('');
            var isAuthorized = $("input[name='isAuthorized']:checked").val();
            if (isAuthorized != 0 && isAuthorized != 1){
                $('#isAuthorizedMsg').html('是否获得授权不能为空');
                return false;
            } else if (isAuthorized == 0){
                $('#isAuthorizedMsg').html('');
                return true;
            } else if (isAuthorized == 1){
                $('#isAuthorizedMsg').html('');

                var checkRequired = 1;
                var regionsFlag = [];

                var regionsStrs = $('.J-shouquan-input-1');
                if (regionsStrs.length == 0){
                    checkRequired= 0;
                }
                for (var index = 0; index < regionsStrs.length; index++) {
                    var regionStrr = regionsStrs[index].value;
                    var regionStrs = regionStrr.split('@');
                    regionsFlag.push.apply(regionsFlag,regionStrs);

                    if (regionStrr == null || regionStrr == undefined || regionStrr == ''){
                        checkRequired = 0;
                    }
                }
                $('#skuAuthorizationMsg').html('');

                var terminalTypeStr = $('.J-shouquan-input-3');
                if (terminalTypeStr.length == 0){
                    checkRequired= 0;
                }
                for (var index = 0; index < terminalTypeStr.length; index++) {
                    var terminalTypeStrr = terminalTypeStr[index].value;
                    if (terminalTypeStrr == null || terminalTypeStrr == undefined || terminalTypeStrr == ''){
                        checkRequired = 0;
                    }
                }
                if (checkRequired == 0){
                    $('#skuAuthorizationMsg').html('授权范围不允许为空');
                    return false;
                } else {
                    $('#skuAuthorizationMsg').html('');
                }

                var isRegionsRepeat = isRepeat(regionsFlag);
                if (isRegionsRepeat) {
                    $('#skuAuthorizationMsg').html('授权区域不能重复');
                    return false;
                } else {
                    $('#skuAuthorizationMsg').html('');
                }
                return true;
            }
        }
    }

    /**
     * 检测数组元素是否重复
     * @param arr
     * @returns {boolean}
     */
    function isRepeat(arr) {
        var hash = {};
        for(var i in arr) {
            if(hash[arr[i]]) {
                return true;
            }
            hash[arr[i]] = true;
        }
        return false;
    }
</script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/layer.js"></script>
<%--<div class="form-list   form-tips4 ml7">--%>
<%--    <form id="batchTaxCateNo" method="post" enctype="multipart/form-data">--%>
<%--    </form>--%>

<%--</div>--%>


<%@ include file="../../common/footer.jsp" %>