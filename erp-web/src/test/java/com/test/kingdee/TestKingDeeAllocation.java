package com.test.kingdee;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.domain.command.KingDeeAllocationCommand;
import com.vedeng.erp.kingdee.dto.KingDeeAllocationDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeAllocationDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeAllocationMapper;
import com.vedeng.erp.kingdee.mapstruct.KingDeeAllocationCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeAllocationConvertor;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.RepoStatus;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 调拨单
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring.xml", "classpath:spring-mybatis.xml"})
public class TestKingDeeAllocation {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Resource
    private KingDeeAllocationCommandConvertor commandConvertor;

    @Resource
    private KingDeeAllocationConvertor allocationConvertor;

    @Autowired
    private KingDeeAllocationMapper kingDeeAllocationMapper;


    @Test
    public void kingDeeAllocation() {
    	KingDeeAllocationDto dto = new KingDeeAllocationDto();
        dto.setFId("0");
        dto.setFBillNo("");
        dto.setFBillTypeId("ZJDB01_SYS");
        dto.setFTransferDirect("GENERAL");
        dto.setFTransferBizType("InnerOrgTransfer");
        dto.setFStockOutOrgId("101");
        dto.setFOwnerTypeOutIdHead("BD_OwnerOrg");
        dto.setFStockOrgId("101");
        dto.setFDate("2022-09-18 00:00:00");
       dto.setFQzokJdr("张三");
       dto.setFQzokBddjtid("贝登erp对应的单据头ID");

        List<KingDeeAllocationDetailDto>  FEntityDetail = new ArrayList<>();
        KingDeeAllocationDetailDto detailDto = new KingDeeAllocationDetailDto();
        detailDto.setFMaterialId("SKU0001");
        detailDto.setFQty("3.0");
        detailDto.setFSrcStockId("CK9999");
        detailDto.setFDestStockId("CK9998");
        detailDto.setFQzokJdkhid("借货的客户id");
        detailDto.setFQzokYsddh("原始订单号");
        detailDto.setFQzokGsywdh("归属业务单号");
        detailDto.setFQzokYwlx("业务类型");
        detailDto.setFQzokPch("批次号");
        detailDto.setFQzokXlh("序列号");
        FEntityDetail.add(detailDto);
        dto.setFBillEntry(FEntityDetail);

        KingDeeAllocationCommand command = commandConvertor.toCommand(dto);


        System.out.println("入参："+JSON.toJSONString(command));
        RepoStatus save = kingDeeBaseApi.save(new SaveExtCommand<>(command, dto.getFormId()));
        ArrayList<SuccessEntity> successEntities = save.getSuccessEntitys();
//        System.out.println("金蝶返回："+JSON.toJSONString(successEntities));
        if (CollUtil.isNotEmpty(successEntities)) {
            SuccessEntity successEntity = CollUtil.getFirst(successEntities);
            System.out.println("数据库入参："+JSON.toJSONString(allocationConvertor.toEntity(dto)));
            kingDeeAllocationMapper.insertSelective(allocationConvertor.toEntity(dto));
        }
    }
}
