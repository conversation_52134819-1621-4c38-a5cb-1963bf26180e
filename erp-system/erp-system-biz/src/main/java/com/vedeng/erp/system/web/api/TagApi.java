package com.vedeng.erp.system.web.api;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.erp.system.dto.TagDto;
import com.vedeng.erp.system.service.TagService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 系统老标签api
 * @date 2022/7/12 11:36
 */
@ExceptionController
@RestController
@RequestMapping("/tag")
@Slf4j
public class TagApi {

    @Autowired
    private TagService tagService;


    /**
     * 可根据tagType companyid  isRecommend 来查询
     * @param tagDto 对象
     * @return R<List<TagDto>>
     */
    @RequestMapping(value = "/getTypeTags", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<List<TagDto>> getTypeTags(TagDto tagDto) {
        return R.success(tagService.getTypeTags(tagDto));
    }

}
