package com.vedeng.crm.category.web.api;

import com.vedeng.common.core.base.R;
import com.vedeng.crm.api.ProductKeywordApiService;
import com.vedeng.crm.dto.CategoryMatchDto;
import com.vedeng.crm.category.service.ProductKeywordServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商品分类API
 * @menu: AI智能助手 > 商品分类
 * @date 2025/4/9 15:00
 */
@RestController
@RequestMapping("/crm/category")
public class CrmCategoryApi {

    @Autowired
    private ProductKeywordApiService productKeywordApiService;



    /**
     * 提取关键词并匹配商品分类
     * @param input 用户输入内容
     * @return 包含关键词和匹配的商品分类列表
     */
    @RequestMapping("/match")
    public R<CategoryMatchDto> matchCategories(@RequestBody String input) {
        // 提取关键词
        List<String> keywords = productKeywordApiService.extractKeywords(input);
        // 匹配商品分类
        List<CategoryMatchDto.CategoryMatch> matchedCategories = productKeywordApiService.matchCategories(keywords);

        CategoryMatchDto result = new CategoryMatchDto();
        result.setKeywords(keywords);
        result.setMatchedCategories(matchedCategories);
        
        return R.success(result);
    }

    /**
     * 根据业务ID和业务类型获取三级分类和关键词
     * @param businessId 业务ID
     * @param businessType 业务类型 0线索 1商机
     * @return 包含关键词和分类信息的结果
     */
    @RequestMapping("/getByBusiness")
    public R<CategoryMatchDto> getCategoryByBusiness(
            @RequestParam Integer businessId,
            @RequestParam Integer businessType) {
        CategoryMatchDto result = productKeywordApiService.getCategoryByBusiness(businessId, businessType);
        return R.success(result);
    }
}
