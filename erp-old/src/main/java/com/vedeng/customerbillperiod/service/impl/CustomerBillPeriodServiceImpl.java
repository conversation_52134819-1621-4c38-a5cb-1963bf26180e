package com.vedeng.customerbillperiod.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.annotation.MethodLock;
import com.vedeng.common.util.DateUtil;
import com.vedeng.customerbillperiod.constant.CustomerBillPeriodOverdueManageDetailTypeEnum;
import com.vedeng.customerbillperiod.constant.CustomerBillPeriodTypeEnum;
import com.vedeng.customerbillperiod.constant.CustomerBillPeriodUseDetailSummaryKey;
import com.vedeng.customerbillperiod.constant.CustomerBillPeriodUseTypeEnum;
import com.vedeng.customerbillperiod.dao.CustomerBillPeriodMapper;
import com.vedeng.customerbillperiod.dao.CustomerBillPeriodOverdueManagementDetailMapper;
import com.vedeng.customerbillperiod.dao.CustomerBillPeriodUseDetailMapper;
import com.vedeng.customerbillperiod.dto.*;
import com.vedeng.customerbillperiod.exception.CustomerBillPeriodException;
import com.vedeng.customerbillperiod.model.CustomerBillPeriod;
import com.vedeng.customerbillperiod.model.CustomerBillPeriodOverdueManagementDetail;
import com.vedeng.customerbillperiod.model.CustomerBillPeriodUseDetail;
import com.vedeng.customerbillperiod.service.CustomerBillPeriodManagementCodeCommonService;
import com.vedeng.customerbillperiod.service.CustomerBillPeriodService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: daniel
 * @Date: 2021/7/22 15 03
 * @Description:
 */
@Service
public class CustomerBillPeriodServiceImpl implements CustomerBillPeriodService {

    private static final Logger logger = LoggerFactory.getLogger(CustomerBillPeriodServiceImpl.class);

    @Resource
    private CustomerBillPeriodMapper customerBillPeriodMapper;

    @Resource
    private CustomerBillPeriodUseDetailMapper customerBillPeriodUseDetailMapper;

    @Resource
    private CustomerBillPeriodOverdueManagementDetailMapper customerBillPeriodOverdueManagementDetailMapper;

    @Resource
    private CustomerBillPeriodManagementCodeCommonService customerBillPeriodManagementCodeCommonService;

    @Override
    public CustomerBillPeriodUseDetailOfOrder getUsedCustomerBillPeriodAmountByOrderId(Integer companyId, Long customerId, Long orderId) {
        CustomerBillPeriodUseDetailOfOrder useDetailOfOrder = new CustomerBillPeriodUseDetailOfOrder();
        List<CustomerBillPeriodUseDetail> useDetailList = customerBillPeriodUseDetailMapper.getCustomerBillPeriodUseDetailListByOrderId(companyId,
                customerId, orderId);
        BigDecimal originAmount = BigDecimal.ZERO;
        BigDecimal currentAmount = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(useDetailList)) {
            for (CustomerBillPeriodUseDetail item : useDetailList) {
                if (CustomerBillPeriodUseTypeEnum.PAY_ORDER.getCode().equals(item.getUseType())) {
                    originAmount = item.getAmount();
                }
                currentAmount = currentAmount.add(item.getAmount());
            }
        }
        useDetailOfOrder.setOriginUsedAmount(originAmount);
        useDetailOfOrder.setCurrentUsedAmount(currentAmount);
        return useDetailOfOrder;
    }

    @Override
    public List<CustomerBillPeriodSummaryInfoDto> getCustomerBillPeriodSummaryInfo(Integer companyId, Long customerId) {
        List<CustomerBillPeriod> billPeriodList = customerBillPeriodMapper.getCustomerBillPeriodListByCustomerId(companyId, customerId);
        //按照账期类型分类，获取所有账期集合
        Map<Integer, List<CustomerBillPeriod>> billPeriodMap =
                billPeriodList.stream().collect(Collectors.groupingBy(CustomerBillPeriod::getBillPeriodType));

        if (billPeriodMap.keySet().size() == 0) {
            return Collections.emptyList();
        }

        Long currentTime = System.currentTimeMillis();

        return billPeriodMap.keySet()
                .stream()
                .map(key -> {
                    List<CustomerBillPeriod> billPeriodListByType = billPeriodMap.get(key);
                    CustomerBillPeriodSummaryInfoDto summaryInfoDto = new CustomerBillPeriodSummaryInfoDto();
                    summaryInfoDto.setBillPeriodType(key);
                    List<CustomerBillPeriod> validBillPeriodList = billPeriodListByType.stream()
                            .filter(item -> item.getBillPeriodStart() < currentTime && item.getBillPeriodEnd() > currentTime)
                            .collect(Collectors.toList());
                    summaryInfoDto.setTotalAmount(validBillPeriodList.stream().map(CustomerBillPeriod::getApplyAmount).reduce(BigDecimal.ZERO,BigDecimal::add));
                    //如果当前有效账期个数大于一，则不展示账期有效期
                    if (validBillPeriodList.size() == 1){
                        summaryInfoDto.setBillPeriodStart(validBillPeriodList.get(0).getBillPeriodStart());
                        summaryInfoDto.setBillPeriodEnd(validBillPeriodList.get(0).getBillPeriodEnd());
                    }
                    long lastModTime = billPeriodListByType.stream()
                            .map(item -> {
                                if (item.getModTime() == null || item.getModTime() <= 0){
                                    return item.getAddTime();
                                }
                                return item.getModTime();
                            })
                            .reduce(0L,Long::max);

                    summaryInfoDto.setLastModTime(lastModTime);
                    summaryInfoDto.setCountOfValid(validBillPeriodList.size());

                    Map<Integer,Map<Long,String>> useDetailSummary = getUseDetailSummaryOfBillPeriodList(billPeriodListByType);
                    //账期可用余额
                    BigDecimal availableAmount = useDetailSummary.get(CustomerBillPeriodUseDetailSummaryKey.AVAILABLE_AMOUNT).values()
                            .stream().map(BigDecimal::new).reduce(BigDecimal.ZERO, BigDecimal::add);
                    //账期未归还金额
                    BigDecimal unreturnedAmount = useDetailSummary.get(CustomerBillPeriodUseDetailSummaryKey.UNRETURNED_AMOUNT).values()
                            .stream().map(BigDecimal::new).reduce(BigDecimal.ZERO,BigDecimal::add);
                    //账期逾期金额
                    BigDecimal overdueAmount = useDetailSummary.get(CustomerBillPeriodUseDetailSummaryKey.OVERDUE_AMOUNT).values()
                            .stream().map(BigDecimal::new).reduce(BigDecimal.ZERO,BigDecimal::add);
                    //账期使用次数
                    Integer countUsed = useDetailSummary.get(CustomerBillPeriodUseDetailSummaryKey.COUNT_OF_USED).values()
                            .stream().map(Integer::parseInt).reduce(0,Integer::sum);
                    //账期逾期次数
                    Integer countOfOverdue = useDetailSummary.get(CustomerBillPeriodUseDetailSummaryKey.COUNT_OF_OVERDUE).values()
                            .stream().map(Integer::parseInt).reduce(0,Integer::sum);

                    summaryInfoDto.setAvailableAmount(availableAmount);
                    summaryInfoDto.setUnreturnedAmount(unreturnedAmount);
                    summaryInfoDto.setOverDueAmount(overdueAmount);
                    summaryInfoDto.setCountOfUsed(countUsed);
                    summaryInfoDto.setCountOfOverDue(countOfOverdue);

                    return summaryInfoDto;

                })
                .collect(Collectors.toList());

    }

    @Override
    public List<CustomerBillPeriodDetailsDto> getCustomerBillPeriodDetailsByType(Integer companyId, Long customerId, Integer billPeriodType) {
        List<CustomerBillPeriod> validBillPeriodList = customerBillPeriodMapper.getValidCustomerBillPeriodByType(companyId,customerId,
                billPeriodType,System.currentTimeMillis());
        if (validBillPeriodList.size() == 0) {
            return Collections.emptyList();
        }
        Map<Integer,Map<Long,String>> useDetailSummary = getUseDetailSummaryOfBillPeriodList(validBillPeriodList);
        //账期可用余额
        Map<Long,String> availableAmountMap = useDetailSummary.get(CustomerBillPeriodUseDetailSummaryKey.AVAILABLE_AMOUNT);
        //账期未归还金额
        Map<Long,String> unreturnedAmountOfMap = useDetailSummary.get(CustomerBillPeriodUseDetailSummaryKey.UNRETURNED_AMOUNT);
        //账期逾期金额
        Map<Long,String> overdueAmountMap = useDetailSummary.get(CustomerBillPeriodUseDetailSummaryKey.OVERDUE_AMOUNT);
        //账期使用次数
        Map<Long,String> countUsedMap = useDetailSummary.get(CustomerBillPeriodUseDetailSummaryKey.COUNT_OF_USED);
        //账期逾期次数
        Map<Long,String> countOfOverdueMap = useDetailSummary.get(CustomerBillPeriodUseDetailSummaryKey.COUNT_OF_OVERDUE);

        return validBillPeriodList.stream()
                .map(item -> {
                    Long billPeriodId = item.getBillPeriodId();
                    CustomerBillPeriodDetailsDto dto = new CustomerBillPeriodDetailsDto();
                    BeanUtils.copyProperties(item,dto);
                    dto.setAvailableAmount(availableAmountMap.containsKey(billPeriodId) ? new BigDecimal(availableAmountMap.get(billPeriodId)) : BigDecimal.ZERO);
                    dto.setUnreturnedAmount(unreturnedAmountOfMap.containsKey(billPeriodId) ? new BigDecimal(unreturnedAmountOfMap.get(billPeriodId)) : BigDecimal.ZERO);
                    dto.setOverDueAmount(overdueAmountMap.containsKey(billPeriodId) ? new BigDecimal(overdueAmountMap.get(billPeriodId)) : BigDecimal.ZERO);
                    dto.setCountOfUsed(countUsedMap.containsKey(billPeriodId) ? Integer.parseInt(countUsedMap.get(billPeriodId)) : 0);
                    dto.setCountOfOverDue(countOfOverdueMap.containsKey(billPeriodId) ? Integer.parseInt(countOfOverdueMap.get(billPeriodId)) : 0);
                    return dto;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<CustomerBillPeriodCreditHistoryDto> getCustomerBillPeriodCreditHistoryByCreators(CustomerBillPeriodCreditHistoryQueryDto queryDto) {
        if (queryDto == null || CollectionUtils.isEmpty(queryDto.getBillPeriodAppliers())){
            return new ArrayList<>();
        }
        //创建者创建的账期集合
        List<CustomerBillPeriod> customerBillPeriodList = customerBillPeriodMapper.getCustomerBillPeriodListByCreator(queryDto.getCompanyId(),
                queryDto.getBillPeriodAppliers(),queryDto.getStartTime(),queryDto.getEndTime());
        if (CollectionUtils.isEmpty(customerBillPeriodList)){
            return new ArrayList<>();
        }
        //账期集合按照账期类型分类
        Map<Integer,List<CustomerBillPeriod>> mapOfBillPeriod = customerBillPeriodList.stream()
                .collect(Collectors.groupingBy(CustomerBillPeriod::getBillPeriodType));
        return mapOfBillPeriod.keySet().parallelStream()
                .map(billPeriodType -> {
                    CustomerBillPeriodCreditHistoryDto creditHistoryDto = initCustomerBillPeriodCreditHistory(billPeriodType);
                    List<Long> billPeriodIdList =
                            mapOfBillPeriod.get(billPeriodType).stream().map(CustomerBillPeriod::getBillPeriodId).collect(Collectors.toList());

                    //该类型账期集合的总申请额度
                    creditHistoryDto.setApplyAmount(mapOfBillPeriod.get(billPeriodType).stream().map(CustomerBillPeriod::getApplyAmount).reduce(BigDecimal.ZERO,BigDecimal::add));

                    //该类型账期集合的使用集合
                    List<CustomerBillPeriodUseDetail> useDetailList =
                            customerBillPeriodUseDetailMapper.batchGetCustomerBillPeriodUseDetailListByBillPeriodIdList(queryDto.getCompanyId(),
                                    billPeriodIdList,null,null);

                    if (!CollectionUtils.isEmpty(useDetailList)){
                        List<Long> useDetailIdList = new ArrayList<>();
                        //使用账期支付的订单集合
                        List<Long> orderIdList = new ArrayList<>();
                        BigDecimal unreturnedAmount = BigDecimal.ZERO;
                        BigDecimal freezingAmount = BigDecimal.ZERO;
                        for (CustomerBillPeriodUseDetail item : useDetailList){
                            freezingAmount = freezingAmount.add(item.getAmount());
                            if (CustomerBillPeriodUseTypeEnum.PAY_ORDER.getCode().equals(item.getUseType())){
                                useDetailIdList.add(item.getBillPeriodUseDetailId());
                                if (item.getOccupancy() == 1 && item.getUnreturnedAmount().compareTo(BigDecimal.ZERO) > 0){
                                    orderIdList.add(item.getRelatedId());
                                    unreturnedAmount = unreturnedAmount.add(item.getUnreturnedAmount());
                                }
                            }
                        }
                        creditHistoryDto.setOrderIdList(orderIdList);
                        creditHistoryDto.setUnreturnedAmount(unreturnedAmount);
                        creditHistoryDto.setAvailableAmount(creditHistoryDto.getApplyAmount().subtract(freezingAmount));

                        //该账期集合下使用明细关联的逾期管理记录
                        List<CustomerBillPeriodOverdueManagementDetail> overdueManagementDetailList =
                                customerBillPeriodOverdueManagementDetailMapper.getOverdueManageDetailByUseDetailIdList(useDetailIdList,
                                        Arrays.asList(CustomerBillPeriodOverdueManageDetailTypeEnum.ORDER_EXPRESS.getCode(),
                                                CustomerBillPeriodOverdueManageDetailTypeEnum.ORDER_INVOICE.getCode()));
                        if (!CollectionUtils.isEmpty(overdueManagementDetailList)){
                            BigDecimal overdueAmount = BigDecimal.ZERO;
                            int daysOfOverdue = 0;
                            List<Long> overdueManageDetailIdList = new ArrayList<>();
                            for (CustomerBillPeriodOverdueManagementDetail item : overdueManagementDetailList){
                                if (item.getOverdueDays() > 0){
                                    //逾期未归还金额
                                    overdueAmount = overdueAmount.add(item.getUnreturnedAmount());
                                    daysOfOverdue = daysOfOverdue + item.getOverdueDays();
                                    overdueManageDetailIdList.add(item.getBillPeriodOverdueManagementDetailId());
                                }
                            }
                            creditHistoryDto.setOverdueAmount(overdueAmount);
                            creditHistoryDto.setDaysOfOverdue(daysOfOverdue);
                            if (overdueManageDetailIdList.size() > 0) {
                                creditHistoryDto.setOrderCountOfOverdue(customerBillPeriodOverdueManagementDetailMapper.getOrderIdListByOverdueManageDetailIdList(overdueManageDetailIdList).size());
                            } else {
                                creditHistoryDto.setOrderCountOfOverdue(0);
                            }
                        }
                    }

                    //历史逾期统计
                    getCustomerBillPeriodOverdueHistoryInTimeInterval(queryDto.getCompanyId(),billPeriodIdList,queryDto.getStartTime(),
                            queryDto.getEndTime(),creditHistoryDto);
                    return creditHistoryDto;
                })
                .collect(Collectors.toList());
    }

    @Override
    public CustomerBillPeriodAvailableAmountDto getCustomerBillPeriodAvailableAmount(CustomerBillPeriodAvailableAmountQueryDto availableAmountQueryDto) {
        CustomerBillPeriodAvailableAmountDto availableAmountDto = new CustomerBillPeriodAvailableAmountDto();
        //查询客户各类型账期的可用余额
        List<BillPeriodItem> billPeriodItems = customerBillPeriodMapper.getAvailableAmountGroupByType(availableAmountQueryDto.getCompanyId(),
                availableAmountQueryDto.getCustomerId(),availableAmountQueryDto.getOrderId(),System.currentTimeMillis());
        availableAmountDto.setAllBillPeriod(billPeriodItems);
        availableAmountDto.setTotalAvailableAmount(billPeriodItems.stream().map(BillPeriodItem::getAvailableAmount).reduce(BigDecimal.ZERO,BigDecimal::add));
        return availableAmountDto;
    }


    @MethodLock(className = CustomerBillPeriodFreezingDto.class, field = "orderId", time = 10000)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void freezingCustomerBillPeriodAmount(CustomerBillPeriodFreezingDto freezingDto) throws CustomerBillPeriodException {
        logger.info("客户账期冻结金额：{}",freezingDto.toString());
        //将可用账期按照订单、正式、临时的账期类型进行排序
        List<CustomerBillPeriod> availableAmountInBillPeriodList = customerBillPeriodMapper.getAvailableAmountGroupByBillPeriod(freezingDto.getCompanyId(),
                freezingDto.getCustomerId(), freezingDto.getOrderId(),System.currentTimeMillis()).stream()
                .filter(item -> item.getApplyAmount().compareTo(BigDecimal.ZERO) > 0)
                .peek(item -> {
                    if (CustomerBillPeriodTypeEnum.ORDER.getCode().equals(item.getBillPeriodType())){
                        item.setCreator(-1);
                    } else {
                        item.setCreator(item.getBillPeriodType());
                    }
                })
                .sorted(Comparator.comparing(CustomerBillPeriod::getCreator))
                .collect(Collectors.toList());
        BigDecimal availableAmount = availableAmountInBillPeriodList.stream().map(CustomerBillPeriod::getApplyAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
        if (availableAmount.compareTo(freezingDto.getFreezingAmount()) < 0) {
            throw new CustomerBillPeriodException("客户账期冻结金额失败，客户可用余额为：" + availableAmount);
        }

        //防止同一订单重复冻结账期金额
        if (customerBillPeriodUseDetailMapper.getCustomerBillPeriodUseDetailListByOrderId(freezingDto.getCompanyId(), freezingDto.getCustomerId(),
                freezingDto.getOrderId()).size() > 0) {
            throw new CustomerBillPeriodException("客户账期冻结金额失败，该订单已使用过账期");
        }

        //按照订单、正式、临时账期类型，顺序扣减可用金额；
        List<CustomerBillPeriodUseDetail> useDetailList = new ArrayList<>();
        BigDecimal freezingAmount = freezingDto.getFreezingAmount();
        for (CustomerBillPeriod item : availableAmountInBillPeriodList){
            CustomerBillPeriodUseDetail useDetail = new CustomerBillPeriodUseDetail();
            useDetail.setBillPeriodId(item.getBillPeriodId());
            useDetail.setCompanyId(freezingDto.getCompanyId());
            useDetail.setCustomerId(freezingDto.getCustomerId());
            useDetail.setSettlementPeriod(item.getSettlementPeriod());
            useDetail.setSettlementType(freezingDto.getSettlementType());
            useDetail.setOccupancy(0);
            useDetail.setUseType(CustomerBillPeriodUseTypeEnum.PAY_ORDER.getCode());
            useDetail.setRelatedId(freezingDto.getOrderId());
            useDetail.setParentUseDetailId(0L);
            useDetail.setAddTime(freezingDto.getAddTime());
            BigDecimal amount = item.getApplyAmount().compareTo(freezingAmount) >= 0 ? freezingAmount : item.getApplyAmount();
            useDetail.setAmount(amount);
            useDetail.setUnreturnedAmount(amount);
            useDetailList.add(useDetail);

            freezingAmount = freezingAmount.subtract(item.getApplyAmount());
            if (freezingAmount.compareTo(BigDecimal.ZERO) <= 0){
                break;
            }
        }
        useDetailList.forEach(customerBillPeriodUseDetailMapper::insertSelective);
        logger.info("批量生成客户账期使用记录：{}", JSONObject.toJSONString(useDetailList));
    }

    @Override
    public void occupancyCustomerBillPeriodFrozenAmount(CustomerBillPeriodOccupancyDto occupancyDto) throws CustomerBillPeriodException {
        logger.info("占用已冻结的客户账期：{}",occupancyDto.toString());
        try {
            customerBillPeriodUseDetailMapper.occupancyCustomerBillPeriodUseDetailList(occupancyDto.getCompanyId(),occupancyDto.getCustomerId(),
                    occupancyDto.getOrderId());
        } catch (Exception e){
            logger.error("占用已冻结的客户账期，发生异常：",e);
            throw new CustomerBillPeriodException("占用已冻结的客户账期失败");
        }
    }

    @Override
    public List<CustomerBillPeriodUseDetail> getOccupancyCustomerBillPeriodUseDetail(CustomerBillPeriodOccupancyDto occupancyDto) throws CustomerBillPeriodException {
        try {
            return customerBillPeriodUseDetailMapper.getOccupancyCustomerBillPeriodUseDetail(occupancyDto.getCompanyId(), occupancyDto.getCustomerId(),
                    occupancyDto.getOrderId());
        } catch (Exception e) {
            logger.error("查询是否被占用，发生异常：", e);
            throw new CustomerBillPeriodException("查询是否被占用失败");
        }
    }


    @MethodLock(className = CustomerBillPeriodUnfreezingDto.class, field = "relatedId", time = 10000)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void  unfreezingCustomerBillPeriodAmount(CustomerBillPeriodUnfreezingDto unfreezingDto) throws CustomerBillPeriodException {
        logger.info("解冻客户已冻结的客户账期使用明细：{}",unfreezingDto.toString());
        if (!CustomerBillPeriodUseTypeEnum.isExist(unfreezingDto.getUnfreezingType())){
            throw new CustomerBillPeriodException("解冻客户账期失败，解冻类型错误");
        }
        if (customerBillPeriodUseDetailMapper.getUseDetailListByRelatedIdAndType(unfreezingDto.getCompanyId(),unfreezingDto.getCustomerId(),
                unfreezingDto.getRelatedId(),unfreezingDto.getUnfreezingType()).size() > 0){
            throw new CustomerBillPeriodException("解冻客户账期失败，该类型数据已发生解冻操作");
        }
        //订单流项目增加：销售订单撤销生效功能，撤销生效时，同步要删除账期冻结记录
        if (CustomerBillPeriodUseTypeEnum.CANCEL_VALID.getCode().equals(unfreezingDto.getUnfreezingType())){
            customerBillPeriodUseDetailMapper.removeCustomerBillPeriodFrozenUseDetail(unfreezingDto.getCompanyId(),unfreezingDto.getCustomerId(),
                    unfreezingDto.getRelatedId());
            logger.info("订单：{}撤销生效，删除对应的冻结账期记录",unfreezingDto.getRelatedId());
            return;
        }
        //未还金额大于0的账期使用明细
        List<CustomerBillPeriodUseDetail> useDetailListOfUnreturned =
                customerBillPeriodUseDetailMapper.getCustomerBillPeriodUseDetailListByOrderId(unfreezingDto.getCompanyId(),
                        unfreezingDto.getCustomerId(),unfreezingDto.getOrderId()).stream()
                        .filter(item -> CustomerBillPeriodUseTypeEnum.PAY_ORDER.getCode().equals(item.getUseType()) && item.getUnreturnedAmount().compareTo(BigDecimal.ZERO) > 0)
                        .collect(Collectors.toList());

        Set<Long> billPeriodIdList = new HashSet<>();
        List<Long> userDetailIdListOfUnreturned = new ArrayList<>();
        for (CustomerBillPeriodUseDetail item : useDetailListOfUnreturned){
            billPeriodIdList.add(item.getBillPeriodId());
            userDetailIdListOfUnreturned.add(item.getBillPeriodUseDetailId());
        }
        //使用的账期类型
        Map<Long, CustomerBillPeriod> mapOfBillPeriodType =
                customerBillPeriodMapper.batchGetCustomerBillPeriodById(new ArrayList<>(billPeriodIdList)).stream().collect(Collectors.toMap(CustomerBillPeriod::getBillPeriodId, item -> item));

        //账期使用记录的逾期情况
        List<Long> useDetailIdListHasOverdue = customerBillPeriodOverdueManagementDetailMapper.getCustomerBillPeriodUseDetailIdHasOverdue(userDetailIdListOfUnreturned);

        //归还规则：逾期 > 正常；订单账期>临时账期>正式账期；
        //对于未完全还款的使用明细，按照以上规则进行排序
        List<CustomerBillPeriodUseDetail> useDetailListOfUnreturnedSorted = useDetailListOfUnreturned.stream()
                .peek(item -> {
                    item.setSettlementType(mapOfBillPeriodType.get(item.getBillPeriodId()).getBillPeriodType());
                    if (useDetailIdListHasOverdue.contains(item.getBillPeriodUseDetailId())){
                        item.setSettlementPeriod(0);
                    } else {
                        item.setSettlementPeriod(1);
                    }
                })
                .sorted(Comparator.comparing(CustomerBillPeriodUseDetail::getSettlementPeriod).thenComparing(item -> item.getSettlementType() * -1))
                .collect(Collectors.toList());

        BigDecimal unfreezingAmount = unfreezingDto.getUnfreezingAmount().abs();
        for (CustomerBillPeriodUseDetail item : useDetailListOfUnreturnedSorted){
            CustomerBillPeriodUseDetail unfreezingItem = new CustomerBillPeriodUseDetail();
            unfreezingItem.setBillPeriodId(item.getBillPeriodId());
            unfreezingItem.setCompanyId(item.getCompanyId());
            unfreezingItem.setCustomerId(item.getCustomerId());
            unfreezingItem.setUseType(unfreezingDto.getUnfreezingType());
            unfreezingItem.setRelatedId(unfreezingDto.getRelatedId());
            unfreezingItem.setParentUseDetailId(item.getBillPeriodUseDetailId());
            unfreezingItem.setAddTime(unfreezingDto.getAddTime());
            BigDecimal amount = item.getUnreturnedAmount().compareTo(unfreezingAmount) >= 0 ? unfreezingAmount : item.getUnreturnedAmount();
            unfreezingItem.setAmount(amount.multiply(new BigDecimal("-1")));
            customerBillPeriodUseDetailMapper.insertSelective(unfreezingItem);
            logger.info("生成账期解冻明细：{}",unfreezingItem.toString());

            //更新账期使用明细的待归还金额
            CustomerBillPeriodUseDetail toUpdate = new CustomerBillPeriodUseDetail();
            toUpdate.setBillPeriodUseDetailId(item.getBillPeriodUseDetailId());
            toUpdate.setUnreturnedAmount(item.getUnreturnedAmount().subtract(amount));
            customerBillPeriodUseDetailMapper.updateByPrimaryKeySelective(toUpdate);
            logger.info("更新账期使用明细中待归还金额：{}",toUpdate.toString());

            //如果解冻账期类型是还款或者售后退货，则需要同步添加账期编码记录，及更新未还金额
            if (CustomerBillPeriodUseTypeEnum.REPAYMENT.getCode().equals(unfreezingDto.getUnfreezingType())
                    || CustomerBillPeriodUseTypeEnum.AFTER_SALES_RETURN.getCode().equals(unfreezingDto.getUnfreezingType())){
                RollbackCustomerBillPeriodManagementDetailDto managementDetailDto = new RollbackCustomerBillPeriodManagementDetailDto();
                BeanUtils.copyProperties(unfreezingDto,managementDetailDto);
                if (CustomerBillPeriodUseTypeEnum.REPAYMENT.getCode().equals(unfreezingDto.getUnfreezingType())){
                    managementDetailDto.setType(CustomerBillPeriodOverdueManageDetailTypeEnum.REPAYMENT.getCode());
                } else if (CustomerBillPeriodUseTypeEnum.AFTER_SALES_RETURN.getCode().equals(unfreezingDto.getUnfreezingType())){
                    managementDetailDto.setType(CustomerBillPeriodOverdueManageDetailTypeEnum.AFTER_SALES_RETURN.getCode());
                }
                managementDetailDto.setAmount(amount);
                customerBillPeriodManagementCodeCommonService.generateNegativeBillPeriodManagementCode(managementDetailDto);
            }

            unfreezingAmount = unfreezingAmount.subtract(amount);
            if (unfreezingAmount.compareTo(BigDecimal.ZERO) <= 0){
                break;
            }
        }
    }


    private CustomerBillPeriodCreditHistoryDto initCustomerBillPeriodCreditHistory(Integer billPeriodType){
        CustomerBillPeriodCreditHistoryDto creditHistoryDto = new CustomerBillPeriodCreditHistoryDto();
        creditHistoryDto.setBillPeriodType(billPeriodType);
        creditHistoryDto.setOrderIdList(new ArrayList<>());
        creditHistoryDto.setUnreturnedAmount(BigDecimal.ZERO);
        creditHistoryDto.setOverdueAmount(BigDecimal.ZERO);
        creditHistoryDto.setOrderCountOfOverdue(0);
        creditHistoryDto.setDaysOfOverdue(0);
        creditHistoryDto.setAvgOverdueDaysByOrder(BigDecimal.ZERO);
        creditHistoryDto.setApplyAmount(BigDecimal.ZERO);
        creditHistoryDto.setAvailableAmount(BigDecimal.ZERO);
        creditHistoryDto.setHistoryCountOfOverdue(0);
        creditHistoryDto.setHistoryUsedCount(0);
        creditHistoryDto.setHistoryPercentOverdueByUsedCount(BigDecimal.ZERO);
        creditHistoryDto.setHistoryOrderIdList(new ArrayList<>());
        creditHistoryDto.setHistoryOrderCountOfOverdue(0);
        creditHistoryDto.setHistoryDaysOfOverdue(0);
        creditHistoryDto.setHistoryAvgDaysOfOverdueByOrder(BigDecimal.ZERO);
        return creditHistoryDto;
    }


    /**
     * 获取时间段里的指定账期的信用记录信息
     * @param companyId 公司id
     * @param billPeriodIdList 账期集合
     * @param start 开始时间
     * @param end 截止时间
     */
    private void getCustomerBillPeriodOverdueHistoryInTimeInterval(Integer companyId, List<Long> billPeriodIdList, Long start, Long end,
                                                                   CustomerBillPeriodCreditHistoryDto creditHistoryDto){
        //按照产品要求，上线之前(2021-09-07，时间戳：1630944000000)的逾期记录不纳入信用记录统计
        if (start == null || start < 1630944000000L){
            start = 1630944000000L;
        }
        List<CustomerBillPeriodUseDetail> useDetailList =
                customerBillPeriodUseDetailMapper.batchGetCustomerBillPeriodUseDetailListByBillPeriodIdList(companyId, billPeriodIdList,start,end);
        if (CollectionUtils.isEmpty(useDetailList)){
            return;
        }

        List<Long> useDetailIdList = new ArrayList<>();
        List<Long> orderIdList = new ArrayList<>();
        for (CustomerBillPeriodUseDetail item : useDetailList){
            if (CustomerBillPeriodUseTypeEnum.PAY_ORDER.getCode().equals(item.getUseType())){
                useDetailIdList.add(item.getBillPeriodUseDetailId());
                orderIdList.add(item.getRelatedId());
            }
        }
        List<CustomerBillPeriodOverdueManagementDetail> overdueManagementDetailList =
                customerBillPeriodOverdueManagementDetailMapper.getOverdueManageDetailByUseDetailIdList(useDetailIdList,
                        Arrays.asList(CustomerBillPeriodOverdueManageDetailTypeEnum.ORDER_EXPRESS.getCode(),
                                CustomerBillPeriodOverdueManageDetailTypeEnum.ORDER_INVOICE.getCode()));
        if (CollectionUtils.isEmpty(overdueManagementDetailList)){
            return;
        }
        int historyCountOfOverdue = 0;
        int historyUsedCount = 0;
        Set<Long> historyOrderOfOverdue = new HashSet<>();
        BigDecimal historyAmountOfOverdue = BigDecimal.ZERO;
        int historyDaysOfOverdue = 0;
        for (CustomerBillPeriodOverdueManagementDetail item : overdueManagementDetailList){
            historyUsedCount ++;
            if (item.getOverdueDays() > 0){
                historyCountOfOverdue ++;
                historyAmountOfOverdue = historyAmountOfOverdue.add(item.getOverdueAmount());
                historyOrderOfOverdue.add(item.getRelatedId());
                historyDaysOfOverdue += item.getOverdueDays();
            }
        }
        creditHistoryDto.setHistoryCountOfOverdue(historyCountOfOverdue);
        creditHistoryDto.setHistoryUsedCount(historyUsedCount);
        if (historyUsedCount == 0){
            creditHistoryDto.setHistoryPercentOverdueByUsedCount(BigDecimal.ZERO);
        } else {
            creditHistoryDto.setHistoryPercentOverdueByUsedCount(new BigDecimal(historyCountOfOverdue).divide(new BigDecimal(historyUsedCount),2, RoundingMode.HALF_UP));
        }
        creditHistoryDto.setHistoryOrderIdList(orderIdList);
        creditHistoryDto.setHistoryAmountOfOverdue(historyAmountOfOverdue);
        creditHistoryDto.setHistoryOrderCountOfOverdue(historyOrderOfOverdue.size());
        creditHistoryDto.setHistoryDaysOfOverdue(historyDaysOfOverdue);
        if (historyOrderOfOverdue.size() == 0) {
            creditHistoryDto.setHistoryAvgDaysOfOverdueByOrder(BigDecimal.ZERO);
        } else {
            creditHistoryDto.setHistoryAvgDaysOfOverdueByOrder(new BigDecimal(historyDaysOfOverdue).divide(new BigDecimal(historyOrderOfOverdue.size()),2, RoundingMode.HALF_UP));
        }
    }


    /**
     * 获取指定账期集合的使用情况
     * @param billPeriodList 客户所有账期集合（有效的和失效的）
     */
    private Map<Integer,Map<Long,String>> getUseDetailSummaryOfBillPeriodList(List<CustomerBillPeriod> billPeriodList){
        List<Long> billPeriodIdList = billPeriodList.stream().map(CustomerBillPeriod::getBillPeriodId).collect(Collectors.toList());
        //账期可用余额
        Map<Long,String> availableAmountMap =
                customerBillPeriodMapper.getAvailableAmountByBillPeriodList(billPeriodIdList, DateUtil.sysTimeMillis()).stream()
                        .collect(Collectors.toMap(CustomerBillPeriod::getBillPeriodId,item -> item.getApplyAmount().toString()));
        //账期未归还金额
        Map<Long,String> unreturnedAmountOfMap =
                customerBillPeriodUseDetailMapper.getUnreturnedAmountByBillPeriodIdList(billPeriodIdList).stream()
                        .collect(Collectors.toMap(CustomerBillPeriodUseDetail::getBillPeriodId,item->item.getUnreturnedAmount().toString()));
        //账期逾期金额
        Map<Long,String> overdueAmountMap =
                customerBillPeriodOverdueManagementDetailMapper.getOverdueAmountByBillPeriodIdList(billPeriodIdList).stream()
                        .collect(Collectors.toMap(CustomerBillPeriodOverdueManagementDetail::getBillPeriodUseDetailId,
                                item -> item.getOverdueAmount().toString()));
        //账期使用次数
        Map<Long,String> countUsedMap =
                customerBillPeriodUseDetailMapper.getCountUsedGroupByBillPeriod(billPeriodIdList).stream().collect(Collectors.toMap(CustomerBillPeriodUseDetail::getBillPeriodId,item->String.valueOf(item.getOccupancy())));
        //账期逾期次数
        Map<Long,String> countOfOverdueMap =
                customerBillPeriodOverdueManagementDetailMapper.getCountOfOverdueGroupByBillPeriod(billPeriodIdList).stream()
                        .collect(Collectors.toMap(CustomerBillPeriodOverdueManagementDetail::getBillPeriodUseDetailId,
                                item->String.valueOf(item.getOverdueDays())));

        Map<Integer,Map<Long,String>> useDetailSummary = new HashMap<>(5);
        useDetailSummary.put(CustomerBillPeriodUseDetailSummaryKey.AVAILABLE_AMOUNT,availableAmountMap);
        useDetailSummary.put(CustomerBillPeriodUseDetailSummaryKey.UNRETURNED_AMOUNT,unreturnedAmountOfMap);
        useDetailSummary.put(CustomerBillPeriodUseDetailSummaryKey.OVERDUE_AMOUNT,overdueAmountMap);
        useDetailSummary.put(CustomerBillPeriodUseDetailSummaryKey.COUNT_OF_USED,countUsedMap);
        useDetailSummary.put(CustomerBillPeriodUseDetailSummaryKey.COUNT_OF_OVERDUE,countOfOverdueMap);
        return useDetailSummary;
    }

    @Override
    public List<CustomerBillPeriodUseDetail> revertCustomerBillPeriodInfo(Long billPeriodId, Long relatedId, Long parentUseDetailId) {
        return customerBillPeriodUseDetailMapper.getRevertBillPeriodInfo(billPeriodId, parentUseDetailId);
    }

    @Override
    public List<CustomerBillPeriodDetailManageDto> getBillPeriodDetailManageInfo(Long parentUseDetailId) {
        return customerBillPeriodUseDetailMapper.getBillPeriodDetailManage(parentUseDetailId);
    }

    @Override
    public CustomerBillPeriod selectByprimaryKey(Long billPeriodId) {
        return customerBillPeriodMapper.selectByPrimaryKey(billPeriodId);
    }
}
