package com.newtask.filemove.batch;

import com.google.common.collect.Lists;
import com.newtask.filemove.AbstractFileMoveInterface;
import com.newtask.filemove.dto.CommonConstant;
import com.vedeng.common.constant.CommonConstants;
import com.newtask.filemove.dto.FileMoveDto;
import com.newtask.filemove.theadpool.FileMoveThreadPool;
import com.newtask.filemove.theadpool.TraderFinanceTask;
import com.vedeng.filemove.dao.FileMoveDao;
import com.vedeng.trader.model.TraderFinance;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ExecutorService;

/**
 * 商品附件的批处理命令
 */
@Service
public class TraderFinanceBatchCommand extends AbstractFileMoveInterface {

    private static final Logger LOGGER = LoggerFactory.getLogger(TraderFinanceBatchCommand.class);

    @Resource
    private FileMoveDao fileMoveDao;

    protected String getCommandType() {
        return CommonConstant.BATCH;
    }

    protected List<FileMoveDto> getFileMoveInfoByPageSize(int start, int pageSize) {

        List<TraderFinance> traderFinanceList = fileMoveDao.getTraderFinanceByPage(start,pageSize);

        if(CollectionUtils.isEmpty(traderFinanceList)){
            return null;
        }

        return Lists.transform(traderFinanceList , (entity) -> {
                                                        FileMoveDto fileMoveDto = new FileMoveDto();
                                                        fileMoveDto.setId(entity.getTraderFinanceId());
                                                        fileMoveDto.setDomain(entity.getAverageTaxpayerDomain());
                                                        fileMoveDto.setUri(entity.getAverageTaxpayerUri());
                                                        return fileMoveDto;
                                                    });
    }

    @Override
    protected Runnable getFileMoveTask(List<FileMoveDto> fileMoveDtoList) {
        return new TraderFinanceTask(fileMoveDtoList);
    }

    @Override
    protected ExecutorService getExecutorService() {
        return FileMoveThreadPool.getTraderCertificateThreadPool();
    }
}
