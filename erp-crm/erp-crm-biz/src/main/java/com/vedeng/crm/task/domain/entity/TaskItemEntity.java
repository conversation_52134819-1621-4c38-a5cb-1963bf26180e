package com.vedeng.crm.task.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 任务记录明细表
 */
@Getter
@Setter
public class TaskItemEntity extends BaseEntity {
    /**
     * 主键
     */
    private Long taskItemId;

    /**
     * 任务主键
     */
    private Long taskId;

    /**
     * 任务人ID
     */
    private Integer taskUser;

    /**
     * 任务人
     */
    private String taskUserName;

    /**
     * 处理状态：0待处理 1已处理 2关闭
     */
    private Integer doneStatus;

    /**
     * 处理人ID
     */
    private Integer doneUser;

    /**
     * 处理人
     */
    private String doneUserName;

    /**
     * 处理时间
     */
    private Date doneTime;

    /**
     * 处理结果
     */
    private String doneRemark;

    /**
     * 是否删除 0否 1是
     */
    private Integer isDelete;


    /**
     * 更新备注
     */
    private String updateRemark;

    /**
     * 是否已临期提醒（0否，1是）
     */
    private Integer isDeadlineReminder;

    /**
     * 是否已超时提醒（0否，1是）
     */
    private Integer isTimeoutReminder;
}