package com.vedeng.erp.kingdee.batch.handle;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023/12/1 9:05
 **/
@Service
@Slf4j
public class BuyOrderVirtualInvoiceHandle {


    @Autowired
    private BatchSettlementBillItemDtoMapper batchSettlementBillItemDtoMapper;

    @Autowired
    private BatchBuyorderGoodsDtoMapper batchBuyorderGoodsDtoMapper;

    @Autowired
    private BatchVirtualInvoiceItemDtoMapper batchVirtualInvoiceItemDtoMapper;

    @Autowired
    private BatchRVirtualInvoiceJWarehouseDtoMapper batchRVirtualInvoiceJWarehouseDtoMapper;


    /**
     * 采购票排除虚拟票
     * @param data  采购票数据
     * @return 排除虚拟票后的采购票数据
     */
    public List<BatchInvoiceDetailDto> checkAndProcessIfHaveVirtualInvoice(List<BatchInvoiceDetailDto> data) {

        log.info("采购票排除虚拟票，入参：{}", JSON.toJSONString(data));

        if (CollUtil.isEmpty(data)) {
            return data;
        }
        // 判断是否有异常数据
        List<Integer> buyOrderGoodsIds = data.stream().map(BatchInvoiceDetailDto::getDetailgoodsId).collect(Collectors.toList());
        List<BatchSettlementBillItemDto> settlementBillItemDtoList = batchSettlementBillItemDtoMapper.selectByBusinessItemIdAndBuyOrder(buyOrderGoodsIds);

        List<BatchBuyorderGoodsDto> buyOrderGoodsDtoList = batchBuyorderGoodsDtoMapper.findByBuyorderGoodsIdInAndIsDelete(buyOrderGoodsIds, 0);
        Map<Integer, BatchBuyorderGoodsDto> buyOrderGoods2Map = buyOrderGoodsDtoList.stream().collect(Collectors.toMap(BatchBuyorderGoodsDto::getBuyorderGoodsId, x -> x, (k1, k2) -> k1));
        List<BatchSettlementBillItemDto> matchAllRebate = settlementBillItemDtoList.stream().filter(x -> {
            BatchBuyorderGoodsDto batchBuyorderGoodsDto = buyOrderGoods2Map.get(x.getBusinessItemId());
            if (Objects.isNull(batchBuyorderGoodsDto)) {
                return false;
            }
            Integer num = Optional.ofNullable(batchBuyorderGoodsDto.getNum()).orElse(0);
            BigDecimal price = Optional.ofNullable(batchBuyorderGoodsDto.getPrice()).orElse(BigDecimal.ZERO);
            BigDecimal amount = price.multiply(new BigDecimal(num));
            batchBuyorderGoodsDto.setAmount(amount);
            return x.getAmount().compareTo(amount) == 0;
        }).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(matchAllRebate)) {
            log.error("业务数据异常：存在异常录入的全部返利的票明细：全返利，{}",JSON.toJSONString(matchAllRebate));
            return Collections.emptyList();
        }
        List<Integer> invoiceDetailIds = data.stream().map(BatchInvoiceDetailDto::getInvoiceDetailId).collect(Collectors.toList());

        // 计算排除虚拟票的数量
        List<BatchVirtualInvoiceItemDto> virtualInvoiceItemDtoList = batchVirtualInvoiceItemDtoMapper.selectBySourceInvoiceItemIds(invoiceDetailIds);
        if (CollUtil.isEmpty(virtualInvoiceItemDtoList)) {
            return data;
        }
        Map<Integer, List<BatchVirtualInvoiceItemDto>> virtualInvoice2Map = virtualInvoiceItemDtoList.stream().collect(Collectors.groupingBy(BatchVirtualInvoiceItemDto::getSourceInvoiceItemId));
        data.forEach(x->{

            List<BatchVirtualInvoiceItemDto> virtualInvoiceItemList = virtualInvoice2Map.get(x.getInvoiceDetailId());
            if (CollUtil.isEmpty(virtualInvoiceItemList)) {
                return;
            }
            BigDecimal allNum = virtualInvoiceItemList.stream().map(a -> a.getNum().abs()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            BigDecimal subtract = x.getNum().subtract(allNum);
            x.setNum(subtract);
        });

        List<BatchInvoiceDetailDto> result = data.stream().filter(x -> x.getNum().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        log.info("处理后返回结果：{}",JSON.toJSONString(result));

        return result;
    }

    /**
     * 处理可用库存扣除虚拟票绑定
     * @param data 可用库存扣除虚拟票绑定数据
     * @return 处理后的可用库存扣除虚拟票绑定数据
     */
    public List<BatchRInvoiceDetailJOperateLogDto> checkAndProcessVirtualOperateLog(List<BatchRInvoiceDetailJOperateLogDto> data) {

        log.info("处理可用库存扣除虚拟票绑定，入参:{}", JSON.toJSONString(data));
        if (CollUtil.isEmpty(data)) {
            return data;
        }


        List<Integer> operateItemIds = data.stream().map(BatchRInvoiceDetailJOperateLogDto::getOperateLogId).collect(Collectors.toList());

        List<BatchRVirtualInvoiceJWarehouseDto> virtualInvoiceJWarehouseDtoList = batchRVirtualInvoiceJWarehouseDtoMapper.selectByWarehouseGoodsOutInItemIds(operateItemIds);
        if (CollUtil.isEmpty(virtualInvoiceJWarehouseDtoList)) {
            return data;
        }

        Map<Integer, List<BatchRVirtualInvoiceJWarehouseDto>> virtualInvoice2Map = virtualInvoiceJWarehouseDtoList.stream().collect(Collectors.groupingBy(BatchRVirtualInvoiceJWarehouseDto::getWarehouseGoodsOutInItemId));
        data.forEach(x->{
            // 可用库存扣除虚拟票绑定数量
            List<BatchRVirtualInvoiceJWarehouseDto> virtualInvoiceItemList = virtualInvoice2Map.get(x.getOperateLogId());
            if (CollUtil.isEmpty(virtualInvoiceItemList)) {
                return;
            }
            BigDecimal allNum = virtualInvoiceItemList.stream().map(a -> a.getNum().abs()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            BigDecimal subtract = x.getCanRelationNum().subtract(allNum);
            x.setCanRelationNum(subtract);
        });

        List<BatchRInvoiceDetailJOperateLogDto> result = data.stream().filter(x -> x.getCanRelationNum().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        log.info("处理后返回结果：{}",JSON.toJSONString(result));

        return result;
    }


    public List<BatchRVirtualInvoiceJWarehouseDto> bindInvoice(List<BatchRInvoiceDetailJOperateLogDto> validWarehousingLogByRelatedIds, BatchVirtualInvoiceItemDto x, Integer outInType) {
        log.info("发票详情关联有效出入库记录:{},{}", JSON.toJSONString(validWarehousingLogByRelatedIds), JSON.toJSONString(x));

        List<BatchRVirtualInvoiceJWarehouseDto> result = new LinkedList<>();
        for (BatchRInvoiceDetailJOperateLogDto validDetailGoodsItem : validWarehousingLogByRelatedIds) {
            if (validDetailGoodsItem.getCanRelationNum().compareTo(BigDecimal.ZERO) < 1) {
                continue;
            }

            log.info("开始绑定发票与出入库记录:{}", JSON.toJSONString(validDetailGoodsItem));
            BigDecimal thisRelationNum = validDetailGoodsItem.getCanRelationNum().compareTo(x.getNum()) > -1 ?
                    x.getNum() : validDetailGoodsItem.getCanRelationNum();

            BatchRVirtualInvoiceJWarehouseDto rVirtualInvoiceJWarehouseDto = BatchRVirtualInvoiceJWarehouseDto.builder()
                    .warehouseGoodsOutInItemId(validDetailGoodsItem.getOperateLogId())
                    .virtualInvoiceItemId(x.getVirtualInvoiceItemId())
                    .num(thisRelationNum)
                    .warehouseGoodsOutInId(validDetailGoodsItem.getWarehouseGoodsOutInId())
                    .virtualInvoiceId(x.getVirtualInvoiceId())
                    .sku("V" + validDetailGoodsItem.getGoodsId())
                    .skuId(validDetailGoodsItem.getGoodsId())
                    .outInType(outInType)
                    .build();
            rVirtualInvoiceJWarehouseDto.setAddTime(new Date());
            rVirtualInvoiceJWarehouseDto.setModTime(new Date());
            rVirtualInvoiceJWarehouseDto.setCreator(ErpConstant.DEFAULT_USER_ID);
            rVirtualInvoiceJWarehouseDto.setUpdater(ErpConstant.DEFAULT_USER_ID);
            log.info("发票与出入库关联关系生成 BatchRVirtualInvoiceJWarehouseDto:{}", JSON.toJSONString(rVirtualInvoiceJWarehouseDto));
            result.add(rVirtualInvoiceJWarehouseDto);
            validDetailGoodsItem.setCanRelationNum(validDetailGoodsItem.getCanRelationNum().subtract(thisRelationNum).stripTrailingZeros());
            x.setNum(x.getNum().subtract(thisRelationNum).stripTrailingZeros());
            if (x.getNum().compareTo(BigDecimal.ZERO) < 1) {
                break;
            }
        }
        return result;
    }
}
