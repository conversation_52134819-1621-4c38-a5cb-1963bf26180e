package com.vedeng.erp.buyorder.service;

import com.vedeng.erp.buyorder.dto.BuyorderExpenseItemDto;
import com.vedeng.order.model.BuyorderModifyApply;
import com.vedeng.order.model.BuyorderModifyApplyGoods;

import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.buyorder.service
 * @Date 2021/11/2 9:12
 */
public interface NewBuyOrderModifyApplyService {

    /**
     * 保存采购单申请修改
     */
    Integer saveBuyOrderEditApply(BuyorderModifyApply buyorderModifyApply, List<BuyorderExpenseItemDto> buyOrderExpenseGoodsList);

    /**
     * 校验是否允许修改发货方式（普发/直发）
     * type: 1: 普发改直发； 2：直发改普发
     */
    void checkChangeDeliveryType(Integer type, Integer buyorderId);

    /**
     * 根据采购商品修改集合校验对应销售单中的sku是否已经发货
     * buyorderModifyApplyGoodsList 采购商品修改集合（为了获取buyorderGoodsId）
     * false: 已发货；true: 未发货
     */
    Boolean checkSaleorderGoodsDeliveryStatus(List<BuyorderModifyApplyGoods> buyorderModifyApplyGoodsList);

}
