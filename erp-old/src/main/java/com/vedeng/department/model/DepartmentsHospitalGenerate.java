package com.vedeng.department.model;

public class DepartmentsHospitalGenerate {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_DEPARTMENTS_HOSPITAL.DEPARTMENT_ID
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	private Integer departmentId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_DEPARTMENTS_HOSPITAL.DEPARTMENT_NAME
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	private String departmentName;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_DEPARTMENTS_HOSPITAL.DESCRIPTION
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	private String description;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_DEPARTMENTS_HOSPITAL.IS_DELETE
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	private Integer isDelete;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_DEPARTMENTS_HOSPITAL.UPDATER
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	private Integer updater;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_DEPARTMENTS_HOSPITAL.MOD_TIME
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	private Long modTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_DEPARTMENTS_HOSPITAL.CREATOR
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	private Integer creator;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_DEPARTMENTS_HOSPITAL.ADD_TIME
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	private Long addTime;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_DEPARTMENTS_HOSPITAL.DEPARTMENT_ID
	 * @return  the value of T_DEPARTMENTS_HOSPITAL.DEPARTMENT_ID
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	public Integer getDepartmentId() {
		return departmentId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_DEPARTMENTS_HOSPITAL.DEPARTMENT_ID
	 * @param departmentId  the value for T_DEPARTMENTS_HOSPITAL.DEPARTMENT_ID
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	public void setDepartmentId(Integer departmentId) {
		this.departmentId = departmentId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_DEPARTMENTS_HOSPITAL.DEPARTMENT_NAME
	 * @return  the value of T_DEPARTMENTS_HOSPITAL.DEPARTMENT_NAME
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	public String getDepartmentName() {
		return departmentName;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_DEPARTMENTS_HOSPITAL.DEPARTMENT_NAME
	 * @param departmentName  the value for T_DEPARTMENTS_HOSPITAL.DEPARTMENT_NAME
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	public void setDepartmentName(String departmentName) {
		this.departmentName = departmentName;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_DEPARTMENTS_HOSPITAL.DESCRIPTION
	 * @return  the value of T_DEPARTMENTS_HOSPITAL.DESCRIPTION
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_DEPARTMENTS_HOSPITAL.DESCRIPTION
	 * @param description  the value for T_DEPARTMENTS_HOSPITAL.DESCRIPTION
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	public void setDescription(String description) {
		this.description = description;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_DEPARTMENTS_HOSPITAL.IS_DELETE
	 * @return  the value of T_DEPARTMENTS_HOSPITAL.IS_DELETE
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	public Integer getIsDelete() {
		return isDelete;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_DEPARTMENTS_HOSPITAL.IS_DELETE
	 * @param isDelete  the value for T_DEPARTMENTS_HOSPITAL.IS_DELETE
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	public void setIsDelete(Integer isDelete) {
		this.isDelete = isDelete;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_DEPARTMENTS_HOSPITAL.UPDATER
	 * @return  the value of T_DEPARTMENTS_HOSPITAL.UPDATER
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	public Integer getUpdater() {
		return updater;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_DEPARTMENTS_HOSPITAL.UPDATER
	 * @param updater  the value for T_DEPARTMENTS_HOSPITAL.UPDATER
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	public void setUpdater(Integer updater) {
		this.updater = updater;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_DEPARTMENTS_HOSPITAL.MOD_TIME
	 * @return  the value of T_DEPARTMENTS_HOSPITAL.MOD_TIME
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	public Long getModTime() {
		return modTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_DEPARTMENTS_HOSPITAL.MOD_TIME
	 * @param modTime  the value for T_DEPARTMENTS_HOSPITAL.MOD_TIME
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	public void setModTime(Long modTime) {
		this.modTime = modTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_DEPARTMENTS_HOSPITAL.CREATOR
	 * @return  the value of T_DEPARTMENTS_HOSPITAL.CREATOR
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	public Integer getCreator() {
		return creator;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_DEPARTMENTS_HOSPITAL.CREATOR
	 * @param creator  the value for T_DEPARTMENTS_HOSPITAL.CREATOR
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_DEPARTMENTS_HOSPITAL.ADD_TIME
	 * @return  the value of T_DEPARTMENTS_HOSPITAL.ADD_TIME
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	public Long getAddTime() {
		return addTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_DEPARTMENTS_HOSPITAL.ADD_TIME
	 * @param addTime  the value for T_DEPARTMENTS_HOSPITAL.ADD_TIME
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	public void setAddTime(Long addTime) {
		this.addTime = addTime;
	}
}