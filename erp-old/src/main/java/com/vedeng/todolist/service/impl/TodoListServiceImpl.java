package com.vedeng.todolist.service.impl;

import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.EmptyUtils;
import com.vedeng.common.util.StringUtil;
import com.vedeng.firstengage.dao.FirstEngageMapper;
import com.vedeng.firstengage.dao.ProductCompanyMapper;
import com.vedeng.firstengage.dao.RegistrationNumberMapper;
import com.vedeng.firstengage.model.*;
import com.vedeng.firstengage.service.FirstEngageService;
import com.vedeng.goods.dao.CoreSkuGenerateMapper;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.dao.CoreSpuGenerateMapper;
import com.vedeng.goods.dao.CoreSpuMapper;
import com.vedeng.goods.manufacturer.dao.ManufacturerMapper;
import com.vedeng.goods.manufacturer.model.Manufacturer;
import com.vedeng.goods.model.*;
import com.vedeng.goods.model.vo.GoodsStorageConditionVo;
import com.vedeng.goods.utils.GoodsStorageConditionUtils;
import com.vedeng.order.dao.BussinessChanceMapper;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.dao.StandardCategoryMapper;
import com.vedeng.system.dao.SysOptionDefinitionMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.service.SysOptionDefinitionService;
import com.vedeng.todolist.constant.RiskCheckTodoListBuzPropertyEnum;
import com.vedeng.todolist.constant.TodoListBuzSceneEnum;
import com.vedeng.todolist.dao.RiskCheckLogMapper;
import com.vedeng.todolist.dao.TodoListMapper;
import com.vedeng.todolist.dto.QualityDepartmentTodoListDto;
import com.vedeng.todolist.dto.QualityDepartmentWarnListDto;
import com.vedeng.todolist.dto.SupplyChainTodoListDto;
import com.vedeng.todolist.model.TodoList;
import com.vedeng.todolist.service.TodoListService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: daniel
 * @Date: 2020/12/15 09 26
 * @Description:
 */
@Service
public class TodoListServiceImpl implements TodoListService {

    @Resource
    private TodoListMapper todoListMapper;

    @Resource
    private CoreSkuMapper coreSkuMapper;

    @Resource
    private CoreSpuMapper coreSpuMapper;

    @Resource
    private CoreSpuGenerateMapper coreSpuGenerateMapper;

    @Resource
    private FirstEngageMapper firstEngageMapper;

    @Resource
    private RegistrationNumberMapper registrationNumberMapper;

    @Resource
    private ProductCompanyMapper productCompanyMapper;

    @Autowired
    private AttachmentMapper attachmentMapper;

    @Resource
    private CoreSkuGenerateMapper coreSkuGenerateMapper;

    @Autowired
    private SysOptionDefinitionService sysOptionDefinitionService;

    @Resource
    private StandardCategoryMapper standardCategoryMapper;

    @Resource
    private SysOptionDefinitionMapper sysOptionDefinitionMapper;

    @Autowired
    private FirstEngageService firstEngageService;

    @Value("${api_http}")
    protected String api_http;

    @Autowired
    private RiskCheckLogMapper riskCheckLogMapper;

    @Resource
    private ManufacturerMapper manufacturerMapper;

    @Override
    public SupplyChainTodoListDto getSupplyChainTodoListByUser(List<Integer> userIdList) {
        SupplyChainTodoListDto dto = new SupplyChainTodoListDto();
        List<TodoList> riskCheckSkuTodoList = todoListMapper.getUnHandledRiskCheckSkuTodoListByUserList(TodoListBuzSceneEnum.RISK_CHECK_SKU_DATA.getBuzSceneId(),userIdList);
        Integer countOfRiskCheckSkuTodoListGroupByOrder = riskCheckSkuTodoList.parallelStream().map(TodoList::getBuzExtra).filter(StringUtils::isNotBlank).collect(Collectors.toSet()).size();
        dto.setRiskCheckSkuTodoListCount(riskCheckSkuTodoList.parallelStream().map(TodoList::getBuzId).collect(Collectors.toSet()).size());
        dto.setRiskCheckSkuTodoListCountGroupByOrder(countOfRiskCheckSkuTodoListGroupByOrder);

        List<TodoList> riskCheckTraderSupplyTodoList = todoListMapper.getUnHandledRiskCheckTraderSupplyTodoListByUserList(TodoListBuzSceneEnum.RISK_CHECK_TRADER_SUPPLY_DATA.getBuzSceneId(),userIdList);
        dto.setRiskCheckTraderSupplyTodoListCount(riskCheckTraderSupplyTodoList.parallelStream().map(TodoList::getBuzId).collect(Collectors.toSet()).size());
        Integer countOfRiskCheckTraderSupplyTodoListGroupByOrder = riskCheckTraderSupplyTodoList.parallelStream().map(TodoList::getBuzExtra).filter(StringUtils::isNotBlank).collect(Collectors.toSet()).size();
        dto.setRiskCheckTraderSupplyTodoListCountGroupByOrder(countOfRiskCheckTraderSupplyTodoListGroupByOrder);

        dto.setMaintainSkuAndSpuCountGroupByGrade(getUnhandledMainTodoListCountGroupByGrade(TodoListBuzSceneEnum.MAINTAIN_DATA_SKU,userIdList));
        dto.setMaintainDeliveryTimeCountGroupByGrade(getUnhandledMainTodoListCountGroupByGrade(TodoListBuzSceneEnum.MAINTAIN_DATA_DELIVERY_TIME,userIdList));
        dto.setMaintainPriceCountGroupByGrade(getUnhandledMainTodoListCountGroupByGrade(TodoListBuzSceneEnum.MAINTAIN_DATA_PRICE,userIdList));
        if (dto.getMaintainPriceCountGroupByGrade().size() > 0){
            dto.setMaintainPriceSkuList(getUnHandledMaintainPriceTodoListGroupByGrade(userIdList));
        }
        dto.setMaintainAftersalePolicyCountGroupByGrade(getUnhandledMainTodoListCountGroupByGrade(TodoListBuzSceneEnum.MAINTAIN_DATA_AFTER_SALE_POLICY,userIdList));
        dto.setMaintainSupplyAftersalePolicyCountGroupByGrade(getUnhandledMainTodoListCountGroupByGrade(TodoListBuzSceneEnum.MAINTAIN_DATA_SUPPLY_AFTER_SALE_POLICY,userIdList));
        dto.setMaintainOperationInfoCountGroupByGrade(getUnhandledMainTodoListCountGroupByGrade(TodoListBuzSceneEnum.MAINTAIN_DATA_OPERATION_INFO,userIdList));
        return dto;
    }

    @Override
    public QualityDepartmentTodoListDto getQualityDepartmentTodoList() {
        QualityDepartmentTodoListDto dto = new QualityDepartmentTodoListDto();
        dto.setTraderCertificateCheckCount(todoListMapper.getUnHandledCountByBuzType(TodoListBuzSceneEnum.ZLB_CHECK_TRADER_CUSTOMER_CERTIFICATE.getBuzSceneId()));
        dto.setBuyorderCheckCount(todoListMapper.getUnHandledCountByBuzType(TodoListBuzSceneEnum.ZLB_CHECK_BUY_ORDER.getBuzSceneId()));
        dto.setSaleorderCheckCount(todoListMapper.getUnHandledCountByBuzType(TodoListBuzSceneEnum.ZLB_CHECK_SALE_ORDER.getBuzSceneId()));
        dto.setRiskSaleorderCheckCount(riskCheckLogMapper.getUnTriggeredCountByPropertyGroupByBuzExtra(RiskCheckTodoListBuzPropertyEnum.RISK_CHECK_SALEORDER.getValue()));
        dto.setRiskBuyorderCheckCount(riskCheckLogMapper.getUnTriggeredCountByPropertyGroupByBuzExtra(RiskCheckTodoListBuzPropertyEnum.RISK_CHECK_BUYORDER.getValue()));
        return dto;
    }

    @Override
    public QualityDepartmentWarnListDto getQualityDepartmentWarnList() {
        QualityDepartmentWarnListDto dto = new QualityDepartmentWarnListDto();
       /* String nowDate = DateUtil.getNowDate("yyyy-MM-dd");
        Long serEndTime = DateUtil.getDateAfter(DateUtil.StringToDate(nowDate,"yyyy-MM-dd"),30);
        //供应商
        //25营业执照
        dto.setSupplyBusinessLicense(todoListMapper.getSupplyLicenseNum(serEndTime, SysOptionConstant.ID_25));
        //439生产许可证
        dto.setSupplyManufacturingLicense(todoListMapper.getSupplyLicenseNum(serEndTime,SysOptionConstant.ID_439));
        //1102生产企业生产产品登记表
        dto.setSupplyProductsRegister(todoListMapper.getSupplyLicenseNum(serEndTime,SysOptionConstant.ID_1102));
        //29医疗器械经营许可证（三类）有效期至
        dto.setSupplyThirdLevelLicense(todoListMapper.getSupplyLicenseNum(serEndTime,SysOptionConstant.ID_29));
        //客户
        //25营业执照
        dto.setTraderBusinessLicense(todoListMapper.getTraderLicenseNum(serEndTime,SysOptionConstant.ID_25));
        //438执业许可证
        dto.setTraderPracticiseLicense(todoListMapper.getTraderLicenseNum(serEndTime,SysOptionConstant.ID_438));
        //29医疗器械经营许可证（三类）有效期至
        dto.setTraderThirdLevelLicense(todoListMapper.getTraderLicenseNum(serEndTime,SysOptionConstant.ID_29));*/

        Map<String, Object> paramMap = new HashMap<>();
        //查询一个月即将过期
        // 0月 时间戳
        Long firstEngageDateStart = DateUtil.convertLong(DateUtil.getDayOfMonth(0), DateUtil.DATE_FORMAT);
        // 1个月前
        Long firstEngageDateEnd = DateUtil.convertLong(DateUtil.getDayOfMonth(1), DateUtil.DATE_FORMAT);
        paramMap.put("firstEngageODateStart", firstEngageDateStart);
        paramMap.put("firstEngageODateEnd", firstEngageDateEnd);
        //查询已经过期
        firstEngageDateEnd = DateUtil.convertLong(DateUtil.getDayOfMonth(0), DateUtil.DATE_FORMAT);
        paramMap.put("firstEngageEDateEnd", firstEngageDateEnd);
        paramMap.put("firstEngage", new FirstEngage());
        List<FirstEngage> firstEngageList = firstEngageMapper.getFirstEngageInfoList(paramMap);
        dto.setProductsRegistration(firstEngageList.size());
        return dto;
    }

    /**
     * 按照SKU等级，统计待维护的待办事项
     * @param sceneEnum 待办事项业务场景
     * @param userList 用户集合
     * @return 结果
     */
    private Map<Integer, Integer> getUnhandledMainTodoListCountGroupByGrade(TodoListBuzSceneEnum sceneEnum,
                                                                            List<Integer> userList){
        List<TodoList> maintainTodoList =
                todoListMapper.getUnHandledMaintainTodoListCountByUserListGroupByGrade(sceneEnum.getBuzSceneId(), userList);
        //按照sku等级分类统计
        return maintainTodoList.stream().collect(Collectors.toMap(TodoList::getId,TodoList::getBuzId));
    }


    /**
     * 获取待维护价格的待办事项，按照商品等级统计sku
     * @param userList 用户集合
     * @return 结果
     */
    private Map<Integer, String> getUnHandledMaintainPriceTodoListGroupByGrade(List<Integer> userList){
        Map<Integer, List<TodoList>> maintainPriceOfSku =
                todoListMapper.getUnHandledMaintainTodoListSkuByUserList(TodoListBuzSceneEnum.MAINTAIN_DATA_PRICE.getBuzSceneId(), userList)
                        .stream()
                        .collect(Collectors.groupingBy(TodoList::getBuzType));
        Map<Integer, String> skuListGroupByGoodsLevel = new HashMap<>();
        for (Integer key : maintainPriceOfSku.keySet()){
            String skuListStr = maintainPriceOfSku.get(key).stream().map(TodoList::getBuzExtra).collect(Collectors.joining(","));
            skuListGroupByGoodsLevel.put(key,skuListStr);
        }
        return skuListGroupByGoodsLevel;
    }

    private String listObject2String(List<TodoList> list){
        return list.stream().map(item -> String.valueOf(item.getBuzId())).collect(Collectors.joining(","));
    }
    /**
     * 国标分类，一级
     */
    private static final Integer STANDARD_PARRNT_ID_0 = 0;
    /**
     * @description: 获取商品风控信息
     * @return: Map<String, Object>
     * @author: Strange
     * @date: 2020/12/23
     **/
    @Override
    public Map<String, Object> getRiskSkuInfo(Goods goods) {
        Map<String, Object> resultMap = new HashMap<>();
        String sku = goods.getSku();
        if(StringUtil.isBlank(sku)){
            return resultMap;
        }
        CoreSku coreSku =  coreSkuMapper.getSkuInfoByNo(sku);

        CoreSkuGenerate skuInfoDb = coreSkuGenerateMapper.selectByPrimaryKey(coreSku.getSkuId());

        GoodsStorageConditionVo goodsStorageConditionVo = GoodsStorageConditionUtils.createGoodsStorageConditionVo(skuInfoDb);

        CoreSpu spu = coreSpuMapper.getSpuBySku(sku);

        CoreSpuGenerate generate = coreSpuGenerateMapper.selectByPrimaryKey(spu.getSpuId());

        FirstEngage firstEngage = firstEngageMapper.getFirstEngageInfoBySkuNo(sku);

        SimpleMedicalCategory simpleMedicalCategory = firstEngageMapper.getSimpleMedicalCategory(sku);

        if(simpleMedicalCategory != null && simpleMedicalCategory.getRegistrationNumberId() != null){
            RegistrationNumber registrationNumber = registrationNumberMapper.selectByPrimaryKey(simpleMedicalCategory.getRegistrationNumberId());

            Manufacturer manufacturer = manufacturerMapper.selectByPrimaryKey(registrationNumber.getManufacturerId());

            ProductCompany productCompany = productCompanyMapper.selectByPrimaryKey(registrationNumber.getProductCompanyId());

            // 新国标
            if(null != firstEngage.getNewStandardCategoryId() && firstEngage.getNewStandardCategoryId() > 0){
                List<FirstEngage> firstEngageList = new ArrayList<>();
                firstEngageList.add(firstEngage);
                // 根据新国标分类id查询新国标分类
                List<Map<String, Object>> mapList = standardCategoryMapper.getStandardCategoryStrMap(firstEngageList);
                if(CollectionUtils.isNotEmpty(mapList)){
                    firstEngage.setNewStandardCategoryName(mapList.get(0).get("categoryName").toString());
                }
            }

            // 旧国标
            if(null != firstEngage.getOldStandardCategoryId() && firstEngage.getOldStandardCategoryId() > 0){
                SysOptionDefinition sysOptionDefinition = sysOptionDefinitionMapper.selectByPrimaryKey(firstEngage.getOldStandardCategoryId());
                firstEngage.setOldStandardCategoryName(sysOptionDefinition.getTitle());
            }

            // 转换时间 批准日期
            if(null != registrationNumber.getIssuingDate() && registrationNumber.getIssuingDate() > 0){
                registrationNumber.setIssuingDateStr(DateUtil.convertString(registrationNumber.getIssuingDate(), DateUtil.DATE_FORMAT));
            }
            // 转换时间 有效期至
            if(null != registrationNumber.getEffectiveDate() && registrationNumber.getEffectiveDate() > 0){
                registrationNumber.setEffectiveDateStr(DateUtil.convertString(registrationNumber.getEffectiveDate(), DateUtil.DATE_FORMAT));
            }
            Map<String,Object> paramMap = new HashMap<>();
            // 附件类型
            paramMap.put("attachmentType", CommonConstants.ATTACHMENT_TYPE_974);
            List<Integer> attachmentFunction = new ArrayList<>();
            // 注册证附件/备案凭证附件
            attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_975);
            paramMap.put("attachmentFunction", attachmentFunction);
            paramMap.put("registrationNumberId", registrationNumber.getRegistrationNumberId());
            paramMap.put("firstEngageId", firstEngage.getFirstEngageId());
            // 所有附件
            List<Attachment> attachments = attachmentMapper.getAttachmentsList(paramMap);
            if(CollectionUtils.isNotEmpty(attachments)){
                // 注册证附件/备案凭证附件
                List<Attachment> zczAttachments = new ArrayList<>();
                // 编辑注册证附件信息
                List<Map<String, Object>> zczMapList = new ArrayList<>();
                int size = attachments.size();
                for (int i = 0; i < size; i++) {
                    Attachment attachment = attachments.get(i);
                    // 编辑注册证附件信息
                    Map<String, Object> attachmentMap = new HashMap<>();
                    attachmentMap.put("message", "操作成功");
                    attachmentMap.put("httpUrl", api_http+attachment.getDomain());
                    // uri
                    String uri = attachment.getUri();
                    if(EmptyUtils.isEmpty(uri)){
                        continue;
                    }
                    String[] uriArray = uri.split("/");
                    String fileName = uriArray[uriArray.length-1];
                    String fileNameTemp = "/" + fileName;
                    // 文件后缀
                    String[] prefixArray = fileNameTemp.split("\\.");
                    String prefix = prefixArray[prefixArray.length-1];
                    // 去除路径名
                    String filePath = uri.replaceAll(fileNameTemp, "");
                    attachmentMap.put("fileName", fileName);
                    attachmentMap.put("filePath", uri);
                    attachmentMap.put("prefix", prefix);
                    attachmentMap.put("domain",attachment.getDomain());
                    // 将注册证分组
                    if(CommonConstants.ATTACHMENT_FUNCTION_975.equals(attachment.getAttachmentFunction())){
                        zczAttachments.add(attachment);
                        zczMapList.add(attachmentMap);
                    }
                }
                firstEngage.setZczMapList(zczMapList);
                registrationNumber.setZczAttachments(zczAttachments);
            }
            resultMap.put("registrationNumber",registrationNumber);
            resultMap.put("manufacturer",manufacturer);
            resultMap.put("productCompany",productCompany);
            resultMap.put("zczMapList",new JSONArray(firstEngage.getZczMapList()).toString());
        }
        // 参数
        Map<String, Object> standparamMap = new HashMap<>();
        // 启用状态
        standparamMap.put("status", CommonConstants.STATUS_1);
        // 一级类别
        standparamMap.put("parentId", STANDARD_PARRNT_ID_0);

        // 新国标分类信息
        List<Map<String, Object>> newStandCategoryList = firstEngageService.getNewStandardCategory(standparamMap);

        List<Integer> scopeList = new ArrayList<>();
        // 管理类别作用域
        scopeList.add(CommonConstants.SCOPE_1090);
        // 医疗类别 作用域 1020
        scopeList.add(CommonConstants.SCOPE_1020);
        // 管理类别
        Map<String,List<SysOptionDefinition>> sysOptionMap = sysOptionDefinitionService.getSysOptionDefinitionByParam(scopeList);

        List<SysOptionDefinition> levelList=sysOptionMap.get("1090");
        sysOptionMap.put("1090",levelList.stream().sorted(Comparator.comparing(SysOptionDefinition::getSysOptionDefinitionId)).collect(Collectors.toList()));
        // 旧国标分类解析成json格式
        List<SysOptionDefinition> oldStand = sysOptionMap.get("1020");
        if(EmptyUtils.isEmpty(oldStand)){
            resultMap.put("oldStandCategoryList", null);
        }
        List<Map<String, Object>> oldStandCategoryList = new ArrayList<>();
        int oldSize = oldStand.size();
        // 遍历旧国标分类
        for(int i=0; i < oldSize; i++){
            SysOptionDefinition sysOptionDefinition = oldStand.get(i);
            Map<String, Object> dataMap = new HashMap<>();
            // 旧国标名称
            dataMap.put("label", sysOptionDefinition.getTitle());
            // id
            dataMap.put("value", sysOptionDefinition.getSysOptionDefinitionId());
            oldStandCategoryList.add(dataMap);
        }
        resultMap.put("oldStandCategoryList", new JSONArray(oldStandCategoryList).toString());
        resultMap.put("sysOptionMap", sysOptionMap);
        resultMap.put("newStandCategoryList", new JSONArray(newStandCategoryList).toString());

        resultMap.put("sysOptionMap", sysOptionMap);
        resultMap.put("hasRegistrationCert",simpleMedicalCategory != null);
        resultMap.put("coreSku",coreSku);
        resultMap.put("simpleMedicalCategory",simpleMedicalCategory);
        resultMap.put("spu",generate);
        resultMap.put("firstEngage",firstEngage);
        resultMap.put("goodsStorageConditionVo",goodsStorageConditionVo);

        return resultMap;
    }

    @Override
    public void updateSku(CoreSkuGenerate skuGenerate, GoodsStorageConditionVo goodsStorageConditionVo) {
        GoodsStorageConditionUtils.populateStorageCondition(skuGenerate,goodsStorageConditionVo);
        CoreSkuGenerate update = new CoreSkuGenerate();
        update.setSkuId(skuGenerate.getSkuId());
        update.setStorageConditionOne(skuGenerate.getStorageConditionOne());
        update.setStorageConditionOneLowerValue(skuGenerate.getStorageConditionOneLowerValue());
        update.setStorageConditionOneUpperValue(skuGenerate.getStorageConditionOneUpperValue());
        update.setStorageConditionHumidityLowerValue(skuGenerate.getStorageConditionHumidityLowerValue());
        update.setStorageConditionHumidityUpperValue(skuGenerate.getStorageConditionHumidityUpperValue());
        update.setStorageConditionTwo(skuGenerate.getStorageConditionTwo());
        update.setIsEnableValidityPeriod(skuGenerate.getIsEnableValidityPeriod());
        update.setEffectiveDays(skuGenerate.getEffectiveDays());
        update.setEffectiveDayUnit(skuGenerate.getEffectiveDayUnit());
        update.setNearTermWarnDays(skuGenerate.getNearTermWarnDays());
        update.setOverNearTermWarnDays(skuGenerate.getOverNearTermWarnDays());
        update.setIsFactorySnCode(skuGenerate.getIsFactorySnCode());
        update.setIsManageVedengCode(skuGenerate.getIsManageVedengCode());

        coreSkuGenerateMapper.updateByPrimaryKeySelective(update);
    }

    @Override
    public void updateSpu(CoreSpuGenerate spuGenerate) {
        CoreSpuGenerate update = new CoreSpuGenerate();
        update.setSpuId(spuGenerate.getSpuId());
        update.setMedicalInstrumentCatalogIncluded(spuGenerate.getMedicalInstrumentCatalogIncluded());
        update.setSpuName(spuGenerate.getSpuName());
        update.setSpecsModel(spuGenerate.getSpecsModel());

        coreSpuGenerateMapper.updateByPrimaryKeySelective(update);

    }

    @Override
    public void updateFirstEngage(FirstEngage firstEngage, RegistrationNumber registrationNumber, ProductCompany productCompany, User currentUser) {
        RegistrationNumber updateRegistrationNumber = new RegistrationNumber();
        updateRegistrationNumber.setRegistrationNumberId(registrationNumber.getRegistrationNumberId());
        updateRegistrationNumber.setRegistrationNumber(registrationNumber.getRegistrationNumber());
        updateRegistrationNumber.setManageCategoryLevel(registrationNumber.getManageCategoryLevel());
        updateRegistrationNumber.setProductChineseName(registrationNumber.getProductChineseName());
        updateRegistrationNumber.setModel(registrationNumber.getModel());
        updateRegistrationNumber.setManufacturerId(registrationNumber.getManufacturerId());
        updateRegistrationNumber.setProductionEnterpriseName(registrationNumber.getProductionEnterpriseName());
        updateRegistrationNumber.setProductCompanyLicence(registrationNumber.getProductCompanyLicence());
        if(EmptyUtils.isNotBlank(registrationNumber.getIssuingDateStr())){
            updateRegistrationNumber.setIssuingDate(DateUtil.convertLong(registrationNumber.getIssuingDateStr(), DateUtil.DATE_FORMAT));
        }

        registrationNumberMapper.updateByPrimaryKeySelective(updateRegistrationNumber);

        Manufacturer manufacturer = new Manufacturer();
        manufacturer.setManufacturerId(registrationNumber.getManufacturerId());
        manufacturer.setProductCompanyLicence(registrationNumber.getProductCompanyLicence());
        manufacturerMapper.updateByPrimaryKeySelective(manufacturer);

        FirstEngage updateFirstEngage = new FirstEngage();
        updateFirstEngage.setFirstEngageId(firstEngage.getFirstEngageId());
        updateFirstEngage.setStandardCategoryType(firstEngage.getStandardCategoryType());
        updateFirstEngage.setNewStandardCategoryId(firstEngage.getNewStandardCategoryId());
        updateFirstEngage.setOldStandardCategoryId(firstEngage.getOldStandardCategoryId());
        updateFirstEngage.setUpdater(currentUser.getUserId());

        firstEngageMapper.updateByPrimaryKeySelective(updateFirstEngage);

        ProductCompany updateProductCompany = new ProductCompany();
        updateProductCompany.setProductCompanyId(productCompany.getProductCompanyId());
        updateProductCompany.setProductCompanyChineseName(productCompany.getProductCompanyChineseName());
//        updateProductCompany.setProductCompanyLicence(productCompany.getProductCompanyLicence());

        productCompanyMapper.updateByPrimaryKeySelective(updateProductCompany);

        Map<String,Object> paramMap = new HashMap<>();
        // 附件类型
        paramMap.put("attachmentType", CommonConstants.ATTACHMENT_TYPE_974);
        List<Integer> attachmentFunction = new ArrayList<>();
        // 注册证附件/备案凭证附件
        attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_975);
        paramMap.put("attachmentFunction", attachmentFunction);
        paramMap.put("registrationNumberId", registrationNumber.getRegistrationNumberId());
        paramMap.put("firstEngageId", firstEngage.getFirstEngageId());
        // 所有附件
        List<Attachment> attachments = attachmentMapper.getAttachmentsList(paramMap);
        for (Attachment attachment : attachments) {
            attachmentMapper.deleteByPrimaryKey(attachment.getAttachmentId());
        }

        List<Attachment> zczAttachments = firstEngage.getRegistration().getZczAttachments();
        if(CollectionUtils.isNotEmpty(zczAttachments)){
            for (Attachment attachment : zczAttachments) {
                attachment.setCreator(currentUser.getUserId());
                attachment.setAttachmentType(CommonConstants.ATTACHMENT_TYPE_974);
                attachment.setRelatedId(registrationNumber.getRegistrationNumberId());
                attachmentMapper.insertSelective(attachment);

            }
        }

    }

    @Override
    public List<TodoList> getTodoListByExtraProperty(String extra, String orderNo) {

        return todoListMapper.getTodoListByExtraProperty(extra,orderNo);
    }

    @Override
    public Manufacturer getManufacturerDetail(Integer id) {
        return manufacturerMapper.selectByPrimaryKey(id);
    }

    @Override
    public void deleteByBuzTypeAndBuzExtra(Integer buzType,String buzExtra) {
        if(buzType == null || StringUtil.isEmpty(buzExtra)){
            return;
        }
        todoListMapper.deleteByBuzTypeAndBuzExtra(buzType,buzExtra);
    }


    @Override
    public Integer getCrmTaskCount(Integer userId) {
        return todoListMapper.getCrmTaskCount(userId);
    }

    @Override
    public Integer getCrmTodoTaskCount(Integer userId) {
        return todoListMapper.getCrmTodoTaskCount(userId);
    }
}
