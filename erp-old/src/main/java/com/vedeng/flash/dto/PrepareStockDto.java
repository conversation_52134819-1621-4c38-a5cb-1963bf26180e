package com.vedeng.flash.dto;

import com.vedeng.flash.model.RegularOperateLog;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * @Description:  备货计划数据传输
 * @Author:       Davis
 * @Date:         2021/5/18 下午7:45
 * @Version:      1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PrepareStockDto extends RegularOperateLog {
    /**
     * 定品/备货主键ID
     */
    private Integer regularId;

    /**
     * skuId
     */
    private Integer skuId;

    /**
     * 定品状态 0：启用，1：删除
     */
    private Integer skuStatus;

    /**
     * 安全库存
     */
    private Integer safeStock;
    private Integer safeStockSys;

    /**
     * 调整安全库存
     */
    private Integer originSafeStock;

    /**
     * 安全库存系数
     */
    private Integer safeRatio;

    /**
     * 安全库存配置方式 0：手动输入，1：系统配置
     */
    private Integer deployType;

    /**
     * 是否调整安全库存 0：否，1：是
     */
    private Integer isAdjustStock;

    /**
     * 首次断货时间
     */
    private Long firstOutStockTime;

    /**
     * 预警等级 0：安全，1：二级预警，2：一级预警
     */
    private Integer warnLevel;

    /**
     * 是否备货 0：否，1：是
     */
    private Integer isPrepare;

    /**
     * 最后一次加入定品池时间
     */
    private Long lastAddTime;

    /**
     * 添加人
     */
    private Integer creator;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 更新人
     */
    private Integer updator;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 近三个月销量
     */
    private Integer threeMonthDaysSaleNum;

    /**
     * 预测安全库存系数
     */
    private Integer globalSafeRatio;

    /**
     * 处理类型
     */
    private Integer dealType;

    /**
     * 替换SKU订货号
     */
    private String replaceSkuNo;

    /**
     * 产品助理
     */
    private Integer assignmentAssistantId;
    /**
     * 产品名称
     */
    private String skuName;

    /**
     * 备货主键ID
     */
    private Integer saleorderId;

    /**
     * 合格库可用数量
     */
    private Integer hgStock;

    /**
     * 任务处理人
     */
    private String taskDealer;
}
