package com.vedeng.logistics.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.aftersales.model.BuyOrderAndAfterSalesDto;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.utils.numgenerator.BillNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.dao.GoodsMapper;
import com.vedeng.goods.model.CoreSku;
import com.vedeng.logistics.dao.RExpressWarehouseGoodsOutInMapper;
import com.vedeng.logistics.dao.WarehouseGoodsOutInItemMapper;
import com.vedeng.logistics.dao.WarehouseGoodsOutInMapper;
import com.vedeng.logistics.eums.WarehouseGoodsInEnum;
import com.vedeng.logistics.eums.WarehouseOutInSourceEnum;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.WarehouseGoodsOutIn;
import com.vedeng.logistics.model.WarehouseGoodsOutInItem;
import com.vedeng.logistics.model.dto.RExpressWarehouseGoodsOutInDto;
import com.vedeng.logistics.model.outIn.OutInDetail;
import com.vedeng.logistics.service.WarehouseGoodsInService;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.BuyorderGoods;
import com.vedeng.order.model.Saleorder;
import com.vedeng.orderstream.aftersales.model.AfterSalesDirectInfo;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.dao.StandardCategoryMapper;
import com.vedeng.system.dao.SysOptionDefinitionMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.model.vo.UrlToPdfParam;
import com.vedeng.system.service.OssUtilsService;
import com.vedeng.util.HashUtils;
import com.wms.dao.WmsInOrderMapper;
import com.wms.dao.WmsInOutPersonMapper;
import com.wms.dao.WmsOutputOrderMapper;
import com.wms.dto.WmsInOrderDto;
import com.wms.dto.WmsInOutPersonDto;
import com.wms.model.po.WmsOutputOrder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Service("warehouseGoodsInService")
@Slf4j
public class WarehouseGoodsInServiceImpl implements WarehouseGoodsInService {

	private static final double SCALE = 0.6;

	private static final int LT_LENGTH = 10;
	
	private static final int AT_LENGTH = 16;
	
	private static final String RENDER_URL = "/api/render";
	public static final String str_6840 = "6840体外诊断试剂";

	@Value("${oss_http}")
    private String ossHttp;
	
	@Value("${html2Pdf.domain}")
    private String html2PdfDomain;
	
	//入库单验收报告pdf中图片地址
	@Value("${warehouseReport.imgpath}")
	private String imgPath;

	@Autowired
    private WarehouseGoodsOutInMapper warehouseGoodsOutInMapper;
    
	@Qualifier("buyorderMapper")
    @Autowired
    private BuyorderMapper buyorderMapper;
	
	@Autowired
	private AfterSalesMapper afterSalesMapper;
	
	@Autowired
	private AfterSalesGoodsMapper afterSalesGoodsMapper;
	
	@Autowired
	private BuyorderGoodsMapper buyorderGoodsMapper;
	
	@Autowired
	private CoreSkuMapper coreSkuMapper;
	
	@Autowired
    private OssUtilsService ossUtilsService;
	
	@Autowired
	private GoodsMapper goodsMapper;
	

	@Autowired
	private AttachmentMapper attachmentMapper;
	
	@Autowired
	private WarehouseGoodsOutInItemMapper warehouseGoodsOutInItemMapper;

	@Autowired
	private WmsOutputOrderMapper outputOrderMapper;

	@Autowired
	private SaleorderMapper saleorderMapper;

	@Autowired
	private RExpressWarehouseGoodsOutInMapper rExpressWarehouseGoodsOutInMapper;

	@Autowired
	private WmsInOrderMapper wmsInOrderMapper;

	@Autowired
	@Qualifier("standardCategoryMapper")
	private StandardCategoryMapper standardCategoryMapper;

	@Autowired
	@Qualifier("sysOptionDefinitionMapper")
	private SysOptionDefinitionMapper sysOptionDefinitionMapper;

	@Autowired
	private WmsInOutPersonMapper wmsInOutPersonMapper;




	@Override
    public WarehouseGoodsOutIn insertWarehouseGoodsOutIn(WarehouseGoodsOutIn warehouseGoodsOutIn) {
        //系统自动生成erp入库单号
        String orderNo = warehouseGoodsOutIn.getRelateNo();
        String outInCompany = "";
        //获取收发货方
        //如果是售后单，主表关联单号为售后单号，
		//如果为采购售后单号查询采购单号查询出入库方
        if(warehouseGoodsOutIn.getOutInType().equals(WarehouseGoodsInEnum.BUYORDER_WAREHOUSE_CHANGE_IN.getErpCode())) {
        	AfterSales afterSales = afterSalesMapper.getAfterSalesByNo(warehouseGoodsOutIn.getRelateNo());
        	if(Objects.nonNull(afterSales)) {
        		//采购单号
        		orderNo =afterSales.getOrderNo();
				//根据采购订单表，查询采购订单收发货方
				Buyorder buyorder = buyorderMapper.getBuyOrderByOrderNo(orderNo);
				if(Objects.nonNull(buyorder)) {
					outInCompany = buyorder.getTraderName();
				}
        	}
        }
		//如果是采购入库，并且采购单赠品为是：入库类型改为采购赠品入库
		if(warehouseGoodsOutIn.getOutInType().equals(WarehouseGoodsInEnum.PURCHASE_IN.getErpCode())){
			Buyorder buyorder = buyorderMapper.getBuyOrderByOrderNo(warehouseGoodsOutIn.getRelateNo());
			if(Objects.nonNull(buyorder)) {
				outInCompany = buyorder.getTraderName();
				//是否采购赠品订单0否1是
				Integer isGift = buyorder.getIsGift();
				if(1 == isGift){
					log.info("wms回传入库单生成采购入库记录，变更入库类型为采购赠品入库，采购单号:{},isGift:{}",orderNo,isGift);
					warehouseGoodsOutIn.setOutInType(WarehouseGoodsInEnum.PURCHASE_GIFT_IN.getErpCode());
				}
			}
		}
		//如果为销售售后单号查询销售单号查询出入库方
		if(warehouseGoodsOutIn.getOutInType().equals(WarehouseGoodsInEnum.ORDER_WAREHOUSE_CHANGE_IN.getErpCode())
				||warehouseGoodsOutIn.getOutInType().equals(WarehouseGoodsInEnum.ORDER_WAREHOUSE_BACK_IN.getErpCode())
				) {
			AfterSales afterSales = afterSalesMapper.getAfterSalesByNo(warehouseGoodsOutIn.getRelateNo());
			if(Objects.nonNull(afterSales)) {
				//获取收发货方
				Saleorder saleorder = saleorderMapper.getSaleOrderById(afterSales.getOrderId());
				if(Objects.nonNull(saleorder)){
					outInCompany = saleorder.getTraderName();
				}
			}
		}

        //如果为外借入库，发货发固定值：南京贝登医疗股份有限公司
        if(warehouseGoodsOutIn.getOutInType().equals(WarehouseGoodsInEnum.LENDOUT_WAREHOUSE_IN.getErpCode())){
			outInCompany="南京贝登医疗股份有限公司";
		}

        BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.WAREHOUSE_GOODS_OUT_IN_RK);
        String outInNo = new BillNumGenerator().distribution(billGeneratorBean);
        warehouseGoodsOutIn.setOutInNo(outInNo);
        warehouseGoodsOutIn.setOutInCompany(outInCompany);
        //保存出入库日志主表
        log.info("开始保存出入库日志表outInNo:{},wmsOrderNo:{},relateNo:{}", outInNo, warehouseGoodsOutIn.getWmsNo(), warehouseGoodsOutIn.getRelateNo());
        return saveDb(warehouseGoodsOutIn, () -> warehouseGoodsOutInMapper.insertSelective(warehouseGoodsOutIn));
    }

    /**
     * 保存入库
     *
     * @param t
     * @param supplier
     * @param <T>
     * @return
     */
    private <T> T saveDb(T t, Supplier<Integer> supplier) {
        if (supplier.get() > 0) {
            log.info("保存出入库日志主表成功");
            return t;
        }
        log.info("保存出入库日志主表失败");
        return null;
    }


	/**
	 * 采购直发入库（维护物流信息节点）
	 * @param user ： 用户信息
	 * @param pkId ：  采购订单为 T_BUYORDER 主键buyOrderId 售后订单为 T_AFTER_SALES 主键AFTER_SALES_ID
	 * @param goodsIdAndSendNum ：  byOrderGoodsId 为T_BUYORDER_GOODS中的主键或者为T_AFTER_SALES_GOODS中的主键 ，sendNum 为发货数量
	 * @param erpOutInType
	 */
	@Override
	@Transactional
	public void insertWarehouseGoodsPurchaseDirectOutInDirect(User user, Integer pkId,
															  List<Map<String, Object>> goodsIdAndSendNum, WarehouseGoodsInEnum erpOutInType, Express express) {
		//step1：新增：T_WAREHOUSE_GOODS_OUT_IN 
		//step2：遍历商品信息 新增：T_WAREHOUSE_GOODS_OPERATE_LOG_DIRECT
		WarehouseGoodsOutIn warehouseGoodsOutIn = new WarehouseGoodsOutIn();
		//ERP出入库单号
		BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.WAREHOUSE_GOODS_OUT_IN_RK);
        String outInNo = new BillNumGenerator().distribution(billGeneratorBean);
		warehouseGoodsOutIn.setOutInNo(outInNo);
		//wmsNo直发的给默认值
		warehouseGoodsOutIn.setWmsNo("");
		//根据erpOutInType，配置采购单号或者售后单号,以及出入库方
		String relateNo = "";
		String outInCompany = "";
		if(erpOutInType.getErpCode().equals(WarehouseGoodsInEnum.BUYORDER_WAREHOUSE_CHANGE_IN.getErpCode())
				|| erpOutInType.getErpCode().equals(WarehouseGoodsInEnum.PURCHASE_IN.getErpCode())) {
			log.info("处理采购单信息");
        	//根据采购订单表，查询采购订单收发货方
        	Buyorder buyorder = buyorderMapper.selectByPrimaryKey(pkId);
        	log.info("采购订单表信息：{}",JSON.toJSONString(buyorder));
        	if(Objects.nonNull(buyorder)) {
        		relateNo = buyorder.getBuyorderNo();
        		outInCompany = buyorder.getTraderName();
        	}
		}else {
			log.info("处理售后单信息");
			AfterSales afterSales = afterSalesMapper.selectByPrimaryKey(pkId);
			log.info("售后订单表信息：{}",JSON.toJSONString(afterSales));
			if(Objects.nonNull(afterSales)) {
				relateNo =afterSales.getAfterSalesNo();
				//获取收发货方
				Buyorder buyorder = buyorderMapper.getBuyOrderByOrderNo(afterSales.getOrderNo());
				if(Objects.nonNull(buyorder)) {
					outInCompany = buyorder.getTraderName();
				}
			}
		}
		warehouseGoodsOutIn.setRelateNo(relateNo);
		warehouseGoodsOutIn.setOutInType(erpOutInType.getErpCode());
		warehouseGoodsOutIn.setOutInCompany(outInCompany);
		Date currentDate = new Date();
		warehouseGoodsOutIn.setOutInTime(currentDate);
		warehouseGoodsOutIn.setSource(WarehouseOutInSourceEnum.DIRECT_DELIVERY.getSource());
		warehouseGoodsOutIn.setAddTime(currentDate);
		//维护物流信息的人
		if(Objects.nonNull(user)) {
			warehouseGoodsOutIn.setCreator(user.getUserId());
			warehouseGoodsOutIn.setCreatorName(user.getUsername());
		}
		log.info("直发采购或者采购售后换货入库日志主表插入数据：{}",JSON.toJSONString(warehouseGoodsOutIn));
		int success = warehouseGoodsOutInMapper.insertSelective(warehouseGoodsOutIn);
		if(success>0) {
			RExpressWarehouseGoodsOutInDto rExpressWarehouseGoodsOutInDto = new RExpressWarehouseGoodsOutInDto();
			rExpressWarehouseGoodsOutInDto.setWarehouseGoodsOutInId(warehouseGoodsOutIn.getWarehouseGoodsOutInId().intValue());
			rExpressWarehouseGoodsOutInDto.setExpressId(express.getExpressId());
			CurrentUser currentUser = CurrentUser.getCurrentUser();
			rExpressWarehouseGoodsOutInDto.setCreator(currentUser.getId());
			rExpressWarehouseGoodsOutInDto.setCreatorName(currentUser.getUsername());
			rExpressWarehouseGoodsOutInDto.setUpdater(currentUser.getId());
			rExpressWarehouseGoodsOutInDto.setUpdaterName(currentUser.getUsername());
			rExpressWarehouseGoodsOutInMapper.insertSelective(rExpressWarehouseGoodsOutInDto);
			//异步生成入库单验收报告
			List<WarehouseGoodsOutInItem> warehouseGoodsOutInItemList = new ArrayList<>();
			for (Map<String, Object> map : goodsIdAndSendNum) {
				Integer sendNum = (Integer) map.get("sendNum");
				//插入明细表T_WAREHOUSE_GOODS_OPERATE_LOG_DIRECT
				WarehouseGoodsOutInItem warehouseGoodsOutInItem = new WarehouseGoodsOutInItem();
				warehouseGoodsOutInItem.setCompanyId(1);
				warehouseGoodsOutInItem.setOperateType(erpOutInType.getErpCode());
				//relatedId 关联采购、销售、售后产品ID  表T_BUYORDER_GOODS 字段BUYORDER_GOODS_ID  表T_AFTER_SALES_GOODS字段GOODS_ID
				Integer relatedId = 0;
				Integer goodsId = 0;
				if(erpOutInType.getErpCode().equals(WarehouseGoodsInEnum.BUYORDER_WAREHOUSE_CHANGE_IN.getErpCode())
						|| erpOutInType.getErpCode().equals(WarehouseGoodsInEnum.PURCHASE_IN.getErpCode())) {
					relatedId = (Integer) map.get("byOrderGoodsId");
					BuyorderGoods byOrderGoods = buyorderGoodsMapper.selectbGoodsKey(relatedId);
					if(Objects.nonNull(byOrderGoods)) {
						goodsId = byOrderGoods.getGoodsId();
					}
				}else {
					AfterSalesGoods afterSalesGoods = afterSalesGoodsMapper.selectByPrimaryKey((Integer) map.get("byOrderGoodsId"));
					if(Objects.nonNull(afterSalesGoods)) {
						relatedId = afterSalesGoods.getGoodsId();
						goodsId = relatedId;
					}
				}
				warehouseGoodsOutInItem.setRelatedId(relatedId);
				warehouseGoodsOutInItem.setGoodsId(goodsId);
				warehouseGoodsOutInItem.setCheckStatusTime(DateUtil.formatDateTime(currentDate));
				warehouseGoodsOutInItem.setAddTime(currentDate);
				//维护物流信息的人
				if(Objects.nonNull(user)) {
					warehouseGoodsOutInItem.setCreator(user.getUserId());
				}
				CoreSku coreSku = coreSkuMapper.selectByPrimaryKey(goodsId);
				String skuNo = "";
				//sn码 若商品“是否厂家赋SN码”为“是，一物一sn码，厂商批次号为空
				//sn码 若商品“是否厂家赋SN码”为“否，sn码为空，共一个厂商批次号
				//sn码生成规则：AT+16位唯一码(基于T_WAREHOUSE_GOODS_OPERATE_LOG_DIRECT主键生成) 此处sn码为空，生成成功后更新sn码
				String sn = "";
				boolean getSn = false;
				if(Objects.nonNull(coreSku)) {
					//ERP出入库单号
					skuNo = coreSku.getSkuNo();
					if(Boolean.TRUE.equals(coreSku.getIsFactorySnCode())) {
						getSn = true;
					}
				}
				//厂商批次号、贝登批次码 默认为空
				String batchNumber = "";
				warehouseGoodsOutInItem.setLogType(0);
				warehouseGoodsOutInItem.setBarcodeFactory(sn);
				warehouseGoodsOutInItem.setOutInNo(outInNo);
				//是否厂家赋SN码”为“是，一物一sn码，厂商批次号为空
				if(getSn){
					for (int size = 0; size < sendNum; size++ ){
						warehouseGoodsOutInItem.setNum(new BigDecimal(1));
						warehouseGoodsOutInItem.setBatchNumber(batchNumber);
						warehouseGoodsOutInItem.setVedengBatchNumber(batchNumber);
						log.info("直发采购或者采购售后换货入库日志明细表插入数据：{}",JSON.toJSONString(warehouseGoodsOutInItem));
						warehouseGoodsOutInItemMapper.insertSelective(warehouseGoodsOutInItem);
						log.info("更新批次码");
						WarehouseGoodsOutInItem warehouseGoodsOutInItemUpdate = new WarehouseGoodsOutInItem();
						warehouseGoodsOutInItemUpdate.setWarehouseGoodsOutInDetailId(warehouseGoodsOutInItem.getWarehouseGoodsOutInDetailId());
						//AT+16位唯一码(基于T_WAREHOUSE_GOODS_OPERATE_LOG_DIRECT主键生成)
						sn = "AT"+HashUtils.md5ToHexString(String.valueOf(warehouseGoodsOutInItem.getWarehouseGoodsOutInDetailId()),AT_LENGTH);
						log.info("SN码：{}",sn);
						warehouseGoodsOutInItemUpdate.setBarcodeFactory(sn);
						log.info("直发采购或者采购售后换货入库日志明细表更新数据：{}",JSON.toJSONString(warehouseGoodsOutInItemUpdate));
						warehouseGoodsOutInItemMapper.updateByPrimaryKeySelective(warehouseGoodsOutInItemUpdate);
						warehouseGoodsOutInItemList.add(warehouseGoodsOutInItem);
					}
				}
				//是否厂家赋SN码”为“否，sn码为空，共一个厂商批次号
				else{
					//厂商批次号、贝登批次码 LT+(订单号+sku订货号+出库时间)维度生成的12位码
					String dateStr = DateUtil.format(currentDate, DatePattern.PURE_DATE_PATTERN);
					batchNumber = "LT"+HashUtils.md5ToHexString(relateNo+skuNo+dateStr,LT_LENGTH);
					log.info("厂商批号、贝登批次码：{}",batchNumber);
					warehouseGoodsOutInItem.setBatchNumber(batchNumber);
					warehouseGoodsOutInItem.setVedengBatchNumber(batchNumber);
					warehouseGoodsOutInItem.setNum(new BigDecimal(sendNum));
					log.info("直发采购或者采购售后换货入库日志明细表插入数据：{}",JSON.toJSONString(warehouseGoodsOutInItem));
					warehouseGoodsOutInItemMapper.insertSelective(warehouseGoodsOutInItem);
					warehouseGoodsOutInItemList.add(warehouseGoodsOutInItem);
				}

			}
			//异步生成入库单验收报告
			new Thread(()->{
				String uuid = UUID.randomUUID().toString().replace("-", "");
				log.info("开始执行createWarehouseGoodsInReport,uuid:{}",uuid);
				createWarehouseGoodsInReport(warehouseGoodsOutIn,warehouseGoodsOutInItemList);
				log.info("结束执行createWarehouseGoodsInReport，uuid:{}",uuid);
			} ).start();
		}
	}

	/**
	 * 采购售后直发换货入库
	 * @param user
	 * @param pkId
	 * @param warehouseGoodsOutInItemParamsList
	 * @param erpOutInType
	 */
	@Override
	@Transactional
	public void insertWarehouseGoodsPurchaseOutIn(User user,Integer pkId,
			List<WarehouseGoodsOutInItem> warehouseGoodsOutInItemParamsList, WarehouseGoodsInEnum erpOutInType) {
		//step1：新增：T_WAREHOUSE_GOODS_OUT_IN 
		//step2：遍历商品信息 新增：T_WAREHOUSE_GOODS_OPERATE_LOG_DIRECT
		WarehouseGoodsOutIn warehouseGoodsOutIn = new WarehouseGoodsOutIn();
		//ERP出入库单号
		BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.WAREHOUSE_GOODS_OUT_IN_RK);
        String outInNo = new BillNumGenerator().distribution(billGeneratorBean);
		warehouseGoodsOutIn.setOutInNo(outInNo);
		//wmsNo直发的给默认值
		warehouseGoodsOutIn.setWmsNo("");
		//根据erpOutInType，配置采购单号或者售后单号,以及出入库方
		String relateNo = "";
		String outInCompany = "";
		if(erpOutInType.getErpCode().equals(WarehouseGoodsInEnum.PURCHASE_IN.getErpCode())) {
			log.info("处理采购单信息");
        	//根据采购订单表，查询采购订单收发货方
        	Buyorder buyorder = buyorderMapper.selectByPrimaryKey(pkId);
        	log.info("采购订单表信息：{}",JSON.toJSONString(buyorder));
        	if(Objects.nonNull(buyorder)) {
        		relateNo = buyorder.getBuyorderNo();
        		outInCompany = buyorder.getTraderName();
        	}
		}else {
			log.info("处理售后单信息");
			AfterSales afterSales = afterSalesMapper.selectByPrimaryKey(pkId);
			log.info("售后订单表信息：{}",JSON.toJSONString(afterSales));
			if(Objects.nonNull(afterSales)) {
				relateNo =afterSales.getAfterSalesNo();
				//获取收发货方
				Buyorder buyorder = buyorderMapper.getBuyOrderByOrderNo(afterSales.getOrderNo());
				if(Objects.nonNull(buyorder)) {
					outInCompany = buyorder.getTraderName();
				}
			}
		}
		warehouseGoodsOutIn.setRelateNo(relateNo);
		warehouseGoodsOutIn.setOutInType(erpOutInType.getErpCode());
		warehouseGoodsOutIn.setOutInCompany(outInCompany);
		Date currentDate = new Date();
		warehouseGoodsOutIn.setOutInTime(currentDate);
		warehouseGoodsOutIn.setSource(WarehouseOutInSourceEnum.DIRECT_DELIVERY.getSource());
		warehouseGoodsOutIn.setAddTime(currentDate);
		//维护物流信息的人
		if(Objects.nonNull(user)) {
			warehouseGoodsOutIn.setCreator(user.getUserId());
			warehouseGoodsOutIn.setCreatorName(user.getUsername());
		}
		log.info("直发采购或者采购售后换货入库日志主表插入数据：{}",JSON.toJSONString(warehouseGoodsOutIn));
		int success = warehouseGoodsOutInMapper.insertSelective(warehouseGoodsOutIn);
		if(success>0) {
			List<WarehouseGoodsOutInItem> warehouseGoodsOutInItemList = new ArrayList<>();
			for (WarehouseGoodsOutInItem warehouseGoodsOutInItemParams : warehouseGoodsOutInItemParamsList) {
				//插入明细表T_WAREHOUSE_GOODS_OPERATE_LOG_DIRECT
				Integer goodsId = warehouseGoodsOutInItemParams.getGoodsId();
				WarehouseGoodsOutInItem warehouseGoodsOutInItem = new WarehouseGoodsOutInItem();
				warehouseGoodsOutInItem.setCompanyId(1);
				warehouseGoodsOutInItem.setOperateType(erpOutInType.getErpCode());
				warehouseGoodsOutInItem.setRelatedId(warehouseGoodsOutInItemParams.getRelatedId());
				warehouseGoodsOutInItem.setGoodsId(goodsId);
				warehouseGoodsOutInItem.setCheckStatusTime(DateUtil.formatDateTime(currentDate));
				warehouseGoodsOutInItem.setAddTime(currentDate);
				//维护物流信息的人
				if(Objects.nonNull(user)) {
					warehouseGoodsOutInItem.setCreator(user.getUserId());
				}
				CoreSku coreSku = coreSkuMapper.selectByPrimaryKey(goodsId);
				//sn码 若商品“是否厂家赋SN码”为“是，一物一sn码，厂商批次号作为sn码
				//sn码 若商品“是否厂家赋SN码”为“否，sn码为空，共一个厂商批次号
				//sn码生成规则：AT+16位唯一码(基于T_WAREHOUSE_GOODS_OPERATE_LOG_DIRECT主键生成) 此处sn码为空，生成成功后更新sn码
				String sn = "";
				boolean getSn = false;
				if(Objects.nonNull(coreSku) && Boolean.TRUE.equals(coreSku.getIsFactorySnCode())) {
					getSn = true;
				}
				warehouseGoodsOutInItem.setProductDate(warehouseGoodsOutInItemParams.getProductDate());
				warehouseGoodsOutInItem.setExpirationDate(warehouseGoodsOutInItemParams.getExpirationDate());
				warehouseGoodsOutInItem.setCheckStatusTime(warehouseGoodsOutInItemParams.getCheckStatusTime());
				warehouseGoodsOutInItem.setSterilizationBatchNumber(warehouseGoodsOutInItemParams.getSterilizationBatchNumber());
				warehouseGoodsOutInItem.setLogType(0);
				warehouseGoodsOutInItem.setBarcodeFactory(sn);
				warehouseGoodsOutInItem.setOutInNo(outInNo);
				//设置保持页面显示同步，不设置为空
				warehouseGoodsOutInItem.setBatchNumber(warehouseGoodsOutInItemParams.getBatchNumber());
				warehouseGoodsOutInItem.setVedengBatchNumber(warehouseGoodsOutInItemParams.getVedengBatchNumber());
				//“是否厂家赋SN码”为“是，一物一sn码，厂商批次号作为sn码
				if(getSn){
					for (int size =0; size < warehouseGoodsOutInItemParams.getNum().intValue();size++) {
						warehouseGoodsOutInItem.setNum(new BigDecimal(1));
						//厂商批号、贝登批次码 LT+(订单号+sku订货号+出库时间)维度生成的12位码
						warehouseGoodsOutInItem.setBarcodeFactory(warehouseGoodsOutInItemParams.getBatchNumber());
						log.info("直发采购或者采购售后换货入库日志明细表插入数据：{}",JSON.toJSONString(warehouseGoodsOutInItem));
						warehouseGoodsOutInItemMapper.insertSelective(warehouseGoodsOutInItem);
						//异步生成入库单验收报告
						log.info("直发采购生成验收报告单");
						warehouseGoodsOutInItemList.add(warehouseGoodsOutInItem);

					}
				}
				//“是否厂家赋SN码”为“否，sn码为空，共一个厂商批次号
				else{
					warehouseGoodsOutInItem.setNum(warehouseGoodsOutInItemParams.getNum());
					log.info("直发采购或者采购售后换货入库日志明细表插入数据：{}",JSON.toJSONString(warehouseGoodsOutInItem));
					warehouseGoodsOutInItemMapper.insertSelective(warehouseGoodsOutInItem);
					//异步生成入库单验收报告
					log.info("直发采购生成验收报告单");
					warehouseGoodsOutInItemList.add(warehouseGoodsOutInItem);
				}
			}
			new Thread(()-> {
				String uuid = UUID.randomUUID().toString().replace("-", "");
				log.info("开始执行createWarehouseGoodsInReport，uuid：{}",uuid);
				createWarehouseGoodsInReport(warehouseGoodsOutIn,warehouseGoodsOutInItemList);
				log.info("结束执行createWarehouseGoodsInReport，uuid：{}",uuid);
			}).start();
		}
	}

	@Override
	public void regenerateWarehouseInReport(Long warehouseGoodsOutInId) {
		WarehouseGoodsOutIn warehouseGoodsOutIn = warehouseGoodsOutInMapper.selectByPrimaryKey(warehouseGoodsOutInId);
		if (Objects.isNull(warehouseGoodsOutIn)) {
			log.info("regenerateWarehouseInReport：入库单未查询到 warehouseGoodsOutInId:{}",warehouseGoodsOutInId);
			return;
		}
		WarehouseGoodsOutInItem warehouseGoodsOutInItem = new WarehouseGoodsOutInItem();
		warehouseGoodsOutInItem.setOutInNo(warehouseGoodsOutIn.getOutInNo());
		warehouseGoodsOutInItem.setIsDelete(0);
		List<WarehouseGoodsOutInItem> warehouseGoodsOutInItemList = warehouseGoodsOutInItemMapper.findByAll(warehouseGoodsOutInItem);

		//新增验收报告附件
		Attachment attachment = new Attachment();
		attachment.setAttachmentType(ErpConst.WAREHOUSER_ATTACHMET_TYPE);
		attachment.setAttachmentFunction(ErpConst.WAREHOUSER_ATTACHMET_FUNCTION);
		attachment.setRelatedId(Integer.parseInt(String.valueOf(warehouseGoodsOutIn.getWarehouseGoodsOutInId())));
		log.info("regenerateWarehouseInReport 删除历史复核单：{}",JSON.toJSONString(attachment));
		attachmentMapper.delAttachment(attachment);

		createWarehouseGoodsInReport(warehouseGoodsOutIn, warehouseGoodsOutInItemList);
	}

	@Override
	public void createWarehouseGoodsInReport(WarehouseGoodsOutIn warehouse,List<WarehouseGoodsOutInItem> warehouseGoodsOutInItemList) {
		log.info("入库单主信息：{}，入库单明细信息：{}",warehouse,JSON.toJSONString(warehouseGoodsOutInItemList));
		//只生成普发采购入库和普发采购换货入库的验收报告
		Integer outInType = warehouse.getOutInType();
		if(CollectionUtils.isEmpty(warehouseGoodsOutInItemList)) {
			return;
		}
		//拼接HTML文档
		StringBuilder sb = new StringBuilder();
		boolean isOne = outInType.equals(WarehouseGoodsInEnum.PURCHASE_IN.getErpCode()) ||
				outInType.equals(WarehouseGoodsInEnum.BUYORDER_WAREHOUSE_CHANGE_IN.getErpCode()) ||
				outInType.equals(WarehouseGoodsInEnum.ORDER_WAREHOUSE_CHANGE_IN.getErpCode()) ||
				outInType.equals(WarehouseGoodsInEnum.ORDER_WAREHOUSE_BACK_IN.getErpCode()) ||
				outInType.equals(WarehouseGoodsInEnum.PURCHASE_GIFT_IN.getErpCode());


		if (isOne) {
			templateOneIn(warehouse, warehouseGoodsOutInItemList, outInType, sb);
		} else {
			templateOtherIn(warehouse, warehouseGoodsOutInItemList, outInType, sb);
		}
		log.info("create html in param:{}",sb);
    	try {
    		htmlStrToPdfGetFile(sb.toString(),warehouse);
		} catch (Exception e) {
			log.error("转换PDF失败...",e);
		}
	}

	/**
	 * 非其他入库单单的模板
	 * @param warehouse
	 * @param warehouseGoodsOutInItemList
	 * @param outInType
	 * @param sb
	 */
	private void templateOneIn(WarehouseGoodsOutIn warehouse, List<WarehouseGoodsOutInItem> warehouseGoodsOutInItemList, Integer outInType, StringBuilder sb) {
		//替换：验收报告编号（outInNo）、关联订单号(relateNo)、供应商名称(outInCompany)
		String preFix = "<html><head><title>入库验收报告</title><meta http-equiv=\"X-UA-Compatible\"content=\"IE=edge\"/><meta http-equiv=\"content-type\"content=\"text/html; charset=utf-8\"/></head><style>.ax_default{font-family:'Arial Normal','Arial',sans-serif;font-weight:400;font-style:normal;font-size:13px;letter-spacing:normal;color:#333333;vertical-align:none;text-align:center;line-height:normal;text-transform:none;margin-left:180px}.H2{font-family:'Arial Normal','Arial',sans-serif;font-weight:bold;font-style:normal;font-size:24px;text-align:left;margin-left:680px}.label{font-size:14px;text-align:left}.imgs{font-size:14px;text-align:right}body{margin:0px;background-image:none;position:relative;left:-176px;width:1371px;margin-left:auto;margin-right:auto;text-align:left}#base{position:absolute;z-index:0}td{text-align-last:center;border:solid 1px#000}th{border:solid 1px#000}table{border-spacing:0;border-collapse:collapse}</style><body><div id=\"base\"class=\"\"><!--Unnamed(矩形)--><div id=\"u0\"class=\"ax_default H2\"><div id=\"u0_text\"class=\"text \"><p><span>入库验收报告</span></p></div></div><!--Unnamed(矩形)--><div id=\"u1\"class=\"ax_default label\"><div id=\"u0_text\"class=\"text \"><p><span >单据类型：outInType</span><span style=\"margin-left:60px\">入库单编号：outInNo</span><span style=\"margin-left:60px\">关联订单号：relateNo</span><span style=\"margin-left:60px\">供应商/客户名称：outInCompany</span></p></div></div><!--Unnamed(表格)--><div id=\"u3\"class=\"ax_default\"><table><thead><tr><th rowspan=2 style=\"width:50px;height:30px\">序号</th><th rowspan=2 style=\"width:20%\">产品名称</th><th rowspan=2 style=\"width:80px\">订货号</th><th rowspan=2 style=\"width:5%\">规格型号</th><th rowspan=2 style=\"width:80px\">单位</th><th rowspan=2 style=\"width:15%\">注册证编号</th><th rowspan=2 style=\"width:80px\">收货数量</th><th rowspan=2 style=\"width:80px\">验收数量</th><th rowspan=2 style=\"width:80px\">验收结果</th><th colspan=5 style=\"width:80px;height:35px\">验收项目</th><th rowspan=2 style=\"width:80px\">入库状态</th><tr><th style=\"width:80px\">外观</th><th style=\"width:80px\">包装</th><th style=\"width:80px\">标签</th><th style=\"width:80px\">合格证明文件</th><th style=\"width:80px\">效期</th></tr></tr></thead><tbody id=\"effect\">";
		String typeByCode = WarehouseGoodsInEnum.getTypeByCode(outInType);
		sb.append(preFix.replace("outInNo", warehouse.getOutInNo()).replace("relateNo", warehouse.getRelateNo()).replace("outInCompany", warehouse.getOutInCompany()).replace("outInType", typeByCode));


		//替换：验收报告明细 序号（pageIndex）、产品名称（goodsName）、订货号（skuNo）、规格型号（goodsModel）、注册证编号（registerNo）、收货数量（receiveNum）、验收数量（checkNum）
		//验收结果（checkResult）、入库状态（goodsStorageStatus）、外观（goodsFace）、包装（goodsPackage）、标签（goodsSign）、合格证明文件（certificateFile）、效期（goodsPeriod）
		boolean has6840 = false;
		for (int i = 0; i < warehouseGoodsOutInItemList.size(); i++) {
			WarehouseGoodsOutInItem warehouseGoodsOutInItem = warehouseGoodsOutInItemList.get(i);
			//如果为采购入库
			OutInDetail skuInfo = new OutInDetail();
			Integer skuId =  warehouseGoodsOutInItem.getGoodsId();
			querySkuInfo(skuInfo, skuId);
			boolean is6840 = skuInfo.isIs6840();
			if (is6840) {
				has6840 = true;
			}
			StringBuilder model = new StringBuilder();
			if (StrUtil.isNotEmpty(skuInfo.getSpec())) {
				model.append(skuInfo.getSpec());
			}
			if (StrUtil.isNotEmpty(skuInfo.getSpec())&&StrUtil.isNotEmpty(skuInfo.getModel())) {
				model.append("/");
			}
			model.append(skuInfo.getModel());
			String middleFix = "<tr><td>pageIndex</td><td>goodsName</td><td>skuNo</td><td>goodsModel</td><td>unit</td><td>registerNo</td><td>receiveNum</td><td>checkNum</td><td>checkResult</td><td>goodsFace</td><td>goodsPackage</td><td>goodsSign</td><td>certificateFile</td><td>goodsPeriod</td><td>goodsStorageStatus</td></tr>";
			sb.append(middleFix.replace("pageIndex", String.valueOf(i+1)).replace("goodsName", skuInfo.getSkuName())
					.replace("skuNo", skuInfo.getSkuNo())
					.replace("goodsModel", model.toString()).replace("unit",skuInfo.getUnitName())
					.replace("registerNo", skuInfo.getRegistrationNumber()).replace("receiveNum", String.valueOf(warehouseGoodsOutInItem.getNum())).replace("checkNum", String.valueOf(warehouseGoodsOutInItem.getNum()))
					.replace("checkResult", "合格").replace("goodsStorageStatus", "已入库").replace("goodsFace", "合格").replace("goodsPackage", "合格").replace("goodsSign", "合格").replace("certificateFile", "合格")
					.replace("goodsPeriod", "合格"));
		}

		String surFix = "</tbody></table></div><!--Unnamed(矩形)--><div id=\"u43\"class=\"ax_default label\"><div id=\"u0_text\"class=\"text \"><p><span >收货员：receiver</span><span style=\"margin-left:300px\">验收员：checker</span><span style=\"margin-left:300px\">验收入库时间：checkTime</span></p></div></div><div class=\"ax_default imgs\"><div><span style=\"color: red;border: 3px solid red;font-size: 25px;padding: 10px\">入库验收完成</span></div></div></div></body></html>";
		//获取收货员 验收员
		WmsInOrderDto data = new WmsInOrderDto();
		getConsigneeAndInspector(warehouse, has6840, data);

		//替换：收货员（receiver）、验收员（checker）、验收入库时间（checkTime）
		sb.append(surFix.replace("receiver", StrUtil.isEmpty(data.getConsignee())?"":data.getConsignee())
				.replace("checker", StrUtil.isEmpty(data.getInspector())?"":data.getInspector())
				.replace("checkTime", DateUtil.formatDate(warehouse.getOutInTime())));
	}

	private void getConsigneeAndInspector(WarehouseGoodsOutIn warehouse, boolean has6840, WmsInOrderDto data) {
		if (StrUtil.isNotEmpty(warehouse.getWmsNo())) {
			List<WmsInOrderDto> wmsInOrderDtos = wmsInOrderMapper.selectByWmsNo(warehouse.getWmsNo());
			if (CollUtil.isNotEmpty(wmsInOrderDtos)) {
				data.setConsignee(StrUtil.isEmpty(wmsInOrderDtos.get(0).getConsignee())?"":wmsInOrderDtos.get(0).getConsignee());
				data.setInspector(StrUtil.isEmpty(wmsInOrderDtos.get(0).getInspector())?"":wmsInOrderDtos.get(0).getInspector());
			}
		}
		if (StrUtil.isEmpty(data.getConsignee())) {
			DateTime dateTime = DateUtil.parseDate("2023-11-28");
			DateTime outInDate = DateUtil.parseDate(DateUtil.formatDateTime(warehouse.getOutInTime()));
			String s = DateUtil.formatDate(outInDate);
			int compare = DateUtil.compare(dateTime, outInDate);
			if (compare<0) {

				List<WmsInOutPersonDto> wmsInOutPersonDtos = wmsInOutPersonMapper.selectByType(1001);
				if (CollUtil.isNotEmpty(wmsInOutPersonDtos)) {
					data.setConsignee(StrUtil.isEmpty(wmsInOutPersonDtos.get((int) (Math.random() * wmsInOutPersonDtos.size())).getName())?"":wmsInOutPersonDtos.get((int) (Math.random() * wmsInOutPersonDtos.size())).getName());
				}
			} else {
				List<WmsInOutPersonDto> wmsInOutPersonDtos = wmsInOutPersonMapper.selectByTypeAndDate(1, s);
				if (CollUtil.isNotEmpty(wmsInOutPersonDtos)) {
					data.setConsignee(StrUtil.isEmpty(wmsInOutPersonDtos.get((int) (Math.random() * wmsInOutPersonDtos.size())).getName())?"":wmsInOutPersonDtos.get((int) (Math.random() * wmsInOutPersonDtos.size())).getName());
				}


			}
		}

		if (StrUtil.isEmpty(data.getInspector())) {
			if (!has6840) {
				DateTime dateTime = DateUtil.parseDate("2023-11-28");
				DateTime outInDate = DateUtil.parseDate(DateUtil.formatDateTime(warehouse.getOutInTime()));
				String s = DateUtil.formatDate(outInDate);
				int compare = DateUtil.compare(dateTime, outInDate);
				if (compare<0) {
					List<WmsInOutPersonDto> wmsInOutPersonDtos = wmsInOutPersonMapper.selectByType(1002);
					if (CollUtil.isNotEmpty(wmsInOutPersonDtos)) {
						data.setInspector(StrUtil.isEmpty(wmsInOutPersonDtos.get((int) (Math.random() * wmsInOutPersonDtos.size())).getName())?"":wmsInOutPersonDtos.get((int) (Math.random() * wmsInOutPersonDtos.size())).getName());
					}

				} else {
					List<WmsInOutPersonDto> wmsInOutPersonDtos = wmsInOutPersonMapper.selectByTypeAndDate(2, s);
					if (CollUtil.isNotEmpty(wmsInOutPersonDtos)) {
						data.setInspector(StrUtil.isEmpty(wmsInOutPersonDtos.get((int) (Math.random() * wmsInOutPersonDtos.size())).getName())?"":wmsInOutPersonDtos.get((int) (Math.random() * wmsInOutPersonDtos.size())).getName());
					}

				}
			} else {
				data.setInspector("桑阿敏");
			}

		}
	}

	/**
	 * 其他如入库单模板
	 * @param warehouse
	 * @param warehouseGoodsOutInItemList
	 * @param outInType
	 * @param sb
	 */
	private void templateOtherIn(WarehouseGoodsOutIn warehouse, List<WarehouseGoodsOutInItem> warehouseGoodsOutInItemList, Integer outInType, StringBuilder sb) {
		//替换：验收报告编号（outInNo）、关联订单号(relateNo)、供应商名称(outInCompany)
		String preFix = "<html><head><title>入库报告</title><meta http-equiv=\"X-UA-Compatible\"content=\"IE=edge\"/><meta http-equiv=\"content-type\"content=\"text/html; charset=utf-8\"/></head><style>.ax_default{font-family:'Arial Normal','Arial',sans-serif;font-weight:400;font-style:normal;font-size:13px;letter-spacing:normal;color:#333333;vertical-align:none;text-align:center;line-height:normal;text-transform:none;margin-left:180px}.H2{font-family:'Arial Normal','Arial',sans-serif;font-weight:bold;font-style:normal;font-size:24px;text-align:left;margin-left:680px}.label{font-size:14px;text-align:left}.imgs{font-size:14px;text-align:right}body{margin:0px;background-image:none;position:relative;left:-176px;width:1371px;margin-left:auto;margin-right:auto;text-align:left}#base{position:absolute;z-index:0}td{text-align-last:center;border:solid 1px#000}th{border:solid 1px#000}table{border-spacing:0;border-collapse:collapse}</style><body><div id=\"base\"class=\"\"><!--Unnamed(矩形)--><div id=\"u0\"class=\"ax_default H2\"><div id=\"u0_text\"class=\"text \"><p><span>入库报告</span></p></div></div><!--Unnamed(矩形)--><div id=\"u1\"class=\"ax_default label\"><div id=\"u0_text\"class=\"text \"><p><span>单据类型：outInType</span><span style=\"margin-left:60px\">入库单编号：outInNo</span><span style=\"margin-left:60px\">关联订单号：relateNo</span><span style=\"margin-left:60px\">供应商/客户名称：outInCompany</span></p></div></div><!--Unnamed(表格)--><div id=\"u3\"class=\"ax_default\"><table><thead><tr><th rowspan=2 style=\"width:50px;height:30px\">序号</th><th rowspan=2 style=\"width:20%\">产品名称</th><th rowspan=2 style=\"width:100px\">订货号</th><th rowspan=2 style=\"width:100px\">规格型号</th><th rowspan=2 style=\"width:80px\">单位</th><th rowspan=2 style=\"width:20%\">注册证编号</th><th rowspan=2 style=\"width:120px\">入库数量</th><th rowspan=2 style=\"width:150px\">入库状态</th></tr></thead><tbody id=\"effect\">";
		String outInCompany = "/";
		if (outInType.equals(WarehouseGoodsInEnum.LENDOUT_WAREHOUSE_IN.getErpCode()) ) {
			WmsOutputOrder outputOrder = outputOrderMapper.selectByOrderNo(warehouse.getRelateNo());
			if (Objects.nonNull(outputOrder)) {
				outInCompany = outputOrder.getBorrowTraderName();
			}
		}
		String typeByCode = WarehouseGoodsInEnum.getTypeByCode(outInType);
		sb.append(preFix.replace("outInType", typeByCode).replace("outInNo", warehouse.getOutInNo()).replace("relateNo", warehouse.getRelateNo()).replace("outInCompany", outInCompany));
		boolean has6840 = false;
		if(CollectionUtils.isNotEmpty(warehouseGoodsOutInItemList)) {
			for (int i = 0; i < warehouseGoodsOutInItemList.size(); i++) {
				WarehouseGoodsOutInItem warehouseGoodsOutInItem = warehouseGoodsOutInItemList.get(i);
				OutInDetail skuInfo = new OutInDetail();
				Integer skuId =  warehouseGoodsOutInItem.getGoodsId();
				querySkuInfo(skuInfo, skuId);
				boolean is6840 = skuInfo.isIs6840();
				if (is6840) {
					has6840 = true;
				}
				StringBuilder model = new StringBuilder();
				if (StrUtil.isNotEmpty(skuInfo.getSpec())) {
					model.append(skuInfo.getSpec());
				}
				if (StrUtil.isNotEmpty(skuInfo.getSpec())&&StrUtil.isNotEmpty(skuInfo.getModel())) {
					model.append("/");
				}
				model.append(skuInfo.getModel());
				String middleFix = "<tr><td>pageIndex</td><td>goodsName</td><td>skuNo</td><td>goodsModel</td><td>unit</td><td>registerNo</td><td>receiveNum</td><td>goodsStorageStatus</td></tr>";
				sb.append(middleFix.replace("pageIndex", String.valueOf(i+1)).replace("goodsName", skuInfo.getSkuName())
						.replace("skuNo", skuInfo.getSkuNo())
						.replace("goodsModel", model.toString())
						.replace("registerNo", skuInfo.getRegistrationNumber()).replace("receiveNum", String.valueOf(warehouseGoodsOutInItem.getNum()))
						.replace("unit", skuInfo.getUnitName()).replace("goodsStorageStatus", "已入库"));
			}
		}

		WmsInOrderDto data = new WmsInOrderDto();
		getConsigneeAndInspector(warehouse, has6840, data);

		String surFix = "</tbody></table></div><!--Unnamed(矩形)--><div id=\"u43\"class=\"ax_default label\"><div id=\"u0_text\"class=\"text \"><p><span >收货员：receiver</span><span style=\"margin-left:600px\">入库时间：checkTime</span></p></div></div><div class=\"ax_default imgs\"><div><span style=\"color: red;border: 3px solid red;font-size: 25px;padding: 10px\">入库完成</span></div></div></div></body></html>";

		//替换：收货员（receiver）、验收员（checker）、验收入库时间（checkTime）
		sb.append(surFix.replace("receiver", StrUtil.isEmpty(data.getConsignee())?"":data.getConsignee())
				.replace("checkTime", DateUtil.formatDate(warehouse.getOutInTime())));
	}

	/**
	 * 获取商品明细信息
	 * @param skuInfo
	 * @param skuId
	 */
	@Override
	public void querySkuInfo(OutInDetail skuInfo, Integer skuId) {
		List<Integer> skuIdList = Arrays.asList(skuId);
		List<Map<String, Object>> maps = goodsMapper.skuTipList(skuIdList);
		if(CollectionUtils.isNotEmpty(maps)) {
			Map<String,Object> sku = maps.get(0);
			if(Objects.nonNull(sku.get("FIRST_ENGAGE_ID"))) {
				skuInfo.setFirstEngageId(Integer.valueOf(sku.get("FIRST_ENGAGE_ID").toString()));
				Integer new_standard_category_id = Integer.valueOf(sku.get("NEW_STANDARD_CATEGORY_ID").toString());
				Integer old_standard_category_id = Integer.valueOf(sku.get("OLD_STANDARD_CATEGORY_ID").toString());

				// 新国标
				if( null != new_standard_category_id && new_standard_category_id > 0){
					// 根据新国标分类id查询新国标分类
					String s = standardCategoryMapper.selectNameById(new_standard_category_id);
					if (str_6840.equals(s)) {
						skuInfo.setIs6840(true);
					}
				}

				// 旧国标
				if(null != old_standard_category_id && old_standard_category_id > 0){
					SysOptionDefinition sysOptionDefinition = sysOptionDefinitionMapper.selectByPrimaryKey(old_standard_category_id);
					SysOptionDefinition data = Optional.ofNullable(sysOptionDefinition).orElse(new SysOptionDefinition());
					String title = data.getTitle();
					if (str_6840.equals(title)) {
						skuInfo.setIs6840(true);
					}
				}

			}
			skuInfo.setSkuId(Integer.valueOf(sku.get("SKU_ID").toString()));
			skuInfo.setSpuId(Integer.valueOf(sku.get("SPU_ID").toString()));
			skuInfo.setSkuNo(sku.get("SKU_NO").toString());

			if(Objects.nonNull(sku.get("SHOW_NAME"))){
				skuInfo.setSkuName(sku.get("SHOW_NAME").toString());
			}
			if(Objects.nonNull(sku.get("BRAND_NAME"))){
				skuInfo.setBrandName(sku.get("BRAND_NAME").toString());
			}
			if(Objects.nonNull(sku.get("SPEC"))){
				skuInfo.setSpec(sku.get("SPEC").toString());
			}
			if(Objects.nonNull(sku.get("MODEL"))){
				skuInfo.setModel(sku.get("MODEL").toString());
			}
			if(Objects.nonNull(sku.get("REGISTRATION_NUMBER"))){
				skuInfo.setRegistrationNumber(sku.get("REGISTRATION_NUMBER").toString());
			}
			if(Objects.nonNull(sku.get("UNIT_NAME"))){
				skuInfo.setUnitName(sku.get("UNIT_NAME").toString());
			}
			if(Objects.nonNull(sku.get("FIRST_ENGAGE_ID"))){
				skuInfo.setFirstEngageId(Integer.valueOf(sku.get("FIRST_ENGAGE_ID").toString()));
			}
		}
	}
	
	
	/**
	 * HTML文本转PDF
	 * @param html 文本
	 * @param warehouseGoodsOutIn 入库单主表数据
	 */
	@Retryable(value=NullPointerException.class,maxAttempts = 3,backoff = @Backoff(delay = 2000L,multiplier = 1.5))
    public void htmlStrToPdfGetFile(String html,WarehouseGoodsOutIn warehouseGoodsOutIn) {
        String html2PdfUrl = html2PdfDomain + RENDER_URL;
        UrlToPdfParam urlToPdfParam = new UrlToPdfParam();
        urlToPdfParam.setHtml(html);
        UrlToPdfParam.Pdf pdf = new UrlToPdfParam.Pdf();
        UrlToPdfParam.Pdf.PdfMargin margin = new UrlToPdfParam.Pdf.PdfMargin("1cm" , "1cm" , "1cm" , "0cm");
        pdf.setMargin(margin);
        pdf.setScale(SCALE);
        urlToPdfParam.setPdf(pdf);
        // 上传入库单验收报告返回oss链接
        String ossUrl = ossUtilsService.migrateFile2Oss(html2PdfUrl, "pdf","验收单" + warehouseGoodsOutIn.getOutInNo(), urlToPdfParam);
        log.info("生成入库验收报告,入库单号：{},返回地址ossUrl:{}" , warehouseGoodsOutIn.getOutInNo(),ossUrl);
        if(StringUtils.isBlank(ossUrl)){
			log.info("生成入库验收报告失败");
			throw new NullPointerException("生成入库验收报告失败");
		}

        //新增验收报告附件
        Attachment attachment = new Attachment();
        attachment.setAttachmentType(ErpConst.WAREHOUSER_ATTACHMET_TYPE);
        attachment.setAttachmentFunction(ErpConst.WAREHOUSER_ATTACHMET_FUNCTION);
        attachment.setRelatedId(Integer.parseInt(String.valueOf(warehouseGoodsOutIn.getWarehouseGoodsOutInId())));
        
        String domainAndUri = ossUrl.split(ossHttp)[1];
        int domainIndex = domainAndUri.indexOf('/');
        String domain = domainAndUri.substring(0, domainIndex);
        String uri = domainAndUri.substring(domainIndex);
        attachment.setDomain(domain);
        attachment.setUri(uri);
        attachment.setName("验收单");
        attachment.setSuffix("pdf");
        attachment.setAddTime(System.currentTimeMillis());
        attachmentMapper.insertSelective(attachment);
    }

	@Override
	public List<WarehouseGoodsOutInItem> getWarehouseGoodsPurchaseOutInByPkId(Integer afterSalesId) {
		AfterSales afterSalesReturn = afterSalesMapper.getAfterSalesById(afterSalesId);
		if(Objects.nonNull(afterSalesReturn)) {
			List<WarehouseGoodsOutInItem> outInItemList = warehouseGoodsOutInItemMapper.getRetrunOutInItem(afterSalesReturn.getAfterSalesNo());
			return outInItemList;
		}
		return null;
	}

	/**
	 * 销售售后退换货直发入库
	 * @param user
	 * @param afterSalesId
	 * @param afterSalesDirectInfo
	 * @param erpOutInType
	 */
	@Override
	@Transactional
	public void inserSalesAfterThOrHh(User user, Integer afterSalesId, AfterSalesDirectInfo afterSalesDirectInfo, WarehouseGoodsInEnum erpOutInType) {
		WarehouseGoodsOutIn warehouseGoodsOutIn = new WarehouseGoodsOutIn();
		//ERP出入库单号
		BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.WAREHOUSE_GOODS_OUT_IN_RK);
		String outInNo = new BillNumGenerator().distribution(billGeneratorBean);
		warehouseGoodsOutIn.setOutInNo(outInNo);
		//wmsNo直发的给默认值
		warehouseGoodsOutIn.setWmsNo("");
		//根据erpOutInType，获取入库单的收发货方，取销售单的客户名称
		String relateNo = "";
		String outInCompany = "";
		log.info("销售售后退还货直发入库信息");
		AfterSales afterSales = afterSalesMapper.selectByPrimaryKey(afterSalesId);
		log.info("销售售后订单表信息：{}",JSON.toJSONString(afterSales));
		if(Objects.nonNull(afterSales)) {
			relateNo =afterSales.getAfterSalesNo();
			//获取收发货方
			Saleorder saleorder = saleorderMapper.getSaleOrderById(afterSales.getOrderId());
			if(Objects.nonNull(saleorder)) {
				outInCompany = saleorder.getTraderName();
			}
		}

		warehouseGoodsOutIn.setRelateNo(relateNo);
		warehouseGoodsOutIn.setOutInType(erpOutInType.getErpCode());
		warehouseGoodsOutIn.setOutInCompany(outInCompany);
		Date currentDate = new Date();
		warehouseGoodsOutIn.setOutInTime(currentDate);
		warehouseGoodsOutIn.setSource(WarehouseOutInSourceEnum.DIRECT_DELIVERY.getSource());
		warehouseGoodsOutIn.setAddTime(currentDate);
		//维护物流信息的人
		if(Objects.nonNull(user)) {
			warehouseGoodsOutIn.setCreator(user.getUserId());
			warehouseGoodsOutIn.setCreatorName(user.getUsername());
		}
		log.info("销售售后退还货直发入库主表插入数据：{}",JSON.toJSONString(warehouseGoodsOutIn));
		int success = warehouseGoodsOutInMapper.insertSelective(warehouseGoodsOutIn);
		if(success>0) {
			//异步生成入库单验收报告
			List<WarehouseGoodsOutInItem> warehouseGoodsOutInItemList = new ArrayList<>();
			WarehouseGoodsOutInItem warehouseGoodsOutInItem = new WarehouseGoodsOutInItem();
			warehouseGoodsOutInItem.setCompanyId(1);
			warehouseGoodsOutInItem.setOperateType(erpOutInType.getErpCode());
			warehouseGoodsOutInItem.setCheckStatus(1);
			warehouseGoodsOutInItem.setCheckStatusTime(DateUtil.formatDateTime(currentDate));
			warehouseGoodsOutInItem.setIsExpress(0);
			warehouseGoodsOutInItem.setOutInNo(warehouseGoodsOutIn.getOutInNo());
			//维护物流信息的人
			if(Objects.nonNull(user)) {
				warehouseGoodsOutInItem.setCreator(user.getUserId());
				warehouseGoodsOutInItem.setCreatorName(user.getUsername());
			}
			//入库
			warehouseGoodsOutInItem.setLogType(0);
			//关联采购、销售、售后产品ID，表T_AFTER_SALES_GOODS字段AFTER_SALES_GOODS_ID
			Integer relatedId = afterSalesDirectInfo.getAfterSalesGoodsId();
			warehouseGoodsOutInItem.setRelatedId(relatedId);
			log.info("关联采购、销售、售后产品ID，表T_AFTER_SALES_GOODS字段AFTER_SALES_GOODS_ID:relatedId:{}",relatedId);
			AfterSalesGoods afterSalesGoods = afterSalesGoodsMapper.getAfterSalesGoodsByAfterSalesGoodsId(relatedId);
			Integer goodsId = 0;
			if(Objects.nonNull(afterSalesGoods)){
				goodsId = afterSalesGoods.getGoodsId();
			}
			warehouseGoodsOutInItem.setGoodsId(goodsId);
			CoreSku coreSku = coreSkuMapper.selectByPrimaryKey(goodsId);
			//sn码 若商品“是否厂家赋SN码”为“是，一物一sn码，厂商批次号为空
			//sn码 若商品“是否厂家赋SN码”为“否，sn码为空，共一个厂商批次号
			//sn码生成规则：AT+16位唯一码(基于T_WAREHOUSE_GOODS_OPERATE_LOG_DIRECT主键生成) 此处sn码为空，生成成功后更新sn码
			boolean getSn = false;
			if(Objects.nonNull(coreSku)) {
				if(Boolean.TRUE.equals(coreSku.getIsFactorySnCode())) {
					getSn = true;
				}
			}
			//step1:区分销售售后类型，如果为销售售后退货类型，赋sn码和厂商批次码逻辑需要变动，销售售后换货的原逻辑不变
			//step2:赠品逻辑
			//赠品：厂商赋值【是】，取销售正向出库单内的赠品的sn码，一物一sn码，厂商批次码为空
			//赠品：厂商赋值【否】，取销售正向出库单内的赠品的厂商批次码，sn为空
			//step3:非赠品逻辑
			//非赠品：厂商赋值【是】，取销售正向出库单内的非赠品的sn码，一物一sn码，厂商批次码为空
			//非赠品：厂商赋值【否】，取销售正向出库单内的非赠品的厂商批次码，sn为空

			Integer outInType = erpOutInType.getErpCode();
			//直发销售售后退货
			List<String> snList= new ArrayList<>();
			if(outInType == WarehouseGoodsInEnum.ORDER_WAREHOUSE_BACK_IN.getErpCode()){
				//获取可用sn码，销售出库sn码排除掉销售售后已使用的sn码，厂商赋值【是】执行一物一码，厂商赋值【否】sn为空，赋厂商批次号
				//1：查询销售出库时的对应的商品SN码；2：查询销售售后退货已经使用的全部sn码
				String saleOrderNo = warehouseGoodsOutInMapper.getSaleOrderNoByAfterSalesNo(afterSales.getAfterSalesNo());
				List saleOrderNoList = Arrays.asList(saleOrderNo);
				snList = warehouseGoodsOutInMapper.getBarcodFactoryListBySaleOrderNo(saleOrderNoList,goodsId,relatedId);
			}
			else {
				//查询采购入库该商品的sn码列表,orderNo 售后单号对应的销售单对应的采购单号，本次入库的goodsId
				List<String> buyOrderNos = warehouseGoodsOutInMapper.getBuyOrderOrderNo(afterSales.getAfterSalesNo());
				if (CollectionUtils.isNotEmpty(buyOrderNos)) {
					//查询采购入库对应商品的sn码
					snList = warehouseGoodsOutInMapper.getBarcodFactoryListByOrderNo(buyOrderNos, goodsId);
				}
			}
			log.info("查询采购入库对应商品的sn码:{}",JSON.toJSONString(snList));
			int allSize = snList.size();
			//查询售后已使用的全部sn码,orderNo 销售单号，本次入库的goodsId
			List<String> existSnList=warehouseGoodsOutInMapper.getExistBarcodFactoryForInList(afterSales.getAfterSalesNo(),goodsId);
			log.info("查询售后已使用的全部sn码(包含退货和换货):{}",JSON.toJSONString(existSnList));
			//如果入库sn不为空，已使用sn不为空
			//判断snList是否存在重复，如果存在重复，则进行remove掉已使用的sn码个数
			//如果不存在重复，则不进行remove
			if(CollectionUtils.isNotEmpty(snList) && CollectionUtils.isNotEmpty(existSnList)){
				List<String> distinctSnList = snList.stream().distinct().collect(Collectors.toList());
				log.info("去重后的sn码：{}",distinctSnList);
				int usedSize = distinctSnList.size();
				//去重后的sn码原始sn码不同
				if(allSize!=usedSize){
					for (int size =0 ; size< existSnList.size() ; size ++){
						snList.remove(size);
					}
				}

			}
			//取值sn码,如果厂家赋SN码”为“是，随机取采购入库单时的sn码进行赋值
			//取值sn码,如果厂家赋SN码”为“否，sn码为空，其他厂商批次码，贝登批次码取采购入库单时数据
			String sn = "";
			if(getSn){
				for (int size = 0; size < afterSalesDirectInfo.getNum(); size++ ){
					//设置sn码,防止数组越界
					try{
						if(CollectionUtils.isNotEmpty(snList)){
							sn = snList.get(size);
						}
					}catch(Exception e){
						log.error("snList数组越界，获取不到sn码，不影响业务进行！");
					}
					//设置sn码
					warehouseGoodsOutInItem.setBarcodeFactory(sn);
					warehouseGoodsOutInItem.setNum(new BigDecimal(1));
					log.info("直发采购或者采购售后换货入库日志明细表插入数据：{}",JSON.toJSONString(warehouseGoodsOutInItem));
					warehouseGoodsOutInItemMapper.insertSelective(warehouseGoodsOutInItem);
					warehouseGoodsOutInItemList.add(warehouseGoodsOutInItem);
				}
			}else{
				warehouseGoodsOutInItem.setBarcodeFactory(sn);
				warehouseGoodsOutInItem.setNum(new BigDecimal(afterSalesDirectInfo.getNum()));
				//如果有批次号，设置批次号
				List<String> vedengBachList = new ArrayList<>();
				if(outInType == WarehouseGoodsInEnum.ORDER_WAREHOUSE_BACK_IN.getErpCode()){
					String saleOrderNo = warehouseGoodsOutInMapper.getSaleOrderNoByAfterSalesNo(afterSales.getAfterSalesNo());
					List<String> saleOrderList = Arrays.asList(saleOrderNo);
					if(CollectionUtils.isNotEmpty(saleOrderList)) {
						vedengBachList = warehouseGoodsOutInMapper.getVedengBatchListBySaleOrderNo(saleOrderList, goodsId,relatedId);
					}

				}else{
					List<String> buyOrderNos = warehouseGoodsOutInMapper.getBuyOrderOrderNo(afterSales.getAfterSalesNo());
					if(CollectionUtils.isNotEmpty(buyOrderNos)){
						vedengBachList = warehouseGoodsOutInMapper.getVedengBatchListByOrderNo(buyOrderNos,goodsId);
					}
				}
				if(CollectionUtils.isNotEmpty(vedengBachList)){
					warehouseGoodsOutInItem.setBatchNumber(vedengBachList.get(0));
				}
				log.info("直发采购或者采购售后换货入库日志明细表插入数据：{}",JSON.toJSONString(warehouseGoodsOutInItem));
				warehouseGoodsOutInItemMapper.insertSelective(warehouseGoodsOutInItem);
				warehouseGoodsOutInItemList.add(warehouseGoodsOutInItem);
			}

			try {
				if (WarehouseGoodsInEnum.ORDER_WAREHOUSE_BACK_IN.getErpCode().equals(erpOutInType.getErpCode())) {
					// 查询商品名发送站内信给采购人员
					List<Integer> list = warehouseGoodsOutInItemList.stream().map(WarehouseGoodsOutInItem::getRelatedId).distinct().collect(Collectors.toList());
					if (CollectionUtils.isNotEmpty(list)) {
						List<BuyOrderAndAfterSalesDto> buyOrderList = afterSalesGoodsMapper.selectBuyOrderByAfterSalesGoods(list);

						if (CollUtil.isNotEmpty(buyOrderList)) {

							Map<Integer, List<BuyOrderAndAfterSalesDto>> collect = buyOrderList.stream().collect(Collectors.groupingBy(BuyOrderAndAfterSalesDto::getUserId));

							String url = "/order/aftersalesUpgrade/viewAfterSalesDetail.do?afterSalesId=" + afterSalesId;
							for (Map.Entry<Integer, List<BuyOrderAndAfterSalesDto>> entry : collect.entrySet()) {
								// 发送站内信
								List<BuyOrderAndAfterSalesDto> value = entry.getValue();
								String sku = value.stream().map(BuyOrderAndAfterSalesDto::getSku).collect(Collectors.joining(StrUtil.COMMA));
								Map<String, String> paramMap = new HashMap<>();
								paramMap.put("afterSaleOrderNo", afterSales.getAfterSalesNo());
								paramMap.put("sku", sku);
								Integer key = entry.getKey();
								log.info("发送站内信给采购人员，采购人员id:{},参数：{}", JSON.toJSONString(key), JSON.toJSONString(paramMap));
								MessageUtil.sendMessage(300, CollUtil.newArrayList(key), paramMap, url, null);
								log.info("发送站内信给采购人员，成功");
							}
						}

					}

				}
			} catch (Exception e) {
				log.error("发送站内信失败", e);
			}


			new Thread(()-> {
				String uuid = UUID.randomUUID().toString().replace("-", "");
				log.info("开始执行createWarehouseGoodsInReport,uuid:{}",uuid);
				createWarehouseGoodsInReport(warehouseGoodsOutIn,warehouseGoodsOutInItemList);
				log.info("结束执行createWarehouseGoodsInReport,uuid:{}",uuid);
			}).start();
		}
	}

}
