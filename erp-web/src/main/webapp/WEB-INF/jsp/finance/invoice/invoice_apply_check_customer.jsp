<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<style>
    table {
        width: 100%;
        border-collapse: collapse;

    }
    .tip{
        margin-top: 20px;
        text-align: center;
        width: 69%;
        height: 12%;
    }
    td, th {
        /*border: 1px solid #ddd;*/
        text-align: left;
        padding: 8px;
    }

    .require {
        color: red;
        width: 15%;
        text-align:center;
    }
    .needless {
        color: grey;
        width: 15%;
        text-align:center;
    }
</style>
<div>
    <div class="tip">缺少必需的财务资质信息，请检查。</div>
    <table>
    <tbody>
        <tr>
            <td class="require">必需</td>
            <td style="width: 23%;">税务登记号</td>
            <td>${traderFinanceDto.taxNum}</td>
        </tr>
        <tr>
            <c:choose>
                <c:when test="${isSpecialInvoice}"> <td class="require">必需</td> </c:when>
                <c:otherwise> <td class="needless">无需</td> </c:otherwise>
            </c:choose>
            <td>注册地址</td>
            <td>${traderFinanceDto.regAddress}</td>
        </tr>
        <tr>
            <c:choose>
                <c:when test="${isSpecialInvoice}"> <td class="require">必需</td> </c:when>
                <c:otherwise> <td class="needless">无需</td> </c:otherwise>
            </c:choose>
            <td>注册电话</td>
            <td>${traderFinanceDto.regTel}</td>
        </tr>
        <tr>
            <c:choose>
                <c:when test="${isSpecialInvoice}"> <td class="require">必需</td> </c:when>
                <c:otherwise> <td class="needless">无需</td> </c:otherwise>
            </c:choose>
            <td>开户银行</td>
            <td>${traderFinanceDto.bank}</td>
        </tr>
        <tr>
            <c:choose>
                <c:when test="${isSpecialInvoice}"> <td class="require">必需</td> </c:when>
                <c:otherwise> <td class="needless">无需</td> </c:otherwise>
            </c:choose>
            <td>银行账号</td>
            <td>${traderFinanceDto.bankAccount}</td>
        </tr>
        <tr>
            <c:choose>
                <c:when test="${isSpecialInvoice}"> <td class="require">必需</td> </c:when>
                <c:otherwise> <td class="needless">无需</td> </c:otherwise>
            </c:choose>
            <td>一般纳税人资质</td>
            <td>
                <c:choose>
                    <c:when test="${traderFinanceDto.averageTaxpayerUri eq '' or traderFinanceDto.averageTaxpayerUri eq null}">无</c:when>
                    <c:otherwise>有</c:otherwise>
                </c:choose>
            </td>
        </tr>
    </tbody>
    </table>
</div>

</body>
