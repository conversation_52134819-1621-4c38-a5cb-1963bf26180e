package com.vedeng.goods.dao;

import com.vedeng.goods.model.BaseAttributeValue;
import com.vedeng.goods.model.vo.BaseAttributeValueVo;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;
import java.util.Collection;
import java.util.List;
import java.util.Map;

@Named("baseAttributeValueMapper")
public interface BaseAttributeValueMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_BASE_ATTRIBUTE_VALUE
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    int deleteByPrimaryKey(Integer baseAttributeValueId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_BASE_ATTRIBUTE_VALUE
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    int insert(BaseAttributeValue record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_BASE_ATTRIBUTE_VALUE
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    int insertSelective(BaseAttributeValue record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_BASE_ATTRIBUTE_VALUE
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    BaseAttributeValue selectByPrimaryKey(Integer baseAttributeValueId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_BASE_ATTRIBUTE_VALUE
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    int updateByPrimaryKeySelective(BaseAttributeValue record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_BASE_ATTRIBUTE_VALUE
     *
     * @mbg.generated Wed May 08 13:52:43 CST 2019
     */
    int updateByPrimaryKey(BaseAttributeValue record);

    /**
     * @description 新增属性值
     * <AUTHOR>
     * @param
     * @date 2019/5/8
     */
    Integer insertAttributeValByParam(Map<String, Object> paramMap);

    /**
     * @description 删除属性值信息
     * <AUTHOR>
     * @param
     * @date 2019/5/8
     */
    Integer deleteAttrValByParam(Map<String, Object> paramMap);

    /**
     * @description 根据属性Id获取属性值列表
     * <AUTHOR>
     * @param
     * @date 2019/5/22
     */
    List<BaseAttributeValueVo> getAttrValueByAttrId(Map<String, Object> paramMap);

    /**
     * @description 更新属性值信息
     * <AUTHOR>
     * @param
     * @date 2019/5/8
     */
    Integer updateAttributeValByParamBatch(Map<String, Object> paramMap);

    /**
     * @description 批量删除属性值信息
     * <AUTHOR>
     * @param
     * @date 2019/5/8
     */
    Integer deleteAttrValByParamBatch(Map<String, Object> paramMap);

    /**
     * 查询属性值列表,倒叙
     * <AUTHOR>
     * @param map
     * @return
     */
    List<BaseAttributeValueVo> getBaseAttributeValueVoList(Map<String,Object> map);

    /**
     * 根据属性ID查询属性值列表
     *  <AUTHOR>
     * @param record
     * @return
     */
    List<BaseAttributeValueVo> getBaseAttributeValueVoListByAttrId(BaseAttributeValueVo record);

    /**
     * 根据分类ID查询分类关联的属性值列表
     * <AUTHOR>
     * @param map
     * @return
     */
    List<BaseAttributeValueVo> getAttrValueListByCategoryId(Map<String,Object> map);

    /**
     * 查询属性值列表,正叙
     * <AUTHOR>
     * @param map
     * @return
     */
    List<BaseAttributeValueVo> getBaseAttributeValueVoListASC(Map<String,Object> map);

    /**
     * @description 根据属性值Id列表查询已删除的属性值的数目
     * <AUTHOR>
     * @param map
     * @return
     */
    Integer getDeletedAttrValueNumByIds(Map<String,Object> map);


    /**
     * 获指定属性号关联的属性值。
     *
     * @param attributeIdSet 目标数据表的主键列表
     * @param deleted value is equal to 0 or 1
     * @return
     */
    List<BaseAttributeValue> listAttrValueByAttrIds(@Param("attributeIdSet") Collection<Integer> attributeIdSet, @Param("deleted") Integer deleted);


    /**
     * 根据属性值Id获取属性值列表
     *
     * @param
     */
    List<BaseAttributeValue> listAttributeValueByIds(@Param("attributeValueIdList") List<Integer> attributeValueIdList);



    /**
     * 根据skuId获取属性值列表
     *
     * @param skuIdList
     */
    List<BaseAttributeValue> listAttributeValueBySkuIds(@Param("skuIdList") List<Integer> skuIdList);

}