<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="详情页" scope="application"/>
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript">

    function closeLendout() {
        checkLogin();
        index = layer.confirm("您是否确认关闭该外借单？", {
            btn: ['确定', '取消'] //按钮
        }, function () {

            $.ajax({
                url: page_url + '/wms/commodityLendOut/closeOrder.do',
                data: {"lendOutId": "${outputOrder.id}"},
                type: "POST",
                dataType: "json",
                async: false,
                success: function (data) {
                    debugger;
                    if (data.code == 0) {
                        window.location.reload();
                    } else {
                        layer.alert(data.message);
                    }
                },
                error: function (data) {
                    if (data.status == 1001) {
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });

            layer.close(index);

        }, function () {
        });
    }
</script>

<div class="content">
    <div class="formtitle"></div>
    <div id="desc_div">
        <form method="post" id="addForm" action="${pageContext.request.contextPath}/wms/commodityLendOut/addLendOut.do">
            <ul class="payplan">

                <div class="parts">

                    <div class="title-container title-container-blue">
                        <div class="table-title nobor">
                            基本信息
                        </div>
                    </div>

                    <table class="table table-bordered table-striped table-condensed table-centered">
                        <tbody>
                        <tr>
                            <td class="table-smaller">外借单号</td>
                            <td>${outputOrder.orderNo}</td>
                            <td class="table-smaller">审核状态</td>
                            <td>
                                <c:choose>
                                    <c:when test="${outputOrder.verifyStatus eq 0}">
                                        待审核
                                    </c:when>
                                    <c:when test="${outputOrder.verifyStatus eq 1}">
                                        审核中
                                    </c:when>
                                    <c:when test="${outputOrder.verifyStatus eq 2}">
                                        审核通过
                                    </c:when>
                                    <c:when test="${outputOrder.verifyStatus eq 3}">
                                        审核不通过
                                    </c:when>
                                    <c:when test="${outputOrder.verifyStatus eq 4}">
                                        已关闭
                                    </c:when>
                                </c:choose>
                            </td>
                        </tr>
                        <tr>
                            <td>借用企业</td>
                            <td>${outputOrder.borrowTraderName}</td>
                            <td>借用原因</td>
                            <td>${outputOrder.borrowReason}</td>
                        </tr>
                        <tr>
                            <td>创建人</td>
                            <td>${outputOrder.creator}</td>
                            <td>创建时间</td>
                            <td>${outputOrder.addTime}</td>
                        </tr>
                        <tr>
                            <td>归还状态</td>
                            <td>
                                <c:choose>
                                    <c:when test="${outputOrder.returnStatus eq 0}">
                                        未归还
                                    </c:when>
                                    <c:when test="${outputOrder.returnStatus eq 1}">
                                        部分归还
                                    </c:when>
                                    <c:when test="${outputOrder.returnStatus eq 2}">
                                        已归还
                                    </c:when>
                                </c:choose>
                            </td>
                            <td></td>
                            <td></td>
                        </tr>
                        </tbody>
                    </table>
                </div>

                <div class="parts">
                    <div class="title-container title-container-blue">
                        <div class="table-title nobor">
                            收货信息
                        </div>
                    </div>
                    <table class="table table-bordered table-striped table-condensed table-centered">
                        <tbody>
                        <tr>
                            <td class="table-smaller">收货客户</td>
                            <td>${outputOrder.borrowTraderName}</td>
                            <td class="table-smaller">收货联系人</td>
                            <td>${outputOrder.receiver}</td>
                        </tr>
                        <tr>
                            <td>电话</td>
                            <td>${outputOrder.receiverPhone}</td>
                            <td>手机</td>
                            <td>${outputOrder.receiverTelphone}</td>
                        </tr>
                        <tr>
                            <td>收货地区</td>
                            <td>${outputOrder.receiverAddress}</td>
                            <td></td>
                            <td>
                            </td>
                        </tr>
                        <tr>
                            <td>收货地址</td>
                            <td colspan="3">${outputOrder.detailAddress}</td>
                        </tr>
                        <tr>
                            <td>物流备注</td>
                            <td colspan="3">${outputOrder.logisticCommnet}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>

                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">产品信息</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                            <tr>
                                <th style="width:80px">序号</th>
                                <th style="width:80px">订货号</th>
                                <th style="width:80px">产品名称</th>
                                <th style="width:80px">品牌</th>
                                <th style="width:80px">型号</th>
                                <th style="width:80px">数量</th>
                                <th style="width:80px">预计归还日期</th>
                            </tr>
                            </thead>
                            <tbody id="thisTimeUpdateTbody">
                            <c:forEach var="lendOutGood" items="${lendOutGoodList}" varStatus="staut">
                                <tr>
                                    <td>${staut.count}</td>
                                    <td>${lendOutGood.skuNo}</td>
                                    <td>${lendOutGood.showName}</td>
                                    <td>${lendOutGood.brandName}</td>
                                    <td>${lendOutGood.model}</td>
                                    <td>${lendOutGood.outputNum}</td>
                                    <td>${lendOutGood.expectReturnedTime}</td>
                                </tr>
                            </c:forEach>
                            </tbody>
                        </table>
                    </div>
                </li>

                <%--                出库记录--%>
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">出库记录</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                            <tr>
                                <th style="width:80px">出库单据</th>
                                <th class="wid5">序号</th>
                                <th class="wid10">订货号</th>
                                <th>产品名称</th>
                                <th>品牌</th>
                                <th>型号</th>
                                <th>实际出库数量</th>
                                <th class="wid4">单位</th>
                                <th>贝登批次码</th>
                                <th>生产日期</th>
                                <th>有效期至</th>
                                <th>出库时间</th>
                                <th>入库日期</th>
                                <th>生产批号</th>
                                <th>SN码</th>
                                <th>灭菌编号</th>
                                <th class="wid12">注册证号</th>
                            </tr>
                            </thead>
                            <tbody>
                            <c:forEach var="listout" items="${warehouseOutList}" varStatus="num1">
                                <tr>
                                    <td  class="text-left">
                                        <a class="addtitle"
                                           href="javascript:void(0);"
                                           tabTitle='{"num":"viewck${listout.outInNo}","link":"./warehouse/warehousesout/detail.do?outInNo=${listout.outInNo}&outInType=${listout.operateType}","title":"出库单详情页"}'>${listout.outInNo}</a>
                                    </td>
                                    <td>${num1.count}</td>
                                    <td>${newSkuInfosMap[listout.skuId].SKU_NO}</td>
                                    <td class="text-left">
                                        <a class="addtitle" href="javascript:void(0);"
                                           tabTitle='{"num":"viewgoods${listout.skuId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${listout.skuId}","title":"产品信息"}'>${newSkuInfosMap[listout.skuId].SHOW_NAME}</a>
                                    </td>
                                    <td>${newSkuInfosMap[listout.skuId].BRAND_NAME}</td>
                                    <td>${newSkuInfosMap[listout.skuId].MODEL}</td>
                                    <td>${listout.num}</td>
                                    <td>${newSkuInfosMap[listout.skuId].UNIT_NAME}</td>
                                    <td>${ listout.vedengBatchNumber}</td>
                                    <td><date:date value="${listout.productDate}" format="yyyy-MM-dd HH:mm:ss"/></td>
                                    <td><date:date value="${listout.expirationDate}" format="yyyy-MM-dd HH:mm:ss"/></td>
                                    <td>${ listout.addTimeStr}</td>
                                    <td><date:date value="${listout.checkStatusTime}" format="yyyy-MM-dd HH:mm:ss"/></td>
                                    <td>${ listout.batchNumber}</td>
                                    <td>${ listout.barcodeFactory}</td>
                                    <td>${ listout.sterilizationBatchNumber}</td>
                                    <td class="text-left">
                                        <a class="addtitle" href="javascript:void(0);"
                                           tabTitle='{"num":"viewfirstgoods${newSkuInfosMap[listout.skuId].FIRST_ENGAGE_ID}","link":"./firstengage/baseinfo/getFirstSearchDetail.do?firstEngageId=${newSkuInfosMap[listout.skuId].FIRST_ENGAGE_ID}","title":"首营信息"}'>${newSkuInfosMap[listout.skuId].REGISTRATION_NUMBER}</a>
                                    </td>
                                </tr>
                            </c:forEach>
                            <!-- 查询无结果弹出 -->
                            <c:if test="${empty warehouseOutList}">
                                <tr>
                                    <td colspan="17">暂无出库记录</td>
                                </tr>
                            </c:if>
                            </tbody>
                        </table>
                    </div>
                </li>
                <%--                入库记录--%>
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">入库记录</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                            <tr>
                                <th style="width:80px">入库单据</th>
                                <th class="wid5">序号</th>
                                <th class="wid10">订货号</th>
                                <th>产品名称</th>
                                <th>品牌</th>
                                <th>型号</th>
                                <th>实际入库数量</th>
                                <th class="wid4">单位</th>
                                <th>贝登批次码</th>
                                <th>生产日期</th>
                                <th>有效期至</th>
                                <th>入库日期</th>
                                <th>生产批号</th>
                                <th>SN码</th>
                                <th>灭菌编号</th>
                                <th class="wid12">注册证号</th>
                            </tr>
                            </thead>
                            <tbody>
                            <c:forEach var="listin" items="${warehouseInList}" varStatus="num2">
                                <tr>
                                <td>
                                    <a class="addtitle" href="javascript:void(0);"
                                       tabTitle='{"num":"","link":"/warehouseOutIn/detail.do?warehouseGoodsOutInId=${listin.warehouseGoodsOutInId}","title":"外借入库详情"}'>${listin.outInNo}</a>
                                </td>
                                <td>${num2.count}</td>
                                <td>${newSkuInfosMap[listin.skuId].SKU_NO}</td>
                                <td class="text-left">
                                    <a class="addtitle" href="javascript:void(0);"
                                       tabTitle='{"num":"viewgoods${listin.skuId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${listin.skuId}","title":"产品信息"}'>${newSkuInfosMap[listin.skuId].SHOW_NAME}</a>
                                </td>
                                <td>${newSkuInfosMap[listin.skuId].BRAND_NAME}</td>
                                <td>${newSkuInfosMap[listin.skuId].MODEL}</td>
                                <td>${listin.num}</td>
                                <td>${newSkuInfosMap[listin.skuId].UNIT_NAME}</td>
                                <td>${ listin.vedengBatchNumber}</td>
                                <td><date:date value="${listin.productDate}" format="yyyy-MM-dd HH:mm:ss"/></td>
                                <td><date:date value="${listin.expirationDate}" format="yyyy-MM-dd HH:mm:ss"/></td>
                                <td><date:date value="${listin.checkStatusTime}" format="yyyy-MM-dd HH:mm:ss"/></td>
                                <td>${ listin.batchNumber}</td>
                                <td>${ listin.barcodeFactory}</td>
                                <td>${ listin.sterilizationBatchNumber}</td>
                                <td class="text-left">
                                    <a class="addtitle" href="javascript:void(0);"
                                       tabTitle='{"num":"viewfirstgoods${newSkuInfosMap[listin.skuId].FIRST_ENGAGE_ID}","link":"./firstengage/baseinfo/getFirstSearchDetail.do?firstEngageId=${newSkuInfosMap[listin.skuId].FIRST_ENGAGE_ID}","title":"首营信息"}'>${newSkuInfosMap[listin.skuId].REGISTRATION_NUMBER}</a>
                                </td>
                                </tr>
                            </c:forEach>
                            <c:if test="${empty warehouseInList}">
                                <tr>
                                    <td colspan="16">暂无入库记录</td>
                                </tr>
                            </c:if>
                            </tbody>
                        </table>
                    </div>
                </li>
                <%--                产品验收报告--%>
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">产品验收报告</div>
                            <div class="title-click nobor pop-new-data" layerParams='{"width":"600px","height":"540px","title":"添加产品验收报告","link":"./toAddGoodsAcceptanceReport.do?orderNo=${outputOrder.orderNo}&lendOutId=${lendOutId}"}'>添加</div>

                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                            <tr>
                                <th style="width:80px">添加人</th>
                                <th class="wid5">添加时间</th>
                                <th class="wid10">订货号</th>
                                <th>产品名称</th>
                                <th>产品外包装</th>
                                <th>产品外观</th>
                                <th>产品性能</th>
                                <th class="wid4">产品退回原因</th>
                                <th>产品具体描述</th>
                                <th>附件</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <c:forEach var="var" items="${acceptanceReportList}" varStatus="num2">
                                <tr>
                                    <td>${var.creatorName} </td>
                                    <td>${var.addTimeStr} </td>
                                    <td>${var.sku} </td>
                                    <td>
                                        <a href="${pageContext.request.contextPath}/goods/vgoods/viewSku.do?skuId=${var.goodsId}&spuId=${var.spuId}&pageType=0">
                                                ${var.showName}
                                        </a>
                                    </td>
                                    <td>${var.packaging} </td>
                                    <td>${var.appearance} </td>
                                    <td>${var.performance} </td>
                                    <td>${var.backReason} </td>
                                    <td>${var.description} </td>
                                    <td>
                                        <c:forEach var="att" items="${var.attachmentList}" varStatus="status">
                                            <span>${att.name}&nbsp;</span>
                                        </c:forEach>
                                    </td>
                                    <td>
                                        <div class="title-click nobor pop-new-data" layerParams='{"width":"600px","height":"520px","title":"修改产品验收报告","link":"./toAddGoodsAcceptanceReport.do?goodsAcceptanceReportId=${var.goodsAcceptanceReportId}&lendOutId=${lendOutId}"}'>修改</div>
                                    </td>
                                </tr>
                            </c:forEach>
                            </tbody>
                        </table>
                    </div>
                </li>
                <%--                审核记录--%>
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor ">审核记录</div>
                            <div class="title-click nobor pop-new-data" layerParams='{"width":"520px","height":"200px","text-align":"center","title":"请选择通知人","link":"./toChoose.do?lendOutId=${outputOrder.id}"}'>通知售后</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                            <tr>
                                <th style="width:50px">操作人</th>
                                <th style="width:80px">操作时间</th>
                                <th style="width:80px">操作事项</th>
                                <th style="width:80px">备注</th>
                            </tr>
                            </thead>
                            <tbody>
                            <c:forEach var="hi" items="${historicActivityInstance}" varStatus="status">
                                <c:if test="${not empty  hi.activityName}">
                                    <tr>
                                        <td>
                                            <c:choose>
                                                <c:when test="${hi.activityType == 'startEvent'}">
                                                    ${startUser}
                                                </c:when>
                                                <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                                </c:when>
                                                <c:otherwise>
                                                    <c:if test="${historicActivityInstance.size() == status.count}">
                                                        <c:forEach var="vs" items="${verifyUsersList}"
                                                                   varStatus="status">
                                                            <c:if test="${fn:contains(verifyUserList, vs)}">
                                                                <span class="font-green">${vs}</span>&nbsp;
                                                            </c:if>
                                                            <c:if test="${!fn:contains(verifyUserList, vs)}">
                                                                <span>${vs}</span>&nbsp;
                                                            </c:if>
                                                        </c:forEach>

                                                        <c:if test="${empty verifyUsersList && empty hi.assignee}">
                                                            ${verifyUsers}
                                                        </c:if>
                                                    </c:if>
                                                    <c:if test="${historicActivityInstance.size() != status.count}">
                                                        ${hi.assignee}
                                                    </c:if>
                                                </c:otherwise>
                                            </c:choose>


                                        </td>
                                        <td><fmt:formatDate value="${hi.endTime}" pattern="yyyy-MM-dd HH:mm:ss"/></td>
                                        <td>
                                            <c:choose>
                                                <c:when test="${hi.activityType == 'startEvent'}">
                                                    开始
                                                </c:when>
                                                <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                                    结束
                                                </c:when>
                                                <c:otherwise>
                                                    ${hi.activityName}
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                        <td class="font-red">${commentMap[hi.taskId]}</td>
                                    </tr>
                                </c:if>
                            </c:forEach>
                            <!-- 查询无结果弹出 -->

                            <c:if test="${empty historicActivityInstance}">
                                <!-- 查询无结果弹出 -->
                                <tr>
                                    <td colspan="4">暂无审核记录。</td>
                                </tr>
                            </c:if>
                            </tbody>
                        </table>
                    </div>
                </li>

            </ul>
            <div class="add-tijiao tcenter mt10">

                <c:if test="${null!=historicInfo.taskInfo and (taskInfo.assignee == curr_user.username or historicInfo.candidateUserMap['belong']) }">
                    <button type="button" class="bt-bg-style bg-light-green bt-small mr10 pop-new-data"
                            layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./auditResult.do?taskId=${taskId}&lendOutId=${outputOrder.id}&pass=true"}'>
                        审核通过
                    </button>
                    <button type="button" class="bt-bg-style bg-light-orange bt-small mr10 pop-new-data"
                            layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./auditResult.do?taskId=${taskId}&lendOutId=${outputOrder.id}&pass=false"}'>
                        审核不通过
                    </button>
                </c:if>

                <c:if test="${outputOrder.creator == curr_user.username && outputOrder.verifyStatus != 2 && outputOrder.verifyStatus != 4}">
                    <button type="button" class="bt-bg-style bg-deep-red" onclick="closeLendout();">关闭外借单</button>
                </c:if>

            </div>
        </form>
    </div>
</div>
<%@ include file="../../common/footer.jsp" %>