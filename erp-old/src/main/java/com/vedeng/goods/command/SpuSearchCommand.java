package com.vedeng.goods.command;

import com.vedeng.common.constant.goods.GoodsCheckStatusEnum;
import com.vedeng.common.controller.BaseCommand;
import com.vedeng.firstengage.model.FirstEngage;
import com.vedeng.system.model.SysOptionDefinition;

import java.util.Date;
import java.util.List;

public class SpuSearchCommand extends BaseCommand {

	// 是否可售
	private Integer isAvailableSale;

	private List<SysOptionDefinition> spuTypeList;

	private Integer tabCheckStatus;

	private FirstEngage firstEngage;

	private String searchType;// 多维搜索的方式

	private String searchValue;// 多维搜索的value

	private List<String> errors;

	private Integer spuId;

	private String spuIds;// 导出

	private Integer categoryId;

	@Deprecated
	private Integer spuLevel;// 0:其他产品,1:核心产品、2:临时产品、
	private Integer brandId;
	private Integer spuType;// 默认为器械
	private Integer firstEngageId;
	private String spuName;
	private String showName;
	private String wikiHref;

	private String spuStatus;

	private String registrationIcon;// 注册商标

	private Integer skuId;
	private String skuIds;// 批量设置备货

	private String hospitalDeptTags;

	private Integer operateInfoId;
	private Integer operateInfoFlag;
	private GoodsCheckStatusEnum[] checkStatus = GoodsCheckStatusEnum.values();

	private Integer manageCategoryLevel;// 管理类别
	private Integer spuCheckStatus;// spu审核状态

	private Integer skuCheckStatus;// sku审核状态
	private Integer newStandardCategoryId;// 新国标

	private Integer hasBackupMachine;// 0未设置备货

	private Integer productCompanyId;// 厂家

	private Integer pushStatus;// 推送的状态

	/**
	 * 推送状态集合
	 */
	private List<Integer> pushStatusList;

	//价格审核状态
	private Integer priceVerifyStatus;


	/**
	 * 物料编码
	 *
	 * @since ERP_LV_2020_86
	 */
	private String materialCode;


	private Integer goodsLevelNo;


	private Integer goodsPositionNo;

	private Integer goodsTodoItemSearchFlag;

	/**
	 * 同步状态 0 未推送  1 已推送 2 需重推
	 */
	private Integer synchronizationStatus;


	/**
	 * 禁用状态
	 */
	private Integer spuDisableStatus;

	/**
	 * 禁用状态，默认启用
	 */
	private Integer skuDisableStatus = 1;


	public Integer getSpuDisableStatus() {
		return spuDisableStatus;
	}

	public void setSpuDisableStatus(Integer spuDisableStatus) {
		this.spuDisableStatus = spuDisableStatus;
	}

	public Integer getSkuDisableStatus() {
		return skuDisableStatus;
	}

	public void setSkuDisableStatus(Integer skuDisableStatus) {
		this.skuDisableStatus = skuDisableStatus;
	}

	public Integer getSynchronizationStatus() {
		return synchronizationStatus;
	}

	public void setSynchronizationStatus(Integer synchronizationStatus) {
		this.synchronizationStatus = synchronizationStatus;
	}

	public Integer getPushStatus() {
		return pushStatus;
	}

	public void setPushStatus(Integer pushStatus) {
		this.pushStatus = pushStatus;
	}

	public Integer getIsStockup() {
		return isStockup;
	}

	public void setIsStockup(Integer isStockup) {
		this.isStockup = isStockup;
	}

	private Integer isStockup;
	public String getProductCompanyName() {
		return productCompanyName;
	}

	public void setProductCompanyName(String productCompanyName) {
		this.productCompanyName = productCompanyName;
	}

	private String productCompanyName;// 厂家


	private Integer departmentId;// 科室

	public String getDepartmentName() {
		return departmentName;
	}

	public void setDepartmentName(String departmentName) {
		this.departmentName = departmentName;
	}

	private String departmentName;// 科室名称

	private Date modTimeStart;
	private Date modTimeEnd;


    private Integer spuIdSearch;

	public Integer getSpuIdSearch() {
		return spuIdSearch;
	}

	public void setSpuIdSearch(Integer spuIdSearch) {
		this.spuIdSearch = spuIdSearch;
	}

	private String deleteReason;



	private Integer assignmentManagerId;




	private Integer assignmentAssistantId;

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    private String brandName;

	/**
	 * 是否需要报备
	 */
	private Integer isNeedReport;

	/**
	 * 待办事项涉及的商品的等级
	 */
	private Integer goodsLevelFromTodoList;

	/**
	 * 待办事项业务类型
	 */
	private Integer buzTypeFromTodoList;

	private String subordinateList;

	private List<Integer> subordinates;

	public String getSubordinateList() {
		return subordinateList;
	}

	public void setSubordinateList(String subordinateList) {
		this.subordinateList = subordinateList;
	}

	public List<Integer> getSubordinates() {
		return subordinates;
	}

	public void setSubordinates(List<Integer> subordinates) {
		this.subordinates = subordinates;
	}

	public Integer getGoodsLevelFromTodoList() {
		return goodsLevelFromTodoList;
	}

	public void setGoodsLevelFromTodoList(Integer goodsLevelFromTodoList) {
		this.goodsLevelFromTodoList = goodsLevelFromTodoList;
	}

	public Integer getBuzTypeFromTodoList() {
		return buzTypeFromTodoList;
	}

	public void setBuzTypeFromTodoList(Integer buzTypeFromTodoList) {
		this.buzTypeFromTodoList = buzTypeFromTodoList;
	}

	public Integer getIsNeedReport() {
		return isNeedReport;
	}

	public void setIsNeedReport(Integer isNeedReport) {
		this.isNeedReport = isNeedReport;
	}

	public Integer getAssignmentManagerId() {
		return assignmentManagerId;
	}

	public void setAssignmentManagerId(Integer assignmentManagerId) {
		this.assignmentManagerId = assignmentManagerId;
	}
	public Integer getAssignmentAssistantId() {
		return assignmentAssistantId;
	}

	public void setAssignmentAssistantId(Integer assignmentAssistantId) {
		this.assignmentAssistantId = assignmentAssistantId;
	}

	public Integer getTabCheckStatus() {
		return tabCheckStatus;
	}

	public void setTabCheckStatus(Integer tabCheckStatus) {
		this.tabCheckStatus = tabCheckStatus;
	}
	public Integer getManageCategoryLevel() {
		return manageCategoryLevel;
	}

	public void setManageCategoryLevel(Integer manageCategoryLevel) {
		this.manageCategoryLevel = manageCategoryLevel;
	}

	public Integer getSpuCheckStatus() {
		return spuCheckStatus;
	}

	public void setSpuCheckStatus(Integer spuCheckStatus) {
		this.spuCheckStatus = spuCheckStatus;
	}

	public Integer getSkuCheckStatus() {
		return skuCheckStatus;
	}

	public void setSkuCheckStatus(Integer skuCheckStatus) {
		this.skuCheckStatus = skuCheckStatus;
	}

	public Integer getNewStandardCategoryId() {
		return newStandardCategoryId;
	}

	public void setNewStandardCategoryId(Integer newStandardCategoryId) {
		this.newStandardCategoryId = newStandardCategoryId;
	}

	public Integer getHasBackupMachine() {
		return hasBackupMachine;
	}

	public void setHasBackupMachine(Integer hasBackupMachine) {
		this.hasBackupMachine = hasBackupMachine;
	}

	public void setCheckStatus(GoodsCheckStatusEnum[] checkStatus) {
		this.checkStatus = checkStatus;
	}

	public Integer getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Integer categoryId) {
		this.categoryId = categoryId;
	}

	public Integer getSpuLevel() {
		return spuLevel;
	}

	public void setSpuLevel(Integer spuLevel) {
		this.spuLevel = spuLevel;
	}

	public Integer getBrandId() {
		return brandId;
	}

	public void setBrandId(Integer brandId) {
		this.brandId = brandId;
	}

	public Integer getFirstEngageId() {
		return firstEngageId;
	}

	public void setFirstEngageId(Integer firstEngageId) {
		this.firstEngageId = firstEngageId;
	}

	public String getSpuName() {
		return spuName;
	}

	public void setSpuName(String spuName) {
		this.spuName = spuName;
	}

	public String getWikiHref() {
		return wikiHref;
	}

	public void setWikiHref(String wikiHref) {
		this.wikiHref = wikiHref;
	}

	public String getSpuStatus() {
		return spuStatus;
	}

	public void setSpuStatus(String spuStatus) {
		this.spuStatus = spuStatus;
	}

	public List<Integer> getPushStatusList() {
		return pushStatusList;
	}

	public void setPushStatusList(List<Integer> pushStatusList) {
		this.pushStatusList = pushStatusList;
	}

	public Integer getSpuType() {
		return spuType;
	}

	public void setSpuType(Integer spuType) {
		this.spuType = spuType;
	}

	public String getShowName() {
		return showName;
	}

	public void setShowName(String showName) {
		this.showName = showName;
	}

	public List<String> getErrors() {
		return errors;
	}

	public void setErrors(List<String> errors) {
		this.errors = errors;
	}

	public List<SysOptionDefinition> getSpuTypeList() {
		return spuTypeList;
	}

	public void setSpuTypeList(List<SysOptionDefinition> spuTypeList) {
		this.spuTypeList = spuTypeList;
	}

	public String getRegistrationIcon() {
		return registrationIcon;
	}

	public void setRegistrationIcon(String registrationIcon) {
		this.registrationIcon = registrationIcon;
	}

	public GoodsCheckStatusEnum[] getCheckStatus() {
		return checkStatus;
	}

	public Integer getOperateInfoId() {
		return operateInfoId;
	}

	public void setOperateInfoId(Integer operateInfoId) {
		this.operateInfoId = operateInfoId;
	}

	public Integer getSpuId() {
		return spuId;
	}

	public void setSpuId(Integer spuId) {
		this.spuId = spuId;
	}

	public Integer getOperateInfoFlag() {
		return operateInfoFlag;
	}

	public void setOperateInfoFlag(Integer operateInfoFlag) {
		this.operateInfoFlag = operateInfoFlag;
	}


	public FirstEngage getFirstEngage() {
		return firstEngage;
	}

	public void setFirstEngage(FirstEngage firstEngage) {
		this.firstEngage = firstEngage;
	}

	public String getHospitalDeptTags() {
		return hospitalDeptTags;
	}

	public void setHospitalDeptTags(String hospitalDeptTags) {
		this.hospitalDeptTags = hospitalDeptTags;
	}

	public String getSearchType() {
		return searchType;
	}

	public void setSearchType(String searchType) {
		this.searchType = searchType;
	}

	public String getSearchValue() {
		return searchValue;
	}

	public void setSearchValue(String searchValue) {
		this.searchValue = searchValue;
	}

	public Integer getProductCompanyId() {
		return productCompanyId;
	}

	public void setProductCompanyId(Integer productCompanyId) {
		this.productCompanyId = productCompanyId;
	}

	public Integer getDepartmentId() {
		return departmentId;
	}

	public void setDepartmentId(Integer departmentId) {
		this.departmentId = departmentId;
	}

	public Date getModTimeStart() {
		return modTimeStart;
	}

	public void setModTimeStart(Date modTimeStart) {
		this.modTimeStart = modTimeStart;
	}

	public Date getModTimeEnd() {
		return modTimeEnd;
	}

	public void setModTimeEnd(Date modTimeEnd) {
		this.modTimeEnd = modTimeEnd;
	}

	public String getDeleteReason() {
		return deleteReason;
	}

	public void setDeleteReason(String deleteReason) {
		this.deleteReason = deleteReason;
	}

	public Integer getSkuId() {
		return skuId;
	}

	public void setSkuId(Integer skuId) {
		this.skuId = skuId;
	}

	public String getSkuIds() {
		return skuIds;
	}

	public void setSkuIds(String skuIds) {
		this.skuIds = skuIds;
	}

	public String getSpuIds() {
		return spuIds;
	}

	public void setSpuIds(String spuIds) {
		this.spuIds = spuIds;
	}

	public Integer getPriceVerifyStatus() {
		return priceVerifyStatus;
	}

	public void setPriceVerifyStatus(Integer priceVerifyStatus) {
		this.priceVerifyStatus = priceVerifyStatus;
	}

	/*
	养护类型
	 */
	private Integer curingType;
	/*
	是否必须检测报告
	 */
	private Integer isNeedTestReprot;

	/*
	是否套件
	 */
	private Integer isKit;

	/*
	是否异性品
	 */
	private Integer isBadGoods;
	/*
	是否启用多级包装
	 */
	private Integer isEnableMultistagePackage;

	public Integer getCuringType() {
		return curingType;
	}

	public void setCuringType(Integer curingType) {
		this.curingType = curingType;
	}

	public Integer getIsNeedTestReprot() {
		return isNeedTestReprot;
	}

	public void setIsNeedTestReprot(Integer isNeedTestReprot) {
		this.isNeedTestReprot = isNeedTestReprot;
	}

	public Integer getIsKit() {
		return isKit;
	}

	public void setIsKit(Integer isKit) {
		this.isKit = isKit;
	}

	public Integer getIsBadGoods() {
		return isBadGoods;
	}

	public void setIsBadGoods(Integer isBadGoods) {
		this.isBadGoods = isBadGoods;
	}

	public Integer getIsEnableMultistagePackage() {
		return isEnableMultistagePackage;
	}

	public void setIsEnableMultistagePackage(Integer isEnableMultistagePackage) {
		this.isEnableMultistagePackage = isEnableMultistagePackage;
	}

	public String getMaterialCode() {
		return materialCode;
	}

	public void setMaterialCode(String materialCode) {
		this.materialCode = materialCode;
	}

	public Integer getGoodsLevelNo() {
		return goodsLevelNo;
	}

	public void setGoodsLevelNo(Integer goodsLevelNo) {
		this.goodsLevelNo = goodsLevelNo;
	}

	public Integer getGoodsPositionNo() {
		return goodsPositionNo;
	}

	public void setGoodsPositionNo(Integer goodsPositionNo) {
		this.goodsPositionNo = goodsPositionNo;
	}

	public Integer getGoodsTodoItemSearchFlag() {
		return goodsTodoItemSearchFlag;
	}

	public void setGoodsTodoItemSearchFlag(Integer goodsTodoItemSearchFlag) {
		this.goodsTodoItemSearchFlag = goodsTodoItemSearchFlag;
	}

	public Integer getIsAvailableSale() {
		return isAvailableSale;
	}

	public void setIsAvailableSale(Integer isAvailableSale) {
		this.isAvailableSale = isAvailableSale;
	}
}
