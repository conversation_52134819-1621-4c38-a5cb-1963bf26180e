package com.vedeng.trader.group.visit;

import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.http.HttpRestClientUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.op.api.dto.sku.ReqSkuDto;
import com.vedeng.track.api.dto.business.req.BusinessReqDto;
import com.vedeng.track.api.dto.business.res.TrackResDto;
import com.vedeng.trader.dao.WebAccountMapper;
import com.vedeng.trader.group.GoodsBehavior;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import java.util.ArrayList;
import java.util.BitSet;
import java.util.List;

public abstract class TrackBaseBehvior extends GoodsBehavior {
    private Logger logger= LoggerFactory.getLogger(TrackBaseBehvior.class);
    // 运行时注入service
    private WebAccountMapper webAccountMapper=(WebAccountMapper) context.getBean(WebAccountMapper.class);


    public BitSet getVisitData(BusinessReqDto reqDto) {
        String url = trackUrl + "track/getVisitMobile";
        return getTrackData(url,reqDto);
    }

    public BitSet getAddCartData(BusinessReqDto reqDto){
        String url = trackUrl + "track/getAddShopCartMobile";
        return getTrackData(url,reqDto);
    }

    public BitSet getSearchData(BusinessReqDto reqDto){
        String url = trackUrl + "track/getSearchKeyWordsMobile";
        return getTrackData(url,reqDto);
    }
    private BitSet getTrackData(String url,Object params){
        BitSet result=new BitSet();
        try{


            com.alibaba.fastjson.TypeReference<RestfulResult<List<TrackResDto>>> typeReference = new com.alibaba.fastjson.TypeReference<RestfulResult<List<TrackResDto>>>() {
            };
            RestfulResult<List<TrackResDto>> mobileRes = HttpRestClientUtil.restPost(url, typeReference, null, params);
            if(mobileRes!=null&&mobileRes.isSuccess()){
                List<TrackResDto> mobiles=mobileRes.getData();
                List<String> mobileList=new ArrayList<>();
                if(CollectionUtils.isNotEmpty(mobiles)){
                    for(TrackResDto d:mobiles){
                        if(d==null|| StringUtil.isEmpty(d.getMobile())){
                            continue;
                        }
                        mobileList.add(d.getMobile());
                    }
                }
                if(CollectionUtils.isNotEmpty(mobileList)){
                    List<Integer> traderIds=webAccountMapper.getTraderIdsByMobileList(mobileList);
                    if (CollectionUtils.isNotEmpty(traderIds)) {
                        for(Integer id:traderIds){
                            if(id!=null){
                                result.set(id);
                            }
                        }
                    }
                }
            }

        }catch (Exception ex){
            logger.error("最近有访问行为出错：",ex);
        }
        return result;
    }

    public void getGoods(Integer type){
        ReqSkuDto skuDto=new ReqSkuDto();

        if(type==0){
            skuDto.setCategoryId(getCategoryId());
        }else if(type==1){
            skuDto.setBrandId(getBrandId());
        }
        try{
            String url = operateUrl + "/op/getAllSkuListByParams";
            com.alibaba.fastjson.TypeReference<RestfulResult<List<String>>> typeReference = new com.alibaba.fastjson.TypeReference<RestfulResult<List<String>>>() {
            };
            RestfulResult<List<String>> restfulResult = HttpRestClientUtil.restPost(url, typeReference, null, skuDto);
            if(restfulResult.isSuccess()){
                setSkuList(restfulResult.getData());
            }
        }catch (Exception ex){}
    }
}
