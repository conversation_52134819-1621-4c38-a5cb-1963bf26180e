
Vue.component('ui-radio', {
    template: `
        <div
            class="vd-ui-radio-item"
            :class="{
                'vd-ui-radio-item-checked': currentChecked,
                'vd-ui-radio-item-disabled': disabled
            }"
            @click="handlerClick()"
        >
            <div class="vd-ui-radio-inner">
                <div class="vd-ui-radio-icon">
                    <div class="vd-ui-radio-icon-selected"></div>
                </div>
                <span class="vd-ui-raiod-label">{{ label }}</span>
                <span class="vd-ui-raiod-tip" v-html="tip"></span>
            </div>
        </div>
    `,

    props: {
        label: {
            type: String,
            default: "",
        },
        value: {
            type: String,
        },
        tip: {
            type: String,
        },
        checked: {
            type: Boolean,
            default: false,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        clearable: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            currentChecked: false,
        };
    },
    mounted() {
        this.currentChecked = this.checked;
    },
    watch: {
        checked() {
            this.currentChecked = this.checked;
        },
    },
    methods: {
        handlerClick() {
            if (!this.disabled && (this.clearable || !this.currentChecked)) {
                this.currentChecked = !this.currentChecked;
                this.$emit("update:checked", this.currentChecked);
                this.$emit("change", this.currentChecked);
            }
        },
    },
})
