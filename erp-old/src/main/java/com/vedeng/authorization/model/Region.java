package com.vedeng.authorization.model;

import java.io.Serializable;
import java.util.List;

/**
 * <b>Description:</b><br> 地区bean
 * <AUTHOR>
 * @Note
 * <b>ProjectName:</b> erp
 * <br><b>PackageName:</b> com.vedeng.authorization.model
 * <br><b>ClassName:</b> Region
 * <br><b>Date:</b> 2017年4月25日 上午11:09:21
 */
public class Region implements Serializable{
    private Integer regionId;

    private Integer parentId;

    private String regionName;

    private Integer regionType;

    private Integer agencyId;

    private String regionFullName;
    private String regionCode;
    private List<Region> child;

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }



    public String getRegionFullName() {
        return regionFullName;
    }

    public void setRegionFullName(String regionFullName) {
        this.regionFullName = regionFullName;
    }

    public Integer getRegionId() {
        return regionId;
    }

    public void setRegionId(Integer regionId) {
        this.regionId = regionId;
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public String getRegionName() {
        return regionName;
    }

    public void setRegionName(String regionName) {
        this.regionName = regionName == null ? null : regionName.trim();
    }

    public Integer getRegionType() {
        return regionType;
    }

    public void setRegionType(Integer regionType) {
        this.regionType = regionType;
    }

    public Integer getAgencyId() {
        return agencyId;
    }

    public void setAgencyId(Integer agencyId) {
        this.agencyId = agencyId;
    }

    public List<Region> getChild() {
        return child;
    }

    public void setChild(List<Region> child) {
        this.child = child;
    }
}