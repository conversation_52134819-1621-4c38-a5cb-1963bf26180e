package com.vedeng.erp.trader.service;


import com.github.pagehelper.PageInfo;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.trader.domain.dto.BankMatchConfigGroupDto;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 开户行匹配配置分组
 * @date 2024/8/26 8:56
 */
public interface BankMatchConfigGroupService {


    /**
     * 分页查询
     *
     * @param bankMatchConfigGroup 分页查询条件
     * @return 分页结果
     */
    PageInfo<BankMatchConfigGroupDto> page(PageParam<BankMatchConfigGroupDto> bankMatchConfigGroup);

    /**
     * 查询一条数据
     * @param groupId groupId
     * @return
     */
    BankMatchConfigGroupDto selectOne(Long groupId);

    /**
     * 新增
     * @param bankMatchConfigGroup bankMatchConfigGroup
     */
    void save(BankMatchConfigGroupDto bankMatchConfigGroup);

    /**
     * 修改
     * @param bankMatchConfigGroup bankMatchConfigGroup
     */
    void update(BankMatchConfigGroupDto bankMatchConfigGroup);

    /**
     * 删除
     * @param groupId groupId
     */
    void delete(Long groupId);

}
