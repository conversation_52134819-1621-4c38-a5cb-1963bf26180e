function addNum(obj, num, buyorderGoodsId, saleorderGoodsId) {
    checkLogin();
    var srnum = $(obj).val();
    var numReg = /^([1]?\d{1,10})$/;
    if (srnum == '') {
        layer.alert("数量不能为空");
        $(obj).val(0);
        return false;
    }
    if (srnum != '' && !numReg.test(srnum)) {
        layer.alert("数量必须为正整数且小于11位数字");
        $(obj).val(0);
        return false;
    }
    if (Number(srnum) < 1 || Number(srnum) > Number(num)) {
        layer.alert("数量必须大于1小于" + num);
        $(obj).val(0);
        return false;
    }
    $(obj).siblings("input").val(buyorderGoodsId + "|" + saleorderGoodsId + "|" + srnum);
    var sum = Number(0);
    var goodsId = $(obj).attr("alt1");
    $.each($("input[alt1='" + goodsId + "']"), function (i, n) {
        sum += Number($(this).val());
    });
    $("span[alt='" + goodsId + "']").html(sum);
    $("span[alt='" + goodsId + "']").siblings("input").val(buyorderGoodsId + "|" + sum);
    //计算总件数
    var zSum = Number(0);
    $.each($("span .buyNum"), function (i, n) {
        alert($(this).html());
        zSum += Number($(this).html());
    });
    $("#zSum").html(sum);
    //计算单个总额
    var price = $("input[alt='" + goodsId + "']").val();
    if (price != undefined && price != "") {
        $("span[alt='" + buyorderGoodsId + "']").html(Number(sum) * Number(price));
    }

    //计算总金额
    var allMoney = Number(0);
    $.each($(".oneAllMoney"), function (i, n) {
        allMoney += Number($(this).html());
    });
    $("#zMoney").html(allMoney);


}

function changPrice(obj, buyorderGoodsId, goodsId) {
    checkLogin();
    var price = $(obj).val();
    var priceReg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
    if (price == '') {
        layer.alert("单价不允许为空");
        return false;
    }
    if (price != '' && price.length > 10 && !priceReg.test(price)) {
        layer.alert("单价输入错误！仅允许使用数字，最多精确到小数点后两位");
        return false;
    }
    if (Number(price) > 300000000) {
        layer.alert("单价不允许超过3亿");
        return false;
    }

    $(obj).siblings().val(buyorderGoodsId + "|" + price);
    var num = $("span[alt='" + goodsId + "']").html();

    $("span[alt='" + buyorderGoodsId + "']").html(Number(num) * Number(price));
    //计算总金额
    var allMoney = Number(0);
    $.each($(".oneAllMoney"), function (i, n) {
        allMoney += Number($(this).html());
    });
    $("#zMoney").html(allMoney);
}

function changComments(obj, buyorderGoodsId) {
    checkLogin();
    var insideComments = $(obj).val();
    if (insideComments != '' && insideComments != undefined) {
        $(obj).siblings().val(buyorderGoodsId + "|" + insideComments);
    }

}

function applyReview() {
    checkLogin();
    index = layer.confirm("您是否确认该操作？", {
        btn: ['确定', '取消'] //按钮
    }, function () {
        $("#myform").attr("action", page_url + "/order/buyorder/saveApplyReview.do");
        $("#myform").submit();
        layer.close(index);
    }, function () {
    });
}

function validateBuyOrderAfterSaleStaus() {

    var message = "";

    $.ajax({
        type: "POST",
        url: page_url + "/aftersale/serviceStandard/validateBuyOrderAfterSaleStaus.do",
        data: {'buyorderId': $("#buyorderId").val()},
        dataType: 'json',
        async: false,
        success: function (data) {
            if (data.code != 0) {
                message = data.message;
            }
        },
        error: function (data) {
            if (data.status == 1001) {
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    });

    return message;

}

function newApplyValidBuyorder() {
    checkLogin();

    var zMoney = $("#zMoney").html();
    var number = Number($("#prepaidAmount").val()) + Number($("#accountPeriodAmount").val()) + Number($("#retainageAmount").val()) + Number($("#rebateTotal").html());
    if (Number(zMoney) != number.toFixed(2)) {
        layer.alert("付款信息与订单总金额不相符，请确认");
        return false;
    }

    var flag = false
    $.each($(".price"), function (i, n) {
        if (Number($(this).html()) == 0) {
            flag = true;
            return false;
        }
    });

    var msg = '';
    if (flag) {
        msg += "订单内有商品采购价为0，";
    }

    msg += validateBuyOrderAfterSaleStaus();

    layer.confirm(msg + "是否确认提交审核？", {
        btn: ['确定', '取消'] //按钮
    }, function () {
        $.ajax({
            type: "POST",
            url: "/order/buyorder/checkBuyorder.do",
            data: $('#myform').serialize(),
            dataType: "json",
            success: function (data) {
                if (data.code == 0) {
                    $.ajax({
                        type: "POST",
                        url: "/order/buyorder/editApplyValidBuyorder.do",
                        data: $('#myform').serialize(),
                        dataType: 'json',
                        success: function (data) {
                            if (data.code == 0) {
                                window.location.reload();
                            } else {
                                layer.alert(data.message);
                            }
                        },
                        error: function (data) {
                            if (data.status == 1001) {
                                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
                            }
                        }
                    });
                } else {
                    layer.alert(data.message);
                    layer.confirm(data.message, {btn: ['确认']}, function () {
                        window.location.reload();
                    });
                    return false;
                }

            }
        });

    }, function () {
    });
}

function newEditBuyorder() {
    checkLogin();
    $("#myform").attr("action", page_url + "/order/newBuyorder/newEditBuyorderPage.do");
    $("#myform").submit();
}

function newCloseBuyorder(buyorderId) {
    checkLogin();
    index = layer.confirm("您是否确认该操作？", {
        btn: ['确定', '取消'] //按钮
    }, function () {
        $.ajax({
            type: "POST",
            url: page_url + "/order/buyorder/saveColseBuyorder.do",
            data: {'buyorderId': buyorderId, 'status': 3},
            dataType: 'json',
            success: function (data) {
                if (data.code == 0) {
                    self.location.reload();
                } else {
                    layer.alert(data.message);
                }
            },
            error: function (data) {
                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
        layer.close(index);
    }, function () {
    });
}

function payApplyPass(payApplyId) {
    checkLogin();
    index = layer.confirm("您是否确认通过？", {
        btn: ['确定', '取消'] //按钮
    }, function () {
        $.ajax({
            type: "POST",
            url: page_url + "/finance/buyorder/payApplyPass.do",
            data: {'payApplyId': payApplyId},
            dataType: 'json',
            success: function (data) {
                if (data.code == 0) {
                    self.location.reload();
                } else {
                    layer.alert(data.message);
                }
            },
            error: function (data) {
                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
        layer.close(index);
    }, function () {
    });
}

$(document).ready(function () {
    var buyorderId = $("input[name='buyorderId']").val();
    var deliveryDirect = $("input[name='deliveryDirect']").val();
    var validStatus = $("input[name='validStatus']").val();
    var lockedStatus = $("input[name='lockedStatus']").val();
    var status = $("input[name='status']").val();
    var userId = $("input[name='userId']").val();
    var user_id = $("#user_id").val();
    $.ajax({
        type: "POST",
        url: "/order/buyorder/getSaleBuyNumByAjax.do",
        async: true,
        data: {
            'buyorderId': buyorderId,
            'deliveryDirect': deliveryDirect,
            'validStatus': validStatus,
            'lockedStatus': lockedStatus,
            'status': status,
            'userId': userId
        },
        dataType: 'json',
        success: function (data) {
            //物流管理
            var wlhtml = "";
            //入库记录
            var rkhtml = "";
            var myDate = new Date();
            var buyorderVo = data.data;
            var voList = buyorderVo.buyorderGoodsVoList;
            var expressList = buyorderVo.expressList;
            for (var i = 0; i < voList.length; i++) {
                var bgv = voList[i];
                if (bgv.saleorderGoodsVoList != null && bgv.saleorderGoodsVoList.length > 0) {
                    for (var x = 0; x < bgv.saleorderGoodsVoList.length; x++) {
                        var saleorderGoods = bgv.saleorderGoodsVoList[x];
                        var numHtml = "";
                        if (buyorderVo.deliveryDirect == 1) {
                            //var number = Number(saleorderGoods.num) - Number(saleorderGoods.buyNum) + Number(saleorderGoods.saleBuyNum);
                            //numHtml = numHtml + ((saleorderGoods.saleBuyNum == null || saleorderGoods.saleBuyNum == '') ? 0 : saleorderGoods.saleBuyNum) + "/" + number;
                            var number = Number(saleorderGoods.needBuyNum) + Number((saleorderGoods.saleBuyNum == null || saleorderGoods.saleBuyNum == '') ? 0 : saleorderGoods.saleBuyNum);
                            numHtml = numHtml + ((saleorderGoods.saleBuyNum == null || saleorderGoods.saleBuyNum == '') ? 0 : saleorderGoods.saleBuyNum) + "/" + number;
                        } else if (buyorderVo.deliveryDirect == 0) {
                            var number = Number(saleorderGoods.needBuyNum) + Number((saleorderGoods.saleBuyNum == null || saleorderGoods.saleBuyNum == '') ? 0 : saleorderGoods.saleBuyNum);;
                            numHtml = numHtml + ((saleorderGoods.saleBuyNum == null || saleorderGoods.saleBuyNum == '') ? 0 : saleorderGoods.saleBuyNum) + "/" + number;
                        }
                        $("#" + (i + 1) + "_" + saleorderGoods.saleorderId + "_" + x).html(numHtml);
                    }
                }
            }
           // 物流信息
            if (buyorderVo.expressList != null && buyorderVo.expressList.length > 0) {
                for (var i = 0; i < expressList.length; i++) {
                    var express = expressList[i];
                    var wlScore = 0.00
                    for (var j = 0; j < express.expressDetail.length; j++) {
                        wlScore = Number(wlScore) + Number(express.expressDetail[j].amount);
                    }
                    wlhtml = wlhtml + "<tr><td>" + express.logisticsNo + "</td><td>" + express.logisticsName + "</td>" +
                        "<td>" + formatDate(express.deliveryTime) + "</td>" +
                        "<td>" + wlScore.toFixed(2) + "</td>" +
                        "<td class='text-left'>";
                    for (var z = 0; z < express.expressDetail.length; z++) {
                        var expressDetails = express.expressDetail[z];
                        wlhtml = wlhtml + "<div>" + expressDetails.goodName + "&nbsp;&nbsp;&nbsp;&nbsp;" + expressDetails.num + expressDetails.unitName + "</div><br/>";
                    }
                    wlhtml += "</td>"
                    let arrivalStatus = '';
                    if (2 == express.arrivalStatus) {
                        arrivalStatus = '已签收'
                    }
                    if (0 == express.arrivalStatus) {
                        arrivalStatus = '未签收'
                    }
                    wlhtml += "<td>" + arrivalStatus + "</td>" ;
                    let comment = ' ';
                    if (null != express.logisticsComments){
                        comment = express.logisticsComments;
                    }
                    wlhtml += "<td>"+comment+"</td>";
                    if (buyorderVo.deliveryDirect == 1){
                        let enableReceive = ' ';
                        if(1 == express.enableReceive){
                            enableReceive = '是'
                        }
                        if(0 == express.enableReceive){
                            enableReceive = '否'
                        }
                        wlhtml +="<td>" + enableReceive + "</td>";
                    }
                    wlhtml += "<td>";
                    if (parseInt(buyorderVo.validStatus) == 1 && parseInt(buyorderVo.lockedStatus) != 1 && parseInt(buyorderVo.status) == 1 && buyorderVo.userId == user_id && parseInt(express.arrivalStatus) == 0) {
                        wlhtml = wlhtml + "<span class='bt-smaller bt-border-style border-blue loadMoreAddtitle' " +
                            "tabTitle='{\"num\":\"editExpress\",\"link\":\"./order/buyorder/editExpress.do?" +
                            "expressId=" + express.expressId + "&buyorderId=" + buyorderVo.buyorderId + "\"," +
                            "\"title\":\" 编辑快递\"}'>编辑</span>" + "<span class='bt-smaller bt-border-style border-red' onclick='deleteExpress(" + express.expressId + ")'" + ">删除</span>";
                    }
                    wlhtml = wlhtml + "<sapn class='customername pos_rel> " +
                        "<div class='brand-color1'>" +
                        "<i class='bt-smaller bt-border-style border-blue pop-new-data-dny'" +
                        "layerParams='{\"width\":\"40%\",\"height\":\"420px\",\"title\":\"查看物流\",\"link\":\"" + page_url + "/warehouse/warehousesout/queryExpressInfo.do?" +
                        "logisticsNo=" + express.logisticsNo +"&logisticsId=" + express.logisticsId + "\"}'>查看物流</i>" +
                        "</div><div class='pos_abs customernameshow mouthControlPos'>最新信息：" + express.contentNew + "</div></span>";
                    wlhtml = wlhtml + "</td></tr>";
                }
            }

            if (buyorderVo.expressList != null && buyorderVo.expressList.length > 0) {
                if (buyorderVo.deliveryDirect == 1){
                    wlhtml = wlhtml + "<tr> <td colspan='9' class='allchosetr text-left'>";
                }else {
                    wlhtml = wlhtml + "<tr> <td colspan='8' class='allchosetr text-left'>";
                }
                let allamount = 0.00;
                let allarrivalnum = 0;
                for (let i = 0; i < buyorderVo.expressList.length; i++) {
                    let amount = 0.00;
                    let arrivalnum = 0;
                    for (let j = 0; j < ((buyorderVo.expressList)[i]).expressDetail.length; j++) {
                        amount = Number(amount) + Number(((buyorderVo.expressList)[i]).expressDetail[j].amount);
                        arrivalnum = Number(arrivalnum) + Number((buyorderVo.expressList)[i].expressDetail[j].num);
                    }
                    allamount = (Number(allamount) + Number(amount)).toFixed(2);
                    allarrivalnum = Number(allarrivalnum) + Number(arrivalnum);
                }
                var allnum = 0;
                for (var i = 0; i < buyorderVo.buyorderGoodsVoList.length; i++) {
                    allnum = Number(allnum) + Number(buyorderVo.buyorderGoodsVoList[i].num);
                }
                wlhtml = wlhtml + " 运费总额：<span class='warning-color1 mr10'>" + allamount + "</span>" +
                    "已发/商品总数:  <span class='warning-color1'>" + allarrivalnum + "/" + allnum + "</span>" +
                    "</td></tr>";
            } else {
                if (buyorderVo.deliveryDirect == 1){
                    wlhtml = wlhtml + "<tr><td colspan='9'>暂无记录！</td></tr>";
                }else {
                    wlhtml = wlhtml + "<tr><td colspan='8'>暂无记录！</td></tr>";
                }
            }
            /************************************************************************************/
            var logVoList = buyorderVo.warehouseGoodsOperateLogVoList;
            if (logVoList != null && logVoList.length > 0) {
                num = 0;
                for (var i = 0; i < logVoList.length; i++) {
                    var agolv = logVoList[i];
                    var productDate = '';
                    if (agolv.productDate != 0 && agolv.productDate != null) {
                        productDate = dateFormat(agolv.productDate);
                    }
                    var expirationDate = '';
                    if (agolv.expirationDate != 0) {
                        expirationDate = dateFormat(agolv.expirationDate);
                    }

                    var addTimeDate = '';
                    if (agolv.addTime != 0) {
                        addTimeDate = dateFormat(agolv.addTime);
                    }

                    var portalUrl = ''
                    if (agolv.firstEngageId != null && agolv.registrationNumber != null) {
                        portalUrl = "<a class=\"addtitle\" href=\"javascript:void(0);\" tabTitle='{\"link\":\"./firstengage/baseinfo/getFirstSearchDetail.do?firstEngageId=" + agolv.firstEngageId + "\",\"title\":\"注册证号详情页\"}'>" + agolv.registrationNumber + "</a>"
                    }
                    rkhtml = rkhtml + "<tr>" +
                        "<td>" + (num * 1 + 1) + "</td>" +
                        "<td>" + agolv.sku + "</td>" +
                        "<td class='text-left'>" +
                        "<span class='font-blue cursor-pointer loadMoreAddtitle' tabTitle='{\"num\":" +
                        "\"viewsaleorder" + myDate.getMilliseconds() + parseInt(Math.random() * 10000) + "\"" +
                        ",\"link\":\"./goods/goods/viewbaseinfo.do?goodsId=" + agolv.goodsId + "\"," +
                        "\"title\":\"产品信息\"}'>" + agolv.goodsName + "</span>" + agolv.materialCode + "</td>" +
                        "<td>" + agolv.brandName + "</td>" +
                        "<td>" + agolv.model + "</td>" +
                        "<td>" + agolv.num + "</td>" +
                        "<td>" + agolv.unitName + "</td>" +
                        "<td>" + agolv.vedengBatchNumer + "</td>" +
                        "<td>" + productDate + "</td>" +
                        "<td>" + expirationDate + "</td>" +
                        "<td>" + addTimeDate + "</td>" +
                        "<td>" + (agolv.batchNumber == null ? '' : agolv.batchNumber) + "</td>" +
                        "<td>" + (agolv.sterilizationBatchNo == null ? '' : agolv.sterilizationBatchNo) + "</td>" +
                        "<td>" + portalUrl + "</td>" +
                        "</tr>";
                    num++;
                }
            } else {
                rkhtml = rkhtml + "<tr><td colspan='14'>暂无记录！</td></tr>";
            }
            /************************************************************************************/
            if (wlhtml != "") {
                $("#wulb").find("tr:last").after(wlhtml);
            }
            if (rkhtml != "") {
                $("#rk").find("tr:last").after(rkhtml);
            }
            loadMoreAddTitle();
            loadMoreBlueKuang();
            dnyPopNewdata();
        },
        error: function (data) {
            loadMoreClose();
            if (data.status == 1001) {
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
            }
        }
    });
});

function dateFormat(longTypeDate){
    var dateType = "";
    var date = new Date();
    date.setTime(longTypeDate);
    dateType += date.getFullYear();   //年
    dateType += "-" + (date.getMonth() + 1); //月
    dateType += "-" + date.getDay();   //日
    return dateType;
}

function formatDate(time, format) {
    if (time != null) {
        if (format == null) {
            format = 'YYYY-MM-DD';
        }
        var date = new Date(time);
        var year = date.getFullYear(),
            month = date.getMonth() + 1,//月份是从0开始的
            day = date.getDate(),
            hour = date.getHours(),
            min = date.getMinutes(),
            sec = date.getSeconds();
        var preArr = Array.apply(null, Array(10)).map(function (elem, index) {
            return '0' + index;
        });////开个长度为10的数组 格式为 00 01 02 03

        var newTime = format.replace(/YYYY/g, year)
            .replace(/MM/g, preArr[month] || month)
            .replace(/DD/g, preArr[day] || day)
            .replace(/hh/g, preArr[hour] || hour)
            .replace(/mm/g, preArr[min] || min)
            .replace(/ss/g, preArr[sec] || sec);

        return newTime;
    }
}

function contractReturnDel(attachmentId) {
    checkLogin();
    layer.confirm("确认删除吗？", {
        btn: ['确定', '取消'] //按钮
    }, function () {
        $.ajax({
            type: "POST",
            url: "/order/buyorder/contractReturnDel.do",
            data: {'attachmentId': attachmentId},
            dataType: 'json',
            success: function (data) {
                window.location.reload();
            },
            error: function (data) {
                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    }, function () {
    });
}

function delGeInfoCollection(buyorderGoodsId) {
    checkLogin();
    if (confirm("确定要删除吗？") == true) {
        $.ajax({
            type: "POST",
            url: page_url + "/order/georder/delGeInfoCollection.do",
            data: {"buyorderGoodsId": buyorderGoodsId},
            dataType: 'json',
            success: function (data) {
                if (data.code == 0) {
                    window.location.reload();
                } else {
                    layer.alert(data.message)
                    return false;
                }
            },
            error: function (data) {
                if (data.code == 1001) {
                    layer.alert("没有权限");
                    return false;
                }
            }
        });
    } else {
        return false;
    }


}

function addSncode(sku, goodsId, buyorderGoodsId) {
    checkLogin();
    layer.open({
        type: 2,
        shadeClose: false, //点击遮罩关闭
        area: ["800px", "550px"],
        title: "添加序列号",
        content: "/order/georder/addSncode.do?sku=" + sku + "&&goodsId=" + goodsId + "&&buyorderGoodsId=" + buyorderGoodsId,
    });
}

function updateGeInfoCollection(buyorderGoodsId, geContractNo, geSaleContractNo, sku) {
    checkLogin();
    layer.open({
        type: 2,
        shadeClose: false, //点击遮罩关闭
        area: ["500px", "390px"],
        title: "GE采集信息",
        content: ["/order/georder/geInfoCollection.do?geBuyorderGoodsIds=" + buyorderGoodsId + "&&geContractNo=" + geContractNo + "&&geSaleContractNo=" + geSaleContractNo + "&&sku=" + sku, 'no']
    });
}

function showTaskInfo(obj) {
    var earlyWarningTaskId = $(obj).attr("data-keyId");
    $("#taskInfoDetail")
        .attr(
            'layerParams',
            '{"width":"50%","height":"50%","title":"跟进记录","link":"'
            + page_url
            + '/flash/earlyWarningGoods/followInfoDetail.do?earlyWarningTaskId='
            + earlyWarningTaskId + '"}');
    $("#taskInfoDetail").click();
}

function deleteExpress(expressId) {
    checkLogin();
    layer.confirm("是否确认删除物流信息？", {
        btn: ['确定', '取消'] //按钮
    }, function () {
        $.ajax({
            type: "POST",
            url: "/order/newBuyorder/deleteExpress.do",
            data: {'expressId': expressId},
            dataType: 'json',
            success: function (data) {
                if (data.code == 0) {

                    self.location.reload();
                } else {
                    layer.alert(data.message);
                }
            },
            error: function (data) {
                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
        layer.close(index);
    }, function () {
    });
}

function passContractVerify(buyorderid,addTime) {
    var passIndex = layer.load(0,{shade: [0.1,'#fff']});
    $.ajax({
        type: "POST",
        url: "/order/newBuyorder/complementPurchaseContractTask.do",
        data: {
            "pass": true,
            "buyorderId": buyorderid,
            "addTime": addTime,
        },
        dataType: 'json',
        success: function (data) {
            if (data.code == 0) {
                self.location.reload();
            } else {
                layer.alert(data.message);
            }
            layer.close(passIndex)
        },
        error: function (){
            layer.close(passIndex)
        }
    })
}