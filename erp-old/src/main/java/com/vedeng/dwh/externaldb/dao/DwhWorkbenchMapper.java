package com.vedeng.dwh.externaldb.dao;

import com.vedeng.dwh.externaldb.dao.generate.DwsBussinessChanceWorkbenchDfMapper;
import com.vedeng.dwh.model.dto.DwhQueryParamsDto;
import com.vedeng.dwh.model.dto.DwhWorkbenchDto;
import com.vedeng.dwh.model.dto.DwhWorkbenchOverviewDto;
import java.util.List;

/**
 * workbench mapper.
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2020/11/3 11:10 下午.
 * @author: Tomcat.Hui.
 */
public interface DwhWorkbenchMapper extends DwsBussinessChanceWorkbenchDfMapper {

    /**
     * 获取指定用户数据概况.
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/4 9:08 上午.
     * @author: Tomcat.Hui.
     * @param params: .
     * @return: java.util.List<com.vedeng.dwh.model.dto.DwhWorkbenchDto>.
     * @throw: .
     */
    DwhWorkbenchOverviewDto getUserBussinessChanceOverview(DwhQueryParamsDto params);

    /**
     * 管理用户查看数据概况.
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/4 9:09 上午.
     * @author: Tomcat.Hui.
     * @param params: .
     * @return: java.util.List<com.vedeng.dwh.model.dto.DwhWorkbenchDto>.
     * @throw: .
     */
    List<DwhWorkbenchOverviewDto> getLeaderBussinessChanceOverview(DwhQueryParamsDto params);

    /**
     * 商机状态分布.
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/4 9:09 上午.
     * @author: Tomcat.Hui.
     * @param params: .
     * @return: java.util.List<com.vedeng.dwh.model.dto.DwhWorkbenchDto>.
     * @throw: .
     */
    List<DwhWorkbenchOverviewDto> getBussinessStatus(DwhQueryParamsDto params);

    /**
     * 获取管理视角的商机分布.
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/4 3:55 下午.
     * @author: Tomcat.Hui.
     * @param params: .
     * @return: java.util.List<com.vedeng.dwh.model.dto.DwhWorkbenchOverviewDto>.
     * @throw: .
     */
    List<DwhWorkbenchOverviewDto> getLeaderBussinessStatus(DwhQueryParamsDto params);

    /**
     * 获取重点商机.
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/4 9:24 上午.
     * @author: Tomcat.Hui.
     * @param params: .
     * @return: java.util.List<com.vedeng.dwh.model.dto.DwhWorkbenchDto>.
     * @throw: .
     */
    List<DwhWorkbenchDto> getImportBussinessChance(DwhQueryParamsDto params);

    /**
     * 获取重点商机汇总.
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/4 10:53 上午.
     * @author: Tomcat.Hui.
     * @param params: .
     * @return: java.util.List<com.vedeng.dwh.model.dto.DwhWorkbenchDto>.
     * @throw: .
     */
    List<DwhWorkbenchOverviewDto> getLeaderImportBussinessChance(DwhQueryParamsDto params);
}
