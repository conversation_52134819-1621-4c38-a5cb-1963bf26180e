<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="终端列表" scope="application" />
<%@ include file="../../common/common.jsp"%>

<script type="text/javascript" src='<%= basePath %>static/js/trader/relationInfo.js?rnd=${resourceVersionKey}'></script>

<div class="formpublic formpublic1">
    <div>
        <form method="post" id="search"
                  action="<%= basePath %>trader/relation/searchCustomerList.do?traderId=${traderCustomer.traderId}">
                <div>
                    <!-- 初始模式 -->
                    <ul class="searchTable visible">
                        <li>
                            <div class="infor_name  infor_name6 ">
                                <span>*</span> <label>客户名称</label>
                            </div>

                            <div class="f_left table-larger" style="width: 80%">
                                <div class="mb10">
                                    <input type="text" class="input-larger mr5"
                                           name="searchTraderName" id="searchTraderName"
                                           value="${traderCustomer.searchTraderName}">
                                    <span class="bt-bg-style bt-small bg-light-blue" onclick="search();" id="searchError">搜索</span>
                                </div>
                                <div id="searchNameError"></div>
                            </div>
                            <div>
                                <table class="table table-bordered table-striped table-condensed table-centered mb10">
                                    <thead>
                                    <th>客户名称</th>
                                    <th>地区</th>
                                    <th>创建时间</th>
                                    <th class="table-smallest6">选择</th>
                                    </thead>
                                    <tbody>

                                    <c:if test="${not empty searchCustomerList}">
                                        <c:forEach items="${searchCustomerList}" var="list"
                                                   varStatus="status">
                                            <tr>
                                                <td>${list.traderName}</td>
                                                <td>${list.address}</td>
                                                <td><date:date value="${list.addTime}" /></td>
                                                <c:choose>
                                                    <c:when test="${(list.traderId == traderCustomer.traderId) || list.associatedCustomerGroup > 0}">
                                                        <td>
                                                            <a href="javaScript:void(0)" style="color: grey">选择</a>
                                                        </td>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <td width="5%" style="text-align: center">
                                                            <a href="javaScript:void(0)"
                                                               onclick="associateCustomer(${traderCustomer.traderCustomerId},${list.traderCustomerId})">选择</a>
                                                        </td>
                                                    </c:otherwise>
                                                </c:choose>
                                            </tr>
                                        </c:forEach>
                                    </c:if>
                                    <c:if test="${empty searchCustomerList}">
                                        <tr>
                                            <td colspan="5">查询无结果！请尝试使用其他关键词搜索。</td>
                                        </tr>
                                    </c:if>
                                    </tbody>
                                </table>
                            </div>
                        </li>
                        <li class="visible"><tags:page page="${page}" />
                            <div class="clear"></div></li>
                        <div class="clear"></div>
                    </ul>
                </div>
            </form>
    </div>
</div>
</body>
</html>
