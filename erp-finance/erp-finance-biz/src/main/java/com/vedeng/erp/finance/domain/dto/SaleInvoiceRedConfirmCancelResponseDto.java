package com.vedeng.erp.finance.domain.dto;

import lombok.Data;

/**
 * 红字确认单撤销出参
 */
@Data
public class SaleInvoiceRedConfirmCancelResponseDto extends TaxesReturnInfo {
    /** 撤销成功标志 (Y-成功 N-失败) */
    private String Code;

    /** 撤销返回信息 (成功/失败原因) */
    private String Message;

    /** 红字信息表id */
    private String Uuid;

    /** 红字信息表确认单状态 */
    private String HzqrxxztDm;

    /** 开票标志 */
    private String Kjbz;

    /** 蓝字发票号码 */
    private String Lzfphm;

    /** 蓝字发票代码 */
    private String Lzfpdm;

    /** 购买方税号 */
    private String Gmfnsrsbh;

    /** 销售方税号 */
    private String Xsfnsrsbh;

    /** 蓝字开票日期 */
    private String Lzkprq;

    /** 录入方身份 */
    private String Lrfsf;

    /** 蓝字特殊类型代码 */
    private String LzfpTdyslxDm;

    /** 是否增值税发票 */
    private String Sfzzfpbz;
}
