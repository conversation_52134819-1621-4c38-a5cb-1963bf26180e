<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="选择终端" scope="application" />

<%@ include file="../common/common.jsp"%>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/terminal/selectTerminal.js?rnd=${resourceVersionKey}"></script>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/terminal/selectTerminal.css">
<div class="formpublic">
    <form method="post" action="" id="myform">
        <!-- 不区分编辑还是新增，关联的订单ID -->
        <input type="hidden" name="businessId" value="${requestScope.businessId}"/>
        <!-- 不区分编辑还是新增，关联的场景ID，按原值回传提交接口即可 -->
        <input type="hidden" name="scene" value="${requestScope.scene}"/>
        <!-- 编辑时，此字段不为空 -->
        <input type="hidden" name="businessId" value="${requestScope.terminalId}"/>

        <!-- 终端性质 -->
        <div class="form-group">
            <c:forEach var="list" items="${natureOptionsList}" varStatus="staut">
                <input type="radio" value="${list.sysOptionDefinitionId}" name="natureOption" id="natureOption${list.sysOptionDefinitionId}"/>
                <label for="natureOption${list.sysOptionDefinitionId}">${list.title}</label>
            </c:forEach>
        </div>
        <div class="form-group">
            <label for="terminalName" class="col-sm-2 control-label">终端名称</label>
            <div class="col-sm-10">
                <input type="text" name="terminalName" id="terminalName" value="${requestScope.terminalName}" class="form-control" placeholder="请输入终端名称"/>
            </div>
        </div>
        <div class="form-group">
            <div class="col-sm-offset-2 col-sm-10">
                <button type="submit" class="btn btn-primary">保存</button>
            </div>
        </div>
    </form>
</div>
<%@ include file="../common/footer.jsp"%>
