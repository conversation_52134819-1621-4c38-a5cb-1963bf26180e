function applyValidCustomer(traderCustomerId,taskId){
	checkLogin();
	var formToken = $("input[name='formToken']").val();
	layer.confirm("您是否确认申请审核该用户？", {
		  btn: ['确定','取消'] //按钮
		}, function(){
			$.ajax({
				type: "POST",
				url: "./editApplyValidCustomer.do",
				data: {'traderCustomerId':traderCustomerId,'taskId':taskId,'formToken':formToken},
				dataType:'json',
				success: function(data){
					if (data.code == 0) {
						window.location.reload();
					} else {
						layer.alert(data.message);
					}
					
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
		}, function(){
	});
}


function restVerify(traderCustomerId){
	checkLogin();
	if(traderCustomerId > 0){
		layer.confirm("您是否重置为待审核？", {
			  btn: ['确定','取消'] //按钮
			}, function(){
				$.ajax({
					type: "POST",
					url: "./restverify.do",
					data: {'traderCustomerId':traderCustomerId},
					dataType:'json',
					success: function(data){
						if (data.code == 0) {
							window.location.reload();
						} else {
							layer.alert(data.message);
						}
						
					},
					error:function(data){
						if(data.status ==1001){
							layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
						}
					}
				});
			}, function(){
		});
	}
}

function upateTerminal(traderCustomerTerminalId) {

	layer.open({
		type: 2,
		content: '/traderChaining/edit.do?traderCustomerTerminalId=' + traderCustomerTerminalId,
		area: ['1200px', '600px'],
		title :'修改依据',
		moveOut: true,
		maxmin: true

	});
}

function queryTerminalRecord(traderCustomerTerminalId,name) {

	layer.open({
		type: 2,
		content: '/traderChaining/auditRecord.do?traderCustomerTerminalId=' + traderCustomerTerminalId+"&name="+name,
		area: ['1200px', '600px'],
		title :'审核记录',
		moveOut: true,
		maxmin: true,
		btn: ['返回'],
		yes: function (index, layero) {
			layer.close(index);
		}

	});
}

function addTerminalChaining(traderId, traderCustomerId) {
	let link = '/traderChaining/addView.do?traderId='+traderId+'&traderCustomerId='+traderCustomerId;
	var uniqueName = link.replace('./', '').replace(/["&'./:=%?[\]]/gi, '-').replace(/(--)/gi, '') +'-'+ new Date().getTime();
	window.parent.postMessage({
		from:'ez',
		name: "添加终端建链",
		url:link,
		id:"tab-"+uniqueName
	}, '*');
}