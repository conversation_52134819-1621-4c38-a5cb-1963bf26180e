package com.wms.dto;

/**
 * <AUTHOR>
 * @ClassName WmsStockRequest.java
 * @Description TODO 查询wms可用库存dto
 * @createTime 2020年08月11日 09:10:00
 */
public class WmsStockRequest {

    private String sku;
    //逻辑仓code
    private String lotAtt08;
    //每页记录数
    private Integer pageSize;
    //页数, 当前获取页数
    private Integer pageNo;

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getLotAtt08() {
        return lotAtt08;
    }

    public void setLotAtt08(String lotAtt08) {
        this.lotAtt08 = lotAtt08;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }
}
