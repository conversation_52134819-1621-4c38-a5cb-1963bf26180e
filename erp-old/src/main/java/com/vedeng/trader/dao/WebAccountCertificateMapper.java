package com.vedeng.trader.dao;


import com.vedeng.trader.model.WebAccountCertificate;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WebAccountCertificateMapper {

    List<WebAccountCertificate> getCertificateList(WebAccountCertificate webAccountCertificate);

    void deleteCertificateByWebAccountId(@Param("webAccountId") int webAccountId);

    int deleteByPrimaryKey(Integer webAccountCertificateId);

    int updateByPrimaryKeySelective(WebAccountCertificate record);

    /**
     * 根据用户id来查询用户提交的资质数量
     * @param webAccountId
     * @return
     */
    Integer getCountByWebAccountId(Integer webAccountId);
}