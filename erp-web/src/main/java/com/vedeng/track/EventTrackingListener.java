package com.vedeng.track;

import com.vedeng.common.trace.data.TrackParamsData;
import com.vedeng.common.trace.track.EventTrackingEvent;
import com.vedeng.common.trace.track.TrackStrategy;
import com.vedeng.common.trace.track.factory.TrackStrategyFactory;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 监听spring事件进行处理
 * @ClassName:  EventTrackingListener
 * @author: <PERSON>.yang
 * @date:   2024年6月3日 下午3:23:59
 * @Copyright:
 */
//@Component
@Deprecated
public class EventTrackingListener implements ApplicationListener<EventTrackingEvent>{

    private static final Logger LOG = LoggerFactory.getLogger(EventTrackingListener.class);

    @Autowired
    private TrackStrategyFactory trackStrategyFactory;

    @Value("${trackLogOn:true}")
    private boolean trackLogOn;
    
    @Async("trackThreadPoolExecutor")
	@Override
	public void onApplicationEvent(EventTrackingEvent event) {
		TrackParamsData trackParamsData =  (TrackParamsData) event.getSource();
        if(trackLogOn) {
        	LOG.info("================执行线程{}================",Thread.currentThread().getName());
            LOG.info("================监听到埋点事件，参数：{}=================",trackParamsData);
        }
        TrackStrategy trackStrategy = trackStrategyFactory.getStrategyByType(trackParamsData.getEventTrackingEnum());
        trackStrategy.track(trackParamsData);
	}

}
