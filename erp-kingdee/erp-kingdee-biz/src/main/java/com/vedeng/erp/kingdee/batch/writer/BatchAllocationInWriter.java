package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeeAllocationDto;
import com.vedeng.erp.kingdee.service.KingDeeAllocationApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 外借入库单
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchAllocationInWriter extends BaseWriter<KingDeeAllocationDto> {

    @Autowired
    private KingDeeAllocationApiService kingDeeAllocationApiService;


    @Override
    public void doWrite(KingDeeAllocationDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("BatchAllocationInWriterService#doWrite,外借入库单,{}", JSON.toJSONString(item));
        item.setKingDeeBizEnums(KingDeeBizEnums.saveAllocation);
        kingDeeAllocationApiService.register(item,true);
    }
}
