package com.vedeng.goods.supprot;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Data
public abstract class BaseGoodsDo implements Ordinal {

    private final static Integer DEFAULT_ORDINAL = 0;

    private Integer id;

    private Integer ordinal;

    private Integer isDeleted;

    private Integer creatorId;

    private Integer updaterId;

    private Date addTime;

    private Date modTime;


    @Override
    public Integer getOrdinal() {
        return ordinal == null ? DEFAULT_ORDINAL : ordinal;
    }
}
