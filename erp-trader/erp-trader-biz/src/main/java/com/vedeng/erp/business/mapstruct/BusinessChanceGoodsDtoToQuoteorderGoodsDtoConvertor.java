package com.vedeng.erp.business.mapstruct;

import com.vedeng.erp.business.domain.dto.BusinessChanceGoodsDto;
import com.vedeng.erp.trader.domain.dto.QuoteorderGoodsDto;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @description 商机商品 dto 转换 报价商品 dto
 * @date 2022/7/13 14:03
 **/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface BusinessChanceGoodsDtoToQuoteorderGoodsDtoConvertor {

    /**
     * 商机商品对象 转 报价商品对象
     * @param businessChanceGoodsDto 商机对象
     * @return QuoteorderGoodsDto 报价对象
     */
    QuoteorderGoodsDto busChanceGoodsToQuoteGoodsDto(BusinessChanceGoodsDto businessChanceGoodsDto);

    /**
     *  商机商品集合对象 转 报价商品集合对象
     * @param businessChanceGoodsDtos 商机商品集合
     * @return List<QuoteorderGoodsDto> 报价商品集合
     */
    List<QuoteorderGoodsDto> busChanceGoodsListToQuoteGoodsDtoList(List<BusinessChanceGoodsDto> businessChanceGoodsDtos);
}
