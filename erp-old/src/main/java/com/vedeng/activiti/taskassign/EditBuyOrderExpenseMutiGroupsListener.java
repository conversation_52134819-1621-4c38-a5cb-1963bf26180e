package com.vedeng.activiti.taskassign;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;
import com.vedeng.erp.buyorder.service.BuyorderExpenseApiService;
import com.vedeng.goods.dto.ProductManageAndAsistDto;
import com.vedeng.goods.service.GoodsApiService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 采购费用单会签
 * @date 2022/10/22 14:20
 **/
@Slf4j
public class EditBuyOrderExpenseMutiGroupsListener implements TaskListener {

    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
    private BuyorderExpenseApiService buyorderExpenseApiService = (BuyorderExpenseApiService) context.getBean("buyorderExpenseServiceImpl");
    private GoodsApiService GoodsApiService = (GoodsApiService) context.getBean("GoodsAPIService");

    @Override
    public void notify(DelegateTask delegateTask) {

        BuyorderExpenseDto buyorderInfo = (BuyorderExpenseDto) delegateTask.getVariable("buyorderExpenseInfo");
        if (buyorderInfo != null) {
            buyorderExpenseApiService.doAudit(buyorderInfo.getBuyorderExpenseId());
            List<String> skus = buyorderExpenseApiService.getProductManageAndAsistNameList(buyorderInfo.getBuyorderExpenseId());
            List<ProductManageAndAsistDto> productManageAndAsistDtos = GoodsApiService.batchQueryProductManageAndAsist(skus);
            List<List<String>> productManageAndAsistNameList = getProductManageAndAsistNameList(productManageAndAsistDtos);
            delegateTask.setVariable("passCount", 0);

            if (CollUtil.isNotEmpty(productManageAndAsistNameList)) {
                delegateTask.setVariable("partyList", productManageAndAsistNameList);
            }
            String preAssignee = delegateTask.getVariable("currentAssinee").toString();

            Map<String, String> variableMap = new HashedMap();
            // 共用采购单的模板 些buyorderNo
            variableMap.put("orderNo", buyorderInfo.getBuyorderExpenseNo());
            String url = "./buyorderExpense/details.do?buyorderExpenseId=" + buyorderInfo.getBuyorderExpenseId();
            // 发送消息
            MessageUtil.sendMessage(236, getProductManageAndAsistIdList(productManageAndAsistDtos), variableMap, url, preAssignee);

        }

    }

    List<List<String>> getProductManageAndAsistNameList(List<ProductManageAndAsistDto> productManageAndAsistDtos) {

        List<List<String>> manageAndAsistNameSet = new ArrayList<>();

        productManageAndAsistDtos.forEach(manageAndAsist -> {
            Set<String> name = new HashSet<>();
            if (StrUtil.isNotEmpty(manageAndAsist.getProductManageName())) {
                name.add(manageAndAsist.getProductManageName());
            }
            if (StrUtil.isNotEmpty(manageAndAsist.getProductAssitName())) {
                name.add(manageAndAsist.getProductAssitName());
            }
            if (CollUtil.isNotEmpty(name)) {
                manageAndAsistNameSet.add(new ArrayList<>(name));
            }

        });

        return manageAndAsistNameSet;
    }
    List<Integer> getProductManageAndAsistIdList(List<ProductManageAndAsistDto> productManageAndAsistDtos) {

        Set<Integer> manageAndAsistNameIdSet = new HashSet<>();

        productManageAndAsistDtos.forEach(manageAndAsist -> {

            if (StringUtils.isNotEmpty(manageAndAsist.getProductManageName())) {
                manageAndAsistNameIdSet.add(manageAndAsist.getProductManageUserId());
            }

            if (StringUtils.isNotEmpty(manageAndAsist.getProductAssitName())) {
                manageAndAsistNameIdSet.add(manageAndAsist.getProductAssitUserId());
            }


        });

        return new ArrayList<>(manageAndAsistNameIdSet);
    }

}
