package com.newtask.celery.cache;

import com.newtask.celery.model.JoinPointModel;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: Cherny.chen
 * @Create: 2021/7/14 14:16
 */
public class JoinPointCache {
    private final static Map<String, JoinPointModel> UUID_JOIN_POINT_CACHE = new ConcurrentHashMap<>(64);

    public static boolean haveCache(String uuid) {
        return UUID_JOIN_POINT_CACHE.containsKey(uuid);
    }

    public static JoinPointModel getJointPointModel(String uuid) {
        return UUID_JOIN_POINT_CACHE.get(uuid);
    }

    public static void put(String uuid, JoinPointModel joinPointModel) {
        UUID_JOIN_POINT_CACHE.put(uuid, joinPointModel);
    }


}
