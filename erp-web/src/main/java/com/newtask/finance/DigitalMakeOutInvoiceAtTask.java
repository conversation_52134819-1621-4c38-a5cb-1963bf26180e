package com.newtask.finance;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.enums.InvoiceApplyCheckRuleEnum;
import com.vedeng.common.redis.redission.RedissonLockUtils;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.finance.dto.InvoiceApplyDto;
import com.vedeng.erp.finance.dto.InvoiceCheckRequestDto;
import com.vedeng.erp.finance.dto.InvoiceCheckResultDto;
import com.vedeng.erp.finance.dto.OpenInvoiceResultDto;
import com.vedeng.erp.finance.enums.CheckChainEnum;
import com.vedeng.erp.finance.enums.SalesOpenInvoiceTypeEnum;
import com.vedeng.erp.finance.service.FullyDigitalInvoiceApiService;
import com.vedeng.erp.finance.service.InvoiceApplyApiService;
import com.vedeng.erp.finance.service.InvoiceCheckApiService;
import com.vedeng.erp.system.service.SysOptionDefinitionApiService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.weaver.ast.Var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 数电开票售后安调任务
 */
@JobHandler("DigitalMakeOutInvoiceAtTask")
@Component
@Slf4j
public class DigitalMakeOutInvoiceAtTask extends AbstractJobHandler {

    @Autowired
    private InvoiceApplyApiService invoiceApplyApiService;

    @Autowired
    private FullyDigitalInvoiceApiService fullyDigitalInvoiceApiService;

    @Autowired
    private SysOptionDefinitionApiService sysOptionDefinitionApiService;

    @Autowired
    private InvoiceCheckApiService invoiceCheckApiService;

    // 排除的日志
    @Value("#{'${excluded.log.content.list}'.split(',')}")
    private List<String> excludedLogContentList;

    /**
     * 数电开票售后安调任务
     * @param param
     * @return
     * @throws Exception
     */
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        log.info("数电开票售后安调任务开始执行");
        // 判断自动开票开关是否开启
        // 是否开启数电发票
        Boolean afterSalesAutoInvoiceFlag = sysOptionDefinitionApiService.getInvoiceApplyButtonShow(InvoiceApplyCheckRuleEnum.AFTER_SALES_ORDER_AUTO_INVOICE);
        if (!afterSalesAutoInvoiceFlag) {
            log.info("财务未开启自动开关，执行结束");
            return ReturnT.SUCCESS;
        }

        boolean lock = RedissonLockUtils.tryLock(ErpConstant.INVOICING_AFTERMARKET_OPEN_TASK,0,2, TimeUnit.HOURS);

        if (!lock) {
            log.info("定时任务加锁失败，触发开票,存在处理中的开票业务");
            return ReturnT.SUCCESS;
        }

        try {
            int pageSize = 1000;
            int pageNum = 1;
            while (true) {
                // 分页查询所有未开票的售后安调单
                Page<Object> page = new Page<>(pageNum, pageSize);
                PageInfo<InvoiceApplyDto> pageInfo = invoiceApplyApiService.getAtWaitInvoiceApply(page, param);
                List<InvoiceApplyDto> passCheckList = pageInfo.getList().stream().filter(invoiceApply -> {
                    // 开票规则校验
                    InvoiceCheckRequestDto invoiceCheckRequestDto = getInvoiceCheckRequestDto(invoiceApply);
                    InvoiceCheckResultDto invoiceCheckResultDto = invoiceCheckApiService.openCheck(invoiceCheckRequestDto);
                    return invoiceCheckResultDto.getSuccess();
                }).collect(Collectors.toList());
                // 调用开票
                passCheckList.forEach(invoiceApplyDto -> {
                    OpenInvoiceResultDto openInvoiceResultDto = fullyDigitalInvoiceApiService.openSaleInvoice(invoiceApplyDto, SalesOpenInvoiceTypeEnum.AT_OPEN_INVOICE);
                    if (!openInvoiceResultDto.isSuccess()) {
                        String msg = openInvoiceResultDto.getMsg();
                        if (excludedLogContentList.stream().noneMatch(msg::contains)) {
                            log.error("数电发票开票定时任务异常{}", msg);
                        } else {
                            log.info("数电发票开票定时任务异常:{}", msg);
                        }
                    }
                });
                if (!pageInfo.isHasNextPage()) {
                    break;
                }
                pageNum = pageInfo.getNextPage();
            }
        } catch (Exception e) {
            log.error("数电发票开票定时任务异常:", e);
        }finally {
            RedissonLockUtils.unlock(ErpConstant.INVOICING_AFTERMARKET_OPEN_TASK);
        }
        return ReturnT.SUCCESS;
    }

    private static InvoiceCheckRequestDto getInvoiceCheckRequestDto(InvoiceApplyDto invoiceApply) {
        InvoiceCheckRequestDto invoiceCheckRequestDto = new InvoiceCheckRequestDto();
        invoiceCheckRequestDto.setCheckChainEnum(CheckChainEnum.INVOICE_OPEN_AFTER);
        invoiceCheckRequestDto.setInvoiceApplyId(invoiceApply.getInvoiceApplyId());
        invoiceCheckRequestDto.setInvoiceProperty(invoiceApply.getInvoiceProperty());
        invoiceCheckRequestDto.setType(invoiceApply.getType());
        invoiceCheckRequestDto.setRelatedId(invoiceApply.getRelatedId());
        invoiceCheckRequestDto.setInvoiceInfoType(invoiceApply.getInvoiceInfoType());
        invoiceCheckRequestDto.setInvoiceMessage(invoiceApply.getInvoiceMessage());
        invoiceCheckRequestDto.setDetailList(invoiceApply.getInvoiceApplyDetailDtoList().stream().map(invoiceApplyDetailDto -> {
            InvoiceCheckRequestDto.InvoiceCheckRequestDetailDto invoiceCheckRequestDetailDto = new InvoiceCheckRequestDto.InvoiceCheckRequestDetailDto();
            invoiceCheckRequestDetailDto.setInvoiceApplyDetailId(invoiceApplyDetailDto.getInvoiceApplyDetailId());
            invoiceCheckRequestDetailDto.setDetailGoodsId(invoiceApplyDetailDto.getDetailgoodsId());
            invoiceCheckRequestDetailDto.setPrice(invoiceApplyDetailDto.getPrice());
            invoiceCheckRequestDetailDto.setNum(invoiceApplyDetailDto.getNum());
            invoiceCheckRequestDetailDto.setTotalAmount(invoiceApplyDetailDto.getTotalAmount());
            return invoiceCheckRequestDetailDto;
        }).collect(Collectors.toList()));
        return invoiceCheckRequestDto;
    }
}
