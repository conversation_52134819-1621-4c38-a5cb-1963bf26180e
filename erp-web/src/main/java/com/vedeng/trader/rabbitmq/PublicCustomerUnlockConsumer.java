package com.vedeng.trader.rabbitmq;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.rabbitmq.client.Channel;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.call.service.CallService;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.trace.data.TrackParamsData;
import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.common.trace.mq.AbstractMessageListener;
import com.vedeng.common.trace.track.TrackStrategy;
import com.vedeng.common.trace.track.factory.TrackStrategyFactory;
import com.vedeng.erp.trader.domain.PublicCustomerRecord;
import com.vedeng.erp.trader.domain.dto.TraderCustomerDto;
import com.vedeng.erp.trader.mapper.PublicCustomerRecordMapper;
import com.vedeng.erp.trader.mapper.PublicTraderMapper;
import com.vedeng.order.dao.BussinessChanceMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.BussinessChance;
import com.vedeng.order.model.Saleorder;
import com.vedeng.trader.dao.CommunicateRecordMapper;
import com.vedeng.trader.dao.RTraderJUserMapper;
import com.vedeng.trader.dao.TraderCustomerMapper;
import com.vedeng.trader.model.CommunicateRecord;
import com.vedeng.trader.model.RTraderJUser;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.TraderCustomer;
import com.vedeng.trader.service.TraderCustomerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 监听公私海客户24H后解锁
 *
 * <AUTHOR>
 * @create 2022/2/15 15:55
 */
@Component
@Slf4j
public class PublicCustomerUnlockConsumer extends AbstractMessageListener {

    @Autowired
    private TraderCustomerService traderCustomerService;

    @Resource
    private UserMapper userMapper;

    @Resource
    private PublicTraderMapper publicTraderMapper;

    @Autowired
    @Qualifier("rTraderJUserMapper")
    private RTraderJUserMapper rTraderJUserMapper;

    @Resource
    private TraderCustomerMapper traderCustomerMapper;

    @Resource
    private PublicCustomerRecordMapper publicCustomerRecordMapper;

    @Resource
    private CommunicateRecordMapper communicateRecordMapper;

    @Resource
    private BussinessChanceMapper bussinessChanceMapper;

    @Autowired
    private SaleorderMapper saleorderMapper;
    
    @Autowired
    private TrackStrategyFactory trackStrategyFactory;
    
    /**
     * 公海虚拟账户配置人员ID
     */
    @Value("${VIRTUAL_PUBLIC_CUSTOMER_USER_ID}")
    private Integer virtualPublicCustomerUserId;

    @Autowired
    private CallService callService;

    @Override
    public void doBusiness(Message message, Channel channel) {

        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("公私海客户24H后解锁-回传消息：[{}]", message);
        try {
            handleMessage(messageBody);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), Boolean.FALSE);
        } catch (Exception e) {
            log.error("公私海客户24H后解锁-发生错误，公海客户：{}，错误信息：", messageBody, e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), Boolean.FALSE, Boolean.FALSE);
            } catch (IOException ex) {
                log.error("公私海客户24H后解锁-消费失败，将消息返回给rabbitmq错误：", ex);
            }
        }

    }

    /**
     * 处理锁定客户
     *
     * @param message message
     */
    private void handleMessage(String message) {

        List<Integer> traderCustomerIdList = JSONUtil.toList(message, Integer.class);
        if (CollectionUtil.isEmpty(traderCustomerIdList)) {
            throw new RuntimeException("公私海客户24H后解锁{" + message + "} 解析失败");
        }
        User virtualUser = userMapper.selectByPrimaryKey(virtualPublicCustomerUserId);

        traderCustomerIdList.forEach(traderCustomerId -> {
            Optional.ofNullable(getRTraderJUser(traderCustomerId))
                    .ifPresent(ru -> {
                        Trader trader = traderCustomerMapper.getTraderByTraderCustomerId(traderCustomerId);
                        if(ErpConst.ONE.equals(trader.getBelongPlatform())) {
                            List<PublicCustomerRecord> publicCustomerRecordList =
                                    publicCustomerRecordMapper.getPublicCustomerRecordListByTraderCustomerId(traderCustomerId);
                            if (CollectionUtils.isEmpty(publicCustomerRecordList)) {
                                log.error("客户：{}不存在公海记录", traderCustomerId);
                                return;
                            }
                            PublicCustomerRecord lastPublicCustomerRecord = publicCustomerRecordList.get(publicCustomerRecordList.size() - 1);
                            Long lastPrivatizedTime = lastPublicCustomerRecord.getPrivatizedTime();
                            if (lastPrivatizedTime == null || lastPrivatizedTime == 0) {
                                log.error("客户：{}未发生锁定操作，不能进行解锁逻辑", traderCustomerId);
                                return;
                            }
                            if (lastPublicCustomerRecord.getIsUnlock() > 0) {
                                log.error("客户：{}已经发生解锁操作", traderCustomerId);
                                return;
                            }
                            // 解锁校验处理
                            Map<String, Object> result = unlockValidHandle(ru, lastPrivatizedTime);

                            // 解锁成功
                            if (Integer.parseInt(result.get("unlock").toString()) > 0) {
                                // 更新记录表字段
                                Integer publicTraderRecordId = publicTraderMapper.selectByTraderCustomerIdAndStates(traderCustomerId);
                                if (publicTraderRecordId == null) {
                                    log.info("{} 解锁条件符合，但解锁失败，对应IS_PRIVATIZED、IS_UNLOCK字段错误", traderCustomerId);
                                    return;
                                }
                                publicTraderMapper.updatePublicCustomerRecordByPrimaryKey(publicTraderRecordId,
                                        Integer.parseInt(result.get("unlock").toString()), Integer.parseInt(result.get("unlock_related_id").toString())
                                        , Long.parseLong(result.get("unlock_time").toString()));

                                //公海客户解锁成功，进行埋点
                                //VDERP-17057  【客户档案】ERP客户档案时间轴 公海解锁
                                Map<String, Object> trackParams = new HashMap<>();
                        		trackParams.put("traderId", ru.getTraderId());
                        		Integer userId = ru.getUserId();
                        		User user = userMapper.selectByPrimaryKey(userId);
                        		trackParams.put("track_user", user);
                        		TrackStrategy trackStrategy = trackStrategyFactory.getStrategyByType(EventTrackingEnum.BASE_INFO_CUSTOMER_RELEASE);
                        		TrackParamsData trackParamsData = new TrackParamsData();
                    			trackParamsData.setEventTrackingEnum(EventTrackingEnum.BASE_INFO_CUSTOMER_RELEASE);
                    			trackParamsData.setTrackParams(trackParams);
                        		trackParamsData.setTrackResult(ResultInfo.success());
                        		trackStrategy.track(trackParamsData);
                                
                            } else {
                                //解锁失败
                                if (publicCustomerRecordMapper.getUnPrivatizedByCustomerId(traderCustomerId) > 0) {
                                    return;
                                }
                                // 重新分配给虚拟销售1726
                                traderCustomerService.assignSingleCustomer(ru.getTraderId(), virtualUser.getUserId(), virtualUser.getCompanyId(), virtualUser,null);
                                // 重新进入公海列表
                                TraderCustomerDto traderCustomerDto = new TraderCustomerDto();
                                traderCustomerDto.setTraderCustomerId(traderCustomerId);
                                traderCustomerDto.setUserId(lastPublicCustomerRecord.getOriginUserId());
                                List<TraderCustomerDto> operationList = new ArrayList<>();
                                operationList.add(traderCustomerDto);
                                publicCustomerRecordMapper.batchSavePublicCustomerRecords(operationList);
                            }
                        }else {
                            return;
                        }
                    });
        });

    }

    /**
     * 获取当前关联关系
     *
     * @param traderCustomerId traderCustomerId
     * @return RTraderJUser
     */
    private RTraderJUser getRTraderJUser(Integer traderCustomerId) {
        TraderCustomer traderCustomer = traderCustomerMapper.selectByPrimaryKey(traderCustomerId);
        if (Objects.isNull(traderCustomer)) {
            log.info("{} 解锁条件失败，重新进入公海列表失败 traderCustomer -- {}", traderCustomerId, traderCustomer);
            return null;
        }
        RTraderJUser ru = rTraderJUserMapper.getUserByTraderId(traderCustomer.getTraderId());
        if (Objects.isNull(ru) || Objects.isNull(ru.getUserId())) {
            log.info("{} 解锁条件失败，重新进入公海列表失败 ru -- {}", traderCustomerId, ru);
            return null;
        }
        return ru;
    }


    /**
     * 解锁校验处理
     * 公海锁定客户后，销售24小时内对客户有接通或客户有商机（含销售自主创建、总机创建、前台客户创建等）行为，
     * 该客户在计算下一轮掉入公海前，长久停留在客户列表的过程；
     * 接通：公海相关接通标准均为通话时长大于等于120秒
     *
     * @param rTraderJUser    rTraderJUser
     * @param privatizedTime 公海客户锁定时间
     */
    private Map<String, Object> unlockValidHandle(RTraderJUser rTraderJUser, Long privatizedTime) {
        Map<String, Object> result = new HashMap<>();
        result.put("unlock",0);

        // 客户有商机（含销售自主创建、总机创建、前台客户创建等）
        BussinessChance bussinessChance = bussinessChanceMapper.selectBusinessChanceTimesByTraderId(rTraderJUser.getTraderId(),privatizedTime);
        if (Objects.nonNull(bussinessChance)) {
            result.put("unlock",ErpConst.ONE);
            result.put("unlock_related_id",bussinessChance.getBussinessChanceId());
            result.put("unlock_time",bussinessChance.getAddTime());
            return result;
        }

        //客户有生效订单
        Saleorder saleorder = saleorderMapper.getFirstOrderOfTraderAfterSomeTime(rTraderJUser.getTraderId(),privatizedTime);
        if (saleorder != null) {
            result.put("unlock",ErpConst.THREE);
            result.put("unlock_related_id",saleorder.getSaleorderId());
            result.put("unlock_time",saleorder.getAddTime());
            return result;
        }

        // 销售24小时内对客户有接通
        List<CommunicateRecord> communicateRecordList = communicateRecordMapper.selectValidCommunicationsByTraderId(rTraderJUser.getTraderId(),
                privatizedTime);
        List<String> unSyncCoidList = communicateRecordList.stream()
                .filter(item -> item.getCoid() != null && item.getCoidLength() == null)
                .map(CommunicateRecord::getCoid).distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(unSyncCoidList)){
            Map<String,Map<String,Object>> unSyncCoidRes = callService.getRecordListByCoid(unSyncCoidList).stream()
                    .collect(Collectors.toMap(map -> map.get("COID").toString(), item -> item,(k1,k2)->k1));
            log.info("unlockValidHandle查询到的cc通话数据：{}", JSON.toJSONString(unSyncCoidRes));
            communicateRecordList.forEach(
                    item -> {
                        if (item.getCoid() != null && unSyncCoidRes.containsKey(item.getCoid())){
                            int coidLenght = Integer.parseInt(unSyncCoidRes.get(item.getCoid()).get("FILELEN").toString());
                            item.setCoidLength(coidLenght);
                        }
                    }
            );
        }

        if (CollectionUtils.isNotEmpty(communicateRecordList)){
            CommunicateRecord unlockCommunicateRecord =
                    communicateRecordList.stream().filter(item -> item.getCoidLength()!=null&&item.getCoidLength() >= 120).findFirst().orElse(null);
            if (unlockCommunicateRecord != null) {
                result.put("unlock",ErpConst.TWO);
                result.put("unlock_related_id",unlockCommunicateRecord.getCommunicateRecordId());
                result.put("unlock_time",unlockCommunicateRecord.getAddTime());
                return result;
            }
        }

        return result;
    }

}
