package com.vedeng.erp.broadcast.web.api;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.R;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.broadcast.service.BroadcastUserService;
import com.vedeng.erp.broadcast.domain.dto.BroadCastUserListDto;
import com.vedeng.erp.broadcast.domain.dto.BroadCastUserQueryDto;
import com.vedeng.erp.broadcast.domain.dto.BroadCastUserDetailDto;
import com.vedeng.erp.broadcast.domain.dto.BroadCastUserFormDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import javax.validation.Valid;
import java.util.List;

/**
 * 播报用户管理API控制器
 * 提供人员管理相关的RESTful接口
 */
@RestController
@RequestMapping("/broadcast/user")
@Slf4j
public class BroadCastUserApiController {

    @Autowired
    private BroadcastUserService broadcastUserService;

    /**
     * 人员管理页面
     * @return ModelAndView
     */
    @NoNeedAccessAuthorization
    @RequestMapping("/list")
    public ModelAndView list() {
        return new ModelAndView("receiptnotice/staff");
    }

    /**
     * 分页查询播报用户列表
     * 支持用户名模糊查询和AED销售筛选
     * 
     * @param pageParam 分页查询参数，包含分页信息和查询条件
     * @return 分页用户列表，包含总数、页码等分页信息
     */
    @ResponseBody
    @RequestMapping("/getUserListPage")
    @NoNeedAccessAuthorization
    public R<PageInfo<BroadCastUserListDto>> getUserListPage(@RequestBody PageParam<BroadCastUserQueryDto> pageParam) {
        try {
            PageInfo<BroadCastUserListDto> result = broadcastUserService.getBroadcastUserListPage(pageParam);
            return R.success(result);
        } catch (Exception e) {
            log.error("分页查询播报用户列表失败", e);
            return R.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取全量AED销售列表
     * 用于下拉选择框
     * 
     * @param username 可选的用户名搜索参数，支持模糊查询
     * @return AED销售列表
     */
    @ResponseBody
    @RequestMapping("/getAllAedUserList")
    @NoNeedAccessAuthorization
    public R<List<BroadCastUserDetailDto.BroadCastAedUserDto>> getAllAedUserList(@RequestParam(value = "username", required = false) String username) {
        try {
            List<BroadCastUserDetailDto.BroadCastAedUserDto> result = broadcastUserService.getAllAedUserList(username);
            return R.success(result);
        } catch (Exception e) {
            log.error("获取AED销售列表失败", e);
            return R.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 新增或编辑播报用户
     * 根据表单中的id字段判断操作类型：
     * - id为null或0: 新增操作
     * - id不为null且大于0: 编辑操作
     * 
     * @param formDto 播报用户表单数据
     * @return 操作结果
     */
    @ResponseBody
    @RequestMapping("/saveOrUpdate")
    public R<String> saveOrUpdate(@Valid @RequestBody BroadCastUserFormDto formDto) {
        try {
            boolean isUpdate = formDto.getId() != null && formDto.getId() > 0;
            
            if (isUpdate) {
                // 编辑操作
                broadcastUserService.updateBroadcastUser(formDto);
                return R.success("编辑成功");
            } else {
                // 新增操作
                broadcastUserService.saveBroadcastUser(formDto);
                return R.success("新增成功");
            }
        } catch (Exception e) {
            String operation = (formDto.getId() != null && formDto.getId() > 0) ? "编辑" : "新增";
            log.error("{}播报用户失败", operation, e);
            return R.error(operation + "失败：" + e.getMessage());
        }
    }
    
    /**
     * 删除播报用户
     * 根据ID逻辑删除播报用户关联关系
     * 
     * @param id 播报用户关联关系ID
     * @return 操作结果
     */
    @ResponseBody
    @RequestMapping("/delete")
    public R<String> delete(@RequestParam("id") Integer id) {
        try {
            broadcastUserService.deleteBroadcastUser(id);
            return R.success("删除成功");
        } catch (Exception e) {
            log.error("删除播报用户失败", e);
            return R.error("删除失败：" + e.getMessage());
        }
    }
}
