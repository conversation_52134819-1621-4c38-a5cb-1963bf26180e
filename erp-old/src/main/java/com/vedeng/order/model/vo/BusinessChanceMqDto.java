package com.vedeng.order.model.vo;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 前台通过MQ推送的商机消息体
 * <AUTHOR>
 * @date created in 2020/3/27 10:31
 */
@Data
public class BusinessChanceMqDto {

    /**
     * 商机编号
     */
    private String businessCode;

    /**
     * 商机来源
     */
    private Integer source;

    /**
     * 咨询入口：咨 询入口：首页（1）、商品详情页（2），搜索结果页（3）、产品导航页（4）、品牌中心（5）、
     * 页头（6）、侧边功能栏（7）、专题模板（8）、器械数据库商品详情页（9）、其他（10）、
     * 分享购物车（11）、常购商品（12）、商品收藏（13）、采购指南（14）、 常用清单(15)、 招投标中心(16)、 海报中心(17)、 详细找货(18)、为您找货频道页(19)、低等级商品(20)
     * 贝登百科(21)、科研特麦帮(22)、医疗器械资料库(23)、清仓特卖(24)、贝登排行榜(25)、医疗器械商品库（26）、招标资讯（27）
     */
    private Integer entrances;

    /**
     * 功能，立即询价（1）、立即订购（2）、商品咨询（3）、为您找货（4）、IM自主询价（5）
     */
    private Integer functions;

    /**
     * 附件，oss地址
     */
    private String attachment;

    /**
     * 咨询商品名称
     */
    private String goodsName;

    /**
     * 咨询内容
     */
    private String content;

    private String traderName;

    /**
     * 联系人
     */
    private String checkTraderContactName;

    /**
     * 联系方式
     */
    private String checkTraderContactMobile;

    /**
     * 前台登录用户的手机号，当用户为游客时，默认为空
     */
    private String loginMobile;

    /**
     * 商机创建时间
     */
    private Long addTime;

    /** 海报分分享人Id */
    private Integer sharerId;

    /** 海报Id */
    private Integer posterId;

    /**
     * 用户IP
     */
    private String userIp;

}
