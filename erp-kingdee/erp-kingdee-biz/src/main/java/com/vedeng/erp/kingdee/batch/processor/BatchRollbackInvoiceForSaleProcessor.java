package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.finance.enums.InvoiceBelongTypeEnum;
import com.vedeng.erp.finance.enums.InvoiceTaxTypeEnum;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchRollbackInvoiceDto;
import com.vedeng.erp.kingdee.batch.dto.BatchRollbackInvoiceProcessDto;
import com.vedeng.erp.kingdee.batch.repository.BatchRollbackInvoiceDtoMapper;
import com.vedeng.erp.kingdee.dto.result.KingDeeOutPutFeeResultDto;
import com.vedeng.erp.kingdee.service.KingDeeSaleInStockService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 销售线--回滚蓝字作废票关联的蓝字有效票处理器
 *
 * <AUTHOR>
 * @create 2023−01-13 下午1:42
 */
@Service
@Slf4j
public class BatchRollbackInvoiceForSaleProcessor extends BaseProcessor<BatchRollbackInvoiceDto, List<BatchRollbackInvoiceProcessDto>> {

    @Autowired
    BatchRollbackInvoiceDtoMapper batchRollbackInvoiceDtoMapper;
    @Autowired
    KingDeeBaseApi kingDeeBaseApi;
    @Autowired
    KingDeeSaleInStockService kingDeeSaleInStockService;

    @Override
    public List<BatchRollbackInvoiceProcessDto> doProcess(BatchRollbackInvoiceDto batchRollbackInvoiceDto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("销售线蓝字作废票关联的蓝字有效票回滚批处理 BatchRollbackInvoiceDto:{}", JSON.toJSONString(batchRollbackInvoiceDto));
        List<BatchRollbackInvoiceProcessDto> resultList = new ArrayList<>();
        //查询蓝票对应的商品是否包含实物商品或虚拟商品
        Set<Integer> isVirtualGoodSet = batchRollbackInvoiceDtoMapper.getIsVirtualGoodSetByInvoiceId(batchRollbackInvoiceDto.getInvoiceId());
        if (isVirtualGoodSet.contains(null)) {
            isVirtualGoodSet.add(0);
        }

        // 实物商品 对应 KING_DEE_SALES_VAT_PLAIN_INVOICE,KING_DEE_SALES_VAT_SPECIAL_INVOICE
        if (CollUtil.contains(isVirtualGoodSet, 0)) {
            boolean isSpecialInvoice = InvoiceTaxTypeEnum.getIsSpecialInvoice(batchRollbackInvoiceDto.getInvoiceType());
            String formId = "";
            if (InvoiceBelongTypeEnum.SALE_ORDER.getCode().equals(batchRollbackInvoiceDto.getType())) {
                formId = isSpecialInvoice ? KingDeeFormConstant.SALE_VAT_SPECIAL_INVOICE : KingDeeFormConstant.SALE_VAT_PLAIN_INVOICE;
            }

            // 调用金蝶查询发票Fid
            List<KingDeeOutPutFeeResultDto> queryResult = kingDeeSaleInStockService.getKingDeeSaleInvoiceFid(formId, batchRollbackInvoiceDto.getInvoiceId());
            if (CollUtil.isNotEmpty(queryResult)) {
                String ids = CollUtil.getFirst(queryResult).getFID();
                log.info("蓝字作废关联的蓝字有效发票(实物商品)回滚批处理 invoiceId:{},formId:{},ids:{}", batchRollbackInvoiceDto.getInvoiceId(), formId, ids);
                resultList.add(BatchRollbackInvoiceProcessDto.builder().formId(formId).ids(ids).invoiceId(batchRollbackInvoiceDto.getInvoiceId()).orgId(KingDeeConstant.ORG_ID.toString()).isPay(Boolean.FALSE).build());
            }
        }

        // 虚拟商品 对应 KING_DEE_OUT_PUT_FEE_PLAIN_INVOICE,KING_DEE_OUT_PUT_FEE_SPECIAL_INVOICE
        if (CollUtil.contains(isVirtualGoodSet, 1)) {
            boolean isSpecialInvoice = InvoiceTaxTypeEnum.getIsSpecialInvoice(batchRollbackInvoiceDto.getInvoiceType());
            String formId = "";
            if (InvoiceBelongTypeEnum.SALE_ORDER.getCode().equals(batchRollbackInvoiceDto.getType())) {
                formId = isSpecialInvoice ? KingDeeFormConstant.OUTPUT_FEE_SPECIAL_INVOICE : KingDeeFormConstant.OUTPUT_FEE_PLAIN_INVOICE;
            }

            // 调用金蝶查询发票Fid
            List<KingDeeOutPutFeeResultDto> queryResult = kingDeeSaleInStockService.getKingDeeSaleInvoiceFid(formId, batchRollbackInvoiceDto.getInvoiceId());
            if (CollUtil.isNotEmpty(queryResult)) {
                String ids = CollUtil.getFirst(queryResult).getFID();
                log.info("蓝字作废关联的蓝字有效发票(虚拟商品)回滚批处理 invoiceId:{},formId:{},ids:{}", batchRollbackInvoiceDto.getInvoiceId(), formId, ids);
                resultList.add(BatchRollbackInvoiceProcessDto.builder().formId(formId).ids(ids).invoiceId(batchRollbackInvoiceDto.getInvoiceId()).orgId(KingDeeConstant.ORG_ID.toString()).isPay(Boolean.FALSE).build());
            }
        }
        return resultList;
    }
}
