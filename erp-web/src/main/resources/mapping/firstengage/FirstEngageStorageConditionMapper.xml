<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.firstengage.dao.FirstEngageStorageConditionMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.firstengage.model.FirstEngageStorageCondition">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 20 18:33:10 CST 2019.
    -->
    <id column="FIRST_ENGAGE_STORAGE_CONDITION_ID" jdbcType="INTEGER" property="firstEngageStorageConditionId" />
    <result column="FIRST_ENGAGE_ID" jdbcType="INTEGER" property="firstEngageId" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
  </resultMap>
  <sql id="Base_Column_List">
    FIRST_ENGAGE_STORAGE_CONDITION_ID, FIRST_ENGAGE_ID, NAME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_FIRST_ENGAGE_STORAGE_CONDITION
    where FIRST_ENGAGE_STORAGE_CONDITION_ID = #{firstEngageStorageConditionId,jdbcType=INTEGER}
  </select>
  
  <select id="selectByParam" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_FIRST_ENGAGE_STORAGE_CONDITION
    where 
    FIRST_ENGAGE_ID = #{firstEngageId,jdbcType=INTEGER}
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_FIRST_ENGAGE_STORAGE_CONDITION
    where FIRST_ENGAGE_STORAGE_CONDITION_ID = #{firstEngageStorageConditionId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.firstengage.model.FirstEngageStorageCondition">
    insert into T_FIRST_ENGAGE_STORAGE_CONDITION (FIRST_ENGAGE_STORAGE_CONDITION_ID, FIRST_ENGAGE_ID, 
      NAME)
    values (#{firstEngageStorageConditionId,jdbcType=INTEGER}, #{firstEngageId,jdbcType=INTEGER}, 
      #{name,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.firstengage.model.FirstEngageStorageCondition">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 20 18:33:10 CST 2019.
    -->
    insert into T_FIRST_ENGAGE_STORAGE_CONDITION
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="firstEngageStorageConditionId != null">
        FIRST_ENGAGE_STORAGE_CONDITION_ID,
      </if>
      <if test="firstEngageId != null">
        FIRST_ENGAGE_ID,
      </if>
      <if test="name != null">
        NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="firstEngageStorageConditionId != null">
        #{firstEngageStorageConditionId,jdbcType=INTEGER},
      </if>
      <if test="firstEngageId != null">
        #{firstEngageId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <!-- 添加存储条件 -->
  <insert id="insertSelectiveList" parameterType="map">
		INSERT INTO T_FIRST_ENGAGE_STORAGE_CONDITION (FIRST_ENGAGE_ID, `NAME`)
		VALUES
		<foreach collection="storageCondition" item="condition" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			    #{firstEngageId, jdbcType=INTEGER},
				#{condition.name, jdbcType=VARCHAR}
			</trim>
		</foreach>
  </insert>
  
  
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.firstengage.model.FirstEngageStorageCondition">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 20 18:33:10 CST 2019.
    -->
    update T_FIRST_ENGAGE_STORAGE_CONDITION
    <set>
      <if test="firstEngageId != null">
        FIRST_ENGAGE_ID = #{firstEngageId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        NAME = #{name,jdbcType=VARCHAR},
      </if>
    </set>
    where FIRST_ENGAGE_STORAGE_CONDITION_ID = #{firstEngageStorageConditionId,jdbcType=INTEGER}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.vedeng.firstengage.model.FirstEngageStorageCondition">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 20 18:33:10 CST 2019.
    -->
    update T_FIRST_ENGAGE_STORAGE_CONDITION
    set FIRST_ENGAGE_ID = #{firstEngageId,jdbcType=INTEGER},
      NAME = #{name,jdbcType=VARCHAR}
    where FIRST_ENGAGE_STORAGE_CONDITION_ID = #{firstEngageStorageConditionId,jdbcType=INTEGER}
  </update>

  <delete id="deleteByPrimaryParam" parameterType="java.util.Map">

    delete  from T_FIRST_ENGAGE_STORAGE_CONDITION where FIRST_ENGAGE_ID = #{firstEngageId, jdbcType=INTEGER}

  </delete>
</mapper>