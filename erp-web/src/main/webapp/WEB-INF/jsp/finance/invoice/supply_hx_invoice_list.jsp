<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="进项票（供应链）" scope="application" />
<%@ include file="../../common/common.jsp"%>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css"  media="all">
<style type="text/css">
    .layui-tab-brief > .layui-tab-title .layui-this{
        color: #3384ef;
    }
    .layui-tab-brief>.layui-tab-title .layui-this:after{
        border-bottom: 2px solid #3384ef;
    }
</style>
<div class="layui-tab layui-tab-brief" lay-filter="test">
    <ul class="layui-tab-title">
        <li class="layui-this">待录票</li>
        <li>审核中</li>
        <li>已审核</li>
        <li>待退票</li>
    </ul>
    <div class="layui-tab-content" style="height: 100px;">
        <iframe style="width: 100%; min-height: 750px;" scrolling="no" frameborder="no" src="/supplyChain/invoice/hx_invoice_wait.do?idFlag=0"></iframe>
    </div>
</div>
<script src="/webjars/ezadmin/plugins/layui/layui.js" charset="utf-8"></script>
<script>
    layui.use('element', function(){
        var element = layui.element; //Tab的切换功能，切换事件监听等，需要依赖element模块

        //触发事件
        var active = {
            tabChange: function(){
                //切换到指定Tab项
                element.tabChange('demo', '22'); //切换到：用户管理
            }
        };

        element.on('tab(test)', function(data){
            $(".layui-tab-content").find("iframe").attr("src",'/supplyChain/invoice/hx_invoice_wait.do?idFlag=' + data.index);
        });

    });
</script>
<script>

</script>
<%@ include file="../../common/footer.jsp"%>