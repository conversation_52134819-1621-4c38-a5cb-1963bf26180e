<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.finance.mapper.AfterSalesInvoiceMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.AfterSalesInvoiceEntity">
    <!--@mbg.generated-->
    <!--@Table T_AFTER_SALES_INVOICE-->
    <id column="AFTER_SALES_INVOICE_ID" jdbcType="INTEGER" property="afterSalesInvoiceId" />
    <result column="AFTER_SALES_ID" jdbcType="INTEGER" property="afterSalesId" />
    <result column="INVOICE_ID" jdbcType="INTEGER" property="invoiceId" />
    <result column="IS_REFUND_INVOICE" jdbcType="BOOLEAN" property="isRefundInvoice" />
    <result column="STATUS" jdbcType="BOOLEAN" property="status" />
    <result column="IS_SEND_INVOICE" jdbcType="BOOLEAN" property="isSendInvoice" />
    <result column="HANDLE_STATUS" jdbcType="BOOLEAN" property="handleStatus" />
    <result column="HANDLE_COMMENTS" jdbcType="VARCHAR" property="handleComments" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="INVOICE_NO" jdbcType="VARCHAR" property="invoiceNo" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    AFTER_SALES_INVOICE_ID, AFTER_SALES_ID, INVOICE_ID, IS_REFUND_INVOICE, `STATUS`, 
    IS_SEND_INVOICE, HANDLE_STATUS, HANDLE_COMMENTS, MOD_TIME, UPDATER, INVOICE_NO
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_AFTER_SALES_INVOICE
    where AFTER_SALES_INVOICE_ID = #{afterSalesInvoiceId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_AFTER_SALES_INVOICE
    where AFTER_SALES_INVOICE_ID = #{afterSalesInvoiceId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="AFTER_SALES_INVOICE_ID" keyProperty="afterSalesInvoiceId" parameterType="com.vedeng.erp.finance.domain.entity.AfterSalesInvoiceEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES_INVOICE (AFTER_SALES_ID, INVOICE_ID, IS_REFUND_INVOICE, 
      `STATUS`, IS_SEND_INVOICE, HANDLE_STATUS, 
      HANDLE_COMMENTS, MOD_TIME, UPDATER, 
      INVOICE_NO)
    values (#{afterSalesId,jdbcType=INTEGER}, #{invoiceId,jdbcType=INTEGER}, #{isRefundInvoice,jdbcType=BOOLEAN}, 
      #{status,jdbcType=BOOLEAN}, #{isSendInvoice,jdbcType=BOOLEAN}, #{handleStatus,jdbcType=BOOLEAN}, 
      #{handleComments,jdbcType=VARCHAR}, #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}, 
      #{invoiceNo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="AFTER_SALES_INVOICE_ID" keyProperty="afterSalesInvoiceId" parameterType="com.vedeng.erp.finance.domain.entity.AfterSalesInvoiceEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES_INVOICE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterSalesId != null">
        AFTER_SALES_ID,
      </if>
      <if test="invoiceId != null">
        INVOICE_ID,
      </if>
      <if test="isRefundInvoice != null">
        IS_REFUND_INVOICE,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
      <if test="isSendInvoice != null">
        IS_SEND_INVOICE,
      </if>
      <if test="handleStatus != null">
        HANDLE_STATUS,
      </if>
      <if test="handleComments != null and handleComments != ''">
        HANDLE_COMMENTS,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="invoiceNo != null and invoiceNo != ''">
        INVOICE_NO,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="afterSalesId != null">
        #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="invoiceId != null">
        #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="isRefundInvoice != null">
        #{isRefundInvoice,jdbcType=BOOLEAN},
      </if>
      <if test="status != null">
        #{status,jdbcType=BOOLEAN},
      </if>
      <if test="isSendInvoice != null">
        #{isSendInvoice,jdbcType=BOOLEAN},
      </if>
      <if test="handleStatus != null">
        #{handleStatus,jdbcType=BOOLEAN},
      </if>
      <if test="handleComments != null and handleComments != ''">
        #{handleComments,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="invoiceNo != null and invoiceNo != ''">
        #{invoiceNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.finance.domain.entity.AfterSalesInvoiceEntity">
    <!--@mbg.generated-->
    update T_AFTER_SALES_INVOICE
    <set>
      <if test="afterSalesId != null">
        AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="invoiceId != null">
        INVOICE_ID = #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="isRefundInvoice != null">
        IS_REFUND_INVOICE = #{isRefundInvoice,jdbcType=BOOLEAN},
      </if>
      <if test="status != null">
        `STATUS` = #{status,jdbcType=BOOLEAN},
      </if>
      <if test="isSendInvoice != null">
        IS_SEND_INVOICE = #{isSendInvoice,jdbcType=BOOLEAN},
      </if>
      <if test="handleStatus != null">
        HANDLE_STATUS = #{handleStatus,jdbcType=BOOLEAN},
      </if>
      <if test="handleComments != null and handleComments != ''">
        HANDLE_COMMENTS = #{handleComments,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="invoiceNo != null and invoiceNo != ''">
        INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR},
      </if>
    </set>
    where AFTER_SALES_INVOICE_ID = #{afterSalesInvoiceId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.finance.domain.entity.AfterSalesInvoiceEntity">
    <!--@mbg.generated-->
    update T_AFTER_SALES_INVOICE
    set AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      INVOICE_ID = #{invoiceId,jdbcType=INTEGER},
      IS_REFUND_INVOICE = #{isRefundInvoice,jdbcType=BOOLEAN},
      `STATUS` = #{status,jdbcType=BOOLEAN},
      IS_SEND_INVOICE = #{isSendInvoice,jdbcType=BOOLEAN},
      HANDLE_STATUS = #{handleStatus,jdbcType=BOOLEAN},
      HANDLE_COMMENTS = #{handleComments,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
    where AFTER_SALES_INVOICE_ID = #{afterSalesInvoiceId,jdbcType=INTEGER}
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_AFTER_SALES_INVOICE
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="AFTER_SALES_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.afterSalesId != null">
            when AFTER_SALES_INVOICE_ID = #{item.afterSalesInvoiceId,jdbcType=INTEGER} then #{item.afterSalesId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceId != null">
            when AFTER_SALES_INVOICE_ID = #{item.afterSalesInvoiceId,jdbcType=INTEGER} then #{item.invoiceId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_REFUND_INVOICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isRefundInvoice != null">
            when AFTER_SALES_INVOICE_ID = #{item.afterSalesInvoiceId,jdbcType=INTEGER} then #{item.isRefundInvoice,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="`STATUS` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status != null">
            when AFTER_SALES_INVOICE_ID = #{item.afterSalesInvoiceId,jdbcType=INTEGER} then #{item.status,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_SEND_INVOICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isSendInvoice != null">
            when AFTER_SALES_INVOICE_ID = #{item.afterSalesInvoiceId,jdbcType=INTEGER} then #{item.isSendInvoice,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="HANDLE_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.handleStatus != null">
            when AFTER_SALES_INVOICE_ID = #{item.afterSalesInvoiceId,jdbcType=INTEGER} then #{item.handleStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="HANDLE_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.handleComments != null">
            when AFTER_SALES_INVOICE_ID = #{item.afterSalesInvoiceId,jdbcType=INTEGER} then #{item.handleComments,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when AFTER_SALES_INVOICE_ID = #{item.afterSalesInvoiceId,jdbcType=INTEGER} then #{item.modTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when AFTER_SALES_INVOICE_ID = #{item.afterSalesInvoiceId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceNo != null">
            when AFTER_SALES_INVOICE_ID = #{item.afterSalesInvoiceId,jdbcType=INTEGER} then #{item.invoiceNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where AFTER_SALES_INVOICE_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.afterSalesInvoiceId,jdbcType=INTEGER}
    </foreach>
  </update>



</mapper>