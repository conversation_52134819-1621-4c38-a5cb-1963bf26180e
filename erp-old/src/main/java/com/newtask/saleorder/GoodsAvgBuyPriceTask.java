package com.newtask.saleorder;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.goods.dao.CoreSkuGenerateMapper;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.io.IOException;
import java.sql.SQLException;
import java.util.*;

/**
 *  计算成本价 临时代码迁移至定时任务
 * 
 * <AUTHOR>
 *
 */
@JobHandler(value = "GoodsAvgBuyPriceTask")
@Component
@Slf4j
public class GoodsAvgBuyPriceTask extends AbstractJobHandler {

	Logger logger = LoggerFactory.getLogger(GoodsAvgBuyPriceTask.class);

	@Autowired
	SaleorderGoodsMapper saleorderGoodsMapper;
	@Autowired
	CoreSkuGenerateMapper coreSkuGenerateMapper;
	Object lock =new Object();
	@Autowired
	@Qualifier("dataSource")
	DataSource dataSource;
	Dao dao=Dao.getInstance();
	@Override
	public ReturnT<String> doExecute(String param) throws Exception{
		//
		if(StringUtils.isBlank(param)){
			dao.executeUpdate(dataSource,"update T_SALEORDER_GOODS set BUY_PRICE_FLAG='0' " +
					"where UPDATE_DATA_TIME > date_sub(curdate(), interval 1 day)",null);
		}

		String initSql="SELECT B.DELIVERY_DIRECT,  " +
				"       A.SALEORDER_ID,  " +
				"       SALEORDER_NO,  " +
				"       SALEORDER_GOODS_ID,  " +
				"       B.NUM   SALEORDER_NUM,  " +
				"       B.PRICE SALEORDER_PRICE  " +
				"FROM T_SALEORDER A  " +
				"         LEFT JOIN T_SALEORDER_GOODS B ON A.SALEORDER_ID = B.SALEORDER_ID " +
				"WHERE B.IS_DELETE = 0  " +
				"  AND A.VALID_STATUS = 1  " +

				"  AND A.COMPANY_ID = 1  " ;


		if(StringUtils.isNotBlank(param)){
			initSql+=" and B.SALEORDER_GOODS_ID  "+param;
		}else{
			initSql+="  AND (B.BUY_PRICE_FLAG IS NULL OR B.BUY_PRICE_FLAG = '0')  " ;
			initSql+=	"  and B.UPDATE_DATA_TIME > date_sub(curdate(), interval 1 day) LIMIT 1000";
		}
		executeBiz(initSql,param);

		 executeRangeOrderTable(param);

		return ReturnT.SUCCESS;
	}

	private List<Map<String,Object>> executeRandgeOrderLimit(String param){
		String sql="select SALEORDER_GOODS_ID,GOODS_ID from T_SALEORDER_GOODS where   BUY_PRICE_FLAG='1' " +
				"   and UPDATE_DATA_TIME > date_sub(curdate(), interval 1 day) ";
		if(StringUtils.isNotBlank(param)){
			sql+=" and SALEORDER_GOODS_ID "+param;
		}
		sql+=" limit 1000";
		try {
			List<Map<String,Object>> saleorderGoods=selectList(sql,null);

			return saleorderGoods;
		} catch (SQLException throwables) {
			 return Collections.emptyList();
		}
	}

	private void executeRangeOrderTable(String param) throws SQLException {
		//昨天所有需要计算的

		List<Map<String,Object>> saleorderGoods=executeRandgeOrderLimit(param);
		while(notNull(saleorderGoods)){
			String buyPriceSql=" SELECT " +
					"   sum( TEMP.BUYORDER_PRICE * TEMP.BUYORDER_NUM_LOG ) / sum(TEMP.BUYORDER_NUM_LOG)  BUYORDER_PRICE " +
					"                    ,group_concat(B.BUYORDER_NO) BUYORDER_NOS " +
					"     FROM " +
					"   TEMP_ORDERS TEMP  LEFT JOIN T_BUYORDER B ON TEMP.BUYORDER_ID=B.BUYORDER_ID " +
					"   WHERE SALEORDER_DETAIL_ID=? AND B.STATUS<3 AND B.VALID_STATUS=1 group by SALEORDER_DETAIL_ID ";
			//前10个生效且全部付款的单价大于0的 实际采购 商品的平均单价
			String HISTORY_AVG_PRICE=" select   round( SUM((B.PRICE*(B.NUM-IFNULL(B.AFTER_RETURN_AMOUNT,0))))/SUM(B.NUM-IFNULL(B.AFTER_RETURN_AMOUNT,0)),2)  AVGPRICE" +
					"     from T_BUYORDER A " +
					"         LEFT JOIN T_BUYORDER_GOODS  B ON A.BUYORDER_ID=B.BUYORDER_ID " +
					"where A.VALID_STATUS=1 and A.STATUS<3 AND B.IS_DELETE=0 AND A.PAYMENT_STATUS=2 AND  B.GOODS_ID=? " +
					" AND B.PRICE>0 " +
					"     ORDER BY A.BUYORDER_ID limit 10 ";
			saleorderGoods.parallelStream().forEach(item->{
				try {
					logger.info("计算成本价--"+item.get("SALEORDER_GOODS_ID") );
					//标记已经计算
					String tag = "update T_SALEORDER_GOODS set BUY_PRICE_FLAG=9"
							+ " WHERE SALEORDER_GOODS_ID=?"  ;
					dao.executeUpdate(dataSource, tag, new Object[]{item.get("SALEORDER_GOODS_ID")});


					//计算BUY_PRICE
					Map<String,Object> buyPriceMap=selectOne(buyPriceSql,new Object[]{item.get("SALEORDER_GOODS_ID")});
					if(buyPriceMap!=null&&buyPriceMap.get("BUYORDER_PRICE")!=null){
						String updateSql = "update T_SALEORDER_GOODS set BUY_PRICE=" +  buyPriceMap.get("BUYORDER_PRICE")
								+ " WHERE SALEORDER_GOODS_ID=?"  ;
						dao.executeUpdate(dataSource, updateSql, new Object[]{item.get("SALEORDER_GOODS_ID")});
					}
					if(buyPriceMap!=null&&buyPriceMap.get("BUYORDER_NOS")!=null){
						String updateSql = "update T_SALEORDER_GOODS set BUYORDER_NOS='" +  buyPriceMap.get("BUYORDER_NOS")
								+ "' WHERE SALEORDER_GOODS_ID=?"  ;
						dao.executeUpdate(dataSource, updateSql, new Object[]{item.get("SALEORDER_GOODS_ID")});
					}
					//计算 HISTORY_AVG_PRICE
					Map<String,Object> hisPriceMap=selectOne(HISTORY_AVG_PRICE,new Object[]{item.get("GOODS_ID")});
					if(hisPriceMap!=null&&hisPriceMap.get("AVGPRICE")!=null){
						String updateSql = "update T_SALEORDER_GOODS set HISTORY_AVG_PRICE=" +  hisPriceMap.get("AVGPRICE")
								+ " WHERE SALEORDER_GOODS_ID=?"  ;
						dao.executeUpdate(dataSource, updateSql, new Object[]{item.get("SALEORDER_GOODS_ID")});
					}
				}catch (Exception e){
					logger.error(item.get("SALEORDER_GOODS_ID")+"",e);
				}
			});
			saleorderGoods=executeRandgeOrderLimit(param);
		}

		// 全部标记回来
		String tag = "update T_SALEORDER_GOODS set BUY_PRICE_FLAG=1"
				+ " WHERE BUY_PRICE_FLAG=9"  ;
		dao.executeUpdate(dataSource, tag, null);

	}

	public   Map<String,Object> selectOne(String sql, Object[] obj) throws SQLException {
		return dao.executeQueryOne(dataSource,sql,obj);
	}
	public   List<Map<String,Object>> selectList(String sql, Object[] obj) throws SQLException {
		return dao.executeQuery(dataSource,sql,obj);
	}

	public  void executeBiz(String sql,String param) throws SQLException {
		List<Map<String,Object>> list=lockList(sql);
		while(notNull(list)){
			XxlJobLogger.log("开始计算成本价================="+list.size());
			long start=System.currentTimeMillis();
			List<Integer> ids=new ArrayList<>();
			for(Map<String,Object> map:list){
				List<PricePO> pricePOList=new ArrayList<>();
				try {
					//清理上一次计算的数据
					String REMOVEOLD = "delete from  TEMP_ORDERS WHERE SALEORDER_DETAIL_ID = " + map.get("SALEORDER_GOODS_ID");
					dao.executeUpdate(dataSource, REMOVEOLD, null);
				}catch(Exception e){
					log.error("【executeBiz】处理异常",e);
				}


				getAll(map.get("SALEORDER_GOODS_ID")+"",pricePOList,map.get("SALEORDER_GOODS_ID")+"" );
				//    logger.info(map.get("SALEORDER_GOODS_ID")+"\t"+(System.currentTimeMillis()-start));
				// start =System.currentTimeMillis();
				ids.add(org.apache.commons.lang3.math.NumberUtils.toInt(map.get("SALEORDER_GOODS_ID")+""));

				for(PricePO po:pricePOList){
					String pricePoOne="SELECT  ID FROM TEMP_ORDERS where SALEORDER_DETAIL_ID="+po.getSALEORDER_GOODS_ID()+" and BUYORDER_DETAIL_ID="+po.getBUYORDER_GOODS_ID() +" limit 1";//
					try {
						XxlJobLogger.log(JsonUtils.translateToJson(po));
					} catch (IOException e) {
						log.error("【executeBiz】处理异常",e);
					}
					synchronized (lock) {
						Map<String,Object>  pricePoOneO=selectOne(pricePoOne,null);
						if (pricePoOneO != null && pricePoOneO.containsKey("ID")) {
							String updateSql = "update TEMP_ORDERS set BUYORDER_ID=" + po.getBUYORDER_ID() + ",BUYORDER_NO='" + po.getBUYORDER_NO() + "'" +
									",UPDATE_TIME=NOW(),BUYORDER_NUM=" + po.getBUYORDER_NUM() + ",BUYORDER_PRICE=" + po.getBUYORDER_PRICE() + ",BUYORDER_NUM_LOG=" + po.getBUYORDER_NUM_LOG()
									+ " WHERE ID=" + pricePoOneO.get("ID");
							dao.executeUpdate(dataSource, updateSql, null);
						} else {

							String insert = "INSERT INTO  TEMP_ORDERS ( `SALEORDER_ID`, `SALEORDER_NO`, `SALEORDER_DETAIL_ID`, `SALEORDER_NUM`, `SALEORDER_PRICE`, `BUYORDER_ID`," +
									" `BUYORDER_NO`, `BUYORDER_DETAIL_ID`, `ADD_TIME`,UPDATE_TIME, `BUYORDER_NUM`, `BUYORDER_PRICE`, `BUYORDER_NUM_LOG`) VALUES " +
									"(  " + po.getSALEORDER_ID() + ", '" + po.getSALEORDER_NO() + "', " + po.getSALEORDER_GOODS_ID() + ", " +
									po.getSALEORDER_NUM() + ", " + po.getSALEORDER_PRICE() + ", " + po.getBUYORDER_ID() + ",'" +
									po.getBUYORDER_NO() + "', " + po.getBUYORDER_GOODS_ID() + ", now(),now(), " + po.getBUYORDER_NUM() + ", " + po.getBUYORDER_PRICE() + ", " + po.getBUYORDER_NUM_LOG() + ")";
							dao.executeUpdate(dataSource, insert, null);
						}
					}
				}

			}
			String update="update T_SALEORDER_GOODS set BUY_PRICE_FLAG='1' where SALEORDER_GOODS_ID IN("+ StringUtils.join(ids,",") +")";
			dao.executeUpdate(dataSource,update,null);
			  logger.info( new Date()+"\t"+(System.currentTimeMillis()-start));
			if(StringUtils.isNotBlank(param)){
				list=null;
			}else{
				list=lockList(sql);
			}
		}
	}
	public   Object lock2=new Object();
	public   List<Map<String,Object>> lockList(String sql) throws SQLException {
		List<Map<String,Object>> list=null;
		synchronized (lock2) {
			list=  selectList(sql, null);
			if(notNull(list)){
				List<Integer> ids=new ArrayList<>();
				for(Map<String,Object> map:list){
					ids.add(NumberUtils.toInt(map.get("SALEORDER_GOODS_ID")+""));
				}
				try {
					String update = "update T_SALEORDER_GOODS set BUY_PRICE_FLAG='2' where SALEORDER_GOODS_ID IN(" + StringUtils.join(ids, ",") + ")";
					dao.executeUpdate(dataSource, update, null);
				}catch (Exception e){
					log.error("【executeBiz】处理异常",e);
				}
			}
		}
		return list;
	}
	/**
	 *
	 * @param SALEORDER_GOODS_ID
	 * @param result
	 * @throws SQLException
	 */
	  void getAll(String SALEORDER_GOODS_ID,List<PricePO> result ,String dataSourceDetailGoodsId ) throws SQLException {


		String chubarcode="select BARCODE_ID,SUM(NUM) NUM from T_WAREHOUSE_GOODS_OPERATE_LOG WHERE OPERATE_TYPE=2 and BARCODE_ID>0 and IS_ENABLE=1 and RELATED_ID=  "+SALEORDER_GOODS_ID +" group by barcode_id" ;
		List<Map<String,Object>> chubarcodeList=selectList(chubarcode,null);
		List<String> BARCODE=new ArrayList<>();
		int outCount=0;
		int totalLeftCount=0;
		//先看出库总数量
		if(notNull(chubarcodeList)){
			XxlJobLogger.log("先看出库总数量 "+SALEORDER_GOODS_ID);
			for(Map<String,Object> map:chubarcodeList){
				BARCODE.add(map.get("BARCODE_ID")+"");
				outCount+= org.apache.commons.lang3.math.NumberUtils.toInt((map.get("NUM")+"").replace("-",""));
				totalLeftCount =outCount;
			}
		}else{
			// 通过现有采购单与销售单关系查找
			XxlJobLogger.log("通过现有采购单与销售单关系查找 "+SALEORDER_GOODS_ID );
			String relateDataSql="SELECT A.BUYORDER_GOODS_ID RELATED_ID,A.NUM from T_R_BUYORDER_J_SALEORDER A  " +
					"LEFT JOIN T_BUYORDER_GOODS B ON A.BUYORDER_GOODS_ID=B.BUYORDER_GOODS_ID " +
					"LEFT JOIN T_BUYORDER C ON B.BUYORDER_ID=C.BUYORDER_ID " +
					" WHERE C.STATUS<3 AND  SALEORDER_GOODS_ID= "+SALEORDER_GOODS_ID;
			List<Map<String,Object>> relateData=selectList(relateDataSql,null);

			if(notNull(relateData)){
				for(Map<String,Object> map:relateData){
					Map<String,Object> buy=selectOne("select A.BUYORDER_ID,A.BUYORDER_NO,B.BUYORDER_GOODS_ID,B.PRICE BUYORDER_PRICE,B.NUM AS BUYORDER_NUM from T_BUYORDER A LEFT JOIN T_BUYORDER_GOODS B ON A.BUYORDER_ID=B.BUYORDER_ID " +
							"WHERE   B.BUYORDER_GOODS_ID= "+map.get("RELATED_ID") ,null);
					map.putAll(buy);

					Map<String,Object> ORDER=selectOne("select A.SALEORDER_ID,A.SALEORDER_NO,B.SALEORDER_GOODS_ID,B.NUM AS SALEORDER_NUM,B.PRICE SALEORDER_PRICE   from T_SALEORDER A LEFT JOIN T_SALEORDER_GOODS B ON A.SALEORDER_ID=B.SALEORDER_ID " +
							"WHERE B.SALEORDER_GOODS_ID="+SALEORDER_GOODS_ID,null);
					map.putAll(ORDER);
					map.put("BUYORDER_NUM_LOG", org.apache.commons.lang3.math.NumberUtils.toInt(map.get("NUM")+""));

					PricePO pricePO=trans(map,  dataSourceDetailGoodsId);
					result.add(pricePO);
				}
			}
			return;
		}

		//1 找采购入库，求出数量，如果数量达到 出库总数，则不需要继续找。
		String rukubarcodes="select RELATED_ID,SUM(NUM) NUM from T_WAREHOUSE_GOODS_OPERATE_LOG WHERE OPERATE_TYPE=1 and IS_ENABLE=1 and BARCODE_ID in(  "+
				StringUtils.join(BARCODE,",")+") and BARCODE_ID>0   GROUP BY RELATED_ID" ;

		List<Map<String,Object>> rubarcodeList=selectList(rukubarcodes,null);

		//先看出库总数量
		if(notNull(rubarcodeList)){
			XxlJobLogger.log("找采购入库，求出数量，如果数量达到 出库总数，则不需要继续找 "+SALEORDER_GOODS_ID );
			for(Map<String,Object> map:rubarcodeList){

				Map<String,Object> buy=selectOne("select A.BUYORDER_ID,A.BUYORDER_NO,B.BUYORDER_GOODS_ID,B.PRICE BUYORDER_PRICE,B.NUM AS BUYORDER_NUM from T_BUYORDER A LEFT JOIN T_BUYORDER_GOODS B ON A.BUYORDER_ID=B.BUYORDER_ID " +
						"WHERE B.BUYORDER_GOODS_ID= "+map.get("RELATED_ID") ,null);
				map.putAll(buy);

				Map<String,Object> ORDER=selectOne("select A.SALEORDER_ID,A.SALEORDER_NO,B.SALEORDER_GOODS_ID,B.NUM AS SALEORDER_NUM,B.PRICE SALEORDER_PRICE   from T_SALEORDER A LEFT JOIN T_SALEORDER_GOODS B ON A.SALEORDER_ID=B.SALEORDER_ID " +
						"WHERE B.SALEORDER_GOODS_ID="+SALEORDER_GOODS_ID,null);
				map.putAll(ORDER);
				map.put("BUYORDER_NUM_LOG", org.apache.commons.lang3.math.NumberUtils.toInt(map.get("NUM")+""));

				PricePO pricePO=trans(map,dataSourceDetailGoodsId);
				result.add(pricePO);

				totalLeftCount-= org.apache.commons.lang3.math.NumberUtils.toInt(map.get("NUM")+"");
				if(totalLeftCount<=0){
					return;
				}
			}
		}
		if(totalLeftCount<=0){
			return;
		}
		//5.找5销售退货入库  ，求出数量，如果数量达到 出库总数，则不需要继续找。关联=售后detailid  能够关联orderdetailid
		String tuihuobarcode="select A.RELATED_ID,SUM(A.NUM) NUM,B.ORDER_DETAIL_ID from T_WAREHOUSE_GOODS_OPERATE_LOG A LEFT JOIN T_AFTER_SALES_GOODS B " +
				"ON A.RELATED_ID=B.AFTER_SALES_GOODS_ID WHERE A.OPERATE_TYPE=5 and A.IS_ENABLE=1 and A.BARCODE_ID in( "+
				StringUtils.join(BARCODE,",")+" ) and A.BARCODE_ID>0   GROUP BY A.RELATED_ID" ;

		List<Map<String,Object>> tuihuobarcodeList=selectList(tuihuobarcode,null);

		//先看出库总数量
		if(notNull(tuihuobarcodeList)){
			XxlJobLogger.log("找5销售退货入库  ，求出数量，如果数量达到 出库总数，则不需要继续找。关联=售后detailid  能够关联orderdetailid "+SALEORDER_GOODS_ID );
			for(Map<String,Object> map:tuihuobarcodeList) {
				int afterCount=  org.apache.commons.lang3.math.NumberUtils.toInt(map.get("NUM")+"");
				totalLeftCount-=afterCount;
				String newOrderDetailId=map.get("ORDER_DETAIL_ID")+"";
				getAll(newOrderDetailId,result,dataSourceDetailGoodsId);
				if(totalLeftCount<=0){
					return;
				}


			}
		}
		  XxlJobLogger.log("售后退货入库还未找到SALEORDER_GOODS_ID"+SALEORDER_GOODS_ID);
		//通过售后单入库找还是找不到，则通过销售换货入库
		//3.找3销售换货入库  ，求出数量，如果数量达到 出库总数，则不需要继续找。关联=售后detailid  能够关联orderdetailid
		String huanhuobarcode="select A.RELATED_ID,SUM(A.NUM) NUM,B.ORDER_DETAIL_ID from T_WAREHOUSE_GOODS_OPERATE_LOG A LEFT JOIN T_AFTER_SALES_GOODS B " +
				"ON A.RELATED_ID=B.AFTER_SALES_GOODS_ID WHERE A.OPERATE_TYPE=3 and A.IS_ENABLE=1 and A.BARCODE_ID in( "+
				StringUtils.join(BARCODE,",")+" ) and A.BARCODE_ID>0   GROUP BY A.RELATED_ID" ;

		List<Map<String,Object>> huanhuobarcodeList=selectList(huanhuobarcode,null);

		//先看出库总数量
		if(notNull(huanhuobarcodeList)){
			for(Map<String,Object> map:huanhuobarcodeList) {
				int afterCount=  org.apache.commons.lang3.math.NumberUtils.toInt(map.get("NUM")+"");
				totalLeftCount-=afterCount;
				String newOrderDetailId=map.get("ORDER_DETAIL_ID")+"";

				if(!StringUtils.equals(newOrderDetailId,SALEORDER_GOODS_ID)){
					getAll(newOrderDetailId,result,dataSourceDetailGoodsId);
				}else{
					  logger.info("出库使用了换货入库条码"+SALEORDER_GOODS_ID);
				}
				if(totalLeftCount<=0){
					return;
				}

			}
		}
		  XxlJobLogger.log("售后换货入库还未找到SALEORDER_GOODS_ID"+SALEORDER_GOODS_ID);
		//再找采购换货

		//1 再找采购换货
		String caigouhuanhuo="select RELATED_ID,SUM(NUM) NUM from T_WAREHOUSE_GOODS_OPERATE_LOG WHERE OPERATE_TYPE=8 and IS_ENABLE=1 and BARCODE_ID in(  "+
				StringUtils.join(BARCODE,",")+") and BARCODE_ID>0   GROUP BY RELATED_ID" ;

		List<Map<String,Object>> caigouhuanhuoList=selectList(caigouhuanhuo,null);

		//先看出库总数量
		if(notNull(caigouhuanhuoList)){
			XxlJobLogger.log("再找采购换货"+SALEORDER_GOODS_ID);
			for(Map<String,Object> map:caigouhuanhuoList){

				Map<String,Object> buy=selectOne("select A.BUYORDER_ID,A.BUYORDER_NO,B.BUYORDER_GOODS_ID,B.PRICE BUYORDER_PRICE,B.NUM AS BUYORDER_NUM " +
						" from T_AFTER_SALES_GOODS C left join T_AFTER_SALES D  on C.AFTER_SALES_ID=D.AFTER_SALES_ID " +
						" left join T_BUYORDER A on D.ORDER_ID=A.BUYORDER_ID " +
						" left join T_BUYORDER_GOODS B on B.BUYORDER_GOODS_ID=C.ORDER_DETAIL_ID " +
						" WHERE C.AFTER_SALES_GOODS_ID="+map.get("RELATED_ID") ,null);
				map.putAll(buy);

				Map<String,Object> ORDER=selectOne("select A.SALEORDER_ID,A.SALEORDER_NO,B.SALEORDER_GOODS_ID,B.NUM AS SALEORDER_NUM,B.PRICE SALEORDER_PRICE   from T_SALEORDER A LEFT JOIN T_SALEORDER_GOODS B ON A.SALEORDER_ID=B.SALEORDER_ID " +
						"WHERE B.SALEORDER_GOODS_ID="+SALEORDER_GOODS_ID,null);
				map.putAll(ORDER);
				map.put("BUYORDER_NUM_LOG", org.apache.commons.lang3.math.NumberUtils.toInt(map.get("NUM")+""));

				PricePO pricePO=trans(map,dataSourceDetailGoodsId);
				result.add(pricePO);

				totalLeftCount-= org.apache.commons.lang3.math.NumberUtils.toInt(map.get("NUM")+"");
				if(totalLeftCount<=0){
					return;
				}
			}
		}
		  XxlJobLogger.log("采购换货入库还未找到SALEORDER_GOODS_ID"+SALEORDER_GOODS_ID);

		//
		// 通过现有采购单与销售单关系查找
		String relateDataSql="SELECT BUYORDER_GOODS_ID RELATED_ID,NUM from T_R_BUYORDER_J_SALEORDER WHERE SALEORDER_GOODS_ID="+SALEORDER_GOODS_ID;
		List<Map<String,Object>> relateData=selectList(relateDataSql,null);

		if(notNull(relateData)){
			XxlJobLogger.log("通过现有采购单与销售单关系查找"+SALEORDER_GOODS_ID);
			for(Map<String,Object> map:relateData){
				Map<String,Object> buy=selectOne("select A.BUYORDER_ID,A.BUYORDER_NO,B.BUYORDER_GOODS_ID,B.PRICE BUYORDER_PRICE,B.NUM AS BUYORDER_NUM from T_BUYORDER A LEFT JOIN T_BUYORDER_GOODS B ON A.BUYORDER_ID=B.BUYORDER_ID " +
						"WHERE B.BUYORDER_GOODS_ID= "+map.get("RELATED_ID") ,null);
				map.putAll(buy);

				Map<String,Object> ORDER=selectOne("select A.SALEORDER_ID,A.SALEORDER_NO,B.SALEORDER_GOODS_ID,B.NUM AS SALEORDER_NUM,B.PRICE SALEORDER_PRICE   from T_SALEORDER A LEFT JOIN T_SALEORDER_GOODS B ON A.SALEORDER_ID=B.SALEORDER_ID " +
						"WHERE B.SALEORDER_GOODS_ID="+SALEORDER_GOODS_ID,null);
				map.putAll(ORDER);
				map.put("BUYORDER_NUM_LOG", org.apache.commons.lang3.math.NumberUtils.toInt(map.get("NUM")+""));

				PricePO pricePO=trans(map,dataSourceDetailGoodsId);
				result.add(pricePO);
			}
			return ;
		}


		//找外借出库   关联到采购单
		String waijiesql=" select RELATED_ID,SUM(NUM) NUM from T_WAREHOUSE_GOODS_OPERATE_LOG where barcode_id in( " +
				" select barcode_id from T_WAREHOUSE_GOODS_OPERATE_LOG where RELATED_ID in(select RELATED_ID from T_WAREHOUSE_GOODS_OPERATE_LOG WHERE OPERATE_TYPE=9 and IS_ENABLE=1 and BARCODE_ID in( "+
				StringUtils.join(BARCODE,",")+" ) and BARCODE_ID>0   GROUP BY RELATED_ID) AND OPERATE_TYPE=10 " +
				"  ) and OPERATE_TYPE in(1) GROUP BY RELATED_ID";

		List<Map<String,Object>> waijieruList=selectList(waijiesql,null);

		//
		if(notNull(waijieruList)){
			XxlJobLogger.log("找外借出库   关联到采购单"+SALEORDER_GOODS_ID);
			for(Map<String,Object> map:waijieruList){

				Map<String,Object> buy=selectOne("select A.BUYORDER_ID,A.BUYORDER_NO,B.BUYORDER_GOODS_ID,B.PRICE BUYORDER_PRICE,B.NUM AS BUYORDER_NUM from T_BUYORDER A LEFT JOIN T_BUYORDER_GOODS B ON A.BUYORDER_ID=B.BUYORDER_ID " +
						"WHERE B.BUYORDER_GOODS_ID= "+map.get("RELATED_ID") ,null);
				map.putAll(buy);

				Map<String,Object> ORDER=selectOne("select A.SALEORDER_ID,A.SALEORDER_NO,B.SALEORDER_GOODS_ID,B.NUM AS SALEORDER_NUM,B.PRICE SALEORDER_PRICE   from T_SALEORDER A LEFT JOIN T_SALEORDER_GOODS B ON A.SALEORDER_ID=B.SALEORDER_ID " +
						"WHERE B.SALEORDER_GOODS_ID="+SALEORDER_GOODS_ID,null);
				map.putAll(ORDER);
				map.put("BUYORDER_NUM_LOG", org.apache.commons.lang3.math.NumberUtils.toInt(map.get("NUM")+""));

				PricePO pricePO=trans(map,dataSourceDetailGoodsId);
				result.add(pricePO);

				totalLeftCount-= org.apache.commons.lang3.math.NumberUtils.toInt(map.get("NUM")+"");
				if(totalLeftCount<=0){
					return;
				}
			}
		}
		if(totalLeftCount<=0){
			return;
		}
		//3
		String waijiesql3=" select RELATED_ID,SUM(A.NUM) NUM ,B.ORDER_DETAIL_ID from T_WAREHOUSE_GOODS_OPERATE_LOG A LEFT JOIN T_AFTER_SALES_GOODS B  ON A.RELATED_ID=B.AFTER_SALES_GOODS_ID   where A.barcode_id in( " +
				" select barcode_id from T_WAREHOUSE_GOODS_OPERATE_LOG where RELATED_ID in(select RELATED_ID from T_WAREHOUSE_GOODS_OPERATE_LOG WHERE OPERATE_TYPE=9 and IS_ENABLE=1 and BARCODE_ID in( "+
				StringUtils.join(BARCODE,",")+" ) and BARCODE_ID>0   GROUP BY RELATED_ID) AND OPERATE_TYPE=10 " +
				"  ) and OPERATE_TYPE in(3) GROUP BY RELATED_ID";

		List<Map<String,Object>> waijieruList3=selectList(waijiesql3,null);

		if(notNull(waijieruList3)){
			for(Map<String,Object> map:waijieruList3) {
				int afterCount=  org.apache.commons.lang3.math.NumberUtils.toInt(map.get("NUM")+"");
				totalLeftCount-=afterCount;
				String newOrderDetailId=map.get("ORDER_DETAIL_ID")+"";

				if(!StringUtils.equals(newOrderDetailId,SALEORDER_GOODS_ID)){
					getAll(newOrderDetailId,result,dataSourceDetailGoodsId);
				}else{
					  logger.info("出库使用了换货入库条码"+SALEORDER_GOODS_ID);
				}
				if(totalLeftCount<=0){
					return;
				}

			}
		}

		//5
		String waijiesql5=" select RELATED_ID,SUM(A.NUM) NUM,B.ORDER_DETAIL_ID from T_WAREHOUSE_GOODS_OPERATE_LOG A LEFT JOIN T_AFTER_SALES_GOODS B  ON A.RELATED_ID=B.AFTER_SALES_GOODS_ID  where barcode_id in( " +
				" select barcode_id from T_WAREHOUSE_GOODS_OPERATE_LOG where RELATED_ID in(select RELATED_ID from T_WAREHOUSE_GOODS_OPERATE_LOG WHERE OPERATE_TYPE=9 and IS_ENABLE=1 and BARCODE_ID in( "+
				StringUtils.join(BARCODE,",")+" ) and BARCODE_ID>0   GROUP BY RELATED_ID) AND OPERATE_TYPE=10 " +
				"  ) and OPERATE_TYPE in(5) GROUP BY RELATED_ID";
		List<Map<String,Object>> waijieruList5=selectList(waijiesql5,null);

		//先看出库总数量
		if(notNull(waijieruList5)){
			for(Map<String,Object> map:waijieruList5) {
				int afterCount=  org.apache.commons.lang3.math.NumberUtils.toInt(map.get("NUM")+"");
				totalLeftCount-=afterCount;
				String newOrderDetailId=map.get("ORDER_DETAIL_ID")+"";
				getAll(newOrderDetailId,result,dataSourceDetailGoodsId);
				if(totalLeftCount<=0){
					return;
				}
			}
		}

		//8
		String waijiesql8=" select RELATED_ID,SUM(A.NUM) NUM from T_WAREHOUSE_GOODS_OPERATE_LOG A where barcode_id in( " +
				" select barcode_id from T_WAREHOUSE_GOODS_OPERATE_LOG where RELATED_ID in(select RELATED_ID from T_WAREHOUSE_GOODS_OPERATE_LOG WHERE OPERATE_TYPE=9 and IS_ENABLE=1 and BARCODE_ID in( "+
				StringUtils.join(BARCODE,",")+" ) and BARCODE_ID>0   GROUP BY RELATED_ID) AND OPERATE_TYPE=10 " +
				"  ) and OPERATE_TYPE in(8) GROUP BY RELATED_ID";
		List<Map<String,Object>> waijieruList8=selectList(waijiesql8,null);
		//先看出库总数量
		if(notNull(waijieruList8)){
			for(Map<String,Object> map:waijieruList8){

				Map<String,Object> buy=selectOne("select A.BUYORDER_ID,A.BUYORDER_NO,B.BUYORDER_GOODS_ID,B.PRICE BUYORDER_PRICE,B.NUM AS BUYORDER_NUM from T_BUYORDER A LEFT JOIN T_BUYORDER_GOODS B ON A.BUYORDER_ID=B.BUYORDER_ID " +
						"WHERE B.BUYORDER_GOODS_ID= "+map.get("RELATED_ID") ,null);
				map.putAll(buy);

				Map<String,Object> ORDER=selectOne("select A.SALEORDER_ID,A.SALEORDER_NO,B.SALEORDER_GOODS_ID,B.NUM AS SALEORDER_NUM,B.PRICE SALEORDER_PRICE   from T_SALEORDER A LEFT JOIN T_SALEORDER_GOODS B ON A.SALEORDER_ID=B.SALEORDER_ID " +
						"WHERE B.SALEORDER_GOODS_ID="+SALEORDER_GOODS_ID,null);
				map.putAll(ORDER);
				map.put("BUYORDER_NUM_LOG", org.apache.commons.lang3.math.NumberUtils.toInt(map.get("NUM")+""));

				PricePO pricePO=trans(map,dataSourceDetailGoodsId);
				result.add(pricePO);

				totalLeftCount-= NumberUtils.toInt(map.get("NUM")+"");
				if(totalLeftCount<=0){
					return;
				}
			}
		}
		//通过外借单也无法找到
		 XxlJobLogger.log("通过外借单也无法找到"+SALEORDER_GOODS_ID);

		//构造一个无数据的数据
		Map<String,Object> noDataMap=new HashMap<>();

		Map<String,Object> ORDER=selectOne("select A.SALEORDER_ID,A.SALEORDER_NO,B.SALEORDER_GOODS_ID,B.NUM AS SALEORDER_NUM,B.PRICE SALEORDER_PRICE   from T_SALEORDER A LEFT JOIN T_SALEORDER_GOODS B ON A.SALEORDER_ID=B.SALEORDER_ID " +
				"WHERE B.SALEORDER_GOODS_ID="+SALEORDER_GOODS_ID,null);
		noDataMap.putAll(ORDER);
		PricePO pricePO=trans(noDataMap,dataSourceDetailGoodsId);
		if(!notNull(result)){
			result.add(pricePO);
		}

	}

//    操作类型
//1入库
//2出库
	//3销售换货入库
//4销售换货出库
	//5销售退货入库
//6采购退货出库
//7采购换货出库
//8采购换货入库
//9外借入库 x
//10外借出库
//11盘盈入库 x

	  List<PricePO> transList(List<Map<String, Object>> result,String dataSourceDetailGoodsId){
		List<PricePO> list=new ArrayList<>();
		result.forEach(item->{
			PricePO pricePO=trans(item,dataSourceDetailGoodsId);
			//  logger.info(pricePO);
			list.add(pricePO);
		});
		return list;
	}
	   PricePO  trans( Map<String, Object>  result ,String dataSourceDetailGoodsId){
		PricePO po=new PricePO();
		po.setSALEORDER_GOODS_ID(dataSourceDetailGoodsId+"");
		po.setBUYORDER_ID(result.get("BUYORDER_ID")+"");
		po.setBUYORDER_GOODS_ID(result.get("BUYORDER_GOODS_ID")+"");
		po.setBUYORDER_NO(result.get("BUYORDER_NO")+"");
		po.setSALEORDER_ID(result.get("SALEORDER_ID")+"");
		po.setSALEORDER_NO(result.get("SALEORDER_NO")+"");
		po.setSALEORDER_NUM(result.get("SALEORDER_NUM")+"");
		po.setBUYORDER_NUM(result.get("BUYORDER_NUM")+"");
		po.setSALEORDER_PRICE(result.get("SALEORDER_PRICE")+"");
		po.setBUYORDER_PRICE(result.get("BUYORDER_PRICE")+"");
		po.setBUYORDER_NUM_LOG(result.get("BUYORDER_NUM_LOG")+"");
		return po;
	}

	  boolean notNull(Collection list){
		if(list!=null&&!list.isEmpty()){
			return true;
		}
		return false;
	}

}
