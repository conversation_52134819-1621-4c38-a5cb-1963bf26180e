package com.vedeng.erp.saleorder.model.po;

import lombok.Data;

import java.util.Date;

@Data
public class ExpressFeedback {
    /**
     * Column: EXPRESS_FEEDBACK_ID
     * Type: INT
     * Remark: 主键ID
     */
    private Integer expressFeedbackId;

    /**
     * Column: EXPRESS_ID
     * Type: INT
     * Remark: 快递主键ID
     */
    private Integer expressId;

    /**
     * Column: EXPRESS_LOGISTICS_NO
     * Type: VARCHAR(255)
     * Remark: 原快递单号
     */
    private String expressLogisticsNo;

    /**
     * Column: GEN_LOGISTICS_NO
     * Type: VARCHAR(255)
     * Remark: 生成单号
     */
    private String genLogisticsNo;

    /**
     * Column: DELIVERY_MONTH
     * Type: VARCHAR(255)
     * Remark: 发货月份
     */
    private String deliveryMonth;

    /**
     * Column: IS_SUCCESS
     * Type: BIT
     * Default value: 0
     * Remark: 是否替换成功（0未执行，1已成功,2执行失败）
     */
    private Integer isSuccess;

    /**
     * Column: IS_DELETE
     * Type: BIT
     * Default value: 0
     * Remark: 是否删除 0否 1是
     */
    private Integer isDelete;

    /**
     * Column: ADD_TIME
     * Type: DATETIME
     * Default value: CURRENT_TIMESTAMP
     * Remark: 创建时间
     */
    private Date addTime;

    /**
     * Column: MOD_TIME
     * Type: DATETIME
     * Default value: CURRENT_TIMESTAMP
     * Remark: 修改时间
     */
    private Date modTime;

    /**
     * Column: CREATOR
     * Type: INT
     * Default value: 0
     * Remark: 创建者ID
     */
    private Integer creator;

    /**
     * Column: UPDATER
     * Type: INT
     * Default value: 0
     * Remark: 修改者ID
     */
    private Integer updater;

    /**
     * Column: CREATOR_NAME
     * Type: VARCHAR(50)
     * Remark: 创建者名称
     */
    private String creatorName;

    /**
     * Column: UPDATER_NAME
     * Type: VARCHAR(50)
     * Remark: 修改者名称
     */
    private String updaterName;

    /**
     * Column: UPDATE_REMARK
     * Type: VARCHAR(255)
     * Remark: 更新备注
     */
    private String updateRemark;

    /**
     * Column: REMARK
     * Type: VARCHAR(255)
     * Remark: 备注
     */
    private String remark;

    /**
     * Column: RETRY_COUNT
     * Type: INT
     * Default value: 5
     * Remark: 失败重试次数
     */
    private Integer retryCount;
    //重写equals方法根据主键id对比
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ExpressFeedback that = (ExpressFeedback) o;
        return expressFeedbackId.equals(that.expressFeedbackId);
    }
}
