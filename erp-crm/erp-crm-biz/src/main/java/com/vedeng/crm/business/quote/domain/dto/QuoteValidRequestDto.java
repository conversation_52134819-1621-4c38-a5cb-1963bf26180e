package com.vedeng.crm.business.quote.domain.dto;

import com.vedeng.common.core.utils.validator.group.DefaultGroup;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class QuoteValidRequestDto {

    /**
     * 报价单
     */
    @NotNull(message = "报价单id不能为空", groups = DefaultGroup.class)
    private Integer quoteorderId;

    /**
     * true 生效
     * false 失效
     */
    @NotNull(message = "生效状态不能为空", groups = DefaultGroup.class)
    private boolean valid;

    //@NotNull(message = "是否确认", groups = DefaultGroup.class)
    private Boolean confirmed;//默认已确认，confirmed为false时，将检查是否有任务未结束
}
