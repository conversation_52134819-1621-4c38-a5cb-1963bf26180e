package com.vedeng.erp.trader.service;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.base.Page;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.trader.dto.*;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public interface VisitRecordApiService {


    /**
     * 查询拜访计划列表
     * @param pageParam
     * @return
     */
    PageInfo<VisitInputDto> searchVisitRecordListPage(PageParam<VisitSearchDto> pageParam);

    /**
     * 查询拜访计划详情
     * @param visitId
     * @return
     */
    VisitInputApiDto queryVisitRecordById(Integer visitId);

    /**
     * 工作台，按拜访人查询拜访计划数
     * @param userId
     * @return
     */
    Map<String, Integer> queryVisitRecordByVisitorId(Integer userId);


    /**
     * 获取拜访人
     * @return
     */
    List<VisitUser> queryVisitUserListByBelongUser();

    int saveBatchVisitRecord(VisitBatchInputApiDto visitBatchInputApiDto);

//    /**
//     * 创建拜访计划
//     * @param VisitInputApiDto
//     * @return
//     */
//    VisitInputApiDto insertVisitRecord(VisitInputApiDto VisitInputApiDto);
//
//    /**
//     * 编辑拜访计划
//     * @param VisitInputApiDto
//     * @return
//     */
//    VisitInputApiDto editVisitRecord(VisitInputApiDto VisitInputApiDto);

//    /**
//     * 打卡
//     * @param visitCardInputDto
//     * @return
//     */
//    VisitInputApiDto saveVisitCard(VisitCardInputDto visitCardInputDto);
//
//    /**
//     *
//     * @param visitContentInputDto
//     * @return
//     */
//    VisitInputApiDto saveVisitContent(VisitContentInputDto visitContentInputDto);

//    /**
//     * 编辑拜访内容
//     * @param visitContentInputDto
//     * @return
//     */
//    VisitInputApiDto editVisitContent(VisitContentInputDto visitContentInputDto);

    /**
     * 删除拜访记录
     * @param visitDeleteDto
     * @return
     */
    boolean deleteVisitRecord(VisitDeleteDto visitDeleteDto);


    boolean checkIfTraderExists(List<Integer> userIdList,Integer traderId,Integer customerType);


    @Data
    class VisitUser {
        private Integer userId;
        private String username;

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            VisitUser visitUser = (VisitUser) o;
            return Objects.equals(userId, visitUser.userId);
        }

        @Override
        public int hashCode() {
            return Objects.hash(userId);
        }
    }

    /**
     * 上线后初始化拜访计划的部门
     */
    public List<VisitInputDto> selectInitVisitRecordList();

    public void initVisitRecord(VisitInputDto visitInputDto);
}
