<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans 
		http://www.springframework.org/schema/beans/spring-beans-3.2.xsd"
	default-lazy-init="true">
	
	<bean id="jedisPool" class="redis.clients.jedis.JedisSentinelPool">
		<constructor-arg index="0" value="${masterName}" />
		<constructor-arg index="1">
			<set>
				<value>${redis.sentinel1}</value>
				<value>${redis.sentinel2}</value>
				<value>${redis.sentinel3}</value>
			</set>
		</constructor-arg>
		<constructor-arg index="2" ref="jedisPoolConfig" />
		<constructor-arg index="3" value="${redis.sentinelPassword}" />
		<!-- <constructor-arg index="3" value="${redis.timeout}" type="int" />
		<constructor-arg index="4" value="${redis.password}"/>
		<constructor-arg index="5" value="${redis.database}" type="int" />
		<constructor-arg index="6" value="${redis.clientName}"/> -->
	</bean>

	<bean id="jedisPoolConfig" class="redis.clients.jedis.JedisPoolConfig">
        <property name="maxIdle" value="${redis.maxIdle}" />
        <property name="maxTotal" value="${redis.maxTotal}" />
        <property name="MaxWaitMillis" value="${redis.MaxWaitMillis}" />
        <property name="testOnBorrow" value="${redis.testOnBorrow}" />
    </bean>

	<!-- 哨兵配置 -->
	<bean id="sentinelConfig"
		  class="org.springframework.data.redis.connection.RedisSentinelConfiguration">
		<!-- 服务名称 -->
		<property name="master">
			<bean class="org.springframework.data.redis.connection.RedisNode">
				<property name="name" value="${masterName}" />
			</bean>
		</property>
		<!-- 哨兵服务IP和端口 -->
		<property name="sentinels">
			<set>
				<bean class="org.springframework.data.redis.connection.RedisNode">
					<constructor-arg name="host" value="${sentinelIP1}" />
					<constructor-arg name="port" value="${redis.sentinelPort}" />
				</bean>
				<bean class="org.springframework.data.redis.connection.RedisNode">
					<constructor-arg name="host" value="${sentinelIP2}" />
					<constructor-arg name="port" value="${redis.sentinelPort}" />
				</bean>
				<bean class="org.springframework.data.redis.connection.RedisNode">
					<constructor-arg name="host" value="${sentinelIP3}" />
					<constructor-arg name="port" value="${redis.sentinelPort}" />
				</bean>
			</set>
		</property>
	</bean>
    <!-- spring data redis -->
    <bean id="jedisConnectionFactory"
        class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory">
     <!--<property name="usePool" value="true"></property>

         <constructor-arg index="0" ref="jedisPoolConfig" />
         -->
		<constructor-arg name="sentinelConfig" ref="sentinelConfig" />
		<constructor-arg name="poolConfig" ref="jedisPoolConfig" />
        <property name="password" value="${redis.sentinelPassword}" />
        <property name="timeout" value="${redis.timeout}" />  
        <property name="database" value="${redis.default.db}"></property>
    </bean>
	<!-- jdk序列化器，可保存对象 -->
	<bean id="jdkSerializationRedisSerializer"
		  class="org.springframework.data.redis.serializer.JdkSerializationRedisSerializer" />
	<!-- String序列化器 -->
	<bean id="stringRedisSerializer"
		  class="org.springframework.data.redis.serializer.StringRedisSerializer" />

  
    <bean name="redisTemplate"  
        class="org.springframework.data.redis.core.StringRedisTemplate"  
        lazy-init="false">  
        <property name="connectionFactory" ref="jedisConnectionFactory" />
		<property name="keySerializer" ref="stringRedisSerializer" />
		<property name="defaultSerializer" ref="stringRedisSerializer" />
		<property name="valueSerializer" ref="jdkSerializationRedisSerializer" />
    </bean>  



	<!-- redisSessionDAO -->
	<bean id="redisSessionDAO" class="com.vedeng.common.shiro.RedisSessionDAO">
		<property name="redisTemplate" ref="redisTemplate" />
		<property name="expire" value="${shiro_redis_session}" />
	</bean>

	<!-- cacheManager -->
	<bean id="cacheManager" class="com.vedeng.common.shiro.RedisCacheManager">
		<property name="redisTemplate" ref="redisTemplate" />
		<property name="expire" value="${shiro_redis_cache}" />
	</bean>

	
</beans>