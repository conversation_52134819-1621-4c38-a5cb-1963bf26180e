package com.vedeng.goods.service.api;


import com.vedeng.goods.model.dto.OrgDTO;
import com.vedeng.price.dto.SkuPriceInfoDetailResponseDto;

import java.util.List;

/**商品远程调用其他服务api接口
 * <AUTHOR>
 */
public interface VgoodsApiService {


    /**
     * 获取区域商城
     *
     * @return 区域集合
     */
    List<OrgDTO> getOrgDTOList();


    /**
     * 获取skuNo核价基本信息失败
     *
     * @param skuNo sku编号
     * @return
     */
    SkuPriceInfoDetailResponseDto findSkuPriceInfoBySkuNo(String skuNo);


}
