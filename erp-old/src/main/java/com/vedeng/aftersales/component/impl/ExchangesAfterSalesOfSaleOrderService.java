package com.vedeng.aftersales.component.impl;

import com.vedeng.aftersales.component.AbstractAfterSalesOrderServiceOfSaleOrder;
import com.vedeng.aftersales.component.dto.AbstractAfterSaleOrderDto;
import com.vedeng.aftersales.component.dto.AfterSaleOrderAddDto;
import com.vedeng.aftersales.component.dto.AfterSaleOrderCloseDto;
import com.vedeng.aftersales.component.dto.AfterSaleOrderGoodAddDto;
import com.vedeng.aftersales.component.exception.AfterSaleCloseException;
import com.vedeng.aftersales.component.exception.AfterSaleDetailException;
import com.vedeng.aftersales.component.exception.AfterSaleValidateException;
import com.vedeng.aftersales.component.exception.LockSaleOrderSkuOnCreateException;
import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesDetail;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.trader.dao.TraderAddressMapper;
import com.vedeng.trader.model.TraderAddress;
import com.wms.service.context.ThreadLocalContext;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: daniel
 * @Date: 2021/6/21 14 47
 * @Description:
 */
@Service
public class ExchangesAfterSalesOfSaleOrderService extends AbstractAfterSalesOrderServiceOfSaleOrder {

    @Resource
    private TraderAddressMapper traderAddressMapper;

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Resource
    private AfterSalesMapper afterSalesMapper;

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Override
    protected void customerAfterSaleOrderCreateValidate(Object addDto) throws AfterSaleValidateException {
        AfterSaleOrderAddDto afterSaleOrderAddDto = (AfterSaleOrderAddDto)addDto;

        //售后原因如果为空
        if (afterSaleOrderAddDto.getAfterSalesReason() == null) {
            throw new AfterSaleValidateException("售后原因不能为空");
        }

        if (CollectionUtils.isEmpty(afterSaleOrderAddDto.getGoodsList())) {
            throw new AfterSaleValidateException("所选择的售后商品为空");
        }

        if(StringUtil.isEmpty(afterSaleOrderAddDto.getContactProvince()) ||
                StringUtil.isEmpty(afterSaleOrderAddDto.getContactCity()) ||
                StringUtil.isEmpty(afterSaleOrderAddDto.getContactArea())) {
            throw new AfterSaleValidateException("省/市/区参数为空！");
        }
        if (StringUtil.isEmpty(afterSaleOrderAddDto.getContactAddress()) || afterSaleOrderAddDto.getContactAddress().length() > 256) {
            throw new AfterSaleValidateException("收货地址为必填且不大于256个字符！");
        }

        Saleorder saleorder = saleorderMapper.getSaleorderByOrderNo(afterSaleOrderAddDto.getSaleOrderNo());

        for (AfterSaleOrderGoodAddDto goods:afterSaleOrderAddDto.getGoodsList()) {
            SaleorderGoods saleorderGoods = saleorderGoodsMapper.getSaleorderGoodsBySaleorderIdAndSkuNo(saleorder.getSaleorderId(), goods.getSkuNo());
            if(goods.getNum() < 1 || (saleorderGoods.getDeliveryNum() == null ? 0 : saleorderGoods.getDeliveryNum().intValue()) < goods.getNum()){
                throw new AfterSaleValidateException("所选择的SKU:"+goods.getSkuNo()+"数量等于0或申请售后数量大于已发货数量！");
            }
        }
    }

    @Override
    protected void customerPropertySet(AfterSaleOrderAddDto afterSaleOrderAddDto, AfterSales afterSales) {

    }

    @Override
    protected void customerAfterSaleOrderClose(AfterSaleOrderCloseDto afterSaleOrderCloseDto) throws AfterSaleCloseException {
        AfterSales afterSales = afterSalesMapper.getAfterSalesByNo(afterSaleOrderCloseDto.getAfterSalesNo());
        Saleorder saleorder = saleorderMapper.getSaleorderBySaleorderId(afterSales.getOrderId());
        saleorder.setServiceStatus(3);//售后关闭
        boolean isHaveUnlockSaleorderGoods = isHaveUnlockSaleorderGoods(saleorder);
        boolean isHaveReturnGoodsOrMoneyAfterSaleorder = isHaveReturnGoodsOrMoneyAfterSaleorder(saleorder);
        if(!isHaveUnlockSaleorderGoods && !isHaveReturnGoodsOrMoneyAfterSaleorder){
            saleorder.setLockedStatus(0);
        }
        if(0 == saleorderMapper.updateByPrimaryKeySelective(saleorder)){
            throw new AfterSaleCloseException("关闭售后单遇到错误！");
        }
        if(saleorder.getLockedStatus() != null && saleorder.getLockedStatus()==0){
            updateUnlockSaleOrderWarning(saleorder.getSaleorderId());
        }
        List<AfterSalesGoodsVo> afterSalesGoodsList = afterSalesGoodsMapper.getAfterSalesGoodListForSaleorder(afterSales.getAfterSalesId());
        if(CollectionUtils.isEmpty(afterSalesGoodsList)){
            throw new AfterSaleCloseException("未查询到售后单商品信息！");
        }

        saleorderGoodsMapper.updateGoodsNoLockStatusBySaleorderGoodsId(afterSalesGoodsList);

        isHaveUnlockSaleorderGoods = isHaveUnlockSaleorderGoods(saleorder);

        boolean isHaveReturnMoneyAfterSaleorder = isHaveReturnMoneyAfterSaleorder(saleorder);

        if(!isHaveUnlockSaleorderGoods && !isHaveReturnMoneyAfterSaleorder){
            saleorder.setLockedStatus(0);//0未锁定，1已锁定
            saleorderMapper.updateByPrimaryKeySelective(saleorder);
            updateUnlockSaleOrderWarning(saleorder.getSaleorderId());
        }

    }

    private boolean isHaveReturnGoodsOrMoneyAfterSaleorder(Saleorder saleorder) {
        AfterSales afterSales = new AfterSales();
        afterSales.setOrderId(saleorder.getSaleorderId());
        return afterSalesMapper.getAfterSalesNoEndByorderId(afterSales).isEmpty();
    }

    @Override
    protected void customerAddAfterSaleGood(AfterSaleOrderAddDto afterSaleOrderAddDto) {

    }

    @Override
    protected <T extends AbstractAfterSaleOrderDto> void customerAddAfterSaleDetail(AfterSalesDetail afterSalesDetail, T afterSaleOrderAddDto) throws AfterSaleDetailException {
        Saleorder saleorder = ThreadLocalContext.get("saleorder");
        if (null == saleorder || null == saleorder.getTraderId()) {
            throw new AfterSaleDetailException("售后单关联销售单不存在！");
        }
        String areaIds = afterSaleOrderAddDto.getContactProvince()+","+afterSaleOrderAddDto.getContactCity()+","+afterSaleOrderAddDto.getContactArea();
        TraderAddress traderAddress = traderAddressMapper.getAddressInfoByAddress(saleorder.getTraderId(), CommonConstants.TRADER_TYPE_1, areaIds, Integer.parseInt(afterSaleOrderAddDto.getContactArea()), afterSaleOrderAddDto.getContactAddress());
        if (traderAddress == null) {
            traderAddress = new TraderAddress();
            traderAddress.setAreaId(Integer.valueOf(afterSaleOrderAddDto.getContactArea()));
            traderAddress.setTraderType(CommonConstants.TRADER_TYPE_1);
            traderAddress.setIsEnable(ErpConst.ONE);
            traderAddress.setAddress(afterSaleOrderAddDto.getContactAddress());
            traderAddress.setAreaIds(areaIds);
            traderAddress.setTraderId(saleorder.getTraderId());
            traderAddress.setAddTime(DateUtil.sysTimeMillis());
            traderAddress.setCreator(2);
            traderAddress.setUpdater(2);
            traderAddress.setModTime(DateUtil.sysTimeMillis());
            traderAddressMapper.insertSelective(traderAddress);
        }
        afterSalesDetail.setAddressId(traderAddress.getTraderAddressId());
        afterSalesDetail.setAreaId(traderAddress.getAreaId());
        afterSalesDetail.setAddress(traderAddress.getAddress());
    }

    @Override
    protected void customeAddDealOfSaleOrder(Object addDto) throws LockSaleOrderSkuOnCreateException {
        AfterSaleOrderAddDto afterSaleOrderAddDto = (AfterSaleOrderAddDto) addDto;
        lockSaleOrderSkuOnCreate(afterSaleOrderAddDto);
    }

    @Override
    protected void customerAfterSaleOrderModifyValidate(Object modifyDto){

    }
    @Override
    public void customeModifyDealOfSaleOrder(Object modifyDto){
        //重新锁定销售单商品
        lockSaleOrderSkuOnModify(modifyDto);
    }
}
