package com.vedeng.common.core.utils.numgenerator;


import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.vedeng.common.core.utils.numgenerator.bean.NoGeneratorBean;
import com.vedeng.common.redis.utils.RedisUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 系统中编号生成抽象类
 * @date 2022/7/13 11:05
 */
@Slf4j
public abstract class NumGenerator {


    private static final Integer[] DICT_MAP_LIST = new Integer[]{0, 7, 1, 6, 9, 3, 4, 5, 2, 8};

    public static final String SALT = "1487245";

    private static final String DATE_FORMAT = "yyMMdd";

    private static String zero = "0000000000";

    /**
     * 生成 给予自定义组装方法并去调用
     *
     * @param t 编号生成参数类
     * @return 编号
     */
    final String generate(NoGeneratorBean t) {
        return this.getPrefix(t) + this.compose(t);
    }

    public abstract String compose(NoGeneratorBean t);

    /**
     * 编号前缀 动态前缀方法
     *
     * @return 前缀
     */
    public abstract String getPrefix(NoGeneratorBean t);


    /**
     * 调用持久化方法
     *
     * @return 是否成功
     */
    public abstract boolean persistence();


    /**
     * 主键 依据主键加盐生成随机数
     *
     * @param noGeneratorBean 入参 numberOfDigits id
     * @return String
     */
    public String generatePrimaryKeyNum(NoGeneratorBean noGeneratorBean) {
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yy");
        StringBuilder distNum = new StringBuilder(sdf.format(date));
        // 盐
        // 总位数-2
        // 盐数值：获取 根据盐进行补0的数字
        // 初始数值： id * 3  + 盐数值
        // 遍历 初始数值 根据 DICT_MAP_LIST 中 下标数 获取对应数值，在拼接
        String digits = StrUtil.padAfter(SALT, noGeneratorBean.getNumberOfDigits() - 2, '0');
        String s = String.valueOf(noGeneratorBean.getId() * 3 + Integer.parseInt(digits));
        String[] ss = s.split("");

        for (String v : ss) {
            distNum.append(DICT_MAP_LIST[Integer.parseInt(v)]);
        }
        return distNum.toString();
    }


    /**
     * 基础加密规则
     *
     * @param noGeneratorBean 入参
     * @return String
     */
    public String encryptionRule(NoGeneratorBean noGeneratorBean) {
        return "";
    }

    /**
     * 基础随机数规则
     *
     * @param noGeneratorBean 随机数
     * @return String
     */
    public String randomKeyNum(NoGeneratorBean noGeneratorBean) {
        return RandomUtil.randomNumbers(noGeneratorBean.getRandomDigits());
    }

    /**
     * 基础redis规则
     * 根据redis获取当日自增的编号
     * num的位数由length限制，redis中的值未到0补全
     *
     * @param noGeneratorBean key redis中的key 次key一定要唯一 length 自增的数字的num 的长度
     * @return String
     */
    public String getDayAutoIncrementNoByRedis(NoGeneratorBean noGeneratorBean) {
        // 时间
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        // 自增
        long l = RedisUtil.StringOps.incrBy(noGeneratorBean.getKey(), 1);
        if (l == 0) {
            l = RedisUtil.StringOps.incrBy(noGeneratorBean.getKey(), 1);
        }
        // 当日
        int expireTime = (int) ((cal.getTimeInMillis() - System.currentTimeMillis()) / 1000);
        RedisUtil.KeyOps.expire(noGeneratorBean.getKey(), expireTime, TimeUnit.SECONDS);
        StringBuilder suffix = new StringBuilder();
        StringBuilder append = suffix.append(zero).append(l);

        String str = append.substring(append.length() - noGeneratorBean.getRedisLength(), append.length());
        log.info("redis返回的编号:{}", str);
        return str;
    }

    /**
     * 基础日期格式化方法
     *
     * @param noGeneratorBean 入参 dateFormat
     * @return String 格式化字符串
     */
    public String getDateFormatNo(NoGeneratorBean noGeneratorBean) {

        Date date = new Date();
        String fmt = DATE_FORMAT;
        if (StrUtil.isNotEmpty(noGeneratorBean.getDateFormat())) {
            fmt = noGeneratorBean.getDateFormat();
        }
        SimpleDateFormat sdf = new SimpleDateFormat(fmt);

        return sdf.format(date);
    }

}
