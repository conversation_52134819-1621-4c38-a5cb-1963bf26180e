package com.vedeng.erp.kingdee.batch.repository;
import org.apache.ibatis.annotations.Param;
import java.util.List;

import com.vedeng.erp.kingdee.batch.dto.BatchRVirtualInvoiceJWarehouseDto;


/**
 * @description ${end}
 * <AUTHOR>
 * @date 2023/11/27 9:37
 **/
public interface BatchRVirtualInvoiceJWarehouseDtoMapper {
    int deleteByPrimaryKey(Integer virtualInvoiceWarehouseId);

    int insert(BatchRVirtualInvoiceJWarehouseDto record);

    int insertSelective(BatchRVirtualInvoiceJWarehouseDto record);

    BatchRVirtualInvoiceJWarehouseDto selectByPrimaryKey(Integer virtualInvoiceWarehouseId);

    int updateByPrimaryKeySelective(BatchRVirtualInvoiceJWarehouseDto record);

    int updateByPrimaryKey(BatchRVirtualInvoiceJWarehouseDto record);

    List<BatchRVirtualInvoiceJWarehouseDto> selectByWarehouseGoodsOutInItemIds(@Param("list")List<Integer> list);

    List<BatchRVirtualInvoiceJWarehouseDto> selectByVirtualInvoiceId(@Param("virtualInvoiceId")Integer virtualInvoiceId);

    List<BatchRVirtualInvoiceJWarehouseDto> findByWarehouseGoodsOutInItemId(@Param("warehouseGoodsOutInItemId") Integer warehouseGoodsOutInItemId);

    List<BatchRVirtualInvoiceJWarehouseDto> findByVirtualInvoiceId(@Param("virtualInvoiceId") Integer virtualInvoiceId);

    int deleteByVirtualInvoiceWarehouseIds(@Param("list")List<Integer> list);








}