package com.vedeng.erp.buyorder.dto;

import lombok.Data;

/**
 * @Author: Putin
 * @CreateTime: 2023-01-17  15:11
 */
@Data
public class ExpenseBuyForSaleDetail {
    /**
     * 销售商品id
     */
    private Integer saleorderGoodsId;
    /**
     * 采购单号
     */
    private String buyorderNo;
    /**
     * 采购进程
     */
    private String buyorderDemand;
    /**
     * 状态更新时间
     */
    private String buyProcessModTimeString;
    /**
     * 采购对接人
     */
    private String buyDockUserName;

    /**
     * 采购状态
     */
    private Integer buyorderStatus;
}
