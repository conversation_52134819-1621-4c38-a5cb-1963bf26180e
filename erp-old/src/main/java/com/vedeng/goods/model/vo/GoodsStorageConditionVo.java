package com.vedeng.goods.model.vo;

import lombok.Data;

import java.util.Arrays;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Data
public class GoodsStorageConditionVo implements Cloneable {

    /**
     * 存储条件（温度)
     *
     * @see com.vedeng.goods.enums.GoodsStorageConditionTemperatureEnum
     * @since ERP_SV_2020_61
     */
    private Integer storageConditionTemperature;

    /**
     * 存储条件（温度,单位：摄氏度），其他温度范围值存储较小值
     *
     * @since ERP_SV_2020_61
     */
    private Float storageConditionTemperatureLowerValue;

    /**
     * 存储条件（温度，单位：摄氏度），其他温度范围值存储较大值
     *
     * @since ERP_SV_2020_61
     */
    private Float storageConditionTemperatureUpperValue;

    /**
     * 存储条件（湿度，单位：%）：范围值存储较小的值
     *
     * @since ERP_SV_2020_61
     */
    private Float storageConditionHumidityLowerValue;

    /**
     * 存储条件（湿度，单位：%）：范围值存储较大的
     *
     * @since ERP_SV_2020_61
     */
    private Float storageConditionHumidityUpperValue;

    /**
     * 存储条件（其他）
     *
     * @see com.vedeng.goods.enums.GoodsStorageConditionOthersEnum
     * @since ERP_SV_2020_61
     */
    private Integer[] storageConditionOthersArray;


    @Override
    public GoodsStorageConditionVo clone() {
        GoodsStorageConditionVo copiedObject = new GoodsStorageConditionVo();
        copiedObject.setStorageConditionTemperature(this.getStorageConditionTemperature());
        copiedObject.setStorageConditionTemperatureLowerValue(this.getStorageConditionTemperatureLowerValue());
        copiedObject.setStorageConditionTemperatureUpperValue(this.getStorageConditionTemperatureUpperValue());
        copiedObject.setStorageConditionHumidityLowerValue(this.getStorageConditionHumidityLowerValue());
        copiedObject.setStorageConditionHumidityUpperValue(this.getStorageConditionHumidityUpperValue());
        if (storageConditionOthersArray != null && storageConditionOthersArray.length != 0) {
            copiedObject.setStorageConditionOthersArray(Arrays.copyOf(storageConditionOthersArray, storageConditionOthersArray.length));
        }
        return copiedObject;
    }
}
