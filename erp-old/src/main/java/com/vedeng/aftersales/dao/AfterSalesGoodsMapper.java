package com.vedeng.aftersales.dao;

import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.aftersales.model.BuyOrderAndAfterSalesDto;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface AfterSalesGoodsMapper {
    int deleteByPrimaryKey(Integer afterSalesGoodsId);

    int insert(AfterSalesGoods record);

    int insertSelective(AfterSalesGoods record);

    AfterSalesGoods selectByPrimaryKey(Integer afterSalesGoodsId);

    int updateByPrimaryKeySelective(AfterSalesGoods record);

    int updateByPrimaryKey(AfterSalesGoods record);

	/**
	 * <b>Description:</b><br> 根据ORDER_DETAIL_ID和AFTER_SALES_ID查询售后退货的数量
	 *
	 * @param afterSalesGoodsVo
	 * @return
	 * @Note <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月10日 下午4:01:13
	 */
    Integer getAfterSalesNumByParam(AfterSalesGoodsVo afterSalesGoodsVo);

	/**
	 * <b>Description:</b><br> 带采购查询销售订单的售后数量
	 *
	 * @param saleorderGoodsIds
	 * @return
	 * @Note <b>Author:</b> east
	 * <br><b>Date:</b> 2018年2月1日 下午7:05:08
	 */
    List<AfterSalesGoodsVo> getPurchaseAfterSalesNum(@Param("saleorderGoodsIds") List<SaleorderGoodsVo> saleorderGoodsIds);


	/**
	 * <b>Description:</b><br> 带采购查询销售订单的售后数量
	 *
	 * @param saleorderGoodsIdList
	 * @return
	 * @Note <b>Author:</b> duke
	 * <br><b>Date:</b> 2019年8月1日 下午7:05:08
	 */
	List<AfterSalesGoodsVo> getPurchaseAfterSalesNumList(@Param("saleorderGoodsIdList") List<Integer> saleorderGoodsIdList);

	/**
	 * <b>Description:</b><br> 查询采购商品的售后退货数量---直发
	 *
	 * @param saleorderGoodsIds
	 * @return
	 * @Note <b>Author:</b> east
	 * <br><b>Date:</b> 2018年2月1日 下午7:05:08
	 */
    List<AfterSalesGoodsVo> getBuyorderAfterSalesNum(@Param("bgvList") List<SaleorderGoodsVo> bgvList);

	List<AfterSalesGoodsVo> getBuyorderAfterSalesNumList(@Param("saleorderGoodsIdList") List<Integer> saleorderGoodsIdList);


	/**
	 * <b>Description:</b><br> 根据saleorderGoodsId查询采购商品的售后退货数量
	 *
	 * @param saleorderGoodsIds
	 * @return
	 * @Note <b>Author:</b> east
	 * <br><b>Date:</b> 2018年2月1日 下午7:05:08
	 */
    Integer getBuyorderAfterSalesNumBySaleorderGoodsId(@Param("saleorderGoodsId") Integer saleorderGoodsId);

	/**
	 * <b>Description:</b><br> 查询采购商品的售后退货数量
	 *
	 * @param saleorderGoodsIds
	 * @return
	 * @Note <b>Author:</b> east
	 * <br><b>Date:</b> 2018年2月1日 下午7:05:08
	 */
    List<BuyorderGoodsVo> getBuyorderAfterSalesNumByIds(@Param("saleorderGoodsList") List<SaleorderGoodsVo> saleorderGoodsList);

	/**
	 * <b>Description:</b><br> 批量查询采购商品的售后退货数量
	 *
	 * @param saleorderGoodsIds
	 * @return
	 * @Note <b>Author:</b> east
	 * <br><b>Date:</b> 2018年2月1日 下午7:05:08
	 */
    List<BuyorderGoodsVo> batchBuyorderAfterSalesNums(@Param("buyorderGoodsList") List<BuyorderGoodsVo> buyorderGoodsList);

	/**
	 * <b>Description:</b><br> 查询销售订单的售后产品列表信息
	 *
	 * @param afterSalesGoods
	 * @return
	 * @Note <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月17日 下午6:48:18
	 */
    List<AfterSalesGoodsVo> getAfterSalesGoodsVosList(AfterSalesGoods afterSalesGoods);

	/**
	 * <b>Description:</b><br> 查询采购订单的售后产品列表信息
	 *
	 * @param afterSalesGoods
	 * @return
	 * @Note <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月17日 下午6:48:18
	 */
    List<AfterSalesGoodsVo> getBuyorderAfterSalesGoodsVosList(AfterSalesGoods afterSalesGoods);

	List<AfterSalesGoodsVo> getAfterSalesGoodList(@Param("afterSalesId") Integer afterSalesId);

	List<AfterSalesGoodsVo> getAfterSalesGoodListForSaleorder(@Param("afterSalesId") Integer afterSalesId);


	/**
	 * <b>Description:</b><br> 根据售后安调的id查询售后产品列表信息
	 *
	 * @param afterSalesGoods
	 * @return
	 * @Note <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月17日 下午6:48:18
	 */
    List<AfterSalesGoodsVo> getAfterSalesGoodsVosListByAfterSalesInstallstionId(Integer afterSalesInstallstionId);

    /**
	 * <b>Description:</b><br> 仓储物流订单下的商品
	 *
	 * @param afterSalesVo
	 * @return
	 * @Note <b>Author:</b> scott
	 * <br><b>Date:</b> 2017年10月17日 下午5:31:10
	 */
	List<AfterSalesGoodsVo> getAfterSalesGoodsVoList(AfterSalesVo afterSalesVo);

	/**
	 * <b>Description:</b><br> 根据id查询售后产品
	 *
	 * @param afterSalesGoods
	 * @return
	 * @Note <b>Author:</b> scott
	 * <br><b>Date:</b> 2017年10月19日 上午11:15:10
	 */
	AfterSalesGoodsVo getAfterSalesGoodsInfo(AfterSalesGoods afterSalesGoods);

	/**
	 * <b>Description:</b><br> 查询退换货订单的退换货手续费信息
	 *
	 * @param afterSalesGoods
	 * @return
	 * @Note <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月18日 上午11:28:12
	 */
	List<AfterSalesGoods> getThhGoodsList(AfterSalesGoods afterSalesGoods);

	/**
	 * <b>Description:</b><br> 查询退换货订单的退换货手续费信息
	 *
	 * @param afterSalesGoods
	 * @return
	 * @Note <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月18日 上午11:28:12
	 */
	AfterSalesGoodsVo getSpecialGoods(AfterSalesGoods afterSalesGoods);

	/**
	 * <b>Description:</b><br> 编辑售后服务费
	 *
	 * @param afterSalesId
	 * @return
	 * @Note <b>Author:</b> east
	 * <br><b>Date:</b> 2017年11月2日 下午2:09:20
	 */
	int updateAfterSalesServiceMoney(AfterSalesGoods afterSalesGoods);

	/**
	 * <b>Description:</b><br> 删除售后订单关联的产品
	 *
	 * @param afterSalesId
	 * @return
	 * @Note <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月23日 下午3:15:35
	 */
	int delAfterSalesGoodsByParam(Integer afterSalesId);

	/**
	 * <b>Description:</b><br> 获取售后（销售and采购）退换货产品入库信息
	 *
	 * @param asg
	 * @return
	 * @Note <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年10月23日 下午1:57:11
	 */
	List<AfterSalesGoodsVo> getAfterReturnGoodsStorageList(AfterSalesGoods asgv);

	/**
	 * <b>Description:</b><br> 换货商品在仓库中的位置
	 *
	 * @param afterSalesGoodsVo
	 * @return
	 * @Note <b>Author:</b> scott
	 * <br><b>Date:</b> 2017年11月1日 上午10:33:25
	 */
	List<AfterSalesGoodsVo> warehouseList(AfterSalesGoodsVo afterSalesGoodsVo);

	/**
	 * <b>Description:</b><br> 快递已发商品数
	 *
	 * @param afterSalesGoodsVo
	 * @return
	 * @Note <b>Author:</b> scott
	 * <br><b>Date:</b> 2017年11月7日 下午1:56:24
	 */
	Integer getKdNum(AfterSalesGoodsVo afterSalesGoodsVo);

	/**
	 * <b>Description:</b><br>
	 *
	 * @param afterGoodsIdList
	 * @param companyId
	 * @param operateType
	 * @return
	 * @Note <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年11月16日 下午5:44:10
	 */
	List<AfterSalesGoods> getAfterGoodsList(@Param("afterGoodsIdList") List<Integer> afterGoodsIdList, @Param("companyId") Integer companyId, @Param("operateType") Integer operateType);

	/**
	 * <b>Description:</b><br>
	 *
	 * @param afterGoodsDetailList
	 * @param companyId
	 * @param i
	 * @return
	 * @Note <b>Author:</b> duke
	 * <br><b>Date:</b> 2018年2月10日 下午8:22:18
	 */
	List<AfterSalesGoods> getSaleGoodsWarehouseList(@Param("afterGoodsIdList") List<Integer> afterGoodsIdList, @Param("companyId") Integer companyId, @Param("operateType") Integer operateType);

	/**
	 * <b>Description:</b><br> 查询已完结售后的退货数量
	 *
	 * @param bgvList
	 * @return
	 * @Note <b>Author:</b> east
	 * <br><b>Date:</b> 2018年3月9日 下午3:49:18
	 */
	Integer getSaleorderAftersaleReturnGoods(@Param("saleorderGoodsId") Integer saleorderGoodsId);

	Integer getSaleorderAfterSaleArrivalGoods(@Param("saleorderGoodsId") Integer saleorderGoodsId);

	/**
	 * <b>Description:</b><br> 批量查询已完结售后的退货数量
	 *
	 * @param bgvList
	 * @return
	 * @Note <b>Author:</b> east
	 * <br><b>Date:</b> 2018年3月9日 下午3:49:18
	 */
	List<SaleorderGoodsVo> batchSaleorderAftersaleReturnGoods(@Param("saleorderGoodsList") List<SaleorderGoodsVo> saleorderGoodsList);

	/**
	 * <b>Description:</b><br> 查询已完结售后的退货数量
	 *
	 * @param bgvList
	 * @return
	 * @Note <b>Author:</b> east
	 * <br><b>Date:</b> 2018年3月9日 下午3:49:18
	 */
	Integer getBuyorderAftersaleReturnGoods(@Param("buyorderGoodsId") Integer buyorderGoodsId);

	/**
	 * 查询已完结售后的退货数量
	 * <b>Description:</b><br>
	 *
	 * @param bgvList
	 * @return
	 * @Note <b>Author:</b> Cooper
	 * <br><b>Date:</b> 2018年7月2日 上午11:42:07
	 */
	List<AfterSalesGoodsVo> getBuyorderAftersaleReturnGoodsByList(@Param("bgvList") List<BuyorderGoodsVo> bgvList);

	/**
	 * <b>Description:</b><br> 查询销售产品关联的采购产品的已完结售后的退货数量
	 *
	 * @param bgvList
	 * @return
	 * @Note <b>Author:</b> east
	 * <br><b>Date:</b> 2018年3月9日 下午3:49:18
	 */
	Integer getBuyorderAftersaleReturnGoodsBySaleorderGoodsId(@Param("saleorderGoodsId") Integer saleorderGoodsId);

	/**
	 * <b>Description:</b><br> 批量查询销售产品关联的采购产品的已完结售后的退货数量
	 *
	 * @param bgvList
	 * @return
	 * @Note <b>Author:</b> east
	 * <br><b>Date:</b> 2018年3月9日 下午3:49:18
	 */
	List<SaleorderGoodsVo> batchBuyorderAftersaleReturnGoodsBySaleorderGoodsId(@Param("saleorderGoodsList") List<SaleorderGoodsVo> saleorderGoodsList);

	/**
	 * <b>Description:</b><br> 查询销售售后生成的条码数
	 *
	 * @param afterSalesGoodsVo
	 * @return
	 * @Note <b>Author:</b> scott
	 * <br><b>Date:</b> 2018年3月20日 上午8:57:19
	 */
	AfterSalesGoodsVo getAfterSalesGoodsBnum(AfterSalesGoodsVo afterSalesGoodsVo);

	/**
	 * <b>Description:</b><br> 更新所有的特殊商品收发货状态
	 *
	 * @param afterSalesGoods
	 * @Note <b>Author:</b> scott
	 * <br><b>Date:</b> 2018年4月28日 上午9:36:22
	 */
	void updateAllTsGoodsInfo(AfterSalesGoods afterSalesGoods);

	/**
	 * <b>Description:</b><br> 查询退货订单的产品
	 *
	 * @param afterSalesGoods
	 * @return
	 * @Note <b>Author:</b> east
	 * <br><b>Date:</b> 2017年10月18日 上午11:28:12
	 */
	List<SaleorderGoodsVo> getReturnGoodsList(@Param("afterSalesId") Integer afterSalesId);

	/**
	 * <b>Description:</b><br> 查询订单下未出库的售后商品
	 *
	 * @param afterSalesId
	 * @return
	 * @Note <b>Author:</b> scott
	 * <br><b>Date:</b> 2018年7月17日 下午1:18:35
	 */
	List<AfterSalesGoods> getAfterSalesGoodsNoOutList(Integer afterSalesId);

	/**
	 * <b>Description:</b><br> 批量更新售后产品状态
	 *
	 * @param afterSalesGoodsList
	 * @return
	 * @Note <b>Author:</b> scott
	 * <br><b>Date:</b> 2018年7月17日 下午1:42:37
	 */
	Integer updateByPrimaryKeySelectiveBatch(List<AfterSalesGoods> afterSalesGoodsList);

	/**
	 * <b>Description:</b><br> 批量更新售后产品入库状态
	 *
	 * @param afterorderGoodList
	 * @return
	 * @Note <b>Author:</b> scott
	 * <br><b>Date:</b> 2018年7月19日 上午11:25:29
	 */
	Integer updateByPrimaryKeySelectiveInBatch(List<AfterSalesGoods> afterorderGoodList);

	/**
	 * <b>Description:</b><br> 查询采购商品的售后退货数量
	 *
	 * @param saleorderGoodsIds
	 * @return
	 * @Note <b>Author:</b> east
	 * <br><b>Date:</b> 2018年2月1日 下午7:05:08
	 */
    List<AfterSalesGoodsVo> getBuyorderAfterSalesNumByBgvList(@Param("bgvList") List<BuyorderGoodsVo> bgvList);

	/**
	 * <b>Description:</b><br> 售后单下除去特殊商品的所有商品
	 *
	 * @param afterGoodsId
	 * @return
	 * @Note <b>Author:</b> scott
	 * <br><b>Date:</b> 2018年9月6日 上午8:56:45
	 */
	List<AfterSalesGoods> getAfterGoodsShList(Integer afterGoodsId);

    /**
	 * <b>Description:</b><br>  根据多个ORDER_DETAIL_ID和AFTER_SALES_ID查询售后退货的数量
	 *
	 * @param afterSalesGoodsVo
	 * @return
	 * @Note <b>Author:</b> Michael
	 * <br><b>Date:</b> 2018年7月26日 下午3:00:42
	 */
    List<BuyorderGoodsVo> getAfterSalesListNumByParam(AfterSalesGoodsVo afterSalesGoodsVo);

	/**
	 * <b>Description:</b><br>根据afterSaleGoodsId集合获取
	 *
	 * @param relatedIdList
	 * @return
	 * @Note <b>Author:</b> Michael
	 * <br><b>Date:</b> 2018年8月1日 上午8:57:56
	 */
    List<AfterSalesGoods> getAfterSalesGoodsInfoByList(List<Integer> relatedIdList);

    /**
	 * <b>Description:</b><br>根据afterSaleList去批量更新收货信息
	 *
	 * @param asgList
	 * @return
	 * @Note <b>Author:</b> Michael
	 * <br><b>Date:</b> 2018年8月1日 上午9:21:03
	 */
    int batchUpdateArrivalByList(List<AfterSalesGoods> asgList);

    /**
	 * <b>Description:</b><br>根据afterSaleList去批量更新发货信息
	 *
	 * @param asgList
	 * @return
	 * @Note <b>Author:</b> Michael
	 * <br><b>Date:</b> 2018年8月1日 上午9:21:03
	 */
    int batchUpdateDeliveryByList(List<AfterSalesGoods> asgList);


	/**
	 * <b>Description: 根据afterSalesId获取当前售后订单商品（不包含特殊商品）</b><br>
	 *
	 * @param afterSalesVoList
	 * @param parendId         [数字字典配置的特殊商品的父类ID]
	 * @return <b>Author: Franlin.wu</b>
	 * <br><b>Date: 2018年12月17日 下午8:38:47 </b>
	 */
    List<AfterSalesGoods> selectByAfterSalesId(@Param("afterSalesVoList") List<AfterSalesVo> afterSalesVoList, @Param("parendId") Integer parendId);

    /**
	 * <b>Description: 根据商品ID和售后主键ID</b><br>
	 *
	 * @param record
	 * @return <b>Author: Franlin.wu</b>
	 * <br><b>Date: 2018年12月18日 下午1:47:20 </b>
	 */
    int updateByGoodsIdAndAfterSalesId(AfterSalesGoods record);

	/**
	 * 通过售后商品单id获取归属订单id
	 *
	 * @Author:strange
	 * @Date:17:44 2019-11-13
	 */
	Integer getSaleOrderIdByafterSalesGoodsId(@Param("relatedId") Integer relatedId);

	/**
	 * 获取售后单内商品未出库数量
	 *
	 * @Author:strange
	 * @Date:17:42 2019-12-07
	 */
    List<AfterSalesGoods> getAfterSalesGoodsNoOutNumList(Integer afterSalesId);

	Integer updateDataTimeByOrderId(Integer orderId);

	Integer updateDataTimeByDetailId(Integer orderDetailId);

	/**
	 * 获取采购售后退货数量
	 *
	 * @return
	 * <AUTHOR>
	 * @Date 3:42 下午 2020/5/21
	 * @Param
	 **/
	List<AfterSalesGoodsVo> getBuyorderReturnGoodsByBuyorderId(@Param("buyorderId") Integer buyorderId);

	/**
	 * @description: 获取采购退货单已完成售后数量
	 * @return:
	 * @author: Strange
	 * @date: 2020/7/30
	 **/
	Integer getAfterbuyorderNumByBuyorderGoodsId(AfterSalesGoods searchGoods);



	List<AfterSalesGoodsVo> getAfterSaleGoodListBySaleId(@Param("afterSalesId") Integer afterSalesId);

	/**
	 * @description: 获取销售售后商品信息
	 * @return:  List<AfterSalesGoodsVo>
	 * @author: Strange
	 * @date: 2020/8/10
	 **/
    List<AfterSalesGoodsVo> getAftersalesGoodsList(AfterSalesGoods afterGoods);

	/**
	 * @description: 获取当前销售单进行中退货数量
	 * @return:
	 * @author: Strange
	 * @date: 2020/8/17
	 **/
    Integer getSaleorderAfterNowReturnNum(Integer saleorderGoodsId);

	/**
	 * 根据售后订单号获取售后订单商品(所有)
	 *
	 * @param afterSalesNo
	 * @return
	 */
	List<AfterSalesGoods> getAfterSalesGoodsByAfterSalesNo(String afterSalesNo);

	AfterSalesGoods getAfterSalesGoodsBySalesNo(@Param("afterSalesNo") String afterSalesNo,@Param("skuId") Integer skuId);

    int getBuyOrderHistoryReturnNum(@Param("buyorderGoodsId") Integer buyorderGoodsId);

	/**
	 * @description: 获取销售售后商品信息
	 * @return:  List<AfterSalesGoodsVo>
	 * @author: Strange
	 * @date: 2020/8/10
	 **/
	List<AfterSalesGoodsVo> getAftersalesGoodsListIsDirect(AfterSalesGoods afterGoods);

	List<AfterSalesGoods> getAfterSalesGoodsByAfterSalesId(Integer afterSalesId);

	List<AfterSalesGoods> getAfterSalesGoodsByAfterSalesIdList(@Param("afterSalesIdList") List<Integer> afterSalesIdList);

    List<SaleorderGoods> getRelatedSaleOrderGoods(Integer afterSalesId);

	/**
	 * 根据关联的id查询销售单明细信息
	 * @param afterSalesGoods
	 * @return
	 */
	SaleorderGoods getRelatedSaleOrderGoodsByGoodsId(AfterSalesGoods afterSalesGoods);

	/**
	 * @desc 根据采购单查询指定售后商品信息
	 * @param buyorderId
	 * @param buyorderGoodsId
	 * @param buyorderNo
	 * @return
	 */
	List<AfterSalesGoods> getSpecialAftersalesGoodsList(@Param("buyorderId") Integer buyorderId,@Param("buyorderGoodsId")Integer buyorderGoodsId,
														@Param("buyorderNo")String buyorderNo,@Param("afterSalesStatus")Integer afterSalesStatus);

	List<AfterSalesVo> getAfterSaleInfobySaleorderGoodsIds(@Param("saleorderGoodsIdList") List<Integer> saleorderGoodsIdList, @Param("type") Integer type);

	/**
	 * 查此售后单之前的商品退货数量
	 *
	 * @param afterSales
	 * @return
	 */
	List<AfterSalesGoodsVo> getExcludeCurrentGoodNum(AfterSalesVo afterSales);

	AfterSalesGoodsVo getAfterSalesGoodsInfoDetail(AfterSalesGoodsVo afterSalesGoodsVo);

	/**
	 * 获取销售订单的完结的售后退货单商品数量
	 * @param saleOrderId 销售订单id
	 * @return 退货数量
	 */
	List<AfterSalesGoods> getAfterSalesGoodsCountOfSaleOrderRefund(@Param("saleOrderId") Integer saleOrderId);

	/**
	 * 获取售后单除去特殊商品的售后商品
	 * @param afterSalesId
	 * @return
	 */
	List<AfterSalesGoods> getAfterSalesGoodExceptSpecialGoodsList(Integer afterSalesId);

	/** 获取所有的售后商品
	 *
	 * @param afterSalesId
	 * @return
	 */
	List<AfterSalesGoodsVo> getAllAftersalesGoodsList(Integer afterSalesId);


	/**
	 * 根据售后单id查询对应的销售商品
	 * @param afterSalesId
	 * @return
	 */
	List<Integer> getSaleorderGoodsIdByAftersaleId(@Param("afterSalesId") Integer afterSalesId);

	/**
	 * 根据直发采购售后关联销售售后ID 查询采购单
	 * @param afterSalesId
	 * @return
	 */
	List<AfterSalesGoodsVo> getAfterSalesByDeliveryDirectAfterId(@Param("afterSalesId") Integer afterSalesId, @Param("skuId") Integer skuId);



	/**
	 * 查询售后单安调商品信息
	 * @param afterSalesId
	 * @return
	 */
	List<AfterSalesGoodsVo> getAtGoodsInfoList(@Param("afterSalesId") Integer afterSalesId);

	/**
	 * 删除不在工程师安调信息内的 售后安调商品
	 */
	void deleteUnInstallGoods(@Param("afterSalesId") Integer afterSalesId, @Param("afterSalesGoodsIds") List<Integer> afterSalesGoodsIds);

	/**
	 * 获取当前产品的最小出库时间
	 * @param orderDetailId 销售明细
	 * @return long 出库时间
	 */
	Long getOutTimeByWarehouseGoodsOperateLog(Integer orderDetailId);

	/**
	 * 获取当前销售单采购单的签收时间
	 * @param saleorderGoodsId 销售单明细id
	 * @return 签收时间
	 */
	Long getArriveTimeByBuyOrderExpressTime(Integer saleorderGoodsId);

	/**
	 * 获取当前销售单的签收时间
	 * @param saleorderGoodsId 销售单明细
	 * @return 签收时间
	 */
	Long getArriveTimeBySaleOrderExpressTime(Integer saleorderGoodsId);

	/**
	 * 查询售后退货数量
	 * @param orderGoodsId 订单商品id
	 * @param companyId 公司id
	 * @param afterType 区分售后类型
	 * @return 每个商品的售后数量
	 */
	Integer getGoodsAfterReturnNum(@Param("orderGoodsId") Integer orderGoodsId, @Param("companyId") Integer companyId, @Param("afterType") Integer afterType);


	/**
	 * 根据主键查询售后单明细
	 * @param afterSalesGoodsId
	 * @return
	 */
	AfterSalesGoods getAfterSalesGoodsByAfterSalesGoodsId(@Param("afterSalesGoodsId") Integer afterSalesGoodsId);

	List<Integer> getExpenseAfterSalesStatusByAfterSalesId(@Param("afterSalesId") Integer afterSalesId);

	List<Integer> getExpenseAfterSalesStatusBySaleGoodsIds(@Param("list") List<AfterSalesGoodsVo> afterSalesGoodsList);

	AfterSalesGoods queryHaveInstallationAndSn(AfterSalesGoods afterSalesGoods);

    List<BuyOrderAndAfterSalesDto> selectBuyOrderByAfterSalesGoods(List<Integer> list);
}