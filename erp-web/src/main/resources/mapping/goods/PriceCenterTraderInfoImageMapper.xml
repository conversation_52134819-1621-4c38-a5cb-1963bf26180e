<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.dao.PriceCenterTraderInfoImageMapper">


    <select id="getTraderInfoImage" resultType="com.newtask.model.TraderInfoImageDto">
        SELECT
        tr.TRADER_ID,tr.TRADER_NAME
        FROM T_TRADER tr
        left join T_TRADER_SUPPLIER tts
        on tr.TRADER_ID = tts.TRADER_ID
        where
	    tts.TRADER_ID is not null
        and  tr.MOD_TIME >= unix_timestamp(DATE_SUB(NOW(), INTERVAL 20 MINUTE) )*1000
limit 5000
    </select>

    <select id="getTraderInfoImageAll" resultType="com.newtask.model.TraderInfoImageDto">
         SELECT
        tr.TRADER_ID,tr.TRADER_NAME
        FROM T_TRADER tr
        left join T_TRADER_SUPPLIER tts
        on tr.TRADER_ID = tts.TRADER_ID
        where
	    tts.TRADER_ID is not null
    </select>


</mapper>