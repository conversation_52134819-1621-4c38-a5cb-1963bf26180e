package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.annotation.KingDeeID;
import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import lombok.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 其他入库   https://www.yuque.com/manhuo/gf1570/uilh7d
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date
 */
@Getter
@Setter
@WriteBackField(needBackField = {"FID","FEntity.FEntryID", "FEntity.F_QZOK_BDDJHID"})
public class KingDeeStorageInDto extends KingDeeMqBaseDto {

    /**
     * id
     */
    private Integer id;

    /**
     * 单据内码  0：表示新增
     * 非0：云星空系统单据FID值，表示修改
     */
    @WriteBackField
    @KingDeeID
    private String fId;

    /**
     * 单据编号 填写单据编号，若为空则调用系统的编码规则生成
     */
    @BusinessID
    private String fBillNo;
    /**
     * 贝登单据头ID 贝登单据头ID号（预留）
     */
    private String fQzokBddjtId;

    /**
     * 库存方向 如果是普通入库，默认值 ：GENERAL
     * 如果是退货，则默认值 ： RETURN
     */
    private String fStockDirect;
    /**
     * 单据日期 格式yyyy-MM-dd
     */
    private String fDate;

    /**
     * 明细
     */
    private List<KingDeeStorageInDetailDto> fEntity = new ArrayList<>();

    /**
     * 单据类型 填单据类型编码，默认QTRKD01_SYS
     */
    private String fBillTypeId;

    /**
     * 库存组织 填写组织编码
     */
    private String fStockOrgId;

    /**
     * 供应商 填写供应商编码
     */
    private String fSupplierId;

    /**
     * 客户编码
     */
    private String fQzokKh;

    /**
     * 部门 填写部门编码，默认值 ：BM9999
     */
    private String fDeptId;


    public KingDeeStorageInDto() {
        this.fBillTypeId = "QTRKD01_SYS";
        this.fStockOrgId = KingDeeConstant.ORG_ID.toString();
        this.fDeptId = "BM9999";
    }

    @Override
    public String getFormId() {
        return KingDeeFormConstant.STK_MISCELLANEOUS;
    }
}
