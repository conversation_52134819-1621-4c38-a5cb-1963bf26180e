<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="编辑基本信息" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<%--<%@ include file="supplier_tag.jsp" %>--%>
<script type="text/javascript" src="<%= basePath %>static/js/jquery/ajaxfileupload.js?rnd=${resourceVersionKey}"></script>
<div style="width: 100%;height: 20px"></div>
<form method="post"
      action="${pageContext.request.contextPath}/trader/supplier/saveeditbaseinfo.do" id="supplierForm">
    <div class="baseinforcontainer" style="padding-bottom: 15px;">
        <div class="border overflow-hidden">
            <div class="baseinfor f_left">基本信息</div>
        </div>
        <div class="baseinforeditform">
            <ul>
                <li>
                    <div class="infor_name" style="width: 125px">
                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="18px" height="18px">
                        <span>*</span>
                        <lable>供应商名称</lable>
                    </div>
                    <div class="f_left">
                        <input type="text" class="input-largest errobor" name="traderName"
                               id="traderName" value="${traderSupplier.trader.traderName }"/>
                    </div>
                    &nbsp;&nbsp;
                    <input type="button" class="btn bg-light-blue btn-small margin-left-20" onclick="getTyWarehouseArea()" value="贝登天眼数据同步">
                </li>
                <li>
                    <div class="infor_name" style="width: 125px">
                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="18px" height="18px">
                        <span>*</span>
                        <lable>供应商所在地区</lable>
                    </div>
                    <div class="f_left">
                        <select class="input-middle mr6" name="province">
                            <option value="0">请选择</option>
                            <c:if test="${not empty provinceList }">
                                <c:forEach items="${provinceList }" var="province">
                                    <option value="${province.regionId }"
                                            <c:if test="${ not empty provinceRegion &&  province.regionId == provinceRegion.regionId }">selected="selected"</c:if>>${province.regionName }</option>
                                </c:forEach>
                            </c:if>
                        </select> <select class="input-middle mr6" name="city">
                        <option value="0">请选择</option>
                        <c:if test="${not empty cityList }">
                            <c:forEach items="${cityList }" var="city">
                                <option value="${city.regionId }"
                                        <c:if test="${ not empty cityRegion &&  city.regionId == cityRegion.regionId }">selected="selected"</c:if>>${city.regionName }</option>
                            </c:forEach>
                        </c:if>
                    </select> <select class="input-middle mr6" name="zone" id="zone">
                        <option value="0">请选择</option>
                        <c:if test="${not empty zoneList }">
                            <c:forEach items="${zoneList }" var="zone">
                                <option value="${zone.regionId }"
                                        <c:if test="${ not empty zoneRegion &&  zone.regionId == zoneRegion.regionId }">selected="selected"</c:if>>${zone.regionName }</option>
                            </c:forEach>
                        </c:if>
                    </select>
                    </div>
                </li>

                <li>
                    <div class="infor_name" style="width: 125px">
                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="18px" height="18px">
                        <span>*</span>
                        <lable>库房地区</lable>
                    </div>
                    <div class="f_left">
                        <select class="input-middle mr6" name="warehouseProvince">
                            <option value="0">请选择</option>
                            <c:if test="${not empty provinceList }">
                                <c:forEach items="${provinceList }" var="province">
                                    <option value="${province.regionId }"
                                            <c:if test="${ not empty warehouseProvinceRegion &&  province.regionId == warehouseProvinceRegion.regionId }">selected="selected"</c:if>>${province.regionName }</option>
                                </c:forEach>
                            </c:if>
                        </select> <select class="input-middle mr6" name="warehouseCity" id="warehouseCity">
                        <option value="0">请选择</option>
                        <c:if test="${not empty warehouseCityList}">
                            <c:forEach items="${warehouseCityList }" var="city">
                                <option value="${city.regionId }"
                                        <c:if test="${ not empty warehouseCityRegion &&  city.regionId == warehouseCityRegion.regionId }">selected="selected"</c:if>>${city.regionName }</option>
                            </c:forEach>
                        </c:if>
                    </select> <select class="input-middle mr6" name="warehouseAreaId" id="warehouseAreaId">
                        <option value="0">请选择</option>
                        <c:if test="${not empty warehouseZoneList}">
                            <c:forEach items="${warehouseZoneList}" var="zone">
                                <option value="${zone.regionId }"
                                        <c:if test="${ not empty warehouseZoneRegion &&  zone.regionId == warehouseZoneRegion.regionId }">selected="selected"</c:if>>${zone.regionName}</option>
                            </c:forEach>
                        </c:if>
                    </select>
                    </div>

                    &nbsp;&nbsp;&nbsp;
                    <span class="font-grey9" id="referenceInfo">
					</span>
                </li>

                <li>
                    <div class="form-tips" style="width: 125px">
                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="18px" height="18px">
                        <span>*</span>
                        <lable>库房详细地址</lable>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" name='warehouseAddress' id='warehouseAddress' value="${traderSupplier.warehouseAddress}" class="input-largest"/>
                        </div>
                        <div id="warehouseAddressError"></div>
                    </div>
                </li>

                <li>
                    <div class="infor_name" style="width: 125px">
                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="18px" height="18px">
                        <span>*</span>
                        <label>供应商类型</label>
                    </div>
                    <div class="f_left inputradio">
                        <select class="input-middle f_left mr10 mt0" id="traderType" name="traderTypeSup">
                            <option value="0">请选择</option>
                            <option value="1" <c:if test="${traderSupplier.traderType eq 1}">selected</c:if>>生产厂家</option>
                            <option value="2" <c:if test="${traderSupplier.traderType eq 2}">selected</c:if>>渠道商</option>
                        </select>
                    </div>
                </li>

                <li>
                    <div class="form-tips">
                        <lable>热线电话</lable>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" name='hotTelephone' id='hotTelephone' class="input-large"
                                   value="${traderSupplier.hotTelephone }"/>
                        </div>
                        <div id="hotTelephoneError"></div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <span>*</span>
                        <lable>售后总对接人</lable>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" name='afterSaleManager' id='afterSaleManager' class="input-large"
                                   value="${traderSupplier.afterSaleManager}" placeholder="联系人姓名"/>

                            <input type="text" name='serviceTelephone' id='serviceTelephone' class="input-large"
                                   value="${traderSupplier.serviceTelephone }" placeholder="联系方式" onblur="checkContactWay(this,'联系方式只能包含数字或-')"/>
                        </div>
                        <div id="serviceTelephoneError"></div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <span>*</span>
                        <lable>安装服务联系人</lable>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" name='installServiceContactName' id='installServiceContactName' class="input-large"
                                   placeholder="联系人姓名" value="${traderSupplier.installServiceContactName }" />
                            <input type="text" name='installServiceContactWay' id='installServiceContactWay' class="input-large"
                                   placeholder="联系方式" value="${traderSupplier.installServiceContactWay }" onblur="checkContactWay(this,'联系方式只能包含数字或-')"/>
                        </div>
                        <div id="installServiceContactNameError"></div>
                    </div>
                </li>

                <li>
                    <div class="form-tips">
                        <lable>技术支持联系人</lable>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" name='technicalDirectContactName' id='technicalDirectContactName' class="input-large"
                                   placeholder="联系人姓名" value="${traderSupplier.technicalDirectContactName }"/>
                            <input type="text" name='technicalDirectContactWay' id='technicalDirectContactWay' class="input-large"
                                   placeholder="联系方式" value="${traderSupplier.technicalDirectContactWay }" onblur="checkContactWay(this,'联系方式只能包含数字或-')"/>
                        </div>
                        <div id="technicalDirectContactNameError"></div>
                    </div>
                </li>

                <li>
                    <div class="form-tips">
                        <lable>维修服务联系人</lable>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" name='maintenanceContactName' id='maintenanceContactName' class="input-large"
                                   placeholder="联系人姓名" value="${traderSupplier.maintenanceContactName }"/>
                            <input type="text" name='maintenanceContactWay' id='maintenanceContactWay' class="input-large"
                                   placeholder="联系方式" value="${traderSupplier.maintenanceContactWay }" onblur="checkContactWay(this,'联系方式只能包含数字或-')"/>
                        </div>
                        <div id="maintenanceContactNameError"></div>
                    </div>
                </li>

                <li>
                    <div class="form-tips">
                        <lable>退换货联系人</lable>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" name='exchangeContactName' id='exchangeContactName' class="input-large"
                                   placeholder="联系人姓名" value="${traderSupplier.exchangeContactName }"/>
                            <input type="text" name='exchangeContactWay' id='exchangeContactWay' class="input-large"
                                   placeholder="联系方式" value="${traderSupplier.exchangeContactWay }" onblur="checkContactWay(this,'联系方式只能包含数字或-')"/>
                        </div>
                        <div id="exchangeContactNameError"></div>
                    </div>
                </li>

                <li>
                    <div class="form-tips">
                        <lable>其他对接人</lable>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" name='otherContactName' id='otherContactName' class="input-large"
                                   placeholder="联系人姓名" value="${traderSupplier.otherContactName }"/>
                            <input type="text" name='otherContactWay' id='otherContactWay' class="input-large"
                                   placeholder="联系方式" value="${traderSupplier.otherContactWay }" onblur="checkContactWay(this,'联系方式只能包含数字或-')"/>
                        </div>
                        <div id="otherContactNameError"></div>
                    </div>
                </li>
              <%--  2020.11.12质量改善要求删除--%>
               <%-- <li>
                    <div class="infor_name ">
                        <label>供应品牌</label>
                    </div>
                    <div class="f_left inputradio">
                        <div class="career-one">
                            <input class="input-middle f_left mr10 mt0" placeholder="请输入关键词查询"
                                   name="bussinessBrandKey"/>
                            <div class="f_left bt-bg-style bg-light-blue bt-small mr8 searchbrand"
                                 onclick="searchBussinessBrand()">搜索
                            </div>
                            <select class="input-middle f_left mr10 mt0" name="bussinessBrands"></select>
                            <div class="f_left bt-bg-style bg-light-blue bt-small mr10 addbrand"
                                 onclick="addBussinessBrand()" id="addBussinessBrand">添加
                            </div>
                        </div>
                        <div class="addtags addbrandtags mt8 <c:if test="${ empty traderSupplier.traderSupplierSupplyBrands }">none</c:if>">
                            <ul id="bussinessBrand_show">
                                <c:if test="${not empty traderSupplier.traderSupplierSupplyBrands }">
                                    <c:forEach items="${traderSupplier.traderSupplierSupplyBrands }" var="brand">
                                        <li class="bluetag">${brand.brand.brandName }
                                            <input type="hidden" name="bussinessBrandId" value="${brand.brandId }">
                                            <i class="iconbluecha" onclick="delTag(this);"></i>
                                        </li>
                                    </c:forEach>
                                </c:if>
                            </ul>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="infor_name mt0">
                        <lable>供应商品</lable>
                    </div>
                    <div class="f_left ">
                        <input type="text" class="input-xxx"
                               name="traderSupplier.supplyProduct" id="supplyProduct"
                               placeholder="不同关键词之间以逗号分隔" value="${traderSupplier.supplyProduct }"/>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <lable>承运商名称</lable>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" name='logisticsName' id='logisticsName' class="input-large"
                                   value="${traderSupplier.logisticsName }"/>
                        </div>
                        <div id="logisticsNameError"></div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <lable>企业宣传片</lable>
                    </div>
                    <div class="f_left">
                        <c:if test="${not empty traderSupplier.companyUriList}">
                            <c:forEach items="${traderSupplier.companyUriList}" var="cmu" varStatus="status">
                                <c:if test="${status.count == 1}">
                                    <div class="form-blanks">
                                        <input type="text" name='companyUri' id='companyUri_1' class="input-largest"
                                               value="${cmu.uri}"/>
                                    </div>
                                </c:if>
                                <c:if test="${status.count > 1}">
                                    <div class="form-blanks mt10">
                                        <div class="pos_rel f_left">
                                            <input type="text" class="input-largest" id="name_${status.count}"
                                                   name="companyUri" value="${cmu.uri}"/>
                                        </div>
                                        <div class="f_left">
                                            <span class="font-red cursor-pointer mt3" onclick="delBase(${status.count})"
                                                  id="img_del_${status.count}">删除</span>
                                        </div>
                                        <div class="clear"></div>
                                    </div>
                                </c:if>
                            </c:forEach>
                        </c:if>
                        <c:if test="${empty traderSupplier.companyUriList}">
                            <div class="form-blanks">
                                <input type="text" name='companyUri' id='companyUri_1' class="input-largest"/>
                            </div>
                        </c:if>
                        <div class="mt8" id="conadd">
                            <span class="bt-border-style bt-small border-blue" onclick="conaddBase();">继续添加</span>
                        </div>
                        <div id="logisticsNameError"></div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <lable>官方网址</lable>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" name='website' id='website' class="input-large"
                                   value="${traderSupplier.website}"/>
                        </div>
                        <div id="websiteError"></div>
                    </div>
                </li>--%>
                <li>
                    <div class="infor_name mt0">
                        <lable>备注</lable>
                    </div>
                    <div class="f_left ">
                        <input type="text" class="input-xxx"
                               name="comments" id="comments" value="${traderSupplier.comments}"/>
                    </div>
                </li>
            <%--    <li>
                    <div class="infor_name mt0">
                        <lable>简介</lable>
                    </div>
                    <div class="f_left ">
                        <textarea class="input-xxx" name="brief" id="brief" >${traderSupplier.brief}</textarea>
                    </div>
                </li>--%>
            </ul>
        </div>
        <div class="add-tijiao tcenter">
            <input type="hidden" name="traderSupplier.traderSupplierId" value="${traderSupplier.traderSupplierId }">
            <input type="hidden" name="beforeParams" value='${beforeParams}'/>
            <%--<button type="submit">提交</button>--%>
            <%--<button class="dele" id="close-layer" type="button"--%>
                    <%--onclick="goUrl('${pageContext.request.contextPath}/trader/supplier/baseinfo.do?traderSupplierId=${traderSupplier.traderSupplierId}');">--%>
                <%--取消--%>
            <%--</button>--%>
        </div>
    </div>
    <div class="baseinforcontainer" style="padding-bottom: 15px;">
        <div class="border overflow-hidden">
            <div class="baseinfor f_left">资质</div>
        </div>

        <div class="addElement">
            <div class="add-main">
                <input type="hidden" name="domain" value="${domain}" id="domain">
                <ul class="add-detail add-detail1">
                    <li class="table-large" style="display: none">
                        <div class="infor_name" style="width: 155px;">
                            <span>*</span>
                            <label>三证合一</label>
                        </div>
                        <div class="f_left inputfloat mt3">
                            <input type="radio" name="threeInOne" id="one" value="1" onclick="thOne();" />
                            <label class="mr8">是</label>
                            <input type="radio" name="threeInOne" id="zero" value="0" onclick="thZero();"checked="checked"/>
                            <label>否</label>
                            <div id="threeInOneError"></div>
                        </div>
                    </li>

                    <li>
                        <div class="infor_name" style="width: 155px">
                            <span>*</span>
                            <label>纳税人分类</label>
                        </div>
                        <div class="f_left inputradio">
                            <select class="input-middle f_left mr10 mt0" name="taxPayerType" id="taxPayerType">
                                <option value="0" <c:if test="${traderSupplier.taxPayerType eq 0}">selected</c:if>></option>
                                <option value="1" <c:if test="${traderSupplier.taxPayerType eq 1}">selected</c:if>>一般纳税人</option>
                                <option value="2" <c:if test="${traderSupplier.taxPayerType eq 2}">selected</c:if>>小规模纳税人</option>
                            </select>
                        </div>
                    </li>

                    <li>
                        <div class="infor_name sex_name" style="width: 155px;">
                            <label>纳税人附件</label>
                        </div>
                        <div class="f_left insertli">
                            <ul id="taxPayer">
                                <li>
                                    <c:choose>
                                        <c:when test="${!empty taxPayerList}">
                                            <input type="hidden" value="${taxPayerList.size()}" id="taxPayerAttachmentCount">
                                            <c:forEach items="${taxPayerList }" var="taxPayer" varStatus="st">
                                                <div class="clear mb8">
                                                    <div class="pos_rel f_left mb8">
                                                        <input type="file" class="upload_file" name="lwfile"
                                                               id="file_20_${st.index}" style="display: none;"
                                                               onchange="uploadFile(this,20);"/>
                                                        <c:choose>
                                                            <c:when test="${st.index == 0}">
                                                                <input type="text" class="input-middle" id="name_20"
                                                                       readonly="readonly"
                                                                       placeholder="请上传纳税人附件" name="taxPayerName"
                                                                       onclick="file_20_${st.index}.click();"
                                                                       value="${taxPayer.name}">
                                                                <input type="hidden" id="uri_20_${st.index}"
                                                                       name="taxPayerUri" value="${taxPayer.uri}">
                                                            </c:when>
                                                            <c:otherwise>
                                                                <input type="text" class="input-middle"
                                                                       id="name_20_${st.index}" readonly="readonly"
                                                                       placeholder="请上传纳税人附件" name="name_20"
                                                                       onclick="file_20_${st.index}.click();"
                                                                       value="${taxPayer.name}">
                                                                <input type="hidden" id="uri_20_${st.index}"
                                                                       name="uri_20" value="${taxPayer.uri}">
                                                            </c:otherwise>
                                                        </c:choose>
                                                        <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                               onclick="return $('#file_20_${st.index}').click();">浏览</label>
                                                    </div>
                                                    <c:choose>
                                                        <c:when test="${taxPayer.uri ne null && taxPayer.uri ne ''}">
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7" id="img_icon_20"></i>
                                                                <a href="http://${taxPayer.domain}${taxPayer.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4"
                                                                   id="img_view_20">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0}">
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="del(20)"
                                                                              id="img_del_20">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_20">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7 none" id="img_icon_20"></i>
                                                                <a href="http://${taxPayer.domain}${taxPayer.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                                   id="img_view_20">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0}">
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="del(20)"
                                                                              id="img_del_20">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_20">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </div>
                                            </c:forEach>
                                        </c:when>

                                        <c:otherwise>
                                            <div class="mb8">
                                                <div class="f_left">
                                                    <input type="file" class="upload_file" name="lwfile" id="file_20"
                                                           style="display: none;" onchange="uploadFile(this,20);"/>
                                                    <input type="text" class="input-middle" id="name_20"
                                                           readonly="readonly"
                                                           placeholder="请上传纳税人附件" name="taxPayerName"
                                                           onclick="file_20.click();" value="${taxPayer.name}">
                                                    <input type="hidden" id="uri_20" name="taxPayerUri"
                                                           value="${taxPayer.uri}">
                                                    <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                           onclick="return $('#file_20').click();">浏览</label>
                                                </div>
                                                <!-- 上传成功出现 -->
                                                <c:choose>
                                                    <c:when test="${!empty taxPayer.uri}">
                                                        <i class="iconsuccesss ml7" id="img_icon_20"></i>
                                                        <a href="http://${taxPayer.domain}${taxPayer.uri}" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4"
                                                           id="img_view_20">查看</a>
                                                        <span class="font-red cursor-pointer mt4" onclick="del(3)"
                                                              id="img_del_20">删除</span>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <i class="iconsuccesss ml7 none" id="img_icon_20"></i>
                                                        <a href="" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                           id="img_view_20">查看</a>
                                                        <span class="font-red cursor-pointer mt4 none" onclick="del(20)"
                                                              id="img_del_20">删除</span>
                                                    </c:otherwise>
                                                </c:choose>
                                                <div class="clear"></div>
                                            </div>
                                        </c:otherwise>
                                    </c:choose>


                                    <div class="mt8 clear" id="conadd20" <c:if test="${taxPayerList.size()>=10}">style="display: none" </c:if>>
                                        <span class="bt-border-style bt-small border-blue "
                                              onclick="con_add(20);">继续添加</span>
                                    </div>

                                    <div class="font-grey9" style="margin-top:7px;">
                                        <span style="color: #999;">1，图片格式只能JPG、PNG、JPEG、BMP、PDF等格式<br>
                                             2，图片大小不超过5M<br>
                                        3，最多上传10个纳税人附件
                                        </span>

                                    </div>
                                </li>
                            </ul>
                        </div>
                    </li>


                    <li>
                        <div class="infor_name sex_name" style="width: 155px;">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="18px" height="18px">
                            <span>*</span>
                            <label>营业执照</label>
                        </div>
                        <div class="f_left insertli insertli1">
                            <ul>
                                <li style="margin-bottom:0px;">
                                    <c:choose>
                                        <c:when test="${!empty businessList}">
                                            <c:forEach items="${businessList}" var="business" varStatus="st">
                                                <c:if test="${st.index == 0}">
                                                    <c:set var="beginTime" value="${business.begintime}"></c:set>
                                                    <c:set var="endTime" value="${business.endtime}"></c:set>
                                                    <c:set var="sn" value="${business.sn}"></c:set>
                                                </c:if>
                                                <div class="mb8">
                                                    <div class="pos_rel f_left ">
                                                        <input type="file" class="upload_file" name="lwfile"
                                                               id="file_1_${st.index}" style="display: none;"
                                                               onchange="uploadFile(this,1);"/>
                                                        <c:choose>
                                                            <c:when test="${st.index == 0 }">
                                                                <input type="text" class="input-middle" id="name_1"
                                                                       readonly="readonly"
                                                                       placeholder="请上传营业执照" name="busName"
                                                                       onclick="file_1_${st.index}.click();"
                                                                       value="${business.name}">
                                                                <input type="hidden" id="uri_1_${st.index}"
                                                                       name="busUri" value="${business.uri}">
                                                            </c:when>
                                                            <c:otherwise>
                                                                <input type="text" class="input-middle"
                                                                       id="name_1_${st.index}" readonly="readonly"
                                                                       placeholder="请上传营业执照" name="name_1"
                                                                       onclick="file_1_${st.index}.click();"
                                                                       value="${business.name}">
                                                                <input type="hidden" id="uri_1_${st.index}" name="uri_1"
                                                                       value="${business.uri}">
                                                            </c:otherwise>
                                                        </c:choose>
                                                        <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                               onclick="return $('#file_1_${st.index}').click();">浏览</label>
                                                    </div>

                                                    <c:choose>
                                                        <c:when test="${business.uri ne null && business.uri ne ''}">
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7" id="img_icon_1"></i>
                                                                <a href="http://${business.domain}${business.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4"
                                                                   id="img_view_1">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0 }">
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="del(1)" id="img_del_1">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_1">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7 none" id="img_icon_1"></i>
                                                                <a href="http://${business.domain}${business.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                                   id="img_view_1">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0 }">
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="del(1)" id="img_del_1">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_1">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <div class="clear"></div>
                                                </div>
                                            </c:forEach>
                                        </c:when>
                                        <c:otherwise>
                                            <div class="mb8">
                                                <div class="f_left">
                                                    <input type="file" class="upload_file" id='file_1' name="lwfile"
                                                           style="display: none;" onchange="uploadFile(this,1);">
                                                    <input type="text" class="input-middle" id="name_1" readonly="readonly"
                                                           placeholder="请上传营业执照" name="busName" onclick="file_1.click();"
                                                           value="${business.name}">
                                                    <input type="hidden" id="uri_1" name="busUri" value="${business.uri}">
                                                    <div class="font-red " style="display: none;">请上传营业执照</div>
                                                </div>
                                                <label class="bt-bg-style bt-middle bg-light-blue ml10" type="file" id="busUpload"
                                                       onclick="return $('#file_1').click();">浏览</label>
                                                <!-- 上传成功出现 -->
                                                <c:choose>
                                                    <c:when test="${!empty business.uri}">
                                                        <i class="iconsuccesss ml7" id="img_icon_1"></i>
                                                        <a href="http://${business.domain}${business.uri}" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4" id="img_view_1">查看</a>
                                                        <span class="font-red cursor-pointer mt4" onclick="del(1)"
                                                              id="img_del_1">删除</span>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <i class="iconsuccesss ml7 none" id="img_icon_1"></i>
                                                        <a href="" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4 none" id="img_view_1">查看</a>
                                                        <span class="font-red cursor-pointer mt4 none" onclick="del(1)"
                                                              id="img_del_1">删除</span>
                                                    </c:otherwise>
                                                </c:choose>
                                            </div>
                                    </c:otherwise>
                                    </c:choose>
                                    <div class="clear" id="conadd1">
                                        <span class="bt-border-style bt-small border-blue"
                                              onclick="con_add(1);">继续添加</span>
                                    </div>

                                    <div class="font-grey9" style="margin-top:7px;">
                                        1，图片格式只能JPG、PNG、JPEG、BMP、PDF等格式<br>
                                        2，图片大小不超过5M
                                    </div>
                                </li>
                                <li style="margin-bottom:0px;">
                                    <label class="f_left mt4 mr10">设置有效期</label>
                                    <div class="f_left">
                                        <div class='inputfloat'>
                                            <input type="text" class="Wdate input-smaller" placeholder="请选择日期"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'busEndTime\')}'})" autocomplete="off"
                                                   name="busStartTime"
                                                   id="busStartTime"
                                                   value='<date:date value ="${business.begintime} " format="yyyy-MM-dd"/>'/>
                                            <input type="text" class="Wdate input-smaller" placeholder="请选择日期"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'busStartTime\')}'})" autocomplete="off"
                                                   name="busEndTime"
                                                   id="busEndTime"
                                                   value='<date:date value ="${business.endtime} " format="yyyy-MM-dd"/>'/>
                                        </div>
                                        <div class="font-red " style="display: none;">开始时间不能为空</div>
                                    </div>
                                </li>
                                <li class="inputfloat" style="margin-bottom:0px;">
                                    <input class="mt8" type="checkbox" name="isMedical"
                                           <c:if test="${business.isMedical eq 1}">checked="checked"</c:if> value="1">
                                    <label class="mt5">含有医疗器械</label>
                                </li>
                                <li style="margin-bottom:0px;">
                                    <label class="f_left mt4 mr10">设置发证日期</label>
                                    <div class="f_left">
                                        <div class='inputfloat'>
                                            <input type="text" class="Wdate input-smaller" placeholder="请选择日期"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'busEndTime\')}'})"
                                                   name="busIssueDate"
                                                   id="busIssueDate"
                                                   value='<date:date value ="${business.issueDate} " format="yyyy-MM-dd"/>'/>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                            <div id="businessError"></div>
                        </div>
                    </li>
                    <li style="display: none">
                        <div class="infor_name sex_name" style="width: 155px;">
                            <label>税务登记证</label>
                        </div>
                        <div class="f_left insertli insertli1" id="tax">
                            <ul>
                                <li>
                                    <div class="f_left">
                                        <input type="file" class="upload_file" id='file_2' name="lwfile"
                                               style="display: none;" onchange="uploadFile(this,2);">
                                        <input type="text" class="input-middle" id="name_2" readonly="readonly"
                                               <c:if test="${business.threeInOne eq 1}">disabled="disabled"</c:if>
                                               placeholder="请上传税务登记证" name="taxName" onclick="file_2.click();"
                                               value="${tax.name}">
                                        <input type="hidden" id="uri_2" name="taxUri" value="${tax.uri}">
                                        <div class="font-red " style="display: none;">请上传税务登记证</div>
                                    </div>
                                    <label class="bt-bg-style bt-middle bg-light-blue ml10 <c:if test='${business.threeInOne eq 1}'>bg-opcity</c:if>"
                                           type="file" id="taxUpload"
                                           <c:if test='${business eq null || business.threeInOne ne 1}'>onclick="return $('#file_2').click();"</c:if>>浏览</label>
                                    <!-- 上传成功出现 -->
                                    <c:choose>
                                        <c:when test="${!empty tax.uri}">
                                            <i class="iconsuccesss ml7" id="img_icon_2"></i>
                                            <a href="http://${tax.domain}${tax.uri}" target="_blank"
                                               class="font-blue cursor-pointer mr5 ml10 mt4" id="img_view_2">查看</a>
                                            <span class="font-red <c:if test="${business eq null || business.threeInOne eq 0}">cursor-pointer</c:if> mt4"
                                                  <c:if test="${business eq null || business.threeInOne eq 0}">onclick="del(2)"</c:if>
                                                  id="img_del_2">删除</span>
                                        </c:when>
                                        <c:otherwise>
                                            <i class="iconsuccesss ml7 none" id="img_icon_2"></i>
                                            <a href="" target="_blank"
                                               class="font-blue cursor-pointer mr5 ml10 mt4 none" id="img_view_2">查看</a>
                                            <span class="font-red <c:if test="${business eq null || business.threeInOne eq 0}">cursor-pointer</c:if> mt4 none"
                                                  <c:if test="${business eq null || business.threeInOne eq 0}">onclick="del(2)"</c:if>
                                                  id="img_del_2">删除</span>
                                        </c:otherwise>
                                    </c:choose>

                                </li>
                                <li>
                                    <label class="f_left mt4 mr10 <c:if test='${business.threeInOne eq 1}'>bg-opcity</c:if>">设置有效期</label>
                                    <div class="f_left">
                                        <div class='inputfloat'>
                                            <input type="text"
                                                   class="Wdate input-smaller <c:if test='${business.threeInOne eq 1}'>bg-opcity</c:if>"
                                                   placeholder="请选择日期"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'taxEndTime\')}'})" autocomplete="off"
                                                   name="taxStartTime"
                                                   id="taxStartTime"
                                                   value='<date:date value ="${tax.begintime} " format="yyyy-MM-dd"/>'
                                                   <c:if test="${business.threeInOne eq 1}">disabled="disabled"</c:if>/>
                                            <input type="text"
                                                   class="Wdate input-smaller <c:if test='${business.threeInOne eq 1}'>bg-opcity</c:if>"
                                                   placeholder="请选择日期"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'taxStartTime\')}'})" autocomplete="off"
                                                   name="taxEndTime"
                                                   id="taxEndTime"
                                                   value='<date:date value ="${tax.endtime} " format="yyyy-MM-dd"/>'
                                                   <c:if test="${business.threeInOne eq 1}">disabled="disabled"</c:if>/>
                                        </div>
                                        <div class="font-red " style="display: none;">开始时间不能为空</div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </li>
                    <li style="display: none">
                        <div class="infor_name sex_name" style="width: 155px;">
                            <label>组织机构代码证</label>
                        </div>
                        <div class="f_left insertli " id="org">
                            <ul>
                                <li style="margin-bottom:0px;">
                                    <div class="f_left">
                                        <input type="file" class="upload_file" id='file_3' name="lwfile"
                                               style="display: none;" onchange="uploadFile(this,3);">
                                        <input type="text" class="input-middle" id="name_3" readonly="readonly"
                                               <c:if test="${business.threeInOne eq 1}">disabled="disabled"</c:if>
                                               placeholder="请上传组织机构代码证" name="orgName" onclick="file_3.click();"
                                               value="${orga.name}">
                                        <input type="hidden" id="uri_3" name="orgaUri" value="${orga.uri}">
                                        <div class="font-red " style="display: none;">请上传组织机构代码证</div>
                                    </div>
                                    <label class="bt-bg-style bt-middle bg-light-blue ml10
                                    	<c:if test='${business.threeInOne eq 1}'>bg-opcity</c:if>" type="file"
                                           id="orgUpload"
                                           <c:if test='${business eq null || business.threeInOne ne 1}'>onclick="return $('#file_3').click();"</c:if>>浏览</label>
                                    <!-- 上传成功出现 -->
                                    <c:choose>
                                        <c:when test="${!empty orga.uri}">
                                            <i class="iconsuccesss ml7" id="img_icon_3"></i>
                                            <a href="http://${orga.domain}${orga.uri}" target="_blank"
                                               class="font-blue cursor-pointer mr5 ml10 mt4" id="img_view_3">查看</a>
                                            <span class="font-red <c:if test="${business eq null || business.threeInOne eq 0}">cursor-pointer</c:if> mt4"
                                                  <c:if test="${business eq null || business.threeInOne eq 0}">onclick="del(3)"</c:if>
                                                  id="img_del_3">删除</span>
                                        </c:when>
                                        <c:otherwise>
                                            <i class="iconsuccesss ml7 none" id="img_icon_3"></i>
                                            <a href="" target="_blank"
                                               class="font-blue <c:if test="${business.threeInOne eq 0}">cursor-pointer</c:if> mr5 ml10 mt4 none"
                                               id="img_view_3">查看</a>
                                            <span class="font-red cursor-pointer mt4 none"
                                                  <c:if test="${business eq null || business.threeInOne eq 0}">onclick="del(3)"</c:if>
                                                  id="img_del_3">删除</span>
                                        </c:otherwise>
                                    </c:choose>

                                </li>
                                <li style="margin-bottom:0px;">
                                    <label class="f_left mt4 mr10 <c:if test='${business.threeInOne eq 1}'>bg-opcity</c:if>">设置有效期</label>
                                    <div class="f_left">
                                        <div class='inputfloat'>
                                            <input class="Wdate input-smaller <c:if test='${business.threeInOne eq 1}'>bg-opcity</c:if>"
                                                   type="text" placeholder="请选择日期"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'orgaEndTime\')}'})" autocomplete="off"
                                                   name="orgaStartTime"
                                                   id="orgaStartTime"
                                                   value='<date:date value ="${orga.begintime} " format="yyyy-MM-dd"/>'
                                                   <c:if test="${business.threeInOne eq 1}">disabled="disabled"</c:if>/>
                                            <input class="Wdate input-smaller <c:if test='${business.threeInOne eq 1}'>bg-opcity</c:if>"
                                                   type="text" placeholder="请选择日期"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'orgaStartTime\')}'})" autocomplete="off"
                                                   name="orgaEndTime"
                                                   id="orgaEndTime"
                                                   value='<date:date value ="${orga.endtime} " format="yyyy-MM-dd"/>'
                                                   <c:if test="${business.threeInOne eq 1}">disabled="disabled"</c:if>/>
                                        </div>
                                        <div class="font-red" style="display: none;">开始时间不能为空</div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </li>
                    <li class="table-large">
                        <div class="infor_name mt0" style="width: 155px;">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="18px" height="18px">
                            <label>是否多证合一</label>
                        </div>
                        <div class="f_left inputfloat">
                            <input type="radio" name="medicalQualification" id="med1" value="1" onclick="medOne();"
                                   <c:if test="${twoMedical.medicalQualification eq 1}">checked="checked"</c:if>/>
                            <label class="mr8">是</label>
                            <input type="radio" name="medicalQualification" id="med0" value="0" onclick="medZero();"
                                   <c:if test="${twoMedical.medicalQualification ne 1}">checked="checked"</c:if>/>
                            <label>否</label>
                        </div>
                        <div class="font-grey9" style="margin-top:7px;">
                            营业执照和二类备案凭证合一
                        </div>
                    </li>
                    <li>
                        <div class="infor_name sex_name" style="width: 155px;">
                            <span id="twoMedicalSpan">
                                <c:if test="${twoMedical.medicalQualification eq 1}">*</c:if>
                            </span>
                            <label id="twoMedicalLab">
                                <c:choose>
                                    <c:when test="${twoMedical.medicalQualification eq 1}">
                                        多证合一辅助证明
                                    </c:when>
                                    <c:otherwise>
                                        医疗器械二类备案凭证
                                    </c:otherwise>
                                </c:choose>
                            </label>
                        </div>
                        <div class="f_left insertli ">
                            <ul id="two_medical">
                                <li style="margin-bottom:0;">
                                    <c:choose>
                                        <c:when test="${!empty twoMedicalList }">
                                            <c:forEach items="${twoMedicalList }" var="twoMedical" varStatus="st">
                                                <c:if test="${st.index == 0}">
                                                    <c:set var="twoBeginTime" value="${twoMedical.begintime}"></c:set>
                                                    <c:set var="twoEndTime" value="${twoMedical.endtime}"></c:set>
                                                    <c:set var="twoSn" value="${twoMedical.sn}"></c:set>
                                                </c:if>
                                                <div class="mb8">
                                                    <div class="pos_rel f_left ">
                                                        <input type="file" class="upload_file" name="lwfile"
                                                               id="file_4_${st.index}" style="display: none;"
                                                               onchange="uploadFile(this,4);"/>
                                                        <c:choose>
                                                            <c:when test="${st.index == 0 }">
                                                                <input type="text" class="input-middle" id="name_4"
                                                                       readonly="readonly"
                                                                       placeholder="请上传二类备案凭证" name="twoName"
                                                                       onclick="file_4_${st.index}.click();"
                                                                       value="${twoMedical.name}">
                                                                <input type="hidden" id="uri_4_${st.index}"
                                                                       name="twoUri" value="${twoMedical.uri}">
                                                            </c:when>
                                                            <c:otherwise>
                                                                <input type="text" class="input-middle"
                                                                       id="name_4_${st.index}" readonly="readonly"
                                                                       placeholder="请上传二类备案凭证" name="name_4"
                                                                       onclick="file_4_${st.index}.click();"
                                                                       value="${twoMedical.name}">
                                                                <input type="hidden" id="uri_4_${st.index}" name="uri_4"
                                                                       value="${twoMedical.uri}">
                                                            </c:otherwise>
                                                        </c:choose>
                                                        <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                               id="twoUpload"
                                                               onclick="return $('#file_4_${st.index}').click();">浏览</label>
                                                    </div>

                                                    <c:choose>
                                                        <c:when test="${twoMedical.uri ne null && twoMedical.uri ne ''}">
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7" id="img_icon_4"></i>
                                                                <a href="http://${twoMedical.domain}${twoMedical.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4"
                                                                   id="img_view_4">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0 }">
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="del(4)" id="img_del_4">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_4">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7 none" id="img_icon_4"></i>
                                                                <a href="http://${twoMedical.domain}${twoMedical.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                                   id="img_view_4">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0 }">
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="del(4)" id="img_del_4">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_4">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <div class="clear"></div>
                                                </div>
                                            </c:forEach>
                                        </c:when>
                                        <c:otherwise>
                                            <div class="mb8">
                                                <div class="f_left">
                                                    <input type="file" class="upload_file" name="lwfile" id="file_4"
                                                           style="display: none;" onchange="uploadFile(this,4);"/>
                                                    <input type="text" class="input-middle" id="name_4"
                                                           readonly="readonly"
                                                           placeholder="请上传二类备案凭证" name="twoName"
                                                           onclick="file_4.click();" value="${twoMedical.name}">
                                                    <input type="hidden" id="uri_4" name="twoUri"
                                                           value="${twoMedical.uri}">
                                                    <div class="font-red " style="display: none;">请上传二类备案凭证</div>
                                                </div>
                                                <label class="bt-bg-style bt-middle bg-light-blue ml10" id="twoUpload"
                                                       onclick="return $('#file_4').click();">浏览</label>
                                                <!-- 上传成功出现 -->
                                                <c:choose>
                                                    <c:when test="${!empty twoMedical.uri}">
                                                        <i class="iconsuccesss ml7" id="img_icon_4"></i>
                                                        <a href="http://${twoMedical.domain}${twoMedical.uri}"
                                                           target="_blank" class="font-blue cursor-pointer mr5 ml10 mt4"
                                                           id="img_view_4">查看</a>
                                                        <span class="font-red cursor-pointer mt4" onclick="del(4)"
                                                              id="img_del_4">删除</span>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <i class="iconsuccesss ml7 none" id="img_icon_4"></i>
                                                        <a href="" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                           id="img_view_4">查看</a>
                                                        <span class="font-red cursor-pointer mt4 none" onclick="del(4)"
                                                              id="img_del_4">删除</span>
                                                    </c:otherwise>
                                                </c:choose>
                                                <div class="clear"></div>
                                                <div id="twoMedicalError"></div>
                                            </div>
                                        </c:otherwise>
                                    </c:choose>

                                    <div class=" clear" id="conadd4">
                                        <span class="bt-border-style bt-small border-blue "
                                              onclick="con_add(4);">继续添加</span>
                                    </div>

                                    <div class="font-grey9" style="margin-top:7px;">
                                        1，图片格式只能JPG、PNG、JPEG、BMP等格式<br>
                                        2，图片大小不超过5M<br>
                                        <div id="medicalQualificationFont" <c:if test="${twoMedical.medicalQualification eq 0}">style="display: none"</c:if>>
                                             3，请上传多证合一辅助证明（必填项）
                                        </div>
                                    </div>
                                </li>
                                <li style="margin-bottom:0;">
                                    <label class='f_left mt4 mr10'>设置有效期</label>
                                    <div class="f_left ">
                                        <div class='inputfloat'>
                                            <input class="Wdate input-smaller" type="text" placeholder="请选择日期"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'twoEndTime\')}'})" autocomplete="off"
                                                   name="twoStartTime"
                                                   id="twoStartTime"
                                                   value='<date:date value ="${twoBeginTime} " format="yyyy-MM-dd"/>'/>
                                            <input class="Wdate input-smaller" type="text" placeholder="请选择日期"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'twoStartTime\')}'})" autocomplete="off"
                                                   name="twoEndTime"
                                                   id="twoEndTime"
                                                   value='<date:date value ="${twoEndTime} " format="yyyy-MM-dd"/>'/>
                                        </div>
                                        <div class="font-red " style="display: none;">开始时间不能为空</div>
                                    </div>
                                </li>
                                <li class="specialli" style="margin-bottom:0;">
                                    <label class="f_left mt4 mr10 <c:if test="${twoMedical.medicalQualification eq 1}">bg-opcity</c:if>" id="twoSnLab">
                                        许可证编号
                                    </label>
                                    <div class="f_left ">
                                        <input <c:if test="${twoMedical.medicalQualification eq 1}">disabled="disabled"</c:if> value="${twoSn}"
                                               type="text" name="twoSn" class="input-middle" id="twoSn"/>
                                        <div class="font-red " style="display: none;"></div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </li>

                    <li class=''>
                        <div class="infor_name sex_name" style="width: 155px;">
                            <label class="mt25">选择医疗器械二类</br>备案凭证详情 (新国标)</label>
                        </div>
                        <div class="f_left medicalquality overflow-hidden" style='width:84%;'>
                            <input type="checkbox" name="twoMedicalTypeNewAll" onclick="clickAll('twoMedicalTypeNew')">
                            <label>全选</label>
                            <ul class="f_left">
                                <c:forEach items="${newStandCategoryList}" var="newStandCategory">
                                    <c:set var="contains" value="false"/>
                                    <%--<c:if test="${fn:contains(newStandCategory.relatedField,2)}">--%>
                                        <c:if test="${not empty newTwo}">
                                            <c:forEach items="${newTwo}" var="mc" varStatus="status">
                                                <c:if test="${newStandCategory.value eq mc.medicalCategoryId}">
                                                    <c:set var="contains" value="true"/>
                                                </c:if>
                                            </c:forEach>
                                        </c:if>
                                        <c:choose>
                                            <c:when test="${contains == true}">
                                                <li style="width:25%;float:left;">
                                                    <input type="checkbox" name="twoMedicalTypeNew"
                                                           value="${newStandCategory.value}" checked="checked"
                                                           onclick="clickOne('twoMedicalTypeNew')">
                                                    <label>${newStandCategory.label}</label>
                                                </li>
                                            </c:when>
                                            <c:otherwise>
                                                <li style="width:25%;float:left;">
                                                    <input type="checkbox" name="twoMedicalTypeNew"
                                                           value="${newStandCategory.value}"
                                                           onclick="clickOne('twoMedicalTypeNew')">
                                                    <label>${newStandCategory.label}</label>
                                                </li>
                                            </c:otherwise>
                                        </c:choose>
                                    <%--</c:if>--%>
                                </c:forEach>
                            </ul>
                        </div>
                    </li>

                    <li class=''>
                        <div class="infor_name sex_name" style="width: 155px;">
                            <label class="mt25">选择医疗器械二类</br>备案凭证详情 (旧国标)</label>
                        </div>
                        <div class="f_left medicalquality overflow-hidden" style='width:84%;'>
                            <input type="checkbox" name="twoMedicalTypeAll" onclick="clickAll('twoMedicalType')">
                            <label>全选</label>
                            <ul class="f_left" id="medical_ul">
                                <c:forEach items="${medicalTypes }" var="mt">
                                    <c:set var="contains" value="false"/>
                                    <c:if test="${fn:contains(mt.relatedField,2)}">
                                        <c:if test="${not empty two}">
                                            <c:forEach items="${two }" var="mc" varStatus="status">
                                                <c:if test="${mt.sysOptionDefinitionId eq mc.medicalCategoryId}">
                                                    <c:set var="contains" value="true"/>
                                                </c:if>
                                            </c:forEach>
                                        </c:if>
                                        <c:choose>
                                            <c:when test="${contains == true }">
                                                <li style="width:25%;float:left;">
                                                    <input type="checkbox" name="twoMedicalType"
                                                           value="${mt.sysOptionDefinitionId }" checked="checked"
                                                           onclick="clickOne('twoMedicalType')">
                                                    <label>${mt.title}</label>
                                                </li>
                                            </c:when>
                                            <c:otherwise>
                                                <li style="width:25%;float:left;">
                                                    <input type="checkbox" name="twoMedicalType"
                                                           value="${mt.sysOptionDefinitionId }"
                                                           onclick="clickOne('twoMedicalType')">
                                                    <label>${mt.title}</label>
                                                </li>
                                            </c:otherwise>
                                        </c:choose>
                                    </c:if>
                                </c:forEach>
                            </ul>
                        </div>
                    </li>

                    <li>
                        <div class="infor_name sex_name" style="width: 155px;">
                            <label>医疗器械经营许可证 (三类)</label>
                        </div>
                        <div class="f_left insertli ">
                            <ul id="three_medical">
                                <li>
                                    <c:choose>
                                        <c:when test="${!empty threeMedicalList}">
                                            <c:forEach items="${threeMedicalList }" var="threeMedical" varStatus="st">
                                                <c:if test="${st.index == 0}">
                                                    <c:set var="beginTime" value="${threeMedical.begintime}"></c:set>
                                                    <c:set var="endTime" value="${threeMedical.endtime}"></c:set>
                                                    <c:set var="sn" value="${threeMedical.sn}"></c:set>
                                                </c:if>
                                                <div class="mb8">
                                                    <div class="pos_rel f_left ">
                                                        <input type="file" class="upload_file" name="lwfile"
                                                               id="file_5_${st.index}" style="display: none;"
                                                               onchange="uploadFile(this,5);"/>
                                                        <c:choose>
                                                            <c:when test="${st.index == 0 }">
                                                                <input type="text" class="input-middle" id="name_5"
                                                                       readonly="readonly"
                                                                       placeholder="请上传医疗器械经营许可证" name="threeName"
                                                                       onclick="file_5_${st.index}.click();"
                                                                       value="${threeMedical.name}">
                                                                <input type="hidden" id="uri_5_${st.index}"
                                                                       name="threeUri" value="${threeMedical.uri}">
                                                            </c:when>
                                                            <c:otherwise>
                                                                <input type="text" class="input-middle"
                                                                       id="name_5_${st.index}" readonly="readonly"
                                                                       placeholder="请上传医疗器械经营许可证" name="name_5"
                                                                       onclick="file_5_${st.index}.click();"
                                                                       value="${threeMedical.name}">
                                                                <input type="hidden" id="uri_5_${st.index}" name="uri_5"
                                                                       value="${threeMedical.uri}">
                                                            </c:otherwise>
                                                        </c:choose>
                                                        <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                               onclick="return $('#file_5_${st.index}').click();">浏览</label>
                                                    </div>

                                                    <c:choose>
                                                        <c:when test="${threeMedical.uri ne null && threeMedical.uri ne ''}">
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7" id="img_icon_5"></i>
                                                                <a href="http://${threeMedical.domain}${threeMedical.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4"
                                                                   id="img_view_5">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0 }">
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="del(5)" id="img_del_5">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_5">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7 none" id="img_icon_5"></i>
                                                                <a href="http://${threeMedical.domain}${threeMedical.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                                   id="img_view_5">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0 }">
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="del(5)" id="img_del_5">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_5">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <div class="clear"></div>
                                                </div>
                                            </c:forEach>
                                        </c:when>
                                        <c:otherwise>
                                            <div class="mb8">
                                                <div class="f_left">
                                                    <input type="file" class="upload_file" name="lwfile" id="file_5"
                                                           style="display: none;" onchange="uploadFile(this,5);"/>
                                                    <input type="text" class="input-middle upload_file_tmp" id="name_5"
                                                           readonly="readonly"
                                                           <c:if test="${twoMedical.medicalQualification eq 1}">disabled="disabled"</c:if>
                                                           placeholder="请上传医疗器械经营许可证 (三类)" name="threeName" onclick="file_5.click();"
                                                           value="${threeMedical.name}">
                                                    <input type="hidden" id="uri_5" name="threeUri" value="${threeMedical.uri}">
                                                    <div class="font-red " style="display: none;">请上传医疗器械经营许可证 (三类)</div>
                                                </div>
                                                <label class="bt-bg-style bt-middle bg-light-blue ml10
	                                    	<c:if test="${twoMedical.medicalQualification eq 1}">bg-opcity</c:if>"
                                                       id="threeUpload"
                                                       <c:if test="${twoMedical.medicalQualification ne 1}">onclick="return $('#file_5').click();"</c:if>>浏览</label>
                                                <!-- 上传成功出现 -->
                                                <c:choose>
                                                    <c:when test="${!empty threeMedical.uri}">
                                                        <i class="iconsuccesss ml7" id="img_icon_5"></i>
                                                        <a href="http://${threeMedical.domain}${threeMedical.uri}" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4" id="img_view_5">查看</a>
                                                        <span class="font-red <c:if test="${twoMedical eq null ||twoMedical.medicalQualification eq 0}">cursor-pointer</c:if> mt4"
                                                              <c:if test="${twoMedical eq null || twoMedical.medicalQualification ne 1}">onclick="del(5)"</c:if>
                                                              id="img_del_5">删除</span>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <i class="iconsuccesss ml7 none" id="img_icon_5"></i>
                                                        <a href="" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4 none" id="img_view_5">查看</a>
                                                        <span class="font-red <c:if test="${twoMedical eq null ||twoMedical.medicalQualification eq 0}">cursor-pointer</c:if> mt4 none"
                                                              <c:if test="${twoMedical eq null ||twoMedical.medicalQualification eq 0}">onclick="del(5)"</c:if>
                                                              id="img_del_5">删除</span>
                                                    </c:otherwise>
                                                </c:choose>
                                            </div>
                                        </c:otherwise>
                                    </c:choose>

                                    <div class="clear" id="conadd5">
                                        <span class="bt-border-style bt-small border-blue "
                                              onclick="con_add(5);">继续添加</span>
                                    </div>

                                    <div class="font-grey9" style="margin-top:7px;">
                                        1，图片格式只能JPG、PNG、JPEG、BMP、PDF等格式<br>
                                        2，图片大小不超过5M
                                    </div>

                                </li>
                                <li>
                                    <label class="f_left  mt4 mr10 <c:if test="${twoMedical.medicalQualification eq 1}">bg-opcity</c:if>">设置有效期</label>
                                    <div class="f_left ">
                                        <div class='inputfloat'>
                                            <input class="Wdate input-smaller <c:if test="${twoMedical.medicalQualification eq 1}">bg-opcity</c:if>"
                                                   type="text" placeholder="请选择日期"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'threeEndTime\')}'})" autocomplete="off"
                                                   name="threeStartTime"
                                                   id="threeStartTime"
                                                   value='<date:date value ="${threeMedical.begintime} " format="yyyy-MM-dd"/>'
                                                   <c:if test="${twoMedical.medicalQualification eq 1}">disabled="disabled"</c:if>/>
                                            <input class="Wdate input-smaller <c:if test="${twoMedical.medicalQualification eq 1}">bg-opcity</c:if>"
                                                   type="text" placeholder="请选择日期"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'threeStartTime\')}'})" autocomplete="off"
                                                   name="threeEndTime"
                                                   id="threeEndTime"
                                                   value='<date:date value ="${threeMedical.endtime} " format="yyyy-MM-dd"/>'
                                                   <c:if test="${twoMedical.medicalQualification eq 1}">disabled="disabled"</c:if>/>
                                        </div>
                                        <div class="font-red" style="display: none;">开始时间不能为空</div>
                                    </div>
                                </li>
                                <li class="specialli">
                                    <label class="f_left mt4 mr10 <c:if test="${twoMedical.medicalQualification eq 1}">bg-opcity</c:if>">许可证编号</label>
                                    <div class="f_left ">
                                        <input type="text" name="threeSn" id="threeSn" class="input-middle"
                                               value="${threeMedical.sn}"
                                               <c:if test="${twoMedical.medicalQualification eq 1}">disabled="disabled"</c:if>/>
                                        <div class="font-red" style="display: none;">许可证编号不可为空</div>
                                    </div>
                                    <div class="clear"></div>
                                </li>
                            </ul>
                        </div>
                    </li>

                    <li class=''>
                        <div class="infor_name sex_name" style="width: 155px;">
                            <label class="mt25">选择医疗器械经营许可证</br>详情(三类) (新国标)</label>
                        </div>
                        <div class="f_left medicalquality overflow-hidden" style='width:84%;'>
                            <input type="checkbox" name="threeMedicalTypeNewAll" onclick="clickAll('threeMedicalTypeNew')">
                            <label>全选</label>
                            <ul class="f_left">
                                <c:forEach items="${newStandCategoryList}" var="newStandCategory">
                                    <c:set var="contains" value="false"/>
                              <%--      <c:if test="${fn:contains(mt.relatedField,2)}">--%>
                                        <c:if test="${not empty newThree}">
                                            <c:forEach items="${newThree}" var="mc" varStatus="status">
                                                <c:if test="${newStandCategory.value eq mc.medicalCategoryId}">
                                                    <c:set var="contains" value="true"/>
                                                </c:if>
                                            </c:forEach>
                                        </c:if>
                                        <c:choose>
                                            <c:when test="${contains == true }">
                                                <li style="width:25%;float:left;">
                                                    <input type="checkbox" name="threeMedicalTypeNew"
                                                           value="${newStandCategory.value}" checked="checked"
                                                           onclick="clickOne('threeMedicalTypeNew')">
                                                    <label>${newStandCategory.label}</label>
                                                </li>
                                            </c:when>
                                            <c:otherwise>
                                                <li style="width:25%;float:left;">
                                                    <input type="checkbox" name="threeMedicalTypeNew"
                                                           value="${newStandCategory.value}"
                                                           onclick="clickOne('threeMedicalTypeNew')">
                                                    <label>${newStandCategory.label}</label>
                                                </li>
                                            </c:otherwise>
                                        </c:choose>
                                <%--    </c:if>--%>
                                </c:forEach>
                            </ul>
                        </div>
                    </li>

                    <li class=''>
                        <div class="infor_name sex_name" style="width: 155px;">
                            <label class="mt1">选择医疗器械经营许可证</br>详情 (三类) (旧国标)</label>
                        </div>
                        <div class="f_left medicalquality overflow-hidden" style="width:84%;">
                            <input type="checkbox" name="threeMedicalTypeAll" onclick="clickAll('threeMedicalType')">
                            <label>全选</label>
                            <ul class="f_left" id="medical_ul">
                                <c:forEach items="${medicalTypes }" var="mt">
                                    <c:set var="contains" value="false"/>
                                    <c:if test="${fn:contains(mt.relatedField,3)}">
                                        <c:if test="${not empty three}">
                                            <c:forEach items="${three }" var="mc" varStatus="status">
                                                <c:if test="${mt.sysOptionDefinitionId eq mc.medicalCategoryId}">
                                                    <c:set var="contains" value="true"/>
                                                </c:if>
                                            </c:forEach>
                                        </c:if>
                                        <c:choose>
                                            <c:when test="${contains == true }">
                                                <li style="width:25%;float:left;">
                                                    <input type="checkbox" name="threeMedicalType"
                                                           value="${mt.sysOptionDefinitionId }" checked="checked"
                                                           onclick="clickOne('threeMedicalType')">
                                                    <label>${mt.title}</label>
                                                </li>
                                            </c:when>
                                            <c:otherwise>
                                                <li style="width:25%;float:left;">
                                                    <input type="checkbox" name="threeMedicalType"
                                                           value="${mt.sysOptionDefinitionId }"
                                                           onclick="clickOne('threeMedicalType')">
                                                    <label>${mt.title}</label>
                                                </li>
                                            </c:otherwise>
                                        </c:choose>
                                    </c:if>
                                </c:forEach>
                            </ul>


                        </div>
                    </li>
                    <li class="">
                        <div class="infor_name sex_name" style="width: 155px;">
                            <label>医疗器械生产许可证</label>
                        </div>
                        <div class="f_left insertli">
                            <ul id="product">
                                <li>
                                    <c:choose>
                                        <c:when test="${!empty productList}">
                                            <c:forEach items="${productList}" var="product" varStatus="st">
                                                <c:if test="${st.index == 0}">
                                                    <c:set var="beginTime" value="${product.begintime}"></c:set>
                                                    <c:set var="endTime" value="${product.endtime}"></c:set>
                                                    <c:set var="sn" value="${product.sn}"></c:set>
                                                </c:if>
                                                <div class="mb8">
                                                    <div class="pos_rel f_left ">
                                                        <input type="file" class="upload_file" name="lwfile"
                                                               id="file_6_${st.index}" style="display: none;"
                                                               onchange="uploadFile(this,6);"/>
                                                        <c:choose>
                                                            <c:when test="${st.index == 0 }">
                                                                <input type="text" class="input-middle" id="name_6"
                                                                       readonly="readonly"
                                                                       placeholder="请上传医疗器械生产许可证" name="productName"
                                                                       onclick="file_6_${st.index}.click();"
                                                                       value="${product.name}">
                                                                <input type="hidden" id="uri_6_${st.index}"
                                                                       name="productUri" value="${product.uri}">
                                                            </c:when>
                                                            <c:otherwise>
                                                                <input type="text" class="input-middle"
                                                                       id="name_6_${st.index}" readonly="readonly"
                                                                       placeholder="请上传医疗器械生产许可证" name="name_6"
                                                                       onclick="file_6_${st.index}.click();"
                                                                       value="${product.name}">
                                                                <input type="hidden" id="uri_6_${st.index}" name="uri_6"
                                                                       value="${product.uri}">
                                                            </c:otherwise>
                                                        </c:choose>
                                                        <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                               id="twoUpload"
                                                               onclick="return $('#file_6_${st.index}').click();">浏览</label>
                                                    </div>

                                                    <c:choose>
                                                        <c:when test="${product.uri ne null && product.uri ne ''}">
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7" id="img_icon_6"></i>
                                                                <a href="http://${product.domain}${product.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4"
                                                                   id="img_view_6">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0 }">
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="del(6)" id="img_del_6">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_6">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7 none" id="img_icon_6"></i>
                                                                <a href="http://${product.domain}${product.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                                   id="img_view_6">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0 }">
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="del(6)" id="img_del_6">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_6">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <div class="clear"></div>
                                                </div>
                                            </c:forEach>
                                        </c:when>
                                        <c:otherwise>
                                            <div class="mb8">
                                                <div class="f_left">
                                                    <input type="file" class="upload_file" name="lwfile" id="file_6"
                                                           style="display: none;" onchange="uploadFile(this,6);"/>
                                                    <input type="text" class="input-middle" id="name_6" readonly="readonly"
                                                           placeholder="请上传医疗器械生产许可证" name="productName" onclick="file_6.click();"
                                                           value="${product.name}">
                                                    <input type="hidden" id="uri_6" name="productUri" value="${product.uri}">
                                                    <div class="font-red " style="display: none;">请上传医疗器械生产许可证</div>
                                                </div>
                                                <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                       onclick="return $('#file_6').click();">浏览</label>
                                                <!-- 上传成功出现 -->
                                                <c:choose>
                                                    <c:when test="${!empty product.uri}">
                                                        <i class="iconsuccesss ml7" id="img_icon_6"></i>
                                                        <a href="http://${product.domain}${product.uri}" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4" id="img_view_6">查看</a>
                                                        <span class="font-red cursor-pointer mt4" onclick="del(6)"
                                                              id="img_del_6">删除</span>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <i class="iconsuccesss ml7 none" id="img_icon_6"></i>
                                                        <a href="" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4 none" id="img_view_6">查看</a>
                                                        <span class="font-red cursor-pointer mt4 none" onclick="del(6)"
                                                              id="img_del_6">删除</span>
                                                    </c:otherwise>
                                                </c:choose>
                                            </div>
                                        </c:otherwise>
                                    </c:choose>

                                    <div class="clear" id="conadd6">
                                        <span class="bt-border-style bt-small border-blue "
                                              onclick="con_add(6);">继续添加</span>
                                    </div>

                                    <div class="font-grey9" style="margin-top:7px;">
                                        1，图片格式只能JPG、PNG、JPEG、BMP、PDF等格式<br>
                                        2，图片大小不超过5M
                                    </div>

                                </li>
                                <li>
                                    <label class="f_left mt4 mr10">设置有效期</label>
                                    <div class="f_left">
                                        <div class='inputfloat'>
                                            <input class="Wdate input-smaller" type="text" placeholder="请选择日期"
                                                   id="productStartTime"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'productEndTime\')}'})" autocomplete="off"
                                                   name="productStartTime"
                                                   value='<date:date value ="${product.begintime} " format="yyyy-MM-dd"/>'/>
                                            <input class="Wdate input-smaller" type="text" placeholder="请选择日期"
                                                   id="productEndTime"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'productStartTime\')}'})" autocomplete="off"
                                                   name="productEndTime"
                                                   value='<date:date value ="${product.endtime} " format="yyyy-MM-dd"/>'/>
                                        </div>
                                        <div class="font-red " style="display: none;">开始时间不能为空</div>
                                    </div>

                                </li>
                                <li class="specialli">
                                    <label class="f_left mt4 mr10">许可证编号</label>
                                    <div class="f_left ">
                                        <input type="text" name="productSn" class="input-middle" value="${product.sn}"
                                               id="productSn"/>
                                        <div class="font-red" style="display: none;"></div>
                                    </div>
                                    <div class="clear"></div>
                                </li>
                            </ul>
                        </div>
                    </li>

                    <li class="">
                        <div class="infor_name sex_name" style="width: 155px;">
                            <label>第一类医疗器械生产备案凭证</label>
                        </div>
                        <div class="f_left insertli">
                            <ul id="firstCategoryCertificate_1">
                                <li>
                                    <c:choose>
                                        <c:when test="${! empty firstCategoryCertificateList }">
                                            <c:forEach items="${firstCategoryCertificateList }" var="firstCategoryCertificate" varStatus="st">
                                                <c:if test="${st.index == 0}">
                                                    <c:set var="beginTime" value="${firstCategoryCertificate.begintime}"></c:set>
                                                    <c:set var="endTime" value="${firstCategoryCertificate.endtime}"></c:set>
                                                    <c:set var="sn" value="${firstCategoryCertificate.sn}"></c:set>
                                                </c:if>
                                                <div class="mb8">
                                                    <div class="pos_rel f_left ">
                                                        <input type="file" class="upload_file" name="lwfile"
                                                               id="file_100_${st.index}" style="display: none;"
                                                               onchange="uploadFile(this,100);"/>
                                                        <c:choose>
                                                            <c:when test="${st.index == 0 }">
                                                                <input type="text" class="input-middle" id="name_100"
                                                                       readonly="readonly"
                                                                       placeholder="请上传第一类医疗器械生产备案凭证" name="firstCategoryCertificateName"
                                                                       onclick="file_100_${st.index}.click();"
                                                                       value="${firstCategoryCertificate.name}">
                                                                <input type="hidden" id="uri_100_${st.index}"
                                                                       name="firstCategoryCertificateUri" value="${firstCategoryCertificate.uri}">
                                                            </c:when>
                                                            <c:otherwise>
                                                                <input type="text" class="input-middle"
                                                                       id="name_100_${st.index}" readonly="readonly"
                                                                       placeholder="请上传第一类医疗器械生产备案凭证" name="name_100"
                                                                       onclick="file_100_${st.index}.click();"
                                                                       value="${firstCategoryCertificate.name}">
                                                                <input type="hidden" id="uri_100_${st.index}" name="uri_100"
                                                                       value="${firstCategoryCertificate.uri}">
                                                            </c:otherwise>
                                                        </c:choose>
                                                        <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                               id="twoUpload"
                                                               onclick="return $('#file_100_${st.index}').click();">浏览</label>
                                                    </div>

                                                    <c:choose>
                                                        <c:when test="${firstCategoryCertificate.uri ne null && firstCategoryCertificate.uri ne ''}">
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7" id="img_icon_100"></i>
                                                                <a href="http://${firstCategoryCertificate.domain}${firstCategoryCertificate.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4"
                                                                   id="img_view_100">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0 }">
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="del(100)" id="img_del_100">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_100">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7 none" id="img_icon_100"></i>
                                                                <a href="http://${firstCategoryCertificate.domain}${firstCategoryCertificate.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                                   id="img_view_100">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0 }">
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="del(100)" id="img_del_100">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_100">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <div class="clear"></div>
                                                </div>
                                            </c:forEach>
                                        </c:when>
                                        <c:otherwise>
                                            <div class="mb8">
                                            <div class="f_left">
                                                <input type="file" class="upload_file" name="lwfile" id="file_100"
                                                       style="display: none;" onchange="uploadFile(this,100);"/>
                                                <input type="text" class="input-middle" id="name_100" readonly="readonly"
                                                       placeholder="请上传第一类医疗器械生产备案凭证" name="firstCategoryCertificateName"
                                                       onclick="file_100.click();" value="${firstCategoryCertificate.name}">
                                                <input type="hidden" id="uri_100" name="firstCategoryCertificateUri" value="${firstCategoryCertificate.uri}">
                                                <div id="firstCategory_certificate" class="font-red " style="display: none;">请上传第一类医疗器械生产备案凭证
                                                </div>
                                            </div>
                                            <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                   onclick="return $('#file_100').click();">浏览</label>
                                            <!-- 上传成功出现 -->
                                            <c:choose>
                                                <c:when test="${!empty firstCategoryCertificate.uri}">
                                                    <i class="iconsuccesss ml7" id="img_icon_100"></i>
                                                    <a href="http://${firstCategoryCertificate.domain}${firstCategoryCertificate.uri}" target="_blank"
                                                       class="font-blue cursor-pointer mr5 ml10 mt4" id="img_view_100">查看</a>
                                                    <span class="font-red cursor-pointer mt4" onclick="del(100)"
                                                          id="img_del_100">删除</span>
                                                </c:when>
                                                <c:otherwise>
                                                    <i class="iconsuccesss ml7 none" id="img_icon_100"></i>
                                                    <a href="" target="_blank"
                                                       class="font-blue cursor-pointer mr5 ml10 mt4 none" id="img_view_100">查看</a>
                                                    <span class="font-red cursor-pointer mt4 none" onclick="del(100)"
                                                          id="img_del_100">删除</span>
                                                </c:otherwise>
                                            </c:choose>
                                        </div></c:otherwise>
                                    </c:choose>
                                    <div class="clear" id="conadd100">
                                        <span class="bt-border-style bt-small border-blue "
                                              onclick="con_add(100);">继续添加</span>
                                    </div>

                                    <div class="font-grey9" style="margin-top:7px;">
                                        1，图片格式只能JPG、PNG、JPEG、BMP、PDF等格式<br>
                                        2，图片大小不超过5M
                                    </div>

                                </li>
                                <li>
                                    <label class="f_left mt4 mr10">设置有效期</label>
                                    <div class="f_left">
                                        <div class='inputfloat'>
                                            <input class="Wdate input-smaller" type="text" placeholder="请选择日期"
                                                   id="firstCategoryCertificateStartTime"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'firstCategoryCertificateEndTime\')}'})" autocomplete="off"
                                                   name="firstCategoryCertificateStartTime"
                                                   value='<date:date value ="${firstCategoryCertificate.begintime} " format="yyyy-MM-dd"/>'/>
                                            <input class="Wdate input-smaller" type="text" placeholder="请选择日期"
                                                   id="firstCategoryCertificateEndTime"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'firstCategoryCertificateStartTime\')}'})" autocomplete="off"
                                                   name="firstCategoryCertificateEndTime"
                                                   value='<date:date value ="${firstCategoryCertificate.endtime} " format="yyyy-MM-dd"/>'/>
                                        </div>
                                        <div id="first_categoryCertificate" class="font-red" style="display: none;">开始时间不能为空</div>
                                    </div>

                                </li>

                                <li class="specialli">
                                    <label class="f_left mt4 mr10">备案号</label>
                                    <div class="f_left ">
                                        <input type="text" name="recordNo" class="input-middle" value="${firstCategoryCertificate.recordNo}"
                                               id="recordNo"/>
                                        <div class="font-red" style="display: none;"></div>
                                    </div>
                                    <div class="clear"></div>
                                </li>

                            </ul>
                        </div>
                    </li>

                    <li class="">
                        <div class="infor_name sex_name" style="width: 155px;">
                            <label>生产企业生产产品登记表</label>
                        </div>
                        <div class="f_left insertli">
                            <ul id="productRegistration_1">
                                <li>
                                    <c:choose>
                                        <c:when test="${! empty productRegistrationList}">
                                            <c:forEach items="${productRegistrationList }" var="productRegistration" varStatus="st">
                                                <c:if test="${st.index == 0}">
                                                    <c:set var="beginTime" value="${productRegistration.begintime}"></c:set>
                                                    <c:set var="endTime" value="${productRegistration.endtime}"></c:set>
                                                    <c:set var="sn" value="${productRegistration.sn}"></c:set>
                                                </c:if>
                                                <div class="mb8">
                                                    <div class="pos_rel f_left ">
                                                        <input type="file" class="upload_file" name="lwfile"
                                                               id="file_101_${st.index}" style="display: none;"
                                                               onchange="uploadFile(this,101);"/>
                                                        <c:choose>
                                                            <c:when test="${st.index == 0 }">
                                                                <input type="text" class="input-middle" id="name_101"
                                                                       readonly="readonly"
                                                                       placeholder="请上传二类备案凭证" name="productRegistrationName"
                                                                       onclick="file_101_${st.index}.click();"
                                                                       value="${productRegistration.name}">
                                                                <input type="hidden" id="uri_101_${st.index}"
                                                                       name="productRegistrationUri" value="${productRegistration.uri}">
                                                            </c:when>
                                                            <c:otherwise>
                                                                <input type="text" class="input-middle"
                                                                       id="name_101_${st.index}" readonly="readonly"
                                                                       placeholder="请上传二类备案凭证" name="name_101"
                                                                       onclick="file_101_${st.index}.click();"
                                                                       value="${productRegistration.name}">
                                                                <input type="hidden" id="uri_101_${st.index}" name="uri_101"
                                                                       value="${productRegistration.uri}">
                                                            </c:otherwise>
                                                        </c:choose>
                                                        <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                               id="twoUpload"
                                                               onclick="return $('#file_101_${st.index}').click();">浏览</label>
                                                    </div>

                                                    <c:choose>
                                                        <c:when test="${productRegistration.uri ne null && productRegistration.uri ne ''}">
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7" id="img_icon_101"></i>
                                                                <a href="http://${productRegistration.domain}${productRegistration.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4"
                                                                   id="img_view_101">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0 }">
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="del(101)" id="img_del_101">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_101">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7 none" id="img_icon_101"></i>
                                                                <a href="http://${productRegistration.domain}${productRegistration.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                                   id="img_view_101">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0 }">
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="del(101)" id="img_del_101">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_101">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <div class="clear"></div>
                                                </div>
                                            </c:forEach>
                                        </c:when>
                                        <c:otherwise>
                                            <div class="mb8">
                                            <div class="f_left">
                                                <input type="file" class="upload_file" name="lwfile" id="file_101"
                                                       style="display: none;" onchange="uploadFile(this,101);"/>
                                                <input type="text" class="input-middle" id="name_101" readonly="readonly"
                                                       placeholder="请上传生产企业生产产品登记表" name="productRegistrationName"
                                                       onclick="file_101.click();" value="${productRegistration.name}">
                                                <input type="hidden" id="uri_101" name="productRegistrationUri" value="${productRegistration.uri}">
                                                <div id="product_registration" class="font-red " style="display: none;">请上传生产企业生产产品登记表
                                                </div>
                                            </div>
                                            <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                   onclick="return $('#file_101').click();">浏览</label>
                                            <!-- 上传成功出现 -->
                                            <c:choose>
                                                <c:when test="${!empty productRegistration.uri}">
                                                    <i class="iconsuccesss ml7" id="img_icon_101"></i>
                                                    <a href="http://${productRegistration.domain}${productRegistration.uri}" target="_blank"
                                                       class="font-blue cursor-pointer mr5 ml10 mt4" id="img_view_101">查看</a>
                                                    <span class="font-red cursor-pointer mt4" onclick="del(101)"
                                                          id="img_del_101">删除</span>
                                                </c:when>
                                                <c:otherwise>
                                                    <i class="iconsuccesss ml7 none" id="img_icon_101"></i>
                                                    <a href="" target="_blank"
                                                       class="font-blue cursor-pointer mr5 ml10 mt4 none" id="img_view_101">查看</a>
                                                    <span class="font-red cursor-pointer mt4 none" onclick="del(101)"
                                                          id="img_del_101">删除</span>
                                                </c:otherwise>
                                            </c:choose>
                                        </div>
                                        </c:otherwise>
                                    </c:choose>


                                    <div class="clear" id="conadd101">
                                        <span class="bt-border-style bt-small border-blue "
                                              onclick="con_add(101);">继续添加</span>
                                    </div>

                                    <div class="font-grey9" style="margin-top:7px;">
                                        1，图片格式只能JPG、PNG、JPEG、BMP、PDF等格式<br>
                                        2，图片大小不超过30M
                                    </div>

                                </li>
                                <li>
                                    <label class="f_left mt4 mr10">设置有效期</label>
                                    <div class="f_left">
                                        <div class='inputfloat'>
                                            <input class="Wdate input-smaller" type="text" placeholder="请选择日期"
                                                   id="productRegistrationStartTime"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'productRegistrationEndTime\')}'})" autocomplete="off"
                                                   name="productRegistrationStartTime"
                                                   value='<date:date value ="${productRegistration.begintime} " format="yyyy-MM-dd"/>'/>
                                            <input class="Wdate input-smaller" type="text" placeholder="请选择日期"
                                                   id="productRegistrationEndTime"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'productRegistrationStartTime\')}'})" autocomplete="off"
                                                   name="productRegistrationEndTime"
                                                   value='<date:date value ="${productRegistration.endtime} " format="yyyy-MM-dd"/>'/>
                                        </div>
                                        <div id="product_registration_div" class="font-red" style="display: none;">开始时间不能为空</div>
                                    </div>

                                </li>
                            </ul>
                        </div>
                    </li>

                    <li class="" style="display: none;">
                        <div class="infor_name sex_name" style="width: 155px;">
                            <label>医疗器械经营许可证</label>
                        </div>
                        <div class="f_left insertli">
                            <ul id="operate">
                                <li>
                                    <div class="f_left">
                                        <input type="file" class="upload_file" name="lwfile" id="file_7"
                                               style="display: none;" onchange="uploadFile(this,7);"/>
                                        <input type="text" class="input-middle" id="name_7" readonly="readonly"
                                               placeholder="请上传医疗机构经营许可证" name="operateName" onclick="file_7.click();"
                                               value="${operate.name}">
                                        <input type="hidden" id="uri_7" name="operateUri" value="${operate.uri}">
                                        <div class="font-red " style="display: none;">请上传医疗器械经营许可证</div>
                                    </div>
                                    <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                           onclick="return $('#file_7').click();">浏览</label>
                                    <!-- 上传成功出现 -->
                                    <c:choose>
                                        <c:when test="${!empty operate.uri}">
                                            <i class="iconsuccesss ml7" id="img_icon_7"></i>
                                            <a href="http://${operate.domain}${operate.uri}" target="_blank"
                                               class="font-blue cursor-pointer mr5 ml10 mt4" id="img_view_7">查看</a>
                                            <span class="font-red cursor-pointer mt4" onclick="del(7)"
                                                  id="img_del_7">删除</span>
                                        </c:when>
                                        <c:otherwise>
                                            <i class="iconsuccesss ml7 none" id="img_icon_7"></i>
                                            <a href="" target="_blank"
                                               class="font-blue cursor-pointer mr5 ml10 mt4 none" id="img_view_7">查看</a>
                                            <span class="font-red cursor-pointer mt4 none" onclick="del(7)"
                                                  id="img_del_7">删除</span>
                                        </c:otherwise>
                                    </c:choose>
                                </li>
                                <li>
                                    <label class="f_left mt4 mr10">设置有效期</label>
                                    <div class="f_left">
                                        <div class='inputfloat'>
                                            <input class="Wdate input-smaller" type="text" placeholder="请选择日期"
                                                   id="operateStartTime"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'operateEndTime\')}'})" autocomplete="off"
                                                   name="operateStartTime"
                                                   value='<date:date value ="${operate.begintime} " format="yyyy-MM-dd"/>'/>
                                            <input class="Wdate input-smaller" type="text" placeholder="请选择日期"
                                                   id="operateEndTime"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'operateStartTime\')}'})" autocomplete="off"
                                                   name="operateEndTime"
                                                   value='<date:date value ="${operate.endtime} " format="yyyy-MM-dd"/>'/>
                                        </div>
                                        <div class="font-red " style="display: none;">开始时间不能为空</div>
                                    </div>

                                </li>
                                <li class="specialli">
                                    <label class="f_left mt4 mr10">许可证编号</label>
                                    <div class="f_left ">
                                        <input type="text" name="operateSn" class="input-middle" value="${operate.sn}"
                                               id="operateSn"/>
                                        <div class="font-red" style="display: none;"></div>
                                    </div>
                                    <div class="clear"></div>
                                </li>

                            </ul>
                        </div>
                    </li>
                    <!-- begin by franlin for[3865 供应商资质中，增加销售人授权书，销售人信息]  at 2018-06-21  -->
                    <li class="">
                        <div class="infor_name sex_name" style="width: 155px;">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="18px" height="18px">
                            <label>销售人员授权书</label>
                        </div>
                        <div class="f_left insertli">
                            <ul id="saleAuth_1">
                                <li>
                                    <c:choose>
                                        <c:when test="${! empty saleAuthList}">
                                            <c:forEach items="${saleAuthList }" var="saleAuth" varStatus="st">
                                                <c:if test="${st.index == 0}">
                                                    <c:set var="beginTime" value="${saleAuth.begintime}"></c:set>
                                                    <c:set var="endTime" value="${saleAuth.endtime}"></c:set>
                                                    <c:set var="sn" value="${saleAuth.sn}"></c:set>
                                                </c:if>
                                                <div class="mb8">
                                                    <div class="pos_rel f_left ">
                                                        <input type="file" class="upload_file" name="lwfile"
                                                               id="file_8_${st.index}" style="display: none;"
                                                               onchange="uploadFile(this,8);"/>
                                                        <c:choose>
                                                            <c:when test="${st.index == 0 }">
                                                                <input type="text" class="input-middle" id="name_8"
                                                                       readonly="readonly"
                                                                       placeholder="请上传二类备案凭证" name="saleAuthBookName"
                                                                       onclick="file_8_${st.index}.click();"
                                                                       value="${saleAuth.name}">
                                                                <input type="hidden" id="uri_8_${st.index}"
                                                                       name="saleAuthBookUri" value="${saleAuth.uri}">
                                                            </c:when>
                                                            <c:otherwise>
                                                                <input type="text" class="input-middle"
                                                                       id="name_8_${st.index}" readonly="readonly"
                                                                       placeholder="请上传二类备案凭证" name="name_8"
                                                                       onclick="file_8_${st.index}.click();"
                                                                       value="${saleAuth.name}">
                                                                <input type="hidden" id="uri_8_${st.index}" name="uri_8"
                                                                       value="${saleAuth.uri}">
                                                            </c:otherwise>
                                                        </c:choose>
                                                        <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                               id="twoUpload"
                                                               onclick="return $('#file_8_${st.index}').click();">浏览</label>
                                                    </div>

                                                    <c:choose>
                                                        <c:when test="${saleAuth.uri ne null && saleAuth.uri ne ''}">
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7" id="img_icon_8"></i>
                                                                <a href="http://${saleAuth.domain}${saleAuth.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4"
                                                                   id="img_view_8">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0 }">
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="del(8)" id="img_del_8">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_8">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7 none" id="img_icon_8"></i>
                                                                <a href="http://${saleAuth.domain}${saleAuth.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                                   id="img_view_8">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0 }">
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="del(8)" id="img_del_8">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_8">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <div class="clear"></div>
                                                </div>
                                            </c:forEach>
                                        </c:when>
                                        <c:otherwise>
                                            <div class="mb8">
                                                <div class="f_left">
                                                    <input type="file" class="upload_file" name="lwfile" id="file_8"
                                                           style="display: none;" onchange="uploadFile(this,8);"/>
                                                    <input type="text" class="input-middle" id="name_8" readonly="readonly"
                                                           placeholder="请上传销售人员授权书" name="saleAuthBookName"
                                                           onclick="file_8.click();" value="${saleAuth.name}">
                                                    <input type="hidden" id="uri_8" name="saleAuthBookUri" value="${saleAuth.uri}">
                                                    <div id="sale_auto_book" class="font-red " style="display: none;">请上传销售人员授权书
                                                    </div>
                                                </div>
                                                <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                       onclick="return $('#file_8').click();">浏览</label>
                                                <!-- 上传成功出现 -->
                                                <c:choose>
                                                    <c:when test="${!empty saleAuth.uri}">
                                                        <i class="iconsuccesss ml7" id="img_icon_8"></i>
                                                        <a href="http://${saleAuth.domain}${saleAuth.uri}" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4" id="img_view_8">查看</a>
                                                        <span class="font-red cursor-pointer mt4" onclick="del(8)"
                                                              id="img_del_8">删除</span>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <i class="iconsuccesss ml7 none" id="img_icon_8"></i>
                                                        <a href="" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4 none" id="img_view_8">查看</a>
                                                        <span class="font-red cursor-pointer mt4 none" onclick="del(8)"
                                                              id="img_del_8">删除</span>
                                                    </c:otherwise>
                                                </c:choose>
                                            </div>
                                        </c:otherwise>
                                    </c:choose>
                                    <div class="clear" id="conadd8">
                                        <span class="bt-border-style bt-small border-blue "
                                              onclick="con_add(8);">继续添加</span>
                                    </div>

                                    <div class="font-grey9" style="margin-top:7px;">
                                        1，图片格式只能JPG、PNG、JPEG、BMP、PDF等格式<br>
                                        2，图片大小不超过5M
                                    </div>
                                </li>
                                <li>
                                    <label class="f_left mt4 mr10">设置有效期</label>
                                    <div class="f_left">
                                        <div class='inputfloat'>
                                            <input class="Wdate input-smaller" type="text" placeholder="请选择日期"
                                                   id="saleAuthBookStartTime"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'saleAuthBookEndTime\')}'})" autocomplete="off"
                                                   name="saleAuthBookStartTime"
                                                   value='<date:date value ="${saleAuth.begintime} " format="yyyy-MM-dd"/>'/>
                                            <input class="Wdate input-smaller" type="text" placeholder="请选择日期"
                                                   id="saleAuthBookEndTime"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'saleAuthBookStartTime\')}'})" autocomplete="off"
                                                   name="saleAuthBookEndTime"
                                                   value='<date:date value ="${saleAuth.endtime} " format="yyyy-MM-dd"/>'/>
                                        </div>
                                        <div id="sale_time_div" class="font-red" style="display: none;">开始时间不能为空</div>
                                    </div>

                                </li>
                            </ul>
                        </div>
                    </li>
                    <li class="" style="margin-bottom:14px; display: none">
                        <div class="infor_name sex_name" style="width: 155px;">
                            <label>授权销售人信息</label>
                        </div>
                        <div class="f_left insertli">
                            <ul id="saleAuth_2">
                                <li class="specialli" style="margin-bottom:0px;">
                                    <label class="f_left mt4 mr10">职位</label>
                                    <div class="f_left ">
                                        <input type="text" name="authPost" class="input-middle"
                                               value="${saleAuth.authPost}" maxlength="100"/>
                                        <div class="font-red" style="display: none;"></div>
                                    </div>
                                    <div class="clear"></div>
                                </li>
                                <li class="specialli" style="margin-bottom:0px;">
                                    <label class="f_left mt4 mr10">姓名</label>
                                    <div class="f_left ">
                                        <input type="text" name="authUserName" class="input-middle"
                                               value="${saleAuth.authUserName}" maxlength="100"/>
                                        <div class="font-red" style="display: none;"></div>
                                    </div>
                                    <div class="clear"></div>
                                </li>
                                <li class="specialli" style="margin-bottom:0px;">
                                    <label class="f_left mt4 mr10">联系方式</label>
                                    <div class="f_left ">
                                        <input type="text" name="authContactInfo" class="input-middle"
                                               value="${saleAuth.authContactInfo}" maxlength="200"/>
                                        <div class="font-red" style="display: none;"></div>
                                    </div>
                                    <div class="clear"></div>
                                </li>
                            </ul>
                        </div>
                    </li>
                    <li class="" style="display: none">
                        <div class="infor_name sex_name" style="width: 155px;">
                            <label>品牌授权书</label>
                        </div>
                        <div class="f_left insertli">
                            <ul id="brandBook">
                                <li>
                                    <c:choose>
                                        <c:when test="${!empty brandBookList}">
                                            <c:forEach items="${brandBookList }" var="brandBook" varStatus="st">
                                                <c:if test="${st.index == 0}">
                                                    <c:set var="brankBeginTime" value="${brandBook.begintime}"></c:set>
                                                    <c:set var="brankEndTime" value="${brandBook.endtime}"></c:set>
                                                </c:if>
                                                <div class="mb8">
                                                    <div class="pos_rel f_left ">

                                                        <input type="file" class="upload_file" name="lwfile"
                                                               id="file_9_${st.index}" style="display: none;"
                                                               onchange="uploadFile(this,9);"/>
                                                        <c:choose>
                                                            <c:when test="${st.index == 0}">
                                                                <input type="text" class="input-middle" id="name_9"
                                                                       readonly="readonly"
                                                                       placeholder="请上传品牌授权书" name="brandBookName"
                                                                       onclick="file_9_${st.index}.click();"
                                                                       value="${brandBook.name}">
                                                                <input type="hidden" id="uri_9_${st.index}"
                                                                       name="brandBookUri" value="${brandBook.uri}">
                                                            </c:when>
                                                            <c:otherwise>
                                                                <input type="text" class="input-middle"
                                                                       id="name_9_${st.index}" readonly="readonly"
                                                                       placeholder="请上传品牌授权书" name="name_9"
                                                                       onclick="file_9_${st.index}.click();"
                                                                       value="${brandBook.name}">
                                                                <input type="hidden" id="uri_9_${st.index}" name="uri_9"
                                                                       value="${brandBook.uri}">
                                                            </c:otherwise>
                                                        </c:choose>
                                                        <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                               onclick="return $('#file_9_${st.index}').click();"
                                                               style="margin:0 12px 0 10px">浏览</label>

                                                        <div><span id="brand_book" class="font-red "
                                                                   style="display: none;">请上传品牌授权书</span></div>
                                                    </div>

                                                    <c:choose>
                                                        <c:when test="${brandBook.uri ne null && brandBook.uri ne ''}">
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7" id="img_icon_9"></i>
                                                                <a href="http://${brandBook.domain}${brandBook.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4"
                                                                   id="img_view_9">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0}">
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="del(9)" id="img_del_9">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_9">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7 none" id="img_icon_9"></i>
                                                                <a href="http://${brandBook.domain}${brandBook.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                                   id="img_view_9">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0}">
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="del(9)" id="img_del_9">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_9">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <div class="clear"></div>
                                                </div>
                                            </c:forEach>
                                        </c:when>

                                        <c:otherwise>
                                            <div class=" mb8">
                                                <div class="f_left">

                                                    <input type="file" class="upload_file" name="lwfile" id="file_9"
                                                           style="display: none;" onchange="uploadFile(this,9);"/>
                                                    <input type="text" class="input-middle" id="name_9"
                                                           readonly="readonly"
                                                           placeholder="请上传品牌授权书" name="brandBookName"
                                                           onclick="file_9.click();" value="${brandBook.name}">
                                                    <input type="hidden" id="uri_9" name="brandBookUri"
                                                           value="${brandBook.uri}">
                                                    <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                           onclick="return $('#file_9').click();">浏览</label>

                                                    <div><span id="brand_book" class="font-red " style="display: none;">请上传品牌授权书</span>
                                                    </div>

                                                </div>
                                                <!-- 上传成功出现 -->
                                                <div class="f_left">
                                                    <i class="iconsuccesss ml7 none" id="img_icon_9"></i>
                                                    <a href="" target="_blank"
                                                       class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                       id="img_view_9">查看</a>
                                                    <span class="font-red cursor-pointer mt4 none" onclick="del(9)"
                                                          id="img_del_9">删除</span>
                                                </div>

                                                <div class="clear"></div>
                                            </div>
                                        </c:otherwise>
                                    </c:choose>

                                    <div class=" clear" id="conadd9">
                                        <span class="bt-border-style bt-small border-blue "
                                              onclick="con_add(9);">继续添加</span>
                                    </div>

                                    <div class="font-grey9" style="margin-top:7px;">
                                        1，图片格式只能JPG、PNG、JPEG、BMP、PDF等格式<br>
                                        2，图片大小不超过5M
                                    </div>
                                </li>
                                <li>
                                    <label class="f_left mt4 mr10">设置有效期</label>
                                    <div class="f_left">
                                        <div class='inputfloat'>
                                            <input class="Wdate input-smaller" type="text" placeholder="请选择日期"
                                                   id="brandBookStartTime"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'brandBookEndTime\')}'})" autocomplete="off"
                                                   name="brandBookStartTime"
                                                   value='<date:date value ="${brankBeginTime} " format="yyyy-MM-dd"/>'/>

                                            <input class="Wdate input-smaller" type="text" placeholder="请选择日期"
                                                   id="brandBookEndTime"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'brandBookStartTime\')}'})" autocomplete="off"
                                                   name="brandBookEndTime"
                                                   value='<date:date value ="${brankEndTime} " format="yyyy-MM-dd"/>'/>
                                        </div>
                                        <div id="brandBook_time_div" class="font-red" style="display: none;">开始时间不能为空
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </li>

                    <li class="">
                        <div class="infor_name sex_name" style="width: 155px;">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="18px" height="18px">
                            <label>随货同行单模板</label>
                        </div>
                        <div class="f_left insertli">
                            <ul id="goodsWithTem">
                                <li>
                                    <c:choose>
                                        <c:when test="${!empty goodWithTemList}">
                                            <c:forEach items="${goodWithTemList}" var="goodWithTem" varStatus="st">
                                                <div class="clear mb8">
                                                    <div class="pos_rel f_left mb8">
                                                        <input type="file" class="upload_file" name="lwfile"
                                                               id="file_103_${st.index}" style="display: none;"
                                                               onchange="uploadFile(this,103);"/>
                                                        <c:choose>
                                                            <c:when test="${st.index == 0}">
                                                                <input type="text" class="input-middle" id="name_103"
                                                                       readonly="readonly"
                                                                       placeholder="请上传随货同行单模板" name="goodsWithTemName"
                                                                       onclick="file_103_${st.index}.click();"
                                                                       value="${goodWithTem.name}">
                                                                <input type="hidden" id="uri_103_${st.index}"
                                                                       name="goodsWithTemUri" value="${goodWithTem.uri}">
                                                            </c:when>
                                                            <c:otherwise>
                                                                <input type="text" class="input-middle"
                                                                       id="name_103_${st.index}" readonly="readonly"
                                                                       placeholder="请上传随货同行单模板" name="name_103"
                                                                       onclick="file_103_${st.index}.click();"
                                                                       value="${goodWithTem.name}">
                                                                <input type="hidden" id="uri_103_${st.index}"
                                                                       name="uri_103" value="${goodWithTem.uri}">
                                                            </c:otherwise>
                                                        </c:choose>
                                                        <label class="bt-bg-style bt-middle bg-light-blue ml103"
                                                               onclick="return $('#file_103_${st.index}').click();">浏览</label>
                                                    </div>
                                                    <c:choose>
                                                        <c:when test="${goodWithTem.uri ne null && goodWithTem.uri ne ''}">
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7" id="img_icon_103"></i>
                                                                <a href="http://${goodWithTem.domain}${goodWithTem.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4"
                                                                   id="img_view_103">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0}">
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="del(103)"
                                                                              id="img_del_103">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_103">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7 none" id="img_icon_103"></i>
                                                                <a href="http://${goodWithTem.domain}${goodWithTem.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                                   id="img_view_103">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0}">
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="del(103)"
                                                                              id="img_del_103">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_103">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </div>
                                            </c:forEach>
                                        </c:when>

                                        <c:otherwise>
                                            <div class="mb8">
                                                <div class="f_left">
                                                    <input type="file" class="upload_file" name="lwfile" id="file_103"
                                                           style="display: none;" onchange="uploadFile(this,103);"/>
                                                    <input type="text" class="input-middle" id="name_103"
                                                           readonly="readonly"
                                                           placeholder="请上传随货同行单模板" name="goodsWithTemName"
                                                           onclick="file_103.click();" value="${goodWithTem.name}">
                                                    <input type="hidden" id="uri_103" name="goodsWithTemUri"
                                                           value="${goodWithTem.uri}">
                                                    <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                           onclick="return $('#file_103').click();">浏览</label>
                                                </div>
                                                <!-- 上传成功出现 -->
                                                <c:choose>
                                                    <c:when test="${!empty goodWithTem.uri}">
                                                        <i class="iconsuccesss ml7" id="img_icon_103"></i>
                                                        <a href="http://${goodWithTem.domain}${goodWithTem.uri}" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4"
                                                           id="img_view_103">查看</a>
                                                        <span class="font-red cursor-pointer mt4" onclick="del(3)"
                                                              id="img_del_103">删除</span>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <i class="iconsuccesss ml7 none" id="img_icon_103"></i>
                                                        <a href="" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                           id="img_view_103">查看</a>
                                                        <span class="font-red cursor-pointer mt4 none" onclick="del(103)"
                                                              id="img_del_103">删除</span>
                                                    </c:otherwise>
                                                </c:choose>
                                                <div class="clear"></div>
                                            </div>
                                        </c:otherwise>
                                    </c:choose>

                                    <div class="mt8 clear" id="conadd103">
                                        <span class="bt-border-style bt-small border-blue "
                                              onclick="con_add(103);">继续添加</span>
                                    </div>

                                    <div class="font-grey9" style="margin-top:7px;">
                                        1，图片格式只能JPG、PNG、JPEG、BMP、PDF等格式<br>
                                        2，图片大小不超过5M
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </li>

                    <li class="">
                        <div class="infor_name sex_name" style="width: 155px;">
                            <%--<img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="18px" height="18px">--%>
                            <label>质量保证协议</label>
                        </div>
                        <div class="f_left insertli">
                            <ul id="qualityAssurance">
                                <li>
                                    <c:choose>
                                        <c:when test="${!empty qualityAssuranceList}">
                                            <c:forEach items="${qualityAssuranceList }" var="qualityAssurance" varStatus="st">
                                                <div class="clear mb8">
                                                    <div class="pos_rel f_left mb8">
                                                        <input type="file" class="upload_file" name="lwfile"
                                                               id="file_104_${st.index}" style="display: none;"
                                                               onchange="uploadFile(this,104);"/>
                                                        <c:choose>
                                                            <c:when test="${st.index == 0}">
                                                                <input type="text" class="input-middle" id="name_104"
                                                                       readonly="readonly"
                                                                       placeholder="请上传质量保证协议" name="qualityAssuranceName"
                                                                       onclick="file_104_${st.index}.click();"
                                                                       value="${qualityAssurance.name}">
                                                                <input type="hidden" id="uri_104_${st.index}"
                                                                       name="qualityAssuranceUri" value="${qualityAssurance.uri}">
                                                            </c:when>
                                                            <c:otherwise>
                                                                <input type="text" class="input-middle"
                                                                       id="name_104_${st.index}" readonly="readonly"
                                                                       placeholder="请上传质量保证协议" name="name_104"
                                                                       onclick="file_104_${st.index}.click();"
                                                                       value="${qualityAssurance.name}">
                                                                <input type="hidden" id="uri_104_${st.index}"
                                                                       name="uri_104" value="${qualityAssurance.uri}">
                                                            </c:otherwise>
                                                        </c:choose>
                                                        <label class="bt-bg-style bt-middle bg-light-blue ml104"
                                                               onclick="return $('#file_104_${st.index}').click();">浏览</label>
                                                    </div>
                                                    <c:choose>
                                                        <c:when test="${qualityAssurance.uri ne null && qualityAssurance.uri ne ''}">
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7" id="img_icon_104"></i>
                                                                <a href="http://${qualityAssurance.domain}${qualityAssurance.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4"
                                                                   id="img_view_104">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0}">
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="del(104)"
                                                                              id="img_del_104">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_104">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7 none" id="img_icon_104"></i>
                                                                <a href="http://${qualityAssurance.domain}${qualityAssurance.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                                   id="img_view_104">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0}">
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="del(104)"
                                                                              id="img_del_104">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_104">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </div>
                                            </c:forEach>
                                        </c:when>

                                        <c:otherwise>
                                            <div class="mb8">
                                                <div class="f_left">
                                                    <input type="file" class="upload_file" name="lwfile" id="file_104"
                                                           style="display: none;" onchange="uploadFile(this,104);"/>
                                                    <input type="text" class="input-middle" id="name_104"
                                                           readonly="readonly"
                                                           placeholder="请上传质量保证协议" name="qualityAssuranceName"
                                                           onclick="file_104.click();" value="${qualityAssurance.name}">
                                                    <input type="hidden" id="uri_104" name="qualityAssuranceUri"
                                                           value="${qualityAssurance.uri}">
                                                    <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                           onclick="return $('#file_104').click();">浏览</label>
                                                </div>
                                                <!-- 上传成功出现 -->
                                                <c:choose>
                                                    <c:when test="${!empty qualityAssurance.uri}">
                                                        <i class="iconsuccesss ml7" id="img_icon_104"></i>
                                                        <a href="http://${qualityAssurance.domain}${qualityAssurance.uri}" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4"
                                                           id="img_view_104">查看</a>
                                                        <span class="font-red cursor-pointer mt4" onclick="del(3)"
                                                              id="img_del_104">删除</span>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <i class="iconsuccesss ml7 none" id="img_icon_104"></i>
                                                        <a href="" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                           id="img_view_104">查看</a>
                                                        <span class="font-red cursor-pointer mt4 none" onclick="del(104)"
                                                              id="img_del_104">删除</span>
                                                    </c:otherwise>
                                                </c:choose>
                                                <div class="clear"></div>
                                            </div>
                                        </c:otherwise>
                                    </c:choose>

                                    <div class="mt8 clear" id="conadd104">
                                        <span class="bt-border-style bt-small border-blue "
                                              onclick="con_add(104);">继续添加</span>
                                    </div>

                                    <div class="font-grey9" style="margin-top:7px;">
                                        1，图片格式只能JPG、PNG、JPEG、BMP、PDF等格式<br>
                                        2，图片大小不超过5M
                                    </div>
                                </li>
                                <li style="margin-bottom:0px;">
                                    <label class="f_left mt4 mr10">设置有效期</label>
                                    <div class="f_left">
                                        <div class='inputfloat'>
                                            <input type="text" class="Wdate input-smaller" placeholder="请选择日期"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'qualityAssuranceEndTime\')}'})" autocomplete="off"
                                                   name="qualityAssuranceStartTime"
                                                   id="qualityAssuranceStartTime"
                                                   value='<date:date value ="${qualityAssurance.begintime} " format="yyyy-MM-dd"/>'/>
                                            <input type="text" class="Wdate input-smaller" placeholder="请选择日期"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'qualityAssuranceStartTime\')}'})" autocomplete="off"
                                                   name="qualityAssuranceEndTime"
                                                   id="qualityAssuranceEndTime"
                                                   value='<date:date value ="${qualityAssurance.endtime} " format="yyyy-MM-dd"/>'/>
                                        </div>
                                        <div id="qualityAssuranc_time_div" class="font-red " style="display: none;">开始时间不能为空</div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </li>

                    <li class="">
                        <div class="infor_name sex_name" style="width: 155px;">
                            <%--<img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="18px" height="18px">--%>
                            <label>售后服务承诺书</label>
                        </div>
                        <div class="f_left insertli">
                            <ul id="afterSalesBook">
                                <li>
                                    <c:choose>
                                        <c:when test="${!empty afterSalesBookList}">
                                            <c:forEach items="${afterSalesBookList}" var="afterSalesBook" varStatus="st">
                                                <div class="clear mb8">
                                                    <div class="pos_rel f_left mb8">
                                                        <input type="file" class="upload_file" name="lwfile"
                                                               id="file_105_${st.index}" style="display: none;"
                                                               onchange="uploadFile(this,105);"/>
                                                        <c:choose>
                                                            <c:when test="${st.index == 0}">
                                                                <input type="text" class="input-middle" id="name_105"
                                                                       readonly="readonly"
                                                                       placeholder="请上传售后服务承诺书" name="afterSalesBookName"
                                                                       onclick="file_105_${st.index}.click();"
                                                                       value="${afterSalesBook.name}">
                                                                <input type="hidden" id="uri_105_${st.index}"
                                                                       name="afterSalesBookUri" value="${afterSalesBook.uri}">
                                                            </c:when>
                                                            <c:otherwise>
                                                                <input type="text" class="input-middle"
                                                                       id="name_105_${st.index}" readonly="readonly"
                                                                       placeholder="请上传售后服务承诺书" name="name_105"
                                                                       onclick="file_105_${st.index}.click();"
                                                                       value="${afterSalesBook.name}">
                                                                <input type="hidden" id="uri_105_${st.index}"
                                                                       name="uri_105" value="${afterSalesBook.uri}">
                                                            </c:otherwise>
                                                        </c:choose>
                                                        <label class="bt-bg-style bt-middle bg-light-blue ml105"
                                                               onclick="return $('#file_105_${st.index}').click();">浏览</label>
                                                    </div>
                                                    <c:choose>
                                                        <c:when test="${afterSalesBook.uri ne null && afterSalesBook.uri ne ''}">
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7" id="img_icon_105"></i>
                                                                <a href="http://${afterSalesBook.domain}${afterSalesBook.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4"
                                                                   id="img_view_105">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0}">
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="del(105)"
                                                                              id="img_del_105">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_105">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7 none" id="img_icon_105"></i>
                                                                <a href="http://${afterSalesBook.domain}${afterSalesBook.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                                   id="img_view_105">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0}">
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="del(105)"
                                                                              id="img_del_105">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_105">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </div>
                                            </c:forEach>
                                        </c:when>

                                        <c:otherwise>
                                            <div class="mb8">
                                                <div class="f_left">
                                                    <input type="file" class="upload_file" name="lwfile" id="file_105"
                                                           style="display: none;" onchange="uploadFile(this,105);"/>
                                                    <input type="text" class="input-middle" id="name_105"
                                                           readonly="readonly"
                                                           placeholder="请上传售后服务承诺书" name="afterSalesBookName"
                                                           onclick="file_105.click();" value="${afterSalesBook.name}">
                                                    <input type="hidden" id="uri_105" name="afterSalesBookUri"
                                                           value="${afterSalesBook.uri}">
                                                    <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                           onclick="return $('#file_105').click();">浏览</label>
                                                </div>
                                                <!-- 上传成功出现 -->
                                                <c:choose>
                                                    <c:when test="${!empty afterSalesBook.uri}">
                                                        <i class="iconsuccesss ml7" id="img_icon_105"></i>
                                                        <a href="http://${afterSalesBook.domain}${afterSalesBook.uri}" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4"
                                                           id="img_view_105">查看</a>
                                                        <span class="font-red cursor-pointer mt4" onclick="del(3)"
                                                              id="img_del_105">删除</span>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <i class="iconsuccesss ml7 none" id="img_icon_105"></i>
                                                        <a href="" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                           id="img_view_105">查看</a>
                                                        <span class="font-red cursor-pointer mt4 none" onclick="del(105)"
                                                              id="img_del_105">删除</span>
                                                    </c:otherwise>
                                                </c:choose>
                                                <div class="clear"></div>
                                            </div>
                                        </c:otherwise>
                                    </c:choose>

                                    <div class="mt8 clear" id="conadd105">
                                        <span class="bt-border-style bt-small border-blue "
                                              onclick="con_add(105);">继续添加</span>
                                    </div>

                                    <div class="font-grey9" style="margin-top:7px;">
                                        1，图片格式只能JPG、PNG、JPEG、BMP、PDF等格式<br>
                                        2，图片大小不超过5M
                                    </div>
                                </li>

                                <li style="margin-bottom:0px;">
                                    <label class="f_left mt4 mr10">设置有效期</label>
                                    <div class="f_left">
                                        <div class='inputfloat'>
                                            <input type="text" class="Wdate input-smaller" placeholder="请选择日期"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'afterSalesBookEndTime\')}'})" autocomplete="off"
                                                   name="afterSalesBookStartTime"
                                                   id="afterSalesBookStartTime"
                                                   value='<date:date value ="${afterSalesBook.begintime} " format="yyyy-MM-dd"/>'/>
                                            <input type="text" class="Wdate input-smaller" placeholder="请选择日期"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'afterSalesBookStartTime\')}'})" autocomplete="off"
                                                   name="afterSalesBookEndTime"
                                                   id="afterSalesBookEndTime"
                                                   value='<date:date value ="${afterSalesBook.endtime} " format="yyyy-MM-dd"/>'/>
                                        </div>
                                        <div id="afterSalesBook_time_div" class="font-red " style="display: none;">开始时间不能为空</div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </li>

                    <li class="">
                        <div class="infor_name sex_name" style="width: 155px;">
                            <label>质量体系调查表或</br>合格供应商档案</label>
                        </div>
                        <div class="f_left insertli">
                            <ul id="qualityAndTrader">
                                <li>
                                    <c:choose>
                                        <c:when test="${!empty qualityAndTraderList}">
                                            <c:forEach items="${qualityAndTraderList}" var="qualityAndTrader" varStatus="st">
                                                <div class="clear mb8">
                                                    <div class="pos_rel f_left mb8">
                                                        <input type="file" class="upload_file" name="lwfile"
                                                               id="file_106_${st.index}" style="display: none;"
                                                               onchange="uploadFile(this,106);"/>
                                                        <c:choose>
                                                            <c:when test="${st.index == 0}">
                                                                <input type="text" class="input-middle" id="name_106"
                                                                       readonly="readonly"
                                                                       placeholder="请上传质量体系调查表或合格供应商档案" name="qualityAndTraderName"
                                                                       onclick="file_106_${st.index}.click();"
                                                                       value="${qualityAndTrader.name}">
                                                                <input type="hidden" id="uri_106_${st.index}"
                                                                       name="qualityAndTraderUri" value="${qualityAndTrader.uri}">
                                                            </c:when>
                                                            <c:otherwise>
                                                                <input type="text" class="input-middle"
                                                                       id="name_106_${st.index}" readonly="readonly"
                                                                       placeholder="请上传质量体系调查表或合格供应商档案" name="name_106"
                                                                       onclick="file_106_${st.index}.click();"
                                                                       value="${qualityAndTrader.name}">
                                                                <input type="hidden" id="uri_106_${st.index}"
                                                                       name="uri_106" value="${qualityAndTrader.uri}">
                                                            </c:otherwise>
                                                        </c:choose>
                                                        <label class="bt-bg-style bt-middle bg-light-blue ml106"
                                                               onclick="return $('#file_106_${st.index}').click();">浏览</label>
                                                    </div>
                                                    <c:choose>
                                                        <c:when test="${qualityAndTrader.uri ne null && qualityAndTrader.uri ne ''}">
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7" id="img_icon_106"></i>
                                                                <a href="http://${qualityAndTrader.domain}${qualityAndTrader.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4"
                                                                   id="img_view_106">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0}">
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="del(106)"
                                                                              id="img_del_106">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_106">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7 none" id="img_icon_106"></i>
                                                                <a href="http://${qualityAndTrader.domain}${qualityAndTrader.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                                   id="img_view_106">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0}">
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="del(106)"
                                                                              id="img_del_106">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_106">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </div>
                                            </c:forEach>
                                        </c:when>

                                        <c:otherwise>
                                            <div class="mb8">
                                                <div class="f_left">
                                                    <input type="file" class="upload_file" name="lwfile" id="file_106"
                                                           style="display: none;" onchange="uploadFile(this,106);"/>
                                                    <input type="text" class="input-middle" id="name_106"
                                                           readonly="readonly"
                                                           placeholder="请上传质量体系调查表或合格供应商档案" name="qualityAndTraderName"
                                                           onclick="file_106.click();" value="${qualityAndTrader.name}">
                                                    <input type="hidden" id="uri_106" name="qualityAndTraderUri"
                                                           value="${qualityAndTrader.uri}">
                                                    <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                           onclick="return $('#file_106').click();">浏览</label>
                                                </div>
                                                <!-- 上传成功出现 -->
                                                <c:choose>
                                                    <c:when test="${!empty qualityAndTrader.uri}">
                                                        <i class="iconsuccesss ml7" id="img_icon_106"></i>
                                                        <a href="http://${qualityAndTrader.domain}${qualityAndTrader.uri}" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4"
                                                           id="img_view_106">查看</a>
                                                        <span class="font-red cursor-pointer mt4" onclick="del(106)"
                                                              id="img_del_106">删除</span>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <i class="iconsuccesss ml7 none" id="img_icon_106"></i>
                                                        <a href="" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                           id="img_view_106">查看</a>
                                                        <span class="font-red cursor-pointer mt4 none" onclick="del(106)"
                                                              id="img_del_106">删除</span>
                                                    </c:otherwise>
                                                </c:choose>
                                                <div class="clear"></div>
                                            </div>
                                        </c:otherwise>
                                    </c:choose>

                                    <div class="mt8 clear" id="conadd106">
                                        <span class="bt-border-style bt-small border-blue "
                                              onclick="con_add(106);">继续添加</span>
                                    </div>

                                    <div class="font-grey9" style="margin-top:7px;">
                                        1，图片格式只能JPG、PNG、JPEG、BMP、PDF等格式<br>
                                        2，图片大小不超过5M
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </li>

                    <li class="">
                        <div class="infor_name sex_name" style="width: 155px;">
                            <label>其他</label>
                        </div>
                        <div class="f_left insertli">
                            <ul id="theOther">
                                <li>
                                    <c:choose>
                                        <c:when test="${!empty otherList}">
                                            <c:forEach items="${otherList }" var="other" varStatus="st">
                                                <div class="clear mb8">
                                                    <div class="pos_rel f_left mb8">
                                                        <input type="file" class="upload_file" name="lwfile"
                                                               id="file_10_${st.index}" style="display: none;"
                                                               onchange="uploadFile(this,10);"/>
                                                        <c:choose>
                                                            <c:when test="${st.index == 0}">
                                                                <input type="text" class="input-middle" id="name_10"
                                                                       readonly="readonly"
                                                                       placeholder="请上传其他资质图片" name="otherName"
                                                                       onclick="file_10_${st.index}.click();"
                                                                       value="${other.name}">
                                                                <input type="hidden" id="uri_10_${st.index}"
                                                                       name="otherUri" value="${other.uri}">
                                                            </c:when>
                                                            <c:otherwise>
                                                                <input type="text" class="input-middle"
                                                                       id="name_10_${st.index}" readonly="readonly"
                                                                       placeholder="请上传其他资质图片" name="name_10"
                                                                       onclick="file_10_${st.index}.click();"
                                                                       value="${other.name}">
                                                                <input type="hidden" id="uri_10_${st.index}"
                                                                       name="uri_10" value="${other.uri}">
                                                            </c:otherwise>
                                                        </c:choose>
                                                        <!-- 														 <div id="brand_book" class="font-red " style="display: none;">请上传品牌授权书</div> -->
                                                        <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                               onclick="return $('#file_10_${st.index}').click();">浏览</label>
                                                    </div>
                                                    <c:choose>
                                                        <c:when test="${other.uri ne null && other.uri ne ''}">
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7" id="img_icon_10"></i>
                                                                <a href="http://${other.domain}${other.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4"
                                                                   id="img_view_10">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0}">
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="del(10)"
                                                                              id="img_del_10">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_10">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7 none" id="img_icon_10"></i>
                                                                <a href="http://${other.domain}${other.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                                   id="img_view_10">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0}">
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="del(10)"
                                                                              id="img_del_10">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_10">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </div>
                                            </c:forEach>
                                        </c:when>

                                        <c:otherwise>
                                            <div class="mb8">
                                                <div class="f_left">
                                                    <input type="file" class="upload_file" name="lwfile" id="file_10"
                                                           style="display: none;" onchange="uploadFile(this,10);"/>
                                                    <input type="text" class="input-middle" id="name_10"
                                                           readonly="readonly"
                                                           placeholder="请上传其他资质图片" name="otherName"
                                                           onclick="file_10.click();" value="${other.name}">
                                                    <input type="hidden" id="uri_10" name="otherUri"
                                                           value="${other.uri}">
                                                    <!-- 					                                    <div id="brand_book" class="font-red " style="display: none;">请上传品牌授权书</div> -->
                                                    <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                           onclick="return $('#file_10').click();">浏览</label>
                                                </div>
                                                <!-- 上传成功出现 -->
                                                <c:choose>
                                                    <c:when test="${!empty other.uri}">
                                                        <i class="iconsuccesss ml7" id="img_icon_10"></i>
                                                        <a href="http://${other.domain}${other.uri}" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4"
                                                           id="img_view_10">查看</a>
                                                        <span class="font-red cursor-pointer mt4" onclick="del(10)"
                                                              id="img_del_10">删除</span>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <i class="iconsuccesss ml7 none" id="img_icon_10"></i>
                                                        <a href="" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                           id="img_view_10">查看</a>
                                                        <span class="font-red cursor-pointer mt4 none" onclick="del(10)"
                                                              id="img_del_10">删除</span>
                                                    </c:otherwise>
                                                </c:choose>
                                                <div class="clear"></div>
                                            </div>
                                        </c:otherwise>
                                    </c:choose>

                                    <div class="mt8 clear" id="conadd10">
                                        <span class="bt-border-style bt-small border-blue "
                                              onclick="con_add(10);">继续添加</span>
                                    </div>

                                    <div class="font-grey9" style="margin-top:7px;">
                                        1，图片格式只能JPG、PNG、JPEG、BMP、PDF等格式<br>
                                        2，图片大小不超过5M
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </li>

                    <!-- end by franlin for[3865 供应商资质中，增加销售人授权书，销售人信息]  at 2018-06-21 -->
                </ul>
                <div class="font-grey9 ml120 line20">
                    友情提醒
                    <br/>1、结束日期可以不填写，代表资质没有结束日期；
                    <br/>2、三证合一和医疗资质合一的客户无需再设置其他信息；
                </div>

                <input type="hidden" name="busTraderCertificateId" value="${business.traderCertificateId}">
                <input type="hidden" name="taxTraderCertificateId" value="${tax.traderCertificateId}">
                <input type="hidden" name="orgaTraderCertificateId" value="${orga.traderCertificateId}">
                <input type="hidden" name="twoTraderCertificateId" value="${twoMedical.traderCertificateId}">
                <input type="hidden" name="threeTraderCertificateId" value="${threeMedical.traderCertificateId}">
                <input type="hidden" name="productTraderCertificateId" value="${product.traderCertificateId}">
                <input type="hidden" name="traderId" value="${traderSupplier1.traderId}">
                <input type="hidden" name="traderSupplierId" value="${traderSupplier1.traderSupplierId}">
                <input type="hidden" name="traderType" value="2">
                <input type="hidden" name="beforeParams" value='${beforeParams}'>
            </div>
        </div>

    </div>
    <iframe class="j-iframe" src="/trader/supplier/getContactsAddress.do?traderId=${traderSupplier1.traderId}&traderSupplierId=${traderSupplier1.traderSupplierId}" frameborder="0" style="width: 100%;border: 0; " onload="setIframeHeight(this)" scrolling="no">
    </iframe>

    <div class="add-tijiao tcenter mt20">
        <button type="submit" id='submit'>保存</button>
    </div>
</form>
<div style="width: 100%;height:80px"></div>
<script type="text/javascript">
    function checkContactWay(contactWay,errorMsg){

        if(contactWay.value == ""){
            return;
        }

        var re = /^(\d{3,4}-?)?\d{7,9}(-?\d{2,6})?$|^$/;
        if(!re.test(contactWay.value)){
            layer.alert(errorMsg);
        }

    }
</script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/supplier/edit_baseinfo.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/region/index.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp" %>
