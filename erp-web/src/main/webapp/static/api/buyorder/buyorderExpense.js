


function add(data) {
    return axios({
        url: '/buyorderExpense/add.do',
        method: 'post',
        data: data
    })
}


function getBuyorderExpenseEditInfo(data) {
    return axios({
        url: '/buyorderExpense/getBuyorderExpenseEditInfo.do',
        method: 'post',
        params: data
    })
}

function doPayApplyComplement(data) {
    return axios({
        url: '/old/buyorderExpense/doPayApplyComplement.do',
        method: 'post',
        params: data
    })
}

function byPreBuyorderGetDetail(data) {
    return axios({
        url: '/buyorderExpense/byPreBuyorderGetDetail.do',
        method: 'post',
        data: data
    })
}

function update(data) {
    return axios({
        url: '/buyorderExpense/update.do',
        method: 'post',
        data: data
    })
}

function getDetail(data) {
    return axios({
        url: '/buyorderExpense/getDetail.do',
        method: 'post',
        params:{buyorderExpenseId:data}
    })
}

function auditRecordData(data) {
    return axios({
        url: '/old/buyorderExpense/getBuyorderExpenseCheckStatus.do',
        method: 'post',
        params:{buyorderExpenseId:data}
    })
}
function checkUser(data) {
    return axios({
        url: '/buyorderExpense/checkUser.do',
        method: 'post',
        params:{buyorderExpenseId:data}
    })
}

function getButton(data) {
    return axios({
        url: '/old/buyorderExpense/getButton.do',
        method: 'post',
        params:{buyorderExpenseId:data}
    })
}

function getSteps(data) {
    return axios({
        url: '/buyorderExpense/getOrderStepsNode.do',
        method: 'post',
        params:{buyorderExpenseId:data}
    })
}

function closeBuyorderExpense(data) {
    return axios({
        url: '/buyorderExpense/closeBuyOrderExpense.do',
        method: 'post',
        params:{buyorderExpenseId:data}
    })
}

function paymentAuditRecordData(data) {
    return axios({
        url: '/old/buyorderExpense/getBuyorderExpensePaymentCheckStatus.do',
        method: 'post',
        params:{payApplyId:data}
    })
}

function payApplyData(data) {
    return axios({
        url: '/payApply/api/payApplyData.do',
        method: 'post',
        params:{payType:4125,relatedId:data,companyId:1}
    })
}

function capitalBillData(data) {
    return axios({
        url: '/capitalBill/api/capitalBillData.do',
        method: 'post',
        params:{orderType:4,relatedId:data,operationType:"finance_buy_expense_detail"}
    })
}

function invoiceData(data) {
    return axios({
        url: '/invoice/api/getInvoiceListByRelatedId.do',
        method: 'post',
        params:{type:4126,relatedId:data,validStatus:1}
    })
}


function afterSaleData(data) {
    return axios({
        url:'/buyorderExpense/aftersale/getExpenseAfterSalesByExpenseId.do',
        method:'post',
        params:{buyorderExpenseId:data}
    })
}

function completeOrder(data) {
    return axios({
        url:'/buyorderExpense/completeBuyOrderExpense.do',
        method:'post',
        params:{buyorderExpenseId:data}
    })
}

function parseBuyOrderExpenseOrderDesc(buyOrderExpenseOrder){
    let orderDescStr = ''
    let buyOrderExpenseOrderDetailDto = buyOrderExpenseOrder.buyorderExpenseDetailDto
    if(buyOrderExpenseOrderDetailDto != undefined){
        let orderRemarkArr = buyOrderExpenseOrderDetailDto.orderDesc
        if(orderRemarkArr != undefined){
            for(let i in orderRemarkArr){
                orderRemark = orderRemarkArr[i]
                let orderRemarkType = orderRemark.type
                let saleOrderDetail = orderRemark.originalOrderDto
                let saleOrderNo = saleOrderDetail.no
                let saleOrderId = saleOrderDetail.orderId
                let saleOrderGoodsList = saleOrderDetail.goodsDtos
                let buyorderExpenseOrder = orderRemark.resultOrderDto
                let buyorderExpenseOrderNo = buyorderExpenseOrder.no
                let buyorderExpenseOrderId = buyorderExpenseOrder.orderId
                for (let j in saleOrderGoodsList){
                    goodsDto = saleOrderGoodsList[j]
                    let sku = goodsDto.sku
                    let goodsId = goodsDto.goodsId
                    let x = goodsDto.x
                    let y = goodsDto.y
                    let tempDesc = ""
                    if(orderRemarkType == 1){
                        // 本次关联的销售订单 A 中 B 订货号退货 X 个，分摊后本订单需要退货 Y 个
                        tempDesc = `本次关联的销售订单 <a style="text-decoration:none;" href="javascript:void(0);"
                                onclick="openTab('销售订单信息', '/order/saleorder/view.do?saleorderId=${saleOrderId}');">
                                <strong class="order-desc-style">${saleOrderNo}</strong>
                                </a> 中 <a href="javascript:void(0);" style="text-decoration:none;"
                           onclick="openTab('产品信息', '/goods/goods/viewbaseinfo.do?goodsId=${goodsId}');">
                           <strong class="order-desc-style">${sku}</strong>
                           </a> 订货号退货 ${x} 个，分摊后本订单需要退货 ${y} 个</br>`
                    }else if (orderRemarkType == 2){
                        //该订单由销售单A中B订货号退货，联动采购费用单C退货，自动转单生成
                        tempDesc = `该订单由销售单 <a style="text-decoration:none;" href="javascript:void(0);"
                                onclick="openTab('销售订单信息', '/order/saleorder/view.do?saleorderId=${saleOrderId}');">
                                <strong class="order-desc-style">${saleOrderNo}</strong>
                                </a> 中 <a class="addtitle" href="javascript:void(0);" style="text-decoration:none;"
                           onclick="openTab('产品信息', '/goods/goods/viewbaseinfo.do?goodsId=${goodsId}');">
                           <strong class="order-desc-style">${sku}</strong>
                           </a> 订货号退货，联动采购费用单 <a href="javascript:void(0);" style="text-decoration:none;" 
                           onclick="openTab('采购费用单信息', '/buyorderExpense/details.do?buyorderExpenseId=${buyorderExpenseOrderId}');">
                           <strong class="order-desc-style">${buyorderExpenseOrderNo}</strong>
                           </a> 退货，自动转单生成</br>`
                    }
                    orderDescStr = orderDescStr + tempDesc
                }
            }
        }
    }
    return orderDescStr
}
