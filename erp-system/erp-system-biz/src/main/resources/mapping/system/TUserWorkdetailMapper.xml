<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.system.mapper.TUserWorkdetailMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.system.domain.TUserWorkdetail">
    <!--@mbg.generated-->
    <!--@Table T_USER_WORKDETAIL-->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="UNIQUE_ID" jdbcType="VARCHAR" property="uniqueId" />
    <result column="JOB_NUMBER" jdbcType="VARCHAR" property="jobNumber" />
    <result column="HAND_OVER_JOB_NUMBER" jdbcType="VARCHAR" property="handOverJobNumber" />
    <result column="HAND_OVER_NAME" jdbcType="VARCHAR" property="handOverName" />
    <result column="START_TIME" jdbcType="TIMESTAMP" property="startTime" />
    <result column="END_TIME" jdbcType="TIMESTAMP" property="endTime" />
    <result column="WORKTYPE" jdbcType="INTEGER" property="worktype" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, UNIQUE_ID, JOB_NUMBER, HAND_OVER_JOB_NUMBER, HAND_OVER_NAME, START_TIME, END_TIME, 
    WORKTYPE, ADD_TIME, MOD_TIME
  </sql>




  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_USER_WORKDETAIL
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_USER_WORKDETAIL
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.system.domain.TUserWorkdetail" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_USER_WORKDETAIL (UNIQUE_ID, JOB_NUMBER, HAND_OVER_JOB_NUMBER, 
      HAND_OVER_NAME, START_TIME, END_TIME, 
      WORKTYPE, ADD_TIME, MOD_TIME
      )
    values (#{uniqueId,jdbcType=VARCHAR}, #{jobNumber,jdbcType=VARCHAR}, #{handOverJobNumber,jdbcType=VARCHAR}, 
      #{handOverName,jdbcType=VARCHAR}, #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, 
      #{worktype,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.system.domain.TUserWorkdetail" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_USER_WORKDETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="uniqueId != null">
        UNIQUE_ID,
      </if>
      <if test="jobNumber != null">
        JOB_NUMBER,
      </if>
      <if test="handOverJobNumber != null">
        HAND_OVER_JOB_NUMBER,
      </if>
      <if test="handOverName != null">
        HAND_OVER_NAME,
      </if>
      <if test="startTime != null">
        START_TIME,
      </if>
      <if test="endTime != null">
        END_TIME,
      </if>
      <if test="worktype != null">
        WORKTYPE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="uniqueId != null">
        #{uniqueId,jdbcType=VARCHAR},
      </if>
      <if test="jobNumber != null">
        #{jobNumber,jdbcType=VARCHAR},
      </if>
      <if test="handOverJobNumber != null">
        #{handOverJobNumber,jdbcType=VARCHAR},
      </if>
      <if test="handOverName != null">
        #{handOverName,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="worktype != null">
        #{worktype,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.system.domain.TUserWorkdetail">
    <!--@mbg.generated-->
    update T_USER_WORKDETAIL
    <set>
      <if test="uniqueId != null">
        UNIQUE_ID = #{uniqueId,jdbcType=VARCHAR},
      </if>
      <if test="jobNumber != null">
        JOB_NUMBER = #{jobNumber,jdbcType=VARCHAR},
      </if>
      <if test="handOverJobNumber != null">
        HAND_OVER_JOB_NUMBER = #{handOverJobNumber,jdbcType=VARCHAR},
      </if>
      <if test="handOverName != null">
        HAND_OVER_NAME = #{handOverName,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        START_TIME = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        END_TIME = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="worktype != null">
        WORKTYPE = #{worktype,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.system.domain.TUserWorkdetail">
    <!--@mbg.generated-->
    update T_USER_WORKDETAIL
    set UNIQUE_ID = #{uniqueId,jdbcType=VARCHAR},
      JOB_NUMBER = #{jobNumber,jdbcType=VARCHAR},
      HAND_OVER_JOB_NUMBER = #{handOverJobNumber,jdbcType=VARCHAR},
      HAND_OVER_NAME = #{handOverName,jdbcType=VARCHAR},
      START_TIME = #{startTime,jdbcType=TIMESTAMP},
      END_TIME = #{endTime,jdbcType=TIMESTAMP},
      WORKTYPE = #{worktype,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByUniqueId" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_USER_WORKDETAIL
    where UNIQUE_ID = #{id,jdbcType=INTEGER}
  </select>

  <select id="selectByUserIdAndDate"  resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_USER_WORKDETAIL
    where JOB_NUMBER = #{jobNumber,jdbcType=VARCHAR}
    AND WORKTYPE = #{worktype,jdbcType=INTEGER}
    AND START_TIME <![CDATA[ <= ]]>  #{date,jdbcType=VARCHAR}
    AND END_TIME <![CDATA[ >= ]]>  #{date,jdbcType=VARCHAR}

  </select>

</mapper>