<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="订单打印" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<%--<link href='<%=basePath%>static/css/order_print/system.css'
      rel="stylesheet" type="text/css">--%>
<%--<link rel="stylesheet"
      href='<%=basePath%>static/css/order_print/ui.theme.css?rnd=${resourceVersionKey}'
      type="text/css" media="all">--%>
<%--<link type="text/css" rel="stylesheet"
      href='<%=basePath%>static/css/order_print/layer.css?rnd=${resourceVersionKey}'
      id="skinlayercss">--%>
<script type="text/javascript"
        src='<%=basePath%>static/js/jquery.PrintArea.js'></script>
<script type="text/javascript"
        src='<%=basePath%>static/js/order/saleorder/order_print.js?rnd=${resourceVersionKey}'></script>
<link type="text/css" rel="stylesheet" href="<%=basePath%>static/css/general.css?rnd=${resourceVersionKey}">
<link type="text/css" rel="stylesheet" href="<%=basePath%>static/css/manage.css"/>
<body>

<c:choose>
    <c:when test="${buyorderVo.contractUrl != null && buyorderVo.contractUrl!=''}">
        <div  >
            <iframe style="width: 1400px;height: 1300px"  src="${buyorderVo.contractUrl}" frameborder="0" style="width:1000px; height:600px;" scrolling="no"></iframe>
        </div>
    </c:when>

<c:otherwise>
<div id="print-contract">
    <%-- 更换字体 --%>
    <style>
        @font-face {
            font-family: 'SourceHanSansCN-Regular';
            src: url('<%=basePath%>static/hansFont/SourceHanSansCN-Regular.otf');
        }

        html, body, #print-contract {
            font-family: "SourceHanSansCN-Regular", sans-serif;
        }
        .keyWord{
            color: rgba(255,255,255,0);
            font-size: 1px;
        }
    </style>
    <div class="">
        <div class="contract-head">
            <c:if test="${ buyorderVo.orgId == 36}">
                <img src="<%=basePath%>static/images/logo2.jpg"/>
            </c:if>
            <c:if test="${buyorderVo.orgId != 36 }">
                <img src="<%=basePath%>static/images/logo1.jpg?v=20201029" class=""/>
            </c:if>
            <div class="contract-number">
                <div>合同号码: ${buyorderVo.buyorderNo }</div>
                <div>制单日期:
                    <c:if test="${buyorderVo.validTime ==0 }">
                        ${currTime}
                    </c:if>
                    <c:if test="${buyorderVo.validTime !=0}">
                        <date:date value="${buyorderVo.validTime }" format="yyyy-MM-dd"/>
                    </c:if>
                </div>
            </div>
        </div>
        <div class="contract-head-title">采购合同</div>
        <div class="contract-print-table">
            <table>
                <tbody>
                <tr class="jiayi">
                    <td><span>甲方：</span><span>南京贝登医疗股份有限公司</span><span class="keyWord">$yi$</span></td>
                    <td><span>乙方：</span><span>${ buyorderVo.traderName}</span><span class="keyWord">$yifang$</span></td>
                </tr>
                <tr>
                    <td>
                        <div style="display: inline-block">
                            采购及收票人员： ${detail.realName}
                        </div>
                        <div>
                            <span>联系方式：</span>
                            <span>${detail.mobile }
                            <c:if test="${detail.mobile !=null && detail.mobile !='' &&detail.telephone!=null &&detail.telephone!='' }">/</c:if> ${detail.telephone }</span>
                        </div>
                        <div>
                            <span>开票信息：</span>
                            <span></span>
                        </div>
                        <div class="overflow-hidden">
                            <span style="width:71px;float:left;">注册地址/电话：</span>

                            <span style="float:left;display:inline-block;max-width:-moz-calc(100% - 91px);max-width:-webkit-calc(100% - 91px);max-width:calc(100% - 91px);">${vedeng_address_phone}</span>
                        </div>
                        <div>
                            <span>开户行：</span>
                            <span>中国建设银行股份有限公司南京中山南路支行</span>
                        </div>
                        <div>
                            <span>税号：</span>
                            <span>91320100589439066H</span>
                        </div>
                        <div>
                            <span>账号：</span>
                            <span>32001881236052503686</span>
                        </div>
                    </td>
                    <td>
                        <div>
                            <span>销售人员：</span>
                            <span>${ buyorderVo.traderContactName}<%--<c:if test="${buyorderVo.sex eq 0}">&nbsp;女士</c:if><c:if test="${buyorderVo.sex eq 1}">&nbsp;先生</c:if>--%>
                            </span>
                        </div>
                        <div>
                            <span>联系方式：</span>
                            <span>${buyorderVo.traderContactTelephone }
                            <c:if test="${buyorderVo.traderContactTelephone!=null && buyorderVo.traderContactTelephone!=''}">/</c:if>
                            ${buyorderVo.traderContactMobile }</span>
                        </div>
                        <div>
                            <span>收款信息：</span>
                            <span></span>
                        </div>
                        <div>
                            <span>开户行：</span>
                            <span>${buyorderVo.bank }</span>
                        </div>
                        <div>
                            <span>账号：</span>
                            <span>${buyorderVo.bankAccount }</span>
                        </div>
                        <div>
                            <span>税号：</span>
                            <span>${buyorderVo.taxNum }</span>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
            <div class="contract-print-title2 pl10">货物接收信息</div>
            <table>
                <tbody>
                <tr>
                    <td>
                        <div>
                            <span>收货人/联系方式：</span>
                            <span>${buyorderVo.takeTraderContactName }/${buyorderVo.takeTraderContactMobile }/${buyorderVo.takeTraderContactTelephone }</span>
                            <div>
                                <span>收货地址：</span>
                                <span>${buyorderVo.takeTraderArea}${buyorderVo.takeTraderAddress}</span>
                            </div>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
            <div class="contract-tips">就甲方向乙方采购产品事宜，经友好协商，由双方于${contractDateList.get(0)}年${contractDateList.get(1)}月${contractDateList.get(2)}日在南京市秦淮区签订本合同，以资遵守。</div>
            <div class="contract-print-title2 pl10">一、产品信息：</div>
            <table class="print-product-table">
                <tbody>
                <tr class="font-bold tdsmall">
                    <td style="width:4%;">序号</td>
                    <td style="width:8%;">订货号</td>
                    <td style=" ">产品名称</td>
                    <td style="width:6%;">品牌</td>
                    <td style="width:8%;">型号/规格</td>
                    <c:if test="${haveMedicalApparatus == 1}">
                        <td style="width:11%;">注册证编号/备案编号</td>
                        <td style="width:11%;">注册人/备案人名称</td>
                    </c:if>
                    <td style="width:4%;">数量</td>
                    <td style="width:4%;">单位</td>
                    <td style="width:6%">单价(元）</td>
                    <td style="width:6%;">金额(元）</td>
                    <td style="width:5%;">货期<br/><span style="font-size:0.75em;">(日)</span></td>
                    <td style="width:5%">安装政策</td>
                    <td style="width:5%;">质保期</td>
                    <td style="width:4%;">备注</td>
                </tr>
                <c:set var="count" value="1"></c:set>
                <c:forEach var="list" items="${buyorderGoodsList}" varStatus="num">
                    <c:if test="${list.isDelete eq 0}">
                        <tr>
                            <td class="" style="width: 4%;"> ${count}</td>
                            <c:set var="count" value="${count+1}"></c:set>
                            <td style="width: 6%;">${list.sku }</td>
                            <td style="width: 26%; text-align:left;">${ list.goodsName}</td>
                            <td style="">${list.brandName }</td>
                            <td style="">
                                <c:choose>
                                    <c:when test="${list.spuType == 316 || list.spuType == 1008}">
                                        ${list.model}
                                    </c:when>
                                    <c:when test="${list.spuType == 317 || list.spuType == 318}">
                                        ${list.spec}
                                    </c:when>
                                    <c:otherwise>
                                        <c:choose>
                                            <c:when test="${list.model != null && list.model != ''}">
                                                ${list.model}
                                            </c:when>
                                            <c:otherwise>${list.spec}</c:otherwise>
                                        </c:choose>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <c:if test="${haveMedicalApparatus == 1}">
                                <td style="">
                                    <c:choose>
                                        <c:when test="${list.registrationNumber != null && list.registrationNumber != '' }">
                                            ${list.registrationNumber}
                                        </c:when>
                                        <c:otherwise>
                                            -
                                        </c:otherwise>
                                    </c:choose>
                                </td>
                                <td style="">
                                    <c:choose>
                                        <c:when test="${list.manufacturerName != null && list.manufacturerName != '' }">
                                            ${list.manufacturerName}
                                        </c:when>
                                        <c:otherwise>
                                            -
                                        </c:otherwise>
                                    </c:choose>
                                </td>
                            </c:if>
                            <td style="width: 4%;">${list.num }</td>
                            <td style="width: 4%;">${list.unitName }</td>
                            <td style="width: 10%;">${list.prices }</td>
                            <td style="width: 10%;">${list.allPrice}</td>
                            <td style="width: 5%;">${list.deliveryCycle}</td>

                            <td>${list.installPolicy }</td>
                            <td>${list.qualityPeriod }</td>
                            <td>${list.insideComments }</td>
                        </tr>
                    </c:if>
                </c:forEach>

                <tr>
                    <td colspan=3>合计</td>
                    <td colspan="${haveMedicalApparatus == 1 ? 8 : 6}" style="overflow: hidden;">
                        <div class="total-count f_left">（大写）${chineseNumberTotalPrice }</div>
                        <div class="total-count-num f_right">￥${totalAmount}</div>
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="${haveMedicalApparatus == 1 ? 15 : 13}">&nbsp;</td>
                </tr>
                </tbody>
            </table>
            <div class="contract-print-title2">二、付款方式&发票类型：</div>
            <div class="contract-details">
                1、 付款方式：
                <c:if test="${buyorderVo.bankAcceptance == 1}">
                    银行承兑汇票，
                </c:if>
                <c:if test="${buyorderVo.paymentType == 419}">
                    先款后货，预付100% ，预付金额<fmt:formatNumber type="number" value="${buyorderVo.prepaidAmount + buyorderExpenseDto.buyorderExpenseDetailDto.prepaidAmount}" pattern="0.00" maxFractionDigits="2"/>元。
                </c:if>
                <c:if test="${buyorderVo.paymentType == 420}">
                    先货后款，预付80%，预付金额<fmt:formatNumber type="number" value="${buyorderVo.prepaidAmount + buyorderExpenseDto.buyorderExpenseDetailDto.prepaidAmount}" pattern="0.00" maxFractionDigits="2"/>元，账期支付<fmt:formatNumber type="number" value="${buyorderVo.accountPeriodAmount + buyorderExpenseDto.buyorderExpenseDetailDto.accountPeriodAmount}" pattern="0.00" maxFractionDigits="2"/>元。
                </c:if>
                <c:if test="${buyorderVo.paymentType == 421}">
                    先货后款，预付50%，预付金额<fmt:formatNumber type="number" value="${buyorderVo.prepaidAmount + buyorderExpenseDto.buyorderExpenseDetailDto.prepaidAmount}" pattern="0.00" maxFractionDigits="2"/>元，账期支付<fmt:formatNumber type="number" value="${buyorderVo.accountPeriodAmount + buyorderExpenseDto.buyorderExpenseDetailDto.accountPeriodAmount}" pattern="0.00" maxFractionDigits="2"/>元。
                </c:if>
                <c:if test="${buyorderVo.paymentType == 422}">
                    先货后款，预付30%，预付金额<fmt:formatNumber type="number" value="${buyorderVo.prepaidAmount + buyorderExpenseDto.buyorderExpenseDetailDto.prepaidAmount}" pattern="0.00" maxFractionDigits="2"/>元，账期支付<fmt:formatNumber type="number" value="${buyorderVo.accountPeriodAmount + buyorderExpenseDto.buyorderExpenseDetailDto.accountPeriodAmount}" pattern="0.00" maxFractionDigits="2"/>元。
                </c:if>
                <c:if test="${buyorderVo.paymentType == 423}">
                    先货后款，预付0%，预付金额<fmt:formatNumber type="number" value="${buyorderVo.prepaidAmount + buyorderExpenseDto.buyorderExpenseDetailDto.prepaidAmount}" pattern="0.00" maxFractionDigits="2"/>元，账期支付<fmt:formatNumber type="number" value="${buyorderVo.accountPeriodAmount + buyorderExpenseDto.buyorderExpenseDetailDto.accountPeriodAmount}" pattern="0.00" maxFractionDigits="2"/>元。
                </c:if>
                <c:if test="${buyorderVo.paymentType == 424}">
                    自定义，预付金额<fmt:formatNumber type="number" value="${buyorderVo.prepaidAmount + buyorderExpenseDto.buyorderExpenseDetailDto.prepaidAmount}" pattern="0.00" maxFractionDigits="2"/>元，账期支付${buyorderVo.accountPeriodAmount }元，尾款${buyorderVo.retainageAmount }元。
                    ，尾款期限${buyorderVo.retainageAmountMonth }月
                </c:if>
                <br/>
                2、 发票类型：
                <c:forEach var="list" items="${invoiceTypes}">
                    <c:if test="${buyorderVo.invoiceType == list.sysOptionDefinitionId}">${list.title}</c:if>
                </c:forEach>
                。乙方开具的发票必须注明双方签订合同时的订单号，否则会影响后续合作和付款。
                <br/>
                3、 开票时间：乙方应在发货后七个工作日内向甲方开具满足上述发票类型的合规票据并于开票之日将发票寄送给甲方；若本合同分批次发货的，乙方应在每批次发货后的七个工作日完成开票并于开票之日将发票寄送给甲方。
            </div>
            <div class="contract-print-title2">三、产品交付：</div>
            <div class="contract-details">
                1、 关于货期：货期⾃⼄⽅收到甲⽅预付款之日起开始计算。<br/>
                2、 当收到甲方对产品与订单不符，或质量不合格声明时，乙方负责无条件退货。甲方由此而产生的所有费用，应由乙方承担，由此造成交货期延误的，按本条第3款约定执行。<br/>
                3、 交货期：如因乙方原因造成货物不能在订单规定的日期完成交货，乙方需承担延迟交付的责任，乙方每天赔偿合同总金额的0.3%作为违约赔偿金，直到收到货物为止。如到合同规定的交期，乙方仍不能交货，甲方有权解除合同，乙方必须全额退还甲方的订货预付款，且乙方向甲方支付合同总金额30%的违约金，并承担对甲方造成的损失。<br/>
                4、 针对存在有效期的产品（效期产品），包括但不限于耗材、试剂产品，乙方应保证送达甲方指定收货地点时效期产品剩余有效期的天数大于效期产品总有效期天数的2/3。否则甲方有权直接拒收，由此产生的甲方协助乙方退回货物的运费、乙方再次发货给甲方的运费、拒收货物损毁灭失风险等全部由乙方承担。由此造成交货期延误的，按本条第3款约定执行。<br/>
                5、 合同第一条所约定的合同价款包括但不限于货款、税金、包装、运输、装卸、保险、报关（如系进口商品）、安装、调试、技术指导、培训、咨询以及售后服务等其他各项有关费用。即：除合同第一条所约定的合同价款外，甲方无须再向乙方支付其他任何费用。<br/>
                6、 乙方应保证产品到货时随货提供产品质量合格证明材料（包括但不限于质量检验报告、合格证书、合格标签等）。产品若为医疗器械，乙方还应提供加盖乙方出库章的随货同行单；有冷链储运管理要求的，乙方还应提供合规的冷链交接单、在途温湿度记录；产品若为进口产品，乙方应保证产品已按照中国的进口法律、法规和政策办理全部进口手续，应向甲方提供真实有效的进口报关单及出入境检验检疫证书（如适用）。<br/>
                7、 产品经甲方验收合格后，视为乙方完成产品的交付，产品灭失、损毁的风险自此转移至甲方。<br/>
                8、 未经甲方书面同意，乙方不得提前交货，否则甲方有权拒收，乙方应承担因此发生的仓储费、保管费、货损或其他费用。
            </div>
            <div class="contract-print-title2">四、质量保证：</div>
            <div class="contract-details">
                1、 乙方保证提供的产品为原厂原包装正品，产品未经拆封、未经更换标签、及未开机使用。乙方应保证产品内外包装上标识产品唯一性质的编码内容完全一致，且可以在厂家官方可查验渠道上进行验证查询。<br/>
                2、 乙方提供的产品必须是符合国家质量标准的合格品，产品的包装、标签和说明书符合国家和行业的有关规定，其包装能确保商品质量和货物运输要求。<br/>
                3、 乙方应当采取有效措施，确保产品运输、贮存过程符合产品说明书或者标签标示要求，尤其是需要特殊温度储运的产品。<br/>
                4、 若乙方提供的产品因质量问题（含包装质量）验收不合格，乙方应积极协助甲方退换货，退换货产生的运输费用由乙方承担。若因质量问题（含包装质量）而造成甲方损失的，乙方也应承担甲方的损失。
            </div>
            <div class="contract-print-title2">五、售后服务：</div>
            <div class="contract-details">
                1、 根据甲方及产品特性，乙方给甲方提供售后服务，服务内容包含提供技术支持、对质量或故障医疗器械进行维修，产品培训等。服务的方式包含直接上门提供维修服务，电话方式进行专业指导、联系生产企业对接解决问题。<br/>
                2、 对于甲方自身设立售后服务人员，可由乙方对其下游客户或使用者提供安装、维修、技术培训的服务，必要时可联系生产企业技术人员提供售后服务。<br/>
            </div>
            <div class="contract-print-title2">六、反商业贿赂：</div>
            <div class="contract-details">
                1、 双方应按照所有适用的法律法规，包括但不限于所有适用的反贿赂和反不正当法律法规，履行其在本合同项下的各项义务。乙方确认知晓国家法律、法规和甲方相关制度关于反商业贿赂事项的各项规定。乙方已获甲方正式告知，甲方对于任何形式商业贿赂均持坚决的反对态度，亦不会授权任何员工要求、指示、暗示乙方实施、参与任何形式商业贿赂行为，以获得交易机会或其他经济利益。甲方员工如有实施商业贿赂行为，乙方将第一时间告知甲方。双方在开展业务发展活动过程中，将严格遵守国家法律、法规，不从事、参与任何形式的商业贿赂及不正当竞争行为，以自身行动维护双方良好的合作关系。一旦发现，甲方有权单方解除本合同，并将乙方纳入供应商黑名单，同时，乙方应向甲方支付等同于已发生业务货款金额二十倍的违约金，并承担由于该违约行为给甲方造成的一切损失，甲方可直接从应付乙方的货款中扣除，不足部分甲方保留向乙方追偿的权利。
            </div>
            <div class="contract-print-title2">七、知识产权：</div>
            <div class="contract-details">
                1、 未经甲⽅事先书⾯同意，乙⽅不得在任何国家或地区，并通过任何形式，使⽤甲⽅及其关联⽅的商标、商号或将与甲⽅商标、商号相同或类似的内容注册为乙⽅的商标、商号、公司域名等。<br/>
                2、 未经甲⽅事先书⾯同意，乙⽅不得对甲⽅及其关联⽅的企业名称、商标、商号、服务标志或标识进⾏任何商业使⽤和擅⾃作出任何变更，包括但不限于在⼴告、宣传资料、办公地点等使⽤。<br/>
                3、 乙方承诺对于乙方所销售的产品未侵害任何第三方的知识产权，若甲方因此受到任何第三方关于该产品知识产权的责任追究都由乙⽅⾃⾏承担，由此给甲⽅造成损失的，乙⽅还应向甲⽅承担相应的赔偿责任。
            </div>
            <div class="contract-print-title2">八、保密条款：</div>
            <div class="contract-details">
                甲方向乙方提供的与此订单有关的所有信息，包括但不限于关于产品、商业机密、图纸、文件、商标，乙方必须保密，且未经甲方事先书面同意，乙方不得公开或以其他方式向第三方透露，否则应当承担给甲方造成的损失。<br/>
            </div>
            <div class="contract-print-title2">九、违约责任：</div>
            <div class="contract-details">
                合同生效后，乙方不得单方面撤销合同。若乙方单方面撤销合同或乙方未按照本合同各条的要求履行义务而产生违约的，乙方应承担合同总金额30%的违约金（各条有单独约定违约金的适用各条约定的违约金），并承担给甲方造成的损失。
            </div>
            <div class="contract-print-title2"> 十、合同争议处理：</div>
            <div class="contract-details">
                对于合同争议，双方需本着友好精神协商解决，协商不成，可将争议提请甲方所在地人民法院诉讼解决。
            </div>
            <div class="contract-print-title2"> 十一、合同效力：</div>
            <div class="contract-details">
                本合同自双方盖章之日起生效，传真件、扫描件与原件具有同等法律效力。
            </div>
            <div class="contract-print-title2"> 十二、 其他约定：</div>
            <div class="contract-details">
                若货物由乙方按照甲方指示直接发送甲方客户的，乙方还应遵守如下约定：<br>
                1、乙方按照甲方要求发货。<br>
                2、乙方发货时，不能夹带乙方任何联系方式，如名片、便签、含价格单据等。<br>
                3、乙方发货时，不能夹带乙方开具给甲方的发票。<br>
                4、甲方的客户，甲方有权自主维护。若甲方客户有意越过甲方与乙方直接合作，乙方应拒绝为甲方客户报价，并将情况及时告知甲方。<br>
                5、上述条款中，乙方若有任何一条违约情况发生，应承担合同总金额30%的违约金。<br>
            </div>
            <div class="contract-print-title2">十三、补充条款：</div>
            <c:if test="${!empty buyorderVo.additionalClause}">
                <div class="contract-details">${buyorderVo.additionalClause }</div>
            </c:if>
            <c:if test="${empty buyorderVo.additionalClause}">
                <div class="contract-details">无。</div>
            </c:if>
            <div class="contract-print-sign">
                <ul>
                    <li style="margin-left: 50px;padding:32px 45px 0px 45px;">
                        <div class="sign-name">甲方：南京贝登医疗股份有限公司<span class="keyWord">$yi$</span></div>
                        <div class="sign-place">
                            <%--<ul>
                                <li>
                                    <div style="margin-top:22px;">${ detail.realName }</div>
                                    <div>授权人签字</div>
                                </li>
                                <li>
                                    <div style="margin-top:22px;">
                                        <c:if test="${buyorderVo.validTime ==0 }">
                                            ${currTime}
                                        </c:if>
                                        <c:if test="${buyorderVo.validTime !=0}">
                                            <date:date value="${buyorderVo.validTime }" format="yyyy-MM-dd"/>
                                        </c:if>
                                    </div>
                                    <div>日期</div>
                                </li>
                            </ul>--%>
                        </div>
                    </li>
                    <li style="float: right; margin-right: 50px;overflow:visible;position: relative; padding:32px 0 28px 0;">
                        <div class="sign-name"><span class="keyWord">$yifang$</span>乙方：${ buyorderVo.traderName}</div>
                        <div class="sign-place">
                            <%--<ul>
                                <li>
                                    <div></div>
                                    <div>授权人签字</div>
                                </li>
                                <li>
                                    <div></div>
                                    <div>日期</div>
                                </li>
                            </ul>--%>
                        </div>
                    </li>
                    <div class='clear'></div>
                </ul>
            </div>
            <div style="page-break-before: always;">
                <table cellpadding="0" cellspacing="0" width="100%" border="0"
                       height="90">
                    <tbody>
                    <tr>
                        <td colspan="2" class="align_c"
                            style="padding-top: 15px; padding-bottom: 15px;text-align:center"><b
                                style="font-size: 14px;">委托直接发货说明</b></td>
                    </tr>
                    <tr>
                        <td colspan="2">致 <b>${ buyorderVo.traderName}</b> <br> 兹委托贵司 <b>${ buyorderVo.buyorderNo}</b>
                            货物直接发至我司客户处（发货信息请见合同）。非常感谢贵司对于
                            <c:if test="${ buyorderVo.companyId ==1}">
                                贝登工作的支持，贝登坚信，
                            </c:if>

                            <div>通过这样的合作，未来我们将更加深入地展开合作，更加彼此信任。</div>
                            <div> 特将发货注意事项，进行如下说明：</div>
                            <div> 1、快递单书写内容：</div>
                            <c:if test="${ buyorderVo.companyId ==1}">
                                <div style='margin-left:15px;'>发货公司名字---南京贝登医疗股份有限公司</div>
                                <div style='margin-left:15px;'> 寄件人姓名------贝登物流部</div>
                                <div style='margin-left:15px;'>
                                    联系电话---------025-86282793
                                </div>
                            </c:if>

                            2、请将产品仔细包装，以保证产品在抵达目的地时包装完好。<br> <br> 再次感谢贵司对
                            <c:if test="${ buyorderVo.companyId ==1}">
                                贝登
                            </c:if>
                            工作的支持！<br>
                            <br>
                            <br>
                        </td>
                    </tr>
                    </tbody>
                </table>

            </div>
        </div>
    </div>
        <c:if test="${autoGenerate != null and !autoGenerate}">
            <div class='tcenter mb15'>
                    <span class=" bt-small bt-bg-style bg-light-blue" id="btnPrint">打印</span>
                <c:if test="${buyorderVo.validStatus ==1}">
                    <span class=" bt-small bt-bg-style bg-light-blue"  id="signature">电子签章</span>
                </c:if>
            </div>
        </c:if>
</div>
</c:otherwise>
</c:choose>
<input type="hidden" name="buyorderId" id="buyorderId" value="${buyorderVo.buyorderId}">
<script type="text/javascript">
    $("#signature").click(function() {
        let buyorderId = $("#buyorderId").val();
        $.ajax({
            url:page_url+'/order/newBuyorder/signature.do',
            data:{"buyorderId":buyorderId},
            type:"POST",
            dataType : "json",
            async: false,
            success:function(data)
            {
                if(data.code==0){
                    layer.alert("已发起电子签章流程。可稍后查看，无需频繁发起");
                }else{
                    layer.alert(data.message);
                }

            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });

    });
</script>
<script>
    $("#btnPrint").click(function () {
        $("#btnPrint").hide();

        if (window.ActiveXObject || "ActiveXObject" in window) {
            $("#print-contract").printArea({
                mode: 'popup'
            });
        } else {
            $("#print-contract").printArea({
                mode: 'popup',
                popHt: 800,
                popWd: 1500,
                popX: 200,
                popY: 200,
                popTitle: "${buyorderVo.buyorderNo }" + "${ buyorderVo.traderName}"
            });
        }
        $("#btnPrint").show();
    });
</script>
<%@ include file="../../common/footer.jsp" %>
