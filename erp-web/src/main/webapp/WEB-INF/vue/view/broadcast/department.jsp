<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../common/common.jsp" %>

<link rel="stylesheet" href="/static/vue/vd-ui/common.css">
<link rel="stylesheet" href="/static/vue/vd-ui/ui.css">
<link rel="stylesheet" href="/static/vue/receiptnotice/list.css">
<title>部门管理</title>

<div class="user-container list-container" id="page-container">

    <div class="list-top">
        <h1 class="title">部门管理</h1>
        <ui-button type="primary" width="100px" @click="addRow">新建小组</ui-button>
    </div>

    <div class="list-filter form-wrap">
        <div class="list-row">
            <div class="margin-r20">
                <ui-form-item label="部门名称" label-width="72px">
                    <ui-input v-model="filter_dep" clearable></ui-input> 
                </ui-form-item>
            </div>
            <div class="margin-r20">
                <ui-form-item label="小组名称" label-width="72px">
                    <ui-input v-model="filter_group" clearable></ui-input> 
                </ui-form-item>
            </div>
            <div class="margin-r20">
                <ui-form-item label="AED销售" label-width="74px">
                    <ui-select 
                        :remote="true" 
                        :remote-info="saleRemoteInfo"
                        placeholder="全部" 
                        v-model="filter_sale" 
                        :default-label="filter_saleName"
                        clearable 
                        @change="handlerChange"
                    ></ui-select>
                </ui-form-item>
            </div>
            <div class="filter-btns">
                <ui-button type="primary" width="100px" @click="handlerSearch">搜索</ui-button>
                <ui-button type="link" class="margin-l10" width="100px" @click="handlerReset">重置</ui-button>
            </div>
        </div>
    </div>

    <div class="list-wrap">
        <el-table 
            ref="userData" 
            :data="list" 
            border 
            :header-cell-style="{ textAlign: 'center', background: '#ECF1F5' }"
            :cell-style="{ textAlign: 'center' }"
            size="small"
            style="width: 100%"
        >
            <el-table-column prop="id" label="小组ID" width="80"></el-table-column>
            <el-table-column prop="deptName" label="部门名称" width="200"></el-table-column>
            <el-table-column prop="groupName" label="小组名称" width="200"></el-table-column>
            <el-table-column label="AED销售">
                <div slot-scope="scope" style="word-break: break-word;">
                    {{ scope.row.aedUsernames && scope.row.aedUsernames.split(',').join('、') }}
                </div>
            </el-table-column>
            <el-table-column prop="creatorRealName" label="创建人" width="120"></el-table-column>
            <el-table-column prop="addTime" label="创建时间" width="180"></el-table-column>
            <el-table-column fixed="right" label="操作" width="120">
                <template slot-scope="scope">
                    <el-button @click.native.prevent="editRow(scope.row)" type="text" size="mini">编辑</el-button>
                    <el-button @click.native.prevent="delRow(scope.row)" type="text" size="mini">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            class="list-pagination"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="pageNum"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total">
        </el-pagination>
    </div>

    <ui-dialog
        :title="editData? '编辑小组' : '新建小组'"
        :visible.sync="isShowEditGroup"
        width="680px"
        align="center"
        scroll="in"
    >
        <div v-if="isShowEditGroup" class="form-wrap label-width-2">
            <ui-form-item label="部门" :must="true">
                <!-- 编辑时 禁用 ??? -->
                <ui-select
                    :remote="true" 
                    :remote-info="deptRemoteInfo"
                    placeholder="请选择" 
                    v-model="deptId" 
                    clearable 
                    :disabled="editData"
                    :default-label="defaultDeptName"
                    valid="editGroup_deptId"
                ></ui-select>
            </ui-form-item>
            <ui-form-item label="小组" :must="true">
                <div style="width: 300px;">
                    <ui-input 
                        v-model="groupName" 
                        maxlength="100" 
                        placeholder="请输入" 
                        clearable
                        valid="editGroup_groupName"
                    ></ui-input>
                </div>
            </ui-form-item>
            <ui-form-item label="AED销售">
                <ui-select
                    :remote="true" 
                    :remote-info="saleRemoteInfo" 
                    placeholder="请选择" 
                    v-model="sales" 
                    clearable 
                    multiple-type="fixed"
                    :default-multi="defaultSales"
                ></ui-select>
                <!-- valid="editGroup_sales" -->
            </ui-form-item>
        </div>
        <template slot="footer">
            <div class="dlg-form-footer">
                <ui-button @click="confirm" type="primary">保存</ui-button>
                <ui-button @click="isShowEditGroup = false" class="close">取消</ui-button>
            </div>
        </template>
    </ui-dialog>
</div>

<script src="${pageContext.request.contextPath}/static/vue/vd-ui/ui.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/js/receiptnotice/department.js?rnd=${resourceVersionKey}"></script>