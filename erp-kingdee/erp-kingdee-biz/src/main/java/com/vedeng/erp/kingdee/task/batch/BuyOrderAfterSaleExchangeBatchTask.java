package com.vedeng.erp.kingdee.task.batch;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.batch.job.BuyOrderAfterSaleExchangeBatchJob;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购单售后换货job
 * @date 2022/5/16 10:57
 */
@JobHandler(value = "BuyOrderAfterSaleExchangeBatchTask")
@Component
public class BuyOrderAfterSaleExchangeBatchTask extends AbstractJobHandler {

    @Autowired
    private BuyOrderAfterSaleExchangeBatchJob batchJob;

    @Autowired
    private JobLauncher jobLauncher;

    /**
     * {"beginTime":"2022-11-01 00:00:00","endTime":"2022-12-01 00:00:00"}
     *
     * @param param
     * @returnPayAliReceiptBillBatchJob
     * @throws Exception
     */
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("====================采购换货售后流程开始==================");
        JobParameters jobParameters = new TaskBatchHandle().buildJobParameters(param);
        Job job = batchJob.buyOrderAfterSaleExchangeFlowJob();
        jobLauncher.run(job, jobParameters);
        XxlJobLogger.log("====================采购换货售后流程结束==================");
        return SUCCESS;
    }


}

