package com.vedeng.erp.trader.service;


import com.vedeng.erp.common.dto.RSalesJBusinessOrderDto;
import com.vedeng.erp.trader.dto.PushBusinessLeadsDto;

import java.util.List;

public interface BusinessLeadsApiService {

    void pushSave(PushBusinessLeadsDto dto);

    /**
     * 更新首次跟进时间
     * @param id 线索id
     */
    void updateLeadsFirstFollowTime(Integer id);

    /**
     * 更新跟进状态
     * @param id
     */
    void updateLeadsFollowStatus(Integer id);

    /**
     * 检查当前用户是否可见
     * @param id
     * @return
     */
    String checkBusinessLeadsIfCurrentCurrentCanSee(Integer id, Integer uesrId, List<RSalesJBusinessOrderDto> list);


    String getBusinessLeadsNoById(Integer id);
}
