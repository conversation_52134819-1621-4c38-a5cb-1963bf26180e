package com.vedeng.erp.system.service;

import com.vedeng.erp.system.domain.entity.SyncOutInRelateEntity;
import java.util.List;

public interface SyncOutInRelateService {
    int deleteByPrimaryKey(Integer id);

    int insert(SyncOutInRelateEntity record);

    int insertSelective(SyncOutInRelateEntity record);

    SyncOutInRelateEntity selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SyncOutInRelateEntity record);

    int updateByPrimaryKey(SyncOutInRelateEntity record);

    List<SyncOutInRelateEntity> selectByProcessStatus(Integer processStatus);

    List<SyncOutInRelateEntity> selectByOutBusinessNo(String outBusinessNo);

    List<SyncOutInRelateEntity> selectByInBusinessNo(String inBusinessNo);
} 