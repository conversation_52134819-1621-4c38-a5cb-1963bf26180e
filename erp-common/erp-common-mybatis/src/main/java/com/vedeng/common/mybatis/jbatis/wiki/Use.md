#如何使用通用DAO



##1. DAO实现类实现继承通用的`BaseDao <T, PK extends Serializable>`并实现对应的DAO接口,必须指定泛型`<T>`和 主键类型<PK>

例如下面的例子:

```java
public interface UserInfoDAO  extends IBaseDao<UserInfoDTO,Integer>{
	//其他必须手写的接口...
}
```

### DAO接口继承通用的`IBaseDao <T, PK extends Serializable>`,必须指定泛型`<T>`和 主键类型<PK>(可选)

例如下面的例子:


	public class UserInfoDAOImpl extends BaseDao<UserInfoDTO, Integer> implements UserInfoDAO {
		//其他必须手写的接口...
	}



一旦继承了`BaseDao<T, PK extends Serializable>`,继承的`DAO`就拥有了以下通用的方法:


	//插入一条数据
	//UUID,类似Mysql的INDENTITY自动增长(自动回写)
	//优先使用传入的参数值,参数值空时,才会UUID,自动增长
	public abstract  PK create(T object);
	
	//根据主键进行更新,这里最多只会更新一条数据
	//参数为实体类
	public abstract  int update(T object);
	
	//根据实体类进行删除
	//参数为实体类
	public abstract  int remove(T object);
	
	//根据实体类进行统计
	//参数为实体类
	public abstract  int count(T object);
	
	//插入多条数据
	//UUID,类似Mysql的INDENTITY自动增长(自动回写)
	//优先使用传入的参数值,参数值空时,才会UUID,自动增长
	public abstract  boolean createOfBatch(List<T> list);
	
	//更新多条数据
	//更新的数据不为空 由REPLACE INTO实现
	public abstract  int updateOfBatch(List<T> list);
	
	//删除多条数据
	//删除的数据不为空 由REPLACE INTO实现
	public abstract  int removeOfBacth(List<T> list);
	
	//根据实体类不为null的字段进行查询,条件全部使用=号and条件
	public abstract  List<T> queryByObject(T object);
	
	//根据实体类List集合不为null的字段进行查询,条件全部使用=号and条件
	public abstract  List<T> queryByList(List<T> list);
	
	//根据实体类不为null的字段进行查询,条件全部使用=号and条件，orderby排序 分页
	public abstract List<T> queryListByCriteria(T object,String orderby,Integer offset,Integer limit);
	
	//根据实体类List集合不为null的字段进行查询,条件全部使用=号and条件，orderby排序 分页
	public abstract List<T> queryListByBatchCriteria(List<T> list,String orderby,Integer offset,Integer limit);
	
	//根据主键进行查询,必须保证结果唯一
	//单个字段做主键时,可以直接写主键的值
	public abstract T findById(PK id);
	
	//根据主键进行删除
	//单个字段做主键时,可以直接写主键的值
	public abstract boolean removeById(PK id);


##2. 泛型(实体类)`<T>`的类型必须符合要求

实体类按照如下规则和数据库表进行转换,注解全部是JPA中的注解:

1. 表名默认使用类名,驼峰转下划线(只对大写字母进行处理),如`UserInfo`默认对应的表名为`user_info`。

2. 表名可以使用`@Table(name = "tableName")`进行指定,对不符合第一条默认规则的可以通过这种方式指定表名.

3. 字段默认和`@Column`一样,都会作为表字段,表字段默认为Java对象的`Field`名字驼峰转下划线形式.

4. 可以使用`@Column(name = "fieldName")`指定不符合第3条规则的字段名

5. 使用`@Transient`注解可以忽略字段,添加该注解的字段不会作为表字段使用.

6. 建议一定是有一个`@Id`注解作为主键的字段,可以有多个`@Id`注解的字段作为联合主键.

7. 默认情况下,实体类中如果不存在包含`@Id`注解的字段,所有的字段都会作为主键字段进行使用(这种效率极低).

8. 实体类可以继承使用,但必须重新指定主键或覆盖主键值

9. 由于基本类型,如int作为实体类字段时会有默认值0,而且无法消除,所以实体类中建议不要使用基本类型.

除了上面提到的这些,Mapper还提供了UUID(任意数据库,字段长度32)、主键自增(类似Mysql)三种方式，其中序列和UUID可以配置多个，主键自增只能配置一个。

这三种方式不能同时使用,同时存在时按照 `UUID>主键自增`的优先级进行选择.下面是具体配置方法:



####1. 使用UUID时:
```java
//可以用于任意字符串类型长度超过32位的字段
@GeneratedValue(generator = "UUID")
private String username;
```
该字段不会回写。这种情况对应类似如下的XML：

`  <insert id="insertAuthor">
      <bind name="username_bind" value='@java.util.UUID@randomUUID().toString().replace("-", "")' />
      insert into Author
        (id, username, password, email,bio, favourite_section)
      values
        (#{id}, #{username_bind}, #{password}, #{email}, #{bio}, #{favouriteSection,jdbcType=VARCHAR})
  </insert>`

####2. 使用主键自增:

`//不限于@Id注解的字段,但是一个实体类中只能存在一个(继承关系中也只能存在一个)
@Id
@GeneratedValue(generator = "JDBC")
private Integer id;`
  
增加这个注解后，__会回写ID__。

   
  这种情况对于的xml类似下面这样：  

`  <insert id="insertAuthor">
    <selectKey keyProperty="id" resultType="int" order="BEFORE">
      SELECT LAST_INSERT_ID();
    </selectKey>
    insert into Author
      (id, username, password, email,bio, favourite_section)
    values
      (#{id}, #{username}, #{password}, #{email}, #{bio}, #{favouriteSection,jdbcType=VARCHAR})
  </insert>`




##3. 代码中使用

例如下面这个简单的例子:

	public void testFindById() {
			UserInfoDTO user = userInfoDAO.findById(36);
			Assert.assertNotEquals(null, user);
		}
