package com.vedeng.erp.trader.constant;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 系统判定-经营状态异常、系统判定-客户无联系人、系统判定-客户禁用、销售标记-公司转行、销售标记-公司注销、销售标记-服务型公司、销售标记-其他
 */
public enum InvalidReasonEnum {
    
    INVALID_REASON_MANAGE_STATE(1,"系统判定-经营状态异常"),
    INVALID_REASON_NO_CONTACT(2,"系统判定-客户无联系人"),
    INVALID_REASON_DISABLED(3,"系统判定-客户禁用"),
    INVALID_REASON_COMPANY_TRANSFER(4,"销售标记-公司转行"),
    INVALID_REASON_COMPANY_CANCEL(5,"销售标记-公司注销"),
    INVALID_REASON_SERVICE_COMPANY(6,"销售标记-服务型公司"),
    INVALID_REASON_OTHER(7,"销售标记-其他");
    private Integer type;
    
    private String reason;
    
    InvalidReasonEnum(Integer type,String reason){
        this.type = type;
        this.reason = reason;
    }
    public Integer getType() {
        return type;
    }
    public void setType(Integer type) {
        this.type = type;
    }
    
    public String getReason() {
        return reason;
    }
    
    public void setReason(String reason) {
        this.reason = reason;
    }
    
    public static List<String> getReasonList(){
        return Arrays.stream(InvalidReasonEnum.values()).map(InvalidReasonEnum::getReason).collect(Collectors.toList());
    }
    
    public static String getInvalidReason(List<Integer> types,String prefix){

        List<String> collect = types.stream().map(type -> {
            for (InvalidReasonEnum invalidReasonEnum : InvalidReasonEnum.values()) {
                if (Objects.equals(type, invalidReasonEnum.getType()) && invalidReasonEnum.getReason().contains(prefix)) {
                    return invalidReasonEnum.getReason().split("-")[1];
                }
            }
            return "";
        }).collect(Collectors.toList());
        // &符号分割
        if (CollUtil.isEmpty(collect)) {
            return "";
        }
        String collect1 = collect.stream().filter(StrUtil::isNotBlank).collect(Collectors.joining("&"));
        if (StrUtil.isNotBlank(collect1)){
            return prefix + "-" + collect1;
        }
        return "";
    }
}
