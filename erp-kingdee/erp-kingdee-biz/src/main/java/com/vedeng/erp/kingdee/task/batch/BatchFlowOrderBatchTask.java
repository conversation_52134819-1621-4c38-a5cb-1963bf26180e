package com.vedeng.erp.kingdee.task.batch;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.batch.job.BaseInfoCompensateBatchJob;
import com.vedeng.erp.kingdee.batch.job.FlowOrderBatchJob;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 流转单
 * @date 2025/1/21 13:07
 */
@JobHandler(value = "BatchFlowOrderBatchTask")
@Component
public class BatchFlowOrderBatchTask  extends AbstractJobHandler {

    @Autowired
    private JobLauncher jobLauncher;

    @Autowired
    private FlowOrderBatchJob flowOrderBatchJob;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        XxlJobLogger.log("==================流转单任务开始====================");
        JobParameters compensateJobParameters = new TaskBatchHandle().buildJobParameters(param);
        Job baseInfoCompensateFlowJob = flowOrderBatchJob.flowOrderJob();
        jobLauncher.run(baseInfoCompensateFlowJob, compensateJobParameters);
        XxlJobLogger.log("==================流转单任务结束====================");

        return SUCCESS;
    }
}
