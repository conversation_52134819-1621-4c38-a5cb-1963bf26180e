package com.vedeng.api.standard.adapter.express;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.vedeng.api.standard.adapter.express.dto.ExpressCreateRequest;
import com.vedeng.api.standard.adapter.express.dto.ExpressQueryRequest;
import com.vedeng.api.standard.adapter.express.dto.ExpressResponse;
import com.vedeng.api.standard.adapter.express.dto.ExpressSignRequest;
import com.vedeng.api.standard.core.AbstractServiceAdapter;
import com.vedeng.api.standard.core.ApiRequest;
import com.vedeng.api.standard.core.exception.ApiStandardException;
import com.vedeng.api.standard.template.BusinessTemplate;
import com.vedeng.api.standard.template.ParameterConfig;
import com.vedeng.api.standard.validation.ValidationContextKeys;
import com.vedeng.api.standard.validation.rules.AddExpressEnableRule;
import com.vedeng.api.standard.validation.rules.SignExpressEnableRule;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.SyncExpressDetailDto;
import com.vedeng.logistics.service.ExpressService;
import com.vedeng.order.model.vo.BuyorderVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Component("expressServiceAdapter")
public class ExpressServiceAdapter extends AbstractServiceAdapter {

    @Autowired
    private BusinessTemplate businessTemplate;
    
    @Override
    protected void registerOperationHandlers() {
        registerThrowingHandler("create", this::executeCreateOperation);
        registerThrowingHandler("query", this::executeQueryOperation);
        registerThrowingHandler("sign", this::executeSignOperation);
        registerThrowingHandler("signCheck", this::executeSignCheckOperation);
    }

    /**
     * 获取不需要身份认证的操作列表
     *
     * 采购单模块中，查询操作通常不需要身份认证，
     * 允许外部系统或匿名用户查询采购单信息
     *
     * @return 不需要认证的操作名称数组
     */
    @Override
    public String[] getNoAuthActions() {
        return new String[]{"query","signCheck"};
    }

    private Object executeQueryOperation(ApiRequest apiRequest) {
        ExpressQueryRequest expressRequest = new ExpressQueryRequest();
        BeanUtil.fillBeanWithMap(apiRequest.getData(), expressRequest, true);
        try {
            return businessTemplate.<ExpressQueryRequest, ExpressResponse>executeCreate(apiRequest)
                    .requestType(ExpressQueryRequest.class)
                    .controller("buyorderApiServiceImpl", "getExpressGoodsList")
                    .withoutHttpParameters(
                            ParameterConfig.of(String.class, expressRequest.getBuyOrderNo())
                    )
                    .execute();
        } catch (Exception e) {
            throw new RuntimeException("新增快递:失败: " + e.getMessage(), e);
        }
    }

    private Object executeCreateOperation(ApiRequest apiRequest) {
        ExpressCreateRequest expressRequest = new ExpressCreateRequest();
        logger.info("apiRequest", JSONUtil.toJsonStr(apiRequest.getData()));
        BeanUtil.fillBeanWithMap(apiRequest.getData(), expressRequest, true);

        try {
            return businessTemplate.<ExpressCreateRequest, ExpressResponse>executeCreate(apiRequest)
                    .requestType(ExpressCreateRequest.class)
                    .responseType(ExpressResponse.class)
                    .validationRules(AddExpressEnableRule.class)
                    .controller("buyorderController", "saveAddExpress")
                    .withIdempotencyHandling("EXPRESS_CREATE")
                    .withoutHttpParameters(
                            ParameterConfig.fromValidationContext(ValidationContextKeys.AMOUNT, BigDecimal.class),
                            ParameterConfig.fromValidationContext(ValidationContextKeys.EXPRESS, Express.class),
                            ParameterConfig.fromValidationContext(ValidationContextKeys.DELIVERY_TIMES, String.class),
                            ParameterConfig.fromValidationContext(ValidationContextKeys.ID_NUM_PRICE, String.class),
                            ParameterConfig.fromValidationContext(ValidationContextKeys.ID_SEND_N_SENDED_N_SUM_N, String.class),
                            ParameterConfig.fromValidationContext(ValidationContextKeys.BUYORDER_VO, BuyorderVo.class)

                    )
                    .execute();
        } catch (Exception e) {
            throw ApiStandardException.serviceExecutionError("新增快递失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行快递签收操作
     * 根据快递单号和采购单号查询expressId，拼接&515，调用WarehousesOutController.editExpressStatus
     */
    private Object executeSignOperation(ApiRequest apiRequest) {
        ExpressSignRequest signRequest = new ExpressSignRequest();
        BeanUtil.fillBeanWithMap(apiRequest.getData(), signRequest, true);

        String requestId = apiRequest.getRequestId();
        String logisticsNo = signRequest.getLogisticsNo();
        String buyOrderNo = signRequest.getBuyOrderNo();

        logger.info("开始执行快递签收操作，requestId: {}, 快递单号: {}, 采购单号: {}",
                   requestId, logisticsNo, buyOrderNo);

        try {
            Object result = businessTemplate.<ExpressSignRequest, Object>executeCreate(apiRequest)
                    .requestType(ExpressSignRequest.class)
                    .controller("warehousesOutController", "editExpressStatus")
                    .validationRules(SignExpressEnableRule.class)
                    .withoutHttpParameters(
                            ParameterConfig.fromValidationContext(ValidationContextKeys.EXPRESS_IDS, String.class),
                            ParameterConfig.fromValidationContext(ValidationContextKeys.BEFORE_PARAMS, String.class)
                    )
                    .execute();

            logger.info("快递签收操作完成，requestId: {}, 快递单号: {}, 采购单号: {}, 结果: {}",
                       requestId, logisticsNo, buyOrderNo, result != null ? "成功" : "失败");

            return result;
        } catch (Exception e) {
            logger.info("快递签收操作异常，requestId: {}, 快递单号: {}, 采购单号: {}",
                        requestId, logisticsNo, buyOrderNo, e);
            throw ApiStandardException.serviceExecutionError("快递签收失败: " + e.getMessage(), e);
        }
    }

    @Autowired
    private ExpressService expressService;
    
    /**
     * 执行快递签收状态检查操作
     * 检查快递是否已经签收
     */
    private Object executeSignCheckOperation(ApiRequest apiRequest) {
        ExpressSignRequest signRequest = new ExpressSignRequest();
        BeanUtil.fillBeanWithMap(apiRequest.getData(), signRequest, true);

        String requestId = apiRequest.getRequestId();
        String logisticsNo = signRequest.getLogisticsNo();
        String buyOrderNo = signRequest.getBuyOrderNo();

        logger.info("开始检查快递签收状态，requestId: {}, 快递单号: {}, 采购单号: {}",
                   requestId, logisticsNo, buyOrderNo);

        try {
            SyncExpressDetailDto detailDto = expressService.getDetailByNo(buyOrderNo, logisticsNo);

            if (detailDto != null) {
                logger.info("快递签收状态检查完成，requestId: {}, 快递单号: {}, 采购单号: {}, expressId: {}, 签收状态: {}",
                           requestId, logisticsNo, buyOrderNo, detailDto.getExpressId(), detailDto.getArrivalStatus());
            } else {
                logger.warn("未找到快递信息，requestId: {}, 快递单号: {}, 采购单号: {}",
                           requestId, logisticsNo, buyOrderNo);
            }

            return detailDto;
        } catch (Exception e) {
            logger.info("快递签收状态检查异常，requestId: {}, 快递单号: {}, 采购单号: {}",
                        requestId, logisticsNo, buyOrderNo, e);
            throw ApiStandardException.serviceExecutionError("快递签收检查失败: " + e.getMessage(), e);
        }
    }


    @Override
    public String getModuleName() {
        return "express";
    }
}
