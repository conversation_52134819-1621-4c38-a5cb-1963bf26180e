package com.vedeng.api.standard.auth;

import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.system.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;

/**
 * 用户查询服务
 * 专注于动态真实用户查询
 *
 * <AUTHOR>
 * @version 4.0
 * @since 2024-12-20
 */
@Service
public class UserLookupService {

    private static final Logger logger = LoggerFactory.getLogger(UserLookupService.class);

    @Autowired
    private UserMapper userMapper;

    /**
     * 根据用户标识查询用户信息
     *
     * @param userId   用户ID
     * @param userName 用户名
     * @return 用户对象，如果找不到返回null
     */
    public User findUser(String userId, String userName) {
        try {
            // 优先使用用户ID查询
            if (!StringUtils.isEmpty(userId)) {
                Integer userIdLong = Integer.parseInt(userId);
                User user = findUserById(userIdLong);
                if (user != null) {
                    logger.debug("通过用户ID找到用户: {} -> {}", userId, user.getUsername());
                    return user;
                }
            }

            // 使用用户名查询
            if (!StringUtils.isEmpty(userName)) {
                User user = findUserByName(userName);
                if (user != null) {
                    logger.debug("通过用户名找到用户: {} -> {}", userName, user.getUsername());
                    return user;
                }
            }

            logger.warn("未找到用户信息: userId={}, userName={}", userId, userName);
            return null;

        } catch (Exception e) {
            logger.error("查询用户信息异常: userId={}, userName={}", userId, userName, e);
            return null;
        }
    }

    /**
     * 根据用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象
     */
    public User findUserById(Integer userId) {
        if (userId == null || userId <= 0) {
            return null;
        }

        try {
            return userMapper.getUserByUserId(userId);
        } catch (Exception e) {
            logger.error("根据用户ID查询用户异常: {}", userId, e);
            return null;
        }
    }

    /**
     * 根据用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象
     */
    public User findUserByName(String userName) {
        if (StringUtils.isEmpty(userName)) {
            return null;
        }

        try {
            return userMapper.getByUsername(userName, 1);
        } catch (Exception e) {
            logger.error("根据用户名查询用户异常: {}", userName, e);
            return null;
        }
    }

    /**
     * 验证用户信息是否完整有效
     *
     * @param user 用户对象
     * @return true-有效，false-无效
     */
    public boolean isValidUser(User user) {
        if (user == null) {
            return false;
        }

        // 检查必要字段
        if (user.getUserId() == null || user.getUserId() <= 0) {
            logger.warn("用户ID无效: {}", user.getUserId());
            return false;
        }

        if (StringUtils.isEmpty(user.getUsername())) {
            logger.warn("用户名为空: userId={}", user.getUserId());
            return false;
        }

        // 可以添加更多验证逻辑，如用户状态检查等
        // if (user.getStatus() != 1) {
        //     logger.warn("用户已禁用: userId={}, status={}", user.getUserId(), user.getStatus());
        //     return false;
        // }

        return true;
    }

    /**
     * 补全用户信息
     * 确保用户对象包含业务逻辑所需的所有字段
     *
     * @param user 用户对象
     * @return 补全后的用户对象
     */
    public User completeUserInfo(User user) {
        if (user == null) {
            return null;
        }

        //// 设置默认值，确保业务逻辑不会因为缺少字段而出错
        //if (user.getStatus() == null) {
        //    user.setStatus(1); // 默认启用状态
        //}
        //
        //if (user.getDelFlag() == null) {
        //    user.setDelFlag(0); // 默认未删除
        //}

        if (StringUtils.isEmpty(user.getRealName())) {
            user.setRealName(user.getUsername()); // 如果没有真实姓名，使用用户名
        }

        return user;
    }



}
