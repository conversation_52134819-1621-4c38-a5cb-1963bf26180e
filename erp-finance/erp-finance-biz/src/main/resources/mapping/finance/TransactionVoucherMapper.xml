<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.finance.mapper.TransactionVoucherMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.TransactionVoucherEntity">
    <!--@mbg.generated-->
    <!--@Table T_TRANSACTION_VOUCHER-->
    <id column="TRANSACTION_VOUCHER_ID" jdbcType="BIGINT" property="transactionVoucherId" />
    <result column="BILL_NO" jdbcType="VARCHAR" property="billNo" />
    <result column="VOUCHER_NO" jdbcType="VARCHAR" property="voucherNo" />
    <result column="TRANSACTION_TYPE" jdbcType="INTEGER" property="transactionType" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="VOUCHER_DATE" jdbcType="VARCHAR" property="voucherDate" />
    <result column="RELATE_ID" jdbcType="INTEGER" property="relateId" />
    <result column="SOURCE" jdbcType="TINYINT" property="source" />
    <result column="VOUCHER_URL" jdbcType="VARCHAR" property="voucherUrl" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    TRANSACTION_VOUCHER_ID, BILL_NO, VOUCHER_NO, TRANSACTION_TYPE, IS_DELETE, ADD_TIME,
    CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, MOD_TIME, VOUCHER_DATE, RELATE_ID,
    `SOURCE`, VOUCHER_URL
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_TRANSACTION_VOUCHER
    where TRANSACTION_VOUCHER_ID = #{transactionVoucherId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_TRANSACTION_VOUCHER
    where TRANSACTION_VOUCHER_ID = #{transactionVoucherId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="TRANSACTION_VOUCHER_ID" keyProperty="transactionVoucherId" parameterType="com.vedeng.erp.finance.domain.entity.TransactionVoucherEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRANSACTION_VOUCHER (BILL_NO, VOUCHER_NO, TRANSACTION_TYPE,
      IS_DELETE, ADD_TIME, CREATOR,
      CREATOR_NAME, UPDATER, UPDATER_NAME,
      MOD_TIME, VOUCHER_DATE, RELATE_ID,
      `SOURCE`, VOUCHER_URL)
    values (#{billNo,jdbcType=VARCHAR}, #{voucherNo,jdbcType=VARCHAR}, #{transactionType,jdbcType=INTEGER},
      #{isDelete,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER},
      #{creatorName,jdbcType=VARCHAR}, #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR},
      #{modTime,jdbcType=TIMESTAMP}, #{voucherDate,jdbcType=VARCHAR}, #{relateId,jdbcType=INTEGER},
      #{source,jdbcType=TINYINT}, #{voucherUrl,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="TRANSACTION_VOUCHER_ID" keyProperty="transactionVoucherId" parameterType="com.vedeng.erp.finance.domain.entity.TransactionVoucherEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRANSACTION_VOUCHER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="billNo != null and billNo != ''">
        BILL_NO,
      </if>
      <if test="voucherNo != null and voucherNo != ''">
        VOUCHER_NO,
      </if>
      <if test="transactionType != null">
        TRANSACTION_TYPE,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="voucherDate != null and voucherDate != ''">
        VOUCHER_DATE,
      </if>
      <if test="relateId != null">
        RELATE_ID,
      </if>
      <if test="source != null">
        `SOURCE`,
      </if>
      <if test="voucherUrl != null and voucherUrl != ''">
        VOUCHER_URL,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="billNo != null and billNo != ''">
        #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="voucherNo != null and voucherNo != ''">
        #{voucherNo,jdbcType=VARCHAR},
      </if>
      <if test="transactionType != null">
        #{transactionType,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="voucherDate != null and voucherDate != ''">
        #{voucherDate,jdbcType=VARCHAR},
      </if>
      <if test="relateId != null">
        #{relateId,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
      <if test="voucherUrl != null and voucherUrl != ''">
        #{voucherUrl,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.finance.domain.entity.TransactionVoucherEntity">
    <!--@mbg.generated-->
    update T_TRANSACTION_VOUCHER
    <set>
      <if test="billNo != null and billNo != ''">
        BILL_NO = #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="voucherNo != null and voucherNo != ''">
        VOUCHER_NO = #{voucherNo,jdbcType=VARCHAR},
      </if>
      <if test="transactionType != null">
        TRANSACTION_TYPE = #{transactionType,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="voucherDate != null and voucherDate != ''">
        VOUCHER_DATE = #{voucherDate,jdbcType=VARCHAR},
      </if>
      <if test="relateId != null">
        RELATE_ID = #{relateId,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        `SOURCE` = #{source,jdbcType=TINYINT},
      </if>
      <if test="voucherUrl != null and voucherUrl != ''">
        VOUCHER_URL = #{voucherUrl,jdbcType=VARCHAR},
      </if>
    </set>
    where TRANSACTION_VOUCHER_ID = #{transactionVoucherId,jdbcType=BIGINT}
  </update>
  <update id="updateFileUrlByVoucherUrlAndVoucherNo" parameterType="com.vedeng.erp.finance.domain.entity.TransactionVoucherEntity">
    <!--@mbg.generated-->
    update T_TRANSACTION_VOUCHER SET
        MOD_TIME = NOW(),
        VOUCHER_URL = #{voucherUrl,jdbcType=VARCHAR}
    where VOUCHER_NO = #{voucherNo,jdbcType=VARCHAR} AND VOUCHER_DATE = #{voucherDate,jdbcType=VARCHAR}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.finance.domain.entity.TransactionVoucherEntity">
    <!--@mbg.generated-->
    update T_TRANSACTION_VOUCHER
    set BILL_NO = #{billNo,jdbcType=VARCHAR},
      VOUCHER_NO = #{voucherNo,jdbcType=VARCHAR},
      TRANSACTION_TYPE = #{transactionType,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      VOUCHER_DATE = #{voucherDate,jdbcType=VARCHAR},
      RELATE_ID = #{relateId,jdbcType=INTEGER},
      `SOURCE` = #{source,jdbcType=TINYINT},
      VOUCHER_URL = #{voucherUrl,jdbcType=VARCHAR}
    where TRANSACTION_VOUCHER_ID = #{transactionVoucherId,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_TRANSACTION_VOUCHER
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="BILL_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRANSACTION_VOUCHER_ID = #{item.transactionVoucherId,jdbcType=BIGINT} then #{item.billNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="VOUCHER_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRANSACTION_VOUCHER_ID = #{item.transactionVoucherId,jdbcType=BIGINT} then #{item.voucherNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TRANSACTION_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRANSACTION_VOUCHER_ID = #{item.transactionVoucherId,jdbcType=BIGINT} then #{item.transactionType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRANSACTION_VOUCHER_ID = #{item.transactionVoucherId,jdbcType=BIGINT} then #{item.isDelete,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRANSACTION_VOUCHER_ID = #{item.transactionVoucherId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRANSACTION_VOUCHER_ID = #{item.transactionVoucherId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRANSACTION_VOUCHER_ID = #{item.transactionVoucherId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRANSACTION_VOUCHER_ID = #{item.transactionVoucherId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRANSACTION_VOUCHER_ID = #{item.transactionVoucherId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRANSACTION_VOUCHER_ID = #{item.transactionVoucherId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="VOUCHER_DATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRANSACTION_VOUCHER_ID = #{item.transactionVoucherId,jdbcType=BIGINT} then #{item.voucherDate,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="RELATE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRANSACTION_VOUCHER_ID = #{item.transactionVoucherId,jdbcType=BIGINT} then #{item.relateId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="`SOURCE` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRANSACTION_VOUCHER_ID = #{item.transactionVoucherId,jdbcType=BIGINT} then #{item.source,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="VOUCHER_URL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRANSACTION_VOUCHER_ID = #{item.transactionVoucherId,jdbcType=BIGINT} then #{item.voucherUrl,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where TRANSACTION_VOUCHER_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.transactionVoucherId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_TRANSACTION_VOUCHER
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="BILL_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.billNo != null">
            when TRANSACTION_VOUCHER_ID = #{item.transactionVoucherId,jdbcType=BIGINT} then #{item.billNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="VOUCHER_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.voucherNo != null">
            when TRANSACTION_VOUCHER_ID = #{item.transactionVoucherId,jdbcType=BIGINT} then #{item.voucherNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRANSACTION_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.transactionType != null">
            when TRANSACTION_VOUCHER_ID = #{item.transactionVoucherId,jdbcType=BIGINT} then #{item.transactionType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDelete != null">
            when TRANSACTION_VOUCHER_ID = #{item.transactionVoucherId,jdbcType=BIGINT} then #{item.isDelete,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when TRANSACTION_VOUCHER_ID = #{item.transactionVoucherId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when TRANSACTION_VOUCHER_ID = #{item.transactionVoucherId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when TRANSACTION_VOUCHER_ID = #{item.transactionVoucherId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when TRANSACTION_VOUCHER_ID = #{item.transactionVoucherId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when TRANSACTION_VOUCHER_ID = #{item.transactionVoucherId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when TRANSACTION_VOUCHER_ID = #{item.transactionVoucherId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="VOUCHER_DATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.voucherDate != null">
            when TRANSACTION_VOUCHER_ID = #{item.transactionVoucherId,jdbcType=BIGINT} then #{item.voucherDate,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="RELATE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.relateId != null">
            when TRANSACTION_VOUCHER_ID = #{item.transactionVoucherId,jdbcType=BIGINT} then #{item.relateId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="`SOURCE` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.source != null">
            when TRANSACTION_VOUCHER_ID = #{item.transactionVoucherId,jdbcType=BIGINT} then #{item.source,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="VOUCHER_URL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.voucherUrl != null">
            when TRANSACTION_VOUCHER_ID = #{item.transactionVoucherId,jdbcType=BIGINT} then #{item.voucherUrl,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where TRANSACTION_VOUCHER_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.transactionVoucherId,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="TRANSACTION_VOUCHER_ID" keyProperty="transactionVoucherId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRANSACTION_VOUCHER
    (BILL_NO, VOUCHER_NO, TRANSACTION_TYPE, IS_DELETE, ADD_TIME, CREATOR, CREATOR_NAME,
      UPDATER, UPDATER_NAME, MOD_TIME, VOUCHER_DATE, RELATE_ID, `SOURCE`, VOUCHER_URL
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.billNo,jdbcType=VARCHAR}, #{item.voucherNo,jdbcType=VARCHAR}, #{item.transactionType,jdbcType=INTEGER},
        #{item.isDelete,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER},
        #{item.creatorName,jdbcType=VARCHAR}, #{item.updater,jdbcType=INTEGER}, #{item.updaterName,jdbcType=VARCHAR},
        #{item.modTime,jdbcType=TIMESTAMP}, #{item.voucherDate,jdbcType=VARCHAR}, #{item.relateId,jdbcType=INTEGER},
        #{item.source,jdbcType=TINYINT}, #{item.voucherUrl,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="TRANSACTION_VOUCHER_ID" keyProperty="transactionVoucherId" parameterType="com.vedeng.erp.finance.domain.entity.TransactionVoucherEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRANSACTION_VOUCHER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="transactionVoucherId != null">
        TRANSACTION_VOUCHER_ID,
      </if>
      BILL_NO,
      VOUCHER_NO,
      TRANSACTION_TYPE,
      IS_DELETE,
      ADD_TIME,
      CREATOR,
      CREATOR_NAME,
      UPDATER,
      UPDATER_NAME,
      MOD_TIME,
      VOUCHER_DATE,
      RELATE_ID,
      `SOURCE`,
      VOUCHER_URL,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="transactionVoucherId != null">
        #{transactionVoucherId,jdbcType=BIGINT},
      </if>
      #{billNo,jdbcType=VARCHAR},
      #{voucherNo,jdbcType=VARCHAR},
      #{transactionType,jdbcType=INTEGER},
      #{isDelete,jdbcType=INTEGER},
      #{addTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=INTEGER},
      #{creatorName,jdbcType=VARCHAR},
      #{updater,jdbcType=INTEGER},
      #{updaterName,jdbcType=VARCHAR},
      #{modTime,jdbcType=TIMESTAMP},
      #{voucherDate,jdbcType=VARCHAR},
      #{relateId,jdbcType=INTEGER},
      #{source,jdbcType=TINYINT},
      #{voucherUrl,jdbcType=VARCHAR},
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="transactionVoucherId != null">
        TRANSACTION_VOUCHER_ID = #{transactionVoucherId,jdbcType=BIGINT},
      </if>
      BILL_NO = #{billNo,jdbcType=VARCHAR},
      VOUCHER_NO = #{voucherNo,jdbcType=VARCHAR},
      TRANSACTION_TYPE = #{transactionType,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      VOUCHER_DATE = #{voucherDate,jdbcType=VARCHAR},
      RELATE_ID = #{relateId,jdbcType=INTEGER},
      `SOURCE` = #{source,jdbcType=TINYINT},
      VOUCHER_URL = #{voucherUrl,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="TRANSACTION_VOUCHER_ID" keyProperty="transactionVoucherId" parameterType="com.vedeng.erp.finance.domain.entity.TransactionVoucherEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRANSACTION_VOUCHER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="transactionVoucherId != null">
        TRANSACTION_VOUCHER_ID,
      </if>
      <if test="billNo != null and billNo != ''">
        BILL_NO,
      </if>
      <if test="voucherNo != null and voucherNo != ''">
        VOUCHER_NO,
      </if>
      <if test="transactionType != null">
        TRANSACTION_TYPE,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="voucherDate != null and voucherDate != ''">
        VOUCHER_DATE,
      </if>
      <if test="relateId != null">
        RELATE_ID,
      </if>
      <if test="source != null">
        `SOURCE`,
      </if>
      <if test="voucherUrl != null and voucherUrl != ''">
        VOUCHER_URL,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="transactionVoucherId != null">
        #{transactionVoucherId,jdbcType=BIGINT},
      </if>
      <if test="billNo != null and billNo != ''">
        #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="voucherNo != null and voucherNo != ''">
        #{voucherNo,jdbcType=VARCHAR},
      </if>
      <if test="transactionType != null">
        #{transactionType,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="voucherDate != null and voucherDate != ''">
        #{voucherDate,jdbcType=VARCHAR},
      </if>
      <if test="relateId != null">
        #{relateId,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
      <if test="voucherUrl != null and voucherUrl != ''">
        #{voucherUrl,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="transactionVoucherId != null">
        TRANSACTION_VOUCHER_ID = #{transactionVoucherId,jdbcType=BIGINT},
      </if>
      <if test="billNo != null and billNo != ''">
        BILL_NO = #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="voucherNo != null and voucherNo != ''">
        VOUCHER_NO = #{voucherNo,jdbcType=VARCHAR},
      </if>
      <if test="transactionType != null">
        TRANSACTION_TYPE = #{transactionType,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="voucherDate != null and voucherDate != ''">
        VOUCHER_DATE = #{voucherDate,jdbcType=VARCHAR},
      </if>
      <if test="relateId != null">
        RELATE_ID = #{relateId,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        `SOURCE` = #{source,jdbcType=TINYINT},
      </if>
      <if test="voucherUrl != null and voucherUrl != ''">
        VOUCHER_URL = #{voucherUrl,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="findByBillNo" resultType="com.vedeng.erp.finance.domain.entity.TransactionVoucherEntity">
    select
    <include refid="Base_Column_List" />
    from T_TRANSACTION_VOUCHER
    where BILL_NO = #{billNo,jdbcType=VARCHAR}
  </select>
  <select id="getByVoucherDateAndNo" resultMap="BaseResultMap">
      select *
      from T_TRANSACTION_VOUCHER
      where VOUCHER_DATE = #{voucherDate,jdbcType=VARCHAR}
        and VOUCHER_NO = #{voucherNo,jdbcType=VARCHAR}
      LIMIT 1
  </select>
  <select id="getTranFlowByKingDeePayBill" resultType="java.lang.String">
    select
      F_QZOK_LSH
    from KING_DEE_PAY_BILL
    where F_BILL_NO = #{billNo,jdbcType=VARCHAR}
      and IS_DELETE = 0
    order by KING_DEE_PAY_BILL_ID desc
    limit 1
  </select>

  <select id="getTranFlowByKingDeeReceiveBill" resultType="java.lang.String">
    select
      F_QZOK_LSH
    from KING_DEE_RECEIVE_BILL
    where F_BILL_NO = #{billNo,jdbcType=VARCHAR}
      and IS_DELETE = 0
    order by KING_DEE_RECEIVE_BILL_ID desc
    limit 1
  </select>

  <select id="getTranFlowByKingDeePayRefundBill" resultType="java.lang.String">
    select F_QZOK_LSH
    from KING_DEE_PAY_REFUND_BILL
    where F_BILL_NO = #{billNo,jdbcType=VARCHAR}
    order by ID desc
    limit 1
  </select>

  <select id="getTranFlowByKingDeeReceiveRefundBill" resultType="java.lang.String">
    select F_QZOK_LSH
    from KING_DEE_RECEIVE_REFUND_BILL
    where F_BILL_NO = #{billNo,jdbcType=VARCHAR}
    order by KING_DEE_RECEIVE_REFUND_BILL_ID desc
    limit 1;
  </select>

<!--auto generated by MybatisCodeHelper on 2024-01-12-->
  <select id="findByRelateIdAndSource" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_TRANSACTION_VOUCHER
    where
    IS_DELETE = 0
    and RELATE_ID=#{relateId,jdbcType=INTEGER}
    and `SOURCE`=#{source,jdbcType=TINYINT}
  </select>
</mapper>