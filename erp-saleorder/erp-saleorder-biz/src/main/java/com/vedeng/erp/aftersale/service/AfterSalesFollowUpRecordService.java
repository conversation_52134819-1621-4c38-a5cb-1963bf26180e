package com.vedeng.erp.aftersale.service;

import com.vedeng.orderstream.aftersales.model.AfterSalesFollowUpRecord;

import java.util.List;

/**
 * <AUTHOR> simgo.wang
 * @date : 2021/10/13 20:21
 * @desc :
 */
public interface AfterSalesFollowUpRecordService {

    /**
     * 新增跟进记录
     * @param afterSalesFollowUpRecord
     * @return
     */
    int insert (AfterSalesFollowUpRecord afterSalesFollowUpRecord);

    /**
     * 查询跟进记录
     * @param afterSalesId
     * @return
     */
    List<AfterSalesFollowUpRecord> selectByAfterSalesId(Integer afterSalesId);

    /**
     * 影响范围
     * 1、/addFollowUpRecord 新增跟进记录
     */
    /**
     * 根据ID查询跟进记录
     */
    AfterSalesFollowUpRecord getAfterSalesFollowUpRecordById(Integer recordId);

    /**
     * 影响范围
     * 1、/saveFollowUpRecord 保存新增售后跟进记录页面
     */

    /**
     * 根据ID更新跟进记录
     * @param afterSalesFollowUpRecord
     */
    void updateAfterSalesFollowUpRecordSelectById(AfterSalesFollowUpRecord afterSalesFollowUpRecord);
}
