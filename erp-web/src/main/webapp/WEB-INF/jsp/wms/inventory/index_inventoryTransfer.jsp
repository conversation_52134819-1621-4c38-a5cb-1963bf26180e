<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="库存转移单" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript" src='<%= basePath %>static/js/goods/brand/index.js?rnd=${resourceVersionKey}'></script>
<div class="content">
    <div class="searchfunc">

        <form method="post" id="search" action="<%=basePath%>wms/inventory/inventoryTransfer.do">
            <ul>
                <li>
                    <label class="infor_name">库存转移单号：</label>
                    <input type="text" class="input-middle" name="inventoryTransferNo" id="inventoryTransferNo"
                           value="${inventoryTransferDto.inventoryTransferNo}">
                </li>

                <li>
                    <label class="infor_name">单据类型：</label>
                    <select name="type" class="input-middle f_left">
                        <option value="0" <c:if test="${inventoryTransferDto.type eq 0}">selected="selected"</c:if>>全部
                        </option>

                        <option value="1" <c:if test="${inventoryTransferDto.type eq 1}">selected="selected"</c:if>>
                            仓库库存转移单
                        </option>
                        <option value="2" <c:if test="${inventoryTransferDto.type eq 2}">selected="selected"</c:if>>
                            近效期转移单
                        </option>
                        <option value="3" <c:if test="${inventoryTransferDto.type eq 3}">selected="selected"</c:if>>
                            超近效期转移单
                        </option>
                        <option value="4" <c:if test="${inventoryTransferDto.type eq 4}">selected="selected"</c:if>>
                            秒杀活动移仓单
                        </option>
                        <option value="5" <c:if test="${inventoryTransferDto.type eq 5}">selected="selected"</c:if>>
                            等通知发货移库单
                        </option>
                        <option value="6" <c:if test="${inventoryTransferDto.type eq 6}">selected="selected"</c:if>>
                            多地址发货移库单
                        </option>
                    </select>
                </li>

                <li>
                    <label class="infor_name">转移单状态：</label>
                    <select name="status" class="input-middle f_left">
                        <option value="0" <c:if test="${inventoryTransferDto.status eq 0}">selected="selected"</c:if>>全部
                        </option>
                        <option value="1" <c:if test="${inventoryTransferDto.status eq 1}">selected="selected"</c:if>>
                            进行中
                        </option>
                        <option value="2" <c:if test="${inventoryTransferDto.status eq 2}">selected="selected"</c:if>>
                            已完结
                        </option>
                    </select>
                </li>

                <li>
                    <label class="infor_name">关联单号：</label>
                    <input type="text" class="input-middle" name="orderNo" id="orderNo"
                           value="${inventoryTransferDto.orderNo}">
                </li>

                <li>
                    <label class="infor_name">创建时间：</label>
                    <input class="Wdate f_left input-smaller96 mr5" type="text" placeholder="请选择日期"
                           onClick="WdatePicker()" autocomplete="off"
                           onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'searchEndTime\')}'})"
                           name="searchBeginTime" id="searchBeginTime"
                           value='<date:date value ="${inventoryTransferDto.searchBeginTime}" format="yyyy-MM-dd" />'>
                    <div class="gang">-</div>
                    <input class="Wdate f_left input-smaller96" type="text" placeholder="请选择日期" onClick="WdatePicker()"
                           onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'searchBeginTime\')}'})"
                           name="searchEndTime" id="searchEndTime" autocomplete="off"
                           value='<date:date value ="${inventoryTransferDto.searchEndTime}" format="yyyy-MM-dd" />'>
                </li>
            </ul>
            <div class="tcenter">
                <span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();"
                      id="searchSpan">搜索</span>
                <span class="bt-small bg-light-blue bt-bg-style" onclick="reset();">重置</span>
            </div>
        </form>
    </div>
    <div class='normal-list-page list-page'>
        <table
                class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr class="sort">
                <th class="wid5">库存转移单号</th>
                <th class="wid5">关联单号</th>
                <th class="wid6">单据类型</th>
                <th class="wid10">创建时间</th>
                <th class="wid17">转移单状态</th>
                <th class="wid6">目标逻辑仓</th>
            </tr>
            </thead>
            <tbody class="brand">
            <c:if test="${not empty inventoryTransferVos}">
                <c:forEach items="${inventoryTransferVos }" var="list" varStatus="status">
                    <tr>
                        <td>
                            <a href="javascript:void(0)"
                               onclick="viewInventoryTransferDetail('${list.inventoryTransferId}')">${list.inventoryTransferNo}</a>
                        </td>
                        <td>
                            <a href="javascript:void(0)"
                               onclick="viewOrderInfo(${list.orderId})">${list.orderNo}</a>
                        </td>
                        <td>
                                ${list.typeStr}
                        </td>
                        <td><date:date value="${list.addTime}"/></td>
                        <td>
                            <c:if test="${list.status eq 1}">进行中</c:if>
                            <c:if test="${list.status eq 2}">已完结</c:if>
                        </td>
                        <td>${list.toWarehouseStr}</td>
                    </tr>
                </c:forEach>
            </c:if>
            </tbody>
        </table>
        <c:if test="${empty inventoryTransferVos}">
            <!-- 查询无结果弹出 -->
            <div class="noresult">查询无结果！请尝试使用其他搜索条件。</div>
        </c:if>
        <tags:page page="${page}"/>
    </div>
</div>
<script>
    /**
     * 查看库存转移单详情
     * @param inventoryTransferId 库存转移单ID
     */
    function viewInventoryTransferDetail(inventoryTransferId) {
        $.ajax({
            url: '/wms/inventory/inventoryTransferDetail.do?inventoryTransferId=' + inventoryTransferId,
            type: 'get',
            dataType: "html",
            success: function (res) {
                layer.open({
                    type: 1,
                    shade: 0.1,
                    area: ['800', '600px'],
                    title: '库存转移单',
                    content: res,
                    success: function (layero, index) {

                    }
                });
            },
            error: function (data) {
                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        })
    }

    /**
     * 查看订单信息
     * @param orderId 订单ID
     */
    function viewOrderInfo(orderId) {
        layer.alert('开发中。。')
    }
</script>
<%@ include file="../../common/footer.jsp" %>
