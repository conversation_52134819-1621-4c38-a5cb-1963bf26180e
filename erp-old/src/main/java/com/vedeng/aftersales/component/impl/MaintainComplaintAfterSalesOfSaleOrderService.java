package com.vedeng.aftersales.component.impl;

import com.vedeng.aftersales.component.AbstractAfterSalesOrderServiceOfSaleOrder;
import com.vedeng.aftersales.component.dto.AbstractAfterSaleOrderDto;
import com.vedeng.aftersales.component.dto.AfterSaleOrderAddDto;
import com.vedeng.aftersales.component.dto.AfterSaleOrderCloseDto;
import com.vedeng.aftersales.component.exception.AfterSaleCloseException;
import com.vedeng.aftersales.component.exception.AfterSaleDetailException;
import com.vedeng.aftersales.component.exception.AfterSaleValidateException;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesDetail;
import org.springframework.stereotype.Service;

@Service
public class MaintainComplaintAfterSalesOfSaleOrderService extends AbstractAfterSalesOrderServiceOfSaleOrder {
    @Override
    protected void customeAddDealOfSaleOrder(Object adddDto) throws Exception {

    }

    @Override
    protected <T extends AbstractAfterSaleOrderDto> void customerAddAfterSaleDetail(AfterSalesDetail afterSalesDetail, T afterSaleOrderDto) throws AfterSaleDetailException {

    }

    @Override
    protected void customerAddAfterSaleGood(AfterSaleOrderAddDto afterSaleOrderAddDto) throws Exception {

    }

    @Override
    protected void customerPropertySet(AfterSaleOrderAddDto afterSaleOrderAddDto, AfterSales afterSales) {

    }

    @Override
    protected void customerAfterSaleOrderClose(AfterSaleOrderCloseDto afterSaleOrderCloseDto) throws AfterSaleCloseException {

    }

    @Override
    protected void customerAfterSaleOrderCreateValidate(Object afterSaleOrderAddDto) throws AfterSaleValidateException {

    }

    @Override
    protected void customerAfterSaleOrderModifyValidate(Object modifyDto) throws AfterSaleValidateException {

    }
}
