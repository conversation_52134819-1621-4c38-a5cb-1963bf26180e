package com.vedeng.infrastructure.bank.api.common;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import java.io.IOException;
import java.io.StringReader;
import java.io.StringWriter;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: Xml转换类
 * @date 2024/10/11 14:57
 */
public class XmlConverter {

    // 缓存 JAXBContext 实例，提升性能
    private static final ConcurrentHashMap<Class<?>, JAXBContext> JAXB_CONTEXT_CACHE = new ConcurrentHashMap<>();
    private static final String DEFAULT_ENCODING = "UTF-8";

    /**
     * 获取或创建 JAXBContext
     *
     * @param clazz 要创建 JAXBContext 的类
     * @return JAXBContext 实例
     */
    private static JAXBContext getJAXBContext(Class<?> clazz) {
        return JAXB_CONTEXT_CACHE.computeIfAbsent(clazz, k -> {
            try {
                return JAXBContext.newInstance(k);
            } catch (JAXBException e) {
                throw new RuntimeException("Failed to create JAXBContext for class: " + k, e);
            }
        });
    }

    /**
     * 将任意对象转换为 XML 字符串
     *
     * @param object   要转换的对象
     * @param encoding 编码格式，例如 "UTF-8", "GB2312"
     * @param <T>      对象类型
     * @return XML 字符串
     * @throws JAXBException 如果转换失败
     */
    public static <T> String convertToXml(T object, String encoding) throws JAXBException, IOException {
        if (object == null) {
            throw new IllegalArgumentException("Object to convert cannot be null.");
        }

        JAXBContext context = getJAXBContext(object.getClass());
        Marshaller marshaller = context.createMarshaller();

        // 设置格式化输出和编码
        marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, Boolean.TRUE);
        marshaller.setProperty(Marshaller.JAXB_ENCODING, encoding);

        try (StringWriter sw = new StringWriter()) {
            marshaller.marshal(object, sw);
            return sw.toString();
        }
    }

    /**
     * 将任意对象转换为 XML 字符串，使用默认编码 "UTF-8"
     *
     * @param object 要转换的对象
     * @param <T>    对象类型
     * @return XML 字符串
     * @throws JAXBException 如果转换失败
     */
    public static <T> String convertToXml(T object) throws JAXBException, IOException {
        return convertToXml(object, DEFAULT_ENCODING);
    }

    /**
     * 将 XML 字符串转换为对象
     *
     * @param xml   XML 字符串
     * @param clazz 目标对象的类
     * @param <T>   目标对象类型
     * @return 转换后的对象
     * @throws JAXBException 如果转换失败
     */
    public static <T> T convertToObject(String xml, Class<T> clazz) throws JAXBException {
        if (xml == null || xml.isEmpty()) {
            throw new IllegalArgumentException("XML string to convert cannot be null or empty.");
        }

        JAXBContext context = getJAXBContext(clazz);
        Unmarshaller unmarshaller = context.createUnmarshaller();

        try (StringReader reader = new StringReader(xml)) {
            return (T) unmarshaller.unmarshal(reader);
        }
    }
}
