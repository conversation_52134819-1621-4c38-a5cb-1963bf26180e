package com.newtask.filemove;

import com.google.common.base.Strings;
import com.newtask.filemove.dto.FileMoveDto;
import com.vedeng.common.model.FileInfo;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.common.util.UrlUtils;
import com.vedeng.file.api.constants.TerminationType;
import com.vedeng.file.api.util.SignUtil;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RequestCallback;
import org.springframework.web.client.ResponseExtractor;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.IOException;
import java.io.InputStream;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Random;

/**
 * OSS上传服务
 */
@Service
public class OssUploadService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OssUploadService.class);

    @Autowired
    private RestTemplate restTemplate;

    /**
     * OSS地址
     */
    @Value("${oss_url}")
    private String ossUrl;

    /**
     * oss秘钥
     */
    @Value("${oss_key}")
    private String ossKey;

    /**
     * oss应用码
     */
    @Value("${oss_app_code}")
    private String ossAppCode;


    /**
     * oss文档路径
     */
    @Value("${oss_file_path}")
    private String ossFilePath;

    public FileInfo downloadFileAndSendFile2Oss(FileMoveDto pendingTask){

        String url = "http://" + pendingTask.getDomain() + pendingTask.getUri();

        RequestCallback requestCallback = request -> request.getHeaders().setAccept(Arrays.asList(MediaType.APPLICATION_OCTET_STREAM, MediaType.ALL));

        ResponseExtractor<FileInfo> responseExtractor = response -> {
            //发送到oss
            return sendFile2Oss(url,"http://"+ossUrl+ossFilePath,response.getBody());
        } ;

        return restTemplate.execute(url, HttpMethod.GET,requestCallback,responseExtractor);
    }

    public FileInfo sendFile2Oss(String fileSourceUrl, String ossTargetUrl, InputStream inputStream) {
        Instant start = Instant.now();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
        String[] urlArray = fileSourceUrl.split("\\.");
        String suffix = urlArray[urlArray.length-1];
        long timestamp = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        queryParams.add("appCode",ossAppCode);
        String authorization = SignUtil.sign(ossKey,(ossAppCode+timestamp).getBytes());
        queryParams.add("Authorization",authorization);
        queryParams.add("timestamp",String.valueOf(timestamp));
        queryParams.add("deviceInfo","pc");
        queryParams.add("sourceId","erp");
        queryParams.add("suffix",suffix);
        queryParams.add("previlege","0");
        queryParams.add("termination", TerminationType.PC.getCode());
        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder
                .fromHttpUrl(ossTargetUrl)
                .queryParams(queryParams);

        MultiValueMap<String,Object> bodyMap = new LinkedMultiValueMap<>();
        Resource resource = new InputStreamResource(inputStream){

            @Override
            public String getFilename(){
                return "tmp";
            }

            @Override
            public long contentLength() throws IOException {
                return inputStream.available();
            }
        };
        bodyMap.add("file",resource);
        HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(bodyMap,headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(uriComponentsBuilder.build().encode().toUri(),httpEntity,String.class);

        LOGGER.info("迁移文件到OSS，响应结果：{}",responseEntity.getBody());

        //解析返回的结果，获取ossUrl和resourceId
        JSONObject jsonObject = JSONObject.fromObject(responseEntity.getBody());
        boolean result = Boolean.parseBoolean(jsonObject.getString("success"));

        if (!result){
            return new FileInfo(-1,"文件上传失败");
        }

        long costTime = ChronoUnit.MILLIS.between(start, Instant.now());
        LOGGER.info("成功迁移文件{}到OSS，耗时:{}，响应结果：{}",fileSourceUrl,costTime,responseEntity.getBody());

        JSONObject data = JSONObject.fromObject(jsonObject.get("data"));
        String ossFileUrl = data.getString("url");
        String resourceId = data.getString("resourceId");

        String[] domainAndUri= UrlUtils.getDomainAndUriFromUrl(ossFileUrl);
        if(domainAndUri!=null&& StringUtil.isNotBlank(domainAndUri[0])&&StringUtil.isNotBlank(domainAndUri[1])) {

            return new FileInfo(0, "上传成功",getNewFileName(suffix), domainAndUri[1],
                    domainAndUri[0], resourceId, costTime + StringUtils.EMPTY);
        }else{
            return new FileInfo(-1,"文件上传失败");
        }
    }

    public String getNewFileName(String suffix){
        String time = DateUtil.gainNowDate() + "";
        Random random = new Random();
        time = time + "_" + String.format("%04d", random.nextInt(10000));// 随机数4位，不足4位，补位
        return time + "." + suffix;
    }

}
