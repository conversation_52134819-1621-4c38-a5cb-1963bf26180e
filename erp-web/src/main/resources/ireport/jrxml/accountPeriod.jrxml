<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="accountPeriod" language="groovy" printOrder="Horizontal" pageWidth="1420" pageHeight="615" orientation="Landscape" columnWidth="1420" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isTitleNewPage="true" whenResourceMissingType="Empty" isIgnorePagination="true" uuid="4e52887f-2521-4218-a53d-9416a548937f">
	<property name="ireport.zoom" value="0.****************"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="style_detail_left" hAlign="Left" vAlign="Middle" isBlankWhenNull="true" fontName="宋体" fontSize="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H">
		<box>
			<pen lineWidth="0.5"/>
			<topPen lineWidth="0.5"/>
			<leftPen lineWidth="0.5"/>
			<bottomPen lineWidth="0.5"/>
			<rightPen lineWidth="0.5"/>
		</box>
	</style>
	<style name="style_detail_center" hAlign="Center" vAlign="Middle" fontName="SansSerif" fontSize="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H">
		<box>
			<pen lineWidth="0.5"/>
			<topPen lineWidth="0.5"/>
			<leftPen lineWidth="0.5"/>
			<bottomPen lineWidth="0.5"/>
			<rightPen lineWidth="0.5"/>
		</box>
	</style>
	<style name="style_title_name" hAlign="Center" vAlign="Middle" fontName="宋体" fontSize="13" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H">
		<box>
			<pen lineWidth="0.5"/>
			<topPen lineWidth="0.5"/>
			<leftPen lineWidth="0.5"/>
			<bottomPen lineWidth="0.5"/>
			<rightPen lineWidth="0.5"/>
		</box>
	</style>
	<style name="style_title_value" hAlign="Left" vAlign="Middle" isBlankWhenNull="true" fontName="宋体" fontSize="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H">
		<box>
			<pen lineWidth="0.5"/>
			<topPen lineWidth="0.5"/>
			<leftPen lineWidth="0.5"/>
			<bottomPen lineWidth="0.5"/>
			<rightPen lineWidth="0.5"/>
		</box>
	</style>
	<style name="style_detail_right" hAlign="Right" vAlign="Middle" fontName="宋体" fontSize="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H">
		<box>
			<pen lineWidth="0.5"/>
			<topPen lineWidth="0.5"/>
			<leftPen lineWidth="0.5"/>
			<bottomPen lineWidth="0.5"/>
			<rightPen lineWidth="0.5"/>
		</box>
	</style>
	<style name="style_title_center" backcolor="#00FF00" hAlign="Center" vAlign="Middle" fontName="SansSerif" fontSize="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H">
		<box>
			<pen lineWidth="0.5"/>
			<topPen lineWidth="0.5"/>
			<leftPen lineWidth="0.5"/>
			<bottomPen lineWidth="0.5"/>
			<rightPen lineWidth="0.5"/>
		</box>
	</style>
	<field name="traderName" class="java.lang.String"/>
	<field name="traderType" class="java.lang.Double"/>
	<field name="customerNature" class="java.lang.String"/>
	<field name="accountPeriodNow" class="java.lang.String"/>
	<field name="accountPeriodDaysNow" class="java.lang.String"/>
	<field name="overdueTimes" class="java.lang.String"/>
	<field name="overdueAmount" class="java.lang.String"/>
	<field name="addTimeStr" class="java.lang.String"/>
	<field name="accountPeriodApply" class="java.lang.String"/>
	<field name="accountPeriodDaysApply" class="java.lang.String"/>
	<field name="creatorNm" class="java.lang.String"/>
	<field name="status" class="java.lang.String"/>
	<columnHeader>
		<band height="25" splitType="Stretch">
			<frame>
				<reportElement style="style_detail_center" mode="Opaque" x="0" y="0" width="1420" height="25" backcolor="#99CCFF" uuid="a144a4a6-65db-4f80-9384-bbbdad2c8cea"/>
				<staticText>
					<reportElement style="style_detail_center" x="0" y="0" width="210" height="25" forecolor="#000000" backcolor="#0099FF" uuid="18cedb62-bdd4-46f9-b1a8-270ff18d300a"/>
					<textElement>
						<font fontName="宋体" size="12"/>
						<paragraph tabStopWidth="30"/>
					</textElement>
					<text><![CDATA[客户名称]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="210" y="0" width="70" height="25" backcolor="#0099FF" uuid="71432a89-05dd-4b4d-827f-d27dfec4ab6a"/>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[客户身份]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="280" y="0" width="70" height="25" backcolor="#0099FF" uuid="51e32d51-563a-4747-ab56-28951ec3a1e5"/>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[客户性质]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="350" y="0" width="90" height="25" backcolor="#0099FF" uuid="bea791f6-763b-41e8-8672-a64be45165ed"/>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[当前额度]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="440" y="0" width="95" height="25" backcolor="#0099FF" uuid="de8ca1d5-fc92-45fd-8ab4-abb2f9ce38e8"/>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[当前帐期天数]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="535" y="0" width="65" height="25" backcolor="#0099FF" uuid="039277e6-dd25-48ac-b57e-3eea0a4763a8"/>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[逾期次数]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="600" y="0" width="90" height="25" backcolor="#0099FF" uuid="95455b76-85c2-4f2b-a83c-a98c53cef5dd"/>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[逾期总额]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="690" y="0" width="140" height="25" backcolor="#0099FF" uuid="bc2b2d17-61d8-4f88-b85c-0b8de60858e5"/>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[申请日期]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="830" y="0" width="110" height="25" backcolor="#0099FF" uuid="dce86fe6-be50-4a9a-a5ab-41475e4f52c1"/>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[申请金额]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="940" y="0" width="90" height="25" backcolor="#0099FF" uuid="3828a4eb-4772-4608-82c9-7b94b4ec0768"/>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[申请帐期天数]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="1030" y="0" width="90" height="25" backcolor="#0099FF" uuid="60c8ccd6-6dc1-465e-8930-726ef349af6e"/>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[归属人员]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="1120" y="0" width="75" height="25" backcolor="#0099FF" uuid="8e2076f1-0e63-460f-98d9-0e6ae6d9d96f"/>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[审核状态]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="1195" y="0" width="85" height="25" backcolor="#0099FF" uuid="56dac134-7307-4a7f-ac87-d94aa9ff6cd0"/>
					<textElement>
						<font fontName="宋体" size="12"/>
						<paragraph tabStopWidth="30"/>
					</textElement>
					<text><![CDATA[审核人]]></text>
				</staticText>
				<staticText>
					<reportElement style="style_detail_center" x="1280" y="0" width="140" height="25" backcolor="#0099FF" uuid="3a00f76f-ed2b-4150-9a78-2e4f4c96c83d"/>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[审核时间]]></text>
				</staticText>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="20" splitType="Stretch">
			<frame>
				<reportElement mode="Opaque" x="0" y="0" width="1420" height="20" backcolor="#FFFFFF" uuid="ff4f5f4f-741b-485d-b65c-040bc8627a9e"/>
				<textField isStretchWithOverflow="true">
					<reportElement style="style_detail_left" stretchType="RelativeToTallestObject" x="0" y="0" width="210" height="20" uuid="ad9f938d-cc40-4433-a518-9da3e96b2997"/>
					<textElement>
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{traderName}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="210" y="0" width="70" height="20" uuid="6dd9afab-7e34-42f5-b6e2-94ef768043ed"/>
					<textElement>
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{traderType}==1?'客户':'供应商')]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="false">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="280" y="0" width="70" height="20" uuid="3a2c85db-3545-459d-96ed-a21cf42d4aec"/>
					<textElement>
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{customerNature}==null?'':($F{customerNature}.equals('465')?'分销':'终端')]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="350" y="0" width="90" height="20" uuid="aeb04abe-8103-4cb0-a21e-70e443835453"/>
					<textElement>
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{accountPeriodNow}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement style="style_detail_left" stretchType="RelativeToTallestObject" x="440" y="0" width="95" height="20" uuid="51a539f9-dc20-454c-9654-f0f3ce675b41"/>
					<textElement textAlignment="Center">
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{accountPeriodDaysNow}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement style="style_detail_left" stretchType="RelativeToTallestObject" x="535" y="0" width="65" height="20" uuid="e4349903-ca9d-4e93-97e6-151614fa3748"/>
					<textElement textAlignment="Center">
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{overdueTimes}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement style="style_detail_left" stretchType="RelativeToTallestObject" x="600" y="0" width="90" height="20" uuid="6f7e77f8-c3d1-45c9-9688-5f24272b7cd1"/>
					<textElement textAlignment="Center">
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{overdueAmount}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy HH.mm.ss" isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="690" y="0" width="140" height="20" uuid="451aeb2a-cc72-401a-b61a-97a3c93e9f7b"/>
					<textElement>
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{addTimeStr}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="830" y="0" width="110" height="20" uuid="3ce049b8-7a92-48a1-af6e-2bb4bcc8b958"/>
					<textElement>
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{accountPeriodApply}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="940" y="0" width="90" height="20" uuid="d86ed8a3-6e71-4c94-b1ae-cf9b1521c207"/>
					<textElement>
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{accountPeriodDaysApply}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="1030" y="0" width="90" height="20" uuid="a3a5cb9f-975e-4ff9-b057-02c65b2665b3"/>
					<textElement>
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{creatorNm}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="1120" y="0" width="75" height="20" uuid="d94b9d56-2c20-4506-bb00-2707f21df4a0"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement>
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{status}.equals('0')?'审核中':($F{status}.equals('1')?'审核通过':'审核不通过')]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy HH.mm.ss" isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="1280" y="0" width="140" height="20" uuid="f9c5610b-b456-432d-965f-24b0c0195588"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement>
						<font fontName="宋体" size="11"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="style_detail_center" stretchType="RelativeToTallestObject" x="1195" y="0" width="85" height="20" uuid="e8ad7409-7951-4715-9f9b-6c52db6658f0"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement>
						<font fontName="宋体" size="11"/>
					</textElement>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
