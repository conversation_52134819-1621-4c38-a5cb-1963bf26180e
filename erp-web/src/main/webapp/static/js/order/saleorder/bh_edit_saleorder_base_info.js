$(function(){

	$("#prepareReaseonType").change(function(){
		if($(this).val() == "4264"){
			$("#prepareCommentsDiv").show();
		}else {
			$("#prepareCommentsDiv").hide();
		}
	})

	$("#prepareReaseonType").trigger('change');

	$("#editBhSaleorderForm").submit(function(){
		checkLogin();
		clearErroeMes();//清除錯誤提示信息
		let reaseonType = $("#prepareReaseonType").val();
		if(reaseonType == "-1"){
			warnTips("prepareCommentsErr","请选择申请原因");
			return false;
		}

		if(reaseonType == "4264"){
			var prepareComments = $("#prepareComments").val();
			if( prepareComments == ''){
				warnTips("prepareCommentsErr","请填写申请原因");
				return false;
			}
			if( prepareComments.length > 256 ){
				warnTips("prepareCommentsErr","申请原因不允许超过256个字");
				return false;
			}
		}

		var marketingPlan = $("#marketingPlan").val();
		if(marketingPlan!="" && marketingPlan.length>256){
			warnTips("marketingPlan","后期营销计划长度应该在0-256个字符之间");
			return false;
		}
		$.ajax({
			url:page_url+'/order/saleorder/saveEditBhSaleorder.do',
			data:$('#editBhSaleorderForm').serialize(),
			type:"POST",
			dataType : "json",
			success:function(data)
			{
				if (data.code == 0) {
					window.parent.location.reload();
				} else {
					layer.alert(data.message);
				}
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
		return false;
	})
});

function updateReaseon(obj){
	checkLogin();
	let val = $(obj).val();
	if(val=="4264"){
		$("#prepareCommentsDiv").show();
	}else{
		$("#prepareCommentsDiv").hide();
	}
	warnTips("prepareCommentsErr","");
}
