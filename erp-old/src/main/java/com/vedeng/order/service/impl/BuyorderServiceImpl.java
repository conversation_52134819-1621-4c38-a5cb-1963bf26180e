package com.vedeng.order.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.common.constants.Contant;
import com.fasterxml.jackson.core.type.TypeReference;
import com.newtask.data.dao.BuyorderDataMapper;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.authorization.dao.OrganizationMapper;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.*;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.enums.BusinessSourceTypeEnum;
import com.vedeng.common.core.enums.SupplierAssetEnum;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.shiro.SpringContextHolder;
import com.vedeng.common.util.*;
import com.vedeng.customerbillperiod.constant.CustomerBillPeriodOverdueManageDetailTypeEnum;
import com.vedeng.erp.buyorder.dto.BuyorderModifyApplyGoods;
import com.vedeng.erp.buyorder.dto.*;
import com.vedeng.erp.buyorder.service.*;
import com.vedeng.erp.finance.cmd.SettlementBillQueryCmd;
import com.vedeng.erp.finance.dto.SettlementBillApiDto;
import com.vedeng.erp.finance.dto.SettlementBillItemApiDto;
import com.vedeng.erp.finance.service.CapitalBillApiService;
import com.vedeng.erp.finance.service.SettlementBillApiService;
import com.vedeng.erp.finance.service.PayApplyApiService;
import com.vedeng.erp.saleorder.api.OrderInfoSyncService;
import com.vedeng.erp.saleorder.dto.SaleOrderGoodsDetailDto;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import com.vedeng.erp.saleorder.service.SaleOrderGoodsApiService;
import com.vedeng.erp.trader.dto.SupplierAssetApiDto;
import com.vedeng.erp.trader.dto.TraderSupplierDto;
import com.vedeng.erp.trader.service.SupplierAssetApiService;
import com.vedeng.erp.trader.service.TraderSupplierApiService;
import com.vedeng.finance.dao.InvoiceMapper;
import com.vedeng.finance.dao.PayApplyDetailMapper;
import com.vedeng.finance.dao.PayApplyMapper;
import com.vedeng.finance.dto.InvoiceRelationWarehouseLogDto;
import com.vedeng.finance.model.CapitalBill;
import com.vedeng.finance.model.CapitalBillDetail;
import com.vedeng.finance.model.Invoice;
import com.vedeng.finance.model.PayApply;
import com.vedeng.finance.service.CapitalBillService;
import com.vedeng.flash.constant.FlashConstant;
import com.vedeng.flash.dao.EarlyWarningTaskMapper;
import com.vedeng.flash.dto.EarlyWarningTaskDto;
import com.vedeng.flash.dto.ExpeditingCountingDto;
import com.vedeng.flash.dto.PrepareStockCommand;
import com.vedeng.flash.dto.PrepareStockDto;
import com.vedeng.flash.enums.DealTypeEnum;
import com.vedeng.flash.enums.WarnLevelEnum;
import com.vedeng.flash.service.prepare.PrepareStockService;
import com.vedeng.flash.service.warningtask.ExpeditingTicketsService;
import com.vedeng.goods.dao.GoodsChannelPriceMapper;
import com.vedeng.goods.dao.GoodsMapper;
import com.vedeng.goods.dao.GoodsSafeStockMapper;
import com.vedeng.goods.dao.RCategoryJUserMapper;
import com.vedeng.goods.model.Goods;
import com.vedeng.goods.model.GoodsChannelPrice;
import com.vedeng.goods.model.GoodsSafeStock;
import com.vedeng.goods.model.vo.GoodsVo;
import com.vedeng.logistics.dao.ExpressDetailMapper;
import com.vedeng.logistics.dao.ExpressMapper;
import com.vedeng.logistics.dao.OutboundBatchesRecodeMapper;
import com.vedeng.logistics.dao.WarehouseGoodsOperateLogMapper;
import com.vedeng.logistics.model.*;
import com.vedeng.logistics.model.vo.ExpressArrivalDetailVo;
import com.vedeng.logistics.model.vo.WarehouseGoodsOperateLogVo;
import com.vedeng.logistics.service.ExpressService;
import com.vedeng.logistics.service.LogisticsService;
import com.vedeng.order.controller.BuyorderController;
import com.vedeng.order.dao.*;
import com.vedeng.order.model.*;
import com.vedeng.order.model.dto.OrderAssistantRelationDto;
import com.vedeng.order.model.dto.PurchaseContractDto;
import com.vedeng.order.model.ge.GeTraderSku;
import com.vedeng.order.model.ge.MasterSlaveList;
import com.vedeng.order.model.ge.VBuyorderSncodeMaster;
import com.vedeng.order.model.ge.VBuyorderSncodeSlave;
import com.vedeng.order.model.vo.*;
import com.vedeng.order.service.*;
import com.vedeng.system.dao.AddressMapper;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.dao.SysOptionDefinitionMapper;
import com.vedeng.system.dao.VerifiesInfoMapper;
import com.vedeng.system.model.*;
import com.vedeng.system.model.vo.AddressVo;
import com.vedeng.system.service.AddressService;
import com.vedeng.system.service.ParamsConfigValueService;
import com.vedeng.system.service.VerifiesRecordService;
import com.vedeng.todolist.constant.TodoListBuzSceneEnum;
import com.vedeng.todolist.dao.TodoListMapper;
import com.vedeng.trader.dao.PeriodUseNodeRecordMapper;
import com.vedeng.trader.dao.TraderAddressMapper;
import com.vedeng.trader.dao.TraderContactMapper;
import com.vedeng.trader.model.TraderAddress;
import com.vedeng.trader.model.TraderContact;
import com.vedeng.trader.model.po.PeriodUseNodeRecordPo;
import com.vedeng.trader.model.vo.TraderAddressVo;
import com.vedeng.trader.service.TraderSupplierService;
import com.wms.constant.OutBoundBatchConstant;
import com.wms.service.util.GlobalThreadPool;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.impl.persistence.entity.HistoricActivityInstanceEntity;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <b>Description:</b><br>
 * 采购订单service接口实现
 *
 * <AUTHOR>
 * @Note <b>ProjectName:</b> erp <br>
 * <b>PackageName:</b> com.vedeng.order.service.impl <br>
 * <b>ClassName:</b> BuyorderServiceImpl <br>
 * <b>Date:</b> 2017年7月11日 上午9:20:01
 */
@Service("buyorderService")
@Slf4j
public class BuyorderServiceImpl extends BaseServiceimpl implements BuyorderService {
    public static Logger logger = LoggerFactory.getLogger(BuyorderServiceImpl.class);

    @Resource
    private UserMapper userMapper;
    @Resource
    private OrganizationMapper organizationMapper;
    @Autowired
    @Qualifier("paramsConfigValueService")
    private ParamsConfigValueService paramsConfigValueService;
    @Autowired
    @Qualifier("addressService")
    private AddressService addressService;

    @Autowired
    @Qualifier("rCategoryJUserMapper")
    private RCategoryJUserMapper rCategoryJUserMapper;

    @Autowired
    @Qualifier("goodsChannelPriceMapper")
    private GoodsChannelPriceMapper goodsChannelPriceMapper;

    @Autowired
    @Qualifier("goodsSafeStockMapper")
    private GoodsSafeStockMapper goodsSafeStockMapper;

    @Resource
    private AddressMapper addressMapper;

    @Resource
    private BuyOrderModifyApplyMapper buyOrderModifyApplyMapper;

    @Resource
    private BuyorderModifyApplyGoodsMapper buyorderModifyApplyGoodsMapper;

    @Autowired
    private VerifiesInfoMapper verifiesInfoMapper;
    @Autowired
    private BuyorderDataMapper buyorderDataMapper;

    @Resource
    private BuyorderMapper buyorderMapper;

    @Resource
    private ExpressMapper expressMapper;

    @Autowired
    @Qualifier("expressDetailMapper")
    private ExpressDetailMapper expressDetailMapper;

    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Resource
    private SaleorderMapper saleorderMapper;
    @Resource
    private GoodsMapper goodsMapper;

    @Autowired
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Resource
    private VBuyorderSncodeMasterMapper vBuyorderSncodeMasterMapper;

    @Resource
    private VBuyorderSncodeSlaveMapper vBuyorderSncodeSlaveMapper;

    @Resource
    private GeInfoCollectionMapper geInfoCollectionMapper;

    @Autowired
    @Qualifier("GeInfoCollectionService")
    private GeInfoCollectionService geInfoCollectionService;

    @Value("${GE_TRADER_SKU}")
    protected String GE_TRADER_SKU;

    @Autowired
    private RiskCheckService riskCheckService;

    @Autowired
    private ExpeditingTicketsService expeditingTicketsService;

    @Resource
    private OrderAssistantJProductPositionMapper orderAssistantJProductPositionMapper;

    @Resource
    private TodoListMapper todoListMapper;

    @Autowired
    private EarlyWarningTaskMapper earlyWarningTaskMapper;

    @Autowired
    private PrepareStockService prepareStockService;

    @Autowired
    private OrderAssistantRelationMapper orderAssistantRelationMapper;

    @Resource
    private PeriodUseNodeRecordMapper periodUseNodeRecordMapper;

    @Resource
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Resource
    private WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

    @Resource
    private TraderContactMapper traderContactMapper;

    @Resource
    private TraderAddressMapper traderAddressMapper;

    @Resource
    private OrderCommonService orderCommonService;

    @Autowired
    private SaleorderService saleorderService;

    @Resource
    private SysOptionDefinitionMapper sysOptionDefinitionMapper;

    @Resource
    private RBuyorderSaleorderMapper rBuyorderSaleorderMapper;

    @Autowired
    private BuyorderExpenseApiService expenseApiService;

    @Autowired
    private CapitalBillService capitalBillService;

    @Resource
    private PayApplyMapper payApplyMapper;

    @Resource
    private PayApplyDetailMapper payApplyDetailMapper;

    @Autowired
    private BuyorderExpenseApiService buyorderExpenseApiService;

    @Resource
    private InvoiceMapper invoiceMapper;

    @Autowired
    private RBuyorderExpenseJSaleorderService rBuyorderExpenseJSaleorderService;

    @Autowired
    private SaleOrderGoodsApiService saleOrderGoodsApiService;


    @Autowired
    private SaleOrderApiService saleOrderApiService;

    @Autowired
    private AttachmentMapper attachmentMapper;
    @Autowired
    @Qualifier("verifiesRecordService")
    private VerifiesRecordService verifiesRecordService;

    @Autowired
    @Qualifier("actionProcdefService")
    protected ActionProcdefService actionProcdefService;

    @Autowired
    private SupplierAssetApiService supplierAssetApiService;

    @Autowired
    private SettlementBillApiService settlementBillApiService;

    @Autowired
    private TraderSupplierApiService traderSupplierApiService;

    @Autowired
    private BuyOrderRebateApiService buyOrderRebateApiService;

    @Autowired // 自动装载
    protected ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    @Autowired
    private PayApplyApiService payApplyApiService;
    @Autowired
    private TraderSupplierService traderSupplierService;
    @Autowired
    private CapitalBillApiService capitalBillApiService;
    @Autowired
    private BuyorderApiService buyorderApiService;

    private static final Integer STATUS_CLOSED = 3;//关闭状态

    private ResultInfo returnContinue(String message){
        ResultInfo resultInfo = new ResultInfo();
        resultInfo.setCode(2);//需要继续请求
        resultInfo.setMessage(message);
        return resultInfo;
    }

    private ResultInfo returnNoMore(String message){
        ResultInfo resultInfo = new ResultInfo();
        resultInfo.setCode(3);
        resultInfo.setMessage(message);
        return resultInfo;
    }

    @Transactional
    @Override
    public ResultInfo<?> processLogisticsFromSaleOrder(List<SyncExpressDto> sourceSyncExpressDtoList) {

        String buyorderNo = sourceSyncExpressDtoList.get(0).getBuyorderNo();
        BuyOrderApiDto buyOrderApiDto =  buyorderApiService.getBuyorderByBuyorderNo(buyorderNo);
        List<BuyorderGoodsApiDto> buyorderGoods = buyOrderApiDto.getBuyorderGoodsApiDtos();
        if( STATUS_CLOSED.equals( buyOrderApiDto.getStatus() ) || CollectionUtils.isEmpty(buyorderGoods) ) {
            return returnNoMore("采购订单已关闭或当前无商品信息");
        }

        Map<String,BuyorderGoodsApiDto> skuAndGiftKeyMap = new HashMap<>();
        Map<String,BuyorderGoodsApiDto> skuKeyMap = new HashMap<>();//
        for( BuyorderGoodsApiDto buyorderGoodsApiDto : buyorderGoods ) {
            skuAndGiftKeyMap.put(buyorderGoodsApiDto.getSku()+"-"+(buyorderGoodsApiDto.getIsGift()?"1":"0"), buyorderGoodsApiDto);
            skuKeyMap.put(buyorderGoodsApiDto.getSku(), buyorderGoodsApiDto);
        }


        //组装一个HashMap，来暂存已经同步过来的物流信息，已经同步过来的物流信息不再做同步。直接跳过。按物流快递单号判断重复性
        List<SyncExpressDto> targetSyncExpressDtos = expressMapper.getSyncExpressListForBuyOrder(buyorderNo);
        Map<String,SyncExpressDto> targetSyncExpressDtoMap = new HashMap<>();
        List<String> targetLogisticNoExistList =targetSyncExpressDtos.stream().map(SyncExpressDto::getLogisticsNo).collect(Collectors.toList());
        for( SyncExpressDto syncExpressDto : targetSyncExpressDtos ) {
            targetSyncExpressDtoMap.put(syncExpressDto.getLogisticsNo(), syncExpressDto);
        }

        //遍历源单号的物流信息，从已存在的单号HashMap中去掉，如果此时，已存在的物流单号，如果还剩下，那说明当前ERP系统中，手工维护过了物流信息
        for( SyncExpressDto syncExpressDto : sourceSyncExpressDtoList ) {
            targetLogisticNoExistList.remove(syncExpressDto.getLogisticsNo());//移除当前对象的单号
        }

        if(CollectionUtils.isNotEmpty(targetLogisticNoExistList)){
            //如果当前ERP中的物流快递单号去除了来源系统给过来的以外，还有其他的单号，说明不需要再同步了
            return returnNoMore("采购订单号已维护过了快递信息，不需要再同步了");
        }

        for(SyncExpressDto syncExpressDto : sourceSyncExpressDtoList ){
            String logisticsNo = syncExpressDto.getLogisticsNo();
            if(targetSyncExpressDtoMap.containsKey(logisticsNo)){
                log.info("快递单号已经同步过了");
                continue;
            }

            String delivery = DateUtil.convertString(syncExpressDto.getDeliveryTime(), DateUtil.DATE_FORMAT_NO);
            String directNo = OutBoundBatchConstant.DIRECT_PREFIX + buyOrderApiDto.getBuyorderId() + delivery;


            Express express = new Express();
            express.setLogisticsNo(logisticsNo);
            express.setLogisticsId(syncExpressDto.getLogisticsId());
            express.setCompanyId(syncExpressDto.getCompanyId());
            express.setLogisticsNo(logisticsNo);
            express.setDeliveryTime(syncExpressDto.getDeliveryTime());
            express.setBatchNo(directNo);
            express.setCreator(syncExpressDto.getCreator());
            express.setUpdater(syncExpressDto.getUpdater());
            express.setAddTime(System.currentTimeMillis());
            express.setModTime(System.currentTimeMillis());
            express.setIsEnable(1); // 必填，不能为null，影响状态的查询展示
            express.setLogisticsComments("");// 必填，不能为null，影响状态的查询展示
            expressMapper.insertSelective(express);
            Integer expressId = express.getExpressId();
            StringBuilder id_num_price_sku = new StringBuilder();
            List<SyncExpressDetailDto> expressDetailDtoList = syncExpressDto.getExpressDetailDtoList();
            for( SyncExpressDetailDto syncExpressDetailDto : expressDetailDtoList ) {
                String nonAllArrivalReason = syncExpressDetailDto.getNonAllArrivalReason();//此处存的是SKU-1,这种格式，用来匹配商品行 即SKU+横线+是否赠品
                BuyorderGoodsApiDto buyorderGoodsApiDto = skuAndGiftKeyMap.get(nonAllArrivalReason);
                if(buyorderGoodsApiDto==null){
                    buyorderGoodsApiDto = skuKeyMap.get(nonAllArrivalReason.split("-")[0]);
                    if(buyorderGoodsApiDto==null){
                        log.error("出现异常商品，SKU没找到");
                        continue;
                    }
                }
                Integer buyorderGoodsId = buyorderGoodsApiDto.getBuyorderGoodsId();

                ExpressDetail expressDetail = new ExpressDetail();
                expressDetail.setExpressId(expressId);
                expressDetail.setNum(syncExpressDetailDto.getNum());
                expressDetail.setRelatedId(buyorderGoodsId);
                expressDetail.setBusinessType(SysOptionConstant.ID_515);
                expressDetailMapper.insertSelective(expressDetail);
                id_num_price_sku.append(buyorderGoodsId).append("|").append(syncExpressDetailDto.getNum()).append("|")
                        .append(buyorderGoodsApiDto.getPrice()).append("|").append(buyorderGoodsApiDto.getSku()).append("_");
            }

            //批次表新增或删除批次
            OutboundBatchesRecode outboundBatchesRecode = new OutboundBatchesRecode();
            outboundBatchesRecode.setBatchType(Constants.TWO);
            insertOrUpdateOutboundBatches(express, outboundBatchesRecode);

            //以下这一整段逻辑摘自  /order/buyorder/saveAddExpress
            //更新采购单发货状态 VDERP-2431
            updateBuyorderDeliveryStatus(buyOrderApiDto.getBuyorderId());

            //VDERP-14182 更新采购订单ExpressEnableReceive
            if(1 == buyorderMapper.selectDeliveryDirect(buyOrderApiDto.getBuyorderId())){
                checkExpressEnableReceive(buyOrderApiDto.getBuyorderId());
            }

            // VDERP-8759 订单流发货状态同步
            calculateDeliveryStatus(buyOrderApiDto.getBuyorderId(), 2);

            // 更新采购单关联的直属费用单的发货状态
            BuyorderExpenseDto buyorderExpenseDto = buyorderExpenseApiService.queryAttributiveExpenseInfoByBuyorderId(buyOrderApiDto.getBuyorderId());
            if (Objects.nonNull(buyorderExpenseDto)) {
                buyorderExpenseApiService.doDeliveryStatus(buyorderExpenseDto.getBuyorderExpenseId());
                log.info("直发采购单新增物流信息同步更新直属费用单发货状态，采购费用单：{}", JSON.toJSONString(buyorderExpenseDto));
            }
            //更新赠品单状态
            updateGiftOrderStatus(buyOrderApiDto.getBuyorderId());
            pushExpressToWeb(express, buyOrderApiDto.getBuyorderId(), id_num_price_sku.toString(), "保存");// id_num_price暂先赋空了
            //采购添加快递处理直发订单账期编码监管
            dealDirectOrderPeriodManagement(express, buyOrderApiDto.getBuyorderId(), ErpConst.OperateType.INSERT_OPERATE);

            // 修改对应的销售商品的发货状态
            List<RBuyorderSaleorder> listInfo = rBuyorderSaleorderMapper.getRBuyorderSaleorderInfoList(buyOrderApiDto.getBuyorderId());
            List<SaleorderGoods> sdLists = saleorderGoodsMapper.getSaleorderGoodsList(listInfo);
            for (SaleorderGoods saleorderGoods : sdLists) {
                //订单流升级更新销售单收发货信息
                orderInfoSyncService.syncDeliveryAndArrivalDetailOfSaleOrder(saleorderGoods.getSaleorderId());
            }

            /**
             * 推送快递信息至base服务
             */

            String phone =   expressService.getPhoneByBusinessType(express.getExpressId(), express.getLogisticsNo());

            logger.info("开始推送快递信息至base服务,单号:{}", express.getLogisticsNo());
            logisticsService.pushLogisticsToBase(express.getLogisticsNo()
                    , logisticsService.getLogisticsCodeByLogisticsId(express.getLogisticsId())
                    , phone);
        }
        return ResultInfo.success("同步成功");
    }

    /**
     * 出库批次表新建批次
     *
     * @param express
     */
    private void insertOrUpdateOutboundBatches(Express express,OutboundBatchesRecode outboundBatchesRecode) {
        try {
            if (org.apache.commons.lang.StringUtils.isNotEmpty(express.getBatchNo())) {
                outboundBatchesRecode.setBatchNo(express.getBatchNo());
                outboundBatchesRecode.setDeliveryTime(express.getDeliveryTime());
                outboundBatchesRecode.setCreator(express.getCreator());
                outboundBatchesRecode.setUpdater(express.getUpdater());
                outboundBatchesRecode.setAddTime(DateUtil.sysTimeMillis());
                outboundBatchesRecode.setModTime(DateUtil.sysTimeMillis());
                //新增批次表
                outboundBatchesRecodeMapper.insertOrUpdate(outboundBatchesRecode);
            }

        } catch (Exception e) {
            logger.error("新建批次无物流关系失败", e);
        }
    }
    @Autowired
    @Qualifier("outboundBatchesRecodeMapper")
    private OutboundBatchesRecodeMapper outboundBatchesRecodeMapper;

    @Autowired
    @Qualifier("expressService")
    private ExpressService expressService;

    @Autowired
    @Qualifier("logisticsService")
    private LogisticsService logisticsService;

    @Autowired
    private OrderInfoSyncService orderInfoSyncService;

    private void calculateDeliveryStatus(Integer buyorderId, Integer point){
        BuyorderVo buyorderVo = buyorderMapper.getBuyorderVoById(buyorderId);
        buyorderVo.setBuyorderGoodsVoList(buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderId(buyorderId));
        if (buyorderVo.getIsNew() == 1) {
            Buyorder buyorder = new Buyorder();
            buyorder.setBuyorderId(buyorderId);
            buyorder.setDeliveryStatus(buyorderVo.getDeliveryStatus()); // 先赋上原来的值，防止一个条件都没满足，传入mybatis造成sql错误

            Integer totalArrivalNum = 0; // 收货数量之和
            Integer totalPurchaseNum = 0; // 采购数量（现值）之和
            List<BuyorderGoodsVo> buyorderGoodsVoList = buyorderVo.getBuyorderGoodsVoList();
            Integer num = expressMapper.getTotalNumInExpressDetailByBuyorderId(buyorderId);

            for (BuyorderGoodsVo buyorderGoodsVo : buyorderGoodsVoList) {
                totalArrivalNum += buyorderGoodsVo.getArrivalNum();
                totalPurchaseNum += buyorderGoodsVo.getNum() - buyorderGoodsVo.getAfterReturnNum();
            }
            if (point == 1) { // 确认收货 或wms回传入库记录
                if (totalArrivalNum.equals(totalPurchaseNum) && buyorderVo.getArrivalStatus() == 2) {
                    buyorder.setDeliveryStatus(2);
                } else if (totalArrivalNum < totalPurchaseNum && buyorderVo.getArrivalStatus() == 1) {
                    if (Objects.isNull(num) || num == 0) {
                        buyorder.setDeliveryStatus(1);
                    } else if (num < totalPurchaseNum) {
                        buyorder.setDeliveryStatus(1);
                    } else if (num.equals(totalPurchaseNum)) {
                        buyorder.setDeliveryStatus(2);
                    }
                }
                buyorder.setDeliveryTime(System.currentTimeMillis());
                log.info("订单流-确认收货或wms回传入库记录更改发货状态{}",buyorderVo.getBuyorderNo());
            } else if (point == 2) { // 新增/编辑/删除快递信息
                if (Objects.isNull(num) || num == 0) {
                    buyorder.setDeliveryStatus(0);
                } else if (num < totalPurchaseNum) {
                    buyorder.setDeliveryStatus(1);
                } else if (num.equals(totalPurchaseNum)) {
                    buyorder.setDeliveryStatus(2);
                }
                buyorder.setDeliveryTime(System.currentTimeMillis());
                log.info("订单流-新增编辑删除快递信息更改发货状态{}",buyorderVo.getBuyorderNo());
            } else if (point == 3) { // 采购售后退货完结
                if (buyorderVo.getDeliveryStatus() == 1 && (totalPurchaseNum <= totalArrivalNum || (!Objects.isNull(num) && totalPurchaseNum <= num))) {
                    buyorder.setDeliveryStatus(2);
                    buyorder.setDeliveryTime(System.currentTimeMillis());
                    log.info("订单流-采购售后退货完结更改发货状态{}-totalPurchaseNum{}-totalArrivalNum{}-num{}" +
                            "", buyorderVo.getBuyorderNo(),totalPurchaseNum,totalArrivalNum,num);
                }
            }
            buyorderMapper.updateByPrimaryKeySelective(buyorder);
        }
    }

    /**
     * <b>Description:</b><br>
     * 查询分页信息
     *
     * @param buyorderVo
     * @param page
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年7月11日 上午9:25:35
     */
    @Override
    public Map<String, Object> getBuyorderVoPage(BuyorderVo buyorderVo, Page page) {

        Map<String, Object> map = null;
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<Map<String, Object>>> TypeRef =
                new TypeReference<ResultInfo<Map<String, Object>>>() {
                };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.GET_BUYORDER_PAGE,
                    buyorderVo, clientId, clientKey, TypeRef, page);
            if (result != null && result.getCode() == 0) {
                Map<String, Object> result_map = (Map<String, Object>) result.getData();
                if (result_map != null && result_map.size() > 0) {
                    map = new HashMap<String, Object>();

                    net.sf.json.JSONArray json = null;
                    String openInvoiceApplyStr = result_map.get("list").toString();
                    json = net.sf.json.JSONArray.fromObject(openInvoiceApplyStr);

                    List<BuyorderVo> list = (List<BuyorderVo>) json.toCollection(json, BuyorderVo.class);
                    // map.put("buyorderList", list);

                    if (list != null && list.size() > 0) {
                        for (BuyorderVo bv : list) {
                            // 采购单归属：创建人
                            bv.setUserName(getUserNameByUserId(bv.getUserId()));

                            // 归属采购：供应商的归属
                            if (ObjectUtils.notEmpty(bv.getTraderId())) {
                                User user = userMapper.getUserByTraderId(bv.getTraderId(), ErpConst.TWO);
                                if (user != null) {
                                    bv.setHomePurchasing(getUserNameByUserId(user.getUserId()));
                                }
                            }
                            // 采购部门
                            if (ObjectUtils.notEmpty(bv.getOrgId())) {
                                bv.setBuyDepartmentName(
                                        organizationMapper.selectByPrimaryKey(bv.getOrgId()).getOrgName());
                            }
                            // 采购人员
                            if (ObjectUtils.notEmpty(bv.getUserId())) {
                                bv.setBuyPerson(getUserNameByUserId(bv.getUserId()));
                            }
                            riskCheckService.setBuyorderGoodsIsRiskInfo(bv);
                        }
                    }
                    map.put("list", list);

                    buyorderVo = (BuyorderVo) JSONObject.toBean(JSONObject.fromObject(result_map.get("buyorder")),
                            BuyorderVo.class);
                    map.put("buyorderVo", buyorderVo);

                    page = result.getPage();
                    map.put("page", page);
                }
            }
            return map;
        } catch (IOException e) {
            logger.error("getBuyorderVoPage->error:", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * <b>Description:</b><br>
     * 获取新增页采购订单的详情
     *
     * @param buyorder
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年7月19日 上午10:13:30
     */
    @Override
    public BuyorderVo getAddBuyorderVoDetail(Buyorder buyorder, User user) {
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            BuyorderVo buyorderVo = new BuyorderVo();
            buyorderVo.setCompanyId(1);
            buyorderVo.setBuyorderId(buyorder.getBuyorderId());
            buyorderVo.setBuyorderNo(buyorder.getBuyorderNo());
            buyorderVo.setCompanyId(1);
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.GET_BUYORDER_DETAIL,
                    buyorderVo, clientId, clientKey, TypeRef);
            if (result == null || result.getCode() == -1) {
                return null;
            }
            JSONObject json = JSONObject.fromObject(result.getData());
            BuyorderVo bv = JsonUtils.readValue(json.toString(), BuyorderVo.class);
            if (bv != null && bv.getBuyorderGoodsVoList() != null && bv.getBuyorderGoodsVoList().size() > 0) {
                bv.setCreateName(getUserNameByUserId(bv.getCreator()));
                bv.setHomePurchasing(getUserNameByUserId(bv.getUserId()));
                bv.setBuyDepartmentName(getOrgNameByOrgId(bv.getOrgId()));
                List<BuyorderGoodsVo> list = bv.getBuyorderGoodsVoList();
                Integer bgSum = 0;
                BigDecimal bgAmount = new BigDecimal("0.00");
                BigDecimal bgAmountAct = new BigDecimal("0.00");
                for (BuyorderGoodsVo bgv : list) {
                    Integer buySum = 0;
                    if (bgv.getSaleorderGoodsVoList() != null && bgv.getSaleorderGoodsVoList().size() > 0) {
                        List<SaleorderGoodsVo> saleList = bgv.getSaleorderGoodsVoList();
                        for (SaleorderGoodsVo sgv : saleList) {
                            sgv.setApplicantName(getUserNameByUserId(sgv.getUserId()));
                            if (sgv.getDeliveryDirect() != null && sgv.getDeliveryDirect() == 1) {
                                //buySum += sgv.getNum() - (sgv.getBuyNum() == null ? 0 : sgv.getBuyNum());
                                buySum += sgv.getNeedBuyNum() == null ? 0 : sgv.getNeedBuyNum();
                            } else {
                                buySum += sgv.getNeedBuyNum() == null ? 0 : sgv.getNeedBuyNum();
                            }
                            bv.setSaleorderNo(sgv.getSaleorderNo());
                            // 备货单采购价取默认备货单的采购价
                            if ("BH".equals(sgv.getSaleorderNo().substring(0, 2))) {
                                bgv.setPrice(sgv.getPrice());
                            }
                        }
                        bgv.setBuySum(buySum);
                        bgSum += buySum;
                    }
                    if (bgv.getNum() == null || bgv.getNum() == 0) {
                        bgv.setNum(buySum);
                    }
                    if (bgv.getPrice() != null) {
                        BigDecimal b1 = new BigDecimal(bgv.getNum());
                        // BigDecimal b2 = new BigDecimal(bgv.getPrice());
                        BigDecimal bgvAmount = b1.multiply(bgv.getPrice());
                        bgv.setOneBuyorderGoodsAmount(bgvAmount);
                    }
                    if (bgv.getActualPurchasePrice() != null) {
                        BigDecimal b1 = new BigDecimal(bgv.getNum());
                        BigDecimal bgvAmountAct = b1.multiply(bgv.getActualPurchasePrice());
                        bgv.setOneBuyorderGoodsAmountAct(bgvAmountAct);
                    }
                    bgAmount = bgAmount.add(bgv.getOneBuyorderGoodsAmount() == null ? new BigDecimal(0) : bgv.getOneBuyorderGoodsAmount());
                    bgAmountAct = bgAmountAct.add(bgv.getOneBuyorderGoodsAmountAct() == null ? new BigDecimal(0) : bgv.getOneBuyorderGoodsAmountAct());
                    bgv.setUserList(rCategoryJUserMapper.getUserByCategory(bgv.getCategoryId(), bgv.getCompanyId()));
                }
                bv.setBuyorderAmount(bgAmount);
                bv.setBuyorderAmountAct(bgAmountAct);
                bv.setBuyorderSum(bgSum);
                //查询出订单的GE合同编号和销售订单编号并赋值，更改GE商品标志位
                editBuyorderGEInfo(bv);
            }
            return bv;
        } catch (IOException e) {
            throw new RuntimeException();
        }
    }

    /**
     * 查询出订单的GE合同编号和销售订单编号、主机和对应的所有探头并赋值，更改GE商品标志位
     */
    @Override
    public void editBuyorderGEInfo(BuyorderVo bv) {
        if (GE_TRADER_SKU != null) {
            //将GE合同编号和GE销售合同编号、主机、配件与buyorderGoodsId绑定
            if (CollectionUtils.isNotEmpty(bv.getBuyorderGoodsVoList())) {
                for (BuyorderGoodsVo bgv : bv.getBuyorderGoodsVoList()) {
                    Integer buyorderGoodsId = bgv.getBuyorderGoodsId();
                    //根据buyorderGoodsId，查询出GE合同编号和GE销售合同编号，并赋值
                    BuyorderGoodsVo buyorderGoodsVo = geInfoCollectionMapper.getGeNoInfo(bgv.getBuyorderGoodsId());
                    if(buyorderGoodsVo != null) {
                        bgv.setGeSaleContractNo(buyorderGoodsVo.getGeSaleContractNo());
                        bgv.setGeContractNo(buyorderGoodsVo.getGeContractNo());
                        bgv.setMasterSlaveLists(getMasterSlave(buyorderGoodsId));
                    }
                }
                //修改buyorderGoods中标志位geSkuFlag，是GE商品 1，不是默认值0；buyorder中标志位geInfoFlag，有GE商品 1，没有0
                GeTraderSku geTraderSku = JSON.parseObject(GE_TRADER_SKU, GeTraderSku.class);
                bv.getBuyorderGoodsVoList().forEach(item -> {
                    boolean containsGeSku = CollectionUtils.isNotEmpty(geTraderSku.getSkuList()) && geTraderSku.getSkuList().contains(item.getSku());
                    if (containsGeSku && StringUtils.isEmpty(item.getGeContractNo()) && StringUtils.isEmpty(item.getGeSaleContractNo())) {
                        item.setGeSkuFlag(1);
                        bv.setGeInfoFlag(1);
                    }
                });
            }
        }
    }


    /**
     * 根据buyorderGoodsId，查询出所有主机和对应的所有探头
     *
     * @param buyorderGoodsId
     * @return
     */
    @Override
    public List<MasterSlaveList> getMasterSlave(Integer buyorderGoodsId) {
        List<MasterSlaveList> masterSlaveLists = new ArrayList<>();
        List<VBuyorderSncodeMaster> masters = vBuyorderSncodeMasterMapper.getGeMasterInfo(buyorderGoodsId);
        if (CollectionUtils.isNotEmpty(masters)) {
            for (VBuyorderSncodeMaster mas : masters) {
                MasterSlaveList masterSlaveList = new MasterSlaveList();
                masterSlaveList.setvBuyorderSncodeMaster(mas);
                List<VBuyorderSncodeSlave> slaves = vBuyorderSncodeSlaveMapper.getGeSlaveInfo(mas.getBuyorderSncodeMasterId());
                masterSlaveList.setvBuyorderSncodeSlaves(slaves);
                masterSlaveLists.add(masterSlaveList);
            }
            return masterSlaveLists;
        }
        return null;
    }


    /**
     * <b>Description:</b><br>
     * 获取采购订单的详情
     *
     * @param buyorder
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年7月19日 上午10:13:30
     */
    @Override
    public BuyorderVo getBuyorderVoDetail(Buyorder buyorder, User user) {
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            BuyorderVo buyorderVo = new BuyorderVo();
            buyorderVo.setCompanyId(user.getCompanyId());
            buyorderVo.setBuyorderId(buyorder.getBuyorderId());
            buyorderVo.setBuyorderNo(buyorder.getBuyorderNo());
            buyorderVo.setFlag(buyorder.getFlag());
            log.info("调用db接口{}", JSON.toJSONString(buyorderVo));
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.GET_BUYORDER_DETAIL,
                    buyorderVo, clientId, clientKey, TypeRef);
            if (result == null || result.getCode() == -1) {
                return null;
            }
            JSONObject json = JSONObject.fromObject(result.getData());
            BuyorderVo bv = JsonUtils.readValue(json.toString(), BuyorderVo.class);
            log.info("调用db接口返回值{}", JSON.toJSONString(bv));
            editBuyorderGEInfo(bv);
            List<User> userList = userMapper.getAllUserList(user.getCompanyId());
            if (bv != null && bv.getBuyorderGoodsVoList() != null && bv.getBuyorderGoodsVoList().size() > 0) {
                for (User us : userList) {
                    if (us.getUserId().equals(bv.getCreator())) {
                        bv.setCreateName(us.getUsername());
                    }
                    if (us.getUserId().equals(bv.getUserId())) {
                        bv.setHomePurchasing(us.getUsername());
                    }
                }
                bv.setBuyDepartmentName(getOrgNameByOrgId(bv.getOrgId()));
                List<BuyorderGoodsVo> list = bv.getBuyorderGoodsVoList();
                Integer bgSum = 0;
                BigDecimal bgAmount = new BigDecimal("0.00");
                BigDecimal bgAmountAct = new BigDecimal("0.00");
                for (BuyorderGoodsVo bgv : list) {
                    Integer buySum = 0;
                    if (bgv.getSaleorderGoodsVoList() != null && bgv.getSaleorderGoodsVoList().size() > 0) {
                        List<SaleorderGoodsVo> saleList = bgv.getSaleorderGoodsVoList();
                        for (SaleorderGoodsVo sgv : saleList) {
                            for (User us : userList) {
                                if (us.getUserId().equals(sgv.getUserId())) {
                                    sgv.setApplicantName(us.getUsername());
                                    break;
                                }

                            }

                            buySum += sgv.getNum() - (sgv.getBuyNum() == null ? 0 : sgv.getBuyNum());
                            bv.setSaleorderNo(sgv.getSaleorderNo());
                            bv.setSaleorderType(sgv.getOrderType());
                        }
                        bgv.setBuySum(buySum);
                    }
                    if (bgv.getPrice() != null && bgv.getNum() != null) {
                        BigDecimal b1 = new BigDecimal(bgv.getNum());
                        BigDecimal bgvAmount = b1.multiply(bgv.getPrice());
                        bgv.setOneBuyorderGoodsAmount(bgvAmount);
                    }
                    if (bgv.getActualPurchasePrice() != null && bgv.getNum() != null) {
                        BigDecimal b1 = new BigDecimal(bgv.getNum());
                        BigDecimal bgvAmountAct = b1.multiply(bgv.getActualPurchasePrice());
                        bgv.setOneBuyorderGoodsAmountAct(bgvAmountAct);
                    }
                    if (bgv.getNum() != 0) {
                        bgSum += bgv.getNum();
                    } else {
                        // bgv.setNum(buySum);
                        // bgSum += buySum;
                    }
                    bgAmount = bgAmount.add(bgv.getOneBuyorderGoodsAmount());
                    bgAmountAct = bgAmountAct.add(bgv.getOneBuyorderGoodsAmountAct() == null ? new BigDecimal(0) : bgv.getOneBuyorderGoodsAmountAct());
                    bgv.setUserList(rCategoryJUserMapper.getUserByCategory(bgv.getCategoryId(), bgv.getCompanyId()));
                    bgv.setTempPrice(bgv.getPrice());
                }
                bv.setBuyorderAmount(bgAmount);
                bv.setBuyorderAmountAct(bgAmountAct);
                bv.setBuyorderSum(bgSum);
            }
            if (bv != null && bv.getAttachmentList() != null && bv.getAttachmentList().size() > 0) {
                List<Attachment> list = bv.getAttachmentList();
                for (Attachment attachment : list) {
                    for (User us : userList) {
                        if (us.getUserId().equals(attachment.getCreator())) {
                            attachment.setUsername(us.getUsername());
                            break;
                        }
                    }

                }
            }
            if (bv != null && bv.getWarehouseGoodsOperateLogVoList() != null
                    && bv.getWarehouseGoodsOperateLogVoList().size() > 0) {
                List<WarehouseGoodsOperateLogVo> list = bv.getWarehouseGoodsOperateLogVoList();
                for (WarehouseGoodsOperateLogVo av : list) {
                    for (User us : userList) {
                        if (us.getUserId().equals(av.getCreator())) {
                            av.setOperaterName(us.getUsername());
                            break;
                        }
                    }

                }
            }
            if (bv != null && bv.getExpressList() != null && bv.getExpressList().size() > 0) {
                List<Express> list = bv.getExpressList();
                for (Express express : list) {
                    for (User us : userList) {
                        if (us.getUserId().equals(express.getCreator())) {
                            express.setUpdaterUsername(us.getUsername());
                            break;
                        }
                    }
                }
            }

            if (bv != null && bv.getInvoiceList() != null && bv.getInvoiceList().size() > 0) {
                List<Invoice> list = bv.getInvoiceList();
                for (Invoice invoice : list) {
                    for (User us : userList) {
                        if (us.getUserId().equals(invoice.getCreator())) {
                            invoice.setCreatorName(us.getUsername());
                            break;
                        }
                    }

                }
            }
            // 采购人员
            if (ObjectUtils.notEmpty(bv.getUserId())) {
                for (User us : userList) {
                    if (us.getUserId().equals(bv.getUserId())) {
                        bv.setBuyPerson(us.getUsername());
                        break;
                    }
                }
            }
            return bv;
        } catch (IOException e) {
            throw new RuntimeException();
        }
    }



    /**
     * 采购单SkuId根据物流单分组
     * @param bv 采购单详情
     */
    @Override
    public void logisticsGroupingByBuyOrderGoodsId(BuyorderVo bv) {
        if(bv==null){
return;
        }
        List<BuyorderGoodsVo> bgvList = bv.getBuyorderGoodsVoList();
        if(CollectionUtils.isEmpty(bgvList)){
            return;
        }

        List<BuyorderGoodsVo> returnBgvList = new ArrayList<>();

        // 一个采购单SKU对应多个物流详情明细，细分
        bgvList.forEach(bgv -> {
            List<Express> expressList = expressDetailMapper.selectAllByRelatedKey(bgv);
            if (CollectionUtils.isEmpty(expressList)) {
                return;
            }
            expressList.forEach(express -> {
                if(express == null){
                    return;
                }
                BuyorderGoodsVo tempBuyOrderGoodsVo = new BuyorderGoodsVo();
                BeanUtils.copyProperties(bgv, tempBuyOrderGoodsVo);
                tempBuyOrderGoodsVo.setExpressId(express.getExpressId());
                tempBuyOrderGoodsVo.setLogisticsNo(express.getLogisticsNo());
                tempBuyOrderGoodsVo.setDeliveryTime(express.getDeliveryTime());
                ExpressDetail expressDetail = expressDetailMapper.selectOneByRelatedKey(tempBuyOrderGoodsVo);
                tempBuyOrderGoodsVo.setCurrentDeliveryNum(Integer.valueOf(0));
                if (!Objects.isNull(expressDetail) && !Objects.isNull(expressDetail.getNum())) {
                    tempBuyOrderGoodsVo.setCurrentDeliveryNum(expressDetail.getNum());
                }
                returnBgvList.add(tempBuyOrderGoodsVo);
            });
        });

        // 根据物流分组，发货时间倒序、物流号倒序
        returnBgvList.sort((before, after) -> {

            if (before.getDeliveryTime() == null || after.getDeliveryTime() == null) {
                return 0;
            }
            int deliveryTimeCompare = before.getDeliveryTime().compareTo(after.getDeliveryTime());
            int expressCompare = before.getExpressId().compareTo(after.getExpressId());
            if (deliveryTimeCompare == 0) {
                return expressCompare;
            }
            return deliveryTimeCompare;

        });

        bv.setBuyorderGoodsVoList(returnBgvList);
    }


    /**
     * <b>Description:</b><br>
     * 保存新增采购订单
     *
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年7月11日 上午9:27:14
     */
    @Override
    // @SystemServiceLog(operationType = "add",desc = "保存新增采购订单")
    public ResultInfo<?> saveOrUpdateBuyorderVo(BuyorderVo buyorderVo, User user) {
        //校验专项发货的采购单创建
        ResultInfo resultInfo = checkBuyorderSpecial(buyorderVo,null);
        if(resultInfo.getCode() != 0){
            return resultInfo;
        }
        if (buyorderVo != null && buyorderVo.getBuyorderId() == null) {
            buyorderVo.setCreator(user.getUserId());
            // 订单归属人和归属部门指产品负责人和产品负责人部门
            buyorderVo.setUserId(user.getUserId());
            buyorderVo.setOrgId(user.getOrgId());
            buyorderVo.setAddTime(DateUtil.sysTimeMillis());
            buyorderVo.setCreator(user.getUserId());
            buyorderVo.setCompanyId(user.getCompanyId());
        }
        buyorderVo.setUpdater(user.getUserId());
        buyorderVo.setModTime(DateUtil.sysTimeMillis());
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {

            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.SAVE_ADD_BUYORDER,
                    buyorderVo, clientId, clientKey, TypeRef);
            if (result != null && result.getCode().equals(0)) {
                //更新采购单updataTime
                Integer buyOrderId = (Integer) result.getData();
                String operateType = OrderDataUpdateConstant.BUY_VP_ORDER_GENERATE;
                if (buyorderVo.getOrderType().equals(1)) {
                    operateType = OrderDataUpdateConstant.BUY_VB_ORDER_GENERATE;
                }
                orderCommonService.updateBuyOrderDataUpdateTime(buyOrderId, null, operateType);

                // 订单流 - 更新BUYORDER的SALEORDER_NOS字段
                List<Integer> idsList = new ArrayList<>();
                CollectionUtils.collect(Arrays.asList(buyorderVo.getSaleorderGoodsIds().split(",")), input -> Integer.valueOf(input.toString().split("\\|")[1]), idsList);
                List<String> saleorderNoList = saleorderMapper.getSaleorderNoList(idsList);
                Buyorder buyorder = new Buyorder();
                buyorder.setBuyorderId(buyOrderId);
                buyorder.setSaleorderNos(saleorderNoList.stream().map(String::valueOf).collect(Collectors.joining(",")));
                buyorderMapper.updateByPrimaryKeySelective(buyorder);
            }
            return result;
        } catch (Exception e) {
            throw new RuntimeException();
        }
    }

    /**
     * <b>Description:</b><br>
     * 查询待采购列表
     *
     * @param goodsVo
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年7月13日 下午4:51:06
     */
    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> getSaleorderGoodsVoList(GoodsVo goodsVo) {
        long l1 = System.currentTimeMillis();
        List<GoodsVo> gvList = new ArrayList<>();
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<GoodsVo>>> TypeRef = new TypeReference<ResultInfo<List<GoodsVo>>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.GET_SALESORDERGOODS_LIST,
                    goodsVo, clientId, clientKey, TypeRef);

            if (result == null || result.getCode() == -1) {
                return null;
            }
            List<GoodsVo> list = (List<GoodsVo>) result.getData();
            gvList.addAll(list);
            if (list != null && list.size() > 0 && result.getStatus() > 20) {
                int size = (int) Math.ceil((double) result.getStatus() / (double) 20);
                Integer currentCount = 2;
                for (int i = 0; i < size - 1; i++) {
                    goodsVo.setCurrentCount(currentCount);
                    result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.GET_PURCHASE_LIST, goodsVo,
                            clientId, clientKey, TypeRef);
                    if (result == null || result.getCode() == -1) {
                        return null;
                    }
                    list = (List<GoodsVo>) result.getData();
                    gvList.addAll(list);
                    currentCount++;
                }

            }

            // 含有压缩方式
            // String result = (String) HttpClientUtils.compressPost(httpUrl +
            // ErpConst.GET_SALESORDERGOODS_LIST,
            // goodsVo, clientId, clientKey, TypeRef);
            // if (result == null || "".equals(result)) {
            // return null;
            // }
            // JSONObject json = JSONObject.fromObject(result);
            // String str = json.getString("data");
            // str = GZipUtils.unCompress(json.getString("data").toString());
            // JSONArray jsonArray = JSONArray.fromObject(str);
            // List<GoodsVo> list =
            // (List<GoodsVo>)JSONArray.toCollection(jsonArray, GoodsVo.class);
            // gvList.addAll(list);
            // Integer listSize = Integer.valueOf(json.getString("status"));
            // if(list != null && list.size() > 0 && listSize > 30){
            // int size = (int) Math.ceil((double)listSize/(double)30);
            // Integer currentCount = 2;
            // for(int i = 0;i<size-1;i++){
            // goodsVo.setCurrentCount(currentCount);
            // result = (String) HttpClientUtils.compressPost(httpUrl +
            // ErpConst.GET_PURCHASE_LIST,
            // goodsVo, clientId, clientKey, TypeRef);
            // if (result == null || "".equals(result)) {
            // return null;
            // }
            // json = JSONObject.fromObject(result);
            // str = json.getString("data");
            // str = GZipUtils.unCompress(json.getString("data").toString());
            // jsonArray = JSONArray.fromObject(str);
            // list = (List<GoodsVo>)JSONArray.toCollection(jsonArray,
            // GoodsVo.class);
            // gvList.addAll(list);
            // currentCount ++;
            // }
            //
            // }
            if (gvList != null && gvList.size() > 0) {
                Integer buySum = 0;// 待采购订单产品数量总计
                List<User> userList = rCategoryJUserMapper.batchUserByCategory(gvList, goodsVo.getCompanyId());
                List<User> allUserList = userMapper.getAllUserList(goodsVo.getCompanyId());
                for (GoodsVo gv : gvList) {
                    JSONArray jsonarray = JSONArray.fromObject(gv.getSgvList());
                    List<SaleorderGoodsVo> svList =
                            (List<SaleorderGoodsVo>) JSONArray.toCollection(jsonarray, SaleorderGoodsVo.class);
                    gv.setSgvList(svList);
                    // List<SaleorderGoodsVo> svList = gv.getSgvList();
                    Integer proBuySum = 0;
                    for (SaleorderGoodsVo sv : svList) {
                        for (User u : allUserList) {
                            if (u.getUserId().intValue() == sv.getUserId()) {
                                sv.setApplicantName(u.getUsername());
                                break;
                            }

                        }

                        proBuySum += sv.getNum() - sv.getBuyNum();
                    }
                    buySum += proBuySum;
                    gv.setProBuySum(proBuySum);

                    // gv.setUserList(rCategoryJUserMapper.getUserByCategory(gv.getCategoryId(),
                    // gv.getCompanyId()));

                    for (User u : userList) {
                        if (gv.getCategoryId().intValue() == u.getCategoryId().intValue()) {
                            if (gv.getUserList() == null) {
                                List<User> uList = new ArrayList<>();
                                uList.add(u);
                                gv.setUserList(uList);
                            } else {
                                gv.getUserList().add(u);
                            }
                        }
                    }

                }
                Map<String, Object> map = new HashMap<>();
                map.put("buySum", buySum);
                map.put("list", gvList);
                long l2 = System.currentTimeMillis();
                System.out.println(l2 - l1);
                return map;
            }
            return null;
        } catch (IOException e) {
            logger.info("接口超时异常2,{}",new Object[]{com.alibaba.fastjson.JSONObject.toJSONString(goodsVo)});
            throw new RuntimeException();
        }
    }

    /**
     * <b>Description:</b><br>
     * 查询待采购列表分页
     *
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年7月13日 下午4:51:06
     */
    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> getSaleorderGoodsVoListPage(GoodsVo goodsVo, Page page) {

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<SaleorderVo>>> TypeRef =
                new TypeReference<ResultInfo<List<SaleorderVo>>>() {
                };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.GET_SALESORDERGOODS_LIST,
                    goodsVo, clientId, clientKey, TypeRef, page);
            if (result == null || result.getCode() == -1) {
                return null;
            }
            List<SaleorderVo> list = (List<SaleorderVo>) result.getData();
            page = result.getPage();

            if (list != null && list.size() > 0) {
                Integer buySum = 0;// 待采购订单产品数量总计

                List<User> allUserList = userMapper.getAllUserList(goodsVo.getCompanyId());

                for (SaleorderVo gv : list) {
                    Saleorder saleOrderById = saleorderMapper.getSaleOrderById(gv.getSaleorderId());
                    gv.setLogisticsComments(saleOrderById.getLogisticsComments());

                    List<User> userList = rCategoryJUserMapper.batchUserCategoryBySaleorderVo(gv.getSgvList(),
                            goodsVo.getCompanyId());
                    Integer proBuySum = 0;
                    for (User u : allUserList) {
                        if (gv.getOrderType() == 2 && gv.getCreator().intValue() == u.getUserId().intValue()) {// 备货单的申请人取创建人
                            gv.setApplicantName(u.getUsername());
                            break;
                        } else if (gv.getOrderType() != 2 && u.getUserId().intValue() == gv.getValidUserId().intValue()) {
                            gv.setApplicantName(u.getUsername());
                            break;
                        } else if (gv.getOrderType() != 2 && gv.getValidUserId().intValue() == 0) {
                            User traderUser=userMapper.getUserByTraderId(gv.getTraderId(), 1);
                            gv.setApplicantName(traderUser == null ? ""
                                    : traderUser.getUsername());
                            break;
                        }

                    }
                    for (SaleorderGoodsVo sv : gv.getSgvList()) {
                        if (sv.getDeliveryDirect() == 0) {
                            proBuySum += sv.getNeedBuyNum() == null ? 0 : sv.getNeedBuyNum();
                        } else {
                            proBuySum += sv.getNum() - (sv.getBuyNum() == null ? 0 : sv.getBuyNum());
                        }
                        for (User u : userList) {
                            if (sv.getCategoryId() != null
                                    && sv.getCategoryId().intValue() == u.getCategoryId().intValue()) {
                                if (sv.getUserList() == null) {
                                    List<User> uList = new ArrayList<>();
                                    uList.add(u);
                                    sv.setUserList(uList);
                                } else {
                                    sv.getUserList().add(u);
                                }
                            }
                        }
                    }
                    buySum += proBuySum;
                    gv.setProBuySum(proBuySum);
                }
                Map<String, Object> map = new HashMap<>();
                map.put("buySum", buySum);
                map.put("list", list);
                map.put("page", page);
                return map;
            }
            return null;
        } catch (IOException e) {
            logger.info("接口超时异常3,{},{}",new Object[]{com.alibaba.fastjson.JSONObject.toJSONString(goodsVo), com.alibaba.fastjson.JSONObject.toJSONString(page)});
            throw new RuntimeException();
        }
    }


    public Map<String, Object> getUnPurchasingOrderList(GoodsVo goodsVo, Page page) {

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<SaleorderVo>>> TypeRef =
                new TypeReference<ResultInfo<List<SaleorderVo>>>() {
                };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.GET_SALESORDERGOODS_LIST,
                    goodsVo, clientId, clientKey, TypeRef, page);
            if (result == null || result.getCode() == -1) {
                return null;
            }
            List<SaleorderVo> list = (List<SaleorderVo>) result.getData();
            page = result.getPage();

            if (list != null && list.size() > 0) {
                Integer buySum = 0;// 待采购订单产品数量总计

                List<User> allUserList = userMapper.getAllUserList(goodsVo.getCompanyId());

                for (SaleorderVo gv : list) {
                    Saleorder saleOrderById = saleorderMapper.getSaleOrderById(gv.getSaleorderId());
                    gv.setLogisticsComments(saleOrderById.getLogisticsComments());

                    List<User> userList = rCategoryJUserMapper.batchUserCategoryBySaleorderVo(gv.getSgvList(),
                            goodsVo.getCompanyId());
                    Integer proBuySum = 0;
                    for (User u : allUserList) {
                        if (gv.getOrderType() == 2 && gv.getCreator().intValue() == u.getUserId().intValue()) {// 备货单的申请人取创建人
                            gv.setApplicantName(u.getUsername());
                            break;
                        } else if (gv.getOrderType() != 2 && u.getUserId().intValue() == gv.getValidUserId().intValue()) {
                            gv.setApplicantName(u.getUsername());
                            break;
                        } else if (gv.getOrderType() != 2 && gv.getValidUserId().intValue() == 0) {
                            User traderUser=userMapper.getUserByTraderId(gv.getTraderId(), 1);
                            gv.setApplicantName(traderUser == null ? ""
                                    : traderUser.getUsername());
                            break;
                        }

                    }
                    for (SaleorderGoodsVo sv : gv.getSgvList()) {
                        if (sv.getDeliveryDirect() == 0) {
                            proBuySum += sv.getNeedBuyNum() == null ? 0 : sv.getNeedBuyNum();
                        } else {
                            proBuySum += sv.getNum() - (sv.getBuyNum() == null ? 0 : sv.getBuyNum());
                        }
                        for (User u : userList) {
                            if (sv.getCategoryId() != null
                                    && sv.getCategoryId().intValue() == u.getCategoryId().intValue()) {
                                if (sv.getUserList() == null) {
                                    List<User> uList = new ArrayList<>();
                                    uList.add(u);
                                    sv.setUserList(uList);
                                } else {
                                    sv.getUserList().add(u);
                                }
                            }
                        }
                    }
                    buySum += proBuySum;
                    gv.setProBuySum(proBuySum);
                }
                Map<String, Object> map = new HashMap<>();
                map.put("buySum", buySum);
                map.put("list", list);
                map.put("page", page);
                return map;
            }

            return null;
        } catch (Exception e) {
            logger.info("接口超时异常1,{},{}",new Object[]{com.alibaba.fastjson.JSONObject.toJSONString(goodsVo), com.alibaba.fastjson.JSONObject.toJSONString(page)});
            logger.error("新异常getUnPurchasingOrderList",e);
            throw new RuntimeException();
        }
    }

    /**
     * <b>Description:</b><br>
     * 忽略待采购订单
     *
     * @param saleorderGoodsIds
     * @param user
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年7月21日 下午5:23:38
     */
    @Override
    // @SystemServiceLog(operationType = "eidt",desc = "忽略待采购订单")
    public ResultInfo<?> saveIgnore(String saleorderGoodsIds, User user) {
        if (saleorderGoodsIds == null || "".equals(saleorderGoodsIds) || saleorderGoodsIds.split(",").length == 0) {
            return null;
        }
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            List<SaleorderGoods> list = new ArrayList<>();
            SaleorderGoods saleorderGoods = null;
            String saleorderGoodsIDs[] = saleorderGoodsIds.split(",");
            for (int i = 0; i < saleorderGoodsIDs.length; i++) {
                saleorderGoods = new SaleorderGoods();
                saleorderGoods.setUpdater(user.getUserId());
                saleorderGoods.setModTime(DateUtil.sysTimeMillis());
                saleorderGoods.setIgnoreTime(DateUtil.sysTimeMillis());
                saleorderGoods.setIgnoreUserId(user.getUserId());
                saleorderGoods.setIsIgnore(ErpConst.ONE);
                saleorderGoods.setSaleorderGoodsId(Integer.valueOf(saleorderGoodsIDs[i].split("\\|")[1]));
                list.add(saleorderGoods);
            }
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.SAVE_IGNORE, list, clientId,
                    clientKey, TypeRef);
            return result;
        } catch (IOException e) {
            throw new RuntimeException();
        }
    }

    /**
     * <b>Description:</b><br>
     * 查询已忽略订单列表分页信息
     *
     * @param saleorderGoodsVo
     * @param page
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年7月11日 上午9:25:35
     */
    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> getIgnoreSaleorderPage(SaleorderGoodsVo saleorderGoodsVo, Page page) {
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<SaleorderGoodsVo>>> TypeRef =
                new TypeReference<ResultInfo<List<SaleorderGoodsVo>>>() {
                };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.GET_IGNORE_SALEORDER_PAGE,
                    saleorderGoodsVo, clientId, clientKey, TypeRef, page);
            if (result == null || result.getCode() == -1) {
                return null;
            }
            List<SaleorderGoodsVo> list = (List<SaleorderGoodsVo>) result.getData();
            for (SaleorderGoodsVo sgv : list) {
                sgv.setApplicantName(getUserNameByUserId(sgv.getUserId()));
                sgv.setIgnoreName(getUserNameByUserId(sgv.getIgnoreUserId()));

            }
            Map<String, Object> map = new HashMap<>();
            map.put("list", list);
            page = result.getPage();
            map.put("page", page);
            return map;
        } catch (IOException e) {
            throw new RuntimeException();
        }
    }

    /**
     * <b>Description:</b><br>
     * 新增采购页面提交
     *
     * @param buyorder
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年7月11日 上午9:27:14
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public ResultInfo<?> saveEditBuyorderAndBuyorderGoods(Buyorder buyorder, HttpServletRequest request) {
        //打印入参日志
        logger.info("新增采购页面提交参数：buyorder:{}", JSON.toJSONString(buyorder));
        Map<String, Object> map = new HashMap<>();
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        GeTraderSku geTraderSku = JSON.parseObject(GE_TRADER_SKU, GeTraderSku.class);
        ResultInfo resultInfo = new ResultInfo(0, "GE信息代码校验处异常！");
        if (geTraderSku != null) {
            BuyorderVo bv = getAddBuyorderVoDetail(buyorder, user);
            boolean isGeTrader = buyorder.getTraderId().equals(geTraderSku.getTraderId());
            if (CollectionUtils.isNotEmpty(bv.getBuyorderGoodsVoList())) {
                bv.getBuyorderGoodsVoList().forEach(item -> {
                    boolean containsGeSku = CollectionUtils.isNotEmpty(geTraderSku.getSkuList()) && geTraderSku.getSkuList().contains(item.getSku());
                    if (!isGeTrader && containsGeSku && StringUtils.isNotEmpty(item.getGeContractNo()) && StringUtils.isNotEmpty(item.getGeSaleContractNo())) {
                        resultInfo.setCode(-1);
                        resultInfo.setMessage("该采购订单GE信息录入错误，请检查供应商或删除录入的信息！");
                    }
                });
            }
        }
        if (resultInfo.getCode() == -1) {
            return resultInfo;
        }
        // 判断是否是首次提交预计发货时间和预计收货时间
        if (request.getParameter("firstSendReceiveFlag") != null && String.valueOf(1).equals(request.getParameter("firstSendReceiveFlag"))) {
            logger.info("firstSendReceiveFlag --> " + request.getParameter("firstSendReceiveFlag"));
            // 存放首次提交预计发货时间标识
            map.put("firstSendReceiveFlag", 1);
        }

        buyorder.setUpdater(user.getUserId());
        buyorder.setModTime(DateUtil.sysTimeMillis());

        // 判断之前是否已经使用了返利
        Buyorder oldBuyorder = buyorderMapper.selectByPrimaryKey(buyorder.getBuyorderId());
        if (!ErpConst.ZERO.equals(oldBuyorder.getStatus())) {
            return new ResultInfo<>(-1, "该采购单不是待确认状态，无法提交！");
        }
        List<BuyorderGoodsVo> buyorderGoodsVoList = buyorderGoodsMapper.getBuyorderGoodsListByBuyorderId(buyorder.getBuyorderId());
        Boolean isExistSettlement = CollUtil.isNotEmpty(buyorderGoodsVoList) && buyorderGoodsVoList.stream()
                .anyMatch(goods -> Objects.nonNull(goods.getRebateAmount()) && goods.getRebateAmount().compareTo(BigDecimal.ZERO) > 0);
        if (isExistSettlement) {
            buyorder.setOldRebateGoodsList(buyorderGoodsVoList);
            buyorder.setOldTraderId(oldBuyorder.getTraderId());
        }

        // 判断当前是否使用了返利
        List<BuyorderGoods> goodsList = buyorder.getGoodsList();
        Boolean isUsedRebate = CollUtil.isNotEmpty(goodsList) && goodsList.stream()
                .anyMatch(goods -> Objects.nonNull(goods.getRebateAmount()) && goods.getRebateAmount().compareTo(BigDecimal.ZERO) > 0);

        // 判断使用的返利是否超过了供应商的可用余额
        if (isUsedRebate) {
            // 当前单据已使用的返利
            BigDecimal usedRebateCharge = buyorderGoodsVoList.stream().map(BuyorderGoodsVo::getRebateAmount).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            TraderSupplierDto traderSupplierDto = traderSupplierApiService.getTraderSupplierByTraderId(buyorder.getTraderId());
            if (Objects.isNull(traderSupplierDto)) {
                log.info("使用供应商返利，未查询到供应商信息，traderId:{}", buyorder.getTraderId());
                return new ResultInfo<>(-1, "使用供应商返利，未查询到供应商信息！");
            }
            SupplierAssetApiDto supplierAsset = supplierAssetApiService.getSupplierAsset(traderSupplierDto.getTraderSupplierId(), SupplierAssetEnum.rebate.getCode());
            if (Objects.isNull(supplierAsset)) {
                log.info("使用供应商返利，未查询到供应商资产信息，traderSupplierId:{}", traderSupplierDto.getTraderSupplierId());
                return new ResultInfo<>(-1, "使用供应商返利，未查询到供应商资产信息！");
            }


            BigDecimal totalRebateAmount = goodsList.stream()
                    .filter(goods -> Objects.nonNull(goods.getRebateAmount()) && goods.getRebateAmount().compareTo(BigDecimal.ZERO) > 0)
                    .map(BuyorderGoods::getRebateAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            // 做对比时，供应商可用返利需要加上当前订单的使用返利
            if (totalRebateAmount.compareTo(supplierAsset.getApplyAsset().add(usedRebateCharge)) > 0) {
                log.info("使用的返利金额超过了供应商的可用余额，traderSupplierId:{}, totalRebateAmount:{}, applyAsset:{}",
                        traderSupplierDto.getTraderSupplierId(), totalRebateAmount, supplierAsset.getApplyAsset());
                return new ResultInfo<>(-1, "使用的返利金额超过了供应商的可用余额！");
            }
        }

        if (!buyorder.getStatus().equals(ErpConst.THREE) && !buyorder.getStatus().equals(ErpConst.ONE)) {
            List<BuyorderGoods> bgList = getBuyorderGoods(request, user);
            //VDERP-14362 销售采购单如果商品都不是赠品，则采购单改为非赠品采购单
            if (buyorder.getOrderType() == 0 && bgList.stream().allMatch(g -> ErpConst.ZERO.equals(g.getIsGift()))) {
                buyorder.setIsGift(0);
            }

            // VDERP-14753
            if (bgList.stream().allMatch(g -> ErpConst.ONE.equals(g.getIsGift())) || bgList.stream().allMatch(g -> BigDecimal.ZERO.compareTo(g.getPrice()) == 0)) {
                buyorder.setIsGift(1);
            }
            List<RBuyorderSaleorder> sgList = getSaleorderGoods(request, user);
            List<BuyorderGoods> orderGoodsList = buyorder.getGoodsList();
            //赋值buyorderGoods的返利信息
            bgList.forEach(b -> orderGoodsList.forEach(o -> {
                if (Objects.equals(o.getBuyorderGoodsId(), b.getBuyorderGoodsId())){
                    b.setRebatePrice(o.getRebatePrice());
                    b.setRebateAmount(o.getRebateAmount());
                    b.setActualPurchasePrice(o.getActualPurchasePrice());
                }
            }));
            logger.info("bgList : " + bgList.toString());
            map.put("bgList", bgList);
            map.put("sgList", sgList);

            // 获取页面提交的供应商联系人数据
            String SupplierContacts = request.getParameter("traderContactStr");
            if (ObjectUtils.notEmpty(SupplierContacts)) {
                String[] contacts = SupplierContacts.split("\\|");
                if (contacts.length > 0) {
                    buyorder.setTraderContactId(Integer.valueOf(contacts[0]));
                }
                if (contacts.length > 1) {
                    buyorder.setTraderContactName(contacts[1]);
                }
                if (contacts.length > 2) {
                    buyorder.setTraderContactMobile(contacts[2]);
                }
                if (contacts.length > 3) {
                    buyorder.setTraderContactTelephone(contacts[3]);
                }
            }

            // 获取页面提交的实际供应商联系人数据
            String SupplierContactsAct = request.getParameter("traderContactStrAct");
            if (ObjectUtils.notEmpty(SupplierContactsAct)) {
                String[] contacts = SupplierContactsAct.split("\\|");
                if (contacts.length > 0) {
                    buyorder.setTraderContactIdAct(Integer.valueOf(contacts[0]));
                }
                if (contacts.length > 1) {
                    buyorder.setTraderContactNameAct(contacts[1]);
                }
                if (contacts.length > 2) {
                    buyorder.setTraderContactMobileAct(contacts[2]);
                }
                if (contacts.length > 3) {
                    buyorder.setTraderContactTelephoneAct(contacts[3]);
                }
            }

            // 获取页面提交的供应商地址数据
            String supplierAddress = request.getParameter("traderAddressStr");
            if (ObjectUtils.notEmpty(supplierAddress)) {
                String[] addresss = supplierAddress.split("\\|");
                if (addresss.length > 0) {
                    buyorder.setTraderAddressId(Integer.valueOf(addresss[0]));
                }
                if (addresss.length > 1) {
                    buyorder.setTraderArea(addresss[1]);
                }
                if (addresss.length > 2) {
                    buyorder.setTraderAddress(addresss[2]);
                }
            }
            if (buyorder.getDeliveryDirect() == 0) {
                String takeAddressId = request.getParameter("takeAddressId");
                if (ObjectUtils.notEmpty(takeAddressId) && takeAddressId.split("\\|").length > 2) {
                    Integer addressId = Integer.valueOf(takeAddressId.split("\\|")[0]);
                    Address add = addressService.getAddressById(addressId);
                    buyorder.setTakeTraderArea(takeAddressId.split("\\|")[2]);
                    buyorder.setTakeTraderName(takeAddressId.split("\\|")[1]);
                    if (add != null) {
                        buyorder.setTakeTraderAddress(add.getAddress());
                        buyorder.setTakeTraderContactName(add.getContactName());
                        buyorder.setTakeTraderContactMobile(add.getMobile());
                        buyorder.setTakeTraderContactTelephone(add.getTelephone());
                        buyorder.setTakeTraderAddressId(add.getAddressId());
                    }
                }
            }
        }
        map.put("buyorder", buyorder);
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        //删除GE配件表
        String buygoodIds = buyorder.getDelBuyGoodsIds();
        if (StringUtils.isNotEmpty(buygoodIds)) {
            BuyorderVo bv = getAddBuyorderVoDetail(buyorder, user);
            bv.setBuyorderId(buyorder.getBuyorderId());
            editBuyorderGEInfo(bv);
            String[] buyGoodsIDs = buyorder.getDelBuyGoodsIds().split(",");
            for (int i = 0; i < buyGoodsIDs.length; i++) {
                Integer buygoodid = Integer.parseInt(buyGoodsIDs[i]);
                if (CollectionUtils.isNotEmpty(bv.getBuyorderGoodsVoList())) {
                    bv.getBuyorderGoodsVoList().forEach(item -> {
                        if (buygoodid.equals(item.getBuyorderGoodsId()) && item.getGeSkuFlag() == 1) {
                            geInfoCollectionService.delGeInfoCollection(buygoodid);
                        }
                    });
                }
            }
        }

        ResultInfo<?> result = null;
        try {
            result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.SAVE_EDIT_BUYORDER, map,
                    clientId, clientKey, TypeRef);
        } catch (Exception e) {
            logger.error("调用DB保存编辑采购订单异常",e);
        }
        if (result != null && result.getCode().equals(0)) {
            // 发送站内消息采购商品集合 -修改0 -新增1
            Map<String, Integer> sendMsgMap = (Map<String, Integer>) result.getData();
            if (sendMsgMap != null && !sendMsgMap.isEmpty()) {
                for (String buyOrderGoodsIdKey : sendMsgMap.keySet()) {
                    logger.info("buyOrderGoodsIdKey" + buyOrderGoodsIdKey);
                    Integer newOrEditFlag = sendMsgMap.get(buyOrderGoodsIdKey);
                    // 获取当前采购商品对应采购单信息
                    Buyorder buyOrderDetail = buyorderGoodsMapper.selectBuyorderByBuyOrderGoodsId(Integer.valueOf(buyOrderGoodsIdKey));
                    logger.info("buyOrderDetail :" + buyOrderDetail);
                    if (buyOrderDetail == null) {
                        continue;
                    }
                    // 过滤 非VP、 对应销售单商品已经全部发货
                    List<Saleorder> validSaleOrderNos = buyorderGoodsMapper.selectValidSaleOrderByBuyOrderGoodsId(Integer.valueOf(buyOrderGoodsIdKey));
                    if (validSaleOrderNos == null || validSaleOrderNos.size() == 0) {
                        continue;
                    }

                    // -发送修改消息 0 (db已经注释掉)
                    // -发送新增消息 1
                    if (Integer.valueOf(1).equals(newOrEditFlag)) {
                        Map<String, String> mapParams = new HashMap<>();
                        for (Saleorder saleOrderItem : validSaleOrderNos) {
                            // 根据销售单编码查找对应销售
                            List<Integer> saleOrderTraderIds = buyorderGoodsMapper.selectSaleOrderTraderIdsBySaleOrderId(saleOrderItem.getSaleorderId());
                            saleOrderTraderIds = saleOrderTraderIds.stream().filter(ObjectUtils::notEmpty).collect(Collectors.toList());
                            if (!ObjectUtils.allNotNull(saleOrderItem)) {
                                continue;
                            }
                            mapParams.put("saleOrderNo", saleOrderItem.getSaleorderNo());
                            MessageUtil.sendMessage2(171, saleOrderTraderIds, mapParams, "./order/saleorder/view.do?saleorderId=" + saleOrderItem.getSaleorderId().intValue(), user.getUsername());
                        }
                        // 下面本来是修改时发站内信 但是需求要求审核提交时
                    }  if (Integer.valueOf(0).equals(newOrEditFlag)) {
                        Map<String, String> mapParams = new HashMap<>();
                        for (Saleorder saleOrderItem : validSaleOrderNos) {
                            // 根据销售单编码查找对应销售
                            List<Integer> saleOrderTraderIds = buyorderGoodsMapper.selectSaleOrderTraderIdsBySaleOrderId(saleOrderItem.getSaleorderId());
                            saleOrderTraderIds = saleOrderTraderIds.stream().filter(ObjectUtils::notEmpty).collect(Collectors.toList());
                            if (!ObjectUtils.allNotNull(saleOrderItem)) {
                                continue;
                            }
                            mapParams.put("saleOrderNo", saleOrderItem.getSaleorderNo());
                            mapParams.put("buyOrderNo", buyOrderDetail.getBuyorderNo());
                            MessageUtil.sendMessage2(170, saleOrderTraderIds, mapParams, "./order/saleorder/view.do?saleorderId=" + saleOrderItem.getSaleorderId().intValue(), user.getUsername());
                        }
                    }
                }
            }
            // 更新日期
            orderCommonService.updateBuyOrderDataUpdateTime(buyorder.getBuyorderId(), null, OrderDataUpdateConstant.BUY_ORDER_EDIT);

            // 更新费用单相关信息
            updateBuyOrderExpenseInfo(buyorder);

            // 执行采购订单返利相关操作
            executeBuyOrderRebateProcess(buyorder, isExistSettlement, isUsedRebate);

            // 保存实际供应商信息
            if (ErpConstant.SUBSIDIARY_TRADER_ID.equals(buyorder.getTraderId())) {
                BuyorderActualSupplierDto actualSupplierDto = new BuyorderActualSupplierDto();
                actualSupplierDto.setBuyorderId(buyorder.getBuyorderId());
                actualSupplierDto.setTraderId(buyorder.getTraderIdAct());
                actualSupplierDto.setTraderName(buyorder.getTraderNameAct());
                actualSupplierDto.setTraderContactId(buyorder.getTraderContactIdAct());
                actualSupplierDto.setTraderContactName(buyorder.getTraderContactNameAct());
                actualSupplierDto.setTraderContactMobile(buyorder.getTraderContactMobileAct());
                actualSupplierDto.setTraderContactTelephone(buyorder.getTraderContactTelephoneAct());
                actualSupplierDto.setPaymentType(buyorder.getPaymentTypeAct());
                actualSupplierDto.setPrepaidAmount(buyorder.getPrepaidAmountAct());
                actualSupplierDto.setAccountPeriodAmount(buyorder.getAccountPeriodAmountAct());
                actualSupplierDto.setRetainageAmount(buyorder.getRetainageAmountAct());
                actualSupplierDto.setRetainageAmountMonth(buyorder.getRetainageAmountMonthAct());
                actualSupplierDto.setInvoiceType(buyorder.getInvoiceTypeAct());
                actualSupplierDto.setNeedInvoice(buyorder.getNeedInvoice());
                buyorderApiService.saveOrUpdateBuyorderActualSupplier(actualSupplierDto);
            }
            // 是否银行承兑汇票 原有现有-无 原有现无-删除 原无现有-插入 原无现无-无
            OrderPaymentDetailsDto orderPaymentDetailsDto = buyorderApiService.queryOrderPaymentDetails(buyorder.getBuyorderId(), ErpConstant.ZERO, ErpConstant.BANK_ACCEPTANCE);
            if (ErpConstant.SUBSIDIARY_TRADER_ID.equals(buyorder.getTraderId()) && SysOptionConstant.ID_419.equals(buyorder.getPaymentType()) && ErpConstant.ONE.equals(buyorder.getBankAcceptance())) {
                if (Objects.isNull(orderPaymentDetailsDto)) {
                    // 插入
                    buyorderApiService.insertOrderPaymentDetails(buyorder.getBuyorderId(), ErpConstant.ZERO, ErpConstant.BANK_ACCEPTANCE);
                }
            } else {
                if (Objects.nonNull(orderPaymentDetailsDto)) {
                    // 删除
                    buyorderApiService.deleteOrderPaymentDetails(orderPaymentDetailsDto.getTOrderPaymentDetailsId());
                }
            }
        }
        return result;// 操作成功
    }

    private void executeBuyOrderRebateProcess(Buyorder buyorder, Boolean isExistSettlement, Boolean isUsedRebate) {
        BuyOrderRebateDto buyOrderRebateDto = new BuyOrderRebateDto();
        buyOrderRebateDto.setBuyorderId(buyorder.getBuyorderId());
        buyOrderRebateDto.setBuyorderNo(buyorder.getBuyorderNo());
        buyOrderRebateDto.setTraderId(buyorder.getTraderId());
        buyOrderRebateDto.setOldTraderId(buyorder.getOldTraderId());
        List<BuyorderGoods> goodsList = buyorder.getGoodsList();
        if (CollUtil.isNotEmpty(goodsList)) {
            List<BuyOrderRebateDto.BuyOrderGoodsRebateDto> buyOrderGoodsRebateDtoList = goodsList.stream()
                    .filter(g -> Objects.nonNull(g) && Objects.nonNull(g.getRebateAmount()) && g.getRebateAmount().compareTo(BigDecimal.ZERO) > 0)
                    .map(g -> {
                        BuyOrderRebateDto.BuyOrderGoodsRebateDto buyOrderGoodsRebateDto = new BuyOrderRebateDto.BuyOrderGoodsRebateDto();
                        buyOrderGoodsRebateDto.setBuyorderGoodsId(g.getBuyorderGoodsId());
                        buyOrderGoodsRebateDto.setGoodsName(g.getGoodsName());
                        buyOrderGoodsRebateDto.setRebatePrice(g.getRebatePrice());
                        buyOrderGoodsRebateDto.setRebateNum(g.getRebateNum());
                        buyOrderGoodsRebateDto.setRebateAmount(g.getRebateAmount());
                        return buyOrderGoodsRebateDto;
                    }).collect(Collectors.toList());
            buyOrderRebateDto.setNewbuyOrderGoodsRebateList(buyOrderGoodsRebateDtoList);
        }
        List<BuyorderGoodsVo> oldRebateGoodsList = buyorder.getOldRebateGoodsList();
        if (CollUtil.isNotEmpty(oldRebateGoodsList)) {
            List<BuyOrderRebateDto.BuyOrderGoodsRebateDto> buyOrderGoodsRebateDtoList = oldRebateGoodsList.stream()
                    .filter(g -> Objects.nonNull(g) && Objects.nonNull(g.getRebateAmount()) && g.getRebateAmount().compareTo(BigDecimal.ZERO) > 0)
                    .map(g -> {
                        BuyOrderRebateDto.BuyOrderGoodsRebateDto buyOrderGoodsRebateDto = new BuyOrderRebateDto.BuyOrderGoodsRebateDto();
                        buyOrderGoodsRebateDto.setBuyorderGoodsId(g.getBuyorderGoodsId());
                        buyOrderGoodsRebateDto.setGoodsName(g.getGoodsName());
                        buyOrderGoodsRebateDto.setRebatePrice(g.getRebatePrice());
                        buyOrderGoodsRebateDto.setRebateNum(BigDecimal.valueOf(g.getNum()));
                        buyOrderGoodsRebateDto.setRebateAmount(g.getRebateAmount());
                        return buyOrderGoodsRebateDto;
                    }).collect(Collectors.toList());
            buyOrderRebateDto.setOldBuyOrderGoodsRebateList(buyOrderGoodsRebateDtoList);
        }
        buyOrderRebateApiService.addOrUpdateBuyOrderRebateProcess(buyOrderRebateDto, isExistSettlement, isUsedRebate);
    }


    /**
     * 更新采购费用单和采购费用商品信息
     *
     * @param buyorder 采购单信息
     */
    @Transactional(rollbackFor = Throwable.class)
    public void updateBuyOrderExpenseInfo(Buyorder buyorder) {
        Integer buyorderExpenseId = buyorder.getBuyorderExpenseId();
        List<BuyorderExpenseItemDto> expenseItemDtoList = Optional.ofNullable(buyorder.getBuyorderExpenseItemDtos()).orElse(new ArrayList<>());
        // 过滤掉未删除的虚拟商品
        List<BuyorderExpenseItemDto> expenseItemDtos = expenseItemDtoList.stream().filter(item -> item.getIsDelete() == 0).collect(Collectors.toList());
        buyorder.setBuyorderExpenseItemDtos(expenseItemDtos);

        if (expenseItemDtos.size() > 0) {
            BuyorderExpenseDto buyorderExpenseDto = new BuyorderExpenseDto();
            buildBuyorderExpenseDtoInfo(buyorder, buyorderExpenseDto);

            if (buyorderExpenseId != null) {
                // 已存在直属费用单时，编辑费用单信息及其 附属信息
                buyorderExpenseDto.setBuyorderExpenseId(buyorderExpenseId);
                buyorderExpenseApiService.update(buyorderExpenseDto);
            } else {
                // 不存在，直接新增费用直属费用单及其相关信息
                buyorderExpenseApiService.add(buyorderExpenseDto);
            }
        } else {
            if (buyorderExpenseId != null) {
                // 说明在编辑时将所有费用单商品全部删除，所以需要将费用单相关信息也全部删除
                buyorderExpenseApiService.deleteExpenseInfoByExpenseId(buyorderExpenseId);
                logger.info("新增或编辑采购单触发删除直属采购费用单相关信息，采购费用单id：{}", buyorderExpenseId);
            }
        }
    }

    /**
     * 从采购单中获取并构造费用单信息(erp-old模块无法引入erp-buyorder模块的mapstruct，只好一个个set)
     * 这边不好区分是新增还是编辑，也不好区分哪个字段发生了改变，因此只能所有字段都执行一遍
     * @param buyorder 采购单
     * @param buyorderExpenseDto 费用单
     */
    private void buildBuyorderExpenseDtoInfo(Buyorder buyorder, BuyorderExpenseDto buyorderExpenseDto) {
        buyorderExpenseDto.setBuyorderId(buyorder.getBuyorderId());
        buyorderExpenseDto.setBuyorderNo(buyorderMapper.selectByPrimaryKey(buyorder.getBuyorderId()).getBuyorderNo());

        // BuyorderExpenseDetail信息
        BuyorderExpenseDetailDto buyorderExpenseDetailDto = BuyorderExpenseDetailDto.builder()
                .paymentType(buyorder.getPaymentType())
                .paymentComments(buyorder.getPaymentComments())
                .invoiceType(buyorder.getInvoiceType())
                .invoiceComments(buyorder.getInvoiceComments())
                .traderId(buyorder.getTraderId())
                .traderName(buyorder.getTraderName())
                .traderContactId(buyorder.getTraderContactId())
                .traderContactName(buyorder.getTraderContactName())
                .traderContactMobile(buyorder.getTraderContactMobile())
                .traderContactTelephone(buyorder.getTraderContactTelephone())
                .traderAddressId(buyorder.getTraderAddressId())
                .traderArea(buyorder.getTraderArea())
                .traderAddress(buyorder.getTraderAddress())
                .traderComments(buyorder.getTraderComments())
                .periodDay(buyorder.getPeriodDay())
                .orgId(buyorder.getOrgId())
                .build();

        // buyorderExpenseItemDtos,这边还需要计算费用单总金额以及付款计划
        BigDecimal totalAmount = new BigDecimal("0.00");
        for (BuyorderExpenseItemDto item : buyorder.getBuyorderExpenseItemDtos()) {
            totalAmount = totalAmount.add(item.getBuyorderExpenseItemDetailDto().getPrice().multiply(new BigDecimal(item.getNum())));
        }

        buyorderExpenseDetailDto.setTotalAmount(totalAmount);
        buyorderExpenseDto.setBuyorderExpenseDetailDto(buyorderExpenseDetailDto);
        buyorderExpenseDto.setBuyorderExpenseItemDtos(buyorder.getBuyorderExpenseItemDtos());

        // 计算付款计划信息
        reCalculatePaymentTerm(buyorderExpenseDto.getBuyorderExpenseDetailDto());
    }

    /**
     *
     * 计算 采购费用单 预付金额、账期金额、尾款三个字段
     */
    private void reCalculatePaymentTerm(BuyorderExpenseDetailDto buyorderExpenseDetailDto) {
        BigDecimal zero = new BigDecimal("0.00");
        switch (buyorderExpenseDetailDto.getPaymentType()) {
            case 424: // 自定义,等同于预付100%
            case 419: // 预付100%
                buyorderExpenseDetailDto.setPrepaidAmount(buyorderExpenseDetailDto.getTotalAmount());
                buyorderExpenseDetailDto.setAccountPeriodAmount(zero);
                buyorderExpenseDetailDto.setRetainageAmount(zero);
                buyorderExpenseDetailDto.setRetainageAmountMonth(0);
                break;
            case 3175: // 预付90%
                BigDecimal prepaidAmount = buyorderExpenseDetailDto.getTotalAmount().multiply(new BigDecimal(0.90)).setScale(2, BigDecimal.ROUND_HALF_UP);
                buyorderExpenseDetailDto.setPrepaidAmount(prepaidAmount);
                buyorderExpenseDetailDto.setAccountPeriodAmount(buyorderExpenseDetailDto.getTotalAmount().subtract(prepaidAmount));
                buyorderExpenseDetailDto.setRetainageAmount(zero);
                buyorderExpenseDetailDto.setRetainageAmountMonth(0);
                break;
            case 420: // 预付80%
                BigDecimal prepaidAmount1 = buyorderExpenseDetailDto.getTotalAmount().multiply(new BigDecimal(0.80)).setScale(2, BigDecimal.ROUND_HALF_UP);
                buyorderExpenseDetailDto.setPrepaidAmount(prepaidAmount1);
                buyorderExpenseDetailDto.setAccountPeriodAmount(buyorderExpenseDetailDto.getTotalAmount().subtract(prepaidAmount1));
                buyorderExpenseDetailDto.setRetainageAmount(zero);
                buyorderExpenseDetailDto.setRetainageAmountMonth(0);
                break;
            case 421: // 预付50%
                BigDecimal prepaidAmount2 = buyorderExpenseDetailDto.getTotalAmount().multiply(new BigDecimal(0.50)).setScale(2, BigDecimal.ROUND_HALF_UP);
                buyorderExpenseDetailDto.setPrepaidAmount(prepaidAmount2);
                buyorderExpenseDetailDto.setAccountPeriodAmount(buyorderExpenseDetailDto.getTotalAmount().subtract(prepaidAmount2));
                buyorderExpenseDetailDto.setRetainageAmount(zero);
                buyorderExpenseDetailDto.setRetainageAmountMonth(0);
                break;
            case 422: // 预付30%
                BigDecimal prepaidAmount3 = buyorderExpenseDetailDto.getTotalAmount().multiply(new BigDecimal(0.30)).setScale(2, BigDecimal.ROUND_HALF_UP);
                buyorderExpenseDetailDto.setPrepaidAmount(prepaidAmount3);
                buyorderExpenseDetailDto.setAccountPeriodAmount(buyorderExpenseDetailDto.getTotalAmount().subtract(prepaidAmount3));
                buyorderExpenseDetailDto.setRetainageAmount(zero);
                buyorderExpenseDetailDto.setRetainageAmountMonth(0);
                break;
            case 3174: // 预付10%
                BigDecimal prepaidAmount4 = buyorderExpenseDetailDto.getTotalAmount().multiply(new BigDecimal(0.10)).setScale(2, BigDecimal.ROUND_HALF_UP);
                buyorderExpenseDetailDto.setPrepaidAmount(prepaidAmount4);
                buyorderExpenseDetailDto.setAccountPeriodAmount(buyorderExpenseDetailDto.getTotalAmount().subtract(prepaidAmount4));
                buyorderExpenseDetailDto.setRetainageAmount(zero);
                buyorderExpenseDetailDto.setRetainageAmountMonth(0);
                break;
            case 423: // 预付0%
                buyorderExpenseDetailDto.setAccountPeriodAmount(buyorderExpenseDetailDto.getTotalAmount());
                buyorderExpenseDetailDto.setPrepaidAmount(BigDecimal.ZERO);
                buyorderExpenseDetailDto.setRetainageAmount(zero);
                buyorderExpenseDetailDto.setRetainageAmountMonth(0);
                break;
        }
    }


    /**
     * <b>Description:</b><br>
     * 分解销售商品对象
     *
     * @param request
     * @param user
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年8月4日 下午3:31:15
     */
    private List<RBuyorderSaleorder> getSaleorderGoods(HttpServletRequest request, User user) {
        List<RBuyorderSaleorder> sgList = new ArrayList<>();
        String[] dbBuyNums = request.getParameterValues("dbBuyNum");
        if (dbBuyNums != null && dbBuyNums.length > 0) {
            for (int i = 0; i < dbBuyNums.length; i++) {
                RBuyorderSaleorder sg = new RBuyorderSaleorder();
                sg.setBuyorderGoodsId(Integer.valueOf(dbBuyNums[i].split("\\|")[0]));
                sg.setSaleorderGoodsId(Integer.valueOf(dbBuyNums[i].split("\\|")[1]));
                sg.setNum(Integer.valueOf(dbBuyNums[i].split("\\|")[2]));
                sgList.add(sg);
            }
        }
        return sgList;
    }

    /**
     * <b>Description:</b><br>
     * 分解采购商品对象
     *
     * @param request
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年8月3日 上午10:30:46
     */
    private List<BuyorderGoods> getBuyorderGoods(HttpServletRequest request, User user) {
        List<BuyorderGoods> bgList = new ArrayList<>();

        String[] buySums = request.getParameterValues("buySum");
        String[] prices = request.getParameterValues("price");
        String[] insideComments = request.getParameterValues("insideComments");
        String[] deliveryCycle = request.getParameterValues("deliveryCycle");
        String[] isHaveAuth = request.getParameterValues("isHaveAuth");
        String[] isGiftGoods = request.getParameterValues("isGiftGoods");
        String[] installation = request.getParameterValues("installation");
        String[] goodsComments = request.getParameterValues("goodsComments");
        // 获取预计发货时间和预计收货时间
        String[] sendGoodsTimeStrings = request.getParameterValues("sendGoodsTimeStr");
        String[] receiveGoodsTimeStrings = request.getParameterValues("receiveGoodsTimeStr");
        //打印receiveGoodsTimeStrings
        logger.info("receiveGoodsTimeStrings:{}",receiveGoodsTimeStrings);
        String[] referPrices = request.getParameterValues("referPrice");
        if (buySums != null && buySums.length > 0) {
            for (int i = 0; i < buySums.length; i++) {
                BuyorderGoods bg = new BuyorderGoods();
                bg.setBuyorderGoodsId(Integer.valueOf(buySums[i].split("\\|")[0]));
                bg.setNum(Integer.valueOf(buySums[i].split("\\|")[1]));
                for (int j = 0; j < prices.length; j++) {
                    if (bg.getBuyorderGoodsId().equals(Integer.valueOf(prices[i].split("\\|")[0]))) {
                        bg.setPrice(BigDecimal.valueOf(Double.valueOf(prices[i].split("\\|")[1])));
                        break;
                    }
                }
                if (insideComments != null && insideComments.length > 0) {
                    for (int j = 0; j < insideComments.length; j++) {
                        if (i < insideComments.length && bg.getBuyorderGoodsId().equals(Integer.valueOf(insideComments[i].split("\\|")[0]))) {
                            if (insideComments[i].split("\\|").length > 1) {
                                bg.setInsideComments(insideComments[i].split("\\|")[1]);
                            } else {
                                bg.setInsideComments("");
                            }
                            break;
                        }
                    }
                }
                if (sendGoodsTimeStrings != null && sendGoodsTimeStrings.length > 0) {
                    for (int j = 0; j < sendGoodsTimeStrings.length; j++) {
                        if (i < sendGoodsTimeStrings.length && StringUtils.isNotBlank(sendGoodsTimeStrings[i].split("\\|")[0])
                                && bg.getBuyorderGoodsId().equals(Integer.valueOf(sendGoodsTimeStrings[i].split("\\|")[0]))) {
                            if (sendGoodsTimeStrings[i].split("\\|").length > 1) {
                                logger.info("sendGoodsTimeStrings[" + i + "] :" + sendGoodsTimeStrings[i].split("\\|")[1]);
                                if (StringUtils.isBlank(sendGoodsTimeStrings[i].split("\\|")[1])) {
                                    bg.setSendGoodsTime(null);
                                } else {
                                    bg.setSendGoodsTime(DateUtil.convertLong(sendGoodsTimeStrings[i].split("\\|")[1], DateUtil.DATE_FORMAT));
                                }
                            } else {
                                bg.setSendGoodsTime(null);
                            }
                            break;
                        }
                    }
                }
                if (receiveGoodsTimeStrings != null && receiveGoodsTimeStrings.length > 0) {
                    for (int j = 0; j < receiveGoodsTimeStrings.length; j++) {
                        if (i < receiveGoodsTimeStrings.length && StringUtils.isNotBlank(receiveGoodsTimeStrings[i].split("\\|")[0])
                                && bg.getBuyorderGoodsId().equals(Integer.valueOf(receiveGoodsTimeStrings[i].split("\\|")[0]))) {
                            if (receiveGoodsTimeStrings[i].split("\\|").length > 1) {
                                logger.info("receiveGoodsTimeStrings[" + i + "] :" + receiveGoodsTimeStrings[i].split("\\|")[1]);
                                if (StringUtils.isBlank(receiveGoodsTimeStrings[i].split("\\|")[1])) {
                                    bg.setReceiveGoodsTime(null);
                                } else {
                                    bg.setReceiveGoodsTime(DateUtil.convertLong(receiveGoodsTimeStrings[i].split("\\|")[1], DateUtil.DATE_FORMAT));
                                }
                            } else {
                                bg.setReceiveGoodsTime(null);
                            }
                            break;
                        }
                    }
                }
                if (deliveryCycle != null && deliveryCycle.length > 0) {
                    for (int j = 0; j < deliveryCycle.length; j++) {
                        if (i < deliveryCycle.length && StringUtil.isNotBlank(deliveryCycle[i]) &&
                                bg.getBuyorderGoodsId().equals(Integer.valueOf(deliveryCycle[i].split("\\|")[0]))) {
                            if (deliveryCycle[i].split("\\|").length > 1) {
                                bg.setDeliveryCycle(deliveryCycle[i].split("\\|")[1]);
                            } else {
                                bg.setDeliveryCycle("");
                            }
                            break;
                        }
                    }
                }

                if (isHaveAuth != null && isHaveAuth.length > 0) {
                    for (int j = 0; j < isHaveAuth.length; j++) {
                        if (i < isHaveAuth.length && StringUtil.isNotBlank(isHaveAuth[i]) &&
                                bg.getBuyorderGoodsId().equals(Integer.valueOf(isHaveAuth[i].split("\\|")[0]))) {
                            if (isHaveAuth[i].split("\\|").length > 1) {
                                bg.setIsHaveAuth(Integer.valueOf(isHaveAuth[i].split("\\|")[1]));
                            } else {
                                bg.setIsHaveAuth(1);
                            }
                            break;
                        }
                    }
                }

                if (isGiftGoods != null && isGiftGoods.length > 0) {
                    for (int j = 0; j < isGiftGoods.length; j++) {
                        if (i < isGiftGoods.length && StringUtil.isNotBlank(isGiftGoods[i]) &&
                                bg.getBuyorderGoodsId().equals(Integer.valueOf(isGiftGoods[i].split("\\|")[0]))) {
                            if (isGiftGoods[i].split("\\|").length > 1) {
                                bg.setIsGift(Integer.valueOf(isGiftGoods[i].split("\\|")[1]));
                            } else {
                                bg.setIsGift(0);
                            }
                            break;
                        }
                    }
                }
                if (referPrices != null && referPrices.length > 0) {
                    for (int j = 0; j < referPrices.length; j++) {
                        if (i < referPrices.length && StringUtil.isNotBlank(referPrices[i]) &&
                                bg.getBuyorderGoodsId().equals(Integer.valueOf(referPrices[i].split("\\|")[0]))) {
                            if (referPrices[i].split("\\|").length > 1) {
                                bg.setReferPrice(new BigDecimal(referPrices[i].split("\\|")[1]));
                            } else {
                                bg.setReferPrice(BigDecimal.ZERO);
                            }
                            break;
                        }
                    }
                }

                if (installation != null && installation.length > 0) {
                    for (int j = 0; j < installation.length; j++) {
                        if (i < installation.length && bg.getBuyorderGoodsId().equals(Integer.valueOf(installation[i].split("\\|")[0]))) {
                            if (installation[i].split("\\|").length > 1) {
                                bg.setInstallation(installation[i].split("\\|")[1]);
                            } else {
                                bg.setInstallation("");
                            }
                            break;
                        }
                    }
                }
                if (goodsComments != null && goodsComments.length > 0) {
                    for (int j = 0; j < goodsComments.length; j++) {
                        if (i < goodsComments.length && bg.getBuyorderGoodsId().equals(Integer.valueOf(goodsComments[i].split("\\|")[0]))) {
                            if (goodsComments[i].split("\\|").length > 1) {
                                bg.setGoodsComments(goodsComments[i].split("\\|")[1]);
                            } else {
                                bg.setGoodsComments("");
                            }
                            break;
                        }
                    }
                }
                bg.setUpdater(user.getUserId());
                bg.setModTime(DateUtil.sysTimeMillis());
                bgList.add(bg);
            }
        }
        return bgList;
    }

    /**
     * <b>Description:</b><br>
     * 获取加入采购订单页面的列表信息
     *
     * @param buyorderVo
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年8月8日 上午11:21:00
     */
    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> getGoodsVoList(BuyorderVo buyorderVo) {
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {

            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.GET_GOODSVO_LIST, buyorderVo,
                    clientId, clientKey, TypeRef);
            if (result == null || result.getCode() == -1) {
                return null;
            } else if (result.getCode() == -2) {
                Map<String, Object> map = new HashMap<>();
                map.put("-2", -2);
                return map;
            }
            JSONArray json = JSONArray.fromObject(result.getData());
            List<GoodsVo> gvList = (List<GoodsVo>) JSONArray.toCollection(json, GoodsVo.class);
            Integer sum = 0;
            //是否含有虚拟商品
            Integer isHaveVirtureSku = 0;
            for (GoodsVo gv : gvList) {
                Integer num = 0;
                JSONArray jsonArray = JSONArray.fromObject(gv.getSgvList());
                List<SaleorderGoodsVo> list =
                        (List<SaleorderGoodsVo>) JSONArray.toCollection(jsonArray, SaleorderGoodsVo.class);
                for (SaleorderGoodsVo sgv : list) {
                    sgv.setApplicantName(getUserNameByUserId(sgv.getUserId()));
                    //VDERP-13277 待采购订单列表不区分直发和普发
                    num += sgv.getNeedBuyNum() == null ? 0 : sgv.getNeedBuyNum();
                    gv.setSaleorderNo(sgv.getSaleorderNo());
                    gv.setIsGift(sgv.getIsGift());
                    gv.setIsVirture(sgv.getIsVirture());
                    if(sgv.getIsVirture()==2){
                        isHaveVirtureSku = 2;
                    }
                }
                sum += num;
                gv.setProBuySum(num);
                gv.setSgvList(list);
            }
            Map<String, Object> map = new HashMap<>();
            map.put("sum", sum);
            map.put("isHaveVirtureSku", isHaveVirtureSku);
            map.put("gvList", gvList);
            return map;
        } catch (Exception e) {
            logger.error(Contant.ERROR_MSG, e);
            throw new RuntimeException();
        }
    }

    /**
     * <b>Description:</b><br>
     * 加入已存在采购订单
     *
     * @param buyorder
     * @param request
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年8月9日 上午10:34:46
     */
    @Override
    // @SystemServiceLog(operationType = "eidt",desc = "加入已存在采购订单")
    public ResultInfo<?> saveAddHavedBuyorder(Map<String, Object> map) {
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {

            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.SAVE_HAVED_BUYORDER, map,
                    clientId, clientKey, TypeRef);
            return result;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<AddressVo> getAddressVoList(ParamsConfigValue paramsConfigValue) {
        List<Integer> addressIds = paramsConfigValueService.getParamsValueList(paramsConfigValue);
        List<AddressVo> list = new ArrayList<>();
        AddressVo adv = null;
        if (addressIds != null && addressIds.size() > 0) {
            list = addressMapper.getAddressVoList(addressIds);
            if (list != null && list.size() > 0) {
                adv = list.get(0);
            }
        }

        List<AddressVo> adList = addressMapper.getAddressVoListByParam(paramsConfigValue.getCompanyId());
        for (AddressVo av : adList) {
            if (adv != null && adv.getAddressId().intValue() == av.getAddressId().intValue()) {
                av.setIsDefault(1);
            } else {
                av.setIsDefault(0);
            }
            String area = getAddressByAreaId(av.getAreaId());
            av.setAreas(area.replace(" ", ""));
        }
        return adList;
    }

    @Override
    @Transactional
    public ResultInfo<?> saveApplyPayment(PayApply payApply) {
        /**迁移db
        ResultInfo<?> result = null;
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<Integer>> TypeRef = new TypeReference<ResultInfo<Integer>>() {
        };
        String url = httpUrl + "order/buyorder/saveapplypayment.htm";
        try {
            result = (ResultInfo<Integer>) HttpClientUtils.post(url, payApply, clientId, clientKey, TypeRef);
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
        }
        return result;**/
        int i = payApplyMapper.insertSelective(payApply);
        if (i == 1) {
            // 更新往来单位类型
            payApplyApiService.updateAccountType(payApply.getPayApplyId());
            // 付款申请详情数据
            if (payApply.getDetailList() != null && !payApply.getDetailList().isEmpty()) {// 详情不为空
                int n = payApplyDetailMapper.batchInsertApplyDetail(payApply);
                if (n > 0) {
                    return new ResultInfo(0, "操作成功", payApply.getPayApplyId());
                }
            }
        }
        return new ResultInfo();
    }

    /**
     * <b>Description:</b><br>
     * 申请审核
     *
     * @param buyorder
     * @param request
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年9月7日 上午9:31:36
     */
    @Override
    // @SystemServiceLog(operationType = "eidt",desc = "采购订单申请审核")
    public ResultInfo<?> saveApplyReview(Buyorder buyorder) {
        Map<String, Object> map = new HashMap<>();
        map.put("buyorder", buyorder);
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {

            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.SAVE_EDIT_BUYORDER, map,
                    clientId, clientKey, TypeRef);
            return result;
        } catch (Exception e) {
            throw new RuntimeException();
        }
    }

    /**
     * <b>Description:</b><br>
     * 关闭采购订单
     *
     * @param buyorder
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年9月7日 上午9:38:08
     */
    @Override
    // @SystemServiceLog(operationType = "eidt",desc = "关闭采购订单")
    public ResultInfo<?> saveCloseBuyorder(Buyorder buyorder) {
        Map<String, Object> map = new HashMap<>();
        map.put("buyorder", buyorder);
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            //删除录入的GE信息
            User user = new User();
            BuyorderVo bv = getAddBuyorderVoDetail(buyorder, user);
            if (CollectionUtils.isNotEmpty(bv.getBuyorderGoodsVoList())) {
                bv.getBuyorderGoodsVoList().forEach(item -> {
                    geInfoCollectionService.delGeInfoCollection(item.getBuyorderGoodsId());
                });
            }

            Buyorder buyorderDB = this.buyorderMapper.selectByPrimaryKey(buyorder.getBuyorderId());

            if (buyorderDB != null && StringUtils.isNotEmpty(buyorderDB.getBuyorderNo())) {

                //删除采购单对应的商品的代办
                todoListMapper.deleteByBuzTypeAndBuzExtra(TodoListBuzSceneEnum.RISK_CHECK_SKU_DATA.getBuzSceneId(), buyorderDB.getBuyorderNo());

                //删除采购单对应的供应商的代办
                todoListMapper.deleteByBuzTypeAndBuzExtra(TodoListBuzSceneEnum.RISK_CHECK_TRADER_SUPPLY_DATA.getBuzSceneId(), buyorderDB.getBuyorderNo());

            }

            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.SAVE_EDIT_BUYORDER, map,
                    clientId, clientKey, TypeRef);
            if (result != null && result.getCode().equals(0)) {
                orderCommonService.updateBuyOrderDataUpdateTime(buyorder.getBuyorderId(), null, OrderDataUpdateConstant.BUY_ORDER_CLOSE);
            }
            BuyorderExpenseDto buyorderExpenseDto = buyorderExpenseApiService.queryAttributiveExpenseInfoByBuyorderId(buyorder.getBuyorderId());
            if (Objects.nonNull(buyorderExpenseDto)) {
                buyorderExpenseApiService.closeBuyOrderExpense(buyorderExpenseDto.getBuyorderExpenseId());
            }
            // 采购单关闭后，关闭返利流程
            buyOrderRebateApiService.closeBuyOrderRebateProcess(buyorder.getBuyorderId());
            return result;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public BuyorderVo getBuyorderGoodsVoList(Buyorder buyorder) {
        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<BuyorderVo>> TypeRef = new TypeReference<ResultInfo<BuyorderVo>>() {
            };
            String url = httpUrl + "order/buyorder/getbuyordergoodsbybuyorderid.htm";
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, buyorder, clientId, clientKey, TypeRef);
            JSONObject json = JSONObject.fromObject(result.getData());
            BuyorderVo sv = JsonUtils.readValue(json.toString(), BuyorderVo.class);
            if ("hh".equals(buyorder.getFlag()) || "th".equals(buyorder.getFlag())) {
                List<TraderAddress> list = sv.getTavList();
                if (list != null && list.size() > 0) {
                    List<TraderAddressVo> tavList = new ArrayList<>();
                    TraderAddressVo tav = null;
                    for (TraderAddress ta : list) {
                        tav = new TraderAddressVo();
                        tav.setTraderAddress(ta);
                        tav.setArea(getAddressByAreaId(ta.getAreaId()));
                        tavList.add(tav);
                    }
                    sv.setTraderAddressVoList(tavList);
                }
            }
            return sv;
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
    }

    /**
     * 保存采购单主表信息
     */
    @Override
    public ResultInfo<?> saveBuyorder(Buyorder buyorder) {
        // 采购单审核通过后，虚拟商品自动收发货，这段逻辑在buyorderService.handleBuyOrderVirtualGoodsArrival已有，
        // 因此当前这个方法只在采购单申请审核一个地方调用，用来锁定采购单，所以此处的关于虚拟商品的收发货所有逻辑删除
        int Num = buyorderMapper.updateByPrimaryKeySelective(buyorder);
        if (Num > 0) {
            return new ResultInfo(0, "修改成功");
        } else {
            return new ResultInfo();
        }
    }

    @Override
    public Map<String, Object> getBHManageList(GoodsVo goodsVo, Page page) {
        String url = httpUrl + "order/buyorder/getbhmanagelist.htm";
        final TypeReference<ResultInfo<Map<String, Object>>> TypeRef =
                new TypeReference<ResultInfo<Map<String, Object>>>() {
                };
        try {
            ResultInfo<?> result =
                    (ResultInfo<?>) HttpClientUtils.post(url, goodsVo, clientId, clientKey, TypeRef, page);
            Map<String, Object> mapData = (Map<String, Object>) result.getData();
            String listStr = (String) mapData.get("list");
            // String allListStr = (String) mapData.get("allList");

            List<GoodsVo> list = null;
            // List<GoodsVo> allList = null;

            if (!"[]".equals(listStr)) {
                net.sf.json.JSONArray json = net.sf.json.JSONArray.fromObject(listStr);
                list = (List<GoodsVo>) json.toCollection(json, GoodsVo.class);
            } else {
                list = new ArrayList<>();
            }
            // if(!"[]".equals(allList)){
            // net.sf.json.JSONArray allListStrjson =
            // net.sf.json.JSONArray.fromObject(allListStr);
            // allList = (List<GoodsVo>)
            // allListStrjson.toCollection(allListStrjson, GoodsVo.class);
            // }else{
            // allList = new ArrayList<>();
            // }

            if (null != list && list.size() > 0) {
                List<Integer> goodsIds = new ArrayList<>();
                List<Integer> categoryIds = new ArrayList<>();
                for (GoodsVo goods : list) {
                    goodsIds.add(goods.getGoodsId());
                    if (null != goods.getCategoryId() && goods.getCategoryId() > 0) {
                        categoryIds.add(goods.getCategoryId());
                    }

                    if (null != goods.getGoodsLevel() && goods.getGoodsLevel() > 0) {
                        SysOptionDefinition sysOptionDefinition = getSysOptionDefinitionById(goods.getGoodsLevel());
                        if (null != sysOptionDefinition) {
                            goods.setGoodsLevelName(sysOptionDefinition.getTitle());
                        }
                    }
                }

                if (goodsIds.size() > 0) {
                    // 安全库存
                    List<GoodsSafeStock> goodsSafeStock =
                            goodsSafeStockMapper.getGoodsSafeStock(goodsIds, goodsVo.getCompanyId());
                    if (null != goodsSafeStock && goodsSafeStock.size() > 0) {
                        for (GoodsSafeStock stock : goodsSafeStock) {
                            for (GoodsVo goods : list) {
                                if (stock.getGoodsId().equals(goods.getGoodsId())) {
                                    goods.setSafeNum(stock.getNum());
                                }
                            }
                        }
                    }
                    // 采购价
                    List<GoodsChannelPrice> purchasePrice = goodsChannelPriceMapper.getPurchasePrice(goodsIds);
                    if (null != purchasePrice && purchasePrice.size() > 0) {
                        for (GoodsChannelPrice channelPrice : purchasePrice) {
                            for (GoodsVo goods : list) {
                                if (channelPrice.getGoodsId().equals(goods.getGoodsId())) {
                                    goods.setPurchasePrice(channelPrice.getPublicPrice());
                                }
                            }
                        }
                    }
                }

                if (categoryIds.size() > 0) {
                    // 归属
                    List<User> userList =
                            rCategoryJUserMapper.getUserByCategoryIds(categoryIds, goodsVo.getCompanyId());
                    if (null != userList && userList.size() > 0) {
                        for (User u : userList) {
                            for (GoodsVo goods : list) {
                                if (u.getCategoryId().equals(goods.getCategoryId())) {
                                    goods.setOwner(u.getOwners());
                                }
                            }
                        }
                    }
                }
            }

            // Integer maybeSaleNum = 0;
            // BigDecimal maybeOccupyAmount = new BigDecimal(0);
            // if(null != allList && allList.size() > 0){
            // List<Integer> goodsIdsList = new ArrayList<>();
            // for(GoodsVo goods : allList){
            // goodsIdsList.add(goods.getGoodsId());
            // maybeSaleNum += goods.getMaybeSaleNum30();
            // }
            // //采购价
            // if(null != goodsIdsList && goodsIdsList.size() > 0){
            // List<GoodsChannelPrice> purchasePriceList =
            // goodsChannelPriceMapper.getPurchasePrice(goodsIdsList);
            // if(null != purchasePriceList && purchasePriceList.size() > 0){
            // for(GoodsChannelPrice channelPrice : purchasePriceList){
            // for(GoodsVo goods : allList){
            // if(channelPrice.getGoodsId().equals(channelPrice.getGoodsId())){
            // if(null != channelPrice.getPrivatePrice()){
            // maybeOccupyAmount =
            // maybeOccupyAmount.add(channelPrice.getPublicPrice().multiply(new
            // BigDecimal(goods.getMaybeSaleNum30())));
            // }
            // }
            // }
            // }
            // }
            // }
            // }

            page = result.getPage();
            Map<String, Object> map = new HashMap<>();
            map.put("list", list);
            map.put("page", page);
            // map.put("maybeSaleNum", maybeSaleNum);
            // map.put("maybeOccupyAmount", maybeOccupyAmount);
            return map;
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            throw new RuntimeException();
        }
    }

    @Override
    public Map<String, Object> getBHManageStat(GoodsVo goodsVo) {
        String url = httpUrl + "order/buyorder/getbhmanagestat.htm";
        final TypeReference<ResultInfo<Map<String, Object>>> TypeRef =
                new TypeReference<ResultInfo<Map<String, Object>>>() {
                };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, goodsVo, clientId, clientKey, TypeRef);
            Map<String, Object> mapData = (Map<String, Object>) result.getData();
            // String listStr = (String) mapData.get("list");
            String allListStr = (String) mapData.get("allList");

            List<GoodsVo> allList = null;

            if (!"[]".equals(allList)) {
                net.sf.json.JSONArray allListStrjson = net.sf.json.JSONArray.fromObject(allListStr);
                allList = (List<GoodsVo>) allListStrjson.toCollection(allListStrjson, GoodsVo.class);
            } else {
                allList = new ArrayList<>();
            }

            Integer maybeSaleNum = 0;
            BigDecimal maybeOccupyAmount = new BigDecimal(0);
            if (null != allList && allList.size() > 0) {
                List<Integer> goodsIdsList = new ArrayList<>();
                for (GoodsVo goods : allList) {
                    goodsIdsList.add(goods.getGoodsId());
                    maybeSaleNum += goods.getMaybeSaleNum30();
                }
                // 采购价
                if (null != goodsIdsList && goodsIdsList.size() > 0) {
                    List<GoodsChannelPrice> purchasePriceList = goodsChannelPriceMapper.getPurchasePrice(goodsIdsList);
                    if (null != purchasePriceList && purchasePriceList.size() > 0) {
                        for (GoodsChannelPrice channelPrice : purchasePriceList) {
                            for (GoodsVo goods : allList) {
                                if (channelPrice.getGoodsId().equals(channelPrice.getGoodsId())) {
                                    if (null != channelPrice.getPrivatePrice()) {
                                        maybeOccupyAmount = maybeOccupyAmount.add(channelPrice.getPublicPrice()
                                                .multiply(new BigDecimal(goods.getMaybeSaleNum30())));
                                    }
                                }
                            }
                        }
                    }
                }
            }
            Map<String, Object> map = new HashMap<>();
            map.put("maybeSaleNum", maybeSaleNum);
            map.put("maybeOccupyAmount", maybeOccupyAmount);
            return map;
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            throw new RuntimeException();
        }
    }

    @Override
    public ResultInfo batchAddBhSaleorderGoods(List<Integer> list, Saleorder bhSaleorder, HttpSession session) {
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        Long time = DateUtil.sysTimeMillis();
        List<SaleorderGoods> saleorderGoods = new ArrayList<>();
        for (Integer goodsId : list) {
            SaleorderGoods sGoods = new SaleorderGoods();
            sGoods.setSaleorderId(bhSaleorder.getSaleorderId());
            sGoods.setGoodsId(goodsId);
            // 获取产品核价里面的采购价
            GoodsChannelPrice goodsChannelPrice = goodsChannelPriceMapper.getPurchasePriceByGoodsID(goodsId);
            if (null != goodsChannelPrice) {
                sGoods.setPurchasingPrice(goodsChannelPrice.getPublicPrice().toString());
                sGoods.setPrice(goodsChannelPrice.getPublicPrice());
            }
            sGoods.setCurrencyUnitId(1);
            sGoods.setAddTime(time);
            sGoods.setCreator(user.getUserId());
            sGoods.setModTime(time);
            sGoods.setUpdater(user.getUserId());
            Goods g = new Goods();
            g.setCompanyId(user.getCompanyId());
            g.setGoodsId(goodsId);
            sGoods.setGoods(g);

            saleorderGoods.add(sGoods);
        }

        String url = httpUrl + "order/buyorder/batchaddbhsaleordergoods.htm";
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            ResultInfo<?> result =
                    (ResultInfo<?>) HttpClientUtils.post(url, saleorderGoods, clientId, clientKey, TypeRef);

            if (result != null) {
                return result;
            } else {
                return new ResultInfo<>();
            }
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            throw new RuntimeException();
        }
    }

    @Override
    public ResultInfo batchAddBhSaleorderGoodsNew(List<Integer> list, Saleorder bhSaleorder, HttpSession session) {
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        Long time = DateUtil.sysTimeMillis();
        List<SaleorderGoods> saleorderGoods = new ArrayList<>();
        List<PrepareStockDto> stockDtoList = new ArrayList<>();
        for (Integer goodsId : list) {
            PrepareStockCommand command = new PrepareStockCommand();
            command.setNeedPage(1);
            command.setSkuId(goodsId);
            List<PrepareStockDto> stockList = prepareStockService.getPrepareStockListPage(command, null);
            Integer supplementStock = 0;
            if (CollectionUtils.isNotEmpty(stockList)) {
                PrepareStockDto stockDto = stockList.get(0);
                supplementStock = stockDto.getSupplementStock();
                stockDto.setDealType(DealTypeEnum.PREPARE_STOCK.getCode());
                stockDto.setSaleorderId(bhSaleorder.getSaleorderId());
                stockDto.setStockWarn(WarnLevelEnum.codeToMessage(stockDto.getWarnLevel()));
                prepareStockService.dealWithSaleNum(stockDto);
                stockDtoList.add(stockDto);
            }
            SaleorderGoods sGoods = new SaleorderGoods();
            sGoods.setSaleorderId(bhSaleorder.getSaleorderId());
            sGoods.setGoodsId(goodsId);
            // 获取产品核价里面的采购价
            GoodsChannelPrice goodsChannelPrice = goodsChannelPriceMapper.getPurchasePriceByGoodsID(goodsId);
            if (null != goodsChannelPrice) {
                sGoods.setPurchasingPrice(goodsChannelPrice.getPublicPrice().toString());
                sGoods.setPrice(goodsChannelPrice.getPublicPrice());
            }
            sGoods.setNum(supplementStock);
            sGoods.setCurrencyUnitId(1);
            sGoods.setAddTime(time);
            sGoods.setCreator(user.getUserId());
            sGoods.setModTime(time);
            sGoods.setUpdater(user.getUserId());
            Goods g = new Goods();
            g.setCompanyId(user.getCompanyId());
            g.setGoodsId(goodsId);
            sGoods.setGoods(g);
            saleorderGoods.add(sGoods);
        }

        String url = httpUrl + "order/buyorder/batchaddbhsaleordergoods.htm";
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            ResultInfo<?> result =
                    (ResultInfo<?>) HttpClientUtils.post(url, saleorderGoods, clientId, clientKey, TypeRef);

            if (result != null) {
                // 处理日志
                prepareStockService.saveBhOperateLog(session, stockDtoList);
                return result;
            } else {
                return new ResultInfo<>();
            }
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            throw new RuntimeException();
        }
    }

    @Override
    public BuyorderVo getBuyOrderPrintInfo(Buyorder buyorder) {
        try {
//            // 定义反序列化 数据格式---迁移db
//            final TypeReference<ResultInfo<BuyorderVo>> TypeRef = new TypeReference<ResultInfo<BuyorderVo>>() {
//            };
//            String url = httpUrl + "order/buyorder/getbuyorderprintinfo.htm";
//            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, buyorder, clientId, clientKey, TypeRef);
//            BuyorderVo sgw = (BuyorderVo) result.getData();
//            return sgw;
            List<BuyorderGoodsVo> bgvList = new ArrayList<>();
            bgvList = buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderIds(buyorder.getBuyorderId());
            BuyorderVo bv = new BuyorderVo();
            bv = buyorderMapper.getBuyOrderPrintInfo(buyorder);
            bv.setBuyorderGoodsVoList(bgvList);
            return bv;
        } catch (Exception e) {
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
    }

    @Override
    public List<Integer> getBuyorderUserBySaleorderGoodsIds(List<Integer> saleorderGoodsIds) {
        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<List<Integer>>> TypeRef = new TypeReference<ResultInfo<List<Integer>>>() {
            };
            String url = httpUrl + "order/buyorder/getbuyorderuserbysaleordergoodsids.htm";
            ResultInfo<?> result =
                    (ResultInfo<?>) HttpClientUtils.post(url, saleorderGoodsIds, clientId, clientKey, TypeRef);
            List<Integer> buyorderUserNames = (List<Integer>) result.getData();
            return buyorderUserNames;
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Buyorder confirmArrival(Buyorder buyorder, HttpServletRequest request, String id_arrivalNum,String id_nonAllArrivalReason) {
        logger.info("confirmArrival :{},{},{}",new Object[]{com.alibaba.fastjson.JSONObject.toJSONString(buyorder),id_arrivalNum,id_nonAllArrivalReason});

        Long time = DateUtil.sysTimeMillis();
        List<BuyorderGoods> buyorderGoodsList = new ArrayList<>();
        BuyorderVo buyorderVo = buyorderMapper.getBuyorderVoById(buyorder.getBuyorderId());
        parseIdStr(buyorder, id_arrivalNum, time, buyorderGoodsList);

        buyorder.setGoodsList(buyorderGoodsList);


        // [订单流]直发订单，确认收货更新物流快递单签收状态
        updateSelectedExpressArrivalStatus(buyorder, request, id_nonAllArrivalReason);

        try {
            Buyorder res = confirmArrivalByBuyorder(buyorder);
            if (res != null) {
                orderCommonService.updateBuyOrderDataUpdateTime(res.getBuyorderId(), null, OrderDataUpdateConstant.BUY_ORDER_CONFIRM);
                ///采购单总金额大于0且不为未付款才能生成催票任务
                logger.info("采购单催票任务判断，采购单信息：{}",JSON.toJSONString(buyorderVo));
                if (buyorderVo.getTotalAmount().compareTo(new BigDecimal(0)) > 0
                        && buyorderVo.getPaymentStatus() != BuyorderVo.NOT_PAYMENT) {
                    generateEarlyWarningTask(buyorder);
                }
            }
            return res;
        } catch (Exception e) {
            logger.error("采购确认收货发生异常，", e);
            return null;
        }
    }

    private void updateSelectedExpressArrivalStatus(Buyorder buyorder, HttpServletRequest request, String id_nonAllArrivalReason) {

        try{
            updateExpressDetailNum(buyorder,id_nonAllArrivalReason);
            updateExpressArrivalStatus(buyorder);
            impactOnUpdateExpressDetailNum(buyorder,request);
            checkExpressEnableReceive(buyorder.getBuyorderId());

        }catch (Exception e){
            logger.info("确认收货更新签收状态&修改快递发货数量失败！"+e.getMessage());
            throw new RuntimeException("确认收货更新签收状态失败");
        }
    }

    /**
     * 重新计算直发采购订单的ExpressEnableReceive字段
     *
     * @param buyorderId
     */
    @Override
    public void checkExpressEnableReceive(Integer buyorderId) {
        //是否为直发订单
        Buyorder buyorder = buyorderMapper.selectByPrimaryKey(buyorderId);
        if (ObjectUtil.isNotNull(buyorder) && 1 == buyorder.getDeliveryDirect()){
            //该订单下所有的快递
            List<Express> expressList = expressMapper.selectExpressListByBuyOrderId(buyorderId);
            logger.info("采购订单id:{}，同步快递是否可收货字段，快递信息:{}",buyorderId,JSON.toJSONString(expressList));
            Buyorder updateObj = new Buyorder();
            updateObj.setBuyorderId(buyorderId);
            //快递中是否有 EnableReceive == 1 && arrivalStatus == 0 ,有则ExpressEnableReceive = 1,否则为0
            updateObj.setExpressEnableReceive(expressList.stream()
                    .anyMatch(e -> (1 == e.getEnableReceive() && 0 == e.getArrivalStatus())) ? 1 : 0);
            buyorderMapper.updateByPrimaryKeySelective(updateObj);
            return;
        }
        logger.info("采购订单id:{},不是直发订单，不需要同步ExpressEnableReceive字段",buyorderId);
    }

    /**
     * 自动触发物流编辑产生影响
     * @see BuyorderController#saveEditExpress(javax.servlet.http.HttpServletRequest, com.vedeng.logistics.model.Express, java.lang.String, java.math.BigDecimal, java.lang.String, java.lang.String, com.vedeng.order.model.vo.BuyorderVo, javax.servlet.http.HttpSession, java.lang.String)
     * Copy from 【保存编辑快递信息】,作了部分调整
     * @param buyorder
     * @param request
     */
    private void impactOnUpdateExpressDetailNum(Buyorder buyorder,HttpServletRequest request) {

//        User currentUser = getSessionUser(request);
        if (CollectionUtils.isEmpty(buyorder.getGoodsList())) {
            return;
        }
        long sysTimeMillis = DateUtil.sysTimeMillis();

        List<Integer> expressIdList = buyorder.getGoodsList().stream().map(item -> item.getExpressId()).distinct().collect(Collectors.toList());
        List<Express> expressList = expressDetailMapper.selectAllInRelatedKey(expressIdList);

        expressList.forEach(express->{
            express.setAddTime(sysTimeMillis);
//            express.setCreator(currentUser.getUserId());
//            express.setUpdater(currentUser.getUserId());
            express.setModTime(sysTimeMillis);
            express.setIsEnable(ErpConst.ONE);
            express.setBusinessType(SysOptionConstant.ID_515);
        });

        expressList.forEach(express -> {

            ExpressService expressService = SpringContextHolder.getBean(ExpressService.class);
            BuyorderService buyorderService = SpringContextHolder.getBean(BuyorderService.class);
            BuyorderInfoSyncService buyorderInfoSyncService = SpringContextHolder.getBean(BuyorderInfoSyncService.class);

            ResultInfo result = expressService.saveExpress(express);
            if (result == null || result.getCode() == -1) {
                throw new RuntimeException("saveEditExpress error : " + result.getMessage());
            }

            // 更新采购单发货状态 VDERP-2431
            buyorderService.updateBuyorderDeliveryStatus(buyorder.getBuyorderId());

            // VDERP-8759 订单流发货状态同步
            buyorderInfoSyncService.syncDeliveryStatus(buyorder.getBuyorderId(), 2);

            String id_num_price = AssemblyNecessaryData(express);
            // 直发采购单 + 订单类型0销售订单采购  推送物流信息到前端
            pushExpressToWeb((Express) result.getData(), buyorder.getBuyorderId(), id_num_price, "编辑");

            // 采购修改快递处理直发订单账期编码监管
            buyorderService.dealDirectOrderPeriodManagement((Express) result.getData(), buyorder.getBuyorderId(),ErpConst.OperateType.UPDATE_OPERATE_END);
        });

    }

    /**
     * TODO 两边同步修改 Copy
     * @see BuyorderController#pushExpressToWeb(com.vedeng.logistics.model.Express, java.lang.Integer, java.lang.String, java.lang.String)
     * @param reExpress
     * @param buyOrderId
     * @param id_num_price_sku
     * @param action
     */
    public void pushExpressToWeb(Express reExpress, Integer buyOrderId,String id_num_price_sku,String action) {
        if (reExpress == null) {
            logger.info("保存物流信息失败");
            return;
        }
        BuyorderVo buyorderVo = getBuyOrderVoForDeliveryDirect(buyOrderId);
        if (null == buyorderVo) {
            // 非直发销售单
            return;
        }
        Integer saleOrderId = buyorderVo.getSaleorderId();
        if (null != saleOrderId && 0 != saleOrderId) {
            //业务逻辑还没有确认清楚，暂时注释
            Set<Integer> specialGoods= goodsMapper.getSpecialGoodsList()
                    .stream().map(Goods::getGoodsId).collect(Collectors.toSet());
            LogisticsOrderData logisticsOrderData = new LogisticsOrderData();
            List<LogisticsOrderGoodsData> logisticsOrderGoodsDataList = new ArrayList<>();
            int countZeroSum = 0;
            String[] params = id_num_price_sku.split("_");
            if (params.length > 0) {
                for (String line : params) {
                    String[] rows = line.split("\\|");
                    LogisticsOrderGoodsData logisticsOrderGoodsData = new LogisticsOrderGoodsData();
                    if (null != rows[1]) {
                        int skuN = Integer.parseInt(rows[1]);
                        try {
                            if(specialGoods.contains(Integer.parseInt(rows[3].substring(1)))){
                               continue;
                            }
                        }catch (Exception e){
                            logger.error(line,e);
                        }
                        if (skuN == 0  ) {
                            countZeroSum += 1;
                            continue;
                        }
                        //数量放到rabbitmq数据
                        logisticsOrderGoodsData.setNum(skuN);
                    }
                    if (null != rows[3]) {
                        //放到rabbitmq数据
                        logisticsOrderGoodsData.setSkuNo(rows[3]);
                    }
                    //rabbitmq需要发送的商品数据
                    logisticsOrderGoodsDataList.add(logisticsOrderGoodsData);
                }
                logisticsOrderData.setOrderGoodsLogisticsDataList(logisticsOrderGoodsDataList);
                // 推送前端
                logger.info("采购单包裹信息推送 reExpress:{},saleOrderId:{},logisticsOrderData:{}",
                        JSON.toJSONString(reExpress), saleOrderId, JSON.toJSONString(logisticsOrderData));
                ExpressService expressService = SpringContextHolder.getBean(ExpressService.class);
                expressService.pushExpressToWeb(reExpress,"1",saleOrderId,countZeroSum,logisticsOrderData,action,true);
            }
        }


    }

    /**
     * 组装字符串 id_num_price
     * @param express
     * @return
     */
    private String AssemblyNecessaryData(Express express) {

        if (CollectionUtils.isEmpty(express.getExpressDetail())) {
            throw new RuntimeException("saveEditExpress error : " + "缺少物流详情信息 - " +JSON.toJSONString(express));
        }

        StringJoiner stringJoiner = new StringJoiner("_");
        express.getExpressDetail().forEach(expressDetail -> {
            // 获取当前物流详情SKU对应单价和数量
            BuyorderGoods buyorderGoods = buyorderGoodsMapper.selectByPrimaryKey(expressDetail.getRelatedId());
            if (Objects.isNull(buyorderGoods) || Objects.isNull(buyorderGoods.getPrice())) {
                buyorderGoods.setPrice(BigDecimal.ZERO);
            }
            StringJoiner innerStringJoiner = new StringJoiner("|");
            innerStringJoiner.add(String.valueOf(expressDetail.getRelatedId()));
            innerStringJoiner.add(String.valueOf(expressDetail.getNum()));
            innerStringJoiner.add(String.valueOf(buyorderGoods.getPrice()));
            innerStringJoiner.add(buyorderGoods.getSku());
            stringJoiner.add(innerStringJoiner.toString());
        });

        return stringJoiner.toString();
    }

    /**
     * 更新物流详情数量(运费不需要更改) 记录本次发货数量&运费
     * @param buyorder
     */
    private void updateExpressDetailNum(Buyorder buyorder,String id_nonAllArrivalReason) {

        // 采购修改快递处理直发订单账期编码监管
        List<Integer> expressIdList = buyorder.getGoodsList().stream().map(item -> item.getExpressId()).distinct().collect(Collectors.toList());
        expressIdList.forEach(expressId->{
            Express express = expressMapper.selectExpressByPrimaryKey(expressId);
            dealDirectOrderPeriodManagement(express, buyorder.getBuyorderId(),ErpConst.OperateType.UPDATE_OPERATE_FRONT);
        });

        // 更新发货数量
        buyorder.getGoodsList().forEach(item -> {

            ExpressDetail expressDetail = new ExpressDetail();
            expressDetail.setExpressId(item.getExpressId());
            expressDetail.setRelatedId(item.getBuyorderGoodsId());
            expressDetail.setNum(item.getArrivalNum());
            String valueByKVString = getValueByKVString(item.getExpressId(),item.getBuyorderGoodsId(), id_nonAllArrivalReason);
            expressDetail.setNonAllArrivalReason(Objects.isNull(valueByKVString)?"":valueByKVString);
            expressDetailMapper.updateExpressDetailNum(expressDetail);

            // 更新采购单商品进程描述
            List<Integer> buyOrderGoodsIdList = Arrays.asList(item.getBuyorderGoodsId());
            List<ExpressArrivalDetailVo> expressArrivalDetailVos = expressDetailMapper.getExpressArrivalDetailByRelatedIdListAndBusinessType(buyOrderGoodsIdList, 515);
            int deliverySum = expressArrivalDetailVos.stream().mapToInt(ExpressArrivalDetailVo::getDeliveryNum).sum();
            BuyorderGoods buyorderGoods = buyorderGoodsMapper.selectByPrimaryKey(item.getBuyorderGoodsId());
            if (Objects.isNull(buyorderGoods)) {
                return;
            }
            buyorderGoodsMapper.updateProcessDescByBuyOrderGoodsId(item.getBuyorderGoodsId(),
                    "供应商已发货(" + (deliverySum + item.getArrivalNum()) + "/" + (buyorderGoods.getNum() - deliverySum - item.getArrivalNum()) + ")");
        });

        // 因为发货数量变更导致产品运费也发生变化
        buyorder.getGoodsList()
                .stream()
                .mapToInt(BuyorderGoods::getExpressId)
                .distinct()
                .forEach(item -> {
            setFreightByRelatedKey(item);
        });
    }

    /**
     * 获取当前快递SKU的运费
     * @param expressId
     * @return
     */
    private void setFreightByRelatedKey(Integer expressId) {

        if (Objects.isNull(expressId)) {
            return;
        }

        List<ExpressDetail> expressDetailList = expressDetailMapper.selectAllByExpressId(expressId);
        if (CollectionUtils.isEmpty(expressDetailList)) {
            return;
        }

        // 获取单次快递总运费
        Double sumFreight = 0.00;
        for (ExpressDetail ex : expressDetailList) {
            if (Objects.isNull(ex.getAmount())) {
                continue;
            }
            sumFreight += ex.getAmount().doubleValue();
        }

        // 获取单次快递涉及采购SKU -- 总价值
        List<BuyorderGoodsVo> buyorderGoodsVoList = buyorderGoodsMapper.selectExpressInfoByRelatedKey(expressDetailList);
        if (CollectionUtils.isEmpty(buyorderGoodsVoList)) {
            return;
        }
        buyorderGoodsVoList.stream().forEach(item -> {
            if (Objects.isNull(item)) {
                return;
            }
            item.setNum(ErpConst.ZERO);
            if (Objects.isNull(item.getPrice())) {
                item.setPrice(BigDecimal.ZERO);
            }
            expressDetailList.stream().forEach(innerItem -> {
                if (item.getBuyorderGoodsId().equals(innerItem.getRelatedId())) {
                    item.setNum(innerItem.getNum());
                    return;
                }
            });
        });
        Double sumPrice = buyorderGoodsVoList.stream().mapToDouble(item -> item.getNum() * item.getPrice().doubleValue()).sum();

        // 分配运费 -- 刷新本次运费所有数据
        // 运费价格 *（产品单价*产品数量）/ 所有产品总价 -- 保留两位小数
        for (int i = 0; i < expressDetailList.size(); i++) {

            Double toDistributedSumFreight = 0.00;
            Double toDistributedFreight = 0.00;

            if (i == expressDetailList.size() - 1) {
                toDistributedFreight = sumFreight - toDistributedSumFreight;
            } else {
                ExpressDetail expressDetail = expressDetailList.get(i);
                Optional<BuyorderGoodsVo> first = buyorderGoodsVoList.stream().filter(item -> expressDetail.getRelatedId().equals(item.getBuyorderGoodsId())).findFirst();
                if (!first.isPresent()) {
                    BigDecimal tempFreight = new java.math.BigDecimal(toDistributedFreight);
                    tempFreight = tempFreight.setScale(ErpConst.TWO, BigDecimal.ROUND_HALF_UP);
                    expressDetailList.get(i).setAmount(tempFreight);
                    continue;
                }
                BuyorderGoodsVo buyorderGoodsVo = first.get();

                toDistributedSumFreight += (double) Math.round(sumFreight.doubleValue()
                        * (buyorderGoodsVo.getNum() * buyorderGoodsVo.getPrice().doubleValue()) / sumPrice
                        * 100) / 100;
                toDistributedFreight = (double) Math.round(sumFreight.doubleValue()
                        * (buyorderGoodsVo.getNum() * buyorderGoodsVo.getPrice().doubleValue()) / sumPrice
                        * 100) / 100;
            }
            BigDecimal tempFreight = new java.math.BigDecimal(toDistributedFreight);
            tempFreight = tempFreight.setScale(ErpConst.TWO, BigDecimal.ROUND_HALF_UP);

            // 分配运费金额
            expressDetailList.get(i).setAmount(tempFreight);
        }

        // 数据落地
        expressDetailList.stream().forEach(item -> {

            item.setExpressId(null);
            item.setRelatedId(null);
            item.setBusinessType(null);
            item.setNum(null);
            expressDetailMapper.updateByPrimaryKeySelective(item);
        });

    }

    /**
     * 自动更新物流收货状态
     * @param buyorder
     */
    private void updateExpressArrivalStatus(Buyorder buyorder) {

        List<BuyorderGoods> buyOrderGoodsList = buyorder.getGoodsList();
        if(CollectionUtils.isEmpty(buyOrderGoodsList)){
            return;
        }
        logger.info("确认收货更新快递到货-签收状态，{}",buyOrderGoodsList);
        expressMapper.updateArrivalStatusByRelatedKey(buyOrderGoodsList);

    }


    private void generateEarlyWarningTask(Buyorder buyorder) {
        List<SysOptionDefinition> dictionaryByParentId = sysOptionDefinitionMapper.getDictionaryByParentId(ErpConst.SPECIALGOODS_SYS_PARENT_ID);

        for (BuyorderGoods buyorderGoods : buyorder.getGoodsList()) {
            //VDERP-7931 催票任务增加条件采购单内sku不为特殊商品，如运费或售后维修费，且sku单价不为0
            if(fillteTicketsCondition(buyorderGoods,dictionaryByParentId)) {
                EarlyWarningTaskDto earlyWarningTaskDto = new EarlyWarningTaskDto();
                earlyWarningTaskDto.setRelateBusinessId(buyorderGoods.getBuyorderGoodsId());
                earlyWarningTaskDto.setUrgingTicketNum(buyorderGoods.getArrivalNum());
                logger.info("采购满足退票任务：{}",JSON.toJSONString(earlyWarningTaskDto));
                expeditingTicketsService.create(earlyWarningTaskDto);
            }
        }
    }

    //VDERP-7931 催票任务增加条件采购单内sku不为特殊商品，如运费或售后维修费，且sku单价不为0
    private boolean fillteTicketsCondition(BuyorderGoods buyorderGoods, List<SysOptionDefinition> dictionaryByParentId) {
        BuyorderGoods buyorderGoodsInfo = buyorderGoodsMapper.getbuyorderGoodsInfoByBuyorderGoodsId(buyorderGoods.getBuyorderGoodsId());
        if (Objects.isNull(buyorderGoods.getArrivalNum()) || buyorderGoods.getArrivalNum() < 1) {
            logger.info("确认收货数量小于1 该采购单商品跳过 buyorderGoods:{}", JSON.toJSONString(buyorderGoods));
            return false;
        }

        if(buyorderGoodsInfo.getPrice() == null || buyorderGoodsInfo.getPrice().compareTo(BigDecimal.ZERO) <= 0){
            return false;
        }
        if(CollectionUtils.isNotEmpty(dictionaryByParentId)){
            Optional<SysOptionDefinition> any = dictionaryByParentId.stream().filter(e -> e.getComments().equals(buyorderGoodsInfo.getGoodsId().toString())).findAny();
            if(any.isPresent()){
                return false;
            }
        }
        return true;
    }

    private Buyorder confirmArrivalByBuyorder(Buyorder buyorder) {
        logger.info("确认收货开始 id:{}", buyorder.getBuyorderId());
        if (null != buyorder.getGoodsList()) {
            List<BuyorderGoods> list = buyorder.getGoodsList();
            for (BuyorderGoods buyorderGoods : list) {
                BuyorderGoods newBuyOrderGoods = new BuyorderGoods();
                newBuyOrderGoods.setBuyorderGoodsId(buyorderGoods.getBuyorderGoodsId());
                newBuyOrderGoods.setArrivalTime(buyorderGoods.getArrivalTime());
                BuyorderGoods buyOrderGoodsInfo =
                        buyorderGoodsMapper.selectByPrimaryKey(buyorderGoods.getBuyorderGoodsId());
                Integer curArrivalNum = buyorderGoods.getArrivalNum(); // 当前收货数量
                Integer dbArrivalNum = buyOrderGoodsInfo.getArrivalNum();
                Integer totalNum = buyOrderGoodsInfo.getNum();
                curArrivalNum = ((curArrivalNum + dbArrivalNum) <= totalNum) ? curArrivalNum : (totalNum - dbArrivalNum);
                if ((curArrivalNum + dbArrivalNum) == totalNum) {
                    newBuyOrderGoods.setArrivalStatus(2); // 全部收货
                } else if (curArrivalNum > 0 || buyOrderGoodsInfo.getArrivalNum() > 0) {
                    newBuyOrderGoods.setArrivalStatus(1); // 部分收货
                } else {
                    newBuyOrderGoods.setArrivalStatus(0); // 未收货
                }
                newBuyOrderGoods.setArrivalNum(curArrivalNum + dbArrivalNum);
                buyorderGoodsMapper.updateByPrimaryKeySelective(newBuyOrderGoods);
                //TODO 同步对应的销售商品订单
                syncSaleOrderGood(newBuyOrderGoods);
                //syncSaleOrderGoodsArrivalStatus(newBuyOrderGoods);
            }
        }

        // 获取订单产品信息
        List<BuyorderGoodsVo> goodsList =
                buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderIdNoSpecial(buyorder.getBuyorderId());
        int buyorderArrivalStatus = 1;
        int arrivalStatus0Num = 0;
        int arrivalStatus2Num = 0;
        List<Integer> bogIds = new ArrayList<>(goodsList.size());
        for (BuyorderGoodsVo sg : goodsList) {
            if (sg.getArrivalStatus() == 0) {
                arrivalStatus0Num++;
            }
            if (sg.getArrivalStatus() == 2) {
                arrivalStatus2Num++;
            }
            bogIds.add(sg.getBuyorderGoodsId());
        }
        if (arrivalStatus0Num == goodsList.size()) {
            buyorderArrivalStatus = 0;
        }
        if (arrivalStatus2Num == goodsList.size()) {
            buyorderArrivalStatus = 2;
        }
        Buyorder buyorderExtra = new Buyorder();
        buyorderExtra.setBuyorderId(buyorder.getBuyorderId());
        buyorderExtra.setArrivalStatus(buyorderArrivalStatus);
        buyorderExtra.setArrivalTime(DateUtil.sysTimeMillis());
        if (buyorderArrivalStatus == 2) {
            buyorderExtra.setDeliveryStatus(2);
            buyorderExtra.setDeliveryTime(buyorderExtra.getArrivalTime());
        }
        logger.info("采购订单更新状态信息 buyorderExtra:{}", JSON.toJSONString(buyorderExtra));
        buyorderMapper.updateByPrimaryKeySelective(buyorderExtra);

        try {
            syncSaleOrderArrivalStatus(bogIds);
        } catch (Exception e) {
            logger.error("同步销售单状态出错", e);
        }

        return buyorder;
    }

    /**
     * @param buyOrderGoodIds 采购单商品集合
     */
    private void syncSaleOrderArrivalStatus(List<Integer> buyOrderGoodIds) {
        if (buyOrderGoodIds.size() <= 0) {
            return;
        }
        logger.info("同步销售单收发货状态 buyOrderGoodIds:{}", JSON.toJSONString(buyOrderGoodIds)) ;
        // 同步销售单收发货状态
        List<Integer> saleOrderIds = buyorderMapper.getSaleOrderIdByBuyOrderGoodIds(buyOrderGoodIds);
        if (CollectionUtils.isNotEmpty(saleOrderIds)) {
            for (Integer saleOrderId : saleOrderIds) {
                Saleorder dbSaleOrder = saleorderMapper.getSaleOrderById(saleOrderId);
                if (null == dbSaleOrder) {
                    continue;
                }

                List<SaleorderGoods> saleOrderGoodsList = saleorderMapper.getSaleorderGoodsByIdNoSpecial(dbSaleOrder.getSaleorderId());

                logger.info("采购商品关联销售单的所有销售商品信息 saleOrderGoodsList :{}", JSON.toJSONString(saleOrderGoodsList));
//                Set<String> specialGoods = new HashSet<>();
//                try {
//                    specialGoods = goodsMapper.getSpecialGoodsList()
//                            .stream().map(Goods::getSku).collect(Collectors.toSet());
//                } catch (Exception e) {
//                    specialGoods = new HashSet<>();
//                }
                if (CollectionUtils.isNotEmpty(saleOrderGoodsList)) {
                    logger.info("syncSaleOrderArrivalStatus--更新销售订单：{}中特殊虚拟商品收货状态",saleOrderId);
                    //更新特殊商品收货
                    int i = saleorderGoodsMapper.updateAllTsGoodsInfo(saleOrderId);
                    //VDERP-11333 【FAQ4436】【功能】【ERP】【销售订单】销售订单收货状态有误
                    //场景遗漏，如果销售单商品全部收货，此处需剔除
                    saleOrderGoodsList = saleOrderGoodsList.stream().filter(item -> {
                        if(item.getAfterReturnNum() != null && item.getNum() - item.getAfterReturnNum() == 0){
                            return false;
                        }else {
                            return true;
                        }
                    }).collect(Collectors.toList());
                    int saleOrderArrivalStatus = 1;
                    int arrivalCount0 = 0;
                    int arrivalCount2 = 0;
                    for (SaleorderGoods sog : saleOrderGoodsList) {
                        //特殊商品
//                        if (specialGoods.contains(sog.getSku())) {
//                            continue;
//                        }
                        if (sog.getArrivalStatus() == 0) {
                            arrivalCount0++;
                        }
                        if (sog.getArrivalStatus() == 2) {
                            arrivalCount2++;
                        }
                    }
                    if (arrivalCount0 == saleOrderGoodsList.size()) {
                        saleOrderArrivalStatus = 0;
                    }
                    if (arrivalCount2 == saleOrderGoodsList.size()) {
                        saleOrderArrivalStatus = 2;
                    }
                    if (dbSaleOrder.getArrivalStatus().equals(saleOrderArrivalStatus)) {
                        continue;
                    }

                    Saleorder updateSaleOrder = new Saleorder();
                    // 更新销售单收货状态
                    updateSaleOrder.setSaleorderId(dbSaleOrder.getSaleorderId());
                    updateSaleOrder.setArrivalStatus(saleOrderArrivalStatus);
                    updateSaleOrder.setArrivalTime(DateUtil.sysTimeMillis());
                    if (saleOrderArrivalStatus == 2 && dbSaleOrder.getDeliveryStatus() != 2) {
                        updateSaleOrder.setDeliveryStatus(2);
                        updateSaleOrder.setDeliveryTime(DateUtil.sysTimeMillis());
                    }
                    if (saleOrderArrivalStatus == 1 && dbSaleOrder.getDeliveryStatus() == 0) {
                        updateSaleOrder.setDeliveryStatus(1);
                        updateSaleOrder.setDeliveryTime(DateUtil.sysTimeMillis());
                    }
                    logger.info("更新销售单收货状态信息 updateSaleOrder:{}", JSON.toJSONString(updateSaleOrder));
                    saleorderMapper.updateByPrimaryKeySelective(updateSaleOrder);

                    //BD订单全部收货时，同步订单状态到贝登前台
                    if ( saleOrderArrivalStatus == 2){
                        GlobalThreadPool.submitMessage(new Runnable() {
                            @Override
                            public void run() {
                                try {
                                    Thread.sleep(120000L);
                                } catch (InterruptedException e) {
                                    logger.error("采购直发确认收货延迟失败", e);
                                }
                                String uuid = UUID.randomUUID().toString().replace("-", "");
                                logger.info("开始执行updateBDLogisticsStatus，uuid：{}",uuid);
                                logger.info("采购直发订单延迟发送更新订单信息 updateSaleOrder:{}", JSON.toJSONString(updateSaleOrder));
                                saleorderService.updateBDLogisticsStatus(updateSaleOrder);
                                logger.info("结束执行updateBDLogisticsStatus，uuid：{}",uuid);
                            }
                        });
                    }
                }
            }
        }
    }

    /**
     * 根据采购单商品的状态
     *
     * @param buyorderGood
     */
    public void syncSaleOrderGood(BuyorderGoods buyorderGood) {

        if (buyorderGood == null) {
            return;
        }

        List<SaleorderGoods> saleOrderGoodList = saleorderGoodsMapper.getSaleorderGoodsVoByBuyorderGoodsId(buyorderGood.getBuyorderGoodsId());

        if (CollectionUtils.isEmpty(saleOrderGoodList)) {
            logger.info("采购单商品没有关联的直发销售订单商品 buyorderGood:{}", JSON.toJSONString(buyorderGood));
            return;
        }

        logger.info("采购单商品关联的直发销售订单商品 saleOrderGoodList:{}", JSON.toJSONString(saleOrderGoodList));

        //直发采购单中的商品只会关联一个销售单的商品 所以只取一条数据
        SaleorderGoods saleOrderGood = saleOrderGoodList.stream().findFirst().get();

        BuyorderGoods relateBuyOrder = buyorderGoodsMapper.listNumByDirectSaleorderGoodsIds(Collections.singletonList(saleOrderGood.getSaleorderGoodsId()));
        logger.info("销售订单检索的采购订单信息 relateBuyOrder:{}", JSON.toJSONString(relateBuyOrder));

        SaleorderGoods updateSaleOrderGood = new SaleorderGoods();
        updateSaleOrderGood.setSaleorderGoodsId(saleOrderGood.getSaleorderGoodsId());
        updateSaleOrderGood.setArrivalStatus((saleOrderGood.getNum() - (saleOrderGood.getAfterReturnNum() == null ? 0 : saleOrderGood.getAfterReturnNum())) <= relateBuyOrder.getArrivalNum() ? 2 : 1);
        updateSaleOrderGood.setArrivalTime(System.currentTimeMillis());

        List<Integer> relatedBuyOrderGoodsId = buyorderGoodsMapper.listMultipleNumByDirectSaleorderGoodsIds(Collections.singletonList(saleOrderGood.getSaleorderGoodsId()));
        List<ExpressArrivalDetailVo> expressArrivalDetailVos = expressDetailMapper.getExpressArrivalDetailByRelatedIdListAndBusinessType(relatedBuyOrderGoodsId,515);
        int deliverySum = expressArrivalDetailVos.stream().mapToInt(ExpressArrivalDetailVo::getDeliveryNum).sum();

        updateSaleOrderGood.setDeliveryStatus((saleOrderGood.getNum() - (saleOrderGood.getAfterReturnNum() == null ? 0 : saleOrderGood.getAfterReturnNum())) <= deliverySum ? 2 : 1);
        updateSaleOrderGood.setDeliveryNum(deliverySum);
        updateSaleOrderGood.setDeliveryTime(System.currentTimeMillis());

        logger.info("同步对应的销售商品订单 updateSaleOrderGood:{}", JSON.toJSONString(updateSaleOrderGood));
        saleorderGoodsMapper.updateByPrimaryKeySelective(updateSaleOrderGood);

    }

    // 同步采购单对应得销售单收货状态 销售商品单-采购商品单
    private void syncSaleOrderGoodsArrivalStatus(BuyorderGoods buyorderGoods) {
        if (null == buyorderGoods) {
            return;
        }
        List<SaleorderGoods> rSaleOrderGoods = saleorderGoodsMapper.getSaleorderGoodsVoByBuyorderGoodsId(buyorderGoods.getBuyorderGoodsId());
        if (CollectionUtils.isNotEmpty(rSaleOrderGoods)) {
            for (SaleorderGoods dbSaleOrderGoods : rSaleOrderGoods) {
                if (!dbSaleOrderGoods.getDeliveryDirect().equals(1) || buyorderGoods.getArrivalStatus().equals(0)
                        || dbSaleOrderGoods.getArrivalStatus().equals(2)) {
                    // 过滤非直发,未发货单，或销售已到货
                    return;
                }

                long logTime = System.currentTimeMillis();
                SaleorderGoods updateSaleGoods = new SaleorderGoods();
                updateSaleGoods.setSaleorderGoodsId(dbSaleOrderGoods.getSaleorderGoodsId());
                // 计算销售商品当前到货数量
                BuyorderGoods numBuyOrder = buyorderGoodsMapper.listNumByDirectSaleorderGoodsIds(Collections.singletonList(dbSaleOrderGoods.getSaleorderGoodsId()));

                updateSaleGoods.setArrivalStatus(numBuyOrder.getNum() <= numBuyOrder.getArrivalNum() ? 2 : 1);
                updateSaleGoods.setArrivalTime(logTime);
                if (updateSaleGoods.getArrivalStatus() == 2 && dbSaleOrderGoods.getArrivalStatus() != 2) {
                    // 全部收货更新发货状态
                    updateSaleGoods.setDeliveryStatus(2);
                    updateSaleGoods.setDeliveryTime(logTime);
                    updateSaleGoods.setDeliveryNum(numBuyOrder.getNum());
                }

                logger.info("同步直发销售商品收货状态 num :{}, arrivalNum :{} , buyOrderGoodsId: {},saleOrderGoodsId :{}",
                        numBuyOrder.getNum(), numBuyOrder.getArrivalNum(), buyorderGoods.getBuyorderGoodsId(), dbSaleOrderGoods.getSaleorderGoodsId());
                saleorderGoodsMapper.updateByPrimaryKeySelective(updateSaleGoods);

            }
        }

    }


    /**
     * <b>Description:</b><br>
     * 获取采购入库采购订单的详情(已拦截特殊商品)
     *
     * @param buyorder
     * @param user
     * @return
     * @Note <b>Author:</b> Michael <br>
     * <b>Date:</b> 2018年2月11日 下午1:18:30
     */
    @Override
    public BuyorderVo getBuyorderInDetail(BuyorderVo buyorder, User user) {
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            BuyorderVo buyorderVo = new BuyorderVo();
            buyorderVo.setCompanyId(user.getCompanyId());
            buyorderVo.setBuyorderId(buyorder.getBuyorderId());
            buyorderVo.setBuyorderNo(buyorder.getBuyorderNo());
            buyorderVo.setBuyorderIdList(buyorder.getBuyorderIdList());
            String url = httpUrl + "order/buyorder/getbuyorderindetail.htm";
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, buyorderVo, clientId, clientKey, TypeRef);
            if (result == null || result.getCode() == -1) {
                return null;
            }
            JSONObject json = JSONObject.fromObject(result.getData());
            BuyorderVo bv = JsonUtils.readValue(json.toString(), BuyorderVo.class);
            if (bv != null && bv.getBuyorderGoodsVoList() != null && bv.getBuyorderGoodsVoList().size() > 0) {
                bv.setCreateName(getUserNameByUserId(bv.getCreator()));
                bv.setHomePurchasing(getUserNameByUserId(bv.getUserId()));
                bv.setBuyDepartmentName(getOrgNameByOrgId(bv.getOrgId()));
            }

            // 采购人员
            if (ObjectUtils.notEmpty(bv.getUserId())) {
                bv.setBuyPerson(getUserNameByUserId(bv.getUserId()));
            }
            return bv;
        } catch (IOException e) {
            throw new RuntimeException();
        }
    }

    /**
     * <b>Description:</b><br>
     * 根据采购单查询运费
     *
     * @param buyorderGoodsVo
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2018年3月6日 上午10:14:12
     */
    @Override
    public BuyorderGoodsVo getFreightByBuyorderId(BuyorderGoodsVo buyorderGoodsVo) {
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            String url = httpUrl + "order/buyorder/getfreightbybuyorderid.htm";
            ResultInfo<?> result =
                    (ResultInfo<?>) HttpClientUtils.post(url, buyorderGoodsVo, clientId, clientKey, TypeRef);
            if (result == null || result.getCode() == -1) {
                return null;
            }
            JSONObject json = JSONObject.fromObject(result.getData());
            BuyorderGoodsVo bv = JsonUtils.readValue(json.toString(), BuyorderGoodsVo.class);
            return bv;
        } catch (IOException e) {
            throw new RuntimeException();
        }
    }

    /**
     * <b>Description:</b><br>
     * 保存采购订单的运费
     *
     * @param buyorderGoodsVo
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2018年3月6日 上午11:01:46
     */
    @Override
    public ResultInfo<?> saveBuyorderFreight(BuyorderGoodsVo buyorderGoodsVo, User user) {
        if (buyorderGoodsVo != null && ObjectUtils.isEmpty(buyorderGoodsVo.getBuyorderGoodsId())) {
            buyorderGoodsVo.setAddTime(DateUtil.sysTimeMillis());
            buyorderGoodsVo.setCreator(user.getUserId());
            buyorderGoodsVo.setModTime(DateUtil.sysTimeMillis());
            buyorderGoodsVo.setUpdater(user.getUserId());
        } else {
            buyorderGoodsVo.setModTime(DateUtil.sysTimeMillis());
            buyorderGoodsVo.setUpdater(user.getUserId());
        }
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            String url = httpUrl + "order/buyorder/savebuyorderfreight.htm";
            ResultInfo<?> result =
                    (ResultInfo<?>) HttpClientUtils.post(url, buyorderGoodsVo, clientId, clientKey, TypeRef);
            return result;
        } catch (IOException e) {
            throw new RuntimeException();
        }
    }

    @Override
    public BuyorderVo getBuyorderVoDetailByAjax(Buyorder buyorder, User user) {
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            String url = httpUrl + "order/buyorder/getbuyorderdetailbyajax.htm";
            BuyorderVo buyorderVo = new BuyorderVo();
            buyorderVo.setCompanyId(user.getCompanyId());
            buyorderVo.setBuyorderId(buyorder.getBuyorderId());
            buyorderVo.setBuyorderNo(buyorder.getBuyorderNo());
            buyorderVo.setSearchUserId(user.getUserId());
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, buyorderVo, clientId, clientKey, TypeRef);
            if (result == null || result.getCode() == -1) {
                return null;
            }
            JSONObject json = JSONObject.fromObject(result.getData());
            BuyorderVo bv = JsonUtils.readValue(json.toString(), BuyorderVo.class);
            List<User> userList = userMapper.getAllUserList(user.getCompanyId());

            for (User us : userList) {
                if (us.getUserId().equals(bv.getCreator())) {
                    bv.setCreateName(us.getUsername());
                }
                if (us.getUserId().equals(bv.getUserId())) {
                    bv.setHomePurchasing(us.getUsername());
                }
            }
            bv.setBuyDepartmentName(getOrgNameByOrgId(bv.getOrgId()));

            if (bv != null && bv.getAttachmentList() != null && bv.getAttachmentList().size() > 0) {
                List<Attachment> list = bv.getAttachmentList();
                for (Attachment attachment : list) {
                    for (User us : userList) {
                        if (us.getUserId().equals(attachment.getCreator())) {
                            attachment.setUsername(us.getUsername());
                            break;
                        }
                    }

                }
            }

            if (bv != null && bv.getExpressList() != null && bv.getExpressList().size() > 0) {
                List<Express> list = bv.getExpressList();
                for (Express express : list) {
                    for (User us : userList) {
                        if (us.getUserId().equals(express.getCreator())) {
                            express.setUpdaterUsername(us.getUsername());
                            break;
                        }
                    }
                }
            }

            if (bv != null && bv.getInvoiceList() != null && bv.getInvoiceList().size() > 0) {
                List<Invoice> list = bv.getInvoiceList();
                for (Invoice invoice : list) {
                    for (User us : userList) {
                        if (us.getUserId().equals(invoice.getCreator())) {
                            invoice.setCreatorName(us.getUsername());
                            break;
                        }
                    }

                }
            }
            // 采购人员
            if (ObjectUtils.notEmpty(bv.getUserId())) {
                for (User us : userList) {
                    if (us.getUserId().equals(bv.getUserId())) {
                        bv.setBuyPerson(us.getUsername());
                        break;
                    }
                }
            }
            return bv;
        } catch (IOException e) {
            throw new RuntimeException();
        }
    }

    /**
     * <b>Description:</b><br>
     * 获取采购订单的详情异步
     *
     * @param buyorder
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年7月19日 上午10:13:30
     */
    @Override
    public List<BuyorderGoodsVo> getBuyorderGoodsVoListByAjax(BuyorderVo buyorderVo, User user) {
        try {
            // int size = (int)
            // Math.ceil((double)buyorderVo.getBuyorderGoodsCount()/(double)10);
            if (ObjectUtils.notEmpty(buyorderVo.getCurrentCount())) {
                buyorderVo.setCurrentCount(2);
            }
            final TypeReference<ResultInfo<List<BuyorderGoodsVo>>> typeRef =
                    new TypeReference<ResultInfo<List<BuyorderGoodsVo>>>() {
                    };
            String urlFor = httpUrl + "order/buyorder/getbuyordergoodslistbyajax.htm";
            ResultInfo<?> result = (ResultInfo<List<BuyorderGoodsVo>>) HttpClientUtils.post(urlFor, buyorderVo,
                    clientId, clientKey, typeRef);
            if (result == null || result.getCode() == -1) {
                return null;
            }
            List<BuyorderGoodsVo> bvlist = (List<BuyorderGoodsVo>) result.getData();

            return bvlist;
        } catch (IOException e) {
            // TODO Auto-generated catch block
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
    }

    @Override
    @Transactional
    public ResultInfo<?> saveBuyorderAttachmentForAadmin(PurchaseContractDto purchaseContractDto){
        //先异步校验
        VerifiesInfo selectDto = new VerifiesInfo();
        selectDto.setStatus(ErpConst.ZERO);
        selectDto.setRelateTable(ErpConst.PURCHASE_CONTRACT_VERIFY);
        selectDto.setRelateTableKey(purchaseContractDto.getBuyorderId());
        List<VerifiesInfo> infoList = verifiesInfoMapper.getVerifiesInfo(selectDto);
        if (CollUtil.isNotEmpty(infoList)){
            return ResultInfo.error("审核中无法再次提交,请刷新订单页");
        }
        Integer creator = 1;
        //User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        long nowL = DateUtil.sysTimeMillis();
        for (Attachment attachment : purchaseContractDto.getAttachmentList()) {
            Attachment insertAttachment= new Attachment();
            insertAttachment.setRelatedId(purchaseContractDto.getBuyorderId());
            insertAttachment.setAttachmentFunction(SysOptionConstant.ID_514);
            insertAttachment.setDomain(attachment.getDomain());
            insertAttachment.setUri(attachment.getFilePath());
            insertAttachment.setName(attachment.getFileName());
            insertAttachment.setOssResourceId(attachment.getOssResourceId());
            String suffix = attachment.getPrefix();
            insertAttachment.setSuffix(suffix);
            insertAttachment.setAttachmentType(SysOptionConstant.ID_461);
            insertAttachment.setCreator(creator);
            insertAttachment.setAddTime(nowL);
            insertAttachment.setAlt(ErpConst.ONE.equals(purchaseContractDto.getAltType()) ? "标准合同" : "非标准合同");
            attachmentMapper.insertSelective(insertAttachment);
        }
        //开启采购合同审核流
        //this.startBuyorderContract(request,purchaseContractDto.getBuyorderId(),user);
        //更新采购合同审核状态
        VerifiesInfo verifiesInfo = new VerifiesInfo();
        verifiesInfo.setRelateTable(ErpConst.PURCHASE_CONTRACT_VERIFY);
        verifiesInfo.setRelateTableKey(purchaseContractDto.getBuyorderId());
        verifiesInfo.setVerifiesType(ErpConst.SYS_OPT_PURCHASE_CONTRACT);
        verifiesInfo.setStatus(ErpConst.ONE);
        verifiesInfo.setAddTime(nowL);
        verifiesInfoMapper.insertSelective(verifiesInfo);
        //更新buyorder_data的IS_CONTRACT_RETURN_STATUS
        buyorderDataMapper.updateContractReturnStatus(purchaseContractDto.getBuyorderId(),ErpConst.ONE);
        return ResultInfo.success();
    }

    /**
     * 处理采购单附件上传事件
     *
     * @param buyorderId 采购单ID
     * @param buyorderNo 采购单编号
     * @param fileName 附件名称
     * @param filePath 附件路径
     * @param domain 附件域名
     * @param attachmentType 附件类型
     * @param attachmentFunction 附件功能类型
     * @param creator 创建人ID
     * @return ResultInfo 处理结果
     */
    @Transactional
    public ResultInfo<?> saveBuyorderAttachmentForAadmin(Integer buyorderId, String buyorderNo, String fileName,
                                                        String filePath, String domain, Integer attachmentType,
                                                        Integer attachmentFunction, Integer creator,String ossResourceId) {
        logger.info("处理采购单附件上传事件 buyorderId:{}, fileName:{}", buyorderId, fileName);

        // 构建PurchaseContractDto对象
        PurchaseContractDto purchaseContractDto = new PurchaseContractDto();
        purchaseContractDto.setBuyorderId(buyorderId);
        purchaseContractDto.setBuyorderNo(buyorderNo);
        purchaseContractDto.setAltType(1); // 默认为标准合同

        // 构建附件列表
        List<Attachment> attachmentList = new ArrayList<>();
        Attachment attachment = new Attachment();
        attachment.setPrefix("pdf");
        attachment.setAttachmentType(attachmentType != null ? attachmentType : SysOptionConstant.ID_461);
        attachment.setAttachmentFunction(attachmentFunction != null ? attachmentFunction : SysOptionConstant.ID_514);
        attachment.setDomain(domain);
        attachment.setFilePath(filePath);
        attachment.setFileName(fileName);
        attachment.setOssResourceId(ossResourceId);
        attachmentList.add(attachment);

        purchaseContractDto.setAttachmentList(attachmentList);

        // 调用原有方法保存附件
        return saveBuyorderAttachmentForAadmin(purchaseContractDto);
    }


    @Override
    @Transactional
    public ResultInfo<?> saveBuyorderAttachment(HttpServletRequest request, PurchaseContractDto purchaseContractDto) {
        //先异步校验
        VerifiesInfo selectDto = new VerifiesInfo();
        selectDto.setStatus(ErpConst.ZERO);
        selectDto.setRelateTable(ErpConst.PURCHASE_CONTRACT_VERIFY);
        selectDto.setRelateTableKey(purchaseContractDto.getBuyorderId());
        List<VerifiesInfo> infoList = verifiesInfoMapper.getVerifiesInfo(selectDto);
        if (CollUtil.isNotEmpty(infoList)){
            return ResultInfo.error("审核中无法再次提交,请刷新订单页");
        }

        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        long nowL = DateUtil.sysTimeMillis();
        for (Attachment attachment : purchaseContractDto.getAttachmentList()) {
            Attachment insertAttachment= new Attachment();
            insertAttachment.setRelatedId(purchaseContractDto.getBuyorderId());
            insertAttachment.setAttachmentFunction(SysOptionConstant.ID_514);
            insertAttachment.setDomain(attachment.getDomain());
            insertAttachment.setUri(attachment.getFilePath());
            insertAttachment.setName(attachment.getFileName());
            insertAttachment.setOssResourceId(attachment.getOssResourceId());
            String suffix = attachment.getPrefix();
            insertAttachment.setSuffix(suffix);
            insertAttachment.setAttachmentType(SysOptionConstant.ID_461);
            insertAttachment.setCreator(user.getUserId());
            insertAttachment.setAddTime(nowL);
            insertAttachment.setAlt(ErpConst.ONE.equals(purchaseContractDto.getAltType()) ? "标准合同" : "非标准合同");
            attachmentMapper.insertSelective(insertAttachment);
        }
        //开启采购合同审核流
        this.startBuyorderContract(request,purchaseContractDto.getBuyorderId(),user);
        //更新采购合同审核状态
        VerifiesInfo verifiesInfo = new VerifiesInfo();
        verifiesInfo.setRelateTable(ErpConst.PURCHASE_CONTRACT_VERIFY);
        verifiesInfo.setRelateTableKey(purchaseContractDto.getBuyorderId());
        verifiesInfo.setVerifiesType(ErpConst.SYS_OPT_PURCHASE_CONTRACT);
        verifiesInfo.setStatus(ErpConst.ZERO);
        verifiesInfo.setAddTime(nowL);
        verifiesInfoMapper.insertSelective(verifiesInfo);
        //更新buyorder_data的IS_CONTRACT_RETURN_STATUS
        buyorderDataMapper.updateContractReturnStatus(purchaseContractDto.getBuyorderId(),ErpConst.ONE);
        return ResultInfo.success();
    }

    @Override
    public void confirmArrivalSaveBuyorderInvoiceStatus(Integer buyorderId) {
        try {
            logger.info("开始保存采购单收票状态信息 buyorderId:{}", buyorderId);
            if (buyorderId == null){
                return;
            }
            BuyorderVo buyorderInfo = buyorderMapper.getBuyorderVoById(buyorderId);
            buyorderInfo.setRealAmount(buyorderMapper.getRealAmount(buyorderId));
            logger.info("采购单的真实总价为 buyorderId :{},  realAmount :{}", buyorderId, buyorderInfo.getRealAmount());

            //获取采购单的商品信息
            List<BuyorderGoodsVo> buyorderGoodsVos = new ArrayList<>();
            for (BuyorderGoodsVo item : buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderId(buyorderId)){
                if (item.getIsDelete() == 0){
                    buyorderGoodsVos.add(item);
                }
            }
            buyorderInfo.setBuyorderGoodsVoList(buyorderGoodsVos);
            logger.info("采购单的基本信息 buyorderInfo:{}", JSON.toJSONString(buyorderInfo));
            int invoiceStatus;
            /**
             * 1.若采购单总额=0
             * （1）当采购单的到货状态变为“全部到货”时，将收票状态更新为：全部收票；
             * （2）否则为：未收票
             */
            if (BigDecimal.ZERO.compareTo(buyorderInfo.getTotalAmount()) == 0) {
                logger.info("采购订单的采购总额为0 buyorderId:{}", JSON.toJSONString(buyorderId));
                if (buyorderInfo.getArrivalStatus().equals(2)) {
                    logger.info("采购订单总额为0并且全部到货 buyorderId:{}", JSON.toJSONString(buyorderId));
                    invoiceStatus = 2;
                } else {
                    logger.info("采购订单总额为0未全部到货 buyorderId:{}", JSON.toJSONString(buyorderId));
                    invoiceStatus = 0;
                }
            } else {
                /**
                 * 2（1）若采购单各sku的录票数量合计=0，则更新收票状态为：未收票
                 * （2）若采购单中每个商品均满足以下条件，则采购单的收票状态为“全部收票”：
                 * ① -1≤（商品的实际金额-商品的收票总额）≤1
                 * ② 商品的收票数量=商品的实际数量
                 */
                logger.info("采购订单的采购总额不为0 buyorderId:{}", JSON.toJSONString(buyorderId));
                BigDecimal invoiceTotalNum = BigDecimal.ZERO;
                boolean isComplete = true;
                boolean isRebate = true;
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(buyorderInfo.getBuyorderGoodsVoList())) {
                    //去掉采购单价为0的采购商品
                    List<BuyorderGoodsVo> buyorderGoodsVoList = new ArrayList<>();
                    for (BuyorderGoodsVo item : buyorderInfo.getBuyorderGoodsVoList()){
                        if (item.getPrice().compareTo(BigDecimal.ZERO) != 0){
                            buyorderGoodsVoList.add(item);
                        }
                    }
                    for (BuyorderGoodsVo buyorderGoodsVo : buyorderGoodsVoList) {
                        BigDecimal invoiceTotalAmount = buyorderMapper.getHaveInvoiceTotalAmount(buyorderGoodsVo.getBuyorderGoodsId());
                        logger.info("采购订单商品的收票总额 buyorderGoodsId:{}，invoiceTotalAmount:{}", buyorderGoodsVo.getBuyorderGoodsId(), invoiceTotalAmount);

                        //采购商品的已收票数量
                        BigDecimal haveInvoiceNums = buyorderMapper.getHaveInvoiceNums(buyorderGoodsVo.getBuyorderGoodsId());
                        logger.info("采购订单商品的已收票数量 buyorderGoodsId:{}, haveInvoiceNums:{}", buyorderGoodsVo.getBuyorderGoodsId(), haveInvoiceNums);

                        if (haveInvoiceNums != null) {
                            invoiceTotalNum = invoiceTotalNum.add(haveInvoiceNums);
                        }

                        //采购商品的实际数量
                        BigDecimal realTotalNum = buyorderMapper.getGoodsTotalNum(buyorderGoodsVo.getBuyorderGoodsId());
                        logger.info("采购订单商品的实际数量 buyorderGoodsId:{}, realTotalNum:{}", buyorderGoodsVo.getBuyorderGoodsId(), realTotalNum);
//                        VDERP-6880修复采购单sku采购又全部售后的情况导致的收票状态不正确，原本全部收票的状态误判为部分收票
                        if(realTotalNum.compareTo(new BigDecimal(0)) == 0 && haveInvoiceNums == null){
                            continue;
                        }

                        //采购订单的实际总额
                        // VDERP-16204 计算采购单实际金额时，需要减去每个sku的返利金额
                        BigDecimal realPrice = buyorderGoodsVo.getPrice().subtract(buyorderGoodsVo.getRebatePrice());

                        BigDecimal realTotalAmount = realPrice.multiply(realTotalNum);

                        if (realPrice.compareTo(BigDecimal.ZERO) <= 0) {
                            continue;
                        }

                        if(realPrice.compareTo(BigDecimal.ZERO) > 0){
                            //商品的收票数量!=商品的实际数量
                            if (haveInvoiceNums == null || haveInvoiceNums.compareTo(realTotalNum) != 0) {
                                isComplete = false;
                                logger.info("商品的收票数量不等于商品的实际数量， 采购商品：{}", JSON.toJSONString(buyorderGoodsVo));
                            }
                            isRebate = false;
                        }
                        logger.info("采购订单商品的实际总额 buyorderGoodsId:{}, realTotalAmount:{}", buyorderGoodsVo.getBuyorderGoodsId(),realTotalAmount);

                        //-1≤（商品的实际金额-商品的收票总额）≤1 不成立
                        if (realTotalAmount.subtract(invoiceTotalAmount == null ? BigDecimal.ZERO : invoiceTotalAmount).abs().compareTo(BigDecimal.ONE) >= 1 ) {
                            isComplete = false;
                            logger.info("商品的实际金额与商品的收票总额校验不通过， 采购商品：{}", JSON.toJSONString(buyorderGoodsVo));
                        }
                    }
                }

                if (invoiceTotalNum.compareTo(BigDecimal.ZERO) == 0&&!isRebate) {
                    //采购订单为未收票
                    logger.info("采购订单的收票数量为0 未收票 buyorderId:{}", JSON.toJSONString(buyorderId));
                    invoiceStatus = 0;
                } else if (isComplete) {
                    logger.info("采购订单符合条件为全部收票 buyorderId:{}", JSON.toJSONString(buyorderId));
                    //采购订单全部收票
                    invoiceStatus = 2;
                } else {
                    logger.info("采购订单不符合条件为部分收票 buyorderId:{}", JSON.toJSONString(buyorderId));
                    //采购订单未全部收票
                    invoiceStatus = 1;
                }
                //使用了返利更新收票状态
                SettlementBillQueryCmd settlementBillQueryCmd = new SettlementBillQueryCmd(buyorderId, BusinessSourceTypeEnum.buyOrder);
                SettlementBillApiDto settlement = settlementBillApiService.getSettlementByBusiness(settlementBillQueryCmd);
                //使用了返利
                if (ObjectUtil.isNotNull(settlement)) {
                    BigDecimal rebateAmount = settlement.getSettlementBillItemDtoList().stream().map(SettlementBillItemApiDto::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    // rebateAmount还需扣除售后的金蝶
                    BigDecimal afterRebateAmount = capitalBillApiService.getAfterSalesFlByBuyOrderId(buyorderId);
                    BigDecimal invoiceAmount = buyorderMapper.getInvoiceAmount(buyorderId);
                    BigDecimal amount = (null == buyorderMapper.getRealAmount(buyorderId)) ? BigDecimal.ZERO : buyorderMapper.getRealAmount(buyorderId);
                    BigDecimal realAmount = amount.subtract(rebateAmount).add(afterRebateAmount);
                    logger.info("更新采购订单收票状态,使用了返利,实际收票金额:{},实际订单金额(未扣除返利):{},返利金额:{},售后返利金额:{},采购单总金额:{}", invoiceAmount, amount, rebateAmount, afterRebateAmount, buyorderInfo.getTotalAmount());
                    //全部收票（己收票金额-售后退票金额）=（原始金额-售后金额-返利金额）｜｜ 订单全部使用返利
                    //此外为部分收票
                    invoiceStatus = (invoiceAmount.compareTo(realAmount) == 0 || (buyorderInfo.getTotalAmount().compareTo(rebateAmount) == 0)) ? 2 : 1;
                }
            }

            logger.info("更新采购单录票状态信息 buyorderId:{},invoiceStatus:{}", buyorderId, invoiceStatus);
            buyorderMapper.saveInvoiceStatus(buyorderId, invoiceStatus);

            // VDERP-8755 订单流 采购单票货款全部完成时，将状态置为 已完结
            if (invoiceStatus == 2) {
                Buyorder buyorder = buyorderMapper.selectByPrimaryKey(buyorderId);
                if (buyorder.getIsNew() == 1 && buyorder.getStatus() == 1 && buyorder.getPaymentStatus() == 2 && buyorder.getDeliveryStatus() == 2 && buyorder.getArrivalStatus() == 2) {
                    buyorder.setStatus(2);
                    buyorderMapper.updateByPrimaryKeySelective(buyorder);
                    logger.info("订单流票货款全部完成更新采购单状态为已完结--全部收票:{}", buyorderId);
                }
            }

        } catch (Exception e) {
            logger.error("采购订单保存收票状态error", e);
        }
    }

    private void startBuyorderContract(HttpServletRequest request,Integer buyorderId, User user) {
        List<String> userName = userMapper.getUserListByPositionId(44);
        userName.add("Robin.xu");
        try {
            BuyorderVo buyorder = buyorderMapper.getBuyorderVoById(buyorderId);
            Map<String, Object> variableMap = new HashMap<String, Object>();
            variableMap.put("buyorderInfo", buyorder);
            variableMap.put("currentAssinee", user.getUsername());
            variableMap.put("processDefinitionKey", "purchaseContractVerify");
            variableMap.put("businessKey", "purchaseContractVerify_" + buyorderId);
            variableMap.put("relateTableKey", buyorderId);
            variableMap.put("relateTable", "T_BUYORDER");
            variableMap.put("verifyUserList", StrUtil.join(",",userName));
            variableMap.put("verifyUsers", StrUtil.join(",",userName));
            variableMap.put("startUserId",user.getUserId());
            actionProcdefService.createProcessInstance(request, "purchaseContractVerify",
                    "purchaseContractVerify_" + buyorderId, variableMap);
            // 默认申请人通过
            // 根据BusinessKey获取生成的审核实例
            Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
                    "purchaseContractVerify_" +buyorderId);
            if (historicInfo.get("endStatus") != "审核完成") {
                Task taskInfo = (Task) historicInfo.get("taskInfo");
                String taskId = taskInfo.getId();
                Authentication.setAuthenticatedUserId(user.getUsername());
                Map<String, Object> variables = new HashMap<>();
                // 默认审批通过
                actionProcdefService.complementTask(null, taskId, "",
                        user.getUsername(), variables);
            }
        }catch (Exception e){
            logger.error("发起采购订单合同回传审核流异常:", e);
        }

    }

    /**
     * <b>Description:</b><br>
     * 获取出入库记录
     *
     * @param buyorderVo
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2018年6月1日 上午9:14:55
     */
    @Override
    public List<WarehouseGoodsOperateLogVo> getWarehouseGoodsOperateLogVoListPage(BuyorderVo buyorderVo) {
        try {
            final TypeReference<ResultInfo<List<WarehouseGoodsOperateLogVo>>> typeRef =
                    new TypeReference<ResultInfo<List<WarehouseGoodsOperateLogVo>>>() {
                    };
            String urlFor = httpUrl + "order/buyorder/getwarehousegoodsoperateloglist.htm";
            ResultInfo<?> result = (ResultInfo<List<WarehouseGoodsOperateLogVo>>) HttpClientUtils.post(urlFor,
                    buyorderVo, clientId, clientKey, typeRef);
            if (result == null || result.getCode() == -1) {
                return null;
            }
            List<WarehouseGoodsOperateLogVo> bvlist = (List<WarehouseGoodsOperateLogVo>) result.getData();
            if (bvlist != null && bvlist.size() > 0) {
                List<User> userList = userMapper.getAllUserList(buyorderVo.getCompanyId());
                for (WarehouseGoodsOperateLogVo av : bvlist) {
                    for (User us : userList) {
                        if (us.getUserId().equals(av.getCreator())) {
                            av.setOperaterName(us.getUsername());
                            break;
                        }
                    }
                }
            }
            return bvlist;
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
    }

    @Override
    public BuyorderVo getBuyorderGoodsVoListPage(BuyorderVo buyorderVo) {
        try {
            final TypeReference<ResultInfo<List<BuyorderGoodsVo>>> typeRef =
                    new TypeReference<ResultInfo<List<BuyorderGoodsVo>>>() {
                    };
            String urlFor = httpUrl + "order/buyorder/getbuyordergoodsvolist.htm";
            ResultInfo<?> result = (ResultInfo<List<BuyorderGoodsVo>>) HttpClientUtils.post(urlFor, buyorderVo,
                    clientId, clientKey, typeRef);
            if (result == null || result.getCode() == -1) {
                return null;
            }
            List<BuyorderGoodsVo> bglist = (List<BuyorderGoodsVo>) result.getData();
            BuyorderVo bv = null;
            if (bglist != null && bglist.size() > 0) {
                bv = new BuyorderVo();
                List<User> userList = userMapper.getAllUserList(buyorderVo.getCompanyId());
                try {
                    Integer bgSum = 0;
                    BigDecimal bgAmount = new BigDecimal("0.00");
                    BigDecimal bgAmountAct = new BigDecimal("0.00");
                    bv.setAftersaleCanClose(1);// 默认可以
                    boolean flag = false;
                    for (BuyorderGoodsVo bgv : bglist) {
                        // 订单的所有商品都退货后，订单可以关闭
                        // 只要有一个订单产品的数量跟售后退货产品数量不相等，该订单就不能关闭
                        if ((bgv.getAfterReturnNum() != null
                                && bgv.getNum().intValue() > bgv.getAfterReturnNum().intValue())
                                || bgv.getAfterReturnNum() == null) {
                            flag = true;
                            break;
                        }
                    }
                    if (flag) {
                        bv.setAftersaleCanClose(0);
                    }

                    for (BuyorderGoodsVo bgv : bglist) {
                        Integer buySum = 0;
                        if (bgv.getSaleorderGoodsVoList() != null && bgv.getSaleorderGoodsVoList().size() > 0) {
                            List<SaleorderGoodsVo> saleList = bgv.getSaleorderGoodsVoList();
                            for (SaleorderGoodsVo sgv : saleList) {
                                for (User us : userList) {
                                    if (us.getUserId().equals(sgv.getUserId())) {
                                        sgv.setApplicantName(us.getUsername());
                                        break;
                                    }

                                }

                                buySum += sgv.getNum() - (sgv.getBuyNum() == null ? 0 : sgv.getBuyNum());
                                bv.setSaleorderNo(sgv.getSaleorderNo());
                            }
                            bgv.setBuySum(buySum);
                        }
                        if (bgv.getPrice() != null && bgv.getNum() != null) {
                            BigDecimal b1 = new BigDecimal(bgv.getNum());
                            BigDecimal bgvAmount = b1.multiply(bgv.getPrice());
                            bgv.setOneBuyorderGoodsAmount(bgvAmount);
                        }
                        if (bgv.getActualPurchasePrice() != null && bgv.getNum() != null) {
                            BigDecimal b1 = new BigDecimal(bgv.getNum());
                            BigDecimal bgvAmountAct = b1.multiply(bgv.getActualPurchasePrice());
                            bgv.setOneBuyorderGoodsAmountAct(bgvAmountAct);
                        }
                        if (bgv.getNum() != 0) {
                            bgSum += bgv.getNum();
                        } else {
                            // bgv.setNum(buySum);
                            // bgSum += buySum;
                        }
                        bgAmount = bgAmount.add(bgv.getOneBuyorderGoodsAmount());
                        bgAmountAct = bgAmountAct.add(bgv.getOneBuyorderGoodsAmountAct() == null ? new BigDecimal(0) : bgv.getOneBuyorderGoodsAmountAct());
                        bgv.setUserList(
                                rCategoryJUserMapper.getUserByCategory(bgv.getCategoryId(), bgv.getCompanyId()));
                    }
                    bv.setBuyorderAmount(bgAmount);
                    bv.setBuyorderAmountAct(bgAmountAct);
                    bv.setBuyorderSum(bgSum);
                } catch (Exception e) {
                    logger.error(Contant.ERROR_MSG, e);
                }
                bv.setBuyorderGoodsVoList(bglist);
            }
            return bv;
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
    }

    @SuppressWarnings({"unchecked"})
    @Override
    public BuyorderVo getBuyorderVoDetailNew(Buyorder buyorder, User user) {
        // TODO Auto-generated method stub
        // Map<String,Object> map=new HashMap<String,Object>();
        // 定义反序列化 数据格式
        BuyorderVo bv = null;
        final TypeReference<ResultInfo<Map<String, Object>>> TypeRef =
                new TypeReference<ResultInfo<Map<String, Object>>>() {
                };
        try {
            BuyorderVo buyorderVo = new BuyorderVo();
            buyorderVo.setCompanyId(user.getCompanyId());
            buyorderVo.setBuyorderId(buyorder.getBuyorderId());
            buyorderVo.setBuyorderNo(buyorder.getBuyorderNo());
            ResultInfo<BuyorderVo> result = (ResultInfo<BuyorderVo>) HttpClientUtils
                    .post(httpUrl + ErpConst.GET_BUYORDER_DETAIL_NEW, buyorderVo, clientId, clientKey, TypeRef);
            if (result == null || result.getCode() == -1) {
                return null;
            }
            // map= result.getData();
            // page=result.getPage();
            JSONObject json = JSONObject.fromObject(result.getData());
            bv = JsonUtils.readValue(json.toString(), BuyorderVo.class);
            //查询出订单的GE合同编号和销售订单编号,主机和探头并赋值
            editBuyorderGEInfo(bv);
            // BuyorderVo bv = result.getData();
            //
            bv.setBuyDepartmentName(getOrgNameByOrgId(bv.getOrgId()));
            // 创建人名称
            bv.setCreateName(userMapper.getUserNameByUserId(bv.getCreator()));
            // 归属人名称
            bv.setHomePurchasing(userMapper.getUserNameByUserId(bv.getUserId()));
            // 销售订单申请人
            List<BuyorderGoodsVo> list = bv.getBuyorderGoodsVoList();
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        List<User> userList = userMapper.getAllUserList(bv.getCompanyId());
            // Integer bgSum = 0;
            // BigDecimal bgAmount = new BigDecimal("0.00");
            /*
             * List<User> bgvUserList=new ArrayList<User>(); List<Integer> Ids=new ArrayList<Integer>();
             */
            List<String> skuList = new ArrayList<>();
            for (BuyorderGoodsVo bgv : list) {
                skuList.add(bgv.getSku());
                // Integer buySum = 0;
                if (bgv.getSaleorderGoodsVoList() != null && bgv.getSaleorderGoodsVoList().size() > 0) {
                    List<SaleorderGoodsVo> saleList = bgv.getSaleorderGoodsVoList();
                    for (SaleorderGoodsVo sgv : saleList) {
                        // sgv.setApplicantName(userMapper.getUserNameByUserId(sgv.getUserId()));
                        for (User us : userList) {
                            if (us.getUserId().equals(sgv.getUserId())) {
                                sgv.setApplicantName(us.getUsername());
                                break;
                            }

                        }
                        // buySum += sgv.getNum() - (sgv.getBuyNum() == null ? 0
                        // : sgv.getBuyNum());
                        bv.setSaleorderNo(sgv.getSaleorderNo());
                    }
                    bgv.setSaleorderGoodsVoList(saleList);
                    // Ids.add(bgv.getCategoryId());
                    bgv.setUserList(rCategoryJUserMapper.getUserByCategory(bgv.getCategoryId(), user.getCompanyId()));
                    // bgv.setBuySum(buySum);//没用上，查了干嘛？？？？
                }
            }
            //风控
            riskCheckService.setBuyorderGoodsIsRiskInfo(buyorder, list);
            // 取V_CORE_SKU的AVAILABLE_STOCK_NUM、STOCK_NUM字段，不再调用库存服务查询
//            Map<String, WarehouseStock> stockInfo = warehouseStockService.getStockInfo(skuList);
//            for (BuyorderGoodsVo buyorderGoodsVo : list) {
//                WarehouseStock warehouseStock = stockInfo.get(buyorderGoodsVo.getSku());
//                buyorderGoodsVo.setCanUseGoodsStock(warehouseStock.getAvailableStockNum());
//                buyorderGoodsVo.setGoodsStock(warehouseStock.getStockNum());
//            }
            /*
             * bgvUserList=rCategoryJUserMapper.getUserByCategoryIds(Ids, user.getCompanyId()); for (BuyorderGoodsVo bgv
             * : list) { for(user) bgv.setUserList(); }
             */
            bv.setBuyorderGoodsVoList(list);
            // 合同回传
            if (bv != null && bv.getAttachmentList() != null && bv.getAttachmentList().size() > 0) {
                List<Attachment> lists = bv.getAttachmentList();
                for (Attachment attachment : lists) {
                    // attachment.setUsername(userMapper.getUserNameByUserId(attachment.getCreator()));
                    for (User us : userList) {
                        if (us.getUserId().equals(attachment.getCreator())) {
                            attachment.setUsername(us.getUsername());
                            break;
                        }
                    }
                }
                bv.setAttachmentList(lists);
            }
            // 入库记录
            /*
             * if(bv != null && bv.getWarehouseGoodsOperateLogVoList() != null &&
             * bv.getWarehouseGoodsOperateLogVoList().size() > 0){ List<WarehouseGoodsOperateLogVo> lists =
             * bv.getWarehouseGoodsOperateLogVoList(); for (WarehouseGoodsOperateLogVo av : lists) {
             * av.setOperaterName(userMapper.getUserNameByUserId(av.getCreator() )); for (User us : userList) {
             * if(us.getUserId().equals(av.getCreator())){ av.setOperaterName(us.getUsername()); break; } } }
             * bv.setWarehouseGoodsOperateLogVoList(lists); }
             */
            // 物流信息
            /*
             * if(bv != null && bv.getExpressList() !=null && bv.getExpressList().size()>0){ List<Express> lists =
             * bv.getExpressList(); for (Express express : lists) {
             * express.setUpdaterUsername(userMapper.getUserNameByUserId(express .getCreator())); for (User us :
             * userList) { if(us.getUserId().equals(express.getCreator())){
             * express.setUpdaterUsername(us.getUsername()); break; } } } bv.setExpressList(lists); }
             */
            // 发票列表
            if (bv != null && bv.getInvoiceList() != null && bv.getInvoiceList().size() > 0) {
                List<Invoice> lists = bv.getInvoiceList();
                for (Invoice invoice : lists) {
                    for (User us : userList) {
                        if (us.getUserId().equals(invoice.getCreator())) {
                            invoice.setCreatorName(us.getUsername());
                            break;
                        }
                    }
                }
                bv.setInvoiceList(lists);
            }
            // 采购人员名称
            bv.setBuyPerson(userMapper.getUserNameByUserId(bv.getUserId()));

            //判断商品的预警状态
            if (CollectionUtils.isNotEmpty(bv.getBuyorderGoodsVoList())) {
                List<BuyorderGoodsVo> buyorderGoodsVoList = bv.getBuyorderGoodsVoList();
                for (BuyorderGoodsVo buyorderGoodsVo : buyorderGoodsVoList) {
                    EarlyWarningTaskDto earlyWarningTaskDto = earlyWarningTaskMapper.getExpeditionTaskInfoByBuyorderGoodsId(buyorderGoodsVo);
                    buyorderGoodsVo.setEarlyWarningTaskDto(earlyWarningTaskDto);
                }
            }

            //查询直属费用单信息
            BuyorderExpenseDto buyorderExpenseDto = expenseApiService.queryAttributiveExpenseInfoByBuyorderId(buyorder.getBuyorderId());
            logger.info("采购单id{},查询到的直属采购费用单信息{},",buyorderVo.getBuyorderId(),JSON.toJSONString(buyorderExpenseDto));
            if(buyorderExpenseDto != null) {
                bv.setBuyorderExpenseDto(buyorderExpenseDto);
                // 交易记录
                CapitalBill capitalBill = new CapitalBill();
                capitalBill.setOperationType("finance_buy_expense");
                if (bv.getBuyorderExpenseDto() != null) {
                    CapitalBillDetail capitalBillDetail = new CapitalBillDetail();
                    capitalBillDetail.setOrderType(4);// 采购费用订单类型
                    capitalBillDetail.setOrderNo(bv.getBuyorderExpenseDto().getBuyorderExpenseNo());
                    capitalBillDetail.setRelatedId(bv.getBuyorderExpenseDto().getBuyorderExpenseId());
                    capitalBill.setCapitalBillDetail(capitalBillDetail);
                    List<CapitalBill> virtureCapitalBills = capitalBillService.getCapitalBillList(capitalBill);
                    bv.setBuyorderExpenseCapitalBills(virtureCapitalBills);
                }
            }

            setInvoiceRelationOperateLog(bv);
            //判断供应商资质是否过期
            if (null != bv.getTraderId()){
                bv.setCertificateOverdue(traderSupplierService.traderCertificateOverdue(bv.getTraderId()));
            }
            return bv;
        } catch (Exception e) {
            logger.error("查询采购信息", e);
        }
        return bv;
    }

    /**
     * 发票关联入库日志信息
     * @param bv
     */
    private void setInvoiceRelationOperateLog(BuyorderVo bv) {
        try {
            if (CollectionUtils.isEmpty(bv.getInvoiceList())){
                return;
            }
            List<String> invoiceNoList = bv.getInvoiceList().stream().map(Invoice::getInvoiceNo).distinct().collect(Collectors.toList());
            if(CollectionUtils.isEmpty(invoiceNoList)){
                return;
            }
            List<InvoiceRelationWarehouseLogDto> relationOperateLogList = invoiceMapper.getRelationOperateLog(invoiceNoList, bv.getBuyorderId());
            if (CollectionUtils.isEmpty(relationOperateLogList)){
                return;
            }
            Map<String, List<InvoiceRelationWarehouseLogDto>> relationOperateLogMap = relationOperateLogList.stream()
                    .collect(Collectors.groupingBy(InvoiceRelationWarehouseLogDto::getInvoiceNo));
            bv.getInvoiceList().forEach(invoice -> {
                if (relationOperateLogMap.containsKey(invoice.getInvoiceNo())){
                    invoice.setInvoiceRelationWarehouseLogDtoList(relationOperateLogMap.get(invoice.getInvoiceNo()));
                }
            });
        } catch (Exception e) {
            logger.error("setInvoiceRelationOperateLog warn invoiceList:{}", JSON.toJSONString(bv.getInvoiceList()), e);
        }
    }

    @Override
    public BuyorderVo getBuyorderVoDetail(Buyorder buyorder) {
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<BuyorderVo>> TypeRef = new TypeReference<ResultInfo<BuyorderVo>>() {
        };
        try {
            String url = httpUrl + "order/buyorder/getapplybuyorderdetail.htm";
            @SuppressWarnings("unchecked")
            ResultInfo<BuyorderVo> result =
                    (ResultInfo<BuyorderVo>) HttpClientUtils.post(url, buyorder, clientId, clientKey, TypeRef);
            if (result == null || result.getCode() == -1) {
                return null;
            }

            return result.getData();
        } catch (IOException e) {
            throw new RuntimeException();
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public BuyorderVo getSaleBuyNumByAjax(Buyorder buyorder, User user) {
        // TODO Auto-generated method stub
        // Map<String,Object> map=new HashMap<String,Object>();
        // Buyorder bv=new Buyorder();
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<BuyorderVo>> TypeRef = new TypeReference<ResultInfo<BuyorderVo>>() {
        };
        try {
            ResultInfo<BuyorderVo> result = (ResultInfo<BuyorderVo>) HttpClientUtils
                    .post(httpUrl + ErpConst.GET_SALEORDER_NUM_AJAX, buyorder, clientId, clientKey, TypeRef);
            if (result == null || result.getCode() == -1) {
                return null;
            }
            BuyorderVo bv = result.getData();
            List<User> userList = userMapper.getAllUserList(user.getCompanyId());
            // 物流信息
            if (bv != null && bv.getExpressList() != null && bv.getExpressList().size() > 0) {
                List<Express> lists = bv.getExpressList();
                for (Express express : lists) {
                    // express.setUpdaterUsername(userMapper.getUserNameByUserId(express.getCreator()));
                    for (User us : userList) {
                        if (us.getUserId().equals(express.getCreator())) {
                            express.setUpdaterUsername(us.getUsername());
                            break;
                        }
                    }
                }
                bv.setExpressList(lists);
            }
            if (bv != null && bv.getWarehouseGoodsOperateLogVoList() != null
                    && bv.getWarehouseGoodsOperateLogVoList().size() > 0) {
                List<WarehouseGoodsOperateLogVo> lists = bv.getWarehouseGoodsOperateLogVoList();
                for (WarehouseGoodsOperateLogVo av : lists) {
                    // av.setOperaterName(userMapper.getUserNameByUserId(av.getCreator()));
                    for (User us : userList) {
                        if (us.getUserId().equals(av.getCreator())) {
                            av.setOperaterName(us.getUsername());
                            break;
                        }
                    }
                }
                bv.setWarehouseGoodsOperateLogVoList(lists);
            }
            return bv;
        } catch (Exception e) {
            // TODO: handle exception
            throw new RuntimeException();
        }
    }

    /**
     * <b>Description:</b><br>
     * 保存采购修改单生效状态
     *
     * @param buyorderGoodsVo
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2018年3月6日 上午11:01:46
     */
    @Override
    public ResultInfo<?> saveApplyBuyorderModfiyValidStatus(BuyorderModifyApply buyorderModifyApply) {

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            String url = httpUrl + "order/buyorder/saveapplybuyordermodfiystatus.htm";
            ResultInfo<?> result =
                    (ResultInfo<?>) HttpClientUtils.post(url, buyorderModifyApply, clientId, clientKey, TypeRef);
            return result;
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            // throw new RuntimeException();
            return new ResultInfo<>();
        }
    }

    /**
     * <b>Description:</b><br>
     * 获取采购订单申请修改的详情
     *
     * @param buyorder
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年7月19日 上午10:13:30
     */
    @Override
    public BuyorderVo getBuyorderVoApplyUpdateDetail(Buyorder buyorder) {
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<BuyorderVo>> TypeRef = new TypeReference<ResultInfo<BuyorderVo>>() {
        };
        try {
            String url = httpUrl + "order/buyorder/getapplybuyorderdetail.htm";
            @SuppressWarnings("unchecked")
            ResultInfo<BuyorderVo> result =
                    (ResultInfo<BuyorderVo>) HttpClientUtils.post(url, buyorder, clientId, clientKey, TypeRef);
            if (result == null || result.getCode() == -1) {
                return null;
            }
            BuyorderVo bv = result.getData();
            if (bv != null && bv.getBuyorderGoodsVoList() != null && bv.getBuyorderGoodsVoList().size() > 0) {
                List<BuyorderGoodsVo> list = bv.getBuyorderGoodsVoList();
                for (BuyorderGoodsVo bgv : list) {
                    bgv.setUserList(rCategoryJUserMapper.getUserByCategory(bgv.getCategoryId(), bgv.getCompanyId()));
                }
            }
            return result.getData();
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
    }

    /**
     * <b>Description:</b><br>
     * 保存采购订单的申请修改
     *
     * @param buyorderGoodsVo
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2018年3月6日 上午11:01:46
     */
    @Override
    public ResultInfo<?> saveBuyorderApplyUpdate(BuyorderModifyApply buyorderModifyApply,
                                                 BuyorderModifyApplyGoodsVo buyorderModifyApplyGoodsVo) {
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            Map<String, Object> map = new HashMap<>();
            map.put("buyorderModifyApply", buyorderModifyApply);
            map.put("buyorderModifyApplyGoodsVo", buyorderModifyApplyGoodsVo);
            String url = httpUrl + "order/buyorder/savebuyorderapplyupdate.htm";
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, map, clientId, clientKey, TypeRef);
            return result;
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return new ResultInfo<>();
        }
    }

    /**
     * <b>Description:</b><br>
     * 采购订单修改列表分页
     *
     * @param buyorderModifyApplyVo
     * @param page
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2018年7月18日 上午11:13:55
     */
    @Override
    public Map<String, Object> getBuyorderModifyApplyListPage(BuyorderModifyApplyVo buyorderModifyApplyVo, Page page) {
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<BuyorderModifyApplyVo>>> TypeRef =
                new TypeReference<ResultInfo<List<BuyorderModifyApplyVo>>>() {
                };
        try {
            String url = httpUrl + "order/buyorder/getbuyordermodifyapplylistpage.htm";
            ResultInfo<List<BuyorderModifyApplyVo>> result = (ResultInfo<List<BuyorderModifyApplyVo>>) HttpClientUtils
                    .post(url, buyorderModifyApplyVo, clientId, clientKey, TypeRef, page);
            if (result == null || result.getCode() == -1) {
                return null;
            }
            List<BuyorderModifyApplyVo> list = result.getData();
            if (list != null) {
                for (BuyorderModifyApplyVo bmav : list) {
                    // 采购部门
                    if (ObjectUtils.notEmpty(bmav.getOrgId())) {
                        bmav.setOrgName(organizationMapper.selectByPrimaryKey(bmav.getOrgId()).getOrgName());
                    }
                    // 采购人员
                    if (ObjectUtils.notEmpty(bmav.getUserId())) {
                        bmav.setUserName(getUserNameByUserId(bmav.getUserId()));
                    }
                }
            }
            Map<String, Object> map = new HashMap<>();
            map.put("list", result.getData());
            map.put("page", result.getPage());
            return map;
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
    }

    /**
     * <b>Description:</b><br>
     *
     * @param buyorderModifyApply
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2018年7月18日 下午2:34:24
     */
    @Override
    public BuyorderModifyApplyVo getBuyorderModifyApplyVoDetail(BuyorderModifyApply buyorderModifyApply) {
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<BuyorderModifyApplyVo>> TypeRef =
                new TypeReference<ResultInfo<BuyorderModifyApplyVo>>() {
                };
        try {
            String url = httpUrl + "order/buyorder/getbuyordermodifyapplydetail.htm";
            ResultInfo<BuyorderModifyApplyVo> result = (ResultInfo<BuyorderModifyApplyVo>) HttpClientUtils.post(url,
                    buyorderModifyApply, clientId, clientKey, TypeRef);
            if (result == null || result.getCode() == -1) {
                return null;
            }
            BuyorderModifyApplyVo bv = result.getData();
            if (bv != null && bv.getBgvList() != null && bv.getBgvList().size() > 0) {
                List<BuyorderGoodsVo> list = bv.getBgvList();
                for (BuyorderGoodsVo bgv : list) {
                    bgv.setUserList(rCategoryJUserMapper.getUserByCategory(bgv.getCategoryId(), bgv.getCompanyId()));
                    bgv.setRebateAfterPrice(bgv.getPrice().subtract(bgv.getRebatePrice()));
                }
            }
            return bv;
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
    }

    @Override
    public List<BuyorderModifyApply> getBuyorderModifyApplyList(BuyorderModifyApply buyorderModifyApply) {
        List<BuyorderModifyApply> list = null;
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<BuyorderModifyApply>>> TypeRef =
                new TypeReference<ResultInfo<List<BuyorderModifyApply>>>() {
                };
        String url = httpUrl + "order/buyorder/getbuyordermodifyapplylist.htm";
        try {
            ResultInfo<?> result =
                    (ResultInfo<?>) HttpClientUtils.post(url, buyorderModifyApply, clientId, clientKey, TypeRef);
            list = (List<BuyorderModifyApply>) result.getData();
            if (list != null && list.size() > 0) {
                List<Integer> userIds = new ArrayList<>();
                for (BuyorderModifyApply s : list) {
                    if (null != s.getCreator() && s.getCreator() > 0) {
                        userIds.add(s.getCreator());
                    }

                }

                if (userIds.size() > 0) {
                    List<User> userList = userMapper.getUserByUserIds(userIds);
                    for (BuyorderModifyApply s : list) {
                        for (User u : userList) {
                            if (u.getUserId().equals(s.getCreator())) {
                                s.setCreatorName(u.getUsername());
                            }
                        }
                    }
                }
            }
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
        }
        return list;
    }

    @Override
    public BuyorderVo getBuyorderBarcodeVoDetail(BuyorderVo buyorder, User user) {
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            BuyorderVo buyorderVo = new BuyorderVo();
            buyorderVo.setCompanyId(user.getCompanyId());
            buyorderVo.setBuyorderId(buyorder.getBuyorderId());
            buyorderVo.setBuyorderNo(buyorder.getBuyorderNo());
            String url = httpUrl + "order/buyorder/getbuyorderbarcodedetail.htm";
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, buyorderVo, clientId, clientKey, TypeRef);
            if (result == null || result.getCode() == -1) {
                return null;
            }
            JSONObject json = JSONObject.fromObject(result.getData());
            BuyorderVo bv = JsonUtils.readValue(json.toString(), BuyorderVo.class);
            if (bv != null && bv.getBuyorderGoodsVoList() != null && bv.getBuyorderGoodsVoList().size() > 0) {
                bv.setCreateName(getUserNameByUserId(bv.getCreator()));
                bv.setHomePurchasing(getUserNameByUserId(bv.getUserId()));
                bv.setBuyDepartmentName(getOrgNameByOrgId(bv.getOrgId()));
            }

            // 采购人员
            if (ObjectUtils.notEmpty(bv.getUserId())) {
                bv.setBuyPerson(getUserNameByUserId(bv.getUserId()));
            }
            return bv;
        } catch (IOException e) {
            throw new RuntimeException();
        }
    }

//    @Override
//    public List<Integer> getBuyOrderIdsByCurrentOperateUser(Page page, String currentOperateUser) {
//
//        Map<String, Object> map = new HashMap<>();
//        map.put("currentOperateUser", currentOperateUser);
//        map.put("page", page);
//        List<String> processIds = procinstMapper.getBuyOrderIdsByCurrentOperateUser(map);
//        List<String>  buyOrderIdsByCurrentOperateUser = new ArrayList<>();
//        if (processIds.size() > 0){
//            map.put("processIds", processIds);
//            buyOrderIdsByCurrentOperateUser = procinstMapper.getBuyOrderIdsByProcessIds(map);
//        }
//        List<Integer> buyOrderIdsList = new ArrayList<>();
//        for(String str :buyOrderIdsByCurrentOperateUser){
//            String buyOrderId = str.split("_")[1];
//            buyOrderIdsList.add(Integer.parseInt(buyOrderId));
//        }
//        return buyOrderIdsList;
//    }


    @Override
    public List<Integer> getBuyOrderIdsByCurrentOperateUser(Page page, String currentOperateUser) {
        Map<String, Object> map = new HashMap<>();
        map.put("currentOperateUser", currentOperateUser);
        map.put("page", page);
        return verifiesInfoMapper.getBuyorderListUnVerified(map);
    }

    @Override
    public void updateBuyorderDeliveryStatus(Integer buyorderId) {
        try {
            //VDERP-2431
            if (buyorderId == null) {
                return;
            }
            Buyorder buyorder = buyorderMapper.selectByPrimaryKey(buyorderId);
            if (buyorder == null) {
                return;
            }
            Buyorder order = new Buyorder();
            order.setBuyorderId(buyorderId);
            order.setModTime(System.currentTimeMillis());
            List<BuyorderGoodsVo> goodsList = buyorderGoodsMapper.getBuyorderGoodsListByBuyorderId(buyorderId);
            List<ExpressDetail> expressDetailList = expressMapper.getExpressDetailListByBuyorderId(buyorderId);
            setBuyorderDeliveryStatus(buyorder, order, goodsList, expressDetailList);
            if (order.getDeliveryStatus() != null) {
                logger.info("updateBuyorderDeliveryStatus buyorderId:{},deliveryStatus:{}", buyorderId, order.getDeliveryStatus());
                buyorderMapper.updateByPrimaryKeySelective(order);
            }
        } catch (Exception e) {
            logger.error("updateBuyorderArrivalStatus error", e);
        }
    }

    private void setBuyorderDeliveryStatus(Buyorder buyorder, Buyorder order, List<BuyorderGoodsVo> goodsList, List<ExpressDetail> expressDetailList) {
        logger.info("setBuyorderDeliveryStatus buyorderId:{},arrivalStatus:{}", buyorder.getBuyorderId(), buyorder.getArrivalStatus());
        if (buyorder.getArrivalStatus() != null && buyorder.getArrivalStatus().equals(2)) {
            order.setDeliveryStatus(2);
        } else if (CollectionUtils.isEmpty(expressDetailList)) {
            logger.info("setBuyorderDeliveryStatus expressDetailList为空 buyorderId:{},arrivalStatus:{}", buyorder.getBuyorderId(), buyorder.getArrivalStatus());
            order.setDeliveryStatus(buyorder.getArrivalStatus());
        } else if (CollectionUtils.isNotEmpty(expressDetailList)) {
            int arrivalNum = 0;
            for (ExpressDetail expressDetail : expressDetailList) {
                arrivalNum = arrivalNum + expressDetail.getNum();
            }
            int realNum = 0;
            for (BuyorderGoodsVo buyorderGoodsVo : goodsList) {
                realNum = realNum + buyorderGoodsVo.getRealNum();
            }
            logger.info("setBuyorderDeliveryStatus buyorderId:{},arrivalNum:{},realNum:{}", buyorder.getBuyorderId(), arrivalNum, realNum);
            if (arrivalNum == 0) {
                order.setDeliveryStatus(0);
            } else if (realNum > arrivalNum) {
                order.setDeliveryStatus(1);
            } else if (realNum == arrivalNum) {
                order.setDeliveryStatus(2);
            }
        }
    }

    @Override
    public Buyorder getBuyOrderByOrderId(Integer buyorderId) {
        return this.buyorderMapper.selectByPrimaryKey(buyorderId);
    }

    @Override
    public List<BuyorderVo> getBuyorderVoList(Integer goodsId) {
        return buyorderMapper.getBuyorderVoList(goodsId);
    }

    @Override
    public BuyorderVo getBuyorderVoByOrderNo(String buyorderNo) {
        BuyorderVo buyorderVo = buyorderMapper.getBuyorderVoByBuyorderNo(buyorderNo);
        if (buyorderVo != null) {
            buyorderVo.setBuyorderGoodsVoList(buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderId(buyorderVo.getBuyorderId()));
        }
        return buyorderVo;
    }

    @Override
    public void updateBuyorderGoodPrice(Integer buyorderGoodsId,
                                        BigDecimal price,
                                        BigDecimal originalPurchasePrice,
                                        String couponReason) {
        BuyorderGoods buyorder = new BuyorderGoods();
        buyorder.setBuyorderGoodsId(buyorderGoodsId);
        buyorder.setPrice(price);
        buyorder.setCouponReason(couponReason);
        buyorder.setOriginalPurchasePrice(originalPurchasePrice);
        buyorderGoodsMapper.updateByPrimaryKeySelective(buyorder);
    }

    @Override
    public void clearPriceInfo(Integer buyorderGoodId) {
        buyorderGoodsMapper.clearPriceInfo(buyorderGoodId);
    }

    @Override
    public BuyorderVo getBuyorderInfoById(Integer buyorderId) {
        BuyorderVo buyorderVo = buyorderMapper.getBuyorderVoById(buyorderId);
        List<BuyorderGoodsVo> buyorderGoodsVoList = buyorderGoodsMapper.getBuyorderGoodsListByBuyorderId(buyorderId);
        buyorderVo.setBuyorderGoodsVoList(buyorderGoodsVoList);
        return buyorderVo;
    }

    /**
     * 采购订单审核通过，则订单中的虚拟商品自动全部收货
     *
     * @param buyOrderId
     */
    @Override
    public void handleBuyOrderVirtualGoodsArrival(Integer buyOrderId) {
        try {

            BuyorderVo buyOrderVo = this.getBuyorderInfoById(buyOrderId);
            if (null == buyOrderVo || buyOrderVo.getVerifyStatus() != 1) {
                // 审核未通过
                return;
            }

            List<BuyorderGoodsVo> buyOrderGoodsVoList = buyOrderVo.getBuyorderGoodsVoList();
            if (CollectionUtils.isNotEmpty(buyOrderGoodsVoList)) {
                long logTime = System.currentTimeMillis();
                int buyOrderVirtualGoodsCount = 0;
                for (BuyorderGoodsVo buyorderGoodsVo : buyOrderGoodsVoList) {
                    // 虚拟商品
                    if (buyorderGoodsVo.getIsVirtureSku() != null && buyorderGoodsVo.getIsVirtureSku() == 1) {
                        BuyorderGoods updateBuyOrderGood = new BuyorderGoods();
                        updateBuyOrderGood.setBuyorderGoodsId(buyorderGoodsVo.getBuyorderGoodsId());
                        updateBuyOrderGood.setArrivalStatus(2); // 全部收货
                        updateBuyOrderGood.setArrivalNum(buyorderGoodsVo.getNum());
                        updateBuyOrderGood.setArrivalTime(logTime);
                        buyorderGoodsMapper.updateByPrimaryKeySelective(updateBuyOrderGood);
                        buyOrderVirtualGoodsCount++;
                        logger.info("采购单中虚拟商品自动收货，buyorderGoodsId: {}", buyorderGoodsVo.getBuyorderGoodsId());
                    }
                }
                if (buyOrderVirtualGoodsCount != 0 && buyOrderVirtualGoodsCount == buyOrderGoodsVoList.size()) {
                    Buyorder buyorder = new Buyorder();
                    buyorder.setBuyorderId(buyOrderId);
                    buyorder.setArrivalStatus(2);
                    buyorder.setArrivalTime(logTime);
                    buyorder.setDeliveryStatus(2);
                    buyorder.setDeliveryTime(logTime);
                    buyorderMapper.updateByPrimaryKeySelective(buyorder);
                    logger.info("采购单中的商品全部为虚拟商品自动设置采购单全部收发货，采购单id：{}", buyOrderId);
                }
            }

        } catch (Exception e) {
            log.error("【handleBuyOrderVirtualGoodsArrival】处理异常",e);
            logger.error("采购订单中的虚拟商品自动全部收货发生异常", e);
        }
    }

    @Override
    public void clearBuyOrderAuditInfo(Integer buyOrderId) {
        buyorderMapper.clearBuyOrderAuditInfo(buyOrderId);
    }

    @Override
    public BuyorderVo getBuyOrderVoForDeliveryDirect(Integer buyOrderId) {
        BuyorderVo buyorderVo = buyorderMapper.getBuyorderVoById(buyOrderId);
        if (buyorderVo != null && buyorderVo.getDeliveryDirect() == 1 && buyorderVo.getOrderType() == 0) {
            // 直发采购单
            List<BuyorderGoodsVo> buyOrderGoodsVoList = buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderId(buyorderVo.getBuyorderId());
            if (buyOrderGoodsVoList.size() > 0) {
                List<Integer> bogIds = buyOrderGoodsVoList.stream().map(BuyorderGoodsVo::getBuyorderGoodsId).collect(Collectors.toList());
                // 根据采购商品id 获取销售订单id
                List<Integer> saleOrderIds = buyorderMapper.getSaleOrderIdByBuyOrderGoodIds(bogIds);
                buyorderVo.setSaleorderId(saleOrderIds.get(0));
            }
            buyorderVo.setBuyorderGoodsVoList(buyOrderGoodsVoList);
            return buyorderVo;
        }
        return null;
    }

    @Override
    public List<User> getOrderAssitantInfoByIdSelective(Integer orderAssitantUserId) {
        return userMapper.getUserByUserIdSelective(orderAssitantUserId);
    }

    @Override
    public List<User> getProductUserByOrderAssistantId(Integer orderAssitantUserId, Integer type) {
        return orderAssistantJProductPositionMapper.getProductUserByOrderAssistantId(orderAssitantUserId, type);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultInfo bindOrderAssitantToProductUser(OrderAssistantRelationDto orderAssistantRelationDto, HttpSession session) {
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        long now = DateUtil.gainNowDate();
        if (null == user) {
            user = new User();
            user.setUserId(2);
        }
        orderAssistantRelationDto.setCreator(user.getUserId());
        orderAssistantRelationDto.setAddTime(DateUtil.gainNowDate());
        orderAssistantRelationDto.setIsDelete(0);
        //先判断是否已存在相关绑定信息
        Integer num = orderAssistantRelationMapper.getBingdedInfoByUser(orderAssistantRelationDto);
        if (num == null || num > 0) {
            return new ResultInfo(-1, "该绑定关系已经存在");
        }
        orderAssistantRelationMapper.insertSelective(orderAssistantRelationDto);
        return new ResultInfo(0, "绑定成功");
    }

    @Override
    public ResultInfo unbindOrderAssRelation(OrderAssistantRelationDto orderAssistantRelationDto) {
        orderAssistantRelationMapper.unbindOrderAssRelation(orderAssistantRelationDto);
        return null;
    }

    @Override
    public Map<String, Object> queryVBBuyorderList(SearchVBByGoodVo searchVBByGoodVo, Page page) {
        Map<String, Object> map = new HashMap<>();
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<BuyorderVBVo>>> TypeRef = new TypeReference<ResultInfo<List<BuyorderVBVo>>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.GET_ABLE_VB_PAGE,
                    searchVBByGoodVo, clientId, clientKey, TypeRef, page);
            if (result != null && result.getCode() == 0) {
                map.put("list", (List<BuyorderVBVo>) result.getData());
                map.put("page", result.getPage());
            }
            return map;
        } catch (IOException e) {
            logger.error("queryVBBuyorderList->error:", e);
            throw new RuntimeException();
        }
    }

    @Override
    public ResultInfo<?> buyOrderBindVB(BuyOrderBindVBVO buyOrderBindVBVO) {
        List<String> saleorderGoodInfosList = Arrays.asList(buyOrderBindVBVO.getSaleorderGoodInfos().split(","));
        for (String saleorderGoodInfo : saleorderGoodInfosList) {
            if (!buyorderMapper.bindVB(Integer.valueOf(saleorderGoodInfo.split("NUM")[0]), Integer.valueOf(saleorderGoodInfo.split("NUM")[1]), buyOrderBindVBVO.getBuyorderGoodsId())) {
                return new ResultInfo(-1, "保存数据时失败！");
            } else {
                //通知销售单归属销售
                doSendBindVBMessage(buyOrderBindVBVO.getBuyorderGoodsId(), saleorderGoodInfo.split("NUM")[0]);
            }
        }

        return new ResultInfo(0, "保存成功");
    }

    @Override
    public ResultInfo<?> changeComponent(String saleorderGoodsIds, Integer componentId, Integer userId, String reson) {
        List<String> saleorderGoodIdList = Arrays.asList(saleorderGoodsIds.split(","));
        for (String saleorderGoodId : saleorderGoodIdList) {
            SaleorderGoods saleorderGoodsUpdateTemp = new SaleorderGoods();
            saleorderGoodsUpdateTemp.setSaleorderGoodsId(Integer.valueOf(saleorderGoodId));
            saleorderGoodsUpdateTemp.setComponentId(componentId);
            saleorderGoodsUpdateTemp.setBuyDockUserId(userId);
            saleorderGoodsUpdateTemp.setBuyProcessModTime(new Date().getTime());
            saleorderGoodsUpdateTemp.setBuyProcessModReson(reson);
            if (saleorderGoodsMapper.updateByPrimaryKeySelective(saleorderGoodsUpdateTemp) <= 0) {
                return new ResultInfo(-1, "保存数据时失败！");
            }
            //更新预警等级从立即采购移动至暂缓采购或者无需采购时
            if (Saleorder.COMPONENTID_BUY_LATER.equals(componentId) || Saleorder.COMPONENTID_NOT_BUY.equals(componentId)) {
                SaleorderGoodsVo saleorderGoodsVo = saleorderGoodsMapper.getSaleorderGoodsVoInfoById(Integer.valueOf(saleorderGoodId));
                if (saleorderGoodsVo.getIsWarn() != null && saleorderGoodsVo.getIsWarn() == 1) {
                    SaleorderGoodsVo newSaleorderGoodsVo = new SaleorderGoodsVo();
                    newSaleorderGoodsVo.setSaleorderGoodsId(Integer.valueOf(saleorderGoodId));
                    newSaleorderGoodsVo.setWarnLevel(null);
                    saleorderGoodsMapper.updateWarnStatusSelective(newSaleorderGoodsVo);
                }
            }
            if (Saleorder.COMPONENTID_BUY_NOW.equals(componentId)) {
                SaleorderGoodsVo saleorderGoodsVo = saleorderGoodsMapper.getSaleorderGoodsVoInfoById(Integer.valueOf(saleorderGoodId));
                if (saleorderGoodsVo.getAging() != null && saleorderGoodsVo.getAging() == 2) {
                    SaleorderGoodsVo newSaleorderGoodsVo = new SaleorderGoodsVo();
                    newSaleorderGoodsVo.setSaleorderGoodsId(Integer.valueOf(saleorderGoodId));
                    newSaleorderGoodsVo.setWarnLevel(2);
                    saleorderGoodsMapper.updateWarnStatusSelective(newSaleorderGoodsVo);
                } else {
                    Integer saleorderId = saleorderGoodsVo.getSaleorderId();
                    Saleorder saleorderBySaleorderId = saleorderMapper.getSaleorderBySaleorderId(saleorderId);
                    SaleorderGoodsVo newSaleorderGoodsVo = new SaleorderGoodsVo();
                    newSaleorderGoodsVo.setSaleorderGoodsId(Integer.valueOf(saleorderGoodId));
                    newSaleorderGoodsVo.setAgingTime(saleorderBySaleorderId.getSatisfyDeliveryTime());
                    long nowTime = DateUtil.gainNowDate();
                    long difference = (nowTime - saleorderBySaleorderId.getSatisfyDeliveryTime()) / (1000 * 60 * 60);
                    if (difference < 8) {
                        newSaleorderGoodsVo.setAging(0);
                        newSaleorderGoodsVo.setWarnLevel(null);
                    } else if (difference < 12) {
                        newSaleorderGoodsVo.setAging(1);
                        newSaleorderGoodsVo.setWarnLevel(3);
                    } else {
                        newSaleorderGoodsVo.setAging(2);
                        newSaleorderGoodsVo.setWarnLevel(2);
                        if (difference >= 24) {
                            newSaleorderGoodsVo.setWarnLevel(1);
                        }
                    }
                    saleorderGoodsMapper.updateWarnStatusSelective(newSaleorderGoodsVo);
                }
            }
        }
        //更新虚拟商品无需采购收发货状态
        if(Saleorder.COMPONENTID_NOT_BUY.equals(componentId)){
            List<Integer> list = saleorderGoodIdList.stream().map(Integer::valueOf).collect(Collectors.toList());
            List<SaleOrderGoodsDetailDto> expenseDetail = saleOrderGoodsApiService.getExpenseDetail(list);
            long updateTime = System.currentTimeMillis();
            if (expenseDetail.size()>0){
                expenseDetail.forEach(o -> {
                    o.setArrivalStatus(2); //全部收货
                    o.setDeliveryStatus(2); // 全部发货
                    o.setDeliveryNum(o.getNum());
                    o.setDeliveryTime(updateTime);
                    o.setArrivalTime(updateTime);
                });
                logger.info("更新无需采购虚拟商品的收发货状态：{}",expenseDetail);
                saleOrderGoodsApiService.updateExpenseGoodsDeliveryAndArrivalStatus(expenseDetail);
                Set<Integer> saleOrderSet = expenseDetail.stream().map(SaleOrderGoodsDetailDto::getSaleorderId).collect(Collectors.toSet());
                if (saleOrderSet.size()>0){
                    logger.info("更新无需采购虚拟商品对应的销售单的收发货状态：{},关联销售单：{}",list,saleOrderSet);
                    for (Integer saleOrderId : saleOrderSet) {
                        saleOrderApiService.checkSaleorderDeliveryAndArrivalStatus(saleOrderId);
                    }
                }
            }
        }
        return new ResultInfo(0, "保存成功");
    }

    private void doSendBindVBMessage(Integer buyorderGoodsId, String saleorderGoodsId) {
        Buyorder buyOrder = buyorderMapper.getByuorderByBuyorderGoodsId(buyorderGoodsId);
        Saleorder saleOrder = saleorderMapper.getSaleorderBySaleorderGoodsId(Integer.valueOf(saleorderGoodsId));
        //获取销售单的归属销售
        List<Integer> userId = getOwnerSaleManIdBySaleOrder(saleOrder);
        //组装消息参数
        Map<String, String> params = new HashMap<>();
        params.put("saleOrderNo", saleOrder.getSaleorderNo());
        params.put("buyOrderNo", buyOrder.getBuyorderNo());
        //根据订单类型组装订单详情页URL
        String url = getSaleOrderDetailUrl(saleOrder);
        //销售单绑定在途VB单通知归属销售消息模板ID=165
        if (!MessageUtil.sendMessage(165, userId, params, url)) {
            logger.info("绑定VB单成功但消息发送失败");
        }
    }

    private List<Integer> getOwnerSaleManIdBySaleOrder(Saleorder saleOrder) {
        List<Integer> userIds = new ArrayList();
        userIds.add(saleorderMapper.getOwnerNameBySaleorderId(saleOrder.getSaleorderId()));
        return userIds;
    }

    private String getSaleOrderDetailUrl(Saleorder saleOrder) {
        if (saleOrder.getOrderType() == 0 || saleOrder.getOrderType() == 1 || saleOrder.getOrderType() == 9) {//销售订单详情
            return ErpConst.SALEORDER_DETIAL_URL + saleOrder.getSaleorderId();
        } else if (saleOrder.getOrderType() == 5) {//耗材订单详情
            return ErpConst.HC_SALEORDER_DETIAL_URL + saleOrder.getSaleorderId();
        } else if (saleOrder.getOrderType() == 7 || saleOrder.getOrderType() == 8) {//集采订单详情
            return ErpConst.JC_SALEORDER_DETIAL_URL + saleOrder.getSaleorderId();
        } else {//都找不到则默认销售订单详情
            return ErpConst.SALEORDER_DETIAL_URL + saleOrder.getSaleorderId();
        }
    }


    @Override
    public List<EarlyWarningTaskDto> getRemindGoodsTask() {
        List<BuyorderGoods> preRemindGoodsOrderList = buyorderMapper.getPreRemindGoodsOrderList();

        List<EarlyWarningTaskDto> earlyWarningTaskDtoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(preRemindGoodsOrderList)) {
            return earlyWarningTaskDtoList;
        }
        for (BuyorderGoods buyorderGoods : preRemindGoodsOrderList) {
            EarlyWarningTaskDto earlyWarningTaskDto = new EarlyWarningTaskDto();
            earlyWarningTaskDto.setEarlyWarningType(FlashConstant.EXPEDITING_TYPE);
            earlyWarningTaskDto.setRelateBusinessId(buyorderGoods.getBuyorderGoodsId());
            earlyWarningTaskDto.setBusinessExtra1(buyorderGoods.getBuyorderNo());
            earlyWarningTaskDto.setIsDeleted(0);
            earlyWarningTaskDto.setAddTime(DateUtil.getNowDate(DateUtil.TIME_FORMAT));
            earlyWarningTaskDto.setCreator(FlashConstant.NJADMIN_ID);
            earlyWarningTaskDto.setBuyorderId(buyorderGoods.getBuyorderId());
            earlyWarningTaskDto.setTaskDealer(String.valueOf(buyorderGoods.getCreator()));
            earlyWarningTaskDto.setFollowUpNum(0);
            earlyWarningTaskDto.setDeliveryTime(buyorderGoods.getSendGoodsTime());
            try {
                earlyWarningTaskDto.setGoodsDay(Integer.valueOf(buyorderGoods.getDeliveryCycle()));
            } catch (NumberFormatException e) {
                logger.info("采购商品详情{}货期转换失败，当前存储的货期不能转换为数字", buyorderGoods.getBuyorderGoodsId());
                continue;
            }
            ExpeditingCountingDto expeditingCountNumDto = caculateCountNum(buyorderGoods);
            Integer status = getBuyorderGoodsStatus(expeditingCountNumDto);
            if (FlashConstant.TASK_STATUS_INIT.equals(status) || FlashConstant.TASK_COMPLETED.equals(status)
                    || FlashConstant.TASK_FAILED.equals(status)) {
                continue;
            }
            if (FlashConstant.TASK_STATUS_ADVENT.equals(status)) {
//                任务状态:0:初始化 1.临期 2.逾期 3.逾期超过12小时 4.创建超过48小时
                earlyWarningTaskDto.setTaskStatus(FlashConstant.TASK_STATUS_ADVENT);
                earlyWarningTaskDto.setSendImpendingMessage(1);
            }
            if (FlashConstant.TASK_STATUS_OVERDUE.equals(status)) {
                earlyWarningTaskDto.setTaskStatus(FlashConstant.TASK_STATUS_OVERDUE);
                earlyWarningTaskDto.setSendOverdueMessage(1);
            }
            if (FlashConstant.TASK_STATUS_OVERDUE_HALFDAY.equals(status)) {
                earlyWarningTaskDto.setTaskStatus(FlashConstant.TASK_STATUS_OVERDUE_HALFDAY);
                earlyWarningTaskDto.setSendOver12hoursMessage(1);
            }
            earlyWarningTaskDtoList.add(earlyWarningTaskDto);
        }
        return earlyWarningTaskDtoList;

    }

    @Override
    public void updateGenerateInfo(EarlyWarningTaskDto earlyWarningTaskDto) {
        buyorderGoodsMapper.updateBuyorderGoodsInfo(earlyWarningTaskDto);
        BuyorderVo buyorderVoById = buyorderMapper.getBuyorderVoById(earlyWarningTaskDto.getBuyorderId());
        if (FlashConstant.TASK_STATUS_ADVENT.equals(earlyWarningTaskDto.getTaskStatus())) {
            if (buyorderVoById.getExpeditingStatus() == null || buyorderVoById.getExpeditingStatus() == 0) {
                buyorderVoById.setExpeditingStatus(1);
            }
        } else {
            if (buyorderVoById.getExpeditingStatus() == null || buyorderVoById.getExpeditingStatus() == 0 || buyorderVoById.getExpeditingStatus() == 1) {
                buyorderVoById.setExpeditingStatus(2);
            }
        }
        if (buyorderVoById.getExpeditingFollowStatus() == null || buyorderVoById.getExpeditingFollowStatus() == 0) {
            buyorderVoById.setExpeditingFollowStatus(1);
        } else if (buyorderVoById.getExpeditingFollowStatus() == 3) {
            buyorderVoById.setExpeditingFollowStatus(2);
        }
        buyorderMapper.updateExpeditingStatusById(buyorderVoById);

    }

    @Override
    public boolean dealExpeditingByCloseOrder(Integer buyOrderId) {
        if (buyOrderId == null) {
            return false;
        }

        //1.关闭订单预警标识
        BuyorderVo order = new BuyorderVo();
        order.setBuyorderId(buyOrderId);
        order.setExpeditingStatus(0);
        logger.info("关闭采购订单预警更新订单层预警信息 buyOrderId:{}", buyOrderId);
        buyorderMapper.updateExpeditingStatusById(order);

        //2.关闭订单商品预警标识
        List<Integer> orderGoodsIds = buyorderGoodsMapper.getBuyorderGoodsIdsByBuyorderId(buyOrderId);
        if (CollectionUtils.isEmpty(orderGoodsIds)) {
            return false;
        }
        logger.info("关闭采购订单预警更新订单商品层预警信息 orderGoodsIds:{}", JSON.toJSONString(orderGoodsIds));
        earlyWarningTaskMapper.batchCloseOrderGoodsTask(orderGoodsIds);
        return true;
    }

    @Override
    public boolean dealUrgeTicketBuyCloseOrder(String orderNo) {
        logger.info("采购订单关闭处理催票信息 orderNo:{}", orderNo);
        if (StringUtils.isBlank(orderNo)){
            return false;
        }
        earlyWarningTaskMapper.batchCloseUrgeTicketByOrderNo(orderNo);
        return true;
    }

    @Override
    public List<Integer> getBuyOrderIdsByStatus(Integer status) {
        if (status == null) {
            return null;
        }
        return buyorderMapper.getBuyOrderIdsByStatus(status);
    }

    @Override
    public void dealDirectOrderPeriodManagement(Express reExpress, Integer buyOrderId,Integer operateType) {
        logger.info("采购添加快递处理直发订单账期编码监管begin reExpress:{},buyOrderId:{}", JSON.toJSONString(reExpress), buyOrderId);
        if (reExpress == null || buyOrderId == null) {
            return;
        }
        BuyorderVo buyorderVo = getBuyOrderVoForDeliveryDirect(buyOrderId);
        if (buyorderVo == null) {
            logger.info("采购添加快递处理直发订单账期编码监管非直发订单 buyOrderId:{}", buyOrderId);
            return;
        }
        Integer saleOrderId = buyorderVo.getSaleorderId();
        if (saleOrderId == null) {
            logger.warn("采购添加快递处理直发订单账期编码监管销售订单异常告警 buyOrderId:{}", buyOrderId);
            return;
        }

        BigDecimal goodsAmount = expressMapper.getAllGoodsAmountByExpressId(reExpress.getExpressId());
        if (goodsAmount == null || goodsAmount.compareTo(BigDecimal.ZERO) < 1) {
            logger.warn("该订单为虚拟快递单或金额不存在 expressId:{}", reExpress.getExpressId());
            return;
        }
        if (ErpConst.OperateType.INSERT_OPERATE.equals(operateType)) {

            logger.info("采购添加快递处理直发订单账期编码监管begin expressId : {} operateType :{}",reExpress.getExpressId(),operateType);

            setPeriodUseNodeRecordPo(reExpress, saleOrderId, goodsAmount, CustomerBillPeriodOverdueManageDetailTypeEnum.ORDER_EXPRESS);
        }

        if (ErpConst.OperateType.DELETE_OPERATE.equals(operateType)) {

            logger.info("采购添加快递处理直发订单账期编码监管begin expressId : {} operateType :{}",reExpress.getExpressId(),operateType);

            setPeriodUseNodeRecordPo(reExpress, saleOrderId, goodsAmount, CustomerBillPeriodOverdueManageDetailTypeEnum.ORDER_EXPRESS_INVALID);
        }

        if (ErpConst.OperateType.UPDATE_OPERATE_FRONT.equals(operateType)) {

            logger.info("采购添加快递处理直发订单账期编码监管begin expressId : {} operateType :{}",reExpress.getExpressId(),operateType);

            setPeriodUseNodeRecordPo(reExpress, saleOrderId, goodsAmount, CustomerBillPeriodOverdueManageDetailTypeEnum.ORDER_EXPRESS_INVALID);
        }

        if (ErpConst.OperateType.UPDATE_OPERATE_END.equals(operateType)) {

            logger.info("采购添加快递处理直发订单账期编码监管begin expressId : {} operateType :{}",reExpress.getExpressId(),operateType);

            setPeriodUseNodeRecordPo(reExpress, saleOrderId, goodsAmount, CustomerBillPeriodOverdueManageDetailTypeEnum.ORDER_EXPRESS);
        }
    }

    private void setPeriodUseNodeRecordPo(Express reExpress, Integer saleOrderId, BigDecimal goodsAmount, CustomerBillPeriodOverdueManageDetailTypeEnum orderExpress) {

        PeriodUseNodeRecordPo periodUseNodeRecordPo = new PeriodUseNodeRecordPo();
        periodUseNodeRecordPo.setOrderType(orderExpress.getCode());
        periodUseNodeRecordPo.setOrderId(saleOrderId.longValue());
        periodUseNodeRecordPo.setRelatedId(reExpress.getExpressId().longValue());
        periodUseNodeRecordPo.setAddTime(System.currentTimeMillis());
        periodUseNodeRecordPo.setCreator(ErpConst.ONE);
        periodUseNodeRecordPo.setAmount(goodsAmount);
        periodUseNodeRecordMapper.insertRecord(periodUseNodeRecordPo);
    }

    public Integer getBuyorderGoodsStatus(ExpeditingCountingDto expeditingCountingDto) {
        if ((expeditingCountingDto.getBuyNum() - expeditingCountingDto.getAftersaleNum() - expeditingCountingDto.getArriveNum()) <= 0) {
            return FlashConstant.TASK_COMPLETED;
        }
        String deliveryCycle = expeditingCountingDto.getDeliveryCycle();

        try {
            Integer goodsDay = Integer.valueOf(deliveryCycle);
            Long firstSendGoodsTime = expeditingCountingDto.getSendGoodsTime();
            if (firstSendGoodsTime == null) {
                logger.info("采购商品详情{}预计发货时间为空", expeditingCountingDto.getBuyorderGoodsId());
                return FlashConstant.TASK_FAILED;
            }

            Long now = DateUtil.gainNowDate();
            Long adventTime = 0L;//催货参考时间
            //VDERP-7217采购单催货时间计算调整start
            long validTime1 = DateUtil.getDateBefore(DateUtil.getDateFormatByTime(expeditingCountingDto.getValidTime()), 1);
            String firstSendTimeStr = DateUtil.convertString(firstSendGoodsTime, "yyyy-MM-dd");//预计发货时间
            String validTimeStr = DateUtil.convertString(expeditingCountingDto.getValidTime(), "yyyy-MM-dd");//采购单生效时间
            int interval = new Long(DateUtil.getDistanceTimeDays(firstSendTimeStr + " 00:00:00",
                    DateUtil.convertString(validTime1, "yyyy-MM-dd") + " 00:00:00")).intValue();
            //计算临界值
            int days = interval / 2;
            long goodsTime = DateUtil.getDateAfter(DateUtil.StringToDate(validTimeStr, "yyyy-MM-dd"), days);
            if (interval % 2 != 0) {
                //取余运算判断奇偶
                //奇数---12点
                adventTime = DateUtil.convertLong(DateUtil.convertString(goodsTime, "yyyy-MM-dd") + " 12:00:00", "yyyy-MM-dd HH:mm:ss");
            } else {
                //偶数---0点
                adventTime = DateUtil.convertLong(DateUtil.convertString(goodsTime, "yyyy-MM-dd") + " 00:00:00", "yyyy-MM-dd HH:mm:ss");
            }
            //VDERP-7217采购单催货时间计算调整start
//            if (goodsDay%2 == 1) {
//                adventTime = DateUtil.getDateBefore(new Date(firstSendGoodsTime), goodsDay / 2 + 1);
//                adventTime = adventTime + 12 * 60 * 60 * 1000;
//            } else {
//                adventTime = DateUtil.getDateBefore(new Date(firstSendGoodsTime), goodsDay / 2);
//            }
            // VDERP-8072 逾期时间跳过周六周日
            Long overdueTime = firstSendGoodsTime + 24 * 60 * 60 * 1000;
            Long overdueHalfDayTime = DateUtil.getDateAfterExpectWeekends(overdueTime, 0.5);

            if ((now.compareTo(adventTime) < 0)) {
                //正常
                return FlashConstant.TASK_STATUS_INIT;
            } else if ((now.compareTo(overdueTime) <= 0)) {
                //临期
                return FlashConstant.TASK_STATUS_ADVENT;
            } else if ((now.compareTo(overdueHalfDayTime) < 0)) {
                //逾期 不过12小时
                return FlashConstant.TASK_STATUS_OVERDUE;
            } else {
                //逾期超过12小时
                return FlashConstant.TASK_STATUS_OVERDUE_HALFDAY;
            }
        } catch (Exception e) {
            logger.info("采购商品详情{}货期转换失败，当前存储的货期不能转换为数字", expeditingCountingDto.getBuyorderGoodsId());
            return FlashConstant.TASK_FAILED;
        }

    }


    public ExpeditingCountingDto caculateCountNum(BuyorderGoods buyorderGoods) {
        //售后
        ExpeditingCountingDto expeditingCountingDto = buyorderGoodsMapper.getBuyorderGoodsStatus(buyorderGoods);
        if (OrderConstant.DELEVIRY_STATUS_1.equals(buyorderGoods.getDeliveryDirect())) {
            //直发
            List<ExpressDetail> expressDetailList = expressMapper.getExpressDetailsListByBuyorderGoodsId(buyorderGoods);
            if (CollectionUtils.isNotEmpty(expressDetailList)) {
                Integer arrivalNum = 0;
                for (ExpressDetail expressDetail : expressDetailList) {
                    if (expressDetail.getNum() != null) {
                        arrivalNum += expressDetail.getNum();
                    }
                }
                expeditingCountingDto.setArriveNum(arrivalNum);
            }
        }
        return expeditingCountingDto;
    }


    @Override
    public void updateBuyorderGoodsSnapshotInfo(int snapShotOrderId) {
        List<BuyorderGoodsVo> buyorderGoodsVoList = buyorderGoodsMapper.getBuyorderGoodsListByBuyorderId(snapShotOrderId);
        if (CollectionUtils.isNotEmpty(buyorderGoodsVoList)) {
            buyorderGoodsVoList.forEach(buyorderGoodsVo -> {
                buyorderGoodsMapper.updateBuyorderGoodsSnapshotInfo(buyorderGoodsVo);
            });
        }
        logger.info("订单生效后更新采购订单快照信息成功！订单ID: " + snapShotOrderId + "时间： " + DateUtil.getNowDate(DateUtil.TIME_FORMAT));
    }

    @Override
    public void updateVerifyStatus(Integer buyorderId, Integer status) {
        buyorderMapper.saveVerifyStatus(buyorderId, status);
    }

    @Override
    public BuyorderVo getNewBuyorderGoodsVoList(Buyorder buyorder) {
        try {
            BuyorderVo sv = getNewBuyorderGoodsVo(buyorder);
            List<TraderAddress> list = sv.getTavList();
            if (list != null && list.size() > 0) {
                List<TraderAddressVo> tavList = new ArrayList<>();
                TraderAddressVo tav = null;
                for (TraderAddress ta : list) {
                    tav = new TraderAddressVo();
                    tav.setTraderAddress(ta);
                    tav.setArea(getAddressByAreaId(ta.getAreaId()));
                    tavList.add(tav);
                }
                sv.setTraderAddressVoList(tavList);
            }
            return sv;
        } catch (Exception e) {
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
    }

    public BuyorderVo getNewBuyorderGoodsVo(Buyorder buyorder) {
        BuyorderVo sv = buyorderMapper.getBuyorderVoById(buyorder.getBuyorderId());
        List<BuyorderGoodsVo> sgvList = buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderId(buyorder.getBuyorderId());
        if(CollectionUtils.isNotEmpty(sgvList)) {
            for (BuyorderGoodsVo sgv : sgvList) {
                // 查询当前的采购单入库商品在库数量--改为查询入库数量VDERP-11291
                WarehouseGoodsOperateLog record = new WarehouseGoodsOperateLog();
                record.setOperateType(1);
                record.setRelatedId(sgv.getBuyorderGoodsId());
                record.setCompanyId(1);
                Integer deliveryNum = Math.abs(warehouseGoodsOperateLogMapper.getInStockByRelateIdOptType(record));
                Integer buyNum = sgv.getNum() - sgv.getAfterReturnNum();
                //如果是直发订单
                if (sv.getDeliveryDirect() == 1) {
                    sgv.setAfterSaleUpLimitNum(buyNum);
                } else {
                    //采购单数量 - 已完成售后数量
                    sgv.setAfterSaleUpLimitNum(buyNum);
                    if ("hh".equals(buyorder.getFlag())) {
                        sgv.setAfterSaleUpLimitNum(buyNum.compareTo(deliveryNum) > 0 ? deliveryNum : buyNum);
                    }
                }

                sgv.setReceiveNum(sgv.getArrivalNum());
                sgv.setDeliveryNum(buyorderGoodsMapper.getDeliveryNum(sgv.getBuyorderGoodsId()));
                BigDecimal haveInvoiceNums = buyorderMapper.getHaveInvoiceNums(sgv.getBuyorderGoodsId());
                sgv.setInvoiceNum(haveInvoiceNums == null ? 0 : haveInvoiceNums.intValue());
            }
            sgvList.removeIf(e->e.getAfterSaleUpLimitNum() == 0);
        }
        sv.setBgvList(sgvList);

        TraderContact traderContact = new TraderContact();
        traderContact.setTraderId(sv.getTraderId());
        traderContact.setTraderType(2);
        traderContact.setIsEnable(1);
        List<TraderContact> tcList = traderContactMapper.getTraderContact(traderContact);
        sv.setTcList(tcList);
        TraderAddress traderAddress = new TraderAddress();
        traderAddress.setTraderId(sv.getTraderId());
        traderAddress.setTraderType(2);
        traderAddress.setIsEnable(1);
        List<TraderAddress> list = traderAddressMapper.getAllTraderAddressList(traderAddress);
        sv.setTavList(list);
        return sv;
    }

    @Override
    public void sendMsgByCompareNewOldDeliveryDirect(Integer buyOrderModifyApplyId) {

        boolean sameValue = true;

        // 比较新旧值
        BuyOrderModifyApply buyOrderModifyApply = buyOrderModifyApplyMapper.selectSingleById(buyOrderModifyApplyId);
        if (buyOrderModifyApply == null || buyOrderModifyApply.getDeliveryDirect() == null) {
            return;
        }
        sameValue = buyOrderModifyApply.getDeliveryDirect().equals(buyOrderModifyApply.getOldDeliveryDirect());

        // 新旧值(是否直发)不一样，发送站内信
        if (sameValue) {
            return;
        }

        // 查询采购单关联所有销售单，包含一个采购单对应多个销售单场景
        List<SaleorderVo> saleOrderVoList = buyOrderModifyApplyMapper.selectRelatedSaleOrderInfoById(buyOrderModifyApplyId);
        if (CollectionUtils.isEmpty(saleOrderVoList)) {
            return;
        }
        saleOrderVoList.stream().forEach(item -> {

            // 查询对应销售单归属销售
            if (item.getSaleorderId() == null || item.getSaleorderNo() == null) {
                return;
            }
            List<User> attributableSaleList = saleorderMapper.selectAttributableSalesById(item.getSaleorderId());
            if (CollectionUtils.isEmpty(attributableSaleList)) {
                return;
            }
            // 去除集合NULL值 -- 备货单没有归属销售
            attributableSaleList.removeAll(Collections.singleton(null));
            List<Integer> attributableSaleIdList = attributableSaleList.stream().map(oneItem -> oneItem.getUserId()).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(attributableSaleIdList)) {
                return;
            }

            Map<String, String> params = new HashMap<>();
            params.put("saleorderNo", item.getSaleorderNo());
            params.put("deliveryDirectOld", Integer.valueOf(1).equals(buyOrderModifyApply.getOldDeliveryDirect()) ? "直发" : "普发");
            params.put("deliveryDirectNew", Integer.valueOf(1).equals(buyOrderModifyApply.getDeliveryDirect()) ? "直发" : "普发");

            String[] str = {"njadmin", "2"};
            MessageUtil.sendMessage(202, attributableSaleIdList, params, "./orderstream/saleorder/detail.do?saleOrderId=" + item.getSaleorderId(), str);
        });

    }

    @Override
    public void sendMsgVerifyByCompareNewOldDeliveryDirect(Integer buyOrderModifyApplyId) {

        boolean sameValue = true;

        // 比较新旧值
        BuyOrderModifyApply buyOrderModifyApply = buyOrderModifyApplyMapper.selectSingleById(buyOrderModifyApplyId);
        if (buyOrderModifyApply == null || buyOrderModifyApply.getDeliveryDirect() == null) {
            return;
        }
        sameValue = buyOrderModifyApply.getDeliveryDirect().equals(buyOrderModifyApply.getOldDeliveryDirect());

        // 新旧值(是否直发)不一样，发送站内信
        if (sameValue) {
            return;
        }
        // 发站内信给销售单归属销售
        // 查询采购单关联所有销售单，包含一个采购单对应多个销售单场景

        // 发站内信给采购单创建者
        List<BuyorderVo> buyOrderVoList = buyOrderModifyApplyMapper.selectRelatedBuyOrderInfoById(buyOrderModifyApplyId);
        if (CollectionUtils.isEmpty(buyOrderVoList)) {
            return;
        }
        buyOrderVoList.stream().forEach(item -> {

            if (item.getBuyorderId() == null || item.getBuyorderNo() == null || item.getCreator() == null) {
                return;
            }
            ArrayList<Integer> creatorIdsList = new ArrayList<>();
            creatorIdsList.add(item.getCreator());

            Map<String, String> params = new HashMap<>();
            params.put("buyorderNo", item.getBuyorderNo());
            params.put("deliveryDirectOld", Integer.valueOf(1).equals(buyOrderModifyApply.getOldDeliveryDirect()) ? "直发" : "普发");
            params.put("deliveryDirectNew", Integer.valueOf(1).equals(buyOrderModifyApply.getDeliveryDirect()) ? "直发" : "普发");

            String[] str = {"njadmin", "2"};
            MessageUtil.sendMessage(204, creatorIdsList, params, "./order/newBuyorder/newViewBuyOrderDetail.do?buyorderId=" + item.getBuyorderId(), str);


        });
    }

    @Override
    public void sendMsgVerifyNotByCompareNewOldDeliveryDirect(Integer buyOrderModifyApplyId) {

        boolean sameValue = true;

        // 比较新旧值
        BuyOrderModifyApply buyOrderModifyApply = buyOrderModifyApplyMapper.selectSingleById(buyOrderModifyApplyId);
        if (buyOrderModifyApply == null || buyOrderModifyApply.getDeliveryDirect() == null) {
            return;
        }
        sameValue = buyOrderModifyApply.getDeliveryDirect().equals(buyOrderModifyApply.getOldDeliveryDirect());

        // 新旧值(是否直发)不一样，发送站内信
        if (sameValue) {
            return;
        }
        // 查询采购单关联所有销售单，包含一个采购单对应多个销售单场景
        List<SaleorderVo> saleOrderVoList = buyOrderModifyApplyMapper.selectRelatedSaleOrderInfoById(buyOrderModifyApplyId);
        if (CollectionUtils.isEmpty(saleOrderVoList)) {
            return;
        }
        saleOrderVoList.stream().forEach(item -> {

            // 查询对应销售单归属销售
            if (item.getSaleorderId() == null || item.getSaleorderNo() == null) {
                return;
            }
            List<User> attributableSaleList = saleorderMapper.selectAttributableSalesById(item.getSaleorderId());
            if (CollectionUtils.isEmpty(attributableSaleList)) {
                return;
            }
            // 去除集合NULL值 -- 备货单没有归属销售
            attributableSaleList.removeAll(Collections.singleton(null));
            List<Integer> attributableSaleIdList = attributableSaleList.stream().map(oneItem -> oneItem.getUserId()).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(attributableSaleIdList)) {
                return;
            }

            Map<String, String> params = new HashMap<>();
            params.put("saleorderNo", item.getSaleorderNo());
            params.put("deliveryDirectOld", Integer.valueOf(1).equals(buyOrderModifyApply.getOldDeliveryDirect()) ? "直发" : "普发");
            params.put("deliveryDirectNew", Integer.valueOf(1).equals(buyOrderModifyApply.getDeliveryDirect()) ? "直发" : "普发");

            String[] str = {"njadmin", "2"};
            MessageUtil.sendMessage(205, attributableSaleIdList, params, "./orderstream/saleorder/detail.do?saleOrderId=" + item.getSaleorderId(), str);
        });
    }

    @Override
    public void sendMsgByCompareNewOldSkuPrice(Integer buyOrderModifyApplyId) {

        boolean sameValue = true;

        // 比较新旧值
        List<BuyorderModifyApplyGoods> buyorderModifyApplyGoods = buyorderModifyApplyGoodsMapper.selectAllByApplyId(buyOrderModifyApplyId);
        if (CollectionUtils.isEmpty(buyorderModifyApplyGoods)) {
            return;
        }

        for (BuyorderModifyApplyGoods item : buyorderModifyApplyGoods) {
            if ((item.getPrice() != null && !item.getPrice().equals(item.getOldPrice()))) {
                sameValue = false;
                break;
            }
        }

        // 新旧值(价格)不一样，发送站内信
        if (sameValue) {
            return;
        }

        // 是否存在已上传的合同 - 查询回传合同数量
        BuyOrderModifyApply buyOrderModifyApply = buyOrderModifyApplyMapper.selectSingleById(buyOrderModifyApplyId);
        if (buyOrderModifyApply == null || buyOrderModifyApply.getBuyorderId() == null) {
            return;
        }
        Integer attachmentCount = buyorderMapper.selectAttachmentCountByBuyOrderId(buyOrderModifyApply.getBuyorderId());
        if (attachmentCount.compareTo(Integer.valueOf(0)) == 0) {
            return;
        }

        // 发站内信给采购单创建者
        List<BuyorderVo> buyOrderVoList = buyOrderModifyApplyMapper.selectRelatedBuyOrderInfoById(buyOrderModifyApplyId);
        if (CollectionUtils.isEmpty(buyOrderVoList)) {
            return;
        }
        buyOrderVoList.stream().forEach(item -> {

            if (item.getBuyorderId() == null || item.getBuyorderNo() == null || item.getCreator() == null) {
                return;
            }
            ArrayList<Integer> creatorIdsList = new ArrayList<>();
            creatorIdsList.add(item.getCreator());

            Map<String, String> params = new HashMap<>();
            params.put("buyorderNo", item.getBuyorderNo());

            String[] str = {"njadmin", "2"};
            MessageUtil.sendMessage(206, creatorIdsList, params, "./order/newBuyorder/newViewBuyOrderDetail.do?buyorderId=" + item.getBuyorderId(), str);


        });
    }

    @Override
    public void notifySaleOrderUpdateState(Integer buyorderModifyApplyId) {


    }

    @Override
    public StepBar generateStepBar(Map<String, Object> historicInfo, List<String> usersList, List<String> verifyUsersList) {

        // String
        Object startUser = historicInfo.get("startUser");
        // List<HistoricActivityInstanceEntity>
        Object historicActivityInstance = historicInfo.get("historicActivityInstance");
        // HashMap<String,HashMap<String,String>>
        Object commentMap = historicInfo.get("commentMap");

        StepBar stepBar = new StepBar();
        ArrayList<StepBar.Step> stepList = new ArrayList<>();
        if (historicActivityInstance instanceof List) {
            List<HistoricActivityInstanceEntity> historicActivityInstanceList = (List<HistoricActivityInstanceEntity>) historicActivityInstance;
            for (int i = 0; i < historicActivityInstanceList.size(); i++) {

                HistoricActivityInstanceEntity historicActivityInstanceEntity = historicActivityInstanceList.get(i);
                if (StringUtil.isEmpty(historicActivityInstanceEntity.getActivityName())) {
                    continue;
                }
                StepBar.Step step = new StepBar.Step();
                if (historicActivityInstanceEntity.getEndTime() != null) {
                    step.setOperateTime(DateUtil.DateToString(historicActivityInstanceEntity.getEndTime(), DateUtil.TIME_FORMAT));
                }
                if ("startEvent".equals(historicActivityInstanceEntity.getActivityType())) {
                    step.setOperateName((startUser instanceof String) ? (String) startUser : "");
                    step.setTitle("开始");
                } else if ("intermediateThrowEvent".equals(historicActivityInstanceEntity.getActivityType())) {
                    step.setOperateName("");
                    step.setTitle("结束");
                } else {
                    step.setOperateName(historicActivityInstanceEntity.getAssignee());
                    step.setTitle(historicActivityInstanceEntity.getActivityName());
                }
                if (commentMap instanceof HashMap && historicActivityInstanceEntity.getTaskId() != null) {
                    Object o = ((HashMap) commentMap).get(historicActivityInstanceEntity.getTaskId());
                    if (o instanceof String) {
                        step.setDesc((String) o);
                    }
                }
                stepList.add(step);
            }
        }
        stepBar.setStepList(stepList);
        return stepBar;

    }
    @Override
    public boolean checkNumByExpress(Integer buyorderId, String id_arrivalNum) {
        Long time = DateUtil.sysTimeMillis();
        List<BuyorderGoods> buyorderGoodsList = new ArrayList<>();
        Buyorder buyorder = new Buyorder();
        buyorder.setBuyorderId(buyorderId);
        parseIdStr(buyorder, id_arrivalNum, time, buyorderGoodsList);

        // 汇总重复BuyOrderGoodsId的到货数量
        List<BuyorderGoods> resultBuyorderGoodsList = getSummaryAfterBuyOrderGoods(buyorderGoodsList);

        //已收货数量 + 本次收货数量 需  小于等于  已快递数量
        for (BuyorderGoods curGoods : resultBuyorderGoodsList) {
            BuyorderGoods hisGoods = buyorderGoodsMapper.selectByPrimaryKey(curGoods.getBuyorderGoodsId());
            List<ExpressDetail> expressDetails = expressMapper.getExpressDetailsListByBuyorderGoodsId(hisGoods);
            if(CollectionUtils.isEmpty(expressDetails)){
                return false;
            }else{
                int sum = expressDetails.stream().mapToInt(ExpressDetail::getNum).sum();
                if(sum < hisGoods.getArrivalNum() + curGoods.getArrivalNum()){
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 获取汇总后的采购SKU集合
     * @param buyorderGoodsList
     * @return
     */
    private List<BuyorderGoods> getSummaryAfterBuyOrderGoods(List<BuyorderGoods> buyorderGoodsList) {
        List<BuyorderGoods> resultBuyorderGoodsList = new ArrayList<>();
        buyorderGoodsList.stream().forEach(item->{
            int sumArrivalNum = buyorderGoodsList.stream().filter(innerItem -> item.getBuyorderGoodsId().equals(innerItem.getBuyorderGoodsId())).mapToInt(BuyorderGoods::getArrivalNum).sum();
            BuyorderGoods buyorderGoods = new BuyorderGoods();
            BeanUtils.copyProperties(item,buyorderGoods);
            buyorderGoods.setArrivalNum(sumArrivalNum);

            if(resultBuyorderGoodsList.stream().map(BuyorderGoods::getBuyorderGoodsId).collect(Collectors.toList()).contains(item.getBuyorderGoodsId())){
                return;
            }
            resultBuyorderGoodsList.add(buyorderGoods);
        });
        return resultBuyorderGoodsList;
    }

    private void parseIdStr(Buyorder buyorder, String id_arrivalNum, Long time, List<BuyorderGoods> buyorderGoodsList) {
        if (null != buyorder.getBuyorderId() && StringUtils.isNotEmpty(id_arrivalNum)) {
            String[] lines = id_arrivalNum.split(",");
            for (String line : lines) {
                String[] split = line.split("_");
                if (split.length == 3) {
                    Integer expressId = Integer.parseInt(split[0]);
                    Integer buyOrderGoodId = Integer.parseInt(split[1]);
                    Integer arrivalNum = Integer.parseInt(split[2]);

                    BuyorderGoods buyorderGoods = new BuyorderGoods();
                    buyorderGoods.setExpressId(expressId);
                    buyorderGoods.setBuyorderGoodsId(buyOrderGoodId);
                    buyorderGoods.setArrivalNum(arrivalNum);
                    buyorderGoods.setArrivalTime(time);
                    buyorderGoodsList.add(buyorderGoods);
                }
            }
        }
    }

    //VDERP-8571销售订单专向发货sku创建采购单与下发数量逻辑强化--校验限制专项发货不可混单采购start
    @Override
    public ResultInfo checkBuyorderSpecial(BuyorderVo buyorderVo,String[] dbbuysums) {
        ResultInfo resultInfo = new ResultInfo(0,"校验成功");
        Map<Integer,Integer> saleorderMap = new HashMap<>();
        Map<Integer,Integer> normalMap = new HashMap<>();
        if(buyorderVo.getBuyorderId() == null || ErpConst.ZERO.equals(buyorderVo.getBuyorderId())){
            //生成采购单校验混采
            String saleorderGoodsIdstr = buyorderVo.getSaleorderGoodsIds();
            String[] saleorderGoodsArray = saleorderGoodsIdstr.split(",");
            //分割前台传参获取销售单商品id
            for(String saleorderGood : saleorderGoodsArray){
                String[] goodsInfo = saleorderGood.split("\\|");
                //分隔符前为goodsid后为saleordergoodsid
                if(goodsInfo.length < 2){
                    return new ResultInfo(-1,"校验采购单失败");
                }
                SaleorderGoods saleorderGoods = saleorderGoodsMapper.selectByPrimaryKey(Integer.parseInt(goodsInfo[1]));
                if(saleorderGoods != null){
                    if(ErpConst.ONE.equals(saleorderGoods.getSpecialDelivery())){
                        //是专项发货，记录
                        if(saleorderMap.containsKey(saleorderGoods.getSaleorderId())){
                            //如果map存在销售单id，则记录数+1
                            saleorderMap.put(saleorderGoods.getSaleorderId(),saleorderMap.get(saleorderGoods.getSaleorderId()) + 1);
                        }else {
                            saleorderMap.put(saleorderGoods.getSaleorderId(),1);
                        }
                    }else{
                        //专项发货记录到另外一个map
                        normalMap.put(saleorderGoods.getSaleorderId(),1);
                    }
                }
            }
        }else if(dbbuysums != null){
            //加入采购单--校验混采
            for(String buySum : dbbuysums){
                String[] goodsInfo = buySum.split("\\|");
                if(goodsInfo.length < 2){
                    return new ResultInfo(-1,"校验采购单失败");
                }
                SaleorderGoods saleorderGoods = saleorderGoodsMapper.selectByPrimaryKey(Integer.parseInt(goodsInfo[1]));
                if(saleorderGoods != null){
                    if(ErpConst.ONE.equals(saleorderGoods.getSpecialDelivery())){
                        //是专项发货，记录
                        if(saleorderMap.containsKey(saleorderGoods.getSaleorderId())){
                            //如果map存在销售单id，则记录数+1
                            saleorderMap.put(saleorderGoods.getSaleorderId(),saleorderMap.get(saleorderGoods.getSaleorderId()) + 1);
                        }else {
                            saleorderMap.put(saleorderGoods.getSaleorderId(),1);
                        }
                    }else{
                        //专项发货记录到另外一个map
                        normalMap.put(saleorderGoods.getSaleorderId(),1);
                    }
                }
            }
            List<SaleorderGoods> saleorderGoodsList = saleorderGoodsMapper.getSaleorderGoodsByBuyorderId(buyorderVo.getBuyorderId());
            for(SaleorderGoods saleorderGoods : saleorderGoodsList){
                if(ErpConst.ONE.equals(saleorderGoods.getSpecialDelivery())){
                    //是专项发货，记录
                    if(saleorderMap.containsKey(saleorderGoods.getSaleorderId())){
                        //如果map存在销售单id，则记录数+1
                        saleorderMap.put(saleorderGoods.getSaleorderId(),saleorderMap.get(saleorderGoods.getSaleorderId()) + 1);
                    }else {
                        saleorderMap.put(saleorderGoods.getSaleorderId(),1);
                    }
                }else{
                    //专项发货记录到另外一个map
                    normalMap.put(saleorderGoods.getSaleorderId(),1);
                }
            }
        }else {
            resultInfo = new ResultInfo(-3,"参数传递不正确，无法判断采购商品");
        }
        if(saleorderMap.size() > 1){
            resultInfo = new ResultInfo(-3,"专向仅允许选择同订单专向产品");
        }
        if(!saleorderMap.isEmpty() && !normalMap.isEmpty()){
            resultInfo = new ResultInfo(-3,"专向与非专向不允许同时被选择");
        }
        return resultInfo;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW,rollbackFor = Exception.class)
    public void udpateContractUrlByBuyOrderId(int buyOrderId,String contractUrl) {
        buyorderMapper.updateContractUrlOfBuyorder(buyOrderId,contractUrl);
    }
    //VDERP-8571销售订单专向发货sku创建采购单与下发数量逻辑强化--校验限制专项发货不可混单采购start


    @Override
    public List<Integer> queryBuyorderGoodsIdsBySaleorderGoodsIds(List<Integer> saleirderDirectGoods) {
        List<Integer> buyorderGoodsIds = rBuyorderSaleorderMapper.queryBuyorderGoodsIdsBySaleorderGoodsIds(saleirderDirectGoods);
        return buyorderGoodsIds;
    }

    @Override
    public boolean validDirectAfterSales(Integer buyorderGoodsId, Integer goodsId) {
        List<Integer> saleorderIds = rBuyorderSaleorderMapper.getSaleOrderIdByBuyOrderGoodsId(buyorderGoodsId);
        if (saleorderIds.size() != 1) {
            return true;
        } else {
            List<Map<String, Integer>> buyorderIds = rBuyorderSaleorderMapper.getBuyorderIdBySaleorderIdAndGoodsId(goodsId, saleorderIds.get(0));
            return buyorderIds.size() != 1 || buyorderIds.get(0).get("DELIVERY_NUM") <= 0;
        }
    }

    @Override
    public BuyorderVo getBuyOrderInfoForAddExpress(Buyorder buyorder) {
        BuyorderVo bv = new BuyorderVo();
        bv.setBuyorderId(buyorder.getBuyorderId());
        bv.setBuyorderNo(buyorder.getBuyorderNo());
        List<BuyorderGoodsVo> buyOrderGoodsVoList = buyorderGoodsMapper.getBuyOrderGoodsVoListForAddExpress(buyorder.getBuyorderId());
        for (BuyorderGoodsVo buyorderGoodsVo : buyOrderGoodsVoList) {
            // 采购售后退货数量
            Integer goodsAfterReturnNum = Optional.ofNullable(afterSalesGoodsMapper.getGoodsAfterReturnNum(buyorderGoodsVo.getBuyorderGoodsId(), buyorderGoodsVo.getCompanyId(), SysOptionConstant.ID_536)).orElse(0);
            buyorderGoodsVo.setAfterReturnNum(goodsAfterReturnNum);
        }
        // 处理GE信息
        editBuyorderGEInfo(bv);
        bv.setBuyorderGoodsVoList(buyOrderGoodsVoList);
        return bv;
    }

    @Override
    public void updateGiftOrderStatus(Integer buyorderId) {
        long currentTimeMillis = System.currentTimeMillis();
        Buyorder buyorder = buyorderMapper.selectByPrimaryKey(buyorderId);
        if(buyorder != null && buyorder.getIsGift() != null && buyorder.getIsGift().equals(1)){
            Buyorder buyorderUpdate = new Buyorder();
            buyorderUpdate.setBuyorderId(buyorderId);
            buyorderUpdate.setPaymentStatus(2);
            if(buyorder.getPaymentTime() == null || buyorder.getPaymentTime() == 0L){
                buyorderUpdate.setPaymentTime(currentTimeMillis);
            }
            buyorderUpdate.setInvoiceStatus(2);
            if(buyorder.getInvoiceTime() == null || buyorder.getInvoiceTime() == 0L){
                buyorderUpdate.setInvoiceTime(currentTimeMillis);
            }
            buyorderMapper.updateByPrimaryKeySelective(buyorderUpdate);
        }
    }


}
