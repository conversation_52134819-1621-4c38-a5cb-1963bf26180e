package com.vedeng.crm.business.quote.mapper;

import com.vedeng.crm.business.quote.domain.entity.CrmQuoteorderGoods;
import com.vedeng.crm.business.quote.domain.entity.QuoteorderNeedsEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface QuoteorderNeedsMapper {
    int deleteByPrimaryKey(Long quoteorderNeedsId);

    int insertSelective(QuoteorderNeedsEntity record);

    QuoteorderNeedsEntity selectByPrimaryKey(Long quoteorderNeedsId);

    int updateByPrimaryKeySelective(QuoteorderNeedsEntity record);

    int updateByPrimaryKey(QuoteorderNeedsEntity record);

    int batchInsert(List<QuoteorderNeedsEntity> record);

    List<QuoteorderNeedsEntity> selectAllByQuoteorderId(Integer quoteorderId);

    List<QuoteorderNeedsEntity>  selectByQuoteNeedsIdList(@Param(value="quoteorderNeedsIdList") List<Long> quoteorderNeedsIdList);

    void batchDelete(List<QuoteorderNeedsEntity> extraOldList);

    List<CrmQuoteorderGoods> queryQuoteGoodsByNeedsIdAndSkuNos(@Param("skuNos") List<String> skuNos,@Param("quoteorderNeedsId") Long quoteorderNeedsId);
}
