<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.trader.mapper.PublicCustomerCalculateRulesMapper">

    <select id="getAllRules" resultType="com.vedeng.erp.trader.domain.PublicCustomerCalculateRules">
        SELECT
            *
        FROM
            T_PUBLIC_CUSTOMER_CALCULATE_RULES
        ORDER BY
            ADD_TIME DESC
    </select>

    <select id="getRecentlyPublicCustomerCalculateRule"
            resultType="com.vedeng.erp.trader.domain.PublicCustomerCalculateRules">
        SELECT
            *
        FROM
            T_PUBLIC_CUSTOMER_CALCULATE_RULES
        ORDER BY
            PUBLIC_CUSTOMER_CALCULATE_RULES_ID DESC
            LIMIT 1
    </select>

    <insert id="insert">
        INSERT INTO T_PUBLIC_CUSTOMER_CALCULATE_RULES ( CUSTOMER_CREATED_DAYS, VALID_ORDER_DAYS, VALID_ORDER_COUNT, VALID_ORDER_CALCULATOR,
        COMMUNICATION_DAYS, COMMUNICATION_COUNT, COMMUNICATION_CALCULATOR,LOCK_PROTECT_DAYS, ADD_TIME, CREATOR )
        VALUES
	    (#{customerCreatedDays,jdbcType=INTEGER}, #{validOrderDays,jdbcType=INTEGER},#{validOrderCount,jdbcType=INTEGER},
	    #{validOrderCalculator,jdbcType=INTEGER},#{communicationDays,jdbcType=INTEGER},#{communicationCount,jdbcType=INTEGER},
	    #{communicationCalculator,jdbcType=INTEGER},#{lockProtectDays,jdbcType=INTEGER},#{addTime,jdbcType=BIGINT},#{creator,jdbcType=INTEGER})
    </insert>

</mapper>
