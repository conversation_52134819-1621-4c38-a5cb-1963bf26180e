package com.vedeng.system.service;

import java.util.List;
import java.util.Map;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.order.model.AuthorizationStorage;
import com.vedeng.system.model.Attachment;

public interface AttachmentService {
	/**
	 * <b>Description:</b><br> 新增或修改附件数据
	 * @param ad
	 * @return
	 * @Note
	 * <b>Author:</b> leo.yang
	 * <br><b>Date:</b> 2018年7月25日 下午6:37:56
	 */
	ResultInfo<?> saveOrUpdateAttachment(Attachment attachment);

	void saveAuthorization(String[] fileName, String[] fileUri, String domain, Integer relatedId, Long time ,Integer functionId);

	void delAuthorizationStorageInfo(Integer temporaryStorageId, Integer authorizationStorageFile);

	List<Attachment> getAttachmentInfoByRelatedIdAndFunctionId(Integer authorizationApplyId, Integer authorizationApplyFile);

	/**
	 * <AUTHOR>
	 * @desc 根据ATTACHMENT_TYPE或ATTACHMENT_FUNCTION或RELATED_ID或ATTACHMENT_ID查询附件信息
	 * @param attachment
	 * @return
	 */
	List<Attachment> queryAttachmentList(Attachment attachment);

	/**
	 * 根据附件id删除附件
	 * @param attachment
	 */
	void delAttachment(Attachment attachment);

	/**
	 * 获取附件列表
	 * @param attachment
	 * @return
	 */
	List<Attachment> queryOutInAttachmentList(Attachment attachment);

	/**
	 * 删除出库单附件 (软删除)
	 * @param attachment
	 */
	void delWarehouseOutAttachment(Attachment attachment);
}
