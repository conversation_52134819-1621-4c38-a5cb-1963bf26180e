package com.vedeng.erp.buyorder.service.impl;

import com.vedeng.authorization.model.User;
import com.vedeng.base.api.dto.reqParam.BusinessInfo;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.buyorder.manager.esign.BuyOrderElectronicSignHandle;
import com.vedeng.erp.buyorder.manager.esign.MultBuyOrderElectronicSignHandle;
import com.vedeng.erp.buyorder.service.BuyorderElectronicSignatureService;
import com.vedeng.infrastructure.esign.domain.ElectronicSignParam;
import com.vedeng.infrastructure.esign.enums.ElectronicSignBusinessEnums;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/9 11:09
 * @describe
 */
@Service
public class BuyorderElectronicSignatureServiceImpl implements BuyorderElectronicSignatureService {

    public static Logger logger = LoggerFactory.getLogger(BuyorderElectronicSignatureServiceImpl.class);

    @Autowired
    private BuyOrderElectronicSignHandle buyOrderElectronicSignHandle;

    /**
     * 双公司章的场景
     */
    @Autowired
    private MultBuyOrderElectronicSignHandle multBuyOrderElectronicSignHandle;

    @Override
    public ResultInfo<?> signature(Integer buyorderId, User user) {
        ResultInfo<?> result = new ResultInfo<>();
        try {
            BusinessInfo businessInfo = new BusinessInfo();
            // 操作人
            businessInfo.setOperator(user.getUsername());

            ElectronicSignParam electronicSignParam = buyOrderElectronicSignHandle.buildElectronicSignParam(buyorderId,businessInfo);
            buyOrderElectronicSignHandle.electronicSign(electronicSignParam);
            result.setCode(0);
            result.setMessage("操作成功，电子签单签署需要2分钟左右完成，请稍后刷新页面查看");
//            if(false){
//                ElectronicSignParam electronicSignParam = ElectronicSignParam
//                        .builder()
//                        .buyOrderId(buyorderId)
//                        .flowType(2)
//                        .businessInfo(businessInfo)
//                        .electronicSignBusinessEnums(ElectronicSignBusinessEnums.BUY_ORDER)
//                        .build();
//                buyOrderElectronicSignHandle.electronicSign(electronicSignParam);
//                result.setCode(0);
//                result.setMessage("操作成功");
//            }else{
//                ElectronicSignParam electronicSignParam = ElectronicSignParam
//                        .builder()
//                        .buyOrderId(buyorderId)
//                        .flowType(3) // 3 表示多主体印章盖章
//                        .businessInfo(businessInfo)
//                        .electronicSignBusinessEnums(ElectronicSignBusinessEnums.MULT_BUYORDER_SEAL)
//                        .build();
//                multBuyOrderElectronicSignHandle.electronicSign(electronicSignParam);
//                result.setCode(0);
//                result.setMessage("操作成功");
//            }

        } catch (Exception e) {
            logger.error("BuyorderElectronicSignatureServiceImpl -> signature : error", e);
            result.setCode(-1);
            result.setMessage("操作失败，调用电子签章服务失败");
        }
        return result;
    }


}
