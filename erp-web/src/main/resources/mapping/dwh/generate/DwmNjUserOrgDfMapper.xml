<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.dwh.externaldb.dao.generate.DwmNjUserOrgDfMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.dwh.model.generate.DwmNjUserOrgDfDo">
    <id column="VD_ODS_PK" jdbcType="BIGINT" property="vdOdsPk" />
    <result column="START_TIME" jdbcType="TIMESTAMP" property="startTime" />
    <result column="END_TIME" jdbcType="TIMESTAMP" property="endTime" />
    <result column="USER_ID" jdbcType="INTEGER" property="userId" />
    <result column="ORG_ID" jdbcType="INTEGER" property="orgId" />
    <result column="USERNAME" jdbcType="VARCHAR" property="username" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="POSITION_ID" jdbcType="INTEGER" property="positionId" />
    <result column="POSITION_NAME" jdbcType="VARCHAR" property="positionName" />
    <result column="POSITION_TYPE" jdbcType="INTEGER" property="positionType" />
    <result column="POSITION_LEVEL" jdbcType="INTEGER" property="positionLevel" />
    <result column="IS_ADMIN" jdbcType="INTEGER" property="isAdmin" />
    <result column="IS_DISABLED" jdbcType="INTEGER" property="isDisabled" />
    <result column="NUMBER" jdbcType="INTEGER" property="number" />
    <result column="SYSTEM" jdbcType="VARCHAR" property="system" />
    <result column="PARENT_ID" jdbcType="INTEGER" property="parentId" />
    <result column="SEX" jdbcType="INTEGER" property="sex" />
    <result column="MOBILE" jdbcType="VARCHAR" property="mobile" />
    <result column="REAL_NAME" jdbcType="VARCHAR" property="realName" />
    <result column="EMAIL" jdbcType="VARCHAR" property="email" />
    <result column="TELEPHONE" jdbcType="VARCHAR" property="telephone" />
    <result column="L1_ID" jdbcType="INTEGER" property="l1Id" />
    <result column="L1_NAME" jdbcType="VARCHAR" property="l1Name" />
    <result column="L2_ID" jdbcType="INTEGER" property="l2Id" />
    <result column="L2_NAME" jdbcType="VARCHAR" property="l2Name" />
    <result column="L3_ID" jdbcType="INTEGER" property="l3Id" />
    <result column="L3_NAME" jdbcType="VARCHAR" property="l3Name" />
  </resultMap>
  <sql id="Base_Column_List">
    VD_ODS_PK, START_TIME, END_TIME, USER_ID, ORG_ID, USERNAME, COMPANY_ID, POSITION_ID, 
    POSITION_NAME, POSITION_TYPE, POSITION_LEVEL, IS_ADMIN, IS_DISABLED, `NUMBER`, `SYSTEM`, 
    PARENT_ID, SEX, MOBILE, REAL_NAME, EMAIL, TELEPHONE, L1_ID, L1_NAME, L2_ID, L2_NAME, 
    L3_ID, L3_NAME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from DWM_NJ_USER_ORG_DF
    where VD_ODS_PK = #{vdOdsPk,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from DWM_NJ_USER_ORG_DF
    where VD_ODS_PK = #{vdOdsPk,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="VD_ODS_PK" keyProperty="vdOdsPk" parameterType="com.vedeng.dwh.model.generate.DwmNjUserOrgDfDo" useGeneratedKeys="true">
    insert into DWM_NJ_USER_ORG_DF (START_TIME, END_TIME, USER_ID, 
      ORG_ID, USERNAME, COMPANY_ID, 
      POSITION_ID, POSITION_NAME, POSITION_TYPE, 
      POSITION_LEVEL, IS_ADMIN, IS_DISABLED, 
      `NUMBER`, `SYSTEM`, PARENT_ID, 
      SEX, MOBILE, REAL_NAME, 
      EMAIL, TELEPHONE, L1_ID, 
      L1_NAME, L2_ID, L2_NAME, 
      L3_ID, L3_NAME)
    values (#{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{userId,jdbcType=INTEGER}, 
      #{orgId,jdbcType=INTEGER}, #{username,jdbcType=VARCHAR}, #{companyId,jdbcType=INTEGER}, 
      #{positionId,jdbcType=INTEGER}, #{positionName,jdbcType=VARCHAR}, #{positionType,jdbcType=INTEGER}, 
      #{positionLevel,jdbcType=INTEGER}, #{isAdmin,jdbcType=INTEGER}, #{isDisabled,jdbcType=INTEGER}, 
      #{number,jdbcType=INTEGER}, #{system,jdbcType=VARCHAR}, #{parentId,jdbcType=INTEGER}, 
      #{sex,jdbcType=INTEGER}, #{mobile,jdbcType=VARCHAR}, #{realName,jdbcType=VARCHAR}, 
      #{email,jdbcType=VARCHAR}, #{telephone,jdbcType=VARCHAR}, #{l1Id,jdbcType=INTEGER}, 
      #{l1Name,jdbcType=VARCHAR}, #{l2Id,jdbcType=INTEGER}, #{l2Name,jdbcType=VARCHAR}, 
      #{l3Id,jdbcType=INTEGER}, #{l3Name,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="VD_ODS_PK" keyProperty="vdOdsPk" parameterType="com.vedeng.dwh.model.generate.DwmNjUserOrgDfDo" useGeneratedKeys="true">
    insert into DWM_NJ_USER_ORG_DF
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="startTime != null">
        START_TIME,
      </if>
      <if test="endTime != null">
        END_TIME,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="orgId != null">
        ORG_ID,
      </if>
      <if test="username != null">
        USERNAME,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="positionId != null">
        POSITION_ID,
      </if>
      <if test="positionName != null">
        POSITION_NAME,
      </if>
      <if test="positionType != null">
        POSITION_TYPE,
      </if>
      <if test="positionLevel != null">
        POSITION_LEVEL,
      </if>
      <if test="isAdmin != null">
        IS_ADMIN,
      </if>
      <if test="isDisabled != null">
        IS_DISABLED,
      </if>
      <if test="number != null">
        `NUMBER`,
      </if>
      <if test="system != null">
        `SYSTEM`,
      </if>
      <if test="parentId != null">
        PARENT_ID,
      </if>
      <if test="sex != null">
        SEX,
      </if>
      <if test="mobile != null">
        MOBILE,
      </if>
      <if test="realName != null">
        REAL_NAME,
      </if>
      <if test="email != null">
        EMAIL,
      </if>
      <if test="telephone != null">
        TELEPHONE,
      </if>
      <if test="l1Id != null">
        L1_ID,
      </if>
      <if test="l1Name != null">
        L1_NAME,
      </if>
      <if test="l2Id != null">
        L2_ID,
      </if>
      <if test="l2Name != null">
        L2_NAME,
      </if>
      <if test="l3Id != null">
        L3_ID,
      </if>
      <if test="l3Name != null">
        L3_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="positionId != null">
        #{positionId,jdbcType=INTEGER},
      </if>
      <if test="positionName != null">
        #{positionName,jdbcType=VARCHAR},
      </if>
      <if test="positionType != null">
        #{positionType,jdbcType=INTEGER},
      </if>
      <if test="positionLevel != null">
        #{positionLevel,jdbcType=INTEGER},
      </if>
      <if test="isAdmin != null">
        #{isAdmin,jdbcType=INTEGER},
      </if>
      <if test="isDisabled != null">
        #{isDisabled,jdbcType=INTEGER},
      </if>
      <if test="number != null">
        #{number,jdbcType=INTEGER},
      </if>
      <if test="system != null">
        #{system,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=INTEGER},
      </if>
      <if test="sex != null">
        #{sex,jdbcType=INTEGER},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="realName != null">
        #{realName,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="l1Id != null">
        #{l1Id,jdbcType=INTEGER},
      </if>
      <if test="l1Name != null">
        #{l1Name,jdbcType=VARCHAR},
      </if>
      <if test="l2Id != null">
        #{l2Id,jdbcType=INTEGER},
      </if>
      <if test="l2Name != null">
        #{l2Name,jdbcType=VARCHAR},
      </if>
      <if test="l3Id != null">
        #{l3Id,jdbcType=INTEGER},
      </if>
      <if test="l3Name != null">
        #{l3Name,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.dwh.model.generate.DwmNjUserOrgDfDo">
    update DWM_NJ_USER_ORG_DF
    <set>
      <if test="startTime != null">
        START_TIME = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        END_TIME = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        ORG_ID = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="username != null">
        USERNAME = #{username,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="positionId != null">
        POSITION_ID = #{positionId,jdbcType=INTEGER},
      </if>
      <if test="positionName != null">
        POSITION_NAME = #{positionName,jdbcType=VARCHAR},
      </if>
      <if test="positionType != null">
        POSITION_TYPE = #{positionType,jdbcType=INTEGER},
      </if>
      <if test="positionLevel != null">
        POSITION_LEVEL = #{positionLevel,jdbcType=INTEGER},
      </if>
      <if test="isAdmin != null">
        IS_ADMIN = #{isAdmin,jdbcType=INTEGER},
      </if>
      <if test="isDisabled != null">
        IS_DISABLED = #{isDisabled,jdbcType=INTEGER},
      </if>
      <if test="number != null">
        `NUMBER` = #{number,jdbcType=INTEGER},
      </if>
      <if test="system != null">
        `SYSTEM` = #{system,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        PARENT_ID = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="sex != null">
        SEX = #{sex,jdbcType=INTEGER},
      </if>
      <if test="mobile != null">
        MOBILE = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="realName != null">
        REAL_NAME = #{realName,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        EMAIL = #{email,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        TELEPHONE = #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="l1Id != null">
        L1_ID = #{l1Id,jdbcType=INTEGER},
      </if>
      <if test="l1Name != null">
        L1_NAME = #{l1Name,jdbcType=VARCHAR},
      </if>
      <if test="l2Id != null">
        L2_ID = #{l2Id,jdbcType=INTEGER},
      </if>
      <if test="l2Name != null">
        L2_NAME = #{l2Name,jdbcType=VARCHAR},
      </if>
      <if test="l3Id != null">
        L3_ID = #{l3Id,jdbcType=INTEGER},
      </if>
      <if test="l3Name != null">
        L3_NAME = #{l3Name,jdbcType=VARCHAR},
      </if>
    </set>
    where VD_ODS_PK = #{vdOdsPk,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.dwh.model.generate.DwmNjUserOrgDfDo">
    update DWM_NJ_USER_ORG_DF
    set START_TIME = #{startTime,jdbcType=TIMESTAMP},
      END_TIME = #{endTime,jdbcType=TIMESTAMP},
      USER_ID = #{userId,jdbcType=INTEGER},
      ORG_ID = #{orgId,jdbcType=INTEGER},
      USERNAME = #{username,jdbcType=VARCHAR},
      COMPANY_ID = #{companyId,jdbcType=INTEGER},
      POSITION_ID = #{positionId,jdbcType=INTEGER},
      POSITION_NAME = #{positionName,jdbcType=VARCHAR},
      POSITION_TYPE = #{positionType,jdbcType=INTEGER},
      POSITION_LEVEL = #{positionLevel,jdbcType=INTEGER},
      IS_ADMIN = #{isAdmin,jdbcType=INTEGER},
      IS_DISABLED = #{isDisabled,jdbcType=INTEGER},
      `NUMBER` = #{number,jdbcType=INTEGER},
      `SYSTEM` = #{system,jdbcType=VARCHAR},
      PARENT_ID = #{parentId,jdbcType=INTEGER},
      SEX = #{sex,jdbcType=INTEGER},
      MOBILE = #{mobile,jdbcType=VARCHAR},
      REAL_NAME = #{realName,jdbcType=VARCHAR},
      EMAIL = #{email,jdbcType=VARCHAR},
      TELEPHONE = #{telephone,jdbcType=VARCHAR},
      L1_ID = #{l1Id,jdbcType=INTEGER},
      L1_NAME = #{l1Name,jdbcType=VARCHAR},
      L2_ID = #{l2Id,jdbcType=INTEGER},
      L2_NAME = #{l2Name,jdbcType=VARCHAR},
      L3_ID = #{l3Id,jdbcType=INTEGER},
      L3_NAME = #{l3Name,jdbcType=VARCHAR}
    where VD_ODS_PK = #{vdOdsPk,jdbcType=BIGINT}
  </update>
</mapper>