package com.vedeng.erp.finance.mapper;

import com.vedeng.erp.finance.domain.entity.InvoiceVoucherEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface InvoiceVoucherMapper {
    int deleteByPrimaryKey(Long invoiceVoucherId);

    int insert(InvoiceVoucherEntity record);

    int insertOrUpdate(InvoiceVoucherEntity record);

    int insertOrUpdateSelective(InvoiceVoucherEntity record);

    int insertSelective(InvoiceVoucherEntity record);

    InvoiceVoucherEntity selectByPrimaryKey(Long invoiceVoucherId);

    int updateByPrimaryKeySelective(InvoiceVoucherEntity record);

    int updateFileUrlByVoucherUrlAndVoucherNo(InvoiceVoucherEntity record);

    int updateByPrimaryKey(InvoiceVoucherEntity record);

    int updateBatch(List<InvoiceVoucherEntity> list);

    int updateBatchSelective(List<InvoiceVoucherEntity> list);

    int batchInsert(@Param("list") List<InvoiceVoucherEntity> list);

    /**
     * 根据发票id查询
     *
     * @param invoiceId invoiceId
     * @return InvoiceVoucherEntity
     */
    InvoiceVoucherEntity findByInvoiceId(@Param("invoiceId") String invoiceId);

    InvoiceVoucherEntity getByVoucherDateAndNo(@Param("voucherDate") String voucherDate, @Param("voucherNo") String voucherNo);
}