package com.vedeng.erp.buyorder.service;

import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.buyorder.domain.entity.GeActionLog;
import com.vedeng.erp.buyorder.domain.entity.GeBusinessChance;
import com.vedeng.erp.buyorder.dto.GeBusinessChanceDto;
import com.vedeng.erp.buyorder.dto.GeExamineBasicDto;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/2/16 13:00
 * @describe
 */
public interface GeBusinessChanceService {
    GeExamineBasicDto queryBasicInfo(Integer geBussinessChanceId);

    /**
     * <AUTHOR>
     * @desc 保存GE商机
     * @param geBusinessChanc
     * @return
     */
    ResultInfo saveGeBusinesschance(GeBusinessChance geBusinessChanc, User user);

    /**
     * <AUTHOR>
     * @desc 查询GE商机基础信息
     * @param geBussinessChanceId
     */
    GeBusinessChanceDto searchGeBusinessChanceInfo(ModelAndView mv, Integer geBussinessChanceId);

    /**
     * <AUTHOR>
     * @desc 根据商机ID查询信息
     * @param geBusinessChanceId
     * @return
     */
    GeBusinessChanceDto queryGeBusinessChanceById(Integer geBusinessChanceId);

    List<GeActionLog> queryOperationLog(Integer geBussinessChanceId);
}
