.vd-ui-page {
  position: relative;
  display: flex;
  justify-content: flex-end;
}
.vd-ui-page .vd-ui-page-list {
  display: flex;
  justify-content: center;
}
.vd-ui-page .vd-ui-page-jump {
  display: flex;
  margin-left: 20px;
}
.vd-ui-page .vd-ui-page-jump .vd-ui-input {
  width: 80px !important;
  margin-right: -1px;
  position: relative;
  z-index: 1;
}
.vd-ui-page .vd-ui-page-jump .vd-ui-input .vd-ui-input__inner {
  border-radius: 3px 0 0 3px;
}
.vd-ui-page .vd-ui-page-jump .vd-ui-button {
  border-radius: 0 3px 3px 0;
  line-height: 28px;
  padding: 0 14px;
}
.vd-ui-page .vd-ui-page-total-txt {
  line-height: 30px;
  position: absolute;
  left: 0;
  top: 0;
}
.vd-ui-page .vd-ui-page-total-txt.vd-ui-total-small {
  line-height: 29px;
}
.vd-ui-page .vd-ui-page-item {
  width: 33px;
  height: 30px;
  text-align: center;
  line-height: 28px;
  background: #F5F7FA;
  border-radius: 3px;
  box-sizing: border-box;
  border: solid 1px #BABFC2;
  margin-right: 5px;
  cursor: pointer;
  text-decoration: none;
  color: #333333;
}
.vd-ui-page .vd-ui-page-item:hover {
  background: #EBEFF2;
}
.vd-ui-page .vd-ui-page-item.vd-ui-page-active {
  background: #0099FF;
  border-color: #0099FF;
  color: #ffffff;
}
.vd-ui-page .vd-ui-page-item.vd-ui-page-item-wider {
  width: auto;
  padding: 0 15px;
}
.vd-ui-page .vd-ui-page-item:last-child {
  margin-right: 0;
}
.vd-ui-page .vd-ui-page-item .icon-app-left,
.vd-ui-page .vd-ui-page-item .icon-app-right {
  vertical-align: -2px;
  font-size: 16px;
}
.vd-ui-page .vd-ui-page-disabled {
  color: #999999;
  cursor: not-allowed;
}
.vd-ui-page .vd-ui-page-disabled:hover {
  background: #F5F7FA;
}
.vd-ui-page .vd-ui-page-omit {
  margin: 0 10px 0 5px;
  line-height: 30px;
}
.vd-ui-page .vd-ui-page-small .vd-ui-page-item {
  width: 29px;
  height: 29px;
  line-height: 27px;
}
.vd-ui-page .vd-ui-page-small .vd-ui-page-item.vd-ui-page-item-wider {
  width: auto;
  padding: 0 10px;
}
