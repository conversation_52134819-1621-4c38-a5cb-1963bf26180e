package com.wms.model.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class AddSampleOutDto {

    private Long sampleOrderId;

    private Integer applyType;

    private String activityCode;

    private String activityName;

    private Long borrowTraderId;

    private String borrowTraderName;

    private Integer traderContactId;

    private String traderContact;

    private Integer traderAddressId;

    private String traderAddress;

    private String logisticCommnet;

    private String borrowReason;

    private String domain;

    private String fileName;

    private String fileUri;

    private String[] skuNo;

    private Long[] outputNum;

    private BigDecimal[] price;

    private BigDecimal[] purchasePrice;

}
