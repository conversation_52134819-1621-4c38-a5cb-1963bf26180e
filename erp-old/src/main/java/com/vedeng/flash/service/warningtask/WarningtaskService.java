package com.vedeng.flash.service.warningtask;

import com.vedeng.flash.dto.EarlyWarningTaskDto;

import java.util.List;

public interface WarningtaskService {

    /**
     * 创建
     * @param earlyWarningTaskDto
     */
    void create(EarlyWarningTaskDto earlyWarningTaskDto);

    /**
     * 逾期超过12小时
     */
    void warningtaskOver12Hour(Long earlyWarningTaskId);

    /**
     * 逾期
     */
    void warningtaskOverdue(Long earlyWarningTaskId);

    /**
     * 关闭
     */
    void close(Long earlyWarningTaskId);

    /**
     * 批量新增
     * @param earlyWarningTaskList
     */
    void createBatch(List<EarlyWarningTaskDto> earlyWarningTaskList);

    /**
     * 批量新增
     * @param earlyWarningTaskList
     */
    void updateBatch(List<EarlyWarningTaskDto> earlyWarningTaskList);

}
