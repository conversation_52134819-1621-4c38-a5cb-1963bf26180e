package com.vedeng.price.service;

import com.vedeng.price.api.price.dto.price.FilterContractSkuResDto;
import com.vedeng.price.api.price.dto.price.PriceInfoResponseDto;
import com.vedeng.price.dto.ContractPriceInfoDetailResponseDto;
import com.vedeng.price.dto.SkuPriceInfoDetailResponseDto;

import java.util.List;

/**
 * 价格中心请求接口
 */
public interface BasePriceService {

    SkuPriceInfoDetailResponseDto findSkuPriceInfoBySkuNo(String skuNo);

    ContractPriceInfoDetailResponseDto findSkuContractInfoByCon(Integer traderId, String skuNo);

    List<PriceInfoResponseDto> batchFindPriceInfo(List<String> skuNos, Integer traderId);

    PriceInfoResponseDto findPriceInfo(String skuNo, Integer traderId);

    /**
     * 过滤客户的协议商品
     *
     * @param traderId
     * @param skuNoList
     * @return
     */
    List<FilterContractSkuResDto> listFilteredGoodsContractPrice(Integer traderId, List<String> skuNoList);

}
