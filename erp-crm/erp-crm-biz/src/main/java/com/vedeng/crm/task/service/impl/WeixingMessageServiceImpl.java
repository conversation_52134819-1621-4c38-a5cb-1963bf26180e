package com.vedeng.crm.task.service.impl;

import org.springframework.stereotype.Service;

import org.springframework.beans.factory.annotation.Autowired;

import com.vedeng.crm.task.domain.entity.WeixingMessageEntity;
import com.vedeng.crm.task.mapper.WeixingMessageMapper;
import com.vedeng.crm.task.service.WeixingMessageService;
@Service
public class WeixingMessageServiceImpl implements WeixingMessageService{

    @Autowired
    private WeixingMessageMapper weixingMessageMapper;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return weixingMessageMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(WeixingMessageEntity record) {
        return weixingMessageMapper.insert(record);
    }

    @Override
    public int insertSelective(WeixingMessageEntity record) {
        return weixingMessageMapper.insertSelective(record);
    }

    @Override
    public WeixingMessageEntity selectByPrimaryKey(Long id) {
        return weixingMessageMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(WeixingMessageEntity record) {
        return weixingMessageMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(WeixingMessageEntity record) {
        return weixingMessageMapper.updateByPrimaryKey(record);
    }

}
