package com.vedeng.erp.trader.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 客户账户表
 */
@Data
public class CustomerBankAccountDto {
    
    /**
     * 主键
     */
    @ExcelIgnore
    private Long customerBankAccountId;

    /**
     * 客户ID
     */
    @ExcelIgnore
    private Integer traderId;

    /**
     * 账户类型 1银行 2微信 3支付宝
     */
    @ExcelIgnore
    private Integer accountType;

    /**
     * 账户类型名称
     */
    @ExcelIgnore
    private String accountTypeName;

    /**
     * 账户名称
     */
    @ExcelProperty(value = "* 账户名称", index = 0)
    private String accountName;

    /**
     * 账号
     */
    @ExcelProperty(value = "* 账号", index = 1)
    private String accountNo;

    /**
     * 银行ID
     */
    @ExcelIgnore
    private Integer bankId;

    /**
     * 银行名称
     */
    @ExcelIgnore
    private String bankName;

    /**
     * 联行号
     */
    @ExcelIgnore
    private String bankNo;

    /**
     * 是否验证 0否 1是
     */
    @ExcelIgnore
    private Integer isVerify;

    /**
     * 是否验证名称
     */
    @ExcelIgnore
    private String isVerifyName;

    /**
     * 是否可用 0否 1是
     */
    @ExcelIgnore
    private Integer isEnable;

    /**
     * 客户名称
     */
    @ExcelIgnore
    private String traderName;

}
