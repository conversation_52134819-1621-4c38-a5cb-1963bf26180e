package com.newtask.broadcast;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.broadcast.statistics.project.BroadcastMonthAed;
import com.vedeng.erp.common.broadcast.param.TimePeriod;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.util.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 时间参数 params，字符串，例如： 2025-06
 * @ClassName:  BroadcastMonthAedTask   
 * @author: <PERSON><PERSON>yang
 * @date:   2025年6月18日 上午10:56:37    
 * @Copyright:
 */
@JobHandler(value = "broadcastMonthAedTask")
@Component
public class BroadcastMonthAedTask extends AbstractJobHandler {

    Logger logger = LoggerFactory.getLogger(BroadcastMonthAedTask.class);
    
    @Autowired
    private BroadcastMonthAed broadcastMonthAed;
    
    @Override
    public ReturnT<String> doExecute(String params) throws Exception {
        XxlJobLogger.log("月AED出库量播报 start-----------");
        logger.info("月AED出库量播报  start-----------");
        try {
        	
        	List<TimePeriod> timePeriodList = processDate(params);
        	if(CollectionUtils.isEmpty(timePeriodList)  && StringUtils.isEmpty(params)) {
        		broadcastMonthAed.invocation(null,true);
        	}else {
        		for (TimePeriod timePeriod : timePeriodList) {
        			broadcastMonthAed.invocation(timePeriod,false);
				}
        	}
        	
        }catch(Exception e) {
        	XxlJobLogger.log("月AED出库量播报失败");
        	logger.error("月AED出库量播报失败");
        	return FAIL;
        }
        logger.info("月AED出库量播报 end-----------");
        XxlJobLogger.log("月AED出库量播报 end-----------");
        return SUCCESS;
    }
    
    public static List<TimePeriod> processDate(String params) {
    	if(StringUtils.isEmpty(params)) {
    		return null;
    	}
        List<TimePeriod> result = new ArrayList<>();
        String[] parts = params.split("-");
        Calendar cal = Calendar.getInstance();
        // yyyy-MM格式
        if (parts.length == 2) { 
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]) - 1;
            // 获取月份天数
            cal.set(year, month, 1);
            int daysInMonth = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
            // 设置当天开始时间
            cal.set(year, month, 1, 0, 0, 0);
            Date startDate = cal.getTime();
            // 设置当月最后一天结束时间
            cal.set(year, month, daysInMonth, 23, 59, 59);
            Date EndDate = cal.getTime();
            TimePeriod period = new TimePeriod(null,null,startDate,EndDate);
            result.add(period);
        }else {
        	return null;
        }
        return result;
    }
    
    public static void main(String[] args) {
    	List<TimePeriod> result = processDate("2025-06");
    	System.out.println(JSON.toJSONString(result));
	}
}
