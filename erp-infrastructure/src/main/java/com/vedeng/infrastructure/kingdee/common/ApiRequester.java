package com.vedeng.infrastructure.kingdee.common;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.gson.Gson;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.*;
import com.vedeng.infrastructure.kingdee.common.sdk.utils.Base64Utils;
import com.vedeng.infrastructure.kingdee.common.sdk.utils.MD5Utils;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public class ApiRequester {

    K3CloudCookieStore cookieStore;
    IdentifyInfo identify;
    protected int connectTimeout = 120;
    protected int connectionRequrestTimeout = 120;
    protected int socketTimeout = 180;
    protected String uri;

    public ApiRequester(String uri) {
        this.uri = uri;
    }

    public K3CloudCookieStore getCookieStore() {
        return this.cookieStore;
    }

    public void setCookieStore(K3CloudCookieStore cookieStore) {
        this.cookieStore = cookieStore;
    }

    public IdentifyInfo getIdentify() {
        return this.identify;
    }

    public void setIdentify(IdentifyInfo identify) {
        this.identify = identify;
    }

    public int getConnectTimeout() {
        return this.connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getConnectionRequrestTimeout() {
        return this.connectionRequrestTimeout;
    }

    public void setConnectionRequrestTimeout(int connectionRequrestTimeout) {
        this.connectionRequrestTimeout = connectionRequrestTimeout;
    }

    public int getSocketTimeout() {
        return this.socketTimeout;
    }

    public void setSocketTimeout(int socketTimeout) {
        this.socketTimeout = socketTimeout;
    }

    public String postJson(RequestBodyObject requestBody) throws Exception {
        try {
            return postJson(this.uri, requestBody, this.connectTimeout, this.connectionRequrestTimeout, this.socketTimeout);
        } catch (IOException e) {
            log.error("【postJson】 处理异常", e);
            throw e;
        }
    }

    protected String postJson(String uri, RequestBodyObject json, int connectTimeout, int connectionRequestTimeout, int socketTimeout) throws Exception {
        Transaction t = Cat.newTransaction("HTTP:POST:KINGDEE", uri);

        try {
        HttpRequester req = new HttpRequester(uri, buildHeader(getUrlPath(uri)), json, connectTimeout, connectionRequestTimeout);
        log.info("调用金蝶接口请求参数[{}]", JSON.toJSONString(json));
        TimeInterval timer = DateUtil.timer();
        String body = req.post();
        log.info("调用金蝶接口uri:{},参数:{},花费时间{}", uri, JSON.toJSONString(json), timer.interval());
        log.info("调用金蝶接口，返回值[{}]", body);
        getCookieFromReq(req);
            t.setSuccessStatus();
        return body;
        } catch (Throwable e) {
            t.setStatus(e);
            Cat.logError(e);
            throw e;
        } finally {
            t.complete();
        }

    }

    protected void getCookieFromReq(HttpRequester req) {
        if (!(req.getRepoCookies() == null || this.cookieStore == null)) {
            for (Cookie c : req.getRepoCookies()) {
                this.cookieStore.getCookies().put(c.getName(), c);
                if (c.getName().equals(ConstDefine.KDSERVICE_SESSIONID)) {
                    this.cookieStore.setSID(c.getValue());
                }
            }
        }
    }

    String getUrlPath(String url) {
        if (!url.startsWith("http")) {
            return url;
        }
        int index = url.indexOf("/", 10);
        if (index > -1) {
            return url.substring(index);
        }
        return url;
    }

    public HashMap<String, String> buildHeader(String path) {
        HashMap<String, String> header = new HashMap<>();
        try {
            if (this.identify != null) {
                String apigwId = "";
                String apigwSec = "";
                String[] arr = this.identify.getAppId().split("_");
                if (arr.length == 2) {
                    apigwId = arr[0];
                    apigwSec = decodeSec(arr[1]);
                }
                header.put(ConstDefine.X_Api_ClientID, apigwId);
                header.put(ConstDefine.X_Api_Auth_Version, "2.0");
                Timestamp ts = new Timestamp(System.currentTimeMillis());
                String tsVal = Long.toString(ts.getTime());
                header.put(ConstDefine.X_Api_Timestamp, tsVal);
                String nonceVal = Long.toString(ts.getTime());
                header.put(ConstDefine.X_Api_Nonce, nonceVal);
                header.put(ConstDefine.X_Api_Signheaders, "X-Api-TimeStamp,X-Api-Nonce");
                header.put(ConstDefine.X_Api_Signature, MD5Utils.hashMAC(String.format("POST\n%s\n\nx-api-nonce:%s\nx-api-timestamp:%s\n", URLEncoder.encode(path, "UTF-8"), nonceVal, tsVal), apigwSec));
                header.put(ConstDefine.X_KD_AppKey, this.identify.getAppId());
                String data = String.format("%s,%s,%s,%s", this.identify.getdCID(), this.identify.getUserName(), this.identify.getlCID(), this.identify.getOrgNum());
                header.put(ConstDefine.X_KD_AppData, Base64Utils.encodingToBase64(data.getBytes("UTF-8")));
                header.put(ConstDefine.X_KD_Signature, MD5Utils.hashMAC(this.identify.getAppId() + data, this.identify.getAppSecret()));
            }
            if (this.cookieStore != null) {
                if (this.cookieStore.getSID() != null) {
                    header.put(ConstDefine.X_KD_SID, this.cookieStore.getSID());
                }
                if (this.cookieStore.getCookies().size() > 0) {
                    String cookieHD = String.format("Theme=standard", new Object[0]);
                    Iterator<Map.Entry<String, Cookie>> it = this.cookieStore.getCookies().entrySet().iterator();
                    while (it.hasNext()) {
                        cookieHD = cookieHD + "; " + it.next().getValue().toString();
                    }
                    header.put("Cookie", cookieHD);
                }
            }
        } catch (Exception e) {
            log.error("【buildHeader】处理异常", e);
        }
        log.info("调用金蝶接口，请求头信息[{}]", new Gson().toJson(header));
        return header;
    }

    String decodeSec(String sec) {
        if (sec.length() == 32) {
            return Base64Utils.encodingToBase64(xOrSec(Base64Utils.decodingFromBase64(sec)));
        }
        return sec;
    }

    byte[] xOrSec(byte[] buffer) {
        byte[] pwd = null;
        try {
            pwd = "0054f397c6234378b09ca7d3e5debce7".getBytes("UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error("【xOrSec】处理异常", e);
        }
        for (int i = 0; i < buffer.length; i++) {
            buffer[i] = (byte) (buffer[i] ^ pwd[i]);
        }
        return buffer;
    }
}
