package com.vedeng.order.service.validator.impl;

import com.vedeng.firstengage.model.RegistrationNumber;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.service.validator.dto.ManageCategoryLevel;
import com.vedeng.order.service.validator.dto.QualifyAutoAudtioException;
import com.vedeng.order.service.validator.dto.RegistrationCategory;
import org.springframework.stereotype.Service;

/**
 * match:
    若采购订单产品【"是否有注册证/备案凭证"字段为"有"，注册证类型为"进口注册证"，管理类别为"三类"】
   validator:
     该供应商须满足以下资质条件才能自动审核通过：
     1）营业执照已上传
     2）执照在有效期内
     3）医疗器械经营许可证已上传
     4）医疗器械经营许可证在有效期内
     5）该供应商的"医疗器械经营许可证详情（三类）（旧国标）"与对应三类产品的"旧国标分类的一级编码"匹配，或该供应商的"医疗器械经营许可证详情（三类）（新国标）"与对应三类产品的"新国标分类的一级编码"匹配。
     满足一个即可
     7）产品注册证在有效期内
   备注输出内容：订单中产品在供应商医疗器械经营许可证范围内
 */
@Service
public class QualifyAutoAudtioValidator5 extends AbstractQualifyAutoAudtioValidator{

    @Override
    public boolean isMatch(BuyorderGoodsVo buyOrderGoods) {

        //是否有注册证
        boolean hasRegistrationCert = super.hasRegistrationCert(buyOrderGoods.getSku());

        //获取SKU的注册证信息
        RegistrationNumber registrationNumber = super.getRegisterCertificateInfo(buyOrderGoods.getSku());

        return hasRegistrationCert
                && registrationNumber.getCategory() == RegistrationCategory.IMPORT_REGISTRATION
                && registrationNumber.getManageCategoryLevel() == ManageCategoryLevel.THIRD_CATEGORY;
    }

    @Override
    public String validator(BuyorderGoodsVo buyOrderGoods) throws QualifyAutoAudtioException {

        Integer traderId = super.getTradeInfo(buyOrderGoods).getTraderId();

        if(!super.isBusinessLicenseUpload(traderId)){
            throw new QualifyAutoAudtioException("该供应商的营业执照缺失");
        }

        if(!super.isBusinessLicenseInValidityPeriod(traderId)){
            throw new QualifyAutoAudtioException("该供应商的营业执照过期");
        }

        if(!super.isThirdCategoryUpload(traderId)){
            throw new QualifyAutoAudtioException("该供应商的医疗器械经营许可证缺失");
        }

        if(!super.isThirdCategoryInValidityPeriod(traderId)){
            throw new QualifyAutoAudtioException("该供应商的医疗器械经营许可证过期");
        }

        if(!super.isThirdCategoryMatch(traderId,buyOrderGoods.getSku())){
            throw new QualifyAutoAudtioException("该供应商的医疗器械经营许可证经营范围不满足订单中产品的国标分类");
        }

        if(!super.isProductRegisteValid(buyOrderGoods.getSku())){
            throw new QualifyAutoAudtioException("产品注册证过期");
        }

        return "订单中产品在供应商医疗器械经营许可证范围内";
    }
    
    @Override
    public boolean isMatch(Integer traderId, String skuNo) {
        //是否有注册证
        boolean hasRegistrationCert = super.hasRegistrationCert(skuNo);

        //获取SKU的注册证信息
        RegistrationNumber registrationNumber = super.getRegisterCertificateInfo(skuNo);

        return hasRegistrationCert
                && registrationNumber.getCategory() == RegistrationCategory.IMPORT_REGISTRATION
                && registrationNumber.getManageCategoryLevel() == ManageCategoryLevel.THIRD_CATEGORY;
    }

    @Override
    public String validator(Integer traderId, String skuNo) throws QualifyAutoAudtioException {
        if(!super.isBusinessLicenseUpload(traderId)){
            throw new QualifyAutoAudtioException("该供应商的营业执照缺失");
        }

        if(!super.isBusinessLicenseInValidityPeriod(traderId)){
            throw new QualifyAutoAudtioException("该供应商的营业执照过期");
        }

        if(!super.isThirdCategoryUpload(traderId)){
            throw new QualifyAutoAudtioException("该供应商的医疗器械经营许可证缺失");
        }

        if(!super.isThirdCategoryInValidityPeriod(traderId)){
            throw new QualifyAutoAudtioException("该供应商的医疗器械经营许可证过期");
        }

        if(!super.isThirdCategoryMatch(traderId, skuNo)){
            throw new QualifyAutoAudtioException("该供应商的医疗器械经营许可证经营范围不满足订单中产品的国标分类");
        }

        if(!super.isProductRegisteValid(skuNo)){
            throw new QualifyAutoAudtioException("产品注册证过期");
        }

        return "订单中产品在供应商医疗器械经营许可证范围内";
    }
}
