package com.vedeng.trader.enums;


import java.util.Arrays;
import java.util.Optional;

public enum WebAccountTypeEnum {
    PERSONAL(1,"个人"),
    DEALER(2,"经销商"),
    TERMINAL(3,"终端")
    ;
    private Integer code;
    private String desc;

    WebAccountTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code){
        Optional<WebAccountTypeEnum> any = Arrays.stream(WebAccountTypeEnum.values())
                .filter(v -> code.equals(v.code)).findAny();
        if (any.isPresent()){
            return any.get().desc;
        }
        return "";
    }

}
