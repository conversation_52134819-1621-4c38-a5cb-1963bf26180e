package com.vedeng.customerbillperiod.constant;

/**
 * @Author: daniel
 * @Date: 2021/7/26 11 39
 * @Description: 客户账期使用类型枚举类
 */
public enum CustomerBillPeriodUseTypeEnum {

    /**
     * 支付订单
     */
    PAY_ORDER(1,"支付订单"),

    /**
     * 关闭订单
     */
    CLOSE_ORDER(2,"关闭订单"),

    AFTER_SALES_RETURN(3,"售后退货"),

    REPAYMENT(4,"还款"),

    CANCEL_VALID(5,"订单撤销生效"),
    ;

    private Integer code;

    private String desc;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    CustomerBillPeriodUseTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 判断账期使用类型是否合法
     * @param code 使用类型编号
     * @return 是否合法
     */
    public static Boolean isExist(Integer code){
        for (CustomerBillPeriodUseTypeEnum item : CustomerBillPeriodUseTypeEnum.values()){
            if (item.getCode().equals(code)){
                return true;
            }
        }
        return false;
    }
}
