package com.vedeng.erp.buyorder.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * T_PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL
 * <AUTHOR>

@Data
public class PurchaseDeliveryDirectBatchDetailVo implements Serializable {
    private Integer purchaseDeliveryDirectBatchDetailId;

    /**
     * T_PURCHASE_DELIVERY_DIRECT_BATCH_INFO的主键
     */
    private Integer purchaseDeliveryDirectBatchInfoId;

    /**
     * 采购订单的物流明细ID
     */
    private Integer expressDetailId;
    
    /**
     * 物流单号
     */
    private String logisticsNo;
    
    private Integer expressId;

    /**
     * 物流发货时间
     */
    private String expressDeliveryTime;

    /**
     * 订货号
     */
    private String sku;

    /**
     * 产品名称
     */
    private String skuName;

    /**
     * 型号/规格
     */
    private String model;

    /**
     * 单位
     */
    private String unit;

    /**
     * 生产企业
     */
    private String productCompany;

    /**
     * 生产许可证号
     */
    private String productionLicence;

    /**
     * 注册证号
     */
    private String registerNumber;

    /**
     * 生产批次号/序列号
     */
    private String batchNumber;

    /**
     * 收货数量
     */
    private Integer arrivalCount;

    /**
     * WMS采购入库已作业的数量
     */
    private Integer wmsHandledArrivalCount;

    /**
     * WMS销售出库已作业的数量
     */
    private Integer wmsHandledDeliveryCount;

    /**
     * 生产日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date manufactureDateTime;

    /**
     * 失效日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date invalidDateTime;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 创建者
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Date modTime;

    /**
     * 更新者
     */
    private Integer updater;

    private Integer spuType;

    private Integer oldNum;

    /**
     * 是否时序列号，需要唯一
     */
    private Integer unionSequence;
    /**
     * 创建人员名称
     */
    private String creatorName;

    private String medicalInstrumentCatalogIncluded;

    private Integer isManageVedengCode;

    private Integer isEnableValidityPeriod;

    private static final long serialVersionUID = 1L;

}
