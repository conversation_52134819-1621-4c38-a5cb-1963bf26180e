package com.vedeng.flash.enums;

/**
 * @Description:  调整原因
 * @Author:       davis
 * @Date:         2021/5/24 上午11:03
 * @Version:      1.0
 */
public enum AdjustReasonEnum {

    REASON_ZERO(0 , "价格调整暂时供应商无法供货"),
    REASON_ONE(1, "供应商内部原因（产能调整、库存盘点）"),
    REASON_TWO(2, "内部活动"),
    REASON_THREE(3, "市场环境"),
    REASON_FOUR(4, "其他");

    AdjustReasonEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private Integer code;

    private String msg;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    /**
     * 根据编码获取编码信息
     * @param code
     * @return
     */
    public static String codeToMessage(Integer code){
        if(code == null){
            return "";
        }
        for(AdjustReasonEnum enums : AdjustReasonEnum.values()){
            if(code.equals(enums.getCode())){
                return enums.getMsg();
            }
        }
        return "";
    }

}
