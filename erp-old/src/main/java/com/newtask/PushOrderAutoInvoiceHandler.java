package com.newtask;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.vedeng.authorization.dao.OrganizationMapper;
import com.vedeng.authorization.model.Organization;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.enums.InvoiceApplyCheckRuleEnum;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.aftersale.service.AfterSalesApiService;
import com.vedeng.erp.finance.constants.FinanceConstant;
import com.vedeng.erp.finance.dto.*;
import com.vedeng.erp.finance.enums.CheckChainEnum;
import com.vedeng.erp.finance.enums.InvoiceTaxTypeEnum;
import com.vedeng.erp.finance.enums.SalesOpenInvoiceTypeEnum;
import com.vedeng.erp.finance.service.InvoiceApiService;
import com.vedeng.erp.finance.service.InvoiceApplyApiService;
import com.vedeng.erp.finance.service.InvoiceCheckApiService;
import com.vedeng.erp.saleorder.dto.OrderFinanceInfoDto;
import com.vedeng.erp.saleorder.dto.SaleOrderGoodsDetailDto;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import com.vedeng.erp.saleorder.service.SaleOrderGoodsApiService;
import com.vedeng.erp.trader.dto.TraderCustomerDto;
import com.vedeng.erp.trader.service.TraderCustomerApiService;
import com.vedeng.finance.dao.InvoiceApplyMapper;
import com.vedeng.finance.model.InvoiceApply;
import com.vedeng.finance.model.InvoiceApplyDetail;
import com.vedeng.logistics.model.ExpressDetail;
import com.vedeng.logistics.service.ExpressService;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.service.SaleorderDataService;
import com.vedeng.system.dao.SysOptionDefinitionMapper;
import com.vedeng.system.service.OrganizationService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@JobHandler(value = "pushOrderAutoInvoiceHandler")
@Component
@Slf4j
public class PushOrderAutoInvoiceHandler extends AbstractJobHandler {

    @Autowired
    private InvoiceApplyApiService invoiceApplyApiService;
    @Autowired
    private InvoiceCheckApiService invoiceCheckApiService;
    @Autowired
    private InvoiceApiService invoiceApiService;
    @Autowired
    private SaleOrderApiService saleOrderApiService;
    @Autowired
    private SaleOrderGoodsApiService saleOrderGoodsApiService;
    @Autowired
    private SaleorderDataService saleorderDataService;
    @Autowired
    private AfterSalesApiService afterSalesApiService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private ExpressService expressService;
    @Autowired
    private SysOptionDefinitionMapper sysOptionDefinitionMapper;
    @Autowired
    TraderCustomerApiService traderCustomerApiService;

    private static final Integer ADK_ORGANIZATION_ID = 125;


    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        log.info("销售订单自动生成发票申请定时任务开始执行");
        List<Organization> organizations = getOrganizationBelongToBussiness();
        int pageNum = 1;
        int pageSize = 100;
        while (true){
            Page<Object> page = new Page<>(pageNum, pageSize);
            PageInfo<SaleorderInfoDto> pageInfo = saleOrderApiService.getSaleorderCanInvoiceListPage(page, param);

            for (SaleorderInfoDto order : pageInfo.getList()) {
                try {
                    if (Objects.isNull(order) || Objects.isNull(order.getSaleorderId()) || Objects.isNull(order.getInvoiceStatus()) ||
                            StrUtil.isBlank(order.getSaleorderNo()) || Objects.isNull(order.getTraderId())) {
                        log.info("销售订单自动生成发票申请定时任务,销售订单信息为空,order:{}", order);
                        continue;
                    }
                    List<SaleOrderGoodsDetailDto> goodsList = saleOrderGoodsApiService.getBySaleorderId(order.getSaleorderId());
                    if (CollUtil.isEmpty(goodsList)) {
                        log.info("销售订单自动生成发票申请定时任务,销售订单商品为空,order:{}", order);
                        continue;
                    }
                    OrderFinanceInfoDto orderFinanceInfoDto = saleOrderApiService.getSaleorderFinanceInfo(order.getSaleorderId());
                    if (Objects.isNull(orderFinanceInfoDto)) {
                        log.info("销售订单自动生成发票申请定时任务,未查询到关联的财务信息,saleorderId:{}", order.getSaleorderId());
                        continue;
                    }
                    // 判断发票类型
                    if (!InvoiceTaxTypeEnum.getIsSpecialInvoice(order.getInvoiceType())) {
                        // 增值税普通发票
                        if (StrUtil.isBlank(orderFinanceInfoDto.getTaxNum())) {
                            TraderCustomerDto traderCustomerDto = traderCustomerApiService.getTraderCustomerAptitudeInfoByTraderId(orderFinanceInfoDto.getTraderId());
                            if (Objects.isNull(traderCustomerDto) || !ErpConstant.ONE.equals(traderCustomerDto.getAptitudeStatus())) {
                                // 税务登记号
                                log.info("销售订单自动生成发票申请定时任务,客户开票资料不全且客户资质审核状态非审核通过,无法开具增值税普通发票,orderFinanceInfoDto:{}", JSON.toJSONString(orderFinanceInfoDto));
                                continue;
                            }
                        }
                    } else if (InvoiceTaxTypeEnum.getIsSpecialInvoice(order.getInvoiceType())) {
                        // 增值税专用发票
                        // 注册地址、注册电话、开户银行、银行账号、税务登记号、一般纳税人资质
                        if (StrUtil.isBlank(orderFinanceInfoDto.getRegAddress()) || StrUtil.isBlank(orderFinanceInfoDto.getRegTel())
                                || StrUtil.isBlank(orderFinanceInfoDto.getBank()) || StrUtil.isBlank(orderFinanceInfoDto.getBankAccount())
                                || StrUtil.isBlank(orderFinanceInfoDto.getTaxNum()) || StrUtil.isBlank(orderFinanceInfoDto.getAverageTaxpayerUri())) {
                            log.info("销售订单自动生成发票申请定时任务,客户开票资料不全,无法开具增值税专用发票,orderFinanceInfoDto:{}", JSON.toJSONString(orderFinanceInfoDto));
                            continue;
                        }
                    }

                    Organization organization = organizationMapper.getOrganizationByTraderId(order.getTraderId());
                    if (Objects.isNull(organization) || Objects.isNull(organization.getOrgId())) {
                        order.setOrgId(0);
                    } else {
                        order.setOrgId(organization.getOrgId());
                    }
                    int belongType = getSaleOrderOrganizationType(order.getOrgId(), organizations);
                    BigDecimal realAmount = saleorderDataService.getRealAmount(order.getSaleorderId());
                    Map<Integer, BigDecimal> appliedAndOpenedInvoice = getAlreadyApplyTotalNums(order);
                    List<InvoiceApplyDetailDto> applyDetails = new ArrayList<>();

                    if (ErpConstant.ONE.equals(belongType)) {
                        BigDecimal appliedAmount = new BigDecimal("0.00");
                        for (SaleOrderGoodsDetailDto g : goodsList) {
                            if (Objects.isNull(g) || Objects.isNull(g.getSaleorderGoodsId()) ||
                                    Objects.isNull(g.getPrice()) || BigDecimal.ZERO.compareTo(g.getPrice()) >= 0) {
                                log.info("销售订单自动生成发票申请定时任务,销售订单商品信息为空,goods:{}", g);
                                continue;
                            }

                            BigDecimal appliedNum = BigDecimal.ZERO;
                            if (Objects.nonNull(appliedAndOpenedInvoice) && Objects.nonNull(appliedAndOpenedInvoice.get(g.getSaleorderGoodsId()))) {
                                appliedNum = appliedAndOpenedInvoice.get(g.getSaleorderGoodsId());
                            }

                            g.setNum(g.getNum() - afterSalesApiService.getPurchaseAfterSalesGoodsNum(g.getSaleorderGoodsId()));
                            appliedAmount = getAppliedAmount(order, appliedAmount, g, appliedNum);
                            if (new BigDecimal(g.getNum()).compareTo(appliedNum) > 0) {
                                BigDecimal applyNum = new BigDecimal(g.getNum()).subtract(appliedNum);
                                if (applyNum.compareTo(new BigDecimal("1")) >= 0) {
                                    InvoiceApplyDetailDto applyDetail = new InvoiceApplyDetailDto();
                                    applyDetail.setNum(applyNum);
                                    applyDetail.setDetailgoodsId(g.getSaleorderGoodsId());
                                    applyDetail.setPrice(g.getPrice());
                                    if (isCouponsType(order.getOrderType())) {
                                        applyDetail.setTotalAmount(g.getMaxSkuRefundAmount().subtract(appliedNum.multiply(g.getPrice())));
                                    } else {
                                        applyDetail.setTotalAmount(applyDetail.getPrice().multiply(new BigDecimal(applyNum + "")));
                                    }
                                    applyDetail.setProductName(g.getGoodsName());
                                    applyDetail.setSpecModel(getSpecModel(g.getSaleorderGoodsId()));
                                    applyDetail.setUnit(g.getUnitName());
                                    applyDetail.setTaxRate(new BigDecimal(sysOptionDefinitionMapper.selectByPrimaryKey(order.getInvoiceType()).getComments()));
                                    //(含税金额*税率)/(1+税率) 保留2位小数
                                    applyDetail.setTaxAmount((applyDetail.getTotalAmount().multiply(applyDetail.getTaxRate())).divide(BigDecimal.ONE.add(applyDetail.getTaxRate()), 2, RoundingMode.HALF_UP));
                                    applyDetail.setTaxExclusiveAmount(applyDetail.getTotalAmount().subtract(applyDetail.getTaxAmount()));
                                    applyDetail.setTaxExclusivePrice(applyDetail.getTaxExclusiveAmount().divide(applyDetail.getNum(), 8, RoundingMode.HALF_UP));
                                    applyDetails.add(applyDetail);
                                }
                            }
                        }
                        if (CollUtil.isEmpty(applyDetails) || appliedAmount.compareTo(realAmount) >= 0 ||
                                realAmount.compareTo(BigDecimal.ZERO) <= 0 || isOverSaleorderAmount(order, applyDetails, realAmount)) {
                            log.info("销售订单自动生成发票申请定时任务,开票金额超额,order:{},appliedAmount:{},realAmount:{}", order, appliedAmount, realAmount);
                            continue;
                        }
                        saveInvoiceApply(order, applyDetails);
                    } else {
                        BigDecimal appliedAmount = new BigDecimal("0.00");
                        List<Integer> goodsIds = goodsList.stream().map(SaleOrderGoodsDetailDto::getSaleorderGoodsId).filter(Objects::nonNull).collect(Collectors.toList());
                        List<ExpressDetail> expresses = expressService.getSEGoodsNum(goodsIds);
                        for (SaleOrderGoodsDetailDto g : goodsList) {
                            if (Objects.isNull(g) || Objects.isNull(g.getSaleorderGoodsId()) ||
                                    Objects.isNull(g.getPrice()) || BigDecimal.ZERO.compareTo(g.getPrice()) >= 0) {
                                log.info("销售订单自动生成发票申请定时任务,销售订单商品信息为空,goods:{}", g);
                                continue;
                            }
                            BigDecimal appliedNum = BigDecimal.ZERO;
                            if (Objects.nonNull(appliedAndOpenedInvoice) && Objects.nonNull(appliedAndOpenedInvoice.get(g.getSaleorderGoodsId()))) {
                                appliedNum = appliedAndOpenedInvoice.get(g.getSaleorderGoodsId());
                            }
                            int arrivedNum = 0;
                            int deliveredNum = g.getDeliveryNum();
                            if (CollectionUtils.isNotEmpty(expresses)) {
                                for (ExpressDetail express : expresses) {
                                    if (express != null && express.getSaleOrderGoodsId() != null
                                            && express.getSaleOrderGoodsId().equals(g.getSaleorderGoodsId())) {
                                        arrivedNum = express.getArriveNum();
                                    }
                                }
                            }
                            int afterNum = afterSalesApiService.getPurchaseAfterSalesGoodsNum(g.getSaleorderGoodsId());
                            g.setNum(g.getNum() - afterNum);
                            if (g.getGoodsId().equals(127063) || g.getGoodsId().equals(251526) || g.getGoodsId().equals(256675)
                                    || g.getGoodsId().equals(253620) || g.getGoodsId().equals(251462) || g.getGoodsId().equals(140633)) {
                                if (order.getArrivalStatus() == 2) {
                                    arrivedNum = g.getNum();
                                }
                            }
                            if (arrivedNum > deliveredNum) {
                                arrivedNum = deliveredNum;
                            }
                            BigDecimal applyNum = new BigDecimal(arrivedNum).subtract(appliedNum);
                            appliedAmount = getAppliedAmount(order, appliedAmount, g, appliedNum);
                            if (applyNum.compareTo(new BigDecimal("1")) >= 0) {
                                InvoiceApplyDetailDto applyDetail = new InvoiceApplyDetailDto();
                                applyDetail.setNum(applyNum);
                                applyDetail.setDetailgoodsId(g.getSaleorderGoodsId());
                                applyDetail.setPrice(g.getPrice());
                                if(isCouponsType(order.getOrderType())) {
                                    if(arrivedNum==g.getNum()){
                                        applyDetail.setTotalAmount(g.getMaxSkuRefundAmount().subtract(applyDetail.getPrice().multiply(appliedNum)));
                                    }else{
                                        applyDetail.setTotalAmount(applyDetail.getPrice().multiply(new BigDecimal(applyNum + "")));
                                    }
                                }else{
                                    applyDetail.setTotalAmount(applyDetail.getPrice().multiply(new BigDecimal(applyNum + "")));
                                }
                                applyDetail.setProductName(g.getGoodsName());
                                applyDetail.setSpecModel(getSpecModel(g.getSaleorderGoodsId()));
                                applyDetail.setUnit(g.getUnitName());
                                applyDetail.setTaxRate(new BigDecimal(sysOptionDefinitionMapper.selectByPrimaryKey(order.getInvoiceType()).getComments()));
                                //(含税金额*税率)/(1+税率) 保留2位小数
                                applyDetail.setTaxAmount((applyDetail.getTotalAmount().multiply(applyDetail.getTaxRate())).divide(BigDecimal.ONE.add(applyDetail.getTaxRate()),2, RoundingMode.HALF_UP));
                                applyDetail.setTaxExclusiveAmount(applyDetail.getTotalAmount().subtract(applyDetail.getTaxAmount()));
                                applyDetail.setTaxExclusivePrice(applyDetail.getTaxExclusiveAmount().divide(applyDetail.getNum(),8,RoundingMode.HALF_UP));
                                applyDetails.add(applyDetail);
                            }
                        }
                        if (CollUtil.isEmpty(applyDetails) || appliedAmount.compareTo(realAmount) >= 0 ||
                                realAmount.compareTo(BigDecimal.ZERO) <= 0 || isOverSaleorderAmount(order, applyDetails, realAmount)) {
                            log.info("销售订单自动生成发票申请定时任务,开票金额超额,order:{},appliedAmount:{},realAmount:{}", order, appliedAmount, realAmount);
                            continue;
                        }
                        saveInvoiceApply(order, applyDetails);
                    }
                } catch (Exception e) {
                    log.error("销售订单自动生成发票申请定时任务,销售订单异常,order:{}", order, e);
                }
            }
            if (!pageInfo.isHasNextPage()) {
                break;
            }
            pageNum = pageInfo.getNextPage();
        }
        return ReturnT.SUCCESS;
    }

    public void saveInvoiceApply(SaleorderInfoDto order, List<InvoiceApplyDetailDto> details) {
        InvoiceApplyDto invoiceApply = new InvoiceApplyDto();
        invoiceApply.setCompanyId(ErpConstant.ONE);
        invoiceApply.setIsAuto(ErpConstant.FOUR);
        invoiceApply.setInvoiceProperty(ErpConstant.THREE);
        invoiceApply.setIsAdvance(ErpConstant.ZERO);
        invoiceApply.setRelatedId(order.getSaleorderId());
        invoiceApply.setYyValidStatus(ErpConstant.ONE);
        invoiceApply.setType(SysOptionConstant.ID_505);
        invoiceApply.setApplyMethod(ErpConstant.ONE);
        invoiceApply.setComments(order.getSaleorderNo());
        invoiceApply.setInvoiceInfoType(ErpConstant.ZERO);
        InvoiceCheckRequestDto invoiceCheckRequestDto = getInvoiceCheckRequestDto(invoiceApply, details);
        InvoiceCheckResultDto invoiceCheckResultDto = invoiceCheckApiService.applyCheck(invoiceCheckRequestDto);
        if (!invoiceCheckResultDto.getSuccess()) {
            log.info("销售订单自动生成发票申请定时任务,发票校验不通过,invoiceCheckRequestDto:{},invoiceCheckResultDto:{}", invoiceCheckRequestDto, invoiceCheckResultDto);
            return;
        }
        log.info("销售订单自动生成发票申请定时任务,发票校验通过保存发票申请,invoiceApply:{},details:{}", invoiceApply, details);
        invoiceApplyApiService.saveInvoiceApply(invoiceApply, details);
    }

    private boolean isOverSaleorderAmount(SaleorderInfoDto order, List<InvoiceApplyDetailDto> applyDetails, BigDecimal realAmount) {
        Map<Integer, BigDecimal> occupiedMap=getInvoiceOccupiedAmount(order);
        BigDecimal occupiedAmount=occupiedMap.entrySet().stream().map(Map.Entry::getValue).reduce(BigDecimal.ZERO,BigDecimal::add);
        BigDecimal currentAmount=applyDetails.stream().map(InvoiceApplyDetailDto::getTotalAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
        BigDecimal sumAmount=occupiedAmount.add(currentAmount);
        if(sumAmount.compareTo(realAmount)>0){
            return true;
        }else{
            return false;
        }
    }

    private Map<Integer, BigDecimal> getInvoiceOccupiedAmount(SaleorderInfoDto order) {
        Map<Integer, BigDecimal> invoicedMap = getInvoicedAmount(order);
        Map<Integer, BigDecimal> appliedMap = getInvoiceApplyAmount(order);
        appliedMap.entrySet().stream().forEach(
                a -> invoicedMap.entrySet().stream().filter(
                        i -> i.getKey().equals(a.getKey())).findFirst().map(
                        m -> a.setValue(a.getValue().add(m.getValue())))
        );
        return appliedMap;
    }

    private Map<Integer, BigDecimal> getInvoiceApplyAmount(SaleorderInfoDto order) {
        List<Map<String,Object>> appliedList = invoiceApiService.getAppliedTaxNum(order.getSaleorderId(), order.getSaleorderNo());;
        return appliedList.stream()
                .collect(Collectors.toMap(
                        m -> Integer.parseInt(m.get("SALEORDER_GOODS_ID").toString()),
                        m -> new BigDecimal(m.get("TOTAL_AMOUNT").toString())));
    }

    private Map<Integer, BigDecimal> getInvoicedAmount(SaleorderInfoDto order) {
        List<Map<String, Object>> invoicedList = getInvoiceDataFinally(order);
        return invoicedList.stream()
                .collect(Collectors.toMap(m -> Integer.parseInt(
                                m.get("SALEORDER_GOODS_ID").toString()),
                        m -> new BigDecimal(new BigDecimal(m.get("INVOICE_AMOUNT").toString()).stripTrailingZeros().toPlainString())));
    }

    private String getSpecModel(Integer saleorderGoodsId) {
        log.info("查询销售明细规格型号,saleorderGoodsId:{}",saleorderGoodsId);
        SaleOrderGoodsDetailDto saleOrderGoodsDetailDto = saleOrderGoodsApiService.getBySaleOrderGoodsId(saleorderGoodsId);
        Integer spuType = saleOrderGoodsDetailDto.getSpuType();
        List<Integer> typeList = Arrays.asList(316,317,318,1008);
        if ((Integer.valueOf(316).equals(spuType) || Integer.valueOf(1008).equals(spuType))
                || (!typeList.contains(spuType) && StrUtil.isNotBlank(saleOrderGoodsDetailDto.getModel()))){
            return saleOrderGoodsDetailDto.getModel();
        }else {
            return saleOrderGoodsDetailDto.getSpec();
        }
    }

    private boolean isCouponsType(Integer orderType) {
        if(orderType == null){
            return false;
        }
        if(orderType.equals(5) || orderType.equals(1)){
            return true;
        }
        return false;
    }

    private BigDecimal getAppliedAmount(SaleorderInfoDto order, BigDecimal appliedAmount, SaleOrderGoodsDetailDto g, BigDecimal appliedNum) {
        if (isCouponsType(order.getOrderType())) {
            g.setMaxSkuRefundAmount(g.getMaxSkuRefundAmount().subtract(afterSalesApiService.getPurchaseAfterSalesGoodsAmount(g.getSaleorderGoodsId())));
            if (appliedNum.compareTo(new BigDecimal(g.getNum() + "")) >= 0) {
                appliedAmount = appliedAmount.add(g.getMaxSkuRefundAmount());
            } else {
                appliedAmount = appliedAmount.add(g.getPrice().multiply(new BigDecimal(appliedNum + "")));
            }
        } else {
            appliedAmount = appliedAmount.add(g.getPrice().multiply(new BigDecimal(appliedNum + "")));
        }
        return appliedAmount;
    }

    private Map<Integer, BigDecimal> getAlreadyApplyTotalNums(SaleorderInfoDto order) {
        Map<Integer, BigDecimal> invoicedMap = getInvoicedNums(order);
        Map<Integer, BigDecimal> appliedMap = getInvoiceApplyNums(order);
        appliedMap.entrySet().stream().forEach(
                a -> invoicedMap.entrySet().stream().filter(
                        i -> i.getKey().equals(a.getKey())).findFirst().map(
                        m -> a.setValue(a.getValue().add(m.getValue())))
        );
        return appliedMap;
    }

    private Map<Integer, BigDecimal> getInvoiceApplyNums(SaleorderInfoDto order) {
        List<Map<String,Object>> appliedList = invoiceApiService.getAppliedTaxNum(order.getSaleorderId(), order.getSaleorderNo());
        return appliedList.stream()
                .collect(Collectors.toMap(
                        m -> Integer.parseInt(m.get("SALEORDER_GOODS_ID").toString()),
                        m -> new BigDecimal(m.get("APPLY_NUM").toString())));
    }

    private Map<Integer, BigDecimal> getInvoicedNums(SaleorderInfoDto order) {
        List<Map<String, Object>> invoicedList = getInvoiceDataFinally(order);
        return invoicedList.stream()
                .collect(Collectors.toMap(m -> Integer.parseInt(
                        m.get("SALEORDER_GOODS_ID").toString()),
                        m -> new BigDecimal(new BigDecimal(m.get("INVOICE_NUM").toString()).stripTrailingZeros().toPlainString())));
    }

    private List<Map<String, Object>> getInvoiceDataFinally(SaleorderInfoDto order) {
        List<Map<String, Object>> invoicedListNew = invoiceApiService.getInvoicedTaxNum(order.getSaleorderId(), order.getSaleorderNo());
        List<Map<String, Object>> invoicedListOld = invoiceApiService.getInvoicedDataOld(order.getSaleorderId(), order.getSaleorderNo());

        List<Map<String, Object>> result = Lists.newArrayList();
        invoicedListNew.stream().forEach(newData -> invoicedListOld.stream()
                .filter(oldData -> oldData.get("SALEORDER_GOODS_ID").toString().equals(newData.get("SALEORDER_GOODS_ID").toString())).findFirst()
                .map( x -> {
                    Integer saleorderGoodsId = Integer.parseInt(x.get("SALEORDER_GOODS_ID").toString());
                    BigDecimal invoicedNumOld = new BigDecimal(x.get("INVOICE_NUM").toString());
                    BigDecimal invoicedAmountOld = new BigDecimal(x.get("INVOICE_AMOUNT").toString());
                    Map<String, Object> map = new HashMap<>();
                    map.put("SALEORDER_GOODS_ID",saleorderGoodsId);
                    map.put("INVOICE_NUM",invoicedNumOld.add(new BigDecimal(newData.get("INVOICE_NUM").toString())));
                    map.put("INVOICE_AMOUNT",invoicedAmountOld.add(new BigDecimal(newData.get("INVOICE_AMOUNT").toString())));
                    result.add(map);
                    return x;
                })
        );
        return result;
    }


    private int getSaleOrderOrganizationType(Integer orgId, List<Organization> organizations) {
        if (CollectionUtils.isNotEmpty(organizations)) {
            for (Organization org : organizations) {
                if (org != null && org.getOrgId() != null && org.getOrgId().equals(orgId)) {
                    if (orgId.intValue() == ADK_ORGANIZATION_ID) {
                        return ErpConstant.THREE;
                    }
                    return ErpConstant.TWO;
                }
            }
        }
        return ErpConstant.ONE;
    }


    private List<Organization> getOrganizationBelongToBussiness() {
        Organization organization = new Organization();
        organization.setCompanyId(1);
        organization.setParentId(2);
        organization.setOrgId(40);
        organization.setOrgName("集团业务部");
        return organizationService.getOrganizationChild(organization);
    }

    private static InvoiceCheckRequestDto getInvoiceCheckRequestDto(InvoiceApplyDto invoiceApply, List<InvoiceApplyDetailDto> details) {
        InvoiceCheckRequestDto invoiceCheckRequestDto = new InvoiceCheckRequestDto();
        invoiceCheckRequestDto.setCheckChainEnum(CheckChainEnum.INVOICE_APPLY_SALES);
        invoiceCheckRequestDto.setInvoiceProperty(invoiceApply.getInvoiceProperty());
        invoiceCheckRequestDto.setType(invoiceApply.getType());
        invoiceCheckRequestDto.setRelatedId(invoiceApply.getRelatedId());
        invoiceCheckRequestDto.setInvoiceInfoType(invoiceApply.getInvoiceInfoType());
        invoiceCheckRequestDto.setInvoiceMessage("");
        invoiceCheckRequestDto.setDetailList(details.stream().map(invoiceApplyDetailDto -> {
            InvoiceCheckRequestDto.InvoiceCheckRequestDetailDto invoiceCheckRequestDetailDto = new InvoiceCheckRequestDto.InvoiceCheckRequestDetailDto();
            invoiceCheckRequestDetailDto.setDetailGoodsId(invoiceApplyDetailDto.getDetailgoodsId());
            invoiceCheckRequestDetailDto.setPrice(invoiceApplyDetailDto.getPrice());
            invoiceCheckRequestDetailDto.setNum(invoiceApplyDetailDto.getNum());
            invoiceCheckRequestDetailDto.setTotalAmount(invoiceApplyDetailDto.getTotalAmount());
            return invoiceCheckRequestDetailDto;
        }).collect(Collectors.toList()));
        return invoiceCheckRequestDto;
    }

}
