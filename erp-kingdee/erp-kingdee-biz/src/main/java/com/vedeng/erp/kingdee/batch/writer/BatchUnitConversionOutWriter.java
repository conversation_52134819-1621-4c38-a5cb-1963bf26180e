package com.vedeng.erp.kingdee.batch.writer;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.domain.entity.KingDeeStorageInEntity;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeStorageInCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeStorageInConvertor;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeStorageInMapper;
import com.vedeng.erp.kingdee.service.KingDeeStorageInApiService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @description 单位转换出库
 * <AUTHOR>
 */
@Service
@Slf4j
public class BatchUnitConversionOutWriter extends BaseWriter<KingDeeStorageInDto> {


    @Autowired
    private KingDeeStorageInApiService kingDeeStorageInApiService;

    @Override
    public void doWrite(KingDeeStorageInDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {

        log.info("单位转换出库:{}", JSON.toJSONString(dto));

        dto.setKingDeeBizEnums(KingDeeBizEnums.saveStorageIn);
        kingDeeStorageInApiService.register(dto,true);

    }


}
