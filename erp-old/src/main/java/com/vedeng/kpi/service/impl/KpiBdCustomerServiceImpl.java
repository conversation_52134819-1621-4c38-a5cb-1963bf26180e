package com.vedeng.kpi.service.impl;

import com.vedeng.kpi.model.DO.KpiDailyCountDo;
import com.vedeng.kpi.model.DTO.KpiDataQueryDto;
import com.vedeng.kpi.model.DTO.KpiUserInfoDto;
import com.vedeng.kpi.model.DTO.emptyEntity.NullKpiDataQueryDto;
import com.vedeng.kpi.service.KpiBaseService;
import com.vedeng.kpi.service.KpiBdCustomerService;
import com.vedeng.kpi.share.KpiCommonConstant;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 五行BD客户处理.
 * @jira: VDERP-2376【五行剑法】规则修改.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/7/9 10:12 上午.
 * @author: Tomcat.Hui.
 */
@Service
public class KpiBdCustomerServiceImpl extends KpiBaseService implements KpiBdCustomerService {

    @Override
    public void dataHandler(KpiUserInfoDto userInfo) {

        String kpiDateStr = getDateStr(userInfo.getKpiDateEnd());
        log.info("开始更新用户 " + userInfo.getUserIdName() + " " + kpiDateStr + " 日 "
                + KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_BDKH);

        KpiDataQueryDto query = kpiParamTransService.baseParamsToQuery(userInfo);
        KpiDailyCountDo kpiDailyCount = kpiParamTransService.baseParamsToCount(userInfo);

        query.setUserIds(kpiLoadingCache.getGroupUserIdsByUserId(query.getUserId()));

        //团队新增客户
        List<KpiDataQueryDto> groupCoList = getGroupBdNewCoCustomers();

        /** 团队失效客户数量(按userid分组)
         * 取团队下所有人的所有订单,判断每笔订单是否全部退款(存在operation = 1 &存在 operation = 3 的log),
         * 取全部退款的订单数量相加
         * */
        Map<Integer,Integer> map = kpiOrderLogMapper.getGroupBdTraderMonth(query).stream()
                .collect(Collectors.groupingBy(KpiDataQueryDto::getUserId,
                        Collectors.summingInt(KpiDataQueryDto::getIsAllReturn)));

        Integer coNums = groupCoList.stream()
                .filter(u -> u.getUserId().equals(userInfo.getUserId()))
                .findFirst().orElse(new NullKpiDataQueryDto()).getCoCustomers();

        Integer lostNums = null == map.get(userInfo.getUserId()) ? 0 : map.get(userInfo.getUserId());

        log.info("用户 " + userInfo.getUserIdName() + " " + kpiDateStr + " 日 "
                + KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_BDKH + " 新增/失效 BD客户数: " + coNums + "/" + lostNums);

        kpiDailyCount.setBdNewCustomerNum(coNums);
        kpiDailyCount.setBdLostCustomerNum(lostNums);

        kpiDailyCount.setBdCustomerScore(new BigDecimal(countCurrentUserScore(userInfo,kpiDailyCount,groupCoList,map))
                .setScale(8, RoundingMode.HALF_UP));
        insertOrUpdateCount(kpiDailyCount);
    }

    /**
     * 计算个人得分.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: 个人得分=个人的（新增BD客户数-失效BD客户数）/团队的（新增BD客户数-失效BD客户数）*加权值（加权值=该项指标的考核比例*100）.
     * @version: 1.0.
     * @date: 2020/6/9 10:49 上午.
     * @author: Tomcat.Hui.
     * @param userInfo: .
     * @param kpiDailyCount: .
     * @param groupCoList: .
     * @param groupMap: .
     * @return: java.lang.Double.
     * @throws: .
     */
    private Double countCurrentUserScore(KpiUserInfoDto userInfo, KpiDailyCountDo kpiDailyCount,
                                         List<KpiDataQueryDto> groupCoList, Map<Integer,Integer> groupMap) {

        log.info("开始计算用户 " + userInfo.getUserIdName() + " " + getDateStr(userInfo.getKpiDateEnd()) + " 日 "
                + KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_BDKH + " 得分");

        try {
            BigDecimal weight = getWeight(userInfo.getGroupId(),KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_BDKH);
            log.info("用户 " + userInfo.getUserIdName() + " " + KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_BDKH + " 所属团队 "
                    + userInfo.getGroupName() + " "
                    + getDateStr(userInfo.getKpiDateEnd()) + " 月团队权重 " + weight);

            Integer coNum = groupCoList.stream().collect(Collectors.summingInt(KpiDataQueryDto::getCoCustomers));
            Integer lostNum = groupMap.entrySet().stream().collect(Collectors.summingInt(u -> u.getValue()));

            BigDecimal aveNum = new BigDecimal(coNum - lostNum)
                    .divide(new BigDecimal(kpiLoadingCache.getGroupUserIdsByUserId(userInfo.getUserId()).size())
                            , 8,RoundingMode.HALF_UP);

            log.info("用户 " + userInfo.getUserIdName() + " " + getDateStr(userInfo.getKpiDateEnd()) + " 日 "
                    + KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_BDKH + " 团队 " + userInfo.getGroupName()
                    + " 得失BD客户数: " + coNum + "/" + lostNum + " 平均BD客户数 " + aveNum);

            Double score = 0.0;
            if (new BigDecimal(coNum - lostNum).compareTo(BigDecimal.ZERO) != 0) {
                score = new Double(kpiDailyCount.getBdNewCustomerNum() - kpiDailyCount.getBdLostCustomerNum())
                        / aveNum.doubleValue() * weight.doubleValue();
            }
            log.info("用户 " + userInfo.getUserIdName() + " " + getDateStr(userInfo.getKpiDateEnd()) + " 日 "
                    + KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_BDKH + " BD客户数得分: " + score);

            return score;
        }catch (Exception e){
            log.error("用户 " + userInfo.getUserIdName() + " " + KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_BDKH + " 计算个人得分出现异常: ",e);
            throw e;
        }
    }

    /**
     * @description: 查询团队当月新增BD客户数.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: 按照客户ID去重.
     * @version: 1.0.
     * @date: 2020/6/27 5:36 下午.
     * @author: Tomcat.Hui.
     * @param query: .
     * @return: java.util.List<com.vedeng.kpi.model.DTO.KpiDataQueryDto>.
     * @throws: .
     */
    public List<KpiDataQueryDto> getGroupBdNewCoCustomers(KpiDataQueryDto query) {
        return kpiOrderLogMapper.getGroupBdNewCustomers(query);
    }

    @Override
    public List<KpiDataQueryDto> getKpiBdLostDetail(KpiDataQueryDto query) {
        return kpiOrderLogMapper.getKpiBdLostDetail(query);
    }

    @Override
    public List<KpiDataQueryDto> getKpiBdCoDetail(KpiDataQueryDto query) {
        return kpiOrderLogMapper.getKpiBdCoDetail(query);
    }


}
