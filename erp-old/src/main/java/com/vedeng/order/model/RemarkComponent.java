package com.vedeng.order.model;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description:  备注组件实体类
 * @Author:       davis
 * @Date:         2021/4/14 下午3:01
 * @Version:      1.0
 */
@Data
public class RemarkComponent implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Integer componentId;

    /**
     * 组件父ID
     */
    private Integer parentId;

    /**
     * 组件名称
     */
    private String name;

    /**
     * 组件类型
     */
    private Integer type;

    /**
     * 组件编码
     */
    private String code;

    /**
     * 组件状态
     */
    private Integer status;

    /**
     * 是否默认
     */
    private Integer isDefault;

    /**
     * 是否需要日期
     */
    private Integer isDate;

    /**
     * 是否需要展示原因
     */
    private Integer isReason;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 添加人
     */
    private Integer creator;

    /**
     * 最近一次修改时间
     */
    private Long modTime;

    /**
     * 最近一次修改人
     */
    private Integer updater;

}
