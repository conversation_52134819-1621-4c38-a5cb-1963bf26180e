package com.vedeng.crm.task.web.controller;

import com.vedeng.common.core.annotation.MenuDesc;
import com.vedeng.common.core.base.BaseController;
import com.vedeng.common.core.base.ExceptionController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * <AUTHOR>
 * @description 商机页面跳转层
 * @date 2022/7/14 10:41
 **/
@ExceptionController
@Controller
@RequestMapping("/crm/task/profile")
@Slf4j
public class TaskController extends BaseController {


    /**
     * 任务列表
     */
    //@MenuDesc(menuValue = "C03", menuDesc = "任务列表")
    @RequestMapping(value = "/index")
    public String index() {
        return "vue/view/crm/profile/task/index";
    }


    /**
     * 任务商机
     */
    //@MenuDesc(menuValue = "C0301", menuDesc = "新增任务")
    @RequestMapping(value = "/add")
    public ModelAndView add() {
        ModelAndView view = new ModelAndView("vue/view/crm/profile/task/add");
        return view;
    }

    /**
     * 任务商机
     */
    //@MenuDesc(menuValue = "C0302", menuDesc = "编辑任务")
    @RequestMapping(value = "/edit")
    public ModelAndView edit() {
        ModelAndView view = new ModelAndView("vue/view/crm/profile/task/add");
        return view;
    }

    /**
     * 任务详情
     */
    @RequestMapping(value = "/detail")
    public String detail() {
        return "/vue/view/crm/profile/task/detail";
    }

}
