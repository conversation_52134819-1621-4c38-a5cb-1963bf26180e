package com.vedeng.erp.system.service.impl;

import com.vedeng.erp.system.domain.entity.VerifiesInfoEntity;
import com.vedeng.erp.system.dto.VerifiesInfoDto;
import com.vedeng.erp.system.mapper.VerifiesInfoEntityMapper;
import com.vedeng.erp.system.mapstruct.VerfiesInfoConvertor;
import com.vedeng.erp.system.service.VerifiesInfoApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class VerifiesInfoApiServiceImpl implements VerifiesInfoApiService {

    @Autowired
    private VerifiesInfoEntityMapper verifiesInfoEntityMapper;

    @Autowired
    private VerfiesInfoConvertor verfiesInfoConvertor;

    @Override
    public VerifiesInfoDto selectOne(VerifiesInfoDto verifiesInfoDto) {
        VerifiesInfoEntity entity = verfiesInfoConvertor.toEntity(verifiesInfoDto);
        VerifiesInfoEntity verifiesInfoEntity = verifiesInfoEntityMapper.findOneByAll(entity);
        if (Objects.isNull(verifiesInfoEntity)){
            return null;
        }
        VerifiesInfoDto result = verfiesInfoConvertor.toDto(verifiesInfoEntity);
        return result;
    }

    @Override
    public void saveVerifiesInfo(VerifiesInfoDto verifiesInfoDto) {
        VerifiesInfoEntity entity = verfiesInfoConvertor.toEntity(verifiesInfoDto);
        verifiesInfoEntityMapper.insert(entity);
    }


}
