package com.vedeng.erp.saleorder.dto;

import com.vedeng.common.core.base.BaseDto;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;

@Getter
@Setter
public class SaleorderAdditionalClauseDto extends BaseDto {

    private Long id;

    private Integer saleorderId;

    private String saleorderNo;

    private String additionalClauseNos;

    private String installationAddress;

    private String prodName103;

    private String prodName105;

    private String traderName;

    private Integer saleCityId;

    private Integer saleProvinceId;

    private String saleProvinceName;

    private String saleCityName;

    private String terminalTraderName;

    private String snCode;

    private BigDecimal prepaidReagentAmount;

    private String additionalClause;
    //合同显示
    private String additionalClauseFinalShow;
    //页面显示，有样式要求
    private java.util.List<SaleorderAdditionalClauseItemDto> additionalClauseItemShowList=new ArrayList<>();

}