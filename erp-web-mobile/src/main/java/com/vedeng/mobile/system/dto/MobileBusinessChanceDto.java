package com.vedeng.mobile.system.dto;

 import com.fasterxml.jackson.annotation.JsonFormat;
import com.vedeng.common.core.utils.validator.group.AddGroup;
import com.vedeng.common.core.utils.validator.group.UpdateGroup;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description 商机基本信息
 * @date 2022/7/12 10:43
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MobileBusinessChanceDto {

    /**
     * 主键
     */
    @NotNull(message = "商机主键不可为空", groups = {UpdateGroup.class})
    private Integer bussinessChanceId;

    /**
     * 网站询价ID
     */
    private Integer webBussinessChanceId;

    /**
     * 商机单号
     */
    private String bussinessChanceNo;

    /**
     * 网站用户ID
     */
    private Integer webAccountId;

    /**
     * ERP公司ID(T_COMPANY)
     */
    private Integer companyId;

    /**
     * 部门ID
     */
    private Integer orgId;

    /**
     * 归属ERP用户ID
     */
    private Integer userId;

    /**
     * 客户ID
     */
    private Integer traderId;

    /**
     * 客户名称
     */
    private String checkTraderName;

    /**
     * 客户地区
     */
    private String checkTraderArea;

    /**
     * 客户联系人
     */

    private String checkTraderContactName;

    /**
     * 客户联系人手机
     */

    private String checkTraderContactMobile;

    /**
     * 客户联系人电话
     */
    private String checkTraderContactTelephone;

    /**
     * 商机类型字典库
     */
    private Integer type;

    /**
     * 商机时间
     */
    private Long receiveTime;

    /**
     * 询价行为
     */
    private Integer inquiry;

    /**
     * 商机来源字典库
     */
    private Integer source;

    /**
     * 询价方式字典库
     */
    private Integer communication;

    /**
     * 询价内容
     */
    private String content;

    /**
     * 商品分类 字典库
     */
    private Integer goodsCategory;

    /**
     * 商品品牌
     */
    private String goodsBrand;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 交易者名称
     */
    //@ExcelProperty("客户名称")
    private String traderName;

    /**
     * 地区最小级ID
     */
    private Integer areaId;

    /**
     * 多级地址逗号“,”拼接（冗余字段）
     */
    private String areaIds;

    /**
     * 联系人ID
     */

    private Integer traderContactId;


    private String traderContactName;


    private String mobile;


    /**
     * 电话
     */
    //@ExcelProperty("电话")
    private String telephone;

    /**
     * 其它联系方式
     */
    private String otherContact;

    /**
     * 备注
     */
    //@ExcelProperty("备注")
    private String comments;

    /**
     * 分配时间
     */
    private Long assignTime;

    /**
     * 初次查看时间(归属人)
     */
    private Long firstViewTime;

    /**
     * 商机状态0未处理、1报价中、2已报价、3已订单、4已关闭、5未分配、6处理中、7已成单
     */
    private Integer status;

    /**
     * 审核备注(关闭原因) 字典库
     */
    private Integer statusComments;

    /**
     * 关闭原因
     */
    private String closeReason;

    /**
     * 关闭原因备注
     */
    private String closedComments;

    /**
     * 微信openID
     */
    private String wenxinOpenId;

    /**
     * 商机等级 字典库
     */
    private Integer bussinessLevel;

    /**
     * 商机阶段 字典库
     */
    private Integer bussinessStage;

    /**
     * 询价类型 字典库
     */
    private Integer enquiryType;

    /**
     * 成单机率 字典库
     */
    private Integer orderRate;

    /**
     * 预计金额 比较符号
     */
    private String amountOpt;

    /**
     * 预计金额
     */
    private BigDecimal amount;

    /**
     * 预计成单时间
     */
    private Long orderTime;

    /**
     * 预计成单时间(date类型)
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderTimeDate;

    /**
     * 商机作废原因
     */
    private Integer cancelReason;

    /**
     * 商机作废原因 字典项名称
     */
    private String cancelReasonStr;

    /**
     * 其它原因
     */
    private String otherReason;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 添加人
     */
    private Integer creator;

    /**
     * 最近一次编辑时间
     */
    private Long modTime;

    /**
     * 最近一次编辑人
     */
    private Integer updater;

    /**
     * 咨询入口字典库：商品详情页，搜索结果页、产品导航页、品牌中心、页头、右侧悬浮、专题模板
     */
    private Integer entrances;

    /**
     * '商机精准度 0无法判断 1不精准 2一般精准 3高精准'
     */
    private Integer businessChanceAccuracy;

    /**
     * '商机精准度 0无法判断 1不精准 2一般精准 3高精准 显示'
     */
    private String businessChanceAccuracyShow;

    /**
     * '是否属于科研购，用于显示不同icon
     */
    private Integer belongPlatfromByOrgAndUser;
    /**
     * 功能字典库：立即询价、立即订购、商品咨询、帮您找货、发送采购需求
     */
    private Integer functions;

    /**
     * 0：老商机，1：新商机
     */
    private Boolean isNew;

    private String productComments;

    /**
     * 合并状态，0未合并，1被合并，2合并其他的
     */
    private Integer mergeStatus;

    /**
     * 旧商机单号
     */
    private String oldChanceNo;

    /**
     * 关联的商机id
     */
    private Integer bussinessParentId;

    /**
     * 产品备注（销售）
     */
    @NotNull(message = "产品备注不可为空", groups = {AddGroup.class})
    //@ExcelProperty("*产品备注（销售）")
    private String productCommentsSale;

    /**
     * 关闭申请审核状态，0未申请审核或者，1待审核
     */
    private Boolean closeCheckStatus;

    /**
     * 商机关联,json字符串
     */
    private String bncLink;

    /**
     * 是否关联bd订单,0未关联,1已关联
     */
    private Boolean isLinkBd;

    /**
     * 最初的商机来源
     */
    private Integer originSource;

    /**
     * IM新增商机前台cookieId
     */
    private String cookieId;

    /**
     * IM新增商机前台来源链接
     */
    private String pageFrom;


    /**
     * 系统测算 商机等级
     */
    private Integer systemBusinessLevel;

    /**
     * 线索标签id：英文逗号分割的id
     */
    private String tagIds;

    private List<Integer> tagIdList;

    /**
     * 系统测算 成单机率
     */
    private Integer systemOrderRate;

    /**
     * 完整度
     */
    private Integer completion;

    /**
     * 竞争对手信息
     */
    private String competitorInformation;

    /**
     * 主管指导意见
     */
    private String supervisorGuidance;

    /**
     * 是否采购关键人 1 是 2 否 3 其他
     */
    private Integer isPolicymaker;

    /**
     * 是否采购关键人 为其他的时候
     */
    private String other;


    /**
     * 终端名称
     */
    private String terminalTraderName;


    /**
     * 终端类型 字典
     */
    private Integer terminalTraderType;

    /**
     * 采购方式 直接采购 招投标采购
     */
    private Integer purchasingType;


    /**
     * 招标时间
     */
    private Date tenderTime;

    /**
     * 采购时间 字典库
     */
    private Integer purchasingTime;


    /**
     * @ 组装对象 @
     * 沟通记录
     */
    private MobileCommunicateRecordDto communicateRecordDto;

    /**
     * 目标用户id集合
     */
    private List<Integer> userIdList;

    /**
     * 当前登录用户id，用于查询各个人的置顶信息
     */
    private Integer currentUserId;

    /**
     * 当前登录用户的职位类型
     */
    private Integer currentPositionType;

    /**
     * 归属销售名称
     */
    private String username;

    /**
     * 联系方式，包括 客户联系人、手机号、电话
     */
    private String contactWay;

    /**
     * 产品信息（包括产品信息-产品备注-总机/销售 产品信息-询价产品）
     */
    private String productInfo;

    /**
     * 商机等级类型，1：系统值 2：个人校正值 用于区分列表搜索区域组合下拉框的类型
     */
    private Integer businessLevelType;

    /**
     * 商机等级值 字典项 用于记录列表搜索区域组合下拉框的值
     */
    private Integer businessLevelValue;

    /**
     * 成单几率类型，1：系统值 2：个人校正值 用于区分列表搜索区域组合下拉框的类型
     */
    private Integer orderRateType;

    /**
     * 成单几率值 字典项 用于记录列表搜索区域组合下拉框的值
     */
    private Integer orderRateValue;


    /**
     * 下次沟通日期 开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date nextCommunicationDateStart;

    /**
     * 下次沟通日期 结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date nextCommunicationDateEnd;

    /**
     * 预计成单时间 开始
     */
    private Long orderTimeStart;

    /**
     * 预计成单时间 结束
     */
    private Long orderTimeEnd;

    /**
     * 分配时间 转商机时间 关闭时间 创建时间
     * 搜索时间
     */
    private String searchTime;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Long startTime;
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Long endTime;

    /**
     * 客户类型 字典id
     */
    private Integer customerType;

    /**
     * 客户类型 名称
     */
    private String customerTypeName;

    /**
     * 客户性质 字典id
     */
    private Integer customerNature;

    /**
     * 客户性质 名称
     */
    private String customerNatureName;

    /**
     * 商机类型 名称
     */
    private String typeName;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 询价方式 名称
     */
    private String communicationName;

    /**
     * 联系人情况 根据 是否采购关键人 判断
     */
    private String traderContactInfo;

    /**
     * 地区 省市区拼接
     */
    private String areaStr;

    /**
     * 联系地址
     */
    private String address;

    /**
     * 终端性质 名称
     */
    private String terminalTraderTypeName;

    /**
     * 商机阶段 名称
     */
    private String businessStageName;

    /**
     * 成单机率 字典值
     */
    private String orderRateStr;

    /**
     * 询价类型 名称
     */
    private String enquiryTypeName;

    /**
     * 采购方式 名称
     */
    private String purchasingTypeName;

    /**
     * 采购时间 字典项
     */
    private String purchasingTimeName;


    /**
     * 判断是否是线索转商机
     */
    private Integer businessLeadsId;

    /**
     * 是否确认客户
     */
    private Integer isConfirmTrader;


    /**
     * 地区ids
     */
    private Set<Integer> areaIdList;

    /**
     * 省ids
     */
    private Set<Integer> provinceIdList = new HashSet<>();
    /**
     * 市ids
     */
    private Set<Integer> cityIdList = new HashSet<>();
    /**
     * 县ids
     */
    private Set<Integer> countyIdList = new HashSet<>();



    /**
     * 商机来源名称
     */
    private String sourceName;

    /**
     * 咨询入口 名称
     */
    private String entranceName;

    /**
     * 功能名称
     */
    private String functionName;

    /**
     * 产品分类
     */
    private String goodsCategoryName;

    /**
     * 附件 uri
     */
    private String attachmentUri;

    /**
     * 附件 domain
     */
    private String attachmentDomain;

    /**
     * 询价行为 字典项
     */
    private String inquiryName;

    /**
     * 系统成单几率
     */
    private String systemOrderRateStr;


    /**
     * 归属人IdList
     */
    private List<Integer> belongerIdList;

    /**
     * 沟通记录筛选项
     */
    private Integer communicateState;


}
