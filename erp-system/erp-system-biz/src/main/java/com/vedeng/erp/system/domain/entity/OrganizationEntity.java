package com.vedeng.erp.system.domain.entity;

import lombok.Data;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2022/8/29 13:19
 **/
/**
    * 部门
    */
@Data
public class OrganizationEntity {
    /**
    * 部门ID
    */
    private Integer orgId;

    /**
    * 公司ID
    */
    private Integer companyId;

    /**
    * 上级部门id
    */
    private Integer parentId;

    /**
    * 部门名称
    */
    private String orgName;

    /**
    * 级别
    */
    private Integer level;

    /**
    * 部门类型同职位类型 字典库ID
    */
    private Integer type;

    /**
    * 添加时间
    */
    private Long addTime;

    /**
    * 添加人
    */
    private Integer creator;

    /**
    * 最近一次编辑时间
    */
    private Long modTime;

    /**
    * 最近一次编辑人
    */
    private Integer updater;

    /**
    * 是否删除 1删除 
    */
    private Integer isDeleted;
}