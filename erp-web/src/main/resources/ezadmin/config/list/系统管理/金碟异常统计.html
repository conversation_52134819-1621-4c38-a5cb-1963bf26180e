<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>金蝶异常统计</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="xm3iMgleisLeid33" datasource="erp-datasourcetarget" empty_show="" fixednumber="0" firstcol="numbers">
<div id="appendHead">

        <style>
            .rowButtons{
                min-width: 160px;
            }
            li{
                position: relative;
                float: left;
                padding: 10px 0;
                display: list-item;
            }
            li>a{    color: #333;
                border-right: 1px solid #ddd;
                padding: 0 10px;}
            ul{    overflow: hidden;
                list-style: none outside none;}
            /*.layui-elip{*/
            /*    white-space: normal !important;*/
            /*}*/
            .ezcall{
                color: #01AAED;
                font-size: 16px !important;
                cursor: pointer;
            }
            .utext i{
                font-size: 16px !important;
            }

        </style>

</div>

<div class="layui-fluid">
    <form class="layui-form" id="search">

        <div class=" layui-inline "><label class="layui-form-label">接口名称</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="" name="EVENT_TYPE" placeholder="" style="" alias="" jdbctype="" data="[
                {&quot;V&quot;:&quot;保存供应商 BD_Supplier&quot;,&quot;K&quot;:&quot;Supplier_save&quot;},
{&quot;V&quot;:&quot;供应商批量保存 BD_Supplier&quot;,&quot;K&quot;:&quot;Supplier_batchSave&quot;},
{&quot;V&quot;:&quot;保存付款单 AP_PAYBILL&quot;,&quot;K&quot;:&quot;PayBill_savePayBillInfo&quot;},
{&quot;V&quot;:&quot;保存支付宝付款单 AP_PAYBILL&quot;,&quot;K&quot;:&quot;PayBill_savePayBillAli&quot;},
{&quot;V&quot;:&quot;物料 BD_MATERIAL&quot;,&quot;K&quot;:&quot;Material_save&quot;},
{&quot;V&quot;:&quot;新增客户 BD_Customer&quot;,&quot;K&quot;:&quot;Customer_save&quot;},
{&quot;V&quot;:&quot;更新供应商 BD_Supplier&quot;,&quot;K&quot;:&quot;Supplier_update&quot;},
{&quot;V&quot;:&quot;更新物料 BD_MATERIAL&quot;,&quot;K&quot;:&quot;Material_update&quot;},
{&quot;V&quot;:&quot;更新客户 BD_Customer&quot;,&quot;K&quot;:&quot;Customer_update&quot;},
{&quot;V&quot;:&quot;更新进项费用普票 IV_PUREXPINV&quot;,&quot;K&quot;:&quot;IFPInvoice_update&quot;},
{&quot;V&quot;:&quot;更新进项费用专票 IV_PUREXVATIN&quot;,&quot;K&quot;:&quot;IFSInvoice_update&quot;},
{&quot;V&quot;:&quot;更新采购费用普票 IV_PURCHASEOC&quot;,&quot;K&quot;:&quot;PVPInvoice_update&quot;},
{&quot;V&quot;:&quot;更新采购费用专票 IV_PURCHASEIC&quot;,&quot;K&quot;:&quot;PVSInvoice_update&quot;},
{&quot;V&quot;:&quot;保存普通商品专票 IV_SALESIC&quot;,&quot;K&quot;:&quot;SalesVatSpecialInvoice_save&quot;},
{&quot;V&quot;:&quot;保存普通商品普票 IV_SALESOC&quot;,&quot;K&quot;:&quot;SalesVatPlainInvoice_save&quot;},
{&quot;V&quot;:&quot;保存费用商品专票 IV_SALEEXVATIN&quot;,&quot;K&quot;:&quot;OutPutFeeSpecialInvoice_save&quot;},
{&quot;V&quot;:&quot;保存费用商品普票 IV_SALEEXINV&quot;,&quot;K&quot;:&quot;OutPutFeePlainInvoice_save&quot;},
{&quot;V&quot;:&quot;保存收款流水 AR_RECEIVEBILL&quot;,&quot;K&quot;:&quot;ReceiveBill_save&quot;},
{&quot;V&quot;:&quot;保存应收单调整单 QZOK_BOS_YSTZD&quot;,&quot;K&quot;:&quot;NeedReceiveAdjust_save&quot;},
{&quot;V&quot;:&quot;保存其他出库 STK_MisDelivery&quot;,&quot;K&quot;:&quot;StorageOut_save&quot;},
{&quot;V&quot;:&quot;保存其他入库单 STK_MISCELLANEOUS&quot;,&quot;K&quot;:&quot;StorageIn_save&quot;},
{&quot;V&quot;:&quot;保存安调单 QZOK_ATHD&quot;,&quot;K&quot;:&quot;InstallServiceRecord_save&quot;},
{&quot;V&quot;:&quot;修改安调单 QZOK_ATHD&quot;,&quot;K&quot;:&quot;InstallServiceRecord_update&quot;},
{&quot;V&quot;:&quot;销售出库单 SAL_OUTSTOCK&quot;,&quot;K&quot;:&quot;SaleOutStock_save&quot;},
{&quot;V&quot;:&quot;标准(用)应收单 AR_receivable&quot;,&quot;K&quot;:&quot;ReceiveCommon_save&quot;},
{&quot;V&quot;:&quot;保存采购入库单 STK_InStock&quot;,&quot;K&quot;:&quot;PurchaseReceipt_save&quot;},
{&quot;V&quot;:&quot;保存销售结算调整单 QZOK_XSJSTZD&quot;,&quot;K&quot;:&quot;SaleSettlementAdjustment_save&quot;},
{&quot;V&quot;:&quot;保存采购退货出库单 PUR_MRB&quot;,&quot;K&quot;:&quot;PurchaseBack_save&quot;},
{&quot;V&quot;:&quot;费用应收单 AR_receivable&quot;,&quot;K&quot;:&quot;ReceiveFee_save&quot;},
{&quot;V&quot;:&quot;保存支付宝收款流水 AR_RECEIVEBILL&quot;,&quot;K&quot;:&quot;AliReceiveBill_save&quot;},
{&quot;V&quot;:&quot;保存销售退货入库单 SAL_RETURNSTOCK&quot;,&quot;K&quot;:&quot;SaleReturnStock_save&quot;},
{&quot;V&quot;:&quot;保存采购应付单 AP_Payable&quot;,&quot;K&quot;:&quot;PayCommon_save&quot;},
{&quot;V&quot;:&quot;采购专票 IV_PURCHASEIC&quot;,&quot;K&quot;:&quot;PurchaseInvoiceSpecial_save&quot;},
{&quot;V&quot;:&quot;采购普票 IV_PURCHASEOC&quot;,&quot;K&quot;:&quot;PurchaseInvoicePlain_save&quot;},
{&quot;V&quot;:&quot;采购费用应付单 AP_Payable&quot;,&quot;K&quot;:&quot;PayExpenses_save&quot;},
{&quot;V&quot;:&quot;采购费用专票 IV_PUREXVATIN&quot;,&quot;K&quot;:&quot;InPutFeeSpecialInvoice_save&quot;},
{&quot;V&quot;:&quot;采购费用普票 IV_PUREXPINV&quot;,&quot;K&quot;:&quot;InPutFeePlainInvoice_save&quot;},
{&quot;V&quot;:&quot;应付余额调整单 QZOK_BOS_YFTZD&quot;,&quot;K&quot;:&quot;NeedPay_save&quot;},
{&quot;V&quot;:&quot;保存快递签收 QZOK_KDQSD&quot;,&quot;K&quot;:&quot;ExpressReceipt_save&quot;},
{&quot;V&quot;:&quot;保存快递成本 QZOK_KDCB&quot;,&quot;K&quot;:&quot;ExpressCost_save&quot;},
{&quot;V&quot;:&quot;更新快递成本 QZOK_KDCB&quot;,&quot;K&quot;:&quot;ExpressCost_update&quot;},
{&quot;V&quot;:&quot;更新快递签收 QZOK_KDQSD&quot;,&quot;K&quot;:&quot;ExpressReceipt_update&quot;},
{&quot;V&quot;:&quot;附件上传&quot;,&quot;K&quot;:&quot;FileData_upload&quot;}
                ]" datatype="JSON" oper=""></select>
            </div>
        </div>
    </form>
    <hr class="layui-border-blue">

    <table id="table" class="layui-table" style=" width:100%">
        <thead>

        <tr id="column">
            <th name="EVENT_TYPE">异常类型</th>
            <th name="EVENT_TYPE_NAME" body="td-link" url="/ezadmin/list/list-xm3iMgleisLeidk?EVENT_TYPE=${EVENT_TYPE}" >接口名称-formId</th>
            <th name="CNT">异常数</th>
            <th type="rowbutton" id="rowbutton" style="min-width: 150px">
                <button type="single" opentype=""
                        url="/ezadmin/list/list-xm3iMgleisLeidk?EVENT_TYPE=${EVENT_TYPE}"
                        windowname="查看" name="MODEL" >查看
                </button>
            </th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td colspan="100">
                express:
                <pre id="express" class="layui-code" orderby="" groupby="">
    select
     EVENT_TYPE, count(1) AS CNT, case EVENT_TYPE
            when 'Supplier_save' then '保存供应商 BD_Supplier'
            when 'Supplier_batchSave' then '供应商批量保存 BD_Supplier'
            when 'PayBill_savePayBillInfo' then '保存付款单 AP_PAYBILL'
            when 'PayBill_savePayBillAli' then '保存支付宝付款单 AP_PAYBILL'
            when 'Material_save' then '物料 BD_MATERIAL'
            when 'Customer_save' then '新增客户 BD_Customer'
            when 'Supplier_update' then '更新供应商 BD_Supplier'
            when 'Material_update' then '更新物料 BD_MATERIAL'
            when 'Customer_update' then '更新客户 BD_Customer'
            when 'IFPInvoice_update' then '更新进项费用普票 IV_PUREXPINV'
            when 'IFSInvoice_update' then '更新进项费用专票 IV_PUREXVATIN'
            when 'PVPInvoice_update' then '更新采购费用普票 IV_PURCHASEOC'
            when 'PVSInvoice_update' then '更新采购费用专票 IV_PURCHASEIC'
            when 'SalesVatSpecialInvoice_save' then '保存普通商品专票 IV_SALESIC'
            when 'SalesVatPlainInvoice_save' then '保存普通商品普票 IV_SALESOC'
            when 'OutPutFeeSpecialInvoice_save' then '保存费用商品专票 IV_SALEEXVATIN'
            when 'OutPutFeePlainInvoice_save' then '保存费用商品普票 IV_SALEEXINV'
            when 'ReceiveBill_save' then '保存收款流水 AR_RECEIVEBILL'
            when 'NeedReceiveAdjust_save' then '保存应收单调整单 QZOK_BOS_YSTZD'
            when 'StorageOut_save' then '保存其他出库 STK_MisDelivery'
            when 'StorageIn_save' then '保存其他入库单 STK_MISCELLANEOUS'
            when 'InstallServiceRecord_save' then '保存安调单 QZOK_ATHD'
            when 'InstallServiceRecord_update' then '修改安调单 QZOK_ATHD'
            when 'SaleOutStock_save' then '销售出库单 SAL_OUTSTOCK'
            when 'ReceiveCommon_save' then '标准(用)应收单 AR_receivable'
            when 'PurchaseReceipt_save' then '保存采购入库单 STK_InStock'
            when 'SaleSettlementAdjustment_save' then '保存销售结算调整单 QZOK_XSJSTZD'
            when 'PurchaseBack_save' then '保存采购退货出库单 PUR_MRB'
            when 'ReceiveFee_save' then '费用应收单 AR_receivable'
            when 'AliReceiveBill_save' then '保存支付宝收款流水 AR_RECEIVEBILL'
            when 'SaleReturnStock_save' then '保存销售退货入库单 SAL_RETURNSTOCK'
            when 'PayCommon_save' then '保存采购应付单 AP_Payable'
            when 'PurchaseInvoiceSpecial_save' then '采购专票 IV_PURCHASEIC'
            when 'PurchaseInvoicePlain_save' then '采购普票 IV_PURCHASEOC'
            when 'PayExpenses_save' then '采购费用应付单 AP_Payable'
            when 'InPutFeeSpecialInvoice_save' then '采购费用专票 IV_PUREXVATIN'
            when 'InPutFeePlainInvoice_save' then '采购费用普票 IV_PUREXPINV'
            when 'NeedPay_save' then '应付余额调整单 QZOK_BOS_YFTZD'
            when 'ExpressReceipt_save' then '保存快递签收 QZOK_KDQSD'
            when 'ExpressCost_save' then '保存快递成本 QZOK_KDCB'
            when 'ExpressCost_update' then '更新快递成本 QZOK_KDCB'
            when 'ExpressReceipt_update' then '更新快递签收 QZOK_KDQSD'
            when 'FileData_upload' then '附件上传'
        end as EVENT_TYPE_NAME

    from
        (select * from KING_DEE_EVENT_MSG  where
        MESSAGE_STATUS = 3
        and IS_DELETE = 0   group by BUSINESS_ID,EVENT_TYPE)  a group by EVENT_TYPE

                 </pre>
            </td>
        </tr>
        </tbody>
    </table>
</div>
</body>

<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
</script>
<script>
    layui.use(function () {

    })
</script>
</html>