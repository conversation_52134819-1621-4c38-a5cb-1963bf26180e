package com.vedeng.finance.dao;


import com.vedeng.finance.model.PayApply;
import com.vedeng.finance.model.PayApplyDetail;
import com.vedeng.finance.model.vo.PayApplyDetailVo;

import java.util.List;

public interface PayApplyDetailMapper {
    int deleteByPrimaryKey(Integer payApplyDetailId);

    int insert(PayApplyDetail record);

    int insertSelective(PayApplyDetail record);

    PayApplyDetail selectByPrimaryKey(Integer payApplyDetailId);

    int updateByPrimaryKeySelective(PayApplyDetail record);

    int updateByPrimaryKey(PayApplyDetail record);

    PayApplyDetailVo getPayApplyDetailVo(PayApplyDetailVo payApplyDetailVo);

    List<PayApplyDetail> getPayApplyDetailList(PayApplyDetail payApplyDetail);

    /**
     * 迁移db
     * @param payApply
     * @return
     */
    int batchInsertApplyDetail(PayApply payApply);
}