package com.vedeng.kpi.cache;

import com.vedeng.authorization.model.User;
import com.vedeng.kpi.model.DTO.KpiGroupConfigDto;
import com.vedeng.kpi.model.DTO.KpiTeamDto;
import com.vedeng.kpi.model.DTO.KpiUserConfigDto;
import com.vedeng.kpi.model.DTO.KpiUserInfoDto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 五行缓存类.
 * @jira: VDERP-2376【五行剑法】规则修改.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/7/6 9:21 上午.
 * @author: Tomcat.Hui.
 */
public interface KpiLoadingCache {

    /**
     * 根据团队ID查询团队配置.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/6 9:22 上午.
     * @author: Tomcat.Hui.
     * @param groupId: 团队ID.
     * @return: com.vedeng.kpi.model.DTO.KpiGroupConfigDto.
     * @throws: .
     */
    KpiGroupConfigDto getGroupConfig(Integer groupId);

    /**
     * 查询所有团队配置.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: 通过查表获取所有团队ID,然后从缓存中获取,LoadingCache会判断传入的ids,
     *          去除已在缓存中存在的ID,取剩余未缓存的ID,getAll()方法中查询
     *          (getAll方法会调用被重载的loadAll方法).
     * @version: 1.0.
     * @date: 2020/7/6 9:22 上午.
     * @author: Tomcat.Hui.
     * @return: java.util.List<com.vedeng.kpi.model.DTO.KpiGroupConfigDto>.
     * @throws: .
     */
    List<KpiGroupConfigDto> getAllGroupConfig();

    KpiTeamDto getTeamConfig(Integer groupId,Integer teamId);

    /**
     * @description: 查询所有团队配置.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: 通过查表获取所有团队ID,然后从缓存中获取,LoadingCache会判断传入的ids,
     *          去除已在缓存中存在的ID,取剩余未缓存的ID,getAll()方法中查询
     *          (getAll方法会调用被重载的loadAll方法).
     * @version: 1.0.
     * @date: 2020/7/6 9:22 上午.
     * @author: Tomcat.Hui.
     * @return: java.util.List<com.vedeng.kpi.model.DTO.KpiGroupConfigDto>.
     * @throws: .
     */
    KpiUserConfigDto getUserConfig(Integer userId);

    /**
     * 获取全量用户配置.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: 通过查表获取所有用户ID, 然后从缓存中获取, LoadingCache会判断传入的ids,
     *          去除已在缓存中存在的ID,取剩余未缓存的ID,getAll()方法中查询
     *          (getAll方法会调用被重载的loadAll方法).
     * @version: 1.0.
     * @date: 2020/7/6 9:43 上午.
     * @author: Tomcat.Hui.
     * @return: java.util.List<com.vedeng.kpi.model.DTO.KpiUserConfigDto>.
     * @throws: .
     */
    List<KpiUserConfigDto> getAllUserConfig();

    /**
     * 获取指定月团队总目标.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: 为了兼容当前目标和历史月份目标,这里有个是否为当前月的判断.
     * @version: 1.0.
     * @date: 2020/7/6 9:44 上午.
     * @author: Tomcat.Hui.
     * @param groupId: .
     * @param date: .
     * @return: java.math.BigDecimal.
     * @throws: .
     */
    BigDecimal getGroupMonthGoal(Integer groupId, Date date);

    /**
     * 获取小组总目标.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/6 2:36 下午.
     * @author: Tomcat.Hui.
     * @param groupId: 团队ID.
     * @param teamId: 小组ID.
     * @param date: 查询时间.
     * @return: java.math.BigDecimal.
     * @throws: .
     */
    BigDecimal getTeamMonthGoal(Integer groupId,Integer teamId, Date date);

    /**
     * 获取用户所在团队下所有用户ID.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: 获取用户所在团队下所有小组内的所有用户ID.
     * @version: 1.0.
     * @date: 2020/7/6 9:56 上午.
     * @author: Tomcat.Hui.
     * @param userId: .
     * @return: java.util.List<java.lang.Integer>.
     * @throws: .
     */
    List<Integer> getGroupUserIdsByUserId(Integer userId);


    /**
     * 获取团队下所有用户ID.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: 逻辑同getGroupUserIdsByUserId.
     * @version: 1.0.
     * @date: 2020/7/6 10:31 上午.
     * @author: Tomcat.Hui.
     * @param groupId: 团队ID.
     * @return: java.util.List<com.vedeng.kpi.model.DTO.KpiUserConfigDto>.
     * @throws: .
     */
    List<KpiUserConfigDto> getGroupUsersByGroupId(Integer groupId);

    /**
     * 获取指定月用户目标.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: 测试环境数据历史月目标会有缺失,正是环境数据较全,这里是否为当前月判断是为了兼容当前/历史.
     * @version: 1.0.
     * @date: 2020/7/6 9:47 上午.
     * @author: Tomcat.Hui.
     * @param userId: 用户ID.
     * @param monthStr: 月份字符串 yyyy-MM-dd.
     * @return: java.math.BigDecimal.
     * @throws: .
     */
    BigDecimal getHisTargetByUser(Integer userId,String monthStr);

    /**
     * 获取团队名称.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/6 9:48 上午.
     * @author: Tomcat.Hui.
     * @param groupId: 团队id.
     * @return: java.lang.String.
     * @throws: .
     */
    String getGroupName(Integer groupId);

    /**
     * 清除所有缓存.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/6 9:49 上午.
     * @author: Tomcat.Hui.
     * @return: void.
     * @throws: .
     */
    void clearAll();

    /**
     * 清除用户和团队配置.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/6 9:49 上午.
     * @author: Tomcat.Hui.
     * @return: void.
     * @throws: .
     */
    void clearUserAndGroup();

    /**
     * 清除用户目标缓存.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/6 9:49 上午.
     * @author: Tomcat.Hui.
     * @return: void.
     * @throws: .
     */
    void KpiAmountTarget();

    /**
     * 获取当前用户所管理的用户.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/15 11:17 上午.
     * @author: Tomcat.Hui.
     * @param user: .
     * @return: java.util.List<com.vedeng.kpi.model.DTO.KpiUserConfigDto>.
     * @throw: .
     */
    List<KpiUserConfigDto> getManagerUsers(KpiUserInfoDto userInfo);
}
