package com.vedeng.erp.market.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.util.StringUtil;
import com.vedeng.erp.market.api.MarketPlanApiService;
import com.vedeng.erp.market.dao.MarketPlanExtendMapper;
import com.vedeng.erp.market.dao.MarketPlanMapper;
import com.vedeng.erp.market.dao.MarketPlanTraderExtendMapper;
import com.vedeng.erp.market.dao.MarketPlanTraderMapper;
import com.vedeng.erp.market.domain.entity.MarketPlan;
import com.vedeng.erp.market.domain.entity.MarketPlanTrader;
import com.vedeng.erp.market.domain.vo.MarketPlanTraderResponseVo;
import com.vedeng.erp.market.domain.vo.MarketPlanTraderVo;
import com.vedeng.erp.market.domain.vo.MarketPlanVo;
import com.vedeng.erp.market.service.MarketService;
import com.vedeng.system.model.Tag;
import com.vedeng.trader.model.CommunicateRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2023/9/20
 */
@Slf4j
@Service
public class MarketServiceImpl implements MarketService, MarketPlanApiService {

    @Autowired
    private MarketPlanMapper marketPlanMapper;

    @Autowired
    private MarketPlanTraderMapper marketPlanTraderMapper;

    @Autowired
    private MarketPlanTraderExtendMapper marketPlanTraderExtendMapper;

    @Autowired
    private MarketPlanExtendMapper marketPlanExtendMapper;


    @Override
    @Transactional
    public boolean handleMarketPlanFromOp(MarketPlanVo marketPlanVo){
        log.info("接收到指南针精准营销运营活动:{}",new Object[]{marketPlanVo});
        if(ObjectUtil.isEmpty(marketPlanVo.getPlanId())){//如果本次报文是活动ID与客户的关系
            dealMarketPlanTrader(marketPlanVo.getTraderList());
            return true;
        }else{
            Integer id = marketPlanVo.getPlanId();
            MarketPlan marketPlanOld = marketPlanMapper.selectByPrimaryKey(id);
            if(marketPlanOld  != null &&  ObjectUtil.equal(marketPlanVo.getPlanStatus(),marketPlanOld.getPlanStatus())) {
                log.warn("接收到前台指南针运营活动重复");
                return true;
            }
            //主键沿用前台指南针的活动ID
            MarketPlan marketPlan = new MarketPlan();
            marketPlan.setId(marketPlanVo.getPlanId());

            if(ObjectUtil.equal(2,marketPlanVo.getPlanStatus())){//1启用 2禁用
                marketPlanOld.setModTime(marketPlanVo.getModTime());
                marketPlanOld.setPlanStatus(marketPlanVo.getPlanStatus());
                marketPlanMapper.updateByPrimaryKey(marketPlanOld);
                return true;
            }

            String promotionChannelsIdListJson = marketPlanVo.getPromotionChannelsIdListJson();
            List<Integer>  promotionChannelsList = JSONArray.parseArray(promotionChannelsIdListJson,Integer.class);
            if(!promotionChannelsList.contains(1)){  //推广渠道 1销售1对1沟通 apollo 如果非1的不处理
                log.warn("接收到前台指南针运营活动不包含1销售1对1沟通，不处理：{}",new Object[]{promotionChannelsIdListJson});
                return true;
            }

            //正常保存的逻辑
            marketPlan.setPlanName(marketPlanVo.getPlanName());
            marketPlan.setPlanCreateTime(marketPlanVo.getAddTime());
            marketPlan.setPlanEndTime(marketPlanVo.getPlanEndTime());
            marketPlan.setPlanStartTime(marketPlanVo.getPlanStartTime());
            marketPlan.setContentType(marketPlanVo.getContentType());
            marketPlan.setPromotionChannels(promotionChannelsList.get(0));
            marketPlan.setPosterUrl(marketPlanVo.getPosterUrl());
            marketPlan.setMarketingScriptListJson(marketPlanVo.getMarketingScriptListJson());
            marketPlan.setPlanStatus(marketPlanVo.getPlanStatus());
            marketPlan.setCreatorId(marketPlanVo.getCreatorId());
            marketPlan.setCreatorName(marketPlanVo.getCreatorName());
            marketPlan.setAddTime(marketPlanVo.getAddTime());
            marketPlan.setUpdator(marketPlanVo.getUpdator());
            marketPlan.setModTime(marketPlanVo.getModTime());
            marketPlanMapper.insertSelective(marketPlan);
            //正常不会有明细和活动抬头一起到ERP
            dealMarketPlanTrader(marketPlanVo.getTraderList());

            return true;
        }
    }

    public void dealMarketPlanTrader(List<MarketPlanTraderVo> traderVoList){
        if(CollectionUtil.isNotEmpty(traderVoList)){
            for(MarketPlanTraderVo planTrader :traderVoList){
                MarketPlanTrader planTraderOld = marketPlanTraderMapper.findMarketPlanTrader(
                        planTrader.getTraderId(),planTrader.getTraderCustomerId(),planTrader.getPlanId());
                if(planTraderOld!=null){
                    log.warn("精准营销活动明细已存在{}", JSONObject.toJSONString(planTrader));
                    continue;
                }
                log.info("精准营销活动明细保存:{}",JSONObject.toJSONString(planTrader));
                MarketPlanTrader marketPlanTraderNew = new MarketPlanTrader();
                BeanUtil.copyProperties(planTrader,marketPlanTraderNew);
                marketPlanTraderNew.setSendMsg(0);//是否联系，0否1是
                marketPlanTraderNew.setAddTime(new Date());
                marketPlanTraderNew.setCreator(null);
                marketPlanTraderNew.setModTime(new Date());
                marketPlanTraderNew.setUpdator(null);

                marketPlanTraderMapper.insertSelective(marketPlanTraderNew);
            }
        }
    }


    @Override
    public MarketPlan queryMarketPlanByPlanId(Integer planId) {
        return marketPlanMapper.selectByPrimaryKey(planId);
    }

    @Override
    public List<MarketPlanTraderResponseVo> queryMarketPlanTraderList(Integer planId,Integer sendMsg, Integer userId, Page page ) {
        Map map = new HashMap();
        map.put("planId",planId);
        map.put("sendMsg",sendMsg);
        map.put("userId",userId);
        map.put("page",page);
        List<MarketPlanTraderResponseVo> responseVoList = marketPlanTraderExtendMapper.queryMarketPlanTraderListPage(map);
        List<CommunicateRecord> communicateRecordList = new ArrayList<>();
        for(MarketPlanTraderResponseVo vo:responseVoList){
            if(vo.getLastCommId()!=null){
                CommunicateRecord record = new CommunicateRecord();
                record.setCommunicateRecordId(vo.getLastCommId());
                record.setTraderId(vo.getTraderId());
                communicateRecordList.add(record);
            }
        }
        return responseVoList;
    }


    @Override
    public List<MarketPlan> queryMarketPlanTongqi(Integer planId, Integer userId, Integer traderId, Integer traderCustomerId) {
        return marketPlanExtendMapper.queryMarketPlanTongqi(planId,userId,traderId,traderCustomerId);
    }




    @Override
    public void updateMarketPlanTraderSendMsg(Integer traderId){

        log.info("添加沟通记录时检查客户-对应的活动沟通记录是否要更新为已沟通:{}",traderId);
        if(traderId==null){
            return;
        }
        List<Integer> list = marketPlanTraderExtendMapper.checkMarketPlanTraderSendMsg(traderId);
        log.info("添加沟通记录时检查客户-对应的活动沟通记录是否要更新为已沟通:{}",list);
        if(CollectionUtil.isNotEmpty(list)){
            Integer result = marketPlanTraderExtendMapper.updateMarketPlanTraderSendMsg(list);
            log.info("updateMarketPlanTraderSendMsg success:{}",result);
        }

    }

    @Override
    public Map<String, Integer> queryUserMarketPlanTraderList(Integer userId) {
        return marketPlanTraderExtendMapper.queryUserMarketPlanTraderList(userId);
    }
}
