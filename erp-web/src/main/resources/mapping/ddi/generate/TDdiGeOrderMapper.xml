<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.newtask.ddi.dao.generate.TDdiGeOrderMapper">
  <resultMap id="BaseResultMap" type="com.newtask.ddi.model.generate.TDdiGeOrder">
    <id column="T_DDI_SALEORDER_ID" jdbcType="INTEGER" property="tDdiSaleorderId" />
    <result column="DISTRIBUTOR" jdbcType="VARCHAR" property="distributor" />
    <result column="QUOTE_ID" jdbcType="VARCHAR" property="quoteId" />
    <result column="SALEORDER_NO" jdbcType="VARCHAR" property="saleorderNo" />
    <result column="DELIVERY_TIME" jdbcType="TIMESTAMP" property="deliveryTime" />
    <result column="GOODS_TYPE" jdbcType="VARCHAR" property="goodsType" />
    <result column="GOODS_NAME" jdbcType="VARCHAR" property="goodsName" />
    <result column="GOODS_MODEL" jdbcType="VARCHAR" property="goodsModel" />
    <result column="REGISTRATION_NUMBER" jdbcType="VARCHAR" property="registrationNumber" />
    <result column="SERIAL_NUMBER" jdbcType="VARCHAR" property="serialNumber" />
    <result column="EFFECTIVE_DAYS" jdbcType="TIMESTAMP" property="effectiveDays" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
    <result column="UNIT" jdbcType="VARCHAR" property="unit" />
    <result column="PRICE" jdbcType="DECIMAL" property="price" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="MANUFACTURER" jdbcType="VARCHAR" property="manufacturer" />
    <result column="SHIPMENT_TYPE" jdbcType="VARCHAR" property="shipmentType" />
    <result column="TRADER_CITY" jdbcType="VARCHAR" property="traderCity" />
    <result column="TRADER_ADDRESS" jdbcType="VARCHAR" property="traderAddress" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="WAREHOUSE_OUT_NO" jdbcType="VARCHAR" property="warehouseOutNo" />
    <result column="DISTRIBUTOR_ID" jdbcType="INTEGER" property="distributorId" />
    <result column="DISTRIBUTOR_NAME" jdbcType="VARCHAR" property="distributorName" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MODE_TIME" jdbcType="TIMESTAMP" property="modeTime" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
  </resultMap>
  <sql id="Base_Column_List">
    T_DDI_SALEORDER_ID, DISTRIBUTOR, QUOTE_ID, SALEORDER_NO, DELIVERY_TIME, GOODS_TYPE,
    GOODS_NAME, GOODS_MODEL, REGISTRATION_NUMBER, SERIAL_NUMBER, EFFECTIVE_DAYS, TRADER_ID,
    TRADER_NAME, NUM, UNIT, PRICE, AMOUNT, MANUFACTURER, SHIPMENT_TYPE, TRADER_CITY,
    TRADER_ADDRESS, REMARK, WAREHOUSE_OUT_NO, DISTRIBUTOR_ID, DISTRIBUTOR_NAME, ADD_TIME,
    MODE_TIME, IS_DELETE, CREATOR, UPDATER
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_DDI_SALEORDER
    where T_DDI_SALEORDER_ID = #{tDdiSaleorderId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_DDI_SALEORDER
    where T_DDI_SALEORDER_ID = #{tDdiSaleorderId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="T_DDI_SALEORDER_ID" keyProperty="tDdiSaleorderId" parameterType="com.newtask.ddi.model.DdiGeOrder" useGeneratedKeys="true">
    insert into T_DDI_SALEORDER (DISTRIBUTOR, QUOTE_ID, SALEORDER_NO,
      DELIVERY_TIME, GOODS_TYPE, GOODS_NAME,
      GOODS_MODEL, REGISTRATION_NUMBER, SERIAL_NUMBER,
      EFFECTIVE_DAYS, TRADER_ID, TRADER_NAME,
      NUM, UNIT, PRICE, AMOUNT,
      MANUFACTURER, SHIPMENT_TYPE, TRADER_CITY,
      TRADER_ADDRESS, REMARK, WAREHOUSE_OUT_NO,
      DISTRIBUTOR_ID, DISTRIBUTOR_NAME, ADD_TIME,
      MODE_TIME, IS_DELETE, CREATOR,
      UPDATER)
    values (#{distributor,jdbcType=VARCHAR}, #{quoteId,jdbcType=VARCHAR}, #{saleorderNo,jdbcType=VARCHAR},
      #{deliveryTime,jdbcType=TIMESTAMP}, #{goodsType,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR},
      #{goodsModel,jdbcType=VARCHAR}, #{registrationNumber,jdbcType=VARCHAR}, #{serialNumber,jdbcType=VARCHAR},
      #{effectiveDays,jdbcType=TIMESTAMP}, #{traderId,jdbcType=INTEGER}, #{traderName,jdbcType=VARCHAR},
      #{num,jdbcType=INTEGER}, #{unit,jdbcType=VARCHAR}, #{price,jdbcType=DECIMAL}, #{amount,jdbcType=DECIMAL},
      #{manufacturer,jdbcType=VARCHAR}, #{shipmentType,jdbcType=VARCHAR}, #{traderCity,jdbcType=VARCHAR},
      #{traderAddress,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{warehouseOutNo,jdbcType=VARCHAR},
      #{distributorId,jdbcType=INTEGER}, #{distributorName,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP},
      #{modeTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=BOOLEAN}, #{creator,jdbcType=INTEGER},
      #{updater,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="T_DDI_SALEORDER_ID" keyProperty="tDdiSaleorderId" parameterType="com.newtask.ddi.model.DdiGeOrder" useGeneratedKeys="true">
    insert into T_DDI_SALEORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="distributor != null">
        DISTRIBUTOR,
      </if>
      <if test="quoteId != null">
        QUOTE_ID,
      </if>
      <if test="saleorderNo != null">
        SALEORDER_NO,
      </if>
      <if test="deliveryTime != null">
        DELIVERY_TIME,
      </if>
      <if test="goodsType != null">
        GOODS_TYPE,
      </if>
      <if test="goodsName != null">
        GOODS_NAME,
      </if>
      <if test="goodsModel != null">
        GOODS_MODEL,
      </if>
      <if test="registrationNumber != null">
        REGISTRATION_NUMBER,
      </if>
      <if test="serialNumber != null">
        SERIAL_NUMBER,
      </if>
      <if test="effectiveDays != null">
        EFFECTIVE_DAYS,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderName != null">
        TRADER_NAME,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="unit != null">
        UNIT,
      </if>
      <if test="price != null">
        PRICE,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="manufacturer != null">
        MANUFACTURER,
      </if>
      <if test="shipmentType != null">
        SHIPMENT_TYPE,
      </if>
      <if test="traderCity != null">
        TRADER_CITY,
      </if>
      <if test="traderAddress != null">
        TRADER_ADDRESS,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="warehouseOutNo != null">
        WAREHOUSE_OUT_NO,
      </if>
      <if test="distributorId != null">
        DISTRIBUTOR_ID,
      </if>
      <if test="distributorName != null">
        DISTRIBUTOR_NAME,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modeTime != null">
        MODE_TIME,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="distributor != null">
        #{distributor,jdbcType=VARCHAR},
      </if>
      <if test="quoteId != null">
        #{quoteId,jdbcType=VARCHAR},
      </if>
      <if test="saleorderNo != null">
        #{saleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="goodsType != null">
        #{goodsType,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="goodsModel != null">
        #{goodsModel,jdbcType=VARCHAR},
      </if>
      <if test="registrationNumber != null">
        #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="effectiveDays != null">
        #{effectiveDays,jdbcType=TIMESTAMP},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderName != null">
        #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="shipmentType != null">
        #{shipmentType,jdbcType=VARCHAR},
      </if>
      <if test="traderCity != null">
        #{traderCity,jdbcType=VARCHAR},
      </if>
      <if test="traderAddress != null">
        #{traderAddress,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="warehouseOutNo != null">
        #{warehouseOutNo,jdbcType=VARCHAR},
      </if>
      <if test="distributorId != null">
        #{distributorId,jdbcType=INTEGER},
      </if>
      <if test="distributorName != null">
        #{distributorName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modeTime != null">
        #{modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.newtask.ddi.model.DdiGeOrder">
    update T_DDI_SALEORDER
    <set>
      <if test="distributor != null">
        DISTRIBUTOR = #{distributor,jdbcType=VARCHAR},
      </if>
      <if test="quoteId != null">
        QUOTE_ID = #{quoteId,jdbcType=VARCHAR},
      </if>
      <if test="saleorderNo != null">
        SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryTime != null">
        DELIVERY_TIME = #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="goodsType != null">
        GOODS_TYPE = #{goodsType,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="goodsModel != null">
        GOODS_MODEL = #{goodsModel,jdbcType=VARCHAR},
      </if>
      <if test="registrationNumber != null">
        REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        SERIAL_NUMBER = #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="effectiveDays != null">
        EFFECTIVE_DAYS = #{effectiveDays,jdbcType=TIMESTAMP},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderName != null">
        TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="unit != null">
        UNIT = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        PRICE = #{price,jdbcType=DECIMAL},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="manufacturer != null">
        MANUFACTURER = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="shipmentType != null">
        SHIPMENT_TYPE = #{shipmentType,jdbcType=VARCHAR},
      </if>
      <if test="traderCity != null">
        TRADER_CITY = #{traderCity,jdbcType=VARCHAR},
      </if>
      <if test="traderAddress != null">
        TRADER_ADDRESS = #{traderAddress,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="warehouseOutNo != null">
        WAREHOUSE_OUT_NO = #{warehouseOutNo,jdbcType=VARCHAR},
      </if>
      <if test="distributorId != null">
        DISTRIBUTOR_ID = #{distributorId,jdbcType=INTEGER},
      </if>
      <if test="distributorName != null">
        DISTRIBUTOR_NAME = #{distributorName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modeTime != null">
        MODE_TIME = #{modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where T_DDI_SALEORDER_ID = #{tDdiSaleorderId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.newtask.ddi.model.DdiGeOrder">
    update T_DDI_SALEORDER
    set DISTRIBUTOR = #{distributor,jdbcType=VARCHAR},
      QUOTE_ID = #{quoteId,jdbcType=VARCHAR},
      SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR},
      DELIVERY_TIME = #{deliveryTime,jdbcType=TIMESTAMP},
      GOODS_TYPE = #{goodsType,jdbcType=VARCHAR},
      GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      GOODS_MODEL = #{goodsModel,jdbcType=VARCHAR},
      REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR},
      SERIAL_NUMBER = #{serialNumber,jdbcType=VARCHAR},
      EFFECTIVE_DAYS = #{effectiveDays,jdbcType=TIMESTAMP},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      NUM = #{num,jdbcType=INTEGER},
      UNIT = #{unit,jdbcType=VARCHAR},
      PRICE = #{price,jdbcType=DECIMAL},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      MANUFACTURER = #{manufacturer,jdbcType=VARCHAR},
      SHIPMENT_TYPE = #{shipmentType,jdbcType=VARCHAR},
      TRADER_CITY = #{traderCity,jdbcType=VARCHAR},
      TRADER_ADDRESS = #{traderAddress,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      WAREHOUSE_OUT_NO = #{warehouseOutNo,jdbcType=VARCHAR},
      DISTRIBUTOR_ID = #{distributorId,jdbcType=INTEGER},
      DISTRIBUTOR_NAME = #{distributorName,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MODE_TIME = #{modeTime,jdbcType=TIMESTAMP},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER}
    where T_DDI_SALEORDER_ID = #{tDdiSaleorderId,jdbcType=INTEGER}
  </update>
</mapper>