package com.newtask.goods.builder;


import com.vedeng.common.constant.ErpConst;

import java.util.StringJoiner;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class StandardStorageConditionSQLStatementBuilder extends AbstractSqlBuilder {

    private String table;

    private String primaryKeyName;

    private Integer keyInInteger;

    private Integer storageConditionTemperature;

    private Float storageConditionTemperatureLowerValue;

    private Float storageConditionTemperatureUpperValue;

    private Float storageConditionHumidityLowerValue;

    private Float storageConditionHumidityUpperValue;

    private String storageConditionOthers;

    public static StandardStorageConditionSQLStatementBuilder builder() {
        return new StandardStorageConditionSQLStatementBuilder();
    }

    @Override
    public String build() {
        if (table == null || table.length() == 0) {
            throw new IllegalArgumentException("Table name is empty");
        }
        if (primaryKeyName == null || primaryKeyName.length() == 0) {
            throw new IllegalArgumentException("primary key name is empty");
        }
        if (keyInInteger == null) {
            throw new IllegalArgumentException("primary key is null");
        }


        StringJoiner columnsToUpdate = new StringJoiner(ErpConst.Symbol.COMMA);
        if (storageConditionTemperature != null) {
            columnsToUpdate.add("STORAGE_CONDITION_TEMPERATURE=" + storageConditionTemperature);
        }

        if (storageConditionTemperatureLowerValue != null) {
            columnsToUpdate.add("STORAGE_CONDITION_TEMPERATURE_LOWER_VALUE=" + storageConditionTemperatureLowerValue);
        }

        if (storageConditionTemperatureUpperValue != null) {
            columnsToUpdate.add("STORAGE_CONDITION_TEMPERATURE_UPPER_VALUE=" + storageConditionTemperatureUpperValue);
        }

        if (storageConditionHumidityLowerValue != null) {
            columnsToUpdate.add("STORAGE_CONDITION_HUMIDITY_LOWER_VALUE=" + storageConditionHumidityLowerValue);
        }

        if (storageConditionHumidityUpperValue != null) {
            columnsToUpdate.add("STORAGE_CONDITION_HUMIDITY_UPPER_VALUE=" + storageConditionHumidityUpperValue);
        }

        if (storageConditionOthers != null && storageConditionOthers.length() > 0) {
            columnsToUpdate.add("STORAGE_CONDITION_OTHERS=\"" + storageConditionOthers + "\"");
        }

        if (columnsToUpdate.length() == 0) {
            throw new IllegalArgumentException("Has not any columns need to update");
        }

        return "update " + table + "\nset " + columnsToUpdate.toString() + " \nwhere " + primaryKeyName + "=" + keyInInteger;
    }

    public StandardStorageConditionSQLStatementBuilder setTable(String table) {
        this.table = table;
        return this;
    }

    public StandardStorageConditionSQLStatementBuilder setPrimaryKeyName(String primaryKeyName) {
        this.primaryKeyName = primaryKeyName;
        return this;
    }

    public StandardStorageConditionSQLStatementBuilder setKeyInInteger(Integer keyInInteger) {
        this.keyInInteger = keyInInteger;
        return this;
    }

    public StandardStorageConditionSQLStatementBuilder setStorageConditionTemperature(Integer storageConditionTemperature) {
        this.storageConditionTemperature = storageConditionTemperature;
        return this;
    }

    public StandardStorageConditionSQLStatementBuilder setStorageConditionTemperatureLowerValue(Float storageConditionTemperatureLowerValue) {
        this.storageConditionTemperatureLowerValue = storageConditionTemperatureLowerValue;
        return this;
    }

    public StandardStorageConditionSQLStatementBuilder setStorageConditionTemperatureUpperValue(Float storageConditionTemperatureUpperValue) {
        this.storageConditionTemperatureUpperValue = storageConditionTemperatureUpperValue;
        return this;
    }

    public StandardStorageConditionSQLStatementBuilder setStorageConditionHumidityLowerValue(Float storageConditionHumidityLowerValue) {
        this.storageConditionHumidityLowerValue = storageConditionHumidityLowerValue;
        return this;
    }

    public StandardStorageConditionSQLStatementBuilder setStorageConditionHumidityUpperValue(Float storageConditionHumidityUpperValue) {
        this.storageConditionHumidityUpperValue = storageConditionHumidityUpperValue;
        return this;
    }

    public StandardStorageConditionSQLStatementBuilder setStorageConditionOthers(String storageConditionOthers) {
        this.storageConditionOthers = storageConditionOthers;
        return this;
    }


}
