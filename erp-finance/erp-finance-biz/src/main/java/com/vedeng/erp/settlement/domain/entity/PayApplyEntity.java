package com.vedeng.erp.settlement.domain.entity;

import java.math.BigDecimal;
import lombok.Data;


/**
 * 付款申请
 */
@Data
public class PayApplyEntity {
    private Integer payApplyId;

    /**
     * 公司ID
     */
    private Integer companyId;

    /**
     * 付款申请类型字典（采购、售后）
     */
    private Integer payType;

    /**
     * 关联表ID
     */
    private Integer relatedId;

    /**
     * 交易主体1对公2对私
     */
    private Integer traderSubject;

    /**
     * 交易方式字典库
     */
    private Integer traderMode;

    /**
     * 交易者名称
     */
    private String traderName;

    private BigDecimal amount;

    /**
     * 货币单位ID
     */
    private Integer currencyUnitId;

    /**
     * 开户银行
     */
    private String bank;

    /**
     * 银行帐号
     */
    private String bankAccount;

    /**
     * 开户行支付联行号
     */
    private String bankCode;

    /**
     * 银行回单备注
     */
    private String bankRemark;

    /**
     * 备注
     */
    private String comments;

    /**
     * 审核 0待审核 1通过 2不通过
     */
    private Integer validStatus;

    /**
     * 最后一次审核时间
     */
    private Long validTime;

    /**
     * 审核备注
     */
    private String validComments;

    /**
     * 审核人ID
     */
    private Integer validUserId;

    /**
     * 是否制单 0否1是
     */
    private Integer isBill;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 添加人
     */
    private Integer creator;

    /**
     * 最近一次编辑时间
     */
    private Long modTime;

    /**
     * 最近一次编辑人
     */
    private Integer updater;

    /**
     * 申请付款状态
     */
    private Integer payStatus;

    /**
     * 付款申请支付银行ID
     */
    private Integer payBanktypeId;

    /**
     * 付款申请支付开户行名称
     */
    private String payBanktypeName;

    /**
     * 往来单位类型(0-客户,1-供应商)
     */
    private Integer accountType;

}