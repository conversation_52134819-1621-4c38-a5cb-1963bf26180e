package com.vedeng.infrastructure.wxrobot.constants;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 微信机器人消息模版
 * @date 2023/9/25 15:43
 */
public class WxRobotMsgTemple {

    /**
     * 金蝶异常模版
     */
    public static final String KING_DEE_ERROR = "<font color=\"red\">金蝶异常重试达到{}次数据</font>\n消息表id:{}\n事件类型:{}\n消息体:{}\n错误信息:{}";

    /**
     * 开票失败模版
     */
    public static final String OPEN_INVOICE_FAIL_ERROR = "## <font color=\"red\">开票失败</font>\n开票申请ID:{}\n失败原因:{}";

    /**
     * 下载发票失败
     */
    public static final String DOWN_INVOICE_ERROR = "## <font color=\"red\">发票{}文件下载失败</font>\n消息表id:{}\n发票号码:{}\n失败原因:{}";

    /**
     * 生成物流附件失败
     */
    public static final String GENERATE_LOGISTICSINFO_FILE_ERROR = "## <font color=\"red\">物流单文件生成失败</font>\n物流号:{}";

    /**
     * 生成物流附件失败
     */
    public static final String ACCEPTANCE_BILL_ERROR = "## <font color=\"red\">承兑汇票无法自动核销，请手动处理！</font>\n票据号码:{}";

    public static final String SETTLEMENT_NO_ORDER = "## <font color=\"red\">客户到款无订单提醒</font>\n<@{}> 你有未完成结款的客户到款，系统未查询到已生效的销售订单，请及时建立或审核销售订单。到款信息如下：\n客户名称：{}\n银行流水：{}\n收款时间：{}\n收款金额：{}\n未结款金额：{}\n\n如建立的销售订单满足自动结款条件，20分钟内会自动结款，可前往销售订单详情页查看结款信息\n\n本消息是今日第 {} 次通知";
    public static final String SETTLEMENT_NO_ORDER_DISMISSION = "## <font color=\"red\">客户到款无订单提醒</font>\n<@{}> {}已离职，其存在未完成结款的客户到款，系统未查询到已生效的销售订单，请及时建立、审核销售订单或调整客户归属销售。到款信息如下：\n客户名称：{}\n银行流水：{}\n收款时间：{}\n收款金额：{}\n未结款金额：{}\n\n如建立的销售订单满足自动结款条件，20分钟内会自动结款，可前往销售订单详情页查看结款信息\n\n本消息是今日第 {} 次通知";
    public static final String CAN_NOT_AUTO_SETTLEMENT = "## <font color=\"red\">客户到款无可自动结款订单提醒</font>\n<@{}> 你有未完成结款的客户到款，系统未查询到满足自动结款的销售订单，请在群里及时提供销售订单号，无需回单文件。到款信息如下：\n客户名称：{}\n银行流水：{}\n收款时间：{}\n收款金额：{}\n未结款金额：{}\n\n本消息是今日第 {} 次通知，请引用该内容回复并{}";
    public static final String CAN_NOT_AUTO_SETTLEMENT_DISMISSION = "## <font color=\"red\">客户到款无可自动结款订单提醒</font>\n<@{}> {}已离职，其存在未完成结款的客户到款，系统未查询到满足自动结款的销售订单，请在群里及时提供销售订单号或调整客户归属销售。到款信息如下：\n客户名称：{}\n银行流水：{}\n收款时间：{}\n收款金额：{}\n未结款金额：{}\n\n本消息是今日第 {} 次通知，请引用该内容回复并{}";
}
