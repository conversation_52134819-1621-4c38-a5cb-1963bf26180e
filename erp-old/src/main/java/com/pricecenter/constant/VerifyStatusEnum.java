package com.pricecenter.constant;

/**
 * 订单审核状态的枚举类型
 */
public enum VerifyStatusEnum {

    ToBeImproved("待完善",0),
    Reviewing("审核中",1),
    Approved("审核通过",2),
    Reject("审核不通过",3);

    private String name;

    private int value;

    VerifyStatusEnum(String name, int value) {
        this.name = name;
        this.value = value;
    }
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public static String getNameByValue(int findValue){

        String name = "";

        for(VerifyStatusEnum enumItem : VerifyStatusEnum.values()){
            if(enumItem.getValue() == findValue){
                name = enumItem.getName();
                break;
            }
        }

        return name;
    }

    public static Integer getValueByName(String name){

        Integer value = null;

        for(VerifyStatusEnum enumItem : VerifyStatusEnum.values()){
            if(enumItem.getName().equals(name)){
                value = enumItem.getValue();
                break;
            }
        }

        return value;
    }
}
