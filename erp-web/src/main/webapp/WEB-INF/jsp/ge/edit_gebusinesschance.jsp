<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="维护GE商机" scope="application" />
<%@ include file="../common/common.jsp"%>
<script type="text/javascript" src="<%= basePath %>static/js/ge/edit_gebusinesschance.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src='<%= basePath %>static/js/region/index.js?rnd=${resourceVersionKey}'></script>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<div class="form-list  form-tips12">
    <form method="post" action="<%=basePath%>/businesschance/ge/saveeditgebusinesschance.do" id="editGeBusinessChanceForm">
        <div class="parts">
            <div class="title-container title-container-blue">
                <div class="table-title nobor">
                    基础信息
                </div>
            </div>
            <table class="table table-bordered table-striped table-condensed table-centered">
                <tbody>
                <tr>
                    <td class="table-smaller">GE商机号</td>
                    <td>${geBusinessChance.geBussinessChanceNo}</td>
                    <td class="table-smaller">报单日期</td>
                    <td><fmt:formatDate value="${geBusinessChance.addTime}" pattern="yyyy-MM-dd HH:mm"/></td>
                </tr>
                <tr>
                    <td class="table-smaller">报价单号</td>
                    <td>${geBusinessChance.quoteorderNo}</td>
                    <td class="table-smaller">商机状态</td>
                    <td>
                        <select name="businessChanceStatus" id="businessChanceStatus" style="width: 120px">
                            <option value="1" ${geBusinessChanceDetail.businessChanceStatus eq 1 ? 'selected' : ''}>跟进中</option>
                            <option value="2" ${geBusinessChanceDetail.businessChanceStatus eq 2 ? 'selected' : ''}>赢单</option>
                            <option value="3" ${geBusinessChanceDetail.businessChanceStatus eq 3 ? 'selected' : ''}>失单</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td class="table-smaller">终端医院名称</td>
                    <td>${geBusinessChance.terminalTraderName}</td>
                    <td class="table-smaller">医院性质</td>
                    <td>${geBusinessChance.hospitalType == 1 ? '公立' : (geBusinessChance.hospitalType == 2 ? '非公' : '-')}</td>
                </tr>
                <tr>
                    <td class="table-smaller">意向型号</td>
                    <td>${geBusinessChance.goodsName}</td>
                    <td class="table-smaller">所属地区-详细地址</td>
                    <td>${geBusinessChance.salesArea} ${geBusinessChance.address}</td>
                </tr>
                <tr>
                    <td class="table-smaller">商机来源</td>
                    <td>${geBusinessChance.geBusinessChanceSourceName}</td>
                    <td class="table-smaller">经销商名称</td>
                    <td>${geBusinessChance.traderName == null || geBusinessChance.traderName == '' ? '-':geBusinessChance.traderName}</td>
                </tr>
                <tr>
                    <td class="table-smaller">报单状态</td>
                    <td>
                        ${geBusinessChance.status == 1 ? '可跟进' : (geBusinessChance.status == 2 ? '不可跟进' : '-')}
                        <c:if test="${geBusinessChance.status == 2}">
                            (${geBusinessChanceFeedBack.content})
                        </c:if>
                    </td>
                </tr>
                </tbody>
    </table>
        </div>
        <div class="parts">
            <div class="title-container title-container-blue">
                <div class="table-title nobor">
                    终端信息
                </div>
            </div>
            <ul style="margin-top: 20px">
                <li>
                    <div class="form-tips">
                        <label>销售额：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" style="width: 300px" name="salesAmount" value="<fmt:formatNumber  value="${geBusinessChanceDetail.salesAmount}" pattern="0.00"/>" id="salesAmount" placeholder="请输入">&nbsp;&nbsp; 万元
                            <div id="salesAmountErrorMsg"></div>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <label>医院等级规模：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <select name="hospitalSize" id="hospitalSize" style="width: 300px;">
                                <option value="0" ${geBusinessChanceDetail.hospitalSize eq 0 ? 'selected':''}>请选择</option>
                                <c:forEach items="${geHospitalSizeSys}" var="item">
                                    <option value="${item.sysOptionDefinitionId}" ${geBusinessChanceDetail.hospitalSize eq item.sysOptionDefinitionId ? 'selected':''}>${item.title}</option>
                                </c:forEach>
                            </select>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <label>床位数：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" style="width: 300px" name="bedNum" value="${geBusinessChanceDetail.bedNum}" id="bedNum" placeholder="请输入">&nbsp;&nbsp; 张
                            <div id="bedNumErrorMsg"></div>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <label>年营收：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" style="width: 300px" name="yearInSum" value="<fmt:formatNumber  value="${geBusinessChanceDetail.yearInSum}" pattern="0.00"/>" id="yearInSum" placeholder="请输入">&nbsp;&nbsp; 万元
                            <div id="yearInSumErrorMsg"></div>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <label>CT患者量(日)：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" style="width: 300px" name="ctDailyNum" value="${geBusinessChanceDetail.ctDailyNum}" id="ctDailyNum" placeholder="请输入">
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <label>是否新建医院：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <select name="isNewHospital" id="isNewHospital" style="width: 300px">
                                <option value="" ${geBusinessChanceDetail.isNewHospital == null || geBusinessChanceDetail.isNewHospital == '' ? 'selected' : ''}>请选择</option>
                                <option value="0" ${geBusinessChanceDetail.isNewHospital eq 0 ? 'selected' : ''}>否</option>
                                <option value="1" ${geBusinessChanceDetail.isNewHospital eq 1 ? 'selected' : ''}>是</option>
                            </select>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <label>新建医院场地进度：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <select name="newHospitalPercent" id="newHospitalPercent" style="width: 300px">
                                <option value="0" ${geBusinessChanceDetail.newHospitalPercent eq 0 ? 'selected' : ''}>请选择</option>
                                <option value="1" ${geBusinessChanceDetail.newHospitalPercent eq 1 ? 'selected' : ''}>20%</option>
                                <option value="2" ${geBusinessChanceDetail.newHospitalPercent eq 2 ? 'selected' : ''}>50%</option>
                                <option value="3" ${geBusinessChanceDetail.newHospitalPercent eq 3 ? 'selected' : ''}>70%</option>
                                <option value="4" ${geBusinessChanceDetail.newHospitalPercent eq 4 ? 'selected' : ''}>100%</option>
                            </select>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <label>预计采购月份：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks" >
                            <input class="Wdate f_left input-smaller96 m0" style="width: 300px" autocomplete="off" readonly onClick="WdatePicker({dateFmt:'yyyy-MM'})"
                                   name="expectBuyTimeStr" id="expectBuyTime"
                                   value="<fmt:formatDate value="${geBusinessChanceDetail.expectBuyTime }" pattern="yyyy-MM"/>" type="text" placeholder="请选择">
                        </div>
                    </div>
                </li>
            </ul>
        </div>
        <div class="parts">
            <div class="title-container title-container-blue">
                <div class="table-title nobor">
                    竞品信息
                </div>
            </div>
            <ul style="margin-top: 20px">
                <li>
                    <div class="form-tips">
                        <label>现有竞争对手：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" style="width: 300px" name="competeName" value="${geBusinessChanceDetail.competeName}" id="competeName" placeholder="请输入">
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <label>竞争对手产品：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" style="width: 300px" name="competeSkuName" value="${geBusinessChanceDetail.competeSkuName}" id="competeSkuName" placeholder="请输入">
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <label>竞品价格：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" style="width: 300px" name="competeSkuPrice" value="<fmt:formatNumber  value="${geBusinessChanceDetail.competeSkuPrice}" pattern="0.00"/>" id="competeSkuPrice" placeholder="请输入">&nbsp;&nbsp; 万元
                        </div>
                    </div>
                </li>
            </ul>
        </div>
        <div class="parts">
            <div class="title-container title-container-blue">
                <div class="table-title nobor">
                    原有装机信息
                </div>
            </div>
            <ul style="margin-top: 20px">
                <li>
                    <div class="form-tips">
                        <label>是否新增：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <select name="isNewInstall" id="isNewInstall" style="width: 300px">
                                <option value="" ${geBusinessChanceDetail.isNewInstall == null || geBusinessChanceDetail.isNewInstall == '' ? 'selected' : ''}>请选择</option>
                                <option value="0" ${geBusinessChanceDetail.isNewInstall eq 0 ? 'selected' : ''}>否</option>
                                <option value="1" ${geBusinessChanceDetail.isNewInstall eq 1 ? 'selected' : ''}>是</option>
                            </select>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <label>原有品牌：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" style="width: 300px" name="originSkuBand" value="${geBusinessChanceDetail.originSkuBand}" id="originSkuBand" placeholder="请输入">
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <label>原有型号：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" style="width: 300px" name="originSkuModel" value="${geBusinessChanceDetail.originSkuModel}" id="originSkuModel" placeholder="请输入">
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <label>装机时间：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input class="Wdate f_left input-smaller96 m0" style="width: 300px" autocomplete="off" readonly onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})"
                                   name="installTimeStr" id="installTime"
                                   value="<fmt:formatDate value="${geBusinessChanceDetail.installTime}" pattern="yyyy-MM-dd"/>" type="text" placeholder="请选择">
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <label>销售公司：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" style="width: 300px" name="saleCompanyName" value="${geBusinessChanceDetail.saleCompanyName}" id="saleCompanyName" placeholder="请输入">
                        </div>
                    </div>
                </li>
            </ul>
        </div>
        <div class="parts">
            <div class="title-container title-container-blue">
                <div class="table-title nobor">
                    商机详细情况
                </div>
            </div>
            <ul style="margin-top: 20px">
                <li>
                    <div class="form-tips">
                        <label>赢单率：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <select name="winOrderPercent" id="winOrderPercent" style="width: 300px">
                                <option value="" ${geBusinessChanceDetail.winOrderPercent == null || geBusinessChanceDetail.winOrderPercent == '' ? 'selected' : ''}>请选择</option>
                                <option value="1" ${geBusinessChanceDetail.winOrderPercent eq 1 ? 'selected' : ''}>高</option>
                                <option value="2" ${geBusinessChanceDetail.winOrderPercent eq 2 ? 'selected' : ''}>中</option>
                                <option value="3" ${geBusinessChanceDetail.winOrderPercent eq 3 ? 'selected' : ''}>低</option>
                            </select>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <label>资金来源：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" style="width: 300px" name="moneySource" value="${geBusinessChanceDetail.moneySource}" id="moneySource" placeholder="请输入">
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <label>资金情况：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" style="width: 300px" name="moneySituation" value="${geBusinessChanceDetail.moneySituation}" id="moneySituation" placeholder="请输入">
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <label>预算/预备资金金额：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" style="width: 300px" name="prepareAmount" value="<fmt:formatNumber  value="${geBusinessChanceDetail.prepareAmount}" pattern="0.00"/>" id="prepareAmount" placeholder="请输入">&nbsp;&nbsp; 万元
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <label>采购形式：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <select name="buyType" id="buyType" style="width: 300px;">
                                <option value="0" ${geBusinessChanceDetail.buyType eq 0 ? 'selected':''}>请选择</option>
                                <c:forEach items="${geBuyTypeSys}" var="item">
                                    <option value="${item.sysOptionDefinitionId}" ${geBusinessChanceDetail.buyType eq item.sysOptionDefinitionId ? 'selected':''}>${item.title}</option>
                                </c:forEach>
                            </select>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <label>是否安排考察：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <select name="isApplyInspect" id="isApplyInspect" style="width: 300px">
                                <option value="" ${geBusinessChanceDetail.isApplyInspect == null || geBusinessChanceDetail.isApplyInspect == '' ? 'selected' : ''}>请选择</option>
                                <option value="0" ${geBusinessChanceDetail.isApplyInspect eq 0 ? 'selected' : ''}>否</option>
                                <option value="1" ${geBusinessChanceDetail.isApplyInspect eq 1 ? 'selected' : ''}>是</option>
                            </select>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <label>机房是否已经布置完成：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <select name="isEngineComplete" id="isEngineComplete" style="width: 300px">
                                <option value="" ${geBusinessChanceDetail.isEngineComplete == null || geBusinessChanceDetail.isEngineComplete == '' ? 'selected' : ''}>请选择</option>
                                <option value="0" ${geBusinessChanceDetail.isEngineComplete eq 0 ? 'selected' : ''}>否</option>
                                <option value="1" ${geBusinessChanceDetail.isEngineComplete eq 1 ? 'selected' : ''}>是</option>
                            </select>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <label>报价方案：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" style="width: 300px" name="quoteMethod" value="${geBusinessChanceDetail.quoteMethod}" id="quoteMethod" placeholder="请输入">
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <label>方案价格：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" style="width: 300px" name="quoteMethodPrice" value="<fmt:formatNumber  value="${geBusinessChanceDetail.quoteMethodPrice}" pattern="0.00"/>" id="quoteMethodPrice" placeholder="请输入">&nbsp;&nbsp; 万元
                        </div>
                    </div>
                </li>
            </ul>
        </div>
        <div class="parts">
            <div class="title-container title-container-blue">
                <div class="table-title nobor">
                    关键问题解决
                </div>
            </div>
            <ul style="margin-top: 20px">
                <li>
                    <div class="form-tips">
                        <label>项目阶段：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <select name="projectPhase" id="projectPhase" style="width: 300px;">
                                <option value="0" ${geBusinessChanceDetail.projectPhase eq 0 ? 'selected':''}>请选择</option>
                                <c:forEach items="${geProjectPhaseSys}" var="item">
                                    <option value="${item.sysOptionDefinitionId}" ${geBusinessChanceDetail.projectPhase eq item.sysOptionDefinitionId ? 'selected':''}>${item.title}</option>
                                </c:forEach>
                            </select>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <label>待解决关键事项：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" style="width: 300px" name="needComplete" value="${geBusinessChanceDetail.needComplete}" id="needComplete" placeholder="请输入">
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <label>需求时间：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input class="Wdate f_left input-smaller96 m0" style="width: 300px" autocomplete="off" readonly onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})"
                                   name="needTimeStr" id="needTime"
                                   value="<fmt:formatDate value="${geBusinessChanceDetail.needTime}" pattern="yyyy-MM-dd"/>" type="text" placeholder="请选择">
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <label>协助部门：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" style="width: 300px" name="assistDepartment" value="${geBusinessChanceDetail.assistDepartment}" id="assistDepartment" placeholder="请输入">
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <label>解决进展：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" style="width: 300px" name="projectEvolve" value="${geBusinessChanceDetail.projectEvolve}" id="projectEvolve" placeholder="请输入">
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <label>解决时间：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input class="Wdate f_left input-smaller96 m0" style="width: 300px" autocomplete="off" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})"
                                   name="evolveTimeStr" id="evolveTime"
                                   value="<fmt:formatDate value="${geBusinessChanceDetail.evolveTime}" pattern="yyyy-MM-dd"/>" readonly type="text" placeholder="请选择">
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <label>下一步计划：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" style="width: 300px" name="nextPlan" value="${geBusinessChanceDetail.nextPlan}" id="nextPlan" placeholder="请输入">
                        </div>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <label>需要支持事宜：</label>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" style="width: 300px" name="needSupport" value="${geBusinessChanceDetail.needSupport}" id="needSupport" placeholder="请输入">
                        </div>
                    </div>
                </li>
            </ul>
            <input type="hidden" name="formToken" value="${formToken}"/>
            <input type="hidden" name="geBussinessChanceId" id="geBussinessChanceId" value="${geBusinessChance.geBussinessChanceId}">
            <input type="hidden" name="terminalPartIsChange" id="terminalPartIsChange" value="${geBusinessChanceDetail.terminalPartIsChange}">
            <input type="hidden" name="competePartIsChange" id="competePartIsChange" value="${geBusinessChanceDetail.competePartIsChange}">
            <input type="hidden" name="originInstallPartIsChange" id="originInstallPartIsChange" value="${geBusinessChanceDetail.originInstallPartIsChange}">
            <input type="hidden" name="chanceDetailPartIsChange" id="chanceDetailPartIsChange" value="${geBusinessChanceDetail.chanceDetailPartIsChange}">
            <input type="hidden" name="keyDealPartIsChange" id="keyDealPartIsChange" value="${geBusinessChanceDetail.keyDealPartIsChange}">
        </div>
        <div class="add-tijiao tcenter">
            <button type="button" class="bt-bg-style bg-light-blue" onclick="geEditSubmit();">确定</button>
        </div>
        <input class="child" type="hidden" id="layerIndex">
    </form>
</div>
</body>
