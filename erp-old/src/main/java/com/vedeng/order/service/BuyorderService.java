package com.vedeng.order.service;

import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.service.BaseService;
import com.vedeng.finance.model.PayApply;
import com.vedeng.flash.dto.EarlyWarningTaskDto;
import com.vedeng.goods.model.vo.GoodsVo;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.SyncExpressDto;
import com.vedeng.logistics.model.vo.WarehouseGoodsOperateLogVo;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.BuyorderModifyApply;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.StepBar;
import com.vedeng.order.model.dto.OrderAssistantRelationDto;
import com.vedeng.order.model.dto.PurchaseContractDto;
import com.vedeng.order.model.ge.MasterSlaveList;
import com.vedeng.order.model.vo.*;
import com.vedeng.system.model.ParamsConfigValue;
import com.vedeng.system.model.vo.AddressVo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <b>Description:</b><br> 采购订单service
 * <AUTHOR>
 * @Note
 * <b>ProjectName:</b> erp
 * <br><b>PackageName:</b> com.vedeng.order.service
 * <br><b>ClassName:</b> BuyOrderService
 * <br><b>Date:</b> 2017年7月11日 上午9:14:17
 */
public interface BuyorderService extends BaseService {


	ResultInfo<?> processLogisticsFromSaleOrder(List<SyncExpressDto> syncExpressDtoList);

	/**
	 * <b>Description:</b><br> 查询采购列表分页信息
	 * @param buyorderVo
	 * @param page
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年7月11日 上午9:25:35
	 */
	Map<String, Object> getBuyorderVoPage(BuyorderVo buyorderVo,Page page);

	/**
	 * <b>Description:</b><br> 获取新增页采购订单的详情
	 * @param buyorder
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年7月19日 上午10:13:30
	 */
	BuyorderVo getAddBuyorderVoDetail(Buyorder buyorder,User user);

	/**
	 * <b>Description:</b><br> 获取采购订单的详情
	 * @param buyorder
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年7月19日 上午10:13:30
	 */
	BuyorderVo getBuyorderVoDetail(Buyorder buyorder,User user);
	/**
	 * <b>Description:</b><br> 获取采购订单的详情
	 * @param buyorder
	 * @return
	 * @Note
	 * <b>Author:</b> Cooper
	 * <br><b>Date:</b> 2017年7月19日 上午10:13:30
	 */
	BuyorderVo getBuyorderVoDetailNew(Buyorder buyorder,User user);
	/**
	 * 通过ajax后补数据
	 * <b>Description:</b><br>
	 * @param buyorder
	 * @param user
	 * @return
	 * @Note
	 * <b>Author:</b> Cooper
	 * <br><b>Date:</b> 2018年7月6日 上午8:58:04
	 */
	BuyorderVo getSaleBuyNumByAjax(Buyorder buyorder,User user);

	/**
	 * <b>Description:</b><br> 获取采购订单的详情（申请修改时用到）
	 * @param buyorder
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年7月19日 上午10:13:30
	 */
	BuyorderVo getBuyorderVoDetail(Buyorder buyorder);

	/**
	 * <b>Description:</b><br> 获取采购订单的详情异步
	 * @param buyorder
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年7月19日 上午10:13:30
	 */
	BuyorderVo getBuyorderVoDetailByAjax(Buyorder buyorder,User user);

	/**
	 * <b>Description:</b><br> 获取采购订单的详情异步
	 * @param buyorder
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年7月19日 上午10:13:30
	 */
	List<BuyorderGoodsVo> getBuyorderGoodsVoListByAjax(BuyorderVo buyorder,User user);

	/**
	 * <b>Description:</b><br> 获取出入库记录
	 * @param buyorderVo
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2018年6月1日 上午9:14:55
	 */
	List<WarehouseGoodsOperateLogVo> getWarehouseGoodsOperateLogVoListPage(BuyorderVo buyorderVo);

	/**
	 * <b>Description:</b><br> 获取采购详情中的产品信息
	 * @param buyorderVo
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2018年6月1日 上午9:14:55
	 */
	BuyorderVo getBuyorderGoodsVoListPage(BuyorderVo buyorderVo);

	/**
	 * <b>Description:</b><br> 查询待采购列表
	 * @param saleorderGoodsVo
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年7月13日 下午4:51:06
	 */
	Map<String, Object> getSaleorderGoodsVoList(GoodsVo goodsVo);

	/**
	 * <b>Description:</b><br> 查询待采购列表分页
	 * @param saleorderGoodsVo
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年7月13日 下午4:51:06
	 */
	Map<String, Object> getSaleorderGoodsVoListPage(GoodsVo goodsVo,Page page);

	Map<String, Object> getUnPurchasingOrderList(GoodsVo goodsVo, Page page);

	/**
	 * <b>Description:</b><br> 忽略待采购订单
	 * @param saleorderGoodsIds
	 * @param user
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年7月21日 下午5:23:38
	 */
	ResultInfo saveIgnore(String saleorderGoodsIds,User user);



	/**
	 * <b>Description:</b><br> 获取加入采购订单页面的列表信息
	 * @param buyorderVo
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年8月8日 上午11:21:00
	 */
	Map<String, Object> getGoodsVoList(BuyorderVo buyorderVo);

	/**
	 * <b>Description:</b><br> 保存或更新
	 * @param buyorder
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年7月11日 上午9:27:14
	 */
	ResultInfo<?> saveOrUpdateBuyorderVo(BuyorderVo buyorderVo , User user);

	/**
	 * <b>Description:</b><br> 更新采购订单
	 * @param buyorder
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年7月11日 上午9:27:14
	 */
	ResultInfo<?> saveEditBuyorderAndBuyorderGoods(Buyorder buyorder ,HttpServletRequest request);

	/**
	 * <b>Description:</b><br> 加入已存在采购订单
	 * @param buyorder
	 * @param request
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年8月9日 上午10:34:46
	 */
	ResultInfo<?> saveAddHavedBuyorder(Map<String, Object> map);

	/**
	 * <b>Description:</b><br> 查询已忽略订单列表分页信息
	 * @param saleorderGoodsVo
	 * @param page
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年7月11日 上午9:25:35
	 */
	Map<String, Object> getIgnoreSaleorderPage(SaleorderGoodsVo saleorderGoodsVo,Page page);

	/**
	 * <b>Description:</b><br> 获取普发的收货地址列表
	 * @param paramsConfigValue
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年8月22日 下午6:22:31
	 */
	List<AddressVo> getAddressVoList(ParamsConfigValue paramsConfigValue);

	/**
	 * <b>Description:</b><br> 保存付款申请
	 * @param payApply
	 * @return
	 * @Note
	 * <b>Author:</b> leo.yang
	 * <br><b>Date:</b> 2017年8月31日 上午10:46:43
	 */
	ResultInfo<?> saveApplyPayment(PayApply payApply);

	/**
	 * <b>Description:</b><br> 申请审核
	 * @param buyorder
	 * @param request
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年9月7日 上午9:31:36
	 */
	ResultInfo<?> saveApplyReview(Buyorder buyorder);

	/**
	 * <b>Description:</b><br> 关闭采购订单
	 * @param buyorder
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年9月7日 上午9:38:08
	 */
	ResultInfo<?> saveCloseBuyorder(Buyorder buyorder);

	/**
	 * <b>Description:</b><br> 根据采购订单的id获取销售订单产品的列表
	 * @param buyorder
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年10月19日 下午2:18:56
	 */
	BuyorderVo getBuyorderGoodsVoList(Buyorder buyorder);
	/**
	 * 保存采购单主表信息
	 */
	ResultInfo<?> saveBuyorder(Buyorder buyorder);

	/**
	 * <b>Description:</b><br> 获取备货 计划产品列表
	 * @param goodsVo
	 * @param page
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年11月22日 下午5:32:04
	 */
	Map<String, Object> getBHManageList(GoodsVo goodsVo, Page page);

	/**
	 * <b>Description:</b><br> 批量添加备货产品
	 * @param list
	 * @param bhSaleorder
	 * @param session
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年11月23日 下午3:35:22
	 */
	ResultInfo batchAddBhSaleorderGoods(List<Integer> list, Saleorder bhSaleorder, HttpSession session);
	ResultInfo batchAddBhSaleorderGoodsNew(List<Integer> list, Saleorder bhSaleorder, HttpSession session);
    /**
     *
     * <b>Description:</b><br> 打印采购单
     * @param buyorder
     * @return
     * @Note
     * <b>Author:</b> scott
     * <br><b>Date:</b> 2017年12月14日 上午10:52:53
     */
	BuyorderVo getBuyOrderPrintInfo(Buyorder buyorder);
	 /**
     *
     * <b>Description:</b><br> 根据销售订单产品ID集合获取采购归属人id
     * @param saleorderGoodsIds
     * @return
     * @Note
     * <b>Author:</b> Michael
     * <br><b>Date:</b> 2017年12月14日 上午10:52:53
     */
	List<Integer> getBuyorderUserBySaleorderGoodsIds(List<Integer> saleorderGoodsIds);

	/**
	 * <b>Description:</b><br> 备货计划管理分析
	 * @param goodsVo
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2018年2月10日 下午2:01:09
	 */
	Map<String, Object> getBHManageStat(GoodsVo goodsVo);
	/**
	 *
	 * <b>Description:</b><br> 获取采购入库采购订单的详情
	 * @param buyorder
	 * @param user
	 * @return
	 * @Note
	 * <b>Author:</b> Michael
	 * <br><b>Date:</b> 2018年2月11日 下午1:19:08
	 */
	BuyorderVo getBuyorderInDetail(BuyorderVo buyorder, User user);

	/**
	 * <b>Description:</b><br> 采购订单确认收货
	 * @param buyorder
	 * @param request
	 * @param id_arrivalNum
	 * @return
	 * @Note
	 * <b>Author:</b> leo.yang
	 * <br><b>Date:</b> 2018年2月11日 下午4:13:41
	 */
	Buyorder confirmArrival(Buyorder buyorder, HttpServletRequest request,String id_arrivalNum,String id_nonAllArrivalReason);

	/**
	 * <b>Description:</b><br> 根据采购单查询运费
	 * @param buyorderGoodsVo
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2018年3月6日 上午10:14:12
	 */
	BuyorderGoodsVo getFreightByBuyorderId(BuyorderGoodsVo buyorderGoodsVo);

	/**
	 * <b>Description:</b><br> 保存采购订单的运费
	 * @param buyorderGoodsVo
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2018年3月6日 上午11:01:46
	 */
	ResultInfo<?> saveBuyorderFreight(BuyorderGoodsVo buyorderGoodsVo,User user);

	/**
	 * <b>Description:</b><br> 删除采购订单的商品
	 * @param buyorderGoodsVo
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2018年3月6日 上午11:01:46
	 */
	ResultInfo<?> saveApplyBuyorderModfiyValidStatus(BuyorderModifyApply buyorderModifyApply);

	/**
	 * <b>Description:</b><br> 获取采购订单申请修改的详情
	 * @param buyorder
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年7月19日 上午10:13:30
	 */
	BuyorderVo getBuyorderVoApplyUpdateDetail(Buyorder buyorder);

	/**
	 * <b>Description:</b><br> 保存采购订单的申请修改
	 * @param buyorderGoodsVo
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2018年3月6日 上午11:01:46
	 */
	ResultInfo<?> saveBuyorderApplyUpdate(BuyorderModifyApply buyorderModifyApply,BuyorderModifyApplyGoodsVo buyorderModifyApplyGoodsVo);

	/**
	 * <b>Description:</b><br> 采购订单修改列表分页
	 * @param buyorderModifyApplyVo
	 * @param page
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2018年7月18日 上午11:13:55
	 */
	Map<String, Object> getBuyorderModifyApplyListPage(BuyorderModifyApplyVo buyorderModifyApplyVo, Page page);

	/**
	 * <b>Description:</b><br> 获取详情
	 * @param buyorderModifyApply
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2018年7月18日 下午2:34:24
	 */
	BuyorderModifyApplyVo getBuyorderModifyApplyVoDetail(BuyorderModifyApply buyorderModifyApply);
	/**
	 *
	 * <b>Description:</b><br> 采购订单修改列表
	 * @param buyorderModifyApply
	 * @return
	 * @Note
	 * <b>Author:</b> Michael
	 * <br><b>Date:</b> 2018年7月24日 下午7:27:33
	 */
	List<BuyorderModifyApply> getBuyorderModifyApplyList(BuyorderModifyApply buyorderModifyApply);
	/**
	 *
	 * <b>Description:</b><br> 获取批量生产条码采购单信息信息
	 * @param request
	 * @return
	 * @throws IOException
	 * @Note
	 * <b>Author:</b> Michael
	 * <br><b>Date:</b> 2018年7月26日 下午2:20:53
	 */
	BuyorderVo getBuyorderBarcodeVoDetail(BuyorderVo buyorderVo, User user);

	List<Integer> getBuyOrderIdsByCurrentOperateUser(Page page, String currentOperateUser);

	/**
	 * 更新采购单发货状态
	 * <AUTHOR>
	 * @Date 2:44 下午 2020/5/21
	 * @Param
	 * @return
	 **/
    void updateBuyorderDeliveryStatus(Integer buyorderId);

	Buyorder getBuyOrderByOrderId(Integer buyorderId);

	/**
	 *
	 * <b>Description:</b><br> 在途商品列表
	 * @param goodsId
	 * @return
	 * @Note
	 * <b>Author:</b> scott
	 * <br><b>Date:</b> 2017年10月9日 上午11:25:05
	 */
	List<BuyorderVo> getBuyorderVoList(Integer goodsId);

	/**
	 * 根据采购订单号获取采购订单相关信息
	 *
	 * @param buyorderNo
	 * @return
	 */
	BuyorderVo getBuyorderVoByOrderNo(String buyorderNo);

    void updateBuyorderGoodPrice(Integer buyorderGoodsId, BigDecimal price, BigDecimal originalPurchasePrice, String couponReason);

	void clearPriceInfo(Integer buyorderGoodId);

	/**
	 * 根据采购订单号获取GE主机和探头信息
	 * @param buyorderGoodsId
	 * @return
	 */
	List<MasterSlaveList> getMasterSlave(Integer buyorderGoodsId);

	/**
	 * 查询出订单的GE合同编号和销售订单编号并赋值，更改GE商品标志位
	 */
	void editBuyorderGEInfo(BuyorderVo buyorderVo);

    BuyorderVo getBuyorderInfoById(Integer buyorderId);

	/**
	 *  处理虚拟商品自动全部到货
	 * @param buyOrderId
	 */
	void handleBuyOrderVirtualGoodsArrival(Integer buyOrderId);

	void clearBuyOrderAuditInfo(Integer orderId);

    BuyorderVo getBuyOrderVoForDeliveryDirect(Integer buyOrderId);
	/**
	 * @Description 根据goods信息查询可以绑定的VB单
	 * <AUTHOR>
	 * @Date 14:20 2021/4/13
	 * @Param [goodId, goodNum, page]
	 * @return java.util.List<com.vedeng.order.model.vo.BuyorderVBVo>
	 **/
	Map<String, Object> queryVBBuyorderList(SearchVBByGoodVo searchVBByGoodVo, Page page);
	/**
	 * @Description 待采购订单绑定VB单
	 * <AUTHOR>
	 * @Date 14:30 2021/4/14
	 * @Param [buyOrderBindVBVO]
	 * @return com.vedeng.common.model.ResultInfo<?>
	 **/
    ResultInfo<?> buyOrderBindVB(BuyOrderBindVBVO buyOrderBindVBVO);
	/**
	 * 查询订单助理信息
	 * @param orderAssitantUserId
	 * @return
	 */
	List<User> getOrderAssitantInfoByIdSelective(Integer orderAssitantUserId);

	/**
	 * 查询产品经理信息
	 * @param orderAssitantUserId
	 * @return
	 */
    List<User> getProductUserByOrderAssistantId(Integer orderAssitantUserId,Integer type);
	/**
	 * @Description 更新待采购单的采购类型（立即采购，暂缓采购，无需采购）
	 * <AUTHOR>
	 * @Date 17:07 2021/4/15
	 * @Param [saleorderGoodsIds, componentId]
	 * @return com.vedeng.common.model.ResultInfo<?>
	 **/
	ResultInfo<?> changeComponent(String saleorderGoodsIds, Integer componentId,Integer userId,String reson);

	/**
	 * 绑定订单助理和产品经理
	 * @param orderAssistantRelationDto
	 * @param session
	 * @return
	 */
	ResultInfo bindOrderAssitantToProductUser(OrderAssistantRelationDto orderAssistantRelationDto, HttpSession session);

	/**
	 * .
	 * @jira: 解绑订单助理和产品经理
	 * @notes: .
	 * @version: 1.0.
	 * @date: 2021/5/20 15:40.
	 * @author: Randy.Xu.
	 * @param orderAssistantRelationDto
	 * @return: com.vedeng.common.model.ResultInfo.
	 * @throws:  .
	 */
	ResultInfo unbindOrderAssRelation(OrderAssistantRelationDto orderAssistantRelationDto);

	/**
	 * 获得需要生成的催货任务
	 * @jira: .
	 * @notes: .
	 * @version: 1.0.
	 * @date: 2021/5/18 20:13.
	 * @author: Randy.Xu.
	 * @param
	 * @return: java.util.List<com.vedeng.flash.dto.EarlyWarningTaskDto>.
	 * @throws:  .
	 */
	List<EarlyWarningTaskDto> getRemindGoodsTask();

	void updateGenerateInfo(EarlyWarningTaskDto earlyWarningTaskDto);

	/**
	 * 采购订单关闭预警处理
	 *
	 * @param buyOrderId
	 * @return
	 */
	boolean dealExpeditingByCloseOrder(Integer buyOrderId);

	/**
	 * 采购订单关闭处理催票信息
	 *
	 * @param orderNo
	 * @return
	 */
	boolean dealUrgeTicketBuyCloseOrder(String orderNo);

	/**
	 * 根据采购订单状态检索采购订单ID
	 *
	 * @param status
	 * @return
	 */
	List<Integer> getBuyOrderIdsByStatus(Integer status);

	/**
	 * 采购添加快递处理直发订单账期编码监管
	 *
	 * @param reExpress
	 * @param buyOrderId
	 * @param operateType
	 */
	void dealDirectOrderPeriodManagement(Express reExpress, Integer buyOrderId,Integer operateType);


	/**
	 * 审核通过后更新采购单商品信息
	 * @param snapShotOrderId
	 */
	void updateBuyorderGoodsSnapshotInfo(int snapShotOrderId);

	/**
	 * 更新主表审核状态
	 * @param buyorderId
	 * @param status
	 */
	void updateVerifyStatus(Integer buyorderId,Integer status);

	/**
	 * 查询订单流申请售后的采购单信息
	 * @param buyorder
	 * @return
	 */
	BuyorderVo getNewBuyorderGoodsVoList(Buyorder buyorder);

	/**
	 * {提交审核}修改采购单比较是否直发状态
	 * @param buyOrderModifyApplyId
	 */
    void sendMsgByCompareNewOldDeliveryDirect(Integer buyOrderModifyApplyId);

	/**
	 * {审核完成}修改采购单比较是否直发状态
	 * @param buyOrderModifyApplyId
	 */
	void sendMsgVerifyByCompareNewOldDeliveryDirect(Integer buyOrderModifyApplyId);

	/**
	 * {驳回}修改采购单比较是否直发状态
	 * @param buyOrderModifyApplyId
	 */
	void sendMsgVerifyNotByCompareNewOldDeliveryDirect(Integer buyOrderModifyApplyId);

	/**
	 * {审核完成}比较采购单SKU新旧价格
	 * @param buyOrderModifyApplyId
	 */
	void sendMsgByCompareNewOldSkuPrice(Integer buyOrderModifyApplyId);

	/**
	 * 通知销售单修改状态
	 * @param buyorderModifyApplyId
	 */
    void notifySaleOrderUpdateState(Integer buyorderModifyApplyId);

	/**
	 * 生成采购修改步骤条
     * @param historicInfo
     * @param usersList
     * @param verifyUsersList
     */
	StepBar generateStepBar(Map<String, Object> historicInfo, List<String> usersList, List<String> verifyUsersList);

	/**
	 * @description: 校验是否可确认收货
	 * @return:
	 * @author: Strange
	 * @date: 2021/10/20
	 **/
    boolean checkNumByExpress(Integer buyorderId, String id_arrivalNum);

	/**
	 * @desc 专项发货对应采购单校验
	 * <AUTHOR>
	 * @param buyorderVo
	 * @param dbbuysums 加入采购订单传递参数
	 * @return
	 */
	ResultInfo checkBuyorderSpecial(BuyorderVo buyorderVo,String[] dbbuysums);


	void udpateContractUrlByBuyOrderId(int buyOrderId,String contractUrl);

	/**
	 * 采购单SkuId根据物流单分组
	 * @param bv 采购单详情
	 */
	void logisticsGroupingByBuyOrderGoodsId(BuyorderVo bv);

	/**
	 * <AUTHOR>
	 * @desc 根据销售直发商品id查询采购单商品ID
	 * @param saleirderDirectGoods
	 * @return
	 */
	List<Integer> queryBuyorderGoodsIdsBySaleorderGoodsIds(List<Integer> saleirderDirectGoods);

	/**
	 * 直发商品采购售后校验 VDERP-10681
	 *
	 * @param buyorderGoodsId 采购商品id
	 * @param goodsId skuId
	 * @return true: 校验通过；false: 校验不通过
	 */
	boolean validDirectAfterSales(Integer buyorderGoodsId, Integer goodsId);

	/**
	 * VDERP-11979 采购单新增物流信息时，采购单信息查询接口重构
	 * @param buyorder 采购单id
	 * @return BuyorderVo
	 */
	BuyorderVo getBuyOrderInfoForAddExpress(Buyorder buyorder);

	/**
	 * VDERP-12398
	 * @param buyorderId
	 */
    void updateGiftOrderStatus(Integer buyorderId);

	void checkExpressEnableReceive(Integer buyorderId);

    ResultInfo<?> saveBuyorderAttachment(HttpServletRequest request,PurchaseContractDto purchaseContractDto);

	/**
	 * 自动上传采购合同标准模板，并审核通过。
	 * @param purchaseContractDto
	 * @return
	 */
	ResultInfo<?> saveBuyorderAttachmentForAadmin(PurchaseContractDto purchaseContractDto);

	/**
	 * 处理采购单附件上传事件
	 *
	 * @param buyorderId 采购单ID
	 * @param buyorderNo 采购单编号
	 * @param fileName 附件名称
	 * @param filePath 附件路径
	 * @param domain 附件域名
	 * @param attachmentType 附件类型
	 * @param attachmentFunction 附件功能类型
	 * @param creator 创建人ID
	 * @return ResultInfo 处理结果
	 */
	ResultInfo<?> saveBuyorderAttachmentForAadmin(Integer buyorderId, String buyorderNo, String fileName,
                                                String filePath, String domain, Integer attachmentType,
                                                Integer attachmentFunction, Integer creator,String ossResourceId);


	void confirmArrivalSaveBuyorderInvoiceStatus(Integer buyorderId);
}
