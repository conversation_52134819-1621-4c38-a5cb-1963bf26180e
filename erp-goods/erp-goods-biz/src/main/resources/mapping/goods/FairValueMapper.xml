<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.mapper.FairValueMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.goods.domain.entity.FairValueEntity">
        <id column="FAIR_VALUE_ID" jdbcType="INTEGER" property="fairValueId"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName"/>
        <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName"/>
        <result column="SKU" jdbcType="VARCHAR" property="sku"/>
        <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId"/>
        <result column="SKU_NAME" jdbcType="VARCHAR" property="skuName"/>
        <result column="PRICE" jdbcType="DECIMAL" property="price"/>
        <result column="HISTORY_DATA" property="historyData"
                typeHandler="com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler"/>
        <result column="SALE_ORDER_DATA" property="saleOrderData"
                typeHandler="com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler"/>
    </resultMap>
    <sql id="Base_Column_List">
    FAIR_VALUE_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME,
    SKU, GOODS_ID, SKU_NAME, PRICE, HISTORY_DATA, SALE_ORDER_DATA
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_FAIR_VALUE
        where FAIR_VALUE_ID = #{fairValueId,jdbcType=INTEGER}
    </select>
    <select id="selectByGoodsId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_FAIR_VALUE
        where GOODS_ID = #{goodsId,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_FAIR_VALUE
    where FAIR_VALUE_ID = #{fairValueId,jdbcType=INTEGER}
  </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into T_FAIR_VALUE (ADD_TIME, MOD_TIME, CREATOR,
              UPDATER, CREATOR_NAME, UPDATER_NAME,
              SKU, GOODS_ID, SKU_NAME,
              PRICE, HISTORY_DATA, SALE_ORDER_DATA
              )
            values
            <foreach collection="list" item="item" index="index" separator=",">
                (#{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER},
                  #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR},
                  #{item.sku,jdbcType=VARCHAR}, #{item.goodsId,jdbcType=INTEGER}, #{item.skuName,jdbcType=VARCHAR},
                  #{item.price,jdbcType=DECIMAL}, #{item.historyData,jdbcType=VARCHAR}, #{item.saleOrderData,jdbcType=VARCHAR}
                  )
            </foreach>
    </insert>

    <update id="batchUpdate" parameterType="java.util.List">
        <!--@mbg.generated-->
        update T_FAIR_VALUE
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="ADD_TIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when FAIR_VALUE_ID = #{item.fairValueId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="MOD_TIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when FAIR_VALUE_ID = #{item.fairValueId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="CREATOR = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when FAIR_VALUE_ID = #{item.fairValueId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="UPDATER = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when FAIR_VALUE_ID = #{item.fairValueId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="CREATOR_NAME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when FAIR_VALUE_ID = #{item.fairValueId,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="UPDATER_NAME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when FAIR_VALUE_ID = #{item.fairValueId,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="SKU = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when FAIR_VALUE_ID = #{item.fairValueId,jdbcType=INTEGER} then #{item.sku,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="GOODS_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when FAIR_VALUE_ID = #{item.fairValueId,jdbcType=INTEGER} then #{item.goodsId,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="SKU_NAME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when FAIR_VALUE_ID = #{item.fairValueId,jdbcType=INTEGER} then #{item.skuName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="PRICE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when FAIR_VALUE_ID = #{item.fairValueId,jdbcType=INTEGER} then #{item.price,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="HISTORY_DATA = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when FAIR_VALUE_ID = #{item.fairValueId,jdbcType=INTEGER} then #{item.historyData,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="SALE_ORDER_DATA = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when FAIR_VALUE_ID = #{item.fairValueId,jdbcType=INTEGER} then #{item.saleOrderData,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where FAIR_VALUE_ID in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.fairValueId,jdbcType=INTEGER}
        </foreach>
    </update>
</mapper>