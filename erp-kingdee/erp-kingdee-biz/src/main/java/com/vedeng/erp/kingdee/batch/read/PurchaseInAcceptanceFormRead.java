package com.vedeng.erp.kingdee.batch.read;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.kingdee.batch.common.reader.BaseItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchAttachmentDto;
import com.vedeng.erp.kingdee.batch.dto.BatchKingDeePurchaseReceiptDto;
import com.vedeng.erp.kingdee.batch.repository.BatchAttachmentDtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.NonTransientResourceException;
import org.springframework.batch.item.ParseException;
import org.springframework.batch.item.UnexpectedInputException;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 验收单附件
 * @date 2022/11/18 13:55
 */
@Slf4j
public class PurchaseInAcceptanceFormRead extends BaseItemReader<BatchAttachmentDto> {

    private Integer index = 0;

    @Autowired
    private BatchAttachmentDtoMapper batchAttachmentDtoMapper;

    @Override
    public BatchAttachmentDto read() throws Exception{
        List<BatchKingDeePurchaseReceiptDto> purchaseInData = (List<BatchKingDeePurchaseReceiptDto>) getStepParameter("purchaseInData");
        String formId = (String) getStepParameter("formId");

        if (CollUtil.isEmpty(purchaseInData)) {
            return null;
        }
        if (index >= purchaseInData.size()) {
            index = 0;
            return null;
        }
        if (StrUtil.isEmpty(formId)) {
            log.error("formId不可为空");
            throw new ServiceException("formId不可为空");
        }
        BatchKingDeePurchaseReceiptDto batchKingDeePurchaseReceiptDto = purchaseInData.get(index++);
        BatchAttachmentDto query = BatchAttachmentDto.builder()
                .attachmentType(462)
                .attachmentFunction(4211)
                .relatedId(batchKingDeePurchaseReceiptDto.getWarehouseGoodsOutInId().intValue())
                .build();
        BatchAttachmentDto batchAttachmentDto = batchAttachmentDtoMapper.purchaseInfindByQuery(query);
        if (Objects.isNull(batchAttachmentDto)) {
            return BatchAttachmentDto.builder().build();
        }
        batchAttachmentDto.setFId(batchKingDeePurchaseReceiptDto.getFId());
        batchAttachmentDto.setOutInNo(batchKingDeePurchaseReceiptDto.getOutInNo());
        batchAttachmentDto.setFormId(formId);
        return batchAttachmentDto;
    }
}
