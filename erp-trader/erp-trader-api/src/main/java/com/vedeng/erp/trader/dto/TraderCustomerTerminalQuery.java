package com.vedeng.erp.trader.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.vedeng.common.core.base.BaseDto;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 客户终端表
 * @date 2023/8/7 10:17
 */
@Getter
@Setter
public class TraderCustomerTerminalQuery {


    /**
     * 客户名
     */
    private String traderName;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 销售id
     */
    private Integer userId;

    /**
     * 销售用户id集合
     */
    private List<Integer> userIdList;


}