package com.vedeng.crm.xxl;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@JobHandler(value="testJob")
@Slf4j
public class TestJob extends AbstractJobHandler {
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        log.info("testJob");
        return null;
    }
}
