package com.vedeng.erp.trader.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.trader.domain.dto.BankMatchConfigGroupDto;
import com.vedeng.erp.trader.domain.entity.BankMatchConfigEntity;
import com.vedeng.erp.trader.domain.entity.BankMatchConfigGroupEntity;
import com.vedeng.erp.trader.mapper.BankMatchConfigGroupMapper;
import com.vedeng.erp.trader.mapper.BankMatchConfigMapper;
import com.vedeng.erp.trader.mapstruct.BankMatchConfigGroupConvertor;
import com.vedeng.erp.trader.service.BankMatchConfigGroupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.1
 * @description: 开户行匹配配置分组服务实现类
 * @date 2024/8/26 8:57
 */
@Slf4j
@Service
public class BankMatchConfigGroupServiceImpl implements BankMatchConfigGroupService {

    @Autowired
    private BankMatchConfigGroupMapper bankMatchConfigGroupMapper;
    @Autowired
    private BankMatchConfigMapper bankMatchConfigMapper;

    @Autowired
    private BankMatchConfigGroupConvertor bankMatchConfigGroupConvertor;

    @Override
    public PageInfo<BankMatchConfigGroupDto> page(PageParam<BankMatchConfigGroupDto> bankMatchConfigGroup) {
        BankMatchConfigGroupDto param = bankMatchConfigGroup.getParam();
        param.setIsDelete(ErpConstant.F);
        return PageHelper.startPage(bankMatchConfigGroup).doSelectPageInfo(() -> bankMatchConfigGroupMapper.findByAll(param));
    }

    @Override
    public BankMatchConfigGroupDto selectOne(Long groupId) {
        BankMatchConfigGroupEntity bankMatchConfigGroupEntity = bankMatchConfigGroupMapper.selectByPrimaryKey(groupId);
        if (bankMatchConfigGroupEntity != null) {
            BankMatchConfigGroupDto bankMatchConfigGroupDto = bankMatchConfigGroupConvertor.toDto(bankMatchConfigGroupEntity);
            List<BankMatchConfigEntity> allByGroupId = bankMatchConfigMapper.findAllByGroupId(groupId);
            String keyword = allByGroupId.stream().map(BankMatchConfigEntity::getKeyWords).collect(Collectors.joining("\n"));
            bankMatchConfigGroupDto.setBankConfig(keyword);
            return bankMatchConfigGroupDto;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void save(BankMatchConfigGroupDto bankMatchConfigGroup) {
        log.info("保存开户行匹配配置分组: {}", bankMatchConfigGroup.getGroupName());
        validateBankMatchConfigGroup(bankMatchConfigGroup);

        if (isGroupNameExists(bankMatchConfigGroup.getGroupName())) {
            throw new ServiceException("该银行名称已存在");
        }
        BankMatchConfigGroupEntity bankMatchConfigGroupEntity = bankMatchConfigGroupConvertor.toEntity(bankMatchConfigGroup);
        bankMatchConfigGroupMapper.insertSelective(bankMatchConfigGroupEntity);
        bankMatchConfigGroup.setGroupId(bankMatchConfigGroupEntity.getGroupId());

        saveBankMatchConfigs(bankMatchConfigGroup);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void update(BankMatchConfigGroupDto bankMatchConfigGroup) {
        log.info("更新开户行匹配配置分组: {}", bankMatchConfigGroup.getGroupName());
        validateBankMatchConfigGroup(bankMatchConfigGroup);

        BankMatchConfigGroupEntity existingGroup = bankMatchConfigGroupMapper.selectByPrimaryKey(bankMatchConfigGroup.getGroupId());
        if (!bankMatchConfigGroup.getGroupName().equals(existingGroup.getGroupName()) && isGroupNameExists(bankMatchConfigGroup.getGroupName())) {
            throw new ServiceException("该银行名称已存在");
        }

        bankMatchConfigGroupMapper.updateByPrimaryKeySelective(bankMatchConfigGroupConvertor.toEntity(bankMatchConfigGroup));
        bankMatchConfigMapper.deleteByGroupId(bankMatchConfigGroup.getGroupId());

        saveBankMatchConfigs(bankMatchConfigGroup);
    }

    @Override
    public void delete(Long groupId) {
        log.info("删除开户行匹配配置分组: {}", groupId);
        bankMatchConfigGroupMapper.deleteByGroupId(groupId);
        List<BankMatchConfigEntity> allByGroupId = bankMatchConfigMapper.findAllByGroupId(groupId);
        if(CollUtil.isNotEmpty(allByGroupId)){
            allByGroupId.forEach(bankMatchConfigEntity -> bankMatchConfigMapper.deleteByPrimaryKey(bankMatchConfigEntity.getBankMatchConfigId()));
        }
    }

    private void validateBankMatchConfigGroup(BankMatchConfigGroupDto bankMatchConfigGroup) {
        Assert.notEmpty(bankMatchConfigGroup.getGroupName(), "开户行匹配配置分组名称不能为空");
        Assert.notEmpty(bankMatchConfigGroup.getBankConfig(), "开户行匹配配置分组不能为空");
    }

    private boolean isGroupNameExists(String groupName) {
        return CollUtil.isNotEmpty(bankMatchConfigGroupMapper.findAllByGroupName(groupName));
    }

    private void saveBankMatchConfigs(BankMatchConfigGroupDto bankMatchConfigGroup) {
        String[] bankConfigs = bankMatchConfigGroup.getBankConfig().split("\n");

        for (String bankMatchConfig : bankConfigs) {
            bankMatchConfig = bankMatchConfig.trim();
            if (isKeyWordsExists(bankMatchConfig)) {
                BankMatchConfigEntity existingConfig = bankMatchConfigMapper.findByKeyWords(bankMatchConfig).get(0);
                BankMatchConfigGroupEntity oldGroup = bankMatchConfigGroupMapper.selectByPrimaryKey(existingConfig.getGroupId());
                throw new ServiceException("关键词 " + bankMatchConfig + " 存在于 " + oldGroup.getGroupName() + " 中，不可添加");
            }

            BankMatchConfigEntity bankMatchConfigEntity = new BankMatchConfigEntity();
            bankMatchConfigEntity.setGroupId(bankMatchConfigGroup.getGroupId());
            bankMatchConfigEntity.setIsDelete(0);
            bankMatchConfigEntity.setKeyWords(bankMatchConfig);

            bankMatchConfigMapper.insertSelective(bankMatchConfigEntity);
        }
    }

    private boolean isKeyWordsExists(String keyWords) {
        return CollUtil.isNotEmpty(bankMatchConfigMapper.findByKeyWords(keyWords));
    }
}
