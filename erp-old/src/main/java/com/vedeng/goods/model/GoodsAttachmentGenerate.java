package com.vedeng.goods.model;

public class GoodsAttachmentGenerate {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_ATTACHMENT.GOODS_ATTACHMENT_ID
     *
     * @mbggenerated Thu Jun 06 14:44:30 CST 2019
     */
    private Integer goodsAttachmentId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_ATTACHMENT.GOODS_ID
     *
     * @mbggenerated Thu Jun 06 14:44:30 CST 2019
     */
    private Integer goodsId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_ATTACHMENT.ATTACHMENT_TYPE
     *
     * @mbggenerated Thu Jun 06 14:44:30 CST 2019
     */
    private Integer attachmentType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_ATTACHMENT.DOMAIN
     *
     * @mbggenerated Thu Jun 06 14:44:30 CST 2019
     */
    private String domain;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_ATTACHMENT.URI
     *
     * @mbggenerated Thu Jun 06 14:44:30 CST 2019
     */
    private String uri;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_ATTACHMENT.ALT
     *
     * @mbggenerated Thu Jun 06 14:44:30 CST 2019
     */
    private String alt;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_ATTACHMENT.SORT
     *
     * @mbggenerated Thu Jun 06 14:44:30 CST 2019
     */
    private Integer sort;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_ATTACHMENT.IS_DEFAULT
     *
     * @mbggenerated Thu Jun 06 14:44:30 CST 2019
     */
    private Integer isDefault;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_ATTACHMENT.STATUS
     *
     * @mbggenerated Thu Jun 06 14:44:30 CST 2019
     */
    private Integer status;

    private Integer synSuccess;

    private String originalFilepath;


    private String displayName;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_ATTACHMENT.GOODS_ATTACHMENT_ID
     *
     * @return the value of T_GOODS_ATTACHMENT.GOODS_ATTACHMENT_ID
     *
     * @mbggenerated Thu Jun 06 14:44:30 CST 2019
     */
    public Integer getGoodsAttachmentId() {
        return goodsAttachmentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_ATTACHMENT.GOODS_ATTACHMENT_ID
     *
     * @param goodsAttachmentId the value for T_GOODS_ATTACHMENT.GOODS_ATTACHMENT_ID
     *
     * @mbggenerated Thu Jun 06 14:44:30 CST 2019
     */
    public void setGoodsAttachmentId(Integer goodsAttachmentId) {
        this.goodsAttachmentId = goodsAttachmentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_ATTACHMENT.GOODS_ID
     *
     * @return the value of T_GOODS_ATTACHMENT.GOODS_ID
     *
     * @mbggenerated Thu Jun 06 14:44:30 CST 2019
     */
    public Integer getGoodsId() {
        return goodsId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_ATTACHMENT.GOODS_ID
     *
     * @param goodsId the value for T_GOODS_ATTACHMENT.GOODS_ID
     *
     * @mbggenerated Thu Jun 06 14:44:30 CST 2019
     */
    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_ATTACHMENT.ATTACHMENT_TYPE
     *
     * @return the value of T_GOODS_ATTACHMENT.ATTACHMENT_TYPE
     *
     * @mbggenerated Thu Jun 06 14:44:30 CST 2019
     */
    public Integer getAttachmentType() {
        return attachmentType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_ATTACHMENT.ATTACHMENT_TYPE
     *
     * @param attachmentType the value for T_GOODS_ATTACHMENT.ATTACHMENT_TYPE
     *
     * @mbggenerated Thu Jun 06 14:44:30 CST 2019
     */
    public void setAttachmentType(Integer attachmentType) {
        this.attachmentType = attachmentType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_ATTACHMENT.DOMAIN
     *
     * @return the value of T_GOODS_ATTACHMENT.DOMAIN
     *
     * @mbggenerated Thu Jun 06 14:44:30 CST 2019
     */
    public String getDomain() {
        return domain;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_ATTACHMENT.DOMAIN
     *
     * @param domain the value for T_GOODS_ATTACHMENT.DOMAIN
     *
     * @mbggenerated Thu Jun 06 14:44:30 CST 2019
     */
    public void setDomain(String domain) {
        this.domain = domain;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_ATTACHMENT.URI
     *
     * @return the value of T_GOODS_ATTACHMENT.URI
     *
     * @mbggenerated Thu Jun 06 14:44:30 CST 2019
     */
    public String getUri() {
        return uri;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_ATTACHMENT.URI
     *
     * @param uri the value for T_GOODS_ATTACHMENT.URI
     *
     * @mbggenerated Thu Jun 06 14:44:30 CST 2019
     */
    public void setUri(String uri) {
        this.uri = uri;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_ATTACHMENT.ALT
     *
     * @return the value of T_GOODS_ATTACHMENT.ALT
     *
     * @mbggenerated Thu Jun 06 14:44:30 CST 2019
     */
    public String getAlt() {
        return alt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_ATTACHMENT.ALT
     *
     * @param alt the value for T_GOODS_ATTACHMENT.ALT
     *
     * @mbggenerated Thu Jun 06 14:44:30 CST 2019
     */
    public void setAlt(String alt) {
        this.alt = alt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_ATTACHMENT.SORT
     *
     * @return the value of T_GOODS_ATTACHMENT.SORT
     *
     * @mbggenerated Thu Jun 06 14:44:30 CST 2019
     */
    public Integer getSort() {
        return sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_ATTACHMENT.SORT
     *
     * @param sort the value for T_GOODS_ATTACHMENT.SORT
     *
     * @mbggenerated Thu Jun 06 14:44:30 CST 2019
     */
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_ATTACHMENT.IS_DEFAULT
     *
     * @return the value of T_GOODS_ATTACHMENT.IS_DEFAULT
     *
     * @mbggenerated Thu Jun 06 14:44:30 CST 2019
     */
    public Integer getIsDefault() {
        return isDefault;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_ATTACHMENT.IS_DEFAULT
     *
     * @param isDefault the value for T_GOODS_ATTACHMENT.IS_DEFAULT
     *
     * @mbggenerated Thu Jun 06 14:44:30 CST 2019
     */
    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_ATTACHMENT.STATUS
     *
     * @return the value of T_GOODS_ATTACHMENT.STATUS
     *
     * @mbggenerated Thu Jun 06 14:44:30 CST 2019
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_ATTACHMENT.STATUS
     *
     * @param status the value for T_GOODS_ATTACHMENT.STATUS
     *
     * @mbggenerated Thu Jun 06 14:44:30 CST 2019
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getSynSuccess() {
        return synSuccess;
    }

    public void setSynSuccess(Integer synSuccess) {
        this.synSuccess = synSuccess;
    }

    public String getOriginalFilepath() {
        return originalFilepath;
    }

    public void setOriginalFilepath(String originalFilepath) {
        this.originalFilepath = originalFilepath;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }
}