package com.vedeng.order.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Getter
public enum QuotedAlarmLevelEnum {

    /**
     * Level enums.
     */
    NONE(0, "无预警"),
    LEVEL_ONE(1, "第一级预警"),
    LEVEL_TWO(2, "第二级预警"),
    LEVEL_THREE(3, "第三级预警");

    private Integer level;
    private String message;


    QuotedAlarmLevelEnum(Integer level, String message) {
        this.level = level;
        this.message = message;
    }


    public static QuotedAlarmLevelEnum getOrDefault(Integer level) {
        for (QuotedAlarmLevelEnum levelEnum : values()) {
            if (Objects.equals(level, levelEnum.getLevel())) {
                return levelEnum;
            }
        }
        return NONE;
    }

}
