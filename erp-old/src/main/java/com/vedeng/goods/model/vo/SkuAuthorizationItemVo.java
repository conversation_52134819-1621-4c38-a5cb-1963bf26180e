package com.vedeng.goods.model.vo;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * SKU报备信息明细视图
 *
 * <AUTHOR>
 * @date 2020/9/24 14:19:20
 */
public class SkuAuthorizationItemVo implements Serializable {

    /**
     * 雪花ID
     */
    private Long snowFlakeId;

    /**
     * 省份ID集合
     */
    private List<Integer> regionIds;

    /**
     * 授权类型ID集合
     */
    private List<Integer> terminalTypeIds;

    public Long getSnowFlakeId() {
        return snowFlakeId;
    }

    public void setSnowFlakeId(Long snowFlakeId) {
        this.snowFlakeId = snowFlakeId;
    }

    public List<Integer> getRegionIds() {
        return regionIds;
    }

    public void setRegionIds(List<Integer> regionIds) {
        this.regionIds = regionIds;
    }

    public List<Integer> getTerminalTypeIds() {
        return terminalTypeIds;
    }

    public void setTerminalTypeIds(List<Integer> terminalTypeIds) {
        this.terminalTypeIds = terminalTypeIds;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o){
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SkuAuthorizationItemVo that = (SkuAuthorizationItemVo) o;
        return Objects.equals(regionIds, that.regionIds) &&
                Objects.equals(terminalTypeIds, that.terminalTypeIds);
    }

    @Override
    public int hashCode() {
        return Objects.hash(regionIds, terminalTypeIds);
    }
}
