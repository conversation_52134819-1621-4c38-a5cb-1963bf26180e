package com.vedeng.erp.kingdee.mapstruct;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeeInPutFeeSpecialInvoiceEntity;
import com.vedeng.erp.kingdee.dto.InPutFeeSpecialInvoiceDetailDto;
import com.vedeng.erp.kingdee.dto.InPutFeeSpecialInvoiceDto;
import org.mapstruct.*;

import java.util.List;

/**
 * 金蝶 进项票 dto 转 entity
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface KingDeeInPutFeeSpecialInvoiceConvertor extends BaseMapStruct<KingDeeInPutFeeSpecialInvoiceEntity,InPutFeeSpecialInvoiceDto> {
    /**
     * KingDeeInPutFeeSpecialInvoiceEntity
     *
     * @param dto InPutFeeSpecialInvoiceDto
     * @return KingDeeInPutFeeSpecialInvoiceEntity
     */
    @Mapping(target = "fpurexpinventry", source = "FPUREXPINVENTRY", qualifiedByName = "listToString")
    @Override
    KingDeeInPutFeeSpecialInvoiceEntity toEntity(InPutFeeSpecialInvoiceDto dto);

    /**
     * dto 原List 转 JSON
     *
     * @param source 对象
     * @return String String
     */
    @Named("listToString")
    default String listToString(List<InPutFeeSpecialInvoiceDetailDto> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSON.toJSONString(source);
    }
}
