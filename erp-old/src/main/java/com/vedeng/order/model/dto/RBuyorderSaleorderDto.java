package com.vedeng.order.model.dto;

import com.vedeng.order.model.RBuyorderSaleorder;

public class RBuyorderSaleorderDto extends RBuyorderSaleorder {

    /**
     * 采购单的状态
     */
    private Integer status;

    /**
     * 采购单号
     */
    private String buyorderNo;
    /**
     * 采购单号
     */
    private Integer buyorderId;

    public Integer getBuyorderId() {
        return buyorderId;
    }

    public void setBuyorderId(Integer buyorderId) {
        this.buyorderId = buyorderId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getBuyorderNo() {
        return buyorderNo;
    }

    public void setBuyorderNo(String buyorderNo) {
        this.buyorderNo = buyorderNo;
    }
}
