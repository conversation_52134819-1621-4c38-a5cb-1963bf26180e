package com.vedeng.erp.system.service.impl;

import com.vedeng.erp.system.dto.CostCategoryApiDto;
import com.vedeng.erp.system.mapper.CostCategoryMapper;
import com.vedeng.erp.system.service.CostCategroryApiService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class CostCategroryApiServiceImpl implements CostCategroryApiService {

    @Resource
    private CostCategoryMapper costCategoryMapper;

    @Override
    public List<CostCategoryApiDto> queryAllValidInfos() {
        return costCategoryMapper.queryAllValidInfos();
    }
}
