<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="生成电子签章链接" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%=basePath%>static/js/orderstream/saleorder/add_electronic_signature.js?rnd=${resourceVersionKey}'></script>
<div class="formpublic">
    <div style="margin-bottom: 10px;">
        链接生成后，系统会自动给客户发短信，请提前与客户沟通。<br><br>
        客户签署，需要客户实名信息，请务必确认填写正确的客户姓名，否则客户无法通过实名认证：<br>
    </div>
    <form method="post" action="" id="addElectronicSignature">
            <ul>
                <li>
                    <div class="infor_name">
                        <span>*</span>
                        <lable>客户姓名：</lable>
                    </div>
                    <div class="f_left  ">
                        <input name="realName" id="realName" value="${realName}"/>
                    </div>
                </li>
                <li>
                    <div class="infor_name">
                        <lable>手机号：</lable>
                    </div>
                    <div class="f_left">
                        <input name="phoneNumber" id="phoneNumber" value="${phoneNumber}" style="border:none;outline:none;" readonly="readonly"/>
                    </div>
                </li>
            </ul>
            <div class="add-tijiao tcenter">
                <input type="hidden" name="formToken" value="${formToken}"/>
                <input type="hidden" name="saleorderId" value="${saleorder.saleorderId}">
                <button type="submit" id="submit" style="background-color: #3384ef;border-color: #fff;">提交</button>
                <button class="dele" id="close-layer" type="button"  style="background-color: #f5f7fa;border-color: #ced2d9;color: black;">取消</button>
            </div>
    </form>
</div>
<%@ include file="../../common/footer.jsp"%>