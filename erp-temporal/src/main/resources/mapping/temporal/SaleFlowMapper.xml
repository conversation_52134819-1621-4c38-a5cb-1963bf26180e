<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.temporal.mapper.SaleFlowMapper">

    <resultMap id="SaleFlowSkuDtoMap" type="com.vedeng.temporal.domain.dto.SaleFlowSkuDto">
        <id column="FLOW_ORDER_DETAIL_ID" property="flowOrderDetailId"/>
        <result column="SKU_ID" property="skuId"/>
        <result column="SKU_NO" property="skuNo"/>
        <result column="PRODUCT_NAME" property="productName"/>
        <result column="BRAND" property="brand"/>
        <result column="MODEL" property="model"/>
        <result column="UNIT" property="unit"/>
        <result column="QUANTITY" property="quantity"/>
        <result column="PRICE" property="price"/>
        <result column="MARKUP_RATE" property="markupRate"/>
        <result column="GROSS_PROFIT_RATE" property="grossProfitRate"/>
    </resultMap>

    <resultMap id="SaleFlowDtoMap" type="com.vedeng.temporal.domain.dto.SaleFlowDto">
        <id column="FLOW_ORDER_ID" property="flowOrderId"/>
        <result column="FLOW_ORDER_NO" property="flowOrderNo"/>
        <result column="FLOW_NODE_ID" property="flowNodeId"/>
        <result column="NODE_LEVEL" property="nodeLevel"/>
        <result column="OPEN_INVOICE" property="openInvoice"/>
        <result column="PAYMENT_METHOD" property="paymentMethod"/>
        <result column="INVOICE_TYPE" property="invoiceType"/>
        <result column="AMOUNT" property="amount"/>
        <result column="CREDIT_PAYMENT" property="creditPayment"/>
        <result column="BALANCE" property="balance"/>
        <result column="BALANCE_DUE_DATE" property="balanceDueDate"/>
        <result column="TRADER_ID" property="traderId"/>
        <result column="TRADER_NAME" property="traderName"/>
        <result column="TRADER_CONTACT_ID" property="traderContactId"/>
        <result column="TRADER_CONTACT_NAME" property="traderContactName"/>
        <result column="TRADER_CONTACT_PHONE" property="traderContactPhone"/>
        <result column="TRADER_ADDRESS_ID" property="traderAddressId"/>
        <result column="TRADER_CONTACT_ADDRESS" property="traderContactAddress"/>
        <result column="RECEIVER_TRADER_CONTACT_ID" property="receiverTraderContactId"/>
        <result column="RECEIVER_NAME" property="receiverName"/>
        <result column="RECEIVER_PHONE" property="receiverPhone"/>
        <result column="RECEIVER_ADDRESS_ID" property="receiverAddressId"/>
        <result column="RECEIVER_ADDRESS" property="receiverAddress"/>
        <result column="INVOICE_RECEIVER_TRADER_CONTACT_ID" property="invoiceReceiverTraderContactId"/>
        <result column="INVOICE_RECEIVER_NAME" property="invoiceReceiverName"/>
        <result column="INVOICE_RECEIVER_PHONE" property="invoiceReceiverPhone"/>
        <result column="INVOICE_RECEIVER_ADDRESS_ID" property="invoiceReceiverAddressId"/>
        <result column="INVOICE_RECEIVER_ADDRESS" property="invoiceReceiverAddress"/>
        <collection property="skuList" ofType="com.vedeng.temporal.domain.dto.SaleFlowSkuDto" resultMap="SaleFlowSkuDtoMap"/>
    </resultMap>

    <select id="queryAddressByAddressId" resultType="com.vedeng.temporal.dto.SaleOrderAddressDto"  >
        select
            *
        from T_ADDRESS a
        where a.ADDRESS_ID = #{addressId,jdbcType=INTEGER}
    </select>

    <select id="selectSaleFlowWithSkuAndPrice" resultMap="SaleFlowDtoMap">
        SELECT
            fn.FLOW_ORDER_ID,
            fo.FLOW_ORDER_NO,
            fn.FLOW_NODE_ID,
            fn.NODE_LEVEL,
            fn.OPEN_INVOICE,
            fn.PAYMENT_METHOD,
            fn.INVOICE_TYPE,
            fn.AMOUNT,
            fn.CREDIT_PAYMENT,
            fn.BALANCE,
            fn.BALANCE_DUE_DATE,
            fn.TRADER_ID,
            fn.TRADER_NAME,
            fn.TRADER_CONTACT_ID,
            fn.TRADER_CONTACT_NAME,
            fn.TRADER_CONTACT_PHONE,
            fn.TRADER_ADDRESS_ID,
            fn.TRADER_CONTACT_ADDRESS,
            fn.RECEIVER_TRADER_CONTACT_ID,
            fn.RECEIVER_NAME,
            fn.RECEIVER_PHONE,
            fn.RECEIVER_ADDRESS_ID,
            fn.RECEIVER_ADDRESS,
            fn.INVOICE_RECEIVER_TRADER_CONTACT_ID,
            fn.INVOICE_RECEIVER_NAME,
            fn.INVOICE_RECEIVER_PHONE,
            fn.INVOICE_RECEIVER_ADDRESS_ID,
            fn.INVOICE_RECEIVER_ADDRESS,
            fod.FLOW_ORDER_DETAIL_ID,
            fod.SKU_ID,
            fod.SKU_NO,
            fod.PRODUCT_NAME,
            fod.BRAND,
            fod.MODEL,
            fod.UNIT,
            fod.QUANTITY,
            fnodp.PRICE,
            fnodp.MARKUP_RATE,
            fnodp.GROSS_PROFIT_RATE
        FROM T_FLOW_NODE fn
        INNER JOIN T_FLOW_ORDER fo ON fn.FLOW_ORDER_ID = fo.FLOW_ORDER_ID
        LEFT JOIN T_FLOW_ORDER_DETAIL fod ON fn.FLOW_ORDER_ID = fod.FLOW_ORDER_ID AND fod.IS_DELETE = 0
        LEFT JOIN T_FLOW_NODE_ORDER_DETAIL_PRICE fnodp ON fn.FLOW_NODE_ID = fnodp.FLOW_NODE_ID AND fod.FLOW_ORDER_DETAIL_ID = fnodp.FLOW_ORDER_DETAIL_ID
        WHERE fn.FLOW_ORDER_ID = #{flowOrderId}
          AND fn.NODE_LEVEL = #{nodeLevel}
          AND fn.IS_DELETE = 0
        ORDER BY fod.FLOW_ORDER_DETAIL_ID
    </select>
</mapper> 