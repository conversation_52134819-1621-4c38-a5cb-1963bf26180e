package com.wms.task;

import com.vedeng.common.constant.ApiUrlConstant;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.stock.api.stock.dto.OrderCheckReqDto;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName OutOrderCheckTask.java
 * @Description TODO 出库单据核对
 * @createTime 2021年12月10日 18:39:00
 */
@Component
@JobHandler(value = "OutOrderCheckTask")
public class OutOrderCheckTask extends AbstractJobHandler {


    @Value("${stock_admin_url}")
    private String stockAdminUrl;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        OrderCheckReqDto reqDto = new OrderCheckReqDto();
        if(StringUtil.isBlank(param)){
            reqDto.setStartTime("2016-01-01");
            reqDto.setEndTime(DateUtil.getNowDate(DateUtil.DATE_FORMAT));
        }else{
            reqDto = com.alibaba.fastjson.JSONObject.parseObject(param, OrderCheckReqDto.class);
        }

        String json = com.alibaba.fastjson.JSONObject.toJSONString(reqDto);
        String url = stockAdminUrl + ApiUrlConstant.OUT_ORDER_CHECK_REPORT;
        JSONObject jsonObject = NewHttpClientUtils.httpPost(url, json);
        XxlJobLogger.log("响应:"+jsonObject.toString());

        return SUCCESS;
    }
}
