package com.vedeng.erp.finance.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.erp.finance.domain.dto.InvoiceApplyDetailXhDto;
import com.vedeng.erp.finance.domain.entity.InvoiceApplyDetailXhEntity;
import com.vedeng.erp.finance.mapper.InvoiceApplyDetailXhMapper;
import com.vedeng.erp.finance.mapstruct.ApplyDetailXhConvertor;
import com.vedeng.erp.finance.service.InvoiceApplyDetailXhService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class InvoiceApplyDetailXhServiceImpl implements InvoiceApplyDetailXhService {

    @Autowired
    private ApplyDetailXhConvertor applyDetailXhConvertor;
    
    @Autowired
    private InvoiceApplyDetailXhMapper invoiceApplyDetailXhMapper;
    
    @Override
    @Transactional
    public void insert(List<InvoiceApplyDetailXhDto> list) {
        for (InvoiceApplyDetailXhDto invoiceApplyDetailXhDto : list) {
            InvoiceApplyDetailXhEntity invoiceApplyDetailXh = applyDetailXhConvertor.toEntity(invoiceApplyDetailXhDto);
            invoiceApplyDetailXhMapper.insertSelective(invoiceApplyDetailXh);
        }
    }

    @Override
    public List<InvoiceApplyDetailXhDto> getByApplyId(Integer applyId) {
        List<InvoiceApplyDetailXhEntity> list = invoiceApplyDetailXhMapper.selectByApplyId(applyId);
        if(CollUtil.isNotEmpty(list)){
            return applyDetailXhConvertor.toDto(list);
        }
        return null;
    }
}
