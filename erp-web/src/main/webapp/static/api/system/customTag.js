function addTag(data) {
    return axios({
        url: '/customTag/add.do',
        method: 'post',
        data: data
    })
}

function updateTag(data) {
    return axios({
        url: '/customTag/update.do',
        method: 'post',
        data: data
    })
}

function removeTag(data) {
    return axios({
        url: '/customTag/delete.do',
        method: 'post',
        params: data
    })
}

function getBusinessTags(data) {
    return axios({
        url: '/customTag/getUserTags.do',
        method: 'post',
        params: data
    })
}

function getBusinessTagsToMap(data) {
    return axios({
        url: '/customTag/getUserTagsToMap.do',
        method: 'post',
        params: data
    })
}