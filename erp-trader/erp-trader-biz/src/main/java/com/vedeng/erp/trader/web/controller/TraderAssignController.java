package com.vedeng.erp.trader.web.controller;

import com.vedeng.common.core.base.ExceptionController;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

@Controller
@ExceptionController
@RequestMapping("/traderAssign")
public class TraderAssignController {

    @RequestMapping("/select")
    public ModelAndView traderAssignSelect() {
        ModelAndView mv = new ModelAndView("vue/view/trader/trader_assign_select");
        return mv;
    }
}
