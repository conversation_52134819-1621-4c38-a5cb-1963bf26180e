package com.vedeng.infrastructure.kingdee.common.constant;

import com.ctrip.framework.apollo.ConfigService;

/**
 * 金蝶常量类
 *
 * <AUTHOR>
 */
public class KingDeeConstant {

    /**
     * 金蝶异步事件队列
     */
    public final static String KING_DEE_QUEUE = "kingDeeEventQueue";
    /**
     * 金蝶异步事件交换机
     */
    public final static String KING_DEE_EXCHANGE = "kingDeeEventQueueExchange";
    /**
     * 金蝶异步事件路由key
     */
    public final static String KING_DEE_ROUTING_KEY = "kingDeeEventQueueRoutingKey";

    /**
     * 组织id
     * 贝登集团=100
     */
    public final static Integer ORG_ID = Integer.parseInt(ConfigService.getAppConfig().getProperty("kingdee.orgNum", ""));


    public final static Integer ONE = 1;

    public final static Integer ZERO = 0;

    public final static Integer TWO = 2;

    public final static Integer THREE = 3;

    public final static Integer FOUR = 4;

    public final static Integer FIVE = 5;
    /**
     * 采购单申请付款
     */
    public final static Integer ID_517 = 517;
    /**
     * 销售售后申请付款
     */
    public final static Integer ID_518 = 518;
    /**
     * 采购费用单申请付款
     */
    public final static Integer ID_4125 = 4125;
    /**
     * 销售售后退票
     */
    public final static Integer ID_542 = 542;
    /**
     * 销售售后退货
     */
    public final static Integer ID_539 = 539;

    /**
     * 销售售后换货
     */
    public final static Integer ID_540 = 540;

    /**
     * 售后主体类型 销售
     */
    public final static Integer ID_535 = 535;

    /**
     * admin
     */
    public final static String ADMIN = "admin";

    /**
     * 采购直发快递类型
     */
    public final static Integer ID_515 = 515;

    public final static Integer PURCHASE = 536;

    /**
     * 出入库单据主表来源 ERP
     */
    public final static String SOURCE_ERP = "ERP";

    /**
     * 出入库单据主表来源 WMS
     */
    public final static String SOURCE_WMS = "WMS";
    /**
     * 出入库验收单类型
     */
    public static final Integer WAREHOUSER_ATTACHMET_TYPE = 462;
    /**
     * 入库验收单FUNCTION
     */
    public static final Integer WAREHOUSER_ATTACHMET_FUNCTION = 4211;
    /**
     * 出库验收单FUNCTION
     */
    public static final Integer WAREHOUSE_ATTACHMET_OUT_FUNCTION = 4213;
    /**
     * 已结算FBillNo前缀
     */
    public static final String SETTLE_ACCOUNT = "C_";
    /**
     * 已忽略FBillNo前缀
     */
    public static final String IGNORE = "B_";

    public static final String OTHER_BUSINESS_RECEIPTS = "其他业务收款";

    public static final String PURCHASE_AFTER_SALES_PAYMENT = "采购售后收款";

    public static final String BD_SUPPLIER = "BD_Supplier";
}
