<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.department.dao.DepartmentFeeItemsMappingMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.department.model.DepartmentFeeItemsMapping">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 19:02:47 CST 2019.
    -->
    <id column="DEPARTMENT_FEE_ITEMS_MAPPING_ID" jdbcType="INTEGER" property="departmentFeeItemsMappingId" />
    <result column="DEPARTMENT_ID" jdbcType="INTEGER" property="departmentId" />
    <result column="DEPARTMENT_FEE_ITEMS_ID" jdbcType="INTEGER" property="departmentFeeItemsId" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 19:02:47 CST 2019.
    -->
    DEPARTMENT_FEE_ITEMS_MAPPING_ID, DEPARTMENT_ID, DEPARTMENT_FEE_ITEMS_ID, UPDATER, 
    MOD_TIME, CREATOR, ADD_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 19:02:47 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from T_DEPARTMENT_FEE_ITEMS_MAPPING
    where DEPARTMENT_FEE_ITEMS_MAPPING_ID = #{departmentFeeItemsMappingId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 19:02:47 CST 2019.
    -->
    delete from T_DEPARTMENT_FEE_ITEMS_MAPPING
    where DEPARTMENT_FEE_ITEMS_MAPPING_ID = #{departmentFeeItemsMappingId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.department.model.DepartmentFeeItemsMapping">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 19:02:47 CST 2019.
    -->
    insert into T_DEPARTMENT_FEE_ITEMS_MAPPING (DEPARTMENT_FEE_ITEMS_MAPPING_ID, DEPARTMENT_ID, 
      DEPARTMENT_FEE_ITEMS_ID, UPDATER, MOD_TIME, 
      CREATOR, ADD_TIME)
    values (#{departmentFeeItemsMappingId,jdbcType=INTEGER}, #{departmentId,jdbcType=INTEGER}, 
      #{departmentFeeItemsId,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, 
      #{creator,jdbcType=INTEGER}, #{addTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.department.model.DepartmentFeeItemsMapping">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 19:02:47 CST 2019.
    -->
    insert into T_DEPARTMENT_FEE_ITEMS_MAPPING
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="departmentFeeItemsMappingId != null">
        DEPARTMENT_FEE_ITEMS_MAPPING_ID,
      </if>
      <if test="departmentId != null">
        DEPARTMENT_ID,
      </if>
      <if test="departmentFeeItemsId != null">
        DEPARTMENT_FEE_ITEMS_ID,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="departmentFeeItemsMappingId != null">
        #{departmentFeeItemsMappingId,jdbcType=INTEGER},
      </if>
      <if test="departmentId != null">
        #{departmentId,jdbcType=INTEGER},
      </if>
      <if test="departmentFeeItemsId != null">
        #{departmentFeeItemsId,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.department.model.DepartmentFeeItemsMapping">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 19:02:47 CST 2019.
    -->
    update T_DEPARTMENT_FEE_ITEMS_MAPPING
    <set>
      <if test="departmentId != null">
        DEPARTMENT_ID = #{departmentId,jdbcType=INTEGER},
      </if>
      <if test="departmentFeeItemsId != null">
        DEPARTMENT_FEE_ITEMS_ID = #{departmentFeeItemsId,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
    </set>
    where DEPARTMENT_FEE_ITEMS_MAPPING_ID = #{departmentFeeItemsMappingId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.department.model.DepartmentFeeItemsMapping">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 19:02:47 CST 2019.
    -->
    update T_DEPARTMENT_FEE_ITEMS_MAPPING
    set DEPARTMENT_ID = #{departmentId,jdbcType=INTEGER},
      DEPARTMENT_FEE_ITEMS_ID = #{departmentFeeItemsId,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=BIGINT}
    where DEPARTMENT_FEE_ITEMS_MAPPING_ID = #{departmentFeeItemsMappingId,jdbcType=INTEGER}
  </update>
  
  <select id="getDepartmentsHospitalByParam" resultType="com.vedeng.department.model.DepartmentsHospital">
    SELECT
        a.DEPARTMENT_ID,
        IF(
            d.FEE_ITEMS_NAME = '' OR d.FEE_ITEMS_NAME IS NULL,
          IF(
              c.FEE_ITEMS_NAME = '' OR c.FEE_ITEMS_NAME IS NULL,
            IF(b.FEE_ITEMS_NAME = '' OR b.FEE_ITEMS_NAME IS NULL, '--', b.FEE_ITEMS_NAME),
            CONCAT(c.FEE_ITEMS_NAME, '>', b.FEE_ITEMS_NAME)
          ),
          CONCAT(d.FEE_ITEMS_NAME, '>', c.FEE_ITEMS_NAME, '>', b.FEE_ITEMS_NAME)
        ) AS feePro
    FROM
        T_DEPARTMENT_FEE_ITEMS_MAPPING a
    LEFT JOIN T_DEPARTMENT_FEE_ITEMS b ON a.DEPARTMENT_FEE_ITEMS_ID = b.DEPARTMENT_FEE_ITEMS_ID
    LEFT JOIN T_DEPARTMENT_FEE_ITEMS c ON b.PARENT_ID = c.DEPARTMENT_FEE_ITEMS_ID
    LEFT JOIN T_DEPARTMENT_FEE_ITEMS d ON c.PARENT_ID = d.DEPARTMENT_FEE_ITEMS_ID
    WHERE
        a.DEPARTMENT_ID IN
        <foreach collection="hospitalList" item="feePro" open="(" separator="," close=")">
            #{feePro.departmentId, jdbcType=INTEGER}
        </foreach>
        AND a.IS_DELETE = #{isDelete, jdbcType=INTEGER}
  </select>


<!--  <insert id="insertDepartmentFeeItemsInfos" parameterType="java.util.Map">
    insert into T_DEPARTMENT_FEE_ITEMS_MAPPING (DEPARTMENT_ID,
      DEPARTMENT_FEE_ITEMS_ID, UPDATER, MOD_TIME,
      CREATOR, ADD_TIME)
    values
      <foreach collection="departmentFeeItemsMappings" item="feeItems" separator=",">
        <trim prefix="(" suffix=")" suffixOverrides=",">
          #{deptInfoId, jdbcType=INTEGER},
          #{feeItems.departmentFeeItemsId, jdbcType=INTEGER},
          #{creator,jdbcType=INTEGER},
          unix_timestamp(now())*1000,
          #{creator,jdbcType=INTEGER},
          unix_timestamp(now())*1000
        </trim>
      </foreach>
  </insert>-->

  <update id="deleteFeeItemsMappingsById" parameterType="com.vedeng.department.model.DepartmentsHospital">
    update
      T_DEPARTMENT_FEE_ITEMS_MAPPING
    SET
      IS_DELETE = 1,
      UPDATER = #{updater, jdbcType=INTEGER},
      MOD_TIME = unix_timestamp(now())*1000
    where
      DEPARTMENT_ID = #{departmentId, jdbcType=INTEGER}
  </update>

  <insert id="insertDepartmentFeeItemsInfos" parameterType="java.util.Map">
    insert into
        T_DEPARTMENT_FEE_ITEMS_MAPPING
          (
            DEPARTMENT_ID,
            DEPARTMENT_FEE_ITEMS_ID,
            UPDATER,
            MOD_TIME,
            CREATOR,
            ADD_TIME
          )
    values
    <foreach collection="feePros" item="feeItemId" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{departmentId, jdbcType=INTEGER},
        #{feeItemId, jdbcType=INTEGER},
        #{userId,jdbcType=INTEGER},
        unix_timestamp(now())*1000,
        #{userId,jdbcType=INTEGER},
        unix_timestamp(now())*1000
      </trim>
    </foreach>
  </insert>

</mapper>