package com.vedeng.erp.saleorder.task;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.saleorder.service.SaleOrderDataApiService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/11/8
 */
@JobHandler(value = "SaleOrderGoodsMaoLiTask")
@Component
@Slf4j
public class SaleOrderGoodsMaoLiTask  extends AbstractJobHandler {

    @Autowired
    private SaleOrderDataApiService saleOrderDataApiService;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        saleOrderDataApiService.updateSaleOrderGoodsMaoliForJob();
        return ReturnT.SUCCESS;
    }
}
