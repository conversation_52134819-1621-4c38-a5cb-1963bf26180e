package com.wms.constant;

/**
 * <AUTHOR>
 * @Description 库存转移单类型
 * @createTime 2020年06月28日 11:37:00
 */
public enum InventoryTransferTypeEnum {
    WAREHOUSE_INVENTORY_TRANSFER(1, "仓库库存转移单", "NM"),
    RECENTTIME_INVENTORY_TRANSFER(2, "近效期转移单", "TA"),
    OVER_ECENTTIME_INVENTORY_TRANSFER(3, "超近效期转移单", "TB"),
    SECKILL_INVENTORY_TRANSFER(4, "秒杀活动移仓单", "TR"),
    WAIT_NOTICE_INVENTORY_TRANSFER(5, " 等通知发货移库单", "SO"),
    MULTI_ADDRESS_INVENTORY_TRANSFER(6, " 多地址发货移库单", "SA");

    InventoryTransferTypeEnum(Integer type, String typeStr, String code) {
        this.type = type;
        this.typeStr = typeStr;
        this.code = code;
    }

    private Integer type;

    private String typeStr;

    private String code;

    public static String getTypeStr(Integer type) {
        String typeStr = "";

        for (InventoryTransferTypeEnum enumItem : InventoryTransferTypeEnum.values()) {
            if (enumItem.getType().equals(type)) {
                typeStr = enumItem.getTypeStr();
                break;
            }
        }

        return typeStr;
    }

    public static Integer getTypeByCode(String code) {
        Integer type = 0;

        for (InventoryTransferTypeEnum enumItem : InventoryTransferTypeEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                type = enumItem.getType();
                break;
            }
        }

        return type;
    }

    public Integer getType() {
        return type;
    }

    public String getTypeStr() {
        return typeStr;
    }

    public String getCode() {
        return code;
    }}
