package com.vedeng.erp.aftersale.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 可退票信息数据dto
 * @date 2022/10/27 13:43
 */
@Data
public class RefundInvoiceRecordDto {

    /**
     * 发票号码
     */
    private String invoiceNo;

    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 发票金额
     */
    private BigDecimal invoiceAmount;

    /**
     * 发票种类
     */
    private Integer invoiceType;

    /**
     * 发票实际蓝字票invoiceId集合
     */
    private List<Integer> invoiceIds;

    /**
     * 退票状态
     * 退票状态 0未退票 1已退票 2退票中 3无需退票
     */
    private Integer refundInvoiceStatus;

    /**
     * 寄送状态
     */
    private Integer sendStatus;


}
