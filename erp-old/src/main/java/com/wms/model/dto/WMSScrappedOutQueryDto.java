package com.wms.model.dto;

/**
 * 报废出库查询dto
 */
public class WMSScrappedOutQueryDto extends WMSLendOutQueryDto{

    private String applyer;

    private String applyerDepartment;

    private Integer scrapType;

    private Integer scrapLevel;

    private String skuName;

    private String skuNo;

    private String brandName;

    private Integer scrapDealType;

    private String model;

    private Integer outputNum;

    private Integer outStatus;

    private String remark;

    public Integer getOutStatus() {
        return outStatus;
    }

    public void setOutStatus(Integer outStatus) {
        this.outStatus = outStatus;
    }

    public String getApplyer() {
        return applyer;
    }

    public void setApplyer(String applyer) {
        this.applyer = applyer;
    }

    public String getApplyerDepartment() {
        return applyerDepartment;
    }

    public void setApplyerDepartment(String applyerDepartment) {
        this.applyerDepartment = applyerDepartment;
    }

    public Integer getScrapType() {
        return scrapType;
    }

    public void setScrapType(Integer scrapType) {
        this.scrapType = scrapType;
    }

    public Integer getScrapLevel() {
        return scrapLevel;
    }

    public void setScrapLevel(Integer scrapLevel) {
        this.scrapLevel = scrapLevel;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    public String getSkuNo() {
        return skuNo;
    }

    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public Integer getScrapDealType() {
        return scrapDealType;
    }

    public void setScrapDealType(Integer scrapDealType) {
        this.scrapDealType = scrapDealType;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public Integer getOutputNum() {
        return outputNum;
    }

    public void setOutputNum(Integer outputNum) {
        this.outputNum = outputNum;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
