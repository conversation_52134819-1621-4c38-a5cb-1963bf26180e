package com.vedeng.order.model;

import java.io.Serializable;
import java.util.List;

/**
 * T_QUOTEORDER_CONSULT_REPLY
 * <AUTHOR>
 */
public class QuoteorderConsultReply implements Serializable {
    private Integer quoteorderConsultReplyId;

    /**
     * 报价单ID
     */
    private Integer quoteorderId;

    /**
     * 咨询类型，1咨询供应链，2咨询主管
     */
    private Integer consultType;

    /**
     * 咨询处理人
     */
    private Integer consultReplier;

    private List<Integer> consultReplierList;

    /**
     * 咨询回复人名字
     */
    private String consultReplierName;

    /**
     * 咨询回复人角色，0供应主管，1spu归属人，2分配人
     */
    private Integer consultReplierRole;

    private Integer quoteorderGoodsId;

    /**
     * 咨询SKU
     */
    private String sku;

    /**
     * 其他咨询内容
     */
    private String consultOther;

    /**
     * 其他咨询的回复
     */
    private String consultOtherReply;

    /**
     * 咨询其他的回复状态，-1待回复，0未咨询，1已回复
     */
    private Integer consultOtherReplyStatus;

    /**
     * 咨询报备情况的回复，0不需要报备 2成功 3失败
     */
    private Integer reportConsultReply;

    /**
     * 报备失败的原因
     */
    private String reportConsultReplyContent;

    /**
     * 咨询报备情况的回复状态，-1待回复，0未咨询，1已回复
     */
    private Integer reportConsultReplyStatus;

    /**
     * 咨询参考价格的回复
     */
    private String referencePriceReply;

    /**
     * 咨询参考价格的回复状态，-1待回复，0未咨询，1已回复
     */
    private Integer referencePriceReplyStatus;

    /**
     * 咨询货期的回复
     */
    private String referenceDeliveryCycleReply;

    /**
     * 咨询货期的回复状态，-1待回复，0未咨询，1已回复
     */
    private Integer referenceDeliveryCycleReplyStatus;

    /**
     * 咨询主管的内容
     */
    private String consultExecutive;

    /**
     * 主管回复的内容
     */
    private String consultExecutiveReply;

    /**
     * 咨询主管的回复状态，-1待回复，0未咨询，1已回复
     */
    private Integer consultExecutiveReplyStatus;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 创建者
     */
    private Integer creator;

    /**
     * 更新者
     */
    private Integer updater;

    private static final long serialVersionUID = 1L;

    public Integer getQuoteorderConsultReplyId() {
        return quoteorderConsultReplyId;
    }

    public void setQuoteorderConsultReplyId(Integer quoteorderConsultReplyId) {
        this.quoteorderConsultReplyId = quoteorderConsultReplyId;
    }

    public Integer getQuoteorderId() {
        return quoteorderId;
    }

    public void setQuoteorderId(Integer quoteorderId) {
        this.quoteorderId = quoteorderId;
    }

    public Integer getConsultType() {
        return consultType;
    }

    public void setConsultType(Integer consultType) {
        this.consultType = consultType;
    }

    public Integer getConsultReplier() {
        return consultReplier;
    }

    public void setConsultReplier(Integer consultReplier) {
        this.consultReplier = consultReplier;
    }

    public String getConsultReplierName() {
        return consultReplierName;
    }

    public List<Integer> getConsultReplierList() {
        return consultReplierList;
    }

    public void setConsultReplierList(List<Integer> consultReplierList) {
        this.consultReplierList = consultReplierList;
    }

    public void setConsultReplierName(String consultReplierName) {
        this.consultReplierName = consultReplierName;
    }

    public Integer getConsultReplierRole() {
        return consultReplierRole;
    }

    public void setConsultReplierRole(Integer consultReplierRole) {
        this.consultReplierRole = consultReplierRole;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getConsultOther() {
        return consultOther;
    }

    public void setConsultOther(String consultOther) {
        this.consultOther = consultOther;
    }

    public String getConsultOtherReply() {
        return consultOtherReply;
    }

    public void setConsultOtherReply(String consultOtherReply) {
        this.consultOtherReply = consultOtherReply;
    }

    public Integer getConsultOtherReplyStatus() {
        return consultOtherReplyStatus;
    }

    public void setConsultOtherReplyStatus(Integer consultOtherReplyStatus) {
        this.consultOtherReplyStatus = consultOtherReplyStatus;
    }

    public Integer getReportConsultReply() {
        return reportConsultReply;
    }

    public void setReportConsultReply(Integer reportConsultReply) {
        this.reportConsultReply = reportConsultReply;
    }

    public String getReportConsultReplyContent() {
        return reportConsultReplyContent;
    }

    public void setReportConsultReplyContent(String reportConsultReplyContent) {
        this.reportConsultReplyContent = reportConsultReplyContent;
    }

    public Integer getReportConsultReplyStatus() {
        return reportConsultReplyStatus;
    }

    public void setReportConsultReplyStatus(Integer reportConsultReplyStatus) {
        this.reportConsultReplyStatus = reportConsultReplyStatus;
    }

    public String getReferencePriceReply() {
        return referencePriceReply;
    }

    public void setReferencePriceReply(String referencePriceReply) {
        this.referencePriceReply = referencePriceReply;
    }

    public Integer getReferencePriceReplyStatus() {
        return referencePriceReplyStatus;
    }

    public void setReferencePriceReplyStatus(Integer referencePriceReplyStatus) {
        this.referencePriceReplyStatus = referencePriceReplyStatus;
    }

    public String getReferenceDeliveryCycleReply() {
        return referenceDeliveryCycleReply;
    }

    public void setReferenceDeliveryCycleReply(String referenceDeliveryCycleReply) {
        this.referenceDeliveryCycleReply = referenceDeliveryCycleReply;
    }

    public Integer getReferenceDeliveryCycleReplyStatus() {
        return referenceDeliveryCycleReplyStatus;
    }

    public void setReferenceDeliveryCycleReplyStatus(Integer referenceDeliveryCycleReplyStatus) {
        this.referenceDeliveryCycleReplyStatus = referenceDeliveryCycleReplyStatus;
    }

    public String getConsultExecutive() {
        return consultExecutive;
    }

    public void setConsultExecutive(String consultExecutive) {
        this.consultExecutive = consultExecutive;
    }

    public String getConsultExecutiveReply() {
        return consultExecutiveReply;
    }

    public void setConsultExecutiveReply(String consultExecutiveReply) {
        this.consultExecutiveReply = consultExecutiveReply;
    }

    public Integer getConsultExecutiveReplyStatus() {
        return consultExecutiveReplyStatus;
    }

    public void setConsultExecutiveReplyStatus(Integer consultExecutiveReplyStatus) {
        this.consultExecutiveReplyStatus = consultExecutiveReplyStatus;
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    public Integer getQuoteorderGoodsId() {
        return quoteorderGoodsId;
    }

    public void setQuoteorderGoodsId(Integer quoteorderGoodsId) {
        this.quoteorderGoodsId = quoteorderGoodsId;
    }
}