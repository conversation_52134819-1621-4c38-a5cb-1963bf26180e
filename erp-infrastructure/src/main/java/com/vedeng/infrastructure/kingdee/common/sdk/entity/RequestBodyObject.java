package com.vedeng.infrastructure.kingdee.common.sdk.entity;


/* loaded from: k3cloud-webapi-sdk7.9.2.jar:com/kingdee/bos/webapi/entity/RequestBodyObject.class */
public class RequestBodyObject extends JsonBase {
    protected Object[] parameters;

    public RequestBodyObject(Object[] parameters) {
        this.parameters = parameters;
    }

    public Object[] getParameters() {
        return this.parameters;
    }

    public void setParameters(Object[] parameters) {
        this.parameters = parameters;
    }
}
