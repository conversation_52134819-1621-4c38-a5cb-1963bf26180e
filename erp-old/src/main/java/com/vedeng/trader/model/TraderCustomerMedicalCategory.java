package com.vedeng.trader.model;

import java.io.Serializable;

public class TraderCustomerMedicalCategory implements Serializable{

    private Integer traderMedicalCategoryId;

    private Integer traderId;

    private Integer traderType;

    private Integer traderCustomerMedicalCategoryId;

    private Integer traderCustomerId;

    private Integer medicalCategoryId;

    private Integer medicalCategoryLevel;

    public Integer getTraderCustomerMedicalCategoryId() {
        return traderCustomerMedicalCategoryId;
    }

    public void setTraderCustomerMedicalCategoryId(Integer traderCustomerMedicalCategoryId) {
        this.traderCustomerMedicalCategoryId = traderCustomerMedicalCategoryId;
    }

    public Integer getTraderCustomerId() {
        return traderCustomerId;
    }

    public void setTraderCustomerId(Integer traderCustomerId) {
        this.traderCustomerId = traderCustomerId;
    }

    public Integer getMedicalCategoryId() {
        return medicalCategoryId;
    }

    public void setMedicalCategoryId(Integer medicalCategoryId) {
        this.medicalCategoryId = medicalCategoryId;
    }

    public Integer getMedicalCategoryLevel() {
        return medicalCategoryLevel;
    }

    public void setMedicalCategoryLevel(Integer medicalCategoryLevel) {
        this.medicalCategoryLevel = medicalCategoryLevel;
    }

    public Integer getTraderMedicalCategoryId() {
        return traderMedicalCategoryId;
    }

    public void setTraderMedicalCategoryId(Integer traderMedicalCategoryId) {
        this.traderMedicalCategoryId = traderMedicalCategoryId;
    }

    public Integer getTraderId() {
        return traderId;
    }

    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }

    public Integer getTraderType() {
        return traderType;
    }

    public void setTraderType(Integer traderType) {
        this.traderType = traderType;
    }
}