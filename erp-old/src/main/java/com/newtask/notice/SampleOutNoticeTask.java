package com.newtask.notice;

import com.vedeng.common.activiti.entity.ActivitiTaskUnDoEntity;
import com.vedeng.common.activiti.service.ActivitiNoticeService;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.wms.service.WmsSampleOutService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/10/22
 */
@Component
@JobHandler(value = "SampleOutNoticeTask")
public class SampleOutNoticeTask  extends AbstractJobHandler {

    @Autowired
    private WmsSampleOutService sampleOutService;


    @Autowired
    private ActivitiNoticeService activitiNoticeService;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        List<ActivitiTaskUnDoEntity> taskUnDoEntities =  activitiNoticeService.selectTimeoutTaskForNotice("sampleOutAudit_");
        for(ActivitiTaskUnDoEntity taskUnDoEntity : taskUnDoEntities){
            String  x = taskUnDoEntity.getBusinessKey();
            Long lendoutId =Long.parseLong(x.split("_")[1]);

            sampleOutService.sendWeixinNoticeForActiviti(lendoutId,x,taskUnDoEntity.getTaskId(),taskUnDoEntity.getUserName());

            XxlJobLogger.log("处理中"+taskUnDoEntity.getBusinessKey());
        }
        return ReturnT.SUCCESS;
    }



}

