package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.finance.dto.InvoiceDto;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.BatchAfterSalesDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDetailDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveFeeDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveFeeDto;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2023−02-02 下午1:35
 * @description  (蓝票)售后费用应收单，组装数据
 */
@Service
@Slf4j
public class AfterSaleReceiveFeeProcessor extends BaseProcessor<BatchInvoiceDto, BatchReceiveFeeDto> {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;
    @Autowired
    private BatchInvoiceDetailDtoMapper batchInvoiceDetailDtoMapper;
    @Autowired
    private BatchInvoiceDtoMapper batchInvoiceDtoMapper;
    @Autowired
    private BatchAfterSalesDtoMapper batchAfterSalesDtoMapper;


    //金蝶销售售后手续费-费用编码"035"
    private static final String UNIT_KING_DEE_NO = "035";

    //售后单类型-销售售后
    private static final String AFTER_SALE = "535";
    //售后单类型-第三方
    private static final String THRID_DEAL = "537";

    @Override
    public BatchReceiveFeeDto doProcess(BatchInvoiceDto batchInvoiceDto, JobParameters params, ExecutionContext stepContext) throws Exception {
        BatchReceiveFeeDto batchReceiveFeeDto = new BatchReceiveFeeDto();
        //应收单对象 dto
        KingDeeReceiveFeeDto dto = new KingDeeReceiveFeeDto();
        dto.setFQzokBddjtid(Convert.toStr(batchInvoiceDto.getInvoiceId()));
        //判断该蓝字有效票是否有作废票，如果有不推送（不在sql中过滤是为了避免自连接时间过长）
        List<InvoiceDto> invoiceList = batchInvoiceDtoMapper.findDeprecatedBlueInvoice(batchInvoiceDto.getInvoiceId());
        if (invoiceList.size() > 0){
            log.info("(蓝票)售后费用应收单，该蓝字有效票有对应的蓝字作废票，不推送金蝶，发票信息：{}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }
        // 判断是否数据已存在
        boolean old = kingDeeBaseApi.isExist(dto);
        if(old){
            log.info("(蓝票)售后费用应收单,数据已存在:{}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }
        if (null == batchInvoiceDto.getRelatedId()){
            log.info("(蓝票)售后费用应收单,发票id为{},无法找到对应订单",batchInvoiceDto.getInvoiceId());
            return null;
        }
        if (null == batchInvoiceDto.getAfterSalesId()){
            log.info("(蓝票)售后费用应收单,发票id为{},无法找到对应售后",batchInvoiceDto.getInvoiceId());
            return null;
        }
        //组装对象
        this.packReceiveFeeDto(batchInvoiceDto, dto);
        batchReceiveFeeDto.setKingDeeReceiveFeeDto(dto);
        return batchReceiveFeeDto;
    }

    private void packReceiveFeeDto(BatchInvoiceDto batchInvoiceDto,KingDeeReceiveFeeDto dto){
        log.info("销售售后手续费（蓝票）组装数据开始:{}", JSONUtil.toJsonStr(batchInvoiceDto));
        dto.setFdate(DateUtil.formatDate(DateUtil.date(batchInvoiceDto.getAddTime()))) //开票日期
                .setFid("0");
        //第三方安调 FCUSTOMERID取售后单
        BatchAfterSalesDto afterSalesDto = batchAfterSalesDtoMapper.findByAfterSalesId(batchInvoiceDto.getAfterSalesId());
        if (StrUtil.equals(AFTER_SALE,Convert.toStr(afterSalesDto.getSubjectType()))){
            dto.setFcustomerid(Convert.toStr(batchInvoiceDto.getTraderCustomerId()));
        }else {
            dto.setFcustomerid(Convert.toStr(batchAfterSalesDtoMapper.getTraderCustomerIdByAfterSalesId(batchInvoiceDto.getAfterSalesId())));
        }
        //发票详情
        List<BatchInvoiceDetailDto> invoiceDetailList = batchInvoiceDetailDtoMapper.findByInvoiceId(batchInvoiceDto.getInvoiceId());
        //税率
        DecimalFormat decimalFormat = new DecimalFormat("0.00#");
        String taxRate = Objects.isNull(batchInvoiceDto.getRatio()) ? "0.00" : decimalFormat.format(batchInvoiceDto.getRatio().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP));
        invoiceDetailList.forEach(d -> {
            //应收单详情对象 receiveFeeDetailDto
            KingDeeReceiveFeeDetailDto receiveFeeDetailDto = new KingDeeReceiveFeeDetailDto()
                    .setFPriceQty(Convert.toStr(d.getNum()))
                    .setFTaxPrice(Convert.toStr(d.getTotalAmount().divide(d.getNum(),6,RoundingMode.HALF_UP)))
                    .setFEntryTaxRate(taxRate)
                    .setFQzokBddjhid(Convert.toStr(d.getInvoiceDetailId()));
            receiveFeeDetailDto.setFcostid(UNIT_KING_DEE_NO);
            dto.getFEntityDetail().add(receiveFeeDetailDto);
        });
    }
}
