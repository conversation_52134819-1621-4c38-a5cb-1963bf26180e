package com.vedeng.common.core.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

@Slf4j
public class ErpSpringBeanUtil implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    private static WebApplicationContext webApplicationContext = ContextLoader.getCurrentWebApplicationContext();

    @Override
    public void setApplicationContext(ApplicationContext context) throws BeansException {
        setContext(context);
    }

    public static void setContext(ApplicationContext context) {
        applicationContext = context;
    }

    public static <T> T getBean(Class<T> clazz) {
        if (webApplicationContext == null) {
            log.info("【getBean】 上下文为空，重新获取web容器");
            webApplicationContext = ContextLoader.getCurrentWebApplicationContext();
        }
        try {
            if (webApplicationContext == null) {
                log.info("【getBean】 上下文为空，重新获取web容器");
                webApplicationContext = ContextLoader.getCurrentWebApplicationContext();
            }
            // 优先从WebApplicationContext获取
            return webApplicationContext.getBean(clazz);
        } catch (Exception e) {
            log.error("【getBean】 处理异常",e);
        }
        try {
            return applicationContext.getBean(clazz);
        } catch (Exception e) {
            log.error("【getBean】 处理异常",e);
        }
        return null;
    }

    public static <T> T getBean(String name, Class<T> clazz) {

        if (!StringUtils.isNotEmpty(name)) {
            return null;
        }
        try {
            return webApplicationContext.getBean(clazz);
        } catch (Exception e) {
            log.error("【getBean】 处理异常",e);
        }
        try {
            return applicationContext.getBean(name, clazz);
        } catch (Exception e) {
            log.error("【getBean】 处理异常",e);
        }
        return null;
    }
}
