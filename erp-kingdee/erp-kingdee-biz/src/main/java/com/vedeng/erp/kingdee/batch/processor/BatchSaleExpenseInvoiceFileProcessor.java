package com.vedeng.erp.kingdee.batch.processor;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import com.vedeng.erp.kingdee.batch.handle.InvoiceFileHandle;
import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import com.vedeng.erp.kingdee.dto.OutPutFeePlainInvoiceDto;
import com.vedeng.erp.kingdee.dto.OutPutFeeSpecialInvoiceDto;
import com.vedeng.erp.kingdee.service.KingDeeFileDataService;
import com.vedeng.erp.kingdee.service.KingDeeOutPutFeePlainInvoiceService;
import com.vedeng.erp.kingdee.service.KingDeeOutPutFeeSpecialInvoiceService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 销售费用发票附件处理（红蓝票公用,Pdf和Xml公用）
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/1 11:05
 */
@Service
@Slf4j
public class BatchSaleExpenseInvoiceFileProcessor extends BaseProcessor<BatchInvoiceDto, KingDeeFileDataDto> {

    @Autowired
    private KingDeeFileDataService kingDeeFileDataService;

    @Autowired
    private KingDeeOutPutFeeSpecialInvoiceService kingDeeOutPutFeeSpecialInvoiceService;

    @Autowired
    private KingDeeOutPutFeePlainInvoiceService kingDeeOutPutFeePlainInvoiceService;

    @Autowired
    private InvoiceFileHandle invoiceFileHandle;

    @Override
    public KingDeeFileDataDto doProcess(BatchInvoiceDto invoiceDto, JobParameters params, ExecutionContext stepContext) throws Exception {
        if (StringUtils.isBlank(invoiceDto.getOssFileUrl())) {
            log.info("当前销售费用发票没有回传附件, invoice:{}", JSON.toJSONString(invoiceDto));
            return null;
        }
        // 专票
        if (invoiceDto.getInvoiceTypeName().contains(InvoiceFileHandle.SPECIAL_INVOICE)) {
            // step 1: 校验对应的发票 有没有推送至金蝶（若发票已经推送至金蝶，则证明满足了所有的校验条件，因此这边不需要再校验蓝字作废、实物or费用等条件了）
            OutPutFeeSpecialInvoiceDto query = new OutPutFeeSpecialInvoiceDto();
            query.setFQzokBddjtid(invoiceDto.getInvoiceId().toString());
            kingDeeOutPutFeeSpecialInvoiceService.query(query);
            if (query.getFid() == null || InvoiceFileHandle.ZERO.equals(query.getFid())) {
                log.info("当前销售费用发票专票未推送金蝶,无需推送附件：{}", JSON.toJSONString(invoiceDto));
                return null;
            }

            // step2： 校验当前这个附件 有没有推送过（根据本地附件回写表判断）
            KingDeeFileDataDto existPdfParam = KingDeeFileDataDto.builder()
                    .formId(KingDeeFormConstant.OUTPUT_FEE_SPECIAL_INVOICE)
                    .erpId(invoiceDto.getInvoiceId().toString())
                    .url(invoiceDto.getOssFileUrl())
                    .build();
            List<KingDeeFileDataDto> existFile = kingDeeFileDataService.getByBusinessIdAndUri(existPdfParam);
            if (CollectionUtils.isEmpty(existFile)) {
                // 先处理T_INVOICE中的pdf格式的
                return invoiceFileHandle.createKingDeeFileDataDto(invoiceDto, KingDeeFormConstant.OUTPUT_FEE_SPECIAL_INVOICE, query.getFid());
            }
        }

        // 普票
        if (invoiceDto.getInvoiceTypeName().contains(InvoiceFileHandle.PLAIN_INVOICE)) {
            // step 1: 校验对应的发票 有没有推送至金蝶（若发票已经推送至金蝶，则证明满足了所有的校验条件，因此这边不需要再校验蓝字作废、实物or费用等条件了）
            OutPutFeePlainInvoiceDto query = new OutPutFeePlainInvoiceDto();
            query.setFQzokBddjtid(invoiceDto.getInvoiceId().toString());
            kingDeeOutPutFeePlainInvoiceService.query(query);
            if (query.getFid() == null || InvoiceFileHandle.ZERO.equals(query.getFid())) {
                log.info("当前销售费用发票普票未推送金蝶,无需推送附件：{}", JSON.toJSONString(invoiceDto));
                return null;
            }
            // step2： 校验当前这个附件 有没有推送过（根据本地附件回写表判断）
            KingDeeFileDataDto existPdfParam = KingDeeFileDataDto.builder()
                    .formId(KingDeeFormConstant.OUTPUT_FEE_PLAIN_INVOICE)
                    .erpId(invoiceDto.getInvoiceId().toString())
                    .url(invoiceDto.getOssFileUrl())
                    .build();
            List<KingDeeFileDataDto> existFile = kingDeeFileDataService.getByBusinessIdAndUri(existPdfParam);
            if (CollectionUtils.isEmpty(existFile)) {
                // 先处理T_INVOICE中的pdf格式的
                return invoiceFileHandle.createKingDeeFileDataDto(invoiceDto, KingDeeFormConstant.OUTPUT_FEE_PLAIN_INVOICE, query.getFid());
            }
        }
        return null;
    }
}