const E = window.wangEditor
function initForm(){

    layui.use(['form','laydate', 'table','dropdown','colorpicker'], function(){
        var $ = layui.jquery,
            form = layui.form,
            table = layui.table;
        form.render();

        var dropdown = layui.dropdown;
        if($(".dropdown_button").length>0){
            $(".dropdown_button").each(function(){
                var p= $(this).attr("itemsJson");
                var j=JSON.parse(p);
                for(let i=0;i<j.length;i++){
                    j[i]['title'] =j[i]['itemLabel'];
                    j[i]['id'] =j[i]['itemName'];
                }
                //初演示
                dropdown.render({
                    elem:  $(this)
                    ,data:j
                    ,click: function(obj){
                        if(obj.area==undefined){
                            ezopen(obj.openType,obj.windowName,obj.itemUrl);
                        }else{
                            ezopen(obj.openType,obj.windowName,obj.itemUrl,obj.area);
                        }
                        //   layer.alert('点击了：'+JSON.stringify(obj)  , this.elem, {tips: [1, '#5FB878']})
                    }
                });
            })
        }

        var colorpicker=layui.colorpicker;
        colorpicker.render({
            elem: '.colorpick',
            done: function(color){
                console.log(this.elem, color);
                $(this.elem).removeClass("layui-inline");
                 $(this.elem).parent().find("input").val(color);
            },change: function(color){ //颜色改变的回调
                if(colorChange){
                    colorChange(color);
                }
            }
        });
        $(".colorpick").removeClass("layui-inline");



        $(".J-reset").click(function(){
            form.val("searchForm", getobj());
        })

        var laydate = layui.laydate;
        $(".ez-daterange-parent").each(function(){
            var _this=$(this);
            renderDateParent(_this);
        })
    })
    $(".ez-help").each(function(){
        $(this).click(function(){
            var url=$(this).attr("src");
            var tips=$(this).attr("tips");
            var id=$(this).attr("id");
            if(url){
                layer.open({
                    type: 2,
                    title: '说明',
                    shadeClose: true,
                    shade: 0.3,
                    maxmin: true, //开启最大化最小化按钮
                    area: ['90%', '90%'],
                    content: url
                });
            }
            if(tips){
                layer.tips(tips, '#'+id, {
                    tips: [1, '#3595CC'],
                    time: 4000
                });
            }
        })
    });
    $(".richtext").each(function(){
        var id=$(this).attr("id");
        var $text1 = $("#DESC_"+id)
        let editor = E.createEditor({
            selector: "#editor_"+id,
            html:$text1.val(),
            mode: 'simple',
            config: {
                placeholder: '请输入...',
                MENU_CONF: {
                    uploadImage: {
                        fieldName: 'file',
                        server: '/system/uploadDesc.html',
                        maxFileSize: 1 * 1024 * 1024,
                        allowedFileTypes: ['image/*']//,
                      //  base64LimitSize: 10 * 1024 * 1024 // 10M 以下插入 base64
                    }
                },
                onChange() {
                }
            }
        });
        editor.getConfig().onChange=function(){
            $text1.val(editor.getHtml()) ;
        }
        let toolbar = E.createToolbar({
            editor,
            mode: 'simple',
            selector:  "#toolbar_"+id,
            config: {}
        })
    })


    $(".ez-xmselect").each(function(){
        var xmel=$(this);
        renderXmselect(xmel);
    })
    $(".ez-laycascader").each(function(){
        var _this=$(this);
        renderCascader(_this);
    })
}

function istrue(c){
   return  (c||'true')=='true'||(c||'1')=='1';
}

function renderCascader(cas){
    layui.use('layCascader', function () {
        try {
            var layCascader = layui.layCascader;
            var _this = $(cas);
            var url = _this.attr("ez_url");
            var ezurl = _this.attr("ezlist_url");
            var value = _this.attr("ez_value") || 'VALUE';
            var label = _this.attr("ez_label") || 'LABEL';
            var children = _this.attr("ez_children") || 'CHILDREN';
            var multiple = istrue(_this.attr("multi"));
            var itemsJson = _this.attr("itemsJson");
            // debugger
            var itemPlaceholder = _this.attr("placeholder") || '请选择';
            var paramValue = _this.val() || '[]';
            var collapseTags = istrue(_this.attr("collapsetags"));
            var showAllLevels = istrue(_this.attr("showalllevels"));

            if (url || ezurl) {
                $.get(url || ezurl, function (data) {
                    var res = data.data;
                    var prop = {};
                    prop.value = value;
                    prop.label = label;
                    prop.children = children;
                    prop.multiple = multiple;

                    console.log(prop);
                    if (ezurl) {
                        res = flatToTree(res, 0);
                    }
                    layCascader({
                        elem: _this[0],
                        props: prop,
                        filterable: true,
                        filterMethod: function (node, val) {//重写搜索方法。
                            if (val == node.data[label]) {//把value相同的搜索出来

                                return true;
                            }
                            if ((node.data[label] + node.data[label]).indexOf(val) != -1) {//名称中包含的搜索出来
                                return true;
                            }
                            //  console.log(node.data.orgName+node.data.orgNames+'##'+(node.data.orgId+'').indexOf(val));
                            return !ezpingyin(val, (node.data[label] + node.data[label]), (node.data[value] + ''));
                        },
                        clearable: true,
                        placeholder: itemPlaceholder,
                        collapseTags: collapseTags,
                        showAllLevels: showAllLevels,
                        value: paramValue,
                        options: res
                    });
                })
            } else if (itemsJson) {
                var res = JSON.parse(itemsJson);
                var prop = {};
                prop.value = value;
                prop.label = label;
                prop.children = children;
                prop.multiple = multiple;
                // res=flatToTree(res,0);
                layCascader({
                    elem: _this[0],
                    props: prop,
                    filterable: true,
                    filterMethod: function (node, val) {//重写搜索方法。
                        if (val == node.data[label]) {//把value相同的搜索出来

                            return true;
                        }
                        if ((node.data[label] + node.data[label]).indexOf(val) != -1) {//名称中包含的搜索出来
                            return true;
                        }
                        //  console.log(node.data.orgName+node.data.orgNames+'##'+(node.data.orgId+'').indexOf(val));
                        return !ezpingyin(val, (node.data[label] + node.data[label]), (node.data[value] + ''));
                    },
                    clearable: true,
                    placeholder: itemPlaceholder,
                    collapseTags: collapseTags,
                    value: paramValue,
                    options: res
                });
            }
        }catch (e) {
            
        }

    })
    
}

function renderXmselect(xm){
    try{
    var xmel=$(xm);
    var initdata=xmel.attr("itemsJson");
    var initvalue=xmel.attr("value");
    var itemName=xmel.attr("name");
    var itemPlaceholder=xmel.attr("itemPlaceholder");
    xmSelect.render({
        el: xmel[0],
        language: 'zn',
        filterable: true,
        filterMethod: function (val, item, index, prop) {//重写搜索方法。
            if(val == item.K){//把value相同的搜索出来
                return true;
            }
            if(item.V.indexOf(val) != -1){//名称中包含的搜索出来
                return true;
            }
            return !ezpingyin(val, item.V, item.K);
        },
        style: {
            height: '26px'  ,
        },
        prop: {
            name: 'V',
            value: 'K',
        },
        name: itemName,
        tips: itemPlaceholder,
        data:  JSON.parse(initdata),
        initValue:  JSON.parse(initvalue)
    })
    }catch(e){

    }
}


function renderDateParent(_this){
    var date=_this.find(".ez-daterange>input");
    if(date.length==2){
        var start=date[0];
        var end=date[1];
        renderDate(start,end,"date");
    }
    var datetime=_this.find(".ez-datetimerange>input");
    if(datetime.length==2){
        var start=datetime[0];
        var end=datetime[1];
        renderDate(start,end,"datetime");
    }
}

function renderDate(dateStart,dateEnd,type){
    try {
        if (dateStart == undefined || dateStart.length < 1
            || dateEnd == undefined || dateEnd.length < 1) {
            return;
        }
        if (type == undefined) {
            type = 'date'
        }
        var holiday=[[],[]];
        if($("#holiday")!=null&&$("#holiday").val()!=null){
            holiday=JSON.parse($("#holiday").val());
        }
        var shortcut=[
            {
                text: "昨天",
                value: function(){
                    var now = new Date();
                    now.setDate(now.getDate() - 1);
                    return now;
                }()
            },
            { text: "今天",  value:function(){
                    const now = new Date();
                    return now;
                }() },
            {
                text: "本月月初",
                value: function(){
                    // 获取当前日期
                    const today = new Date();
                    const year = today.getFullYear();
                    const month = today.getMonth();
                    // 创建一个新的 Date 对象，设置为本月1号
                    const firstDayOfMonth = new Date(year, month, 1);
                    return firstDayOfMonth;
                }()
            },
            {
                text: "上月月初",
                value: function(){
                    // 获取当前日期
                    const today = new Date();
// 获取上个月的年份和月份
                    const year = today.getFullYear();
                    const month = today.getMonth() - 1;
// 创建一个新的 Date 对象，设置为上个月1号
                    const firstDayOfLastMonth = new Date(year, month, 1);
// 获取上个月的最后一天
                    const lastDayOfLastMonth = new Date(year, month + 1, 0);
                    return [firstDayOfLastMonth];
                }()
            },
            {
                text: "上月月末",
                value: function(){
                    // 获取当前日期
                    const today = new Date();
// 获取上个月的年份和月份
                    const year = today.getFullYear();
                    const month = today.getMonth() - 1;
// 创建一个新的 Date 对象，设置为上个月1号
                    const firstDayOfLastMonth = new Date(year, month, 1);
// 获取上个月的最后一天
                    const lastDayOfLastMonth = new Date(year, month + 1, 0);
                    return [lastDayOfLastMonth];
                }()
            },{
                text:"前30天",
                value:function(){
                    const today = new Date();
                    const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
                    return thirtyDaysAgo;
                }()
            },
            {
                text: "今年年初",
                value: function(){
                    const now = new Date();
                    const year = now.getFullYear();
                    const januaryFirst = new Date(year, 0, 1);
                    return januaryFirst;
                }()
            },
            {
                text: "去年年初",
                value: function(){
                    const now = new Date();
                    const year = now.getFullYear();
                    const januaryFirst = new Date(year-1, 0, 1);
                    return januaryFirst;
                }()
            },
            {
                text: "去年年末",
                value: function(){
                    const now = new Date();
                    const year = now.getFullYear();
                    const januaryFirst = new Date(year-1, 11, 31);
                    return januaryFirst;
                }()
            }
        ];
        layui.use(['laydate'], function () {
            var laydate = layui.laydate;
            var starDate = laydate.render({
                elem: dateStart, type: type,
                shortcuts:shortcut ,
                holidays: holiday,
                done: function (value, data) {
                    if (value != "") {
                        endDate.config.min = {
                            year: data.year,
                            month: data.month - 1,
                            date: data.date,
                            hours: type == 'date' ? 0 : data.hours,
                            minutes: type == 'date' ? 0 : data.minutes,
                            seconds: type == 'date' ? 0 : data.seconds,
                        }
                    }
                }
            });
            //常规用法
            var endDate = laydate.render({
                elem: dateEnd, type: type,
                shortcuts:shortcut ,
                holidays: holiday,
                done: function (value, data) {
                    if (value != "") {
                        starDate.config.max = {
                            year: data.year,
                            month: data.month - 1,
                            date: data.date,
                            hours: type == 'date' ? 0 : data.hours,
                            minutes: type == 'date' ? 0 : data.minutes,
                            seconds: type == 'date' ? 0 : data.seconds,
                        }
                    }
                }
            });

            if(dateStart.value!=''){
                var date=new Date(dateStart.value);
                var year=date.getFullYear();
                var month=date.getMonth();
                var day=date.getDate();
                var hours=date.getHours();
                var minutes=date.getMinutes();
                var seconds=date.getSeconds();
                endDate.config.min = {
                    year: year,
                    month: month,
                    date: day,
                    hours: type == 'date' ? 0 : data.hours,
                    minutes: type == 'date' ? 0 : data.minutes,
                    seconds: type == 'date' ? 0 : data.seconds,
                }
            }
            if(dateEnd.value!=''){
                var date=new Date(dateEnd.value);
                var year=date.getFullYear();
                var month=date.getMonth();
                var day=date.getDate();
                var hours=date.getHours();
                var minutes=date.getMinutes();
                var seconds=date.getSeconds();
                starDate.config.max = {
                    year: year,
                    month: month,
                    date: day,
                    hours: type == 'date' ? 0 : data.hours,
                    minutes: type == 'date' ? 0 : data.minutes,
                    seconds: type == 'date' ? 0 : data.seconds,
                }
            }

        })
    }catch(e){}
}



function val(targetCentent, name) {
    try {
        var label = targetCentent.find("[name=" + name + "]").val()
        return label;
    } catch (e) {
        console.log(e)
        return "";
    }
}
function setVal(targetCentent, name,value) {
    try {
        targetCentent.find("[name=" + name + "]").val(value)
    } catch (e) {
        console.log(e)
    }
}
//获取表格中所有ID
function getAllIds() {
    var ids = "";
    $("input[name='row_data_id']").each(function () {
        ids = ids + $(this).val() + ","
    })
    console.log("获取到业务ID: " + ids + '0');
    return ids + '0';
}
//获取表格中所有选中ID  name为list-body-checkbox
function getCheckIdsUrl() {
    var goodsIdArr="-1";
    $("input[name='list-body-checkbox']:not(:disabled)").each(function () {
        if (this.checked) {
            goodsIdArr+=','+$(this).attr("_CHECK_ID_VALUE");
        }
    })
    if(goodsIdArr=="-1"){
        return "";
    }
    return "_CHECKD_IDS=" + encodeURI(goodsIdArr);
}
function getJsonCheckIds() {

    var lines=[];
    $("input[name='list-body-checkbox']:not(:disabled)").each(function () {
        if (this.checked) {
            lines.push($(this).attr("_CHECK_ID_VALUE"));
        }
    })
    return JSON.stringify(lines);
}

function getJsonCheckIdAndNames() {

    var lines=[];
    $("input[name='list-body-checkbox']:not(:disabled)").each(function () {
        if (this.checked) {
            var line={};

            line.ID=$(this).attr("_CHECK_ID_VALUE");

            var inputs=$(this).parent().find("[type=hidden]");

            for(let i=0;i<inputs.length;i++){
                var cname=$(inputs[i]).attr("item_name");
                var value=$(inputs[i]).val();
                line[cname]=value;
            }

            lines.push(line);
        }
    })
    return JSON.stringify(lines);
}

function getJsonRowIds(row) {

    var lines=[];
    // $("input[name='list-body-checkbox']").each(function () {
    //     if (this.checked) {
    //         lines.push($(this).attr("_CHECK_ID_VALUE"));
    //     }
    // })
    lines.push(row.find("[name='row_data_hidden_ID']").val());
    return JSON.stringify(lines);
}

function getJsonRowIdAndNames(row,name) {
    var lines=[];
    var line={};
    line.ID=row.find("[name='row_data_hidden_ID']").val();

    var inputs=row.find("[type=hidden]");

     for(let i=0;i<inputs.length;i++){
        var cname=$(inputs[i]).attr("item_name");
        var value=$(inputs[i]).val();
        line[cname]=value;
    }
    lines.push(line);
    return JSON.stringify(lines);
}

var $searchWrap=$("#searchForm")
var getSearchParams = function () {
    var params = [];
    $("#searchForm").find('input,select').each(function () {
        if ($(this).attr('name')) {
            params.push($(this).attr('name') + '=' + encodeURI($(this).val()));
        }
    })
    return params.join('&');
};
function openModel(url,name,area){
    var json=['90%', '90%'];
    if(area!==undefined && area !=''){
        try{
            json= area.split(",");
        }catch(e){
            json=area;
        }
        if(json.length==1){
            json=area;
        }
    }
    name=name==undefined?"窗口":name;
    var index = layer.open({
        title: name,
        type: 2,
        shade: 0.2,
        maxmin:true,
        shadeClose: true,
        area: json,
        content: url,
        moveOut:true
    });
    return index;
}
function openModelCallback(url,name,callback){
    name=name==undefined?"窗口":name;
    var index = layer.open({
        title: name,
        type: 2,
        shade: 0.2,
        maxmin:true,
        shadeClose: true,
        area: ['90%', '90%'],
        content: url,
        moveOut:true
    });
    return index;
}
function openModelReload(url,name,cancel){
    name=name==undefined?"窗口":name;
    var index = layer.open({
        title: name,
        type: 2,
        shade: 0.2,
        maxmin:true,
        shadeClose: true,
        area: ['90%', '90%'],
        content: url,
        moveOut:true,
        cancel: cancel
    });
}



function  openTab( title,link){
    var uniqueName = link.replace('./', '').replace(/["&'./:=%?[\]]/gi, '-').replace(/(--)/gi, '');
    window.parent.postMessage({
        from:'ez',
        name: title,
        url:link,
        id:"tab-"+uniqueName
    }, '*');
}

function openBlank(appendUrl){
    var a = document.createElement("a");
    a.setAttribute("href", appendUrl);
    a.setAttribute("target", "_blank");
    a.setAttribute("id", "camnpr");
    document.body.appendChild(a);
    a.click();
    a.remove();
}

//parentid,id,name

function flatToTree(arr, parentId){
       console.log('flagtotree='+arr);
        let tree = [];
        arr.forEach(item => {
            if (item.PARENT_ID == parentId) {
                let children = flatToTree(arr, item.ID);
                if (children.length > 0) {
                    item.CHILDREN = children;
                }
                tree.push(item);
            }
        });
        return tree;

}


function watermark(settings) {
    if($(".mask_div").length>0){
        return;
    }
    //默认设置
    var defaultSettings = {
        watermark_txt: "",
        watermark_x: 20, //水印起始位置x轴坐标
        watermark_y: 20, //水印起始位置Y轴坐标
        watermark_rows: 0, //水印行数
        watermark_cols: 0, //水印列数
        watermark_x_space: 100, //水印x轴间隔
        watermark_y_space: 50, //水印y轴间隔
        watermark_color: '#aaa', //水印字体颜色
        watermark_alpha: 0.1, //水印透明度
        watermark_fontsize: '12px', //水印字体大小
        watermark_font: '宋体', //水印字体
        watermark_width: 210, //水印宽度
        watermark_height: 80, //水印长度
        watermark_angle: 20 //水印倾斜度数
    };
    if (arguments.length === 1 && typeof arguments[0] === "object") {
        var src = arguments[0] || {};
        for (key in src) {
            if (src[key] && defaultSettings[key] && src[key] === defaultSettings[key]) continue;
            else if (src[key]) defaultSettings[key] = src[key];
        }
    }
    var oTemp = document.createDocumentFragment();
    //获取页面最大宽度
    var page_width = Math.max(document.body.scrollWidth, document.body.clientWidth);
    var cutWidth = page_width * 0.0150;
    var page_width = page_width - cutWidth;
    //获取页面最大高度
    var page_height = Math.max(document.body.scrollHeight, document.body.clientHeight) + 30;
    page_height = Math.max(page_height, window.innerHeight - 30);
    //如果将水印列数设置为0，或水印列数设置过大，超过页面最大宽度，则重新计算水印列数和水印x轴间隔
    if (defaultSettings.watermark_cols == 0 || (parseInt(defaultSettings.watermark_x + defaultSettings.watermark_width * defaultSettings.watermark_cols + defaultSettings.watermark_x_space * (defaultSettings.watermark_cols - 1)) > page_width)) {
        defaultSettings.watermark_cols = parseInt((page_width - defaultSettings.watermark_x + defaultSettings.watermark_x_space) / (defaultSettings.watermark_width + defaultSettings.watermark_x_space));
        defaultSettings.watermark_x_space = parseInt((page_width - defaultSettings.watermark_x - defaultSettings.watermark_width * defaultSettings.watermark_cols) / (defaultSettings.watermark_cols - 1));
    }
    //如果将水印行数设置为0，或水印行数设置过大，超过页面最大长度，则重新计算水印行数和水印y轴间隔
    if (defaultSettings.watermark_rows == 0 || (parseInt(defaultSettings.watermark_y + defaultSettings.watermark_height * defaultSettings.watermark_rows + defaultSettings.watermark_y_space * (defaultSettings.watermark_rows - 1)) > page_height)) {
        defaultSettings.watermark_rows = parseInt((defaultSettings.watermark_y_space + page_height - defaultSettings.watermark_y) / (defaultSettings.watermark_height + defaultSettings.watermark_y_space));
        defaultSettings.watermark_y_space = parseInt(((page_height - defaultSettings.watermark_y) - defaultSettings.watermark_height * defaultSettings.watermark_rows) / (defaultSettings.watermark_rows - 1));
    }
    var x;
    var y;
    for (var i = 0; i < defaultSettings.watermark_rows-1; i++) {
        y = defaultSettings.watermark_y + (defaultSettings.watermark_y_space + defaultSettings.watermark_height) * i;
        for (var j = 0; j < defaultSettings.watermark_cols; j++) {
            x = defaultSettings.watermark_x + (defaultSettings.watermark_width + defaultSettings.watermark_x_space) * j;
            var mask_div = document.createElement('div');
            mask_div.id = 'mask_div' + i + j;
            mask_div.className = 'mask_div';
            mask_div.appendChild(document.createTextNode(defaultSettings.watermark_txt));
            //设置水印div倾斜显示
            mask_div.style.webkitTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
            mask_div.style.MozTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
            mask_div.style.msTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
            mask_div.style.OTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
            mask_div.style.transform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
            mask_div.style.visibility = "";
            mask_div.style.position = "absolute";
            mask_div.style.left = x + 'px';
            mask_div.style.top = y + 'px';
            mask_div.style.overflow = "hidden";
            mask_div.style.zIndex = "9999";
            //让水印不遮挡页面的点击事件
            mask_div.style.pointerEvents = 'none';
            mask_div.style.opacity = defaultSettings.watermark_alpha;
            mask_div.style.fontSize = defaultSettings.watermark_fontsize;
            mask_div.style.fontFamily = defaultSettings.watermark_font;
            mask_div.style.color = defaultSettings.watermark_color;
            mask_div.style.textAlign = "center";
            mask_div.style.width = defaultSettings.watermark_width + 'px';
            mask_div.style.height = defaultSettings.watermark_height + 'px';
            mask_div.style.display = "block";
            oTemp.appendChild(mask_div);
        };
    };
    document.body.appendChild(oTemp);
}

function getNow() {
    var d = new Date();
    var year = d.getFullYear();
    var month = change(d.getMonth() + 1);
    var day = change(d.getDate());
    var hour = change(d.getHours());
    var minute = change(d.getMinutes());
    var second = change(d.getSeconds());

    function change(t) {
        if (t < 10) {
            return "0" + t;
        } else {
            return t;
        }
    }
    var time = year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second + '';
    return time;
}




$(document).on('click', '.ezopenbutton', function () {
    var btn = $(this);
    if (btn.attr("item_url") == '#') {
        return;
    }
    var editor = btn.attr("editor")
    var appendUrl = btn.attr("ITEM_URL");
    var openType = btn.attr("ITEM_OPEN_TYPE");
    var area= btn.attr("area");
    var title=btn.attr("ITEM_OPEN_TITLE")==undefined?"打开":btn.attr("ITEM_OPEN_TITLE");
    ezopen(openType,title,appendUrl,area);
})

function ezopen(openType,title,appendUrl,area){
    switch (openType) {
        case 'APPEND_PARAM':
            if (appendUrl!=null&&appendUrl.indexOf('?') <= 0) {
                appendUrl += '?' + getSearchParams() + '&' + getCheckIdsUrl();
            } else {
                appendUrl += '&' + getSearchParams() + '&' + getCheckIdsUrl();
            }
            openModel(appendUrl, title,area);
            break;
        case 'MODEL':
            openModel(appendUrl, title,area);
            break;
        case 'CONFIRM_MODEL':
            layer.confirm('确认操作?', {icon: 3, title:'提示'}, function(index){
                layer.close(index);
                openModel(appendUrl, title,area);
            })
            break;
        case '_BLANK':
            openBlank(appendUrl);
            break;
        case '_BLANK_PARAM':
            if (appendUrl.indexOf('?') <= 0) {
                appendUrl += '?' + getSearchParams() + '&' + getCheckIdsUrl();
            } else {
                appendUrl += '&' + getSearchParams() + '&' + getCheckIdsUrl();
            }
            openBlank(appendUrl);
            break;
        case '_BLANK_PARAM_COLUMN':
            if (appendUrl.indexOf('?') <= 0) {
                appendUrl += '?' + getSearchParams() + '&' + getCheckIdsUrl();
            } else {
                appendUrl += '&' + getSearchParams() + '&' + getCheckIdsUrl();
            }
            var key = 'EZ_CONFIG_' + $("#ENCRYPT_LIST_ID").val();
            var jsonconfig = localStorage.getItem(key);
            if (jsonconfig != undefined) {
                var json = JSON.parse(jsonconfig);
                var search = json.search;
                var column = json.column;
                if (jsonconfig != column&& column.length>0) {
                    var columnurl="";
                    for (let index=0;index<column.length;index++) {
                        columnurl+=column[index]+","
                    }
                    appendUrl+="_BLANK_PARAM_COLUMN="+columnurl;
                }


            }

            openBlank(appendUrl);
            break;
        case 'LOCATION':
            location.href = appendUrl;
            break;
        case 'PARENT':
            openTab(title,appendUrl)

            break;
        case 'AJAX':
            $.getJSON(appendUrl,function(result){
                if(result.success){
                    layer.alert("操作成功",function(index){
                        location.reload();
                    })
                }else{
                    layer.alert("操作失败")
                }
            })
            // openTab(title,appendUrl)
            break;
        case 'CONFIRM_AJAX':
            layer.confirm('确认操作?', {icon: 3, title:'提示'}, function(index){
                $.getJSON(appendUrl,function(result){
                    if(result.success){
                        layer.alert("操作成功",function(index){
                            location.reload();
                        })
                    }else{
                        layer.alert("操作失败"+result.message)
                    }
                })
                layer.close(index);
            });

            // openTab(title,appendUrl)
            break;
        default:
            openModel(appendUrl, title,area);
            console.log('无opentype默认model')
    }
}



//判断是否是手机
function IsMobile() {
    return $("#_EZ_MOBILE_FLAG").val()==='1'
}
function toFloat(num){
    var result=parseFloat(num);
    if(isNaN(result)){
        return 0;
    }return result;
}



//打开选择窗口
$(document).on('click', '.ez-select', function () {
    let url=$(this).attr("item_url");
    let callback=$(this).attr("callback");
    var json=['90%', '90%'];
    var _btn=$(this);
    var index = layer.open({
        title: "请选择" ,
        type: 2,
        shade: 0.2,
        maxmin:true,
        shadeClose: true,
        area: json,
        content: url,
        moveOut:true,
        btn: ['确定', '取消'],
        yes: function(i,layero){
            var iframeWin = window[layero.find('iframe')[0]['name']];
            var ids=iframeWin.getJsonCheckIds();
            var lines=iframeWin.getJsonCheckIdAndNames();
            $(this).attr("ids",ids);
            $(this).attr("lines",lines);
            window[callback](ids,lines,_btn);
        }
    });
})
//行选择事件
$(document).on('click', '.ezrowselectbutton', function () {
    var id=$(this).attr("id");
    var name=$(this).attr("fname");
    var ezcallback=$(this).attr("ez_callback")||$(this).attr("item_url");
   // var doc=$(itemUrl,window.parent.document);
    var row=$(this).parents("tr");
    var ids=getJsonRowIds(row);
    var lines=getJsonRowIdAndNames(row,name);
    window.parent[ezcallback]&&window.parent[ezcallback](ids,lines);
})
//弹框选择
$(document).on('click', '.ezcheckbutton', function () {
    var id=$(this).attr("id");
    var name=$(this).attr("fname");
    var ezcallback=$(this).attr("ez_callback")||$(this).attr("item_url");
    var doc=$(ezcallback,window.parent.document);
    var ids=getJsonCheckIds();
    var lines=getJsonCheckIdAndNames();
    window.parent[ezcallback]&&window.parent[ezcallback](ids,lines);
})