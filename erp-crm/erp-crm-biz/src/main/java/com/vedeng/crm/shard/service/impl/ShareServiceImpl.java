package com.vedeng.crm.shard.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.enums.JumpErpTitleEnum;
import com.vedeng.common.trace.enums.BizLogEnum;
import com.vedeng.crm.api.ShareApiService;
import com.vedeng.crm.common.service.JumpService;
import com.vedeng.crm.shard.service.ShareService;
import com.vedeng.erp.business.domain.dto.BusinessChanceDto;
import com.vedeng.erp.business.domain.dto.BusinessLeadsDto;
import com.vedeng.erp.business.service.BusinessChanceService;
import com.vedeng.erp.business.service.BusinessLeadsService;
import com.vedeng.erp.common.dto.RSalesJBusinessOrderDto;
import com.vedeng.erp.system.dto.OperationLogDto;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.OperationLogApiService;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.domain.entity.RSalesJBusinessOrderEntity;
import com.vedeng.erp.trader.mapper.RSalesJBusinessOrderMapper;
import com.vedeng.erp.trader.mapstruct.RSalesJBusinessOrderConvertor;
import com.vedeng.infrastructure.feign.uac.UacWxUserInfoApiService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/7/18
 */
@Service
@Slf4j
public class ShareServiceImpl implements ShareService, ShareApiService {


    @Autowired
    private RSalesJBusinessOrderMapper rSalesJBusinessOrderMapper;

    @Autowired
    private RSalesJBusinessOrderConvertor rSalesJBusinessOrderConvertor;

    @Autowired
    private OperationLogApiService operationLogApiService;
    
    @Autowired
    private UacWxUserInfoApiService uacWxUserInfoApiService;
    
    @Autowired
    private BusinessLeadsService businessLeadsService;
    
    @Autowired
    private BusinessChanceService businessChanceService;
    
    @Autowired
    private UserApiService userApiService;

    @Autowired
    private JumpService jumpService;

    @Value("${lxcrmUrl}")
    private String lxcrmUrl;

    @Override
    public void shareBusiness(RSalesJBusinessOrderDto rSalesJBusinessOrderDto, CurrentUser currentUser) {
        log.info("分享入参：{}", JSON.toJSONString(rSalesJBusinessOrderDto));
        List<RSalesJBusinessOrderDto> list = rSalesJBusinessOrderMapper.findByBusinessIdAndTypeAndUserId(rSalesJBusinessOrderDto);
        if (list != null && list.size() > 0) {
            log.info("分享已存在{}", JSON.toJSONString(rSalesJBusinessOrderDto));
            return;
        }
        // 记录操作日志
        OperationLogDto logDto = new OperationLogDto();
        logDto.setCreator(currentUser.getId());
        logDto.setCreatorName(currentUser.getUsername());
        logDto.setOperationTime(new Date());
        HashMap<String,String> params = new HashMap<>();
        params.put("userName",currentUser.getUsername());
        params.put("targetUserName",rSalesJBusinessOrderDto.getSaleUserName());
        logDto.setParams(params);
        logDto.setBizId(rSalesJBusinessOrderDto.getBusinessId());
        //* 业务类型
        //* 1.商机
        //* 2.报价
        if(rSalesJBusinessOrderDto.getBusinessType() == 5){
            operationLogApiService.save(logDto, BizLogEnum.BUSINESSlEADS_SHARE);
            // 发送企业微信消息提醒 - 线索协作人
            BusinessLeadsDto leadsDto = businessLeadsService.getOne(rSalesJBusinessOrderDto.getBusinessId());
            if (leadsDto != null) {
                String content = String.format("%s 添加您为 %s 线索协作人，请查看", 
                        currentUser.getUsername(), leadsDto.getLeadsNo());
                String url = jumpService.getjumpUrl( lxcrmUrl + "/crm/businessLeads/profile/detail?id=" + rSalesJBusinessOrderDto.getBusinessId(), 
                JumpErpTitleEnum.BUSSINESS_LEAD_DETAIL);
                sendCardMessage(rSalesJBusinessOrderDto.getSaleUserId(), "协作人添加提醒", content, url, "查看详情");
            }
        }
        if(rSalesJBusinessOrderDto.getBusinessType() == 1){
            operationLogApiService.save(logDto, BizLogEnum.BUSINESS_CHANE_SHARE);
            // 发送企业微信消息提醒 - 商机协作人
            BusinessChanceDto businessChanceDto = new BusinessChanceDto();
            businessChanceDto.setBussinessChanceId(rSalesJBusinessOrderDto.getBusinessId());
            BusinessChanceDto businessChanceResult = businessChanceService.selectOne(businessChanceDto);
            if (businessChanceResult != null) {
                String content = String.format("%s 添加您为 %s 商机协作人，请查看", 
                        currentUser.getUsername(), businessChanceResult.getBussinessChanceNo());
                String url = jumpService.getjumpUrl( lxcrmUrl + "/crm/businessChance/profile/detail?id=" + rSalesJBusinessOrderDto.getBusinessId(), 
                JumpErpTitleEnum.BUSSINESS_CHANCE_DETAIL);
                sendCardMessage(rSalesJBusinessOrderDto.getSaleUserId(), "协作人添加提醒", content, url, "查看详情");
            }
        }
        rSalesJBusinessOrderMapper.insertSelective(rSalesJBusinessOrderConvertor.toEntity(rSalesJBusinessOrderDto));
    }

    @Override
    public void shareAutoBusiness(RSalesJBusinessOrderDto rSalesJBusinessOrderDto, CurrentUser currentUser) {
        log.info("分享入参：{}", JSON.toJSONString(rSalesJBusinessOrderDto));
        List<RSalesJBusinessOrderDto> list = rSalesJBusinessOrderMapper.findByBusinessIdAndTypeAndUserId(rSalesJBusinessOrderDto);
        if (list != null && list.size() > 0) {
            log.info("分享已存在{}", JSON.toJSONString(rSalesJBusinessOrderDto));
            return;
        }
        // 记录操作日志
        OperationLogDto logDto = new OperationLogDto();
        logDto.setCreator(currentUser.getId());
        logDto.setCreatorName(currentUser.getUsername());
        logDto.setOperationTime(new Date());
        HashMap<String,String> params = new HashMap<>();
        params.put("userName",currentUser.getUsername());
        params.put("targetUserName",rSalesJBusinessOrderDto.getSaleUserName());
        logDto.setParams(params);
        logDto.setBizId(rSalesJBusinessOrderDto.getBusinessId());
        //* 业务类型
        //* 1.商机
        if(rSalesJBusinessOrderDto.getBusinessType() == 1 || rSalesJBusinessOrderDto.getBusinessType() == 5){
            operationLogApiService.save(logDto, BizLogEnum.BUSINESS_CHANE_SHARE_AUTO);
        }
        if(rSalesJBusinessOrderDto.getBusinessType() == 5){
            operationLogApiService.save(logDto, BizLogEnum.BUSINESSlEADS_SHARE_AUTO);
        }
        rSalesJBusinessOrderMapper.insertSelective(rSalesJBusinessOrderConvertor.toEntity(rSalesJBusinessOrderDto));
    }


    @Override
    public void cancelShare(Integer bussinessChanceId,CurrentUser currentUser) {
        RSalesJBusinessOrderEntity oldEntity = rSalesJBusinessOrderMapper.selectByPrimaryKey(bussinessChanceId);
        if(oldEntity == null){
            return ;
        }
        rSalesJBusinessOrderMapper.deleteById(bussinessChanceId);
        // 记录操作日志
        OperationLogDto logDto = new OperationLogDto();
        logDto.setCreator(currentUser.getId());
        logDto.setCreatorName(currentUser.getUsername());
        logDto.setOperationTime(new Date());
        HashMap<String,String> params = new HashMap<>();
        params.put("userName",currentUser.getUsername());
        params.put("targetUserName",oldEntity.getSaleUserName());
        logDto.setParams(params);
        logDto.setBizId(oldEntity.getBusinessId());
        if(oldEntity.getBusinessType() == 5){
            operationLogApiService.save(logDto, BizLogEnum.BUSINESSlEADS_ABORT_SHARE);
            // 发送企业微信消息提醒 - 删除线索协作人
            BusinessLeadsDto leadsDto = businessLeadsService.getOne(oldEntity.getBusinessId());
            if (leadsDto != null) {
                String content = String.format("%s 删除了您与 %s 线索协作人关系", 
                        currentUser.getUsername(), leadsDto.getLeadsNo());
                sendCardMessage(oldEntity.getSaleUserId(), "协作人移除提醒", content, "", "知道了");
            }
        }
        if(oldEntity.getBusinessType() == 1){
            operationLogApiService.save(logDto, BizLogEnum.BUSINESS_CHANE_ABORT_SHARE);
            // 发送企业微信消息提醒 - 删除商机协作人
            BusinessChanceDto businessChanceDto = new BusinessChanceDto();
            businessChanceDto.setBussinessChanceId(oldEntity.getBusinessId());
            BusinessChanceDto businessChanceResult = businessChanceService.selectOne(businessChanceDto);
            if (businessChanceResult != null) {
                String content = String.format("%s 删除了您与 %s 商机协作人关系", 
                        currentUser.getUsername(), businessChanceResult.getBussinessChanceNo());
                sendCardMessage(oldEntity.getSaleUserId(), "协作人移除提醒", content, "", "知道了");
            }
        }
    }
    
    /**
     * 统一发送卡片消息
     * @param targetUserId 目标用户ID
     * @param title 标题
     * @param content 内容
     * @param url 跳转链接
     * @param btnText 按钮文本
     */
    private void sendCardMessage(Integer targetUserId, String title, String content, String url, String btnText) {
        try {
            if (targetUserId == null) {
                log.warn("目标用户ID为空，无法发送消息");
                return;
            }
            
            // 获取用户工号
            UserDto userDto = userApiService.getUserBaseInfo(targetUserId);
            if (userDto == null) {
                log.warn("无法获取用户信息，用户ID: {}", targetUserId);
                return;
            }
            
            String userNumber = userDto.getNumber();
            if (StringUtils.isBlank(userNumber)) {
                log.warn("用户工号为空，用户ID: {}", targetUserId);
                return;
            }
            
            // 创建并发送卡片消息
            WxCpMessage wxCpMessage = new WxCpMessage();
            wxCpMessage.setToUser(userNumber);
            
            boolean isMarkdown = StrUtil.isEmpty(url);
            wxCpMessage.setMsgType(isMarkdown ? "markdown" : "textcard");
            wxCpMessage.setTitle(title);
            
            if (isMarkdown) {
                // 优化Markdown样式
                StringBuilder markdownContent = new StringBuilder();
                // 标题使用info颜色（蓝色）并加粗
                markdownContent.append("<font color=\"info\">**").append(title).append("**</font>\n\n");
                
                // 添加当前时间，灰色显示
                String formattedTime = DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");
                markdownContent.append("<font color=\"comment\">").append(formattedTime).append("</font>\n\n");
                
                // 主体内容格式化 - 使用引用样式增加可读性
                markdownContent.append("> ").append(content.replace("\n", "\n> ")).append("\n\n");

                // 设置内容
                wxCpMessage.setContent(markdownContent.toString());
            } else {
                wxCpMessage.setDescription(content);
                wxCpMessage.setUrl(url);
                wxCpMessage.setBtnTxt(btnText);
            }
            
            log.info("发送企业微信消息: 用户={}, 类型={}, 标题={}, 内容={}", 
                    userNumber, wxCpMessage.getMsgType(), title, isMarkdown ? wxCpMessage.getContent() : content);
            uacWxUserInfoApiService.sendToUser(wxCpMessage);
        } catch (Exception e) {
            log.error("发送企业微信消息失败，目标用户ID: {}, 原因: {}", targetUserId, e.getMessage(), e);
        }
    }

    @Override
    public List<RSalesJBusinessOrderDto> getShareListByBusinessId(Integer businessId,Integer businessType) {
        List<RSalesJBusinessOrderDto> list = null;
        // 根据业务类型调用不同的计算标签方法
        if (businessType == 5) {
            // 线索业务类型 - 使用新方法直接计算标签，不依赖T_R_SALES_J_BUSINESS_ORDER表作为条件
            list = rSalesJBusinessOrderMapper.calculateShareTagsWithoutBusinessJOrder(businessId);
        } else if (businessType == 1) {
            // 商机业务类型 - 使用新方法直接计算标签，不依赖T_R_SALES_J_BUSINESS_ORDER表作为条件
            list = rSalesJBusinessOrderMapper.calculateShareTagsWithoutBusinessJOrderForChance(businessId);
        } 

        if(CollectionUtils.isNotEmpty(list)){
            // 按用户ID分组，合并相同用户的标签
            Map<Integer, List<RSalesJBusinessOrderDto>> userGroups = list.stream()
                    .collect(Collectors.groupingBy(RSalesJBusinessOrderDto::getSaleUserId));
            
            // 处理每个用户的标签
            List<RSalesJBusinessOrderDto> result = new ArrayList<>();
            userGroups.forEach((userId, userShares) -> {
                RSalesJBusinessOrderDto mergedShare = userShares.get(0);
                // 获取所有标签并按优先级排序
                List<Integer> sortedTags = userShares.stream()
                        .map(RSalesJBusinessOrderDto::getShareTag)
                        .filter(Objects::nonNull)
                        .sorted()
                        .collect(Collectors.toList());
                
                // 设置最高优先级的标签（最小值）
                Integer highestTag = sortedTags.stream()
                        .min(Comparator.naturalOrder())
                        .orElse(3); // 默认为手动添加
                mergedShare.setShareTag(highestTag);
                
                // 设置所有标签的列表
                mergedShare.setShareTags(sortedTags);
                
                result.add(mergedShare);
            });
            return result;
        }
        return list;
    }

    @Override
    public RSalesJBusinessOrderDto getShareListByBusinessIdAndUserId(Integer businessId, Integer businessType,Integer saleUserId) {
        RSalesJBusinessOrderDto rSalesJBusinessOrderDto = new RSalesJBusinessOrderDto();
        rSalesJBusinessOrderDto.setBusinessType(ErpConstant.ONE);//1.商机 2.报价 3.订单 4.售后 5.线索
        rSalesJBusinessOrderDto.setBusinessId(businessId);
        rSalesJBusinessOrderDto.setSaleUserId(saleUserId);
        List<RSalesJBusinessOrderDto> result =  rSalesJBusinessOrderMapper.findByBusinessIdAndTypeAndUserId(rSalesJBusinessOrderDto);
        if(CollectionUtils.isNotEmpty(result)){
            return result.get(0);
        }
        return null;
    }

    @Override
    public Boolean checkShareBtn(Integer businessId, Integer businessType) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
       
        if(businessType == 5){
            BusinessLeadsDto relateLead = businessLeadsService.findById(businessId);
            if (Objects.isNull(relateLead)){
                log.info("线索不存在");
                return Boolean.FALSE;
            }
            if (Objects.equals(relateLead.getBelongerId(), currentUser.getId()) || 
                    Objects.equals(relateLead.getCreator(), currentUser.getId())){
                // 创建人/归属销售
                return Boolean.TRUE;
            }
            // 上级
            Boolean isParent1 = isParent(relateLead.getBelongerId(), currentUser.getId());
            Boolean isParent2 = isParent(relateLead.getCreator(), currentUser.getId());
            return isParent1 || isParent2;
        }
        if(businessType == 1){
            BusinessChanceDto businessChanceDto = new BusinessChanceDto();
            businessChanceDto.setBussinessChanceId(businessId);
            BusinessChanceDto relateLead =  businessChanceService.selectOne(businessChanceDto);
            if (Objects.isNull(relateLead)){
                log.info("商机不存在");
                return Boolean.FALSE;
            }
            if (Objects.equals(relateLead.getUserId(), currentUser.getId()) ||
                    Objects.equals(relateLead.getCreator(), currentUser.getId())){
                // 创建人/归属销售
                return Boolean.TRUE;
            }
            // 上级
            Boolean isParent1 = isParent(relateLead.getUserId(), currentUser.getId());
            Boolean isParent2 = isParent(relateLead.getCreator(), currentUser.getId());
            return isParent1 || isParent2;
        }
        return Boolean.FALSE;
    }

    /**
     * 是否是上级-逻辑应为根据当前操作人，查询其所有下属，或包含了被检测的人。则被检测的人为当前操作人的下属
     * @return
     */
    private Boolean isParent(Integer userId,Integer currentUserId){
        //根据currentUserId查询所有的下属，看是否包含userId
        List<UserDto> userDtoList = userApiService.queryUserSubFromUac(currentUserId);
        Set<Integer> allSubordinateUserIdList = userDtoList.stream().map(UserDto::getUserId).collect(Collectors.toSet());
        return allSubordinateUserIdList.contains(userId);
//        UserDto creatorUser = userApiService.getUserById(userId);
//        if (Objects.isNull(creatorUser)){
//            log.info("用户不存在");
//            return Boolean.FALSE;
//        }
//        if (creatorUser.getParentId().equals(currentUserId)) {
//            return Boolean.TRUE;
//        }
//        log.info("判断是否商机{}不是{}的上级",userId,currentUserId);
//        return Boolean.FALSE;
    }
}
