Vue.component('ui-checkbox-group', {
    template: `
        <div class="vd-ui-checkbox-group" :class="{'vd-ui-checkbox-vertical': layout === 'vertical'}">
            <template v-for="(item, index) in boxList">
                <ui-checkbox
                    :key="index"
                    :label="item.label"
                    :checked.sync="item.checked"
                    :disabled="item.disabled"
                    @change="handlerChange"
                ></ui-checkbox>
            </template>
            <div class="vd-ui-input-error" v-if="errorable">
                <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i>
                <p class="vd-ui-input-error--errmsg">{{errorMsg}}</p>
            </div>
        </div>
    `,
    watch: {
        list() {
            this.setList();
        },
        onProgress() {
            this.boxList.forEach((item, index) => {
                if (this.onProgress === 3) {
                    this.$set(this.boxList[index], "checked", true);
                } else if (this.onProgress === 1) {
                    this.$set(this.boxList[index], "checked", false);
                }
            });

            if (this.onProgress !== 2) {
                this.handlerChange(null, true);
            }
        }
    },
    data() {
        return {
            boxList: [],
        };
    },
    props: {
        list: {
            type: Array,
            default() {
                return [];
            },
        },
        values: {
            type: Array,
            default() {
                return [];
            },
        },
        onProgress: {
            type: Number,
            default: 1,
        },
        errorMsg: {
            type: String,
            default: ''
        },
        errorable: {
            type: Boolean,
            default: false
        },
        layout: {
            type: String, //vertical 垂直排列
            default: ''
        }
    },
    mounted() {
        this.$form.setValidEl(this);
        this.setList();
    },
    methods: {
        setList() {
            this.boxList = JSON.parse(JSON.stringify(this.list));

            this.boxList.forEach((item) => {
                this.values.forEach((value) => {
                    if (item.value == value) {
                        item.checked = true;
                    }
                });
            });

            this.checkOnProgress();
        },
        handlerChange(data, silent) {
            console.log('boxList:', this.boxList);
            let values = [];
            this.boxList.forEach((item) => {
                if (item.checked) {
                    values.push(item.value);
                }
            });

            if (values.join('__') !== this.values.join('__')) {
                this.$emit("update:values", values);
                this.$emit("change", values);
                
                this.checkValid(values);

                if (!silent) {
                    this.$nextTick(() => {
                        this.checkOnProgress();
                    });
                }
            }
        },
        checkOnProgress() {
            if (
                this.values.length &&
                this.values.length === this.boxList.length
            ) {
                this.$emit("update:onProgress", 3);
            } else if (!this.values.length) {
                this.$emit("update:onProgress", 1);
            } else {
                this.$emit("update:onProgress", 2);
            }
        },
        selectAll() {
            this.boxList.forEach((item) => {
                item.checked = true;
            });

            this.handlerChange();
        },
        checkValid(newValue) {
            if(this.validKey && this.validValue) {
                if(this.$form.validList[this.validKey].indexOf(this.validValue) !== -1) {
                    let validData = this.$form.checkValid(this.validValue, newValue, this.validKey);

                    this.triggerError(validData);
                }
            }
        },
        triggerError(validData) {
            if(validData.result) {
                this.errorMsg = '';
                this.errorable = false;
            } else {
                this.errorMsg = validData.message;
                this.errorable = true;
            }
        }
    },
})