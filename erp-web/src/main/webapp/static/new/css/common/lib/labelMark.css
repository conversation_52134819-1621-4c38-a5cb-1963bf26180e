@import url(../font/font.css);
.lm-wrap {
  position: fixed;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  font: 14px/1.5 'Microsoft YaHei', arial, sans-serif;
}
.lm-wrap * {
  box-sizing: border-box;
}
.lm-wrap .lm-cnt {
  width: 600px;
  position: relative;
}
.lm-wrap .lm-cnt .lm-close {
  font-style: normal;
  position: absolute;
  font-size: 24px;
  display: inline-block;
  width: 44px;
  height: 12px;
  line-height: 12px;
  text-align: center;
  right: -6px;
  top: 14px;
  cursor: pointer;
  color: #999;
}
.lm-wrap .lm-cnt .lm-close:hover {
  color: #333;
}
.lm-wrap .lm-cnt .lm-title {
  padding: 0 20px;
  line-height: 44px;
  height: 44px;
  border-bottom: 1px solid #edf0f2;
  background: #fafbfc;
  border-radius: 3px 3px 0 0;
  font-size: 16px;
}
.lm-wrap .lm-cnt .lm-main {
  background: #fff;
  padding: 20px;
}
.lm-wrap .lm-cnt .lm-main-block {
  margin-bottom: 20px;
}
.lm-wrap .lm-cnt .lm-main-block:last-child {
  margin-bottom: 0;
}
.lm-wrap .lm-cnt .lm-main-title {
  font-size: 16px;
  margin-bottom: 15px;
}
.lm-wrap .lm-cnt .lm-main-title .lm-main-tip {
  font-size: 12px;
  color: #999;
  margin-left: 10px;
  display: inline-block;
}
.lm-wrap .lm-cnt .lm-main-list .lm-main-item {
  display: flex;
  margin-bottom: 10px;
}
.lm-wrap .lm-cnt .lm-main-list .lm-main-item:last-child {
  margin-bottom: 0;
}
.lm-wrap .lm-cnt .lm-main-list .lm-main-item .item-label {
  width: 120px;
  margin-right: 10px;
  text-align: right;
  line-height: 28px;
}
.lm-wrap .lm-cnt .lm-main-list .lm-main-item .item-field {
  flex: 1;
}
.lm-wrap .lm-cnt .lm-main-list .lm-checkbox-list {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: -5px;
}
.lm-wrap .lm-cnt .lm-main-list .lm-checkbox-list .lm-checkbox-item {
  font-size: 12px;
  padding: 4px 10px;
  border: 1px solid #ced3d9;
  border-radius: 3px;
  margin-right: 10px;
  color: #999;
  margin-bottom: 5px;
  cursor: pointer;
}
.lm-wrap .lm-cnt .lm-main-list .lm-checkbox-list .lm-checkbox-item:hover {
  border-color: #09f;
  color: #09f;
}
.lm-wrap .lm-cnt .lm-main-list .lm-checkbox-list .lm-checkbox-item.selected {
  background: #09f;
  color: #fff;
  border-color: #09f;
  cursor: default;
}
.lm-wrap .lm-cnt .lm-main-list .lm-checkbox-list .lm-checkbox-item.disabled {
  color: #c2c6cc;
  border-color: #dae0e6;
  cursor: not-allowed;
}
.lm-wrap .lm-cnt .lm-main-preview {
  border: 1px dashed #ced3d9;
  padding: 10px;
}
.lm-wrap .lm-cnt .lm-main-preview .pre-item {
  display: flex;
  margin-bottom: 5px;
  color: #999;
}
.lm-wrap .lm-cnt .lm-main-preview .pre-item .item-label {
  width: 120px;
  margin-right: 5px;
  text-align: right;
  align-items: center;
}
.lm-wrap .lm-cnt .lm-main-preview .pre-item .item-field {
  flex: 1;
}
.lm-wrap .lm-cnt .lm-main-preview .pre-item .input-edit {
  outline: none;
  border: 1px dashed #ced3d9;
  padding: 7px 10px;
  color: #999;
  width: 100%;
  resize: none;
  height: 63px;
}
.lm-wrap .lm-cnt .lm-footer {
  border-top: 1px solid #edf0f2;
  background: #fff;
  padding: 10px 20px;
  text-align: right;
  border-radius: 0 0 3px 3px;
}
.lm-wrap .lm-cnt .lm-footer .lm-btn {
  background: #09f;
  color: #fff;
  font-size: 14px;
  border-radius: 3px;
  padding: 6px 15px;
  display: inline-block;
  cursor: pointer;
  margin-left: 7px;
  border: 1px solid #09f;
}
.lm-wrap .lm-cnt .lm-footer .lm-btn.btn-grey {
  background: #F5F7FA;
  border: 1px solid #CED3D9;
  color: #333;
}
.lm-wrap .lm-cnt .lm-footer .lm-btn:hover {
  opacity: 0.8;
}
.lm-wrap .lm-info-tip {
  background: #FFF0E5;
  display: flex;
  color: #666;
  margin-bottom: 10px;
  padding: 5px 10px;
  align-items: center;
}
.lm-wrap .lm-info-tip .info-icon {
  display: inline-block;
  color: #f60;
  font-size: 16px;
  margin-right: 5px;
  margin-top: 3px;
}
.lm-wrap.lm-choose {
  z-index: 1000;
}
.lm-wrap.lm-choose .lm-cnt {
  width: 500px;
}
.lm-wrap.lm-choose .lm-cnt .lm-form {
  padding-top: 40px;
}
.lm-wrap .lm-form {
  background: #fff;
  border-radius: 3px 3px 0 0;
  padding: 20px;
  min-height: 200px;
}
.lm-wrap .lm-form .lm-form-item {
  margin-bottom: 15px;
  display: flex;
}
.lm-wrap .lm-form .lm-form-item:last-child {
  margin-bottom: 0;
}
.lm-wrap .lm-form .lm-form-item .item-label {
  width: 120px;
  margin-right: 10px;
  line-height: 30px;
  text-align: right;
}
.lm-wrap .lm-form .lm-form-item .item-label .must {
  color: #e64545;
}
.lm-wrap .lm-form .lm-form-item .item-field {
  flex: 1;
}
.lm-wrap .lm-form .lm-form-item .item-field .input-date-wrap {
  position: relative;
  width: 200px;
}
.lm-wrap .lm-form .lm-form-item .item-field .input-text {
  width: 200px;
}
.lm-wrap .lm-form .lm-form-item .item-field .input-textarea {
  resize: none;
  width: 100%;
}
.lm-wrap .lm-form .lm-form-item .item-field .item-field-txt {
  line-height: 30px;
}
.calendar {
  z-index: 1001;
  box-sizing: content-box;
}
.lm-wrap i {
  height: auto;
  background: none;
  margin-bottom: 0;
}

.customernameshow .pre-item {
  display: flex;
  margin-bottom: 5px;
  color: #fff;
}

.customernameshow .item-label {
  width: 75px;
}

.lm-wrap .select-suggest {
  width: auto !important;
}

.lm-wrap .select-suggest .select-selected{
  padding: 5px 0 5px 10px !important;
}
