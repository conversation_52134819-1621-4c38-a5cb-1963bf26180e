package com.vedeng.erp.buyorder.mapper;

import com.vedeng.erp.buyorder.domain.entity.BuyorderModifyApplyExpenseGoodsEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BuyorderModifyApplyExpenseGoodsMapper {
    /**
     * delete by primary key
     * @param buyorderModifyApplyExpenseGoodsId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer buyorderModifyApplyExpenseGoodsId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(BuyorderModifyApplyExpenseGoodsEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(BuyorderModifyApplyExpenseGoodsEntity record);

    /**
     * select by primary key
     * @param buyorderModifyApplyExpenseGoodsId primary key
     * @return object by primary key
     */
    BuyorderModifyApplyExpenseGoodsEntity selectByPrimaryKey(Integer buyorderModifyApplyExpenseGoodsId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(BuyorderModifyApplyExpenseGoodsEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(BuyorderModifyApplyExpenseGoodsEntity record);

    int updateBatchSelective(List<BuyorderModifyApplyExpenseGoodsEntity> list);

    /**
     * 根据采购修改单id查询关联的费用单修改记录信息
     *
     * @param buyorderModifyApplyId 采购修改单id
     * @return List<BuyorderModifyApplyExpenseGoodsEntity>
     */
    List<BuyorderModifyApplyExpenseGoodsEntity> getByBuyorderModifyApplyId(@Param("buyorderModifyApplyId") Integer buyorderModifyApplyId);
}