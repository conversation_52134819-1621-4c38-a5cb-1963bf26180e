package com.newtask.esign;

import com.vedeng.base.api.dto.reqParam.BusinessInfo;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.buyorder.manager.esign.BuyOrderElectronicSignHandle;
import com.vedeng.erp.quote.manager.esign.AuthorizationElectronicSignHandle;
import com.vedeng.infrastructure.esign.domain.ElectronicSignParam;
import com.vedeng.infrastructure.esign.domain.entity.ElectronicSignRecordEntity;
import com.vedeng.infrastructure.esign.enums.ElectronicSignBusinessEnums;
import com.vedeng.infrastructure.esign.service.ElectronicSignRecordService;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeEventMsgDto;
import com.vedeng.infrastructure.kingdee.service.KingDeeEventMsgService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 电子签章异常补偿任务
 * @date 2022/8/29 13:12
 */
@JobHandler(value = "EsignErrorTask")
@Component
@Slf4j
public class EsignErrorTask extends AbstractJobHandler {


    @Autowired
    private ElectronicSignRecordService electronicSignRecordService;

    @Autowired
    private AuthorizationElectronicSignHandle authorizationElectronicSignHandle;

    @Value("${sign.retryNum:3}")
    private int retryNum;


    @Override
    public ReturnT<String> doExecute(String param) throws Exception  {
        List<ElectronicSignRecordEntity> fail = electronicSignRecordService.getFail(ElectronicSignBusinessEnums.SALE_AUTHORIZATION.getType());
        Map<Integer, List<ElectronicSignRecordEntity>> map = fail.stream().filter(f -> f.getRetryNum() <= retryNum).collect(Collectors.groupingBy(ElectronicSignRecordEntity::getBusinessType));
        map.forEach((k, v) -> v.forEach(e -> {
            BusinessInfo businessInfo = new BusinessInfo();
            businessInfo.setOperator(ErpConstant.DEFAULT_USERNAME);
            ElectronicSignParam electronicSignParam = ElectronicSignParam
                    .builder()
                    .flowType(2)
                    .authorizationApplyId(Integer.parseInt(e.getBusinessId()))
                    .businessInfo(businessInfo)
                    .electronicSignBusinessEnums(ElectronicSignBusinessEnums.getEnum(k))
                    .build();
            authorizationElectronicSignHandle.electronicSign(electronicSignParam);
        }));
        return ReturnT.SUCCESS;
    }


}
