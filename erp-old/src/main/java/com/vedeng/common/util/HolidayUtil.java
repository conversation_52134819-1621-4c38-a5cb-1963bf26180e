package com.vedeng.common.util;

import org.apache.commons.collections.CollectionUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class HolidayUtil {

    private static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public static List<String> holidayList = Arrays.asList(
            "2021-01-01 00:00:00,2021-01-03 23:59:59",
            "2021-02-11 00:00:00,2021-02-17 23:59:59",
            "2021-04-03 00:00:00,2021-04-05 23:59:59",
            "2021-05-01 00:00:00,2021-05-05 23:59:59",
            "2021-06-12 00:00:00,2021-06-14 23:59:59",
            "2021-09-19 00:00:00,2021-09-21 23:59:59",
            "2021-10-01 00:00:00,2021-10-07 23:59:59",
            //22年
            "2022-01-01 00:00:00,2022-01-03 23:59:59",
            "2022-01-31 00:00:00,2022-02-06 23:59:59",
            "2022-04-03 00:00:00,2022-04-05 23:59:59",
            "2022-04-30 00:00:00,2022-05-04 23:59:59",
            "2022-06-03 00:00:00,2022-06-05 23:59:59",
            "2022-09-10 00:00:00,2022-09-12 23:59:59",
            "2022-10-01 00:00:00,2022-10-07 23:59:59",
            //23年
            "2023-01-01 00:00:00,2022-01-02 23:59:59",
            "2023-01-21 00:00:00,2022-01-27 23:59:59",
            "2023-04-05 00:00:00,2022-04-05 23:59:59",
            "2023-04-29 00:00:00,2022-05-03 23:59:59",
            "2023-06-22 00:00:00,2022-06-24 23:59:59",
            "2023-09-29 00:00:00,2022-10-06 23:59:59"
    );

    public static List<String> specialWorkList = Arrays.asList(
            "2021-02-07 00:00:00,2021-02-07 23:59:59",
            "2021-02-20 00:00:00,2021-02-20 23:59:59",
            "2021-04-25 00:00:00,2021-04-25 23:59:59",
            "2021-05-08 00:00:00,2021-05-08 23:59:59",
            "2021-09-18 00:00:00,2021-09-18 23:59:59",
            "2021-09-26 00:00:00,2021-09-26 23:59:59",
            "2021-10-09 00:00:00,2021-10-09 23:59:59",
            //22年
            "2022-01-29 00:00:00,2022-01-30 23:59:59",
            "2022-04-02 00:00:00,2022-04-02 23:59:59",
            "2022-04-24 00:00:00,2022-04-24 23:59:59",
            "2022-05-07 00:00:00,2022-05-07 23:59:59",
            "2022-10-08 00:00:00,2022-10-09 23:59:59",
            //22年
            "2023-01-28 00:00:00,2022-01-29 23:59:59",
            "2023-04-23 00:00:00,2022-04-23 23:59:59",
            "2023-05-06 00:00:00,2022-05-06 23:59:59",
            "2023-06-25 00:00:00,2022-06-25 23:59:59",
            "2023-10-07 00:00:00,2022-10-08 23:59:59"
    );

    public static void main(String[] args) throws Exception {
        //获取计划激活日期

        long time = DateUtil.convertLong("2020-06-24 10:10:00","yyyy-MM-dd HH:mm:ss");
        long resultDate = getWorkDate(time,1);
        System.out.println("计划激活日期为:" + sdf.format(resultDate));
    }

    /**
     * 获取某给时间num天工作日后的时间
     * @param time
     * @param num
     * @return
     * @throws ParseException
     */
    public static long getWorkDate(long time,int num) throws ParseException {

        Date today = new Date(time);

        Date tomorrow = null;
        int delay = 1;

        while(delay <= num){

            tomorrow = getTomorrow(today);

            if (isHoliday(tomorrow)){
                today = tomorrow;
                continue;
            }

            if(isWeekend(tomorrow)){
                today = tomorrow;
                continue;
            }

            //当前日期+1即tomorrow,判断是否是节假日,同时要判断是否是周末,都不是则将scheduleActiveDate日期+1,直到循环num次即可
            delay++;
            today = tomorrow;
        }
        return today.getTime();
    }

    /**
     * 获取明天的日期
     *
     * @param date
     * @return
     */
    public static Date getTomorrow(Date date){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, +1);
        date = calendar.getTime();
        return date;
    }

    /**
     * 判断是否是weekend
     * @return
     * @throws ParseException
     */
    public static boolean isWeekend(Date tomorrow) throws ParseException {

        Calendar cal = Calendar.getInstance();
        cal.setTime(tomorrow);

        for(int i = 0; i < specialWorkList.size(); i++){

            String[] currentWork = specialWorkList.get(i).split(",");

            long startTime = DateUtil.convertLong(currentWork[0],DateUtil.TIME_FORMAT);

            long endTime = DateUtil.convertLong(currentWork[1],DateUtil.TIME_FORMAT);

            if(startTime <= tomorrow.getTime() && tomorrow.getTime() <= endTime){
                return false;
            }
        }

        if(cal.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY
                || cal.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY){
            return true;
        }

        return false;
    }

    /**
     * 判断是否是holiday
     * @return
     * @throws ParseException
     */
    public static boolean isHoliday(Date tomorrow) throws ParseException {

        if(CollectionUtils.isEmpty(holidayList)) {
            return false;
        }

        for(int i = 0; i < holidayList.size(); i++){

            String[] currentHoliday = holidayList.get(i).split(",");

            long startTime = DateUtil.convertLong(currentHoliday[0],DateUtil.TIME_FORMAT);

            long endTime = DateUtil.convertLong(currentHoliday[1],DateUtil.TIME_FORMAT);

            if(startTime <= tomorrow.getTime() && tomorrow.getTime() <= endTime){
                return true;
            }
        }

        return false;
    }
}
