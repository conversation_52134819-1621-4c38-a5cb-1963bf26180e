package com.vedeng.finance.controller;

import cn.hutool.core.date.DateTime;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.redis.redission.RedissonLockUtils;
import com.vedeng.common.redis.utils.RedisUtil;
import com.vedeng.finance.service.BankBillAutoIgnoreService;
import com.vedeng.finance.service.BankBillAutoSettlementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Date;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/8 9:33
 **/
@Controller
@RequestMapping("/finance/auto")
@Slf4j
public class BankAutoController {

    private static final int CORE_POOL_SIZE = 10;
    private static final int MAX_POOL_SIZE = CORE_POOL_SIZE * 5;
    private static final long KEEP_ALIVE_TIME = 60L;

    private final ThreadPoolExecutor executor = new ThreadPoolExecutor(CORE_POOL_SIZE,
            MAX_POOL_SIZE, KEEP_ALIVE_TIME, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(MAX_POOL_SIZE * 4, true),
            new ThreadFactoryBuilder().setNameFormat("autoBank-pool-%d").build(),
            new ThreadPoolExecutor.AbortPolicy());


    @Autowired
    private BankBillAutoSettlementService bankBillAutoSettlementService;

    @Autowired
    private BankBillAutoIgnoreService bankBillAutoIgnoreService;

    private final static String REDIS_KEY_LOAD = "ERP:BATCH:SETTLEMENT:LOADING:1";
    private final static String REDIS_KEY_LOAD_TOTAL = "ERP:BATCH:SETTLEMENT:LOADING:TOTAL";
    private final static String REDIS_KEY_LOAD_DONE = "ERP:BATCH:SETTLEMENT:LOADING:DONE";

    private final static String REDIS_KEY_IGNORE_LOAD = "ERP:BATCH:IGNORE:LOADING:1";
    private final static String REDIS_KEY_IGNORE_LOAD_TOTAL = "ERP:BATCH:IGNORE:LOADING:TOTAL";
    private final static String REDIS_KEY_IGNORE_LOAD_DONE = "ERP:BATCH:IGNORE:LOADING:DONE";

    @Value("${autoBankTime}")
    public int autoBankTime;

    @Value("${ignoreBankTime}")
    public int ignoreBankTime;

    @ResponseBody
    @RequestMapping(value = "/settlement")
    @NoNeedAccessAuthorization
    public ResultInfo construction() throws InterruptedException {

        boolean hasKey = RedisUtil.KeyOps.hasKey(REDIS_KEY_LOAD);
        if (hasKey) {
            log.info("加锁失败，手动触发自动结款,存在处理中的结款业务");
            return ResultInfo.error("存在处理中的结款业务,请稍后再试");
        }

        log.info("自动结款加锁成功, key = [{}]", REDIS_KEY_LOAD);

        executor.submit(() -> {

            boolean lock = RedissonLockUtils.tryLock(REDIS_KEY_LOAD);

            if (!lock) {
                log.info("加锁失败，手动触发自动结款,存在处理中的结款业务");
                return;
            }
            Date end = new Date();

            DateTime begin = cn.hutool.core.date.DateUtil.offsetDay(end, -autoBankTime);
            try {
                bankBillAutoSettlementService.doWithPage(begin, end, true);
            } catch (Exception e) {
                log.error("触发自动结款,异常",e);
            }finally {
                // 解锁手动触发
                RedissonLockUtils.unlock(REDIS_KEY_LOAD);
                log.info("自动结款解锁成功, key = [{}]", REDIS_KEY_LOAD);
            }


        });

        return ResultInfo.success();
    }

    @ResponseBody
    @RequestMapping(value = "/settlementLoading")
    @NoNeedAccessAuthorization
    public ResultInfo settlementLoading() {

        long total = RedisUtil.StringOps.incrBy(REDIS_KEY_LOAD_TOTAL, 0);
        long done = RedisUtil.StringOps.incrBy(REDIS_KEY_LOAD_DONE, 0);
        if (total == 0) {
            return ResultInfo.success(100);
        }
        if (total == done) {
            return ResultInfo.success(100);
        }
        long result = done * 100 / total;
        return ResultInfo.success(result);
    }


    @ResponseBody
    @RequestMapping(value = "/ignore")
    @NoNeedAccessAuthorization
    public ResultInfo ignore() throws InterruptedException {
        // 加锁
        boolean hasKey = RedisUtil.KeyOps.hasKey(REDIS_KEY_IGNORE_LOAD);
        if (hasKey) {
            log.info("加锁失败，手动触发自动忽略,存在处理中的忽略业务");
            return ResultInfo.error("存在处理中的忽略业务,请稍后再试");
        }
        log.info("自动忽略加锁成功, key = [{}]", REDIS_KEY_IGNORE_LOAD);

        executor.submit(() -> {

            boolean lock = RedissonLockUtils.tryLock(REDIS_KEY_IGNORE_LOAD);
            if (!lock) {
                log.info("加锁失败，手动触发自动忽略,存在处理中的忽略业务");
                return;
            }
            Date end = new Date();

            DateTime begin = cn.hutool.core.date.DateUtil.offsetDay(end, -ignoreBankTime);
            try {
                bankBillAutoIgnoreService.doWithPage(begin, end, true);
            } catch (Exception e) {
                log.error("触发自动忽略,异常",e);
            }finally {
                // 解锁手动触发
                RedissonLockUtils.unlock(REDIS_KEY_IGNORE_LOAD);
                log.info("自动忽略解锁成功, key = [{}]", REDIS_KEY_IGNORE_LOAD);
            }

        });

        return ResultInfo.success();
    }

    @ResponseBody
    @RequestMapping(value = "/ignoreLoading")
    @NoNeedAccessAuthorization
    public ResultInfo ignoreLoading() {

        long total = RedisUtil.StringOps.incrBy(REDIS_KEY_IGNORE_LOAD_TOTAL, 0);
        long done = RedisUtil.StringOps.incrBy(REDIS_KEY_IGNORE_LOAD_DONE, 0);
        if (total == 0) {
            return ResultInfo.success(100);
        }
        if (total == done) {
            return ResultInfo.success(100);
        }
        long result = done * 100 / total;
        return ResultInfo.success(result);
    }



}
