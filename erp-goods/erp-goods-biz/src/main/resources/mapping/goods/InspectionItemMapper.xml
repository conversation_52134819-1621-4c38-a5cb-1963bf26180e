<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.mapper.InspectionItemMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.goods.domain.entity.InspectionItem">
            <id property="id" column="ID" jdbcType="INTEGER"/>
            <result property="name" column="NAME" jdbcType="VARCHAR"/>
            <result property="sort" column="SORT" jdbcType="INTEGER"/>
            <result property="description" column="DESCRIPTION" jdbcType="VARCHAR"/>
            <result property="isDeleted" column="IS_DELETED" jdbcType="TINYINT"/>
            <result property="creator" column="CREATOR" jdbcType="INTEGER"/>
            <result property="addTime" column="ADD_TIME" jdbcType="TIMESTAMP"/>
            <result property="updater" column="UPDATER" jdbcType="INTEGER"/>
            <result property="modTime" column="MOD_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,`NAME`,SORT,DESCRIPTION,IS_DELETED,CREATOR,ADD_TIME,UPDATER,MOD_TIME
    </sql>

    <select id="findAllOrderBySort" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_INSPECTION_ITEM order by SORT asc
    </select>

    <select id="findAllByNameAndIsDeleted" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_INSPECTION_ITEM
        where `NAME`=#{name,jdbcType=VARCHAR} and IS_DELETED=#{isDeleted,jdbcType=TINYINT}
    </select>

</mapper>
