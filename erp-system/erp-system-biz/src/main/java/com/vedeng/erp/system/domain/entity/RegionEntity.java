package com.vedeng.erp.system.domain.entity;

import java.util.Date;
import lombok.Data;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2022/7/13 16:07
 **/
/**
    * 地区信息表
    */
@Data
public class RegionEntity {

    private Long regionId;

    private Long parentId;

    /**
    * 地区名
    */
    private String regionName;

    /**
    * 地区类型
    */
    private Integer regionType;

    /**
    * 办事处的id,这里有一个bug,同一个省不能有多个办事处,该字段只记录最新的那个办事处的id
    */
    private Integer agencyId;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 更新时间
    */
    private Date updateTime;

    /**
    * 20200728最新数据
    */
    private String regionFullName;

    private String regionCode;

    private Boolean isDeleted;
}