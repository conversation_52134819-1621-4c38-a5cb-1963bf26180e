package com.vedeng.finance.enums;

/**
 * <AUTHOR>
 * @date 2020/09/10
 **/
public enum InvoiceAuthStatusEnum {
    COMPLETE(0, "未在认证"),
    AUTHING(1, "正在认证");

    private Integer code;

    private String desc;

    InvoiceAuthStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
