package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.annotation.KingDeeID;
import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import lombok.*;

import java.util.List;

/**
 * 调拨单 dto  https://www.yuque.com/manhuo/gf1570/my2awk
 * <AUTHOR>
 * @version 1.0
 * @description: 预处理金蝶调拨单 dto  由erp实际业务转换
 * @date
 */
@Getter
@Setter
@WriteBackField(needBackField = {"FID"})
public class KingDeeAllocationDto extends KingDeeMqBaseDto {

    /**
     * id
     */
    private Integer id;

    /**
     * 单据内码
     */
    @KingDeeID
    @WriteBackField
    private String fId;
    /**
     * 单据编号
     */
    @BusinessID
    private String fBillNo;
    /**
     * 单据类型
     */
    private String fBillTypeId;
    /**
     * 调拨方向
     */
    private String fTransferDirect;
    /**
     * 调拨类型
     */
    private String fTransferBizType;
    /**
     * 调出库存组织
     */
    private String fStockOutOrgId;
    /**
     * fOwnerTypeOutIdHead
     */
    private String fOwnerTypeOutIdHead;
    /**
     * 调入库存组织
     */
    private String fStockOrgId;
    /**
     * 单据日期
     */
    private String fDate;
    /**
     * 借调人
     */
    private String fQzokJdr;
    /**
     * 贝登单据头ID
     */
    private String fQzokBddjtid;
    /**
     * fBillEntry
     */
    private List<KingDeeAllocationDetailDto> fBillEntry;


    public KingDeeAllocationDto() {
        this.fBillTypeId = "ZJDB01_SYS";
        this.fTransferBizType = "InnerOrgTransfer";
        this.fStockOutOrgId = KingDeeConstant.ORG_ID.toString();
        this.fStockOrgId = KingDeeConstant.ORG_ID.toString();
        this.fOwnerTypeOutIdHead ="BD_OwnerOrg";
    }

    @Override
    public String getFormId() {
        return KingDeeFormConstant.STK_TRANSFER_DIRECT;
    }
}
