package com.vedeng.erp.kingdee.service.impl;

import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeeNeedReceiveCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeNeedReceiveAdjustEntity;
import com.vedeng.erp.kingdee.dto.KingDeeNeedReceiveDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeNeedReceiveCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeNeedReceiveConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeNeedReceiveAdjustRepository;
import com.vedeng.erp.kingdee.service.KingDeeNeedReceiveAdjustApiService;
import com.vedeng.erp.kingdee.service.KingDeeNeedReceiveAdjustService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class KingDeeNeedReceiveAdjustServiceImpl extends KingDeeBaseServiceImpl<
        KingDeeNeedReceiveAdjustEntity,
        KingDeeNeedReceiveDto,
        KingDeeNeedReceiveCommand,
        KingDeeNeedReceiveAdjustRepository,
        KingDeeNeedReceiveConvertor,
        KingDeeNeedReceiveCommandConvertor>
        implements KingDeeNeedReceiveAdjustService, KingDeeNeedReceiveAdjustApiService {

}
