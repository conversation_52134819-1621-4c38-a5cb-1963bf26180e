package com.vedeng.erp.broadcast.service;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.broadcast.domain.dto.BatchUploadResultDto;
import com.vedeng.erp.broadcast.domain.dto.BroadcastContentConfigDto;
import com.vedeng.erp.broadcast.domain.dto.BroadcastContentConfigReqDto;
import com.vedeng.erp.broadcast.domain.dto.BroadcastContentConfigRespDto;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashSet;

/**
 * 播报内容配置服务接口
 */
public interface BroadcastContentConfigService {

    /**
     * 分页查询播报内容配置
     *
     * @param pageParam 分页参数
     * @return 分页结果
     */
    PageInfo<BroadcastContentConfigRespDto> page(PageParam<BroadcastContentConfigReqDto> pageParam);

    /**
     * 添加播报内容配置
     *
     * @param dto 播报内容配置DTO
     */
    void add(BroadcastContentConfigDto dto);

    /**
     * 修改播报内容配置
     *
     * @param dto 播报内容配置DTO
     */
    void update(BroadcastContentConfigDto dto);



    /**
     * 删除播报内容配置
     *
     * @param ids ID集合
     */
    void delete(HashSet<Integer> ids);

    /**
     * 获取播报内容配置详情
     *
     * @param id 主键ID
     * @return 播报内容配置DTO
     */
    BroadcastContentConfigDto get(Integer id);

    /**
     * 批量上传图片并创建播报内容配置
     *
     * @param files 上传的文件数组
     * @return 批量上传结果
     */
    BatchUploadResultDto batchUpload(MultipartFile[] files);
}
