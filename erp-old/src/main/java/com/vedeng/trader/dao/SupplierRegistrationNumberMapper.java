package com.vedeng.trader.dao;

import com.vedeng.trader.model.SupplierRegistrationNumber;
import com.vedeng.trader.model.SupplierRegistrationNumberExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SupplierRegistrationNumberMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SUPPLIER_REGISTRATION_NUMBER
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    int countByExample(SupplierRegistrationNumberExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SUPPLIER_REGISTRATION_NUMBER
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    int deleteByExample(SupplierRegistrationNumberExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SUPPLIER_REGISTRATION_NUMBER
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    int deleteByPrimaryKey(Integer supplierRegistrationNumberId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SUPPLIER_REGISTRATION_NUMBER
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    int insert(SupplierRegistrationNumber record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SUPPLIER_REGISTRATION_NUMBER
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    int insertSelective(SupplierRegistrationNumber record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SUPPLIER_REGISTRATION_NUMBER
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    List<SupplierRegistrationNumber> selectByExample(SupplierRegistrationNumberExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SUPPLIER_REGISTRATION_NUMBER
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    SupplierRegistrationNumber selectByPrimaryKey(Integer supplierRegistrationNumberId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SUPPLIER_REGISTRATION_NUMBER
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    int updateByExampleSelective(@Param("record") SupplierRegistrationNumber record, @Param("example") SupplierRegistrationNumberExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SUPPLIER_REGISTRATION_NUMBER
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    int updateByExample(@Param("record") SupplierRegistrationNumber record, @Param("example") SupplierRegistrationNumberExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SUPPLIER_REGISTRATION_NUMBER
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    int updateByPrimaryKeySelective(SupplierRegistrationNumber record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SUPPLIER_REGISTRATION_NUMBER
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    int updateByPrimaryKey(SupplierRegistrationNumber record);
    /**
     * <b>Description:</b>根据traderId和numberId查询证书信息<br>
     * @param
     * @return
     * @Note
     * <b>@Author:calvin</b>
     * <br><b>@Date:</b> 2020/11/30
     */
    SupplierRegistrationNumber getInfoByTraderIdAndNumberId(SupplierRegistrationNumber number);
}