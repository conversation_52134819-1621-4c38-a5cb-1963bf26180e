var head_height=62,content_height=$(window).height()-head_height;$(function(){var s=content_height-50,a=$(".side-nav").height(),e=Math.floor(a/s),i=0;$(".main-frame").css("height",content_height+"px"),$(".side-nav-content").css("height",s+"px"),s>=a&&$(".scroll-btn").addClass("disabled"),$(".scroll-btn").click(function(){if(e>2){if($(this).hasClass("disabled"))return!1;$(this).hasClass("prev")?i--:i++,$(".scroll-btn").removeClass("disabled"),0===i&&$(".side-bar .next").addClass("disabled"),Math.abs(i)>e&&$(this).addClass("disabled"),$(".side-nav").stop(!1,!0).animate({top:s/2*i})}else{if($(this).hasClass("disabled"))return!1;$(this).hasClass("prev")&&($(".side-nav").stop(!1,!0).animate({top:s-a}),$(".side-bar .next").removeClass("disabled"),$(this).addClass("disabled")),$(this).hasClass("next")&&($(".side-nav").stop(!1,!0).animate({top:0}),$(".side-bar .prev").removeClass("disabled"),$(this).addClass("disabled"))}}),$(".side-nav a").on("click",function(){return $(this).hasClass("active")?!1():($(".side-nav a").removeClass("active"),void $(this).addClass("active"))})}),$(window).resize(function(){var s=$(window).height()-head_height,a=s-50;$(".side-nav").stop(!1,!0).animate({top:0}),$(".main-frame").css("height",s+"px"),$(".side-nav-content").css("height",a+"px")});