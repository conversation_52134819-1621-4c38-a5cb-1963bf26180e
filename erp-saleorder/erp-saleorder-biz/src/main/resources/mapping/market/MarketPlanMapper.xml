<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.market.dao.MarketPlanMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.market.domain.entity.MarketPlan">
    <!--@mbg.generated-->
    <!--@Table T_MARKETING_PLAN-->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="PLAN_NAME" jdbcType="VARCHAR" property="planName" />
    <result column="PLAN_CREATE_TIME" jdbcType="TIMESTAMP" property="planCreateTime" />
    <result column="PLAN_START_TIME" jdbcType="TIMESTAMP" property="planStartTime" />
    <result column="PLAN_END_TIME" jdbcType="TIMESTAMP" property="planEndTime" />
    <result column="CONTENT_TYPE" jdbcType="INTEGER" property="contentType" />
    <result column="PROMOTION_CHANNELS" jdbcType="INTEGER" property="promotionChannels" />
    <result column="POSTER_URL" jdbcType="LONGVARCHAR" property="posterUrl" />
    <result column="MARKETING_SCRIPT_LIST_JSON" jdbcType="LONGVARCHAR" property="marketingScriptListJson" />
    <result column="PLAN_STATUS" jdbcType="INTEGER" property="planStatus" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="CREATOR_ID" jdbcType="INTEGER" property="creatorId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="UPDATOR" jdbcType="VARCHAR" property="updator" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="SYSTEM_ADD_TIME" jdbcType="TIMESTAMP" property="systemAddTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, PLAN_NAME, PLAN_CREATE_TIME, PLAN_START_TIME, PLAN_END_TIME, CONTENT_TYPE, PROMOTION_CHANNELS, 
    POSTER_URL, MARKETING_SCRIPT_LIST_JSON, PLAN_STATUS, CREATOR_NAME, CREATOR_ID, ADD_TIME, 
    UPDATOR, MOD_TIME, SYSTEM_ADD_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_MARKETING_PLAN
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_MARKETING_PLAN
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.erp.market.domain.entity.MarketPlan">
    <!--@mbg.generated-->
    insert into T_MARKETING_PLAN (ID, PLAN_NAME, PLAN_CREATE_TIME, 
      PLAN_START_TIME, PLAN_END_TIME, CONTENT_TYPE, 
      PROMOTION_CHANNELS, POSTER_URL, MARKETING_SCRIPT_LIST_JSON, 
      PLAN_STATUS, CREATOR_NAME, CREATOR_ID, 
      ADD_TIME, UPDATOR, MOD_TIME, 
      SYSTEM_ADD_TIME)
    values (#{id,jdbcType=INTEGER}, #{planName,jdbcType=VARCHAR}, #{planCreateTime,jdbcType=TIMESTAMP}, 
      #{planStartTime,jdbcType=TIMESTAMP}, #{planEndTime,jdbcType=TIMESTAMP}, #{contentType,jdbcType=INTEGER}, 
      #{promotionChannels,jdbcType=INTEGER}, #{posterUrl,jdbcType=LONGVARCHAR}, #{marketingScriptListJson,jdbcType=LONGVARCHAR}, 
      #{planStatus,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{creatorId,jdbcType=INTEGER}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updator,jdbcType=VARCHAR}, #{modTime,jdbcType=TIMESTAMP}, 
      #{systemAddTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.erp.market.domain.entity.MarketPlan">
    <!--@mbg.generated-->
    insert into T_MARKETING_PLAN
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="planName != null">
        PLAN_NAME,
      </if>
      <if test="planCreateTime != null">
        PLAN_CREATE_TIME,
      </if>
      <if test="planStartTime != null">
        PLAN_START_TIME,
      </if>
      <if test="planEndTime != null">
        PLAN_END_TIME,
      </if>
      <if test="contentType != null">
        CONTENT_TYPE,
      </if>
      <if test="promotionChannels != null">
        PROMOTION_CHANNELS,
      </if>
      <if test="posterUrl != null">
        POSTER_URL,
      </if>
      <if test="marketingScriptListJson != null">
        MARKETING_SCRIPT_LIST_JSON,
      </if>
      <if test="planStatus != null">
        PLAN_STATUS,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="creatorId != null">
        CREATOR_ID,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="updator != null">
        UPDATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="systemAddTime != null">
        SYSTEM_ADD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="planName != null">
        #{planName,jdbcType=VARCHAR},
      </if>
      <if test="planCreateTime != null">
        #{planCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="planStartTime != null">
        #{planStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="planEndTime != null">
        #{planEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contentType != null">
        #{contentType,jdbcType=INTEGER},
      </if>
      <if test="promotionChannels != null">
        #{promotionChannels,jdbcType=INTEGER},
      </if>
      <if test="posterUrl != null">
        #{posterUrl,jdbcType=LONGVARCHAR},
      </if>
      <if test="marketingScriptListJson != null">
        #{marketingScriptListJson,jdbcType=LONGVARCHAR},
      </if>
      <if test="planStatus != null">
        #{planStatus,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null">
        #{updator,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="systemAddTime != null">
        #{systemAddTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.market.domain.entity.MarketPlan">
    <!--@mbg.generated-->
    update T_MARKETING_PLAN
    <set>
      <if test="planName != null">
        PLAN_NAME = #{planName,jdbcType=VARCHAR},
      </if>
      <if test="planCreateTime != null">
        PLAN_CREATE_TIME = #{planCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="planStartTime != null">
        PLAN_START_TIME = #{planStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="planEndTime != null">
        PLAN_END_TIME = #{planEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contentType != null">
        CONTENT_TYPE = #{contentType,jdbcType=INTEGER},
      </if>
      <if test="promotionChannels != null">
        PROMOTION_CHANNELS = #{promotionChannels,jdbcType=INTEGER},
      </if>
      <if test="posterUrl != null">
        POSTER_URL = #{posterUrl,jdbcType=LONGVARCHAR},
      </if>
      <if test="marketingScriptListJson != null">
        MARKETING_SCRIPT_LIST_JSON = #{marketingScriptListJson,jdbcType=LONGVARCHAR},
      </if>
      <if test="planStatus != null">
        PLAN_STATUS = #{planStatus,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        CREATOR_ID = #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null">
        UPDATOR = #{updator,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="systemAddTime != null">
        SYSTEM_ADD_TIME = #{systemAddTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.market.domain.entity.MarketPlan">
    <!--@mbg.generated-->
    update T_MARKETING_PLAN
    set PLAN_NAME = #{planName,jdbcType=VARCHAR},
      PLAN_CREATE_TIME = #{planCreateTime,jdbcType=TIMESTAMP},
      PLAN_START_TIME = #{planStartTime,jdbcType=TIMESTAMP},
      PLAN_END_TIME = #{planEndTime,jdbcType=TIMESTAMP},
      CONTENT_TYPE = #{contentType,jdbcType=INTEGER},
      PROMOTION_CHANNELS = #{promotionChannels,jdbcType=INTEGER},
      POSTER_URL = #{posterUrl,jdbcType=LONGVARCHAR},
      MARKETING_SCRIPT_LIST_JSON = #{marketingScriptListJson,jdbcType=LONGVARCHAR},
      PLAN_STATUS = #{planStatus,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      CREATOR_ID = #{creatorId,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      UPDATOR = #{updator,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      SYSTEM_ADD_TIME = #{systemAddTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=INTEGER}
  </update>
</mapper>