package com.vedeng.erp.finance.service.impl.check.handler;

import cn.hutool.core.util.StrUtil;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.aftersale.dto.AfterSalesDetailApiDto;
import com.vedeng.erp.aftersale.dto.AfterSalesDto;
import com.vedeng.erp.aftersale.service.AfterSalesApiService;
import com.vedeng.erp.finance.enums.InvoiceTaxTypeEnum;
import com.vedeng.erp.finance.service.AbstractCheckHandler;
import com.vedeng.erp.finance.dto.InvoiceCheckRequestDto;
import com.vedeng.erp.finance.dto.InvoiceCheckResultDto;
import com.vedeng.erp.finance.enums.CheckHandlerEnum;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import com.vedeng.erp.settlement.mapper.CapitalBillMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 专票对公收款校验
 */
@Service
@Slf4j
public class SpecialTicketCheckHandler extends AbstractCheckHandler {

    @Autowired
    private SaleOrderApiService saleOrderApiService;
    @Autowired
    private AfterSalesApiService afterSalesApiService;
    @Autowired
    private CapitalBillMapper capitalBillMapper;

    @Override
    public void handleSalesCheck(InvoiceCheckRequestDto invoiceCheckRequestDto, InvoiceCheckResultDto invoiceCheckResultDto) {
        log.info("专票对公收款校验-销售,invoiceCheckRequestDto:{}", invoiceCheckRequestDto);
        SaleorderInfoDto saleOrder = saleOrderApiService.getBySaleOrderId(invoiceCheckRequestDto.getRelatedId());
        if (Objects.isNull(saleOrder)) {
            throw new ServiceException("专票对公收款校验-销售,订单不存在");
        }
        Integer invoiceType = saleOrder.getInvoiceType();
        Integer saleorderId = saleOrder.getSaleorderId();
        String traderName = saleOrder.getTraderName();
        BigDecimal realTotalAmount = saleOrder.getRealTotalAmount();
        if (InvoiceTaxTypeEnum.getIsSpecialInvoice(invoiceType)) {
            BigDecimal publicIncome = capitalBillMapper.getSaleOrderPublicIncome(saleorderId, traderName);
            BigDecimal totalExpenditure = capitalBillMapper.getSaleOrderTotalExpenditure(saleorderId);
            BigDecimal totalExpenditureBalance = capitalBillMapper.getSaleOrderTotalExpenditureBalance(saleorderId);
            BigDecimal subtractResult = publicIncome.subtract(totalExpenditure.add(totalExpenditureBalance));
            if (subtractResult.compareTo(realTotalAmount) < 0) {
                CheckHandlerEnum checkHandlerEnum = getCheckHandlerEnum();
                String promptText = StrUtil.format(checkHandlerEnum.getPromptText(), subtractResult, realTotalAmount);
                buildResult(invoiceCheckResultDto, promptText);
            }
        }
    }

    @Override
    public void handleAfterCheck(InvoiceCheckRequestDto invoiceCheckRequestDto, InvoiceCheckResultDto invoiceCheckResultDto) {
        log.info("专票对公收款校验-售后,invoiceCheckRequestDto:{}", invoiceCheckRequestDto);
        AfterSalesDetailApiDto afterSalesDetailApiDto = afterSalesApiService.getAfterSalesDetailByAfterSalesId(invoiceCheckRequestDto.getRelatedId());
        if (Objects.isNull(afterSalesDetailApiDto)) {
            throw new ServiceException("专票对公收款校验-售后,售后单不存在");
        }
        Integer invoiceType = afterSalesDetailApiDto.getInvoiceType();
        Integer afterSalesId = afterSalesDetailApiDto.getAfterSalesId();
        String traderName = afterSalesDetailApiDto.getTraderName();
        BigDecimal serviceAmount = afterSalesDetailApiDto.getServiceAmount();
        if (InvoiceTaxTypeEnum.getIsSpecialInvoice(invoiceType)) {
            BigDecimal publicIncome = capitalBillMapper.getAfterSalesPublicIncome(afterSalesId, traderName);
            if (publicIncome.compareTo(serviceAmount) < 0) {
                CheckHandlerEnum checkHandlerEnum = getCheckHandlerEnum();
                String promptText = StrUtil.format(checkHandlerEnum.getPromptText(), publicIncome, serviceAmount);
                buildResult(invoiceCheckResultDto, promptText);
            }
        }
    }
}
