package com.vedeng.erp.finance.domain.entity;

import java.util.Date;

import com.vedeng.common.core.base.BaseDto;
import lombok.Data;

/**
    * 付款申请回单关联表
    */
@Data
public class RPayApplyJBankReceiptEntity extends BaseDto {
    /**
    * 主键
    */
    private Long rPayApplyJBankReceiptId;

    /**
    * 回单解析表ID
    */
    private Long bankReceiptAliasId;

    /**
    * 银行ID
    */
    private Integer bankId;

    /**
    * 付款申请ID
    */
    private Integer payApplyId;

    /**
    * 是否删除 0否 1是
    */
    private Integer isDelete;

    /**
    * 更新备注
    */
    private String updateRemark;
}
