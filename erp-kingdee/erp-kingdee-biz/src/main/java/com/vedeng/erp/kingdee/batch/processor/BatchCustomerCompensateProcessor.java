package com.vedeng.erp.kingdee.batch.processor;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.BatchCustomerFinanceDto;
import com.vedeng.erp.kingdee.domain.entity.KingDeeCustomerEntity;
import com.vedeng.erp.kingdee.dto.KingDeeCustomerDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeCustomerMapper;
import com.vedeng.erp.trader.service.TraderCustomerApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 客户补偿处理器
 */
@Service
@Slf4j
public class BatchCustomerCompensateProcessor implements ItemProcessor<BatchCustomerFinanceDto, KingDeeCustomerDto> {

    @Autowired
    private TraderCustomerApiService traderCustomerApiService;

    @Override
    public KingDeeCustomerDto process(BatchCustomerFinanceDto batchCustomerFinanceDto) throws Exception {
        if (Objects.isNull(batchCustomerFinanceDto) || Objects.isNull(batchCustomerFinanceDto.getTraderCustomerId())) {
            log.info("客户补偿处理器，traderCustomerId为空，不进行处理");
            return null;
        }
        KingDeeCustomerDto kingDeeCustomerInfo = traderCustomerApiService.getKingDeeCustomerInfo(batchCustomerFinanceDto.getTraderCustomerId());
        if (Objects.isNull(kingDeeCustomerInfo)) {
            log.info("客户补偿处理器，traderCustomerId:{}，未查询到客户信息", batchCustomerFinanceDto.getTraderCustomerId());
            return null;
        }
        if (KingDeeBizEnums.updateCustomer.equals(kingDeeCustomerInfo.getKingDeeBizEnums())) {
            log.info("客户补偿处理器，traderCustomerId:{}，客户信息为更新，不进行处理", batchCustomerFinanceDto.getTraderCustomerId());
            return null;
        }
        log.info("客户补偿处理器，traderCustomerId:{}，kingDeeCustomerInfo:{}", batchCustomerFinanceDto.getTraderCustomerId(), JSON.toJSONString(kingDeeCustomerInfo));
        return kingDeeCustomerInfo;
    }
}