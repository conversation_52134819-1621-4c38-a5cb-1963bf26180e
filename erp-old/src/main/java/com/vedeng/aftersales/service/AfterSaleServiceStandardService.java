package com.vedeng.aftersales.service;

import com.vedeng.aftersales.component.dto.AfterSaleServiceLabelDto;
import com.vedeng.aftersales.model.*;
import com.vedeng.aftersales.model.dto.*;
import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface AfterSaleServiceStandardService {

    List<AfterSaleServiceStandardApplyDto> querylistPage(AfterSaleServiceStandardApplyQueryDto queryDto, Page page);

    int insertServiceStandardApply(AfterSaleServiceStandardApply afterSaleServiceStandardApply);

    AfterSaleServiceStandardApply findServiceStandardApplyById(Long serviceStandardApplyId);

    AfterSaleServiceStandardApply selectAfterSaleServiceStandardBySkuNo(String skuNo);

    void addSupplyAfterSalePolicy(HttpServletRequest request, AddSupplyAfterSalePolicyDto addSupplyAfterSalePolicyDto, User user) throws Exception;

    List<AfterSaleSupplyPolicyDto> getSupplyAfterSalePolicyListBySkuNo(String skuNo);

    void editBdAfterSalePolicyApply(HttpServletRequest request, EditBdAfterSalePolicyDto editBdAfterSalePolicyDto, User user);

    List<AfterSaleServiceStandardApplyAttachment> selectAfterSaleServiceStandardAttashmentList(Long serviceStandardApplyId);

    List<AfterSaleServiceStandardApplyDto> querySimilarProductListPage(ReferenceSimilarProductQueryDto queryDto, Page page);

    Map<String,String> queryMaxAndMinInstallFee(ReferenceSimilarProductQueryDto queryDto);

    List<AfterSaleServiceStandardApplyDto> maintainInstallAreaListPage(MaintainInstallAreaDto queryDto, Page page);

    List<AfterSaleSupplyPolicyDto> referenceSupplyAfterSalePolicy(String skuNo,Page page);

    void copySupplyAfterSalePolicy(String skuNo, Long supplyPolicyId,Integer copyType) throws Exception;

    List<AfterSaleServiceStandardApplyAttachment> getAfterSaleServiceStandardAttashmentById(Long serviceStandardApplyId);

    void verifyFinish(Long serviceStandardApplyId, boolean pass);

    AfterSaleSupplyPolicyDto findSupplyAfterSalePolicy(Long supplyPolicyId);

    List<CopySupplyAfterSalePolicyDto> querySupplyAfterSalePolicyListPage(CopySupplyAfterSalePolicyQueryDto queryDto, Page page);

    List<AfterSaleSupplyPolicyDto> findSupplyAfterSalePolicyByTraderId(Long traderId);

    ResultInfo copySupplyAfterSalePolicyList(String skuNo, Long supplyPolicyId, User user, Integer copyType, Integer formerSupplyPolicyId);

    void updateBySkuNo(AfterSaleServiceStandardApply updateApply);

    void deleteSupplyPolicyById(Long supplyPolicyId);

    void modifySupplyAfterSalePolicy(HttpServletRequest request, EditSupplyAfterSalePolicyDto editSupplyAfterSalePolicyDto, User user);

    AfterSaleSupplyPolicy findSupplyAfterSalePolicyBySkuNoAndTraderId(Long traderId, String sku);

    AfterSaleServiceStandardInfoDto getEffectAfterSalePolicy(String skuNo);

    Map<String, BigDecimal> findLastestYearNumAndAmout(String skuNo, Long traderId);

    List<AfterSaleSupplyPolicyAttachment> findSupplyAfterSalePolicyAttashMent(Long supplyPolicyId);

    List<AfterSaleSupplyPolicyDto> findSupplyPolicyBySkuNoAndServiceType(String skuNo, Integer serviceType);

    Long copyOrginalFactorySupplyPolicy(String skuNo, Long traderId,User user);

    void copyOrginalFactoryToCurrentSuppplyPolicy(Long supplyPolicyId, User user);

    AfterSaleSupplyPolicy getSupplyAfterSalePolicyListBySkuNoAndTraderId(String skuNo, Long traderId);

    AfterSaleServiceStandardApplyInstallArea queryInstallArea(String skuNo);

    AfterSaleServiceStandardApplyInstallArea selectServiceStandardApplyInstallArea(Long serviceStandardApplyId);

    List<AfterSaleServiceStandardInfoAttachment> getAttashmentListByInfoId(Long serviceStandardInfoId);

    List<AfterSaleServiceLabelDto> getAfterSaleServiceLabels(String skuNo);

    List<AfterSaleServiceLabelDto> getAllAfterSaleServiceLabels(String skuNo);
}
