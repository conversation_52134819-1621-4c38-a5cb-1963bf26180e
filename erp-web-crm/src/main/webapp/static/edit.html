<!DOCTYPE html>

<html>

<head>
    <title>CRM-线索</title>
    <link rel="stylesheet" href="./css/common/common.css">
    <link rel="stylesheet" href="./ui/ui.css">
    <link rel="stylesheet" href="./css/pages/businessLeads.css">
</head>

<body>
    <input type="hidden" id="isHiddenLayout" value="1">
    <div class="page-wrap" id="page-container">
        <page-header></page-header>
        <div class="page-container">
            <page-side></page-side>
            <div class="page-main">

                <div class="form-wrap" style="padding: 40px 0;">
                    <div class="card">
                        <div class="card-title">线索来源</div>
                        <div>
                            <ui-form-item label="线索类型" :must="true" :text="true">
                                <div class="ui-col-12">
                                    <div>总计询价</div>
                                </div>
                            </ui-form-item>
                            <ui-form-item label="企微提醒" :must="true" :text="true">
                                <div class="ui-col-12">
                                    <ui-radio-group
                                        :list="radioList1"
                                        :value.sync="sendVx"
                                        :disabled="isEdit"
                                    ></ui-radio-group>
                                    <p class="form-tip" v-show="sendVx == 1">- 当选择“提醒”时会触发，企业微信卡片消息提醒，提醒人“归属销售”</p>
                                </div>
                            </ui-form-item>
                            <ui-form-item label="询价行为" :must="true" :text="true">
                                <div class="ui-col-12">
                                    <ui-radio-group
                                        :list="SysOptions"
                                        :value.sync="inquiry"
                                    ></ui-radio-group>
                                </div>
                            </ui-form-item>
                            <ui-form-item label="渠道类型" :must="true" :text="true">
                                <div class="ui-col-12">
                                    <ui-radio-group
                                        :list="Sources"
                                        :value.sync="source"
                                        @change="changeSource"
                                    ></ui-radio-group>
                                </div>
                            </ui-form-item>
                            <ui-form-item label="渠道名称" :must="true" :text="true" v-if="SourceNames.length">
                                <div class="ui-col-12">
                                    <ui-radio-group
                                        :list="SourceNames"
                                        :value.sync="sourceName"
                                    ></ui-radio-group>
                                </div>
                            </ui-form-item>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-title">线索详情</div>
                        <div>
                            <ui-form-item label="三级分类" :must="true">
                                <div class="ui-col-6">
                                    <ui-search-related
                                        base-url="/crm/category/public/findThreeCategory"
                                        width="490"
                                        :size="5"
                                        @change="handlerCategory"
                                    ></ui-search-related>
                                </div>
                            </ui-form-item>
                            <ui-form-item label="产品信息" :must="true">
                                <ui-input 
                                    type="textarea"
                                    maxlength="1000"
                                    v-model="goodsInfo"
                                    height-auto
                                    height="35px"
                                    width="823px"
                                ></ui-input>
                            </ui-form-item>
                            <ui-form-item label="客户名称" :must="true">
                                <div class="ui-col-8">
                                    <ui-tyc
                                        width="490px"
                                        placeholder="请准确填写客户名称"
                                        v-model="traderName"
                                    ></ui-tyc>
                                </div>
                            </ui-form-item>
                            <ui-form-item label="联系人">
                                <ui-input v-model="contact" width="323px" maxlength="20"></ui-input>
                            </ui-form-item>
                            <ui-form-item label="手机">
                                <ui-input type="number" v-model="phone" width="323px" maxlength="11"></ui-input>
                            </ui-form-item>
                            <ui-form-item label="固话">
                                <ui-input type="number" v-model="telephone" width="323px" maxlength="20"></ui-input>
                            </ui-form-item>
                            <ui-form-item label="其他联系方式">
                                <ui-input type="number" v-model="otherContactInfo" width="323px" maxlength="50"></ui-input>
                            </ui-form-item>
                            <ui-form-item label="地区" :must="true">
                                <ui-cascader
                                    width="323px"
                                    class="margin" 
                                    :data="addressData" 
                                    v-model="area" 
                                    clearable 
                                    filterable
                                    @change="handleChange"
                                ></ui-cascader>
                            </ui-form-item>
                            <ui-form-item label="归属销售" :must="true">
                                <ui-select
                                    width="323px"
                                    :remote="true" 
                                    :avatar="true" 
                                    placeholder="请输入并选择归属人" 
                                    v-model="belongerId" 
                                    clearable 
                                    :remote-info="allUserRemoteInfo" 
                                    valid="editBusinessleads_belongerId"
                                ></ui-select>
                            </ui-form-item>
                            <ui-form-item label="备注" :must="true">
                                <ui-input
                                    width="823px"
                                    type="textarea"
                                    maxlength="1000"
                                    v-model="beizhu"
                                    height-auto
                                    height="35px"
                                    width="823px"
                                ></ui-input>
                            </ui-form-item>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="./js/common/vue.js"></script>
    <script src="./js/common/axios.js"></script>
    <script src="./js/common/lodash.min.js"></script>
    <script src="./ui/ui.js"></script>
    <script src="./js/common/layout.js"></script>
    <script src="./js/pages/businessleadsEdit.js"></script>
</body>

</html>