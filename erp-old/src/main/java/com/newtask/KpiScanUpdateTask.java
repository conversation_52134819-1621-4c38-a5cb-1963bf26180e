package com.newtask;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.DateUtil;
import com.vedeng.kpi.cache.KpiLoadingCache;
import com.vedeng.kpi.model.DO.KpiOrderLogDo;
import com.vedeng.kpi.model.DTO.KpiDataQueryDto;
import com.vedeng.kpi.model.DTO.KpiUserConfigDto;
import com.vedeng.kpi.model.DTO.KpiUserInfoDto;
import com.vedeng.kpi.model.base.KpiBaseParams;
import com.vedeng.kpi.service.KpiBaseService;
import com.vedeng.kpi.service.KpiOrderLogService;
import com.vedeng.kpi.service.KpiParamTransService;
import com.vedeng.kpi.service.KpiUpdateService;
import com.vedeng.kpi.share.KpiCommonConstant;
import com.vedeng.kpi.share.KpiUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@JobHandler(value="KpiScanUpdateTask")
public class KpiScanUpdateTask extends AbstractJobHandler {

    private static final Logger log = LoggerFactory.getLogger("kpilog");

    @Autowired
    KpiOrderLogService kpiOrderLogService;

    @Autowired
    KpiUpdateService kpiUpdateService;

    @Autowired
    KpiLoadingCache kpiLoadingCache;

    @Autowired
    KpiParamTransService kpiParamTransService;

    /**
     * 批量更新.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/9 2:15 下午.
     * @author: Tomcat.Hui.
     * @param date: .
     * @return: void.
     * @throw: .
     */
    private void updateKpiBatch(Date date){
        XxlJobLogger.log("五行消费用户kpi更新开始，计算时间：{}",date.toString());
        try {

            List<KpiOrderLogDo> logs = kpiOrderLogService.getUserToUpdate(date);

            //待更新消费状态位,为了防止使用in查询SQL过长,改取首尾ID,更新这批ID以内所有数据消费状态位
            Integer maxUnuseIds = logs.stream().map(KpiOrderLogDo::getId)
                    .collect(Collectors.maxBy(Comparator.comparing(Integer::intValue))).orElse(0);
            Integer minUnuseIds = logs.stream().map(KpiOrderLogDo::getId)
                    .collect(Collectors.minBy(Comparator.comparing(Integer::intValue))).orElse(0);

            //按用户去重,同一用户只取本月最后一条更新,避免重复
            List<KpiUserInfoDto> users =  logs.stream().collect(Collectors.groupingBy(KpiOrderLogDo::getUserId,
                    Collectors.collectingAndThen(Collectors.maxBy(Comparator.comparing(KpiOrderLogDo::getKpiDate)),
                            Optional::get)))
                    .values().stream().map(log -> kpiParamTransService.logToUserInfo(log,date))
                    .collect(Collectors.toList());

            XxlJobLogger.log("查询到当月可消费记录 " + users.size() + " 条");
            //按照团队分组,这样可以共享团队数据查询,加快速度
            Map<Integer,List<KpiUserInfoDto>> groupUserList =
                    users.stream().collect(Collectors.groupingBy(KpiBaseParams::getGroupId));

            for (Integer groupId : groupUserList.keySet()) {
                //设置团队共享threadlocale
                KpiDataQueryDto query = new KpiDataQueryDto();
                query.setUserIds(kpiLoadingCache.getGroupUsersByGroupId(groupId)
                        .stream().map(KpiUserConfigDto::getUserId).collect(Collectors.toList()));
                query.setGroupId(groupId);
                query.setKpiDateStart(KpiUtils.getMonthStart(date));
                query.setKpiDateEnd(KpiUtils.getDateStart(date));
                query.setKpiDate(KpiUtils.getDateStart(date));
                query.setStartMillisecond(KpiUtils.getMonthStart(date).getTime());
                query.setEndMillisecond(KpiUtils.getDateEnd(date).getTime());
                query.setDays(KpiCommonConstant.KPI_CUSTOMERS_DAYS_90);
                KpiBaseService.initThreadParams(query);

                for (KpiUserInfoDto user : groupUserList.get(groupId)) {
                    XxlJobLogger.log("开始更新 " + user.getUserIdName() + " 五行数据");
                    kpiUpdateService.updateKpiSingle(user);
                    XxlJobLogger.log("更新 " + user.getUserIdName() + " 五行数据结束");
                }
            }

            XxlJobLogger.log("开始更新消费状态位,ID区间: " + minUnuseIds + " - " + maxUnuseIds);
            KpiDataQueryDto query = new KpiDataQueryDto();
            query.setStartId(minUnuseIds);
            query.setEndId(maxUnuseIds);
            Integer updateResult = kpiOrderLogService.updateUnusedLog(query);
            XxlJobLogger.log("更新消费状态位,ID区间: " + minUnuseIds + " - " + maxUnuseIds + " 成功条数:" + updateResult);

        }catch (Exception e){
            XxlJobLogger.log("五行用户kpi更新出现异常",e);
        }finally {
        }
    }

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        Date date = null;
        XxlJobLogger.log("XXL-JOB, Hello World.");
        if (StringUtils.isNotBlank(param)){
            date = DateUtil.StringToDate(param);
        }
        if (date == null) {
            date = new Date();
        }
        updateKpiBatch(date);
        return SUCCESS;
    }
}
