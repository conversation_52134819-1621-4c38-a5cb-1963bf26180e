package com.vedeng.doc.common;

/**
 * 营业执照、生产许可、生产产品登记表、经营许可、二类备案、产品授权、厂家其他荣誉、售后服务承诺书、一类生产备案凭证、销售人员授权书、聚合页
 *
 * <AUTHOR>
 * @create 2022/1/10 20:36
 */
public enum SupplierSortEnum {

    BUSINESS_PERMIT(1, "营业执照"),
    PRODUCTION_LICENSE(1, "生产许可"),
    PRODUCTION_PRODUCT_REGISTRATION_FORM(1, "生产产品登记表"),
    BUSINESS_LICENSE(1, "经营许可"),
    CLASS_II_FILING(1, "二类备案"),
    PRODUCT_AUTHORIZATION(1, "产品授权"),
    MANUFACTURERS_OTHER_HONORS(1, "厂家其他荣誉"),
    AFTER_SALES_SERVICE_COMMITMENT(1, "售后服务承诺书"),
    CLASS_I_PRODUCTION_RECORD_CERTIFICATE(1, "一类生产备案凭证"),
    SALESPERSON_POWER_OF_ATTORNEY(1, "销售人员授权书"),
    AGGREGATE_PAGES(1, "聚合页");

    private Integer sort;
    private String tagName;

    SupplierSortEnum(Integer sort, String tagName) {
        this.sort = sort;
        this.tagName = tagName;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getTagName() {
        return tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }
}
