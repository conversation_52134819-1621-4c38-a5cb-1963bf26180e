package com.vedeng.goods.mapper;

import com.vedeng.goods.domain.entity.SkuSceneCategoryEntity;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface SkuSceneCategoryMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(SkuSceneCategoryEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(SkuSceneCategoryEntity record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    SkuSceneCategoryEntity selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(SkuSceneCategoryEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(SkuSceneCategoryEntity record);

    int updateBatchSelective(List<SkuSceneCategoryEntity> list);

    int batchInsert(List<SkuSceneCategoryEntity> list);

    List<SkuSceneCategoryEntity> findBySceneIdAndIsDelete(@Param("sceneId") Long sceneId);

    int updateIsDeleteById(@Param("id") Long id);
}