package com.vedeng.common.constant;

/**
 * <b>Description:</b><br>
 * erp常量
 *
 * <AUTHOR>
 * @Note <b>ProjectName:</b> erp <br>
 *       <b>PackageName:</b> com.vedeng.common.constant <br>
 *       <b>ClassName:</b> ErpConst <br>
 *       <b>Date:</b> 2017年5月18日 下午1:35:49
 */
public class ErpConst {

	public final static String BD = "南京贝登医疗股份有限公司";

	/**
	 * 南京分公司Id
	 */
	public static final Integer NJ_COMPANY_ID=1;

	public static final Integer NJ_ADMIN_ID = 2;
	public static final String NJ_ADMIN_NAME = "njadmin";

	public static final Integer ADMIN_ID = 1;

	public static final Integer ZERO = 0;
	public static final Integer ROOT_REGION = 100000;

	public static final Integer MINUS_ONE = -1;

	public static final Integer MINUS_TWO = -2;

	/**
	 * @Fields ONE : TODO 1
	 */
	public static final Integer ONE = 1;

	/**
	 * @Fields TWO : TODO 2
	 */
	public static final Integer TWO = 2;
	/**
	 * @Fields THREE : TODO 3
	 */
	public static final Integer THREE = 3;
	/**
	 * @Fields FOUR : TODO 4
	 */
	public static final Integer FOUR = 4;
	/**
	 * @Fields FIVE : TODO 5
	 */
	public static final Integer FIVE = 5;
	/**
	 * @Fields FIVE : TODO 5
	 */
	public static final Integer SIX = 6;

	/**
	 * SEVEN
	 */
	public static final Integer SEVEN = 7;

	/**
	 * EIGHT
	 */
	public static final Integer EIGHT = 8;

	/**
	 * @Fields FIVE : TODO 100
	 */
	public static final Integer HUNDRED = 100;

	/**
	 * 已锁定
	 */
	public static final Integer LOCK_STATUS = 1;

	/**
	 * erp接入码
	 */
	public static final String ACCESS_CODE = "erp";

	/**
	 * @Fields SCOP_EMPLOYEES : TODO 字典数据
	 */
	public static final String DATA_DICTIONARY_LIST = "sysoptiondefinition/getdatadictionarylist.htm";

	/**
	 * @Fields SCOP_EMPLOYEES : TODO 供应商分页地址
	 */
	public static final String TRADER_SUPPLIER_PAGE = "tradersupplier/gettradersupplierlistpage.htm";

	/**
	 * @Fields SCOP_EMPLOYEES : TODO 供应商置顶地址
	 */
	public static final String TRADER_SUPPLIER_TOP = "tradersupplier/toptradersupplier.htm";

	/**
	 * @Fields SCOP_EMPLOYEES : TODO 供应商禁用地址
	 */
	public static final String TRADER_SUPPLIER_DISABLED = "tradersupplier/disabledtradersupplier.htm";

	/**
	 * @Fields SCOP_EMPLOYEES : TODO 客户分页地址
	 */
	public static final String TRADER_CUSTOMER_PAGE = "tradercustomer/gettradercustomerlistpage.htm";

	/**
	 * @Fields SCOP_EMPLOYEES : TODO 医械购同步税号的地址
	 */
	public static final String SYNC_TRADER_FINANCE_TO_YXG = "/account/company/updateTaxNum.do";
	/**
	 * 获取客户财务信息
	 */

	public static final String GET_TRADER_FINANCE = "tradercustomer/getTraderFinance.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 客户搜索信息查询沟通记录id集合
	 */
	public static final String GET_COMUNICATE_IDS = "tradercustomer/getcommunicaterecordids.htm";

	/**
	 * @Fields SCOP_EMPLOYEES : TODO 客户置顶地址
	 */
	public static final String TRADER_CUSTOMER_TOP = "tradercustomer/toptradercustomer.htm";

	/**
	 * @Fields SCOP_EMPLOYEES : TODO 客户禁用地址
	 */
	public static final String TRADER_CUSTOMER_DISABLED = "tradercustomer/disabledtradercustomer.htm";

	/**
	 * @Fields SCOP_EMPLOYEES : TODO 客户联系人地址列表
	 */
	public static final String TRADER_CONTACTS_ADDRESS = "tradercustomer/getcontactsaddresslist.htm";

	/**
	 * @Fields SCOP_EMPLOYEES : TODO 新增客户联系人
	 */
	public static final String TRADER_CONTACTS_SAVE = "tradercustomer/addtradercontact.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 转移联系人
	 */
	public static final String SAVE_TRANSFER_CONTACTS = "tradercustomer/savetransfercontact.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 禁用联系人
	 */
	public static final String SAVE_DISABLED_CONTACTS = "tradercustomer/savedisabledcontact.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 设置默认联系人
	 */
	public static final String SAVE_DEFAULT_CONTACTS = "tradercustomer/savedefaultcontact.htm";

	/**
	 * @Fields SCOP_EMPLOYEES : TODO 保存客户联地址
	 */
	public static final String TRADER_ADDRESS_SAVE = "tradercustomer/savetraderaddress.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 设置地址禁用启用状态
	 */
	public static final String SAVE_DISABLED_ADDRESS = "tradercustomer/savedisabledaddress.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 设置默认地址
	 */
	public static final String SAVE_DEFAULT_ADDRESS = "tradercustomer/savedefaultaddress.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 查询客户联系人
	 */
	public static final String TRADER_CONTACTS_QUERY = "tradercustomer/querytradercontact.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 查询联系人背景
	 */
	public static final String TRADER_CONTACTS_EXPERIENCE = "tradercustomer/getcontactexperience.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 删除联系人背景
	 */
	public static final String DEL_CONTACTS_EXPERIENCE = "tradercustomer/delcontactexperience.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 查询客户地址
	 */
	public static final String TRADER_ADDRESS_QUERY = "tradercustomer/querytraderaddress.htm";

	/**
	 * @Fields SCOP_EMPLOYEES : TODO 获取客户的财务与资质信息
	 */
	public static final String TRADER_FINANCE_APTITUDE = "tradercustomer/gettraderfinanceandaptitude.htm";
	/**
	 * TODO 获取客户的财务与资质信息
	 */
	public static final String NEW_TRADER_FINANCE_APTITUDE = "tradercustomer/getnewtraderfinanceandaptitude.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 获取交易者的账期分页信息
	 */
	public static final String GET_AMOUNT_BILL_PAGE = "tradercustomer/getamountbilllistpage.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 获取交易者的交易流水分页信息
	 */
	public static final String GET_CAPITAL_BILL_PAGE = "tradercustomer/getcapitalbilllistpage.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 删除供应商的银行帐号
	 */
	public static final String DEL_SUPPLIER_BANK = "tradercustomer/delsupplierbank.htm";

	/**
	 * @Fields SCOP_EMPLOYEES : TODO 保存客户的资质信息
	 */
	public static final String SAVE_APTITUDE = "tradercustomer/saveaptitude.htm";

	/**
	 * @Fields SCOP_EMPLOYEES : TODO 获取客户财务信息
	 */
	public static final String GET_CUSTOMER_FINANCE = "tradercustomer/getcustomerfinance.htm";

	/**
	 * @Fields SCOP_EMPLOYEES : TODO 保存客户财务信息
	 */
	public static final String SAVE_CUSTOMER_FINANCE = "tradercustomer/savecustomerfinance.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO查询客户属于分销还是终端
	 */
	public static final String GET_CUSTOMER_CATEGORY = "tradercustomer/getcustomercategory.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO根据traderId查询客户信息
	 */
	public static final String GET_CUSTOMER_INFO = "trader/getcustomeinfobytraderid.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO根据联系人主键查询联系人的详情和行业背景
	 */
	public static final String GET_CONTACT_DETAIL = "tradercustomer/getcontactdetail.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO保存联系人行业背景
	 */
	public static final String SAVE_CONTACT_EXPERIENCR = "tradercustomer/savecontactexperience.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 保存商机
	 */
	public static final String SAVE_BUSSNESS_CHANCE = "order/bussinesschance/savebussinesschance.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 查询售后商机详情
	 */
	public static final String GET_BUSSNESS_CHANCE_DETAIL = "order/bussinesschance/getbussinesschancedetail.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 保存商机确认后客户的信息
	 */
	public static final String SAVE_CONFIRM_CUSTOMER = "order/bussinesschance/saveconfirmcustomer.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 查询采购订单的分页列表
	 */
	public static final String GET_BUYORDER_PAGE = "order/buyorder/getbuyorderlistpage.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 查询可以被关联的VB单列表
	 */
	public static final String GET_ABLE_VB_PAGE = "order/buyorder/queryVBBuyorderList.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 查询已忽略订单的分页列表
	 */
	public static final String GET_IGNORE_SALEORDER_PAGE = "order/buyorder/getignoresaleorderlistpage.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 查询采购订单的详情信息
	 */
	public static final String GET_BUYORDER_DETAIL = "order/buyorder/getbuyorderdetail.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 查询采购订单的详情信息(分页)
	 */
	public static final String GET_BUYORDER_DETAIL_NEW = "order/buyorder/getbuyorderdetaillistnew.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 页面加载完成后通过AJAX异步补充销售订单的数据（采购订单列表详情页）
	 */
	public static final String GET_SALEORDER_NUM_AJAX = "order/buyorder/getsalebuynumbyajax.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 忽略待采购订单的产品
	 */
	public static final String SAVE_IGNORE = "order/buyorder/saveignore.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 新增采购订单
	 */
	public static final String ADD_BUYORDER_PAGE = "order/buyorder/addbuyorderpage.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 获取加入采购订单页面的产品列表信息
	 */
	public static final String GET_GOODSVO_LIST = "order/buyorder/getgoodsvolist.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 保存新增采购订单
	 */
	public static final String SAVE_ADD_BUYORDER = "order/buyorder/saveaddbuyorder.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 保存编辑采购订单
	 */
	public static final String SAVE_EDIT_BUYORDER = "order/buyorder/saveeditbuyorder.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 保存加入已存在的采购订单
	 */
	public static final String SAVE_HAVED_BUYORDER = "order/buyorder/savehavedbuyorder.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 查询待采购订单的列表
	 */
	public static final String GET_SALESORDERGOODS_LIST = "order/buyorder/getsalesordergoodslist.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 循环拉取待采购订单的列表
	 */
	public static final String GET_PURCHASE_LIST = "order/buyorder/getpurchaselist.htm";

	/**
	 * @Fields SCOP_EMPLOYEES : TODO 查询字典表的列表
	 */
	public static final String GET_SYSTEM_OPTION_LIST = "sysoptiondefinition/getsystemoptionlist.htm";
	/**
	 * @Fields SCOP_EMPLOYEES : TODO 查询字典表的对象
	 */
	public static final String GET_SYSTEM_OPTION_OBJECT = "sysoptiondefinition/getsystemoption.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 查询售后分页信息
	 */
	public static final String GET_AFTERSALES_PAGE = "aftersales/order/getaftersalespage.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 根据orderid查询售后列表信息
	 */
	public static final String GET_AFTERSALESLIST_BYORDERID = "aftersales/order/getaftersaleslistbyorderid.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 保存新增售后
	 */
	public static final String SAVE_ADD_AFTERSALES = "aftersales/order/saveaddaftersales.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 保存编辑售后
	 */
	public static final String SAVE_EDIT_AFTERSALES = "aftersales/order/saveeditaftersales.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 查看售后的详情
	 */
	public static final String VIEW_AFTERSALES_DETAIL = "aftersales/order/viewaftersalesdetail.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 申请审核
	 */
	public static final String APPLY_AFTERSALES_AUDIT = "aftersales/order/applyfatersalesaudit.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 关闭订单
	 */
	public static final String SAVE_AFTERSALES_CLOSE = "aftersales/order/saveaftersalesclose.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 保存编辑的退换货手续费
	 */
	public static final String SAVE_AFTERSALES_REFUNDFEE = "aftersales/order/saveaftersalesrefundfee.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 保存编辑的退票信息
	 */
	public static final String SAVE_AFTERSALES_REFUNDTICKET = "aftersales/order/saveaftersalesrefundticket.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 保存新增或更新的售后过程
	 */
	public static final String SAVE_AFTERSALES_RECORD = "aftersales/order/saveaftersalesrecord.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 获取售后过程
	 */
	public static final String GET_AFTERSALES_RECORD = "aftersales/order/getaftersalesrecord.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 保存编辑的安调信息
	 */
	public static final String SAVE_AFTERSALES_INSTALLSTION = "aftersales/order/saveaftersalesinstallstion.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 新增获取工程师和维修产品
	 */
	public static final String GET_ENIGNEER_INSTALLSTION = "aftersales/order/getenigneerinstallstion.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 编辑获取工程师和维修产品
	 */
	public static final String EDIT_ENIGNEER_INSTALLSTION = "aftersales/order/editenigneerinstallstion.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 获取工程师分页信息
	 */
	public static final String GET_ENIGNEER_PAGE = "aftersales/order/getenigneerpage.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 查询交易者的联系人和财务信息
	 */
	public static final String GET_CUSTOMER_CONTACTANDFINCAE = "aftersales/order/getcustomercontactandfinace.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 保存新增的工程师与售后产品的关系
	 */
	public static final String SAVE_AFTERSALES_ENGINEER = "aftersales/order/saveaftersalesengineer.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 保存编辑的售后安调信息
	 */
	public static final String SAVE_UPDATE_INSTALLSTION = "aftersales/order/saveupdateinstallstion.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 查询售后安调维修申请付款信息
	 */
	public static final String GET_AFTERSALES_APPLYPAY = "aftersales/order/getaftersalesapplypay.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 保存售后安调维修申请付款信息
	 */
	public static final String SAVE_AFTERSALES_APPLYPAY = "aftersales/order/saveaftersalesapplypay.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 执行退款运算操作--退到余额
	 */
	public static final String EXECUTE_REFUND_OPERATION = "aftersales/order/executerefundoperation.htm";

	public static final String CAL_REFUND = "aftersales/order/calRefund.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 保存退货的申请付款
	 */
	public static final String SAVE_RUEFUND_APPLY_PAY = "aftersales/order/saverefundapplypay.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 根据saleorderid查询产品信息列表
	 */
	public static final String GET_SALEORDERGOODS_BYSALEORDERID = "order/saleorder/getsaleordergoodsbysaleorderid.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 查询echarts的数据
	 */
	public static final String GET_ECHARTSVO = "home/page/getecharts.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 获取物流数据中心数据
	 */
	public static final String GET_GETLOGISTICECHARTS = "datacenter/mdtrader/getlogisticsechartsdatavo.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 获取售后数据中心数据
	 */
	public static final String GET_GETAFTERSALESECHARTS = "datacenter/mdtrader/getaftersalesechartsdatavo.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 查询echarts首页地图的客户数据
	 */
	public static final String GET_ECHARTSVO_CUSTOMER = "home/page/getechartscustomer.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 查询售后总监的首页数据
	 */
	public static final String GET_AFTERSALES_DATAVO = "home/page/getaftersalesdatavo.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 销售工程师个人首页-商机跟进列表
	 */
	public static final String GET_SALE_ENGINEER_DATA = "home/page/getbussinesschancevolistpage.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 查询销售工程师个人首页数据
	 */
	public static final String GET_SALEENGINEERDATAVO = "report/homepage/getsaleengineerdatavo.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 保存新增或修改的快递公司
	 */
	public static final String SAVE_LOGISTICE = "logistics/express/saveaddorupdatelogistics.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 设置默认的快递公司
	 */
	public static final String SAVE_SET_DEFAULT_LOGISTICE = "logistics/express/savesetdefaultlogistics.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 查询供应商沟通记录的主键
	 */
	public static final String GET_TAG_TRADERLIST = "tradersupplier/gettagtraderidlist.htm";
	/**
	 * @Fields GET_AFTERSALES_PAGE : TODO 保存提前采购申请
	 */
	public static final String SAVE_APPLY_PURCHASE = "order/saleorder/saveapplypurchase.htm";
	/**
	 * @Fields CURR_USER : TODO session中的user
	 */
	public static final String CURR_USER = "curr_user";

	/**
	 * @Fields SYSTEM_ERROR : TODO返回信息
	 */
	public final static String SYSTEM_ERROR_MSG = "系统错误";

	/**
	 * @Fields SYSTEM_ERROR : TODO返回信息
	 */
	public final static String USERNAME_PWD_NULL_MSG = "用户名或密码不能为空";

	public final static String USERNAME_COMPANY_NULL_MSG = "分公司不能为空";

	public final static String CODE_ERROR_MSG = "验证码错误";

	/**
	 * @Fields USERNAME_PWD_ERROR_MSG : TODO 登录错误
	 */
	public final static String USERNAME_PWD_ERROR_MSG = "用户名或密码错误";

	/**
	 * @Fields SYSTEM_ERROR : TODO返回信息
	 */
	public final static String USER_DISABLED_ERROR_MSG = "用户已被禁用";

	/**
	 * @Fields SYSTEM_ERROR : TODO返回信息
	 */
	public final static String USER_NOT_LOGIN_SYSTEM_MSG = "用户不能登陆此系统";

	/**
	 * @Fields KEY_PREFIX_SESSION : TODO session前置key
	 */
	public final static String KEY_PREFIX_SESSION = "shiro_redis_session:";

	/**
	 * @Fields KEY_PREFIX_USERID_SESSIONID : TODO usderId和sessionId存储redis的前置key
	 */
	public final static String KEY_PREFIX_USERID_SESSIONID = "redis_sessionId_userId_";

	/**
	 * @Fields REDIS_USERID_SESSIONID_TIMEOUT : TODO session过期时间8小时（临时）
	 */
	public final static Integer REDIS_USERID_SESSIONID_TIMEOUT = 8 * 60 * 60;

	/**
	 * @Fields KEY_PREFIX_DATA_DICTIONARY : TODO redis数据字典list的前置key
	 */
	public final static String KEY_PREFIX_DATA_DICTIONARY_LIST = "redis_dictionary_list:";
	/**
	 * KEY_PREFIX_DATA_DICTIONARY_BUSS_LIST：商机渠道3级联动数据
	 */
	public final static String KEY_PREFIX_DATA_DICTIONARY_BUSS_LIST = "redis_dictionary_buss_list:";
	public final static String KEY_PREFIX_DATA_DICTIONARY_TYPE = "redis_dictionary_type:";
	/**
	 * @Fields KEY_PREFIX_DATA_DICTIONARY : TODO redis数据字典object的前置key
	 */
	public final static String KEY_PREFIX_DATA_DICTIONARY_OBJECT = "redis_dictionary_obj:";
	/**
	 * @Fields KEY_PREFIX_DATA_DICTIONARY : TODO redis物流公司列表的前置key
	 */
	public final static String KEY_PREFIX_LOGISTICS_LIST = "redis_logistics_list:";
	/**
	 * @Fields KEY_PREFIX_DATA_DICTIONARY : TODO redis菜单的前置key
	 */
	public final static String KEY_PREFIX_MENU = "curr_user_menu_:";
	/**
	 * @Fields KEY_PREFIX_DATA_DICTIONARY : TODO redis一级菜单的前置key
	 */
	public final static String KEY_PREFIX_GROUP_MENU = "curr_group_menu_:";
	/**
	 * @Fields KEY_PREFIX_DATA_DICTIONARY : TODO redis地区列表的前置key
	 */
	public final static String KEY_PREFIX_REGION_LIST = "redis_regin_list:";
	public final static String KEY_PREFIX_CATEGORY_LIST = "redis_category_list:";
	public final static String KEY_PREFIX_GOODSVALIDATEDRULE = "GoodsValidatedRule:";
	/**
	 * @Fields KEY_PREFIX_DATA_DICTIONARY : TODO redis地区列表的前置key
	 */
	public final static String KEY_PREFIX_ORIGANIZATION_LIST = "redis_origanization_list:";
	/**
	 * @Fields KEY_PREFIX_DATA_DICTIONARY : TODO redis地区对象的前置key
	 */
	public final static String KEY_PREFIX_REGION_OBJECT = "redis_regin_obj:";
	/**
	 * @Fields KEY_PREFIX_DATA_DICTIONARY : TODO 当前用户角色前置key
	 */
	public final static String KEY_PREFIX_USER_ROLES = "redis_user_roles:";

	/**
	 * @Fields KEY_PREFIX_DATA_DICTIONARY : TODO 当前用户权限的前置key
	 */
	public final static String KEY_PREFIX_USER_PERMISSIONS = "redis_usde_permissions:";
	public final static String KEY_PREFIX_SPU_NAME = "redis_latestname:";
	/**
	 * @Fields KEY_PREFIX_ATTACHMENT : TODO 附件缓存前置key
	 */
	public final static String KEY_PREFIX_ATTACHMENT = "redis_attachment_:";
	//public final static String KEY_PREFIX_ATTACHMENT_SIZE = "redis_attachment_size_:";

	/**
	 * @Fields LOGIN_AUTH_CODE : TODO 当前用户登录验证码
	 */
	public final static String LOGIN_AUTH_CODE = "login_auth_code";

	/**
	 * @Fields EXPORT_DATA_LIMIT : TODO 批量导出数据每页数据
	 */
	public static final Integer EXPORT_DATA_LIMIT = 1000;

	public static final Integer EXPORT_DATA_LIMIT_2 = 2000;

	public static final Integer EXPORT_DATA_LIMIT_3 = 3000;

	public static final Integer EXPORT_DATA_LIMIT_5 = 5000;

	/************************* redis过期时间start ***************************/
	public static final Integer REDIS_EXPORT_DATA_ = 300;
	/************************* redis过期时间end ***************************/
	/**
	 * 美年接口推送成功与失败的状态码
	 */
	public static final String SEND_DATA_SUCCESS = "S";
	public static final String SEND_DATA_FAIL = "E";

	// 语音文件处理状态，0-未处理
	public static final Integer NO_TRANSFORMATION = 0;
	// 语音文件处理状态，1-处理中
	public static final Integer WAIT_TRANSFORMATION = 1;
	// 语音文件处理状态，2-处理完成
	public static final Integer DONE_TRANSFORMATION = 2;

	public static final Integer TYPE_0 = 0;

	public static final Integer TYPE_1 = 1;
	public static final String ERP_DOMAIN = "vedeng.com";
	public static final String TAOBAO="支付宝（中国）网络技术有限公司";
	public static final String WEIXIN="财付通支付科技有限公司";
	public static final String WLOG = "Wlog_Id";
	public static final String WAREHOUSE_GOODS_OPERATE_LOG_ID ="warehouse_goods_operate_log_id";
	public static final String BARCODE ="barcode_Id";
	public static final String RETURN_BARCODE ="Return_Barcode";
	public static final String METHODLOCK_REDISKEY="MethodLock_Rediskey";
	public static final String BDEXPRESSKEY = "BDEXPRESSKEY ORDERID:";


	/**
	 * erp客户资质资质
	 */
	public static final String GET_FINANCE_AND_APTITUDE_URL="./trader/customer/baseinfo.do?traderId=";
	/**
	 * 报价单关联订单审核页
	 */
	public static final String QUOTE_LINK_BD_URL="./order/quote/checkQuoteLinkBdPage.do?quoteLinkBdLogId=";
	/**
	 * sku详情页
	 */
	public static final String SKU_VIEW_URL="./goods/vgoods/viewSku.do?skuId=%d&spuId=%d&&pageType=0";
	/**
	 * erp客户资质页面
	 */
	public static final String GET_APTITUDE_URL="./trader/customer/getFinanceAndAptitude.do?traderId=%d&traderCustomerId=%d";


	/**
	 * erp供应商信息url
	 */
	public static final String SUPPLIER_BASE_URL="./trader/supplier/baseinfo.do?traderId=";
	/**
	*售后主体类型为销售单
	* @Author:strange
	* @Date:19:10 2019-12-05
	*/
	public static final Integer AFTER_SALEORDER_TYPE=535;
	/**
	*销售订单换货
	* @Author:strange
	* @Date:20:03 2019-12-05
	*/
	public static final Integer AFTER_SALEORDERRETURN_TYPE=540;


	/**
	 * 查询OP分类下的产品数量信息
	 */
	public static final String GET_GOODSNUM_URL="operationclassify/getGoodsNumByCategoryId?erpcategoryId=";

	/**
	 * 向OP系统中推送三级分类信息
	 */
	public static final String SAVE_CATEGORY_URL="operationclassify/pushupdateCategory?erpcategoryId=";
	public static final String SAVE_CATEGORY_URL_NEW="operationclassify/pushupdateCategory";

	/**
	 * 贝登前台认证新增资质
	 */
	public static final String BD_CETIFICATE_ADD_URL="webaccount/certificate/add.htm";
	/**
	 * 医械购新增客户接口
	 */
	public static final String YXG_ADD_TRADER_URL="trader/addYxgTraderByName.htm";

	/**
	 * 贝登前台认证更新资质
	 */
	public static final String BD_CETIFICATE_UPDATE_URL="webaccount/certificate/update.htm";

	/**
	 * 集采用户基本信息查询接口地址
	 */
	public static final String JC_ACCOUNT_INFO_URL = "/jc/api/account/detail";

	/**
	 * 集采签名获取
	 */
	public static final String JC_NOLOGIN_GENERATESIGN_URL = "/jc/noLogin/generateSign";

	/**
	 * 订单详情跳转URL
	 */
	public static final String SALEORDER_DETIAL_URL="./order/saleorder/view.do?saleorderId=";
	public static final String HC_SALEORDER_DETIAL_URL="./order/hc/hcOrderDetailsPage.do?saleorderId=";
	public static final String JC_SALEORDER_DETIAL_URL="./order/jc/view.do?saleorderId=";



	//小医院订单type
	public static final Integer EL_ORDER_TYPE = 6;

	public static final  Integer PRINT_ORDER = 101;//出库单
	public static final  String PRINT_OUT_TYPE_F = "0";//出库单
	public static final  String  PRINT_EXPIRATIONDATE_TYPE_F= "1";//带效期出库单
	public static final  String PRINT_HC_TYPE_F = "2";//医械购出库单
	public static final  String PRINT_EXPRESS_HC_TYPE_F = "3";//医械购出库单(物流模块)
	public static final  String PRINT_KYG_TYPE_F = "4";//科研购出库单
	public static final  String PRINT_PRICE_TYPE_F = "5";//带价格出库单
	public static final  String PRINT_NOPRICE_TYPE_F = "6";//不带价格出库单
	public static final  String PRINT_EXPRESS_KYG_TYPE_F = "7";//科研购出库单(物流模块)
	public static final  String PRINT_EXPRESS_PRICE_TYPE_F = "8";//带价格出库单(物流模块)
	public static final  String PRINT_EXPRESS_NOPRICE_TYPE_F = "9";//不带价格出库单
	public static final  String PRINT_FLOWERORDER_TYPE_F = "10";//不带价格出库单
	public static final  String PRINT_PRICE_JC_TYPE_F = "11";//非公集采带价格出库单
	public static final  String PRINT_JC_TYPE_F = "12";//非公集采不带价格出库单
	public static final  String	PRINT_STANDARD_TYPE_F = "15";//标准出库单
	public static final  String	PRINT_EXPRESS_STANDARD_TYPE_F = "16";//物流标准出库单

	public static final  String PRINT_EXPRESS_PRICE_JC_TYPE_F = "13";//集采带价格出库单(物流模块)
	public static final  String PRINT_EXPRESS_JC_TYPE_F = "14";//集采不带价格出库单(物流模块)


	public static final  Integer KYG_PRINT_ORDERTYPE = 4;//科研购出库单
	public static final  Integer PRICE_PRINT_ORDERTYPE = 5;//带价格出库单
	public static final  Integer NOPRICE_PRINT_ORDERTYPE = 6;//不带价格出库单

	//重新定义一份
	//医械购出库单
	public static final  Integer PRINT_OUT_TYPE_YXG = 2;
	//科研购出库单
	public static final  Integer PRINT_OUT_TYPE_KYG = 3;
	//贝登出库单
	public static final  Integer PRINT_OUT_TYPE_BD = 4;
	//集采出库单
	public static final  Integer PRINT_OUT_TYPE_JC = 5;


	//采购类型
	public static final Integer BUY_ORDER_TYPE=517;

	//售后类型
	public static final Integer AFTERSALES_ORDER_TYPE=518;

    //资质类型（分销）
	public static final Integer CUSTOME_RNATURE=465;

	public static final Integer DAY_PERIOD_THIRTY=30;

	//资质类型（终端）
	public static final Integer CUSTOME_INTERMIAL=466;

	//客户类型 426科研医疗
	public static final Integer CUSTOME_TYPE_RESEARCH_MEDICAL=426;

	//ERP简单密码
	public static final String SIMPLE_PASSWORD="123456";
    public static final String YXG_ORG_NAME = "医械购";

    public static final String VIRTUAL_EXPRESS_NO= "虚拟快递单";

    public static final String QUALITY_ORG = "质量管理部";

    public static final String B2B_BUSINESS_UNIT = "营销中心";

    public static final String APPLY_VALID_ERROR_MESSAGE = "该订单已处于审核流程，无法再次提交";
	public static Integer bc_source_bd_pc = 4059;
	public static Integer bc_source_bd_app = 4061;
	public static Integer bc_source_bd_m = 4060;

	/**
	 *  状态
	 */
	public interface STATUS_STATE{
		Integer NO = 0;
		Integer SOME = 1;
		Integer YES = 2;
	}
	/**
	 *  审核状态 0待提交审核 1审核中 3审核通过 2审核不通过
	 */
	public interface CHECK_STATUS_STATE{
		Integer PRE = 0;
		Integer CHECKING = 1;
		Integer APPROVED = 3;
		Integer REJECT = 2;
	}
	/**
	 * 删除状态
	 */
	public interface DELETE_STATE{
		/**
		 * 未删除
		 */
    	Integer IS_NOT_DELETE = 0;
		/**
		 * 已删除
		 */
		Integer IS_DELETE = 1;
	}

	/**
	 * 发货通知详情页面按钮操作类型
	 */
	public interface OPERATION_TYPE{
		/**
		 * 申请审核
		 */
		Integer  APPLICATION_REVIEW=1;
		/**
		 * 关闭发货通知
		 */
		Integer CLOSE_DELIVERY_NOTICE=2;
		/**
		 * 申请通过
		 */
		Integer APPLICATION_PASSED=3;
		/**
		 * 申请不通过
		 */
		Integer APPLICATION_NOT_PASSED=4;
	}

	/**
	 * 发货通知单的状态
	 */
	public interface DELIVERY_NOTICE_STATUS{
		/**
		 * 待确认
		 */
		Integer DQR=0;
		/**
		 * 进行中
		 */
		Integer JXZ=1;
		/**
		 * 已完结
		 */
		Integer YWJ=2;
		/**
		 * 已关闭
		 */
		Integer YGB=3;
	}

	/**
	 * 发货通知单的审核状态
	 */
	public interface DELIVERY_NOTICE_AUDIT_STATUS{
		/**
		 * 待审核
		 */
		Integer DHS=0;
		/**
		 * 审核中
		 */
		Integer SHZ=1;
		/**
		 * 审核通过
		 */
		Integer SHTG=2;
		/**
		 * 审核不通过
		 */
		Integer SHBTG=3;
	}
	/**
	 * 发货通知单的单号起止  001-->999
	 */
	public interface DELIVERY_NOTICE_NO{
		/**
		 * 发货通知单的单号 start 001
		 */
		String START="001";

		/**
		 * 发货通知单的单号 end 999
		 */
		String END="999";
	}

	/**
	 * 商品资质文件类型
	 */
	public interface  QLA_ATTACHMENT_TYPE{
		String CPZCZ="产品注册证";
		String CPZCZ_DZ="产品注册证(贝)";
		String SCQYYYZZ="生产企业营业执照";
		String SCQYYYZZ_DZ="生产企业营业执照(贝)";
		String YLQXSCXKZ="医疗器械生产许可证";
		String YLQXSCXKZ_DZ="医疗器械生产许可证(贝)";
		String SCQYSCDJB="生产企业生产产品登记表";
		String SCQYSCDJB_DZ="生产企业生产产品登记表(贝)";
	}

	/**
	 * 对于下传Wms商品资料时 如果spu_type为空的则默认传SPU_TYPE_QT 其他类型
	 */
	public static final Integer SPU_TYPE_QT=999;


	/**
	 * Constants for symbol
	 */
	public static final class Symbol{

		public final static String COMMA = ",";

		public final static String COMMA_IN_CN = "、";

		public final static String PERIOD = ".";

		public final static String SLASH = "/";

		public final static String HYPHEN= "-";

		public static final String SPACE = " ";

		public static final String COLON = ":";
	}

	/**
	 * 采购开票
	 */
	public static final Integer BUY_ORDER_INVOICE = 503;

	public static final String HTTP = "http://";

	/**
	 * 出库单信息 附件类型
	 */
	public static final Integer OUTBOUND_ORDER_TYPE = 2004;

	/**
	 * 出库单信息 应用类型
	 */
	public static final Integer OUTBOUND_ORDER_FUNCTION = 2005;

	/**
	 * 出库单信息 带价格
	 */
	public static final Integer OUTBOUND_ORDER_FUNCTION_PRICE = 4105;

	/**
	 * 出库单信息 不带价格
	 */
	public static final Integer OUTBOUND_ORDER_FUNCTION_NO_PRICE = 4106;

	public static final int NJ_ROOT_ORG = 2;

	public static final Integer SIXTY_FOUR = 64;

	public static final Integer SPECIALGOODS_SYS_PARENT_ID = 693;

	public static final class OperateType {

		public static final Integer INSERT_OPERATE = 0;
		public static final Integer DELETE_OPERATE = 1;
		public static final Integer UPDATE_OPERATE_FRONT = 2;
		public static final Integer UPDATE_OPERATE_END = 3;
		public static final Integer SELECT_OPERATE = 4;
	}

	public static final  Integer CUSTOMER_VERIFY_TYPE = 617;

	public static final  Integer SUPPLYER_VERIFY_TYPE = 619;

	/**
	 * 修改订单审核
	 */
	public static final  Integer ORDER_MODIFY_TYPE = 634;

	/**
	 * 机构等级
	 */
	public static final Integer MECHANISM_LEVEL = 3700;

	/**
	 * 根据父级id缓存所有子集部门rediskey
	 */
	public static String ORG_PARENT_ALL_IDS = "ORG_PARENT_ALL_IDS";

	/**
	 * 沟通记录用户键
	 */
	public static String RECORD_USER_KEY = "RECORD_USER_KEY";

	public static String CHILDREN_OF_PARENT_USER = "CHILDREN_OF_PARENT_USER";
	public static String ALL_BRAND = "ALL_BRAND";
	public static String ALL_MANUFACTURER= "ALL_MANUFACTURER";

	public static final  Integer ERROR_CODE = -1;

	/**
	 * 出入库验收单类型
	 */
	public static final Integer WAREHOUSER_ATTACHMET_TYPE = 462;

	/**
	 * 入库验收单FUNCTION
	 */
	public static final Integer WAREHOUSER_ATTACHMET_FUNCTION = 4211;


	/**
	 * 在线确认附件
	 */
	public static final Integer ONLINE_EXPRESS_ATTACHMET_TYPE = 980;
	/**
	 * 在线确认附件
	 */
	public static final Integer ONLINE_EXPRESS_ATTACHMET_FUNCTION = 1021;

	/**
	 * 出库验收单FUNCTION
	 */
	public static final Integer WAREHOUSE_ATTACHMET_OUT_FUNCTION = 4213;

	/**
	 * 样品出库单
	 */
	public static final Integer SAMPLE_ORDER_TYPE = 4214;

	/**
	 * 样品出库附件
	 */
	public static final Integer SAMPLE_ORDER_FUNCTION = 4215;


	public static final String BOSS = "david.ding";

	/**
	 * 采购合同回传审核字典id
	 */
	public static final Integer SYS_OPT_PURCHASE_CONTRACT = 4461;

	/**
	 * 采购合同虚拟审核表
	 */
	public static final String PURCHASE_CONTRACT_VERIFY = "T_BUYORDER_CONTRACT";

	/**
	 * 税收编码操作记录模版
	 */
	public static final String TAX_CATEGORY_NO_RECORD_TEMPLATE = "修改人:{},修改时间:{},原值:{}";
}
