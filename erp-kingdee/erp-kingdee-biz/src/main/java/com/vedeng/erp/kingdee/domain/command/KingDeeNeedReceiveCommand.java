package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶实际接受参数类
 */
@Getter
@Setter
public class KingDeeNeedReceiveCommand {

    /**
     * 单据内码
     */
    private String FID;
    /**
     * 单据号
     */
    private String FBillNo;
    /**
     * 单据日期 2022-10-10
     */
    private String F_VPFN_Date;
    /**
     * 组织代码
     */
    private KingDeeNumberCommand F_VPFN_JG = new KingDeeNumberCommand();
    /**
     * 供应商
     */
    private KingDeeNumberCommand F_VPFN_KH = new KingDeeNumberCommand();
    /**
     * fEntity
     */
    private List<KingDeeNeedReceiveEntityCommand> FEntity = new ArrayList<>();

    @Getter
    @Setter
    public static class KingDeeNeedReceiveEntityCommand {
        /**
         * 原始订单号
         */
        private String F_VPFN_YSDDH;
        /**
         * 归属业务单号
         */
        private String F_VPFN_GSYWDH;
        /**
         * 业务类型
         */
        private String F_VPFN_YWLX;
        /**
         * 调整金额
         */
        private BigDecimal F_VPFN_TZJE;
    }


}
