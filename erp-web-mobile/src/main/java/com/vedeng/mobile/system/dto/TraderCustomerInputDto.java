package com.vedeng.mobile.system.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/4/8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TraderCustomerInputDto {

    private Integer userId;//当前的用户ID

    private Integer traderId;//交易者ID

    private Integer traderCustomerId;//客户ID

    private String traderName;//客户名称

    private Integer customerFrom;//1erp2终端库3天眼查

    /**
     * 客户所在地区-省CODE
     */
    private Integer provinceCode;

    /**
     * 客户所在地区-省名称
     */
    private String provinceName;

    /**
     * 客户所在地区-市CODE
     */
    private Integer cityCode;

    /**
     * 客户所在地区-市名称
     */
    private String cityName;

    /**
     * 客户所在地区-区CODE-20240407 ERP_SV_2024_17版本 拜访计划优化需求新增
     */
    private Integer areaCode;

    /**
     * 客户所在地区-区名称-20240407 ERP_SV_2024_17版本 拜访计划优化需求新增
     */
    private String areaName;

    private Integer customerNature;//客户类型 (ERP客户时自动带入且不能修改，其他必填。465分销466终端)

    private Integer tzCustomer;//是否同舟会员 0 否 1 是

    private Integer tzCustomerLevel;//同舟会员等级 0无等级，不展示 1 金牌 2 银牌






}
