package com.vedeng.erp.buyorder.task.buyorder.statusSync;

import com.newtask.data.AbstractDataByTimeSync;
import com.newtask.data.dao.BuyorderDataMapper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description com.newtask.data.buyorder.statusSync
 * @Date 2021/10/19 15:29
 */
@Component
public class BuyorderIsContractReturnStatusSync extends AbstractDataByTimeSync {

    @Resource
    BuyorderDataMapper buyorderDataMapper;

    @Override
    public List<Map<String, Object>> loadBizByTimeData(Long startTime, Long endTime) {
        return buyorderDataMapper.getOrderIsContractByTime(startTime, endTime);
    }


    @Override
    public void updateData(List<Map<String, Object>> dataList) {
        if (CollectionUtils.isNotEmpty(dataList)) {
            List<Integer> orderIds = new ArrayList<>();
            long nowTime = System.currentTimeMillis();
            dataList.forEach(item -> {
                Integer buyorderId = Integer.valueOf(item.get("buyorderId").toString());
                makeExist(buyorderId, nowTime);
                orderIds.add(buyorderId);
            });
            buyorderDataMapper.updateOrderIsContractByTime(orderIds, nowTime);
        }
    }

    @Override
    public void makeExist(Integer id, Long nowTime) {
        int buyorderDataId = buyorderDataMapper.getBuyorderDataById(id);
        if (buyorderDataId <= 0) {
            buyorderDataMapper.insertBuyorderData(id, nowTime);
        }
    }


    /**
     * @desc 根据销售单id同步销售单信息
     * <AUTHOR>
     * @param saleorderId
     */
    public void process(Integer saleorderId){
        List<Integer> list=new ArrayList<>();
        list.add(saleorderId);
        List<Map<String,Object>> map=loadBizData(list);
        updateData(map);
    }


    public List<Map<String, Object>> loadBizData(List<Integer> bizIds) {
        List<Map<String, Object>> verifyStatus = null;

        if (CollectionUtils.isNotEmpty(bizIds)) {
            verifyStatus = buyorderDataMapper.getOrderIsContractByBuyorderId(bizIds).stream()
                    .map(bizId -> {
                        Map<String, Object> item = new HashMap<>(1);
                        item.put("buyorderId",bizId);
                        return item;
                    })
                    .collect(Collectors.toList());
        }

        return verifyStatus;
    }
}
