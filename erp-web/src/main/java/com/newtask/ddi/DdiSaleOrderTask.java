package com.newtask.ddi;

import com.alibaba.fastjson.JSON;
import com.newtask.ddi.dao.DdiGeOrderMapper;
import com.newtask.ddi.model.DdiGeOrder;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.common.util.StringUtil;
import com.vedeng.order.model.GeSaleOrder;
import com.wms.dao.WmsOutputSnCodeMapper;
import com.wms.model.WmsOutSnCodeOrder;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: DDI销售订单采集任务
 * @date 2020/11/18 19:21
 */
@Component
@JobHandler(value = "OutputGeOrderTask")
public class DdiSaleOrderTask extends AbstractJobHandler {

    private static final Logger LOGGER= LoggerFactory.getLogger(DdiSaleOrderTask.class);

    @Autowired
    private DdiGeOrderMapper ddiGeOrderMapper;

    @Autowired
    private WmsOutputSnCodeMapper wmsOutputSnCodeMapper;

    @Value("${GE_TRADER_SKU}")
    protected String geTraderSku;


    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        GeSaleOrder geSaleOrder = JsonUtils.readValue(geTraderSku, GeSaleOrder.class);
        List<String> skuList = geSaleOrder.getSkuList();
        List<WmsOutSnCodeOrder> orders = null;
        Timestamp startTime = null;
        Timestamp endTime = null;
        if (StringUtil.isNotBlank(param)){
            // 获取之前的订单中GE商品
            String[] params = param.split(",");
            startTime = DateUtil.getDayStartTime(DateUtil.StringToDate(params[0]));
            endTime = DateUtil.getDayEndTime(DateUtil.StringToDate(params[1]));
        }else {
            Date yesterdayDate = DateUtil.getPreviousDayByDateTime(new Date());
            startTime = DateUtil.getDayStartTime(yesterdayDate);
            endTime = DateUtil.getDayEndTime(yesterdayDate);
        }
        orders = wmsOutputSnCodeMapper.getSnCodeOrderByTime(skuList, startTime, endTime);
        LOGGER.info("DDI销售订单采集任务开始:" + JSON.toJSONString(orders));
        XxlJobLogger.log("DDI销售订单采集任务开始:" + JSON.toJSONString(orders));
        if (CollectionUtils.isNotEmpty(orders)){
            for (WmsOutSnCodeOrder order : orders) {
                DdiGeOrder geOrder = ddiGeOrderMapper.getGeOrders(order);
                if (null != geOrder){
                    // GE产品销售数量不用去订单里面取，取1
                    geOrder.setNum(1);
                    ddiGeOrderMapper.insertSelective(geOrder);
                }
            }
        }
        LOGGER.info("DDI销售订单采集任务完成:" + JSON.toJSONString(orders));
        XxlJobLogger.log("DDI销售订单采集任务完成:" + JSON.toJSONString(orders));
        return SUCCESS;
    }

}
