package com.vedeng.erp.kingdee.batch.repository;
import com.vedeng.erp.kingdee.batch.dto.BatchSaleAndBuyOrderGoodsDto;
import org.apache.ibatis.annotations.Param;

import com.vedeng.erp.kingdee.batch.dto.BatchBuyorderDto;

import java.util.List;

/**
 * <AUTHOR>
 * @description ${end}
 * @date 2022/11/24 13:35
 **/
public interface BatchBuyorderDtoMapper {

    BatchBuyorderDto selectByPrimaryKey(Integer buyorderId);

    BatchBuyorderDto selectByBuyorderNo(@Param("buyorderNo")String buyorderNo);


    List<BatchSaleAndBuyOrderGoodsDto> getSaleAndBuyOrderGoodsByIds(@Param("list") List<Integer> buyOrderGoodsIds);
}