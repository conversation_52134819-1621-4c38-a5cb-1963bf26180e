package com.vedeng.erp.kingdee.batch.repository;


import com.vedeng.erp.kingdee.batch.dto.BatchRollbackInvoiceDto;

import java.util.List;
import java.util.Set;

public interface BatchRollbackInvoiceDtoMapper {


    List<BatchRollbackInvoiceDto> findByAll(BatchRollbackInvoiceDto batchRollbackInvoiceDto);

    List<BatchRollbackInvoiceDto> findVirtualInvoiceDisable(BatchRollbackInvoiceDto batchRollbackInvoiceDto);

    String getExpensePlainInvoiceFid(String invoiceIdStr);

    String getExpenseSpecialInvoiceFid(String invoiceIdStr);

    String getPayCommonIdByInvoiceFid(String invoiceIdStr);

    String getExpensePayCommonIdByInvoiceFid(String invoiceIdStr);

    String getBuyOrderPlainInvoiceFid(String invoiceIdStr);

    String getBuyOrderSpecialInvoiceFid(String invoiceIdStr);

    /**
     * 查询销售蓝字作废发票关联的蓝字有效票
     * @param batchRollbackInvoiceDto
     * @return
     */
    List<BatchRollbackInvoiceDto> findAfterSaleDeprecateBlueInvoice(BatchRollbackInvoiceDto batchRollbackInvoiceDto);

    String getSaleOrderSpecialInvoiceFid(String invoiceIdStr);

    String getSaleOrderPlainInvoiceFid(String invoiceIdStr);

    String getReceiveCommonIdByInvoiceIdStr(String invoiceIdStr);

    String getReceiveFeeIdByInvoiceIdStr(String invoiceIdStr);

    Set<Integer> getIsVirtualGoodSetByInvoiceId(Integer invoiceId);

    String getSaleOrderFeeSpecialInvoiceFid(String invoiceIdStr);

    String getSaleOrderFeePlainInvoiceFid(String invoiceIdStr);
}