<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="售后服务公司详情" scope="application" />
<%@ include file="../../common/common.jsp"%>
<div class="main-container">
	<div class="parts">
		<div class="title-container">
			<div class="table-title nobor">基本详情</div>
		</div>
		<table class="table">
			<tbody>
			<tr>
				<td class="wid20">公司名称：</td>
				<td>${engineer.name }</td>
				<td class="wid20">开户银行:</td>
				<td>${engineer.bank }</td>
			</tr>
			<tr>
				<td>开户账号：</td>
				<td>${engineer.bankAccount }</td>
				<td>开户支行联行号：</td>
				<td>${engineer.bankCode }</td>
			</tr>
			</tbody>
		</table>
	</div>

	<div class="parts">
		<div class="title-container">
			<div class="table-title nobor">服务历史</div>
		</div>
		<table class="table">
			<thead>
			<tr>
				<th class="wid5">序号</th>
				<th>售后单号</th>
				<th>订单状态</th>
				<th>服务时间</th>
				<th>酬金</th>
				<th>服务评分</th>
				<th>技能评分</th>
				<th>评分备注</th>
			</tr>
			</thead>
			<tbody>
			<c:choose>
				<c:when test="${not empty engineer.afterSalesInstallstions }">
					<c:forEach items="${engineer.afterSalesInstallstions }" var="afterSalesInstallstion" varStatus="status">
						<tr>
							<td>${status.count }</td>
							<td>${afterSalesInstallstion.afterSalesNo }</td>
							<td>
								<c:choose>
									<c:when test="${afterSalesInstallstion.atferSalesStatus == 0 }">
										待确认
									</c:when>
									<c:when test="${afterSalesInstallstion.atferSalesStatus == 1 }">
										进行中
									</c:when>
									<c:when test="${afterSalesInstallstion.atferSalesStatus == 2 }">
										已完结
									</c:when>
									<c:when test="${afterSalesInstallstion.atferSalesStatus == 3 }">
										已关闭
									</c:when>
								</c:choose>
							</td>
							<td><date:date value ="${afterSalesInstallstion.serviceTime} "/></td>
							<td>${afterSalesInstallstion.engineerAmount }</td>
							<td>
								<c:if test="${afterSalesInstallstion.serviceScore > 0}">${afterSalesInstallstion.serviceScore }</c:if>
							</td>
							<td>
								<c:if test="${afterSalesInstallstion.skillScore > 0}">${afterSalesInstallstion.skillScore }</c:if>
							</td>
							<td>${afterSalesInstallstion.scoreComments }</td>
						</tr>
					</c:forEach>
				</c:when>
				<c:otherwise>
					<tr>
						<td colspan="8">服务历史暂无信息</td>
					</tr>
				</c:otherwise>
			</c:choose>
			</tbody>
		</table>
		<tags:page page="${page}" />
	</div>
</div>
<script type="text/javascript"
	src="${pageContext.request.contextPath}/static/js/aftersales/engineer/view_engineer.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp"%>