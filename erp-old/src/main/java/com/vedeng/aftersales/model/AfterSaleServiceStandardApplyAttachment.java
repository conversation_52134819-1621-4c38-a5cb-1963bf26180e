package com.vedeng.aftersales.model;

public class AfterSaleServiceStandardApplyAttachment {
    private Long serviceStandardApplyAttachmentId;

    private Long serviceStandardApplyId;

    private String domain;

    private String uri;

    private String fileName;

    private Integer creator;

    private Integer updator;

    private String addTime;

    private String modTime;

    public Long getServiceStandardApplyAttachmentId() {
        return serviceStandardApplyAttachmentId;
    }

    public void setServiceStandardApplyAttachmentId(Long serviceStandardApplyAttachmentId) {
        this.serviceStandardApplyAttachmentId = serviceStandardApplyAttachmentId;
    }

    public Long getServiceStandardApplyId() {
        return serviceStandardApplyId;
    }

    public void setServiceStandardApplyId(Long serviceStandardApplyId) {
        this.serviceStandardApplyId = serviceStandardApplyId;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain == null ? null : domain.trim();
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri == null ? null : uri.trim();
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName == null ? null : fileName.trim();
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getAddTime() {
        return addTime;
    }

    public void setAddTime(String addTime) {
        this.addTime = addTime == null ? null : addTime.trim();
    }

    public String getModTime() {
        return modTime;
    }

    public void setModTime(String modTime) {
        this.modTime = modTime == null ? null : modTime.trim();
    }
}