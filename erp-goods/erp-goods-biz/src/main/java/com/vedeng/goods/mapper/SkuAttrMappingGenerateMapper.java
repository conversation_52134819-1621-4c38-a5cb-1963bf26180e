package com.vedeng.goods.mapper;

import com.vedeng.goods.domain.dto.SkuAttrDto;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.goods.mapper
 * @Date 2022/1/11 16:58
 */
@Repository("newSkuAttrMappingGenerateMapper")
public interface SkuAttrMappingGenerateMapper {

     /**
      * 获取SKU属性
      * @param skuNo
      * @return
      */
     List<SkuAttrDto> getGoodsAttribute(String skuNo);

}
