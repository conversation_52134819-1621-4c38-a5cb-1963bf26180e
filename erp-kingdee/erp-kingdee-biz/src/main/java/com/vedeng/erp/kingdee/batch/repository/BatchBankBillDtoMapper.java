package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchBankBillDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BatchBankBillDtoMapper {
    /**
     * 更新流水推送金蝶状态
     * <AUTHOR>
     * @param bankBillId
     */
    void updateKingdeePushedStatus(@Param("bankBillId") Integer bankBillId);

    /**
     * 查询符合条件且未推送金蝶的银行流水
     * @param batchBankBillDto
     * @return
     */
    List<BatchBankBillDto> queryNeedPullBankBill(BatchBankBillDto batchBankBillDto);
    List<BatchBankBillDto> queryReceiveBillFill(BatchBankBillDto batchBankBillDto);
    List<BatchBankBillDto> queryReceiveRefundBillFill(BatchBankBillDto batchBankBillDto);
    List<BatchBankBillDto> queryPayBillFill(BatchBankBillDto batchBankBillDto);
    List<BatchBankBillDto> queryPayRefundBillFill(BatchBankBillDto batchBankBillDto);


    List<BatchBankBillDto> queryNeedPushReceiptBankBillNew(BatchBankBillDto batchBankBillDto);

    List<BatchBankBillDto> queryNeedPullIgnoreBankBill(BatchBankBillDto batchBankBillDto);

    /**
     * 查询符合条件推送付款单
     * @param batchBankBillDto
     * @return
     */
    List<BatchBankBillDto> queryhistoryNeedPullBankBill(BatchBankBillDto batchBankBillDto);

    /**
     * 查询需要推送的已结算付款银行流水
     */
    List<BatchBankBillDto> queryNeedPushPaymentBankBillNew(BatchBankBillDto batchBankBillDto);

    /**
     * 查询需要推送的已结算付款微信与支付宝流水
     */
    List<BatchBankBillDto> queryNeedPushPaymentWeChatAndAlipayNew(BatchBankBillDto batchBankBillDto);

    List<BatchBankBillDto> querypaymentwechatNeedPullBankBill(BatchBankBillDto batchBankBillDto);

    List<BatchBankBillDto> getAliReceiptBillNotPushedToKingDee(BatchBankBillDto batchBankBillDto);
    /**
     * 根据银行流水id查询流水信息
     * <AUTHOR>
     * @param bankBillId
     * @return
     */
    BatchBankBillDto queryInfoById(@Param("bankBillId") Integer bankBillId);

    /**
     * 根据订单号查询支付宝流水关联的手续费
     *
     * @param orderNo 订单号
     * @return 手续费
     */
    BatchBankBillDto getAliFeeBankBillByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 根据CAPITAL_SEARCH_FLOW查询支付宝流水关联的手续费
     */
    BatchBankBillDto getAliFeeBankBillByCapitalSearchFlow(@Param("capitalSearchFlow") String capitalSearchFlow);

    BatchBankBillDto getAliFeeByCapitalSearchFlow(@Param("capitalSearchFlow") String capitalSearchFlow);

    /**
     * 查询微信流水关联的手续费流水
     *
     * @param tranFlow 微信流水号
     * @return 手续费
     */
    BatchBankBillDto getWeChatFeeBankBillByTranFlow(@Param("tranFlow") String tranFlow);

    /**
     * 查询已忽略付款退款单流水
     *
     * @param query BatchBankBillDto
     * @return List<BatchBankBillDto>
     */
    List<BatchBankBillDto> queryIgnoredPayRefundBill(BatchBankBillDto query);

    /**
     * 查询已结算付款退款单流水
     *
     * @param query BatchBankBillDto
     * @return List<BatchBankBillDto>
     */
    List<BatchBankBillDto> querySettledPayRefundBill(BatchBankBillDto query);

    /**
     * 查询已忽略收款退款单流水
     *
     * @param query BatchBankBillDto
     * @return List<BatchBankBillDto>
     */
    List<BatchBankBillDto> queryIgnoredReceiveRefundBill(BatchBankBillDto query);

    /**
     * 查询已结算收款退款单流水
     *
     * @param query BatchBankBillDto
     * @return List<BatchBankBillDto>
     */
    List<BatchBankBillDto> querySettledReceiveRefundBill(BatchBankBillDto query);


    BatchBankBillDto selectByPrimaryKey(Integer bankBillId);
}
