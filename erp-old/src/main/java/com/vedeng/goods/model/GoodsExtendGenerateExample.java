package com.vedeng.goods.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class GoodsExtendGenerateExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public GoodsExtendGenerateExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andGoodsExtendIdIsNull() {
            addCriterion("GOODS_EXTEND_ID is null");
            return (Criteria) this;
        }

        public Criteria andGoodsExtendIdIsNotNull() {
            addCriterion("GOODS_EXTEND_ID is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsExtendIdEqualTo(Integer value) {
            addCriterion("GOODS_EXTEND_ID =", value, "goodsExtendId");
            return (Criteria) this;
        }

        public Criteria andGoodsExtendIdNotEqualTo(Integer value) {
            addCriterion("GOODS_EXTEND_ID <>", value, "goodsExtendId");
            return (Criteria) this;
        }

        public Criteria andGoodsExtendIdGreaterThan(Integer value) {
            addCriterion("GOODS_EXTEND_ID >", value, "goodsExtendId");
            return (Criteria) this;
        }

        public Criteria andGoodsExtendIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("GOODS_EXTEND_ID >=", value, "goodsExtendId");
            return (Criteria) this;
        }

        public Criteria andGoodsExtendIdLessThan(Integer value) {
            addCriterion("GOODS_EXTEND_ID <", value, "goodsExtendId");
            return (Criteria) this;
        }

        public Criteria andGoodsExtendIdLessThanOrEqualTo(Integer value) {
            addCriterion("GOODS_EXTEND_ID <=", value, "goodsExtendId");
            return (Criteria) this;
        }

        public Criteria andGoodsExtendIdIn(List<Integer> values) {
            addCriterion("GOODS_EXTEND_ID in", values, "goodsExtendId");
            return (Criteria) this;
        }

        public Criteria andGoodsExtendIdNotIn(List<Integer> values) {
            addCriterion("GOODS_EXTEND_ID not in", values, "goodsExtendId");
            return (Criteria) this;
        }

        public Criteria andGoodsExtendIdBetween(Integer value1, Integer value2) {
            addCriterion("GOODS_EXTEND_ID between", value1, value2, "goodsExtendId");
            return (Criteria) this;
        }

        public Criteria andGoodsExtendIdNotBetween(Integer value1, Integer value2) {
            addCriterion("GOODS_EXTEND_ID not between", value1, value2, "goodsExtendId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdIsNull() {
            addCriterion("GOODS_ID is null");
            return (Criteria) this;
        }

        public Criteria andGoodsIdIsNotNull() {
            addCriterion("GOODS_ID is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsIdEqualTo(Integer value) {
            addCriterion("GOODS_ID =", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdNotEqualTo(Integer value) {
            addCriterion("GOODS_ID <>", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdGreaterThan(Integer value) {
            addCriterion("GOODS_ID >", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("GOODS_ID >=", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdLessThan(Integer value) {
            addCriterion("GOODS_ID <", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdLessThanOrEqualTo(Integer value) {
            addCriterion("GOODS_ID <=", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdIn(List<Integer> values) {
            addCriterion("GOODS_ID in", values, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdNotIn(List<Integer> values) {
            addCriterion("GOODS_ID not in", values, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdBetween(Integer value1, Integer value2) {
            addCriterion("GOODS_ID between", value1, value2, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdNotBetween(Integer value1, Integer value2) {
            addCriterion("GOODS_ID not between", value1, value2, "goodsId");
            return (Criteria) this;
        }

        public Criteria andCustomerNamesIsNull() {
            addCriterion("CUSTOMER_NAMES is null");
            return (Criteria) this;
        }

        public Criteria andCustomerNamesIsNotNull() {
            addCriterion("CUSTOMER_NAMES is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerNamesEqualTo(String value) {
            addCriterion("CUSTOMER_NAMES =", value, "customerNames");
            return (Criteria) this;
        }

        public Criteria andCustomerNamesNotEqualTo(String value) {
            addCriterion("CUSTOMER_NAMES <>", value, "customerNames");
            return (Criteria) this;
        }

        public Criteria andCustomerNamesGreaterThan(String value) {
            addCriterion("CUSTOMER_NAMES >", value, "customerNames");
            return (Criteria) this;
        }

        public Criteria andCustomerNamesGreaterThanOrEqualTo(String value) {
            addCriterion("CUSTOMER_NAMES >=", value, "customerNames");
            return (Criteria) this;
        }

        public Criteria andCustomerNamesLessThan(String value) {
            addCriterion("CUSTOMER_NAMES <", value, "customerNames");
            return (Criteria) this;
        }

        public Criteria andCustomerNamesLessThanOrEqualTo(String value) {
            addCriterion("CUSTOMER_NAMES <=", value, "customerNames");
            return (Criteria) this;
        }

        public Criteria andCustomerNamesLike(String value) {
            addCriterion("CUSTOMER_NAMES like", value, "customerNames");
            return (Criteria) this;
        }

        public Criteria andCustomerNamesNotLike(String value) {
            addCriterion("CUSTOMER_NAMES not like", value, "customerNames");
            return (Criteria) this;
        }

        public Criteria andCustomerNamesIn(List<String> values) {
            addCriterion("CUSTOMER_NAMES in", values, "customerNames");
            return (Criteria) this;
        }

        public Criteria andCustomerNamesNotIn(List<String> values) {
            addCriterion("CUSTOMER_NAMES not in", values, "customerNames");
            return (Criteria) this;
        }

        public Criteria andCustomerNamesBetween(String value1, String value2) {
            addCriterion("CUSTOMER_NAMES between", value1, value2, "customerNames");
            return (Criteria) this;
        }

        public Criteria andCustomerNamesNotBetween(String value1, String value2) {
            addCriterion("CUSTOMER_NAMES not between", value1, value2, "customerNames");
            return (Criteria) this;
        }

        public Criteria andSellingWordsIsNull() {
            addCriterion("SELLING_WORDS is null");
            return (Criteria) this;
        }

        public Criteria andSellingWordsIsNotNull() {
            addCriterion("SELLING_WORDS is not null");
            return (Criteria) this;
        }

        public Criteria andSellingWordsEqualTo(String value) {
            addCriterion("SELLING_WORDS =", value, "sellingWords");
            return (Criteria) this;
        }

        public Criteria andSellingWordsNotEqualTo(String value) {
            addCriterion("SELLING_WORDS <>", value, "sellingWords");
            return (Criteria) this;
        }

        public Criteria andSellingWordsGreaterThan(String value) {
            addCriterion("SELLING_WORDS >", value, "sellingWords");
            return (Criteria) this;
        }

        public Criteria andSellingWordsGreaterThanOrEqualTo(String value) {
            addCriterion("SELLING_WORDS >=", value, "sellingWords");
            return (Criteria) this;
        }

        public Criteria andSellingWordsLessThan(String value) {
            addCriterion("SELLING_WORDS <", value, "sellingWords");
            return (Criteria) this;
        }

        public Criteria andSellingWordsLessThanOrEqualTo(String value) {
            addCriterion("SELLING_WORDS <=", value, "sellingWords");
            return (Criteria) this;
        }

        public Criteria andSellingWordsLike(String value) {
            addCriterion("SELLING_WORDS like", value, "sellingWords");
            return (Criteria) this;
        }

        public Criteria andSellingWordsNotLike(String value) {
            addCriterion("SELLING_WORDS not like", value, "sellingWords");
            return (Criteria) this;
        }

        public Criteria andSellingWordsIn(List<String> values) {
            addCriterion("SELLING_WORDS in", values, "sellingWords");
            return (Criteria) this;
        }

        public Criteria andSellingWordsNotIn(List<String> values) {
            addCriterion("SELLING_WORDS not in", values, "sellingWords");
            return (Criteria) this;
        }

        public Criteria andSellingWordsBetween(String value1, String value2) {
            addCriterion("SELLING_WORDS between", value1, value2, "sellingWords");
            return (Criteria) this;
        }

        public Criteria andSellingWordsNotBetween(String value1, String value2) {
            addCriterion("SELLING_WORDS not between", value1, value2, "sellingWords");
            return (Criteria) this;
        }

        public Criteria andMarketStrategyIsNull() {
            addCriterion("MARKET_STRATEGY is null");
            return (Criteria) this;
        }

        public Criteria andMarketStrategyIsNotNull() {
            addCriterion("MARKET_STRATEGY is not null");
            return (Criteria) this;
        }

        public Criteria andMarketStrategyEqualTo(String value) {
            addCriterion("MARKET_STRATEGY =", value, "marketStrategy");
            return (Criteria) this;
        }

        public Criteria andMarketStrategyNotEqualTo(String value) {
            addCriterion("MARKET_STRATEGY <>", value, "marketStrategy");
            return (Criteria) this;
        }

        public Criteria andMarketStrategyGreaterThan(String value) {
            addCriterion("MARKET_STRATEGY >", value, "marketStrategy");
            return (Criteria) this;
        }

        public Criteria andMarketStrategyGreaterThanOrEqualTo(String value) {
            addCriterion("MARKET_STRATEGY >=", value, "marketStrategy");
            return (Criteria) this;
        }

        public Criteria andMarketStrategyLessThan(String value) {
            addCriterion("MARKET_STRATEGY <", value, "marketStrategy");
            return (Criteria) this;
        }

        public Criteria andMarketStrategyLessThanOrEqualTo(String value) {
            addCriterion("MARKET_STRATEGY <=", value, "marketStrategy");
            return (Criteria) this;
        }

        public Criteria andMarketStrategyLike(String value) {
            addCriterion("MARKET_STRATEGY like", value, "marketStrategy");
            return (Criteria) this;
        }

        public Criteria andMarketStrategyNotLike(String value) {
            addCriterion("MARKET_STRATEGY not like", value, "marketStrategy");
            return (Criteria) this;
        }

        public Criteria andMarketStrategyIn(List<String> values) {
            addCriterion("MARKET_STRATEGY in", values, "marketStrategy");
            return (Criteria) this;
        }

        public Criteria andMarketStrategyNotIn(List<String> values) {
            addCriterion("MARKET_STRATEGY not in", values, "marketStrategy");
            return (Criteria) this;
        }

        public Criteria andMarketStrategyBetween(String value1, String value2) {
            addCriterion("MARKET_STRATEGY between", value1, value2, "marketStrategy");
            return (Criteria) this;
        }

        public Criteria andMarketStrategyNotBetween(String value1, String value2) {
            addCriterion("MARKET_STRATEGY not between", value1, value2, "marketStrategy");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyIsNull() {
            addCriterion("PROMOTION_POLICY is null");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyIsNotNull() {
            addCriterion("PROMOTION_POLICY is not null");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyEqualTo(String value) {
            addCriterion("PROMOTION_POLICY =", value, "promotionPolicy");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyNotEqualTo(String value) {
            addCriterion("PROMOTION_POLICY <>", value, "promotionPolicy");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyGreaterThan(String value) {
            addCriterion("PROMOTION_POLICY >", value, "promotionPolicy");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyGreaterThanOrEqualTo(String value) {
            addCriterion("PROMOTION_POLICY >=", value, "promotionPolicy");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyLessThan(String value) {
            addCriterion("PROMOTION_POLICY <", value, "promotionPolicy");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyLessThanOrEqualTo(String value) {
            addCriterion("PROMOTION_POLICY <=", value, "promotionPolicy");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyLike(String value) {
            addCriterion("PROMOTION_POLICY like", value, "promotionPolicy");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyNotLike(String value) {
            addCriterion("PROMOTION_POLICY not like", value, "promotionPolicy");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyIn(List<String> values) {
            addCriterion("PROMOTION_POLICY in", values, "promotionPolicy");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyNotIn(List<String> values) {
            addCriterion("PROMOTION_POLICY not in", values, "promotionPolicy");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyBetween(String value1, String value2) {
            addCriterion("PROMOTION_POLICY between", value1, value2, "promotionPolicy");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyNotBetween(String value1, String value2) {
            addCriterion("PROMOTION_POLICY not between", value1, value2, "promotionPolicy");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodIsNull() {
            addCriterion("WARRANTY_PERIOD is null");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodIsNotNull() {
            addCriterion("WARRANTY_PERIOD is not null");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodEqualTo(String value) {
            addCriterion("WARRANTY_PERIOD =", value, "warrantyPeriod");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodNotEqualTo(String value) {
            addCriterion("WARRANTY_PERIOD <>", value, "warrantyPeriod");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodGreaterThan(String value) {
            addCriterion("WARRANTY_PERIOD >", value, "warrantyPeriod");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodGreaterThanOrEqualTo(String value) {
            addCriterion("WARRANTY_PERIOD >=", value, "warrantyPeriod");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodLessThan(String value) {
            addCriterion("WARRANTY_PERIOD <", value, "warrantyPeriod");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodLessThanOrEqualTo(String value) {
            addCriterion("WARRANTY_PERIOD <=", value, "warrantyPeriod");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodLike(String value) {
            addCriterion("WARRANTY_PERIOD like", value, "warrantyPeriod");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodNotLike(String value) {
            addCriterion("WARRANTY_PERIOD not like", value, "warrantyPeriod");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodIn(List<String> values) {
            addCriterion("WARRANTY_PERIOD in", values, "warrantyPeriod");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodNotIn(List<String> values) {
            addCriterion("WARRANTY_PERIOD not in", values, "warrantyPeriod");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodBetween(String value1, String value2) {
            addCriterion("WARRANTY_PERIOD between", value1, value2, "warrantyPeriod");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodNotBetween(String value1, String value2) {
            addCriterion("WARRANTY_PERIOD not between", value1, value2, "warrantyPeriod");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodRuleIsNull() {
            addCriterion("WARRANTY_PERIOD_RULE is null");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodRuleIsNotNull() {
            addCriterion("WARRANTY_PERIOD_RULE is not null");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodRuleEqualTo(String value) {
            addCriterion("WARRANTY_PERIOD_RULE =", value, "warrantyPeriodRule");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodRuleNotEqualTo(String value) {
            addCriterion("WARRANTY_PERIOD_RULE <>", value, "warrantyPeriodRule");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodRuleGreaterThan(String value) {
            addCriterion("WARRANTY_PERIOD_RULE >", value, "warrantyPeriodRule");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodRuleGreaterThanOrEqualTo(String value) {
            addCriterion("WARRANTY_PERIOD_RULE >=", value, "warrantyPeriodRule");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodRuleLessThan(String value) {
            addCriterion("WARRANTY_PERIOD_RULE <", value, "warrantyPeriodRule");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodRuleLessThanOrEqualTo(String value) {
            addCriterion("WARRANTY_PERIOD_RULE <=", value, "warrantyPeriodRule");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodRuleLike(String value) {
            addCriterion("WARRANTY_PERIOD_RULE like", value, "warrantyPeriodRule");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodRuleNotLike(String value) {
            addCriterion("WARRANTY_PERIOD_RULE not like", value, "warrantyPeriodRule");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodRuleIn(List<String> values) {
            addCriterion("WARRANTY_PERIOD_RULE in", values, "warrantyPeriodRule");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodRuleNotIn(List<String> values) {
            addCriterion("WARRANTY_PERIOD_RULE not in", values, "warrantyPeriodRule");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodRuleBetween(String value1, String value2) {
            addCriterion("WARRANTY_PERIOD_RULE between", value1, value2, "warrantyPeriodRule");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodRuleNotBetween(String value1, String value2) {
            addCriterion("WARRANTY_PERIOD_RULE not between", value1, value2, "warrantyPeriodRule");
            return (Criteria) this;
        }

        public Criteria andWarrantyRepairFeeIsNull() {
            addCriterion("WARRANTY_REPAIR_FEE is null");
            return (Criteria) this;
        }

        public Criteria andWarrantyRepairFeeIsNotNull() {
            addCriterion("WARRANTY_REPAIR_FEE is not null");
            return (Criteria) this;
        }

        public Criteria andWarrantyRepairFeeEqualTo(BigDecimal value) {
            addCriterion("WARRANTY_REPAIR_FEE =", value, "warrantyRepairFee");
            return (Criteria) this;
        }

        public Criteria andWarrantyRepairFeeNotEqualTo(BigDecimal value) {
            addCriterion("WARRANTY_REPAIR_FEE <>", value, "warrantyRepairFee");
            return (Criteria) this;
        }

        public Criteria andWarrantyRepairFeeGreaterThan(BigDecimal value) {
            addCriterion("WARRANTY_REPAIR_FEE >", value, "warrantyRepairFee");
            return (Criteria) this;
        }

        public Criteria andWarrantyRepairFeeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("WARRANTY_REPAIR_FEE >=", value, "warrantyRepairFee");
            return (Criteria) this;
        }

        public Criteria andWarrantyRepairFeeLessThan(BigDecimal value) {
            addCriterion("WARRANTY_REPAIR_FEE <", value, "warrantyRepairFee");
            return (Criteria) this;
        }

        public Criteria andWarrantyRepairFeeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("WARRANTY_REPAIR_FEE <=", value, "warrantyRepairFee");
            return (Criteria) this;
        }

        public Criteria andWarrantyRepairFeeIn(List<BigDecimal> values) {
            addCriterion("WARRANTY_REPAIR_FEE in", values, "warrantyRepairFee");
            return (Criteria) this;
        }

        public Criteria andWarrantyRepairFeeNotIn(List<BigDecimal> values) {
            addCriterion("WARRANTY_REPAIR_FEE not in", values, "warrantyRepairFee");
            return (Criteria) this;
        }

        public Criteria andWarrantyRepairFeeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("WARRANTY_REPAIR_FEE between", value1, value2, "warrantyRepairFee");
            return (Criteria) this;
        }

        public Criteria andWarrantyRepairFeeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("WARRANTY_REPAIR_FEE not between", value1, value2, "warrantyRepairFee");
            return (Criteria) this;
        }

        public Criteria andResponseTimeIsNull() {
            addCriterion("RESPONSE_TIME is null");
            return (Criteria) this;
        }

        public Criteria andResponseTimeIsNotNull() {
            addCriterion("RESPONSE_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andResponseTimeEqualTo(String value) {
            addCriterion("RESPONSE_TIME =", value, "responseTime");
            return (Criteria) this;
        }

        public Criteria andResponseTimeNotEqualTo(String value) {
            addCriterion("RESPONSE_TIME <>", value, "responseTime");
            return (Criteria) this;
        }

        public Criteria andResponseTimeGreaterThan(String value) {
            addCriterion("RESPONSE_TIME >", value, "responseTime");
            return (Criteria) this;
        }

        public Criteria andResponseTimeGreaterThanOrEqualTo(String value) {
            addCriterion("RESPONSE_TIME >=", value, "responseTime");
            return (Criteria) this;
        }

        public Criteria andResponseTimeLessThan(String value) {
            addCriterion("RESPONSE_TIME <", value, "responseTime");
            return (Criteria) this;
        }

        public Criteria andResponseTimeLessThanOrEqualTo(String value) {
            addCriterion("RESPONSE_TIME <=", value, "responseTime");
            return (Criteria) this;
        }

        public Criteria andResponseTimeLike(String value) {
            addCriterion("RESPONSE_TIME like", value, "responseTime");
            return (Criteria) this;
        }

        public Criteria andResponseTimeNotLike(String value) {
            addCriterion("RESPONSE_TIME not like", value, "responseTime");
            return (Criteria) this;
        }

        public Criteria andResponseTimeIn(List<String> values) {
            addCriterion("RESPONSE_TIME in", values, "responseTime");
            return (Criteria) this;
        }

        public Criteria andResponseTimeNotIn(List<String> values) {
            addCriterion("RESPONSE_TIME not in", values, "responseTime");
            return (Criteria) this;
        }

        public Criteria andResponseTimeBetween(String value1, String value2) {
            addCriterion("RESPONSE_TIME between", value1, value2, "responseTime");
            return (Criteria) this;
        }

        public Criteria andResponseTimeNotBetween(String value1, String value2) {
            addCriterion("RESPONSE_TIME not between", value1, value2, "responseTime");
            return (Criteria) this;
        }

        public Criteria andHaveStandbyMachineIsNull() {
            addCriterion("HAVE_STANDBY_MACHINE is null");
            return (Criteria) this;
        }

        public Criteria andHaveStandbyMachineIsNotNull() {
            addCriterion("HAVE_STANDBY_MACHINE is not null");
            return (Criteria) this;
        }

        public Criteria andHaveStandbyMachineEqualTo(Integer value) {
            addCriterion("HAVE_STANDBY_MACHINE =", value, "haveStandbyMachine");
            return (Criteria) this;
        }

        public Criteria andHaveStandbyMachineNotEqualTo(Integer value) {
            addCriterion("HAVE_STANDBY_MACHINE <>", value, "haveStandbyMachine");
            return (Criteria) this;
        }

        public Criteria andHaveStandbyMachineGreaterThan(Integer value) {
            addCriterion("HAVE_STANDBY_MACHINE >", value, "haveStandbyMachine");
            return (Criteria) this;
        }

        public Criteria andHaveStandbyMachineGreaterThanOrEqualTo(Integer value) {
            addCriterion("HAVE_STANDBY_MACHINE >=", value, "haveStandbyMachine");
            return (Criteria) this;
        }

        public Criteria andHaveStandbyMachineLessThan(Integer value) {
            addCriterion("HAVE_STANDBY_MACHINE <", value, "haveStandbyMachine");
            return (Criteria) this;
        }

        public Criteria andHaveStandbyMachineLessThanOrEqualTo(Integer value) {
            addCriterion("HAVE_STANDBY_MACHINE <=", value, "haveStandbyMachine");
            return (Criteria) this;
        }

        public Criteria andHaveStandbyMachineIn(List<Integer> values) {
            addCriterion("HAVE_STANDBY_MACHINE in", values, "haveStandbyMachine");
            return (Criteria) this;
        }

        public Criteria andHaveStandbyMachineNotIn(List<Integer> values) {
            addCriterion("HAVE_STANDBY_MACHINE not in", values, "haveStandbyMachine");
            return (Criteria) this;
        }

        public Criteria andHaveStandbyMachineBetween(Integer value1, Integer value2) {
            addCriterion("HAVE_STANDBY_MACHINE between", value1, value2, "haveStandbyMachine");
            return (Criteria) this;
        }

        public Criteria andHaveStandbyMachineNotBetween(Integer value1, Integer value2) {
            addCriterion("HAVE_STANDBY_MACHINE not between", value1, value2, "haveStandbyMachine");
            return (Criteria) this;
        }

        public Criteria andConditionsIsNull() {
            addCriterion("CONDITIONS is null");
            return (Criteria) this;
        }

        public Criteria andConditionsIsNotNull() {
            addCriterion("CONDITIONS is not null");
            return (Criteria) this;
        }

        public Criteria andConditionsEqualTo(String value) {
            addCriterion("CONDITIONS =", value, "conditions");
            return (Criteria) this;
        }

        public Criteria andConditionsNotEqualTo(String value) {
            addCriterion("CONDITIONS <>", value, "conditions");
            return (Criteria) this;
        }

        public Criteria andConditionsGreaterThan(String value) {
            addCriterion("CONDITIONS >", value, "conditions");
            return (Criteria) this;
        }

        public Criteria andConditionsGreaterThanOrEqualTo(String value) {
            addCriterion("CONDITIONS >=", value, "conditions");
            return (Criteria) this;
        }

        public Criteria andConditionsLessThan(String value) {
            addCriterion("CONDITIONS <", value, "conditions");
            return (Criteria) this;
        }

        public Criteria andConditionsLessThanOrEqualTo(String value) {
            addCriterion("CONDITIONS <=", value, "conditions");
            return (Criteria) this;
        }

        public Criteria andConditionsLike(String value) {
            addCriterion("CONDITIONS like", value, "conditions");
            return (Criteria) this;
        }

        public Criteria andConditionsNotLike(String value) {
            addCriterion("CONDITIONS not like", value, "conditions");
            return (Criteria) this;
        }

        public Criteria andConditionsIn(List<String> values) {
            addCriterion("CONDITIONS in", values, "conditions");
            return (Criteria) this;
        }

        public Criteria andConditionsNotIn(List<String> values) {
            addCriterion("CONDITIONS not in", values, "conditions");
            return (Criteria) this;
        }

        public Criteria andConditionsBetween(String value1, String value2) {
            addCriterion("CONDITIONS between", value1, value2, "conditions");
            return (Criteria) this;
        }

        public Criteria andConditionsNotBetween(String value1, String value2) {
            addCriterion("CONDITIONS not between", value1, value2, "conditions");
            return (Criteria) this;
        }

        public Criteria andExtendedWarrantyFeeIsNull() {
            addCriterion("EXTENDED_WARRANTY_FEE is null");
            return (Criteria) this;
        }

        public Criteria andExtendedWarrantyFeeIsNotNull() {
            addCriterion("EXTENDED_WARRANTY_FEE is not null");
            return (Criteria) this;
        }

        public Criteria andExtendedWarrantyFeeEqualTo(String value) {
            addCriterion("EXTENDED_WARRANTY_FEE =", value, "extendedWarrantyFee");
            return (Criteria) this;
        }

        public Criteria andExtendedWarrantyFeeNotEqualTo(String value) {
            addCriterion("EXTENDED_WARRANTY_FEE <>", value, "extendedWarrantyFee");
            return (Criteria) this;
        }

        public Criteria andExtendedWarrantyFeeGreaterThan(String value) {
            addCriterion("EXTENDED_WARRANTY_FEE >", value, "extendedWarrantyFee");
            return (Criteria) this;
        }

        public Criteria andExtendedWarrantyFeeGreaterThanOrEqualTo(String value) {
            addCriterion("EXTENDED_WARRANTY_FEE >=", value, "extendedWarrantyFee");
            return (Criteria) this;
        }

        public Criteria andExtendedWarrantyFeeLessThan(String value) {
            addCriterion("EXTENDED_WARRANTY_FEE <", value, "extendedWarrantyFee");
            return (Criteria) this;
        }

        public Criteria andExtendedWarrantyFeeLessThanOrEqualTo(String value) {
            addCriterion("EXTENDED_WARRANTY_FEE <=", value, "extendedWarrantyFee");
            return (Criteria) this;
        }

        public Criteria andExtendedWarrantyFeeLike(String value) {
            addCriterion("EXTENDED_WARRANTY_FEE like", value, "extendedWarrantyFee");
            return (Criteria) this;
        }

        public Criteria andExtendedWarrantyFeeNotLike(String value) {
            addCriterion("EXTENDED_WARRANTY_FEE not like", value, "extendedWarrantyFee");
            return (Criteria) this;
        }

        public Criteria andExtendedWarrantyFeeIn(List<String> values) {
            addCriterion("EXTENDED_WARRANTY_FEE in", values, "extendedWarrantyFee");
            return (Criteria) this;
        }

        public Criteria andExtendedWarrantyFeeNotIn(List<String> values) {
            addCriterion("EXTENDED_WARRANTY_FEE not in", values, "extendedWarrantyFee");
            return (Criteria) this;
        }

        public Criteria andExtendedWarrantyFeeBetween(String value1, String value2) {
            addCriterion("EXTENDED_WARRANTY_FEE between", value1, value2, "extendedWarrantyFee");
            return (Criteria) this;
        }

        public Criteria andExtendedWarrantyFeeNotBetween(String value1, String value2) {
            addCriterion("EXTENDED_WARRANTY_FEE not between", value1, value2, "extendedWarrantyFee");
            return (Criteria) this;
        }

        public Criteria andIsRefundIsNull() {
            addCriterion("IS_REFUND is null");
            return (Criteria) this;
        }

        public Criteria andIsRefundIsNotNull() {
            addCriterion("IS_REFUND is not null");
            return (Criteria) this;
        }

        public Criteria andIsRefundEqualTo(Integer value) {
            addCriterion("IS_REFUND =", value, "isRefund");
            return (Criteria) this;
        }

        public Criteria andIsRefundNotEqualTo(Integer value) {
            addCriterion("IS_REFUND <>", value, "isRefund");
            return (Criteria) this;
        }

        public Criteria andIsRefundGreaterThan(Integer value) {
            addCriterion("IS_REFUND >", value, "isRefund");
            return (Criteria) this;
        }

        public Criteria andIsRefundGreaterThanOrEqualTo(Integer value) {
            addCriterion("IS_REFUND >=", value, "isRefund");
            return (Criteria) this;
        }

        public Criteria andIsRefundLessThan(Integer value) {
            addCriterion("IS_REFUND <", value, "isRefund");
            return (Criteria) this;
        }

        public Criteria andIsRefundLessThanOrEqualTo(Integer value) {
            addCriterion("IS_REFUND <=", value, "isRefund");
            return (Criteria) this;
        }

        public Criteria andIsRefundIn(List<Integer> values) {
            addCriterion("IS_REFUND in", values, "isRefund");
            return (Criteria) this;
        }

        public Criteria andIsRefundNotIn(List<Integer> values) {
            addCriterion("IS_REFUND not in", values, "isRefund");
            return (Criteria) this;
        }

        public Criteria andIsRefundBetween(Integer value1, Integer value2) {
            addCriterion("IS_REFUND between", value1, value2, "isRefund");
            return (Criteria) this;
        }

        public Criteria andIsRefundNotBetween(Integer value1, Integer value2) {
            addCriterion("IS_REFUND not between", value1, value2, "isRefund");
            return (Criteria) this;
        }

        public Criteria andExchangeConditionsIsNull() {
            addCriterion("EXCHANGE_CONDITIONS is null");
            return (Criteria) this;
        }

        public Criteria andExchangeConditionsIsNotNull() {
            addCriterion("EXCHANGE_CONDITIONS is not null");
            return (Criteria) this;
        }

        public Criteria andExchangeConditionsEqualTo(String value) {
            addCriterion("EXCHANGE_CONDITIONS =", value, "exchangeConditions");
            return (Criteria) this;
        }

        public Criteria andExchangeConditionsNotEqualTo(String value) {
            addCriterion("EXCHANGE_CONDITIONS <>", value, "exchangeConditions");
            return (Criteria) this;
        }

        public Criteria andExchangeConditionsGreaterThan(String value) {
            addCriterion("EXCHANGE_CONDITIONS >", value, "exchangeConditions");
            return (Criteria) this;
        }

        public Criteria andExchangeConditionsGreaterThanOrEqualTo(String value) {
            addCriterion("EXCHANGE_CONDITIONS >=", value, "exchangeConditions");
            return (Criteria) this;
        }

        public Criteria andExchangeConditionsLessThan(String value) {
            addCriterion("EXCHANGE_CONDITIONS <", value, "exchangeConditions");
            return (Criteria) this;
        }

        public Criteria andExchangeConditionsLessThanOrEqualTo(String value) {
            addCriterion("EXCHANGE_CONDITIONS <=", value, "exchangeConditions");
            return (Criteria) this;
        }

        public Criteria andExchangeConditionsLike(String value) {
            addCriterion("EXCHANGE_CONDITIONS like", value, "exchangeConditions");
            return (Criteria) this;
        }

        public Criteria andExchangeConditionsNotLike(String value) {
            addCriterion("EXCHANGE_CONDITIONS not like", value, "exchangeConditions");
            return (Criteria) this;
        }

        public Criteria andExchangeConditionsIn(List<String> values) {
            addCriterion("EXCHANGE_CONDITIONS in", values, "exchangeConditions");
            return (Criteria) this;
        }

        public Criteria andExchangeConditionsNotIn(List<String> values) {
            addCriterion("EXCHANGE_CONDITIONS not in", values, "exchangeConditions");
            return (Criteria) this;
        }

        public Criteria andExchangeConditionsBetween(String value1, String value2) {
            addCriterion("EXCHANGE_CONDITIONS between", value1, value2, "exchangeConditions");
            return (Criteria) this;
        }

        public Criteria andExchangeConditionsNotBetween(String value1, String value2) {
            addCriterion("EXCHANGE_CONDITIONS not between", value1, value2, "exchangeConditions");
            return (Criteria) this;
        }

        public Criteria andExchangeModeIsNull() {
            addCriterion("EXCHANGE_MODE is null");
            return (Criteria) this;
        }

        public Criteria andExchangeModeIsNotNull() {
            addCriterion("EXCHANGE_MODE is not null");
            return (Criteria) this;
        }

        public Criteria andExchangeModeEqualTo(String value) {
            addCriterion("EXCHANGE_MODE =", value, "exchangeMode");
            return (Criteria) this;
        }

        public Criteria andExchangeModeNotEqualTo(String value) {
            addCriterion("EXCHANGE_MODE <>", value, "exchangeMode");
            return (Criteria) this;
        }

        public Criteria andExchangeModeGreaterThan(String value) {
            addCriterion("EXCHANGE_MODE >", value, "exchangeMode");
            return (Criteria) this;
        }

        public Criteria andExchangeModeGreaterThanOrEqualTo(String value) {
            addCriterion("EXCHANGE_MODE >=", value, "exchangeMode");
            return (Criteria) this;
        }

        public Criteria andExchangeModeLessThan(String value) {
            addCriterion("EXCHANGE_MODE <", value, "exchangeMode");
            return (Criteria) this;
        }

        public Criteria andExchangeModeLessThanOrEqualTo(String value) {
            addCriterion("EXCHANGE_MODE <=", value, "exchangeMode");
            return (Criteria) this;
        }

        public Criteria andExchangeModeLike(String value) {
            addCriterion("EXCHANGE_MODE like", value, "exchangeMode");
            return (Criteria) this;
        }

        public Criteria andExchangeModeNotLike(String value) {
            addCriterion("EXCHANGE_MODE not like", value, "exchangeMode");
            return (Criteria) this;
        }

        public Criteria andExchangeModeIn(List<String> values) {
            addCriterion("EXCHANGE_MODE in", values, "exchangeMode");
            return (Criteria) this;
        }

        public Criteria andExchangeModeNotIn(List<String> values) {
            addCriterion("EXCHANGE_MODE not in", values, "exchangeMode");
            return (Criteria) this;
        }

        public Criteria andExchangeModeBetween(String value1, String value2) {
            addCriterion("EXCHANGE_MODE between", value1, value2, "exchangeMode");
            return (Criteria) this;
        }

        public Criteria andExchangeModeNotBetween(String value1, String value2) {
            addCriterion("EXCHANGE_MODE not between", value1, value2, "exchangeMode");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionIsNull() {
            addCriterion("FREIGHT_DESCRIPTION is null");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionIsNotNull() {
            addCriterion("FREIGHT_DESCRIPTION is not null");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionEqualTo(String value) {
            addCriterion("FREIGHT_DESCRIPTION =", value, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionNotEqualTo(String value) {
            addCriterion("FREIGHT_DESCRIPTION <>", value, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionGreaterThan(String value) {
            addCriterion("FREIGHT_DESCRIPTION >", value, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("FREIGHT_DESCRIPTION >=", value, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionLessThan(String value) {
            addCriterion("FREIGHT_DESCRIPTION <", value, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionLessThanOrEqualTo(String value) {
            addCriterion("FREIGHT_DESCRIPTION <=", value, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionLike(String value) {
            addCriterion("FREIGHT_DESCRIPTION like", value, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionNotLike(String value) {
            addCriterion("FREIGHT_DESCRIPTION not like", value, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionIn(List<String> values) {
            addCriterion("FREIGHT_DESCRIPTION in", values, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionNotIn(List<String> values) {
            addCriterion("FREIGHT_DESCRIPTION not in", values, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionBetween(String value1, String value2) {
            addCriterion("FREIGHT_DESCRIPTION between", value1, value2, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionNotBetween(String value1, String value2) {
            addCriterion("FREIGHT_DESCRIPTION not between", value1, value2, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andDeliveryIsNull() {
            addCriterion("DELIVERY is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryIsNotNull() {
            addCriterion("DELIVERY is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryEqualTo(String value) {
            addCriterion("DELIVERY =", value, "delivery");
            return (Criteria) this;
        }

        public Criteria andDeliveryNotEqualTo(String value) {
            addCriterion("DELIVERY <>", value, "delivery");
            return (Criteria) this;
        }

        public Criteria andDeliveryGreaterThan(String value) {
            addCriterion("DELIVERY >", value, "delivery");
            return (Criteria) this;
        }

        public Criteria andDeliveryGreaterThanOrEqualTo(String value) {
            addCriterion("DELIVERY >=", value, "delivery");
            return (Criteria) this;
        }

        public Criteria andDeliveryLessThan(String value) {
            addCriterion("DELIVERY <", value, "delivery");
            return (Criteria) this;
        }

        public Criteria andDeliveryLessThanOrEqualTo(String value) {
            addCriterion("DELIVERY <=", value, "delivery");
            return (Criteria) this;
        }

        public Criteria andDeliveryLike(String value) {
            addCriterion("DELIVERY like", value, "delivery");
            return (Criteria) this;
        }

        public Criteria andDeliveryNotLike(String value) {
            addCriterion("DELIVERY not like", value, "delivery");
            return (Criteria) this;
        }

        public Criteria andDeliveryIn(List<String> values) {
            addCriterion("DELIVERY in", values, "delivery");
            return (Criteria) this;
        }

        public Criteria andDeliveryNotIn(List<String> values) {
            addCriterion("DELIVERY not in", values, "delivery");
            return (Criteria) this;
        }

        public Criteria andDeliveryBetween(String value1, String value2) {
            addCriterion("DELIVERY between", value1, value2, "delivery");
            return (Criteria) this;
        }

        public Criteria andDeliveryNotBetween(String value1, String value2) {
            addCriterion("DELIVERY not between", value1, value2, "delivery");
            return (Criteria) this;
        }

        public Criteria andFuturesDeliveryIsNull() {
            addCriterion("FUTURES_DELIVERY is null");
            return (Criteria) this;
        }

        public Criteria andFuturesDeliveryIsNotNull() {
            addCriterion("FUTURES_DELIVERY is not null");
            return (Criteria) this;
        }

        public Criteria andFuturesDeliveryEqualTo(String value) {
            addCriterion("FUTURES_DELIVERY =", value, "futuresDelivery");
            return (Criteria) this;
        }

        public Criteria andFuturesDeliveryNotEqualTo(String value) {
            addCriterion("FUTURES_DELIVERY <>", value, "futuresDelivery");
            return (Criteria) this;
        }

        public Criteria andFuturesDeliveryGreaterThan(String value) {
            addCriterion("FUTURES_DELIVERY >", value, "futuresDelivery");
            return (Criteria) this;
        }

        public Criteria andFuturesDeliveryGreaterThanOrEqualTo(String value) {
            addCriterion("FUTURES_DELIVERY >=", value, "futuresDelivery");
            return (Criteria) this;
        }

        public Criteria andFuturesDeliveryLessThan(String value) {
            addCriterion("FUTURES_DELIVERY <", value, "futuresDelivery");
            return (Criteria) this;
        }

        public Criteria andFuturesDeliveryLessThanOrEqualTo(String value) {
            addCriterion("FUTURES_DELIVERY <=", value, "futuresDelivery");
            return (Criteria) this;
        }

        public Criteria andFuturesDeliveryLike(String value) {
            addCriterion("FUTURES_DELIVERY like", value, "futuresDelivery");
            return (Criteria) this;
        }

        public Criteria andFuturesDeliveryNotLike(String value) {
            addCriterion("FUTURES_DELIVERY not like", value, "futuresDelivery");
            return (Criteria) this;
        }

        public Criteria andFuturesDeliveryIn(List<String> values) {
            addCriterion("FUTURES_DELIVERY in", values, "futuresDelivery");
            return (Criteria) this;
        }

        public Criteria andFuturesDeliveryNotIn(List<String> values) {
            addCriterion("FUTURES_DELIVERY not in", values, "futuresDelivery");
            return (Criteria) this;
        }

        public Criteria andFuturesDeliveryBetween(String value1, String value2) {
            addCriterion("FUTURES_DELIVERY between", value1, value2, "futuresDelivery");
            return (Criteria) this;
        }

        public Criteria andFuturesDeliveryNotBetween(String value1, String value2) {
            addCriterion("FUTURES_DELIVERY not between", value1, value2, "futuresDelivery");
            return (Criteria) this;
        }

        public Criteria andTransportRequirementsIsNull() {
            addCriterion("TRANSPORT_REQUIREMENTS is null");
            return (Criteria) this;
        }

        public Criteria andTransportRequirementsIsNotNull() {
            addCriterion("TRANSPORT_REQUIREMENTS is not null");
            return (Criteria) this;
        }

        public Criteria andTransportRequirementsEqualTo(String value) {
            addCriterion("TRANSPORT_REQUIREMENTS =", value, "transportRequirements");
            return (Criteria) this;
        }

        public Criteria andTransportRequirementsNotEqualTo(String value) {
            addCriterion("TRANSPORT_REQUIREMENTS <>", value, "transportRequirements");
            return (Criteria) this;
        }

        public Criteria andTransportRequirementsGreaterThan(String value) {
            addCriterion("TRANSPORT_REQUIREMENTS >", value, "transportRequirements");
            return (Criteria) this;
        }

        public Criteria andTransportRequirementsGreaterThanOrEqualTo(String value) {
            addCriterion("TRANSPORT_REQUIREMENTS >=", value, "transportRequirements");
            return (Criteria) this;
        }

        public Criteria andTransportRequirementsLessThan(String value) {
            addCriterion("TRANSPORT_REQUIREMENTS <", value, "transportRequirements");
            return (Criteria) this;
        }

        public Criteria andTransportRequirementsLessThanOrEqualTo(String value) {
            addCriterion("TRANSPORT_REQUIREMENTS <=", value, "transportRequirements");
            return (Criteria) this;
        }

        public Criteria andTransportRequirementsLike(String value) {
            addCriterion("TRANSPORT_REQUIREMENTS like", value, "transportRequirements");
            return (Criteria) this;
        }

        public Criteria andTransportRequirementsNotLike(String value) {
            addCriterion("TRANSPORT_REQUIREMENTS not like", value, "transportRequirements");
            return (Criteria) this;
        }

        public Criteria andTransportRequirementsIn(List<String> values) {
            addCriterion("TRANSPORT_REQUIREMENTS in", values, "transportRequirements");
            return (Criteria) this;
        }

        public Criteria andTransportRequirementsNotIn(List<String> values) {
            addCriterion("TRANSPORT_REQUIREMENTS not in", values, "transportRequirements");
            return (Criteria) this;
        }

        public Criteria andTransportRequirementsBetween(String value1, String value2) {
            addCriterion("TRANSPORT_REQUIREMENTS between", value1, value2, "transportRequirements");
            return (Criteria) this;
        }

        public Criteria andTransportRequirementsNotBetween(String value1, String value2) {
            addCriterion("TRANSPORT_REQUIREMENTS not between", value1, value2, "transportRequirements");
            return (Criteria) this;
        }

        public Criteria andTransportWeightIsNull() {
            addCriterion("TRANSPORT_WEIGHT is null");
            return (Criteria) this;
        }

        public Criteria andTransportWeightIsNotNull() {
            addCriterion("TRANSPORT_WEIGHT is not null");
            return (Criteria) this;
        }

        public Criteria andTransportWeightEqualTo(String value) {
            addCriterion("TRANSPORT_WEIGHT =", value, "transportWeight");
            return (Criteria) this;
        }

        public Criteria andTransportWeightNotEqualTo(String value) {
            addCriterion("TRANSPORT_WEIGHT <>", value, "transportWeight");
            return (Criteria) this;
        }

        public Criteria andTransportWeightGreaterThan(String value) {
            addCriterion("TRANSPORT_WEIGHT >", value, "transportWeight");
            return (Criteria) this;
        }

        public Criteria andTransportWeightGreaterThanOrEqualTo(String value) {
            addCriterion("TRANSPORT_WEIGHT >=", value, "transportWeight");
            return (Criteria) this;
        }

        public Criteria andTransportWeightLessThan(String value) {
            addCriterion("TRANSPORT_WEIGHT <", value, "transportWeight");
            return (Criteria) this;
        }

        public Criteria andTransportWeightLessThanOrEqualTo(String value) {
            addCriterion("TRANSPORT_WEIGHT <=", value, "transportWeight");
            return (Criteria) this;
        }

        public Criteria andTransportWeightLike(String value) {
            addCriterion("TRANSPORT_WEIGHT like", value, "transportWeight");
            return (Criteria) this;
        }

        public Criteria andTransportWeightNotLike(String value) {
            addCriterion("TRANSPORT_WEIGHT not like", value, "transportWeight");
            return (Criteria) this;
        }

        public Criteria andTransportWeightIn(List<String> values) {
            addCriterion("TRANSPORT_WEIGHT in", values, "transportWeight");
            return (Criteria) this;
        }

        public Criteria andTransportWeightNotIn(List<String> values) {
            addCriterion("TRANSPORT_WEIGHT not in", values, "transportWeight");
            return (Criteria) this;
        }

        public Criteria andTransportWeightBetween(String value1, String value2) {
            addCriterion("TRANSPORT_WEIGHT between", value1, value2, "transportWeight");
            return (Criteria) this;
        }

        public Criteria andTransportWeightNotBetween(String value1, String value2) {
            addCriterion("TRANSPORT_WEIGHT not between", value1, value2, "transportWeight");
            return (Criteria) this;
        }

        public Criteria andIsHvaeFreightIsNull() {
            addCriterion("IS_HVAE_FREIGHT is null");
            return (Criteria) this;
        }

        public Criteria andIsHvaeFreightIsNotNull() {
            addCriterion("IS_HVAE_FREIGHT is not null");
            return (Criteria) this;
        }

        public Criteria andIsHvaeFreightEqualTo(Integer value) {
            addCriterion("IS_HVAE_FREIGHT =", value, "isHvaeFreight");
            return (Criteria) this;
        }

        public Criteria andIsHvaeFreightNotEqualTo(Integer value) {
            addCriterion("IS_HVAE_FREIGHT <>", value, "isHvaeFreight");
            return (Criteria) this;
        }

        public Criteria andIsHvaeFreightGreaterThan(Integer value) {
            addCriterion("IS_HVAE_FREIGHT >", value, "isHvaeFreight");
            return (Criteria) this;
        }

        public Criteria andIsHvaeFreightGreaterThanOrEqualTo(Integer value) {
            addCriterion("IS_HVAE_FREIGHT >=", value, "isHvaeFreight");
            return (Criteria) this;
        }

        public Criteria andIsHvaeFreightLessThan(Integer value) {
            addCriterion("IS_HVAE_FREIGHT <", value, "isHvaeFreight");
            return (Criteria) this;
        }

        public Criteria andIsHvaeFreightLessThanOrEqualTo(Integer value) {
            addCriterion("IS_HVAE_FREIGHT <=", value, "isHvaeFreight");
            return (Criteria) this;
        }

        public Criteria andIsHvaeFreightIn(List<Integer> values) {
            addCriterion("IS_HVAE_FREIGHT in", values, "isHvaeFreight");
            return (Criteria) this;
        }

        public Criteria andIsHvaeFreightNotIn(List<Integer> values) {
            addCriterion("IS_HVAE_FREIGHT not in", values, "isHvaeFreight");
            return (Criteria) this;
        }

        public Criteria andIsHvaeFreightBetween(Integer value1, Integer value2) {
            addCriterion("IS_HVAE_FREIGHT between", value1, value2, "isHvaeFreight");
            return (Criteria) this;
        }

        public Criteria andIsHvaeFreightNotBetween(Integer value1, Integer value2) {
            addCriterion("IS_HVAE_FREIGHT not between", value1, value2, "isHvaeFreight");
            return (Criteria) this;
        }

        public Criteria andTransportationCompletionStandardIsNull() {
            addCriterion("TRANSPORTATION_COMPLETION_STANDARD is null");
            return (Criteria) this;
        }

        public Criteria andTransportationCompletionStandardIsNotNull() {
            addCriterion("TRANSPORTATION_COMPLETION_STANDARD is not null");
            return (Criteria) this;
        }

        public Criteria andTransportationCompletionStandardEqualTo(Integer value) {
            addCriterion("TRANSPORTATION_COMPLETION_STANDARD =", value, "transportationCompletionStandard");
            return (Criteria) this;
        }

        public Criteria andTransportationCompletionStandardNotEqualTo(Integer value) {
            addCriterion("TRANSPORTATION_COMPLETION_STANDARD <>", value, "transportationCompletionStandard");
            return (Criteria) this;
        }

        public Criteria andTransportationCompletionStandardGreaterThan(Integer value) {
            addCriterion("TRANSPORTATION_COMPLETION_STANDARD >", value, "transportationCompletionStandard");
            return (Criteria) this;
        }

        public Criteria andTransportationCompletionStandardGreaterThanOrEqualTo(Integer value) {
            addCriterion("TRANSPORTATION_COMPLETION_STANDARD >=", value, "transportationCompletionStandard");
            return (Criteria) this;
        }

        public Criteria andTransportationCompletionStandardLessThan(Integer value) {
            addCriterion("TRANSPORTATION_COMPLETION_STANDARD <", value, "transportationCompletionStandard");
            return (Criteria) this;
        }

        public Criteria andTransportationCompletionStandardLessThanOrEqualTo(Integer value) {
            addCriterion("TRANSPORTATION_COMPLETION_STANDARD <=", value, "transportationCompletionStandard");
            return (Criteria) this;
        }

        public Criteria andTransportationCompletionStandardIn(List<Integer> values) {
            addCriterion("TRANSPORTATION_COMPLETION_STANDARD in", values, "transportationCompletionStandard");
            return (Criteria) this;
        }

        public Criteria andTransportationCompletionStandardNotIn(List<Integer> values) {
            addCriterion("TRANSPORTATION_COMPLETION_STANDARD not in", values, "transportationCompletionStandard");
            return (Criteria) this;
        }

        public Criteria andTransportationCompletionStandardBetween(Integer value1, Integer value2) {
            addCriterion("TRANSPORTATION_COMPLETION_STANDARD between", value1, value2, "transportationCompletionStandard");
            return (Criteria) this;
        }

        public Criteria andTransportationCompletionStandardNotBetween(Integer value1, Integer value2) {
            addCriterion("TRANSPORTATION_COMPLETION_STANDARD not between", value1, value2, "transportationCompletionStandard");
            return (Criteria) this;
        }

        public Criteria andAcceptanceNoticeIsNull() {
            addCriterion("ACCEPTANCE_NOTICE is null");
            return (Criteria) this;
        }

        public Criteria andAcceptanceNoticeIsNotNull() {
            addCriterion("ACCEPTANCE_NOTICE is not null");
            return (Criteria) this;
        }

        public Criteria andAcceptanceNoticeEqualTo(String value) {
            addCriterion("ACCEPTANCE_NOTICE =", value, "acceptanceNotice");
            return (Criteria) this;
        }

        public Criteria andAcceptanceNoticeNotEqualTo(String value) {
            addCriterion("ACCEPTANCE_NOTICE <>", value, "acceptanceNotice");
            return (Criteria) this;
        }

        public Criteria andAcceptanceNoticeGreaterThan(String value) {
            addCriterion("ACCEPTANCE_NOTICE >", value, "acceptanceNotice");
            return (Criteria) this;
        }

        public Criteria andAcceptanceNoticeGreaterThanOrEqualTo(String value) {
            addCriterion("ACCEPTANCE_NOTICE >=", value, "acceptanceNotice");
            return (Criteria) this;
        }

        public Criteria andAcceptanceNoticeLessThan(String value) {
            addCriterion("ACCEPTANCE_NOTICE <", value, "acceptanceNotice");
            return (Criteria) this;
        }

        public Criteria andAcceptanceNoticeLessThanOrEqualTo(String value) {
            addCriterion("ACCEPTANCE_NOTICE <=", value, "acceptanceNotice");
            return (Criteria) this;
        }

        public Criteria andAcceptanceNoticeLike(String value) {
            addCriterion("ACCEPTANCE_NOTICE like", value, "acceptanceNotice");
            return (Criteria) this;
        }

        public Criteria andAcceptanceNoticeNotLike(String value) {
            addCriterion("ACCEPTANCE_NOTICE not like", value, "acceptanceNotice");
            return (Criteria) this;
        }

        public Criteria andAcceptanceNoticeIn(List<String> values) {
            addCriterion("ACCEPTANCE_NOTICE in", values, "acceptanceNotice");
            return (Criteria) this;
        }

        public Criteria andAcceptanceNoticeNotIn(List<String> values) {
            addCriterion("ACCEPTANCE_NOTICE not in", values, "acceptanceNotice");
            return (Criteria) this;
        }

        public Criteria andAcceptanceNoticeBetween(String value1, String value2) {
            addCriterion("ACCEPTANCE_NOTICE between", value1, value2, "acceptanceNotice");
            return (Criteria) this;
        }

        public Criteria andAcceptanceNoticeNotBetween(String value1, String value2) {
            addCriterion("ACCEPTANCE_NOTICE not between", value1, value2, "acceptanceNotice");
            return (Criteria) this;
        }

        public Criteria andPackingNumberIsNull() {
            addCriterion("PACKING_NUMBER is null");
            return (Criteria) this;
        }

        public Criteria andPackingNumberIsNotNull() {
            addCriterion("PACKING_NUMBER is not null");
            return (Criteria) this;
        }

        public Criteria andPackingNumberEqualTo(String value) {
            addCriterion("PACKING_NUMBER =", value, "packingNumber");
            return (Criteria) this;
        }

        public Criteria andPackingNumberNotEqualTo(String value) {
            addCriterion("PACKING_NUMBER <>", value, "packingNumber");
            return (Criteria) this;
        }

        public Criteria andPackingNumberGreaterThan(String value) {
            addCriterion("PACKING_NUMBER >", value, "packingNumber");
            return (Criteria) this;
        }

        public Criteria andPackingNumberGreaterThanOrEqualTo(String value) {
            addCriterion("PACKING_NUMBER >=", value, "packingNumber");
            return (Criteria) this;
        }

        public Criteria andPackingNumberLessThan(String value) {
            addCriterion("PACKING_NUMBER <", value, "packingNumber");
            return (Criteria) this;
        }

        public Criteria andPackingNumberLessThanOrEqualTo(String value) {
            addCriterion("PACKING_NUMBER <=", value, "packingNumber");
            return (Criteria) this;
        }

        public Criteria andPackingNumberLike(String value) {
            addCriterion("PACKING_NUMBER like", value, "packingNumber");
            return (Criteria) this;
        }

        public Criteria andPackingNumberNotLike(String value) {
            addCriterion("PACKING_NUMBER not like", value, "packingNumber");
            return (Criteria) this;
        }

        public Criteria andPackingNumberIn(List<String> values) {
            addCriterion("PACKING_NUMBER in", values, "packingNumber");
            return (Criteria) this;
        }

        public Criteria andPackingNumberNotIn(List<String> values) {
            addCriterion("PACKING_NUMBER not in", values, "packingNumber");
            return (Criteria) this;
        }

        public Criteria andPackingNumberBetween(String value1, String value2) {
            addCriterion("PACKING_NUMBER between", value1, value2, "packingNumber");
            return (Criteria) this;
        }

        public Criteria andPackingNumberNotBetween(String value1, String value2) {
            addCriterion("PACKING_NUMBER not between", value1, value2, "packingNumber");
            return (Criteria) this;
        }

        public Criteria andPackingQuantityIsNull() {
            addCriterion("PACKING_QUANTITY is null");
            return (Criteria) this;
        }

        public Criteria andPackingQuantityIsNotNull() {
            addCriterion("PACKING_QUANTITY is not null");
            return (Criteria) this;
        }

        public Criteria andPackingQuantityEqualTo(String value) {
            addCriterion("PACKING_QUANTITY =", value, "packingQuantity");
            return (Criteria) this;
        }

        public Criteria andPackingQuantityNotEqualTo(String value) {
            addCriterion("PACKING_QUANTITY <>", value, "packingQuantity");
            return (Criteria) this;
        }

        public Criteria andPackingQuantityGreaterThan(String value) {
            addCriterion("PACKING_QUANTITY >", value, "packingQuantity");
            return (Criteria) this;
        }

        public Criteria andPackingQuantityGreaterThanOrEqualTo(String value) {
            addCriterion("PACKING_QUANTITY >=", value, "packingQuantity");
            return (Criteria) this;
        }

        public Criteria andPackingQuantityLessThan(String value) {
            addCriterion("PACKING_QUANTITY <", value, "packingQuantity");
            return (Criteria) this;
        }

        public Criteria andPackingQuantityLessThanOrEqualTo(String value) {
            addCriterion("PACKING_QUANTITY <=", value, "packingQuantity");
            return (Criteria) this;
        }

        public Criteria andPackingQuantityLike(String value) {
            addCriterion("PACKING_QUANTITY like", value, "packingQuantity");
            return (Criteria) this;
        }

        public Criteria andPackingQuantityNotLike(String value) {
            addCriterion("PACKING_QUANTITY not like", value, "packingQuantity");
            return (Criteria) this;
        }

        public Criteria andPackingQuantityIn(List<String> values) {
            addCriterion("PACKING_QUANTITY in", values, "packingQuantity");
            return (Criteria) this;
        }

        public Criteria andPackingQuantityNotIn(List<String> values) {
            addCriterion("PACKING_QUANTITY not in", values, "packingQuantity");
            return (Criteria) this;
        }

        public Criteria andPackingQuantityBetween(String value1, String value2) {
            addCriterion("PACKING_QUANTITY between", value1, value2, "packingQuantity");
            return (Criteria) this;
        }

        public Criteria andPackingQuantityNotBetween(String value1, String value2) {
            addCriterion("PACKING_QUANTITY not between", value1, value2, "packingQuantity");
            return (Criteria) this;
        }

        public Criteria andAdvantageIsNull() {
            addCriterion("ADVANTAGE is null");
            return (Criteria) this;
        }

        public Criteria andAdvantageIsNotNull() {
            addCriterion("ADVANTAGE is not null");
            return (Criteria) this;
        }

        public Criteria andAdvantageEqualTo(String value) {
            addCriterion("ADVANTAGE =", value, "advantage");
            return (Criteria) this;
        }

        public Criteria andAdvantageNotEqualTo(String value) {
            addCriterion("ADVANTAGE <>", value, "advantage");
            return (Criteria) this;
        }

        public Criteria andAdvantageGreaterThan(String value) {
            addCriterion("ADVANTAGE >", value, "advantage");
            return (Criteria) this;
        }

        public Criteria andAdvantageGreaterThanOrEqualTo(String value) {
            addCriterion("ADVANTAGE >=", value, "advantage");
            return (Criteria) this;
        }

        public Criteria andAdvantageLessThan(String value) {
            addCriterion("ADVANTAGE <", value, "advantage");
            return (Criteria) this;
        }

        public Criteria andAdvantageLessThanOrEqualTo(String value) {
            addCriterion("ADVANTAGE <=", value, "advantage");
            return (Criteria) this;
        }

        public Criteria andAdvantageLike(String value) {
            addCriterion("ADVANTAGE like", value, "advantage");
            return (Criteria) this;
        }

        public Criteria andAdvantageNotLike(String value) {
            addCriterion("ADVANTAGE not like", value, "advantage");
            return (Criteria) this;
        }

        public Criteria andAdvantageIn(List<String> values) {
            addCriterion("ADVANTAGE in", values, "advantage");
            return (Criteria) this;
        }

        public Criteria andAdvantageNotIn(List<String> values) {
            addCriterion("ADVANTAGE not in", values, "advantage");
            return (Criteria) this;
        }

        public Criteria andAdvantageBetween(String value1, String value2) {
            addCriterion("ADVANTAGE between", value1, value2, "advantage");
            return (Criteria) this;
        }

        public Criteria andAdvantageNotBetween(String value1, String value2) {
            addCriterion("ADVANTAGE not between", value1, value2, "advantage");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated do_not_delete_during_merge Thu Jun 27 19:06:50 CST 2019
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}