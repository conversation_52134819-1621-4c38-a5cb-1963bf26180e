package com.test.stock;

import com.vedeng.logistics.dao.BarcodeMapper;
import com.vedeng.logistics.dao.WarehouseGoodsOperateLogMapper;
import com.vedeng.logistics.model.Barcode;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @ClassName VedengBatchNumberService2.java
 * @Description TODO
 * @createTime 2020年10月21日 10:41:00
 */
public class VedengBatchNumberService2 extends SpeicalWarehouseProcessor {

    @Resource
    private WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

    @Resource
    private BarcodeMapper barcodeMapper;

    @Test
    public void test1(){
        List<WarehouseGoodsOperateLog> list =  warehouseGoodsOperateLogMapper.getNullbarcodeIdInlog();
        List<String> strings = new ArrayList<>();
        Set<String> set = new HashSet<>();
        list.forEach(log -> {
            Barcode barcode1 = barcodeMapper.getBarcodeByBarcode(log.getVedengBatchNumer());
            WarehouseGoodsOperateLog update = new WarehouseGoodsOperateLog();
            update.setWarehouseGoodsOperateLogId(log.getWarehouseGoodsOperateLogId());
            if(barcode1 != null){
                String sb = "UPDATE T_WAREHOUSE_GOODS_OPERATE_LOG SET BARCODE_ID = #{barcodeId} WHERE WAREHOUSE_GOODS_OPERATE_LOG_ID = #{id};";
                List<WarehouseGoodsOperateLog> outlog = warehouseGoodsOperateLogMapper.getNullbarcodeIdOutlog(log);
                update.setBarcodeId(barcode1.getBarcodeId());
                sb.replace("#{barcodeId}",barcode1.getBarcodeId().toString()).replace(" #{id}",log.getWarehouseGoodsOperateLogId().toString());
                strings.add( sb.replace("#{barcodeId}",barcode1.getBarcodeId().toString()).replace(" #{id}",log.getWarehouseGoodsOperateLogId().toString()));
                for (WarehouseGoodsOperateLog warehouseGoodsOperateLog : outlog) {
                    strings.add( sb.replace("#{barcodeId}",barcode1.getBarcodeId().toString()).replace(" #{id}",warehouseGoodsOperateLog.getWarehouseGoodsOperateLogId().toString()));
                }
            }else{
                if(!set.add(log.getVedengBatchNumer())){
                    return;
                }
                StringBuilder sb = new StringBuilder(" insert into  T_BARCODE( TYPE,IS_ENABLE,DETAIL_GOODS_ID,BARCODE,GOODS_ID,ADD_TIME,MOD_TIME ) values (3,1,");

                StringBuilder append = sb.append(log.getRelatedId() + ",").append("\'"+log.getVedengBatchNumer()+"\',").append(log.getGoodsId() + ",").append(System.currentTimeMillis() + ",").append(System.currentTimeMillis() + ");");
                strings.add(append.toString());
            }
        });
        for (String string : strings) {
            System.out.println(string);
        }
    }
}
