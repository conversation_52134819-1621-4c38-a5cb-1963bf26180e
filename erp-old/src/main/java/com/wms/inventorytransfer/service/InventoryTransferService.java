package com.wms.inventorytransfer.service;


import com.vedeng.common.page.Page;
import com.vedeng.common.service.BaseService;
import com.wms.dto.WMSResultInfo;
import com.wms.inventorytransfer.model.dto.InventoryTransferDto;
import com.wms.inventorytransfer.model.dto.InventoryTransferRequest;
import com.wms.inventorytransfer.model.dto.InventoryTransferSearchDto;
import com.wms.dto.WmsInventoryTransferDto;
import com.wms.inventorytransfer.model.vo.InventoryTransferDetailVO;
import com.wms.inventorytransfer.model.vo.InventoryTransferVO;

import java.util.List;
import java.util.Map;

/**
 * 库存转移单
 *
 * <AUTHOR>
 * @date 2020/7/10 14:22:10
 */
public interface InventoryTransferService extends BaseService {

    /**
     * @return
     * @describe 获取库存转移单信息
     * <AUTHOR>
     * @date 2020/7/10 14:10:15
     */
    Map<String, Object> getInventoryTransferVos(InventoryTransferSearchDto inventoryTransferDto, Page page);

    /**
     * @return
     * @describe 获取库存转移单详情信息
     * <AUTHOR>
     * @date 2020/7/10 15:27:15
     */
    List<InventoryTransferDetailVO> getInventoryTransferDetailById(Integer inventoryTransferId);

    /**
     * @param inventoryTransferId
     * @return
     * @describe 通过库存转移单ID查询信息
     * <AUTHOR>
     * @date 2020/7/13 9:45:22
     */
    InventoryTransferVO getInventoryTransferById(Integer inventoryTransferId);

    /**
     * @param actionId 活动ID
     * @return
     * @describe 根据活动ID获取库存转移单信息
     * <AUTHOR>
     * @date 2020/7/20 9:12:24
     */
    List<InventoryTransferDto> getInventoryTransferInfoByActionId(Integer actionId);

    /**
     * 保存库存转移单相关信息
     *
     * @param inventoryTransferRequest
     * @return
     * <AUTHOR>
     * @date 2020/7/24 16:45:23
     */
    void saveInventoryTransferRequest(InventoryTransferRequest inventoryTransferRequest);

}
