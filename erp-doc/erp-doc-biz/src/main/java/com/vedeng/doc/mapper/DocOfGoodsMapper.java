package com.vedeng.doc.mapper;
import java.util.List;

import com.vedeng.doc.domain.entity.DocOfGoods;
import org.apache.ibatis.annotations.Param;

/**
 *
 * <AUTHOR>
 * @Entity com.vedeng.doc.model.DocOfGoods
 */
public interface DocOfGoodsMapper{

    int deleteByPrimaryKey(Integer id);

    int insert(DocOfGoods record);

    int insertSelective(DocOfGoods record);

    DocOfGoods selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(DocOfGoods record);

    int updateByPrimaryKey(DocOfGoods record);

    /**
     * skuId查询skuName
     * @param skuId
     * @return
     */
    String selectCoreSkuByPrimaryKey(@Param("skuId") Integer skuId);

    /**
     * 根据条件查询
     * @param skuId sku
     * @param disable 是否禁用
     * @param isDelete 是否删除
     * @return
     */
    List<DocOfGoods> findBySkuIdAndDisableAndIsDelete(@Param("skuId")Integer skuId,@Param("disable")Integer disable,@Param("isDelete")Integer isDelete);


}




