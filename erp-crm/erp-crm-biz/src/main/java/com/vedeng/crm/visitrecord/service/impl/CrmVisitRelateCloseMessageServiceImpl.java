package com.vedeng.crm.visitrecord.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.enums.JumpErpTitleEnum;
import com.vedeng.crm.business.quote.domain.dto.SendMessageDto;
import com.vedeng.crm.visitrecord.domain.vo.VisitRecordVo;
import com.vedeng.crm.visitrecord.domain.vo.VisitTongxingUserVo;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.dto.TraderUserDto;
import com.vedeng.erp.trader.service.TraderCustomerBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/3/10
 */
@Service("CrmVisitRelateCloseMessageServiceImpl")
public class CrmVisitRelateCloseMessageServiceImpl extends BaseMessageSendServiceImpl {


    public void sendMessage(VisitRecordVo visitRecordVo, List<VisitTongxingUserVo> tongxingUserList, String closeUserName, String closeReason) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        //创建拼接消息
        StringBuilder messageBuilder = new StringBuilder();
        messageBuilder.append("拜访编号：").append(visitRecordVo.getVisitRecordNo()).append("\n");
        messageBuilder.append("拜访客户：").append(visitRecordVo.getCustomerName()).append("\n");
        messageBuilder.append("计划拜访时间：").append(DateUtil.format(visitRecordVo.getPlanVisitDate(),"yyyy-MM-dd") ).append("\n");
        messageBuilder.append("线索/商机编号：").append(visitRecordVo.getBussinessChanceNo()).append("\n");
        messageBuilder.append("线索/商机关闭人：").append(closeUserName).append("\n");
        messageBuilder.append("线索/商机关闭原因：").append(closeReason);
        String message = messageBuilder.toString();

        //处理消息需要发送的人员
        List<Integer> userIds = new ArrayList<>();
        userIds.add(visitRecordVo.getVisitorId());//拜访人
        if(CollectionUtil.isNotEmpty(tongxingUserList)){
            List<Integer> tongxingIdsList =
                    tongxingUserList.stream().map(VisitTongxingUserVo::getTongxingUserId).collect(Collectors.toList());
            userIds.addAll(tongxingIdsList);//同行人
        }
        userIds.add(visitRecordVo.getAddUserId());//创建人

        userIds.removeAll(Collections.singleton(currentUser.getId()));//消息的发送人，去掉当前的操作人，即创建人
        List<UserDto> userDtoSendList =  getUserInfoByUserIds(userIds);
        //获取这些人的工号
        List<String> visitJobNumList = CollectionUtil.isEmpty(userDtoSendList)?new ArrayList<>()
                :(userDtoSendList.stream().map(UserDto::getNumber).collect(Collectors.toList())) ;
        for(String jobNumber:visitJobNumList){
            SendMessageDto sendMessageDto = new SendMessageDto();
            sendMessageDto.setUrl(jumpService.getMjumpUrl(crmApplicationMessageJumpUrl + "/crm/visitRecord/m/detail?id=" + visitRecordVo.getId(),
                    lxcrmUrl + "/crm/visitRecord/profile/detail?id=" + visitRecordVo.getId(),
                    JumpErpTitleEnum.VISIT_RECORD_DETAIL));
            sendMessageDto.setUserNumber(jobNumber);
            sendMessageDto.setFormat(message);
            sendCardMsg(sendMessageDto, "拜访计划关联的线索/商机已关闭（不影响正常拜访）");
        }

    }
}
