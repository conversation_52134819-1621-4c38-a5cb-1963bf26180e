package com.vedeng.erp.saleorder.buzlogic.create.impl;

import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.order.model.Saleorder;
import com.vedeng.erp.saleorder.buzlogic.create.CreateSaleOrderBuzLogic;
import com.vedeng.erp.saleorder.constant.SalesOrderTypeEnum;
import com.vedeng.erp.saleorder.service.BaseSaleOrderService;
import com.vedeng.erp.saleorder.service.SaleOrderServiceFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class VsCreateSaleOrderBuzLogic extends CreateSaleOrderBuzLogic {

    public VsCreateSaleOrderBuzLogic(){
        List<String> sequence = new ArrayList<>();
        sequence.add("createOrder");
        sequence.add("syncSaleorderDate");
        super.setSequence(sequence);
    }

    @Override
    public ResultInfo run(Saleorder saleorder){
        ResultInfo result = new ResultInfo(0,"操作成功");
        new VsCreateSaleOrderBuzLogic();
        for (String methodName : getSequence()){
            switch (methodName) {
                case "createOrder":
                    if(ErpConst.ZERO.equals(result.getCode())) {
                        result = super.createOrder(saleorder);
                    }
                    break;
                case "syncSaleorderDate":
                    if(ErpConst.ZERO.equals(result.getCode())){
                        result = super.syncSaleorderDate(saleorder);
                    }
                    break;
                default:
                    break;
            }
        }
        return result;
    }

}
