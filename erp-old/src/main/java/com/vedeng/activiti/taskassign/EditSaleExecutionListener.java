package com.vedeng.activiti.taskassign;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.order.dao.RBuyorderSaleorderMapper;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderModifyApply;
import com.vedeng.order.model.SaleorderModifyApplyGoods;
import com.vedeng.order.service.SaleorderService;
import com.wms.service.util.GlobalThreadPool;
import com.wms.service.LogicalSaleorderChooseService;
import com.wms.service.processor.other.InventoryTransferProcessor;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

public class EditSaleExecutionListener implements ExecutionListener {
    private static final org.slf4j.Logger LOG = LoggerFactory.getLogger(EditSaleExecutionListener.class);
    // 运行时注入service
    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
    private SaleorderService saleorderService = (SaleorderService) context.getBean("saleorderService");
    private SaleorderMapper saleorderMapper = (SaleorderMapper) context.getBean("saleorderMapper");
    private RBuyorderSaleorderMapper rBuyorderSaleorderMapper = (RBuyorderSaleorderMapper) context.getBean("RBuyorderSaleorderMapper");
    private ActionProcdefService actionProcdefService = (ActionProcdefService) context.getBean("actionProcdefService");
    private SaleorderGoodsMapper saleorderGoodsMapper =(SaleorderGoodsMapper)context.getBean("saleorderGoodsMapper");
    private LogicalSaleorderChooseService logicalSaleorderChooseServiceImpl = (LogicalSaleorderChooseService) context.getBean("logicalSaleorderChooseService");
    private static final Logger logger = LoggerFactory.getLogger(InventoryTransferProcessor.class);

    //修改订单审核触发器
    //根据穿参通用回写主表中状态
    public void notify(DelegateExecution execution) throws Exception {
        ServletRequestAttributes ra= (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request =  ra.getRequest();
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        SaleorderModifyApply saleorderModifyApply = (SaleorderModifyApply) execution.getVariable("saleorderModifyApplyInfo");

        if(saleorderModifyApply == null){
            saleorderModifyApply = new SaleorderModifyApply();
            saleorderModifyApply.setSaleorderModifyApplyId((Integer) execution.getVariable("saleorderModifyApplyId"));
            saleorderModifyApply.setSaleorderId((Integer) execution.getVariable("orderId"));
        }

        String a = execution.getCurrentActivityName();
        SaleorderModifyApply saleorderModifyApplyInfo = saleorderService.getSaleorderModifyApplyInfo(saleorderModifyApply);

        if("审核完成".equals(execution.getCurrentActivityName())){
            ResultInfo<?> res = saleorderService.saveSaleorderModifyApplyToSaleorder(saleorderModifyApply);
        }else{
            actionProcdefService.updateInfo("T_SALEORDER", "SALEORDER_ID", saleorderModifyApply.getSaleorderId(), "LOCKED_STATUS", 0,2);
            saleorderService.updateUnlockSaleOrderWarning(saleorderModifyApply.getSaleorderId());
        }
        //iswmscancel为1时需要补偿wms出库单任务
        if(saleorderModifyApplyInfo.getIsWmsCancel() != null && saleorderModifyApplyInfo.getIsWmsCancel().equals(1)){
            GlobalThreadPool.submitMessage(new Runnable() {
                @Override
                public void run() {
                    try{
                        String uuid = UUID.randomUUID().toString().replace("-", "");
                        logger.info("开始执行补偿wms出库单任务，uuid：{}",uuid);
                        Thread.sleep(5000L);
                        Saleorder saleorder = new Saleorder();
                        saleorder.setSaleorderId((Integer) execution.getVariable("orderId"));
                        logicalSaleorderChooseServiceImpl.chooseLogicalSaleorder(saleorder,user);
                        logger.info("结束执行补偿wms出库单任务，uuid：{}",uuid);

                    }catch (Exception e){
                        logger.error("【notify】处理异常",e);
                    }
                }
            });

        }

		// 校验
		if ("审核完成".equals(execution.getCurrentActivityName())) {
			// 发送采购线通知
			SaleorderModifyApply data = saleorderMapper.getSaleorderModifyApplyBySaleorderModifyId(saleorderModifyApply.getSaleorderModifyApplyId());
			List<SaleorderModifyApplyGoods> modifyApplyList = saleorderMapper.getModifyApplyList(saleorderModifyApply.getSaleorderModifyApplyId());
			Saleorder saleOrderById = saleorderMapper.getSaleOrderById(data.getSaleorderId());
			Set<Integer> userIds = new HashSet<>();
			if (CollUtil.isNotEmpty(modifyApplyList)) {
				// 产品发货方式、内部备注发生变更时
				List<Integer> saleorderGoodsIds = modifyApplyList.stream()
						.filter(x -> {

                            if (StrUtil.isEmpty(x.getInsideComments())) {
                                x.setInsideComments("");
                            }

                            if (StrUtil.isEmpty(x.getOldInsideComments())) {
                                x.setOldInsideComments("");
                            }

                            return !x.getDeliveryDirect().equals(x.getOldDeliveryDirect()) || !x.getInsideComments().equals(x.getOldInsideComments());
                        })
						.map(SaleorderModifyApplyGoods::getSaleorderGoodsId).collect(Collectors.toList());
				// 根据销售订单产品ID查询采购单的创建人
				if (CollUtil.isNotEmpty(saleorderGoodsIds)) {
					List<Integer> userIdList = rBuyorderSaleorderMapper.getBuyorderUserBySaleorderGoodsIds(saleorderGoodsIds);
					if (CollUtil.isNotEmpty(userIdList)) {
						userIds.addAll(userIdList);
					}
				}

            }

            if (!data.getTakeTraderAddress().equals(data.getOldTakeTraderAddress()) || !data.getTakeTraderArea().equals(data.getOldTakeTraderArea())) {
                List<Integer> userList = rBuyorderSaleorderMapper.getBuyorderUserBySaleorderIdAndDirect(data.getSaleorderId());
                if (CollUtil.isNotEmpty(userList)) {
                    userIds.addAll(userList);
                }
            }

            if (CollUtil.isNotEmpty(userIds)) {
                // 发送消息
                Map<String,String> map = new HashMap<>();
                map.put("saleorderModifyApplyNo",data.getSaleorderModifyApplyNo());
                map.put("saleorderNo",saleOrderById.getSaleorderNo());
                MessageUtil.sendMessage2(310, CollUtil.newArrayList(userIds),map,"/orderstream/saleorder/viewModifyApply.do?saleorderModifyApplyId="+data.getSaleorderModifyApplyId()+"&saleorderId="+data.getSaleorderId(),data.getUsername());
            }

        }
    }
}
