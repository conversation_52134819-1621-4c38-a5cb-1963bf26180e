package com.vedeng.erp.kingdee.mapstruct;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeeSalesVatSpecialInvoiceEntity;
import com.vedeng.erp.kingdee.dto.KingDeeSalesVatSpecialInvoiceDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeSalesVatSpecialInvoiceDto;
import org.mapstruct.*;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.kingdee.mapstruct
 * @Date 2023/1/9 16:01
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, builder = @Builder(disableBuilder = true))
public interface KingDeeSalesVatSpecialInvoiceConvertor extends BaseMapStruct<KingDeeSalesVatSpecialInvoiceEntity, KingDeeSalesVatSpecialInvoiceDto> {
    /**
     * dto转entity
     *
     * @param dto KingDeeSalesVatSpecialInvoiceDto
     * @return KingDeeSalesVatSpecialInvoiceEntity
     */
    @Mapping(target = "fsalesicentry", source = "fsalesicentry", qualifiedByName = "fSalesEntryListToJsonArray")
    @Mapping(target = "FBillTypeId", source = "FBillTypeID")
    @Override
    KingDeeSalesVatSpecialInvoiceEntity toEntity(KingDeeSalesVatSpecialInvoiceDto dto);

    /**
     * entity转dto
     *
     * @param entity KingDeeSalesVatSpecialInvoiceEntity
     * @return KingDeeSalesVatSpecialInvoiceDto
     */
    @Mapping(target = "fsalesicentry", source = "fsalesicentry", qualifiedByName = "fSalesEntryJsonArrayToList")
    @Override
    KingDeeSalesVatSpecialInvoiceDto toDto(KingDeeSalesVatSpecialInvoiceEntity entity);

    /**
     * entity 中JSONArray 转 原对象
     *
     * @param jsonArray JSONArray
     * @return List<KingDeeSalesVatSpecialInvoiceDetailDto> dto中的对象
     */
    @Named("fSalesEntryJsonArrayToList")
    default List<KingDeeSalesVatSpecialInvoiceDetailDto> entryJsonArrayToList(JSONArray jsonArray) {
        if (CollUtil.isEmpty(jsonArray)) {
            return Collections.emptyList();
        }
        return jsonArray.toJavaList(KingDeeSalesVatSpecialInvoiceDetailDto.class);
    }

    /**
     * dto 原对象中 转 JSONArray
     *
     * @param source 对象
     * @return JSONArray JSONArray
     */
    @Named("fSalesEntryListToJsonArray")
    default JSONArray entryListToJsonArray(List<KingDeeSalesVatSpecialInvoiceDetailDto> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSONArray.parseArray(JSON.toJSONString(source));
    }

}
