package com.vedeng.common.core.exception;

import com.vedeng.common.core.base.BaseException;
import com.vedeng.common.core.base.BaseResponseCode;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/3/19
 */
public class ClientMustBeWeixinException extends BaseException {

    public ClientMustBeWeixinException(BaseResponseCode code) {
        super(code);
    }

    public ClientMustBeWeixinException(String message) {
        super(message);
    }

    public ClientMustBeWeixinException(BaseResponseCode status, Object data) {
        super(status, data);
    }

    public ClientMustBeWeixinException(Integer code, String message) {
        super(code, message);
    }

    public ClientMustBeWeixinException(Integer code, String message, Object data) {
        super(code, message, data);
    }

    public ClientMustBeWeixinException(String message, Throwable cause) {
        super(message, cause);
    }
}