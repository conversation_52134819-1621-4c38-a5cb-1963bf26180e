package com.vedeng.erp.finance.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.aftersale.dto.AfterSalesDto;
import com.vedeng.erp.aftersale.service.AfterSalesApiService;
import com.vedeng.erp.finance.constants.FinanceConstant;
import com.vedeng.erp.finance.domain.entity.AfterSalesInvoiceEntity;
import com.vedeng.erp.finance.dto.InvoiceDto;
import com.vedeng.erp.finance.mapper.AfterSalesInvoiceMapper;
import com.vedeng.erp.finance.service.AfterSalesInvoiceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AfterSalesInvoiceServiceImpl implements AfterSalesInvoiceService {

    @Autowired
    private AfterSalesInvoiceMapper afterSalesInvoiceMapper;
    @Autowired
    private AfterSalesApiService afterSalesApiService;

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void saveAfterSaleInvoice(InvoiceDto invoice) {
        log.info("发票:{}开始关联售后单", invoice.getInvoiceId());
        List<AfterSalesDto> saleOrderAfterSale = afterSalesApiService.findByOrderIdGetSaleOrderAfterSale(invoice.getRelatedId());
        log.info("发票:{}已存在售后单数量:{} ", invoice.getInvoiceId(), saleOrderAfterSale.size());
        if (CollUtil.isEmpty(saleOrderAfterSale)) {
            return;
        }
        saleOrderAfterSale.stream()
                .filter(a -> a.getAtferSalesStatus().equals(FinanceConstant.WAIT_CONFIRM) ||
                        a.getAtferSalesStatus().equals(FinanceConstant.IN_PROGRESS))
                .forEach(a -> {
                    log.info("售后单:{}开始关联发票信息", a.getAfterSalesNo());
                    AfterSalesInvoiceEntity afterSalesInvoice = new AfterSalesInvoiceEntity();
                    afterSalesInvoice.setInvoiceId(invoice.getInvoiceId());
                    afterSalesInvoice.setAfterSalesId(a.getAfterSalesId());
                    afterSalesInvoice.setIsRefundInvoice(ErpConstant.T);
                    afterSalesInvoiceMapper.insertSelective(afterSalesInvoice);
                    log.info("售后单:{}开始关联发票信息成功", a.getAfterSalesNo());
                });
    }
}
