package com.vedeng.erp.broadcast.mapdao;

import com.vedeng.erp.broadcast.domain.dto.BroadCastUserDetailDto;
import com.vedeng.erp.broadcast.domain.dto.BroadCastUserListDto;
import com.vedeng.erp.broadcast.domain.entity.BroadcastRAedUserEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * AED区域经理与ERP用户映射Mapper
 */
public interface BroadcastRAedUserMapper {

    /**
     * 根据主键删除记录
     *
     * @param id 主键
     * @return 删除记录数
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 插入记录
     *
     * @param record 记录
     * @return 插入记录数
     */
    int insert(BroadcastRAedUserEntity record);

    /**
     * 选择性插入记录
     *
     * @param record 记录
     * @return 插入记录数
     */
    int insertSelective(BroadcastRAedUserEntity record);

    /**
     * 根据主键查询记录
     *
     * @param id 主键
     * @return 记录
     */
    BroadcastRAedUserEntity selectByPrimaryKey(Integer id);

    /**
     * 选择性更新记录
     *
     * @param record 记录
     * @return 更新记录数
     */
    int updateByPrimaryKeySelective(BroadcastRAedUserEntity record);

    /**
     * 根据主键更新记录
     *
     * @param record 记录
     * @return 更新记录数
     */
    int updateByPrimaryKey(BroadcastRAedUserEntity record);

    /**
     * 根据ERP用户ID查询记录
     * 只查询未删除的记录
     *
     * @param erpUserId ERP用户ID
     * @return 记录，如果不存在则返回null
     */
    BroadcastRAedUserEntity selectByErpUserId(@Param("erpUserId") Integer erpUserId);

    List<BroadCastUserListDto> selectBroadcastUserListPage(@Param("username") String username,@Param("aedUserId") Integer aedUserId);

    List<BroadCastUserDetailDto.BroadCastAedUserDto> selectAllAedUserList(@Param("username") String username);
    
    /**
     * 获取个人的AED销售
     * @param queryId
     * @return
     */
    String selectAedSingleByErpUserId(@Param("queryId") Integer queryId);
    
    /**
     * 获取小组的AED销售
     * @param queryId
     * @return
     */
    String selectAedTeamByErpUserId(@Param("queryId") Integer queryId);
    
    /**
     * 获取部门的AED销售
     * @param queryId
     * @return
     */
    String selectAedDeptByErpUserId(@Param("queryId") Integer queryId);
}
