<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.goods.dao.BaseCategoryMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.goods.model.BaseCategory" >
    <!--          -->
    <id column="BASE_CATEGORY_ID" property="baseCategoryId" jdbcType="INTEGER" />
    <result column="BASE_CATEGORY_NAME" property="baseCategoryName" jdbcType="VARCHAR" />
    <result column="BASE_CATEGORY_NICKNAME" property="baseCategoryNickname" jdbcType="VARCHAR" />
    <result column="BASE_CATEGORY_LEVEL" property="baseCategoryLevel" jdbcType="BIT" />
    <result column="BASE_CATEGORY_TYPE" property="baseCategoryType" jdbcType="BIT" />
    <result column="BASE_CATEGORY_EXAMPLE_PRODUCT" property="baseCategoryExampleProduct" jdbcType="VARCHAR" />
    <result column="BASE_CATEGORY_DESCRIBE" property="baseCategoryDescribe" jdbcType="VARCHAR" />
    <result column="BASE_CATEGORY_INTENDED_USE" property="baseCategoryIntendedUse" jdbcType="VARCHAR" />
    <result column="IS_DELETED" property="isDeleted" jdbcType="BIT" />
    <result column="PARENT_ID" property="parentId" jdbcType="INTEGER" />
    <result column="TREENODES" property="treenodes" jdbcType="VARCHAR" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="TIMESTAMP" />
    <result column="ADD_TIME" property="addTime" jdbcType="TIMESTAMP" />
      <result column="TAX_CLASSIFICATION_CODE" property="taxClassificationCode" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="BaseResultVoMap" type="com.vedeng.goods.model.vo.BaseCategoryVo" extends="BaseResultMap">
    <result column="CORE_PRODUCT_NUM" property="coreProductNum" jdbcType="INTEGER" />
    <result column="TEMPORARY_PRODUCT_NUM" property="temporaryProductNum" jdbcType="INTEGER" />
    <result column="OTHER_PRODUCT_NUM" property="otherProductNum" jdbcType="INTEGER" />
    <result column="CATEGORY_JOIN_NAME" property="categoryJoinName" jdbcType="VARCHAR" />
    <result column="ATTRIBUTE_JOIN_NAME" property="attributeJoinName" jdbcType="VARCHAR" />
    <result column="FIRST_LEVEL_CATEGORY_NAME" property="firstLevelCategoryName" jdbcType="VARCHAR" />
    <result column="SECOND_LEVEL_CATEGORY_NAME" property="secondLevelCategoryName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--          -->
    BASE_CATEGORY_ID, BASE_CATEGORY_NAME, BASE_CATEGORY_NICKNAME, BASE_CATEGORY_LEVEL, 
    BASE_CATEGORY_TYPE, BASE_CATEGORY_EXAMPLE_PRODUCT, BASE_CATEGORY_DESCRIBE, BASE_CATEGORY_INTENDED_USE,
    IS_DELETED, PARENT_ID, TREENODES, CREATOR, UPDATER, MOD_TIME, ADD_TIME,TAX_CLASSIFICATION_CODE
  </sql>
  <select id="getCategoryListByParentId" resultMap="BaseResultVoMap" parameterType="java.lang.Integer" >
    <!--          -->
    select
      BASE_CATEGORY_ID, BASE_CATEGORY_NAME,BASE_CATEGORY_LEVEL,PARENT_ID
    from V_BASE_CATEGORY
    where PARENT_ID = #{parentId,jdbcType=INTEGER} and IS_DELETED=0
  </select>
    <select id="getCategoryLevelAllListPage" parameterType="java.util.Map" resultType="com.vedeng.goods.model.vo.CategoryToOpVo">
        SELECT C3.BASE_CATEGORY_ID,C3.BASE_CATEGORY_NAME,
         C2.BASE_CATEGORY_ID AS SECOND_CATEGORY_ID,C2.BASE_CATEGORY_NAME AS SECOND_CATEGORY_NAME,
         C1.BASE_CATEGORY_ID AS FIRST_CATEGORY_ID,C1.BASE_CATEGORY_NAME AS FIRST_CATEGORY_NAME
         FROM V_BASE_CATEGORY C3
        LEFT JOIN V_BASE_CATEGORY C2 ON C3.PARENT_ID=C2.BASE_CATEGORY_ID
        LEFT JOIN V_BASE_CATEGORY C1 ON C2.PARENT_ID=C1.BASE_CATEGORY_ID
        WHERE C3.BASE_CATEGORY_LEVEL=3 AND C3.IS_DELETED=0
    </select>

    <select id="getCategoryLevelAllById" resultType="com.vedeng.goods.model.vo.CategoryToOpVo">
        SELECT C3.BASE_CATEGORY_ID,C3.BASE_CATEGORY_NAME,
         C2.BASE_CATEGORY_ID AS SECOND_CATEGORY_ID,C2.BASE_CATEGORY_NAME AS SECOND_CATEGORY_NAME,
         C1.BASE_CATEGORY_ID AS FIRST_CATEGORY_ID,C1.BASE_CATEGORY_NAME AS FIRST_CATEGORY_NAME
         FROM V_BASE_CATEGORY C3
        LEFT JOIN V_BASE_CATEGORY C2 ON C3.PARENT_ID=C2.BASE_CATEGORY_ID
        LEFT JOIN V_BASE_CATEGORY C1 ON C2.PARENT_ID=C1.BASE_CATEGORY_ID
        WHERE C3.BASE_CATEGORY_LEVEL=3 AND C3.IS_DELETED=0 AND C3.BASE_CATEGORY_ID=#{categoryId}
    </select>

    <select id="getCategoryLevelAllByIdList" resultType="com.vedeng.goods.model.vo.CategoryToOpVo">
        SELECT C3.BASE_CATEGORY_ID,C3.BASE_CATEGORY_NAME,
         C2.BASE_CATEGORY_ID AS SECOND_CATEGORY_ID,C2.BASE_CATEGORY_NAME AS SECOND_CATEGORY_NAME,
         C1.BASE_CATEGORY_ID AS FIRST_CATEGORY_ID,C1.BASE_CATEGORY_NAME AS FIRST_CATEGORY_NAME
         FROM V_BASE_CATEGORY C3
        LEFT JOIN V_BASE_CATEGORY C2 ON C3.PARENT_ID=C2.BASE_CATEGORY_ID
        LEFT JOIN V_BASE_CATEGORY C1 ON C2.PARENT_ID=C1.BASE_CATEGORY_ID
        WHERE C3.BASE_CATEGORY_LEVEL=3 AND C3.IS_DELETED=0 AND C3.BASE_CATEGORY_ID IN
        <foreach collection="list" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>


    <select id="selectByPrimaryKey" resultMap="BaseResultVoMap" parameterType="java.lang.Integer" >
        <!--          -->
        select
        <include refid="Base_Column_List" />
        from V_BASE_CATEGORY
        where BASE_CATEGORY_ID = #{baseCategoryId,jdbcType=INTEGER}
    </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--          -->
    delete from V_BASE_CATEGORY
    where BASE_CATEGORY_ID = #{baseCategoryId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.goods.model.BaseCategory" >
    <!--          -->
    insert into V_BASE_CATEGORY (BASE_CATEGORY_ID, BASE_CATEGORY_NAME, 
      BASE_CATEGORY_NICKNAME, BASE_CATEGORY_LEVEL, BASE_CATEGORY_TYPE,
      BASE_CATEGORY_EXAMPLE_PRODUCT, BASE_CATEGORY_DESCRIBE,
      BASE_CATEGORY_INTENDED_USE, IS_DELETED, PARENT_ID, 
      TREENODES, CREATOR, UPDATER, 
      MOD_TIME, ADD_TIME,TAX_CLASSIFICATION_CODE)
    values (#{baseCategoryId,jdbcType=INTEGER}, #{baseCategoryName,jdbcType=VARCHAR}, 
      #{baseCategoryNickname,jdbcType=VARCHAR}, #{baseCategoryLevel,jdbcType=BIT}, #{baseCategoryType,jdbcType=BIT}, 
      #{baseCategoryExampleProduct,jdbcType=VARCHAR}, #{baseCategoryDescribe,jdbcType=VARCHAR},
      #{baseCategoryIntendedUse,jdbcType=VARCHAR}, #{isDeleted,jdbcType=BIT}, #{parentId,jdbcType=INTEGER}, 
      #{treenodes,jdbcType=VARCHAR}, #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, 
      #{modTime,jdbcType=TIMESTAMP}, #{addTime,jdbcType=TIMESTAMP},#{taxClassificationCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.goods.model.vo.BaseCategoryVo" useGeneratedKeys="true" keyProperty="baseCategoryId">
    <!--          -->
    insert into V_BASE_CATEGORY
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="baseCategoryId != null" >
        BASE_CATEGORY_ID,
      </if>
      <if test="baseCategoryName != null" >
        BASE_CATEGORY_NAME,
      </if>
      <if test="baseCategoryNickname != null" >
        BASE_CATEGORY_NICKNAME,
      </if>
      <if test="baseCategoryLevel != null" >
        BASE_CATEGORY_LEVEL,
      </if>
      <if test="baseCategoryType != null" >
        BASE_CATEGORY_TYPE,
      </if>
      <if test="baseCategoryExampleProduct != null" >
        BASE_CATEGORY_EXAMPLE_PRODUCT,
      </if>
      <if test="baseCategoryDescribe != null" >
        BASE_CATEGORY_DESCRIBE,
      </if>
      <if test="baseCategoryIntendedUse != null" >
        BASE_CATEGORY_INTENDED_USE,
      </if>
      <if test="isDeleted != null" >
        IS_DELETED,
      </if>
      <if test="parentId != null" >
        PARENT_ID,
      </if>
      <if test="treenodes != null" >
        TREENODES,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="taxClassificationCode != null" >
        TAX_CLASSIFICATION_CODE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="baseCategoryId != null" >
        #{baseCategoryId,jdbcType=INTEGER},
      </if>
      <if test="baseCategoryName != null" >
        #{baseCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="baseCategoryNickname != null" >
        #{baseCategoryNickname,jdbcType=VARCHAR},
      </if>
      <if test="baseCategoryLevel != null" >
        #{baseCategoryLevel,jdbcType=BIT},
      </if>
      <if test="baseCategoryType != null" >
        #{baseCategoryType,jdbcType=BIT},
      </if>
      <if test="baseCategoryExampleProduct != null" >
        #{baseCategoryExampleProduct,jdbcType=VARCHAR},
      </if>
      <if test="baseCategoryDescribe != null" >
        #{baseCategoryDescribe,jdbcType=VARCHAR},
      </if>
      <if test="baseCategoryIntendedUse != null" >
        #{baseCategoryIntendedUse,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null" >
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="parentId != null" >
        #{parentId,jdbcType=INTEGER},
      </if>
      <if test="treenodes != null" >
        #{treenodes,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taxClassificationCode != null" >
        #{taxClassificationCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.model.BaseCategory" >
    <!--          -->
    update V_BASE_CATEGORY
    <set >
      <if test="baseCategoryName != null" >
        BASE_CATEGORY_NAME = #{baseCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="baseCategoryNickname != null" >
        BASE_CATEGORY_NICKNAME = #{baseCategoryNickname,jdbcType=VARCHAR},
      </if>
      <if test="baseCategoryLevel != null" >
        BASE_CATEGORY_LEVEL = #{baseCategoryLevel,jdbcType=BIT},
      </if>
      <if test="baseCategoryType != null" >
        BASE_CATEGORY_TYPE = #{baseCategoryType,jdbcType=BIT},
      </if>
      <if test="baseCategoryExampleProduct != null" >
        BASE_CATEGORY_EXAMPLE_PRODUCT = #{baseCategoryExampleProduct,jdbcType=VARCHAR},
      </if>
      <if test="baseCategoryDescribe != null" >
        BASE_CATEGORY_DESCRIBE = #{baseCategoryDescribe,jdbcType=VARCHAR},
      </if>
      <if test="baseCategoryIntendedUse != null" >
        BASE_CATEGORY_INTENDED_USE = #{baseCategoryIntendedUse,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null" >
        IS_DELETED = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="parentId != null" >
        PARENT_ID = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="treenodes != null" >
        TREENODES = #{treenodes,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
        <if test="taxClassificationCode != null" >
        TAX_CLASSIFICATION_CODE = #{taxClassificationCode,jdbcType=VARCHAR},
      </if>
    </set>
    where BASE_CATEGORY_ID = #{baseCategoryId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.model.BaseCategory" >
    <!--          -->
    update V_BASE_CATEGORY
    set BASE_CATEGORY_NAME = #{baseCategoryName,jdbcType=VARCHAR},
      BASE_CATEGORY_NICKNAME = #{baseCategoryNickname,jdbcType=VARCHAR},
      BASE_CATEGORY_LEVEL = #{baseCategoryLevel,jdbcType=BIT},
      BASE_CATEGORY_TYPE = #{baseCategoryType,jdbcType=BIT},
      BASE_CATEGORY_EXAMPLE_PRODUCT = #{baseCategoryExampleProduct,jdbcType=VARCHAR},
      BASE_CATEGORY_DESCRIBE = #{baseCategoryDescribe,jdbcType=VARCHAR},
      BASE_CATEGORY_INTENDED_USE = #{baseCategoryIntendedUse,jdbcType=VARCHAR},
      IS_DELETED = #{isDeleted,jdbcType=BIT},
      PARENT_ID = #{parentId,jdbcType=INTEGER},
      TREENODES = #{treenodes,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      TAX_CLASSIFICATION_CODE = #{taxClassificationCode,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP}
    where BASE_CATEGORY_ID = #{baseCategoryId,jdbcType=INTEGER}
  </update>
  <select id="getFirstCategoryListPage" parameterType="java.util.Map" resultMap="BaseResultVoMap">
    SELECT
        DISTINCT  a.BASE_CATEGORY_ID,
                  a.BASE_CATEGORY_NAME,
                  a.BASE_CATEGORY_TYPE,
                  a.BASE_CATEGORY_LEVEL,
                  a.PARENT_ID,
                  a.TREENODES
    FROM
        V_BASE_CATEGORY a
    LEFT JOIN V_BASE_CATEGORY d ON d.PARENT_ID = a.BASE_CATEGORY_ID AND d.IS_DELETED = #{baseCategoryVo.isDeleted, jdbcType=INTEGER}
    LEFT JOIN V_BASE_CATEGORY e ON e.PARENT_ID = d.BASE_CATEGORY_ID AND e.IS_DELETED = #{baseCategoryVo.isDeleted, jdbcType=INTEGER}
    WHERE
        a.IS_DELETED = #{baseCategoryVo.isDeleted, jdbcType=INTEGER}
        AND a.BASE_CATEGORY_LEVEL = #{baseCategoryVo.baseCategoryLevel, jdbcType=INTEGER}
    <if test="baseCategoryVo.baseCategoryName != null and baseCategoryVo.baseCategoryName != ''">
          AND (
            a.BASE_CATEGORY_NAME LIKE CONCAT('%',#{baseCategoryVo.baseCategoryName,jdbcType=VARCHAR},'%')
            OR d.BASE_CATEGORY_NAME LIKE CONCAT('%',#{baseCategoryVo.baseCategoryName,jdbcType=VARCHAR},'%')
            OR e.BASE_CATEGORY_NAME LIKE CONCAT('%',#{baseCategoryVo.baseCategoryName,jdbcType=VARCHAR},'%')
          )
    </if>
    <if test="baseCategoryVo.baseCategoryType != null">
        AND e.BASE_CATEGORY_TYPE = #{baseCategoryVo.baseCategoryType,jdbcType=BIT}
    </if>
    ORDER BY
      a.ADD_TIME DESC
  </select>
  <select id="getSecondCategoryList" parameterType="java.util.Map" resultMap="BaseResultVoMap">
      SELECT
          DISTINCT d.BASE_CATEGORY_ID,
          d.BASE_CATEGORY_NAME,
          d.BASE_CATEGORY_TYPE,
          d.BASE_CATEGORY_LEVEL,
          d.PARENT_ID,
          d.TREENODES
      FROM
        V_BASE_CATEGORY a
      LEFT JOIN V_BASE_CATEGORY d ON d.PARENT_ID = a.BASE_CATEGORY_ID  AND d.IS_DELETED = #{baseCategoryVo.isDeleted, jdbcType=INTEGER}
      LEFT JOIN V_BASE_CATEGORY e ON e.PARENT_ID = d.BASE_CATEGORY_ID AND e.IS_DELETED = #{baseCategoryVo.isDeleted, jdbcType=INTEGER}
      WHERE
          a.IS_DELETED = #{baseCategoryVo.isDeleted, jdbcType=INTEGER}
        AND d.BASE_CATEGORY_LEVEL = #{baseCategoryVo.baseCategoryLevel, jdbcType=INTEGER}
      <if test="baseCategoryVo.baseCategoryName != null and baseCategoryVo.baseCategoryName != ''">
        AND (
            d.BASE_CATEGORY_NAME LIKE CONCAT('%',#{baseCategoryVo.baseCategoryName,jdbcType=VARCHAR},'%')
            OR e.BASE_CATEGORY_NAME LIKE CONCAT('%',#{baseCategoryVo.baseCategoryName,jdbcType=VARCHAR},'%')
        )
      </if>
      <if test="baseCategoryVo.baseCategoryType != null">
        AND e.BASE_CATEGORY_TYPE = #{baseCategoryVo.baseCategoryType,jdbcType=BIT}
      </if>
      ORDER BY
        d.ADD_TIME DESC
  </select>

    <select id="getSecondCategoryListForNameQuery" parameterType="java.util.Map" resultMap="BaseResultVoMap">
        SELECT
        DISTINCT d.BASE_CATEGORY_ID,
        d.BASE_CATEGORY_NAME,
        d.BASE_CATEGORY_TYPE,
        d.BASE_CATEGORY_LEVEL,
        d.PARENT_ID,
        d.TREENODES
        FROM
        V_BASE_CATEGORY a
        LEFT JOIN V_BASE_CATEGORY d ON d.PARENT_ID = a.BASE_CATEGORY_ID  AND d.IS_DELETED = #{baseCategoryVo.isDeleted, jdbcType=INTEGER}
        LEFT JOIN V_BASE_CATEGORY e ON e.PARENT_ID = d.BASE_CATEGORY_ID AND e.IS_DELETED = #{baseCategoryVo.isDeleted, jdbcType=INTEGER}
        WHERE
        a.IS_DELETED = #{baseCategoryVo.isDeleted, jdbcType=INTEGER}
        AND d.BASE_CATEGORY_LEVEL = #{baseCategoryVo.baseCategoryLevel, jdbcType=INTEGER}
        <if test="baseCategoryVo.baseCategoryName != null and baseCategoryVo.baseCategoryName != ''">
            AND (
            a.BASE_CATEGORY_NAME LIKE CONCAT('%',#{baseCategoryVo.baseCategoryName,jdbcType=VARCHAR},'%')
            OR d.BASE_CATEGORY_NAME LIKE CONCAT('%',#{baseCategoryVo.baseCategoryName,jdbcType=VARCHAR},'%')
            OR e.BASE_CATEGORY_NAME LIKE CONCAT('%',#{baseCategoryVo.baseCategoryName,jdbcType=VARCHAR},'%')
            )
        </if>
        <if test="baseCategoryVo.baseCategoryType != null">
            AND e.BASE_CATEGORY_TYPE = #{baseCategoryVo.baseCategoryType,jdbcType=BIT}
        </if>
        ORDER BY
        d.ADD_TIME DESC
    </select>
  <select id="getThirdCategoryList" parameterType="java.util.Map" resultMap="BaseResultVoMap">
      SELECT
          x.BASE_CATEGORY_ID,
          x.BASE_CATEGORY_NAME,
          x.BASE_CATEGORY_TYPE,
          x.BASE_CATEGORY_LEVEL,
          x.PARENT_ID,
          x.TREENODES,
          SUM(IF(x.SPU_LEVEL = 1, x.NUM, 0)) AS CORE_PRODUCT_NUM,
          SUM(IF(x.SPU_LEVEL = 2, x.NUM, 0)) AS TEMPORARY_PRODUCT_NUM,
          SUM(IF(x.SPU_LEVEL = 3, x.NUM, 0)) AS OTHER_PRODUCT_NUM
      FROM
      (
          SELECT
              a.BASE_CATEGORY_ID,
              a.BASE_CATEGORY_NAME,
              a.BASE_CATEGORY_TYPE,
              a.BASE_CATEGORY_LEVEL,
              a.PARENT_ID,
              a.TREENODES,
              b.SPU_LEVEL,
              COUNT(c.SKU_ID) AS NUM
          FROM
              V_BASE_CATEGORY a
          LEFT JOIN V_CORE_SPU b ON a.BASE_CATEGORY_ID = b.CATEGORY_ID AND b.CHECK_STATUS != 4
                AND b. STATUS = 1 AND b.SPU_LEVEL IS NOT NULL
          LEFT JOIN V_CORE_SKU c ON b.SPU_ID = c.SPU_ID AND c.SKU_ID IS NOT NULL
                AND c.CHECK_STATUS != 4
                AND c. STATUS = 1
          WHERE
            a.IS_DELETED = #{baseCategoryVo.isDeleted, jdbcType=INTEGER}
          AND a.BASE_CATEGORY_LEVEL = #{baseCategoryVo.baseCategoryLevel, jdbcType=INTEGER}
          <if test="baseCategoryVo.baseCategoryName != null and baseCategoryVo.baseCategoryName != ''">
              AND a.BASE_CATEGORY_NAME LIKE CONCAT('%',#{baseCategoryVo.baseCategoryName,jdbcType=VARCHAR},'%')
          </if>
          <if test="baseCategoryVo.baseCategoryType != null">
              AND a.BASE_CATEGORY_TYPE = #{baseCategoryVo.baseCategoryType,jdbcType=BIT}
          </if>
          GROUP BY
              a.BASE_CATEGORY_ID,
              a.BASE_CATEGORY_NAME,
              a.BASE_CATEGORY_TYPE,
              a.BASE_CATEGORY_LEVEL,
              a.PARENT_ID,
              a.TREENODES,
              b.SPU_LEVEL
          ORDER BY
            a.ADD_TIME DESC
      ) x
      GROUP BY
          x.BASE_CATEGORY_ID,
          x.BASE_CATEGORY_NAME,
          x.BASE_CATEGORY_TYPE,
          x.BASE_CATEGORY_LEVEL,
          x.PARENT_ID,
          x.TREENODES
  </select>

    <select id="getThirdCategoryListForNameQuery" parameterType="java.util.Map" resultMap="BaseResultVoMap">
        SELECT
        x.BASE_CATEGORY_ID,
        x.BASE_CATEGORY_NAME,
        x.BASE_CATEGORY_TYPE,
        x.BASE_CATEGORY_LEVEL,
        x.PARENT_ID,
        x.TREENODES,
        SUM(IF(x.SPU_LEVEL = 1, x.NUM, 0)) AS CORE_PRODUCT_NUM,
        SUM(IF(x.SPU_LEVEL = 2, x.NUM, 0)) AS TEMPORARY_PRODUCT_NUM,
        SUM(IF(x.SPU_LEVEL = 0, x.NUM, 0)) AS OTHER_PRODUCT_NUM
        FROM
        (
        SELECT
        a.BASE_CATEGORY_ID,
        a.BASE_CATEGORY_NAME,
        a.BASE_CATEGORY_TYPE,
        a.BASE_CATEGORY_LEVEL,
        a.PARENT_ID,
        a.TREENODES,
        b.SPU_LEVEL,
        COUNT(c.SKU_ID) AS NUM
        FROM
        V_BASE_CATEGORY a
        LEFT JOIN V_BASE_CATEGORY d ON a.PARENT_ID = d.BASE_CATEGORY_ID  AND d.IS_DELETED = #{baseCategoryVo.isDeleted, jdbcType=INTEGER}
        LEFT JOIN V_BASE_CATEGORY e ON d.PARENT_ID = e.BASE_CATEGORY_ID AND e.IS_DELETED = #{baseCategoryVo.isDeleted, jdbcType=INTEGER}
        LEFT JOIN V_CORE_SPU b ON a.BASE_CATEGORY_ID = b.CATEGORY_ID AND b.CHECK_STATUS != 4
        AND b. STATUS = 1 AND b.SPU_LEVEL IS NOT NULL
        LEFT JOIN V_CORE_SKU c ON b.SPU_ID = c.SPU_ID AND c.SKU_ID IS NOT NULL
        AND c.CHECK_STATUS != 4
        AND c. STATUS = 1
        WHERE
        a.IS_DELETED = #{baseCategoryVo.isDeleted, jdbcType=INTEGER}
        AND a.BASE_CATEGORY_LEVEL = #{baseCategoryVo.baseCategoryLevel, jdbcType=INTEGER}
        <if test="baseCategoryVo.baseCategoryName != null and baseCategoryVo.baseCategoryName != ''">
            AND  (
            a.BASE_CATEGORY_NAME LIKE CONCAT('%',#{baseCategoryVo.baseCategoryName,jdbcType=VARCHAR},'%')
            OR d.BASE_CATEGORY_NAME LIKE CONCAT('%',#{baseCategoryVo.baseCategoryName,jdbcType=VARCHAR},'%')
            OR e.BASE_CATEGORY_NAME LIKE CONCAT('%',#{baseCategoryVo.baseCategoryName,jdbcType=VARCHAR},'%')
            )
        </if>
        <if test="baseCategoryVo.baseCategoryType != null">
            AND a.BASE_CATEGORY_TYPE = #{baseCategoryVo.baseCategoryType,jdbcType=BIT}
        </if>
        GROUP BY
        a.BASE_CATEGORY_ID,
        a.BASE_CATEGORY_NAME,
        a.BASE_CATEGORY_TYPE,
        a.BASE_CATEGORY_LEVEL,
        a.PARENT_ID,
        a.TREENODES,
        b.SPU_LEVEL
        ORDER BY
        a.ADD_TIME DESC
        ) x
        GROUP BY
        x.BASE_CATEGORY_ID,
        x.BASE_CATEGORY_NAME,
        x.BASE_CATEGORY_TYPE,
        x.BASE_CATEGORY_LEVEL,
        x.PARENT_ID,
        x.TREENODES
    </select>
  <select id="getBaseCategoryListPage" parameterType="java.util.Map" resultMap="BaseResultVoMap">
    SELECT
        DISTINCT
        a.BASE_CATEGORY_ID,
        CONCAT(
            d.BASE_CATEGORY_NAME,
            '-',
            c.BASE_CATEGORY_NAME,
            '-',
            b.BASE_CATEGORY_NAME
        ) AS CATEGORY_JOIN_NAME
    FROM
        V_CATEGORY_ATTR_VALUE_MAPPING a
    LEFT JOIN V_BASE_CATEGORY b ON a.BASE_CATEGORY_ID = b.BASE_CATEGORY_ID
    LEFT JOIN V_BASE_CATEGORY c ON b.PARENT_ID = c.BASE_CATEGORY_ID
    LEFT JOIN V_BASE_CATEGORY d ON c.PARENT_ID = d.BASE_CATEGORY_ID
    WHERE
        a.IS_DELETED = #{isDeleted, jdbcType=INTEGER}
    AND a.BASE_ATTRIBUTE_ID = #{attrId, jdbcType=INTEGER}
    ORDER BY
        a.ADD_TIME DESC
  </select>


  <select id="getBaseCategoryList" parameterType="java.lang.Integer" resultType="java.util.HashMap">
        SELECT
            a.BASE_CATEGORY_ID as thirdCategoryId,
            b.BASE_CATEGORY_ID as secondCategoryId,
            c.BASE_CATEGORY_ID as firstCategoryId
        FROM
            V_BASE_CATEGORY a
        LEFT JOIN V_BASE_CATEGORY b ON a.PARENT_ID = b.BASE_CATEGORY_ID
        LEFT JOIN V_BASE_CATEGORY c ON b.PARENT_ID = c.BASE_CATEGORY_ID
        WHERE
            a.BASE_CATEGORY_ID = #{baseCategoryId,jdbcType=INTEGER}
   </select>

  <select id="getCategoryListByIds" parameterType="java.util.Map" resultMap="BaseResultVoMap">
      SELECT
          x.BASE_CATEGORY_ID,
          x.BASE_CATEGORY_NAME,
          x.BASE_CATEGORY_TYPE,
          x.BASE_CATEGORY_LEVEL,
          x.PARENT_ID,
          x.TREENODES
      <if test="level == 3">
          ,
          SUM(IF(x.SPU_LEVEL = 1, x.NUM, 0)) AS CORE_PRODUCT_NUM,
          SUM(IF(x.SPU_LEVEL = 2, x.NUM, 0)) AS TEMPORARY_PRODUCT_NUM,
          SUM(IF(x.SPU_LEVEL = 3, x.NUM, 0)) AS OTHER_PRODUCT_NUM
      </if>
      FROM
      (
        SELECT
          a.BASE_CATEGORY_ID,
          a.BASE_CATEGORY_NAME,
          a.BASE_CATEGORY_TYPE,
          a.BASE_CATEGORY_LEVEL,
          a.PARENT_ID,
          a.TREENODES
      <if test="level == 3">
          ,
          b.SPU_LEVEL,
          COUNT(c.SKU_ID) AS NUM
      </if>
      FROM
        V_BASE_CATEGORY a
      <if test="level == 3">
          LEFT JOIN V_CORE_SPU b ON a.BASE_CATEGORY_ID = b.CATEGORY_ID AND b.CHECK_STATUS != 4
              AND b. STATUS = 1 AND b.SPU_LEVEL IS NOT NULL
          LEFT JOIN V_CORE_SKU c ON b.SPU_ID = c.SPU_ID  AND c.SKU_ID IS NOT NULL
              AND c.CHECK_STATUS != 4 AND c. STATUS = 1
      </if>
      WHERE
        a.IS_DELETED = #{isDeleted, jdbcType=INTEGER}
      AND a.BASE_CATEGORY_LEVEL = #{level, jdbcType=INTEGER}
      <if test="list != null and list.size() > 0">
          AND a.PARENT_ID IN
          <foreach collection="list" item="category" open="(" close=")" index="index" separator=",">
              #{category.baseCategoryId,jdbcType=INTEGER}
          </foreach>
      </if>
      <if test="level == 3">
          GROUP BY
              a.BASE_CATEGORY_ID,
              a.BASE_CATEGORY_NAME,
              a.BASE_CATEGORY_TYPE,
              a.BASE_CATEGORY_LEVEL,
              a.PARENT_ID,
              a.TREENODES,
              b.SPU_LEVEL
      </if>
      ) x
      <if test="level == 3">
          GROUP BY
              x.BASE_CATEGORY_ID,
              x.BASE_CATEGORY_NAME,
              x.BASE_CATEGORY_TYPE,
              x.BASE_CATEGORY_LEVEL,
              x.PARENT_ID,
              x.TREENODES
      </if>
  </select>
  <select id="getthirdCategoryListById" parameterType="com.vedeng.goods.model.vo.BaseCategoryVo" resultMap="BaseResultVoMap">
    SELECT
        x.BASE_CATEGORY_ID,
        x.BASE_CATEGORY_NAME,
        x.BASE_CATEGORY_TYPE,
        x.BASE_CATEGORY_LEVEL,
        x.PARENT_ID,
        x.TREENODES,
        SUM(IF(x.SPU_LEVEL = 1, x.NUM, 0)) AS CORE_PRODUCT_NUM,
        SUM(IF(x.SPU_LEVEL = 2, x.NUM, 0)) AS TEMPORARY_PRODUCT_NUM,
        SUM(IF(x.SPU_LEVEL = 3, x.NUM, 0)) AS OTHER_PRODUCT_NUM
    FROM
    (
    SELECT
        a.BASE_CATEGORY_ID,
        a.BASE_CATEGORY_NAME,
        a.BASE_CATEGORY_TYPE,
        a.BASE_CATEGORY_LEVEL,
        a.PARENT_ID,
        a.TREENODES,
        b.SPU_LEVEL,
        COUNT(c.SKU_ID) AS NUM
    FROM
        V_BASE_CATEGORY a
    LEFT JOIN V_CORE_SPU b ON a.BASE_CATEGORY_ID = b.CATEGORY_ID AND b.CHECK_STATUS != 4
        AND b. STATUS = 1
        AND b.SPU_LEVEL IS NOT NULL
    LEFT JOIN V_CORE_SKU c ON b.SPU_ID = c.SPU_ID AND c.SKU_ID IS NOT NULL
        AND c.CHECK_STATUS != 4
        AND c. STATUS = 1
    WHERE
            a.IS_DELETED = #{isDeleted, jdbcType=INTEGER}
        AND a.BASE_CATEGORY_LEVEL = #{baseCategoryLevel, jdbcType=INTEGER}
        AND a.BASE_CATEGORY_ID = #{baseCategoryId,jdbcType=INTEGER}
    GROUP BY
        a.BASE_CATEGORY_ID,
        a.BASE_CATEGORY_NAME,
        a.BASE_CATEGORY_TYPE,
        a.BASE_CATEGORY_LEVEL,
        a.PARENT_ID,
        a.TREENODES,
        b.SPU_LEVEL
    )x
    GROUP BY
        x.BASE_CATEGORY_ID,
        x.BASE_CATEGORY_NAME,
        x.BASE_CATEGORY_TYPE,
        x.BASE_CATEGORY_LEVEL,
        x.PARENT_ID,
        x.TREENODES
  </select>
  <update id="deleteCategory" parameterType="java.util.Map">
     UPDATE
      V_BASE_CATEGORY
     SET IS_DELETED = #{isDeleted,jdbcType=BIT},
         UPDATER = #{updater,jdbcType=INTEGER},
         MOD_TIME = #{modTime,jdbcType=TIMESTAMP}
     WHERE
     <if test="list != null and list.size()>0">
       BASE_CATEGORY_ID IN
       <foreach collection="list" item="category" index="index" open="(" close=")" separator=",">
         #{category.baseCategoryId,jdbcType=INTEGER}
       </foreach>
     </if>
     <if test="list == null or list.size()==0">
       BASE_CATEGORY_ID = null
     </if>
  </update>
  <select id="getBaseCategoryListPageByAttr" parameterType="java.util.Map" resultMap="BaseResultVoMap">
      SELECT DISTINCT
          CONCAT(
              d.BASE_CATEGORY_NAME,
              '-',
              c.BASE_CATEGORY_NAME,
              '-',
              b.BASE_CATEGORY_NAME
          ) AS CATEGORY_JOIN_NAME
      FROM
          V_CATEGORY_ATTR_VALUE_MAPPING a
      LEFT JOIN V_BASE_CATEGORY b ON a.BASE_CATEGORY_ID = b.BASE_CATEGORY_ID AND b.IS_DELETED = 0
      LEFT JOIN V_BASE_CATEGORY c ON b.PARENT_ID = c.BASE_CATEGORY_ID AND c.IS_DELETED = 0
      LEFT JOIN V_BASE_CATEGORY d ON c.PARENT_ID = d.BASE_CATEGORY_ID AND d.IS_DELETED = 0
      WHERE
        a.IS_DELETED = #{isDeleted, jdbcType=INTEGER}
        AND a.BASE_ATTRIBUTE_ID = #{attrId, jdbcType=INTEGER}
      ORDER BY
        a.ADD_TIME DESC
  </select>
  <select id="checkRepeatCategory" parameterType="com.vedeng.goods.model.vo.BaseCategoryVo" resultType="java.lang.Integer">
      SELECT
        COUNT(1)
      FROM
        V_BASE_CATEGORY a
      WHERE a.BASE_CATEGORY_NAME = #{baseCategoryName,jdbcType=VARCHAR}
      AND a.IS_DELETED = 0
      AND a.BASE_CATEGORY_LEVEL = #{baseCategoryLevel,jdbcType=BIT}
      <if test="baseCategoryLevel == 2 || baseCategoryLevel == 3">
        AND a.PARENT_ID = #{parentId,jdbcType=INTEGER}
      </if>
      <if test="baseCategoryLevel == 3">
        AND a.BASE_CATEGORY_TYPE = #{baseCategoryType,jdbcType=BIT}
      </if>
      <if test="baseCategoryId != null and baseCategoryId != 0">
        AND a.BASE_CATEGORY_ID != #{baseCategoryId,jdbcType=INTEGER}
      </if>
  </select>
    <select id="getOrganizedCategoryNameById" parameterType="map" resultType="java.lang.String">
        SELECT
            CONCAT(
                c.BASE_CATEGORY_NAME,
                #{symbol},
                b.BASE_CATEGORY_NAME,
                #{symbol},
                a.BASE_CATEGORY_NAME
            ) AS CATEGORY_JOIN_NAME
        FROM
            V_BASE_CATEGORY a
        LEFT JOIN V_BASE_CATEGORY b ON a.PARENT_ID = b.BASE_CATEGORY_ID
        LEFT JOIN V_BASE_CATEGORY c ON b.PARENT_ID = c.BASE_CATEGORY_ID
        WHERE
            a.BASE_CATEGORY_ID = #{thirdCategoryId,jdbcType=INTEGER}
    </select>
    <select id="getCategoryIdByKeyWords" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT
          DISTINCT CATEGORY_ID
        FROM
          V_CORE_SPU a
        LEFT JOIN V_CORE_SKU b ON a.SPU_ID = b.SPU_ID AND b.SKU_ID IS NOT NULL
          AND b.CHECK_STATUS != 4
          AND b. STATUS = 1
        WHERE
          a.CHECK_STATUS != 4
          AND a.STATUS = 1
          AND a.SPU_LEVEL IS NOT NULL
          AND (
            a.SPU_NAME LIKE CONCAT('%',#{keyWords,jdbcType=VARCHAR},'%')
            OR SKU_NAME LIKE CONCAT('%',#{keyWords,jdbcType=VARCHAR},'%')
            )
          AND a.CATEGORY_ID != 0
    </select>
    <select id="getCategoryListByKeyWords" resultType="com.vedeng.goods.model.vo.BaseCategoryVo">
        SELECT
            a.BASE_CATEGORY_ID,
            CONCAT(
                c.BASE_CATEGORY_NAME,
                ' > ',
                b.BASE_CATEGORY_NAME,
                ' > ',
                a.BASE_CATEGORY_NAME
            ) AS CATEGORY_JOIN_NAME,
            a.BASE_CATEGORY_TYPE
        FROM
            V_BASE_CATEGORY a
        LEFT JOIN V_BASE_CATEGORY b ON a.PARENT_ID = b.BASE_CATEGORY_ID
        LEFT JOIN V_BASE_CATEGORY c ON b.PARENT_ID = c.BASE_CATEGORY_ID
        WHERE
            a.IS_DELETED = 0
            AND b.IS_DELETED = 0
            AND c.IS_DELETED = 0
            AND (a.BASE_CATEGORY_NAME LIKE CONCAT('%',#{keyWords,jdbcType=VARCHAR},'%')
            OR b.BASE_CATEGORY_NAME LIKE CONCAT('%',#{keyWords,jdbcType=VARCHAR},'%')
            OR c.BASE_CATEGORY_NAME LIKE CONCAT('%',#{keyWords,jdbcType=VARCHAR},'%')
            <if test="list != null and list.size() > 0">
                OR a.BASE_CATEGORY_ID IN
                <foreach collection="list" item="id" index="index" open="(" close=")" separator=",">
                    #{id,jdbcType=INTEGER}
                </foreach>
            </if>
            )
    </select>

    <select id="getFirstCategory" resultMap="BaseResultMap">
        select BASE_CATEGORY_ID,BASE_CATEGORY_NAME from V_BASE_CATEGORY where IS_DELETED=0 and BASE_CATEGORY_LEVEL=1 order by ADD_TIME desc
    </select>

    <select id="getSecondCategory" resultMap="BaseResultMap">
        select BASE_CATEGORY_ID,BASE_CATEGORY_NAME from V_BASE_CATEGORY where IS_DELETED=0 and BASE_CATEGORY_LEVEL=2 and PARENT_ID=#{baseCategoryId} order by ADD_TIME desc
    </select>

    <select id="getBaseCategoryInfoById" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" /> from V_BASE_CATEGORY where BASE_CATEGORY_ID = #{categoryId}
    </select>

    <update id="moveCatrgory">
        update V_BASE_CATEGORY set PARENT_ID = #{parentId} where BASE_CATEGORY_ID = #{baseCatrgoryId}
    </update>

    <select id="getMoveCategoryDtoByCategoryId" resultType="com.vedeng.goods.model.dto.MoveCategoryDto">
        select P.SPU_ID,P.SHOW_NAME as SPU_NAME,count(SKU_ID) SKU_COUNT from V_CORE_SPU P
        left join V_CORE_SKU K on P.SPU_ID=K.SPU_ID
        where P.CATEGORY_ID = #{categoryId} and P.STATUS=1 and K.STATUS=1
        group by P.SPU_ID
    </select>

    <select id="getFirstAndSecondCategoryListByKeyWords" resultType="com.vedeng.goods.model.FirstAndSecondCategoryInfo">
        SELECT
        c.BASE_CATEGORY_ID as firstCategoryId,
        c.BASE_CATEGORY_NAME as firstCategoryName,
        b.BASE_CATEGORY_ID as secondCategoryId,
        b.BASE_CATEGORY_NAME as secondCategoryName
        FROM
        V_BASE_CATEGORY a
        LEFT JOIN V_BASE_CATEGORY b ON a.PARENT_ID = b.BASE_CATEGORY_ID
        LEFT JOIN V_BASE_CATEGORY c ON b.PARENT_ID = c.BASE_CATEGORY_ID
        WHERE
        a.IS_DELETED = 0
        AND b.IS_DELETED = 0
        AND c.IS_DELETED = 0
        AND (a.BASE_CATEGORY_NAME LIKE CONCAT('%',#{keyWords,jdbcType=VARCHAR},'%')
        OR b.BASE_CATEGORY_NAME LIKE CONCAT('%',#{keyWords,jdbcType=VARCHAR},'%')
        OR c.BASE_CATEGORY_NAME LIKE CONCAT('%',#{keyWords,jdbcType=VARCHAR},'%')
        <if test="list != null and list.size() > 0">
            OR a.BASE_CATEGORY_ID IN
            <foreach collection="list" item="id" index="index" open="(" close=")" separator=",">
                #{id,jdbcType=INTEGER}
            </foreach>
        </if>
        )
    </select>
    <select id="getAllHierarchyFor" parameterType="java.lang.Integer"  resultType="java.lang.String">
        SELECT
            CONCAT(
                    c.BASE_CATEGORY_ID,
                    ',',
                    b.BASE_CATEGORY_ID,
                    ',',
                    a.BASE_CATEGORY_ID
                ) AS CATEGORY_JOIN_NAME
        FROM
            V_BASE_CATEGORY a
                LEFT JOIN V_BASE_CATEGORY b ON a.PARENT_ID = b.BASE_CATEGORY_ID
                LEFT JOIN V_BASE_CATEGORY c ON b.PARENT_ID = c.BASE_CATEGORY_ID
        WHERE
            a.BASE_CATEGORY_ID = #{thirdCategoryId,jdbcType=INTEGER}
    </select>
    <select id="listSpuIdiUnderThirdCategory" parameterType="list" resultType="java.lang.Integer">
        SELECT spu.SPU_ID
        FROM   V_BASE_CATEGORY c
        LEFT JOIN V_CORE_SPU spu ON c.BASE_CATEGORY_ID = spu.CATEGORY_ID
        WHERE  spu.CATEGORY_ID = #{thirdCategoryId,jdbcType=INTEGER}
          AND spu.STATUS = 1 and c.BASE_CATEGORY_LEVEL=3
    </select>

    <select id="getAllLevelCategoryNameById" resultType="java.lang.String">
        SELECT CONCAT(C1.BASE_CATEGORY_NAME,'/',C2.BASE_CATEGORY_NAME,'/',C3.BASE_CATEGORY_NAME)
         FROM V_BASE_CATEGORY C3
        LEFT JOIN V_BASE_CATEGORY C2 ON C3.PARENT_ID=C2.BASE_CATEGORY_ID
        LEFT JOIN V_BASE_CATEGORY C1 ON C2.PARENT_ID=C1.BASE_CATEGORY_ID
        WHERE C3.BASE_CATEGORY_LEVEL=3 AND C3.IS_DELETED=0 AND C3.BASE_CATEGORY_ID=#{categoryId}
    </select>

    <select id="getValidSpuInfoByCategoryId" resultType="com.vedeng.goods.command.SpuAddCommand">
        SELECT
            T2.SPU_ID,
            T2.CHECK_STATUS AS SPU_CHECK_STATUS
        FROM
            V_BASE_CATEGORY T1
            LEFT JOIN V_CORE_SPU T2 ON T1.BASE_CATEGORY_ID = T2.CATEGORY_ID
        WHERE
            T2.CATEGORY_ID = #{baseCategoryId,jdbcType=INTEGER}
            AND T1.BASE_CATEGORY_LEVEL = 3
            AND T2.STATUS = 1
            AND T2.CHECK_STATUS = 3
    </select>
    <select id="getDepartmentByBaseCategoryId" resultType="java.lang.String">
        select group_concat(distinct c.DEPARTMENT_NAME SEPARATOR '、')
        from V_BASE_CATEGORY a
                 left join V_CATEGORY_DEPARTMENT b on a.BASE_CATEGORY_ID = b.CATEGORY_ID and b.IS_DELETED = 0
                 left join T_DEPARTMENTS_HOSPITAL c on b.DEPARTMENT_ID = c.DEPARTMENT_ID and c.IS_DELETE = 0
        where a.IS_DELETED = 0
          and c.DEPARTMENT_NAME is not null
          and a.BASE_CATEGORY_ID = #{categoryId,jdbcType=INTEGER}
    </select>

  <select id="findByBaseCategoryNameAndIsDeleted" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from V_BASE_CATEGORY
        where BASE_CATEGORY_NAME=#{baseCategoryName,jdbcType=VARCHAR} and IS_DELETED=#{isDeleted,jdbcType=BIT}
    </select>

    <select id="getBaseCategoryListByIds" resultMap="BaseResultVoMap">
        select
        <include refid="Base_Column_List" />
        from V_BASE_CATEGORY
        where BASE_CATEGORY_ID IN
        <foreach collection="baseCategoryIds" item="baseCategoryId" separator="," open="(" close=")">
            #{baseCategoryId,jdbcType=INTEGER}
        </foreach>
    </select>

    <select id="getRecent6MonthCategorySaleInfo" resultType="com.vedeng.goods.model.dto.CategorySaleInfoDto">
        SELECT
            p.CATEGORY_ID AS categoryId,
            sum( g.NUM ) AS MYCOUNT
        FROM
            T_SALEORDER_GOODS g
                LEFT JOIN V_CORE_SKU k ON g.GOODS_ID = k.SKU_ID
                LEFT JOIN V_CORE_SPU p ON k.SPU_ID = p.SPU_ID
                LEFT JOIN T_SALEORDER C ON C.SALEORDER_ID = g.SALEORDER_ID
        WHERE
            C.VALID_STATUS > 0
          AND COMPANY_ID = 1
          AND g.IS_DELETE = 0
          AND g.ADD_TIME > UNIX_TIMESTAMP( date_sub( curdate(), INTERVAL + 6 MONTH )) * 1000
        GROUP BY
            p.CATEGORY_ID
    </select>

    <update id="batchUpdateRecent6MonthCategorySaleNum">
        UPDATE V_BASE_CATEGORY
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="SIX_MONTH_SALE_NUM = case" suffix="end,">
                <foreach collection="categorySaleInfoList" item="categorySaleInfo" index="index">
                    <if test="categorySaleInfo.myCount!=null">
                        when BASE_CATEGORY_ID = #{categorySaleInfo.categoryId} then #{categorySaleInfo.myCount}
                    </if>
                </foreach>
            </trim>
        </trim>
        WHERE BASE_CATEGORY_ID IN
        <foreach collection="categorySaleInfoList" item="categorySaleInfo" separator="," open="(" close=")">
            #{categorySaleInfo.categoryId}
        </foreach>
    </update>
</mapper>