package com.vedeng.flash.controller;

import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.Organization;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.jasper.IreportExport;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.flash.constant.FlashConstant;
import com.vedeng.flash.dao.RegularPrepareSkuMapper;
import com.vedeng.flash.dto.DealDetailDto;
import com.vedeng.flash.dto.PrepareStockCommand;
import com.vedeng.flash.dto.PrepareStockDto;
import com.vedeng.flash.service.prepare.PrepareStockService;
import com.vedeng.goods.model.Goods;
import com.vedeng.order.model.Buyorder;
import com.vedeng.system.service.OrgService;
import com.vedeng.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:  备货计划
 * @Author:       Davis
 * @Date:         2021/5/18 下午2:23
 * @Version:      1.0
 */
@Controller
@RequestMapping("/flash/prepare")
public class PrepareStockPlanController extends BaseController {

    @Autowired
    UserService userService;

    @Autowired
    private PrepareStockService prepareStockService;

    @Autowired
    private RegularPrepareSkuMapper regularPrepareSkuMapper;

    @Resource
    private OrgService orgService;

    @Resource
    private UserMapper userMapper;

    /**
     * 备货计划列表
     * @param model 视图模型
     * @param command 查询条件
     * @param request 请求头
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/list")
    public String index(Model model, @ModelAttribute("command") PrepareStockCommand command, HttpServletRequest request) {
        User currentUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        Page page = getPageTag(request, command.getPageNo(), command.getPageSize());
        try {
            model.addAttribute("spuTypeList", getSysOptionDefinitionInNewGoodsFlow());
            //所有的分配人
            List<User> assUser = userService.getAllGoodsManagersAndAssistant();
            model.addAttribute("assUser", assUser);

        } catch (Exception e) {
            logger.error("商品列表页，搜索条件初始化失败。", e);
        }

        List<Integer> userIdList = new ArrayList<>();

        if(command.getIsAdmin() != null && command.getIsAdmin() == 1){
            List<Organization> orgList = orgService.getChildrenOrgByParentId(currentUser.getOrgId(), 1);
            if(org.apache.commons.collections.CollectionUtils.isNotEmpty(orgList)) {
                orgList.stream().forEach(organization -> {
                    List<Integer> _userIdList = userMapper.getUserIdListByOrgId(organization.getOrgId());
                    userIdList.addAll(_userIdList);
                });
            }
        }

        command.setUserIdList(userIdList);

        List<PrepareStockDto> prepareStockList = prepareStockService.getPrepareStockListPage(command, page);

        model.addAttribute("currentUser", currentUser);
        model.addAttribute("skuList", prepareStockList);
        model.addAttribute("page", page);
        model.addAttribute("command", command);
        return "flash/prepare/prepare_list";
    }

    /**
     * 在途列表
     * @param model 视图模型
     * @return
     */
    @RequestMapping(value = "/getIntransitStock")
    public String getIntransitStock(Model model, PrepareStockCommand command) {
        List<Buyorder> buyOrderList = prepareStockService.getIntranstiStockList(command);
        model.addAttribute("buyOrderList", buyOrderList);
        return "flash/prepare/intransit_stock";
    }


    /**
     * 初始化设置安全库存页面
     * @param model 视图模型
     * @return
     */
    @RequestMapping(value = "/initSafeStockPage")
    public String initSafeStockPage(Model model, PrepareStockCommand command) {
        PrepareStockDto prepareStockDto = prepareStockService.initSafeStockPage(command);
        model.addAttribute("prepareStock", prepareStockDto);
        return "flash/prepare/safe_stock";
    }

    /**
     * 设置安全库存
     * @param stockDto 安全库存
     * @param request 请求头
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/setSafeStock")
    public ResultInfo setSafeStock(PrepareStockDto stockDto, HttpServletRequest request) {
        return prepareStockService.setSafeStock(request, stockDto);
    }


    /**
     * 初始化处理备货计划页面
     * @param model 视图模型
     * @return
     */
    @RequestMapping(value = "/initDealPrepareStockPage")
    public String initDealPrepareStockPage(Model model, PrepareStockCommand command) {
        PrepareStockDto prepareStockDto = prepareStockService.initSafeStockPage(command);
        model.addAttribute("prepareStock", prepareStockDto);
        return "flash/prepare/deal_stock";
    }


    /**
     * 查询SKU
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/searchSku", method = RequestMethod.POST)
    public ResultInfo<Goods> searchSku(HttpServletRequest request, @RequestBody PrepareStockCommand command) {
        return prepareStockService.searchSku(request, command);
    }

    /**
     * 处理暂不备货
     * @param stockDto 备货计划
     * @param request 请求头
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/dealPrepareStock")
    public ResultInfo dealPrepareStock(PrepareStockDto stockDto, HttpServletRequest request) {
        return prepareStockService.dealPrepareStock(request, stockDto);
    }

    /**
     * 更新备货计划
     * @param regularIds 备货计划
     * @param request 请求头
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/updatePrepare")
    public ResultInfo updatePrepare( HttpServletRequest request, @RequestParam String regularIds) {
        return prepareStockService.updatePrepare(request, regularIds);
    }

    /**
     * 导出备货计划页面
     * @return
     */
    @RequestMapping(value = "/initExportPrepareStockPage")
    public String initExportPrepareStockPage() {
        return "flash/prepare/export_stock";
    }

    /**
     * 导出备货计划
     * @param command 备货计划查询条件
     * @param request 请求头
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/exportPrepareStock")
    public void exportPrepareStock(HttpServletRequest request, HttpServletResponse response, PrepareStockCommand command) {
        Map<String, Object> regularSnapshotMap = prepareStockService.exportPrepareStock(request, command);
        String[] sheetNames = new String[]{"备货计划列表", "操作日志"};
        Map<String, String> jrxmlMap = new HashMap<>();
        jrxmlMap.put("regularSnapshotList", "/WEB-INF/ireport/jrxml/备货计划列表.jrxml");
        jrxmlMap.put("regularOperateLogList", "/WEB-INF/ireport/jrxml/备货计划列表操作日志.jrxml");
        IreportExport.exportMoreSheet(sheetNames, request, response, jrxmlMap,
                command.getExportMonth()+"备货计划列表.xls", regularSnapshotMap);
    }

    /**
     * 配置预测安全库存系数页面
     * @return
     */
    @RequestMapping(value = "/iniForecastSafeRatioPage")
    public String iniForecastSafeRatioPage(Model model) {
        Integer forecastSafeRatio = regularPrepareSkuMapper.getForecastSafeRatio();
        model.addAttribute("forecastSafeRatio", forecastSafeRatio);
        return "flash/prepare/forecast_safe_ratio";
    }

    /**
     * 配置预测安全库存系数
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/setForecastSafeRatio")
    public ResultInfo setForecastSafeRatio(HttpServletRequest request, PrepareStockDto prepareStockDto) {
        return prepareStockService.setForecastSafeRatio(request, prepareStockDto);
    }

    /**
     * 安全库存日志
     * @return
     */
    @RequestMapping(value = "/initSafeStockLogPage")
    public String initSafeStockLogPage(Model model, PrepareStockCommand command) {
        Map<String, Object> stockMap = prepareStockService.initSafeStockLogPage(command);
        model.addAttribute("stockLog", stockMap);
        return "flash/prepare/stock_log_list";
    }
    /**
     * @Description 打开处理详情界面
     * <AUTHOR>
     * @Date 19:22 2021/5/27
     * @Param
     * @return
     **/
    @RequestMapping(value = "/initDealDetailPage")
    public ModelAndView initDealDetailPage(HttpServletRequest request,
                                     @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                     @RequestParam(required = false) Integer pageSize,
                                     PrepareStockCommand command) {
        Page page = getPageTag(request, pageNo, pageSize);
        ModelAndView mav = new ModelAndView("flash/prepare/stock_log_detail");
        DealDetailDto dealDetailDto = prepareStockService.initDealDetailPage(command,page);
        mav.addObject("dealDetailDto", dealDetailDto);
        mav.addObject("page",dealDetailDto.getPage());
        return mav;
    }
}
