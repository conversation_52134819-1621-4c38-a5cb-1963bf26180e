package com.vedeng.order.dao;

import com.vedeng.order.model.OrderAssistantRelationDo;
import com.vedeng.order.model.dto.OrderAssistantRelationDto;
import org.springframework.stereotype.Repository;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface OrderAssistantRelationMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(OrderAssistantRelationDo record);

    int insertSelective(OrderAssistantRelationDo record);

    OrderAssistantRelationDo selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(OrderAssistantRelationDo record);

    int updateByPrimaryKey(OrderAssistantRelationDo record);

    List<OrderAssistantRelationDo> getBingdedInfoByOrderAssId(Integer orderAssitantUserId);

    List<OrderAssistantRelationDto> getAllBindedInfoByOrderAssIdSelectiveListPage(Map<String, Object> orderAssitantUserId);

    List<OrderAssistantRelationDo> getBingdedInfoByOrderAssIds(@Param("orderAssitantUserIds") List<Integer> orderAssitantUserIds);

    Integer getBingdedInfoByUser(OrderAssistantRelationDto orderAssistantRelationDto);

    int unbindOrderAssRelation(OrderAssistantRelationDto orderAssistantRelationDto);

    List<OrderAssistantRelationDo> getAllBindedInfo();
}