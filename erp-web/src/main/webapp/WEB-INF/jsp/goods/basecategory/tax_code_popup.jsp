  <%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
  <!DOCTYPE html>
  <html>
  <head>
    <meta charset="UTF-8">
    <title>税务编码</title>
      <link rel="stylesheet" href="${pageContext.request.contextPath}/webjars/ezadmin/plugins/layui/css/layui.css" media="all">
      <script src="${pageContext.request.contextPath}/webjars/ezadmin/layui/js/core.js?rnd=${resourceVersionKey}"></script>
      <script src="${pageContext.request.contextPath}/webjars/ezadmin/layui/js/ezform.js?rnd=${resourceVersionKey}"></script>
      <script src="${pageContext.request.contextPath}/webjars/ezadmin/layui/js/ezlist.js?rnd=${resourceVersionKey}"></script>
      <script src="${pageContext.request.contextPath}/webjars/ezadmin/layui/js/ezlist-layui.js?rnd=${resourceVersionKey}"></script>
    <style>

      .layui-table-grid-down {
        display: none;
      }
      .layui-icon {
        height: auto;
        background: none;
      }
    </style>
  </head>
  <body>
  <!-- 查询条件表单 -->
  <form class="layui-form" style="margin: 10px;">
      <div class="layui-form-item" style="display: flex; align-items: center;">
          <label class="layui-form-label" style="width: auto">税收编码</label>
          <div class="layui-input-inline">
              <input type="text" name="finalCode" placeholder="请输入查询条件" autocomplete="off" class="layui-input">
          </div>
          <label class="layui-form-label" style="width: auto">货物和劳务名称</label>
          <div class="layui-input-inline">
              <input type="text" name="goodsServicesName" placeholder="请输入查询条件" autocomplete="off" class="layui-input">
          </div>
          <label class="layui-form-label" style="width: auto">商品和服务分类简称</label>
          <div class="layui-input-inline">
              <input type="text" name="classificationAbbreviation" placeholder="请输入查询条件" autocomplete="off" class="layui-input">
          </div>
      </div>
      <div class="layui-form-item" style="display: flex; align-items: center;">
          <label class="layui-form-label" style="width: auto">说明&#12288;&#12288;</label>
          <div class="layui-input-inline">
              <input type="text" name="description" placeholder="请输入查询条件" autocomplete="off" class="layui-input">
          </div>
          <label class="layui-form-label" style="width: auto">汇总项名称&#12288;&#12288;</label>
          <div class="layui-input-inline">
              <input type="text" name="goodsServicesNameAbbreviation" placeholder="请输入查询条件" autocomplete="off" class="layui-input">
          </div>
          <label class="layui-form-label" style="width: auto">汇总项简介&#12288;&#12288;&#12288;&#12288;</label>
          <div class="layui-input-inline">
              <input type="text" name="goodsServicesClassificationAbbreviation" placeholder="请输入查询条件" autocomplete="off" class="layui-input">
          </div>
      </div>
      <div class="layui-form-item" style="display: flex; align-items: center;">
          <label class="layui-form-label" style="width: auto">关键字&#12288;</label>
          <div class="layui-input-inline">
              <input type="text" name="keyword" placeholder="请输入查询条件" autocomplete="off" class="layui-input">
          </div>
          <label class="layui-form-label" style="width: auto">是否常用&#12288;&#12288;&#12288;</label>
          <div class="layui-col-md6" style="width: 190px">
              <select name="isCommon">
                  <option value="">全部</option>
                  <option value="Y">常用</option>
                  <option value="N">不常用</option>
              </select>
          </div>
      </div>
      <div class="layui-form-item" style="text-align: center;">
          <div class="layui-inline">
              <button type="button" class="layui-btn" id="searchBtn">查询</button>
          </div>
          <div class="layui-inline">
              <button type="button" class="layui-btn layui-btn-primary" id="resetBtn">重置</button>
          </div>
      </div>
  </form>


  <!-- 分页列表 -->
  <table id="dataTable" lay-filter="dataTable"></table>

  <script>
    layui.use(['table', 'laypage'], function () {
      var laypage = layui.laypage;
      var table = layui.table;
      // 渲染表格
      table.render({
        elem: '#dataTable',
        id: 'dataTable',
        url: "/taxClassification/api/page.do", // 数据接口
        page: true, // 开启分页
        method: 'POST', // 将请求类型更改为 POST
        contentType: 'application/json', // 设置请求内容类型为 JSON
        headers: {
          'Content-Type': 'application/json;charset=UTF-8' // 设置请求头的内容类型
        },
        cols: [[ // 列配置
          {type: 'radio'}, // 单选框
          {field: 'goodsServicesClassificationAbbreviation', title: '汇总项简称',templet: function(d){
              return '<div title="' + d.goodsServicesClassificationAbbreviation + '">' + d.goodsServicesClassificationAbbreviation + '</div>';
            },type: 'normal', width: 150},
          {field: 'goodsServicesNameAbbreviation', title: '汇总项名称',templet: function(d){
              return '<div title="' + d.goodsServicesNameAbbreviation + '">' + d.goodsServicesNameAbbreviation + '</div>';
            },type: 'normal', width: 250},
          {field: 'goodsServicesName', title: '货物和劳务名称',templet: function(d){
              return '<div title="' + d.goodsServicesName + '">' + d.goodsServicesName + '</div>';
            },type: 'normal', width: 250},
          {field: 'classificationAbbreviation', title: '商品和服务分类简称',templet: function(d){
              return '<div title="' + d.classificationAbbreviation + '">' + d.classificationAbbreviation + '</div>';
            },type: 'normal', width: 300},
          {field: 'keyword', title: '关键字',templet: function(d){
              return '<div title="' + d.keyword + '">' + d.keyword + '</div>';
            },type: 'normal', width: 300},
          {field: 'description', title: '说明',templet: function(d){
              return '<div title="' + d.description + '">' + d.description + '</div>';
            },type: 'normal', width: 300},
          {field: 'isCommon', title: '是否常用',templet: function(d){
              return '<div title="' + d.isCommon + '">' + d.isCommon + '</div>';
            },type: 'normal', width: 100},
          {field: 'finalCode', title: '末级编码',templet: function(d){
              return '<div title="' + d.finalCode + '">' + d.finalCode + '</div>';
            },type: 'normal', width: 200},
          {field: 'summaryCode', title: '汇总编码',templet: function(d){
              return '<div title="' + d.summaryCode + '">' + d.summaryCode + '</div>';
            },type: 'normal', width: 200}
        ]],
        request: {
          pageName: 'pageNum', // 用于分页的参数名称，默认为 'page'
          limitName: 'pageSize' // 每页数据量的参数名，默认为 'limit'
        }
      });

      // 查询按钮点击事件
      document.getElementById('searchBtn').onclick = function () {
        var finalCode = document.getElementsByName('finalCode')[0].value;
        var goodsServicesName = document.getElementsByName('goodsServicesName')[0].value;
        var classificationAbbreviation = document.getElementsByName('classificationAbbreviation')[0].value;
        var description = document.getElementsByName('description')[0].value;
        var goodsServicesNameAbbreviation = document.getElementsByName('goodsServicesNameAbbreviation')[0].value;
        var goodsServicesClassificationAbbreviation = document.getElementsByName('goodsServicesClassificationAbbreviation')[0].value;
        var keyword = document.getElementsByName('keyword')[0].value;
        var isCommon = document.getElementsByName('isCommon')[0].value;
        table.reload('dataTable', {
          page: {
            curr: 1 // 重置为第一页
          },
          where: {
            param: {"finalCode": finalCode, "goodsServicesName": goodsServicesName, "classificationAbbreviation": classificationAbbreviation,
              "description": description, "goodsServicesNameAbbreviation": goodsServicesNameAbbreviation, "goodsServicesClassificationAbbreviation": goodsServicesClassificationAbbreviation,
              "keyword": keyword, "isCommon": isCommon},
          }
        });
      };
    });

    document.getElementById('resetBtn').onclick = function () {
      var inputFields = document.getElementsByClassName('layui-input');
      for (var i = 0; i < inputFields.length; i++) {
        inputFields[i].value = ''; // 清空输入框的值
      }
      var selectFields = document.getElementsByName('isCommon');
      for (var i = 0; i < selectFields.length; i++) {
            selectFields[i].value = ''; // 清空下拉框的值
      }
        layui.use(['table', 'laypage'], function () {
            var laypage = layui.laypage;
            var table = layui.table;
            table.reload('dataTable', {
                page: {
                    curr: 1 // 重置为第一页
                },
                where: {
                    param: {},
                }
            })
        });
    };

  </script>
  </body>
  </html>
