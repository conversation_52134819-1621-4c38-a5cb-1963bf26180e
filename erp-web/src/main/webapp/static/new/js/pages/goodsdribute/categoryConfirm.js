

function qd(secondId,thirdId) {
    checkLogin();
    $.ajax({
        type: "POST",
        url: page_url + "/category/base/submitCategory.do",
        data: $('#tjform').serialize(),
        dataType: 'json',
        async: false,
        success: function (data) {
            if (data.data){
            parent.location.reload()
            }else {
                index = layer.confirm(data.message, {
                    btn: ['确定'] //按钮
                }, function () {
                    layer.close(index);
                    window.location.reload()
                }, function () {
                });
            }
        },
        error: function (data) {
            if (data.status == 1001) {
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    });

}