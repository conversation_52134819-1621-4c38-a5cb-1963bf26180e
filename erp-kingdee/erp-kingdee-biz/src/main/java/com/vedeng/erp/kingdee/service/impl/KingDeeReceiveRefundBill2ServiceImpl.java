package com.vedeng.erp.kingdee.service.impl;

import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeeReceiveRefundBillCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveRefundBill;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveRefundBillDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeReceiveRefundBillCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeReceiveRefundBillConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeActualReceiveRefundBillRepository;
import com.vedeng.erp.kingdee.service.KingDeeReceiveRefundBillApiService;
import com.vedeng.erp.kingdee.service.KingDeeReceiveRefundBillService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class KingDeeReceiveRefundBill2ServiceImpl extends KingDeeBaseServiceImpl<KingDeeReceiveRefundBill
        , KingDeeReceiveRefundBillDto
        , KingDeeReceiveRefundBillCommand
        , KingDeeActualReceiveRefundBillRepository
        , KingDeeReceiveRefundBillConvertor
        , KingDeeReceiveRefundBillCommandConvertor> implements KingDeeReceiveRefundBillService, KingDeeReceiveRefundBillApiService {


    @Override
    public boolean getIsAutoSubmitAndAudit(KingDeeReceiveRefundBillDto kingDeeReceiveRefundBillDto){
        Boolean isAutoSubmitAndAudit = kingDeeReceiveRefundBillDto.getIsAutoSubmitAndAudit();
        // 使用原来的标识
        log.info("isAutoSubmitAndAudit使用自动提交审核标识：{}",isAutoSubmitAndAudit);
        return isAutoSubmitAndAudit;
    }
}
