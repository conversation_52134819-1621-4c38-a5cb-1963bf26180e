package com.vedeng.activiti.taskassign;

import com.vedeng.erp.aftersale.dto.ExpenseAfterSalesDto;
import com.vedeng.erp.aftersale.service.ExpenseAfterSalesApiService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import java.util.Objects;

public class EditExpenseAfterSaleExecutionListener implements ExecutionListener {

	public static Logger logger = LoggerFactory.getLogger(EditExpenseAfterSaleExecutionListener.class);
    // 运行时注入service
    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
	private final ExpenseAfterSalesApiService expenseAfterSalesApiService = (ExpenseAfterSalesApiService) context.getBean("expenseAfterSalesServiceImpl");

    //售后审核触发器
    //根据穿参通用回写主表中状态
    @Override
	public void notify(DelegateExecution execution) throws Exception {
		ExpenseAfterSalesDto expenseAfterSales = (ExpenseAfterSalesDto) execution.getVariable("expenseAfterSales");
		//已生效 审核通过
		if (Objects.nonNull(expenseAfterSales)) {
			expenseAfterSalesApiService.audit(expenseAfterSales.getExpenseAfterSalesId());
			try {

				// 计算更新售后单的 退票状态
				expenseAfterSalesApiService.calculateAndUpdateInvoiceReturnStatus(expenseAfterSales.getExpenseAfterSalesId());
				logger.info("采购费用售后单审核通过计算并更新退票状态，售后单id：{}", expenseAfterSales.getExpenseAfterSalesId());
			} catch (Exception e) {
				logger.error("EditExpenseAfterSaleExecutionListener warn", e);
			}

		}

	}
}
