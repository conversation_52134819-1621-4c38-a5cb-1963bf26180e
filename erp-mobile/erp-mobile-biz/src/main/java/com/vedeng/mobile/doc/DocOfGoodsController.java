package com.vedeng.mobile.doc;

import com.vedeng.common.core.base.R;
import com.vedeng.doc.dto.DocOfGoodsQueryDetailDto;
import com.vedeng.doc.query.DocOfGoodsDetailQuery;
import com.vedeng.doc.service.DocOfGoodsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 资料库-商品资料
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/mobile/doc")
@RequiredArgsConstructor
public class DocOfGoodsController {

    private final DocOfGoodsService docOfGoodsService;

    /**
     * 查询资料库商品资料下载信息
     *
     * @param docOfGoodsDetailQuery 参数
     * @return List
     */
    @PostMapping("/queryDetail")
    public R<List<DocOfGoodsQueryDetailDto>> queryDocOfGoodsDetail(@RequestBody DocOfGoodsDetailQuery docOfGoodsDetailQuery) {
        return R.success(docOfGoodsService.queryDocOfGoodsDetail(docOfGoodsDetailQuery));
    }


}
