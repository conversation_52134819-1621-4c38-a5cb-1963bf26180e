<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="查看可安装区域" scope="application" />
<%@ include file="../../common/common.jsp"%>
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/tag.css?rnd=${resourceVersionKey}">
<div class="main-container">
    <div class="normal-list-page">
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
                <tr>
                    <th class="wid6">省</th>
                    <th class="wid6">市</th>
                    <th class="wid6">区</th>
                </tr>
            </thead>
            <tbody>
                <c:forEach var="installAreaList" items="${installAreaList}"  varStatus="num">
                    <tr>
                        <td>${maintainInstallArea.provinceName}</td>
                        <td>
                            ${maintainInstallArea.cityName}
                        </td>
                        <td>${maintainInstallArea.zoneName}</td>
                    </tr>
                </c:forEach>
            </tbody>
        </table>
    </div>
    <tags:page page="${page}" />
</div>

<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/price/index.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp"%>
