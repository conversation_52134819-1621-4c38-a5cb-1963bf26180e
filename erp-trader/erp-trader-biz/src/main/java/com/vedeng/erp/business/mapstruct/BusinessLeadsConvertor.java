package com.vedeng.erp.business.mapstruct;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.ctrip.framework.apollo.ConfigService;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.business.domain.dto.BusinessLeadsDto;
import com.vedeng.erp.business.domain.entity.BusinessLeadsEntity;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: dto entity转换类
 * @date 2022/7/9 13:21
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface BusinessLeadsConvertor extends BaseMapStruct<BusinessLeadsEntity, BusinessLeadsDto> {


    /**
     * DTO转Entity
     *
     * @param dto BusinessLeadsDto
     * @return BusinessLeadsEntity
     */
    @Override
    @Mapping(target = "followPic", source = "followPicList", qualifiedByName = "strList2Str")
    BusinessLeadsEntity toEntity(BusinessLeadsDto dto);

    /**
     * Entity转DTO
     *
     * @param entity BusinessLeadsEntity
     * @return BusinessLeadsDto
     */
    @Override
    @Mapping(target = "tagIdList", source = "tagIds", qualifiedByName = "str2IntList")
    @Mapping(target = "followPicList", source = "followPic", qualifiedByName = "str2StrList")
    BusinessLeadsDto toDto(BusinessLeadsEntity entity);


    /**
     * str转strList
     *
     * @param str str
     * @return List<String>
     */
    @Named("str2StrList")
    default List<String> str2StrList(String str) {
        if (StringUtils.isBlank(str)){
            return Collections.emptyList();
        }
        String[] split = str.split(",");
        String ossHttp = ConfigService.getAppConfig().getProperty("oss_http", "");
        String ossUrl = ConfigService.getAppConfig().getProperty("oss_url", "");
        return Optional.of(Arrays.asList(split)).orElse(new ArrayList<>()).stream().map(pic -> ossHttp + ossUrl + pic).collect(Collectors.toList());
    }

    /**
     * strList转str
     *
     * @param strList strList
     * @return String
     */
    @Named("strList2Str")
    default String strList2Str(List<String> strList) {
        if (CollUtil.isEmpty(strList)) {
            return "";
        }
        return String.join(StrUtil.COMMA, strList);
    }


    /**
     * str转intList
     *
     * @param str str
     * @return List<Integer>
     */
    @Named("str2IntList")
    default List<Integer> str2IntList(String str) {
        if (StringUtils.isBlank(str)){
            return Collections.emptyList();
        }
        Integer[] integers = Convert.toIntArray(str);
        return Arrays.asList(integers);
    }

    /**
     * intList转str
     *
     * @param intList intList
     * @return String
     */
    @Named("intList2Str")
    default String intList2Str(List<Integer> intList) {
        if (CollUtil.isEmpty(intList)) {
            return "";
        }
        return intList.stream().map(String::valueOf).collect(Collectors.joining(StrUtil.COMMA));
    }


}
