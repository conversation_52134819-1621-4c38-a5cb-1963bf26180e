<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchExpressReceiptBasicDataDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchExpressReceiptBasicDataDto">
    <!--@mbg.generated-->
    <!--@Table T_EXPRESS_RECEIPT_BASIC_DATA-->
    <id column="EXPRESS_RECEIPT_BASIC_DATA_ID" jdbcType="INTEGER" property="expressReceiptBasicDataId" />
    <result column="UNIQUE_KEY" jdbcType="VARCHAR" property="uniqueKey" />
    <result column="SALEORDER_NO" jdbcType="VARCHAR" property="saleorderNo" />
    <result column="OUT_IN_NO" jdbcType="VARCHAR" property="outInNo" />
    <result column="IS_EXPENSE" jdbcType="INTEGER" property="isExpense" />
    <result column="LOGISTICS" jdbcType="VARCHAR" property="logistics" />
    <result column="EXPRESS_ID" jdbcType="INTEGER" property="expressId" />
    <result column="TAKE_TRADER_ADDRESS" jdbcType="VARCHAR" property="takeTraderAddress" />
    <result column="TAKE_TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="takeTraderContactName" />
    <result column="TAKE_TRADER_CONTACT_TELEPHONE" jdbcType="VARCHAR" property="takeTraderContactTelephone" />
    <result column="LOGISTICS_NO" jdbcType="VARCHAR" property="logisticsNo" />
    <result column="DATA_TYPE" jdbcType="INTEGER" property="dataType" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="BATCH_NUMBER" jdbcType="VARCHAR" property="batchNumber" />
    <result column="BARCODE_FACTORY" jdbcType="VARCHAR" property="barcodeFactory" />
    <result column="FIRST_TRADER_TIME" jdbcType="TIMESTAMP" property="firstTraderTime" />
    <result column="ARRIVAL_TIME" jdbcType="TIMESTAMP" property="arrivalTime" />
    <result column="IS_GIFT" jdbcType="INTEGER" property="isGift" />
    <result column="WMS_NO" jdbcType="VARCHAR" property="wmsNo" />
    <result column="OLD_UNIQUE_KEY" jdbcType="VARCHAR" property="oldUniqueKey" />
    <result column="ARRIVAL_ACT_FLAG" jdbcType="INTEGER" property="arrivalActFlag" />
    <result column="ETL_TIME" jdbcType="TIMESTAMP" property="etlTime" />
    <result column="IS_PUSH_KINGDEE" jdbcType="INTEGER" property="isPushKingdee" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    EXPRESS_RECEIPT_BASIC_DATA_ID, UNIQUE_KEY, SALEORDER_NO, OUT_IN_NO, IS_EXPENSE, LOGISTICS, 
    EXPRESS_ID, TAKE_TRADER_ADDRESS, TAKE_TRADER_CONTACT_NAME, TAKE_TRADER_CONTACT_TELEPHONE, 
    LOGISTICS_NO, DATA_TYPE, NUM, SKU, BATCH_NUMBER, BARCODE_FACTORY, FIRST_TRADER_TIME, 
    ARRIVAL_TIME, IS_GIFT, WMS_NO, OLD_UNIQUE_KEY, ARRIVAL_ACT_FLAG, ETL_TIME, IS_PUSH_KINGDEE, 
    REMARK, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_EXPRESS_RECEIPT_BASIC_DATA
    where EXPRESS_RECEIPT_BASIC_DATA_ID = #{expressReceiptBasicDataId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_EXPRESS_RECEIPT_BASIC_DATA
    where EXPRESS_RECEIPT_BASIC_DATA_ID = #{expressReceiptBasicDataId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="EXPRESS_RECEIPT_BASIC_DATA_ID" keyProperty="expressReceiptBasicDataId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchExpressReceiptBasicDataDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_EXPRESS_RECEIPT_BASIC_DATA (UNIQUE_KEY, SALEORDER_NO, OUT_IN_NO, 
      IS_EXPENSE, LOGISTICS, EXPRESS_ID, 
      TAKE_TRADER_ADDRESS, TAKE_TRADER_CONTACT_NAME, 
      TAKE_TRADER_CONTACT_TELEPHONE, LOGISTICS_NO, 
      DATA_TYPE, NUM, SKU, 
      BATCH_NUMBER, BARCODE_FACTORY, FIRST_TRADER_TIME, 
      ARRIVAL_TIME, IS_GIFT, WMS_NO, 
      OLD_UNIQUE_KEY, ARRIVAL_ACT_FLAG, ETL_TIME, 
      IS_PUSH_KINGDEE, REMARK, ADD_TIME, 
      MOD_TIME, CREATOR, UPDATER, 
      CREATOR_NAME, UPDATER_NAME)
    values (#{uniqueKey,jdbcType=VARCHAR}, #{saleorderNo,jdbcType=VARCHAR}, #{outInNo,jdbcType=VARCHAR}, 
      #{isExpense,jdbcType=INTEGER}, #{logistics,jdbcType=VARCHAR}, #{expressId,jdbcType=INTEGER}, 
      #{takeTraderAddress,jdbcType=VARCHAR}, #{takeTraderContactName,jdbcType=VARCHAR}, 
      #{takeTraderContactTelephone,jdbcType=VARCHAR}, #{logisticsNo,jdbcType=VARCHAR}, 
      #{dataType,jdbcType=INTEGER}, #{num,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, 
      #{batchNumber,jdbcType=VARCHAR}, #{barcodeFactory,jdbcType=VARCHAR}, #{firstTraderTime,jdbcType=TIMESTAMP}, 
      #{arrivalTime,jdbcType=TIMESTAMP}, #{isGift,jdbcType=INTEGER}, #{wmsNo,jdbcType=VARCHAR}, 
      #{oldUniqueKey,jdbcType=VARCHAR}, #{arrivalActFlag,jdbcType=INTEGER}, #{etlTime,jdbcType=TIMESTAMP}, 
      #{isPushKingdee,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, 
      #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="EXPRESS_RECEIPT_BASIC_DATA_ID" keyProperty="expressReceiptBasicDataId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchExpressReceiptBasicDataDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_EXPRESS_RECEIPT_BASIC_DATA
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="uniqueKey != null and uniqueKey != ''">
        UNIQUE_KEY,
      </if>
      <if test="saleorderNo != null and saleorderNo != ''">
        SALEORDER_NO,
      </if>
      <if test="outInNo != null and outInNo != ''">
        OUT_IN_NO,
      </if>
      <if test="isExpense != null">
        IS_EXPENSE,
      </if>
      <if test="logistics != null and logistics != ''">
        LOGISTICS,
      </if>
      <if test="expressId != null">
        EXPRESS_ID,
      </if>
      <if test="takeTraderAddress != null and takeTraderAddress != ''">
        TAKE_TRADER_ADDRESS,
      </if>
      <if test="takeTraderContactName != null and takeTraderContactName != ''">
        TAKE_TRADER_CONTACT_NAME,
      </if>
      <if test="takeTraderContactTelephone != null and takeTraderContactTelephone != ''">
        TAKE_TRADER_CONTACT_TELEPHONE,
      </if>
      <if test="logisticsNo != null and logisticsNo != ''">
        LOGISTICS_NO,
      </if>
      <if test="dataType != null">
        DATA_TYPE,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="sku != null and sku != ''">
        SKU,
      </if>
      <if test="batchNumber != null and batchNumber != ''">
        BATCH_NUMBER,
      </if>
      <if test="barcodeFactory != null and barcodeFactory != ''">
        BARCODE_FACTORY,
      </if>
      <if test="firstTraderTime != null">
        FIRST_TRADER_TIME,
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME,
      </if>
      <if test="isGift != null">
        IS_GIFT,
      </if>
      <if test="wmsNo != null and wmsNo != ''">
        WMS_NO,
      </if>
      <if test="oldUniqueKey != null and oldUniqueKey != ''">
        OLD_UNIQUE_KEY,
      </if>
      <if test="arrivalActFlag != null">
        ARRIVAL_ACT_FLAG,
      </if>
      <if test="etlTime != null">
        ETL_TIME,
      </if>
      <if test="isPushKingdee != null">
        IS_PUSH_KINGDEE,
      </if>
      <if test="remark != null and remark != ''">
        REMARK,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="uniqueKey != null and uniqueKey != ''">
        #{uniqueKey,jdbcType=VARCHAR},
      </if>
      <if test="saleorderNo != null and saleorderNo != ''">
        #{saleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="outInNo != null and outInNo != ''">
        #{outInNo,jdbcType=VARCHAR},
      </if>
      <if test="isExpense != null">
        #{isExpense,jdbcType=INTEGER},
      </if>
      <if test="logistics != null and logistics != ''">
        #{logistics,jdbcType=VARCHAR},
      </if>
      <if test="expressId != null">
        #{expressId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderAddress != null and takeTraderAddress != ''">
        #{takeTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactName != null and takeTraderContactName != ''">
        #{takeTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactTelephone != null and takeTraderContactTelephone != ''">
        #{takeTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="logisticsNo != null and logisticsNo != ''">
        #{logisticsNo,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null">
        #{dataType,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="sku != null and sku != ''">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="batchNumber != null and batchNumber != ''">
        #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="barcodeFactory != null and barcodeFactory != ''">
        #{barcodeFactory,jdbcType=VARCHAR},
      </if>
      <if test="firstTraderTime != null">
        #{firstTraderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="arrivalTime != null">
        #{arrivalTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isGift != null">
        #{isGift,jdbcType=INTEGER},
      </if>
      <if test="wmsNo != null and wmsNo != ''">
        #{wmsNo,jdbcType=VARCHAR},
      </if>
      <if test="oldUniqueKey != null and oldUniqueKey != ''">
        #{oldUniqueKey,jdbcType=VARCHAR},
      </if>
      <if test="arrivalActFlag != null">
        #{arrivalActFlag,jdbcType=INTEGER},
      </if>
      <if test="etlTime != null">
        #{etlTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isPushKingdee != null">
        #{isPushKingdee,jdbcType=INTEGER},
      </if>
      <if test="remark != null and remark != ''">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchExpressReceiptBasicDataDto">
    <!--@mbg.generated-->
    update T_EXPRESS_RECEIPT_BASIC_DATA
    <set>
      <if test="uniqueKey != null and uniqueKey != ''">
        UNIQUE_KEY = #{uniqueKey,jdbcType=VARCHAR},
      </if>
      <if test="saleorderNo != null and saleorderNo != ''">
        SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="outInNo != null and outInNo != ''">
        OUT_IN_NO = #{outInNo,jdbcType=VARCHAR},
      </if>
      <if test="isExpense != null">
        IS_EXPENSE = #{isExpense,jdbcType=INTEGER},
      </if>
      <if test="logistics != null and logistics != ''">
        LOGISTICS = #{logistics,jdbcType=VARCHAR},
      </if>
      <if test="expressId != null">
        EXPRESS_ID = #{expressId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderAddress != null and takeTraderAddress != ''">
        TAKE_TRADER_ADDRESS = #{takeTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactName != null and takeTraderContactName != ''">
        TAKE_TRADER_CONTACT_NAME = #{takeTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactTelephone != null and takeTraderContactTelephone != ''">
        TAKE_TRADER_CONTACT_TELEPHONE = #{takeTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="logisticsNo != null and logisticsNo != ''">
        LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null">
        DATA_TYPE = #{dataType,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="sku != null and sku != ''">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="batchNumber != null and batchNumber != ''">
        BATCH_NUMBER = #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="barcodeFactory != null and barcodeFactory != ''">
        BARCODE_FACTORY = #{barcodeFactory,jdbcType=VARCHAR},
      </if>
      <if test="firstTraderTime != null">
        FIRST_TRADER_TIME = #{firstTraderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME = #{arrivalTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isGift != null">
        IS_GIFT = #{isGift,jdbcType=INTEGER},
      </if>
      <if test="wmsNo != null and wmsNo != ''">
        WMS_NO = #{wmsNo,jdbcType=VARCHAR},
      </if>
      <if test="oldUniqueKey != null and oldUniqueKey != ''">
        OLD_UNIQUE_KEY = #{oldUniqueKey,jdbcType=VARCHAR},
      </if>
      <if test="arrivalActFlag != null">
        ARRIVAL_ACT_FLAG = #{arrivalActFlag,jdbcType=INTEGER},
      </if>
      <if test="etlTime != null">
        ETL_TIME = #{etlTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isPushKingdee != null">
        IS_PUSH_KINGDEE = #{isPushKingdee,jdbcType=INTEGER},
      </if>
      <if test="remark != null and remark != ''">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
    </set>
    where EXPRESS_RECEIPT_BASIC_DATA_ID = #{expressReceiptBasicDataId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchExpressReceiptBasicDataDto">
    <!--@mbg.generated-->
    update T_EXPRESS_RECEIPT_BASIC_DATA
    set UNIQUE_KEY = #{uniqueKey,jdbcType=VARCHAR},
      SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR},
      OUT_IN_NO = #{outInNo,jdbcType=VARCHAR},
      IS_EXPENSE = #{isExpense,jdbcType=INTEGER},
      LOGISTICS = #{logistics,jdbcType=VARCHAR},
      EXPRESS_ID = #{expressId,jdbcType=INTEGER},
      TAKE_TRADER_ADDRESS = #{takeTraderAddress,jdbcType=VARCHAR},
      TAKE_TRADER_CONTACT_NAME = #{takeTraderContactName,jdbcType=VARCHAR},
      TAKE_TRADER_CONTACT_TELEPHONE = #{takeTraderContactTelephone,jdbcType=VARCHAR},
      LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR},
      DATA_TYPE = #{dataType,jdbcType=INTEGER},
      NUM = #{num,jdbcType=INTEGER},
      SKU = #{sku,jdbcType=VARCHAR},
      BATCH_NUMBER = #{batchNumber,jdbcType=VARCHAR},
      BARCODE_FACTORY = #{barcodeFactory,jdbcType=VARCHAR},
      FIRST_TRADER_TIME = #{firstTraderTime,jdbcType=TIMESTAMP},
      ARRIVAL_TIME = #{arrivalTime,jdbcType=TIMESTAMP},
      IS_GIFT = #{isGift,jdbcType=INTEGER},
      WMS_NO = #{wmsNo,jdbcType=VARCHAR},
      OLD_UNIQUE_KEY = #{oldUniqueKey,jdbcType=VARCHAR},
      ARRIVAL_ACT_FLAG = #{arrivalActFlag,jdbcType=INTEGER},
      ETL_TIME = #{etlTime,jdbcType=TIMESTAMP},
      IS_PUSH_KINGDEE = #{isPushKingdee,jdbcType=INTEGER},
      REMARK = #{remark,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR}
    where EXPRESS_RECEIPT_BASIC_DATA_ID = #{expressReceiptBasicDataId,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_EXPRESS_RECEIPT_BASIC_DATA
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="UNIQUE_KEY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.uniqueKey,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="SALEORDER_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.saleorderNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="OUT_IN_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.outInNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="IS_EXPENSE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.isExpense,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="LOGISTICS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.logistics,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="EXPRESS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.expressId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="TAKE_TRADER_ADDRESS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.takeTraderAddress,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TAKE_TRADER_CONTACT_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.takeTraderContactName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TAKE_TRADER_CONTACT_TELEPHONE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.takeTraderContactTelephone,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="LOGISTICS_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.logisticsNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="DATA_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.dataType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.num,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="SKU = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.sku,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="BATCH_NUMBER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.batchNumber,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="BARCODE_FACTORY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.barcodeFactory,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="FIRST_TRADER_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.firstTraderTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.arrivalTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="IS_GIFT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.isGift,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="WMS_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.wmsNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="OLD_UNIQUE_KEY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.oldUniqueKey,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_ACT_FLAG = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.arrivalActFlag,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ETL_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.etlTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="IS_PUSH_KINGDEE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.isPushKingdee,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.remark,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where EXPRESS_RECEIPT_BASIC_DATA_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.expressReceiptBasicDataId,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_EXPRESS_RECEIPT_BASIC_DATA
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="UNIQUE_KEY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.uniqueKey != null">
            when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.uniqueKey,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SALEORDER_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.saleorderNo != null">
            when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.saleorderNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="OUT_IN_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.outInNo != null">
            when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.outInNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_EXPENSE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isExpense != null">
            when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.isExpense,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="LOGISTICS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.logistics != null">
            when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.logistics,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="EXPRESS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.expressId != null">
            when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.expressId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="TAKE_TRADER_ADDRESS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.takeTraderAddress != null">
            when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.takeTraderAddress,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TAKE_TRADER_CONTACT_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.takeTraderContactName != null">
            when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.takeTraderContactName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TAKE_TRADER_CONTACT_TELEPHONE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.takeTraderContactTelephone != null">
            when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.takeTraderContactTelephone,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="LOGISTICS_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.logisticsNo != null">
            when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.logisticsNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="DATA_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.dataType != null">
            when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.dataType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.num != null">
            when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.num,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="SKU = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.sku != null">
            when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.sku,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="BATCH_NUMBER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.batchNumber != null">
            when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.batchNumber,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="BARCODE_FACTORY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.barcodeFactory != null">
            when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.barcodeFactory,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FIRST_TRADER_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.firstTraderTime != null">
            when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.firstTraderTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.arrivalTime != null">
            when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.arrivalTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_GIFT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isGift != null">
            when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.isGift,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="WMS_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.wmsNo != null">
            when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.wmsNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="OLD_UNIQUE_KEY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.oldUniqueKey != null">
            when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.oldUniqueKey,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_ACT_FLAG = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.arrivalActFlag != null">
            when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.arrivalActFlag,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ETL_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.etlTime != null">
            when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.etlTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_PUSH_KINGDEE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isPushKingdee != null">
            when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.isPushKingdee,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.remark != null">
            when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.remark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when EXPRESS_RECEIPT_BASIC_DATA_ID = #{item.expressReceiptBasicDataId,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where EXPRESS_RECEIPT_BASIC_DATA_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.expressReceiptBasicDataId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="EXPRESS_RECEIPT_BASIC_DATA_ID" keyProperty="expressReceiptBasicDataId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_EXPRESS_RECEIPT_BASIC_DATA
    (UNIQUE_KEY, SALEORDER_NO, OUT_IN_NO, IS_EXPENSE, LOGISTICS, EXPRESS_ID, TAKE_TRADER_ADDRESS, 
      TAKE_TRADER_CONTACT_NAME, TAKE_TRADER_CONTACT_TELEPHONE, LOGISTICS_NO, DATA_TYPE, 
      NUM, SKU, BATCH_NUMBER, BARCODE_FACTORY, FIRST_TRADER_TIME, ARRIVAL_TIME, IS_GIFT, 
      WMS_NO, OLD_UNIQUE_KEY, ARRIVAL_ACT_FLAG, ETL_TIME, IS_PUSH_KINGDEE, REMARK, ADD_TIME, 
      MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.uniqueKey,jdbcType=VARCHAR}, #{item.saleorderNo,jdbcType=VARCHAR}, #{item.outInNo,jdbcType=VARCHAR}, 
        #{item.isExpense,jdbcType=INTEGER}, #{item.logistics,jdbcType=VARCHAR}, #{item.expressId,jdbcType=INTEGER}, 
        #{item.takeTraderAddress,jdbcType=VARCHAR}, #{item.takeTraderContactName,jdbcType=VARCHAR}, 
        #{item.takeTraderContactTelephone,jdbcType=VARCHAR}, #{item.logisticsNo,jdbcType=VARCHAR}, 
        #{item.dataType,jdbcType=INTEGER}, #{item.num,jdbcType=INTEGER}, #{item.sku,jdbcType=VARCHAR}, 
        #{item.batchNumber,jdbcType=VARCHAR}, #{item.barcodeFactory,jdbcType=VARCHAR}, 
        #{item.firstTraderTime,jdbcType=TIMESTAMP}, #{item.arrivalTime,jdbcType=TIMESTAMP}, 
        #{item.isGift,jdbcType=INTEGER}, #{item.wmsNo,jdbcType=VARCHAR}, #{item.oldUniqueKey,jdbcType=VARCHAR}, 
        #{item.arrivalActFlag,jdbcType=INTEGER}, #{item.etlTime,jdbcType=TIMESTAMP}, #{item.isPushKingdee,jdbcType=INTEGER}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=INTEGER}, #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, 
        #{item.updaterName,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="EXPRESS_RECEIPT_BASIC_DATA_ID" keyProperty="expressReceiptBasicDataId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchExpressReceiptBasicDataDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_EXPRESS_RECEIPT_BASIC_DATA
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="expressReceiptBasicDataId != null">
        EXPRESS_RECEIPT_BASIC_DATA_ID,
      </if>
      UNIQUE_KEY,
      SALEORDER_NO,
      OUT_IN_NO,
      IS_EXPENSE,
      LOGISTICS,
      EXPRESS_ID,
      TAKE_TRADER_ADDRESS,
      TAKE_TRADER_CONTACT_NAME,
      TAKE_TRADER_CONTACT_TELEPHONE,
      LOGISTICS_NO,
      DATA_TYPE,
      NUM,
      SKU,
      BATCH_NUMBER,
      BARCODE_FACTORY,
      FIRST_TRADER_TIME,
      ARRIVAL_TIME,
      IS_GIFT,
      WMS_NO,
      OLD_UNIQUE_KEY,
      ARRIVAL_ACT_FLAG,
      ETL_TIME,
      IS_PUSH_KINGDEE,
      REMARK,
      ADD_TIME,
      MOD_TIME,
      CREATOR,
      UPDATER,
      CREATOR_NAME,
      UPDATER_NAME,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="expressReceiptBasicDataId != null">
        #{expressReceiptBasicDataId,jdbcType=INTEGER},
      </if>
      #{uniqueKey,jdbcType=VARCHAR},
      #{saleorderNo,jdbcType=VARCHAR},
      #{outInNo,jdbcType=VARCHAR},
      #{isExpense,jdbcType=INTEGER},
      #{logistics,jdbcType=VARCHAR},
      #{expressId,jdbcType=INTEGER},
      #{takeTraderAddress,jdbcType=VARCHAR},
      #{takeTraderContactName,jdbcType=VARCHAR},
      #{takeTraderContactTelephone,jdbcType=VARCHAR},
      #{logisticsNo,jdbcType=VARCHAR},
      #{dataType,jdbcType=INTEGER},
      #{num,jdbcType=INTEGER},
      #{sku,jdbcType=VARCHAR},
      #{batchNumber,jdbcType=VARCHAR},
      #{barcodeFactory,jdbcType=VARCHAR},
      #{firstTraderTime,jdbcType=TIMESTAMP},
      #{arrivalTime,jdbcType=TIMESTAMP},
      #{isGift,jdbcType=INTEGER},
      #{wmsNo,jdbcType=VARCHAR},
      #{oldUniqueKey,jdbcType=VARCHAR},
      #{arrivalActFlag,jdbcType=INTEGER},
      #{etlTime,jdbcType=TIMESTAMP},
      #{isPushKingdee,jdbcType=INTEGER},
      #{remark,jdbcType=VARCHAR},
      #{addTime,jdbcType=TIMESTAMP},
      #{modTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=INTEGER},
      #{updater,jdbcType=INTEGER},
      #{creatorName,jdbcType=VARCHAR},
      #{updaterName,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="expressReceiptBasicDataId != null">
        EXPRESS_RECEIPT_BASIC_DATA_ID = #{expressReceiptBasicDataId,jdbcType=INTEGER},
      </if>
      UNIQUE_KEY = #{uniqueKey,jdbcType=VARCHAR},
      SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR},
      OUT_IN_NO = #{outInNo,jdbcType=VARCHAR},
      IS_EXPENSE = #{isExpense,jdbcType=INTEGER},
      LOGISTICS = #{logistics,jdbcType=VARCHAR},
      EXPRESS_ID = #{expressId,jdbcType=INTEGER},
      TAKE_TRADER_ADDRESS = #{takeTraderAddress,jdbcType=VARCHAR},
      TAKE_TRADER_CONTACT_NAME = #{takeTraderContactName,jdbcType=VARCHAR},
      TAKE_TRADER_CONTACT_TELEPHONE = #{takeTraderContactTelephone,jdbcType=VARCHAR},
      LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR},
      DATA_TYPE = #{dataType,jdbcType=INTEGER},
      NUM = #{num,jdbcType=INTEGER},
      SKU = #{sku,jdbcType=VARCHAR},
      BATCH_NUMBER = #{batchNumber,jdbcType=VARCHAR},
      BARCODE_FACTORY = #{barcodeFactory,jdbcType=VARCHAR},
      FIRST_TRADER_TIME = #{firstTraderTime,jdbcType=TIMESTAMP},
      ARRIVAL_TIME = #{arrivalTime,jdbcType=TIMESTAMP},
      IS_GIFT = #{isGift,jdbcType=INTEGER},
      WMS_NO = #{wmsNo,jdbcType=VARCHAR},
      OLD_UNIQUE_KEY = #{oldUniqueKey,jdbcType=VARCHAR},
      ARRIVAL_ACT_FLAG = #{arrivalActFlag,jdbcType=INTEGER},
      ETL_TIME = #{etlTime,jdbcType=TIMESTAMP},
      IS_PUSH_KINGDEE = #{isPushKingdee,jdbcType=INTEGER},
      REMARK = #{remark,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="EXPRESS_RECEIPT_BASIC_DATA_ID" keyProperty="expressReceiptBasicDataId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchExpressReceiptBasicDataDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_EXPRESS_RECEIPT_BASIC_DATA
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="expressReceiptBasicDataId != null">
        EXPRESS_RECEIPT_BASIC_DATA_ID,
      </if>
      <if test="uniqueKey != null and uniqueKey != ''">
        UNIQUE_KEY,
      </if>
      <if test="saleorderNo != null and saleorderNo != ''">
        SALEORDER_NO,
      </if>
      <if test="outInNo != null and outInNo != ''">
        OUT_IN_NO,
      </if>
      <if test="isExpense != null">
        IS_EXPENSE,
      </if>
      <if test="logistics != null and logistics != ''">
        LOGISTICS,
      </if>
      <if test="expressId != null">
        EXPRESS_ID,
      </if>
      <if test="takeTraderAddress != null and takeTraderAddress != ''">
        TAKE_TRADER_ADDRESS,
      </if>
      <if test="takeTraderContactName != null and takeTraderContactName != ''">
        TAKE_TRADER_CONTACT_NAME,
      </if>
      <if test="takeTraderContactTelephone != null and takeTraderContactTelephone != ''">
        TAKE_TRADER_CONTACT_TELEPHONE,
      </if>
      <if test="logisticsNo != null and logisticsNo != ''">
        LOGISTICS_NO,
      </if>
      <if test="dataType != null">
        DATA_TYPE,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="sku != null and sku != ''">
        SKU,
      </if>
      <if test="batchNumber != null and batchNumber != ''">
        BATCH_NUMBER,
      </if>
      <if test="barcodeFactory != null and barcodeFactory != ''">
        BARCODE_FACTORY,
      </if>
      <if test="firstTraderTime != null">
        FIRST_TRADER_TIME,
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME,
      </if>
      <if test="isGift != null">
        IS_GIFT,
      </if>
      <if test="wmsNo != null and wmsNo != ''">
        WMS_NO,
      </if>
      <if test="oldUniqueKey != null and oldUniqueKey != ''">
        OLD_UNIQUE_KEY,
      </if>
      <if test="arrivalActFlag != null">
        ARRIVAL_ACT_FLAG,
      </if>
      <if test="etlTime != null">
        ETL_TIME,
      </if>
      <if test="isPushKingdee != null">
        IS_PUSH_KINGDEE,
      </if>
      <if test="remark != null and remark != ''">
        REMARK,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="expressReceiptBasicDataId != null">
        #{expressReceiptBasicDataId,jdbcType=INTEGER},
      </if>
      <if test="uniqueKey != null and uniqueKey != ''">
        #{uniqueKey,jdbcType=VARCHAR},
      </if>
      <if test="saleorderNo != null and saleorderNo != ''">
        #{saleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="outInNo != null and outInNo != ''">
        #{outInNo,jdbcType=VARCHAR},
      </if>
      <if test="isExpense != null">
        #{isExpense,jdbcType=INTEGER},
      </if>
      <if test="logistics != null and logistics != ''">
        #{logistics,jdbcType=VARCHAR},
      </if>
      <if test="expressId != null">
        #{expressId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderAddress != null and takeTraderAddress != ''">
        #{takeTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactName != null and takeTraderContactName != ''">
        #{takeTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactTelephone != null and takeTraderContactTelephone != ''">
        #{takeTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="logisticsNo != null and logisticsNo != ''">
        #{logisticsNo,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null">
        #{dataType,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="sku != null and sku != ''">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="batchNumber != null and batchNumber != ''">
        #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="barcodeFactory != null and barcodeFactory != ''">
        #{barcodeFactory,jdbcType=VARCHAR},
      </if>
      <if test="firstTraderTime != null">
        #{firstTraderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="arrivalTime != null">
        #{arrivalTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isGift != null">
        #{isGift,jdbcType=INTEGER},
      </if>
      <if test="wmsNo != null and wmsNo != ''">
        #{wmsNo,jdbcType=VARCHAR},
      </if>
      <if test="oldUniqueKey != null and oldUniqueKey != ''">
        #{oldUniqueKey,jdbcType=VARCHAR},
      </if>
      <if test="arrivalActFlag != null">
        #{arrivalActFlag,jdbcType=INTEGER},
      </if>
      <if test="etlTime != null">
        #{etlTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isPushKingdee != null">
        #{isPushKingdee,jdbcType=INTEGER},
      </if>
      <if test="remark != null and remark != ''">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="expressReceiptBasicDataId != null">
        EXPRESS_RECEIPT_BASIC_DATA_ID = #{expressReceiptBasicDataId,jdbcType=INTEGER},
      </if>
      <if test="uniqueKey != null and uniqueKey != ''">
        UNIQUE_KEY = #{uniqueKey,jdbcType=VARCHAR},
      </if>
      <if test="saleorderNo != null and saleorderNo != ''">
        SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="outInNo != null and outInNo != ''">
        OUT_IN_NO = #{outInNo,jdbcType=VARCHAR},
      </if>
      <if test="isExpense != null">
        IS_EXPENSE = #{isExpense,jdbcType=INTEGER},
      </if>
      <if test="logistics != null and logistics != ''">
        LOGISTICS = #{logistics,jdbcType=VARCHAR},
      </if>
      <if test="expressId != null">
        EXPRESS_ID = #{expressId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderAddress != null and takeTraderAddress != ''">
        TAKE_TRADER_ADDRESS = #{takeTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactName != null and takeTraderContactName != ''">
        TAKE_TRADER_CONTACT_NAME = #{takeTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactTelephone != null and takeTraderContactTelephone != ''">
        TAKE_TRADER_CONTACT_TELEPHONE = #{takeTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="logisticsNo != null and logisticsNo != ''">
        LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null">
        DATA_TYPE = #{dataType,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="sku != null and sku != ''">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="batchNumber != null and batchNumber != ''">
        BATCH_NUMBER = #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="barcodeFactory != null and barcodeFactory != ''">
        BARCODE_FACTORY = #{barcodeFactory,jdbcType=VARCHAR},
      </if>
      <if test="firstTraderTime != null">
        FIRST_TRADER_TIME = #{firstTraderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME = #{arrivalTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isGift != null">
        IS_GIFT = #{isGift,jdbcType=INTEGER},
      </if>
      <if test="wmsNo != null and wmsNo != ''">
        WMS_NO = #{wmsNo,jdbcType=VARCHAR},
      </if>
      <if test="oldUniqueKey != null and oldUniqueKey != ''">
        OLD_UNIQUE_KEY = #{oldUniqueKey,jdbcType=VARCHAR},
      </if>
      <if test="arrivalActFlag != null">
        ARRIVAL_ACT_FLAG = #{arrivalActFlag,jdbcType=INTEGER},
      </if>
      <if test="etlTime != null">
        ETL_TIME = #{etlTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isPushKingdee != null">
        IS_PUSH_KINGDEE = #{isPushKingdee,jdbcType=INTEGER},
      </if>
      <if test="remark != null and remark != ''">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="findByArrivalActFlagListAndAddTimeAndIsPushKingDee" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from
      T_EXPRESS_RECEIPT_BASIC_DATA
    <where>
    <if test="arrivalActFlagList != null and arrivalActFlagList.size() > 0">
      ARRIVAL_ACT_FLAG in
      <foreach collection="arrivalActFlagList" item="item" open="(" separator="," close=")">
        #{item,jdbcType=INTEGER}
      </foreach>
    </if>
    <if test="beginTime != null">
      and ADD_TIME <![CDATA[>=]]> #{beginTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null">
      and ADD_TIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
    </if>
    </where>
    order by EXPRESS_RECEIPT_BASIC_DATA_ID asc
    limit #{_pagesize} offset #{_skiprows}
  </select>
</mapper>