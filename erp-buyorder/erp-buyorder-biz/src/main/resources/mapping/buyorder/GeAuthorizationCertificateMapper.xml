<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.buyorder.mapper.GeAuthorizationCertificateMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.buyorder.domain.entity.GeAuthorizationCertificate">
    <id column="AUTHORIZATION_CERTIFICATE_ID" jdbcType="INTEGER" property="authorizationCertificateId" />
    <result column="AUTHORIZATION_ID" jdbcType="INTEGER" property="authorizationId" />
    <result column="TYPE" jdbcType="INTEGER" property="type" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="DOMAIN" jdbcType="VARCHAR" property="domain" />
    <result column="URI" jdbcType="VARCHAR" property="uri" />
    <result column="EXTRA" jdbcType="VARCHAR" property="extra" />
    <result column="OSS_RESOURCE_ID" jdbcType="VARCHAR" property="ossResourceId" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="SUFFIX" jdbcType="VARCHAR" property="suffix" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
  </resultMap>
  <sql id="Base_Column_List">
    AUTHORIZATION_CERTIFICATE_ID, AUTHORIZATION_ID, `TYPE`, `NAME`, `DOMAIN`, URI, EXTRA, 
    OSS_RESOURCE_ID, IS_DELETE, SUFFIX, CREATOR, ADD_TIME, UPDATER, MOD_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_GE_AUTHORIZATION_CERTIFICATE
    where AUTHORIZATION_CERTIFICATE_ID = #{authorizationCertificateId,jdbcType=INTEGER}
  </select>

  <select id="selectByGeAuId" parameterType="com.vedeng.erp.buyorder.domain.entity.GeAuthorizationCertificate" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from T_GE_AUTHORIZATION_CERTIFICATE
    where AUTHORIZATION_ID = #{authorizationId}
    <if test="isDelete!=null">
      and IS_DELETE = #{isDelete}
    </if>
    <if test="type != null">
      and TYPE = #{type}
    </if>
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_GE_AUTHORIZATION_CERTIFICATE
    where AUTHORIZATION_CERTIFICATE_ID = #{authorizationCertificateId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="AUTHORIZATION_CERTIFICATE_ID" keyProperty="authorizationCertificateId" parameterType="com.vedeng.erp.buyorder.domain.entity.GeAuthorizationCertificate" useGeneratedKeys="true">
    insert into T_GE_AUTHORIZATION_CERTIFICATE (AUTHORIZATION_ID, `TYPE`, `NAME`, 
      `DOMAIN`, URI, EXTRA, 
      OSS_RESOURCE_ID, IS_DELETE, SUFFIX, 
      CREATOR, ADD_TIME, UPDATER, 
      MOD_TIME)
    values (#{authorizationId,jdbcType=INTEGER}, #{type,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, 
      #{domain,jdbcType=VARCHAR}, #{uri,jdbcType=VARCHAR}, #{extra,jdbcType=VARCHAR}, 
      #{ossResourceId,jdbcType=VARCHAR}, #{isDelete,jdbcType=INTEGER}, #{suffix,jdbcType=VARCHAR}, 
      #{creator,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER}, 
      #{modTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="AUTHORIZATION_CERTIFICATE_ID" keyProperty="authorizationCertificateId" parameterType="com.vedeng.erp.buyorder.domain.entity.GeAuthorizationCertificate" useGeneratedKeys="true">
    insert into T_GE_AUTHORIZATION_CERTIFICATE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="authorizationId != null">
        AUTHORIZATION_ID,
      </if>
      <if test="type != null">
        `TYPE`,
      </if>
      <if test="name != null">
        `NAME`,
      </if>
      <if test="domain != null">
        `DOMAIN`,
      </if>
      <if test="uri != null">
        URI,
      </if>
      <if test="extra != null">
        EXTRA,
      </if>
      <if test="ossResourceId != null">
        OSS_RESOURCE_ID,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="suffix != null">
        SUFFIX,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="authorizationId != null">
        #{authorizationId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="domain != null">
        #{domain,jdbcType=VARCHAR},
      </if>
      <if test="uri != null">
        #{uri,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
      <if test="ossResourceId != null">
        #{ossResourceId,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="suffix != null">
        #{suffix,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.buyorder.domain.entity.GeAuthorizationCertificate">
    update T_GE_AUTHORIZATION_CERTIFICATE
    <set>
      <if test="authorizationId != null">
        AUTHORIZATION_ID = #{authorizationId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `TYPE` = #{type,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        `NAME` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="domain != null">
        `DOMAIN` = #{domain,jdbcType=VARCHAR},
      </if>
      <if test="uri != null">
        URI = #{uri,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        EXTRA = #{extra,jdbcType=VARCHAR},
      </if>
      <if test="ossResourceId != null">
        OSS_RESOURCE_ID = #{ossResourceId,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="suffix != null">
        SUFFIX = #{suffix,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where AUTHORIZATION_CERTIFICATE_ID = #{authorizationCertificateId,jdbcType=INTEGER}
  </update>
  <update id="updateByParam" parameterType="com.vedeng.erp.buyorder.domain.entity.GeAuthorizationCertificate">
    update T_GE_AUTHORIZATION_CERTIFICATE
    set IS_DELETE = 1
    where AUTHORIZATION_ID = #{authorizationId}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.buyorder.domain.entity.GeAuthorizationCertificate">
    update T_GE_AUTHORIZATION_CERTIFICATE
    set AUTHORIZATION_ID = #{authorizationId,jdbcType=INTEGER},
      `TYPE` = #{type,jdbcType=INTEGER},
      `NAME` = #{name,jdbcType=VARCHAR},
      `DOMAIN` = #{domain,jdbcType=VARCHAR},
      URI = #{uri,jdbcType=VARCHAR},
      EXTRA = #{extra,jdbcType=VARCHAR},
      OSS_RESOURCE_ID = #{ossResourceId,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      SUFFIX = #{suffix,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP}
    where AUTHORIZATION_CERTIFICATE_ID = #{authorizationCertificateId,jdbcType=INTEGER}
  </update>
</mapper>