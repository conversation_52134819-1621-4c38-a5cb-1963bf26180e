package com.vedeng.erp.finance.api;

import com.vedeng.erp.finance.dto.InvoiceFileDto;
import com.vedeng.erp.finance.dto.VoucherDto;

import java.util.List;

/**
 * 通过桌面端的提效工具
 */
public interface InvoiceFileApi {

    /**
     *
     * @param invoiceFileDto 发票及附件dto
     * @return
     */
    boolean handleInvoiceFile(InvoiceFileDto invoiceFileDto);
    /**
     * 根据发票号到T_INVOICE表的INVOICE_NO字段查询是否存在
     */
    boolean getInvoiceByInvoiceNoAndTag(String invoiceNo,Integer tag);

    /**
     * 根据发票号到T_INVOICE_FILE表中的INVOICE_NO字段查询是否有已上传的发票附件
     */
    boolean getInvoiceFileByInvoiceNo(String invoiceNo);

    /**
     * 查询款凭证或票凭证主键id
     */
    VoucherDto judgeTransactionOrInvoice(String voucherDate, String voucherNo);

    /**
     * 更新凭证url
     * @param voucherDto
     * @param fileUrl
     * @param voucherDate
     * @param voucherNo
     * @return 变更行数
     */
    int updateVoucherUrl(VoucherDto voucherDto, String fileUrl,String voucherDate, String voucherNo);
}
