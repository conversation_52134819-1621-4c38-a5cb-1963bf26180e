$(function() {
	$("#submit").click(function(){
		checkLogin();
		$(".warning").remove();
		$("input").removeClass("errorbor");
		if($("#traderContactId").val() == 0){
			warnTips("traderContactId","联系人不允许为空");
			return false;
		}

		if($("#begin").val() == '' || $("#end").val() == ''){
			warnTips("end"," 沟通时间不允许为空");
			return false;
		}

		/*	2020.12http://jira.ivedeng.com/browse/VDERP-5008 需求中要求删除的字段*/
		/*var communicateGoal=$('input:radio[name="communicateGoal"]:checked').val();
		if(communicateGoal==undefined||communicateGoal==""){
			$("#communicateGoal").css("display","");
			return false;
		}else{
			$("#communicateGoal").css("display","none");
		}
		
		var communicateMode=$('input:radio[name="communicateMode"]:checked').val();
		if(communicateMode==undefined||communicateMode==""){
			$("#communicateMode").css("display","");
			return false;
		}else{
			$("#communicateMode").css("display","none");
		}*/
		
		if($("input[name='tagId']").length == 0 && $("input[name='tagName']").length == 0 && $("textarea[name='contentSuffix']").val() == ''){
			warnTips("tag_show_ul"," 沟通内容不允许为空");
			return false;
		}

		if ($("textarea[name='contentSuffix']").val().length > 200){
			warnTips("contentSuffixError"," 沟通内容最多输入200字符，请检查后提交");
			return false;
		}

		var nextDate = $("input[name='nextDate']").val();
		if (! $('#noneNextDate').is(':checked') && nextDate.length == 0){
			warnTips("nextDate"," 下次沟通时间不允许为空");
			return false;
		}
		if(nextDate.length != 0 && $("#begin").val()>=nextDate+' 23:59:59'){
			warnTips("nextDate"," 下次沟通时间不能在沟通时间之前");
			return false;
		}
		
		if($("#nextContactContent").val().length > 256){
			warnTips("nextContactContent"," 下次沟通内容长度不允许超过256个字符");
			return false;
		}
		if($("#comments").val().length > 128){
			warnTips("comments"," 备注长度不允许超过128个字符");
			return false;
		}
		$.ajax({
			url:page_url+'/order/saleorder/saveCommunicate.do',
			data:$('#addCommunicate').serialize(),
			type:"POST",
			dataType : "json",
			async: false,
			success:function(data){
				$('#cancle').click();
				parent.location.reload();
				/*
				layer.alert(data.message, {
					icon : (data.code == 0 ? 1 : 2)
				}, function() {
					$('#cancle').click();
					parent.location.reload();
				});*/
				
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
		return false;
	});

	$('#noneNextDate').click(function () {
		var noneNextDate = $('#noneNextDate').is(':checked');
		if (noneNextDate){
			$('#nextDate').val('');
			$('#nextDate').css('background-color', '#CCCCCC');
			$('#nextDate').attr('disabled', true);
			$('#noneNextDateVal').val(1);
		} else {
			$('#nextDate').css('background-color', 'white');
			$('#nextDate').attr('disabled', false);
			$('#noneNextDateVal').val(0);
		}
	})
});
