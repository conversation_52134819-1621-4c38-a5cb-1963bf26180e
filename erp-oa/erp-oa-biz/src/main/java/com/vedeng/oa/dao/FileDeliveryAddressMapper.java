package com.vedeng.oa.dao;

import com.vedeng.oa.model.FileDeliveryAddress;
import com.vedeng.oa.model.FileDeliveryAddressExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FileDeliveryAddressMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_ADDRESS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    long countByExample(FileDeliveryAddressExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_ADDRESS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    int deleteByExample(FileDeliveryAddressExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_ADDRESS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_ADDRESS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    int insert(FileDeliveryAddress record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_ADDRESS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    int insertSelective(FileDeliveryAddress record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_ADDRESS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    List<FileDeliveryAddress> selectByExample(FileDeliveryAddressExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_ADDRESS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    FileDeliveryAddress selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_ADDRESS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    int updateByExampleSelective(@Param("record") FileDeliveryAddress record, @Param("example") FileDeliveryAddressExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_ADDRESS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    int updateByExample(@Param("record") FileDeliveryAddress record, @Param("example") FileDeliveryAddressExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_ADDRESS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    int updateByPrimaryKeySelective(FileDeliveryAddress record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_ADDRESS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    int updateByPrimaryKey(FileDeliveryAddress record);
}