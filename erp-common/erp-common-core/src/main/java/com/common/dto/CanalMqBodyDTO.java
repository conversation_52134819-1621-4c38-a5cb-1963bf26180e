package com.common.dto;

import lombok.Data;

/**
 * @Author: daniel
 * @Date: 2021/3/16 16 40
 * @Description: Canal监听的消息体
 */
@Data
public class CanalMqBodyDTO {

    /**
     * Binlog操作类型：INSERT UPDATE DELETE
     */
    private String type;

    /**
     * canal接收到binlog时的时间戳
     */
    private Long ts;

    /**
     * 数据变动的表名
     */
    private String table;

    private Object sqlType;

    private String sql;

    /**
     * 主键名称
     */
    private String[] pkNames;

    /**
     * 老数据
     */
    private Object[] old;

    /**
     * mysql数据类型
     */
    private Object mysqlType;

    private Boolean isDdl;

    private Integer id;

    /**
     * binlog的时间戳
     */
    private Long es;

    /**
     * 数据库
     */
    private String database;

    /**
     * 更新后的数据集
     */
    private Object[] data;
}
