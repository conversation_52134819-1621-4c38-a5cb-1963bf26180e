package com.vedeng.logistics.dao;

import com.vedeng.logistics.model.WmsOutputOrderGoodsExtra;

import java.util.List;

public interface WmsOutputOrderGoodsExtraMapper {
    /**
     * delete by primary key
     * @param warehouseGoodsExtraId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long warehouseGoodsExtraId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(WmsOutputOrderGoodsExtra record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(WmsOutputOrderGoodsExtra record);

    /**
     * select by primary key
     * @param warehouseGoodsExtraId primary key
     * @return object by primary key
     */
    WmsOutputOrderGoodsExtra selectByPrimaryKey(Long warehouseGoodsExtraId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(WmsOutputOrderGoodsExtra record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(WmsOutputOrderGoodsExtra record);

    List<WmsOutputOrderGoodsExtra> selectWmsExtraByLendOutId(WmsOutputOrderGoodsExtra wmsOutputOrderGoodsExtra);
}