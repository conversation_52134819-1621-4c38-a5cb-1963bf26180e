<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="客户列表" scope="application"/>
<%@ include file="../../common/common.jsp" %>

<script type="text/javascript"
        src="<%=basePath%>/static/js/customer/index.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="<%=basePath%>/static/js/region/index.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/webjars/ezadmin/plugins/layui/layui.js"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/webjars/ezadmin/plugins/cascader/xm-select.js"></script>
<link rel="stylesheet" href="${pageContext.request.contextPath}/webjars/ezadmin/plugins/layui/css/layui.css"/>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/webjars/ezadmin/plugins/cascader/cascader.js?rnd=${resourceVersionKey}"></script>
<link rel="stylesheet" href="${pageContext.request.contextPath}/webjars/ezadmin/plugins/cascader/cascader.css"/>
<script type="text/javascript" src='<%=basePath%>static/js/jquery.cookie.js'></script>

<style>
    .list-pages-search .el-cascader {
        line-height: 26px;
        height: 26px;
        width: 70%
    }
    .el-cascader__search-input {
        margin-top: 2px;
    }
    .el-cascader .el-input .el-input__inner {
        height: 26px;
        line-height: 26px;
    }

    .el-cascader i, .el-cascader-panel i {
        height: auto;
        background: none;
    }

    .el-cascader .el-cascader__tags {
        flex-wrap: inherit;
    }

    a,
    a:hover,
    a:focus {
        text-decoration: none;
        outline-style: none;
        color: #3384ef;
        cursor: pointer;
    }

    .layui-text a:not(.layui-btn) {
        color: #3384ef;
    }

    #showBtn .layui-icon {
        background: none;
        height: auto;
        margin-bottom: 0;
    }

    .search-list-wrap li {
        height: 28px;
    }
    .xm-search i {
        background: none !important;
    }

</style>
<script>

    function showOrHideBasicInformationDiv() {
        var data = $("#basicInformationDivShowBtn").attr("data");
        if (data == "hidden") {
            $("#basicInformationDiv").css({ overflow: 'visible','padding-left':'5px','flex-grow': '1',height:'auto'});
            $("#basicInformationDivShowBtn").attr("data", "show");
            $("#basicInformationDivShowBtn").html("<span style=\"font-weight:bold \">基本信息</span><i class=\"layui-icon layui-icon-up\" style=\"font-size: 12px !important;  background: none;display: initial !important;\"></i>");
            $.cookie('cus_show_button_basicInformationDivShowBtn', 'show');
        } else {
            $("#basicInformationDiv").css({height: '32px', overflow: 'hidden','padding-left':'5px','flex-grow': '1'});
            $("#basicInformationDivShowBtn").attr("data", "hidden");
            $.cookie('cus_show_button_basicInformationDivShowBtn', 'hidden');
            $("#basicInformationDivShowBtn").html("<span style=\"font-weight:bold \">基本信息</span><i class=\"layui-icon  layui-icon-down\" style=\"font-size: 12px !important;  background: none;display: initial !important;\"></i>");
        }
    }

    function showOrHidecpmDivShowBtn() {
        //debugger
        var data = $("#cpmDivShowBtn").attr("data");
        if (data == "hidden") {
            $("#cpmDiv").css({ overflow: 'visible','padding-left':'5px','flex-grow': '1',height:'auto'});
            $("#cpmDivShowBtn").attr("data", "show");
            $("#cpmDivShowBtn").html("<span style=\"font-weight:bold \">CPM标签</span><i class=\"layui-icon layui-icon-up\" style=\"font-size: 12px !important;  background: none;display: initial !important;\"></i>");
            $.cookie('cus_show_button_cpmDivShowBtn', 'show');
        } else {
            $("#cpmDiv").css({height: '32px', overflow: 'hidden','padding-left':'5px','flex-grow': '1'});
            $("#cpmDivShowBtn").attr("data", "hidden");
            $("#cpmDivShowBtn").html("<span style=\"font-weight:bold \">CPM标签</span><i class=\"layui-icon  layui-icon-down\" style=\"font-size: 12px !important;  background: none;display: initial !important;\"></i>");
            $.cookie('cus_show_button_cpmDivShowBtn', 'hidden');
        }
    }

    function showOrHidedecisionDivShowBtn() {
        //debugger
        var data = $("#decisionDivShowBtn").attr("data");
        if (data == "hidden") {
            $("#decisionDiv").css({ overflow: 'visible','padding-left':'0px','flex-grow': '1',height:'auto'});
            $("#decisionDivShowBtn").attr("data", "show");
            $("#decisionDivShowBtn").html("<span style=\"font-weight:bold \">决策标签</span><i class=\"layui-icon layui-icon-up\" style=\"font-size: 12px !important;  background: none;display: initial !important;\"></i>");
            $.cookie('cus_show_button_decisionDivShowBtn', 'show');
        } else {
            $("#decisionDiv").css({height: '32px', overflow: 'hidden','padding-left':'0px','flex-grow': '1'});
            $("#decisionDivShowBtn").attr("data", "hidden");
            $("#decisionDivShowBtn").html("<span style=\"font-weight:bold \">决策标签</span><i class=\"layui-icon  layui-icon-down\" style=\"font-size: 12px !important;  background: none;display: initial !important;\"></i>");
            $.cookie('cus_show_button_decisionDivShowBtn', 'hidden');
        }
    }

    function showOrHidetradingInquiryDivShowBtn() {
        //debugger
        var data = $("#tradingInquiryDivShowBtn").attr("data");
        if (data == "hidden") {
            $("#tradingInquiryDiv").css({ overflow: 'visible','padding-left':'0px','flex-grow': '1',height:'auto'});
            $("#tradingInquiryDivShowBtn").attr("data", "show");
            $("#tradingInquiryDivShowBtn").html("<span style=\"font-weight:bold \">交易&询价标签</span><i class=\"layui-icon layui-icon-up\" style=\"font-size: 12px !important;  background: none;display: initial !important;\"></i>");
            $.cookie('cus_show_button_tradingInquiryDivShowBtn', 'show');
        } else {
            $("#tradingInquiryDiv").css({height: '32px', overflow: 'hidden','padding-left':'0px','flex-grow': '1'});
            $("#tradingInquiryDivShowBtn").attr("data", "hidden");
            $("#tradingInquiryDivShowBtn").html("<span style=\"font-weight:bold \">交易&询价标签</span><i class=\"layui-icon  layui-icon-down\" style=\"font-size: 12px !important;  background: none;display: initial !important;\"></i>");
            $.cookie('cus_show_button_tradingInquiryDivShowBtn', 'hidden');
        }
    }

    $(document).ready(function () {
        var basicInformationDivShowBtnValue = $.cookie("cus_show_button_basicInformationDivShowBtn");
        if (basicInformationDivShowBtnValue != undefined && basicInformationDivShowBtnValue == "show") {
            $("#basicInformationDivShowBtn").attr("data", "hidden");
            showOrHideBasicInformationDiv();
        }

        //debugger;

        var cpmDivShowBtnValue = $.cookie("cus_show_button_cpmDivShowBtn");
        if (cpmDivShowBtnValue != undefined && cpmDivShowBtnValue == "show") {
            $("#cpmDivShowBtn").attr("data", "hidden");
            showOrHidecpmDivShowBtn();
        }

        var decisionDivShowBtnValue = $.cookie("cus_show_button_decisionDivShowBtn");
        if (decisionDivShowBtnValue != undefined && decisionDivShowBtnValue == "show") {
            $("#decisionDivShowBtn").attr("data", "hidden");
            showOrHidedecisionDivShowBtn();
        }

        var tradingInquiryDivShowBtnValue = $.cookie("cus_show_button_tradingInquiryDivShowBtn");
        if (tradingInquiryDivShowBtnValue != undefined && tradingInquiryDivShowBtnValue == "show") {
            $("#tradingInquiryDivShowBtn").attr("data", "hidden");
            showOrHidetradingInquiryDivShowBtn();
        }


    })

</script>

<div class="main-container">
    <div class="list-pages-search">
        <form action="${pageContext.request.contextPath}/trader/customer/index.do" method="post" id="search">
            <input type="hidden" name="search" value="click">
            <input type="hidden" name="categoryList" id="selectCategoryList" value="${traderCustomerVo.categoryList}">
            <input type="hidden" name="selectLabelPerfectionValue" id="selectLabelPerfectionValue" value="${traderCustomerVo.selectLabelPerfectionValue}">
            <input type="hidden" name="selectLabelPerfectionChildValue" id="selectLabelPerfectionChildValue" value="${traderCustomerVo.selectLabelPerfectionChildValue}">
            <input type="hidden" name="selectLabelPerfectionChildValueOne" id="selectLabelPerfectionChildValueOne" value="${traderCustomerVo.selectLabelPerfectionChildValueOne}">
            <input type="hidden" name="businessTerminalCategoriesAndType" id="selectBusinessTerminalCategoriesAndType"
                   value="${traderCustomerVo.businessTerminalCategoriesAndType}">
            <input type="hidden" name="traderCustomerMainCategory" id="selectTraderCustomerMainCategory" value="${traderCustomerVo.traderCustomerMainCategory}">
            <input type="hidden" name="traderCustomerTrade" id="selectTraderCustomerTrade"
                   value="${traderCustomerVo.traderCustomerTrade}">
            <input type="hidden" name="institutionLevel" id="selectInstitutionLevel"
                   value="${traderCustomerVo.institutionLevel}">
            <input type="hidden" name="traderCustomerBrand" id="selectTraderCustomerBrand"
                   value="${traderCustomerVo.traderCustomerBrand}">
            <input type="hidden" name="selectTraderCustomerBrandList" id="selectTraderCustomerBrandList">

            <input type="hidden" name="selectAgencyBrandList" id="selectAgencyBrandList">
            <input type="hidden" name="agencyBrandIdList" id="agencyBrandIdList" value="${traderCustomerVo.agencyBrandIdList}">
            <input type="hidden" name="agencySkuList" id="selectAgencySkuList" value="${traderCustomerVo.agencySkuList}">

            <input type="hidden" name="sharedSaleId" id="sharedSaleId" value="${traderCustomerVo.sharedSaleId}">
            <input type="hidden" name="saleList" id="saleList">

            <div style="display: flex">
                <div style="width: 100px">
                    <button type="button" style="border: none" id="basicInformationDivShowBtn" href="javascript:void(0)" data="hidden"
                            onclick="javascript:showOrHideBasicInformationDiv()" class="layui-btn layui-btn-primary layui-btn-sm"
                            data-placement="top" title="展开" aria-invalid="false">
                        <span style="font-weight:bold ">基本信息</span>
                        <i class="layui-icon layui-icon-down"
                           style="font-size: 12px !important;  background: none;display: initial !important;"></i>
                    </button>
                </div>
                <div id="basicInformationDiv" style="height: 32px; overflow: hidden;padding-left:0px;flex-grow: 1">


                    <ul class="search-list-wrap">
                        <li>
                            <label class="infor_name">客户名称</label>
                            <input type="text" class="input-middle" name="name" id="name" value="${traderCustomerVo.name}">
                        </li>
                        <li style="width: 282px">
                            <label class="infor_name">客户类型</label>
                            <input id="customerCategoryTypeSelect" style="width: 63%">
                        </li>
                        <li><label class="infor_name">经销商有效性</label> <select
                                class="input-middle f_left" name="effectiveness">
                            <option value="">全部</option>
                            <option value="1"
                                    <c:if test="${traderCustomerVo.effectiveness eq 1 }">selected="selected"</c:if>>有效
                            </option>
                            <option value="0"
                                    <c:if test="${traderCustomerVo.effectiveness eq 0 }">selected="selected"</c:if>>无效
                            </option>
                        </select></li>
                        <li>
                            <label class="infor_name">归属销售</label>
                            <select class="input-middle f_left" name="homePurchasing">
                                <option value="">全部</option>
                                <c:if test="${not empty userList }">
                                    <c:forEach items="${userList }" var="user">
                                        <option value="${user.userId }"
                                                <c:if test="${traderCustomerVo.homePurchasing eq user.userId }">selected="selected"</c:if>>${user.username }</option>
                                    </c:forEach>
                                </c:if>
                            </select>
                        </li>

                        <li>
                            <label class="infor_name">归属平台</label>
                            <select class="input-middle f_left" name="belongPlatform" style="width: 100px">
                                <option value="">全部</option>
                                <option value="1"
                                        <c:if test="${traderCustomerVo.belongPlatform eq 1 }">selected="selected"</c:if>>
                                    贝登医疗
                                </option>
                                <option value="2"
                                        <c:if test="${traderCustomerVo.belongPlatform eq 2 }">selected="selected"</c:if>>医械购
                                </option>
                                <option value="3"
                                        <c:if test="${traderCustomerVo.belongPlatform eq 3 }">selected="selected"</c:if>>科研购
                                </option>
                                <option value="5"
                                        <c:if test="${traderCustomerVo.belongPlatform eq 5 }">selected="selected"</c:if>>其他
                                </option>
                                <option value="6"
                                        <c:if test="${traderCustomerVo.belongPlatform eq 6 }">selected="selected"</c:if>>集采
                                </option>
                            </select>
                        </li>

                        <li><label class="infor_name">客户等级</label> <select
                                class="input-middle f_left" name="traderCustomerLevelGrade">
                            <option value="">全部</option>
                            <c:if test="${traderCustomerVo.belongPlatform eq 1}">
                                <c:forEach items="${traderCustomerLevelGradeList}" var="level">
                                    <option value="${level}" <c:if test="${traderCustomerVo.traderCustomerLevelGrade == level}">selected="selected"</c:if>>${level}</option>
                                </c:forEach>
                            </c:if>
                            <c:if test="${traderCustomerVo.belongPlatform != null and traderCustomerVo.belongPlatform  !=1  }">
                                <c:forEach items="${traderCustomerLevelGradeOriginal}" var="level">
                                    <option value="${level}" <c:if test="${traderCustomerVo.traderCustomerLevelGrade == level}">selected="selected"</c:if>>${level}</option>
                                </c:forEach>
                            </c:if>

                        </select></li>

                        <li>
                            <label class="infor_name">客户接收人</label>
                            <div id="sharedSale" style="width: 220px; margin-left: 100px" ></div>
                        </li>

                        <li><label class="infor_name">有无手机</label> <select
                                class="input-middle f_left" name="traderCustomerIsMobile">
                            <option value="">全部</option>
                            <option value="1"
                                    <c:if test="${traderCustomerVo.traderCustomerIsMobile eq 1 }">selected="selected"</c:if>>
                                有手机
                            </option>
                            <option value="2"
                                    <c:if test="${traderCustomerVo.traderCustomerIsMobile eq 2 }">selected="selected"</c:if>>
                                无手机
                            </option>
                        </select></li>
                        <li><label class="infor_name">联系方式</label> <input type="text"
                                                                          class="input-middle" placeholder="电话/手机/QQ/微信/邮箱"
                                                                          name="contactWay"
                                                                          id="contactWay"
                                                                          value="${traderCustomerVo.contactWay}"></li>

                        <li><label class="infor_name">是否公海</label> <select
                                class="input-middle f_left" name="traderCustomerIsSea">
                            <option value="">全部</option>
                            <option value="1"
                                    <c:if test="${traderCustomerVo.traderCustomerIsSea eq 1 }">selected="selected"</c:if>>
                                公海客户
                            </option>
                            <option value="2"
                                    <c:if test="${traderCustomerVo.traderCustomerIsSea eq 2 }">selected="selected"</c:if>>
                                非公海客户
                            </option>
                        </select></li>


                        <li><label class="infor_name">贝登会员</label> <select
                                class="input-middle f_left" name="traderCustomerIsMember">
                            <option value="">全部</option>
                            <option value="1"
                                    <c:if test="${traderCustomerVo.traderCustomerIsMember eq 1 }">selected="selected"</c:if>>
                                贝登会员
                            </option>
                            <option value="0"
                                    <c:if test="${traderCustomerVo.traderCustomerIsMember eq 0 }">selected="selected"</c:if>>
                                非贝登会员
                            </option>
                        </select></li>

                        <li><label class="infor_name">审核状态</label> <select
                                class="input-middle f_left" name="customerStatus">
                            <option value="">全部</option>
                            <option value="3"
                                    <c:if test="${traderCustomerVo.customerStatus eq 3 }">selected="selected"</c:if>>
                                待审核
                            </option>
                            <option value="0"
                                    <c:if test="${traderCustomerVo.customerStatus eq 0 }">selected="selected"</c:if>>
                                审核中
                            </option>
                            <option value="1"
                                    <c:if test="${traderCustomerVo.customerStatus eq 1 }">selected="selected"</c:if>>
                                审核通过
                            </option>
                            <option value="2"
                                    <c:if test="${traderCustomerVo.customerStatus eq 2 }">selected="selected"</c:if>>
                                审核不通过
                            </option>
                        </select></li>

                        <li><label class="infor_name">资质审核状态</label>
                            <select class="input-middle f_left" name="aptitudeStatus">
                                <option value="">全部</option>
                                <option value="3"
                                        <c:if test="${traderCustomerVo.aptitudeStatus eq 3 }">selected="selected"</c:if>>待审核
                                </option>
                                <option value="4"
                                        <c:if test="${traderCustomerVo.aptitudeStatus eq 4 }">selected="selected"</c:if>>初审中
                                </option>
                                <option value="5"
                                        <c:if test="${traderCustomerVo.aptitudeStatus eq 5 }">selected="selected"</c:if>>
                                    公章审核
                                </option>
                                <option value="0"
                                        <c:if test="${traderCustomerVo.aptitudeStatus eq 0 }">selected="selected"</c:if>>复审中
                                </option>
                                <option value="1"
                                        <c:if test="${traderCustomerVo.aptitudeStatus eq 1 }">selected="selected"</c:if>>
                                    审核通过
                                </option>
                                <option value="2"
                                        <c:if test="${traderCustomerVo.aptitudeStatus eq 2 }">selected="selected"</c:if>>
                                    审核不通过
                                </option>
                            </select></li>

                        <li>
                            <label class="infor_name">税号审核状态</label>
                            <select class="input-middle f_left" name="financeCheckStatus">
                                <option value="">全部</option>
                                <option value="0"
                                        <c:if test="${traderCustomerVo.financeCheckStatus eq 0 }">selected="selected"</c:if>>
                                    审核中
                                </option>
                                <option value="1"
                                        <c:if test="${traderCustomerVo.financeCheckStatus eq 1 }">selected="selected"</c:if>>
                                    审核通过
                                </option>
                                <option value="2"
                                        <c:if test="${traderCustomerVo.financeCheckStatus eq 2 }">selected="selected"</c:if>>
                                    审核不通过
                                </option>
                            </select>
                        </li>

                        <li>
                            <label class="infor_name">客户提醒</label>
                            <select class="input-middle f_left" name="customerAlert">
                                <option value="">全部</option>
                                <option value="1"
                                        <c:if test="${traderCustomerVo.customerAlert eq 1 }">selected="selected"</c:if>>公海预警
                                </option>
                                <option value="2"
                                        <c:if test="${traderCustomerVo.customerAlert eq 2 }">selected="selected"</c:if>>公海锁定
                                </option>
                                <option value="3"
                                        <c:if test="${traderCustomerVo.customerAlert eq 3 }">selected="selected"</c:if>>无需提醒
                                </option>
                            </select>
                        </li>

                        <li>
                            <div class="infor_name" style='margin-top:0px; width: 120px;'>
                                <select name="areaType">
                                    <option value="3"
                                            <c:if test="${traderCustomerVo.areaType eq 3 }">selected="selected"</c:if>>经营区域
                                    </option>
                                    <option value="1"
                                            <c:if test="${traderCustomerVo.areaType eq 1 }">selected="selected"</c:if>>注册地区
                                    </option>
                                    <option value="2"
                                            <c:if test="${traderCustomerVo.areaType eq 2 }">selected="selected"</c:if>>仓库地区
                                    </option>

                                </select>
                            </div>

                            <select class="wid9" name="province" id="province">
                                <option value="0">全部</option>
                                <c:if test="${not empty provinceList }">
                                    <c:forEach items="${provinceList }" var="prov">
                                        <option value="${prov.regionId }"
                                                <c:if test="${traderCustomerVo.province eq prov.regionId }">selected="selected"</c:if>>${prov.regionName }</option>
                                    </c:forEach>
                                </c:if>
                            </select>
                            <select class="wid9" name="city" id="city">
                                <option value="0">全部</option>
                                <c:if test="${not empty cityList }">
                                    <c:forEach items="${cityList }" var="cy">
                                        <option value="${cy.regionId }"
                                                <c:if test="${traderCustomerVo.city eq cy.regionId }">selected="selected"</c:if>>${cy.regionName }</option>
                                    </c:forEach>
                                </c:if>
                            </select>
                            <select class="wid9" name="zone" id="zone">
                                <option value="0">全部</option>
                                <c:if test="${not empty zoneList }">
                                    <c:forEach items="${zoneList }" var="zo">
                                        <option value="${zo.regionId }"
                                                <c:if test="${traderCustomerVo.zone eq zo.regionId }">selected="selected"</c:if>>${zo.regionName }</option>
                                    </c:forEach>
                                </c:if>
                            </select>
                        </li>


                        <li>
                            <div class="infor_name" style='margin-top:0px; width: 120px;'>
                                <select name="timeType">
                                    <option value="2"
                                            <c:if test="${traderCustomerVo.timeType eq 2 }">selected="selected"</c:if>>交易时间
                                    </option>
                                    <option value="1"
                                            <c:if test="${traderCustomerVo.timeType eq 1 }">selected="selected"</c:if>>创建时间
                                    </option>
                                    <option value="3"
                                            <c:if test="${traderCustomerVo.timeType eq 3 }">selected="selected"</c:if>>更新时间
                                    </option>
                                    <option value="4"
                                            <c:if test="${traderCustomerVo.timeType eq 4 }">selected="selected"</c:if>>
                                        最近沟通时间
                                    </option>
                                </select>
                            </div>
                            <input class="Wdate f_left input-smaller96 mr5" type="text" autocomplete="off"
                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endTime\')}'})"
                                   name="queryStartTime" id="startTime" value=<date:date
                                    value="${traderCustomerVo.startTime} "/>>
                            <div class="gang">-</div>
                            <input class="Wdate f_left input-smaller96" type="text" autocomplete="off"
                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'startTime\')}'})"
                                   name="queryEndTime" id="endTime" value=<date:date value="${traderCustomerVo.endTime} "/>>
                        </li>


                        <li>
                            <label class="infor_name">标签完善度</label>
                            <input id="labelPerfectionSelect" style="width: 63%"/>


                        </li>
                        <li>
                            <div id="reRender">
                                <input id="labelPerfectionChildSelect" style="width: 63%"/>
                            </div>

                        </li>

                        <li>
                            <label class="infor_name">人工更新标签</label>
                            <select name="manualUpdate">
                                <option value=""
                                        <c:if test="${traderCustomerVo.manualUpdate ==null }">selected="selected"</c:if>>全部
                                </option>
                                <option value="1"
                                        <c:if test="${traderCustomerVo.manualUpdate eq 1 }">selected="selected"</c:if>>是
                                </option>
                                <option value="0"
                                        <c:if test="${traderCustomerVo.manualUpdate eq 0 }">selected="selected"</c:if>>否
                                </option>
                            </select>
                        </li>

                    </ul>
                </div>
            </div>


            <div style="display: flex">
                <div style="width: 100px">
                    <button type="button" style="border: none" id="cpmDivShowBtn" href="javascript:void(0)" data="hidden"
                            onclick="javascript:showOrHidecpmDivShowBtn()" class="layui-btn layui-btn-primary layui-btn-sm"
                            data-placement="top" title="展开" aria-invalid="false">
                        <span style="font-weight:bold ">CPM标签</span>
                        <i class="layui-icon layui-icon-down"
                           style="font-size: 12px !important;  background: none;display: initial !important;"></i>
                    </button>
                </div>
                <div id="cpmDiv" style="height: 32px; overflow: hidden;padding-left:0px;flex-grow: 1">

                    <ul>
                        <li style="width: 282px">
                            <label class="infor_name">经营终端类型</label>
                            <input id="businessTerminalCategoriesAndTypesSelect" style="width: 63%">
                        </li>

                        <li><label class="infor_name">终端机构性质</label>
                            <select
                                    class="input-middle f_left" name="institutionNature">
                                <option value="">全部</option>
                                <option value="0"
                                        <c:if test="${traderCustomerVo.institutionNature eq 0 }">selected="selected"</c:if>>
                                    公立
                                </option>
                                <option value="1"
                                        <c:if test="${traderCustomerVo.institutionNature eq 1 }">selected="selected"</c:if>>
                                    非公
                                </option>
                            </select>
                        </li>

                        <li style="width: 330px">
                            <label class="infor_name">终端机构评级</label>
                            <input id="institutionLevel" style="width: 63%">
                        </li>
                        <%--<li> <div id="traderCustomerBrand" style="width: 220px" ></div></li>--%>


                        <li><label class="infor_name">主营商品类型</label> <select
                                class="input-middle f_left" name="skuType">
                            <option value="">全部</option>
                            <option value="0" <c:if test="${traderCustomerVo.skuType eq 0 }">selected="selected"</c:if>>设备
                            </option>
                            <option value="1" <c:if test="${traderCustomerVo.skuType eq 1 }">selected="selected"</c:if>>高值耗材
                            </option>
                            <option value="2" <c:if test="${traderCustomerVo.skuType eq 2 }">selected="selected"</c:if>>低值耗材
                            </option>
                            <option value="3" <c:if test="${traderCustomerVo.skuType eq 3 }">selected="selected"</c:if>>试剂
                            </option>
                            <option value="4" <c:if test="${traderCustomerVo.skuType eq 4 }">selected="selected"</c:if>>软件
                            </option>
                        </select></li>

                        <li><label class="infor_name">主营商品范畴</label>
                            <select class="input-middle f_left" id = "traderCustomerMainCategoryTypeId" name="traderCustomerMainCategoryType" style="width: 100px;">
                                <option value="">全部</option>
                                <option value="0" <c:if test="${traderCustomerVo.traderCustomerMainCategoryType eq 0 }">selected="selected"</c:if>>综合
                                </option>
                                <option value="1" <c:if test="${traderCustomerVo.traderCustomerMainCategoryType eq 1 }">selected="selected"</c:if>>专业
                                </option>
                            </select>
                        </li>
                        <div id="traderCustomerMainCategorySelectShow" style="display: none">
                            <li>
                                <input id="traderCustomerMainCategorySelect" style="width: 90%">
                            </li>
                        </div>

                        <li><label class="infor_name">销售类别</label> <select
                                class="input-middle f_left" name="traderCustomerOwnership">
                            <option value="">全部</option>
                            <option value="0"
                                    <c:if test="${traderCustomerVo.traderCustomerOwnership eq 0 }">selected="selected"</c:if>>
                                直销为主
                            </option>
                            <option value="1"
                                    <c:if test="${traderCustomerVo.traderCustomerOwnership eq 1 }">selected="selected"</c:if>>
                                分销为主
                            </option>
                            <option value="2"
                                    <c:if test="${traderCustomerVo.traderCustomerOwnership eq 2 }">selected="selected"</c:if>>
                                直分销并重
                            </option>
                        </select></li>

                        <li>
                            <label class="infor_name">核心资源</label>
                            <select class="input-middle f_left" name="traderCustomerDevelopLevel">
                                <option value="">全部</option>
                                <option value="1" <c:if test="${traderCustomerVo.traderCustomerDevelopLevel eq 1 }">selected="selected"</c:if>>
                                    产品型渠道商
                                </option>
                                <option value="2" <c:if test="${traderCustomerVo.traderCustomerDevelopLevel eq 2 }">selected="selected"</c:if>>
                                    客户型渠道商
                                </option>
                            </select>
                        </li>


                        <li>
                            <label class="infor_name">代理品牌</label>
                            <div id="agencyBrand" style="width: 220px; margin-left: 100px" ></div>
                        </li>
                        <li>
                            <label class="infor_name" style="width: 20px">or</label>
                            <input type="text" class="input-middle" name="otherAgencyBrand" id="otherAgencyBrand" placeholder="请输入其他关键词" value="${traderCustomerVo.otherAgencyBrand}">
                        </li>

                        <li>
                            <label class="infor_name">代理商品</label>
                            <input id="agencySkuCascader" style="width: 220px;">
                        </li>
                        <li>
                            <label class="infor_name" style="width: 20px">or</label>
                            <input type="text" class="input-middle" name="otherAgencySku" id="otherAgencySku" placeholder="请输入其他关键词" value="${traderCustomerVo.otherAgencySku}">
                        </li>

                        <li>
                            <label class="infor_name">客户型渠道商</label>
                            <select class="input-middle f_left" name="governmentRelation">
                                <option value="">全部</option>
                                <option value="3" <c:if test="${traderCustomerVo.governmentRelation eq '3' }">selected="selected"</c:if>>基层医疗关系</option>
                                <option value="4" <c:if test="${traderCustomerVo.governmentRelation eq '4' }">selected="selected"</c:if>>等级医院关系</option>
                                <option value="5" <c:if test="${traderCustomerVo.governmentRelation eq '5' }">selected="selected"</c:if>>应急医疗机构关系</option>
                                <option value="6" <c:if test="${traderCustomerVo.governmentRelation eq '6' }">selected="selected"</c:if>>专业公共卫生机构关系</option>
                                <option value="0"
                                        <c:if test="${traderCustomerVo.governmentRelation eq '0' }">selected="selected"</c:if>>
                                    卫健委关系
                                </option>
                                <option value="1"
                                        <c:if test="${traderCustomerVo.governmentRelation eq '1' }">selected="selected"</c:if>>
                                    医联体关系
                                </option>
                                <option value="2" <c:if test="${traderCustomerVo.governmentRelation eq '2' }">selected="selected"</c:if>>
                                    其他关系
                                </option>
                            </select>
                            <input type="text" class="input-middle" style="display: none" name="otherGovernmentRelation" id="otherGovernmentRelation" placeholder="请输入其他关键词" value="${traderCustomerVo.otherGovernmentRelation}">
                        </li>

                    </ul>
                </div>
            </div>


            <div style="display: flex">
                <div style="width: 100px">
                    <button type="button" style="border: none" id="decisionDivShowBtn" href="javascript:void(0)" data="hidden"
                            onclick="javascript:showOrHidedecisionDivShowBtn()" class="layui-btn layui-btn-primary layui-btn-sm"
                            data-placement="top" title="展开" aria-invalid="false">
                        <span style="font-weight:bold ">决策标签</span>
                        <i class="layui-icon layui-icon-down"
                           style="font-size: 12px !important;  background: none;display: initial !important;"></i>
                    </button>
                </div>
                <div id="decisionDiv" style="height: 32px; overflow: hidden;padding-left:0px;flex-grow: 1">

                    <ul>


                        <li><label class="infor_name">生命周期</label> <select
                                class="input-middle f_left" name="traderCustomerLifeCycle">
                            <option value="">全部</option>
                            <option value="导入期"
                                    <c:if test="${traderCustomerVo.traderCustomerLifeCycle == '导入期' }">selected="selected"</c:if>>
                                导入期
                            </option>
                            <option value="认证期"
                                    <c:if test="${traderCustomerVo.traderCustomerLifeCycle == '认证期' }">selected="selected"</c:if>>
                                认证期
                            </option>
                            <option value="首单期"
                                    <c:if test="${traderCustomerVo.traderCustomerLifeCycle == '首单期' }">selected="selected"</c:if>>
                                首单期
                            </option>
                            <option value="一次复购期"
                                    <c:if test="${traderCustomerVo.traderCustomerLifeCycle == '一次复购期' }">selected="selected"</c:if>>
                                一次复购期
                            </option>
                            <option value="二次复购期"
                                    <c:if test="${traderCustomerVo.traderCustomerLifeCycle == '二次复购期' }">selected="selected"</c:if>>
                                二次复购期
                            </option>
                            <option value="忠诚期"
                                    <c:if test="${traderCustomerVo.traderCustomerLifeCycle == '忠诚期' }">selected="selected"</c:if>>
                                忠诚期
                            </option>
                            <option value="休眠期"
                                    <c:if test="${traderCustomerVo.traderCustomerLifeCycle == '休眠期' }">selected="selected"</c:if>>
                                休眠期
                            </option>
                            <option value="流失期"
                                    <c:if test="${traderCustomerVo.traderCustomerLifeCycle == '流失期' }">selected="selected"</c:if>>
                                流失期
                            </option>
                        </select></li>

                    </ul>
                </div>
            </div>


            <div style="display: flex">
                <div style="width: 100px">
                    <button type="button" style="border: none" id="tradingInquiryDivShowBtn" href="javascript:void(0)" data="hidden"
                            onclick="javascript:showOrHidetradingInquiryDivShowBtn()" class="layui-btn layui-btn-primary layui-btn-sm"
                            data-placement="top" title="展开" aria-invalid="false">
                        <span style="font-weight:bold ">交易&询价标签</span>
                        <i class="layui-icon layui-icon-down"
                           style="font-size: 12px !important;  background: none;display: initial !important;"></i>
                    </button>
                </div>
                <div id="tradingInquiryDiv" style="height: 32px; overflow: hidden;padding-left:0px;flex-grow: 1">

                    <ul>


                        <li><label class="infor_name">交易分类</label> <select
                                class="input-middle f_left" name="traderCustomerTradeType">
                            <option value="0">近30天</option>
                            <option value="1"
                                    <c:if test="${traderCustomerVo.traderCustomerTradeType eq 1 }">selected="selected"</c:if>>
                                近60天
                            </option>
                            <option value="2"
                                    <c:if test="${traderCustomerVo.traderCustomerTradeType eq 2 }">selected="selected"</c:if>>
                                近90天
                            </option>
                            <option value="3"
                                    <c:if test="${traderCustomerVo.traderCustomerTradeType eq 3 }">selected="selected"</c:if>>
                                近180天
                            </option>
                            <option value="4"
                                    <c:if test="${traderCustomerVo.traderCustomerTradeType eq 4 }">selected="selected"</c:if>>
                                近365天
                            </option>
                            <option value="5"
                                    <c:if test="${traderCustomerVo.traderCustomerTradeType eq 5 }">selected="selected"</c:if>>
                                近730天
                            </option>
                        </select>
                        </li>
                        <li>
                            <input id="traderCustomerTrade">
                        </li>

                        <li><label class="infor_name">交易品牌</label>
                            <select
                                    class="input-middle f_left" name="traderCustomerBrandType">
                                <option value="0">近30天</option>
                                <option value="1"
                                        <c:if test="${traderCustomerVo.traderCustomerBrandType eq 1 }">selected="selected"</c:if>>
                                    近60天
                                </option>
                                <option value="2"
                                        <c:if test="${traderCustomerVo.traderCustomerBrandType eq 2 }">selected="selected"</c:if>>
                                    近90天
                                </option>
                                <option value="3"
                                        <c:if test="${traderCustomerVo.traderCustomerBrandType eq 3 }">selected="selected"</c:if>>
                                    近180天
                                </option>
                                <option value="4"
                                        <c:if test="${traderCustomerVo.traderCustomerBrandType eq 4 }">selected="selected"</c:if>>
                                    近365天
                                </option>
                                <option value="5"
                                        <c:if test="${traderCustomerVo.traderCustomerBrandType eq 5 }">selected="selected"</c:if>>
                                    近730天
                                </option>
                            </select>
                        </li>
                        <li> <div id="traderCustomerBrand" style="width: 220px" ></div>

                        <li><label class="infor_name">有合作</label> <select
                                class="input-middle f_left" name="cooperate">
                            <option value="">全部</option>
                            <option value="1" <c:if test="${traderCustomerVo.cooperate eq 1 }">selected="selected"</c:if>>是
                            </option>
                            <option value="2" <c:if test="${traderCustomerVo.cooperate eq 2 }">selected="selected"</c:if>>否
                            </option>
                        </select></li>

                        <li><label class="infor_name">有报价</label> <select
                                class="input-middle f_left" name="quote">
                            <option value="0">全部</option>
                            <option value="1" <c:if test="${traderCustomerVo.quote eq 1 }">selected="selected"</c:if>>是
                            </option>
                            <option value="2" <c:if test="${traderCustomerVo.quote eq 2 }">selected="selected"</c:if>>否
                        </select></li>




                        <li><label class="infor_name">沟通记录</label> <select
                                class="input-middle f_left" name="traderCustomerCommunicate">
                            <option value="">全部</option>
                            <option value="1"
                                    <c:if test="${traderCustomerVo.traderCustomerCommunicate eq 1 }">selected="selected"</c:if>>
                                近30天无沟通
                            </option>
                            <option value="2"
                                    <c:if test="${traderCustomerVo.traderCustomerCommunicate eq 2 }">selected="selected"</c:if>>
                                近60天无沟通
                            </option>
                            <option value="3"
                                    <c:if test="${traderCustomerVo.traderCustomerCommunicate eq 3 }">selected="selected"</c:if>>
                                近90天无沟通
                            </option>
                            <option value="4"
                                    <c:if test="${traderCustomerVo.traderCustomerCommunicate eq 4 }">selected="selected"</c:if>>
                                近180天无沟通
                            </option>
                            <option value="5"
                                    <c:if test="${traderCustomerVo.traderCustomerCommunicate eq 5 }">selected="selected"</c:if>>
                                近365天无沟通
                            </option>
                            <option value="6"
                                    <c:if test="${traderCustomerVo.traderCustomerCommunicate eq 6 }">selected="selected"</c:if>>
                                近730天无沟通
                            </option>
                        </select></li>
                        <li><label class="infor_name">历史沟通次数</label> <select
                                class="input-middle f_left" name="traderCustomerCommunicateTimes">
                            <option value="">全部</option>
                            <option value="1"
                                    <c:if test="${traderCustomerVo.traderCustomerCommunicateTimes eq 1 }">selected="selected"</c:if>>
                                0次
                            </option>
                            <option value="2"
                                    <c:if test="${traderCustomerVo.traderCustomerCommunicateTimes eq 2 }">selected="selected"</c:if>>
                                1次
                            </option>
                            <option value="3"
                                    <c:if test="${traderCustomerVo.traderCustomerCommunicateTimes eq 3 }">selected="selected"</c:if>>
                                2次~5次
                            </option>
                            <option value="4"
                                    <c:if test="${traderCustomerVo.traderCustomerCommunicateTimes eq 4 }">selected="selected"</c:if>>
                                5次以上
                            </option>
                        </select></li>
                        <li><label class="infor_name">信息搜索</label> <input type="text"
                                                                          class="input-middle" placeholder="沟通记录/商机信息"
                                                                          name="searchMsg" id=""
                                                                          value="${traderCustomerVo.searchMsg}"></li>

                    </ul>
                </div>
            </div>


            <div class="tcenter">
                <span class="bg-light-blue bt-bg-style bt-small" id="searchSpan" onclick="searchTrader();">搜索</span>
                <span class="bt-small bg-light-blue bt-bg-style" onclick="resetTrader();">重置</span>
                <span class="bg-light-blue bt-bg-style bt-small addtitle"
                      tabTitle='{"num":"customer","link":"./trader/customer/add.do","title":"新增客户"}'>新增客户</span>
                <span class="bg-light-blue bt-bg-style bt-small pop-new-data"
                      layerParams='{"width":"400px","height":"200px","title":"批量新增客户","link":"./uplodebatchcustomer.do"}'>批量新增客户</span>
                <%--<span class="bg-light-blue bt-bg-style bt-small" onclick="exportList()">导出列表</span>--%>
                <shiro:hasPermission name="/jsp/customer/limitPrice.do">
                            <span class="bt-small bg-light-blue bt-bg-style pop-new-data"
                                  layerparams='{"width":"700px","height":"550px","title":"限制改价名单","link":"./limitPrice.do"}'>
                                    限制改价名单
                            </span>
                </shiro:hasPermission>

            </div>
            <input type="hidden" id="cityid" value="${city}"/>
            <input type="hidden" id="zoneid" value="${zone}"/>
        </form>
    </div>
    <div class="fixdiv">
        <div class="superdiv">
            <table
                    class="table table-bordered table-striped table-condensed table-centered">
                <thead>
                <tr>
                    <th class="sorts">序号</th>
                    <th class="wid22">客户名称</th>
                    <th class="wid20">地区</th>
                    <th class="wid10">有效优惠券</th>
                    <%--                    <th class="wid8">客户分群</th>--%>
                    <th class="wid12">客户类型</th>
                    <th class="wid10">贝登商城会员</th>
                    <th class="wid7">交易次数</th>
                    <th class="wid8">交易金额</th>
                    <th class="wid7">报价次数</th>
                    <th class="wid7">沟通次数</th>
                    <th class="wid7">客户等级</th>
<%--                    <th class="wid7">客户得分</th>--%>
                    <th class="wid10">客户资质审核</th>
<%--                    <th class="wid13">战略合作伙伴</th>--%>
<%--                    <th class="wid10">基层医疗经销商</th>--%>
                    <th class="wid15">归属销售</th>
                    <th class="wid15">最近沟通时间</th>
                    <th class="wid15">更新时间</th>
                    <th class="wid10">审核状态</th>
                    <th class="wid15">操作</th>
                </tr>
                </thead>

                <tbody class="employeestate">
                <c:if test="${not empty list}">
                    <c:forEach items="${list}" var="traderCustomerVo"
                               varStatus="status">
                        <tr>
                            <td>${status.count}</td>
                            <td class="text-left  ">
                                <c:if test="${traderCustomerVo.isView eq 1 && curr_user.positType eq 310}">
                                    <a class="addtitle" href="javascript:void(0);"
                                       tabTitle='{"num":"viewcustomer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											"link":"/trader/customer/new/portrait.do?traderId=${traderCustomerVo.traderId}&traderCustomerId=${traderCustomerVo.traderCustomerId}&customerNature=${traderCustomerVo.customerNature}",
											"title":"客户360"}'>
                                        <c:if test="${not empty traderGroupMap && not empty traderGroupMap[traderCustomerVo.traderId]}">
                                            <span style="color: red">【${traderGroupMap[traderCustomerVo.traderId].traderGroupName}】</span>
                                        </c:if>
                                            ${traderCustomerVo.name}
                                    </a>

                                </c:if>
                                <c:if test="${traderCustomerVo.isView ne 1 && curr_user.positType eq 310}">
                                    <c:if test="${not empty traderGroupMap && not empty traderGroupMap[traderCustomerVo.traderId]}">
                                        <span style="color: red">【${traderGroupMap[traderCustomerVo.traderId].traderGroupName}】</span>
                                    </c:if>
                                    ${traderCustomerVo.name}
                                </c:if>
                                <c:if test="${curr_user.positType ne 310}">
                                    <a class="addtitle" href="javascript:void(0);"
                                       tabTitle='{"num":"viewcustomer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											"link":"/trader/customer/new/portrait.do?traderId=${traderCustomerVo.traderId}&traderCustomerId=${traderCustomerVo.traderCustomerId}&customerNature=${traderCustomerVo.customerNature}",
											"title":"客户360"}'>
                                        <c:if test="${not empty traderGroupMap && not empty traderGroupMap[traderCustomerVo.traderId]}">
                                            <span style="color: red">【${traderGroupMap[traderCustomerVo.traderId].traderGroupName}】</span>
                                        </c:if>
                                            ${traderCustomerVo.name}
                                    </a>
                                    ${traderCustomerVo.verifyStatus == 0 && fn:contains(traderCustomerVo.verifyUsername, curr_user.username) ?"<font color='red'>[审]</font>":""}
                                </c:if>

                            </td>
                            <td>${traderCustomerVo.address }</td>
                            <td>
                                <c:choose>
                                    <c:when test="${traderCustomerVo.couponFlag eq 1}">
												<span class="forbid clcforbid pop-new-data"
                                                      layerParams='{"width":"70%","height":"80%","title":"查看优惠券","link":"/order/saleorder/getTraderCoupons.do?traderId=${traderCustomerVo.traderId}"}'>查看优惠券</span>
                                    </c:when>
                                    <c:otherwise>/</c:otherwise>
                                </c:choose>
                            </td>
<%--                            <td>${traderCustomerVo.traderGroupStr } </td>--%>
                            <td>${traderCustomerVo.traderCustomerCategoryNames }</td>
                            <td>${traderCustomerVo.isVedengMember==1?"是":"否"}</td>
                            <td>${traderCustomerVo.buyCount}</td>
                            <td><fmt:formatNumber type="number" value="${traderCustomerVo.buyMoney}" pattern="0.00"
                                                  maxFractionDigits="2"/></td>
                            <td>${traderCustomerVo.quoteCount}</td>
                            <td>${traderCustomerVo.historyCommunicationNum}</td>
                            <td>${traderCustomerVo.traderCustomerLevelGrade}
                            </td>
                            <td>
                                <c:if test="${empty traderCustomerVo.aptitudeStatus || traderCustomerVo.aptitudeStatus eq 3}">待审核</c:if>
                                <c:if test="${traderCustomerVo.aptitudeStatus eq 4}">初审中</c:if>
                                <c:if test="${traderCustomerVo.aptitudeStatus eq 5}">公章审核</c:if>
                                <c:if test="${traderCustomerVo.aptitudeStatus eq 0}">复审中</c:if>
                                <c:if test="${traderCustomerVo.aptitudeStatus eq 1}">审核通过</c:if>
                                <c:if test="${traderCustomerVo.aptitudeStatus eq 2}">审核不通过</c:if>
                            </td>
                            <td>${traderCustomerVo.personal}</td>
                            <td><date:date value="${traderCustomerVo.lastCommuncateTime} "/></td>
                            <td><date:date value="${traderCustomerVo.modTime} "/></td>
                            <td>
                                <c:if test="${empty traderCustomerVo.verifyStatus || traderCustomerVo.verifyStatus eq 3}">待审核</c:if>
                                <c:if test="${traderCustomerVo.verifyStatus eq 0}">审核中</c:if>
                                <c:if test="${traderCustomerVo.verifyStatus eq 1}">审核通过</c:if>
                                <c:if test="${traderCustomerVo.verifyStatus eq 2}">审核不通过</c:if>
                            </td>
                            <td>
                                <c:if test="${traderCustomerVo.isView eq 1}">
                                    <c:choose>
                                        <c:when test="${traderCustomerVo.isTop eq 0}">
											<span class="edit-user"
                                                  onclick="stick(this,${traderCustomerVo.traderCustomerId},1);">置顶</span>
                                        </c:when>
                                        <c:otherwise>
											<span class="edit-user"
                                                  onclick="stick(this,${traderCustomerVo.traderCustomerId},0);">取消置顶</span>
                                        </c:otherwise>
                                    </c:choose> <c:choose>
                                    <c:when test="${traderCustomerVo.isEnable eq 0}">
											<span class="edit-user"
                                                  onclick="setDisabled(${traderCustomerVo.traderCustomerId},1);">启用</span>
                                    </c:when>
                                    <c:otherwise>
											<span class="forbid clcforbid pop-new-data"
                                                  layerParams='{"width":"600px","height":"200px","title":"禁用","link":"./initDisabledPage.do?traderCustomerId=${traderCustomerVo.traderCustomerId}"}'>禁用</span>
                                    </c:otherwise>
                                </c:choose>
                                </c:if>
                            </td>
                        </tr>
                    </c:forEach>
                </c:if>
                </tbody>
            </table>
            <c:if test="${empty list }">
                <!-- 查询无结果弹出 -->
                <div class="noresult">查询无结果！请尝试使用其他搜索条件。</div>
            </c:if>
        </div>
    </div>
    <tags:page page="${page}"/>
</div>
<%@ include file="../../common/footer.jsp" %>

<script>

    var traderCustomerLevelGradeStr='${traderCustomerLevelGradeListStr}';
    var traderCustomerLevelGradeOriginalStr='${traderCustomerLevelGradeOriginalStr}';



    $(document).ready(function () {

        $('select[name="belongPlatform"]').change(function() {
            var platform = $(this).val(); // 获取选中的归属平台值
            var $customerLevelSelect = $('select[name="traderCustomerLevelGrade"]'); // 获取客户等级选择器

            // 清空客户等级选择器的当前选项
            $customerLevelSelect.empty();

            if(platform == "") {
                $customerLevelSelect.append('<option value="">全部</option>');
            }
            if(platform == "1") {
                $customerLevelSelect.append('<option value="">全部</option>');
                arr = traderCustomerLevelGradeStr.split(',');
                $.each(arr, function(index, value) {
                    $customerLevelSelect.append('<option value="'+value+'">'+value+'</option>');
                });
            }

            if(platform != "1"&&platform!='') {
                $customerLevelSelect.append('<option value="">全部</option>');
                arr = traderCustomerLevelGradeOriginalStr.split(',');
                $.each(arr, function(index, value) {
                    $customerLevelSelect.append('<option value="'+value+'">'+value+'</option>');
                });
            }

        });
    });

    $(function () {
        var governmentRelation = $('select[name="governmentRelation"]').val();
        if (governmentRelation == '2') {
            $('#otherGovernmentRelation').show();
        } else {
            $('#otherGovernmentRelation').hide();
        }
    });

    var traderCustomerLevel;
    var businessTerminalCategoriesAndTypes;
    var customerCategory;
    var traderCustomerMainCategory;
    var traderCustomerTrade;
    var labelPerfection;
    var labelPerfectionChild;
    var agencySkuList;

    var traderCustomerBrand = xmSelect.render({
        el: '#traderCustomerBrand',
        style: {
            minHeight: '26px',
            height: '26px',
        },
        toolbar: {
            show: true,
            showIcon: false,
        },
        size: 'mini',
        filterable: true,
        remoteSearch: true,
        prop: {
            value: 'brandId',
            name: 'brandName',
        },
        theme:{
            color: '#409eff'
        },
        data: [],
        delay: 200,
        remoteMethod: function(val, cb, show){
            if(!val){
                return cb([]);
            }
            $.ajax({
                type: "GET",
                url: page_url + "/goodsBandApi/queryBrand.do",
                data: {'brandName': val},
                dataType: 'json',
                success: function (data) {
                    cb(data.data)
                }
            });
        }
    });

    // 客户接收人
    var sharedSale = xmSelect.render({
        el: '#sharedSale',
        style: {
            minHeight: '26px',
            height: '26px'
        },
        toolbar: {
            show: false,
            showIcon: false
        },
        size: 'mini',
        filterable: true,
        remoteSearch: true,
        prop: {
            value: 'userId',
            name: 'username'
        },
        theme:{
            color: '#409eff'
        },
        data: [],
        delay: 200,
        radio: true,
        clickClose: true,
        remoteMethod: function (val, cb, show) {
            if (!val) {
                return cb([]);
            }
            $.ajax({
                type: "POST",
                url: page_url + "/system/user/search.do?type=ALL",
                data: {'username': val},
                dataType: 'json',
                success: function (data) {
                    cb(data.data)
                }
            });
        }
    });

    var agencyBrand = xmSelect.render({
        el: '#agencyBrand',
        style: {
            minHeight: '26px',
            height: '26px'
        },
        toolbar: {
            show: true,
            showIcon: false
        },
        size: 'mini',
        filterable: true,
        remoteSearch: true,
        prop: {
            value: 'brandId',
            name: 'brandName'
        },
        theme:{
            color: '#409eff'
        },
        data: [],
        delay: 200,
        remoteMethod: function (val, cb, show) {
            if (!val) {
                return cb([]);
            }
            $.ajax({
                type: "GET",
                url: page_url + "/goodsBandApi/queryBrand.do",
                data: {'brandName': val},
                dataType: 'json',
                success: function (data) {
                    cb(data.data)
                }
            });
        }
    });

    // 品牌查询
    const viewInfo = {
        selectTraderCustomerBrandList: '${selectTraderCustomerBrandList}'
    };
    if (viewInfo.selectTraderCustomerBrandList !== 'undefined' && viewInfo.selectTraderCustomerBrandList != '') {
        viewInfo.selectTraderCustomerBrandList =JSON.parse(viewInfo.selectTraderCustomerBrandList)
        var newArr = viewInfo.selectTraderCustomerBrandList.map(function (obj) {
            return {...obj, selected: true};
        });
        traderCustomerBrand.update({
            data: newArr
        })
    }

    // 代理品牌
    const  agencyBrandViewInfo = {
        selectAgencyBrandList: '${selectAgencyBrandList}'
    };
    if (agencyBrandViewInfo.selectAgencyBrandList !== 'undefined' && agencyBrandViewInfo.selectAgencyBrandList != '') {
        agencyBrandViewInfo.selectAgencyBrandList =JSON.parse(agencyBrandViewInfo.selectAgencyBrandList);
        var newArr = agencyBrandViewInfo.selectAgencyBrandList.map(function (obj) {
            return {...obj, selected: true};
        });
        agencyBrand.update({
            data: newArr
        })
    }

    const shareSaleViewInfo = {
        saleList: '${saleList}'
    };
    if (shareSaleViewInfo.saleList !== 'undefined' && shareSaleViewInfo.saleList != '') {
        shareSaleViewInfo.saleList =JSON.parse(shareSaleViewInfo.saleList);
        var newArr = shareSaleViewInfo.saleList.map(function (obj) {
            return {...obj, selected: true};
        });
        sharedSale.update({
            data: newArr
        })
    }


    layui.use('layCascader', function () {
        var layCascader = layui.layCascader;
        $.ajax({
            type: "GET",
            url: page_url + "/trader/customer/traderCustomerTypeTree.do",
            dataType: 'json',
            success: function (data) {
                let value = $("#selectCategoryList").val()
                if (value != null && value.length > 0) {
                    value = JSON.parse(value)
                }
                customerCategory = layCascader({
                    elem: '#customerCategoryTypeSelect',
                    clearable: true,
                    collapseTags: true,
                    minCollapseTagsNumber: 1,
                    value: value,
                    options: data.data,
                    extendClass: true,
                    extendStyle: true,
                    props: {
                        value: 'id',
                        label: 'customerCategoryName',
                        multiple: true
                    }
                });
            }
        });

        let labelPerfectionValue = $("#selectLabelPerfectionValue").val()

        let labelPerfectionChildValue = $("#selectLabelPerfectionChildValue").val()
        if (labelPerfectionChildValue != null && labelPerfectionChildValue.length > 0) {
            labelPerfectionChildValue = JSON.parse(labelPerfectionChildValue)
        }
        let labelPerfectionChildValueOne = $("#selectLabelPerfectionChildValueOne").val()

        var labelPerfectionOpton = [
            {"id": "0", "name": "全部"},
            {"id": "1", "name": "主营客户(C)"},
            {"id": "2","name": "主营商品类型(P)"},
            {"id": "3", "name": "主营商品范畴(P)"},
            {"id": "4", "name": "经营模式(M)"},
            {"id": "5", "name": "经营区域(D)"}];
        var labelPerfectionChildOpton = [
            {"id": 0, "name": "0%~80%"},
            {"id": 20, "name": "81%~100%"}];
        var labelPerfectionChildOpton2 = [
            {"id": "0", "name": "0%"},
            {"id": "20", "name": "20%"}];
        var labelPerfectionChildOptonSale = [
            {"id": "0", "name": "0%"},
            {"id": "5", "name": "5%"},
            {"id": "15", "name": "15%"},
            {"id": "20", "name": "20%"}];
        labelPerfection = layCascader({
            elem: '#labelPerfectionSelect',
            // clearable: true,
            collapseTags: true,
            minCollapseTagsNumber: 1,
            value: labelPerfectionValue,
            options: labelPerfectionOpton,
            extendClass: true,
            extendStyle: true,
            props: {
                value: 'id',
                label: 'name',
                multiple: false
            }
        });
        labelPerfectionChild = layCascader({
            elem: '#labelPerfectionChildSelect',
            clearable: true,
            collapseTags: true,
            minCollapseTagsNumber: 1,
            value: labelPerfectionValue!=null&&labelPerfectionValue=="0" ?labelPerfectionChildValue:labelPerfectionChildValueOne,
            options: labelPerfectionValue!=null&&labelPerfectionValue=="0" ?labelPerfectionChildOpton:(labelPerfectionValue!=null&&labelPerfectionValue=="4"?labelPerfectionChildOptonSale:labelPerfectionChildOpton2),
            extendClass: true,
            extendStyle: true,
            props: {
                value: 'id',
                label: 'name',
                multiple: labelPerfectionValue!=null&&labelPerfectionValue=="0" ?true:false
            }
        });
        labelPerfection.change(function (value, node) {
            if (value == "0") {
                $("#selectLabelPerfectionChildValueOne").val()
                let labelPerfectionChildValue = $("#selectLabelPerfectionChildValue").val()
                if (labelPerfectionChildValue != null && labelPerfectionChildValue.length > 0) {
                    labelPerfectionChildValue = JSON.parse(labelPerfectionChildValue)
                }
                document.getElementById("reRender").innerHTML = "<input id=\"labelPerfectionChildSelect\" style=\"width: 63%\"/>";
                console.log(document.getElementById('reRender'))
                labelPerfectionChild =layCascader({
                    elem: '#labelPerfectionChildSelect',
                    clearable: true,
                    collapseTags: true,
                    minCollapseTagsNumber: 1,
                    value: labelPerfectionChildValue,
                    options: labelPerfectionChildOpton,
                    extendClass: true,
                    extendStyle: true,
                    props: {
                        value: 'id',
                        label: 'name',
                        multiple: true
                    }
                });
            }
            else if (value == "4") {
                $("#selectLabelPerfectionChildValue").val("")
                let labelPerfectionChildValueOne = $("#selectLabelPerfectionChildValueOne").val()
                document.getElementById("reRender").innerHTML = "<input id=\"labelPerfectionChildSelect\" style=\"width: 63%\"/>";
                console.log(document.getElementById('reRender'))
                labelPerfectionChild = layCascader({
                    elem: '#labelPerfectionChildSelect',
                    clearable: true,
                    collapseTags: true,
                    minCollapseTagsNumber: 1,
                    value: labelPerfectionChildValueOne,
                    options: labelPerfectionChildOptonSale,
                    extendClass: true,
                    extendStyle: true,
                    props: {
                        value: 'id',
                        label: 'name',
                        multiple: false
                    }
                });
            }
            else {
                $("#selectLabelPerfectionChildValue").val("")
                let labelPerfectionChildValueOne = $("#selectLabelPerfectionChildValueOne").val()
                document.getElementById("reRender").innerHTML = "<input id=\"labelPerfectionChildSelect\" style=\"width: 63%\"/>";
                console.log(document.getElementById('reRender'))
                labelPerfectionChild = layCascader({
                    elem: '#labelPerfectionChildSelect',
                    clearable: true,
                    collapseTags: true,
                    minCollapseTagsNumber: 1,
                    value: labelPerfectionChildValueOne,
                    options: labelPerfectionChildOpton2,
                    extendClass: true,
                    extendStyle: true,
                    props: {
                        value: 'id',
                        label: 'name',
                        multiple: false
                    }
                });
            }


        });

        $.ajax({
            type: "GET",
            url: page_url + "/trader/customer/getBusinessTerminal.do",
            dataType: 'json',
            success: function (data) {
                let value1 = $("#selectBusinessTerminalCategoriesAndType").val();
                let defaultArray = [];
                if (value1 != null && value1.length > 0) {
                    var array = value1.substring(1, value1.length - 1).split(",");
                    array = array.map(function (element) {
                        return element.trim();
                    });
                    Array.prototype.push.apply(defaultArray, array);
                }
                businessTerminalCategoriesAndTypes = layCascader({
                    elem: '#businessTerminalCategoriesAndTypesSelect',
                    clearable: true,
                    collapseTags: true,
                    minCollapseTagsNumber: 1,
                    value: defaultArray,
                    options: data.data,
                    extendClass: true,
                    extendStyle: true,
                    props: {
                        value: 'label',
                        label: 'value',
                        multiple: true
                    }
                });
            }
        });

        $(document).ready(function () {
            $('select[name="traderCustomerMainCategoryType"]').change(function () {
                if ($(this).val() == '1') {
                    $('#traderCustomerMainCategorySelectShow').show();
                } else {
                    $('#traderCustomerMainCategorySelectShow').hide();
                }
            });

            $('select[name="governmentRelation"]').change(function () {
                if ($(this).val() == '2') {
                    $('#otherGovernmentRelation').show();
                } else {
                    $('#otherGovernmentRelation').hide();
                }
            });
            $('select[name="traderCustomerMainCategoryType"]').change(function() {
                if ($(this).val() == '1') {
                    $('#traderCustomerMainCategorySelectShow').show();
                } else {
                    $('#traderCustomerMainCategorySelectShow').hide();
                    traderCustomerMainCategory.clearCheckedNodes();
                }
            });
            $.ajax({
                type: "GET",
                url: page_url + "/goodsCategory/getAllCategory.do",
                dataType: 'json',
                success: function (data) {
                    let value2 = $("#selectTraderCustomerMainCategory").val();
                    if(value2!=null && value2.length>0){
                        value2 = JSON.parse(value2);
                        if ($("#traderCustomerMainCategoryTypeId").val() === '1'){
                            $('#traderCustomerMainCategorySelectShow').show();
                        }else {
                            $('#traderCustomerMainCategorySelectShow').hide();
                            value2 = [];
                        }

                    }
                    traderCustomerMainCategory = layCascader({
                        elem: '#traderCustomerMainCategorySelect',
                        clearable: true,
                        collapseTags: true,
                        minCollapseTagsNumber: 1,
                        value: value2,
                        options: data.data,
                        extendClass:true,
                        extendStyle:true,
                        props: {
                            value: 'baseCategoryId',
                            label: 'baseCategoryName',
                            multiple: true
                        }
                    });

                    let agencySkuOptions = [];
                    let selectAgencySkuList = $("#selectAgencySkuList").val();
                    if(selectAgencySkuList!=null && selectAgencySkuList.length>0){
                        agencySkuOptions = JSON.parse(selectAgencySkuList)
                    }
                    agencySkuList = layCascader({
                        elem: '#agencySkuCascader',
                        clearable: true,
                        collapseTags: true,
                        minCollapseTagsNumber: 1,
                        value: agencySkuOptions,
                        options: data.data,
                        extendClass:true,
                        extendStyle:true,
                        props: {
                            value: 'baseCategoryId',
                            label: 'baseCategoryName',
                            multiple: true
                        }
                    });


                    let value3 = $("#selectTraderCustomerTrade").val()
                    if(value3!=null && value3.length>0){
                        value3 = JSON.parse(value3)
                    }
                    traderCustomerTrade = layCascader({
                        elem: '#traderCustomerTrade',
                        clearable: true,
                        collapseTags: true,
                        minCollapseTagsNumber: 1,
                        value: value3[0],
                        options: data.data,
                        props: {
                            value: 'baseCategoryId',
                            label: 'baseCategoryName'
                        }
                    });
                }
            });
            // $.ajax({
            //     type: "GET",
            //     url: page_url + "/goodsCategory/getAllCategory.do",
            //     dataType: 'json',
            //     success: function (data) {
            //
            //     }
            // });



            let value5 = $("#selectInstitutionLevel").val()
            if (value5 != null && value5.length > 0) {
                value5 = JSON.parse(value5)
            }else {
                value5 = [-1]
            }
            traderCustomerLevel = layCascader({
                elem: '#institutionLevel',
                clearable: true,
                collapseTags: true,
                filterable: true,
                value: value5,
                minCollapseTagsNumber: 1,
                extendClass: true,
                extendStyle: true,
                props: {
                    multiple: true
                },
                options: [
                    {value: -1, label: "全部"},
                    {value: 0, label: "一级医院"},
                    {value: 1, label: "二级医院"},
                    {value: 2, label: "三级医院"},
                    {value: 3, label: "未定级医院"},
                    {value: 4, label: "社区卫生服务中心（站）"},
                    {value: 5, label: "乡镇卫生院"},
                    {value: 6, label: "诊所（医务室）"},
                    {value: 7, label: "村卫生室"},
                    {value: 8, label: "应急三级医院"},
                    {value: 9, label: "应急二级医院"},
                    {value: 10, label: "应急基层医疗"}
                ]
            })
        });



    })



    /**
     * 搜索列表
     * @returns
     */
    function searchTrader() {
        checkLogin();
        var div = '<div class="tip-loadingNewData" style="position:fixed;width:100%;height:100%; z-index:100; background:rgba(0,0,0,0.3)">' +
            '<i class="iconloadingblue" style="position:absolute;left:50%;top:32%;"></i></div>';
        $("body").prepend(div);
        //customerCategory不为空或者未定义

        if (typeof customerCategory !== 'undefined' ) {
            let customerCategorys = customerCategory.getCheckedValues();
            if(customerCategorys!=null && customerCategorys.length>0 && customerCategorys!='[]'){
                $("#selectCategoryList").val(customerCategorys)
            }else{
                $("#selectCategoryList").val(null)
            }
        }else{
            $("#selectCategoryList").val(null)
        }

        if (typeof businessTerminalCategoriesAndTypes !== 'undefined') {
            let businessTerminalCategoriesAndTypesBetter = businessTerminalCategoriesAndTypes.getCheckedValues();
            if(businessTerminalCategoriesAndTypesBetter!=null && businessTerminalCategoriesAndTypesBetter.length>0 && businessTerminalCategoriesAndTypesBetter!='[]'){
                $("#selectBusinessTerminalCategoriesAndType").val(businessTerminalCategoriesAndTypesBetter)
            }else{
                $("#selectBusinessTerminalCategoriesAndType").val(null)
            }
        }else{
            $("#selectBusinessTerminalCategoriesAndType").val(null)
        }


        if (typeof traderCustomerMainCategory !== 'undefined') {
            let traderCustomerMainCategoryBetter = traderCustomerMainCategory.getCheckedValues();
            if(traderCustomerMainCategoryBetter!=null && traderCustomerMainCategoryBetter.length>0 && traderCustomerMainCategoryBetter!='[]'){
                $("#selectTraderCustomerMainCategory").val(traderCustomerMainCategoryBetter)
            }else{
                $("#selectTraderCustomerMainCategory").val(null)
            }
        }else{
            $("#selectTraderCustomerMainCategory").val(null)
        }

        // 代理商品
        if (typeof agencySkuList !== 'undefined') {
            let agencySkuBetter = agencySkuList.getCheckedValues();
            if(agencySkuBetter!=null && agencySkuBetter.length>0 && agencySkuBetter!='[]'){
                $("#selectAgencySkuList").val(agencySkuBetter)
            }else{
                $("#selectAgencySkuList").val(null)
            }
        }else{
            $("#selectAgencySkuList").val(null)
        }

        if (typeof traderCustomerTrade !== 'undefined') {
            let traderCustomerTradeBetter = traderCustomerTrade.getCheckedValues();
            if(traderCustomerTradeBetter!=null){
                $("#selectTraderCustomerTrade").val(traderCustomerTradeBetter)
            }else{
                $("#selectTraderCustomerTrade").val(null)
            }
        }else{
            $("#selectTraderCustomerTrade").val(null)
        }

        // 品牌
        let traderCustomerBrandBetter = traderCustomerBrand.getValue('value');
        let traderCustomerBrandBetterList = traderCustomerBrand.getValue();
        if (traderCustomerBrandBetter != null && traderCustomerBrandBetter.length > 0 && traderCustomerBrandBetter != '[]') {
            $("#selectTraderCustomerBrand").val(traderCustomerBrandBetter)
            $("#selectTraderCustomerBrandList").val(JSON.stringify(traderCustomerBrandBetterList))
        } else {
            $("#selectTraderCustomerBrand").val(null)
            $("#selectTraderCustomerBrandList").val(null)
        }
        if (typeof traderCustomerLevel !== 'undefined' ) {
            let traderCustomerLevelBetter = traderCustomerLevel.getCheckedValues();
            if(traderCustomerLevelBetter!=null && traderCustomerLevelBetter!='[]'){
                $("#selectInstitutionLevel").val(traderCustomerLevelBetter)
            }else{
                $("#selectInstitutionLevel").val(null)
            }
        }else{
            $("#selectInstitutionLevel").val(null)
        }

        // 代理品牌
        let agencyBrandBetter = agencyBrand.getValue('value');
        let agencyBrandBetterList = agencyBrand.getValue();
        if (agencyBrandBetter != null && agencyBrandBetter.length > 0 && agencyBrandBetter != '[]') {
            $("#agencyBrandIdList").val(agencyBrandBetter)
            $("#selectAgencyBrandList").val(JSON.stringify(agencyBrandBetterList))
        } else {
            $("#agencyBrandIdList").val(null)
            $("#selectAgencyBrandList").val(null)
        }

        // 分享销售
        var sharedSaleId = sharedSale.getValue('value');
        var sharedSaleOptionList = sharedSale.getValue();
        if (sharedSaleOptionList != null && sharedSaleOptionList.length > 0 && sharedSaleOptionList != '[]') {
            $("#sharedSaleId").val(sharedSaleId);
            $("#saleList").val(JSON.stringify(sharedSaleOptionList))
        } else {
            $("#sharedSaleId").val(null);
            $("#saleList").val(null)
        }



        if (typeof labelPerfection !== 'undefined') {
            let parent = labelPerfection.getCheckedValues();
            if(parent!=null){
                $("#selectLabelPerfectionValue").val(parent);
                    if (typeof labelPerfectionChild !== 'undefined') {
                        if (parent == "0") {
                            $("#selectLabelPerfectionChildValueOne").val(null);
                            let value = labelPerfectionChild.getCheckedValues();
                            if (value != null && value != '[]') {
                                $("#selectLabelPerfectionChildValue").val(value);
                            } else {
                                $("#selectLabelPerfectionChildValue").val(null);
                            }
                        } else {
                            $("#selectLabelPerfectionChildValue").val(null);
                            let value = labelPerfectionChild.getCheckedValues();
                            if (value != null) {
                                $("#selectLabelPerfectionChildValueOne").val(value);
                            } else {
                                $("#selectLabelPerfectionChildValueOne").val(null);
                            }
                        }
                    }
            }else{
                $("#selectTraderCustomerMainCategory").val(null)
            }
        }else{
            $("#selectTraderCustomerMainCategory").val(null)
        }
        $("#search").submit();
    }

    //为列表页搜索框添加回车事件
    if($("#search")!=undefined && $("#search").html()!=undefined){
        $("#search :input").keydown(function (e) {
            if (e.which == 13) {
                $("#search :input").each(function(){
                    $(this).val($(this).val()==null?"":$(this).val().trim());
                })
                let customerCategorys = customerCategory.getCheckedValues();
                if(customerCategorys!=null && customerCategorys.length>0 && customerCategorys!='[]'){
                    $("#selectCategoryList").val(customerCategorys)
                }else{
                    $("#selectCategoryList").val(null)
                }
                if (typeof businessTerminalCategoriesAndTypes !== 'undefined') {
                    let businessTerminalCategoriesAndTypesBetter = businessTerminalCategoriesAndTypes.getCheckedValues();
                    if(businessTerminalCategoriesAndTypesBetter!=null && businessTerminalCategoriesAndTypesBetter.length>0 && businessTerminalCategoriesAndTypesBetter!='[]'){
                        $("#selectBusinessTerminalCategoriesAndType").val(businessTerminalCategoriesAndTypesBetter)
                    }else{
                        $("#selectBusinessTerminalCategoriesAndType").val(null)
                    }
                }else{
                    $("#selectBusinessTerminalCategoriesAndType").val(null)
                }


                if (typeof traderCustomerMainCategory !== 'undefined') {
                    let traderCustomerMainCategoryBetter = traderCustomerMainCategory.getCheckedValues();
                    if(traderCustomerMainCategoryBetter!=null && traderCustomerMainCategoryBetter.length>0 && traderCustomerMainCategoryBetter!='[]'){
                        $("#selectTraderCustomerMainCategory").val(traderCustomerMainCategoryBetter)
                    }else{
                        $("#selectTraderCustomerMainCategory").val(null)
                    }
                }else{
                    $("#selectTraderCustomerMainCategory").val(null)
                }

                // 代理商品
                if (typeof agencySkuList !== 'undefined') {
                    let agencySkuBetter = agencySkuList.getCheckedValues();
                    if(agencySkuBetter!=null && agencySkuBetter.length>0 && agencySkuBetter!='[]'){
                        $("#selectAgencySkuList").val(agencySkuBetter)
                    }else{
                        $("#selectAgencySkuList").val(null)
                    }
                }else{
                    $("#selectAgencySkuList").val(null)
                }

                if (typeof traderCustomerTrade !== 'undefined') {
                    let traderCustomerTradeBetter = traderCustomerTrade.getCheckedValues();
                    if(traderCustomerTradeBetter!=null){
                        $("#selectTraderCustomerTrade").val(traderCustomerTradeBetter)
                    }else{
                        $("#selectTraderCustomerTrade").val(null)
                    }
                }else{
                    $("#selectTraderCustomerTrade").val(null)
                }

                // 品牌
                let traderCustomerBrandBetter = traderCustomerBrand.getValue('value');
                let traderCustomerBrandBetterList = traderCustomerBrand.getValue();
                if (traderCustomerBrandBetter != null && traderCustomerBrandBetter.length > 0 && traderCustomerBrandBetter != '[]') {
                    $("#selectTraderCustomerBrand").val(traderCustomerBrandBetter)
                    $("#selectTraderCustomerBrandList").val(JSON.stringify(traderCustomerBrandBetterList))
                } else {
                    $("#selectTraderCustomerBrand").val(null)
                    $("#selectTraderCustomerBrandList").val(null)
                }

                // 代理品牌
                // 代理品牌
                let agencyBrandBetter = agencyBrand.getValue('value');
                let agencyBrandBetterList = agencyBrand.getValue();
                if (agencyBrandBetter != null && agencyBrandBetter.length > 0 && agencyBrandBetter != '[]') {
                    $("#agencyBrandIdList").val(agencyBrandBetter)
                    $("#selectAgencyBrandList").val(JSON.stringify(agencyBrandBetterList))
                } else {
                    $("#agencyBrandIdList").val(null)
                    $("#selectAgencyBrandList").val(null)
                }

                if (typeof traderCustomerLevel !== 'undefined' ) {
                    let traderCustomerLevelBetter = traderCustomerLevel.getCheckedValues();
                    if(traderCustomerLevelBetter!=null && traderCustomerLevelBetter!='[]'){
                        $("#selectInstitutionLevel").val(traderCustomerLevelBetter)
                    }else{
                        $("#selectInstitutionLevel").val(null)
                    }
                }else{
                    $("#selectInstitutionLevel").val(null)
                }

                if (typeof labelPerfection !== 'undefined') {
                    let parent = labelPerfection.getCheckedValues();
                    if(parent!=null){
                        $("#selectLabelPerfectionValue").val(parent);
                        if (typeof labelPerfectionChild !== 'undefined') {
                            if (parent == "0") {
                                $("#selectLabelPerfectionChildValueOne").val(null);
                                let value = labelPerfectionChild.getCheckedValues();
                                if (value != null && value != '[]') {
                                    $("#selectLabelPerfectionChildValue").val(value);
                                } else {
                                    $("#selectLabelPerfectionChildValue").val(null);
                                }
                            } else {
                                $("#selectLabelPerfectionChildValue").val(null);
                                let value = labelPerfectionChild.getCheckedValues();
                                if (value != null) {
                                    $("#selectLabelPerfectionChildValueOne").val(value);
                                } else {
                                    $("#selectLabelPerfectionChildValueOne").val(null);
                                }
                            }
                        }
                    }else{
                        $("#selectTraderCustomerMainCategory").val(null)
                    }
                }else{
                    $("#selectTraderCustomerMainCategory").val(null)
                }

                $("#search").submit();
                return false;
            }
        })
    }

    //form表单重置
    function resetTrader(){
        $("form").find("input[type='text']").val('');
        $("form").find(".hidden-reset").val('');
        $.each($("form select"),function(i,n){
            $(this).children("option:first").prop("selected",true);
        });
        $("#selectCategoryList").val(null);
        $("#selectTraderCustomerBrandList").val(null);
        $("#selectAgencyBrandList").val(null);

        traderCustomerLevel.clearCheckedNodes();
        traderCustomerBrand.setValue([ ]);
        sharedSale.setValue([ ]);
        agencyBrand.setValue([ ]);
        $('#otherGovernmentRelation').hide();
        businessTerminalCategoriesAndTypes.clearCheckedNodes();
        customerCategory.clearCheckedNodes();
        traderCustomerMainCategory.clearCheckedNodes();
        agencySkuList.clearCheckedNodes();
        traderCustomerTrade.clearCheckedNodes();
        customerCategory.clearCheckedNodes();
        labelPerfection.clearCheckedNodes();
        labelPerfectionChild.clearCheckedNodes();
        $("#selectLabelPerfectionChildValueOne").val(null);
        $("#selectLabelPerfectionChildValue").val(null);
        $("#selectLabelPerfectionValue").val(null);
        $("#cusProperty").val("");
        //地区市 区信息重置
        if ($("select[name='city']") != undefined) {
            $("select[name='city'] option:gt(0)").remove();
        }
        if ($("select[name='zone']") != undefined) {
            $("select[name='zone'] option:gt(0)").remove();
        }

        var $customerLevelSelect = $('select[name="traderCustomerLevelGrade"]'); // 获取客户等级选择器
        // 清空客户等级选择器的当前选项
        $customerLevelSelect.empty();
        $customerLevelSelect.append('<option value="">全部</option>');
    }
</script>

