package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.domain.FileInfoDto;
import com.vedeng.common.core.utils.FileInfoUtils;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchAttachmentDto;
import com.vedeng.erp.kingdee.dto.KingDeeFileDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class AlipayReceiptPushKingdeeFormProcessor  extends BaseProcessor<BatchAttachmentDto, KingDeeFileDto> {
    @Override
    public KingDeeFileDto doProcess(BatchAttachmentDto input, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("上传支付宝回单入参对象：{}", JSON.toJSONString(input));
        if (StrUtil.isEmpty(input.getUri())) {
            return null;
        }
        FileInfoDto base64FromUrl = FileInfoUtils.getBase64FromUrl(input.getUri());
        String name = StrUtil.isEmpty(input.getName()) ? "file" + base64FromUrl.getSuffix() : input.getName() + base64FromUrl.getSuffix();
        return KingDeeFileDto.builder()
                .fileName("支付宝收款回单流水号"+name)
                .sendByte(base64FromUrl.getFileBase64())
                .aliasFileName(input.getName())
                .billNo(input.getName())
                .FormId(input.getFormId())
                .isLast(true)
                .interId(input.getFId())
                .build();
    }
}
