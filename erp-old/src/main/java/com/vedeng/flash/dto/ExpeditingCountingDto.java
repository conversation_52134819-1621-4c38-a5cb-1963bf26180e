package com.vedeng.flash.dto;

import lombok.Data;

@Data
public class ExpeditingCountingDto {
    //购买数量
    private Integer buyNum;

    //售后数量
    private Integer aftersaleNum;

    //到货数量
    private Integer arriveNum;

    //预计首次发货时间
    private Long sendGoodsTime;

    //货期
    private String deliveryCycle;

    //采购订单详情ID
    private Integer buyorderGoodsId;

    //生效时间
    private Long validTime;
}
