<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
       xmlns:jdbc="http://www.springframework.org/schema/jdbc" xmlns:jee="http://www.springframework.org/schema/jee"
       xmlns:tx="http://www.springframework.org/schema/tx" xmlns:util="http://www.springframework.org/schema/util"
       xmlns:task="http://www.springframework.org/schema/task"
       xsi:schemaLocation="
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.2.xsd
		http://www.springframework.org/schema/jdbc http://www.springframework.org/schema/jdbc/spring-jdbc-3.2.xsd
		http://www.springframework.org/schema/jee http://www.springframework.org/schema/jee/spring-jee-3.2.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.2.xsd
		http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-3.2.xsd
		http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.2.xsd">

    <bean id="canalSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="canalDataSource" />
        <property name="mapperLocations">
            <list>
                <value>classpath:/mapping/kpi/replica/*.xml</value>
            </list>
        </property>
    </bean>

    <bean id="canalDataSource"
          class="com.ezadmin.plugins.sqlog.EzSqlogDataSource">
        <property name="realDataSource" ref="canalDataSourceTarget"></property>
        <property name="logType" value="${logType}"></property>
    </bean>

    <bean id="canalDataSourceTarget" class="com.alibaba.druid.pool.DruidDataSource"  init-method="init" destroy-method="close">
        <property name="url" value="${kpi.replica.url}" />
        <property name="username" value="${kpi.replica.username}" />
        <property name="password" value="${kpi.replica.password}" />
<!--        <property name="url" value="************************************************************************************************************************" />-->
<!--        <property name="username" value="qa_kpi_replica" />-->
<!--        <property name="password" value="qa_kpi_replica" />-->

        <property name="initialSize" value="0" />
        <property name="maxActive" value="20" />

        <property name="minIdle" value="0" />
        <property name="maxWait" value="60000" />

        <property name="validationQuery" value="${validationQuery}" />
        <property name="testOnBorrow" value="false" />
        <property name="testOnReturn" value="false" />
        <property name="testWhileIdle" value="true" />
        <property name="timeBetweenEvictionRunsMillis" value="60000" />
        <property name="minEvictableIdleTimeMillis" value="25200000" />
        <property name="removeAbandoned" value="true" />
        <property name="removeAbandonedTimeout" value="1800" />
        <property name="logAbandoned" value="true" />
        <property name="filters" value="mergeStat" />
    </bean>

    <bean id="canalMapperScannerConfigurer" class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="sqlSessionFactoryBeanName" value="canalSqlSessionFactory" />
        <property name="basePackage" value="com.vedeng.kpi.replica.dao" />
<!--        <property name="annotationClass" value="javax.inject.Named" />-->
    </bean>

    <bean id="canalTransactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="canalDataSource" />
    </bean>

    <tx:annotation-driven transaction-manager="canalTransactionManager" proxy-target-class="true" />
</beans>
