package com.vedeng.crm.web.api;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.erp.system.dto.SysOptionDefinitionDto;
import com.vedeng.erp.system.service.SysOptionDefinitionApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * 字典表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/7/15 15:32
 */
@ExceptionController
@RestController
@RequestMapping("/crm/sysOption/public")
@Slf4j
public class SysOptionDefinitionApi {

    @Autowired
    private SysOptionDefinitionApiService sysOptionDefinitionApiService;

    @RequestMapping(value = "/getByParentCode")
    @NoNeedAccessAuthorization
    public R<List<SysOptionDefinitionDto>> getByParentCode(@RequestParam String parentCode) {
        return R.success(sysOptionDefinitionApiService.getOptionDefinitionListByOptType(parentCode));
    }

    @RequestMapping(value = "/getByParentId")
    @NoNeedAccessAuthorization
    public R< List<SysOptionDefinitionDto>> getByParentId(@RequestParam Integer parentId) {
        return R.success(sysOptionDefinitionApiService.getByParentIdList(Collections.singletonList(parentId)));
    }
}