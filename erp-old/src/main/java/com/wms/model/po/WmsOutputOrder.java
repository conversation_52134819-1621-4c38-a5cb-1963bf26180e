package com.wms.model.po;

import com.vedeng.erp.wms.dto.WmsOutputOrderExtra;

import java.util.List;

public class WmsOutputOrder {

    private Long id;

    private Integer type;

    private String orderNo;

    private Integer verifyStatus;

    private Integer outStatus;

    private Integer returnStatus;

    private String borrowReason;

    private Long borrowTraderId;

    private String borrowTraderName;

    private String logisticCommnet;

    private String belongDepartment;

    private Integer scrapType;

    private Integer scrapLevel;

    private Integer scrapDealType;

    private String applyer;

    private Integer applyerId;

    private String applyerDepartment;

    private Integer applyerDepartmentId;

    private String recipientApplyer;

    private String recipientDepartment;

    private Integer recipientType;

    private String appleOutDate;

    private String useNature;

    private String useName;

    private String receiver;

    private String receiverAddress;

    private String receiverPhone;

    private String receiverTelphone;

    private String detailAddress;

    private String applyReason;

    private String remark;

    private String realOutputTime;

    private String customerReceiveTime;

    private String approvalTime;

    private String addTime;

    private String updateTime;

    private String creator;

    private String updator;

    private Integer isDelete;

    private List<WmsOutputOrderGoods> wmsOutputOrderGoods;

    public List<WmsOutputOrderGoods> getWmsOutputOrderGoods() {
        return wmsOutputOrderGoods;
    }

    public void setWmsOutputOrderGoods(List<WmsOutputOrderGoods> wmsOutputOrderGoods) {
        this.wmsOutputOrderGoods = wmsOutputOrderGoods;
    }

    private WmsOutputOrderExtra wmsOutputOrderExtra;

    public WmsOutputOrderExtra getWmsOutputOrderExtra() {
        return wmsOutputOrderExtra;
    }

    public void setWmsOutputOrderExtra(WmsOutputOrderExtra wmsOutputOrderExtra) {
        this.wmsOutputOrderExtra = wmsOutputOrderExtra;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    public Integer getVerifyStatus() {
        return verifyStatus;
    }

    public void setVerifyStatus(Integer verifyStatus) {
        this.verifyStatus = verifyStatus;
    }

    public Integer getReturnStatus() {
        return returnStatus;
    }

    public void setReturnStatus(Integer returnStatus) {
        this.returnStatus = returnStatus;
    }

    public String getBorrowReason() {
        return borrowReason;
    }

    public void setBorrowReason(String borrowReason) {
        this.borrowReason = borrowReason == null ? null : borrowReason.trim();
    }

    public Long getBorrowTraderId() {
        return borrowTraderId;
    }

    public void setBorrowTraderId(Long borrowTraderId) {
        this.borrowTraderId = borrowTraderId;
    }

    public String getLogisticCommnet() {
        return logisticCommnet;
    }

    public void setLogisticCommnet(String logisticCommnet) {
        this.logisticCommnet = logisticCommnet == null ? null : logisticCommnet.trim();
    }

    public String getBelongDepartment() {
        return belongDepartment;
    }

    public void setBelongDepartment(String belongDepartment) {
        this.belongDepartment = belongDepartment == null ? null : belongDepartment.trim();
    }

    public Integer getScrapType() {
        return scrapType;
    }

    public void setScrapType(Integer scrapType) {
        this.scrapType = scrapType;
    }

    public Integer getScrapLevel() {
        return scrapLevel;
    }

    public void setScrapLevel(Integer scrapLevel) {
        this.scrapLevel = scrapLevel;
    }

    public Integer getScrapDealType() {
        return scrapDealType;
    }

    public void setScrapDealType(Integer scrapDealType) {
        this.scrapDealType = scrapDealType;
    }

    public String getApplyer() {
        return applyer;
    }

    public void setApplyer(String applyer) {
        this.applyer = applyer == null ? null : applyer.trim();
    }

    public String getApplyerDepartment() {
        return applyerDepartment;
    }

    public void setApplyerDepartment(String applyerDepartment) {
        this.applyerDepartment = applyerDepartment == null ? null : applyerDepartment.trim();
    }

    public String getRecipientApplyer() {
        return recipientApplyer;
    }

    public void setRecipientApplyer(String recipientApplyer) {
        this.recipientApplyer = recipientApplyer == null ? null : recipientApplyer.trim();
    }

    public String getRecipientDepartment() {
        return recipientDepartment;
    }

    public void setRecipientDepartment(String recipientDepartment) {
        this.recipientDepartment = recipientDepartment == null ? null : recipientDepartment.trim();
    }

    public Integer getRecipientType() {
        return recipientType;
    }

    public void setRecipientType(Integer recipientType) {
        this.recipientType = recipientType;
    }

    public String getAppleOutDate() {
        return appleOutDate;
    }

    public void setAppleOutDate(String appleOutDate) {
        this.appleOutDate = appleOutDate == null ? null : appleOutDate.trim();
    }

    public String getUseNature() {
        return useNature;
    }

    public void setUseNature(String useNature) {
        this.useNature = useNature == null ? null : useNature.trim();
    }

    public String getUseName() {
        return useName;
    }

    public void setUseName(String useName) {
        this.useName = useName == null ? null : useName.trim();
    }

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver == null ? null : receiver.trim();
    }

    public String getReceiverAddress() {
        return receiverAddress;
    }

    public void setReceiverAddress(String receiverAddress) {
        this.receiverAddress = receiverAddress == null ? null : receiverAddress.trim();
    }

    public String getReceiverTelphone() {
        return receiverTelphone;
    }

    public void setReceiverTelphone(String receiverTelphone) {
        this.receiverTelphone = receiverTelphone == null ? null : receiverTelphone.trim();
    }

    public String getDetailAddress() {
        return detailAddress;
    }

    public void setDetailAddress(String detailAddress) {
        this.detailAddress = detailAddress == null ? null : detailAddress.trim();
    }

    public String getApplyReason() {
        return applyReason;
    }

    public void setApplyReason(String applyReason) {
        this.applyReason = applyReason == null ? null : applyReason.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getRealOutputTime() {
        return realOutputTime;
    }

    public void setRealOutputTime(String realOutputTime) {
        this.realOutputTime = realOutputTime == null ? null : realOutputTime.trim();
    }

    public String getCustomerReceiveTime() {
        return customerReceiveTime;
    }

    public void setCustomerReceiveTime(String customerReceiveTime) {
        this.customerReceiveTime = customerReceiveTime == null ? null : customerReceiveTime.trim();
    }

    public String getAddTime() {
        return addTime;
    }

    public void setAddTime(String addTime) {
        this.addTime = addTime == null ? null : addTime.trim();
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime == null ? null : updateTime.trim();
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    public String getUpdator() {
        return updator;
    }

    public void setUpdator(String updator) {
        this.updator = updator == null ? null : updator.trim();
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public String getBorrowTraderName() {
        return borrowTraderName;
    }

    public void setBorrowTraderName(String borrowTraderName) {
        this.borrowTraderName = borrowTraderName;
    }

    public String getReceiverPhone() {
        return receiverPhone;
    }

    public void setReceiverPhone(String receiverPhone) {
        this.receiverPhone = receiverPhone;
    }

    public String getApprovalTime() {
        return approvalTime;
    }

    public void setApprovalTime(String approvalTime) {
        this.approvalTime = approvalTime;
    }

    public Integer getOutStatus() {
        return outStatus;
    }

    public void setOutStatus(Integer outStatus) {
        this.outStatus = outStatus;
    }

    public Integer getApplyerId() {
        return applyerId;
    }

    public void setApplyerId(Integer applyerId) {
        this.applyerId = applyerId;
    }

    public Integer getApplyerDepartmentId() {
        return applyerDepartmentId;
    }

    public void setApplyerDepartmentId(Integer applyerDepartmentId) {
        this.applyerDepartmentId = applyerDepartmentId;
    }
}