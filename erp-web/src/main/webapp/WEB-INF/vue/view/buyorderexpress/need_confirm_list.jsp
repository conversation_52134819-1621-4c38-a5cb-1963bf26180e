<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
            + path + "/";
%>
<c:set var="path" value="<%=basePath%>" scope="application" />
<link rel="stylesheet" href="<%=basePath%>static/css/general.css?rnd=${resourceVersionKey}" />
<link rel="stylesheet" href="<%=basePath%>static/css/manage.css" />
<div id="app" style="display: none;">
    <el-card class="box-card J-block">
        <div slot="header" class="clearfix">
            <span>直发快递待确认列表</span>
        </div>
        <template>
            <el-form :inline="true" :model="buyOrderExpressDto" :rules="searchRules" ref="form">
                <el-form-item label="快递单号" size="mini" prop="searchExpressNo">
                    <el-input v-model="buyOrderExpressDto.searchExpressNo" size="mini" placeholder="" style="width: 200px"></el-input>
                </el-form-item>
                <el-form-item label="关联单号" size="mini" prop="searchRelatedNo">
                    <el-input v-model="buyOrderExpressDto.searchRelatedNo" size="mini" placeholder="" style="width: 200px"></el-input>
                </el-form-item>
                <el-form-item label="发货日期" size="mini">
                    <el-date-picker
                            v-model="buyOrderExpressDto.searchDeliveryDate"
                            type="daterange"
                            align="right" size="mini"
                            unlink-panels
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            :default-time="['00:00:00', '23:59:59']"
                            :picker-options="pickerOptions">
                    </el-date-picker>
                </el-form-item>
            </el-form>

            <el-row style="margin-bottom: 20px; text-align: center">
                <el-button type="primary" :loading="loading" @click="submitForm()" size="mini">搜索</el-button>
                <el-button type="primary" plain @click="reset()" size="mini">重置</el-button>
            </el-row>

            <el-table size="mini"
                    :data="buyOrderExpressDtoList"
                    ref="distributionLinkTable"
                    border
                    style="width: 100%; "
                    v-loading="loading"
                    key="traderContact">
                <el-table-column
                        align="center"
                        text-align="center"
                        label="快递单号" width="140">
                    <template slot-scope="scope">
                        <el-row>{{scope.row.logisticsNo || '-'}}</el-row>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        label="快递公司" width="100">
                    <template slot-scope="scope">
                        <span>{{scope.row.logisticsName || '-'}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        label="关联单号" width="220">
                    <template slot-scope="scope">
                        <el-link type="primary"
                                 :underline="false"
                                 @click="saleorderInfo(scope.row.saleorderId)">{{scope.row.saleorderNo}}</el-link>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        label="发件人" width="120">
                    <template slot-scope="scope">
                        <span>{{scope.row.addresser || '-'}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        label="发货日期" width="100">
                    <template slot-scope="scope">
                        <span>{{scope.row.deliveryTimeStr || '-'}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        label="收件名称" width="100">
                    <template slot-scope="scope">
                        <span>{{scope.row.receiver || '-'}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        label="收货地址">
                    <template slot-scope="scope">
                        <span>{{scope.row.receiverAddress || '-'}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        label="快递备注">
                    <template slot-scope="scope">
                        <span>{{scope.row.logisticsComments || '-'}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        label="操作" width="80">
                    <template slot-scope="scope" >
                        <el-button type="text" size="small" @click="showMessageList(scope.row.expressId)">备注</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPageNo"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="currentSize"
                    layout="->,total, sizes, prev, pager, next, jumper"
                    :total="totalLines">
            </el-pagination>
        </template>
    </el-card>

    <el-dialog size="mini"
            title="备注"
            :visible.sync="dialogVisible"
            :close-on-click-modal="false"
            width="620px">
        <div style="max-height: 280px; overflow: auto; margin-bottom: 10px;">
            <el-table :data="communicationRecords"   size="mini">
                <el-table-column prop="addTime" label="添加时间" width="140">
                    <template slot-scope="scope">
                        {{ new Date(scope.row.addTime).toLocaleString() }}
                    </template>
                </el-table-column>
                <el-table-column prop="addUserName" label="添加人" width="120"></el-table-column>
                <el-table-column prop="content" label="备注" width="300"></el-table-column>
            </el-table>
        </div>
        <el-tag size="medium">添加备注</el-tag>
        <el-input
                type="textarea"
                :rows="4"
                placeholder="请输入备注，最多200个字符"
                v-model="newRecordContent"
                @input="handleInput">
        </el-input>
        <span slot="footer" class="dialog-footer">

        <el-button type="primary" @click="addCommunicationRecord(currentlySelectedExpressId, newRecordContent)"  :disabled="isSubmitting">确定</el-button>
        <el-button @click="dialogVisible = false">取消</el-button>
    </span>
    </el-dialog>
</div>

<script src="${pageContext.request.contextPath}/static/api/buyorder/express.js?rnd=${resourceVersionKey}"></script>

<script type="text/javascript">
    let vm = new Vue({
        el: '#app',
        data() {
            return {
                currentSize: 10,
                currentPageNo: 1,
                totalLines: 0,

                loading: false,
                dialogVisible: false,
                isSubmitting: false, //是否正在提交
                communicationRecords: [], // 沟通记录列表
                newRecordContent: '', // 新增沟通记录的内容
                maxChars: 200, // 最大输入字符数
                currentlySelectedExpressId: null, // 当前选中的expressId
                buyOrderExpressDtoList:[],

                buyOrderExpressDto: {
                    searchExpressNo : '',
                    searchRelatedNo : '',
                    searchDeliveryDate: []
                },

                searchRules: {
                    searchExpressNo: [{max: 50, message: "最多输入50个字符", trigger: "blur"}],
                    searchRelatedNo: [{max: 50, message: "最多输入50个字符", trigger: "blur"}]
                },

                pickerOptions: {
                    shortcuts: [{
                        text: '最近一周',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近一个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近三个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
                }
            }
        },

        mounted() {
            loadingApp();
        },

        created() {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            this.buyOrderExpressDto.searchDeliveryDate = [start, end];
            this.doSearch();
        },

        methods: {
            handleInput() {
                if (this.newRecordContent.length > this.maxChars) {
                    this.newRecordContent = this.newRecordContent.substring(0, this.maxChars);
                }
            },
            submitForm() {
                this.$refs.form.validate(valid => {
                    if(valid) {
                        this.currentPageNo = 1;
                        this.doSearch();
                    } else {
                        return false;
                    }
                })
            },

            // 重置按钮
            reset() {
                this.buyOrderExpressDto.searchExpressNo = '';
                this.buyOrderExpressDto.searchRelatedNo = '';
                this.buyOrderExpressDto.searchDeliveryDate = [];
                this.$nextTick(() => {
                    this.doSearch();
                });
            },

            doSearch() {
                var pageParam = {
                    "pageSize": this.currentSize,
                    "pageNum": this.currentPageNo,
                    "param": {
                        searchExpressNo: this.buyOrderExpressDto.searchExpressNo,
                        searchRelatedNo: this.buyOrderExpressDto.searchRelatedNo,
                        searchDeliveryDateStart: this.buyOrderExpressDto.searchDeliveryDate == null ? null : this.buyOrderExpressDto.searchDeliveryDate[0],
                        searchDeliveryDateEnd: this.buyOrderExpressDto.searchDeliveryDate == null ? null : this.buyOrderExpressDto.searchDeliveryDate[1]
                    }
                };
                this.loading = true;
                getNeedConfirmList(pageParam).then(res => {
                    this.loading = false;
                    console.log("res", res);
                    this.buyOrderExpressDtoList = res.data.data.list;
                    this.totalLines = res.data.data.total;
                })
            },

            // 查看销售订单详情
            saleorderInfo(saleorderId) {
                console.log('/orderstream/saleorder/detail.do?saleOrderId='+saleorderId+'&scene=0');
                openTab("销售详情", '/orderstream/saleorder/detail.do?saleOrderId='+saleorderId+'&scene=0');
            },

            handleSizeChange(val) {
                this.currentSize = val;
                this.currentPageNo = 1;
                this.doSearch();
            },

            handleCurrentChange(val) {
                this.currentPageNo = val;
                this.doSearch();
            },
            showMessageList(expressId) {
                this.currentlySelectedExpressId = expressId;
                // 显示弹框
                this.dialogVisible = true;
                // 发送请求获取沟通记录列表
                this.getCommunicationRecords(expressId);

            },

            getCommunicationRecords(expressId) {
                console.log(this.communicationRecords );
                // 这里需要根据实际情况调整API的URL和参数
                axios.get('/buyOrder/express/api/needConfirmMessageList.do', {
                    params: {
                        expressId: expressId
                    }
                }).then(response => {
                    this.communicationRecords = response.data.data; // 假设返回的数据是数组格式
                    console.log(this.communicationRecords );
                }).catch(error => {
                    console.error('获取沟通记录失败:', error);
                });
            },

            // 添加沟通记录的方法
            addCommunicationRecord(expressId, content) {
                if(content == '') {
                    this.$message.error('请输入备注内容');
                    return;
                }
                this.isSubmitting = true;
                console.log(expressId);
                // 这里需要根据实际情况调整API的URL和参数
                axios.post('/buyOrder/express/api/addCommunicationRecord.do', {
                    expressId: expressId,
                    content: content,
                    // 其他可能需要的参数，如添加时间、添加人等
                }).then(response => {

                    if(response.data.code>=0){
                        // 添加成功后的操作
                        this.$message.success('备注添加成功');
                        // 刷新沟通记录列表
                        this.getCommunicationRecords(expressId);
                        this.dialogVisible = false;
                        this.newRecordContent = '';
                    }else{
                        this.$message.error('备注添加失败');
                    }
                    this.isSubmitting = false;

                }).catch(error => {
                    console.error('备注添加失败:', error);
                });
            }
        }
    })
</script>


<style>
    .el-dialog__body {
        padding: 0px 20px;
    }
    .el-card__body {
        margin-top:10px;
        padding: 10px;
    }
    .el-form-item{
        margin-bottom: 10px;
    }
    .el-card__header {
        display: none;
        height: 34px;
        background-color: #c5ddfb;
        box-sizing: border-box;
        padding-left: 15px;
        font-weight: 700;
        padding-top: 7px;
        padding-bottom: 22px;
        color: #303133;
    }

    .el-input--mini .el-input__icon {
        line-height: 0;
    }

    .el-pagination .btn-next .el-icon, .el-pagination .btn-prev .el-icon {
        margin-right: 5px;
    }

    .el-date-editor .el-range__icon {
        display: contents;
    }

    .el-date-editor .el-range__close-icon{
        display: contents;
    }

    .el-date-editor .el-range-separator {
        width: 10%;
    }

    .el-select .el-input .el-select__caret {
        display: none;
    }
    i{
        background:none;
    }
</style>