package com.vedeng.activiti;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class ProcessKeyGenerator {

    private static final String SEPARATOR = "_";

    private final String prefix;

    private ProcessKeyGenerator(String prefix) {
        this.prefix = prefix;
    }


    public static ProcessKeyGenerator createWithPrefix(String prefix) {
        if (!checkNotBlank(prefix)) {
            throw new IllegalArgumentException("prefix is null");
        }
        return new ProcessKeyGenerator(prefix);
    }


    public String generate(String suffix) {
        if (!checkNotBlank(suffix)) {
            throw new IllegalArgumentException("suffix is null");
        }
        return String.join(SEPARATOR, prefix, suffix);
    }

    public String generate(String suffix,String type) {
        if (!checkNotBlank(suffix)) {
            throw new IllegalArgumentException("suffix is null");
        }
        if (!checkNotBlank(type)) {
            throw new IllegalArgumentException("type is null");
        }
        return String.join(SEPARATOR, prefix,type, suffix);
    }


    private static boolean checkNotBlank(String str) {
        return str != null && !str.trim().isEmpty();
    }
}
