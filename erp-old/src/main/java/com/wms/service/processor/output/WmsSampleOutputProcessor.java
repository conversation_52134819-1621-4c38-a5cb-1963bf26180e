package com.wms.service.processor.output;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.util.DateUtil;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.wms.constant.OutputStatusConstant;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.constant.WmsLogicalOperateTypeEnum;
import com.wms.dao.WmsOutputOrderGoodsMapper;
import com.wms.dao.WmsOutputOrderMapper;
import com.wms.dto.OutputDto;
import com.wms.dto.OutputGoodDto;
import com.wms.dto.StockCalculateDto;
import com.wms.model.po.WmsOutputOrder;
import com.wms.model.po.WmsOutputOrderGoods;
import com.wms.service.context.ThreadLocalContext;
import com.wms.service.processor.AbstractOutputOrderProcessor;
import com.wms.service.stockcalculate.StockinfoCaculateInterface;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 样品出库单 -> 出库处理器
 */
@Service
public class WmsSampleOutputProcessor extends AbstractOutputOrderProcessor {

    private static final Logger LOGGER = LoggerFactory.getLogger(WmsSampleOutputProcessor.class);

    @Autowired
    private WmsOutputOrderMapper outputOrderMapper;

    @Autowired
    private WmsOutputOrderGoodsMapper outputOrderGoodsMapper;

    @Autowired
    @Qualifier("lendOutCaculateImpl")
    private StockinfoCaculateInterface stockinfoCaculateInterface;


    @Override
    protected void commonValidator(OutputDto outputDto) throws Exception {

        if (!WmsInterfaceOrderType.SAMPLE_ORDER_OUT.equals(outputDto.getOrderType())) {
            throw new Exception("出库单:" + outputDto.getOrderType() + "的类型错误!");
        }

    }

    @Override
    protected void updateOrderData(OutputDto outputDto) {

        String sampleOutNo = outputDto.getSOReference1();

        List<OutputGoodDto> outputOrderGoodsList = outputDto.getDetails();

        WmsOutputOrder wmsOutputOrder = outputOrderMapper.selectByOrderNo(sampleOutNo);

        List<WmsOutputOrderGoods> outputOrderGoodList = outputOrderGoodsMapper.queryOutputGoodsByLendOutId(wmsOutputOrder.getId());

        ThreadLocalContext.put("outputOrderGoodList", outputOrderGoodList);

        boolean isAllOut = true;

        String currentTime = DateUtil.convertString(System.currentTimeMillis(), DateUtil.TIME_FORMAT);

        for (WmsOutputOrderGoods outputOrderGood : outputOrderGoodList) {

            int outputNum = outputOrderGoodsList.stream()
                    .filter(g -> g.getSKU().equals(outputOrderGood.getSkuNo()))
                    .mapToInt(g -> g.getQtyShipped().intValue())
                    .sum();

            int realNum = outputOrderGood.getOutputNum();

            WmsOutputOrderGoods updateGood = new WmsOutputOrderGoods();
            updateGood.setId(outputOrderGood.getId());
            updateGood.setAlreadyOutputNum(outputOrderGood.getAlreadyOutputNum() + outputNum);


            updateGood.setLastOutputTime(currentTime);
            updateGood.setUpdateTime(currentTime);
            updateGood.setOutStatus(updateGood.getAlreadyOutputNum() == 0 ? OutputStatusConstant.UN_OUTPUT : updateGood.getAlreadyOutputNum() >= realNum ? OutputStatusConstant.OUTPUTED : OutputStatusConstant.PART_OUTPUT);

            LOGGER.info("商品样品单出库，更新样品出库单商品的数据: updateGood:{}", JSON.toJSONString(updateGood));
            outputOrderGoodsMapper.updateByPrimaryKeySelective(updateGood);

            //未全部发货
            if (OutputStatusConstant.OUTPUTED > updateGood.getOutStatus()) {
                isAllOut = false;
            }
        }

        WmsOutputOrder outputOrderUpdate = new WmsOutputOrder();
        outputOrderUpdate.setId(wmsOutputOrder.getId());
        outputOrderUpdate.setUpdateTime(currentTime);
        outputOrderUpdate.setOutStatus(isAllOut ? OutputStatusConstant.OUTPUTED : OutputStatusConstant.PART_OUTPUT);

        LOGGER.info("商品样品单出库，更新样品出库单的数据, outputOrderUpdate:{}", JSON.toJSONString(outputOrderUpdate));
        this.outputOrderMapper.updateByPrimaryKeySelective(outputOrderUpdate);
    }

    @Override
    protected int getOperateType(OutputDto requestBean) {
        return StockOperateTypeConst.SAMPLE_ORDER_OUT;
    }

    @Override
    protected int getWmsLogicalOperateType() {
        return WmsLogicalOperateTypeEnum.SAMPLE_OUT.getOperateTypeCode();
    }

    @Override
    protected int getRelateId(OutputGoodDto outputGoodDto) {
        return Integer.valueOf(outputGoodDto.getUserDefine1());
    }

    @Override
    protected List<WarehouseDto> getWarehouseStockList(List<StockCalculateDto> stockCalculateList) {
        return stockinfoCaculateInterface.calculateStockInfo(stockCalculateList);
    }


}
