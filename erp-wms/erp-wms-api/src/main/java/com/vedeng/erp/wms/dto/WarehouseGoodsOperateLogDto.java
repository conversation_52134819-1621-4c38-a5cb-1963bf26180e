package com.vedeng.erp.wms.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
    * 产品出入库日志
    */
@Data
public class WarehouseGoodsOperateLogDto implements Serializable {
    private Integer warehouseGoodsOperateLogId;

    /**
    * 产品条码ID
    */
    private Integer barcodeId;

    /**
    * 公司ID
    */
    private Integer companyId;

    /**
    * 操作类型1入库 2出库3销售换货入库4销售换货出库5销售退货入库6采购退货出库7采购换货出库8采购换货入库 9外借入库 10外借出库 11调整盘盈入库 12盘盈入库 13报废出库 14领用出库 15 调整盘亏 16 盘亏出库 17采购赠品入库 18样品出库 19单位转换出库 20单位转换入库
    */
    private Integer operateType;

    /**
    * 关联采购、销售、售后产品ID
    */
    private Integer relatedId;

    /**
    * 出库时对应拣货单详细ID
    */
    private Integer warehousePickingDetailId;

    /**
    * 商品ID
    */
    private Integer goodsId;

    /**
    * SN码
    */
    private String barcodeFactory;

    /**
    * 产品数量
    */
    private Integer num;

    /**
    * 仓库ID
    */
    private Integer warehouseId;

    /**
    * 库房ID
    */
    private Integer storageRoomId;

    /**
    * 货区ID
    */
    private Integer storageAreaId;

    /**
    * 库位ID
    */
    private Integer storageLocationId;

    /**
    * 货架ID
    */
    private Integer storageRackId;

    /**
    * 厂商批号
    */
    private String batchNumber;

    /**
    * 效期
    */
    private Long expirationDate;

    /**
    * 入库验收0未验收1验收通过2不通过
    */
    private Integer checkStatus;

    /**
    * 入库验收人
    */
    private Integer checkStatusUser;

    /**
    * 入库时间
    */
    private Long checkStatusTime;

    /**
    * 出库复核0未复核1通过2不通过
    */
    private Integer recheckStatus;

    /**
    * 出库复核人
    */
    private Integer recheckStatusUser;

    /**
    * 出库复核时间
    */
    private Long recheckStatusTime;

    /**
    * 备注
    */
    private String comments;

    /**
    * 是否有效 0否 1是
    */
    private Integer isEnable;

    /**
    * 是否已绑定快递0否 1是
    */
    private Integer isExpress;

    /**
    * 创建时间
    */
    private Long addTime;

    /**
    * 创建人
    */
    private Integer creator;

    /**
    * 更新时间
    */
    private Long modTime;

    /**
    * 更新人
    */
    private Integer updater;

    /**
    * 0 否 1 是
    */
    private Integer isProblem;

    /**
    * 问题备注
    */
    private String problemRemark;

    /**
    * 生产日期
    */
    private Long productDate;

    /**
    * 成本价
    */
    private BigDecimal costPrice;

    /**
    * 入库条码是否使用  0未使用  1使用
    */
    private Integer isUse;

    /**
    * 逻辑仓id 
    */
    private Integer logicalWarehouseId;

    /**
    * 贝登批次码
    */
    private String vedengBatchNumer;

    /**
    * 剩余库存数量
    */
    private Integer lastStockNum;

    /**
    * 灭菌批号
    */
    private String sterilzationBatchNumber;

    /**
    * 日志类型 0入库 1出库
    */
    private Integer logType;

    /**
    * 成本价(新)
    */
    private BigDecimal newCostPrice;

    /**
    * 来源信息
    */
    private String tagSources;

    /**
    * 专向发货关联VP单号
    */
    private String dedicatedBuyorderNo;

    private static final long serialVersionUID = 1L;
}