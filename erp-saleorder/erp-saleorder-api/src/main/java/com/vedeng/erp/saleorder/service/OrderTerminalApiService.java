package com.vedeng.erp.saleorder.service;

import com.vedeng.erp.saleorder.dto.OrderTerminalDto;
import com.vedeng.erp.saleorder.dto.SaleOrderTerminalDto;

import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.saleorder.service
 * @Date 2023/9/4 14:50
 */
public interface OrderTerminalApiService {

    /**
     * 根据erp业务类型和业务单id查询关联的终端信息
     *
     * @param businessId   业务id
     * @param businessType 业务类型 0:销售订单 1:商机单 2:报价单
     * @return OrderTerminalDto
     */
    OrderTerminalDto getTerminalInfoByBusinessIdAndBusinessType(Integer businessId, Integer businessType);

    /**
     * 保存终端信息
     *
     * @param terminalDto OrderTerminalDto
     */
    void save(OrderTerminalDto terminalDto);

    /**
     * 删除终端信息
     *
     * @param terminalDto OrderTerminalDto
     */
    void remove(OrderTerminalDto terminalDto);
    List<SaleOrderTerminalDto> findSaleOrderTerminalBySaleOrderIdAPI(Integer saleorderId);
}
