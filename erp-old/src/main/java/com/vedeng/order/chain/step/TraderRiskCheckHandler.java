package com.vedeng.order.chain.step;

import com.vedeng.common.util.StringUtil;
import com.vedeng.order.chain.AbstractRiskHandlerStep;
import com.vedeng.order.chain.RiskEnum;
import com.vedeng.order.chain.RiskHandlerKeyConstant;
import com.vedeng.order.chain.model.RiskModel;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.model.Trader;
import com.wms.service.chain.HandlerStepContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Set;

/**
 * <AUTHOR>
 * @ClassName TraderRiskCheckHandler.java
 * @Description TODO 客户风控信息校验
 * @createTime 2020年12月08日 15:47:00
 */
@Service
public class TraderRiskCheckHandler extends AbstractRiskHandlerStep {
    Logger logger = LoggerFactory.getLogger(TraderRiskCheckHandler.class);
    @Resource
    private TraderMapper traderMapper;

    @Override
    public RiskModel doDealWith(HandlerStepContext context) {
        RiskModel riskModel = context.get(RiskHandlerKeyConstant.RISK_MODEL);
        Integer traderId = context.get(RiskHandlerKeyConstant.TRADER_ID);

        boolean flag = true;
        Trader trader = traderMapper.getTraderByTraderId(traderId);
        Trader traderAptitudeCheckedByTraderId = traderMapper.getTraderAptitudeCheckedByTraderId(traderId);
        String aptitMessage = "";
        String riskMessage = "";
        //客户资质审核通过
        if(traderAptitudeCheckedByTraderId == null){
            flag = false;
            aptitMessage = "客户资质尚未提交审核";
        }
        if(trader==null){
            logger.info("TraderRiskCheckHandler null traderId={}",traderId);
            return riskModel;
        }     
        //客户名称、仓库地址
        if(StringUtil.isBlank(trader.getWarehouseDetailAddress())){
            riskMessage = "客户的仓库地址未维护";
            flag = false;
        }else if(StringUtil.isBlank(trader.getTraderName())){
            riskMessage = "客户的名称未维护";
            flag = false;
        }

        if(!flag){
            Set<RiskEnum> riskEnumList = riskModel.getRiskEnumList();
            String message = "";
            if(StringUtil.isNotBlank(aptitMessage)){
                riskEnumList.add(RiskEnum.TRADER_QA_RISK);
                message = aptitMessage;
            }

            if(StringUtil.isNotBlank(riskMessage)){
                riskEnumList.add(RiskEnum.TRADER_RISK);
                message = message +" " + riskMessage;
            }
            if(StringUtil.isNotBlank(message)){
                message = message + ",请前往维护!";
            }
            riskModel.setRiskEnumList(riskEnumList);
            riskModel.setTraderRiskMessage(message);
            riskModel.setIsRisk(false);
            riskModel.setIsOneChek(false);
        }
        return riskModel;
    }
}
