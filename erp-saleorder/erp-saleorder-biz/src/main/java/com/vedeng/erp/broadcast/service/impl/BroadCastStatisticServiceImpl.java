package com.vedeng.erp.broadcast.service.impl;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSON;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.redis.RedisUtils;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.broadcast.StatisticDataDto;
import com.vedeng.erp.broadcast.StatisticDataDto.StatisticsData;
import com.vedeng.erp.broadcast.StatisticDataDto.StatisticsLineData;
import com.vedeng.erp.broadcast.domain.entity.BroadcastContentConfigEntity;
import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticAedNum;
import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticAedNumExample;
import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticIncomeDay;
import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticIncomeDayExample;
import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticIncomeMonth;
import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticIncomeMonthExample;
import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticVdAmount;
import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticVdAmountExample;
import com.vedeng.erp.broadcast.mapdao.BroadcastContentConfigMapper;
import com.vedeng.erp.broadcast.service.BroadCastStatisticService;
import com.vedeng.erp.common.broadcast.MessageSubjectEnum;
import com.vedeng.erp.common.broadcast.constants.BroadcastRedisKey;
import com.vedeng.erp.statistic.mapper.BroadcastStatisticAedNumMapper;
import com.vedeng.erp.statistic.mapper.BroadcastStatisticIncomeDayMapper;
import com.vedeng.erp.statistic.mapper.BroadcastStatisticIncomeMonthMapper;
import com.vedeng.erp.statistic.mapper.BroadcastStatisticVdAmountMapper;
import com.vedeng.infrastructure.feign.uac.UacWxUserInfoApiService;
import com.vedeng.uac.api.dto.UserInfoDto;

import cn.hutool.core.collection.CollUtil;


/**
 * 到款播报统计服务
 * @ClassName:  BroadCastStatisticServiceImpl
 * @author: Neil.yang
 * @date:   2025年7月18日 上午11:22:45
 * @Copyright:
 */
@Service
public class BroadCastStatisticServiceImpl implements BroadCastStatisticService {
	
	//日志
	public static Logger LOGGER = LoggerFactory.getLogger(BroadCastStatisticServiceImpl.class);
	
	@Autowired
	private BroadcastStatisticIncomeDayMapper broadcastStatisticIncomeDayMapper;
	
	@Autowired
	private BroadcastStatisticIncomeMonthMapper broadcastStatisticIncomeMonthMapper;
	
	@Autowired
	private BroadcastStatisticAedNumMapper broadcastStatisticAedNumMapper;
	
	@Autowired
	private BroadcastStatisticVdAmountMapper broadcastStatisticVdAmountMapper;
	
	@Autowired
	private BroadcastContentConfigMapper broadcastContentConfigMapper;
	
	@Autowired
	private UacWxUserInfoApiService uacWxUserInfoApiService;
	
	@Autowired
    private RedisUtils redisUtils;
	
	
	@Value("${redis_dbtype}")
	private String dbType;
	
	@Value("${rank_top_num:10}")
	private Integer rankTopNum;

	@Override
	public StatisticDataDto getBraodCastStatisticData() {
		StatisticDataDto statisticDataDto = new StatisticDataDto();
		//日到款处理
		List<StatisticsData> incomeDayDataList = getIncomeDayDataList();
		statisticDataDto.setIncomeDayDataList(incomeDayDataList);
		
		//月到款处理
		List<StatisticsData> incomeMonthDataList = getIncomeMonthDataList();
		statisticDataDto.setIncomeMonthDataList(incomeMonthDataList);
		
		//AED出库处理
		List<StatisticsData> aedNumDataList = getAedNumDataList();
		statisticDataDto.setAedNumDataList(aedNumDataList);
		
		//自有品牌出库金额处理
		List<StatisticsData> vdAmountDataList = getVdAmountDataList();
		statisticDataDto.setVdAmountDataList(vdAmountDataList);
		return statisticDataDto;
	}

	private List<StatisticsData> getIncomeDayDataList() {
		List<StatisticsData>  statisticsDataList = new ArrayList<>();
		BroadcastStatisticIncomeDayExample example = new BroadcastStatisticIncomeDayExample();
		example.createCriteria().andIsDelEqualTo(0).andStatisticsTimeEqualTo(DateUtil.DateToString(new Date(), DateUtils.DATE_FORMAT_10));
		example.setOrderByClause("AMOUNT DESC");
		List<BroadcastStatisticIncomeDay>  incomeDayList = broadcastStatisticIncomeDayMapper.selectByExample(example);
		
		//按照统计类型【1：个人；2：小组；3：部门】分组
		Map<Integer, List<BroadcastStatisticIncomeDay>> map = incomeDayList.stream().collect(Collectors.groupingBy(BroadcastStatisticIncomeDay::getStatisticsType));
		for (Map.Entry<Integer, List<BroadcastStatisticIncomeDay>> entry : map.entrySet()) {
			//统计的类型
			Integer type = entry.getKey();
			LOGGER.info("执行到款数据，类型【1：个人；2：小组；3：部门】:{}",type);
			//对应类型的原始数据
			List<BroadcastStatisticIncomeDay> incomeDayListByType = entry.getValue();
			List<StatisticsLineData> statisticsLineDataList = operateDayData(type,incomeDayListByType);
			if(CollectionUtils.isNotEmpty(statisticsLineDataList)) {
				StatisticsData singleData = new StatisticsData();
				singleData.setPicUrl(getPic(1,type,statisticsLineDataList.get(0)));
				singleData.setType(type);
				if(statisticsLineDataList.size()>rankTopNum) {
					statisticsLineDataList = statisticsLineDataList.subList(0, rankTopNum);
				}
				singleData.setStatisticsLineDataList(statisticsLineDataList);
				statisticsDataList.add(singleData);
			}
			
		}
		return statisticsDataList;
	}
	
	private List<StatisticsData> getIncomeMonthDataList() {
		List<StatisticsData>  statisticsDataList = new ArrayList<>();
		BroadcastStatisticIncomeMonthExample example = new BroadcastStatisticIncomeMonthExample();
		example.createCriteria().andIsDelEqualTo(0).andStatisticsTimeEqualTo(DateUtil.DateToString(new Date(), "yyyy-MM"));
		example.setOrderByClause(" ARCHIEVED_PRECENT DESC , AMOUNT DESC ");
		List<BroadcastStatisticIncomeMonth>  incomeMonthList = broadcastStatisticIncomeMonthMapper.selectByExample(example);
		
		//按照统计类型【1：个人；2：小组；3：部门】分组
		Map<Integer, List<BroadcastStatisticIncomeMonth>> map = incomeMonthList.stream().collect(Collectors.groupingBy(BroadcastStatisticIncomeMonth::getStatisticsType));
		for (Map.Entry<Integer, List<BroadcastStatisticIncomeMonth>> entry : map.entrySet()) {
			//统计的类型
			Integer type = entry.getKey();
			LOGGER.info("执行到款数据，类型【1：个人；2：小组；3：部门】:{}",type);
			//对应类型的原始数据
			List<BroadcastStatisticIncomeMonth> incomeMonthListByType = entry.getValue();
			List<StatisticsLineData> statisticsLineDataList = operateMonthData(type,incomeMonthListByType);
			if(CollectionUtils.isNotEmpty(statisticsLineDataList)) {
				StatisticsData singleData = new StatisticsData();
				singleData.setPicUrl(getPic(2,type,statisticsLineDataList.get(0)));
				singleData.setType(type);
				if(statisticsLineDataList.size()>rankTopNum) {
					statisticsLineDataList = statisticsLineDataList.subList(0, rankTopNum);
				}
				singleData.setStatisticsLineDataList(statisticsLineDataList);
				statisticsDataList.add(singleData);
			}
			
		}
		return statisticsDataList;
	}
	
	private List<StatisticsData> getAedNumDataList() {
		List<StatisticsData>  statisticsDataList = new ArrayList<>();
		BroadcastStatisticAedNumExample example = new BroadcastStatisticAedNumExample();
		example.createCriteria().andIsDelEqualTo(0).andStatisticsTimeEqualTo(DateUtil.DateToString(new Date(), "yyyy-MM"));
		example.setOrderByClause("WAREHOUSE_NUM DESC");
		List<BroadcastStatisticAedNum>  aedMonthList = broadcastStatisticAedNumMapper.selectByExample(example);
		
		//按照统计类型【1：个人；2：小组；3：部门】分组
		Map<Integer, List<BroadcastStatisticAedNum>> map = aedMonthList.stream().collect(Collectors.groupingBy(BroadcastStatisticAedNum::getStatisticsType));
		for (Map.Entry<Integer, List<BroadcastStatisticAedNum>> entry : map.entrySet()) {
			//统计的类型
			Integer type = entry.getKey();
			LOGGER.info("执行到款数据，类型【1：个人；2：小组；3：部门】:{}",type);
			//对应类型的原始数据
			List<BroadcastStatisticAedNum> aedMonthListByType = entry.getValue();
			List<StatisticsLineData> statisticsLineDataList = operateAedData(type,aedMonthListByType);
			if(CollectionUtils.isNotEmpty(statisticsLineDataList)) {
				StatisticsData singleData = new StatisticsData();
				singleData.setPicUrl(getPic(3,type,statisticsLineDataList.get(0)));
				singleData.setType(type);
				if(statisticsLineDataList.size()>rankTopNum) {
					statisticsLineDataList = statisticsLineDataList.subList(0, rankTopNum);
				}
				singleData.setStatisticsLineDataList(statisticsLineDataList);
				statisticsDataList.add(singleData);
			}
			
		}
		return statisticsDataList;
		
	}


	private List<StatisticsData> getVdAmountDataList() {
		List<StatisticsData>  statisticsDataList = new ArrayList<>();
		BroadcastStatisticVdAmountExample example = new BroadcastStatisticVdAmountExample();
		example.createCriteria().andIsDelEqualTo(0).andStatisticsTimeEqualTo(DateUtil.DateToString(new Date(), "yyyy-MM"));
		example.setOrderByClause("WAREHOUSE_AMOUNT DESC");
		List<BroadcastStatisticVdAmount>  vdAmountMonthList = broadcastStatisticVdAmountMapper.selectByExample(example);
		
		//按照统计类型【1：个人；2：小组；3：部门】分组
		Map<Integer, List<BroadcastStatisticVdAmount>> map = vdAmountMonthList.stream().collect(Collectors.groupingBy(BroadcastStatisticVdAmount::getStatisticsType));
		for (Map.Entry<Integer, List<BroadcastStatisticVdAmount>> entry : map.entrySet()) {
			//统计的类型
			Integer type = entry.getKey();
			LOGGER.info("执行到款数据，类型【1：个人；2：小组；3：部门】:{}",type);
			//对应类型的原始数据
			List<BroadcastStatisticVdAmount> vdAmountMonthListByType = entry.getValue();
			List<StatisticsLineData> statisticsLineDataList = operateVdAmountData(type,vdAmountMonthListByType);
			if(CollectionUtils.isNotEmpty(statisticsLineDataList)) {
				StatisticsData singleData = new StatisticsData();
				singleData.setPicUrl(getPic(4,type,statisticsLineDataList.get(0)));
				singleData.setType(type);
				if(statisticsLineDataList.size()>rankTopNum) {
					statisticsLineDataList = statisticsLineDataList.subList(0, rankTopNum);
				}
				singleData.setStatisticsLineDataList(statisticsLineDataList);
				statisticsDataList.add(singleData);
			}
		}
		return statisticsDataList;
	}

	
	

	/**
	 * 处理日到款数据
	 * @param type 统计类型【1：个人；2：小组；3：部门】
	 * @param list 
	 * @return
	 */
	private List<StatisticsLineData> operateDayData(Integer type, List<BroadcastStatisticIncomeDay> list) {
		List<StatisticsLineData>  resultList = new ArrayList<>();
		//如果是个人，取前三名的人员头像
		Map<Integer,String> userIdAliasHeadPicMap = new HashMap<>();
		if(type.equals(MessageSubjectEnum.SALES_SINGLE.getSubject())) {
			List<Integer> userIdList = list.stream().map(temp->temp.getUserId()).collect(Collectors.toList());
			//根据userIdList，到uac获取到用户头像信息
			RestfulResult<List<UserInfoDto>> uacResult = uacWxUserInfoApiService.getUserByUserIds(userIdList);
            if (uacResult.isSuccess() && CollUtil.isNotEmpty(uacResult.getData())){
                List<UserInfoDto> data = uacResult.getData();
                for (UserInfoDto userInfo : data) {
                	userIdAliasHeadPicMap.put(userInfo.getId(), userInfo.getAliasHeadPicture());
                }
            }
		}
		
		for (BroadcastStatisticIncomeDay broadcastStatisticIncomeDay : list) {
			StatisticsLineData statisticsLineData = new StatisticsLineData();
			statisticsLineData.setAmount(Objects.nonNull(broadcastStatisticIncomeDay.getAmount())?broadcastStatisticIncomeDay.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString():"");
			statisticsLineData.setDeptId(broadcastStatisticIncomeDay.getDeptId());
			statisticsLineData.setDeptName(broadcastStatisticIncomeDay.getDeptName());
			statisticsLineData.setTeamId(broadcastStatisticIncomeDay.getTeamId());
			statisticsLineData.setTeamName(broadcastStatisticIncomeDay.getTeamName());
			if(type.equals(MessageSubjectEnum.SALES_SINGLE.getSubject())) {
				statisticsLineData.setUserAliasHeadPic(userIdAliasHeadPicMap.get(broadcastStatisticIncomeDay.getUserId()));
			}
			statisticsLineData.setUserId(broadcastStatisticIncomeDay.getUserId());
			String userName = broadcastStatisticIncomeDay.getUserName();
			if(StringUtils.isNotEmpty(userName) && userName.contains(".")) {
				userName = userName.split("\\.")[0];
			}
			statisticsLineData.setUserName(userName);
			resultList.add(statisticsLineData);
		}
		return resultList;
	}
	
	/**
	 * 处理月到款数据
	 * @param type
	 * @param list
	 * @return
	 */
	private List<StatisticsLineData> operateMonthData(Integer type,List<BroadcastStatisticIncomeMonth> list) {
		List<StatisticsLineData>  resultList = new ArrayList<>();
		//如果是个人，取前三名的人员头像
		Map<Integer,String> userIdAliasHeadPicMap = new HashMap<>();
		if(type.equals(MessageSubjectEnum.SALES_SINGLE.getSubject())) {
			List<Integer> userIdList = list.stream().map(temp->temp.getUserId()).collect(Collectors.toList());
			//根据userIdList，到uac获取到用户头像信息
			RestfulResult<List<UserInfoDto>> uacResult = uacWxUserInfoApiService.getUserByUserIds(userIdList);
            if (uacResult.isSuccess() && CollUtil.isNotEmpty(uacResult.getData())){
                List<UserInfoDto> data = uacResult.getData();
                for (UserInfoDto userInfo : data) {
                	userIdAliasHeadPicMap.put(userInfo.getId(), userInfo.getAliasHeadPicture());
                }
            }
		}
		
		for (BroadcastStatisticIncomeMonth broadcastStatisticIncomeMonth : list) {
			StatisticsLineData statisticsLineData = new StatisticsLineData();
			statisticsLineData.setAmount(Objects.nonNull(broadcastStatisticIncomeMonth.getAmount())?broadcastStatisticIncomeMonth.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString():"");
			statisticsLineData.setDeptId(broadcastStatisticIncomeMonth.getDeptId());
			statisticsLineData.setDeptName(broadcastStatisticIncomeMonth.getDeptName());
			statisticsLineData.setTeamId(broadcastStatisticIncomeMonth.getTeamId());
			statisticsLineData.setTeamName(broadcastStatisticIncomeMonth.getTeamName());
			if(type.equals(MessageSubjectEnum.SALES_SINGLE.getSubject())) {
				statisticsLineData.setUserAliasHeadPic(userIdAliasHeadPicMap.get(broadcastStatisticIncomeMonth.getUserId()));
			}
			statisticsLineData.setArchievedPrecent(broadcastStatisticIncomeMonth.getArchievedPrecent());
			statisticsLineData.setUserId(broadcastStatisticIncomeMonth.getUserId());
			String userName = broadcastStatisticIncomeMonth.getUserName();
			if(StringUtils.isNotEmpty(userName) && userName.contains(".")) {
				userName = userName.split("\\.")[0];
			}
			statisticsLineData.setUserName(userName);
			resultList.add(statisticsLineData);
		}
		return resultList;
	}

	private List<StatisticsLineData> operateAedData(Integer type, List<BroadcastStatisticAedNum> list) {
		List<StatisticsLineData>  resultList = new ArrayList<>();
		//如果是个人，取前三名的人员头像
		Map<Integer,String> userIdAliasHeadPicMap = new HashMap<>();
		if(type.equals(MessageSubjectEnum.SALES_SINGLE.getSubject())) {
			List<Integer> userIdList = list.stream().map(temp->temp.getUserId()).collect(Collectors.toList());
			//根据userIdList，到uac获取到用户头像信息
			RestfulResult<List<UserInfoDto>> uacResult = uacWxUserInfoApiService.getUserByUserIds(userIdList);
            if (uacResult.isSuccess() && CollUtil.isNotEmpty(uacResult.getData())){
                List<UserInfoDto> data = uacResult.getData();
                for (UserInfoDto userInfo : data) {
                	userIdAliasHeadPicMap.put(userInfo.getId(), userInfo.getAliasHeadPicture());
                }
            }
		}
		
		for (BroadcastStatisticAedNum broadcastStatisticAedNum : list) {
			StatisticsLineData statisticsLineData = new StatisticsLineData();
			statisticsLineData.setWarehouseNum(broadcastStatisticAedNum.getWarehouseNum());
			statisticsLineData.setDeptId(broadcastStatisticAedNum.getDeptId());
			statisticsLineData.setDeptName(broadcastStatisticAedNum.getDeptName());
			statisticsLineData.setTeamId(broadcastStatisticAedNum.getTeamId());
			statisticsLineData.setTeamName(broadcastStatisticAedNum.getTeamName());
			if(type.equals(MessageSubjectEnum.SALES_SINGLE.getSubject())) {
				statisticsLineData.setUserAliasHeadPic(userIdAliasHeadPicMap.get(broadcastStatisticAedNum.getUserId()));
			}
			statisticsLineData.setUserId(broadcastStatisticAedNum.getUserId());
			String userName = broadcastStatisticAedNum.getUserName();
			if(StringUtils.isNotEmpty(userName) && userName.contains(".")) {
				userName = userName.split("\\.")[0];
			}
			statisticsLineData.setUserName(userName);
			resultList.add(statisticsLineData);
		}
		return resultList;
	}
	
	private List<StatisticsLineData> operateVdAmountData(Integer type,List<BroadcastStatisticVdAmount> list) {
		List<StatisticsLineData>  resultList = new ArrayList<>();
		//如果是个人，取前三名的人员头像
		Map<Integer,String> userIdAliasHeadPicMap = new HashMap<>();
		if(type.equals(MessageSubjectEnum.SALES_SINGLE.getSubject())) {
			List<Integer> userIdList = list.stream().map(temp->temp.getUserId()).collect(Collectors.toList());
			//根据userIdList，到uac获取到用户头像信息
			RestfulResult<List<UserInfoDto>> uacResult = uacWxUserInfoApiService.getUserByUserIds(userIdList);
            if (uacResult.isSuccess() && CollUtil.isNotEmpty(uacResult.getData())){
                List<UserInfoDto> data = uacResult.getData();
                for (UserInfoDto userInfo : data) {
                	userIdAliasHeadPicMap.put(userInfo.getId(), userInfo.getAliasHeadPicture());
                }
            }
		}
		
		for (BroadcastStatisticVdAmount broadcastStatisticVdAmount : list) {
			StatisticsLineData statisticsLineData = new StatisticsLineData();
			statisticsLineData.setWarehouseAmount(Objects.nonNull(broadcastStatisticVdAmount.getWarehouseAmount())?broadcastStatisticVdAmount.getWarehouseAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString():"");
			statisticsLineData.setDeptId(broadcastStatisticVdAmount.getDeptId());
			statisticsLineData.setDeptName(broadcastStatisticVdAmount.getDeptName());
			statisticsLineData.setTeamId(broadcastStatisticVdAmount.getTeamId());
			statisticsLineData.setTeamName(broadcastStatisticVdAmount.getTeamName());
			if(type.equals(MessageSubjectEnum.SALES_SINGLE.getSubject())) {
				statisticsLineData.setUserAliasHeadPic(userIdAliasHeadPicMap.get(broadcastStatisticVdAmount.getUserId()));
			}
			statisticsLineData.setUserId(broadcastStatisticVdAmount.getUserId());
			String userName = broadcastStatisticVdAmount.getUserName();
			if(StringUtils.isNotEmpty(userName) && userName.contains(".")) {
				userName = userName.split("\\.")[0];
			}
			statisticsLineData.setUserName(userName);
			resultList.add(statisticsLineData);
		}
		return resultList;
	}
	
	
	/**
	 * 获取图片信息
	 * @param project 播报项目 1=日播报，2=月播报，3=AED出库，4=自有品牌出库金额；
	 * @param type 	  统计类型【1：个人；2：小组；3：部门】
	 * @param statisticsLineData 统计数据
	 * @return
	 */
	private String getPic(int project, int type, StatisticsLineData statisticsLineData) {
		String picUrl = "http://#";
		//获取图片信息，专属的
		List<BroadcastContentConfigEntity> broadcastContentConfigStatisticsList = null;
		if(project==1 || project==2 ) {
			if(type==1) {
				String exclusiveTargetValues  = Objects.nonNull(statisticsLineData.getUserId())?String.valueOf(statisticsLineData.getUserId()):null;
				broadcastContentConfigStatisticsList = broadcastContentConfigMapper.selectByCondition(null, 1, exclusiveTargetValues, null, null, null, null);
			}else if(type==2) {
				String exclusiveTargetValues  = Objects.nonNull(statisticsLineData.getTeamId())?String.valueOf(statisticsLineData.getTeamId()):null;
				broadcastContentConfigStatisticsList = broadcastContentConfigMapper.selectByCondition(null, 2, exclusiveTargetValues, null, null, null, null);
			}else if(type==3) {
				String exclusiveTargetValues  = Objects.nonNull(statisticsLineData.getDeptId())?String.valueOf(statisticsLineData.getDeptId()):null;
				broadcastContentConfigStatisticsList = broadcastContentConfigMapper.selectByCondition(null, 2, exclusiveTargetValues, null, null, null, null);
			}
		}else if(project==3) {
			broadcastContentConfigStatisticsList = broadcastContentConfigMapper.selectByCondition(null, 3, "1", null, null, null, null);
		}else if(project==4) {
			broadcastContentConfigStatisticsList = broadcastContentConfigMapper.selectByCondition(null, 3, "2", null, null, null, null);
		}
		
		if(Objects.nonNull(broadcastContentConfigStatisticsList) && CollectionUtils.isNotEmpty(broadcastContentConfigStatisticsList)) {
			Collections.shuffle(broadcastContentConfigStatisticsList);
			picUrl = broadcastContentConfigStatisticsList.get(0).getPicUrl();
		}else {
			broadcastContentConfigStatisticsList = broadcastContentConfigMapper.selectByCondition(null, 0, null, null, null, null, null);
			if(Objects.nonNull(broadcastContentConfigStatisticsList)  && CollectionUtils.isNotEmpty(broadcastContentConfigStatisticsList) ) {
				Collections.shuffle(broadcastContentConfigStatisticsList);
				picUrl = broadcastContentConfigStatisticsList.get(0).getPicUrl();
			}
		}
		return picUrl;
	}
	
	
	
	
	
	
}
