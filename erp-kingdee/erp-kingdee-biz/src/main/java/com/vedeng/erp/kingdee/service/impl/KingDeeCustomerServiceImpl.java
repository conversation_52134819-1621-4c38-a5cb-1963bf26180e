package com.vedeng.erp.kingdee.service.impl;

import com.vedeng.erp.kingdee.batch.repository.BatchCustomerFinanceDtoMapper;
import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeeCustomerCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeCustomerEntity;
import com.vedeng.erp.kingdee.dto.KingDeeCustomerDto;
import com.vedeng.erp.kingdee.dto.KingDeeMaterialDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeCustomerCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeCustomerConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeCustomerRepository;
import com.vedeng.erp.kingdee.service.KingDeeCustomerApiService;
import com.vedeng.erp.kingdee.service.KingDeeCustomerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 客户信息处理类
 * @date 2022/9/8 14:20
 **/
@Service
@Slf4j
public class KingDeeCustomerServiceImpl extends KingDeeBaseServiceImpl<
        KingDeeCustomerEntity,
        KingDeeCustomerDto,
        KingDeeCustomerCommand,
        KingDeeCustomerRepository,
        KingDeeCustomerConvertor,
        KingDeeCustomerCommandConvertor
        > implements KingDeeCustomerService, KingDeeCustomerApiService {

    @Autowired
    private BatchCustomerFinanceDtoMapper batchCustomerFinanceDtoMapper;

    @Override
    public void updatePost(Object... objects) {
        super.updatePost(objects);
        KingDeeCustomerDto dto = getD(objects);
        batchCustomerFinanceDtoMapper.updateKingDeePushStatus(dto.getTraderCustomerFinanceId());
    }
}
