<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.VisitRecordLogNewMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.VisitRecordLog">
    <!--@mbg.generated-->
    <!--@Table T_VISIT_RECORD_LOG-->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="USER_ID" jdbcType="INTEGER" property="userId" />
    <result column="OPERATION_TIME" jdbcType="TIMESTAMP" property="operationTime" />
    <result column="OPERATION_TYPE" jdbcType="VARCHAR" property="operationType" />
    <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
    <result column="RECORD_ID" jdbcType="INTEGER" property="recordId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, USER_ID, OPERATION_TIME, OPERATION_TYPE, DESCRIPTION, RECORD_ID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_VISIT_RECORD_LOG
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_VISIT_RECORD_LOG
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.trader.domain.entity.VisitRecordLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_VISIT_RECORD_LOG (USER_ID, OPERATION_TIME, OPERATION_TYPE, 
      DESCRIPTION, RECORD_ID)
    values (#{userId,jdbcType=INTEGER}, #{operationTime,jdbcType=TIMESTAMP}, #{operationType,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{recordId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.trader.domain.entity.VisitRecordLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_VISIT_RECORD_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="operationTime != null">
        OPERATION_TIME,
      </if>
      <if test="operationType != null">
        OPERATION_TYPE,
      </if>
      <if test="description != null">
        DESCRIPTION,
      </if>
      <if test="recordId != null">
        RECORD_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="operationTime != null">
        #{operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationType != null">
        #{operationType,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="recordId != null">
        #{recordId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.VisitRecordLog">
    <!--@mbg.generated-->
    update T_VISIT_RECORD_LOG
    <set>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      <if test="operationTime != null">
        OPERATION_TIME = #{operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationType != null">
        OPERATION_TYPE = #{operationType,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        DESCRIPTION = #{description,jdbcType=VARCHAR},
      </if>
      <if test="recordId != null">
        RECORD_ID = #{recordId,jdbcType=INTEGER},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.VisitRecordLog">
    <!--@mbg.generated-->
    update T_VISIT_RECORD_LOG
    set USER_ID = #{userId,jdbcType=INTEGER},
      OPERATION_TIME = #{operationTime,jdbcType=TIMESTAMP},
      OPERATION_TYPE = #{operationType,jdbcType=VARCHAR},
      DESCRIPTION = #{description,jdbcType=VARCHAR},
      RECORD_ID = #{recordId,jdbcType=INTEGER}
    where ID = #{id,jdbcType=INTEGER}
  </update>
</mapper>