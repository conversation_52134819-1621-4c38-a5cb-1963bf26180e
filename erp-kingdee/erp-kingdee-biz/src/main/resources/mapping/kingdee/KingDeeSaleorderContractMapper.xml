<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.repository.mappers.KingDeeSaleorderContractMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.domain.entity.KingDeeSaleorderContractEntity">
    <!--@mbg.generated-->
    <!--@Table KING_DEE_SALEORDER_CONTRACT-->
    <id column="KING_DEE_SALEORDER_CONTRACT_ID" jdbcType="INTEGER" property="kingDeeSaleorderContractId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="F_ID" jdbcType="INTEGER" property="fId" />
    <result column="F_QZOK_OrgId" jdbcType="VARCHAR" property="fQzokOrgid" />
    <result column="F_QZOK_HTH" jdbcType="VARCHAR" property="fQzokHth" />
    <result column="F_QZOK_HTRQ" jdbcType="VARCHAR" property="fQzokHtrq" />
    <result column="F_QZOK_HTJE" jdbcType="VARCHAR" property="fQzokHtje" />
    <result column="F_QZOK_SLL" jdbcType="VARCHAR" property="fQzokSll" />
    <result column="F_QZOK_DDH" jdbcType="VARCHAR" property="fQzokDdh" />
    <result column="FBillNo" jdbcType="VARCHAR" property="FBillNo" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    KING_DEE_SALEORDER_CONTRACT_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, 
    UPDATER_NAME, F_ID, F_QZOK_OrgId, F_QZOK_HTH, F_QZOK_HTRQ, F_QZOK_HTJE, F_QZOK_SLL, 
    F_QZOK_DDH,FBillNo
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from KING_DEE_SALEORDER_CONTRACT
    where KING_DEE_SALEORDER_CONTRACT_ID = #{kingDeeSaleorderContractId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from KING_DEE_SALEORDER_CONTRACT
    where KING_DEE_SALEORDER_CONTRACT_ID = #{kingDeeSaleorderContractId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="KING_DEE_SALEORDER_CONTRACT_ID" keyProperty="kingDeeSaleorderContractId" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeSaleorderContractEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_SALEORDER_CONTRACT (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      F_ID, F_QZOK_OrgId, F_QZOK_HTH, 
      F_QZOK_HTRQ, F_QZOK_HTJE, F_QZOK_SLL, 
      F_QZOK_DDH,FBillNo)
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{fId,jdbcType=INTEGER}, #{fQzokOrgid,jdbcType=VARCHAR}, #{fQzokHth,jdbcType=VARCHAR},
      #{fQzokHtrq,jdbcType=VARCHAR}, #{fQzokHtje,jdbcType=VARCHAR}, #{fQzokSll,jdbcType=VARCHAR}, 
      #{fQzokDdh,jdbcType=VARCHAR},#{FBillNo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="KING_DEE_SALEORDER_CONTRACT_ID" keyProperty="kingDeeSaleorderContractId" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeSaleorderContractEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_SALEORDER_CONTRACT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="fId != null">
        F_ID,
      </if>
      <if test="fQzokOrgid != null">
        F_QZOK_OrgId,
      </if>
      <if test="fQzokHth != null">
        F_QZOK_HTH,
      </if>
      <if test="fQzokHtrq != null">
        F_QZOK_HTRQ,
      </if>
      <if test="fQzokHtje != null">
        F_QZOK_HTJE,
      </if>
      <if test="fQzokSll != null">
        F_QZOK_SLL,
      </if>
      <if test="fQzokDdh != null">
        F_QZOK_DDH,
      </if>
      <if test="FBillNo != null">
        FBillNo,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fId != null">
        #{fId,jdbcType=VARCHAR},
      </if>
      <if test="fQzokOrgid != null">
        #{fQzokOrgid,jdbcType=VARCHAR},
      </if>
      <if test="fQzokHth != null">
        #{fQzokHth,jdbcType=VARCHAR},
      </if>
      <if test="fQzokHtrq != null">
        #{fQzokHtrq,jdbcType=VARCHAR},
      </if>
      <if test="fQzokHtje != null">
        #{fQzokHtje,jdbcType=VARCHAR},
      </if>
      <if test="fQzokSll != null">
        #{fQzokSll,jdbcType=VARCHAR},
      </if>
      <if test="fQzokDdh != null">
        #{fQzokDdh,jdbcType=VARCHAR},
      </if>
      <if test="FBillNo != null">
        #{FBillNo,jdbcType=VARCHAR}
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeSaleorderContractEntity">
    <!--@mbg.generated-->
    update KING_DEE_SALEORDER_CONTRACT
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fId != null">
        F_ID = #{fId,jdbcType=INTEGER},
      </if>
      <if test="fQzokOrgid != null">
        F_QZOK_OrgId = #{fQzokOrgid,jdbcType=VARCHAR},
      </if>
      <if test="fQzokHth != null">
        F_QZOK_HTH = #{fQzokHth,jdbcType=VARCHAR},
      </if>
      <if test="fQzokHtrq != null">
        F_QZOK_HTRQ = #{fQzokHtrq,jdbcType=VARCHAR},
      </if>
      <if test="fQzokHtje != null">
        F_QZOK_HTJE = #{fQzokHtje,jdbcType=VARCHAR},
      </if>
      <if test="fQzokSll != null">
        F_QZOK_SLL = #{fQzokSll,jdbcType=VARCHAR},
      </if>
      <if test="fQzokDdh != null">
        F_QZOK_DDH = #{fQzokDdh,jdbcType=VARCHAR},
      </if>
    </set>
    where KING_DEE_SALEORDER_CONTRACT_ID = #{kingDeeSaleorderContractId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeSaleorderContractEntity">
    <!--@mbg.generated-->
    update KING_DEE_SALEORDER_CONTRACT
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      F_ID = #{fId,jdbcType=INTEGER},
      F_QZOK_OrgId = #{fQzokOrgid,jdbcType=VARCHAR},
      F_QZOK_HTH = #{fQzokHth,jdbcType=VARCHAR},
      F_QZOK_HTRQ = #{fQzokHtrq,jdbcType=VARCHAR},
      F_QZOK_HTJE = #{fQzokHtje,jdbcType=VARCHAR},
      F_QZOK_SLL = #{fQzokSll,jdbcType=VARCHAR},
      F_QZOK_DDH = #{fQzokDdh,jdbcType=VARCHAR}
    where KING_DEE_SALEORDER_CONTRACT_ID = #{kingDeeSaleorderContractId,jdbcType=INTEGER}
  </update>

  <select id="getFidByDDH" resultType="java.lang.Integer">
    select F_ID from KING_DEE_SALEORDER_CONTRACT where F_QZOK_DDH = #{saleorderNo,jdbcType=VARCHAR}
  </select>
</mapper>