package com.vedeng.order.enums;


/**
 * 报价单的咨询答复状态枚举
 *
 * <AUTHOR> [<EMAIL>]
 */
public enum QuotedConsultStateEnum {

    /**
     * 无
     */
    NONE(0, "无"),

    /**
     * 供应链未处理，主管未处理"
     */
    BOTH_NOT_DONE(1, "供应链未处理，主管未处理"),

    /**
     * 供应链部分处理，主管未处理
     */
    SUB_PARTIAL_DONE_AND_SUP_NOT_DONE(2, "供应链部分处理，主管未处理"),

    /**
     * 供应链已全部处理，主管未处理
     */
    SUB_DONE_AND_SUP_NOT_DONE(3, "供应链已全部处理，主管未处理"),

    /**
     * 供应链未处理，主管已处理
     */
    SUB_NOT_DONE_AND_SUP_DONE(4, "供应链未处理，主管已处理"),

    /**
     * 供应链部分处理，主管已处理
     */
    SUB_PARTIAL_DONE_AND_SUP_DONE(5, "供应链部分处理，主管已处理"),

    /**
     * 供应链全部处理，主管已处理
     */
    BOTH_DONE(6, "供应链全部处理，主管已处理")
    ;

    private Integer state;
    private String message;

    /**
     * Constructor with state and message.
     *
     * @param state
     * @param message
     */
    QuotedConsultStateEnum(Integer state, String message) {
        this.state = state;
        this.message = message;
    }


    public Integer getState() {
        return state;
    }

    public String getMessage() {
        return message;
    }}
