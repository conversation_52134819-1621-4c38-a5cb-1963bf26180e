package com.vedeng.erp.trader.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.trader.dto.DwhCustomerTagCpmAddDfDto;
import com.vedeng.erp.trader.mapper.DwhCustomerTagCpmAddDfMapper;
import com.vedeng.erp.trader.service.DwhCustomerTagCpmAddDfService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description
 * @date 2024/4/1 9:00
 **/
@Service
@Slf4j
public class DwhCustomerTagCpmAddDfServiceImpl implements DwhCustomerTagCpmAddDfService {

    @Autowired
    private DwhCustomerTagCpmAddDfMapper dwhCustomerTagCpmAddDfMapper;

    @Override
    public PageInfo<DwhCustomerTagCpmAddDfDto> page(PageParam<DwhCustomerTagCpmAddDfDto> query) {

        return PageHelper.startPage(query).doSelectPageInfo(() -> dwhCustomerTagCpmAddDfMapper.findByAll(query.getParam()));
    }
}
