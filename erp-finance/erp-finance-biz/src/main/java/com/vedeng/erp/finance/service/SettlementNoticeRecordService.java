package com.vedeng.erp.finance.service;

import com.vedeng.erp.finance.dto.SettlementNoticeRecordDto;

import java.util.List;
import java.util.Map;

public interface SettlementNoticeRecordService {
    
    List<SettlementNoticeRecordDto> querySettlementNoticeRecord(Integer bankBillId,Integer noticeType);
    
    Long createSettlementNoticeRecord(SettlementNoticeRecordDto settlementNoticeRecordDto);

    void updateSettlementNoticeRecord(SettlementNoticeRecordDto settlementNoticeRecordDto);

    Map<String, String> getUserNumber();
}
