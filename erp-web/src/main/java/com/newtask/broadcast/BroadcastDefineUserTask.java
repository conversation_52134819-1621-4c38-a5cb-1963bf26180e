package com.newtask.broadcast;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.broadcast.statistics.project.BroadcastUserDefine;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@JobHandler(value = "broadcastDefineUserTask")
@Component
public class BroadcastDefineUserTask extends AbstractJobHandler {

    Logger logger = LoggerFactory.getLogger(BroadcastDefineUserTask.class);
    
    @Autowired
    private BroadcastUserDefine broadcastUserDefine;
    
    @Override
    public ReturnT<String> doExecute(String params) throws Exception {
        XxlJobLogger.log("用户自定义播报 start-----------");
        logger.info("用户自定义播报  start-----------");
        try {
        	broadcastUserDefine.invocation(null,true);
        }catch(Exception e) {
        	XxlJobLogger.log("用户自定义播报失败");
        	logger.error("用户自定义播报失败");
        	return FAIL;
        }
        logger.info("用户自定义播报 end-----------");
        XxlJobLogger.log("用户自定义播报 end-----------");
        return SUCCESS;
    }
}
