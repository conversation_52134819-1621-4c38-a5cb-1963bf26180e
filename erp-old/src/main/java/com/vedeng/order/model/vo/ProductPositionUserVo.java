package com.vedeng.order.model.vo;

import com.vedeng.authorization.model.User;
import lombok.Data;

import java.util.List;

/**
 * .传输产品经理，产品助理信息
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2021/4/13 11:28.
 * @author: <PERSON><PERSON>.
 */
@Data
public class ProductPositionUserVo {

    Integer orderAssitantUserId;

    private List<User> productManagerList;

    private List<User> productAssistantList;

    String managerUser;

    String assUser;

}
