package com.vedeng.call.model;

/**
 * 电话信息实例
 *
 * <AUTHOR>
 */
public class PhonePrefixInfo {
    /**
     * 号码前缀
     */
    private String prefix;

    /**
     * 端口
     */
    private Integer port;

    /**
     * SIP账户
     */
    private String sipAccount;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 绑定号码
     */
    private String phoneNo;

    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getSipAccount() {
        return sipAccount;
    }

    public void setSipAccount(String sipAccount) {
        this.sipAccount = sipAccount;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPhoneNo() {
        return phoneNo;
    }

    public void setPhoneNo(String phoneNo) {
        this.phoneNo = phoneNo;
    }

    public PhonePrefixInfo() {
    }

    public PhonePrefixInfo(String prefix, Integer port, String sipAccount, String userName, String phoneNo) {
        this.prefix = prefix;
        this.port = port;
        this.sipAccount = sipAccount;
        this.userName = userName;
        this.phoneNo = phoneNo;
    }
}
