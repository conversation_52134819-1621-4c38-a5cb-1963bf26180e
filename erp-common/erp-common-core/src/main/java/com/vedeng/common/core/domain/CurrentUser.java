package com.vedeng.common.core.domain;

import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.utils.ServletUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 当前登录用户信息
 * @date 2022/2/14 11:28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class CurrentUser implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 登录用户id
     */
    private Integer id;

    /**
     * 真实名称
     */
    private String actualName;

    /**
     * 用户名称
     */
    private String username;

    /**
     * 用户直接上级id
     */
    private Integer parentId;

    /**
     * 用户部门
     */
    private Integer orgId;

    /**
     * 用户部门名称
     */
    private String orgName;

    /**
     * 是否管理员0普通用户1管理员2超级管理员
     */
    private Integer isAdmin;

    /**
     * 公司id
     */
    private Integer companyId;

    /**
     * 当前用户身份类型：销售、 采购...
     */
    private Integer positType;

    /**
     * 身份集合
     */
    private List<Integer> positionTypes;

    /**
     * 是否启用
     */
    private Integer isDisabled;

    /**
     * 权限
     */
    private Set<String> permissions = new HashSet();

    /**
     * 工号
     */
    private String number;

    /**
     * 头像信息
     */
    private String headPic;//头像信息

    /**
     * 获取当前登录用户
     * 当Servlet线程中不存在任何用户信息时，取admin账户信息
     *
     * @return CurrentUser
     */
    public static CurrentUser getCurrentUser() {
        CurrentUser currentUser = null;
        try {
            currentUser = (CurrentUser) ServletUtils.getRequest().getSession().getAttribute(ErpConstant.CURRENT_USER);
        } catch (Exception e) {
            log.debug("获取用户失败", e);//此处获取不到为正常情况，日志先还原
        }
        return currentUser == null ? CurrentUser.builder().id(ErpConstant.DEFAULT_USER_ID).username(ErpConstant.DEFAULT_USERNAME).isAdmin(0).orgId(0).companyId(1).build() : currentUser;
    }

}
