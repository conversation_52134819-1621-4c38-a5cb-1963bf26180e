package com.vedeng.erp.finance.service.impl.api;

import com.vedeng.erp.finance.domain.entity.InvoiceVoucherEntity;
import com.vedeng.erp.finance.mapper.InvoiceVoucherMapper;
import com.vedeng.erp.finance.service.InvoiceVoucherApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * @description: 发票金蝶凭证号接口实现类
 * @return:
 * @author: Suqin
 * @date: 2022/12/7 16:47
 **/
@Service
public class InvoiceVoucherApiServiceImpl implements InvoiceVoucherApiService {

    @Autowired
    InvoiceVoucherMapper invoiceVoucherMapper;

    @Override
    public boolean isPushedKingdee(Integer invoiceId) {
        InvoiceVoucherEntity invoiceVoucherEntity = invoiceVoucherMapper.findByInvoiceId(invoiceId.toString());
        return invoiceVoucherEntity != null;
    }
}
