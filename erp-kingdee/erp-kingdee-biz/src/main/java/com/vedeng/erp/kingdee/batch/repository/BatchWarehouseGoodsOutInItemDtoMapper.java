package com.vedeng.erp.kingdee.batch.repository;

import java.util.Collection;

import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInItemDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/9 12:06
 **/
public interface BatchWarehouseGoodsOutInItemDtoMapper {

    /**
     * select by primary key
     *
     * @param warehouseGoodsOutInDetailId primary key
     * @return object by primary key
     */
    BatchWarehouseGoodsOutInItemDto selectByPrimaryKey(Long warehouseGoodsOutInDetailId);

    /**
     * 主单查子单
     *
     * @param outInNo
     * @return
     */
    List<BatchWarehouseGoodsOutInItemDto> findByOutInNo(@Param("outInNo") String outInNo);


    /**
     * 查询所有的出入库单明细根据时间排序（出入库单维度）
     *
     * @param outInOrders
     * @return
     */
    List<BatchWarehouseGoodsOutInItemDto> findAllOutInGoodsByOrder(List<BatchWarehouseGoodsOutInDto> outInOrders);

    /**
     * 根据入库单产品查找对应SKU
     *
     * @param outInNo
     * @param relatedId
     * @return
     */
    String getSkuByIn(@Param("outInNo") String outInNo, @Param("relatedId") Integer relatedId);

    /**
     * 根据出库单产品查找对应SKU
     *
     * @param outInNo
     * @param relatedId
     * @return
     */
    String getSkuByOut(@Param("outInNo") String outInNo, @Param("relatedId") Integer relatedId);

    /**
     * 根据入库单产品查找对应SPU类型
     *
     * @param item
     * @return
     */
    Integer getSpuTypeByIn(BatchWarehouseGoodsOutInItemDto item);

    /**
     * 根据入库单商品查询对应销售单商品ID
     *
     * @param inGoods
     * @return
     */
    Integer getOrderDetailId(BatchWarehouseGoodsOutInItemDto inGoods);

    /**
     * 根据入库单商品查找所有相关的出库单商品
     *
     * @param outInItem
     * @param isSn
     * @param isBatch
     * @return
     */
    List<BatchWarehouseGoodsOutInItemDto> findAllOutByIn(@Param("outInItem") BatchWarehouseGoodsOutInItemDto outInItem, @Param("isSn") Boolean isSn, @Param("isBatch") Boolean isBatch, @Param("validTime") Long validTime);


    /**
     * 根据出入库记录类型，关联商品的业务id查询出入库记录
     */
    List<BatchWarehouseGoodsOutInItemDto> findByOperateTypeAndRelatedIdIn(@Param("operateType")Integer operateType,@Param("relatedIdCollection")Collection<Integer> relatedIdCollection);


    /**
     * @param afterSalesId 销售售后id
     * @return
     * @desc 获取销售售后单的（赠品入库）记录
     */
    List<BatchWarehouseGoodsOutInItemDto> getGiftGoodsWarehouseIn(@Param("afterSalesId") Integer afterSalesId);


    int insertSelective(BatchWarehouseGoodsOutInItemDto record);

    int updateByPrimaryKeySelective(BatchWarehouseGoodsOutInItemDto record);

    /**
     * 根据单号和sku查询明细信息
     *
     * @param outInNo
     * @param goodsId
     * @param operateType
     * @return
     * <AUTHOR>
     */
    BatchWarehouseGoodsOutInItemDto queryInfoByGoodsIdAndNo(@Param("outInNo") String outInNo, @Param("goodsId") Integer goodsId,
                                                            @Param("operateType") Integer operateType);

    /**
     * 根据销售单号查询该订单中赠品的出库记录信息
     *
     * @param orderNo 销售单号
     * @return List<BatchWarehouseGoodsOutInItemDto>
     */
    List<BatchWarehouseGoodsOutInItemDto> getGiftWarehouseItemByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 根据操作类型、SN码查询
     *
     * @param operateType
     * @param barcodeFactory
     * @return
     */
    List<BatchWarehouseGoodsOutInItemDto> findByBarcodeFactory(@Param("barcodeFactory") String barcodeFactory);


    List<BatchWarehouseGoodsOutInItemDto> findByOutInNoAndBarcodeFactory(@Param("outInNo") String outInNo,
                                                                         @Param("barcodeFactory") String barcodeFactory);


    /**
     * 根据关联id查询
     *
     * @param relatedId
     * @return
     */
    List<BatchWarehouseGoodsOutInItemDto> findByRelatedId(Integer relatedId);


    /**
     * 根据销售订单商品的主键id，查询销售换货出库单
     *
     * @param relatedId saleodergoods的主键id
     * @return
     */
    List<BatchWarehouseGoodsOutInItemDto> getWarehouseGoodsOutInBySaleOrderGoodsId(@Param("relatedId") Integer relatedId);

    int batchInsert(@Param("list") List<BatchWarehouseGoodsOutInItemDto> list);

    List<BatchWarehouseGoodsOutInItemDto> findByAll(BatchWarehouseGoodsOutInItemDto batchWarehouseGoodsOutInItemDto);

    int updateBatchBarcodeFactory(List<BatchWarehouseGoodsOutInItemDto> list);


    /**
     * 根据出入库记录类型，关联商品的业务id查询出入库记录
     */
    List<BatchWarehouseGoodsOutInItemDto> findByOperateTypeAndRelatedId(@Param("operateType") Integer operateType
            , @Param("relatedId") Integer relatedId, @Param("isVirtual") Integer isVirtual,@Param("relateNo") String relateNo);

    Integer findWarehouseGoodsOutInIdByItemId(@Param("changeWarehouseGoodsOutInDetailId") Integer changeWarehouseGoodsOutInDetailId);

    int batchInsertOld(@Param("list") List<BatchWarehouseGoodsOutInItemDto> list);


    /**
     * 查询 赠品数据 出库
     * @param outInNo
     * @return a.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID,a.RELATED_ID,a.OUT_IN_NO,a.NUM,b.SKU
     */
    List<BatchWarehouseGoodsOutInItemDto> selectByOutInNoAndIsGift(@Param("outInNo")String outInNo);

    /**
     * 查询 赠品数据 入库
     * @param outInNo 出入库单号
     * @return List<BatchWarehouseGoodsOutInItemDto a.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID,a.RELATED_ID,a.OUT_IN_NO,a.NUM,b.SKU
     */
    List<BatchWarehouseGoodsOutInItemDto> selectByOutInNoAndIsGiftAfterSale(@Param("outInNo")String outInNo);

    List<BatchWarehouseGoodsOutInItemDto> selectByRelatedIdsAndOperateType(@Param("list")List<Integer> relatedId,@Param("operateType")Integer operateType);



}