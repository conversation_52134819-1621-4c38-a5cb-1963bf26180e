<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wms.dao.WmsInputOrderMapper" >
  <resultMap id="BaseResultMap" type="com.wms.model.po.WmsInputOrder" >
    <!--          -->
    <id column="WMS_INPUT_ORDER_ID" property="wmsInputOrderId" jdbcType="INTEGER" />
    <result column="ORDER_TYPE" property="orderType" jdbcType="INTEGER" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="VERIFY_STATUS" property="verifyStatus" jdbcType="INTEGER" />
    <result column="ARRIVAL_STATUS" property="arrivalStatus" jdbcType="INTEGER" />
    <result column="APPLY_INTIME" property="applyIntime" jdbcType="TIMESTAMP" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="APPLYER" property="applyer" jdbcType="VARCHAR" />
    <result column="APPLYER_USERID" property="applyerUserid" jdbcType="INTEGER" />
    <result column="APPLYER_DEPARTMENT" property="applyerDepartment" jdbcType="VARCHAR" />
    <result column="APPLYER_DEPARTMENT_ID" property="applyerDepartmentId" jdbcType="INTEGER" />
    <result column="ADD_TIME" property="addTime" jdbcType="TIMESTAMP" />
    <result column="MODE_TIME" property="modeTime" jdbcType="TIMESTAMP" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
    <result column="IS_DELETE" property="isDelete" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--          -->
    WMS_INPUT_ORDER_ID, ORDER_TYPE, ORDER_NO, VERIFY_STATUS, ARRIVAL_STATUS, APPLY_INTIME, 
    REMARK, APPLYER, APPLYER_USERID, APPLYER_DEPARTMENT, APPLYER_DEPARTMENT_ID, ADD_TIME, 
    MODE_TIME, CREATOR, UPDATER, IS_DELETE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--          -->
    select 
    <include refid="Base_Column_List" />
    from T_WMS_INPUT_ORDER
    where WMS_INPUT_ORDER_ID = #{wmsInputOrderId,jdbcType=INTEGER}
  </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--          -->
    delete from T_WMS_INPUT_ORDER
    where WMS_INPUT_ORDER_ID = #{wmsInputOrderId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.wms.model.po.WmsInputOrder" >
    <!--          -->
    insert into T_WMS_INPUT_ORDER (WMS_INPUT_ORDER_ID, ORDER_TYPE, ORDER_NO, 
      VERIFY_STATUS, ARRIVAL_STATUS, APPLY_INTIME, 
      REMARK, APPLYER, APPLYER_USERID, 
      APPLYER_DEPARTMENT, APPLYER_DEPARTMENT_ID, 
      ADD_TIME, MODE_TIME, CREATOR, 
      UPDATER, IS_DELETE)
    values (#{wmsInputOrderId,jdbcType=INTEGER}, #{orderType,jdbcType=INTEGER}, #{orderNo,jdbcType=VARCHAR}, 
      #{verifyStatus,jdbcType=INTEGER}, #{arrivalStatus,jdbcType=INTEGER}, #{applyIntime,jdbcType=TIMESTAMP}, 
      #{remark,jdbcType=VARCHAR}, #{applyer,jdbcType=VARCHAR}, #{applyerUserid,jdbcType=INTEGER}, 
      #{applyerDepartment,jdbcType=VARCHAR}, #{applyerDepartmentId,jdbcType=INTEGER}, 
      #{addTime,jdbcType=TIMESTAMP}, #{modeTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{isDelete,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.wms.model.po.WmsInputOrder" useGeneratedKeys="true" keyProperty="wmsInputOrderId">
    <!--          -->
    insert into T_WMS_INPUT_ORDER
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="wmsInputOrderId != null" >
        WMS_INPUT_ORDER_ID,
      </if>
      <if test="orderType != null" >
        ORDER_TYPE,
      </if>
      <if test="orderNo != null" >
        ORDER_NO,
      </if>
      <if test="verifyStatus != null" >
        VERIFY_STATUS,
      </if>
      <if test="arrivalStatus != null" >
        ARRIVAL_STATUS,
      </if>
      <if test="applyIntime != null" >
        APPLY_INTIME,
      </if>
      <if test="remark != null" >
        REMARK,
      </if>
      <if test="applyer != null" >
        APPLYER,
      </if>
      <if test="applyerUserid != null" >
        APPLYER_USERID,
      </if>
      <if test="applyerDepartment != null" >
        APPLYER_DEPARTMENT,
      </if>
      <if test="applyerDepartmentId != null" >
        APPLYER_DEPARTMENT_ID,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="modeTime != null" >
        MODE_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
      <if test="isDelete != null" >
        IS_DELETE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="wmsInputOrderId != null" >
        #{wmsInputOrderId,jdbcType=INTEGER},
      </if>
      <if test="orderType != null" >
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="verifyStatus != null" >
        #{verifyStatus,jdbcType=INTEGER},
      </if>
      <if test="arrivalStatus != null" >
        #{arrivalStatus,jdbcType=INTEGER},
      </if>
      <if test="applyIntime != null" >
        #{applyIntime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="applyer != null" >
        #{applyer,jdbcType=VARCHAR},
      </if>
      <if test="applyerUserid != null" >
        #{applyerUserid,jdbcType=INTEGER},
      </if>
      <if test="applyerDepartment != null" >
        #{applyerDepartment,jdbcType=VARCHAR},
      </if>
      <if test="applyerDepartmentId != null" >
        #{applyerDepartmentId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modeTime != null" >
        #{modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null" >
        #{isDelete,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wms.model.po.WmsInputOrder" >
    <!--          -->
    update T_WMS_INPUT_ORDER
    <set >
      <if test="orderType != null" >
        ORDER_TYPE = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null" >
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="verifyStatus != null" >
        VERIFY_STATUS = #{verifyStatus,jdbcType=INTEGER},
      </if>
      <if test="arrivalStatus != null" >
        ARRIVAL_STATUS = #{arrivalStatus,jdbcType=INTEGER},
      </if>
      <if test="applyIntime != null" >
        APPLY_INTIME = #{applyIntime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="applyer != null" >
        APPLYER = #{applyer,jdbcType=VARCHAR},
      </if>
      <if test="applyerUserid != null" >
        APPLYER_USERID = #{applyerUserid,jdbcType=INTEGER},
      </if>
      <if test="applyerDepartment != null" >
        APPLYER_DEPARTMENT = #{applyerDepartment,jdbcType=VARCHAR},
      </if>
      <if test="applyerDepartmentId != null" >
        APPLYER_DEPARTMENT_ID = #{applyerDepartmentId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modeTime != null" >
        MODE_TIME = #{modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null" >
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
    </set>
    where WMS_INPUT_ORDER_ID = #{wmsInputOrderId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wms.model.po.WmsInputOrder" >
    <!--          -->
    update T_WMS_INPUT_ORDER
    set ORDER_TYPE = #{orderType,jdbcType=INTEGER},
      ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      VERIFY_STATUS = #{verifyStatus,jdbcType=INTEGER},
      ARRIVAL_STATUS = #{arrivalStatus,jdbcType=INTEGER},
      APPLY_INTIME = #{applyIntime,jdbcType=TIMESTAMP},
      REMARK = #{remark,jdbcType=VARCHAR},
      APPLYER = #{applyer,jdbcType=VARCHAR},
      APPLYER_USERID = #{applyerUserid,jdbcType=INTEGER},
      APPLYER_DEPARTMENT = #{applyerDepartment,jdbcType=VARCHAR},
      APPLYER_DEPARTMENT_ID = #{applyerDepartmentId,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MODE_TIME = #{modeTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=INTEGER}
    where WMS_INPUT_ORDER_ID = #{wmsInputOrderId,jdbcType=INTEGER}
  </update>
  <select id="querySurplusInlistPage" parameterType="Map" resultType="com.wms.model.po.WmsSurplusInOrder">
    SELECT
    T.*,COUNT(B.WMS_INPUT_ORDER_GOODS_ID) AS countSku,
    B.SKU_NO,
    SKU.SHOW_NAME AS goodsName,
    SKU.MODEL,
    BR.BRAND_NAME,
    UN.UNIT_NAME,
    B.INPUT_NUM,
    (CASE T.ARRIVAL_STATUS
    WHEN 0 THEN
    '未收货'
    WHEN 1 THEN
    '部分收货'
    ELSE
    '全部收货'
    END
    ) AS 'arrivalStatusStr',
    (CASE T.VERIFY_STATUS
    WHEN 0 THEN
    '待审核'
    WHEN 1 THEN
    '审核中'
    WHEN 2 THEN
    '审核通过'
    WHEN 3 THEN
    '审核不通过'
    ELSE
    '已关闭'
    END
    ) AS 'verifyStatusStr'
    FROM
    T_WMS_INPUT_ORDER T
    LEFT JOIN T_WMS_INPUT_ORDER_GOODS B ON T.WMS_INPUT_ORDER_ID=B.WMS_INPUT_ORDER_ID
    LEFT JOIN V_CORE_SKU SKU ON SKU.SKU_ID = B.GOODS_ID
    LEFT JOIN V_CORE_SPU SPU ON SPU.SPU_ID = SKU.SPU_ID
    LEFT JOIN T_BRAND BR ON BR.BRAND_ID=SPU.BRAND_ID
    LEFT JOIN T_UNIT UN ON UN.UNIT_ID=SKU.UNIT_ID
    WHERE
    T.ORDER_TYPE=1
    <if test="wmsSurplusInQueryDto.orderNo != null and wmsSurplusInQueryDto.orderNo != ''">

      AND T.ORDER_NO LIKE
      CONCAT('%',#{wmsSurplusInQueryDto.orderNo,jdbcType=VARCHAR},'%' )

    </if>
    <if test="wmsSurplusInQueryDto.goodsName != null and wmsSurplusInQueryDto.goodsName != ''">

      AND SKU.SHOW_NAME LIKE
      CONCAT('%',#{wmsSurplusInQueryDto.goodsName,jdbcType=VARCHAR},'%' )

    </if>
    <if test="wmsSurplusInQueryDto.skuNo != null and wmsSurplusInQueryDto.skuNo != ''">

      AND B.SKU_NO LIKE
      CONCAT('%',#{wmsSurplusInQueryDto.skuNo,jdbcType=VARCHAR},'%' )

    </if>
    <if test="wmsSurplusInQueryDto.brandName != null and wmsSurplusInQueryDto.brandName != ''">

      AND BR.BRAND_NAME LIKE
      CONCAT('%',#{wmsSurplusInQueryDto.brandName,jdbcType=VARCHAR},'%' )

    </if>
    <if test="wmsSurplusInQueryDto.applyIntimeStrat != null">

      AND T.APPLY_INTIME <![CDATA[>=]]> date(#{wmsSurplusInQueryDto.applyIntimeStrat,jdbcType=TIMESTAMP})

    </if>
    <if test="wmsSurplusInQueryDto.applyIntimeEnd != null ">

      AND T.APPLY_INTIME <![CDATA[<=]]> date(#{wmsSurplusInQueryDto.applyIntimeEnd,jdbcType=TIMESTAMP})

    </if>
    <if test="wmsSurplusInQueryDto.model != null and wmsSurplusInQueryDto.model != ''">

      AND SKU.MODEL LIKE
      CONCAT('%',#{wmsSurplusInQueryDto.model,jdbcType=VARCHAR},'%' )

    </if>
    <if test="wmsSurplusInQueryDto.arrivalStatus != null ">

      AND T.ARRIVAL_STATUS =#{wmsSurplusInQueryDto.arrivalStatus,jdbcType=INTEGER}

    </if>
    <if test="wmsSurplusInQueryDto.verifyStatus != null ">

      AND T.VERIFY_STATUS = #{wmsSurplusInQueryDto.verifyStatus,jdbcType=INTEGER}

    </if>
    GROUP BY T.WMS_INPUT_ORDER_ID
    ORDER BY T.WMS_INPUT_ORDER_ID
  </select>
    <select id="getWmsInputOrderByNo" resultType="com.wms.model.po.WmsInputOrder">
      select
      <include refid="Base_Column_List" />
      from T_WMS_INPUT_ORDER
      where  ORDER_NO = #{orderNo,jdbcType=VARCHAR}
    </select>
    <select id="getValidAndUnInInpuorderBySkuNos" resultType="com.wms.model.po.WmsInputOrder">
      select DISTINCT  ORDER_NO
      FROM T_WMS_INPUT_ORDER WIO
      LEFT JOIN T_WMS_INPUT_ORDER_GOODS WIOG ON WIOG.WMS_INPUT_ORDER_ID= WIO.WMS_INPUT_ORDER_ID AND WIOG.IS_DELTET =0
      WHERE WIO.VERIFY_STATUS IN (0,1,2)
      AND WIO.IS_DELETE = 0
      AND WIO.ARRIVAL_STATUS IN (0,1)
      AND WIOG.SKU_NO = #{skuNo,jdbcType=VARCHAR}

    </select>

  <select id="getWmsOutputOrderByOrderNoAndType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_WMS_INPUT_ORDER
    where  ORDER_NO = #{relateNo,jdbcType=VARCHAR}
    and ORDER_TYPE = #{orderType,jdbcType=INTEGER}
    </select>
</mapper>