package com.vedeng.erp.buyorder.enums;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.buyorder.dto.BuyOrderKingDeeDto;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
public enum BuyOrderPaymentTypeEnum {

    /**
     * 先款后货，预付100%
     */
    PAY_IN_ADVANCE_100(419,"先款后货，预付100%","，预付金额%s元。"),

    /**
     * 先货后款，预付80%
     */
    GOODS_IN_ADVANCE_80(420,"先货后款，预付80%","，预付金额%s元，账期支付%s元。"),

    /**
     * 先货后款，预付50%
     */
    GOODS_IN_ADVANCE_50(421, "先货后款，预付50%","，预付金额%s元，账期支付%s元。"),

    /**
     * 先货后款，预付30%
     */
    GOODS_IN_ADVANCE_30(422, "先货后款，预付30%","，预付金额%s元，账期支付%s元。"),

    /**
     * 先货后款，预付0%
     */
    GOODS_IN_ADVANCE_0(423, "先货后款，预付0%","，预付金额%s元，账期支付%s元。"),

    /**
     * 待收款
     */
    CUSTOMIZE(424, "自定义","，预付金额%s元，账期支付%s元，尾款%s元，尾款期限%s月。"),
    ;

    /**
     * 付款类型描述
     * @param buyOrderKingDeeDto
     * @return
     */
    public static String getSupplement(BuyOrderKingDeeDto buyOrderKingDeeDto) {
        try{
            BuyOrderPaymentTypeEnum byType = getByType(buyOrderKingDeeDto.getPaymentType());
            if (Objects.isNull(byType)){
                log.info("参数：{}", JSON.toJSON(buyOrderKingDeeDto));
                return "";
            }
            if (PAY_IN_ADVANCE_100.equals(byType)){
                return byType.getDesc() + String.format(byType.getDescSupplement(), buyOrderKingDeeDto.getPrepaidAmount());
            }
            if (CUSTOMIZE.equals(byType)){
                return byType.getDesc() + String.format(byType.getDescSupplement(), buyOrderKingDeeDto.getPrepaidAmount(), buyOrderKingDeeDto.getAccountPeriodAmount(), buyOrderKingDeeDto.getRetainageAmount(), buyOrderKingDeeDto.getRetainageAmountMonth());
            }
            return byType.getDesc() +String.format(byType.getDescSupplement(), buyOrderKingDeeDto.getPrepaidAmount(), buyOrderKingDeeDto.getAccountPeriodAmount());
        }catch (Exception e){
            log.info("参数：{}", JSON.toJSON(buyOrderKingDeeDto));
            log.error("金蝶查询接口转换付款类型异常：{}",e);
            return "";
        }
    }


    public static BuyOrderPaymentTypeEnum getByType(Integer type){
        for (BuyOrderPaymentTypeEnum buyOrderPaymentTypeEnum : BuyOrderPaymentTypeEnum.values()) {
            if (buyOrderPaymentTypeEnum.getType().equals(type)) {
                return buyOrderPaymentTypeEnum;
            }
        }
        return null;
    }


    private Integer type;

    private String desc;

    private String descSupplement;

    BuyOrderPaymentTypeEnum(Integer type, String desc, String descSupplement) {
        this.type = type;
        this.desc = desc;
        this.descSupplement = descSupplement;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getDescSupplement() {
        return descSupplement;
    }

    public void setDescSupplement(String descSupplement) {
        this.descSupplement = descSupplement;
    }
}

