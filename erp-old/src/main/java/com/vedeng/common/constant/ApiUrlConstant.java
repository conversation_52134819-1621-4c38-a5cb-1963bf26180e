package com.vedeng.common.constant;

/**
 * @Description: api接口url
 * @Author: Franlin.wu
 * @Version: V1.0.0
 * @Since: 1.0
 * @Date: 2019/6/19
 */
public interface ApiUrlConstant {

    /**
     * 发送微信模板消息接口
     */
    String API_WECHAT_SEND_TEMPLATE_MSG = "/weChat/sendTemplateMsg";

    /**
     * 推送客户归属销售至耗材商城
     */
    String API_TRADER_SALER_PUT_TO_HC = "/accountInfo/updateTraderSaler";

    /**
     * 同步支付宝账单
     */
    String API_ALIPAY_SYNC_BILL_DATA = "alipay/billQuery";

    /**
     * 同步微信账单
     */
    String API_WECHAT_SYNC_BILL_DATA = "wxpay/billQuery";

    /**
     * 进销存报告
     */
    String API_INVENTORY_REPORT = "/stock/wms/inventoryReport";
    /**
     * 同步SKU占用
     */
    String SYNC_SKU_STOCK_INFO = "/stock/getLastestChangedStockInfoSimple";
    /**
     * 出库单据核对
     */
    String OUT_ORDER_CHECK_REPORT = "/stock/order/outOrderCheck";

}
