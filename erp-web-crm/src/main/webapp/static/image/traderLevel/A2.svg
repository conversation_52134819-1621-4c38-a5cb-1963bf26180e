<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="图层_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 width="16px" height="16px" viewBox="0 0 16 16" enable-background="new 0 0 16 16" xml:space="preserve">
<linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="-15.8003" y1="825.7988" x2="-1.558" y2="840.0411" gradientTransform="matrix(1 0 0 1 16.6797 -824.9199)">
	<stop  offset="0.0056" style="stop-color:#DF71F3"/>
	<stop  offset="1" style="stop-color:#9626CA"/>
</linearGradient>
<path fill="url(#SVGID_1_)" d="M16,13c0,1.657-1.343,3-3,3H3c-1.657,0-3-1.343-3-3V3c0-1.657,1.343-3,3-3h10c1.657,0,3,1.343,3,3V13
	z"/>
<g>
	<linearGradient id="SVGID_2_" gradientUnits="userSpaceOnUse" x1="2.6504" y1="6.6123" x2="7.9944" y2="11.9563">
		<stop  offset="0" style="stop-color:#FFFFFF"/>
		<stop  offset="1" style="stop-color:#F6CFFF"/>
	</linearGradient>
	<path fill="url(#SVGID_2_)" d="M6.577,9.945h-2.52l-0.547,1.66H2.285l2.48-7.109h1.128l2.451,7.109H7.119L6.577,9.945z
		 M4.384,8.955h1.87L5.341,6.152H5.312L4.384,8.955z"/>
	<linearGradient id="SVGID_3_" gradientUnits="userSpaceOnUse" x1="8.7109" y1="5.9556" x2="14.0384" y2="11.283">
		<stop  offset="0" style="stop-color:#FFFFFF"/>
		<stop  offset="1" style="stop-color:#F6CFFF"/>
	</linearGradient>
	<path fill="url(#SVGID_3_)" d="M13.715,11.605H9.131v-0.814l2.221-2.569c0.361-0.427,0.611-0.778,0.752-1.055
		s0.211-0.542,0.211-0.796c0-0.303-0.086-0.549-0.254-0.74c-0.17-0.189-0.412-0.285-0.729-0.285c-0.367,0-0.648,0.119-0.844,0.356
		s-0.293,0.537-0.293,0.898H9.047l-0.01-0.029C9.021,5.963,9.223,5.448,9.641,5.026c0.418-0.421,0.982-0.632,1.691-0.632
		c0.68,0,1.213,0.183,1.594,0.547C13.309,5.305,13.5,5.784,13.5,6.376c0,0.4-0.105,0.776-0.32,1.128
		c-0.213,0.352-0.563,0.822-1.051,1.412l-1.5,1.719l0.01,0.023h3.076V11.605z"/>
</g>
</svg>
