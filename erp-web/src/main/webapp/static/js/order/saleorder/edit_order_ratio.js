$(function (){
    $('input[name="sendInvoice"]').change(function() {
        var sendInvoiceValue = $('input[name="sendInvoice"]:checked').val();
        if (0==sendInvoiceValue){
            $('#invoiceContactLi').css("display","none")
            $('#invoiceContactAddressLi').css("display","none")
        }else {
            $('#invoiceContactLi').css("display","block")
            $('#invoiceContactAddressLi').css("display","block")
        }
    });
    var sendInvoiceValue = $('input[name="sendInvoice"]:checked').val();
    if (0==sendInvoiceValue){
        $('#invoiceContactLi').css("display","none")
        $('#invoiceContactAddressLi').css("display","none")
    }else {
        $('#invoiceContactLi').css("display","block")
        $('#invoiceContactAddressLi').css("display","block")
    }

    var type = $('select[name="invoiceType"]').val();
    if (972 == type){
        $('#invoiceProperty option[value="2"]').hide()
    }
    $('select[name="invoiceType"]').change(function (){
        let newType = $('select[name="invoiceType"]').val();
        if (972 == newType){
            $('#invoiceProperty option[value="2"]').hide()
        }else {
            $('#invoiceProperty option[value="2"]').show()
        }
        if (972 == newType && $('#invoiceProperty').val() == 2){
            $('#invoiceProperty').val('1')
        }
    })
})
function saveRatioEdit(){
    layer.confirm("您是否确认修改？", {
        btn : [ '确定', '取消' ]
    }, function() {
        checkLogin();
        //校验开票申请状态
        var validStatus;
        var invoiceApplyId = $('#invoiceApplyId').val();
        $.ajax({
            async : false,
            url:'/invoiceApply/api/checkInvoiceApplyStStatus.do?invoiceApplyId='+invoiceApplyId,
            type:'GET',
            dataType:"json",
            success: function (res){
                if (1 == res.data){
                    validStatus = 1;
                }
            }
        })
        if (1 == validStatus){
            layer.alert("数据状态为已开票，不可修改")
            return false;
        }
        console.log($("#trader_contact_3").val())
        console.log($("#address_3").val())
        var sendInvoiceValue = $('input[name="sendInvoice"]:checked').val();
        console.log(sendInvoiceValue)
        if (1==sendInvoiceValue && !$("#trader_contact_3").val()){
            layer.alert("请选择收票联系人")
            return false;
        }
        if (1==sendInvoiceValue && !$("#address_3").val()){
            layer.alert("请选择收票地址")
            return false;
        }
        $.ajax({
            async : true,
            url : page_url + '/order/saleorder/saveOrderRatioEdit.do',
            data : $("#editOrderRatioEdit").serialize(),
            type : "POST",
            dataType : "json",
            success : function(data) {
                refreshNowPageList(data);
            },error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    });
}
