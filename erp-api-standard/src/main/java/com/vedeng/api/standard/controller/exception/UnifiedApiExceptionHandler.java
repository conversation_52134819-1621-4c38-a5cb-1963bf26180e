package com.vedeng.api.standard.controller.exception;

import com.vedeng.api.standard.core.ApiResponse;
import com.vedeng.api.standard.core.BaseResponseCode;
import com.vedeng.api.standard.core.exception.ApiStandardException;
import com.vedeng.api.standard.core.exception.AuthenticationException;
import com.vedeng.api.standard.core.exception.BusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.servlet.http.HttpServletRequest;

/**
 * 统一API异常处理器
 * 处理所有API接口的异常情况
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-18
 */
@ControllerAdvice(basePackages = "com.vedeng.api.standard.controller")
@ResponseBody
public class UnifiedApiExceptionHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(UnifiedApiExceptionHandler.class);
    
    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.OK)
    public ApiResponse<Object> handleBusinessException(BusinessException e, HttpServletRequest request) {
        String requestId = getRequestId(request);

        logger.warn("业务异常: requestId={}, code={}, message={}",
            requestId, e.getCode(), e.getMessage());

        ApiResponse<Object> response = ApiResponse.error(e.getCode(), e.getMessage());
        response.setRequestId(requestId);

        return response;
    }
    
    /**
     * 处理API标准异常
     */
    @ExceptionHandler(ApiStandardException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResponse<Object> handleApiStandardException(ApiStandardException e, HttpServletRequest request) {
        String requestId = getRequestId(request);

        logger.error("API标准异常: requestId={}, code={}, message={}",
            requestId, e.getCode(), e.getMessage(), e);

        ApiResponse<Object> response = ApiResponse.error(e.getCode(), e.getMessage());
        response.setRequestId(requestId);

        return response;
    }

    /**
     * 处理认证异常
     */
    @ExceptionHandler(AuthenticationException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public ApiResponse<Object> handleAuthenticationException(AuthenticationException e, HttpServletRequest request) {
        String requestId = getRequestId(request);

        logger.warn("认证异常: requestId={}, code={}, message={}",
            requestId, e.getCode(), e.getMessage());

        ApiResponse<Object> response = ApiResponse.error(e.getCode(), e.getMessage());
        response.setRequestId(requestId);

        return response;
    }
    
    /**
     * 处理参数异常
     */
    @ExceptionHandler({IllegalArgumentException.class, IllegalStateException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Object> handleParameterException(Exception e, HttpServletRequest request) {
        String requestId = getRequestId(request);

        logger.warn("参数异常: requestId={}, message={}", requestId, e.getMessage());

        ApiResponse<Object> response = ApiResponse.error(BaseResponseCode.PARAMETER_ERROR.getCode(), e.getMessage());
        response.setRequestId(requestId);

        return response;
    }
    
    /**
     * 处理不支持的操作异常
     */
    @ExceptionHandler(UnsupportedOperationException.class)
    @ResponseStatus(HttpStatus.NOT_IMPLEMENTED)
    public ApiResponse<Object> handleUnsupportedOperationException(UnsupportedOperationException e, HttpServletRequest request) {
        String requestId = getRequestId(request);

        logger.warn("不支持的操作: requestId={}, message={}", requestId, e.getMessage());

        ApiResponse<Object> response = ApiResponse.error(BaseResponseCode.API_NOT_FOUND.getCode(), e.getMessage());
        response.setRequestId(requestId);

        return response;
    }
    
    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResponse<Object> handleRuntimeException(RuntimeException e, HttpServletRequest request) {
        String requestId = getRequestId(request);

        logger.error("运行时异常: requestId={}", requestId, e);

        ApiResponse<Object> response = ApiResponse.error(BaseResponseCode.SYSTEM_BUSY);
        response.setRequestId(requestId);

        return response;
    }
    
    /**
     * 处理所有其他异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResponse<Object> handleException(Exception e, HttpServletRequest request) {
        String requestId = getRequestId(request);

        logger.error("系统异常: requestId={}", requestId, e);

        ApiResponse<Object> response = ApiResponse.error(BaseResponseCode.SYSTEM_BUSY);
        response.setRequestId(requestId);

        return response;
    }
    
    /**
     * 从请求中获取请求ID
     */
    private String getRequestId(HttpServletRequest request) {
        // 尝试从请求属性中获取
        Object requestIdObj = request.getAttribute("requestId");
        if (requestIdObj != null) {
            return requestIdObj.toString();
        }
        
        // 尝试从请求头中获取
        String requestId = request.getHeader("X-Request-ID");
        if (requestId != null && !requestId.trim().isEmpty()) {
            return requestId;
        }
        
        // 生成新的请求ID
        return java.util.UUID.randomUUID().toString();
    }
}
