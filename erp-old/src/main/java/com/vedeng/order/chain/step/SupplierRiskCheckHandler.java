package com.vedeng.order.chain.step;

import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.util.StringUtil;
import com.vedeng.order.chain.AbstractRiskHandlerStep;
import com.vedeng.order.chain.RiskEnum;
import com.vedeng.order.chain.RiskHandlerKeyConstant;
import com.vedeng.order.chain.model.RiskModel;
import com.vedeng.trader.dao.TraderCertificateMapper;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.dao.TraderSupplierMapper;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.TraderSupplier;
import com.vedeng.trader.model.vo.TraderCertificateVo;
import com.wms.service.chain.HandlerStepContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @ClassName SupplierRiskCheckHandler.java
 * @Description TODO 供应商风控信息校验
 * @createTime 2020年12月08日 15:51:00
 */
@Service
@Slf4j
public class SupplierRiskCheckHandler extends AbstractRiskHandlerStep {


    @Override
    public RiskModel doDealWith(HandlerStepContext context) {

        RiskModel riskModel = context.get(RiskHandlerKeyConstant.RISK_MODEL);
        Integer traderId = context.get(RiskHandlerKeyConstant.TRADER_ID);
        HashMap<String,Boolean> ignoreFiledMap = context.get(RiskHandlerKeyConstant.IGNORE);

        //供应商名称、供应商类型、供应商所在地区（营业执照为准）、营业执照、是否多证合一、
        // 销售授权书、随货同行单模板、质量保证协议、售后服务承诺书、仓库地址

        Trader trader = getTrader(traderId);

        TraderSupplier traderSupplier = getTraderSupplier(traderId);

        TraderCertificateVo traderCertificate = new TraderCertificateVo();
        traderCertificate.setTraderId(traderId);
        //营业执照
        traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_25);
        List<TraderCertificateVo> tcList2 = getTraderCertificateVos(traderCertificate);
        // 二类医疗资质 多证合一辅助证明
        traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_28);
        List<TraderCertificateVo> tList = getTraderCertificateVos(traderCertificate);
        // 销售授权书 与 销售人信息
        traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_1100);
        List<TraderCertificateVo> saleList = getTraderCertificateVos(traderCertificate);
        //随货同行模板 -VDERP-17676 加上
        traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_896);
        List<TraderCertificateVo> goodWithTemList = getTraderCertificateVos(traderCertificate);
//        //质量保证协议 -VDERP-17676 去掉
//        traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_897);
//        List<TraderCertificateVo> qualityAssuranceList = getTraderCertificateVos(traderCertificate);
//        //售后服务承诺书 -VDERP-17676 去掉
//        traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_898);
//        List<TraderCertificateVo> afterSalesBookList = getTraderCertificateVos(traderCertificate);

        boolean flag = true;
        if(trader == null || traderSupplier == null){
            flag = false;
        }else if(StringUtil.isBlank(trader.getTraderName())){
            //供应商名称
            flag = false;
        }else if(traderSupplier.getTraderType() == null || traderSupplier.getTraderType().equals(0)){
            //供应商类型
            flag = false;
        }else if(StringUtil.isBlank(trader.getAreaIds())){
            //供应商所在地区（营业执照为准）
            flag = false;
        }else if(CollectionUtils.isEmpty(tcList2)){
            //营业执照
            flag = false;
        }else if(trader.getMedicalQualification().equals(1) && CollectionUtils.isEmpty(tList)){
            //是否多证合一
            flag = false;
        }else if(CollectionUtils.isEmpty(saleList)){
            //销售授权书
            flag = false;
        }
        else if(CollectionUtils.isEmpty(goodWithTemList)){
            //随货同行模板
            flag = false;
//        }
//        else if(CollectionUtils.isEmpty(qualityAssuranceList) && ignoreFiledMap !=null && ignoreFiledMap.get("qualityAssuranceList")){
//            //质量保证协议
//            flag = false;
//        }else if(CollectionUtils.isEmpty(afterSalesBookList) && ignoreFiledMap !=null && ignoreFiledMap.get("afterSalesBookList")){
//            //售后服务承诺书
//            flag = false;
        }else if(StringUtil.isBlank(traderSupplier.getWarehouseAddress())){
            //仓库地址
            flag = false;
        }
        log.info("触发风控检查：{},{}",traderId,flag);
        if(!flag){
            Set<RiskEnum> riskEnumList = riskModel.getRiskEnumList();
            riskEnumList.add(RiskEnum.SUPPLIER_RISK);
            riskModel.setRiskEnumList(riskEnumList);
            riskModel.setIsRisk(false);
            riskModel.setSupplierRiskMessage("供应商信息不完善");
            riskModel.setIsOneChek(false);

        }
        return riskModel;
    }

}
