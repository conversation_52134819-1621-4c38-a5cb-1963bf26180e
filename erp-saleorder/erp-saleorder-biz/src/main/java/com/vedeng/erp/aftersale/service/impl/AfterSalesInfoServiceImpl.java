package com.vedeng.erp.aftersale.service.impl;

import com.vedeng.aftersales.dao.AfterSalesDetailMapper;
import com.vedeng.aftersales.model.vo.AfterSalesDetailVo;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.erp.aftersale.service.AfterSalesInfoService;
import com.vedeng.trader.dao.TraderContactMapper;
import com.vedeng.trader.dao.TraderFinanceMapper;
import com.vedeng.trader.model.TraderContact;
import com.vedeng.trader.model.vo.TraderFinanceVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> simgo.wang
 * @date : 2021/10/14 10:34
 * @desc :
 */
@Service
public class AfterSalesInfoServiceImpl implements AfterSalesInfoService {

    @Resource
    private TraderContactMapper traderContactMapper;

    @Resource
    private TraderFinanceMapper traderFinanceMapper;

    @Resource
    private AfterSalesDetailMapper afterSalesDetailMapper;

    @Override
    public List<TraderContact> getTraderContact(TraderContact traderContact) {
        return traderContactMapper.getTraderContact(traderContact);
    }

    @Override
    public TraderFinanceVo getTraderCustomerFinance(TraderFinanceVo traderFinance) {
        return traderFinanceMapper.getTraderCustomerFinance(traderFinance);
    }

    @Override
    public int updateByPrimaryKeyInfo(AfterSalesVo afterSalesVo) {
        return afterSalesDetailMapper.updateByPrimaryKeyInfo(afterSalesVo);
    }

    @Override
    public int updateByPrimaryKeyDetailInfo(AfterSalesDetailVo afterSalesDetailVo) {
        return afterSalesDetailMapper.updateByPrimaryKeyDetailInfo(afterSalesDetailVo);
    }

    @Override
    public TraderContact selectTraderContactInfoByPrimaryKey(Integer traderContactId) {
        return traderContactMapper.selectByPrimaryKey(traderContactId);
    }
}
