<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="出库单详情" scope="application" />
<%@ include file="../../common/common.jsp"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<div class="main-container">
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">基本信息</div>
        </div>
        <table class="table">
            <tbody>
            <tr class="J-skuInfo-tr">
                <td class="table-smaller">出库单号</td>
                <td>${warehouseGoodsOut.outInNo}</td>
                <td class="table-smaller">WMS出库单号</td>
                <td>${warehouseGoodsOut.wmsNo}</td>
            </tr>
            <tr>
                <td >关联单号</td>
                <td CLASS="table-smaller">
                    <c:if test="${purchaseOrderIsGift}">
                        <span style="color: red">【赠品订单】</span>
                    </c:if>
                    <c:choose>
                        <c:when test="${warehouseGoodsOut.updateRemark=='21-22其他'}">
                            ${warehouseGoodsOut.relateNo}
                        </c:when>
                        <c:otherwise><a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewOutrelateNo${warehouseGoodsOut.relateNo}","link":"./warehouse/warehousesout/relatedDetail.do?relatedNo=${warehouseGoodsOut.relateNo}&outInType=${warehouseGoodsOut.outInType}","title":"关联信息"}'>${warehouseGoodsOut.relateNo}</a></c:otherwise>
                    </c:choose>

                </td>
                <td>出库单类型</td>
                <td CLASS="table-smaller">
                    <c:choose>
                        <c:when test="${warehouseGoodsOut.outInType==2}">销售出库</c:when>
                        <c:when test="${warehouseGoodsOut.outInType==4}">销售换货出库</c:when>
                        <c:when test="${warehouseGoodsOut.outInType==6}">采购售后退货出库</c:when>
                        <c:when test="${warehouseGoodsOut.outInType==7}">采购售后换货出库</c:when>
                        <c:when test="${warehouseGoodsOut.outInType==10}">外借出库</c:when>
                        <c:when test="${warehouseGoodsOut.outInType==13}">报废出库</c:when>
                        <c:when test="${warehouseGoodsOut.outInType==14}">领用出库</c:when>
                        <c:when test="${warehouseGoodsOut.outInType==16}">盘亏出库</c:when>
                        <c:when test="${warehouseGoodsOut.outInType==18}">样品出库</c:when>
                        <c:when test="${warehouseGoodsOut.outInType==19}">库存转换出库</c:when>
                        <c:otherwise></c:otherwise>
                    </c:choose>
                    </td>
            </tr>
            <tr>
                <td>归属部门</td>
                <td class="table-smaller">${warehouseGoodsOut.belongUserOrgName}</td>
                <td>单据归属</td>
                <td class="table-smaller">${warehouseGoodsOut.belongUserName}</td>
            </tr>
            <tr>
                <td>订单总额</td>
                <td class="table-smaller">${warehouseGoodsOut.orderTotalAmount}</td>
                <td>商品总数</td>
                <td class="table-smaller">${warehouseGoodsOut.totalGoodsNum}</td>
            </tr>
            </tbody>
        </table>
    </div>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">收货客户信息</div>
        </div>
        <table class="table">
            <tbody>
            <tr>
                <td class="table-smaller">收货客户</td>
                <td>${warehouseGoodsOut.takeTraderName}</td>
                <td class="table-smaller">收货联系人</td>
                <td>${warehouseGoodsOut.takeTraderContactName}</td>
            </tr>
            <tr>
                <td>联系电话</td>
                <td CLASS="table-smaller">${warehouseGoodsOut.takeTraderContactTelephone}</td>
                <td>联系手机号</td>
                <td CLASS="table-smaller">${warehouseGoodsOut.takeTraderContactMobile}</td>
            </tr>
            <tr>
                <td>收货地区</td>
                <td class="table-smaller">${warehouseGoodsOut.takeArea}</td>
                <td>收货地址</td>
                <td class="table-smaller">${warehouseGoodsOut.takeAddress}</td>
            </tr>
            <tr>
                <td>指定物流公司</td>
                <td class="table-smaller">
                    <c:choose>
                        <c:when test="${warehouseGoodsOut.isVirture eq 1}">
                        <%--虚拟出库单默认展示为空--%>
                        </c:when>
                        <c:otherwise>
                            <c:forEach var="list" items="${logisticsList}">
                                <c:if test="${warehouseGoodsOut.logisticsCompanyId == list.logisticsId}">${list.name}</c:if>
                            </c:forEach>
                        </c:otherwise>
                    </c:choose>
                </td>
                <td>运费说明</td>
                <td class="table-smaller">
                    <c:choose>
                        <c:when test="${warehouseGoodsOut.isVirture eq 1}">
                            <%--虚拟出库单默认展示为空--%>
                        </c:when>
                        <c:otherwise>
                            <c:forEach var="list" items="${freightDescriptions}">
                                <c:if test="${warehouseGoodsOut.freightDescriptionId == list.sysOptionDefinitionId}">${list.title}</c:if>
                            </c:forEach>
                        </c:otherwise>
                    </c:choose>
                </td>
            </tr>
            <tr>
                <td>物流备注</td>
                <td class="table-smaller" colspan="3">
                    <c:choose>
                        <c:when test="${warehouseGoodsOut.isVirture eq 1}">
                            <%--虚拟出库单默认展示为空--%>
                        </c:when>
                        <c:otherwise>
                            ${warehouseGoodsOut.logisticsComments}
                        </c:otherwise>
                    </c:choose>
                </td>
            </tr>
            </tbody>
        </table>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">出库记录</div>
        </div>
        <table class="table  table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th class="wid5">序号</th>
                <th class="wid10">订货号</th>
                <th>产品名称</th>
                <th>品牌</th>
                <th>型号/规格</th>
                <th>实际出库数量</th>
                <th class="wid4">单位</th>
                <th>贝登批次码</th>
                <th>生产日期</th>
                <th>有效期至</th>
                <th>出库时间</th>
                <th>批次号</th>
                <th>SN码</th>
                <th>灭菌编号</th>
                <th class="wid12">注册证号</th>
            </tr>
            </thead>
            <tbody>
            <c:forEach var="wlist" items="${warehouseGoodsOut.warehouseGoodsOutLogList}" varStatus="num1">
                <tr>
                    <td>${num1.count}</td>
                    <td>${newSkuInfosMap[wlist.skuId].SKU_NO}</td>
                    <td class="text-left">
                        <c:if test="${warehouseGoodsOut.outInType eq 2 && wlist.giftGoods eq 1}">
                            <img src="<%= basePath %>static/images/gift_icon.svg" style="width: 15px" />
                        </c:if>
                        <a class="addtitle" href="javascript:void(0);"
                           tabTitle='{"num":"viewgoods${wlist.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${wlist.goodsId}","title":"产品信息"}'>${newSkuInfosMap[wlist.skuId].SHOW_NAME}</a>
                    </td>
                    <td>${newSkuInfosMap[wlist.skuId].BRAND_NAME}</td>
                    <td>
                        <c:choose>
                        <c:when test="${newSkuInfosMap[wlist.skuId].SPU_TYPE=='317' || newSkuInfosMap[wlist.skuId].SPU_TYPE=='318'}">${newSkuInfosMap[wlist.skuId].SPEC}</c:when>
                        <c:when test="${newSkuInfosMap[wlist.skuId].SPU_TYPE=='316' || newSkuInfosMap[wlist.skuId].SPU_TYPE=='1008'}">${newSkuInfosMap[wlist.skuId].MODEL}</c:when>
                        <c:otherwise>${newSkuInfosMap[wlist.skuId].MODEL}/${newSkuInfosMap[wlist.skuId].SPEC}</c:otherwise>
                        </c:choose>
                    </td>
                    <td>${wlist.num}</td>
                    <td>${newSkuInfosMap[wlist.skuId].UNIT_NAME}</td>
                    <td>${wlist.vedengBatchNumber}</td>
                    <td>
                        <c:choose>
                            <c:when test="${outInSource eq 'ERP'}">见内部包装说明</c:when>
                            <c:otherwise>${wlist.productDate}</c:otherwise>
                        </c:choose>
                    </td>
                    <td>
                        <c:choose>
                            <c:when test="${outInSource eq 'ERP'}">见内部包装说明</c:when>
                            <c:otherwise>${wlist.expirationDate}</c:otherwise>
                        </c:choose>
                    </td>
                    <td>${wlist.addTimeStr}</td>
                    <td>${wlist.batchNumber}</td>
                    <td>${wlist.barcodeFactory}</td>
                    <td>${wlist.sterilizationBatchNumber}</td>
                    <td>
                        <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewfirstgoods${newSkuInfosMap[wlist.skuId].FIRST_ENGAGE_ID}","link":"./firstengage/baseinfo/getFirstSearchDetail.do?firstEngageId=${newSkuInfosMap[wlist.skuId].FIRST_ENGAGE_ID}","title":"首营信息"}'>${newSkuInfosMap[wlist.skuId].REGISTRATION_NUMBER}</a>
                    </td>
                </tr>
            </c:forEach>
            <!-- 查询无结果弹出 -->
            <c:if test="${empty warehouseGoodsOut.warehouseGoodsOutLogList}">
                <tr>
                    <td colspan="15">暂无出库记录。</td>
                </tr>
            </c:if>
            </tbody>
        </table>

    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                出库附件
            </div>
            <div class="title-click nobor  pop-new-data" layerparams='{"width":"520px","height":"300px","title":"出库附件","link":"./warehouseOutAttachInit.do?warehouseGoodsOutInId=${warehouseGoodsOut.warehouseGoodsOutInId}"}' >
                新增附件
            </div>
        </div>
        <table class="table  table-style6">
            <thead>
            <tr>
                <th class="wid6">序号</th>
                <th>附件名称</th>
                <th>操作人</th>
                <th>上传时间</th>
                <th>操作</th>
            </tr>
            </thead>
            <tbody>
            <c:if test="${not empty AttachmentList }">
                <c:forEach var="att" items="${AttachmentList}" varStatus="status">
                    <tr>
                        <td>${status.count}</td>
                        <td><a href="http://${att.domain}${att.uri}" target="_blank">${att.name}</a></td>
                        <td>${att.creatorName}</td>
                        <td><date:date value ="${att.addTimeStr}"/></td>
                        <td>
                            <span class="forbid clcforbid" onclick="delWarehouseAttachment(${att.attachmentId})">删除</span>
                        </td>
                    </tr>
                </c:forEach>
            </c:if>
            <c:if test="${empty AttachmentList }">
                <td colspan="5">暂无出库附件记录</td>
            </c:if>
            </tbody>
        </table>
    </div>
    <c:if test="${showWarehouseOutRecheckReport}">
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">出库复核报告</div>
        </div>
        <table class="table  table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th class="wid6">序号</th>
                <th>附件名称</th>
                <th>附件来源</th>
                <th>上传时间</th>
            </tr>
            </thead>
            <tbody>
            <c:if test="${not empty warehouseOutRecheckReportList}">
                <c:forEach var="report" items="${warehouseOutRecheckReportList}" varStatus="status">
                    <tr>
                        <td>${status.count}</td>
                        <td><a href="http://${report.domain}${fn:replace(report.uri,'display' , 'download')}">复核单</a></td>
                        <td>${outInSource}</td>
                        <td><date:date value ="${report.addTimeStr}"/></td>
                    </tr>
                </c:forEach>
            </c:if>
            <c:if test="${empty warehouseOutRecheckReportList}">
                <tr>
                    <td colspan="4">暂无出库复核报告。</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>
    </c:if>

</div>
<script>
    //附件删除
    function delWarehouseAttachment(attachmentId) {
        layer.confirm("您是否确认删除？", {
            btn: ['确定', '取消'] //按钮
        }, function () {
            $.ajax({
                type: "POST",
                url: page_url + "/warehouse/warehousesout/delWarehouseOutAttachment.do",
                data: {
                    'attachmentId': attachmentId
                },
                dataType: 'json',
                success: function (data) {
                    parent.layer.close(index);
                    window.location.reload();
                },
                error: function (data) {
                    layer.alert("删除失败")
                }
            });
        }, function () {
        });
    }

</script>

<%@ include file="../../common/footer.jsp"%>
