package com.newtask.filemove;

import com.newtask.filemove.batch.*;
import com.newtask.filemove.dto.CommandProcessorResult;
import com.newtask.filemove.dto.CommonConstant;
import com.vedeng.common.constant.CommonConstants;
import com.newtask.filemove.single.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CommandProcessorFactory {

    @Autowired
    private TraderCertificateSingleCommand traderCertificateSingleCommand;

    @Autowired
    private TraderCertificateBatchCommand traderCertificateBatchCommand;

    @Autowired
    private BrandSingleCommand brandSingleCommand;

    @Autowired
    private BrandBatchCommand brandBatchCommand;

    @Autowired
    private GoodsAttachmentSingleCommand goodsAttachmentSingleCommand;

    @Autowired
    private GoodsAttachmentBatchCommand goodsAttachmentBatchCommand;

    @Autowired
    private AttachmentBatchCommand attachmentBatchCommand;

    @Autowired
    private AttachmentSingleCommand attachmentSingleCommand;

    @Autowired
    private TraderFinanceBatchCommand traderFinanceBatchCommand;

    @Autowired
    private TraderFinanceSingleCommand traderFinanceSingleCommand;

    /**
     * 获取命令处理器的接口
     * @param param
     * @return
     */
    public CommandProcessorResult getCommandProcessor(String param) throws Exception{

        if(StringUtils.isEmpty(param)){
            throw new Exception("参数不能为空");
        }

        String[] params = param.split(CommonConstant.SPLIT_CHAR);
        if (params.length < 3) {
            throw new Exception("参数格式不对");
        }

        String commandType = params[0];
        String businessType = params[1];

        FileMoveInterface fileMoveInterface = getFileMoveInterface(commandType,businessType);;

        return new CommandProcessorResult(fileMoveInterface,params[2]);
    }

    /**
     * 获取文件迁移接口
     * @param commandType
     * @param businessType
     * @return
     */
    private FileMoveInterface getFileMoveInterface(String commandType, String businessType) {

        //单命令处理器
        if (CommonConstant.SINGLE.equals(commandType)) {
            if(CommonConstant.TRADER_CERTIFICATE.equals(businessType)){
                return traderCertificateSingleCommand;
            }
            if(CommonConstant.BRAND.equals(businessType)){
                return brandSingleCommand;
            }
            if(CommonConstant.GOODS_ATTACHMENT.equals(businessType)){
                return goodsAttachmentSingleCommand;
            }

            if(CommonConstant.ATTACHMENT.equals(businessType)){
                return attachmentSingleCommand;
            }

            if(CommonConstant.TRADER_FINANCE.equals(businessType)){
                return traderFinanceSingleCommand;
            }
        }


        //批处理命令处理器
        if (CommonConstant.BATCH.equals(commandType)) {

            if(CommonConstant.TRADER_CERTIFICATE.equals(businessType)){
                return traderCertificateBatchCommand;
            }

            if(CommonConstant.BRAND.equals(businessType)){
                return brandBatchCommand;
            }

            if(CommonConstant.GOODS_ATTACHMENT.equals(businessType)){
                return goodsAttachmentBatchCommand;
            }

            if(CommonConstant.ATTACHMENT.equals(businessType)){
                return attachmentBatchCommand;
            }

            if(CommonConstant.TRADER_FINANCE.equals(businessType)){
                return traderFinanceBatchCommand;
            }

        }

        return null;
    }

}
