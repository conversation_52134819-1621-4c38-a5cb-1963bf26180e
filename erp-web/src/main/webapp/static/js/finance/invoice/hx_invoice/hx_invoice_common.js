/**
 * @desc下载并保存航信发票地址信息
 * <AUTHOR>
 * @date 2020/11/25 14:35:35
 *
 * @param invoiceCode 发票代码
 * @param invoiceNum 发票号
 * @param hxInvoiceId id
 * @param openFlag 打开方式
 */
function viewAndDownloadHxInvoiceHref(invoiceCode,invoiceNum,hxInvoiceId,openFlag) {
    console.log(invoiceCode + ',' + invoiceNum + ',' + hxInvoiceId + ',' + openFlag);
    var imgSrc = $('#imgSrc' + hxInvoiceId).val();
    if (imgSrc != null && imgSrc != undefined && imgSrc != ''){
        console.log('图片已经下载完毕直接展示；');
        viewImgByOpenType(openFlag,hxInvoiceId);
        return;
    }

    $.ajax({
        url: '/finance/invoice/downLoadAndSaveInvoiceHref.do',
        data :{
            invoiceId: hxInvoiceId,
            invoiceType: '1',
            invoiceCode: invoiceCode,
            invoiceNum: invoiceNum
        },
        type: 'post',
        dataType: "json",
        success: function (res) {
            if (res.code == -1){
                console.log(res.message);
                layer.alert(res.message,function () {
                    layer.closeAll();
                });
            } else {
                console.log('图片本次下载完毕开始展示；');
                $('#imgSrc' + hxInvoiceId).val(res.data);
                viewImgByOpenType(openFlag,hxInvoiceId);
            }
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    })
}

/**
 * @desc根据打开方式查看图片 0 本页面查看   1 新tab查看
 * <AUTHOR>
 * @date 2020/11/25 14:35:35
 *
 * @param openFlag
 * @param hxInvoiceId
 */
function viewImgByOpenType(openFlag,hxInvoiceId) {
    if (openFlag == 0){
        if ($('#imgFlag' + hxInvoiceId).val() == 0) {
            $('#invoiceImg' + hxInvoiceId).html('  <div>' +
                ' <img style="max-height: 480px" src="' + $('#imgSrc' + hxInvoiceId).val() + '">' +
                '</div>')
            var imgTr = document.getElementById('imgTr' + hxInvoiceId);
            imgTr.style.display = 'table-row';
            $('#imgFlag' + hxInvoiceId).val(1);
            $('#viewInvocieFont' + hxInvoiceId).html('收起发票');
        } else {
            $('#invoiceImg' + hxInvoiceId).html('')
            var imgTr = document.getElementById('imgTr' + hxInvoiceId);
            imgTr.style.display = 'none';
            $('#imgFlag' + hxInvoiceId).val(0)
            $('#viewInvocieFont' + hxInvoiceId).html('查看发票');
        }
    } else {
        window.open($('#imgSrc' + hxInvoiceId).val());
    }
}