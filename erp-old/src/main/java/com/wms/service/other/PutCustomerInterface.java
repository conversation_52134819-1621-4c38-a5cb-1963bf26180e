package com.wms.service.other;

import com.vedeng.trader.model.Trader;
import com.wms.constant.WMSContant;
import com.wms.dto.WmsRequest;
import com.wms.service.AbstractWmsInterface;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;

import java.util.Arrays;

/**
 * 客戶资料下传接口
 */
@Service
@Slf4j
public class PutCustomerInterface extends AbstractWmsInterface {



    @Override
    protected void setMethodAndMessageId(WmsRequest wmsRequest) {
        wmsRequest.setMethod(WMSContant.PUT_CUSTOMER);
        wmsRequest.setMessageId(WMSContant.PUT_CUSTOMER);
    }

    @Override
    protected JSONObject getXmlDate(Object... param) {

        if(param == null ||param.length == 0){
            return null;
        }

        JSONArray headArray = new JSONArray();

        Arrays.stream(param).forEach(paramItem -> {

            Trader trader = (Trader)paramItem;

            JSONObject headItem = parseTraderToJsonObject(trader);

            headArray.add(headItem);
        });

        JSONObject headObject = new JSONObject();
        headObject.put("header",headArray);
        return headObject;
    }

    /**
     * 将一个客户对象转换成符合要求的JSONObject
     * @param trader
     * @return
     */
    private JSONObject parseTraderToJsonObject(Trader trader) {

        JSONObject headItem = new JSONObject();
        try {
            //TraderCustomer traderCustomer = traderCustomerMapper.getBaseCustomerByTraderId(trader.getTraderId());
            headItem.put("customerId",trader.getTraderId());
            headItem.put("customerType",trader.getCustomerType()==1? "CO" : "VE");//1 客户 2供应商
            //headItem.put("customerType",traderCustomer!=null? "CO" : "VE");//1 客户 2供应商
            headItem.put("activeFlag",trader.getIsEnable()==null||trader.getIsEnable() == 1 ? "Y" : "N");
            headItem.put("customerDescr1",trader.getTraderName());
        }catch (Exception e){
            log.error("【parseTraderToJsonObject】处理异常",e);
        }
        return headItem;
    }

    @Override
    protected <T> T parseResponseDate(JSONObject returnObject) {
        return null;
    }

}
