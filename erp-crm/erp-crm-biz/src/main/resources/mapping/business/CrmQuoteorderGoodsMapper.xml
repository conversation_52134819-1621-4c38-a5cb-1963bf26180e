<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.crm.business.quote.mapper.CrmQuoteorderGoodsMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.crm.business.quote.domain.entity.CrmQuoteorderGoods">
    <!--@mbg.generated-->
    <!--@Table T_QUOTEORDER_GOODS-->
    <id column="QUOTEORDER_GOODS_ID" jdbcType="INTEGER" property="quoteorderGoodsId" />
    <result column="QUOTEORDER_ID" jdbcType="INTEGER" property="quoteorderId" />
    <result column="IS_TEMP" jdbcType="INTEGER" property="isTemp" />
    <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="GOODS_NAME" jdbcType="VARCHAR" property="goodsName" />
    <result column="BRAND_NAME" jdbcType="VARCHAR" property="brandName" />
    <result column="MODEL" jdbcType="VARCHAR" property="model" />
    <result column="UNIT_NAME" jdbcType="VARCHAR" property="unitName" />
    <result column="PRICE" jdbcType="DECIMAL" property="price" />
    <result column="CURRENCY_UNIT_ID" jdbcType="INTEGER" property="currencyUnitId" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
    <result column="DELIVERY_CYCLE" jdbcType="VARCHAR" property="deliveryCycle" />
    <result column="DELIVERY_DIRECT" jdbcType="INTEGER" property="deliveryDirect" />
    <result column="DELIVERY_DIRECT_COMMENTS" jdbcType="VARCHAR" property="deliveryDirectComments" />
    <result column="REGISTRATION_NUMBER" jdbcType="VARCHAR" property="registrationNumber" />
    <result column="SUPPLIER_NAME" jdbcType="VARCHAR" property="supplierName" />
    <result column="REFERENCE_COST_PRICE" jdbcType="DECIMAL" property="referenceCostPrice" />
    <result column="REFERENCE_PRICE" jdbcType="VARCHAR" property="referencePrice" />
    <result column="REFERENCE_DELIVERY_CYCLE" jdbcType="VARCHAR" property="referenceDeliveryCycle" />
    <result column="REPORT_STATUS" jdbcType="INTEGER" property="reportStatus" />
    <result column="REPORT_COMMENTS" jdbcType="VARCHAR" property="reportComments" />
    <result column="HAVE_INSTALLATION" jdbcType="INTEGER" property="haveInstallation" />
    <result column="GOODS_COMMENTS" jdbcType="VARCHAR" property="goodsComments" />
    <result column="INSIDE_COMMENTS" jdbcType="VARCHAR" property="insideComments" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="LAST_REFERENCE_USER" jdbcType="INTEGER" property="lastReferenceUser" />
    <result column="IS_NEED_REPLY" jdbcType="INTEGER" property="isNeedReply" />
    <result column="IS_CONSUL_DELIVERY_CYCLE" jdbcType="INTEGER" property="isConsulDeliveryCycle" />
    <result column="IS_CONSUL_PRICE" jdbcType="INTEGER" property="isConsulPrice" />
    <result column="IS_CONSUL_REPORT" jdbcType="INTEGER" property="isConsulReport" />
    <result column="BRAND_ID" jdbcType="INTEGER" property="brandId" />
    <result column="IMG_URL" jdbcType="VARCHAR" property="imgUrl" />
    <result column="PARAM_CONTENT" jdbcType="VARCHAR" property="paramContent" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    QUOTEORDER_GOODS_ID, QUOTEORDER_ID, IS_TEMP, GOODS_ID, SKU, GOODS_NAME, BRAND_NAME,
    MODEL, UNIT_NAME, PRICE, CURRENCY_UNIT_ID, NUM, DELIVERY_CYCLE, DELIVERY_DIRECT,
    DELIVERY_DIRECT_COMMENTS, REGISTRATION_NUMBER, SUPPLIER_NAME, REFERENCE_COST_PRICE,
    REFERENCE_PRICE, REFERENCE_DELIVERY_CYCLE, REPORT_STATUS, REPORT_COMMENTS, HAVE_INSTALLATION,
    GOODS_COMMENTS, INSIDE_COMMENTS, IS_DELETE, ADD_TIME, CREATOR, MOD_TIME, UPDATER,
    LAST_REFERENCE_USER, IS_NEED_REPLY, IS_CONSUL_DELIVERY_CYCLE, IS_CONSUL_PRICE, IS_CONSUL_REPORT,
    BRAND_ID,IMG_URL,PARAM_CONTENT
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_QUOTEORDER_GOODS
    where QUOTEORDER_GOODS_ID = #{quoteorderGoodsId,jdbcType=INTEGER} AND IS_DELETE = 0
  </select>

  <select id="selectByQuoteorderId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_QUOTEORDER_GOODS
    where QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER} and IS_DELETE = 0 AND IS_TEMP = 0
    order by   QUOTEORDER_GOODS_ID asc
  </select>


  <select id="selectByQuoteNeedsIdList"   resultMap="BaseResultMap">
    SELECT goods.* FROM T_QUOTEORDER_NEEDS tqn
    LEFT JOIN T_R_QUOTEORDER_NEEDS_J_GOODS goodsrelate on tqn.QUOTEORDER_NEEDS_ID = goodsrelate.QUOTEORDER_NEEDS_ID AND goodsrelate .IS_DELETE =0
    left join T_QUOTEORDER_GOODS   goods on goodsrelate.QUOTEORDER_GOODS_ID = goods.QUOTEORDER_GOODS_ID and goods.IS_DELETE =0
    WHERE tqn.QUOTEORDER_NEEDS_ID  in
    <foreach item="needId" index="index" collection="quoteorderNeedsIdList" open="(" separator="," close=")">
      #{needId,jdbcType=BIGINT}
    </foreach>
    AND goods.QUOTEORDER_GOODS_ID IS NOT NULL
    AND tqn.IS_DELETE =0
  </select>


  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_QUOTEORDER_GOODS
    where QUOTEORDER_GOODS_ID = #{quoteorderGoodsId,jdbcType=INTEGER}
  </delete>
  <update id="updatePriceByPrimaryKey" parameterType="com.vedeng.crm.business.quote.domain.entity.CrmQuoteorderGoods">
    UPDATE T_QUOTEORDER_GOODS
    SET PRICE = NULL
    where QUOTEORDER_GOODS_ID = #{quoteorderGoodsId,jdbcType=INTEGER}
  </update>

  <insert id="insertSelective" keyColumn="QUOTEORDER_GOODS_ID" keyProperty="quoteorderGoodsId" parameterType="com.vedeng.crm.business.quote.domain.entity.CrmQuoteorderGoods" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_QUOTEORDER_GOODS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="quoteorderId != null">
        QUOTEORDER_ID,
      </if>
      <if test="isTemp != null">
        IS_TEMP,
      </if>
      <if test="goodsId != null">
        GOODS_ID,
      </if>
      <if test="sku != null">
        SKU,
      </if>
      <if test="goodsName != null">
        GOODS_NAME,
      </if>
      <if test="brandName != null">
        BRAND_NAME,
      </if>
      <if test="model != null">
        MODEL,
      </if>
      <if test="unitName != null">
        UNIT_NAME,
      </if>
      <if test="price != null">
        PRICE,
      </if>
      <if test="currencyUnitId != null">
        CURRENCY_UNIT_ID,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="deliveryCycle != null">
        DELIVERY_CYCLE,
      </if>
      <if test="deliveryDirect != null">
        DELIVERY_DIRECT,
      </if>
      <if test="deliveryDirectComments != null">
        DELIVERY_DIRECT_COMMENTS,
      </if>
      <if test="registrationNumber != null">
        REGISTRATION_NUMBER,
      </if>
      <if test="supplierName != null">
        SUPPLIER_NAME,
      </if>
      <if test="referenceCostPrice != null">
        REFERENCE_COST_PRICE,
      </if>
      <if test="referencePrice != null">
        REFERENCE_PRICE,
      </if>
      <if test="referenceDeliveryCycle != null">
        REFERENCE_DELIVERY_CYCLE,
      </if>
      <if test="reportStatus != null">
        REPORT_STATUS,
      </if>
      <if test="reportComments != null">
        REPORT_COMMENTS,
      </if>
      <if test="haveInstallation != null">
        HAVE_INSTALLATION,
      </if>
      <if test="goodsComments != null">
        GOODS_COMMENTS,
      </if>
      <if test="insideComments != null">
        INSIDE_COMMENTS,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="lastReferenceUser != null">
        LAST_REFERENCE_USER,
      </if>
      <if test="isNeedReply != null">
        IS_NEED_REPLY,
      </if>
      <if test="isConsulDeliveryCycle != null">
        IS_CONSUL_DELIVERY_CYCLE,
      </if>
      <if test="isConsulPrice != null">
        IS_CONSUL_PRICE,
      </if>
      <if test="isConsulReport != null">
        IS_CONSUL_REPORT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="quoteorderId != null">
        #{quoteorderId,jdbcType=INTEGER},
      </if>
      <if test="isTemp != null">
        #{isTemp,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="unitName != null">
        #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="currencyUnitId != null">
        #{currencyUnitId,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="deliveryCycle != null">
        #{deliveryCycle,jdbcType=VARCHAR},
      </if>
      <if test="deliveryDirect != null">
        #{deliveryDirect,jdbcType=INTEGER},
      </if>
      <if test="deliveryDirectComments != null">
        #{deliveryDirectComments,jdbcType=VARCHAR},
      </if>
      <if test="registrationNumber != null">
        #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="referenceCostPrice != null">
        #{referenceCostPrice,jdbcType=DECIMAL},
      </if>
      <if test="referencePrice != null">
        #{referencePrice,jdbcType=VARCHAR},
      </if>
      <if test="referenceDeliveryCycle != null">
        #{referenceDeliveryCycle,jdbcType=VARCHAR},
      </if>
      <if test="reportStatus != null">
        #{reportStatus,jdbcType=INTEGER},
      </if>
      <if test="reportComments != null">
        #{reportComments,jdbcType=VARCHAR},
      </if>
      <if test="haveInstallation != null">
        #{haveInstallation,jdbcType=INTEGER},
      </if>
      <if test="goodsComments != null">
        #{goodsComments,jdbcType=VARCHAR},
      </if>
      <if test="insideComments != null">
        #{insideComments,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="lastReferenceUser != null">
        #{lastReferenceUser,jdbcType=INTEGER},
      </if>
      <if test="isNeedReply != null">
        #{isNeedReply,jdbcType=INTEGER},
      </if>
      <if test="isConsulDeliveryCycle != null">
        #{isConsulDeliveryCycle,jdbcType=INTEGER},
      </if>
      <if test="isConsulPrice != null">
        #{isConsulPrice,jdbcType=INTEGER},
      </if>
      <if test="isConsulReport != null">
        #{isConsulReport,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <insert id="insertQuoteOrderGoodsNoSku" keyColumn="QUOTEORDER_GOODS_ID" keyProperty="quoteorderGoodsId" parameterType="com.vedeng.crm.business.quote.domain.entity.CrmQuoteorderGoods" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_QUOTEORDER_GOODS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="quoteorderId != null">
        QUOTEORDER_ID,
      </if>
      <if test="isTemp != null">
        IS_TEMP,
      </if>
      <if test="goodsId != null">
        GOODS_ID,
      </if>
      <if test="sku != null">
        SKU,
      </if>
      <if test="goodsName != null">
        GOODS_NAME,
      </if>
      <if test="brandName != null">
        BRAND_NAME,
      </if>
      <if test="model != null">
        MODEL,
      </if>
      <if test="unitName != null">
        UNIT_NAME,
      </if>
      <if test="price != null">
        PRICE,
      </if>
      <if test="currencyUnitId != null">
        CURRENCY_UNIT_ID,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="deliveryCycle != null">
        DELIVERY_CYCLE,
      </if>
      <if test="deliveryDirect != null">
        DELIVERY_DIRECT,
      </if>
      <if test="deliveryDirectComments != null">
        DELIVERY_DIRECT_COMMENTS,
      </if>
      <if test="registrationNumber != null">
        REGISTRATION_NUMBER,
      </if>
      <if test="supplierName != null">
        SUPPLIER_NAME,
      </if>
      <if test="referenceCostPrice != null">
        REFERENCE_COST_PRICE,
      </if>
      <if test="referencePrice != null">
        REFERENCE_PRICE,
      </if>
      <if test="referenceDeliveryCycle != null">
        REFERENCE_DELIVERY_CYCLE,
      </if>
      <if test="reportStatus != null">
        REPORT_STATUS,
      </if>
      <if test="reportComments != null">
        REPORT_COMMENTS,
      </if>
      <if test="haveInstallation != null">
        HAVE_INSTALLATION,
      </if>
      <if test="goodsComments != null">
        GOODS_COMMENTS,
      </if>
      <if test="insideComments != null">
        INSIDE_COMMENTS,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="lastReferenceUser != null">
        LAST_REFERENCE_USER,
      </if>
      <if test="isNeedReply != null">
        IS_NEED_REPLY,
      </if>
      <if test="isConsulDeliveryCycle != null">
        IS_CONSUL_DELIVERY_CYCLE,
      </if>
      <if test="isConsulPrice != null">
        IS_CONSUL_PRICE,
      </if>
      <if test="isConsulReport != null">
        IS_CONSUL_REPORT,
      </if>
      <if test="brandId != null">
        BRAND_ID,
      </if>
      <if test="imgUrl != null">
        IMG_URL,
      </if>
      <if test="paramContent != null">
        PARAM_CONTENT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="quoteorderId != null">
        #{quoteorderId,jdbcType=INTEGER},
      </if>
      <if test="isTemp != null">
        #{isTemp,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="unitName != null">
        #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="currencyUnitId != null">
        #{currencyUnitId,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="deliveryCycle != null">
        #{deliveryCycle,jdbcType=VARCHAR},
      </if>
      <if test="deliveryDirect != null">
        #{deliveryDirect,jdbcType=INTEGER},
      </if>
      <if test="deliveryDirectComments != null">
        #{deliveryDirectComments,jdbcType=VARCHAR},
      </if>
      <if test="registrationNumber != null">
        #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="referenceCostPrice != null">
        #{referenceCostPrice,jdbcType=DECIMAL},
      </if>
      <if test="referencePrice != null">
        #{referencePrice,jdbcType=VARCHAR},
      </if>
      <if test="referenceDeliveryCycle != null">
        #{referenceDeliveryCycle,jdbcType=VARCHAR},
      </if>
      <if test="reportStatus != null">
        #{reportStatus,jdbcType=INTEGER},
      </if>
      <if test="reportComments != null">
        #{reportComments,jdbcType=VARCHAR},
      </if>
      <if test="haveInstallation != null">
        #{haveInstallation,jdbcType=INTEGER},
      </if>
      <if test="goodsComments != null">
        #{goodsComments,jdbcType=VARCHAR},
      </if>
      <if test="insideComments != null">
        #{insideComments,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="lastReferenceUser != null">
        #{lastReferenceUser,jdbcType=INTEGER},
      </if>
      <if test="isNeedReply != null">
        #{isNeedReply,jdbcType=INTEGER},
      </if>
      <if test="isConsulDeliveryCycle != null">
        #{isConsulDeliveryCycle,jdbcType=INTEGER},
      </if>
      <if test="isConsulPrice != null">
        #{isConsulPrice,jdbcType=INTEGER},
      </if>
      <if test="isConsulReport != null">
        #{isConsulReport,jdbcType=INTEGER},
      </if>
      <if test="brandId != null">
        #{brandId,jdbcType=INTEGER},
      </if>
      <if test="imgUrl != null">
        #{imgUrl,jdbcType=VARCHAR},
      </if>
      <if test="paramContent != null">
        #{paramContent,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateQuoteOrderGoodsNoSku" parameterType="com.vedeng.crm.business.quote.domain.entity.CrmQuoteorderGoodsNoSku">
    <!--@mbg.generated-->
    update T_QUOTEORDER_GOODS
    <set>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="goodsName != null">
        GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="brandId != null">
        BRAND_ID=#{brandId,jdbcType=INTEGER},
      </if>
      <if test="unitName != null">
        UNIT_NAME = #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="imgUrl != null">
        IMG_URL=#{imgUrl,jdbcType=VARCHAR},
      </if>
      <if test="paramContent != null">
        PARAM_CONTENT=#{paramContent,jdbcType=VARCHAR},
      </if>
    </set>
    where QUOTEORDER_GOODS_ID = #{quoteorderGoodsId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.crm.business.quote.domain.entity.CrmQuoteorderGoods">
    <!--@mbg.generated-->
    update T_QUOTEORDER_GOODS
    <set>
      <if test="quoteorderId != null">
        QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER},
      </if>
      <if test="isTemp != null">
        IS_TEMP = #{isTemp,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        GOODS_ID = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        MODEL = #{model,jdbcType=VARCHAR},
      </if>
      <if test="unitName != null">
        UNIT_NAME = #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        PRICE = #{price,jdbcType=DECIMAL},
      </if>
      <if test="price == null">
        PRICE = null,
      </if>
      <if test="currencyUnitId != null">
        CURRENCY_UNIT_ID = #{currencyUnitId,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="deliveryCycle != null">
        DELIVERY_CYCLE = #{deliveryCycle,jdbcType=VARCHAR},
      </if>
      <if test="deliveryCycle == null">
        DELIVERY_CYCLE = null,
      </if>
      <if test="deliveryDirect != null">
        DELIVERY_DIRECT = #{deliveryDirect,jdbcType=INTEGER},
      </if>
      <if test="deliveryDirectComments != null">
        DELIVERY_DIRECT_COMMENTS = #{deliveryDirectComments,jdbcType=VARCHAR},
      </if>
      <if test="registrationNumber != null">
        REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        SUPPLIER_NAME = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="referenceCostPrice != null">
        REFERENCE_COST_PRICE = #{referenceCostPrice,jdbcType=DECIMAL},
      </if>
      <if test="referencePrice != null">
        REFERENCE_PRICE = #{referencePrice,jdbcType=VARCHAR},
      </if>
      <if test="referenceDeliveryCycle != null">
        REFERENCE_DELIVERY_CYCLE = #{referenceDeliveryCycle,jdbcType=VARCHAR},
      </if>
      <if test="reportStatus != null">
        REPORT_STATUS = #{reportStatus,jdbcType=INTEGER},
      </if>
      <if test="reportStatus == null">
        REPORT_STATUS = null,
      </if>
      <if test="reportComments != null">
        REPORT_COMMENTS = #{reportComments,jdbcType=VARCHAR},
      </if>
      <if test="haveInstallation != null">
        HAVE_INSTALLATION = #{haveInstallation,jdbcType=INTEGER},
      </if>
      <if test="goodsComments != null">
        GOODS_COMMENTS = #{goodsComments,jdbcType=VARCHAR},
      </if>
      <if test="insideComments != null">
        INSIDE_COMMENTS = #{insideComments,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="lastReferenceUser != null">
        LAST_REFERENCE_USER = #{lastReferenceUser,jdbcType=INTEGER},
      </if>
      <if test="isNeedReply != null">
        IS_NEED_REPLY = #{isNeedReply,jdbcType=INTEGER},
      </if>
      <if test="isConsulDeliveryCycle != null">
        IS_CONSUL_DELIVERY_CYCLE = #{isConsulDeliveryCycle,jdbcType=INTEGER},
      </if>
      <if test="isConsulPrice != null">
        IS_CONSUL_PRICE = #{isConsulPrice,jdbcType=INTEGER},
      </if>
      <if test="isConsulReport != null">
        IS_CONSUL_REPORT = #{isConsulReport,jdbcType=INTEGER},
      </if>
    </set>
    where QUOTEORDER_GOODS_ID = #{quoteorderGoodsId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.crm.business.quote.domain.entity.CrmQuoteorderGoods">
    <!--@mbg.generated-->
    update T_QUOTEORDER_GOODS
    set QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER},
    IS_TEMP = #{isTemp,jdbcType=INTEGER},
    GOODS_ID = #{goodsId,jdbcType=INTEGER},
    SKU = #{sku,jdbcType=VARCHAR},
    GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
    BRAND_NAME = #{brandName,jdbcType=VARCHAR},
    MODEL = #{model,jdbcType=VARCHAR},
    UNIT_NAME = #{unitName,jdbcType=VARCHAR},
    PRICE = #{price,jdbcType=DECIMAL},
    CURRENCY_UNIT_ID = #{currencyUnitId,jdbcType=INTEGER},
    NUM = #{num,jdbcType=INTEGER},
    DELIVERY_CYCLE = #{deliveryCycle,jdbcType=VARCHAR},
    DELIVERY_DIRECT = #{deliveryDirect,jdbcType=INTEGER},
    DELIVERY_DIRECT_COMMENTS = #{deliveryDirectComments,jdbcType=VARCHAR},
    REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR},
    SUPPLIER_NAME = #{supplierName,jdbcType=VARCHAR},
    REFERENCE_COST_PRICE = #{referenceCostPrice,jdbcType=DECIMAL},
    REFERENCE_PRICE = #{referencePrice,jdbcType=VARCHAR},
    REFERENCE_DELIVERY_CYCLE = #{referenceDeliveryCycle,jdbcType=VARCHAR},
    REPORT_STATUS = #{reportStatus,jdbcType=INTEGER},
    REPORT_COMMENTS = #{reportComments,jdbcType=VARCHAR},
    HAVE_INSTALLATION = #{haveInstallation,jdbcType=INTEGER},
    GOODS_COMMENTS = #{goodsComments,jdbcType=VARCHAR},
    INSIDE_COMMENTS = #{insideComments,jdbcType=VARCHAR},
    IS_DELETE = #{isDelete,jdbcType=INTEGER},
    ADD_TIME = #{addTime,jdbcType=BIGINT},
    CREATOR = #{creator,jdbcType=INTEGER},
    MOD_TIME = #{modTime,jdbcType=BIGINT},
    UPDATER = #{updater,jdbcType=INTEGER},
    LAST_REFERENCE_USER = #{lastReferenceUser,jdbcType=INTEGER},
    IS_NEED_REPLY = #{isNeedReply,jdbcType=INTEGER},
    IS_CONSUL_DELIVERY_CYCLE = #{isConsulDeliveryCycle,jdbcType=INTEGER},
    IS_CONSUL_PRICE = #{isConsulPrice,jdbcType=INTEGER},
    IS_CONSUL_REPORT = #{isConsulReport,jdbcType=INTEGER}
    where QUOTEORDER_GOODS_ID = #{quoteorderGoodsId,jdbcType=INTEGER}
  </update>

  <update id="updateDeliveryCycle">
    update T_QUOTEORDER_GOODS
      set DELIVERY_CYCLE = #{deliveryCycle,jdbcType=VARCHAR},
          MOD_TIME = #{modTime,jdbcType=BIGINT},
          UPDATER = #{updater,jdbcType=INTEGER}
    where QUOTEORDER_GOODS_ID = #{quoteorderGoodsId,jdbcType=INTEGER}
    <if test="oldDeliveryCycle != null and oldDeliveryCycle != ''">
      and DELIVERY_CYCLE = #{oldDeliveryCycle,jdbcType=VARCHAR}
    </if>
    <if test="oldDeliveryCycle == null or oldDeliveryCycle == '' ">
      and (DELIVERY_CYCLE is null or DELIVERY_CYCLE = '')
    </if>
      and IS_DELETE = 0
  </update>

  <update id="updateNum">
    update T_QUOTEORDER_GOODS
      set NUM = #{num,jdbcType=INTEGER},
          MOD_TIME = #{modTime,jdbcType=BIGINT},
          UPDATER = #{updater,jdbcType=INTEGER}
    where QUOTEORDER_GOODS_ID = #{quoteorderGoodsId,jdbcType=INTEGER}
      and NUM = #{oldNum,jdbcType=INTEGER}
      and IS_DELETE = 0
  </update>

  <update id="updatePrice">
    update T_QUOTEORDER_GOODS
      set

        PRICE = #{price,jdbcType=DECIMAL},
          MOD_TIME = #{modTime,jdbcType=BIGINT},
          UPDATER = #{updater,jdbcType=INTEGER}
    where QUOTEORDER_GOODS_ID = #{quoteorderGoodsId,jdbcType=INTEGER}
    <if test="oldPrice ==null">
      and PRICE IS NULL
    </if>
    <if test="oldPrice !=null">
      and PRICE = #{oldPrice,jdbcType=DECIMAL}
    </if>
      and IS_DELETE = 0
  </update>

  <update id="updateReportStatus">
    update T_QUOTEORDER_GOODS
      set REPORT_STATUS = #{reportStatus,jdbcType=INTEGER},
          REPORT_COMMENTS = #{reportComments,jdbcType=VARCHAR},
          MOD_TIME = #{modTime,jdbcType=BIGINT},
          UPDATER = #{updater,jdbcType=INTEGER}
    where QUOTEORDER_GOODS_ID = #{quoteorderGoodsId,jdbcType=INTEGER}
      <if test="oldReportStatus != null">
        and REPORT_STATUS = #{oldReportStatus,jdbcType=INTEGER}
      </if>
      <if test="oldReportStatus == null">
        and REPORT_STATUS is null
      </if>
      and IS_DELETE = 0
  </update>

  <update id="batchUpdateConsultationReport">
    update T_QUOTEORDER_GOODS
      set IS_CONSUL_REPORT = CASE WHEN #{isConsulReport} != 0 THEN #{isConsulReport} ELSE IS_CONSUL_REPORT END,
          IS_CONSUL_DELIVERY_CYCLE = CASE WHEN #{isConsulDeliveryCycle} != 0 THEN #{isConsulDeliveryCycle} ELSE IS_CONSUL_DELIVERY_CYCLE END,
          IS_CONSUL_PRICE = CASE WHEN #{isConsulPrice} != 0 THEN #{isConsulPrice} ELSE IS_CONSUL_PRICE END,
          <if test="reportStatus != null">
          REPORT_STATUS = #{reportStatus,jdbcType=INTEGER},
          </if>
            MOD_TIME = #{modTime,jdbcType=BIGINT},
          UPDATER = #{updater,jdbcType=INTEGER}
    where QUOTEORDER_GOODS_ID in
    <foreach collection="quoteorderGoodsIds" item="quoteorderGoodsId" open="(" separator="," close=")">
      #{quoteorderGoodsId,jdbcType=INTEGER}
    </foreach>
  </update>

  <update id="cleanConsultationContent" parameterType="com.vedeng.crm.business.quote.domain.dto.CleanConsultationContentDto">
    update T_QUOTEORDER_GOODS
      set IS_CONSUL_REPORT = 0,
          IS_CONSUL_DELIVERY_CYCLE = 0,
          IS_CONSUL_PRICE = 0,
          MOD_TIME = #{modTime,jdbcType=BIGINT},
          UPDATER = #{updater,jdbcType=INTEGER}
    where QUOTEORDER_GOODS_ID in
    <foreach collection="quoteorderGoodsList" item="quoteorderGoods" open="(" separator="," close=")">
      #{quoteorderGoods.quoteorderGoodsId,jdbcType=INTEGER}
    </foreach>
  </update>
</mapper>
