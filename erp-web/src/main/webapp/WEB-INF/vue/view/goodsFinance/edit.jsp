<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<div id="app" style="display: none;">
    <el-form ref="form" :model="form" label-width="130px" :rules="rules">
        <el-row>
            <el-col :span="12">
                <el-form-item label="商品订货号">
                    <el-input v-model="form.skuNo" readonly="true" disabled="true"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="商品名称">
                    <el-input v-model="form.showName" readonly="true" disabled="true"></el-input>
                </el-form-item>
            </el-col>
        </el-row>

        <el-row>
            <el-col :span="12">
                <el-form-item label="商品单位">
                    <el-input v-model="form.unitName" disabled="true" readonly="true"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="商品类别">
                    <el-input v-model="form.spuTypeValue" disabled="true" readonly="true"></el-input>
                </el-form-item>
            </el-col>
        </el-row>

        <el-row>
            <el-col :span="12">
                <el-form-item label="是否医疗器械" class="is-required" prop="isMedicalEquipment">
                    <el-select v-model="form.isMedicalEquipment" placeholder="请选择" name="isMedicalEquipment"
                               @change="changeCheckMedicalEquipment()">
                        <el-option
                                v-for="item in medicalEquipmentOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12" v-if="checkMedicalEquipment">
                <el-form-item label="医疗器械细分类" class="is-required"
                              :rules="this.checkMedicalEquipment?rules.medicalEquipmentType : [{required: false}]"
                              prop="medicalEquipmentType">
                    <el-input v-model.trim="form.medicalEquipmentType" name="medicalEquipmentType"></el-input>
                </el-form-item>
            </el-col>
        </el-row>

        <el-row>
            <el-col :span="12" v-if="checkMedicalEquipment">
                <el-form-item label="医疗器械用途" class="is-required"
                              :rules="this.checkMedicalEquipment?rules.medicalEquipmentUse:[{required: false}]"
                              prop="medicalEquipmentUse">
                    <el-input v-model.trim="form.medicalEquipmentUse" name="medicalEquipmentUse"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12" v-if="checkMedicalEquipment">
                <el-form-item label="医疗器械产线" class="is-required"
                              :rules="this.checkMedicalEquipment?rules.medicalEquipmentLine:[{required: false}]"
                              prop="medicalEquipmentLine">
                    <el-input v-model.trim="form.medicalEquipmentLine" name="medicalEquipmentLine"></el-input>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row style="text-align: center;">
            <el-button type="primary" @click="onSubmit('form')" :loading="onSubmitLoading">提交</el-button>
            <el-button @click="closeThis">取消</el-button>
        </el-row>
    </el-form>
</div>

<script src="${pageContext.request.contextPath}/static/api/goods/finance/goodsFinance.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">
    const goodsFinanceInfo = {
        goodsFinanceId: '${goodsFinanceId}'
    };

    const sendThis = (_this) => {
        vm = _this;
    }
    new Vue({
        el: '#app',
        data() {
            return {
                onSubmitLoading: false,
                checkMedicalEquipment: false,
                form: {},
                medicalEquipmentOptions: [{
                    value: 0,
                    label: '否'
                }, {
                    value: 1,
                    label: '是'
                }],
                rules: {
                    isMedicalEquipment: [
                        {required: true, message: '是否医疗器械必填', trigger: 'blur'}
                    ],
                    medicalEquipmentType: [
                        {required: true, message: '医疗器械细分类必填', trigger: 'blur'},
                        {max: 50, message: '内容过长，请检查', trigger: 'blur'}
                    ],
                    medicalEquipmentUse: [
                        {required: true, message: '医疗器械用途必填', trigger: 'blur'},
                        {max: 50, message: '内容过长，请检查', trigger: 'blur'}
                    ],
                    medicalEquipmentLine: [
                        {required: true, message: '医疗器械产线必填', trigger: 'blur'},
                        {max: 50, message: '内容过长，请检查', trigger: 'blur'}
                    ]
                }
            }
        },
        created() {
            this.initData();
        },
        mounted() {
            loadingApp()
            sendThis(this);
        },
        methods: {
            async initData() {
                if (goodsFinanceInfo.goodsFinanceId != null && goodsFinanceInfo.goodsFinanceId != "") {
                    await viewData(goodsFinanceInfo).then(res => {
                        this.form = res.data.data;
                        // 不在 option 中的值设为 ''
                        if (this.form.isMedicalEquipment != 0 && this.form.isMedicalEquipment != 1) {
                            this.form.isMedicalEquipment = ''
                            this.checkMedicalEquipment = false;
                        } else if (this.form.isMedicalEquipment == 1) {
                            this.checkMedicalEquipment = true;
                        }
                    })
                }
            },
            closeThis() {
                parent.layer.close(index);
            },
            // 校验条件根据 值 变化
            changeCheckMedicalEquipment() {
                this.checkMedicalEquipment = this.form.isMedicalEquipment == 1 ? true : false;
            },
            onSubmit(form) {
                this.onSubmitLoading = true;
                this.$refs[form].validate((valid) => {
                    if (valid) {
                        let data = {
                            goodsFinanceId: this.form.goodsFinanceId,
                            isMedicalEquipment: this.form.isMedicalEquipment,
                            medicalEquipmentType: this.form.isMedicalEquipment == 1 ? this.form.medicalEquipmentType : '',
                            medicalEquipmentUse: this.form.isMedicalEquipment == 1 ? this.form.medicalEquipmentUse : '',
                            medicalEquipmentLine: this.form.isMedicalEquipment == 1 ? this.form.medicalEquipmentLine : ''
                        }
                        saveGoodsFinanceInfo(data).then(res => {
                            if (res.data.code == 0) {
                                this.$message({
                                    message: '保存成功!',
                                    type: 'success',
                                    onClose: () => {
                                        this.closeThis()
                                    }
                                });
                                window.parent.location.reload();
                            } else {
                                this.$message({
                                    message: res.data.message,
                                    type: 'error',
                                });
                                this.onSubmitLoading = false;
                            }
                        }).catch((err) => {
                            console.log(err);
                        })
                        this.onSubmitLoading = false
                    }
                    this.onSubmitLoading = false
                })
            }
        }

    })
</script>




