package com.vedeng.config;


import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;
import org.springframework.aop.framework.ProxyFactory;
import org.springframework.aop.target.AbstractLazyCreationTargetSource;
import org.springframework.batch.core.configuration.JobRegistry;
import org.springframework.batch.core.configuration.annotation.BatchConfigurer;
import org.springframework.batch.core.configuration.support.MapJobRegistry;
import org.springframework.batch.core.explore.JobExplorer;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.transaction.PlatformTransactionManager;

import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 自定义batch配置
 * @date 2022/10/25 12:57
 */
public class SimpleBatchConfiguration extends AbstractBatchConfiguration {

    @Autowired
    private ApplicationContext context;

    private boolean initialized = false;

    private AtomicReference<JobRepository> jobRepository = new AtomicReference<JobRepository>();

    private AtomicReference<JobLauncher> jobLauncher = new AtomicReference<JobLauncher>();

    private AtomicReference<JobRegistry> jobRegistry = new AtomicReference<JobRegistry>();

    private AtomicReference<PlatformTransactionManager> transactionManager = new AtomicReference<PlatformTransactionManager>();

    private AtomicReference<JobExplorer> jobExplorer = new AtomicReference<JobExplorer>();

    @Override
    @Bean
    public JobRepository jobRepository() throws Exception {
        return createLazyProxy(jobRepository, JobRepository.class);
    }

    @Override
    @Bean
    public JobLauncher jobLauncher() throws Exception {
        return createLazyProxy(jobLauncher, JobLauncher.class);
    }

    @Override
    @Bean
    public JobRegistry jobRegistry() throws Exception {
        return createLazyProxy(jobRegistry, JobRegistry.class);
    }

    @Override
    @Bean
    public JobExplorer jobExplorer() {
        return createLazyProxy(jobExplorer, JobExplorer.class);
    }

    @Override
    @Bean
    public PlatformTransactionManager transactionManager() throws Exception {
        return createLazyProxy(transactionManager, PlatformTransactionManager.class);
    }

    private <T> T createLazyProxy(AtomicReference<T> reference, Class<T> type) {
        ProxyFactory factory = new ProxyFactory();
        factory.setTargetSource(new ReferenceTargetSource<T>(reference));
        factory.addAdvice(new PassthruAdvice());
        factory.setInterfaces(new Class<?>[] { type });
        @SuppressWarnings("unchecked")
        T proxy = (T) factory.getProxy();
        return proxy;
    }

    /**
     * Sets up the basic components by extracting them from the
     * {@link BatchConfigurer configurer}, defaulting to some sensible values as
     * long as a unique DataSource is available.
     *
     * @throws Exception
     *             if there is a problem in the configurer
     */
    protected void initialize() throws Exception {
        if (initialized) {
            return;
        }
        BatchConfigurer configurer = getConfigurer(context.getBeansOfType(BatchConfigurer.class).values());
        jobRepository.set(configurer.getJobRepository());
        jobLauncher.set(configurer.getJobLauncher());
        transactionManager.set(configurer.getTransactionManager());
        jobRegistry.set(new MapJobRegistry());
        jobExplorer.set(configurer.getJobExplorer());
        initialized = true;
    }

    private class PassthruAdvice implements MethodInterceptor {

        @Override
        public Object invoke(MethodInvocation invocation) throws Throwable {
            return invocation.proceed();
        }

    }

    private class ReferenceTargetSource<T> extends AbstractLazyCreationTargetSource {

        private AtomicReference<T> reference;

        public ReferenceTargetSource(AtomicReference<T> reference) {
            this.reference = reference;
        }

        @Override
        protected Object createObject() throws Exception {
            initialize();
            return reference.get();
        }
    }

}
