Vue.component('ui-checkbox', {
    template: `
        <div
            class="vd-ui-checkbox-item"
            :class="{
                'single-row': singleRow,
                'vd-ui-checkbox-item-checked': currentChecked,
                'vd-ui-checkbox-item-disabled': disabled,
                'vd-ui-checkbox-item-progress': isSelectedAll && onProgress === 2,
                'vd-ui-checkbox-item-labeltype': type === 'label'
            }"
            @click="handlerClick(!currentChecked)"
            :title="label"
        >
            <div class="vd-ui-checkbox-inner">
                <div class="vd-ui-checkbox-icon">
                    <div class="vd-ui-checkbox-icon-selected2">
                        <i class="vd-ui_icon icon-selected2"></i>
                    </div>
                </div>
                <span v-html="label" class="vd-ui-checkbox-label"></span>
            </div>
        </div>
    `,
    props: {
        label: {
            type: String,
            default: "",
        },
        checked: {
            type: <PERSON>olean,
            default: false,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        onProgress: {
            type: Number,
            default: 1,
        },
        isSelectedAll: {
            type: Boolean,
            default: false,
        },
        type: {
            type: String,
            default: "", //label
        },
        // 单行展示
        singleRow: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            currentChecked: false,
        };
    },
    mounted() {
        this.currentChecked = this.checked;
    },
    watch: {
        onProgress() {
            this.currentChecked = this.onProgress === 3;
            this.$emit("update:checked", this.onProgress === 3);
        },
        checked() {
            this.currentChecked = this.checked;
        },
    },
    methods: {
        handlerClick() {
            if (this.isSelectedAll) {
                if (this.onProgress === 3) {
                    this.$emit("update:onProgress", 1);
                } else {
                    this.$emit("update:onProgress", 3);
                }
            }
            
            if (!this.disabled) {
                this.currentChecked = !this.currentChecked;
                this.$emit("update:checked", this.currentChecked);
                this.$emit("change", this.currentChecked);
            }
        },
    },
})

Vue.component('ui-checkbox-group', {
    template: `
        <div 
            class="vd-ui-checkbox-group"
            :class="{
                'is-label': type==='label',
                'is-margin-': !singleRow
            }"
        >
            <template v-for="(item, index) in boxList">
                <ui-checkbox
                    :key="index"
                    :label="item.label"
                    :checked.sync="item.checked"
                    :disabled="item.disabled"
                    @change="handlerChange"
                    :type="type"
                    :singleRow="singleRow"
                ></ui-checkbox>
            </template>
            <div class="vd-ui-input-error" v-if="errorable">
                <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i>
                <p class="vd-ui-input-error--errmsg">{{errorMsg}}</p>
            </div>
        </div>
    `,
    watch: {
        list() {
            this.setList();
        },
        onProgress() {
            this.boxList.forEach((item, index) => {
                if (this.onProgress === 3) {
                    this.$set(this.boxList[index], "checked", true);
                } else if (this.onProgress === 1) {
                    this.$set(this.boxList[index], "checked", false);
                }
            });

            if (this.onProgress !== 2) {
                this.handlerChange(null, true);
            }
        },
        value() {
            this.setList();
        }
    },
    data() {
        return {
            boxList: [],
        };
    },
    props: {
        list: {
            type: Array,
            default() {
                return [];
            },
        },
        value: {
            type: Array,
            default() {
                return [];
            },
        },
        onProgress: {
            type: Number,
            default: 1,
        },
        errorMsg: {
            type: String,
            default: ''
        },
        errorable: {
            type: Boolean,
            default: false
        },
        type: {
            type: String,
            default: '' //label
        },
        singleRow: {
            type: Boolean,
            default: false
        }
    },
    mounted() {
        this.setList();
    },
    methods: {
        setList() {
            this.boxList = JSON.parse(JSON.stringify(this.list));

            this.boxList.forEach((item) => {
                this.value.forEach((value) => {
                    if (item.value == value) {
                        item.checked = true;
                    }
                });
            });

            this.checkOnProgress();
        },
        handlerChange(data, silent) {
            console.log('boxList:', this.boxList);
            let values = [];
            this.boxList.forEach((item) => {
                if (item.checked) {
                    values.push(item.value);
                }
            });

            if (values.join('__') !== this.value.join('__')) {
                this.$emit("input", values);
                this.$emit("change", values);
                
                this.checkValid(values);

                if (!silent) {
                    this.$nextTick(() => {
                        this.checkOnProgress();
                    });
                }
            }
        },
        checkOnProgress() {
            if (
                this.value.length &&
                this.value.length === this.boxList.length
            ) {
                this.$emit("update:onProgress", 3);
            } else if (!this.value.length) {
                this.$emit("update:onProgress", 1);
            } else {
                this.$emit("update:onProgress", 2);
            }
        },
        selectAll() {
            this.boxList.forEach((item) => {
                item.checked = true;
            });

            this.handlerChange();
        },
        checkValid(newValue) {
            // if(this.validKey && this.validValue) {
            //     if(this.$form.validList[this.validKey].indexOf(this.validValue) !== -1) {
            //         let validData = this.$form.checkValid(this.validValue, newValue, this.validKey);

            //         this.triggerError(validData);
            //     }
            // }
        },
        triggerError(validData) {
            if(validData.result) {
                this.errorMsg = '';
                this.errorable = false;
            } else {
                this.errorMsg = validData.message;
                this.errorable = true;
            }
        }
    },
})