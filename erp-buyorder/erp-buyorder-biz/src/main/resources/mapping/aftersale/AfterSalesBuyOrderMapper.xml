<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.aftersale.mapper.AfterSalesBuyOrderMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.aftersale.domain.entity.BuyOrderAfterSalesEntity">
    <!--@mbg.generated-->
    <!--@Table T_AFTER_SALES-->
    <id column="AFTER_SALES_ID" jdbcType="INTEGER" property="afterSalesId" />
    <result column="AFTER_SALES_NO" jdbcType="VARCHAR" property="afterSalesNo" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="SUBJECT_TYPE" jdbcType="INTEGER" property="subjectType" />
    <result column="TYPE" jdbcType="INTEGER" property="type" />
    <result column="ORDER_ID" jdbcType="INTEGER" property="orderId" />
    <result column="ORDER_NO" jdbcType="VARCHAR" property="orderNo" />
    <result column="SERVICE_USER_ID" jdbcType="INTEGER" property="serviceUserId" />
    <result column="VALID_STATUS" jdbcType="BOOLEAN" property="validStatus" />
    <result column="VALID_TIME" jdbcType="BIGINT" property="validTime" />
    <result column="STATUS" jdbcType="BOOLEAN" property="status" />
    <result column="ATFER_SALES_STATUS" jdbcType="BOOLEAN" property="atferSalesStatus" />
    <result column="ATFER_SALES_STATUS_RESON" jdbcType="INTEGER" property="atferSalesStatusReson" />
    <result column="ATFER_SALES_STATUS_USER" jdbcType="INTEGER" property="atferSalesStatusUser" />
    <result column="ATFER_SALES_STATUS_COMMENTS" jdbcType="VARCHAR" property="atferSalesStatusComments" />
    <result column="FIRST_VALID_STATUS" jdbcType="BOOLEAN" property="firstValidStatus" />
    <result column="FIRST_VALID_TIME" jdbcType="BIGINT" property="firstValidTime" />
    <result column="FIRST_VALID_USER" jdbcType="INTEGER" property="firstValidUser" />
    <result column="FIRST_VALID_COMMENTS" jdbcType="VARCHAR" property="firstValidComments" />
    <result column="SOURCE" jdbcType="BOOLEAN" property="source" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="IS_OUT_AFTER" jdbcType="BOOLEAN" property="isOutAfter" />
    <result column="INVOICE_SEND_STATUS" jdbcType="BOOLEAN" property="invoiceSendStatus" />
    <result column="INVOICE_ARRIVAL_STATUS" jdbcType="BOOLEAN" property="invoiceArrivalStatus" />
    <result column="UPDATE_DATA_TIME" jdbcType="TIMESTAMP" property="updateDataTime" />
    <result column="IS_LIGHTNING" jdbcType="BOOLEAN" property="isLightning" />
    <result column="CREATE_TYPE" jdbcType="TINYINT" property="createType" />
    <result column="CREATE_FRONT_END_USER" jdbcType="VARCHAR" property="createFrontEndUser" />
    <result column="CLOSE_FRONT_END_MOBILE" jdbcType="VARCHAR" property="closeFrontEndMobile" />
    <result column="VERIFIES_NOT_PASS_REASON" jdbcType="VARCHAR" property="verifiesNotPassReason" />
    <result column="HANDLE_STATUS" jdbcType="BOOLEAN" property="handleStatus" />
    <result column="INVOICE_REFUND_STATUS" jdbcType="BOOLEAN" property="invoiceRefundStatus" />
    <result column="AMOUNT_REFUND_STATUS" jdbcType="BOOLEAN" property="amountRefundStatus" />
    <result column="AMOUNT_COLLECTION_STATUS" jdbcType="BOOLEAN" property="amountCollectionStatus" />
    <result column="AMOUNT_PAY_STATUS" jdbcType="BOOLEAN" property="amountPayStatus" />
    <result column="INVOICE_MAKEOUT_STATUS" jdbcType="BOOLEAN" property="invoiceMakeoutStatus" />
    <result column="IS_NEW" jdbcType="BOOLEAN" property="isNew" />
    <result column="DELIVERY_DIRECT_AFTER_SALES_ID" jdbcType="INTEGER" property="deliveryDirectAfterSalesId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    AFTER_SALES_ID, AFTER_SALES_NO, COMPANY_ID, SUBJECT_TYPE, `TYPE`, ORDER_ID, ORDER_NO, 
    SERVICE_USER_ID, VALID_STATUS, VALID_TIME, `STATUS`, ATFER_SALES_STATUS, ATFER_SALES_STATUS_RESON, 
    ATFER_SALES_STATUS_USER, ATFER_SALES_STATUS_COMMENTS, FIRST_VALID_STATUS, FIRST_VALID_TIME, 
    FIRST_VALID_USER, FIRST_VALID_COMMENTS, `SOURCE`, ADD_TIME, CREATOR, MOD_TIME, UPDATER, 
    IS_OUT_AFTER, INVOICE_SEND_STATUS, INVOICE_ARRIVAL_STATUS, UPDATE_DATA_TIME, IS_LIGHTNING, 
    CREATE_TYPE, CREATE_FRONT_END_USER, CLOSE_FRONT_END_MOBILE, VERIFIES_NOT_PASS_REASON, 
    HANDLE_STATUS, INVOICE_REFUND_STATUS, AMOUNT_REFUND_STATUS, AMOUNT_COLLECTION_STATUS, 
    AMOUNT_PAY_STATUS, INVOICE_MAKEOUT_STATUS, IS_NEW, DELIVERY_DIRECT_AFTER_SALES_ID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_AFTER_SALES
    where AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_AFTER_SALES
    where AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="AFTER_SALES_ID" keyProperty="afterSalesId" parameterType="com.vedeng.erp.aftersale.domain.entity.BuyOrderAfterSalesEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES (AFTER_SALES_NO, COMPANY_ID, SUBJECT_TYPE, 
      `TYPE`, ORDER_ID, ORDER_NO, 
      SERVICE_USER_ID, VALID_STATUS, VALID_TIME, 
      `STATUS`, ATFER_SALES_STATUS, ATFER_SALES_STATUS_RESON, 
      ATFER_SALES_STATUS_USER, ATFER_SALES_STATUS_COMMENTS, 
      FIRST_VALID_STATUS, FIRST_VALID_TIME, FIRST_VALID_USER, 
      FIRST_VALID_COMMENTS, `SOURCE`, ADD_TIME, 
      CREATOR, MOD_TIME, UPDATER, 
      IS_OUT_AFTER, INVOICE_SEND_STATUS, INVOICE_ARRIVAL_STATUS, 
      UPDATE_DATA_TIME, IS_LIGHTNING, CREATE_TYPE, 
      CREATE_FRONT_END_USER, CLOSE_FRONT_END_MOBILE, 
      VERIFIES_NOT_PASS_REASON, HANDLE_STATUS, INVOICE_REFUND_STATUS, 
      AMOUNT_REFUND_STATUS, AMOUNT_COLLECTION_STATUS, 
      AMOUNT_PAY_STATUS, INVOICE_MAKEOUT_STATUS, 
      IS_NEW, DELIVERY_DIRECT_AFTER_SALES_ID)
    values (#{afterSalesNo,jdbcType=VARCHAR}, #{companyId,jdbcType=INTEGER}, #{subjectType,jdbcType=INTEGER}, 
      #{type,jdbcType=INTEGER}, #{orderId,jdbcType=INTEGER}, #{orderNo,jdbcType=VARCHAR}, 
      #{serviceUserId,jdbcType=INTEGER}, #{validStatus,jdbcType=BOOLEAN}, #{validTime,jdbcType=BIGINT}, 
      #{status,jdbcType=BOOLEAN}, #{atferSalesStatus,jdbcType=BOOLEAN}, #{atferSalesStatusReson,jdbcType=INTEGER}, 
      #{atferSalesStatusUser,jdbcType=INTEGER}, #{atferSalesStatusComments,jdbcType=VARCHAR}, 
      #{firstValidStatus,jdbcType=BOOLEAN}, #{firstValidTime,jdbcType=BIGINT}, #{firstValidUser,jdbcType=INTEGER}, 
      #{firstValidComments,jdbcType=VARCHAR}, #{source,jdbcType=BOOLEAN}, #{addTime,jdbcType=BIGINT}, 
      #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}, 
      #{isOutAfter,jdbcType=BOOLEAN}, #{invoiceSendStatus,jdbcType=BOOLEAN}, #{invoiceArrivalStatus,jdbcType=BOOLEAN}, 
      #{updateDataTime,jdbcType=TIMESTAMP}, #{isLightning,jdbcType=BOOLEAN}, #{createType,jdbcType=TINYINT}, 
      #{createFrontEndUser,jdbcType=VARCHAR}, #{closeFrontEndMobile,jdbcType=VARCHAR}, 
      #{verifiesNotPassReason,jdbcType=VARCHAR}, #{handleStatus,jdbcType=BOOLEAN}, #{invoiceRefundStatus,jdbcType=BOOLEAN}, 
      #{amountRefundStatus,jdbcType=BOOLEAN}, #{amountCollectionStatus,jdbcType=BOOLEAN}, 
      #{amountPayStatus,jdbcType=BOOLEAN}, #{invoiceMakeoutStatus,jdbcType=BOOLEAN}, 
      #{isNew,jdbcType=BOOLEAN}, #{deliveryDirectAfterSalesId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="AFTER_SALES_ID" keyProperty="afterSalesId" parameterType="com.vedeng.erp.aftersale.domain.entity.BuyOrderAfterSalesEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterSalesNo != null and afterSalesNo != ''">
        AFTER_SALES_NO,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="subjectType != null">
        SUBJECT_TYPE,
      </if>
      <if test="type != null">
        `TYPE`,
      </if>
      <if test="orderId != null">
        ORDER_ID,
      </if>
      <if test="orderNo != null and orderNo != ''">
        ORDER_NO,
      </if>
      <if test="serviceUserId != null">
        SERVICE_USER_ID,
      </if>
      <if test="validStatus != null">
        VALID_STATUS,
      </if>
      <if test="validTime != null">
        VALID_TIME,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
      <if test="atferSalesStatus != null">
        ATFER_SALES_STATUS,
      </if>
      <if test="atferSalesStatusReson != null">
        ATFER_SALES_STATUS_RESON,
      </if>
      <if test="atferSalesStatusUser != null">
        ATFER_SALES_STATUS_USER,
      </if>
      <if test="atferSalesStatusComments != null and atferSalesStatusComments != ''">
        ATFER_SALES_STATUS_COMMENTS,
      </if>
      <if test="firstValidStatus != null">
        FIRST_VALID_STATUS,
      </if>
      <if test="firstValidTime != null">
        FIRST_VALID_TIME,
      </if>
      <if test="firstValidUser != null">
        FIRST_VALID_USER,
      </if>
      <if test="firstValidComments != null and firstValidComments != ''">
        FIRST_VALID_COMMENTS,
      </if>
      <if test="source != null">
        `SOURCE`,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="isOutAfter != null">
        IS_OUT_AFTER,
      </if>
      <if test="invoiceSendStatus != null">
        INVOICE_SEND_STATUS,
      </if>
      <if test="invoiceArrivalStatus != null">
        INVOICE_ARRIVAL_STATUS,
      </if>
      <if test="updateDataTime != null">
        UPDATE_DATA_TIME,
      </if>
      <if test="isLightning != null">
        IS_LIGHTNING,
      </if>
      <if test="createType != null">
        CREATE_TYPE,
      </if>
      <if test="createFrontEndUser != null and createFrontEndUser != ''">
        CREATE_FRONT_END_USER,
      </if>
      <if test="closeFrontEndMobile != null and closeFrontEndMobile != ''">
        CLOSE_FRONT_END_MOBILE,
      </if>
      <if test="verifiesNotPassReason != null and verifiesNotPassReason != ''">
        VERIFIES_NOT_PASS_REASON,
      </if>
      <if test="handleStatus != null">
        HANDLE_STATUS,
      </if>
      <if test="invoiceRefundStatus != null">
        INVOICE_REFUND_STATUS,
      </if>
      <if test="amountRefundStatus != null">
        AMOUNT_REFUND_STATUS,
      </if>
      <if test="amountCollectionStatus != null">
        AMOUNT_COLLECTION_STATUS,
      </if>
      <if test="amountPayStatus != null">
        AMOUNT_PAY_STATUS,
      </if>
      <if test="invoiceMakeoutStatus != null">
        INVOICE_MAKEOUT_STATUS,
      </if>
      <if test="isNew != null">
        IS_NEW,
      </if>
      <if test="deliveryDirectAfterSalesId != null">
        DELIVERY_DIRECT_AFTER_SALES_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="afterSalesNo != null and afterSalesNo != ''">
        #{afterSalesNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="subjectType != null">
        #{subjectType,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null and orderNo != ''">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="serviceUserId != null">
        #{serviceUserId,jdbcType=INTEGER},
      </if>
      <if test="validStatus != null">
        #{validStatus,jdbcType=BOOLEAN},
      </if>
      <if test="validTime != null">
        #{validTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=BOOLEAN},
      </if>
      <if test="atferSalesStatus != null">
        #{atferSalesStatus,jdbcType=BOOLEAN},
      </if>
      <if test="atferSalesStatusReson != null">
        #{atferSalesStatusReson,jdbcType=INTEGER},
      </if>
      <if test="atferSalesStatusUser != null">
        #{atferSalesStatusUser,jdbcType=INTEGER},
      </if>
      <if test="atferSalesStatusComments != null and atferSalesStatusComments != ''">
        #{atferSalesStatusComments,jdbcType=VARCHAR},
      </if>
      <if test="firstValidStatus != null">
        #{firstValidStatus,jdbcType=BOOLEAN},
      </if>
      <if test="firstValidTime != null">
        #{firstValidTime,jdbcType=BIGINT},
      </if>
      <if test="firstValidUser != null">
        #{firstValidUser,jdbcType=INTEGER},
      </if>
      <if test="firstValidComments != null and firstValidComments != ''">
        #{firstValidComments,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="isOutAfter != null">
        #{isOutAfter,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceSendStatus != null">
        #{invoiceSendStatus,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceArrivalStatus != null">
        #{invoiceArrivalStatus,jdbcType=BOOLEAN},
      </if>
      <if test="updateDataTime != null">
        #{updateDataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isLightning != null">
        #{isLightning,jdbcType=BOOLEAN},
      </if>
      <if test="createType != null">
        #{createType,jdbcType=TINYINT},
      </if>
      <if test="createFrontEndUser != null and createFrontEndUser != ''">
        #{createFrontEndUser,jdbcType=VARCHAR},
      </if>
      <if test="closeFrontEndMobile != null and closeFrontEndMobile != ''">
        #{closeFrontEndMobile,jdbcType=VARCHAR},
      </if>
      <if test="verifiesNotPassReason != null and verifiesNotPassReason != ''">
        #{verifiesNotPassReason,jdbcType=VARCHAR},
      </if>
      <if test="handleStatus != null">
        #{handleStatus,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceRefundStatus != null">
        #{invoiceRefundStatus,jdbcType=BOOLEAN},
      </if>
      <if test="amountRefundStatus != null">
        #{amountRefundStatus,jdbcType=BOOLEAN},
      </if>
      <if test="amountCollectionStatus != null">
        #{amountCollectionStatus,jdbcType=BOOLEAN},
      </if>
      <if test="amountPayStatus != null">
        #{amountPayStatus,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceMakeoutStatus != null">
        #{invoiceMakeoutStatus,jdbcType=BOOLEAN},
      </if>
      <if test="isNew != null">
        #{isNew,jdbcType=BOOLEAN},
      </if>
      <if test="deliveryDirectAfterSalesId != null">
        #{deliveryDirectAfterSalesId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.aftersale.domain.entity.BuyOrderAfterSalesEntity">
    <!--@mbg.generated-->
    update T_AFTER_SALES
    <set>
      <if test="afterSalesNo != null and afterSalesNo != ''">
        AFTER_SALES_NO = #{afterSalesNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="subjectType != null">
        SUBJECT_TYPE = #{subjectType,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `TYPE` = #{type,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        ORDER_ID = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null and orderNo != ''">
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="serviceUserId != null">
        SERVICE_USER_ID = #{serviceUserId,jdbcType=INTEGER},
      </if>
      <if test="validStatus != null">
        VALID_STATUS = #{validStatus,jdbcType=BOOLEAN},
      </if>
      <if test="validTime != null">
        VALID_TIME = #{validTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        `STATUS` = #{status,jdbcType=BOOLEAN},
      </if>
      <if test="atferSalesStatus != null">
        ATFER_SALES_STATUS = #{atferSalesStatus,jdbcType=BOOLEAN},
      </if>
      <if test="atferSalesStatusReson != null">
        ATFER_SALES_STATUS_RESON = #{atferSalesStatusReson,jdbcType=INTEGER},
      </if>
      <if test="atferSalesStatusUser != null">
        ATFER_SALES_STATUS_USER = #{atferSalesStatusUser,jdbcType=INTEGER},
      </if>
      <if test="atferSalesStatusComments != null and atferSalesStatusComments != ''">
        ATFER_SALES_STATUS_COMMENTS = #{atferSalesStatusComments,jdbcType=VARCHAR},
      </if>
      <if test="firstValidStatus != null">
        FIRST_VALID_STATUS = #{firstValidStatus,jdbcType=BOOLEAN},
      </if>
      <if test="firstValidTime != null">
        FIRST_VALID_TIME = #{firstValidTime,jdbcType=BIGINT},
      </if>
      <if test="firstValidUser != null">
        FIRST_VALID_USER = #{firstValidUser,jdbcType=INTEGER},
      </if>
      <if test="firstValidComments != null and firstValidComments != ''">
        FIRST_VALID_COMMENTS = #{firstValidComments,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        `SOURCE` = #{source,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="isOutAfter != null">
        IS_OUT_AFTER = #{isOutAfter,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceSendStatus != null">
        INVOICE_SEND_STATUS = #{invoiceSendStatus,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceArrivalStatus != null">
        INVOICE_ARRIVAL_STATUS = #{invoiceArrivalStatus,jdbcType=BOOLEAN},
      </if>
      <if test="updateDataTime != null">
        UPDATE_DATA_TIME = #{updateDataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isLightning != null">
        IS_LIGHTNING = #{isLightning,jdbcType=BOOLEAN},
      </if>
      <if test="createType != null">
        CREATE_TYPE = #{createType,jdbcType=TINYINT},
      </if>
      <if test="createFrontEndUser != null and createFrontEndUser != ''">
        CREATE_FRONT_END_USER = #{createFrontEndUser,jdbcType=VARCHAR},
      </if>
      <if test="closeFrontEndMobile != null and closeFrontEndMobile != ''">
        CLOSE_FRONT_END_MOBILE = #{closeFrontEndMobile,jdbcType=VARCHAR},
      </if>
      <if test="verifiesNotPassReason != null and verifiesNotPassReason != ''">
        VERIFIES_NOT_PASS_REASON = #{verifiesNotPassReason,jdbcType=VARCHAR},
      </if>
      <if test="handleStatus != null">
        HANDLE_STATUS = #{handleStatus,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceRefundStatus != null">
        INVOICE_REFUND_STATUS = #{invoiceRefundStatus,jdbcType=BOOLEAN},
      </if>
      <if test="amountRefundStatus != null">
        AMOUNT_REFUND_STATUS = #{amountRefundStatus,jdbcType=BOOLEAN},
      </if>
      <if test="amountCollectionStatus != null">
        AMOUNT_COLLECTION_STATUS = #{amountCollectionStatus,jdbcType=BOOLEAN},
      </if>
      <if test="amountPayStatus != null">
        AMOUNT_PAY_STATUS = #{amountPayStatus,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceMakeoutStatus != null">
        INVOICE_MAKEOUT_STATUS = #{invoiceMakeoutStatus,jdbcType=BOOLEAN},
      </if>
      <if test="isNew != null">
        IS_NEW = #{isNew,jdbcType=BOOLEAN},
      </if>
      <if test="deliveryDirectAfterSalesId != null">
        DELIVERY_DIRECT_AFTER_SALES_ID = #{deliveryDirectAfterSalesId,jdbcType=INTEGER},
      </if>
    </set>
    where AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.aftersale.domain.entity.BuyOrderAfterSalesEntity">
    <!--@mbg.generated-->
    update T_AFTER_SALES
    set AFTER_SALES_NO = #{afterSalesNo,jdbcType=VARCHAR},
      COMPANY_ID = #{companyId,jdbcType=INTEGER},
      SUBJECT_TYPE = #{subjectType,jdbcType=INTEGER},
      `TYPE` = #{type,jdbcType=INTEGER},
      ORDER_ID = #{orderId,jdbcType=INTEGER},
      ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      SERVICE_USER_ID = #{serviceUserId,jdbcType=INTEGER},
      VALID_STATUS = #{validStatus,jdbcType=BOOLEAN},
      VALID_TIME = #{validTime,jdbcType=BIGINT},
      `STATUS` = #{status,jdbcType=BOOLEAN},
      ATFER_SALES_STATUS = #{atferSalesStatus,jdbcType=BOOLEAN},
      ATFER_SALES_STATUS_RESON = #{atferSalesStatusReson,jdbcType=INTEGER},
      ATFER_SALES_STATUS_USER = #{atferSalesStatusUser,jdbcType=INTEGER},
      ATFER_SALES_STATUS_COMMENTS = #{atferSalesStatusComments,jdbcType=VARCHAR},
      FIRST_VALID_STATUS = #{firstValidStatus,jdbcType=BOOLEAN},
      FIRST_VALID_TIME = #{firstValidTime,jdbcType=BIGINT},
      FIRST_VALID_USER = #{firstValidUser,jdbcType=INTEGER},
      FIRST_VALID_COMMENTS = #{firstValidComments,jdbcType=VARCHAR},
      `SOURCE` = #{source,jdbcType=BOOLEAN},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      IS_OUT_AFTER = #{isOutAfter,jdbcType=BOOLEAN},
      INVOICE_SEND_STATUS = #{invoiceSendStatus,jdbcType=BOOLEAN},
      INVOICE_ARRIVAL_STATUS = #{invoiceArrivalStatus,jdbcType=BOOLEAN},
      UPDATE_DATA_TIME = #{updateDataTime,jdbcType=TIMESTAMP},
      IS_LIGHTNING = #{isLightning,jdbcType=BOOLEAN},
      CREATE_TYPE = #{createType,jdbcType=TINYINT},
      CREATE_FRONT_END_USER = #{createFrontEndUser,jdbcType=VARCHAR},
      CLOSE_FRONT_END_MOBILE = #{closeFrontEndMobile,jdbcType=VARCHAR},
      VERIFIES_NOT_PASS_REASON = #{verifiesNotPassReason,jdbcType=VARCHAR},
      HANDLE_STATUS = #{handleStatus,jdbcType=BOOLEAN},
      INVOICE_REFUND_STATUS = #{invoiceRefundStatus,jdbcType=BOOLEAN},
      AMOUNT_REFUND_STATUS = #{amountRefundStatus,jdbcType=BOOLEAN},
      AMOUNT_COLLECTION_STATUS = #{amountCollectionStatus,jdbcType=BOOLEAN},
      AMOUNT_PAY_STATUS = #{amountPayStatus,jdbcType=BOOLEAN},
      INVOICE_MAKEOUT_STATUS = #{invoiceMakeoutStatus,jdbcType=BOOLEAN},
      IS_NEW = #{isNew,jdbcType=BOOLEAN},
      DELIVERY_DIRECT_AFTER_SALES_ID = #{deliveryDirectAfterSalesId,jdbcType=INTEGER}
    where AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_AFTER_SALES
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="AFTER_SALES_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.afterSalesNo != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.afterSalesNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="COMPANY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.companyId != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.companyId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="SUBJECT_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.subjectType != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.subjectType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="`TYPE` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.type != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.type,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ORDER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderId != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.orderId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ORDER_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderNo != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.orderNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SERVICE_USER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.serviceUserId != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.serviceUserId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="VALID_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.validStatus != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.validStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="VALID_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.validTime != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.validTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`STATUS` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.status,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="ATFER_SALES_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.atferSalesStatus != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.atferSalesStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="ATFER_SALES_STATUS_RESON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.atferSalesStatusReson != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.atferSalesStatusReson,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ATFER_SALES_STATUS_USER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.atferSalesStatusUser != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.atferSalesStatusUser,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ATFER_SALES_STATUS_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.atferSalesStatusComments != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.atferSalesStatusComments,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FIRST_VALID_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.firstValidStatus != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.firstValidStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="FIRST_VALID_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.firstValidTime != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.firstValidTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="FIRST_VALID_USER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.firstValidUser != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.firstValidUser,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="FIRST_VALID_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.firstValidComments != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.firstValidComments,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`SOURCE` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.source != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.source,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.addTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.modTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_OUT_AFTER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isOutAfter != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.isOutAfter,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_SEND_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceSendStatus != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.invoiceSendStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_ARRIVAL_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceArrivalStatus != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.invoiceArrivalStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATE_DATA_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateDataTime != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.updateDataTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_LIGHTNING = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isLightning != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.isLightning,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATE_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createType != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.createType,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATE_FRONT_END_USER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createFrontEndUser != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.createFrontEndUser,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="CLOSE_FRONT_END_MOBILE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.closeFrontEndMobile != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.closeFrontEndMobile,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="VERIFIES_NOT_PASS_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.verifiesNotPassReason != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.verifiesNotPassReason,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="HANDLE_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.handleStatus != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.handleStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_REFUND_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceRefundStatus != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.invoiceRefundStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="AMOUNT_REFUND_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.amountRefundStatus != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.amountRefundStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="AMOUNT_COLLECTION_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.amountCollectionStatus != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.amountCollectionStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="AMOUNT_PAY_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.amountPayStatus != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.amountPayStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_MAKEOUT_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceMakeoutStatus != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.invoiceMakeoutStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_NEW = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isNew != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.isNew,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="DELIVERY_DIRECT_AFTER_SALES_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deliveryDirectAfterSalesId != null">
            when AFTER_SALES_ID = #{item.afterSalesId,jdbcType=INTEGER} then #{item.deliveryDirectAfterSalesId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
    </trim>
    where AFTER_SALES_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.afterSalesId,jdbcType=INTEGER}
    </foreach>
  </update>

<!--auto generated by MybatisCodeHelper on 2023-12-12-->
  <select id="findByOrderId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_AFTER_SALES
    where VALID_STATUS = 1 -- 生效
    and ATFER_SALES_STATUS in (1,2) -- 售后订单状态：0待确认（默认）、1进行中、2已完结、3已关闭'
    and ORDER_ID=#{orderId,jdbcType=INTEGER}
  </select>
</mapper>