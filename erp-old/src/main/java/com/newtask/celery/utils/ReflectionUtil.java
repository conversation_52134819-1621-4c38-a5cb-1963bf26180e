package com.newtask.celery.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.newtask.celery.exception.CeleryInvokeException;
import com.vedeng.common.shiro.SpringContextHolder;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.springframework.aop.support.AopUtils;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class ReflectionUtil {

    /**
     * 反射调用spring bean方法的入口
     *
     * @param beanId     beanId
     * @param methodName 方法名
     * @param argsArray  实际参数(字节数组)
     * @throws Exception
     */
    public void invokeService(String beanId, String methodName, byte[] argsArray) throws Exception {
        if (!SpringContextHolder.containsBean(beanId)) {
            throw new CeleryInvokeException("Spring找不到对应的Bean");
        }

        // 从Spring中获取代理对象（可能被JDK或者CGLIB代理）
        Object proxyObject = SpringContextHolder.getBean(beanId);

        // 获取代理对象执行的方法
        Method method = getMethod(proxyObject.getClass(), methodName);

        // 获取参数
        Object[] args = null;
        if (argsArray != null && argsArray.length > 0) {
            args = SerializationUtils.deserialize(argsArray);
        }

        if (method == null) {
            throw new CeleryInvokeException(String.format("没有找到%s方法", methodName));
        }

        // 执行方法
        method.invoke(proxyObject, args);
    }


    /**
     * 获取目标方法
     *
     * @param proxyObject
     * @param methodStr
     * @return
     */
    private Method getMethod(Class proxyObject, String methodStr) {
        Method[] methods = proxyObject.getMethods();

        for (Method method : methods) {
            if (method.getName().equalsIgnoreCase(methodStr)) {
                return method;
            }
        }

        return null;
    }

}