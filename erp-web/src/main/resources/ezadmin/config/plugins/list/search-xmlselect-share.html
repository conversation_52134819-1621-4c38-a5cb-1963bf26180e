<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>搜索-多选下拉</title>
</head>
<body id="xmselectshard"  alias="xm-select-share" type="search">
<div    th:style="${custom_style}"  th:ITEM_NAME="${itemName}"  th:ITEM_ID="${itemName}"    >
    <label   class="layui-form-label" th:for="${'itemName-' + itemName}"  th:text="${itemLabel}"   th:title="${itemLabel}">普通文本</label>
    <div th:class="${#strings.isEmpty(itemParamValue)?'layui-input-inline':'layui-input-inline layui-border-blue' } "  >
        <div th:itemPlaceholder="${itemPlaceholder}" th:itemsJson="${itemsJson}" class="ez-xmselect-share"  th:id="${'itemName-' + itemName}" th:name="${itemName}" th:value="${#strings.isEmpty(itemParamValue)?'[]':'['+itemParamValue+']'}" ></div>
    </div>
</div>
</body>
</html>