package com.wms.service.stockallocation;

import com.alibaba.fastjson.JSON;
import com.vedeng.logistics.dao.WarehouseGoodsOperateLogMapper;
import com.vedeng.logistics.model.WarehouseStock;
import com.vedeng.logistics.service.WarehouseStockService;
import com.wms.constant.LogicalEnum;
import com.wms.dto.StockAllocationRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 采购相关的库存分配策略
 */
@Service
public class PurchaseStockAllocationStrategy  extends AbstractStockAllocationStrategy{

    private static final Logger LOGGER = LoggerFactory.getLogger(PurchaseStockAllocationStrategy.class);

    @Resource
    private WarehouseStockService warehouseStockService;

    @Resource
    private WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

    private List<Integer> stockAllocationOrder = Arrays.asList(
            LogicalEnum.BHG.getLogicalWarehouseId(),
            LogicalEnum.D.getLogicalWarehouseId(),
            LogicalEnum.C.getLogicalWarehouseId(),
            LogicalEnum.B.getLogicalWarehouseId(),
            LogicalEnum.CJXQ.getLogicalWarehouseId(),
            LogicalEnum.JXQ.getLogicalWarehouseId(),
            LogicalEnum.DCL.getLogicalWarehouseId(),
            LogicalEnum.DJC.getLogicalWarehouseId(),
            LogicalEnum.HG.getLogicalWarehouseId()
    );


    @Override
    protected List<Integer> getStockAllocationOrder() {
        return stockAllocationOrder;
    }

    @Override
    protected Map<Integer, WarehouseStock> getLogicalStockMapInfo(StockAllocationRequest stockAllocationRequest) {

        List<String> skuNoList = Arrays.asList(stockAllocationRequest.getSku());

        List<WarehouseStock> warehouseStockList = warehouseStockService.getLogicalStockInfo(skuNoList).get(stockAllocationRequest.getSku());

        LOGGER.info("从库存服务获取到的库存信息:" + JSON.toJSONString(warehouseStockList));

        Map<Integer, WarehouseStock> resultMap = new HashMap<>();

        stockAllocationOrder.stream().forEach(logicalWarehouseId ->{

            WarehouseStock warehouseStock = new WarehouseStock();
            warehouseStock.setLogicalWarehouseId(logicalWarehouseId);
            warehouseStock.setSku(stockAllocationRequest.getSku());

            WarehouseStock stock = warehouseStockList
                    .stream()
                    .filter(wc -> {
                        return wc.getLogicalWarehouseId().equals(logicalWarehouseId);
                    })
                    .findFirst()
                    .orElse(null);

            warehouseStock.setAvailableStockNum(stock == null ? 0 : stock.getAvailableStockNum());

            resultMap.put(logicalWarehouseId,warehouseStock);
        });

        return resultMap;
    }
}
