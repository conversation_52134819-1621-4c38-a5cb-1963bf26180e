package com.newtask.ddi.dao.generate;

import com.newtask.ddi.model.generate.TDdiGeOrder;
import org.springframework.stereotype.Repository;

@Repository
public interface TDdiGeOrderMapper {
    int deleteByPrimaryKey(Integer tDdiSaleorderId);

    int insert(TDdiGeOrder record);

    int insertSelective(TDdiGeOrder record);

    TDdiGeOrder selectByPrimaryKey(Integer tDdiSaleorderId);

    int updateByPrimaryKeySelective(TDdiGeOrder record);

    int updateByPrimaryKey(TDdiGeOrder record);
}