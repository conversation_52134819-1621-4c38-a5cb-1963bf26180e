package com.vedeng.finance.model;

import lombok.Data;

/**
 * @description: 继承Invoice实体类，用于接收时间字符串
 * @return: No such property: code for class: Script1
 * @author: Suqin
 * @date: 2022/12/5 14:25
 **/
@Data
public class InvoiceTimeDto extends Invoice{
    // 原始认证时间
    private String authTimeStr;

    // 原始申请时间
    private String addTimeStr;

    // 原始审核时间
    private String validTimeStr;

    // 修改的申请时间
    private String addTimeLabel;

    // 修改的审核时间
    private String validTimeLabel;

    // 修改的认证时间
    private String authTimeLabel;

    // 金蝶凭证号
    private String voucherNo;
}
