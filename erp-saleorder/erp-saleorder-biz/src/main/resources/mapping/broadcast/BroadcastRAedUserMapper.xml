<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.broadcast.mapdao.BroadcastRAedUserMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.erp.broadcast.domain.entity.BroadcastRAedUserEntity">
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="ERP_USER_ID" jdbcType="INTEGER" property="erpUserId"/>
        <result column="AED_USER_ID" jdbcType="INTEGER" property="aedUserId"/>
        <result column="IS_DELETED" jdbcType="TINYINT" property="isDeleted"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID, ERP_USER_ID, AED_USER_ID, IS_DELETED, ADD_TIME, MOD_TIME, CREATOR, UPDATER
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_BROADCAST_R_AED_USER
        where ID = #{id,jdbcType=INTEGER}
        and IS_DELETED = 0
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        update T_BROADCAST_R_AED_USER
        set IS_DELETED = 1, MOD_TIME = now()
        where ID = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastRAedUserEntity">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into T_BROADCAST_R_AED_USER (ERP_USER_ID, AED_USER_ID,
        IS_DELETED, ADD_TIME, MOD_TIME,
        CREATOR, UPDATER)
        values (#{erpUserId,jdbcType=INTEGER}, #{aedUserId,jdbcType=INTEGER},
        #{isDeleted,jdbcType=TINYINT}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP},
        #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER})
    </insert>

    <insert id="insertSelective" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastRAedUserEntity">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into T_BROADCAST_R_AED_USER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="erpUserId != null">
                ERP_USER_ID,
            </if>
            <if test="aedUserId != null">
                AED_USER_ID,
            </if>
            <if test="isDeleted != null">
                IS_DELETED,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="erpUserId != null">
                #{erpUserId,jdbcType=INTEGER},
            </if>
            <if test="aedUserId != null">
                #{aedUserId,jdbcType=INTEGER},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastRAedUserEntity">
        update T_BROADCAST_R_AED_USER
        <set>
            <if test="erpUserId != null">
                ERP_USER_ID = #{erpUserId,jdbcType=INTEGER},
            </if>
            <if test="aedUserId != null">
                AED_USER_ID = #{aedUserId,jdbcType=INTEGER},
            </if>
            <if test="isDeleted != null">
                IS_DELETED = #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
        </set>
        where ID = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastRAedUserEntity">
        update T_BROADCAST_R_AED_USER
        set ERP_USER_ID = #{erpUserId,jdbcType=INTEGER},
        AED_USER_ID = #{aedUserId,jdbcType=INTEGER},
        IS_DELETED = #{isDeleted,jdbcType=TINYINT},
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
        CREATOR = #{creator,jdbcType=INTEGER},
        UPDATER = #{updater,jdbcType=INTEGER}
        where ID = #{id,jdbcType=INTEGER}
    </update>

    <!-- ========== 自定义查询方法 ========== -->

    <!-- 根据ERP用户ID查询记录 -->
    <select id="selectByErpUserId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_BROADCAST_R_AED_USER
        where ERP_USER_ID = #{erpUserId,jdbcType=INTEGER}
        and IS_DELETED = 0
        limit 1
    </select>

    <!-- 播报用户列表查询结果映射 -->
    <resultMap id="BroadCastUserListResultMap" type="com.vedeng.erp.broadcast.domain.dto.BroadCastUserListDto">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="erp_username" jdbcType="VARCHAR" property="erpUsername" />
        <result column="erp_real_name" jdbcType="VARCHAR" property="erpRealName" />
        <result column="aed_username" jdbcType="VARCHAR" property="aedUsername" />
        <result column="aed_real_name" jdbcType="VARCHAR" property="aedRealName" />
        <result column="creator_real_name" jdbcType="VARCHAR" property="creatorRealName" />
        <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
        <result column="erp_user_id" jdbcType="INTEGER" property="erpUserId" />
        <result column="aed_user_id" jdbcType="INTEGER" property="aedUserId" />
        <result column="creator" jdbcType="INTEGER" property="creator" />
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    </resultMap>

    <!-- AED用户列表查询结果映射 -->
    <resultMap id="BroadCastAedUserResultMap" type="com.vedeng.erp.broadcast.domain.dto.BroadCastUserDetailDto$BroadCastAedUserDto">
        <result column="aed_user_id" jdbcType="INTEGER" property="aedUserId" />
        <result column="username" jdbcType="VARCHAR" property="username" />
        <result column="real_name" jdbcType="VARCHAR" property="realName" />
    </resultMap>

    <!-- 分页查询播报用户列表 -->
    <select id="selectBroadcastUserListPage" resultMap="BroadCastUserListResultMap">
        SELECT DISTINCT
        r.ID as id,
        (SELECT u.USERNAME FROM T_USER u WHERE u.USER_ID = r.ERP_USER_ID) as erp_username,
        (SELECT ud.REAL_NAME FROM T_USER_DETAIL ud WHERE ud.USER_ID = r.ERP_USER_ID) as erp_real_name,
        (SELECT u.USERNAME FROM T_USER u WHERE u.USER_ID = r.AED_USER_ID) as aed_username,
        (SELECT ud.REAL_NAME FROM T_USER_DETAIL ud WHERE ud.USER_ID = r.AED_USER_ID) as aed_real_name,
        (SELECT ud.REAL_NAME FROM T_USER_DETAIL ud WHERE ud.USER_ID = r.CREATOR) as creator_real_name,
        r.ADD_TIME as add_time,
        r.ERP_USER_ID as erp_user_id,
        r.AED_USER_ID as aed_user_id,
        r.CREATOR as creator,
        r.IS_DELETED as is_deleted
        FROM T_BROADCAST_R_AED_USER r
        WHERE r.IS_DELETED = 0
        <if test="username != null and username != ''">
            AND (
            (SELECT u.USERNAME FROM T_USER u WHERE u.USER_ID = r.ERP_USER_ID) LIKE CONCAT('%', #{username}, '%')
            )
        </if>
        <if test="aedUserId != null">
            AND r.AED_USER_ID = #{aedUserId}
        </if>
        ORDER BY r.MOD_TIME DESC
    </select>

    <!-- 获取全量AED销售列表 -->
    <select id="selectAllAedUserList" resultMap="BroadCastAedUserResultMap">
        SELECT DISTINCT
            r.AED_USER_ID as aed_user_id,
            (SELECT u.USERNAME FROM T_USER u WHERE u.USER_ID = r.AED_USER_ID AND u.IS_DISABLED = 0) as username,
            (SELECT ud.REAL_NAME FROM T_USER_DETAIL ud WHERE ud.USER_ID = r.AED_USER_ID) as real_name
        FROM T_BROADCAST_R_AED_USER r
        WHERE r.IS_DELETED = 0
          AND EXISTS (SELECT 1 FROM T_USER u WHERE u.USER_ID = r.AED_USER_ID AND u.IS_DISABLED = 0)
        <if test="username != null and username != ''">
            AND (SELECT u.USERNAME FROM T_USER u WHERE u.USER_ID = r.AED_USER_ID AND u.IS_DISABLED = 0) LIKE CONCAT('%', #{username}, '%')
        </if>
        ORDER BY (SELECT u.USERNAME FROM T_USER u WHERE u.USER_ID = r.AED_USER_ID) ASC
    </select>
    
    <!-- 获取个人的AED销售 -->
    <select id="selectAedSingleByErpUserId" resultType="java.lang.String" parameterType="java.lang.Integer">
        select
        t.USERNAME userName
        from T_BROADCAST_R_AED_USER trau left join  T_USER t on trau.AED_USER_ID = t.USER_ID
        where ERP_USER_ID = #{queryId,jdbcType=INTEGER}
        and IS_DELETED = 0
        limit 1
    </select>
    
    <!-- 获取小组的AED销售 -->
    <select id="selectAedTeamByErpUserId" resultType="java.lang.String" parameterType="java.lang.Integer">
       SELECT DISTINCT userName from ( 
	        SELECT 
			    GROUP_CONCAT( USERNAME SEPARATOR ', ') AS userName
			FROM
			    T_USER
			WHERE
			    FIND_IN_SET(USER_ID,
			            (SELECT 
			                    GROUP_CONCAT(DISTINCT tbd.AED_USER_ID SEPARATOR ',') USER_ID
			                FROM
			                    T_BROADCAST_DEPT tbd
			                WHERE
			                    tbd.ID = #{queryId,jdbcType=INTEGER} and tbd.IS_DELETED=0))
			        AND IS_DISABLED = 0)t
    </select>
    
    <!-- 获取部门的AED销售 -->
    <select id="selectAedDeptByErpUserId" resultType="java.lang.String" parameterType="java.lang.Integer">
	    SELECT DISTINCT userName from (  
	        SELECT 
			    GROUP_CONCAT( USERNAME SEPARATOR ', ') AS userName
			FROM
			    T_USER
			WHERE
			    FIND_IN_SET(USER_ID,
			            (SELECT 
			                    GROUP_CONCAT(DISTINCT tbd.AED_USER_ID SEPARATOR ',') USER_ID
			                FROM
			                    T_BROADCAST_DEPT tbd
			                WHERE
			                    tbd.parent_id = #{queryId,jdbcType=INTEGER} and tbd.IS_DELETED=0))
			        AND IS_DISABLED = 0)t
    </select>
    
    
    
</mapper>
