<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
    <%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
    <%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
    <%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
    <%@ taglib prefix="shiro" uri="http://shiro.apache.org/tags"%>
    <%@ taglib prefix="tags" tagdir="/WEB-INF/tags"%>
    <%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
    <%@ taglib uri="http://com.vedeng.common.util/definetags" prefix="define"%>
    <%@taglib prefix="sf" uri="http://www.springframework.org/tags/form"%>

    <%@ page trimDirectiveWhitespaces="true" %>

        <%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
%>
    <c:set var="path" value="<%=basePath%>" scope="application" />
<c:forEach var="bankBilllist" items="${list}" varStatus="num">
    <c:set var="page_get_amount" value="${page_get_amount+bankBilllist.flag1!=0?bankBilllist.amt:0}"></c:set>
    <c:set var="page_pay_amount" value="${page_pay_amount+bankBilllist.flag1==0?bankBilllist.amt:0}"></c:set>
    <c:if test="${bankBilllist.bankTag == 1 || bankBilllist.bankTag eq 4 || bankBilllist.bankTag eq 5}">
            <thead>
            <tr>
                <th class="wid4">序号</th>
                <th class="wid15">交易时间</th>
                <th >对方名称</th>
                <th >收款金额</th>
                <th >对方账号</th>
                <th class="wid8">摘要</th>
                <th class="wid8">备注</th>
                <th class="wide8">商户订单号</th>
                <th >付款金额</th>
                <th class="wid12">剩余结款金额</th>
                <th class="wid30">操作</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td>${num.count}</td>
                <td>${fn:substring(bankBilllist.realTrandatetime,0,19)}</td>
                <td>${bankBilllist.accName1}</td>
                <td><c:if test="${bankBilllist.flag1 != 0}">${bankBilllist.amt}</c:if></td>
                <td>${bankBilllist.accno2}</td>
                <td>${bankBilllist.message}</td>
                <td>${bankBilllist.det}</td>
                <td>${bankBilllist.orderNo}</td>
                <td><c:if test="${bankBilllist.flag1 == 0}">${bankBilllist.amt}</c:if></td>
                <td>${bankBilllist.amt-bankBilllist.matchedAmount}</td>
                <td>
                    <input type="hidden" name="bankBillId" value="${bankBilllist.bankBillId}"/>
                    <c:choose>
                        <c:when test="${bankBilllist.bankTag eq 4 }">
                            <span class="bt-smaller bt-border-style border-blue" onclick="manualAlipayMatchInfo(this,${bankBilllist.bankBillId},${bankBilllist.bankTag});">手动匹配</span>
                            <span class="bt-smaller bt-border-style border-red J-pop-new-data"  layerParams='{"width":"600px","height":"640px","title":"忽略","link":"./addIgnore.do?bankBillId=${bankBilllist.bankBillId}&type=1&bankTag=${bankBilllist.bankTag}"}'>忽略</span>
                        </c:when>
                        <c:otherwise>
                            <c:if test="${bankBilllist.accName1 == '支付宝（中国）网络技术有限公司' || bankBilllist.accName1 == '财付通支付科技有限公司'}"><span class="bt-smaller bt-border-style border-blue J-pop-new-data"  layerParams='{"width":"500px","height":"180px","title":"批量增补提现/转移记录","link":"./bankBillBatchInit.do?bankBillId=${bankBilllist.bankBillId}&bankAccName=${bankBilllist.accName1}"}'>导入提现记录</span></c:if>
                            <c:if test="${bankBilllist.isShowBatchPay eq 1}"><span class="bt-smaller bt-border-style border-blue J-pop-new-data"  layerParams='{"width":"600px","height":"500px","title":"导入","link":"./bankBillBatchInitAndConfirm.do?bankBillId=${bankBilllist.bankBillId}&bankAccName=${bankBilllist.accName1}"}'>批量结款</span></c:if>
                            <span class="bt-smaller bt-border-style border-blue" onclick="manualMatchInfo(this,${bankBilllist.bankBillId});">手动匹配</span>
                            <span class="bt-smaller bt-border-style border-red J-pop-new-data"  layerParams='{"width":"600px","height":"640px","title":"忽略","link":"./addIgnore.do?bankBillId=${bankBilllist.bankBillId}&type=1"}'>忽略</span>
                        </c:otherwise>
                    </c:choose>

                </td>
            </tr>
            <c:if test="${bankBilllist.capitalBillDetailList != null}">
                <tr>
                    <td class="table-container" colspan="11">
                        <table class="table">
                            <thead>
                            <tr>
                                <th class="wid4">选择</th>
                                <th class="wid9">订单号</th>
                                <th class="wid15">合同名称</th>
                                <th class="wid14">生效时间</th>
                                <th class="wid10">订单实际总额</th>
                                <th class="wid10">已付款金额</th>
                                <th class="wid10">剩余帐期金额</th>
                                <th>本次拟结款金额</th>
                                <th>拟结款名称</th>
                                <th class="wid6">交易主体</th>
                                <th>备注</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <c:set var="remain_cbc_log" value="${bankBilllist.amt-bankBilllist.matchedAmount}"></c:set>
                            <c:set var="page_order_num" value="${page_order_num+bankBilllist.capitalBillDetailList.size()}"></c:set>
                            <c:forEach var="capitalBillDetail" items="${bankBilllist.capitalBillDetailList}" varStatus="cnum">
                                <c:set var="page_order_amount" value="${page_order_amount+capitalBillDetail.saleorder.totalAmount}"></c:set>
                                <c:set var="page_match_amount" value="${page_match_amount+capitalBillDetail.amount}"></c:set>
                                <c:set var="remain_saleorder" value="${capitalBillDetail.saleorder.totalAmount-capitalBillDetail.saleorder.receivedAmount}"></c:set>
                                <tr>
                                    <c:choose>
                                        <c:when test="${bankBilllist.bankTag eq 4}">
                                            <td><input class="cid_input" type="checkbox" name="checkOne" bankBillValue="${bankBilllist.bankBillId}" capitalBillValue="${capitalBillDetail.capitalBillId}" autocomplete="off"></td>
                                            <td>
                                                <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewsaleorder${capitalBillDetail.saleorder.saleorderId}","link":"./order/saleorder/view.do?saleorderId= ${capitalBillDetail.saleorder.saleorderId}","title":"订单信息"}'> ${capitalBillDetail.saleorder.saleorderNo}</a>
                                            </td>
                                            <td>${capitalBillDetail.saleorder.traderName}</td>
                                            <td><date:date value ="${capitalBillDetail.saleorder.validTime}" format="yyyy-MM-dd HH:mm:ss"/></td>
                                            <td>${capitalBillDetail.saleorder.totalAmount}</td>
                                            <td>${capitalBillDetail.saleorder.realPayAmount}</td>
                                            <td>${capitalBillDetail.saleorder.residueAmount}</td>
                                            <td>${capitalBillDetail.amount}</td>
                                            <td>${capitalBillDetail.payer}</td>
                                            <td>
                                                <c:choose>
                                                    <c:when test="${capitalBillDetail.traderSubject ==1}">对公</c:when>
                                                    <c:otherwise>对私</c:otherwise>
                                                </c:choose>
                                            </td>
                                            <td>${capitalBillDetail.comment}</td>
                                            <td><span class="bt-smaller bt-border-style border-blue" onclick="updateCapitalBill(${bankBilllist.bankBillId},${capitalBillDetail.capitalBillId},this)">确认</span></td>
                                        </c:when>
                                        <c:otherwise>
                                            <td>${cnum.count}</td>
                                            <td>
                                                <a class="J-addtitle" href="javascript:void(0);" tabTitle='{"num":"viewsaleorder${capitalBillDetail.saleorder.saleorderId}","link":"./order/saleorder/view.do?saleorderId= ${capitalBillDetail.saleorder.saleorderId}","title":"订单信息"}'> ${capitalBillDetail.saleorder.saleorderNo}</a>
                                            </td>
                                            <td>${capitalBillDetail.saleorder.traderName}</td>
                                            <td><date:date value ="${capitalBillDetail.saleorder.validTime}" format="yyyy-MM-dd HH:mm:ss"/></td>
                                            <td>${capitalBillDetail.saleorder.totalAmount}</td>
                                            <td>${capitalBillDetail.saleorder.receivedAmount}</td>
                                            <td>${capitalBillDetail.saleorder.residueAmount}</td>
                                            <!-- ******** Tomcat.hui VDERP-1447 在页面进行判断 -->
                                            <c:choose>
                                                <c:when test="${bankBilllist.accName1 eq '支付宝（中国）网络技术有限公司' or bankBilllist.accName1 eq '财付通支付科技有限公司'}">
                                                    <td><input type="text"  name="amount" value="${bankBilllist.amt}" style="text-align:center;" onchange="check_pay(this,${bankBilllist.amt-bankBilllist.matchedAmount},${capitalBillDetail.saleorder.totalAmount-capitalBillDetail.saleorder.receivedAmount},${remain_cbc_log>remain_saleorder?remain_saleorder:remain_cbc_log})"></td>
                                                </c:when>
                                                <c:otherwise>
                                                    <td><input type="text"  name="amount" value="${remain_cbc_log>remain_saleorder?remain_saleorder:remain_cbc_log}" style="text-align:center;" onchange="check_pay(this,${bankBilllist.amt-bankBilllist.matchedAmount},${capitalBillDetail.saleorder.totalAmount-capitalBillDetail.saleorder.receivedAmount},${remain_cbc_log>remain_saleorder?remain_saleorder:remain_cbc_log})"></td>
                                                </c:otherwise>
                                            </c:choose>
                                            <!-- ******** Tomcat.hui VDERP-1447 在页面进行判断 -->
                                            <td><input type="text" name="payer" value="${bankBilllist.accName1}"></td>
                                            <td>
                                                <select class="input-middle" name="traderSubject" id="">
                                                    <option value="1" <c:if test="${capitalBillDetail.saleorder.searchType ==1}">selected</c:if>>对公</option>
                                                    <option value="2" <c:if test="${capitalBillDetail.saleorder.searchType ==2}">selected</c:if>>对私</option>
                                                </select>
                                            </td>
                                            <td><input type="text" name="comments"></td>
                                            <td><span class="bt-smaller bt-border-style border-blue" onclick="salemoneyadd(${bankBilllist.bankBillId},${capitalBillDetail.saleorder.saleorderId},${capitalBillDetail.saleorder.receivedAmount},this)">确认</span></td>
                                            <c:if test="${(remain_cbc_log-remain_saleorder)>0}">
                                                <c:set var="remain_cbc_log" value="${remain_cbc_log-remain_saleorder}"></c:set>
                                            </c:if>
                                            <c:if test="${(remain_cbc_log-remain_saleorder)<=0}">
                                                <c:set var="remain_cbc_log" value="0"></c:set>
                                            </c:if>
                                        </c:otherwise>
                                    </c:choose>
                                </tr>
                            </c:forEach>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </c:if>
            </tbody>
    </c:if>
</c:forEach>
