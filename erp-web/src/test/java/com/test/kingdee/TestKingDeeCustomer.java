package com.test.kingdee;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.domain.command.KingDeeCustomerCommand;
import com.vedeng.erp.kingdee.dto.KingDeeCustomerDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeCustomerDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeCustomerMapper;
import com.vedeng.erp.kingdee.mapstruct.KingDeeCustomerCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeCustomerConvertor;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.RepoStatus;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.List;


/**
 * 金蝶  客户测试类
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring.xml", "classpath:spring-mybatis.xml"})
public class TestKingDeeCustomer {

    @Autowired
    private KingDeeCustomerCommandConvertor customerCommandConvertor;

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private KingDeeCustomerMapper customerMapper;

    @Autowired
    private KingDeeCustomerConvertor dtoConvertor;

    @Test
    public void kingDeeSaveCustomer() {

        KingDeeCustomerDto dto = new KingDeeCustomerDto();
        dto.setKingDeeBizEnums(KingDeeBizEnums.saveCustomer);
        dto.setFCustId("0");
        dto.setFCreateOrgId(101);
        dto.setFNumber(999888);
        dto.setFUseOrgId(101);
        dto.setFName("测试客户新增");
        dto.setFQzokSyyygt("所属医院");
        dto.setFQzokSsjt("所属集团");
        dto.setFQzokXym("现用名是这个");
        dto.setFQzokKhxztext("性质是什么");
        dto.setFQzokZdkhfltext("终端客户分类是什么");
        dto.setFQzokYljgfltext("医疗机构分类文本是什么");
        dto.setFQzokKhxfltext("客户细分类文本是什么");
        dto.setFQzokYydjtext("医院等级文本是什么");
        dto.setFQzokKhdj("客户等级文本是什么");
        dto.setFprovince("010");


        List<KingDeeCustomerDetailDto> FT_BD_CUSTBANK = new ArrayList<>();

        KingDeeCustomerDetailDto sonDto = new KingDeeCustomerDetailDto();
        sonDto.setFbankcode("**********");
        sonDto.setFaccountname("帐户名称");
        sonDto.setFopenbankname("开户银行");
        sonDto.setFcnaps("1234");
        FT_BD_CUSTBANK.add(sonDto);

        dto.setFT_BD_CUSTBANK(FT_BD_CUSTBANK);

        System.out.println("dto-->"+JSON.toJSON(dto));
        //转换为金蝶入参
        KingDeeCustomerCommand command = customerCommandConvertor.toCommand(dto);
        System.out.println("入参commad-->"+JSON.toJSON(command));
        //调用金蝶方法
        RepoStatus save = kingDeeBaseApi.save(new SaveExtCommand<>(command, dto.getFormId()));
        ArrayList<SuccessEntity> successEntities = save.getSuccessEntitys();

        System.out.println("金蝶返回-->"+JSON.toJSON(successEntities));

        if (CollUtil.isNotEmpty(successEntities)) {
            SuccessEntity successEntity = CollUtil.getFirst(successEntities);
            dto.setFCustId(successEntity.getId());
            //金蝶业务数据入表
            customerMapper.insertSelective(dtoConvertor.toEntity(dto));
        System.out.println("finish-->");
        }
    }

    //调用记录 - 1
    //{"fUseOrgId":{"fNumber":"101"},"f_QZOK_YYDJTEXT":"医院等级文本是什么","f_QZOK_YLJGFLTEXT":"医疗机构分类文本是什么","f_QZOK_SSJT":"所属集团","f_QZOK_KHDJ":"客户等级文本是什么","fName":"测试客户新增","f_QZOK_SYYYGT":"所属医院","f_QZOK_ZDKHFLTEXT":"终端客户分类是什么","fSaldeptId":{},"fPROVINCE":{"fNumber":"010"},"fCreateOrgId":{"fNumber":"101"},"f_QZOK_XYM":"现用名是这个","fCustTypeId":{},"f_QZOK_KHXFLTEXT":"客户细分类文本是什么","fTaxRate":{},"fCUSTID":"0","f_QZOK_KHXZTEXT":"性质是什么","fNumber":"999888","fTradingCurrid":{},"fSettleTypeId":{},"fT_BD_CUSTBANK":[{"fCNAPS":"1234","fOPENBANKNAME":"开户银行","fACCOUNTNAME":"帐户名称","fBANKCODE":"**********"}]}
    //调用金蝶接口，返回值[{"Result":{"ResponseStatus":{"IsSuccess":true,"Errors":[],"SuccessEntitys":[{"Id":322321,"Number":"999888","DIndex":0}],"SuccessMessages":[],"MsgCode":0},"Id":322321,"Number":"999888","NeedReturnData":[{}]}}]
    //金蝶返回-->[{"number":"999888","dIndex":0,"id":"322321"}]

}
