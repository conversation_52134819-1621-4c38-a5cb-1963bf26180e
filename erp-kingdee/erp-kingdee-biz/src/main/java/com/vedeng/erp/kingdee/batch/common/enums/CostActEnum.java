package com.vedeng.erp.kingdee.batch.common.enums;

import com.vedeng.common.core.exception.ServiceException;

/**
 * 成本计算标记
 */
public enum CostActEnum {

    ACT_ADD(1 , "新增"),
    ACT_UPDATE(2, "更新"),
    ACT_DELETE(4, "删除")
    ;

    CostActEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private Integer code;

    private String msg;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static String isDelete(int code){
        if(ACT_DELETE.code.equals(code)){
            return Boolean.TRUE.toString();
        }
        if (ACT_ADD.code.equals(code) || ACT_UPDATE.code.equals(code)){
            return Boolean.FALSE.toString();
        }
        throw new ServiceException("成本标记获取未匹配:"+code);
    }

}
