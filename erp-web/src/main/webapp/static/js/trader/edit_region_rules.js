$(function() {
    var cansubmit = true;
    $("#submit").click(function() {
        checkLogin();
        layer.confirm('是否保存修改?', {icon: 3, title:'提示'}, function(index){
            //do something
            layer.close(index);
            if(checkAllData()){
                if (cansubmit) {
                    cansubmit = false;
                    // $("form").attr("enctype", "multipart/form-data");
                    var regionId =  $('select[name="zone"] option:selected').val();
                    var userId =  $("#userId option:selected").val();
                    var publicCustomerRegionRulesId =  $("#publicCustomerRegionRulesId").val();
                    // var aaa = new FormData($("form")[0]);
                    // console.log(aaa)
                    $.ajax({
                        type: "POST",
                        url: "/trader/customer/doUpdate.do",
                        data: {"userId":userId,"publicCustomerRegionRulesId":publicCustomerRegionRulesId,"regionId":regionId},
                        dataType:'json',
                        success: function(data){
                            if (data.code == 1) {
                                // alert(data.data)
                                $("#message").html(data.message);
                                flag=false
                                // layer.msg('操作成功', {
                                //     icon: 1,
                                //     time: 1000 //2秒关闭（如果不配置，默认是3秒）
                                // }, function(){
                                //     //do something
                                //
                                //     $("#search").submit();
                                // });
                                // debugger

                                // layer.alert("操作成功失败");
                            } else if (data.code == 2) {
                                $("#message").html(data.data.provinceName+"-"+data.data.cityName+"-"+data.data.zoneName+"已分配给"+data.data.username);
                                flag=false;
                                // layer.msg('操作失败', {
                                //     icon: 2,
                                //     time: 1000 //2秒关闭（如果不配置，默认是3秒）
                                // }, function () {
                                //     //do something
                                //     // $("#search").submit();
                                // });
                            }else if(data.code == 0){
                                window.location.href='/trader/customer/regionRulesList.do?flag=1';
                            } else {
                                layer.msg('操作失败', {
                                    icon: 2,
                                    time: 1000 //2秒关闭（如果不配置，默认是3秒）
                                }, function(){
                                    //do something
                                    // $("#search").submit();
                                });
                            }

                        },
                        error:function(data){
                            if (data.status == 1001) {
                                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
                            }
                        }
                    });
                } else {
                    console.log("请勿重复提交");
                    return false;
                }
            }else{
                return false;
            }
        },function (index){
            layer.close(index);
            return false;
        });

    })
});
var callbackdata = function (){

}

function custom_close(){
    window.location.href='/trader/customer/regionRulesList.do';
}

$(function(){
    $("select[name='province']").change(function(){
        checkLogin();
        var regionId = $(this).val();
        if(regionId > 0){
            $.ajax({
                type : "POST",
                url : page_url+"/system/region/getregion.do",
                data :{'regionId':regionId},
                dataType : 'json',
                success : function(data) {
                    $option = "<option value='0'>请选择</option>";
                    $.each(data.listData,function(i,n){
                        $option += "<option value='"+data.listData[i]['regionId']+"'>"+data.listData[i]['regionName']+"</option>";
                    });
                    $("select[name='city'] option:gt(0)").remove();
                    $("select[name='zone'] option:gt(0)").remove();
                    $("#zone").val("0").trigger("change");
                    $("#city").val("0").trigger("change");

                    $("select[name='city']").html($option);
                    $("select[name='zone']").html("<option value='0'>请选择</option>");
                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });
        }else if(regionId==0){
            $("select[name='city'] option:gt(0)").remove();
            $("select[name='zone'] option:gt(0)").remove();
        }
        checkAllData();
    });

    $("select[name='city']").change(function(){
        checkLogin();
        var regionId = $(this).val();
        if(regionId > 0){
            $.ajax({
                type : "POST",
                url : page_url+"/system/region/getregion.do",
                data :{'regionId':regionId},
                dataType : 'json',
                success : function(data) {
                    $option = "<option value='0'>请选择</option>";
                    $.each(data.listData,function(i,n){
                        $option += "<option value='"+data.listData[i]['regionId']+"'>"+data.listData[i]['regionName']+"</option>";
                    });
                    $("select[name='zone'] option:gt(0)").remove();

                    $("select[name='zone']").html($option);
                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });
        }else if(regionId==0){
            $("select[name='zone'] option:gt(0)").remove();
        }
        checkAllData();
    });
});
function getUserData(){
    var userId =  $("#userId option:selected").val();
    // alert(userId);
    if (userId == null || userId == -1 || userId == undefined) {
        $("#u-message").html("请选择销售");
        $("#checkData").val(0);
        $("#user-message").html("");
        return;
    }
    // alert(1)
    $.ajax({
        type: "POST",
        url: "/trader/customer/getUserInfo.do",
        data:  {"userId":userId},
        dataType:'json',
        success: function(data){
            if (data.code == 0) {
                // alert(data.data)
                $("#user-message").html("归属部门："+data.data.orgName+"&nbsp;&nbsp;"+"主管："+data.data.userBossName);
                // layer.msg('操作成功', {
                //     icon: 1,
                //     time: 1000 //2秒关闭（如果不配置，默认是3秒）
                // }, function(){
                //     //do something
                //
                //     $("#search").submit();
                // });
                // debugger

                // layer.alert("操作成功失败");
            } else {
                layer.msg('操作失败', {
                    icon: 2,
                    time: 1000 //2秒关闭（如果不配置，默认是3秒）
                }, function(){
                    //do something
                    // $("#search").submit();
                });
            }

        },
        error:function(data){
            if (data.status == 1001) {
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
            }
        }
    });

    checkAllData();
}

function checkAllData(){
    var regionId =  $('select[name="zone"] option:selected').val();
    var userId =  $("#userId option:selected").val();
    var publicCustomerRegionRulesId =  $("#publicCustomerRegionRulesId").val();

    // alert(regionId);
    if (userId == null || userId == -1 || userId == undefined) {
        $("#u-message").html("请选择销售");
        $("#checkData").val(0);
        return false;
    }else {
        $("#u-message").html("");
    }
    if (regionId == null || regionId == 0 || regionId == undefined) {
        $("#message").html("请选择区域");
        $("#checkData").val(0);
        return false;
    }else {
        $("#message").html("");
    }

    var flag=true;
    // alert(1);
    $.ajax({
        type: "POST",
        url: "/trader/customer/doCheckData.do",
        data:  {"userId":userId,"publicCustomerRegionRulesId":publicCustomerRegionRulesId,"regionId":regionId},
        dataType:'json',
        async:false,
        success: function(data){
            if (data.code == 1) {
                // alert(data.data)
                $("#message").html(data.message);
                flag=false
                // layer.msg('操作成功', {
                //     icon: 1,
                //     time: 1000 //2秒关闭（如果不配置，默认是3秒）
                // }, function(){
                //     //do something
                //
                //     $("#search").submit();
                // });
                // debugger

                // layer.alert("操作成功失败");
            } else if (data.code == 2) {
                $("#message").html(data.data.provinceName+"-"+data.data.cityName+"-"+data.data.zoneName+"已分配给"+data.data.username);
                flag=false;
                // layer.msg('操作失败', {
                //     icon: 2,
                //     time: 1000 //2秒关闭（如果不配置，默认是3秒）
                // }, function () {
                //     //do something
                //     // $("#search").submit();
                // });
            }else if(data.code == 0){
                $("#message").html("")
            }

        },
        error:function(data){
            if (data.status == 1001) {
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
                flag=false;
            }
        }
    });

    // alert(flag);
    return flag;
}