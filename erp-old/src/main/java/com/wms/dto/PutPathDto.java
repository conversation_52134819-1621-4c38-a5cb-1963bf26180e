package com.wms.dto;

import java.util.List;

public class PutPathDto extends AbstractCommonDto {

    private String SOReference1;

    private String OrderNo;

    private List<PutPathDetailDto> details;


    public List<PutPathDetailDto> getDetails() {
        return details;
    }

    public String getSOReference1() {
        return SOReference1;
    }

    public void setSOReference1(String SOReference1) {
        this.SOReference1 = SOReference1;
    }

    public String getOrderNo() {
        return OrderNo;
    }

    public void setOrderNo(String orderNo) {
        OrderNo = orderNo;
    }

    public void setDetails(List<PutPathDetailDto> details) {
        this.details = details;
    }

    @Override
    public String toString() {
        return "PutPathDto{" +
                "SOReference1='" + SOReference1 + '\'' +
                ", OrderNo='" + OrderNo + '\'' +
                ", details=" + details +
                '}';
    }
}
