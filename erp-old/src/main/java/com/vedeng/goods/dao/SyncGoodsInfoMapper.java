package com.vedeng.goods.dao;

import com.vedeng.goods.model.dto.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 同步SKU信息Mapper
 * <AUTHOR>
 */
public interface SyncGoodsInfoMapper {

    /**
     * 获取同步ES的SKU信息
     *
     * @param skuIds
     * @return
     */
    List<SyncSkuInfo2EsDto> getSyncSkuInfoBySkuIds(@Param("skuIds") List<Integer> skuIds);

    /**
     * 获取有效skuID集合
     *
     * @return
     */
    List<Integer> getValidSkuIds();


    /**
     * 获取无效的SKU的集合
     * @return
     */
    List<Integer> getNotValidSkuIds();


    /**
     * skuId获取SKU属性信息
     *
     * @param skuIds
     * @return
     */
    List<SyncSkuAttrDto> getSyncSkuAttrBySkuIds(@Param("skuIds") List<Integer> skuIds);

    /**
     * skuId获取科室信息
     *
     * @param skuIds
     * @return
     */
    List<SyncSkuDeptDto> getSyncSkuDeptBySkuIds(@Param("skuIds") List<Integer> skuIds);

    /**
     * skuId获取检查项目信息
     *
     * @param skuIds
     * @return
     */
    List<SyncSkuInspectionDto> getSyncSkuInspectionBySkuIds(@Param("skuIds") List<Integer> skuIds);

    /**
     * 获取SKU  6个月销量信息
     *
     * @param skuIds
     * @return
     */
    List<SyncSkuSaleInfoDto> getSyncSkuSaleInfoRecent6MonthBySkuIds(@Param("skuIds") List<Integer> skuIds);

    /**
     * SKU分类销量信息
     *
     * @param skuIds
     * @return
     */
    List<SyncSkuCategorySaleInfoDto> getSyncSkuCategorySaleInfoBySkuIds(@Param("skuIds") List<Integer> skuIds);

    /**
     * 品牌获取skuIds
     * @param brandIds
     * @return
     */
    List<Integer> getSkuIdsByBrandIds(@Param("brandIds") List<Integer> brandIds);

    /**
     * 分类获取skuIds
     * @param baseCategoryIds
     * @return
     */
    List<Integer> getSkuIdsByBaseCategoryIds(@Param("baseCategoryIds") List<Integer> baseCategoryIds);

    /**
     * 科室获取skuIds
     * @param departmentIds
     * @return
     */
    List<Integer> getSkuIdsByDepartmentIds(@Param("departmentIds") List<Integer> departmentIds);

    /**
     * 属性ID获取skuIds
     * @param baseAttributeIds
     * @return
     */
    List<Integer> getSkuIdsByBaseAttributeIds(@Param("baseAttributeIds") List<Integer> baseAttributeIds);

    /**
     * 分类ID和等级获取所有三级分类ID
     *
     * @param baseCategoryId
     * @param level
     * @return
     */
    List<Integer> getThirdBaseCategoryIdsByIdAndLevel(@Param("baseCategoryId") Integer baseCategoryId,@Param("level") Integer level);


    /**
     * 获取场景分类
     * @param skuNos
     * @return
     */
    List<SyncSkuSceneCategoryDto> selectSkuSceneCategoryBySkuNos(@Param("skuNos") List<String> skuNos);
}