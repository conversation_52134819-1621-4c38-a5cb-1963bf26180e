package com.wms.dao;

import com.vedeng.erp.wms.dto.WmsOutputOrderExtra;
import org.apache.ibatis.annotations.Param;

public interface WmsOutputOrderExtraMapper {
    int deleteByPrimaryKey(Long outputOrderExtraId);

    int insert(WmsOutputOrderExtra record);

    int insertSelective(WmsOutputOrderExtra record);

    WmsOutputOrderExtra selectByPrimaryKey(Long outputOrderExtraId);

    int updateByPrimaryKeySelective(WmsOutputOrderExtra record);

    int updateByPrimaryKey(WmsOutputOrderExtra record);

    WmsOutputOrderExtra selectByWmsOutputOrderId(@Param("wmsOutputOrderId")Long wmsOutputOrderId);

    void deleteWmsOutputOrderExtraByOrderId(@Param("wmsOutputOrderId")Long wmsOutputOrderId);
}