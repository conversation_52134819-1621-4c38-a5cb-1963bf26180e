package com.vedeng.erp.business.mapper;

import com.vedeng.erp.business.domain.entity.BusinessChanceSupportRecord;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BusinessChanceSupportRecordMapper {
    int deleteByPrimaryKey(Long businessChanceSupportRecordId);

    int insert(BusinessChanceSupportRecord record);

    int insertOrUpdate(BusinessChanceSupportRecord record);

    int insertOrUpdateSelective(BusinessChanceSupportRecord record);

    int insertSelective(BusinessChanceSupportRecord record);

    BusinessChanceSupportRecord selectByPrimaryKey(Long businessChanceSupportRecordId);

    int updateByPrimaryKeySelective(BusinessChanceSupportRecord record);

    int updateByPrimaryKey(BusinessChanceSupportRecord record);

    int updateBatch(List<BusinessChanceSupportRecord> list);

    int updateBatchSelective(List<BusinessChanceSupportRecord> list);

    int batchInsert(@Param("list") List<BusinessChanceSupportRecord> list);

    Integer countFollowUpTodayByUserId(@Param("userId") Integer userId, @Param("currentDate") String currentDate);

    Integer countFollowUpTodayByUserIdList(@Param("userIdList") List<Integer> userIdList, @Param("currentDate") String currentDate);


    List<BusinessChanceSupportRecord> getStatisticalDataList(@Param("userId") Integer userId, @Param("startTime") String startTime, @Param("endTime") String endTime);

    List<BusinessChanceSupportRecord> selectByBusinessChanceId(@Param("businessChanceId") Integer businessChanceId);

    List<BusinessChanceSupportRecord> findByCreator(@Param("creator")Integer creator, @Param("businessChanceId") Integer businessChanceId);


}