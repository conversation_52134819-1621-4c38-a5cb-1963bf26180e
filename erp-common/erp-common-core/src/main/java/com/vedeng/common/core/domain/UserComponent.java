package com.vedeng.common.core.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 用户组件
 * @date 2024/7/30 18:53
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserComponent {

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 头像
     */
    private String aliasHeadPicture;
}
