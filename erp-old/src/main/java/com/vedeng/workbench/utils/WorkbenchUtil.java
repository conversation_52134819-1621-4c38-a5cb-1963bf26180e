package com.vedeng.workbench.utils;


import com.vedeng.common.util.HolidayUtil;

import java.sql.Timestamp;
import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

/**
 * .
 *
 * @jira: .
 * @notes: 商机工作台工具类
 * @version: 1.0.
 * @date: 2020/10/30 9:49.
 * @author: <PERSON><PERSON>.
 */
public class WorkbenchUtil extends HolidayUtil {



    /**
     * .获取某个日期的开始时间
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/6 11:35.
     * @author: <PERSON><PERSON>.
     * @param d
     * @return: java.sql.Timestamp.
     * @throws:  .
     */
    public static Timestamp getDayStartTime(Date d) {
        Calendar calendar = Calendar.getInstance();
        if (null != d)
            calendar.setTime(d);
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return new Timestamp(calendar.getTimeInMillis());
    }

    /**
     * .获取某个日期的结束时间
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/6 11:35.
     * @author: Randy.Xu.
     * @param d
     * @return: java.sql.Timestamp.
     * @throws:  .
     */
    public static Timestamp getDayEndTime(Date d) {
        Calendar calendar = Calendar.getInstance();
        if (null != d)
            calendar.setTime(d);
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH), 23, 59, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return new Timestamp(calendar.getTimeInMillis());
    }


    /**
     * .
     *
     * @param
     * @jira: .
     * @notes: .获取本周周一00:00
     * @version: 1.0.
     * @date: 2020/10/30 9:52.
     * @author: Randy.Xu.
     * @return: java.util.Date.
     * @throws: .
     */
    public static Date getThisWeekStart() {

        Date date = new Date();
        if (date == null) {
            return null;
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int dayofweek = cal.get(Calendar.DAY_OF_WEEK);
        if (dayofweek == 1) {
            dayofweek += 7;
        }
        cal.add(Calendar.DATE, 2 - dayofweek);
        return getDayStartTime(cal.getTime());
    }

    /**
     * .
     *
     * @param
     * @jira: .
     * @notes: .获取本周周日24:00
     * @version: 1.0.
     * @date: 2020/10/30 10:43.
     * @author: Randy.Xu.
     * @return: java.util.Date.
     * @throws: .
     */
    public static Date getThisWeekEnd() {
        Calendar cal = Calendar.getInstance();
        cal.setTime(getThisWeekStart());
        cal.add(Calendar.DAY_OF_WEEK, 6);
        Date weekEndSta = cal.getTime();
        return getDayEndTime(weekEndSta);
    }


    /**
     * .
     *
     * @param
     * @jira: .
     * @notes: .获取上周周一00:00
     * @version: 1.0.
     * @date: 2020/11/3 8:48.
     * @author: Randy.Xu.
     * @return: java.util.Date.
     * @throws: .
     */
    public static Date getPreWeekStart() {
        Calendar cal = Calendar.getInstance();
        cal.setTime(getThisWeekStart());
        cal.add(Calendar.DATE, -7);
        return cal.getTime();
    }

    /**
     * .
     *
     * @param
     * @jira: .
     * @notes: .获取上周周日24:00
     * @version: 1.0.
     * @date: 2020/11/3 9:07.
     * @author: Randy.Xu.
     * @return: java.util.Date.
     * @throws: .
     */
    public static Date getPreWeekEnd() {
        Calendar cal = Calendar.getInstance();
        cal.setTime(getPreWeekStart());
        cal.add(Calendar.DAY_OF_WEEK, 7);
        return cal.getTime();
    }


    /**
     * .
     *
     * @param date
     * @jira: .
     * @notes: 获取昨天
     * @version: 1.0.
     * @date: 2020/11/3 19:42.
     * @author: Randy.Xu.
     * @return: java.util.Date.
     * @throws: .
     */
    public static Date getYesterday(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_WEEK, -1);
        date = calendar.getTime();
        return date;
    }

    /**
     * 获取某给时间num天工作日后的时间
     *
     * @param date
     * @param num
     * @return
     * @throws ParseException
     */
    public static Date getWorkDateBefore(Date date, int num) throws ParseException {

        Date today = date;

        Date yesterday = null;
        int delay = 1;

        while (delay <= num) {

            yesterday = getYesterday(today);

            if (isHoliday(yesterday)) {
                today = yesterday;
                continue;
            }

            if (isWeekend(yesterday)) {
                today = yesterday;
                continue;
            }

            //当前日期+1即tomorrow,判断是否是节假日,同时要判断是否是周末,都不是则将scheduleActiveDate日期+1,直到循环num次即可
            delay++;
            today = yesterday;
        }
        return today;
    }

    /**
     * 日期是否相同
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/8 18:29.
     * @author: Randy.Xu.
     * @param d1
     * @param d2
     * @return: boolean.
     * @throws:  .
     */
    public static boolean sameDate(Date d1, Date d2) {

        if(null == d1 || null == d2)

            return false;


        Calendar cal1 = Calendar.getInstance();

        cal1.setTime(d1);

        Calendar cal2 = Calendar.getInstance();

        cal2.setTime(d2);

        return  cal1.get(0) == cal2.get(0) && cal1.get(1) == cal2.get(1) && cal1.get(6) == cal2.get(6);

    }
}
