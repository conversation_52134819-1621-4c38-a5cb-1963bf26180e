<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.crm.visitrecord.mapper.CrmVisitRecordLogMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.crm.visitrecord.domain.vo.VisitRecordLogVo">
        <id column="ID" property="id" jdbcType="INTEGER"/>
        <result column="RECORD_ID" property="recordId" jdbcType="INTEGER"/>
        <result column="OPERATION_TYPE" property="operationType" jdbcType="INTEGER"/>
        <result column="OPERATION_CONTENT" property="operationContent" jdbcType="VARCHAR"/>
        <result column="OPERATION_TIME" property="operationTime" jdbcType="TIMESTAMP"/>
        <result column="ADD_TIME" property="addTime" jdbcType="TIMESTAMP"/>
        <result column="ADD_USER_ID" property="addUserId" jdbcType="INTEGER"/>
        <result column="ADD_USER_NAME" property="addUserName" jdbcType="VARCHAR"/>
    </resultMap>

    <insert id="insert" parameterType="com.vedeng.crm.visitrecord.domain.vo.VisitRecordLogVo">
        INSERT INTO T_VISIT_RECORD_LOG_DETAIL (
        RECORD_ID, OPERATION_TYPE, OPERATION_CONTENT,
        OPERATION_TIME, ADD_TIME, ADD_USER_ID, ADD_USER_NAME
        ) VALUES (
        #{recordId}, #{operationType}, #{operationContent},
        #{operationTime}, #{addTime}, #{addUserId}, #{addUserName}
        )
    </insert>

    <select id="selectPageByRecordId" resultMap="BaseResultMap">
        SELECT
        ID, RECORD_ID, OPERATION_TYPE, OPERATION_CONTENT,
        OPERATION_TIME, ADD_TIME, ADD_USER_ID, ADD_USER_NAME
        FROM T_VISIT_RECORD_LOG_DETAIL
        WHERE RECORD_ID = #{recordId}
        ORDER BY OPERATION_TIME DESC
    </select>


    <select id="selectPageByVisitRecordId" resultType="com.vedeng.erp.system.dto.OperationLogDto">
        SELECT
            ID as "operationLogId",
            RECORD_ID as "bizId",
            OPERATION_CONTENT "logContent",
            OPERATION_TIME as "operationTime",
            ADD_TIME as "addTime",
            ADD_TIME AS "formatAddTime",
            ADD_TIME AS "modTime",
            ADD_USER_ID as "creator",
            ADD_USER_NAME as "creatorName",
            ADD_USER_ID as "updater",
            ADD_USER_NAME as "updaterName",
            userDetail.ALIAS_HEAD_PICTURE     as "aliasHeadPicture"
        FROM
            T_VISIT_RECORD_LOG_DETAIL detail
        LEFT JOIN T_USER_DETAIL  userDetail on detail.ADD_USER_ID = userDetail.USER_ID
        WHERE
            RECORD_ID = #{recordId}
        ORDER BY OPERATION_TIME DESC
    </select>
</mapper>