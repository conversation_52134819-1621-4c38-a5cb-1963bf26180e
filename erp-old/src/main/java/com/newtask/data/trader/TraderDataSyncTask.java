package com.newtask.data.trader;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.StringUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *
 **/
@Component
@JobHandler(value = "TraderDataSyncTask")
public class TraderDataSyncTask extends AbstractJobHandler {

    @Autowired
    TraderPhoneNumSync traderPhoneNumSync;

    @Autowired
    TraderAfterNumSync traderAfterNumSync;

    @Autowired
    private TraderSaleOrderSync traderSaleOrderSync;

    @Autowired
    TraderCheckStatusSync traderCheckStatusSync;

    @Autowired
    TraderAttrCheckStatusSync traderAttrCheckStatusSync;

    @Autowired
    TraderQuoteNumSync traderQuoteNumSync;

    @Autowired
    TraderReceiveTotalAmoountSync traderReceiveTotalAmoountSync;

    @Autowired
    TraderSkuAndBrandNumSync traderSkuAndBrandNumSync;

    @Autowired
    private TraderCommunicateNumSync traderCommunicateNumSync;

    @Autowired
    private TraderBussinessChanceNumSync traderBussinessChanceNumSync;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        if(StringUtil.isEmpty(param)){
            param="0";
        }
        //传递类型，在初始化的时候，只初始化某个process
//        String type="";
//        String paramArray[]=param.split("-");
//        if(paramArray.length ==3){
//            param=paramArray[1]+"-"+paramArray[2];
//            type=paramArray[0];
//        }else{
//           // return SUCCESS;
//        }
        XxlJobLogger.log("更新售后总金额 退货次数");
        //更新售后总金额 退货次数
        traderAfterNumSync.process(param);

        XxlJobLogger.log("更新 交易次数，交易金额，首次交易时间，最近交易时间");
        //更新 交易次数，交易金额，首次交易时间，最近交易时间
        traderSaleOrderSync.process(param);

        XxlJobLogger.log("更新 客户审核状态");
        //更新 客户审核状态
        traderCheckStatusSync.updateStatusProcess(param);

        XxlJobLogger.log("更新 资质审核状态");
        //更新 资质审核状态
        traderAttrCheckStatusSync.updateStatusProcess(param);

        XxlJobLogger.log("更新报价数量");
        //更新报价数量
        traderQuoteNumSync.process(param);

        XxlJobLogger.log("更新收款金额");
        //更新收款金额
        traderReceiveTotalAmoountSync.process(param);

        XxlJobLogger.log("更新sku数量和 品牌覆盖数");
        //更新sku数量和 品牌覆盖数
        traderSkuAndBrandNumSync.process(param);

        XxlJobLogger.log("更新沟通次数 上次沟通时间");
        //更新沟通次数 上次沟通时间
        traderCommunicateNumSync.process(param);

        XxlJobLogger.log("更新手机号码个数");
        //更新手机号码个数
        traderPhoneNumSync.process(param);

        XxlJobLogger.log("更新商机总数");
        //更新商机总数
        traderBussinessChanceNumSync.process(param);

        //商机总数


        //归属销售

        //归属平台


        return SUCCESS;
    }
}
