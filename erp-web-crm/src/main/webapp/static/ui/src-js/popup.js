let uiPopupVue = Vue.component('ui-popup', {
    template: `
    <transition name="popup-fade">
        <div class="ui-popup-message-box-wrapper" v-show="isShow">
            <transition name="popup-move">
                <div class="ui-popup-message-box" v-show="isShow">
                    <div class="msg-title">
                        <div class="msg-fork" @click="deleteButton">
                            <i class="vd-ui_icon icon-delete"></i>
                        </div>
                    </div>
                    <div class="msg-content">
                        <i class="vd-ui_icon"
                           :class='icon'
                        ></i>
                        <div class="msg-tip">
                            <div class="msg-tip-title" v-show='title'>{{title}}</div>
                            <div class="msg-tip-word" v-show='message' v-html="message"></div>
                        </div>
                    </div>
                    <div class="msg-button-choice">
                        <div
                            class='vd-button'
                            v-for='(item, index) in buttons'
                            :class='item.btnClass'
                            @click="handleButton(item)"
                            :key="index"
                        >{{item.txt}}</div>
                    </div>
                </div>
            </transition>
        </div>
    </transition>`,
    data() {
        return {
            isShow: false,
            type: '',
            title: '',
            message: '',
            buttons: [],
            defaultClass: '',  // 按钮默认class
            iconType: {
                'success': 'icon-yes2',
                'error': 'icon-error2',
                'warn': 'icon-caution2',
                'info': 'icon-info2'
            }
        }
    },
    computed: {
        icon() {
            return this.iconType[this.type];
        }
    },
    methods: {
        show() {
            this.isShow = true;
        },
        hide() {
            this.isShow = false;
        },
        handleButton(item) {
            this.isShow = false;
            if (item.callback) {
                item.callback();
            }
        },
        deleteButton() {
            this.isShow = false;
            this.handleDelete && this.handleDelete();
        }
    }
})

let installPopupComponents = () => {
    const VdPop = Vue.extend(uiPopupVue);
    let vm = null;

    const showPopup = (type, opt) => {
        return new Promise((resolve, reject) => {
            if (!vm) {
                vm = new VdPop();
                vm.$mount();
                document.body.appendChild(vm.$el);
            } else {
                vm.hide();
            }

            vm.type = type || vm.type;
            vm.title = opt.title || '';
            vm.message = opt.message || '';
            vm.buttons = opt.buttons || vm.buttons;
            vm.handleDelete = opt.handleDelete || vm.handleDelete;
            vm.defaultClass = opt.defaultClass || vm.defaultClass;

            vm.show();
        });
    }

    const fn = (type, opt) => {
        return Promise.resolve(showPopup(type, opt));
    };

    showPopup.success = fn.bind(null, 'success');
    showPopup.error = fn.bind(null, 'error');
    showPopup.warn = fn.bind(null, 'warn');
    showPopup.info = fn.bind(null, 'info');

    Vue.prototype.$popup = showPopup;
}

installPopupComponents();
