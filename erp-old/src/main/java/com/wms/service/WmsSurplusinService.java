package com.wms.service;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.logistics.model.outIn.OutInDetail;
import com.wms.model.dto.WmsInputOrderGoodsDto;
import com.wms.model.dto.WmsSurplusInQueryDto;
import com.wms.model.po.WmsInputOrder;
import com.wms.model.po.WmsInputOrderGoods;
import com.wms.model.po.WmsSurplusInOrder;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName WmsSurplusinService.java
 * @Description TODO 盘盈入库
 * @createTime 2020年09月21日 15:57:00
 */
public interface WmsSurplusinService {
    /**
     * @description: 列表页查询
     * @return: List<WmsSurplusInOrder>
     * @author: Strange
     * @date: 2020/9/21
     **/
    List<WmsSurplusInOrder> querySurplusInlistPage(WmsSurplusInQueryDto wmsSurplusInQueryDto, Page page);

    /**
     * @description: 保存盘盈入库单
     * @return: ResultInfo
     * @author: Strange
     * @date: 2020/9/22
     **/
    ResultInfo saveSurplusInOrder(WmsInputOrder wmsInputOrder);

    /**
     * @description: 获取盘盈单信息
     * @return: WmsInputOrder
     * @author: Strange
     * @date: 2020/9/22
     **/
    WmsInputOrder getSurplusInOrderById(Integer wmsInputOrderId);

    /**
     * @description: 更新盘盈单状态
     * @return: Integer
     * @author: Strange
     * @date: 2020/9/22
     **/
    Integer updatesurplusInOrderAuditStatus(Integer wmsInputOrderId, int value);

    /**
     * @description: 下发盘盈入库单
     * @return:
     * @author: Strange
     * @date: 2020/9/22
     **/
    void putWmsSurplusInOrder(Integer wmsInputOrderId);

    /**
     * @description: 获取盘盈入库单信息
     * @return: WmsInputOrder
     * @author: Strange
     * @date: 2020/9/22
     **/
    WmsInputOrder getSurplusInorderByNo(String surplusNo);

    /**
     * @description: 获取入库单商品信息
     * @return: List<WmsInputOrderGoods>
     * @author: Strange
     * @date: 2020/9/22
     **/
    List<WmsInputOrderGoods> getWmsInputOrderGoods(Integer wmsInputOrderId);
    /**
     * @description: 获取盘盈单商品信息
     * @return:   List<WmsInputOrderGoodsDto>
     * @author: Strange
     * @date: 2020/9/23
     **/
    List<WmsInputOrderGoodsDto> getWmsInputOrderGoodsDto(Integer wmsInputOrderId);

    /**
     * @description: 获取入库单入库记录
     * @return: List<WarehouseGoodsOperateLog>
     * @author: Strange
     * @date: 2020/9/23
     **/
    List<WarehouseGoodsOperateLog> getWarehouseLogListByOrderId(Integer wmsInputOrderId, Integer logOperateType);

    /**
     * 获取盘盈入库 入库记录
     * @param orderNo
     * @param outInType
     * @param operateType
     * @return
     */
    List<OutInDetail> getOUtInDetailList(String orderNo, Integer outInType, Integer operateType);
}
