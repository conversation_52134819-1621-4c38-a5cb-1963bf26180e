<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>联系人信息列表操作设置</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">

</head>
<body id="traderContractForm" datasource="erp-datasourcetarget"
      SUCCESS_URL="/ezadmin/form/form-traderContractForm?ID=${ID}"
>
<div class="layui-container">
    <form ID="inputForm"   method="post" class="layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label">表单初始化</label>
            <div class="layui-input-block">
                <pre id="init" class="layui-code"></pre>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">具体操作</label>
            <div class="layui-input-block">

                 <pre id="submit" class="layui-code" SUCCESS_URL="" >
                  import com.ezadmin.web.EzResult;
                  if(isNotBlank("TRADER_CONTACT_ID")){

                     if($("TYPE").equals("1")){
                        if($("IS_ENABLE").equals("1")){
                            update("update T_TRADER_CONTACT set IS_ENABLE=0,MOD_TIME=NOW() WHERE TRADER_CONTACT_ID=#{TRADER_CONTACT_ID}");
                        }
                        if($("IS_ENABLE").equals("0")){
                            update("update T_TRADER_CONTACT set IS_ENABLE=1,MOD_TIME=NOW() WHERE TRADER_CONTACT_ID=#{TRADER_CONTACT_ID}");
                        }
                     }

                     if($("TYPE").equals("2")){
                        if(isNotBlank("TRADER_ID")){
                            update("update T_TRADER_CONTACT set IS_DEFAULT=0 WHERE TRADER_TYPE= #{TRADER_TYPE} and TRADER_ID=#{TRADER_ID}");
                            update("update T_TRADER_CONTACT set IS_DEFAULT=1,MOD_TIME=NOW() WHERE TRADER_TYPE= #{TRADER_TYPE} and TRADER_ID=#{TRADER_ID} and TRADER_CONTACT_ID=#{TRADER_CONTACT_ID}");
                        }
                     }

                     if($("TYPE").equals("3")){
                        if($("IS_TOP").equals("1")){
                            update("update T_TRADER_CONTACT set IS_TOP=0,MOD_TIME=NOW() WHERE TRADER_CONTACT_ID=#{TRADER_CONTACT_ID}");
                        }
                        if($("IS_TOP").equals("0")){
                            update("update T_TRADER_CONTACT set IS_TOP=1,MOD_TIME=NOW() WHERE TRADER_CONTACT_ID=#{TRADER_CONTACT_ID}");
                        }
                     }

                  }
                  return EzResult.instance();
                 </pre>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">表单删除</label>
            <div class="layui-input-block">
                <pre id="delete" class="layui-code"></pre>
            </div>
        </div>
    </form>

    <form id="configForm" class="layui-form">
        <div class="layui-card" group_name="表单数据配置">
            <div class="layui-card-header">
                表单数据配置
            </div>
            <div class="layui-card-body">
            </div>
        </div>
        <div class="layui-form-item "><label class="layui-form-label">初始表达式</label>
            <div class="layui-input-block form-group">
                <pre class="form-item layui-code" id="init" name="INIT_EXPRESS" type=""></pre>
            </div>
        </div>
        <div class="layui-form-item ">
            <label class="layui-form-label">提交表达式</label>
            <div class="layui-input-block form-group">
      <pre class="form-item layui-code" id="submit" name="SUBMIT_EXPRESS" type="">
                  import com.ezadmin.web.EzResult;
                  if(isNotBlank("TRADER_CONTACT_ID")){

                     if($("TYPE").equals("1")){
                        if($("IS_ENABLE").equals("1")){
                            update("update T_TRADER_CONTACT set IS_ENABLE=0,MOD_TIME=NOW() WHERE TRADER_CONTACT_ID=#{TRADER_CONTACT_ID}");
                        }
                        if($("IS_ENABLE").equals("0")){
                            update("update T_TRADER_CONTACT set IS_ENABLE=1,MOD_TIME=NOW() WHERE TRADER_CONTACT_ID=#{TRADER_CONTACT_ID}");
                        }
                     }

                     if($("TYPE").equals("2")){
                        if(isNotBlank("TRADER_ID")){
                            update("update T_TRADER_CONTACT set IS_DEFAULT=0 WHERE TRADER_TYPE= #{TRADER_TYPE} and TRADER_ID=#{TRADER_ID}");
                            update("update T_TRADER_CONTACT set IS_DEFAULT=1,MOD_TIME=NOW() WHERE TRADER_TYPE= #{TRADER_TYPE} and TRADER_ID=#{TRADER_ID} and TRADER_CONTACT_ID=#{TRADER_CONTACT_ID}");
                        }
                     }

                     if($("TYPE").equals("3")){
                        if($("IS_TOP").equals("1")){
                            update("update T_TRADER_CONTACT set IS_TOP=0,MOD_TIME=NOW() WHERE TRADER_CONTACT_ID=#{TRADER_CONTACT_ID}");
                        }
                        if($("IS_TOP").equals("0")){
                            update("update T_TRADER_CONTACT set IS_TOP=1,MOD_TIME=NOW() WHERE TRADER_CONTACT_ID=#{TRADER_CONTACT_ID}");
                        }
                     }

                  }
                 return EzResult.instance();
          </pre>
            </div>
        </div>
        <div class="layui-form-item ">
            <label class="layui-form-label">删除表达式</label>
            <div class="layui-input-block form-group">
                <pre class="form-item layui-code" id="delete" name="DELETE_EXPRESS" type=""></pre>
            </div>
        </div>
        <div class="layui-form-item "><label class="layui-form-label">分组表达式</label>
            <div class="layui-input-block form-group">
                <pre class="form-item layui-code" name="GROUP_DATA" type=""></pre>
            </div>
        </div>
    </form>

</div>
</body>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function(){

    })
</script>
<!--<div id="appendFoot">-->
<!--    这是一段放在后面的自定义的html代码片段-->
<!--</div>-->
</html>