package com.vedeng.order.model.ge;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * V_BUYORDER_SNCODE_SLAVE
 * <AUTHOR>
@Data
public class VBuyorderSncodeSlave implements Serializable {
    /**
     * 采购单DDI采集配件表id
     */
    private Integer buyorderSncodeSlaveId;

    /**
     * 主表id
     */
    private Integer buyorderSncodeMasterId;

    /**
     * 配件序列号 SN码
     */
    private String slaveSncode;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 更新时间
     */
    private Date modeTime;

    /**
     * 是否删除  0否  1是
     */
    private Boolean isDelete;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新人
     */
    private Integer updater;

    private static final long serialVersionUID = 1L;

    public Integer getBuyorderSncodeSlaveId() {
        return buyorderSncodeSlaveId;
    }

    public void setBuyorderSncodeSlaveId(Integer buyorderSncodeSlaveId) {
        this.buyorderSncodeSlaveId = buyorderSncodeSlaveId;
    }

    public Integer getBuyorderSncodeMasterId() {
        return buyorderSncodeMasterId;
    }

    public void setBuyorderSncodeMasterId(Integer buyorderSncodeMasterId) {
        this.buyorderSncodeMasterId = buyorderSncodeMasterId;
    }

    public String getSlaveSncode() {
        return slaveSncode;
    }

    public void setSlaveSncode(String slaveSncode) {
        this.slaveSncode = slaveSncode;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Date getModeTime() {
        return modeTime;
    }

    public void setModeTime(Date modeTime) {
        this.modeTime = modeTime;
    }

    public Boolean getDelete() {
        return isDelete;
    }

    public void setDelete(Boolean delete) {
        isDelete = delete;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }
}