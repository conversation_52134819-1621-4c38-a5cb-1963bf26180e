package com.vedeng.erp.buyorder.service;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.vedeng.erp.buyorder.mapper.BuyorderActualSupplierMapper;
import com.vedeng.erp.buyorder.domain.entity.BuyorderActualSupplier;
@Service
public class BuyorderActualSupplierService{

    @Resource
    private BuyorderActualSupplierMapper buyorderActualSupplierMapper;

    
    public int deleteByPrimaryKey(Long buyorderActualSupplierId) {
        return buyorderActualSupplierMapper.deleteByPrimaryKey(buyorderActualSupplierId);
    }

    
    public int insert(BuyorderActualSupplier record) {
        return buyorderActualSupplierMapper.insert(record);
    }

    
    public int insertSelective(BuyorderActualSupplier record) {
        return buyorderActualSupplierMapper.insertSelective(record);
    }

    
    public BuyorderActualSupplier selectByPrimaryKey(Long buyorderActualSupplierId) {
        return buyorderActualSupplierMapper.selectByPrimaryKey(buyorderActualSupplierId);
    }

    
    public int updateByPrimaryKeySelective(BuyorderActualSupplier record) {
        return buyorderActualSupplierMapper.updateByPrimaryKeySelective(record);
    }

    
    public int updateByPrimaryKey(BuyorderActualSupplier record) {
        return buyorderActualSupplierMapper.updateByPrimaryKey(record);
    }

}
