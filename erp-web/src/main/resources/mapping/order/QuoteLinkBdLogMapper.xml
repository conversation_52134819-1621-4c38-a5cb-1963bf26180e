<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.order.dao.QuoteLinkBdLogMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.order.model.QuoteLinkBdLog" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Oct 13 10:45:00 CST 2020.
    -->
    <id column="QUOTE_LINK_BD_LOG_ID" property="quoteLinkBdLogId" jdbcType="INTEGER" />
    <result column="QUOTE_ID" property="quoteId" jdbcType="INTEGER" />
    <result column="BD_ORDER_ID" property="bdOrderId" jdbcType="INTEGER" />
    <result column="IS_ENABLE" property="isEnable" jdbcType="BIT" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="UPDATOR" property="updator" jdbcType="INTEGER" />
  </resultMap>

  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Oct 13 10:45:00 CST 2020.
    -->
    QUOTE_LINK_BD_LOG_ID, QUOTE_ID, BD_ORDER_ID, IS_ENABLE, ADD_TIME, UPDATE_TIME, CREATOR, 
    UPDATOR
  </sql>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Oct 13 10:45:00 CST 2020.
    -->
    select 
    <include refid="Base_Column_List" />
    from T_QUOTE_LINK_BD_LOG
    where QUOTE_LINK_BD_LOG_ID = #{quoteLinkBdLogId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Oct 13 10:45:00 CST 2020.
    -->
    delete from T_QUOTE_LINK_BD_LOG
    where QUOTE_LINK_BD_LOG_ID = #{quoteLinkBdLogId,jdbcType=INTEGER}
  </delete>

  <insert id="insert" parameterType="com.vedeng.order.model.QuoteLinkBdLog" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Oct 13 10:45:00 CST 2020.
    -->
    <selectKey resultType="java.lang.Integer" keyProperty="quoteLinkBdLogId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_QUOTE_LINK_BD_LOG (QUOTE_ID, BD_ORDER_ID, IS_ENABLE, 
      ADD_TIME, UPDATE_TIME, CREATOR, 
      UPDATOR)
    values (#{quoteId,jdbcType=INTEGER}, #{bdOrderId,jdbcType=INTEGER}, #{isEnable,jdbcType=BIT}, 
      #{addTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, 
      #{updator,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.order.model.QuoteLinkBdLog" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Oct 13 10:45:00 CST 2020.
    -->
    <selectKey resultType="java.lang.Integer" keyProperty="quoteLinkBdLogId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_QUOTE_LINK_BD_LOG
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="quoteId != null" >
        QUOTE_ID,
      </if>
      <if test="bdOrderId != null" >
        BD_ORDER_ID,
      </if>
      <if test="isEnable != null" >
        IS_ENABLE,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="updator != null" >
        UPDATOR,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="quoteId != null" >
        #{quoteId,jdbcType=INTEGER},
      </if>
      <if test="bdOrderId != null" >
        #{bdOrderId,jdbcType=INTEGER},
      </if>
      <if test="isEnable != null" >
        #{isEnable,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updator != null" >
        #{updator,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>


  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.order.model.QuoteLinkBdLog" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Oct 13 10:45:00 CST 2020.
    -->
    update T_QUOTE_LINK_BD_LOG
    <set >
      <if test="quoteId != null" >
        QUOTE_ID = #{quoteId,jdbcType=INTEGER},
      </if>
      <if test="bdOrderId != null" >
        BD_ORDER_ID = #{bdOrderId,jdbcType=INTEGER},
      </if>
      <if test="isEnable != null" >
        IS_ENABLE = #{isEnable,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updator != null" >
        UPDATOR = #{updator,jdbcType=INTEGER},
      </if>
    </set>
    where QUOTE_LINK_BD_LOG_ID = #{quoteLinkBdLogId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.order.model.QuoteLinkBdLog" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Oct 13 10:45:00 CST 2020.
    -->
    update T_QUOTE_LINK_BD_LOG
    set QUOTE_ID = #{quoteId,jdbcType=INTEGER},
      BD_ORDER_ID = #{bdOrderId,jdbcType=INTEGER},
      IS_ENABLE = #{isEnable,jdbcType=BIT},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATOR = #{updator,jdbcType=INTEGER}
    where QUOTE_LINK_BD_LOG_ID = #{quoteLinkBdLogId,jdbcType=INTEGER}
  </update>

  <select id="getLinkLog" resultMap="BaseResultMap" parameterType="com.vedeng.order.model.QuoteLinkBdLog">
    SELECT QUOTE_LINK_BD_LOG_ID,QUOTE_ID,BD_ORDER_ID
    FROM T_QUOTE_LINK_BD_LOG
    WHERE BD_ORDER_ID=#{bdOrderId} AND IS_ENABLE=#{isEnable}
    LIMIT 1
  </select>

  <select id="getLinkLogByQuoteId" resultType="com.vedeng.order.model.QuoteLinkBdLog">
    SELECT QUOTE_LINK_BD_LOG_ID,QUOTE_ID,BD_ORDER_ID
    FROM T_QUOTE_LINK_BD_LOG
    WHERE QUOTE_ID=#{quoteId} AND IS_ENABLE=1
    LIMIT 1
  </select>
</mapper>