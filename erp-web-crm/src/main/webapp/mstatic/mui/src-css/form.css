/* form */
.form-wrap {
  position: relative;
}
.form-fixed-bottom {
  width: 100%;
  max-width: 768px;
  min-width: 320px;
  background: #fff;
  position: fixed;
  bottom: 0;
  padding-bottom: env(safe-area-inset-bottom);
  left: 50%;
  transform: translateX(-50%);
  z-index: 99;
  border-top: solid 1px #EBEFF2;
}
.form-fixed-bottom .submit-btn {
  height: 53px;
}
.form-fixed-bottom .submit-btn.padding {
  padding: 10px;
}
.form-fixed-bottom.z-index-top {
  z-index: 9999;
}
.form-section {
  padding: 0 10px;
  margin-bottom: 10px;
}
.form-section .form-section-title {
  position: relative;
  height: 41px;
  padding: 10px 5px;
  font-size: 14px;
  font-weight: 700;
}
.form-section .form-section-title .section-title-btn {
  height: 100%;
  color: #09f;
  display: flex;
  align-items: center;
  position: absolute;
  right: 0;
  top: 0px;
}
.form-section .form-section-title .section-title-btn > i {
  font-size: 16px;
  margin-right: 5px;
}
.form-section .form-section-title .section-title-btn > span {
  font-size: 12px;
  font-weight: 400;
}
.form-card {
  background: #fff;
  border-radius: 5px;
}
.form-card.mb10 {
  margin-bottom: 10px;
}
.form-item {
  display: flex;
  border-bottom: solid 1px #F5F7FA;
}
.form-item:last-child {
  border: none;
}
.form-item .form-label {
  width: 100px;
  flex-shrink: 0;
  color: #000;
  line-height: 18px;
  white-space: nowrap;
  padding: 10px;
  padding-right: 0px;
}
.form-item .form-label.middle {
  display: flex;
  align-items: center;
}
.form-item .form-label .must {
  color: #e64545;
}
.form-item .form-fields {
  position: relative;
  flex: 1;
  min-width: 0;
  word-break: break-all;
  padding: 10px;
}
.form-item .form-fields.no-padding {
  padding: 0;
}
.form-item .form-fields .vd-ui-input-error {
  color: #E64545;
  margin-top: 5px;
  font-size: 0;
  white-space: nowrap;
}
.form-item .form-fields .vd-ui-input-error .vd-ui-input-error--icon {
  font-size: 16px;
  margin-right: 5px;
  line-height: 1;
  vertical-align: -2px;
}
.form-item .form-fields .vd-ui-input-error .vd-ui-input-error--errmsg {
  font-size: 14px;
  margin: 0px;
  display: inline-block;
}
.form-item.vertical {
  display: block;
}
.form-item.vertical .form-label {
  width: 100%;
  padding-bottom: 5px;
}
.form-item.vertical .form-fields {
  width: 100%;
  margin: 0;
}
.form-tip {
  display: flex;
  font-size: 12px;
}
.form-tip i {
  height: 16px;
  font-size: 16px;
  margin-right: 5px;
  padding-top: 2px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.form-tip.success {
  color: #13BF13;
}
.form-tip.warn {
  color: #F60;
}
.form-tip.error {
  color: #E64545;
}
.form-tip.info {
  color: #09f;
}
.form-tip.map,
.form-tip.tel {
  color: #09F;
}
.form-tip.style2 {
  padding: 10px;
}
.form-tip.style2.success {
  color: #000;
  background: #c5ffc5;
}
.form-tip.style2.success i {
  color: #13BF13;
}
.form-tip.style2.warn {
  color: #000;
  background: #ffbd91;
}
.form-tip.style2.warn i {
  color: #F60;
}
.form-tip.style2.error {
  color: #000;
  background: #FCE9E9;
}
.form-tip.style2.error i {
  color: #E64545;
}
.form-tip.style2.info {
  color: #000;
  background: #E0F3FF;
}
.form-tip.style2.info i {
  color: #09F;
}
/* 表单逻辑相关 */
.form-placeholder {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}
.form-placeholder > span {
  font-size: 12px;
  color: #ccc;
}
.form-placeholder > i {
  width: 36px;
  height: 18px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  color: #666;
}
