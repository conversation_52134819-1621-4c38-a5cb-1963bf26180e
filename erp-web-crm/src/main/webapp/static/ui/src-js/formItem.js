Vue.component('ui-form-item', {
    template: `<div class="form-item" :class="{'form-item-text': text}">
        <div class="form-label" :style="{'width': labelWidth}"><span class="must" v-if="must">*</span>{{label}}<template v-if="label">：</template></div>
        <div class="form-fields">
            <slot></slot>
        </div>
    </div>`,
    props: {
        label: {
            type: String,
            default: ''
        },
        must: {
            type: Boolean,
            default: false
        },
        text: {
            type: Boolean,
            default: false
        },
        labelWidth: {
            type: String,
            default: ''
        }
    },
    mounted(){
       
    },
    methods: {
       
    }
})