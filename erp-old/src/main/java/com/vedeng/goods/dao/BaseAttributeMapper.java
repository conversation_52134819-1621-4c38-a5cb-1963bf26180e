package com.vedeng.goods.dao;


import com.vedeng.goods.model.BaseAttribute;
import com.vedeng.goods.model.vo.BaseAttributeVo;

import javax.inject.Named;
import java.util.List;
import java.util.Map;
@Named("baseAttributeMapper")
public interface BaseAttributeMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_BASE_ATTRIBUTE
     *
     * @mbg.generated Wed May 08 13:32:46 CST 2019
     */
    int deleteByPrimaryKey(Integer baseAttributeId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_BASE_ATTRIBUTE
     *
     * @mbg.generated Wed May 08 13:32:46 CST 2019
     */
    int insert(BaseAttribute record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_BASE_ATTRIBUTE
     *
     * @mbg.generated Wed May 08 13:32:46 CST 2019
     */
    int insertSelective(BaseAttributeVo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_BASE_ATTRIBUTE
     *
     * @mbg.generated Wed May 08 13:32:46 CST 2019
     */
    BaseAttribute selectByPrimaryKey(Integer baseAttributeId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_BASE_ATTRIBUTE
     *
     * @mbg.generated Wed May 08 13:32:46 CST 2019
     */
    int updateByPrimaryKeySelective(BaseAttribute record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_BASE_ATTRIBUTE
     *
     * @mbg.generated Wed May 08 13:32:46 CST 2019
     */
    int updateByPrimaryKey(BaseAttribute record);

    /**
     * @description 属性详情
     * <AUTHOR>
     * @param
     * @date 2019/5/9
     */
    BaseAttributeVo getBaseAttributeByParam(Map<String, Object> param);

    /**
     * @description 属性列表
     * <AUTHOR>
     * @param
     * @date 2019/5/9
     */
    List<BaseAttributeVo> getBaseAttributeInfoListPage(Map<String, Object> param);

    /**
     * @description 单位信息
     * <AUTHOR>
     * @param
     * @date 2019/5/13
     */
    List<Map<String, Object>> getUnitInfoMap(Map<String, Object> paramMap);

    /**
     * @description 删除属性信息
     * <AUTHOR>
     * @param
     * @date 2019/5/15
     */
    Integer delAttribute(Map<String, Object> paramMap);

    /**
     * @description 根据分类id查询属性
     * <AUTHOR>
     * @param
     * @date 2019/5/17
     */
    List<BaseAttribute> getAttributeInfoByCategory(Integer baseCategoryId);

    /**
     * @description 检查属性是否已经存在
     * <AUTHOR>
     * @param
     * @date 2019/5/22
     */
    Integer checkAttrubuteIsExist(BaseAttributeVo baseAttributeVo);

    /**
     * @description 获取分类引用的属性列表
     * <AUTHOR>
     * @param map
     * @return
     */
    List<BaseAttributeVo> getAttributeListByCategoryId(Map<String,Object> map);

    /**
     * @description 根据属性Id列表查询已删除的属性的数目
     * <AUTHOR>
     * @param map
     * @return
     */
    Integer getDeletedAttrNumByIds(Map<String,Object> map);

    /**
     * 通过属性编号获取对应属性
     *
     * @param attIdList
     * @return
     */
    List<BaseAttribute> listAttributeByAttrIdList(List<Integer> attIdList);
}