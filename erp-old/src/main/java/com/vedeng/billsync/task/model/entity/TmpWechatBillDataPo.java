package com.vedeng.billsync.task.model.entity;

import lombok.Data;
import java.util.Date;

@Data
public class TmpWechatBillDataPo {
    /**
     * 交易时间
     */
    private Date occurrenceTime;

    /**
     * 公众账号ID
     */
    private String publicId;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 特约商户号
     */
    private String specialMerchantNo;

    /**
     * 设备号
     */
    private String deviceNo;

    /**
     * 微信订单号
     */
    private String wxOrderNo;

    /**
     * 商户订单号
     */
    private String orderNo;

    /**
     * 用户标识
     */
    private String userId;

    /**
     * 交易类型
     */
    private String businessType;

    /**
     * 交易状态
     */
    private String businessStatus;

    /**
     * 付款银行
     */
    private String bank;

    /**
     * 货币种类
     */
    private String currency;

    /**
     * 应结订单金额
     */
    private String orderAmountDue;

    /**
     * 代金券金额
     */
    private String couponAmount;

    /**
     * 微信退款单号
     */
    private String wxRefundNo;

    /**
     * 商户退款单号
     */
    private String refundNo;

    /**
     * 退款金额
     */
    private String refundAmount;

    /**
     * 充值券退款金额
     */
    private String rechargeRefundAmount;

    /**
     * 退款类型
     */
    private String refundType;

    /**
     * 退款状态
     */
    private String refundStatus;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 商户数据包
     */
    private String dataPack;

    /**
     * 手续费
     */
    private String handlingFee;

    /**
     * 费率
     */
    private String rate;

    /**
     * 订单金额
     */
    private String orderAmount;

    /**
     * 申请退款金额
     */
    private String reqRefundAmount;

}
