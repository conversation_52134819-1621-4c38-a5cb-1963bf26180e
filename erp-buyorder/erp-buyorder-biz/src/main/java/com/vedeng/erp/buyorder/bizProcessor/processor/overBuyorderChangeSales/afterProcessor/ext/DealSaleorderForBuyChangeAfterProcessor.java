package com.vedeng.erp.buyorder.bizProcessor.processor.overBuyorderChangeSales.afterProcessor.ext;

import com.vedeng.erp.buyorder.bizProcessor.dto.BizDto;
import com.vedeng.erp.buyorder.bizProcessor.processor.overBuyorderChangeSales.afterProcessor.OverBuyorderAfterSaleChangeOrderAfterProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DealSaleorderForBuyChangeAfterProcessor extends OverBuyorderAfterSaleChangeOrderAfterProcessor {
    @Override
    public void doProcess(BizDto bizDto) {
    }
}
