<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.saleorder.dao.ExpressFeedbackMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.saleorder.model.po.ExpressFeedback">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <id column="EXPRESS_FEEDBACK_ID" jdbcType="INTEGER" property="expressFeedbackId" />
        <result column="EXPRESS_ID" jdbcType="INTEGER" property="expressId" />
        <result column="EXPRESS_LOGISTICS_NO" jdbcType="VARCHAR" property="expressLogisticsNo" />
        <result column="GEN_LOGISTICS_NO" jdbcType="VARCHAR" property="genLogisticsNo" />
        <result column="DELIVERY_MONTH" jdbcType="VARCHAR" property="deliveryMonth" />
        <result column="IS_SUCCESS" jdbcType="INTEGER" property="isSuccess" />
        <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
        <result column="CREATOR" jdbcType="INTEGER" property="creator" />
        <result column="UPDATER" jdbcType="INTEGER" property="updater" />
        <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
        <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
        <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
        <result column="REMARK" jdbcType="VARCHAR" property="remark" />
        <result column="RETRY_COUNT" jdbcType="INTEGER" property="retryCount" />
    </resultMap>
    <sql id="Example_Where_Clause">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        EXPRESS_FEEDBACK_ID, EXPRESS_ID, EXPRESS_LOGISTICS_NO, GEN_LOGISTICS_NO, DELIVERY_MONTH,
        IS_SUCCESS, IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME,
        UPDATE_REMARK, REMARK, RETRY_COUNT
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        select
        <include refid="Base_Column_List" />
        from T_EXPRESS_FEEDBACK
        where EXPRESS_FEEDBACK_ID = #{expressFeedbackId,jdbcType=INTEGER}
    </select>
    <select id="selectByIsSuccess" resultType="com.vedeng.erp.saleorder.model.po.ExpressFeedback">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        select
        <include refid="Base_Column_List" />
        from T_EXPRESS_FEEDBACK
        WHERE IS_SUCCESS in(0,2)
        and IS_DELETE = 0
        AND ADD_TIME &gt;= #{beginTime}
        AND ADD_TIME &lt;= #{endTime}
        and RETRY_COUNT &gt; 0
        order by ADD_TIME asc
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        delete from T_EXPRESS_FEEDBACK
        where EXPRESS_FEEDBACK_ID = #{expressFeedbackId,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.vedeng.erp.saleorder.model.po.ExpressFeedback">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        insert into T_EXPRESS_FEEDBACK (EXPRESS_FEEDBACK_ID, EXPRESS_ID, EXPRESS_LOGISTICS_NO,
        GEN_LOGISTICS_NO, DELIVERY_MONTH, IS_SUCCESS,
        IS_DELETE, ADD_TIME, MOD_TIME,
        CREATOR, UPDATER, CREATOR_NAME,
        UPDATER_NAME, UPDATE_REMARK, REMARK,
        RETRY_COUNT)
        values (#{expressFeedbackId,jdbcType=INTEGER}, #{expressId,jdbcType=INTEGER}, #{expressLogisticsNo,jdbcType=VARCHAR},
        #{genLogisticsNo,jdbcType=VARCHAR}, #{deliveryMonth,jdbcType=VARCHAR}, #{isSuccess,jdbcType=BIT},
        #{isDelete,jdbcType=BIT}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP},
        #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR},
        #{updaterName,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
        #{retryCount,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" parameterType="com.vedeng.erp.saleorder.model.po.ExpressFeedback">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        insert into T_EXPRESS_FEEDBACK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="expressFeedbackId != null">
                EXPRESS_FEEDBACK_ID,
            </if>
            <if test="expressId != null">
                EXPRESS_ID,
            </if>
            <if test="expressLogisticsNo != null">
                EXPRESS_LOGISTICS_NO,
            </if>
            <if test="genLogisticsNo != null">
                GEN_LOGISTICS_NO,
            </if>
            <if test="deliveryMonth != null">
                DELIVERY_MONTH,
            </if>
            <if test="isSuccess != null">
                IS_SUCCESS,
            </if>
            <if test="isDelete != null">
                IS_DELETE,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="creatorName != null">
                CREATOR_NAME,
            </if>
            <if test="updaterName != null">
                UPDATER_NAME,
            </if>
            <if test="updateRemark != null">
                UPDATE_REMARK,
            </if>
            <if test="remark != null">
                REMARK,
            </if>
            <if test="retryCount != null">
                RETRY_COUNT,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="expressFeedbackId != null">
                #{expressFeedbackId,jdbcType=INTEGER},
            </if>
            <if test="expressId != null">
                #{expressId,jdbcType=INTEGER},
            </if>
            <if test="expressLogisticsNo != null">
                #{expressLogisticsNo,jdbcType=VARCHAR},
            </if>
            <if test="genLogisticsNo != null">
                #{genLogisticsNo,jdbcType=VARCHAR},
            </if>
            <if test="deliveryMonth != null">
                #{deliveryMonth,jdbcType=VARCHAR},
            </if>
            <if test="isSuccess != null">
                #{isSuccess,jdbcType=BIT},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=BIT},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updaterName != null">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateRemark != null">
                #{updateRemark,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="retryCount != null">
                #{retryCount,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.saleorder.model.po.ExpressFeedback">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        update T_EXPRESS_FEEDBACK
        <set>
            <if test="expressId != null">
                EXPRESS_ID = #{expressId,jdbcType=INTEGER},
            </if>
            <if test="expressLogisticsNo != null">
                EXPRESS_LOGISTICS_NO = #{expressLogisticsNo,jdbcType=VARCHAR},
            </if>
            <if test="genLogisticsNo != null">
                GEN_LOGISTICS_NO = #{genLogisticsNo,jdbcType=VARCHAR},
            </if>
            <if test="deliveryMonth != null">
                DELIVERY_MONTH = #{deliveryMonth,jdbcType=VARCHAR},
            </if>
            <if test="isSuccess != null">
                IS_SUCCESS = #{isSuccess,jdbcType=BIT},
            </if>
            <if test="isDelete != null">
                IS_DELETE = #{isDelete,jdbcType=BIT},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null">
                CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updaterName != null">
                UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateRemark != null">
                UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="retryCount != null">
                RETRY_COUNT = #{retryCount,jdbcType=INTEGER},
            </if>
        </set>
        where EXPRESS_FEEDBACK_ID = #{expressFeedbackId,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.saleorder.model.po.ExpressFeedback">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        update T_EXPRESS_FEEDBACK
        set EXPRESS_ID = #{expressId,jdbcType=INTEGER},
        EXPRESS_LOGISTICS_NO = #{expressLogisticsNo,jdbcType=VARCHAR},
        GEN_LOGISTICS_NO = #{genLogisticsNo,jdbcType=VARCHAR},
        DELIVERY_MONTH = #{deliveryMonth,jdbcType=VARCHAR},
        IS_SUCCESS = #{isSuccess,jdbcType=BIT},
        IS_DELETE = #{isDelete,jdbcType=BIT},
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
        CREATOR = #{creator,jdbcType=INTEGER},
        UPDATER = #{updater,jdbcType=INTEGER},
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
        REMARK = #{remark,jdbcType=VARCHAR},
        RETRY_COUNT = #{retryCount,jdbcType=INTEGER}
        where EXPRESS_FEEDBACK_ID = #{expressFeedbackId,jdbcType=INTEGER}
    </update>
</mapper>