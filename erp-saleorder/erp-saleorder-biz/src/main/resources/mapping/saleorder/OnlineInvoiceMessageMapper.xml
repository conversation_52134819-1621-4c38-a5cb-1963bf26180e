<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.saleorder.dao.OnlineInvoiceMessageMapper">


    <insert id="insert" parameterType="com.vedeng.erp.saleorder.model.po.OnlineInvoiceMessagePo" useGeneratedKeys="true"
            keyProperty="id">
        insert into T_ONLINE_INVOICE_MESSAGE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="messageId != null">
                MESSAGE_ID,
            </if>
            <if test="businessKey != null">
                BUSINESS_KEY,
            </if>
            <if test="consumeResult != null">
                CONSUME_RESULT,
            </if>
            <if test="consumeTimes != null">
                CONSUME_TIMES,
            </if>
            <if test="messageBody != null">
                MESSAGE_BODY,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="messageId != null">
                #{messageId,jdbcType=VARCHAR},
            </if>
            <if test="businessKey != null">
                #{businessKey,jdbcType=INTEGER},
            </if>
            <if test="consumeResult != null">
                #{consumeResult,jdbcType=INTEGER},
            </if>
            <if test="consumeTimes != null">
                #{consumeTimes,jdbcType=INTEGER},
            </if>
            <if test="messageBody != null">
                #{messageBody,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <update id="updateOnlineInvoiceMessage">
        UPDATE T_ONLINE_INVOICE_MESSAGE
        SET CONSUME_RESULT = #{consumeResult,jdbcType=INTEGER},
            CONSUME_TIMES  = CONSUME_TIMES + 1,
            UPDATE_TIME    = #{currentTime,jdbcType=BIGINT}
        WHERE ID = #{id,jdbcType=INTEGER}
    </update>

    <select id="getOnlineInvoiceMessageByIds"
            resultType="com.vedeng.erp.saleorder.model.po.OnlineInvoiceMessagePo">
        SELECT
        *
        FROM
        T_ONLINE_INVOICE_MESSAGE
        WHERE
        ID IN
        <foreach collection="ids" index="index" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>

    <select id="getUnHandleRecords" resultType="com.vedeng.erp.saleorder.model.po.OnlineInvoiceMessagePo">
        SELECT *
        FROM T_ONLINE_INVOICE_MESSAGE
        WHERE CONSUME_RESULT = 0
          AND CONSUME_TIMES <![CDATA[<]]> 100
    </select>

    <select id="getMessageInfoByMessageId"
            resultType="com.vedeng.erp.saleorder.model.po.OnlineInvoiceMessagePo">
        SELECT *
        FROM T_ONLINE_INVOICE_MESSAGE
        WHERE MESSAGE_ID = #{messageId,jdbcType=VARCHAR}
    </select>

</mapper>