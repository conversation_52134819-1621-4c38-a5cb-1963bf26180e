package com.wms.inventorytransfer.model.vo;


import com.wms.inventorytransfer.model.po.InventoryTransferPO;

/**
 * @describe 库存转移单视图对象
 * <AUTHOR>
 * @date 2020/7/10 13:43:25
 */
public class InventoryTransferVO extends InventoryTransferPO {
    /**
     * 单据类型
     */
    private String typeStr;

    /**
     * 状态
     */
    private String statusStr;

    /**
     * 来源目标仓
     */
    private String toWarehouseStr;

    public String getTypeStr() {
        return typeStr;
    }

    public void setTypeStr(String typeStr) {
        this.typeStr = typeStr;
    }

    public String getStatusStr() {
        return statusStr;
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }

    public String getToWarehouseStr() {
        return toWarehouseStr;
    }

    public void setToWarehouseStr(String toWarehouseStr) {
        this.toWarehouseStr = toWarehouseStr;
    }
}
