package com.vedeng.erp.business.common.enums;

import lombok.Getter;

@Getter
public enum BiddingPhaseEnum {

    SMALL(5801,"提案咨询"),

    BIG(5802,"立项论证"),

    SYNTHESIS(5803,"意向公示"),
    
    AED(5804,"公开招标"),

    EMERGENCY(5805,"合同签署"),
    
    ;
    
    
    
    private final Integer code;


    private final String title;

    BiddingPhaseEnum(Integer code, String title) {
        this.code = code;
        this.title = title;
    }


    public static String getTitleByCode(Integer code) {
        for (BiddingPhaseEnum businessChanceLevelEnum : BiddingPhaseEnum.values()) {
            if (businessChanceLevelEnum.getCode().equals(code)) {
                return businessChanceLevelEnum.getTitle();
            }
        }
        return "";
    }

}
