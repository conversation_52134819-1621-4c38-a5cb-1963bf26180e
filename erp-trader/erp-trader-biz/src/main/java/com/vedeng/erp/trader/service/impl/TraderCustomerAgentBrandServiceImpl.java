package com.vedeng.erp.trader.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.erp.trader.dto.TraderCustomerAgentBrandDto;
import com.vedeng.erp.trader.mapper.TraderCustomerAgentBrandMapper;
import com.vedeng.erp.trader.mapstruct.TraderCustomerAgentBrandConvertor;
import com.vedeng.erp.trader.service.TraderCustomerAgentBrandService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/11 17:31
 **/
@Service
@Slf4j
public class TraderCustomerAgentBrandServiceImpl implements TraderCustomerAgentBrandService {

    @Autowired
    private TraderCustomerAgentBrandMapper traderCustomerAgentBrandMapper;

    @Autowired
    private TraderCustomerAgentBrandConvertor traderCustomerAgentBrandConvertor;

    @Override
    public void addAll(List<TraderCustomerAgentBrandDto> traderCustomerAgentBrandDtoList) {

        if (CollUtil.isEmpty(traderCustomerAgentBrandDtoList)) {
            return;
        }
        traderCustomerAgentBrandMapper.batchInsert(traderCustomerAgentBrandConvertor.toEntity(traderCustomerAgentBrandDtoList));

    }

    @Override
    public void deleteByTraderCustomerId(Integer traderCustomerId) {

        if (Objects.isNull(traderCustomerId)) {
            return;
        }
        traderCustomerAgentBrandMapper.deleteByTraderCustomerId(traderCustomerId);
    }
}
