package com.vedeng.erp.aftersale.dto;

import com.alibaba.fastjson.JSONArray;
import com.vedeng.erp.buyorder.dto.OrderRemarkDto;
import com.vedeng.infrastructure.file.domain.Attachment;
import com.vedeng.common.core.base.BaseDto;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseItemDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * T_EXPENSE_AFTER_SALES
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ExpenseAfterSalesDto extends BaseDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private Long expenseAfterSalesId;

    /**
     * 费用售后订单号
     */
    private String expenseAfterSalesNo;

    /**
     * 采购费用单主键ID
     */
    private Integer buyorderExpenseId;

    /**
     * 费用售后类型（字典库）退货退票、退票
     */
    private Integer expenseAfterSalesType;

    /**
     * 费用售后原因（字典库）退货、退款、重做、其他
     */
    private Integer expenseAfterSalesReason;

    /**
     * 详细说明
     */
    private String expenseAfterSalesComments;

    /**
     * 订单详细说明 转单等相关信息
     */
    private List<OrderRemarkDto> orderDesc;

    /**
     * 是否自动转单创建 0 否 1是
     */
    private Integer isAuto;

    /**
     * 客户ID
     */
    private Integer traderId;

    /**
     * 联系人ID
     */
    private Integer traderContactId;

    /**
     * 联系人名称
     */
    private String traderContactName;

    /**
     * 联系人电话
     */
    private String traderContactTelephone;

    /**
     * 联系人手机
     */
    private String traderContactMobile;

    /**
     * 款项退还 1退至公司账户 2退至供应商余额
     */
    private Integer refundMethod;

    /**
     * 是否删除 0否 1是
     */
    private Integer isDelete;

    /**
     * 更新备注
     */
    private String updateRemark;

    /**
     * 售后总额
     */
    private BigDecimal totalAmount;

    /**
     * 费用售后单 状态实体
     */
    private ExpenseAfterSalesStatusDto expenseAfterSalesStatusDto;

    /**
     * 费用单 可退货商品集合 （仅新增/编辑页面使用）
     */
    private List<BuyorderExpenseItemDto> expenseItemList;

    /**
     * 附件信息集合
     */
    private List<Attachment> attachmentList;

    /**
     * 费用单 可退票商品信息集合 （仅新增/编辑页面使用）
     */
    private List<ExpenseItemAndInvoiceDto> itemAndInvoiceDtoList = new ArrayList<>();

    /**
     * 采购费用订单信息
     */
    private BuyorderExpenseDto buyorderExpenseDto;

    /**
     * 费用售后退货单商品信息集合
     */
    private List<ExpenseAfterSalesItemDto> expenseAfterSalesItemDtoList;

    /**
     * 仅退票售后单 退票商品信息集合
     */
    private List<ReturnInvoiceGoodsDto> returnInvoiceGoodsDtoList;
}