package com.vedeng.goods.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.goods.domain.dto.GoodsFinanceInfoDto;
import com.vedeng.goods.domain.entity.GoodsFinance;
import com.vedeng.goods.mapper.GoodsFinanceMapper;
import com.vedeng.goods.service.GoodsFinanceInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: Zeno.zuo
 * @email: <EMAIL>
 * @date: 2022/11/28 20:26
 **/
@Service
@Slf4j
public class GoodsFinanceInfoServiceImpl implements GoodsFinanceInfoService {

    @Autowired
    private GoodsFinanceMapper goodsFinanceMapper;


    @Value("${goodsFinanceInfoAuditUser}")
    private String goodsFinanceInfoAuditUser;

    /**
     * 是否医疗器械-中文描述
     */
    private final String MEDICAL_EQUIPMENT = "是";

    /**
     * 是否医疗器械-中文描述
     */
    private final String NO_MEDICAL_EQUIPMENT = "否";

    /**
     * 商品财务信息-医疗器械属性最大长度
     */
    private final Integer MAX_MEDICAL_EQUIPMENT_ATTR_LENGTH = 50;

    /**
     * excel 最大数据量 10W
     */
    private final Integer MAX_DATA_LENGTH = 100000;


    @Override
    public GoodsFinanceInfoDto getGoodsFinanceInfoDetailBySku(String skuNo) {
        return goodsFinanceMapper.selectGoodsFinanceBySkuNo(skuNo);
    }

    @Override
    public GoodsFinanceInfoDto getGoodsFinanceInfoDetailById(Long goodsFinanceId) {
        GoodsFinanceInfoDto goodsFinanceInfoDto = goodsFinanceMapper.selectGoodsFinanceByPrimaryKey(goodsFinanceId);
        goodsFinanceInfoDto.setAuditStatus(Convert.toInt(goodsFinanceInfoDto.getAuditStatus(),ErpConstant.ZERO));
        goodsFinanceInfoDto.setIsInstallation(Convert.toInt(goodsFinanceInfoDto.getIsInstallation(),ErpConstant.ZERO));
        goodsFinanceInfoDto.setAuditInstallation(Convert.toInt(goodsFinanceInfoDto.getAuditInstallation(),ErpConstant.ZERO));
        if (goodsFinanceInfoDto.getApplyUserId() != null && goodsFinanceInfoDto.getApplyUserId() != 0) {
            GoodsFinanceInfoDto.AuditInfoDto auditInfoDto = new GoodsFinanceInfoDto.AuditInfoDto();
            auditInfoDto.setUser(goodsFinanceInfoDto.getApplyUsername());
            auditInfoDto.setTime(goodsFinanceInfoDto.getApplyTime());
            auditInfoDto.setOper("提交审批");
            goodsFinanceInfoDto.getAuditInfoDtoList().add(auditInfoDto);

            if(goodsFinanceInfoDto.getAuditUserId() == null || goodsFinanceInfoDto.getAuditUserId() == 0){
                // 下一节点审批信息
                GoodsFinanceInfoDto.AuditInfoDto nextAuditInfoDto = new GoodsFinanceInfoDto.AuditInfoDto();
                List<String> list = Arrays.stream(goodsFinanceInfoAuditUser.trim().split(",")).map(String::toLowerCase).collect(Collectors.toList());
                // 排除发起人
                List<String> auditUserList = list.stream().filter(user -> !user.equals(goodsFinanceInfoDto.getApplyUsername().toLowerCase())).collect(Collectors.toList());
                nextAuditInfoDto.setUser(String.join(",", auditUserList));
                goodsFinanceInfoDto.getAuditInfoDtoList().add(nextAuditInfoDto);
            }

        }
        if (goodsFinanceInfoDto.getAuditUserId() != null && goodsFinanceInfoDto.getAuditUserId() != 0) {
            GoodsFinanceInfoDto.AuditInfoDto auditInfoDto = new GoodsFinanceInfoDto.AuditInfoDto();
            auditInfoDto.setUser(goodsFinanceInfoDto.getAuditUsername());
            auditInfoDto.setTime(goodsFinanceInfoDto.getAuditTime());
            if (goodsFinanceInfoDto.getAuditStatus() == 2) {
                auditInfoDto.setOper("审批通过");
            }
            if (goodsFinanceInfoDto.getAuditStatus() == 3) {
                auditInfoDto.setOper("审批驳回");
            }
            auditInfoDto.setRemark(goodsFinanceInfoDto.getAuditRemark());
            goodsFinanceInfoDto.getAuditInfoDtoList().add(auditInfoDto);
        }
        return goodsFinanceInfoDto;
    }

    @Override
    public Integer saveGoodsFinanceInfo(GoodsFinanceInfoDto financeInfoDto) {
        GoodsFinance saveGoodsFinance = convertToSaveGoodsFinance(financeInfoDto);
        return goodsFinanceMapper.updateByPrimaryKeySelective(saveGoodsFinance);
    }

    @Override
    public void importExcelFile(MultipartFile file) throws Exception {
        List<GoodsFinanceInfoDto> goodsFinanceInfoDtoList = new ArrayList<>();
        AtomicInteger row = new AtomicInteger(1);
        EasyExcel.read(file.getInputStream(), GoodsFinanceInfoDto.class, new GoodsFinanceReadListener<GoodsFinanceInfoDto>(dataList ->
                dataList.forEach(data -> {
                    row.getAndIncrement();
                    if(row.get() > MAX_DATA_LENGTH){
                        throw new ServiceException(StrUtil.format("数据量超过10W!", row.get()));
                    }
                    if (!checkBlankUpdateData(data)) {
                        throw new ServiceException(StrUtil.format("第{}行数据不可为空", row.get()));
                    }
                    if (!checkFormatUpdateData(data)) {
                        throw new ServiceException(StrUtil.format("第{}行数据有误", row.get()));
                    }
                    // 中文 是/否 转换为 数字
                    if (MEDICAL_EQUIPMENT.equals(data.getMedicalEquipment())) {
                        data.setIsMedicalEquipment(1);
                    } else if (NO_MEDICAL_EQUIPMENT.equals(data.getMedicalEquipment())) {
                        data.setIsMedicalEquipment(0);
                    }
                    goodsFinanceInfoDtoList.add(data);
                }))).sheet().doRead();
        log.info("GoodsFinanceInfoServiceImpl.importExcelFile 批量处理商品财务信息, 本次一共需要处理 {} 条数据", goodsFinanceInfoDtoList.size());
        if(CollectionUtil.isEmpty(goodsFinanceInfoDtoList)){
            throw new ServiceException("上传的是空模板! 请检查文件");
        }
        List<Integer> fetch = fetchInvalidSkuNo(goodsFinanceInfoDtoList);
        if(fetch.size() > 0){
            throw new ServiceException(StrUtil.format("{} 行订货号不存在", StrUtil.join(",", fetch)));
        }
        batchProcessExcelData(goodsFinanceInfoDtoList);
    }

    @Override
    public void audit(GoodsFinanceInfoDto goodsFinanceInfoDto) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        GoodsFinance goodsFinance = goodsFinanceMapper.findByGoodsFinanceId(goodsFinanceInfoDto.getGoodsFinanceId());

        goodsFinance.setAuditRemark(goodsFinanceInfoDto.getAuditRemark());
        goodsFinance.setAuditStatus(goodsFinanceInfoDto.getAuditStatus());
        if(goodsFinanceInfoDto.getAuditStatus() == 1){
            goodsFinance.setAuditInstallation(goodsFinanceInfoDto.getAuditInstallation());
            goodsFinance.setApplyUserId(currentUser.getId());
            goodsFinance.setApplyUsername(currentUser.getUsername());
            goodsFinance.setApplyTime(new Date());

            // 重置审核
            goodsFinance.setAuditUserId(0);
            goodsFinance.setAuditUsername("");
        }
        if(goodsFinanceInfoDto.getAuditStatus() == 2 || goodsFinanceInfoDto.getAuditStatus() == 3){
            goodsFinance.setAuditUserId(currentUser.getId());
            goodsFinance.setAuditUsername(currentUser.getUsername());
            goodsFinance.setAuditTime(new Date());
            goodsFinance.setModTime(new Date());
            if(goodsFinanceInfoDto.getAuditStatus() == 2){
                goodsFinance.setIsInstallation(goodsFinanceInfoDto.getAuditInstallation());
                goodsFinance.setIsPush(ErpConstant.F);
            }
            if(goodsFinanceInfoDto.getAuditStatus() == 3){
                goodsFinance.setAuditInstallation(goodsFinanceInfoDto.getAuditInstallation());
            }

        }

        goodsFinanceMapper.updateByPrimaryKeySelective(goodsFinance);
    }

    @Override
    public void auditButton(GoodsFinanceInfoDto goodsFinanceInfoDto) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        GoodsFinance goodsFinance = goodsFinanceMapper.findByGoodsFinanceId(goodsFinanceInfoDto.getGoodsFinanceId());
        List<String> list = Arrays.stream(goodsFinanceInfoAuditUser.trim().split(",")).map(String::toLowerCase).collect(Collectors.toList());
        // 是否存在配置中
        if (list.contains(currentUser.getUsername().toLowerCase())) {
            goodsFinanceInfoDto.setAuditButton(true);
        }

        if(goodsFinance.getAuditStatus() == 1){
            // 审核中排除发起人
            List<String> auditUserList = list.stream().filter(user -> !user.equals(goodsFinance.getApplyUsername().toLowerCase())).collect(Collectors.toList());
            if (!auditUserList.contains(currentUser.getUsername().toLowerCase())) {
                goodsFinanceInfoDto.setAuditButton(false);
            }
        }

    }

    /**
     * 批量处理 excel 数据
     * @param excelDataList
     */
    public void batchProcessExcelData(List<GoodsFinanceInfoDto> excelDataList){
        int size = excelDataList.size();
        int batch = 1000;
        int start = 0;
        // 分批转换, 避免全量数据 convert 耗时太长
        List<GoodsFinanceInfoDto> tempList = new ArrayList<>();
        while (start < size){
            tempList.add(excelDataList.get(start));
            if(tempList.size() >= batch || start == size - 1){
                List<GoodsFinance> goodsFinanceList = convertExcelDataListToGoodsFinance(tempList);
                goodsFinanceMapper.updateBatchGoodsFinanceBySkuNo(goodsFinanceList);
                tempList = new ArrayList<>();
                log.info("GoodsFinanceInfoServiceImpl.batchProcessExcelData 批量修改商品财务信息, 目前进度: =========>  {} / {}", start, size);
            }
            start++;
        }
    }


    /**
     * 分批update数据
     * @param goodsFinanceList
     */
    public void batchUpdate(List<GoodsFinance> goodsFinanceList){
        int size = goodsFinanceList.size();
        int batch = 1000;
        int start = 0;
        List<GoodsFinance> tempList = new ArrayList<>();
        while (start < size){
            tempList.add(goodsFinanceList.get(start));
            if(tempList.size() >= batch || start == size - 1){
                goodsFinanceMapper.updateBatchGoodsFinanceBySkuNo(tempList);
                tempList = new ArrayList<>();
            }
            start++;
        }
    }

    /**
     * 检查空数据
     *
     * @param goodsFinanceInfoDto
     * @return
     */
    private boolean checkBlankUpdateData(GoodsFinanceInfoDto goodsFinanceInfoDto) {
        if (goodsFinanceInfoDto == null) {
            return false;
        }
        return !StringUtils.isBlank(goodsFinanceInfoDto.getSkuNo()) && !StringUtils.isBlank(goodsFinanceInfoDto.getMedicalEquipment());
    }

    /**
     * 校验数据格式
     *
     * @param goodsFinanceInfoDto
     * @return
     */
    private boolean checkFormatUpdateData(GoodsFinanceInfoDto goodsFinanceInfoDto) {
        if (goodsFinanceInfoDto == null) {
            return false;
        }
        // 校验描述
        if (!MEDICAL_EQUIPMENT.equals(goodsFinanceInfoDto.getMedicalEquipment()) && !NO_MEDICAL_EQUIPMENT.equals(goodsFinanceInfoDto.getMedicalEquipment())) {
            return false;
        }
        // 根据是否医疗器械 校验其他属性
        switch (goodsFinanceInfoDto.getMedicalEquipment()) {
            case MEDICAL_EQUIPMENT:
                // 如果是医疗器械, 医疗器械细分类、医疗器械用途、医疗器械产线 都必须不为空并且长度需小于 50
                if(StringUtils.isBlank(goodsFinanceInfoDto.getMedicalEquipmentType()) ||
                        StringUtils.isBlank(goodsFinanceInfoDto.getMedicalEquipmentUse()) ||
                        StringUtils.isBlank(goodsFinanceInfoDto.getMedicalEquipmentLine())){
                    return false;
                }
                if (goodsFinanceInfoDto.getMedicalEquipmentType().length() > MAX_MEDICAL_EQUIPMENT_ATTR_LENGTH ||
                        goodsFinanceInfoDto.getMedicalEquipmentUse().length() > MAX_MEDICAL_EQUIPMENT_ATTR_LENGTH ||
                        goodsFinanceInfoDto.getMedicalEquipmentLine().length() > MAX_MEDICAL_EQUIPMENT_ATTR_LENGTH) {
                    return false;
                }
                return true;
            case NO_MEDICAL_EQUIPMENT:
                return true;
            default:
                return false;
        }
    }

    /**
     * 转换类型为 GoodsFinance 方便插入
     * 由于修改只会修改几个值 所以仅设置部分属性
     *
     * @param goodsFinanceInfoDto
     * @return
     */
    private GoodsFinance convertToSaveGoodsFinance(GoodsFinanceInfoDto goodsFinanceInfoDto) {
        GoodsFinance saveGoodsFinance = new GoodsFinance();
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        saveGoodsFinance.setGoodsFinanceId(goodsFinanceInfoDto.getGoodsFinanceId());
        saveGoodsFinance.setIsMedicalEquipment(goodsFinanceInfoDto.getIsMedicalEquipment());
        saveGoodsFinance.setMedicalEquipmentType(goodsFinanceInfoDto.getMedicalEquipmentType());
        saveGoodsFinance.setMedicalEquipmentUse(goodsFinanceInfoDto.getMedicalEquipmentUse());
        saveGoodsFinance.setMedicalEquipmentLine(goodsFinanceInfoDto.getMedicalEquipmentLine());
        saveGoodsFinance.setModTime(new Date());
        saveGoodsFinance.setUpdater(currentUser.getId());
        saveGoodsFinance.setUpdaterName(currentUser.getUsername());
        saveGoodsFinance.setIsPush(ErpConstant.ZERO);
        return saveGoodsFinance;
    }

    private List<GoodsFinance> convertExcelDataListToGoodsFinance(List<GoodsFinanceInfoDto> goodsFinanceInfoDtoList){
        List<GoodsFinance> goodsFinanceList = new ArrayList<>();
        for (GoodsFinanceInfoDto goodsFinanceInfoDto : goodsFinanceInfoDtoList) {
            goodsFinanceList.add(convertExcelDataToGoodsFinance(goodsFinanceInfoDto));
        }
        return goodsFinanceList;
    }

    /**
     * 将 excel 读取的 dto 转换类型为 GoodsFinance 方便插入
     * 由于修改只会修改几个值 所以仅设置部分属性
     *
     * @param goodsFinanceInfoDto
     * @return
     */
    private GoodsFinance convertExcelDataToGoodsFinance(GoodsFinanceInfoDto goodsFinanceInfoDto) {
        GoodsFinance saveGoodsFinance = new GoodsFinance();
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        saveGoodsFinance.setSkuNo(goodsFinanceInfoDto.getSkuNo());
        saveGoodsFinance.setIsMedicalEquipment(goodsFinanceInfoDto.getIsMedicalEquipment());
        // 对于 是否医疗器械 选择否的数据， 医疗器械细分类、用途、产线都设置为空
        saveGoodsFinance.setMedicalEquipmentType(goodsFinanceInfoDto.getIsMedicalEquipment() == 1 ? goodsFinanceInfoDto.getMedicalEquipmentType() : "");
        saveGoodsFinance.setMedicalEquipmentUse(goodsFinanceInfoDto.getIsMedicalEquipment() == 1 ? goodsFinanceInfoDto.getMedicalEquipmentUse() : "");
        saveGoodsFinance.setMedicalEquipmentLine(goodsFinanceInfoDto.getIsMedicalEquipment() == 1 ? goodsFinanceInfoDto.getMedicalEquipmentLine() : "");
        saveGoodsFinance.setModTime(new Date());
        saveGoodsFinance.setUpdater(currentUser.getId());
        saveGoodsFinance.setUpdaterName(currentUser.getUsername());
        saveGoodsFinance.setIsPush(ErpConstant.ZERO);
        return saveGoodsFinance;
    }

    /**
     * 找到不在商品财务信息表（T_GOODS_FINANCE）中的SKU, 并返回索引
     * @param goodsFinanceList
     * @return
     */
    public List<Integer> fetchInvalidSkuNo(List<GoodsFinanceInfoDto> goodsFinanceList){
        List<Integer> res = new ArrayList<>();
        int size = goodsFinanceList.size();
        int batch = 1000;
        int start = 0;
        while (start < size){
            List<String> goodsFinanceSkuList = new ArrayList<>();
            // 获取该批次的 sku list
            for (int i = 0; i < batch; i++){
                if (start + i == size){
                    break;
                }
                goodsFinanceSkuList.add(goodsFinanceList.get(start + i).getSkuNo());
            }
            // 去重， 然后去数据库中查询
            List<String> skuList = goodsFinanceSkuList.stream().distinct().collect(Collectors.toList());
            List<String> loopList = goodsFinanceMapper.getSkuNoListByQueryList(skuList);
            // 如果 查询出来的 sku list 数据量不同， 说明有 sku 不在该表中
            if (skuList.size() != loopList.size()){
                // 使用原始的 goodsFinanceSkuList 而不是去重后的 skuList 去遍历，以获取行号
                for(int j = 0; j < goodsFinanceSkuList.size(); j++){
                    if(!loopList.contains(goodsFinanceSkuList.get(j))){
                        res.add(start + j + 2);
                    }
                }
            }
            // 发现不在表中的 SKU 直接跳出
            if(res.size() >= 1){
                break;
            }
            start = start + batch;
        }
        return res;
    }
}
