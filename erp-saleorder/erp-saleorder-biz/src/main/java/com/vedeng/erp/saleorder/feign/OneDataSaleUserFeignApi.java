package com.vedeng.erp.saleorder.feign;

import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.onedataapi.api.profit.GrossProfitApi;
import com.vedeng.onedataapi.api.profit.req.SaleUserGrossProfitReqDto;
import com.vedeng.onedataapi.api.profit.res.GrossProfitData;
import feign.Headers;
import feign.Param;
import feign.RequestLine;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignApi(serverName = "onedataapi")
public interface OneDataSaleUserFeignApi extends GrossProfitApi {

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("GET /sale/user/{saleUserId}/grossProfit/{year}/{month}")
    @Override
    RestfulResult<GrossProfitData> detailSaleUserMonthlyGrossProfit(@Param("saleUserId") Integer saleUserId, @Param("year") String year, @Param("month") String month);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /sale/user/grossProfit/batch")
    @Override
    RestfulResult<List<GrossProfitData>> batchSaleUserMonthlyGrossProfit(@RequestBody SaleUserGrossProfitReqDto reqDto);


    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /sale/user/grossProfit/batchByDateRange")
    @Override
    RestfulResult<List<GrossProfitData>> batchSaleUserGrossProfitByDateRange(@RequestBody SaleUserGrossProfitReqDto reqDto);
}
