package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeeFileDataEntity;
import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * @description:
 * @author: yana.jiang
 * @date: 2022/11/10
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface KingDeeFileConvertor extends BaseMapStruct<KingDeeFileDataEntity, KingDeeFileDataDto> {

}
