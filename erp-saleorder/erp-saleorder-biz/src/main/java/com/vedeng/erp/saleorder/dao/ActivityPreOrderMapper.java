package com.vedeng.erp.saleorder.dao;
import java.util.Collection;

import com.vedeng.erp.saleorder.dto.ActivityPreOrderDto;
import org.apache.ibatis.annotations.Param;

import com.vedeng.erp.saleorder.domain.entity.ActivityPreOrderEntity;

import java.util.List;

public interface ActivityPreOrderMapper {
    /**
     * delete by primary key
     *
     * @param activityPreOrderId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer activityPreOrderId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(ActivityPreOrderEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(ActivityPreOrderEntity record);

    /**
     * select by primary key
     *
     * @param activityPreOrderId primary key
     * @return object by primary key
     */
    ActivityPreOrderEntity selectByPrimaryKey(Integer activityPreOrderId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(ActivityPreOrderEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(ActivityPreOrderEntity record);

    int updateBatchSelective(List<ActivityPreOrderEntity> list);

    /**
     * 根据编号获取
     *
     * @param orderNo orderNo
     * @return ActivityPreOrderEntity
     */
    ActivityPreOrderEntity findByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 根据订单号获取
     * @param saleorderId
     * @return
     */
    ActivityPreOrderDto findBySaleorderId(@Param("saleorderId")Integer saleorderId);


    ActivityPreOrderDto findByActivityPreOrderId(@Param("activityPreOrderId")Integer activityPreOrderId);

    /**
     * 获取当前客户在此活动下已经购买过多少数量的sku
     *
     * @param traderId
     * @param actionId
     * @param skuId
     * @return
     */
    Integer findExistNumByTraderIdAndActionId(@Param("traderId") Integer traderId,
                                              @Param("actionId") Integer actionId,
                                              @Param("skuId") Integer skuId);


    List<ActivityPreOrderEntity> findByActivityPreOrderIdIn(@Param("activityPreOrderIdCollection")Collection<Integer> activityPreOrderIdCollection);



}