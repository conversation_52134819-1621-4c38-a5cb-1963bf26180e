# ERP-Temporal XXL-Job 任务配置说明

## 概述

本文档说明 erp-temporal 模块中 XXL-Job 定时任务的配置和使用方法。所有任务都位于 `com.vedeng.temporal.task` 包下。

## 任务列表

### 1. 工作流恢复任务 (WorkflowRecoveryJob)

**任务标识**: `workflowRecoveryJob`  
**建议执行频率**: 每5分钟执行一次  
**功能**: 自动检测和恢复失败的工作流

#### 参数配置

| 参数 | 说明 | 示例 | 默认值 |
|------|------|------|--------|
| 空参数或"auto" | 自动恢复模式 | `auto` | 自动恢复，最多10个 |
| workflowId | 恢复指定工作流 | `workflowId=wf_123456` | - |
| dryRun | 试运行模式 | `dryRun=true` | false |
| maxCount | 单次最大恢复数量 | `maxCount=5` | 10 |
| mode | 执行模式 | `mode=single` | auto |

#### 参数组合示例

```bash
# 自动恢复模式（默认）
auto

# 恢复指定工作流
workflowId=wf_123456

# 试运行模式，检查但不执行恢复
dryRun=true,maxCount=20

# 限制单次恢复数量
maxCount=5

# 组合参数
mode=auto,maxCount=15,dryRun=false
```

#### XXL-Job 配置建议

```
任务描述: 工作流自动恢复任务
Cron: 0 */5 * * * ?  (每5分钟执行)
运行模式: BEAN
JobHandler: workflowRecoveryJob
执行参数: auto
路由策略: 第一个
子任务: 无
任务超时时间: 300秒
失败重试次数: 2
```

---

### 2. 检查点清理任务 (CheckpointCleanupJob)

**任务标识**: `checkpointCleanupJob`  
**建议执行频率**: 每天凌晨2点执行一次  
**功能**: 清理过期的工作流检查点数据

#### 参数配置

| 参数 | 说明 | 示例 | 默认值 |
|------|------|------|--------|
| 空参数或"auto" | 自动清理模式 | `auto` | 按配置清理过期数据 |
| days | 清理天数 | `days=30` | 配置文件中的值 |
| dryRun | 试运行模式 | `dryRun=true` | false |
| force | 强制清理所有 | `force=true` | false |
| workflowId | 清理指定工作流 | `workflowId=wf_123456` | - |

#### 参数组合示例

```bash
# 自动清理模式（默认）
auto

# 清理30天前的检查点
days=30

# 试运行模式，只统计不清理
dryRun=true

# 强制清理所有检查点（危险操作）
force=true,dryRun=false

# 清理指定工作流的检查点
workflowId=wf_123456

# 组合参数
days=15,dryRun=false
```

#### XXL-Job 配置建议

```
任务描述: 检查点数据清理任务
Cron: 0 0 2 * * ?  (每天凌晨2点执行)
运行模式: BEAN
JobHandler: checkpointCleanupJob
执行参数: auto
路由策略: 第一个
子任务: 无
任务超时时间: 1800秒
失败重试次数: 1
```

---

### 3. 工作流监控任务 (WorkflowMonitorJob)

**任务标识**: `workflowMonitorJob`  
**建议执行频率**: 每10分钟执行一次  
**功能**: 监控工作流执行状态和健康度

#### 参数配置

| 参数 | 说明 | 示例 | 默认值 |
|------|------|------|--------|
| 空参数或"health" | 健康检查模式 | `health` | 检查整体状态 |
| report | 报告类型 | `report=summary` | - |
| alert | 启用告警 | `alert=true` | false |
| timeout | 超时检查时间(秒) | `timeout=3600` | 3600 |

#### 参数组合示例

```bash
# 健康检查模式（默认）
health

# 生成汇总报告
report=summary

# 生成详细报告
report=detail

# 启用告警的健康检查
alert=true,timeout=1800

# 组合参数
report=summary,alert=true
```

#### XXL-Job 配置建议

```
任务描述: 工作流监控任务
Cron: 0 */10 * * * ?  (每10分钟执行)
运行模式: BEAN
JobHandler: workflowMonitorJob
执行参数: alert=true
路由策略: 第一个
子任务: 无
任务超时时间: 180秒
失败重试次数: 1
```

---

### 4. 多公司业务流程任务 (MultiCompanyBusinessJob)

**任务标识**: `multiCompanyBusinessJob`
**建议执行频率**: 根据业务需求，建议每小时或每天执行一次
**功能**: 多公司业务流程自动化任务，支持批量启动和单个启动两种模式

#### 执行模式说明

##### 批量模式 (Batch Mode)
- **触发条件**: 参数为空、"batch"、"auto" 时启用
- **执行逻辑**: 从数据库读取有效的流程配置，批量启动多个工作流
- **数据来源**: T_FLOW_ORDER 表中的有效流程配置
- **适用场景**: 定时批量处理业务流程，自动化程度高

##### 单个模式 (Single Mode)
- **触发条件**: 传入流转单编号时启用
- **执行逻辑**: 根据流转单编号查询流程配置，启动单个工作流
- **参数格式**: 直接传入流转单编号（如：FO202501010001）
- **适用场景**: 手动触发特定业务流程，精确控制

#### 参数配置

| 参数 | 说明 | 示例 | 默认值 |
|------|------|------|--------|
| 空参数 | 批量模式 | 无参数 | 批量处理所有有效流程 |
| "batch" | 批量模式 | `batch` | 批量处理所有有效流程 |
| "auto" | 批量模式 | `auto` | 批量处理所有有效流程 |
| 流转单编号 | 单个模式 | `FO202501010001` | 启动指定流程的工作流 |

#### 单个模式参数说明

单个模式只需要传入流转单编号即可，系统会自动：
1. 根据流转单编号查询流程配置
2. 获取对应的公司执行顺序
3. 构建工作流请求并启动

| 参数 | 类型 | 必填 | 说明 | 示例 |
|------|------|------|------|------|
| flowOrderNo | String | 是 | 流转单编号 | "FO202501010001" |

#### 参数组合示例

```bash
# 批量模式（推荐用于定时任务）
# 空参数
（无参数）

# 明确指定批量模式
batch

# 自动模式
auto

# 单个模式 - 流转单编号
FO202501010001

# 单个模式 - 另一个流转单示例
FO202501020002
```

#### XXL-Job 配置建议

```
任务描述: 多公司业务流程自动化任务
Cron: 0 0 */1 * * ?  (每小时执行，可根据业务需求调整)
运行模式: BEAN
JobHandler: multiCompanyBusinessJob
执行参数: batch
路由策略: 第一个
子任务: 无
任务超时时间: 1800秒 (30分钟，考虑批量处理时间)
失败重试次数: 2
```

#### 业务流程说明

##### 批量模式执行流程
1. **查询流程配置**: 从 T_FLOW_ORDER 表查询有效的流程配置
2. **获取公司序列**: 通过 FlowNodeBasedCompanyService 获取每个流程的公司执行顺序
3. **构建请求对象**: 为每个流程配置构建 CompanyBusinessRequest 对象
4. **启动工作流**: 调用 WorkflowOrchestrationService 批量启动 Temporal 工作流
5. **统计结果**: 记录成功和失败的统计信息，输出执行报告

##### 单个模式执行流程
1. **参数校验**: 校验流转单编号参数
2. **查询配置**: 根据流转单编号查询流程配置
3. **获取序列**: 获取公司执行顺序配置
4. **构建请求**: 构建 CompanyBusinessRequest 对象
5. **启动工作流**: 调用 WorkflowOrchestrationService 启动 Temporal 工作流
6. **返回结果**: 返回工作流启动结果和工作流ID

##### 业务类型转换
- **基础业务类型 1 (采购)** → `PURCHASE_BUSINESS`
- **基础业务类型 2 (销售)** → `SALES_BUSINESS`
- **其他或未知类型** → `MULTI_COMPANY_BUSINESS`

#### 依赖服务

| 服务 | 说明 | 用途 |
|------|------|------|
| WorkflowOrchestrationService | 工作流编排服务 | 启动和管理 Temporal 工作流 |
| FlowNodeBasedCompanyService | 公司序列服务 | 获取业务流程的公司执行顺序 |
| TemporalFlowOrderMapper | 流程配置数据访问 | 查询有效的流程配置数据 |

#### 监控指标

批量模式执行时会输出以下统计信息：
- **总处理数量**: 查询到的有效流程配置总数
- **成功启动数量**: 成功启动工作流的数量
- **失败数量**: 启动失败的数量
- **失败详情**: 失败的流程配置编号和失败原因

#### 注意事项

1. **批量模式性能**: 批量处理时会同时启动多个工作流，注意监控系统资源使用情况
2. **流转单编号**: 单个模式需要确保传入的流转单编号在系统中存在且有效
3. **流程状态**: 只有审核状态为已审核且未删除的流程配置才能启动工作流
4. **公司序列**: 确保流程配置有对应的公司执行顺序配置
5. **超时设置**: 批量模式可能需要较长执行时间，建议设置合适的超时时间
6. **错误处理**: 批量模式中单个流程失败不会影响其他流程的执行

---

## 配置依赖

### 1. 配置文件设置

确保在 `erp-temporal.properties` 中配置了相关参数：

```properties
# 工作流恢复机制配置
temporal.workflow.recovery.enabled=true
temporal.workflow.recovery.autoRecoveryEnabled=true
temporal.workflow.recovery.autoRecoveryInterval=300000
temporal.workflow.recovery.maxRestartCount=3
temporal.workflow.recovery.recoveryTimeoutHours=24

# 检查点配置
temporal.workflow.recovery.checkpointEnabled=true
temporal.workflow.recovery.checkpointIntervalMinutes=5
temporal.workflow.recovery.checkpointMaxAgeDays=30
temporal.workflow.recovery.checkpointMaxSizeBytes=10485760
temporal.workflow.recovery.checkpointAutoCleanupEnabled=true
```

### 2. XXL-Job 执行器配置

确保在 Spring 配置中正确配置了 XXL-Job 执行器，并扫描了任务包：

```xml
<!-- JobHandler 扫描路径 -->
<context:component-scan base-package="com.vedeng.temporal.task" />
```

## 监控和告警

### 1. 任务执行日志

所有任务都会在 XXL-Job 控制台和应用日志中记录详细的执行信息：

- 任务开始和结束时间
- 参数解析结果
- 执行过程详情
- 统计数据和结果

### 2. 异常处理

任务执行异常时会：

- 记录详细的错误信息
- 返回失败状态给 XXL-Job
- 触发 XXL-Job 的重试机制（如果配置了重试）

### 3. 告警建议

建议为以下情况配置告警：

- 工作流恢复任务连续失败
- 检查点清理任务执行异常
- 工作流监控任务发现大量失败工作流
- 任务执行时间超过预期

## 最佳实践

### 1. 执行时间安排

```
02:00 - 检查点清理任务（低峰期执行）
每5分钟 - 工作流恢复任务（高频监控）
每10分钟 - 工作流监控任务（状态检查）
每小时 - 多公司业务流程任务（业务流程自动化）
```

### 2. 参数配置建议

- **生产环境**: 使用默认参数，启用告警
- **测试环境**: 可以使用 `dryRun=true` 进行测试
- **开发环境**: 可以调整执行频率和参数进行调试
- **多公司业务任务**:
  - **生产环境**: 使用 `batch` 模式进行批量处理
  - **测试环境**: 使用单个模式测试特定业务流程
  - **紧急处理**: 使用键值对格式快速启动单个工作流

### 3. 性能考虑

- 工作流恢复任务：限制 `maxCount` 避免单次处理过多
- 检查点清理任务：在低峰期执行，避免影响业务
- 监控任务：适当的执行频率，避免过度监控
- 多公司业务任务：
  - 批量模式可能同时启动多个工作流，注意监控系统资源
  - 设置合适的超时时间，避免长时间运行影响其他任务
  - 在业务高峰期适当调整执行频率

## 故障排查

### 1. 常见问题

1. **任务无法启动**: 检查 JobHandler 扫描路径和 Spring 配置
2. **参数解析错误**: 检查参数格式是否正确
3. **执行超时**: 调整 XXL-Job 的超时时间配置
4. **依赖服务不可用**: 检查 Temporal 服务器和数据库连接
5. **多公司业务任务特有问题**:
   - **批量模式无数据**: 检查 T_FLOW_ORDER 表是否有有效的流程配置
   - **公司序列为空**: 检查 T_FLOW_NODE 表的公司配置
   - **工作流启动失败**: 检查 Temporal 服务器状态和工作流定义
   - **流转单不存在**: 确认传入的流转单编号在系统中存在
   - **流程未审核**: 确认流程配置的审核状态为已审核

### 2. 日志查看

- XXL-Job 控制台：查看任务执行状态和日志
- 应用日志：查看详细的业务执行日志
- 数据库日志：查看工作流执行记录

### 3. 调试方法

1. 使用 `dryRun=true` 参数进行试运行
2. 调整日志级别查看详细信息
3. 在测试环境验证参数配置
4. 检查相关服务的健康状态
5. **多公司业务任务调试**:
   - 使用单个模式测试特定流转单的业务流程
   - 检查数据库中的流程配置数据（T_FLOW_ORDER表）
   - 验证公司执行顺序配置（T_FLOW_NODE表）
   - 查看 Temporal Web UI 确认工作流状态
