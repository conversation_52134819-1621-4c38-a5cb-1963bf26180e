package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.date.DateUtil;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import com.vedeng.erp.kingdee.batch.processor.BatchExpenseBlueInvoiceProcessor;
import com.vedeng.erp.kingdee.batch.processor.BatchExpenseNeedReceiveProcessor;
import com.vedeng.erp.kingdee.batch.processor.BatchHistorySaleBlueInvoiceCompositeProcessor;
import com.vedeng.erp.kingdee.batch.tasklet.PushInvoiceVoucherTasklet;
import com.vedeng.erp.kingdee.batch.writer.BatchExpenseBlueInvoiceWriter;
import com.vedeng.erp.kingdee.batch.writer.BatchExpenseNeedReceiveWriter;
import com.vedeng.erp.kingdee.dto.KingDeeOutPutFeePlainAndSpecialInvoiceDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveFeeDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.item.support.CompositeItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;

/**
 * 销售单费用商品正向流程推送
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/6 15:37
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class SaleOrderExpenseBatchJob extends BaseJob {

    @Autowired
    private BatchExpenseNeedReceiveProcessor batchExpenseNeedReceiveProcessor;
    @Autowired
    private BatchExpenseNeedReceiveWriter batchExpenseNeedReceiveWriter;
    @Autowired
    private BatchExpenseBlueInvoiceProcessor batchExpenseBlueInvoiceProcessor;
    @Autowired
    private BatchExpenseBlueInvoiceWriter batchExpenseBlueInvoiceWriter;
    @Autowired
    private PushInvoiceVoucherTasklet pushInvoiceVoucherTasklet;
    @Autowired
    private BatchHistorySaleBlueInvoiceCompositeProcessor batchHistorySaleBlueInvoiceCompositeProcessor;

    public Job saleOrderVirtualGoodsFlowJob() {
        return jobBuilderFactory.get("saleOrderVirtualGoodsFlowJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(expenseNeedReceivePush())
                .next(expenseBlueInvoicePush())
                .next(saveInvoiceVoucher())
                .build();
    }

    /**
     * 费用应收单推送
     *
     * @return Step
     */
    private Step expenseNeedReceivePush() {
        return stepBuilderFactory.get("销售费用应收单")
                .<BatchInvoiceDto, KingDeeReceiveFeeDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchSaleOrderExpenseDtoItemReader(null, null))
                .processor(compositeItemExcelHistoryExpenseNeedReceiveProcessor())
                .writer(batchExpenseNeedReceiveWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 费用蓝票推送
     *
     * @return Step
     */
    private Step expenseBlueInvoicePush() {
        return stepBuilderFactory.get("销售费用蓝票")
                .<BatchInvoiceDto, KingDeeOutPutFeePlainAndSpecialInvoiceDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchSaleOrderExpenseDtoItemReader(null, null))
                .processor(compositeItemExcelHistoryExpenseBlueInvoiceProcessor())
                .writer(batchExpenseBlueInvoiceWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 21-22历史蓝票Excel处理及生成费用应收单
     *
     * @return
     */
    @Bean
    public CompositeItemProcessor<BatchInvoiceDto, KingDeeReceiveFeeDto> compositeItemExcelHistoryExpenseNeedReceiveProcessor() {
        CompositeItemProcessor<BatchInvoiceDto, KingDeeReceiveFeeDto> compositeItemProcessor = new CompositeItemProcessor<>();
        compositeItemProcessor.setDelegates(Arrays.asList(batchHistorySaleBlueInvoiceCompositeProcessor, batchExpenseNeedReceiveProcessor));
        return compositeItemProcessor;
    }

    /**
     * 21-22历史蓝票Excel处理及生成费用蓝票
     *
     * @return
     */
    @Bean
    public CompositeItemProcessor<BatchInvoiceDto, KingDeeOutPutFeePlainAndSpecialInvoiceDto> compositeItemExcelHistoryExpenseBlueInvoiceProcessor() {
        CompositeItemProcessor<BatchInvoiceDto, KingDeeOutPutFeePlainAndSpecialInvoiceDto> compositeItemProcessor = new CompositeItemProcessor<>();
        compositeItemProcessor.setDelegates(Arrays.asList(batchHistorySaleBlueInvoiceCompositeProcessor, batchExpenseBlueInvoiceProcessor));
        return compositeItemProcessor;
    }

    /**
     * 发票存入金蝶凭证信息表
     */
    private Step saveInvoiceVoucher() {
        return stepBuilderFactory.get("销售费用发票存入金蝶凭证信息表")
                .tasklet(pushInvoiceVoucherTasklet)
                .build();
    }



    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> batchSaleOrderExpenseDtoItemReader(@Value("#{jobParameters['beginTime']}") String beginTime, @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                .type(505)
                // 通过时间是当天的
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(beginTime).getTime())
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(endTime).getTime())
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "findSaleOrderBlueInvoiceBatch", batchInvoiceDto);
    }

}