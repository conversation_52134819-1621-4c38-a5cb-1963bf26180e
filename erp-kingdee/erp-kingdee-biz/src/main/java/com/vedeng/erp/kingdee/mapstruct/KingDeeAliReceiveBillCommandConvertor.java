package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeeAliReceiveBillCommand;
import com.vedeng.erp.kingdee.domain.command.KingDeeReceiveBillCommand;
import com.vedeng.erp.kingdee.dto.KingDeeAliReceiveBillDto;
import com.vedeng.erp.kingdee.dto.KingDeeAliReceiveBillEntryDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveBillDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface KingDeeAliReceiveBillCommandConvertor extends BaseCommandMapStruct<KingDeeAliReceiveBillCommand, KingDeeAliReceiveBillDto> {
    /**
     * dto转为金蝶command
     * @param dto
     * @return
     */
    @Override
    @Mapping(target = "FBillTypeId.FNumber", source = "FBillTypeId")
    @Mapping(target = "FPayOrgId.FNumber", source = "FPayOrgId")
    @Mapping(target = "FContactUnit.FNumber", source = "FContactUnit")
    @Mapping(target = "f_QZOK_JYZT", source = "FQzokJyzt")
    @Mapping(target = "f_QZOK_JYLX", source = "FQzokJylx")
    @Mapping(target = "f_QZOK_JYFS", source = "FQzokJyfs")
    @Mapping(target = "FRECEIVEBILLENTRY", source = "FReceiveBillEntry")
    @Mapping(target = "FId", source = "FId")
    @Mapping(target = "FBillNo", source = "FBillNo")
    @Mapping(target = "FDATE", source = "FDate")
    @Mapping(target = "f_qzok_lsh", source = "FQzokLsh")
    KingDeeAliReceiveBillCommand toCommand(KingDeeAliReceiveBillDto dto);


    @Mapping(target = "FSETTLETYPEID.FNumber", source = "FSettleTypeId")
    @Mapping(target = "FPURPOSEID.FNumber", source = "FPurposeId")
    @Mapping(target = "FACCOUNTID.FNumber", source = "FAccountId")
    @Mapping(target = "f_QZOK_SKYW.FNumber", source = "FQzokSkyw")
    @Mapping(target = "f_QZOK_BDDJHID", source = "FQzokBddjhid")
    @Mapping(target = "f_QZOK_YSDDH", source = "FQzokYsddh")
    @Mapping(target = "FRECTOTALAMOUNTFOR", source = "FRecTotalAmountFor")
    @Mapping(target = "f_QZOK_GSYWDH", source = "FQzokGsywdh")
    @Mapping(target = "f_QZOK_YWLX", source = "FQzokYwlx")
    @Mapping(target = "FHANDLINGCHARGEFOR", source = "FHandlingChargeFor")
    KingDeeAliReceiveBillCommand.FRECEIVEBILLENTRY toCommand(KingDeeAliReceiveBillEntryDto dto);
}
