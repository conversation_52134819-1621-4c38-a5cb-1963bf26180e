package com.vedeng.erp.saleorder.dao.extend;

import org.apache.ibatis.annotations.Param;

public interface ActivityPreOrderExtendMapper {

    /**
     * 获取当前客户在此活动下已经购买过多少数量的sku
     *
     * @param traderId
     * @param actionId
     * @param skuId
     * @return
     */
    Integer findExistNumByTraderIdAndActionId(@Param("traderId") Integer traderId,
                                              @Param("actionId") Integer actionId,
                                              @Param("skuId") Integer skuId);


}