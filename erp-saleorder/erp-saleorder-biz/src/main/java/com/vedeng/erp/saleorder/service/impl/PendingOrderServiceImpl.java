package com.vedeng.erp.saleorder.service.impl;

import cn.hutool.crypto.SecureUtil;
import com.vedeng.authorization.model.Organization;
import com.vedeng.authorization.model.User;
import com.vedeng.common.page.Page;
import com.vedeng.infrastructure.sms.service.SmsService;
import com.vedeng.erp.confirmrecord.dao.ConfirmRecordDao;
import com.vedeng.erp.confirmrecord.model.ConfirmRecord;
import com.vedeng.erp.saleorder.constant.ConfirmRecordBusinessTypeEnum;
import com.vedeng.erp.saleorder.service.PendingOrderService;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.vo.SaleorderVo;
import com.vedeng.system.service.OrgService;
import com.vedeng.system.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/9 14:03
 * @describe 待签收业务处理
 */
@Service
public class PendingOrderServiceImpl implements PendingOrderService {

    Logger logger= LoggerFactory.getLogger(PendingOrderServiceImpl.class);
    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Autowired
    @Qualifier("orgService")
    protected OrgService orgService;

    @Autowired
    @Qualifier("userService")
    private UserService userService;

    @Value("${confirm_encrypt_key}")
    private String confirmEncryptKey;

    @Resource
    private ConfirmRecordDao confirmRecordDao;

    @Autowired
    private SmsService smsService;

    @Override
    public List<SaleorderVo> getPendingOrderListBySaleorder(Saleorder saleorder, Long validTimeStart, Long validTimeEnd){
        List<SaleorderVo> pendingOrderListBySaleorder = saleorderGoodsMapper.getPendingOrderListBySaleorder(saleorder, validTimeStart, validTimeEnd);
        pendingOrderListBySaleorder.forEach(res -> {
            // 手机号加密
            res.setTraderContactMobile(SecureUtil.aes(confirmEncryptKey.getBytes()).encryptHex(res.getTraderContactMobile()));
        });
        return pendingOrderListBySaleorder;
    }

    @Override
    public Map<String, Object> getPendingOrderList(Saleorder saleorder, Page page) {

        Map<String, Object> map = new HashMap<>(16);
        map.put("saleorder", saleorder);
        map.put("page", page);
        List<SaleorderVo> saleorderList = saleorderGoodsMapper.getPendingOrderListPage(map);
        saleorderList.forEach(res -> {
            // 手机号加密
            res.setTraderContactMobile(SecureUtil.aes(confirmEncryptKey.getBytes()).encryptHex(res.getTraderContactMobile()));
            if(res.getTraderId()!=null && res.getTraderId()>0) {
                User userOrg = orgService.getTraderUserAndOrgByTraderId(res.getTraderId(), 1);
                res.setOptUserName(userOrg == null ? "" : userOrg.getUsername());
                res.setSalesDeptName(userOrg == null ? "" : userOrg.getOrgName());
            } else {
                User user = userService.getUserById(res.getUserId());
                res.setOptUserName(user == null ? "" : user.getUsername());
                res.setOptUserId(user == null ? 0 : user.getUserId());
                Organization org = orgService.getOrgNameByUserId(res.getUserId());
                res.setSalesDeptName(org == null ? "" : org.getOrgName());
            }
        });
        Map<String, List<SaleorderVo>> listMap = saleorderList.stream().collect(Collectors.groupingBy(o -> o.getTraderId() + "/" + o.getTraderContactMobile()));
        if (listMap.size() == 1){
            map.put("copyButton","1");
            map.put("traderId",listMap.get(listMap.keySet().toArray()[0].toString()).get(0).getTraderId());
            map.put("traderContactMobile",listMap.get(listMap.keySet().toArray()[0].toString()).get(0).getTraderContactMobile());
        }else {
            map.put("copyButton","0");
        }
        map.put("saleorderList", saleorderList);
        return map;
    }


    @Override
    public void sendSms(List<SaleorderVo> saleorderVos,User user, Integer sendType) {
        Date date = new Date();
        Map<String, Boolean> map = new HashMap<>(16);
        saleorderVos.forEach(res -> {
            String content = "@1@="+res.getTraderId()+",@2@="+res.getTraderContactMobile();
            String traderContactMobileDecrypt = null;
            if (!map.containsKey(res.getTraderId() + "-" + res.getTraderContactMobile())) {
                // 手机号解密
                traderContactMobileDecrypt = SecureUtil.aes(confirmEncryptKey.getBytes()).decryptStr(res.getTraderContactMobile());
                Boolean sendTplSms =  smsService.sendTplSms(traderContactMobileDecrypt, "JSM40187-0075", content);
                map.put(res.getTraderId() + "-" + res.getTraderContactMobile(), sendTplSms);
            }

            if (map.get(res.getTraderId() + "-" + res.getTraderContactMobile())) {
                // 发送短信成功，保存发送短信记录
                ConfirmRecord confirmRecord = new ConfirmRecord();
                // 业务详细描述（货物签收单商品名称）
                confirmRecord.setBusinessDesc(res.getGoodsName());
                // 业务编码（获取签收单存入订单商品表主键）
                confirmRecord.setBusinessNo(res.getSaleorderGoodsId().toString());
                confirmRecord.setBusinessType(ConfirmRecordBusinessTypeEnum.PENDING_CONFIRM.getCode());
                confirmRecord.setCreator(user.getUserId());
                confirmRecord.setAddTime(date);
                confirmRecord.setUpdater(user.getUserId());
                confirmRecord.setModTime(date);
                confirmRecord.setSendType(sendType);
                confirmRecord.setSendTime(date);
                // 发送方式类型（0：短信、1：微信、2：邮件）
                confirmRecord.setSendMethodType("0");
                // 是否确认，默认：否
                confirmRecord.setConfirmStatus(0);
                confirmRecordDao.insertSelective(confirmRecord);
                logger.info("发送签收通知短信通知成功 saleorderGoodsId：{}，traderContactMobile：{}" , res.getSaleorderGoodsId() ,traderContactMobileDecrypt);
            }
        });

    }
}
