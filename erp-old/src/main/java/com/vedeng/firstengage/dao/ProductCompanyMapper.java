package com.vedeng.firstengage.dao;

import com.vedeng.firstengage.model.ProductCompany;
import com.vedeng.firstengage.model.RegistrationNumber;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface ProductCompanyMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_PRODUCT_COMPANY
     *
     * @mbg.generated Wed Mar 20 18:36:27 CST 2019
     */
    int deleteByPrimaryKey(Integer productCompanyId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_PRODUCT_COMPANY
     *
     * @mbg.generated Wed Mar 20 18:36:27 CST 2019
     */
    int insert(ProductCompany record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_PRODUCT_COMPANY
     *
     * @mbg.generated Wed Mar 20 18:36:27 CST 2019
     */
    int insertSelective(ProductCompany record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_PRODUCT_COMPANY
     *
     * @mbg.generated Wed Mar 20 18:36:27 CST 2019
     */
    ProductCompany selectByPrimaryKey(Integer productCompanyId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_PRODUCT_COMPANY
     *
     * @mbg.generated Wed Mar 20 18:36:27 CST 2019
     */
    int updateByPrimaryKeySelective(ProductCompany record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_PRODUCT_COMPANY
     *
     * @mbg.generated Wed Mar 20 18:36:27 CST 2019
     */
    int updateByPrimaryKey(ProductCompany record);

    /**
     * @description 所有
     * <AUTHOR>
     * @param
     * @date 2019/5/21
     */
    List<Map<String,Object>>  getallcompany(@Param("productCompanyName")String productCompanyName);

    /**
     * @description 批量生产企业
     * <AUTHOR>
     * @param
     * @date 2019/6/5
     */
    void insertList(List<RegistrationNumber> list);


    /**
     * Gets by productCompanyName.
     *
     * @param productCompanyName 生产企业名称(中文)
     * @return
     */
    ProductCompany getByProductCompanyName(String productCompanyName);
}