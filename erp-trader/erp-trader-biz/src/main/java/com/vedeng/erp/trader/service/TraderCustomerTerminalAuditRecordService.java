package com.vedeng.erp.trader.service;

import com.vedeng.erp.trader.dto.TraderCustomerTerminalAuditRecordDto;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/9/5 14:33
 **/
public interface TraderCustomerTerminalAuditRecordService {

    /**
     * 根据终端id 获取最后一次的审核不通过备注
     * @param traderCustomerTerminalId
     * @return
     */
    String queryLastRejectDesc(Integer traderCustomerTerminalId);

    /**
     * 保存审核 记录
     * @param add 数据
     */
    void add(TraderCustomerTerminalAuditRecordDto add);

    /**
     * 获取审核记录
     * @param traderCustomerTerminalId
     * @return
     */
    List<TraderCustomerTerminalAuditRecordDto> getAuditRecordList(Integer traderCustomerTerminalId);
}
