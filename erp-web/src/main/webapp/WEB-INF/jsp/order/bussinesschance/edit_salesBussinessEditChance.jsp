
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="编辑销售商机" scope="application" />
<%@ include file="../../common/common.jsp"%>

<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/order/bussinesschance/add_salesEditCommentsChance.js?rnd=${resourceVersionKey}"></script>
<div class="formpublic formpublic1 pt0">
    <form method="post" id="myform" action="">
        <input type="hidden" name="bussinessChanceId" value="${bussinessChanceVo.bussinessChanceId}">
        <input type="hidden" name="type" value="392">
        <input type="hidden" name="traderId" id ="traderId" value="${traderCustomer.traderId }">
<%--        <input type="hidden" name="traderCustomerId" value="${traderCustomer.traderCustomerId }">--%>
        <div class="line">
            <ul>

                <li>
                    <div class="infor_name">
                        <label>产品备注（主机）：</label>
                    </div>
                    <div class="f_left">
                            <textarea class="askprice" name="productComments" id="productComments" placeholder="请输入产品备注（主机）,不超过300字">${bussinessChanceVo.productComments}</textarea>
                        <div id="productComments1" class="font-red " style="display: none">产品备注（主机）不能大于300字符</div>
                    </div>
                </li>
            </ul>
        </div>



        <div class="add-tijiao">
            <button id="submit" type="button" class="dele">提交</button>
    <button type="button" class="dele" id="close-layer">取消</button>
        </div>
    </form>
</div>
</body>

</html>
