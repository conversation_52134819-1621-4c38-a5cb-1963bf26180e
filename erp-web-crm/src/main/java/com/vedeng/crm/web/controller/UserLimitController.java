package com.vedeng.crm.web.controller;

import com.vedeng.common.core.base.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 首页
 * @date 2024/7/17 16:35
 */
@Controller
@Slf4j
@RequestMapping("/crm")
public class UserLimitController extends BaseController {

    @RequestMapping("/noPermission")
    public String noPermission() {

        return "vue/view/crm/noPermission";
    }

    @RequestMapping("/clientMustBeWeixin")
    public String clientMustBeWeixin() {

        return "vue/view/crm/clientMustBeWeixin";
    }



    @RequestMapping("/404")
    public String http404() {
        return "vue/view/crm/profile/common/404";
    }


    @RequestMapping("/500")
    public String http500() {
        return "vue/view/crm/profile/common/500";
    }


    /**
     * 用户模拟ERP功能跳转，解决
     * @return
     */
    @ResponseBody
    @RequestMapping("/common/profile/ssoForErp")
    public String ssoForErp(){
        return "ok";
    }

}
