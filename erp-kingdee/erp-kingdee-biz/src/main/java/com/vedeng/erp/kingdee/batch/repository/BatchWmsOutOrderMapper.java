package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchWmsOutOrderDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2023/12/11 8:54
 **/
public interface BatchWmsOutOrderMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(BatchWmsOutOrderDto record);

    int insertSelective(BatchWmsOutOrderDto record);

    BatchWmsOutOrderDto selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(BatchWmsOutOrderDto record);

    int updateByPrimaryKey(BatchWmsOutOrderDto record);

    List<BatchWmsOutOrderDto> selectByWmsNo(@Param("wmsNo")String wmsNo);


}