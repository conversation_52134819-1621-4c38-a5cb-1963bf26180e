package com.vedeng.erp.aftersale.feign;

import com.vedeng.aftersales.model.dto.AfterSalesPushRecordDTO;
import com.vedeng.aftersales.model.dto.AfterSalesUpdateRecordDto;
import com.vedeng.aftersales.model.dto.TradePushRecodeDto;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.common.feign.constants.ServerConstants;
import feign.Headers;
import feign.Param;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

@FeignApi(serverName = ServerConstants.YXB_ADMIN_SERVER)
public interface AfterSaleOrderYxbApi {
    //工单下推医修帮
    @Headers({"Content-Type: application/json", "Accept: application/json","token: {token}"})
    @RequestLine("POST /api/syncAftersalesOrder")
    RestfulResult<String> syncWorkOrder(@RequestBody AfterSalesPushRecordDTO data, @Param("token") String token);

    //修改医修帮对应工单状态
    @Headers({"Content-Type: application/json", "Accept: application/json","token: {token}"})
    @RequestLine("POST /api/updateAftersalesStatus")
    RestfulResult<String> syncWorkOrderStatus(@RequestBody AfterSalesUpdateRecordDto data, @Param("token") String token);

    //给医修帮工单推送付款记录
    @Headers({"Content-Type: application/json", "Accept: application/json","token: {token}"})
    @RequestLine("POST /api/addTradeRecode")
    RestfulResult<String> syncWorkOrderPay(@RequestBody TradePushRecodeDto data, @Param("token") String token);
}
