package com.smallhospital.service;

import com.smallhospital.dto.ELContractSkuDTO;
import com.smallhospital.dto.ELOrderDto;
import com.smallhospital.dto.ElSkuInfo;
import com.smallhospital.dto.ValidatorResult;
import com.smallhospital.model.ContractSku;
import com.smallhospital.model.ElContractSku;
import com.smallhospital.model.vo.ELContractVO;
import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;

import java.util.List;

public interface ELContractService {

    List<ELContractVO> querylistPage(ELContractVO contract, Page page);

    int saveContractInfo(ELContractVO contract);

    /**
     * <b>Description:</b>根据合同id获取修改的合同<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/4/20
     */
    ELContractVO getContractModifyInfo(Integer contractId);

    ELContractVO findById(Integer contractId);

    ResultInfo saveModifyContract(ELContractVO contract, User user, List<ElContractSku> skuList);

    void updateContract(ELContractVO contract);

    List<Integer> findOtherValidSkus(ELContractVO contractVO);

    List<ContractSku> checkSkuIdsIsOnSale(ELContractSkuDTO elContractDTO);

    List<Integer> findNotExitsIds(List<Integer> skuIds);

    List<ELContractVO> findByTradeId(Integer traderId);

    /**
     * 校验合同有效期
     * @param orderDto
     * @return
     */
    ValidatorResult validatorContractExpire(ELOrderDto orderDto);
    /**
     * <b>Description:</b>开始终止合同流程<br>
     * @param contractId 合同编号
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/4/17
     */
    ResultInfo startTermination(Integer contractId);

    /**
     * <b>Description:</b>审批终止合同流程<br>
     * @param contractId 合同标识
     * @param type 审核类型 1为销售主管、总监审核，2为客户确认
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/4/17
     */
    ResultInfo checkTermination(ELContractVO elContractVO,Integer type);

    /**
     * <b>Description:</b>审核修改合同的内容<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/4/21
     */
    ResultInfo checkModify(ELContractVO contractVO);


    /**
     * 校验产品合同
     * @param orderDto
     * @return
     */
    void validatorContractSku(ELOrderDto orderDto);

    /**
     * 校验产品价格
     * @param orderDto
     * @return
     */
    List<ElSkuInfo> validatorSkuPrice(ELOrderDto orderDto);
}
