; void function () {
    var defaults = {
        el: '',
        value: {
            year: '',
            month: ''
        }
    };

    var YearMonthSelect = function (config) {
        this.config = $.extend({}, defaults, config);
        this.__init();
        return this;
    };

    var initHtml = function (config) {
        config = config || {};

        var dropOptions = '';

        for (var i = 0; i < 12; i++) {
            dropOptions += `<div class="ym-month-item J-ym-month-item" data-month="${i + 1}">${i + 1}月</div>`;
        }

        return `<div class="ym-select-wrap">
            <div class="ym-select-trigger J-ym-select-trigger">
                <div class="ym-select-label placeholder J-ym-select-label"></div>
                <span class="vd-icon icon-down"></span>
            </div>
            <div class="ym-select-drop-wrap J-ym-select-drop-wrap">
                <div class="ym-select-drop-year">
                    <span class="vd-icon icon-slide-up left J-ym-year-prev"></span>
                    <span class="year-txt J-ym-year" data-year="2025">2025</span>
                    <span class="vd-icon icon-slide-up right J-ym-year-next"></span>
                </div>
                <div class="ym-select-drop-month-wrap">
                    <div class="ym-select-drop-month-list J-ym-select-drop-month-list">
                        <div class="ym-month-item J-ym-month-item">全年</div>
                        ${dropOptions}
                    </div>
                </div>
            </div>
        </div>`;
    };

    YearMonthSelect.prototype = {
        constructor: 'YearMonthSelect',
        __init: function () {
            console.log('new yms')
            this.$wrap = $(this.config.el);

            this.$wrap.append(initHtml());
            this.value = this.config.value || {};

            this.__initData();

            this.__bindEvent();
        },
        __initData() {
            this.__checkLabel();
        },
        __checkLabel() {
            if (this.value && this.value.year) {
                var label = this.value.year + '年' + (this.value.month ? this.value.month + '月' : '');
                $('.J-ym-select-label', this.$wrap).html(label).removeClass('placeholder');
            } else {
                $('.J-ym-select-label', this.$wrap).html(this.config.placeholder || '请选择').addClass('placeholder');
            }
        },
        __checkItem() {
            $('.J-ym-month-item', this.$wrap).removeClass('active').removeClass('disabled');

            if(this.value.year && this.value.year == $('.J-ym-year', this.$wrap).data('year')) {
                if(this.value.month) {
                    $('.J-ym-month-item', this.$wrap).eq(this.value.month).addClass('active');
                } else {
                    $('.J-ym-month-item', this.$wrap).eq(0).addClass('active');
                }
            }

            var _this = this;

            if(this.config.max && this.config.max.year && $('.J-ym-year', this.$wrap).data('year') >= this.config.max.year) {
                $('.J-ym-year-next', this.$wrap).addClass('hide');

                if(this.config.max.month) {
                    $('.J-ym-month-item', this.$wrap).each(function() {
                        if($(this).data('month') && $(this).data('month') > _this.config.max.month) {
                            $(this).addClass('disabled');
                        }
                    })
                }
            } else {
                $('.J-ym-year-next', this.$wrap).removeClass('hide');
            }

            if(this.config.min && this.config.min.year && $('.J-ym-year', this.$wrap).data('year') <= this.config.min.year) {
                $('.J-ym-year-prev', this.$wrap).addClass('hide');

                if(this.config.min.month) {
                    $('.J-ym-month-item', this.$wrap).each(function() {
                        if($(this).data('month') && $(this).data('month') < _this.config.min.month) {
                            $(this).addClass('disabled');
                        }
                    })
                }
            } else {
                $('.J-ym-year-prev', this.$wrap).removeClass('hide');
            }
        },
        __bindEvent: function () {
            var _this = this;

            $('.J-ym-select-trigger', this.$wrap).click(function () {
                if ($(this).hasClass('open')) {
                    $('.J-ym-select-drop-wrap', _this.$wrap).slideUp(220);
                    $(this).removeClass('open');
                } else {
                    $('.J-ym-select-drop-wrap', _this.$wrap).slideDown(220);
                    $(this).addClass('open');

                    var showYear = _this.value.year || new Date().getFullYear();
                    $('.J-ym-year', _this.$wrap).data('year', showYear).html(showYear);
                    _this.__checkItem();
                }
            })

            $(document).click(function () {
                $('.J-ym-select-drop-wrap').slideUp(220);
                $('.J-ym-select-trigger').removeClass('open');
            })

            this.$wrap.click(function (e) {
                e.stopPropagation();
            })

            $('.J-ym-month-item', this.$wrap).click(function () {
                if($(this).hasClass('disabled')) {
                    return;
                }

                _this.value.year = $('.J-ym-year', _this.$wrap).data('year');
                _this.value.month = $(this).data('month');
                _this.__checkLabel();

                $('.J-ym-select-drop-wrap').slideUp(220);
                $('.J-ym-select-trigger').removeClass('open');

                _this.config.change && _this.config.change(_this.value);
            })

            $('.J-ym-year-prev', this.$wrap).click(function () {
                var nYear = $('.J-ym-year', _this.$wrap).data('year');
                $('.J-ym-year', _this.$wrap).data('year', nYear - 1).html(nYear - 1);
                _this.__checkItem();
            })

            $('.J-ym-year-next', this.$wrap).click(function () {
                var nYear = $('.J-ym-year', _this.$wrap).data('year');
                $('.J-ym-year', _this.$wrap).data('year', nYear + 1).html(nYear + 1);
                _this.__checkItem();
            })
        },

    }

    window.YearMonthSelect = YearMonthSelect;
}.call(this);