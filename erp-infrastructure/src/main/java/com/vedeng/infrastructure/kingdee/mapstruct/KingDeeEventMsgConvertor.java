package com.vedeng.infrastructure.kingdee.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeEventMsgDto;
import com.vedeng.infrastructure.kingdee.domain.entity.KingDeeEventMsgEntity;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * dto 转 entity
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface KingDeeEventMsgConvertor extends BaseMapStruct<KingDeeEventMsgEntity, KingDeeEventMsgDto> {



}
