package com.vedeng.price.dto;

import java.math.BigDecimal;

public class SkuPriceInfoPurchaseDto implements Comparable<SkuPriceInfoPurchaseDto> {

    private Long traderId;

    private String traderName;

    private BigDecimal purchasePrice;

    private String modTime;

    public Long getTraderId() {
        return traderId;
    }

    public void setTraderId(Long traderId) {
        this.traderId = traderId;
    }

    public BigDecimal getPurchasePrice() {
        return purchasePrice;
    }

    public void setPurchasePrice(BigDecimal purchasePrice) {
        this.purchasePrice = purchasePrice;
    }

    public String getModTime() {
        return modTime;
    }

    public void setModTime(String modTime) {
        this.modTime = modTime;
    }


    @Override
    public int compareTo(SkuPriceInfoPurchaseDto skuPriceInfoPurchaseDto) {

        if(this.getModTime().compareTo(skuPriceInfoPurchaseDto.getModTime()) > 0)
        {
            return 1;
        }else if(this.getModTime().compareTo(skuPriceInfoPurchaseDto.getModTime()) < 0){
            return -1;
        }else{
            if(this.getPurchasePrice().compareTo(skuPriceInfoPurchaseDto.getPurchasePrice()) >= 0){
                return 1;
            }else{
                return -1;
            }
        }
    }

    public String getTraderName() {
        return traderName;
    }

    public void setTraderName(String traderName) {
        this.traderName = traderName;
    }
}
