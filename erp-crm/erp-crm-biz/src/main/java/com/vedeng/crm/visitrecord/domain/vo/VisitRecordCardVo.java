package com.vedeng.crm.visitrecord.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.Date;

@Data
public class VisitRecordCardVo {
    /** 记录ID */
    private Integer id;
    
    /** 关联表的ID */
    private Integer recordId;
    
    /** 打卡人ID */
    private Integer visitUserId;
    
    /** 打卡人姓名 */
    private String userName;
    
    /** 打卡人头像 */
    private String aliasHeadPicture;
    
    /** 拍照的照片链接以逗号拼接 */
    private String cardPhotoUrls;
    
    /** 打卡位置x,y */
    private String cardLocation;
    
    /** 打卡时间 */
    private Date cardTime;
    
    /** 添加时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date addTime;
    
    /** 添加人 */
    private Integer addUserId;
    
    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modTime;
    
    /** 更新人 */
    private Integer modUserId;
} 