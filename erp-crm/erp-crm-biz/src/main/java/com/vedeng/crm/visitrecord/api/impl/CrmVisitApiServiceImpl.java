package com.vedeng.crm.visitrecord.api.impl;

import com.netflix.discovery.converters.Auto;
import com.vedeng.crm.visitrecord.api.CrmVisitApiService;
import com.vedeng.crm.visitrecord.domain.vo.VisitRecordVo;
import com.vedeng.crm.visitrecord.domain.vo.VisitTongxingUserVo;
import com.vedeng.crm.visitrecord.facade.impl.CrmVisitRecordFacadeImpl;
import com.vedeng.crm.visitrecord.mapper.CrmVisitRecordMapper;
import com.vedeng.crm.visitrecord.service.CrmVisitRecordService;
import com.vedeng.crm.visitrecord.service.VisitTongxingUserService;
import com.vedeng.crm.visitrecord.service.impl.CrmVisitRelateCloseMessageServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/3/13
 */
@Service
public class CrmVisitApiServiceImpl implements CrmVisitApiService {

    public static final Integer VISIT_STATUS_CLOSE = 4;
    private static final Logger log = LoggerFactory.getLogger(CrmVisitApiServiceImpl.class);

    @Autowired
    private VisitTongxingUserService visitTongxingUserService;

    @Autowired
    @Qualifier(value = "CrmVisitRelateCloseMessageServiceImpl")
    private CrmVisitRelateCloseMessageServiceImpl crmVisitRelateCloseMessageServiceImpl;

    @Autowired
    private CrmVisitRecordMapper visitRecordMapper;

    @Override
    public void SendMessageForCheckBusinessChance(String bussinessChanceNo,String closeUserName,String closeReason) {
        List<VisitRecordVo> visitRecordVoList =  //crmVisitRecordService.selectVisitRecordByRelateNo(bussinessChanceNo,2);
                                                visitRecordMapper.selectVisitRecordByRelateNo(bussinessChanceNo,2);
        for(VisitRecordVo  visitRecordVo:visitRecordVoList){
            if(visitRecordVo!=null && !VISIT_STATUS_CLOSE.equals(visitRecordVo.getVisitRecordStatus())){
                //如果关联的有拜访计划，并且状态是未关闭，则发送消息给销售人员
                log.info("商机{}关联了拜访计划：{}，关闭商机时触发推送消息",bussinessChanceNo,visitRecordVo.getVisitRecordNo());
                List<VisitTongxingUserVo> tongxingUserList = visitTongxingUserService.getTongxingUserList(visitRecordVo.getId());
                crmVisitRelateCloseMessageServiceImpl.sendMessage(visitRecordVo,tongxingUserList,closeUserName,closeReason);


            }
        }
    }

    @Override
    public void SendMessageForCheckLeads(String leadsNo,String closeUserName,String closeReason) {
        List<VisitRecordVo> visitRecordVoList =  //crmVisitRecordService.selectVisitRecordByRelateNo(leadsNo,2);
                                                visitRecordMapper.selectVisitRecordByRelateNo(leadsNo,1);
        for(VisitRecordVo  visitRecordVo:visitRecordVoList){
            if(visitRecordVo!=null && !VISIT_STATUS_CLOSE.equals(visitRecordVo.getVisitRecordStatus())){
                //如果关联的有拜访计划，并且状态是未关闭，则发送消息给销售人员
                log.info("线索{}关联了拜访计划：{}，关闭线索时触发推送消息",leadsNo,visitRecordVo.getVisitRecordNo());
                List<VisitTongxingUserVo> tongxingUserList = visitTongxingUserService.getTongxingUserList(visitRecordVo.getId());
                crmVisitRelateCloseMessageServiceImpl.sendMessage(visitRecordVo,tongxingUserList,closeUserName,closeReason);


            }
        }
    }

    @Override
    public List<VisitRecordVo> selectVisitRecordByRelateId(Integer relateId, Integer relateType) {
        return visitRecordMapper.selectVisitRecordByRelateId(relateId,relateType);
    }
}
