package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.BatchCapitalBillDto;
import com.vedeng.erp.kingdee.dto.KingDeeCustomerDto;
import com.vedeng.erp.kingdee.dto.KingDeeNeedReceiveDto;
import com.vedeng.erp.kingdee.dto.KingDeeNeedReceiveEntityDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeCustomerMapper;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 客户余额支付销售订单订单 processor
 */
@Service
@Slf4j
public class SaleOrderPayByBalanceProcessor implements ItemProcessor<BatchCapitalBillDto, KingDeeNeedReceiveDto> {

    @Autowired
    KingDeeCustomerMapper kingDeeCustomerMapper;
    @Autowired
    KingDeeBaseApi kingDeeBaseApi;
    @Override
    public KingDeeNeedReceiveDto process(BatchCapitalBillDto dto) throws Exception {
        log.info("销售订单余额付款，金蝶应收单调整单，参数:{}", JSON.toJSONString(dto));
        // 根据erp客户id查询金蝶客户id
        KingDeeCustomerDto kingDeeCustomerDto = kingDeeCustomerMapper.queryInfoByCustomerId(dto.getTraderCustomerId());
        if (ObjectUtil.isEmpty(kingDeeCustomerDto)){
            log.error("销售订单余额付款，金蝶应收单调整单，未查询到金蝶客户id，参数:{}",JSON.toJSONString(dto));
            return null;
        }
        // 金蝶应收单调整单 needReceiveDto
        KingDeeNeedReceiveDto needReceiveDto = new KingDeeNeedReceiveDto();
        needReceiveDto.setFid("0");
        needReceiveDto.setFBillNo(dto.getCapitalBillNo());
        // 判断是否数据已存在
        boolean old = kingDeeBaseApi.isExist(needReceiveDto);
        if(old){
            log.info("销售订单余额付款,数据已存在:{}", JSON.toJSONString(dto));
            return null;
        }
        needReceiveDto.setFVpfnDate(DateUtil.formatDate(DateUtil.date(dto.getTraderTime())));
        needReceiveDto.setFVpfnJg(KingDeeConstant.ORG_ID.toString());
        needReceiveDto.setFVpfnKh(Convert.toStr(kingDeeCustomerDto.getFNumber()));
        List<KingDeeNeedReceiveEntityDto> FEntity = new ArrayList<>();
        KingDeeNeedReceiveEntityDto needReceiveEntityDto = new KingDeeNeedReceiveEntityDto();
        needReceiveEntityDto.setFVpfnYsddh(dto.getOrderNo());
        needReceiveEntityDto.setFVpfnGsywdh(dto.getOrderNo());
        //销售订单余额付款-应收单调整单 业务类型
        needReceiveEntityDto.setFVpfnYwlx("订单收款");
        needReceiveEntityDto.setFVpfnTzje(dto.getAmount().abs());
        FEntity.add(needReceiveEntityDto);
        needReceiveDto.setFEntityList(FEntity);
        return needReceiveDto;
    }
}
