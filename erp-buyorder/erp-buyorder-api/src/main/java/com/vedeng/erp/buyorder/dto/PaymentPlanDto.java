package com.vedeng.erp.buyorder.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 付款计划 封装类
 * @date 2022/8/23 10:37
 **/
@Data
public class PaymentPlanDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 计划
     */
    private String plan;

    /**
     * 计划内容
     */
    private String planContent;

    /**
     * 支付金额
     */
    private BigDecimal amount;

    /**
     * 备注
     */
    private String remark;
}
