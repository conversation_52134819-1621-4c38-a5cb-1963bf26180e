package com.vedeng.erp.settlement.domain.entity;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2022/9/6 8:54
 **/
@Data
public class BankBillEntity {
    private Integer bankBillId;

    /**
    * 公司ID
    */
    private Integer companyId;

    /**
    * 银行标示1建设银行2南京银行3中国银行4支付宝5微信
    */
    private Integer bankTag;

    /**
    * 交易流水号（唯一）
    */
    private String tranFlow;

    /**
    * 交易日期(系统记账日期)
    */
    private Date trandate;

    /**
    * 交易时间
    */
    private Date trantime;

    /**
    * 真实交易日期
    */
    private Date realTrandate;

    /**
    * 真实交易日期时间
    */
    private Date realTrandatetime;

    /**
    * 凭证种类
    */
    private String creTyp;

    /**
    * 凭证号码
    */
    private String creNo;

    /**
    * 摘要
    */
    private String message;

    private BigDecimal amt;

    /**
    * 余额
    */
    private String amt1;

    /**
    * 借贷标志(0-借 转出 ,1-贷 转入)
    */
    private Integer flag1;

    /**
    * 对方账号
    */
    private String accno2;

    /**
    * 对方行号
    */
    private String accBankno;

    /**
    * 对方账户名称
    */
    private String accName1;

    /**
    * 交易钞汇标志(0－钞户 1－汇户)
    */
    private Integer flag2;

    /**
    * 企业支付流水号
    */
    private String bflow;

    /**
    * 活存账户明细号
    */
    private String detNo;

    /**
    * 备注
    */
    private String det;

    /**
    * 关联账号(一户通主账户查询时返回关联子账号)
    */
    private String rltvAccno;

    /**
    * 对方账户开户行名称
    */
    private String cadbankNm;

    /**
    * 状态 是否忽略 0否 1是
    */
    private Integer status;

    /**
    * 忽略备注
    */
    private String comments;

    private BigDecimal matchedAmount;

    private String ovrlsttnTrckNo;

    /**
    * 是否一致(付款客户与结款客户)1是2否
    */
    private Integer isConsistency;

    /**
    * 匹配项目(字典库)
    */
    private Integer matchedObject;

    /**
    * 支付宝或微信交易商户订单号
    */
    private String orderNo;

    /**
    * 关联流水号
    */
    private String relatedBill;

    /**
    * 手续费标识项(0-不是手续费，1-是手续费)
    */
    private Integer isFee;

    /**
    * 数据同步时间
    */
    private Date syncDate;

    /**
    * 财务添加的用于匹配的流水号
    */
    private String capitalSearchFlow;

    /**
    * 支付宝流水回单sso地址
    */
    private String receiptUrl;

    /**
     * 是否已推送过金蝶
     */
    private Integer havePushedKingdee;
}