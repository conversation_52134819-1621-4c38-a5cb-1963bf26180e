package com.newtask;

import com.pricecenter.dto.PageResultDto;
import com.pricecenter.dto.PriceSkuGroupPriceDto;
import com.pricecenter.dto.PricedSkuPageQueryDto;
import com.pricecenter.service.BasePriceMaintainService;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.model.dto.CoreSkuDto;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 价格中心集团价初始化接口 执行一次
 *
 */
@JobHandler(value = "priceCenterGroupPriceInitTask")
@Component
public class PriceCenterGroupPriceInitTask extends AbstractJobHandler {

    Logger logger = LoggerFactory.getLogger(PriceCenterGroupPriceInitTask.class);

    private static int PAGE_SIZE = 5000;

    @Autowired
    private BasePriceMaintainService basePriceMaintainService;

    @Resource
    private CoreSkuMapper coreSkuMapper;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        XxlJobLogger.log("价格中心集团价初始化start-----------");
        logger.info("价格中心集团价初始化start-----------");

        PageResultDto firstPageResult =  getCurrentPageData(0);
        int totalRecords = firstPageResult.getTotalRecords();

        if(totalRecords <= 0){
            return ReturnT.SUCCESS;
        }

        int totalPage = getTotalPage(totalRecords);

        List<PriceSkuGroupPriceDto> pricedSkuList = null;

        for(int currentPage = 0; currentPage < totalPage ;currentPage++){

            if(currentPage == 0){
                pricedSkuList = firstPageResult.getDatas();
            }else {
                pricedSkuList = getCurrentPageData(currentPage).getDatas();
            }

            if(CollectionUtils.isEmpty(pricedSkuList)){
                break;
            }

            //处理下集团价
            dealWithGroupPrice(pricedSkuList);

            basePriceMaintainService.batchUpdateGroupPrice(pricedSkuList);
        }



        logger.info("价格中心集团价初始化end-----------");
        XxlJobLogger.log("价格中心集团价初始化end-----------");
        return SUCCESS;
    }

    /**
     * 处理下已经核价的sku列表
     * @param pricedSkuList
     */
    private void dealWithGroupPrice(List<PriceSkuGroupPriceDto> pricedSkuList) {

        List<String> skuNos = pricedSkuList.stream().map(pricedSku -> pricedSku.getSkuNo()).collect(Collectors.toList());

        List<CoreSkuDto> skuList = coreSkuMapper.batchFindBySkuNos(skuNos);

        pricedSkuList.stream().forEach(pricedSku -> {

            CoreSkuDto coreSkuDto = skuList.stream().filter(sku->sku.getSkuNo().equals(pricedSku.getSkuNo())).findFirst().get();
            if(coreSkuDto == null){
                return;
            }

            //1. 需要报备的产品集团价 = 终端价*120%
            if(coreSkuDto.getNeedReport() != null && coreSkuDto.getNeedReport() == 1){
                pricedSku.setGroupPrice(pricedSku.getTerminalPrice().multiply(BigDecimal.valueOf(1.2)));
                return;
            }

            //2. 耗材集团价 = 终端价
            if(coreSkuDto.getSpuType() == 317){
                pricedSku.setGroupPrice(pricedSku.getTerminalPrice());
                return;
            }

            //3. 设备集团价 = 终端价 *99% 后向上取整
            if(coreSkuDto.getSpuType() == 316){
                pricedSku.setGroupPrice(pricedSku.getTerminalPrice().multiply(BigDecimal.valueOf(0.99)).setScale(2, BigDecimal.ROUND_HALF_UP));
                return;
            }

        });
    }

    private PageResultDto getCurrentPageData(int pageNo) {

        PricedSkuPageQueryDto pricedSkuPageQueryDto = new PricedSkuPageQueryDto();

        pricedSkuPageQueryDto.setPageNo(pageNo);
        pricedSkuPageQueryDto.setPageSize(PAGE_SIZE);

        return basePriceMaintainService.findPricedSkuPageQuery(pricedSkuPageQueryDto);
    }

    private int getTotalPage(int totalRecords) {
        int pageNum = totalRecords / PAGE_SIZE;
        return (totalRecords % PAGE_SIZE) > 0 ? pageNum + 1 : pageNum;
    }
}
