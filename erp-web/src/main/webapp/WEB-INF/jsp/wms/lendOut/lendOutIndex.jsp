<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="商品外借列表" scope="application" />
<%@ include file="../../common/common.jsp"%>
<div class="content">
    <div class="searchfunc">
        <form action="${pageContext.request.contextPath}/wms/commodityLendOut/index.do" method="post" id="search">
            <ul>
                <li>
                    <label class="infor_name">外借订单号</label>
                    <input type="text" class="input-middle" name="orderNo" id="orderNo" value="${lendOutQueryDto.orderNo }"/>
                </li>

                <li>
                    <label class="infor_name">审核状态</label>
                    <select class="input-middle f_left" name="verifyStatus">
                        <option value="">全部</option>
                        <option <c:if test="${lendOutQueryDto.verifyStatus eq 1}">selected</c:if> value="1">审核中</option>
                        <option <c:if test="${lendOutQueryDto.verifyStatus eq 2}">selected</c:if> value="2">审核通过</option>
                        <option <c:if test="${lendOutQueryDto.verifyStatus eq 3}">selected</c:if> value="3">审核未通过</option>
                        <option <c:if test="${lendOutQueryDto.verifyStatus eq 4}">selected</c:if> value="4">已关闭</option>
                    </select>
                </li>

                <li>

                    <label class="infor_name">创建时间</label>
                    <input class="Wdate f_left input-smaller96 m0" type="text" placeholder="请选择日期" autocomplete="off"
                           onClick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endtime\')}'})"
                           name="addStartTime" id="starttime" value="${lendOutQueryDto.addStartTime }">

                    <div class="f_left ml1 mr1 mt4">-</div>
                    <input class="Wdate f_left input-smaller96" type="text" placeholder="请选择日期" autocomplete="off"
                           onClick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'starttime\')}'})"
                           name="addEndTime" id="endtime" value="${lendOutQueryDto.addEndTime }">
                </li>

                <li>
                    <label class="infor_name">归还状态</label>
                    <select class="input-middle f_left" name="returnStatus">
                        <option value="">全部</option>
                        <option <c:if test="${lendOutQueryDto.returnStatus eq 0}">selected</c:if> value="0">未归还</option>
                        <option <c:if test="${lendOutQueryDto.returnStatus eq 1}">selected</c:if> value="1">部分归还</option>
                        <option <c:if test="${lendOutQueryDto.returnStatus eq 2}">selected</c:if> value="2">已归还</option>
                    </select>
                </li>
            </ul>
            <ul>
                <li>
                    <label class="infor_name">产品名称</label>
                    <input type="text" class="input-middle" name="goodsName" id="goodsName" value="${lendOutQueryDto.goodsName }"/>
                </li>

                <li>
                    <label class="infor_name">订货号</label>
                    <input type="text" class="input-middle" name="skuNo" id="skuNo" value="${lendOutQueryDto.skuNo }"/>
                </li>

                <li>
                    <label class="infor_name">品牌</label>
                    <input type="text" class="input-middle" name="brandName" id="brandName" value="${lendOutQueryDto.brandName }"/>
                </li>

                <li>
                    <label class="infor_name">型号</label>
                    <input type="text" class="input-middle" name="model" id="model" value="${lendOutQueryDto.model }"/>
                </li>
            </ul>
            <div class="tcenter">
                <span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();" id="searchSpan">搜索</span>
                <span class="bt-small bg-light-blue bt-bg-style" onclick="reset();">重置</span>
                <span class="bg-light-blue bt-bg-style bt-small addtitle"
                      tabTitle='{"num":"customer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
												"link":"./wms/commodityLendOut/toAddLendOut.do","title":"新增外借"}'>新增外借</span>
            </div>
        </form>
    </div>
    <div  class="normal-list-page">
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th class="sorts">外借订单号</th>
                <th class="wid10">创建人</th>
                <th class="wid15">归属部门</th>
                <th class="wid15">借用企业</th>
                <th class="wid15">创建时间</th>
                <th class="wid15">审核状态</th>
                <th class="wid15">产品名称</th>
                <th class="wid10">订货号</th>
                <th class="wid15">品牌</th>
                <th class="wid15">型号</th>
                <th class="wid15">归还状态</th>
<%--                <th class="wid15">操作</th>--%>
            </tr>
            </thead>
            <tbody class="company">
            <c:if test="${not empty lendOutList}">
                <c:forEach items="${lendOutList}" var="lendOut" varStatus="status">
                    <tr>
<%--                        <td>--%>
<%--                            ${lendOut.orderNo}--%>
<%--                        </td>--%>
                        <td>
                        <a class="addtitle"
                           href="javascript:void(0);"
                           tabTitle='{ "link":"./wms/commodityLendOut/detail.do?lendOutId=${lendOut.id}","title":"外借订单详情页"}'>${lendOut.orderNo}</a>
                        </td>

                        <td>
                            ${lendOut.creator}
                        </td>
                        <td>
                            ${lendOut.belongDepartment}
                        </td>
                        <td>
                            ${lendOut.borrowTraderName}
                        </td>
                        <td>
                            ${lendOut.addTime}
                        </td>
                        <td>
                            <c:choose>
                                <c:when test="${lendOut.verifyStatus eq 0}">
                                    待审核
                                </c:when>
                                <c:when test="${lendOut.verifyStatus eq 1}">
                                    审核中
                                </c:when>
                                <c:when test="${lendOut.verifyStatus eq 2}">
                                    审核通过
                                </c:when>
                                <c:otherwise>
                                    审核不通过
                                </c:otherwise>
                            </c:choose>
                        </td>

                        <td>
                            <a class="addtitle"
                               href="javascript:void(0);"
                               tabTitle='{ "link":"./goods/goods/viewbaseinfo.do?goodsId=${lendOut.goodsId}","title":"产品信息"}'>${lendOut.goodsName}</a>
                        </td>

                        <td>
                                ${lendOut.skuNo}
                        </td>
                        <td>
                                ${lendOut.brandName}
                        </td>
                        <td>
                                ${lendOut.model}
                        </td>
                        <td>
                            <c:choose>
                                <c:when test="${lendOut.returnStatus eq 0}">
                                    未归还
                                </c:when>
                                <c:when test="${lendOut.returnStatus eq 1}">
                                    部分归还
                                </c:when>
                                <c:when test="${lendOut.returnStatus eq 2}">
                                    已归还
                                </c:when>
                            </c:choose>
                        </td>
                    </tr>
                </c:forEach>
            </c:if>
            </tbody>
        </table>
        <c:if test="${empty lendOutList}">
            <div class="noresult">查询无结果！请尝试使用其他搜索条件。</div>
        </c:if>
        <tags:page page="${page}"/>
    </div>
</div>
<%@ include file="../../common/footer.jsp"%>