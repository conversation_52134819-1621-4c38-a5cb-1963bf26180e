<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.finance.mapper.RPayApplyJCapitalBillMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.RPayApplyJCapitalBill">
    <!--@mbg.generated-->
    <!--@Table T_R_PAY_APPLY_J_CAPITAL_BILL-->
    <id column="R_PAY_APPLY_J_CAPITAL_BILL_ID" jdbcType="INTEGER" property="rPayApplyJCapitalBillId" />
    <result column="PAY_APPLY_ID" jdbcType="INTEGER" property="payApplyId" />
    <result column="CAPITAL_BILL_ID" jdbcType="INTEGER" property="capitalBillId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    R_PAY_APPLY_J_CAPITAL_BILL_ID, PAY_APPLY_ID, CAPITAL_BILL_ID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_R_PAY_APPLY_J_CAPITAL_BILL
    where R_PAY_APPLY_J_CAPITAL_BILL_ID = #{rPayApplyJCapitalBillId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_R_PAY_APPLY_J_CAPITAL_BILL
    where R_PAY_APPLY_J_CAPITAL_BILL_ID = #{rPayApplyJCapitalBillId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="R_PAY_APPLY_J_CAPITAL_BILL_ID" keyProperty="rPayApplyJCapitalBillId" parameterType="com.vedeng.erp.finance.domain.entity.RPayApplyJCapitalBill" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_PAY_APPLY_J_CAPITAL_BILL (PAY_APPLY_ID, CAPITAL_BILL_ID)
    values (#{payApplyId,jdbcType=INTEGER}, #{capitalBillId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="R_PAY_APPLY_J_CAPITAL_BILL_ID" keyProperty="rPayApplyJCapitalBillId" parameterType="com.vedeng.erp.finance.domain.entity.RPayApplyJCapitalBill" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_PAY_APPLY_J_CAPITAL_BILL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="payApplyId != null">
        PAY_APPLY_ID,
      </if>
      <if test="capitalBillId != null">
        CAPITAL_BILL_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="payApplyId != null">
        #{payApplyId,jdbcType=INTEGER},
      </if>
      <if test="capitalBillId != null">
        #{capitalBillId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.finance.domain.entity.RPayApplyJCapitalBill">
    <!--@mbg.generated-->
    update T_R_PAY_APPLY_J_CAPITAL_BILL
    <set>
      <if test="payApplyId != null">
        PAY_APPLY_ID = #{payApplyId,jdbcType=INTEGER},
      </if>
      <if test="capitalBillId != null">
        CAPITAL_BILL_ID = #{capitalBillId,jdbcType=INTEGER},
      </if>
    </set>
    where R_PAY_APPLY_J_CAPITAL_BILL_ID = #{rPayApplyJCapitalBillId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.finance.domain.entity.RPayApplyJCapitalBill">
    <!--@mbg.generated-->
    update T_R_PAY_APPLY_J_CAPITAL_BILL
    set PAY_APPLY_ID = #{payApplyId,jdbcType=INTEGER},
      CAPITAL_BILL_ID = #{capitalBillId,jdbcType=INTEGER}
    where R_PAY_APPLY_J_CAPITAL_BILL_ID = #{rPayApplyJCapitalBillId,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_R_PAY_APPLY_J_CAPITAL_BILL
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="PAY_APPLY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when R_PAY_APPLY_J_CAPITAL_BILL_ID = #{item.rPayApplyJCapitalBillId,jdbcType=INTEGER} then #{item.payApplyId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CAPITAL_BILL_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when R_PAY_APPLY_J_CAPITAL_BILL_ID = #{item.rPayApplyJCapitalBillId,jdbcType=INTEGER} then #{item.capitalBillId,jdbcType=INTEGER}
        </foreach>
      </trim>
    </trim>
    where R_PAY_APPLY_J_CAPITAL_BILL_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.rPayApplyJCapitalBillId,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_R_PAY_APPLY_J_CAPITAL_BILL
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="PAY_APPLY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.payApplyId != null">
            when R_PAY_APPLY_J_CAPITAL_BILL_ID = #{item.rPayApplyJCapitalBillId,jdbcType=INTEGER} then #{item.payApplyId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CAPITAL_BILL_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.capitalBillId != null">
            when R_PAY_APPLY_J_CAPITAL_BILL_ID = #{item.rPayApplyJCapitalBillId,jdbcType=INTEGER} then #{item.capitalBillId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
    </trim>
    where R_PAY_APPLY_J_CAPITAL_BILL_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.rPayApplyJCapitalBillId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="R_PAY_APPLY_J_CAPITAL_BILL_ID" keyProperty="rPayApplyJCapitalBillId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_PAY_APPLY_J_CAPITAL_BILL
    (PAY_APPLY_ID, CAPITAL_BILL_ID)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.payApplyId,jdbcType=INTEGER}, #{item.capitalBillId,jdbcType=INTEGER})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="R_PAY_APPLY_J_CAPITAL_BILL_ID" keyProperty="rPayApplyJCapitalBillId" parameterType="com.vedeng.erp.finance.domain.entity.RPayApplyJCapitalBill" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_PAY_APPLY_J_CAPITAL_BILL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="rPayApplyJCapitalBillId != null">
        R_PAY_APPLY_J_CAPITAL_BILL_ID,
      </if>
      PAY_APPLY_ID,
      CAPITAL_BILL_ID,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="rPayApplyJCapitalBillId != null">
        #{rPayApplyJCapitalBillId,jdbcType=INTEGER},
      </if>
      #{payApplyId,jdbcType=INTEGER},
      #{capitalBillId,jdbcType=INTEGER},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="rPayApplyJCapitalBillId != null">
        R_PAY_APPLY_J_CAPITAL_BILL_ID = #{rPayApplyJCapitalBillId,jdbcType=INTEGER},
      </if>
      PAY_APPLY_ID = #{payApplyId,jdbcType=INTEGER},
      CAPITAL_BILL_ID = #{capitalBillId,jdbcType=INTEGER},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="R_PAY_APPLY_J_CAPITAL_BILL_ID" keyProperty="rPayApplyJCapitalBillId" parameterType="com.vedeng.erp.finance.domain.entity.RPayApplyJCapitalBill" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_PAY_APPLY_J_CAPITAL_BILL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="rPayApplyJCapitalBillId != null">
        R_PAY_APPLY_J_CAPITAL_BILL_ID,
      </if>
      <if test="payApplyId != null">
        PAY_APPLY_ID,
      </if>
      <if test="capitalBillId != null">
        CAPITAL_BILL_ID,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="rPayApplyJCapitalBillId != null">
        #{rPayApplyJCapitalBillId,jdbcType=INTEGER},
      </if>
      <if test="payApplyId != null">
        #{payApplyId,jdbcType=INTEGER},
      </if>
      <if test="capitalBillId != null">
        #{capitalBillId,jdbcType=INTEGER},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="rPayApplyJCapitalBillId != null">
        R_PAY_APPLY_J_CAPITAL_BILL_ID = #{rPayApplyJCapitalBillId,jdbcType=INTEGER},
      </if>
      <if test="payApplyId != null">
        PAY_APPLY_ID = #{payApplyId,jdbcType=INTEGER},
      </if>
      <if test="capitalBillId != null">
        CAPITAL_BILL_ID = #{capitalBillId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
</mapper>