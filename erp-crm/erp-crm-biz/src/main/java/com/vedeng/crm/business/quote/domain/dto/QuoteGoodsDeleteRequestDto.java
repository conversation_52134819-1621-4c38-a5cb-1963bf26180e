package com.vedeng.crm.business.quote.domain.dto;

import com.vedeng.common.core.utils.validator.group.DefaultGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class QuoteGoodsDeleteRequestDto {

    /**
     * 报价单id
     */
    @NotNull(message = "报价单id不能为空", groups = DefaultGroup.class)
    private Integer quoteorderId;

    /**
     * 待删除报价明细
     */
    @NotEmpty(message = "待删除明细不能为空", groups = DefaultGroup.class)
    private List<DeleteDetail> deleteDetailList;

    @Data
    public static class DeleteDetail {

        /**
         * 报价单明细id
         */
        @NotNull(message = "报价单明细id不能为空", groups = DefaultGroup.class)
        private Integer quoteorderGoodsId;

        /**
         * sku VXXXXX
         */
        @NotBlank(message = "sku不能为空", groups = DefaultGroup.class)
        private String sku;
    }
}
