package com.vedeng.goods.manager.validator.InvalidatorChain.spu.Invalid;

import com.google.common.base.CharMatcher;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.exception.InvalidatorChainException;
import com.vedeng.goods.manager.validator.GoodsInvalidatorChain;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.vedeng.goods.manager.validator.enums.GoodsInvalidatorErrorEnum.*;

@Service
public class SpuNoRelatedOrderForConditionFiveInvalidator extends GoodsInvalidatorChain {
    @Autowired
    AfterSalesMapper afterSalesMapper;

    @Resource
    SaleorderMapper saleorderMapper;

    @Resource
    CoreSkuMapper coreSkuMapper;

    @Override
    public void doInvalid() {
        String spuNo = getSpuNo();
        List<String> skuNoList = getSkuNoList();
        if(CollectionUtils.isEmpty(skuNoList)){
            return ;
        }
        StringBuffer skus = new StringBuffer();
        StringBuffer orders = new StringBuffer();
        for (String skuNo : skuNoList) {
            List<Integer> goodsIds = coreSkuMapper.getGoodsIdBySkuNo(skuNo);
            List<AfterSales> afterSalesList = afterSalesMapper.getRelatedAftersalesOrderConditionFive(goodsIds);
            List<Saleorder> saleorderList = saleorderMapper.getRelatedSaleOrderInprocess(skuNo);
            if (CollectionUtils.isNotEmpty(saleorderList) || CollectionUtils.isNotEmpty(afterSalesList)) {
                skus.append(skuNo);
                skus.append(",");
                if (CollectionUtils.isNotEmpty(saleorderList)) {
                    String collect = saleorderList.stream().map(e -> e.getSaleorderNo()).collect(Collectors.joining(","));
                    orders.append(collect);
                    orders.append(",");
                }
                if (CollectionUtils.isNotEmpty(afterSalesList)) {
                    String collect = afterSalesList.stream().map(e -> e.getAfterSalesNo()).collect(Collectors.joining(","));
                    orders.append(collect);
                    orders.append(",");
                }
            }
        }
        if (skus.length() > 0 && orders.length() > 0) {
            String tempSkusList = SpuNoRelatedOrderForConditionFiveInvalidatorError.getMessage().replace("skus", skus.substring(0,skus.length()-1));
            String skusList = tempSkusList.replace("orderNo", orders.substring(0,orders.length()-1));
            throw new InvalidatorChainException(SpuNoRelatedOrderForConditionFiveInvalidatorError.getCode(), skusList);
        }
    }
}
