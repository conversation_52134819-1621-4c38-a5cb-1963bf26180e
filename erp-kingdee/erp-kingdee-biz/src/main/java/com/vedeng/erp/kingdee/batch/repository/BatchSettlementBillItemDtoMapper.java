package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchSettlementBillItemDto;import org.apache.ibatis.annotations.Param;import java.util.List;


/**
 * <AUTHOR>
 * @description ${end}
 * @date 2023/12/9 12:59
 **/
public interface BatchSettlementBillItemDtoMapper {
    int deleteByPrimaryKey(Integer settleItemBillId);

    int insert(BatchSettlementBillItemDto record);

    int insertSelective(BatchSettlementBillItemDto record);

    BatchSettlementBillItemDto selectByPrimaryKey(Integer settleItemBillId);

    int updateByPrimaryKeySelective(BatchSettlementBillItemDto record);

    int updateByPrimaryKey(BatchSettlementBillItemDto record);

    List<BatchSettlementBillItemDto> selectBySettleBillId(@Param("settleBillId")Integer settleBillId);



    /**
     * 正向的
     *
     * @param settleBillId
     * @return
     */
    List<BatchSettlementBillItemDto> selectBySettleBillIdAndInvoiceStatusBlue(@Param("settleBillId") Integer settleBillId);

    /**
     * 售后的的
     *
     * @param settleBillId
     * @return
     */
    List<BatchSettlementBillItemDto> selectBySettleBillIdAndInvoiceStatusRed(@Param("settleBillId") Integer settleBillId);

    /**
     * 根据采购单明细id 查询 采购的返利明细
     *
     * @param businessItemIds
     * @return
     */
    List<BatchSettlementBillItemDto> selectByBusinessItemIdAndBuyOrder(@Param("list") List<Integer> businessItemIds);

    List<BatchSettlementBillItemDto> selectByBusinessItemIdAndAfterSale(@Param("list")List<Integer> businessItemIds);


}