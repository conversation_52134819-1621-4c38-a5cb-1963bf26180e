package com.vedeng.common.shiro;



import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.redis.RedisUtils;
import org.apache.batik.dom.util.XMLHttpRequest;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @ClassName CurrentLimitInterceptor.java
 * @Description TODO  ip限流拦截器
 * @createTime 2021年02月04日 17:31:00
 */
public class CurrentLimitInterceptor implements HandlerInterceptor {
    @Resource
    private RedisUtils redisUtils;

    @Value("${redis_dbtype}")
    protected String dbType;

    @Value("${current_LimitInterceptor_Flag}")
    private String currentLimitInterceptorFlag;

    private static final Logger logger = LoggerFactory.getLogger(CurrentLimitInterceptor.class);

    @Override
    public boolean preHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o) throws Exception {

            String ipAddress = getIpAddress(httpServletRequest);
            String requestUri =StringUtils.lowerCase( httpServletRequest.getRequestURI());
        try {
            //ajax
            User currentUser = (User) httpServletRequest.getSession().getAttribute(ErpConst.CURR_USER);
            String[] config = StringUtils.split(currentLimitInterceptorFlag, "_");
            if (config == null || config.length < 4) {
                return true;
            }
            if(whiteUrl(requestUri)||currentUser==null) {
            	return true;
            }
            String openFlag = config[0];
            int maxRute = NumberUtils.toInt(config[1], 10);
            int expire = NumberUtils.toInt(config[2], 3);
            int lockTime = NumberUtils.toInt(config[3], 300);
            //同一个ip，同一个URL 3秒钟8次 改成  同一个用户， 同一个ip，同一个URL 3秒钟8次
            String userName=currentUser == null ? null : currentUser.getUsername();
            String encodeUrl=toLetter(requestUri);
            String checkAccessKey = dbType + "denyip:"+userName + ipAddress+encodeUrl;

            if(requestUri.startsWith("/goods/vgoods/static/clearlock.do")){
                    redisUtils.del(dbType + "denyip:"+userName +httpServletRequest.getParameter("checkAccessKey"));
                return true;
            }
            if (StringUtils.equalsIgnoreCase("XMLHttpRequest", httpServletRequest.getHeader("X-Requested-With"))) {
                return true;
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(redisUtils.get(checkAccessKey)) && "true".equals(openFlag)) {
                logger.error("过于频繁访问记录{}，请求地址：{}，用户：{}，访问地址：{}",checkAccessKey, requestUri,userName, ipAddress);
                httpServletResponse.setHeader("Content-type", "text/html;charset=UTF-8");
                // 用UTF-8转码，而不是用默认的ISO8859
                httpServletResponse.setCharacterEncoding("UTF-8");
                // response.setContentType("text/html;charset=UTF-8");
                httpServletResponse.getWriter().write("<html lang=\"en\">\n" +
                        "<head>\n" +
                        "    <meta charset=\"UTF-8\">\n" +
                        "    <title>操作失败</title>\n" +
                        "    <link rel=\"stylesheet\" href=\"/static/css/content.css\" />\n" +
                        "    <link rel=\"stylesheet\" href=\"/static/css/general.css\" />\n" +
                        "    <link rel=\"stylesheet\" href=\"/static/css/manage.css\" />\n" +
                        "     <script src=\"/webjars/ezadmin/plugins/jquery/jquery.min.js\"></script>\n" +
                        "</head>\n" +
                        "<body>\n" +
                        "    <div class=\"operate\">\n" +
                        "        <div class=\"false\">\n" +
                        "                            操作失败！<br/>\n" +
                        "            <br>\n" +
                        "\t\t\t\t<span class=\"jump\">系统判断您当前操作过于频繁,已经进行拦截。\n" +
                        "\t\t\t\t<br>\n" +
                        "\t\t\t\t" +lockTime+"秒后系统自动解除限制；"+
                        "\t\t\t\t<br>\n" +
                        "\t\t\t\t您也可以点击<a   id='unlock' target='_blank'>解锁</a>解除控制,如有疑问,请联系研发部。\n" +
                        "\t\t\t\t<br>\n" +
                        "\t\t\t\t<a href=\"javascript:window.history.back();\">返回</a>上一个页面。\n" +
                        "\t\t\t\t</span>\n" +
                        "            <script>\n" +
                        "\t\t\t\t$(function(){\n" +
                        "\t\t\t\t\t$(\"#unlock\").click(function(){\n" +
                        "\t\t\t\t\t\t$.get(\"/goods/vgoods/static/clearLock.do?from=lock&checkAccessKey="+ipAddress+encodeUrl+"\",function(data){ \n" +
                        "\t\t\t\t\t\tvar dd=JSON.parse(data);\n" +
                        "\t\t\t\t\t\tif(dd.code==0){\n" +
                        "\t\t\t\t\t\t\talert(\"解锁成功\"); location.reload();\n" +
                        "\t\t\t\t\t\t}\n" +
                        "\t\t\t\t\t\t})\n" +
                        "\t\t\t\t\t})\t\n" +
                        "\t\t\t\t})\n" +
                        "\t\t\t</script>\n" +
                        "        </div>\n" +
                        "        <div class=\"false-img\"><img src=\"/static/images/operatefalse.jpg\" /></div>\n" +
                        "    </div>\n" +
                        "</body>\n" +
                        "</html>");
                return false;
            }
            //
            String accessKey = "";
            try {
                accessKey = dbType + "accessKey:"+userName+ ipAddress+encodeUrl;
            } catch (Exception e) {
                //
                return true;
            }
            if (org.apache.commons.lang3.StringUtils.isBlank(accessKey)) {
                return true;
            }
            //expire秒内同一ip同一用户访问同一uri最多maxRute次
            boolean access = redisUtils.access(accessKey, maxRute, expire);
            if (!access) {
                //一旦触发，则5分钟内禁止访问
                logger.error("过于频繁访问记录，请求地址 access：{}，用户：{}，访问地址：{} ,locktime:{}",
                        requestUri, userName, ipAddress,lockTime);
                redisUtils.set(checkAccessKey, "1", lockTime);
            }
        }catch (Exception e){
            //redis挂了之类的
            logger.error("限流拦截器{}{}",ipAddress,requestUri,e);
        }
        return true;
    }

    private boolean whiteUrl(String requestUri) {
        //当内外网断网，电子签章重试机制触发时，会导致多次请求
        if(requestUri.startsWith("/orderstream/saleorder/contract_template/print")||
                //出库单打印
                requestUri.indexOf("printoutorder")>0
                ||requestUri.indexOf("upload")>0
    ||requestUri.startsWith("/wms")
    ||requestUri.startsWith("/system")
    ||requestUri.startsWith("/checkSession")
                ||requestUri.startsWith("/warehouseOutIn/regenerateWarehouseInReport")
                ||requestUri.startsWith("/warehouseOutIn/regenerateWarehouseOutReport")
                ||requestUri.startsWith("/finance/auto/settlementLoading")
                ||requestUri.startsWith("/finance/auto/ignoreLoading")
                ||requestUri.startsWith("/finance/invoiceApplyAutoAudit/advanceLoading")
                ||requestUri.startsWith("/invoiceAuto/api/openLoading")
        ) {
            return true;
        }
        return false;
    }

	@Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) throws Exception {

    }

    public  String getIpAddress(HttpServletRequest request) throws IOException {
        // 获取请求主机IP地址,如果通过代理进来，则透过防火墙获取真实IP地址
        String ip = request.getHeader("X-Forwarded-For");

        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("Proxy-Client-IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("WL-Proxy-Client-IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("HTTP_CLIENT_IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("HTTP_X_FORWARDED_FOR");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getRemoteAddr();
            }
        } else if (ip.length() > 15) {
            String[] ips = ip.split(",");
            for (int index = 0; index < ips.length; index++) {
                String strIp = (String) ips[index];
                if (! ("unknown".equalsIgnoreCase(strIp))) {
                    ip = strIp;
                    break;
                }
            }
        }
        return ip;
    }

    String toLetter(String str){
        StringBuilder sb=new StringBuilder();
        char[] cc=str.toCharArray();
        for (int i = 0; i < cc.length; i++) {
            if(Character.isLetterOrDigit(cc[i])){
                sb.append(cc[i]);
            }else{
                sb.append("_");
            }
        }
        return sb.toString();
    }
}
