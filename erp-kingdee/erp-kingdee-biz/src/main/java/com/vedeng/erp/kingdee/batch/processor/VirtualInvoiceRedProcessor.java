package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.finance.enums.InvoiceTaxTypeEnum;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 返利红虚拟票 处理类
 * @date 2022/11/18 13:55
 */

@Service
@Slf4j
public class VirtualInvoiceRedProcessor implements ItemProcessor<BatchSettlementBillDto, List<BatchVirtualInvoiceDto>> {

    @Autowired
    private BatchBuyorderGoodsDtoMapper batchBuyorderGoodsDtoMapper;

    @Autowired
    private BatchSettlementBillItemDtoMapper batchSettlementBillItemDtoMapper;

    @Autowired
    private BatchInvoiceDtoMapper batchInvoiceDtoMapper;

    @Autowired
    private BatchInvoiceDetailDtoMapper batchInvoiceDetailDtoMapper;

    @Autowired
    private BatchVirtualInvoiceDtoMapper batchVirtualInvoiceDtoMapper;

    @Autowired
    private BatchAfterSalesDtoMapper batchAfterSalesDtoMapper;

    @Autowired
    private BatchAfterSalesGoodsDtoMapper batchAfterSalesGoodsDtoMapper;

    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;


    @Override
    public List<BatchVirtualInvoiceDto> process(BatchSettlementBillDto batchSettlementBillDto) throws Exception {

        log.info("VirtualInvoiceRedProcessor.process：{}", JSON.toJSONString(batchSettlementBillDto));


        List<BatchSettlementBillItemDto> afterSalesSettleBillList = batchSettlementBillItemDtoMapper.selectBySettleBillIdAndInvoiceStatusRed(batchSettlementBillDto.getSettleBillId());

        if (CollUtil.isEmpty(afterSalesSettleBillList)) {
            log.info("未开票结算明细为空：{}", JSON.toJSONString(batchSettlementBillDto));
            return null;
        }

        Map<Integer, BatchSettlementBillItemDto> afterSalesSettleBill2BuyOrderGoodsMap = afterSalesSettleBillList.stream().collect(Collectors.toMap(BatchSettlementBillItemDto::getBuyOrderGoodsId, x -> x, (k1, k2) -> k1));
        Set<Integer> integers = afterSalesSettleBill2BuyOrderGoodsMap.keySet();
        List<Integer> buyOrderGoodsIds = CollUtil.newArrayList(integers);
        if (CollUtil.isEmpty(buyOrderGoodsIds)) {
            log.error("采购明细id为空：{}", JSON.toJSONString(buyOrderGoodsIds));
            return null;
        }
        List<BatchBuyorderGoodsDto> buyOrderGoodsDtoList = batchBuyorderGoodsDtoMapper.findByBuyorderGoodsIdInAndIsDelete(buyOrderGoodsIds, 0);
        if (CollUtil.isEmpty(buyOrderGoodsDtoList)) {
            log.error("采购明细为空：{}", JSON.toJSONString(buyOrderGoodsIds));
            return null;
        }

        List<Integer> outAfterSaleList = getAfterSalesGoodsOut(afterSalesSettleBillList);

        Map<Integer, BatchBuyorderGoodsDto> buyOrderGoods2Map = buyOrderGoodsDtoList.stream().collect(Collectors.toMap(BatchBuyorderGoodsDto::getBuyorderGoodsId, x -> x, (k1, k2) -> k1));

        // 正向的返利单
        List<BatchSettlementBillItemDto> buyOrderSettlementItemList = batchSettlementBillItemDtoMapper.selectByBusinessItemIdAndBuyOrder(buyOrderGoodsIds);
        Map<Integer, BatchSettlementBillItemDto> buyOrderSettlementItem2Map = buyOrderSettlementItemList.stream().collect(Collectors.toMap(BatchSettlementBillItemDto::getBusinessItemId, x -> x, (k1, k2) -> k1));
        // 按行校验
        List<BatchSettlementBillItemDto> matchAllRebate = afterSalesSettleBillList.stream().filter(x -> {

            BatchBuyorderGoodsDto batchBuyorderGoodsDto = buyOrderGoods2Map.get(x.getBuyOrderGoodsId());
            if (Objects.isNull(batchBuyorderGoodsDto)) {
                return false;
            }
            BatchSettlementBillItemDto batchSettlementBillItemDto = buyOrderSettlementItem2Map.get(x.getBuyOrderGoodsId());
            Integer num = Optional.ofNullable(batchBuyorderGoodsDto.getNum()).orElse(0);
            BigDecimal price = Optional.ofNullable(batchBuyorderGoodsDto.getPrice()).orElse(BigDecimal.ZERO);
            BigDecimal amount = price.multiply(new BigDecimal(num));
            batchBuyorderGoodsDto.setAmount(amount);
            x.setBuyOrderPrice(price);
            return batchSettlementBillItemDto.getAmount().compareTo(amount) == 0;
        }).collect(Collectors.toList());


        List<BatchSettlementBillItemDto> matchPartialRebate = afterSalesSettleBillList.stream().filter(x -> {
            BatchBuyorderGoodsDto batchBuyorderGoodsDto = buyOrderGoods2Map.get(x.getBuyOrderGoodsId());
            if (Objects.isNull(batchBuyorderGoodsDto)) {
                return false;
            }
            BatchSettlementBillItemDto batchSettlementBillItemDto = buyOrderSettlementItem2Map.get(x.getBuyOrderGoodsId());
            Integer num = Optional.ofNullable(batchBuyorderGoodsDto.getNum()).orElse(0);
            BigDecimal price = Optional.ofNullable(batchBuyorderGoodsDto.getPrice()).orElse(BigDecimal.ZERO);
            BigDecimal amount = price.multiply(new BigDecimal(num));
            batchBuyorderGoodsDto.setAmount(amount);
            return batchSettlementBillItemDto.getAmount().compareTo(amount) != 0;
        }).collect(Collectors.toList());


        // 找票信息
        List<BatchInvoiceDto> invoiceDtoList = batchInvoiceDtoMapper.selectBuyOrderRedInvoiceByAfterSaleId(afterSalesSettleBillList.get(0).getBusinessId());
        List<BatchInvoiceDto> enableInvoice = getEnableInvoice(invoiceDtoList);


        List<BatchInvoiceDetailDto> invoiceDetailByInvoiceIdList = Collections.emptyList();
        Map<Integer, List<BatchInvoiceDetailDto>> invoiceDetail2Map = new HashMap<>();
        if (CollUtil.isNotEmpty(enableInvoice)) {
            // 有实际票 全部返利聚合（排除录票错误的）
            List<Integer> invoiceIds = enableInvoice.stream().map(BatchInvoiceDto::getInvoiceId).collect(Collectors.toList());
            invoiceDetailByInvoiceIdList = batchInvoiceDetailDtoMapper.getInvoiceDetailByInvoiceIdList(invoiceIds);

            invoiceDetail2Map = invoiceDetailByInvoiceIdList.stream().collect(Collectors.groupingBy(BatchInvoiceDetailDto::getDetailgoodsId));
        }

        List<BatchInvoiceDto> enableInvoiceChecked = enableInvoice;
        List<BatchSettlementBillItemDto> matchAllRebateChecked = matchAllRebate;

        if (CollUtil.isNotEmpty(matchAllRebate)) {

            Set<Integer> errorInvoices = new HashSet<>();
            List<BatchSettlementBillItemDto> errorData = checkAndGetErrorData(matchAllRebate, invoiceDetail2Map, errorInvoices);

            if (CollUtil.isNotEmpty(errorData)) {
                log.error("业务数据异常：存在异常录入的全部返利的票明细：{}", JSON.toJSONString(errorData));
            }
            // 排除异常数据
            matchAllRebateChecked = getMatchAllRebateChecked(errorData, matchAllRebateChecked,outAfterSaleList);
            // 排除异常票
            enableInvoiceChecked = getEnableInvoiceChecked(enableInvoiceChecked, errorInvoices);
        }

        List<BatchVirtualInvoiceDto> result = new LinkedList<>();
        // 全部返利
        log.info("全部返利依托的满足条件的明细行数据：{}",JSON.toJSONString(matchAllRebateChecked));
        if (CollUtil.isNotEmpty(matchAllRebateChecked)) {
            result.addAll(fullRebate(matchAllRebateChecked, buyOrderGoodsDtoList));
        }

        // 部分返利 依据实物票生成
        log.info("部分返利依托的满足条件的实物票数据：{}",JSON.toJSONString(enableInvoiceChecked));
        if (CollUtil.isNotEmpty(enableInvoiceChecked)) {
            Map<Integer, BatchSettlementBillItemDto> matchPartialRebate2Map = matchPartialRebate.stream().collect(Collectors.toMap(BatchSettlementBillItemDto::getBuyOrderGoodsId, x -> x, (k1, k2) -> k1));
            result.addAll(partialRebate(matchPartialRebate2Map, buyOrderGoods2Map, enableInvoiceChecked, invoiceDetailByInvoiceIdList));
        }


        if (CollUtil.isEmpty(result)) {
            return null;
        }

        return result;
    }

    /**
     * 反回出库单售后单明细id
     * @param batchSettlementBillItemDtoList
     * @return List<Integer>
     */
    private List<Integer> getAfterSalesGoodsOut(List<BatchSettlementBillItemDto> batchSettlementBillItemDtoList) {
        List<Integer> afterSalesGoodsIds = batchSettlementBillItemDtoList.stream().map(BatchSettlementBillItemDto::getBusinessItemId).collect(Collectors.toList());

        List<BatchAfterSalesGoodsDto> byAfterSalesGoodsIdList = batchAfterSalesGoodsDtoMapper.findByAfterSalesGoodsIds(afterSalesGoodsIds);
        List<BatchWarehouseGoodsOutInItemDto> batchWarehouseGoodsOutInItemDtos = batchWarehouseGoodsOutInItemDtoMapper.selectByRelatedIdsAndOperateType(afterSalesGoodsIds, ErpConstant.SIX);
        List<Integer> outAfterSaleList = byAfterSalesGoodsIdList.stream().filter(x -> {
            BigDecimal realNum = new BigDecimal(x.getNum().toString());
            BigDecimal outNum = BigDecimal.ZERO;
            if (CollUtil.isNotEmpty(batchWarehouseGoodsOutInItemDtos)) {
                Optional<BatchWarehouseGoodsOutInItemDto> first = batchWarehouseGoodsOutInItemDtos.stream().filter(y -> y.getRelatedId().equals(x.getAfterSalesGoodsId())).findFirst();
                if (first.isPresent()) {
                    outNum = first.get().getNum().abs();
                }
            }
            return realNum.compareTo(outNum) <= 0;

        }).map(BatchAfterSalesGoodsDto::getAfterSalesGoodsId).collect(Collectors.toList());
        return outAfterSaleList;
    }

    private List<BatchInvoiceDto> getEnableInvoiceChecked(List<BatchInvoiceDto> enableInvoiceChecked, Set<Integer> errorInvoices) {

        if (CollUtil.isEmpty(errorInvoices)) {
            return enableInvoiceChecked;
        }

        return enableInvoiceChecked.stream().filter(b -> !errorInvoices.contains(b.getInvoiceId())).collect(Collectors.toList());
    }

    private List<BatchSettlementBillItemDto> getMatchAllRebateChecked(List<BatchSettlementBillItemDto> errorData, List<BatchSettlementBillItemDto> matchAllRebateChecked,List<Integer> outAfterSaleList) {
        if (CollUtil.isEmpty(outAfterSaleList)) {
            return Collections.emptyList();
        }
        matchAllRebateChecked = matchAllRebateChecked.stream().filter(x -> {
            Optional<BatchSettlementBillItemDto> any = Optional.empty();
            if (CollUtil.isNotEmpty(errorData)) {
                 any = errorData.stream().filter(a -> a.getSettleItemBillId().equals(x.getSettleItemBillId())).findAny();
            }

            boolean contains = outAfterSaleList.contains(x.getBusinessItemId());
            return !any.isPresent()&&contains;
        }).collect(Collectors.toList());
        return matchAllRebateChecked;
    }

    /**
     * 获取异常数据 全部返利的明细但是录票了的
     *
     * @param matchAllRebate    全部返利行
     * @param invoiceDetail2Map 票明细
     * @param errorInvoices     异常票集合
     * @return List<BatchSettlementBillItemDto> 异常明细行
     */
    private List<BatchSettlementBillItemDto> checkAndGetErrorData(List<BatchSettlementBillItemDto> matchAllRebate, Map<Integer, List<BatchInvoiceDetailDto>> invoiceDetail2Map, Set<Integer> errorInvoices) {

        return matchAllRebate.stream().filter(x -> {
            List<BatchInvoiceDetailDto> batchInvoiceDetailDtos = invoiceDetail2Map.get(x.getBuyOrderGoodsId());
            if (CollUtil.isNotEmpty(batchInvoiceDetailDtos)) {
                Set<Integer> collect = batchInvoiceDetailDtos.stream().map(BatchInvoiceDetailDto::getInvoiceId).collect(Collectors.toSet());
                errorInvoices.addAll(collect);
            }
            return CollUtil.isNotEmpty(batchInvoiceDetailDtos);
        }).collect(Collectors.toList());
    }

    /**
     * 排除生成虚拟票的
     *
     * @param invoiceDtoList 所有蓝票
     * @return List<BatchInvoiceDto>
     */
    private List<BatchInvoiceDto> getEnableInvoice(List<BatchInvoiceDto> invoiceDtoList) {

        if (CollUtil.isEmpty(invoiceDtoList)) {
            return Collections.emptyList();
        }
        List<Integer> invoiceIds = invoiceDtoList.stream().map(BatchInvoiceDto::getInvoiceId).collect(Collectors.toList());
        List<BatchVirtualInvoiceDto> batchVirtualInvoiceDtoList = batchVirtualInvoiceDtoMapper.selectBySourceInvoiceIds(invoiceIds);
        Map<Integer, List<BatchVirtualInvoiceDto>> virtualInvoice2Map = batchVirtualInvoiceDtoList.stream().collect(Collectors.groupingBy(BatchVirtualInvoiceDto::getSourceInvoiceId));
        return invoiceDtoList.stream().filter(x -> {
            boolean enable = x.getIsEnable().equals(ErpConstant.ONE);
            if (!enable) {
                return false;
            }
            return CollUtil.isEmpty(virtualInvoice2Map.get(x.getInvoiceId()));
        }).collect(Collectors.toList());
    }

    /**
     * 部分返利
     *
     * @param batchSettlementBillItem2Map  返利明细
     * @param buyOrderGoods2Map            商品
     * @param enableInvoice                有效票
     * @param invoiceDetailByInvoiceIdList 票明细
     * @return List<BatchVirtualInvoiceDto> 虚拟票
     */
    private List<BatchVirtualInvoiceDto> partialRebate(Map<Integer, BatchSettlementBillItemDto> batchSettlementBillItem2Map, Map<Integer, BatchBuyorderGoodsDto> buyOrderGoods2Map, List<BatchInvoiceDto> enableInvoice, List<BatchInvoiceDetailDto> invoiceDetailByInvoiceIdList) {

        Map<Integer, List<BatchInvoiceDetailDto>> invoiceDetail2Map = invoiceDetailByInvoiceIdList.stream().collect(Collectors.groupingBy(BatchInvoiceDetailDto::getInvoiceId));

        return enableInvoice.stream().map(x -> {

            List<BatchInvoiceDetailDto> detailDtoList = invoiceDetail2Map.get(x.getInvoiceId());

            List<BatchVirtualInvoiceItemDto> virtualInvoiceItemDtoList = detailDtoList.stream().map(a -> {

                BatchSettlementBillItemDto batchSettlementBillItemDto = batchSettlementBillItem2Map.get(a.getDetailgoodsId());
                if (Objects.isNull(batchSettlementBillItemDto)) {
                    // 非返利数据跳过
                    return null;
                }

                BatchVirtualInvoiceItemDto virtualInvoiceItemDto = new BatchVirtualInvoiceItemDto();
                virtualInvoiceItemDto.setSourceInvoiceItemId(a.getInvoiceDetailId());

                BatchBuyorderGoodsDto batchBuyorderGoodsDto = buyOrderGoods2Map.get(a.getDetailgoodsId());
                BigDecimal batchBuyOrderGoodsPrice = batchBuyorderGoodsDto.getPrice().abs();
                virtualInvoiceItemDto.setPrice(batchBuyOrderGoodsPrice.negate());
                BigDecimal billItemDtoPrice = batchSettlementBillItemDto.getPrice().abs();
                virtualInvoiceItemDto.setTotalAmount(a.getTotalAmount().abs().negate().multiply(billItemDtoPrice).divide(batchBuyOrderGoodsPrice.subtract(billItemDtoPrice), 2, RoundingMode.HALF_UP));
                BigDecimal num = virtualInvoiceItemDto.getTotalAmount().divide(virtualInvoiceItemDto.getPrice(), 2, RoundingMode.HALF_UP);
                virtualInvoiceItemDto.setNum(num);
                virtualInvoiceItemDto.setBusinessOrderItemId(batchSettlementBillItemDto.getBusinessItemId());
                return virtualInvoiceItemDto;
            }).filter(Objects::nonNull).collect(Collectors.toList());

            if (CollUtil.isEmpty(virtualInvoiceItemDtoList)) {
                // 整张红票无返利明细
                return null;
            }

            BatchSettlementBillItemDto one = batchSettlementBillItem2Map.entrySet().stream().findAny().get().getValue();

            BatchVirtualInvoiceDto batchVirtualInvoiceDto = new BatchVirtualInvoiceDto();
            BatchSettlementBillItemDto value = batchSettlementBillItem2Map.entrySet().stream().findAny().get().getValue();
            batchVirtualInvoiceDto.setSourceInvoiceId(x.getInvoiceId());
            batchVirtualInvoiceDto.setSettleBillId(value.getSettleBillId());
            batchVirtualInvoiceDto.setInvoiceNo(getPartialRebateVirtualInvoiceNo(one,x.getInvoiceNo()+"-FL"));
            batchVirtualInvoiceDto.setInvoiceCode(x.getInvoiceCode());
            batchVirtualInvoiceDto.setInvoiceProperty(x.getInvoiceProperty());
            batchVirtualInvoiceDto.setInvoiceType(x.getInvoiceType());
            batchVirtualInvoiceDto.setRatio(x.getRatio());
            batchVirtualInvoiceDto.setBusinessType(2);
            batchVirtualInvoiceDto.setBusinessOrderId(value.getBusinessId());
            batchVirtualInvoiceDto.setBusinessOrderNo(value.getBusinessNo());
            batchVirtualInvoiceDto.setColorType(1);
            batchVirtualInvoiceDto.setOpenInvoiceTime(new Date());
            // 计算返利生成的虚拟票明细的值
            BigDecimal amount = virtualInvoiceItemDtoList.stream().map(BatchVirtualInvoiceItemDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            batchVirtualInvoiceDto.setAmount(amount);
            batchVirtualInvoiceDto.setVirtualInvoiceItemDtoList(virtualInvoiceItemDtoList);
            return batchVirtualInvoiceDto;
        }).filter(Objects::nonNull).collect(Collectors.toCollection(LinkedList::new));

    }


    /**
     * 全部返利
     * 发票号：订单号后10位再加后缀-FL，发票金额：订单金额，税额：发票金额*13%普通发票，红蓝字：蓝字有效，不含税金额：发票金额-税额，票种：13%普通发票 发票代码：000000 发票类型：纸质
     *
     * @param batchSettlementBillItemDtoList 返利明细
     * @param buyOrderGoodsDtoList           商品明细
     * @return List<BatchVirtualInvoiceDto> 虚拟票
     */
    private List<BatchVirtualInvoiceDto> fullRebate(List<BatchSettlementBillItemDto> batchSettlementBillItemDtoList, List<BatchBuyorderGoodsDto> buyOrderGoodsDtoList) {

        BatchVirtualInvoiceDto batchVirtualInvoiceDto = new BatchVirtualInvoiceDto();


        String virtualInvoiceNo = getAllRebateVirtualInvoiceNo(batchSettlementBillItemDtoList.get(0));
        batchVirtualInvoiceDto.setInvoiceNo(virtualInvoiceNo);
        batchVirtualInvoiceDto.setInvoiceCode("000000");
        batchVirtualInvoiceDto.setInvoiceProperty(1);
        batchVirtualInvoiceDto.setSettleBillId(batchSettlementBillItemDtoList.get(0).getSettleBillId());
        batchVirtualInvoiceDto.setInvoiceType(InvoiceTaxTypeEnum.PLAIN_INVOICE_13.getCode());
        batchVirtualInvoiceDto.setRatio(InvoiceTaxTypeEnum.PLAIN_INVOICE_13.getTax());
        batchVirtualInvoiceDto.setBusinessType(2);
        batchVirtualInvoiceDto.setBusinessOrderId(batchSettlementBillItemDtoList.get(0).getBusinessId());
        batchVirtualInvoiceDto.setBusinessOrderNo(batchSettlementBillItemDtoList.get(0).getBusinessNo());
        batchVirtualInvoiceDto.setColorType(1);
        BigDecimal amount = batchSettlementBillItemDtoList.stream().map(BatchSettlementBillItemDto::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO).abs().negate();
        batchVirtualInvoiceDto.setAmount(amount);
        batchVirtualInvoiceDto.setOpenInvoiceTime(new Date());
        batchVirtualInvoiceDto.setSourceInvoiceId(0);

        List<BatchVirtualInvoiceItemDto> virtualInvoiceItemDtoList = batchSettlementBillItemDtoList.stream().map(x -> {
            BatchVirtualInvoiceItemDto a = new BatchVirtualInvoiceItemDto();
            a.setNum(x.getNumber());
            a.setPrice(x.getBuyOrderPrice().abs().negate());
            a.setBusinessOrderItemId(x.getBusinessItemId());
            a.setTotalAmount(x.getAmount().abs().negate());
            return a;
        }).collect(Collectors.toList());

        batchVirtualInvoiceDto.setVirtualInvoiceItemDtoList(virtualInvoiceItemDtoList);
        return CollUtil.newLinkedList(batchVirtualInvoiceDto);
    }

    /**
     * 部分返利票号
     *
     * @param data 单号
     * @return String
     */
    private String getPartialRebateVirtualInvoiceNo(BatchSettlementBillItemDto data,String blueInvoice) {

        BatchAfterSalesDto byAfterSalesId = batchAfterSalesDtoMapper.findByAfterSalesId(data.getBusinessId());

        List<Integer> afterSaleIds = batchAfterSalesDtoMapper.selectBySubjectTypeAndOrderNo(536, byAfterSalesId.getOrderNo());

        Integer integer = batchVirtualInvoiceDtoMapper.selectRedCountByInvoiceNo(afterSaleIds, blueInvoice);
        if (Objects.isNull(integer) || integer.equals(0)) {
            integer = 0;
        }
        integer +=1;
        if (integer < 10) {
            return "0" + integer+blueInvoice;
        }

        return integer+blueInvoice;
    }


    private String getAllRebateVirtualInvoiceNo(BatchSettlementBillItemDto data) {

        BatchAfterSalesDto byAfterSalesId = batchAfterSalesDtoMapper.findByAfterSalesId(data.getBusinessId());
        String invoiceNo = StrUtil.sub(byAfterSalesId.getOrderNo(), -10, byAfterSalesId.getOrderNo().length()) + "-FL";

        List<Integer> afterSaleIds = batchAfterSalesDtoMapper.selectBySubjectTypeAndOrderNo(536, byAfterSalesId.getOrderNo());

        Integer integer = batchVirtualInvoiceDtoMapper.selectRedCountByInvoiceNo(afterSaleIds, invoiceNo);
        if (Objects.isNull(integer) || integer.equals(0)) {
            integer = 0;
        }
        integer +=1;
        if (integer < 10) {
            return "0" + integer+invoiceNo;
        }

        return integer+invoiceNo;
    }

}
