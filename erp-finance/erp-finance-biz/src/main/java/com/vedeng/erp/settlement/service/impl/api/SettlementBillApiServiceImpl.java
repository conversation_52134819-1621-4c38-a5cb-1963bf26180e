package com.vedeng.erp.settlement.service.impl.api;

import com.vedeng.erp.finance.cmd.SettlementBillCreateCmd;
import com.vedeng.erp.finance.cmd.SettlementBillDeleteCmd;
import com.vedeng.erp.finance.cmd.SettlementBillQueryCmd;
import com.vedeng.erp.finance.cmd.SettlementBillSettleCmd;
import com.vedeng.erp.finance.dto.SettlementBillApiDto;
import com.vedeng.erp.finance.mapstruct.SettlementNoticeRecordConvertor;
import com.vedeng.erp.finance.service.SettlementBillApiService;
import com.vedeng.erp.finance.service.SettlementNoticeRecordService;
import com.vedeng.erp.settlement.domain.dto.SettlementBillDto;
import com.vedeng.erp.settlement.mapstruct.SettlementBillApiConvertor;
import com.vedeng.erp.settlement.service.SettlementBillService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 结算单
 * @date 2023/11/23 14:32
 */
@Service
@Slf4j
public class SettlementBillApiServiceImpl implements SettlementBillApiService {

    @Autowired
    private SettlementBillService settlementBillService;
    
    @Autowired
    private SettlementBillApiConvertor settlementBillApiConvertor;
    
    @Override
    public Integer create(SettlementBillCreateCmd cmd) {
        return settlementBillService.create(cmd);
    }

    @Override
    public void createAlsoSettlement(SettlementBillCreateCmd cmd) {
        settlementBillService.createAlsoSettlement(cmd);
    }

    @Override
    public void settlement(SettlementBillSettleCmd cmd) {
        settlementBillService.settlement(cmd);
    }

    @Override
    public void delete(SettlementBillDeleteCmd cmd) {
        settlementBillService.delete(cmd);
    }

    @Override
    public Integer getSettlementIdByBusiness(SettlementBillQueryCmd cmd) {
        return settlementBillService.getSettlementIdByBusiness(cmd);
    }

    @Override
    public SettlementBillApiDto getSettlementByBusiness(SettlementBillQueryCmd cmd) {
        SettlementBillDto settlementByBusiness = settlementBillService.getSettlementByBusiness(cmd);
        return settlementBillApiConvertor.to(settlementByBusiness);
    }

}
