<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="电子签章" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src="<%= basePath %>static/js/orderstream/saleorder/electronic_signature.js?rnd=${resourceVersionKey}"></script>
<input type="hidden" name="isClick" id="isClick">
<div class="content mt10 ">
    <div class="parts">
        <div style="background-color: #ddd;">
            <div>
                <div style="margin-left: 10px; padding: 10px 0px 10px 2px;">
                    1、电子签章需要客户进行个人实名认证、公司认证，流程较长，请在与客户充分沟通后，再引导客户使用电子签章。<br>
                    2、客户完成电子签章的签署后，合同会自动回传到ERP的订单中，请在收到消息后查看并提交审核。<br>
                    3、如果合同内容有改动，或客户已签署的合同不满足要求，可重新生成链接，发起新的流程。<br>
                </div>
            </div>

            <div class="title-container title-container-blue">
                <div class="table-title nobor">电子签章链接生成记录</div>

                <div class="title-click nobor  pop-new-data"
                     layerParams='{"width":"600px","height":"400px","title":"生成电子签章链接","link":"/orderstream/saleorder/addElectronicSignature.do?saleorderId=${saleOrder.saleorderId}&traderId=${saleOrder.traderId}&traderContactMobile=${saleOrder.traderContactMobile}"}'>
                    <c:choose>
                        <c:when test="${not empty electronicSignatureList}">
                            重新生成链接
                        </c:when>
                        <c:otherwise>
                            生成电子签章链接
                        </c:otherwise>
                    </c:choose>
                </div>
                <button style="display: none" id="openNewLayer" onclick="openNewLayer()"></button>
            </div>
            <%@ include file="view_electronic_signature.jsp"%>
        </div>

    </div>

</div>
