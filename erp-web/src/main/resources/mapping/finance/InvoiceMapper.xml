<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.finance.dao.InvoiceMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.finance.model.Invoice">
        <id column="INVOICE_ID" property="invoiceId" jdbcType="INTEGER" />
        <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
        <result column="TYPE" property="type" jdbcType="INTEGER" />
        <result column="INVOICE_CODE" property="invoiceCode" jdbcType="INTEGER" />
        <result column="RELATED_ID" property="relatedId" jdbcType="INTEGER" />
        <result column="INVOICE_NO" property="invoiceNo" jdbcType="VARCHAR" />
        <result column="INVOICE_PROPERTY" property="invoiceProperty" jdbcType="INTEGER"/>
        <result column="INVOICE_TYPE" property="invoiceType" jdbcType="INTEGER" />
        <result column="INVOICE_HREF" property="invoiceHref" jdbcType="INTEGER" />
        <result column="RATIO" property="ratio" jdbcType="DECIMAL" />
        <result column="COLOR_TYPE" property="colorType" jdbcType="BIT" />
        <result column="AMOUNT" property="amount" jdbcType="DECIMAL" />
        <result column="IS_ENABLE" property="isEnable" jdbcType="BIT" />
        <result column="VALID_USERID" property="validUserId" jdbcType="INTEGER" />
        <result column="VALID_STATUS" property="validStatus" jdbcType="BIT" />
        <result column="VALID_TIME" property="validTime" jdbcType="BIGINT" />
        <result column="VALID_COMMENTS" property="validComments" jdbcType="VARCHAR" />
        <result column="INVOICE_PRINT_STATUS" property="invoicePrintStatus" jdbcType="BIT" />
        <result column="INVOICE_CANCEL_STATUS" property="invoiceCancelStatus" jdbcType="BIT" />
        <result column="EXPRESS_ID" property="expressId" jdbcType="INTEGER" />
        <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
        <result column="CREATOR" property="creator" jdbcType="INTEGER" />
        <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
        <result column="UPDATER" property="updater" jdbcType="INTEGER" />
        <result column="TAG" property="tag" jdbcType="INTEGER" />
        <result column="IS_AUTH" property="isAuth" jdbcType="INTEGER" />
        <result column="IS_MONTH_AUTH" property="isMonthAuth" jdbcType="INTEGER" />
        <result column="AUTH_TIME" property="authTime" jdbcType="BIGINT" />
        <result column="INVOICE_FROM" property="invoiceFrom" jdbcType="TINYINT" />
        <result column="HX_INVOICE_ID" property="hxInvoiceId" jdbcType="INTEGER" />
    </resultMap>
    <resultMap id="wmsInvoiceResultMap" type="com.wms.dto.WmsInvoiceInfoDto">
        <result column="INVOICE_APPLY_ID" property="invoiceApplyId" jdbcType="INTEGER"/>
        <result column="ERP_ORDER_NO" property="erpOrderNo" jdbcType="VARCHAR"/>
        <result column="WMS_ORDER_NO" property="wmsOrderNo" jdbcType="VARCHAR"/>
        <result column="ORDER_TYPE" property="orderType" jdbcType="INTEGER"/>
        <collection property="invoiceList" ofType="com.vedeng.finance.model.Invoice">
            <result column="INVOICE_HREF" property="invoiceHref" jdbcType="VARCHAR"/>
            <result column="INVOICE_ID" property="invoiceId" jdbcType="INTEGER"/>
            <result column="INVOICE_NO" property="invoiceNo" jdbcType="VARCHAR"/>
            <result column="OSS_FILE_URL" property="ossFileUrl" jdbcType="VARCHAR"/>
            <result column="RESOURCE_ID" property="resourceId" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>
    <sql id="Base_Column_List">
		INVOICE_ID, COMPANY_ID, TYPE, RELATED_ID, INVOICE_NO, INVOICE_TYPE, RATIO, COLOR_TYPE,
		ABS(AMOUNT) AS AMOUNT,
		IS_ENABLE, VALID_STATUS, VALID_TIME, VALID_COMMENTS, INVOICE_PRINT_STATUS,
		INVOICE_CANCEL_STATUS, EXPRESS_ID,
		ADD_TIME, CREATOR, MOD_TIME, UPDATER,VALID_USERID, TAG, IS_AUTH, IS_MONTH_AUTH, AUTH_TIME
	</sql>

    <resultMap id="relationOperateLogResultMap" type="com.vedeng.finance.dto.InvoiceRelationWarehouseLogDto">
        <result column="INVOICE_NO" property="invoiceNo" jdbcType="VARCHAR"/>
        <result column="OUT_IN_NO" property="outInNo" jdbcType="VARCHAR"/>
        <collection property="itemList" ofType="com.vedeng.finance.dto.InvoiceRelationWarehouseLogItemDto">
            <result column="SKU" property="sku"  jdbcType="VARCHAR"/>
            <result column="TOTAL_NUM" property="totalNum" jdbcType="DECIMAL" />
        </collection>
    </resultMap>

    <insert id="insertSelective" parameterType="com.vedeng.finance.model.Invoice" useGeneratedKeys="true" keyProperty="invoiceId">
        insert into T_INVOICE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="invoiceId != null">
                INVOICE_ID,
            </if>
            <if test="companyId != null" >
                COMPANY_ID,
            </if>
            <if test="invoiceFlow != null">
                INVOICE_FLOW,
            </if>
            <if test="invoiceProperty != null">
                INVOICE_PROPERTY,
            </if>
            <if test="invoiceCode != null">
                INVOICE_CODE,
            </if>
            <if test="invoiceHref != null">
                INVOICE_HREF,
            </if>
            <if test="type != null">
                TYPE,
            </if>
            <if test="tag != null">
                TAG,
            </if>
            <if test="relatedId != null">
                RELATED_ID,
            </if>
            <if test="afterSalesId != null">
                AFTER_SALES_ID,
            </if>
            <if test="invoiceNo != null">
                INVOICE_NO,
            </if>
            <if test="invoiceType != null">
                INVOICE_TYPE,
            </if>
            <if test="ratio != null">
                RATIO,
            </if>
            <if test="colorType != null">
                COLOR_TYPE,
            </if>
            <if test="amount != null">
                AMOUNT,
            </if>
            <if test="isEnable != null">
                IS_ENABLE,
            </if>
            <if test="validStatus != null">
                VALID_STATUS,
            </if>
            <if test="validTime != null">
                VALID_TIME,
            </if>
            <if test="validComments != null">
                VALID_COMMENTS,
            </if>
            <if test="invoicePrintStatus != null">
                INVOICE_PRINT_STATUS,
            </if>
            <if test="invoiceCancelStatus != null">
                INVOICE_CANCEL_STATUS,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="validUserId != null">
                VALID_USERID,
            </if>
            <!-- add by Tomcat.Hui 2019/11/22 16:04 .Desc: VDERP-1325 分批开票 新增开票申请ID字段. start -->
            <if test="invoiceApplyId != null">
                INVOICE_APPLY_ID,
            </if>
            <!-- add by Tomcat.Hui 2019/11/22 16:04 .Desc: VDERP-1325 分批开票 新增开票申请ID字段. end -->
            <if test="ossFileUrl != null">
                OSS_FILE_URL,
            </if>
            <if test="resourceId != null">
                RESOURCE_ID,
            </if>
            <if test="invoiceFrom != null">
                INVOICE_FROM,
            </if>
            <if test="hxInvoiceId != null">
                HX_INVOICE_ID,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="invoiceId != null">
                #{invoiceId,jdbcType=INTEGER},
            </if>
            <if test="companyId != null" >
                #{companyId,jdbcType=INTEGER},
            </if>
            <if test="invoiceFlow != null">
                #{invoiceFlow,jdbcType=VARCHAR},
            </if>
            <if test="invoiceProperty != null">
                #{invoiceProperty,jdbcType=INTEGER},
            </if>
            <if test="invoiceCode != null">
                #{invoiceCode,jdbcType=VARCHAR},
            </if>
            <if test="invoiceHref != null">
                #{invoiceHref,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="tag != null">
                #{tag,jdbcType=INTEGER},
            </if>
            <if test="relatedId != null">
                #{relatedId,jdbcType=INTEGER},
            </if>
            <if test="afterSalesId != null">
                #{afterSalesId,jdbcType=INTEGER},
            </if>
            <if test="invoiceNo != null">
                #{invoiceNo,jdbcType=VARCHAR},
            </if>
            <if test="invoiceType != null">
                #{invoiceType,jdbcType=INTEGER},
            </if>
            <if test="ratio != null">
                #{ratio,jdbcType=DECIMAL},
            </if>
            <if test="colorType != null">
                #{colorType,jdbcType=BIT},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="isEnable != null">
                #{isEnable,jdbcType=BIT},
            </if>
            <if test="validStatus != null">
                #{validStatus,jdbcType=BIT},
            </if>
            <if test="validTime != null">
                #{validTime,jdbcType=BIGINT},
            </if>
            <if test="validComments != null">
                #{validComments,jdbcType=VARCHAR},
            </if>
            <if test="invoicePrintStatus != null">
                #{invoicePrintStatus,jdbcType=BIT},
            </if>
            <if test="invoiceCancelStatus != null">
                #{invoiceCancelStatus,jdbcType=BIT},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="validUserId != null">
                #{validUserId,jdbcType=INTEGER},
            </if>
            <!-- add by Tomcat.Hui 2019/11/22 16:04 .Desc: VDERP-1325 分批开票 新增开票申请ID字段. start -->
            <if test="invoiceApplyId != null">
                #{invoiceApplyId,jdbcType=INTEGER},
            </if>
            <!-- add by Tomcat.Hui 2019/11/22 16:04 .Desc: VDERP-1325 分批开票 新增开票申请ID字段. end -->
            <if test="ossFileUrl != null">
                #{ossFileUrl,jdbcType=VARCHAR},
            </if>
            <if test="resourceId != null">
                #{resourceId,jdbcType=VARCHAR},
            </if>
            <if test="invoiceFrom != null">
                #{invoiceFrom,jdbcType=TINYINT},
            </if>
            <if test="hxInvoiceId != null">
                #{hxInvoiceId,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <insert id="insert" parameterType="com.vedeng.finance.model.Invoice" useGeneratedKeys="true" keyProperty="invoiceId">
        insert into T_INVOICE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="invoiceId != null">
                INVOICE_ID,
            </if>
            <if test="companyId != null" >
                COMPANY_ID,
            </if>
            <if test="invoiceFlow != null">
                INVOICE_FLOW,
            </if>
            <if test="invoiceProperty != null">
                INVOICE_PROPERTY,
            </if>
            <if test="invoiceCode != null">
                INVOICE_CODE,
            </if>
            <if test="invoiceHref != null">
                INVOICE_HREF,
            </if>
            <if test="type != null">
                TYPE,
            </if>
            <if test="tag != null">
                TAG,
            </if>
            <if test="relatedId != null">
                RELATED_ID,
            </if>
            <if test="afterSalesId != null">
                AFTER_SALES_ID,
            </if>
            <if test="invoiceNo != null">
                INVOICE_NO,
            </if>
            <if test="invoiceType != null">
                INVOICE_TYPE,
            </if>
            <if test="ratio != null">
                RATIO,
            </if>
            <if test="colorType != null">
                COLOR_TYPE,
            </if>
            <if test="amount != null">
                AMOUNT,
            </if>
            <if test="isEnable != null">
                IS_ENABLE,
            </if>
            <if test="validStatus != null">
                VALID_STATUS,
            </if>
            <if test="validTime != null">
                VALID_TIME,
            </if>
            <if test="validComments != null">
                VALID_COMMENTS,
            </if>
            <if test="invoicePrintStatus != null">
                INVOICE_PRINT_STATUS,
            </if>
            <if test="invoiceCancelStatus != null">
                INVOICE_CANCEL_STATUS,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="validUserId != null">
                VALID_USERID,
            </if>
            <!-- add by Tomcat.Hui 2019/11/22 16:04 .Desc: VDERP-1325 分批开票 新增开票申请ID字段. start -->
            <if test="invoiceApplyId != null">
                INVOICE_APPLY_ID,
            </if>
            <!-- add by Tomcat.Hui 2019/11/22 16:04 .Desc: VDERP-1325 分批开票 新增开票申请ID字段. end -->
            <if test="ossFileUrl != null">
                OSS_FILE_URL,
            </if>
            <if test="resourceId != null">
                RESOURCE_ID,
            </if>
            <if test="invoiceFrom != null">
                INVOICE_FROM,
            </if>
            <if test="hxInvoiceId != null">
                HX_INVOICE_ID,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="invoiceId != null">
                #{invoiceId,jdbcType=INTEGER},
            </if>
            <if test="companyId != null" >
                #{companyId,jdbcType=INTEGER},
            </if>
            <if test="invoiceFlow != null">
                #{invoiceFlow,jdbcType=VARCHAR},
            </if>
            <if test="invoiceProperty != null">
                #{invoiceProperty,jdbcType=INTEGER},
            </if>
            <if test="invoiceCode != null">
                #{invoiceCode,jdbcType=VARCHAR},
            </if>
            <if test="invoiceHref != null">
                #{invoiceHref,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="tag != null">
                #{tag,jdbcType=INTEGER},
            </if>
            <if test="relatedId != null">
                #{relatedId,jdbcType=INTEGER},
            </if>
            <if test="afterSalesId != null">
                #{afterSalesId,jdbcType=INTEGER},
            </if>
            <if test="invoiceNo != null">
                #{invoiceNo,jdbcType=VARCHAR},
            </if>
            <if test="invoiceType != null">
                #{invoiceType,jdbcType=INTEGER},
            </if>
            <if test="ratio != null">
                #{ratio,jdbcType=DECIMAL},
            </if>
            <if test="colorType != null">
                #{colorType,jdbcType=BIT},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="isEnable != null">
                #{isEnable,jdbcType=BIT},
            </if>
            <if test="validStatus != null">
                #{validStatus,jdbcType=BIT},
            </if>
            <if test="validTime != null">
                #{validTime,jdbcType=BIGINT},
            </if>
            <if test="validComments != null">
                #{validComments,jdbcType=VARCHAR},
            </if>
            <if test="invoicePrintStatus != null">
                #{invoicePrintStatus,jdbcType=BIT},
            </if>
            <if test="invoiceCancelStatus != null">
                #{invoiceCancelStatus,jdbcType=BIT},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="validUserId != null">
                #{validUserId,jdbcType=INTEGER},
            </if>
            <!-- add by Tomcat.Hui 2019/11/22 16:04 .Desc: VDERP-1325 分批开票 新增开票申请ID字段. start -->
            <if test="invoiceApplyId != null">
                #{invoiceApplyId,jdbcType=INTEGER},
            </if>
            <!-- add by Tomcat.Hui 2019/11/22 16:04 .Desc: VDERP-1325 分批开票 新增开票申请ID字段. end -->
            <if test="ossFileUrl != null">
                #{ossFileUrl,jdbcType=VARCHAR},
            </if>
            <if test="resourceId != null">
                #{resourceId,jdbcType=VARCHAR},
            </if>
            <if test="invoiceFrom != null">
                #{invoiceFrom,jdbcType=TINYINT},
            </if>
            <if test="hxInvoiceId != null">
                #{hxInvoiceId,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>


    <update id="updateInvoiceByKey" parameterType="com.vedeng.finance.model.Invoice">
        update T_INVOICE
        <set>
            <if test="companyId != null" >
                COMPANY_ID = #{companyId,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                TYPE = #{type,jdbcType=INTEGER},
            </if>
            <if test="relatedId != null">
                RELATED_ID = #{relatedId,jdbcType=INTEGER},
            </if>
            <if test="invoiceNo != null">
                INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR},
            </if>
            <if test="invoiceType != null">
                INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
            </if>
            <if test="ratio != null">
                RATIO = #{ratio,jdbcType=DECIMAL},
            </if>
            <if test="colorType != null">
                COLOR_TYPE = #{colorType,jdbcType=BIT},
            </if>
            <if test="amount != null">
                AMOUNT = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="isEnable != null">
                IS_ENABLE = #{isEnable,jdbcType=BIT},
            </if>
            <if test="validStatus != null">
                VALID_STATUS = #{validStatus,jdbcType=BIT},
            </if>
            <if test="validTime != null">
                VALID_TIME = #{validTime,jdbcType=BIGINT},
            </if>
            <if test="validComments != null">
                VALID_COMMENTS = #{validComments,jdbcType=VARCHAR},
            </if>
            <if test="invoicePrintStatus != null">
                INVOICE_PRINT_STATUS = #{invoicePrintStatus,jdbcType=BIT},
            </if>
            <if test="invoiceCancelStatus != null">
                INVOICE_CANCEL_STATUS = #{invoiceCancelStatus,jdbcType=BIT},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="tag != null">
                TAG = #{tag,jdbcType=INTEGER},
            </if>
            <if test="isAuth != null">
                IS_AUTH = #{isAuth,jdbcType=INTEGER},
            </if>
            <if test="isMonthAuth != null">
                IS_MONTH_AUTH = #{isMonthAuth,jdbcType=INTEGER},
            </if>
            <if test="authTime != null">
                AUTH_TIME = #{authTime,jdbcType=BIGINT},
            </if>
            <if test="ossFileUrl != null">
                OSS_FILE_URL = #{ossFileUrl,jdbcType=VARCHAR},
            </if>
            <if test="resourceId != null">
                RESOURCE_ID = #{resourceId,jdbcType=VARCHAR},
            </if>
            <if test="authFailReason != null">
                AUTH_FAIL_REASON = #{authFailReason,jdbcType=VARCHAR},
            </if>
        </set>
        where INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
    </update>

    <select id="getInvoiceListByInvoiceIdList" parameterType="java.util.List" resultType="com.vedeng.finance.model.Invoice">
        select
            a.*,
            b.LOGISTICS_NO AS logisticsNo,
            c.`NAME` AS logisticsName,
            d.TRADER_NAME AS traderName,
            d.TRADER_CONTACT_ID AS traderContactId,
            d.TRADER_CONTACT_MOBILE AS traderContactMobile,
            d.ORDER_TYPE AS orderType
        from
        T_INVOICE a
        LEFT JOIN T_EXPRESS b ON b.EXPRESS_ID = a.EXPRESS_ID
        LEFT JOIN T_LOGISTICS c ON c.LOGISTICS_ID = b.LOGISTICS_ID
        LEFT JOIN T_SALEORDER d ON a.RELATED_ID = d.SALEORDER_ID
        where
			a.INVOICE_ID IN
            <foreach collection="invoiceIdList" item="invoiceId" index="index" open="(" close=")" separator=",">
                #{invoiceId,jdbcType=INTEGER}
            </foreach>
    </select>
    <select id="getInvoiceApllyNum" resultType="java.lang.Integer">
        SELECT
	A.INVOICE_APPLY_ID
    FROM
	T_INVOICE_APPLY A
      WHERE
	A.ADVANCE_VALID_STATUS != 2
	AND A.VALID_STATUS = 0
	AND A.TYPE = 505
	AND A.RELATED_ID =#{saleorderId}
    </select>

    <select id="getSaleOpenInvoiceAmount" parameterType="java.lang.Integer" resultType="java.math.BigDecimal">
		SELECT COALESCE(SUM(A.AMOUNT),0) AS OPEN_INVOICE_AMOUNT
		FROM T_INVOICE A
		WHERE A.TYPE = 505 AND A.RELATED_ID = #{relatedId,jdbcType=INTEGER}
	</select>

    <!-- 修改销售订单发票状态 -->
    <update id="updateSaleInvoiceStatus" parameterType="com.vedeng.finance.model.Invoice">
        <choose>
            <when test="amountCount != null and amountCount == 0">
                <!-- 销售金额（有效和作废总和）等于（销售开票金额加售后开票金额减特殊产品开票金额），开票状态：未开票 -->
                UPDATE T_SALEORDER
                SET INVOICE_STATUS = 0
                <if test="modTime != null">
                    ,MOD_TIME = #{modTime,jdbcType=BIGINT}
                </if>
                <if test="updater != null">
                    ,UPDATER = #{updater,jdbcType=INTEGER}
                </if>
                WHERE     SALEORDER_ID = #{relatedId,jdbcType=INTEGER}
                AND #{amountCount,jdbcType=DECIMAL} = 0;
            </when>
            <otherwise>
                <!-- 销售金额等于（销售开票金额加售后开票金额减特殊产品开票金额），开票状态：已完成 -->
                UPDATE T_SALEORDER
                <!-- add by Tomcat.Hui 2019/12/16 13:40 .Desc: VDERP-1686 开票申请是否开完票，改为根据开票金额与开票申请的金额进行比较来判断. start -->
                SET INVOICE_STATUS = IF((
                <!-- 耗材订单特殊处理.  -->
                SELECT  IF(#{orderType,jdbcType=INTEGER} IS NOT NULL AND #{orderType,jdbcType=INTEGER} = 5,SUM(D.MAX_SKU_REFUND_AMOUNT - IFNULL(E.ALL_AFTER_AMOUNT,0)), SUM((D.NUM - IFNULL(E.NUM, 0)) * D.PRICE))
                FROM T_SALEORDER_GOODS D
                LEFT JOIN
                (SELECT SUM(F.NUM) AS NUM, F.ORDER_DETAIL_ID,IFNULL(SUM(F.SKU_REFUND_AMOUNT),0) AS ALL_AFTER_AMOUNT
                FROM T_AFTER_SALES E
                INNER JOIN T_AFTER_SALES_GOODS F
                ON     E.AFTER_SALES_ID = F.AFTER_SALES_ID
                AND E.SUBJECT_TYPE = 535	<!-- 销售 -->
                AND E.TYPE = 539	<!-- 销售退货 -->
                AND E.ATFER_SALES_STATUS = 2 <!-- 售后单完结 -->
                AND E.ORDER_ID = #{relatedId,jdbcType=INTEGER}
                AND F.GOODS_TYPE = 0	<!-- 普通产品 -->
                GROUP BY F.ORDER_DETAIL_ID) E
                ON D.SALEORDER_GOODS_ID = E.ORDER_DETAIL_ID
                WHERE D.SALEORDER_ID = #{relatedId,jdbcType=INTEGER} AND D.IS_DELETE = 0 <!-- 有效 -->
                ) <![CDATA[ <= ]]> #{amountCount,jdbcType=DECIMAL},2,1)
                <if test="modTime != null">
                    ,MOD_TIME = #{modTime,jdbcType=BIGINT}
                </if>
                <if test="updater != null">
                    ,UPDATER = #{updater,jdbcType=INTEGER}
                </if>
                WHERE  SALEORDER_ID = #{relatedId,jdbcType=INTEGER}
                <!-- add by Tomcat.Hui 2019/12/16 13:40 .Desc: VDERP-1686 开票申请是否开完票，改为根据开票金额与开票申请的金额进行比较来判断. end -->
            </otherwise>
        </choose>
    </update>
    <select id="getInvoiceExpress" resultMap="BaseResultMap">
    SELECT
        A.INVOICE_ID,A.INVOICE_NO,C.EXPRESS_ID
    FROM
        T_INVOICE A
    LEFT JOIN T_EXPRESS_DETAIL B ON A.INVOICE_ID = B.RELATED_ID
    LEFT JOIN T_EXPRESS C ON B.EXPRESS_ID=C.EXPRESS_ID
    WHERE
        B.BUSINESS_TYPE = 497 AND
        A.INVOICE_ID IN
        <foreach collection="invoiceIdList" item="invoiceId" index="index" open="(" close=")" separator=",">
            #{invoiceId,jdbcType=INTEGER}
        </foreach>
    </select>
    <select id="getInvoiceBaseInfoByInvoiceIds" resultMap="BaseResultMap">
        SELECT
            INVOICE_ID,
            INVOICE_CODE,
            INVOICE_NO,
            INVOICE_TYPE,
            AMOUNT,
            RATIO,
            COLOR_TYPE,
            INVOICE_FROM,
            HX_INVOICE_ID,
            ADD_TIME
        FROM
            T_INVOICE
        WHERE
            INVOICE_ID IN
        <foreach collection="invoiceIds" item="invoiceId" index="index" open="(" close=")" separator=",">
            #{invoiceId,jdbcType=INTEGER}
        </foreach>
        GROUP BY
            INVOICE_ID
    </select>


    <update id="saveInvoiceAuthInfo" parameterType="com.vedeng.finance.model.Invoice">
        UPDATE T_INVOICE
        <set>
            IS_AUTH = #{isAuth,jdbcType=INTEGER},
            <if test="authMode != null">
                AUTH_MODE = #{authMode,jdbcType=INTEGER},
            </if>
            <if test="authFailReason != null">
                AUTH_FAIL_REASON = #{authFailReason,jdbcType=VARCHAR},
            </if>
            <if test="authTime != null">
                AUTH_TIME = #{authTime,jdbcType=BIGINT},
            </if>
            <if test="modTime !=null">
                MOD_TIME = #{modTime,jdbcType=BIGINT},
            </if>
        </set>

        WHERE
        HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER}
    </update>

    <select id="getWmsInvoiceInfoDtos" resultMap="wmsInvoiceResultMap">
        SELECT
            T1.INVOICE_APPLY_ID,
            T1.WMS_ORDER_NO,
            T1.ERP_ORDER_NO,
            T2.INVOICE_HREF,
            T2.INVOICE_ID,
            T2.INVOICE_NO,
            T2.OSS_FILE_URL,
            T2.RESOURCE_ID,
            T3.ORDER_TYPE
        FROM
            T_INVOICE_APPLY T1
            INNER JOIN T_INVOICE T2 ON T1.INVOICE_APPLY_ID = T2.INVOICE_APPLY_ID
            INNER JOIN T_SALEORDER T3 ON T1.RELATED_ID = T3.SALEORDER_ID
        WHERE
            T1.IS_SEND_WMS = 0
            AND T1.ERP_ORDER_NO IS NOT NULL
            AND T1.WMS_ORDER_NO IS NOT NULL
            AND T2.INVOICE_PROPERTY in (2,3)
            AND T2.TYPE = 505
            AND T2.TAG = 1
            AND T3.ORDER_TYPE IN
            <foreach collection="invoiceGoodsOrderTypes" item="orderType" index="index" open="(" close=")" separator=",">
                #{orderType,jdbcType=INTEGER}
            </foreach>
            AND T3.INVOICE_METHOD in (3,4)
            AND T3.IS_SEND_INVOICE = 1
            AND T3.IS_SAME_ADDRESS = 1
    </select>

    <update id="signInvoiceAuthStatus">
        UPDATE `T_INVOICE`
        SET IS_AUTHING = #{isAuthStatus,jdbcType=INTEGER}
        WHERE
          HX_INVOICE_ID IN
        <foreach collection="hxInvoiceIds" item="hxInvoiceId" open="(" close=")" separator=",">
            #{hxInvoiceId,jdbcType=INTEGER}
        </foreach>
    </update>

    <select id="getInvoiceBaseInfoByInvoiceId" resultMap="BaseResultMap">
        SELECT
            INVOICE_ID,
            COMPANY_ID,
            TYPE,
            INVOICE_CODE,
            RELATED_ID,
            INVOICE_NO,
            INVOICE_PROPERTY,
            INVOICE_TYPE,
            INVOICE_HREF,
            RATIO,
            COLOR_TYPE,
            AMOUNT,
            IS_ENABLE,
            VALID_USERID,
            VALID_STATUS,
            VALID_TIME,
            VALID_COMMENTS,
            INVOICE_PRINT_STATUS,
            INVOICE_CANCEL_STATUS,
            EXPRESS_ID,
            ADD_TIME,
            CREATOR,
            MOD_TIME,
            UPDATER,
            TAG,
            IS_AUTH,
            IS_MONTH_AUTH,
            AUTH_TIME,
            INVOICE_FROM,
            HX_INVOICE_ID
        FROM
            `T_INVOICE`
        WHERE
            INVOICE_ID =  #{invoiceId,jdbcType=INTEGER}
    </select>
    <select id="getSaleInvoiceOfNeedDownload" resultMap="BaseResultMap">
        SELECT * FROM T_INVOICE WHERE COMPANY_ID = 1 AND RELATED_ID IS NOT NULL AND INVOICE_PROPERTY = 1 AND INVOICE_CANCEL_STATUS = 0 AND TYPE = 505 AND TAG = 1 AND IS_ENABLE = 1 AND INVOICE_CODE IS NOT NULL AND INVOICE_NO IS NOT NULL AND VALID_STATUS = 1 AND ADD_TIME &gt; #{start} AND ADD_TIME &lt; #{end}
    </select>
    <select id="getInvoiceListByNumAndColorType" resultType="com.vedeng.finance.model.Invoice">
        SELECT * FROM T_INVOICE WHERE INVOICE_NO = #{invoiceNum,jdbcType=VARCHAR} AND INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR} AND COLOR_TYPE = #{colorType,jdbcType=INTEGER} AND IS_ENABLE = #{isEnable,jdbcType=INTEGER} AND INVOICE_CANCEL_STATUS = 0 AND VALID_STATUS IN (0,1) AND INVOICE_PROPERTY = 1 AND TYPE IN (503,504,4126)
    </select>

    <select id="getInvoiceListByRelatedId" parameterType="com.vedeng.finance.model.Invoice" resultType="com.vedeng.finance.model.InvoiceDetail">
        SELECT A.INVOICE_ID,
        A.RELATED_ID,
        B.DETAILGOODS_ID,
        SUM(IF(A.COLOR_TYPE = 2 AND A.IS_ENABLE = 1, B.NUM, B.NUM * -1)) AS NUM,
        SUM(B.TOTAL_AMOUNT) AS TOTAL_AMOUNT
        FROM T_INVOICE A LEFT JOIN T_INVOICE_DETAIL B ON A.INVOICE_ID = B.INVOICE_ID
        WHERE A.VALID_STATUS <![CDATA[ <> ]]> 2
        <if test="companyId != null and companyId != 0">
            AND A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
        </if>
        AND A.RELATED_ID IN
        <foreach collection="relatedIdList" item="relatedId" index="index" open="(" close=")" separator=",">
            #{relatedId,jdbcType=INTEGER}
        </foreach>
        <!-- 开票申请类型 -->
        <if test="type != null and type !=''">
            AND A.TYPE = #{type,jdbcType=INTEGER}
        </if>
        <!-- 录票/开票 1开票 2录票 -->
        <if test="tag != null and tag !=''">
            AND A.TAG = #{tag,jdbcType=INTEGER}
        </if>
        AND B.INVOICE_DETAIL_ID IS NOT NULL
        GROUP BY A.RELATED_ID, B.DETAILGOODS_ID
    </select>

    <select id="listByRelatedId" parameterType="java.lang.Integer" resultType="com.vedeng.finance.model.Invoice">
        SELECT <include refid="Base_Column_List"></include>
        FROM T_INVOICE
        WHERE INVOICE_FROM != 2 AND VALID_STATUS != 2 AND COMPANY_ID = 1
        AND HX_INVOICE_ID NOT IN (SELECT HX_INVOICE_ID FROM T_HX_INVOICE WHERE INVOICE_STATUS = 12) AND TYPE = #{type}  AND RELATED_ID = #{relateId}
    </select>

    <select id="getHxInvoiceBaseInfoByCondition" resultType="com.vedeng.finance.model.Invoice">
          SELECT
            HX_INVOICE_ID,
            INVOICE_CODE INVOICE_CODE,
            INVOICE_NUM INVOICE_NO,
            TAX_AMOUNT RATIO_AMOUNT,
            COLOR_TYPE,
            IS_AUTH,
            IS_AUTHING,
            CREATE_TIME ADD_TIME
        FROM
            T_HX_INVOICE
        WHERE
            INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR}
            AND INVOICE_NUM = #{invoiceNo,jdbcType=VARCHAR}
            LIMIT 1
    </select>

    <select id="getInvoiceBaseInfoFromHxByInvoiceIds" resultType="com.vedeng.finance.model.Invoice">
        SELECT
            T1.INVOICE_NUM INVOICE_NO,
            T1.INVOICE_CODE INVOICE_CODE,
            T1.CREATE_TIME ADD_TIME,
            T1.HX_INVOICE_ID HX_INVOICE_ID,
            T1.TAX_AMOUNT RATIO_AMOUNT,
            T2.INVOICE_TYPE INVOICE_TYPE
        FROM
            T_HX_INVOICE T1
            INNER JOIN T_INVOICE T2 ON T1.HX_INVOICE_ID = T2.HX_INVOICE_ID
            AND T2.INVOICE_FROM = 1
            AND T1.INVOICE_STATUS != 12
            AND T2.HX_INVOICE_ID > 0
        WHERE
            T2.INVOICE_ID IN
            <foreach collection="invoiceIds" item="invoiceId" index="index" open="(" close=")" separator=",">
                #{invoiceId,jdbcType=INTEGER}
            </foreach>
        GROUP BY
            T1.HX_INVOICE_ID
    </select>

    <update id="saveInvoiceHref">
        UPDATE `T_INVOICE`
        SET INVOICE_HREF = #{invoiceHref,jdbcType=VARCHAR},
        UPDATER = #{updater,jdbcType=INTEGER},
        MOD_TIME = #{modTime,jdbcType=BIGINT}
        WHERE
            INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
    </update>
    <update id="updateInvoiceOssUrl">
        UPDATE T_INVOICE SET OSS_FILE_URL = #{ossUrl,jdbcType=VARCHAR} WHERE INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
    </update>

    <update id="signHxInvoiceAuthStatus">
        UPDATE T_HX_INVOICE
        SET IS_AUTHING = #{isAuthStatus,jdbcType=INTEGER}
        WHERE
        HX_INVOICE_ID IN
        <foreach collection="hxInvoiceIds" item="hxInvoiceId" open="(" close=")" separator=",">
            #{hxInvoiceId,jdbcType=INTEGER}
        </foreach>
    </update>

    <update id="saveHxInvoiceAuthInfo">
        UPDATE T_HX_INVOICE
        SET
        <if test="isAuth != null">
            IS_AUTH = #{isAuth,jdbcType=INTEGER},
        </if>
        <if test="authMode != null">
            AUTH_MODE = #{authMode,jdbcType=INTEGER},
        </if>
        <if test="authFailReason != null">
            AUTH_FAIL_REASON = #{authFailReason,jdbcType=VARCHAR},
        </if>
        <if test="authTime != null">
            AUTH_TIME = #{authTime,jdbcType=BIGINT}
        </if>
        WHERE
        HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER}
    </update>
    <update id="updateHxInvoiceIdByPrimaryKey">
        UPDATE T_INVOICE SET HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER} WHERE INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
    </update>

    <update id="setHxInvoiceInfoByPrimaryKeys">
        UPDATE T_INVOICE
        SET INVOICE_FROM = 1,
            HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER}
        WHERE
                INVOICE_ID IN
        <foreach collection="invoiceIds" item="invoiceId" index="index" open="(" close=")" separator=",">
            #{invoiceId,jdbcType=INTEGER}
        </foreach>
    </update>
    <update id="updateRelatedIdByPrimaryKey">
        UPDATE T_INVOICE SET RELATED_ID = #{relatedId,jdbcType=INTEGER},TYPE = 4126  WHERE INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
    </update>


    <select id="selectByPrimaryKey" resultType="com.vedeng.finance.model.Invoice">
        SELECT
            *
        FROM
            T_INVOICE
        WHERE
            INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
    </select>

    <select id="selectAmountByPrimaryKey" resultType="com.vedeng.finance.model.Invoice">
        SELECT
           I.INVOICE_NO,
           sum(ID.TOTAL_AMOUNT) AS amount
        FROM
            T_SALEORDER S
            INNER JOIN T_INVOICE I ON S.SALEORDER_ID = I.RELATED_ID
            INNER JOIN T_INVOICE_DETAIL ID ON ID.INVOICE_ID = I.INVOICE_ID
        WHERE
            I.INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
    </select>

    <select id="getInvoiceGoodsNum" resultType="com.vedeng.finance.model.InvoiceDetail">
        SELECT SUM(B.NUM) AS NUM, A.RELATED_ID,SUM(B.TOTAL_AMOUNT) AS TOTAL_AMOUNT,A.TYPE AS invoiceType
        FROM T_INVOICE A LEFT JOIN T_INVOICE_DETAIL B ON A.INVOICE_ID = B.INVOICE_ID
        WHERE A.INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR} AND A.VALID_STATUS <![CDATA[ <> ]]> 2
        <if test="colorType != null and colorType != ''">
            <!-- 1红票，2蓝票 -->
            AND A.COLOR_TYPE = #{colorType,jdbcType=INTEGER}
        </if>
        <if test="isEnable != null">
            <!-- 0作废，1有效 -->
            AND A.IS_ENABLE = #{isEnable,jdbcType=INTEGER}
        </if>
        <if test="type != null">
            <!-- 503采购开票,504售后开票,505销售开票 -->
            AND A.TYPE = #{type,jdbcType=INTEGER}
        </if>
        GROUP BY A.RELATED_ID
    </select>

    <select id="getAfterSalesByAfterInvoiceId" resultType="com.vedeng.aftersales.model.AfterSales">
        SELECT B.*
		FROM T_AFTER_SALES_INVOICE A
		     INNER JOIN T_AFTER_SALES B ON A.AFTER_SALES_ID = B.AFTER_SALES_ID
		WHERE A.AFTER_SALES_INVOICE_ID = #{afterInvoiceId,jdbcType=INTEGER}
    </select>
    <select id="getSaleorderHaveInvoiceTotalAmount" resultType="java.math.BigDecimal">
        SELECT
            sum(
                    IF
                        ( b.COLOR_TYPE = 2 AND b.IS_ENABLE = 1, abs( a.TOTAL_AMOUNT ),- abs( a.TOTAL_AMOUNT ) )
                ) canRecordAmount
        FROM
            T_INVOICE_DETAIL a
                LEFT JOIN T_INVOICE b ON a.INVOICE_ID = b.INVOICE_ID
        WHERE
            b.VALID_STATUS = 1
          AND b.TYPE = 505
          AND a.DETAILGOODS_ID = #{buyorderGoodsId,jdbcType=INTEGER}
        GROUP BY
            a.DETAILGOODS_ID
    </select>
    <select id="getInvoiceListByInvoiceId" resultType="com.vedeng.finance.model.InvoiceDetail">
        SELECT *
        FROM
            T_INVOICE_DETAIL a
        WHERE INVOICE_ID = #{invoiceId, jdbcType=INTEGER};
    </select>
    <select id="getInValidInvoiceListByIds" resultType="com.vedeng.finance.model.Invoice">
        SELECT *
        FROM T_INVOICE I INNER JOIN T_HX_INVOICE HI ON I.HX_INVOICE_ID = HI.HX_INVOICE_ID
        WHERE HI.INVOICE_TYPE = 1 AND  HI.COLOR_TYPE = 1 AND HI.INVOICE_STATUS = 12 AND I.INVOICE_ID IN
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id, jdbcType=INTEGER}
        </foreach>
    </select>

    <resultMap type="com.vedeng.aftersales.model.vo.AfterSalesInvoiceVo" id="AfterSalesInvoiceVoMap">
        <id column="AFTER_SALES_INVOICE_ID" property="afterSalesInvoiceId" jdbcType="INTEGER" />
        <result column="AFTER_SALES_ID" property="afterSalesId" jdbcType="INTEGER" />
        <result column="INVOICE_ID" property="invoiceId" jdbcType="INTEGER" />
        <result column="IS_REFUND_INVOICE" property="isRefundInvoice" jdbcType="BIT" />
        <result column="STATUS" property="status" jdbcType="BIT" />
        <result column="IS_SEND_INVOICE" property="isSendInvoice" jdbcType="BIT" />
        <result column="SEND_TIME" property="sendTime"/>
        <result column="INVOICE_NO" property="invoiceNo" jdbcType="VARCHAR" />
        <result column="INVOICE_CODE" property="invoiceCode" jdbcType="VARCHAR" />
        <result column="INVOICE_TYPE" property="invoiceType" jdbcType="INTEGER" />
        <result column="INVOICE_PROPERTY" property="invoiceProperty" jdbcType="INTEGER" />
        <result column="INVOICE_TYP_NAME" property="invoiceTypeName" jdbcType="VARCHAR" />
        <result column="AMOUNT" property="amount" jdbcType="DECIMAL" />
        <result column="EXPRESS_ID" property="expressId" jdbcType="INTEGER" />
        <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
        <result column="CREATOR" property="creator" jdbcType="INTEGER" />
        <result column="COLOR_TYPE" property="colorType" jdbcType="BIT" />
        <result column="AMOUNT_COUNT" property="amountCount" jdbcType="DECIMAL" />
        <result column="VALID_STATUS" property="validStatus" jdbcType="BIT" />
        <result column="VALID_TIME" property="validTime" jdbcType="BIGINT" />
        <result column="VALID_USERID" property="validUserId" jdbcType="INTEGER" />
        <result column="LOGISTICS_NO" property="logisticsNo" jdbcType="VARCHAR"/>
        <result column="ARRIVAL_STATUS" property="arrivalStatus" jdbcType="INTEGER"/>
        <result column="ARRIVAL_TIME" property="arrivalTime"/>
        <result column="IS_ENABLE" property="isEnable" jdbcType="INTEGER" />
        <result column="TYPE" property="type" jdbcType="INTEGER"/>
        <result column="AFTER_EXPRESS_ID" property="afterExpressId" jdbcType="INTEGER" />
    </resultMap>
    <select id="getBuyorderExpenseSalesByRelatedId"
            resultType="com.vedeng.aftersales.model.vo.AfterSalesInvoiceVo">
        SELECT
            I.RELATED_ID,
            I.INVOICE_ID,
            I.IS_ENABLE,
            I.INVOICE_NO,
            I.INVOICE_TYPE,
            I.AMOUNT,
            I.EXPRESS_ID,
            I.INVOICE_CODE,
            I.ADD_TIME
        FROM
            T_INVOICE I
        WHERE 1=1
          AND I.TYPE = 4126
          AND I.VALID_STATUS in (0,1)
          AND I.RELATED_ID = #{relatedId}
    </select>

    <select id="getAfterSalesInvoiceForInValid" resultMap="AfterSalesInvoiceVoMap">
        -- 		d.AFTER_SALES_INVOICE_ID, , d.IS_REFUND_INVOICE, d.STATUS, d.IS_SEND_INVOICE,
        -- validUsername
	SELECT
	    I.RELATED_ID AS AFTER_SALES_ID,
	    I.INVOICE_ID,
	    I.IS_ENABLE,
		I.INVOICE_NO,
		I.INVOICE_TYPE,
		I.AMOUNT,
		I.EXPRESS_ID,
		SYS.TITLE AS INVOICE_TYP_NAME,
		ABS(SUM(I.AMOUNT)) as AMOUNT_COUNT,
		I.INVOICE_CODE,
		I.ADD_TIME
	FROM
		T_INVOICE I
		INNER JOIN T_HX_INVOICE HI ON I.HX_INVOICE_ID = HI.HX_INVOICE_ID
		LEFT JOIN T_SYS_OPTION_DEFINITION SYS on SYS.SYS_OPTION_DEFINITION_ID = I.INVOICE_TYPE
	WHERE
		HI.INVOICE_TYPE = 1
		AND HI.COLOR_TYPE = 1
		AND HI.INVOICE_STATUS = 12
		AND I.INVOICE_FROM = 2
		AND I.TYPE = 504
		AND I.TAG = 2
		AND I.RELATED_ID = #{afterSalesId}
    </select>
    <select id="selectInvoiceListByParam" resultType="com.vedeng.finance.model.Invoice">
        SELECT
        a.INVOICE_ID, a.COMPANY_ID, a.TYPE, a.RELATED_ID, a.INVOICE_NO, a.INVOICE_TYPE, a.RATIO, a.COLOR_TYPE,
        ABS(SUM(b.TOTAL_AMOUNT)) AS AMOUNT,
        a.IS_ENABLE, a.VALID_STATUS, a.VALID_TIME, a.VALID_COMMENTS, a.INVOICE_PRINT_STATUS,
        a.INVOICE_CANCEL_STATUS, a.EXPRESS_ID,
        a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER, SUM(b.NUM) AS NUM,te.ARRIVAL_STATUS arrivalStatus,tu.USERNAME userName
        FROM T_INVOICE a
        LEFT JOIN T_INVOICE_DETAIL b ON a.INVOICE_ID = b.INVOICE_ID
        LEFT JOIN T_EXPRESS te ON a.`EXPRESS_ID`=te.EXPRESS_ID
        LEFT JOIN T_USER tu ON a.`CREATOR`=tu.USER_ID
        WHERE a.VALID_STATUS = 1
        <if test="invoice.companyId != null and invoice.companyId != 0">
            and	a.COMPANY_ID = #{invoice.companyId,jdbcType=INTEGER}
        </if>
        <if test="saleOrderGoodsList != null">
            AND b.DETAILGOODS_ID in
            <foreach collection="saleOrderGoodsList" item="sgv" open="(" close=")" separator=",">
                #{sgv.saleorderGoodsId,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test = "typeList != null">
            and a.TYPE in
            <foreach collection="typeList" item="type" open="(" close=")" separator=",">
                #{type,jdbcType=INTEGER}
            </foreach>
        </if>
        GROUP BY a.INVOICE_NO having AMOUNT > 0
    </select>
    <select id="selectIngAfterSaleOrderByInvoiceIdList" resultType="java.lang.Integer">
        select
            distinct a.INVOICE_ID
        from T_AFTER_SALES_INVOICE a
             left join T_AFTER_SALES b on a.AFTER_SALES_ID = b.AFTER_SALES_ID
        where b.ATFER_SALES_STATUS in (0, 1)
            and a.INVOICE_ID in
            <foreach collection="invalidInvoiceIdList" item="type" open="(" close=")" separator=",">
                #{type,jdbcType=INTEGER}
            </foreach>
            and b.SUBJECT_TYPE = 535
            and b.TYPE in (542,1135)
    </select>

    <select id="getInvoiceListByValidTime" resultType="com.vedeng.finance.model.Invoice">
        SELECT
          *
        FROM
          T_INVOICE
        WHERE
            TYPE = 503
            AND VALID_STATUS = 1
            AND VALID_TIME <![CDATA[ >= ]]> #{beginTime,jdbcType=BIGINT}
            AND VALID_TIME <![CDATA[ <= ]]> #{endTime,jdbcType=BIGINT}
    </select>
    <select id="getInvoiceListBySaleorderId" resultType="com.vedeng.finance.model.Invoice">
        SELECT
            I.INVOICE_NO,
            I.INVOICE_ID,
            I.COLOR_TYPE,
            I.IS_ENABLE,
            I.ADD_TIME,
            SUM(ID.TOTAL_AMOUNT)  amount
        FROM T_SALEORDER S
                 INNER JOIN T_INVOICE I ON S.SALEORDER_ID = I.RELATED_ID
                 INNER JOIN T_INVOICE_DETAIL ID ON ID.INVOICE_ID = I.INVOICE_ID
        WHERE I.TYPE = 505
          AND I.TAG = 1
--           AND I.COLOR_TYPE=2
--           AND I.IS_ENABLE=1
          AND S.COMPANY_ID = 1
          AND I.RELATED_ID= #{saleorderId}
        GROUP BY I.INVOICE_ID
        ORDER BY I.INVOICE_ID ASC
    </select>

    <select id="getInvoiceListByParam" resultType="com.vedeng.finance.model.Invoice" parameterType="com.vedeng.finance.model.Invoice">
        SELECT
        a.INVOICE_ID, a.COMPANY_ID, a.TYPE, a.RELATED_ID, a.INVOICE_NO, a.INVOICE_TYPE, a.RATIO, a.COLOR_TYPE,
        ABS(SUM(b.TOTAL_AMOUNT)) AS AMOUNT,
        a.IS_ENABLE, a.VALID_STATUS, a.VALID_TIME, a.VALID_COMMENTS, a.INVOICE_PRINT_STATUS,
        a.INVOICE_CANCEL_STATUS, a.EXPRESS_ID,
        a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER, SUM(b.NUM) AS NUM,te.ARRIVAL_STATUS arrivalStatus,tu.USERNAME userName
        FROM T_INVOICE a
        LEFT JOIN T_INVOICE_DETAIL b ON a.INVOICE_ID = b.INVOICE_ID
        LEFT JOIN T_EXPRESS te ON a.`EXPRESS_ID`=te.EXPRESS_ID
        LEFT JOIN T_USER tu ON a.`CREATOR`=tu.USER_ID
        WHERE a.VALID_STATUS = 1
        AND a.INVOICE_NO IS NOT NULL
        <if test="companyId != null and companyId != 0">
            and	a.COMPANY_ID = #{companyId,jdbcType=INTEGER}
        </if>
        <if test="relatedId != null and relatedId != 0">
            and	a.RELATED_ID = #{relatedId,jdbcType=INTEGER}
        </if>
        <if test="type != null and type !=''">
            AND a.TYPE = #{type,jdbcType=INTEGER}
        </if>
        <if test="sgvList != null">
            AND b.DETAILGOODS_ID in
            <foreach collection="sgvList" item="sgv" open="(" close=")" separator=",">
                #{sgv.saleorderGoodsId,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test = "typeList != null">
            and a.TYPE in
            <foreach collection="typeList" item="type" open="(" close=")" separator=",">
                #{type,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="invoiceNo != null and invoiceNo != ''">
            and	a.INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
        </if>
        <!-- 红蓝字 -->
        <if test="colorType != null and colorType != ''">
            AND a.COLOR_TYPE = #{colorType,jdbcType=BIT}
        </if>
        <!-- 是否有效 -->
        <if test="isEnable != null">
            AND a.IS_ENABLE = #{isEnable,jdbcType=BIT}
        </if>
        <if test="invoiceCancelStatus != null">
            AND a.INVOICE_CANCEL_STATUS = #{invoiceCancelStatus,jdbcType=BIT}
        </if>
        GROUP BY a.INVOICE_NO having AMOUNT > 0
    </select>

    <select id="getInvoiceListByEntity" resultType="com.vedeng.finance.model.Invoice" parameterType="com.vedeng.finance.model.Invoice">
        select * from T_INVOICE  WHERE 1 = 1
        <if test="afterSalesId != null and afterSalesId !=''">
            AND AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
        </if>
    </select>



    <select id="getSaleOrderAfterStatus" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT COUNT(*) AS NUM
        FROM T_AFTER_SALES A
        WHERE     A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
        AND A.SUBJECT_TYPE = 535	<!-- 销售 -->
        AND A.TYPE IN (539,
        540,
        542,
        543)	<!-- 销售退货、换货、退票、退款 -->
        AND A.VALID_STATUS = 1	<!-- 生效 -->
        AND A.STATUS = 2	<!-- 审核通过 -->
        AND A.ATFER_SALES_STATUS <![CDATA[ <> ]]> 2	<!-- 非已完结 -->
        AND A.ATFER_SALES_STATUS <![CDATA[ <> ]]> 3	<!-- 非未关闭的 -->
        AND A.ORDER_ID = #{saleorderId,jdbcType=INTEGER}
    </select>

    <!-- 查询是否有开票申请记录（只有销售有申请，包括是否提前开票） -->
    <select id="getOrderOpenInvoiceApply" parameterType="java.lang.Integer" resultType="java.util.Map">
        SELECT IFNULL(
        SUM(IFNULL(IF(A.VALID_STATUS = 1 AND A.YY_VALID_STATUS = 1, 1, 0), 0)), 0)
        AS NUM_PASS,
        IFNULL(
        SUM(IFNULL(IF(A.VALID_STATUS = 0 AND A.YY_VALID_STATUS <![CDATA[ <> ]]> 2 AND A.ADVANCE_VALID_STATUS <![CDATA[ <> ]]> 2, 1, 0), 0)), 0)
        AS NUM_WAIT,
        COUNT(*) AS NUM_COUNT
        FROM T_INVOICE_APPLY A
        WHERE A.TYPE = 505 AND A.RELATED_ID = #{saleorderId,jdbcType=INTEGER}
        <!-- AND A.IS_ADVANCE = #{isAdvance,jdbcType=INTEGER} -->
    </select>

    <select id="getSaleInvoiceAmount" parameterType="java.lang.Integer" resultType="com.vedeng.finance.model.Invoice">
        SELECT X.RELATED_ID,IFNULL(
        SUM(IF(X.COLOR_TYPE = 2 AND X.IS_ENABLE = 1, ABS(X.AMOUNT), 0))
        - SUM(
        IF(
        (X.COLOR_TYPE = 2 AND X.IS_ENABLE = 0)
        OR (X.COLOR_TYPE = 1 AND X.IS_ENABLE = 1),
        ABS(X.AMOUNT),
        0)),
        0)
        AS AMOUNT
        FROM T_INVOICE X
        WHERE     X.TYPE IN (504, 505)	<!-- 包括售后与销售开票 -->
        AND X.RELATED_ID = #{saleorderId,jdbcType=INTEGER}
        AND X.TAG = 1 <!-- 开票 -->
        AND X.INVOICE_CANCEL_STATUS = 0	<!-- 未作废 -->
        GROUP BY X.RELATED_ID
    </select>
    <select id="getSaleOrderAfterRecord" parameterType="java.lang.Integer" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(F.PRICE * E.NUM),0) AS CAN_AMOUNT
        FROM T_AFTER_SALES D
        INNER JOIN T_AFTER_SALES_GOODS E
        ON D.AFTER_SALES_ID = E.AFTER_SALES_ID
        INNER JOIN T_SALEORDER_GOODS F
        ON E.ORDER_DETAIL_ID = F.SALEORDER_GOODS_ID
        WHERE     D.SUBJECT_TYPE = 535	<!-- 销售 -->
        AND D.TYPE = 539	<!-- 销售退货 -->
        AND D.VALID_STATUS = 1	<!-- 生效 -->
        AND D.STATUS = 2	<!-- 审核通过 -->
        AND D.ATFER_SALES_STATUS = 2	<!-- 已完结 -->
        AND E.GOODS_TYPE = 0	<!-- 普通产品 -->
        AND D.ORDER_ID = #{saleorderId,jdbcType=INTEGER}
        GROUP BY D.ORDER_ID
    </select>

    <select id="getBuyOrderAfterRecord" parameterType="java.lang.Integer" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(F.PRICE * E.NUM),0) AS CAN_AMOUNT
        FROM T_AFTER_SALES D
        INNER JOIN T_AFTER_SALES_GOODS E
        ON D.AFTER_SALES_ID = E.AFTER_SALES_ID
        INNER JOIN T_BUYORDER_GOODS F
        <!-- changed by Tomcat.Hui 2020/8/22 12:05 下午 .Desc: VDERP-3268 【采购订单】采购订单VB207447140 的收票状态显示错误. start -->
        ON E.ORDER_DETAIL_ID = F.BUYORDER_GOODS_ID
        <!-- changed by Tomcat.Hui 2020/8/22 12:05 下午 .Desc: VDERP-3268 【采购订单】采购订单VB207447140 的收票状态显示错误. end -->
        WHERE     D.SUBJECT_TYPE = 536	<!-- 采购 -->
        AND D.TYPE = 546	<!-- 采购退货 -->
        AND D.VALID_STATUS = 1	<!-- 生效 -->
        AND D.STATUS = 2	<!-- 审核通过 -->
        AND D.ATFER_SALES_STATUS = 2	<!-- 已完结 -->
        AND E.GOODS_TYPE = 0	<!-- 普通产品 -->
        AND D.ORDER_ID = #{buyorderId,jdbcType=INTEGER}
        GROUP BY D.ORDER_ID
    </select>
    <select id="getBuyRecordInvoiceAmount" parameterType="java.lang.Integer" resultType="java.math.BigDecimal">
		SELECT SUM(A.AMOUNT) AS OPEN_INVOICE_AMOUNT
		FROM T_INVOICE A
		WHERE A.TYPE = 503 AND A.RELATED_ID = #{relatedId,jdbcType=INTEGER}
			  AND A.VALID_STATUS = 1
	</select>

    <select id="getInvoiceExpressInfo" resultType="com.vedeng.finance.model.Invoice">
        SELECT
        A.INVOICE_ID,A.INVOICE_NO,C.EXPRESS_ID,B.NUM invoiceNum
        FROM
        T_INVOICE A
        LEFT JOIN T_EXPRESS_DETAIL B ON A.INVOICE_ID = B.RELATED_ID AND B.BUSINESS_TYPE = 497
        LEFT JOIN T_EXPRESS C ON B.EXPRESS_ID=C.EXPRESS_ID
        WHERE
        A.INVOICE_ID IN
        <foreach collection="invoiceIdList" item="invoiceId" index="index" open="(" close=")" separator=",">
            #{invoiceId,jdbcType=INTEGER}
        </foreach>
        GROUP BY A.INVOICE_ID
    </select>

    <select id="getBlueInvalidInvoiceByCondition" resultType="com.vedeng.finance.model.Invoice">
        SELECT
            *
        FROM
            T_INVOICE
        WHERE
            TYPE IN (503, 4126)
          AND COLOR_TYPE = 2
          AND IS_ENABLE = 0
          <choose>
              <when test="invoiceFrom == 2">
                  AND INVOICE_FROM = 2
                  AND HX_INVOICE_ID > 0
              </when>
          <otherwise>
              AND INVOICE_FROM = 0
              AND HX_INVOICE_ID = 0
          </otherwise>
          </choose>
          AND INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
          AND INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR}
          AND RELATED_ID = #{relatedId,jdbcType=INTEGER}
    </select>

    <select id="getTotalAmountByInvoiceNoAndCode" resultType="java.math.BigDecimal">

        SELECT
                SUM(
                        IFNULL( A.AMOUNT, 0 )) + IFNULL( B.RED__INVOICE_AMOUNT, 0 ) + SUM(
                        IFNULL( C.AMOUNT, 0 )) AMOUNT
        FROM
            T_INVOICE A
                LEFT JOIN (
                SELECT
                    T1.INVOICE_NO ORIGIN_INVOICE_NO,
                    T1.INVOICE_CODE ORIGIN_INVOICE_CODE,
                    SUM(
                            IFNULL( T3.AMOUNT, 0 )) RED__INVOICE_AMOUNT
                FROM
                    T_INVOICE T1
                        LEFT JOIN T_R_INVOICE_J_INVOICE T2 ON T1.INVOICE_ID = T2.RELATE_INVOICE_ID
                        LEFT JOIN T_INVOICE T3 ON T2.INVOICE_ID = T3.INVOICE_ID
                        AND T3.COLOR_TYPE = 1
                        AND T3.IS_ENABLE = 1
                WHERE
                    T3.VALID_STATUS = 1
                GROUP BY
                    T1.INVOICE_NO,
                    T1.INVOICE_CODE
            ) B ON A.INVOICE_NO = B.ORIGIN_INVOICE_NO
                AND A.INVOICE_CODE = B.ORIGIN_INVOICE_CODE
                LEFT JOIN T_INVOICE C ON A.INVOICE_NO = C.INVOICE_NO
                AND A.INVOICE_CODE = C.INVOICE_CODE
                AND C.COLOR_TYPE = 1
                AND C.IS_ENABLE = 0
                AND C.COLOR_COMPLEMENT_TYPE = 1
                AND C.VALID_STATUS = 1
        WHERE
            A.VALID_STATUS = 1
          AND A.INVOICE_NO =  #{invoiceNo,jdbcType=VARCHAR}
          AND A.INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR}
        GROUP BY
            A.INVOICE_NO,
            A.INVOICE_CODE
    </select>

    <select id="getValidInvoiceDetailByInvoiceNoAndCode" resultType="com.vedeng.finance.dto.InvoiceDetailDto">
        SELECT T1.INVOICE_ID,
               T1.RELATED_ID,
               T2.DETAILGOODS_ID,
               T2.PRICE,
               SUM(IF(T1.COLOR_TYPE = 2 AND T1.IS_ENABLE = 1, T2.NUM, T2.NUM * - 1)) INVOICE_NUM
        FROM T_INVOICE T1
                 INNER JOIN T_INVOICE_DETAIL T2 ON T1.INVOICE_ID = T2.INVOICE_ID
        WHERE T1.VALID_STATUS = 1
          AND T1.COMPANY_ID = 1
          AND (T1.COLOR_TYPE = 2 OR T1.COLOR_COMPLEMENT_TYPE = 1)
          AND T1.INVOICE_NO LIKE CONCAT('%',#{invoiceNo,jdbcType=VARCHAR},'%')
          AND T1.INVOICE_CODE LIKE CONCAT('%',#{invoiceCode,jdbcType=VARCHAR},'%')
        GROUP BY T1.TYPE,
                 T2.DETAILGOODS_ID
        HAVING INVOICE_NUM > 0
    </select>

    <select id="getValidInvoiceDetailByHxInvoiceId" resultType="com.vedeng.finance.dto.InvoiceDetailDto">
        SELECT T1.INVOICE_ID,
               T1.RELATED_ID,
               T2.DETAILGOODS_ID,
               T2.PRICE,
               SUM(IF(T1.COLOR_TYPE = 2 AND T1.IS_ENABLE = 1, T2.NUM, T2.NUM * - 1)) INVOICE_NUM
        FROM T_INVOICE T1
        INNER JOIN T_INVOICE_DETAIL T2 ON T1.INVOICE_ID = T2.INVOICE_ID
        WHERE T1.VALID_STATUS = 1
          AND T1.COMPANY_ID = 1
          AND T1.HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER}
          AND (T1.COLOR_TYPE = 2 OR T1.COLOR_COMPLEMENT_TYPE = 1)
        GROUP BY T1.INVOICE_ID,
                 T1.TYPE,
                 T2.DETAILGOODS_ID
        HAVING INVOICE_NUM > 0
    </select>

    <select id="getInReviewInvoiceReversalByInvoiceId" resultType="java.lang.Integer">
        SELECT INVOICE_REVERSAL_ID
        FROM T_INVOICE_REVERSAL
        WHERE IS_DELETE = 0
          AND REVERSAL_AUDIT_STATUS = 0
          AND INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
    </select>

    <select id="getInReviewInvoiceReversalByInvoiceNoAndCode" resultType="java.lang.Integer">
       SELECT
            INVOICE_REVERSAL_ID
        FROM
            T_INVOICE_REVERSAL
        WHERE
            IS_DELETE = 0
          AND REVERSAL_AUDIT_STATUS = 0
          AND INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
          AND INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR}
    </select>

    <select id="listInvoiceByInvoiceNoAndCode" resultType="com.vedeng.finance.model.Invoice">
        SELECT *
        FROM T_INVOICE
        WHERE COMPANY_ID = 1
          AND COLOR_TYPE = 2
          AND IS_ENABLE = 1
          AND INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
          AND INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR}
    </select>

    <select id="queryPlusInvoiceBuyorderGoods" resultType="com.vedeng.erp.aftersale.dto.AfterBuyorderInvoiceGoodsDto">
        SELECT
            C.SKU,A.INVOICE_NO,A.INVOICE_CODE,C.PRICE AS buyPrice,C.UNIT_NAME,SUM(B.NUM) AS invoiceNum,B.DETAILGOODS_ID,
            C.GOODS_ID,(C.NUM-C.AFTER_RETURN_NUM)AS num
        FROM T_INVOICE A
        LEFT JOIN T_INVOICE_DETAIL B ON A.INVOICE_ID = B.INVOICE_ID
        LEFT JOIN T_BUYORDER_GOODS C ON B.DETAILGOODS_ID = C.BUYORDER_GOODS_ID
        WHERE
            A.RELATED_ID = #{buyorderId,jdbcType=INTEGER}
            AND A.VALID_STATUS = 1
            AND A.IS_ENABLE = 1
            AND A.COLOR_TYPE = 2
            AND A.TAG = 2
            AND A.TYPE = 503
        GROUP BY A.INVOICE_NO,A.INVOICE_CODE,B.DETAILGOODS_ID
    </select>

    <select id="queryMinusInvoiceBuyorderGoods" resultType="com.vedeng.erp.aftersale.dto.AfterBuyorderInvoiceGoodsDto">
        SELECT
            A.INVOICE_NO,A.INVOICE_CODE,SUM(B.NUM) AS invoiceNum,D.INVOICE_NO AS originInvoiceNo,D.INVOICE_CODE AS originInvoiceCode,B.DETAILGOODS_ID
        FROM T_INVOICE A
        LEFT JOIN T_INVOICE_DETAIL B ON A.INVOICE_ID = B.INVOICE_ID
        LEFT JOIN T_R_INVOICE_J_INVOICE C ON A.INVOICE_ID = C.RELATE_INVOICE_ID
        LEFT JOIN T_INVOICE D ON C.INVOICE_ID = D.INVOICE_ID
        WHERE
            A.RELATED_ID = #{buyorderId,jdbcType=INTEGER}
            AND A.VALID_STATUS = 1
            AND ((A.COLOR_TYPE = 2 AND A.IS_ENABLE = 0) OR A.COLOR_TYPE =1)
            AND A.TAG = 2
            AND A.TYPE = 503
        GROUP BY A.INVOICE_NO,A.INVOICE_CODE,B.DETAILGOODS_ID
    </select>

    <select id="getValidInvoiceListThisAudit" resultType="com.vedeng.finance.model.Invoice">
        SELECT T2.*
        FROM T_INVOICE T1
         LEFT JOIN T_INVOICE T2 ON T1.INVOICE_NO = T2.INVOICE_NO AND T1.INVOICE_CODE = T2.INVOICE_CODE
        WHERE T2.COLOR_TYPE = 2
          AND T2.IS_ENABLE = 1
          AND T2.VALID_STATUS = 0
          AND T1.INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
        GROUP BY T2.INVOICE_ID
    </select>

    <select id="getValidInvoiceByCondition" resultType="com.vedeng.finance.model.Invoice">
        SELECT *
        FROM T_INVOICE
        WHERE COMPANY_ID = 1
          AND (COLOR_TYPE = 2 OR COLOR_COMPLEMENT_TYPE = 1)
          AND INVOICE_NO LIKE CONCAT('%',#{invoiceNo,jdbcType=VARCHAR},'%')
          AND INVOICE_CODE LIKE CONCAT('%',#{invoiceCode,jdbcType=VARCHAR},'%')
    </select>

    <select id="getAllRelationInvoiceByInvoiceNoAndCode" resultType="com.vedeng.finance.model.Invoice">
        SELECT *
        FROM T_INVOICE
        WHERE COMPANY_ID = 1
          AND (COLOR_TYPE = 2 OR COLOR_COMPLEMENT_TYPE = 1)
          AND INVOICE_NO LIKE CONCAT('%',#{invoiceNo,jdbcType=VARCHAR},'%')
          AND INVOICE_CODE LIKE CONCAT('%',#{invoiceCode,jdbcType=VARCHAR},'%')
        GROUP BY TYPE, RELATED_ID
    </select>

    <select id="getRelationOperateLog" resultMap="relationOperateLogResultMap">
        SELECT
            T2.INVOICE_NO,
            T3.OUT_IN_NO,
            T1.SKU,
            SUM( T1.NUM ) TOTAL_NUM
        FROM
            T_R_INVOICE_DETAIL_J_OPERATE_LOG T1
                LEFT JOIN T_INVOICE T2 ON T1.INVOICE_ID = T2.INVOICE_ID
                LEFT JOIN T_WAREHOUSE_GOODS_OUT_IN_ITEM T3 ON T1.OPERATE_LOG_ID = T3.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID
        WHERE
            T1.IS_DELETE = 0
          AND T1.OPERATE_TYPE = 1
          AND T2.RELATED_ID =#{orderId,jdbcType=INTEGER}
          AND T2.INVOICE_NO IN
        <foreach collection="invoiceNoList" item="invoiceNo" index="index" open="(" close=")" separator=",">
            #{invoiceNo,jdbcType=VARCHAR}
        </foreach>
        GROUP BY
            T2.INVOICE_NO,
            T3.OUT_IN_NO,
            T1.GOODS_ID
        HAVING
            TOTAL_NUM > 0
        ORDER BY
            T3.ADD_TIME ASC;
    </select>

    <select id="getInvoiceInfoByCustomCondition" resultMap="BaseResultMap">
        SELECT *
        FROM T_INVOICE
        WHERE INVOICE_NO = #{invoice.invoiceNo,jdbcType=VARCHAR}
          AND INVOICE_CODE = #{invoice.invoiceCode,jdbcType=VARCHAR}
          AND VALID_STATUS = 1
          AND COLOR_TYPE = 1
          AND IS_ENABLE = 1
          AND COMPANY_ID = 1
          AND ADD_TIME >= #{invoice.addTime,jdbcType=BIGINT}
            LIMIT 1
    </select>

    <select id="getInvoiceVoucherInfo" resultType="com.vedeng.finance.dto.InvoiceVoucherDto">
        SELECT
            INVOICE_VOUCHER_ID,
            INVOICE_ID,
            concat(VOUCHER_DATE,' ',VOUCHER_NO) as VOUCHER_NO
        FROM
            T_INVOICE_VOUCHER
        WHERE
            IS_DELETE = 0
          AND INVOICE_ID IN
        <foreach collection="invoiceIdList" item="invoiceId" index="index" open="(" close=")" separator=",">
            #{invoiceId,jdbcType=INTEGER}
        </foreach>
        GROUP BY
            INVOICE_ID
    </select>

    <select id="getAfterPayTraderName" resultType="java.lang.String">
        SELECT A.TRADER_NAME
        FROM T_PAY_APPLY A
        WHERE A.COMPANY_ID = #{companyId,jdbcType=INTEGER} AND A.RELATED_ID = #{afterSalesId,jdbcType=INTEGER}
          AND A.PAY_TYPE = 518 AND A.VALID_STATUS = 1
        ORDER BY A.ADD_TIME DESC
        LIMIT 1
    </select>

    <select id="getInvoiceInfoByInvoiceId" resultType="com.vedeng.finance.model.InvoiceTimeDto">
        SELECT
            INVOICE_ID,
            COMPANY_ID,
            TYPE,
            INVOICE_CODE,
            RELATED_ID,
            INVOICE_NO,
            INVOICE_PROPERTY,
            INVOICE_TYPE,
            INVOICE_HREF,
            RATIO,
            COLOR_TYPE,
            AMOUNT,
            IS_ENABLE,
            VALID_USERID,
            VALID_STATUS,
            VALID_TIME,
            VALID_COMMENTS,
            INVOICE_PRINT_STATUS,
            INVOICE_CANCEL_STATUS,
            EXPRESS_ID,
            ADD_TIME,
            CREATOR,
            MOD_TIME,
            UPDATER,
            TAG,
            IS_AUTH,
            IS_MONTH_AUTH,
            AUTH_TIME,
            INVOICE_FROM,
            HX_INVOICE_ID,
            OSS_FILE_URL
        FROM
            `T_INVOICE`
        WHERE
            INVOICE_ID =  #{invoiceId,jdbcType=INTEGER}
    </select>
</mapper>
