package com.vedeng.goods.service;

import com.vedeng.common.page.Page;
import com.vedeng.goods.model.dto.GoodsRemovalRecordQueryDto;
import com.vedeng.goods.model.vo.CoreSkuBaseVO;
import com.vedeng.goods.model.vo.GoodsRemovalRecordVo;
import com.vedeng.goods.model.vo.SpuRemovalPreparedVo;
import com.vedeng.goods.model.vo.SpuRemovalDetailVo;

import java.io.IOException;
import java.io.OutputStream;
import java.util.List;

/**
 * <AUTHOR> [<EMAIL>]
 */
public interface GoodsRemovalService {

    /**
     * @param goodsRemovalRecordQueryDto
     * @return
     */
    List<GoodsRemovalRecordVo> listGoodsRemovalRecordWithPage(GoodsRemovalRecordQueryDto goodsRemovalRecordQueryDto, Page page);


    /**
     * 获取spu移动阶段目标分类的需要增补的属性及属性值明细
     *
     * @param targetCategoryId
     * @param spuId
     * @return
     */
    List<SpuRemovalDetailVo> listCategoryAttributeDetailsBeforeChange(Integer targetCategoryId, Integer spuId);


    /**
     * 获取spu移动阶段目标分类的需要增补的属性及属性值明细
     *
     * @param targetCategoryId
     * @param spuRemovalLogId
     * @return
     */
    List<SpuRemovalDetailVo> listCategoryAttributeDetailsAfterChange(Integer targetCategoryId, Integer spuRemovalLogId);

    /**
     * 获取spu下sku的明细
     *
     * @param spuId
     * @return
     */
    List<CoreSkuBaseVO> listSkuInfoAfterSpuRemoval(Integer spuId);

    /**
     * SPU迁移--迁移阶段
     *
     * @param categoryId
     * @param spuIds
     * @return
     * @throws IllegalArgumentException if parameter is invalid.
     * @throws IllegalStateException
     */
    List<SpuRemovalPreparedVo> prepareSpuRemoval(Integer categoryId, Integer[] spuIds);


    /**
     * spu移动--确认阶段
     *
     * @param categoryId
     * @param spuIds
     * @param reason
     * @return
     * @throws IllegalArgumentException if parameter is invalid.
     * @throws IllegalStateException
     */
    void confirmSpuRemoval(Integer categoryId, Integer[] spuIds, String reason);

    /**
     * @param spuRemovalLogIds
     * @param outputStream
     * @throws IOException
     */
    void exportGoodsRemovalRecords(Integer[] spuRemovalLogIds, OutputStream outputStream) throws IOException;
}
