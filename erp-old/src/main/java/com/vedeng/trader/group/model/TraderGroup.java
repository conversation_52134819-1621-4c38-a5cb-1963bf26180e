package com.vedeng.trader.group.model;

import java.io.Serializable;

/**
 *客户分群信息
 *
 * <AUTHOR>
 * @date $
 */
public class TraderGroup implements Serializable {
    private static final long serialVersionUID = 1L;
    private Integer traderGroupId;
    private String traderGroupName;
    private String traderTagIds;
    private Long addTime;
    private String creator;
    private Long updateTime;
    private Integer updater;
    private Integer isDelete;

    public Integer getTraderGroupId() {
        return traderGroupId;
    }

    public void setTraderGroupId(Integer traderGroupId) {
        this.traderGroupId = traderGroupId;
    }

    public String getTraderGroupName() {
        return traderGroupName;
    }

    public void setTraderGroupName(String traderGroupName) {
        this.traderGroupName = traderGroupName;
    }

    public String getTraderTagIds() {
        return traderTagIds;
    }

    public void setTraderTagIds(String traderTagIds) {
        this.traderTagIds = traderTagIds;
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    @Override
    public String toString() {
        return "TraderGroup{" +
                "traderGroupId=" + traderGroupId +
                ", traderGroupName='" + traderGroupName + '\'' +
                '}';
    }
}
