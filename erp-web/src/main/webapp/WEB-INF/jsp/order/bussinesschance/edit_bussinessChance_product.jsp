<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/new/css/common/global.css">
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/new/css/pages/goods/basecategory/add.css?rnd=${resourceVersionKey}">
</head>

<body>


<div class="form-wrap">
    <div class="form-container base-form form-span-8">
        <div class="form-title" style="text-align: center;">手动添加产品</div>
        <div style="float: right;margin: 35px">
            <input class="btn large btn-blue" type="button" onclick="addBussinessChanceProduct()" value="自动检索">
        </div>

        <div class="form-block">
            <div class="form-block-title">基本信息</div>
            <div class="form-item">
                <div class="form-label"><span class="must">*</span>产品名称：</div>
                <div class="form-fields">
                    <div class="form-col col-8">
                        <input id="productName" type="text" autocomplete="off" valid-max="64" class="input-text">
                        <span style="color: red" id="productNameMsg"></span>
                    </div>
                </div>
            </div>

            <div class="form-item">
                <div class="form-label">品牌：</div>
                <div class="form-fields">
                    <div class="form-col col-8">
                        <input id="brand" type="text" autocomplete="off" valid-max="64" class="input-text">
                    </div>
                </div>
            </div>

            <div class="form-item">
                <div class="form-label">型号：</div>
                <div class="form-fields">
                    <div class="form-col col-8">
                        <input id="model" type="text" autocomplete="off" valid-max="64" class="input-text">
                    </div>
                </div>
            </div>

        </div>
        <div class="form-btn">
            <div class="form-fields">
                <button class="btn btn-blue btn-large" onclick="addProductByEdit()">确定</button>
                <a class="btn btn-large" onclick="addBussinessChanceProduct()">取消</a>
            </div>
        </div>
    </div>
</div>

<script type="text/tmpl" class="J-attr-tmpl">
        <div class="attr-tr cf J-attr-item">
            <div class="attr-item col-4">
                <div class="J-attr-name-wrap"></div>
                <input type="hidden" value="" class="J-attr-name" name="baseAttributeId">
            </div>
            <div class="attr-item col-4">
                <div class="J-attr-value-wrap"></div>
                <input type="hidden" value="" class="J-attr-value" name="baseAttributeValueIds">
            </div>
            <div class="col-1 attr-del">
                <i class="vd-icon icon-recycle J-attr-del"></i>
            </div>
        </div>

</script>
<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/util.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/select.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/artDialog/2.0.0/artDialog.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.validate.js"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/suggestSelect.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/order/bussinesschance/edit_bussinessChance_product.js?rnd=${resourceVersionKey}"></script>
</body>

</html>