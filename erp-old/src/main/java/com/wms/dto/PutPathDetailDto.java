package com.wms.dto;

/**
 * 图片路径下传详情
 */
public class PutPathDetailDto {

    /**
     * ERP单据编号(Y)
     */
    private String SOReference1;

    /**
     * WMS单据编号(Y)
     */
    private String OrderNo;

    /**
     * WMS单据行号
     */
    private String OrderlineNo;

    /**
     * 图片类型（Y）
     * 1 发票
     * 2 出库随货同行单
     */
    private String Picture_type;

    /**
     * 图片路径（Y）
     */
    private String Picture_path;

    /**
     * 预留字段
     */
    private String UserDefine1;

    /**
     * 预留字段
     */
    private String UserDefine2;

    /**
     * 预留字段
     */
    private String UserDefine3;

    /**
     * 预留字段
     */
    private String UserDefine4;

    /**
     * 预留字段
     */
    private String UserDefine5;

    public String getSOReference1() {
        return SOReference1;
    }

    public void setSOReference1(String SOReference1) {
        this.SOReference1 = SOReference1;
    }

    public String getOrderNo() {
        return OrderNo;
    }

    public void setOrderNo(String orderNo) {
        OrderNo = orderNo;
    }

    public String getOrderlineNo() {
        return OrderlineNo;
    }

    public void setOrderlineNo(String orderlineNo) {
        OrderlineNo = orderlineNo;
    }

    public String getPicture_type() {
        return Picture_type;
    }

    public void setPicture_type(String picture_type) {
        Picture_type = picture_type;
    }

    public String getPicture_path() {
        return Picture_path;
    }

    public void setPicture_path(String picture_path) {
        Picture_path = picture_path;
    }

    public String getUserDefine1() {
        return UserDefine1;
    }

    public void setUserDefine1(String userDefine1) {
        UserDefine1 = userDefine1;
    }

    public String getUserDefine2() {
        return UserDefine2;
    }

    public void setUserDefine2(String userDefine2) {
        UserDefine2 = userDefine2;
    }

    public String getUserDefine3() {
        return UserDefine3;
    }

    public void setUserDefine3(String userDefine3) {
        UserDefine3 = userDefine3;
    }

    public String getUserDefine4() {
        return UserDefine4;
    }

    public void setUserDefine4(String userDefine4) {
        UserDefine4 = userDefine4;
    }

    public String getUserDefine5() {
        return UserDefine5;
    }

    public void setUserDefine5(String userDefine5) {
        UserDefine5 = userDefine5;
    }

    @Override
    public String toString() {
        return "PutPathDetailDto{" +
                "SOReference1='" + SOReference1 + '\'' +
                ", OrderNo='" + OrderNo + '\'' +
                ", OrderlineNo='" + OrderlineNo + '\'' +
                ", Picture_type='" + Picture_type + '\'' +
                ", Picture_path='" + Picture_path + '\'' +
                ", UserDefine1='" + UserDefine1 + '\'' +
                ", UserDefine2='" + UserDefine2 + '\'' +
                ", UserDefine3='" + UserDefine3 + '\'' +
                ", UserDefine4='" + UserDefine4 + '\'' +
                ", UserDefine5='" + UserDefine5 + '\'' +
                '}';
    }
}
