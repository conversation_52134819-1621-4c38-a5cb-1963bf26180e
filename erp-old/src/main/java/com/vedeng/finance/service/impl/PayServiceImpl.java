package com.vedeng.finance.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.OrderNoDict;
import com.vedeng.finance.dao.CapitalBillDetailMapper;
import com.vedeng.finance.dao.CapitalBillMapper;
import com.vedeng.finance.model.CapitalBill;
import com.vedeng.finance.model.CapitalBillDetail;
import com.vedeng.finance.model.PayOrder;
import com.vedeng.finance.service.CapitalBillService;
import com.vedeng.finance.service.PayService;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.HcOrderAutoAuditService;
import com.wms.service.util.GlobalThreadPool;
import com.wms.service.LogicalSaleorderChooseService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @ClassName PayServiceImpl.java
 * @Description TODO 支付信息推送
 * @createTime 2020年09月24日 17:52:00
 */
@Service
public class PayServiceImpl implements PayService {
    Logger logger= LoggerFactory.getLogger(PayServiceImpl.class);

    @Resource
    private SaleorderMapper saleorderMapper;

    @Autowired
    private LogicalSaleorderChooseService logicalSaleorderChooseService;

    @Resource
    private CapitalBillMapper capitalBillMapper;

    @Resource
    private CapitalBillDetailMapper capitalBillDetailMapper;

    @Resource
    private AfterSalesMapper afterSalesMapper;

    @Resource
    private HcOrderAutoAuditService hcOrderAutoAuditService;

    @Autowired
    @Qualifier("capitalBillService")
    protected CapitalBillService capitalBillService;

    @Autowired
    private AfterSalesService afterSalesOrderService;

    @Resource
    private OrderNoDict orderNoDict;
    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    public ResultInfo orderPay(PayOrder payOrder) throws Exception {
        try {
            // 根据订单检查订单是否存在
            Saleorder saleorder = new Saleorder();
            saleorder.setSaleorderNo(payOrder.getOrderNo());
            Saleorder orderInfo = saleorderMapper.getSaleorderBySaleorderNo(saleorder);

            if (null == orderInfo) {
                return new ResultInfo(-1, "订单不存在");
            }
            // 订单是否已经付款完成
            if (0 != orderInfo.getPaymentStatus()) {  //1 对于医械购是已付款 对于erp是部分付款
                return new ResultInfo(-1, "订单付款状态异常");
            }

            // 流水是否存在
            CapitalBill capitalBill = new CapitalBill();
            capitalBill.setTranFlow(payOrder.getTraderNo());// 支付宝/微信流水号
            capitalBill.setTraderType(1);// 收入
            Integer traderMode = 0;
            Integer payType = 0;// 支付方式 1支付宝、2微信、3银行
            switch (payOrder.getTraderMode()) {// 31支付宝,32微信
                case 31:
                    traderMode = 520;
                    payType = 1;
                    break;
                case 32:
                    traderMode = 522;
                    payType = 2;
                    break;
            }
            capitalBill.setTraderMode(traderMode);// 支付宝、微信
            CapitalBill bill = capitalBillMapper.getCapitalBillByTranFlow(capitalBill);

            // 流水已存在
            if (null != bill) {
                return new ResultInfo(-1, "订单流水已存在");
            }

            /*************** 订单付款状态修改Start ***************/
            /**
             *VDERP-1505 HC订单价格设置可修改
             * 需要校验实际付款金额来判断付款状态
             */
            BigDecimal realAmount =  saleorderMapper.getRealAmount(orderInfo.getSaleorderId());
            logger.info("订单的 单号:{},实际金额为:{}",orderInfo.getSaleorderNo(),realAmount);
            logger.info("推送的 单号:{},付款价格为:{}" ,orderInfo.getSaleorderNo(),payOrder.getRealTotalMoney());
            if (payOrder.getRealTotalMoney().compareTo(realAmount) > -1){
                orderInfo.setPaymentStatus(2); //全部付款
                GlobalThreadPool.submitMessage(new Runnable() {
                    @Override
                    public void run() {
                        try {

                                 String uuid = UUID.randomUUID().toString().replace("-", "");
                                logger.info("开始执行订单付款状态修改,uuid:{}",uuid);
                                // 获取交易信息数据
                                CapitalBill capitalBill = new CapitalBill();
                                capitalBill.setOperationType("finance_sale_detail");
                                CapitalBillDetail capitalBillDetail = new CapitalBillDetail();
                                capitalBillDetail.setOrderType(ErpConst.ONE);// 销售订单类型
                                capitalBillDetail.setOrderNo(orderInfo.getSaleorderNo());
                                capitalBillDetail.setRelatedId(orderInfo.getSaleorderId());
                                capitalBill.setCapitalBillDetail(capitalBillDetail);
                                List<CapitalBill> capitalBillList = capitalBillService.getCapitalBillList(capitalBill);
                                //交易数据为空时，满足HC订单首次达到全部付款条件
                                if (CollectionUtils.isEmpty(capitalBillList)){
                                    orderInfo.setVerifyStatus(saleorderMapper.getSaleordergetVerifyStatus(orderInfo.getSaleorderId()));
                                    Integer verifyStatus=orderInfo.getVerifyStatus();
                                    //满足，订单审核状态不是审核中/审核通过条件，触发自动审核
                                    if (null == verifyStatus || (null != verifyStatus && verifyStatus != 0 && verifyStatus != 1)){
                                        Saleorder order=new Saleorder();
                                        order.setSaleorderId(orderInfo.getSaleorderId());
                                        //耗材订单自动审核流程
                                        order.setAutoAudit(1);
                                        int i = saleorderMapper.updateByPrimaryKeySelective(order);
                                        if (i>0) {
                                            logger.info("HC订单自动审核开始============================"+ JSON.toJSONString(orderInfo));
                                            hcOrderAutoAuditService.hcOrderAutoAudit(orderInfo);
                                            logger.info("HC订单自动审核结束============================"+ JSON.toJSONString(orderInfo));
                                        }else {
                                            logger.info("HC订单自动审核异常,设置自动审核失败！");
                                        }

                                }
                            }
                            logger.info("结束执行订单付款状态修改,uuid:{}",uuid);
                        }catch (Exception e){
                            logger.error("sendPrint error",e);
                        }
                    }
                });
            } else {
                orderInfo.setPaymentStatus(1); //部分付款
            }
            orderInfo.setPaymentTime(payOrder.getTraderTime());
            orderInfo.setIsPayment(1);
            orderInfo.setPaymentMode(0);// 线上支付
            orderInfo.setPayType(payType);
            orderInfo.setSatisfyDeliveryTime(DateUtil.sysTimeMillis());
            int updatePayInfo = saleorderMapper.updateSaleorderPayStatus(orderInfo);
            if (updatePayInfo <= 0) {
                throw new Exception("订单支付失败");
            }
            /*************** 订单付款状态修改End ***************/

            /*************** 订单流水Start ***************/
            long sysTimeMillis = DateUtil.sysTimeMillis();
            capitalBill.setCompanyId(payOrder.getCompanyId());
            capitalBill.setTraderTime(payOrder.getTraderTime());
            capitalBill.setTraderSubject(2);// 对私
            capitalBill.setAmount(payOrder.getRealTotalMoney());
            capitalBill.setCurrencyUnitId(1);
            capitalBill.setAddTime(sysTimeMillis);
            capitalBill.setCreator(1);

            int insert = capitalBillMapper.insertSelective(capitalBill);
            // 流水添加错误
            if (insert <= 0) {
                throw new Exception("支付流水添加异常");
            }

            Integer capitalBillId = capitalBill.getCapitalBillId();

            String capitalBillNo = orderNoDict.getOrderNum(capitalBillId, 10);

            // 生成资金流水号
            CapitalBill capitalBillExtra = new CapitalBill();
            capitalBillExtra.setCapitalBillId(capitalBillId);
            capitalBillExtra.setCapitalBillNo(capitalBillNo);
            capitalBillMapper.updateByPrimaryKeySelective(capitalBillExtra);

            CapitalBillDetail capitalBillDetail = new CapitalBillDetail();
            capitalBillDetail.setCapitalBillId(capitalBillId);
            capitalBillDetail.setBussinessType(526);// 订单收款
            capitalBillDetail.setOrderType(1);
            capitalBillDetail.setOrderNo(orderInfo.getSaleorderNo());
            capitalBillDetail.setRelatedId(orderInfo.getSaleorderId());
            capitalBillDetail.setAmount(payOrder.getRealTotalMoney());
            capitalBillDetail.setTraderId(orderInfo.getTraderId());
            capitalBillDetail.setTraderType(1);// 所属类型 1::经销商（包含终端）2:供应商
            if (null != orderInfo.getOwnerUserId() && orderInfo.getOwnerUserId() > 0) {
                capitalBillDetail.setUserId(orderInfo.getOwnerUserId());
                CapitalBillDetail capitalUser = capitalBillDetailMapper.getCapitalUser(capitalBillDetail);
                if (null != capitalUser) {
                    capitalBillDetail.setOrgId(capitalUser.getOrgId());
                    capitalBillDetail.setOrgName(capitalUser.getOrgName());
                }
            }

            int insertSelective = capitalBillDetailMapper.insertSelective(capitalBillDetail);
            if (insertSelective <= 0) {
                throw new Exception("支付流水详情添加异常");
            }
            User user = new User();
            user.setUsername("admin");
            user.setUserId(1);
            logicalSaleorderChooseService.chooseLogicalSaleorder(orderInfo,user);
            /*************** 订单流水End ***************/
            ResultInfo resultInfo = new ResultInfo();
            resultInfo.setCode(0);
            resultInfo.setMessage("操作成功");
            resultInfo.setData(capitalBillNo);
            return resultInfo;
        } catch (Exception e) {
            throw new Exception("操作失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    public ResultInfo orderRefund(PayOrder payOrder) throws Exception {
        try {
            // 根据订单检查订单是否存在
            Saleorder saleorder = new Saleorder();
            saleorder.setSaleorderNo(payOrder.getOrderNo());
            Saleorder orderInfo = saleorderMapper.getSaleorderBySaleorderNo(saleorder);

            if (null == orderInfo) {
                return new ResultInfo(-1, "订单不存在");
            }

            // 流水是否存在
            CapitalBill capitalBill = new CapitalBill();
            capitalBill.setTranFlow(payOrder.getTraderNo());// 支付宝/微信流水号
            capitalBill.setTraderType(2);// 支出
            Integer traderMode = 0;
            switch (payOrder.getTraderMode()) {// 31支付宝,32微信
                case 31:
                    traderMode = 520;
                    break;
                case 32:
                    traderMode = 522;
                    break;
            }
            capitalBill.setTraderMode(traderMode);// 支付宝、微信
            /*
             * CapitalBill bill =
             * capitalBillMapper.getCapitalBillByTranFlow(capitalBill);
             *
             * //流水已存在 if(null != bill){ return new ResultInfo(-1,"订单流水已存在"); }
             */

            /*************** 订单流水Start ***************/
            long sysTimeMillis = DateUtil.sysTimeMillis();
            capitalBill.setCompanyId(payOrder.getCompanyId());
            capitalBill.setTraderTime(payOrder.getTraderTime());
            capitalBill.setTraderSubject(2);// 对私
            capitalBill.setAmount(payOrder.getRealTotalMoney());
            capitalBill.setCurrencyUnitId(1);
            capitalBill.setAddTime(sysTimeMillis);
            capitalBill.setCreator(1);

            int insert = capitalBillMapper.insertSelective(capitalBill);
            // 流水添加错误
            if (insert <= 0) {
                logger.error("退款流水添加异常"+payOrder.getOrderNo());
                throw new Exception("退款流水添加异常");
            }

            Integer capitalBillId = capitalBill.getCapitalBillId();

            String capitalBillNo = orderNoDict.getOrderNum(capitalBillId, 10);

            // 生成资金流水号
            CapitalBill capitalBillExtra = new CapitalBill();
            capitalBillExtra.setCapitalBillId(capitalBillId);
            capitalBillExtra.setCapitalBillNo(capitalBillNo);
            capitalBillMapper.updateByPrimaryKeySelective(capitalBillExtra);

            // 获取售后单信息
            AfterSales afterSales = afterSalesMapper.getAfterSalesByNo(payOrder.getOutRequestNo());
            Integer relatedId = 0;
            if (null != afterSales) {
                relatedId = afterSales.getAfterSalesId();
            }

            CapitalBillDetail capitalBillDetail = new CapitalBillDetail();
            capitalBillDetail.setCapitalBillId(capitalBillId);
            capitalBillDetail.setBussinessType(531);// 退款
            capitalBillDetail.setOrderType(3);// 订单类型 1销售订单 2采购订单 3售后订单
            capitalBillDetail.setOrderNo(payOrder.getOutRequestNo());
            capitalBillDetail.setRelatedId(relatedId);
            capitalBillDetail.setAmount(payOrder.getRealTotalMoney());
            capitalBillDetail.setTraderId(orderInfo.getTraderId());
            capitalBillDetail.setTraderType(1);// 所属类型 1::经销商（包含终端）2:供应商
            if (null != orderInfo.getOwnerUserId() && orderInfo.getOwnerUserId() > 0) {
                capitalBillDetail.setUserId(orderInfo.getOwnerUserId());
                CapitalBillDetail capitalUser = capitalBillDetailMapper.getCapitalUser(capitalBillDetail);
                if (null != capitalUser) {
                    capitalBillDetail.setOrgId(capitalUser.getOrgId());
                    capitalBillDetail.setOrgName(capitalUser.getOrgName());
                }
            }

            int insertSelective = capitalBillDetailMapper.insertSelective(capitalBillDetail);
            if (insertSelective <= 0) {
                throw new Exception("退款流水详情添加异常"+payOrder.getOrderNo());
            }

            afterSalesOrderService.refreshAmountRefundStatus(afterSales.getAfterSalesId());
            /*************** 订单流水End ***************/
            ResultInfo resultInfo = new ResultInfo();
            resultInfo.setCode(0);
            resultInfo.setMessage("操作成功");
            resultInfo.setData(capitalBillNo);
            return resultInfo;
        } catch (Exception e) {
            logger.error("退款流水详情添加异常"+payOrder.getOrderNo(),e);
            throw new Exception("操作失败");
        }
    }
}
