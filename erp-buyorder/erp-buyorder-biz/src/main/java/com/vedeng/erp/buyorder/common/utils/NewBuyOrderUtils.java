package com.vedeng.erp.buyorder.common.utils;

import com.vedeng.activiti.model.AssigneeVo;
import com.vedeng.common.util.StringUtil;
import com.vedeng.erp.buyorder.common.constant.BuyOrderModifyStatus;
import com.vedeng.erp.buyorder.common.constant.BuyOrderStatus;
import com.vedeng.erp.buyorder.common.constant.CommonBuyOrder;
import com.vedeng.erp.buyorder.dto.StatusNode;
import com.vedeng.order.model.vo.BuyorderVo;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.history.HistoricActivityInstance;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.web.servlet.ModelAndView;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description com.vedeng.orderstream.buyorder.utils
 * @Date 2021/10/15 14:43
 */
@Slf4j
public class NewBuyOrderUtils {

    /**
     * 根据采购单主、子状态计算新的采购单状态，因为存在多个节点同时高亮的情况，因此用逗号隔开
     * 由于要兼容列表页筛选字段的定时任务，所以此处没有返回具体的相加后的数值，而是用逗号隔开
     *
     * @param buyorder buyorder
     * @return 计算后的采购单状态
     */
    public static String calcStatusAndSubStatusOfBuyOrder(BuyorderVo buyorder) {
        // 节点对应规则: 1待确认; 2审核中; 3待付款; 4待发货; 5待收货; 6待收票; 7已完结; 8已关闭
        if (buyorder.getStatus() == 0) {
            if (buyorder.getVerifyStatus() != null && buyorder.getVerifyStatus() == 0) {
                return "2"; // 审核中
            } else {
                return "1"; // 待确认
            }
        } else if (buyorder.getStatus() == 1) {
            // 付款状态 1:全部付款 0: 部分/未付款
            int paymentStatus = buyorder.getPaymentStatus() == 2 ? 1 : 0;
            // 发货状态 2:全部发货 0: 部分/未发货
            int deliveryStatus = buyorder.getDeliveryStatus() == 2 ? 2 : 0;
            // 收货状态 4:全部收货 0: 部分/未收货
            int arrivalStatus = buyorder.getArrivalStatus() == 2 ? 4 : 0;
            // 收票状态 8:全部收票 0:部分/未收票
            int invoiceStatus = buyorder.getInvoiceStatus() == 2 ? 8 : 0;

            // 新订单流中对票货款状态做了联动，因此正常情况下，下列一些情况不会出现，但为了不遗漏还是罗列了所有16种情况
            switch (paymentStatus + deliveryStatus + arrivalStatus + invoiceStatus) {
                case 0:
                    return "3,4,5,6"; // 待付款+待发货+待收货+待收票
                case 8:
                    return "3,4,5"; // 待付款+待发货+待收货
                case 4:
                    return "3,4,6"; // 待付款+待发货+待收票
                case 12:
                    return "3,4"; // 待付款+待发货
                case 2:
                    return "3,5,6"; // 待付款+待收货+待收票
                case 10:
                    return "3,5"; // 待付款+待收货
                case 6:
                    return "3,6"; // 待付款+待收票
                case 14:
                    return "3"; // 待付款
                case 1:
                    return "4,5,6"; // 待发货+待收货+待收票
                case 9:
                    return "4,5"; // 待发货+待收货
                case 5:
                    return "4,6"; // 待发货+待收票
                case 13:
                    return "4"; // 待发货
                case 3:
                    return "5,6"; // 待收货+待收票
                case 11:
                    return "5"; // 待收货
                case 7:
                    return "6"; // 待收票
                case 15:
                    return "0"; // 票货款全部完成，理论上应该不会出现，会变为已完结
                default:
                    return "0";
            }
        } else if (buyorder.getStatus() == 2) {
            return "7"; // 已完结
        } else {
            return "8"; // 已关闭
        }
    }

    /**
     * 初始化状态节点并根据订单状态装配
     */

    // 采购单状态节点
    private static String[] status = {BuyOrderStatus.WAIT_CONFIRM, BuyOrderStatus.VALIDATING, BuyOrderStatus.WAIT_PAYMENT,
            BuyOrderStatus.WAIT_DELIVERY, BuyOrderStatus.WAIT_RECEIVE_GOODS, BuyOrderStatus.WAIT_RECEIVE_INVOICE,
            BuyOrderStatus.COMPLETED, BuyOrderStatus.CLOSED};

    // 采购单审核状态节点
    private static String[] verifyStatus = {BuyOrderStatus.START_AUDIT, BuyOrderStatus.APPLICANT, BuyOrderStatus.PM_VALIDATING,
            BuyOrderStatus.QUALITY_AUTOMATIC_VALIDATING, BuyOrderStatus.QUALITY_DEPART_VALIDATING,
            BuyOrderStatus.FINISHED, BuyOrderStatus.REJECT};

    // 采购单付款审核状态节点
    private static String[] paymentStatus = {BuyOrderStatus.START_AUDIT, BuyOrderStatus.APPLICANT, BuyOrderStatus.FINANCIAL_VOUCHER,
            BuyOrderStatus.FINANCIAL_AUDIT, BuyOrderStatus.FINISHED, BuyOrderStatus.REJECT};

    // 采购单申请修改审核节点
    private static String[] modifyApplyStatus = {BuyOrderModifyStatus.START_AUDIT, BuyOrderModifyStatus.APPLICANT, BuyOrderModifyStatus.SUPERVISOR_AUDIT,
            BuyOrderModifyStatus.FINISHED, BuyOrderModifyStatus.REJECT};

    private static List<StatusNode> initStatusNodeList(String type) {
        List<StatusNode> statusNodeList = new ArrayList<>();
        String[] temp;
        switch (type) {
            case CommonBuyOrder.BUY_ORDER_STATUS:
                temp = status;
                break;
            case CommonBuyOrder.BUY_ORDER_VERIFY:
                temp = verifyStatus;
                break;
            case CommonBuyOrder.PAYMENT_VERIFY:
                temp = paymentStatus;
                break;
            default:
                temp = modifyApplyStatus;
                break;
        }
        for (int i = 0; i < temp.length; i++) {
            StatusNode statusNode = new StatusNode(temp[i], 0, i + 1);
            statusNodeList.add(statusNode);
        }
        return statusNodeList;
    }

    /**
     * 根据采购单状态高亮 StatusNode
     *
     * @param calcStatus 计算后的采购单状态
     * @return 主流程状态节点集合
     */
    public static List<StatusNode> lightStatusNode(BuyorderVo buyorderVo, String calcStatus) {
        // 初始化采购单状态节点 然后根据当前状态设置各个节点的状态
        List<StatusNode> buyOrderStatus = initStatusNodeList(CommonBuyOrder.BUY_ORDER_STATUS);

        // 处理字符串(split的结果是按照字符串出现的顺序排列，因此可以确定当多个节点同时高亮时的首尾节点)
        String[] statusArray = calcStatus.split(",");

        // step1 将所有高亮的节点高亮
        for (String status : statusArray) {
            StatusNode.changeNodeStatusBySort(buyOrderStatus, Integer.valueOf(status), Integer.valueOf(status), 2);
        }

        // step2 将第一个节点之前的节点全部置为1：已进行
        StatusNode.changeNodeStatusBySort(buyOrderStatus, 1, Integer.valueOf(statusArray[0]) - 1, 1);

        // step3 付款 发货 收货 收票四个节点需要一一校验
        // 已关闭的也需要一一校验
        for (StatusNode node : buyOrderStatus) {
            if (buyorderVo.getStatus() == 1) {
                if (node != null && node.getSort() > 2 && node.getSort() < 7 && node.getStatus() != 2) {
                    node.setStatus(1);
                }
            } else if (buyorderVo.getStatus() == 3) {
                if (buyorderVo.getValidStatus() == 0) {
                    // 未生效，则除了第一个待确认为1，其他全是0
                    StatusNode.changeNodeStatusBySort(buyOrderStatus, 2, 7, 0);
                } else {
                    // 已生效，待确认、待审核为1，其他全是0（新订单可以保证关闭的采购单票货款都是未进行，老数据有少部分错误数据，但是不会进入到新订单详情页）
                    StatusNode.changeNodeStatusBySort(buyOrderStatus, 3, 7, 0);
                }
            }
        }

        // step4 删除已关闭或者 已完结节点
        if (buyorderVo.getStatus() == 3) {
            buyOrderStatus.remove(6);
        } else {
            buyOrderStatus.remove(7);
        }

        return buyOrderStatus;
    }

    /**
     * 构造采购单审核流程和付款审核流程节点串
     *
     * @param mav
     * @param leastHistoricActivityInstanceOver 获取到的审核节点
     * @param type                              区分是 订单审核流程 || 付款审核流程 || 修改申请审核流程
     * @return List<StatusNode> 初始化完成的审核节点集合
     */
    public static List<StatusNode> addVerifyStatusNode(ModelAndView mav, List<HistoricActivityInstance> leastHistoricActivityInstanceOver, String type, String verifyUsers, Map<String, Object> commentMap) {

        // 处理掉所有ActivityName为null的节点
        leastHistoricActivityInstanceOver.removeIf(temp -> StringUtil.isEmpty(temp.getActivityName()));

        List<StatusNode> verifyNodeList = new ArrayList<>();
        // 获取最后一个节点的name
        String last;
        if (leastHistoricActivityInstanceOver.size() == 0) {
            return verifyNodeList;
        } else {
            last = leastHistoricActivityInstanceOver.get(leastHistoricActivityInstanceOver.size() - 1).getActivityName();
        }

        ArrayList<AssigneeVo> assigneeVoList = (ArrayList<AssigneeVo>) mav.getModel().get("assigneeVos");

        // 若是已完成的审核流，其最后一个节点为“审核完成” 或 “驳回”，则遍历各个审核节点，动态构建节点串
        if (last.equals(BuyOrderStatus.FINISHED) || last.equals(BuyOrderStatus.REJECT)) {
            for (int i = 0; i < leastHistoricActivityInstanceOver.size(); i++) {
                HistoricActivityInstance historicActivityInstance = leastHistoricActivityInstanceOver.get(i);
                StatusNode tempNode = new StatusNode(historicActivityInstance.getActivityName(), 0, i + 1);
                // 拼接操作人 操作时间 备注等信息
                StringBuffer tempTip = new StringBuffer();
                if (StringUtil.isNotEmpty(historicActivityInstance.getAssignee())) {

                    // VDERP-8831
                    AssigneeVo assigneeVo = assigneeVoList.stream().filter(e -> e.getAssignee().equals(historicActivityInstance.getAssignee())).findFirst().orElse(null);
                    if (Objects.isNull(assigneeVo) || !type.equals(CommonBuyOrder.BUY_ORDER_VERIFY)) {
                        tempTip.append(historicActivityInstance.getAssignee()).append("<br>");
                    } else {
                        tempTip.append(assigneeVo.getRealName()).append("<br>");
                    }

                }
                if (MapUtils.isNotEmpty(commentMap)) {
                    String comment = (String) commentMap.get(historicActivityInstance.getTaskId());
                    if (StringUtils.isNotBlank(comment)) {
                        tempTip.append("<span title=\"").append(comment).append("\" class=\"no-wrap\">").append(comment).append("</span>").append("<br>");
                    }
                }
                tempTip.append(DateFormatUtils.format(historicActivityInstance.getEndTime(), "yyyy-MM-dd HH:mm:ss")).append("</br>");
                tempNode.setTip(tempTip.toString());
                if (last.equals(BuyOrderStatus.REJECT)) {
                    tempNode.setFail(1);
                }
                verifyNodeList.add(tempNode);
            }

        } else {  // 若最后一个节点不为“审核完成” 或 “驳回”，则取当前生效的审核流程，作为默认节点串
            // todo 以后要做成支持实时读取审核流各个节点，目前是写死的，不利于以后审核流程改变的拓展性
            // 区分是 订单审核流程 还是 付款审核流程 或是 修改申请审核流程
            if (type.equals(CommonBuyOrder.BUY_ORDER_VERIFY)) {
                verifyNodeList = initStatusNodeList(CommonBuyOrder.BUY_ORDER_VERIFY);
            } else if (type.equals(CommonBuyOrder.PAYMENT_VERIFY)) {
                verifyNodeList = initStatusNodeList(CommonBuyOrder.PAYMENT_VERIFY);
            } else if (type.equals(CommonBuyOrder.BUY_ORDER_MODIFY_APPLY)) {
                verifyNodeList = initStatusNodeList(CommonBuyOrder.BUY_ORDER_MODIFY_APPLY);
            }

            // 删除最后一个节点"驳回"
            verifyNodeList.remove(verifyNodeList.size() - 1);

            // 遍历节点串，将tip填充
            for (HistoricActivityInstance item : leastHistoricActivityInstanceOver) {
                for (StatusNode node : verifyNodeList) {
                    if (item.getActivityName().equals(node.getLabel())) {
                        StringBuffer tempTip = new StringBuffer();
                        if (!last.equals(item.getActivityName())) {
                            if (StringUtil.isNotEmpty(item.getAssignee())) {

                                // VDERP-8831
                                AssigneeVo assigneeVo = assigneeVoList.stream().filter(e -> e.getAssignee().equals(item.getAssignee())).findFirst().orElse(null);
                                if (Objects.isNull(assigneeVo) || !type.equals(CommonBuyOrder.BUY_ORDER_VERIFY)) {
                                    tempTip.append(item.getAssignee()).append("<br>");
                                } else {
                                    tempTip.append(assigneeVo.getRealName()).append("<br>");
                                }

                            }
                            if (MapUtils.isNotEmpty(commentMap)) {
                                String comment = (String) commentMap.get(item.getTaskId());
                                if (StringUtils.isNotBlank(comment)) {
                                    tempTip.append("<span title=\"").append(comment).append("\" class=\"no-wrap\">").append(comment).append("</span>").append("<br>");
                                }
                            }
                            tempTip.append(DateFormatUtils.format(item.getEndTime(), "yyyy-MM-dd HH:mm:ss")).append("<br>");
                            node.setTip(tempTip.toString());
                        } else {
                            tempTip.append("<span title=\"").append(verifyUsers).append("\" class=\"no-wrap\">").append(verifyUsers).append("</span>");
                            node.setTip(tempTip.toString());
                        }
                    }
                }
            }
        }

        // 根据当前节点更新各个前后节点的状态
        // step1: 根据last查询到对应节点的sort
        Integer currentStatus = 0;
        for (StatusNode node : verifyNodeList) {
            if (node.getLabel().equals(last)) {
                currentStatus = node.getSort();
            }
        }

        // step2: 更新各个节点状态
        StatusNode.changeNodeStatus(verifyNodeList, currentStatus);
        return verifyNodeList;
    }

    private static SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

    public static Long getTimeStamp(String str) {
        if (StringUtils.isNotBlank(str)) {
            try {
                return simpleDateFormat.parse(str).getTime();
            } catch (ParseException e) {
                log.error("【getTimeStamp】 处理异常",e);
            }
        }
        return null;
    }

    /**
     * 构造采购单修改申请详情页 状态节点串
     *
     * @param verifyStatus
     * @return
     */
    public static List<StatusNode> initBuyOrderModifyApplyStatusNode(Integer verifyStatus) {
        List<StatusNode> buyOrderModifyApplyStatusNode = new ArrayList<>();
        StatusNode waitAudit = new StatusNode(BuyOrderModifyStatus.WAIT_VALID, 0, 1);
        StatusNode validating = new StatusNode(BuyOrderModifyStatus.VALIDATING, 0, 2);
        StatusNode completed = new StatusNode(BuyOrderModifyStatus.COMPLETED, 0, 3);
        buyOrderModifyApplyStatusNode.add(waitAudit);
        buyOrderModifyApplyStatusNode.add(validating);
        buyOrderModifyApplyStatusNode.add(completed);
        if (verifyStatus == null) {
            StatusNode.changeNodeStatus(buyOrderModifyApplyStatusNode, 1);
        } else if (verifyStatus == 0) {
            StatusNode.changeNodeStatus(buyOrderModifyApplyStatusNode, 2);
        } else {
            StatusNode.changeNodeStatus(buyOrderModifyApplyStatusNode, 3);
        }
        return buyOrderModifyApplyStatusNode;
    }

    /**
     * 每3位中间添加逗号的格式化显示
     *
     * @param value
     * @return
     */
    public static String getCommaFormat(BigDecimal value) {
        return getFormat(",###.##", value);
    }

    /**
     * 自定义数字格式方法
     *
     * @param style
     * @param value
     * @return
     */
    public static String getFormat(String style, BigDecimal value) {
        DecimalFormat df = new DecimalFormat();
        df.applyPattern(style);// 将格式应用于格式化器
        return df.format(value.doubleValue());
    }

    public static void fillNodeInfo(BuyorderVo buyorderVo, List<StatusNode> statusNodeList) {
        String paymentInfo = buyorderVo.getPaymentStatus() == 0 ? CommonBuyOrder.WAIT_PAY : buyorderVo.getPaymentStatus() == 1 ? CommonBuyOrder.PART_PAY : CommonBuyOrder.ALL_PAY;
        String deliveryInfo = buyorderVo.getDeliveryStatus() == 0 ? CommonBuyOrder.WAIT_DELIVERY : buyorderVo.getDeliveryStatus() == 1 ? CommonBuyOrder.PART_DELIVERY : CommonBuyOrder.ALL_DELIVERY;
        String arrivalInfo = buyorderVo.getArrivalStatus() == 0 ? CommonBuyOrder.WAIT_ARRIVAL : buyorderVo.getArrivalStatus() == 1 ? CommonBuyOrder.PART_ARRIVAL : CommonBuyOrder.ALL_ARRIVAL;
        String invoiceInfo = buyorderVo.getInvoiceStatus() == 0 ? CommonBuyOrder.WAIT_INVOICE : buyorderVo.getInvoiceStatus() == 1 ? CommonBuyOrder.PART_INVOICE : CommonBuyOrder.ALL_INVOICE;
        for (StatusNode node : statusNodeList) {
            switch (node.getSort()) {
                case 3:
                    node.setTip(paymentInfo);
                    break;
                case 4:
                    node.setTip(deliveryInfo);
                    break;
                case 5:
                    node.setTip(arrivalInfo);
                    break;
                case 6:
                    node.setTip(invoiceInfo);
                    break;
            }
        }
    }

}
