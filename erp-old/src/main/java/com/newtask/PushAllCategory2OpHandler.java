package com.newtask;

import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.http.HttpRestClientUtil;
import com.vedeng.common.page.Page;
import com.vedeng.goods.dao.BaseCategoryMapper;
import com.vedeng.goods.model.vo.CategoryToOpVo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <b>Description:</b>全量推送分类到Op<br>
 * <b>Author:calvin</b>
 * <br><b>Date:</b> 2020/8/10
 */
@JobHandler(value = "pushAllCategory2OpHandler")
@Component
public class PushAllCategory2OpHandler extends AbstractJobHandler {

    private Logger logger= LoggerFactory.getLogger(PushAllCategory2OpHandler.class);
    @Autowired
    private BaseCategoryMapper baseCategoryMapper;

    @Value("${operate_url}")
    public String operateUrl;
    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        try {
            XxlJobLogger.log("开始同步分类信息");
            Page page = new Page(1, 20);
            int currentPage = 1;
            Map<String, Object> map = new HashMap<>();
            map.put("page", page);
            String url = operateUrl + "/operationclassify/fullPushCategory";
            do {
                List<CategoryToOpVo> categoryList = baseCategoryMapper.getCategoryLevelAllListPage(map);
                com.alibaba.fastjson.TypeReference<RestfulResult> typeReference = new com.alibaba.fastjson.TypeReference<RestfulResult>() {
                };
                RestfulResult restfulResult = HttpRestClientUtil.restPost(url, typeReference, null, categoryList);
                if(restfulResult==null||!restfulResult.isSuccess()){
                    logger.info("同步到op接口失败");
                    XxlJobLogger.log("同步到op接口失败");
                }
                currentPage++;
                page.setPageNo(currentPage);
            } while (currentPage < page.getTotalPage());
        }catch (Exception ex){
            XxlJobLogger.log("同步失败",ex);
            logger.error("向op同步分类信息失败：",ex);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
