package com.vedeng.common.listener;

import com.google.common.eventbus.Subscribe;
import com.vedeng.common.core.config.IObserver;
import com.vedeng.common.core.listenerEvent.goods.SyncSkuInfo2EsEvent;
import com.vedeng.docSync.service.SyncGoodsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: sku
 * @date 2024/12/16 11:19
 */
@Component
@Slf4j
public class SkuListener implements IObserver {


    @Autowired
    SyncGoodsService  syncGoodsService;


    @Subscribe
    public void syncSkuInfo2EsBySkuIds(SyncSkuInfo2EsEvent syncSkuInfo2EsEvent) {
        log.info("syncSkuInfo2EsBySkuIds:{}",syncSkuInfo2EsEvent);
        syncGoodsService.syncSkuInfo2EsBySkuIds(syncSkuInfo2EsEvent.getSkuIds());
    }

}
