package com.vedeng.goods.mapper;

import com.vedeng.goods.domain.entity.SkuMainDept;
import java.util.List;

import com.vedeng.goods.dto.SkuMainDeptDto;
import org.apache.ibatis.annotations.Param;

public interface SkuMainDeptMapper {
    int deleteByPrimaryKey(Integer skuMainDeptId);

    int insert(SkuMainDept record);

    int insertOrUpdate(SkuMainDept record);

    int insertOrUpdateSelective(SkuMainDept record);

    int insertSelective(SkuMainDept record);

    SkuMainDept selectByPrimaryKey(Integer skuMainDeptId);

    int updateByPrimaryKeySelective(SkuMainDept record);

    int updateByPrimaryKey(SkuMainDept record);

    int updateBatch(List<SkuMainDept> list);

    int updateBatchSelective(List<SkuMainDept> list);

    int batchInsert(@Param("list") List<SkuMainDept> list);

    List<SkuMainDeptDto> getMainDeptBySkuNo(@Param("skuNo") String skuNo);

}