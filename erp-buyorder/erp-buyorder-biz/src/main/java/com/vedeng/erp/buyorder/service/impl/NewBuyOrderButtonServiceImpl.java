package com.vedeng.erp.buyorder.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.model.AfterSaleBuyorderDirectOutLog;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.aftersales.model.vo.AfterSalesInvoiceVo;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.core.enums.BusinessSourceTypeEnum;
import com.vedeng.erp.buyorder.common.constant.AfterSalesConstant;
import com.vedeng.erp.buyorder.service.NewBuyOrderButtonService;
import com.vedeng.erp.finance.cmd.SettlementBillQueryCmd;
import com.vedeng.erp.finance.service.SettlementBillApiService;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.erp.wms.dto.AfterSaleBuyorderDirectOutLogDto;
import com.vedeng.erp.wms.dto.WarehouseGoodsOperateLogDto;
import com.vedeng.erp.wms.service.WarehouseGoodsOperateLogApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description: 采购单详情页按钮状态初始化
 * @Param:
 * @return:
 */
@Service
@Slf4j
public class NewBuyOrderButtonServiceImpl implements NewBuyOrderButtonService {

    @Value("${specialGoodsInAfterSlaesOfBuyorder}")
    private String specialGoodsInAfterSlaesOfBuyorder;

    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Autowired
    private UserApiService userApiService;

    @Autowired
    private WarehouseGoodsOperateLogApiService warehouseGoodsOperateLogApiService;

    @Autowired
    private SettlementBillApiService settlementBillApiService;

    /**
     * 采购单详情页按钮状态初始化
     * @param mav
     * @param currentUser
     * @param afterSalesVo
     */
    @Override
    public void buttonStatusInit(ModelAndView mav, User currentUser, AfterSalesVo afterSalesVo) {
        log.info("按钮权限初始化：{}",JSONUtil.toJsonStr(afterSalesVo));
        if (mav.getModel().get("isCreator") != null || isCreatorParent(afterSalesVo.getCreator(),currentUser)) {
            //编辑
            if (AfterSalesConstant.CONFIRM_CODE.equals(afterSalesVo.getAtferSalesStatus()) && (AfterSalesConstant.VALID_FAIL_CODE.equals(afterSalesVo.getStatus()) || AfterSalesConstant.VALID_CONFIRM_CODE.equals(afterSalesVo.getStatus()))) {
                log.info("【按钮权限】编辑，有权限");
                mav.addObject("editFlag", 1);
                if (mav.getModel().get("isCreator") != null){
                    log.info("【按钮权限】申请审核，有权限");
                    mav.addObject("applyFlag", 1);
                }
            }


            //确认完成
            /**
             * 进行中&不存在付款审核或售后完结审核中
             * 直发/普发退货方式：sku全部收货或退的数量＞未收货数量时，有出库记录
             * 直发/普发退货方式：sku未收货或退的数量≤未收货数量时，出库记录为空
             * 有退款记录，退款状态：已退款或无退款且偿还账期=0
             * 有退票记录时，退票状态为已退票
             */
            if (AfterSalesConstant.TH_BUYORDER_AFTER_SALE_CODE.equals(afterSalesVo.getType())) {
                if (AfterSalesConstant.PROCRESSING_CODE.equals(afterSalesVo.getAtferSalesStatus()) && mav.getModel().get("isOverVerify") == null
                        && mav.getModel().get("isPayApply") == null) {
                    boolean flag = true;
                    //采购售后确认完成计算时，需要排除运费等特殊商品
                    List<String> specialGoodsListInAfterSlaesOfBuyorder =
                            Arrays.stream(specialGoodsInAfterSlaesOfBuyorder.split(",")).collect(Collectors.toList());
                    List<AfterSalesGoodsVo> afterSalesGoodsList = afterSalesVo.getAfterSalesGoodsList().stream()
                            .filter(item -> !specialGoodsListInAfterSlaesOfBuyorder.contains(item.getSku()))
                            .collect(Collectors.toList());

                    //标识采购售后全部出库
                    if (CollectionUtils.isNotEmpty(afterSalesGoodsList)) {
                        boolean outFlag = true;
                        for (AfterSalesGoodsVo afterSalesGoodsVo : afterSalesGoodsList) {
                            Integer unReceiveNum = buyorderGoodsMapper.getBuyorderGoodsUnReceiveNum(afterSalesGoodsVo.getOrderDetailId());
                            if(ErpConst.ZERO.compareTo(unReceiveNum) > 0){
                                unReceiveNum = 0;
                            }
                            if (AfterSalesConstant.ALL_RECEIVE_CODE.equals(afterSalesGoodsVo.getBuyorderArrivalStatus()) || afterSalesGoodsVo.getNum().compareTo(unReceiveNum) > 0) {
                                if (AfterSalesConstant.UN_DELIVERY_DIRECT_CODE.equals(afterSalesGoodsVo.getDeliveryDirect())) {
                                    List<WarehouseGoodsOperateLogDto> warehouseGoodsOperateLogList = this.getBuyorderAfterSaleLog(afterSalesGoodsVo.getAfterSalesGoodsId());
                                    if (CollectionUtils.isEmpty(warehouseGoodsOperateLogList)) {
                                        flag = false;
                                        outFlag = false;
                                        log.info("【按钮权限】确认完成，flag:{},outFlag:{}",flag,outFlag);
                                    }
                                } else if (AfterSalesConstant.IS_DELIVERY_DIRECT_CODE.equals(afterSalesGoodsVo.getDeliveryDirect())) {
                                    List<AfterSaleBuyorderDirectOutLogDto> afterSaleBuyorderDirectOutLogList = this.getBuyorderAfterSaleDirectLog(afterSalesGoodsVo.getAfterSalesGoodsId());
                                    if (CollectionUtils.isEmpty(afterSaleBuyorderDirectOutLogList)) {
                                        flag = false;
                                        outFlag = false;
                                        log.info("【按钮权限】确认完成，flag:{},outFlag:{}",flag,outFlag);
                                    }
                                }
                            } else if (AfterSalesConstant.NO_RECEIVE_CODE.equals(afterSalesGoodsVo.getBuyorderArrivalStatus()) || afterSalesGoodsVo.getNum().compareTo(unReceiveNum) <= 0) {
                                if (AfterSalesConstant.UN_DELIVERY_DIRECT_CODE.equals(afterSalesGoodsVo.getDeliveryDirect())) {
                                    List<WarehouseGoodsOperateLogDto> warehouseGoodsOperateLogList = this.getBuyorderAfterSaleLog(afterSalesGoodsVo.getAfterSalesGoodsId());
                                    if (CollectionUtils.isNotEmpty(warehouseGoodsOperateLogList)) {
                                        flag = false;
                                        outFlag = false;
                                        log.info("【按钮权限】确认完成，flag:{},outFlag:{}",flag,outFlag);
                                    }
                                } else if (AfterSalesConstant.IS_DELIVERY_DIRECT_CODE.equals(afterSalesGoodsVo.getDeliveryDirect())) {
                                    List<AfterSaleBuyorderDirectOutLogDto> afterSaleBuyorderDirectOutLogList = this.getBuyorderAfterSaleDirectLog(afterSalesGoodsVo.getAfterSalesGoodsId());
                                    if (CollectionUtils.isNotEmpty(afterSaleBuyorderDirectOutLogList)) {
                                        flag = false;
                                        outFlag = false;
                                        log.info("【按钮权限】确认完成，flag:{},outFlag:{}",flag,outFlag);
                                    }
                                }
                            }
                        }
                        mav.addObject("outFlag",outFlag);
                    }

                    if(!Arrays.asList(AfterSalesConstant.ALL_REFOUND_CODE,AfterSalesConstant.NO_REFOUND_CODE).contains(afterSalesVo.getRefundAmountStatus())){
                        flag = false;
                        log.info("【按钮权限】确认完成，flag:{},原因：当前退款状态：{}",flag,afterSalesVo.getRefundAmountStatus());
                    }


                    if (CollectionUtils.isNotEmpty(afterSalesVo.getAfterSalesInvoiceVoList())) {
                        for (AfterSalesInvoiceVo afterSalesInvoiceVo : afterSalesVo.getAfterSalesInvoiceVoList()) {
                            if(!AfterSalesConstant.RETURN_INVOICE_STATUS.equals(afterSalesInvoiceVo.getStatus()) && !AfterSalesConstant.IS_REFUND_INVOICE_NO.equals(afterSalesInvoiceVo.getIsRefundInvoice())){
                                flag = false;
                                log.info("【按钮权限】确认完成，flag:{}",flag);
                                break;
                            }
                        }

                    }
                    if (flag) {
                        log.info("【按钮权限】确认完成，有权限");
                        mav.addObject("overFlag", 1);
                    }
                }
            } else if (AfterSalesConstant.HH_BUYORDER_AFTER_SALE_CODE.equals(afterSalesVo.getType())) {
                //进行中&不存在付款审核中
                //普发换，必须有出入库记录，且出入库数量=申请换货数量
                if (AfterSalesConstant.PROCRESSING_CODE.equals(afterSalesVo.getAtferSalesStatus()) && mav.getModel().get("isPayApply") == null) {
                    boolean flag = true;
                    if (CollectionUtils.isNotEmpty(afterSalesVo.getAfterSalesGoodsList())) {
                        for (AfterSalesGoodsVo afterSalesGoodsVo : afterSalesVo.getAfterSalesGoodsList()) {
                            if (AfterSalesConstant.UN_DELIVERY_DIRECT_CODE.equals(afterSalesGoodsVo.getDeliveryDirect())) {
                                Integer outNum = this.getBuyorderAfterSaleLogNumByType(afterSalesGoodsVo.getAfterSalesGoodsId(), 7);
                                Integer inNum = this.getBuyorderAfterSaleLogNumByType(afterSalesGoodsVo.getAfterSalesGoodsId(), 8);
                                if (!(outNum.equals(afterSalesGoodsVo.getNum()) && inNum.equals(afterSalesGoodsVo.getNum()))) {
                                    flag = false;
                                    log.info("【按钮权限】确认完成，flag:{}",flag);
                                }
                            }
                        }
                    }
                    if (flag) {
                        log.info("【按钮权限】确认完成，有权限");
                        mav.addObject("overFlag", 1);
                    }
                }
            }
        }

        // 创建者以及上级
        if (mav.getModel().get("isCreator") != null || isCreatorParent(afterSalesVo.getCreator(),currentUser)) {
            //关闭订单
            if ((AfterSalesConstant.CONFIRM_CODE.equals(afterSalesVo.getAtferSalesStatus()) && (AfterSalesConstant.VALID_FAIL_CODE.equals(afterSalesVo.getStatus()) || AfterSalesConstant.VALID_CONFIRM_CODE.equals(afterSalesVo.getStatus())))
                    || (AfterSalesConstant.PROCRESSING_CODE.equals(afterSalesVo.getAtferSalesStatus()) && AfterSalesConstant.CLOSEABLE_CODE.equals(afterSalesVo.getCloseStatus()))) {
                log.info("【按钮权限】关闭订单，有权限");
                mav.addObject("closeFlag", 1);
            }
            if (AfterSalesConstant.TH_BUYORDER_AFTER_SALE_CODE.equals(afterSalesVo.getType())) {
                //执行退款运算 订单状态：进行中，审核状态:不为审核中,退款状态为null
                if (AfterSalesConstant.PROCRESSING_CODE.equals(afterSalesVo.getAtferSalesStatus()) && afterSalesVo.getStatus() != 1 && !getMoneyChangeStatus(afterSalesVo)) {
                    log.info("【按钮权限】执行退款运算，有权限");
                    mav.addObject("excuteFlag", 1);
                    // 票 货
                    if (!getInvoiceChangeStatus(afterSalesVo) && !getGoodsChangesStatus(afterSalesVo)){
                        log.info("【按钮权限】编辑，有权限");
                        mav.addObject("editFlag2",1);
                        mav.addObject("editFlagSwitch","Y".equals(editFlagSwitch)?"1":"");
                    }
                }
            }
        }

        // 售后服务费编辑按钮权限
        this.setServiceEditButton(mav,afterSalesVo);

        if (mav.getModel().get("isVerifyUser") != null) {
            if (AfterSalesConstant.VALID_PROCRESSING_CODE.equals(afterSalesVo.getStatus()) || mav.getModel().get("overVerifyFlag") != null) {
                log.info("【按钮权限】审核按钮，有权限");
                mav.addObject("validFlag", 1);
            }
        }
    }

    @Value("${afterSale.editFlagSwitch:N}")
    private String editFlagSwitch;

    /**
     * 设置售后服务费编辑按钮权限
     * @param mav
     * @param afterSalesVo
     *
     */
    private void setServiceEditButton(ModelAndView mav,AfterSalesVo afterSalesVo){
        if(!AfterSalesConstant.TH_BUYORDER_AFTER_SALE_CODE.equals(afterSalesVo.getType())){
            log.info("【按钮权限】服务费编辑，非采购退货类型");
            mav.addObject("serviceEditButton", Boolean.TRUE);
            return;
        }
        SettlementBillQueryCmd cmd = new SettlementBillQueryCmd(afterSalesVo.getOrderId(), BusinessSourceTypeEnum.buyOrder);
        Integer buyOrderSettlementId= settlementBillApiService.getSettlementIdByBusiness(cmd);
        if (Objects.isNull(buyOrderSettlementId)){
            log.info("【按钮权限】服务费编辑，采购退货类型未查到返利结算单");
            mav.addObject("serviceEditButton", Boolean.TRUE);
            return;
        }

        log.info("【按钮权限】服务费编辑，采购退货类型查到返利结算单{}",buyOrderSettlementId);
        mav.addObject("serviceEditButton", Boolean.FALSE);
    }

    /**
     * 是否是上级
     * @param creator 单据创建人
     * @param currentUser 登录人
     * @return
     */
    private Boolean isCreatorParent(Integer creator,User currentUser){
        // 单据创建人信息
        UserDto creatorUser = userApiService.getUserById(creator);
        if (Objects.isNull(creatorUser)){
            return Boolean.FALSE;
        }
        if (creatorUser.getParentId().equals(currentUser.getUserId())) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 根据售后商品ID查找仓库商品操作日志
     *
     * @param afterSalesGoodsId 售后商品ID
     * @return 仓库商品操作日志列表
     */
    private List<WarehouseGoodsOperateLogDto> getBuyorderAfterSaleLog(Integer afterSalesGoodsId){
        WarehouseGoodsOperateLogDto findDto = new WarehouseGoodsOperateLogDto();
        findDto.setRelatedId(afterSalesGoodsId);
        findDto.setOperateType(6);
        findDto.setIsEnable(1);
        List<WarehouseGoodsOperateLogDto> warehouseGoodsOperateLogList = warehouseGoodsOperateLogApiService.findWarehouseByAll(findDto);
        return warehouseGoodsOperateLogList;
    }

    /**
     * 根据售后商品ID查找仓库商品操作日志
     *
     * @param afterSalesGoodsId 售后商品ID
     * @return 仓库商品操作日志列表
     */
    private List<AfterSaleBuyorderDirectOutLogDto> getBuyorderAfterSaleDirectLog(Integer afterSalesGoodsId){
        AfterSaleBuyorderDirectOutLogDto findDto = new AfterSaleBuyorderDirectOutLogDto();
        findDto.setAfterSalesGoodsId(afterSalesGoodsId);
        findDto.setIsDelete(0);
        List<AfterSaleBuyorderDirectOutLogDto> afterSaleBuyorderDirectOutLogDtoList = warehouseGoodsOperateLogApiService.findBuyOrderAfterSaleDirectByAll(findDto);
        return afterSaleBuyorderDirectOutLogDtoList;
    }

    /**
     * 根据售后商品ID和操作类型查找仓库商品操作日志数量
     *
     * @param afterSalesGoodsId 售后商品ID
     * @param type 操作类型
     * @return 仓库商品操作日志数量
     */
    private Integer getBuyorderAfterSaleLogNumByType(Integer afterSalesGoodsId,Integer type){
        WarehouseGoodsOperateLogDto findDto = new WarehouseGoodsOperateLogDto();
        findDto.setRelatedId(afterSalesGoodsId);
        findDto.setOperateType(type);
        findDto.setIsEnable(1);
        List<WarehouseGoodsOperateLogDto> warehouseGoodsOperateLogList = warehouseGoodsOperateLogApiService.findWarehouseByAll(findDto);
        if(CollUtil.isEmpty(warehouseGoodsOperateLogList)){
            return 0;
        }
        log.info("根据售后商品ID和操作类型查找仓库商品操作日志数量，入参：{}", JSONUtil.toJsonStr(warehouseGoodsOperateLogList));
        int sum = warehouseGoodsOperateLogList.stream().mapToInt(e -> Math.abs(Optional.ofNullable(e.getNum()).orElse(0))).sum();
        log.info("根据售后商品ID和操作类型查找仓库商品操作日志数量，结果：{}", sum);
        return sum;
    }

    /**
     * 票 货
     * @param afterSalesVo
     * @return
     */
    private boolean getInvoiceChangeStatus(AfterSalesVo afterSalesVo){
        List<AfterSalesInvoiceVo> afterSalesInvoiceVoList = afterSalesVo.getAfterSalesInvoiceVoList();
        if (CollectionUtils.isEmpty(afterSalesInvoiceVoList)){
            return Boolean.FALSE;
        }
        List<AfterSalesInvoiceVo> collect = afterSalesInvoiceVoList.stream().filter(e -> e.getIsRefundInvoice() == 1 && e.getStatus() == 1).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)){
            return Boolean.FALSE;
        }
        log.info("{}票有变动,{}",afterSalesVo.getAfterSalesNo(), JSON.toJSONString(collect));
        return Boolean.TRUE;
    }

    /**
     * 货物变动
     * @param afterSalesVo
     * @return
     */
    public boolean getGoodsChangesStatus(AfterSalesVo afterSalesVo){
        List<WarehouseGoodsOperateLog> afterReturnOutstockList = afterSalesVo.getAfterReturnOutstockList();
        List<AfterSaleBuyorderDirectOutLog> directReturnOutstockList = afterSalesVo.getDirectReturnOutstockList();
        if (CollectionUtils.isNotEmpty(afterReturnOutstockList) || CollectionUtils.isNotEmpty(directReturnOutstockList)){
            log.info("{}货有变动，{},{}",afterSalesVo.getAfterSalesNo(),JSON.toJSONString(afterReturnOutstockList),JSON.toJSONString(directReturnOutstockList));
            return Boolean.TRUE;
        }
        return Boolean.FALSE;

    }

    /**
     * 款是否有变动
     * @param afterSalesVo
     * @return
     */
    private boolean getMoneyChangeStatus(AfterSalesVo afterSalesVo){
        Integer refundAmountStatus = afterSalesVo.getRefundAmountStatus();
        if (Objects.isNull(refundAmountStatus)){
            return Boolean.FALSE;
        }
        log.info("{}款有变动",afterSalesVo.getAfterSalesNo());
        return Boolean.TRUE;
    }
}
