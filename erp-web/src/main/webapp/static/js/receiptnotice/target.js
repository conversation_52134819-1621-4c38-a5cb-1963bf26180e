void function () {
    new Vue({
        el: '#page-container',
        data: {
            yearList: [],
            currentYear: '',
            tabActiveIndex: '1',
            personList: [],
            groupList: [],
            departmentList: [],
            loadingEl: null
        },
        async created() {
            this.showloading();
            await this.getYearList();
        },
        methods: {
            getYearList() {
                return axios.get('/broadcast/target/years.do').then(({ data }) => {
                    if (data.code === 0) {
                        let years = data.data || [];

                        if(years && years.length) {
                            let yearList = [];

                            years.forEach(item => {
                                yearList.push({
                                    label: item,
                                    value: item
                                })
                            })

                            this.yearList = yearList;

                            this.currentYear = years.sort().reverse()[0];

                            this.getTargetList();
                        } else {
                            this.hideloading();
                        }
                    }
                })
            },
            getTargetList() {
                this.showloading();

                axios.post('/broadcast/target/query.do', {
                    targetYear: this.currentYear,
                    value: 'list'
                }).then(({ data }) => {
                    this.hideloading();
                    if (data.code === 0) {
                        this.personList = data.data.person || [];
                        this.groupList = data.data.group || [];
                        this.departmentList = data.data.department || [];
                    }
                })
            },
            downloadTemplate() {
                window.open('/broadcast/target/template/download.do');
            },
            handlerImportProgress() {
                this.showloading();
            },
            handlerImportSuccess(data) {
                console.log(data)
                this.hideloading();

                if (data.code === 0) {
                    this.$message({
                        message: '导入成功，页面即将刷新',
                        type: 'success'
                    });

                    setTimeout(() => {
                        window.location.reload();
                    }, 1000)
                }
            },
            handlerImportError() {
                this.hideloading();
            },
            showloading() {
                this.loadingEl = this.$loading({
                    lock: true,
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
            },
            hideloading() {
                this.loadingEl && this.loadingEl.close();
            }
        }
    })
}.call(this);