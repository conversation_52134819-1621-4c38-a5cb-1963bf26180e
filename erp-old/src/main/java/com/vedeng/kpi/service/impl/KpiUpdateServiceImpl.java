package com.vedeng.kpi.service.impl;

import com.vedeng.common.exception.ShowErrorMsgException;
import com.vedeng.kpi.cache.KpiLoadingCache;
import com.vedeng.kpi.dao.KpiOrderLogMapper;
import com.vedeng.kpi.model.DTO.KpiDataQueryDto;
import com.vedeng.kpi.model.DTO.KpiUserInfoDto;
import com.vedeng.kpi.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 五行聚合更新service.
 * @jira: VDERP-2376【五行剑法】规则修改.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/7/9 2:18 下午.
 * @author: Tomcat.Hui.
 */
@Service
public class KpiUpdateServiceImpl implements KpiUpdateService {

    private static final Logger log = LoggerFactory.getLogger("kpilog");

    @Autowired
    KpiLoadingCache kpiLoadingCache;

    @Qualifier("kpiAmountServiceImpl")
    @Autowired
    KpiBaseService kpiAmountService;

    @Qualifier("kpiChanceServiceImpl")
    @Autowired
    KpiBaseService kpiChanceService;

    @Qualifier("kpiCustomerServiceImpl")
    @Autowired
    KpiBaseService kpiCustomerService;

    @Qualifier("kpiBdCustomerServiceImpl")
    @Autowired
    KpiBaseService kpiBdCustomerService;

    @Resource
    KpiOrderLogMapper kpiOrderLogMapper;

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void updateKpiSingle(KpiUserInfoDto userInfo) throws Exception {

        log.info("用户" + userInfo.getUserIdName() + " 开始指定更新kpi数据 起始时间:" + userInfo.getKpiDateStart() +
                " 结束时间 " + userInfo.getKpiDateEnd());
        try {

            kpiAmountService.dataHandler(userInfo);
            kpiCustomerService.dataHandler(userInfo);
            kpiBdCustomerService.dataHandler(userInfo);
            kpiChanceService.dataHandler(userInfo);

            log.info("用户" + userInfo.getUserIdName() + "指定更新kpi数据 起始时间:" + userInfo.getKpiDateStart() +
                    " 结束时间 " + userInfo.getKpiDateEnd() + " 完成");
        }catch (ShowErrorMsgException r){
            log.error("",r);
        }
        catch (Exception e){
            log.error("用户" + userInfo.getUserIdName() + " 更新 " + userInfo.getKpiDateStart() + " 日数据出现异常: ",e);
            throw e;
        }
    }
}
