package com.newtask.quoteorder;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import org.springframework.context.SmartLifecycle;

/**
 * <AUTHOR> [<EMAIL>]
 */
abstract class LifecycleSupport extends Abstract<PERSON>obHandler implements SmartLifecycle {

    private volatile boolean closed = false;

    @Override
    public boolean isAutoStartup() {
        return true;
    }

    @Override
    public void stop(Runnable callback) {
    }

    @Override
    public void start() {
        if (!isClose()) {
            onStart();
        }
        closed = false;
    }

    @Override
    public void stop() {
        if (!isClose()) {
            onStop();
        }
        closed = true;
    }

    protected boolean isClose() {
        return closed;
    }

    @Override
    public boolean isRunning() {
        return isClose();
    }

    @Override
    public int getPhase() {
        return 0;
    }

    protected abstract void onStart();

    protected abstract void onStop();

}
