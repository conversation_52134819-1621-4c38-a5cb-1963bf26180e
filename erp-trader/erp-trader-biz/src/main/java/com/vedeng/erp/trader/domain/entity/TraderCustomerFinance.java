package com.vedeng.erp.trader.domain.entity;

import java.io.Serializable;
import java.util.Date;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * 客户列表（财务专用）
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class TraderCustomerFinance extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    private Integer traderCustomerFinanceId;

    /**
     * 交易者ID
     */
    private Integer traderId;

    /**
     * (新)客户名称，不推送金蝶
     */
    private String customerNameNew;

    /**
     * 客户性质：1直接客户 2间接客户
     */
    private Integer customerNature;

    /**
     * 终端客户分类：1公立 2民营个体 3民营集团
     */
    private Integer customerClass;

    /**
     * 集团名称
     */
    private String groupName;

    /**
     * 客户细分类：1医疗卫生机构 2非医疗卫生机构 3分销商
     */
    private Integer customerSecondType;

    /**
     * 医疗机构分类：1医院 2基层医疗卫生机构 3专业医疗卫生机构 4其他医疗卫生机构
     */
    private Integer customerThirdType;

    /**
     * 医院等级：1一级、2二级、3三级、4未分级
     */
    private Integer hospitalLever;

    /**
     * 所属医院共体
     */
    private String hospitalName;

    /**
     * 是否推送金蝶 0否 1是
     */
    private Integer isPush;

    /**
     * 推送时间
     */
    private Date pushTime;

    /**
     * 是否删除 0否 1是
     */
    private Integer isDelete;

    /**
     * 备注
     */
    private String remark;

    /**
     * 更新备注
     */
    private String updateRemark;

    private static final long serialVersionUID = 1L;
}