<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="编辑售后服务公司" scope="application" />
<%@ include file="../../common/common.jsp"%>
<div class="content mt10 ">
	<div class="parts">
		<div class="title-container title-container-blue">
			<div class="table-title nobor">编辑售后服务公司</div>
		</div>
		<div class="formpublic formpublic1">
	<input id="companyName" type="hidden" value="${companyName }">
	<input id="oldCompanyName" type="hidden" value="${engineer.company }">
	<form method="post" id="myform" action="${pageContext.request.contextPath}/aftersales/engineer/saveedit.do">
		<ul>
			<li>
				<div class="form-tips">
					<span>*</span>
					<lable>公司名称</lable>
				</div>
				<div class="f_left">
					<div class='inputfloat'>
						<c:if test="${engineer.traderSupplierId ne 0}">
						<span>${engineer.name}</span>
						</c:if>
						<c:if test="${engineer.traderSupplierId eq 0}">
							<span class="none" id="name"></span>
						<label class="bt-bg-style bg-light-blue bt-small none" onclick="research();"
							   id="research">重新搜索</label>
						<input type="text" placeholder="请输入供应商名称" class="input-largest" name="searchTraderName"
							   id="searchTraderName" value="${engineer.name}">
						<label class="bt-bg-style bg-light-blue bt-small" onclick="searchSupplier();"
							   id="errorMes">搜索</label>
						<span style="display:none;">
									<!-- 弹框 -->
									<div class="title-click nobor  pop-new-data" id="popSupplier"></div>
								</span>
						</c:if>
						<input type="hidden" id="traderSupplierId" name="traderSupplierId" value="${engineer.traderSupplierId}"/>
						<input type="hidden" id="traderId" name="traderId"/>
						<input type="hidden" id="traderName" name="traderName"  value="${engineer.name}"/>
						<input type="hidden" name="name" value="${engineer.name}" />
						<input type="hidden" id="owner" name="owner" value="2" />
					</div>
					<div class="font-red none"></div>
				</div>

			</li>
			<li>
				<div class="form-tips">
					<lable>开户银行</lable>
				</div>
				<div class="f_left">
					<div class="form-blanks">
						<input type="text" class=" input-large" name="bank" id="bank" value="${engineer.bank}"/>
					</div>
				</div>
			</li>
			<li>
				<div class="form-tips">
					<lable>开户账号</lable>
				</div>
				<div class="f_left ">
					<div class="form-blanks">
						<input type="text" onkeyup="this.value=this.value.replace(/[^\d]/g,'') " onafterpaste="this.value=this.value.replace(/[^\d]/g,'')"
							   placeholder="账号只允许输入阿拉伯数字" class=" input-large" name="bankAccount" id="bankAccount" value="${engineer.bankAccount }"/>
					</div>
				</div>
			</li>
			<li>
				<div class="form-tips">
					<lable>开户支行联行号</lable>
				</div>
				<div class="f_left ">
					<div class="form-blanks">
						<input type="text" onkeyup="this.value=this.value.replace(/[^\d]/g,'') " onafterpaste="this.value=this.value.replace(/[^\d]/g,'')"
							   placeholder="账号只允许输入阿拉伯数字" class=" input-large" name="bankCode" id="bankCode" value="${engineer.bankCode }"/>
					</div>
				</div>
			</li>
		</ul>
		<div class="add-tijiao tcenter">
			<input type="hidden" name="engineerId" value="${engineer.engineerId }">
			<input type="hidden" name="beforeParams" value='${beforeParams}'/>
			<button type="submit" id="submit_edit">提交</button>
		</div>
	</form>
</div>
	</div>
</div>
<script type="text/javascript"
	src="${pageContext.request.contextPath}/static/js/aftersales/engineer/edit_engineer.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
		src="${pageContext.request.contextPath}/static/js/aftersales/engineer/search_company.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
	src="${pageContext.request.contextPath}/static/js/region/index.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp"%>