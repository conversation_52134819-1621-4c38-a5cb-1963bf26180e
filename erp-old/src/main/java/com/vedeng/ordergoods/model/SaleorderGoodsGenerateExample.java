package com.vedeng.ordergoods.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SaleorderGoodsGenerateExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_SALEORDER_GOODS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_SALEORDER_GOODS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_SALEORDER_GOODS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_GOODS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public SaleorderGoodsGenerateExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_GOODS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_GOODS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_GOODS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_GOODS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_GOODS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_GOODS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_GOODS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_GOODS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_GOODS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_GOODS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_SALEORDER_GOODS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andSaleorderGoodsIdIsNull() {
            addCriterion("SALEORDER_GOODS_ID is null");
            return (Criteria) this;
        }

        public Criteria andSaleorderGoodsIdIsNotNull() {
            addCriterion("SALEORDER_GOODS_ID is not null");
            return (Criteria) this;
        }

        public Criteria andSaleorderGoodsIdEqualTo(Integer value) {
            addCriterion("SALEORDER_GOODS_ID =", value, "saleorderGoodsId");
            return (Criteria) this;
        }

        public Criteria andSaleorderGoodsIdNotEqualTo(Integer value) {
            addCriterion("SALEORDER_GOODS_ID <>", value, "saleorderGoodsId");
            return (Criteria) this;
        }

        public Criteria andSaleorderGoodsIdGreaterThan(Integer value) {
            addCriterion("SALEORDER_GOODS_ID >", value, "saleorderGoodsId");
            return (Criteria) this;
        }

        public Criteria andSaleorderGoodsIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("SALEORDER_GOODS_ID >=", value, "saleorderGoodsId");
            return (Criteria) this;
        }

        public Criteria andSaleorderGoodsIdLessThan(Integer value) {
            addCriterion("SALEORDER_GOODS_ID <", value, "saleorderGoodsId");
            return (Criteria) this;
        }

        public Criteria andSaleorderGoodsIdLessThanOrEqualTo(Integer value) {
            addCriterion("SALEORDER_GOODS_ID <=", value, "saleorderGoodsId");
            return (Criteria) this;
        }

        public Criteria andSaleorderGoodsIdIn(List<Integer> values) {
            addCriterion("SALEORDER_GOODS_ID in", values, "saleorderGoodsId");
            return (Criteria) this;
        }

        public Criteria andSaleorderGoodsIdNotIn(List<Integer> values) {
            addCriterion("SALEORDER_GOODS_ID not in", values, "saleorderGoodsId");
            return (Criteria) this;
        }

        public Criteria andSaleorderGoodsIdBetween(Integer value1, Integer value2) {
            addCriterion("SALEORDER_GOODS_ID between", value1, value2, "saleorderGoodsId");
            return (Criteria) this;
        }

        public Criteria andSaleorderGoodsIdNotBetween(Integer value1, Integer value2) {
            addCriterion("SALEORDER_GOODS_ID not between", value1, value2, "saleorderGoodsId");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdIsNull() {
            addCriterion("SALEORDER_ID is null");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdIsNotNull() {
            addCriterion("SALEORDER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdEqualTo(Integer value) {
            addCriterion("SALEORDER_ID =", value, "saleorderId");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdNotEqualTo(Integer value) {
            addCriterion("SALEORDER_ID <>", value, "saleorderId");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdGreaterThan(Integer value) {
            addCriterion("SALEORDER_ID >", value, "saleorderId");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("SALEORDER_ID >=", value, "saleorderId");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdLessThan(Integer value) {
            addCriterion("SALEORDER_ID <", value, "saleorderId");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdLessThanOrEqualTo(Integer value) {
            addCriterion("SALEORDER_ID <=", value, "saleorderId");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdIn(List<Integer> values) {
            addCriterion("SALEORDER_ID in", values, "saleorderId");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdNotIn(List<Integer> values) {
            addCriterion("SALEORDER_ID not in", values, "saleorderId");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdBetween(Integer value1, Integer value2) {
            addCriterion("SALEORDER_ID between", value1, value2, "saleorderId");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdNotBetween(Integer value1, Integer value2) {
            addCriterion("SALEORDER_ID not between", value1, value2, "saleorderId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdIsNull() {
            addCriterion("GOODS_ID is null");
            return (Criteria) this;
        }

        public Criteria andGoodsIdIsNotNull() {
            addCriterion("GOODS_ID is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsIdEqualTo(Integer value) {
            addCriterion("GOODS_ID =", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdNotEqualTo(Integer value) {
            addCriterion("GOODS_ID <>", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdGreaterThan(Integer value) {
            addCriterion("GOODS_ID >", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("GOODS_ID >=", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdLessThan(Integer value) {
            addCriterion("GOODS_ID <", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdLessThanOrEqualTo(Integer value) {
            addCriterion("GOODS_ID <=", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdIn(List<Integer> values) {
            addCriterion("GOODS_ID in", values, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdNotIn(List<Integer> values) {
            addCriterion("GOODS_ID not in", values, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdBetween(Integer value1, Integer value2) {
            addCriterion("GOODS_ID between", value1, value2, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdNotBetween(Integer value1, Integer value2) {
            addCriterion("GOODS_ID not between", value1, value2, "goodsId");
            return (Criteria) this;
        }

        public Criteria andSkuIsNull() {
            addCriterion("SKU is null");
            return (Criteria) this;
        }

        public Criteria andSkuIsNotNull() {
            addCriterion("SKU is not null");
            return (Criteria) this;
        }

        public Criteria andSkuEqualTo(String value) {
            addCriterion("SKU =", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotEqualTo(String value) {
            addCriterion("SKU <>", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuGreaterThan(String value) {
            addCriterion("SKU >", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuGreaterThanOrEqualTo(String value) {
            addCriterion("SKU >=", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuLessThan(String value) {
            addCriterion("SKU <", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuLessThanOrEqualTo(String value) {
            addCriterion("SKU <=", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuLike(String value) {
            addCriterion("SKU like", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotLike(String value) {
            addCriterion("SKU not like", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuIn(List<String> values) {
            addCriterion("SKU in", values, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotIn(List<String> values) {
            addCriterion("SKU not in", values, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuBetween(String value1, String value2) {
            addCriterion("SKU between", value1, value2, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotBetween(String value1, String value2) {
            addCriterion("SKU not between", value1, value2, "sku");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIsNull() {
            addCriterion("GOODS_NAME is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIsNotNull() {
            addCriterion("GOODS_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNameEqualTo(String value) {
            addCriterion("GOODS_NAME =", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotEqualTo(String value) {
            addCriterion("GOODS_NAME <>", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameGreaterThan(String value) {
            addCriterion("GOODS_NAME >", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameGreaterThanOrEqualTo(String value) {
            addCriterion("GOODS_NAME >=", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLessThan(String value) {
            addCriterion("GOODS_NAME <", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLessThanOrEqualTo(String value) {
            addCriterion("GOODS_NAME <=", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLike(String value) {
            addCriterion("GOODS_NAME like", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotLike(String value) {
            addCriterion("GOODS_NAME not like", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIn(List<String> values) {
            addCriterion("GOODS_NAME in", values, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotIn(List<String> values) {
            addCriterion("GOODS_NAME not in", values, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameBetween(String value1, String value2) {
            addCriterion("GOODS_NAME between", value1, value2, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotBetween(String value1, String value2) {
            addCriterion("GOODS_NAME not between", value1, value2, "goodsName");
            return (Criteria) this;
        }

        public Criteria andBrandNameIsNull() {
            addCriterion("BRAND_NAME is null");
            return (Criteria) this;
        }

        public Criteria andBrandNameIsNotNull() {
            addCriterion("BRAND_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andBrandNameEqualTo(String value) {
            addCriterion("BRAND_NAME =", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotEqualTo(String value) {
            addCriterion("BRAND_NAME <>", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameGreaterThan(String value) {
            addCriterion("BRAND_NAME >", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameGreaterThanOrEqualTo(String value) {
            addCriterion("BRAND_NAME >=", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameLessThan(String value) {
            addCriterion("BRAND_NAME <", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameLessThanOrEqualTo(String value) {
            addCriterion("BRAND_NAME <=", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameLike(String value) {
            addCriterion("BRAND_NAME like", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotLike(String value) {
            addCriterion("BRAND_NAME not like", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameIn(List<String> values) {
            addCriterion("BRAND_NAME in", values, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotIn(List<String> values) {
            addCriterion("BRAND_NAME not in", values, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameBetween(String value1, String value2) {
            addCriterion("BRAND_NAME between", value1, value2, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotBetween(String value1, String value2) {
            addCriterion("BRAND_NAME not between", value1, value2, "brandName");
            return (Criteria) this;
        }

        public Criteria andModelIsNull() {
            addCriterion("MODEL is null");
            return (Criteria) this;
        }

        public Criteria andModelIsNotNull() {
            addCriterion("MODEL is not null");
            return (Criteria) this;
        }

        public Criteria andModelEqualTo(String value) {
            addCriterion("MODEL =", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotEqualTo(String value) {
            addCriterion("MODEL <>", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThan(String value) {
            addCriterion("MODEL >", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualTo(String value) {
            addCriterion("MODEL >=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThan(String value) {
            addCriterion("MODEL <", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualTo(String value) {
            addCriterion("MODEL <=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLike(String value) {
            addCriterion("MODEL like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotLike(String value) {
            addCriterion("MODEL not like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelIn(List<String> values) {
            addCriterion("MODEL in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotIn(List<String> values) {
            addCriterion("MODEL not in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelBetween(String value1, String value2) {
            addCriterion("MODEL between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotBetween(String value1, String value2) {
            addCriterion("MODEL not between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNull() {
            addCriterion("UNIT_NAME is null");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNotNull() {
            addCriterion("UNIT_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andUnitNameEqualTo(String value) {
            addCriterion("UNIT_NAME =", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotEqualTo(String value) {
            addCriterion("UNIT_NAME <>", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThan(String value) {
            addCriterion("UNIT_NAME >", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThanOrEqualTo(String value) {
            addCriterion("UNIT_NAME >=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThan(String value) {
            addCriterion("UNIT_NAME <", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThanOrEqualTo(String value) {
            addCriterion("UNIT_NAME <=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLike(String value) {
            addCriterion("UNIT_NAME like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotLike(String value) {
            addCriterion("UNIT_NAME not like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameIn(List<String> values) {
            addCriterion("UNIT_NAME in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotIn(List<String> values) {
            addCriterion("UNIT_NAME not in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameBetween(String value1, String value2) {
            addCriterion("UNIT_NAME between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotBetween(String value1, String value2) {
            addCriterion("UNIT_NAME not between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andPriceIsNull() {
            addCriterion("PRICE is null");
            return (Criteria) this;
        }

        public Criteria andPriceIsNotNull() {
            addCriterion("PRICE is not null");
            return (Criteria) this;
        }

        public Criteria andPriceEqualTo(BigDecimal value) {
            addCriterion("PRICE =", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotEqualTo(BigDecimal value) {
            addCriterion("PRICE <>", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThan(BigDecimal value) {
            addCriterion("PRICE >", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("PRICE >=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThan(BigDecimal value) {
            addCriterion("PRICE <", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("PRICE <=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceIn(List<BigDecimal> values) {
            addCriterion("PRICE in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotIn(List<BigDecimal> values) {
            addCriterion("PRICE not in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("PRICE between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("PRICE not between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andRealPriceIsNull() {
            addCriterion("REAL_PRICE is null");
            return (Criteria) this;
        }

        public Criteria andRealPriceIsNotNull() {
            addCriterion("REAL_PRICE is not null");
            return (Criteria) this;
        }

        public Criteria andRealPriceEqualTo(BigDecimal value) {
            addCriterion("REAL_PRICE =", value, "realPrice");
            return (Criteria) this;
        }

        public Criteria andRealPriceNotEqualTo(BigDecimal value) {
            addCriterion("REAL_PRICE <>", value, "realPrice");
            return (Criteria) this;
        }

        public Criteria andRealPriceGreaterThan(BigDecimal value) {
            addCriterion("REAL_PRICE >", value, "realPrice");
            return (Criteria) this;
        }

        public Criteria andRealPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("REAL_PRICE >=", value, "realPrice");
            return (Criteria) this;
        }

        public Criteria andRealPriceLessThan(BigDecimal value) {
            addCriterion("REAL_PRICE <", value, "realPrice");
            return (Criteria) this;
        }

        public Criteria andRealPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("REAL_PRICE <=", value, "realPrice");
            return (Criteria) this;
        }

        public Criteria andRealPriceIn(List<BigDecimal> values) {
            addCriterion("REAL_PRICE in", values, "realPrice");
            return (Criteria) this;
        }

        public Criteria andRealPriceNotIn(List<BigDecimal> values) {
            addCriterion("REAL_PRICE not in", values, "realPrice");
            return (Criteria) this;
        }

        public Criteria andRealPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("REAL_PRICE between", value1, value2, "realPrice");
            return (Criteria) this;
        }

        public Criteria andRealPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("REAL_PRICE not between", value1, value2, "realPrice");
            return (Criteria) this;
        }

        public Criteria andPurchasingPriceIsNull() {
            addCriterion("PURCHASING_PRICE is null");
            return (Criteria) this;
        }

        public Criteria andPurchasingPriceIsNotNull() {
            addCriterion("PURCHASING_PRICE is not null");
            return (Criteria) this;
        }

        public Criteria andPurchasingPriceEqualTo(BigDecimal value) {
            addCriterion("PURCHASING_PRICE =", value, "purchasingPrice");
            return (Criteria) this;
        }

        public Criteria andPurchasingPriceNotEqualTo(BigDecimal value) {
            addCriterion("PURCHASING_PRICE <>", value, "purchasingPrice");
            return (Criteria) this;
        }

        public Criteria andPurchasingPriceGreaterThan(BigDecimal value) {
            addCriterion("PURCHASING_PRICE >", value, "purchasingPrice");
            return (Criteria) this;
        }

        public Criteria andPurchasingPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("PURCHASING_PRICE >=", value, "purchasingPrice");
            return (Criteria) this;
        }

        public Criteria andPurchasingPriceLessThan(BigDecimal value) {
            addCriterion("PURCHASING_PRICE <", value, "purchasingPrice");
            return (Criteria) this;
        }

        public Criteria andPurchasingPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("PURCHASING_PRICE <=", value, "purchasingPrice");
            return (Criteria) this;
        }

        public Criteria andPurchasingPriceIn(List<BigDecimal> values) {
            addCriterion("PURCHASING_PRICE in", values, "purchasingPrice");
            return (Criteria) this;
        }

        public Criteria andPurchasingPriceNotIn(List<BigDecimal> values) {
            addCriterion("PURCHASING_PRICE not in", values, "purchasingPrice");
            return (Criteria) this;
        }

        public Criteria andPurchasingPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("PURCHASING_PRICE between", value1, value2, "purchasingPrice");
            return (Criteria) this;
        }

        public Criteria andPurchasingPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("PURCHASING_PRICE not between", value1, value2, "purchasingPrice");
            return (Criteria) this;
        }

        public Criteria andCurrencyUnitIdIsNull() {
            addCriterion("CURRENCY_UNIT_ID is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyUnitIdIsNotNull() {
            addCriterion("CURRENCY_UNIT_ID is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyUnitIdEqualTo(Integer value) {
            addCriterion("CURRENCY_UNIT_ID =", value, "currencyUnitId");
            return (Criteria) this;
        }

        public Criteria andCurrencyUnitIdNotEqualTo(Integer value) {
            addCriterion("CURRENCY_UNIT_ID <>", value, "currencyUnitId");
            return (Criteria) this;
        }

        public Criteria andCurrencyUnitIdGreaterThan(Integer value) {
            addCriterion("CURRENCY_UNIT_ID >", value, "currencyUnitId");
            return (Criteria) this;
        }

        public Criteria andCurrencyUnitIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("CURRENCY_UNIT_ID >=", value, "currencyUnitId");
            return (Criteria) this;
        }

        public Criteria andCurrencyUnitIdLessThan(Integer value) {
            addCriterion("CURRENCY_UNIT_ID <", value, "currencyUnitId");
            return (Criteria) this;
        }

        public Criteria andCurrencyUnitIdLessThanOrEqualTo(Integer value) {
            addCriterion("CURRENCY_UNIT_ID <=", value, "currencyUnitId");
            return (Criteria) this;
        }

        public Criteria andCurrencyUnitIdIn(List<Integer> values) {
            addCriterion("CURRENCY_UNIT_ID in", values, "currencyUnitId");
            return (Criteria) this;
        }

        public Criteria andCurrencyUnitIdNotIn(List<Integer> values) {
            addCriterion("CURRENCY_UNIT_ID not in", values, "currencyUnitId");
            return (Criteria) this;
        }

        public Criteria andCurrencyUnitIdBetween(Integer value1, Integer value2) {
            addCriterion("CURRENCY_UNIT_ID between", value1, value2, "currencyUnitId");
            return (Criteria) this;
        }

        public Criteria andCurrencyUnitIdNotBetween(Integer value1, Integer value2) {
            addCriterion("CURRENCY_UNIT_ID not between", value1, value2, "currencyUnitId");
            return (Criteria) this;
        }

        public Criteria andNumIsNull() {
            addCriterion("NUM is null");
            return (Criteria) this;
        }

        public Criteria andNumIsNotNull() {
            addCriterion("NUM is not null");
            return (Criteria) this;
        }

        public Criteria andNumEqualTo(Integer value) {
            addCriterion("NUM =", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotEqualTo(Integer value) {
            addCriterion("NUM <>", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumGreaterThan(Integer value) {
            addCriterion("NUM >", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("NUM >=", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumLessThan(Integer value) {
            addCriterion("NUM <", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumLessThanOrEqualTo(Integer value) {
            addCriterion("NUM <=", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumIn(List<Integer> values) {
            addCriterion("NUM in", values, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotIn(List<Integer> values) {
            addCriterion("NUM not in", values, "num");
            return (Criteria) this;
        }

        public Criteria andNumBetween(Integer value1, Integer value2) {
            addCriterion("NUM between", value1, value2, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotBetween(Integer value1, Integer value2) {
            addCriterion("NUM not between", value1, value2, "num");
            return (Criteria) this;
        }

        public Criteria andBuyNumIsNull() {
            addCriterion("BUY_NUM is null");
            return (Criteria) this;
        }

        public Criteria andBuyNumIsNotNull() {
            addCriterion("BUY_NUM is not null");
            return (Criteria) this;
        }

        public Criteria andBuyNumEqualTo(Integer value) {
            addCriterion("BUY_NUM =", value, "buyNum");
            return (Criteria) this;
        }

        public Criteria andBuyNumNotEqualTo(Integer value) {
            addCriterion("BUY_NUM <>", value, "buyNum");
            return (Criteria) this;
        }

        public Criteria andBuyNumGreaterThan(Integer value) {
            addCriterion("BUY_NUM >", value, "buyNum");
            return (Criteria) this;
        }

        public Criteria andBuyNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("BUY_NUM >=", value, "buyNum");
            return (Criteria) this;
        }

        public Criteria andBuyNumLessThan(Integer value) {
            addCriterion("BUY_NUM <", value, "buyNum");
            return (Criteria) this;
        }

        public Criteria andBuyNumLessThanOrEqualTo(Integer value) {
            addCriterion("BUY_NUM <=", value, "buyNum");
            return (Criteria) this;
        }

        public Criteria andBuyNumIn(List<Integer> values) {
            addCriterion("BUY_NUM in", values, "buyNum");
            return (Criteria) this;
        }

        public Criteria andBuyNumNotIn(List<Integer> values) {
            addCriterion("BUY_NUM not in", values, "buyNum");
            return (Criteria) this;
        }

        public Criteria andBuyNumBetween(Integer value1, Integer value2) {
            addCriterion("BUY_NUM between", value1, value2, "buyNum");
            return (Criteria) this;
        }

        public Criteria andBuyNumNotBetween(Integer value1, Integer value2) {
            addCriterion("BUY_NUM not between", value1, value2, "buyNum");
            return (Criteria) this;
        }

        public Criteria andDeliveryNumIsNull() {
            addCriterion("DELIVERY_NUM is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryNumIsNotNull() {
            addCriterion("DELIVERY_NUM is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryNumEqualTo(Integer value) {
            addCriterion("DELIVERY_NUM =", value, "deliveryNum");
            return (Criteria) this;
        }

        public Criteria andDeliveryNumNotEqualTo(Integer value) {
            addCriterion("DELIVERY_NUM <>", value, "deliveryNum");
            return (Criteria) this;
        }

        public Criteria andDeliveryNumGreaterThan(Integer value) {
            addCriterion("DELIVERY_NUM >", value, "deliveryNum");
            return (Criteria) this;
        }

        public Criteria andDeliveryNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("DELIVERY_NUM >=", value, "deliveryNum");
            return (Criteria) this;
        }

        public Criteria andDeliveryNumLessThan(Integer value) {
            addCriterion("DELIVERY_NUM <", value, "deliveryNum");
            return (Criteria) this;
        }

        public Criteria andDeliveryNumLessThanOrEqualTo(Integer value) {
            addCriterion("DELIVERY_NUM <=", value, "deliveryNum");
            return (Criteria) this;
        }

        public Criteria andDeliveryNumIn(List<Integer> values) {
            addCriterion("DELIVERY_NUM in", values, "deliveryNum");
            return (Criteria) this;
        }

        public Criteria andDeliveryNumNotIn(List<Integer> values) {
            addCriterion("DELIVERY_NUM not in", values, "deliveryNum");
            return (Criteria) this;
        }

        public Criteria andDeliveryNumBetween(Integer value1, Integer value2) {
            addCriterion("DELIVERY_NUM between", value1, value2, "deliveryNum");
            return (Criteria) this;
        }

        public Criteria andDeliveryNumNotBetween(Integer value1, Integer value2) {
            addCriterion("DELIVERY_NUM not between", value1, value2, "deliveryNum");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusIsNull() {
            addCriterion("DELIVERY_STATUS is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusIsNotNull() {
            addCriterion("DELIVERY_STATUS is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusEqualTo(Byte value) {
            addCriterion("DELIVERY_STATUS =", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusNotEqualTo(Byte value) {
            addCriterion("DELIVERY_STATUS <>", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusGreaterThan(Byte value) {
            addCriterion("DELIVERY_STATUS >", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("DELIVERY_STATUS >=", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusLessThan(Byte value) {
            addCriterion("DELIVERY_STATUS <", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusLessThanOrEqualTo(Byte value) {
            addCriterion("DELIVERY_STATUS <=", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusIn(List<Byte> values) {
            addCriterion("DELIVERY_STATUS in", values, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusNotIn(List<Byte> values) {
            addCriterion("DELIVERY_STATUS not in", values, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusBetween(Byte value1, Byte value2) {
            addCriterion("DELIVERY_STATUS between", value1, value2, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("DELIVERY_STATUS not between", value1, value2, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeIsNull() {
            addCriterion("DELIVERY_TIME is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeIsNotNull() {
            addCriterion("DELIVERY_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeEqualTo(Long value) {
            addCriterion("DELIVERY_TIME =", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeNotEqualTo(Long value) {
            addCriterion("DELIVERY_TIME <>", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeGreaterThan(Long value) {
            addCriterion("DELIVERY_TIME >", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("DELIVERY_TIME >=", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeLessThan(Long value) {
            addCriterion("DELIVERY_TIME <", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeLessThanOrEqualTo(Long value) {
            addCriterion("DELIVERY_TIME <=", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeIn(List<Long> values) {
            addCriterion("DELIVERY_TIME in", values, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeNotIn(List<Long> values) {
            addCriterion("DELIVERY_TIME not in", values, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeBetween(Long value1, Long value2) {
            addCriterion("DELIVERY_TIME between", value1, value2, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeNotBetween(Long value1, Long value2) {
            addCriterion("DELIVERY_TIME not between", value1, value2, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryCycleIsNull() {
            addCriterion("DELIVERY_CYCLE is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryCycleIsNotNull() {
            addCriterion("DELIVERY_CYCLE is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryCycleEqualTo(String value) {
            addCriterion("DELIVERY_CYCLE =", value, "deliveryCycle");
            return (Criteria) this;
        }

        public Criteria andDeliveryCycleNotEqualTo(String value) {
            addCriterion("DELIVERY_CYCLE <>", value, "deliveryCycle");
            return (Criteria) this;
        }

        public Criteria andDeliveryCycleGreaterThan(String value) {
            addCriterion("DELIVERY_CYCLE >", value, "deliveryCycle");
            return (Criteria) this;
        }

        public Criteria andDeliveryCycleGreaterThanOrEqualTo(String value) {
            addCriterion("DELIVERY_CYCLE >=", value, "deliveryCycle");
            return (Criteria) this;
        }

        public Criteria andDeliveryCycleLessThan(String value) {
            addCriterion("DELIVERY_CYCLE <", value, "deliveryCycle");
            return (Criteria) this;
        }

        public Criteria andDeliveryCycleLessThanOrEqualTo(String value) {
            addCriterion("DELIVERY_CYCLE <=", value, "deliveryCycle");
            return (Criteria) this;
        }

        public Criteria andDeliveryCycleLike(String value) {
            addCriterion("DELIVERY_CYCLE like", value, "deliveryCycle");
            return (Criteria) this;
        }

        public Criteria andDeliveryCycleNotLike(String value) {
            addCriterion("DELIVERY_CYCLE not like", value, "deliveryCycle");
            return (Criteria) this;
        }

        public Criteria andDeliveryCycleIn(List<String> values) {
            addCriterion("DELIVERY_CYCLE in", values, "deliveryCycle");
            return (Criteria) this;
        }

        public Criteria andDeliveryCycleNotIn(List<String> values) {
            addCriterion("DELIVERY_CYCLE not in", values, "deliveryCycle");
            return (Criteria) this;
        }

        public Criteria andDeliveryCycleBetween(String value1, String value2) {
            addCriterion("DELIVERY_CYCLE between", value1, value2, "deliveryCycle");
            return (Criteria) this;
        }

        public Criteria andDeliveryCycleNotBetween(String value1, String value2) {
            addCriterion("DELIVERY_CYCLE not between", value1, value2, "deliveryCycle");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectIsNull() {
            addCriterion("DELIVERY_DIRECT is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectIsNotNull() {
            addCriterion("DELIVERY_DIRECT is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectEqualTo(Integer value) {
            addCriterion("DELIVERY_DIRECT =", value, "deliveryDirect");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectNotEqualTo(Integer value) {
            addCriterion("DELIVERY_DIRECT <>", value, "deliveryDirect");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectGreaterThan(Integer value) {
            addCriterion("DELIVERY_DIRECT >", value, "deliveryDirect");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectGreaterThanOrEqualTo(Integer value) {
            addCriterion("DELIVERY_DIRECT >=", value, "deliveryDirect");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectLessThan(Integer value) {
            addCriterion("DELIVERY_DIRECT <", value, "deliveryDirect");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectLessThanOrEqualTo(Integer value) {
            addCriterion("DELIVERY_DIRECT <=", value, "deliveryDirect");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectIn(List<Integer> values) {
            addCriterion("DELIVERY_DIRECT in", values, "deliveryDirect");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectNotIn(List<Integer> values) {
            addCriterion("DELIVERY_DIRECT not in", values, "deliveryDirect");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectBetween(Integer value1, Integer value2) {
            addCriterion("DELIVERY_DIRECT between", value1, value2, "deliveryDirect");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectNotBetween(Integer value1, Integer value2) {
            addCriterion("DELIVERY_DIRECT not between", value1, value2, "deliveryDirect");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectCommentsIsNull() {
            addCriterion("DELIVERY_DIRECT_COMMENTS is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectCommentsIsNotNull() {
            addCriterion("DELIVERY_DIRECT_COMMENTS is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectCommentsEqualTo(String value) {
            addCriterion("DELIVERY_DIRECT_COMMENTS =", value, "deliveryDirectComments");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectCommentsNotEqualTo(String value) {
            addCriterion("DELIVERY_DIRECT_COMMENTS <>", value, "deliveryDirectComments");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectCommentsGreaterThan(String value) {
            addCriterion("DELIVERY_DIRECT_COMMENTS >", value, "deliveryDirectComments");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectCommentsGreaterThanOrEqualTo(String value) {
            addCriterion("DELIVERY_DIRECT_COMMENTS >=", value, "deliveryDirectComments");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectCommentsLessThan(String value) {
            addCriterion("DELIVERY_DIRECT_COMMENTS <", value, "deliveryDirectComments");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectCommentsLessThanOrEqualTo(String value) {
            addCriterion("DELIVERY_DIRECT_COMMENTS <=", value, "deliveryDirectComments");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectCommentsLike(String value) {
            addCriterion("DELIVERY_DIRECT_COMMENTS like", value, "deliveryDirectComments");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectCommentsNotLike(String value) {
            addCriterion("DELIVERY_DIRECT_COMMENTS not like", value, "deliveryDirectComments");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectCommentsIn(List<String> values) {
            addCriterion("DELIVERY_DIRECT_COMMENTS in", values, "deliveryDirectComments");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectCommentsNotIn(List<String> values) {
            addCriterion("DELIVERY_DIRECT_COMMENTS not in", values, "deliveryDirectComments");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectCommentsBetween(String value1, String value2) {
            addCriterion("DELIVERY_DIRECT_COMMENTS between", value1, value2, "deliveryDirectComments");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectCommentsNotBetween(String value1, String value2) {
            addCriterion("DELIVERY_DIRECT_COMMENTS not between", value1, value2, "deliveryDirectComments");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIsNull() {
            addCriterion("REGISTRATION_NUMBER is null");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIsNotNull() {
            addCriterion("REGISTRATION_NUMBER is not null");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberEqualTo(String value) {
            addCriterion("REGISTRATION_NUMBER =", value, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberNotEqualTo(String value) {
            addCriterion("REGISTRATION_NUMBER <>", value, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberGreaterThan(String value) {
            addCriterion("REGISTRATION_NUMBER >", value, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberGreaterThanOrEqualTo(String value) {
            addCriterion("REGISTRATION_NUMBER >=", value, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberLessThan(String value) {
            addCriterion("REGISTRATION_NUMBER <", value, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberLessThanOrEqualTo(String value) {
            addCriterion("REGISTRATION_NUMBER <=", value, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberLike(String value) {
            addCriterion("REGISTRATION_NUMBER like", value, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberNotLike(String value) {
            addCriterion("REGISTRATION_NUMBER not like", value, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIn(List<String> values) {
            addCriterion("REGISTRATION_NUMBER in", values, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberNotIn(List<String> values) {
            addCriterion("REGISTRATION_NUMBER not in", values, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberBetween(String value1, String value2) {
            addCriterion("REGISTRATION_NUMBER between", value1, value2, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberNotBetween(String value1, String value2) {
            addCriterion("REGISTRATION_NUMBER not between", value1, value2, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andSupplierNameIsNull() {
            addCriterion("SUPPLIER_NAME is null");
            return (Criteria) this;
        }

        public Criteria andSupplierNameIsNotNull() {
            addCriterion("SUPPLIER_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andSupplierNameEqualTo(String value) {
            addCriterion("SUPPLIER_NAME =", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameNotEqualTo(String value) {
            addCriterion("SUPPLIER_NAME <>", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameGreaterThan(String value) {
            addCriterion("SUPPLIER_NAME >", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameGreaterThanOrEqualTo(String value) {
            addCriterion("SUPPLIER_NAME >=", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameLessThan(String value) {
            addCriterion("SUPPLIER_NAME <", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameLessThanOrEqualTo(String value) {
            addCriterion("SUPPLIER_NAME <=", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameLike(String value) {
            addCriterion("SUPPLIER_NAME like", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameNotLike(String value) {
            addCriterion("SUPPLIER_NAME not like", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameIn(List<String> values) {
            addCriterion("SUPPLIER_NAME in", values, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameNotIn(List<String> values) {
            addCriterion("SUPPLIER_NAME not in", values, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameBetween(String value1, String value2) {
            addCriterion("SUPPLIER_NAME between", value1, value2, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameNotBetween(String value1, String value2) {
            addCriterion("SUPPLIER_NAME not between", value1, value2, "supplierName");
            return (Criteria) this;
        }

        public Criteria andReferenceCostPriceIsNull() {
            addCriterion("REFERENCE_COST_PRICE is null");
            return (Criteria) this;
        }

        public Criteria andReferenceCostPriceIsNotNull() {
            addCriterion("REFERENCE_COST_PRICE is not null");
            return (Criteria) this;
        }

        public Criteria andReferenceCostPriceEqualTo(BigDecimal value) {
            addCriterion("REFERENCE_COST_PRICE =", value, "referenceCostPrice");
            return (Criteria) this;
        }

        public Criteria andReferenceCostPriceNotEqualTo(BigDecimal value) {
            addCriterion("REFERENCE_COST_PRICE <>", value, "referenceCostPrice");
            return (Criteria) this;
        }

        public Criteria andReferenceCostPriceGreaterThan(BigDecimal value) {
            addCriterion("REFERENCE_COST_PRICE >", value, "referenceCostPrice");
            return (Criteria) this;
        }

        public Criteria andReferenceCostPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("REFERENCE_COST_PRICE >=", value, "referenceCostPrice");
            return (Criteria) this;
        }

        public Criteria andReferenceCostPriceLessThan(BigDecimal value) {
            addCriterion("REFERENCE_COST_PRICE <", value, "referenceCostPrice");
            return (Criteria) this;
        }

        public Criteria andReferenceCostPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("REFERENCE_COST_PRICE <=", value, "referenceCostPrice");
            return (Criteria) this;
        }

        public Criteria andReferenceCostPriceIn(List<BigDecimal> values) {
            addCriterion("REFERENCE_COST_PRICE in", values, "referenceCostPrice");
            return (Criteria) this;
        }

        public Criteria andReferenceCostPriceNotIn(List<BigDecimal> values) {
            addCriterion("REFERENCE_COST_PRICE not in", values, "referenceCostPrice");
            return (Criteria) this;
        }

        public Criteria andReferenceCostPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("REFERENCE_COST_PRICE between", value1, value2, "referenceCostPrice");
            return (Criteria) this;
        }

        public Criteria andReferenceCostPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("REFERENCE_COST_PRICE not between", value1, value2, "referenceCostPrice");
            return (Criteria) this;
        }

        public Criteria andReferencePriceIsNull() {
            addCriterion("REFERENCE_PRICE is null");
            return (Criteria) this;
        }

        public Criteria andReferencePriceIsNotNull() {
            addCriterion("REFERENCE_PRICE is not null");
            return (Criteria) this;
        }

        public Criteria andReferencePriceEqualTo(BigDecimal value) {
            addCriterion("REFERENCE_PRICE =", value, "referencePrice");
            return (Criteria) this;
        }

        public Criteria andReferencePriceNotEqualTo(BigDecimal value) {
            addCriterion("REFERENCE_PRICE <>", value, "referencePrice");
            return (Criteria) this;
        }

        public Criteria andReferencePriceGreaterThan(BigDecimal value) {
            addCriterion("REFERENCE_PRICE >", value, "referencePrice");
            return (Criteria) this;
        }

        public Criteria andReferencePriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("REFERENCE_PRICE >=", value, "referencePrice");
            return (Criteria) this;
        }

        public Criteria andReferencePriceLessThan(BigDecimal value) {
            addCriterion("REFERENCE_PRICE <", value, "referencePrice");
            return (Criteria) this;
        }

        public Criteria andReferencePriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("REFERENCE_PRICE <=", value, "referencePrice");
            return (Criteria) this;
        }

        public Criteria andReferencePriceIn(List<BigDecimal> values) {
            addCriterion("REFERENCE_PRICE in", values, "referencePrice");
            return (Criteria) this;
        }

        public Criteria andReferencePriceNotIn(List<BigDecimal> values) {
            addCriterion("REFERENCE_PRICE not in", values, "referencePrice");
            return (Criteria) this;
        }

        public Criteria andReferencePriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("REFERENCE_PRICE between", value1, value2, "referencePrice");
            return (Criteria) this;
        }

        public Criteria andReferencePriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("REFERENCE_PRICE not between", value1, value2, "referencePrice");
            return (Criteria) this;
        }

        public Criteria andReferenceDeliveryCycleIsNull() {
            addCriterion("REFERENCE_DELIVERY_CYCLE is null");
            return (Criteria) this;
        }

        public Criteria andReferenceDeliveryCycleIsNotNull() {
            addCriterion("REFERENCE_DELIVERY_CYCLE is not null");
            return (Criteria) this;
        }

        public Criteria andReferenceDeliveryCycleEqualTo(String value) {
            addCriterion("REFERENCE_DELIVERY_CYCLE =", value, "referenceDeliveryCycle");
            return (Criteria) this;
        }

        public Criteria andReferenceDeliveryCycleNotEqualTo(String value) {
            addCriterion("REFERENCE_DELIVERY_CYCLE <>", value, "referenceDeliveryCycle");
            return (Criteria) this;
        }

        public Criteria andReferenceDeliveryCycleGreaterThan(String value) {
            addCriterion("REFERENCE_DELIVERY_CYCLE >", value, "referenceDeliveryCycle");
            return (Criteria) this;
        }

        public Criteria andReferenceDeliveryCycleGreaterThanOrEqualTo(String value) {
            addCriterion("REFERENCE_DELIVERY_CYCLE >=", value, "referenceDeliveryCycle");
            return (Criteria) this;
        }

        public Criteria andReferenceDeliveryCycleLessThan(String value) {
            addCriterion("REFERENCE_DELIVERY_CYCLE <", value, "referenceDeliveryCycle");
            return (Criteria) this;
        }

        public Criteria andReferenceDeliveryCycleLessThanOrEqualTo(String value) {
            addCriterion("REFERENCE_DELIVERY_CYCLE <=", value, "referenceDeliveryCycle");
            return (Criteria) this;
        }

        public Criteria andReferenceDeliveryCycleLike(String value) {
            addCriterion("REFERENCE_DELIVERY_CYCLE like", value, "referenceDeliveryCycle");
            return (Criteria) this;
        }

        public Criteria andReferenceDeliveryCycleNotLike(String value) {
            addCriterion("REFERENCE_DELIVERY_CYCLE not like", value, "referenceDeliveryCycle");
            return (Criteria) this;
        }

        public Criteria andReferenceDeliveryCycleIn(List<String> values) {
            addCriterion("REFERENCE_DELIVERY_CYCLE in", values, "referenceDeliveryCycle");
            return (Criteria) this;
        }

        public Criteria andReferenceDeliveryCycleNotIn(List<String> values) {
            addCriterion("REFERENCE_DELIVERY_CYCLE not in", values, "referenceDeliveryCycle");
            return (Criteria) this;
        }

        public Criteria andReferenceDeliveryCycleBetween(String value1, String value2) {
            addCriterion("REFERENCE_DELIVERY_CYCLE between", value1, value2, "referenceDeliveryCycle");
            return (Criteria) this;
        }

        public Criteria andReferenceDeliveryCycleNotBetween(String value1, String value2) {
            addCriterion("REFERENCE_DELIVERY_CYCLE not between", value1, value2, "referenceDeliveryCycle");
            return (Criteria) this;
        }

        public Criteria andReportStatusIsNull() {
            addCriterion("REPORT_STATUS is null");
            return (Criteria) this;
        }

        public Criteria andReportStatusIsNotNull() {
            addCriterion("REPORT_STATUS is not null");
            return (Criteria) this;
        }

        public Criteria andReportStatusEqualTo(Byte value) {
            addCriterion("REPORT_STATUS =", value, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusNotEqualTo(Byte value) {
            addCriterion("REPORT_STATUS <>", value, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusGreaterThan(Byte value) {
            addCriterion("REPORT_STATUS >", value, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("REPORT_STATUS >=", value, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusLessThan(Byte value) {
            addCriterion("REPORT_STATUS <", value, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusLessThanOrEqualTo(Byte value) {
            addCriterion("REPORT_STATUS <=", value, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusIn(List<Byte> values) {
            addCriterion("REPORT_STATUS in", values, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusNotIn(List<Byte> values) {
            addCriterion("REPORT_STATUS not in", values, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusBetween(Byte value1, Byte value2) {
            addCriterion("REPORT_STATUS between", value1, value2, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("REPORT_STATUS not between", value1, value2, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportCommentsIsNull() {
            addCriterion("REPORT_COMMENTS is null");
            return (Criteria) this;
        }

        public Criteria andReportCommentsIsNotNull() {
            addCriterion("REPORT_COMMENTS is not null");
            return (Criteria) this;
        }

        public Criteria andReportCommentsEqualTo(String value) {
            addCriterion("REPORT_COMMENTS =", value, "reportComments");
            return (Criteria) this;
        }

        public Criteria andReportCommentsNotEqualTo(String value) {
            addCriterion("REPORT_COMMENTS <>", value, "reportComments");
            return (Criteria) this;
        }

        public Criteria andReportCommentsGreaterThan(String value) {
            addCriterion("REPORT_COMMENTS >", value, "reportComments");
            return (Criteria) this;
        }

        public Criteria andReportCommentsGreaterThanOrEqualTo(String value) {
            addCriterion("REPORT_COMMENTS >=", value, "reportComments");
            return (Criteria) this;
        }

        public Criteria andReportCommentsLessThan(String value) {
            addCriterion("REPORT_COMMENTS <", value, "reportComments");
            return (Criteria) this;
        }

        public Criteria andReportCommentsLessThanOrEqualTo(String value) {
            addCriterion("REPORT_COMMENTS <=", value, "reportComments");
            return (Criteria) this;
        }

        public Criteria andReportCommentsLike(String value) {
            addCriterion("REPORT_COMMENTS like", value, "reportComments");
            return (Criteria) this;
        }

        public Criteria andReportCommentsNotLike(String value) {
            addCriterion("REPORT_COMMENTS not like", value, "reportComments");
            return (Criteria) this;
        }

        public Criteria andReportCommentsIn(List<String> values) {
            addCriterion("REPORT_COMMENTS in", values, "reportComments");
            return (Criteria) this;
        }

        public Criteria andReportCommentsNotIn(List<String> values) {
            addCriterion("REPORT_COMMENTS not in", values, "reportComments");
            return (Criteria) this;
        }

        public Criteria andReportCommentsBetween(String value1, String value2) {
            addCriterion("REPORT_COMMENTS between", value1, value2, "reportComments");
            return (Criteria) this;
        }

        public Criteria andReportCommentsNotBetween(String value1, String value2) {
            addCriterion("REPORT_COMMENTS not between", value1, value2, "reportComments");
            return (Criteria) this;
        }

        public Criteria andHaveInstallationIsNull() {
            addCriterion("HAVE_INSTALLATION is null");
            return (Criteria) this;
        }

        public Criteria andHaveInstallationIsNotNull() {
            addCriterion("HAVE_INSTALLATION is not null");
            return (Criteria) this;
        }

        public Criteria andHaveInstallationEqualTo(Byte value) {
            addCriterion("HAVE_INSTALLATION =", value, "haveInstallation");
            return (Criteria) this;
        }

        public Criteria andHaveInstallationNotEqualTo(Byte value) {
            addCriterion("HAVE_INSTALLATION <>", value, "haveInstallation");
            return (Criteria) this;
        }

        public Criteria andHaveInstallationGreaterThan(Byte value) {
            addCriterion("HAVE_INSTALLATION >", value, "haveInstallation");
            return (Criteria) this;
        }

        public Criteria andHaveInstallationGreaterThanOrEqualTo(Byte value) {
            addCriterion("HAVE_INSTALLATION >=", value, "haveInstallation");
            return (Criteria) this;
        }

        public Criteria andHaveInstallationLessThan(Byte value) {
            addCriterion("HAVE_INSTALLATION <", value, "haveInstallation");
            return (Criteria) this;
        }

        public Criteria andHaveInstallationLessThanOrEqualTo(Byte value) {
            addCriterion("HAVE_INSTALLATION <=", value, "haveInstallation");
            return (Criteria) this;
        }

        public Criteria andHaveInstallationIn(List<Byte> values) {
            addCriterion("HAVE_INSTALLATION in", values, "haveInstallation");
            return (Criteria) this;
        }

        public Criteria andHaveInstallationNotIn(List<Byte> values) {
            addCriterion("HAVE_INSTALLATION not in", values, "haveInstallation");
            return (Criteria) this;
        }

        public Criteria andHaveInstallationBetween(Byte value1, Byte value2) {
            addCriterion("HAVE_INSTALLATION between", value1, value2, "haveInstallation");
            return (Criteria) this;
        }

        public Criteria andHaveInstallationNotBetween(Byte value1, Byte value2) {
            addCriterion("HAVE_INSTALLATION not between", value1, value2, "haveInstallation");
            return (Criteria) this;
        }

        public Criteria andGoodsCommentsIsNull() {
            addCriterion("GOODS_COMMENTS is null");
            return (Criteria) this;
        }

        public Criteria andGoodsCommentsIsNotNull() {
            addCriterion("GOODS_COMMENTS is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsCommentsEqualTo(String value) {
            addCriterion("GOODS_COMMENTS =", value, "goodsComments");
            return (Criteria) this;
        }

        public Criteria andGoodsCommentsNotEqualTo(String value) {
            addCriterion("GOODS_COMMENTS <>", value, "goodsComments");
            return (Criteria) this;
        }

        public Criteria andGoodsCommentsGreaterThan(String value) {
            addCriterion("GOODS_COMMENTS >", value, "goodsComments");
            return (Criteria) this;
        }

        public Criteria andGoodsCommentsGreaterThanOrEqualTo(String value) {
            addCriterion("GOODS_COMMENTS >=", value, "goodsComments");
            return (Criteria) this;
        }

        public Criteria andGoodsCommentsLessThan(String value) {
            addCriterion("GOODS_COMMENTS <", value, "goodsComments");
            return (Criteria) this;
        }

        public Criteria andGoodsCommentsLessThanOrEqualTo(String value) {
            addCriterion("GOODS_COMMENTS <=", value, "goodsComments");
            return (Criteria) this;
        }

        public Criteria andGoodsCommentsLike(String value) {
            addCriterion("GOODS_COMMENTS like", value, "goodsComments");
            return (Criteria) this;
        }

        public Criteria andGoodsCommentsNotLike(String value) {
            addCriterion("GOODS_COMMENTS not like", value, "goodsComments");
            return (Criteria) this;
        }

        public Criteria andGoodsCommentsIn(List<String> values) {
            addCriterion("GOODS_COMMENTS in", values, "goodsComments");
            return (Criteria) this;
        }

        public Criteria andGoodsCommentsNotIn(List<String> values) {
            addCriterion("GOODS_COMMENTS not in", values, "goodsComments");
            return (Criteria) this;
        }

        public Criteria andGoodsCommentsBetween(String value1, String value2) {
            addCriterion("GOODS_COMMENTS between", value1, value2, "goodsComments");
            return (Criteria) this;
        }

        public Criteria andGoodsCommentsNotBetween(String value1, String value2) {
            addCriterion("GOODS_COMMENTS not between", value1, value2, "goodsComments");
            return (Criteria) this;
        }

        public Criteria andInsideCommentsIsNull() {
            addCriterion("INSIDE_COMMENTS is null");
            return (Criteria) this;
        }

        public Criteria andInsideCommentsIsNotNull() {
            addCriterion("INSIDE_COMMENTS is not null");
            return (Criteria) this;
        }

        public Criteria andInsideCommentsEqualTo(String value) {
            addCriterion("INSIDE_COMMENTS =", value, "insideComments");
            return (Criteria) this;
        }

        public Criteria andInsideCommentsNotEqualTo(String value) {
            addCriterion("INSIDE_COMMENTS <>", value, "insideComments");
            return (Criteria) this;
        }

        public Criteria andInsideCommentsGreaterThan(String value) {
            addCriterion("INSIDE_COMMENTS >", value, "insideComments");
            return (Criteria) this;
        }

        public Criteria andInsideCommentsGreaterThanOrEqualTo(String value) {
            addCriterion("INSIDE_COMMENTS >=", value, "insideComments");
            return (Criteria) this;
        }

        public Criteria andInsideCommentsLessThan(String value) {
            addCriterion("INSIDE_COMMENTS <", value, "insideComments");
            return (Criteria) this;
        }

        public Criteria andInsideCommentsLessThanOrEqualTo(String value) {
            addCriterion("INSIDE_COMMENTS <=", value, "insideComments");
            return (Criteria) this;
        }

        public Criteria andInsideCommentsLike(String value) {
            addCriterion("INSIDE_COMMENTS like", value, "insideComments");
            return (Criteria) this;
        }

        public Criteria andInsideCommentsNotLike(String value) {
            addCriterion("INSIDE_COMMENTS not like", value, "insideComments");
            return (Criteria) this;
        }

        public Criteria andInsideCommentsIn(List<String> values) {
            addCriterion("INSIDE_COMMENTS in", values, "insideComments");
            return (Criteria) this;
        }

        public Criteria andInsideCommentsNotIn(List<String> values) {
            addCriterion("INSIDE_COMMENTS not in", values, "insideComments");
            return (Criteria) this;
        }

        public Criteria andInsideCommentsBetween(String value1, String value2) {
            addCriterion("INSIDE_COMMENTS between", value1, value2, "insideComments");
            return (Criteria) this;
        }

        public Criteria andInsideCommentsNotBetween(String value1, String value2) {
            addCriterion("INSIDE_COMMENTS not between", value1, value2, "insideComments");
            return (Criteria) this;
        }

        public Criteria andArrivalUserIdIsNull() {
            addCriterion("ARRIVAL_USER_ID is null");
            return (Criteria) this;
        }

        public Criteria andArrivalUserIdIsNotNull() {
            addCriterion("ARRIVAL_USER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andArrivalUserIdEqualTo(Integer value) {
            addCriterion("ARRIVAL_USER_ID =", value, "arrivalUserId");
            return (Criteria) this;
        }

        public Criteria andArrivalUserIdNotEqualTo(Integer value) {
            addCriterion("ARRIVAL_USER_ID <>", value, "arrivalUserId");
            return (Criteria) this;
        }

        public Criteria andArrivalUserIdGreaterThan(Integer value) {
            addCriterion("ARRIVAL_USER_ID >", value, "arrivalUserId");
            return (Criteria) this;
        }

        public Criteria andArrivalUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("ARRIVAL_USER_ID >=", value, "arrivalUserId");
            return (Criteria) this;
        }

        public Criteria andArrivalUserIdLessThan(Integer value) {
            addCriterion("ARRIVAL_USER_ID <", value, "arrivalUserId");
            return (Criteria) this;
        }

        public Criteria andArrivalUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("ARRIVAL_USER_ID <=", value, "arrivalUserId");
            return (Criteria) this;
        }

        public Criteria andArrivalUserIdIn(List<Integer> values) {
            addCriterion("ARRIVAL_USER_ID in", values, "arrivalUserId");
            return (Criteria) this;
        }

        public Criteria andArrivalUserIdNotIn(List<Integer> values) {
            addCriterion("ARRIVAL_USER_ID not in", values, "arrivalUserId");
            return (Criteria) this;
        }

        public Criteria andArrivalUserIdBetween(Integer value1, Integer value2) {
            addCriterion("ARRIVAL_USER_ID between", value1, value2, "arrivalUserId");
            return (Criteria) this;
        }

        public Criteria andArrivalUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("ARRIVAL_USER_ID not between", value1, value2, "arrivalUserId");
            return (Criteria) this;
        }

        public Criteria andArrivalStatusIsNull() {
            addCriterion("ARRIVAL_STATUS is null");
            return (Criteria) this;
        }

        public Criteria andArrivalStatusIsNotNull() {
            addCriterion("ARRIVAL_STATUS is not null");
            return (Criteria) this;
        }

        public Criteria andArrivalStatusEqualTo(Byte value) {
            addCriterion("ARRIVAL_STATUS =", value, "arrivalStatus");
            return (Criteria) this;
        }

        public Criteria andArrivalStatusNotEqualTo(Byte value) {
            addCriterion("ARRIVAL_STATUS <>", value, "arrivalStatus");
            return (Criteria) this;
        }

        public Criteria andArrivalStatusGreaterThan(Byte value) {
            addCriterion("ARRIVAL_STATUS >", value, "arrivalStatus");
            return (Criteria) this;
        }

        public Criteria andArrivalStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("ARRIVAL_STATUS >=", value, "arrivalStatus");
            return (Criteria) this;
        }

        public Criteria andArrivalStatusLessThan(Byte value) {
            addCriterion("ARRIVAL_STATUS <", value, "arrivalStatus");
            return (Criteria) this;
        }

        public Criteria andArrivalStatusLessThanOrEqualTo(Byte value) {
            addCriterion("ARRIVAL_STATUS <=", value, "arrivalStatus");
            return (Criteria) this;
        }

        public Criteria andArrivalStatusIn(List<Byte> values) {
            addCriterion("ARRIVAL_STATUS in", values, "arrivalStatus");
            return (Criteria) this;
        }

        public Criteria andArrivalStatusNotIn(List<Byte> values) {
            addCriterion("ARRIVAL_STATUS not in", values, "arrivalStatus");
            return (Criteria) this;
        }

        public Criteria andArrivalStatusBetween(Byte value1, Byte value2) {
            addCriterion("ARRIVAL_STATUS between", value1, value2, "arrivalStatus");
            return (Criteria) this;
        }

        public Criteria andArrivalStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("ARRIVAL_STATUS not between", value1, value2, "arrivalStatus");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeIsNull() {
            addCriterion("ARRIVAL_TIME is null");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeIsNotNull() {
            addCriterion("ARRIVAL_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeEqualTo(Long value) {
            addCriterion("ARRIVAL_TIME =", value, "arrivalTime");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeNotEqualTo(Long value) {
            addCriterion("ARRIVAL_TIME <>", value, "arrivalTime");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeGreaterThan(Long value) {
            addCriterion("ARRIVAL_TIME >", value, "arrivalTime");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("ARRIVAL_TIME >=", value, "arrivalTime");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeLessThan(Long value) {
            addCriterion("ARRIVAL_TIME <", value, "arrivalTime");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeLessThanOrEqualTo(Long value) {
            addCriterion("ARRIVAL_TIME <=", value, "arrivalTime");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeIn(List<Long> values) {
            addCriterion("ARRIVAL_TIME in", values, "arrivalTime");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeNotIn(List<Long> values) {
            addCriterion("ARRIVAL_TIME not in", values, "arrivalTime");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeBetween(Long value1, Long value2) {
            addCriterion("ARRIVAL_TIME between", value1, value2, "arrivalTime");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeNotBetween(Long value1, Long value2) {
            addCriterion("ARRIVAL_TIME not between", value1, value2, "arrivalTime");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNull() {
            addCriterion("IS_DELETE is null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNotNull() {
            addCriterion("IS_DELETE is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteEqualTo(Integer value) {
            addCriterion("IS_DELETE =", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotEqualTo(Integer value) {
            addCriterion("IS_DELETE <>", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThan(Integer value) {
            addCriterion("IS_DELETE >", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThanOrEqualTo(Integer value) {
            addCriterion("IS_DELETE >=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThan(Integer value) {
            addCriterion("IS_DELETE <", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThanOrEqualTo(Integer value) {
            addCriterion("IS_DELETE <=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIn(List<Integer> values) {
            addCriterion("IS_DELETE in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotIn(List<Integer> values) {
            addCriterion("IS_DELETE not in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteBetween(Integer value1, Integer value2) {
            addCriterion("IS_DELETE between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotBetween(Integer value1, Integer value2) {
            addCriterion("IS_DELETE not between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsIgnoreIsNull() {
            addCriterion("IS_IGNORE is null");
            return (Criteria) this;
        }

        public Criteria andIsIgnoreIsNotNull() {
            addCriterion("IS_IGNORE is not null");
            return (Criteria) this;
        }

        public Criteria andIsIgnoreEqualTo(Byte value) {
            addCriterion("IS_IGNORE =", value, "isIgnore");
            return (Criteria) this;
        }

        public Criteria andIsIgnoreNotEqualTo(Byte value) {
            addCriterion("IS_IGNORE <>", value, "isIgnore");
            return (Criteria) this;
        }

        public Criteria andIsIgnoreGreaterThan(Byte value) {
            addCriterion("IS_IGNORE >", value, "isIgnore");
            return (Criteria) this;
        }

        public Criteria andIsIgnoreGreaterThanOrEqualTo(Byte value) {
            addCriterion("IS_IGNORE >=", value, "isIgnore");
            return (Criteria) this;
        }

        public Criteria andIsIgnoreLessThan(Byte value) {
            addCriterion("IS_IGNORE <", value, "isIgnore");
            return (Criteria) this;
        }

        public Criteria andIsIgnoreLessThanOrEqualTo(Byte value) {
            addCriterion("IS_IGNORE <=", value, "isIgnore");
            return (Criteria) this;
        }

        public Criteria andIsIgnoreIn(List<Byte> values) {
            addCriterion("IS_IGNORE in", values, "isIgnore");
            return (Criteria) this;
        }

        public Criteria andIsIgnoreNotIn(List<Byte> values) {
            addCriterion("IS_IGNORE not in", values, "isIgnore");
            return (Criteria) this;
        }

        public Criteria andIsIgnoreBetween(Byte value1, Byte value2) {
            addCriterion("IS_IGNORE between", value1, value2, "isIgnore");
            return (Criteria) this;
        }

        public Criteria andIsIgnoreNotBetween(Byte value1, Byte value2) {
            addCriterion("IS_IGNORE not between", value1, value2, "isIgnore");
            return (Criteria) this;
        }

        public Criteria andIgnoreTimeIsNull() {
            addCriterion("IGNORE_TIME is null");
            return (Criteria) this;
        }

        public Criteria andIgnoreTimeIsNotNull() {
            addCriterion("IGNORE_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andIgnoreTimeEqualTo(Long value) {
            addCriterion("IGNORE_TIME =", value, "ignoreTime");
            return (Criteria) this;
        }

        public Criteria andIgnoreTimeNotEqualTo(Long value) {
            addCriterion("IGNORE_TIME <>", value, "ignoreTime");
            return (Criteria) this;
        }

        public Criteria andIgnoreTimeGreaterThan(Long value) {
            addCriterion("IGNORE_TIME >", value, "ignoreTime");
            return (Criteria) this;
        }

        public Criteria andIgnoreTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("IGNORE_TIME >=", value, "ignoreTime");
            return (Criteria) this;
        }

        public Criteria andIgnoreTimeLessThan(Long value) {
            addCriterion("IGNORE_TIME <", value, "ignoreTime");
            return (Criteria) this;
        }

        public Criteria andIgnoreTimeLessThanOrEqualTo(Long value) {
            addCriterion("IGNORE_TIME <=", value, "ignoreTime");
            return (Criteria) this;
        }

        public Criteria andIgnoreTimeIn(List<Long> values) {
            addCriterion("IGNORE_TIME in", values, "ignoreTime");
            return (Criteria) this;
        }

        public Criteria andIgnoreTimeNotIn(List<Long> values) {
            addCriterion("IGNORE_TIME not in", values, "ignoreTime");
            return (Criteria) this;
        }

        public Criteria andIgnoreTimeBetween(Long value1, Long value2) {
            addCriterion("IGNORE_TIME between", value1, value2, "ignoreTime");
            return (Criteria) this;
        }

        public Criteria andIgnoreTimeNotBetween(Long value1, Long value2) {
            addCriterion("IGNORE_TIME not between", value1, value2, "ignoreTime");
            return (Criteria) this;
        }

        public Criteria andIgnoreUserIdIsNull() {
            addCriterion("IGNORE_USER_ID is null");
            return (Criteria) this;
        }

        public Criteria andIgnoreUserIdIsNotNull() {
            addCriterion("IGNORE_USER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andIgnoreUserIdEqualTo(Integer value) {
            addCriterion("IGNORE_USER_ID =", value, "ignoreUserId");
            return (Criteria) this;
        }

        public Criteria andIgnoreUserIdNotEqualTo(Integer value) {
            addCriterion("IGNORE_USER_ID <>", value, "ignoreUserId");
            return (Criteria) this;
        }

        public Criteria andIgnoreUserIdGreaterThan(Integer value) {
            addCriterion("IGNORE_USER_ID >", value, "ignoreUserId");
            return (Criteria) this;
        }

        public Criteria andIgnoreUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("IGNORE_USER_ID >=", value, "ignoreUserId");
            return (Criteria) this;
        }

        public Criteria andIgnoreUserIdLessThan(Integer value) {
            addCriterion("IGNORE_USER_ID <", value, "ignoreUserId");
            return (Criteria) this;
        }

        public Criteria andIgnoreUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("IGNORE_USER_ID <=", value, "ignoreUserId");
            return (Criteria) this;
        }

        public Criteria andIgnoreUserIdIn(List<Integer> values) {
            addCriterion("IGNORE_USER_ID in", values, "ignoreUserId");
            return (Criteria) this;
        }

        public Criteria andIgnoreUserIdNotIn(List<Integer> values) {
            addCriterion("IGNORE_USER_ID not in", values, "ignoreUserId");
            return (Criteria) this;
        }

        public Criteria andIgnoreUserIdBetween(Integer value1, Integer value2) {
            addCriterion("IGNORE_USER_ID between", value1, value2, "ignoreUserId");
            return (Criteria) this;
        }

        public Criteria andIgnoreUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("IGNORE_USER_ID not between", value1, value2, "ignoreUserId");
            return (Criteria) this;
        }

        public Criteria andLockedStatusIsNull() {
            addCriterion("LOCKED_STATUS is null");
            return (Criteria) this;
        }

        public Criteria andLockedStatusIsNotNull() {
            addCriterion("LOCKED_STATUS is not null");
            return (Criteria) this;
        }

        public Criteria andLockedStatusEqualTo(Byte value) {
            addCriterion("LOCKED_STATUS =", value, "lockedStatus");
            return (Criteria) this;
        }

        public Criteria andLockedStatusNotEqualTo(Byte value) {
            addCriterion("LOCKED_STATUS <>", value, "lockedStatus");
            return (Criteria) this;
        }

        public Criteria andLockedStatusGreaterThan(Byte value) {
            addCriterion("LOCKED_STATUS >", value, "lockedStatus");
            return (Criteria) this;
        }

        public Criteria andLockedStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("LOCKED_STATUS >=", value, "lockedStatus");
            return (Criteria) this;
        }

        public Criteria andLockedStatusLessThan(Byte value) {
            addCriterion("LOCKED_STATUS <", value, "lockedStatus");
            return (Criteria) this;
        }

        public Criteria andLockedStatusLessThanOrEqualTo(Byte value) {
            addCriterion("LOCKED_STATUS <=", value, "lockedStatus");
            return (Criteria) this;
        }

        public Criteria andLockedStatusIn(List<Byte> values) {
            addCriterion("LOCKED_STATUS in", values, "lockedStatus");
            return (Criteria) this;
        }

        public Criteria andLockedStatusNotIn(List<Byte> values) {
            addCriterion("LOCKED_STATUS not in", values, "lockedStatus");
            return (Criteria) this;
        }

        public Criteria andLockedStatusBetween(Byte value1, Byte value2) {
            addCriterion("LOCKED_STATUS between", value1, value2, "lockedStatus");
            return (Criteria) this;
        }

        public Criteria andLockedStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("LOCKED_STATUS not between", value1, value2, "lockedStatus");
            return (Criteria) this;
        }

        public Criteria andMaxSkuRefundAmountIsNull() {
            addCriterion("MAX_SKU_REFUND_AMOUNT is null");
            return (Criteria) this;
        }

        public Criteria andMaxSkuRefundAmountIsNotNull() {
            addCriterion("MAX_SKU_REFUND_AMOUNT is not null");
            return (Criteria) this;
        }

        public Criteria andMaxSkuRefundAmountEqualTo(BigDecimal value) {
            addCriterion("MAX_SKU_REFUND_AMOUNT =", value, "maxSkuRefundAmount");
            return (Criteria) this;
        }

        public Criteria andMaxSkuRefundAmountNotEqualTo(BigDecimal value) {
            addCriterion("MAX_SKU_REFUND_AMOUNT <>", value, "maxSkuRefundAmount");
            return (Criteria) this;
        }

        public Criteria andMaxSkuRefundAmountGreaterThan(BigDecimal value) {
            addCriterion("MAX_SKU_REFUND_AMOUNT >", value, "maxSkuRefundAmount");
            return (Criteria) this;
        }

        public Criteria andMaxSkuRefundAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("MAX_SKU_REFUND_AMOUNT >=", value, "maxSkuRefundAmount");
            return (Criteria) this;
        }

        public Criteria andMaxSkuRefundAmountLessThan(BigDecimal value) {
            addCriterion("MAX_SKU_REFUND_AMOUNT <", value, "maxSkuRefundAmount");
            return (Criteria) this;
        }

        public Criteria andMaxSkuRefundAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("MAX_SKU_REFUND_AMOUNT <=", value, "maxSkuRefundAmount");
            return (Criteria) this;
        }

        public Criteria andMaxSkuRefundAmountIn(List<BigDecimal> values) {
            addCriterion("MAX_SKU_REFUND_AMOUNT in", values, "maxSkuRefundAmount");
            return (Criteria) this;
        }

        public Criteria andMaxSkuRefundAmountNotIn(List<BigDecimal> values) {
            addCriterion("MAX_SKU_REFUND_AMOUNT not in", values, "maxSkuRefundAmount");
            return (Criteria) this;
        }

        public Criteria andMaxSkuRefundAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("MAX_SKU_REFUND_AMOUNT between", value1, value2, "maxSkuRefundAmount");
            return (Criteria) this;
        }

        public Criteria andMaxSkuRefundAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("MAX_SKU_REFUND_AMOUNT not between", value1, value2, "maxSkuRefundAmount");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("ADD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("ADD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Long value) {
            addCriterion("ADD_TIME =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Long value) {
            addCriterion("ADD_TIME <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Long value) {
            addCriterion("ADD_TIME >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("ADD_TIME >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Long value) {
            addCriterion("ADD_TIME <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Long value) {
            addCriterion("ADD_TIME <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Long> values) {
            addCriterion("ADD_TIME in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Long> values) {
            addCriterion("ADD_TIME not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Long value1, Long value2) {
            addCriterion("ADD_TIME between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Long value1, Long value2) {
            addCriterion("ADD_TIME not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("CREATOR is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("CREATOR is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(Integer value) {
            addCriterion("CREATOR =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(Integer value) {
            addCriterion("CREATOR <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(Integer value) {
            addCriterion("CREATOR >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("CREATOR >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(Integer value) {
            addCriterion("CREATOR <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(Integer value) {
            addCriterion("CREATOR <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<Integer> values) {
            addCriterion("CREATOR in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<Integer> values) {
            addCriterion("CREATOR not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNull() {
            addCriterion("MOD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNotNull() {
            addCriterion("MOD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andModTimeEqualTo(Long value) {
            addCriterion("MOD_TIME =", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotEqualTo(Long value) {
            addCriterion("MOD_TIME <>", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThan(Long value) {
            addCriterion("MOD_TIME >", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("MOD_TIME >=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThan(Long value) {
            addCriterion("MOD_TIME <", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThanOrEqualTo(Long value) {
            addCriterion("MOD_TIME <=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeIn(List<Long> values) {
            addCriterion("MOD_TIME in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotIn(List<Long> values) {
            addCriterion("MOD_TIME not in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeBetween(Long value1, Long value2) {
            addCriterion("MOD_TIME between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotBetween(Long value1, Long value2) {
            addCriterion("MOD_TIME not between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNull() {
            addCriterion("UPDATER is null");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNotNull() {
            addCriterion("UPDATER is not null");
            return (Criteria) this;
        }

        public Criteria andUpdaterEqualTo(Integer value) {
            addCriterion("UPDATER =", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotEqualTo(Integer value) {
            addCriterion("UPDATER <>", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThan(Integer value) {
            addCriterion("UPDATER >", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThanOrEqualTo(Integer value) {
            addCriterion("UPDATER >=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThan(Integer value) {
            addCriterion("UPDATER <", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThanOrEqualTo(Integer value) {
            addCriterion("UPDATER <=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterIn(List<Integer> values) {
            addCriterion("UPDATER in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotIn(List<Integer> values) {
            addCriterion("UPDATER not in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER not between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andOccupyNumIsNull() {
            addCriterion("OCCUPY_NUM is null");
            return (Criteria) this;
        }

        public Criteria andOccupyNumIsNotNull() {
            addCriterion("OCCUPY_NUM is not null");
            return (Criteria) this;
        }

        public Criteria andOccupyNumEqualTo(Integer value) {
            addCriterion("OCCUPY_NUM =", value, "occupyNum");
            return (Criteria) this;
        }

        public Criteria andOccupyNumNotEqualTo(Integer value) {
            addCriterion("OCCUPY_NUM <>", value, "occupyNum");
            return (Criteria) this;
        }

        public Criteria andOccupyNumGreaterThan(Integer value) {
            addCriterion("OCCUPY_NUM >", value, "occupyNum");
            return (Criteria) this;
        }

        public Criteria andOccupyNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("OCCUPY_NUM >=", value, "occupyNum");
            return (Criteria) this;
        }

        public Criteria andOccupyNumLessThan(Integer value) {
            addCriterion("OCCUPY_NUM <", value, "occupyNum");
            return (Criteria) this;
        }

        public Criteria andOccupyNumLessThanOrEqualTo(Integer value) {
            addCriterion("OCCUPY_NUM <=", value, "occupyNum");
            return (Criteria) this;
        }

        public Criteria andOccupyNumIn(List<Integer> values) {
            addCriterion("OCCUPY_NUM in", values, "occupyNum");
            return (Criteria) this;
        }

        public Criteria andOccupyNumNotIn(List<Integer> values) {
            addCriterion("OCCUPY_NUM not in", values, "occupyNum");
            return (Criteria) this;
        }

        public Criteria andOccupyNumBetween(Integer value1, Integer value2) {
            addCriterion("OCCUPY_NUM between", value1, value2, "occupyNum");
            return (Criteria) this;
        }

        public Criteria andOccupyNumNotBetween(Integer value1, Integer value2) {
            addCriterion("OCCUPY_NUM not between", value1, value2, "occupyNum");
            return (Criteria) this;
        }

        public Criteria andIsActionGoodsIsNull() {
            addCriterion("IS_ACTION_GOODS is null");
            return (Criteria) this;
        }

        public Criteria andIsActionGoodsIsNotNull() {
            addCriterion("IS_ACTION_GOODS is not null");
            return (Criteria) this;
        }

        public Criteria andIsActionGoodsEqualTo(Byte value) {
            addCriterion("IS_ACTION_GOODS =", value, "isActionGoods");
            return (Criteria) this;
        }

        public Criteria andIsActionGoodsNotEqualTo(Byte value) {
            addCriterion("IS_ACTION_GOODS <>", value, "isActionGoods");
            return (Criteria) this;
        }

        public Criteria andIsActionGoodsGreaterThan(Byte value) {
            addCriterion("IS_ACTION_GOODS >", value, "isActionGoods");
            return (Criteria) this;
        }

        public Criteria andIsActionGoodsGreaterThanOrEqualTo(Byte value) {
            addCriterion("IS_ACTION_GOODS >=", value, "isActionGoods");
            return (Criteria) this;
        }

        public Criteria andIsActionGoodsLessThan(Byte value) {
            addCriterion("IS_ACTION_GOODS <", value, "isActionGoods");
            return (Criteria) this;
        }

        public Criteria andIsActionGoodsLessThanOrEqualTo(Byte value) {
            addCriterion("IS_ACTION_GOODS <=", value, "isActionGoods");
            return (Criteria) this;
        }

        public Criteria andIsActionGoodsIn(List<Byte> values) {
            addCriterion("IS_ACTION_GOODS in", values, "isActionGoods");
            return (Criteria) this;
        }

        public Criteria andIsActionGoodsNotIn(List<Byte> values) {
            addCriterion("IS_ACTION_GOODS not in", values, "isActionGoods");
            return (Criteria) this;
        }

        public Criteria andIsActionGoodsBetween(Byte value1, Byte value2) {
            addCriterion("IS_ACTION_GOODS between", value1, value2, "isActionGoods");
            return (Criteria) this;
        }

        public Criteria andIsActionGoodsNotBetween(Byte value1, Byte value2) {
            addCriterion("IS_ACTION_GOODS not between", value1, value2, "isActionGoods");
            return (Criteria) this;
        }

        public Criteria andActionOccupyNumIsNull() {
            addCriterion("ACTION_OCCUPY_NUM is null");
            return (Criteria) this;
        }

        public Criteria andActionOccupyNumIsNotNull() {
            addCriterion("ACTION_OCCUPY_NUM is not null");
            return (Criteria) this;
        }

        public Criteria andActionOccupyNumEqualTo(Integer value) {
            addCriterion("ACTION_OCCUPY_NUM =", value, "actionOccupyNum");
            return (Criteria) this;
        }

        public Criteria andActionOccupyNumNotEqualTo(Integer value) {
            addCriterion("ACTION_OCCUPY_NUM <>", value, "actionOccupyNum");
            return (Criteria) this;
        }

        public Criteria andActionOccupyNumGreaterThan(Integer value) {
            addCriterion("ACTION_OCCUPY_NUM >", value, "actionOccupyNum");
            return (Criteria) this;
        }

        public Criteria andActionOccupyNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("ACTION_OCCUPY_NUM >=", value, "actionOccupyNum");
            return (Criteria) this;
        }

        public Criteria andActionOccupyNumLessThan(Integer value) {
            addCriterion("ACTION_OCCUPY_NUM <", value, "actionOccupyNum");
            return (Criteria) this;
        }

        public Criteria andActionOccupyNumLessThanOrEqualTo(Integer value) {
            addCriterion("ACTION_OCCUPY_NUM <=", value, "actionOccupyNum");
            return (Criteria) this;
        }

        public Criteria andActionOccupyNumIn(List<Integer> values) {
            addCriterion("ACTION_OCCUPY_NUM in", values, "actionOccupyNum");
            return (Criteria) this;
        }

        public Criteria andActionOccupyNumNotIn(List<Integer> values) {
            addCriterion("ACTION_OCCUPY_NUM not in", values, "actionOccupyNum");
            return (Criteria) this;
        }

        public Criteria andActionOccupyNumBetween(Integer value1, Integer value2) {
            addCriterion("ACTION_OCCUPY_NUM between", value1, value2, "actionOccupyNum");
            return (Criteria) this;
        }

        public Criteria andActionOccupyNumNotBetween(Integer value1, Integer value2) {
            addCriterion("ACTION_OCCUPY_NUM not between", value1, value2, "actionOccupyNum");
            return (Criteria) this;
        }

        public Criteria andIsCouponsIsNull() {
            addCriterion("IS_COUPONS is null");
            return (Criteria) this;
        }

        public Criteria andIsCouponsIsNotNull() {
            addCriterion("IS_COUPONS is not null");
            return (Criteria) this;
        }

        public Criteria andIsCouponsEqualTo(Byte value) {
            addCriterion("IS_COUPONS =", value, "isCoupons");
            return (Criteria) this;
        }

        public Criteria andIsCouponsNotEqualTo(Byte value) {
            addCriterion("IS_COUPONS <>", value, "isCoupons");
            return (Criteria) this;
        }

        public Criteria andIsCouponsGreaterThan(Byte value) {
            addCriterion("IS_COUPONS >", value, "isCoupons");
            return (Criteria) this;
        }

        public Criteria andIsCouponsGreaterThanOrEqualTo(Byte value) {
            addCriterion("IS_COUPONS >=", value, "isCoupons");
            return (Criteria) this;
        }

        public Criteria andIsCouponsLessThan(Byte value) {
            addCriterion("IS_COUPONS <", value, "isCoupons");
            return (Criteria) this;
        }

        public Criteria andIsCouponsLessThanOrEqualTo(Byte value) {
            addCriterion("IS_COUPONS <=", value, "isCoupons");
            return (Criteria) this;
        }

        public Criteria andIsCouponsIn(List<Byte> values) {
            addCriterion("IS_COUPONS in", values, "isCoupons");
            return (Criteria) this;
        }

        public Criteria andIsCouponsNotIn(List<Byte> values) {
            addCriterion("IS_COUPONS not in", values, "isCoupons");
            return (Criteria) this;
        }

        public Criteria andIsCouponsBetween(Byte value1, Byte value2) {
            addCriterion("IS_COUPONS between", value1, value2, "isCoupons");
            return (Criteria) this;
        }

        public Criteria andIsCouponsNotBetween(Byte value1, Byte value2) {
            addCriterion("IS_COUPONS not between", value1, value2, "isCoupons");
            return (Criteria) this;
        }

        public Criteria andElOrderlistIdIsNull() {
            addCriterion("EL_ORDERLIST_ID is null");
            return (Criteria) this;
        }

        public Criteria andElOrderlistIdIsNotNull() {
            addCriterion("EL_ORDERLIST_ID is not null");
            return (Criteria) this;
        }

        public Criteria andElOrderlistIdEqualTo(Integer value) {
            addCriterion("EL_ORDERLIST_ID =", value, "elOrderlistId");
            return (Criteria) this;
        }

        public Criteria andElOrderlistIdNotEqualTo(Integer value) {
            addCriterion("EL_ORDERLIST_ID <>", value, "elOrderlistId");
            return (Criteria) this;
        }

        public Criteria andElOrderlistIdGreaterThan(Integer value) {
            addCriterion("EL_ORDERLIST_ID >", value, "elOrderlistId");
            return (Criteria) this;
        }

        public Criteria andElOrderlistIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("EL_ORDERLIST_ID >=", value, "elOrderlistId");
            return (Criteria) this;
        }

        public Criteria andElOrderlistIdLessThan(Integer value) {
            addCriterion("EL_ORDERLIST_ID <", value, "elOrderlistId");
            return (Criteria) this;
        }

        public Criteria andElOrderlistIdLessThanOrEqualTo(Integer value) {
            addCriterion("EL_ORDERLIST_ID <=", value, "elOrderlistId");
            return (Criteria) this;
        }

        public Criteria andElOrderlistIdIn(List<Integer> values) {
            addCriterion("EL_ORDERLIST_ID in", values, "elOrderlistId");
            return (Criteria) this;
        }

        public Criteria andElOrderlistIdNotIn(List<Integer> values) {
            addCriterion("EL_ORDERLIST_ID not in", values, "elOrderlistId");
            return (Criteria) this;
        }

        public Criteria andElOrderlistIdBetween(Integer value1, Integer value2) {
            addCriterion("EL_ORDERLIST_ID between", value1, value2, "elOrderlistId");
            return (Criteria) this;
        }

        public Criteria andElOrderlistIdNotBetween(Integer value1, Integer value2) {
            addCriterion("EL_ORDERLIST_ID not between", value1, value2, "elOrderlistId");
            return (Criteria) this;
        }

        public Criteria andUpdateDataTimeIsNull() {
            addCriterion("UPDATE_DATA_TIME is null");
            return (Criteria) this;
        }

        public Criteria andUpdateDataTimeIsNotNull() {
            addCriterion("UPDATE_DATA_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateDataTimeEqualTo(Date value) {
            addCriterion("UPDATE_DATA_TIME =", value, "updateDataTime");
            return (Criteria) this;
        }

        public Criteria andUpdateDataTimeNotEqualTo(Date value) {
            addCriterion("UPDATE_DATA_TIME <>", value, "updateDataTime");
            return (Criteria) this;
        }

        public Criteria andUpdateDataTimeGreaterThan(Date value) {
            addCriterion("UPDATE_DATA_TIME >", value, "updateDataTime");
            return (Criteria) this;
        }

        public Criteria andUpdateDataTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("UPDATE_DATA_TIME >=", value, "updateDataTime");
            return (Criteria) this;
        }

        public Criteria andUpdateDataTimeLessThan(Date value) {
            addCriterion("UPDATE_DATA_TIME <", value, "updateDataTime");
            return (Criteria) this;
        }

        public Criteria andUpdateDataTimeLessThanOrEqualTo(Date value) {
            addCriterion("UPDATE_DATA_TIME <=", value, "updateDataTime");
            return (Criteria) this;
        }

        public Criteria andUpdateDataTimeIn(List<Date> values) {
            addCriterion("UPDATE_DATA_TIME in", values, "updateDataTime");
            return (Criteria) this;
        }

        public Criteria andUpdateDataTimeNotIn(List<Date> values) {
            addCriterion("UPDATE_DATA_TIME not in", values, "updateDataTime");
            return (Criteria) this;
        }

        public Criteria andUpdateDataTimeBetween(Date value1, Date value2) {
            addCriterion("UPDATE_DATA_TIME between", value1, value2, "updateDataTime");
            return (Criteria) this;
        }

        public Criteria andUpdateDataTimeNotBetween(Date value1, Date value2) {
            addCriterion("UPDATE_DATA_TIME not between", value1, value2, "updateDataTime");
            return (Criteria) this;
        }

        public Criteria andRealPayAmountIsNull() {
            addCriterion("REAL_PAY_AMOUNT is null");
            return (Criteria) this;
        }

        public Criteria andRealPayAmountIsNotNull() {
            addCriterion("REAL_PAY_AMOUNT is not null");
            return (Criteria) this;
        }

        public Criteria andRealPayAmountEqualTo(BigDecimal value) {
            addCriterion("REAL_PAY_AMOUNT =", value, "realPayAmount");
            return (Criteria) this;
        }

        public Criteria andRealPayAmountNotEqualTo(BigDecimal value) {
            addCriterion("REAL_PAY_AMOUNT <>", value, "realPayAmount");
            return (Criteria) this;
        }

        public Criteria andRealPayAmountGreaterThan(BigDecimal value) {
            addCriterion("REAL_PAY_AMOUNT >", value, "realPayAmount");
            return (Criteria) this;
        }

        public Criteria andRealPayAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("REAL_PAY_AMOUNT >=", value, "realPayAmount");
            return (Criteria) this;
        }

        public Criteria andRealPayAmountLessThan(BigDecimal value) {
            addCriterion("REAL_PAY_AMOUNT <", value, "realPayAmount");
            return (Criteria) this;
        }

        public Criteria andRealPayAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("REAL_PAY_AMOUNT <=", value, "realPayAmount");
            return (Criteria) this;
        }

        public Criteria andRealPayAmountIn(List<BigDecimal> values) {
            addCriterion("REAL_PAY_AMOUNT in", values, "realPayAmount");
            return (Criteria) this;
        }

        public Criteria andRealPayAmountNotIn(List<BigDecimal> values) {
            addCriterion("REAL_PAY_AMOUNT not in", values, "realPayAmount");
            return (Criteria) this;
        }

        public Criteria andRealPayAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("REAL_PAY_AMOUNT between", value1, value2, "realPayAmount");
            return (Criteria) this;
        }

        public Criteria andRealPayAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("REAL_PAY_AMOUNT not between", value1, value2, "realPayAmount");
            return (Criteria) this;
        }

        public Criteria andAfterReturnAmountIsNull() {
            addCriterion("AFTER_RETURN_AMOUNT is null");
            return (Criteria) this;
        }

        public Criteria andAfterReturnAmountIsNotNull() {
            addCriterion("AFTER_RETURN_AMOUNT is not null");
            return (Criteria) this;
        }

        public Criteria andAfterReturnAmountEqualTo(BigDecimal value) {
            addCriterion("AFTER_RETURN_AMOUNT =", value, "afterReturnAmount");
            return (Criteria) this;
        }

        public Criteria andAfterReturnAmountNotEqualTo(BigDecimal value) {
            addCriterion("AFTER_RETURN_AMOUNT <>", value, "afterReturnAmount");
            return (Criteria) this;
        }

        public Criteria andAfterReturnAmountGreaterThan(BigDecimal value) {
            addCriterion("AFTER_RETURN_AMOUNT >", value, "afterReturnAmount");
            return (Criteria) this;
        }

        public Criteria andAfterReturnAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("AFTER_RETURN_AMOUNT >=", value, "afterReturnAmount");
            return (Criteria) this;
        }

        public Criteria andAfterReturnAmountLessThan(BigDecimal value) {
            addCriterion("AFTER_RETURN_AMOUNT <", value, "afterReturnAmount");
            return (Criteria) this;
        }

        public Criteria andAfterReturnAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("AFTER_RETURN_AMOUNT <=", value, "afterReturnAmount");
            return (Criteria) this;
        }

        public Criteria andAfterReturnAmountIn(List<BigDecimal> values) {
            addCriterion("AFTER_RETURN_AMOUNT in", values, "afterReturnAmount");
            return (Criteria) this;
        }

        public Criteria andAfterReturnAmountNotIn(List<BigDecimal> values) {
            addCriterion("AFTER_RETURN_AMOUNT not in", values, "afterReturnAmount");
            return (Criteria) this;
        }

        public Criteria andAfterReturnAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("AFTER_RETURN_AMOUNT between", value1, value2, "afterReturnAmount");
            return (Criteria) this;
        }

        public Criteria andAfterReturnAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("AFTER_RETURN_AMOUNT not between", value1, value2, "afterReturnAmount");
            return (Criteria) this;
        }

        public Criteria andAfterReturnNumIsNull() {
            addCriterion("AFTER_RETURN_NUM is null");
            return (Criteria) this;
        }

        public Criteria andAfterReturnNumIsNotNull() {
            addCriterion("AFTER_RETURN_NUM is not null");
            return (Criteria) this;
        }

        public Criteria andAfterReturnNumEqualTo(Integer value) {
            addCriterion("AFTER_RETURN_NUM =", value, "afterReturnNum");
            return (Criteria) this;
        }

        public Criteria andAfterReturnNumNotEqualTo(Integer value) {
            addCriterion("AFTER_RETURN_NUM <>", value, "afterReturnNum");
            return (Criteria) this;
        }

        public Criteria andAfterReturnNumGreaterThan(Integer value) {
            addCriterion("AFTER_RETURN_NUM >", value, "afterReturnNum");
            return (Criteria) this;
        }

        public Criteria andAfterReturnNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("AFTER_RETURN_NUM >=", value, "afterReturnNum");
            return (Criteria) this;
        }

        public Criteria andAfterReturnNumLessThan(Integer value) {
            addCriterion("AFTER_RETURN_NUM <", value, "afterReturnNum");
            return (Criteria) this;
        }

        public Criteria andAfterReturnNumLessThanOrEqualTo(Integer value) {
            addCriterion("AFTER_RETURN_NUM <=", value, "afterReturnNum");
            return (Criteria) this;
        }

        public Criteria andAfterReturnNumIn(List<Integer> values) {
            addCriterion("AFTER_RETURN_NUM in", values, "afterReturnNum");
            return (Criteria) this;
        }

        public Criteria andAfterReturnNumNotIn(List<Integer> values) {
            addCriterion("AFTER_RETURN_NUM not in", values, "afterReturnNum");
            return (Criteria) this;
        }

        public Criteria andAfterReturnNumBetween(Integer value1, Integer value2) {
            addCriterion("AFTER_RETURN_NUM between", value1, value2, "afterReturnNum");
            return (Criteria) this;
        }

        public Criteria andAfterReturnNumNotBetween(Integer value1, Integer value2) {
            addCriterion("AFTER_RETURN_NUM not between", value1, value2, "afterReturnNum");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_SALEORDER_GOODS
     *
     * @mbggenerated do_not_delete_during_merge Fri Jun 05 17:05:41 CST 2020
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_SALEORDER_GOODS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}