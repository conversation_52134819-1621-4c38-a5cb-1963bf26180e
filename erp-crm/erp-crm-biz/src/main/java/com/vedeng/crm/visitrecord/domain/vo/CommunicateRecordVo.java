package com.vedeng.crm.visitrecord.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.Date;

@Data
public class CommunicateRecordVo {
    /** 沟通记录ID */
    private Integer communicateRecordId;
    
    /** 关联ID */
    private Integer relatedId;
    
    /** 记录人ID */
    private Integer creator;
    
    /** 记录人姓名 */
    private String userName;
    
    /** 记录人头像 */
    private String aliasHeadPicture;
    
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date addTime;
    
    /** 沟通内容 */
    private String contentSuffix;
} 