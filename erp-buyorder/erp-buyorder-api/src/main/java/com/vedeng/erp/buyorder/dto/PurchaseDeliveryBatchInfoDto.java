package com.vedeng.erp.buyorder.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class PurchaseDeliveryBatchInfoDto {
    private Integer purchaseDeliveryDirectBatchInfoId;

    /**
     * 采购单ID
     */
    private Integer buyorderId;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 创建者
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Date modTime;

    /**
     * 更新者
     */
    private Integer updater;

    /**
     * 详情信息
     */
    private List<PurchaseDeliveryBatchInfoDto> list;

    /**
     * 同行单图片
     */
    private String[] fileUrls;

    /**
     * 当前是否上传同行单图片0 否1 是
     */
    private Integer isUploadFile;
}
