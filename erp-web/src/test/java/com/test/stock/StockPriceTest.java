package com.test.stock;

import com.vedeng.logistics.dao.WarehouseGoodsOperateLogMapper;
import com.vedeng.logistics.service.WarehousesService;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.model.BuyorderGoods;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName StockPriceTest.java
 * @Description TODO
 * @createTime 2020年12月28日 17:28:00
 */
public class StockPriceTest extends SpeicalWarehouseProcessor {

    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Resource
    private WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

    @Autowired
    private WarehousesService warehousesService;

    @Test
    public void test(){
        List<BuyorderGoods> buyorderGoodsList =	buyorderGoodsMapper.getAvgPriceByGoodsId(503646);
        BigDecimal totalPrice = BigDecimal.ZERO;
        int num = 0;
        for (BuyorderGoods buyorderGoods : buyorderGoodsList) {
            totalPrice = totalPrice.add(buyorderGoods.getTotalAmount());
            num += buyorderGoods.getNum();
        }
        BigDecimal totalNum = new BigDecimal(num);
        BigDecimal divide = totalPrice.divide(totalNum, 2, BigDecimal.ROUND_HALF_UP);
        System.out.println(divide);

        //获取外借出库时使用条码入库信息
//        List<WarehouseGoodsOperateLog> list = warehouseGoodsOperateLogMapper.getLendOutWhareHouserInList(84);
//        BigDecimal amount = BigDecimal.ZERO;
//        int totalNum = 0;
//        List<WarehouseGoodsOperateLog> list2 = new ArrayList<>();
//        //通过所有出库使用条码计算平均值
//        for (WarehouseGoodsOperateLog warehouseGoodsOperateLog : list) {
//            Integer operateType = warehouseGoodsOperateLog.getOperateType();
//            list2.add(warehouseGoodsOperateLog);
//            List<WarehouseGoodsOperateLog> priceInfoLogList = warehousesService.getPriceInfoLogList(list2, operateType);
//            if(CollectionUtils.isNotEmpty(priceInfoLogList)) {
//                WarehouseGoodsOperateLog log = priceInfoLogList.get(0);
//                amount = amount.add(log.getCostPrice());
//                totalNum++;
//            }
//            list2.clear();
//
//        }
//        BigDecimal costPrice = amount.divide(new BigDecimal(totalNum),2, BigDecimal.ROUND_HALF_UP);
//        System.out.println(costPrice);
//        List<WarehouseGoodsOperateLog> warehouseGoodsOperateLogList = warehouseGoodsOperateLogMapper.getWarehouseZKlog(StockOperateTypeConst.WAREHOUSE_IN);
//        List<WarehouseGoodsOperateLog> priceList = warehouseGoodsOperateLogMapper.getBuyOrderPrice(warehouseGoodsOperateLogList);
//        Map<Integer, BigDecimal> pricemap = priceList.stream().collect(Collectors.toMap
//                (WarehouseGoodsOperateLog::getRelatedId, WarehouseGoodsOperateLog::getCostPrice));
//        for (WarehouseGoodsOperateLog warehouseGoodsOperateLog : warehouseGoodsOperateLogList) {
//            BigDecimal cgprice = pricemap.get(warehouseGoodsOperateLog.getRelatedId());
//            if(cgprice == null || warehouseGoodsOperateLog.getWarehouseGoodsOperateLogId() == null){
//                continue;
//            }
//            warehouseGoodsOperateLog.setCostPrice(cgprice);
//            warehouseGoodsOperateLog.setNewCostPrice(cgprice);
//        }
    }
}
