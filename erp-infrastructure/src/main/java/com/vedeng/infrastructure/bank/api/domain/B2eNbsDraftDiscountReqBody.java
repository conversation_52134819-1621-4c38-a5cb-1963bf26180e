package com.vedeng.infrastructure.bank.api.domain;

import lombok.Data;

import java.util.List;

/**
 * @Description 贴现请求体
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/10/12
 */
@Data
public class B2eNbsDraftDiscountReqBody {

    /**
     * 客户技术请求流水号，同一客户请勿重复
     */
    private String trnId;

    /**
     * 客户技术请求流水号，同一客户请勿重复
     */
    private String insId;

    /**
     * 签约账号
     */
    private String custAccount;

    /**
     * 贴入人（贴现行）行内外标识
     */
    private String discountBank;

    /**
     * 贴入人（贴现行）账号
     */
    private String receiverAcctNo;

    /**
     * 贴入人（贴现行）行名
     */
    private String receiverBank;

    /**
     * 贴入人（贴现行）参与者机构代码
     */
    private String receiverBankCode;

    /**
     * 贴入人（贴现行）行号
     */
    private String receiverBankNo;

    /**
     * 入账（收款）账号
     */
    private String aoAcctNo;

    /**
     * 入账（收款）账户名称
     */
    private String aoAcctName;

    /**
     * 入账（收款）行号，与申请人开户行行号一致
     */
    private String aoBankNo;

    /**
     * 入账账号参与者代码
     */
    private String aoAcctBankCode;

    /**
     * 禁止背书标记
     */
    private String banEndrsmtMark;

    /**
     * 贴现类型
     */
    private String discType;

    /**
     * 结算方式
     */
    private String sttlmMk;

    /**
     * 贴现利率（0-99.9999）
     */
    private String discRate;

    /**
     * 贴现日:yyyyMMdd
     */
    private String discDt;

    /**
     * 付息方式
     */
    private String payType;

    /**
     * （协议付息比例，协议付息比例是指贴现人承担的比例）
     */
    private String buyPayPcet;

    /**
     * 备注
     */
    private String remark;

    /**
     * 赎回开放日，赎回式贴现选填
     */
    private String redeemOpenDt;

    /**
     * 赎回截止日，赎回式贴现选填
     */
    private String redeemDueDt;

    /**
     * 赎回利率，赎回式贴现选填
     */
    private String redeemRate;

    /**
     * 赎回利率类型，赎回式贴现选填
     */
    private String redeemRateType;

    /**
     * 赎回金额，赎回式贴现选填
     */
    private String redeemAmt;

    /**
     * 票据贴现列表
     */
    private List<BillDiscountMap> list;

    /**
     * 持有流水号和票据流水号
     */
    @Data
    public static class BillDiscountMap {

        /**
         * 票据（包）号码 30 票据信息 票号
         */
        private String billNo;

        /**
         * 子票区间起始 12 票据信息-子票区间起始
         */
        private String billRangeStart;

        /**
         * 子票区间截止 12 票据信息-子票区间截止
         */
        private String billRangeEnd;

        /**
         * 汇票到期:yyyyMMdd 10 票据信息 到期日
         */
        private String dueDt;

        /**
         * 交易金额(贴现金额) 12 票据信息 金额
         */
        private String transAmt;
    }
}