package com.vedeng.crm.visitrecord.domain.vo;

import lombok.*;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/2/28
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class VisitCustomerVo {

    /**
     * 客户名称
     */
    private String traderName;
    /**
     * 交易者ID
     */
    private Integer traderId;

    /** 客户ID */
    private Integer traderCustomerId;


    /**
     * （总交易额：150.00W）-交易类标签-历史交易总额
     */
    private String historyTransactionAmount;

    /**
     * （交易次数：24）-交易类标签-累计下单
     */
    private String historyTransactionNum;

    /**
     * （最近下单：2024-10-10）-交易类标签-最近下单时间
     */
    private String lastOrderTime;

    /**
     * （客户等级：）决策标签-客户等级
     */
    private String customerGrade;

    private Integer belongerId;

    private String belongerName;

    /**
     * 天眼查标识
     */
    private String tycFlag;

    /**
     * 返回给crm的前端erp的超链接
     */
    private String traderNameLink;

    /**
     * ERP内部链接
     */
    private String traderNameInnerLink;

    private boolean belong;

    private boolean share;




}
