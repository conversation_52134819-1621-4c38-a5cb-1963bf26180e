package com.vedeng.erp.kingdee.batch.chain;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.BatchRWarehouseGoodsOutJWarehouseGoodsInDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInItemDto;
import com.vedeng.erp.kingdee.batch.repository.BatchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInItemDtoMapper;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.logging.Log;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/01/15 20:54
 * @description 出入库单绑定公有方法
 */
@Slf4j
abstract public class CommonSaleBackOutInBindMethod {
    @Resource
    BatchWarehouseGoodsOutInDtoMapper batchWarehouseGoodsOutInDtoMapper;

    @Resource
    BatchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper batchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper;

    @Resource
    BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;


    /**
     * @description: 根据入库单或出库单商品找到入库单或出库单
     * @return:
     * @author: Suqin
     * @date: 2023/1/10 18:59
     **/
    public BatchWarehouseGoodsOutInDto getOutInOrder(BatchWarehouseGoodsOutInItemDto outInGoods) {
        BatchWarehouseGoodsOutInDto queryIn = new BatchWarehouseGoodsOutInDto();
        queryIn.setOutInNo(outInGoods.getOutInNo());
        List<BatchWarehouseGoodsOutInDto> oneQueryInOrder = batchWarehouseGoodsOutInDtoMapper.findByItem(queryIn);
        return oneQueryInOrder.get(0);
    }

    /**
     * @description: 返回一个退货出入库的关系表DTO
     * @param relateNo                关联单号（售后单号）
     * @param relatedId               关联产品id（售后产品GoodsId）（销售产品GoodsId）
     * @param WarehouseGoodsOutId     出库单id
     * @param WarehouseGoodsOutItemId 出库单详情id
     * @param WarehouseGoodsInId      入库单id
     * @param WarehouseGoodsInItemId  入库单详情id
     * @return:
     * @author: Suqin
     * @date: 2023/1/11 13:54
     **/
    public BatchRWarehouseGoodsOutJWarehouseGoodsInDto buildBaseDto(String relateNo, Integer relatedId, Long
            WarehouseGoodsOutId, Long WarehouseGoodsOutItemId, Long WarehouseGoodsInId, Long WarehouseGoodsInItemId) {
        BatchRWarehouseGoodsOutJWarehouseGoodsInDto bindOutIn = batchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper.findSkuByAfterSaleNo(relateNo, relatedId);
        bindOutIn.setWarehouseGoodsOutId(WarehouseGoodsOutId);
        bindOutIn.setWarehouseGoodsOutItemId(WarehouseGoodsOutItemId);
        bindOutIn.setWarehouseGoodsInId(WarehouseGoodsInId);
        bindOutIn.setWarehouseGoodsInItemId(WarehouseGoodsInItemId);
        bindOutIn.setRelationType(1);
        return bindOutIn;
    }

    /**
     * 绑定逻辑
     *
     * @param type        1:SN匹配 2:批次号匹配 3:无SN无批次号匹配
     * @param inGoodsNum  入库单商品可用数量
     * @param outGoodsNum 出库单商品可用数量
     * @param bindOutIn   待插入DTO
     * @param outGoods    当前出库单商品
     * @param isRemain    true：入库单商品数量 > 出库单商品数量的情况下，多余的入库单商品数量去匹配其他的出库单商品
     */
    public abstract void bind(int type, BigDecimal inGoodsNum, BigDecimal outGoodsNum,
                              BatchRWarehouseGoodsOutJWarehouseGoodsInDto bindOutIn,
                              BatchWarehouseGoodsOutInItemDto outGoods, boolean isRemain);


    /**
     * 找到对应销售单商品id的出库单商品，将其放在第一位去匹配
     *
     * @param inGoods
     * @param outItems
     */
    public void matchFirst(BatchWarehouseGoodsOutInItemDto inGoods, List<BatchWarehouseGoodsOutInItemDto> outItems) {
        if (CollUtil.isEmpty(outItems)) {
            return;
        }
        Integer orderDetailId = batchWarehouseGoodsOutInItemDtoMapper.getOrderDetailId(inGoods);
        List<BatchWarehouseGoodsOutInItemDto> collect = outItems.stream().filter(a -> a.getRelatedId().equals(orderDetailId)).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)){
            outItems.removeAll(collect);
            outItems.addAll(0, collect);
        }
    }

}
