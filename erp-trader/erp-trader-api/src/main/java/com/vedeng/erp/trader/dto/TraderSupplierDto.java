package com.vedeng.erp.trader.dto;

import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 供应商dto
 * @date 2022/5/24 9:52
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TraderSupplierDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer traderSupplierId;

    private Integer traderId;

    private String traderName;

    /**
     * 余额
     */
    private BigDecimal amount;

    /**
     * 账期额度
     */
    private BigDecimal periodAmount;

    private List<TraderFinanceDto> traderFinanceDtoList;
}
