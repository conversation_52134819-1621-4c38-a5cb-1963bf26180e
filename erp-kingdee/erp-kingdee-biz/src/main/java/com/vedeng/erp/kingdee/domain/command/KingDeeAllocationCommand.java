package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @description:调拨单
 * @author: yana.jiang
 * @date: 2022/11/10
 */
@Getter
@Setter
public class KingDeeAllocationCommand {

    /**
     * 单据内码
     */
    private String fId;
    /**
     * 单据编号
     */
    private String fBillNo;
    /**
     * 单据类型
     */
    private KingDeeNumberCommand fBillTypeId = new KingDeeNumberCommand();
    /**
     * 调拨方向
     */
    private String fTransferDirect;
    /**
     * 调拨类型
     */
    private String fTransferBizType;
    /**
     * 调出库存组织
     */
    private KingDeeNumberCommand fStockOutOrgId = new KingDeeNumberCommand();
    /**
     * fOwnerTypeOutIdHead
     */
    private String fOwnerTypeOutIdHead;
    /**
     * 调入库存组织
     */
    private KingDeeNumberCommand fStockOrgId = new KingDeeNumberCommand();
    /**
     * 单据日期
     */
    private String fDate;
    /**
     * 借调人
     */
    private String F_QZOK_JDR;
    /**
     * 贝登单据头ID
     */
    private String F_QZOK_BDDJTID;
    /**
     * fBillEntry
     */
    private List<KingDeeAllocationDetailCommand> fBillEntry;

    @Data
    public static class KingDeeAllocationDetailCommand {
        /**
         * fMaterialId
         */
        private KingDeeNumberCommand fMaterialId = new KingDeeNumberCommand();
        /**
         * 调拨数量
         */
        private String fQty;
        /**
         * 调出仓库
         */
        private KingDeeNumberCommand fSrcStockId = new KingDeeNumberCommand();
        /**
         * 调入仓库
         */
        private KingDeeNumberCommand fDestStockId = new KingDeeNumberCommand();
        /**
         * 借货的客户id
         */
        private String F_QZOK_JDKHID;
        /**
         * 原始订单号
         */
        private String F_QZOK_YSDDH;
        /**
         * 归属业务单号
         */
        private String F_QZOK_GSYWDH;
        /**
         * 业务类型
         */
        private String F_QZOK_YWLX;
        /**
         * 批次号
         */
        private String F_QZOK_PCH;
        /**
         * 序列号
         */
        private String F_QZOK_XLH;
    }
}
