package com.wms.inventorytransfer.service;

import com.alibaba.fastjson.JSON;
import com.rabbitmq.client.Channel;
import com.vedeng.common.trace.mq.AbstractMessageListener;
import com.wms.inventorytransfer.model.dto.InventoryTransferRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 库存转移单信息消费者
 *
 * <AUTHOR>
 * @date 2020/7/20 14:14:20
 */
@Component
public class InventoryTransferConsumer extends AbstractMessageListener {
    public static final Logger logger = LoggerFactory.getLogger(InventoryTransferConsumer.class);

    @Autowired
    private InventoryTransferService inventoryTransferService;

    @Override
    public void doBusiness(Message message, Channel channel) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        logger.info("库存转移单信息：{}", messageBody);

        try {
            InventoryTransferRequest inventoryTransferRequest = JSON.parseObject(messageBody, InventoryTransferRequest.class);
            if (inventoryTransferRequest == null || inventoryTransferRequest.getActionId() == null) {
                throw new Exception("库存转移单信息异常");
            }
            inventoryTransferService.saveInventoryTransferRequest(inventoryTransferRequest);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            logger.error("库存转移信息消费异常", e);
            try {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            } catch (IOException e1) {
                logger.error("ActionChangeMsgQueue", e);
            }
        }
    }
}
