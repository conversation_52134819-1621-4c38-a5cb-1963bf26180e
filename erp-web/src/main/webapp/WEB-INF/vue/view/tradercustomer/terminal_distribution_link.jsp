<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
            + path + "/";
%>
<c:set var="path" value="<%=basePath%>" scope="application" />
<link rel="stylesheet" href="<%=basePath%>static/css/general.css?rnd=${resourceVersionKey}" />
<link rel="stylesheet" href="<%=basePath%>static/css/manage.css" />
<%@ include file="../../../jsp/trader/customer/terminal_tag.jsp"%>
<div id="app" style="display: none;">
    <el-card class="box-card J-block">
        <div slot="header" class="clearfix">
            <span>经销链路明细</span>
        </div>
        <template>
            <el-form :inline="true" :model="distributionLink" :rules="searchRules" ref="form">
                <el-form-item label="来源">
                    <el-select v-model="distributionLink.linkSourceType" @change="handleSourceChange">
                        <el-option label="全部" value="0"></el-option>
                        <el-option label="中标数据" value="1"></el-option>
                        <el-option label="贝登交易" value="2"></el-option>
                        <el-option label="商机" value="3"></el-option>
                        <el-option label="报价" value="4"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="时间">
                    <el-select v-model="distributionLink.cooperationTimeFrame" @change="handleCooperationTimeChange">
                        <el-option label="近一年" value="1"></el-option>
                        <el-option label="近二年" value="2"></el-option>
                        <el-option label="近三年" value="3"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item :label="searchTraderLabel" prop="traderName">
                    <el-input v-model="distributionLink.traderName" :placeholder="searchTraderHolder" style="width: 400px"></el-input>
                </el-form-item>

                <el-form-item label="最新中标时间">
                    <el-date-picker
                            v-model="distributionLink.lastBiddingTime"
                            type="daterange"
                            ref="timeBox"
                            align="right"
                            unlink-panels
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            :default-time="['00:00:00', '23:59:59']"
                            :picker-options="setDateRange"
                    >
                    </el-date-picker>
                </el-form-item>

                <el-form-item label="最新交易时间">
                    <el-date-picker
                            v-model="distributionLink.lastSaleTime"
                            type="daterange"
                            ref="timeBox"
                            align="right"
                            unlink-panels
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            :default-time="['00:00:00', '23:59:59']"
                            :picker-options="setDateRange"
                    >
                    </el-date-picker>
                </el-form-item>
            </el-form>

            <el-row style="margin-bottom: 20px; text-align: center">
                <el-button type="primary" @click="submitForm()">搜索</el-button>
                <el-button type="primary" plain @click="reset()">重置</el-button>
            </el-row>

            <el-table
                    :data="distributionLinkList"
                    ref="distributionLinkTable"
                    border
                    style="width: 100%; "
                    @sort-change="sort_change"
                    key="traderContact">
                <el-table-column
                        align="center"
                        text-align="center"
                        :label="searchTraderLabel">
                    <template slot-scope="scope">
                        <el-row>{{scope.row.traderName || '-'}}</el-row>
                    </template>
                </el-table-column>
                <el-table-column label="中标情况" align="center" text-align="center">
                    <el-table-column
                            align="center"
                            text-align="center"
                            sortable="custom"
                            prop="biddingCount"
                            label="中标合作次数">
                        <template slot-scope="scope">
                            <span>{{scope.row.biddingCount || '-'}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            align="center"
                            text-align="center"
                            sortable="custom"
                            prop="lastBiddingTime"
                            label="最新中标时间">
                        <template slot-scope="scope">
                            <span>{{scope.row.lastBiddingTime ? parseTime(scope.row.lastBiddingTime, '{y}-{m}-{d}') : '-'}}</span>
                        </template>
                    </el-table-column>
                </el-table-column>
                <el-table-column label="交易情况"  align="center" text-align="center">
                    <el-table-column
                            align="center"
                            text-align="center"
                            sortable="custom"
                            prop="saleCount"
                            label="交易合作次数">
                        <template slot-scope="scope">
                            <span>{{scope.row.saleCount || '-'}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            align="center"
                            text-align="center"
                            sortable="custom"
                            prop="businessChanceCount"
                            label="商机次数">
                        <template slot-scope="scope">
                            <span>{{scope.row.businessChanceCount || '-'}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            align="center"
                            text-align="center"
                            sortable="custom"
                            prop="quoteCount"
                            label="报价次数">
                        <template slot-scope="scope">
                            <span>{{scope.row.quoteCount || '-'}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            align="center"
                            text-align="center"
                            sortable="custom"
                            prop="lastSaleTime"
                            label="最新交易时间">
                        <template slot-scope="scope">
                            <span>{{scope.row.lastSaleTime ? parseTime(scope.row.lastSaleTime, '{y}-{m}-{d}') : '-'}}</span>
                        </template>
                    </el-table-column>
                </el-table-column>
            </el-table>

            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPageNo"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="currentSize"
                    layout="->,total, sizes, prev, pager, next, jumper"
                    :total="totalLines">
            </el-pagination>
        </template>
    </el-card>
</div>

<script src="${pageContext.request.contextPath}/static/api/trader/traderCustomer.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/js/d3.js" charset="utf-8"></script>

<script type="text/javascript">
    const searchName = '${searchName}';
    let vm = new Vue({
        el: '#app',
        data() {
            return {
                currentSize: 10,
                currentPageNo: 1,
                totalLines: 0,
                distributionLinkList:[],
                distributionLink: {
                    searchName : '',
                    linkSourceType: '0',
                    cooperationTimeFrame: '3',
                    traderName: '',
                    lastBiddingTime: [],
                    lastSaleTime: []
                },

                // 排序字段，排序方式,默认按照 交易次数降序排列
                sortColumn: 6,
                sortType: 1,

                searchTraderLabel: '合作经销商名称',
                searchTraderHolder: '请输入经销商名称',

                searchRules: {
                    traderName: [
                        {max: 50, message: "最多输入50个字符", trigger: "blur"}]
                },

                setDateRange: {
                    disabledDate: this.disabledDate
                }
            }
        },

        mounted() {
            loadingApp();
        },

        created() {
            this.doSearch();
        },

        methods: {

            submitForm() {
                this.$refs.form.validate(valid => {
                    if(valid) {
                        this.currentPageNo = 1;
                        this.doSearch();
                    } else {
                        return false;
                    }
                })
            },

            // 重置按钮
            reset() {
                this.distributionLink.linkSourceType = '0';
                this.distributionLink.cooperationTimeFrame = '3';
                this.distributionLink.traderName = '';
                this.distributionLink.lastBiddingTime = [];
                this.distributionLink.lastSaleTime = [];
                this.sortColumn = 6;
                this.sortType = 1;
                this.$refs.distributionLinkTable.clearSort();
                this.$nextTick(() => {
                    this.doSearch();
                });
            },

            // 表头排序回调函数
            sort_change({ column }) {
                var sort = column.order;
                var property = column.property;
                if (!sort) {
                    this.handleSourceChange(this.distributionLink.linkSourceType);
                } else {
                    if (sort == "descending") {
                        this.sortType = 1;
                    }
                    if (sort == "ascending") {
                        this.sortType = 0;
                    }

                    switch(property)
                    {
                        case "biddingCount":
                            this.sortColumn = 1;
                            break;
                        case "saleCount":
                            this.sortColumn = 2;
                            break;
                        case "businessChanceCount":
                            this.sortColumn = 3;
                            break;
                        case "quoteCount":
                            this.sortColumn = 4;
                            break;
                        case "lastBiddingTime":
                            this.sortColumn = 5;
                            break;
                        case "lastSaleTime":
                            this.sortColumn = 6;
                            break;
                        default:
                            this.sortColumn = 0;
                    }
                }
                this.doSearch();
            },

            // 来源选项变化后，需要重置排序规则
            handleSourceChange(val) {
                this.sortType = 1;
                if (val == 0) {
                    // 全部按照最新交易时间降序排列
                    this.sortColumn = 6;
                } else {
                    // 其他来源
                    this.sortColumn = val;
                }
            },

            doSearch() {
                var pageParam = {
                    "pageSize": this.currentSize,
                    "pageNum": this.currentPageNo,
                    "param": {
                        searchName : searchName,
                        linkSourceType: this.distributionLink.linkSourceType,
                        cooperationTimeFrame: this.distributionLink.cooperationTimeFrame,
                        traderName: this.distributionLink.traderName,
                        // elementui clear选中时间后，会把绑定对象置为null
                        lastBiddingTimeStart: this.distributionLink.lastBiddingTime == null ? null : this.distributionLink.lastBiddingTime[0],
                        lastBiddingTimeEnd: this.distributionLink.lastBiddingTime == null ? null : this.distributionLink.lastBiddingTime[1],
                        lastSaleTimeStart: this.distributionLink.lastSaleTime == null ? null : this.distributionLink.lastSaleTime[0],
                        lastSaleTimeEnd: this.distributionLink.lastSaleTime == null ? null : this.distributionLink.lastSaleTime[1],
                        sortColumn: this.sortColumn,
                        sortType: this.sortType
                    }

                };

                getTerminalDistributionLink(pageParam).then(res => {
                    this.distributionLinkList = res.data.data.list;
                    this.totalLines = res.data.data.total;
                })
            },

            handleSizeChange(val) {
                this.currentSize = val;
                this.currentPageNo = 1;
                this.doSearch();
            },
            handleCurrentChange(val) {
                this.currentPageNo = val;
                this.doSearch();
            },

            handleCooperationTimeChange() {
                // 时间改变后，清空日期组件的选中值
                this.distributionLink.lastBiddingTime = [];
                this.distributionLink.lastSaleTime = [];
            },

            // 改变日期可选范围
            disabledDate(time) {

                let nowDate = (new Date()).getTime();

                var today = new Date();
                today.setHours(23);
                today.setMinutes(59);
                today.setSeconds(59);

                if (this.distributionLink.cooperationTimeFrame == '3') {
                    let three = 1097 * 24 * 3600 * 1000;
                    let threeYears = nowDate - three;
                    return time.getTime() > today.getTime() || time.getTime() < threeYears;
                }

                if (this.distributionLink.cooperationTimeFrame == '2') {
                    let two = 732 * 24 * 3600 * 1000;
                    let twoYears = nowDate - two;
                    return time.getTime() > today.getTime() || time.getTime() < twoYears;
                }

                if (this.distributionLink.cooperationTimeFrame == '1') {
                    let one = 367 * 24 * 3600 * 1000;
                    let oneYears = nowDate - one;
                    return time.getTime() > today.getTime() || time.getTime() < oneYears;
                }
            }
        }
    })
</script>


<style>
    .el-card__header {
        height: 34px;
        background-color: #c5ddfb;
        box-sizing: border-box;
        padding-left: 15px;
        font-weight: 700;
        padding-top: 9px;
        padding-bottom: 30px;
        color: #303133;
    }

    .el-input--mini .el-input__icon {
        line-height: 0;
    }

    .el-pagination .btn-next .el-icon, .el-pagination .btn-prev .el-icon {
        margin-right: 5px;
    }

    .el-date-editor .el-range__icon {
        display: contents;
    }

    .el-date-editor .el-range__close-icon{
        display: contents;
    }

    .el-date-editor .el-range-separator {
        width: 10%;
    }

    .el-select .el-input .el-select__caret {
        display: none;
    }
</style>