package com.vedeng.kpi.service.impl;

import com.vedeng.kpi.share.KpiCommonConstant;
import com.vedeng.kpi.model.DO.KpiDailyCountDo;
import com.vedeng.kpi.model.DTO.KpiDataQueryDto;
import com.vedeng.kpi.model.DTO.KpiUserInfoDto;
import com.vedeng.kpi.service.KpiBaseService;
import com.vedeng.kpi.service.KpiCustomerService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 五行客户处理.
 * @jira: VDERP-2376【五行剑法】规则修改.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/7/9 10:13 上午.
 * @author: Tomcat.Hui.
 */
@Service
public class KpiCustomerServiceImpl extends KpiBaseService implements KpiCustomerService{

    @Override
    public void dataHandler(KpiUserInfoDto userInfo) {

        String kpiDateStr = getDateStr(userInfo.getKpiDateEnd());
        log.info("开始更新用户 " + userInfo.getUserIdName() + " " + kpiDateStr + " 日 " +
                KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_KH);

        KpiDataQueryDto query = kpiParamTransService.baseParamsToQuery(userInfo);
        KpiDailyCountDo kpiDailyCount = kpiParamTransService.baseParamsToCount(userInfo);

        query.setUserIds(kpiLoadingCache.getGroupUserIdsByUserId(userInfo.getUserId()));

        log.info("开始查询用户 " + userInfo.getUserIdName() + " " + kpiDateStr + " 日 " + KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_KH + " 团队数据");
        query.setDays(90);
        List<KpiDataQueryDto> groupCoCustomers = getGroupNewCoCustomersMonth();

        /** coOrderNum取值:
         * 取log表同一用户同一客户同一订单的operation类型为 1 or 3 的订单正负相减再求和
         * 如果求和后大于0 表示该用户未完全流失 否则为流失
         * */
        Map<Boolean,Long> groupIsAllReturn = groupCoCustomers.stream()
                .filter(u -> u.getUserId().equals(userInfo.getUserId()))
                .collect(Collectors.groupingBy(u -> u.getCoOrderNum() > 0,Collectors.counting()));

        kpiDailyCount.setCoCustomerNum(Optional.ofNullable(groupIsAllReturn.get(true)).orElse(0L).intValue());
        kpiDailyCount.setLostCustomerNum(Optional.ofNullable(groupIsAllReturn.get(false)).orElse(0L).intValue());

        log.info("用户 " + userInfo.getUserIdName() + KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_KH + " 合作/流失客户: "
                + kpiDailyCount.getCoCustomerNum() + "/" + kpiDailyCount.getLostCustomerNum());
        kpiDailyCount.setCustomerScore(new BigDecimal(countCurrentUserScore(userInfo,kpiDailyCount,groupCoCustomers)));
        insertOrUpdateCount(kpiDailyCount);
        log.info("更新用户 " + userInfo.getUserIdName() + " " + kpiDateStr + " 日 "
                + KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_KH + " 结束");
    }

    /**
     * 计算个人得分.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/6/8 1:42 上午.
     * @author: Tomcat.Hui.
     * @param userInfo: .
     * @param groupCoCustomers: .
     * @return: java.lang.Double.
     * @throws: .
     */
    protected Double countCurrentUserScore(KpiUserInfoDto userInfo,
                                           KpiDailyCountDo kpiDailyCount,
                                           List<KpiDataQueryDto> groupCoCustomers) {
        try {
            log.info("开始计算用户 " + userInfo.getUserIdName() + " " + KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_KH + " 得分");
            BigDecimal weight = getWeight(userInfo.getGroupId(),KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_KH);

            log.info("用户 " + userInfo.getUserIdName() + KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_KH + " 所属团队 "
                    + userInfo.getGroupName() + " "
                    + getDateStr(userInfo.getKpiDateEnd()) + " 月团队权重 " + weight);

            Map<Integer,Integer> groupCustomers = groupCoCustomers.stream()
                    .collect(
                            Collectors.groupingBy(KpiDataQueryDto::getUserId,
                                    Collectors.summingInt(x -> x.getCoOrderNum() > 0 ? 1 : -1))
                    );
            Integer groupTotal = groupCustomers.entrySet().stream().mapToInt(Map.Entry::getValue).sum();
            log.info("用户 " + userInfo.getUserIdName() + " " + KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_KH + " 团队总客户数 " + groupTotal);

            Double score = 0.0;
            if (new BigDecimal(groupTotal).compareTo(BigDecimal.ZERO) != 0) {
                score = new Double(kpiDailyCount.getCoCustomerNum() - kpiDailyCount.getLostCustomerNum())
                        / (
                        new Double(groupTotal) /
                                kpiLoadingCache.getGroupConfig(userInfo.getGroupId())
                                        .getTeams().stream()
                                        .flatMap(t -> t.getUsers().stream()).count()) * weight.doubleValue();
            }

            log.info("用户 " + userInfo.getUserIdName() + " " + KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_KH + " 个人得分 " + score);
            return score;
        }catch (Exception e){
            log.error("用户 " + userInfo.getUserIdName() + " " + KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_KH + " 计算个人得分出现异常: ",e);
            throw e;
        }
    }

    @Override
    public List<KpiDataQueryDto> getGroupCoCustomers(KpiDataQueryDto query) {
        return kpiOrderLogMapper.getGroupCoCustomers(query);
    }

    @Override
    public List<KpiDataQueryDto> getUserCoCustomers(KpiDataQueryDto query) {
        return kpiOrderLogMapper.getUserCoCustomers(query);
    }

    @Override
    public List<KpiDataQueryDto> getUserCoCustomersDetail(KpiDataQueryDto query) {
        return kpiOrderLogMapper.getUserCoCustomersDetail(query);
    }

    @Override
    public List<KpiDataQueryDto> getTraderLastTimes(KpiDataQueryDto query) {
        List<KpiDataQueryDto> result = kpiOrderLogMapper.getTraderLastTimes(query);
        result.stream().forEach(t -> {
            if (null != t.getLastChatTime()) {
                Long diff = System.currentTimeMillis() - t.getLastChatTime();
                t.setChatApartDays(diff / (60 * 60 * 24 * 1000));
            }
        });
        return result;
    }
}
