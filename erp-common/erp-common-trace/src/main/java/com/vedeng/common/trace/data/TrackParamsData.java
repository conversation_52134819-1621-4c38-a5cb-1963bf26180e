package com.vedeng.common.trace.data;

import com.vedeng.common.trace.enums.EventTrackingEnum;
import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * 埋点的数据对象
 * @ClassName:  TrackParamsData
 * @author: <PERSON><PERSON>yang
 * @date:   2024年6月3日 下午3:18:38
 * @Copyright:
 */
@Data
public class TrackParamsData {

    /**参数*/
    private Map<String,Object> trackParams;
    
    /**事件发生事件*/
    private Date trackTime;
    
    /**返回值*/
    private Object trackResult;
    
    /**埋点枚举类*/
    private EventTrackingEnum eventTrackingEnum;
    
}
