package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @description:盘盈单
 * @author: yana.jiang
 * @date: 2022/11/10
 */
@Data
@Getter
@Setter

public class KingDeeInventoryProfitCommand {

    /**
     * 单据内码
     */
    private String fId;
    /**
     * 单据类型
     */
    private KingDeeNumberCommand fBillTypeId = new KingDeeNumberCommand();
    /**
     * 单据编号
     */
    private String fBillNo;
    /**
     * 贝登单据头ID
     */
    private String f_QZOK_BDDJTID;
    /**
     * 库存组织
     */
    private KingDeeNumberCommand fStockOrgId = new KingDeeNumberCommand();
    /**
     * 货主类型
     */
    private String fOwnerTypeIdHead;
    /**
     * 单据日期
     */
    private String fDate;
    /**
     * fDeptId
     */
    private KingDeeNumberCommand fDeptId = new KingDeeNumberCommand();
    /**
     * fBillEntry
     */
    private List<KingDeeInventoryProfitDetailCommand> fBillEntry;
    @Data
    public static class KingDeeInventoryProfitDetailCommand {
        /**
         * fMaterialId
         */
        private KingDeeNumberCommand fMaterialId = new KingDeeNumberCommand();
        /**
         * fStockId
         */
        private KingDeeNumberCommand fStockId = new KingDeeNumberCommand();
        /**
         * fStockStatusId
         */
        private KingDeeNumberCommand fStockStatusId = new KingDeeNumberCommand();
        /**
         * 盘盈数量填写数量
         */
        private String fBaseGainQty;
        /**
         * 成本价
         */
        private String fPrice;
        /**
         * 总成本
         */
        private String fAmount;
        /**
         * 原始订单号
         */
        private String F_QZOK_YSDDH;
        /**
         * 归属业务单号
         */
        private String F_QZOK_GSYWDH;
        /**
         * 业务类型
         */
        private String F_QZOK_YWLX;
        /**
         * 批次号
         */
        private String F_QZOK_PCH;
        /**
         * 序列号
         */
        private String F_QZOK_XLH;
    }
}
