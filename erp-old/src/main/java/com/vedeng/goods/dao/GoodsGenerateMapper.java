package com.vedeng.goods.dao;

import com.vedeng.goods.model.GoodsGenerate;
import com.vedeng.goods.model.GoodsGenerateExample;
import com.vedeng.goods.model.GoodsGenerateWithBLOBs;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface GoodsGenerateMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    int countByExample(GoodsGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    int deleteByExample(GoodsGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    int deleteByPrimaryKey(Integer goodsId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    int insert(GoodsGenerateWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    int insertSelective(GoodsGenerateWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    List<GoodsGenerateWithBLOBs> selectByExampleWithBLOBs(GoodsGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    List<GoodsGenerate> selectByExample(GoodsGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    GoodsGenerateWithBLOBs selectByPrimaryKey(Integer goodsId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    int updateByExampleSelective(@Param("record") GoodsGenerateWithBLOBs record, @Param("example") GoodsGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    int updateByExampleWithBLOBs(@Param("record") GoodsGenerateWithBLOBs record, @Param("example") GoodsGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    int updateByExample(@Param("record") GoodsGenerate record, @Param("example") GoodsGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    int updateByPrimaryKeySelective(GoodsGenerateWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    int updateByPrimaryKeyWithBLOBs(GoodsGenerateWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    int updateByPrimaryKey(GoodsGenerate record);

    /**
     * 更新T_GOODS的禁用/启用状态
     */
    void updateGoodsDiscard(@Param("sku") String sku, @Param("isDiscard") Integer isDiscard);
}