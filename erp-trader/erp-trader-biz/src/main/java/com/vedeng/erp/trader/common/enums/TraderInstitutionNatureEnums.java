package com.vedeng.erp.trader.common.enums;


import com.vedeng.common.core.exception.ServiceException;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 机构性质
 * @date 2021/12/7 9:59
 */
public enum TraderInstitutionNatureEnums {

    /**
     * 业务类型
     */
    BLANK("-1", ""),

    PUBLIC("0", "公立"),

    NO_PUBLIC("1", "非公");


    private final String type;

    private final String label;


    TraderInstitutionNatureEnums(String type, String label) {
        this.type = type;
        this.label = label;
    }

    public String getType() {
        return type;
    }

    public String getLabel() {
        return label;
    }

    /**
     * 机构性质
     *
     * @param type 业务类型
     * @return ElectronicSignBusinessEnums
     */
    public static TraderInstitutionNatureEnums getEnum(String type) {
        return Arrays.stream(TraderInstitutionNatureEnums.values())
                .filter(enums -> enums.getType().equals(type))
                .findFirst().orElseThrow(() -> new ServiceException("无法获取对应的机构性质枚举"));
    }

    /**
     * 获取机构性质名称
     *
     * @param type 业务类型
     * @return ElectronicSignBusinessEnums
     */
    public static String getEnumName(String type) {
        return Arrays.stream(TraderInstitutionNatureEnums.values())
                .filter(enums -> enums.getType().equals(type))
                .findFirst().orElse(TraderInstitutionNatureEnums.BLANK).getLabel();
    }

}
