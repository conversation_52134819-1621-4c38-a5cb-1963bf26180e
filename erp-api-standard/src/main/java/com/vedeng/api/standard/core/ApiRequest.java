package com.vedeng.api.standard.core;

import com.vedeng.authorization.model.User;
import java.io.Serializable;
import java.util.Map;

/**
 * 统一API请求格式类
 * 封装所有API请求的通用信息
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-18
 */
public class ApiRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 请求ID，用于链路追踪
     */
    private String requestId;
    
    /**
     * 模块名称（如：buyorder、saleorder等）
     */
    private String module;
    
    /**
     * 操作名称（如：create、update、delete等）
     */
    private String action;
    
    /**
     * 请求时间戳
     */
    private Long timestamp;
    
    /**
     * 当前登录用户
     */
    private User currentUser;
    
    /**
     * 请求数据
     */
    private Map<String, Object> data;
    
    /**
     * 请求头信息
     */
    private Map<String, String> headers;
    
    /**
     * 客户端IP地址
     */
    private String clientIp;
    
    /**
     * 用户代理信息
     */
    private String userAgent;
    
    /**
     * API版本号
     */
    private String apiVersion;
    
    /**
     * 默认构造函数
     */
    public ApiRequest() {
        this.timestamp = System.currentTimeMillis();
        this.apiVersion = "v1";
    }
    
    /**
     * 构造函数
     */
    public ApiRequest(String module, String action, Map<String, Object> data) {
        this();
        this.module = module;
        this.action = action;
        this.data = data;
    }
    
    /**
     * 获取请求的完整路径
     */
    public String getFullPath() {
        return String.format("/api/%s/%s/%s", apiVersion, module, action);
    }
    
    /**
     * 判断是否为有效请求
     */
    public boolean isValid() {
        return module != null && !module.trim().isEmpty() 
            && action != null && !action.trim().isEmpty()
            && currentUser != null;
    }
    
    // Getter and Setter methods
    public String getRequestId() {
        return requestId;
    }
    
    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }
    
    public String getModule() {
        return module;
    }
    
    public void setModule(String module) {
        this.module = module;
    }
    
    public String getAction() {
        return action;
    }
    
    public void setAction(String action) {
        this.action = action;
    }
    
    public Long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }
    
    public User getCurrentUser() {
        return currentUser;
    }
    
    public void setCurrentUser(User currentUser) {
        this.currentUser = currentUser;
    }
    
    public Map<String, Object> getData() {
        return data;
    }
    
    public void setData(Map<String, Object> data) {
        this.data = data;
    }
    
    public Map<String, String> getHeaders() {
        return headers;
    }
    
    public void setHeaders(Map<String, String> headers) {
        this.headers = headers;
    }
    
    public String getClientIp() {
        return clientIp;
    }
    
    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }
    
    public String getUserAgent() {
        return userAgent;
    }
    
    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }
    
    public String getApiVersion() {
        return apiVersion;
    }
    
    public void setApiVersion(String apiVersion) {
        this.apiVersion = apiVersion;
    }
    
    @Override
    public String toString() {
        return "ApiRequest{" +
                "requestId='" + requestId + '\'' +
                ", module='" + module + '\'' +
                ", action='" + action + '\'' +
                ", timestamp=" + timestamp +
                ", currentUser=" + (currentUser != null ? currentUser.getUsername() : "null") +
                ", clientIp='" + clientIp + '\'' +
                ", apiVersion='" + apiVersion + '\'' +
                '}';
    }
}
