package com.vedeng.erp.saleorder.dao;

import com.vedeng.erp.saleorder.model.dto.CustomerInvoiceDto;
import com.vedeng.erp.saleorder.model.dto.OrderInvoiceInfo;

/**
 * 订单在线开票mapper
 *
 * <AUTHOR>
 */
public interface OnlineInvoiceOpenMapper {

    /**
     * 订单号获取订单开票信息
     *
     * @param orderNo
     * @return
     */
    OrderInvoiceInfo getInvoiceOpenInfoByOrderNo(String orderNo);

    /**
     * 订单号获取售后单数量
     * @param orderNo
     * @return
     */
    int getAfterSalesNumByOrderNum(String orderNo);

    /**
     * 订单正在开票申请中的数量
     *
     * @param orderNo
     * @return
     */
    int getOrderInvoiceApplyNumByOrderNo(String orderNo);

    /**
     * 订单号获取发票状态信息
     * @param orderNo
     * @return
     */
    int getOrderInvoiceStatusByOrderNo(String orderNo);

    /**
     * 订单是否锁定校验
     * @param orderNo
     * @return
     */
    int getOrderIsLockByOrderNo(String orderNo);

    /**
     * 订单获取未完结订单修改单数量
     *
     * @param orderNo
     * @return
     */
    int getOrderModifyNumByOrderNo(String orderNo);

    /**
     * 订单号获取客户资质信息
     * @param orderNo
     * @return
     */
    CustomerInvoiceDto getCustomerInvoiceInfoByOrderNo(String orderNo);

}
