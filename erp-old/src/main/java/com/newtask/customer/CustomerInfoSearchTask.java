package com.newtask.customer;

import com.newtask.customer.service.CustomerInfoSearchService;
import com.vedeng.common.redis.RedisKeyUtils;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.MapUtils;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> [<EMAIL>]
 */
@JobHandler(value = "customerInfoSearchTask")
@Component
public class CustomerInfoSearchTask extends AbstractJobHandler implements InitializingBean, ApplicationContextAware {

    private final static Logger LOGGER = LoggerFactory.getLogger(CustomerInfoSearchTask.class);

    private static final String RECORDS_KEY = RedisKeyUtils.createKey("customer_update_count");

    private static final String HAS_QUOTED_KEY_NAME = "lastHasQuotedColumnCount";
    private static final String IS_COOPERATED_KEY_NAME = "lastIsCooperatedColumnCount";

    private static final Integer ZERO = 0;

    private ConfigurableApplicationContext applicationContext;

    private JdbcTemplate jdbcTemplate;

    @Resource
    private CustomerInfoSearchService customerInfoSearchService;

    private static final ThreadLocal<Map<String, Integer>> CONTEXT = new ThreadLocal<>();

    @Resource
    private RedissonClient redissonClient;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        RMap<String, Integer> recordsMap = redissonClient.getMap(RECORDS_KEY);

        CONTEXT.set(recordsMap.readAllMap());
        boolean changed = false;

        //默认查5分钟数据
        Long startTime = System.currentTimeMillis()-5*60*1000;
        Long endTime = System.currentTimeMillis();
        if(!StringUtils.isEmpty(param)){
            String [] splitTime =  param.split("-");
            if(splitTime.length>=2){
                startTime =Long.parseLong(splitTime[0]) ;
                endTime =Long.parseLong(splitTime[1]) ;
            }

        }
        try {
            changed |= updateHasQuotedColumnIfNecessary(startTime,endTime);
            changed |= updateIsCooperatedColumnIfNecessary(startTime,endTime);
        } finally {

            if (changed) {
                recordsMap.putAll(CONTEXT.get());
            }

            CONTEXT.set(null);
        }

        return ReturnT.SUCCESS;
    }


    private boolean updateHasQuotedColumnIfNecessary(Long startTime,Long endTime) {
        Integer lastHasQuotedColumnCount = obtainLastUpdateCountFromContext(HAS_QUOTED_KEY_NAME);
        List<Integer> countToUpdate = customerInfoSearchService.getTraderIdByTime(startTime, endTime);
        boolean changed = false;
        if(CollectionUtils.isEmpty(countToUpdate)){
            setValueIntoContext(HAS_QUOTED_KEY_NAME, countToUpdate.size());
           return changed;
        }

        try {
            int updated = customerInfoSearchService.updateHasQuoted(countToUpdate);
                LOGGER.info("Updated rows of T_TRADER_CUSTOMER count: {}", updated);
            changed = true;

        } catch (Exception e) {
            LOGGER.error("错误日志",e);
            XxlJobLogger.log("同步T_TRADER_CUSTOMER字段'HAS_QUOTED'信息时发生错误, error:{}", e);
        }

        setValueIntoContext(HAS_QUOTED_KEY_NAME, countToUpdate.size());

        return changed;
    }

    private boolean updateIsCooperatedColumnIfNecessary(Long startTime,Long endTime) {
        Integer lastIsCooperatedColumnCount = obtainLastUpdateCountFromContext(IS_COOPERATED_KEY_NAME);
        List<Integer> countToUpdate = customerInfoSearchService.getTraderIdByTimeAndValidStatus(startTime,endTime);
        boolean changed = false;
        if(CollectionUtils.isEmpty(countToUpdate)){
            setValueIntoContext(HAS_QUOTED_KEY_NAME, countToUpdate.size());
            return changed;
        }
        try {
            int updated =customerInfoSearchService.updateIsCooperated(countToUpdate);
                LOGGER.info("Updated rows of T_TRADER_CUSTOMER count: {}", updated);

            changed = true;

        } catch (Exception e) {
            LOGGER.error("错误日志",e.toString());
            XxlJobLogger.log("同步T_TRADER_CUSTOMER字段'IS_COOPERATED'信息时发生错误, error:{}", e);
        }

        setValueIntoContext(IS_COOPERATED_KEY_NAME, countToUpdate.size());

        return changed;
    }


    private boolean needToProceedBatchUpdate(Integer countToUpdate, Integer lastUpdateCount) {
        return !Objects.equals(countToUpdate, lastUpdateCount);
    }

    private Integer obtainLastUpdateCountFromContext(String key) {
        return MapUtils.getInteger(CONTEXT.get(), key, ZERO);
    }

    private Integer setValueIntoContext(String key, Integer count) {
        return CONTEXT.get().put(key, count);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = (ConfigurableApplicationContext) applicationContext;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        DataSource dataSource = applicationContext.getBean("dataSource", DataSource.class);
        Objects.requireNonNull(dataSource, "dataSource is null");
        jdbcTemplate = new JdbcTemplate(dataSource);
    }

}
