package com.vedeng.erp.aftersale.domain.dto;

import com.vedeng.aftersales.model.*;
import com.vedeng.aftersales.model.vo.*;
import com.vedeng.authorization.model.User;
import com.vedeng.finance.model.CapitalBill;
import com.vedeng.finance.model.Invoice;
import com.vedeng.finance.model.PayApply;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.order.model.Buyorder;
import com.vedeng.system.model.Attachment;
import com.vedeng.trader.model.TraderContact;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AfterSaleDto {
    private Integer afterSalesId;

    private String afterSalesNo;

    private Integer companyId;

    private Integer subjectType;

    private Integer type;

    private Integer orderId;

    private String orderNo;

    private Integer serviceUserId;

    private Integer validStatus;

    private Long validTime;

    private Integer status;

    private Integer atferSalesStatus;

    private Long addTime;

    private Integer creator;

    private Long modTime;

    private Integer updater;

    private Integer flag;//标识

    private String pickNums;// 订单的拣货数+每个批次拣货数

    private String idCnt;// 扫码出库值

    private Integer businessType;//业务类型

    private AfterSalesDetail afterSalesDetail;

    private String verifyUsername;//当前审核人

    private Integer verifyStatus;//审核状态

    private String area,address;

    // begin add by franlin at 2018-05-09 for 用于区分页面场景：通用页面场景为0
    private Integer scenesPageType = 0;
    // end add by franlin at 2018-05-09 for 用于区分页面场景：通用页面场景为0

    /***2018-7-31***/
    private	Integer afterSalesStatusReson;//售后订单关闭原因Id

    private List<AfterSalesTrader> afterSalesTraderList;

    private	String afterSalesStatusResonName;//售后订单关闭原因

    private	Integer afterSalesStatusUser;//售后订单关闭人

    private	String afterSalesStatusUserName;//售后订单关闭人姓名

    private	String afterSalesStatusComments;//售后订单关闭备注
    /***2018-07-31 end ***/

    /*** 2018-9-5 新增开票/收票状态***/
    private Integer invoiceStatus;//开票状态

    private Long invoiceTime;//开票时间

    private Integer receiveInvoiceStatus;//收票状态

    private Long receiveInvoiceTime;//收票时间
    /*** END ***/

    private Integer firstValidStatus; //首次审核状态0待审核1审核通过2审核不通过

    private Long firstValidTime; //首次审核时间

    private Integer firstValidUser; //首次审核人ID

    private String firstValidComments; //首次审核原因

    private Integer source; //售后来源 0:ERP 1:耗材 2：贝登前台
    /**活动id*/
    private Integer actionId;

    /**
     * 所有的产品afterSalesGoodsIds的集合
     */
    private String afterSalesGoodsIds;

    private Integer invoiceSendStatus;//售后丢票寄送状态
    private Integer invoiceArrivalStatus;//售后发票收货状态

    private Integer isOutAfter;//是否可以出库(1、可以  2 不可以)

    /**
     * 是否闪电换货
     * @return
     */
    private transient Integer isLightning;

    /**
     *前台创建售后单登陆用户名
     */
    private String createFrontEndUser;

    /**
     *前台关闭售后单登陆用户名
     */
    private String closeFrontEndMobile;

    /**
     * 处理状态 0无处理状态 1未处理 2部分处理 3全部处理
     */
    private Integer handleStatus;

    /**
     * 退票状态 '退票状态 0无退票状态 1未退票 2部分退票 3全部退票
     */
    private Integer invoiceRefundStatus;

    /**
     * 退款状态 0无退款状态 1未退款 2部分退款 3全部退款 (用于筛选)
     */
    private Integer amountRefundStatus;

    /**
     * 收款状态: 0无收款状态 1未收款 2部分收款 3全部收款
     */
    private Integer amountCollectionStatus;

    /**
     * 付款状态：0无付款状态 1未付款 2部分付款 3全部付款
     */
    private Integer amountPayStatus;

    /**
     * 开票状态：0无开票 1未开票 2全部开票
     */
    private Integer invoiceMakeoutStatus;

    /**
     * 是否订单流新订单，0否1是
     */
    private Integer isNew;

    /**
     * 直发采购售后关联销售售后ID
     */
    private Integer deliveryDirectAfterSalesId;

    private static final long serialVersionUID = 1L;



    private String typeName;// 业务类型名称

    private Integer timeType;// 时间类型：1-申请时间；2-生效时间

    private Long searchStartTime;// 搜索开始时间

    private Long searchEndTime;// 搜索结束时间

    private String starttime;// 搜索开始时间

    private String endtime;// 搜索结束时间

    private List<Integer> serviceUserIdList;//归属售后人员集合

    private String creatorName;//创建人

    private String serviceUserName;//售后人员

    private Integer isView;//列表页是否能查看详情：1-是；0-否

    private Integer reason;//售后原因

    private String comments;//详情说明

    private Integer traderContactId;//联系人id

    private Integer refund;//款项退还

    private String refundComment; //款项退还信息备注

    private String [] afterSalesNum;//退货数量拼接字符串（saleorderGoodsId+num+deliveryDirect）逗号隔开

    private String [] attachName;//附件名称，逗号隔开

    private String [] attachUri;//附件uri,逗号隔开

    private String domain;//域名

    private String takeMsg;//收货信息（traderAddressI|areaId|tav.area|address）

    private Integer province;//省

    private Integer city;//市

    private Integer zone;//区



    private String [] invoiceIds;//退票id集合

    private String reasonName;//售后原因名称

    //以下为售后详情类---开始
    private Integer afterSalesDetailId;

    private String traderName;//客户名称

    private String goodsName;//产品名称

    private String model;//型号

    private String brandName,unitName;// 品牌名称

    private String materialCode;//物料编码

    private BigDecimal serviceAmount;

    private BigDecimal paymentAmount;

    private Integer purchaseStatus;

    private Integer invoiceType;

    private Integer isSendInvoice;

    private List<AfterSalesGoodsVo> afterSalesGoodsList;// 订单下的商品列表

    private List<AfterSalesGoods> thhGoodsList;//退换货产品信息

    private List<AfterSalesDetailVo> afterSalesDetailVoList;//退款信息

    private List<AfterSalesRecordVo> afterSalesRecordVoList;//售后内容

    private List<WarehouseGoodsOperateLog> warehouseGoodsOperateLogList;//出库记录

    private List<AfterSalesGoodsVo> afterReturnGoodsInStorageList;//退换货入库记录

    private List<AfterSalesGoodsVo> afterReturnGoodsOutStorageList;//退换货库记录

    private List<WarehouseGoodsOperateLog> afterReturnInstockList;//售后退换货入库记录

    private List<WarehouseGoodsOperateLog> afterReturnOutstockList;//售后换货出库记录

    private List<Express> expresseList;//物流信息

    private List<AfterSalesInvoiceVo> afterSalesInvoiceVoList;//退票信息

    private List<Invoice> afterReturnInvoiceVoList;//已退票信息

    private List<AfterSalesInvoiceVo> afterOpenInvoiceList;//售后开票记录-发票记录

    private List<AfterSalesInvoiceVo> afterInInvoiceList;//售后录票记录-发票记录

    private List<CapitalBill> afterCapitalBillList;//售后资金-交易记录

    private List<Attachment> afterInvoiceAttachmentList;//售后附件信息记录

    private List<Attachment> afterContractAttachmentList;//售后合同回传附件

    private List<AfterSalesInstallstionVo> afterSalesInstallstionVoList;//售后关联的工程师

    private List<AfterSalesInstallstion> safetyList;//安调信息

    private List<EngineerVo> engineerVoList;//工程师列表

    private List<PayApply> afterPayApplyList;//付款申请（财务-售后-安调）

    private List<TraderContact> traderContactList;//第三方关联客户的联系人列表


    private Integer thGoodsId;//商品库中退换货商品的id

    private String traderContactName;

    private String traderContactMobile;

    private String traderContactTelephone;

    private Integer areaId;

    private Integer addressId;

    private BigDecimal refundAmount;

    private BigDecimal refundFee;

    private BigDecimal realRefundAmount;

    private Integer refundAmountStatus;

    private BigDecimal periodAmount;//订单的账期金额

    private BigDecimal haveRefundAmount;//已退款金额

    private Integer traderSubject;

    private Integer traderMode;//新增字段-交易方式2018-1-31

    private String payee;

    private String bank;

    private String bankCode;

    private String bankAccount;

    /******2018-4-16 新增*******/
    private Integer invoiceTraderId;

    private String invoiceTraderName;

    private Integer invoiceTraderContactId;

    private String invoiceTraderContactName;

    private String invoiceTraderContactMobile;

    private String invoiceTraderContactTelephone;

    private Integer invoiceTraderAddressId;

    private String invoiceTraderArea;

    private String invoiceTraderAddress;

    private String invoiceComments;
    /******2018-4-16 新增**结束*****/
    //售后详情类---结束
    private Integer paymentStatus;//付款状态

    private Integer deliveryStatus;//发货状态

    //  private Integer invoiceStatus;//开票状态

    private Integer arrivalStatus;//收货状态

    private BigDecimal totalAmount;//订单总额

    private Integer orgId;//部门

    private String orgName;//部门名称

    private Integer userId;//归属销售

    private String userName;//归属销售名称

    private Integer saleorderStatus;//销售订单状态

    private Long saleorderValidTime;//销售订单生效时间

    private Integer traderId;

    private Integer customerNature;//客户性质

    private Integer customerLevel;// 客户等级

    private Integer orderCount;// 交易次数

    private BigDecimal orderTotalAmount;// 交易金额

    private Long lastOrderTime;//上次交易时间

    private List<Attachment> attachmentList;//关联的附件列表

    private List<User> userList;//用户信息列表

    private Integer afterSalesGoodsId;//编辑退换货手续费用

    private BigDecimal payAmount;//订单已付款金额

    private String name;//工程师的名称

    private String searchName;//搜索工程师名称

    private String sku;

    private String sku2;

    private Integer saleorderId;

    private String takeTraderName;

    private String takeTraderAddress;

    private Integer deliveryType;

    private String takeTraderArea;

    private Integer logisticsId;

    private Integer freightDescription;

    private String logisticsComments;

    private Integer takeTraderContactId;

    private String takeTraderContactName;

    private String takeTraderContactMobile;

    private String isOut = "0";// 是否关联出库日志表1；是0否

    private BigDecimal amount;//交易者账户余额

    private String eFlag = "0"; // 是否是查询快递出库的订单下的商品0：否，1：是

    private Integer isIn = 0; //判断是否是售后入库



    private Integer goodsId;//商品id

    private BigDecimal capitalTotalAmount;//售后流水：销售使用余额已经支付金额

    private BigDecimal traderAmount;//客户余额

    private String payer;//付款方

    private Integer verifiesType;//审核类型

    private List<String> verifyUsernameList;//当前审核人

    private String addTimeStr;//

    private String validTimeStr;//

    private BigDecimal payPeriodAmount;//偿还帐期

    private Integer isNormal = 0;//是否筛选正常出库产品

    private Integer orderNum;//售后订单数量

    private BigDecimal orderPrice;

    private Integer closeStatus;//售后订单的关闭状态：-货款票状态未改变---1-可以关闭；2-不可以

    private Integer isCanApplyInvoice;//是否能申请开票0-不可以；1-可以；

    private Integer isModifyServiceAmount;//是否能修改售后服务费0-不可以；1-可以
    private BigDecimal PreAnnealing;//订单的预退金额
    //售后税务
    private String regAddress;//注册地址

    private String regTel;//注册电话

    private String taxNum;//税务登记号

    private Integer goodsType;//订单中产品类型（0未维护，1 只有设备,2 只有试剂,3 又有试剂又有设备）

    private Integer verifyType;//审核类型（0 关闭 1完成）

    private Integer receivePaymentStatus;//收款状态

    private Long receivePaymentTime;//收款时间

    private Integer exchangeReturnNum;//已退回数量

    private Integer exchangeDeliverNum;//已重发数量

    private Integer canUseGoodsStock;//可用库存

    private Integer afterGoodsNum;//换货数量

    private Integer goodsStockNum;//库存量
    private Integer orderType; //订单类型
    private String currentAuditName;//当前审核人


    private Integer afterSalesType;//业务类型
    private Integer optUserId;


    private Date afterSalesWixTime;//售后单完结时间

    private String searchStartDateTime;//查询完结时间字段
    private String searchEndDateTime;

    private String mobileNumber;//手机号
    private List<Integer> userIdList;//用户id

    //检索的归属产品ID
    private Integer beLongProductUserId;


    //售后单涉及的归属产品集合
    private List<User> beLongProductUserList;

    //售后单涉及的入库的对应订单
    private List<Buyorder> inOrderList;

    //需要检索的入库订单号
    private String inOrderStr;
    //采购售后创建方式
    private Integer createType;

    /**
     * 退还账期金额
     */
    private BigDecimal refundPeriodAmount;

    //工程师对应的身份证号
    private String card;

    //工程师对应的手机号
    private String phone;

    /**
     * 第一责任部门
     */
    private String firstResponsibleDepartmentStr;

    /**
     * 售后联系人名称
     */
    private String afterConnectUserName;

    /**
     * 售后联系人电话
     */
    private String afterConnectPhone;

    private String buyorderCreatorName;

    private Integer buyorderCreator;

    private Integer num;

    /**
     * 退票状态 @RefundInvoiceStatusEnum
     */
    private Integer refundInvoiceStatus;
    /**
     * 入库状态 @RefundInvoiceStatusEnum
     */
    private Integer inStockStatus;
    /**
     * 退款状态 @RefundInvoiceStatusEnum
     */
    private Integer backMoneyStatus;
    /**
     * 开票状态 @RefundInvoiceStatusEnum
     */
    private Integer createInvoiceStatus;

    /**
     * 出库状态 @RefundInvoiceStatusEnum
     */
    private Integer outStockStatus;

    /**、
     * 已收款合计
     */
    private BigDecimal receiveAmount;
    /**
     * 订单流升级-直发售后商品
     */
    private List<AfterSalesGoodsVo> directGoodsList;
    /**
     * 订单流升级-普发售后商品
     */
    private List<AfterSalesGoodsVo> normalGoodsList;

    /**
     * 最终应退金额
     */
    private BigDecimal finalRefundableAmount;

    private String completeReason;

    /**
     * 安调商品信息
     */
    private List<AfterSalesGoodsVo> atGoodsList;

    private List<AfterSaleBuyorderDirectOutLog> directReturnOutstockList;

    private Integer firstResponsibleDepartment;
    /**
     * 采购仅退票，售后发票号
     */
    private String invoiceNo;
    /**
     * 采购仅退票，更换发票号
     */
    private String newInvoiceNo;
    /**
     * 采购仅退票，更换发票号
     */
    private String newInvoiceCode;
    /**
     * 仅退票暂存表id
     */
    private Integer afterBuyorderInvoiceId;

    /**
     * 是否显示下派医修帮按钮 0 显示取消下派 1 显示下派 2 技术咨询显示取消下派 3 技术咨询显示下派
     * @return
     */
    private Integer isShowDispatchButton;

    private List<ProductSelectionDispatchDto> productSelectionDispatchDtos;
}
