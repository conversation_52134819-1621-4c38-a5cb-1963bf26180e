package com.wms.dao;

import com.wms.model.dto.WmsOutputOrderGoodsDto;
import com.wms.model.po.WmsOutputOrderGoods;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WmsOutputOrderGoodsMapper {

    int deleteByPrimaryKey(Long id);

    int insertSelective(WmsOutputOrderGoods record);

    WmsOutputOrderGoods selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WmsOutputOrderGoods record);

    void batchInsert(List<WmsOutputOrderGoods> lendOutOrderGoods);

    List<WmsOutputOrderGoods> queryOutputGoodsByLendOutId(Long lendOutOrderId);

    /**
     * 根据出库单id查询出库商品信息
     * @param id
     * @return
     */
    List<WmsOutputOrderGoodsDto> queryOutputGoodsByWmsOutPutOrderId(Long id);

    WmsOutputOrderGoods getOrderGoods(@Param("orderNo")String orderNo, @Param("skuNo")String skuNo);

    List<WmsOutputOrderGoodsDto> queryOutputGoodsBySampleOutId(Long sampleOutOrderId);

    void deleteWmsOutputOrderGoodsByOrderId(@Param("sampleOutOrderId") Long sampleOutOrderId);

    /**
     * 根据外借出库单id查询商品信息（不关联其他表）
     *
     * @param sampleOutOrderId 外借出库单id
     * @return List<WmsOutputOrderGoods>
     */
    List<WmsOutputOrderGoods> getWmsOutPutOrderGoods(Long sampleOutOrderId);

    List<Integer> getManageCategoryOfSku(Long sampleOutOrderId);
}