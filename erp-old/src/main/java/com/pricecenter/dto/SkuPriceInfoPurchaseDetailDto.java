package com.pricecenter.dto;

import java.math.BigDecimal;

public class SkuPriceInfoPurchaseDetailDto {

    private Long id;

    private Long skuId;

    private Integer traderId;

    private String traderName;

    private BigDecimal purchasePrice;

    private String modTime;

    private String terminalRate;

    private String distributionRate;

    private String groupRate;

    private String electronicCommerceRate;

    private String  researchTerminalRate;

    /**
     * 供应商售后政策是否维护
     */
    private Integer supplyPolicyMaintained;

    public String getTerminalRate() {
        return terminalRate;
    }

    public void setTerminalRate(String terminalRate) {
        this.terminalRate = terminalRate;
    }

    public String getDistributionRate() {
        return distributionRate;
    }

    public void setDistributionRate(String distributionRate) {
        this.distributionRate = distributionRate;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public Integer getTraderId() {
        return traderId;
    }

    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }

    public BigDecimal getPurchasePrice() {
        return purchasePrice;
    }

    public void setPurchasePrice(BigDecimal purchasePrice) {
        this.purchasePrice = purchasePrice;
    }

    public String getModTime() {
        return modTime;
    }

    public void setModTime(String modTime) {
        this.modTime = modTime;
    }

    public String getTraderName() {
        return traderName;
    }

    public void setTraderName(String traderName) {
        this.traderName = traderName;
    }

    public Integer getSupplyPolicyMaintained() {
        return supplyPolicyMaintained;
    }

    public void setSupplyPolicyMaintained(Integer supplyPolicyMaintained) {
        this.supplyPolicyMaintained = supplyPolicyMaintained;
    }

    public String getGroupRate() {
        return groupRate;
    }

    public void setGroupRate(String groupRate) {
        this.groupRate = groupRate;
    }

    public String getElectronicCommerceRate() {
        return electronicCommerceRate;
    }

    public void setElectronicCommerceRate(String electronicCommerceRate) {
        this.electronicCommerceRate = electronicCommerceRate;
    }

    public String getResearchTerminalRate() {
        return researchTerminalRate;
    }

    public void setResearchTerminalRate(String researchTerminalRate) {
        this.researchTerminalRate = researchTerminalRate;
    }
}
