<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<div class="parts">
    <table class="table">
        <thead>
        <tr>
            <th class="wid20">推送状态</th>
            <th class="wid20">推送时间</th>
            <th class="wid20">推送人</th>
            <th>推送内容</th>
        </tr>
        </thead>
        <tbody>
        <c:if test="${not empty clientStatusRecordList}">
            <c:forEach items="${clientStatusRecordList}" var="clientStatusRecord">
                <tr>
                    <td>
                        <c:choose>
                            <c:when test="${clientStatusRecord.pushClientStatus eq 1}">
                                下派工程师
                            </c:when>
                            <c:when test="${clientStatusRecord.pushClientStatus eq 2}">
                                工程师完成预约
                            </c:when>
                            <c:when test="${clientStatusRecord.pushClientStatus eq 3}">
                                工程师已完工
                            </c:when>
                            <c:otherwise>
                                --
                            </c:otherwise>
                        </c:choose>
                    </td>
                    <td><fmt:formatDate value="${clientStatusRecord.pushTime}" pattern="yyyy-MM-dd HH:mm:ss" /></td>
                    <td>${clientStatusRecord.creatorName}</td>
                    <td>${clientStatusRecord.message}</td>
                </tr>
            </c:forEach>
        </c:if>
        <c:if test="${empty clientStatusRecordList}">
            <!-- 查询无结果弹出 -->
            <tr>
                <td colspan='4'>暂无记录！</td>
            </tr>
        </c:if>
        </tbody>
    </table>
</div>