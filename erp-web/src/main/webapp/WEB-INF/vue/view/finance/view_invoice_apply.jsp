<%--
  Created by IntelliJ IDEA.
  User: ckt
  Date: 2023/11/13
  Time: 09:49
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<html>
<head>
    <title>查看销售订单开票申请</title>
</head>

<style>
    .el-input.is-disabled .el-input__inner {
        color: #696B6F;
    }
    .el-textarea.is-disabled .el-textarea__inner {
        color: #696B6F;
    }
</style>
<body>
<div id="app">
    <div class="invoice_table">
        <el-table
                :data="invoiceApply.invoiceApplyDetailDtoList"
                border
                empty-text="暂无信息"
                fit
                :summary-method="getSummaries"
                show-summary
                :header-cell-style="{background: '#E3ECD9FF'}">
            <el-table-column align="center" label="产品名称" >
                <template slot-scope="scope">
                    <span style="margin-left: 10px">{{ scope.row.productName }}</span>
                    <el-tooltip v-if="scope.row.oldProductName" placement="right">
                        <div slot="content">原值：{{ scope.row.oldProductName }}</div>
                        <i class="el-icon-warning-outline"></i>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column align="center" label="规格型号" width="250">
                <template slot-scope="scope">
                    <span style="margin-left: 10px">{{ scope.row.specModel }}</span>
                    <el-tooltip v-if="scope.row.oldSpec" placement="right">
                        <div slot="content">原值：{{ scope.row.oldSpec }}</div>
                        <i class="el-icon-warning-outline"></i>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="unit" label="单位" width="80"></el-table-column>
            <el-table-column align="center" prop="price" label="单价" width="80"></el-table-column>
            <el-table-column align="center" prop="invoicedNum" label="已开票数量" width="100"></el-table-column>
            <el-table-column align="center" prop="TNNum" width="100">
                <template slot="header" slot-scope="scope">
                    <span>收货T+N未开票数量</span>
                    <el-tooltip placement="right">
                        <div slot="content">七天无理由商品：T+8<br/>其他商品：T+4</div>
                        <i class="el-icon-warning-outline"></i>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="TNDateStr" label="满足收货T+N日期" width="100"></el-table-column>
            <el-table-column align="center" prop="num" label="申请数量" width="100"></el-table-column>
            <el-table-column align="center" prop="taxRateStr" label="税率" width="100"></el-table-column>
            <el-table-column align="center" prop="totalAmount" label="申请金额" width="100"></el-table-column>
        </el-table>

        <el-divider></el-divider>
        <div class="comments">
            票面备注
            <el-input v-model="invoiceApply.comments"
                      type="textarea"
                      :rows="3"
                      placeholder=""
                      style="padding-top: 5px"
                      disabled
                      maxlength="200" show-word-limit>
            </el-input>
        </div>
        <div class="is_advance">
            开票留言
            <el-input type="textarea"
                      :rows="3" v-model="invoiceApply.invoiceMessage" placeholder="" style="padding-top: 5px" maxlength="200" disabled show-word-limit></el-input>
        </div>
        <div class="is_advance">
            提前开票
            <div v-if="invoiceApply.isAdvance">是否提前开票: 是</div>
            <div v-if="!invoiceApply.isAdvance">是否提前开票: 否</div>
            <el-input type="textarea" :rows="3" v-model="invoiceApply.advanceValidReason" placeholder="" style="padding-top: 5px" maxlength="200" disabled show-word-limit></el-input>
        </div>
    </div>
    <%--申请检查不通过信息--%>

    <div style="padding-top: 20px">
        <template v-if="invoiceApply.snapshotDtoList.length > 0">
            <el-divider></el-divider>
            <span  style="font-weight: bold;font-size: 16px">申请检查不通过信息</span>
        </template>

        <div v-for="detailDto in invoiceApply.snapshotDtoList" style="padding-top: 20px">
            {{detailDto.ruleName}} <span class="tipClass">{{detailDto.ruleContent}}</span>
            </br>{{detailDto.promptText}}
            <el-input v-model="detailDto.applyReason"
                      type="textarea"
                      :rows="2"
                      style="padding-top: 5px"
                      :disabled="true"
                      maxlength="200" show-word-limit>
            </el-input>
        </div>
    </div>
</div>



</body>
<script type="text/javascript">
    let invoiceApply = ${invoiceApply};
    let vm = new Vue({
        el: '#app',
        data() {
            return {
                invoiceType: '${invoiceType}',
                invoiceApply: {
                    isAdvance: null,
                    advanceValidComments: '',
                    comments: '',
                    invoiceApplyDetailDtoList: [],
                    invoiceMethod: null,
                    snapshotDtoList: [],
                },
                totalNum: 0,
                totalApplyAmount: 0,
                invoicedTotal: 0,
            }
        },
        methods: {
            getSummaries(param){
                const sums = [];
                var invoicedTotal = 0;
                var TNNumTotal = 0;
                var numTotal = 0;
                var amountTotal = 0;
                this.invoiceApply.invoiceApplyDetailDtoList.forEach(d => {
                    invoicedTotal += d.invoicedNum;
                    TNNumTotal += d.TNNum;
                    numTotal += d.num;
                    amountTotal += d.totalAmount
                })
                param.columns.forEach((column,index) => {
                    if(index === 0){
                        sums[index] = '合计';
                        return;
                    }
                    sums[4] = invoicedTotal ? invoicedTotal : 0;
                    sums[5] = TNNumTotal;
                    sums[7] = numTotal;
                    sums[9] = amountTotal;
                })
                return sums;
            }
        },
        created() {
            this.invoiceApply = invoiceApply;
            this.invoiceApply.invoiceApplyDetailDtoList.forEach( d => {
                this.totalNum += d.num;
                this.totalApplyAmount += d.totalAmount;
            })
        }
    });
</script>
<style>
    #app {
        font-size: 14px;
        font-family: "Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif;
        color: #606266;
    }
    .invoice_header {
        border: 1px solid #EBEEF5;
        height: 60px;
        display: flex;
        justify-content: left;
        align-items: center;
    }
    .invoice_body,.invoice_table,.is_advance,.comments{
        margin-top: 20px;
    }
    .invoice_sum {
        padding-top: 5px;
    }

</style>
</html>
