<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html>
<html>
<head>
    <title>请使用微信客户端访问</title>
    <link rel="stylesheet" href="/static/css/common/common.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/ui/ui.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/css/pages/serverError.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <script type="text/javascript">
        function isWeixinBrowser() {
            var ua = navigator.userAgent.toLowerCase();
            return ua.indexOf("micromessenger") != -1;
        }

        function isPC() {
            let userAgentInfo = navigator.userAgent;
            let Agents = new Array("Android", "iPhone", "SymbianOS", "Windows Phone", "iPad", "iPod");
            let flag = true;
            for (let v = 0; v < Agents.length; v++) {
                if (userAgentInfo.indexOf(Agents[v]) > 0) {
                    flag = false;
                    break;
                }
            }
            return flag;
        }
        if(isWeixinBrowser()){//微信浏览器
            if(true){//!isPC() //如果是端
                window.location.href="/crm/wx/index"; //手机APP端直接到拜访计划列表
            }
        }
    </script>

</head>

<body>
    <!--隐藏的登录用户信息 -->
    <jsp:include page="./profile/common/head_import.jsp"></jsp:include>

    <div class="page-wrap" id="page-container">
        <page-header></page-header>
        <div class="error-wrap">
            <div class="error-img">
                <img src="/static/image/serverError/no-auth.svg" alt="">
            </div>
            <div class="error-info">
                <div class="error-title">请使用微信客户端访问</div>
                <script >

                </script>
                <div class="error-tip">
                    如有疑问：<br>
                    请企业微信联系研发部Aadi。
                </div>
            </div>
        </div>
    </div>
    <script src="/static/js/common/vue.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/ui/ui.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/layout.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/pages/serverError.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
</body>
</html>
