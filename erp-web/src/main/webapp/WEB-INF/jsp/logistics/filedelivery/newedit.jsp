<!DOCTYPE html>
<html lang="en">
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增寄送文件" scope="application" />
 <%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="shiro" uri="http://shiro.apache.org/tags"%>
<%@ taglib prefix="tags" tagdir="/WEB-INF/tags"%>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
<%@ taglib uri="http://com.vedeng.common.util/definetags" prefix="define"%>
<%@taglib prefix="sf" uri="http://www.springframework.org/tags/form"%>

<%@ page trimDirectiveWhitespaces="true" %>

<%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
            + path + "/";
%>
<head>
<c:set var="path" value="<%=basePath%>" scope="application" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="renderer" content="webkit|ie-comp|ie-stand">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
 <title>${title}</title>
<link data-n-head="ssr" rel="icon" type="image/x-icon" href="<%=basePath%>static/favicon.ico">

<link  rel="stylesheet" href='<%= basePath %>webjars/layuidist/css/layui.css?rnd=${resourceVersionKey}'></link>
<link  rel="stylesheet" href='<%= basePath %>webjars/topezadmin/layui/css/ezform.css?rnd=${resourceVersionKey}'></link>
<script src="<%= basePath %>webjars/layuidist/layui.js?rnd=${resourceVersionKey}"></script>
<script src="<%= basePath %>static/js/jquery/jquery3.3.1.js?rnd=${resourceVersionKey}"></script>
<script src="<%= basePath %>webjars/jqueryform/jquery.form.js?rnd=${resourceVersionKey}"></script>
<script src="<%= basePath %>webjars/topezadmin/layui/js/core.js?rnd=${resourceVersionKey}"></script>

    <script src='<%= basePath %>static/js/jquery/export/FileSaver.min.js?rnd1=${resourceVersionKey}'></script>
    <script src='<%= basePath %>static/js/jquery/export/xlsx.core.min.js?rnd1=${resourceVersionKey}'></script>
    <script src='<%= basePath %>static/js/jquery/export/tableExport.min.js?rnd1=${resourceVersionKey}'></script>
    <style>
        .layui-badge-dot1{
            color: #ff3300;
        }
        .pageheight{
            height: calc(100vh - 75px);
            overflow-y: scroll;
            background: #fff;
        }
    </style>

</head>
<body>
<div class="layui-fluid" style="margin-top: 20px;">

    <div class="layui-col-space10 layui-row "  >
        <div class="layui-col-xs12 layui-col-lg7  ">

            <form class="layui-form "  action="/logistics/filedeliveryNew/saveFileDelivery.do" id="deliveryForm"    >
                <input type="hidden" id="fileDeliveryId" name="fileDeliveryId" value="${deliveryVO.fileDeliveryId}">
                <input type="hidden" id="fileDeliveryNo" name="fileDeliveryNo" value="${deliveryVO.fileDeliveryNo}">
                <input type="hidden" name="formToken" value="${formToken}">
                <div class="layui-col-space10 layui-row pageheight">
                    <div class="layui-col-xs12  ">
                        <div class="layui-card">
<%--                            <div class="layui-card-header">基本信息</div>--%>
                            <div class="layui-card-body">
                                <div class="layui-row">
                                    <div class="selector layui-col-xs12 layui-col-xl6 layui-col-space10">
                                        <div class="layui-form-item"  >
                                            <label class="layui-form-label"> <span class="layui-badge-dot1" >*</span>寄件类型 :</label>
                                            <div class="layui-input-block">
                                                <input type="radio" lay-filter="deliveryType" name="deliveryType" value="1" title="文件资料类" <c:if test="${deliveryVO.deliveryType ne 2}">checked </c:if>>
                                                <input type="radio" lay-filter="deliveryType" name="deliveryType" value="2" title="非文件资料类" <c:if test="${deliveryVO.deliveryType eq 2}">checked </c:if>>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-xl6 layui-col-space10">
                                        <div class="layui-form-item"  >
                                            <label class="layui-form-label">
                                                <span class="layui-badge-dot1" >*</span>
                                                寄件总重 :</label>
                                            <div class="layui-input-block">
                                                <div class="layui-input-wrap">
                                                    <c:choose>
                                                        <c:when test="${empty deliveryVO or empty deliveryVO.deliveryTotalWeight}">
                                                            <c:set var="deliveryTotalWeight" value="1" />
                                                        </c:when>
                                                        <c:otherwise>
                                                            <c:set var="deliveryTotalWeight" value="${deliveryVO.deliveryTotalWeight}" />
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <input type="number" name="deliveryTotalWeight" value="${deliveryTotalWeight}" maxlength="13" lay-reqtext="请填写寄件总重" lay-vertype="tips" lay-verify="required|decimal"    class="layui-input" >
                                                    <div class="layui-input-split layui-input-suffix">
                                                        KG
                                                    </div>
                                                </div>
                                                <small class="layui-form-mid layui-text-em item_desc" style="padding:0px !important;">- 将通过重量动态选择承运部门与快递公司</small>
                                            </div>
<%--                                            <div class="layui-form-mid layui-text-em">--%>
<%--                                                KG--%>
<%--                                            </div>--%>
<%--                                            <div class="layui-form-mid layui-text-em">--%>
<%--                                                <i class="layui-icon layui-icon-tips"  lay-on="test-tips-top"--%>
<%--                                                 ></i>--%>
<%--                                            </div>--%>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-xl6 layui-col-space10">
                                        <div class="layui-form-item" >
                                            <label class="layui-form-label">
                                                <span class="layui-badge-dot1" >*</span>
                                                物品名称 :</label>
                                            <div class="layui-input-block">
                                                <div class="layui-input-wrap">
                                                    <input type="text"lay-reqtext="请填写物品名称"  name="deliveryProdName" value="${deliveryVO.deliveryProdName}" maxlength="20" lay-vertype="tips" lay-verify="required"  lay-affix="clear"  class="layui-input" >
                                                </div>
                                                <!--                                <small class="layui-form-mid layui-text-em item_desc">- 标题应简洁、吸引人，并包含品牌名称。</small>-->
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-xl6 layui-col-space10">
                                        <div class="layui-form-item"  >
                                            <label class="layui-form-label">
                                                <span class="layui-badge-dot1" >*</span>
                                                包裹总数 :</label>
                                            <div class="layui-input-block">
                                                <div class="layui-input-wrap">
                                                    <c:choose>
                                                        <c:when test="${empty deliveryVO or empty deliveryVO.deliveryExpressNum}">
                                                            <c:set var="deliveryExpressNum" value="1" />
                                                        </c:when>
                                                        <c:otherwise>
                                                            <c:set var="deliveryExpressNum" value="${deliveryVO.deliveryExpressNum}" />
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <input type="number" lay-reqtext="请填写包裹总数"  name="deliveryExpressNum" value="${deliveryExpressNum}"    lay-vertype="tips" lay-verify="required|number|integer"
                                                             lay-affix="clear"  class="layui-input" >
                                                </div>
                                                <!--                                <small class="layui-form-mid layui-text-em item_desc">- 标题应简洁、吸引人，并包含品牌名称。</small>-->
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12   layui-col-space20">
                                        <div class="layui-form-item"  >
                                            <label class="layui-form-label">
                                                <span class="layui-badge-dot1" >*</span>
                                                寄件说明 :</label>
                                            <div class="layui-input-block">
                                                <div class="layui-input-wrap">
                                                    <textarea  style="min-height: 68px;" lay-reqtext="请填写寄件说明" name="content" value="${deliveryVO.content}" maxlength="100" lay-vertype="tips" lay-verify="required" lay-affix="clear"  class="layui-textarea" ></textarea>
                                                </div>
                                                <!--                                <small class="layui-form-mid layui-text-em item_desc">- 标题应简洁、吸引人，并包含品牌名称。</small>-->
                                            </div>
                                        </div>
                                    </div>
                                    <div class="selector layui-col-xs12 layui-col-xl12 layui-col-space10">
                                        <div class="layui-form-item"  >
                                            <label class="layui-form-label">  承运部门 :</label>
                                            <div class="layui-input-block">
                                                <input type="radio" lay-filter="deliveryDept"   name="deliveryDept" value="1" title="财务部" <c:if test="${empty deliveryVO or empty deliveryVO.deliveryDept or deliveryVO.deliveryDept eq 1}">checked </c:if>  >
                                                <input type="radio" lay-filter="deliveryDept"   name="deliveryDept" value="2" title="物流部" <c:if test="${deliveryVO.deliveryDept eq 2}">checked </c:if> >
                                                <input type="radio" lay-filter="deliveryDept"   name="deliveryDept" value="3" title="申请人所在部门" <c:if test="${deliveryVO.deliveryDept eq 3}">checked </c:if>  >
                                            </div>
                                            <small class="layui-form-mid layui-text-em item_desc" style="padding:0px !important;">- 根据寄件类型与重量自动选择，申请人所在部门只能寄送EMS和电商标快。</small>
                                        </div>
                                    </div>
                                    <div class="selector layui-col-xs12 layui-col-xl12 layui-col-space10">
                                        <div class="layui-form-item"  >
                                            <label class="layui-form-label">  寄件人 :</label>
                                            <div class="layui-input-block">
                                                <input type="radio" lay-filter="sendUserType"   name="sendUserType" value="1" title="南京贝登医疗股份有限公司" <c:if test="${empty deliveryVO or empty deliveryVO.sendUserType or deliveryVO.sendUserType eq 1}">checked </c:if>  >
                                                <input type="radio" lay-filter="sendUserType"   name="sendUserType" value="2" title="申请人" <c:if test="${deliveryVO.sendUserType eq 2}">checked </c:if> >
                                            </div>
<%--                                            <small class="layui-form-mid layui-text-em item_desc" style="padding:0px !important;">- 根据寄件类型与重量自动选择，申请人所在部门只能寄送EMS和电商标快。</small>--%>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-xs12  ">
                        <div class="layui-card">
                            <div class="layui-card-header">收件单位
                                <div class="layui-btn-group" style="float:right">
                                <a id="addmodel" class="layui-btn layui-btn-sm layui-btn-primary"   style="color:#16baaa">
                                    <i class="layui-icon layui-icon-add-1"></i>添加收件单位</a>
                                <a id="import"  class="layui-btn layui-btn-sm layui-btn-primary" style="color:#16baaa" >
                                    <i class="layui-icon layui-icon-upload-drag"></i>
                                    导入收件单位</a>
                                </div>
                            </div>
                            <div class="layui-card-body" style="padding:0px">
                                <table class="layui-table" id="addresstable" lay-filter="addresstable">
                                    <thead>
                                    <tr>
                                        <th lay-data="{field:'ID',hide:true}">ID</th>
                                        <th lay-data="{field:'name1', width:120}">收件单位类型</th>
                                        <th lay-data="{field:'name2', width:150}">收件单位</th>
                                        <th lay-data="{field:'name3', width:80}">联系人</th>
                                        <th lay-data="{field:'name4', width:120}">联系电话</th>
                                        <th lay-data="{field:'name5', minWidth:200}">联系地址</th>
<%--                                        <th lay-data="{field:'name6', minWidth:100,escape:false,edit: 'text'}">快递单号</th>--%>
                                        <th lay-data="{field:'name7', width:120, fixed:'right', templet: '#tooladdress'}" >操作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
<%--                                    <tr data-addressid="1" >--%>
<%--                                        <td  >1</td>--%>
<%--                                        <td>孔子</td>--%>
<%--                                        <td>华夏华夏华夏华夏华夏华夏</td>--%>
<%--                                        <td>有朋自</td>--%>
<%--                                        <td>15555555555</td>--%>
<%--                                        <td>华夏华夏华夏华夏华夏华夏华夏华夏华夏华夏华夏华夏</td>--%>
<%--&lt;%&ndash;                                        <td>abc</td>&ndash;%&gt;--%>
<%--                                        <td>--%>
<%--                                            <a data-id="1" name="editrow" style="color:#16baaa">编辑</a>--%>
<%--&lt;%&ndash;                                            <a id="2" name="editkuaidirow" style="color:#16baaa">编辑快递单</a>&ndash;%&gt;--%>
<%--                                            <a data-id="3" name="delrow" >删除</a>--%>
<%--                                        </td>--%>
<%--                                    </tr>--%>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>



                </div>
            </form>
            <div class="layui-row layui-panel formtopfix formtopfixbuttons" style="margin-left: -16px;"  >
                <div style="margin: 10px 0px 0px 10px;float:left" class="
                    layui-col-xs9 layui-col-xs-offset3 layui-btn-container" id="submitButtonContainer">
                    <button type="button"  class="submitBtn layui-btn layui-bg-blue " >提交</button>
                </div>
            </div>
        </div>
        <div class="layui-col-xs12  layui-col-lg5   ">

            <div class="layui-card pageheight"  >
<%--                <div class="layui-card-header">说明</div>--%>
                <div class="layui-card-body"  >
                    <div class="layui-row">
                          ${config}
                    </div>
                </div>
            </div>

        </div>

    </div>
</div>

<script type="text/html" id="importForm">
    <div class="layui-container" style="width: 480px">
        <form class="layui-form" style="padding-top:15px" >
            <div class="layui-form-item">
<%--                <label class="layui-form-label">快递公司</label>--%>
                <div class="layui-input-block" STYLE="MARGIN-LEFT:0PX !important;">
                    <div class="layui-upload-drag" style="display: block;" id="ID-upload-demo-drag">
                        <i class="layui-icon layui-icon-upload"></i>
                        <div id="ID-upload-demo-tips">点击上传，或将文件拖拽到此处<br>
                            文件格式为 .xls，.xlsx，不超过5M
                        </div>
                        <div style="display: none" id="ID-upload-demo-preview">

                        </div>
                    </div>
                    <a  target=_blank href="/static/template/收件单位导入模板.xlsx" type="button" class="layui-btn layui-btn-primary layui-btn-xs">模版下载</a>
                    请勿更改表格格式
                </div>
            </div>
<%--            <div class="layui-form-item">--%>
<%--                &lt;%&ndash;                <label class="layui-form-label">快递公司</label>&ndash;%&gt;--%>
<%--                <div class="layui-input-block" >--%>
<%--                    <button type="button" class="layui-btn layui-btn-primary" id="ID-upload-demo-action">开始上传</button>--%>
<%--                    <button type="button" class=" closeupload layui-btn layui-btn-primary"  >关闭</button>--%>
<%--                </div>--%>
<%--            </div>--%>
        </form>



    </div>
</script>
<script type="text/html" id="tooladdress">
    <div class="layui-clear-space">
        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
        <a class="layui-btn layui-btn-xs" lay-event="del">删除</a>
    </div>
</script>
<script type="text/html" id="uploadtips">
    <table class="layui-table" id="uploadtiptable" lay-filter="uploadtiptable">
        <thead>
        <tr>
            <th lay-data="{field:'ID',hide:true}">*收件单位</th>
            <th lay-data="{field:'name1', width:120}">*联系人</th>
            <th lay-data="{field:'name2', width:150}">*联系电话</th>
            <th lay-data="{field:'name3', width:80}">*区代码</th>
            <th lay-data="{field:'name4', width:120}">*联系地址</th>
            <th lay-data="{field:'name5', minWidth:300}">错误信息</th>
          </tr>
        </thead>
        <tbody>

        </tbody>
    </table>
</script>

<script>


    // Usage
    layui.use(function(){
        let layer = layui.layer;
        let form=layui.form;
        let table=layui.table;

        var upload = layui.upload;
        table.init('addresstable');
        $("#import").click(function(){
            layer.open({
                type: 1, // page 层类型
                area: ['500px', '350px'],
                title: '导入',
                shade: 0, // 遮罩透明度
                shadeClose: false, // 点击遮罩区域，关闭弹层
                // maxmin: true, // 允许全屏最小化
                anim: 0, // 0-6 的动画形式，-1 不开启
                content: $("#importForm").html(),
                id: 'importId',
                success: function(){
                    var filename="";
                    //根据重量选中
                    upload.render({
                        elem: '#ID-upload-demo-drag',
                        url: '/logistics/filedeliveryNew/upload.do', // 实际使用时改成您自己的上传接口即可。
                        data: {
                            fileDeliveryId:$("#fileDeliveryId").val()
                        },
                        exts:'xls|xlsx',
                        size: 5*1024,
                        // auto: false,
                        // bindAction: '#ID-upload-demo-action',
                        acceptMime:'application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                        choose: function(obj){
                            obj.preview(function(index, file, result){
                                // $("#ID-upload-demo-preview").html(`<span class="layui-inline layui-upload-choose">`+file.name+`</span>`);
                                // $("#ID-upload-demo-preview").show();
                                // $("#ID-upload-demo-tips").hide();
                                filename=file.name;
                            })
                        },
                        done: function(res){
                            if(res.success){
                                var fileDeliveryImportResultVOErrorResult=res.data.fileDeliveryImportResultVOErrorResult;
                                if(fileDeliveryImportResultVOErrorResult.length>0){
                                    layer.open({
                                        type: 1, // page 层类型
                                        area: ['780px', '450px'],
                                        title: '导入失败明细',
                                        shade: 0, // 遮罩透明度
                                        shadeClose: false, // 点击遮罩区域，关闭弹层
                                        // maxmin: true, // 允许全屏最小化
                                        anim: 0, // 0-6 的动画形式，-1 不开启
                                        content: $("#uploadtips").html(),
                                        id: 'uploadId',
                                        btn: [ '下载详情', '取消'],
                                        success: function(){
                                            var laytpl = layui.laytpl;
                                            var getTpl = document.getElementById('uploadTpl').innerHTML;
                                            laytpl(getTpl).render(fileDeliveryImportResultVOErrorResult, function(str){
                                                $("#uploadtiptable tbody").append(str);
                                                // layui.table.init("uploadtiptable");
                                            });
                                        },
                                        btn1: function(index, layero, that){

                                            filename=filename.replace(/\.xls(x)?$/, "")+"_导入失败明细";

                                            $('#uploadtiptable').tableExport( {
                                                type: 'xlsx',
                                                htmlHyperlink: 'content',
                                                tableName:'导入失败明细',
                                                fileName:filename
                                            });

                                            layer.closeAll();
                                        },
                                        btn2:  function(index, layero, that){
                                            layer.closeAll();
                                        }
                                    });
                                }
                                var fileDeliveryAddressVOList=res.data.fileDeliveryAddressVOList;
                                if(fileDeliveryAddressVOList.length>0){
                                    layui.use(function(){
                                        var laytpl = layui.laytpl;
                                        var getTpl = document.getElementById('uploadSuccessTPL').innerHTML;
                                        laytpl(getTpl).render(fileDeliveryAddressVOList, function(str){
                                            $("#addresstable tbody").append(str);
                                            layui.table.init("addresstable");
                                            if(fileDeliveryImportResultVOErrorResult.length==0){
                                                layui.layer.closeAll();
                                                layui.layer.msg("导入成功");
                                            }
                                        });
                                    })
                                }
                            }
                            else{
                                layui.layer.alert("上传失败："+res.message);
                            }
                        },
                        error: function(index, upload, res, xhr){
                            layer.msg('上传失败');
                            console.log(index); // 当前文件的索引
                            // upload(); 重新上传的方法
                            console.log(res);  // 返回值（纯文本）
                            console.log(JSON.parse(res));  // 返回值（json）
                            console.log(xhr);
                        },
                        // before: function(){
                        //     if(element.css('display') !== 'block'){
                        //         layui.layer.msg("请先上传文件")
                        //         return false;
                        //     }
                        //     return true;
                        // }

                    });
                    form.render();
                    // layui.form.render();
                }

            });
        })

        // 自定义验证规则
        form.verify({
            decimal: function(value){
                if(!/^\d{1,10}(\.\d{0,2})?$/.test(value)){
                    return '请输入正确的数据格式 ##########.##';
                }
            },
            integer: function(value){
                if(!/^\d{1,1}?$/.test(value)){
                    return '请输入1-9的数字';
                }
                if(value<1||value>9){
                    return '请输入1-9的数字';
                }
            }
        });
        form.on('radio(deliveryType)', function(data){
            var elem = data.elem; // 获得 radio 原始 DOM 对象
            // var checked = elem.checked; // 获得 radio 选中状态
            // var value = elem.value; // 获得 radio 值
            // var othis = data.othis; // 获得 radio 元素被替换后的 jQuery 对象
            changeDept();
         //   layer.msg(['value: '+ value, 'checked: '+ checked].join('<br>'));
        });
        $("[name=deliveryTotalWeight]").blur(function(){
            changeDept();
        })


        $("#addmodel").click(function(){
            layer.open({
                title: "添加收件单位",
                type: 2,
                shade: 0,
                maxmin: false,
                shadeClose: false,
                area: ['50%','80%'],
                content: "/topezadmin/form/form-filedeliveryaddress?TRADER_TYPE=1&INPUT_TYPE=1&FILE_DELIVERY_ID="+$("#fileDeliveryId").val(),
                moveOut: true,
                success: function(layero, indexyyy, that){

                    var body = layer.getChildFrame('body', indexyyy);
                    if($(body).find('#submitButtonContainer').length>0){
                        $(body).find('#submitButtonContainer').append("<button class='layui-btn  layui-btn-primary' id='closeParent' type='button'>取消</button>");
                        $(body).on("click","#closeParent",function(){
                            layui.layer.close(indexyyy);
                        })
                    }
                }
            });
        })


        // 触发行按钮事件
        table.on('tool(addresstable)', function(obj){
            var data = obj.data; // 获得当前行数据
            if(obj.event === 'edit'){
                layer.open({
                    title: "编辑收件单位",
                    type: 2,
                    shade: 0,
                    maxmin: false,
                    shadeClose: false,
                    area: ['50%','80%'],
                    content: "/topezadmin/form/form-filedeliveryaddress?TRADER_TYPE=1&INPUT_TYPE=1&FILE_DELIVERY_ID="+$("#fileDeliveryId").val()+"&ID="+data.ID,
                    moveOut: true,
                    success: function(layero, indexyyy, that){

                        var body = layer.getChildFrame('body', indexyyy);
                        if($(body).find('#submitButtonContainer').length>0){
                            $(body).find('#submitButtonContainer').append("<button class='layui-btn  layui-btn-primary' id='closeParent' type='button'>取消</button>");
                            $(body).on("click","#closeParent",function(){
                                layui.layer.close(indexyyy);
                            })
                        }
                    }
                });

             //   openForm("/topezadmin/form/form-filedeliveryaddress?FILE_DELIVERY_ID="+$("#fileDeliveryId").val()+"&ID="+data.ID,"编辑收件单位",'750px, 580px');
            } else if(obj.event === 'del'){
                var loadIndex = layer.load(0);
                //todo ajax删除
                $.post("/logistics/filedeliveryNew/deleteFileDeliveryAddress.do" ,
                    {
                        id:data.ID
                }, function(response) {
                        layer.close(loadIndex);
                    if(response.success){
                        obj.del();
                        $("tr[data-addressid="+data.ID+"]").remove();
                    }else{
                        layui.layer.alert(data.message);
                    }
                }, 'json').fail(function () {
                    layer.close(loadIndex);
                    layui.layer.alert("系统异常");
                });

            }
        });

        $(".submitBtn").click(function(){
            if(! layui.form.validate('[name=deliveryTotalWeight]')){
                return false;
            }
            if(! layui.form.validate('[name=deliveryProdName]')){
                return false;
            }
            if(! layui.form.validate('[name=deliveryExpressNum]')){
                return false;
            }
            if(! layui.form.validate('[name=content]')){
                return false;
            }

            if($("#addresstable tbody tr").length==0 ){
                layer.alert("请添加收件单位");
                return false;
            }

            var loadIndex = layer.msg('正在处理', {
                icon: 16,
                shade: 0.01
            });
            $("#deliveryForm").ajaxSubmit({
                url: "/logistics/filedeliveryNew/saveFileDelivery.do",
                type: "POST",
                dataType: 'json',
                success: function (data) {
                    layer.close(loadIndex);
                    if (data.success) {
                        console.log("data::" + data.data);
                        layer.msg("保存成功");
                        location.href="/logistics/filedeliveryNew/getFileDeliveryDetail.do?fileDeliveryId="+$("#fileDeliveryId").val();
                    }else {
                        layer.alert( "保存失败:"+data.message);
                        console.log(data.message )
                    }
                },
                error: function (e) {
                    layer.close(loadIndex);
                    if(e.status===1000){
                        layer.alert("无权限,请联系管理员");
                    }else{
                        layer.alert("保存失败," +  e.responseJSON.message);
                    }
                }
            });
            return false;
        })



        function changeDept(){
            let p1=$("[name=deliveryType]:checked").val();
            let p2= $("[name=deliveryTotalWeight]").val();
            if(p2==''){
                p2=0;
            }
            let result=1;
            if(p1==1){
                if(parseFloat(p2)<30){
                    result=1;
                }else{
                    result=2;
                }
            }else if(p1==2){
                if(parseFloat(p2)<30){
                    result=3;
                }else{
                    result=2;
                }
            }
            $('input[name="deliveryDept"][value='+result+']').prop('checked', true);
            layui.form.render();
        }
        $("body").on("click",".closeupload",function(){
            layui.layer.closeAll();
        })
    });
    function submitAddressSuccess(data){
        if(!data.success){
            layui.layer.alert("添加失败，请重试。"+data.message);
            return;
        }
        layui.use(function(){
            var laytpl = layui.laytpl;
            var getTpl = document.getElementById('TPL').innerHTML;
            laytpl(getTpl).render(data.data, function(str){
                if($("tr[data-addressid="+data.data.id+"]").length>0){
                    $("tr[data-addressid="+data.data.id+"]").html($(str).html());
                }else{
                    $("#addresstable tbody").append(str);
                }
                layui.table.init("addresstable");
                layui.layer.closeAll();
            });
        })
    }
</script>

<script id="TPL" type="text/html">
    <tr data-addressid="{{= d.id }}" >
        <td>{{= d.id }}</td>
        <td> {{=d.traderType==1?'客户':d.traderType==2?'供应商':'其他'}}
         </td>
        <td>{{= d.traderName }}</td>
        <td>{{= d.traderContactName }}</td>
        <td>{{= d.traderContactMobile }}</td>
        <td>{{= d.traderContactAddress }}</td>
        <td>
            <a data-id="{{= d.id }}" name="editrow" style="color:#16baaa">编辑</a>
            <a data-id="{{= d.id }}" name="delrow">删除</a>
        </td>
    </tr>
</script>
<script id="uploadSuccessTPL" type="text/html">
    {{#  layui.each(d, function(index, item){ }}
    <tr data-addressid="{{= item.id }}" >
        <td>{{= item.id }}</td>
        <td> {{=item.traderType==1?'客户':item.traderType==2?'供应商':'其他'}}
        </td>
        <td>{{= item.traderName }}</td>
        <td>{{= item.traderContactName }}</td>
        <td>{{= item.traderContactMobile }}</td>
        <td>{{= item.traderContactAddress }}</td>
        <td>
            <a data-id="{{= item.id }}" name="editrow" style="color:#16baaa">编辑</a>
            <a data-id="{{= item.id }}" name="delrow">删除</a>
        </td>
    </tr>
    {{#  }) }}
</script>

<script id="uploadTpl" type="text/html">
    {{#  layui.each(d, function(index, item){ }}
    <tr>
        <td>{{= item.traderName }}</td>
        <td>{{= item.traderContractName }}</td>
        <td>{{= item.mobile }}</td>
        <td>{{= item.areaId }}</td>
        <td>{{= item.traderAddressInfo }}</td>
        <td>{{= item.errorMessage }}</td>
    </tr>
    {{#  }) }}
</script>





</body>
</html>