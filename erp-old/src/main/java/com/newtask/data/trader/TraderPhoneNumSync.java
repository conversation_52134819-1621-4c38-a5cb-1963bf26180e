package com.newtask.data.trader;

import com.newtask.data.AbstractDataSync;
import com.newtask.data.dao.TraderDateMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *更新手机号码个数
 **/
@Component
public class TraderPhoneNumSync extends AbstractDataSync {

    @Resource
    private TraderDateMapper traderDateMapper;

    @Override
    public void process(String startTime) {
        super.process(startTime);
    }

    /**
     * 查询某一时间段需要更新的客户
     * @param startTime
     * @return
     */
    @Override
    public List<Integer> loadBizId(Long startTime,Long endTime) {
        List<Integer> list=   traderDateMapper.getPhoneNumTraderId(startTime,endTime);
        return list;
    }

    /**
     *
     * @param bizIds
     * @return
     */
    @Override
    public List<Map<String, Object>> loadBizData(List<Integer> bizIds) {
        // 根据 traderId查 手机号码个数
        if(CollectionUtils.isNotEmpty(bizIds)) {
            List<Map<String, Object>> list = traderDateMapper.findPhoneNum(bizIds);
            return list;
        }
        return null;

    }

    @Override
    public void updateData(List<Map<String, Object>> dataList) {

        dataList.forEach(item->{
            Long nowDate = new Date().getTime();
            Integer traderId= NumberUtils.toInt(item.get("traderId")+"");
            Integer mobileCount=NumberUtils.toInt(item.get("mobileCount")+"");

             makeExist(traderId,nowDate);
            traderDateMapper.updatePhoneNum(traderId,mobileCount,nowDate);
        });

    }

    @Override
    public void makeExist(Integer id,Long nowDate) {
        Long count = traderDateMapper.makeExist(id);
        if (count <= 0) {
            //往宽表里添加数据
            traderDateMapper.insertTraderId(id,nowDate);
        }
    }
}
