package com.vedeng.erp.kingdee.batch.writer;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * @description 虚拟票 并跟新 返利收票状态
 */
@Service
@Slf4j
public class VirtualInvoiceWriter extends BaseWriter<List<BatchVirtualInvoiceDto>> {

    @Autowired
    private BatchVirtualInvoiceDtoMapper batchVirtualInvoiceDtoMapper;

    @Autowired
    private BatchVirtualInvoiceItemDtoMapper batchVirtualInvoiceItemDtoMapper;
    @Autowired
    private BatchSettlementBillDtoMapper batchSettlementBillDtoMapper;

    @Autowired
    private BatchSettlementBillItemDtoMapper batchSettlementBillItemDtoMapper;

    @Autowired
    private BatchAfterSalesGoodsDtoMapper batchAfterSalesGoodsDtoMapper;

    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;

    @Autowired
    private BatchBuyorderGoodsDtoMapper batchBuyorderGoodsDtoMapper;


    @Override
    public void doWrite(List<BatchVirtualInvoiceDto> item, JobParameters params, ExecutionContext stepContext) throws Exception {

        log.info("VirtualInvoiceWriter.doWrite:{}", JSON.toJSONString(item));

        if (CollUtil.isEmpty(item)) {
            return;
        }

        item.forEach(x->{

            batchVirtualInvoiceDtoMapper.insertSelective(x);
            BatchVirtualInvoiceDto batchVirtualInvoiceDto = batchVirtualInvoiceDtoMapper.selectByInvoiceNoCodeAndSourceId(x);
            BatchVirtualInvoiceDto update = new BatchVirtualInvoiceDto();
            update.setVirtualInvoiceId(batchVirtualInvoiceDto.getVirtualInvoiceId());
            update.setUuid("FL"+batchVirtualInvoiceDto.getVirtualInvoiceId());
            batchVirtualInvoiceDtoMapper.updateByVirtualInvoiceId(update);

            List<BatchVirtualInvoiceItemDto> virtualInvoiceItemDtoList = x.getVirtualInvoiceItemDtoList();
            virtualInvoiceItemDtoList.forEach(a->{
                a.setVirtualInvoiceId(batchVirtualInvoiceDto.getVirtualInvoiceId());
                batchVirtualInvoiceItemDtoMapper.insertSelective(a);
            });

        });

        boolean doUpdate = checkSettlementBillItem(item.get(0).getSettleBillId(), item.get(0).getColorType());

        if (doUpdate) {
            BatchSettlementBillDto update = new BatchSettlementBillDto();
            update.setSettleBillId(item.get(0).getSettleBillId());
            update.setModTime(new Date());
            batchSettlementBillDtoMapper.updateByPrimaryKeySelective(update);
        }


    }

    private List<BatchBuyorderGoodsDto> getBuyOrderArrivalList(List<Integer> buyOrderGoodsIds, List<BatchBuyorderGoodsDto> buyOrderGoodsDtoList) {

        List<BatchAfterSalesGoodsDto> batchAfterSalesGoodsDtos = batchAfterSalesGoodsDtoMapper.selectByOrderDetailIdsAndTotalAfterNum(buyOrderGoodsIds, 546, 536);

        List<BatchWarehouseGoodsOutInItemDto> batchWarehouseGoodsOutInItemDtos = batchWarehouseGoodsOutInItemDtoMapper.selectByRelatedIdsAndOperateType(buyOrderGoodsIds, ErpConstant.ONE);

        List<BatchBuyorderGoodsDto> batchBuyOrderGoodsArrivalList = buyOrderGoodsDtoList.stream().filter(x -> {
            BigDecimal realNum = new BigDecimal(x.getNum().toString());
            if (CollUtil.isNotEmpty(batchAfterSalesGoodsDtos)) {
                Optional<BatchAfterSalesGoodsDto> first = batchAfterSalesGoodsDtos.stream().filter(y -> y.getOrderDetailId().equals(x.getBuyorderGoodsId())).findFirst();
                if (first.isPresent()) {
                    realNum = realNum.subtract(new BigDecimal(first.get().getNum().toString()));
                }
            }
            BigDecimal arrivalNum = BigDecimal.ZERO;
            if (CollUtil.isNotEmpty(batchWarehouseGoodsOutInItemDtos)) {
                Optional<BatchWarehouseGoodsOutInItemDto> first = batchWarehouseGoodsOutInItemDtos.stream().filter(y -> y.getRelatedId().equals(x.getBuyorderGoodsId())).findFirst();
                if (first.isPresent()) {
                    arrivalNum = first.get().getNum();
                }
            }
            x.setRealNum(realNum);
            return realNum.compareTo(arrivalNum) <= 0;

        }).collect(Collectors.toList());
        return batchBuyOrderGoodsArrivalList;
    }


    private boolean checkSettlementBillItem(Integer settleBillId,Integer type) {

        List<BatchSettlementBillItemDto> batchSettlementBillItemDtos = batchSettlementBillItemDtoMapper.selectBySettleBillId(settleBillId);

        if (type.equals(ErpConstant.TWO)) {

            Map<Integer, BatchSettlementBillItemDto> batchSettlementBillItem2Map = batchSettlementBillItemDtos.stream().collect(Collectors.toMap(BatchSettlementBillItemDto::getBusinessItemId, x -> x, (k1, k2) -> k1));
            Set<Integer> integers = batchSettlementBillItem2Map.keySet();
            List<Integer> buyOrderGoodsIds = CollUtil.newArrayList(integers);

            List<BatchBuyorderGoodsDto> buyOrderGoodsDtoList = batchBuyorderGoodsDtoMapper.findByBuyorderGoodsIdInAndIsDelete(buyOrderGoodsIds, 0);
            List<BatchBuyorderGoodsDto> buyOrderArrivalList = getBuyOrderArrivalList(buyOrderGoodsIds, buyOrderGoodsDtoList);
            batchSettlementBillItemDtos.forEach(x->{
                Optional<BatchBuyorderGoodsDto> first = buyOrderArrivalList.stream().filter(a -> a.getBuyorderGoodsId().equals(x.getSettleItemBillId())).findFirst();
                first.ifPresent(y -> x.setNumber(y.getRealNum()));
                x.setAmount(x.getPrice().multiply(x.getNumber()));
            });
        }




        List<Integer> businessItemIds = batchSettlementBillItemDtos.stream().map(BatchSettlementBillItemDto::getBusinessItemId).collect(Collectors.toList());
        List<BatchVirtualInvoiceItemDto> virtualInvoiceItemDtoList = batchVirtualInvoiceItemDtoMapper.selectByBusinessOrderItemIds(businessItemIds,type);
        Map<Integer, List<BatchVirtualInvoiceItemDto>> virtualInvoiceItemDto2Map = virtualInvoiceItemDtoList.stream().collect(Collectors.groupingBy(BatchVirtualInvoiceItemDto::getBusinessOrderItemId));

        AtomicBoolean flag = new AtomicBoolean(false);

        batchSettlementBillItemDtos.forEach(x->{
            List<BatchVirtualInvoiceItemDto> data = virtualInvoiceItemDto2Map.get(x.getBusinessItemId());
            BatchSettlementBillItemDto update = new BatchSettlementBillItemDto();
            if (CollUtil.isNotEmpty(data)) {
                BigDecimal amount = data.stream().map(a -> a.getTotalAmount().abs()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                update.setSettleItemBillId(x.getSettleItemBillId());
                if (amount.compareTo(BigDecimal.ZERO) == 0) {
                    update.setInvoiceStatus(0);
                }
                // 全部
                if (x.getAmount().compareTo(amount) <= 0) {
                    update.setInvoiceStatus(2);
                }
                // 部分
                if (amount.compareTo(BigDecimal.ZERO) > 0 && x.getAmount().compareTo(amount) > 0) {
                    update.setInvoiceStatus(1);
                }
            } else {
                update.setInvoiceStatus(0);
            }
            batchSettlementBillItemDtoMapper.updateByPrimaryKeySelective(update);
            flag.set(true);
        });

        return flag.get();


    }


}
