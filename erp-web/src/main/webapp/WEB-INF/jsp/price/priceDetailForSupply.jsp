<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="详情" scope="application" />
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<%@ include file="../common/common.jsp"%>
<script type="text/javascript">

    function toEdit() {
        $.ajax({
            async:true,
            url:page_url+'/price/basePriceMaintain/toEditValidator.do',
            data:{"skuPriceChangeApplyId":'${skuPriceChangeApplyDto.id}'},
            type:"POST",
            dataType : "json",
            success:function(data){
                if(data.code==0){
                    var verifyStatus = data.data;
                    if(verifyStatus == '审核中'){
                        layer.alert("该商品核价正在审核中，不可进行编辑");
                    }else{
                        window.location.href = "${pageContext.request.contextPath}/price/basePriceMaintain/edit.do?skuPriceChangeApplyId=${skuPriceChangeApplyDto.id}";
                    }
                }else{
                    layer.alert(data.message)
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        })
    }

    function toEffectPrice(skuPriceChangeApplyId,span){
        $.ajax({
            async:true,
            url:page_url+'/price/basePriceMaintain/toEffectPriceValidator.do',
            data:{"skuPriceChangeApplyId":skuPriceChangeApplyId},
            type:"POST",
            dataType : "json",
            success:function(data){
                if(data.code == 0){
                    openTab(span);
                }else{
                    layer.alert(data.message)
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        })
    }

    function openTab(span){
        var frontPageId = $(window.parent.document).find('.active').eq(1).attr('id');
        var newPageId;
        var tabTitle = $(span).attr('tabTitle');
        if (typeof(tabTitle) == 'undefined') {
            alert('参数错误');
        } else {
            tabTitle = $.parseJSON(tabTitle);
        }
        var id = tabTitle.num;
        // var id = 'index' + Date.parse(new Date()) + Math.floor(Math.random()*1000);
        var name = tabTitle.title;
        var uri = tabTitle.link;
        var closable = 1;
        var item = { 'id': id, 'name': name, 'url': uri, 'closable': closable == 1 ? true : false };
        self.parent.closableTab.addTab(item);
        self.parent.closableTab.resizeMove();
        $(window.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
    }
  	//显示悬浮层
    function showInform() {
        $("div").css("top",$("#salePrice").css("top"));
        $("#inform").show();
    }

    //隐藏悬浮层
    function hiddenInform() {
       $("#inform").hide();
    }

</script>
<style>
    #inform {
        position: absolute;
        top: 425px;
        left: 50px;
        width: 450px;
        max-height: 250px; /* 设置最大高度，当高度达到此值时出现滚动条 */
        z-index: 10;
        background-color: white;
        overflow: auto; /* 自动添加滚动条 */
        box-shadow: 0px 0px 10px #000; /* 外阴影 */
        display: none; /* 默认隐藏 */
    }

    #toggleHistoryBtn {
        text-decoration: none !important;
        font-size: 14px;
        color: #4a91ef;
        float: right;
        margin-right: 10px;
        line-height: 36px;
        display: flex;
        align-items: center;
    }
    
    .toggle-icon-down, .toggle-icon-up {
        display: inline-block;
        margin-right: 6px;
        vertical-align: middle;
        line-height: normal;
        width: 16px;
        height: 16px;
        position: relative;
        top: -1px;
    }
    
    .toggle-icon-down {
        background: url('${pageContext.request.contextPath}/static/images/pulldownicon.png') no-repeat center;
        background-size: contain;
        transform: rotate(0deg);
        transition: transform 0.2s ease;
    }
    
    .toggle-icon-up {
        background: url('${pageContext.request.contextPath}/static/images/pulldownicon.png') no-repeat center;
        background-size: contain;
        transform: rotate(180deg);
        transition: transform 0.2s ease;
    }

    /* 确保标题容器高度一致 */
    .title-container-blue {
        height: 36px;
        line-height: 36px;
    }
    
    /* 确保标题文字垂直居中 */
    .table-title {
        height: 36px;
        line-height: 36px;
        vertical-align: middle;
        display: inline-block;
    }

</style>
<div class="content">
    <div class="formtitle"></div>
    <div  id="desc_div">
        <form method="post" id="addForm" action="${pageContext.request.contextPath}/price/basePriceMaintain/detail.do">
            <input type="hidden" name="skuPriceChangeApplyId" id="skuPriceChangeApplyId" value="${skuPriceChangeApplyDto.id}"/>
            <ul class="payplan">

                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">基本信息</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                                <tr>
                                    <th style="width:50px">订货号</th>
                                    <th style="width:80px">商品名称</th>
                                    <th style="width:80px">主销售部门</th>
                                    <th style="width:80px">核价审核状态</th>
                                    <th style="width:80px">安装费</th>
                                    <th style="width:80px">成本价是否含运费</th>
                                    <th style="width:80px">销售价是否含运费</th>
                                    <th style="width:80px">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        ${skuPriceChangeApplyDto.skuNo}
                                    </td>
                                    <td>
                                        <a class="addtitle" href="javascript:void(0);"
                                           tabTitle='{"num":"viewgoods${skuPriceChangeApplyDto.skuNo}","link":"./goods/goods/viewbaseinfo.do?goodsId=${fn:substringAfter(skuPriceChangeApplyDto.skuNo,'V')}","title":"产品信息"}'>${skuPriceChangeApplyDto.skuName}
                                        </a>

                                    </td>
                                    <td>
                                        ${skuPriceChangeApplyDto.mainDept}
                                    </td>
                                    <td>${skuPriceChangeApplyDto.verifyStatusName}</td>
                                    <td>${installFee}</td>
                                    <td>${skuPriceChangeApplyDto.purchaseContainsFeeStr}</td>
                                    <td>${skuPriceChangeApplyDto.saleContainsFeeStr}</td>

                                    <td>

                                        <span class="edit-user" tabTitle='{"num":"customer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                               "link":"./price/basePriceMaintain/viewEffectPrice.do?skuPriceChangeApplyId=${skuPriceChangeApplyDto.id}","title":"查看修改前价格"}' onclick="toEffectPrice('${skuPriceChangeApplyDto.id}',this)">查看修改前价格</span>


                                        <c:if test="${productMangerOrAsisit == 'true' && skuPriceChangeApplyDto.verifyStatusName!='审核中'}">
                                            <button type="button" class="bt-bg-style bg-light-green bt-small mr10" onclick="toEdit();">编辑</button>
                                        </c:if>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </li>

                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">

                            <div class="table-title nobor">成本价</div>

                            <a class="title-click addtitle" href="javascript:void(0);" tabTitle='{"num":"customer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
					            "link":"./price/basePriceMaintain/purchasePriceTrace.do?skuId=${skuPriceChangeApplyDto.skuId}","title":"查看轨迹"}'>查看轨迹</a>
                        </div>
                        <%--<table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                            <tr>
                                <th style="width:50px">采购价</th>
                                <th style="width:80px">操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>${skuPriceChangeApplyDto.middlePrice}</td>
                                <td><span class="edit-user addtitle" tabTitle='{"num":"customer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
					    "link":"./price/basePriceMaintain/purchasePriceTrace.do?skuId=${skuPriceChangeApplyDto.skuId}","title":"查看轨迹"}'>查看轨迹</span></td>
                            </tr>
                            </tbody>
                        </table>--%>
                    </div>
                </li>

                <li>
                    <div class="parts">
                        <div class="title-container">
                            <div class="table-title nobor">已生效</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                                <tr>
                                    <th style="width:50px">供应商名称</th>
                                    <th style="width:80px">采购成本</th>
                                    <th style="width:80px">生效时间</th>
                                    <th style="width:80px">售后政策</th>
                                </tr>
                            </thead>
                            <tbody>
                            <c:forEach var="purchaseDetail" items="${skuPriceChangeApplyDto.effectPurchaseList}" varStatus="staut">
                                <tr>
                                    <td>${purchaseDetail.traderName}</td>
                                    <td>
                                        ${myfn:toString(purchaseDetail.purchasePrice)}
                                    </td>
                                    <td>${purchaseDetail.modTime}</td>
                                    <td>
                                        <c:if test="${purchaseDetail.supplyPolicyMaintained == 1}">
                                            <span class="title-click addtitle" style='float:none' style="float: right;margin-top: 5px" tabTitle='{"num":"viewaftersales<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                            "link":"${pageContext.request.contextPath}/aftersale/serviceStandard/detail.do?skuNo=${skuPriceChangeApplyDto.skuNo}","title":"查看售后政策"}'>
                                                        查看售后政策
                                                    </span>
                                        </c:if>
                                        <c:if test="${purchaseDetail.supplyPolicyMaintained == 0}">
                                            <span class="title-click addtitle" style='float:none' style="float: right;margin-top: 5px" tabTitle='{"num":"viewaftersales<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                            "link":"${pageContext.request.contextPath}/aftersale/serviceStandard/toAddSupplyAfterSalePolicy.do?skuNo=${skuPriceChangeApplyDto.skuNo}&traderId=${purchaseDetail.traderId}&traderName=${purchaseDetail.traderName}","title":"维护供应商售后政策"}'>
                                                        去维护
                                                    </span>
                                        </c:if>
                                    </td>
                                </tr>
                            </c:forEach>
                            </tbody>
                        </table>
                    </div>
                </li>

                <li>
                    <div class="parts">
                        <div class="title-container">
                            <div class="table-title nobor">本次更新</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                            <tr>
                                <th style="width:50px">供应商名称</th>
                                <th style="width:80px">采购成本</th>
                                <th style="width:80px">变动原因</th>
                                <th style="width:80px">毛利率(终端价)</th>
                                <th style="width:80px">毛利率(经销价)</th>
                                <th style="width:80px">毛利率(集团价)</th>
                                <th style="width:80px">毛利率(电商价)</th>
                                <th style="width:80px">毛利率(科研终端价)</th>
                                <th style="width:80px">售后政策</th>
                            </tr>
                            </thead>
                            <tbody>
                            <c:forEach var="purchaseInfo" items="${skuPriceChangeApplyDto.purchaseInfoList}" varStatus="staut">
                                <tr>
                                    <td>${purchaseInfo.traderName}</td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${purchaseInfo.purchasePriceChange == true}">
                                                <div class="customername pos_rel">
                                                    <span>
                                                        ${myfn:toString(purchaseInfo.purchasePrice)}
                                                        <i class="iconredsigh ml4 采购成本"></i>
                                                    </span>
                                                    <div class="pos_abs customernameshow">原值：${purchaseInfo.lastPurchasePrice == 0 ? "无" : purchaseInfo.lastPurchasePrice}</div>
                                                </div>
                                            </c:when>
                                            <c:otherwise>
                                                ${myfn:toString(purchaseInfo.purchasePrice)}
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td>${purchaseInfo.changeReason}</td>
                                    <td>${purchaseInfo.terminalRate}</td>
                                    <td>${purchaseInfo.distributionRate}</td>
                                    <td>${purchaseInfo.groupRate}</td>
                                    <td>${purchaseInfo.electronicCommerceRate}</td>
                                    <td>${purchaseInfo.researchTerminalRate}</td>
                                    <td>
                                        <c:if test="${purchaseInfo.supplyPolicyMaintained == 1}">
                                            <span class="title-click addtitle" style='float:none' style="float: right;margin-top: 5px" tabTitle='{"num":"viewaftersales<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                            "link":"${pageContext.request.contextPath}/aftersale/serviceStandard/detail.do?skuNo=${skuPriceChangeApplyDto.skuNo}","title":"查看售后政策"}'>
                                                        查看售后政策
                                                    </span>
                                        </c:if>
                                        <c:if test="${purchaseInfo.supplyPolicyMaintained == 0}">
                                            <span class="title-click addtitle" style='float:none' style="float: right;margin-top: 5px" tabTitle='{"num":"viewaftersales<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                            "link":"${pageContext.request.contextPath}/aftersale/serviceStandard/toAddSupplyAfterSalePolicy.do?skuNo=${skuPriceChangeApplyDto.skuNo}&traderId=${purchaseInfo.traderId}&traderName=${purchaseInfo.traderName}","title":"维护供应商售后政策"}'>
                                                        去维护
                                                    </span>
                                        </c:if>
                                    </td>
                                </tr>
                            </c:forEach>
                            </tbody>
                        </table>
                    </div>
                </li>

                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue" id="salePrice">
                           <div class="table-title nobor">销售价</div>
	                        &nbsp;&nbsp;
	                        <img src="<%= basePath%>static/new/img/wenhao.png" onMouseOver="showInform();" onMouseOut="hiddenInform();" style="margin-top:8px;width: 18px;height: 18px"/>
	                    </div>
	                    &nbsp;
	                    <div id="inform" onMouseOut="hiddenInform()">
	                        <p style="margin-left: 10px;margin-top: 5px;font-size:18px;font-family:Arial;font-weight: bolder">销售价填写规范：</p>
	                        <p style="margin-left: 20px">0 < 单价 < 10，可保留2位小数。【例:6.99】</p>
	                        <p style="margin-left: 20px">10 <= 单价 < 100，只可保留1位小数，且小数只能为8或9 【例:69.9】</p>
	                        <p style="margin-left: 20px">100 <= 单价 < 1000，只能填写整数，且个数仅限于0、5、8、9 【例:135】</p>
	                        <p style="margin-left: 20px">1000 <= 单价 < 100000，只能填写整数，且个位数仅限于0、8、9 【例:19999】</p>
	                        <p style="margin-left: 20px">单价超过100000时，只能填写整数，且个位、十位必须为0 【例:158000】</p>
	                        <br>
	                    </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                            <tr>
                                <th style="width:50px">类型</th>
                                <th style="width:80px">价格</th>
                                <th style="width:80px">变动原因</th>
                                <th style="width:80px">生效时间</th>

                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>
                                    市场价
                                </td>
                                <td>
                                    <c:choose>
                                        <c:when test="${skuPriceChangeApplyDto.marketPrice==null}">
                                        </c:when>
                                        <c:otherwise>

                                            <c:choose>
                                                <c:when test="${skuPriceChangeApplyDto.marketPriceChange == true}">
                                                    <div class="customername pos_rel">
                                                        <span>
                                                            ${myfn:toString(skuPriceChangeApplyDto.marketPrice)}
                                                            <i class="iconredsigh ml4 contorlIcon"></i>
                                                        </span>
                                                        <div class="pos_abs customernameshow">原值：${skuPriceChangeApplyDto.lastMarketPrice == 0 ? "无" : skuPriceChangeApplyDto.lastMarketPrice}</div>
                                                    </div>
                                                </c:when>
                                                <c:otherwise>
                                                    ${myfn:toString(skuPriceChangeApplyDto.marketPrice)}
                                                </c:otherwise>
                                            </c:choose>

                                        </c:otherwise>
                                    </c:choose>
                                </td>
                                <td>
                                    ${skuPriceChangeApplyDto.marketPriceChangeReason}
                                </td>
                                <td>${firstEffectTime}</td>
                            </tr>
                            <tr>
                                <td>
                                    终端价
                                </td>
                                <td>
                                    <c:choose>
                                        <c:when test="${skuPriceChangeApplyDto.terminalPrice==null}">
                                        </c:when>
                                        <c:otherwise>
                                            <c:choose>
                                                <c:when test="${skuPriceChangeApplyDto.terminalPriceChange == true}">
                                                    <div class="customername pos_rel">
                                                        <span>
                                                            ${myfn:toString(skuPriceChangeApplyDto.terminalPrice)}
                                                            <i class="iconredsigh ml4 contorlIcon"></i>
                                                        </span>
                                                        <div class="pos_abs customernameshow">原值：${skuPriceChangeApplyDto.lastTerminalPrice == 0 ? "无" : skuPriceChangeApplyDto.lastTerminalPrice}</div>
                                                    </div>
                                                </c:when>
                                                <c:otherwise>
                                                    ${myfn:toString(skuPriceChangeApplyDto.terminalPrice)}
                                                </c:otherwise>
                                            </c:choose>
                                        </c:otherwise>
                                    </c:choose>
                                </td>
                                <td>
                                    ${skuPriceChangeApplyDto.terminalPriceChangeReason}
                                </td>
                                <td>${firstEffectTime}</td>
                            </tr>
                            <tr>
                                <td>
                                    经销价
                                </td>
                                <td>
                                    <c:choose>
                                        <c:when test="${skuPriceChangeApplyDto.distributionPrice==null}">
                                        </c:when>
                                        <c:otherwise>

                                            <c:choose>
                                                <c:when test="${skuPriceChangeApplyDto.distributionPriceChange == true}">
                                                    <div class="customername pos_rel">
                                                        <span>
                                                            ${myfn:toString(skuPriceChangeApplyDto.distributionPrice)}
                                                            <i class="iconredsigh ml4 contorlIcon"></i>
                                                        </span>
                                                        <div class="pos_abs customernameshow">原值：${skuPriceChangeApplyDto.lastDistributionPrice == 0 ? "无" : skuPriceChangeApplyDto.lastDistributionPrice}</div>
                                                    </div>
                                                </c:when>
                                                <c:otherwise>
                                                    ${myfn:toString(skuPriceChangeApplyDto.distributionPrice)}
                                                </c:otherwise>
                                            </c:choose>

                                        </c:otherwise>
                                    </c:choose>
                                </td>
                                <td>
                                    ${skuPriceChangeApplyDto.distributionPriceChangeReason}
                                </td>
                                <td>${firstEffectTime}</td>
                            </tr>
                            <tr>
                                <td>
                                    集团价
                                </td>
                                <td>
                                    <c:choose>
                                        <c:when test="${skuPriceChangeApplyDto.groupPrice==null}">
                                        </c:when>
                                        <c:otherwise>

                                            <c:choose>
                                                <c:when test="${skuPriceChangeApplyDto.groupPriceChange == true}">
                                                    <div class="customername pos_rel">
                                                        <span>
                                                            ${myfn:toString(skuPriceChangeApplyDto.groupPrice)}
                                                            <i class="iconredsigh ml4 contorlIcon"></i>
                                                        </span>
                                                        <div class="pos_abs customernameshow">原值：${skuPriceChangeApplyDto.lastGroupPrice == 0 ? "无" : skuPriceChangeApplyDto.lastGroupPrice}</div>
                                                    </div>
                                                </c:when>
                                                <c:otherwise>
                                                    ${myfn:toString(skuPriceChangeApplyDto.groupPrice)}
                                                </c:otherwise>
                                            </c:choose>

                                        </c:otherwise>
                                    </c:choose>
                                </td>
                                <td>
                                    ${skuPriceChangeApplyDto.groupPriceChangeReason}
                                </td>
                                <td>${firstEffectTime}</td>
                            </tr>
                            <tr>
                                <td>
                                    电商价
                                </td>
                                <td>
                                    <c:choose>
                                        <c:when test="${skuPriceChangeApplyDto.electronicCommercePrice==null}">
                                        </c:when>
                                        <c:otherwise>

                                            <c:choose>
                                                <c:when test="${skuPriceChangeApplyDto.electronicCommercePriceChange == true}">
                                                    <div class="customername pos_rel">
                                                        <span>
                                                            ${myfn:toString(skuPriceChangeApplyDto.electronicCommercePrice)}
                                                            <i class="iconredsigh ml4 contorlIcon"></i>
                                                        </span>
                                                        <div class="pos_abs customernameshow">原值：${skuPriceChangeApplyDto.lastElectronicCommercePrice == 0 ? "无" : skuPriceChangeApplyDto.lastElectronicCommercePrice}</div>
                                                    </div>
                                                </c:when>
                                                <c:otherwise>
                                                    ${myfn:toString(skuPriceChangeApplyDto.electronicCommercePrice)}
                                                </c:otherwise>
                                            </c:choose>

                                        </c:otherwise>
                                    </c:choose>
                                </td>
                                <td>
                                    ${skuPriceChangeApplyDto.electronicCommercePriceChangeReason}
                                </td>
                                <td>${firstEffectTime}</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </li>

                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">审核记录</div>
                            <c:if test="${fn:length(historicActivityInstance) > 1}">
                                <a href="javascript:void(0);" id="toggleHistoryBtn" onclick="toggleHistory()">
                                    <span id="toggleIcon" class="toggle-icon-down"></span>
                                    <span id="showMoreText">显示更多</span>
                                </a>
                            </c:if>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                                <tr>
                                    <th style="width:50px">操作人</th>
                                    <th style="width:80px">操作时间</th>
                                    <th style="width:80px">操作事项</th>
                                    <th style="width:80px">备注</th>
                                </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td colspan="4">
                                    <c:if test="${null!=historicInfo.taskInfo and (taskInfo.assignee == curr_user.username or historicInfo.candidateUserMap['belong']) }">
                                        <button type="button" class="bt-bg-style bg-light-green bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"300px","title":"操作确认","link":"./auditResult.do?taskId=${taskId}&skuPriceChangeApplyId=${skuPriceChangeApplyDto.id}&pass=true"}'>审核通过</button>
                                        <button type="button" class="bt-bg-style bg-light-orange bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"300px","title":"操作确认","link":"./auditResult.do?taskId=${taskId}&skuPriceChangeApplyId=${skuPriceChangeApplyDto.id}&pass=false"}'>审核不通过</button>
                                    </c:if>
                                </td>
                            </tr>
                            
                            <c:choose>
                                <%-- 无审核记录 --%>
                                <c:when test="${empty historicActivityInstance}">
                                    <tr>
                                        <td colspan="4">暂无审核记录。</td>
                                    </tr>
                                </c:when>
                                
                                <%-- 只有一条审核记录 --%>
                                <c:when test="${fn:length(historicActivityInstance) == 1}">
                                    <c:forEach var="hi" items="${historicActivityInstance}" varStatus="status">
                                        <c:if test="${not empty hi.activityName}">
                                            <tr>
                                                <td>
                                                    <c:choose>
                                                        <c:when test="${hi.activityType == 'startEvent'}">
                                                            ${startUser}
                                                        </c:when>
                                                        <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                                        </c:when>
                                                        <c:otherwise>
                                                            <c:if test="${status.count==1}">
                                                                ${verifyUsers}
                                                            </c:if>
                                                            <c:if test="${status.count!=1}">
                                                                ${hi.assignee}
                                                            </c:if>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </td>
                                                <td><fmt:formatDate value="${hi.endTime}" pattern="yyyy-MM-dd HH:mm:ss" /></td>
                                                <td>
                                                    <c:choose>
                                                        <c:when test="${hi.activityType == 'startEvent'}">
                                                            开始
                                                        </c:when>
                                                        <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                                            结束
                                                        </c:when>
                                                        <c:otherwise>
                                                            ${hi.activityName}
                                                        </c:otherwise>
                                                    </c:choose>
                                                </td>
                                                <td class="font-red">${commentMap[hi.taskId]}</td>
                                            </tr>
                                        </c:if>
                                    </c:forEach>
                                </c:when>
                                
                                <%-- 有多条审核记录 --%>
                                <c:otherwise>
                                    <%-- 显示最新的一条记录 --%>
                                    <c:forEach var="hi" items="${historicActivityInstance}" end="0" varStatus="status">
                                        <c:if test="${not empty hi.activityName}">
                                            <tr>
                                                <td>
                                                    <c:choose>
                                                        <c:when test="${hi.activityType == 'startEvent'}">
                                                            ${startUser}
                                                        </c:when>
                                                        <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                                        </c:when>
                                                        <c:otherwise>
                                                            <c:if test="${status.count==1}">
                                                                ${verifyUsers}
                                                            </c:if>
                                                            <c:if test="${status.count!=1}">
                                                                ${hi.assignee}
                                                            </c:if>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </td>
                                                <td><fmt:formatDate value="${hi.endTime}" pattern="yyyy-MM-dd HH:mm:ss" /></td>
                                                <td>
                                                    <c:choose>
                                                        <c:when test="${hi.activityType == 'startEvent'}">
                                                            开始
                                                        </c:when>
                                                        <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                                            结束
                                                        </c:when>
                                                        <c:otherwise>
                                                            ${hi.activityName}
                                                        </c:otherwise>
                                                    </c:choose>
                                                </td>
                                                <td class="font-red">${commentMap[hi.taskId]}</td>
                                            </tr>
                                        </c:if>
                                    </c:forEach>
                                    
                                    <%-- 折叠部分 --%>
                                    <tbody id="historyRecords" style="display:none;">
                                        <c:forEach var="hi" items="${historicActivityInstance}" begin="1" varStatus="status">
                                            <c:if test="${not empty hi.activityName}">
                                                <tr>
                                                    <td>
                                                        <c:choose>
                                                            <c:when test="${hi.activityType == 'startEvent'}">
                                                                ${startUser}
                                                            </c:when>
                                                            <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                                            </c:when>
                                                            <c:otherwise>
                                                                ${hi.assignee}
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                    <td><fmt:formatDate value="${hi.endTime}" pattern="yyyy-MM-dd HH:mm:ss" /></td>
                                                    <td>
                                                        <c:choose>
                                                            <c:when test="${hi.activityType == 'startEvent'}">
                                                                开始
                                                            </c:when>
                                                            <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                                                结束
                                                            </c:when>
                                                            <c:otherwise>
                                                                ${hi.activityName}
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                    <td class="font-red">${commentMap[hi.taskId]}</td>
                                                </tr>
                                            </c:if>
                                        </c:forEach>
                                    </tbody>
                                </c:otherwise>
                            </c:choose>
                            </tbody>
                        </table>
                        
                        <script type="text/javascript">
                            function toggleHistory() {
                                var historyRecords = document.getElementById("historyRecords");
                                var toggleIcon = document.getElementById("toggleIcon");
                                var showMoreText = document.getElementById("showMoreText");
                                
                                if (historyRecords.style.display === "none") {
                                    historyRecords.style.display = "table-row-group";
                                    toggleIcon.className = "toggle-icon-up";
                                    showMoreText.innerText = "折叠显示";
                                } else {
                                    historyRecords.style.display = "none";
                                    toggleIcon.className = "toggle-icon-down";
                                    showMoreText.innerText = "显示更多";
                                }
                            }
                        </script>
                    </div>
                </li>

            </ul>
        </form>
    </div>
</div>
<%@ include file="../common/footer.jsp"%>