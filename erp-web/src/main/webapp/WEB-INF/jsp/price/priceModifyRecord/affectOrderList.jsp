<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="可处理订单" scope="application" />
<%@ include file="../../common/common.jsp"%>
<style type="text/css">

</style>
<script type="text/javascript" src="<%= basePath %>static/js/price/priceModifyRecord/affectOrderList.js?rnd=${resourceVersionKey}"></script>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<div class="formpublic">

    <form action="${pageContext.request.contextPath}/price/skuPriceModifyRecord/showAffectOrderListView.do" id="myform1" method="post">
        <blockquote class="layui-elem-quote layui-text" style="border-left: 5px solid #3384ef">
            温馨提示：30天内，跟单中的报价单，未收款的订单建议通知客户此变更，点击确认代表已处理
        </blockquote>
    </form>
    <hr>
    <div>
        <table class="table table-bordered table-striped table-condensed table-centered" id="cus">
            <thead>
            <tr>
                <th class="text-left wid15">订单号</th>
                <th class="wid12">提醒日期</th>
                <th class="wid12">处理日期</th>
                <th class="wid10">操作</th>
            </tr>
            </thead>
            <tbody>
            <c:if test="${not empty list}">
                <c:forEach items="${list}" var="affectOrder" varStatus="status">
                    <tr>
                        <td>${affectOrder.orderNo }</td>
                        <td>${affectOrder.notifyTimeString}</td>
                        <td>--</td>
                        <td>
                            <a href="javascript:void(0);" style="color: #3384ef" onclick="comfirmPriceChange(${affectOrder.priceChangeAffectOrderId})">确认</a>
                        </td>
                    </tr>
                </c:forEach>
            </c:if>
            </tbody>
        </table>
        <c:if test="${empty list }">
            <!-- 查询无结果弹出 -->
            <div class="noresult"> 无结果</div>
        </c:if>
        <tags:page page="${page}"/>
    </div>
</div>
</body>

</html>
