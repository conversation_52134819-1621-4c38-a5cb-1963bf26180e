package com.vedeng.erp.buyorder.service.impl;

import com.vedeng.erp.aftersale.service.BuyorderExpenseItemApiService;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseItemDetailDto;
import com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseDetailEntity;
import com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseEntity;
import com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseItemDetailEntity;
import com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseItemEntity;
import com.vedeng.erp.buyorderexpense.mapper.BuyorderExpenseDetailMapper;
import com.vedeng.erp.buyorderexpense.mapper.BuyorderExpenseItemDetailMapper;
import com.vedeng.erp.buyorderexpense.mapper.BuyorderExpenseItemMapper;
import com.vedeng.erp.buyorderexpense.mapper.BuyorderExpenseMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Slf4j
@Service
public class BuyorderExpenseItemServiceImpl implements BuyorderExpenseItemApiService {

    @Autowired
    private BuyorderExpenseItemMapper buyorderExpenseItemMapper;
    @Autowired
    private BuyorderExpenseItemDetailMapper buyorderExpenseItemDetailMapper;
    @Autowired
    private BuyorderExpenseMapper buyorderExpenseMapper;
    @Autowired
    private BuyorderExpenseDetailMapper buyorderExpenseDetailMapper;

    @Override
    public BuyorderExpenseItemDetailDto getBuyorderExpenseDetails(Integer buyorderExpenseDetailId) {
        BuyorderExpenseItemDetailEntity buyorderExpenseItemDetailEntity = buyorderExpenseItemDetailMapper.selectByPrimaryKey(buyorderExpenseDetailId);
        BuyorderExpenseItemDetailDto detailDto = new BuyorderExpenseItemDetailDto();
        detailDto.setBuyorderExpenseItemId(buyorderExpenseItemDetailEntity.getBuyorderExpenseItemId());
        detailDto.setPrice(buyorderExpenseItemDetailEntity.getPrice());
        detailDto.setSku(buyorderExpenseItemDetailEntity.getSku());
        return detailDto;
    }

    @Override
    public BuyorderExpenseDto getBuyOrderExpense(Integer buyorderExpenseItemId) {
        log.info("buyorderExpenseItemId:{}",buyorderExpenseItemId);
        //T_BUYORDER_EXPENSE_ITEM
        BuyorderExpenseItemEntity buyorderExpenseItemEntity = buyorderExpenseItemMapper.selectByPrimaryKey(buyorderExpenseItemId);
        //T_BUYORDER_EXPENSE_DETAIL
        BuyorderExpenseDetailEntity buyorderExpenseDetailEntity = buyorderExpenseDetailMapper.selectByBuyorderExpenseId(buyorderExpenseItemEntity.getBuyorderExpenseId());
        //T_BUYORDER_EXPENSE
            BuyorderExpenseEntity buyorderExpenseEntity = buyorderExpenseMapper.selectByPrimaryKey(buyorderExpenseItemEntity.getBuyorderExpenseId());
            BuyorderExpenseDto buyorderExpenseDto = new BuyorderExpenseDto();
            buyorderExpenseDto.setBuyorderExpenseNo(buyorderExpenseEntity.getBuyorderExpenseNo());
            buyorderExpenseDto.setCreator(buyorderExpenseEntity.getCreator());
            buyorderExpenseDto.setTraderId(buyorderExpenseDetailEntity.getTraderId());
            return buyorderExpenseDto;
    }

    @Override
    public BuyorderExpenseDto getBuyOrderExpenseById(Integer id) {
        BuyorderExpenseEntity buyorderExpenseEntity = buyorderExpenseMapper.selectByPrimaryKey(id);
        BuyorderExpenseDto buyorderExpenseDto = new BuyorderExpenseDto();
        buyorderExpenseDto.setBuyorderExpenseNo(buyorderExpenseEntity.getBuyorderExpenseNo());
        buyorderExpenseDto.setOrderType(buyorderExpenseEntity.getOrderType());
        return buyorderExpenseDto;
    }

    @Override
    public BigDecimal getBuyOrderExpenseByOrderNoAndBuyorderExpenseItemId(String orderNo, Integer expenseItemId) {
        return buyorderExpenseMapper.getBuyOrderExpenseByOrderNoAndBuyorderExpenseItemId(orderNo,expenseItemId);
    }

}
