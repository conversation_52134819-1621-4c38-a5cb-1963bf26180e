package com.vedeng.price.dto;

import java.math.BigDecimal;

public class GoodSalePrice {

    //默认的销售价
    private BigDecimal defaultSalePrice;

    private String skuNo;

    public GoodSalePrice(String skuNo, BigDecimal defaultSalePrice) {
        this.skuNo = skuNo;
        this.defaultSalePrice = defaultSalePrice;
    }

    public BigDecimal getDefaultSalePrice() {
        return defaultSalePrice;
    }

    public void setDefaultSalePrice(BigDecimal defaultSalePrice) {
        this.defaultSalePrice = defaultSalePrice;
    }

    public String getSkuNo() {
        return skuNo;
    }

    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }
}
