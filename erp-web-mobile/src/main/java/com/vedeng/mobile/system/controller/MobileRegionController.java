package com.vedeng.mobile.system.controller;

import com.vedeng.common.core.base.BaseResponseCode;
import com.vedeng.common.core.base.R;
import com.vedeng.mobile.system.dto.RegionDto;
import com.vedeng.mobile.system.dto.RegionForMobile;
import com.vedeng.mobile.system.service.RegionService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @create 2022/1/10 16:40
 */
@RestController
@RequestMapping("/mobile")
@RequiredArgsConstructor
public class MobileRegionController {

    private final RegionService regionService;


    @ResponseBody
    @RequestMapping(value = "/region/queryAllRegions")
    public R<RegionForMobile> queryAllRegions() {
        return R.success(regionService.queryRegionLevelTwo(100000));
    }


    /**
     * 根据父级查询子级所有区域
     * @param request
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/region/queryRegionByParentId")
    public R<RegionForMobile> queryRegionByParentId(HttpServletRequest request) {
        String parentId = request.getParameter("parentId");
        if(StringUtils.isEmpty(parentId)){
            return R.error(BaseResponseCode.BAD_REQUEST.getCode(),"parentId不能为空");
        }
        return R.success(regionService.queryRegionByParentId(Integer.parseInt(parentId)));
    }


    /**
     * 省市区三级联动
     */
    @PostMapping("/regions")
    public R<RegionDto> regions() {
        return R.success(regionService.queryAllRegions());
    }
}
