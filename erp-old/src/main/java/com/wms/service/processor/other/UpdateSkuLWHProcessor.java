package com.wms.service.processor.other;

import com.vedeng.common.util.StringUtil;
import com.vedeng.goods.dao.CoreSkuGenerateMapper;
import com.vedeng.goods.model.CoreSkuGenerate;
import com.wms.dto.WmsLwhDto;
import com.wms.service.processor.AbstractWMSCalllBackProcessor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 商品长宽高体积处理器
 */
@Service
public class UpdateSkuLWHProcessor extends AbstractWMSCalllBackProcessor<List<WmsLwhDto>> {


    @Resource
    private CoreSkuGenerateMapper coreSkuGenerateMapper;

    @Override
    protected boolean needIdempotentValidator() {
        return false;
    }

    @Override
    protected String getBusinessKey(List<WmsLwhDto> requestBean) {
        return null;
    }

    @Override
    protected void commonValidator(List<WmsLwhDto> wmsLwhDtoList) throws Exception {

        if(CollectionUtils.isEmpty(wmsLwhDtoList)){
            throw new Exception("skub列表不能为空!");
        }

        for(WmsLwhDto wmsLwhDto : wmsLwhDtoList ){

            if(StringUtil.isEmpty(wmsLwhDto.getSku())){
                throw new Exception("skub不能为空!");
            }
        }
    }


    @Override
    protected void doDealWithRequest(List<WmsLwhDto> wmsLwhDtoList) throws Exception {

        wmsLwhDtoList.stream().forEach(wmsLwhDto -> {

            CoreSkuGenerate coreSkuGenerate = new CoreSkuGenerate();
            coreSkuGenerate.setSkuNo(wmsLwhDto.getSku());
            coreSkuGenerate.setGoodsLength(wmsLwhDto.getSkuLength());
            coreSkuGenerate.setGoodsWidth(wmsLwhDto.getGrossWeight());
            coreSkuGenerate.setGoodsHeight(wmsLwhDto.getSkuHigh());
            coreSkuGenerate.setGrossWeight(wmsLwhDto.getGrossWeight());
            coreSkuGenerate.setSkuNo(wmsLwhDto.getSku());
            coreSkuGenerateMapper.updateSkuBySkuNo(coreSkuGenerate);
        });

    }
}
