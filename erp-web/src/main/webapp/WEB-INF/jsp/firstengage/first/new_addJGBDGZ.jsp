<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>
            上传加盖贝登公章注册证
    </title>
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/new/css/common/global.css">
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/new/css/pages/firstengage/first/add.css?rnd=${resourceVersionKey}">
</head>

<body>
<form action="" id="form_submit"
      class="J-form" method="POST">
    <input type="hidden" name="firstEngageId" value="${firstEngage.firstEngageId }">
    <input type="hidden" name="formToken" value="${formToken}"/>
    <input type="hidden" name="registration.registrationNumberId"
           value="${firstEngage.registration.registrationNumberId }">
    <div class="form-wrap">
        <div class="form-container base-form form-span-8">




<%--            <div style="clear: both"></div>--%>
            <div class="form-block">

                <div class="form-cnt">

                    <div class="form-item one yz">
                        <div class="form-label"><span class="c-title" data-a="注册证附件/备案凭证附件 （贝）："
                                                                                 data-b="注册证附件/备案凭证附件 （贝）">注册证附件/备案凭证附件 （贝）：</span></div>
                        <div class="form-fields">
                            <div class="J-upload"></div>
                            <input type="hidden" class="J-upload-data" value='${zcZBMapList}'>
                            <%--<div class="form-fields-tip">-最多上传5张。</div>--%>
                            <div class="feedback-block" wrapfor="upload1"></div>
                            <div class="feedback-block J-upload-error">
                                <label class="error" for="" style="display: none;"></label>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="form-btn" style="margin-top: 30px">
                    <div class="form-item">
                        <div class="form-fields">
                            <button class="btn btn-blue btn-large" type="button" onclick="subForm()">保存</button>

                            <a href="javascript:void(0)" onclick="cancel()"
                               class="btn btn-large">取消</a>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<script type="text/json" class="J-old-data">
        ${oldStandCategoryList}




</script>

<script type="text/json" class="J-new-data">
        ${newStandCategoryList}




</script>


<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/pikaday.2.1.0.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/util.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/select.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.validate.js"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/upload.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/lv-select.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/inputSuggest.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/artDialog/2.0.0/artDialog.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/dialogSearch.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/suggestSelect.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src='${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/layer.js'></script>
<script src="${pageContext.request.contextPath}/static/new/js/pages/firstengage/first/new_addJGBDGZ.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/pages/goods/commons/goodsStorageCondition.js?rnd=${resourceVersionKey}"></script>
</body>