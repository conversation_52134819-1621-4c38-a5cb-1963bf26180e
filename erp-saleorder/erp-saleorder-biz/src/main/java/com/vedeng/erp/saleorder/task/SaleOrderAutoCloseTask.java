package com.vedeng.erp.saleorder.task;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.saleorder.service.SaleOrderAutoCloseService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 销售订单自动关闭定时任务
 *
 * 功能说明：
 * 1. 检查审核通过且未付款的销售订单
 * 2. 对于超过60天的订单自动关闭
 * 3. 对于剩余时间小于24小时的订单发送企业微信预警（带24小时去重机制）
 * 4. 清理过期的预警去重记录，释放Redis内存
 * 5. 支持历史数据处理模式，通过参数控制执行逻辑
 *
 * 参数说明：
 * - 无参数或空参数：执行正常的定时任务逻辑（自动关闭+预警通知+清理记录）
 * - "HISTORICAL_DATA"：执行历史数据批量处理模式，批量关闭超过60天的历史订单
 * - 订单号格式：支持单个订单号或多个订单号（逗号分隔），如"*********"或"*********,*********,*********"
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
@Component
@JobHandler(value = "SaleOrderAutoCloseTask")
@Slf4j
public class SaleOrderAutoCloseTask extends AbstractJobHandler {

    @Autowired
    private SaleOrderAutoCloseService saleOrderAutoCloseService;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("==================销售订单自动关闭定时任务开始====================");
        log.info("销售订单自动关闭定时任务开始执行，参数: {}", param);

        try {
            // 判断参数类型并执行相应逻辑
            if (param != null && !param.trim().isEmpty()) {
                String trimmedParam = param.trim();

                // 历史数据处理模式
                if ("HISTORICAL_DATA".equalsIgnoreCase(trimmedParam)) {
                    XxlJobLogger.log("执行历史数据处理模式");
                    log.info("执行历史数据处理模式");

                    int historicalProcessedCount = saleOrderAutoCloseService.processHistoricalData();
                    XxlJobLogger.log("历史数据处理完成，处理订单数量: {}", historicalProcessedCount);
                    log.info("历史数据处理完成，处理订单数量: {}", historicalProcessedCount);

                    XxlJobLogger.log("==================销售订单历史数据处理任务结束====================");
                    return ReturnT.SUCCESS;
                } else {
                    // 订单号批量处理模式
                    XxlJobLogger.log("执行订单号批量处理模式，参数: {}", trimmedParam);
                    log.info("执行订单号批量处理模式，参数: {}", trimmedParam);

                    // 解析订单号列表（支持单个订单号或逗号分隔的多个订单号）
                    List<String> orderNoList = Arrays.asList(trimmedParam.split(","));
                    // 去除空格
                    orderNoList = orderNoList.stream()
                            .map(String::trim)
                            .filter(orderNo -> !orderNo.isEmpty())
                            .collect(Collectors.toList());

                    if (orderNoList.isEmpty()) {
                        XxlJobLogger.log("订单号参数为空，执行正常逻辑");
                        log.warn("订单号参数为空，执行正常逻辑");
                    } else {
                        String result = saleOrderAutoCloseService.processOrdersByOrderNoList(orderNoList);
                        XxlJobLogger.log("订单号批量处理结果: {}", result);
                        log.info("订单号批量处理结果: {}", result);

                        // 对指定订单号进行消息提醒处理
                        try {
                            int warningCount = saleOrderAutoCloseService.processWarningNotifications(orderNoList);
                            XxlJobLogger.log("指定订单号预警通知数量: {}", warningCount);
                            log.info("指定订单号预警通知数量: {}", warningCount);
                        } catch (Exception e) {
                            XxlJobLogger.log("指定订单号预警通知处理失败: {}", e.getMessage());
                            log.error("指定订单号预警通知处理失败", e);
                            // 预警通知失败不影响主流程，继续执行
                        }

                        XxlJobLogger.log("==================销售订单批量处理任务结束====================");
                        return ReturnT.SUCCESS;
                    }
                }
            }

            // 正常定时任务逻辑
            XxlJobLogger.log("执行正常定时任务逻辑");
            log.info("执行正常定时任务逻辑");

            // 处理自动关闭
            int autoClosedCount = saleOrderAutoCloseService.processAutoCloseOrders();
            XxlJobLogger.log("自动关闭订单数量: {}", autoClosedCount);
            log.info("自动关闭订单数量: {}", autoClosedCount);

            // 处理24小时预警通知
            int warningCount = saleOrderAutoCloseService.processWarningNotifications();
            XxlJobLogger.log("发送预警通知数量: {}", warningCount);
            log.info("发送预警通知数量: {}", warningCount);

            // 清理过期的预警记录
            int cleanedCount = saleOrderAutoCloseService.cleanExpiredWarningRecords();
            XxlJobLogger.log("清理过期预警记录数量: {}", cleanedCount);
            log.info("清理过期预警记录数量: {}", cleanedCount);

            XxlJobLogger.log("==================销售订单自动关闭定时任务结束====================");
            log.info("销售订单自动关闭定时任务执行完成，关闭订单数：{}，预警通知数：{}，清理记录数：{}", autoClosedCount, warningCount, cleanedCount);

            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log("销售订单自动关闭定时任务执行失败: {}", e.getMessage());
            log.error("销售订单自动关闭定时任务执行失败", e);
            return ReturnT.FAIL;
        }
    }
}
