package com.vedeng.erp.aftersale.domain.entity;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class AfterSaleServiceStandardInfoInstallArea {

    /**
     * 安装区域ID
     */
    private Long installAreaId;

    /**
     * SERVICE_STANDARD_INFO_ID
     */
    private Long serviceStandardInfoId;

    /**
     * 省市区json格式值
     */
    private String provinceCityJsonvalue;

    /**
     * 创建者
     */
    private Integer creator;

    /**
     * 更新者
     */
    private Integer updator;

    /**
     * 添加时间
     */
    private String addTime;

    /**
     * 更新时间
     */
    private String modTime;

}