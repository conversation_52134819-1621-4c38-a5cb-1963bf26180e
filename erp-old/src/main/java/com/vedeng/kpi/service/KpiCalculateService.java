package com.vedeng.kpi.service;

import com.alibaba.otter.canal.protocol.CanalEntry;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.kpi.model.DO.KpiOrderLogDo;
import com.vedeng.order.model.Saleorder;

import java.util.Map;

/**
 * <AUTHOR>
 * @date created in 2020/6/15 10:05
 */
public interface KpiCalculateService {

    /**
     * 监测数据库数据变化
     * @param tableName 数据表
     * @param columnValueMap 数据
     * @param executeTime 执行时间
     */
    void monitorDataChange(CanalEntry.RowChange rowChange, String tableName, Map<String, String> columnValueMap, Long executeTime);


    /**
     * 判断订单是否计入业绩，并计算业绩额
     * a)该销售订单本月内有到款流水记录（不含账期）
     * b)此订单客户累计实付金额 / 订单实际金额>=95%
     * c)订单未被计算过业绩
     * d)订单不是特殊订单（新增的条件）
     * @param saleorder 订单
     */
    Long checkSaleorderPerformanceOfSaleorder(Saleorder saleorder);


    /**
     * 售后结款时，计算业绩
     * @param afterSales 售后单
     * @param executeTime 执行时间
     */
    void checkSalesPerformanceOfAfterSales(AfterSales afterSales, Long executeTime);


    void checkNewBusinessChance(KpiOrderLogDo kpiOrderLogDo);

    void checkKpiDateOfKpiOrderLog(String saleorderNo);

    void checkKpiDateOfAfterSales(String afterSalesNo);
}
