<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.buyorder.mapper.ExpressMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.buyorder.domain.entity.Express">
    <id column="EXPRESS_ID" jdbcType="INTEGER" property="expressId" />
    <result column="LOGISTICS_ID" jdbcType="INTEGER" property="logisticsId" />
    <result column="LOGISTICS_NO" jdbcType="VARCHAR" property="logisticsNo" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="DELIVERY_TIME" jdbcType="BIGINT" property="deliveryTime" />
    <result column="ARRIVAL_STATUS" jdbcType="BIT" property="arrivalStatus" />
    <result column="ARRIVAL_TIME" jdbcType="BIGINT" property="arrivalTime" />
    <result column="DELIVERY_FROM" jdbcType="INTEGER" property="deliveryFrom" />
    <result column="LOGISTICS_COMMENTS" jdbcType="VARCHAR" property="logisticsComments" />
    <result column="IS_ENABLE" jdbcType="BIT" property="isEnable" />
    <result column="PAYMENT_TYPE" jdbcType="BIT" property="paymentType" />
    <result column="CARD_NUMBER" jdbcType="VARCHAR" property="cardNumber" />
    <result column="BUSINESS_TYPE" jdbcType="BIT" property="businessType" />
    <result column="REAL_WEIGHT" jdbcType="DECIMAL" property="realWeight" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
    <result column="AMOUNT_WEIGHT" jdbcType="DECIMAL" property="amountWeight" />
    <result column="MAIL_GOODS" jdbcType="VARCHAR" property="mailGoods" />
    <result column="MAIL_GOODS_NUM" jdbcType="INTEGER" property="mailGoodsNum" />
    <result column="IS_PROTECT_PRICE" jdbcType="BIT" property="isProtectPrice" />
    <result column="PROTECT_PRICE" jdbcType="DECIMAL" property="protectPrice" />
    <result column="IS_RECEIPT" jdbcType="BIT" property="isReceipt" />
    <result column="MAIL_COMMTENTS" jdbcType="VARCHAR" property="mailCommtents" />
    <result column="SENT_SMS" jdbcType="BIT" property="sentSms" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="TRAVELING_BY_TICKET" jdbcType="BIT" property="travelingByTicket" />
    <result column="IS_INVOICING" jdbcType="BIT" property="isInvoicing" />
    <result column="WMS_ORDER_NO" jdbcType="VARCHAR" property="wmsOrderNo" />
  </resultMap>

  <sql id="Base_Column_List">
    EXPRESS_ID, LOGISTICS_ID, LOGISTICS_NO, COMPANY_ID, DELIVERY_TIME, ARRIVAL_STATUS, 
    ARRIVAL_TIME, DELIVERY_FROM, LOGISTICS_COMMENTS, IS_ENABLE, PAYMENT_TYPE, CARD_NUMBER, 
    BUSINESS_TYPE, REAL_WEIGHT, NUM, AMOUNT_WEIGHT, MAIL_GOODS, MAIL_GOODS_NUM, IS_PROTECT_PRICE, 
    PROTECT_PRICE, IS_RECEIPT, MAIL_COMMTENTS, SENT_SMS, ADD_TIME, CREATOR, MOD_TIME, 
    UPDATER, TRAVELING_BY_TICKET, IS_INVOICING, WMS_ORDER_NO
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_EXPRESS
    where EXPRESS_ID = #{expressId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_EXPRESS
    where EXPRESS_ID = #{expressId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="EXPRESS_ID" keyProperty="expressId" parameterType="com.vedeng.erp.buyorder.domain.entity.Express" useGeneratedKeys="true">
    insert into T_EXPRESS (LOGISTICS_ID, LOGISTICS_NO, COMPANY_ID, 
      DELIVERY_TIME, ARRIVAL_STATUS, ARRIVAL_TIME, 
      DELIVERY_FROM, LOGISTICS_COMMENTS, IS_ENABLE, 
      PAYMENT_TYPE, CARD_NUMBER, BUSINESS_TYPE, 
      REAL_WEIGHT, NUM, AMOUNT_WEIGHT, 
      MAIL_GOODS, MAIL_GOODS_NUM, IS_PROTECT_PRICE, 
      PROTECT_PRICE, IS_RECEIPT, MAIL_COMMTENTS, 
      SENT_SMS, ADD_TIME, CREATOR, 
      MOD_TIME, UPDATER, TRAVELING_BY_TICKET, 
      IS_INVOICING, WMS_ORDER_NO)
    values (#{logisticsId,jdbcType=INTEGER}, #{logisticsNo,jdbcType=VARCHAR}, #{companyId,jdbcType=INTEGER}, 
      #{deliveryTime,jdbcType=BIGINT}, #{arrivalStatus,jdbcType=BIT}, #{arrivalTime,jdbcType=BIGINT}, 
      #{deliveryFrom,jdbcType=INTEGER}, #{logisticsComments,jdbcType=VARCHAR}, #{isEnable,jdbcType=BIT}, 
      #{paymentType,jdbcType=BIT}, #{cardNumber,jdbcType=VARCHAR}, #{businessType,jdbcType=BIT}, 
      #{realWeight,jdbcType=DECIMAL}, #{num,jdbcType=INTEGER}, #{amountWeight,jdbcType=DECIMAL}, 
      #{mailGoods,jdbcType=VARCHAR}, #{mailGoodsNum,jdbcType=INTEGER}, #{isProtectPrice,jdbcType=BIT}, 
      #{protectPrice,jdbcType=DECIMAL}, #{isReceipt,jdbcType=BIT}, #{mailCommtents,jdbcType=VARCHAR}, 
      #{sentSms,jdbcType=BIT}, #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, 
      #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}, #{travelingByTicket,jdbcType=BIT}, 
      #{isInvoicing,jdbcType=BIT}, #{wmsOrderNo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="EXPRESS_ID" keyProperty="expressId" parameterType="com.vedeng.erp.buyorder.domain.entity.Express" useGeneratedKeys="true">
    insert into T_EXPRESS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="logisticsId != null">
        LOGISTICS_ID,
      </if>
      <if test="logisticsNo != null">
        LOGISTICS_NO,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="deliveryTime != null">
        DELIVERY_TIME,
      </if>
      <if test="arrivalStatus != null">
        ARRIVAL_STATUS,
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME,
      </if>
      <if test="deliveryFrom != null">
        DELIVERY_FROM,
      </if>
      <if test="logisticsComments != null">
        LOGISTICS_COMMENTS,
      </if>
      <if test="isEnable != null">
        IS_ENABLE,
      </if>
      <if test="paymentType != null">
        PAYMENT_TYPE,
      </if>
      <if test="cardNumber != null">
        CARD_NUMBER,
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE,
      </if>
      <if test="realWeight != null">
        REAL_WEIGHT,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="amountWeight != null">
        AMOUNT_WEIGHT,
      </if>
      <if test="mailGoods != null">
        MAIL_GOODS,
      </if>
      <if test="mailGoodsNum != null">
        MAIL_GOODS_NUM,
      </if>
      <if test="isProtectPrice != null">
        IS_PROTECT_PRICE,
      </if>
      <if test="protectPrice != null">
        PROTECT_PRICE,
      </if>
      <if test="isReceipt != null">
        IS_RECEIPT,
      </if>
      <if test="mailCommtents != null">
        MAIL_COMMTENTS,
      </if>
      <if test="sentSms != null">
        SENT_SMS,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="travelingByTicket != null">
        TRAVELING_BY_TICKET,
      </if>
      <if test="isInvoicing != null">
        IS_INVOICING,
      </if>
      <if test="wmsOrderNo != null">
        WMS_ORDER_NO,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="logisticsId != null">
        #{logisticsId,jdbcType=INTEGER},
      </if>
      <if test="logisticsNo != null">
        #{logisticsNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="arrivalStatus != null">
        #{arrivalStatus,jdbcType=BIT},
      </if>
      <if test="arrivalTime != null">
        #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="deliveryFrom != null">
        #{deliveryFrom,jdbcType=INTEGER},
      </if>
      <if test="logisticsComments != null">
        #{logisticsComments,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null">
        #{isEnable,jdbcType=BIT},
      </if>
      <if test="paymentType != null">
        #{paymentType,jdbcType=BIT},
      </if>
      <if test="cardNumber != null">
        #{cardNumber,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=BIT},
      </if>
      <if test="realWeight != null">
        #{realWeight,jdbcType=DECIMAL},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="amountWeight != null">
        #{amountWeight,jdbcType=DECIMAL},
      </if>
      <if test="mailGoods != null">
        #{mailGoods,jdbcType=VARCHAR},
      </if>
      <if test="mailGoodsNum != null">
        #{mailGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="isProtectPrice != null">
        #{isProtectPrice,jdbcType=BIT},
      </if>
      <if test="protectPrice != null">
        #{protectPrice,jdbcType=DECIMAL},
      </if>
      <if test="isReceipt != null">
        #{isReceipt,jdbcType=BIT},
      </if>
      <if test="mailCommtents != null">
        #{mailCommtents,jdbcType=VARCHAR},
      </if>
      <if test="sentSms != null">
        #{sentSms,jdbcType=BIT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="travelingByTicket != null">
        #{travelingByTicket,jdbcType=BIT},
      </if>
      <if test="isInvoicing != null">
        #{isInvoicing,jdbcType=BIT},
      </if>
      <if test="wmsOrderNo != null">
        #{wmsOrderNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.buyorder.domain.entity.Express">
    update T_EXPRESS
    <set>
      <if test="logisticsId != null">
        LOGISTICS_ID = #{logisticsId,jdbcType=INTEGER},
      </if>
      <if test="logisticsNo != null">
        LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="deliveryTime != null">
        DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="arrivalStatus != null">
        ARRIVAL_STATUS = #{arrivalStatus,jdbcType=BIT},
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="deliveryFrom != null">
        DELIVERY_FROM = #{deliveryFrom,jdbcType=INTEGER},
      </if>
      <if test="logisticsComments != null">
        LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null">
        IS_ENABLE = #{isEnable,jdbcType=BIT},
      </if>
      <if test="paymentType != null">
        PAYMENT_TYPE = #{paymentType,jdbcType=BIT},
      </if>
      <if test="cardNumber != null">
        CARD_NUMBER = #{cardNumber,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE = #{businessType,jdbcType=BIT},
      </if>
      <if test="realWeight != null">
        REAL_WEIGHT = #{realWeight,jdbcType=DECIMAL},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="amountWeight != null">
        AMOUNT_WEIGHT = #{amountWeight,jdbcType=DECIMAL},
      </if>
      <if test="mailGoods != null">
        MAIL_GOODS = #{mailGoods,jdbcType=VARCHAR},
      </if>
      <if test="mailGoodsNum != null">
        MAIL_GOODS_NUM = #{mailGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="isProtectPrice != null">
        IS_PROTECT_PRICE = #{isProtectPrice,jdbcType=BIT},
      </if>
      <if test="protectPrice != null">
        PROTECT_PRICE = #{protectPrice,jdbcType=DECIMAL},
      </if>
      <if test="isReceipt != null">
        IS_RECEIPT = #{isReceipt,jdbcType=BIT},
      </if>
      <if test="mailCommtents != null">
        MAIL_COMMTENTS = #{mailCommtents,jdbcType=VARCHAR},
      </if>
      <if test="sentSms != null">
        SENT_SMS = #{sentSms,jdbcType=BIT},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="travelingByTicket != null">
        TRAVELING_BY_TICKET = #{travelingByTicket,jdbcType=BIT},
      </if>
      <if test="isInvoicing != null">
        IS_INVOICING = #{isInvoicing,jdbcType=BIT},
      </if>
      <if test="wmsOrderNo != null">
        WMS_ORDER_NO = #{wmsOrderNo,jdbcType=VARCHAR},
      </if>
    </set>
    where EXPRESS_ID = #{expressId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.buyorder.domain.entity.Express">
    update T_EXPRESS
    set LOGISTICS_ID = #{logisticsId,jdbcType=INTEGER},
      LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR},
      COMPANY_ID = #{companyId,jdbcType=INTEGER},
      DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
      ARRIVAL_STATUS = #{arrivalStatus,jdbcType=BIT},
      ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      DELIVERY_FROM = #{deliveryFrom,jdbcType=INTEGER},
      LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
      IS_ENABLE = #{isEnable,jdbcType=BIT},
      PAYMENT_TYPE = #{paymentType,jdbcType=BIT},
      CARD_NUMBER = #{cardNumber,jdbcType=VARCHAR},
      BUSINESS_TYPE = #{businessType,jdbcType=BIT},
      REAL_WEIGHT = #{realWeight,jdbcType=DECIMAL},
      NUM = #{num,jdbcType=INTEGER},
      AMOUNT_WEIGHT = #{amountWeight,jdbcType=DECIMAL},
      MAIL_GOODS = #{mailGoods,jdbcType=VARCHAR},
      MAIL_GOODS_NUM = #{mailGoodsNum,jdbcType=INTEGER},
      IS_PROTECT_PRICE = #{isProtectPrice,jdbcType=BIT},
      PROTECT_PRICE = #{protectPrice,jdbcType=DECIMAL},
      IS_RECEIPT = #{isReceipt,jdbcType=BIT},
      MAIL_COMMTENTS = #{mailCommtents,jdbcType=VARCHAR},
      SENT_SMS = #{sentSms,jdbcType=BIT},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      TRAVELING_BY_TICKET = #{travelingByTicket,jdbcType=BIT},
      IS_INVOICING = #{isInvoicing,jdbcType=BIT},
      WMS_ORDER_NO = #{wmsOrderNo,jdbcType=VARCHAR}
    where EXPRESS_ID = #{expressId,jdbcType=INTEGER}
  </update>


  <select id="selectByExpressDetailIds" parameterType="java.lang.Integer" resultType="com.vedeng.erp.buyorder.domain.entity.Express">
    select
    a.*,b.RELATED_ID as buyorderGoodsId,b.EXPRESS_DETAIL_ID as expressDetailId
    from T_EXPRESS a
    LEFT  JOIN  T_EXPRESS_DETAIL b on a.EXPRESS_ID = b.EXPRESS_ID
    where b.EXPRESS_DETAIL_ID in
    <foreach collection="list" item="itemData" separator="," open="(" close=")">
      #{itemData}
    </foreach>
  </select>

  <select id="isDeliveryDirectBuyOrderExpress" resultMap="BaseResultMap">
    select te.*
    from T_EXPRESS te
           left join T_EXPRESS_DETAIL ted on te.EXPRESS_ID = ted.EXPRESS_ID
           left join T_BUYORDER_GOODS tbg on ted.RELATED_ID = tbg.BUYORDER_GOODS_ID and tbg.IS_DELETE = 0
           left join T_BUYORDER tb on tbg.BUYORDER_ID = tb.BUYORDER_ID
    where te.IS_ENABLE = 1
      and (te.LOGISTICS_COMMENTS IS NULL OR te.LOGISTICS_COMMENTS != '虚拟快递单')
      and te.ARRIVAL_STATUS = 0
      and ted.BUSINESS_TYPE = 515
      and tb.DELIVERY_DIRECT = 1
      and te.EXPRESS_ID = #{expressId,jdbcType=INTEGER}
    group by te.EXPRESS_ID;
  </select>

  <select id="getNeedConfirmList" resultType="com.vedeng.erp.buyorder.dto.BuyOrderExpressDto">
    select *
    from (select te.EXPRESS_ID,
    te.LOGISTICS_NO,
    tl.NAME                     as logisticsName,
    ts.SALEORDER_NO,
    ts.SALEORDER_ID,
    tu.USERNAME                 as addresser,
    te.DELIVERY_TIME,
    FROM_UNIXTIME(te.DELIVERY_TIME / 1000, '%Y-%m-%d') as deliveryTimeStr,
    tb.TAKE_TRADER_CONTACT_NAME as receiver,
    tb.TAKE_TRADER_AREA         as receiverAddress,
    te.LOGISTICS_COMMENTS
    from T_EXPRESS te
    left join T_USER tu on te.CREATOR = tu.USER_ID
    left join T_LOGISTICS tl on te.LOGISTICS_ID = tl.LOGISTICS_ID
    left join T_EXPRESS_DETAIL ted on te.EXPRESS_ID = ted.EXPRESS_ID
    left join T_BUYORDER_GOODS tbg on ted.RELATED_ID = tbg.BUYORDER_GOODS_ID and tbg.IS_DELETE = 0
    left join T_BUYORDER tb on tbg.BUYORDER_ID = tb.BUYORDER_ID
    left join T_TRADER_ADDRESS tta on tb.TAKE_TRADER_ADDRESS_ID = tta.TRADER_ADDRESS_ID
    left join T_REGION AREA ON tta.	AREA_ID = AREA.REGION_ID
    left join T_REGION CITY ON AREA.PARENT_ID = CITY.REGION_ID
    left join T_REGION PROVINCE ON CITY.PARENT_ID = PROVINCE.REGION_ID
    left join T_R_BUYORDER_J_SALEORDER trbjs on tbg.BUYORDER_GOODS_ID = trbjs.BUYORDER_GOODS_ID
    left join T_SALEORDER_GOODS tsg on trbjs.SALEORDER_GOODS_ID = tsg.SALEORDER_GOODS_ID
    left join T_SALEORDER ts on tsg.SALEORDER_ID = ts.SALEORDER_ID
    where te.IS_ENABLE = 1
    and (te.LOGISTICS_COMMENTS IS NULL OR te.LOGISTICS_COMMENTS != '虚拟快递单')
    and te.ARRIVAL_STATUS = 0
    and te.IS_INTERCEPTED = 0
    and ted.BUSINESS_TYPE = 515
    and tb.DELIVERY_DIRECT = 1
    and DATEDIFF(DATE(NOW()), DATE(FROM_UNIXTIME(te.DELIVERY_TIME / 1000))) > 10
    and PROVINCE.REGION_ID IN (650000,540000,150000,630000)
    <if test="param.searchExpressNo != null and param.searchExpressNo != ''">
      and te.LOGISTICS_NO like CONCAT('%',#{param.searchExpressNo,jdbcType=VARCHAR},'%' )
    </if>
    <if test="param.searchRelatedNo != null and param.searchRelatedNo != ''">
      and ts.SALEORDER_NO like CONCAT('%',#{param.searchRelatedNo,jdbcType=VARCHAR},'%' )
    </if>
    <if test="param.searchDeliveryDateStartStr != null">
      and te.DELIVERY_TIME &gt;= #{param.searchDeliveryDateStartStr,jdbcType=BIGINT}
    </if>
    <if test="param.searchDeliveryDateEndStr != null">
      and te.DELIVERY_TIME &lt;= #{param.searchDeliveryDateEndStr,jdbcType=BIGINT}
    </if>
    group by te.EXPRESS_ID
    union
    select te.EXPRESS_ID,
    te.LOGISTICS_NO,
    tl.NAME                     as logisticsName,
    ts.SALEORDER_NO,
    ts.SALEORDER_ID,
    tu.USERNAME                 as addresser,
    te.DELIVERY_TIME,
    FROM_UNIXTIME(te.DELIVERY_TIME / 1000, '%Y-%m-%d') as deliveryTimeStr,
    tb.TAKE_TRADER_CONTACT_NAME as receiver,
    tb.TAKE_TRADER_AREA         as receiverAddress,
    te.LOGISTICS_COMMENTS
    from T_EXPRESS te
    left join T_USER tu on te.CREATOR = tu.USER_ID
    left join T_LOGISTICS tl on te.LOGISTICS_ID = tl.LOGISTICS_ID
    left join T_EXPRESS_DETAIL ted on te.EXPRESS_ID = ted.EXPRESS_ID
    left join T_BUYORDER_GOODS tbg on ted.RELATED_ID = tbg.BUYORDER_GOODS_ID and tbg.IS_DELETE = 0
    left join T_BUYORDER tb on tbg.BUYORDER_ID = tb.BUYORDER_ID
    left join T_TRADER_ADDRESS tta on tb.TAKE_TRADER_ADDRESS_ID = tta.TRADER_ADDRESS_ID
    left join T_REGION AREA ON tta.	AREA_ID = AREA.REGION_ID
    left join T_REGION CITY ON AREA.PARENT_ID = CITY.REGION_ID
    left join T_REGION PROVINCE ON CITY.PARENT_ID = PROVINCE.REGION_ID
    left join T_R_BUYORDER_J_SALEORDER trbjs on tbg.BUYORDER_GOODS_ID = trbjs.BUYORDER_GOODS_ID
    left join T_SALEORDER_GOODS tsg on trbjs.SALEORDER_GOODS_ID = tsg.SALEORDER_GOODS_ID
    left join T_SALEORDER ts on tsg.SALEORDER_ID = ts.SALEORDER_ID
    where te.IS_ENABLE = 1
    and (te.LOGISTICS_COMMENTS IS NULL OR te.LOGISTICS_COMMENTS != '虚拟快递单')
    and te.ARRIVAL_STATUS = 0
    and te.IS_INTERCEPTED = 0
    and ted.BUSINESS_TYPE = 515
    and tb.DELIVERY_DIRECT = 1
    and DATEDIFF(DATE(NOW()), DATE(FROM_UNIXTIME(te.DELIVERY_TIME / 1000))) > 7
    and PROVINCE.REGION_ID not in (650000,540000,150000,630000)
    <if test="param.searchExpressNo != null and param.searchExpressNo != ''">
      and te.LOGISTICS_NO like CONCAT('%',#{param.searchExpressNo,jdbcType=VARCHAR},'%' )
    </if>
    <if test="param.searchRelatedNo != null and param.searchRelatedNo != ''">
      and ts.SALEORDER_NO like CONCAT('%',#{param.searchRelatedNo,jdbcType=VARCHAR},'%' )
    </if>
    <if test="param.searchDeliveryDateStartStr != null">
      and te.DELIVERY_TIME &gt;= #{param.searchDeliveryDateStartStr,jdbcType=BIGINT}
    </if>
    <if test="param.searchDeliveryDateEndStr != null">
      and te.DELIVERY_TIME &lt;= #{param.searchDeliveryDateEndStr,jdbcType=BIGINT}
    </if>
    group by te.EXPRESS_ID) result
    order by result.EXPRESS_ID desc
  </select>
    
  <select id="getLogisticsNo" resultType="java.lang.String">
    select b.LOGISTICS_NO
    from T_EXPRESS_DETAIL a
           left join T_EXPRESS b on a.EXPRESS_ID = b.EXPRESS_ID
    where a.EXPRESS_DETAIL_ID = #{expressDetailId,jdbcType=INTEGER}
  </select>
  
  <select id="selectByBuyorderId" resultType="com.vedeng.erp.buyorder.dto.BuyOrderExpressDetailDto">
    select b.*,c.*
    from T_BUYORDER_GOODS a
           left join T_EXPRESS_DETAIL b on a.BUYORDER_GOODS_ID = b.RELATED_ID
           left join T_EXPRESS c on c.EXPRESS_ID = b.EXPRESS_ID
    where a.IS_DELETE = 0
      and b.BUSINESS_TYPE = 515
      and a.BUYORDER_ID =  #{buyorderId,jdbcType=INTEGER}
  </select>
</mapper>
