package com.vedeng.erp.finance.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.vedeng.erp.finance.domain.dto.SaleInvoiceRedConfirmCancelResponseDto;
import com.vedeng.erp.finance.service.AbstractTaxesOpenApiHandler;
import com.vedeng.infrastructure.taxes.base.ITaxesParam;
import com.vedeng.infrastructure.taxes.base.ITaxesResult;
import com.vedeng.infrastructure.taxes.domain.TaxesOpenApiResult;
import com.vedeng.infrastructure.taxes.enums.TaxesReturnCodeEnum;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;

import java.util.Objects;

import static com.vedeng.infrastructure.taxes.common.constant.TaxesConstant.Y;

/**
 * 红字确认单撤销
 */
@Service
public class TaxesInvoiceRedConfirmCancelOpenApiImpl extends AbstractTaxesOpenApiHandler {

    /**
     * 后置处理
     * @param taxesParam
     * @param executeResult
     * @return
     */
    @Override
    protected ITaxesResult afterExecute(ITaxesParam taxesParam, ITaxesResult executeResult) {
        TaxesOpenApiResult openapi = (TaxesOpenApiResult) executeResult;
        String decodeStr = new String(Base64Utils.decodeFromString(openapi.getData()));
        SaleInvoiceRedConfirmCancelResponseDto taxesResult = JSONObject.parseObject(decodeStr, SaleInvoiceRedConfirmCancelResponseDto.class);
        if (Objects.isNull(taxesResult)){
            taxesResult = new SaleInvoiceRedConfirmCancelResponseDto();
        }
        TaxesOpenApiResult.ReturnInfo return_info = openapi.getReturn_info();
        String message = super.getMessage(return_info, taxesResult.getMessage());
        taxesResult.setReturnCode(return_info.getReturn_code());
        taxesResult.setReturnMessage(message);
        // 成功与否：除判断Return_code值，需要额外判断消息体的Code值，
        if (TaxesReturnCodeEnum.SUCCESS.getCode().equals(return_info.getReturn_code()) && Y.equals(taxesResult.getCode())){
            taxesResult.setIsSuccess(Boolean.TRUE);
        }
        return taxesResult;
    }
}
