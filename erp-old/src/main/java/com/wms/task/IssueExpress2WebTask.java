package com.wms.task;

import com.alibaba.fastjson.JSON;
import com.rabbitmq.MsgProducer;
import com.rabbitmq.RabbitConfig;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.StringUtil;
import com.vedeng.logistics.model.LogisticsOrderData;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 推送前台包裹信息补偿任务（临时）
 *
 * <AUTHOR>
 */
@Component
@JobHandler(value = "IssueExpress2WebTask")
public class IssueExpress2WebTask extends AbstractJobHandler {
    public static Logger logger = LoggerFactory.getLogger(IssueExpress2WebTask.class);

    @Autowired
    @Qualifier("msgProducer")
    MsgProducer msgProducer;


    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        XxlJobLogger.log("IssueExpress2WebTask-JOB, START");
        if (StringUtil.isBlank(s)) {
            XxlJobLogger.log("补偿包裹信息任务必须拥有参数信息");
            return FAIL;
        }
        List<LogisticsOrderData> logisticsOrderData = JSON.parseArray(s, LogisticsOrderData.class);
        if (CollectionUtils.isEmpty(logisticsOrderData)) {
            XxlJobLogger.log("补偿包裹信息任务参数信息错误");
            return FAIL;
        }
        logisticsOrderData.forEach(item -> {
            logger.info("补偿发送mq消息至前台 item:{}", JSON.toJSONString(item));
            msgProducer.sendMsg(RabbitConfig.MJX_ADDLOGISTICS_EXCHANGE, RabbitConfig.MJX_ADDLOGISTICS_ROUTINGKEY, JSON.toJSONString(item));
        });
        return SUCCESS;
    }
}
