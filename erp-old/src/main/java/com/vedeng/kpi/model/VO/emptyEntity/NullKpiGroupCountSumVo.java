package com.vedeng.kpi.model.VO.emptyEntity;

import com.vedeng.kpi.model.VO.KpiGroupCountSumVo;

import java.math.BigDecimal;

public class NullKpiGroupCountSumVo extends KpiGroupCountSumVo {

    private static final long serialVersionUID = 5155914234561840739L;

    public NullKpiGroupCountSumVo(){
        this.setSumAmount(BigDecimal.ZERO);
        this.setAmountProgress(BigDecimal.ZERO);
        this.setAmountSort(0);
        this.setAmountScore(BigDecimal.ZERO);

        this.setSumBdLostCustomerNum(BigDecimal.ZERO);
        this.setSumBdNewCustomerNum(BigDecimal.ZERO);
        this.setAveBdCustomerNum(BigDecimal.ZERO);
        this.setBdCustomerSort(0);
        this.setBdCustomerScore(BigDecimal.ZERO);

        this.setSumCoCustomerNum(BigDecimal.ZERO);
        this.setSumLostCustomerNum(BigDecimal.ZERO);
        this.setAveCustomerNum(BigDecimal.ZERO);
        this.setCustomerSort(0);
        this.setCustomerScore(BigDecimal.ZERO);

        this.setSumChanceSuccessNum(0);
        this.setSumChancefailNum(0);
        this.setAveTransProportion(BigDecimal.ZERO);
        this.setChanceScore(BigDecimal.ZERO);
        this.setChanceSort(0);

        this.setIntegrateScore(BigDecimal.ZERO);
        this.setIntegrateSort(0);
    }

}
