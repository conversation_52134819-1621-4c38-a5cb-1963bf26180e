<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.infrastructure.esign.mapper.ElectronicSignRecordMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.infrastructure.esign.domain.entity.ElectronicSignRecordEntity">
        <!--@mbg.generated-->
        <!--@Table T_ELECTRONIC_SIGN_RECORD-->
        <id column="ELECTRONIC_SIGN_RECORD_ID" jdbcType="INTEGER" property="electronicSignRecordId"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName"/>
        <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName"/>
        <result column="SIGN_STATUS" jdbcType="INTEGER" property="signStatus"/>
        <result column="BUSINESS_ID" jdbcType="VARCHAR" property="businessId"/>
        <result column="BUSINESS_TYPE" jdbcType="INTEGER" property="businessType"/>
        <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete"/>
        <result column="RETRY_NUM" jdbcType="INTEGER" property="retryNum"/>
        <result column="SEND_MSG" jdbcType="INTEGER" property="sendMsg"/>
        <result column="ERROR_MSG" jdbcType="VARCHAR" property="errorMsg"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ELECTRONIC_SIGN_RECORD_ID,
        ADD_TIME,
        MOD_TIME,
        CREATOR,
        UPDATER,
        CREATOR_NAME,
        UPDATER_NAME,
        SIGN_STATUS,
        BUSINESS_ID,
        BUSINESS_TYPE,
        IS_DELETE,
        RETRY_NUM,
        SEND_MSG,
        ERROR_MSG
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from T_ELECTRONIC_SIGN_RECORD
        where ELECTRONIC_SIGN_RECORD_ID = #{electronicSignRecordId,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete
        from T_ELECTRONIC_SIGN_RECORD
        where ELECTRONIC_SIGN_RECORD_ID = #{electronicSignRecordId,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="ELECTRONIC_SIGN_RECORD_ID" keyProperty="electronicSignRecordId"
            parameterType="com.vedeng.infrastructure.esign.domain.entity.ElectronicSignRecordEntity"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_ELECTRONIC_SIGN_RECORD (ADD_TIME, MOD_TIME, CREATOR,
                                              UPDATER, CREATOR_NAME, UPDATER_NAME,
                                              SIGN_STATUS, BUSINESS_ID, BUSINESS_TYPE,
                                              IS_DELETE, RETRY_NUM, SEND_MSG,
                                              ERROR_MSG)
        values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER},
                #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR},
                #{signStatus,jdbcType=INTEGER}, #{businessId,jdbcType=VARCHAR}, #{businessType,jdbcType=INTEGER},
                #{isDelete,jdbcType=INTEGER}, #{retryNum,jdbcType=INTEGER}, #{sendMsg,jdbcType=INTEGER},
                #{errorMsg,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="ELECTRONIC_SIGN_RECORD_ID" keyProperty="electronicSignRecordId"
            parameterType="com.vedeng.infrastructure.esign.domain.entity.ElectronicSignRecordEntity"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_ELECTRONIC_SIGN_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="creatorName != null and creatorName != ''">
                CREATOR_NAME,
            </if>
            <if test="updaterName != null and updaterName != ''">
                UPDATER_NAME,
            </if>
            <if test="signStatus != null">
                SIGN_STATUS,
            </if>
            <if test="businessId != null and businessId != ''">
                BUSINESS_ID,
            </if>
            <if test="businessType != null">
                BUSINESS_TYPE,
            </if>
            <if test="isDelete != null">
                IS_DELETE,
            </if>
            <if test="retryNum != null">
                RETRY_NUM,
            </if>
            <if test="sendMsg != null">
                SEND_MSG,
            </if>
            <if test="errorMsg != null and errorMsg != ''">
                ERROR_MSG,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null and creatorName != ''">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updaterName != null and updaterName != ''">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="signStatus != null">
                #{signStatus,jdbcType=INTEGER},
            </if>
            <if test="businessId != null and businessId != ''">
                #{businessId,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null">
                #{businessType,jdbcType=INTEGER},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="retryNum != null">
                #{retryNum,jdbcType=INTEGER},
            </if>
            <if test="sendMsg != null">
                #{sendMsg,jdbcType=INTEGER},
            </if>
            <if test="errorMsg != null and errorMsg != ''">
                #{errorMsg,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.vedeng.infrastructure.esign.domain.entity.ElectronicSignRecordEntity">
        <!--@mbg.generated-->
        update T_ELECTRONIC_SIGN_RECORD
        <set>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null and creatorName != ''">
                CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updaterName != null and updaterName != ''">
                UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="signStatus != null">
                SIGN_STATUS = #{signStatus,jdbcType=INTEGER},
            </if>
            <if test="businessId != null and businessId != ''">
                BUSINESS_ID = #{businessId,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null">
                BUSINESS_TYPE = #{businessType,jdbcType=INTEGER},
            </if>
            <if test="isDelete != null">
                IS_DELETE = #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="retryNum != null">
                RETRY_NUM = #{retryNum,jdbcType=INTEGER},
            </if>
            <if test="sendMsg != null">
                SEND_MSG = #{sendMsg,jdbcType=INTEGER},
            </if>
            <if test="errorMsg != null and errorMsg != ''">
                ERROR_MSG = #{errorMsg,jdbcType=VARCHAR},
            </if>
        </set>
        where ELECTRONIC_SIGN_RECORD_ID = #{electronicSignRecordId,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.vedeng.infrastructure.esign.domain.entity.ElectronicSignRecordEntity">
        <!--@mbg.generated-->
        update T_ELECTRONIC_SIGN_RECORD
        set ADD_TIME      = #{addTime,jdbcType=TIMESTAMP},
            MOD_TIME      = #{modTime,jdbcType=TIMESTAMP},
            CREATOR       = #{creator,jdbcType=INTEGER},
            UPDATER       = #{updater,jdbcType=INTEGER},
            CREATOR_NAME  = #{creatorName,jdbcType=VARCHAR},
            UPDATER_NAME  = #{updaterName,jdbcType=VARCHAR},
            SIGN_STATUS   = #{signStatus,jdbcType=INTEGER},
            BUSINESS_ID   = #{businessId,jdbcType=VARCHAR},
            BUSINESS_TYPE = #{businessType,jdbcType=INTEGER},
            IS_DELETE     = #{isDelete,jdbcType=INTEGER},
            RETRY_NUM     = #{retryNum,jdbcType=INTEGER},
            SEND_MSG      = #{sendMsg,jdbcType=INTEGER},
            ERROR_MSG     = #{errorMsg,jdbcType=VARCHAR}
        where ELECTRONIC_SIGN_RECORD_ID = #{electronicSignRecordId,jdbcType=INTEGER}
    </update>

    <!--auto generated by MybatisCodeHelper on 2024-06-26-->
    <select id="findByBusinessIdAndBusinessType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_ELECTRONIC_SIGN_RECORD
        where BUSINESS_ID = #{businessId,jdbcType=VARCHAR}
          and BUSINESS_TYPE = #{businessType,jdbcType=INTEGER}
          and IS_DELETE = 0
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-06-27-->
    <select id="findBySignStatusIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_ELECTRONIC_SIGN_RECORD
        where SIGN_STATUS in
        <foreach item="item" index="index" collection="signStatusCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
        and BUSINESS_TYPE = #{businessType,jdbcType=INTEGER}
        and IS_DELETE = 0
    </select>


    <update id="addRetryNum">
        update T_ELECTRONIC_SIGN_RECORD
        set RETRY_NUM = ifnull(RETRY_NUM,0) + 1
        where electronic_sign_record_id = #{electronicSignRecordId,jdbcType=INTEGER}
    </update>
</mapper>