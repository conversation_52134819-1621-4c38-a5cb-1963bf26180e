package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeePurchaseBackDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeePurchaseBackCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeePurchaseBackConvertor;
import com.vedeng.erp.kingdee.repository.mappers.KingDeePurchaseBackMapper;
import com.vedeng.erp.kingdee.service.KingDeePurchaseBackApiService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购退货出库
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class PurchaseBackWriter extends BaseWriter<KingDeePurchaseBackDto> {


    @Autowired
    private KingDeePurchaseBackApiService kingDeePurchaseBackApiService;

    @Override
    public void doWrite(KingDeePurchaseBackDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("PurchaseBackWriterService write:{}",JSON.toJSONString(dto));
        dto.setKingDeeBizEnums(KingDeeBizEnums.savePurchaseBack);
        kingDeePurchaseBackApiService.register(dto,true);
    }


}
