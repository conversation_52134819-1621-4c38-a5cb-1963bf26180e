package com.vedeng.goods.mapper;

import com.vedeng.goods.domain.entity.GoodsLevelEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository("newGoodsLevelMapper")
public interface GoodsLevelMapper {
    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(GoodsLevelEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(GoodsLevelEntity record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    GoodsLevelEntity selectByPrimaryKey(Integer id);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(GoodsLevelEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(GoodsLevelEntity record);

    int updateBatchSelective(List<GoodsLevelEntity> list);

    int batchInsert(@Param("list") List<GoodsLevelEntity> list);

    List<GoodsLevelEntity> findAll();

}