package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDetailDto;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import com.vedeng.erp.kingdee.batch.dto.BatchRInvoiceDetailJOperateLogDto;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDetailDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchRInvoiceDetailJOperateLogDtoMapper;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.dto.result.KingDeeReceiveQueryResultDto;
import com.vedeng.erp.kingdee.service.KingDeeReceiveCommonService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 销售售后的负向红票组装
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchSaleOrderAfterSaleInvoiceProcessor implements ItemProcessor<BatchInvoiceDto, KingDeeRedInvoiceDto> {

    //T_INVOICE.TYPE（来源）504（售后开票）：售后单号 /505（销售开票）：销售单号
    private static final Integer TYPE_504 = 504;


    public static final String SPECIAL_INVOICE = "专用发票";
    @Autowired
    private BatchInvoiceDtoMapper batchInvoiceDtoMapper;
    @Autowired
    private KingDeeReceiveCommonService kingDeeReceiveCommonService;
    @Autowired
    private BatchInvoiceDetailDtoMapper batchInvoiceDetailDtoMapper;
    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private BatchRInvoiceDetailJOperateLogDtoMapper batchRInvoiceDetailJOperateLogDtoMapper;

    @Override
    public KingDeeRedInvoiceDto process(BatchInvoiceDto batchInvoiceDto) throws Exception {
        log.info("销售售后的负向红票组装推送开始,{}", JSON.toJSONString(batchInvoiceDto));

        if (StrUtil.isBlank(batchInvoiceDto.getInvoiceNo()) || StrUtil.isBlank(batchInvoiceDto.getInvoiceCode())) {
            log.warn("红票无发票号或者发票代码");
            return null;
        }

        KingDeeRedInvoiceDto kingDeeRedInvoiceDto = new KingDeeRedInvoiceDto();
        // 获取应收单
        List<KingDeeReceiveQueryResultDto> kingDeeAfterSaleReceivableResultDtoList = kingDeeReceiveCommonService.getKingDeeReceiveCommon(batchInvoiceDto.getInvoiceId().toString());
        if (CollUtil.isEmpty(kingDeeAfterSaleReceivableResultDtoList)) {
            log.error("{}无对应应收单数据，无法组装发票", batchInvoiceDto.getInvoiceId());
            return null;
        }
        // 设置发票明细数据,实物商品明细
        List<BatchInvoiceDetailDto> saleOrderInvoiceDetailList;
        if (CollectionUtils.isEmpty(batchInvoiceDto.getBatchInvoiceDetailDtoList())) {
            // 发票明细为空，则表示是非历史数据，直接取数据库查询发票明细
            log.info("开始处理销售售后负向红票明细：{}", JSON.toJSONString(batchInvoiceDto));
            saleOrderInvoiceDetailList = batchInvoiceDetailDtoMapper.getSaleOrderInvoiceDetailList(batchInvoiceDto.getInvoiceId());
        } else {
            // 否则，则证明是从Excel中读取的发票明细
            log.info("开始处理21-22历史销售售后负向红票明细：{}", JSON.toJSONString(batchInvoiceDto));
            saleOrderInvoiceDetailList = batchInvoiceDto.getBatchInvoiceDetailDtoList();
        }

        saleOrderInvoiceDetailList = saleOrderInvoiceDetailList.stream().filter(detailDto -> !Convert.toBool(detailDto.getIsVirtureSku(), false)).collect(Collectors.toList());
        batchInvoiceDto.setBatchInvoiceDetailDtoList(saleOrderInvoiceDetailList);
        BigDecimal taxRate = Objects.isNull(batchInvoiceDto.getRatio()) ? BigDecimal.ZERO : batchInvoiceDto.getRatio().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
        String saleOrderNoBySaleOrderId = batchInvoiceDtoMapper.getSaleOrderNoBySaleOrderId(batchInvoiceDto.getRelatedId());
        String afterSalesNoByAfterSalesId = batchInvoiceDtoMapper.getAfterSalesNoByAfterSalesId(batchInvoiceDto.getAfterSalesId());

        // 是否专票
        boolean isSpecial = StrUtil.isNotEmpty(batchInvoiceDto.getInvoiceTypeName()) && batchInvoiceDto.getInvoiceTypeName().contains(SPECIAL_INVOICE);

        if (isSpecial) {
            KingDeeSalesVatSpecialInvoiceDto kingDeeSalesVatSpecialInvoiceDto = this.specialInvoice(batchInvoiceDto,
                    kingDeeAfterSaleReceivableResultDtoList, saleOrderNoBySaleOrderId, afterSalesNoByAfterSalesId, taxRate);
            if (kingDeeSalesVatSpecialInvoiceDto == null) {
                return null;
            }
            kingDeeRedInvoiceDto.setKingDeeSalesVatSpecialInvoiceDto(kingDeeSalesVatSpecialInvoiceDto);
        } else {
            KingDeeSalesVatPlainInvoiceDto kingDeeSalesVatPlainInvoiceDto = this.vatPlainInvoice(batchInvoiceDto,
                    kingDeeAfterSaleReceivableResultDtoList, saleOrderNoBySaleOrderId, afterSalesNoByAfterSalesId, taxRate);
            if (kingDeeSalesVatPlainInvoiceDto == null) {
                return null;
            }
            kingDeeRedInvoiceDto.setKingDeeSalesVatPlainInvoiceDto(kingDeeSalesVatPlainInvoiceDto);
        }
        return kingDeeRedInvoiceDto;
    }

    private KingDeeSalesVatSpecialInvoiceDto specialInvoice(BatchInvoiceDto batchInvoiceDto,
                                                            List<KingDeeReceiveQueryResultDto> kingDeeAfterSaleReceivableResultDtoList,
                                                            String saleOrderNoBySaleOrderId,
                                                            String afterSalesNoByAfterSalesId,
                                                            BigDecimal taxRate) {
        KingDeeSalesVatSpecialInvoiceDto kingDeeSalesVatSpecialInvoiceDto = new KingDeeSalesVatSpecialInvoiceDto();
        kingDeeSalesVatSpecialInvoiceDto.setFQzokBddjtid(batchInvoiceDto.getInvoiceId().toString());
        // 判断是否数据已存在
        boolean old = kingDeeBaseApi.isExist(kingDeeSalesVatSpecialInvoiceDto);
        if (old) {
            log.info("销售售后红票,金蝶数据已存在:{}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }
        kingDeeSalesVatSpecialInvoiceDto.setFid("0");

        // VDERP-15040
        Date outInTime = batchRInvoiceDetailJOperateLogDtoMapper.findWarehouseOutByInvoiceId(batchInvoiceDto.getInvoiceId(), 5);
        kingDeeSalesVatSpecialInvoiceDto.setFdate(DateUtil.formatDate(new Date(batchInvoiceDto.getAddTime()).compareTo(outInTime) < 0 ? outInTime : new Date(batchInvoiceDto.getAddTime())));
        kingDeeSalesVatSpecialInvoiceDto.setFinvoicedate(DateUtil.formatDateTime(new Date(batchInvoiceDto.getAddTime())));//开票日期格式调整为yyyy-MM-dd HH:mm:ss morton

        kingDeeSalesVatSpecialInvoiceDto.setFinvoiceno(batchInvoiceDto.getInvoiceNo());
        kingDeeSalesVatSpecialInvoiceDto.setFQzokFpdm(batchInvoiceDto.getInvoiceCode());
        kingDeeSalesVatSpecialInvoiceDto.setFcustomerid(batchInvoiceDto.getTraderCustomerId().toString());
        kingDeeSalesVatSpecialInvoiceDto.setFRedBlue(1);
        kingDeeSalesVatSpecialInvoiceDto.setFBillTypeID("XSZZSZYFP01_SYS");
        kingDeeSalesVatSpecialInvoiceDto.setFQzokPzgsywdh(TYPE_504.equals(batchInvoiceDto.getType())?afterSalesNoByAfterSalesId:saleOrderNoBySaleOrderId);
        List<KingDeeSalesVatSpecialInvoiceDetailDto> fsalesicentries = kingDeeSalesVatSpecialInvoiceDto.getFsalesicentry();

        batchInvoiceDto.getBatchInvoiceDetailDtoList().forEach(invoiceDetailDto -> {
            KingDeeSalesVatSpecialInvoiceDetailDto fsalesicentry = new KingDeeSalesVatSpecialInvoiceDetailDto();
            fsalesicentries.add(fsalesicentry);
            fsalesicentry.setFmaterialid(invoiceDetailDto.getSku());
            fsalesicentry.setFtaxrate(taxRate);
            fsalesicentry.setFpriceqty(invoiceDetailDto.getNum().negate());
            BigDecimal price = invoiceDetailDto.getTotalAmount()
                    .divide(invoiceDetailDto.getNum(), 6, RoundingMode.HALF_UP).abs()
                    .divide(BigDecimal.ONE.add(batchInvoiceDto.getRatio()), 6, RoundingMode.HALF_UP);
            fsalesicentry.setFauxprice(price);

            fsalesicentry.setFQzokBddjhid(invoiceDetailDto.getInvoiceDetailId().toString());
            fsalesicentry.setFQzokYsddh(saleOrderNoBySaleOrderId);
            fsalesicentry.setFQzokGsywdh(afterSalesNoByAfterSalesId);
            fsalesicentry.setFQzokYwlx("销售售后");
            fsalesicentry.setFsrcbilltypeid("AR_receivable");
            BigDecimal taxAmount = invoiceDetailDto.getTotalAmount().multiply(batchInvoiceDto.getRatio()).divide(BigDecimal.ONE.add(batchInvoiceDto.getRatio()), BigDecimal.ROUND_CEILING, RoundingMode.HALF_UP);
            fsalesicentry.setFdetailtaxamountfor(taxAmount);
            fsalesicentry.setFamountfor(invoiceDetailDto.getTotalAmount().subtract(taxAmount));

            KingDeeReceiveQueryResultDto kingDeeReceiveQueryResultDto = CollUtil.getFirst(kingDeeAfterSaleReceivableResultDtoList);
            List<BatchRInvoiceDetailJOperateLogDto> detailIdAndOperateTypeList = batchRInvoiceDetailJOperateLogDtoMapper.findByInvoiceDetailIdAndOperateType(invoiceDetailDto.getInvoiceDetailId(), 5);

            if (CollUtil.isEmpty(detailIdAndOperateTypeList)) {
                log.error("未查到红票和入库的关系{}", JSON.toJSONString(invoiceDetailDto));
                throw new KingDeeException("未查到红票和入库的关系");
            }

            detailIdAndOperateTypeList.forEach(detailIdAndOperateType -> {

                String bddjhid = invoiceDetailDto.getInvoiceDetailId().toString() + "-" + detailIdAndOperateType.getOperateLogId().toString();
                List<KingDeeReceiveQueryResultDto> kingDeeReceiveQueryResultDtos = kingDeeAfterSaleReceivableResultDtoList.stream()
                        .filter(q -> q.getF_QZOK_BDDJHID().equals(bddjhid)).collect(Collectors.toList());
                if(CollUtil.isEmpty(kingDeeReceiveQueryResultDtos)){
                    log.error("未能查到销售售后负向标准应收单的明细信息{}", JSON.toJSONString(invoiceDetailDto));
                    throw new KingDeeException("未能查到销售售后负向标准应收单的明细信息");
                }

                kingDeeReceiveQueryResultDtos.forEach(kingDeeReceiveQueryResultDtoDetail->{
                    List<KingDeeSalesVatSpecialInvoiceDetailLinkDto> fsalesicentryLinks = fsalesicentry.getFsalesicentryLink();
                    KingDeeSalesVatSpecialInvoiceDetailLinkDto fsalesicentryLink = new KingDeeSalesVatSpecialInvoiceDetailLinkDto();
                    fsalesicentryLinks.add(fsalesicentryLink);
                    fsalesicentryLink.setFLinkId(0);
                    fsalesicentryLink.setFsalesicentryLinkFsbillid(kingDeeReceiveQueryResultDto.getFID());
                    fsalesicentryLink.setFsalesicentryLinkFsid(kingDeeReceiveQueryResultDtoDetail.getFEntityDetail_FEntryId());
                    fsalesicentryLink.setFsalesicentryLinkFbasicunitqtyold(new BigDecimal(kingDeeReceiveQueryResultDtoDetail.getFPriceQty()));
                    fsalesicentryLink.setFsalesicentryLinkFbasicunitqty(new BigDecimal(kingDeeReceiveQueryResultDtoDetail.getFPriceQty()));
                    fsalesicentryLink.setFsalesicentryLinkFallamountforold(new BigDecimal(kingDeeReceiveQueryResultDtoDetail.getFALLAMOUNTFOR_D()));
                    fsalesicentryLink.setFsalesicentryLinkFallamountfor(new BigDecimal(kingDeeReceiveQueryResultDtoDetail.getFNoTaxAmountFor_D()));
                });
            });
        });
        return kingDeeSalesVatSpecialInvoiceDto;
    }

    private KingDeeSalesVatPlainInvoiceDto vatPlainInvoice(BatchInvoiceDto batchInvoiceDto,
                                                           List<KingDeeReceiveQueryResultDto> kingDeeAfterSaleReceivableResultDtoList,
                                                           String saleOrderNoBySaleOrderId,
                                                           String afterSalesNoByAfterSalesId,
                                                           BigDecimal taxRate) {
        KingDeeSalesVatPlainInvoiceDto kingDeeSalesVatPlainInvoiceDto = new KingDeeSalesVatPlainInvoiceDto();
        kingDeeSalesVatPlainInvoiceDto.setFQzokBddjtid(batchInvoiceDto.getInvoiceId().toString());
        // 判断是否数据已存在
        boolean old = kingDeeBaseApi.isExist(kingDeeSalesVatPlainInvoiceDto);
        if (old) {
            log.info("销售售后红票,金蝶数据已存在:{}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }
        kingDeeSalesVatPlainInvoiceDto.setFid("0");

        Date outInTime = batchRInvoiceDetailJOperateLogDtoMapper.findWarehouseOutByInvoiceId(batchInvoiceDto.getInvoiceId(), 5);
        kingDeeSalesVatPlainInvoiceDto.setFdate(DateUtil.formatDate(new Date(batchInvoiceDto.getAddTime()).compareTo(outInTime) < 0 ? outInTime : new Date(batchInvoiceDto.getAddTime())));
        kingDeeSalesVatPlainInvoiceDto.setFinvoicedate(DateUtil.formatDateTime(new Date(batchInvoiceDto.getAddTime())));//开票日期格式调整为yyyy-MM-dd HH:mm:ss morton

        kingDeeSalesVatPlainInvoiceDto.setFinvoiceno(batchInvoiceDto.getInvoiceNo());
        kingDeeSalesVatPlainInvoiceDto.setFQzokFpdm(batchInvoiceDto.getInvoiceCode());
        kingDeeSalesVatPlainInvoiceDto.setFcustomerid(batchInvoiceDto.getTraderCustomerId().toString());
        kingDeeSalesVatPlainInvoiceDto.setFBillTypeID("XSPTFP01_SYS");
        kingDeeSalesVatPlainInvoiceDto.setFRedBlue(1);
        kingDeeSalesVatPlainInvoiceDto.setFQzokPzgsywdh(TYPE_504.equals(batchInvoiceDto.getType())?afterSalesNoByAfterSalesId:saleOrderNoBySaleOrderId);
        List<KingDeeSalesVatPlainInvoiceDetail> fsalesicentries = kingDeeSalesVatPlainInvoiceDto.getFsalesicentry();
        batchInvoiceDto.getBatchInvoiceDetailDtoList().forEach(invoiceDetailDto -> {
            KingDeeSalesVatPlainInvoiceDetail fsalesicentry = new KingDeeSalesVatPlainInvoiceDetail();
            fsalesicentries.add(fsalesicentry);
            fsalesicentry.setFmaterialid(invoiceDetailDto.getSku());
            fsalesicentry.setFtaxrate(taxRate);
            fsalesicentry.setFpriceqty(invoiceDetailDto.getNum().negate());
            fsalesicentry.setFauxtaxprice(invoiceDetailDto.getTotalAmount().divide(invoiceDetailDto.getNum(), 6, RoundingMode.HALF_UP).abs());
            fsalesicentry.setFQzokBddjhid(invoiceDetailDto.getInvoiceDetailId().toString());
            fsalesicentry.setFQzokYsddh(saleOrderNoBySaleOrderId);
            fsalesicentry.setFQzokGsywdh(afterSalesNoByAfterSalesId);
            fsalesicentry.setFQzokYwlx("销售售后");
            fsalesicentry.setFsrcbilltypeid("AR_receivable");

            KingDeeReceiveQueryResultDto kingDeeReceiveQueryResultDto = CollUtil.getFirst(kingDeeAfterSaleReceivableResultDtoList);
            List<BatchRInvoiceDetailJOperateLogDto> detailIdAndOperateTypeList = batchRInvoiceDetailJOperateLogDtoMapper.findByInvoiceDetailIdAndOperateType(invoiceDetailDto.getInvoiceDetailId(), 5);

            if (CollUtil.isEmpty(detailIdAndOperateTypeList)) {
                log.error("未查到红票和入库的关系{}", JSON.toJSONString(invoiceDetailDto));
                throw new KingDeeException("未查到红票和入库的关系");
            }


            detailIdAndOperateTypeList.forEach(detailIdAndOperateType -> {

                String bddjhid = invoiceDetailDto.getInvoiceDetailId().toString() + "-" + detailIdAndOperateType.getOperateLogId().toString();
                List<KingDeeReceiveQueryResultDto> kingDeeReceiveQueryResultDtos = kingDeeAfterSaleReceivableResultDtoList.stream()
                        .filter(q -> q.getF_QZOK_BDDJHID().equals(bddjhid)).collect(Collectors.toList());
                if(CollUtil.isEmpty(kingDeeReceiveQueryResultDtos)){
                    log.error("未能查到销售售后负向标准应收单的明细信息{}", JSON.toJSONString(invoiceDetailDto));
                    throw new KingDeeException("未能查到销售售后负向标准应收单的明细信息");
                }

                kingDeeReceiveQueryResultDtos.forEach(kingDeeReceiveQueryResultDtoDetail->{
                    List<KingDeeSalesVatPlainInvoiceDetailLinkDto> fsalesicentryLinks = fsalesicentry.getFsalesicentryLink();
                    KingDeeSalesVatPlainInvoiceDetailLinkDto fsalesicentryLink = new KingDeeSalesVatPlainInvoiceDetailLinkDto();
                    fsalesicentryLinks.add(fsalesicentryLink);
                    fsalesicentryLink.setFLinkId(0);
                    fsalesicentryLink.setFsalesicentryLinkFsbillid(kingDeeReceiveQueryResultDto.getFID());
                    fsalesicentryLink.setFsalesicentryLinkFsid(kingDeeReceiveQueryResultDtoDetail.getFEntityDetail_FEntryId());
                    fsalesicentryLink.setFsalesicentryLinkFbasicunitqtyold(new BigDecimal(kingDeeReceiveQueryResultDtoDetail.getFPriceQty()));
                    fsalesicentryLink.setFsalesicentryLinkFbasicunitqty(new BigDecimal(kingDeeReceiveQueryResultDtoDetail.getFPriceQty()));
                    fsalesicentryLink.setFsalesicentryLinkFallamountforold(new BigDecimal(kingDeeReceiveQueryResultDtoDetail.getFALLAMOUNTFOR_D()));
                    fsalesicentryLink.setFsalesicentryLinkFallamountfor(new BigDecimal(kingDeeReceiveQueryResultDtoDetail.getFNoTaxAmountFor_D()));
                });

            });

        });
        return kingDeeSalesVatPlainInvoiceDto;
    }


}
