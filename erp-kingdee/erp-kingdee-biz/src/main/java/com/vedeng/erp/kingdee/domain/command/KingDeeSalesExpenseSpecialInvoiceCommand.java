package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/9 16:15
 */
@NoArgsConstructor
@Data
public class KingDeeSalesExpenseSpecialInvoiceCommand {
    /**
     * 单据内码
     */
    private Integer fid;
    /**
     * 单据号
     */
    private String fBillNo;
    /**
     * 业务日期
     */
    private String fdate;
    /**
     * 贝登单据头ID
     */
    private String F_QZOK_BDDJTID;
    /**
     * 发票号
     */
    private String FINVOICENO;
    /**
     * 发票日期
     */
    private String finvoicedate;
    /**
     * 发票代码
     */
    private String F_QZOK_FPDM;
    /**
     * 开票方式 默认税控 0   手工 1
     */
    private String fbillingway;
    /**
     * 客户
     */
    private KingDeeNumberCommand fcustomerid = new KingDeeNumberCommand();
    /**
     * 单据状态
     */
    private String fdocumentstatus;
    /**
     * 发票类型
     */
    private KingDeeNumberCommand fBillTypeID = new KingDeeNumberCommand();
    /**
     * 结算组织
     */
    private KingDeeNumberCommand fsettleorgid = new KingDeeNumberCommand();
    /**
     * 作废状态 A （正常）
     */
    private String fCancelStatus;
    /**
     * 红蓝字标识 0 蓝字  1 红字
     */
    private Integer fRedBlue;
    /**
     * 发票的明细
     */
    private List<FSALESICENTRY> fsalesicentry;

    /**
     * 往来单位类型
     */
    private KingDeeNumberCommand FCONTACTUNITTYPE;

    /**
     * FSALESICENTRY
     */
    @NoArgsConstructor
    @Data
    public static class FSALESICENTRY {
        /**
         * 物料编码
         */
        private KingDeeNumberCommand fmaterialid = new KingDeeNumberCommand();
        /**
         * 计价数量
         */
        private BigDecimal fpriceqty;
        /**
         * 不含税单价
         */
        private BigDecimal fauxprice;
        /**
         * 不含税金额
         */
        private BigDecimal famountfor;
        /**
         * 税率
         */
        private BigDecimal ftaxrate;
        /**
         * 税额
         */
        private BigDecimal fdetailtaxamountfor;
        /**
         * 贝登单据行ID
         */
        private String F_QZOK_BDDJHID;
        /**
         * 原始订单号
         */
        private String F_QZOK_YSDDH;
        /**
         * 归属业务单号
         */
        private String F_QZOK_GSYWDH;
        /**
         * 业务类型
         */
        private String F_QZOK_YWLX;
        /**
         * 源单类型
         */
        private String fsrcbilltypeid;
        /**
         * 源单关联关系
         */
        private List<FSALEEXINVENTRY_Link> FSALEEXINVENTRY_Link;

        /**
         * FSALESICENTRYLink
         */
        @NoArgsConstructor
        @Data
        public static class FSALEEXINVENTRY_Link {
            /**
             * 实体主键
             */
            private Integer fLinkId;
            /**
             * 业务流程图
             */
            private String FSALESICENTRY_Link_FFlowId;
            /**
             * 推进线路ID
             */
            private Integer FSALESICENTRY_Link_FFlowLineId;
            /**
             * 源单据id
             */
            private String FSALESICENTRY_Link_FRuleId;
            /**
             * 源单表内码
             */
            private Integer FSALESICENTRY_Link_FSTableId;
            /**
             * 源单表
             */
            private String FSALESICENTRY_Link_FSTableName;
            /**
             * 源单内码
             */
            private Integer FSALESICENTRY_Link_FSBillId;
            /**
             * 源单分录内码
             */
            private Integer FSALESICENTRY_Link_FSId;
            /**
             * 原单的数量
             */
            private BigDecimal FSALESICENTRY_Link_FBASICUNITQTYOld;
            /**
             * 修改携带量（实开数量）
             */
            private BigDecimal FSALESICENTRY_Link_FBASICUNITQTY;
            /**
             * 原单的金额
             */
            private BigDecimal FSALESICENTRY_Link_FALLAMOUNTFOROld;
            /**
             * 开票的金额（实开金额）
             */
            private BigDecimal FSALESICENTRY_Link_FALLAMOUNTFOR;
        }
    }
}