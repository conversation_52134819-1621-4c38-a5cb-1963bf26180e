package com.vedeng.infrastructure.kingdee.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.ArrayList;
import java.util.List;

/**
 * 金蝶业务主回写字段注解
 *
 * <AUTHOR>
 */
@Target({ElementType.FIELD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface WriteBackField {

    /**
     * erp需要回写的字段 -> 当前注解的字段
     */
    String value() default "FID";

    /**
     * 金蝶需要返回的字段 -> 金蝶接口中返回的字段  如:FID
     */
    String[] needBackField() default {};

    /**
     * 通过回传的字段写入当前的字段
     * erp的字段
     */
    String backWriteByErpField() default "";


    /**
     * 通过回传的字段写入当前的字段
     * 金蝶的字段
     */
    String backWriteToKingDeeField() default "";
}
