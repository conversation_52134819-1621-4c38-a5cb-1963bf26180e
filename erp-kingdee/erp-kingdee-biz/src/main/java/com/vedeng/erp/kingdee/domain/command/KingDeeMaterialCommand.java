package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶实际接受参数类
 * @date 2022/11/07 18:59
 */
@Getter
@Setter
public class KingDeeMaterialCommand {

    /**
     * 物料内码
     */
    private String fmaterialid;
    /**
     * 创建组织
     */
    private KingDeeNumberCommand fCreateOrgId = new KingDeeNumberCommand();
    /**
     * 使用组织
     */
    private KingDeeNumberCommand fUseOrgId = new KingDeeNumberCommand();
    /**
     * 物料编码
     */
    private String fNumber;
    /**
     * 名称
     */
    private String fName;
    /**
     * 规格型号
     */
    private String fSpecification;
    /**
     * fOldNumber
     */
    private String fOldNumber;
    /**
     * 订货号
     */
    private String F_QZOK_DHH;

    /**
     * 供应商物料编码
     */
    private String F_QZOK_GYSWLBM;
    /**
     * 品牌
     */
    private String F_QZOK_PPPTEXT;
    /**
     * 注册证号
     */
    private String F_QZOK_ZCZHTEXT;
    /**
     * 是否为医疗器械产品
     */
    private String F_QZOK_YLQXTEXT;
    /**
     * 医疗器械产线
     */
    private String F_QZOK_YLQXCXTEXT;
    /**
     * 医疗器械用途
     */
    private String F_QZOK_YLQXYTTEXT;
    /**
     * 医疗器械细分类
     */
    private String F_QZOK_YLQXXFLTEXT;
    /**
     * 医疗器械分类
     */
    private String F_QZOK_YLQXFLTEXT;
    /**
     * 商品运营一级分类
     */
    private String F_QZOK_SPYYYJFLTEXT;
    /**
     * 商品运营二级分类
     */
    private String F_QZOK_SPYYEJFLTEXT;
    /**
     * 商品运营三级分类
     */
    private String F_QZOK_SPYYSJFLTEXT;
    /**
     * 商品运营四级分类
     */
    private String F_QZOK_SPYYSIJFLTEXT;
    /**
     * 商品运营五级分类
     */
    private String F_QZOK_SPYYWJFLTEXT;
    /**
     * 非医疗器械一级分类
     */
    private String F_QZOK_FYLQXYJFLTEXT;
    /**
     * 非医疗器械二级分类
     */
    private String F_QZOK_FYLQXEJFLTEXT;
    /**
     * 主要产品线
     */
    private String F_QZOK_CXFL;
    /**
     * 产线划分类型
     */
    private String F_QZOK_ZYCPX;

    /**
     * 安调分类
     */
    private String F_QZOK_ATFL;

    /**
     * 税收分类编码
     */
    private KingDeeNumberCommand FTAXCATEGORYCODEID = new KingDeeNumberCommand();
    /**
     * 基本信息
     */
    private KingDeeMaterialSubHead subHeadEntity;

    @Getter
    @Setter
    public static class KingDeeMaterialSubHead {
        /**
         * 物料属性
         */
        private String fErpClsID;
        /**
         * 存货类别
         */
        private KingDeeNumberCommand fCategoryID = new KingDeeNumberCommand();
        /**
         * 基本单位
         */
        private KingDeeNumberCommand fBaseUnitId = new KingDeeNumberCommand();
    }
}
