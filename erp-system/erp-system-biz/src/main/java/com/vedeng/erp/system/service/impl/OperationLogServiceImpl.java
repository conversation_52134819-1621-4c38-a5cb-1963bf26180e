package com.vedeng.erp.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.common.trace.enums.BizLogEnum;
import com.vedeng.erp.system.domain.entity.OperationLogEntity;
import com.vedeng.erp.system.dto.OperationLogDto;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.mapper.OperationLogMapper;
import com.vedeng.erp.system.mapstruct.OperationLogConvertor;
import com.vedeng.erp.system.service.OperationLogApiService;
import com.vedeng.erp.system.service.OperationLogService;
import com.vedeng.erp.system.service.UserApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OperationLogServiceImpl implements OperationLogService, OperationLogApiService {

    @Autowired
    private OperationLogMapper operationLogMapper;
    @Autowired
    OperationLogConvertor operationLogConvertor;
    @Autowired
    private UserApiService userApiService;

    @Override
    public int deleteByPrimaryKey(Long operationLogId) {
        return operationLogMapper.deleteByPrimaryKey(operationLogId);
    }

    @Override
    public int insert(OperationLogEntity record) {
        return operationLogMapper.insert(record);
    }

    @Override
    public int insertSelective(OperationLogEntity record) {
        return operationLogMapper.insertSelective(record);
    }

    @Override
    public OperationLogEntity selectByPrimaryKey(Long operationLogId) {
        return operationLogMapper.selectByPrimaryKey(operationLogId);
    }

    @Override
    public int updateByPrimaryKeySelective(OperationLogEntity record) {
        return operationLogMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(OperationLogEntity record) {
        return operationLogMapper.updateByPrimaryKey(record);
    }

    @Override
    public void save(OperationLogDto operationLog, BizLogEnum bizLogEnum) {
        Assert.notNull(operationLog);
        Assert.notNull(bizLogEnum);
        Assert.notNull(operationLog.getBizId());

        String content = bizLogEnum.getTemplateContent();
        if (CollUtil.isNotEmpty(operationLog.getParams())) {
            Map<String, String> params = operationLog.getParams();
            //将params中的每个参数,替换到{userName}分配线索给{targetUserName}中,成为一句话
            for (Map.Entry<String, String> param : params.entrySet()) {
                content = content.replace("{" + param.getKey() + "}", param.getValue());
            }
        }else{
            operationLog.setParams(new HashMap<>());//将此字段补全，避免出来为null的情况
        }

        OperationLogEntity record = new OperationLogEntity();

        record.setBizTypeEnum(bizLogEnum.getBizTypeEnum());
        record.setActionTypeEnum(bizLogEnum.getActionTypeEnum());

        record.setBizId(operationLog.getBizId());
        record.setBizName(bizLogEnum.getBizName());
        record.setLogParam(JSONObject.toJSONString(operationLog.getParams()));
        record.setLogContent(content);
        record.setOperationTime(operationLog.getOperationTime());
        record.setIsDelete(false);
        record.setCreator(operationLog.getCreator());
        record.setCreatorName(operationLog.getCreatorName());
        record.setUpdater(operationLog.getCreator());
        record.setUpdaterName(operationLog.getCreatorName());
        record.setAddTime(new Date());
        record.setModTime(new Date());
        this.insert(record);
    }

    @Override
    public PageInfo<OperationLogDto> page(PageParam<OperationLogDto> operationLogDto) {
        OperationLogDto param = operationLogDto.getParam();
        // 默认按操作时间倒序
        operationLogDto.setOrderBy("OPERATION_LOG_ID desc");
        PageInfo<OperationLogDto> pageInfo = PageHelper.startPage(operationLogDto).doSelectPageInfo(() -> operationLogMapper.findByAll(param));

        //pageInfo.getList().forEach(item -> item.setAliasHeadPicture(userApiService.getUserBaseInfo(item.getCreator()).getAliasHeadPicture()));
        //上述一行代码效率低，会存在重复查的情况，修改为去重并赋值给item
        // 获取所有需要查询的用户ID，并去重
        Set<Integer> userIds = pageInfo.getList().stream()
                .map(OperationLogDto::getCreator)
                .collect(Collectors.toSet());
        if(CollectionUtil.isNotEmpty(userIds)){
            List<UserDto> userList = userApiService.getUserInfoByUserIds(new ArrayList<>(userIds));
            // 根据去重后的用户ID列表，一次性查询所有用户信息
            Map<Integer, UserDto> userInfoMap = userList.stream()
                    .collect(Collectors.toMap(UserDto::getUserId, Function.identity()));

            // 将查询到的用户信息赋值给对应的item
            pageInfo.getList().forEach(item -> {
                UserDto userInfo = userInfoMap.get(item.getCreator());
                if (userInfo != null) {
                    item.setAliasHeadPicture(userInfo.getAliasHeadPicture());
                }
            });
        }

        return pageInfo;
    }


}
