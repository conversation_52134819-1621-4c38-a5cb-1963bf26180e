function submit() {

        debugger;
        var flag = false;
        var dom = $("input[name='maxOutNum']");

        if (dom.val() <= 0 || dom.val() > dom.attr("data-maxNum")) {
            flag = true;
        }

        if (flag) {
            layer.alert("出库数量填写有误");
            return false;
        }
        debugger;
        var logObj = {};
        var parent = dom.parent().parent();
        logObj.num = parent.find("input[name='maxOutNum']").first().val();
        logObj.afterSaleBuyorderDirectOutLogId = parent.find("input[name='afterSaleBuyorderDirectOutLogId']").first().val();
        logObj.produceTimeStr = parent.find("input[name='produceTime']").first().val();
        logObj.validTimeStr = parent.find("input[name='validTime']").first().val();
        logObj.outTimeStr = parent.find("input[name='outTime']").first().val();
        logObj.industryBatchNumber = parent.find("input[name='industryBatchNumber']").first().val();
        logObj.sterilizationNumber = parent.find("input[name='sterilizationNumber']").first().val();
        logObj.vedengBatchNum = parent.find("input[name='vedengBatchNum']").first().val();
        console.log(logObj);
        console.log(JSON.stringify(eval(logObj)));

        $.ajax({
            url: page_url + '/order/newBuyorder/saveEditDirectAfterSaleOutLog.do',
            data: JSON.stringify(eval(logObj)),
            type: "POST",
            dataType: "json",
            contentType: "application/json;charset=utf-8",
            async: false,
            success: function (data) {
                if (data.code == 0) {
                    window.parent.location.reload();
                } else {
                    layer.alert(data.message);
                }

            },
            error: function (data) {
                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
        return false;

}