package com.vedeng.order.controller;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.order.model.RemarkComponentTree;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.coupon.LabelDataDto;
import com.vedeng.order.model.query.LabelQuery;
import com.vedeng.order.service.impl.RemarkComponentServiceImpl;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description:  标签组件备注控制器
 * @Author:       Davis.yu
 * @Date:         2021/4/14 下午2:55
 * @Version:      1.0
 */
@Controller
@RequestMapping("/order/remarkComponent")
public class RemarkComponentController {

    @Autowired
    @Resource
    private RemarkComponentServiceImpl remarkComponentService;

//    private final Integer SCENE=0; //场景 0是销售订单

    /**
     * 初始化备注组件
     * @param labelQuery 请求参数
     * @return 前台组件参数对象
     */
    @ResponseBody
    @RequestMapping(value = "/getInitComponent",method = RequestMethod.POST)
    public ResultInfo<LabelDataDto> getInitComponent(@RequestBody LabelQuery labelQuery) {
        ResultInfo<LabelDataDto> resultInfo = new ResultInfo<>(0, "操作成功");
        List<RemarkComponentTree> componentList = remarkComponentService.getInitComponent(labelQuery);
        LabelDataDto dataDto = new LabelDataDto();
        dataDto.setData(componentList);
        dataDto.setRemark(labelQuery.getRemark());
        resultInfo.setData(dataDto);
        return resultInfo;
    }

    /**
     * @Description:  根据使用场景，订单id，订货号查出采购要求和供应商要求
     * @Author:       wuxu
     * @Date:         2021/4/16 下午2:10
     */
    //根据使用场景，订单id，订货号查出采购要求和供应商要求 用于在采购状态下展示 销售要求
    public  void getRemarlComponentInfo(ModelAndView mv, List<SaleorderGoods> saleorderGoodsList , Saleorder saleorder,Integer scene){ //saleorderGoodsList
        List<String> skus = new ArrayList<>(); //拿到订单对应的所有的sku
        if(saleorderGoodsList.size()>0){
            saleorderGoodsList.stream().forEach(saleGood -> {
                skus.add(saleGood.getSku());
            });
        }
        Map map = new HashMap();
        map.put("skuNos",skus); //订货号即sku
        map.put("saleorderId",saleorder.getSaleorderId()); //订单id
        map.put("scene",scene);
        //根据skuId查询 SaleorderId
        List<Map<String,Object>>  componentList = remarkComponentService.findComponentList(map);
        for (Map maps:componentList) {
            String time = maps.get("time").toString();
            if(StringUtil.isNumeric(time)){
                Long timeNum = Long.valueOf(time);
                if(timeNum > 0 ){
                    maps.put("timeDate",DateUtil.convertString(timeNum, "yyyy-MM-dd"));
                }else{
                    maps.put("timeDate",0);
                }
            }else {
                // 不是数字类型；额
                maps.put("timeDate",0);
            }
        }
        mv.addObject("componentList",componentList);
        mv.addObject("satisfyDeliveryTime",saleorder.getSatisfyDeliveryTime());// 首次满足可采购时间

    }
}
