package com.vedeng.erp.trader.constant;

public class AiConstant {

    public static final String CODE_GROUP_SUMMARY  = "GROUP_SUMMARY"; //分组-摘要
    public static final String CODE_GROUP_KEYWORD  = "GROUP_KEYWORD"; //分组-话术关键词
    public static final String CODE_GROUP_TODOTASK  = "GROUP_TODOTASK"; //分组-待办



    public static final String 	SENCE_BUSINESS_FOLLOW	="SENCE_BUSINESS_FOLLOW";//	商机跟进
    public static final String 	SENCE_PRODUCT_PROMOT	="SENCE_PRODUCT_PROMOT";//	产品推广
    public static final String 	SENCE_CUSTOMER_DEVELOP	="SENCE_CUSTOMER_DEVELOP";//	客户开发
    public static final String 	SENCE_QUOTAT_RESP	="SENCE_QUOTAT_RESP";//	报价响应
    public static final String 	SENCE_BUSINESS_PROCESS	="SENCE_BUSINESS_PROCESS";//	商务处理
    public static final String 	SENCE_AFTER_SALE	="SENCE_AFTER_SALE";	//售后
    public static final String 	SENCE_OTHER	="SENCE_OTHER";	//其他


    public static SenceEnum getEnumByCommunicateType(Integer communicateType){
        if(communicateType==null){
            return SenceEnum.SENCE_OTHER;
        }
        //产品推广【9新定义】 5501 营销任务点进去后
        //商机跟进【1】      244 商机
        //客户开发【10新定义】5502 客户联系人点击通话时
        //报价响应【3】      245 报价单
        //商务处理【2】      246 销售订单
        //售后【4】         248 销售售后
        //其他【0其他 5采购订单】
        switch (communicateType){
            case 244:
                return SenceEnum.SENCE_BUSINESS_FOLLOW;
            case 246:
                return SenceEnum.SENCE_BUSINESS_PROCESS;
            case 245:
                return SenceEnum.SENCE_QUOTAT_RESP;
            case 248:
                return SenceEnum.SENCE_AFTER_SALE;
            case 5501:
                return SenceEnum.SENCE_PRODUCT_PROMOT;
            case 5502:
                return SenceEnum.SENCE_CUSTOMER_DEVELOP;
            default:
                return SenceEnum.SENCE_OTHER;
        }
    }


    /**
     *
     * @param communicateType
     * @return
     */
    public static String getCommunicateSenceByCommunicateType(Integer communicateType){
        if(communicateType==null){
            return AiConstant.SENCE_OTHER;
        }
        //产品推广【9新定义】
        //商机跟进【1】
        //客户开发【10新定义】
        //报价响应【3】
        //商务处理【2】
        //售后【4】
        //其他【0其他 5采购订单】
        switch (communicateType){
            case 1:
                return AiConstant.SENCE_BUSINESS_FOLLOW;
            case 2:
                return AiConstant.SENCE_BUSINESS_PROCESS;
            case 3:
                return AiConstant.SENCE_QUOTAT_RESP;
            case 4:
                return AiConstant.SENCE_AFTER_SALE;
            case 9:
                return AiConstant.SENCE_PRODUCT_PROMOT;
            case 10:
                return AiConstant.SENCE_CUSTOMER_DEVELOP;
            default:
                return AiConstant.SENCE_OTHER;
        }

    }


    /**
     *
     * @param communicateType
     * @return
     */
    public static String getCommunicateSenceByCommunicateTypeName(Integer communicateType){
        if(communicateType==null){
            return SenceEnum.SENCE_OTHER.getName();
        }
        //产品推广【9新定义】
        //商机跟进【1】
        //客户开发【10新定义】
        //报价响应【3】
        //商务处理【2】
        //售后【4】
        //其他【0其他 5采购订单】
        switch (communicateType){
            case 1:
                return SenceEnum.SENCE_BUSINESS_FOLLOW.getName();
            case 2:
                return SenceEnum.SENCE_BUSINESS_PROCESS.getName();
            case 3:
                return SenceEnum.SENCE_QUOTAT_RESP.getName();
            case 4:
                return SenceEnum.SENCE_AFTER_SALE.getName();
            case 9:
                return SenceEnum.SENCE_PRODUCT_PROMOT.getName();
            case 10:
                return SenceEnum.SENCE_CUSTOMER_DEVELOP.getName();
            default:
                return SenceEnum.SENCE_OTHER.getName();
        }
    }


}
