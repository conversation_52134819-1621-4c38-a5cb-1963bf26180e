package com.vedeng.erp.saleorder.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.vedeng.erp.saleorder.dto.CheckSettlementDto;
import com.vedeng.erp.saleorder.dto.OrderFinanceInfoDto;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.saleorder.dto.SaleorderInfoForStandardDto;
import com.vedeng.erp.system.dto.BaseCompanyInfoDto;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 订单内部调用service
 * @date 2022/12/23 10:53
 */
public interface SaleOrderApiService {

    /**
     * 检查是否与子公司的订单
     * @param saleOrderId
     * @return
     */
    boolean checkSignCompanyInfo(Integer saleOrderId);

    BaseCompanyInfoDto checkSignCompanyInfoBySaleOrderId(Integer saleOrderId);


    /**
     * 检验更新销售单收发货状态
     *
     * @param saleorderId
     */
    void checkSaleorderDeliveryAndArrivalStatus(Integer saleorderId);

    /**
     * 获取产品价格信息
     *
     * @param saleorderId
     * @param skuNo
     * @param price
     * @return
     */
    Map<String, String> getSaleOrderGoodsPrice(Integer saleorderId, String skuNo, BigDecimal price);


    /**
     * 查询客户名下是否有生效订单订单
     *
     * @param traderName 客户名
     * @return true 有 false 无
     */
    boolean checkByTraderNameHaveSaleOrder(String traderName);

    /**
     * 更新销售订单的发票状态
     *
     * @param saleOrderId 订单id
     */
    void updateSaleOrderInvoiceStatus(Integer saleOrderId);

    /**
     * 获取订单的收票人联系电话
     *
     * @param saleOrderId 订单id
     * @return InvoiceTraderContactTelephone
     */
    String getSaleOrderInvoiceTraderContactMobile(Integer saleOrderId);

    /**
     * 获取订单的收票人
     * @param saleOrderId
     * @return InvoiceTraderContactName
     */
    String getSaleOrderInvoiceTraderContactName(Integer saleOrderId);
    /**
     * 根据销售售后单id查询关联的销售单信息
     *
     * @param afterSalesId 售后单id
     * @return SaleorderInfoDto
     */
    SaleorderInfoDto getByAfterSaleId(Integer afterSalesId);

    /**
     * 根据销售单id查询销售单信息
     * @param saleOrderId 销售单id
     * @return SaleorderInfoDto
     */
    SaleorderInfoDto getBySaleOrderId(Integer saleOrderId);

    /**
     * 根据销售单id集合查询销售单信息
     * @param saleorderIds 销售单id集合
     * @return
     */
    List<SaleorderInfoDto> getSaleorderInfoBySaleorderIds(List<Integer> saleorderIds);

    /**
     * 获取销售订单财务信息
     *
     * @param saleorderId
     * @return
     */
    OrderFinanceInfoDto getSaleorderFinanceInfo(Integer saleorderId);

    /**
     * 根据相关限制查询符合的订单id
     * @param param 动态的限制条件
     * @return 销售的id集合
     */
    List<Integer> checkSettlement(CheckSettlementDto param);

    /**
     * 获取销售订单合同审核状态
     * @param saleorderId
     * @return
     */
    Integer getContractVerifyStatusBySaleOrderId(Integer saleorderId);

    /**
     * 分页获取可以开票的销售订单
     *
     * @param page
     * @param param
     * @return
     */
    PageInfo<SaleorderInfoDto> getSaleorderCanInvoiceListPage(Page page, String param);

    /**
     * 根据单号查找销售订单信息
     * @param orderNo
     * @return
     */
    SaleorderInfoDto getBySaleOrderNo(String orderNo);

    /**
     * 查询沟通记录关联的销售单信息（id 单号）
     *
     * @param saleOrderIdList 销售单id集合
     * @return id 单号
     */
    List<Map<String, Object>> getCommunicateChanceInfo(List<Integer> saleOrderIdList);


    /**
     * 账期订单是否可发货
     *
     * @param saleOrderId saleOrderId
     */
    boolean periodOrderCanDeliver(Integer saleOrderId);


    /**
     * 查询销售订单的信息-提供给业务流转单
     * @param orderNo
     * @return
     */
    SaleorderInfoForStandardDto getBySaleOrderNoForStandard(String orderNo);

    /**
     * 查询销售订单的信息-提供给业务流转单
     * @param orderNo
     * @return
     */
    SaleorderInfoForStandardDto getBySaleOrderIdForStandard(Integer saleorderId);


}
