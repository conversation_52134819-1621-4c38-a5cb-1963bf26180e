<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>供应商账期申请记录</title> 
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="UG27W5rR7Xg" datasource="erp-reportdatasource" fixednumber="2" append_column_url="" append_row_url="" empty_show="" firstcol="numbers">
  <div id="appendHead"></div> 
  <div class="layui-fluid"> 
   <div class="layui-tab"> 
    <ul class="layui-tab-title" id="tab"> 
    </ul> 
   </div> 
   <form class="layui-form" id="search">
       <div class=" layui-inline ">
           <label class="layui-form-label">供应商名称</label>
           <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="TRADER_NAME" placeholder="" style="" alias="B" jdbctype="" data="" datatype="" oper="LIKE"></object>
           </div>
       </div>
       <div class=" layui-inline "><label class="layui-form-label">审核状态</label>
           <div class="layui-input-inline">
               <object class=" layui-input list-search-item " type="21" name="STATUS" placeholder="" style="" alias="A" jdbctype="" data="[{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" oper="IN"></object>
           </div>
       </div>
       <div class=" layui-inline ">
           <label class="layui-form-label">归属人员</label>
           <div class="layui-input-inline"><object class=" layui-input list-search-item " type="20" name="USERNAME" placeholder="" style="" alias="TU" jdbctype="" data="SELECT USERNAME  K,USERNAME V FROM T_USER   where  IS_DISABLED=0  AND COMPANY_ID=1 order by USERNAME" datatype="KVSQLCACHE" oper="EQ"></object>
           </div>
       </div>
    <div class=" layui-inline "><label class="layui-form-label">添加日期</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="daterange" name="ADD_TIME_STR" placeholder="" style="" alias="FROM_UNIXTIME( A.ADD_TIME / 1000, '%Y-%m-%d %H:%i:%s' )" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
     </div>
    </div>



   </form> 
   <hr class="layui-border-blue"> 
   <div class="btn-group   bd-highlight" id="tableButton"> <button item_name="导出所有" name="导出所有" url="/ezadmin/list/export-UG27W5rR7Xg" opentype="APPEND_PARAM" windowname="" style type="table">导出所有</button>
   </div> 
   <table id="table" class="layui-table" style=" width:100%"> 
    <thead> 
     <tr id="column">
      <th type="rowbutton" id="rowbutton"><button item_name="查看" name="查看" url="/finance/accountperiod/getAccountPeriodApply.do?traderAccountPeriodApplyId=${TRADER_ACCOUNT_PERIOD_APPLY_ID}&amp;traderType=${TRADER_TYPE_ID}" opentype="PARENT" windowname="" style type="single">查看</button></th>
      <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-link" order="0" url="/trader/supplier/baseinfo.do?traderId=${TRADER_ID}" opentype="PARENT" windowname="" datatype="" data="" style="min-width:310px" head="18">客户名称</th>
      <th item_name="TRADER_TYPE" name="TRADER_TYPE" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="" style="" head="18">客户身份</th>
      <th item_name="ACCOUNT_PERIOD_NOW" name="ACCOUNT_PERIOD_NOW" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18" jdbctype="NUMBER">当前额度</th>
      <th item_name="ACCOUNT_PERIOD_DAYS_NOW" name="ACCOUNT_PERIOD_DAYS_NOW" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18" jdbctype="NUMBER">当前帐期天数</th>
      <th item_name="OVERDUE_TIMES" name="OVERDUE_TIMES" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18" jdbctype="NUMBER">逾期次数</th>
      <th item_name="OVERDUE_AMOUNT" name="OVERDUE_AMOUNT" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18" jdbctype="NUMBER">逾期总额</th>
      <th item_name="ADD_TIME_STR" name="ADD_TIME_STR" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="min-width:180px" head="18">日期</th>
      <th item_name="ACCOUNT_PERIOD_APPLY" name="ACCOUNT_PERIOD_APPLY" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18" jdbctype="NUMBER">申请金额</th>
      <th item_name="ACCOUNT_PERIOD_DAYS_APPLY" name="ACCOUNT_PERIOD_DAYS_APPLY" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18" jdbctype="NUMBER">申请帐期天数</th>
      <th item_name="USERNAME" name="USERNAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">归属人员</th>
      <th item_name="STATUS" name="STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" head="18">审核状态</th>
     </tr> 
    </thead> 
    <tbody> 
     <tr> 
      <td colspan="100"> express: <pre id="express" class="layui-code" orderby="" groupby="">SELECT
	A.TRADER_ACCOUNT_PERIOD_APPLY_ID,
	A.TRADER_ID,
	B.TRADER_NAME,
	'供应商' AS TRADER_TYPE,
A.TRADER_TYPE *1 as TRADER_TYPE_ID,
CASE
		
		WHEN C.CUSTOMER_NATURE = 465 THEN
		'分销' 
		WHEN C.CUSTOMER_NATURE = 466 THEN
		'终端' ELSE '' 
	END CUSTOMERNATURE,
	A.ACCOUNT_PERIOD_NOW,
	A.ACCOUNT_PERIOD_DAYS_NOW,
	A.OVERDUE_TIMES,
	A.OVERDUE_AMOUNT,
	FROM_UNIXTIME( A.ADD_TIME / 1000, '%Y-%m-%d %H:%i:%s' ) AS ADD_TIME_STR,
	A.ACCOUNT_PERIOD_APPLY,
	A.ACCOUNT_PERIOD_DAYS_APPLY,
	TU.USERNAME,
	A.STATUS*1 AS STATUS
FROM
	T_TRADER_ACCOUNT_PERIOD_APPLY A
	LEFT JOIN T_TRADER B ON B.TRADER_ID = A.TRADER_ID
	LEFT JOIN T_TRADER_CUSTOMER C ON C.TRADER_ID = A.TRADER_ID 
	AND A.TRADER_TYPE = 1
	LEFT JOIN T_VERIFIES_INFO E ON A.TRADER_ACCOUNT_PERIOD_APPLY_ID = E.RELATE_TABLE_KEY 
	AND E.RELATE_TABLE = 'T_TRADER_ACCOUNT_PERIOD_APPLY'
	LEFT JOIN T_USER TU ON A.CREATOR = TU.USER_ID

WHERE  B.COMPANY_ID = 1
	AND A.TRADER_TYPE = 2</pre> count: <pre id="count" class="layui-code"></pre></td> 
     </tr> 
    </tbody> 
   </table> 
  </div> 
  <div id="appendFoot"></div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
 </body>
</html>