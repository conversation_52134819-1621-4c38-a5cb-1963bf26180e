<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.settlement.mapper.CapitalBillDetailMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.settlement.domain.entity.CapitalBillDetailEntity">
    <!--@mbg.generated-->
    <!--@Table T_CAPITAL_BILL_DETAIL-->
    <id column="CAPITAL_BILL_DETAIL_ID" jdbcType="INTEGER" property="capitalBillDetailId" />
    <result column="CAPITAL_BILL_ID" jdbcType="INTEGER" property="capitalBillId" />
    <result column="BUSSINESS_TYPE" jdbcType="INTEGER" property="bussinessType" />
    <result column="ORDER_TYPE" jdbcType="TINYINT" property="orderType" />
    <result column="ORDER_NO" jdbcType="VARCHAR" property="orderNo" />
    <result column="RELATED_ID" jdbcType="INTEGER" property="relatedId" />
    <result column="AFTER_SALES_INSTALLSTION_ID" jdbcType="INTEGER" property="afterSalesInstallstionId" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_TYPE" jdbcType="TINYINT" property="traderType" />
    <result column="USER_ID" jdbcType="INTEGER" property="userId" />
    <result column="ORG_ID" jdbcType="INTEGER" property="orgId" />
    <result column="ORG_NAME" jdbcType="VARCHAR" property="orgName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    CAPITAL_BILL_DETAIL_ID, CAPITAL_BILL_ID, BUSSINESS_TYPE, ORDER_TYPE, ORDER_NO, RELATED_ID, 
    AFTER_SALES_INSTALLSTION_ID, AMOUNT, TRADER_ID, TRADER_TYPE, USER_ID, ORG_ID, ORG_NAME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_CAPITAL_BILL_DETAIL
    where CAPITAL_BILL_DETAIL_ID = #{capitalBillDetailId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_CAPITAL_BILL_DETAIL
    where CAPITAL_BILL_DETAIL_ID = #{capitalBillDetailId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="CAPITAL_BILL_DETAIL_ID" keyProperty="capitalBillDetailId" parameterType="com.vedeng.erp.settlement.domain.entity.CapitalBillDetailEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_CAPITAL_BILL_DETAIL (CAPITAL_BILL_ID, BUSSINESS_TYPE, ORDER_TYPE, 
      ORDER_NO, RELATED_ID, AFTER_SALES_INSTALLSTION_ID, 
      AMOUNT, TRADER_ID, TRADER_TYPE, 
      USER_ID, ORG_ID, ORG_NAME
      )
    values (#{capitalBillId,jdbcType=INTEGER}, #{bussinessType,jdbcType=INTEGER}, #{orderType,jdbcType=TINYINT}, 
      #{orderNo,jdbcType=VARCHAR}, #{relatedId,jdbcType=INTEGER}, #{afterSalesInstallstionId,jdbcType=INTEGER}, 
      #{amount,jdbcType=DECIMAL}, #{traderId,jdbcType=INTEGER}, #{traderType,jdbcType=TINYINT}, 
      #{userId,jdbcType=INTEGER}, #{orgId,jdbcType=INTEGER}, #{orgName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="CAPITAL_BILL_DETAIL_ID" keyProperty="capitalBillDetailId" parameterType="com.vedeng.erp.settlement.domain.entity.CapitalBillDetailEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_CAPITAL_BILL_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="capitalBillId != null">
        CAPITAL_BILL_ID,
      </if>
      <if test="bussinessType != null">
        BUSSINESS_TYPE,
      </if>
      <if test="orderType != null">
        ORDER_TYPE,
      </if>
      <if test="orderNo != null">
        ORDER_NO,
      </if>
      <if test="relatedId != null">
        RELATED_ID,
      </if>
      <if test="afterSalesInstallstionId != null">
        AFTER_SALES_INSTALLSTION_ID,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderType != null">
        TRADER_TYPE,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="orgId != null">
        ORG_ID,
      </if>
      <if test="orgName != null">
        ORG_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="capitalBillId != null">
        #{capitalBillId,jdbcType=INTEGER},
      </if>
      <if test="bussinessType != null">
        #{bussinessType,jdbcType=INTEGER},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=TINYINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="relatedId != null">
        #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesInstallstionId != null">
        #{afterSalesInstallstionId,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderType != null">
        #{traderType,jdbcType=TINYINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.settlement.domain.entity.CapitalBillDetailEntity">
    <!--@mbg.generated-->
    update T_CAPITAL_BILL_DETAIL
    <set>
      <if test="capitalBillId != null">
        CAPITAL_BILL_ID = #{capitalBillId,jdbcType=INTEGER},
      </if>
      <if test="bussinessType != null">
        BUSSINESS_TYPE = #{bussinessType,jdbcType=INTEGER},
      </if>
      <if test="orderType != null">
        ORDER_TYPE = #{orderType,jdbcType=TINYINT},
      </if>
      <if test="orderNo != null">
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="relatedId != null">
        RELATED_ID = #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesInstallstionId != null">
        AFTER_SALES_INSTALLSTION_ID = #{afterSalesInstallstionId,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderType != null">
        TRADER_TYPE = #{traderType,jdbcType=TINYINT},
      </if>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        ORG_ID = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="orgName != null">
        ORG_NAME = #{orgName,jdbcType=VARCHAR},
      </if>
    </set>
    where CAPITAL_BILL_DETAIL_ID = #{capitalBillDetailId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.settlement.domain.entity.CapitalBillDetailEntity">
    <!--@mbg.generated-->
    update T_CAPITAL_BILL_DETAIL
    set CAPITAL_BILL_ID = #{capitalBillId,jdbcType=INTEGER},
      BUSSINESS_TYPE = #{bussinessType,jdbcType=INTEGER},
      ORDER_TYPE = #{orderType,jdbcType=TINYINT},
      ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      RELATED_ID = #{relatedId,jdbcType=INTEGER},
      AFTER_SALES_INSTALLSTION_ID = #{afterSalesInstallstionId,jdbcType=INTEGER},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_TYPE = #{traderType,jdbcType=TINYINT},
      USER_ID = #{userId,jdbcType=INTEGER},
      ORG_ID = #{orgId,jdbcType=INTEGER},
      ORG_NAME = #{orgName,jdbcType=VARCHAR}
    where CAPITAL_BILL_DETAIL_ID = #{capitalBillDetailId,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="CAPITAL_BILL_DETAIL_ID" keyProperty="capitalBillDetailId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_CAPITAL_BILL_DETAIL
    (CAPITAL_BILL_ID, BUSSINESS_TYPE, ORDER_TYPE, ORDER_NO, RELATED_ID, AFTER_SALES_INSTALLSTION_ID, 
      AMOUNT, TRADER_ID, TRADER_TYPE, USER_ID, ORG_ID, ORG_NAME)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.capitalBillId,jdbcType=INTEGER}, #{item.bussinessType,jdbcType=INTEGER}, 
        #{item.orderType,jdbcType=TINYINT}, #{item.orderNo,jdbcType=VARCHAR}, #{item.relatedId,jdbcType=INTEGER}, 
        #{item.afterSalesInstallstionId,jdbcType=INTEGER}, #{item.amount,jdbcType=DECIMAL}, 
        #{item.traderId,jdbcType=INTEGER}, #{item.traderType,jdbcType=TINYINT}, #{item.userId,jdbcType=INTEGER}, 
        #{item.orgId,jdbcType=INTEGER}, #{item.orgName,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="getAfterSalesFlByBuyOrderId" resultType="java.math.BigDecimal">
    select IFNULL(sum(td.AMOUNT),0)
    from T_BUYORDER a
           left join T_AFTER_SALES b on a.BUYORDER_ID = b.ORDER_ID
           left join T_CAPITAL_BILL_DETAIL td on td.RELATED_ID = b.AFTER_SALES_ID
           left join T_CAPITAL_BILL tc on tc.CAPITAL_BILL_ID = td.CAPITAL_BILL_ID
    where b.ATFER_SALES_STATUS in (1, 2)
      and b.SUBJECT_TYPE = 536  -- 采购售后
      and a.BUYORDER_ID = #{buyOrderId,jdbcType=INTEGER}
      and td.BUSSINESS_TYPE = 531
      and tc.TRADER_MODE = 10000;
  </select>

  <select id="getBuyOrderHavePayMoney" resultType="java.math.BigDecimal">
    select IFNULL(sum(td.AMOUNT),0)
    from T_BUYORDER a
           left join T_CAPITAL_BILL_DETAIL td on td.RELATED_ID = a.BUYORDER_ID
           left join T_CAPITAL_BILL tc on tc.CAPITAL_BILL_ID = td.CAPITAL_BILL_ID
    where td.BUSSINESS_TYPE in(525,533)  -- 525订单付款 、533信用还款
      and tc.TRADER_MODE in (520,521,522,523,528)
      and td.ORDER_TYPE = 2 -- 采购资金流水
      and a.BUYORDER_ID  =#{buyOrderId,jdbcType=INTEGER};
  </select>

  <select id="getBuyOrderAfterSalesHavePayMoney" resultType="java.math.BigDecimal">
    select IFNULL(sum(td.AMOUNT),0)
    from T_BUYORDER a
           left join T_AFTER_SALES b on a.BUYORDER_ID = b.ORDER_ID
           left join T_CAPITAL_BILL_DETAIL td on td.RELATED_ID = b.AFTER_SALES_ID
           left join T_CAPITAL_BILL tc on tc.CAPITAL_BILL_ID = td.CAPITAL_BILL_ID
    where b.ATFER_SALES_STATUS in (1, 2)
      and b.SUBJECT_TYPE = 536 -- 采购售后
      and a.BUYORDER_ID = #{buyOrderId,jdbcType=INTEGER}
      and td.BUSSINESS_TYPE = 531 -- 退款
      and tc.TRADER_MODE in (520,521,522,523,528,530)
      and td.ORDER_TYPE = 3;  -- 售后
  </select>
</mapper>