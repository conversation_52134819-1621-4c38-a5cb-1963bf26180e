package com.vedeng.erp.kingdee.batch.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 生成直发采购出库单和入库单数据封装实体
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchGenerateDirectPurchaseOutAndInOrdersDto {


    private BatchBuyorderDto batchBuyorderDto;

    private List<Map<String, Object>> goodsIdAndSendNum;

    private BatchExpressDto batchExpressDto;

    private String id_sendN_sendedN_sumN;

}
