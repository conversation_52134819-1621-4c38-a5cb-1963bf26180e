<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.system.mapper.FlowNodeOrderDetailPriceMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.system.domain.entity.FlowNodeOrderDetailPriceEntity">
    <!--@mbg.generated-->
    <!--@Table T_FLOW_NODE_ORDER_DETAIL_PRICE-->
    <id column="FLOW_NODE_ORDER_PRICE_ID" property="flowNodeOrderPriceId" />
    <result column="FLOW_ORDER_DETAIL_ID" property="flowOrderDetailId" />
    <result column="FLOW_NODE_ID" property="flowNodeId" />
    <result column="NODE_LEVEL" property="nodeLevel" />
    <result column="PRICE" property="price" />
    <result column="MARKUP_RATE" property="markupRate" />
    <result column="GROSS_PROFIT_RATE" property="grossProfitRate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    FLOW_NODE_ORDER_PRICE_ID, FLOW_ORDER_DETAIL_ID, FLOW_NODE_ID, NODE_LEVEL, PRICE, 
    MARKUP_RATE, GROSS_PROFIT_RATE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_FLOW_NODE_ORDER_DETAIL_PRICE
    where FLOW_NODE_ORDER_PRICE_ID = #{flowNodeOrderPriceId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_FLOW_NODE_ORDER_DETAIL_PRICE
    where FLOW_NODE_ORDER_PRICE_ID = #{flowNodeOrderPriceId}
  </delete>
  <insert id="insert" keyColumn="FLOW_NODE_ORDER_PRICE_ID" keyProperty="flowNodeOrderPriceId" parameterType="com.vedeng.erp.system.domain.entity.FlowNodeOrderDetailPriceEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_FLOW_NODE_ORDER_DETAIL_PRICE (FLOW_ORDER_DETAIL_ID, FLOW_NODE_ID, NODE_LEVEL, PRICE, MARKUP_RATE, 
      GROSS_PROFIT_RATE)
    values (#{flowOrderDetailId}, #{flowNodeId}, #{nodeLevel}, #{price}, #{markupRate}, 
      #{grossProfitRate})
  </insert>
  <insert id="insertSelective" keyColumn="FLOW_NODE_ORDER_PRICE_ID" keyProperty="flowNodeOrderPriceId" parameterType="com.vedeng.erp.system.domain.entity.FlowNodeOrderDetailPriceEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_FLOW_NODE_ORDER_DETAIL_PRICE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="flowOrderDetailId != null">
        FLOW_ORDER_DETAIL_ID,
      </if>
      <if test="flowNodeId != null">
        FLOW_NODE_ID,
      </if>
      <if test="nodeLevel != null">
        NODE_LEVEL,
      </if>
      <if test="price != null">
        PRICE,
      </if>
      <if test="markupRate != null">
        MARKUP_RATE,
      </if>
      <if test="grossProfitRate != null">
        GROSS_PROFIT_RATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="flowOrderDetailId != null">
        #{flowOrderDetailId},
      </if>
      <if test="flowNodeId != null">
        #{flowNodeId},
      </if>
      <if test="nodeLevel != null">
        #{nodeLevel},
      </if>
      <if test="price != null">
        #{price},
      </if>
      <if test="markupRate != null">
        #{markupRate},
      </if>
      <if test="grossProfitRate != null">
        #{grossProfitRate},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.system.domain.entity.FlowNodeOrderDetailPriceEntity">
    <!--@mbg.generated-->
    update T_FLOW_NODE_ORDER_DETAIL_PRICE
    <set>
      <if test="flowOrderDetailId != null">
        FLOW_ORDER_DETAIL_ID = #{flowOrderDetailId},
      </if>
      <if test="flowNodeId != null">
        FLOW_NODE_ID = #{flowNodeId},
      </if>
      <if test="nodeLevel != null">
        NODE_LEVEL = #{nodeLevel},
      </if>
      <if test="price != null">
        PRICE = #{price},
      </if>
      <if test="markupRate != null">
        MARKUP_RATE = #{markupRate},
      </if>
      <if test="grossProfitRate != null">
        GROSS_PROFIT_RATE = #{grossProfitRate},
      </if>
    </set>
    where FLOW_NODE_ORDER_PRICE_ID = #{flowNodeOrderPriceId}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.system.domain.entity.FlowNodeOrderDetailPriceEntity">
    <!--@mbg.generated-->
    update T_FLOW_NODE_ORDER_DETAIL_PRICE
    set FLOW_ORDER_DETAIL_ID = #{flowOrderDetailId},
      FLOW_NODE_ID = #{flowNodeId},
      NODE_LEVEL = #{nodeLevel},
      PRICE = #{price},
      MARKUP_RATE = #{markupRate},
      GROSS_PROFIT_RATE = #{grossProfitRate}
    where FLOW_NODE_ORDER_PRICE_ID = #{flowNodeOrderPriceId}
  </update>

<!--auto generated by MybatisCodeHelper on 2025-01-08-->
  <select id="findByFlowNodeId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_FLOW_NODE_ORDER_DETAIL_PRICE
        where FLOW_NODE_ID=#{flowNodeId}
    </select>

<!--auto generated by MybatisCodeHelper on 2025-01-10-->
  <select id="findByFlowOrderDetailId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_FLOW_NODE_ORDER_DETAIL_PRICE
        where FLOW_ORDER_DETAIL_ID=#{flowOrderDetailId}
    </select>
  
  <select id="findByFlowOrderDetailIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_FLOW_NODE_ORDER_DETAIL_PRICE
    where FLOW_ORDER_DETAIL_ID in
    <foreach collection="flowOrderDetailIds" item="flowOrderDetailId" open="(" close=")" separator=",">
      #{flowOrderDetailId}
    </foreach>
  </select>
</mapper>
