<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.mapper.RegistrationFeedbackRecordMapper">


    <insert id="insert">
        INSERT INTO T_REGISTRATION_FEEDBACK_RECORD(REGISTRATION_NUMBER_ID, STATE, PROBLEM_TYPE, PROBLEM_REMARK,
                                                   REGISTRATION_NUMBER, GOODS_LEVEL_NO, GOODS_POSITION_NO, FILE_TYPE,
                                                   PURPOSE_TYPE, PURPOSE_REMARK, ASSIGNMENT_MANAGER_ID,
                                                   ASSIGNMENT_ASSISTANT_ID, ADD_TIME, CREATOR, MOD_TIME, UPDATER,FIRST_ENGAGE_ID)
        VALUES (#{registrationNumberId,jdbcType=INTEGER}, #{state,jdbcType=INTEGER}, #{problemType,jdbcType=INTEGER},
                #{problemRemark,jdbcType=VARCHAR}, #{registrationNumber,jdbcType=VARCHAR},
                #{goodsLevelNo,jdbcType=INTEGER}, #{goodsPositionNo,jdbcType=INTEGER}, #{fileType,jdbcType=INTEGER},
                #{purposeType,jdbcType=INTEGER}, #{purposeRemark,jdbcType=VARCHAR},
                #{assignmentManagerId,jdbcType=INTEGER}, #{assignmentAssistantId,jdbcType=INTEGER},
                #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT},
                #{updater,jdbcType=INTEGER},#{firstEngageId,jdbcType=INTEGER})
    </insert>

    <select id="listByFirstEngageId" resultType="com.vedeng.goods.dto.RegistrationFeedbackDto">
        SELECT *
        FROM T_REGISTRATION_FEEDBACK_RECORD
        WHERE FIRST_ENGAGE_ID = #{firstEngageId,jdbcType=INTEGER}
        ORDER BY ADD_TIME DESC
    </select>

    <update id="messageReplyById">
        UPDATE T_REGISTRATION_FEEDBACK_RECORD
        SET MESSAGE_REPLY  = #{messageReply,jdbcType=VARCHAR},
            STATE          = 1,
            DEALER_USER_ID = #{userId,jdbcType=INTEGER},
            MOD_TIME       = #{modTime,jdbcType=BIGINT},
            UPDATER        = #{userId,jdbcType=INTEGER}
        WHERE REGISTRATION_FEEDBACK_RECORD_ID = #{id,jdbcType=INTEGER}
          AND STATE != 1
    </update>

    <select id="getById" resultType="com.vedeng.goods.domain.entity.RegistrationFeedbackRecordEntity">
        SELECT *
        FROM T_REGISTRATION_FEEDBACK_RECORD
        WHERE REGISTRATION_FEEDBACK_RECORD_ID = #{registrationFeedbackRecordId,jdbcType=INTEGER}
    </select>

    <select id="feedbackCount" resultType="com.vedeng.goods.domain.dto.FeedbackCountDto">
        SELECT PROBLEM_TYPE as problemType, count(1) as countNum
        FROM T_REGISTRATION_FEEDBACK_RECORD
        WHERE REGISTRATION_NUMBER_ID = #{registrationNumberId,jdbcType=INTEGER}
        GROUP BY PROBLEM_TYPE
    </select>
</mapper>
