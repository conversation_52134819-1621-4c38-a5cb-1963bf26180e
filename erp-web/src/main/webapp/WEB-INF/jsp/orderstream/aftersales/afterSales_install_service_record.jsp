<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>

<%--<input type="hidden" name="ossHttp" id="ossHttp" value="${ossHttp}">--%>
<div class="title-container">
    <div class="table-title nobor">
        安调服务记录
    </div>
    <c:if test="${(afterSalesVo.atferSalesStatus eq 1 || afterSalesVo.atferSalesStatus eq 2) && user.positType eq 312 && not empty afterSalesVo.atGoodsList}">
        <c:set var="display" value="false"/>
        <c:forEach var="goods" items="${afterSalesVo.atGoodsList}">
            <c:if test="${goods.afterSaleUpLimitNum gt 0}">
                <c:set var="display" value="true"/>
            </c:if>
        </c:forEach>
        <c:if test="${display}">
            <div class="title-click nobor  pop-new-data" layerParams='{"width":"1000px","height":"800px","title":"新增服务记录",
                   "link":"<%= basePath %>/order/afterSalesCommon/initAddServiceRecord.do?aftersalesId=${afterSalesVo.afterSalesId}"}'>
                新增服务记录
            </div>
        </c:if>
    </c:if>
</div>
<div class="parts">
    <table class="table">
        <thead>
        <tr>
            <th>序号</th>
            <th>订货号</th>
            <th>产品名称</th>
            <th>品牌</th>
            <th>型号</th>
            <th>验收时间</th>
            <th>本次服务数量</th>
            <th>服务商品序列号</th>
            <th>验收方式</th>
            <th>录音ID</th>
            <th>验收结论</th>
            <th>操作</th>
        </tr>
        </thead>
        <tbody>
        <c:forEach items="${installServiceRecordList}" var="record" varStatus="status">
            <c:forEach items="${record.recordDetailList}" var="detail" varStatus="subStatus">
                <tr>
                    <c:if test="${subStatus.count eq 1}">
                        <td rowspan="${record.recordDetailList.size()}">${status.count}</td>
                    </c:if>
                    <td>${detail.sku}</td>
                    <td>${detail.skuName}</td>
                    <td>${detail.brand}</td>
                    <td>${detail.model}</td>
                    <td><fmt:formatDate value="${record.checkDate}" pattern="yyyy-MM-dd"/></td>
                    <td>${detail.num}</td>
                    <td>${(detail.supplCode == '' || detail.supplCode == null) && detail.supplCode != '自定义序列号'? detail.serialNumber : detail.supplCode}</td>
                    <td>
                        <c:if test="${record.checkType eq 1}">电话回访</c:if>
                        <c:if test="${record.checkType eq 2}">纸单验收</c:if>
                        <c:if test="${record.checkType eq 3}">短信通知</c:if>
                    </td>
                    <td>${record.recordId}</td>
                    <td>${record.checkConclusionName}</td>
                    <c:if test="${subStatus.count eq 1}">
                        <td class="caozuo" rowspan="${record.recordDetailList.size()}">
                            <c:if test="${afterSalesVo.atferSalesStatus eq 1 || afterSalesVo.atferSalesStatus eq 2}">
                                <span class="border-blue pop-new-data" layerParams='{"width":"60%","height":"63%","title":"编辑安调服务记录",
                                    "link":"<%= basePath %>/order/afterSalesCommon/initEditServiceRecord.do?afterSalesServiceId=${record.afterSalesServiceId}"}'>编辑</span>
                                <span class="delete" onclick="delRecordDetail(${record.afterSalesServiceId});" style="display: none;background-color: gray;">删除</span>
                            </c:if>
                            <span class="caozuo-blue pop-new-data"
                                  layerparams='{"width":"50%","height":"70%","title":"查看服务凭证","link":"<%=basePath%>/order/afterSalesCommon/viewAttachment.do?afterSalesServiceId=${record.afterSalesServiceId}"}'>查看服务凭证</span>
                        </td>
                    </c:if>
                </tr>
            </c:forEach>
        </c:forEach>
        <c:if test="${empty installServiceRecordList}">
            <tr>
                <td colspan='12'>暂无任何数据！</td>
            </tr>
        </c:if>
        </tbody>
    </table>
</div>

<script>
    //删除
    function delRecordDetail(afterSalesServiceId) {
        checkLogin();
        if (afterSalesServiceId > 0) {
            layer.confirm("您是否确认删除？", {
                btn: ['确定', '取消'] //按钮
            }, function () {
                $.ajax({
                    type: "POST",
                    url: "/order/afterSalesCommon/deleteServiceRecord.do",
                    data: {'afterSalesServiceId': afterSalesServiceId},
                    dataType: 'json',
                    success: function (data) {
                        parent.layer.close(index);
                        window.location.reload();
                    },
                    error: function (data) {
                        if (data.status == 1001) {
                            layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                        }
                    }
                });
            }, function () {
            });
        }
    }




</script>


