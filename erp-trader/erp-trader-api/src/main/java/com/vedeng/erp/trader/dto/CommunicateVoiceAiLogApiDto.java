package com.vedeng.erp.trader.dto;

import lombok.Data;

import java.util.Date;

@Data
public class CommunicateVoiceAiLogApiDto {
    /**
     * 主键
     */
    private Long id;

    /**
     * T_COMMUNICATE_RECORD表的ID
     */
    private Integer communicateRecordId;

    /**
     * 场景
     */
    private String senceCode;

    /**
     * 分组名称
     */
    private String groupCode;

    /**
     * GPT版本
     */
    private String gptVersion;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 请求内容
     */
    private String requestText;

    /**
     * 返回内容
     */
    private String responseText;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 修改时间
     */
    private Date modTime;

    /**
     * 创建者id
     */
    private Integer creator;

}
