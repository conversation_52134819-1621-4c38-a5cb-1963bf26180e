package com.vedeng.goods.model;

public class GoodsGysOptionAttributegenerate {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_SYS_OPTION_ATTRIBUTE.GOODS_SYS_OPTION_ATTRIBUTE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer goodsSysOptionAttributeId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_SYS_OPTION_ATTRIBUTE.GOODS_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer goodsId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_SYS_OPTION_ATTRIBUTE.ATTRIBUTE_TYPE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer attributeType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_SYS_OPTION_ATTRIBUTE.ATTRIBUTE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer attributeId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_SYS_OPTION_ATTRIBUTE.ATTRIBUTE_OTHER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String attributeOther;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_SYS_OPTION_ATTRIBUTE.GOODS_SYS_OPTION_ATTRIBUTE_ID
     *
     * @return the value of T_GOODS_SYS_OPTION_ATTRIBUTE.GOODS_SYS_OPTION_ATTRIBUTE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getGoodsSysOptionAttributeId() {
        return goodsSysOptionAttributeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_SYS_OPTION_ATTRIBUTE.GOODS_SYS_OPTION_ATTRIBUTE_ID
     *
     * @param goodsSysOptionAttributeId the value for T_GOODS_SYS_OPTION_ATTRIBUTE.GOODS_SYS_OPTION_ATTRIBUTE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setGoodsSysOptionAttributeId(Integer goodsSysOptionAttributeId) {
        this.goodsSysOptionAttributeId = goodsSysOptionAttributeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_SYS_OPTION_ATTRIBUTE.GOODS_ID
     *
     * @return the value of T_GOODS_SYS_OPTION_ATTRIBUTE.GOODS_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getGoodsId() {
        return goodsId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_SYS_OPTION_ATTRIBUTE.GOODS_ID
     *
     * @param goodsId the value for T_GOODS_SYS_OPTION_ATTRIBUTE.GOODS_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_SYS_OPTION_ATTRIBUTE.ATTRIBUTE_TYPE
     *
     * @return the value of T_GOODS_SYS_OPTION_ATTRIBUTE.ATTRIBUTE_TYPE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getAttributeType() {
        return attributeType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_SYS_OPTION_ATTRIBUTE.ATTRIBUTE_TYPE
     *
     * @param attributeType the value for T_GOODS_SYS_OPTION_ATTRIBUTE.ATTRIBUTE_TYPE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setAttributeType(Integer attributeType) {
        this.attributeType = attributeType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_SYS_OPTION_ATTRIBUTE.ATTRIBUTE_ID
     *
     * @return the value of T_GOODS_SYS_OPTION_ATTRIBUTE.ATTRIBUTE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getAttributeId() {
        return attributeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_SYS_OPTION_ATTRIBUTE.ATTRIBUTE_ID
     *
     * @param attributeId the value for T_GOODS_SYS_OPTION_ATTRIBUTE.ATTRIBUTE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setAttributeId(Integer attributeId) {
        this.attributeId = attributeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_SYS_OPTION_ATTRIBUTE.ATTRIBUTE_OTHER
     *
     * @return the value of T_GOODS_SYS_OPTION_ATTRIBUTE.ATTRIBUTE_OTHER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getAttributeOther() {
        return attributeOther;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_SYS_OPTION_ATTRIBUTE.ATTRIBUTE_OTHER
     *
     * @param attributeOther the value for T_GOODS_SYS_OPTION_ATTRIBUTE.ATTRIBUTE_OTHER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setAttributeOther(String attributeOther) {
        this.attributeOther = attributeOther;
    }
}