$(function () {
    $("select[name='zhaobiaoProvince']").change(function () {
        checkLogin();
        var regionId = $(this).val();
        if (regionId > 0) {
            $.ajax({
                type: "POST",
                url: page_url + "/system/region/getregion.do",
                data: {'regionId': regionId},
                dataType: 'json',
                success: function (data) {
                    $option = "<option value='0'>请选择</option>";
                    $.each(data.listData, function (i, n) {
                        $option += "<option value='" + data.listData[i]['regionId'] + "'>" + data.listData[i]['regionName'] + "</option>";
                    });
                    $("select[name='zhaobiaoCity'] option:gt(0)").remove();
                    $("select[name='zhaobiaoCountry'] option:gt(0)").remove();
                    $("#zhaobiaoCountry").val("0").trigger("change");
                    $("#zhaobiaoCity").val("0").trigger("change");

                    $("select[name='zhaobiaoCity']").html($option);
                    $("select[name='zhaobiaoCountry']").html("<option value='0'>请选择</option>");
                },
                error: function (data) {
                    if (data.status == 1001) {
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });
        } else if (regionId == 0) {
            $("select[name='zhaobiaoCity'] option:gt(0)").remove();
            $("select[name='zhaobiaoCountry'] option:gt(0)").remove();
        }
    });

    $("select[name='zhaobiaoCity']").change(function () {
        checkLogin();
        var regionId = $(this).val();
        if (regionId > 0) {
            $.ajax({
                type: "POST",
                url: page_url + "/system/region/getregion.do",
                data: {'regionId': regionId},
                dataType: 'json',
                success: function (data) {
                    $option = "<option value='0'>请选择</option>";
                    $.each(data.listData, function (i, n) {
                        $option += "<option value='" + data.listData[i]['regionId'] + "'>" + data.listData[i]['regionName'] + "</option>";
                    });
                    $("select[name='zhaobiaoCountry'] option:gt(0)").remove();

                    $("select[name='zhaobiaoCountry']").html($option);
                },
                error: function (data) {
                    if (data.status == 1001) {
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });
        } else if (regionId == 0) {
            $("select[name='zhaobiaoCountry'] option:gt(0)").remove();
        }
    });
});


function viewRemarks(remarks) {
    if (remarks == '') {
        remarks = '暂无!';
    }
    layer.alert(remarks);
}


function viewZhaoBiao(infoId) {
    window.open('https://www.vedeng.com/zhaobiao/detail-' + infoId + '.html');
}