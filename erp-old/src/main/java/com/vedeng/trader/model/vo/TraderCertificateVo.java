package com.vedeng.trader.model.vo;

import com.vedeng.trader.model.TraderCertificate;

public class TraderCertificateVo extends TraderCertificate implements Cloneable {

	/**
	 * @Fields serialVersionUID : TODO
	 */
	private static final long serialVersionUID = 1L;

	private Integer threeInOne;// 三证合一

	private Integer medicalQualification;// 医疗资质合一
	
	private Integer customerType;//客户所属类型：1-终端；2-分销
	
	private String[] uris;//用来存放多个图片的地址
    //用来存放多个图片名称
	private String[] names;

	/**
	 * 效期开始时间
	 */
	private String[] startTimes;

	/**
	 * 效期结束时间
	 */
	private String[] endTimes;

	/**
	 * 许可证编号
	 */
	private String[] sns;

	/**
	 * 文件后缀名
	 */
	private String suffix;



	/**
	 * 文件前缀名
	 */
	private String docPreName;

	public String getSuffix() {
		return suffix;
	}

	public void setSuffix(String suffix) {
		this.suffix = suffix;
	}

	public String getDocPreName() {
		return docPreName;
	}

	public void setDocPreName(String docPreName) {
		this.docPreName = docPreName;
	}


	public String[] getNames() {
		return names;
	}

	public void setNames(String[] names) {
		this.names = names;
	}

	public String[] getUris() {
        return uris;
    }

    public void setUris(String[] uris) {
        this.uris = uris;
    }

    public Integer getThreeInOne() {
		return threeInOne;
	}

	public void setThreeInOne(Integer threeInOne) {
		this.threeInOne = threeInOne;
	}

	public Integer getMedicalQualification() {
		return medicalQualification;
	}

	public void setMedicalQualification(Integer medicalQualification) {
		this.medicalQualification = medicalQualification;
	}

	@Override
	public Object clone() throws CloneNotSupportedException {
		return super.clone();
	}

	public Integer getCustomerType() {
		return customerType;
	}

	public void setCustomerType(Integer customerType) {
		this.customerType = customerType;
	}

	public String[] getStartTimes() {
		return startTimes;
	}

	public void setStartTimes(String[] startTimes) {
		this.startTimes = startTimes;
	}

	public String[] getEndTimes() {
		return endTimes;
	}

	public void setEndTimes(String[] endTimes) {
		this.endTimes = endTimes;
	}

	public String[] getSns() {
		return sns;
	}

	public void setSns(String[] sns) {
		this.sns = sns;
	}
}
