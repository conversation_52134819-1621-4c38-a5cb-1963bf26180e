<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:util="http://www.springframework.org/schema/util"
	xsi:schemaLocation="  
    http://www.springframework.org/schema/context  
	http://www.springframework.org/schema/context/spring-context-3.1.xsd  
    http://www.springframework.org/schema/beans  
 	http://www.springframework.org/schema/beans/spring-beans-3.1.xsd  
    http://www.springframework.org/schema/util  
    http://www.springframework.org/schema/util/spring-util.xsd">

	<!--axis2交给spring来管理 -->
	<bean id="applicationContext"
		class="org.apache.axis2.extensions.spring.receivers.ApplicationContextHolder" />

	<context:component-scan base-package="com.vedeng.soap" />
</beans> 