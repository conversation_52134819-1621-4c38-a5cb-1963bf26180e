package com.vedeng.system.model;

import java.io.Serializable;

public class Attachment implements Serializable{

    private Integer attachmentId;

    private Integer attachmentType;

    private Integer attachmentFunction;

    private Integer relatedId;

    private String name;

    private String domain;

    private String uri;

    private String alt;

    private Integer sort;

    private Integer isDefault;

    private Long addTime;

    private Integer creator;

    private String suffix; //文件后缀

    private String username;//操作人名称

    private String relatedNo;

    private Integer isDeleted; //是否有效 0有效 1失效

    /**
     * oss资源标识
     */
    private String ossResourceId;
    // end by franlin for[3865 供应商资质中，增加销售人授权书，销售人信息]  at 2018-06-21

    private String originalFilepath;

    private Integer synSuccess;

    private Long costTime;

    private String httpUrl;

    /**
     *  为了兼容前端上传
     */
    private String filePath;

    private String creatorName;
    private String addTimeStr;

    private String fileName;

    private String prefix;


    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getAddTimeStr() {
        return addTimeStr;
    }

    public void setAddTimeStr(String addTimeStr) {
        this.addTimeStr = addTimeStr;
    }

    public Integer getAttachmentId() {
        return attachmentId;
    }

    public void setAttachmentId(Integer attachmentId) {
        this.attachmentId = attachmentId;
    }

    public Integer getAttachmentType() {
        return attachmentType;
    }

    public void setAttachmentType(Integer attachmentType) {
        this.attachmentType = attachmentType;
    }

    public Integer getAttachmentFunction() {
        return attachmentFunction;
    }

    public void setAttachmentFunction(Integer attachmentFunction) {
        this.attachmentFunction = attachmentFunction;
    }

    public Integer getRelatedId() {
        return relatedId;
    }

    public void setRelatedId(Integer relatedId) {
        this.relatedId = relatedId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain == null ? null : domain.trim();
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri == null ? null : uri.trim();
        this.filePath = this.uri;
    }

    public String getAlt() {
        return alt;
    }

    public void setAlt(String alt) {
        this.alt = alt == null ? null : alt.trim();
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getRelatedNo() {
		return relatedNo;
	}

	public void setRelatedNo(String relatedNo) {
		this.relatedNo = relatedNo;
	}

    public String getOssResourceId() {
        return ossResourceId;
    }

    public void setOssResourceId(String ossResourceId) {
        this.ossResourceId = ossResourceId;
    }

    public String getOriginalFilepath() {
        return originalFilepath;
    }

    public void setOriginalFilepath(String originalFilepath) {
        this.originalFilepath = originalFilepath;
    }

    public Integer getSynSuccess() {
        return synSuccess;
    }

    public void setSynSuccess(Integer synSuccess) {
        this.synSuccess = synSuccess;
    }

    public Long getCostTime() {
        return costTime;
    }

    public void setCostTime(Long costTime) {
        this.costTime = costTime;
    }


    public void setIsDeleted(int isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getSuffix() {
        return suffix;
    }

    public void setSuffix(String suffix) {
        this.suffix = suffix;
    }

    @Override
    public String toString() {
        return "Attachment{" +
                "attachmentId=" + attachmentId +
                ", attachmentType=" + attachmentType +
                ", attachmentFunction=" + attachmentFunction +
                ", relatedId=" + relatedId +
                ", name='" + name + '\'' +
                ", domain='" + domain + '\'' +
                ", uri='" + uri + '\'' +
                ", alt='" + alt + '\'' +
                ", sort=" + sort +
                ", isDefault=" + isDefault +
                ", addTime=" + addTime +
                ", creator=" + creator +
                ", username='" + username + '\'' +
                ", relatedNo='" + relatedNo + '\'' +
                ", suffix='" + suffix + '\'' +
                '}';
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getHttpUrl() {
        return httpUrl;
    }

    public void setHttpUrl(String httpUrl) {
        this.httpUrl = httpUrl;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }
}
