package com.vedeng.common.shiro.support;

import com.vedeng.common.shiro.cas.CasClientHelper;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.filter.DelegatingFilterProxy;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class CompatibleSecurityFilterProxy extends DelegatingFilterProxy {

    public static final String LEGACY_BEAN_NAME = "legacyShiroFilter";
    public static final String CAS_BEAN_NAME = "casShiroFilter";

    private Filter legacyShiroFilter;
    private Filter casShiroFilter;

    //以下四个变量判定是否静态资源，判定成功时，不走shiro,将不读取shiro，减少内存消耗
    private static final String STATIC_RESOURCE_REGEX = "\".*(\\\\.(ico|js|css|jpg|png|txt|gif|jpeg|ico|bmp))";
    private static final Pattern STATIC_RESOURCE_PATTERN = Pattern.compile(STATIC_RESOURCE_REGEX);

    private final String[] excludeUrl = new String[]{"^/static/.*","^/webjars/.*"};
    private final List<Pattern> excludePattern = new LinkedList<>();

    //非静态资源，访问量大，但不需要读取session，直接跳过登录信息的读取，前端userId已传过值了
    private final String[] unReadSessionUrlList = new String[]{
            "/order/quote/queryForSaleMall.do",
            "/system/message/queryJCONoReadMsgNum.do",
            "/system/message/getAllMessageNoread.do",
            "/system/message/queryNoReadMsgNum.do"};


    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        boolean isStatic = false;
        if(request instanceof HttpServletRequest) {
            HttpServletRequest httpRequest = (HttpServletRequest) request;
            String requestURI = httpRequest.getRequestURI();
            if(isStaticUrl(requestURI)){
                isStatic = true;
            }
        }
        if(!isStatic) {
            if (CasClientHelper.enableSingleSignOn()) {
                casShiroFilter.doFilter(request, response, chain);
            } else {
                legacyShiroFilter.doFilter(request, response, chain);
            }
        }else{
            chain.doFilter(request, response);
        }
    }

    @Override
    protected void initFilterBean() throws ServletException {
        synchronized (this) {
            WebApplicationContext wac = findWebApplicationContext();
            if (wac != null && ((ConfigurableApplicationContext) wac).isActive()) {
                this.legacyShiroFilter = initDelegateWithBeanName(wac, LEGACY_BEAN_NAME);
                this.casShiroFilter = initDelegateWithBeanName(wac, CAS_BEAN_NAME);
            }
            //将需要判断url规则的url加到excludePattern中。
            for(String url: excludeUrl) {
                try{
                    Pattern pInclude = Pattern.compile(url);
                    this.excludePattern.add(pInclude);
                }catch (Exception e){
                    logger.error("load pattern failure"+url,e);
                }

            }
        }
    }

    protected Filter initDelegateWithBeanName(WebApplicationContext wac, String beaName) throws ServletException {
        Filter delegate = wac.getBean(beaName, Filter.class);
        if (isTargetFilterLifecycle()) {
            delegate.init(getFilterConfig());
        }
        return delegate;
    }

    private boolean isStaticUrl(String originatingUrl) {
        for(String unReadSessionUrl: unReadSessionUrlList) {
            if(unReadSessionUrl.equals(originatingUrl)) {
                return true;
            }
        }
        return STATIC_RESOURCE_PATTERN.matcher(originatingUrl).matches()
                || match(excludePattern, originatingUrl);
    }

    protected static boolean match(List<Pattern> list, String s) {
        if (s == null || list == null || list.size() == 0) {
            return false;
        } else {
            for (int i = 0; i < list.size(); i++) {
                Pattern pInclude = list.get(i);
                Matcher matcher = pInclude.matcher(s);
                if (matcher.matches()) {
                    return true;
                }
            }
            return false;
        }
    }


}
