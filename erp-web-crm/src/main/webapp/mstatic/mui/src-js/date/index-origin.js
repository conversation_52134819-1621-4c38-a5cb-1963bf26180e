
// 日期公共方法
const util_date = {
    DEFAULT_FORMATS: {
        date: 'yyyy-MM-dd',
        month: 'yyyy-MM',
        datetime: 'yyyy-MM-dd HH:mm:ss',
        time: 'HH:mm:ss',
        week: 'yyyywWW',
        timerange: 'HH:mm:ss',
        daterange: 'yyyy-MM-dd',
        monthrange: 'yyyy-MM',
        datetimerange: 'yyyy-MM-dd HH:mm:ss',
        year: 'yyyy'
    },

    HAVE_TRIGGER_TYPES: [
        'date',
        'datetime',
        'time',
        'time-select',
        'week',
        'month',
        'year',
        'daterange',
        'monthrange',
        'timerange',
        'datetimerange',
        'dates'
    ],

    /* 日期部分 */
    weeks: ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'],
    months: ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'],
    monthView: ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'],
    regDatetime: /^\s*[0-9]{1,4}-[0-9]{1,2}-[0-9]{1,2}\s*[0-9]{1,2}:[0-9]{1,2}:[0-9]{1,2}\s*$/,

    // 小于10拼接0
    beautify: (str, max = 10000, min = 1)=> {
        if (!str) return '';
        str = parseInt(str);
        if (isNaN(str)) return '';
        if (max && typeof(max) != 'number') throw new Error('type error')

        if (str < 10) {
            return '0' + str
        } else if (str > max) {
            return ''
        } else {
            return str
        }
    },

    // 数组转化时间
    arrayToDate: (arr) => {
        if (!Object.prototype.toString.call(arr)) {
            return null;
        }
        let finalArr = arr.map(item => {
            if (util_date.isDate(item)) {
                return item
            }
            let d1 = util_date.toDate(item);
            if (util_date.isDate(d1) && d1 != 'Invalid Date') {
                return d1
            } else {
                return null
            }
        });

        return finalArr;
    },

    // 转化成日期格式
    toDate: function(date) {
        typeof(date) !== 'string' && ( date += '' );
        let val = new Date(date) || null;
        return val
    },

    // 判断是否是日期格式
    isDate: (val) =>  {
        return Object.prototype.toString.call(val) == '[object Date]';
    },

    // 返回默认当天的时间格式
    timeToDate: function (time) {
        if (!util_date.checkToTime(time)) return '';
        let date = new Date();
        let Y = date.getFullYear();
        let M = date.getMonth() + 1;
        let D = date.getDate();

        return new Date(`${ Y }-${ M }-${ D } ${ time }`)
    },

    // 返回日期事件格式
    timeToDatetime: (val) => {
        if (!util_date.checkToDatetime(val)) return '';

        let t = new Date(val);
        if (t == 'Invalid Date') return '';
        
        return new Date(`${ util_date.getDateStr(t) } ${ util_date.getTimeStr(t) }`);
    },

    // 获取当前月份 有几天
    getDayCountOfMonth: function(year, month) {
        if (month === 3 || month === 5 || month === 8 || month === 10) {
            return 30;
        }
        if (month === 1) {
            if (year % 4 === 0 && year % 100 !== 0 || year % 400 === 0) {
                return 29;
            } else {
                return 28;
            }
        }
        return 31;
    },

    getDayCountOfYear: function(year) {
        const isLeapYear = year % 400 === 0 || (year % 100 !== 0 && year % 4 === 0);
        return isLeapYear ? 366 : 365;
    },

    getFirstDayOfMonth: function(date) {
        const temp = new Date(date.getTime());
        temp.setDate(1);
        return temp.getDay();
    },

    prevDate: function(date, amount = 1) {
        return new Date(date.getFullYear(), date.getMonth(), date.getDate() - amount);
    },

    nextDate: function(date, amount = 1) {
        return new Date(date.getFullYear(), date.getMonth(), date.getDate() + amount);
    },

    getStartDateOfMonth: function(year, month) {
        const result = new Date(year, month, 1);
        const day = result.getDay();
    
        if (day === 0) {
            return util_date.prevDate(result, 7);
        } else {
            return util_date.prevDate(result, day);
        }
    },

    range: function(n) {
        return Array.apply(null, {length: n}).map((_, n) => n);
    },

    modifyDate: function(date, y, m, d) {
        return new Date(y, m, d, date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());
    },

    modifyTime: function(date, h, m, s) {
        return new Date(date.getFullYear(), date.getMonth(), date.getDate(), h, m, s, date.getMilliseconds());
    },

    clearTime: function(date) {
        return new Date(date.getFullYear(), date.getMonth(), date.getDate());
    },

    changeYearMonthAndClampDate: function(date, year, month) {
        // clamp date to the number of days in `year`, `month`
        // eg: (2010-1-31, 2010, 2) => 2010-2-28
        const monthDate = Math.min(date.getDate(), util_date.getDayCountOfMonth(year, month));
        return util_date.modifyDate(date, year, month, monthDate);
    },

    prevMonth: function(date) {
        const year = date.getFullYear();
        const month = date.getMonth();
        return month === 0
            ? util_date.changeYearMonthAndClampDate(date, year - 1, 11)
            : util_date.changeYearMonthAndClampDate(date, year, month - 1);
    },

    nextMonth: function(date) {
        const year = date.getFullYear();
        const month = date.getMonth();
        return month === 11
            ? util_date.changeYearMonthAndClampDate(date, year + 1, 0)
            : util_date.changeYearMonthAndClampDate(date, year, month + 1);
    },

    prevYear: function(date, amount = 1) {
        const year = date.getFullYear();
        const month = date.getMonth();
        return util_date.changeYearMonthAndClampDate(date, year - amount, month);
    },

    nextYear: function(date, amount = 1) {
        const year = date.getFullYear();
        const month = date.getMonth();
        return util_date.changeYearMonthAndClampDate(date, year + amount, month);
    },


    // 自定义方法
    // 验证用户输入 能否转化成日期
    checkStringCanToDate: (value, type )=> {
        if (!value) return '';
        if (util_date.isDate(value)) return value;

        if ( type == 'date' ) {
            return util_date.checkToDate(value);
        }
        else if (type == 'year') {
            return util_date.checkToYear(value);
        }
        else if (type == 'month') {
            return util_date.checkToMonth(value);
        }
        else if (type == 'time') {
            return util_date.checkToTime(value);
        }
        else if (type == 'datetime') {
            return util_date.checkToDatetime(value);
        }
        return '';
    },

    // 验证用户输入是否符合 日期格式
    checkToDate: (value)=> {
        typeof(value) != 'string' && ( value += '' );   // 转化字符串
        if (!~value.indexOf('-')) return '';            // 不含关键字符

        let arr = value.split('-');
        let getY = parseInt(arr[0]);
        let arrM = parseInt(arr[1]);
        let arrD = parseInt(arr[2]);
        let year = util_date.beautify(getY, 10000, 1970);
        let month = util_date.beautify(arrM, 31);
        let day = util_date.beautify(arrD, 31);
        if (year && month && day) {
            return `${ year }-${ month }-${ day }`;
        }
        else {
            return '';
        }
    },

    // 验证用户输入是否符合年份格式
    checkToYear: (value) => {
        if (!value) return '';
        if (util_date.isDate(value)) return value;   // 如果已经是时间格式: 不处理
        let intY = parseInt(value);
        let year = intY >= 1970 && intY < 10000 ? intY : '';
        return `${ year }`;
    },

    // 验证用户输入是否符合月份格式
    checkToMonth: (value) => {
        typeof(value) != 'string' && ( value += '' );   // 转化字符串
        if (!~value.indexOf('-')) return '';            // 不含关键字符

        let arr = value.split('-');
        let arrY = parseInt(arr[0]);
        let arrM = parseInt(arr[1]);
        let year = arrY >= 1970 && arrY < 10000 ? arrY : '';
        let month = arrM > 0 && arrM < 10 ? '0' + arrM : (arrM >= 10 && arrM <= 12 ? arrM : '');
        if (year && month) {
            return `${ year }-${ month }`;
        }
        else {
            return '';
        }
    },

    checkToTime: (value)=> {
        typeof(value) != 'string' && ( value += '' );   // 转化字符串
        if (!~value.indexOf(':')) return '';            // 不含关键字符

        let arr = value.split(':');
        let arrH = parseInt(arr[0]);
        let arrM = parseInt(arr[1]);
        let arrS = parseInt(arr[2]);
        let hour = arrH > 0 && arrH < 10 ? '0' + arrH : (arrH >= 10 && arrH <= 23 ? arrH : '');
        let minute = arrM > 0 && arrM < 10 ? '0' + arrM : (arrM >= 10 && arrM <= 59 ? arrM : '');
        let second = arrS > 0 && arrS < 10 ? '0' + arrS : (arrS >= 10 && arrS <= 59 ? arrS : '');

        if (hour && minute && second) {
            return `${ hour }:${ minute }:${ second }`;
        }
        else {
            return '';
        }
    },

    checkToDatetime: (value)=> {
        typeof(value) != 'string' && ( value += '' );   // 转化字符串
        if (!util_date.regDatetime.test(value)) return '';

        let step = value.split(' ');
        let date = step[0].split('-');
        let time = step[1].split(':');

        let Y = parseInt(date[0]);
        let M = parseInt(date[1]);
        let D = parseInt(date[2]);
        let h = parseInt(time[0]);
        let m = parseInt(time[1]);
        let s = parseInt(time[2]);

        Y = (Y >= 1970 && Y < 10000) ? Y : '';
        M = M > 0 && M < 10 ? '0' + M : (M >= 10 && M <= 12 ? M : '');
        D = D > 0 && D < 10 ? '0' + D : (D >= 10 && D <= 31 ? D : '');
        h = h > 0 && h < 10 ? '0' + h : (h >= 10 && h <= 23 ? h : '');
        m = m > 0 && m < 10 ? '0' + m : (m >= 10 && m <= 59 ? m : '');
        s = s > 0 && s < 10 ? '0' + s : (s >= 10 && s <= 59 ? s : '');

        if (Y && M && D && h && m && s) {
            return `${ Y }-${ M }-${ D } ${ h }:${ m }:${ s }`;
        }
        else {
            return ''
        }
    },


    // 格式化日期
    format: (val, type, ) => {
        if (type == 'year') {
            return util_date.formatYear(val);
        }
        else if (type == 'month') {
            return util_date.formatMonth(val);
        }
        else if (type == 'date') {
            return util_date.formatDate(val);
        }
        else if ( type == 'datetime' ) {
            return util_date.formatDatetime(val);
        }
        else if ( type == 'time' ) {
            return util_date.formatTime(val);
        }
        else if ( type == 'daterange' ) {
            return util_date.formatDateRange(val);
        }
    },

    // 格式化日期
    formatDate: (val)=> {
        if (!val) return '';
        let t = util_date.isDate(val) ? val : new Date(val);
        if (t == 'Invalid Date') return '';
        return util_date.getDateStr(t);
    },

    // 格式化年份
    formatYear: (val) => {
        if (!val) return '';
        let t = util_date.isDate(val) ? val : new Date(val);
        if (t == 'Invalid Date') return '';

        let Y = t.getFullYear();
        return Y;
    },

    // 格式化月份
    formatMonth: (val) => {
        if (!val) return '';
        let t = util_date.isDate(val) ? val : new Date(val);
        if (t == 'Invalid Date') return '';

        let M = t.getMonth() + 1;
        M < 10 && ( M = '0' + M);

        return `${ t.getFullYear() }-${ M }`
    },

    // 格式化日期时间
    formatDatetime: (val) => {
        if (!val) return '';
        let t = util_date.isDate(val) ? val : new Date(val);
        if (t == 'Invalid Date') return '';

        return `${ util_date.getDateStr(t) } ${ util_date.getTimeStr(t) }`
    },

    formatTime: (val)=> {
        if (!val) return '';
        let t = util_date.isDate(val) ? val : new Date(val);
        if (t == 'Invalid Date') return '';

        return util_date.getTimeStr(t);
    },

    formatDateRange: (val)=> {
        if (!val.length) return [];
        let D1 = util_date.formatDate(val[0]) || '';
        let D2 = util_date.formatDate(val[1]) || '';
        console.log('D1:', D1);
        console.log('D2:', D2);
        return [D1, D2];
    },

    // 获取年月日 （val一定是date类型）
    getDateStr: (val) => {
        if (!val) return '';
        if (!util_date.isDate(val)) return '';

        let date = new Date(val);
        let Y = date.getFullYear();
        let M = date.getMonth() + 1;
        let D = date.getDate();

        M < 10 && ( M = '0' + M);
        D < 10 && ( D = '0' + D);

        return `${ Y }-${ M }-${ D }`;
    },

    // 获取时分秒 (val必须是date类型)
    getTimeStr: (val) => {
        if (!val) return '';
        if (!util_date.isDate(val)) return '';
        let time = new Date(val);

        let h = time.getHours();
        let m = time.getMinutes();
        let s = time.getSeconds();

        h < 10 && ( h = '0' + h);
        m < 10 && ( m = '0' + m);
        s < 10 && ( s = '0' + s);

        return `${ h }:${ m }:${ s }`;
    },

    modifyWithTimeString: (date, time) => {
        if (date == null || !time) {
            return date;
        }

        if (!util_date.isDate(time)) return '';
        time = new Date(time);
        return util_date.modifyTime(date, time.getHours(), time.getMinutes(), time.getSeconds());
    },

    valueEquals: function(a, b) {
        const dateEquals = function(a, b) {
            const aIsDate = a instanceof Date;
            const bIsDate = b instanceof Date;
            if (aIsDate && bIsDate) {
                return a.getTime() === b.getTime();
            }
            if (!aIsDate && !bIsDate) {
                return a === b;
            }
            return false;
        };
    
        const aIsArray = a instanceof Array;
        const bIsArray = b instanceof Array;
        if (aIsArray && bIsArray) {
            if (a.length !== b.length) {
            return false;
            }
            return a.every((item, index) => dateEquals(item, b[index]));
        }
        if (!aIsArray && !bIsArray) {
            return dateEquals(a, b);
        }
        return false;
    },

    isString: function(val) {
        return typeof val === 'string' || val instanceof String;
    },

    arrayFindIndex: function(arr, pred) {
        for (let i = 0; i !== arr.length; ++i) {
            if (pred(arr[i])) {
                return i;
            }
        }
        return -1;
    },

    arrayFind: function(arr, pred) {
        const idx = util_date.arrayFindIndex(arr, pred);
        return idx !== -1 ? arr[idx] : undefined;
    },

    coerceTruthyValueToArray: function(val) {
        if (Array.isArray(val)) {
            return val;
        } else if (val) {
            return [val];
        } else {
            return [];
        }
    },
    getDateTimestamp: function(time) {
        if (typeof time === 'number' || typeof time === 'string') {
            return util_date.clearTime(new Date(time)).getTime();
        } else if (time instanceof Date) {
            return util_date.clearTime(time).getTime();
        } else {
            return NaN;
        }
    },
    hasClass (el, cls) {
        if (!el || !cls) return false;

        if (cls.indexOf(' ') !== -1) throw new Error('className should not contain space.');
    
        if (el.classList) {
            return el.classList.contains(cls);
        } else {
            return (' ' + el.className + ' ').indexOf(' ' + cls + ' ') > -1;
        }
    },
}

const validator = function(val) {
    // either: String, Array of String, null / undefined
    return (
        val === null || val === undefined || util_date.isString(val) || 
        (Array.isArray(val) && val.length === 2 && val.every(util_date.isString))
    );
};

const Mixin_uiDate_transition = {
    mounted () {
        window.addEventListener('scroll', this.pageScroll, true);
        window.addEventListener('resize', this.pageScroll);
    },
    methods: {
        enter (el) {
            el.style.transition = '0.19s all ease-in';
            el.style.webkitTransform = 'scale(1, 0)';
            el.style.opacity = 0;
        },
        afterEnter (el) {
            el.style.webkitTransform = 'scale(1, 1)';
            el.style.opacity = 1;
        },

        beforeLeave (el) {
            el.style.webkitTransform = 'scale(1, 1)';
            el.style.opacity = 1;
        },
        leave (el) {
            if (el.scrollHeight !== 0) {
                el.style.transition = '0.19s all ease-in';
                el.style.webkitTransform = 'scale(1, 0)';
                el.style.opacity = 0;
            }
        },
        afterLeave (el) {
            el.style.transition = '';
            el.style.webkitTransform = '';
            el.style.opacity = ''
        },
        
        pageScroll () {
            if (this.$refs.pickers) {
                this.topScroll();
            }
        },
        topScroll () {           
            let client = this.$el.getBoundingClientRect();
            let height = this.$refs.pickers.clientHeight;
            let clientHeight = document.body.clientHeight;
            if ( client.bottom + height + 7 > clientHeight && client.top >= height + 2 ) {
                this.animation = 'appear-up';
                this.$refs.pickers.style.top = `-${height+2}px`;
                this.$refs.pickers.style.boxShadow = 'rgba(0, 0, 0, 0.1) 0px -5px 10px';
            } else {
                this.animation = 'appear-down';
                this.$refs.pickers.style.top = "";
                this.$refs.pickers.style.boxShadow = '';
            }
        }
    }
}

Vue.component('ui-date-picker1', {
    template: `
        <div class="vd-ui-date">
            <div 
                v-if="!ranged"
                class="vd-ui-date-editor"
                :class="'vd-ui-date-editor--' + type"
            >
                <ui-input
                    :placeholder="placeholder"
                    v-bind="$attrs"
                    ref="vdInput"
                    :disabled="disabled"
                    :value="displayValue"
                    @input="value => userInput = value"

                    @focus="handleFocus"
                    @clear="handleClear"
                    @keydown.native="handleKeydown"
                    @change="handleChange"

                    :size="size"
                    width="100%"
                    :readonly="!editable || readonly"
                    autocomplete="off"
                    :clearable="clearValue"
                >
                    <i v-if="type=='time'" slot="prefix" class="vd-ui-input__icon vd-ui_icon icon-time"></i>
                    <i v-else slot="prefix" class="vd-ui-input__icon vd-ui_icon icon-date"></i>
                </ui-input>
            </div>

            <!-- 日期范围选择 -->
            <div
                v-else 
                class="vd-ui-range"
                @mouseenter="rangeMouseenter"
                @mouseleave="rangeMouseleave"
                @click="handleRangeClick"
            >
                <i class="vd-ui_icon icon-date"></i>
                <div class="input-box">
                    <input type="text" 
                        autocomplete="off"
                        :placeholder="startPlaceholder"
                        :value="displayValue && displayValue[0]"
                        :readonly="!editable || readonly"
                    />
                </div>
                <span class="split">至</span>
                <div class="input-box">
                    <input type="text" 
                        autocomplete="off"
                        :placeholder="endPlaceholder"
                        :value="displayValue && displayValue[1]"
                        :readonly="!editable || readonly"
                    />
                </div>
                <div class="range-error2">
                    <i 
                        v-show="haveTrigger"
                        class="vd-ui_icon icon-error2" 
                        @click.stop="handleRangeClear"
                    ></i>
                </div>
            </div>


            <transition
                @enter="enter"
                @after-enter="afterEnter"
                @before-leave="beforeLeave"
                @leave="leave"
                @after-leave="afterLeave"
            >
                <div 
                    class="vd-ui-date-wrapper"
                    :class="[animation ? animation : '', 'vd-ui-date-wrapper--' + type]" 
                    v-show="pickerVisible"
                    ref="pickers"
                    @click.stop
                >
                    <!-- 范围 -->
                    <ui-date-range-panel
                        v-if="type === 'daterange'"
                        :firstDayOfWeek="firstDayOfWeek"
                        :value="baseValue"
                        :unlinkPanels="unlinkPanels"
                        :shortcuts="shortcuts"
                        v-bind="pickerOptions"
                        :showTime="type === 'datetime'"
                        @pick="pickDateRange"
                    ></ui-date-range-panel>

                    <!-- 时间 -->
                    <ui-time-panel
                        v-else-if="type === 'time'"
                        :type="type"
                        :value="baseValue"
                        @pick="pickTime"
                        v-bind="pickerOptions"
                    ></ui-time-panel>

                    <!-- 日期时间 -->
                    <ui-date-time-panel
                        v-else-if="type === 'datetime'"
                        :firstDayOfWeek="firstDayOfWeek"
                        :type="type"
                        :value="baseValue"
                        @pick="pickDateTime"
                        :pickerOptions="pickerOptions"
                        :selectionMode="selectionMode"
                    ></ui-date-time-panel>

                    <ui-date-panel
                        v-else
                        :type="type"
                        :value="baseValue"
                        :firstDayOfWeek="firstDayOfWeek"
                        :shortcuts="shortcuts"
                        @pick="pickDate"
                        :selectionMode="selectionMode"
                        v-bind="pickerOptions"
                    ></ui-date-panel>
                </div>
            </transition>

            <div class="vd-ui-input-error" v-if="errorable && !disabled">
                <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i>
                <p class="vd-ui-input-error--errmsg">{{errorMsg}}</p>
            </div>
        </div>
    `,
    props: {
        value: {},
        readonly: Boolean,   // 是否只读
        disabled: {  // 是否禁用
            type: Boolean,
            default: false
        },
        editable: {  // 是否显示清除按钮
            type: Boolean,
            default: true
        },
        clearable: {
            type: Boolean,
            default: true
        },
        size: String,   // large, small, mini
        placeholder: String,
        startPlaceholder: String,
        endPlaceholder: String,
        type: {   // date, year, month, time, datetime, daterange
            type: String,
            default: 'date'
        },
        appendToBody: {
            type: Boolean,
            default: false,
        },
        format: String,
        popperClass: String,
        pickerOptions: {},
        rangeSeparator: {
            type: String,
            default: '-'
        },
        defaultValue: {}, // 可选，选择器打开时默认显示的时间
        defaultTime: {},  // 范围选择时选中日期所使用的当日内具体时刻
        valueFormat: String,   // 绑定值的格式。不指定则绑定值为 Date 对象
        name: {
            default: '',
            validator
        },
        unlinkPanels: {   // 在范围选择器里取消两个日期面板之间的联动
            type: Boolean,
            default: false
        },
        prefixIcon: String,
        clearIcon: {
            type: String,
            default: 'vd-ui-icon-circle-close'
        },
        validateEvent: {    // 输入时是否触发表单的校验
            type: Boolean,
            default: true
        },
        id: {
            default: '',
            validator
        },
        shortcuts: Array, // 快捷选项
        firstDayOfWeek: {
            type: Number,
            default: 1,
            validator: val => val >= 1 && val <= 7
        },
        errorMsg: {
            type: String,
            default: ''
        },
        errorable: {
            type: Boolean,
            default: false
        },
    },
    mixins: [ Mixin_uiDate_transition ],

    data () {
        return {
            pickerVisible: false,   // panel是否展示
            showClose: false,
            valueOnOpen: null, // 选择器打开时的值，用于确认是否发生变化
            unwatchPickerOptions: null,

            baseValue: null,    // 真实选中的日期 【一定是日期格式】 【可能是数组中多个日期】 同 ele parsedValue
            userInput: null,    // 输入框实时输入值
            animation: '',
            realDate: '',    // 实际上选择的时间（日期，年月，月都放在这里），显示都根据这个来
            rangeFocus: false,  // 范围选择器是否获取焦点
        }
    },
    watch: {
        pickerVisible (val) {
            if (this.readonly || this.pickerDisabled) return;
            if (val) {
                this.$nextTick(()=>{
                    this.pageScroll();   // 下拉展开动画
                })
            }
        },
    },
    computed: {
        // 是否范围选择
        ranged () {
            return this.type.indexOf('range') > -1;
        },

        // 同 ele reference
        vdInput () {
            const vdInput = this.$refs.vdInput;
            if (vdInput) return vdInput.$el || vdInput;
            else return '';
        },

        refInput () {
            if (this.vdInput) {
                return [].slice.call(this.vdInput.querySelectorAll('input'));
            }
            return [];
        },

        // 是否显示清除按钮【需要显示 且 有值】
        clearValue () {
            return this.clearable && this.valueOnOpen && true;
        },

        selectionMode () {
            if (this.type == 'month') {
                return 'month';
            } else if (this.type == 'year') {
                return 'year';
            } else if (this.type === 'dates') {
                return 'dates';
            } else if (this.type === 'week') {
                return 'week';
            }
            return 'day';
        },

        // 显示范围清除按钮
        haveTrigger () {
            // 框子获焦 + 至少有一个值
            let zhi = this.baseValue && this.baseValue[0] && this.baseValue[1];
            return zhi && this.rangeFocus
        },

        // 回显示在input中的值 【需要处理显示格式】
        displayValue () {
            if (this.userInput !== null) {
                return this.userInput;
            }
            if (Array.isArray(this.baseValue)) {
                return [
                    util_date.formatDate(this.baseValue[0]),
                    util_date.formatDate(this.baseValue[1])
                ];
            }
            else if (this.baseValue !== null) {
                return util_date.format(this.baseValue, this.type);
            }
            else {
                return ''
            }
        },

    },
    mounted () {
        this.$form.setValidEl(this);

        // 关闭 当前日期窗口之外的其他日期窗口（全局一次只能打开一个）
        let _this = this;
        document.addEventListener('click', (e) => {
           if (!this.$el.contains(e.target)) _this.pickerVisible = false;
        })

        this.initDate();
    },
    methods: {
        // 初始化日期
        initDate () {
            if (this.value) {
                if (util_date.isDate(this.value)) {
                    this.baseValue = this.value;
                }
                else if (this.type == 'daterange') {
                    let test = util_date.arrayToDate(this.value)
                    this.baseValue = test;
                }
                else if (typeof(this.value) == 'string' && util_date.checkStringCanToDate(this.value, this.type)) {
                    if (this.type == 'time') {
                        this.baseValue = util_date.timeToDate(this.value);
                    } else if (this.type == 'datetime') {
                        this.baseValue = util_date.timeToDatetime(this.value);
                    } else {
                        this.baseValue = util_date.toDate(this.value);
                    }
                }
            } else {
                this.baseValue = null;
            }
        },

        // 展示panel
        showPanel () {
            if (this.readonly || this.pickerDisabled) return;
            this.pickerVisible = true;
            this.valueOnOpen = Array.isArray(this.baseValue) ? [...this.baseValue] : this.baseValue;
        },
        // 关闭panel
        hidePanel () {
            this.pickerVisible = false;
            this.userInput = null;
            this.blur();
        },

        // input获焦
        handleFocus (e) {
            // const type = this.type;
            if (util_date.HAVE_TRIGGER_TYPES.indexOf('date') !== -1 && !this.pickerVisible) {
                this.showPanel();
            }
            this.$emit('focus', e);
        },
        // 主动 失去焦点
        blur() {
            this.refInput.forEach(input => input.blur());
            this.$emit('blur', this);
        },
        // 单选-清除
        handleClear () {
            this.emitInput(null);
        },
        // 范围清除
        handleRangeClear (event) {
            this.baseValue = null;
            this.userInput = null;
        },
        // change
        handleChange () {

            // 如果有值
            if (this.userInput) {

                const value = util_date.checkStringCanToDate(this.userInput, this.type);
                // 验证合法性
                if (value) {
                    if (this.type == 'time') {
                        this.baseValue = util_date.timeToDate(value);   // 年和月是否合法
                    } else {
                        this.baseValue = util_date.toDate(value);   // 年和月是否合法
                    }
                    this.emitInput(this.baseValue);
                    this.emitChange(this.baseValue);
                }
                else {
                    // 年份，月份输入时，重新处理把时间给user-input，不然后被赋一次年月或月份后，不好对比时间
                    this.baseValue = this.valueOnOpen || null;
                }

                this.hidePanel();   // 关闭弹窗
            }

            if (this.userInput === '') {
                this.emitInput(null);
                this.emitChange(null);
                this.baseValue = null;
                this.userInput = null;
            }


        },
        // input 触发按键
        handleKeydown (event) {
            const keyCode = event.keyCode;

            // ESC
            if (keyCode === 27) {
                this.hidePanel();
                event.stopPropagation();
                return;
            }

            // Tab
            if (keyCode === 9) {
                if (!this.ranged) {
                    this.handleChange();
                    this.hidePanel();
                    event.stopPropagation();
                } else {
                    // user may change focus between two input
                    setTimeout(() => {
                        if (this.refInput.indexOf(document.activeElement) === -1) {
                            this.hidePanel();
                            event.stopPropagation();
                        }
                    }, 0);
                }
                return;
            }

            if (keyCode === 13) {
                if (this.userInput) {
                    this.handleChange();
                    this.hidePanel();
                }
                event.stopPropagation();
                return;
            }

            // if user is typing, do not let picker handle key input
            if (this.userInput) {
                event.stopPropagation();
                return;
            }
        },
        // change事件
        emitChange(val) {
            // determine user real change only
            if (!util_date.valueEquals(val, this.valueOnOpen)) {
                // this.$emit('change', val);
                this.$emit('change', util_date.format(val, this.type));
                this.valueOnOpen = val;
                this.checkValid(val);
            }
        },
        // input事件
        emitInput(val) {
            console.log('util_date.format(val, this.type):::', util_date.format(val, this.type));
            this.$emit('input', util_date.format(val, this.type));
            this.checkValid(val);
        },
        // 选中日期  [关闭panel => emit值]
        pickDate (val) {
            this.baseValue = val;
            this.userInput = null;
            this.hidePanel();
            this.emitInput(val);
            this.emitChange(val);
        },
        // 选中时间  [关闭panel => emit值]
        pickTime (val, isFinal = false) {
            this.baseValue = val;
            this.userInput = null;
            if (isFinal) {
                this.hidePanel();
                this.emitInput(val);
            }
        },
        // 选中日期时间
        pickDateTime (val, isFinal = false) {
            this.baseValue = val;
            this.userInput = null;
            if (isFinal) {
                this.hidePanel();
                this.emitInput(val);
            }
        },
        // 选中日期范围  [关闭panel => emit值]
        pickDateRange (val, isFinal = false) {
            this.baseValue = val;
            this.userInput = null;
            if (isFinal) {
                this.hidePanel();
                this.emitInput(val);
                this.emitChange(val);
            }
        },
        handleRangeClick() {
            const type = this.type;

            if (util_date.HAVE_TRIGGER_TYPES.indexOf(type) !== -1 && !this.pickerVisible) {
                this.pickerVisible = true;
            }
            this.$emit('focus', this);
        },
        
        rangeMouseenter () {
            this.rangeFocus = true;
        },
        rangeMouseleave () {
            this.rangeFocus = false;
        },
        checkValid(val) {
            if(this.validKey && this.validValue) {
                if(this.$form.validList[this.validKey].indexOf(this.validValue) !== -1) {
                    let validData = this.$form.checkValid(this.validValue, val, this.validKey);

                    this.triggerError(validData);
                }
            }
        },
        triggerError(validData) {
            if(validData.result) {
                this.errorMsg = '';
                this.errorable = false;
            } else {
                this.errorMsg = validData.message;
                this.errorable = true;
            }
        },
    }
})