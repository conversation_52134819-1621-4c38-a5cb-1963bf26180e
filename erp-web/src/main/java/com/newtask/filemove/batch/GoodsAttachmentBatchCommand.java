package com.newtask.filemove.batch;

import com.google.common.collect.Lists;
import com.newtask.filemove.AbstractFileMoveInterface;
import com.newtask.filemove.dto.CommonConstant;
import com.vedeng.common.constant.CommonConstants;
import com.newtask.filemove.dto.FileMoveDto;
import com.newtask.filemove.theadpool.BrandTask;
import com.newtask.filemove.theadpool.FileMoveThreadPool;
import com.newtask.filemove.theadpool.GoodsAttachmentTask;
import com.vedeng.filemove.dao.FileMoveDao;
import com.vedeng.goods.model.GoodsAttachment;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ExecutorService;

/**
 * 商品附件的批处理命令
 */
@Service
public class GoodsAttachmentBatchCommand extends AbstractFileMoveInterface {

    private static final Logger LOGGER = LoggerFactory.getLogger(GoodsAttachmentBatchCommand.class);

    @Resource
    private FileMoveDao fileMoveDao;

    protected String getCommandType() {
        return CommonConstant.BATCH;
    }

    protected List<FileMoveDto> getFileMoveInfoByPageSize(int start, int pageSize) {

        List<GoodsAttachment> goodsAttachmentList = fileMoveDao.getGoodsAttachmentByPage(start,pageSize);

        if(CollectionUtils.isEmpty(goodsAttachmentList )){
            return null;
        }

        return Lists.transform(goodsAttachmentList , (entity) -> {
                                                FileMoveDto fileMoveDto = new FileMoveDto();
                                                fileMoveDto.setId(entity.getGoodsAttachmentId());
                                                fileMoveDto.setDomain(entity.getDomain());
                                                fileMoveDto.setUri(entity.getUri());
                                                return fileMoveDto;
                                            });
    }

    @Override
    protected Runnable getFileMoveTask(List<FileMoveDto> fileMoveDtoList) {
        return new GoodsAttachmentTask(fileMoveDtoList);
    }

    @Override
    protected ExecutorService getExecutorService() {
        return FileMoveThreadPool.getTraderCertificateThreadPool();
    }
}
