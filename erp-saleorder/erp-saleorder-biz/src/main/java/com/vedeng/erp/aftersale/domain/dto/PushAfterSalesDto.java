package com.vedeng.erp.aftersale.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 推送前台售后单
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PushAfterSalesDto {

    /**
     * 销售订单号
     */
    private String orderNo;

    /**
     * 售后单id
     */
    private Integer afterSalesId;

    /**
     * 售后单号
     */
    private String afterSaleOrderNo;

    /**
     * 售后类型id
     */
    private Integer afterSalesTypeId;

    /**
     * 问题描述
     */
    private String afterSalesComment;

    /**
     * 售后申请原因编码
     */
    private Integer afterSalesReasonId;

    /**
     * 售后申请原因名称
     */
    private String afterSalesReasonName;

    /**
     * 售后订单状态：0待确认（默认）、1进行中、2已完结、3已关闭
     */
    private Integer afterSalesStatus;

    /**
     * 售后订单审核状态：0待确认（默认）、1审核中、2审核通过、3审核不通过
     */
    private Integer status;

    /**
     * 审核不通过原因
     */
    private String verifiesNotPassReason;

    /**
     * 收货联系人
     */
    private String contactUserName;

    /**
     * 收货联系人电话
     */
    private String contactMobile;

    /**
     * 收货地区最小级ID
     */
    private Integer areaId;

    /**
     * 收货详细地址
     */
    private String contactAddress;

    /**
     * 商品信息
     */
    private List<PushAfterSalesGoodsDto> shOrderGoodsList;

    /**
     * 发票ID
     */
    private List<Integer> invoiceList;

    /**
     * 附件
     */
    private List<PushAfterSalesAttachmentDto> attachments;

    /**
     * 收货联系人信息
     */
    private ContactData contactData;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContactData {

        /**
         * 收货联系人
         */
        private String contactUserName;

        /**
         * 收货联系人电话
         */
        private String contactMobile;

        /**
         * 省
         */
        private Integer contactProvince;

        /**
         * 市
         */
        private Integer contactCity;

        /**
         * 区
         */
        private Integer contactArea;

        /**
         * 收货详细地址
         */
        private String contactAddress;
    }

}