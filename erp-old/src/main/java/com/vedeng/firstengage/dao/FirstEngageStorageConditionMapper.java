package com.vedeng.firstengage.dao;

import com.vedeng.firstengage.model.FirstEngageStorageCondition;

import java.util.List;
import java.util.Map;

/**
 * ERP_SV_2020_61 作废
 */
@Deprecated
public interface FirstEngageStorageConditionMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE_STORAGE_CONDITION
     *
     * @mbg.generated Wed Mar 20 18:33:10 CST 2019
     */
    int deleteByPrimaryKey(Integer firstEngageStorageConditionId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE_STORAGE_CONDITION
     *
     * @mbg.generated Wed Mar 20 18:33:10 CST 2019
     */
    int insert(FirstEngageStorageCondition record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE_STORAGE_CONDITION
     *
     * @mbg.generated Wed Mar 20 18:33:10 CST 2019
     */
    int insertSelective(FirstEngageStorageCondition record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE_STORAGE_CONDITION
     *
     * @mbg.generated Wed Mar 20 18:33:10 CST 2019
     */
    FirstEngageStorageCondition selectByPrimaryKey(Integer firstEngageStorageConditionId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE_STORAGE_CONDITION
     *
     * @mbg.generated Wed Mar 20 18:33:10 CST 2019
     */
    int updateByPrimaryKeySelective(FirstEngageStorageCondition record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE_STORAGE_CONDITION
     *
     * @mbg.generated Wed Mar 20 18:33:10 CST 2019
     */
    int updateByPrimaryKey(FirstEngageStorageCondition record);

    /**
     * 存储信息
     * <p>Title: insertSelectiveList</p>  
     * <p>Description: </p>  
     * @param paramMap
     * @return  
     * <AUTHOR>
     * @date 2019年3月23日
     */
	Integer insertSelectiveList(Map<String, Object> paramMap);

	/**
	 * 查询存储条件
	 * <p>Title: selectByParam</p>  
	 * <p>Description: </p>  
	 * @param paramMap
	 * @return  
	 * <AUTHOR>
	 * @date 2019年4月2日
	 */
	List<FirstEngageStorageCondition> selectByParam(Map<String, Object> paramMap);

	/**
	 * @description 删除已经存在存储信息
	 * <AUTHOR>
	 * @param
	 * @date 2019/4/19
	 */
    Integer deleteByPrimaryParam(Map<String, Object> paramMap);
}