package com.vedeng.goods.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.goods.domain.entity.CategoryInspection;
import com.vedeng.goods.domain.entity.InspectionItem;
import com.vedeng.goods.mapper.CategoryInspectionMapper;
import com.vedeng.goods.mapper.InspectionItemMapper;
import com.vedeng.goods.service.InspectionItemApiService;
import com.vedeng.goods.service.InspectionItemService;
import com.vedeng.goods.vo.InspectionItemVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【T_INSPECTION_ITEM(检查项目)】的数据库操作Service实现
 * @createDate 2022-02-10 14:28:51
 */
@Service
public class InspectionItemServiceImpl implements InspectionItemService, InspectionItemApiService {

    @Autowired
    private CategoryInspectionMapper categoryInspectionMapper;
    @Autowired
    private InspectionItemMapper inspectionItemMapper;


    @Override
    public List<InspectionItemVo> getInspectionItemList(Integer categoryId) {
        // 获取三级分类和检查项目中间表
        Set<Integer> categoryInspections = categoryInspectionMapper.findByCategoryId(categoryId)
                .stream().map(CategoryInspection::getInspectionId).collect(Collectors.toSet());
        // 获取所有检查项目
        List<InspectionItem> inspectionItems = inspectionItemMapper.findAllOrderBySort();
        List<InspectionItemVo> inspectionItemVoList = new ArrayList<>();
        inspectionItems.forEach(inspectionItem -> {
            InspectionItemVo dto = new InspectionItemVo();
            dto.setId(inspectionItem.getId());
            dto.setName(inspectionItem.getName());
            dto.setSelected(categoryInspections.contains(inspectionItem.getId()));
            inspectionItemVoList.add(dto);
        });
        return inspectionItemVoList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveCategoryInspection(Integer categoryId, Integer[] inspectionItemIds) {
        // 需要变更的数据
        List<Integer> inspectionItemIdList = ArrayUtil.isEmpty(inspectionItemIds) ? new ArrayList<>() : Arrays.asList(inspectionItemIds);

        // 获取当前三级分类下的所有检查项目关系中间表数据
        List<CategoryInspection> oldCategoryInspections = categoryInspectionMapper.findByCategoryId(categoryId);

        // 需要删除的关系
        List<Integer> deleteCategoryInspectionIds = oldCategoryInspections.stream()
                .filter(categoryInspection -> !inspectionItemIdList.contains(categoryInspection.getInspectionId()))
                .map(CategoryInspection::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(deleteCategoryInspectionIds)) {
            deleteCategoryInspectionIds.forEach(id -> categoryInspectionMapper.deleteByPrimaryKey(id));
        }

        // 需要新增的关系
        List<Integer> oldInspectionItemIds = oldCategoryInspections.stream().map(CategoryInspection::getInspectionId).collect(Collectors.toList());
        List<Integer> addCategoryInspectionIds = inspectionItemIdList.stream()
                .filter(id -> !oldInspectionItemIds.contains(id))
                .collect(Collectors.toList());

        // 新增三级分类下检查项目
        if (CollUtil.isNotEmpty(addCategoryInspectionIds)) {
            addCategoryInspectionIds.forEach(id -> {
                CategoryInspection categoryInspection = new CategoryInspection();
                categoryInspection.setCategoryId(categoryId);
                categoryInspection.setInspectionId(id);
                categoryInspectionMapper.insertSelective(categoryInspection);
            });
        }
    }
}
