package com.vedeng.erp.system.service.impl;

import org.springframework.stereotype.Service;

import org.springframework.beans.factory.annotation.Autowired;

import com.vedeng.erp.system.mapper.FlowNodeMapper;
import com.vedeng.erp.system.domain.entity.FlowNodeEntity;
import com.vedeng.erp.system.service.FlowNodeService;
@Service
public class FlowNodeServiceImpl implements FlowNodeService{

    @Autowired
    private FlowNodeMapper flowNodeMapper;

    @Override
    public int deleteByPrimaryKey(Long flowNodeId) {
        return flowNodeMapper.deleteByPrimaryKey(flowNodeId);
    }

    @Override
    public int insert(FlowNodeEntity record) {
        return flowNodeMapper.insert(record);
    }

    @Override
    public int insertSelective(FlowNodeEntity record) {
        return flowNodeMapper.insertSelective(record);
    }

    @Override
    public FlowNodeEntity selectByPrimaryKey(Long flowNodeId) {
        return flowNodeMapper.selectByPrimaryKey(flowNodeId);
    }

    @Override
    public int updateByPrimaryKeySelective(FlowNodeEntity record) {
        return flowNodeMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(FlowNodeEntity record) {
        return flowNodeMapper.updateByPrimaryKey(record);
    }

}
