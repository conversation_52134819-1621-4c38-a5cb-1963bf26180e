package com.vedeng.goods.command;

import com.beust.jcommander.internal.Lists;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.constant.goods.GoodsCheckStatusEnum;
import com.vedeng.common.controller.BaseCommand;
import com.vedeng.common.model.FileInfo;
import com.vedeng.firstengage.model.FirstEngage;
import com.vedeng.goods.model.vo.*;
import com.vedeng.goods.vo.InspectionItemVo;
import com.vedeng.system.model.SysOptionDefinition;
import org.apache.commons.lang.StringUtils;

import java.util.List;

public class SpuAddCommand extends BaseCommand {

	private List<SysOptionDefinition> spuTypeList;
	private CoreSpuDetailVO coreSpuDetailVO;
	private FirstEngage firstEngage;
	private List<DepartmentsHospitalGenerateVO> departmentsHospitalList;
    private Integer assignmentManagerId;
    private Integer assignmentAssistantId;

	private String assignmentManagerName;
	private String assignmentAssistantName;

	private GoodsStorageConditionVo goodsStorageConditionVo;
	private GoodsLevelVo goodsLevelVo;
	private GoodsPositionVo goodsPositionVo;

	private List<InspectionItemVo> inspectionItemVos;

	public List<InspectionItemVo> getInspectionItemVos() {
		return inspectionItemVos;
	}

	public void setInspectionItemVos(List<InspectionItemVo> inspectionItemVos) {
		this.inspectionItemVos = inspectionItemVos;
	}

	public Integer getAssignmentManagerId() {
		return assignmentManagerId;
	}

	public void setAssignmentManagerId(Integer assignmentManagerId) {
		this.assignmentManagerId = assignmentManagerId;
	}

	public Integer getAssignmentAssistantId() {
		return assignmentAssistantId;
	}

	public void setAssignmentAssistantId(Integer assignmentAssistantId) {
		this.assignmentAssistantId = assignmentAssistantId;
	}

	public String getAssignmentManagerName() {
		return assignmentManagerName;
	}

	public void setAssignmentManagerName(String assignmentManagerName) {
		this.assignmentManagerName = assignmentManagerName;
	}

	public String getAssignmentAssistantName() {
		return assignmentAssistantName;
	}

	public void setAssignmentAssistantName(String assignmentAssistantName) {
		this.assignmentAssistantName = assignmentAssistantName;
	}

	public String getSpuCheckFiles() {
		return spuCheckFiles;
	}

	public void setSpuCheckFiles(String spuCheckFiles) {
		this.spuCheckFiles = spuCheckFiles;
	}

	private String  spuCheckFiles;// 检测

	public String getSpuPatentFiles() {
		return spuPatentFiles;
	}

	public void setSpuPatentFiles(String spuPatentFiles) {
		this.spuPatentFiles = spuPatentFiles;
	}

	private String  spuPatentFiles;// 专利

	public String getHospitalTags() {
		return hospitalTags;
	}

	public void setHospitalTags(String hospitalTags) {
		this.hospitalTags = hospitalTags;
	}

	private String hospitalTags;// 功能
	private String hospitalTagsShow;

	public String getHospitalTagsShow(){
		return StringUtils.replace(hospitalTags,"@_@"," 、");
	}
	private GoodsCheckStatusEnum[] checkStatusEnums = GoodsCheckStatusEnum.values();
	//
	// 页面数据
	//
	private Integer spuId;

	public Integer getTempSpuId() {
		return tempSpuId;
	}

	public void setTempSpuId(Integer tempSpuId) {
		this.tempSpuId = tempSpuId;
	}

	private Integer tempSpuId;

	private Integer categoryId;

	/**
	 * 器械类型，1医疗器械，2非医疗器械
	 *
	 * @since ERP_LV_2020_56
	 */
	@Deprecated
	private Integer apparatusType;

	private Boolean hasRegistrationCert;

	private Integer medicalInstrumentCatalogIncluded;

	private String specsModel;

	private Integer spuLevel;// 0:其他产品,1:核心产品、2:临时产品、
	private Integer brandId;

	public String getBrandName() {
		return brandName;
	}

	public void setBrandName(String brandName) {
		this.brandName = brandName;
	}

	private String brandName;
	/**
	 * 一级产品类型
	 */
	private Integer spuType;

	/**
	 * 二级产品类型（目前只有“大型医疗设备”，其一级类别为“设备”）
	 */
	private Integer secondLevelSpuType;

	private Integer firstEngageId;
	private String spuName;
	private String showName;
	private String wikiHref;


	/**
	 * 禁用状态
	 */
	private Integer status;

	/**
	 * 禁用原因
	 */
	private String disabledReason;



	public String getDisabledReason() {
		return disabledReason;
	}

	public void setDisabledReason(String disabledReason) {
		this.disabledReason = disabledReason;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}



	/**
	 * 商品级别
	 *
	 * @since ERP_LV_2020_105
	 */
	private Integer goodsLevelNo;

	/**
	 * 商品档位
	 *
	 * @since  ERP_LV_2020_105
	 */
	private Integer goodsPositionNo;

	public Integer getOperateInfoId() {
		return operateInfoId;
	}

	public void setOperateInfoId(Integer operateInfoId) {
		this.operateInfoId = operateInfoId;
	}

	private Integer operateInfoId;

	public String getTips() {
		return tips;
	}

	public void setTips(String tips) {
		this.tips = tips;
	}

	private String tips;

	public GoodsCheckStatusEnum[] getCheckStatusEnums() {
		return checkStatusEnums;
	}

	public void setCheckStatusEnums(GoodsCheckStatusEnum[] checkStatusEnums) {
		this.checkStatusEnums = checkStatusEnums;
	}

	public Integer getCheckStatus() {
		return checkStatus;
	}

	public void setCheckStatus(Integer checkStatus) {
		this.checkStatus = checkStatus;
	}

	private Integer checkStatus;
	private String registrationIcon;// 注册商标
	private Integer operateInfoFlag;
	private Integer spuCheckStatus;// spu审核状态


	private Integer[] baseAttributeIds;//已经选择的属性id

	private Integer[] primaryAttributeIds;//已经选择主属性id

	private String lastCheckReason;

	private String showType;// 用于区分显示的字段

	public String getShowType() {
		return showType;
	}

	public void setShowType(String showType) {
		this.showType = showType;
	}

	private List<User> productOwnerUsers;

	private String categoryPath;

	//sku
	private String skuInfo;

	public Integer getSkuType() {
		return skuType;
	}

	public void setSkuType(Integer skuType) {
		this.skuType = skuType;
	}

	private Integer skuType;//1器械  2 耗材


	public String getSkuInfo() {
		return skuInfo;
	}

	public void setSkuInfo(String skuInfo) {
		this.skuInfo = skuInfo;
	}

	public Integer[] getBaseAttributeIds() {
		return baseAttributeIds;
	}

	public void setBaseAttributeIds(Integer[] baseAttributeIds) {
		this.baseAttributeIds = baseAttributeIds;
	}
	private List<BaseAttributeVo> baseAttributes = Lists.newArrayList();
	private List<BaseAttributeVo> primaryAttributes = Lists.newArrayList();

	public Integer getOperateInfoFlag() {
		return operateInfoFlag;
	}

	public void setOperateInfoFlag(Integer operateInfoFlag) {
		this.operateInfoFlag = operateInfoFlag;
	}

	public List<SysOptionDefinition> getSpuTypeList() {
		return spuTypeList;
	}

	public void setSpuTypeList(List<SysOptionDefinition> spuTypeList) {
		this.spuTypeList = spuTypeList;
/**/	}

	public FirstEngage getFirstEngage() {
		return firstEngage;
	}

	public void setFirstEngage(FirstEngage firstEngage) {
		this.firstEngage = firstEngage;
	}

	public List<DepartmentsHospitalGenerateVO> getDepartmentsHospitalList() {
		return departmentsHospitalList;
	}

	public void setDepartmentsHospitalList(List<DepartmentsHospitalGenerateVO> departmentsHospitalList) {
		this.departmentsHospitalList = departmentsHospitalList;
	}




	public Integer getSpuId() {
		return spuId;
	}

	public void setSpuId(Integer spuId) {
		this.spuId = spuId;
	}

	public Integer getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Integer categoryId) {
		this.categoryId = categoryId;
	}

	public Integer getSpuLevel() {
		return spuLevel;
	}

	public void setSpuLevel(Integer spuLevel) {
		this.spuLevel = spuLevel;
	}

	public Integer getBrandId() {
		return brandId;
	}

	public void setBrandId(Integer brandId) {
		this.brandId = brandId;
	}

	public Integer getSpuType() {
		return spuType;
	}

	public void setSpuType(Integer spuType) {
		this.spuType = spuType;
	}

	public Integer getSecondLevelSpuType() {
		return secondLevelSpuType;
	}

	public void setSecondLevelSpuType(Integer secondLevelSpuType) {
		this.secondLevelSpuType = secondLevelSpuType;
	}

	public Integer getFirstEngageId() {
		return firstEngageId;
	}

	public void setFirstEngageId(Integer firstEngageId) {
		this.firstEngageId = firstEngageId;
	}

	public String getSpuName() {
		return spuName;
	}

	public void setSpuName(String spuName) {
		this.spuName = spuName;
	}

	public String getShowName() {
		return showName;
	}

	public void setShowName(String showName) {
		this.showName = showName;
	}

	public String getWikiHref() {
		return wikiHref;
	}

	public void setWikiHref(String wikiHref) {
		this.wikiHref = wikiHref;
	}


	public String getRegistrationIcon() {
		return registrationIcon;
	}

	public void setRegistrationIcon(String registrationIcon) {
		this.registrationIcon = registrationIcon;
	}

	public Integer getSpuCheckStatus() {
		return spuCheckStatus;
	}

	public void setSpuCheckStatus(Integer spuCheckStatus) {
		this.spuCheckStatus = spuCheckStatus;
	}

	public List<BaseAttributeVo> getBaseAttributes() {
		return baseAttributes;
	}

	public void setBaseAttributes(List<BaseAttributeVo> baseAttributes) {
		this.baseAttributes = baseAttributes;
	}

	public CoreSpuDetailVO getCoreSpuDetailVO() {
		return coreSpuDetailVO;
	}

	public void setCoreSpuDetailVO(CoreSpuDetailVO coreSpuDetailVO) {
		this.coreSpuDetailVO = coreSpuDetailVO;
	}

	public String getLastCheckReason() {
		return lastCheckReason;
	}

	public void setLastCheckReason(String lastCheckReason) {
		this.lastCheckReason = lastCheckReason;
	}

	public Integer getApparatusType() {
		return apparatusType;
	}

	public void setApparatusType(Integer apparatusType) {
		this.apparatusType = apparatusType;
	}

	public GoodsStorageConditionVo getGoodsStorageConditionVo() {
		return goodsStorageConditionVo;
	}

	public void setGoodsStorageConditionVo(GoodsStorageConditionVo goodsStorageConditionVo) {
		this.goodsStorageConditionVo = goodsStorageConditionVo;
	}

	public Boolean getHasRegistrationCert() {
		return hasRegistrationCert;
	}

	public void setHasRegistrationCert(Boolean hasRegistrationCert) {
		this.hasRegistrationCert = hasRegistrationCert;
	}

	public Integer getMedicalInstrumentCatalogIncluded() {
		return medicalInstrumentCatalogIncluded;
	}

	public void setMedicalInstrumentCatalogIncluded(Integer medicalInstrumentCatalogIncluded) {
		this.medicalInstrumentCatalogIncluded = medicalInstrumentCatalogIncluded;
	}

	public String getSpecsModel() {
		return specsModel;
	}

	public void setSpecsModel(String specsModel) {
		this.specsModel = specsModel;
	}

	public List<User> getProductOwnerUsers() {
		return productOwnerUsers;
	}

	public void setProductOwnerUsers(List<User> productOwnerUsers) {
		this.productOwnerUsers = productOwnerUsers;
	}

	public Integer getGoodsLevelNo() {
		return goodsLevelNo;
	}

	public void setGoodsLevelNo(Integer goodsLevelNo) {
		this.goodsLevelNo = goodsLevelNo;
	}

	public Integer getGoodsPositionNo() {
		return goodsPositionNo;
	}

	public void setGoodsPositionNo(Integer goodsPositionNo) {
		this.goodsPositionNo = goodsPositionNo;
	}

	public String getCategoryPath() {
		return categoryPath;
	}

	public void setCategoryPath(String categoryPath) {
		this.categoryPath = categoryPath;
	}

	public GoodsLevelVo getGoodsLevelVo() {
		return goodsLevelVo;
	}

	public void setGoodsLevelVo(GoodsLevelVo goodsLevelVo) {
		this.goodsLevelVo = goodsLevelVo;
	}

	public GoodsPositionVo getGoodsPositionVo() {
		return goodsPositionVo;
	}

	public void setGoodsPositionVo(GoodsPositionVo goodsPositionVo) {
		this.goodsPositionVo = goodsPositionVo;
	}

	public Integer[] getPrimaryAttributeIds() {
		return primaryAttributeIds;
	}

	public void setPrimaryAttributeIds(Integer[] primaryAttributeIds) {
		this.primaryAttributeIds = primaryAttributeIds;
	}

	public List<BaseAttributeVo> getPrimaryAttributes() {
		return primaryAttributes;
	}

	public void setPrimaryAttributes(List<BaseAttributeVo> primaryAttributes) {
		this.primaryAttributes = primaryAttributes;
	}

	/**
	 * 非医疗器械一级分类
	 */
	private Integer noMedicalFirstType;

	/**
	 * 非医疗器械二级分类
	 */
	private Integer noMedicalSecondType;

	public Integer getNoMedicalSecondType() {
		return noMedicalSecondType;
	}

	public void setNoMedicalSecondType(Integer noMedicalSecondType) {
		this.noMedicalSecondType = noMedicalSecondType;
	}

	public Integer getNoMedicalFirstType() {
		return noMedicalFirstType;
	}

	public void setNoMedicalFirstType(Integer noMedicalFirstType) {
		this.noMedicalFirstType = noMedicalFirstType;
	}

	/**
	 * 税收分类编码
	 */
	private String taxClassificationCode;

	/**
	 * 税收编码简称
	 */
	private String taxCodeSimpleName;

	public String getTaxCodeSimpleName() {
		return taxCodeSimpleName;
	}

	public void setTaxCodeSimpleName(String taxCodeSimpleName) {
		this.taxCodeSimpleName = taxCodeSimpleName;
	}

	public String getTaxClassificationCode() {
		return taxClassificationCode;
	}

	public void setTaxClassificationCode(String taxClassificationCode) {
		this.taxClassificationCode = taxClassificationCode;
	}
}
