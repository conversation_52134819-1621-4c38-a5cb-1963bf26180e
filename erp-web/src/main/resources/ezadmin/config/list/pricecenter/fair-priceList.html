<html>
<head>
    <meta charset="UTF-8">
    <title>公允销售价</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="fair-priceList" datasource="erp-datasourcetarget" append_column_url="" append_row_url="" empty_show="-" firstcol="numbers">
<div id="appendHead"></div>
<div class="layui-tab">
    <ul class="layui-tab-title" id="tab">
    </ul>
</div>
<form class="layui-form" id="search">
    <div class=" layui-inline ">
        <label class="layui-form-label">商品名称</label>
        <div class="layui-input-inline">
            <input class="layui-input list-search-item" jdbctype="VARCHAR" placeholder="请输入" oper="like" name="SKU_NAME" empty_show="-" style="" alias="">
        </div>
    </div>
    <div class=" layui-inline ">
        <label class="layui-form-label">订货号</label>
        <div class="layui-input-inline">
            <input class="layui-input list-search-item" jdbctype="VARCHAR" placeholder="请输入" oper="" name="SKU" empty_show="-" style="" alias="">
        </div>
    </div>
</form>
<hr class="layui-border-blue">
<table id="table" class="layui-table" style=" width:100%">
    <thead>
    <tr id="column">
        <th item_name="SKU_NAME" name="SKU_NAME" body="td-text" opentype="PARENT" datatype="" data="" style="min-width:200px;word-break: break-all;position: sticky;" head="18">商品名称</th>
        <th item_name="SKU" name="SKU" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">订货号</th>
        <th item_name="PRICE" name="PRICE" body="td-text"  url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align: right" jdbctype="NUMBER" >公允价</th>
        <th item_name="MOD_TIME" name="MOD_TIME" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">更新时间</th>
        <th style="width: 200px" type="rowbutton" id="rowbutton">
            <button class="layui-btn list-row-button" opentype="MODEL" type="single"
                    url="/goods/finance/viewHistoryFairPrice.do?fairValueId=${FAIR_VALUE_ID}" windowname="${SKU_NAME}" area="600px,500px"
                    name="history">历史公允价
            </button>
            <button class="layui-btn list-row-button" opentype="MODEL" type="group"
                    url="/goods/finance/viewFairPriceSales.do?fairValueId=${FAIR_VALUE_ID}" windowname="${SKU_NAME}" area="600px,500px"
                    name="sales">计算订单
            </button>
        </th>
    </tr>
    </thead>
    <tbody>
        <tr>
            <td colspan=100>
                express:
                <pre id="express" class="layui-code">
                    SELECT A.FAIR_VALUE_ID,A.SKU,A.GOODS_ID,A.SKU_NAME,A.PRICE,DATE_FORMAT(A.MOD_TIME,'%Y-%m-%d %H:%i:%s') AS MOD_TIME
                    FROM T_FAIR_VALUE A
                    WHERE 1=1
                </pre>
                <pre id="count" class="layui-code"></pre>
            </td>
        </tr>
    </tbody>
</table>
<div id="appendFoot"></div>
</body>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
</html>
