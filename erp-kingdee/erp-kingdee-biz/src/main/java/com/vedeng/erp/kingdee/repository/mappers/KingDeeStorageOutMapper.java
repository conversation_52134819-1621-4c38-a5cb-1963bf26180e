package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeeStorageOutEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface KingDeeStorageOutMapper {
    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(KingDeeStorageOutEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(KingDeeStorageOutEntity record);

    int batchInsert(@Param("list") List<KingDeeStorageOutEntity> list);
}