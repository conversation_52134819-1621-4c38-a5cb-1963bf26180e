package com.vedeng.goods.model;

import java.io.Serializable;

/**
 * V_CORE_SKU_DISABLED_STATUS_HISTORY
 * <AUTHOR>
 */
public class CoreGoodsDisabledStatusHistory implements Serializable {

    /**
     * sku禁用状态历史表ID
     */
    private Integer id;

    /**
     * skuNo或spuNo
     */
    private String goodsNo;


    /**
     * 商品类型 1：SPU，2:SKU
     */
    private Integer goodsType;

    /**
     * 操作状态 0：禁用，1:启用
     */
    private Integer disabledStatus;

    /**
     * 操作原因
     */
    private String reason;

    /**
     * 操作人
     */
    private Integer creator;

    /**
     * 操作时间
     */
    private Long addTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(Integer goodsType) {
        this.goodsType = goodsType;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public Integer getDisabledStatus() {
        return disabledStatus;
    }

    public void setDisabledStatus(Integer disabledStatus) {
        this.disabledStatus = disabledStatus;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }
}