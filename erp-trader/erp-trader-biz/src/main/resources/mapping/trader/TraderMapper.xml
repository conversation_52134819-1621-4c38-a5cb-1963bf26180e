<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.TraderMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.TraderEntity">
    <!--@mbg.generated-->
    <!--@Table T_TRADER-->
    <id column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="PARENT_ID" jdbcType="INTEGER" property="parentId" />
    <result column="IS_ENABLE" jdbcType="INTEGER" property="isEnable" />
    <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName" />
    <result column="AREA_ID" jdbcType="INTEGER" property="areaId" />
    <result column="AREA_IDS" jdbcType="VARCHAR" property="areaIds" />
    <result column="ADDRESS" jdbcType="VARCHAR" property="address" />
    <result column="THREE_IN_ONE" jdbcType="INTEGER" property="threeInOne" />
    <result column="MEDICAL_QUALIFICATION" jdbcType="INTEGER" property="medicalQualification" />
    <result column="SOURCE" jdbcType="INTEGER" property="source" />
    <result column="ACCOUNT_COMPANY_ID" jdbcType="INTEGER" property="accountCompanyId" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="TRADER_STATUS" jdbcType="INTEGER" property="traderStatus" />
    <result column="BELONG_PLATFORM" jdbcType="INTEGER" property="belongPlatform" />
    <result column="LAST_COMMUNICATE_TIME" jdbcType="BIGINT" property="lastCommunicateTime" />
    <result column="PAYOF_STATUS" jdbcType="INTEGER" property="payofStatus" />
    <result column="PAYOF_CHECK_MSG" jdbcType="VARCHAR" property="payofCheckMsg" />
    <result column="TRADER_CHECK_MSG" jdbcType="VARCHAR" property="traderCheckMsg" />
    <result column="WAREHOUSE_AREA_ID" jdbcType="INTEGER" property="warehouseAreaId" />
    <result column="WAREHOUSE_AREA_IDS" jdbcType="VARCHAR" property="warehouseAreaIds" />
    <result column="WAREHOUSE_DETAIL_ADDRESS" jdbcType="VARCHAR" property="warehouseDetailAddress" />
    <result column="LAST_VALID_TIME" jdbcType="TIMESTAMP" property="lastValidTime" />
    <result column="TRADER_TYPE" jdbcType="INTEGER" property="traderType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    TRADER_ID, COMPANY_ID, PARENT_ID, IS_ENABLE, TRADER_NAME, AREA_ID, AREA_IDS, ADDRESS, 
    THREE_IN_ONE, MEDICAL_QUALIFICATION, `SOURCE`, ACCOUNT_COMPANY_ID, ADD_TIME, CREATOR, 
    MOD_TIME, UPDATER, TRADER_STATUS, BELONG_PLATFORM, LAST_COMMUNICATE_TIME, PAYOF_STATUS, 
    PAYOF_CHECK_MSG, TRADER_CHECK_MSG, WAREHOUSE_AREA_ID, WAREHOUSE_AREA_IDS, WAREHOUSE_DETAIL_ADDRESS, 
    LAST_VALID_TIME, TRADER_TYPE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_TRADER
    where TRADER_ID = #{traderId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_TRADER
    where TRADER_ID = #{traderId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="TRADER_ID" keyProperty="traderId" parameterType="com.vedeng.erp.trader.domain.entity.TraderEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER (COMPANY_ID, PARENT_ID, IS_ENABLE, 
      TRADER_NAME, AREA_ID, AREA_IDS, 
      ADDRESS, THREE_IN_ONE, MEDICAL_QUALIFICATION, 
      `SOURCE`, ACCOUNT_COMPANY_ID, ADD_TIME, 
      CREATOR, MOD_TIME, UPDATER, 
      TRADER_STATUS, BELONG_PLATFORM, LAST_COMMUNICATE_TIME, 
      PAYOF_STATUS, PAYOF_CHECK_MSG, TRADER_CHECK_MSG, 
      WAREHOUSE_AREA_ID, WAREHOUSE_AREA_IDS, WAREHOUSE_DETAIL_ADDRESS, 
      LAST_VALID_TIME, TRADER_TYPE)
    values (#{companyId,jdbcType=INTEGER}, #{parentId,jdbcType=INTEGER}, #{isEnable,jdbcType=INTEGER}, 
      #{traderName,jdbcType=VARCHAR}, #{areaId,jdbcType=INTEGER}, #{areaIds,jdbcType=VARCHAR}, 
      #{address,jdbcType=VARCHAR}, #{threeInOne,jdbcType=INTEGER}, #{medicalQualification,jdbcType=INTEGER}, 
      #{source,jdbcType=INTEGER}, #{accountCompanyId,jdbcType=INTEGER}, #{addTime,jdbcType=BIGINT}, 
      #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}, 
      #{traderStatus,jdbcType=INTEGER}, #{belongPlatform,jdbcType=INTEGER}, #{lastCommunicateTime,jdbcType=BIGINT}, 
      #{payofStatus,jdbcType=INTEGER}, #{payofCheckMsg,jdbcType=VARCHAR}, #{traderCheckMsg,jdbcType=VARCHAR}, 
      #{warehouseAreaId,jdbcType=INTEGER}, #{warehouseAreaIds,jdbcType=VARCHAR}, #{warehouseDetailAddress,jdbcType=VARCHAR}, 
      #{lastValidTime,jdbcType=TIMESTAMP}, #{traderType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="TRADER_ID" keyProperty="traderId" parameterType="com.vedeng.erp.trader.domain.entity.TraderEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="parentId != null">
        PARENT_ID,
      </if>
      <if test="isEnable != null">
        IS_ENABLE,
      </if>
      <if test="traderName != null">
        TRADER_NAME,
      </if>
      <if test="areaId != null">
        AREA_ID,
      </if>
      <if test="areaIds != null">
        AREA_IDS,
      </if>
      <if test="address != null">
        ADDRESS,
      </if>
      <if test="threeInOne != null">
        THREE_IN_ONE,
      </if>
      <if test="medicalQualification != null">
        MEDICAL_QUALIFICATION,
      </if>
      <if test="source != null">
        `SOURCE`,
      </if>
      <if test="accountCompanyId != null">
        ACCOUNT_COMPANY_ID,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="traderStatus != null">
        TRADER_STATUS,
      </if>
      <if test="belongPlatform != null">
        BELONG_PLATFORM,
      </if>
      <if test="lastCommunicateTime != null">
        LAST_COMMUNICATE_TIME,
      </if>
      <if test="payofStatus != null">
        PAYOF_STATUS,
      </if>
      <if test="payofCheckMsg != null">
        PAYOF_CHECK_MSG,
      </if>
      <if test="traderCheckMsg != null">
        TRADER_CHECK_MSG,
      </if>
      <if test="warehouseAreaId != null">
        WAREHOUSE_AREA_ID,
      </if>
      <if test="warehouseAreaIds != null">
        WAREHOUSE_AREA_IDS,
      </if>
      <if test="warehouseDetailAddress != null">
        WAREHOUSE_DETAIL_ADDRESS,
      </if>
      <if test="lastValidTime != null">
        LAST_VALID_TIME,
      </if>
      <if test="traderType != null">
        TRADER_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=INTEGER},
      </if>
      <if test="isEnable != null">
        #{isEnable,jdbcType=INTEGER},
      </if>
      <if test="traderName != null">
        #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null">
        #{areaId,jdbcType=INTEGER},
      </if>
      <if test="areaIds != null">
        #{areaIds,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="threeInOne != null">
        #{threeInOne,jdbcType=INTEGER},
      </if>
      <if test="medicalQualification != null">
        #{medicalQualification,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="accountCompanyId != null">
        #{accountCompanyId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="traderStatus != null">
        #{traderStatus,jdbcType=INTEGER},
      </if>
      <if test="belongPlatform != null">
        #{belongPlatform,jdbcType=INTEGER},
      </if>
      <if test="lastCommunicateTime != null">
        #{lastCommunicateTime,jdbcType=BIGINT},
      </if>
      <if test="payofStatus != null">
        #{payofStatus,jdbcType=INTEGER},
      </if>
      <if test="payofCheckMsg != null">
        #{payofCheckMsg,jdbcType=VARCHAR},
      </if>
      <if test="traderCheckMsg != null">
        #{traderCheckMsg,jdbcType=VARCHAR},
      </if>
      <if test="warehouseAreaId != null">
        #{warehouseAreaId,jdbcType=INTEGER},
      </if>
      <if test="warehouseAreaIds != null">
        #{warehouseAreaIds,jdbcType=VARCHAR},
      </if>
      <if test="warehouseDetailAddress != null">
        #{warehouseDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="lastValidTime != null">
        #{lastValidTime,jdbcType=TIMESTAMP},
      </if>
      <if test="traderType != null">
        #{traderType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.TraderEntity">
    <!--@mbg.generated-->
    update T_TRADER
    <set>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="parentId != null">
        PARENT_ID = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="isEnable != null">
        IS_ENABLE = #{isEnable,jdbcType=INTEGER},
      </if>
      <if test="traderName != null">
        TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null">
        AREA_ID = #{areaId,jdbcType=INTEGER},
      </if>
      <if test="areaIds != null">
        AREA_IDS = #{areaIds,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        ADDRESS = #{address,jdbcType=VARCHAR},
      </if>
      <if test="threeInOne != null">
        THREE_IN_ONE = #{threeInOne,jdbcType=INTEGER},
      </if>
      <if test="medicalQualification != null">
        MEDICAL_QUALIFICATION = #{medicalQualification,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        `SOURCE` = #{source,jdbcType=INTEGER},
      </if>
      <if test="accountCompanyId != null">
        ACCOUNT_COMPANY_ID = #{accountCompanyId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="traderStatus != null">
        TRADER_STATUS = #{traderStatus,jdbcType=INTEGER},
      </if>
      <if test="belongPlatform != null">
        BELONG_PLATFORM = #{belongPlatform,jdbcType=INTEGER},
      </if>
      <if test="lastCommunicateTime != null">
        LAST_COMMUNICATE_TIME = #{lastCommunicateTime,jdbcType=BIGINT},
      </if>
      <if test="payofStatus != null">
        PAYOF_STATUS = #{payofStatus,jdbcType=INTEGER},
      </if>
      <if test="payofCheckMsg != null">
        PAYOF_CHECK_MSG = #{payofCheckMsg,jdbcType=VARCHAR},
      </if>
      <if test="traderCheckMsg != null">
        TRADER_CHECK_MSG = #{traderCheckMsg,jdbcType=VARCHAR},
      </if>
      <if test="warehouseAreaId != null">
        WAREHOUSE_AREA_ID = #{warehouseAreaId,jdbcType=INTEGER},
      </if>
      <if test="warehouseAreaIds != null">
        WAREHOUSE_AREA_IDS = #{warehouseAreaIds,jdbcType=VARCHAR},
      </if>
      <if test="warehouseDetailAddress != null">
        WAREHOUSE_DETAIL_ADDRESS = #{warehouseDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="lastValidTime != null">
        LAST_VALID_TIME = #{lastValidTime,jdbcType=TIMESTAMP},
      </if>
      <if test="traderType != null">
        TRADER_TYPE = #{traderType,jdbcType=INTEGER},
      </if>
    </set>
    where TRADER_ID = #{traderId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.TraderEntity">
    <!--@mbg.generated-->
    update T_TRADER
    set COMPANY_ID = #{companyId,jdbcType=INTEGER},
      PARENT_ID = #{parentId,jdbcType=INTEGER},
      IS_ENABLE = #{isEnable,jdbcType=INTEGER},
      TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      AREA_ID = #{areaId,jdbcType=INTEGER},
      AREA_IDS = #{areaIds,jdbcType=VARCHAR},
      ADDRESS = #{address,jdbcType=VARCHAR},
      THREE_IN_ONE = #{threeInOne,jdbcType=INTEGER},
      MEDICAL_QUALIFICATION = #{medicalQualification,jdbcType=INTEGER},
      `SOURCE` = #{source,jdbcType=INTEGER},
      ACCOUNT_COMPANY_ID = #{accountCompanyId,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      TRADER_STATUS = #{traderStatus,jdbcType=INTEGER},
      BELONG_PLATFORM = #{belongPlatform,jdbcType=INTEGER},
      LAST_COMMUNICATE_TIME = #{lastCommunicateTime,jdbcType=BIGINT},
      PAYOF_STATUS = #{payofStatus,jdbcType=INTEGER},
      PAYOF_CHECK_MSG = #{payofCheckMsg,jdbcType=VARCHAR},
      TRADER_CHECK_MSG = #{traderCheckMsg,jdbcType=VARCHAR},
      WAREHOUSE_AREA_ID = #{warehouseAreaId,jdbcType=INTEGER},
      WAREHOUSE_AREA_IDS = #{warehouseAreaIds,jdbcType=VARCHAR},
      WAREHOUSE_DETAIL_ADDRESS = #{warehouseDetailAddress,jdbcType=VARCHAR},
      LAST_VALID_TIME = #{lastValidTime,jdbcType=TIMESTAMP},
      TRADER_TYPE = #{traderType,jdbcType=INTEGER}
    where TRADER_ID = #{traderId,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2023-08-11-->
  <select id="selectByTraderNameAndCompanyId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_TRADER
    where TRADER_NAME=#{traderName,jdbcType=VARCHAR} and COMPANY_ID=#{companyId,jdbcType=INTEGER}
  </select>
  <select id="getTraderBelongOrgIdList" resultType="java.lang.Integer">
    SELECT
      tp.ORG_ID
    FROM
      T_R_TRADER_J_USER trtju
        LEFT JOIN T_R_USER_POSIT trup ON
        trtju.USER_ID = trup.USER_ID
        LEFT JOIN T_POSITION tp ON
        trup.POSITION_ID = tp.POSITION_ID
    WHERE
      trtju.TRADER_ID = #{traderId,jdbcType=INTEGER}
  </select>


  <select id="getTraderList" resultType="com.vedeng.erp.trader.dto.TraderDto">
    SELECT TRADER_ID,TRADER_NAME FROM T_TRADER WHERE TRADER_ID IN
    <foreach item="traderId" index="index" collection="traderIdList" open="(" separator="," close=")">
      #{traderId}
    </foreach>
  </select>


  <select id="getTraderLeftJoinWebAccount" resultType="java.lang.String">
    SELECT
    distinct A.MOBILE
    FROM
    T_WEB_ACCOUNT A
    WHERE
    A.TRADER_ID =#{traderId}
    </select>
    <select id="getTraderUserByTraderId" resultType="com.vedeng.erp.trader.dto.TraderUserDto">
      SELECT
        trtju.*,
        tu.USERNAME AS userName,
        tt.TRADER_NAME
      FROM
        T_R_TRADER_J_USER trtju
          LEFT JOIN T_USER tu ON trtju.USER_ID = tu.USER_ID
          LEFT JOIN T_TRADER tt ON trtju.TRADER_ID = tt.TRADER_ID
      WHERE
        trtju.TRADER_ID = #{traderId,jdbcType=INTEGER}
        AND trtju.TRADER_TYPE = 1
      LIMIT 1
    </select>


  <select id="getBizTrader" resultType="com.vedeng.erp.trader.dto.BizTraderApiDto">
    select
    <choose>
      <when test="bizType == 1 or bizType == 3">
        TBC.TRADER_ID,
        TBC.TRADER_NAME,
        TTC.TRADER_CUSTOMER_ID,
        TBC.BUSSINESS_CHANCE_ID bizId,
        TTC.CUSTOMER_NATURE customerNature,
        1 bizType
        from T_BUSSINESS_CHANCE TBC
        left join T_TRADER_CUSTOMER TTC on TBC.TRADER_ID = TTC.TRADER_ID
        WHERE TBC.BUSSINESS_CHANCE_ID IN
      </when>
      <when test="bizType == 2">
        TBL.TRADER_ID,
        TBL.TRADER_NAME,
        TTC.TRADER_CUSTOMER_ID,
        TBL.ID bizId,
        TTC.CUSTOMER_NATURE customerNature,
        2 bizType
        from T_BUSINESS_LEADS TBL
        left join T_TRADER_CUSTOMER TTC on TBL.TRADER_ID = TTC.TRADER_ID
        WHERE TBL.ID IN
      </when>
      <when test="bizType == 7">
        TBL.TRADER_ID,
        TBL.CUSTOMER_NAME TRADER_NAME,
        TTC.TRADER_CUSTOMER_ID,
        TBL.ID bizId,
        TTC.CUSTOMER_NATURE customerNature,
        7 bizType
        from T_VISIT_RECORD TBL
        left join T_TRADER_CUSTOMER TTC on TBL.TRADER_ID = TTC.TRADER_ID
        WHERE TBL.ID IN
      </when>
<!--      <when test="bizType == 3">-->
<!--        TQ.TRADER_ID,-->
<!--        TQ.TRADER_NAME,-->
<!--        TTC.TRADER_CUSTOMER_ID,-->
<!--        TQ.QUOTEORDER_ID bizId,-->
<!--        TTC.CUSTOMER_NATURE customerNature,-->
<!--        3 bizType-->
<!--        from T_QUOTEORDER TQ-->
<!--        left join T_TRADER_CUSTOMER TTC on TQ.TRADER_ID = TTC.TRADER_ID-->
<!--        WHERE TQ.QUOTEORDER_ID IN-->
<!--      </when>-->
      <otherwise>
        <!-- 默认情况返回空结果 -->
        TRADER_ID,
        TRADER_NAME,
        TRADER_CUSTOMER_ID,
        null bizId,
        null bizType
        from DUAL
        WHERE 1 = 0
      </otherwise>
    </choose>
    <foreach close=")" collection="bizIds" index="index" item="item" open="(" separator=",">
      #{item,jdbcType=INTEGER}
    </foreach>
  </select>
</mapper>