package com.vedeng.crm.visitrecord.domain.dto;

import lombok.Data;

/**
 * @Description 编辑拜访记录的实体
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/3/1
 */
@Data
public class VisitEditInputDto {

    /**
     * 拜访计划的ID
     */
    private Integer id;

    /** 拜访记录添加的-联系人信息-姓名 */
    private String recordContactName;

    /** 拜访记录添加的-联系人信息-手机 */
    private String recordContactMobile;

    /** 拜访记录添加的-联系人信息-电话 */
    private String recordContactTele;

    /** 拜访记录添加的-联系人信息-职位 */
    private String recordContactPosition;

    /** 记录添加的其它联系方式 */
    private String recordOtherContact;

    /** 记录添加的无联系人 */
    private String recordNoContract;


    /** 沟通事项-讲解PPT (默认否，Y是N否) */
    private String showPpt;

    /** 需求和反馈 */
    private String customerRequires;






}
