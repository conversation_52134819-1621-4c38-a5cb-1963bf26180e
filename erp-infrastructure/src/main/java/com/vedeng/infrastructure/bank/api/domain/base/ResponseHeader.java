package com.vedeng.infrastructure.bank.api.domain.base;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: Patric.Cheng
 * @CreateTime: 2024-07-30
 * @Description: 银行响应请求头
 * @Version: 1.0
 */
@Data
public class ResponseHeader {
    /**
     * 状态
     */
    private Status status;
    /**
     * 服务端日期时间，YYYY-MM-DD HH:MM:SS
     */
    private String dtServer;
    /**
     * 默认值N
     */
    private String userKey;
    /**
     * userkey的有效时间(服务器时间)
     */
    private String dtDead;
    /**
     * 服务器响应信息使用的语言，目前仅提供chs(中文简体)，可选
     */
    private String language;

    /**
     * 常见状态码code：
     * 0：成功；
     * WEC32：当是查询交易表示查询条件错误，一般指起始
     * 记录数<=0;
     * WEC02：转账的时候出现网络异常，具体转账成败未
     * 知；
     * 其他：失败代码，表示交易失败。
     * E1602：单笔对账交易，若在报文头code字段返回
     * E1602，则表示该笔交易银行未受理，可以视为交易失
     * 败
     * 描述信息：
     * ok，交易成功。
     * 其他：交易描述
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Status {
        private String code;
        private String severity;
        private String message;
    }

}
