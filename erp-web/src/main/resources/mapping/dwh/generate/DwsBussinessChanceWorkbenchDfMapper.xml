<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.dwh.externaldb.dao.generate.DwsBussinessChanceWorkbenchDfMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.dwh.model.generate.DwsBussinessChanceWorkbenchDfDo">
    <id column="BUSSINESS_CHANCE_ID" jdbcType="INTEGER" property="bussinessChanceId" />
    <result column="BUSSINESS_CHANCE_NO" jdbcType="VARCHAR" property="bussinessChanceNo" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="USER_ID" jdbcType="INTEGER" property="userId" />
    <result column="ORG_ID" jdbcType="INTEGER" property="orgId" />
    <result column="USER_NAME" jdbcType="VARCHAR" property="userName" />
    <result column="L1_ID" jdbcType="INTEGER" property="l1Id" />
    <result column="L1_NAME" jdbcType="VARCHAR" property="l1Name" />
    <result column="L2_ID" jdbcType="INTEGER" property="l2Id" />
    <result column="L2_NAME" jdbcType="VARCHAR" property="l2Name" />
    <result column="L3_ID" jdbcType="INTEGER" property="l3Id" />
    <result column="L3_NAME" jdbcType="VARCHAR" property="l3Name" />
    <result column="POSITION_ID" jdbcType="INTEGER" property="positionId" />
    <result column="POSITION_LEVEL" jdbcType="INTEGER" property="positionLevel" />
    <result column="POSITION_NAME" jdbcType="VARCHAR" property="positionName" />
    <result column="POSITION_TYPE" jdbcType="INTEGER" property="positionType" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName" />
    <result column="TYPE" jdbcType="INTEGER" property="type" />
    <result column="RECEIVE_TIME" jdbcType="BIGINT" property="receiveTime" />
    <result column="SOURCE" jdbcType="INTEGER" property="source" />
    <result column="MOBILE" jdbcType="VARCHAR" property="mobile" />
    <result column="COMMUNICATION" jdbcType="INTEGER" property="communication" />
    <result column="CONTENT" jdbcType="VARCHAR" property="content" />
    <result column="GOODS_CATEGORY" jdbcType="INTEGER" property="goodsCategory" />
    <result column="GOODS_NAME" jdbcType="VARCHAR" property="goodsName" />
    <result column="GOODS_BRAND" jdbcType="VARCHAR" property="goodsBrand" />
    <result column="ASSIGN_TIME" jdbcType="BIGINT" property="assignTime" />
    <result column="FIRST_VIEW_TIME" jdbcType="BIGINT" property="firstViewTime" />
    <result column="STATUS" jdbcType="INTEGER" property="status" />
    <result column="BUSSINESS_LEVEL" jdbcType="INTEGER" property="bussinessLevel" />
    <result column="BUSSINESS_STAGE" jdbcType="INTEGER" property="bussinessStage" />
    <result column="ENQUIRY_TYPE" jdbcType="INTEGER" property="enquiryType" />
    <result column="ORDER_RATE" jdbcType="INTEGER" property="orderRate" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="QUOTE_AMOUNT" jdbcType="DECIMAL" property="quoteAmount" />
    <result column="SALEORDER_AMOUNT" jdbcType="DECIMAL" property="saleorderAmount" />
    <result column="ORDER_TIME" jdbcType="BIGINT" property="orderTime" />
    <result column="IS_NEW" jdbcType="INTEGER" property="isNew" />
    <result column="MERGE_STATUS" jdbcType="INTEGER" property="mergeStatus" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="IS_BIG_PROJECT" jdbcType="INTEGER" property="isBigProject" />
    <result column="HAS_CORE_SPU" jdbcType="INTEGER" property="hasCoreSpu" />
    <result column="IS_BIG_CHANCE" jdbcType="INTEGER" property="isBigChance" />
    <result column="BUSSINESS_AMOUNT" jdbcType="DECIMAL" property="bussinessAmount" />
    <result column="QUOTEORDER_ID" jdbcType="INTEGER" property="quoteorderId" />
    <result column="QUOTEORDER_NO" jdbcType="VARCHAR" property="quoteorderNo" />
    <result column="SALEORDER_ID" jdbcType="INTEGER" property="saleorderId" />
    <result column="RELATED_SALEORDER_NO" jdbcType="VARCHAR" property="relatedSaleorderNo" />
    <result column="PURCHASING_TIME" jdbcType="BIGINT" property="purchasingTime" />
    <result column="PURCHASING_TYPE" jdbcType="INTEGER" property="purchasingType" />
    <result column="IS_POLICYMAKER" jdbcType="INTEGER" property="isPolicymaker" />
    <result column="CHECK_TRADER_NAME" jdbcType="VARCHAR" property="checkTraderName" />
    <result column="CHECK_TRADER_AREA" jdbcType="VARCHAR" property="checkTraderArea" />
    <result column="CHECK_TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="checkTraderContactName" />
    <result column="CHECK_TRADER_CONTACT_MOBILE" jdbcType="VARCHAR" property="checkTraderContactMobile" />
    <result column="CHECK_TRADER_CONTACT_TELEPHONE" jdbcType="VARCHAR" property="checkTraderContactTelephone" />
    <result column="TRADER_CONTACT_ID" jdbcType="INTEGER" property="traderContactId" />
    <result column="TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="traderContactName" />
    <result column="TELEPHONE" jdbcType="VARCHAR" property="telephone" />
  </resultMap>
  <sql id="Base_Column_List">
    BUSSINESS_CHANCE_ID, BUSSINESS_CHANCE_NO, COMPANY_ID, USER_ID, ORG_ID, USER_NAME, 
    L1_ID, L1_NAME, L2_ID, L2_NAME, L3_ID, L3_NAME, POSITION_ID, POSITION_LEVEL, POSITION_NAME, 
    POSITION_TYPE, TRADER_ID, TRADER_NAME, `TYPE`, RECEIVE_TIME, `SOURCE`, MOBILE, COMMUNICATION, 
    CONTENT, GOODS_CATEGORY, GOODS_NAME, GOODS_BRAND, ASSIGN_TIME, FIRST_VIEW_TIME, `STATUS`, 
    BUSSINESS_LEVEL, BUSSINESS_STAGE, ENQUIRY_TYPE, ORDER_RATE, AMOUNT, QUOTE_AMOUNT, 
    SALEORDER_AMOUNT, ORDER_TIME, IS_NEW, MERGE_STATUS, ADD_TIME, CREATOR, IS_BIG_PROJECT, 
    HAS_CORE_SPU, IS_BIG_CHANCE, BUSSINESS_AMOUNT, QUOTEORDER_ID, QUOTEORDER_NO, SALEORDER_ID, 
    RELATED_SALEORDER_NO, PURCHASING_TIME, PURCHASING_TYPE, IS_POLICYMAKER, CHECK_TRADER_NAME, 
    CHECK_TRADER_AREA, CHECK_TRADER_CONTACT_NAME, CHECK_TRADER_CONTACT_MOBILE, CHECK_TRADER_CONTACT_TELEPHONE, 
    TRADER_CONTACT_ID, TRADER_CONTACT_NAME, TELEPHONE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from DWS_BUSSINESS_CHANCE_WORKBENCH_DF
    where BUSSINESS_CHANCE_ID = #{bussinessChanceId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from DWS_BUSSINESS_CHANCE_WORKBENCH_DF
    where BUSSINESS_CHANCE_ID = #{bussinessChanceId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="BUSSINESS_CHANCE_ID" keyProperty="bussinessChanceId" parameterType="com.vedeng.dwh.model.generate.DwsBussinessChanceWorkbenchDfDo" useGeneratedKeys="true">
    insert into DWS_BUSSINESS_CHANCE_WORKBENCH_DF (BUSSINESS_CHANCE_NO, COMPANY_ID, USER_ID, 
      ORG_ID, USER_NAME, L1_ID, 
      L1_NAME, L2_ID, L2_NAME, 
      L3_ID, L3_NAME, POSITION_ID, 
      POSITION_LEVEL, POSITION_NAME, POSITION_TYPE, 
      TRADER_ID, TRADER_NAME, `TYPE`, 
      RECEIVE_TIME, `SOURCE`, MOBILE, 
      COMMUNICATION, CONTENT, GOODS_CATEGORY, 
      GOODS_NAME, GOODS_BRAND, ASSIGN_TIME, 
      FIRST_VIEW_TIME, `STATUS`, BUSSINESS_LEVEL, 
      BUSSINESS_STAGE, ENQUIRY_TYPE, ORDER_RATE, 
      AMOUNT, QUOTE_AMOUNT, SALEORDER_AMOUNT, 
      ORDER_TIME, IS_NEW, MERGE_STATUS, 
      ADD_TIME, CREATOR, IS_BIG_PROJECT, 
      HAS_CORE_SPU, IS_BIG_CHANCE, BUSSINESS_AMOUNT, 
      QUOTEORDER_ID, QUOTEORDER_NO, SALEORDER_ID, 
      RELATED_SALEORDER_NO, PURCHASING_TIME, PURCHASING_TYPE, 
      IS_POLICYMAKER, CHECK_TRADER_NAME, CHECK_TRADER_AREA, 
      CHECK_TRADER_CONTACT_NAME, CHECK_TRADER_CONTACT_MOBILE, 
      CHECK_TRADER_CONTACT_TELEPHONE, TRADER_CONTACT_ID, 
      TRADER_CONTACT_NAME, TELEPHONE)
    values (#{bussinessChanceNo,jdbcType=VARCHAR}, #{companyId,jdbcType=INTEGER}, #{userId,jdbcType=INTEGER}, 
      #{orgId,jdbcType=INTEGER}, #{userName,jdbcType=VARCHAR}, #{l1Id,jdbcType=INTEGER}, 
      #{l1Name,jdbcType=VARCHAR}, #{l2Id,jdbcType=INTEGER}, #{l2Name,jdbcType=VARCHAR}, 
      #{l3Id,jdbcType=INTEGER}, #{l3Name,jdbcType=VARCHAR}, #{positionId,jdbcType=INTEGER}, 
      #{positionLevel,jdbcType=INTEGER}, #{positionName,jdbcType=VARCHAR}, #{positionType,jdbcType=INTEGER}, 
      #{traderId,jdbcType=INTEGER}, #{traderName,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, 
      #{receiveTime,jdbcType=BIGINT}, #{source,jdbcType=INTEGER}, #{mobile,jdbcType=VARCHAR}, 
      #{communication,jdbcType=INTEGER}, #{content,jdbcType=VARCHAR}, #{goodsCategory,jdbcType=INTEGER}, 
      #{goodsName,jdbcType=VARCHAR}, #{goodsBrand,jdbcType=VARCHAR}, #{assignTime,jdbcType=BIGINT}, 
      #{firstViewTime,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, #{bussinessLevel,jdbcType=INTEGER}, 
      #{bussinessStage,jdbcType=INTEGER}, #{enquiryType,jdbcType=INTEGER}, #{orderRate,jdbcType=INTEGER}, 
      #{amount,jdbcType=DECIMAL}, #{quoteAmount,jdbcType=DECIMAL}, #{saleorderAmount,jdbcType=DECIMAL}, 
      #{orderTime,jdbcType=BIGINT}, #{isNew,jdbcType=INTEGER}, #{mergeStatus,jdbcType=INTEGER}, 
      #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, #{isBigProject,jdbcType=INTEGER}, 
      #{hasCoreSpu,jdbcType=INTEGER}, #{isBigChance,jdbcType=INTEGER}, #{bussinessAmount,jdbcType=DECIMAL}, 
      #{quoteorderId,jdbcType=INTEGER}, #{quoteorderNo,jdbcType=VARCHAR}, #{saleorderId,jdbcType=INTEGER}, 
      #{relatedSaleorderNo,jdbcType=VARCHAR}, #{purchasingTime,jdbcType=BIGINT}, #{purchasingType,jdbcType=INTEGER}, 
      #{isPolicymaker,jdbcType=INTEGER}, #{checkTraderName,jdbcType=VARCHAR}, #{checkTraderArea,jdbcType=VARCHAR}, 
      #{checkTraderContactName,jdbcType=VARCHAR}, #{checkTraderContactMobile,jdbcType=VARCHAR}, 
      #{checkTraderContactTelephone,jdbcType=VARCHAR}, #{traderContactId,jdbcType=INTEGER}, 
      #{traderContactName,jdbcType=VARCHAR}, #{telephone,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="BUSSINESS_CHANCE_ID" keyProperty="bussinessChanceId" parameterType="com.vedeng.dwh.model.generate.DwsBussinessChanceWorkbenchDfDo" useGeneratedKeys="true">
    insert into DWS_BUSSINESS_CHANCE_WORKBENCH_DF
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bussinessChanceNo != null">
        BUSSINESS_CHANCE_NO,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="orgId != null">
        ORG_ID,
      </if>
      <if test="userName != null">
        USER_NAME,
      </if>
      <if test="l1Id != null">
        L1_ID,
      </if>
      <if test="l1Name != null">
        L1_NAME,
      </if>
      <if test="l2Id != null">
        L2_ID,
      </if>
      <if test="l2Name != null">
        L2_NAME,
      </if>
      <if test="l3Id != null">
        L3_ID,
      </if>
      <if test="l3Name != null">
        L3_NAME,
      </if>
      <if test="positionId != null">
        POSITION_ID,
      </if>
      <if test="positionLevel != null">
        POSITION_LEVEL,
      </if>
      <if test="positionName != null">
        POSITION_NAME,
      </if>
      <if test="positionType != null">
        POSITION_TYPE,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderName != null">
        TRADER_NAME,
      </if>
      <if test="type != null">
        `TYPE`,
      </if>
      <if test="receiveTime != null">
        RECEIVE_TIME,
      </if>
      <if test="source != null">
        `SOURCE`,
      </if>
      <if test="mobile != null">
        MOBILE,
      </if>
      <if test="communication != null">
        COMMUNICATION,
      </if>
      <if test="content != null">
        CONTENT,
      </if>
      <if test="goodsCategory != null">
        GOODS_CATEGORY,
      </if>
      <if test="goodsName != null">
        GOODS_NAME,
      </if>
      <if test="goodsBrand != null">
        GOODS_BRAND,
      </if>
      <if test="assignTime != null">
        ASSIGN_TIME,
      </if>
      <if test="firstViewTime != null">
        FIRST_VIEW_TIME,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
      <if test="bussinessLevel != null">
        BUSSINESS_LEVEL,
      </if>
      <if test="bussinessStage != null">
        BUSSINESS_STAGE,
      </if>
      <if test="enquiryType != null">
        ENQUIRY_TYPE,
      </if>
      <if test="orderRate != null">
        ORDER_RATE,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="quoteAmount != null">
        QUOTE_AMOUNT,
      </if>
      <if test="saleorderAmount != null">
        SALEORDER_AMOUNT,
      </if>
      <if test="orderTime != null">
        ORDER_TIME,
      </if>
      <if test="isNew != null">
        IS_NEW,
      </if>
      <if test="mergeStatus != null">
        MERGE_STATUS,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="isBigProject != null">
        IS_BIG_PROJECT,
      </if>
      <if test="hasCoreSpu != null">
        HAS_CORE_SPU,
      </if>
      <if test="isBigChance != null">
        IS_BIG_CHANCE,
      </if>
      <if test="bussinessAmount != null">
        BUSSINESS_AMOUNT,
      </if>
      <if test="quoteorderId != null">
        QUOTEORDER_ID,
      </if>
      <if test="quoteorderNo != null">
        QUOTEORDER_NO,
      </if>
      <if test="saleorderId != null">
        SALEORDER_ID,
      </if>
      <if test="relatedSaleorderNo != null">
        RELATED_SALEORDER_NO,
      </if>
      <if test="purchasingTime != null">
        PURCHASING_TIME,
      </if>
      <if test="purchasingType != null">
        PURCHASING_TYPE,
      </if>
      <if test="isPolicymaker != null">
        IS_POLICYMAKER,
      </if>
      <if test="checkTraderName != null">
        CHECK_TRADER_NAME,
      </if>
      <if test="checkTraderArea != null">
        CHECK_TRADER_AREA,
      </if>
      <if test="checkTraderContactName != null">
        CHECK_TRADER_CONTACT_NAME,
      </if>
      <if test="checkTraderContactMobile != null">
        CHECK_TRADER_CONTACT_MOBILE,
      </if>
      <if test="checkTraderContactTelephone != null">
        CHECK_TRADER_CONTACT_TELEPHONE,
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID,
      </if>
      <if test="traderContactName != null">
        TRADER_CONTACT_NAME,
      </if>
      <if test="telephone != null">
        TELEPHONE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bussinessChanceNo != null">
        #{bussinessChanceNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="l1Id != null">
        #{l1Id,jdbcType=INTEGER},
      </if>
      <if test="l1Name != null">
        #{l1Name,jdbcType=VARCHAR},
      </if>
      <if test="l2Id != null">
        #{l2Id,jdbcType=INTEGER},
      </if>
      <if test="l2Name != null">
        #{l2Name,jdbcType=VARCHAR},
      </if>
      <if test="l3Id != null">
        #{l3Id,jdbcType=INTEGER},
      </if>
      <if test="l3Name != null">
        #{l3Name,jdbcType=VARCHAR},
      </if>
      <if test="positionId != null">
        #{positionId,jdbcType=INTEGER},
      </if>
      <if test="positionLevel != null">
        #{positionLevel,jdbcType=INTEGER},
      </if>
      <if test="positionName != null">
        #{positionName,jdbcType=VARCHAR},
      </if>
      <if test="positionType != null">
        #{positionType,jdbcType=INTEGER},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderName != null">
        #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="receiveTime != null">
        #{receiveTime,jdbcType=BIGINT},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="communication != null">
        #{communication,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="goodsCategory != null">
        #{goodsCategory,jdbcType=INTEGER},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="goodsBrand != null">
        #{goodsBrand,jdbcType=VARCHAR},
      </if>
      <if test="assignTime != null">
        #{assignTime,jdbcType=BIGINT},
      </if>
      <if test="firstViewTime != null">
        #{firstViewTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="bussinessLevel != null">
        #{bussinessLevel,jdbcType=INTEGER},
      </if>
      <if test="bussinessStage != null">
        #{bussinessStage,jdbcType=INTEGER},
      </if>
      <if test="enquiryType != null">
        #{enquiryType,jdbcType=INTEGER},
      </if>
      <if test="orderRate != null">
        #{orderRate,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="quoteAmount != null">
        #{quoteAmount,jdbcType=DECIMAL},
      </if>
      <if test="saleorderAmount != null">
        #{saleorderAmount,jdbcType=DECIMAL},
      </if>
      <if test="orderTime != null">
        #{orderTime,jdbcType=BIGINT},
      </if>
      <if test="isNew != null">
        #{isNew,jdbcType=INTEGER},
      </if>
      <if test="mergeStatus != null">
        #{mergeStatus,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="isBigProject != null">
        #{isBigProject,jdbcType=INTEGER},
      </if>
      <if test="hasCoreSpu != null">
        #{hasCoreSpu,jdbcType=INTEGER},
      </if>
      <if test="isBigChance != null">
        #{isBigChance,jdbcType=INTEGER},
      </if>
      <if test="bussinessAmount != null">
        #{bussinessAmount,jdbcType=DECIMAL},
      </if>
      <if test="quoteorderId != null">
        #{quoteorderId,jdbcType=INTEGER},
      </if>
      <if test="quoteorderNo != null">
        #{quoteorderNo,jdbcType=VARCHAR},
      </if>
      <if test="saleorderId != null">
        #{saleorderId,jdbcType=INTEGER},
      </if>
      <if test="relatedSaleorderNo != null">
        #{relatedSaleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchasingTime != null">
        #{purchasingTime,jdbcType=BIGINT},
      </if>
      <if test="purchasingType != null">
        #{purchasingType,jdbcType=INTEGER},
      </if>
      <if test="isPolicymaker != null">
        #{isPolicymaker,jdbcType=INTEGER},
      </if>
      <if test="checkTraderName != null">
        #{checkTraderName,jdbcType=VARCHAR},
      </if>
      <if test="checkTraderArea != null">
        #{checkTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="checkTraderContactName != null">
        #{checkTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="checkTraderContactMobile != null">
        #{checkTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="checkTraderContactTelephone != null">
        #{checkTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="traderContactId != null">
        #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null">
        #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        #{telephone,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.dwh.model.generate.DwsBussinessChanceWorkbenchDfDo">
    update DWS_BUSSINESS_CHANCE_WORKBENCH_DF
    <set>
      <if test="bussinessChanceNo != null">
        BUSSINESS_CHANCE_NO = #{bussinessChanceNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        ORG_ID = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="userName != null">
        USER_NAME = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="l1Id != null">
        L1_ID = #{l1Id,jdbcType=INTEGER},
      </if>
      <if test="l1Name != null">
        L1_NAME = #{l1Name,jdbcType=VARCHAR},
      </if>
      <if test="l2Id != null">
        L2_ID = #{l2Id,jdbcType=INTEGER},
      </if>
      <if test="l2Name != null">
        L2_NAME = #{l2Name,jdbcType=VARCHAR},
      </if>
      <if test="l3Id != null">
        L3_ID = #{l3Id,jdbcType=INTEGER},
      </if>
      <if test="l3Name != null">
        L3_NAME = #{l3Name,jdbcType=VARCHAR},
      </if>
      <if test="positionId != null">
        POSITION_ID = #{positionId,jdbcType=INTEGER},
      </if>
      <if test="positionLevel != null">
        POSITION_LEVEL = #{positionLevel,jdbcType=INTEGER},
      </if>
      <if test="positionName != null">
        POSITION_NAME = #{positionName,jdbcType=VARCHAR},
      </if>
      <if test="positionType != null">
        POSITION_TYPE = #{positionType,jdbcType=INTEGER},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderName != null">
        TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `TYPE` = #{type,jdbcType=INTEGER},
      </if>
      <if test="receiveTime != null">
        RECEIVE_TIME = #{receiveTime,jdbcType=BIGINT},
      </if>
      <if test="source != null">
        `SOURCE` = #{source,jdbcType=INTEGER},
      </if>
      <if test="mobile != null">
        MOBILE = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="communication != null">
        COMMUNICATION = #{communication,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        CONTENT = #{content,jdbcType=VARCHAR},
      </if>
      <if test="goodsCategory != null">
        GOODS_CATEGORY = #{goodsCategory,jdbcType=INTEGER},
      </if>
      <if test="goodsName != null">
        GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="goodsBrand != null">
        GOODS_BRAND = #{goodsBrand,jdbcType=VARCHAR},
      </if>
      <if test="assignTime != null">
        ASSIGN_TIME = #{assignTime,jdbcType=BIGINT},
      </if>
      <if test="firstViewTime != null">
        FIRST_VIEW_TIME = #{firstViewTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        `STATUS` = #{status,jdbcType=INTEGER},
      </if>
      <if test="bussinessLevel != null">
        BUSSINESS_LEVEL = #{bussinessLevel,jdbcType=INTEGER},
      </if>
      <if test="bussinessStage != null">
        BUSSINESS_STAGE = #{bussinessStage,jdbcType=INTEGER},
      </if>
      <if test="enquiryType != null">
        ENQUIRY_TYPE = #{enquiryType,jdbcType=INTEGER},
      </if>
      <if test="orderRate != null">
        ORDER_RATE = #{orderRate,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="quoteAmount != null">
        QUOTE_AMOUNT = #{quoteAmount,jdbcType=DECIMAL},
      </if>
      <if test="saleorderAmount != null">
        SALEORDER_AMOUNT = #{saleorderAmount,jdbcType=DECIMAL},
      </if>
      <if test="orderTime != null">
        ORDER_TIME = #{orderTime,jdbcType=BIGINT},
      </if>
      <if test="isNew != null">
        IS_NEW = #{isNew,jdbcType=INTEGER},
      </if>
      <if test="mergeStatus != null">
        MERGE_STATUS = #{mergeStatus,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="isBigProject != null">
        IS_BIG_PROJECT = #{isBigProject,jdbcType=INTEGER},
      </if>
      <if test="hasCoreSpu != null">
        HAS_CORE_SPU = #{hasCoreSpu,jdbcType=INTEGER},
      </if>
      <if test="isBigChance != null">
        IS_BIG_CHANCE = #{isBigChance,jdbcType=INTEGER},
      </if>
      <if test="bussinessAmount != null">
        BUSSINESS_AMOUNT = #{bussinessAmount,jdbcType=DECIMAL},
      </if>
      <if test="quoteorderId != null">
        QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER},
      </if>
      <if test="quoteorderNo != null">
        QUOTEORDER_NO = #{quoteorderNo,jdbcType=VARCHAR},
      </if>
      <if test="saleorderId != null">
        SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
      </if>
      <if test="relatedSaleorderNo != null">
        RELATED_SALEORDER_NO = #{relatedSaleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchasingTime != null">
        PURCHASING_TIME = #{purchasingTime,jdbcType=BIGINT},
      </if>
      <if test="purchasingType != null">
        PURCHASING_TYPE = #{purchasingType,jdbcType=INTEGER},
      </if>
      <if test="isPolicymaker != null">
        IS_POLICYMAKER = #{isPolicymaker,jdbcType=INTEGER},
      </if>
      <if test="checkTraderName != null">
        CHECK_TRADER_NAME = #{checkTraderName,jdbcType=VARCHAR},
      </if>
      <if test="checkTraderArea != null">
        CHECK_TRADER_AREA = #{checkTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="checkTraderContactName != null">
        CHECK_TRADER_CONTACT_NAME = #{checkTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="checkTraderContactMobile != null">
        CHECK_TRADER_CONTACT_MOBILE = #{checkTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="checkTraderContactTelephone != null">
        CHECK_TRADER_CONTACT_TELEPHONE = #{checkTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null">
        TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        TELEPHONE = #{telephone,jdbcType=VARCHAR},
      </if>
    </set>
    where BUSSINESS_CHANCE_ID = #{bussinessChanceId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.dwh.model.generate.DwsBussinessChanceWorkbenchDfDo">
    update DWS_BUSSINESS_CHANCE_WORKBENCH_DF
    set BUSSINESS_CHANCE_NO = #{bussinessChanceNo,jdbcType=VARCHAR},
      COMPANY_ID = #{companyId,jdbcType=INTEGER},
      USER_ID = #{userId,jdbcType=INTEGER},
      ORG_ID = #{orgId,jdbcType=INTEGER},
      USER_NAME = #{userName,jdbcType=VARCHAR},
      L1_ID = #{l1Id,jdbcType=INTEGER},
      L1_NAME = #{l1Name,jdbcType=VARCHAR},
      L2_ID = #{l2Id,jdbcType=INTEGER},
      L2_NAME = #{l2Name,jdbcType=VARCHAR},
      L3_ID = #{l3Id,jdbcType=INTEGER},
      L3_NAME = #{l3Name,jdbcType=VARCHAR},
      POSITION_ID = #{positionId,jdbcType=INTEGER},
      POSITION_LEVEL = #{positionLevel,jdbcType=INTEGER},
      POSITION_NAME = #{positionName,jdbcType=VARCHAR},
      POSITION_TYPE = #{positionType,jdbcType=INTEGER},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      `TYPE` = #{type,jdbcType=INTEGER},
      RECEIVE_TIME = #{receiveTime,jdbcType=BIGINT},
      `SOURCE` = #{source,jdbcType=INTEGER},
      MOBILE = #{mobile,jdbcType=VARCHAR},
      COMMUNICATION = #{communication,jdbcType=INTEGER},
      CONTENT = #{content,jdbcType=VARCHAR},
      GOODS_CATEGORY = #{goodsCategory,jdbcType=INTEGER},
      GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      GOODS_BRAND = #{goodsBrand,jdbcType=VARCHAR},
      ASSIGN_TIME = #{assignTime,jdbcType=BIGINT},
      FIRST_VIEW_TIME = #{firstViewTime,jdbcType=BIGINT},
      `STATUS` = #{status,jdbcType=INTEGER},
      BUSSINESS_LEVEL = #{bussinessLevel,jdbcType=INTEGER},
      BUSSINESS_STAGE = #{bussinessStage,jdbcType=INTEGER},
      ENQUIRY_TYPE = #{enquiryType,jdbcType=INTEGER},
      ORDER_RATE = #{orderRate,jdbcType=INTEGER},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      QUOTE_AMOUNT = #{quoteAmount,jdbcType=DECIMAL},
      SALEORDER_AMOUNT = #{saleorderAmount,jdbcType=DECIMAL},
      ORDER_TIME = #{orderTime,jdbcType=BIGINT},
      IS_NEW = #{isNew,jdbcType=INTEGER},
      MERGE_STATUS = #{mergeStatus,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      IS_BIG_PROJECT = #{isBigProject,jdbcType=INTEGER},
      HAS_CORE_SPU = #{hasCoreSpu,jdbcType=INTEGER},
      IS_BIG_CHANCE = #{isBigChance,jdbcType=INTEGER},
      BUSSINESS_AMOUNT = #{bussinessAmount,jdbcType=DECIMAL},
      QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER},
      QUOTEORDER_NO = #{quoteorderNo,jdbcType=VARCHAR},
      SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
      RELATED_SALEORDER_NO = #{relatedSaleorderNo,jdbcType=VARCHAR},
      PURCHASING_TIME = #{purchasingTime,jdbcType=BIGINT},
      PURCHASING_TYPE = #{purchasingType,jdbcType=INTEGER},
      IS_POLICYMAKER = #{isPolicymaker,jdbcType=INTEGER},
      CHECK_TRADER_NAME = #{checkTraderName,jdbcType=VARCHAR},
      CHECK_TRADER_AREA = #{checkTraderArea,jdbcType=VARCHAR},
      CHECK_TRADER_CONTACT_NAME = #{checkTraderContactName,jdbcType=VARCHAR},
      CHECK_TRADER_CONTACT_MOBILE = #{checkTraderContactMobile,jdbcType=VARCHAR},
      CHECK_TRADER_CONTACT_TELEPHONE = #{checkTraderContactTelephone,jdbcType=VARCHAR},
      TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      TELEPHONE = #{telephone,jdbcType=VARCHAR}
    where BUSSINESS_CHANCE_ID = #{bussinessChanceId,jdbcType=INTEGER}
  </update>
</mapper>