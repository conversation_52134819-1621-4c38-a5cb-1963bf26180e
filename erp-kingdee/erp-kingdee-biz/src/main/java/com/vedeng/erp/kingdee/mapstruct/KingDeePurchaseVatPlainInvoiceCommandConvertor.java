package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeeInPutFeeSpecialInvoiceCommand;
import com.vedeng.erp.kingdee.domain.command.KingDeePurchaseVatPlainInvoiceCommand;
import com.vedeng.erp.kingdee.dto.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 金蝶 采购普票 dto 转 command
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface KingDeePurchaseVatPlainInvoiceCommandConvertor extends BaseCommandMapStruct<KingDeePurchaseVatPlainInvoiceCommand,PurchaseVatPlainInvoiceDto> {
    /**
     * KingDeePurchaseVatPlainInvoiceCommand
     *
     * @param dto InPutFeePlainInvoiceDto
     * @return KingDeePurchaseVatPlainInvoiceCommand
     */
    @Mapping(target = "FID", source = "fid")
    @Mapping(target = "FDATE", source = "fdate")
    @Mapping(target = "f_QZOK_BDDJTID", source = "FQzokBddjtid")
    @Mapping(target = "FINVOICENO", source = "finvoiceno")
    @Mapping(target = "FINVOICEDATE", source = "finvoicedate")
    @Mapping(target = "FSUPPLIERID.FNumber", source = "fsupplierid")
    @Mapping(target = "FDOCUMENTSTATUS", source = "fdocumentstatus")
    @Mapping(target = "FBillTypeID.FNumber", source = "FBillTypeID")
    @Mapping(target = "FSETTLEORGID.FNumber", source = "fsettleorgid")
    @Mapping(target = "FPURCHASEORGID.FNumber", source = "fpurchaseorgid")
    @Mapping(target = "FCancelStatus", source = "FCancelStatus")
    @Mapping(target = "f_QZOK_FPDM", source = "FQzokFpdm")
    @Mapping(target = "f_QZOK_FPLX", source = "FQzokFplx")
    @Override
    KingDeePurchaseVatPlainInvoiceCommand toCommand(PurchaseVatPlainInvoiceDto dto);

    /**
     * PurchaseVatPlainInvoiceDetailCommand
     *
     * @param dto PurchaseVatPlainInvoiceDetailDto
     * @return PurchaseVatPlainInvoiceDetailCommand
     */
    @Mapping(target = "FMATERIALID.FNumber", source = "fmaterialid")
    @Mapping(target = "FPRICEQTY", source = "fpriceqty")
    @Mapping(target = "FAUXTAXPRICE", source = "fauxtaxprice")
    @Mapping(target = "f_QZOK_BDDJHID", source = "FQzokBddjhid")
    @Mapping(target = "f_QZOK_YSDDH", source = "FQzokYsddh")
    @Mapping(target = "f_QZOK_GSYWDH", source = "FQzokGsywdh")
    @Mapping(target = "f_QZOK_YWLX", source = "FQzokYwlx")
    @Mapping(target = "FSOURCETYPE", source = "fsourcetype")
    @Mapping(target = "FPURCHASEICENTRY_Link",source = "fpurchaseicentryLink")
    KingDeePurchaseVatPlainInvoiceCommand.PurchaseVatPlainInvoiceDetailCommand toCommand(PurchaseVatPlainInvoiceDetailDto dto);

    /**
     * PurchaseVatPlainInvoiceDetailLinkCommand
     *
     * @param dto PurchaseVatPlainInvoiceDetailLinkDto
     * @return PurchaseVatPlainInvoiceDetailLinkCommand
     */
    @Mapping(target = "FPURCHASEICENTRY_Link_FFlowId", source = "fpurchaseicentryLinkFflowid")
    @Mapping(target = "FPURCHASEICENTRY_Link_FFlowLineId", source = "fpurchaseicentryLinkFflowlineid")
    @Mapping(target = "FPURCHASEICENTRY_Link_FRuleId", source = "fpurchaseicentryLinkFruleid")
    @Mapping(target = "FPURCHASEICENTRY_Link_FSTableId", source = "fpurchaseicentryLinkFstableid")
    @Mapping(target = "FPURCHASEICENTRY_Link_FSTableName", source = "fpurchaseicentryLinkFstablename")
    @Mapping(target = "FPURCHASEICENTRY_Link_FSBillId", source = "fpurchaseicentryLinkFsbillid")
    @Mapping(target = "FPURCHASEICENTRY_Link_FSId", source = "fpurchaseicentryLinkFsid")
    @Mapping(target = "FPURCHASEICENTRY_Link_FBASICUNITQTYOld", source = "fpurchaseicentryLinkFbasicunitqtyold")
    @Mapping(target = "FPURCHASEICENTRY_Link_FBASICUNITQTY", source = "fpurchaseicentryLinkFbasicunitqty")
    @Mapping(target = "FPURCHASEICENTRY_Link_FALLAMOUNTFOROld", source = "fpurchaseicentryLinkFallamountforold")
    @Mapping(target = "FPURCHASEICENTRY_Link_FALLAMOUNTFOR", source = "fpurchaseicentryLinkFallamountfor")
    KingDeePurchaseVatPlainInvoiceCommand.PurchaseVatPlainInvoiceDetailLinkCommand toCommand(PurchaseVatPlainInvoiceDetailLinkDto dto);
}
