package com.vedeng.goodsSearch.constant;

/**
 * 商品排序字段映射枚举
 *
 * <AUTHOR>
 */
public enum GoodsSortColumnMappingEnum {

    /**
     * SKU_ID
     */
    SKU_ID(0, "skuId", "SKU_ID", "skuId"),


    /**
     * SKU_NO
     */
    SKU_NO(0, "skuNo", "SKU_NO", "skuNo"),

    /**
     * MODEL
     */
    MODEL(0, "model", "MODEL", "model"),


    /**
     * SPEC
     */
    SPEC(0, "spec", "SPEC", "spec"),

    /**
     * RULE_LEVEL
     */
    RULE_LEVEL(0, "ruleLevel", "GOODS_LEVEL", "ruleLevel"),

    /**
     * 终端价
     */
    TERMINAL_PRICE(1, "terminalPrice", "TERMINAL_PRICE", "terminalPrice"),

    /**
     * 经销价
     */
    DISTRIBUTION_PRICE(1, "distributionPrice", "DISTRIBUTION_PRICE", "distributionPrice"),


    /***
     * 可用库存
     */
    AVAILABLE_STOCK_NUM(1, "availableStock", "AVAILABLE_STOCK_NUM", "availableStockNum"),


    /**
     * 在途库存
     */
    ONWAYNUM(1, "onWayNum", "ONWAYNUM", "onWayNum"),


    /**
     * 库存量
     */
    STOCK_NUM(1, "stock", "STOCK_NUM", "stockNum"),


    /**
     * PURCHASE_TIME
     */
    PURCHASE_TIME(0, "purchaseTime", "PURCHASE_TIME", ""),

    /**
     * 近一年销量
     */
    SALE_COUNT_OF_LAST_YEAR(1, "saleCountOfLastYear", "SALE_COUNT_OF_LAST_YEAR", "oneYearSaleNum"),

    /**
     * 最近一年单价
     */
    AVG_PRICE_OF_LAST_YEAR(1, "avgPriceOfLastYear", "AVG_PRICE_OF_LAST_YEAR", "oneYearAvgPrice"),

    /**
     * SKU_NAME
     */
    SKU_NAME(0, "skuName", "SKU_NAME", "title");



    /**
     * 代码
     */
    private Integer code;

    /**
     * 搜索字段
     */
    private String searchColumn;

    /**
     * 库表字段
     */
    private String tableColumn;

    /**
     * es字段
     */
    private String esColumn;


    public static GoodsSortColumnMappingEnum getInstance(Integer code, String searchColumn) {
        for (GoodsSortColumnMappingEnum v : values()) {
            if (v.code.equals(code) && v.searchColumn.equalsIgnoreCase(searchColumn)) {
                return v;
            }
        }
        return null;
    }


    GoodsSortColumnMappingEnum(Integer code, String searchColumn, String tableColumn, String esColumn) {
        this.code = code;
        this.searchColumn = searchColumn;
        this.tableColumn = tableColumn;
        this.esColumn = esColumn;
    }

    public Integer getCode() {
        return code;
    }

    public String getSearchColumn() {
        return searchColumn;
    }

    public String getTableColumn() {
        return tableColumn;
    }

    public String getEsColumn() {
        return esColumn;
    }
}
