package com.vedeng.order.controller;

import cn.hutool.core.lang.Assert;
import com.common.dto.StepsNodeDto;
import com.common.enums.StepsTypeEnum;
import com.vedeng.activiti.model.AssigneeVo;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.authorization.model.User;
import com.vedeng.common.annotation.SystemControllerLog;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.putHCutil.service.HcSaleorderService;
import com.vedeng.common.util.StringUtil;
import com.vedeng.common.validator.FormToken;
import com.vedeng.erp.aftersale.dto.ExpenseAfterSalesDto;
import com.vedeng.erp.aftersale.service.ExpenseAfterSalesApiService;
import com.vedeng.erp.buyorder.dto.AuditRecordDto;
import com.vedeng.order.service.RiskCheckService;
import com.vedeng.system.service.UserService;
import com.vedeng.system.service.VerifiesRecordService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 采购费用售后审核，屎里雕花
 * @date 2022/11/3 10:23
 **/
@Controller
@RequestMapping("/old/buyorderExpenseAfterSales")
@Slf4j
public class BuyOrderExpenseAfterSalesVerifyController {


    @Autowired // 自动装载
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();


    @Autowired
    @Qualifier("actionProcdefService")
    private ActionProcdefService actionProcdefService;

    @Autowired
    @Qualifier("verifiesRecordService")
    private VerifiesRecordService verifiesRecordService;


    @Autowired
    @Qualifier("hcSaleorderService")
    protected HcSaleorderService hcSaleorderService;

    @Autowired
    private ExpenseAfterSalesApiService expenseAfterSalesApiService;


    @Resource
    private UserService userService;

    @Autowired
    private RiskCheckService riskCheckService;


    /**
     * 采购费用售后审核申请审核
     */
//    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/editApplyAudit")
    @SystemControllerLog(operationType = "edit", desc = "采购费用售后申请审核")
    @NoNeedAccessAuthorization
    public ResultInfo<?> editApplyAudit(HttpServletRequest request, Long expenseAfterSalesId, String taskId) {

        Assert.notNull(expenseAfterSalesId, "采购费用售后发起审核采购费用单id不可为空");
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);

        // 查询当前订单的一些状态
        ExpenseAfterSalesDto expenseAfterSalesDto = expenseAfterSalesApiService.getAfterSalesAndStatusInfo(expenseAfterSalesId);

        try {
            Map<String, Object> variableMap = new HashMap<String, Object>();


            // 开始生成流程(如果没有taskId表示新流程需要生成)
            if (taskId.equals("0")) {
                variableMap.put("expenseAfterSales", expenseAfterSalesDto);
                variableMap.put("currentAssinee", user.getUsername());
                variableMap.put("processDefinitionKey", "expenseAfterSalesVerify");
                variableMap.put("businessKey", "expenseAfterSalesVerify" + "_" + expenseAfterSalesDto.getExpenseAfterSalesId());
                variableMap.put("relateTableKey", Math.toIntExact(expenseAfterSalesDto.getExpenseAfterSalesId()));
                variableMap.put("relateTable", "T_EXPENSE_AFTER_SALES");
                variableMap.put("orgId", user.getOrgId());
                actionProcdefService.createProcessInstance(request, "expenseAfterSalesVerify",
                        "expenseAfterSalesVerify" + "_" + expenseAfterSalesDto.getExpenseAfterSalesId(), variableMap);
            }
            expenseAfterSalesApiService.doAudit(expenseAfterSalesId);
            // 默认申请人通过
            // 根据BusinessKey获取生成的审核实例

            Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
                    "expenseAfterSalesVerify" + "_" + expenseAfterSalesDto.getExpenseAfterSalesId());
            if (historicInfo.get("endStatus") != "审核完成") {
                Task taskInfo = (Task) historicInfo.get("taskInfo");
                taskId = taskInfo.getId();
                Authentication.setAuthenticatedUserId(user.getUsername());
                Map<String, Object> variables = new HashMap<String, Object>();
                // 设置审核完成监听器回写参数
                variables.put("tableName", "T_EXPENSE_AFTER_SALES_STATUS");
                variables.put("id", "EXPENSE_AFTER_SALES_ID");
                variables.put("idValue", Math.toIntExact(expenseAfterSalesDto.getExpenseAfterSalesId()));
                variables.put("key", "AFTER_SALES_STATUS");
                variables.put("value", 1);
                // 回写数据的表在db中
                variables.put("db", 2);
                // 默认审批通过
                ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, "",
                        user.getUsername(), variables);
                // 如果未结束添加审核对应主表的审核状态
                if (!complementStatus.getData().equals("endEvent")) {
                    verifiesRecordService.saveVerifiesInfo(taskId, 0);
                }
            }

            return new ResultInfo(0, "操作成功");
        } catch (Exception e) {
            log.error("editApplyAudit:", e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }

    }

    /**
     * 采购费用售后审核
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/complement")
    @NoNeedAccessAuthorization
    public ModelAndView complement(String taskId, Boolean pass, Integer type, Long expenseAfterSalesId) {
        ModelAndView mv = new ModelAndView();

        //防止多个用户点击审核操作，导致空指针的异常，如果查询不到对应的task，则弹出提示框，刷新父页面
        Task task = processEngine.getTaskService().createTaskQuery().taskId(taskId).singleResult();
        if (task == null) {
            mv.addObject("error_tips", "该采购费用售后单审核状态已发生变化，将刷新采购单售后详情页");
            mv.setViewName("order/buyorder/parent_reload");
            return mv;
        }

        mv.addObject("taskId", taskId);
        mv.addObject("pass", pass);
        mv.addObject("type", type);
        if (null != expenseAfterSalesId) {
            mv.addObject("expenseAfterSalesId", expenseAfterSalesId);
        } else {
            mv.addObject("expenseAfterSalesId", 0);
        }
        mv.setViewName("aftersales/buyorderexpense/new_complement");
        return mv;
    }


    @ResponseBody
    @RequestMapping(value = "/complementAfterSaleTask")
    @SystemControllerLog(operationType = "add", desc = "费用单售后审核操作")
    @NoNeedAccessAuthorization
    public ResultInfo<?> complementAfterSaleTask(HttpServletRequest request, String taskId, String comment, Boolean pass,
                                                 HttpSession session, Long expenseAfterSalesId) {
        // 获取session中user信息
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        Map<String, Object> variables = new HashMap<>();
        variables.put("pass", pass);
        ResultInfo backResultInfo = new ResultInfo(0, "操作成功", expenseAfterSalesId);
        //审批操作
        try {

            if (!pass) {
                //如果审核不通过 获取任务的Service，设置和获取流程变量
                TaskService taskService = processEngine.getTaskService();
                //如果未结束添加审核对应主表的审核状态

                verifiesRecordService.saveVerifiesInfo(taskId, 2);

                //更新主表审核状态
                expenseAfterSalesApiService.unAudit(expenseAfterSalesId);

            }
            ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, comment, user.getUsername(), variables);
            //如果审核没结束添加审核对应主表的审核状态
            if (!"endEvent".equals(complementStatus.getData())) {
                Integer status = 0;
                if (pass) {
                    //如果审核通过
                    status = 0;
                } else {
                    //如果审核不通过
                    status = 2;
                }
                verifiesRecordService.saveVerifiesInfo(taskId, status);

            }

            return backResultInfo;
        } catch (Exception e) {
            log.error(" activity error complementAfterSaleTask:" + expenseAfterSalesId, e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }


    }

    /**
     * 获取采购费用售后单审核记录
     *
     * @param expenseAfterSalesId
     * @return
     */
    @RequestMapping("/getBuyorderExpenseAfterSalesCheckStatus")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<?> getBuyorderExpenseAfterSalesCheckStatus(Long expenseAfterSalesId) {
        Map<String, Object> stringObjectMap = this.buyorderCheckStatus(expenseAfterSalesId);
        ExpenseAfterSalesDto expenseAfterSalesDto =expenseAfterSalesApiService.getAfterSalesAndStatusInfo(expenseAfterSalesId);
        Integer expenseAfterSalesType = expenseAfterSalesDto.getExpenseAfterSalesType();
        CurrentUser currentUser = CurrentUser.getCurrentUser();

        boolean isOperator = false;
        if (expenseAfterSalesDto.getCreator().equals(currentUser.getId())) {
            isOperator = true;
        }

        User user = userService.getUserById(expenseAfterSalesDto.getCreator());
        if (currentUser.getId().equals(user.getParentId())) {
            isOperator = true;
        }

        // 所有的审核记录
        List<AuditRecordDto> auditRecordDtos = this.bindAuditRecordData(stringObjectMap);

        // 审核进度条 (此处默认 以知道所有审核流程节点，若节点发生变化，此处需要更新)
        List<StepsNodeDto> lastAudit = getLastAudit(auditRecordDtos, expenseAfterSalesType);

        Task taskInfo = (Task) stringObjectMap.get("taskInfo");

        boolean isCheckUser = false;
        if (stringObjectMap.get("verifyUsers") != null) {
            String[] verifyUsers = stringObjectMap.get("verifyUsers").toString().split(",");
            for (String verify : verifyUsers) {
               if (verify.contains(currentUser.getUsername())) {
                   isCheckUser = true;
                   break;
               }
            }
        }

        Map<String, Object> result = new HashMap<>(5);
        result.put("taskInfo", taskInfo == null ? 0 : taskInfo.getId());
        result.put("lastAudit", lastAudit);
        result.put("isCheckUser", isCheckUser);
        result.put("isOperator", isOperator);
        return R.success(result);
    }

    private List<StepsNodeDto> getLastAudit(List<AuditRecordDto> recordDtoList, Integer expenseAfterSalesType) {
        // 有可能会存在多条审核记录，所以需要逆序遍历 截取最后一次的审核流节点
        int last = 0;
        for (int i = recordDtoList.size(); i-- > 0; ) {
            if ("开始".equals(recordDtoList.get(i).getOperation())) {
                last = i;
                break;
            }
        }
        List<AuditRecordDto> newList = recordDtoList.subList(last, recordDtoList.size());

        List<StepsNodeDto> nodeList = new ArrayList<>();
        DateFormat cst = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        DateFormat gmt = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.ENGLISH);

        StepsNodeDto startNode = StepsNodeDto.builder().title("开始审核").type(StepsTypeEnum.wait.getType()).build();
        StepsNodeDto applyNode = StepsNodeDto.builder().title("申请人").type(StepsTypeEnum.wait.getType()).build();
        StepsNodeDto productDirectorNode = StepsNodeDto.builder().title("产品主管审核").type(StepsTypeEnum.wait.getType()).build();
        StepsNodeDto superiorNode = StepsNodeDto.builder().title("上级或供应主管或产品总监审核").type(StepsTypeEnum.wait.getType()).build();
        StepsNodeDto financialOfficerNode = StepsNodeDto.builder().title("财务专员审核").type(StepsTypeEnum.wait.getType()).build();
        StepsNodeDto endNode = StepsNodeDto.builder().title("审核完成").type(StepsTypeEnum.wait.getType()).build();

        String processNode = "";
        if (newList.size() > 0) {
            processNode = newList.get(newList.size() - 1).getOperation();
            for (AuditRecordDto auditRecordDto : newList) {
                List<String> description = new ArrayList<>();
                description.add(auditRecordDto.getOperator());

                // 时间为null则证名该节点为当前正在进行中的节点
                if (auditRecordDto.getOperationTime() != null) {
                    try {
                        description.add(cst.format(gmt.parse(auditRecordDto.getOperationTime().toString())));
                    } catch (ParseException e) {
                        log.error("日期格式转化异常");
                    }
                }

                description.add(auditRecordDto.getRemark());
                switch (auditRecordDto.getOperation()) {
                    case "开始":
                        break;
                    case "申请人":
                        applyNode.setDescriptions(description);
                        break;
                    case "产品主管审核":
                        productDirectorNode.setDescriptions(description);
                        startNode.setType(StepsTypeEnum.success.getType());
                        applyNode.setType(StepsTypeEnum.success.getType());
                        break;
                    case "上级或供应主管或产品总监审核":
                        superiorNode.setDescriptions(description);
                        startNode.setType(StepsTypeEnum.success.getType());
                        applyNode.setType(StepsTypeEnum.success.getType());
                        break;
                    case "财务专员审核":
                        financialOfficerNode.setDescriptions(description);
                        superiorNode.setType(StepsTypeEnum.success.getType());
                        break;
                    case "审核完成":
                        endNode.setDescriptions(description);
                        financialOfficerNode.setType(StepsTypeEnum.success.getType());
                        productDirectorNode.setType(StepsTypeEnum.success.getType());
                        endNode.setType(StepsTypeEnum.success.getType());
                        break;
                    case "驳回":
                        endNode.setDescriptions(description);
                        endNode.setTitle("审核不通过");
                        endNode.setType(StepsTypeEnum.error.getType());
                    default:
                }
            }
        }

        if ("驳回".equals(processNode)) {
            if ("产品主管审核".equals(newList.get(newList.size() - 2).getOperation())) {
                productDirectorNode.setType(StepsTypeEnum.error.getType());
            }

            if ("财务专员审核".equals(newList.get(newList.size() - 2).getOperation())) {
                financialOfficerNode.setType(StepsTypeEnum.error.getType());
            }

            if ("上级或供应主管或产品总监审核".equals(newList.get(newList.size() - 2).getOperation())) {
                superiorNode.setType(StepsTypeEnum.error.getType());
                financialOfficerNode.setType(StepsTypeEnum.error.getType());
            }
        }

        nodeList.add(startNode);
        nodeList.add(applyNode);
        if (expenseAfterSalesType == 4121) {
            nodeList.add(productDirectorNode);
        } else {
            nodeList.add(superiorNode);
            nodeList.add(financialOfficerNode);
        }
        nodeList.add(endNode);
        return nodeList;
    }

    /**
     * 根据用户名获取真实姓名 xxx（xx）
     *
     * @param userName
     * @return
     */
    public String getRealNameByUserName(String userName) {
        return userService.getRealNameByUserName(userName);
    }

    private Map<String, Object> buyorderCheckStatus(Long expenseAfterSalesId) {
        Map<String, Object> result = new HashMap<>();
        if (expenseAfterSalesId == null) {
            return result;
        }

        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
                "expenseAfterSalesVerify" + "_" + expenseAfterSalesId);
        result.put("taskInfo", historicInfo.get("taskInfo"));
        result.put("startUser", historicInfo.get("startUser") != null ?
                getRealNameByUserName(historicInfo.get("startUser").toString()) : null);
        result.put("candidateUserMap", historicInfo.get("candidateUserMap"));
        // 最后审核状态
        result.put("endStatus", historicInfo.get("endStatus"));

        List<HistoricActivityInstance> historicActivityInstance = setAssignRealNames(result, historicInfo);

        boolean permoissionsFlag = false;
        if (historicInfo != null && historicInfo.get("startUser") != null) {
            User startUser = userService.getByUsername(historicInfo.get("startUser").toString(), 1);
            permoissionsFlag = startUser != null ? riskCheckService.isOrgFlag(startUser, ErpConst.QUALITY_ORG) : false;
        }
        result.put("permoissionsFlag", permoissionsFlag);
        result.put("historicActivityInstance", historicActivityInstance);
        Object commentMap = historicInfo.get("commentMap");
        result.put("commentMap", commentMap);
        Task taskInfoPay = (Task) historicInfo.get("taskInfo");
        String verifyUsersPay = null;
        if (null != taskInfoPay) {

            Map candidateUserMap = (Map) historicInfo.get("candidateUserMap");

            //获取审核人候选组
            List<IdentityLink> candidateUserList = (List<IdentityLink>) candidateUserMap.get(taskInfoPay.getId());

            if (CollectionUtils.isNotEmpty(candidateUserList)) {

                List<String> userNameList = candidateUserList.stream().map(IdentityLink::getUserId).collect(Collectors.toList());

                verifyUsersPay = StringUtils.join(userNameList, ",");

            }
        }
        String verifyUsers = getVerifyUserRealNames(verifyUsersPay);
        result.put("verifyUsers", verifyUsers);

        return result;

    }

    /**
     * 获取多个审核人员真实姓名
     *
     * @param verifyUsers
     * @return
     */
    protected String getVerifyUserRealNames(String verifyUsers) {
        if (verifyUsers == null) {
            return null;
        }
        List<String> userNames = Arrays.stream(verifyUsers.split(","))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userNames)) {
            return verifyUsers;
        }
        StringBuffer realNames = new StringBuffer();
        userNames.forEach(userName -> {
            realNames.append(getRealNameByUserName(userName)).append(",");
        });
        realNames.deleteCharAt(realNames.length() - 1);
        return realNames.toString();
    }


    /**
     * 设置工作流审核人全称
     *
     * @param result
     * @param historicInfo
     * @return
     */
    protected List<HistoricActivityInstance> setAssignRealNames(Map<String, Object> result, Map<String, Object> historicInfo) {
        ArrayList<AssigneeVo> assigneeVos = new ArrayList<>();

        List<HistoricActivityInstance> historicActivityInstance = (List<HistoricActivityInstance>) historicInfo
                .get("historicActivityInstance");
        if (CollectionUtils.isNotEmpty(historicActivityInstance)) {
            for (HistoricActivityInstance historicActivityInstanceInfo : historicActivityInstance) {
                if (StringUtil.isBlank(historicActivityInstanceInfo.getAssignee())) {
                    continue;
                }
                AssigneeVo assigneeVo = new AssigneeVo();
                assigneeVo.setAssignee(historicActivityInstanceInfo.getAssignee());
                assigneeVo.setRealName(getRealNameByUserName(historicActivityInstanceInfo.getAssignee()));
                assigneeVos.add(assigneeVo);
            }
        }
        ArrayList<AssigneeVo> assigneeVosResult = assigneeVos.stream()
                .filter(assigneeVo -> StringUtil.isNotBlank(assigneeVo.getAssignee()))
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(
                                Comparator.comparing(AssigneeVo::getAssignee))), ArrayList::new));
        result.put("assigneeVos", assigneeVosResult);
        return historicActivityInstance;
    }


    /**
     * 订单审核记录封装
     *
     * @param data
     * @return
     */
    private List<AuditRecordDto> bindAuditRecordData(Map<String, Object> data) {
        List<AuditRecordDto> result = new ArrayList<>();

        List<HistoricActivityInstance> historicActivityInstance = (List<HistoricActivityInstance>) data.get("historicActivityInstance");

        String startUser = (String) data.get("startUser");
        String verifyUsers = (String) data.get("verifyUsers");
        ArrayList<AssigneeVo> assigneeVos = (ArrayList<AssigneeVo>) data.get("assigneeVos");
        Map commentMap = (Map) data.get("commentMap");
        if (CollectionUtils.isNotEmpty(historicActivityInstance)) {
            for (int i = 0; i < historicActivityInstance.size(); i++) {
                HistoricActivityInstance c = historicActivityInstance.get(i);

                // 操作人
                if (StringUtils.isNotEmpty(c.getActivityName())) {
                    AuditRecordDto auditRecordDto = new AuditRecordDto();
                    if ("startEvent".equals(c.getActivityType())) {
                        auditRecordDto.setOperator(startUser == null ? "" : startUser);
                    } else if ("intermediateThrowEvent".equals(c.getActivityType())) {
                        auditRecordDto.setOperator("");
                    } else {

                        if (Objects.isNull(c.getEndTime())) {
                            auditRecordDto.setOperator(verifyUsers);
                        } else {
                            auditRecordSetOperator(assigneeVos, c, auditRecordDto);

                        }
                    }

                    // 时间
                    auditRecordDto.setOperationTime(c.getEndTime());

                    // 操作
                    if ("startEvent".equals(c.getActivityType())) {
                        auditRecordDto.setOperation("开始");
                    } else if ("intermediateThrowEvent".equals(c.getActivityType())) {
                        auditRecordDto.setOperation("结束");
                    } else {
                        auditRecordDto.setOperation(c.getActivityName() == null ? "" : c.getActivityName());
                    }

                    // 备注
                    String remark = (String) commentMap.get(c.getTaskId());
                    auditRecordDto.setRemark(remark == null ? "" : remark);
                    result.add(auditRecordDto);
                }
            }
        }

        return result;
    }

    private void auditRecordSetOperator(ArrayList<AssigneeVo> assigneeVos, HistoricActivityInstance c, AuditRecordDto auditRecordDto) {
        BuyOrderExpenseVerifyController.auditRecordSetOperator(assigneeVos, c, auditRecordDto);
    }
}
