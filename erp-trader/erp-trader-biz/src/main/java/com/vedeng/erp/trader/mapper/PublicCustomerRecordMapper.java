package com.vedeng.erp.trader.mapper;

import com.vedeng.erp.business.dto.PublicCustomerRecordDto;
import com.vedeng.erp.trader.domain.PublicCustomerRecord;
import com.vedeng.erp.trader.domain.dto.TraderCustomerDto;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;
import java.util.List;

/**
 * 客户进入公海记录Mapper
 * <AUTHOR>
 */
@Named("publicCustomerRecordMapper")
public interface PublicCustomerRecordMapper {
    int deleteByPrimaryKey(Long publicCustomerRecordId);

    int insert(PublicCustomerRecord record);

    int insertSelective(PublicCustomerRecord record);

    PublicCustomerRecord selectByPrimaryKey(Long publicCustomerRecordId);

    int updateByPrimaryKeySelective(PublicCustomerRecord record);

    int updateByPrimaryKey(PublicCustomerRecord record);

    /**
     * 批量保存公海记录信息
     * @param traderCustomerDtoList
     * @return
     */
    int batchSavePublicCustomerRecords(@Param("traderCustomerDtoList") List<TraderCustomerDto> traderCustomerDtoList);

    /**
     * 检索保护期内客户ID集合
     * @return
     */
    List<Integer> getProtectedCustomerIds();

    /**
     * <AUTHOR>
     * @desc 根据进入公海的客户ID查询公海记录表
     * @param traderCustomerId
     * @return
     */
    PublicCustomerRecord queryInfoByCustId(Integer traderCustomerId);

    Integer getUnPrivatizedByCustomerId(@Param("traderCustomerId") Integer traderCustomerId);


    void unlockPublicCustomer(@Param("traderCustomerIdList") List<Integer> traderCustomerIdList, @Param("isUnlock") Integer isUnlock, @Param(
            "unlockRelatedId") Integer unlockRelatedId);

    List<PublicCustomerRecord> getPublicCustomerRecordListByTraderCustomerId(@Param("traderCustomerId") Integer traderCustomerId);

    /**
     * 判断客户是否处于锁定并且未解锁成功的状态
     * @param traderCustomerIdList 客户集合
     * @return 处于锁定并且未解锁成功状态的客户集合
     */
    List<Integer> checkTraderCustomerIsPrivatizedAndNotUnlocked(@Param("traderCustomerIdList") List<Integer> traderCustomerIdList);
    
    PublicCustomerRecordDto getByTraderCustomerId(@Param("traderCustomerId") Integer traderCustomerId,@Param("traderId") Integer traderId,@Param("traderName") String traderName);
    
    List<PublicCustomerRecordDto> getMaxRecordList(@Param("traderIdList") List<Integer> traderIdList);
    
    Integer getPublicCustomerNum(@Param("userId") Integer userId,@Param("areaId") Integer areaId);
    
    List<PublicCustomerRecordDto> getPublicCustomerList(@Param("userId") Integer userId,@Param("areaId") Integer areaId);
    
    void updateOrgUserId(@Param("ids") List<Long> ids, @Param("originUserId") Integer originUserId);
}
