<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.logistics.dao.WarehousePickingMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.logistics.model.WarehousePicking" >
    <id column="WAREHOUSE_PICKING_ID" property="warehousePickingId" jdbcType="INTEGER" />
    <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
    <result column="ORDER_TYPE" property="orderType" jdbcType="INTEGER" />
    <result column="ORDER_ID" property="orderId" jdbcType="INTEGER" />
    <result column="IS_ENABLE" property="isEnable" jdbcType="BIT" />
    <result column="PICKING_STATUS" property="pickingStatus" jdbcType="BIT" />
    <result column="PICKING_STATUS_TIME" property="pickingStatusTime" jdbcType="BIGINT" />
    <result column="COMMENTS" property="comments" jdbcType="VARCHAR" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
  </resultMap>
  
  <resultMap type="com.vedeng.logistics.model.WarehousePicking" id="VoResultMap" extends="BaseResultMap">
   <id column="WAREHOUSE_PICKING_DETAIL_ID" property="warehousePickingDetailId" jdbcType="INTEGER" />
  	<result column="GOODS_NAME" property="goodsName" jdbcType="VARCHAR" />
    <result column="BRAND_NAME" property="brandName" jdbcType="VARCHAR" />
    <result column="MODEL" property="model" jdbcType="VARCHAR" />
    <result column="MATERIAL_CODE" property="materialCode" jdbcType="VARCHAR" />
    <result column="UNIT_NAME" property="unitName" jdbcType="VARCHAR" />
    <result column="NUM" property="num" jdbcType="INTEGER" />
    <result column="PICK_TIME" property="pickTime" jdbcType="VARCHAR" />
    <result column="OPERATOR" property="operator" jdbcType="VARCHAR" />
    <result column="SKU" property="sku" jdbcType="VARCHAR" />
    <result column="GOODS_ID" property="goodsId" jdbcType="INTEGER" />
     <result column="CNT" property="cnt" jdbcType="INTEGER" />
     <result column="BARCODE_ID" property="barcodeId" jdbcType="INTEGER" />
  </resultMap>
  
  <sql id="Base_Column_List" >
    WAREHOUSE_PICKING_ID, COMPANY_ID, ORDER_TYPE, ORDER_ID, IS_ENABLE, PICKING_STATUS, 
    PICKING_STATUS_TIME, COMMENTS, ADD_TIME, CREATOR, MOD_TIME, UPDATER
  </sql>

    <select id="selectByPrimaryKey" resultMap="VoResultMap" parameterType="com.vedeng.logistics.model.WarehousePickingDetail">
        select WAREHOUSE_PICKING_DETAIL_ID,GOODS_ID,WAREHOUSE_PICKING_ID from T_WAREHOUSE_PICKING_DETAIL
        where IS_ENABLE=1
            and WAREHOUSE_PICKING_DETAIL_ID=#{warehousePickingDetailId}
        <if test="goodsId!=null">
            and GOODS_ID=#{goodsId}
        </if>
    </select>
  <!-- 当前订单下的拣货清单 -->
  <select id="getWarehousePickList" resultMap="VoResultMap" parameterType="com.vedeng.order.model.Saleorder" >
	     SELECT
	  	c.GOODS_ID,
		c.GOODS_NAME,
		c.BRAND_ID,
		d.BRAND_NAME,
		c.MODEL,
		c.MATERIAL_CODE,
		e.UNIT_NAME,
		b.NUM,
		c.SKU,
		a.MOD_TIME,
		a.UPDATER,
		a.CREATOR,
		a.ADD_TIME,
		a.IS_ENABLE,
		a.WAREHOUSE_PICKING_ID,
		b.WAREHOUSE_PICKING_DETAIL_ID,
	   <!--  g.BARCODE_ID, -->
	 IFNULL(SUM(0-f.NUM),0) CNT
		FROM
		T_WAREHOUSE_PICKING a
		LEFT JOIN T_WAREHOUSE_PICKING_DETAIL b ON a.WAREHOUSE_PICKING_ID = b.WAREHOUSE_PICKING_ID AND b.IS_ENABLE=1
		<!-- LEFT JOIN T_WAREHOUSE_GOODS_OPERATE_LOG g ON  b.WAREHOUSE_PICKING_DETAIL_ID = g.WAREHOUSE_PICKING_DETAIL_ID
		AND g.OPERATE_TYPE IN (1,3,5,8,9) AND g.IS_ENABLE =1 -->
		LEFT JOIN T_GOODS c ON b.GOODS_ID = c.GOODS_ID
		LEFT JOIN T_BRAND d ON c.BRAND_ID = d.BRAND_ID
		LEFT JOIN T_UNIT e ON c.UNIT_ID = e.UNIT_ID
	  LEFT JOIN T_WAREHOUSE_GOODS_OPERATE_LOG f ON b.WAREHOUSE_PICKING_DETAIL_ID = f.WAREHOUSE_PICKING_DETAIL_ID
	 <!--  <if test="bussinessType == 1" >
        AND f.OPERATE_TYPE=4 
      </if>
      <if test="bussinessType == 0" >
        AND f.OPERATE_TYPE=2
      </if> -->
      AND f.OPERATE_TYPE= #{bussinessType,jdbcType=INTEGER}
	  AND f.IS_ENABLE =1
		where 1=1
	  <if test="bussinessType!=2" >
       AND a.ORDER_ID = #{bussinessId,jdbcType=INTEGER}
       AND a.ORDER_TYPE = 1
      </if>
      <if test="bussinessType == 2" >
       AND a.ORDER_ID = #{saleorderId,jdbcType=INTEGER}
       AND a.ORDER_TYPE = 2
      </if>
		AND a.IS_ENABLE =1
	GROUP BY b.WAREHOUSE_PICKING_DETAIL_ID
  </select>
  <!-- 保存拣货订单 -->
  
  <insert id="savePickOrder" parameterType="com.vedeng.logistics.model.WarehousePicking" >
    insert into T_WAREHOUSE_PICKING
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      <if test="orderType != null" >
        ORDER_TYPE,
      </if>
      <if test="orderId != null" >
        ORDER_ID,
      </if>
      <if test="isEnable != null" >
        IS_ENABLE,
      </if>
      <if test="pickingStatus != null" >
        PICKING_STATUS,
      </if>
      <if test="pickingStatusTime != null" >
        PICKING_STATUS_TIME,
      </if>
      <if test="comments != null" >
        COMMENTS,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="companyId != null" >
         #{companyId,jdbcType=INTEGER},
      </if>
      <if test="orderType != null" >
        #{orderType,jdbcType=BIT},
      </if>
      <if test="orderId != null" >
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null" >
        #{isEnable,jdbcType=INTEGER},
      </if>
      <if test="pickingStatus != null" >
        #{pickingStatus,jdbcType=VARCHAR},
      </if>
      <if test="pickingStatusTime != null" >
        #{pickingStatusTime,jdbcType=VARCHAR},
      </if>
      <if test="comments != null" >
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
     <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="warehousePickingId">
		SELECT LAST_INSERT_ID() AS warehousePickingId
    </selectKey>
  </insert>
  <!-- 根据订单id查询拣货清单 -->
   <select id="getPickOrder" resultMap="VoResultMap" parameterType="int" >
    select * from T_WAREHOUSE_PICKING where ORDER_ID=#{saleorderId,jdbcType=INTEGER} AND a.ORDER_TYPE = 2
  </select>
  <!-- 更新拣货订单 -->
  <update id="upPickOrder" parameterType="com.vedeng.logistics.model.WarehousePicking" >
    update T_WAREHOUSE_PICKING
    set COMPANY_ID = #{companyId,jdbcType=INTEGER},
      ORDER_TYPE = #{orderType,jdbcType=INTEGER},
      ORDER_ID = #{orderId,jdbcType=INTEGER},
      IS_ENABLE = #{isEnable,jdbcType=BIT},
      PICKING_STATUS = #{pickingStatus,jdbcType=BIT},
      PICKING_STATUS_TIME = #{pickingStatusTime,jdbcType=BIGINT},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER}
    where ORDER_ID = #{orderId,jdbcType=INTEGER}
  </update>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_WAREHOUSE_PICKING
    where WAREHOUSE_PICKING_ID = #{warehousePickingId,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.vedeng.logistics.model.WarehousePicking" >
    insert into T_WAREHOUSE_PICKING
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="warehousePickingId != null" >
        WAREHOUSE_PICKING_ID,
      </if>
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      <if test="orderType != null" >
        ORDER_TYPE,
      </if>
      <if test="orderId != null" >
        ORDER_ID,
      </if>
      <if test="isEnable != null" >
        IS_ENABLE,
      </if>
      <if test="pickingStatus != null" >
        PICKING_STATUS,
      </if>
      <if test="pickingStatusTime != null" >
        PICKING_STATUS_TIME,
      </if>
      <if test="comments != null" >
        COMMENTS,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="warehousePickingId != null" >
        #{warehousePickingId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="orderType != null" >
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="orderId != null" >
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="isEnable != null" >
        #{isEnable,jdbcType=BIT},
      </if>
      <if test="pickingStatus != null" >
        #{pickingStatus,jdbcType=BIT},
      </if>
      <if test="pickingStatusTime != null" >
        #{pickingStatusTime,jdbcType=BIGINT},
      </if>
      <if test="comments != null" >
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <!-- 撤销拣货记录 -->
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.logistics.model.WarehousePicking" >
    update T_WAREHOUSE_PICKING
    <set >
      <if test="companyId != null" >
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="orderType != null" >
        ORDER_TYPE = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="orderId != null" >
        ORDER_ID = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="isEnable != null" >
        IS_ENABLE = #{isEnable,jdbcType=BIT},
      </if>
      <if test="pickingStatus != null" >
        PICKING_STATUS = #{pickingStatus,jdbcType=BIT},
      </if>
      <if test="pickingStatusTime != null" >
        PICKING_STATUS_TIME = #{pickingStatusTime,jdbcType=BIGINT},
      </if>
      <if test="comments != null" >
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where WAREHOUSE_PICKING_ID = #{warehousePickingId,jdbcType=INTEGER}
  </update>
  <!-- 打印拣货单 -->
  <resultMap type="com.vedeng.logistics.model.WarehousePicking" id="pickingResultMap" extends="VoResultMap">
        <result column="STORAGEADDRESS" property="warehouseArea" jdbcType="VARCHAR" />
	  	<collection property="pdList" javaType="list" ofType="com.vedeng.logistics.model.WarehousePickingDetail">
	  		<id column="WAREHOUSE_PICKING_DETAIL_ID" property="warehousePickingDetailId" jdbcType="INTEGER" />
		    <result column="WAREHOUSE_PICKING_ID" property="warehousePickingId" jdbcType="INTEGER" />
		    <result column="WAREHOUSE_GOODS_OPERATE_LOG_ID" property="warehouseGoodsOperateLogId" jdbcType="INTEGER" />
		    <result column="GOODS_ID" property="goodsId" jdbcType="INTEGER" />
		    <result column="NUM" property="num" jdbcType="INTEGER" />
		    
		    <result column="EXPIRATION_DATE" property="expirationDate" jdbcType="BIGINT" />
		    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
		    <result column="BATCH_NUMBER" property="batchNumber" jdbcType="VARCHAR" />
		    <result column="STORAGEADDRESS" property="warehouseArea" jdbcType="VARCHAR" />
		    <result column="L_COMMENTS" property="comments" jdbcType="VARCHAR" />
	  	</collection>
  </resultMap>
  <!-- 根据id获取拣货记录 -->
  <select id="getWarehousepickListById" resultMap="pickingResultMap" parameterType="com.vedeng.logistics.model.WarehousePicking">
     SELECT
		a.WAREHOUSE_PICKING_ID,
		b.WAREHOUSE_PICKING_DETAIL_ID,
		d.GOODS_ID,
			d.GOODS_NAME,
			e.BRAND_NAME,
			d.MODEL,
			d.MATERIAL_CODE,
			b.NUM,
			c.EXPIRATION_DATE,
			c.ADD_TIME,
			c.BATCH_NUMBER,
			c.COMMENTS L_COMMENTS,
			concat(
				g.WAREHOUSE_NAME,
				' ',
				IFNULL(h.STORAGE_ROOM_NAME, ''),
				' ',
				IFNULL(i.STORAGE_AREA_NAME, ''),
				' ',
				IFNULL(j.STORAGE_RACK_NAME, ''),
				' ',
				IFNULL(k.STORAGE_LOCATION_NAME, '')
			) AS STORAGEADDRESS
		FROM
			T_WAREHOUSE_PICKING a
		LEFT JOIN T_WAREHOUSE_PICKING_DETAIL b ON a.WAREHOUSE_PICKING_ID = b.WAREHOUSE_PICKING_ID
		INNER JOIN T_WAREHOUSE_GOODS_OPERATE_LOG c ON b.WAREHOUSE_GOODS_OPERATE_LOG_ID = c.WAREHOUSE_GOODS_OPERATE_LOG_ID
		AND c.OPERATE_TYPE = 1
		LEFT JOIN T_GOODS d ON c.GOODS_ID = d.GOODS_ID
		LEFT JOIN T_BRAND e ON d.BRAND_ID = e.BRAND_ID
		LEFT JOIN T_WAREHOUSE g ON c.WAREHOUSE_ID = g.WAREHOUSE_ID
		LEFT JOIN T_STORAGE_ROOM h ON c.STORAGE_ROOM_ID = h.STORAGE_ROOM_ID
		LEFT JOIN T_STORAGE_AREA i ON c.STORAGE_AREA_ID = i.STORAGE_AREA_ID
		LEFT JOIN T_STORAGE_RACK j ON c.STORAGE_RACK_ID = j.STORAGE_RACK_ID
		LEFT JOIN T_STORAGE_LOCATION k ON c.STORAGE_LOCATION_ID = k.STORAGE_LOCATION_ID
		LEFT JOIN T_BUYORDER_GOODS l ON l.BUYORDER_GOODS_ID = c.RELATED_ID
		LEFT JOIN T_BUYORDER m ON l.BUYORDER_ID = m.BUYORDER_ID
		WHERE
			a.WAREHOUSE_PICKING_ID =  #{warehousePickingId,jdbcType=INTEGER}
		GROUP BY
			c.ADD_TIME,
			c.BATCH_NUMBER,
			c.EXPIRATION_DATE,
			m.BUYORDER_NO
  </select>
  
  <!-- 当前订单下的拣货清单 -->
  <select id="getWarehouseLendOutPickList" resultMap="VoResultMap" parameterType="com.vedeng.order.model.Saleorder" >
	     SELECT
	  	c.GOODS_ID,
		c.GOODS_NAME,
		c.BRAND_ID,
		d.BRAND_NAME,
		c.MODEL,
		c.MATERIAL_CODE,
		e.UNIT_NAME,
		b.NUM,
		c.SKU,
		a.MOD_TIME,
		a.UPDATER,
		a.CREATOR,
		a.ADD_TIME,
		a.IS_ENABLE,
		a.WAREHOUSE_PICKING_ID,
		b.WAREHOUSE_PICKING_DETAIL_ID,
	 IFNULL(SUM(0-f.NUM),0) CNT
		FROM
		T_WAREHOUSE_PICKING a
		LEFT JOIN T_WAREHOUSE_PICKING_DETAIL b ON a.WAREHOUSE_PICKING_ID = b.WAREHOUSE_PICKING_ID AND b.IS_ENABLE=1
		LEFT JOIN T_GOODS c ON b.GOODS_ID = c.GOODS_ID
		LEFT JOIN T_BRAND d ON c.BRAND_ID = d.BRAND_ID
		LEFT JOIN T_UNIT e ON c.UNIT_ID = e.UNIT_ID
	  LEFT JOIN T_WAREHOUSE_GOODS_OPERATE_LOG f ON b.WAREHOUSE_PICKING_DETAIL_ID = f.WAREHOUSE_PICKING_DETAIL_ID
      AND f.OPERATE_TYPE= #{bussinessType,jdbcType=INTEGER}
	  AND f.IS_ENABLE =1
		where 1=1
	  <if test="bussinessType!=2 and bussinessType!=3" >
       AND a.ORDER_ID = #{bussinessId,jdbcType=INTEGER}
       AND a.ORDER_TYPE = 1
      </if>
      <if test="bussinessType == 2" >
       AND a.ORDER_ID = #{saleorderId,jdbcType=INTEGER}
       AND a.ORDER_TYPE = 2
      </if>
      <if test="bussinessType == 3" >
       AND a.ORDER_ID = #{bussinessId,jdbcType=INTEGER}
       AND a.ORDER_TYPE = 3
      </if>
		AND a.IS_ENABLE =1
	GROUP BY b.WAREHOUSE_PICKING_DETAIL_ID
  </select>
  <select id="getLendOutPickCnt" resultType="java.lang.Integer">
	SELECT
	IFNULL(SUM(b.NUM),0) PICKCNT
	FROM
	`T_WAREHOUSE_PICKING` a
	LEFT JOIN `T_WAREHOUSE_PICKING_DETAIL` b
	ON a.`WAREHOUSE_PICKING_ID` = b.`WAREHOUSE_PICKING_DETAIL_ID`
	AND b.`IS_ENABLE` = 1 AND b.`GOODS_ID` = #{goodsId,jdbcType=INTEGER}
	WHERE a.`ORDER_ID` = #{lendOutId,jdbcType=INTEGER} AND a.`IS_ENABLE` = 1 AND a.`ORDER_TYPE` = #{orderType,jdbcType=INTEGER}
  </select>
</mapper>