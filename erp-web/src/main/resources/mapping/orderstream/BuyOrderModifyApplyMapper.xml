<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.order.dao.BuyOrderModifyApplyMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.erp.buyorder.dto.BuyOrderModifyApply">
            <id property="buyorderModifyApplyId" column="BUYORDER_MODIFY_APPLY_ID" jdbcType="INTEGER"/>
            <result property="buyorderModifyApplyNo" column="BUYORDER_MODIFY_APPLY_NO" jdbcType="VARCHAR"/>
            <result property="companyId" column="COMPANY_ID" jdbcType="INTEGER"/>
            <result property="buyorderId" column="BUYORDER_ID" jdbcType="INTEGER"/>
            <result property="validStatus" column="VALID_STATUS" jdbcType="BOOLEAN"/>
            <result property="validTime" column="VALID_TIME" jdbcType="BIGINT"/>
            <result property="deliveryDirect" column="DELIVERY_DIRECT" jdbcType="BOOLEAN"/>
            <result property="takeTraderId" column="TAKE_TRADER_ID" jdbcType="INTEGER"/>
            <result property="takeTraderName" column="TAKE_TRADER_NAME" jdbcType="VARCHAR"/>
            <result property="takeTraderContactId" column="TAKE_TRADER_CONTACT_ID" jdbcType="INTEGER"/>
            <result property="takeTraderContactName" column="TAKE_TRADER_CONTACT_NAME" jdbcType="VARCHAR"/>
            <result property="takeTraderContactMobile" column="TAKE_TRADER_CONTACT_MOBILE" jdbcType="VARCHAR"/>
            <result property="takeTraderContactTelephone" column="TAKE_TRADER_CONTACT_TELEPHONE" jdbcType="VARCHAR"/>
            <result property="takeTraderAddressId" column="TAKE_TRADER_ADDRESS_ID" jdbcType="INTEGER"/>
            <result property="takeTraderArea" column="TAKE_TRADER_AREA" jdbcType="VARCHAR"/>
            <result property="takeTraderAddress" column="TAKE_TRADER_ADDRESS" jdbcType="VARCHAR"/>
            <result property="logisticsComments" column="LOGISTICS_COMMENTS" jdbcType="VARCHAR"/>
            <result property="invoiceType" column="INVOICE_TYPE" jdbcType="INTEGER"/>
            <result property="invoiceComments" column="INVOICE_COMMENTS" jdbcType="VARCHAR"/>
            <result property="oldDeliveryDirect" column="OLD_DELIVERY_DIRECT" jdbcType="BOOLEAN"/>
            <result property="oldTakeTraderId" column="OLD_TAKE_TRADER_ID" jdbcType="INTEGER"/>
            <result property="oldTakeTraderName" column="OLD_TAKE_TRADER_NAME" jdbcType="VARCHAR"/>
            <result property="oldTakeTraderContactId" column="OLD_TAKE_TRADER_CONTACT_ID" jdbcType="INTEGER"/>
            <result property="oldTakeTraderContactName" column="OLD_TAKE_TRADER_CONTACT_NAME" jdbcType="VARCHAR"/>
            <result property="oldTakeTraderContactMobile" column="OLD_TAKE_TRADER_CONTACT_MOBILE" jdbcType="VARCHAR"/>
            <result property="oldTakeTraderContactTelephone" column="OLD_TAKE_TRADER_CONTACT_TELEPHONE" jdbcType="VARCHAR"/>
            <result property="oldTakeTraderAddressId" column="OLD_TAKE_TRADER_ADDRESS_ID" jdbcType="INTEGER"/>
            <result property="oldTakeTraderArea" column="OLD_TAKE_TRADER_AREA" jdbcType="VARCHAR"/>
            <result property="oldTakeTraderAddress" column="OLD_TAKE_TRADER_ADDRESS" jdbcType="VARCHAR"/>
            <result property="oldLogisticsComments" column="OLD_LOGISTICS_COMMENTS" jdbcType="VARCHAR"/>
            <result property="oldInvoiceType" column="OLD_INVOICE_TYPE" jdbcType="INTEGER"/>
            <result property="oldInvoiceComments" column="OLD_INVOICE_COMMENTS" jdbcType="VARCHAR"/>
            <result property="addTime" column="ADD_TIME" jdbcType="BIGINT"/>
            <result property="creator" column="CREATOR" jdbcType="INTEGER"/>
            <result property="synWmsCancel" column="SYN_WMS_CANCEL" jdbcType="INTEGER"/>
            <result property="deliveryDirectChangeReason" column="DELIVERY_DIRECT_CHANGE_REASON" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        BUYORDER_MODIFY_APPLY_ID,BUYORDER_MODIFY_APPLY_NO,COMPANY_ID,
        BUYORDER_ID,VALID_STATUS,VALID_TIME,
        DELIVERY_DIRECT,TAKE_TRADER_ID,TAKE_TRADER_NAME,
        TAKE_TRADER_CONTACT_ID,TAKE_TRADER_CONTACT_NAME,TAKE_TRADER_CONTACT_MOBILE,
        TAKE_TRADER_CONTACT_TELEPHONE,TAKE_TRADER_ADDRESS_ID,TAKE_TRADER_AREA,
        TAKE_TRADER_ADDRESS,LOGISTICS_COMMENTS,INVOICE_TYPE,
        INVOICE_COMMENTS,OLD_DELIVERY_DIRECT,OLD_TAKE_TRADER_ID,
        OLD_TAKE_TRADER_NAME,OLD_TAKE_TRADER_CONTACT_ID,OLD_TAKE_TRADER_CONTACT_NAME,
        OLD_TAKE_TRADER_CONTACT_MOBILE,OLD_TAKE_TRADER_CONTACT_TELEPHONE,OLD_TAKE_TRADER_ADDRESS_ID,
        OLD_TAKE_TRADER_AREA,OLD_TAKE_TRADER_ADDRESS,OLD_LOGISTICS_COMMENTS,
        OLD_INVOICE_TYPE,OLD_INVOICE_COMMENTS,ADD_TIME,
        CREATOR,SYN_WMS_CANCEL,DELIVERY_DIRECT_CHANGE_REASON,IS_NEW
    </sql>
    <select id="selectSingleById" resultType="com.vedeng.erp.buyorder.dto.BuyOrderModifyApply">
        SELECT
            <include refid="Base_Column_List"></include>
        FROM
            T_BUYORDER_MODIFY_APPLY a
        WHERE
            a.BUYORDER_MODIFY_APPLY_ID = #{buyOrderModifyApplyId,jdbcType=INTEGER}

    </select>
    <select id="selectRelatedSaleOrderInfoById" resultType="com.vedeng.order.model.vo.SaleorderVo">
        SELECT
            distinct f.*
        FROM
            T_BUYORDER_MODIFY_APPLY a
            LEFT JOIN T_BUYORDER b on a.BUYORDER_ID = b.BUYORDER_ID
            LEFT JOIN T_BUYORDER_GOODS c on b.BUYORDER_ID = c.BUYORDER_ID
            LEFT JOIN T_R_BUYORDER_J_SALEORDER d on c.BUYORDER_GOODS_ID = d.BUYORDER_GOODS_ID
            LEFT JOIN T_SALEORDER_GOODS e on d.SALEORDER_GOODS_ID = e.SALEORDER_GOODS_ID
            LEFT JOIN T_SALEORDER f on e.SALEORDER_ID = f.SALEORDER_ID
        WHERE
            a.BUYORDER_MODIFY_APPLY_ID = #{buyOrderModifyApplyId,jdbcType=INTEGER} AND f.SALEORDER_ID IS NOT NULL
    </select>
    <select id="selectRelatedBuyOrderInfoById" resultType="com.vedeng.order.model.vo.BuyorderVo">
        SELECT
            distinct b.*
        FROM
            T_BUYORDER_MODIFY_APPLY a
            LEFT JOIN T_BUYORDER b on a.BUYORDER_ID = b.BUYORDER_ID
        WHERE
            a.BUYORDER_MODIFY_APPLY_ID = #{buyOrderModifyApplyId,jdbcType=INTEGER}
    </select>


    <insert id="insertSelective" parameterType="com.vedeng.order.model.BuyorderModifyApply" >
        insert into T_BUYORDER_MODIFY_APPLY
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="buyorderModifyApplyId != null" >
                BUYORDER_MODIFY_APPLY_ID,
            </if>
            <if test="buyorderModifyApplyNo != null" >
                BUYORDER_MODIFY_APPLY_NO,
            </if>
            <if test="companyId != null" >
                COMPANY_ID,
            </if>
            <if test="buyorderId != null" >
                BUYORDER_ID,
            </if>
            <if test="validStatus != null" >
                VALID_STATUS,
            </if>
            <if test="validTime != null" >
                VALID_TIME,
            </if>
            <if test="deliveryDirect != null" >
                DELIVERY_DIRECT,
            </if>
            <if test="takeTraderId != null" >
                TAKE_TRADER_ID,
            </if>
            <if test="takeTraderName != null" >
                TAKE_TRADER_NAME,
            </if>
            <if test="takeTraderContactId != null" >
                TAKE_TRADER_CONTACT_ID,
            </if>
            <if test="takeTraderContactName != null" >
                TAKE_TRADER_CONTACT_NAME,
            </if>
            <if test="takeTraderContactMobile != null" >
                TAKE_TRADER_CONTACT_MOBILE,
            </if>
            <if test="takeTraderContactTelephone != null" >
                TAKE_TRADER_CONTACT_TELEPHONE,
            </if>
            <if test="takeTraderAddressId != null" >
                TAKE_TRADER_ADDRESS_ID,
            </if>
            <if test="takeTraderArea != null" >
                TAKE_TRADER_AREA,
            </if>
            <if test="takeTraderAddress != null" >
                TAKE_TRADER_ADDRESS,
            </if>
            <if test="logisticsComments != null" >
                LOGISTICS_COMMENTS,
            </if>
            <if test="invoiceType != null" >
                INVOICE_TYPE,
            </if>
            <if test="invoiceComments != null" >
                INVOICE_COMMENTS,
            </if>
            <if test="oldDeliveryDirect != null" >
                OLD_DELIVERY_DIRECT,
            </if>
            <if test="oldTakeTraderId != null" >
                OLD_TAKE_TRADER_ID,
            </if>
            <if test="oldTakeTraderName != null" >
                OLD_TAKE_TRADER_NAME,
            </if>
            <if test="oldTakeTraderContactId != null" >
                OLD_TAKE_TRADER_CONTACT_ID,
            </if>
            <if test="oldTakeTraderContactName != null" >
                OLD_TAKE_TRADER_CONTACT_NAME,
            </if>
            <if test="oldTakeTraderContactMobile != null" >
                OLD_TAKE_TRADER_CONTACT_MOBILE,
            </if>
            <if test="oldTakeTraderContactTelephone != null" >
                OLD_TAKE_TRADER_CONTACT_TELEPHONE,
            </if>
            <if test="oldTakeTraderAddressId != null" >
                OLD_TAKE_TRADER_ADDRESS_ID,
            </if>
            <if test="oldTakeTraderArea != null" >
                OLD_TAKE_TRADER_AREA,
            </if>
            <if test="oldTakeTraderAddress != null" >
                OLD_TAKE_TRADER_ADDRESS,
            </if>
            <if test="oldLogisticsComments != null" >
                OLD_LOGISTICS_COMMENTS,
            </if>
            <if test="oldInvoiceType != null" >
                OLD_INVOICE_TYPE,
            </if>
            <if test="oldInvoiceComments != null" >
                OLD_INVOICE_COMMENTS,
            </if>

            <if test="synWmsCancel != null" >
                SYN_WMS_CANCEL,
            </if>
            <if test="addTime != null" >
                ADD_TIME,
            </if>
            <if test="creator != null" >
                CREATOR,
            </if>
            <if test="deliveryDirectChangeReason != null">
                DELIVERY_DIRECT_CHANGE_REASON,
            </if>
            <if test="totalAmount != null">
                TOTAL_AMOUNT,
            </if>
            <if test="oldTotalAmount != null">
                OLD_TOTAL_AMOUNT,
            </if>
            <if test="isNew != null">
                IS_NEW,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="buyorderModifyApplyId != null" >
                #{buyorderModifyApplyId,jdbcType=INTEGER},
            </if>
            <if test="buyorderModifyApplyNo != null" >
                #{buyorderModifyApplyNo,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null" >
                #{companyId,jdbcType=INTEGER},
            </if>
            <if test="buyorderId != null" >
                #{buyorderId,jdbcType=INTEGER},
            </if>
            <if test="validStatus != null" >
                #{validStatus,jdbcType=BIT},
            </if>
            <if test="validTime != null" >
                #{validTime,jdbcType=BIGINT},
            </if>
            <if test="deliveryDirect != null" >
                #{deliveryDirect,jdbcType=BIT},
            </if>
            <if test="takeTraderId != null" >
                #{takeTraderId,jdbcType=INTEGER},
            </if>
            <if test="takeTraderName != null" >
                #{takeTraderName,jdbcType=VARCHAR},
            </if>
            <if test="takeTraderContactId != null" >
                #{takeTraderContactId,jdbcType=INTEGER},
            </if>
            <if test="takeTraderContactName != null" >
                #{takeTraderContactName,jdbcType=VARCHAR},
            </if>
            <if test="takeTraderContactMobile != null" >
                #{takeTraderContactMobile,jdbcType=VARCHAR},
            </if>
            <if test="takeTraderContactTelephone != null" >
                #{takeTraderContactTelephone,jdbcType=VARCHAR},
            </if>
            <if test="takeTraderAddressId != null" >
                #{takeTraderAddressId,jdbcType=INTEGER},
            </if>
            <if test="takeTraderArea != null" >
                #{takeTraderArea,jdbcType=VARCHAR},
            </if>
            <if test="takeTraderAddress != null" >
                #{takeTraderAddress,jdbcType=VARCHAR},
            </if>
            <if test="logisticsComments != null" >
                #{logisticsComments,jdbcType=VARCHAR},
            </if>
            <if test="invoiceType != null" >
                #{invoiceType,jdbcType=INTEGER},
            </if>
            <if test="invoiceComments != null" >
                #{invoiceComments,jdbcType=VARCHAR},
            </if>
            <if test="oldDeliveryDirect != null" >
                #{oldDeliveryDirect,jdbcType=BIT},
            </if>
            <if test="oldTakeTraderId != null" >
                #{oldTakeTraderId,jdbcType=INTEGER},
            </if>
            <if test="oldTakeTraderName != null" >
                #{oldTakeTraderName,jdbcType=VARCHAR},
            </if>
            <if test="oldTakeTraderContactId != null" >
                #{oldTakeTraderContactId,jdbcType=INTEGER},
            </if>
            <if test="oldTakeTraderContactName != null" >
                #{oldTakeTraderContactName,jdbcType=VARCHAR},
            </if>
            <if test="oldTakeTraderContactMobile != null" >
                #{oldTakeTraderContactMobile,jdbcType=VARCHAR},
            </if>
            <if test="oldTakeTraderContactTelephone != null" >
                #{oldTakeTraderContactTelephone,jdbcType=VARCHAR},
            </if>
            <if test="oldTakeTraderAddressId != null" >
                #{oldTakeTraderAddressId,jdbcType=INTEGER},
            </if>
            <if test="oldTakeTraderArea != null" >
                #{oldTakeTraderArea,jdbcType=VARCHAR},
            </if>
            <if test="oldTakeTraderAddress != null" >
                #{oldTakeTraderAddress,jdbcType=VARCHAR},
            </if>
            <if test="oldLogisticsComments != null" >
                #{oldLogisticsComments,jdbcType=VARCHAR},
            </if>
            <if test="oldInvoiceType != null" >
                #{oldInvoiceType,jdbcType=INTEGER},
            </if>
            <if test="oldInvoiceComments != null" >
                #{oldInvoiceComments,jdbcType=VARCHAR},
            </if>
            <if test="synWmsCancel != null" >
                #{synWmsCancel,jdbcType=INTEGER},
            </if>
            <if test="addTime != null" >
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null" >
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="deliveryDirectChangeReason != null">
                #{deliveryDirectChangeReason,jdbcType=VARCHAR},
            </if>
            <if test="totalAmount != null">
                #{totalAmount,jdbcType=DECIMAL},
            </if>
            <if test="oldTotalAmount != null">
                #{oldTotalAmount,jdbcType=DECIMAL},
            </if>
            <if test="isNew != null">
                #{isNew,jdbcType=INTEGER},
            </if>
        </trim>
        <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="buyorderModifyApplyId">
            SELECT LAST_INSERT_ID() AS buyorderModifyApplyId
        </selectKey>
    </insert>


    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.order.model.BuyorderModifyApply" >
        update T_BUYORDER_MODIFY_APPLY
        <set >
            <if test="buyorderModifyApplyNo != null" >
                BUYORDER_MODIFY_APPLY_NO = #{buyorderModifyApplyNo,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null" >
                COMPANY_ID = #{companyId,jdbcType=INTEGER},
            </if>
            <if test="buyorderId != null" >
                BUYORDER_ID = #{buyorderId,jdbcType=INTEGER},
            </if>
            <if test="validStatus != null" >
                VALID_STATUS = #{validStatus,jdbcType=BIT},
            </if>
            <if test="validTime != null" >
                VALID_TIME = #{validTime,jdbcType=BIGINT},
            </if>
            <if test="deliveryDirect != null" >
                DELIVERY_DIRECT = #{deliveryDirect,jdbcType=BIT},
            </if>
            <if test="takeTraderId != null" >
                TAKE_TRADER_ID = #{takeTraderId,jdbcType=INTEGER},
            </if>
            <if test="takeTraderName != null" >
                TAKE_TRADER_NAME = #{takeTraderName,jdbcType=VARCHAR},
            </if>
            <if test="takeTraderContactId != null" >
                TAKE_TRADER_CONTACT_ID = #{takeTraderContactId,jdbcType=INTEGER},
            </if>
            <if test="takeTraderContactName != null" >
                TAKE_TRADER_CONTACT_NAME = #{takeTraderContactName,jdbcType=VARCHAR},
            </if>
            <if test="takeTraderContactMobile != null" >
                TAKE_TRADER_CONTACT_MOBILE = #{takeTraderContactMobile,jdbcType=VARCHAR},
            </if>
            <if test="takeTraderContactTelephone != null" >
                TAKE_TRADER_CONTACT_TELEPHONE = #{takeTraderContactTelephone,jdbcType=VARCHAR},
            </if>
            <if test="takeTraderAddressId != null" >
                TAKE_TRADER_ADDRESS_ID = #{takeTraderAddressId,jdbcType=INTEGER},
            </if>
            <if test="takeTraderArea != null" >
                TAKE_TRADER_AREA = #{takeTraderArea,jdbcType=VARCHAR},
            </if>
            <if test="takeTraderAddress != null" >
                TAKE_TRADER_ADDRESS = #{takeTraderAddress,jdbcType=VARCHAR},
            </if>
            <if test="logisticsComments != null" >
                LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
            </if>
            <if test="invoiceType != null" >
                INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
            </if>
            <if test="invoiceComments != null" >
                INVOICE_COMMENTS = #{invoiceComments,jdbcType=VARCHAR},
            </if>
            <if test="oldDeliveryDirect != null" >
                OLD_DELIVERY_DIRECT = #{oldDeliveryDirect,jdbcType=BIT},
            </if>
            <if test="oldTakeTraderId != null" >
                OLD_TAKE_TRADER_ID = #{oldTakeTraderId,jdbcType=INTEGER},
            </if>
            <if test="oldTakeTraderName != null" >
                OLD_TAKE_TRADER_NAME = #{oldTakeTraderName,jdbcType=VARCHAR},
            </if>
            <if test="oldTakeTraderContactId != null" >
                OLD_TAKE_TRADER_CONTACT_ID = #{oldTakeTraderContactId,jdbcType=INTEGER},
            </if>
            <if test="oldTakeTraderContactName != null" >
                OLD_TAKE_TRADER_CONTACT_NAME = #{oldTakeTraderContactName,jdbcType=VARCHAR},
            </if>
            <if test="oldTakeTraderContactMobile != null" >
                OLD_TAKE_TRADER_CONTACT_MOBILE = #{oldTakeTraderContactMobile,jdbcType=VARCHAR},
            </if>
            <if test="oldTakeTraderContactTelephone != null" >
                OLD_TAKE_TRADER_CONTACT_TELEPHONE = #{oldTakeTraderContactTelephone,jdbcType=VARCHAR},
            </if>
            <if test="oldTakeTraderAddressId != null" >
                OLD_TAKE_TRADER_ADDRESS_ID = #{oldTakeTraderAddressId,jdbcType=INTEGER},
            </if>
            <if test="oldTakeTraderArea != null" >
                OLD_TAKE_TRADER_AREA = #{oldTakeTraderArea,jdbcType=VARCHAR},
            </if>
            <if test="oldTakeTraderAddress != null" >
                OLD_TAKE_TRADER_ADDRESS = #{oldTakeTraderAddress,jdbcType=VARCHAR},
            </if>
            <if test="oldLogisticsComments != null" >
                OLD_LOGISTICS_COMMENTS = #{oldLogisticsComments,jdbcType=VARCHAR},
            </if>
            <if test="oldInvoiceType != null" >
                OLD_INVOICE_TYPE = #{oldInvoiceType,jdbcType=INTEGER},
            </if>
            <if test="oldInvoiceComments != null" >
                OLD_INVOICE_COMMENTS = #{oldInvoiceComments,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null" >
                ADD_TIME = #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null" >
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="deliveryDirectChangeReason != null">
                DELIVERY_DIRECT_CHANGE_REASON = #{deliveryDirectChangeReason, jdbcType=VARCHAR},
            </if>
            <if test="totalAmount != null">
                TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
            </if>
            <if test="oldTotalAmount != null">
                #{oldTotalAmount,jdbcType=DECIMAL},
            </if>
        </set>
        where BUYORDER_MODIFY_APPLY_ID = #{buyorderModifyApplyId,jdbcType=INTEGER}
    </update>


</mapper>
