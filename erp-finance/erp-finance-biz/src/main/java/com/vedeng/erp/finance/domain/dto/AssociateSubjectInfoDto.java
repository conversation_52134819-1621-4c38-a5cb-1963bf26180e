package com.vedeng.erp.finance.domain.dto;

import lombok.Data;

import java.util.List;

@Data
public class AssociateSubjectInfoDto {

    private InvoiceRedConfirmationDto invoiceRedConfirmationDto;

    private List<AssociateSaleOrderDto> associateSaleOrderDtoList;

    @Data
    public static class AssociateSaleOrderDto {

        private Integer saleOrderId;

        private String saleOrderNo;

        private List<AssociateAfterOrderDto> associateAfterOrderDtoList;

    }

    @Data
    public static class AssociateAfterOrderDto {

        private Integer afterSalesId;

        private String afterSalesNo;
    }

}
