package com.vedeng.erp.settlement.mapper;

import com.vedeng.erp.finance.dto.BankBillDto;
import com.vedeng.erp.finance.dto.BankBillQueryDto;
import com.vedeng.erp.finance.dto.HongRuiBankBillQueryDto;
import com.vedeng.erp.settlement.domain.entity.BankBillEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2022/9/6 8:54
 **/
@Repository("HongRuiBankBillMapper")
public interface HongRuiBankBillMapper {
    /**
     * delete by primary key
     * @param bankBillId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer bankBillId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(BankBillEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(BankBillEntity record);

    /**
     * select by primary key
     * @param bankBillId primary key
     * @return object by primary key
     */
    BankBillEntity selectByPrimaryKey(Integer bankBillId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(BankBillEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(BankBillEntity record);

    int batchInsert(@Param("list") List<BankBillEntity> list);


    /**
     * 根据流水号查
     *
     * @param tranFlow 流水
     * @param bankTag  标识  银行标示1建设银行2南京银行3中国银行4支付宝5微信
     * @return
     */
    BankBillEntity selectByTranFlow(@Param("tranFlow") String tranFlow, @Param("bankTag") Integer bankTag);


    int updateTranFlowForCharge(@Param("tranFlow") String tranFlow, @Param("bankTag") Integer bankTag,@Param("newTranFlow") String newTranFlow);

    /**
     * 查询所有未推送过金蝶的、存在支付宝回单的流水（必须已关联过erp流水）
     * @return List<BankBillDto>
     */
    List<BankBillDto> getAliPayBillNotPushedToKingDee();

    /**
     * 根据流水号模糊查找
     * @param tranFlow 流水号
     * @return
     */
    List<BankBillDto> getByTranFlowLike(@Param("tranFlow")String tranFlow);

    /**
     * 计算 bankBill中 银行方式 剩余结算金额
     * @param bankBillDto bankBillDto
     * @return 剩余结算金额
     */
    BigDecimal matchBankAmount(BankBillDto bankBillDto);


    /**
     * 根据记账时间和公司id获取流水
     * @param accName1
     * @param minTrandate
     * @param maxTrandate
     * @return
     */
    List<BankBillDto> findByAccName1AndTrandateBetween(@Param("accName1")String accName1,@Param("minTrandate")Date minTrandate,@Param("maxTrandate")Date maxTrandate);

    /**
     * 查询剩余结款金额大于0的流水
     * <AUTHOR>
     * @param tranFlow
     * @return
     */
    List<BankBillDto> queryRemainBankBill(@Param("tranFlow")String tranFlow);
    List<BankBillDto> querySKBankBillInfo(@Param("tranFlow")String tranFlow);

    /**
     * 查询
     * @param tranFlow
     * @param bankTag
     * @return
     */
    List<BankBillEntity> findByTranFlowAndBankTag(@Param("tranFlow")String tranFlow,@Param("bankTag")Integer bankTag);


    List<BankBillDto> queryConstructionBankBill(BankBillQueryDto param);

    List<BankBillDto> queryHongRuiBankBill(HongRuiBankBillQueryDto param);

    BankBillDto checkConstructionBankBill(BankBillDto param);

    List<BankBillDto> queryIgnoreBankBill(BankBillQueryDto param);

    BankBillDto checkIgnoreBankBill(Integer bankBillId);
    BankBillEntity findByTranFlow(@Param("tranFlow")String tranFlow);

    List<BankBillDto> getByTranFlowAndBankTag(@Param("tranFlow") String tranFlow, @Param("bankTag") Integer bankTag);

    int countSpecialSerialNumbers();

    BankBillDto selectTranFlowForChargeLastOne(@Param("bankTag") Integer bankTag);
}