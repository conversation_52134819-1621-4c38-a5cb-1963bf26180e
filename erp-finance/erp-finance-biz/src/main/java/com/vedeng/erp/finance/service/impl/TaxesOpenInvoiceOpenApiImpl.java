package com.vedeng.erp.finance.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.core.base.BaseResponseCode;
import com.vedeng.erp.finance.domain.dto.InvoiceApplyDetailXhDto;
import com.vedeng.erp.finance.domain.dto.TaxesGetInvoiceRequestDto;
import com.vedeng.erp.finance.service.InvoiceApplyDetailXhService;
import com.vedeng.infrastructure.taxes.base.ITaxesParam;
import com.vedeng.infrastructure.taxes.base.ITaxesResult;
import com.vedeng.erp.finance.domain.dto.SaleInvoiceOpenRequestDto;
import com.vedeng.erp.finance.domain.dto.SaleInvoiceOpenResponseDto;
import com.vedeng.erp.finance.enums.TaxesInterfaceCodeEnum;
import com.vedeng.erp.finance.service.AbstractTaxesOpenApiHandler;
import com.vedeng.infrastructure.taxes.domain.TaxesOpenApiResult;
import com.vedeng.infrastructure.taxes.enums.TaxesReturnCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 销项票-开票
 */
@Service
@Slf4j
public class TaxesOpenInvoiceOpenApiImpl extends AbstractTaxesOpenApiHandler {

    @Autowired
    TaxesOpenApiServiceImpl taxesOpenApiService;

    @Autowired
    private InvoiceApplyDetailXhService invoiceApplyDetailXhService;

    @Override
    public void beforeExecute(ITaxesParam taxesParam){
        SaleInvoiceOpenRequestDto saleInvoiceOpenRequestDto = (SaleInvoiceOpenRequestDto) taxesParam;
        SaleInvoiceOpenRequestDto.DataInfo data = saleInvoiceOpenRequestDto.getData();
        List<SaleInvoiceOpenRequestDto.Mxzb> mxzbList = data.getMxzbList();
        int i = 1;
        List<InvoiceApplyDetailXhDto> list = new ArrayList<>();
        for (SaleInvoiceOpenRequestDto.Mxzb mxzb : mxzbList) {

            int applyDetailId = mxzb.getXh();
            mxzb.setXh(i);

            InvoiceApplyDetailXhDto dto = new InvoiceApplyDetailXhDto();
            dto.setInvoiceApplyId(Integer.valueOf(saleInvoiceOpenRequestDto.getData().getOrderNo()));
            dto.setInvoiceApplyDetailId(applyDetailId);
            dto.setXh(i);
            list.add(dto);

            i++;
        }
        invoiceApplyDetailXhService.insert(list);
        log.info("开票前置处理结果：{}",JSONObject.toJSON(taxesParam));
    }
    /**
     * 后置处理
     * @param taxesParam
     * @param executeResult
     * @return
     */
    @Override
    protected ITaxesResult afterExecute(ITaxesParam taxesParam, ITaxesResult executeResult) {
        TaxesOpenApiResult openapi = (TaxesOpenApiResult) executeResult;
        SaleInvoiceOpenRequestDto param = (SaleInvoiceOpenRequestDto) taxesParam;
        String orderNo = param.getData().getOrderNo();
        TaxesOpenApiResult.ReturnInfo return_info = openapi.getReturn_info();
        if (TaxesReturnCodeEnum.REPEAT.getCode().equals(return_info.getReturn_code())
                || TaxesReturnCodeEnum.POSSIBLE_EXCEPTION_FAIL.getCode().equals(return_info.getReturn_code())
                || BaseResponseCode.TAXES_TIMEOUT.equals(return_info.getReturn_code())){
            log.info("销项开票，错误码：{}，查询开票流水号:{}",return_info.getReturn_code(),orderNo);
            TaxesGetInvoiceRequestDto taxesGetInvoiceRequestDto = new TaxesGetInvoiceRequestDto(orderNo,TaxesInterfaceCodeEnum.QUERY_INVOICE);
            ITaxesResult invoiceResult = taxesOpenApiService.openapi(taxesGetInvoiceRequestDto, TaxesInterfaceCodeEnum.QUERY_INVOICE);
            SaleInvoiceOpenResponseDto saleInvoiceOpenResponseDto = new SaleInvoiceOpenResponseDto();
            BeanUtils.copyProperties(invoiceResult,saleInvoiceOpenResponseDto);
            if (StringUtils.isBlank(saleInvoiceOpenResponseDto.getFphm())){
                return setInstance(return_info,null);
            }
            return saleInvoiceOpenResponseDto;
        }

        SaleInvoiceOpenResponseDto taxesResult = null;
        try {
            String decodeStr = new String(Base64Utils.decodeFromString(openapi.getData()));
            taxesResult = JSONObject.parseObject(decodeStr, SaleInvoiceOpenResponseDto.class);
        } catch (Exception e) {
            log.error("销项开票失败，原因:解析异常",e);
            return setInstance(null,taxesResult);
        }
        return setInstance(return_info,taxesResult);
    }

    /**
     * 获取真实错误码，如果错误码有误，返回失败错误码
     * @param return_info
     * @param responseDto
     * @return
     */
    private SaleInvoiceOpenResponseDto setInstance(TaxesOpenApiResult.ReturnInfo return_info,SaleInvoiceOpenResponseDto responseDto){
        if (Objects.isNull(responseDto)){
            log.info("开票结果为空");
            responseDto = new SaleInvoiceOpenResponseDto();
        }
        if (Objects.isNull(return_info) || StringUtils.isBlank(return_info.getReturn_code())){
            responseDto.setReturnCode(TaxesReturnCodeEnum.FAIL.getCode());
            responseDto.setReturnMessage(TaxesReturnCodeEnum.FAIL.getName());
            log.error("销项开票失败，原因：无Code码",responseDto.getReturnMessage());
            return responseDto;
        }
        responseDto.setReturnCode(return_info.getReturn_code());
        responseDto.setReturnMessage(return_info.getReturn_message());
        if (TaxesReturnCodeEnum.SUCCESS.getCode().equals(return_info.getReturn_code()) && StringUtils.isNotBlank(responseDto.getFphm())){
            responseDto.setIsSuccess(Boolean.TRUE);
        }
        return responseDto;
    }
}
