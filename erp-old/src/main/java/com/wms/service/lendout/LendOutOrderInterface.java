package com.wms.service.lendout;

import com.wms.constant.WMSContant;
import com.wms.dto.ExgOrderDto;
import com.wms.dto.WmsRequest;
import com.wms.service.AbstractWmsInterface;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;

/**
 * 外借单接口
 */
@Service
public class LendOutOrderInterface extends AbstractWmsInterface {
    @Override
    protected void setMethodAndMessageId(WmsRequest wmsRequest) {
        wmsRequest.setMessageId(WMSContant.PUT_EXG_DATA);
        wmsRequest.setMethod(WMSContant.PUT_EXG_DATA);
    }

    @Override
    protected JSONObject getXmlDate(Object... param) {

        ExgOrderDto exgOrderDto = (ExgOrderDto)param[0];

        JSONArray headArray = new JSONArray();

        JSONObject headItem = new JSONObject();
        headItem.put("CustomerID","VEDENG");
        headItem.put("WarehouseID","D");
        headItem.put("DOCNO",exgOrderDto.getDOCNO());
        headItem.put("DOC_TYPE", exgOrderDto.getDOC_TYPE());

        headItem.put("is_exchange", exgOrderDto.getIs_exchange());
        headItem.put("Department",exgOrderDto.getDepartment());
        headItem.put("StaffName",exgOrderDto.getStaffName());
        headItem.put("ConsigneeID",exgOrderDto.getConsigneeID());
        headItem.put("ConsigneeName",exgOrderDto.getConsigneeName());

        headItem.put("C_Province",exgOrderDto.getC_Province());
        headItem.put("C_City",exgOrderDto.getC_City());
        headItem.put("C_district",exgOrderDto.getC_district());
        headItem.put("C_street",exgOrderDto.getC_street());
        headItem.put("C_Address1",exgOrderDto.getC_Address1());

        headItem.put("C_Contact",exgOrderDto.getC_Contact());
        headItem.put("C_Tel1",exgOrderDto.getC_Tel1());
        headItem.put("C_Tel2",exgOrderDto.getC_Tel2());

        headItem.put("asreasons",exgOrderDto.getAsreasons());
        headItem.put("ApproveTime",exgOrderDto.getApproveTime());
        headItem.put("referenceNo",exgOrderDto.getReferenceNo());
        headItem.put("NOTES",exgOrderDto.getNotes());

        JSONArray detailArray = new JSONArray();

        exgOrderDto.getDetails().stream().forEach(exgOrderGoodDto -> {
            JSONObject detailItem = new JSONObject();
            detailItem.put("LotAtt08",exgOrderGoodDto.getLotAtt08());
            detailItem.put("LotAtt07",exgOrderGoodDto.getLotAtt07());
            detailItem.put("D_EDI_07",exgOrderGoodDto.getD_EDI_07());
            detailItem.put("SKU",exgOrderGoodDto.getSKU());
            detailItem.put("Qty",exgOrderGoodDto.getQty());
            detailArray.add(detailItem);
        });

        headItem.put("details",detailArray);
        headArray.add(headItem);

        JSONObject headObject = new JSONObject();
        headObject.put("header",headArray);
        return headObject;
    }

    @Override
    protected <T> T parseResponseDate(JSONObject returnObject) {
        return null;
    }
}
