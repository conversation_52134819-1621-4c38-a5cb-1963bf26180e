package com.vedeng.goods.dao;


import com.vedeng.goods.model.BaseCategory;
import com.vedeng.goods.model.CategoryMigration;
import com.vedeng.goods.model.dto.MoveCategoryDto;
import com.vedeng.goods.model.vo.BaseCategoryVo;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;
import java.util.List;
import java.util.Map;

@Named("categoryMigrationMapper")
public interface CategoryMigrationMapper {

    void insertCategoryMigration(CategoryMigration categoryMigration);

    List<CategoryMigration> getCtegoryMigretionlistPage(Map<String, Object> map);
}