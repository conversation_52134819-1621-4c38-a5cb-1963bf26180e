package com.vedeng.apollo;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import org.springframework.stereotype.Component;

@Component
public class ApolloConfigHolder {

    @ApolloConfig
    private Config config;

    public String getConfigValue(String key, String defaultValue) {
        return config.getProperty(key, defaultValue);
    }
}