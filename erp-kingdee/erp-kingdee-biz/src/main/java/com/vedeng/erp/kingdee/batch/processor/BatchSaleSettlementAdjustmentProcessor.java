package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.*;
import com.vedeng.goods.dto.FairValueDto;
import com.vedeng.goods.dto.HistoryDataDto;
import com.vedeng.goods.service.FairValueApiService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 销售订单（含赠品）生成调整单及商品数据（正向）
 * @author: Suqin
 * @date: 2023/2/20 11:18
 **/
@Service
@Slf4j
public class BatchSaleSettlementAdjustmentProcessor implements ItemProcessor<BatchWarehouseGoodsOutInDto, BatchSaleSettlementAdjustmentAndItemDto> {
    @Autowired
    BatchSaleorderGoodsDtoMapper batchSaleorderGoodsDtoMapper;

    @Autowired
    FairValueApiService fairValueApiService;

    @Autowired
    BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;

    @Autowired
    BatchBuyorderGoodsDtoMapper batchBuyorderGoodsDtoMapper;

    @Autowired
    BatchSaleorderDtoMapper batchSaleorderDtoMapper;

    @Autowired
    BatchSaleSettlementAdjustmentDtoMapper batchSaleSettlementAdjustmentDtoMapper;

    @Override
    public BatchSaleSettlementAdjustmentAndItemDto process(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto) throws Exception {
        // 出库单对应的销售订单
        BatchSaleorderDto batchSaleorderDto = batchSaleorderDtoMapper.selectBySaleorderNo(batchWarehouseGoodsOutInDto.getRelateNo());
        // 销售订单是否有对应的调整单，有则退出不进行操作
        BatchSaleSettlementAdjustmentDto queryAdjustment = new BatchSaleSettlementAdjustmentDto();
        queryAdjustment.setSaleorderId(batchSaleorderDto.getSaleorderId());
        List<BatchSaleSettlementAdjustmentDto> queryBySaleorderId = batchSaleSettlementAdjustmentDtoMapper.findByItem(queryAdjustment);
        if (CollUtil.isNotEmpty(queryBySaleorderId)) {
            log.info("销售订单生成正向调整单，销售订单已生成对应调整单，销售订单id={}，调整单id={}", batchSaleorderDto.getSaleorderId(), queryBySaleorderId.get(0).getSaleorderId());
            return null;
        }
        // 未付款的销售订单
        if (Objects.isNull(batchSaleorderDto.getPaymentStatus()) || batchSaleorderDto.getPaymentStatus() == 0) {
            log.info("销售订单生成正向调整单，销售订单id={}未付款，不生成调整单", batchSaleorderDto.getSaleorderId());
            return null;
        }
        // 订单商品
        List<BatchSaleorderGoodsDto> goodsList = batchSaleorderGoodsDtoMapper.getBySaleorderId(batchSaleorderDto.getSaleorderId());
        // 过滤虚拟商品
        goodsList = goodsList.stream().filter(goods -> {
            Integer result = batchSaleorderGoodsDtoMapper.selectIsVirtualGoods(goods.getGoodsId());
            return Objects.isNull(result) || KingDeeConstant.ZERO.equals(result);
        }).collect(Collectors.toList());
        //过滤掉不生成调整单的6个固定sku
        List<String> virtulSkus = Arrays.asList("V127063", "V251526", "V256675", "V253620", "V251462", "V140633");
        goodsList = goodsList.stream().filter(goods -> !virtulSkus.contains(goods.getSku())).collect(Collectors.toList());
        if (CollUtil.isEmpty(goodsList)) {
            log.info("销售订单生成正向调整单，销售订单id={}无实物商品，不生成调整单", batchSaleorderDto.getSaleorderId());
            return null;
        }
        // 是否赠品单
        BatchSaleorderGoodsDto isGift = goodsList.stream().filter(a -> BigDecimal.ZERO.compareTo(a.getPrice()) == 0).findFirst().orElse(null);
        if (Objects.isNull(isGift)) {
            log.info("销售订单生成正向调整单，销售订单id={}无赠品，不生成调整单", batchSaleorderDto.getSaleorderId());
            return null;
        }
        // 含税应收单价合计
        BigDecimal adjustmentAmountTotal = BigDecimal.ZERO;
        // 含税销售单价合计
        BigDecimal receivedAmount = BigDecimal.ZERO;
        // 分摊后含税单价合计
        BigDecimal apportionAmountTotal = BigDecimal.ZERO;
        // 调整单明细
        List<BatchSaleSettlementAdjustmentItemDto> adjustmentItems = new ArrayList<>();
        for (BatchSaleorderGoodsDto goods : goodsList) {
            BigDecimal price = goods.getPrice();
            receivedAmount = receivedAmount.add(price.multiply(BigDecimal.valueOf(goods.getNum())).setScale(2, RoundingMode.HALF_UP));
            // 赠品的应收价取销售单中同sku的实物商品的销售价
            if (BigDecimal.ZERO.compareTo(price) == 0) {
                BatchSaleorderGoodsDto realGoods = goodsList
                        .stream()
                        .filter(a -> BigDecimal.ZERO.compareTo(a.getPrice()) != 0 && goods.getGoodsId().equals(a.getGoodsId()))
                        .findFirst()
                        .orElse(null);
                // 取不到实物商品的价格，则取公允价
                if (Objects.isNull(realGoods)) {
                    //取出库单所在月份的公允价进行计算
                    FairValueDto fairValueDto = fairValueApiService.selectByGoodsId(goods.getGoodsId());
                    List<HistoryDataDto> historyDataDtoList = fairValueDto.getHistoryDataDtoList();
                    if (CollUtil.isNotEmpty(historyDataDtoList)) {
                        Date outInTime = batchWarehouseGoodsOutInDto.getOutInTime();
                        //出库的年月的前一个月
                        outInTime = DateUtil.offsetMonth(outInTime, -1);
                        //出库的年月的string
                        String outInTimeString = DateUtil.format(outInTime, "yyyy-MM");
                        List<HistoryDataDto> mouthDataList = historyDataDtoList.stream().filter(x -> outInTimeString.equals(DateUtil.format(x.getGenerateDate(), "yyyy-MM"))).collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(mouthDataList)){
                            price=CollUtil.getFirst(mouthDataList).getFairValue();
                        }
                    }
                } else {
                    price = realGoods.getPrice();
                }
                // 依然取不到价格，则取SKU最近的已生效采购单的采购价*110%为本此计算的含税应收单价。
                if (price.compareTo(BigDecimal.ZERO) == 0) {
                    BatchBuyorderGoodsDto batchBuyorderGoodsDto = batchBuyorderGoodsDtoMapper.selectBySkuValidLately(goods.getSku());
                    if (Objects.nonNull(batchBuyorderGoodsDto)) {
                        price = batchBuyorderGoodsDto.getPrice().multiply(BigDecimal.valueOf(1.1)).setScale(2, RoundingMode.HALF_UP);
                    }
                }
                // 依然取不到价格，不生成调整单
                if (price.compareTo(BigDecimal.ZERO) == 0) {
                    log.error("销售订单生成正向调整单，赠品无法取到含税应收单价，无法生成调整单:{}", JSON.toJSONString(goods));
                    throw new KingDeeException("销售订单生成正向调整单，赠品无法取到含税应收单价，无法生成调整单");
                }
            }
            adjustmentAmountTotal = adjustmentAmountTotal.add(price.multiply(BigDecimal.valueOf(goods.getNum())).setScale(2, RoundingMode.HALF_UP));

            BatchSaleSettlementAdjustmentItemDto adjustmentItem = new BatchSaleSettlementAdjustmentItemDto();
            adjustmentItem.setDifferenceAmount(price);
            adjustmentItem.setActualAmount(goods.getPrice());
            adjustmentItem.setSkuId(goods.getGoodsId());
            adjustmentItem.setSku(goods.getSku());
            adjustmentItem.setSkuName(goods.getGoodsName());
            adjustmentItem.setNum(goods.getNum());
            adjustmentItem.setSaleorderGoodsId(goods.getSaleorderGoodsId());
            adjustmentItems.add(adjustmentItem);
        }
        if (adjustmentItems.isEmpty()) {
            log.error("销售订单生成正向调整单，出库单明细无法与销售订单商品匹配，出库单id：{}，销售订单id：{}", batchWarehouseGoodsOutInDto.getWarehouseGoodsOutInId(), batchSaleorderDto.getSaleorderId());
            throw new KingDeeException("销售订单生成正向调整单，出库单明细无法与销售订单商品匹配");
        }
        // 含税应收单价*（含税销售单价合计/含税应收单价合计）=分摊后含税价
        // 含税应收单价*（含税销售单价合计/含税应收单价合计）-含税销售单价=需调整含税价
        for (BatchSaleSettlementAdjustmentItemDto item : adjustmentItems) {
            // 分摊后含税价（分摊价格）
            BigDecimal apportionAmount = item.getDifferenceAmount().multiply(receivedAmount).divide(adjustmentAmountTotal, 2, RoundingMode.HALF_UP);
            // 需调整含税价（调整金额）
            BigDecimal adjustmentAmount = apportionAmount.subtract(item.getActualAmount());
            item.setApportionAmount(apportionAmount);
            item.setAdjustmentAmount(adjustmentAmount);
            apportionAmountTotal = apportionAmountTotal.add(apportionAmount.multiply(BigDecimal.valueOf(item.getNum())).setScale(2, RoundingMode.HALF_UP));
        }
        // 尾插金额
        BigDecimal differenceAmount = receivedAmount.subtract(apportionAmountTotal);
        // 调整单
        BatchSaleSettlementAdjustmentDto adjustment = new BatchSaleSettlementAdjustmentDto();
        adjustment.setSaleorderId(batchSaleorderDto.getSaleorderId());
        adjustment.setSaleorderNo(batchSaleorderDto.getSaleorderNo());
        adjustment.setTotalAmount(adjustmentAmountTotal);
        adjustment.setReceivedAmount(receivedAmount);
        adjustment.setDifferenceAmount(differenceAmount);
        adjustment.setAdjustmentType(1);
        BatchSaleSettlementAdjustmentAndItemDto batchSaleSettlementAdjustmentAndItemDto = new BatchSaleSettlementAdjustmentAndItemDto();
        batchSaleSettlementAdjustmentAndItemDto.setBatchSaleSettlementAdjustmentDto(adjustment);
        batchSaleSettlementAdjustmentAndItemDto.setBatchSaleSettlementAdjustmentItemDtoList(adjustmentItems);
        return batchSaleSettlementAdjustmentAndItemDto;
    }
}
