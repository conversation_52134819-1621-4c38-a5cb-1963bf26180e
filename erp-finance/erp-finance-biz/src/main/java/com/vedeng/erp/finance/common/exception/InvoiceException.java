package com.vedeng.erp.finance.common.exception;

import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.finance.domain.dto.TaxesReturnInfo;
import com.vedeng.infrastructure.taxes.enums.TaxesReturnCodeEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 发票异常 用于统一处理发票接口或者发票业务异常
 *               需要记录到重试任务表中
 * @date 2023/9/26 13:47
 */
@Getter
public class InvoiceException extends ServiceException {


    /**
     * 接口返回对象
     */
    private TaxesReturnInfo taxesReturnInfo;


    public InvoiceException(String message) {
        super(message);
        taxesReturnInfo = new TaxesReturnInfo();
        taxesReturnInfo.setReturnCode(TaxesReturnCodeEnum.FAIL.getCode());
        taxesReturnInfo.setReturnMessage(message);
    }

    public InvoiceException(String message, Throwable cause) {
        super(message, cause);
    }


    public InvoiceException(TaxesReturnInfo taxesReturnInfo) {
        super(taxesReturnInfo.getReturnMessage());
        this.taxesReturnInfo = taxesReturnInfo;
    }
}
