<%--
  Created by IntelliJ IDEA.
  User: admin
  Date: 2021/5/28
  Time: 13:56
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="删除原因" scope="application" />
<%@ include file="../../common/common.jsp"%>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css?rnd=1+${resourceVersionKey}" />

<html>
<head>
    <style>
        .skuno{
            padding-left: 3%;
            padding-top: 4%;
            padding-bottom: 2%;
        }
        .showname{
            padding-top: 4%;
            padding-bottom: 2%;

        }
        .del-item{
            padding: 1%;
        }
    </style>
</head>
<body>
<div class="layui-container">
    <div class="layui-row ">
        <div class="layui-col-xs4 layui-col-sm4 layui-col-md4 skuno" >
            <b>订货号：</b>
            <span id="skuNo">${coreSku.skuNo}</span>
        </div>
        <div class="layui-col-xs8 layui-col-sm8 layui-col-md8 showname">
            <b>商品名称：</b>
            <span id="showName">${coreSku.skuName}</span>
        </div>
        <hr />
        <c:if test="${not empty regularOperateLogs}">
            <c:forEach items="${regularOperateLogs}" var="comment">
                <div class="layui-col-xs5 layui-col-sm5 layui-col-md5 del-item">
                    </span>
                        <b>删除时间：</b><date:date value ="${comment.operateTime}" format="yyyy-MM-dd HH:mm:ss"/>
                    </span>
                </div>
                <div class="layui-col-xs7 layui-col-sm7 layui-col-md7 del-item">
                    </span>
                        <b>删除原因：</b>${comment.operateReason}
                    </span>
                </div>
            </c:forEach>
        </c:if>
        <c:if test="${empty regularOperateLogs}">
            <div class="layui-col-xs12 layui-col-sm12 layui-col-md12 del-item">
                删除原因不存在
            </div>
        </c:if>



    </div>


</div>
</body>
</html>
