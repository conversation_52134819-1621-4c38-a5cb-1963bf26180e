package com.vedeng.erp.finance.domain.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 红字确认单申请出参
 */
@Data
public class SaleInvoiceRedConfirmApplyResponseDto extends TaxesReturnInfo {

    /**
     * 返回代码 Y N
     */
    private String Code;

    /**
     * 返回消息
     */
    private String Message;

    /**
     * 红字确认单编号
     */
    private String Hzfpxxqrdbh;

    /**
     * 确认单UUID
     */
    private String Uuid;

    private String Gxsf;
    private BigDecimal Jshj;
    private String Lzkprq;
    private String Lzfpdm;
    private String FpkjfsDm;
    private String Qrjkpbz;
    private String Ykjhzfpbz;
    private String Qrfmc;
    private String Lrfsf;
    private String HzqrxxztDm;
    private String Lrrq;
    private String Sfzzfpbz;
    private String Kpfnsrsbh;
    private String LzfppzDm;
    private String Dqnsrsbh;
    private Integer Hzqrdmxsl;
    private String Xsfnsrsbh;
    /**
     * 发票号码
     */
    private String Lzfphm;

    private String Gmfnsrsbh;
}
