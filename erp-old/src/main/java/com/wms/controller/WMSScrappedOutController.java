package com.wms.controller;

import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.authorization.model.Organization;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.common.validator.FormToken;
import com.vedeng.goods.model.Goods;
import com.vedeng.goods.service.GoodsService;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.service.OrgService;
import com.vedeng.system.service.UserService;
import com.wms.constant.LogicalEnum;
import com.wms.constant.WmsOutputOrderTypeConstant;
import com.wms.model.dto.AddScrappedOutDto;
import com.wms.model.dto.WMSScrappedOutQueryDto;
import com.wms.model.dto.WmsOutputOrderGoodsDto;
import com.wms.model.po.WMSScrappedOutOrder;
import com.wms.model.po.WmsOutputOrder;
import com.wms.service.WMSScrappedOutService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.*;
import java.util.stream.Collectors;


/**
 * WMS商品报废出库表
 */
@Controller
@RequestMapping("/wms/scrapOut")
public class WMSScrappedOutController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(WMSScrappedOutController.class);

    @Autowired
    private WMSScrappedOutService scrappedOutService;

    @Autowired
    @Qualifier("orgService")
    private OrgService orgService;

    @Autowired
    private UserService userService;

    @Autowired
    @Qualifier("actionProcdefService")
    protected ActionProcdefService actionProcdefService;

    @Autowired
    private GoodsService goodsService;



    /**
     *查询报废出库的商品数据
     * @param request
     * @param scrappedOutQueryDto
     * @param pageNo
     * @param pageSize
     * @param session
     * @return mv
     */
    @ResponseBody
    @RequestMapping(value="/index")
    public ModelAndView index(HttpServletRequest request,
                              @ModelAttribute("scrappedOutQueryDto") WMSScrappedOutQueryDto scrappedOutQueryDto,
                              @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                              @RequestParam(required = false) Integer pageSize,
                              HttpSession session){

        ModelAndView mv = new ModelAndView();
        User user =(User)session.getAttribute(ErpConst.CURR_USER);
        if(permoissionsFlag(user,mv)){
            return fail(mv);
        }
        //报废品分类
        setScrappedOutSys(mv);
        //查询商品报废出库列表
        Page page = getPageTag(request,pageNo,pageSize);
        List<WMSScrappedOutOrder> scrappedOutList = scrappedOutService.queryScrapedOutlistPage(scrappedOutQueryDto, page);
        
        mv.addObject("scrappedOutList",scrappedOutList);
        mv.addObject("page", page);
        mv.setViewName("wms/scrappedOut/scrappedOutIndex");
        return mv;
    }

    private boolean permoissionsFlag(User user, ModelAndView mv) {
        User userInfo = userService.getUserById(user.getUserId());
        if(userInfo.getUserId().equals(1) || userInfo.getUserId().equals(2)){
            return false;
        }
        if(userInfo == null || userInfo.getOrgName() == null || !userInfo.getOrgName().contains(ErpConst.QUALITY_ORG)){
                mv.addObject("message","非质量管理部不得操作!");
                return true;
        }
        return false;
    }

    /**
     * 新增报废出库单
     * @param request
     * @return modelAndView
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/toAddScrappedOut")
    public ModelAndView toAddScrappedOut(HttpServletRequest request, HttpSession session){

        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);

        ModelAndView modelAndView = new ModelAndView();
        if(user != null && permoissionsFlag(user,modelAndView)){
            return fail(modelAndView);
        }
        // 获取申请部门
        List<Organization> orgList = orgService.getOrgList(0, 1, true);
        //报废出库类型
        getScrappedOutSys(modelAndView);

        // 获取所有申请部门的用户
        List<User> getUserNameListByOrgId = new ArrayList<User>();
        List<User> getUserListByOrgId = new ArrayList<User>();

        if (CollectionUtils.isNotEmpty(orgList)){
            for (Organization org :  orgList) {
                getUserNameListByOrgId = orgService.getUserListBtOrgId(org.getOrgId(),org.getType(),org.getCompanyId());
                getUserListByOrgId.addAll(getUserNameListByOrgId);
            }
        }

        modelAndView.addObject("today", DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd"));
        modelAndView.addObject("orgList", orgList);
        modelAndView.addObject("getUserListByOrgId", getUserListByOrgId);
        modelAndView.addObject("ezScrappedIndexUrl",ezScrappedIndexUrl);
        modelAndView.setViewName("wms/scrappedOut/scrappedOutAdd");
        return modelAndView;

    }

    private void setScrappedOutSys(ModelAndView modelAndView) {
        //报废品分类
        List<SysOptionDefinition> scrapTypeList = getSysOptionDefinitionList(SysOptionConstant.SCRAP_TYPE);
        modelAndView.addObject("scrapTypeList", scrapTypeList);
        //报废品级别
        List<SysOptionDefinition> scrapLevelList = getSysOptionDefinitionList(SysOptionConstant.SCRAP_LEVEL);
        modelAndView.addObject("scrapLevelList", scrapLevelList);
        //报废处理方式
        List<SysOptionDefinition> scrapDealTypeList = getSysOptionDefinitionList(SysOptionConstant.SCRAP_DEAL_TYPE);
        modelAndView.addObject("scrapDealTypeList", scrapDealTypeList);
    }

    private void getScrappedOutSys(ModelAndView modelAndView) {
        //报废品分类
        List<SysOptionDefinition> scrapTypeList = getSysOptionDefinitionList(SysOptionConstant.SCRAP_TYPE);
        modelAndView.addObject("scrapTypeList", scrapTypeList);
        //报废品级别
        List<SysOptionDefinition> scrapLevelList = getSysOptionDefinitionList(SysOptionConstant.SCRAP_LEVEL);
        modelAndView.addObject("scrapLevelList", scrapLevelList);
//        //报废处理方式
//        List<SysOptionDefinition> scrapDealTypeList = getSysOptionDefinitionList(SysOptionConstant.SCRAP_DEAL_TYPE);
//        scrapDealTypeList.removeIf(sysOptionDefinition -> sysOptionDefinition.getTitle().equals("内部领用"));
//        modelAndView.addObject("sysOptionDefinition", scrapDealTypeList);

    }

    /**
     * 根据部门Id获取部门下的所有用户
     * @param org
     * @return
     */
    @ResponseBody
    @RequestMapping("/getUserNameByOrgId")
    public ResultInfo<User> getUserNameByOrgId(Organization org){

        ResultInfo<User> result = new ResultInfo<User>();
        Organization orgDetail = orgService.getOrgByOrgId(org.getOrgId());
        List<User>  userNameList = orgService.getNewUserListBtOrgId(org.getOrgId(),orgDetail.getType(),orgDetail.getCompanyId());
        result.setCode(0);
        result.setListData(userNameList);

        return result;
    }

    /**
     * 报废商品保存
     * @param
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/saveScrappedOutOrder")
    public ModelAndView saveScrappedOutOrder(HttpServletRequest request, AddScrappedOutDto addScrappedOutDto){

        ModelAndView mv = new ModelAndView();

        try{

            User user = (User)request.getSession().getAttribute(ErpConst.CURR_USER);
            if(permoissionsFlag(user,mv)){
                return fail(mv);
            }
            addScrappedOutDto.setScrapDealType(SysOptionConstant.SCRAPE_DEAL_DESTORY);
            //保存报废出库单
            Integer scrappedOutOrderId = scrappedOutService.addScrappedOutOrder(addScrappedOutDto,user);
            if(scrappedOutOrderId > 0){
                scrappedOutService.addWmsLogicalOrderGodos(user,scrappedOutOrderId);
                //下发报废出库单
                scrappedOutService.putScrappedOutOrder(scrappedOutOrderId);
            }
            mv.addObject("url","/wms/scrapOut/scrapDetail.do?scrappedOutId="+scrappedOutOrderId);
            return success(mv);

        }catch(Exception e){

            LOGGER.error("applyScrappedOut error:",e);

            return fail(mv);
        }

    }
    /**
     * 出库详情页
     */
    @RequestMapping(value = "/scrapDetail")
    public ModelAndView scrapDetail(HttpServletRequest request,@Param("scrappedOutId") Long scrappedOutId) {

        ModelAndView mv = new ModelAndView();

        WmsOutputOrder outputOrder = this.scrappedOutService.findScrappedOutById(scrappedOutId);
        mv.addObject("scrappedOut",outputOrder);

        List<WmsOutputOrderGoodsDto> scrappedOutGoodList = this.scrappedOutService.queryOutputGoodsByScrappedOutId(scrappedOutId);
        mv.addObject("scrappedOutGoodList",scrappedOutGoodList);
        List<WarehouseGoodsOperateLog> wlogList = new ArrayList<>();
        if(outputOrder.getType().equals(WmsOutputOrderTypeConstant.CRASH)){
            wlogList = scrappedOutService.getWlogList(Integer.valueOf(scrappedOutId.intValue()), StockOperateTypeConst.SCRAPED_WAREHOUSE_OUT);

        }else if(outputOrder.getType().equals(WmsOutputOrderTypeConstant.RECEIVING)){
            wlogList = scrappedOutService.getWlogList(Integer.valueOf(scrappedOutId.intValue()), StockOperateTypeConst.SCRAPED_WAREHOUSE_OUT_L);
        }
        mv.addObject("wlogList",wlogList);
        mv.setViewName("wms/scrappedOut/scrappedOutDetail");
        return mv;
    }

    /**
     * <b>Description:</b><br>
     * 搜索逻辑仓商品库存
     *
     * @param request
     * @param searchContent
     * @param pageNo
     * @param pageSize
     * @return
     * @Note <b>Author:</b> leo.yang <br>
     *       <b>Date:</b> 2017年7月6日 上午11:49:49
     */
    @RequestMapping(value = "/logicalSearchGoods")
    public ModelAndView logicalSearchGoods(HttpServletRequest request,HttpSession session,
                                          @RequestParam(value = "searchType",required = false) String searchType,
                                          @RequestParam(value = "searchContent", required = false) String searchContent,
                                          @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                          @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                                          @RequestParam(value="callbackFuntion",required=false) String callbackFuntion,
                                           @RequestParam(value="logicalId",required=false) Integer logicalId) {
        ModelAndView mv = new ModelAndView();
        if (StringUtils.isNoneBlank(searchContent)) {
            User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
            Page page = getPageTag(request, pageNo, pageSize);
            Goods goods = new Goods();
            goods.setCompanyId(user.getCompanyId());
            if(StringUtil.isNotBlank(searchType)){
                List<String> skuList = Arrays.asList(searchContent.split(","));
                goods.setGoodsType(Integer.valueOf(searchType));
                goods.setGoodsIdArr(skuList);
            }else{
                goods.setSearchContent(searchContent);
            }

            Map<String, Object> map = new HashMap<String, Object>();
             if(StringUtil.isNotBlank(LogicalEnum.getLogicalWarehouseCode(logicalId))){
                request.setAttribute("logicalId",logicalId);
                map = goodsService.getLogicallistGoodsStockPage(request, goods, page, user);
                mv.addObject("goodsList", map.get("goodsList"));
            }
            mv.addObject("page", map.get("page"));
            mv.addObject("searchContent", searchContent);
        }
        mv.addObject("callbackFuntion", callbackFuntion);
        mv.addObject("logicalId", logicalId);
        mv.setViewName("wms/scrappedOut/logicalSearchGoods");
        return mv;

    }
}
