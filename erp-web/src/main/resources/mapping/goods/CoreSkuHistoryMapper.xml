<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.dao.CoreSkuHistoryMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.model.CoreSkuHistory">
    <id column="SKU_HISTORY_ID" jdbcType="INTEGER" property="skuHistoryId" />
    <result column="SKU_ID" jdbcType="INTEGER" property="skuId" />
    <result column="SKU_ADD_COMMAND" jdbcType="OTHER" property="skuAddCommand" typeHandler="com.vedeng.common.mybatis.handler.CustomSqlJsonHandler"/>
    <result column="CORE_SKU_GENERATE" jdbcType="OTHER" property="coreSkuGenerate" typeHandler="com.vedeng.common.mybatis.handler.CustomSqlJsonHandler"/>
    <result column="GOODS_STORAGE_CONDITION_VO" jdbcType="OTHER" property="goodsStorageConditionVo" typeHandler="com.vedeng.common.mybatis.handler.CustomSqlJsonHandler"/>
    <result column="SKU_AUTHORIZATION_REQUEST_VO" jdbcType="OTHER" property="skuAuthorizationRequestVo" typeHandler="com.vedeng.common.mybatis.handler.CustomSqlJsonHandler"/>
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
  </resultMap>

  <sql id="Base_Column_List">
    SKU_HISTORY_ID, SKU_ID, SKU_ADD_COMMAND, CORE_SKU_GENERATE, GOODS_STORAGE_CONDITION_VO, 
    SKU_AUTHORIZATION_REQUEST_VO, MOD_TIME, UPDATER
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from V_CORE_SKU_HISTORY
    where SKU_HISTORY_ID = #{skuHistoryId,jdbcType=INTEGER}
  </select>

  <select id="selectBySkuId" resultMap="BaseResultMap">
    SELECT
      SKU_HISTORY_ID,
      SKU_ID,
      SKU_ADD_COMMAND,
      CORE_SKU_GENERATE,
      GOODS_STORAGE_CONDITION_VO,
      SKU_AUTHORIZATION_REQUEST_VO,
      JSON_EXTRACT(SKU_ADD_COMMAND, '$.skuCheckFilesJson') AS skuCheckFilesJson
    FROM
        V_CORE_SKU_HISTORY
    WHERE
        SKU_ID = #{skuId,jdbcType=INTEGER}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from V_CORE_SKU_HISTORY
    where SKU_HISTORY_ID = #{skuHistoryId,jdbcType=INTEGER}
  </delete>

  <insert id="insert" keyColumn="SKU_HISTORY_ID" keyProperty="skuHistoryId" parameterType="com.vedeng.goods.model.CoreSkuHistory" useGeneratedKeys="true">
    insert into V_CORE_SKU_HISTORY (SKU_ID, SKU_ADD_COMMAND, CORE_SKU_GENERATE, 
      GOODS_STORAGE_CONDITION_VO, SKU_AUTHORIZATION_REQUEST_VO, 
      MOD_TIME, UPDATER)
    values (#{skuId,jdbcType=INTEGER}, #{skuAddCommand,jdbcType=OTHER,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonHandler}, #{coreSkuGenerate,jdbcType=OTHER,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonHandler},
      #{goodsStorageConditionVo,jdbcType=OTHER,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonHandler}, #{skuAuthorizationRequestVo,jdbcType=OTHER,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonHandler},
      #{modTime,jdbcType=BIGINT,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonHandler}, #{updater,jdbcType=INTEGER,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonHandler})
  </insert>

  <insert id="insertSelective" keyColumn="SKU_HISTORY_ID" keyProperty="skuHistoryId" parameterType="com.vedeng.goods.model.CoreSkuHistory" useGeneratedKeys="true">
    insert into V_CORE_SKU_HISTORY
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="skuId != null">
        SKU_ID,
      </if>
      <if test="skuAddCommand != null">
        SKU_ADD_COMMAND,
      </if>
      <if test="coreSkuGenerate != null">
        CORE_SKU_GENERATE,
      </if>
      <if test="goodsStorageConditionVo != null">
        GOODS_STORAGE_CONDITION_VO,
      </if>
      <if test="skuAuthorizationRequestVo != null">
        SKU_AUTHORIZATION_REQUEST_VO,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="skuId != null">
        #{skuId,jdbcType=INTEGER},
      </if>
      <if test="skuAddCommand != null">
        #{skuAddCommand,jdbcType=OTHER,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonHandler},
      </if>
      <if test="coreSkuGenerate != null">
        #{coreSkuGenerate,jdbcType=OTHER,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonHandler},
      </if>
      <if test="goodsStorageConditionVo != null">
        #{goodsStorageConditionVo,jdbcType=OTHER,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonHandler},
      </if>
      <if test="skuAuthorizationRequestVo != null">
        #{skuAuthorizationRequestVo,jdbcType=OTHER,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonHandler},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.model.CoreSkuHistory">
    update V_CORE_SKU_HISTORY
    <set>
      <if test="skuId != null">
        SKU_ID = #{skuId,jdbcType=INTEGER},
      </if>
      <if test="skuAddCommand != null">
        SKU_ADD_COMMAND = #{skuAddCommand,jdbcType=OTHER,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonHandler},
      </if>
      <if test="coreSkuGenerate != null">
        CORE_SKU_GENERATE = #{coreSkuGenerate,jdbcType=OTHER,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonHandler},
      </if>
      <if test="goodsStorageConditionVo != null">
        GOODS_STORAGE_CONDITION_VO = #{goodsStorageConditionVo,jdbcType=OTHER,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonHandler},
      </if>
      <if test="skuAuthorizationRequestVo != null">
        SKU_AUTHORIZATION_REQUEST_VO = #{skuAuthorizationRequestVo,jdbcType=OTHER,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonHandler},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where SKU_HISTORY_ID = #{skuHistoryId,jdbcType=INTEGER}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.model.CoreSkuHistory">
    update V_CORE_SKU_HISTORY
    set SKU_ID = #{skuId,jdbcType=INTEGER},
      SKU_ADD_COMMAND = #{skuAddCommand,jdbcType=OTHER,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonHandler},
      CORE_SKU_GENERATE = #{coreSkuGenerate,jdbcType=OTHER,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonHandler},
      GOODS_STORAGE_CONDITION_VO = #{goodsStorageConditionVo,jdbcType=OTHER,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonHandler},
      SKU_AUTHORIZATION_REQUEST_VO = #{skuAuthorizationRequestVo,jdbcType=OTHER,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonHandler},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER}
    where SKU_HISTORY_ID = #{skuHistoryId,jdbcType=INTEGER}
  </update>

</mapper>