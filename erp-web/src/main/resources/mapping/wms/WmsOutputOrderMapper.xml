<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wms.dao.WmsOutputOrderMapper" >
  <resultMap id="BaseResultMap" type="com.wms.model.po.WmsOutputOrder" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
    <result column="verify_status" property="verifyStatus" jdbcType="INTEGER" />
    <result column="out_status" property="outStatus" jdbcType="INTEGER" />
    <result column="return_status" property="returnStatus" jdbcType="INTEGER" />
    <result column="borrow_reason" property="borrowReason" jdbcType="VARCHAR" />
    <result column="borrow_trader_name" property="borrowTraderName" jdbcType="BIGINT" />
    <result column="logistic_commnet" property="logisticCommnet" jdbcType="VARCHAR" />
    <result column="logistic_commnet" property="logisticCommnet" jdbcType="VARCHAR" />
    <result column="belong_department" property="belongDepartment" jdbcType="VARCHAR" />
    <result column="scrap_type" property="scrapType" jdbcType="INTEGER" />
    <result column="scrap_level" property="scrapLevel" jdbcType="INTEGER" />
    <result column="scrap_deal_type" property="scrapDealType" jdbcType="INTEGER" />
    <result column="applyer" property="applyer" jdbcType="VARCHAR" />
    <result column="applyer_department" property="applyerDepartment" jdbcType="VARCHAR" />
    <result column="recipient_applyer" property="recipientApplyer" jdbcType="VARCHAR" />
    <result column="recipient_department" property="recipientDepartment" jdbcType="VARCHAR" />
    <result column="recipient_type" property="recipientType" jdbcType="INTEGER" />
    <result column="apple_out_date" property="appleOutDate" jdbcType="VARCHAR" />
    <result column="use_nature" property="useNature" jdbcType="VARCHAR" />
    <result column="use_name" property="useName" jdbcType="VARCHAR" />
    <result column="receiver" property="receiver" jdbcType="VARCHAR" />
    <result column="receiver_address" property="receiverAddress" jdbcType="VARCHAR" />
    <result column="receiver_telphone" property="receiverTelphone" jdbcType="VARCHAR" />
    <result column="receiver_phone" property="receiverPhone" jdbcType="VARCHAR" />
    <result column="detail_address" property="detailAddress" jdbcType="VARCHAR" />
    <result column="apply_reason" property="applyReason" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="real_output_time" property="realOutputTime" jdbcType="VARCHAR" />
    <result column="customer_receive_time" property="customerReceiveTime" jdbcType="VARCHAR" />
    <result column="approval_time" property="approvalTime" jdbcType="VARCHAR" />
    <result column="add_time" property="addTime" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="VARCHAR" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="updator" property="updator" jdbcType="VARCHAR" />
    <result column="is_delete" property="isDelete" jdbcType="INTEGER" />
  </resultMap>

  <resultMap type="com.wms.model.po.WmsLendOutOrder"
             id="LenfoutResultMap" extends="BaseResultMap">
    <result column="goodsName" property="goodsName" jdbcType="VARCHAR" />
    <result column="sku" property="skuNo" jdbcType="VARCHAR" />
    <result column="goodsId" property="goodsId" jdbcType="BIGINT" />
    <result column="BRAND_NAME" property="brandName" jdbcType="VARCHAR" />
    <result column="MODEL" property="model" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="Base_Column_List" >
    id, type, order_no, verify_status,out_status,return_status,borrow_reason, borrow_trader_id,borrow_trader_name, logistic_commnet,
    belong_department, scrap_type, scrap_level, scrap_deal_type, applyer, applyer_department, 
    recipient_applyer, recipient_department, recipient_type, apple_out_date, use_nature, 
    use_name, receiver, receiver_address, receiver_telphone,receiver_phone, detail_address, apply_reason,
    remark, real_output_time, customer_receive_time,approval_time, add_time, update_time, creator,
    updator, is_delete,applyer_id
  </sql>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from T_WMS_OUTPUT_ORDER
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByOrderNo" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
      <include refid="Base_Column_List" />
    from T_WMS_OUTPUT_ORDER
    where order_no = #{orderNo,jdbcType=VARCHAR}
  </select>


  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from T_WMS_OUTPUT_ORDER
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insertSelective" parameterType="com.wms.model.po.WmsOutputOrder" useGeneratedKeys="true" keyProperty="id">
    insert into T_WMS_OUTPUT_ORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="type != null">
        `type`,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="verifyStatus != null">
        verify_status,
      </if>
      <if test="returnStatus != null">
        return_status,
      </if>
      <if test="outStatus != null">
        out_status,
      </if>
      <if test="borrowReason != null">
        borrow_reason,
      </if>
      <if test="borrowTraderId != null">
        borrow_trader_id,
      </if>
      <if test="borrowTraderName != null">
        borrow_trader_name,
      </if>
      <if test="logisticCommnet != null">
        logistic_commnet,
      </if>
      <if test="belongDepartment != null">
        belong_department,
      </if>
      <if test="scrapType != null">
        scrap_type,
      </if>
      <if test="scrapLevel != null">
        scrap_level,
      </if>
      <if test="scrapDealType != null">
        scrap_deal_type,
      </if>
      <if test="applyer != null">
        applyer,
      </if>
      <if test="applyerDepartment != null">
        applyer_department,
      </if>
      <if test="recipientApplyer != null">
        recipient_applyer,
      </if>
      <if test="recipientDepartment != null">
        recipient_department,
      </if>
      <if test="recipientType != null">
        recipient_type,
      </if>
      <if test="appleOutDate != null">
        apple_out_date,
      </if>
      <if test="useNature != null">
        use_nature,
      </if>
      <if test="useName != null">
        use_name,
      </if>
      <if test="receiver != null">
        receiver,
      </if>
      <if test="receiverPhone != null">
        receiver_phone,
      </if>
      <if test="receiverTelphone != null">
        receiver_telphone,
      </if>
      <if test="receiverAddress != null">
        receiver_address,
      </if>
      <if test="detailAddress != null">
        detail_address,
      </if>
      <if test="applyReason != null">
        apply_reason,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="realOutputTime != null">
        real_output_time,
      </if>
      <if test="customerReceiveTime != null">
        customer_receive_time,
      </if>
      <if test="approvalTime != null">
        approval_time,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updator != null">
        updator,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="applyerId != null">
        applyer_id,
      </if>
      <if test="applyerDepartmentId != null">
        applyer_department_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="verifyStatus != null">
        #{verifyStatus,jdbcType=INTEGER},
      </if>
      <if test="returnStatus != null">
        #{returnStatus,jdbcType=INTEGER},
      </if>
      <if test="outStatus != null">
        #{outStatus,jdbcType=INTEGER},
      </if>
      <if test="borrowReason != null">
        #{borrowReason,jdbcType=VARCHAR},
      </if>
      <if test="borrowTraderId != null">
        #{borrowTraderId,jdbcType=BIGINT},
      </if>
      <if test="borrowTraderName != null">
        #{borrowTraderName,jdbcType=VARCHAR},
      </if>
      <if test="logisticCommnet != null">
        #{logisticCommnet,jdbcType=VARCHAR},
      </if>
      <if test="belongDepartment != null">
        #{belongDepartment,jdbcType=VARCHAR},
      </if>
      <if test="scrapType != null">
        #{scrapType,jdbcType=INTEGER},
      </if>
      <if test="scrapLevel != null">
        #{scrapLevel,jdbcType=INTEGER},
      </if>
      <if test="scrapDealType != null">
        #{scrapDealType,jdbcType=INTEGER},
      </if>
      <if test="applyer != null">
        #{applyer,jdbcType=VARCHAR},
      </if>
      <if test="applyerDepartment != null">
        #{applyerDepartment,jdbcType=VARCHAR},
      </if>
      <if test="recipientApplyer != null">
        #{recipientApplyer,jdbcType=VARCHAR},
      </if>
      <if test="recipientDepartment != null">
        #{recipientDepartment,jdbcType=VARCHAR},
      </if>
      <if test="recipientType != null">
        #{recipientType,jdbcType=INTEGER},
      </if>
      <if test="appleOutDate != null">
        #{appleOutDate,jdbcType=VARCHAR},
      </if>
      <if test="useNature != null">
        #{useNature,jdbcType=VARCHAR},
      </if>
      <if test="useName != null">
        #{useName,jdbcType=VARCHAR},
      </if>
      <if test="receiver != null">
        #{receiver,jdbcType=VARCHAR},
      </if>
      <if test="receiverPhone != null">
        #{receiverPhone,jdbcType=VARCHAR},
      </if>
      <if test="receiverTelphone != null">
        #{receiverTelphone,jdbcType=VARCHAR},
      </if>
      <if test="receiverAddress != null">
        #{receiverAddress,jdbcType=VARCHAR},
      </if>
      <if test="detailAddress != null">
        #{detailAddress,jdbcType=VARCHAR},
      </if>
      <if test="applyReason != null">
        #{applyReason,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="realOutputTime != null">
        #{realOutputTime,jdbcType=VARCHAR},
      </if>
      <if test="customerReceiveTime != null">
        #{customerReceiveTime,jdbcType=VARCHAR},
      </if>
      <if test="approvalTime != null">
        #{approvalTime,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updator != null">
        #{updator,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="applyerId != null">
        #{applyerId,jdbcType=INTEGER},
      </if>
      <if test="applyerDepartmentId != null">
        #{applyerDepartmentId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.wms.model.po.WmsOutputOrder" >
    update T_WMS_OUTPUT_ORDER
    <set >
      <if test="type != null" >
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="returnStatus != null" >
        return_status = #{returnStatus,jdbcType=INTEGER},
      </if>
      <if test="outStatus != null" >
        out_status = #{outStatus,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null" >
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="verifyStatus != null" >
        verify_status = #{verifyStatus,jdbcType=INTEGER},
      </if>
      <if test="borrowReason != null" >
        borrow_reason = #{borrowReason,jdbcType=VARCHAR},
      </if>
      <if test="borrowTraderId != null" >
        borrow_trader_id = #{borrowTraderId,jdbcType=BIGINT},
      </if>
      <if test="logisticCommnet != null" >
        logistic_commnet = #{logisticCommnet,jdbcType=VARCHAR},
      </if>
      <if test="belongDepartment != null" >
        belong_department = #{belongDepartment,jdbcType=VARCHAR},
      </if>
      <if test="scrapType != null" >
        scrap_type = #{scrapType,jdbcType=INTEGER},
      </if>
      <if test="scrapLevel != null" >
        scrap_level = #{scrapLevel,jdbcType=INTEGER},
      </if>
      <if test="scrapDealType != null" >
        scrap_deal_type = #{scrapDealType,jdbcType=INTEGER},
      </if>
      <if test="applyer != null" >
        applyer = #{applyer,jdbcType=VARCHAR},
      </if>
      <if test="applyerDepartment != null" >
        applyer_department = #{applyerDepartment,jdbcType=VARCHAR},
      </if>
      <if test="recipientApplyer != null" >
        recipient_applyer = #{recipientApplyer,jdbcType=VARCHAR},
      </if>
      <if test="recipientDepartment != null" >
        recipient_department = #{recipientDepartment,jdbcType=VARCHAR},
      </if>
      <if test="recipientType != null" >
        recipient_type = #{recipientType,jdbcType=INTEGER},
      </if>
      <if test="appleOutDate != null" >
        apple_out_date = #{appleOutDate,jdbcType=VARCHAR},
      </if>
      <if test="useNature != null" >
        use_nature = #{useNature,jdbcType=VARCHAR},
      </if>
      <if test="useName != null" >
        use_name = #{useName,jdbcType=VARCHAR},
      </if>
      <if test="receiver != null" >
        receiver = #{receiver,jdbcType=VARCHAR},
      </if>
      <if test="receiverAddress != null" >
        receiver_address = #{receiverAddress,jdbcType=VARCHAR},
      </if>
      <if test="receiverTelphone != null" >
        receiver_telphone = #{receiverTelphone,jdbcType=VARCHAR},
      </if>
      <if test="detailAddress != null" >
        detail_address = #{detailAddress,jdbcType=VARCHAR},
      </if>
      <if test="applyReason != null" >
        apply_reason = #{applyReason,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="realOutputTime != null" >
        real_output_time = #{realOutputTime,jdbcType=VARCHAR},
      </if>
      <if test="customerReceiveTime != null" >
        customer_receive_time = #{customerReceiveTime,jdbcType=VARCHAR},
      </if>
      <if test="approvalTime != null" >
        approval_time = #{approvalTime,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        add_time = #{addTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updator != null" >
        updator = #{updator,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null" >
        is_delete = #{isDelete,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="querylistPage"  parameterType="Map" resultType="com.wms.model.po.WmsLendOutOrder">
    select
    A.id as id,
    S.SHOW_NAME AS goodsName,
    S.SKU_NO    AS skuNo,
    S.SKU_ID as goodsId,
    BRAND_NAME,
    MODEL,
    order_no,
    A.creator   as creator,
    belong_department,
    borrow_trader_name,
    A.add_time  as add_time,
    verify_status,
    return_status
    from
      T_WMS_OUTPUT_ORDER A
      LEFT JOIN T_WMS_OUTPUT_ORDER_GOODS B ON A.ID = B.wms_output_order_id
      LEFT JOIN V_CORE_SKU S ON B.sku_no = S.SKU_NO
      LEFT JOIN V_CORE_SPU P ON S.SPU_ID = P.SPU_ID
      LEFT JOIN T_BRAND BR ON BR.BRAND_ID = P.BRAND_ID
      WHERE 1=1 AND type =1
      <if test="lendOutQueryDto.orderNo != null and lendOutQueryDto.orderNo != ''">
        and order_no = #{lendOutQueryDto.orderNo,jdbcType=VARCHAR}
      </if>
      <if test="lendOutQueryDto.verifyStatus != null">
        and verify_status = #{lendOutQueryDto.verifyStatus,jdbcType=INTEGER}
      </if>
      <if test="lendOutQueryDto.returnStatus != null">
        and return_status = #{lendOutQueryDto.returnStatus,jdbcType=INTEGER}
      </if>
      <if test="lendOutQueryDto.addStartTime != null and lendOutQueryDto.addStartTime != ''">
        and A.add_time <![CDATA[>=]]> CONCAT(#{lendOutQueryDto.addStartTime,jdbcType=VARCHAR},' 00:00:00')
      </if>
      <if test="lendOutQueryDto.addEndTime != null and lendOutQueryDto.addEndTime != ''">
        and A.add_time <![CDATA[<=]]> CONCAT(#{lendOutQueryDto.addEndTime,jdbcType=VARCHAR},' 23:59:59')
      </if>
      <if test="lendOutQueryDto.goodsName != null">
        and S.SHOW_NAME like CONCAT('%',#{lendOutQueryDto.goodsName,jdbcType=VARCHAR},'%' )
      </if>
    <if test="lendOutQueryDto.skuNo != null">
      and S.SKU_NO like CONCAT('%',#{lendOutQueryDto.skuNo,jdbcType=VARCHAR},'%' )
    </if>
    <if test="lendOutQueryDto.brandName != null">
      and BRAND_NAME like CONCAT('%',#{lendOutQueryDto.brandName,jdbcType=VARCHAR},'%' )
    </if>
    <if test="lendOutQueryDto.model != null">
      and MODEL like CONCAT('%',#{lendOutQueryDto.model,jdbcType=VARCHAR},'%' )
    </if>
    order by add_time desc
  </select>
    <select id="getLendoutValidingBySkuNo" resultType="com.wms.model.po.WmsOutputOrder">
      select
        DISTINCT  WOO.order_no
      from T_WMS_OUTPUT_ORDER WOO
      LEFT JOIN T_WMS_OUTPUT_ORDER_GOODS WOOG ON WOOG.wms_output_order_id = WOO.id AND WOOG.is_delete = 0
      WHERE 1=1
      AND WOO.type = 1
      AND WOO.verify_status= 1
      AND WOO.is_delete = 0
      and WOOG.SKU_NO =  #{skuNo,jdbcType=VARCHAR}


    </select>
  <select id="getUnbackLendOutBySkuNo" resultType="com.wms.model.po.WmsOutputOrder">
    select
    DISTINCT  WOO.order_no
    from T_WMS_OUTPUT_ORDER WOO
    LEFT JOIN T_WMS_OUTPUT_ORDER_GOODS WOOG ON WOOG.wms_output_order_id = WOO.id AND WOOG.is_delete = 0
    WHERE 1=1
    AND WOO.type = 1
    AND WOO.verify_status= 2
    AND WOO.return_status IN (0,1)
    AND WOO.is_delete = 0
    and WOOG.SKU_NO = #{skuNo,jdbcType=VARCHAR}

  </select>

  <select id="getInventoryOutByOrderNo" resultMap="BaseResultMap">
    select * from
    T_WMS_OUTPUT_ORDER
    where order_no = #{orderNo} and type = 4
  </select>

  <select id="getWmsOutputOrderByOrderNoAndType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_WMS_OUTPUT_ORDER
    where order_no = #{orderNo,jdbcType=VARCHAR} and type = #{type,jdbcType=INTEGER}
  </select>


  <select id="getOrderAssistantBySkuIdList" resultType="com.vedeng.order.model.OrderAssistantRelationDo">
    SELECT
      T3.PRODUCT_MANAGER_USER_ID,
      T3.ORDER_ASSISTANT_USER_ID
    FROM
      V_CORE_SKU T1
        LEFT JOIN V_CORE_SPU T2 ON T1.SPU_ID = T2.SPU_ID
        LEFT JOIN T_ORDER_ASSISTANT_RELATION T3 ON T2.ASSIGNMENT_ASSISTANT_ID = T3.PRODUCT_MANAGER_USER_ID
        AND T3.IS_DELETE = 0
    WHERE
        T1.SKU_ID IN
    <foreach item="skuId" index="index" collection="skuIdList" open="("
             separator="," close=")">
      #{skuId}
    </foreach>
  </select>

  <select id="getWarehouseOutList" resultType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
    SELECT
    SUM(a.NUM) NUM,
    0.00 PRICE,
    a.WAREHOUSE_GOODS_OPERATE_LOG_ID,
    a.OPERATE_TYPE,
    a.RELATED_ID,
    a.ADD_TIME,
    a.CREATOR,
    a.EXPIRATION_DATE,
    a.PRODUCT_DATE,
    a.BATCH_NUMBER,
    a.BARCODE_FACTORY,
    n.SKU_NO SKU,
    n.MATERIAL_CODE,
    MM.PRODUCT_COMPANY_LICENCE PRODUCTION_LICENSE,
    MM.RECORD_CERTIFICATE_LICENCE recordCertificateLicence,
    RR.REGISTRATION_NUMBER `RECORD_NUMBER` ,
    RR.MANAGE_CATEGORY_LEVEL manageCategoryLevel,
    j.TEMPERATURE ,
    j.REGISTRATION_NUMBER_ID,
    j.`CONDITION_ONE` ,
    k.`TITLE` ,
    n.`STORAGE_CONDITION_ONE`,
    n.STORAGE_CONDITION_ONE_LOWER_VALUE,
    n.STORAGE_CONDITION_ONE_UPPER_VALUE,
    n.STORAGE_CONDITION_ONE,
    a.GOODS_ID,
    o.SPU_TYPE,
    n.SUPPLY_MODEL,
    n.SKU_NAME PRODUCT_CHINESE_NAME,
    n.SHOW_NAME GOODS_NAME,
    d.BRAND_NAME,
    d.MANUFACTURER,
    n.MODEL,
    n.SPEC,
    c.UNIT_NAME,
    RR.PRODUCT_COMPANY_LICENCE,
    RR.REGISTRATION_NUMBER
    FROM
    T_WAREHOUSE_GOODS_OPERATE_LOG a
    LEFT JOIN T_WMS_OUTPUT_ORDER_GOODS b ON a.RELATED_ID = b.id
    LEFT JOIN `V_CORE_SKU` n ON a.GOODS_ID = n.`SKU_ID`
    LEFT JOIN `V_CORE_SPU` o ON n.`SPU_ID` = o.`SPU_ID`
    LEFT JOIN T_UNIT c ON n.BASE_UNIT_ID   = c.UNIT_ID
    LEFT JOIN T_BRAND d ON o.BRAND_ID = d.BRAND_ID
    LEFT JOIN T_FIRST_ENGAGE j ON o.`FIRST_ENGAGE_ID` = j.`FIRST_ENGAGE_ID`
    LEFT JOIN T_REGISTRATION_NUMBER RR ON RR.REGISTRATION_NUMBER_ID=j.REGISTRATION_NUMBER_ID
    LEFT JOIN T_MANUFACTURER MM ON MM.MANUFACTURER_ID=RR.MANUFACTURER_ID
    LEFT JOIN T_SYS_OPTION_DEFINITION k ON RR.`MANAGE_CATEGORY_LEVEL` = 	k.`SYS_OPTION_DEFINITION_ID`
    WHERE 1 = 1
    <if test="goodsFlag != null and goodsFlag== 1">
      AND o.SPU_TYPE = 316
    </if>
    <if test="goodsFlag != null and goodsFlag== 2">
      AND o.SPU_TYPE <![CDATA[ <> ]]> 316
    </if>
    AND  a.WAREHOUSE_GOODS_OPERATE_LOG_ID IN
    <foreach item="wId" index="index" collection="idList" open="("
             separator="," close=")">
      #{wId}
    </foreach>
    GROUP BY a.EXPIRATION_DATE,n.SKU_NO,a.BATCH_NUMBER,a.PRODUCT_DATE,a.BARCODE_FACTORY
    ORDER BY o.SPU_TYPE = 316
  </select>
</mapper>