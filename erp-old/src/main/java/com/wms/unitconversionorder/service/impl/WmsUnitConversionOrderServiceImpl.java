package com.wms.unitconversionorder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.utils.numgenerator.BillNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.service.VgoodsService;
import com.vedeng.logistics.dao.WarehouseGoodsOperateLogMapper;
import com.vedeng.logistics.eums.WarehouseGoodsOutEnum;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.logistics.model.WarehouseStock;
import com.vedeng.logistics.model.vo.WarehouseGoodsOutLogVo;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.logistics.service.impl.BaseWarehouseGoodsOutDetailService;
import com.vedeng.system.service.VerifiesRecordService;
import com.wms.constant.LogicalEnum;
import com.wms.service.chain.Build.StepBuildFactory;
import com.wms.service.chain.HandlerStep;
import com.wms.service.chain.HandlerStepContext;
import com.wms.unitconversionorder.dao.WmsUnitConversionOrderItemMapper;
import com.wms.unitconversionorder.dao.WmsUnitConversionOrderMapper;
import com.wms.unitconversionorder.model.dto.WmsUnitConversionOrderDto;
import com.wms.unitconversionorder.model.dto.WmsUnitConversionOrderItemDto;
import com.wms.unitconversionorder.model.entity.WmsUnitConversionOrder;
import com.wms.unitconversionorder.model.entity.WmsUnitConversionOrderItem;
import com.wms.unitconversionorder.service.WmsUnitConversionOrderService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 库存转换单接口实现
 * @date 2023/2/17 11:03
 **/
@Service
@Slf4j
public class WmsUnitConversionOrderServiceImpl implements WmsUnitConversionOrderService {

    @Autowired
    private WmsUnitConversionOrderMapper wmsUnitConversionOrderMapper;

    @Autowired
    private WmsUnitConversionOrderItemMapper wmsUnitConversionOrderItemMapper;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Autowired
    @Qualifier("warehouseGoodsOperateLogMapper")
    private WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

    @Autowired
    private VgoodsService vGoodsService;

    @Autowired
    private StepBuildFactory stepBuildFactory;

    @Autowired
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    @Autowired
    @Qualifier("actionProcdefService")
    private ActionProcdefService actionProcdefService;

    @Autowired
    @Qualifier("verifiesRecordService")
    private VerifiesRecordService verifiesRecordService;

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Integer add(WmsUnitConversionOrderDto wmsUnitConversionOrderDto) {

        log.info("add 数据：{}", JSON.toJSONString(wmsUnitConversionOrderDto));

        checkAddData(wmsUnitConversionOrderDto);

        WmsUnitConversionOrder wmsUnitConversionOrder = new WmsUnitConversionOrder();
        BeanUtil.copyProperties(wmsUnitConversionOrderDto, wmsUnitConversionOrder);
        List<WmsUnitConversionOrderItem> wmsUnitConversionOrderItems = BeanUtil.copyToList(wmsUnitConversionOrderDto.getWmsUnitConversionOrderItemDtoList(), WmsUnitConversionOrderItem.class);

        // 订单编号
        BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.WMS_UINT_CONVERSION_ORDER);
        String code = new BillNumGenerator().distribution(billGeneratorBean);
        wmsUnitConversionOrder.setWmsUnitConversionOrderNo(code);

        // 部门
        wmsUnitConversionOrder.setOrgId(CurrentUser.getCurrentUser().getOrgId());
        wmsUnitConversionOrder.setOrgName(CurrentUser.getCurrentUser().getOrgName());
        wmsUnitConversionOrderMapper.insertSelective(wmsUnitConversionOrder);

        // 明细保存
        wmsUnitConversionOrderItems.forEach(c -> {
            c.setWmsUnitConversionOrderId(wmsUnitConversionOrder.getWmsUnitConversionOrderId());
            wmsUnitConversionOrderItemMapper.insertSelective(c);
        });
        return wmsUnitConversionOrder.getWmsUnitConversionOrderId();
    }

    /**
     * 基础字段校验
     *
     * @param wmsUnitConversionOrderDto checkData
     */
    private void checkAddData(WmsUnitConversionOrderDto wmsUnitConversionOrderDto) {

        Assert.notNull(wmsUnitConversionOrderDto, "入参数据可以未空");
        Assert.notEmpty(wmsUnitConversionOrderDto.getReason(), "申请原因不可为空");
        Assert.notNull(wmsUnitConversionOrderDto.getOrderType(), "申请类型不可为空");
        Assert.notEmpty(wmsUnitConversionOrderDto.getWmsUnitConversionOrderItemDtoList(), "转换单明细不可为空");
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void edit(WmsUnitConversionOrderDto wmsUnitConversionOrderDto) {

        log.info("edit 入参：{}", JSON.toJSONString(wmsUnitConversionOrderDto));

        checkEditData(wmsUnitConversionOrderDto);

        WmsUnitConversionOrderDto oldData = wmsUnitConversionOrderMapper.selectByWmsUnitConversionOrderId(wmsUnitConversionOrderDto.getWmsUnitConversionOrderId());
        if (Objects.isNull(oldData)) {
            throw new ServiceException("未能查到原单位转换单信息");
        }

        List<WmsUnitConversionOrderItemDto> oldItemData = oldData.getWmsUnitConversionOrderItemDtoList();
        if (CollUtil.isEmpty(oldItemData)) {
            throw new ServiceException("原始单内的商品数据为空");
        }

        // 主表数据更新
        WmsUnitConversionOrder wmsUnitConversionOrder = new WmsUnitConversionOrder();
        BeanUtil.copyProperties(wmsUnitConversionOrderDto, wmsUnitConversionOrder);
        wmsUnitConversionOrderMapper.updateByPrimaryKeySelective(wmsUnitConversionOrder);

        List<WmsUnitConversionOrderItemDto> newItemData = wmsUnitConversionOrderDto.getWmsUnitConversionOrderItemDtoList();

        // 老的更新
        List<WmsUnitConversionOrderItemDto> needUpdate = newItemData.stream().filter(x -> Objects.nonNull(x.getWmsUnitConversionOrderItemId())).collect(Collectors.toList());
        log.info("edit id:{} 需要更新数据：{}", JSON.toJSONString(wmsUnitConversionOrderDto.getWmsUnitConversionOrderId()), JSON.toJSONString(needUpdate));
        // 新的新增
        List<WmsUnitConversionOrderItemDto> needAdd = newItemData.stream().filter(x -> Objects.isNull(x.getWmsUnitConversionOrderItemId())).collect(Collectors.toList());
        log.info("edit id:{} 需要新增数据：{}", JSON.toJSONString(wmsUnitConversionOrderDto.getWmsUnitConversionOrderId()), JSON.toJSONString(needAdd));
        // 没有的删除
        List<Integer> collect = needUpdate.stream().filter(Objects::nonNull).map(WmsUnitConversionOrderItemDto::getWmsUnitConversionOrderItemId).collect(Collectors.toList());
        List<WmsUnitConversionOrderItemDto> needDelete = oldItemData.stream().filter(x -> !collect.contains(x.getWmsUnitConversionOrderItemId())).collect(Collectors.toList());
        log.info("edit id:{} 需要删除数据：{}", JSON.toJSONString(wmsUnitConversionOrderDto.getWmsUnitConversionOrderId()), JSON.toJSONString(needDelete));

        if (CollUtil.isNotEmpty(needAdd)) {

            needAdd.forEach(x -> x.setWmsUnitConversionOrderId(wmsUnitConversionOrderDto.getWmsUnitConversionOrderId()));
            List<WmsUnitConversionOrderItem> wmsUnitConversionOrderItems = BeanUtil.copyToList(needAdd, WmsUnitConversionOrderItem.class);
            wmsUnitConversionOrderItems.forEach(x -> wmsUnitConversionOrderItemMapper.insertSelective(x));
        }

        if (CollUtil.isNotEmpty(needUpdate)) {

            List<WmsUnitConversionOrderItem> wmsUnitConversionOrderItems = BeanUtil.copyToList(needUpdate, WmsUnitConversionOrderItem.class);
            wmsUnitConversionOrderItems.forEach(x -> wmsUnitConversionOrderItemMapper.updateByPrimaryKeySelective(x));
        }

        if (CollUtil.isNotEmpty(needDelete)) {

            List<WmsUnitConversionOrderItem> update = needDelete.stream().map(x -> {
                WmsUnitConversionOrderItem wmsUnitConversionOrderItem = new WmsUnitConversionOrderItem();
                wmsUnitConversionOrderItem.setWmsUnitConversionOrderItemId(x.getWmsUnitConversionOrderItemId());
                wmsUnitConversionOrderItem.setIsDelete(1);
                return wmsUnitConversionOrderItem;
            }).collect(Collectors.toList());
            update.forEach(x -> wmsUnitConversionOrderItemMapper.updateByPrimaryKeySelective(x));
        }

    }

    /**
     * 编辑入参校验
     *
     * @param wmsUnitConversionOrderDto 数据
     */
    private void checkEditData(WmsUnitConversionOrderDto wmsUnitConversionOrderDto) {

        Assert.notNull(wmsUnitConversionOrderDto, "入参数据可以未空");
        Assert.notNull(wmsUnitConversionOrderDto.getWmsUnitConversionOrderId(), "主键不可为空");
        Assert.notEmpty(wmsUnitConversionOrderDto.getReason(), "申请原因不可为空");
        Assert.notNull(wmsUnitConversionOrderDto.getOrderType(), "申请类型不可为空");
        Assert.notEmpty(wmsUnitConversionOrderDto.getWmsUnitConversionOrderItemDtoList(), "转换单明细不可为空");
    }

    @Override
    public WmsUnitConversionOrderDto getViewData(Integer wmsUnitConversionOrderId) {

        if (Objects.isNull(wmsUnitConversionOrderId)) {
            throw new ServiceException("主键不可为空");
        }

        WmsUnitConversionOrderDto data = wmsUnitConversionOrderMapper.selectByWmsUnitConversionOrderId(wmsUnitConversionOrderId);

        List<WmsUnitConversionOrderItemDto> wmsUnitConversionOrderItemDtoList = data.getWmsUnitConversionOrderItemDtoList();
        //  库存数据 封装
        Map<String, WarehouseStock> logicalStockMapInfo = warehouseStockService.getLogicalStockMapInfo(wmsUnitConversionOrderItemDtoList.stream().map(WmsUnitConversionOrderItemDto::getSourceSkuNo).distinct().collect(Collectors.toList()));

        wmsUnitConversionOrderItemDtoList.forEach(x -> {
            String sourceSkuNo = x.getSourceSkuNo();
            int hg = logicalStockMapInfo.get(sourceSkuNo + LogicalEnum.HG.getLogicalWarehouseId()) == null ? 0 : logicalStockMapInfo.get(sourceSkuNo + LogicalEnum.HG.getLogicalWarehouseId()).getAvailableStockNum();
            int jxq = logicalStockMapInfo.get(sourceSkuNo + LogicalEnum.JXQ.getLogicalWarehouseId()) == null ? 0 : logicalStockMapInfo.get(sourceSkuNo + LogicalEnum.JXQ.getLogicalWarehouseId()).getAvailableStockNum();
            x.setStockNum(hg + jxq);
        });

        return data;
    }

    @Override
    public List<WarehouseGoodsOperateLog> getOutLog(Integer wmsUnitConversionOrderId) {

        log.info("getOutLog 入参：{}", JSON.toJSONString(wmsUnitConversionOrderId));
        List<WarehouseGoodsOperateLog> wmsUnitConversionOutLog = warehouseGoodsOperateLogMapper.getWmsUnitConversionOutLog(wmsUnitConversionOrderId);
        skuData(wmsUnitConversionOutLog);
        log.info("getOutLog result：{}", JSON.toJSONString(wmsUnitConversionOutLog));
        return wmsUnitConversionOutLog;
    }

    /**
     * 绑定sku信息
     *
     * @param wmsUnitConversionOutLog 数据
     */
    private void skuData(List<WarehouseGoodsOperateLog> wmsUnitConversionOutLog) {

        List<Integer> skuIds = wmsUnitConversionOutLog.stream().map(WarehouseGoodsOperateLog::getGoodsId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(skuIds)) {
            List<Map<String, Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);
            Map<String, Map<String, Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key -> key.get("SKU_NO").toString(), v -> v, (k, v) -> v));
            for (WarehouseGoodsOperateLog warehouseGoodsOperateLog : wmsUnitConversionOutLog) {
                Map<String, Object> stringObjectMap = newSkuInfosMap.get(warehouseGoodsOperateLog.getSku());
                if (CollUtil.isNotEmpty(stringObjectMap)) {
                    warehouseGoodsOperateLog.setBrandName(Objects.isNull(stringObjectMap.get("BRAND_NAME")) ? "" : stringObjectMap.get("BRAND_NAME").toString());
                    warehouseGoodsOperateLog.setModel(Objects.isNull(stringObjectMap.get("MODEL")) ? "" : stringObjectMap.get("MODEL").toString());
                    warehouseGoodsOperateLog.setUnitName(Objects.isNull(stringObjectMap.get("UNIT_NAME")) ? "" : stringObjectMap.get("UNIT_NAME").toString());
                    warehouseGoodsOperateLog.setGoodsName(Objects.isNull(stringObjectMap.get("SHOW_NAME")) ? "" : stringObjectMap.get("SHOW_NAME").toString());
                }
            }

        }
    }

    @Override
    public List<WarehouseGoodsOperateLog> getInLog(Integer wmsUnitConversionOrderId) {
        log.info("getInLog 入参：{}", JSON.toJSONString(wmsUnitConversionOrderId));
        List<WarehouseGoodsOperateLog> wmsUnitConversionOutLog = warehouseGoodsOperateLogMapper.getWmsUnitConversionInLog(wmsUnitConversionOrderId);
        skuData(wmsUnitConversionOutLog);
        log.info("getInLog result：{}", JSON.toJSONString(wmsUnitConversionOutLog));
        return wmsUnitConversionOutLog;
    }

    @Override
    @Transactional
    public void putWmsTask(Integer wmsUnitConversionOrderId) throws Exception {

        // 出库
        HandlerStepContext outData = new HandlerStepContext();
        outData.put("wmsUnitConversionOrderId", wmsUnitConversionOrderId);
        HandlerStep handlerStep = stepBuildFactory.buildUnitConversionOutOrder();
        handlerStep.dealWith(outData);

        // 入库
        HandlerStepContext inData = new HandlerStepContext();
        inData.put("wmsUnitConversionOrderId", wmsUnitConversionOrderId);
        HandlerStep handlerInStep = stepBuildFactory.buildUnitConversionInOrder();
        handlerInStep.dealWith(inData);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void applyAudit(HttpServletRequest request, Integer wmsUnitConversionOrderId) {
        Assert.notNull(wmsUnitConversionOrderId, "库存转换单id不可为空");
        CurrentUser user = CurrentUser.getCurrentUser();
        WmsUnitConversionOrderDto conversionOrder = wmsUnitConversionOrderMapper.selectByWmsUnitConversionOrderId(wmsUnitConversionOrderId);
        try {
            Map<String, Object> variableMap = new HashMap<>();
            // 开始生成流程(如果没有taskId表示新流程需要生成)
            variableMap.put("currentAssinee", user.getUsername());
            variableMap.put("processDefinitionKey", "wmsUnitConversionVerify");
            variableMap.put("businessKey", "wmsUnitConversionVerify" + "_" + wmsUnitConversionOrderId);
            variableMap.put("relateTableKey", wmsUnitConversionOrderId);
            variableMap.put("relateTable", "T_WMS_UNIT_CONVERSION_ORDER");
            variableMap.put("orgId", user.getOrgId());
            variableMap.put("conversionOrderNo", conversionOrder.getWmsUnitConversionOrderNo());
            variableMap.put("conversionOrderId", conversionOrder.getWmsUnitConversionOrderId());
            actionProcdefService.createProcessInstance(request, "wmsUnitConversionVerify", "wmsUnitConversionVerify" + "_" + wmsUnitConversionOrderId, variableMap);
            // 修改审核状态
            this.audit(wmsUnitConversionOrderId, 1);

            // 默认申请人通过
            // 根据BusinessKey获取生成的审核实例
            Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "wmsUnitConversionVerify" + "_" + wmsUnitConversionOrderId);
            if (historicInfo.get("endStatus") != "审核完成") {
                Task taskInfo = (Task) historicInfo.get("taskInfo");
                String taskId = taskInfo.getId();
                Authentication.setAuthenticatedUserId(user.getUsername());
                Map<String, Object> variables = new HashMap<>();
                // 默认审批通过
                ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, "", user.getUsername(), variables);
                // 如果未结束添加审核对应主表的审核状态
                if (!"endEvent".equals(complementStatus.getData())) {
                    verifiesRecordService.saveVerifiesInfo(taskId, 0);
                }
            }
        } catch (Exception e) {
            log.error("库存转换单申请审核失败:", e);
        }
    }

    @Override
    public Integer getSkuStockNum(String skuNo) {
        Map<String, WarehouseStock> logicalStockMapInfo = warehouseStockService.getLogicalStockMapInfo(Collections.singletonList(skuNo));
        int hg = logicalStockMapInfo.get(skuNo + LogicalEnum.HG.getLogicalWarehouseId()) == null ? 0 : logicalStockMapInfo.get(skuNo + LogicalEnum.HG.getLogicalWarehouseId()).getAvailableStockNum();
        int jxq = logicalStockMapInfo.get(skuNo + LogicalEnum.JXQ.getLogicalWarehouseId()) == null ? 0 : logicalStockMapInfo.get(skuNo + LogicalEnum.JXQ.getLogicalWarehouseId()).getAvailableStockNum();
        return hg + jxq;
    }

    @Override
    public void audit(Integer wmsUnitConversionOrderId, Integer checkStatus) {
        Assert.notNull(wmsUnitConversionOrderId, "单位转换单id不可为空");
        WmsUnitConversionOrder update = new WmsUnitConversionOrder();
        update.setWmsUnitConversionOrderId(wmsUnitConversionOrderId);
        update.setVerifyStatus(checkStatus);
        wmsUnitConversionOrderMapper.updateByPrimaryKeySelective(update);
    }
}
