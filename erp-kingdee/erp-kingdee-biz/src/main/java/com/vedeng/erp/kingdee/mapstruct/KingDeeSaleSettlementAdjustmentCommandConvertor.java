package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeeSaleSettlementAdjustmentCommand;
import com.vedeng.erp.kingdee.dto.KingDeeSaleSettlementAdjustmentDto;
import com.vedeng.erp.kingdee.dto.KingDeeSaleSettlementAdjustmentEntityDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface KingDeeSaleSettlementAdjustmentCommandConvertor extends BaseCommandMapStruct<KingDeeSaleSettlementAdjustmentCommand, KingDeeSaleSettlementAdjustmentDto> {
    @Mapping(target = "FID", source = "fid")
    @Mapping(target = "f_QZOK_JDXSCKDID", source = "FQzokJdxsckdid")
    @Mapping(target = "f_QZOK_Date", source = "FQzokDate")
    @Mapping(target = "f_QZOK_BDDJTID", source = "FQzokBddjtid")
    @Mapping(target = "FBillNo", source = "FBillNo")
    @Mapping(target = "FEntity",source = "FEntityList")
    KingDeeSaleSettlementAdjustmentCommand toCommand(KingDeeSaleSettlementAdjustmentDto dto);

    @Mapping(target = "f_QZOK_GSYWDH", source = "FQzokGsywdh")
    @Mapping(target = "f_QZOK_YSDDH", source = "FQzokYsddh")
    @Mapping(target = "f_QZOK_YWLX", source = "FGzokYwlx")
    @Mapping(target = "f_QZOK_SPDM", source = "FQzokSpdm")
    @Mapping(target = "f_QZOK_SPMC", source = "FQzokSpmc")
    @Mapping(target = "f_QZOK_PCH", source = "FQzokPch")
    @Mapping(target = "f_QZOK_SN", source = "FQzokSn")
    @Mapping(target = "f_QZOK_BDDJHID", source = "FQzokBddjhid")
    @Mapping(target = "f_QZOK_JDCKDEntryid", source = "FQzokJdckdentryid")
    @Mapping(target = "f_QZOK_TZJE", source = "FQzokTzje")
    @Mapping(target = "f_QZOK_TZSE", source = "FQzokTzse")
    @Mapping(target = "f_QZOK_TZJSHJ", source = "FQzokTzjshj")
    KingDeeSaleSettlementAdjustmentCommand.KingDeeSaleSettlementAdjustmentEntityCommand toCommand(KingDeeSaleSettlementAdjustmentEntityDto dto);
}
