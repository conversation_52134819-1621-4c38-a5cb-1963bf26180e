package com.vedeng.trader.dao;

import com.vedeng.trader.dto.TraderInfoReqDto;
import com.vedeng.trader.dto.TraderInfoRespDto;
import com.vedeng.trader.model.TraderContact;
import com.vedeng.trader.model.vo.TraderContactVo;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TraderContactMapper {

    /**
     * <b>Description:</b><br> 获取traderid列表根据联系方式
     * @param traderContact
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2017年9月5日 上午10:34:47
     */
    List<Integer> getTraderIdListByContactWay(@Param("traderType") Integer traderType, @Param("mobile") String mobile);


    /**
     * <b>Description:</b><br> 获取traderid列表根据微信状态
     * @param traderContact
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2017年9月5日 上午10:34:47
     */
    List<Integer> getTraderIdListByWxStatus(@Param("traderType") Integer traderType, @Param("wxStatus") Integer wxStatus);

    /**
     * <b>Description:</b><br> 交易者联系人
     * @param traderContact
     * @return
     * @Note
     * <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年5月23日 下午3:10:39
     */
    List<TraderContact> getTraderContact(TraderContact traderContact);

    TraderContact selectByPrimaryKey(Integer traderContactId);

    /**
     * 查询联系人详情
     *
     * @param traderContact
     * @return
     */
    TraderContactVo getTraderContactVo(TraderContact traderContact);



    /**
     * <b>Description:</b><br> 查询是否有重复的联系人，名字和手机号重复
     * @param traderContact
     * @return
     * @Note
     * <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年5月23日 下午3:10:39
     */
    List<TraderContact> getTraderContactByNameAndMobile(TraderContact traderContact);

    int insertSelective(TraderContact record);


    /**
     * <b>Description:</b><br> 获取当前客户的联系人
     * @param traderId
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2017年5月23日 上午10:55:30
     */
    List<TraderContactVo> getTraderContactVoList(TraderContactVo traderContactVo);


    /**
     * <b>Description:</b><br> 获取当前客户的联系人 新增订单下拉框用
     * @param
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2023年2月23日 上午10:55:30
     */
    List<TraderContactVo> getTraderContactVoListForOrderSearch(TraderContactVo traderContactVo);

    /**
     * 号码获取归属销售信息
     *
     * @param phone
     * @return
     */
    List<Integer> getBelongUserIdListByPhone(String phone);

    /**
     * 手机号找出关联客户数量
     * @param phone
     * @return
     */
    Integer getTraderCountByPhone(String phone);
    
    void updatePosition(@Param("position") String position, @Param("traderContactId") Integer traderContactId);


    List<TraderInfoRespDto> getBelongTrader(TraderInfoReqDto traderInfoReqDto);

}
