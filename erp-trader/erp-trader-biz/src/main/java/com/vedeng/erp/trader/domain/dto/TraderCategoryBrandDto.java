package com.vedeng.erp.trader.domain.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * 大数据接口返回的  时间段内 交易分类和品牌信息
 * @version 1.0
 * @date 2023/8/15 10:41
 */
@Getter
@Setter
public class TraderCategoryBrandDto {

    /**
     * 近7天内交易分类
     */
    private List<String> sevenCategory;

    /**
     * 近7天内交易品牌
     */
    private List<String> sevenBrand;

    /**
     * 近30天内交易分类
     */
    private List<String> thirtyCategory;

    /**
     * 近30天内交易品牌
     */
    private List<String> thirtyBrand;

    /**
     * 近60天内交易分类
     */
    private List<String> sixtyCategory;

    /**
     * 近60天内交易品牌
     */
    private List<String> sixtyBrand;

    /**
     * 近90天内交易分类
     */
    private List<String> ninetyCategory;

    /**
     * 近90天内交易品牌
     */
    private List<String> ninetyBrand;

    /**
     * 近180天内交易分类
     */
    private List<String> halfYearCategory;

    /**
     * 近180天内交易品牌
     */
    private List<String> halfYearBrand;

    /**
     * 近365天内交易分类
     */
    private List<String> oneYearCategory;

    /**
     * 近365天内交易品牌
     */
    private List<String> oneYearBrand;

    /**
     * 近730天内交易分类
     */
    private List<String> twoYearCategory;

    /**
     * 近730天内交易品牌
     */
    private List<String> twoYearBrand;

    /**
     * 近一年交易但近一月无交易分类
     */
    private List<String> specialCategory;
}