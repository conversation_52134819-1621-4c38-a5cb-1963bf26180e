package com.vedeng.erp.trader.domain.entity;

import java.util.Date;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;


/**
 * @description 交易者基础信息
 * <AUTHOR>
 * @date 2023/8/11 13:24
 **/

@Getter
@Setter
@ToString
@NoArgsConstructor
public class TraderEntity {
    /**
     * 交易者ID
     */
    private Integer traderId;

    /**
     * ERP公司ID(T_COMPANY)
     */
    private Integer companyId;

    /**
     * 交易者的父节点
     */
    private Integer parentId;

    /**
     * 是否有效 0否 1是
     */
    private Integer isEnable;

    /**
     * 交易者名称
     */
    private String traderName;

    /**
     * 地区最小级ID
     */
    private Integer areaId;

    /**
     * 多级地址逗号“,”拼接（冗余字段）
     */
    private String areaIds;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 三证合一 0：否 1：是
     */
    private Integer threeInOne;

    /**
     * 医疗资质合一 0否 1是
     */
    private Integer medicalQualification;

    /**
     * 来源：0ERP，1耗材商城，2CRM拓客
     */
    private Integer source;

    /**
     * 耗材商城客户ID
     */
    private Integer accountCompanyId;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 添加人
     */
    private Integer creator;

    /**
     * 最近一次编辑时间
     */
    private Long modTime;

    /**
     * 最近一次编辑人
     */
    private Integer updater;

    /**
     * 认证状态：0 未审核 1 待审核 2 审核通过 3 审核不通过
     */
    private Integer traderStatus;

    /**
     * 归属平台(1贝登医疗，2医械购，3科研购，4集团业务部，5其他，6集采)
     */
    private Integer belongPlatform;

    /**
     * 最近沟通时间
     */
    private Long lastCommunicateTime;

    /**
     * 代付款证明审核状态,医械购财务审核状态,0 未审核 1 待审核 2 审核通过 3 审核不通过
     */
    private Integer payofStatus;

    /**
     * 代付款证明审核备注信息
     */
    private String payofCheckMsg;

    /**
     * 客户审核备注信息（医械购）
     */
    private String traderCheckMsg;

    /**
     * 仓库地区最小集id
     */
    private Integer warehouseAreaId;

    /**
     * 仓库地区 多级地址逗号,拼接（冗余字段）
     */
    private String warehouseAreaIds;

    /**
     * 仓库的详细地址
     */
    private String warehouseDetailAddress;

    /**
     * 最后一次通过审核时间
     */
    private Date lastValidTime;

    /**
     * 客户属性 0是总公司，1是分公司
     */
    private Integer traderType;
}