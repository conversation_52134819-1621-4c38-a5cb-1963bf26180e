<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.finance.mapper.InvoiceMapper">


    <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.InvoiceEntity">
        <!--@mbg.generated-->
        <!--@Table T_INVOICE-->
        <id column="INVOICE_ID" jdbcType="INTEGER" property="invoiceId" />
        <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
        <result column="INVOICE_CODE" jdbcType="VARCHAR" property="invoiceCode" />
        <result column="INVOICE_FLOW" jdbcType="VARCHAR" property="invoiceFlow" />
        <result column="INVOICE_PROPERTY" jdbcType="BOOLEAN" property="invoiceProperty" />
        <result column="INVOICE_HREF" jdbcType="VARCHAR" property="invoiceHref" />
        <result column="TYPE" jdbcType="INTEGER" property="type" />
        <result column="TAG" jdbcType="BOOLEAN" property="tag" />
        <result column="RELATED_ID" jdbcType="INTEGER" property="relatedId" />
        <result column="AFTER_SALES_ID" jdbcType="INTEGER" property="afterSalesId" />
        <result column="INVOICE_NO" jdbcType="VARCHAR" property="invoiceNo" />
        <result column="INVOICE_TYPE" jdbcType="INTEGER" property="invoiceType" />
        <result column="RATIO" jdbcType="DECIMAL" property="ratio" />
        <result column="COLOR_TYPE" jdbcType="BOOLEAN" property="colorType" />
        <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
        <result column="IS_ENABLE" jdbcType="BOOLEAN" property="isEnable" />
        <result column="VALID_USERID" jdbcType="INTEGER" property="validUserid" />
        <result column="VALID_STATUS" jdbcType="BOOLEAN" property="validStatus" />
        <result column="VALID_TIME" jdbcType="BIGINT" property="validTime" />
        <result column="VALID_COMMENTS" jdbcType="VARCHAR" property="validComments" />
        <result column="INVOICE_PRINT_STATUS" jdbcType="BOOLEAN" property="invoicePrintStatus" />
        <result column="INVOICE_CANCEL_STATUS" jdbcType="BOOLEAN" property="invoiceCancelStatus" />
        <result column="EXPRESS_ID" jdbcType="INTEGER" property="expressId" />
        <result column="IS_AUTH" jdbcType="BOOLEAN" property="isAuth" />
        <result column="IS_MONTH_AUTH" jdbcType="BOOLEAN" property="isMonthAuth" />
        <result column="AUTH_TIME" jdbcType="BIGINT" property="authTime" />
        <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
        <result column="CREATOR" jdbcType="INTEGER" property="creator" />
        <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
        <result column="UPDATER" jdbcType="INTEGER" property="updater" />
        <result column="INVOICE_APPLY_ID" jdbcType="INTEGER" property="invoiceApplyId" />
        <result column="SEND_TIME" jdbcType="TIMESTAMP" property="sendTime" />
        <result column="AFTER_EXPRESS_ID" jdbcType="INTEGER" property="afterExpressId" />
        <result column="OSS_FILE_URL" jdbcType="VARCHAR" property="ossFileUrl" />
        <result column="RESOURCE_ID" jdbcType="VARCHAR" property="resourceId" />
        <result column="INVOICE_FROM" jdbcType="TINYINT" property="invoiceFrom" />
        <result column="HX_INVOICE_ID" jdbcType="INTEGER" property="hxInvoiceId" />
        <result column="AUTH_MODE" jdbcType="BOOLEAN" property="authMode" />
        <result column="AUTH_FAIL_REASON" jdbcType="VARCHAR" property="authFailReason" />
        <result column="IS_AUTHING" jdbcType="BOOLEAN" property="isAuthing" />
        <result column="COLOR_COMPLEMENT_TYPE" jdbcType="BOOLEAN" property="colorComplementType" />
        <result column="IS_AFTER_BUYORDER_ONLY" jdbcType="BOOLEAN" property="isAfterBuyorderOnly" />
        <result column="OPEN_INVOICE_TIME" jdbcType="TIMESTAMP" property="openInvoiceTime" />
    </resultMap>

    <resultMap id="invoiceAndDetail" type="com.vedeng.erp.finance.dto.InvoiceDto">
        <!--@mbg.generated-->
        <!--@Table T_INVOICE-->
        <id column="INVOICE_ID" jdbcType="INTEGER" property="invoiceId"/>
        <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId"/>
        <result column="INVOICE_CODE" jdbcType="VARCHAR" property="invoiceCode"/>
        <result column="INVOICE_FLOW" jdbcType="VARCHAR" property="invoiceFlow"/>
        <result column="INVOICE_PROPERTY" jdbcType="INTEGER" property="invoiceProperty"/>
        <result column="INVOICE_HREF" jdbcType="VARCHAR" property="invoiceHref"/>
        <result column="TYPE" jdbcType="INTEGER" property="type"/>
        <result column="TAG" jdbcType="INTEGER" property="tag"/>
        <result column="RELATED_ID" jdbcType="INTEGER" property="relatedId"/>
        <result column="AFTER_SALES_ID" jdbcType="INTEGER" property="afterSalesId"/>
        <result column="INVOICE_NO" jdbcType="VARCHAR" property="invoiceNo"/>
        <result column="INVOICE_TYPE" jdbcType="INTEGER" property="invoiceType"/>
        <result column="RATIO" jdbcType="DECIMAL" property="ratio"/>
        <result column="COLOR_TYPE" jdbcType="INTEGER" property="colorType"/>
        <result column="AMOUNT" jdbcType="DECIMAL" property="amount"/>
        <result column="IS_ENABLE" jdbcType="INTEGER" property="isEnable"/>
        <result column="VALID_USERID" jdbcType="INTEGER" property="validUserid"/>
        <result column="VALID_STATUS" jdbcType="INTEGER" property="validStatus"/>
        <result column="VALID_TIME" jdbcType="BIGINT" property="validTime"/>
        <result column="VALID_COMMENTS" jdbcType="VARCHAR" property="validComments"/>
        <result column="INVOICE_PRINT_STATUS" jdbcType="INTEGER" property="invoicePrintStatus"/>
        <result column="INVOICE_CANCEL_STATUS" jdbcType="INTEGER" property="invoiceCancelStatus"/>
        <result column="EXPRESS_ID" jdbcType="INTEGER" property="expressId"/>
        <result column="IS_AUTH" jdbcType="INTEGER" property="isAuth"/>
        <result column="IS_MONTH_AUTH" jdbcType="INTEGER" property="isMonthAuth"/>
        <result column="AUTH_TIME" jdbcType="BIGINT" property="authTime"/>
        <result column="ADD_TIME" jdbcType="BIGINT" property="addTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="MOD_TIME" jdbcType="BIGINT" property="modTime"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="INVOICE_APPLY_ID" jdbcType="INTEGER" property="invoiceApplyId"/>
        <result column="SEND_TIME" jdbcType="TIMESTAMP" property="sendTime"/>
        <result column="AFTER_EXPRESS_ID" jdbcType="INTEGER" property="afterExpressId"/>
        <result column="OSS_FILE_URL" jdbcType="VARCHAR" property="ossFileUrl"/>
        <result column="RESOURCE_ID" jdbcType="VARCHAR" property="resourceId"/>
        <result column="INVOICE_FROM" jdbcType="INTEGER" property="invoiceFrom"/>
        <result column="HX_INVOICE_ID" jdbcType="INTEGER" property="hxInvoiceId"/>
        <result column="AUTH_MODE" jdbcType="INTEGER" property="authMode"/>
        <result column="AUTH_FAIL_REASON" jdbcType="VARCHAR" property="authFailReason"/>
        <result column="IS_AUTHING" jdbcType="INTEGER" property="isAuthing"/>
        <result column="COLOR_COMPLEMENT_TYPE" jdbcType="INTEGER" property="colorComplementType"/>
        <result column="IS_AFTER_BUYORDER_ONLY" jdbcType="INTEGER" property="isAfterBuyorderOnly"/>
        <collection property="invoiceDetailDtos" ofType="com.vedeng.erp.finance.dto.InvoiceDetailDto">
            <id column="INVOICE_DETAIL_ID" jdbcType="INTEGER" property="invoiceDetailId" />
            <result column="INVOICE_ID" jdbcType="INTEGER" property="invoiceId" />
            <result column="DETAILGOODS_ID" jdbcType="INTEGER" property="detailgoodsId" />
            <result column="PRICE" jdbcType="DECIMAL" property="price" />
            <result column="NUM" jdbcType="DECIMAL" property="num" />
            <result column="TOTAL_AMOUNT" jdbcType="DECIMAL" property="totalAmount" />
            <result column="CHANGED_GOODS_NAME" jdbcType="VARCHAR" property="changedGoodsName" />
            <result column="SKU" jdbcType="VARCHAR" property="sku"/>
        </collection>
    </resultMap>
    <resultMap id="invoiceAndOrderNo" type="com.vedeng.erp.finance.dto.InvoiceDto">
        <!--@mbg.generated-->
        <!--@Table T_INVOICE-->
        <id column="INVOICE_ID" jdbcType="INTEGER" property="invoiceId"/>
        <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId"/>
        <result column="INVOICE_CODE" jdbcType="VARCHAR" property="invoiceCode"/>
        <result column="INVOICE_FLOW" jdbcType="VARCHAR" property="invoiceFlow"/>
        <result column="INVOICE_PROPERTY" jdbcType="INTEGER" property="invoiceProperty"/>
        <result column="INVOICE_HREF" jdbcType="VARCHAR" property="invoiceHref"/>
        <result column="TYPE" jdbcType="INTEGER" property="type"/>
        <result column="TAG" jdbcType="INTEGER" property="tag"/>
        <result column="RELATED_ID" jdbcType="INTEGER" property="relatedId"/>
        <result column="AFTER_SALES_ID" jdbcType="INTEGER" property="afterSalesId"/>
        <result column="INVOICE_NO" jdbcType="VARCHAR" property="invoiceNo"/>
        <result column="INVOICE_TYPE" jdbcType="INTEGER" property="invoiceType"/>
        <result column="RATIO" jdbcType="DECIMAL" property="ratio"/>
        <result column="COLOR_TYPE" jdbcType="INTEGER" property="colorType"/>
        <result column="AMOUNT" jdbcType="DECIMAL" property="amount"/>
        <result column="IS_ENABLE" jdbcType="INTEGER" property="isEnable"/>
        <result column="VALID_USERID" jdbcType="INTEGER" property="validUserid"/>
        <result column="VALID_STATUS" jdbcType="INTEGER" property="validStatus"/>
        <result column="VALID_TIME" jdbcType="BIGINT" property="validTime"/>
        <result column="VALID_COMMENTS" jdbcType="VARCHAR" property="validComments"/>
        <result column="INVOICE_PRINT_STATUS" jdbcType="INTEGER" property="invoicePrintStatus"/>
        <result column="INVOICE_CANCEL_STATUS" jdbcType="INTEGER" property="invoiceCancelStatus"/>
        <result column="EXPRESS_ID" jdbcType="INTEGER" property="expressId"/>
        <result column="IS_AUTH" jdbcType="INTEGER" property="isAuth"/>
        <result column="IS_MONTH_AUTH" jdbcType="INTEGER" property="isMonthAuth"/>
        <result column="AUTH_TIME" jdbcType="BIGINT" property="authTime"/>
        <result column="ADD_TIME" jdbcType="BIGINT" property="addTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="MOD_TIME" jdbcType="BIGINT" property="modTime"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="INVOICE_APPLY_ID" jdbcType="INTEGER" property="invoiceApplyId"/>
        <result column="SEND_TIME" jdbcType="TIMESTAMP" property="sendTime"/>
        <result column="AFTER_EXPRESS_ID" jdbcType="INTEGER" property="afterExpressId"/>
        <result column="OSS_FILE_URL" jdbcType="VARCHAR" property="ossFileUrl"/>
        <result column="RESOURCE_ID" jdbcType="VARCHAR" property="resourceId"/>
        <result column="INVOICE_FROM" jdbcType="INTEGER" property="invoiceFrom"/>
        <result column="HX_INVOICE_ID" jdbcType="INTEGER" property="hxInvoiceId"/>
        <result column="AUTH_MODE" jdbcType="INTEGER" property="authMode"/>
        <result column="AUTH_FAIL_REASON" jdbcType="VARCHAR" property="authFailReason"/>
        <result column="IS_AUTHING" jdbcType="INTEGER" property="isAuthing"/>
        <result column="COLOR_COMPLEMENT_TYPE" jdbcType="INTEGER" property="colorComplementType"/>
        <result column="IS_AFTER_BUYORDER_ONLY" jdbcType="INTEGER" property="isAfterBuyorderOnly"/>
        <result column="SALEORDER_NO" jdbcType="VARCHAR" property="orderNo"/>
        <result column="AFTER_SALES_NO" jdbcType="VARCHAR" property="afterSalesNo"/>
    </resultMap>

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        INVOICE_ID, COMPANY_ID, INVOICE_CODE, INVOICE_FLOW, INVOICE_PROPERTY, INVOICE_HREF,
        `TYPE`, TAG, RELATED_ID, AFTER_SALES_ID, INVOICE_NO, INVOICE_TYPE, RATIO, COLOR_TYPE,
        AMOUNT, IS_ENABLE, VALID_USERID, VALID_STATUS, VALID_TIME, VALID_COMMENTS, INVOICE_PRINT_STATUS,
        INVOICE_CANCEL_STATUS, EXPRESS_ID, IS_AUTH, IS_MONTH_AUTH, AUTH_TIME, ADD_TIME, CREATOR,
        MOD_TIME, UPDATER, INVOICE_APPLY_ID, SEND_TIME, AFTER_EXPRESS_ID, OSS_FILE_URL, RESOURCE_ID,
        INVOICE_FROM, HX_INVOICE_ID, AUTH_MODE, AUTH_FAIL_REASON, IS_AUTHING, COLOR_COMPLEMENT_TYPE,
        IS_AFTER_BUYORDER_ONLY, OPEN_INVOICE_TIME
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List" />
        from T_INVOICE
        where INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from T_INVOICE
        where INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="INVOICE_ID" keyProperty="invoiceId" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_INVOICE (COMPANY_ID, INVOICE_CODE, INVOICE_FLOW,
        INVOICE_PROPERTY, INVOICE_HREF, `TYPE`,
        TAG, RELATED_ID, AFTER_SALES_ID,
        INVOICE_NO, INVOICE_TYPE, RATIO,
        COLOR_TYPE, AMOUNT, IS_ENABLE,
        VALID_USERID, VALID_STATUS, VALID_TIME,
        VALID_COMMENTS, INVOICE_PRINT_STATUS, INVOICE_CANCEL_STATUS,
        EXPRESS_ID, IS_AUTH, IS_MONTH_AUTH,
        AUTH_TIME, ADD_TIME, CREATOR,
        MOD_TIME, UPDATER, INVOICE_APPLY_ID,
        SEND_TIME, AFTER_EXPRESS_ID, OSS_FILE_URL,
        RESOURCE_ID, INVOICE_FROM, HX_INVOICE_ID,
        AUTH_MODE, AUTH_FAIL_REASON, IS_AUTHING,
        COLOR_COMPLEMENT_TYPE, IS_AFTER_BUYORDER_ONLY,
        OPEN_INVOICE_TIME)
        values (#{companyId,jdbcType=INTEGER}, #{invoiceCode,jdbcType=VARCHAR}, #{invoiceFlow,jdbcType=VARCHAR},
        #{invoiceProperty,jdbcType=BOOLEAN}, #{invoiceHref,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER},
        #{tag,jdbcType=BOOLEAN}, #{relatedId,jdbcType=INTEGER}, #{afterSalesId,jdbcType=INTEGER},
        #{invoiceNo,jdbcType=VARCHAR}, #{invoiceType,jdbcType=INTEGER}, #{ratio,jdbcType=DECIMAL},
        #{colorType,jdbcType=BOOLEAN}, #{amount,jdbcType=DECIMAL}, #{isEnable,jdbcType=BOOLEAN},
        #{validUserid,jdbcType=INTEGER}, #{validStatus,jdbcType=BOOLEAN}, #{validTime,jdbcType=BIGINT},
        #{validComments,jdbcType=VARCHAR}, #{invoicePrintStatus,jdbcType=BOOLEAN}, #{invoiceCancelStatus,jdbcType=BOOLEAN},
        #{expressId,jdbcType=INTEGER}, #{isAuth,jdbcType=BOOLEAN}, #{isMonthAuth,jdbcType=BOOLEAN},
        #{authTime,jdbcType=BIGINT}, #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER},
        #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}, #{invoiceApplyId,jdbcType=INTEGER},
        #{sendTime,jdbcType=TIMESTAMP}, #{afterExpressId,jdbcType=INTEGER}, #{ossFileUrl,jdbcType=VARCHAR},
        #{resourceId,jdbcType=VARCHAR}, #{invoiceFrom,jdbcType=TINYINT}, #{hxInvoiceId,jdbcType=INTEGER},
        #{authMode,jdbcType=BOOLEAN}, #{authFailReason,jdbcType=VARCHAR}, #{isAuthing,jdbcType=BOOLEAN},
        #{colorComplementType,jdbcType=BOOLEAN}, #{isAfterBuyorderOnly,jdbcType=BOOLEAN},
        #{openInvoiceTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="INVOICE_ID" keyProperty="invoiceId" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_INVOICE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                COMPANY_ID,
            </if>
            <if test="invoiceCode != null and invoiceCode != ''">
                INVOICE_CODE,
            </if>
            <if test="invoiceFlow != null and invoiceFlow != ''">
                INVOICE_FLOW,
            </if>
            <if test="invoiceProperty != null">
                INVOICE_PROPERTY,
            </if>
            <if test="invoiceHref != null and invoiceHref != ''">
                INVOICE_HREF,
            </if>
            <if test="type != null">
                `TYPE`,
            </if>
            <if test="tag != null">
                TAG,
            </if>
            <if test="relatedId != null">
                RELATED_ID,
            </if>
            <if test="afterSalesId != null">
                AFTER_SALES_ID,
            </if>
            <if test="invoiceNo != null and invoiceNo != ''">
                INVOICE_NO,
            </if>
            <if test="invoiceType != null">
                INVOICE_TYPE,
            </if>
            <if test="ratio != null">
                RATIO,
            </if>
            <if test="colorType != null">
                COLOR_TYPE,
            </if>
            <if test="amount != null">
                AMOUNT,
            </if>
            <if test="isEnable != null">
                IS_ENABLE,
            </if>
            <if test="validUserid != null">
                VALID_USERID,
            </if>
            <if test="validStatus != null">
                VALID_STATUS,
            </if>
            <if test="validTime != null">
                VALID_TIME,
            </if>
            <if test="validComments != null and validComments != ''">
                VALID_COMMENTS,
            </if>
            <if test="invoicePrintStatus != null">
                INVOICE_PRINT_STATUS,
            </if>
            <if test="invoiceCancelStatus != null">
                INVOICE_CANCEL_STATUS,
            </if>
            <if test="expressId != null">
                EXPRESS_ID,
            </if>
            <if test="isAuth != null">
                IS_AUTH,
            </if>
            <if test="isMonthAuth != null">
                IS_MONTH_AUTH,
            </if>
            <if test="authTime != null">
                AUTH_TIME,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="invoiceApplyId != null">
                INVOICE_APPLY_ID,
            </if>
            <if test="sendTime != null">
                SEND_TIME,
            </if>
            <if test="afterExpressId != null">
                AFTER_EXPRESS_ID,
            </if>
            <if test="ossFileUrl != null and ossFileUrl != ''">
                OSS_FILE_URL,
            </if>
            <if test="resourceId != null and resourceId != ''">
                RESOURCE_ID,
            </if>
            <if test="invoiceFrom != null">
                INVOICE_FROM,
            </if>
            <if test="hxInvoiceId != null">
                HX_INVOICE_ID,
            </if>
            <if test="authMode != null">
                AUTH_MODE,
            </if>
            <if test="authFailReason != null and authFailReason != ''">
                AUTH_FAIL_REASON,
            </if>
            <if test="isAuthing != null">
                IS_AUTHING,
            </if>
            <if test="colorComplementType != null">
                COLOR_COMPLEMENT_TYPE,
            </if>
            <if test="isAfterBuyorderOnly != null">
                IS_AFTER_BUYORDER_ONLY,
            </if>
            <if test="openInvoiceTime != null">
                OPEN_INVOICE_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=INTEGER},
            </if>
            <if test="invoiceCode != null and invoiceCode != ''">
                #{invoiceCode,jdbcType=VARCHAR},
            </if>
            <if test="invoiceFlow != null and invoiceFlow != ''">
                #{invoiceFlow,jdbcType=VARCHAR},
            </if>
            <if test="invoiceProperty != null">
                #{invoiceProperty,jdbcType=BOOLEAN},
            </if>
            <if test="invoiceHref != null and invoiceHref != ''">
                #{invoiceHref,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="tag != null">
                #{tag,jdbcType=BOOLEAN},
            </if>
            <if test="relatedId != null">
                #{relatedId,jdbcType=INTEGER},
            </if>
            <if test="afterSalesId != null">
                #{afterSalesId,jdbcType=INTEGER},
            </if>
            <if test="invoiceNo != null and invoiceNo != ''">
                #{invoiceNo,jdbcType=VARCHAR},
            </if>
            <if test="invoiceType != null">
                #{invoiceType,jdbcType=INTEGER},
            </if>
            <if test="ratio != null">
                #{ratio,jdbcType=DECIMAL},
            </if>
            <if test="colorType != null">
                #{colorType,jdbcType=BOOLEAN},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="isEnable != null">
                #{isEnable,jdbcType=BOOLEAN},
            </if>
            <if test="validUserid != null">
                #{validUserid,jdbcType=INTEGER},
            </if>
            <if test="validStatus != null">
                #{validStatus,jdbcType=BOOLEAN},
            </if>
            <if test="validTime != null">
                #{validTime,jdbcType=BIGINT},
            </if>
            <if test="validComments != null and validComments != ''">
                #{validComments,jdbcType=VARCHAR},
            </if>
            <if test="invoicePrintStatus != null">
                #{invoicePrintStatus,jdbcType=BOOLEAN},
            </if>
            <if test="invoiceCancelStatus != null">
                #{invoiceCancelStatus,jdbcType=BOOLEAN},
            </if>
            <if test="expressId != null">
                #{expressId,jdbcType=INTEGER},
            </if>
            <if test="isAuth != null">
                #{isAuth,jdbcType=BOOLEAN},
            </if>
            <if test="isMonthAuth != null">
                #{isMonthAuth,jdbcType=BOOLEAN},
            </if>
            <if test="authTime != null">
                #{authTime,jdbcType=BIGINT},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="invoiceApplyId != null">
                #{invoiceApplyId,jdbcType=INTEGER},
            </if>
            <if test="sendTime != null">
                #{sendTime,jdbcType=TIMESTAMP},
            </if>
            <if test="afterExpressId != null">
                #{afterExpressId,jdbcType=INTEGER},
            </if>
            <if test="ossFileUrl != null and ossFileUrl != ''">
                #{ossFileUrl,jdbcType=VARCHAR},
            </if>
            <if test="resourceId != null and resourceId != ''">
                #{resourceId,jdbcType=VARCHAR},
            </if>
            <if test="invoiceFrom != null">
                #{invoiceFrom,jdbcType=TINYINT},
            </if>
            <if test="hxInvoiceId != null">
                #{hxInvoiceId,jdbcType=INTEGER},
            </if>
            <if test="authMode != null">
                #{authMode,jdbcType=BOOLEAN},
            </if>
            <if test="authFailReason != null and authFailReason != ''">
                #{authFailReason,jdbcType=VARCHAR},
            </if>
            <if test="isAuthing != null">
                #{isAuthing,jdbcType=BOOLEAN},
            </if>
            <if test="colorComplementType != null">
                #{colorComplementType,jdbcType=BOOLEAN},
            </if>
            <if test="isAfterBuyorderOnly != null">
                #{isAfterBuyorderOnly,jdbcType=BOOLEAN},
            </if>
            <if test="openInvoiceTime != null">
                #{openInvoiceTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceEntity">
        <!--@mbg.generated-->
        update T_INVOICE
        <set>
            <if test="companyId != null">
                COMPANY_ID = #{companyId,jdbcType=INTEGER},
            </if>
            <if test="invoiceCode != null and invoiceCode != ''">
                INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR},
            </if>
            <if test="invoiceFlow != null and invoiceFlow != ''">
                INVOICE_FLOW = #{invoiceFlow,jdbcType=VARCHAR},
            </if>
            <if test="invoiceProperty != null">
                INVOICE_PROPERTY = #{invoiceProperty,jdbcType=BOOLEAN},
            </if>
            <if test="invoiceHref != null and invoiceHref != ''">
                INVOICE_HREF = #{invoiceHref,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                `TYPE` = #{type,jdbcType=INTEGER},
            </if>
            <if test="tag != null">
                TAG = #{tag,jdbcType=BOOLEAN},
            </if>
            <if test="relatedId != null">
                RELATED_ID = #{relatedId,jdbcType=INTEGER},
            </if>
            <if test="afterSalesId != null">
                AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
            </if>
            <if test="invoiceNo != null and invoiceNo != ''">
                INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR},
            </if>
            <if test="invoiceType != null">
                INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
            </if>
            <if test="ratio != null">
                RATIO = #{ratio,jdbcType=DECIMAL},
            </if>
            <if test="colorType != null">
                COLOR_TYPE = #{colorType,jdbcType=BOOLEAN},
            </if>
            <if test="amount != null">
                AMOUNT = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="isEnable != null">
                IS_ENABLE = #{isEnable,jdbcType=BOOLEAN},
            </if>
            <if test="validUserid != null">
                VALID_USERID = #{validUserid,jdbcType=INTEGER},
            </if>
            <if test="validStatus != null">
                VALID_STATUS = #{validStatus,jdbcType=BOOLEAN},
            </if>
            <if test="validTime != null">
                VALID_TIME = #{validTime,jdbcType=BIGINT},
            </if>
            <if test="validComments != null and validComments != ''">
                VALID_COMMENTS = #{validComments,jdbcType=VARCHAR},
            </if>
            <if test="invoicePrintStatus != null">
                INVOICE_PRINT_STATUS = #{invoicePrintStatus,jdbcType=BOOLEAN},
            </if>
            <if test="invoiceCancelStatus != null">
                INVOICE_CANCEL_STATUS = #{invoiceCancelStatus,jdbcType=BOOLEAN},
            </if>
            <if test="expressId != null">
                EXPRESS_ID = #{expressId,jdbcType=INTEGER},
            </if>
            <if test="isAuth != null">
                IS_AUTH = #{isAuth,jdbcType=BOOLEAN},
            </if>
            <if test="isMonthAuth != null">
                IS_MONTH_AUTH = #{isMonthAuth,jdbcType=BOOLEAN},
            </if>
            <if test="authTime != null">
                AUTH_TIME = #{authTime,jdbcType=BIGINT},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="invoiceApplyId != null">
                INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER},
            </if>
            <if test="sendTime != null">
                SEND_TIME = #{sendTime,jdbcType=TIMESTAMP},
            </if>
            <if test="afterExpressId != null">
                AFTER_EXPRESS_ID = #{afterExpressId,jdbcType=INTEGER},
            </if>
            <if test="ossFileUrl != null and ossFileUrl != ''">
                OSS_FILE_URL = #{ossFileUrl,jdbcType=VARCHAR},
            </if>
            <if test="resourceId != null and resourceId != ''">
                RESOURCE_ID = #{resourceId,jdbcType=VARCHAR},
            </if>
            <if test="invoiceFrom != null">
                INVOICE_FROM = #{invoiceFrom,jdbcType=TINYINT},
            </if>
            <if test="hxInvoiceId != null">
                HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER},
            </if>
            <if test="authMode != null">
                AUTH_MODE = #{authMode,jdbcType=BOOLEAN},
            </if>
            <if test="authFailReason != null and authFailReason != ''">
                AUTH_FAIL_REASON = #{authFailReason,jdbcType=VARCHAR},
            </if>
            <if test="isAuthing != null">
                IS_AUTHING = #{isAuthing,jdbcType=BOOLEAN},
            </if>
            <if test="colorComplementType != null">
                COLOR_COMPLEMENT_TYPE = #{colorComplementType,jdbcType=BOOLEAN},
            </if>
            <if test="isAfterBuyorderOnly != null">
                IS_AFTER_BUYORDER_ONLY = #{isAfterBuyorderOnly,jdbcType=BOOLEAN},
            </if>
            <if test="openInvoiceTime != null">
                OPEN_INVOICE_TIME = #{openInvoiceTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceEntity">
        <!--@mbg.generated-->
        update T_INVOICE
        set COMPANY_ID = #{companyId,jdbcType=INTEGER},
        INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR},
        INVOICE_FLOW = #{invoiceFlow,jdbcType=VARCHAR},
        INVOICE_PROPERTY = #{invoiceProperty,jdbcType=BOOLEAN},
        INVOICE_HREF = #{invoiceHref,jdbcType=VARCHAR},
        `TYPE` = #{type,jdbcType=INTEGER},
        TAG = #{tag,jdbcType=BOOLEAN},
        RELATED_ID = #{relatedId,jdbcType=INTEGER},
        AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
        INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR},
        INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
        RATIO = #{ratio,jdbcType=DECIMAL},
        COLOR_TYPE = #{colorType,jdbcType=BOOLEAN},
        AMOUNT = #{amount,jdbcType=DECIMAL},
        IS_ENABLE = #{isEnable,jdbcType=BOOLEAN},
        VALID_USERID = #{validUserid,jdbcType=INTEGER},
        VALID_STATUS = #{validStatus,jdbcType=BOOLEAN},
        VALID_TIME = #{validTime,jdbcType=BIGINT},
        VALID_COMMENTS = #{validComments,jdbcType=VARCHAR},
        INVOICE_PRINT_STATUS = #{invoicePrintStatus,jdbcType=BOOLEAN},
        INVOICE_CANCEL_STATUS = #{invoiceCancelStatus,jdbcType=BOOLEAN},
        EXPRESS_ID = #{expressId,jdbcType=INTEGER},
        IS_AUTH = #{isAuth,jdbcType=BOOLEAN},
        IS_MONTH_AUTH = #{isMonthAuth,jdbcType=BOOLEAN},
        AUTH_TIME = #{authTime,jdbcType=BIGINT},
        ADD_TIME = #{addTime,jdbcType=BIGINT},
        CREATOR = #{creator,jdbcType=INTEGER},
        MOD_TIME = #{modTime,jdbcType=BIGINT},
        UPDATER = #{updater,jdbcType=INTEGER},
        INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER},
        SEND_TIME = #{sendTime,jdbcType=TIMESTAMP},
        AFTER_EXPRESS_ID = #{afterExpressId,jdbcType=INTEGER},
        OSS_FILE_URL = #{ossFileUrl,jdbcType=VARCHAR},
        RESOURCE_ID = #{resourceId,jdbcType=VARCHAR},
        INVOICE_FROM = #{invoiceFrom,jdbcType=TINYINT},
        HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER},
        AUTH_MODE = #{authMode,jdbcType=BOOLEAN},
        AUTH_FAIL_REASON = #{authFailReason,jdbcType=VARCHAR},
        IS_AUTHING = #{isAuthing,jdbcType=BOOLEAN},
        COLOR_COMPLEMENT_TYPE = #{colorComplementType,jdbcType=BOOLEAN},
        IS_AFTER_BUYORDER_ONLY = #{isAfterBuyorderOnly,jdbcType=BOOLEAN},
        OPEN_INVOICE_TIME = #{openInvoiceTime,jdbcType=TIMESTAMP}
        where INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
    </update>

  <select id="getInvoiceListByRelatedIdGroupByInvoiceNoAndColor" resultType="com.vedeng.erp.finance.dto.InvoiceDto">
    SELECT
    a.INVOICE_ID, a.COMPANY_ID, a.TYPE, a.RELATED_ID, a.INVOICE_NO, a.INVOICE_TYPE, a.RATIO, a.COLOR_TYPE,
    SUM(b.TOTAL_AMOUNT) AS AMOUNT,
    a.IS_ENABLE, a.VALID_STATUS, a.VALID_TIME, a.VALID_COMMENTS, a.INVOICE_PRINT_STATUS,
    a.INVOICE_CANCEL_STATUS, a.EXPRESS_ID,a.COLOR_COMPLEMENT_TYPE,a.INVOICE_CODE,
    a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER, SUM(b.NUM) as NUM
    FROM T_INVOICE a
    left join T_INVOICE_DETAIL b on a.INVOICE_ID = b.INVOICE_ID
    WHERE 1=1
      and	a.RELATED_ID = #{relatedId,jdbcType=INTEGER}
    <if test="validStatus !=null ">
      and a.VALID_STATUS = #{validStatus,jdbcType=INTEGER}
    </if>
      and a.COMPANY_ID = 1
    <if test="type != null and type !=''">
      AND a.TYPE = #{type,jdbcType=INTEGER}
    </if>
    <if test="invoiceNo != null and invoiceNo != ''">
      and	a.INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
    </if>
    <!-- 红蓝字 -->
    <if test="colorType != null and colorType != ''">
      AND a.COLOR_TYPE = #{colorType,jdbcType=BIT}
    </if>
    <!-- 是否有效 -->
    <if test="isEnable != null">
      AND a.IS_ENABLE = #{isEnable,jdbcType=BIT}
    </if>
    <if test="invoiceCancelStatus != null">
      AND a.INVOICE_CANCEL_STATUS = #{invoiceCancelStatus,jdbcType=BIT}
    </if>
    GROUP BY a.INVOICE_NO,a.INVOICE_CODE, a.COLOR_TYPE, a.IS_ENABLE,a.COLOR_COMPLEMENT_TYPE
  </select>

  <select id="getInvoiceListByRelatedIdGroupByInvoiceNoAndColorAndOrderGoodsItemId" resultType="com.vedeng.erp.finance.dto.InvoiceByGoodsDto">
    SELECT
    a.INVOICE_ID, a.COMPANY_ID, a.TYPE, a.RELATED_ID, a.INVOICE_NO, a.INVOICE_TYPE, a.RATIO, a.COLOR_TYPE,
    SUM(b.TOTAL_AMOUNT) AS AMOUNT,a.COLOR_COMPLEMENT_TYPE,a.INVOICE_CODE,
    a.IS_ENABLE, a.VALID_STATUS, a.VALID_TIME, a.VALID_COMMENTS, a.INVOICE_PRINT_STATUS,
    a.INVOICE_CANCEL_STATUS, a.EXPRESS_ID,
    a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER, SUM(b.NUM) as NUM,b.DETAILGOODS_ID
    FROM T_INVOICE a
    left join T_INVOICE_DETAIL b on a.INVOICE_ID = b.INVOICE_ID
    WHERE 1=1
    <if test="validStatus !=null ">
      and a.VALID_STATUS = #{validStatus,jdbcType=INTEGER}
    </if>
      and a.COMPANY_ID = 1
    and	a.RELATED_ID = #{relatedId,jdbcType=INTEGER}
    <if test="type != null and type !=''">
      AND a.TYPE = #{type,jdbcType=INTEGER}
    </if>
    <if test="invoiceNo != null and invoiceNo != ''">
      and	a.INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
    </if>
    <!-- 红蓝字 -->
    <if test="colorType != null and colorType != ''">
      AND a.COLOR_TYPE = #{colorType,jdbcType=BIT}
    </if>
    <!-- 是否有效 -->
    <if test="isEnable != null">
      AND a.IS_ENABLE = #{isEnable,jdbcType=BIT}
    </if>
    <if test="invoiceCancelStatus != null">
      AND a.INVOICE_CANCEL_STATUS = #{invoiceCancelStatus,jdbcType=BIT}
    </if>
    GROUP BY a.INVOICE_NO,a.INVOICE_CODE, a.COLOR_TYPE, a.IS_ENABLE,a.COLOR_COMPLEMENT_TYPE,b.DETAILGOODS_ID
  </select>

    <select id="queryLatestInvoiceNo" resultType="java.lang.String">
        SELECT INVOICE_NO
        FROM T_INVOICE
        WHERE INVOICE_NO LIKE CONCAT(#{invoiceNo}, '%')
        ORDER BY INVOICE_NO DESC
        LIMIT 1
    </select>


    <select id="findByRelatedIdGroupByInvoiceNo" resultType="com.vedeng.erp.finance.dto.InvoiceDto">
        SELECT TI.INVOICE_ID,
        TI.COMPANY_ID,
        TI.TYPE,
        TI.RELATED_ID,
        TI.INVOICE_NO,
        TI.INVOICE_CODE,
        TI.INVOICE_TYPE,
        TI.RATIO,
        TI.COLOR_TYPE,
        TI.IS_ENABLE,
        TI.VALID_STATUS,
        TI.VALID_TIME,
        TI.VALID_COMMENTS,
        TI.INVOICE_PRINT_STATUS,
        TI.INVOICE_CANCEL_STATUS,
        TI.EXPRESS_ID,
        TI.ADD_TIME,
        TI.CREATOR,
        TI.MOD_TIME,
        TI.UPDATER,
        sum(TI.AMOUNT) AS AMOUNT
        from T_INVOICE TI
        where TI.RELATED_ID = #{relatedId,jdbcType=INTEGER}
        and TI.VALID_STATUS = 1
        and TI.COMPANY_ID = 1
        and TI.COLOR_TYPE = 2
        and TI.IS_ENABLE = 1
        and TI.TYPE = 4126
        group by TI.INVOICE_NO ,TI.INVOICE_CODE
    </select>
    <select id="getInvoiceListByAfterSalesId" resultType="com.vedeng.erp.finance.dto.InvoiceDto">
      SELECT
            ti.INVOICE_NO ,
            tsod.TITLE AS invoiceTypeName,
            ti.COLOR_TYPE,
            ti.AMOUNT ,
            tu.USERNAME AS creatorName,
            ti.ADD_TIME ,
            ti.VALID_STATUS ,
            tu2.USERNAME AS validUserName,
            ti.VALID_TIME,
            ti.COLOR_COMPLEMENT_TYPE,
            ti.IS_ENABLE,
            ti.INVOICE_TYPE
        FROM
            T_INVOICE ti
        LEFT JOIN T_SYS_OPTION_DEFINITION tsod ON
            ti.INVOICE_TYPE = tsod.SYS_OPTION_DEFINITION_ID
        LEFT JOIN T_USER tu ON
            ti.CREATOR = tu.USER_ID
        LEFT JOIN T_USER tu2 ON
            ti.VALID_USERID = tu2.USER_ID
        WHERE
            ti.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
            AND ti.`TYPE` = #{type,jdbcType=INTEGER}
    </select>
    <select id="calculateAlreadyInvoiceNum" resultType="java.math.BigDecimal">
        SELECT
            SUM(IF (ti.COLOR_TYPE = 2 AND ti.COLOR_COMPLEMENT_TYPE = 0 AND ti.IS_ENABLE = 1 , tid.NUM, tid.NUM *-1))
        FROM
            T_INVOICE ti
        LEFT JOIN T_INVOICE_DETAIL tid ON
            ti.INVOICE_ID = tid.INVOICE_ID
        WHERE
            ti.`TYPE` = 4126
            AND ti.COMPANY_ID = 1
            AND ti.VALID_STATUS = 1
            AND tid.DETAILGOODS_ID = #{goodsId,jdbcType=INTEGER}
    </select>

    <select id="getOneInvoiceByNoCodeType" resultType="com.vedeng.erp.finance.dto.InvoiceDto">
        SELECT
        a.INVOICE_ID, a.COMPANY_ID, a.TYPE, a.RELATED_ID, a.INVOICE_NO, a.INVOICE_TYPE, a.RATIO, a.COLOR_TYPE,
        a.AMOUNT,a.COLOR_COMPLEMENT_TYPE,a.INVOICE_CODE,
        a.IS_ENABLE, a.VALID_STATUS, a.VALID_TIME, a.VALID_COMMENTS, a.INVOICE_PRINT_STATUS,
        a.INVOICE_CANCEL_STATUS, a.EXPRESS_ID,a.AMOUNT,
        a.HX_INVOICE_ID,a.INVOICE_FROM,
        a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER
        FROM T_INVOICE a
        WHERE
        a.RELATED_ID = #{relatedId,jdbcType=INTEGER}
        and	a.INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR}
        and	a.INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
        and a.COMPANY_ID = 1
        <if test="validStatus !=null ">
            and a.VALID_STATUS = #{validStatus,jdbcType=INTEGER}
        </if>
        <if test="type != null and type !=''">
            AND a.TYPE = #{type,jdbcType=INTEGER}
        </if>
        <!-- 红蓝字 -->
        <if test="colorType != null and colorType != ''">
            AND a.COLOR_TYPE = #{colorType,jdbcType=BIT}
        </if>
        <!-- 是否有效 -->
        <if test="isEnable != null">
            AND a.IS_ENABLE = #{isEnable,jdbcType=BIT}
        </if>
        <!---->
        <if test="invoiceCancelStatus != null">
            AND a.INVOICE_CANCEL_STATUS = #{invoiceCancelStatus,jdbcType=BIT}
        </if>
        <if test="colorComplementType!=null">
            AND a.COLOR_COMPLEMENT_TYPE = #{colorComplementType,jdbcType=INTEGER}
        </if>
<!--        group by a.INVOICE_NO,a.INVOICE_CODE,a.INVOICE_ID-->
    </select>
    <select id="getAlreadyRedInvoiceNum" resultType="java.math.BigDecimal">
        SELECT
            IFNULL(SUM(tid.NUM), 0.00) AS NUM
        FROM
            T_INVOICE ti
        LEFT JOIN T_INVOICE_DETAIL tid ON
            ti.INVOICE_ID = tid.INVOICE_ID
            AND tid.DETAILGOODS_ID = #{goodsId,jdbcType=INTEGER}
        WHERE
            ti.COLOR_TYPE = 1
            AND ti.IS_ENABLE = 1
            AND ti.COMPANY_ID = 1
            AND ti.INVOICE_ID IN
            <foreach collection="invoiceIdList" item="invoiceId" index="index" open="(" close=")" separator=",">
                #{invoiceId,jdbcType=INTEGER}
            </foreach>
    </select>

    <select id="querySumAmountByInvoiceNo" resultType="java.math.BigDecimal">
        SELECT
            SUM(A.AMOUNT) AS AMOUNT
        FROM T_INVOICE A
        WHERE A.RELATED_ID = #{relatedId,jdbcType=INTEGER}
            AND	(A.INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR} OR A.INVOICE_NO LIKE CONCAT(#{invoiceNo,jdbcType=VARCHAR},'-','%'))
            AND A.TYPE = #{type,jdbcType=INTEGER}
            AND A.COMPANY_ID = 1
            AND A.VALID_STATUS = 1
        GROUP BY A.INVOICE_NO
    </select>

    <select id="queryAllInvoiceIdByInvoiceNo" resultType="java.lang.Integer">
        SELECT
            DISTINCT INVOICE_ID
        FROM T_INVOICE A
        WHERE A.RELATED_ID = #{relatedId,jdbcType=INTEGER}
          AND	(A.INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR} OR A.INVOICE_NO LIKE CONCAT(#{invoiceNo,jdbcType=VARCHAR},'-','%'))
          AND A.TYPE = #{type,jdbcType=INTEGER}
          AND A.COMPANY_ID = 1
          AND A.VALID_STATUS = 1
    </select>
    <select id="getInvoiceTotalAmountByRelatedId" resultType="java.math.BigDecimal">
        SELECT
            SUM(AMOUNT)
        FROM
            T_INVOICE ti
        WHERE
            ti.VALID_STATUS = 1
            AND ti.COMPANY_ID = 1
            AND ti.`TYPE` = #{type,jdbcType=INTEGER}
            AND ti.RELATED_ID = #{relatedId,jdbcType=INTEGER}
        GROUP BY
            ti.RELATED_ID
    </select>

    <select id="queryInvoiceByInvoiceNoAndRelatedId" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List"/>
        FROM T_INVOICE A
        WHERE A.RELATED_ID = #{relatedId,jdbcType=INTEGER}
          AND A.INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
          AND A.TYPE = #{type,jdbcType=INTEGER}
          AND A.COMPANY_ID = 1
          AND A.VALID_STATUS = 1 LIMIT 1
    </select>

    <select id="queryInvoiceIdByInfo" resultType="java.lang.Integer">
        SELECT
            INVOICE_ID
        FROM
        T_INVOICE
        WHERE
            RELATED_ID = #{buyorderId,jdbcType=INTEGER}
            AND VALID_STATUS = 1 AND COLOR_TYPE = 2 AND IS_ENABLE = 1
            AND TYPE = 503 AND INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
        LIMIT 1
    </select>
    
    <select id="queryReceiptInvoiceRecord" resultMap="BaseResultMap">
        SELECT
            *
        FROM
        T_INVOICE
        WHERE
            RELATED_ID = #{buyorderId,jdbcType=INTEGER}
            AND VALID_STATUS in (0,1) AND COLOR_TYPE = 2 AND IS_ENABLE = 1
            AND TYPE = 503 AND INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
        LIMIT 1
    </select>

    <select id="getValidBuyOrderInvoiceIdsByInvoiceNoAndCode" resultType="java.lang.Integer">
        SELECT
            INVOICE_ID
        FROM
            T_INVOICE
        WHERE
            TYPE = 503
          AND VALID_STATUS = 1
          AND COLOR_TYPE = 2
          AND IS_ENABLE = 1
          AND INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
          AND INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR}
        <if test="orderId != null">
            AND RELATED_ID = #{orderId,jdbcType=INTEGER}
        </if>
    </select>

    <select id="queryValidInvoiceIdByGoodsAndInvoiceNo" resultType="java.lang.Integer">
        SELECT
            DISTINCT A.INVOICE_ID
        FROM
            T_INVOICE_DETAIL A
            LEFT JOIN T_INVOICE B ON A.INVOICE_ID = B.INVOICE_ID
        WHERE
        A.DETAILGOODS_ID IN
        <foreach collection="detailGoodsList" item="detailGoodsId" index="index" open="(" close=")" separator=",">
            #{detailGoodsId,jdbcType=INTEGER}
        </foreach>
        AND B.VALID_STATUS = 1
        AND B.COLOR_TYPE = 2
        AND B.IS_ENABLE = 1
        AND B.TYPE = 503
        AND B.INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
        AND B.RELATED_ID = #{orderId,jdbcType=INTEGER}
    </select>
    <select id="getRedValidInvoiceListByExpenseId" resultType="java.lang.String">
        select INVOICE_NO
        from T_INVOICE
        where TYPE = 4126
          and VALID_STATUS = 1
          and COLOR_TYPE = 1
          and RELATED_ID =#{buyorderExpenseId,jdbcType=INTEGER}
    </select>

    <select id="getBlueInvoiceByExpenseId" resultMap="invoiceAndDetail">
        select TI.INVOICE_ID, COMPANY_ID, INVOICE_CODE, INVOICE_FLOW, INVOICE_PROPERTY, INVOICE_HREF, TYPE, TAG,
        RELATED_ID, AFTER_SALES_ID, INVOICE_NO, INVOICE_TYPE, RATIO, COLOR_TYPE, AMOUNT, IS_ENABLE, VALID_USERID,
        VALID_STATUS, VALID_TIME, VALID_COMMENTS, INVOICE_PRINT_STATUS, INVOICE_CANCEL_STATUS, EXPRESS_ID, IS_AUTH,
        IS_MONTH_AUTH, AUTH_TIME, TI.ADD_TIME, TI.CREATOR, TI.MOD_TIME, TI.UPDATER, INVOICE_APPLY_ID, SEND_TIME, AFTER_EXPRESS_ID,
        OSS_FILE_URL, RESOURCE_ID, INVOICE_FROM, HX_INVOICE_ID, AUTH_MODE, AUTH_FAIL_REASON, IS_AUTHING,
        COLOR_COMPLEMENT_TYPE, IS_AFTER_BUYORDER_ONLY,
        TID.INVOICE_DETAIL_ID, TID.INVOICE_ID, DETAILGOODS_ID, TID.PRICE, TID.NUM, TOTAL_AMOUNT, CHANGED_GOODS_NAME,
        TBEID.SKU
        from T_INVOICE TI left join T_INVOICE_DETAIL TID
        on TI.INVOICE_ID = TID.INVOICE_ID
        left join T_BUYORDER_EXPENSE_ITEM TBEI on TID.DETAILGOODS_ID = TBEI.BUYORDER_EXPENSE_ITEM_ID
        left join T_BUYORDER_EXPENSE_ITEM_DETAIL TBEID on TBEID.BUYORDER_EXPENSE_ITEM_ID = TBEI.BUYORDER_EXPENSE_ITEM_ID
        where TI.VALID_STATUS = 1 and TI.TYPE = 4126
        and TI.COLOR_TYPE = 2
        and TI.COMPANY_ID = 1
        and TI.RELATED_ID = #{buyorderExpenseId,jdbcType=INTEGER};

    </select>

<!--auto generated by MybatisCodeHelper on 2023-09-21-->
    <select id="findBySaleOrderRelatedId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_INVOICE
        where RELATED_ID=#{relatedId,jdbcType=INTEGER}
        and TYPE = 505
    </select>

<!--auto generated by MybatisCodeHelper on 2023-09-23-->
    <select id="findByColorTypeAndEnableAndInvoiceApplyId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_INVOICE
        where
         INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
        and COMPANY_ID = 1
    </select>

    <select id="getSaleOpenInvoiceAmount" parameterType="java.lang.Integer" resultType="java.math.BigDecimal">
        SELECT SUM(A.AMOUNT) AS OPEN_INVOICE_AMOUNT
        FROM T_INVOICE A
        WHERE A.TYPE = 505 AND A.RELATED_ID = #{relatedId,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2023-09-27-->
    <select id="findByInvoiceApplyId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_INVOICE
        where INVOICE_APPLY_ID=#{invoiceApplyId,jdbcType=INTEGER}
    </select>
    <select id="findSaleorderByInvoiceApplyId" resultType="com.vedeng.erp.finance.dto.InvoiceDto">
        select
        A.INVOICE_ID,  A.OPEN_INVOICE_TIME ,A.INVOICE_NO,B.SALEORDER_NO orderNo,B.INVOICE_EMAIL email,C.EMAIL invoiceTraderContactEmail,A.AMOUNT
        from T_INVOICE A left join T_SALEORDER B on A.RELATED_ID = B.SALEORDER_ID
        left join T_TRADER_CONTACT C on C.TRADER_CONTACT_ID = B.INVOICE_TRADER_CONTACT_ID
        where A.COLOR_TYPE=2  and
        A.INVOICE_APPLY_ID=#{invoiceApplyId,jdbcType=INTEGER}
        and A.INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
        and A.IS_ENABLE =1
        and A.COMPANY_ID = 1
    </select>

    <select id="findAtByInvoiceApplyId" resultType="com.vedeng.erp.finance.dto.InvoiceDto">
        select
        A.INVOICE_ID,  A.OPEN_INVOICE_TIME ,A.INVOICE_NO,B.AFTER_SALES_NO orderNo,C.EMAIL invoiceTraderContactEmail,A.AMOUNT
        from T_INVOICE A left join T_AFTER_SALES B on A.RELATED_ID = B.AFTER_SALES_ID
        left join T_AFTER_SALES_DETAIL D on B.AFTER_SALES_ID = D.AFTER_SALES_ID
        left join T_TRADER_CONTACT C on C.TRADER_CONTACT_ID = D.INVOICE_TRADER_CONTACT_ID
        where A.COLOR_TYPE=2  and
        A.INVOICE_APPLY_ID=#{invoiceApplyId,jdbcType=INTEGER}
        and A.INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
        and A.IS_ENABLE =1
        and A.COMPANY_ID = 1
    </select>

<!--auto generated by MybatisCodeHelper on 2023-10-21-->
    <select id="findSaleBlueInvoiceByInvoiceNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_INVOICE
        where
        TYPE = 505
        and IS_ENABLE = 1
        and COLOR_TYPE = 2
        and INVOICE_NO=#{invoiceNo,jdbcType=VARCHAR}
    </select>

<!--auto generated by MybatisCodeHelper on 2023-10-23-->
    <select id="findByInvoiceNoAndRelatedIdAndTypeAndColorType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_INVOICE
        where INVOICE_NO=#{invoiceNo,jdbcType=VARCHAR}
        and RELATED_ID=#{relatedId,jdbcType=INTEGER}
        and`TYPE`=#{type,jdbcType=INTEGER}
        and COLOR_TYPE=#{colorType,jdbcType=BOOLEAN}
        and IS_ENABLE = 1
    </select>

    <select id="findByInvoiceNoOne" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_INVOICE
        where
        INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
        and COMPANY_ID = 1
        order by INVOICE_ID desc limit 1
    </select>

    <select id="findByInvoiceNoAndTag" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_INVOICE
        where
        INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
        AND TAG = #{tag,jdbcType=INTEGER}
        and COMPANY_ID = 1
        order by INVOICE_ID desc limit 1
    </select>


    <select id="getInvoiceTagByInvoiceNo" resultType="java.lang.Integer">
        select
            DISTINCT TAG
        from T_INVOICE
        where
        INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
        and COMPANY_ID = 1
    </select>

    <select id="selectOrderAndAfterSale" resultMap="invoiceAndOrderNo">

        select
            INVOICE_ID, a.COMPANY_ID, INVOICE_CODE, INVOICE_FLOW, INVOICE_PROPERTY, INVOICE_HREF,
            a.`TYPE`, TAG, RELATED_ID, a.AFTER_SALES_ID, INVOICE_NO, a.INVOICE_TYPE, RATIO, COLOR_TYPE,
            AMOUNT, IS_ENABLE, VALID_USERID, a.VALID_STATUS, a.VALID_TIME, VALID_COMMENTS, INVOICE_PRINT_STATUS,
            INVOICE_CANCEL_STATUS, EXPRESS_ID, IS_AUTH, IS_MONTH_AUTH, AUTH_TIME, a.ADD_TIME, a.CREATOR,
            a.MOD_TIME, a.UPDATER, INVOICE_APPLY_ID, SEND_TIME, AFTER_EXPRESS_ID, OSS_FILE_URL, RESOURCE_ID,
            INVOICE_FROM, HX_INVOICE_ID, AUTH_MODE, AUTH_FAIL_REASON, IS_AUTHING, COLOR_COMPLEMENT_TYPE,
            IS_AFTER_BUYORDER_ONLY, OPEN_INVOICE_TIME,IFNULL(c.AFTER_SALES_NO,c2.AFTER_SALES_NO) as AFTER_SALES_NO,b.SALEORDER_NO
        from
            T_INVOICE a
                 left join
            T_SALEORDER b on a.RELATED_ID = b.SALEORDER_ID  and a.TYPE = 505
                 left join
            T_AFTER_SALES c on a.AFTER_SALES_ID = c.AFTER_SALES_ID  and a.AFTER_SALES_ID !=0
                 left join
            T_AFTER_SALES c2 on a.RELATED_ID = c2.AFTER_SALES_ID and a.TYPE = 504
        where
            INVOICE_ID =#{invoiceId,jdbcType=INTEGER}
          and a.COMPANY_ID = 1

    </select>

<!--auto generated by MybatisCodeHelper on 2023-10-27-->
    <select id="findByInvoiceNoAndColorTypeAndInvoiceProperty" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_INVOICE
        where INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
          and COLOR_TYPE = 1
          and INVOICE_PROPERTY = 3
          and IS_ENABLE = 1
    </select>

    <select id="calAmount" resultType="java.math.BigDecimal" parameterType="com.vedeng.erp.finance.dto.InvoiceDto">
        SELECT SUM(A.AMOUNT)
        FROM T_INVOICE A
        WHERE
        A.COLOR_TYPE = 2
        AND A.IS_ENABLE = 1
        AND A.VALID_STATUS = 1
        AND A.RELATED_ID = #{relatedId,jdbcType=INTEGER}
        AND A.TYPE = #{type,jdbcType=INTEGER}
        AND A.TAG = #{tag,jdbcType=INTEGER}
    </select>

    <select id="getAfterInvoiceList" resultType="com.vedeng.erp.finance.dto.InvoiceDto">
        SELECT INVOICE_NO,
        INVOICE_ID,
        INVOICE_HREF,
        INVOICE_CODE,
        INVOICE_TYPE,
        COLOR_TYPE,
        IS_ENABLE,
        AMOUNT,
        CREATOR,
        ADD_TIME,
        EXPRESS_ID,
        VALID_TIME,
        VALID_STATUS,
        VALID_USERID
        FROM T_INVOICE
        WHERE RELATED_ID = #{afterSalesId,jdbcType=INTEGER}
        AND TYPE = #{type,jdbcType=INTEGER}
        AND TAG = #{tag,jdbcType=INTEGER}
        AND COLOR_TYPE = 2
        AND IS_ENABLE = 1
        AND VALID_STATUS = 1
        ORDER BY ADD_TIME DESC
    </select>

    <update id="updateInvoiceOssUrlByOcr">
        UPDATE `T_INVOICE`
        SET INVOICE_HREF = #{invoiceHref,jdbcType=VARCHAR},
        OSS_FILE_URL = #{ossUrl,jdbcType=VARCHAR},
        RESOURCE_ID = #{resourceId,jdbcType=VARCHAR}
        WHERE
        INVOICE_NO = #{invoiceNo,jdbcType=INTEGER}
        AND TAG=#{tag,jdbcType=INTEGER}
    </update>

    <update id="updateInvoiceOssFileUrlAndResourceId">
        update
            T_INVOICE
        set OSS_FILE_URL = #{ossFileUrl,jdbcType=VARCHAR},
            INVOICE_HREF = #{ossFileUrl,jdbcType=VARCHAR},
            RESOURCE_ID = #{resourceId,jdbcType=VARCHAR},
            MOD_TIME= NOW()
        where INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
    </update>

    <select id="getSalesOrderBlueInvoiceAmount" resultType="java.math.BigDecimal">
        select ifnull(sum(abs(ifnull(TI.AMOUNT, 0))), 0)
        from T_INVOICE TI
        where TI.TYPE = 505
          and TAG = 1
          and COLOR_TYPE = 2
          and VALID_STATUS = 1
          and RELATED_ID = #{relatedId,jdbcType=INTEGER}
    </select>

    <select id="getSalesOrderRedInvoiceAmount" resultType="java.math.BigDecimal">
        select ifnull(sum(abs(ifnull(TI.AMOUNT, 0))), 0)
        from T_INVOICE TI
        where TI.TYPE = 505
        and TAG = 1
        and COLOR_TYPE = 1
        and VALID_STATUS = 1
        and RELATED_ID = #{relatedId,jdbcType=INTEGER}
    </select>
</mapper>
