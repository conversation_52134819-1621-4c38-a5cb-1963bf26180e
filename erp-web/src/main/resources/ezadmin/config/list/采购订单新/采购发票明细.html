<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>采购发票明细</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="buyinvoicedetail" datasource="erp-datasourcetarget" fixednumber append_column_url append_row_url empty_show firstcol="numbers"
       tableSearchFlag="false">
  <div id="appendHead"></div> 
  <div class="layui-fluid"> 
   <div class="layui-tab"> 
    <ul class="layui-tab-title" id="tab"> 
    </ul> 
   </div> 
   <form class="layui-form" id="search">
    <div class=" layui-inline ">
     <label class="layui-form-label">采购单号</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="BUYORDER_NO" placeholder style alias="T4" jdbctype data datatype oper> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">发票号</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="INVOICE_NO" placeholder style alias="T1" jdbctype data datatype oper> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">SKU</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="SKU" placeholder style alias="T3" jdbctype data datatype oper>
     </div>
    </div>
   </form> 
   <hr class="layui-border-blue"> 
   <div class="btn-group   bd-highlight" id="tableButton">
   </div> 
   <table id="table" class="layui-table" style=" width:100%"> 
    <thead> 
     <tr id="column">
      <th type="rowbutton" id="rowbutton"></th>
      <th item_name="INVOICE_NO" name="INVOICE_NO" body="td-text" order url opentype windowname datatype data style head="th">发票号</th>
      <th item_name="BUYORDER_NO" name="BUYORDER_NO" body="td-text" order url opentype windowname datatype data style head="th">采购单号</th>
      <th item_name="SKU" name="SKU" body="td-text" order url opentype windowname datatype data style head="th">SKU</th>
      <th item_name="UNIT_NAME" name="UNIT_NAME" body="td-text" order url opentype windowname datatype data style head="th">单位</th>
      <th item_name="SKU_NAME" name="SKU_NAME" body="td-text" order url opentype windowname datatype data style head="th">产品名称</th>
      <th item_name="NUM" name="NUM" body="td-text" order url opentype windowname datatype data style head="th">录票数量</th>
      <th item_name="TOTAL_AMOUNT" name="TOTAL_AMOUNT" body="td-text" order url opentype windowname datatype data style head="th">录票金额</th>
     </tr> 
    </thead> 
    <tbody> 
     <tr> 
      <td colspan="100"> express: <pre id="express" class="layui-code" orderby groupby="GROUP BY T3.BUYORDER_GOODS_ID">
SELECT
	T1.INVOICE_NO  ,
	T4.BUYORDER_NO  ,
	T3.SKU,
	T6.UNIT_NAME ,
	T5.SKU_NAME ,
	format(T2.NUM,2) NUM   ,
	T2.TOTAL_AMOUNT
FROM
	T_INVOICE T1
	LEFT JOIN T_INVOICE_DETAIL T2 ON T1.INVOICE_ID = T2.INVOICE_ID
	LEFT JOIN T_BUYORDER_GOODS T3 ON T2.DETAILGOODS_ID = T3.BUYORDER_GOODS_ID
	LEFT JOIN T_BUYORDER T4 ON T3.BUYORDER_ID = T4.BUYORDER_ID
	LEFT JOIN V_CORE_SKU T5 ON T3.GOODS_ID = T5.SKU_ID
LEFT JOIN T_UNIT T6 ON T5.BASE_UNIT_ID = T6.UNIT_ID
WHERE
	T1.TYPE = 503  AND T1.COMPANY_ID=1 AND T4.BUYORDER_NO IS NOT NULL AND T1.ADD_TIME > 1640972058000
 </pre> count: <pre id="count" class="layui-code"></pre></td>
     </tr> 
    </tbody> 
   </table> 
  </div> 
  <div id="appendFoot"></div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
 </body>
</html>