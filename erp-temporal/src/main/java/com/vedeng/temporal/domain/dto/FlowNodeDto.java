package com.vedeng.temporal.domain.dto;

import java.math.BigDecimal;
import java.util.Date;

public class FlowNodeDto {
    private Long flowNodeId;
    private Long flowOrderId;
    private Integer openInvoice;
    private Integer nodeLevel;
    private Integer paymentMethod;
    private Integer invoiceType;
    private BigDecimal amount;
    private BigDecimal creditPayment;
    private BigDecimal balance;
    private Integer balanceDueDate;
    private Integer traderId;
    private String traderName;
    private Integer traderContactId;
    private String traderContactName;
    private String traderContactPhone;
    private Integer traderAddressId;
    private String traderContactAddress;
    private Integer receiverTraderContactId;
    private String receiverName;
    private String receiverPhone;
    private Integer receiverAddressId;
    private String receiverAddress;
    private Integer invoiceReceiverTraderContactId;
    private String invoiceReceiverName;
    private String invoiceReceiverPhone;
    private Integer invoiceReceiverAddressId;
    private String invoiceReceiverAddress;
    private Integer isDelete;
    private Integer creator;
    private Integer updater;
    private String creatorName;
    private String updaterName;
    private Date addTime;
    private Date modTime;

    // getter/setter
    public Long getFlowNodeId() { return flowNodeId; }
    public void setFlowNodeId(Long flowNodeId) { this.flowNodeId = flowNodeId; }
    public Long getFlowOrderId() { return flowOrderId; }
    public void setFlowOrderId(Long flowOrderId) { this.flowOrderId = flowOrderId; }
    public Integer getOpenInvoice() { return openInvoice; }
    public void setOpenInvoice(Integer openInvoice) { this.openInvoice = openInvoice; }
    public Integer getNodeLevel() { return nodeLevel; }
    public void setNodeLevel(Integer nodeLevel) { this.nodeLevel = nodeLevel; }
    public Integer getPaymentMethod() { return paymentMethod; }
    public void setPaymentMethod(Integer paymentMethod) { this.paymentMethod = paymentMethod; }
    public Integer getInvoiceType() { return invoiceType; }
    public void setInvoiceType(Integer invoiceType) { this.invoiceType = invoiceType; }
    public BigDecimal getAmount() { return amount; }
    public void setAmount(BigDecimal amount) { this.amount = amount; }
    public BigDecimal getCreditPayment() { return creditPayment; }
    public void setCreditPayment(BigDecimal creditPayment) { this.creditPayment = creditPayment; }
    public BigDecimal getBalance() { return balance; }
    public void setBalance(BigDecimal balance) { this.balance = balance; }
    public Integer getBalanceDueDate() { return balanceDueDate; }
    public void setBalanceDueDate(Integer balanceDueDate) { this.balanceDueDate = balanceDueDate; }
    public Integer getTraderId() { return traderId; }
    public void setTraderId(Integer traderId) { this.traderId = traderId; }
    public String getTraderName() { return traderName; }
    public void setTraderName(String traderName) { this.traderName = traderName; }
    public Integer getTraderContactId() { return traderContactId; }
    public void setTraderContactId(Integer traderContactId) { this.traderContactId = traderContactId; }
    public String getTraderContactName() { return traderContactName; }
    public void setTraderContactName(String traderContactName) { this.traderContactName = traderContactName; }
    public String getTraderContactPhone() { return traderContactPhone; }
    public void setTraderContactPhone(String traderContactPhone) { this.traderContactPhone = traderContactPhone; }
    public Integer getTraderAddressId() { return traderAddressId; }
    public void setTraderAddressId(Integer traderAddressId) { this.traderAddressId = traderAddressId; }
    public String getTraderContactAddress() { return traderContactAddress; }
    public void setTraderContactAddress(String traderContactAddress) { this.traderContactAddress = traderContactAddress; }
    public Integer getReceiverTraderContactId() { return receiverTraderContactId; }
    public void setReceiverTraderContactId(Integer receiverTraderContactId) { this.receiverTraderContactId = receiverTraderContactId; }
    public String getReceiverName() { return receiverName; }
    public void setReceiverName(String receiverName) { this.receiverName = receiverName; }
    public String getReceiverPhone() { return receiverPhone; }
    public void setReceiverPhone(String receiverPhone) { this.receiverPhone = receiverPhone; }
    public Integer getReceiverAddressId() { return receiverAddressId; }
    public void setReceiverAddressId(Integer receiverAddressId) { this.receiverAddressId = receiverAddressId; }
    public String getReceiverAddress() { return receiverAddress; }
    public void setReceiverAddress(String receiverAddress) { this.receiverAddress = receiverAddress; }
    public Integer getInvoiceReceiverTraderContactId() { return invoiceReceiverTraderContactId; }
    public void setInvoiceReceiverTraderContactId(Integer invoiceReceiverTraderContactId) { this.invoiceReceiverTraderContactId = invoiceReceiverTraderContactId; }
    public String getInvoiceReceiverName() { return invoiceReceiverName; }
    public void setInvoiceReceiverName(String invoiceReceiverName) { this.invoiceReceiverName = invoiceReceiverName; }
    public String getInvoiceReceiverPhone() { return invoiceReceiverPhone; }
    public void setInvoiceReceiverPhone(String invoiceReceiverPhone) { this.invoiceReceiverPhone = invoiceReceiverPhone; }
    public Integer getInvoiceReceiverAddressId() { return invoiceReceiverAddressId; }
    public void setInvoiceReceiverAddressId(Integer invoiceReceiverAddressId) { this.invoiceReceiverAddressId = invoiceReceiverAddressId; }
    public String getInvoiceReceiverAddress() { return invoiceReceiverAddress; }
    public void setInvoiceReceiverAddress(String invoiceReceiverAddress) { this.invoiceReceiverAddress = invoiceReceiverAddress; }
    public Integer getIsDelete() { return isDelete; }
    public void setIsDelete(Integer isDelete) { this.isDelete = isDelete; }
    public Integer getCreator() { return creator; }
    public void setCreator(Integer creator) { this.creator = creator; }
    public Integer getUpdater() { return updater; }
    public void setUpdater(Integer updater) { this.updater = updater; }
    public String getCreatorName() { return creatorName; }
    public void setCreatorName(String creatorName) { this.creatorName = creatorName; }
    public String getUpdaterName() { return updaterName; }
    public void setUpdaterName(String updaterName) { this.updaterName = updaterName; }
    public Date getAddTime() { return addTime; }
    public void setAddTime(Date addTime) { this.addTime = addTime; }
    public Date getModTime() { return modTime; }
    public void setModTime(Date modTime) { this.modTime = modTime; }
} 