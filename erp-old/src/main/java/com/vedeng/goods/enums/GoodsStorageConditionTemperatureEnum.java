package com.vedeng.goods.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * 商品存储条件(温度)
 *
 * <AUTHOR> [<EMAIL>]
 */
@Getter
public enum GoodsStorageConditionTemperatureEnum {

    /**
     * 常温
     */
    NORMAL(1, "常温0-30℃"),

    /**
     * 阴凉
     */
    SHADY_AND_COLD(2, "阴凉0-20℃"),


    COLD_STORAGE(3, "冷藏2-10℃"),


    /**
     * 其他温度
     */
    OTHERS(4, "其他温度"),
    ;


    private Integer code;
    private String name;

    GoodsStorageConditionTemperatureEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }


    /**
     * 兼容历史数据转换为现在的配置
     *
     * @param oldCode
     * @return
     */
    public static Integer getTemperatureCode(Integer oldCode) {
        for (GoodsStorageConditionTemperatureEnum conditionTemperatureEnum : values()) {
            if (oldCode != null && oldCode.equals(conditionTemperatureEnum.getCode())) {
                return conditionTemperatureEnum.getCode();
            }

        }

        return null;
    }


    public static boolean validate(Integer code) {
        if (code == null) {
            return false;
        }
        return Arrays.stream(values()).anyMatch(e -> code.equals(e.getCode()));
    }

    public static String getNameByCode(Integer code) {
        if(code == null){
            return "";
        }

        return Arrays.stream(values())
                .filter(item -> item.getCode().equals(Integer.valueOf(code)))
                .map(item -> item.getName())
                .findFirst()
                .orElse("");
    }
}
