package com.newtask.invoice;

import com.fasterxml.jackson.core.type.TypeReference;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: daniel
 * @Date: 2021/8/17 14 07
 * @Description: 航信自动纸质票mock接口
 */
@JobHandler("mockHxInvoiceSoap")
@Configuration
public class MockHxInvoiceSoap extends AbstractJobHandler {

    @Value("${http_url}")
    public String httpUrl;

    @Value("${client_id}")
    public String clientId;

    @Value("${client_key}")
    public String clientKey;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        if (StringUtils.isBlank(param)){
            XxlJobLogger.log("mock的订单号不可以为空");
            return ReturnT.FAIL;
        }
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        String url = httpUrl + "finance/invoice/mockHxInvoiceSoap.htm";
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, param, clientId, clientKey,
                    TypeRef);
            if (result != null && result.getCode() == 0){
                XxlJobLogger.log("mock自动纸质票成功");
            }
        } catch (Exception e) {
            XxlJobLogger.log("mock自动纸质票成功");
        }

        return ReturnT.SUCCESS;
    }
}
