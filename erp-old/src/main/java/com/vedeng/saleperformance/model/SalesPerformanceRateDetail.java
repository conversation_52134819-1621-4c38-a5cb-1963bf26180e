package com.vedeng.saleperformance.model;

public class SalesPerformanceRateDetail {
    private Integer salesPerformanceRateDetailId;

    private Integer salesPerformanceRateId;

    private Integer type;

    private String day01;

    private String day02;

    private String day03;

    private String day04;

    private String day05;

    private String day06;

    private String day07;

    private String day08;

    private String day09;

    private String day10;

    private String day11;

    private String day12;

    private String day13;

    private String day14;

    private String day15;

    private String day16;

    private String day17;

    private String day18;

    private String day19;

    private String day20;

    private String day21;

    private String day22;

    private String day23;

    private String day24;

    private String day25;

    private String day26;

    private String day27;

    private String day28;

    private String day29;

    private String day30;

    private String day31;

    private Long modTime;

    public Integer getSalesPerformanceRateDetailId() {
        return salesPerformanceRateDetailId;
    }

    public void setSalesPerformanceRateDetailId(Integer salesPerformanceRateDetailId) {
        this.salesPerformanceRateDetailId = salesPerformanceRateDetailId;
    }

    public Integer getSalesPerformanceRateId() {
        return salesPerformanceRateId;
    }

    public void setSalesPerformanceRateId(Integer salesPerformanceRateId) {
        this.salesPerformanceRateId = salesPerformanceRateId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDay01() {
        return day01;
    }

    public void setDay01(String day01) {
        this.day01 = day01 == null ? null : day01.trim();
    }

    public String getDay02() {
        return day02;
    }

    public void setDay02(String day02) {
        this.day02 = day02 == null ? null : day02.trim();
    }

    public String getDay03() {
        return day03;
    }

    public void setDay03(String day03) {
        this.day03 = day03 == null ? null : day03.trim();
    }

    public String getDay04() {
        return day04;
    }

    public void setDay04(String day04) {
        this.day04 = day04 == null ? null : day04.trim();
    }

    public String getDay05() {
        return day05;
    }

    public void setDay05(String day05) {
        this.day05 = day05 == null ? null : day05.trim();
    }

    public String getDay06() {
        return day06;
    }

    public void setDay06(String day06) {
        this.day06 = day06 == null ? null : day06.trim();
    }

    public String getDay07() {
        return day07;
    }

    public void setDay07(String day07) {
        this.day07 = day07 == null ? null : day07.trim();
    }

    public String getDay08() {
        return day08;
    }

    public void setDay08(String day08) {
        this.day08 = day08 == null ? null : day08.trim();
    }

    public String getDay09() {
        return day09;
    }

    public void setDay09(String day09) {
        this.day09 = day09 == null ? null : day09.trim();
    }

    public String getDay10() {
        return day10;
    }

    public void setDay10(String day10) {
        this.day10 = day10 == null ? null : day10.trim();
    }

    public String getDay11() {
        return day11;
    }

    public void setDay11(String day11) {
        this.day11 = day11 == null ? null : day11.trim();
    }

    public String getDay12() {
        return day12;
    }

    public void setDay12(String day12) {
        this.day12 = day12 == null ? null : day12.trim();
    }

    public String getDay13() {
        return day13;
    }

    public void setDay13(String day13) {
        this.day13 = day13 == null ? null : day13.trim();
    }

    public String getDay14() {
        return day14;
    }

    public void setDay14(String day14) {
        this.day14 = day14 == null ? null : day14.trim();
    }

    public String getDay15() {
        return day15;
    }

    public void setDay15(String day15) {
        this.day15 = day15 == null ? null : day15.trim();
    }

    public String getDay16() {
        return day16;
    }

    public void setDay16(String day16) {
        this.day16 = day16 == null ? null : day16.trim();
    }

    public String getDay17() {
        return day17;
    }

    public void setDay17(String day17) {
        this.day17 = day17 == null ? null : day17.trim();
    }

    public String getDay18() {
        return day18;
    }

    public void setDay18(String day18) {
        this.day18 = day18 == null ? null : day18.trim();
    }

    public String getDay19() {
        return day19;
    }

    public void setDay19(String day19) {
        this.day19 = day19 == null ? null : day19.trim();
    }

    public String getDay20() {
        return day20;
    }

    public void setDay20(String day20) {
        this.day20 = day20 == null ? null : day20.trim();
    }

    public String getDay21() {
        return day21;
    }

    public void setDay21(String day21) {
        this.day21 = day21 == null ? null : day21.trim();
    }

    public String getDay22() {
        return day22;
    }

    public void setDay22(String day22) {
        this.day22 = day22 == null ? null : day22.trim();
    }

    public String getDay23() {
        return day23;
    }

    public void setDay23(String day23) {
        this.day23 = day23 == null ? null : day23.trim();
    }

    public String getDay24() {
        return day24;
    }

    public void setDay24(String day24) {
        this.day24 = day24 == null ? null : day24.trim();
    }

    public String getDay25() {
        return day25;
    }

    public void setDay25(String day25) {
        this.day25 = day25 == null ? null : day25.trim();
    }

    public String getDay26() {
        return day26;
    }

    public void setDay26(String day26) {
        this.day26 = day26 == null ? null : day26.trim();
    }

    public String getDay27() {
        return day27;
    }

    public void setDay27(String day27) {
        this.day27 = day27 == null ? null : day27.trim();
    }

    public String getDay28() {
        return day28;
    }

    public void setDay28(String day28) {
        this.day28 = day28 == null ? null : day28.trim();
    }

    public String getDay29() {
        return day29;
    }

    public void setDay29(String day29) {
        this.day29 = day29 == null ? null : day29.trim();
    }

    public String getDay30() {
        return day30;
    }

    public void setDay30(String day30) {
        this.day30 = day30 == null ? null : day30.trim();
    }

    public String getDay31() {
        return day31;
    }

    public void setDay31(String day31) {
        this.day31 = day31 == null ? null : day31.trim();
    }

    public Long getModTime() {
        return modTime;
    }

    public void setModTime(Long modTime) {
        this.modTime = modTime;
    }
}