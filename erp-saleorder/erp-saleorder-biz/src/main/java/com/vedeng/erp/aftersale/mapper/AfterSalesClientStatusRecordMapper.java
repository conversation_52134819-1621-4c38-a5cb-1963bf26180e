package com.vedeng.erp.aftersale.mapper;

import com.vedeng.erp.aftersale.domain.entity.AfterSalesClientStatusRecord;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AfterSalesClientStatusRecordMapper {
    int deleteByPrimaryKey(Long clientStatusRecordId);

    int insert(AfterSalesClientStatusRecord record);

    int insertOrUpdate(AfterSalesClientStatusRecord record);

    int insertOrUpdateSelective(AfterSalesClientStatusRecord record);

    int insertSelective(AfterSalesClientStatusRecord record);

    AfterSalesClientStatusRecord selectByPrimaryKey(Long clientStatusRecordId);

    int updateByPrimaryKeySelective(AfterSalesClientStatusRecord record);

    int updateByPrimaryKey(AfterSalesClientStatusRecord record);

    int updateBatch(List<AfterSalesClientStatusRecord> list);

    int updateBatchSelective(List<AfterSalesClientStatusRecord> list);

    int batchInsert(@Param("list") List<AfterSalesClientStatusRecord> list);

    List<AfterSalesClientStatusRecord> findByAfterSalesId(@Param("afterSalesId")Integer afterSalesId);

}