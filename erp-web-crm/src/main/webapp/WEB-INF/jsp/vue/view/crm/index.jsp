<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html>
<html>
<head>
    <title>灵犀CRM-首页</title>
    <link rel="stylesheet" href="/static/css/common/common.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/ui/ui.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/css/pages/serverError.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <script type="text/javascript" src='${pageContext.request.contextPath}/static/js/common/jquery.min.js?staticResourceVersion=${requestScope.staticResourceVersion}'></script>
    <script src="//res.wx.qq.com/open/js/jweixin-1.2.0.js"></script>
    <script type="text/javascript">
        function isWeixinBrowser() {
            var ua = navigator.userAgent.toLowerCase();
            return ua.indexOf("micromessenger") != -1;
        }

        function isPC() {
            let userAgentInfo = navigator.userAgent;
            let Agents = new Array("Android", "iPhone", "SymbianOS", "Windows Phone", "iPad", "iPod");
            let flag = true;
            for (let v = 0; v < Agents.length; v++) {
                if (userAgentInfo.indexOf(Agents[v]) > 0) {
                    flag = false;
                    break;
                }
            }
            return flag;
        }
        function deleteCookie(cookieName, path = '/', domain = document.domain) {
            document.cookie = cookieName+`=;
                expires=Thu, 01 Jan 1970 00:00:00 UTC;
                path=/;
                domain=`+domain;
        }

        function getCookieValue(cookieName) {
            const name = cookieName + "=";
            const decodedCookies = decodeURIComponent(document.cookie);
            const cookiesArray = decodedCookies.split(';');

            for (let i = 0; i < cookiesArray.length; i++) {
                let cookie = cookiesArray[i].trim();
                if (cookie.indexOf(name) === 0) {
                    const targeturl = decodeURIComponent(cookie.substring(name.length));
                    //deleteCookie('lxcrmTargetUrl');
                    return targeturl;
                }
            }
            return null; // 如果未找到返回 null
        }
        if(isWeixinBrowser()){//微信浏览器
            const lxcrmTargetUrl = getCookieValue('lxcrmTargetUrl');
            console.log('lxcrmTargetUrl:', lxcrmTargetUrl);
            if(lxcrmTargetUrl !=undefined && lxcrmTargetUrl !=null){
                window.location.href=lxcrmTargetUrl; //手机APP端直接到拜访计划列表
            }else{
                if(!isPC()){
                    window.location.href="/crm/visitRecord/m/index"; //手机APP端直接到拜访计划列表
                }else{
                    window.location.href="/crm/visitRecord/profile/index"; // PC端跳到商机列表
                }
            }



        }else{
            window.location.href="/crm/visitRecord/profile/index"; // PC端跳到商机列表
        }
    </script>
</head>

<body>
<div id="tip" style="display: none;">
    当前页面不支持PC端打开！请使用微信端访问。
</div>

</div>

</body>
</html>
