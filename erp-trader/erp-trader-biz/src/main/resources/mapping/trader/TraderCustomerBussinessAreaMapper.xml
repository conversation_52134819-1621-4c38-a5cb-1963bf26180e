<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.TraderCustomerBussinessAreaMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.TraderCustomerBussinessAreaEntity">
    <!--@mbg.generated-->
    <!--@Table T_TRADER_CUSTOMER_BUSSINESS_AREA-->
    <id column="TRADER_CUSTOMER_BUSSINESS_AREA_ID" jdbcType="INTEGER" property="traderCustomerBussinessAreaId" />
    <result column="TRADER_CUSTOMER_ID" jdbcType="INTEGER" property="traderCustomerId" />
    <result column="AREA_ID" jdbcType="INTEGER" property="areaId" />
    <result column="AREA_IDS" jdbcType="VARCHAR" property="areaIds" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    TRADER_CUSTOMER_BUSSINESS_AREA_ID, TRADER_CUSTOMER_ID, AREA_ID, AREA_IDS
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_TRADER_CUSTOMER_BUSSINESS_AREA
    where TRADER_CUSTOMER_BUSSINESS_AREA_ID = #{traderCustomerBussinessAreaId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_TRADER_CUSTOMER_BUSSINESS_AREA
    where TRADER_CUSTOMER_BUSSINESS_AREA_ID = #{traderCustomerBussinessAreaId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="TRADER_CUSTOMER_BUSSINESS_AREA_ID" keyProperty="traderCustomerBussinessAreaId" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerBussinessAreaEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_CUSTOMER_BUSSINESS_AREA (TRADER_CUSTOMER_ID, AREA_ID, AREA_IDS
      )
    values (#{traderCustomerId,jdbcType=INTEGER}, #{areaId,jdbcType=INTEGER}, #{areaIds,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="TRADER_CUSTOMER_BUSSINESS_AREA_ID" keyProperty="traderCustomerBussinessAreaId" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerBussinessAreaEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_CUSTOMER_BUSSINESS_AREA
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="traderCustomerId != null">
        TRADER_CUSTOMER_ID,
      </if>
      <if test="areaId != null">
        AREA_ID,
      </if>
      <if test="areaIds != null">
        AREA_IDS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="traderCustomerId != null">
        #{traderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="areaId != null">
        #{areaId,jdbcType=INTEGER},
      </if>
      <if test="areaIds != null">
        #{areaIds,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerBussinessAreaEntity">
    <!--@mbg.generated-->
    update T_TRADER_CUSTOMER_BUSSINESS_AREA
    <set>
      <if test="traderCustomerId != null">
        TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="areaId != null">
        AREA_ID = #{areaId,jdbcType=INTEGER},
      </if>
      <if test="areaIds != null">
        AREA_IDS = #{areaIds,jdbcType=VARCHAR},
      </if>
    </set>
    where TRADER_CUSTOMER_BUSSINESS_AREA_ID = #{traderCustomerBussinessAreaId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerBussinessAreaEntity">
    <!--@mbg.generated-->
    update T_TRADER_CUSTOMER_BUSSINESS_AREA
    set TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER},
      AREA_ID = #{areaId,jdbcType=INTEGER},
      AREA_IDS = #{areaIds,jdbcType=VARCHAR}
    where TRADER_CUSTOMER_BUSSINESS_AREA_ID = #{traderCustomerBussinessAreaId,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="TRADER_CUSTOMER_BUSSINESS_AREA_ID" keyProperty="traderCustomerBussinessAreaId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_CUSTOMER_BUSSINESS_AREA
    (TRADER_CUSTOMER_ID, AREA_ID, AREA_IDS)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.traderCustomerId,jdbcType=INTEGER}, #{item.areaId,jdbcType=INTEGER}, #{item.areaIds,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2023-08-14-->
  <delete id="deleteByTraderCustomerId">
        delete from T_TRADER_CUSTOMER_BUSSINESS_AREA
        where TRADER_CUSTOMER_ID=#{traderCustomerId,jdbcType=INTEGER}
    </delete>

<!--auto generated by MybatisCodeHelper on 2024-02-27-->
  <select id="findByTraderCustomerId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_TRADER_CUSTOMER_BUSSINESS_AREA
        where TRADER_CUSTOMER_ID=#{traderCustomerId,jdbcType=INTEGER}
    </select>

  <delete id="deleteByTraderCustomerIdList">
    delete from T_TRADER_CUSTOMER_BUSSINESS_AREA
    where TRADER_CUSTOMER_ID in
    <foreach collection="list" item="item" close=")" open="(" separator=",">
      #{item,jdbcType=INTEGER}
    </foreach>
  </delete>
</mapper>