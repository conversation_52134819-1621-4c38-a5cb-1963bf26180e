package com.vedeng.erp.aftersale.domain.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class AfterSalesRefundInfoDto {

    /**
     * 售后单id
     */
    private Integer afterSalesId;

    /**
     * 款项退还（0无 1退到客户余额 2退给客户）
     */
    private Integer refund;

    /**
     * 最终应退金额
     */
    private BigDecimal finalRefundableAmount;

    /**
     * 退款明细集合
     */
    private List<RefundDetailDto> refundDetailDtoList;

    /**
     * 退款明细
     */
    @Data
    public static class RefundDetailDto {

        /**
         * 交易主体（1对公 2对私）
         */
        private Integer traderSubject;

        /**
         * 交易主体名称
         */
        private String traderSubjectName;

        /**
         * 收款金额
         */
        private BigDecimal receivedAmount = BigDecimal.ZERO;

        /**
         * 已退金额
         */
        private BigDecimal refundedAmount = BigDecimal.ZERO;

        /**
         * 占用金额
         */
        private BigDecimal occupiedAmount = BigDecimal.ZERO;

        /**
         * 可退金额
         */
        private BigDecimal refundableAmount = BigDecimal.ZERO;

        /**
         * 退款金额
         */
        private BigDecimal refundAmount = BigDecimal.ZERO;

        /**
         * 已付金额
         */
        private BigDecimal paidAmount = BigDecimal.ZERO;
    }

}
