package com.vedeng.erp.saleorder.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.vedeng.common.constant.Constants;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.utils.validator.ValidatorUtils;
import com.vedeng.common.core.utils.validator.group.AddGroup;
import com.vedeng.erp.common.constants.JdUploadConstans;
import com.vedeng.erp.saleorder.dao.VJdGoodsMapper;
import com.vedeng.erp.saleorder.dao.VJdSaleorderMapper;
import com.vedeng.erp.saleorder.model.po.JdSaleorderVO;
import com.vedeng.erp.saleorder.model.po.VJdGoods;
import com.vedeng.erp.saleorder.model.po.VJdSaleorder;
import com.vedeng.erp.saleorder.model.po.VJdSaleorderFailed;
import com.vedeng.erp.saleorder.service.JdRegionService;
import com.vedeng.erp.saleorder.service.JdSaleorderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Author: Patric.Cheng
 * @CreateTime: 2022-12-07
 * @Description: TODO
 * @Version: 1.0
 */
@Service
@Slf4j
public class JdSaleorderServiceImpl implements JdSaleorderService {

    @Autowired
    private VJdSaleorderMapper vJdSaleorderMapper;
    @Autowired
    private VJdGoodsMapper vJdGoodsMapper;
    @Autowired
    private JdRegionService jdRegionService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importExcelFile(MultipartFile file,Integer traderId) throws IOException {
        long uploadstart = System.currentTimeMillis();
        List<VJdSaleorder> vJdSaleorderList = new ArrayList<>();
        List<VJdSaleorder> succeedList=new ArrayList<>();
        List<VJdSaleorder> failedList=new ArrayList<>();
        List<String> orderList=new CopyOnWriteArrayList<>();
        EasyExcel.read(file.getInputStream(), VJdSaleorder.class, new PageReadListener<VJdSaleorder>(dataList ->
                dataList.forEach(data -> {
                    if (StringUtils.isEmpty(data.getJdSaleorderNo())){
                        throw new ServiceException("导入失败，excel中存在空的订单号！");
                    }
                    if (!JdUploadConstans.ORDER_DELETE_STATUS.equals(data.getOrderStatus())){
                        if (orderList.contains(data.getJdSaleorderNo())){
                            throw new ServiceException("导入失败，excel中存在重复的订单号！");
                        }
                        data.setTraderId(traderId);
                        orderList.add(data.getJdSaleorderNo());

                        if(validData(data)){
                            succeedList.add(data);
                        }else{
                            failedList.add(data);
                        }
                    }
                }))).sheet().doRead();
        log.info("读取京东订单excel耗时--cost:{}ms", (System.currentTimeMillis() - uploadstart));
        if (CollectionUtil.isNotEmpty(succeedList)){
            long start = System.currentTimeMillis();
            addSkuNoAndRegionId(succeedList);
            log.info("回填地址和sku耗时--cost:{}ms", (System.currentTimeMillis() - start));
        }
        vJdSaleorderList.addAll(succeedList);
        vJdSaleorderList.addAll(failedList);
        if(CollectionUtil.isEmpty(vJdSaleorderList)){
            throw new ServiceException("上传的是空模板! 请检查文件");
        }
        log.info("批量处理jd商品信息, 处理成功 {} 条数据，失败 {} 条数据", succeedList.size(),failedList.size());
        List<String> repeatList= vJdSaleorderMapper.getNotAllowedOrder(vJdSaleorderList);
        List<String> errorOrderList= vJdSaleorderMapper.getErrorOrderList(vJdSaleorderList);
        log.info("重复订单数 {} 错误订单数 {}", repeatList.size(),errorOrderList.size());
        List<VJdSaleorder> insertList = vJdSaleorderList.stream().filter(item -> !repeatList.contains(item.getJdSaleorderNo())).collect(Collectors.toList());
        List<VJdSaleorder> updateList = vJdSaleorderList.stream().filter(item -> errorOrderList.contains(item.getJdSaleorderNo())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(insertList)){
            //新增订单信息，去重
            vJdSaleorderMapper.batchInsertJdSaleorder(insertList);
        }
        if (CollectionUtil.isNotEmpty(updateList)){
            //修改生成失败的订单信息
            for (VJdSaleorder vJdSaleorder : updateList) {
                vJdSaleorder.setOrderGenerateStatus(1);
                vJdSaleorder.setTraderId(traderId);
                vJdSaleorder.setAddTime(new Date());
                vJdSaleorder.setModTime(new Date());
            }
            vJdSaleorderMapper.updateBatchByJdSaleorderNo(updateList);
        }

    }

    static String regex = "^1\\d{10}-\\d{4}$"; // 以1开头，后面跟10位数字，然后是“-”和4位数字
    // 编译正则表达式
    static Pattern pattern = Pattern.compile(regex);
    /**
     * 校验excel参数是否合规
     * @param data
     * @return
     */
    private boolean validData(VJdSaleorder data) {
        String msg = ValidatorUtils.validateData(data, AddGroup.class);
        data.setOrderGenerateStatus(Constants.ONE);
        data.setErrorReason("");
        data.setRegionId(Constants.ZERO);
        if (StringUtils.isNotBlank(msg)){
            data.setErrorReason(JdUploadConstans.REQUIRED_FIELDS_VALID);
            data.setOrderGenerateStatus(Constants.FOUR);
            return false;
        }
        if (!NumberUtil.isNumber(data.getBuyPrice())){
            data.setErrorReason("采购价必须为数字类型");
            data.setOrderGenerateStatus(Constants.FOUR);
            return false;
        }
        data.setPurchasingPrice(new BigDecimal(data.getBuyPrice()));
        if(!StringUtils.isNumeric(data.getBuyNum())){
            data.setErrorReason("购买数量必须为数字类型");
            data.setOrderGenerateStatus(Constants.FOUR);
            return false;
        }
        Matcher matcher = pattern.matcher(data.getArrivalUserPhone());

        if(!(StringUtils.isNumeric(data.getArrivalUserPhone())  || matcher.matches())){
            data.setErrorReason("收货人电话格式错误");
            data.setOrderGenerateStatus(Constants.FOUR);
            return false;
        }
        data.setNum(Integer.parseInt(data.getBuyNum()));
        if(!StringUtils.isNumeric(data.getJdSkuNo())){
            data.setErrorReason(JdUploadConstans.NO_MATCH_GOODS);
            data.setOrderGenerateStatus(Constants.FOUR);
            return false;
        }
        return true;
    }

    /**
     * 填充贝登skuNo和regionId
     * @param succeedList
     */
    private void addSkuNoAndRegionId(List<VJdSaleorder> succeedList) {
        //填充贝登skuNo
        List<VJdGoods> vJdGoodsList=vJdGoodsMapper.getGoodsMappingByList(succeedList);
        Map<Long, String> skuMap = vJdGoodsList.stream().collect(Collectors.toMap(VJdGoods::getJdGoodsId, VJdGoods::getVdSkuNo, (key1, key2) -> key2));
        for (VJdSaleorder vJdSaleorder : succeedList) {
            String jdSkuNo = vJdSaleorder.getJdSkuNo();
            String errorReason = vJdSaleorder.getErrorReason();
            long jdGoodsId=Long.parseLong(jdSkuNo);
            if (skuMap.containsKey(jdGoodsId)){
                vJdSaleorder.setSkuNo(skuMap.get(jdGoodsId));
            }else{
                vJdSaleorder.setOrderGenerateStatus(Constants.FOUR);
                vJdSaleorder.setErrorReason(StringUtils.isEmpty(errorReason)?JdUploadConstans.NO_MATCH_GOODS:errorReason+","+JdUploadConstans.NO_MATCH_GOODS);
            }
        }
        //填充贝登regionId
        List<VJdSaleorder> vJdSaleorders = jdRegionService.selectJdToVdRegion(succeedList);
        Map<String, Integer> regionMap = vJdSaleorders.stream().collect(Collectors.toMap(VJdSaleorder::getJdSaleorderNo, VJdSaleorder::getRegionId, (key1, key2) -> key2));
        for (VJdSaleorder vJdSaleorder : succeedList) {
            String jdSaleorderNo = vJdSaleorder.getJdSaleorderNo();
            String errorReason = vJdSaleorder.getErrorReason();
            if (!Constants.ZERO.equals(regionMap.get(jdSaleorderNo))){
                vJdSaleorder.setRegionId(regionMap.get(jdSaleorderNo));
            } else{
                vJdSaleorder.setOrderGenerateStatus(Constants.FOUR);
                vJdSaleorder.setErrorReason(StringUtils.isEmpty(errorReason)?JdUploadConstans.NO_MATCH_ADDRESS:errorReason+","+JdUploadConstans.NO_MATCH_ADDRESS);
            }
        }
    }
}
