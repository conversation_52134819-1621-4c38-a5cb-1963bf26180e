package com.newtask.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newtask.LogisticsInfoTask;
import com.rabbitmq.ErpMsgProducer;
import com.rabbitmq.RabbitConfig;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.exception.BusinessException;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.logistics.dao.ExpressMapper;
import com.vedeng.logistics.dao.LogisticsMapper;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.Logistics;
import com.vedeng.logistics.service.ExpressService;
import com.vedeng.logistics.service.LogisticsService;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.Buyorder;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 直发采购订单能否确认收货job
 */
@JobHandler(value = "CheckExpressEnableReceiveTask")
@Component
@Slf4j
public class CheckExpressEnableReceiveTask extends AbstractJobHandler {
    @Resource
    ExpressMapper expressMapper;
    @Resource
    BuyorderMapper buyorderMapper;
    @Resource
    ExpressService expressService;
    @Resource
    LogisticsService logisticsService;
    @Resource
    LogisticsInfoTask logisticsInfoTask;
    @Autowired
    private ErpMsgProducer erpMsgProducer;
    @Resource
    private LogisticsMapper logisticsMapper;

    private static final Integer LIMIT_SIZE = 200;

    @Override
    public ReturnT<String> doExecute(String logisticsNo) throws Exception {
        log.info("开始执行直发采购订单是否能签收JOB,快递单号:{},",logisticsNo);
        handleJob(logisticsNo);
        return ReturnT.SUCCESS;
    }

    private void handleJob(String logisticsNo) {
        List<Express> firstExpressList = expressMapper.selectFirstEnableReceiveList(logisticsNo,LIMIT_SIZE);
        if (CollUtil.isNotEmpty(firstExpressList)){
            dealJob(firstExpressList);
            if (CollUtil.size(firstExpressList) == LIMIT_SIZE){
                continueHandle(CollUtil.getLast(firstExpressList).getExpressId());
            }
        }
    }

    private void continueHandle(Integer lastExpressID) {
        log.info("继续执行直发采购订单是否能签收JOB,上次执行的最后一条expressID为:{}",lastExpressID);
        List<Express> continueExpressList = expressMapper.selectContinueEnableReceiveList(lastExpressID,LIMIT_SIZE);
        dealJob(continueExpressList);
        if (CollUtil.isNotEmpty(continueExpressList) && continueExpressList.size() == LIMIT_SIZE){
            continueHandle(CollUtil.getLast(continueExpressList).getExpressId());
        }
    }

    private void dealJob(List<Express> expressList) {
        for (Express ex : expressList) {
            try {
                log.info("直发采购订单是否能签收JOB,express信息:{}", JSON.toJSONString(ex));
                //排除不需要调用快递100的单号
                if (StringUtils.isNotBlank(ex.getLogisticsNo())
                        && ex.getLogisticsNo().startsWith("XN")) {
                    log.info("直发采购订单是否能签收JOB,快递不需要查询快递100,快递信息:{},物流单号:{}", JSONUtil.toJsonStr(ex), ex.getLogisticsNo());
                    continue;
                }
                //调用快递100服务判断该快递是否已签收
                Boolean isArrived = isArrivedFromKuaiDi100(ex);
                if (isArrived) {
                    //快递关联的直发采购订单
                    List<Buyorder> buyorderList = buyorderMapper.selectBuyorderByExpressId(ex.getExpressId());
                    Integer count = expressMapper.updateEnableReceiveById(ex.getExpressId());
                    if (count > 0) {
                        buyorderMapper.updateExpressEnableReceiveByIds(buyorderList);
                        //24小时后未确认收货发站内信(死信队列)
                        try {
                            log.info("直发采购单能否确认收货,发送消息至mq,快递信息:{}", JSONUtil.toJsonStr(ex));
                            erpMsgProducer.sendMsg(RabbitConfig.BUYORDER_ENABLE_RECEIVE_LOCK_EXCHANGE, RabbitConfig.BUYORDER_ENABLER_ECEIVE_LOCK_ROUTINGKEY, JSONUtil.toJsonStr(ex));
                        } catch (Exception e) {
                            log.error("直发采购单能否确认收货,发送队列消息异常 - 快递信息：{}:", JSON.toJSONString(ex));
                            throw new BusinessException("直发采购单能否确认收货,发送队列消息异常");
                        }
                    }
                }
            } catch (Exception e) {
                log.error("执行直发采购订单是否能签收JOB,调用快递100异常", e);
            }
        }
    }
    private Boolean isArrivedFromKuaiDi100(Express express) {
        String phone = expressService.getPhoneByBusinessType(express.getExpressId(), express.getLogisticsNo());
        Logistics logisticsById = logisticsMapper.getExistLogisticsById(express.getLogisticsId());
        if (ObjectUtil.isNull(logisticsById)){
            log.info("未查到物流信息，快递信息:{}",JSONUtil.toJsonStr(express));
            return Boolean.FALSE;
        }
        ResultInfo resultInfo = logisticsInfoTask.queryInfo(logisticsService.getLogisticsCode(logisticsById.getName()), express.getLogisticsNo(), phone);
        if (ObjectUtil.isNotNull(resultInfo) && ObjectUtil.isNotNull(resultInfo.getData()) && ObjectUtil.isNotNull(resultInfo.getCode()) && ErpConst.ZERO.equals(resultInfo.getCode())) {
            String data = resultInfo.getData().toString();
            JSONObject jsonObject = JSON.parseObject(data);
            if (ObjectUtil.isNotNull(jsonObject) &&
                    ("1".equals(jsonObject.getString("ischeck")) || "3".equals(jsonObject.getString("state")))){
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }
}
