package com.vedeng.logistics.model;

import java.io.Serializable;
import java.util.Date;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Data;

/**
    * 出入库日志主表
    */
@Data
public class WarehouseGoodsOutIn extends BaseEntity implements Serializable {
    /**
    * 主键
    */
    private Long warehouseGoodsOutInId;

    /**
    * ERP出入库单号（自动生成）
    */
    private String outInNo;

    /**
    * WMS出入库单号
    */
    private String wmsNo;

    /**
    * 关联单号
    */
    private String relateNo;

    /**
    * 出入库类型 1入库 2出库 3销售换货入库 4销售换货出库 5销售退货入库 6采购退货出库 7采购换货出库 8采购换货入库 9外借入库 10外借出库 11调整盘盈入库 12盘盈入库 13报废出库 14领用出库 15 调整盘亏 16 盘亏出库
    */
    private Integer outInType;

    /**
    * 收发货方
    */
    private String outInCompany;

    /**
    * 出入库时间
    */
    private Date outInTime;

    /**
    * 来源 ERP WMS
    */
    private String source;

    /**
    * 是否删除 0否 1是
    */
    private Integer isDelete;

    /**
    * 备注
    */
    private String remark;

    /**
    * 更新备注
    */
    private String updateRemark;

    /**
     * 是否虚拟0否1是
     */
    private Integer isVirture;

    private static final long serialVersionUID = 1L;
}