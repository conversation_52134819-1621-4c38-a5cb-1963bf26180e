package com.vedeng.goods.model;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class SpuMigratedLogDomain {

    private Integer spuMigtationLogId;


    private Integer spuId;


    private String spuNo;


    private Integer categoryIdPre;


    private String categoryNamePre;


    private Integer categoryIdAfter;


    private String categoryNameAfter;


    private String originPath;

    private String targetPath;


    private String migrationReason;


    /**
     * 增补属性id集合
     */
    private String addAttrs;

    /**
     * 增补属性值id集合
     */
    private String addAttrValues;

    private Integer deleted;


    private Long addTime;

    private Integer creator;

    private Long updateTime;

    private Integer updater;

}