Vue.component('ui-cascader-panel', {
    template: `
        <div class="menu-wrap">
            <ul 
                v-if="level1List.length"
                class="vd-ui-cascader-node"
                @click.stop
                :style="styles"
                @mousedown.prevent
            >
                <li
                    v-for="(item, index) in level1List" 
                    :key="index"
                    @click="openL1(item)"
                    :class="{'selected': tempChoose[0] && tempChoose[0].value == item.value }"
                >
                    <template v-if="each">
                        <i class="vd-ui_icon icon-radio3" v-if="!tempChoose[1] && tempChoose[0] && tempChoose[0].value == item.value"></i>
                        <i class="vd-ui_icon icon-radio1" v-else></i>
                    </template>
                    <p v-html="item.label"></p>
                    <i class="vd-ui_icon icon-app-right icon-right" v-if="item.children && item.children.length"></i>
                    <i class="vd-ui_icon icon-selected2 icon-right" v-else-if="!each && tempChoose[0] && tempChoose[0].value == item.value"></i>
                </li>
            </ul>

            <ul 
                v-if="level2List.length"
                class="vd-ui-cascader-node"
                @click.stop
                :style="styles"
                @mousedown.prevent
            >
                <li
                    v-for="(item, index) in level2List" 
                    :key="index"
                    @click="openL2(item)"
                    :class="{'selected': tempChoose[1] && tempChoose[1].value == item.value }"
                >
                    <template v-if="each">
                        <i class="vd-ui_icon icon-radio3" v-if="!tempChoose[2] && tempChoose[1] && tempChoose[1].value == item.value"></i>
                        <i class="vd-ui_icon icon-radio1" v-else></i>
                    </template>
                    <p v-html="item.label"></p>
                    <i class="vd-ui_icon icon-app-right icon-right" v-if="item.children && item.children.length"></i>
                    <i class="vd-ui_icon icon-selected2 icon-right" v-else-if="!each && tempChoose[1] && tempChoose[1].value == item.value"></i>
                </li>
            </ul>

            <ul 
                v-if="level3List.length"
                class="vd-ui-cascader-node"
                @click.stop
                :style="styles"
                @mousedown.prevent
            >
                <li
                    v-for="(item, index) in level3List" 
                    :key="index"
                    @click="openL3(item)"
                    :class="{'selected': tempChoose[2] && tempChoose[2].value == item.value }"
                >
                    <template v-if="each">
                        <i class="vd-ui_icon icon-radio3" v-if="tempChoose[2] && tempChoose[2].value == item.value"></i>
                        <i class="vd-ui_icon icon-radio1" v-else></i>
                    </template>
                    <p v-html="item.label"></p>
                    <i class="vd-ui_icon icon-selected2 icon-right" v-if="!each && tempChoose[2] && tempChoose[2].value == item.value"></i>
                </li>
            </ul>
        </div>
    `,

    props: {
        data: {
            type:Array,
            default:()=> {
                return []
            }
        },
        selectObj: {
            type: Array
        },
        styles: {
            type: Object,
            default: ()=> {
                return {}
            }
        },
        multiple: {
            type: Boolean,
            default: false
        },
        each: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            level1List: [],
            level2List: [],
            level3List: [],
            tempChoose: [],
        }
    },
    computed:{
    },
    watch: {
        selectObj: {
            deep: true,
            handler (newV) {
                if (newV.length) {
                    this.tempChoose = JSON.parse(JSON.stringify(newV));
                    let l1Info = this.data.filter(item=> item.value == newV[0].value)[0] || {};
                    let l2List = l1Info.children || [];
                    this.level2List = l2List;

                    if (newV[1] && newV[1].value) {
                        let l2Info = l2List.filter(item=> item.value == newV[1].value)[0] || {};
                        let l3List = l2Info.children || [];
                        this.level3List = l3List;
                    } else {
                        this.level3List = [];
                    }
                }
            },
            immediate: true
        }
    },
    mounted() {
        this.level1List = JSON.parse(JSON.stringify(this.data));
        console.log(this.each + '----------------')
    },
    methods: {
        openL1 (val) {
            this.tempChoose = [val];
            this.level2List = val.children || [];
            this.level3List = [];

            if (!this.level2List.length || this.each) { // 直接选中
                let arr =  [{
                    label: val.label,
                    value: val.value,
                    level: val.level
                }]
                this.$emit('pick', arr);
            } 
            
        },
        openL2 (val) {
            this.tempChoose = [this.tempChoose[0], val];
            this.level3List = val.children || [];

            if (!this.level3List.length || this.each) { // 直接选中
                this.$emit('pick', this.tempChoose);
            }
        },
        openL3 (val) {
            this.tempChoose = [this.tempChoose[0], this.tempChoose[1], val];
            this.$emit('pick', this.tempChoose);
        },
    }
})