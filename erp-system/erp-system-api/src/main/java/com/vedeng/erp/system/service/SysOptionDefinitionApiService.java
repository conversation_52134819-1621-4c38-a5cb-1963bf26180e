package com.vedeng.erp.system.service;

import com.vedeng.common.core.enums.InvoiceApplyCheckRuleEnum;
import com.vedeng.erp.system.dto.SysOptionDefinitionDto;

import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.system.service
 * @Date 2022/7/15 14:49
 */
public interface SysOptionDefinitionApiService {

    /**
     * 获取字段组opt_type下的所有字段值
     * @param optType
     * @return
     */
    List<SysOptionDefinitionDto> getOptionDefinitionListByOptType(String optType);


    /**
     * 根据字典组的code查询下面所有字典项的信息
     * @param parentCode 字典组code
     * @return 该组下 所有字典项的信息
     */
    List<SysOptionDefinitionDto> getOptionDefinitionListByParentCode(String parentCode);

    /**
     * 根据字典项id查询字典项信息
     * @param id 字典项id
     * @return SysOptionDefinitionDto
     */
    SysOptionDefinitionDto getOptionDefinitionById(Integer id);

    /**
     * 根据code获取字典表
     * @param code
     * @return
     */
    SysOptionDefinitionDto getOptionDefinition(String code);


    /**
     * 根据ids查询字典项信息
     * @param ids 字典项id
     * @return SysOptionDefinitionDto List
     */
    List<SysOptionDefinitionDto> getByIds(List<Integer> ids);

    /**
     * 根据父级字典项id集合查询所有的子项
     *
     * @param parentIds 父级字典项id集合
     * @return List<SysOptionDefinitionDto>
     */
    List<SysOptionDefinitionDto> getByParentIdList(List<Integer> parentIds);


    int updateInvoiceSwitch(SysOptionDefinitionDto sysOptionDefinitionDto);
    /**
     * 开票配置按钮是否显示
     */
    Boolean getInvoiceApplyButtonShow(InvoiceApplyCheckRuleEnum invoiceApplyCheckRuleEnum);

}
