package com.vedeng.crm.business.quote.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.vedeng.crm.business.quote.service.impl.ImageConverter;
import com.vedeng.crm.business.quote.service.impl.SkuToUrlConverter;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ContentRowHeight(40)
public class QuoteOrderGoodsExportVo {

    @ExcelProperty(value = "序号")
    private Integer id;
    /**
     * 产品需求
     */
    @ExcelProperty(value = "需求产品")
    @ColumnWidth(20)
    private String productNeeds;

    /**
     * 数量需求
     */
    @ExcelProperty(value = "需求数量")
    @ColumnWidth(20)
    private String numNeeds;

    private String distributeBudget;
    private String terminalBudget;

    /**
     * 需求预算
     */
    @ExcelProperty(value = "需求预算")
    @ColumnWidth(20)
    private String budgetaryNeeds;

    /**
     * 需求备注
     */
    @ExcelProperty(value = "需求备注")
    @ColumnWidth(20)
    private String extraNeeds;

    /**
     * 订货号
     */
    @ExcelProperty(value = "订货号", converter = SkuToUrlConverter.class)
    @ColumnWidth(15)
    private String skuNo;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    @ColumnWidth(40)
    private String skuName;

    /**
     * 图片 设置宽高
     */
    @ExcelProperty(value = "图片", converter = ImageConverter.class)
    @ColumnWidth(10)
    private String picture;

    /**
     * 品牌
     */
    @ExcelProperty(value = "品牌")
    @ColumnWidth(20)
    private String brandName;

    /**
     * 型号
     */
    @ExcelProperty(value = "型号")
    @ColumnWidth(20)
    private String model;


    @ExcelProperty(value = "生产厂家")
    @ColumnWidth(20)
    private String companyName;


    /**
     * 主要参数
     */
    @ExcelProperty(value = "主要参数")
    @ColumnWidth(40)
    private String mainParameter;

    /**
     * 使用年限/效期
     */
    @ExcelProperty(value = "使用年限/效期")
    @ColumnWidth(20)
    private String useLife;


    @ExcelProperty(value = "质保政策")
    @ColumnWidth(20)
    private String warrantyInfo;

    /**
     * 预计货期
     */
    @ExcelProperty(value = "预计货期")
    @ColumnWidth(20)
    private String expectDeliveryTime;

    /**
     * 销售单价
     */
    @ExcelProperty(value = "销售单价")
    @ColumnWidth(20)
    private BigDecimal salePrice;

    /**
     * 数量
     */
    @ExcelProperty(value = "数量")
    @ColumnWidth(20)
    private Integer num;

    @ExcelProperty(value = "市场价")
    @ColumnWidth(20)
    private BigDecimal marketPrice;

    @ExcelProperty(value = "拿货总价")
    @ColumnWidth(20)
    private BigDecimal total;


    /**
     * 单位
     */
    @ExcelProperty(value = "单位")
    @ColumnWidth(20)
    private String unitName;

    /**
     * 归属产品经理&助理
     */
    @ExcelProperty(value = "产品负责")
    @ColumnWidth(20)
    private String productManager;

    /**
     * 产品是否可安装 1-是 0-否 null展示-
     */
    @ExcelProperty(value = "是否可安装")
    @ColumnWidth(20)
    private String isInstall;

    @ExcelIgnore
    private Integer skuId;

    @ExcelIgnore
    private Integer reportStatus;

    /**
     * 产品报备状态
     * 0:'无需报备',1:'报备中',2:'报备成功',3:'报备失败'
     */
    @ExcelProperty(value = "产品报备状态")
    @ColumnWidth(20)
    private String reportStatusStr;

    /**
     * 注册证
     */
    @ExcelProperty(value = "注册证")
    @ColumnWidth(20)
    private String registrationNumber;
}
