package com.vedeng.erp.trader.common.enums;

import com.vedeng.common.core.exception.ServiceException;

import java.util.Arrays;

/**
 * T_TRADER_CUSTOMER_MARKETING_PRINCIPAL 枚举值
 */
public enum TraderCustomerMarketingPrincipalEnum {
    /**
     * 默认值
     */
    DEFAULT("", -100, "其他"),

    /**
     * 商品类型-设备
     */
    SKU_TYPE_DEVICE("SKU_TYPE", 0, "设备"),

    /**
     * 商品类型-高值耗材
     */
    SKU_TYPE_HIGH_VALUE_CONSUMABLES("SKU_TYPE", 1, "高值耗材"),

    /**
     * 商品类型-中低值耗材
     */
    SKU_TYPE_LOW_VALUE_CONSUMABLES("SKU_TYPE", 2, "中低值耗材"),

    /**
     * 商品类型-试剂
     */
    SKU_TYPE_REAGENT("SKU_TYPE", 3, "试剂"),

    /**
     * 商品类型-软件
     */
    SKU_TYPE_SOFTWARE("SKU_TYPE", 4, "软件"),

    /**
     * 商品范畴-综合
     */
    SKU_SCOPE_COMPREHENSIVE("SKU_SCOPE", 0, "综合"),

    /**
     * 商品范畴-专业
     */
    SKU_SCOPE_SPECIALITY("SKU_SCOPE", 1, "专业"),

    /**
     * 销售类别-直销为主
     */
    SALES_TYPE_DIRECT_SALES("SALES_TYPE", 0, "直销为主"),

    /**
     * 销售类别-分销为主
     */
    SALES_TYPE_DISTRIBUTION("SALES_TYPE", 1, "分销为主"),

    /**
     * 销售类别-直分销并重
     */
    SALES_TYPE_DIRECT_AND_DISTRIBUTION("SALES_TYPE", 2, "直分销并重"),

    /**
     * 政府关系-卫健委
     */
    GOVERNMENT_RELATION_HEALTH_COMMISSION("GOVERNMENT_RELATION", 0, "卫健委关系"),

    /**
     * 政府关系-医联体
     */
    GOVERNMENT_RELATION_MEDICAL_CONSORTIUM("GOVERNMENT_RELATION", 1, "医联体关系"),

    GOVERNMENT_RELATION_PRIMARY_MEDICAL_CARE("GOVERNMENT_RELATION", 3, "基层医疗关系"),

    GOVERNMENT_RELATION_GRADE_HOSPITAL("GOVERNMENT_RELATION", 4, "等级医院关系"),

    GOVERNMENT_RELATION_EMERGENCY_MEDICAL_INSTITUTIONS("GOVERNMENT_RELATION", 5, "应急医疗机构关系"),

    GOVERNMENT_RELATION_PROFESSIONAL_PUBLIC_HEALTH_INSTITUTIONS("GOVERNMENT_RELATION", 6, "专业公共卫生机构关系"),

    /**
     * 政府关系-其他
     */
    GOVERNMENT_RELATION_OTHER("GOVERNMENT_RELATION", 2, "其他关系"),

    /**
     * 客户有效性-无效
     */
    EFFECTIVENESS_INVALID("EFFECTIVENESS", 0, "无效"),

    /**
     * 客户有效性-有效
     */
    EFFECTIVENESS_EFFECTIVE("EFFECTIVENESS", 1, "有效");

    /**
     * 字段名
     */
    private final String code;

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * label
     */
    private final String label;

    TraderCustomerMarketingPrincipalEnum(String code, Integer value, String label) {
        this.code = code;
        this.value = value;
        this.label = label;
    }

    public String getCode() {
        return code;
    }

    public Integer getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    public static TraderCustomerMarketingPrincipalEnum getEnum(String code, Integer value) {
        return Arrays.stream(TraderCustomerMarketingPrincipalEnum.values())
                .filter(enums -> enums.getCode().equals(code) && enums.getValue().equals(value))
                .findFirst().orElseThrow(() -> new ServiceException("无法获取对应的主营属性枚举"));
    }

    public static String getEnumName(String code, Integer value) {
        return Arrays.stream(TraderCustomerMarketingPrincipalEnum.values())
                .filter(enums -> enums.getCode().equals(code) && enums.getValue().equals(value))
                .findFirst().orElse(TraderCustomerMarketingPrincipalEnum.DEFAULT).getLabel();
    }

}
