package com.vedeng.erp.trader.service;

import com.vedeng.erp.trader.dto.TraderFinanceDto;

import java.util.List;

/**
 * <AUTHOR>
 * @description 客户财务信息
 * @date 2022/9/8 9:41
 **/
public interface TraderFinanceApiService {

    /**
     * 查询客户财务信息
     * @param traderCustomerId 客户id
     * @param traderId trader表id
     * @return
     */
    TraderFinanceDto selectCustomerFiance(Integer traderCustomerId, Integer traderId);

    /**
     * 新增客户财务信息
     * @param traderFinanceDto 对象
     */
    void add(TraderFinanceDto traderFinanceDto);

    /**
     * 根据客户id查询财务信息
     * @param traderId 客户id
     * @param traderType
     * @return 客户财务信息
     */
    TraderFinanceDto selectByTraderIdAndTraderType(Integer traderId, Integer traderType);

    /**
     * 更新客户财务信息
     * @param traderFinanceDto 客户信息
     */
    void update(TraderFinanceDto traderFinanceDto);


    List<TraderFinanceDto> findByTraderIdAndTraderType(Integer traderId, Integer one);
}
