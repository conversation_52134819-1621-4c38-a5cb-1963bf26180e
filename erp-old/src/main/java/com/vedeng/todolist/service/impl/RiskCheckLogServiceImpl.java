package com.vedeng.todolist.service.impl;

import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.goods.model.CoreSpu;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.Saleorder;
import com.vedeng.todolist.constant.RiskCheckTodoListBuzPropertyEnum;
import com.vedeng.todolist.dao.RiskCheckLogMapper;
import com.vedeng.todolist.model.RiskCheckLog;
import com.vedeng.todolist.service.RiskCheckLogService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.*;

/**
 * @Author: daniel
 * @Date: 2020/12/21 08 51
 * @Description:
 */
@Service
public class RiskCheckLogServiceImpl implements RiskCheckLogService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RiskCheckLogServiceImpl.class);

    @Autowired
    private RiskCheckLogMapper riskCheckLogMapper;

    @Autowired
    private BuyorderMapper buyorderMapper;

    @Autowired
    private SaleorderMapper saleorderMapper;

    @Override
    public synchronized List<String> getUnTriggeredOrderOfRickCheck(String riskCheckTodoListBuzPropertyEnum) {
        if (StringUtils.isBlank(riskCheckTodoListBuzPropertyEnum)){
            return new ArrayList<>();
        }
        return riskCheckLogMapper.getUnTriggerOrderOfHasFinished(riskCheckTodoListBuzPropertyEnum);
    }

    @Override
    public void autoFinishTriggerOfRiskCheck(String riskCheckBuzExtra) {
        LOGGER.info("自动完成风控订单：{}",riskCheckBuzExtra);
        if (StringUtils.isBlank(riskCheckBuzExtra)){
            return;
        }
        if (riskCheckLogMapper.getUnTriggerOrderOfHasFinishedByOrderNo(riskCheckBuzExtra).size() > 0){
            LOGGER.info("自动完成风控订单：{}的触发任务",riskCheckBuzExtra);
            RiskCheckLog toUpdate = new RiskCheckLog();
            toUpdate.setRiskCheckBuzExtra(riskCheckBuzExtra);
            toUpdate.setRiskCheckTriggerStatus(1);
            toUpdate.setRiskCheckTriggerTime(DateUtil.sysTimeMillis());
            //默认njadmin为自动风控任务触发者
            toUpdate.setRiskCheckTriggerUser(2);
            riskCheckLogMapper.finishTriggerOfRiskCheckLog(toUpdate);
            sendNoticeWhenOrderTriggered(riskCheckBuzExtra);
        }
    }

    @Override
    public void manualFinishTriggerOfRiskCheck(String riskCheckBuzExtra, Integer riskCheckTriggerUser, String riskCheckTriggerComment) {
        LOGGER.info("人工完成风控订单：{}的触发任务，触发者：{}，备注为：{}",riskCheckBuzExtra,riskCheckTriggerUser,riskCheckTriggerComment);
        if (StringUtils.isBlank(riskCheckBuzExtra) || riskCheckTriggerUser == null || riskCheckTriggerUser == 0){
            return;
        }
        if (riskCheckLogMapper.getUnTriggerOrderOfUnFinishedByOrderNo(riskCheckBuzExtra).size() > 0){
            LOGGER.info("人工完成风控订单：{}的触发任务，触发者：{}，备注为：{}，更新触发状态",riskCheckBuzExtra,riskCheckTriggerUser,riskCheckTriggerComment);
            RiskCheckLog toUpdate = new RiskCheckLog();
            toUpdate.setRiskCheckBuzExtra(riskCheckBuzExtra);
            toUpdate.setRiskCheckTriggerStatus(1);
            toUpdate.setRiskCheckTriggerTime(DateUtil.sysTimeMillis());
            toUpdate.setRiskCheckTriggerUser(riskCheckTriggerUser);
            toUpdate.setRiskCheckTriggerComment(riskCheckTriggerComment);
            riskCheckLogMapper.finishTriggerOfRiskCheckLog(toUpdate);
            sendNoticeWhenOrderTriggered(riskCheckBuzExtra);
        }
    }

    @Override
    public void sendMessage2SaleWhenHcOrderRiskCheck(String hcOrderNo) {
        if (StringUtils.isBlank(hcOrderNo)){
            return;
        }
        Saleorder hcSaleorder = new Saleorder();
        hcSaleorder.setSaleorderNo(hcOrderNo);
        hcSaleorder = saleorderMapper.getSaleorderBySaleorderNo(hcSaleorder);
        if(hcSaleorder == null || ErpConst.FIVE.equals(hcSaleorder.getOrderType())){
            return;
        }
        LOGGER.info("HC订单：{}风控，推送站内信",hcOrderNo);
        //如果该订单已经被风控，则无需发站内信
        if (riskCheckLogMapper.getRiskCheckLogByBuzExtra(hcOrderNo) == null) {
            Saleorder saleorder = saleorderMapper.getSalerOfSaleorderByOrderNo(hcOrderNo);
            String url = "/order/hc/hcOrderDetailsPage.do?saleorderId=" + saleorder.getSaleorderId();
            Map<String, String> params = new HashMap<>(1);
            params.put("orderNo",hcOrderNo);
            MessageUtil.sendMessage(164,Collections.singletonList(saleorder.getCreator()),params,url);
            LOGGER.info("HC订单：{}，向用户：{}推送站内信",hcOrderNo,saleorder.getCreator());
        }
    }


    private void sendNoticeWhenOrderTriggered(String riskCheckBuzExtra){
        RiskCheckLog log = riskCheckLogMapper.getRiskCheckLogByBuzExtra(riskCheckBuzExtra);
        int messageTemplateId = 0;
        String url = null;
        Map<String,String> params = new HashMap<>(1);
        params.put("orderNo",riskCheckBuzExtra);
        List<Integer> userList = new ArrayList<>();
        if (RiskCheckTodoListBuzPropertyEnum.RISK_CHECK_BUYORDER.getValue().equals(log.getRiskCheckBuzProperty())){
            //采购单风控解除后，推送消息给采购单中产品归属
            Buyorder buyorder = buyorderMapper.getBuyOrderByOrderNo(riskCheckBuzExtra);
            Set<Integer> assignList = new HashSet<>();
            for (CoreSpu spu : buyorderMapper.getAssignListOfSkuInBuyorder(buyorder.getBuyorderId())){
                assignList.add(spu.getAssignmentAssistantId());
                assignList.add(spu.getAssignmentManagerId());
            }
            userList.addAll(assignList);
            messageTemplateId = 161;
            url = "order/buyorder/viewBuyorder.do?&buyorderId=" + buyorder.getBuyorderId();
        } else if (RiskCheckTodoListBuzPropertyEnum.RISK_CHECK_SALEORDER.getValue().equals(log.getRiskCheckBuzProperty())){
            //销售单风控解除后，推送消息给销售单归属销售
            Saleorder saleorder = saleorderMapper.getSalerOfSaleorderByOrderNo(riskCheckBuzExtra);
            userList.add(saleorder.getCreator());
            messageTemplateId = 162;
            if (saleorder.getOrderType() == 5){
                url = "/order/hc/hcOrderDetailsPage.do?saleorderId=" + saleorder.getSaleorderId();
            } else {
                url = "/order/saleorder/view.do?saleorderId=" + saleorder.getSaleorderId();
            }
        }
        if (userList.size() > 0) {
            MessageUtil.sendMessage(messageTemplateId,userList,params,url);
        }
    }
}
