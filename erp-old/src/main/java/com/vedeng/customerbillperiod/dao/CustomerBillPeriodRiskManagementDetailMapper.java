package com.vedeng.customerbillperiod.dao;

import com.vedeng.customerbillperiod.model.CustomerBillPeriodRiskManagementDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CustomerBillPeriodRiskManagementDetailMapper {

    int insert(CustomerBillPeriodRiskManagementDetail record);

    int insertSelective(CustomerBillPeriodRiskManagementDetail record);

    CustomerBillPeriodRiskManagementDetail selectByPrimaryKey(Long billPeriodRiskManagementDetailId);

    int updateByPrimaryKeySelective(CustomerBillPeriodRiskManagementDetail record);

    /**
     * 获取订单最新的账期管理编码
     * @param companyId 公司id
     * @param customerId 客户id
     * @param orderId 订单id
     * @return 最新的账期管理编码
     */
    String geNewestBillPeriodManagementCodeByOrderId(@Param("companyId") Integer companyId, @Param("customerId") Long customerId, @Param("orderId") Long orderId);

    /**
     * 根据订单获取未归还完毕的账期风险管理明细
     * @param companyId 公司id
     * @param customerId 客户id
     * @param orderId 订单id
     * @return 风险管理编码
     */
    List<CustomerBillPeriodRiskManagementDetail> getRiskManagementDetailListUnreturnedByOrderId(@Param("companyId") Integer companyId,
                                                                                                @Param("customerId") Long customerId,
                                                                                                @Param("orderId") Long orderId);
}