package com.vedeng.erp.trader.mapper;
import java.util.Collection;
import java.util.List;


import com.vedeng.erp.trader.domain.entity.SpecialSalesEntity;
import com.vedeng.erp.trader.dto.SpecialSalesDto;
import org.apache.ibatis.annotations.Param;

public interface SpecialSalesMapper {
    int deleteByPrimaryKey(Integer specialSalesId);

    int insert(SpecialSalesEntity record);

    int insertOrUpdate(SpecialSalesEntity record);

    int insertOrUpdateSelective(SpecialSalesEntity record);

    int insertSelective(SpecialSalesEntity record);

    SpecialSalesEntity selectByPrimaryKey(Integer specialSalesId);

    int updateByPrimaryKeySelective(SpecialSalesEntity record);

    int updateByPrimaryKey(SpecialSalesEntity record);

    int updateBatch(List<SpecialSalesEntity> list);

    int updateBatchSelective(List<SpecialSalesEntity> list);

    int batchInsert(@Param("list") List<SpecialSalesEntity> list);

    List<SpecialSalesDto> findByRelateIdInAndRelateTypeAndIsDelete(@Param("relateIdCollection") Collection<Integer> relateIdCollection, @Param("relateType") Integer relateType, @Param("isDelete") Integer isDelete);


    List<SpecialSalesEntity> selectByRelatedIdAndType(@Param("relateId") Integer quoteorderId, @Param("type")Integer type);
}