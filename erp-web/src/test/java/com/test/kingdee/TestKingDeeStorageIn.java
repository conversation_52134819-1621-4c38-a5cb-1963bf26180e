package com.test.kingdee;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.domain.command.KingDeeStorageInCommand;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeStorageInMapper;
import com.vedeng.erp.kingdee.mapstruct.KingDeeStorageInCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeStorageInConvertor;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * 其他入库
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring.xml", "classpath:spring-mybatis.xml"})
public class TestKingDeeStorageIn {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private KingDeeStorageInCommandConvertor commandConvertor;

    @Autowired
    private KingDeeStorageInConvertor kingDeeReceiveFeeConvertor;

    @Autowired
    private KingDeeStorageInMapper kingDeeStorageInMapper;


    @Test
    public void kingDeeStorageIn() {
        //一级dto
        KingDeeStorageInDto kingDeeStorageInDto = new KingDeeStorageInDto();
        kingDeeStorageInDto.setFId("0");
        kingDeeStorageInDto.setFBillTypeId("QTRKD01_SYS");
        kingDeeStorageInDto.setFBillNo("zxczxczczxczczcxzczxczc");
        kingDeeStorageInDto.setFQzokBddjtId("贝登erp对应的单据头ID");
        kingDeeStorageInDto.setFStockOrgId("101");
        kingDeeStorageInDto.setFStockDirect("GENERAL");
        kingDeeStorageInDto.setFDate("2022-01-17 00:00:00");
        kingDeeStorageInDto.setFSupplierId("VEN0006");
        kingDeeStorageInDto.setFDeptId("BM9999");

        List<KingDeeStorageInDetailDto> kingDeeStorageInDetailDtoList = new ArrayList<>();
        KingDeeStorageInDetailDto kingDeeStorageInDetailDto = new KingDeeStorageInDetailDto();
        kingDeeStorageInDetailDto.setFMaterialId("V500005");
        kingDeeStorageInDetailDto.setFStockId("CK9999");
        kingDeeStorageInDetailDto.setFQty("12.0");
        kingDeeStorageInDetailDto.setFPrice("10.0");
        kingDeeStorageInDetailDto.setFAmount("120.0");
        kingDeeStorageInDetailDto.setFQzokYsddh("原始订单号");
        kingDeeStorageInDetailDto.setFQzokGsywdh("归属业务单号");
        kingDeeStorageInDetailDto.setFQzokYwlx("业务类型");
        kingDeeStorageInDetailDto.setFQzokPch("批次号");
        kingDeeStorageInDetailDto.setFQzokXlh("序列号");
        kingDeeStorageInDetailDto.setFQzokSqlx("授权类型");
        kingDeeStorageInDetailDto.setFQzokSfzf("是否直发");

        kingDeeStorageInDetailDtoList.add(kingDeeStorageInDetailDto);
        kingDeeStorageInDto.setFEntity(kingDeeStorageInDetailDtoList);

        KingDeeStorageInCommand command = commandConvertor.toCommand(kingDeeStorageInDto);

        System.out.println("入参："+JSON.toJSONString(command));
        kingDeeBaseApi.save(new SaveExtCommand<>(command, kingDeeStorageInDto.getFormId()));

    }
}
