package com.vedeng.temporal.polling.universal.factory;

import com.vedeng.temporal.polling.universal.checker.CompletionChecker;
import com.vedeng.temporal.polling.universal.enums.CheckerType;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 完成条件检查器工厂类
 * 
 * 根据检查器类型和参数配置，创建对应的CompletionChecker实例。
 * 通过工厂模式统一管理所有检查器的创建逻辑，确保类型安全和确定性。
 * 
 * 设计特点：
 * - 静态工厂方法，无需实例化
 * - 类型安全，编译时检查
 * - 参数验证，运行时保障
 * - 易于扩展新的检查器类型
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-24
 */
@Slf4j
public class CompletionCheckerFactory {
    
    /**
     * 创建完成条件检查器
     * 
     * @param checkerType 检查器类型
     * @param params 检查器参数，可为null
     * @return CompletionChecker实例
     * @throws IllegalArgumentException 如果类型不支持或参数无效
     */
    public static CompletionChecker<Map<String, Object>> createChecker(CheckerType checkerType, 
                                                                      Map<String, Object> params) {
        if (checkerType == null) {
            throw new IllegalArgumentException("检查器类型不能为null");
        }
        
        log.debug("创建检查器，类型: {}, 参数: {}", checkerType, params);
        
        switch (checkerType) {
            case FIELD_EQUALS:
                return createFieldEqualsChecker(params);
                
            case FIELD_NOT_EMPTY:
                return createFieldNotEmptyChecker(params);
                
            case FIELD_NOT_NULL:
                return createFieldNotNullChecker(params);
                
            case FIELD_IN_VALUES:
                return createFieldInValuesChecker(params);
                
            case NESTED_FIELD_EQUALS:
                return createNestedFieldEqualsChecker(params);
                
            case FIELD_GREATER_THAN:
                return createFieldGreaterThanChecker(params);
                
            case SALES_ORDER_COMPLETE:
                return createSalesOrderCompleteChecker();
                
            case PURCHASE_ORDER_APPROVED:
                return createPurchaseOrderApprovedChecker();
                
            case ALL_CONDITIONS:
                return createAllConditionsChecker(params);
                
            case ANY_CONDITION:
                return createAnyConditionChecker(params);
                
            default:
                throw new IllegalArgumentException("不支持的检查器类型: " + checkerType);
        }
    }
    
    /**
     * 创建字段相等检查器
     */
    private static CompletionChecker<Map<String, Object>> createFieldEqualsChecker(Map<String, Object> params) {
        String fieldPath = getRequiredParam(params, "fieldPath", String.class);
        Object expectedValue = params.get("expectedValue"); // 可以为null
        
        return (data, context) -> {
            Object actualValue = getFieldValue(data, fieldPath);
            return Objects.equals(actualValue, expectedValue) ||
                   (actualValue != null && expectedValue != null && 
                    Objects.equals(actualValue.toString(), expectedValue.toString()));
        };
    }
    
    /**
     * 创建字段非空检查器
     */
    private static CompletionChecker<Map<String, Object>> createFieldNotEmptyChecker(Map<String, Object> params) {
        String fieldPath = getRequiredParam(params, "fieldPath", String.class);
        
        return (data, context) -> {
            Object value = getFieldValue(data, fieldPath);
            return value instanceof String && !((String) value).trim().isEmpty();
        };
    }
    
    /**
     * 创建字段非null检查器
     */
    private static CompletionChecker<Map<String, Object>> createFieldNotNullChecker(Map<String, Object> params) {
        String fieldPath = getRequiredParam(params, "fieldPath", String.class);
        
        return (data, context) -> getFieldValue(data, fieldPath) != null;
    }
    
    /**
     * 创建字段值在集合中检查器
     */
    @SuppressWarnings("unchecked")
    private static CompletionChecker<Map<String, Object>> createFieldInValuesChecker(Map<String, Object> params) {
        String fieldPath = getRequiredParam(params, "fieldPath", String.class);
        Object expectedValuesObj = params.get("expectedValues");
        
        if (!(expectedValuesObj instanceof Collection)) {
            throw new IllegalArgumentException("expectedValues必须是集合类型");
        }
        
        Collection<Object> expectedValues = (Collection<Object>) expectedValuesObj;
        
        return (data, context) -> {
            Object actualValue = getFieldValue(data, fieldPath);
            if (actualValue == null) return false;
            
            for (Object expectedValue : expectedValues) {
                if (Objects.equals(actualValue, expectedValue) ||
                    Objects.equals(actualValue.toString(), expectedValue.toString())) {
                    return true;
                }
            }
            return false;
        };
    }
    
    /**
     * 创建嵌套字段相等检查器
     */
    private static CompletionChecker<Map<String, Object>> createNestedFieldEqualsChecker(Map<String, Object> params) {
        String fieldPath = getRequiredParam(params, "fieldPath", String.class);
        Object expectedValue = params.get("expectedValue");
        
        return (data, context) -> {
            Object actualValue = getNestedFieldValue(data, fieldPath);
            return Objects.equals(actualValue, expectedValue) ||
                   (actualValue != null && expectedValue != null && 
                    Objects.equals(actualValue.toString(), expectedValue.toString()));
        };
    }
    
    /**
     * 创建数值大于检查器
     */
    private static CompletionChecker<Map<String, Object>> createFieldGreaterThanChecker(Map<String, Object> params) {
        String fieldPath = getRequiredParam(params, "fieldPath", String.class);
        Object expectedValueObj = getRequiredParam(params, "expectedValue", Object.class);
        
        if (!(expectedValueObj instanceof Number)) {
            throw new IllegalArgumentException("expectedValue必须是数值类型");
        }
        
        double expectedValue = ((Number) expectedValueObj).doubleValue();
        
        return (data, context) -> {
            Object actualValueObj = getFieldValue(data, fieldPath);
            if (!(actualValueObj instanceof Number)) return false;
            
            double actualValue = ((Number) actualValueObj).doubleValue();
            return actualValue > expectedValue;
        };
    }
    
    /**
     * 创建销售订单完成检查器
     */
    private static CompletionChecker<Map<String, Object>> createSalesOrderCompleteChecker() {
        return (data, context) -> {
            if (data == null) return false;
            String saleOrderNo = (String) data.get("saleOrderNo");
            return saleOrderNo != null && !saleOrderNo.trim().isEmpty();
        };
    }
    
    /**
     * 创建采购订单审核通过检查器
     */
    private static CompletionChecker<Map<String, Object>> createPurchaseOrderApprovedChecker() {
        return (data, context) -> {
            if (data == null) return false;
            
            // 检查API响应格式
            Object apiResponse = data.get("data");
            if (!(apiResponse instanceof Map)) return false;
            
            Map<String, Object> responseData = (Map<String, Object>) apiResponse;
            
            // 检查审核状态：validStatus = 1 表示审核通过
            Integer validStatus = (Integer) responseData.get("validStatus");
            return validStatus != null && validStatus == 1;
        };
    }
    
    /**
     * 创建所有条件检查器 (预留扩展)
     */
    private static CompletionChecker<Map<String, Object>> createAllConditionsChecker(Map<String, Object> params) {
        // TODO: 实现复合条件逻辑
        throw new UnsupportedOperationException("组合条件检查器暂未实现");
    }
    
    /**
     * 创建任一条件检查器 (预留扩展)
     */
    private static CompletionChecker<Map<String, Object>> createAnyConditionChecker(Map<String, Object> params) {
        // TODO: 实现复合条件逻辑
        throw new UnsupportedOperationException("组合条件检查器暂未实现");
    }
    
    // ========== 工具方法 ==========
    
    /**
     * 获取必需参数
     */
    @SuppressWarnings("unchecked")
    private static <T> T getRequiredParam(Map<String, Object> params, String paramName, Class<T> expectedType) {
        if (params == null) {
            throw new IllegalArgumentException("参数不能为null，缺少必需参数: " + paramName);
        }
        
        Object value = params.get(paramName);
        if (value == null) {
            throw new IllegalArgumentException("缺少必需参数: " + paramName);
        }
        
        if (!expectedType.isInstance(value)) {
            throw new IllegalArgumentException(String.format("参数类型错误: %s，期望: %s，实际: %s", 
                    paramName, expectedType.getSimpleName(), value.getClass().getSimpleName()));
        }
        
        return (T) value;
    }
    
    /**
     * 获取字段值（支持简单字段路径）
     */
    private static Object getFieldValue(Map<String, Object> data, String fieldPath) {
        if (data == null || fieldPath == null) {
            return null;
        }
        
        return data.get(fieldPath);
    }
    
    /**
     * 获取嵌套字段值（支持点分隔路径，如 "data.validStatus"）
     */
    @SuppressWarnings("unchecked")
    private static Object getNestedFieldValue(Map<String, Object> data, String fieldPath) {
        if (data == null || fieldPath == null) {
            return null;
        }
        
        String[] parts = fieldPath.split("\\.");
        Object current = data;
        
        for (String part : parts) {
            if (current instanceof Map) {
                current = ((Map<String, Object>) current).get(part);
            } else {
                return null;
            }
        }
        
        return current;
    }
}