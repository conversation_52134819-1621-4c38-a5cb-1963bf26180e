package com.vedeng.goods.service.impl;

import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.http.HttpRestClientUtil;
import com.vedeng.common.http.HttpURLConstant;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.goods.enums.OperateInfoSourceEnum;
import com.vedeng.goods.model.vo.CoreOperateInfoGenerateVo;
import com.vedeng.goods.model.vo.MlSkuRequestVo;
import com.vedeng.goods.model.vo.MlSkuResponseVo;
import com.vedeng.goods.service.MlFacadeService;
import com.vedeng.ml.api.dto.request.MlSkuRequestDto;
import com.vedeng.ml.api.dto.response.MlSkuResponseDto;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.noggit.JSONUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 马良图解装饰服务层
 *
 * <AUTHOR>
 */
@Service
public class MlFacadeServiceImpl implements MlFacadeService {

    private static final Logger logger = LoggerFactory.getLogger(MlFacadeServiceImpl.class);

    @Autowired
    private RestTemplate restTemplate;

   @Value("${ml.service.url}")
    private String mlUrl ;

    @Override
    public MlSkuResponseVo getSkuGraphicDetailInfo(MlSkuRequestVo mlSkuRequestVo) {
        try {
            ParameterizedTypeReference<RestfulResult<MlSkuResponseDto>> typeReference = new ParameterizedTypeReference<RestfulResult<MlSkuResponseDto>>() {
            };
            RestfulResult result= restTemplate.postForObject(getUrl(HttpURLConstant.SKU_GRAPHIC_DETAIL), convertRequestDto(mlSkuRequestVo),RestfulResult.class);
              if(result!=null&&result.isSuccess()){
                  return convertResponseVo(result);
              }
            logger.error("MlFacadeServiceImpl#getSkuGraphicDetailInfo error! param:{}"
                    , JsonUtils.convertObjectToJsonStr(mlSkuRequestVo)+ JsonUtils.convertObjectToJsonStr(result));
        } catch (Exception e) {
            logger.error("MlFacadeServiceImpl#getSkuGraphicDetailInfo error! param:{}"
                    , JsonUtils.convertObjectToJsonStr(mlSkuRequestVo), e);
        }
        return null;
    }

    @Override
    public void mlSkuGraphicDetailHandler(CoreOperateInfoGenerateVo infoGenerateVo) {
        OperateInfoSourceEnum sourceEnum = OperateInfoSourceEnum.getBySource(infoGenerateVo.getOperateInfoSource());
        infoGenerateVo.setMlDegreeOfCompletion("0%");
//        if (!OperateInfoSourceEnum.ML.equals(sourceEnum)) {
//            // SKU有ML来源，SPU没有，无需额外判断
//            return;
//        }

        if (Objects.isNull(infoGenerateVo.getSkuId())) {
            // 马良来源 且 没有skuId
//            logger.warn("MlFacadeServiceImpl#mlSkuGraphicDetailHandler error! skuId is null! param:{}",
//                    JsonUtils.convertObjectToJsonStr(infoGenerateVo));
            return;
        }
        // 获取马良图解详情数据
        MlSkuRequestVo mlSkuRequestVo = new MlSkuRequestVo();
        mlSkuRequestVo.setScoreFlag(Boolean.TRUE);
        mlSkuRequestVo.setSkuId(infoGenerateVo.getSkuId());
        MlSkuResponseVo mlSkuResponseVo = getSkuGraphicDetailInfo(mlSkuRequestVo);
        if (Objects.isNull(mlSkuResponseVo)) {
            return;
        }

        // 马良数据存在，组装数据

        infoGenerateVo.setMlDegreeOfCompletion(StringUtils.isBlank(mlSkuResponseVo.getDegreeOfCompletion())?"0%": mlSkuResponseVo.getDegreeOfCompletion() );
        infoGenerateVo.setMlSkuGraphicDetail(mlSkuResponseVo.getSkuGraphicDetail());
        infoGenerateVo.setMlSkuScore(mlSkuResponseVo.getSkuScore());
    }

    /**
     * 转换返回VO
     *
     * @param result
     * @return
     */
    private MlSkuResponseVo convertResponseVo(RestfulResult<MlSkuResponseDto> result) {
        if (Objects.isNull(result) || !result.isSuccess() || Objects.isNull(result.getData())) {
            // 请求错误
            logger.error("MlFacadeServiceImpl#convertResponseVo ml result error! result:{}"
                    , JsonUtils.convertObjectToJsonStr(result));
            return null;
        }
        MlSkuResponseVo responseVo = new MlSkuResponseVo();
        try {
            Map<String,Object> map=(Map<String,Object>)result.getData();
            responseVo.setSkuId(NumberUtils.toInt(map.get("skuId")+""));
            responseVo.setSkuGraphicDetail( map.get("skuGraphicDetail") ==null?"":map.get("skuGraphicDetail")+"");
            responseVo.setDegreeOfCompletion(map.get("degreeOfCompletion")==null?"":map.get("degreeOfCompletion")+"");
        } catch ( Exception e) {
            logger.error("MlFacadeServiceImpl#convertResponseVo copyProperties error! result:{}"
                    , JsonUtils.convertObjectToJsonStr(responseVo), e);
        }
        return responseVo;
    }

    /**
     * 转换请求DTO
     *
     * @param mlSkuRequestVo
     * @return
     */
    private MlSkuRequestDto convertRequestDto(MlSkuRequestVo mlSkuRequestVo) {
        MlSkuRequestDto mlSkuRequestDto = new MlSkuRequestDto();
        try {
            BeanUtils.copyProperties(mlSkuRequestDto,mlSkuRequestVo );
        } catch (IllegalAccessException | InvocationTargetException e) {
            logger.error("MlFacadeServiceImpl#convertRequestDto copyProperties error! param:{}"
                    , JsonUtils.convertObjectToJsonStr(mlSkuRequestVo), e);
        }
        return mlSkuRequestDto;
    }

    /**
     * 获取请求地址
     *
     * @param apiUrl
     * @return
     */
    private String getUrl(String apiUrl) {
        return mlUrl + apiUrl;
    }
}
