<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="账期详情" scope="application" />
<%@ include file="../../common/common.jsp"%>
<div class="formpublic">
    <div>唯一标识是根据账期正式触发时生成的编码，点击后可查占用了账期额度的本批产品明细</div>
    <table class="table table-bordered table-striped table-condensed table-centered">
        <thead>
        <tr>
            <th class="wid10">账期编码</th>
            <th class="wid10">结算标准</th>
            <th class="wid10">生成方式</th>
            <th class="wid10">物流日志/发票票号</th>
            <th class="wid10">关联单据</th>
            <th class="wid10">账期计算开始时间</th>
            <th class="wid10">结算周期（天）</th>
            <th class="wid10">产品发货/开票总金额</th>
            <th class="wid10">账期发生额</th>
            <th class="wid10">逾期状态</th>
        </tr>
        </thead>
        <tbody>
        <c:forEach var="list" items="${customerBillPeriodDetail}" varStatus="num">
            <c:choose>
                <c:when test="${not empty list.billPeriodOverdueManagementCode && list.isShow eq 1}">
                    <tr>
                        <td>
                                ${list.billPeriodOverdueManagementCode}
                        </td>
                        <td>
                            <c:choose>
                                <c:when test="${ list.settlementType == 1}">
                                    产品发货
                                </c:when>
                                <c:otherwise>
                                    产品开票
                                </c:otherwise>
                            </c:choose>
                        </td>
                        <td>
                            <c:choose>
                                <c:when test="${list.type == 1}">订单发货</c:when>
                                <c:when test="${list.type == 2}">订单开票</c:when>
                                <c:when test="${list.type == 3}">售后退货</c:when>
                                <c:when test="${list.type == 4}">售后退票</c:when>
                                <c:when test="${list.type == 5}">订单结款</c:when>
                            </c:choose>
                        </td>
                        <td>
                            <c:choose>
                                <c:when test="${not empty list.deliveryDirect && list.deliveryDirect == 1}">直发</c:when>
                                <c:otherwise>${list.relatedInfo}</c:otherwise>
                            </c:choose>
                        </td>
                        <td>
                             ${list.orderRelatedNo}
                            <%--<c:choose>
                                <c:when test="${list.type == 1}">订单发货单号</c:when>
                                <c:when test="${list.type == 2}">订单开票单号</c:when>
                                <c:when test="${list.type == 3}">售后单号${list.orderRelatedNo}</c:when>
                                <c:when test="${list.type == 4}">售后单号${list.orderRelatedNo}</c:when>
                                <c:when test="${list.type == 5}">财务流水号${list.orderRelatedNo}</c:when>
                            </c:choose>--%>
                        </td>
                        <td>
                            <date:date value="${list.addTime}" format="yyyy-MM-dd" />
                        </td>
                        <td>
                                ${list.settlementPeriod}
                        </td>
                        <td>
                                ${list.productAmount}
                        </td>
                        <td>
                                ${list.amount}
                        </td>
                        <td>
                            <c:choose>
                                <c:when test="${not empty list.overdueDays && list.overdueDays>0}">逾期</c:when>
                                <c:otherwise>
                                    未逾期
                                </c:otherwise>
                            </c:choose>
                        </td>
                    </tr>
                </c:when>
            </c:choose>
        </c:forEach>
        <c:if test="${empty customerBillPeriodDetail}">
            <tr>
                <td colspan="10">
                    <!-- 查询无结果弹出 --> 查询无结果！
                </td>
            </tr>
        </c:if>
        </tbody>
    </table>
</div>