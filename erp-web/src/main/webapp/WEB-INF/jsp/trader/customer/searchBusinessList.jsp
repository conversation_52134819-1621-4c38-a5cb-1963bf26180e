
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="终端列表" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='${pageContext.request.contextPath}/static/js/customer/bussinesss.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript">
    $(function(){
        var saleOrderGoodsIds = $("#saleOrderGoodsIds",window.parent.document).val();
        if (saleOrderGoodsIds !== ""){
            saleOrderGoodsIds = saleOrderGoodsIds.split(',');
            $('tbody tr').each(function(i){
                var tdArr = $(this).children();
                var saleOrderGoodsId = tdArr.eq(0).find("input").attr('saleOrderGoodsId');
                if (saleOrderGoodsIds.indexOf(saleOrderGoodsId) > -1){
                    tdArr.eq(0).find("input").attr('checked', "true")
                }
            })
        }
    })
</script>
<div class="fixdiv">
    <div class="superdiv">
        <table
                class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th class="wid10">选择</th>
                <th class="wid10">时间</th>
                <th class="wid10">单号</th>
                <th class="wid20">产品名称</th>
                <th class="wid8">订货号</th>
                <th class="wid8">品牌</th>
                <th class="wid8">型号</th>
                <th class="wid8">单价</th>
                <th class="wid8">数量</th>
                <th class="wid8">单位</th>
                <th class="wid12">总额</th>
                <th class="wid12">终端客户</th>
                <th class="wid12">销售区域</th>
            </tr>
            </thead>
            <tbody>
            <c:forEach var="saleorderGoods" items="${businessList}" varStatus="num">
                <tr>
                    <td>
                        <input type="checkbox" name="saleOrderId"  onclick="goodsCheckClick(this);" value="" class="" autocomplete="off" saleOrderId="${saleorderGoods.saleorderId}" saleOrderGoodsId="${saleorderGoods.saleorderGoodsId}"/>
                            <%--									<input value="${saleorderGoods.saleorderGoodsId}" name="saleorderGoodsId" style="display: none">--%>
                    </td>
                    <td><date:date value ="${saleorderGoods.validTime}"/></td>
                    <td>
                        <a class="addtitle1" href="javascript:void(0);" tabTitle='{"num":"viewsaleorder${saleorderGoods.saleorderId}","link":"./order/saleorder/view.do?saleorderId=${saleorderGoods.saleorderId}","title":"订单信息"}'>${saleorderGoods.saleorderNo}</a>
                    </td>
                    <td class="text-left">${newSkuInfosMap[saleorderGoods.sku].SHOW_NAME}</td>
                    <td>
                        <a class="addtitle1" href="javascript:void(0);" tabTitle='{"num":"viewgoods${saleorderGoods.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${saleorderGoods.goodsId}","title":"产品信息"}'>${saleorderGoods.sku}</a>
                    </td>
                    <td>${newSkuInfosMap[saleorderGoods.sku].BRAND_NAME}</td>
                    <td>${newSkuInfosMap[saleorderGoods.sku].MODEL}</td>
                    <td>${saleorderGoods.price}</td>
                    <td>${saleorderGoods.num}</td>
                    <td>${newSkuInfosMap[saleorderGoods.sku].UNIT_NAME}</td>
                    <td><fmt:formatNumber type="number" value="${saleorderGoods.price * saleorderGoods.num}" pattern="0.00" maxFractionDigits="2" /></td>
                    <td>${saleorderGoods.terminalTraderName}</td>
                    <td>${saleorderGoods.salesArea}</td>
                </tr>
            </c:forEach>
            </tbody>
        </table>
        <c:if test="${empty businessList}">
            <!-- 查询无结果弹出 -->
            <div class="noresult">查询无结果！请尝试使用其他搜索条件。</div>
        </c:if>
    </div>
</div>
<tags:page page="${page}"/>

</body>
</html>
