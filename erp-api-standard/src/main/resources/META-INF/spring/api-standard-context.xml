<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="
           http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans-4.1.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context-4.1.xsd">

    <!--
        ERP API标准化模块Spring配置文件

        职责：
        - 管理 erp-api-standard 模块的所有组件
        - 提供模块化、可重用的配置
        - 避免与主项目配置冲突

        使用方式：
        在主项目的 spring.xml 中导入：
        <import resource="classpath:META-INF/spring/api-standard-context.xml"/>

        并在主项目的组件扫描中排除此包：
        <context:component-scan base-package="com.vedeng.api">
            <context:exclude-filter type="regex" expression="com\.vedeng\.api\.standard\..*"/>
        </context:component-scan>

        兼容Spring 4.1.9版本
    -->

    <!-- 启用注解驱动 -->
    <context:annotation-config />

    <!--
        精确扫描API标准化模块的组件
        只扫描 com.vedeng.api.standard 包，避免与其他包冲突
    -->
    <context:component-scan base-package="com.vedeng.api.standard">
        <!-- 包含所有业务组件 -->
        <context:include-filter type="annotation" expression="org.springframework.stereotype.Component"/>
        <context:include-filter type="annotation" expression="org.springframework.stereotype.Service"/>

        <!-- Web层组件由主项目的 spring-mvc.xml 管理 -->
        <context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
        <context:exclude-filter type="annotation" expression="org.springframework.web.bind.annotation.RestController"/>
    </context:component-scan>

    <!--
        API标准化模块的Mapper接口扫描配置
        注意：映射文件的加载由主项目的SqlSessionFactory负责
        这里只负责扫描Mapper接口
    -->
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.vedeng.api.standard.**.mapper"/>
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"/>
    </bean>

</beans>
