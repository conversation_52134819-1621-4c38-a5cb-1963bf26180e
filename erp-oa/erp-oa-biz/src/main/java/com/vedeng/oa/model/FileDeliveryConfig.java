package com.vedeng.oa.model;

import java.util.Date;

public class FileDeliveryConfig {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_CONFIG.ID
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_CONFIG.IS_DELETE
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    private Integer isDelete;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_CONFIG.ADD_TIME
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    private Date addTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_CONFIG.MOD_TIME
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    private Date modTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_CONFIG.CREATOR
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    private Integer creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_CONFIG.UPDATER
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    private Integer updater;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_CONFIG.UPDATE_REMARK
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    private String updateRemark;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_CONFIG.DELI_CONFIG
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    private String deliConfig;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_CONFIG.ID
     *
     * @return the value of T_FILE_DELIVERY_CONFIG.ID
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_CONFIG.ID
     *
     * @param id the value for T_FILE_DELIVERY_CONFIG.ID
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_CONFIG.IS_DELETE
     *
     * @return the value of T_FILE_DELIVERY_CONFIG.IS_DELETE
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    public Integer getIsDelete() {
        return isDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_CONFIG.IS_DELETE
     *
     * @param isDelete the value for T_FILE_DELIVERY_CONFIG.IS_DELETE
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_CONFIG.ADD_TIME
     *
     * @return the value of T_FILE_DELIVERY_CONFIG.ADD_TIME
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    public Date getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_CONFIG.ADD_TIME
     *
     * @param addTime the value for T_FILE_DELIVERY_CONFIG.ADD_TIME
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_CONFIG.MOD_TIME
     *
     * @return the value of T_FILE_DELIVERY_CONFIG.MOD_TIME
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    public Date getModTime() {
        return modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_CONFIG.MOD_TIME
     *
     * @param modTime the value for T_FILE_DELIVERY_CONFIG.MOD_TIME
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    public void setModTime(Date modTime) {
        this.modTime = modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_CONFIG.CREATOR
     *
     * @return the value of T_FILE_DELIVERY_CONFIG.CREATOR
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_CONFIG.CREATOR
     *
     * @param creator the value for T_FILE_DELIVERY_CONFIG.CREATOR
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_CONFIG.UPDATER
     *
     * @return the value of T_FILE_DELIVERY_CONFIG.UPDATER
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    public Integer getUpdater() {
        return updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_CONFIG.UPDATER
     *
     * @param updater the value for T_FILE_DELIVERY_CONFIG.UPDATER
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_CONFIG.UPDATE_REMARK
     *
     * @return the value of T_FILE_DELIVERY_CONFIG.UPDATE_REMARK
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    public String getUpdateRemark() {
        return updateRemark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_CONFIG.UPDATE_REMARK
     *
     * @param updateRemark the value for T_FILE_DELIVERY_CONFIG.UPDATE_REMARK
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    public void setUpdateRemark(String updateRemark) {
        this.updateRemark = updateRemark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_CONFIG.DELI_CONFIG
     *
     * @return the value of T_FILE_DELIVERY_CONFIG.DELI_CONFIG
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    public String getDeliConfig() {
        return deliConfig;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_CONFIG.DELI_CONFIG
     *
     * @param deliConfig the value for T_FILE_DELIVERY_CONFIG.DELI_CONFIG
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    public void setDeliConfig(String deliConfig) {
        this.deliConfig = deliConfig;
    }
}