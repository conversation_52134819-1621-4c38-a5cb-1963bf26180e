package com.newtask.quoteorder.strategy;

import com.newtask.quoteorder.exception.QuotedAlarmUpgradedException;
import com.newtask.quoteorder.model.QuotedAlarmRecord;
import com.vedeng.order.model.Quoteorder;

/**
 * <AUTHOR> [<EMAIL>]
 */
public interface QuotedAlarmStrategy {

    /**
     * 升级为下一级别预警时间信息
     *
     * @param quotedAlarmRecord 报价预警记录信息
     * @param purchaseTimeType 采购时间类型
     * @throws QuotedAlarmUpgradedException 预警超出最大级别时触发
     */
    void upgradeForNextLevel(QuotedAlarmRecord quotedAlarmRecord, Integer purchaseTimeType);

    /**
     * 升级为下一级别预警时间信息
     *
     * @param quotedAlarmRecord 报价预警记录信息
     * @throws QuotedAlarmUpgradedException 预警超出最大级别时触发
     */
    void upgradeForNextLevel(QuotedAlarmRecord quotedAlarmRecord);
}
