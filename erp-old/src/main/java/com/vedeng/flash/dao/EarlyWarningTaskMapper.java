package com.vedeng.flash.dao;

import com.vedeng.flash.dto.EarlyWarningTaskDto;
import com.vedeng.flash.dto.EarlyTaskGoodsQueryDto;
import com.vedeng.flash.dto.PrepareStockDto;
import com.vedeng.flash.dto.*;
import com.vedeng.flash.model.EarlyWarningTask;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.todolist.dto.EarlyWarningTicksTaskTodoDto;
import com.vedeng.todolist.dto.PrepareStockTaskToDoDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface EarlyWarningTaskMapper {

    int deleteByPrimaryKey(Long earlyWarningTaskId);

    int insert(EarlyWarningTask record);

    int insertSelective(EarlyWarningTask record);

    EarlyWarningTask selectByPrimaryKey(Long earlyWarningTaskId);

    int updateByPrimaryKeySelective(EarlyWarningTask record);

    int updateByPrimaryKey(EarlyWarningTask record);

    /**
     * @Description 获取员工视角的供应链工作台催票数据
     * <AUTHOR>
     * @Date 17:44 2021/5/19
     * @Param
     * @return
     **/
    EarlyWarningTicksTaskTodoDto getEarlyWarningTicksTaskTodoDtoByuserIds(@Param(value = "userIds")List<Integer> userIds);

    List<EarlyWarningTicksDto> getEarlyWarningTicksDtoByUserIdsListPage(Map<String, Object> map);

    List<EarlyWarningTicksDto> getTicksFollowUpRecoredByUserIdListPage(Map<String, Object> map);

    List<EarlyWarningTicksDto> getEarlyWarningTicksDtoByIds(@Param(value = "earlyWarningTaskIdsList")List<Integer> earlyWarningTaskIdsList);
    int getFollowNumByPrimaryKey(Long earlyWarningTaskId);

    List<TaskDealerDto> getAllDealerByUserIds(@Param(value = "userIds")List<Integer> userIds);

    List<EarlyWarningTask> getEarlyWarningTicketTaskByBuyorderGoodId(@Param(value = "buyorderGoodsId")Integer buyorderGoodsId);

    List<EarlyWarningTask> getAllNotDeleteAndNotSendOver48HoursMessageTask();
    /**
     * 获得催货任务状态信息
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/5/21 16:04.
     * @author: Randy.Xu.
     * @param buyorderGoodsVo
     * @return: com.vedeng.flash.dto.EarlyWarningTaskDto.
     * @throws:  .
     */
    EarlyWarningTaskDto getExpeditionTaskInfoByBuyorderGoodsId(BuyorderGoodsVo buyorderGoodsVo);

    /**
     * 获得催货任务的跟进信息详情
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/5/21 17:36.
     * @author: Randy.Xu.
     * @param earlyTaskGoodsQueryDto
     * @return: com.vedeng.flash.dto.EarlyWarningTaskDto.
     * @throws:  .
     */
    EarlyWarningTaskDto getFollowInfoDetailById(EarlyTaskGoodsQueryDto earlyTaskGoodsQueryDto);

    /**
     * 获取指定用户组催货任务List
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/5/22 15:42.
     * @author: Randy.Xu.
     * @param userIdList
     * @return: java.util.List<com.vedeng.flash.model.EarlyWarningTask>.
     * @throws:  .
     */
    List<EarlyWarningTask> getEarlyWarningGoodsTaskListByuserIds(@Param("userIdList") List<Integer> userIdList);

    /**
     * 根据备货计划查询备货任务
     * @param prepareStockDto 备货计划
     * @return  备货任务
     */
    EarlyWarningTaskDto getPrepareStockTask(PrepareStockDto prepareStockDto);

    /**
     * 根据用户Id查询备货任务
     * @param userIdList 用户Id
     * @return  备货任务
     */
    PrepareStockTaskToDoDto getPrepareStockTaskByUserId(List<Integer> userIdList);

    /**
     * 批量新增
     * @param earlyWarningTaskList
     */
    void insertBatch(List<EarlyWarningTaskDto> earlyWarningTaskList);

    /**
     * 批量更新
     * @param earlyWarningTaskList
     */
    void updateBatch(List<EarlyWarningTaskDto> earlyWarningTaskList);

    List<EarlyWarningTask> getAllExpedingTask();

    void updateDelteStatusById(EarlyWarningTask earlyWarningTask);

    EarlyWarningTask getFollowInfoDetail(EarlyWarningTask earlyWarningTask);

    /**
     * 删除备货任务
     * @param stockDto
     */
    void deletePrepareStockBySkuId(PrepareStockDto stockDto);

    List<EarlyWarningTask> getExistEarlyWarningGoodsTaskListByBuyorderGoodsIdList(@Param("buyorderGoodsIdList") List<Integer> buyorderGoodsIdList);

    /**
     * 批量关闭订单预警任务
     *
     * @param orderGoodsIdList
     */
    void batchCloseOrderGoodsTask(@Param("orderGoodsIdList") List<Integer> orderGoodsIdList);

    int closeEarlyTicketTaskList(@Param("earlyWarningTicketTaskList") List<EarlyWarningTask> earlyWarningTicketTaskList);

    /**
     * 批量关闭催票任务
     *
     * @param orderNo
     */
    void batchCloseUrgeTicketByOrderNo(@Param("orderNo") String orderNo);
}
