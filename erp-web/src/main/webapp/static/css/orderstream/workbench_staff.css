html,body {
  color: #333;
  font: 14px/1.5 Microsoft YaHei, arial, sans-serif;
  background-color: #f5f7fa;
}
.wrap {
  padding: 20px 40px;
  display: flex;
  min-width: 1280px;
}
.wrap .main-l {
  flex: 1;
}
.wrap .main-r {
  width: 360px;
  min-width: 360px;
  margin-left: 20px;
}
.wrap .block {
  background: #fff;
  position: relative;
  margin-bottom: 15px;
}
.wrap .block .title {
  font-size: 16px;
  font-weight: bold;
  padding: 15px 20px;
  border-bottom: 1px solid #ebebeb;
}
.wrap .block .title .icon-problem1{
  font-size: 20px;
  font-weight: normal;
  cursor: pointer;
  margin-left: 10px;
  vertical-align: -3px;
}
.wrap .block .title .icon-problem1:hover {
  color: #f60;
}
.wrap .block .title-link {
  position: absolute;
  line-height: 24px;
  right: 20px;
  top: 15px;
}
.wrap .block .title-link .icon-right {
  margin-left: 3px;
  vertical-align: -2px;
  font-size: 16px;
}
.wrap .block .title-link:hover {
  color: #f60;
}
.wrap .block .biz-cnt {
  padding: 20px;
}
.wrap .block .biz-cnt .biz-title {
  background: #f2f2f2;
  padding: 10px;
  color: #18C3B1;
}
.wrap .block .biz-cnt .biz-title i {
  background: none;
}
.wrap .block .biz-cnt .biz-list {
  display: flex;
  border: 1px solid #e6e6e6;
  padding: 10px;
}
.wrap .block .biz-cnt .biz-list .biz-item {
  background: #f2f2f2;
  flex: 1;
  border-radius: 6px;
  margin-right: 10px;
  text-align: center;
}
.wrap .block .biz-cnt .biz-list .biz-item .item-num {
  font-size: 16px;
  height: 50px;
  line-height: 50px;
  color: #18C3B1;
}
.wrap .block .biz-cnt .biz-list .biz-item .item-num.strong {
  font-size: 32px;
}
.wrap .block .biz-cnt .biz-list .biz-item .item-num.red {
  color: #e64545;
}
.wrap .block .biz-cnt .biz-list .biz-item .item-txt {
  color: #666;
  padding: 10px 0;
  font-size: 12px;
  border-top: 1px solid #e6e6e6;
}
.wrap .block .biz-cnt .biz-list .biz-item:last-child {
  margin-right: 0;
}
.wrap .block .work-list {
  display: flex;
}
.wrap .block .work-list .work-item {
  flex: 1;
  padding: 20px 15px 40px 15px;
  position: relative;
  display: flex;
  border-right: 1px solid #ebebeb;
}

.wrap .block .work-list .work-item .item-b{
  position: absolute;
  display: flex;
  align-items: center;
  bottom: 10px;
  left: 0;
  width: 100%;
  padding: 0 15px;
  font-size: 12px;
}
.wrap .block .work-list .work-item .item-a{
  position: absolute;
  /* display: flex; */
  align-items: center;
  /* bottom: 10px; */
  rgiht: 0;
  width: 100%;
  padding: 0 15px;
  font-size: 12px;
  /* float: right; */
  right: 0px;
  top: 10px;
}
.wrap .block .work-list .work-item .item-a .item-option{
  color: #09f;
  float:right;
  cursor: pointer;
}
.wrap .block .work-list .work-item .item-b .item-txt{
  flex: 1;
  color: #999;
}
.wrap .block .work-list .work-item .item-b .item-option{
   color: #09f;
  cursor: help;
 }

.wrap .block .work-list .work-item:last-child {
  border-right: 0;
}
.wrap .block .work-list .work-item .item-icon {
  width: 40px;
  height: 40px;
  background-size: 100% 100%;
  margin-right: 10px;
}
.wrap .block .work-list .work-item .item-icon.icon-1 {
  background-image: url(../../images/workbrench_staff/biz-icon-1.png);
}
.wrap .block .work-list .work-item .item-icon.icon-2 {
  background-image: url(../../images/workbrench_staff/biz-icon-2.png);
}
.wrap .block .work-list .work-item .item-icon.icon-3 {
  background-image: url(../../images/workbrench_staff/biz-icon-3.png);
}
.wrap .block .work-list .work-item .item-icon.icon-4 {
  background-image: url(../../images/workbrench_staff/biz-icon-4.png);
}
.wrap .block .work-list .work-item .item-icon.icon-5 {
  background-image: url(../../images/workbrench_staff/biz-icon-5.png);
}
.wrap .block .work-list .work-item .item-label {
  font-size: 16px;
  margin-bottom: 5px;
  white-space: nowrap;
}
.wrap .block .work-list .work-item .item-num {
  font-weight: bold;
  font-size: 16px;
}
.wrap .block .todo-list {
  display: flex;
  flex-wrap: wrap;
}
.wrap .block .todo-list .todo-block {
  width: 33.33%;
  padding: 20px;
  border-right: 1px solid #ebebeb;
  border-bottom: 1px solid #ebebeb;
}
.wrap .block .todo-list .todo-block.flex-2 {
  width: 66.66%;
}
.wrap .block .todo-list .todo-block .todo-block-title {
  font-weight: 650;
  margin-bottom: 15px;
}
.wrap .block .todo-list .todo-block .todo-block-title .title-icon {
  width: 24px;
  height: 24px;
  background-size: 100% 100%;
  display: inline-block;
  margin-right: 5px;
  vertical-align: top;
}
.wrap .block .todo-list .todo-block .todo-block-title .title-icon.icon-1 {
  background-image: url(../../images/workbrench_staff/todo-icon-1.png);
}
.wrap .block .todo-list .todo-block .todo-block-title .title-icon.icon-2 {
  background-image: url(../../images/workbrench_staff/todo-icon-2.png);
}
.wrap .block .todo-list .todo-block .todo-block-title .title-icon.icon-3 {
  background-image: url(../../images/workbrench_staff/todo-icon-3.png);
}
.wrap .block .todo-list .todo-block .todo-block-list {
  display: flex;
  justify-content: center;
  text-align: center;
}
.wrap .block .todo-list .todo-block .todo-block-list .todo-item {
  padding: 0 5%;
}
.wrap .block .todo-list .todo-block .todo-block-list .todo-item .item-num {
  font-size: 20px;
  display: inline-block;
  margin-bottom: 10px;
}
.wrap .block .todo-list .todo-block .todo-block-list .todo-item .item-num[href]:hover {
  color: #f60;
}
.wrap .block .todo-list .todo-block .todo-block-list .todo-item .item-label {
  white-space: nowrap;
}
.wrap .block .msg-list {
  display: flex;
}
.wrap .block .msg-list .msg-item {
  flex: 1;
  text-align: center;
  padding: 20px 10px;
}
.wrap .block .msg-list .msg-item .item-label {
  margin-bottom: 10px;
}
.wrap .block .msg-list .msg-item .item-num {
  color: #e64545;
  font-weight: bold;
  font-size: 18px;
}
.wrap .block .quick-list {
  padding: 20px 0 10px 10px;
}
.wrap .block .quick-list a {
  margin-right: 10px;
  display: inline-block;
  margin-bottom: 10px;
}
.wrap .block .quick-list a:hover {
  color: #f60;
}
 @media screen and (max-width: 1920px) {
   .wrap .block .todo-list .todo-block .todo-block-list .todo-item {
     padding: 0 2%;
   }
 }

 .title-option-wrap {
  position: absolute;
  right: 20px;
  top: 0;
  display: flex;
  align-items: center;
  height: 55px
 }

 .title-option-wrap .option-item {
  margin-left: 10px;
 }

 .contact-sum-list {
  display: flex;
  align-items: center;
 }

 .contact-sum-list .contact-sum-item {
  flex: 1;
  text-align: center;
  padding: 20px 0;
 }

 .contact-sum-list .contact-sum-item .item-num {
  font-size: 18px;
  margin-bottom: 10px;
 }

 .contact-sum-list .contact-sum-item .item-txt {
  font-size: 12px;
 }

 .analysis-wrap {
  display: flex;
  padding: 20px 0;
 }

 .analysis-wrap .analysis-l {
  border-right: solid 1px #EBEBEB;
 }

 .analysis-wrap .analysis-r {
  padding: 0 20px;
  position: relative;
 }

 .analysis-wrap .analysis-r .chart-placeholder {
  width: 260px;
  height: 315px;
  margin: 0 auto;
  pointer-events: none;
  z-index: 2;
 }

 .analysis-wrap .analysis-l, .analysis-wrap .analysis-r {
  flex: 1;
 }

 .analysis-txt-list {
  position: absolute;
  width: calc(100% - 40px);
  top: 0;
  left: 20px;
  font-size: 12px;
 }

 .analysis-txt-list .analysis-txt-item{
  height: 45px;
  display: flex;
  justify-content: space-between;
 }

 .analysis-txt-list .analysis-txt-item .item-l{
  padding-left: 10px;
  padding-top: 5px;
 }

 .analysis-txt-list .analysis-txt-item .item-l .item-label {
  color: #999;
 }

 .analysis-txt-list .analysis-txt-item .item-l .item-num {
  font-weight: 700;
 }

 .analysis-txt-list .analysis-txt-item.show.bg {
  background: #FAFBFC;
 }
 
 .analysis-txt-list .analysis-txt-item .item-r {
  position: relative;
  width: 80px;
  text-align: right;
  padding-top: 5px;
 }
 
 /* .analysis-txt-list .analysis-txt-item .item-r::before {
    content: '';
    width: 31px;
    height: 19px;
    position: absolute;
    left: -37px;
    top: 9px;
    background-image: url(../../images/loudou-charts.png);
    background-size: 100% 100%;
 } */
 
 .analysis-txt-list .analysis-txt-item .item-r .current-num {
  font-weight: 700;
 }
 
 .analysis-txt-list .analysis-txt-item .item-r .current-num.green {
  color: #13BF13;
 }
 
 .analysis-txt-list .analysis-txt-item .item-r .current-num.orange {
  color: #FF6600;
 }
 
 .analysis-txt-list .analysis-txt-item .item-r .avg-num {
  color: #999;
 }

 .circle-wrap {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
  position: relative;
 }

 .circle-wrap .rate-wrap {
  position: absolute;
  text-align: center;
  top: 47px;
 }

 .circle-wrap .rate-wrap .rate-num {
  font-size: 18px;
 }

 .circle-wrap .rate-wrap .rate-txt {
  font-size: 12px;
 }

 .analysis-num-list {
  display: flex;
  flex-wrap: wrap;
  padding: 0 20px;
  margin-bottom: -20px;
 }

 .analysis-num-list .analysis-num-item{
  width: 33.33%;
  text-align: center;
  margin-bottom: 20px;
 }

 .analysis-num-list .analysis-num-item .item-num{
  font-size: 18px;
 }

 .analysis-num-list .analysis-num-item .item-num.blue{
  color: #09f;
 }

 .analysis-num-list .analysis-num-item .item-txt{
  font-size: 12px;
 }

 .circle {
  -webkit-mask: radial-gradient(transparent 60px, #000 60px);
  width: 140px;
  height: 140px;
  overflow: hidden;
  border-radius: 50%;
  position: relative;
}

.circle-left {
  width: 50%;
  height: 100%;
  background: #ebeff2;
  transform-origin: 100% 50%;
  position: absolute;
  left: 0;
  z-index: 0;
}

.circle-right {
  width: 50%;
  height: 100%;
  background: #ebeff2;
  transition: transform 1s linear;
  transform-origin: 0% 50%;
  position: absolute;
  right: 0;
  z-index: 2;
}

.circle-bottom-left {
  width: 50%;
  height: 100%;
  background: #09f;
  position: absolute;
  left: 0;
  z-index: -1;
}

.circle-bottom-right {
  width: 50%;
  height: 100%;
  background: #09f;
  position: absolute;
  right: 0;
  z-index: 1;
}

.analysis-chart-empty {
  text-align: center;
  padding-top: 30px;
}

.analysis-chart-empty .analysis-empty-img {
  width: 160px;
  height: 160px;
  background-image: url(../../images/chart-empty.svg);
  background-size: 100% 100%;
  margin: 0 auto;
}

.help-dlg-wrap {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, .6);
  z-index: 9999;
  display: none;
}

.help-dlg-wrap .help-dlg-container {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-dlg-wrap .help-dlg-main {
  width: 800px;
  background: #fff;
  overflow: hidden;
  border-radius: 3px;
}

.help-dlg-wrap .help-dlg-main .help-dlg-header {
  background: #F5F7FA;
  padding: 10px 20px;
  position: relative;
  border-bottom: solid 1px #E1E5E8;
}

.help-dlg-wrap .help-dlg-main .help-dlg-title {
  font-size: 16px;
}

.help-dlg-wrap .help-dlg-main .help-dlg-header .icon-delete {
  position: absolute;
  width: 44px;
  height: 44px;
  top: 0;
  right: 0;
  color: #666;
  cursor: pointer;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-dlg-wrap .help-dlg-main .help-dlg-header .icon-delete:hover {
  color: #333;
}

.help-dlg-wrap .help-dlg-main .help-dlg-body {
  padding: 20px 2px 20px 20px;
}

.help-dlg-wrap .help-dlg-main .help-dlg-cnt {
  max-height: 600px;
  overflow: auto;
  padding-right: 18px;
  overscroll-behavior: contain;
}

.help-dlg-wrap .help-dlg-main .help-dlg-cnt::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.help-dlg-wrap .help-dlg-main .help-dlg-cnt::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.help-dlg-wrap .help-dlg-main .help-dlg-cnt::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.help-dlg-wrap .help-dlg-main .help-dlg-cnt::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.help-dlg-wrap .help-dlg-main .help-dlg-cnt::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}

.help-dlg-wrap .help-dlg-main .help-dlg-block {
  margin-bottom: 20px;
}

.help-dlg-wrap .help-dlg-main .help-dlg-block:last-child {
  margin-bottom: 0;
}

.help-dlg-wrap .help-dlg-main .help-dlg-block-title {
  font-weight: 700;
  margin-bottom: 10px;
}

.help-dlg-wrap .help-dlg-main .help-list-wrap {
  border: solid 1px #E1E5E8;
  padding: 30px;
}

.help-dlg-wrap .help-dlg-main .help-list {
  font-size: 12px;
  margin-bottom: -10px;
}

.help-dlg-wrap .help-dlg-main .help-list.line-2 {
  display: flex;
  flex-wrap: wrap;
}

.help-dlg-wrap .help-dlg-main .help-list .help-item {
  display: flex;
  margin-bottom: 10px;
}

.help-dlg-wrap .help-dlg-main .help-list.line-2 .help-item {
  width: 50%;
}

.help-dlg-wrap .help-dlg-main .help-list .help-label {
  width: 108px;
  color: #999;
  margin-right: 10px;
}

.help-dlg-wrap .help-dlg-main .help-list .help-txt {
  flex: 1;
}

.block-analysis .analysis-loading {
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, .5);
  display: none;
  align-items: center;
  justify-content: center;
  top: 0;
  left: 0;
  z-index: 20;
}

@keyframes loading {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.block-analysis .analysis-loading .loading-img {
 width: 32px;
 height: 32px;
 background-image: url(../../images/loading.svg);
 animation: loading 2s linear infinite;
}