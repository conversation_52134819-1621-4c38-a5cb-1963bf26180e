package com.pricecenter.service.validator;

import com.pricecenter.dto.PriceInfoUploadValidatorDto;
import com.pricecenter.service.Validator;
import com.pricecenter.dto.ValidatorResult;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.model.Trader;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 供应商名称相关校验器
 */
@Service
public class TraderNameValidator implements Validator<PriceInfoUploadValidatorDto> {

    @Autowired
    @Qualifier("traderMapper")
    private TraderMapper traderMapper;

    @Override
    public ValidatorResult validator(PriceInfoUploadValidatorDto priceInfoUploadValidatorDto) {

        List<String> exsitSkuList = new ArrayList<>();

        ValidatorResult result = ValidatorResult.newBuild();

        //获取所有的行
        List<Row> rowList = priceInfoUploadValidatorDto.getRows();

        Row thisRow = null;
        int rowNum = 0;
        int cellNum = 1;

        for(int i = 0;i < rowList.size();i++){

            rowNum = i;

            thisRow = rowList.get(i);

            Cell cell = thisRow.getCell(cellNum);

            if (cell == null || cell.getCellType() ==  CellType.BLANK) {
                return result.setMessage("第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：不允许为空，请验证！");
            }

            cell.setCellType(CellType.STRING);
            String traderName = cell.getStringCellValue();

            //供应商“XXX”不存在，提交失败
            Trader trader = traderMapper.findTraderByName(traderName);
            if(trader == null){
                return result.setMessage("供应商"+traderName+"不存在，提交失败");
            }

            /*Integer status = traderMapper.findTraderAuditStatus(trader.getTraderId());

            //供应商“XXX”未审核通过，提交失败
            if(status == null || status != 1){
                return result.setMessage("供应商"+traderName+"未审核通过，提交失败");
            }*/

            priceInfoUploadValidatorDto.getTraderNameAndIdMap().put(traderName,Long.valueOf(trader.getTraderId()));
        }

        return result.setResult(true);

    }
}
