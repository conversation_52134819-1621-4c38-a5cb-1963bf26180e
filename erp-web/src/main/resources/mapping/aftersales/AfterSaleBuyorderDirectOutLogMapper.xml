<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.aftersales.dao.AfterSaleBuyorderDirectOutLogMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.aftersales.model.AfterSaleBuyorderDirectOutLog">
    <id column="AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID" jdbcType="INTEGER" property="afterSaleBuyorderDirectOutLogId" />
    <result column="AFTER_SALES_ID" jdbcType="INTEGER" property="afterSalesId" />
    <result column="AFTER_SALES_GOODS_ID" jdbcType="INTEGER" property="afterSalesGoodsId" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATE_TIME" jdbcType="BIGINT" property="updateTime" />
    <result column="UPDATER" jdbcType="TIMESTAMP" property="updater" />
    <result column="IS_DELETE" jdbcType="TINYINT" property="isDelete" />
    <result column="VEDENG_BATCH_NUM" jdbcType="VARCHAR" property="vedengBatchNum" />
    <result column="PRODUCE_TIME" jdbcType="BIGINT" property="produceTime" />
    <result column="VALID_TIME" jdbcType="BIGINT" property="validTime" />
    <result column="OUT_TIME" jdbcType="BIGINT" property="outTime" />
    <result column="INDUSTRY_BATCH_NUMBER" jdbcType="VARCHAR" property="industryBatchNumber" />
    <result column="STERILIZATION_NUMBER" jdbcType="VARCHAR" property="sterilizationNumber" />
    <result column="REGISTRATION_NUMBER" jdbcType="VARCHAR" property="registrationNumber" />
    <result column="FIRST_ENGAGE_ID" jdbcType="INTEGER" property="firstEngageId" />
    <result column="SHOW_NAME" jdbcType="VARCHAR" property="showName" />
    <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId" />
    <result column="BRAND_NAME" jdbcType="VARCHAR" property="brandName" />
    <result column="MODEL" jdbcType="VARCHAR" property="model" />
    <result column="SPEC" jdbcType="VARCHAR" property="spec" />
    <result column="UNIT_NAME" jdbcType="VARCHAR" property="unitName" />
  </resultMap>
  <sql id="Base_Column_List">
    AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID, AFTER_SALES_ID, AFTER_SALES_GOODS_ID, SKU, 
    NUM, ADD_TIME, CREATOR, UPDATE_TIME, UPDATER, IS_DELETE, VEDENG_BATCH_NUM, PRODUCE_TIME, 
    VALID_TIME, OUT_TIME, INDUSTRY_BATCH_NUMBER, STERILIZATION_NUMBER, REGISTRATION_NUMBER,
    FIRST_ENGAGE_ID, SHOW_NAME, GOODS_ID, BRAND_NAME, MODEL, SPEC, UNIT_NAME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_AFTER_SALE_BUYORDER_DIRECT_OUT_LOG
    where AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_AFTER_SALE_BUYORDER_DIRECT_OUT_LOG
    where AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID" keyProperty="afterSaleBuyorderDirectOutLogId" parameterType="com.vedeng.aftersales.model.AfterSaleBuyorderDirectOutLog" useGeneratedKeys="true">
    insert into T_AFTER_SALE_BUYORDER_DIRECT_OUT_LOG (AFTER_SALES_ID, AFTER_SALES_GOODS_ID, 
      SKU, NUM, ADD_TIME, CREATOR, 
      UPDATE_TIME, UPDATER, IS_DELETE, 
      VEDENG_BATCH_NUM, PRODUCE_TIME, VALID_TIME, 
      OUT_TIME, INDUSTRY_BATCH_NUMBER, STERILIZATION_NUMBER,
      REGISTRATION_NUMBER, FIRST_ENGAGE_ID, SHOW_NAME, 
      GOODS_ID, BRAND_NAME, MODEL, SPEC,
      UNIT_NAME)
    values (#{afterSalesId,jdbcType=INTEGER}, #{afterSalesGoodsId,jdbcType=INTEGER}, 
      #{sku,jdbcType=VARCHAR}, #{num,jdbcType=INTEGER}, #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, 
      #{updateTime,jdbcType=BIGINT}, #{updater,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=TINYINT}, 
      #{vedengBatchNum,jdbcType=VARCHAR}, #{produceTime,jdbcType=BIGINT}, #{validTime,jdbcType=BIGINT}, 
      #{outTime,jdbcType=BIGINT}, #{industryBatchNumber,jdbcType=VARCHAR}, #{sterilizationNumber,jdbcType=VARCHAR},
      #{registrationNumber,jdbcType=VARCHAR}, #{firstEngageId,jdbcType=INTEGER}, #{showName,jdbcType=VARCHAR}, 
      #{goodsId,jdbcType=INTEGER}, #{brandName,jdbcType=VARCHAR}, #{model,jdbcType=VARCHAR}, #{spec,jdbcType=VARCHAR},
      #{unitName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID" keyProperty="afterSaleBuyorderDirectOutLogId" parameterType="com.vedeng.aftersales.model.AfterSaleBuyorderDirectOutLog" useGeneratedKeys="true">
    insert into T_AFTER_SALE_BUYORDER_DIRECT_OUT_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterSalesId != null">
        AFTER_SALES_ID,
      </if>
      <if test="afterSalesGoodsId != null">
        AFTER_SALES_GOODS_ID,
      </if>
      <if test="sku != null">
        SKU,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="vedengBatchNum != null">
        VEDENG_BATCH_NUM,
      </if>
      <if test="produceTime != null">
        PRODUCE_TIME,
      </if>
      <if test="validTime != null">
        VALID_TIME,
      </if>
      <if test="outTime != null">
        OUT_TIME,
      </if>
      <if test="industryBatchNumber != null">
        INDUSTRY_BATCH_NUMBER,
      </if>
      <if test="sterilizationNumber != null">
        STERILIZATION_NUMBER,
      </if>
      <if test="registrationNumber != null">
        REGISTRATION_NUMBER,
      </if>
      <if test="firstEngageId != null">
        FIRST_ENGAGE_ID,
      </if>
      <if test="showName != null">
        SHOW_NAME,
      </if>
      <if test="goodsId != null">
        GOODS_ID,
      </if>
      <if test="brandName != null">
        BRAND_NAME,
      </if>
      <if test="model != null">
        MODEL,
      </if>
      <if test="spec != null">
        SPEC,
      </if>
      <if test="unitName != null">
        UNIT_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="afterSalesId != null">
        #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesGoodsId != null">
        #{afterSalesGoodsId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="vedengBatchNum != null">
        #{vedengBatchNum,jdbcType=VARCHAR},
      </if>
      <if test="produceTime != null">
        #{produceTime,jdbcType=BIGINT},
      </if>
      <if test="validTime != null">
        #{validTime,jdbcType=BIGINT},
      </if>
      <if test="outTime != null">
        #{outTime,jdbcType=BIGINT},
      </if>
      <if test="industryBatchNumber!= null">
        #{industryBatchNumber,jdbcType=VARCHAR},
      </if>
      <if test="sterilizationNumber != null">
        #{sterilizationNumber,jdbcType=VARCHAR},
      </if>
      <if test="registrationNumber != null">
        #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="firstEngageId != null">
        #{firstEngageId,jdbcType=INTEGER},
      </if>
      <if test="showName != null">
        #{showName,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null">
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="brandName != null">
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="spec != null">
        #{spec,jdbcType=VARCHAR},
      </if>
      <if test="unitName != null">
        #{unitName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.aftersales.model.AfterSaleBuyorderDirectOutLog">
    update T_AFTER_SALE_BUYORDER_DIRECT_OUT_LOG
    <set>
      <if test="afterSalesId != null">
        AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesGoodsId != null">
        AFTER_SALES_GOODS_ID = #{afterSalesGoodsId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="vedengBatchNum != null">
        VEDENG_BATCH_NUM = #{vedengBatchNum,jdbcType=VARCHAR},
      </if>
      <if test="produceTime != null">
        PRODUCE_TIME = #{produceTime,jdbcType=BIGINT},
      </if>
      <if test="validTime != null">
        VALID_TIME = #{validTime,jdbcType=BIGINT},
      </if>
      <if test="outTime != null">
        OUT_TIME = #{outTime,jdbcType=BIGINT},
      </if>
      <if test="industryBatchNumber != null">
        INDUSTRY_BATCH_NUMBER = #{industryBatchNumber,jdbcType=VARCHAR},
      </if>
      <if test="sterilizationNumber != null">
        STERILIZATION_NUMBER = #{sterilizationNumber,jdbcType=VARCHAR},
      </if>
      <if test="registrationNumber != null">
        REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="firstEngageId != null">
        FIRST_ENGAGE_ID = #{firstEngageId,jdbcType=INTEGER},
      </if>
      <if test="showName != null">
        SHOW_NAME = #{showName,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null">
        GOODS_ID = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="brandName != null">
        BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        MODEL = #{model,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        SPEC = #{spec,jdbcType=VARCHAR},
      </if>
      <if test="unitName != null">
        UNIT_NAME = #{unitName,jdbcType=VARCHAR},
      </if>
    </set>
    where AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.aftersales.model.AfterSaleBuyorderDirectOutLog">
    update T_AFTER_SALE_BUYORDER_DIRECT_OUT_LOG
    set AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      AFTER_SALES_GOODS_ID = #{afterSalesGoodsId,jdbcType=INTEGER},
      SKU = #{sku,jdbcType=VARCHAR},
      NUM = #{num,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=TIMESTAMP},
      IS_DELETE = #{isDelete,jdbcType=TINYINT},
      VEDENG_BATCH_NUM = #{vedengBatchNum,jdbcType=VARCHAR},
      PRODUCE_TIME = #{produceTime,jdbcType=BIGINT},
      VALID_TIME = #{validTime,jdbcType=BIGINT},
      OUT_TIME = #{outTime,jdbcType=BIGINT},
      INDUSTRY_BATCH_NUMBER = #{industryBatchNumber,jdbcType=VARCHAR},
      STERILIZATION_NUMBER = #{sterilizationNumber,jdbcType=VARCHAR},
      REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR},
      FIRST_ENGAGE_ID = #{firstEngageId,jdbcType=INTEGER},
      SHOW_NAME = #{showName,jdbcType=VARCHAR},
      GOODS_ID = #{goodsId,jdbcType=INTEGER},
      BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      MODEL = #{model,jdbcType=VARCHAR},
      SPEC = #{spec,jdbcType=VARCHAR},
      UNIT_NAME = #{unitName,jdbcType=VARCHAR}
    where AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER}
  </update>

  <select id="getTotalNumByAfterSalesGoodsId" resultType="java.lang.Integer">
      select COALESCE (SUM(NUM),0) FROM T_AFTER_SALE_BUYORDER_DIRECT_OUT_LOG
      WHERE AFTER_SALES_GOODS_ID = #{afterSalesGoodsId,jdbcType = INTEGER}
      AND IS_DELETE = 0
  </select>
  <select id="getDirectReturnOutList" resultType="com.vedeng.aftersales.model.AfterSaleBuyorderDirectOutLog">
    select * from T_AFTER_SALE_BUYORDER_DIRECT_OUT_LOG WHERE IS_DELETE = 0 and AFTER_SALES_ID =  #{afterSalesId,jdbcType=INTEGER}
  </select>
  <select id="getBuyOrderAfterSaleLog" resultType="com.vedeng.aftersales.model.AfterSaleBuyorderDirectOutLog">
    select * from T_AFTER_SALE_BUYORDER_DIRECT_OUT_LOG WHERE AFTER_SALES_GOODS_ID = #{afterSalesGoodsId,jdbcType=INTEGER}
    AND IS_DELETE = 0
  </select>
    <select id="getSimiliarLog" resultType="com.vedeng.aftersales.model.AfterSaleBuyorderDirectOutLog">
      select * from T_AFTER_SALE_BUYORDER_DIRECT_OUT_LOG
      WHERE AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
      AND AFTER_SALES_GOODS_ID = #{afterSalesGoodsId,jdbcType=INTEGER}
      AND SKU = #{sku,jdbcType=VARCHAR}
      AND IS_DELETE = 0
      AND VEDENG_BATCH_NUM = #{vedengBatchNum,jdbcType=VARCHAR}
      <choose>
        <when test="produceTime == null">
          AND PRODUCE_TIME is null
        </when>
        <otherwise>
          AND PRODUCE_TIME = #{produceTime,jdbcType=BIGINT}
        </otherwise>
      </choose>

      <choose>
        <when test="validTime == null">
          AND VALID_TIME is null
        </when>
        <otherwise>
          AND VALID_TIME = #{validTime,jdbcType=BIGINT}
        </otherwise>
      </choose>

      <choose>
        <when test="outTime == null">
          AND OUT_TIME is null
        </when>
        <otherwise>
          AND OUT_TIME = #{outTime,jdbcType=BIGINT}
        </otherwise>
      </choose>
      AND INDUSTRY_BATCH_NUMBER = #{industryBatchNumber,jdbcType=VARCHAR}
      AND STERILIZATION_NUMBER = #{sterilizationNumber,jdbcType=VARCHAR}
      AND REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR}
      AND FIRST_ENGAGE_ID = #{firstEngageId,jdbcType=INTEGER}
      AND SHOW_NAME = #{showName,jdbcType=VARCHAR}
      AND GOODS_ID = #{goodsId,jdbcType=INTEGER}
      AND BRAND_NAME = #{brandName,jdbcType=VARCHAR}
      AND MODEL = #{model,jdbcType=VARCHAR}
      AND SPEC = #{spec,jdbcType=VARCHAR}
      AND UNIT_NAME = #{unitName,jdbcType=VARCHAR}
    </select>
    <update id="delectById">
    update T_AFTER_SALE_BUYORDER_DIRECT_OUT_LOG SET IS_DELETE = 1 WHERE
    AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER}
  </update>
</mapper>