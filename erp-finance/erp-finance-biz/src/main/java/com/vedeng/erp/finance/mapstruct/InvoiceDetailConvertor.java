package com.vedeng.erp.finance.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.finance.domain.entity.InvoiceDetailEntity;
import com.vedeng.erp.finance.dto.InvoiceDetailDto;
import com.vedeng.erp.finance.dto.InvoiceDto;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @description dto转entity
 * @date 2022/7/12 10:45
 **/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,builder = @Builder(disableBuilder = true))
public interface InvoiceDetailConvertor extends BaseMapStruct<InvoiceDetailEntity, InvoiceDetailDto> {
}
