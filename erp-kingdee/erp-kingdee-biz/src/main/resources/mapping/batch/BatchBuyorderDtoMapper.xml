<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchBuyorderDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchBuyorderDto">
    <!--@mbg.generated-->
    <!--@Table T_BUYORDER-->
    <id column="BUYORDER_ID" jdbcType="INTEGER" property="buyorderId" />
    <result column="BUYORDER_NO" jdbcType="VARCHAR" property="buyorderNo" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="ORDER_TYPE" jdbcType="INTEGER" property="orderType" />
    <result column="ORG_ID" jdbcType="INTEGER" property="orgId" />
    <result column="USER_ID" jdbcType="INTEGER" property="userId" />
    <result column="VALID_STATUS" jdbcType="INTEGER" property="validStatus" />
    <result column="VALID_TIME" jdbcType="BIGINT" property="validTime" />
    <result column="STATUS" jdbcType="INTEGER" property="status" />
    <result column="LOCKED_STATUS" jdbcType="INTEGER" property="lockedStatus" />
    <result column="INVOICE_STATUS" jdbcType="INTEGER" property="invoiceStatus" />
    <result column="INVOICE_TIME" jdbcType="BIGINT" property="invoiceTime" />
    <result column="PAYMENT_STATUS" jdbcType="INTEGER" property="paymentStatus" />
    <result column="PAYMENT_TIME" jdbcType="BIGINT" property="paymentTime" />
    <result column="DELIVERY_STATUS" jdbcType="INTEGER" property="deliveryStatus" />
    <result column="DELIVERY_TIME" jdbcType="BIGINT" property="deliveryTime" />
    <result column="ARRIVAL_STATUS" jdbcType="INTEGER" property="arrivalStatus" />
    <result column="ARRIVAL_TIME" jdbcType="BIGINT" property="arrivalTime" />
    <result column="SERVICE_STATUS" jdbcType="INTEGER" property="serviceStatus" />
    <result column="HAVE_ACCOUNT_PERIOD" jdbcType="INTEGER" property="haveAccountPeriod" />
    <result column="DELIVERY_DIRECT" jdbcType="INTEGER" property="deliveryDirect" />
    <result column="TOTAL_AMOUNT" jdbcType="DECIMAL" property="totalAmount" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName" />
    <result column="TRADER_CONTACT_ID" jdbcType="INTEGER" property="traderContactId" />
    <result column="TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="traderContactName" />
    <result column="TRADER_CONTACT_MOBILE" jdbcType="VARCHAR" property="traderContactMobile" />
    <result column="TRADER_CONTACT_TELEPHONE" jdbcType="VARCHAR" property="traderContactTelephone" />
    <result column="TRADER_ADDRESS_ID" jdbcType="INTEGER" property="traderAddressId" />
    <result column="TRADER_AREA" jdbcType="VARCHAR" property="traderArea" />
    <result column="TRADER_ADDRESS" jdbcType="VARCHAR" property="traderAddress" />
    <result column="TRADER_COMMENTS" jdbcType="VARCHAR" property="traderComments" />
    <result column="TAKE_TRADER_ID" jdbcType="INTEGER" property="takeTraderId" />
    <result column="TAKE_TRADER_NAME" jdbcType="VARCHAR" property="takeTraderName" />
    <result column="TAKE_TRADER_CONTACT_ID" jdbcType="INTEGER" property="takeTraderContactId" />
    <result column="TAKE_TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="takeTraderContactName" />
    <result column="TAKE_TRADER_CONTACT_MOBILE" jdbcType="VARCHAR" property="takeTraderContactMobile" />
    <result column="TAKE_TRADER_CONTACT_TELEPHONE" jdbcType="VARCHAR" property="takeTraderContactTelephone" />
    <result column="TAKE_TRADER_ADDRESS_ID" jdbcType="INTEGER" property="takeTraderAddressId" />
    <result column="TAKE_TRADER_AREA" jdbcType="VARCHAR" property="takeTraderArea" />
    <result column="TAKE_TRADER_ADDRESS" jdbcType="VARCHAR" property="takeTraderAddress" />
    <result column="PAYMENT_TYPE" jdbcType="INTEGER" property="paymentType" />
    <result column="PREPAID_AMOUNT" jdbcType="DECIMAL" property="prepaidAmount" />
    <result column="ACCOUNT_PERIOD_AMOUNT" jdbcType="DECIMAL" property="accountPeriodAmount" />
    <result column="PERIOD_DAY" jdbcType="INTEGER" property="periodDay" />
    <result column="RETAINAGE_AMOUNT" jdbcType="DECIMAL" property="retainageAmount" />
    <result column="RETAINAGE_AMOUNT_MONTH" jdbcType="INTEGER" property="retainageAmountMonth" />
    <result column="LOGISTICS_ID" jdbcType="INTEGER" property="logisticsId" />
    <result column="INVOICE_TYPE" jdbcType="INTEGER" property="invoiceType" />
    <result column="FREIGHT_DESCRIPTION" jdbcType="INTEGER" property="freightDescription" />
    <result column="PAYMENT_COMMENTS" jdbcType="VARCHAR" property="paymentComments" />
    <result column="LOGISTICS_COMMENTS" jdbcType="VARCHAR" property="logisticsComments" />
    <result column="INVOICE_COMMENTS" jdbcType="VARCHAR" property="invoiceComments" />
    <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
    <result column="ADDITIONAL_CLAUSE" jdbcType="VARCHAR" property="additionalClause" />
    <result column="STATUS_COMMENTS" jdbcType="INTEGER" property="statusComments" />
    <result column="SATISFY_DELIVERY_TIME" jdbcType="BIGINT" property="satisfyDeliveryTime" />
    <result column="ESTIMATE_ARRIVAL_TIME" jdbcType="VARCHAR" property="estimateArrivalTime" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATE_DATA_TIME" jdbcType="TIMESTAMP" property="updateDataTime" />
    <result column="REAL_PAY_AMOUNT" jdbcType="DECIMAL" property="realPayAmount" />
    <result column="REAL_RETURN_AMOUNT" jdbcType="DECIMAL" property="realReturnAmount" />
    <result column="REAL_TOTAL_AMOUNT" jdbcType="DECIMAL" property="realTotalAmount" />
    <result column="NOPAYBACK_AMOUNT" jdbcType="DECIMAL" property="nopaybackAmount" />
    <result column="REAL_INVOICE_AMOUNT" jdbcType="DECIMAL" property="realInvoiceAmount" />
    <result column="IS_RISK" jdbcType="BOOLEAN" property="isRisk" />
    <result column="RISK_COMMENTS" jdbcType="VARCHAR" property="riskComments" />
    <result column="RISK_TIME" jdbcType="BIGINT" property="riskTime" />
    <result column="EXPEDITING_STATUS" jdbcType="INTEGER" property="expeditingStatus" />
    <result column="EXPEDITING_FOLLOW_STATUS" jdbcType="INTEGER" property="expeditingFollowStatus" />
    <result column="NEW_FLOW" jdbcType="INTEGER" property="newFlow" />
    <result column="SUB_STATUS" jdbcType="VARCHAR" property="subStatus" />
    <result column="VERIFY_STATUS" jdbcType="INTEGER" property="verifyStatus" />
    <result column="IS_NEW" jdbcType="TINYINT" property="isNew" />
    <result column="SALEORDER_NOS" jdbcType="VARCHAR" property="saleorderNos" />
    <result column="CONTRACT_URL" jdbcType="VARCHAR" property="contractUrl" />
    <result column="URGED_MAINTAIN_BATCH_INFO" jdbcType="TINYINT" property="urgedMaintainBatchInfo" />
    <result column="IS_GIFT" jdbcType="INTEGER" property="isGift" />
    <result column="TRADER_SUPPLIER_ID" jdbcType="INTEGER" property="traderSupplierId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    BUYORDER_ID, BUYORDER_NO, COMPANY_ID, ORDER_TYPE, ORG_ID, USER_ID, VALID_STATUS, 
    VALID_TIME, `STATUS`, LOCKED_STATUS, INVOICE_STATUS, INVOICE_TIME, PAYMENT_STATUS, 
    PAYMENT_TIME, DELIVERY_STATUS, DELIVERY_TIME, ARRIVAL_STATUS, ARRIVAL_TIME, SERVICE_STATUS, 
    HAVE_ACCOUNT_PERIOD, DELIVERY_DIRECT, TOTAL_AMOUNT, TRADER_ID, TRADER_NAME, TRADER_CONTACT_ID, 
    TRADER_CONTACT_NAME, TRADER_CONTACT_MOBILE, TRADER_CONTACT_TELEPHONE, TRADER_ADDRESS_ID, 
    TRADER_AREA, TRADER_ADDRESS, TRADER_COMMENTS, TAKE_TRADER_ID, TAKE_TRADER_NAME, TAKE_TRADER_CONTACT_ID, 
    TAKE_TRADER_CONTACT_NAME, TAKE_TRADER_CONTACT_MOBILE, TAKE_TRADER_CONTACT_TELEPHONE, 
    TAKE_TRADER_ADDRESS_ID, TAKE_TRADER_AREA, TAKE_TRADER_ADDRESS, PAYMENT_TYPE, PREPAID_AMOUNT, 
    ACCOUNT_PERIOD_AMOUNT, PERIOD_DAY, RETAINAGE_AMOUNT, RETAINAGE_AMOUNT_MONTH, LOGISTICS_ID, 
    INVOICE_TYPE, FREIGHT_DESCRIPTION, PAYMENT_COMMENTS, LOGISTICS_COMMENTS, INVOICE_COMMENTS, 
    COMMENTS, ADDITIONAL_CLAUSE, STATUS_COMMENTS, SATISFY_DELIVERY_TIME, ESTIMATE_ARRIVAL_TIME, 
    ADD_TIME, CREATOR, MOD_TIME, UPDATER, UPDATE_DATA_TIME, REAL_PAY_AMOUNT, REAL_RETURN_AMOUNT, 
    REAL_TOTAL_AMOUNT, NOPAYBACK_AMOUNT, REAL_INVOICE_AMOUNT, IS_RISK, RISK_COMMENTS, 
    RISK_TIME, EXPEDITING_STATUS, EXPEDITING_FOLLOW_STATUS, NEW_FLOW, SUB_STATUS, VERIFY_STATUS, 
    IS_NEW, SALEORDER_NOS, CONTRACT_URL, URGED_MAINTAIN_BATCH_INFO, IS_GIFT
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_BUYORDER
    where BUYORDER_ID = #{buyorderId,jdbcType=INTEGER}
  </select>

<!--auto generated by MybatisCodeHelper on 2022-11-24-->
  <select id="selectByBuyorderNo" resultMap="BaseResultMap">
    select
    TB.BUYORDER_ID, BUYORDER_NO, COMPANY_ID, ORDER_TYPE, ORG_ID, USER_ID, VALID_STATUS, VALID_TIME, STATUS,
    LOCKED_STATUS, INVOICE_STATUS, INVOICE_TIME, PAYMENT_STATUS, PAYMENT_TIME, DELIVERY_STATUS, DELIVERY_TIME,
    ARRIVAL_STATUS, ARRIVAL_TIME, SERVICE_STATUS, HAVE_ACCOUNT_PERIOD, DELIVERY_DIRECT, TOTAL_AMOUNT, TB.TRADER_ID,
    TRADER_NAME, TRADER_CONTACT_ID, TRADER_CONTACT_NAME, TRADER_CONTACT_MOBILE, TRADER_CONTACT_TELEPHONE,
    TRADER_ADDRESS_ID, TRADER_AREA, TRADER_ADDRESS, TRADER_COMMENTS, TAKE_TRADER_ID, TAKE_TRADER_NAME,
    TAKE_TRADER_CONTACT_ID, TAKE_TRADER_CONTACT_NAME, TAKE_TRADER_CONTACT_MOBILE, TAKE_TRADER_CONTACT_TELEPHONE,
    TAKE_TRADER_ADDRESS_ID, TAKE_TRADER_AREA, TAKE_TRADER_ADDRESS, PAYMENT_TYPE, PREPAID_AMOUNT, ACCOUNT_PERIOD_AMOUNT,
    TB.PERIOD_DAY, RETAINAGE_AMOUNT, RETAINAGE_AMOUNT_MONTH, LOGISTICS_ID, INVOICE_TYPE, FREIGHT_DESCRIPTION,
    PAYMENT_COMMENTS, LOGISTICS_COMMENTS, INVOICE_COMMENTS, TB.COMMENTS, ADDITIONAL_CLAUSE, STATUS_COMMENTS,
    SATISFY_DELIVERY_TIME, ESTIMATE_ARRIVAL_TIME, TB.ADD_TIME, TB.CREATOR, TB.MOD_TIME, TB.UPDATER, UPDATE_DATA_TIME,
    REAL_PAY_AMOUNT, REAL_RETURN_AMOUNT, REAL_TOTAL_AMOUNT, NOPAYBACK_AMOUNT, REAL_INVOICE_AMOUNT, IS_RISK,
    RISK_COMMENTS, RISK_TIME, EXPEDITING_STATUS, EXPEDITING_FOLLOW_STATUS, NEW_FLOW, SUB_STATUS, VERIFY_STATUS, IS_NEW,
    SALEORDER_NOS, CONTRACT_URL, URGED_MAINTAIN_BATCH_INFO, IS_GIFT,
    TTS.TRADER_SUPPLIER_ID
    from T_BUYORDER TB
    LEFT JOIN T_TRADER_SUPPLIER TTS on TB.TRADER_ID = TTS.TRADER_ID
    where BUYORDER_NO=#{buyorderNo,jdbcType=VARCHAR};

  </select>

  <select id="getSaleAndBuyOrderGoodsByIds" resultType="com.vedeng.erp.kingdee.batch.dto.BatchSaleAndBuyOrderGoodsDto">
    select TS.SALEORDER_NO saleorderNo,
    TSG.SALEORDER_GOODS_ID saleorderGoodsId,
    TRBJS.BUYORDER_GOODS_ID buyorderGoodsId,
    TSG.GOODS_ID goodsId
    from T_R_BUYORDER_J_SALEORDER TRBJS
    join T_SALEORDER_GOODS TSG ON TRBJS.SALEORDER_GOODS_ID = TSG.SALEORDER_GOODS_ID
    join T_SALEORDER TS ON TSG.SALEORDER_ID = TS.SALEORDER_ID
    where TRBJS.BUYORDER_GOODS_ID IN
    <foreach collection="list" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
    AND TSG.IS_DELETE = 0
    AND TS.IS_DELETE = 0
  </select>
</mapper>