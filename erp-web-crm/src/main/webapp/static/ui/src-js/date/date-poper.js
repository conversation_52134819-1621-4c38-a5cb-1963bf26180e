Vue.component('ui-date-poper', {
    template: `<div class="ui-poper-wrap" ref="popwrap" :style="'width:' + dropPosition.width + ';left:' +  dropPosition.left + 'px;top:' +  dropPosition.top + 'px;z-index:' + dropPosition.zindex">
        <slot></slot>
    </div>`,
    props: {
        show: {
            type: Boolean,
            default: false
        },
        errorable: {
            type: Boolean,
            default: false
        },
        el: {
            type: Object,
            default() {
                return null
            }
        },
        panel: {
            type: Object,
            default() {
                return null
            }
        },
        zindex: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            dropPosition: {},
            parent: null
        }
    },
    watch: {
        show() {
            if (this.show) {
                document.body.append(this.$refs.popwrap);
                console.log(this.parent)
                this.$nextTick(()=> {
                    this.calcPosition();
                })
            } else {
                this.$refs.popwrap.remove();
            }
        },
    },
    computed: {

    },
    mounted() {
        let parent = this.getScrollParent(this.$parent.$el) || window;
        
        parent.addEventListener('scroll', this.calcPosition);
        window.addEventListener('resize', this.calcPosition);

        this.parent = parent;
    },
    methods: {
        calcPosition () {
            if(!this.show) {
                return;
            }

            let bodyWidth = document.body.clientWidth;
            let bodyHeight = document.body.clientHeight;
            let panel = this.panel || this.$parent.$refs.pickers; // 面板
            let panelWidth = panel.clientWidth;
            let panelHeight = panel.clientHeight;

            let el = this.el || this.$parent.$el; // 输入框
            let inputPosition = el.getBoundingClientRect();

            // 水平
            let left = inputPosition.left;
            let diff = left + panelWidth - bodyWidth;
            if ((left + panelWidth) > bodyWidth) {
                if (left > diff) { // 左侧距离足够放面板
                    left = left - (panelWidth - inputPosition.width)
                } else {
                    left = `-${diff}px`;
                }
            }

            // 垂直
            let top = inputPosition.top + inputPosition.height;
            if ((top+panelHeight) > bodyHeight) {
                if (top >= panelHeight) { // top够放panel
                    top = top - panelHeight - inputPosition.height - 2;
                } else { // top也不够放panel -- 不处理
                    if (this.errorable) {
                        top = top - 26;
                    }
                }
            } else { // 下
                if (this.errorable) {
                    top = top - 26;
                }
            }

            let width = el.offsetWidth + 'px';

            this.dropPosition = {
                width: width,
                top: top,
                left: left,
                zindex: this.zindex || (this.parent === window ? 20 : 3000)
            }

            // 出场动效
            if (top >= inputPosition.top) {
                this.$parent.animation = 'appear-down';
            } else {
                this.$parent.animation = 'appear-up';
            }
        },
        // }, 100),
        getScrollParent(element) {
            if (!element) {
                return null;
            }

            const overflowRegex = /(scroll|auto)/;
            const parent = element.parentElement;

            if (parent && overflowRegex.test(window.getComputedStyle(parent).overflow + window.getComputedStyle(parent).overflowY + window.getComputedStyle(parent).overflowX)) {
                return parent;
            }

            return this.getScrollParent(parent);
        }
    }
})