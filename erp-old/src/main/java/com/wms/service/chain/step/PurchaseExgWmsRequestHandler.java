package com.wms.service.chain.step;

import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.dao.AfterSalesDetailMapper;
import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.util.DateUtil;
import com.vedeng.order.controller.SaleorderController;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.model.Trader;
import com.wms.constant.LogicalEnum;
import com.wms.constant.WMSContant;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.dto.*;
import com.wms.model.po.WmsSendOrder;
import com.wms.service.LogicalSaleorderChooseService;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.WmsInterface;
import com.wms.service.chain.AbstractHandlerStep;
import com.wms.service.chain.HandlerStepContext;
import com.wms.service.context.ThreadLocalContext;
import com.wms.service.stockallocation.PurchaseStockAllocationStrategy;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 发送WMS请求处理器
 */
@Service
public class PurchaseExgWmsRequestHandler extends AbstractHandlerStep {

    private static final Logger LOGGER = LoggerFactory.getLogger(PurchaseExgWmsRequestHandler.class);

    @Resource
    private WMSInterfaceFactory wmsInterfaceFactory;

    @Resource
    private TraderMapper traderMapper;

    @Resource
    private PurchaseStockAllocationStrategy stockAllocationStrategy;

    @Resource
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Resource
    private AfterSalesDetailMapper afterSalesDetailMapper;

    @Autowired
    private LogicalSaleorderChooseService logicalSaleorderChooseService;

    @Override
    protected void doDealWith(HandlerStepContext context) throws Exception {

        AfterSalesVo afterSalesInfo = context.get("afterSalesInfo");

        User user = context.get("user");

        LOGGER.info("换货单下发WMS请求start==================,参数:" + JSON.toJSONString(afterSalesInfo));

        ExgOrderDto exgOrderDto = new ExgOrderDto();

        //售后换货单号或借货单号
        exgOrderDto.setDOCNO(afterSalesInfo.getAfterSalesNo());
        //单据类型 - 采购换货单
        exgOrderDto.setDOC_TYPE(WmsInterfaceOrderType.EXG_PURCHASE);
        //是否闪电换货
        exgOrderDto.setIs_exchange("N");
        //审核时间
        exgOrderDto.setApproveTime(DateUtil.convertString(System.currentTimeMillis(),DateUtil.TIME_FORMAT));
        //部门
        exgOrderDto.setDepartment(user.getOrgName());
        //制单员
        exgOrderDto.setStaffName(user.getUsername());

        Trader trader = traderMapper.getTraderByTraderId(afterSalesInfo.getTraderId());

        //客户ID或供应商ID
        exgOrderDto.setConsigneeID(trader.getTraderId() + Strings.EMPTY);
        //客户名称或供应商名称
        exgOrderDto.setConsigneeName(trader.getTraderName());

        String[] areas = afterSalesInfo.getArea().split(" ");
        //省
        exgOrderDto.setC_Province(areas[0]);
        //城市
        exgOrderDto.setC_City(areas[1]);
        //区
        exgOrderDto.setC_district(areas[2]);
        //街道 无
        exgOrderDto.setC_street("/");
        //收货详细地址
        exgOrderDto.setC_Address1(afterSalesInfo.getAddress());
        //收货人联系人
        exgOrderDto.setC_Contact(afterSalesInfo.getTraderContactName());
        //收货人电话
        exgOrderDto.setC_Tel1(afterSalesInfo.getTraderContactTelephone());
        //收货人手机
        exgOrderDto.setC_Tel2(afterSalesInfo.getTraderContactMobile());
        //售后原因
        exgOrderDto.setAsreasons(afterSalesInfo.getReasonName());

        exgOrderDto.setApproveTime(DateUtil.convertString(System.currentTimeMillis(),DateUtil.TIME_FORMAT));
        exgOrderDto.setReferenceNo(afterSalesInfo.getOrderNo());

        List<ExgOrderGoodDto> details = getDetails(afterSalesInfo);
        if(CollectionUtils.isEmpty(details)){
            return;
        }
        exgOrderDto.setDetails(details);

        //下发存表，V_WMS_SEND_ORDER
        WmsSendOrder wmsSendOrder = logicalSaleorderChooseService.getWmsSendOrder(afterSalesInfo.getAfterSalesNo(),afterSalesInfo.getAfterSalesId(), ErpConst.SIX,user);
        try{
            WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_EXG_DATA);
            WmsResponse wmsResponse = wmsInterface.request(exgOrderDto);
            if ("0".equals(wmsResponse.getReturnFlag())) {
                //throw new Exception("采购售后换货单下传失败");
                LOGGER.info("采购售后换货单下传失败");
            }else {
                //下发成功，更新V_WMS_SEND_ORDER状态
                logicalSaleorderChooseService.saveWmsSendOrder(wmsSendOrder);
            }
        }catch (Exception e){
            LOGGER.error("采购售后换货单下传失败,{}",e);
        }
    }

    private List<ExgOrderGoodDto> getDetails(AfterSalesVo afterSalesInfo) throws Exception{

//        List<ExgOrderGoodDto> orderGoodList = new ArrayList<>();
//
//        List<AfterSalesGoodsVo> afterSalesGoodList =
//                this.afterSalesGoodsMapper.getAfterSalesGoodList(afterSalesInfo.getAfterSalesId());
//
//        for(AfterSalesGoodsVo afterSaleGood : afterSalesGoodList){
//            if(ErpConst.ONE.equals(afterSaleGood.getDeliveryDirect())){
//                //直发换货商品不下发wms
//                continue;
//            }
//            StockAllocationRequest stockAllocationRequest = new StockAllocationRequest();
//            stockAllocationRequest.setNum(afterSaleGood.getNum());
//            stockAllocationRequest.setBuyOrderGoodId(afterSaleGood.getOrderDetailId());
//            stockAllocationRequest.setSku(afterSaleGood.getSku());
//
//            List<StockAllocationResult> allocationResultList = stockAllocationStrategy.stockAllocation(stockAllocationRequest);
//
//            //转换成出库单dto
//            List<ExgOrderGoodDto> exgOrderGoodList = convertAllocationResult(afterSaleGood.getAfterSalesGoodsId(),allocationResultList);
//            orderGoodList.addAll(exgOrderGoodList);
//        }
        List<ExgOrderGoodDto> exgOrderGoodList = ThreadLocalContext.get("orderGoodList");

        return exgOrderGoodList;

    }

    private List<ExgOrderGoodDto> convertAllocationResult(Integer afterSaleGoodId,
                                                          List<StockAllocationResult> allocationResultList) {

        if(CollectionUtils.isEmpty(allocationResultList)){
            return null;
        }

        return allocationResultList
                .stream()
                .map(allocationResult -> {
                    ExgOrderGoodDto exgOrderGoodDto = new ExgOrderGoodDto();
                    exgOrderGoodDto.setSKU(allocationResult.getSku());
                    exgOrderGoodDto.setQty(BigDecimal.valueOf(allocationResult.getNum()));
                    exgOrderGoodDto.setD_EDI_07(afterSaleGoodId + StringUtils.EMPTY);
                    //采购单售后传值*
                    exgOrderGoodDto.setLotAtt07("*");
                    exgOrderGoodDto.setLotAtt08(LogicalEnum.getLogicalWarehouseCode(allocationResult.getLogicalWarehouseId()));
                    return exgOrderGoodDto;
                })
                .collect(Collectors.toList());
    }
}
