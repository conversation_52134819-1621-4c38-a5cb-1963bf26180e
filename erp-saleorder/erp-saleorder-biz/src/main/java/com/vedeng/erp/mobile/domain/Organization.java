package com.vedeng.erp.mobile.domain;

import lombok.Data;

import java.util.List;

@Data
public class Organization {
    private Integer orgId;

    private Integer companyId;

    private Integer parentId;

    private String orgName;

    private Integer level;

    private Long addTime;

    private Integer creator;

    private Long modTime;

    private Integer updater;

    private List<Organization> organizations;

    private String orgNames;

    private Integer type;//部门类型

    private String typeName;//部门类型名称
    private String isDeleted;//是否禁用
    private String deletedFlag;

}