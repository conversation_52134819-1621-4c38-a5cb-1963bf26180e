package com.vedeng.common.trace.track;

import com.vedeng.common.trace.data.TrackParamsData;
import com.vedeng.common.trace.enums.EventTrackingEnum;

import java.util.Arrays;
import java.util.List;

/**
 * 埋点接口
 * @ClassName:  TrackStrategy
 * @author: <PERSON>.yang
 * @date:   2024年6月3日 下午3:31:56
 * @Copyright:
 */
public interface TrackStrategy {

    /**
     * 返回指定的枚举，提供给工厂，根据业务需求进行设置
     * @return
     */
    EventTrackingEnum getTrackStrategy();

    /**
     * 进行埋点操作
     * @param trackParamsData
     */
    void track(TrackParamsData trackParamsData);
}
