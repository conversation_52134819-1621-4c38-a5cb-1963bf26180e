package com.wms.service.processor.input;

import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.order.model.Saleorder;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.constant.WmsLogicalOperateTypeEnum;
import com.wms.dto.InputOrderDto;
import com.wms.dto.InputOrderGoodsDto;
import com.wms.dto.StockCalculateDto;
import com.wms.service.processor.AbstractInputOrderProcessor;
import com.wms.service.stockcalculate.InputOrderAuditPassCaculateImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName AfterSaleInPutProcessor.java
 * @Description TODO 销售售后入库处理器
 * @createTime 2020年08月19日 20:01:00
 */
@Service
public class AfterSaleInPutProcessor extends AbstractInputOrderProcessor {
    @Autowired
    @Qualifier("inputOrderAuditPassCaculateImpl")
    private InputOrderAuditPassCaculateImpl stockinfoCaculateInterface;

    @Autowired
    private AfterSalesMapper afterSalesMapper;

    @Resource
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Override
    protected void updateOrderData(InputOrderDto requestBean) throws Exception {
        logger.info("销售售后单入库 info:"+requestBean.toString());
        String afterNo = requestBean.getASNReference1();
        List<InputOrderGoodsDto> details = requestBean.getDetails();

        AfterSales afterSales = afterSalesMapper.getAfterSalesByNo(afterNo);
        AfterSalesGoods afterGoods = new AfterSalesGoods();
        afterGoods.setAfterSalesId(afterSales.getAfterSalesId());

        Map<Integer, AfterSalesGoodsVo> afterSalesGoodsVoMap = afterSalesGoodsMapper.getAftersalesGoodsList(afterGoods)
                .stream()
                .collect(Collectors.toMap(AfterSalesGoodsVo::getAfterSalesGoodsId, item -> item));

        for (InputOrderGoodsDto detail : details) {
            String userDefine1 = detail.getUserDefine1();
            if(StringUtil.isBlank(userDefine1)){
                throw new Exception("WMS回传数据订单商品id为空  ERP单号"+afterNo+",WMS单号"+requestBean.getASNNO());
            }
            AfterSalesGoodsVo afterSalesGoodsVo = afterSalesGoodsVoMap.get(Integer.valueOf(userDefine1));
            AfterSalesGoods updateGoods = new AfterSalesGoods();
            updateGoods.setAfterSalesGoodsId(afterSalesGoodsVo.getAfterSalesGoodsId());
            updateGoods.setArrivalNum(afterSalesGoodsVo.getArrivalNum() + detail.getReceivedQty().intValue());
            updateGoods.setArrivalStatus(afterSalesGoodsVo.getRknum() <= updateGoods.getArrivalNum() ? 2 : 1);
            updateGoods.setFactoryCode(detail.getLotAtt04());
            updateGoods.setGoodCreateTime(DateUtil.convertLong(detail.getLotAtt01(),"yyyy-MM-dd"));
            updateGoods.setGoodVaildTime(DateUtil.convertLong(detail.getLotAtt02(),"yyyy-MM-dd"));
            afterSalesGoodsMapper.updateByPrimaryKeySelective(updateGoods);
        }
    }


    @Override
    protected int getOperateType(InputOrderDto orderDto) {
        if(WmsInterfaceOrderType.EXCHANG_SALEORDER.equals(orderDto.getASNType())){
            return StockOperateTypeConst.ORDER_WAREHOUSE_CHANGE_IN;
        }else if( WmsInterfaceOrderType.INPUT_SALE_RETURN.equals(orderDto.getASNType())){
            return StockOperateTypeConst.ORDER_WAREHOUSE_BACK_IN;
        }
        return 0;
    }

    @Override
    protected int getWmsLogicalOperateType(InputOrderDto orderDto) {
        if(WmsInterfaceOrderType.EXCHANG_SALEORDER.equals(orderDto.getASNType())){
          return WmsLogicalOperateTypeEnum.AFTER_SALEORDER_TYPE.getOperateTypeCode();
        }else if( WmsInterfaceOrderType.INPUT_SALE_RETURN.equals(orderDto.getASNType())){
            return  WmsLogicalOperateTypeEnum.AFTER_SALEORDER_TYPE.getOperateTypeCode();
        }
        return 0;
    }

    @Override
    protected int getRelateId(InputOrderDto orderDto, InputOrderGoodsDto goodsDto) {
        return Integer.valueOf(goodsDto.getUserDefine1());
    }

    @Override
    protected List<WarehouseDto> getWarehouseStockList(List<StockCalculateDto> stockCalculateList) {
        return stockinfoCaculateInterface.calculateStockInfo(stockCalculateList);
    }

    @Override
    protected int getBarcodeType(InputOrderDto orderDto, InputOrderGoodsDto goodsDto) {
        return 2;
    }

    /**
     * 自定义的相关处理
     * @param requestBean
     */
    @Override
    protected void customHandle(InputOrderDto requestBean) {
        String afterNo = requestBean.getASNReference1();

        AfterSales afterSales = afterSalesMapper.getAfterSalesByNo(afterNo);
        Saleorder saleorder = new Saleorder();
        saleorder.setSaleorderId(afterSales.getOrderId());
        warehouseStockService.updateOccupyStockService(saleorder,0);
    }

    @Override
    protected void commonValidator(InputOrderDto requestBean) throws Exception {
        if(!(WmsInterfaceOrderType.EXCHANG_SALEORDER.equals(requestBean.getASNType())
                || WmsInterfaceOrderType.INPUT_SALE_RETURN.equals(requestBean.getASNType()) )){
            throw new Exception("入库单:"+requestBean.getASNReference1()+"的类型错误!");
        }
    }
}
