package com.vedeng.erp.finance.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/6 11:34
 **/
@Setter
@Getter
public class BankBillQueryDto {


    /**
     * 银行标示1建设银行2南京银行3中国银行4支付宝5微信
     */
    private Integer bankTag;

    /**
     * 借贷标志(0-借 转出 ,1-贷 转入)
     */
    private Integer flag1;


    /**
     * 状态 是否忽略 0否 1是
     */
    private Integer status;


    /**
     * 匹配金额
     */
    private BigDecimal matchedAmount;


    private Date beginTime;

    private Date endTime;

    /**
     * 备注
     */
    private String message;

}
