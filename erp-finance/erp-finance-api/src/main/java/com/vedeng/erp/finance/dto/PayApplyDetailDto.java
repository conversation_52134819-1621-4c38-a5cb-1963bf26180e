package com.vedeng.erp.finance.dto;

import lombok.*;

import java.math.BigDecimal;

/**
 * @description 付款申请详情
 * <AUTHOR>
 * @date 2022/11/4 15:43
 **/
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PayApplyDetailDto {
    
    private Integer payApplyDetailId;

    private Integer payApplyId;

    /**
    * 订单详情ID
    */
    private Integer detailgoodsId;

    private BigDecimal price;

    private BigDecimal num;

    private BigDecimal totalAmount;
}