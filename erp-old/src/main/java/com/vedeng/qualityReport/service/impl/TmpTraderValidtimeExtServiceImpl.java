package com.vedeng.qualityReport.service.impl;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.qualityReport.dao.TmpTraderValidtimeExtMapper;
import com.vedeng.qualityReport.model.TmpTraderValidtimeExtDo;
import com.vedeng.qualityReport.service.TmpTraderValidtimeExtService;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.model.Trader;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


/**
 * trader审核时间同步服务
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2021/1/7 15:11.
 * @author: <PERSON><PERSON>.
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class TmpTraderValidtimeExtServiceImpl implements TmpTraderValidtimeExtService {

    @Autowired
    TmpTraderValidtimeExtMapper tmpTraderVaildtimeExtMapper;

    @Autowired
    TraderMapper traderMapper;

    private static final Logger LOGGER = LoggerFactory.getLogger(TmpTraderValidtimeExtServiceImpl.class);


    @Override
    public ResultInfo saveTraderValidTime() {

        List<TmpTraderValidtimeExtDo> totalTraderValidTImeList = new ArrayList<>();

        try {
            //查到所有审核通过的供应商的信息
            LOGGER.info("开始查询审核通过的供应商的审核通过时间");
            List<TmpTraderValidtimeExtDo> traderSupplierValidtimeExtDoList =  tmpTraderVaildtimeExtMapper.getAllValidTraderSupplier();
            LOGGER.info("结束查询审核通过的供应商的审核通过时间");
            if(CollectionUtils.isNotEmpty(traderSupplierValidtimeExtDoList)){
                totalTraderValidTImeList.addAll(traderSupplierValidtimeExtDoList);
            }
        } catch (Exception e) {
            LOGGER.error("查询审核通过的供应商失败，失败原因：{}",e);
            return new ResultInfo(-1,"查询审核通过的供应商失败");
        }
        //查到所有审核通过的客户信息
        try {
            LOGGER.info("开始查询审核通过的客户的审核通过时间");
            List<TmpTraderValidtimeExtDo> traderCustomerValidtimeExtDoList = tmpTraderVaildtimeExtMapper.getAllValidTraderCustomer();
            LOGGER.info("结束查询审核通过的客户的审核通过时间");
            if(CollectionUtils.isNotEmpty(traderCustomerValidtimeExtDoList)){
                totalTraderValidTImeList.addAll(traderCustomerValidtimeExtDoList);
            }
        } catch (Exception e) {
            LOGGER.error("查询审核通过的客户失败，失败原因：{}",e);
            return new ResultInfo(-1,"查询审核通过的客户失败");
        }
        //更新
        try {
            if(CollectionUtils.isNotEmpty(totalTraderValidTImeList)) {
                LOGGER.info("开始更新审核时间");
                totalTraderValidTImeList.parallelStream().forEach(e -> {
                            traderMapper.UpdateLastValidTimeByIdAndTime(e.getTraderId(), e.getValidTime());
                        }
                );
                LOGGER.info("成功更新审核时间");
            }
        } catch (Exception e) {
            LOGGER.error("查询审核通过的客户失败，失败原因：{}",e);
            return new ResultInfo(-1,"更新审核通过时间失败");
        }

        return new ResultInfo(0,"更新成功");
    }

    @Override
    public void updateTraderCustomerValidTIme(Integer traderCustomerId) {
        try {
            LOGGER.info("开始更新审核通过的客户的审核通过时间");
            TmpTraderValidtimeExtDo traderCustomerValidTime = tmpTraderVaildtimeExtMapper.getTraderCustomerValidTime(traderCustomerId);
            if(null != traderCustomerValidTime ) {
                traderMapper.UpdateLastValidTimeByIdAndTime(traderCustomerValidTime.getTraderId(), traderCustomerValidTime.getValidTime());
            }
            LOGGER.info("结束更新审核通过的客户的审核通过时间");
        } catch (Exception e) {
            LOGGER.error("更新审核通过的客户的审核通过时间失败，失败原因：{}",e);
        }
    }

    @Override
    public void updateTraderSupplierValidTIme(Integer traderSupplierId) {
        try {
            LOGGER.info("开始更新审核通过的供应商的审核通过时间");
            TmpTraderValidtimeExtDo traderSupplierValidTime = tmpTraderVaildtimeExtMapper.getTraderSupplyValidTime(traderSupplierId);
            if(null != traderSupplierValidTime) {
                traderMapper.UpdateLastValidTimeByIdAndTime(traderSupplierValidTime.getTraderId(), traderSupplierValidTime.getValidTime());
            }
            LOGGER.info("结束更新审核通过的供应商的审核通过时间");
        } catch (Exception e) {
            LOGGER.error("更新审核通过的供应商的审核通过时间失败，失败原因：{}",e);
        }
    }


}
