package com.vedeng.billsync.task.model.entity;

import lombok.Data;
import java.util.Date;

@Data
public class TmpAlipayBillDataExtPo {
    /**
     * 财务流水号
     */
    private String financialTurnover;

    /**
     * 业务流水号
     */
    private String businessTurnover;

    /**
     * 商户订单号
     */
    private String orderNo;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 发生时间
     */
    private Date occurrenceTime;

    /**
     * 对方账号
     */
    private String otherAccount;

    /**
     * 收入金额
     */
    private String income;

    /**
     * 支出金额
     */
    private String outlay;

    /**
     * 账户余额
     */
    private String balance;

    /**
     * 交易渠道
     */
    private String tradingChannel;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 备注
     */
    private String remark;

}
