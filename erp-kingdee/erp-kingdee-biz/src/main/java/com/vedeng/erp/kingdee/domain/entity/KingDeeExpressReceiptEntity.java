package com.vedeng.erp.kingdee.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.*;

import java.math.BigDecimal;

/**
    * 金蝶快递签收表
    */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(name = "KING_DEE_EXPRESS_RECEIPT")
public class KingDeeExpressReceiptEntity extends BaseEntity {
    /**
    * 主键ID
    */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
    * 单据内码
    */
    private String fid;

    /**
    * 单据编号
    */
    private String fBillNo;

    /**
    * 发货组织
    */
    private String fQzokOrgId;

    /**
    * 原始订单单号
    */
    private String fQzokYsddh;

    /**
    * 归属业务单号
    */
    private String fQzokGsywdh;

    /**
    * 出入库单号
    */
    private String fQzokCrkdh;

    /**
    * 快递号
    */
    private String fQzokKdh;

    /**
    * 业务类型
    */
    private String fQzokYwlx;

    /**
    * 签收时间
    */
    private String fQzokQssj;

    /**
    * 物流公司
    */
    private String fQzokWlgs;

    /**
    * 物料编码
    */
    private String fQzokWlbm;

    /**
    * 物料序列号
    */
    private String fQzokXlh;

    /**
    * 批次号
    */
    private String fQzokPch;

    /**
    * 发货数量
    */
    private BigDecimal fQzokFhsl;

    /**
    * 收件人
    */
    private String fQzokSjr;

    /**
    * 电话
    */
    private String fQzokDh;

    /**
    * 地址
    */
    private String fQzokDz;

    /**
    * 贝登单据编号
    */
    private String fQzokBddjbh;

    /**
    * 是否已经删除
    */
    private String fQzokSfsc;

    /**
    * 是否计入成本
    */
    private String fQzokSfjrcb;

    /**
     * 是否赠品(默认0，赠品 1)
     */
    private Integer fQzokSfzp;

}