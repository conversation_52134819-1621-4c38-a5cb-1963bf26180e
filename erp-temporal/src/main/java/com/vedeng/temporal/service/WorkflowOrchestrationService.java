package com.vedeng.temporal.service;

import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;

/**
 * 工作流编排服务接口
 * 专门负责Temporal工作流的启动、管理和控制
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-03
 */
public interface WorkflowOrchestrationService {

    /**
     * 启动多公司业务工作流
     * 通过Temporal启动工作流
     *
     * @param request 业务请求对象
     * @return 工作流启动结果
     */
    CompanyBusinessResponse startMultiCompanyWorkflow(CompanyBusinessRequest request);

}

