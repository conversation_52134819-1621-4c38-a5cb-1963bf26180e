<%@ tag language="java" pageEncoding="UTF-8"%>
<%@attribute name="saleorderDataInfo" type="java.util.Map" required="true" %>
<%@attribute name="saleInvoiceApplyList" type="java.util.List" required="true" %>
<%@attribute name="invoiceTypes" type="java.util.List" required="true" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>


<div class="parts" liname="开票申请" id="开票申请">
    <div class="title-container title-container-yellow">
        <div class="table-title nobor">
            开票申请
        </div>
    </div>
    <table class="table">

        <thead>
        <tr>
            <!-- add by Tomcat.Hui 2019/11/21 11:48 .Desc: VDERP-1325 分批开票 开票申请模块 start. -->
            <th>申请人</th>
            <th>申请时间</th>
            <th>申请方式</th>
            <th>是否在线催办</th>
            <th>是否提前开票</th>
            <th>票面备注</th>
            <th>开票留言</th>
            <th>提前申请原因</th>
            <th>操作事项</th>
            <th>备注</th>
            <!-- add by Tomcat.Hui 2019/11/21 11:48 .Desc: VDERP-1325 分批开票 开票申请模块 end. -->
            <!-- add by Tomcat.Hui 2019/11/21 11:48 .Desc: VDERP-1325 分批开票 查看已申请发票详情信息 start. -->
            <th>操作</th>
            <!-- add by Tomcat.Hui 2019/11/21 11:48 .Desc: VDERP-1325 分批开票 查看已申请发票详情信息 start. -->
        </tr>
        </thead>
        <tbody>
        <c:set var="invoiceApplyAmount" value="0"></c:set>
        <c:forEach var="list" items="${saleInvoiceApplyList}" varStatus="num">
            <c:if test="${list.advanceValidStatus ne 2 and list.validStatus ne 2}">
                <c:forEach var="detail" items="${list.invoiceApplyDetails}" varStatus="num">
                    <c:set var="invoiceApplyAmount" value="${invoiceApplyAmount + detail.totalAmount}"></c:set>
                </c:forEach>
            </c:if>
            <tr>
                <td>
                    <c:choose>
                        <c:when test="${list.applyMethod eq 3}">
                            ${list.signerName}
                        </c:when>
                        <c:otherwise>
                            ${list.creatorNm}
                            <c:if test="${list.creator eq 0}">
                                自动申请
                            </c:if>
                        </c:otherwise>
                    </c:choose>
                </td>
                <td><date:date value="${list.addTime}" format="yyyy.MM.dd HH:mm:ss"/></td>
                <!-- add by Tomcat.Hui 2019/11/21 11:48 .Desc: VDERP-1325 分批开票 开票申请模块 start. -->
                <td>
                    <c:if test="${list.applyMethod eq 0}">手动申请触发</c:if>
                    <c:if test="${list.applyMethod eq 1}">定时任务触发</c:if>
                    <!-- add by Tomcat.Hui 2019/12/30 16:29 .Desc: VDERP-1039 票货同行 start. -->
                    <c:if test="${list.applyMethod eq 2}">票货同行物流部申请</c:if>
                    <!-- add by Tomcat.Hui 2019/12/30 16:29 .Desc: VDERP-1039 票货同行 end. -->
                    <c:if test="${list.applyMethod eq 3}">客户在线申请开票</c:if>
                </td>
                <td>
                    <c:choose>
                        <c:when test="${list.urage eq 0}">否</c:when>
                        <c:when test="${list.urage eq null}">否</c:when>
                        <c:otherwise>是</c:otherwise>
                    </c:choose>
                </td>
                <!-- add by Tomcat.Hui 2019/11/21 11:48 .Desc: VDERP-1325 分批开票 开票申请模块 ends. -->
                <td>
                    <c:choose>
                        <c:when test="${list.isAdvance eq 0}">否</c:when>
                        <c:otherwise>是</c:otherwise>
                    </c:choose>
                </td>
                <%--票面备注--%>
                <td>
                    ${list.comments}
                </td>
                <%--开票留言--%>
                <td>
                    ${list.invoiceMessage}
                </td>
                <%--提前申请原因--%>
                <td>
                    ${list.advanceValidReason}
                </td>
                <td>
                    <c:choose>
                        <c:when test="${list.isAdvance eq 1}">
                            <c:choose>
                                <c:when test="${list.advanceValidStatus eq 0}">提前开票审核中</c:when>
                                <c:when test="${list.advanceValidStatus eq 1}">提前开票审核通过<br/>
                                    <c:choose>
                                        <c:when test="${list.validStatus eq 0}">开票申请审核中</c:when>
                                        <c:when test="${list.validStatus eq 1}">开票申请审核通过</c:when>
                                        <c:when test="${list.validStatus eq 2}">开票申请审核不通过</c:when>
                                    </c:choose>
                                </c:when>
                                <c:when test="${list.advanceValidStatus eq 2}">提前开票审核不通过</c:when>
                            </c:choose>
                        </c:when>
                        <c:otherwise>
                            <c:choose>
                                <c:when test="${list.validStatus eq 0}">开票申请审核中</c:when>
                                <c:when test="${list.validStatus eq 1}">开票申请审核通过</c:when>
                                <c:when test="${list.validStatus eq 2}">开票申请审核不通过</c:when>
                            </c:choose>
                        </c:otherwise>
                    </c:choose>
                </td>
                <td>
                    <c:choose>
                        <c:when test="${list.isAdvance eq 1}">
                            ${list.advanceValidComments}
                            <c:if test="${list.yyValidStatus ne 0}">
                                <br/>${list.yyValidComments}
                            </c:if>
                            <c:if test="${list.validStatus ne 0}">
                                <br/>${list.validComments}
                            </c:if>
                        </c:when>
                        <c:otherwise>
                            <c:if test="${list.yyValidStatus ne 0}">
                                <br/>${list.yyValidComments}
                            </c:if>
                            <c:if test="${list.validStatus ne 0}">
                                <br/>${list.validComments}
                            </c:if>
                        </c:otherwise>
                    </c:choose>
                </td>
                <!-- add by Tomcat.Hui 2019/11/21 11:48 .Desc: VDERP-1325 分批开票 查看已申请发票详情信息 start. -->
                <%--查看开票申请--%>
                <td>
                    <button type="button" class="bt-bg-style bg-light-orange bt-small  pop-new-data mr10" layerParams='{"width":"80%","height":"600px","title":"查看开票申请","link":"/invoice/invoiceApply/invoiceApplyDetail.do?invoiceApplyId=${list.invoiceApplyId}"}' >查看开票申请</button>
                </td>
                <!-- add by Tomcat.Hui 2019/11/21 11:48 .Desc: VDERP-1325 分批开票 查看已申请发票详情信息 start. -->
            </tr>
        </c:forEach>
        <!-- add by Tomcat.Hui 2019/11/21 11:48 .Desc: VDERP-1325 分批开票 开票申请模块 start. -->
        <tr>
            <td colspan="11" style="text-align: left; background: #eaf2fd;">
                已申请开票金额：<fmt:formatNumber type="number" value="${invoiceApplyAmount}" pattern="0.00" maxFractionDigits="2" />
                &nbsp;&nbsp;&nbsp;&nbsp;
                <span style="color:red">未申请开票金额：<fmt:formatNumber type="number" value="${saleorderDataInfo['realAmount'] - invoiceApplyAmount}"
                                                                         pattern="0.00" maxFractionDigits="2" /></span>
            </td>
        </tr>
        <!-- add by Tomcat.Hui 2019/11/21 11:48 .Desc: VDERP-1325 分批开票 开票申请模块 end. -->
        <c:if test="${empty saleInvoiceApplyList}">
            <tr>
                <td colspan="11">暂无开票申请。</td>
            </tr>
        </c:if>
        </tbody>
    </table>
</div>