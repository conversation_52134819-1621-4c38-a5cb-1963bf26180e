<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.trader.dao.AccountMapper">


    <select id="getAccount" resultType="com.newtask.model.YxgAccount">
        SELECT A.*, AC.TRADER_ID AS TRADER_ID FROM T_ACCOUNT A JOIN T_ACCOUNT_COMPANY AC ON A.ACCOUNT_ID = AC.ACCOUNT_ID WHERE A.ACCOUNT_ID &gt; #{start} limit 1
    </select>
</mapper>