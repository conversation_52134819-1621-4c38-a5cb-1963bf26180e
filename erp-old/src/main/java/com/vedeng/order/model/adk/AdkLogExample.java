package com.vedeng.order.model.adk;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AdkLogExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_ADK_LOG
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_ADK_LOG
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_ADK_LOG
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_ADK_LOG
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public AdkLogExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_ADK_LOG
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_ADK_LOG
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_ADK_LOG
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_ADK_LOG
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_ADK_LOG
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_ADK_LOG
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_ADK_LOG
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_ADK_LOG
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_ADK_LOG
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_ADK_LOG
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_ADK_LOG
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andAdkLogIdIsNull() {
            addCriterion("adk_log_id is null");
            return (Criteria) this;
        }

        public Criteria andAdkLogIdIsNotNull() {
            addCriterion("adk_log_id is not null");
            return (Criteria) this;
        }

        public Criteria andAdkLogIdEqualTo(Integer value) {
            addCriterion("adk_log_id =", value, "adkLogId");
            return (Criteria) this;
        }

        public Criteria andAdkLogIdNotEqualTo(Integer value) {
            addCriterion("adk_log_id <>", value, "adkLogId");
            return (Criteria) this;
        }

        public Criteria andAdkLogIdGreaterThan(Integer value) {
            addCriterion("adk_log_id >", value, "adkLogId");
            return (Criteria) this;
        }

        public Criteria andAdkLogIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("adk_log_id >=", value, "adkLogId");
            return (Criteria) this;
        }

        public Criteria andAdkLogIdLessThan(Integer value) {
            addCriterion("adk_log_id <", value, "adkLogId");
            return (Criteria) this;
        }

        public Criteria andAdkLogIdLessThanOrEqualTo(Integer value) {
            addCriterion("adk_log_id <=", value, "adkLogId");
            return (Criteria) this;
        }

        public Criteria andAdkLogIdIn(List<Integer> values) {
            addCriterion("adk_log_id in", values, "adkLogId");
            return (Criteria) this;
        }

        public Criteria andAdkLogIdNotIn(List<Integer> values) {
            addCriterion("adk_log_id not in", values, "adkLogId");
            return (Criteria) this;
        }

        public Criteria andAdkLogIdBetween(Integer value1, Integer value2) {
            addCriterion("adk_log_id between", value1, value2, "adkLogId");
            return (Criteria) this;
        }

        public Criteria andAdkLogIdNotBetween(Integer value1, Integer value2) {
            addCriterion("adk_log_id not between", value1, value2, "adkLogId");
            return (Criteria) this;
        }

        public Criteria andAdkLogTimestampIsNull() {
            addCriterion("adk_log_timestamp is null");
            return (Criteria) this;
        }

        public Criteria andAdkLogTimestampIsNotNull() {
            addCriterion("adk_log_timestamp is not null");
            return (Criteria) this;
        }

        public Criteria andAdkLogTimestampEqualTo(Long value) {
            addCriterion("adk_log_timestamp =", value, "adkLogTimestamp");
            return (Criteria) this;
        }

        public Criteria andAdkLogTimestampNotEqualTo(Long value) {
            addCriterion("adk_log_timestamp <>", value, "adkLogTimestamp");
            return (Criteria) this;
        }

        public Criteria andAdkLogTimestampGreaterThan(Long value) {
            addCriterion("adk_log_timestamp >", value, "adkLogTimestamp");
            return (Criteria) this;
        }

        public Criteria andAdkLogTimestampGreaterThanOrEqualTo(Long value) {
            addCriterion("adk_log_timestamp >=", value, "adkLogTimestamp");
            return (Criteria) this;
        }

        public Criteria andAdkLogTimestampLessThan(Long value) {
            addCriterion("adk_log_timestamp <", value, "adkLogTimestamp");
            return (Criteria) this;
        }

        public Criteria andAdkLogTimestampLessThanOrEqualTo(Long value) {
            addCriterion("adk_log_timestamp <=", value, "adkLogTimestamp");
            return (Criteria) this;
        }

        public Criteria andAdkLogTimestampIn(List<Long> values) {
            addCriterion("adk_log_timestamp in", values, "adkLogTimestamp");
            return (Criteria) this;
        }

        public Criteria andAdkLogTimestampNotIn(List<Long> values) {
            addCriterion("adk_log_timestamp not in", values, "adkLogTimestamp");
            return (Criteria) this;
        }

        public Criteria andAdkLogTimestampBetween(Long value1, Long value2) {
            addCriterion("adk_log_timestamp between", value1, value2, "adkLogTimestamp");
            return (Criteria) this;
        }

        public Criteria andAdkLogTimestampNotBetween(Long value1, Long value2) {
            addCriterion("adk_log_timestamp not between", value1, value2, "adkLogTimestamp");
            return (Criteria) this;
        }

        public Criteria andAdkLogAkIsNull() {
            addCriterion("adk_log_ak is null");
            return (Criteria) this;
        }

        public Criteria andAdkLogAkIsNotNull() {
            addCriterion("adk_log_ak is not null");
            return (Criteria) this;
        }

        public Criteria andAdkLogAkEqualTo(String value) {
            addCriterion("adk_log_ak =", value, "adkLogAk");
            return (Criteria) this;
        }

        public Criteria andAdkLogAkNotEqualTo(String value) {
            addCriterion("adk_log_ak <>", value, "adkLogAk");
            return (Criteria) this;
        }

        public Criteria andAdkLogAkGreaterThan(String value) {
            addCriterion("adk_log_ak >", value, "adkLogAk");
            return (Criteria) this;
        }

        public Criteria andAdkLogAkGreaterThanOrEqualTo(String value) {
            addCriterion("adk_log_ak >=", value, "adkLogAk");
            return (Criteria) this;
        }

        public Criteria andAdkLogAkLessThan(String value) {
            addCriterion("adk_log_ak <", value, "adkLogAk");
            return (Criteria) this;
        }

        public Criteria andAdkLogAkLessThanOrEqualTo(String value) {
            addCriterion("adk_log_ak <=", value, "adkLogAk");
            return (Criteria) this;
        }

        public Criteria andAdkLogAkLike(String value) {
            addCriterion("adk_log_ak like", value, "adkLogAk");
            return (Criteria) this;
        }

        public Criteria andAdkLogAkNotLike(String value) {
            addCriterion("adk_log_ak not like", value, "adkLogAk");
            return (Criteria) this;
        }

        public Criteria andAdkLogAkIn(List<String> values) {
            addCriterion("adk_log_ak in", values, "adkLogAk");
            return (Criteria) this;
        }

        public Criteria andAdkLogAkNotIn(List<String> values) {
            addCriterion("adk_log_ak not in", values, "adkLogAk");
            return (Criteria) this;
        }

        public Criteria andAdkLogAkBetween(String value1, String value2) {
            addCriterion("adk_log_ak between", value1, value2, "adkLogAk");
            return (Criteria) this;
        }

        public Criteria andAdkLogAkNotBetween(String value1, String value2) {
            addCriterion("adk_log_ak not between", value1, value2, "adkLogAk");
            return (Criteria) this;
        }

        public Criteria andAdkLogSigIsNull() {
            addCriterion("adk_log_sig is null");
            return (Criteria) this;
        }

        public Criteria andAdkLogSigIsNotNull() {
            addCriterion("adk_log_sig is not null");
            return (Criteria) this;
        }

        public Criteria andAdkLogSigEqualTo(String value) {
            addCriterion("adk_log_sig =", value, "adkLogSig");
            return (Criteria) this;
        }

        public Criteria andAdkLogSigNotEqualTo(String value) {
            addCriterion("adk_log_sig <>", value, "adkLogSig");
            return (Criteria) this;
        }

        public Criteria andAdkLogSigGreaterThan(String value) {
            addCriterion("adk_log_sig >", value, "adkLogSig");
            return (Criteria) this;
        }

        public Criteria andAdkLogSigGreaterThanOrEqualTo(String value) {
            addCriterion("adk_log_sig >=", value, "adkLogSig");
            return (Criteria) this;
        }

        public Criteria andAdkLogSigLessThan(String value) {
            addCriterion("adk_log_sig <", value, "adkLogSig");
            return (Criteria) this;
        }

        public Criteria andAdkLogSigLessThanOrEqualTo(String value) {
            addCriterion("adk_log_sig <=", value, "adkLogSig");
            return (Criteria) this;
        }

        public Criteria andAdkLogSigLike(String value) {
            addCriterion("adk_log_sig like", value, "adkLogSig");
            return (Criteria) this;
        }

        public Criteria andAdkLogSigNotLike(String value) {
            addCriterion("adk_log_sig not like", value, "adkLogSig");
            return (Criteria) this;
        }

        public Criteria andAdkLogSigIn(List<String> values) {
            addCriterion("adk_log_sig in", values, "adkLogSig");
            return (Criteria) this;
        }

        public Criteria andAdkLogSigNotIn(List<String> values) {
            addCriterion("adk_log_sig not in", values, "adkLogSig");
            return (Criteria) this;
        }

        public Criteria andAdkLogSigBetween(String value1, String value2) {
            addCriterion("adk_log_sig between", value1, value2, "adkLogSig");
            return (Criteria) this;
        }

        public Criteria andAdkLogSigNotBetween(String value1, String value2) {
            addCriterion("adk_log_sig not between", value1, value2, "adkLogSig");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andAdkLogStatusIsNull() {
            addCriterion("adk_log_status is null");
            return (Criteria) this;
        }

        public Criteria andAdkLogStatusIsNotNull() {
            addCriterion("adk_log_status is not null");
            return (Criteria) this;
        }

        public Criteria andAdkLogStatusEqualTo(String value) {
            addCriterion("adk_log_status =", value, "adkLogStatus");
            return (Criteria) this;
        }

        public Criteria andAdkLogStatusNotEqualTo(String value) {
            addCriterion("adk_log_status <>", value, "adkLogStatus");
            return (Criteria) this;
        }

        public Criteria andAdkLogStatusGreaterThan(String value) {
            addCriterion("adk_log_status >", value, "adkLogStatus");
            return (Criteria) this;
        }

        public Criteria andAdkLogStatusGreaterThanOrEqualTo(String value) {
            addCriterion("adk_log_status >=", value, "adkLogStatus");
            return (Criteria) this;
        }

        public Criteria andAdkLogStatusLessThan(String value) {
            addCriterion("adk_log_status <", value, "adkLogStatus");
            return (Criteria) this;
        }

        public Criteria andAdkLogStatusLessThanOrEqualTo(String value) {
            addCriterion("adk_log_status <=", value, "adkLogStatus");
            return (Criteria) this;
        }

        public Criteria andAdkLogStatusLike(String value) {
            addCriterion("adk_log_status like", value, "adkLogStatus");
            return (Criteria) this;
        }

        public Criteria andAdkLogStatusNotLike(String value) {
            addCriterion("adk_log_status not like", value, "adkLogStatus");
            return (Criteria) this;
        }

        public Criteria andAdkLogStatusIn(List<String> values) {
            addCriterion("adk_log_status in", values, "adkLogStatus");
            return (Criteria) this;
        }

        public Criteria andAdkLogStatusNotIn(List<String> values) {
            addCriterion("adk_log_status not in", values, "adkLogStatus");
            return (Criteria) this;
        }

        public Criteria andAdkLogStatusBetween(String value1, String value2) {
            addCriterion("adk_log_status between", value1, value2, "adkLogStatus");
            return (Criteria) this;
        }

        public Criteria andAdkLogStatusNotBetween(String value1, String value2) {
            addCriterion("adk_log_status not between", value1, value2, "adkLogStatus");
            return (Criteria) this;
        }

        public Criteria andAdkLogMsgIsNull() {
            addCriterion("adk_log_msg is null");
            return (Criteria) this;
        }

        public Criteria andAdkLogMsgIsNotNull() {
            addCriterion("adk_log_msg is not null");
            return (Criteria) this;
        }

        public Criteria andAdkLogMsgEqualTo(String value) {
            addCriterion("adk_log_msg =", value, "adkLogMsg");
            return (Criteria) this;
        }

        public Criteria andAdkLogMsgNotEqualTo(String value) {
            addCriterion("adk_log_msg <>", value, "adkLogMsg");
            return (Criteria) this;
        }

        public Criteria andAdkLogMsgGreaterThan(String value) {
            addCriterion("adk_log_msg >", value, "adkLogMsg");
            return (Criteria) this;
        }

        public Criteria andAdkLogMsgGreaterThanOrEqualTo(String value) {
            addCriterion("adk_log_msg >=", value, "adkLogMsg");
            return (Criteria) this;
        }

        public Criteria andAdkLogMsgLessThan(String value) {
            addCriterion("adk_log_msg <", value, "adkLogMsg");
            return (Criteria) this;
        }

        public Criteria andAdkLogMsgLessThanOrEqualTo(String value) {
            addCriterion("adk_log_msg <=", value, "adkLogMsg");
            return (Criteria) this;
        }

        public Criteria andAdkLogMsgLike(String value) {
            addCriterion("adk_log_msg like", value, "adkLogMsg");
            return (Criteria) this;
        }

        public Criteria andAdkLogMsgNotLike(String value) {
            addCriterion("adk_log_msg not like", value, "adkLogMsg");
            return (Criteria) this;
        }

        public Criteria andAdkLogMsgIn(List<String> values) {
            addCriterion("adk_log_msg in", values, "adkLogMsg");
            return (Criteria) this;
        }

        public Criteria andAdkLogMsgNotIn(List<String> values) {
            addCriterion("adk_log_msg not in", values, "adkLogMsg");
            return (Criteria) this;
        }

        public Criteria andAdkLogMsgBetween(String value1, String value2) {
            addCriterion("adk_log_msg between", value1, value2, "adkLogMsg");
            return (Criteria) this;
        }

        public Criteria andAdkLogMsgNotBetween(String value1, String value2) {
            addCriterion("adk_log_msg not between", value1, value2, "adkLogMsg");
            return (Criteria) this;
        }

        public Criteria andAdkLogTypeIsNull() {
            addCriterion("adk_log_type is null");
            return (Criteria) this;
        }

        public Criteria andAdkLogTypeIsNotNull() {
            addCriterion("adk_log_type is not null");
            return (Criteria) this;
        }

        public Criteria andAdkLogTypeEqualTo(String value) {
            addCriterion("adk_log_type =", value, "adkLogType");
            return (Criteria) this;
        }

        public Criteria andAdkLogTypeNotEqualTo(String value) {
            addCriterion("adk_log_type <>", value, "adkLogType");
            return (Criteria) this;
        }

        public Criteria andAdkLogTypeGreaterThan(String value) {
            addCriterion("adk_log_type >", value, "adkLogType");
            return (Criteria) this;
        }

        public Criteria andAdkLogTypeGreaterThanOrEqualTo(String value) {
            addCriterion("adk_log_type >=", value, "adkLogType");
            return (Criteria) this;
        }

        public Criteria andAdkLogTypeLessThan(String value) {
            addCriterion("adk_log_type <", value, "adkLogType");
            return (Criteria) this;
        }

        public Criteria andAdkLogTypeLessThanOrEqualTo(String value) {
            addCriterion("adk_log_type <=", value, "adkLogType");
            return (Criteria) this;
        }

        public Criteria andAdkLogTypeLike(String value) {
            addCriterion("adk_log_type like", value, "adkLogType");
            return (Criteria) this;
        }

        public Criteria andAdkLogTypeNotLike(String value) {
            addCriterion("adk_log_type not like", value, "adkLogType");
            return (Criteria) this;
        }

        public Criteria andAdkLogTypeIn(List<String> values) {
            addCriterion("adk_log_type in", values, "adkLogType");
            return (Criteria) this;
        }

        public Criteria andAdkLogTypeNotIn(List<String> values) {
            addCriterion("adk_log_type not in", values, "adkLogType");
            return (Criteria) this;
        }

        public Criteria andAdkLogTypeBetween(String value1, String value2) {
            addCriterion("adk_log_type between", value1, value2, "adkLogType");
            return (Criteria) this;
        }

        public Criteria andAdkLogTypeNotBetween(String value1, String value2) {
            addCriterion("adk_log_type not between", value1, value2, "adkLogType");
            return (Criteria) this;
        }

        public Criteria andRequestIdIsNull() {
            addCriterion("request_id is null");
            return (Criteria) this;
        }

        public Criteria andRequestIdIsNotNull() {
            addCriterion("request_id is not null");
            return (Criteria) this;
        }

        public Criteria andRequestIdEqualTo(String value) {
            addCriterion("request_id =", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdNotEqualTo(String value) {
            addCriterion("request_id <>", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdGreaterThan(String value) {
            addCriterion("request_id >", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdGreaterThanOrEqualTo(String value) {
            addCriterion("request_id >=", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdLessThan(String value) {
            addCriterion("request_id <", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdLessThanOrEqualTo(String value) {
            addCriterion("request_id <=", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdLike(String value) {
            addCriterion("request_id like", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdNotLike(String value) {
            addCriterion("request_id not like", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdIn(List<String> values) {
            addCriterion("request_id in", values, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdNotIn(List<String> values) {
            addCriterion("request_id not in", values, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdBetween(String value1, String value2) {
            addCriterion("request_id between", value1, value2, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdNotBetween(String value1, String value2) {
            addCriterion("request_id not between", value1, value2, "requestId");
            return (Criteria) this;
        }

        public Criteria andAdkOrderNoIsNull() {
            addCriterion("adk_order_no is null");
            return (Criteria) this;
        }

        public Criteria andAdkOrderNoIsNotNull() {
            addCriterion("adk_order_no is not null");
            return (Criteria) this;
        }

        public Criteria andAdkOrderNoEqualTo(String value) {
            addCriterion("adk_order_no =", value, "adkOrderNo");
            return (Criteria) this;
        }

        public Criteria andAdkOrderNoNotEqualTo(String value) {
            addCriterion("adk_order_no <>", value, "adkOrderNo");
            return (Criteria) this;
        }

        public Criteria andAdkOrderNoGreaterThan(String value) {
            addCriterion("adk_order_no >", value, "adkOrderNo");
            return (Criteria) this;
        }

        public Criteria andAdkOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("adk_order_no >=", value, "adkOrderNo");
            return (Criteria) this;
        }

        public Criteria andAdkOrderNoLessThan(String value) {
            addCriterion("adk_order_no <", value, "adkOrderNo");
            return (Criteria) this;
        }

        public Criteria andAdkOrderNoLessThanOrEqualTo(String value) {
            addCriterion("adk_order_no <=", value, "adkOrderNo");
            return (Criteria) this;
        }

        public Criteria andAdkOrderNoLike(String value) {
            addCriterion("adk_order_no like", value, "adkOrderNo");
            return (Criteria) this;
        }

        public Criteria andAdkOrderNoNotLike(String value) {
            addCriterion("adk_order_no not like", value, "adkOrderNo");
            return (Criteria) this;
        }

        public Criteria andAdkOrderNoIn(List<String> values) {
            addCriterion("adk_order_no in", values, "adkOrderNo");
            return (Criteria) this;
        }

        public Criteria andAdkOrderNoNotIn(List<String> values) {
            addCriterion("adk_order_no not in", values, "adkOrderNo");
            return (Criteria) this;
        }

        public Criteria andAdkOrderNoBetween(String value1, String value2) {
            addCriterion("adk_order_no between", value1, value2, "adkOrderNo");
            return (Criteria) this;
        }

        public Criteria andAdkOrderNoNotBetween(String value1, String value2) {
            addCriterion("adk_order_no not between", value1, value2, "adkOrderNo");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_ADK_LOG
     *
     * @mbg.generated do_not_delete_during_merge Sun May 05 10:38:13 CST 2019
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_ADK_LOG
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}