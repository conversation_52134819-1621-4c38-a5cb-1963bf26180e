package com.vedeng.crm.visitrecord.mapper;

import com.vedeng.crm.visitrecord.domain.dto.VisitRecordQueryDto;
import com.vedeng.crm.visitrecord.domain.entity.CrmVisitRecordEntity;
import com.vedeng.crm.visitrecord.domain.vo.UserForVisitDto;
import com.vedeng.crm.visitrecord.domain.vo.VisitRecordVo;
import com.vedeng.crm.visitrecord.domain.vo.VisitTongXingNotCardVo;
import com.vedeng.erp.system.dto.UserDto;
import org.apache.ibatis.annotations.Param;
import java.util.List;

public interface CrmVisitRecordMapper {
    
    List<VisitRecordVo> selectVisitRecordList(VisitRecordQueryDto queryDto);


    List<UserForVisitDto> selectVisitRecordCreateUserList(VisitRecordQueryDto queryDto);

    List<UserForVisitDto> selectVisitRecordVisitUserList(VisitRecordQueryDto queryDto);

    List<UserForVisitDto> selectVisitRecordTongXingUserList(VisitRecordQueryDto queryDto);


    VisitRecordVo selectVisitRecordById(@Param("id") Integer id);


    CrmVisitRecordEntity selectByPrimaryKey(@Param("id") Integer id );

    /**
     * 插入拜访记录
     */
    int insert(VisitRecordVo record);

    /**
     * 更新拜访记录
     */
    int update(VisitRecordVo record);

    /**
     * 选择性更新拜访记录
     */
    int updateSelective(VisitRecordVo record);

    /**
     * 关闭拜访记录
     */
    int updateClose(VisitRecordVo record);

    /**
     * 查询今日待拜访
     * @return
     */
    List<CrmVisitRecordEntity> selectVisitRecordListForToday();

    /**
     * 拜访人今日未打卡
     * @return
     */
    List<CrmVisitRecordEntity> selectVisitRecordListForTodayVisitorNotCard();


    /**
     * 0328 如果拜访人填写了拜访记录后，同行人未打卡时，不发此消息提醒了
     * @return
     */
    List<VisitTongXingNotCardVo> selectVisitRecordListForTodayTongXingNotCard();

    /**
     * 查询当天待拜访的记录(拜访人未打卡:状态为待拜访1或拜访中2，且拜访人是在当天无打卡记录)-防止提前打卡，并填写了拜访记录的情况
     * @return
     */
    List<CrmVisitRecordEntity> sendYestodayNotRecordReminder();


    /**
     * 根据线索商机查询具体的信息
     * @param relateNo
     * @param relateType
     * @return
     */
    List<VisitRecordVo> selectVisitRecordByRelateNo(@Param("relateNo") String relateNo,@Param("relateType") Integer relateType);

    /**
     * 根据线索商机查询具体的信息
     * @param relateId 关联ID
     * @param relateType 关联类型1线索2商机
     * @return
     */
    List<VisitRecordVo> selectVisitRecordByRelateId(@Param("relateId") Integer relateId,@Param("relateType") Integer relateType);
} 