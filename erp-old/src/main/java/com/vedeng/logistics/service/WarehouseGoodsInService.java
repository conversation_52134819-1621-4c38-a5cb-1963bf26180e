package com.vedeng.logistics.service;

import java.util.List;
import java.util.Map;

import com.vedeng.authorization.model.User;
import com.vedeng.logistics.eums.WarehouseGoodsInEnum;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.WarehouseGoodsOutIn;
import com.vedeng.logistics.model.WarehouseGoodsOutInItem;
import com.vedeng.logistics.model.outIn.OutInDetail;
import com.vedeng.orderstream.aftersales.model.AfterSalesDirectInfo;

/**
 * 入库服务类
 * @ClassName:  WarehouseGoodsInService   
 * @author: <PERSON>.yang
 * @date:   2022年11月16日 下午2:30:45    
 * @Copyright:
 */
public interface WarehouseGoodsInService {
	
    WarehouseGoodsOutIn insertWarehouseGoodsOutIn(WarehouseGoodsOutIn warehouseGoodsOutIn);

    /**
     * 直发物流入库单操作
     * @param user ： 用户信息
     * @param pkId ：  采购订单为 T_BUYORDER 主键buyOrderId 售后订单为 T_AFTER_SALES 主键AFTER_SALES_ID
     * @param goodsIdAndSendNum ：  byOrderGoodsId 为T_BUYORDER_GOODS中的主键或者为T_AFTER_SALES_GOODS中的主键 ，sendNum 为发货数量
     * @purchaseIn：参考 WarehouseGoodsInEnum 枚举
     */
	void insertWarehouseGoodsPurchaseDirectOutInDirect(User user, Integer pkId, List<Map<String, Object>> goodsIdAndSendNum,
													   WarehouseGoodsInEnum purchaseIn, Express express);
	
	/**
	 * 生成入库验收报告
	 * @param warehouse
	 * @param warehouseGoodsOperateLogList 普发或者同行单直发
	 * @param warehouseGoodsOperateLogDirectList 物流维护直发
	 */
	void createWarehouseGoodsInReport(WarehouseGoodsOutIn warehouse,List<WarehouseGoodsOutInItem> warehouseGoodsOutInItemList);
	
	
	/**
	 * 采购售后换货入库
	 * @param user
	 * @param pkId
	 * @param warehouseGoodsOutInItemList
	 * @param erpOutInType
	 */
	void insertWarehouseGoodsPurchaseOutIn(User user,Integer pkId,List<WarehouseGoodsOutInItem> warehouseGoodsOutInItemParamsList, WarehouseGoodsInEnum erpOutInType);
	
	/**
	 * 根据skuId获取商品相关信息
	 * @param skuInfo
	 * @param skuId
	 */
	void querySkuInfo(OutInDetail skuInfo, Integer skuId);

	/**
	 * 根据售后主键relationId返回入库单明细
	 * @param afterSalesId
	 * @return
	 */
	List<WarehouseGoodsOutInItem> getWarehouseGoodsPurchaseOutInByPkId(Integer afterSalesId);

	/**
	 * 销售售后退还货直发入库
	 * @param user
	 * @param afterSalesId
	 * @param afterSalesDirectInfo
	 * @param orderWarehouseBackIn
	 */
    void inserSalesAfterThOrHh(User user, Integer afterSalesId, AfterSalesDirectInfo afterSalesDirectInfo, WarehouseGoodsInEnum orderWarehouseBackIn);

	/**
	 * 删除老附件 重新生成新附件
	 * @param warehouseGoodsOutInId 入库单id
	 */
	void regenerateWarehouseInReport(Long warehouseGoodsOutInId);
}
