package com.newtask.goods;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.StringUtil;
import com.vedeng.docSync.service.SyncGoodsService;
import com.vedeng.goods.dao.SyncGoodsInfoMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * 商品信息同步到ES-Search
 */
@Component
@JobHandler(value = "syncSkuInfo2EsSearch")
public class SyncSkuInfo2EsSearchTask extends AbstractJobHandler {

    @Autowired
    private SyncGoodsService syncGoodsService;

    @Resource
    private SyncGoodsInfoMapper syncGoodsInfoMapper;


    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        XxlJobLogger.log("SyncSkuInfo2EsSearchTask -----------" + s);
        List<Integer> validSkuIds = StringUtil.isBlank(s) ? syncGoodsInfoMapper.getValidSkuIds() : JSON.parseArray(s, Integer.class);

        if (CollectionUtils.isEmpty(validSkuIds)) {
            return FAIL;
        }

        syncGoodsService.syncSkuInfo2EsBySkuIds(validSkuIds);

        //获取禁用商品的报文-http://jira.ivedeng.com/browse/CRM-701
        //ERP综合询价商品禁用时索引删除
        syncGoodsService.syncNotValidSkuInfo2EsBySkuIds();
        
        return SUCCESS;
    }
}
