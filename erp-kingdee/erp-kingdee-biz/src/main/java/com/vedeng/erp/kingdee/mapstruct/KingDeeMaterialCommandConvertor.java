package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeeMaterialCommand;
import com.vedeng.erp.kingdee.dto.KingDeeMaterialDto;
import com.vedeng.erp.kingdee.dto.KingDeeMaterialSubHeadEntityDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 金蝶物料dto 转 command
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface KingDeeMaterialCommandConvertor extends BaseCommandMapStruct<KingDeeMaterialCommand, KingDeeMaterialDto> {

    /**
     * KingDeeMaterialCommand
     *
     * @param dto KingDeeMaterialDto
     * @return KingDeeMaterialCommand
     */
    @Mapping(target = "FCreateOrgId.FNumber", source = "FCreateOrgId")
    @Mapping(target = "FUseOrgId.FNumber", source = "FUseOrgId")
    @Mapping(target = "f_QZOK_DHH", source = "FQzokDhh")
    @Mapping(target = "f_QZOK_GYSWLBM", source = "FQzokGyswlbm")
    @Mapping(target = "f_QZOK_PPPTEXT", source = "FQzokPpptext")
    @Mapping(target = "f_QZOK_ZCZHTEXT", source = "FQzokZczhtext")
    @Mapping(target = "f_QZOK_YLQXTEXT", source = "FQzokYlqxtext")
    @Mapping(target = "f_QZOK_YLQXCXTEXT", source = "FQzokYlqxcxtext")
    @Mapping(target = "f_QZOK_YLQXYTTEXT", source = "FQzokYlqxyttext")
    @Mapping(target = "f_QZOK_YLQXXFLTEXT", source = "FQzokYlqxxfltext")
    @Mapping(target = "f_QZOK_YLQXFLTEXT", source = "FQzokYlqxfltext")
    @Mapping(target = "f_QZOK_SPYYYJFLTEXT", source = "FQzokSpyyyjfltext")
    @Mapping(target = "f_QZOK_SPYYEJFLTEXT", source = "FQzokSpyyejfltext")
    @Mapping(target = "f_QZOK_SPYYSJFLTEXT", source = "FQzokSpyysjfltext")
    @Mapping(target = "f_QZOK_SPYYSIJFLTEXT", source = "FQzokSpyysijfltext")
    @Mapping(target = "f_QZOK_SPYYWJFLTEXT", source = "FQzokSpyywjfltext")
    @Mapping(target = "f_QZOK_FYLQXYJFLTEXT", source = "FQzokFylqxyjfltext")
    @Mapping(target = "f_QZOK_FYLQXEJFLTEXT", source = "FQzokFylqxejfltext")
    @Mapping(target = "f_QZOK_CXFL", source = "FQzokZycpx")
    @Mapping(target = "f_QZOK_ZYCPX", source = "FQzokCxfl")
    @Mapping(target = "f_QZOK_ATFL", source = "FQzokAtfl")
    @Mapping(target = "FTAXCATEGORYCODEID.FNumber", source = "FTaxcategoryCodeId")
    @Override
    KingDeeMaterialCommand toCommand(KingDeeMaterialDto dto);

    /**
     * KingDeeMaterialSubHead 转换
     *
     * @param dto KingDeeMaterialSubHeadEntityDto
     * @return KingDeeMaterialCommand.KingDeeMaterialSubHead
     */
    @Mapping(target = "FCategoryID.FNumber", source = "FCategoryID")
    @Mapping(target = "FBaseUnitId.FNumber", source = "FBaseUnitId")
    KingDeeMaterialCommand.KingDeeMaterialSubHead toCommand(KingDeeMaterialSubHeadEntityDto dto);
}

