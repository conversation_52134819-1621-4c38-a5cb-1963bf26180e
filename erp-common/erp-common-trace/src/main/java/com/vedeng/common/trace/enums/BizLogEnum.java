package com.vedeng.common.trace.enums;


/**
 * @Description : 操作日志模板
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/7/22
 */
public enum BizLogEnum {

    /**
     * 操作日志模板
     */
    BUSINESSlEADS_NEW("新建线索", "新建线索", "01", "新建线索"),
    BUSINESSlEADS_ASSIGN("分配线索", "分配线索给{targetUserName}", "01", "分配线索"),
    BUSINESSlEADS_CLOSE("关闭线索", "关闭线索", "01", "关闭线索"),
    BUSINESSlEADS_TO_CHANCE("线索转商机", "线索转商机", "01", "线索转商机"),
    BUSINESSlEADS_EDIT("编辑线索", "编辑线索", "01", "编辑线索"),
    BUSINESSlEADS_SHARE("分享线索", "设置{targetUserName}协作", "01", "分享线索"),
    BUSINESSlEADS_SHARE_AUTO("分享商机", "{targetUserName}创建分享客户线索，自动成为协作人", "01", "分享商机"),
    BUSINESSlEADS_ABORT_SHARE("取消分享线索", "取消{targetUserName}协作", "01", "取消分享线索"),


    BUSINESS_CHANE_NE("新建商机", "新建商机", "02", "新建商机"),
    BUSINESS_CHANE_TO_ORDER("商机赢单", "商机转订单", "02", "商机赢单"),
    BUSINESS_CHANE_CLOSE("关闭商机", "关闭商机", "02", "关闭商机"),
    BUSINESS_CHANE_TO_QUOTE("添加报价", "添加报价", "02", "添加报价"),
    BUSINESS_CHANE_EDIT("编辑商机", "编辑商机", "02", "编辑商机"),
    BUSINESS_CHANE_SHARE("分享商机", "设置{targetUserName}协作", "02", "分享商机"),
    BUSINESS_CHANE_SHARE_AUTO("分享商机", "{targetUserName}创建分享客户商机，自动成为协作人", "02", "分享商机"),
    BUSINESS_CHANE_ABORT_SHARE("取消分享商机", "取消{targetUserName}协作", "02", "取消分享商机"),


    // 报价单
//    NEW_QUOTATION("新建报价单", "新建报价单", "03", "新建报价单"),
    ACTIVATE_QUOTATION("生效报价单", "生效报价单", "03", "生效报价单"),
    REVOKE_ACTIVATION("撤销生效", "撤销生效报价单", "03", "撤销生效"),
    EXPORT_QUOTATION("导出报价", "导出报价", "03", "导出报价"),
    INITIATE_GROUP_CHAT("发起群聊", "发起群聊", "03", "发起群聊"),
    MODIFY_REPORT_STATUS("报备状态修改", "【{skuNo}】{result}", "03", "报备状态修改"),
    PROCESS_DELIVERY_TIME("货期处理", "修改【{skuNo}】的货期为{deliveryDays}天", "03", "货期处理"),
    MODIFY_PRICE("价格修改", "修改【{skuNo}】的价格为{price}元", "03", "价格修改"),
    MODIFY_NUMBER("数量修改", "修改【{skuNo}】的数量为{num}", "03", "数量修改"),
    REMOVE_PRODUCT("删除", "删除【{skuNo}】的报价", "03", "删除"),
    ADD_PRODUCT("添加产品", "添加【{skuNo}】到报价单", "03", "添加产品"),
    ADD_NO_SKU_PRODUCT("添加自定义产品", "手工添加【{skuName}】到报价单", "03", "添加自定义产品"),
    MODIFY_NO_SKU_PRODUCT("修改自定义产品", "修改自定义产品【{skuName}】", "03", "修改自定义产品"),
    MODIFY_PRODUCT("重新添加产品", "把{oldSkuNo}修改为{newSkuNo}", "03", "重新添加产品"),
    ADD_NEED("删除客户需求", "删除“{productNeeds}”的客户需求", "03", "删除客户需求"),


    // 上传文件
    UPLOAD_FILE("上传文件", "上传{fileName}", "03", "上传文件"),
    //删除
    DELETE_FILE("删除文件", "删除{fileName}", "03", "删除文件"),
    //下载
    DOWNLOAD_FILE("下载文件", "下载{fileName}", "03", "下载文件"),

    ;


    /**
     * 业务操作名称
     */
    private String bizName;

    /**
     * 模板的内容
     */
    private String templateContent;

    /**
     * 业务类型 01线索池 02商机库 03 报价单
     */
    private String bizTypeEnum;

    /**
     * 操作动作 中文名称,暂同bizName
     */
    private String actionTypeEnum;

    BizLogEnum(String bizName, String templateContent, String bizTypeEnum, String actionTypeEnum) {
        this.bizName = bizName;
        this.templateContent = templateContent;
        this.bizTypeEnum = bizTypeEnum;
        this.actionTypeEnum = actionTypeEnum;
    }


    public String getBizName() {
        return bizName;
    }

    public String getTemplateContent() {
        return templateContent;
    }

    public String getBizTypeEnum() {
        return bizTypeEnum;
    }

    public String getActionTypeEnum() {
        return actionTypeEnum;
    }


    public void setBizName(String bizName) {
        this.bizName = bizName;
    }

    public void setTemplateContent(String templateContent) {
        this.templateContent = templateContent;
    }

    public void setBizTypeEnum(String bizTypeEnum) {
        this.bizTypeEnum = bizTypeEnum;
    }

    public void setActionTypeEnum(String actionTypeEnum) {
        this.actionTypeEnum = actionTypeEnum;
    }
}
