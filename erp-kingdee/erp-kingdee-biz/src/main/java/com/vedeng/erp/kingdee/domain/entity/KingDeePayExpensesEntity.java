package com.vedeng.erp.kingdee.domain.entity;

import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseEntity;
import java.util.Date;

import com.vedeng.common.mybatis.jbatis.annotation.Column;
import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 费用应付单
 */
@Getter
@Setter
@Table(name = "KING_DEE_PAY_EXPENSES")
public class KingDeePayExpensesEntity extends BaseEntity {
    /**
     * ID
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;


    /**
     * 单据内码
     */
    private Integer fId;

    /**
     * fBillNo
     */
    private String fBillNo;

    /**
     * 单据日期
     */
    private String fDate;

    /**
     * 贝登单据头ID
     */
    private String fQzokBddjtId;

    /**
     * 是否价外税
     */
    private boolean fIsPriceExcludeTax;

    /**
     * 业务类型
     */
    private String fBusinessType;

    /**
     * 是否含税单价录入
     */
    private boolean fIsTax;

    /**
     * 立账类型
     */
    private String fSetAccountType;

    /**
     * 单据类型
     */
    private String fBillTypeId;

    /**
     * 供应商
     */
    private String fSupplierId;

    /**
     * 币别
     */
    private String fCurrencyId;

    /**
     * 结算组织
     */
    private String fSettleOrgId;

    /**
     * 付款组织
     */
    private String fPayOrgId;

    /**
     * 应付单明细
     */
    @Column(jdbcType = "VARCHAR")
    private JSONArray fEntityDetail;

}