package com.vedeng.infrastructure.bank.api.domain;

import com.vedeng.infrastructure.bank.api.domain.base.BankResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/10/11
 */
@Data
public class B2eNbsQryStaySignUpDraftsResponse  extends BankResponse {
    /**
     * 响应体
     */
    private B2eNbsQryStaySignUpDraftsResBody xDataBody;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class B2eNbsQryStaySignUpDraftsResBody {
        /**
         * 客户端交易的唯一标志
         */
        private String trnId;
        /**
         * 银行渠道交易流水号
         */
        private String svrId;
        /**
         * 持有流水号和票据流水号list
         */
        private List<B2eNbsQryStaySignUpDraftsResponse.BillSignMap> list;
    }


    /**
     * 持有流水号和票据流水号
     */
    @Data
    public static class BillSignMap {

        /**
         * 票据（包）号码
         */
        private String billNo;

        /**
         * 交易流水号
         */
        private String transId;
    }

}
