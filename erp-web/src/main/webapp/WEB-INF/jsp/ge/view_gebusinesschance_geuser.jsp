<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="GE商机详情" scope="application" />
<%@ include file="../common/common.jsp"%>
<script type="text/javascript" src="<%= basePath %>static/js/ge/add_gebusinesschance.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src='<%= basePath %>static/js/region/index.js?rnd=${resourceVersionKey}'></script>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<div class="form-list  form-tips12">
    <div style="text-align: right;margin: 10px">
<%--            报单状态可跟进 商机状态跟进中--%>
    <c:if test="${geBusinessChance.status eq 0}">
       <button type="button" class="bt-bg-style bg-light-blue bt-small mr10 addtitle" tabTitle='{"num":"ge_business_chance<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"./businesschance/ge/geExamine.do?geBussinessChanceId=${geBusinessChance.geBussinessChanceId}","title":"GE商机审核"}'>审核</button>
    </c:if>
    </div>
    <%--基础信息--%>
    <div class="parts">
        <div class="title-container title-container-blue">
            <div class="table-title nobor">基础信息</div>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <tbody>
            <tr>
                <td class="table-smaller">GE商机号</td>
                <td>${geBusinessChance.geBussinessChanceNo}</td>
                <td class="table-smaller">报单日期</td>
                <td><fmt:formatDate value="${geBusinessChance.addTime}" pattern="yyyy-MM-dd HH:mm"/></td>
            </tr>
            <tr>
                <td class="table-smaller">报价单号</td>
                <td>${geBusinessChance.quoteorderNo}</td>
                <td class="table-smaller">商机状态</td>
                <td>
                    <div class="customername pos_rel">
                    ${empty geBusinessChanceDetail || geBusinessChanceDetail.businessChanceStatus == 1 ? '跟进中':
                    (geBusinessChanceDetail.businessChanceStatus == 2 ? '赢单' : (geBusinessChanceDetail.businessChanceStatus == 3 ? '失单' : '-'))}
                        <c:if test="${not empty geBusinessChanceDetailRecord
                        && geBusinessChanceDetail.businessChanceStatus != geBusinessChanceDetailRecord.businessChanceStatus}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：${geBusinessChanceDetailRecord.businessChanceStatus == 1 ? '跟进中':
                    (geBusinessChanceDetailRecord.businessChanceStatus == 2 ? '赢单' : (geBusinessChanceDetailRecord.businessChanceStatus == 3 ? '失单' : '-'))}</div>
                        </c:if>
                    </div>
                </td>
            </tr>
            <tr>
                <td class="table-smaller">终端医院名称</td>
                <td>${geBusinessChance.terminalTraderName}</td>
                <td class="table-smaller">医院性质</td>
                <td>${geBusinessChance.hospitalType == 1 ? '公立' : (geBusinessChance.hospitalType == 2 ? '非公' : '-')}</td>
            </tr>
            <tr>
                <td class="table-smaller">意向型号</td>
                <td>${geBusinessChance.goodsName}</td>
                <td class="table-smaller">所属地区-详细地址</td>
                <td>${geBusinessChance.salesArea} ${geBusinessChance.address}</td>
            </tr>
            <tr>
                <td class="table-smaller">商机来源</td>
                <td>${geBusinessChance.geBusinessChanceSourceName}</td>
                <td class="table-smaller">经销商名称</td>
                <td>${geBusinessChance.traderName == null ? '-' : geBusinessChance.traderName}</td>
            </tr>
            <tr>
                <td class="table-smaller">报单状态</td>
                <td style="${geBusinessChance.status == 2 ? 'color: red' : ''};">
                    ${geBusinessChance.status == 1 ? '可跟进' : (geBusinessChance.status == 2 ? '不可跟进' : '待审核')}
                    <c:if test="${geBusinessChance.status == 2 && geBusinessChanceFeedBack.content != null && geBusinessChanceFeedBack.content != ''}">
                        (${geBusinessChanceFeedBack.content})
                    </c:if>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <c:if test="${not empty geBusinessChanceFeedBack}">
        <div class="parts">
            <div class="title-container title-container-yellow">
                <div class="table-title nobor">GE反馈</div>
            </div>
            <table class="table table-bordered table-striped table-condensed table-centered">
                <tbody>
                <tr>
                    <td class="table-smaller">报单状态</td>
                    <td>
                        ${geBusinessChanceFeedBack.status == 1 ? '可跟进':(geBusinessChanceFeedBack.status == 2 ? '不可跟进' : '-')}
                        <c:if test="${geBusinessChance.status == 2 && geBusinessChanceFeedBack.content != null && geBusinessChanceFeedBack.content != ''}">
                            (${geBusinessChanceFeedBack.content})
                        </c:if>
                    </td>
                    <td class="table-smaller">是否有account</td>
                    <td>${geBusinessChanceFeedBack.isHavingAccount == 0 ? '无':(geBusinessChanceFeedBack.isHavingAccount == 1 ? '有':'-')}</td>
                </tr>
                <tr>
                    <td class="table-smaller">account名</td>
                    <td>${geBusinessChanceFeedBack.accountName}</td>
                    <td class="table-smaller">account地址</td>
                    <td>${geBusinessChanceFeedBack.accountArea}${geBusinessChanceFeedBack.accountAddress}</td>
                </tr>
                <tr>
                    <td class="table-smaller">是否有MPC</td>
                    <td>${geBusinessChanceFeedBack.isHavingMpc == 0 ? '无':(geBusinessChanceFeedBack.isHavingMpc == 1 ? '有' : '-')}</td>
                    <td class="table-smaller">MPC详情</td>
                    <td>${geBusinessChanceFeedBack.mpcDetail}</td>
                </tr>
                </tbody>
            </table>
        </div>
    </c:if>
    <c:if test="${geBusinessChance.status == 1}">
    <div class="parts">
        <div class="title-container title-container-blue">
            <div class="table-title nobor">终端信息</div>
        </div>
        <%--已有GE反馈且非不可跟进状态-展示商机详细信息--%>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <tbody>
<%--                终端信息--%>
            <c:if test="${not empty geBusinessChanceDetail && geBusinessChanceDetail.terminalPartIsChange == 1}">
                <tr>
                    <td class="table-smaller">销售额</td>
                    <td>
                        <div class="customername pos_rel">
                            <c:if test="${geBusinessChanceDetail.salesAmount != null}">
                                <fmt:formatNumber type="number" value="${geBusinessChanceDetail.salesAmount}" pattern="0.00" maxFractionDigits="2" /> 万元
                            </c:if>
                            <c:if test="${geBusinessChanceDetail.salesAmount == null}">
                                -
                            </c:if>
                        <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.terminalPartIsChange == 1
                        && geBusinessChanceDetail.salesAmount != geBusinessChanceDetailRecord.salesAmount}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                <c:if test="${geBusinessChanceDetailRecord.salesAmount != null}">
                                    <fmt:formatNumber type="number" value="${geBusinessChanceDetailRecord.salesAmount}" pattern="0.00" maxFractionDigits="2" /> 万元
                                </c:if>
                                <c:if test="${geBusinessChanceDetailRecord.salesAmount == null}">
                                    -
                                </c:if>
                            </div>
                        </c:if>
                        </div>
                    </td>
                    <td class="table-smaller">医院等级规模</td>
                    <td>
                        <div class="customername pos_rel">
                        ${geBusinessChanceDetail.hospitalSizeName == null || geBusinessChanceDetail.hospitalSizeName == '' ? '-' : geBusinessChanceDetail.hospitalSizeName}
                        <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.terminalPartIsChange == 1
                        && geBusinessChanceDetail.hospitalSize != geBusinessChanceDetailRecord.hospitalSize}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">
                                原值：
                                    ${geBusinessChanceDetailRecord.hospitalSizeName == null || geBusinessChanceDetailRecord.hospitalSizeName == '' ? '-' : geBusinessChanceDetailRecord.hospitalSizeName}
                            </div>
                        </c:if>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="table-smaller">床位数</td>
                    <td>
                        <div class="customername pos_rel">
                        ${geBusinessChanceDetail.bedNum == null || geBusinessChanceDetail.bedNum == '' ? '-' : geBusinessChanceDetail.bedNum}
                        ${geBusinessChanceDetail.bedNum == null || geBusinessChanceDetail.bedNum == '' ? '' : '张'}
                        <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.terminalPartIsChange == 1
                        && geBusinessChanceDetail.bedNum != geBusinessChanceDetailRecord.bedNum}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">
                                原值：${geBusinessChanceDetailRecord.bedNum == null || geBusinessChanceDetailRecord.bedNum == '' ? '-' : geBusinessChanceDetailRecord.bedNum}
                                    ${geBusinessChanceDetailRecord.bedNum == null || geBusinessChanceDetailRecord.bedNum == '' ? '' : '张'}
                            </div>
                        </c:if>
                        </div>
                    </td>
                    <td class="table-smaller">年营收</td>
                    <td>
                        <div class="customername pos_rel">
                            <c:if test="${geBusinessChanceDetail.yearInSum != null}">
                                <fmt:formatNumber type="number" value="${geBusinessChanceDetail.yearInSum}" pattern="0.00" maxFractionDigits="2" /> 万元
                            </c:if>
                            <c:if test="${geBusinessChanceDetail.yearInSum == null}">
                                -
                            </c:if>
                        <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.terminalPartIsChange == 1
                        && geBusinessChanceDetail.yearInSum != geBusinessChanceDetailRecord.yearInSum}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                <c:if test="${geBusinessChanceDetailRecord.yearInSum != null}">
                                    <fmt:formatNumber type="number" value="${geBusinessChanceDetailRecord.yearInSum}" pattern="0.00" maxFractionDigits="2" /> 万元
                                </c:if>
                                <c:if test="${geBusinessChanceDetailRecord.yearInSum == null}">
                                    -
                                </c:if>
                            </div>
                        </c:if>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="table-smaller">CT患者量(日)</td>
                    <td>
                        <div class="customername pos_rel">
                        ${geBusinessChanceDetail.ctDailyNum == null || geBusinessChanceDetail.ctDailyNum == '' ? '-':geBusinessChanceDetail.ctDailyNum}
                        <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.terminalPartIsChange == 1
                        && geBusinessChanceDetail.ctDailyNum != geBusinessChanceDetailRecord.ctDailyNum}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">
                                原值：${geBusinessChanceDetailRecord.ctDailyNum == null || geBusinessChanceDetailRecord.ctDailyNum == '' ? '-':geBusinessChanceDetailRecord.ctDailyNum}
                            </div>
                        </c:if>
                        </div>
                    </td>
                    <td class="table-smaller">是否新建医院</td>
                    <td>
                        <div class="customername pos_rel">
                            ${geBusinessChanceDetail.isNewHospital == 0 ? '否' : (geBusinessChanceDetail.isNewHospital == 1 ? '是':'-')}
                        <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.terminalPartIsChange == 1
                        && geBusinessChanceDetail.isNewHospital != geBusinessChanceDetailRecord.isNewHospital}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">
                                原值：${geBusinessChanceDetailRecord.isNewHospital == 0 ? '否' : (geBusinessChanceDetailRecord.isNewHospital == 1 ? '是':'-')}
                            </div>
                        </c:if>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="table-smaller">新建医院场地进度</td>
                    <td>
                        <div class="customername pos_rel">
                        <c:if test="${geBusinessChanceDetail.newHospitalPercent == 0}">-</c:if>
                        <c:if test="${geBusinessChanceDetail.newHospitalPercent == 1}">20%</c:if>
                        <c:if test="${geBusinessChanceDetail.newHospitalPercent == 2}">50%</c:if>
                        <c:if test="${geBusinessChanceDetail.newHospitalPercent == 3}">70%</c:if>
                        <c:if test="${geBusinessChanceDetail.newHospitalPercent == 4}">100%</c:if>
                        <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.terminalPartIsChange == 1
                        && geBusinessChanceDetail.newHospitalPercent != geBusinessChanceDetailRecord.newHospitalPercent}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                <c:if test="${geBusinessChanceDetailRecord.newHospitalPercent == 0}">-</c:if>
                                <c:if test="${geBusinessChanceDetailRecord.newHospitalPercent == 1}">20%</c:if>
                                <c:if test="${geBusinessChanceDetailRecord.newHospitalPercent == 2}">50%</c:if>
                                <c:if test="${geBusinessChanceDetailRecord.newHospitalPercent == 3}">70%</c:if>
                                <c:if test="${geBusinessChanceDetailRecord.newHospitalPercent == 4}">100%</c:if>
                            </div>
                        </c:if>
                        </div>
                    </td>
                    <td class="table-smaller">预计采购月份</td>
                    <td>
                        <div class="customername pos_rel">
                            <c:if test="${geBusinessChanceDetail.expectBuyTime != null}">
                                <fmt:formatDate value="${geBusinessChanceDetail.expectBuyTime}" pattern="yyyy-MM"/>
                            </c:if>
                            <c:if test="${geBusinessChanceDetail.expectBuyTime == null}">
                                -
                            </c:if>
                        <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.terminalPartIsChange == 1
                        && geBusinessChanceDetail.expectBuyTime != geBusinessChanceDetailRecord.expectBuyTime}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                <c:if test="${geBusinessChanceDetailRecord.expectBuyTime != null}">
                                    <fmt:formatDate value="${geBusinessChanceDetailRecord.expectBuyTime}" pattern="yyyy-MM"/>
                                </c:if>
                                <c:if test="${geBusinessChanceDetailRecord.expectBuyTime == null}">
                                    -
                                </c:if>
                            </div>
                        </c:if>
                        </div>
                    </td>
                </tr>
            </c:if>
            <c:if test="${empty geBusinessChanceDetail || geBusinessChanceDetail.terminalPartIsChange == 0}">
                <tr>
                    <td colspan="4">暂无终端信息</td>
                </tr>
            </c:if>
            </tbody>
        </table>
        </div>
<%--                竞品信息--%>
        <div class="parts">
            <div class="title-container title-container-blue">
                <div class="table-title nobor">竞品信息</div>
            </div>
            <table class="table table-bordered table-striped table-condensed table-centered">
                <tbody>
            <c:if test="${not empty geBusinessChanceDetail && geBusinessChanceDetail.competePartIsChange == 1}">
                    <tr>
                        <td class="table-smaller">现有竞争对手</td>
                        <td>
                            <div class="customername pos_rel">
                            ${geBusinessChanceDetail.competeName == null || geBusinessChanceDetail.competeName == '' ? '-' : geBusinessChanceDetail.competeName}
                            <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.competePartIsChange == 1
                            && geBusinessChanceDetail.competeName != geBusinessChanceDetailRecord.competeName}">
                                <i class="iconbluesigh ml4 contorlIcon"></i></span>
                                <div class="pos_abs customernameshow">原值：
                                    ${geBusinessChanceDetailRecord.competeName == null || geBusinessChanceDetailRecord.competeName == '' ? '-' : geBusinessChanceDetailRecord.competeName}
                                </div>
                            </c:if>
                            </div>
                        </td>
                        <td class="table-smaller">竞争对手产品</td>
                        <td>
                            <div class="customername pos_rel">
                                ${geBusinessChanceDetail.competeSkuName == null || geBusinessChanceDetail.competeSkuName == '' ? '-' : geBusinessChanceDetail.competeSkuName}
                            <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.competePartIsChange == 1
                            && geBusinessChanceDetail.competeSkuName != geBusinessChanceDetailRecord.competeSkuName}">
                                <i class="iconbluesigh ml4 contorlIcon"></i></span>
                                <div class="pos_abs customernameshow">原值：
                                        ${geBusinessChanceDetailRecord.competeSkuName == null || geBusinessChanceDetailRecord.competeSkuName == '' ? '-' : geBusinessChanceDetailRecord.competeSkuName}
                                </div>
                            </c:if>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="table-smaller">竞品价格</td>
                        <td>
                            <div class="customername pos_rel">
                                <c:if test="${geBusinessChanceDetail.competeSkuPrice != null}">
                                    <fmt:formatNumber type="number" value="${geBusinessChanceDetail.competeSkuPrice}" pattern="0.00" maxFractionDigits="2" /> 万元
                                </c:if>
                                <c:if test="${geBusinessChanceDetail.competeSkuPrice == null}">
                                    -
                                </c:if>
                            <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.competePartIsChange == 1
                            && geBusinessChanceDetail.competeSkuPrice != geBusinessChanceDetailRecord.competeSkuPrice}">
                                <i class="iconbluesigh ml4 contorlIcon"></i></span>
                                <div class="pos_abs customernameshow">原值：
                                    <c:if test="${geBusinessChanceDetailRecord.competeSkuPrice != null}">
                                        <fmt:formatNumber type="number" value="${geBusinessChanceDetailRecord.competeSkuPrice}" pattern="0.00" maxFractionDigits="2" /> 万元
                                    </c:if>
                                    <c:if test="${geBusinessChanceDetailRecord.competeSkuPrice == null}">
                                        -
                                    </c:if>
                                </div>
                            </c:if>
                            </div>
                        </td>
                        <td class="table-smaller"></td>
                        <td></td>
                    </tr>
            </c:if>
            <c:if test="${empty geBusinessChanceDetail || geBusinessChanceDetail.competePartIsChange == 0}">
                <tr>
                    <td colspan="4">暂无竞品信息</td>
                </tr>
            </c:if>
        </tbody>
        </table>
        </div>
        <%--                原有装机信息--%>
        <div class="parts">
            <div class="title-container title-container-blue">
                <div class="table-title nobor">原有装机信息</div>
            </div>
            <table class="table table-bordered table-striped table-condensed table-centered">
                <tbody>
        <c:if test="${not empty geBusinessChanceDetail && geBusinessChanceDetail.originInstallPartIsChange == 1}">
                <tr>
                    <td class="table-smaller">是否新增</td>
                    <td>
                        <div class="customername pos_rel">
                            ${geBusinessChanceDetail.isNewInstall == 0 ? '否' : (geBusinessChanceDetail.isNewInstall == 1 ? '是':'-')}
                        <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.originInstallPartIsChange == 1
                        && geBusinessChanceDetail.isNewInstall != geBusinessChanceDetailRecord.isNewInstall}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：${geBusinessChanceDetailRecord.isNewInstall == 0 ? '否' : (geBusinessChanceDetailRecord.isNewInstall == 1 ? '是':'-')}</div>
                        </c:if>
                        </div>
                    </td>
                    <td class="table-smaller">原有品牌</td>
                    <td>
                        <div class="customername pos_rel">
                            ${geBusinessChanceDetail.originSkuBand == null || geBusinessChanceDetail.originSkuBand == '' ? '-' : geBusinessChanceDetail.originSkuBand}
                        <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.originInstallPartIsChange == 1
                            && geBusinessChanceDetail.originSkuBand != geBusinessChanceDetailRecord.originSkuBand}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                    ${geBusinessChanceDetailRecord.originSkuBand == null || geBusinessChanceDetailRecord.originSkuBand == '' ? '-' : geBusinessChanceDetailRecord.originSkuBand}
                            </div>
                        </c:if>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="table-smaller">原有型号</td>
                    <td>
                        <div class="customername pos_rel">
                            ${geBusinessChanceDetail.originSkuModel == null || geBusinessChanceDetail.originSkuModel == '' ? '-' : geBusinessChanceDetail.originSkuModel}
                        <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.originInstallPartIsChange == 1
                            && geBusinessChanceDetail.originSkuModel != geBusinessChanceDetailRecord.originSkuModel}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                    ${geBusinessChanceDetailRecord.originSkuModel == null || geBusinessChanceDetailRecord.originSkuModel == '' ? '-' : geBusinessChanceDetailRecord.originSkuModel}
                            </div>
                        </c:if>
                        </div>
                    </td>
                    <td class="table-smaller">装机时间</td>
                    <td>
                        <div class="customername pos_rel">
                            <c:if test="${geBusinessChanceDetail.installTime != null}">
                                <fmt:formatDate value="${geBusinessChanceDetail.installTime}" pattern="yyyy-MM-dd"/>
                            </c:if>
                            <c:if test="${geBusinessChanceDetail.installTime == null}">
                                -
                            </c:if>
                        <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.originInstallPartIsChange == 1
                            && geBusinessChanceDetail.installTime != geBusinessChanceDetailRecord.installTime}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                <c:if test="${geBusinessChanceDetailRecord.installTime != null}">
                                    <fmt:formatDate value="${geBusinessChanceDetailRecord.installTime}" pattern="yyyy-MM-dd"/>
                                </c:if>
                                <c:if test="${geBusinessChanceDetailRecord.installTime == null}">
                                    -
                                </c:if>
                            </div>
                        </c:if>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="table-smaller">销售公司</td>
                    <td>
                        <div class="customername pos_rel">
                            ${geBusinessChanceDetail.saleCompanyName == null || geBusinessChanceDetail.saleCompanyName == '' ? '-' : geBusinessChanceDetail.saleCompanyName}
                        <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.originInstallPartIsChange == 1
                            && geBusinessChanceDetail.saleCompanyName != geBusinessChanceDetailRecord.saleCompanyName}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                    ${geBusinessChanceDetailRecord.saleCompanyName == null || geBusinessChanceDetailRecord.saleCompanyName == '' ? '-' : geBusinessChanceDetailRecord.saleCompanyName}
                            </div>
                        </c:if>
                        </div>
                    </td>
                    <td class="table-smaller"></td>
                    <td></td>
                </tr>
        </c:if>
        <c:if test="${empty geBusinessChanceDetail || geBusinessChanceDetail.originInstallPartIsChange == 0}">
            <tr>
                <td colspan="4">暂无原有装机信息</td>
            </tr>
        </c:if>
        </tbody>
        </table>
        </div>
        <%--                商机详细信息--%>
        <div class="parts">
            <div class="title-container title-container-blue">
                <div class="table-title nobor">商机详细情况</div>
            </div>
            <table class="table table-bordered table-striped table-condensed table-centered">
                <tbody>
        <c:if test="${not empty geBusinessChanceDetail && geBusinessChanceDetail.chanceDetailPartIsChange == 1}">
                <tr>
                    <td class="table-smaller">赢单率</td>
                    <td>
                        <div class="customername pos_rel">
                            ${geBusinessChanceDetail.winOrderPercent == 1 ? '高' : (geBusinessChanceDetail.winOrderPercent == 2 ? '中':
                            (geBusinessChanceDetail.winOrderPercent == 3 ? '低' : '-'))}
                        <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.chanceDetailPartIsChange == 1
                            && geBusinessChanceDetail.winOrderPercent != geBusinessChanceDetailRecord.winOrderPercent}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                    ${geBusinessChanceDetailRecord.winOrderPercent == 1 ? '高' : (geBusinessChanceDetailRecord.winOrderPercent == 2 ? '中':
                                            (geBusinessChanceDetailRecord.winOrderPercent == 3 ? '低' : '-'))}</div>
                        </c:if>
                        </div>
                    </td>
                    <td class="table-smaller">资金来源</td>
                    <td>
                        <div class="customername pos_rel">
                            ${geBusinessChanceDetail.moneySource == null || geBusinessChanceDetail.moneySource == '' ? '-' : geBusinessChanceDetail.moneySource}
                        <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.chanceDetailPartIsChange == 1
                            && geBusinessChanceDetail.moneySource != geBusinessChanceDetailRecord.moneySource}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                    ${geBusinessChanceDetailRecord.moneySource == null || geBusinessChanceDetailRecord.moneySource == '' ? '-' : geBusinessChanceDetailRecord.moneySource}
                            </div>
                        </c:if>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="table-smaller">资金情况</td>
                    <td>
                        <div class="customername pos_rel">
                            ${geBusinessChanceDetail.moneySituation == null || geBusinessChanceDetail.moneySituation == '' ? '-' : geBusinessChanceDetail.moneySituation}
                        <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.chanceDetailPartIsChange == 1
                            && geBusinessChanceDetail.moneySituation != geBusinessChanceDetailRecord.moneySituation}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                    ${geBusinessChanceDetailRecord.moneySituation == null || geBusinessChanceDetailRecord.moneySituation == '' ? '-' : geBusinessChanceDetailRecord.moneySituation}
                            </div>
                        </c:if>
                        </div>
                    </td>
                    <td class="table-smaller">预算/预备资金金额</td>
                    <td>
                        <div class="customername pos_rel">
                            <c:if test="${geBusinessChanceDetail.prepareAmount != null}">
                                <fmt:formatNumber type="number" value="${geBusinessChanceDetail.prepareAmount}" pattern="0.00" maxFractionDigits="2" /> 万元
                            </c:if>
                            <c:if test="${geBusinessChanceDetail.prepareAmount == null}">
                                -
                            </c:if>
                        <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.chanceDetailPartIsChange == 1
                            && geBusinessChanceDetail.prepareAmount != geBusinessChanceDetailRecord.prepareAmount}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                <c:if test="${geBusinessChanceDetailRecord.prepareAmount != null}">
                                    <fmt:formatNumber type="number" value="${geBusinessChanceDetailRecord.prepareAmount}" pattern="0.00" maxFractionDigits="2" /> 万元
                                </c:if>
                                <c:if test="${geBusinessChanceDetailRecord.prepareAmount == null}">
                                    -
                                </c:if>
                            </div>
                        </c:if>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="table-smaller">采购形式</td>
                    <td>
                        <div class="customername pos_rel">
                            ${geBusinessChanceDetail.buyTypeName == null || geBusinessChanceDetail.buyTypeName == '' ? '-' : geBusinessChanceDetail.buyTypeName}
                        <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.chanceDetailPartIsChange == 1
                            && geBusinessChanceDetail.buyTypeName != geBusinessChanceDetailRecord.buyTypeName}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                    ${geBusinessChanceDetailRecord.buyTypeName == null || geBusinessChanceDetailRecord.buyTypeName == '' ? '-' : geBusinessChanceDetailRecord.buyTypeName}
                            </div>
                        </c:if>
                        </div>
                    </td>
                    <td class="table-smaller">是否安排考察</td>
                    <td>
                        <div class="customername pos_rel">
                            ${geBusinessChanceDetail.isApplyInspect == 0 ? '否' : (geBusinessChanceDetail.isApplyInspect == 1 ? '是':'-')}
                        <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.chanceDetailPartIsChange == 1
                            && geBusinessChanceDetail.isApplyInspect != geBusinessChanceDetailRecord.isApplyInspect}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：${geBusinessChanceDetailRecord.isApplyInspect == 0 ? '否' : (geBusinessChanceDetailRecord.isApplyInspect == 1 ? '是':'-')}</div>
                        </c:if>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="table-smaller">机房是否已布置完成</td>
                    <td>
                        <div class="customername pos_rel">
                            ${geBusinessChanceDetail.isEngineComplete == 0 ? '否' : (geBusinessChanceDetail.isEngineComplete == 1 ? '是':'-')}
                        <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.chanceDetailPartIsChange == 1
                            && geBusinessChanceDetail.isEngineComplete != geBusinessChanceDetailRecord.isEngineComplete}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：${geBusinessChanceDetailRecord.isEngineComplete == 0 ? '否' : (geBusinessChanceDetailRecord.isEngineComplete == 1 ? '是':'-')}</div>
                        </c:if>
                        </div>
                    </td>
                    <td class="table-smaller">报价方案</td>
                    <td>
                        <div class="customername pos_rel">
                            ${geBusinessChanceDetail.quoteMethod == null || geBusinessChanceDetail.quoteMethod == '' ? '-' : geBusinessChanceDetail.quoteMethod}
                        <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.chanceDetailPartIsChange == 1
                            && geBusinessChanceDetail.quoteMethod != geBusinessChanceDetailRecord.quoteMethod}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                    ${geBusinessChanceDetailRecord.quoteMethod == null || geBusinessChanceDetailRecord.quoteMethod == '' ? '-' : geBusinessChanceDetailRecord.quoteMethod}
                            </div>
                        </c:if>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="table-smaller">方案价格</td>
                    <td>
                        <div class="customername pos_rel">
                            <c:if test="${geBusinessChanceDetail.quoteMethodPrice != null}">
                                <fmt:formatNumber type="number" value="${geBusinessChanceDetail.quoteMethodPrice}" pattern="0.00" maxFractionDigits="2" /> 万元
                            </c:if>
                            <c:if test="${geBusinessChanceDetail.quoteMethodPrice == null}">
                                -
                            </c:if>
                        <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.chanceDetailPartIsChange == 1
                            && geBusinessChanceDetail.quoteMethodPrice != geBusinessChanceDetailRecord.quoteMethodPrice}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                <c:if test="${geBusinessChanceDetailRecord.quoteMethodPrice != null}">
                                    <fmt:formatNumber type="number" value="${geBusinessChanceDetailRecord.quoteMethodPrice}" pattern="0.00" maxFractionDigits="2" /> 万元
                                </c:if>
                                <c:if test="${geBusinessChanceDetailRecord.quoteMethodPrice == null}">
                                    -
                                </c:if>
                            </div>
                        </c:if>
                        </div>
                    </td>
                    <td class="table-smaller"></td>
                    <td></td>
                </tr>
        </c:if>
        <c:if test="${empty geBusinessChanceDetail || geBusinessChanceDetail.chanceDetailPartIsChange == 0}">
            <tr>
                <td colspan="4">暂无商机详细信息</td>
            </tr>
        </c:if>
        </tbody>
        </table>
        </div>
        <%--                关键问题解决--%>
        <div class="parts">
            <div class="title-container title-container-blue">
                <div class="table-title nobor">关键问题解决</div>
            </div>
            <table class="table table-bordered table-striped table-condensed table-centered">
                <tbody>
        <c:if test="${not empty geBusinessChanceDetail && geBusinessChanceDetail.keyDealPartIsChange == 1}">
                <tr>
                    <td class="table-smaller">项目阶段</td>
                    <td>
                        <div class="customername pos_rel">
                            ${geBusinessChanceDetail.projectPhaseName == null || geBusinessChanceDetail.projectPhaseName == '' ? '-' : geBusinessChanceDetail.projectPhaseName}
                        <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.keyDealPartIsChange == 1
                            && geBusinessChanceDetail.projectPhaseName != geBusinessChanceDetailRecord.projectPhaseName}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                    ${geBusinessChanceDetailRecord.projectPhaseName == null || geBusinessChanceDetailRecord.projectPhaseName == '' ? '-' : geBusinessChanceDetailRecord.projectPhaseName}
                            </div>
                        </c:if>
                        </div>
                    </td>
                    <td class="table-smaller">待解决关键事项</td>
                    <td>
                        <div class="customername pos_rel">
                            ${geBusinessChanceDetail.needComplete == null || geBusinessChanceDetail.needComplete == '' ? '-' : geBusinessChanceDetail.needComplete}
                        <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.keyDealPartIsChange == 1
                            && geBusinessChanceDetail.needComplete != geBusinessChanceDetailRecord.needComplete}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                    ${geBusinessChanceDetailRecord.needComplete == null || geBusinessChanceDetailRecord.needComplete == '' ? '-' : geBusinessChanceDetailRecord.needComplete}
                            </div>
                        </c:if>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="table-smaller">需求时间</td>
                    <td>
                        <div class="customername pos_rel">
                            <c:if test="${geBusinessChanceDetail.needTime != null}">
                                <fmt:formatDate value="${geBusinessChanceDetail.needTime}" pattern="yyyy-MM-dd"/>
                            </c:if>
                            <c:if test="${geBusinessChanceDetail.needTime == null}">
                                -
                            </c:if>
                        <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.keyDealPartIsChange == 1
                        && geBusinessChanceDetail.needTime != geBusinessChanceDetailRecord.needTime}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                <c:if test="${geBusinessChanceDetailRecord.needTime != null}">
                                    <fmt:formatDate value="${geBusinessChanceDetailRecord.needTime}" pattern="yyyy-MM-dd"/>
                                </c:if>
                                <c:if test="${geBusinessChanceDetailRecord.needTime == null}">
                                    -
                                </c:if>
                            </div>
                        </c:if>
                        </div>
                    </td>
                    <td class="table-smaller">协助部门</td>
                    <td>
                        <div class="customername pos_rel">
                            ${geBusinessChanceDetail.assistDepartment == null || geBusinessChanceDetail.assistDepartment == '' ? '-' : geBusinessChanceDetail.assistDepartment}
                        <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.keyDealPartIsChange == 1
                            && geBusinessChanceDetail.assistDepartment != geBusinessChanceDetailRecord.assistDepartment}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                    ${geBusinessChanceDetailRecord.assistDepartment == null || geBusinessChanceDetailRecord.assistDepartment == '' ? '-' : geBusinessChanceDetailRecord.assistDepartment}
                            </div>
                        </c:if>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="table-smaller">解决进展</td>
                    <td>
                        <div class="customername pos_rel">
                            ${geBusinessChanceDetail.projectEvolve == null || geBusinessChanceDetail.projectEvolve == '' ? '-' : geBusinessChanceDetail.projectEvolve}
                        <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.keyDealPartIsChange == 1
                            && geBusinessChanceDetail.projectEvolve != geBusinessChanceDetailRecord.projectEvolve}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：${geBusinessChanceDetailRecord.projectEvolve == null || geBusinessChanceDetailRecord.projectEvolve == '' ? '-' : geBusinessChanceDetailRecord.projectEvolve}</div>
                        </c:if>
                        </div>
                    </td>
                    <td class="table-smaller">解决时间</td>
                    <td>
                        <div class="customername pos_rel">
                            <c:if test="${geBusinessChanceDetail.evolveTime != null}">
                                <fmt:formatDate value="${geBusinessChanceDetail.evolveTime}" pattern="yyyy-MM-dd"/>
                            </c:if>
                            <c:if test="${geBusinessChanceDetail.evolveTime == null}">
                                -
                            </c:if>
                        <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.keyDealPartIsChange == 1
                        && geBusinessChanceDetail.evolveTime != geBusinessChanceDetailRecord.evolveTime}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                <c:if test="${geBusinessChanceDetailRecord.evolveTime != null}">
                                    <fmt:formatDate value="${geBusinessChanceDetailRecord.evolveTime}" pattern="yyyy-MM-dd"/>
                                </c:if>
                                <c:if test="${geBusinessChanceDetailRecord.evolveTime == null}">
                                    -
                                </c:if>
                            </div>
                        </c:if>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="table-smaller">下一步计划</td>
                    <td>
                        <div class="customername pos_rel">
                        ${geBusinessChanceDetail.nextPlan == null || geBusinessChanceDetail.nextPlan == '' ? '-' : geBusinessChanceDetail.nextPlan}
                        <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.keyDealPartIsChange == 1
                        && geBusinessChanceDetail.nextPlan != geBusinessChanceDetailRecord.nextPlan}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                    ${geBusinessChanceDetailRecord.nextPlan == null || geBusinessChanceDetailRecord.nextPlan == '' ? '-' : geBusinessChanceDetailRecord.nextPlan}
                            </div>
                        </c:if>
                        </div>
                    </td>
                    <td class="table-smaller">需要解决的事宜</td>
                    <td>
                        <div class="customername pos_rel">
                        ${geBusinessChanceDetail.needSupport == null || geBusinessChanceDetail.needSupport == '' ? '-' : geBusinessChanceDetail.needSupport}
                        <c:if test="${not empty geBusinessChanceDetailRecord && geBusinessChanceDetailRecord.keyDealPartIsChange == 1
                        && geBusinessChanceDetail.needSupport != geBusinessChanceDetailRecord.needSupport}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                    ${geBusinessChanceDetailRecord.needSupport == null || geBusinessChanceDetailRecord.needSupport == '' ? '-' : geBusinessChanceDetailRecord.needSupport}
                            </div>
                        </c:if>
                        </div>
                    </td>
                </tr>
        </c:if>
        <c:if test="${empty geBusinessChanceDetail || geBusinessChanceDetail.keyDealPartIsChange == 0}">
            <tr style="text-align: center">
                <td colspan="4">暂无关键问题解决信息</td>
            </tr>
        </c:if>
        </tbody>
        </table>
        </div>
    </c:if>

    <tags:ge_business_chance_logs geActionLogs="${geActionLogs}" />
</div>
</body>
