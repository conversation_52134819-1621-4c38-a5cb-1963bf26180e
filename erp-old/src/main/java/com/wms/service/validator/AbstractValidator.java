package com.wms.service.validator;

import com.wms.model.dto.ValidatorResult;
import org.springframework.transaction.annotation.Transactional;

public abstract class AbstractValidator<T> implements Validator<T>{

    @Override
    @Transactional
    public ValidatorResult validator(T ... requestData) throws Exception{

        boolean needToValidate = needToValidate(requestData);

        if(!needToValidate){
            return ValidatorResult.newBuild().setResult(true);
        }

        return doValidator(requestData);
    }

    protected abstract boolean needToValidate(T ... requestData);

    protected abstract ValidatorResult doValidator(T ... requestData) throws Exception;

}
