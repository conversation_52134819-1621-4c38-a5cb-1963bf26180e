package com.vedeng.flash.enums;

import com.vedeng.common.constant.AgingTypeEnum;

/**
 * @Description:  备货计划预警等级
 * @Author:       davis
 * @Date:         2021/5/24 上午11:03
 * @Version:      1.0
 */
public enum WarnLevelEnum {

    SAFE(0 , "安全"),
    SECOND_LEVEL(1, "二级预警-需补货"),
    FIRST_LEVEL(2, "一级预警-缺货");

    WarnLevelEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private Integer code;

    private String msg;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    /**
     * 根据编码获取编码信息
     * @param code
     * @return
     */
    public static String codeToMessage(Integer code){
        if(code == null){
            return "";
        }
        for(WarnLevelEnum enums : WarnLevelEnum.values()){
            if(code.equals(enums.getCode())){
                return enums.getMsg();
            }
        }
        return "";
    }

}
