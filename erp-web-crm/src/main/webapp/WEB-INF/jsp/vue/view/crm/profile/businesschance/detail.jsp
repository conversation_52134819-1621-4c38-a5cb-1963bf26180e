<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html>
<html>
<head>
    <title>CRM-商机详情</title>
    <link rel="stylesheet" href="/static/css/common/common.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/ui/ui.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/css/pages/records.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/css/common/selectProd.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/css/pages/businessChance.css?staticResourceVersion=${requestScope.staticResourceVersion}">
</head>

<body>
    <input type="hidden" id="isHiddenLayout" value="0">
    <jsp:include page="../common/head_import.jsp"></jsp:include>

    <!-- 线索id [线索转id] -->
    <input type="hidden" id="businessLeads-id" value="${param.leadsid}" />

    <!-- 商机id -->
    <input type="hidden" id="businesschance-id" value="${param.id}" />

    <!-- 任务id -->
    <input type="hidden" id="task-id" value="${param.taskItemId}" />

    <div class="page-wrap" id="page-container">
        <page-header></page-header>
        <div class="page-container">
            <div class="page-main">

                <div class="businesschance-detail-container" v-if="!pageLoading">
                    <div class="chanceDetail-header-wrap" :style="{'top': fixedTop+'px'}">
                        <div class="header-main">
                            <div class="header-content">
                                <div class="header-left">
                                    <div class="title">商机详情</div>
                                </div>
                                <div class="header-right">
                                    <ui-button v-if="!isOver" @click="handlerToOrder" type="primary">转订单</ui-button>
                                    <ui-button v-if="!isOver" @click="handlerEdit">编辑</ui-button>
                                    <ui-button v-if="!isOver" @click="handlerQuoteorder">{{quoteorderId? '编辑报价': '添加报价'}}</ui-button>
                                    <ui-button v-if="detail.stage != 6" @click="addVisitPlan">添加拜访</ui-button>
                                    <ui-button @click="handlerAttention" :class="detail.attentionState ? 'no-bg' : ''">
                                        <i class="vd-ui_icon" :class="detail.attentionState? 'icon-collect2': 'icon-collect1'"></i>
                                        <template>{{ detail.attentionState? '取消关注': '关注' }}</template>
                                    </ui-button>
                                    <ui-button v-if="!isOver" type="danger" @click="showCloseLeadsDialog">关闭</ui-button>
                                    <ui-card-switch name="businesschance_detail" v-model="cardLineNum"></ui-card-switch>
                                </div>
                            </div>
                            <div class="header-aside-wrap">
                                <!-- 大屏幕 -->
                                <div class="header-md-aside">
                                    <ui-title-tip :title="item.name" :position="layout_hidden_value ? 'bottom' : 'top'" :y="-23" v-for="item in asideList" :key="item.name">
                                        <div 
                                            class="h-a-item" 
                                            :class="[item.icon, {'active': item.id == asideIndex}]"
                                            @click="asideIndex = item.id"
                                        >
                                            <div class="h-a-item-num" v-if="item.id == 3 && taskNum">{{ taskNum }}</div>
                                        </div>
                                        <!-- <div class="btn-icon btn-message" @click="openRecord"></div> -->
                                    </ui-title-tip>
                                    
                                </div>
                                <!-- 小屏头 -->
                                <div class="header-xs-aside">
                                    <ui-title-tip :title="item.name" :y="-23" :position="layout_hidden_value ? 'bottom' : 'top'" v-for="item in asideList" :key="item.name">
                                        <div 
                                            :class="['h-a-item', item.icon]"
                                            @click="openDialog(item.id)"
                                        >
                                            <div class="h-a-item-num" v-if="item.id == 3 && taskNum">{{ taskNum }}</div>
                                        </div>
                                    </ui-title-tip>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="deatil-page-top-bg" :class="['stage' + detail.stage]" :style="{'top': fixedTop + 60 +'px'}"></div>
                    <div class="detail-page-wrap" :class="{'switch-line-2': cardLineNum == 2}">
                        <div class="chance-step" :class="['stage' + detail.stage]">
                            <ul>
                                <li :class="stepClass('preliminaryNegotiationTime')">
                                    <div class="icon"></div>
                                    <p>初步洽淡</p>
                                </li>
                                <li :class="stepClass('opportunityVerificationTime')">
                                    <div class="icon"></div>
                                    <p>商机验证</p>
                                </li>
                                <li :class="stepClass('preliminarySchemeTime')">
                                    <div class="icon"></div>
                                    <p>初步方案</p>
                                </li>
                                <li :class="stepClass('finalSchemeTime')">
                                    <div class="icon"></div>
                                    <p>最终方案</p>
                                </li>
                                <li v-if="detail.stage == 6" class="close">
                                    <div class="icon"></div>
                                    <p>关闭</p>
                                </li>
                                <li v-else :class="[detail.winningOrderTime ? 'success' : '' ]">
                                    <div class="icon"></div>
                                    <p>赢单</p>
                                </li>
                            </ul>
                        </div>
                        
                        <div class="main">
                            <div class="top-warn-tip" v-if="detail.stage==6">
                                <i class="vd-ui_icon icon-caution2"></i>
                                <div class="tip-cnt">
                                    关闭原因：{{ detail.closeReasonTypeName }}<template v-if="detail.closeReason">（{{ detail.closeReason }}）</template>
                                </div>
                            </div>
                            <div class="detail-top-card">
                                <div class="detail-top-item">
                                    <div class="item-label">商机编号</div>
                                    <div class="item-txt">{{ detail.bussinessChanceNo || '-' }}</div>
                                </div>
                                <div class="detail-top-item">
                                    <div class="item-label">商机等级</div>
                                    <div class="item-txt">{{ detail.systemBusinessLevelStr || '-' }}</div>
                                </div>
                                <div class="detail-top-item">
                                    <div class="item-label">来源</div>
                                    <div class="item-txt">{{ {391: '总机', 392: '销售', 394: '自有商城', 6012: '拜访'}[detail.clueType] }}</div>
                                </div>
                                <div class="detail-top-item">
                                    <div class="item-label">归属销售</div>
                                    <div class="item-txt">
                                        <template v-if="detail.belonger">
                                            <div class="user-avatar">
                                                <img :src="detail.belongPic || GLOBAL.defaultAvatar" alt="">
                                            </div>
                                            <div class="user-txt">{{ detail.belonger }}</div>
                                        </template>
                                        <template v-else>-</template>
                                    </div>
                                </div>
                                <div class="detail-top-item">
                                    <div class="item-label">创建时间</div>
                                    <div class="item-txt">{{ detail.addTime || '-' }}</div>
                                </div>
                            </div>
                            <div class="card detail-card">
                                <div class="card-title">客户和终端信息</div>
                                <div class="info-wrap">
                                    <div class="info-item">
                                        <div class="label">客户名称：</div>
                                        <div class="content">
                                            <ui-trader-name :info="{
                                                traderName: detail.traderName,
                                                traderNameLink: detail.traderNameLink,
                                                traderNameInnerLink: detail.traderNameInnerLink,
                                                tycFlag: detail.tycFlag,
                                                baidu: true
                                            }"></ui-trader-name>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">联系人：</div>
                                        <div class="content">{{ detail.contact || '-' }}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">手机：</div>
                                        <div class="content">
                                            <span v-if="detail.phone" :class="{'highlight': layout_hidden_value && detail.phone}" class="call-span" @click="callNumber_(detail.phone)">
                                                <i class="vd-ui_icon icon-call2"></i>
                                                <span>{{detail.phone}}</span>
                                            </span>
                                            <span v-else>-</span>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">固话：</div>
                                        <div class="content">
                                            <span v-if="detail.telephone" :class="{'highlight': layout_hidden_value && detail.telephone}" class="call-span" @click="callNumber_(detail.telephone)">
                                                <i class="vd-ui_icon icon-call2"></i>
                                                <span>{{detail.telephone}}</span>
                                            </span>
                                            <span v-else>-</span>
                                        </div>
                                    </div>
                                    <div class="info-item" v-if="detail.otherContactInfo">
                                        <div class="label">其他联系方式：</div>
                                        <div class="content">
                                            <div class="ui-col-10">
                                                <p v-for="p in detail.otherContactInfo.split('##')" :key="p" class="show-font">{{ p }}</p>
                                            </div>
                                            <template v-else>-</template>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">终端名称：</div>
                                        <div class="content">
                                            <div class="ui-col-10">{{ detail.terminalTraderName || '-' }}</div>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">终端性质：</div>
                                        <div class="content">
                                            <div class="ui-col-10">{{ detail.terminalTraderNatureStr || '-' }}</div>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">终端区域：</div>
                                        <div class="content">
                                            <div class="ui-col-10">{{ detail.terminalTraderRegionStr || '-' }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="card detail-card">
                                <div class="card-title">商机信息</div>
                                <div class="card-title-ai">
                                    <ai-category-detail :data="defaultAiData" v-if="defaultAiData.list && defaultAiData.list.length"></ai-category-detail>
                                </div>
                                <div class="info-wrap">
                                    <div class="info-item">
                                        <div class="label">业务类型：</div>
                                        <div class="content">
                                            <div class="ui-col-10">{{ detail.businessTypeStr || '-' }}</div>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">产品信息：</div>
                                        <div class="content">
                                            <div class="ui-col-10">{{ detail.goodsInfo || '-' }}</div>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">预计成单金额：</div>
                                        <div class="content">
                                            <div class="ui-col-10">{{ detail.amount? detail.amount + '元' : '-' }}</div>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">预计成单日期：</div>
                                        <div class="content">
                                            <div class="ui-col-10">{{ detail.orderTimeDate || '-' }}</div>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">标签：</div>
                                        <div class="content">
                                            <div class="ui-col-10">{{ tagsShow || '-' }}</div>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">客情关系：</div>
                                        <div class="content">
                                            <div class="ui-col-10" v-if="detail.customerRelationshipStr && detail.customerRelationshipStr.length">{{ detail.customerRelationshipStr.join(' / ') }}</div>
                                            <div class="ui-col-10" v-else>-</div>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">采购方式：</div>
                                        <div class="content">{{ detail.purchasingTypeStr || '-' }}</div>
                                    </div>
                                    <div class="info-item" v-if="detail.purchasingType == 406">
                                        <div class="label">招投标阶段：</div>
                                        <div class="content">
                                            <div class="ui-col-10">{{ detail.biddingPhaseStr || '-' }}</div>
                                        </div>
                                    </div>
                                    <div class="info-item" v-if="detail.purchasingType == 406">
                                        <div class="label">招标参数：</div>
                                        <div class="content">
                                            <div class="ui-col-10">{{ detail.biddingParameter == 1 ? '可调整' : detail.biddingParameter == 2 ? '不可调整' : '-' }}</div>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">备注：</div>
                                        <div class="content">
                                            <div class="ui-col-10">{{ detail.remark || '-' }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="card card-line" v-if="detail.clueType == 391 || detail.clueType == 394">
                                <div class="card-title">渠道信息</div>
                                <div class="info-wrap">
                                    <div class="info-item">
                                        <div class="label">询价行为：</div>
                                        <div class="content">{{detail.inquiryName || '-'}}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">渠道类型：</div>
                                        <div class="content">{{detail.sourceName || '-'}}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">渠道名称：</div>
                                        <div class="content">{{detail.communicationName || '-'}}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">地区：</div>
                                        <div class="content">{{ area || '-' }}</div>
                                    </div>
                                    <!-- <div class="info-item" >
                                        <div class="label">三级分类：</div>
                                        <div class="content">
                                            <div class="ui-col-10" v-if="detail.content">
                                                <p v-for="p in detail.content.split('&&')" :key="p" class="show-font">{{ p }}</p>
                                            </div>
                                            <template v-else>-</template>
                                        </div>
                                    </div> -->
                                    <div class="info-item">
                                        <div class="label">咨询入口：</div>
                                        <div class="content">
                                            <div class="ui-col-10">{{detail.entrancesName || '-'}}</div>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">功能：</div>
                                        <div class="content">
                                            <div class="ui-col-10">{{detail.functionsName || '-'}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="related-order-card">
                                <div class="quote-title flex">
                                    <div class="left flex">
                                        <div class="title">报价信息</div>
                                        <div class="status no" v-if="!quoteorderId">未报价</div>
                                        <div class="status" v-else :class="'s'+quoteorderInfo.validStatus">{{quoteorderInfo.validStatus ? '已生效': '未生效' }}</div>
                                    </div>
                                    <div class="right flex">
                                        <div class="opt" v-if="!quoteorderId">
                                            <div class="btn" @click="createQuoteorder">添加报价</div>
                                        </div>
                                        <div class="opt" v-else>
                                            <div class="btn" @click="exportQuoteorder" v-if="quoteorderInfo.validStatus">导出报价</div>
                                            <div class="btn" @click="editQuoteorder" v-else>编辑报价</div>
                                            <div class="btn" @click="applyQuoteorder" v-if="quoteorderInfo.validStatus && detail.stage != 6">申请授权书</div>
                                            <div class="btn" @click="reviewQuoteorder" v-if="quoteorderInfo.validStatus">查看报价</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="quoteorder-table" v-if="quoteorderId">
                                    <div class="q-tr">
                                        <div class="q-td" v-show="quoteorderInfo.requirementStatus">
                                            <div class="label">客户需求产品数</div>
                                            <div class="value">{{quoteorderInfo.requiredProductNumber | checkValNull}}</div>
                                        </div>
                                        <div class="q-td" v-show="quoteorderInfo.requirementStatus">
                                            <div class="label">客户需求满足率<div 
                                                class="tip">
                                                    <i class="vd-ui_icon icon-info1"></i>
                                                    <span>报价单中添加产品的客户需求行/总客户需求行</span>
                                                </div>
                                            </div>
                                            <div class="value">{{ quoteorderInfo.requiredSatisfactionRate | checkValNull}}</div>
                                        </div>
                                        <div class="q-td">
                                            <div class="label">报价产品数量</div>
                                            <div class="value">{{ quoteorderInfo.quoteProductNumber | checkValNull}}</div>
                                        </div>
                                        <div class="q-td">
                                            <div class="label">独家产品数量<div 
                                                class="tip">
                                                    <i class="vd-ui_icon icon-info1"></i>
                                                    <span>报价单中贝登“独家代理”产品数量</span>
                                                </div>
                                            </div>
                                            <div class="value">{{ quoteorderInfo.exclusiveProductNumber | checkValNull}}</div>
                                        </div>
                                        <div class="q-td">
                                            <div class="label">需要报备数量</div>
                                            <div class="value">{{ quoteorderInfo.needReportedNumber | checkValNull}}</div>
                                        </div>
                                        <div class="q-td">
                                            <div class="label">已核价产品数量</div>
                                            <div class="value">{{ quoteorderInfo.auditedProductNumber | checkValNull}}</div>
                                        </div>
                                        <div class="q-td">
                                            <div class="label">全部预计货期<div 
                                                class="tip right">
                                                    <i class="vd-ui_icon icon-info1"></i>
                                                    <span>报价单中，所有产品的货期中最长时间</span>
                                                </div>
                                            </div>
                                            <div class="value">{{ quoteorderInfo.estimatedDeliveryTime | checkValNull}}</div>
                                        </div>
                                        <div class="q-td">
                                            <div class="label">报价总金额（元）</div>
                                            <div class="value price">{{ quoteorderInfo.totalQuotedAmount | checkValNull}}</div>
                                        </div>
                                    </div>
                                </div>
                                <div v-else class="null-baojia">
                                    <i class="vd-ui_icon icon-info1"></i>
                                    <p>暂无报价</p>
                                </div>
                            </div>

                            <div class="related-order-card" v-if="relatedOrderDtoList && relatedOrderDtoList.length">
                                <div class="title">关联单据</div>
                                <div class="">
                                    <ui-table 
                                        border
                                        :oneline="true"
                                        :width-border="true" 
                                        :auto-scroll="false" 
                                        :headers="relatedOrderHeaders" 
                                        :list="relatedOrderDtoList"
                                    >
                                        <template v-slot:relatedno="{ row }">
                                            <template v-if="row.relatedNo">
                                                <div class="text-line-1" :title="row.relatedNo">
                                                    <template v-if="row.jumpErpUrl">
                                                        <div class="td-link" style="display: inline-block;" @click="GLOBAL.link({name:'单据详情', url: row.jumpErpInnerUrl, link: row.jumpErpUrl, nohost: true, crmType: row.systemCode || 1})">{{ row.relatedNo }}</div>（{{ GLOBAL.getSysName(row.systemCode) }}）
                                                    </template>
                                                    <template v-else>{{ row.relatedNo }}</template>
                                                </div>
                                            </template>
                                            <template v-else>-</template>
                                        </template>
                                    </ui-table>
                                </div>
                            </div>
                        </div>

                        <div class="right-aside hidden-xs" :style="'top:' + (layout_hidden_value ? '0' : '50px')">
                            <div class="right-aside-inner">
                                <!-- 跟进记录面板 -->
                                <follow-record-list
                                    ref="followRecordList"
                                    v-if="asideIndex == 1"
                                    :communicate-type="244"
                                    :related-id="businessChanceId"
                                    :trader-id="detail.traderId"
                                    :trader-name="detail.traderName"
                                    :trader-name-link="detail.traderNameLink"
                                    :trader-name-inner-link="detail.traderNameInnerLink"
                                    :tyc-flag="detail.tycFlag"

                                    :belonger-id="detail.belongerId"
                                    :belonger="detail.belonger"
                                    :belonger-pic="detail.belongPic"
                                    :contact="detail.contact"
                                    :trader-contact-id="detail.traderContactId"
                                    :phone="detail.phone"
                                    :tele-phone="detail.telephone"
                                    @addrecord="handlerRecordAdd"
                                ></follow-record-list>
                                <!-- 任务记录 -->
                                <renwu-list
                                    ref="renwuList"
                                    v-if="asideIndex == 3"
                                    biz-type="1"
                                    :disabled="detail.stage === 6"
                                    :related-id="businessChanceId"
                                    :quoteorder-id="quoteorderId"
                                    @refresh="getTaskNum"
                                ></renwu-list>
                                <!-- 操作记录面板 -->
                                <operation-log 
                                    ref="operationLog" 
                                    v-if="asideIndex == 2"
                                    :quoteorder-id="quoteorderId"
                                    biz-type-enum="02"
                                    :related-id="businessChanceId"
                                ></operation-log>
                                <!-- 协作人面板 -->
                                <partner-list
                                    ref="partnerList"
                                    v-if="asideIndex == 4"
                                    :belonger-id="detail.belongerId"
                                    :related-id="businessChanceId"
                                    :business-type="1"
                                    :business-no="detail.bussinessChanceNo"
                                ></partner-list>
                            </div>
                        </div>
                    </div>
                </div>

                <ui-dialog
                    :visible.sync="isShowToOrder"
                    :title="GLOBAL.checkCrmType().label + '-转订单'"
                    width="720px"
                >
                    <div class="form-wrap label-width-2">
                        <ui-form-item label="" :text="true">
                            <div class="ui-col-8 order-type">
                                <ui-radio-group
                                    :list="ToOrderTypes"
                                    :value.sync="orderType"
                                    average-num="1"
                                    valid="ToOrderValid_orderType"
                                ></ui-radio-group>
                            </div>
                        </ui-form-item>
                        <ui-form-item label="" v-if="orderType == '2'" style="margin-top: -10px;">
                            <div class="ui-col-7">
                                <ui-input
                                    width="260px"
                                    height="75px"
                                    placeholder="请输入销售单号"
                                    show-word-limit
                                    maxlength="50"
                                    @change="chackOrderNo"
                                    v-model="saleOrderNo"
                                    :errorable="!!saleOrderNoError"
                                    :error-msg="saleOrderNoError"
                                ></ui-input>
                            </div>
                        </ui-form-item>
                    </div>
                    <template slot="footer">
                        <div class="dlg-form-footer">
                            <ui-button @click="validSubmit" type="primary">提交</ui-button>
                            <ui-button @click="isShowToOrder=false" class="close">取消</ui-button>
                        </div>
                    </template>
                </ui-dialog>

                <ui-dialog
                    :visible.sync="isShowCloseDialog"
                    title="关闭商机"
                    width="720px"
                >
                    <div class="form-wrap label-width-2">
                        <ui-form-item label="关闭原因" :must="true">
                            <div class="ui-col-4">
                                <ui-select
                                    width="323px"
                                    placeholder="请选择关闭原因"
                                    :data="CloseReasonTypes"
                                    v-model="closeReasonType"
                                    clearable
                                    valid="CloseLeads_closeReasonType"
                                ></ui-select>
                            </div>
                        </ui-form-item>

                        <ui-form-item label="说明">
                            <div class="ui-col-4">
                                <ui-input
                                    type="textarea"
                                    width="440px"
                                    height="75px"
                                    show-word-limit
                                    maxlength="200"
                                    v-model="closeRemark"
                                ></ui-input>
                            </div>
                        </ui-form-item>
                    </div>
                    <template slot="footer">
                        <div class="dlg-form-footer">
                            <ui-button @click="closeTheLeads" type="primary">提交</ui-button>
                            <ui-button @click="isShowCloseDialog=false" class="close">取消</ui-button>
                        </div>
                    </template>
                </ui-dialog>

                <!-- 跟进记录 dialog -->
                <follow-record-list-dialog ref="followRecordListDialog" :communicate-type="244" :refresh-detail-list="refreshFollowPanel" @addrecord="handlerRecordAdd"></follow-record-list-dialog>
                <!-- 任务 dialog -->
                <renwu-dialog ref="renwuDialog" :refresh-panel="refreshRenwuPanel"></renwu-dialog>
                <!-- 操作记录 dialog -->
                <operation-log-dialog ref="operationLogDialog"></operation-log-dialog>
                <!-- 协作人 dialog -->
                <partner-list-dialog ref="partnerListDialog" :refresh-panel="refreshPartnerList"></partner-list-dialog>
                <!-- 任务处理 -->
                <renwu-handle ref="renwuHandle"></renwu-handle>
                <!-- 任务查看 -->
                <renwu-review ref="renwuReview"></renwu-review>
                <!-- 天眼查详情 -->
                <ui-tyc-detail ref="tycDetail"></ui-tyc-detail>
            </div>
        </div>
    </div>

    <script src="/static/js/common/vue.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/axios.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/lodash.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/moment.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/ui/ui.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/layout.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <!-- 跟进记录相关 -->
    <script src="/static/js/common/components/business/records.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <!-- 任务记录 -->
    <script src="/static/js/common/components/business/renwu.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <!-- 操作记录 -->
    <script src="/static/js/common/components/business/operationLog.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/components/business/operationLog-dialog.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <!-- 协作人 -->
    <script src="/static/js/common/components/business/partner.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <!-- 选产品弹层 -->
    <script src="/static/js/common/components/selectProd.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <!-- 线索详情 -->
    <script src="/static/js/pages/businesschanceDetail.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
</body>
</html>
