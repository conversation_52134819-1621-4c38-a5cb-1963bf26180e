package com.vedeng.common.core.aspectj;

import cn.hutool.core.util.StrUtil;
import com.vedeng.common.core.annotation.NoRepeatSubmit;
import com.vedeng.common.core.base.BaseResponseCode;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.utils.ServletUtils;
import com.vedeng.common.redis.redission.RedissonLockUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.TimeUnit;

/**
 * 防重复提交，使用redis锁
 *
 * <AUTHOR>
 * @date 2020/05/16
 */
@Aspect
@Component
@Slf4j
public class RepeatSubmitAspect {

    /**
     * 最大可多等待3秒
     */
    private final static int WAIT_TIME = 3;


    @Pointcut("@annotation(noRepeatSubmit)")
    public void pointCut(NoRepeatSubmit noRepeatSubmit) {

    }

    @Around(value = "pointCut(noRepeatSubmit)", argNames = "pjp,noRepeatSubmit")
    public Object around(ProceedingJoinPoint pjp, NoRepeatSubmit noRepeatSubmit) throws Throwable {
        int lockSeconds = noRepeatSubmit.lockTime();
        HttpServletRequest request = ServletUtils.getRequest();
        CurrentUser currentUser = (CurrentUser) ServletUtils.getRequest().getSession().getAttribute(ErpConstant.CURRENT_USER);
        if (currentUser == null) {
            return R.error(BaseResponseCode.USER_NOT_LOGIN);
        }
        String path = request.getServletPath();
        String key = getKey(currentUser.getUsername(), path);
        if (RedissonLockUtils.tryLock(key, WAIT_TIME, lockSeconds, TimeUnit.SECONDS)) {
            log.info("加锁成功, key = [{}]", key);
            Object result;
            try {
                result = pjp.proceed();
            } finally {
                RedissonLockUtils.unlock(key);
                log.info("释放锁成功, key = [{}]", key);
            }
            return result;
        } else {
            log.error("释放锁失败,重复提交, key = [{}]", key);
            return R.error(BaseResponseCode.OPERATION_PROCESS_ERROR);
        }
    }

    private String getKey(String token, String path) {
        return token + StrUtil.UNDERLINE + path;
    }

}
