package com.vedeng.erp.kingdee.batch.repository;
import java.util.Date;

import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInItemDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BatchWarehouseGoodsOutInDtoMapper {
    /**
     * 查询全部
     *
     * @param batchWarehouseGoodsOutInDto 条件
     * @return 数据
     */
    List<BatchWarehouseGoodsOutInDto> findByAll(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto);

    /**
     * 采购入库 排除赠品单
     *
     * @param batchWarehouseGoodsOutInDto 条件
     * @return 数据
     */
    List<BatchWarehouseGoodsOutInDto> purchaseInFindByAll(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto);

    /**
     * 采购入库 排除赠品单
     *
     * @param batchWarehouseGoodsOutInDto 条件
     * @return 数据
     */
    List<BatchWarehouseGoodsOutInDto> giftBuyOrderFindByAll(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto);

    /**
     * 采购售后出库 赠品单
     *
     * @param batchWarehouseGoodsOutInDto
     * @return
     */
    List<BatchWarehouseGoodsOutInDto> giftBuyOrderOutFindByAll(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto);

    /**
     * 采购赠品单售后换货入库、出库
     *
     * @param batchWarehouseGoodsOutInDto
     * @return
     */
    List<BatchWarehouseGoodsOutInDto> handleGiftBuyOrderFindAll(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto);


    /**
     * 采购退货售后出库 排除赠品单
     *
     * @param batchWarehouseGoodsOutInDto
     * @return
     */
    List<BatchWarehouseGoodsOutInDto> buyOrderOutFindByAll(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto);


    /**
     * 销售售后退货入库
     *
     * @param batchWarehouseGoodsOutInDto
     * @return
     */
    List<BatchWarehouseGoodsOutInDto> saleOrderInFindByAll(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto);

    /**
     * 根据销售订单号获取税率
     *
     * @param afterSalesNo
     * @return
     */
    String saleOrderGetRate(String afterSalesNo);

    int insertSelective(BatchWarehouseGoodsOutInDto record);

    /**
     * 批量查询销售单商品出库单信息
     *
     * @param batchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto
     * @return List<BatchWarehouseGoodsOutInDto>
     */
    List<BatchWarehouseGoodsOutInDto> findSaleOrderGoodsBatch(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto);


    /**
     * 获取出库 包含销售赠品商品的同时满足开票条件条件
     * @param batchWarehouseGoodsOutInDto 出库单
     * @return List<BatchWarehouseGoodsOutInDto>
     */
    List<BatchWarehouseGoodsOutInDto> findSaleOrderGiftGoodsAndHaveInvoice(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto);


    /**
     * 获取出库 包含销售赠品商品的同时满足开票条件条件  整单赠品
     * @param batchWarehouseGoodsOutInDto 出库单
     * @return List<BatchWarehouseGoodsOutInDto>
     */
    List<BatchWarehouseGoodsOutInDto> findWholeSaleOrderGiftGoodsAndHaveInvoice(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto);

    /**
     * 获取入库 包含售后销售赠品商品的同时满足开票条件条件
     * @param batchWarehouseGoodsOutInDto 退货入库单
     * @return List<BatchWarehouseGoodsOutInDto>
     */
    List<BatchWarehouseGoodsOutInDto> findSaleOrderGiftGoodsAfterSaleAndHaveInvoice(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto);

    /**
     * 根据出入库记录id查询对应的明细信息集合
     *
     * @param outInNo 出入库单号
     * @return List<BatchWarehouseGoodsOutInItemDto>
     */
    List<BatchWarehouseGoodsOutInItemDto> getWarehouseGoodsOutInItemByWarehouseId(@Param("outInNo") String outInNo);

    /**
     * 根据ERP业务单号和出入库类型查询对应的出入库日志记录
     *
     * @param relateNo  ERP业务单号
     * @param outInType 出入库类型
     * @return List<BatchWarehouseGoodsOutInDto>
     */
    List<BatchWarehouseGoodsOutInDto> getByRelateNo(@Param("relateNo") String relateNo, @Param("outInType") Integer outInType);

    /**
     * 根据出入库单号和类型查询信息
     *
     * @param outInNo
     * @param outInType
     * @return
     */
    BatchWarehouseGoodsOutInDto queryInfoByNo(@Param("outInNo") String outInNo, @Param("outInType") Integer outInType);

    /**
     * 根据业务单号查询信息
     *
     * @param batchWarehouseGoodsOutInDto
     * @return
     */
    List<BatchWarehouseGoodsOutInDto> findByRelateNo(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto);

    /**
     * 根据条件查询信息
     *
     * @param batchWarehouseGoodsOutInDto
     * @return
     */
    List<BatchWarehouseGoodsOutInDto> findByItem(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto);


    BatchWarehouseGoodsOutInDto findByOutInNo(@Param("outInNo") String outInNo);


    /**
     * 获取销售订单的交易者名称
     *
     * @param orderNo
     * @return
     */
    String getSaleTraderNameBySaleOrder(@Param("orderNo") String orderNo);

    /**
     * 根据关联单号查询出库详情记录
     *
     * @param orderNos
     * @param goodsId
     * @return
     */
    List<String> getBarcodFactoryListByOrderNo(@Param("orderNos") List<String> orderNos, @Param("goodsId") Integer goodsId);

    List<String> getGiftBarcodFactoryListByOrderNo(@Param("orderNos") List<String> orderNos, @Param("goodsId") Integer goodsId);

    List<String> getExistBarcodFactoryList(@Param("orderNo") String orderNo, @Param("goodsId") Integer goodsId);

    List<String> getBatchNumberListByOrderNo(@Param("orderNo") String orderNo, @Param("goodsId") Integer goodsId);

    List<String> getGiftBatchNumberListByOrderNo(@Param("orderNo") String orderNo, @Param("goodsId") Integer goodsId);

    /**
     * 查询
     * @param itemIds
     * @return
     */
    List<BatchWarehouseGoodsOutInDto> selectWarehouseOutInOrder(@Param("itemIds") List<Integer> itemIds);

    List<String> getUsedBarcodFactoryList(@Param("orderNo") String orderNo, @Param("goodsId") Integer goodsId);

    List<BatchWarehouseGoodsOutInDto> findByIsVirtualAndRelateNo(@Param("isVirtual")Integer isVirtual,@Param("relateNo")String relateNo);

    int batchInsert(@Param("list") List<BatchWarehouseGoodsOutInDto> list);

    /**
     * 正向销售调整单捞取数据
     */
    List<BatchWarehouseGoodsOutInDto> querySaleAdjustPriceNew(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto);


    /**
     * 逆向销售调整单推送捞取数据
     */
    List<BatchWarehouseGoodsOutInDto> queryAfterSaleAdjustmentPushNew(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto);

    /**
     * 正向销售调整单推送捞取数据
     */
    List<BatchWarehouseGoodsOutInDto> querySaleAdjustmentPushNew(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto);
}