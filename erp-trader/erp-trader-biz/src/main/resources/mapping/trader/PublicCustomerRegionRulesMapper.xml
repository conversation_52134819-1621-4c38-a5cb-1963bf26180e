<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.PublicCustomerRegionRulesMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.PublicCustomerRegionRules">
    <id column="PUBLIC_CUSTOMER_REGION_RULES_ID" jdbcType="INTEGER" property="publicCustomerRegionRulesId" />
    <result column="REGION_ID" jdbcType="INTEGER" property="regionId" />
    <result column="USER_ID" jdbcType="INTEGER" property="userId" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
  </resultMap>

  <resultMap id="myResultMap" type="com.vedeng.erp.trader.domain.vo.PublicCustomerRegionRulesVo">
    <id column="PUBLIC_CUSTOMER_REGION_RULES_ID" jdbcType="INTEGER" property="publicCustomerRegionRulesId" />
    <result column="REGION_ID" jdbcType="INTEGER" property="regionId" />
    <result column="USER_ID" jdbcType="INTEGER" property="userId" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="zoneId" jdbcType="INTEGER" property="zoneId" />
    <result column="zoneName" jdbcType="VARCHAR" property="zoneName" />
    <result column="cityId" jdbcType="INTEGER" property="cityId" />
    <result column="cityName" jdbcType="VARCHAR" property="cityName" />
    <result column="provinceId" jdbcType="INTEGER" property="provinceId" />
    <result column="provinceName" jdbcType="VARCHAR" property="provinceName" />
    <result column="ORG_ID" jdbcType="INTEGER" property="orgId" />
    <result column="USERNAME" jdbcType="VARCHAR" property="username" />
  </resultMap>

  <sql id="Base_Column_List">
    PUBLIC_CUSTOMER_REGION_RULES_ID, REGION_ID, USER_ID, ADD_TIME, CREATOR, MOD_TIME, 
    UPDATER
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_PUBLIC_CUSTOMER_REGION_RULES
    where PUBLIC_CUSTOMER_REGION_RULES_ID = #{publicCustomerRegionRulesId,jdbcType=INTEGER}
  </select>
  <select id="selectByRegionId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_PUBLIC_CUSTOMER_REGION_RULES
    where REGION_ID = #{regionId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_PUBLIC_CUSTOMER_REGION_RULES
    where PUBLIC_CUSTOMER_REGION_RULES_ID = #{publicCustomerRegionRulesId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="PUBLIC_CUSTOMER_REGION_RULES_ID" keyProperty="publicCustomerRegionRulesId" parameterType="com.vedeng.erp.trader.domain.PublicCustomerRegionRules" useGeneratedKeys="true">
    insert into T_PUBLIC_CUSTOMER_REGION_RULES (REGION_ID, USER_ID, ADD_TIME, 
      CREATOR, MOD_TIME, UPDATER
      )
    values (#{regionId,jdbcType=INTEGER}, #{userId,jdbcType=INTEGER}, #{addTime,jdbcType=BIGINT}, 
      #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="PUBLIC_CUSTOMER_REGION_RULES_ID" keyProperty="publicCustomerRegionRulesId" parameterType="com.vedeng.erp.trader.domain.PublicCustomerRegionRules" useGeneratedKeys="true">
    insert into T_PUBLIC_CUSTOMER_REGION_RULES
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="regionId != null">
        REGION_ID,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="regionId != null">
        #{regionId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <insert id="batchAddPublicCustomerCalculateRules" parameterType="com.vedeng.erp.trader.domain.PublicCustomerRegionRules" >
    insert into T_PUBLIC_CUSTOMER_REGION_RULES (REGION_ID,USER_ID,ADD_TIME,CREATOR,MOD_TIME,UPDATER) values
    <foreach collection="list" item="i" separator=",">
      (#{i.regionId},#{i.userId},#{i.addTime},#{i.creator},#{i.modTime},#{i.updater})
    </foreach>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.PublicCustomerRegionRules">
    update T_PUBLIC_CUSTOMER_REGION_RULES
    <set>
      <if test="regionId != null">
        REGION_ID = #{regionId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where PUBLIC_CUSTOMER_REGION_RULES_ID = #{publicCustomerRegionRulesId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.PublicCustomerRegionRules">
    update T_PUBLIC_CUSTOMER_REGION_RULES
    set REGION_ID = #{regionId,jdbcType=INTEGER},
      USER_ID = #{userId,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER}
    where PUBLIC_CUSTOMER_REGION_RULES_ID = #{publicCustomerRegionRulesId,jdbcType=INTEGER}
  </update>

  <select id="queryListPage" parameterType="java.util.Map" resultMap="myResultMap">
    select a.*,u.USERNAME,
           zone.REGION_ID zoneId,
           zone.REGION_NAME zoneName,
           city.REGION_ID cityId,
           city.REGION_NAME cityName,
           province.REGION_ID provinceId,
           province.REGION_NAME provinceName
    from T_PUBLIC_CUSTOMER_REGION_RULES a
           left join T_USER u on a.USER_ID = u.USER_ID
--            left join T_R_USER_POSIT up on up.USER_ID = u.USER_ID
--            left join T_POSITION  pp on pp.POSITION_ID = up.POSITION_ID and pp.TYPE = 310
           left join T_REGION zone on zone.REGION_ID = a.REGION_ID
      left join T_REGION city on city.REGION_ID = zone.PARENT_ID
      left join T_REGION province on province.REGION_ID = city.PARENT_ID
       <where>
         <if test="regionRulesQueryDto.userId != null and regionRulesQueryDto.userId != -1">
           and a.USER_ID = #{regionRulesQueryDto.userId}
         </if>
         <if test="regionRulesQueryDto.zone != null and regionRulesQueryDto.zone != 0">
           and a.REGION_ID = #{regionRulesQueryDto.zone}
         </if>
         <if test="regionRulesQueryDto.city != null and regionRulesQueryDto.city !=0 ">
           and city.REGION_ID = #{regionRulesQueryDto.city}
         </if>
         <if test="regionRulesQueryDto.province !=null and regionRulesQueryDto.province!=0">
           and province.REGION_ID = #{regionRulesQueryDto.province}
         </if>
         <if test="regionRulesQueryDto.regionId !=null and regionRulesQueryDto.regionId!=0">
           and a.REGION_ID = #{regionRulesQueryDto.regionId}
         </if>
       </where>
        order by ADD_TIME DESC
  </select>

  <delete id="batchDeleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_PUBLIC_CUSTOMER_REGION_RULES
    where PUBLIC_CUSTOMER_REGION_RULES_ID in
    <foreach collection="list" item="it" separator="," open="(" close=")">
      #{it}
    </foreach>


  </delete>


  <select id="queryCustomerRegionRulesList" resultMap="myResultMap">
    select
      a.USER_ID,
      u.USERNAME,
      zone.REGION_ID zoneId,
      zone.REGION_NAME zoneName,
      city.REGION_ID cityId,
      city.REGION_NAME cityName,
      province.REGION_ID provinceId,
      province.REGION_NAME provinceName
    from
         T_CUSTOMER_REGION_SALE a
    left join T_USER u on a.USER_ID = u.USER_ID
    left join T_REGION zone on zone.REGION_ID = a.REGION_ID
    left join T_REGION city on city.REGION_ID = zone.PARENT_ID
    left join T_REGION province on province.REGION_ID = city.PARENT_ID
    <where>
      <if test="regionIdList != null ">
        and a.REGION_ID in
        <foreach close=")" collection="regionIdList" item="item" open="(" separator=", ">
          #{item,jdbcType=INTEGER}
        </foreach>
      </if>
      <if test="userIdList != null ">
        and (a.USER_ID_DOWN in
        <foreach close=")" collection="userIdList" item="item" open="(" separator=", ">
          #{item,jdbcType=INTEGER}
        </foreach>
        OR 
        a.USER_ID in
        <foreach close=")" collection="userIdList" item="item" open="(" separator=", ">
          #{item,jdbcType=INTEGER}
        </foreach>
        )
      </if>
    </where>
  </select>
</mapper>