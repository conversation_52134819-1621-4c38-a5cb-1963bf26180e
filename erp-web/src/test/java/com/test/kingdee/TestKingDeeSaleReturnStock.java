package com.test.kingdee;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.vedeng.erp.kingdee.domain.command.KingDeeSaleBackCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeSaleReturnStockEntity;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSaleBackCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSaleBackConvertor;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeSaleReturnStockMapper;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 销售退货入库单
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring.xml", "classpath:spring-mybatis.xml"})
public class TestKingDeeSaleReturnStock {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Resource
    private KingDeeSaleBackCommandConvertor commandConvertor;

    @Resource
    private KingDeeSaleBackConvertor purchaseReceiptConvertor;

    @Autowired
    private KingDeeSaleReturnStockMapper kingDeeSaleReturnStockMapper;


    @Test
    public void kingDeePurchaseReceipt() {
        KingDeeSaleReturnStockDto dto = new KingDeeSaleReturnStockDto();
        dto.setFid("0");
        dto.setFBillTypeID("XSTHD01_SYS");
        dto.setFBillNo("ckth-0009");
        dto.setFQzokBddjtid("贝登erp对应的单据头ID");
        dto.setFDate("2023-01-13");
        dto.setFSaleOrgId("101");
        dto.setFStockOrgId("101");
        dto.setFRetcustId("KH0001");


        List<KingDeeSaleReturnStockDetailDto> FEntityDetail = new ArrayList<>();
        KingDeeSaleReturnStockDetailDto kingDeeSaleReturnStockDetailDto = new KingDeeSaleReturnStockDetailDto();
        kingDeeSaleReturnStockDetailDto.setFmaterialid("sku0001");
        kingDeeSaleReturnStockDetailDto.setFRealQty(new BigDecimal("1"));
        kingDeeSaleReturnStockDetailDto.setFstockid("ck9999");
        kingDeeSaleReturnStockDetailDto.setFtaxprice(new BigDecimal("113"));
        kingDeeSaleReturnStockDetailDto.setFentrytaxrate(new BigDecimal("13.00"));
        kingDeeSaleReturnStockDetailDto.setFQzokYsddh("原始订单号");
        kingDeeSaleReturnStockDetailDto.setFQzokGsywdh("归属业务单号");
        kingDeeSaleReturnStockDetailDto.setFQzokYwlx("业务类型");
        kingDeeSaleReturnStockDetailDto.setFQzokPch("批次号");
        kingDeeSaleReturnStockDetailDto.setFQzokXlh("序列号");
        kingDeeSaleReturnStockDetailDto.setFQzokBddjhid("贝登订单行ID");
        kingDeeSaleReturnStockDetailDto.setFQzokSfzf("是否直发");
        kingDeeSaleReturnStockDetailDto.setFSrcBillTypeID("SAL_OUTSTOCK");

        List<KingDeeSaleReturnStockDetailLink> fentityLink = new ArrayList<>();
        KingDeeSaleReturnStockDetailLink kingDeeSaleReturnStockDetailLink = new KingDeeSaleReturnStockDetailLink();
        kingDeeSaleReturnStockDetailLink.setFLinkId(0);
        kingDeeSaleReturnStockDetailLink.setFentityLinkFruleid("OutStock-SalReturnStock");
        kingDeeSaleReturnStockDetailLink.setFentityLinkFflowlineid(0);
        kingDeeSaleReturnStockDetailLink.setFentityLinkFstableid(0);
        kingDeeSaleReturnStockDetailLink.setFentityLinkFstablename("T_SAL_OUTSTOCKENTRY");
        kingDeeSaleReturnStockDetailLink.setFentityLinkFsbillid("113480");
        kingDeeSaleReturnStockDetailLink.setFentityLinkFsid("339963");
        kingDeeSaleReturnStockDetailLink.setFentityLinkFbaseunitqtyold(new BigDecimal("0"));
        kingDeeSaleReturnStockDetailLink.setFentityLinkFbaseunitqty(new BigDecimal("1"));
        kingDeeSaleReturnStockDetailLink.setFentityLinkFsalbaseqtyold(new BigDecimal("0"));
        kingDeeSaleReturnStockDetailLink.setFentityLinkFsalbaseqty(new BigDecimal("1"));



        fentityLink.add(kingDeeSaleReturnStockDetailLink);
        kingDeeSaleReturnStockDetailDto.setFentityLink(fentityLink);
        FEntityDetail.add(kingDeeSaleReturnStockDetailDto);
        dto.setFEntity(FEntityDetail);

        KingDeeSaleBackCommand command = commandConvertor.toCommand (dto);

        ArrayList<String> needReturnFields = new ArrayList<>();
        needReturnFields.add("FEntity.FEntryID");
        needReturnFields.add("FEntity.F_QZOK_BDDJHID");
        needReturnFields.add("FEntity.FEntity_Link.FLinkId");
        needReturnFields.add("FEntity.FEntity_Link.FEntity_Link_FSId");

        System.out.println("入参："+JSON.toJSONString(command));
        kingDeeBaseApi.save(new SaveExtCommand<>(command, dto.getFormId(), needReturnFields));

    }
}
