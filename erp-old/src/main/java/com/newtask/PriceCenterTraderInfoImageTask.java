package com.newtask;

import com.newtask.model.TraderInfoImageDto;
import com.newtask.service.PriceCenterTraderInfoImageService;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.JsonUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@JobHandler(value = "PriceCenterTraderInfoImageTask")
@Component
public class PriceCenterTraderInfoImageTask extends AbstractJobHandler {

    @Value("${price.url}")
    private String priceUrl;
    @Autowired
     PriceCenterTraderInfoImageService priceCenterTraderInfoImageService;
    private static String TRADER_INFO_IMAGE = "trader/traderInfoImage";

    Logger logger = LoggerFactory.getLogger(PriceCenterTraderInfoImageTask.class);

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("价格中心同步供应商名称 -----------");
        logger.info("价格中心同步供应商名称start-----------");
        String requestJson="";
        List<TraderInfoImageDto> list = priceCenterTraderInfoImageService.getTraderInfoImage();
        if (param.equals("first")){
            List<TraderInfoImageDto> listAll = priceCenterTraderInfoImageService.getTraderInfoImageAll();
              requestJson = JsonUtils.translateToJson(listAll);
            JSONObject resultJsonObj= NewHttpClientUtils.httpPost(priceUrl + TRADER_INFO_IMAGE,requestJson);
        }else {
              requestJson = JsonUtils.translateToJson(list);
            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + TRADER_INFO_IMAGE, requestJson);
        }
        logger.info("价格中心同步供应商名称end-----------{}",requestJson);
        XxlJobLogger.log("价格中心同步供应商名称end-----------",requestJson);
        return SUCCESS;

    }
}
