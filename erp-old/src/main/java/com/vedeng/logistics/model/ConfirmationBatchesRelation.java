package com.vedeng.logistics.model;

import java.io.Serializable;

/**
 * 确认单-批次关系表
 * @TableName T_CONFIRMATION_BATCHES_RELATION
 */

public class ConfirmationBatchesRelation implements Serializable {
    /**
     * ID
     */
    private Integer id;

    /**
     * 销售订单ID
     */
    private Integer saleorderId;

    /**
     * 确认单id
     */
    private Integer confirmationId;

    /**
     * 批次编码
     */
    private String batchNo;


    /**
     * 是否删除,0未删除,1已删除
     */
    private Integer isEnable;

    /**
     * 操作人
     */
    private Integer creator;

    /**
     * 最近一次编辑时间
     */
    private Long modTime;

    /**
     * 最近一次编辑人
     */
    private Integer updater;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 快递id
     */
    private Integer expressId;


    /**
     * 批次审核是否上传  0是 1否
     */
    private Integer uploadStatus;

    /**
     * 批次审核状态 0通过，1驳回
     */
    private Integer auditStatus;

    private static final long serialVersionUID = 1L;


    public Integer getUploadStatus() {
        return uploadStatus;
    }

    public void setUploadStatus(Integer uploadStatus) {
        this.uploadStatus = uploadStatus;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public Integer getExpressId() {
        return expressId;
    }

    public void setExpressId(Integer expressId) {
        this.expressId = expressId;
    }


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getSaleorderId() {
        return saleorderId;
    }

    public void setSaleorderId(Integer saleorderId) {
        this.saleorderId = saleorderId;
    }

    public Integer getConfirmationId() {
        return confirmationId;
    }

    public void setConfirmationId(Integer confirmationId) {
        this.confirmationId = confirmationId;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }


    public Integer getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(Integer isEnable) {
        this.isEnable = isEnable;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Long getModTime() {
        return modTime;
    }

    public void setModTime(Long modTime) {
        this.modTime = modTime;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    public ConfirmationBatchesRelation(Integer saleorderId) {
        this.saleorderId = saleorderId;
    }

    public ConfirmationBatchesRelation(Integer id, Integer saleorderId, Integer confirmationId, String batchNo, Integer isEnable, Integer creator, Long modTime, Integer updater, Long addTime, Integer expressId, Integer uploadStatus, Integer auditStatus) {
        this.id = id;
        this.saleorderId = saleorderId;
        this.confirmationId = confirmationId;
        this.batchNo = batchNo;
        this.isEnable = isEnable;
        this.creator = creator;
        this.modTime = modTime;
        this.updater = updater;
        this.addTime = addTime;
        this.expressId = expressId;
        this.uploadStatus = uploadStatus;
        this.auditStatus = auditStatus;
    }
    public ConfirmationBatchesRelation() {
    }


}