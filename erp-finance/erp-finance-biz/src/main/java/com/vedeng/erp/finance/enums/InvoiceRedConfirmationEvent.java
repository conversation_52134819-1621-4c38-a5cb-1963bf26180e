package com.vedeng.erp.finance.enums;

/**
 * 确认单执行事件
 *
 * <AUTHOR>
 */
public enum InvoiceRedConfirmationEvent {

    /**
     * 系统初始化
     */
    SYS_INIT("-1", "系统初始化"),

    /**
     * 用户初始化
     */
    USER_INIT("0", "用户初始化"),

    /**
     * 申请
     */
    APPLY("1", "申请"),

    /**
     * 同意
     */
    AGREE("2", "同意"),

    /**
     * 拒绝
     */
    REJECT("3", "拒绝"),

    /**
     * 开票
     */
    OPEN_INVOICE("4", "开票"),

    /**
     * 撤销
     */
    CANCEL("5", "撤销"),

    /**
     * 作废
     */
    INVALID("6", "作废"),

    /**
     * 更新
     */
    RENEW("7", "更新");



    /**
     * code
     */
    private final String code;
    /**
     * 事件名称
     */
    private final String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    InvoiceRedConfirmationEvent(String code, String name) {
        this.code = code;
        this.name = name;
    }
}
