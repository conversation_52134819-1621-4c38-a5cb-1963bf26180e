package com.vedeng.erp.kingdee.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.dto.result.KingDeePurchaseInitReceiptQueryResultDto;
import com.vedeng.erp.kingdee.service.KingDeePurchaseInitInStockService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryExtParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryFilterDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/3/7 16:11
 **/
@Service
@Slf4j
public class KingDeePurchaseInitInStockServiceImpl implements King<PERSON>eePurchaseInitInStockService {


    @Autowired
    KingDeeBaseApi kingDeeBaseApi;


    @Override
    public List<KingDeePurchaseInitReceiptQueryResultDto> getKingDeePurchaseInitInStock(String outInNo) {
        // 调用查询金蝶接口
        KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        queryParam.setFormId(KingDeeFormConstant.STK_INIT_IN_STOCK);
        List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("fBillNo").value(outInNo).build());
        queryParam.setFilterString(queryFilterDtos);
        log.info("金蝶期初采购入库单查询入参：{}", JSON.toJSONString(queryParam));
        return kingDeeBaseApi.query(queryParam, KingDeePurchaseInitReceiptQueryResultDto.class);
    }


}
