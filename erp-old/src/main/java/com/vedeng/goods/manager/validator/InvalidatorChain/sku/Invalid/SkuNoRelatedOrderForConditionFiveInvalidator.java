package com.vedeng.goods.manager.validator.InvalidatorChain.sku.Invalid;

import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.exception.InvalidatorChainException;
import com.vedeng.goods.manager.validator.GoodsInvalidatorChain;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.vedeng.goods.manager.validator.enums.GoodsInvalidatorErrorEnum.*;

/**
 * (5)	该SKU没有待确认、进行中的采购售后退货单、采购售后换货单、销售售后换货单、销售单；
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2021/9/1 16:00.
 * @author: Randy.Xu.
 */
@Service
public class SkuNoRelatedOrderForConditionFiveInvalidator extends GoodsInvalidatorChain {

    @Autowired
    AfterSalesMapper afterSalesMapper;

    @Resource
    SaleorderMapper saleorderMapper;

    @Resource
    CoreSkuMapper coreSkuMapper;

    @Override
    public void doInvalid() {
        String skuNo = getSkuNo();
        List<Integer> goodsIds = coreSkuMapper.getGoodsIdBySkuNo(skuNo);
        List<AfterSales> afterSalesList = afterSalesMapper.getRelatedAftersalesOrderConditionFive(goodsIds);
        List<Saleorder> saleorderList = saleorderMapper.getRelatedSaleOrderInprocess(skuNo);
        StringBuffer sb = new StringBuffer();
        if(CollectionUtils.isNotEmpty(saleorderList)){
            String collect = saleorderList.stream().map(e -> e.getSaleorderNo()).collect(Collectors.joining(","));
            sb.append(collect);
            sb.append(",");
        }
        if(CollectionUtils.isNotEmpty(afterSalesList)){
            String collect = afterSalesList.stream().map(e -> e.getAfterSalesNo()).collect(Collectors.joining(","));
            sb.append(collect);
            sb.append(",");
        }
        if(sb.length()>0){
            String s = sb.substring(0,sb.length()-1);
            String orderNoStr = SkuNoRelatedOrderForConditionFiveInvalidatorError.getMessage().replace("orderNo", s);
            throw new InvalidatorChainException(SkuNoRelatedOrderForConditionFiveInvalidatorError.getCode(),orderNoStr);
        }

    }
}
