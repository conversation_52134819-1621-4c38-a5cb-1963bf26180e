package com.vedeng.erp.kingdee.batch.writer;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.batch.dto.BatchKingDeePurchaseReceiptDto;
import com.vedeng.erp.kingdee.domain.entity.KingDeeStorageInEntity;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeStorageInCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeStorageInConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeStorageInRepository;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeStorageInMapper;
import com.vedeng.erp.kingdee.service.KingDeeStorageInApiService;
import com.vedeng.erp.kingdee.service.KingDeeStorageInService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购赠品入库
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class PurchaseGiftInWriter extends BaseWriter<KingDeeStorageInDto> {


    @Autowired
    private KingDeeStorageInApiService kingDeeStorageInApiService;

    @Autowired
    private KingDeeStorageInService kingDeeStorageInService;

    @Autowired
    private KingDeeStorageInRepository kingDeeStorageInRepository;

    @Override
    public void doWrite(KingDeeStorageInDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {

        log.info("采购赠品入库:{}", JSON.toJSONString(dto));
        dto.setKingDeeBizEnums(KingDeeBizEnums.saveStorageIn);
        kingDeeStorageInApiService.register(dto,true);

        kingDeeStorageInService.query(dto);
        KingDeeStorageInEntity kingDeeStorageInEntity = new KingDeeStorageInEntity();
        kingDeeStorageInEntity.setFBillNo(dto.getFBillNo());
        List<KingDeeStorageInEntity> kingDeeStorageInEntities = kingDeeStorageInRepository.queryByObject(kingDeeStorageInEntity);
        if (CollUtil.isNotEmpty(kingDeeStorageInEntities)) {
            KingDeeStorageInEntity data = kingDeeStorageInEntities.get(0);
            BatchKingDeePurchaseReceiptDto build = BatchKingDeePurchaseReceiptDto.builder()
                    .outInNo(data.getFBillNo())
                    .warehouseGoodsOutInId(Long.valueOf(data.getFQzokBddjtId()))
                    .fId(data.getFId())
                    .build();
            List<BatchKingDeePurchaseReceiptDto> purchaseInData = (List<BatchKingDeePurchaseReceiptDto>)getStepParameter("purchaseInData");
            List<BatchKingDeePurchaseReceiptDto> batchKingDeePurchaseReceiptDtos = new ArrayList<>();
            if (CollUtil.isEmpty(purchaseInData)) {
                batchKingDeePurchaseReceiptDtos.add(build);
                saveStepParameter("purchaseInData", batchKingDeePurchaseReceiptDtos);
                saveStepParameter("formId", dto.getFormId());
            } else {
                purchaseInData.add(build);
            }
        }


    }


}
