package com.newtask.celery.utils;

import com.ctrip.framework.apollo.ConfigService;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class RedisKeyUtils {

    private static final String PROP_NAME = "redis_dbtype";

    private static String PREFIX;

    private static final String ASYNC_REDIS_CAT = "async:";

    public static final String K_QUEUE_LOCK_MAP = "queue_lock_map";

    public static final String K_ACTIVE_QUEUE_SET = "active_queue";


    /**
     * 获取队列锁key
     *
     * @return
     */
    public static String queueLockMapKey() {
        return getPrefix() + K_QUEUE_LOCK_MAP;
    }


    /**
     * 活跃队列
     *
     * @return
     */
    public static String activeQueueSetKey() {
        return getPrefix() + K_ACTIVE_QUEUE_SET;
    }


    private static String getPrefix() {
        String result = PREFIX;
        if (result == null || result.length() == 0) {
            synchronized (RedisKeyUtils.class) {
                result = PREFIX;
                if (result == null || result.length() == 0) {
                    result = ConfigService.getAppConfig().getProperty(PROP_NAME, null) + ASYNC_REDIS_CAT;

                    if (result == null || result.length() == 0) {
                        throw new IllegalStateException(String.format("Failed to get value:[%s] from Apollo config",
                                PROP_NAME));
                    }

                    PREFIX = result;
                }
            }
        }

        return result;
    }

}
