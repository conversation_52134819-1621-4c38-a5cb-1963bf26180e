package com.vedeng.erp.buyorder.mapstruct;

import com.vedeng.erp.buyorder.domain.entity.BuyorderGoods;
import com.vedeng.erp.buyorder.dto.BuyorderGoodsApiDto;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.buyorder.mapstruct
 * @Date 2023/11/24 10:43
 */
@Mapper(componentModel = "spring")
public interface BuyOrderGoodsConvertor  {

    BuyorderGoodsApiDto to(BuyorderGoods buyorderGoods);

    List<BuyorderGoodsApiDto> to(List<BuyorderGoods> buyorderGoods);
}
