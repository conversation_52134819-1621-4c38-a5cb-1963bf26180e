<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="serviceList" language="groovy" printOrder="Horizontal" pageWidth="1410" pageHeight="615" orientation="Landscape" columnWidth="1410" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isTitleNewPage="true" whenResourceMissingType="Empty" isIgnorePagination="true" uuid="a0d85773-bf30-4acd-a5bf-8309f6787e74">
	<property name="ireport.zoom" value="1.1000000000000207"/>
	<property name="ireport.x" value="592"/>
	<property name="ireport.y" value="0"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<style name="style_detail_left" hAlign="Left" vAlign="Middle" isBlankWhenNull="true" fontName="宋体" fontSize="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H"/>
	<style name="style_detail_center" hAlign="Center" vAlign="Middle" fontName="SansSerif" fontSize="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H"/>
	<style name="style_title_name" hAlign="Center" vAlign="Middle" fontName="宋体" fontSize="13" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H"/>
	<style name="style_title_value" hAlign="Left" vAlign="Middle" isBlankWhenNull="true" fontName="宋体" fontSize="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H"/>
	<style name="style_detail_right" hAlign="Right" vAlign="Middle" fontName="宋体" fontSize="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H"/>
	<style name="style_title_center" backcolor="#00FF00" hAlign="Center" vAlign="Middle" fontName="SansSerif" fontSize="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H"/>
	<field name="afterSalesNo" class="java.lang.String"/>
	<field name="orderNo" class="java.lang.String"/>
	<field name="typeName" class="java.lang.String"/>
	<field name="creatorName" class="java.lang.String"/>
	<field name="serviceUserName" class="java.lang.String"/>
	<field name="atferSalesStatus" class="java.lang.String"/>
	<field name="addTimeStr" class="java.lang.String"/>
	<field name="validTimeStr" class="java.lang.String"/>
	<field name="validStatus" class="java.lang.String"/>
	<columnHeader>
		<band height="25" splitType="Stretch">
			<frame>
				<reportElement key="frame" style="style_detail_center" mode="Opaque" x="0" y="0" width="1410" height="25" backcolor="#99CCFF" uuid="9b51a743-dc57-43aa-9065-3e3010633bd8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement key="staticText" style="style_detail_center" x="0" y="0" width="150" height="25" forecolor="#000000" backcolor="#0099FF" uuid="0c067fa4-bdc9-439e-9cca-b4be4c5cf6b1"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[售后单号]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText" style="style_detail_center" x="150" y="0" width="150" height="25" backcolor="#0099FF" uuid="87c27b41-80d3-43c1-8d7c-9a0db3c563d7"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[对应订单号]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText" style="style_detail_center" x="300" y="0" width="150" height="25" backcolor="#0099FF" uuid="0ed497f0-8aba-4615-ac5a-8bc2f75806bc"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[业务类型]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText" style="style_detail_center" x="450" y="0" width="150" height="25" backcolor="#0099FF" uuid="e2a6bbbb-0e1f-422a-942f-99f2f232f4b1"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[创建人]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText" style="style_detail_center" x="600" y="0" width="150" height="25" backcolor="#0099FF" uuid="b5f0b8ec-2b75-4506-8aca-aa727b2712ef"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[售后人员]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText" style="style_detail_center" x="750" y="0" width="150" height="25" backcolor="#0099FF" uuid="76f456d2-919c-4638-ba69-eca5050bf516"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[订单状态]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText" style="style_detail_center" x="900" y="0" width="180" height="25" backcolor="#0099FF" uuid="e4727244-8a56-4573-a420-8b81ee075f04"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[申请时间]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText" style="style_detail_center" x="1080" y="0" width="180" height="25" backcolor="#0099FF" uuid="aae3cf8c-ef93-4906-860f-9045315e03b6"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[生效时间]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText" style="style_detail_center" x="1260" y="0" width="150" height="25" backcolor="#0099FF" uuid="7a9095d1-245a-41a8-9b60-4204f6fd4eab"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[审核状态]]></text>
				</staticText>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="20" splitType="Stretch">
			<frame>
				<reportElement key="frame" mode="Opaque" x="0" y="0" width="1410" height="20" backcolor="#FFFFFF" uuid="91d6476c-ac79-41de-91f0-e993c1d9a334"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField" style="style_detail_left" stretchType="RelativeToTallestObject" x="0" y="0" width="150" height="20" uuid="4a7a8d90-f169-4f56-b924-5cc1fcad251a"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{afterSalesNo}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField" style="style_detail_left" stretchType="RelativeToTallestObject" x="150" y="0" width="150" height="20" uuid="b84aa0d0-04fe-481a-8ae2-9e87d754a8b3"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{orderNo}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField" style="style_detail_left" stretchType="RelativeToTallestObject" x="300" y="0" width="150" height="20" uuid="2c780098-284b-4c2f-8766-fa42be49b3fa"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{typeName}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField" style="style_detail_left" stretchType="RelativeToTallestObject" x="450" y="0" width="150" height="20" uuid="9f231d21-1f83-4cb6-8e1c-43e1a26651bd"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{creatorName}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField" style="style_detail_left" stretchType="RelativeToTallestObject" x="600" y="0" width="150" height="20" uuid="f9854777-193a-42b7-93e6-0e421aa569c5"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{serviceUserName}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField" style="style_detail_left" stretchType="RelativeToTallestObject" x="750" y="0" width="150" height="20" uuid="6857fe32-c6ba-4f46-89a9-ea513ec06b3e"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{atferSalesStatus}.equals( '0' )?'待确认':$F{atferSalesStatus}.equals( '1' )?'进行中':$F{atferSalesStatus}.equals( '2' )?'已完结':$F{atferSalesStatus}.equals( '3' )?'已关闭':'']]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField" style="style_detail_left" stretchType="RelativeToTallestObject" x="900" y="0" width="180" height="20" uuid="fa8d0f54-78b6-4fbe-ac89-c1e712020434"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{addTimeStr}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField" style="style_detail_left" stretchType="RelativeToTallestObject" x="1080" y="0" width="180" height="20" uuid="11d9c26c-a5d6-4dda-a7e5-8d5277c44c92"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{validTimeStr}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField" style="style_detail_left" stretchType="RelativeToTallestObject" x="1260" y="0" width="150" height="20" uuid="ef0a2a12-8fac-423c-b8d1-d74db8caded7"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{validStatus}.equals( '0' )?'待确认':$F{validStatus}.equals( '1' )?'审核中':$F{validStatus}.equals( '2' )?'审核通过':$F{validStatus}.equals( '3' )?'审核不通过':'']]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
