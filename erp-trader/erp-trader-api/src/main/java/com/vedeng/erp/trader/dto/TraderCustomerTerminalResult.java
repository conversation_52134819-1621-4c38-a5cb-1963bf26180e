package com.vedeng.erp.trader.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2023/9/6 10:25
 **/
@Setter
@Getter
@NoArgsConstructor
public class TraderCustomerTerminalResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客户名
     */
    private String traderName;

    /**
     * 客户id
     */
    private Integer traderId;

    /**
     * 交易客户id
     */
    private Integer traderCustomerId;

    /**
     * 归属销售
     */
    private Integer userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 时间
     */
    private Date arrivalTime;

}
