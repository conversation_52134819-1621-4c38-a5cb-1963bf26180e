package com.vedeng.erp.buyorder.service;


import com.vedeng.authorization.model.User;
import com.vedeng.erp.buyorder.domain.entity.GeBusinessChanceDetail;
import com.vedeng.erp.buyorder.dto.GeBusinessChanceDetailDto;

/**
 * <AUTHOR>
 * @desc Ge商机详情service
 */
public interface GeBusinessChanceDetailService {

    /**
     * <AUTHOR>
     * @desc 根据Ge商机主表ID查询信息
     * @param geBusinessChanceId
     * @return
     */
    GeBusinessChanceDetailDto queryByGeBusinessChanceId(Integer geBusinessChanceId);

    /**
     * <AUTHOR>
     * @desc 保存GE商机详情
     * @param geBusinessChanceDetail
     * @param user
     * @return
     */
    GeBusinessChanceDetail saveEditGeBusinessChance(GeBusinessChanceDetail geBusinessChanceDetail, User user);
}
