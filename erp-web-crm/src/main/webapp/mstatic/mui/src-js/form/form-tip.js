

Vue.component('ui-tip', {
    template: `
        <div
            class="form-tip"
            :class="[type, {
                'style2': styles == 2
            }]"
            :style="{
                'margin-top': marginTop,
                'margin-bottom': marginBottom,
            }"
            @click="handlerClick"
        >
            <i :class="['vd-ui_icon', Icons[type]]"></i>
            <span>
                <slot></slot>
            </span>
        </div>
    `,
    props: {
        type: {
            type: String,
            default: 'warn'
        },
        styles: {
            type: String,
            default: '1', // 1::默认提示文案  2:底色提示文案
        },
        marginTop: {
            type: String,
            default: '0'
        },
        marginBottom: {
            type: String,
            default: '0'
        },

        // 逻辑相关
        tel: {
            type: String,
        }
    },
    data() {
        return {
            Icons: {
                // 提示相关
                'warn': 'icon-caution1',
                'success': 'icon-yes1',
                'error': 'icon-error2',
                'info': 'icon-info2',

                // 业务逻辑相关
                'map': 'icon-address',
                'tel': 'icon-call2'
            }
        };
    },
    mounted() {
    },
    methods: {
        handlerClick () {
            if (this.type == 'tel') {
                window.location.href = 'tel:'+this.tel;
            }
            this.$emit('click');
        }
    }
})