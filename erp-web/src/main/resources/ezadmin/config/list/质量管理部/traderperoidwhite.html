<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>可发货客户白名单</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>

<body id="traderperoidwhite" datasource="erp-datasourcetarget" empty_show="" fixednumber="0" firstcol="numbers">

<div id="appendHead">
</div>
<div class="layui-fluid">
    <div class="layui-tab">

    </div>
    <form class="layui-form" id="search">
        <div class="layui-inline ">
            <label>客户名称</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder="请输入客户名称" oper="like" name="TRADER_NAME" empty_show="-" style="" alias="">
            </div>
        </div>
        <div class="layui-inline ">
            <label>归属销售</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="xm-select" name="USER_ID" placeholder="" style="" alias="D" jdbctype="VARCHAR" data="
SELECT
	tu.USER_ID K,
	tu.USERNAME V
FROM
	T_USER tu
	LEFT JOIN T_R_USER_POSIT trup ON tu.USER_ID = trup.USER_ID
	LEFT JOIN T_POSITION tp ON tp.POSITION_ID = trup.POSITION_ID
WHERE
	tu.IS_DISABLED = 0
	AND tp.`TYPE` = 310
GROUP BY
	tu.USER_ID" datatype="KVSQLCACHE" oper="IN"></object>
            </div>
        </div>
        <div class="layui-inline ">
            <label>归属部门</label>
            <div class="layui-input-inline">
                <object type="search-org" class=" layui-input list-search-item " name="ORG_ID" placeholder="" style="" alias="G" jdbctype="NUMBER" data="" datatype="" oper="IN"></object>
            </div>
        </div>
        <div class="layui-inline ">
            <label>是否白名单客户</label>
            <div class="layui-input-inline">
                <select class="layui-input list-search-item"    name="IS_WHITE"  data="yesno" empty_show="-" style="" alias="IF(B.TRADER_ID IS NULL, 0, 1)"></select>

            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">添加时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="ADD_TIME" placeholder="" style="" alias="B" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>

    </form>
    <hr class="layui-border-blue">

    <div class="btn-group   bd-highlight" id="tableButton">
<!--        <button type="table" class="layui-btn layui-btn-normal layui-btn-sm ez-select"-->
<!--                url="/ezadmin/list/list-traderSearch">选择</button>-->
    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <!-- id=column:
       列
      -->
        <tr id="column">
            <th name="TRADER_ID"   >客户ID</th>
            <th body="td-link" opentype="PARENT" url="/trader/customer/baseinfo.do?traderId=${TRADER_ID}"   name="TRADER_NAME"  >客户名称</th>
            <th  name="USERNAME"  >归属销售</th>
            <th  name="ORG_NAME"  >归属部门</th>
            <th  name="IS_WHITE" body="td-select" data="yesno"    >是否白名单客户</th>
            <th   name="ADD_TIME"    >添加时间</th>
            <th type="rowbutton" id="rowbutton">
                 <button class="layui-btn list-row-button" type="group" opentype="CONFIRM_AJAX" url="/ezadmin/form/doSubmit-traderperoidwhite?ID=${TRADER_ID}&TRADER_ID=${TRADER_ID}&IS_DEL=0" windowname="设置白名单{TRADER_NAME}" name="updatoo">设置白名单 </button>
                 <button class="layui-btn list-row-button" type="group" opentype="CONFIRM_AJAX" url="/ezadmin/form/doSubmit-traderperoidwhite?ID=${TRADER_ID}&TRADER_ID=${TRADER_ID}&IS_DEL=1" windowname="取消白名单{TRADER_NAME}" name="updallte">取消白名单 </button>
            </th>
        </tr>
        </thead>
        <tbody>
        <tr>

            <td colspan="100">
                express:
                <pre id="express" class="layui-code" orderby="ORDER BY B.ID DESC" groupby="">
                    SELECT
                        B.ID,
                        A.TRADER_ID,
                        A.TRADER_NAME,
                        D.USERNAME,
                    IF
                        ( B.TRADER_ID IS NULL, 0, 1 ) IS_WHITE,
                        DATE_FORMAT( B.ADD_TIME, '%Y-%m-%d %H:%i:%s' ) AS ADD_TIME,
                        DATE_FORMAT( B.MOD_TIME, '%Y-%m-%d %H:%i:%s' ) AS MOD_TIME,
                        G.ORG_NAME
                    FROM
                        T_TRADER A
                        LEFT JOIN T_TRADER_PERIOD_WHITE B ON A.TRADER_ID = B.TRADER_ID
                        AND B.IS_DEL = 0
                        LEFT JOIN T_TRADER_CUSTOMER ttc ON A.TRADER_ID = ttc.TRADER_ID
                        LEFT JOIN T_VERIFIES_INFO V ON V.RELATE_TABLE_KEY = ttc.TRADER_CUSTOMER_ID AND V.RELATE_TABLE = 'T_TRADER_CUSTOMER'
                        AND V.VERIFIES_TYPE = 617
                            LEFT JOIN T_VERIFIES_INFO VE ON VE.RELATE_TABLE = 'T_CUSTOMER_APTITUDE' AND VE.RELATE_TABLE_KEY = ttc.TRADER_CUSTOMER_ID
                        LEFT JOIN T_R_TRADER_J_USER C ON C.TRADER_ID = A.TRADER_ID
                        AND C.TRADER_TYPE = 1
                        LEFT JOIN T_USER D ON C.USER_ID = D.USER_ID
                        LEFT JOIN T_R_USER_POSIT E ON D.USER_ID = E.USER_ID
                        LEFT JOIN T_POSITION F ON E.POSITION_ID = F.POSITION_ID
                        LEFT JOIN T_ORGANIZATION G ON F.ORG_ID = G.ORG_ID
                    WHERE
                        A.IS_ENABLE = 1
                        AND A.COMPANY_ID = 1
                        AND A.TRADER_NAME IS NOT NULL
                        AND A.TRADER_NAME != ''
                         AND  (V.`STATUS` = 1 OR  VE.`STATUS`=1)
     </pre>
                <pre id="count" class="layui-code">

     </pre>
            </td>
        </tr>
        </tbody>
    </table>
</div>
<div id="appendFoot">
    <script>
        function ez_callback(ids,lines){
            console.log(ids);
            console.log(lines);
            layer.closeAll();
        }
        console.log('hello world!')
    </script>
</div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>

</script>
</body>
</html>