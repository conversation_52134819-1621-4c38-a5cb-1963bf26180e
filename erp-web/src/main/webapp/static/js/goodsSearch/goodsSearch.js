$(function () {
    // var page_url = 'http://172.16.2.72:7777'

    //可筛选参数，该数组按照需求文档的顺序给出，其中属性渲染较为特殊，单独处理
    var filterConfig = [{
        listKey: 'brandList', //接口返回的字段名
        filterLabel: '品牌', //渲染时的标签名
        filterId: 'brandId', //选择时用到的数据key
        filterName: 'brandName', //展示时用到的数据key
        filterSubmitKey: '',  //筛选提交时的字段key，为空时默认取filterId;该字段主要为了兼容渲染和提交时接口字段名不一致
        isMulti: true
    },
    {
        listKey: 'regionPriceList',
        filterLabel: '价位段',
        filterId: 'priceId',
        filterName: 'priceName',
    },
    {
        listKey: 'categoryList',
        filterLabel: '分类',
        filterId: 'categoryId',
        filterName: 'categoryName',
    },
    {
        listKey: 'attributeList', //属性/参数
        filterId: '',
        filterName: '',
        isMulti: true
    },
    {
        listKey: 'spuType',
        filterLabel: '类型',
        filterId: 'spuTypeId',
        filterName: 'spuTypeName',
        filterSubmitKey: 'spuType'
    },
    {
        listKey: 'institutionList',
        filterLabel: '机构',
        filterId: 'institutionId',
        filterName: 'institutionName',
        isMulti: true
    },
    {
        listKey: 'departmentList',
        filterLabel: '科室',
        filterId: 'departmentId',
        filterName: 'departmentName',
        isMulti: true
    },
    {
        listKey: 'brandNature',
        filterLabel: '进口/国产',
        filterId: 'brandNatureId',
        filterName: 'brandNatureName',
    },
    {
        listKey: 'goodsLevelList',
        filterLabel: '等级',
        filterId: 'goodsLevelId',
        filterName: 'goodsLevelName',
        isMulti: true
    },
    {
        listKey: 'goodsPositionList',
        filterLabel: '档位',
        filterId: 'goodsPositionId',
        filterName: 'goodsPositionName',
        isMulti: true
    }]

    //表头配置项
    var listHeads = [{
        name: '订货号',
        key: 'skuNo',
        disabled: true,
    }, {
        name: '商品名称',
        key: 'skuName',
        disabled: true,
    }, {
        name: '近一年销量',
        key: 'saleCountOfLastYear',
        align: 'right',
        sort: true,
    }, {
        name: '贝登分类',
        key: 'category',
    }, {
        name: '品牌名称',
        key: 'brandName',
    }, {
        name: '商品等级',
        key: 'goodsLevel',
    }, {
        name: '商品档位',
        key: 'goodsPosition',
    }, {
        name: '经销价',
        key: 'distributionPrice',
        align: 'right',
        sort: true,
    }, {
        name: '终端价',
        key: 'terminalPrice',
        align: 'right',
        sort: true,
    },
    {
        name: '电商价',
        key: 'electronicCommercePrice',
        align: 'right',
        sort: true,
    },
    {
        name: '最近一年单价',
        key: 'avgPriceOfLastYear',
        align: 'right',
        sort: true
    }, {
        name: '可用库存',
        key: 'availableStock',
        align: 'right',
        sort: true,
    }, {
        name: '在途库存',
        key: 'onWayNum',
        align: 'right',
        sort: true,
    }, {
        name: '库存量',
        key: 'stock',
        align: 'right',
        sort: true,
    }, {
        name: '产品归属',
        key: 'skuAssignments',
    }, {
        name: '最近成单销售',
        key: 'salerOfLastOrder',
    }, {
        name: '审核状态',
        key: 'checkStatus',
    }, {
        name: '税收编码',
        key: 'taxCategoryNo',
    }, {
        name: '商品和服务分类简称',
        key: 'classIficationAbbreviation',
    }, {
        name: '商品类型',
        key: 'spuType',
    }, {
        name: '单位',
        key: 'unit',
    }, {
        name: '规格',
        key: 'spec',
    }, {
        name: '型号',
        key: 'model',
    }, {
        name: '直发货期（天）',
        key: 'directDeliveryTime',
        icon: 'icon-problem1',
        iconTxt: '近90天供应链允诺货期'
    },{
        name: '普发货期（天）',
        key: 'commonDeliveryTime',
        icon: 'icon-problem1',
        iconTxt: '近90天平均货期'
    }, {
        name: '采购到货时长（工作日）',
        align: 'right',
        key: 'purchaseTime',
    }, {
        name: '注册证号',
        key: 'registerNumber',
    }, {
        name: '占用/活动锁定',
        key: 'occupyNum',
        align: 'right',
    }, {
        name: '使用年限',
        key: 'serviceLife',
        align: 'right',
    }];

    $.each(listHeads, function(i, item){
        listHeads[i].sortNum = i;
    })

    var prodIcons = [
        {
            name: '销量之王',
            icon: 'icon-1'
        }, 
        {
            name: '新品',
            icon: 'icon-2'
        }, 
        {
            name: '热销',
            icon: 'icon-3'
        }, 
        {
            name: '同行热推',
            icon: 'icon-4'
        }, 
        {
            name: '价格透明',
            icon: 'icon-5'
        }, 
        {
            name: '现货供应',
            icon: 'icon-6'
        }
    ];

    //加载
    var showLoading = function () {
        $('.J-loading').css('display', 'flex');
    }

    var hideLoading = function () {
        $('.J-loading').hide();
    }

    var errorTimeout = null;
    //报错
    var showError = function (txt) {
        errorTimeout && clearTimeout(errorTimeout)
        $('.J-page-tip').addClass('show').find('.tip-cnt').html(txt);

        errorTimeout = setTimeout(function(){
            hideError();
        }, 3000)
    }

    var hideError = function () {
        $('.J-page-tip').removeClass('show')
    }

    //显示排序的气泡提醒（浏览器记录第一次展示）
    var showSortTipHistory = window.localStorage.getItem('showSortTipHistory') || '';
    if(!showSortTipHistory){
        $('.J-sort-tip').show();
    }

    $('.J-sort-tip-hide').click(function(){
        $('.J-sort-tip').hide();
        window.localStorage.setItem('showSortTipHistory', 1);
    })

    //当前页码
    var pageNo = 1;

    var filterHtml = template($('.J-filter-tmpl').html());

    //校验筛选是否需要展示更多
    var checkFilterMore = function () {
        $('.J-filter-block-item').each(function () {
            var listHeight = $(this).find('.filter-list')[0].scrollHeight;

            if (listHeight > 50) {
                $(this).find('.J-filter-item-more').show();
            } else {
                $(this).find('.J-filter-item-more').hide();
            }
        })
    };

    //屏幕大小改变时处理
    $(window).resize(function () {
        checkFilterMore();
    })

    //处理价格展示
    var parsePrice = function (price) {
        if (price >= 10000) {
            return (Math.round(price / 100) / 100).toFixed(2) + '万'
        } else {
            return price;
        }
    }

    var listData = [];
    var sortKey = '';
    var sortType = '';

    //空列表埋点
    var postEmptyMessage = function(data){
        $.ajax({
            url: '/goodsInfo/search/resultIsEmpty.do',
            data: data
        })
    }

    var getReqdata = function () {
        var reqData = {};

        //关键词
        if ($('.J-filter-item-keyword .item-txt').attr('title')) {
            reqData.keywords = $('.J-filter-item-keyword .item-txt').attr('title');
        }

        //场景
        if ($('.J-scene-search-value').val()) {
            reqData.skuSceneCategoryId = $('.J-scene-search-value').val();
        }

        //筛选项
        var attributeId = [];
        var attributeValueId = [];

        $('.J-filter-select-wrap .filter-item-choose').each(function () {
            var item = $(this).data('filter');

            if (item.filterId == 'attributeValueId') {
                var values = item.value.toString().split(',');

                $.each(values, function (i, value) {
                    attributeId.push(item.labelId);
                    attributeValueId.push(value);
                })

            } else if (item.filterId == 'priceId') {
                var values = item.value.split(',');
                reqData.minSalePrice = values[0];
                reqData.maxSalePrice = values[1];
            } else {
                reqData[item.filterId] = item.value;
            }
        });

        if (attributeId.length) {
            reqData.attributeId = attributeId.join(',');
            reqData.attributeValueId = attributeValueId.join(',');
        }

        //是否核价、有库存等
        $('.J-list-filter-check.checked').each(function () {
            reqData[$(this).data('key')] = 1;
        })

        if (sortKey) {
            reqData.sortColoum = sortKey;
            reqData.sortAsc = sortType;
        }

        reqData.pageNo = pageNo;

        return reqData;
    };

    //搜索
    var getList = function () {
        $('.J-filter-wrap').removeClass('open');
        var reqData = getReqdata();
        
        showLoading();
        $.ajax({
            url: page_url + '/goodsInfo/search.do',
            data: reqData,
            dataType: 'json',
            timeout: 10000,
            success: function (res) {
                if (res.code === 0) {
                    $('.J-filter-block').empty();
                    var filterData = res.data;

                    $.each(filterConfig, function (i, item) {
                        if (filterData[item.listKey] && filterData[item.listKey].length > 1) {
                            if (item.listKey === 'regionPriceList') {
                                $.each(filterData[item.listKey], function (ii, dataItem) {
                                    filterData[item.listKey][ii].priceId = dataItem.minPrice + ',' + dataItem.maxPrice;
                                    if (dataItem.minPrice == -1){
                                        filterData[item.listKey][ii].priceName = '未核价';
                                    } else if (dataItem.minPrice === dataItem.maxPrice){
                                        filterData[item.listKey][ii].priceName = parsePrice(dataItem.minPrice);
                                    } else {
                                        filterData[item.listKey][ii].priceName = parsePrice(dataItem.minPrice) + ' - ' + parsePrice(dataItem.maxPrice);
                                    }
                                })
                            }

                            if (item.listKey !== 'attributeList') {
                                if (!$('.filter-item-choose[data-label="' + item.filterLabel + '"]').length){
                                    $('.J-filter-block').append(filterHtml({
                                        data: filterData[item.listKey],
                                        filterData: item
                                    }))
                                }
                            } else {
                                $.each(filterData[item.listKey], function (ii, data) {
                                    if (!$('.filter-item-choose[data-label="' + data.attributeName + '"]').length) {
                                        $('.J-filter-block').append(filterHtml({
                                            data: data.attributeValueList,
                                            filterData: {
                                                listKey: 'attributeList',
                                                filterLabel: data.attributeName,
                                                filterLabelId: data.attributeId,
                                                filterId: 'attributeValueId',
                                                filterName: 'attributeValueName',
                                                isMulti: true
                                            }
                                        }))
                                    }
                                })
                            }
                        }
                    });

                    listData = res.listData || [];
                    refreshList(listData);

                    if(!listData.length){
                        postEmptyMessage(reqData);
                    }

                    $('html, body').animate({ scrollTop: 0 }, 10);

                    var filterLen = $('.J-filter-wrap .J-filter-block-item').length;
                    if (!filterLen) {
                        $('.J-filter-wrap').hide();
                    } else {
                        $('.J-filter-wrap').show();
                    }

                    if (filterLen > 4) {
                        $('.J-filter-wrap .filter-more').show();
                    } else {
                        $('.J-filter-wrap .filter-more').hide();
                    }

                    if (reqData.keywords) {
                        $('.J-filter-info-list').show();
                    } else {
                        $('.J-filter-info-list').hide();
                    }

                    if (res.page.totalPage > 1) {
                        $('.J-pager').after('<div class="J-pager"></div>');
                        $('.J-pager').eq(0).remove();
                        new Pager({
                            el: '.J-pager',
                            total: res.page.totalRecord,
                            pageNum: pageNo,
                            pageSize: 30,
                            needJump: false,
                            needMax: false,
                            callback: function (num) {
                                pageNo = num;
                                getList();
                            }
                        })

                        $('.J-pager .pager-wrap').before('<div class="pager-total-txt">共 <span>' + res.page.totalRecord + '</span> 条</div>')

                        $('.J-simple-page').show();

                        if (pageNo == 1) {
                            $('.J-page-prev').addClass('disabled');
                        } else {
                            $('.J-page-prev').removeClass('disabled');
                        }

                        if (pageNo == res.page.totalPage) {
                            $('.J-page-next').addClass('disabled');
                        } else {
                            $('.J-page-next').removeClass('disabled');
                        }
                    } else {
                        $('.J-simple-page').hide();
                        $('.J-pager').hide();
                    }

                    $('.J-search-input').val(reqData.keywords)

                    setTimeout(function () {
                        checkFilterInfoFixed();
                        checkTableFixed();
                        checkFilterMore();

                        //防止页面刷新时有左侧的滚动值，导致页面样式有偏差
                        $('html,body').animate({ scrollLeft: 0 }, 0);
                    }, 100)

                    
                }else{
                    showError('系统繁忙，请稍后再试！');
                }
                hideLoading();
            },
            error: function(res, status){
                console.log(status)
                showError('系统繁忙，请稍后再试！');
                hideLoading();
            }
        })
    }

    //设置悬浮表头的宽度
    var resetTabledFixed = function () {
        $('.J-table-fixed .table-inner').width($('.J-table-list').width() + 2);
        $('.J-table-fixed th').each(function (i) {
            $(this).width($('.J-table-list th').eq(i).width());
        })

        $('.J-table-cnt-fixed th, .J-table-cnt-fixed td').each(function (i) {
            $(this).width($('.J-table-list th').eq($(this).index()).width());
        })
    }

    //对象转链接参数，埋点用
    var parseParam = function (param, key) {
        var paramStr = "";
        if (param instanceof String || param instanceof Number || param instanceof Boolean) {
            paramStr += key + "=" + encodeURIComponent(param);
        } else {
            $.each(param, function (i) {
                var k = key == null ? i : key + (param instanceof Array ? "[" + i + "]" : "." + i);
                paramStr += '&' + parseParam(this, k);
            });
        }
        return paramStr;
    };  

    //根据设置排序
    var getSortListHeads = function(){
        var tableSortStorage = window.localStorage.getItem('prod_list_sort');
        if (tableSortStorage) {
            var tableSort = JSON.parse(tableSortStorage || '{}');

            $.each(listHeads, function (i, item) {
                listHeads[i].sortNum = tableSort[item.key]
            })

            listHeads.sort(function (a, b) {
                return a.sortNum - b.sortNum
            })
        }

        return listHeads;
    }

    //刷新列表
    var refreshList = function (list) {
        var tableSettingStorage = window.localStorage.getItem('prod_list_setting');
        var isDefault = false;

        if (!tableSettingStorage){
            isDefault = true;
        }

        var tableSetting = JSON.parse(tableSettingStorage || '{}');
        $('.J-table-head tr, .J-table-body, .J-table-cnt-head tr, .J-table-cnt-body').empty();

        var bodyArr = [];
        $('.J-table-head tr, .J-table-cnt-head tr').append('<th style="text-align:center;">序号</th>');
        var thTmpl = template($('.J-table-head-tmpl').html());
        listHeads = getSortListHeads();
        $.each(listHeads, function (i, item) {
            if (tableSetting[item.key] || isDefault) {
                $('.J-table-head tr').append(thTmpl({
                    item: item,
                    sortKey: sortKey,
                    sortType: sortType
                }));

                if (i < 2) {
                    $('.J-table-cnt-head tr').append(thTmpl({
                        item: item,
                        sortKey: sortKey,
                        sortType: sortType
                    }))
                }

                $.each(list, function (j, tdItem) {
                    if (!bodyArr[j]) {
                        bodyArr[j] = [];
                    }

                    if (item.key == 'skuName') {
                        var labelList = tdItem.labelList || '';
                        var icons = [];
                        var trackParams = "";
                        var reqParams = getReqdata();

                        if (!reqParams.sortColoum && j < 10 && pageNo == 1){
                            $.each(prodIcons, function (ii, iconItem) {
                                if (labelList.indexOf(iconItem.name) !== -1) {
                                    icons.push('<i title="' + iconItem.name + '" class="prod-icon ' + iconItem.icon + '"></i>');
                                }
                            })
                        }
                        
                        if (j < 10 && pageNo == 1 && reqParams.keywords){
                            reqParams.resultNum = j + 1;
                            trackParams = parseParam(reqParams);
                        }

                        bodyArr[j].push('<td ' + (item.align ? 'style="text-align: ' + item.align + '"' : '') + '><a class="J-prod-info" tabtitle=\'{"num":"viewSkuDetail' + tdItem.skuId + '", "link":"./goods/vgoods/viewSku.do?skuId=' + tdItem.skuId + '&pageType=1' + (reqParams.keywords ? '&from=_search' : '') + trackParams + '", "title":"商品整合查询页"}\'>' + icons.join('') + tdItem[item.key] + '</a></td>');
                    } else if (item.key == 'terminalPrice' || item.key == 'electronicCommercePrice') {
						bodyArr[j].push('<td ' + (item.align ? 'style="text-align: ' + item.align + '"' : '') + '>' + (tdItem[item.key] !== null ? tdItem[item.key].toFixed(2) : '') + '</td>');
                    } else if (item.key == 'distributionPrice') {
                        //经销价
                        var feeStr = '';
                        if(tdItem.saleContainsFee == 2) {
                            feeStr = '<span class="fee-icon" title="不含运费"></span>'
                        }

						bodyArr[j].push('<td ' + (item.align ? 'style="text-align: ' + item.align + '"' : '') + '>' + (tdItem[item.key] !== null ? feeStr + tdItem[item.key].toFixed(2) : '') + '</td>');
                    } else if (item.key == 'avgPriceOfLastYear'){
                        if(tdItem[item.key] !== null  && tdItem[item.key] <0){
                            bodyArr[j].push('<td ' + (item.align ? 'style="text-align: ' + item.align + '"' : '') + '>' +'已核价' + '</td>');
                        }else{
                            bodyArr[j].push('<td ' + (item.align ? 'style="text-align: ' + item.align + '"' : '') + '>' + (tdItem[item.key] !== null ? tdItem[item.key].toFixed(2) : '') + '</td>');
                        }
                    } else if (item.key == 'registerNumber'){
                        var labelList = tdItem.labelList || '';
                        var icons = [];
                        let text = tdItem[item.key] === null ? '' : tdItem[item.key];
                        bodyArr[j].push('<td ' + (item.align ? 'style="text-align: ' + item.align + '"' : '') + '><a class="J-prod-info" tabtitle=\'{"num":"viewGoodsDetail' + tdItem.firstEngageId + '", "link":"./firstengage/baseinfo/getFirstSearchDetail.do?firstEngageId=' + tdItem.firstEngageId   + '", "title":"医疗器械注册证详情"}\'>' + text + '</a></td>');
                    }else {
                        bodyArr[j].push('<td ' + (item.align ? 'style="text-align: ' + item.align + '"' : '') + '>' + (tdItem[item.key] !== null ? tdItem[item.key] : '') + '</td>');
                    }
                })
            }
        })

        $.each(bodyArr, function (i, item) {
            $('.J-table-body').append('<tr><td style="text-align:center;">' + (30 * (pageNo - 1) + i + 1) + '</td>' + item.join('') + '</tr>')
            $('.J-table-cnt-body').append('<tr><td style="text-align:center;">' + (30 * (pageNo - 1) + i + 1) + '</td>' + item.splice(0, 2).join('') + '</tr>')
        })

        if (!bodyArr.length) {
            $('.J-table-body').append('<tr class="J-empty"><td class="empty-wrap" colspan="' + $('.J-table-list-wrap th').length + '"><i class="vd-icon icon-info2"></i><div class="empty-txt">查询无结果！请尝试使用其他搜索条件。</div></td></tr>')
        }

        resetTabledFixed();

        $('.J-table-cnt-fixed').height($('.J-table').height())
        $('.J-table-list-wrap').animate({scrollLeft: 0}, 0)
    };

    var setHistory = function (keywords) {
        var history = JSON.parse(localStorage.getItem('prod_list_history') || '[]');

        var newHistory = [keywords];

        $.each(history, function (i, item) {
            if (item != keywords) {
                newHistory.push(item);
            }
        })

        localStorage.setItem('prod_list_history', JSON.stringify(newHistory.splice(0, 10)));
    };

    var inputSearch = function () {
        var keywords = $.trim($('.J-search-input').val());
        var scenes = $('.J-scene-search-value').val();
        var sceneNames = $.trim($('.J-scene-search-wrap .J-text').html());

        if(keywords || scenes) {
            selectedFilter = [];
            sortKey = "";
            sortType = "";
            pageNo = 1;
            $('.J-filter-select-wrap').find('.J-filter-item-keyword, .J-filter-item-scene, .filter-item-choose').remove();
            
            if (keywords) {
                setHistory(keywords);
                $('.J-filter-select-wrap').append('<div class="filter-item-link J-filter-item-keyword"><span class="item-txt" title="' + keywords + '">搜索"' + keywords + '"</span><i class="vd-icon icon-right"></i></div>')
            }

            if(scenes) {
                var sceneDom = '<div class="filter-item-link J-filter-item-scene"><span class="item-txt" title="' + sceneNames + '">' + sceneNames + '</span><i class="vd-icon icon-right"></i></div>';
    
                 $('.J-filter-select-wrap').append(sceneDom);
            } 

            if(!keywords || !scenes) {
                $('.J-filter-select-wrap').addClass('link-last');
            } else {
                $('.J-filter-select-wrap').removeClass('link-last');
            }

            getList();
        }
    };

    //进页面判断有没有值，有值搜索
    var searchInitVal = $('.J-search-input').val().trim();

    if(searchInitVal){
        inputSearch();
    } else {
        getList();
    }

    //点击搜索
    $('.J-search').click(function () {
        inputSearch();
    })

    //输入框回车搜索
    $('.J-search-input').on('keyup', function (e) {
        if (e.keyCode == 13 || e.keyCode == 108) {
            inputSearch();
        } else {
            checkHistoryShow();
        }
    })

    //点击历史搜索
    $('.J-history-wrap').on('click', '.search-history-item', function () {
        $('.J-search-input').val($(this).html());
        inputSearch();
    })

    //搜索框获焦展示历史记录
    var checkHistoryShow = function () {
        if (!$.trim($('.J-search-input').val())) {
            var searchHistory = JSON.parse(localStorage.getItem('prod_list_history') || '[]');

            if (searchHistory.length) {
                $('.J-history-wrap .search-history-item').remove();

                $.each(searchHistory, function (i, item) {
                    $('.J-history-wrap .search-history-clear').before('<div class="search-history-item" title="' + item + '">' + item + '</div>');
                })

                $('.J-history-wrap').show();
            }
        } else {
            $('.J-history-wrap').hide();
        }
    }

    $('.J-search-input').on('focus, click', function (e) {
        e.stopPropagation();
        checkHistoryShow();
        $(this).select();
    })

    $(document).on('click', function () {
        // setTimeout(function () {
            $('.J-history-wrap').hide();
        // }, 300);
    })

    //清除搜索历史
    $('.J-history-clear').click(function (e) {
        e.stopPropagation();
        localStorage.removeItem('prod_list_history');
        $('.J-history-wrap').hide();
    })

    //点击展开/收起更多选项
    $('.J-filter-more-btn').click(function () {
        if ($('.J-filter-wrap').hasClass('open')) {
            $('.J-filter-wrap').removeClass('open')
        } else {
            $('.J-filter-wrap').addClass('open')
            checkFilterMore();
        }
    })

    //点击筛选项更多
    $(document).on('click', '.J-filter-item-more', function () {
        var $parent = $(this).parents('.J-filter-block-item:first');
        var hasShow = $parent.hasClass('show');

        $('.J-filter-block-item').removeClass('show multi');

        if (hasShow) {
            $parent.removeClass('show');
        } else {
            $parent.addClass('show');
        }
    });

    //点击筛选项多选
    $(document).on('click', '.J-filter-item-multi', function () {
        var $parent = $(this).parents('.J-filter-block-item:first');
        $('.J-filter-block-item').removeClass('show multi');
        $parent.addClass('show multi');
    });

    //增加筛选项
    var addFilterItem = function (filterItem) {
        $('.J-filter-select-wrap').append('<div class="filter-item-choose" title="' + filterItem.name + '" data-label="' + filterItem.label +'" data-filter=\'' + JSON.stringify(filterItem) + '\'><span class="item-txt">' + filterItem.label + '：' + filterItem.name + '</span><i class="vd-icon icon-delete"></i></div>')
    };

    //点击筛选项
    $(document).on('click', '.J-filter-item', function () {
        var $parent = $(this).parents('.J-filter-block-item:first');

        if ($parent.hasClass('multi')) {
            //多选
            if ($(this).hasClass('checked')) {
                $(this).removeClass('checked');
            } else {
                $(this).addClass('checked');
            }

            if ($parent.find('.checked').length) {
                $parent.find('.J-filter-multi-confirm').removeClass('disabled');
            } else {
                $parent.find('.J-filter-multi-confirm').addClass('disabled');
            }

        } else {
            //单选
            var itemData = $(this).parents('.J-filter-block-item:first').data('item');
            var value = $(this).data('value');
            var name = $(this).data('name');

            addFilterItem({
                filterId: itemData.filterSubmitKey || itemData.filterId,
                label: itemData.filterLabel,
                name: name,
                value: value,
                labelId: itemData.filterLabelId || ''
            })

            pageNo = 1;
            getList();
        }
    })

    //点击筛选项确定多选
    $(document).on('click', '.J-filter-multi-confirm', function () {
        if (!$(this).hasClass('disabled')) {
            var $parent = $(this).parents('.J-filter-block-item:first');
            var itemData = $parent.data('item');
            var value = [];
            var name = [];

            $parent.find('.checked').each(function () {
                name.push($(this).data('name'));
                value.push($(this).data('value'));
            })

            addFilterItem({
                filterId: itemData.filterSubmitKey || itemData.filterId,
                label: itemData.filterLabel,
                name: name.join(','),
                value: value.join(','),
                labelId: itemData.filterLabelId || ''
            })

            pageNo = 1;
            getList();
        }
    })

    //点击筛选项取消多选
    $(document).on('click', '.J-filter-multi-cancel', function () {
        $(this).parents('.J-filter-block-item:first').removeClass('multi');
    })

    //点击表头上方单个筛选
    $('.J-list-filter-check').click(function () {
        if ($(this).hasClass('checked')) {
            $(this).removeClass('checked');
        } else {
            $(this).addClass('checked');
        }

        pageNo = 1;
        getList();
    })

    //去掉某个筛选
    $('.J-filter-select-wrap').on('click', '.filter-item-choose', function () {
        $(this).remove();
        pageNo = 1;
        getList();
    })

    //点击关键词，清除后面的筛选项
    $('.J-filter-select-wrap').on('click', '.J-filter-item-keyword', function () {
        if ($('.J-filter-select-wrap .filter-item-choose').length || $('.J-filter-item-scene').length) {
            sceneSelect.clearValue();
            inputSearch();
        }
    })

    //点击关键词，清除后面的筛选项
    $('.J-filter-select-wrap').on('click', '.J-filter-item-scene', function () {
        if ($('.J-filter-select-wrap .filter-item-choose').length || $('.J-filter-item-keyword').length) {
            $('.J-search-input').val('');
            inputSearch();
        }
    })

    //清除所有搜索项
    $('.J-reset').click(function () {
        window.location.href = '/goodsInfo/search/goods.do';
    })

    //点击全部商品
    $('.J-filter-all').click(function () {
        if ($(this).next().length) {
            window.location.href = '/goodsInfo/search/goods.do';
        }
    })

    //点击表头排序
    $(document).on('click', '.J-th-sort', function () {
        if ($(this).hasClass('up')) {
            sortType = 1;
        } else if ($(this).hasClass('down')) {
            sortKey = '';
            sortType = '';
        } else {
            sortKey = $(this).data('key');
            sortType = 0;
        }

        pageNo = 1;
        getList();
    })

    //表单点击凸显
    $('.J-table').on('click', 'tr', function () {
        if (!$(this).hasClass('J-empty')) {
            $('.J-table tr').removeClass('active');
            $('.J-table-cnt-fixed tr').removeClass('active');
            $('.J-table-cnt-fixed .J-table-cnt-body tr').eq($(this).index()).addClass('active');
            $(this).addClass('active');
        }
    })

    //表单点击凸显
    $('.J-table-cnt-fixed .J-table-cnt-body').on('click', 'tr', function () {
        $('.J-table tr').removeClass('active');
        $('.J-table-cnt-fixed tr').removeClass('active');
        $('.J-table .J-table-body tr').eq($(this).index()).addClass('active');
        $(this).addClass('active');
    })

    //显示设置弹层
    $('.J-table-setting').click(function () {
        //渲染弹层数据
        $('.J-dlg-setting-wrap .check-list').empty();

        var checkItemTmpl = template($('.J-setting-item-tmpl').html());
        var tableSettingStorage = window.localStorage.getItem('prod_list_setting');
        var isDefault = !tableSettingStorage;

        var tableSetting = JSON.parse(tableSettingStorage || '{}');

        $.each(listHeads, function (i, item) {
            var checked = false;
            if (tableSetting[item.key] || isDefault) {
                checked = true;
            }
            if(item.disabled){
                $('.J-dlg-setting-wrap .J-unsetable .check-list').append(checkItemTmpl({
                    data: $.extend({}, item, {
                        checked: checked
                    })
                }))
            }else{
                $('.J-dlg-setting-wrap .J-setable .check-list').append(checkItemTmpl({
                    data: $.extend({}, item, {
                        checked: checked
                    })
                }))
            }
        })

        //拖拽排序
        new Sortable($('.J-setable .check-list')[0], {
            // draggable: ".J-setable .check-list .check-item",
            scrollSpeed: 30,
            containment: $('.J-setable .check-list')[0],
            handle: ".J-setable .check-list .drag-icon",
            animation: 100,
            ghostClass: "placeholder",
            // 开始拖拽的时候
            onStart: function (/**Event*/evt) {
                $('.J-setable .check-list').addClass('noborder');
            },
            // 结束拖拽
            onEnd: function (/**Event*/evt) {
                setTimeout(function(){
                    $('.J-setable .check-list').removeClass('noborder');
                }, 200)
            },
        });

        $('.J-dlg-setting-wrap').css('display', 'flex');
    })

    //关闭设置弹层
    $('.J-setting-close').click(function () {
        $('.J-dlg-setting-wrap').hide();
    })

    //设置选中切换
    $('.J-dlg-setting-wrap').on('click', '.checkbox-item', function () {
        if (!$(this).hasClass('disabled')) {
            if ($(this).hasClass('checked')) {
                $(this).removeClass('checked');
            } else {
                $(this).addClass('checked');
            }
        }
    })

    var showSettingTip = function(txt){
        $('.J-page-success-tip').addClass('show').find('.tip-cnt').html(txt);

        setTimeout(function(){
            $('.J-page-success-tip').removeClass('show');
        }, 3000);
    }

    //设置提交
    $('.J-setting-confirm').click(function () {
        var settingData = {};
        var sortData = {};

        $('.J-dlg-setting-wrap .checkbox-item').each(function (i, item) {
            if ($(this).hasClass('checked')) {
                settingData[$(this).data('key')] = 1;
            }
            sortData[$(this).data('key')] = i;
        })

        window.localStorage.setItem('prod_list_setting', JSON.stringify(settingData));
        window.localStorage.setItem('prod_list_sort', JSON.stringify(sortData));
        $('.J-dlg-setting-wrap').hide();
        refreshList(listData);

        showSettingTip('保存成功，页面将自动刷新');
    })

    //悬浮
    var checkFilterInfoFixed = function () {
        var top = $('.J-list-filter-info').offset().top;

        if ($(window).scrollTop() > top - 73) {
            $('.J-list-filter-info .list-filter-info-inner').addClass('fixed');
        } else {
            $('.J-list-filter-info .list-filter-info-inner').removeClass('fixed');
        }
    }

    checkFilterInfoFixed();

    var checkTableFixed = function () {
        var scrollTop = $(window).scrollTop();
        var cntFixTop = 0;
        if (scrollTop < $('.J-table-list-wrap').offset().top - 119) {
            cntFixTop = 0;
            $('.J-table-fixed').hide();
            $('.J-table-cnt-fixed').css('top', $('.J-table-list-wrap').offset().top - scrollTop + 'px');
        } else {
            var isShow = $('.J-table-fixed').is(':visible');
            $('.J-table-fixed').show();
            cntFixTop = $('.J-table-list-wrap').offset().top - 119 - scrollTop;
            $('.J-table-cnt-fixed').css('top', '119px')
            if(!isShow){
                $('.J-table-list-wrap').scroll();     
            }
        }
        $('.J-table-cnt-fixed .table-body-inner').css('top', cntFixTop + 'px');
        $('.J-table-cnt-fixed').css('height', $('.J-table-list-wrap').height() + cntFixTop - 14 + 'px')

    }

    $(window).on('scroll', function () {
        checkFilterInfoFixed(); 
        checkTableFixed();
    })

    $(window).on('resize', function () {
        resetTabledFixed();
        checkTableFixed();
    })

    $(document).click(function () {
        checkTableFixed();
        checkFilterInfoFixed();
    })

    //悬浮同步滚动
    $('.J-table-list-wrap').on('scroll', function () {
        var scrollLeft = $(this).scrollLeft();
        $('.J-table-fixed').animate({ scrollLeft: scrollLeft + 'px' }, 0)

        if (scrollLeft > 0) {
            $('.J-table-cnt-fixed').show();
            setTimeout(function(){
                var width = 0;
                $('.J-table .J-table-head tr:first th').each(function(i){
                    if(i < 3){
                        width += ($(this).width() + 22);
                    }
                })
               
                $('.J-table-cnt-fixed .table-body-inner, .J-table-cnt-fixed .table-head-inner').width(width + 2)
                $('.J-table-cnt-fixed').css('width', width + 10);
            }, 100)
        } else {
            $('.J-table-cnt-fixed').hide();
        }

        if(window.localStorage.getItem('prod_list_tip') != '1'){
            $('.J-dlg-tip-wrap').css('display', 'flex');
        }
    })

    $('.J-tip-close').click(function(){
        $('.J-dlg-tip-wrap').hide();
        window.localStorage.setItem('prod_list_tip', '1');
    })

    //上一页
    $('.J-page-prev').click(function () {
        if (!$(this).hasClass('disabled')) {
            pageNo -= 1;
            getList();
        }
    })

    //下一页
    $('.J-page-next').click(function () {
        if (!$(this).hasClass('disabled')) {
            pageNo += 1;
            getList();
        }
    })

    //商品新标签页打开
    $(document).on('click', '.J-prod-info', function () {
        var frontPageId = $(window.parent.document).find('.active').eq(1).attr('id');
        var newPageId;
        var tabTitle = $(this).attr('tabTitle');
        if (typeof (tabTitle) == 'undefined') {
            alert('参数错误');
        } else {
            tabTitle = $.parseJSON(tabTitle);
        }
        var id = tabTitle.num;
        // var id = 'index' + Date.parse(new Date()) + Math.floor(Math.random()*1000);
        var name = tabTitle.title;
        var uri = tabTitle.link;

        window.parent.postMessage({
            from: 'olderp',
            name: name,
            url: uri,
            id: id
        }, '*');


        var closable = 1;
        var item = { 'id': id, 'name': name, 'url': uri, 'closable': closable == 1 ? true : false };

        if (typeof (self.parent.closableTab) != 'undefined') {
            self.parent.closableTab.addTab(item);
            self.parent.closableTab.resizeMove();
            $(window.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
        } else {
            try {
                var uniqueName = uri.replace('./', '').replace(/["&'./:=%?[\]]/gi, '-').replace(/(--)/gi, '');
                window.parent.postMessage({
                    from: 'ez',
                    name: title,
                    url: uri,
                    id: "tab-" + uniqueName
                }, '*');
            } catch (e) { }
        }

    })

    $(".J-pop-new-data").on('click', function () {
        layer.config({
            extend: 'vedeng.com/style.css', //加载您的扩展样式
            skin: 'vedeng.com'
        });
        var layerParams = $(this).attr('layerParams');
        if (typeof (layerParams) == 'undefined') {
            alert('参数错误');
        } else {
            layerParams = $.parseJSON(layerParams);
        }
        var link = layerParams.link;
        if (link.indexOf("?") > 0 && (link.indexOf("?") + 1 == link.length)) {
            link += "pop=pop";
        } else if (link.indexOf("?") < 0) {
            link += "?pop=pop";
        } else if (link.indexOf("?") > 0 && (link.indexOf("?") + 1 != link.length)) {
            link += "&pop=pop";
        }
        var index = layer.open({
            type: 2,
            //area: 'auto',
            area: [layerParams.width, layerParams.height],
            title: layerParams.title,
            shade: 0.2,
            maxmin: true,
            shadeClose: true,
            moveOut: true,
            content: layerParams.noEncodeURI ? encodeURI(link) : encodeURI(encodeURI(link)),
            success: function (layero, index) {
                //layer.iframeAuto(index);
            }
        });
    });

    //tip气泡提示
    $(document).on('mouseenter', '.J-tip-position', function (e) {
        var left = e.originalEvent.clientX + 16;
        var top = e.originalEvent.clientY - 5;
        $('.J-tip-position-cnt').css({left: left + 'px', top: top + 'px'}).html($(this).data('txt')).show();
    });

    $(document).on('mouseleave', '.J-tip-position', function (e) {
        $('.J-tip-position-cnt').hide();
    })

    //场景级联选择
    var sceneSelect = new LvSelect({
        el: '.J-scene-search-wrap',
        input: '.J-scene-search-value',
        async: true,
        placeholder: '科室方案选择',
        clearable: true,
        gap: '/',
        url: page_url + '/sku/scene/getSceneAndCategory.do',
        parseData: function (res) {
            var categoryData = [];

            $.each(res.data, function (i, lv1) {
                var lv1item = {
                    label: lv1.name,
                    value: lv1.id,
                    child: []
                };

                if (lv1.skuSceneCategoryDtoList && lv1.skuSceneCategoryDtoList.length) {
                    $.each(lv1.skuSceneCategoryDtoList, function (ii, lv2) {
                        var lv2item = {
                            label: lv2.name,
                            value: lv2.id,
                        };

                        lv1item.child.push(lv2item)
                    })
                }

                if (lv1item.child.length) {
                    categoryData.push(lv1item);
                }
            })

            console.log(categoryData)

            if(categoryData.length) {
                $('.J-scene-search-wrap').show();
            }

            return categoryData;
        },
        onchange: function() {
            inputSearch();
        }
    })
})