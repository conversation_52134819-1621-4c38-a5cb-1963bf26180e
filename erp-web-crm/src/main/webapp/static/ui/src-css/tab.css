.vd-ui-tab {
  position: relative;
}
.vd-ui-tab .vd-ui-tab-list {
  display: flex;
  border-bottom: 1px solid #EBEFF2;
}
.vd-ui-tab .vd-ui-tab-list .vd-ui-tab-list-more {
  flex-wrap: wrap;
  max-height: 51px;
}
.vd-ui-tab .vd-ui-tab-item {
  margin: 0 20px;
  color: #333;
  cursor: pointer;
  white-space: nowrap;
  transition: color 0.1s ease;
}
.vd-ui-tab .vd-ui-tab-item .vd-ui-tab-item-inner {
  display: flex;
  align-items: center;
  position: relative;
}
.vd-ui-tab .vd-ui-tab-item .vd-ui-tab-item-inner .vd-ui-tab-item-txt {
  padding: 15px 0;
  font-size: 14px;
}
.vd-ui-tab .vd-ui-tab-item .vd-ui-tab-item-inner .vd-ui-tab-item-txt:hover {
  color: #0099FF;
  transition: color 0.1s ease;
}
.vd-ui-tab .vd-ui-tab-item .vd-ui-tab-item-inner .vd-ui-tab-custom-wrap {
  position: relative;
}
.vd-ui-tab .vd-ui-tab-item .vd-ui-tab-item-inner .vd-ui-tab-custom-option {
  position: absolute;
  border: solid 1px #C9CED1;
  border-radius: 3px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  padding: 5px 0;
  background: #fff;
  top: 21px;
  z-index: 11;
  left: 0;
}
.vd-ui-tab .vd-ui-tab-item .vd-ui-tab-item-inner .vd-ui-tab-custom-option .vd-ui-tab-custom-option-item {
  padding: 6px 10px;
  font-weight: normal;
}
.vd-ui-tab .vd-ui-tab-item .vd-ui-tab-item-inner .vd-ui-tab-custom-option .vd-ui-tab-custom-option-item.red {
  color: #e64545;
}
.vd-ui-tab .vd-ui-tab-item .vd-ui-tab-item-inner .vd-ui-tab-custom-option .vd-ui-tab-custom-option-item:hover {
  color: #f60;
  background: #F5F7FA;
}
.vd-ui-tab .vd-ui-tab-item .icon-app-more {
  display: inline-block;
  transform: rotate(90deg);
  width: 21px;
  height: 21px;
  text-align: center;
}
.vd-ui-tab .vd-ui-tab-item .icon-app-more:hover {
  color: #f60;
}
.vd-ui-tab .vd-ui-tab-item .vd-ui-tab-item-more {
  position: relative;
}
.vd-ui-tab .vd-ui-tab-item .vd-ui-tab-item-more .vd-ui-tab-item-more-active {
  color: #0099FF;
}
.vd-ui-tab .vd-ui-tab-item .vd-ui-tab-item-more .icon-down {
  font-size: 16px;
  vertical-align: -2px;
  transition: transform 0.2s ease;
  display: inline-block;
}
.vd-ui-tab .vd-ui-tab-item .vd-ui-tab-item-more .icon-down.hover {
  transform: rotate(180deg);
}
.vd-ui-tab .vd-ui-tab-item .vd-ui-tab-item-line {
  border: solid 1px #EBEFF2;
  margin: 0;
  padding: 11px 20px;
  margin-right: -1px;
  margin-bottom: -1px;
  position: relative;
  z-index: 1;
}
.vd-ui-tab .vd-ui-tab-item.vd-ui-tab-item-active {
  font-weight: bold;
  color: #0099FF;
  border-bottom-color: #fff;
}
.vd-ui-tab .vd-ui-tab-more .vd-ui-tab-more-list {
  position: absolute;
  background: #fff;
  padding: 5px 0;
  border: solid 1px #C9CED1;
  border-radius: 3px;
  box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.1);
  color: #333;
  top: 40px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 11;
}
.vd-ui-tab .vd-ui-tab-more .vd-ui-tab-more-item {
  padding: 6px 10px;
}
.vd-ui-tab .vd-ui-tab-more .vd-ui-tab-more-item:hover {
  background: #F5F7FA;
  color: #333;
}
.vd-ui-tab .vd-ui-tab-bar {
  background: #0099FF;
  height: 3px;
  position: absolute;
  bottom: 0;
  transition: all 0.22s ease-out;
}
