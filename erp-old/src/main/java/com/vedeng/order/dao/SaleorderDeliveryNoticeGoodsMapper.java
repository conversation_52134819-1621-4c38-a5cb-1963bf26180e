package com.vedeng.order.dao;

import com.vedeng.order.model.SaleorderDeliveryNoticeGoods;
import com.vedeng.order.model.SaleorderDeliveryNoticeGoodsExample;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
@Repository
public interface SaleorderDeliveryNoticeGoodsMapper {
    long countByExample(SaleorderDeliveryNoticeGoodsExample example);

    int deleteByExample(SaleorderDeliveryNoticeGoodsExample example);

    int deleteByPrimaryKey(Integer deliveryNoticeGoodsId);

    int insert(SaleorderDeliveryNoticeGoods record);

    int insertSelective(SaleorderDeliveryNoticeGoods record);


    List<SaleorderDeliveryNoticeGoods> selectByExample(SaleorderDeliveryNoticeGoodsExample example);

    SaleorderDeliveryNoticeGoods selectByPrimaryKey(Integer deliveryNoticeGoodsId);

    int updateByExampleSelective(@Param("record") SaleorderDeliveryNoticeGoods record, @Param("example") SaleorderDeliveryNoticeGoodsExample example);

    int updateByExample(@Param("record") SaleorderDeliveryNoticeGoods record, @Param("example") SaleorderDeliveryNoticeGoodsExample example);

    int updateByPrimaryKeySelective(SaleorderDeliveryNoticeGoods record);

    int updateByPrimaryKey(SaleorderDeliveryNoticeGoods record);

}