<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html>
<html>
<head>
    <title>商机列表</title>
    <link rel="stylesheet" href="/static/css/common/common.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/ui/ui.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/css/pages/records.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/css/pages/businessChance.css?staticResourceVersion=${requestScope.staticResourceVersion}">
</head>
<body>
    <!--隐藏的登录用户信息 -->
    <jsp:include page="../common/head_import.jsp"></jsp:include>

    <div class="leads-iframe-wrap J-leads-wrap" style="display: none;">
        
    </div>
    
    <div class="chance-cnt-wrap J-chance-wrap">
        <div class="page-wrap" id="page-container">
            <page-header :side="true"></page-header>
            <div class="page-container">
                <div class="page-main">
                    <div class="list-title-btns">
                        <a class="list-title-btn-item" @click="triggerTabChange(1)">线索</a>
                        <div class="list-title-btn-item active">商机</div>
                    </div>
                    <div class="list-top-option">
                        <ui-button type="primary" icon="icon-add" @click="gotoAdd">新建线索/商机</ui-button>
                    </div>
                    <ui-search-container ref="listContainer" list-name="02" :default-tab="defaultTab" :headers="tableHeaders" url="/crm/businessChance/profile/page" :search-params="searchParams" @edit="tableItemEdit" @scroll="listScroll" @call="handlerCallNumber" :edit-valid="checkCanEdit" edit-valid-msg="赢单或关闭商机不可编辑。" :search-default-params="searchDefaultParams" @afterreset="initAddTime">
                        <template v-slot:filter-list>
                            <ui-search-item label="商机编号" :lock="true">
                                <ui-input v-model="searchParams.bussinessChanceNo"></ui-input>
                            </ui-search-item>
                            <ui-search-item label="商机类型">
                                <ui-select dictionary="5700" multiple-type="fixed" placeholder="全部" v-model="searchParams.businessTypeList" clearable></ui-select>
                            </ui-search-item>
                            <ui-search-item label="商机等级">
                                <ui-select dictionary="938" multiple-type="fixed" placeholder="全部" v-model="searchParams.systemBusinessLevelList" clearable ></ui-select>
                            </ui-search-item>
                            <ui-search-item label="商机阶段">
                                <ui-select :data="stageList"  multiple-type="fixed" placeholder="全部" v-model="searchParams.stageList" clearable></ui-select>
                            </ui-search-item>
                            <ui-search-item label="最近跟进日期">
                                <ui-date-picker
                                    v-model="searchParams.latestFollowUpTimeRange"
                                    type="daterange"
                                    start-placeholder="起始时间"
                                    end-placeholder="截止时间"
                                    @input="handlerFilterDateChange('latestFollowUpTime', $event)"
                                ></ui-date-picker>
                            </ui-search-item>
                            <ui-search-item label="任务截至日期">
                                <ui-date-picker
                                    v-model="searchParams.deadlineDateRange"
                                    type="daterange"
                                    start-placeholder="起始时间"
                                    end-placeholder="截止时间"
                                    @input="handlerFilterDateChange('deadlineDate', $event)"
                                ></ui-date-picker>
                            </ui-search-item>
                            <ui-search-item label="任务处理">
                                <ui-select placeholder="全部" :data="doneStatusList" v-model="searchParams.doneStatus" clearable></ui-select>
                            </ui-search-item>
                            <ui-search-item label="创建时间" :lock="true">
                                <ui-date-picker
                                    v-model="searchParams.addTimeRange"
                                    type="daterange"
                                    start-placeholder="起始时间"
                                    end-placeholder="截止时间"
                                    @input="handlerFilterDateChange('addTime', $event)"
                                ></ui-date-picker>
                            </ui-search-item>
                            <ui-search-item label="创建人">
                                <ui-select :remote="true" :avatar="true" multiple-type="fixed" :default-multi="searchParams.creatorMultiItems" placeholder="全部" v-model="searchParams.creatorIdList" clearable :remote-info="creatorListRemoteInfo" @change="remoteSelectChange('creatorMultiItems', $event)"></ui-select>
                            </ui-search-item>
                            <ui-search-item label="归属销售">
                                <ui-select :remote="true" :avatar="true" multiple-type="fixed" :default-multi="searchParams.belongerMultiItems" placeholder="全部" v-model="searchParams.belongerIdList" clearable :remote-info="belongerListRemoteInfo" @change="remoteSelectChange('belongerMultiItems', $event)"></ui-select>
                            </ui-search-item>
                            <ui-search-item label="销售部门">
                                <ui-cascader-new :multi="true" v-model="searchParams.organizationIdValues" :search="true" :list-data="departmentData" v-if="departmentData && departmentData.length" @change="handlerDepartmentChange"></ui-cascader-new>
                            </ui-search-item>
                            <!-- <ui-search-item label="销售区域">
                                <ui-cascader 
                                    class="margin" 
                                    :data="addressLv2Data" 
                                    v-model="searchParams.salerAreaSelectedIds" 
                                    clearable 
                                    multiple
                                    filterable
                                    @change="handleFilterSalerAddressChange"
                                    width="100%"
                                    v-if="addressLv2Data && addressLv2Data.length"
                                ></ui-cascader>
                            </ui-search-item> -->
                            <ui-search-item label="协作人">
                                <ui-select :remote="true" :avatar="true" multiple-type="fixed" :default-multi="searchParams.collaboratorMultiItems" placeholder="全部" v-model="searchParams.collaboratorIdList" clearable :remote-info="shareUserListRemoteInfo" @change="remoteSelectChange('collaboratorMultiItems', $event)"></ui-select>
                            </ui-search-item>
                            <ui-search-item label="我关注的">
                                <ui-select placeholder="全部" :data="attentionStateList" v-model="searchParams.attentionState" clearable></ui-select>
                            </ui-search-item>
                            <ui-search-item label="预计成单金额">
                                <ui-range-input :min.sync="searchParams.amountMin" :max.sync="searchParams.amountMax" placeholder="单位：元"></ui-range-input>
                            </ui-search-item>
                            <ui-search-item label="预计成单日期">
                                <ui-date-picker
                                    v-model="searchParams.orderTimeRange"
                                    type="daterange"
                                    start-placeholder="起始时间"
                                    end-placeholder="截止时间"
                                    @input="handlerFilterDateChange('orderTime', $event)"
                                ></ui-date-picker>
                            </ui-search-item>
                            <ui-search-item label="客户名称">
                                <ui-input v-model="searchParams.traderName"></ui-input>
                            </ui-search-item>
                            <ui-search-item label="联系人">
                                <ui-input v-model="searchParams.traderContactName"></ui-input>
                            </ui-search-item>
                            <ui-search-item label="手机">
                                <ui-input v-model="searchParams.mobile"></ui-input>
                            </ui-search-item>
                            <ui-search-item label="固话">
                                <ui-input v-model="searchParams.telephone"></ui-input>
                            </ui-search-item>
                            <ui-search-item label="产品信息">
                                <ui-input v-model="searchParams.productCommentsSale"></ui-input>
                            </ui-search-item>
                            <ui-search-item label="备注">
                                <ui-input v-model="searchParams.comments"></ui-input>
                            </ui-search-item>
                            <ui-search-item label="标签">
                                <ui-select :remote="true" multiple-type="fixed" :default-multi="searchParams.tagsMultiItems" placeholder="全部" v-model="searchParams.tagIdList" clearable :remote-info="tagsRemoteInfo" @change="remoteSelectChange('tagsMultiItems', $event)"></ui-select>
                            </ui-search-item>
                            <ui-search-item label="终端名称">
                                <ui-input v-model="searchParams.terminalTraderName"></ui-input>
                            </ui-search-item>
                            <ui-search-item label="终端性质">
                                <ui-select dictionary="5600" multiple-type="fixed" placeholder="全部" v-model="searchParams.terminalTraderNatureList" clearable ></ui-select>
                            </ui-search-item>
                            <ui-search-item label="终端区域">
                                <ui-cascader 
                                    class="margin" 
                                    :data="addressData" 
                                    v-model="searchParams.areaSelectedIds" 
                                    clearable 
                                    multiple
                                    filterable
                                    @change="handleFilterAddressChange"
                                    width="100%"
                                    v-if="addressData && addressData.length"
                                ></ui-cascader>
                            </ui-search-item>
                            <ui-search-item label="客情关系">
                                <ui-select placeholder="全部" :data="customerRelationshipList" multiple-type="fixed" v-model="searchParams.customerRelationshipList" clearable></ui-select>
                            </ui-search-item>
                            <ui-search-item label="采购方式">
                                <ui-select dictionary="404" placeholder="全部" v-model="searchParams.purchasingType" clearable ></ui-select>
                            </ui-search-item>
                            <ui-search-item label="招标阶段">
                                <ui-select dictionary="5800" multiple-type="fixed" placeholder="全部" v-model="searchParams.biddingPhaseList" clearable ></ui-select>
                            </ui-search-item>
                            <ui-search-item label="招标参数">
                                <ui-select placeholder="全部" :data="biddingParameterList" v-model="searchParams.biddingParameter" clearable></ui-select>
                            </ui-search-item>
                            <ui-search-item label="来源">
                                <!-- <ui-select :first-remote="true" :remote-info="sourceRemoteInfo" placeholder="全部" v-model="searchParams.type" clearable></ui-select> -->
                                <ui-select placeholder="全部" :data="sourceList" v-model="searchParams.type" clearable></ui-select>
                            </ui-search-item>
                            <ui-search-item label="线索编号">
                                <ui-input v-model="searchParams.leadsNo"></ui-input>
                            </ui-search-item>
                            <ui-search-item label="订货号">
                                <ui-input v-model="searchParams.skuNo"></ui-input>
                            </ui-search-item>
                            <ui-search-item label="产品名称">
                                <ui-input v-model="searchParams.productName"></ui-input>
                            </ui-search-item>
                            <ui-search-item label="商品分类">
                                <ui-cascader 
                                    class="margin" 
                                    :data="allCategoryList" 
                                    v-model="searchParams.selectedCategoryIdList" 
                                    clearable 
                                    multiple
                                    filterable
                                    @change="handleFilterCategoryChange"
                                    width="100%"
                                    v-if="allCategoryList && allCategoryList.length"
                                ></ui-cascader>
                            </ui-search-item>
                            <ui-search-item label="品牌">
                                <ui-input v-model="searchParams.brandName"></ui-input>
                            </ui-search-item>
                            <ui-search-item label="型号">
                                <ui-input v-model="searchParams.modelNumber"></ui-input>
                            </ui-search-item>
                        </template>
                        <template v-slot:list-button>
                            <ui-button @click="multiAttention">关注</ui-button>
                            <ui-button @click="multiAddPartner">添加协作人</ui-button>
                        </template>
                        <template v-slot:bussinessChanceNo="{ row }">
                            <div class="td-link" @click="GLOBAL.link({name: '商机详情', url: '/crm/businessChance/profile/detail?id=' + row.bussinessChanceId})">{{row.bussinessChanceNo}}</div>
                        </template>
                        <template v-slot:latestCommunicateRecordContent="{ row }">
                            <div class="record-wrap" v-if="row.latestCommunicateRecordId" :title="row.latestCommunicateRecordContent">
                                <i class="vd-ui_icon icon-sms" @click="showRecordDialog(row)"></i>
                                <div class="record-txt text-line-1 " >{{ row.latestCommunicateRecordContent || '-' }}</div>
                            </div>
                            <template v-else>-</template>
                        </template>
                        <template v-slot:latestTaskContent="{ row }">
                            <div class="record-wrap" v-if="row.latestTaskContent" :title="row.latestTaskContent">
                                <i class="vd-ui_icon icon-list" @click="showTaskDialog(row)"></i>
                                <div class="record-txt text-line-1 " >{{ row.latestTaskContent || '-' }}</div>
                            </div>
                            <template v-else>-</template>
                        </template>
                        <template v-slot:traderName="{ row }">
                            <template v-if="row.traderName">
                                <div class="td-tyc-wrap">
                                    <div class="text-line-1" :title="row.traderName">
                                        <template v-if="row.traderNameLink">
                                            <div class="td-link" @click="GLOBAL.link({name:'客户详情', url: row.traderNameInnerLink, link: row.traderNameLink, nohost: true})">{{ row.traderName }}</div>
                                        </template>
                                        <template v-else>{{ row.traderName }}</template>
                                    </div>
                                    <span v-if="row.tycFlag == 'Y'" @click="openTyc(row.traderName)" class="tyc-icon"></span>
                                </div>
                            </template>
                            <template v-else>-</template>
                        </template>
                        <template v-slot:amount="{ row }">
                            <template v-if="row.amount || row.amount == 0">
                                <div class="text-line-1" :title="row.amount + '元'">{{row.amount.toFixed(2)}}</div>
                            </template>
                            <template v-else>-</template>
                        </template>
                        <template v-slot:customerRelationship="{ row }">
                            <div class="text-line-1">{{ getCustomerRelationshipStr(row.customerRelationship) }}</div>
                        </template>
                        <!-- <template v-slot:biddingParameter="{ row }">
                            <div class="text-line-1" v-if="row.biddingParameter">{{ {1:'可调整', 2:'不可调整'}[row.biddingParameter] }}</div>
                            <template v-else>-</template>
                        </template> -->
                        <template v-slot:tags="{ row }">
                            <div class="text-line-1" :title="getTagsStr(row)">{{ getTagsStr(row) }}</div>
                        </template>
                        <template v-slot:leadsNo="{ row }">
                            <div class="td-link" @click="GLOBAL.link({name: '查看线索', url: '/crm/businessLeads/profile/detail?id=' + row.businessLeadsId})" v-if="row.leadsNo">{{row.leadsNo}}</div>
                            <template v-else>-</template>
                        </template>
                        <template v-slot:option="{ row }">
                            <div class="option-wrap">
                                <a class="table-edit" @click="attentionItem(row, 0)" v-if="row.attentionState == 1">取消关注</a>
                                <a class="table-edit" @click="attentionItem(row, 1)" v-else>关注</a>
                            </div>
                        </template>
                    </ui-search-container>
                </div>
            </div>
            
            <ui-poper :show="isShowEditWrapper" position="middle" :el="tableEditEl" ref="editDropwrap">
                <div class="table-edit-wrap">
                    <div class="edit-content">
                        <template v-if="editKey === 'amount'">
                            <ui-input ref="editTdInput" v-model="edit_amount" maxlength="10" :error-msg="editErrorMsg" :errorable="editErrorable" @blur="validSubmitEdit"></ui-input>
                        </template>
                        <template v-if="editKey === 'traderContactName'">
                            <ui-input v-model="edit_traderContactName" ref="editTdInput" maxlength="20" :error-msg="editErrorMsg" :errorable="editErrorable" @blur="validSubmitEdit"></ui-input>
                        </template>
                        <template v-if="editKey === 'productCommentsSale'">
                            <ui-input v-model="edit_productCommentsSale" ref="editTdInput" maxlength="500" resize="none" type="textarea" :error-msg="editErrorMsg" :errorable="editErrorable"></ui-input>
                        </template>
                        <template v-if="editKey === 'comments'">
                            <ui-input v-model="edit_comments" ref="editTdInput" maxlength="500" resize="none" type="textarea"></ui-input>
                        </template>
                        <template v-if="editKey === 'orderTimeDate'">
                            <ui-date-picker v-model="edit_orderTimeDate" ref="editDate" :calcerror="false" :error-msg="editErrorMsg" :errorable="editErrorable" placeholder="请选择日期" @input="validSubmitEdit" zindex="3000"></ui-date-picker>
                        </template>
                        <template v-if="editKey === 'tags'">
                            <div style="width: 265px;">
                                <ui-select :remote="true" multiple-type="fixed" ref="editTdInput" :default-multi="edit_tagsSelectedItems" placeholder="请选择" v-model="edit_tagIds" clearable :remote-info="tagsRemoteInfo"></ui-select>
                            </div>
                        </template>
                        <template v-if="editKey === 'terminalTraderNatureStr'">
                            <ui-select dictionary="5600" placeholder="请选择" ref="editTdInput" v-model="edit_terminalTraderNature" clearable ></ui-select>
                        </template>
                        <template v-if="editKey === 'terminalTraderRegionStr'">
                            <ui-cascader
                                class="margin"
                                :data="addressData"
                                v-model="edit_terminalTraderRegion"
                                clearable
                                filterable
                                ref="editTdInput"
                                @change="handleEditAreaChange"
                            ></ui-cascader>
                        </template>
                    </div>
                    <div class="edit-option">
                        <ui-button type="primary" icon="icon-selected2" @click="submitEdit"></ui-button>
                        <ui-button icon="icon-delete" @click="cancelEdit"></ui-button>
                    </div>
                </div>
            </ui-poper>
            <!-- 跟进记录 -->
            <follow-record-list-dialog ref="followRecordListDialog" :communicate-type="244" @addrecord="handlerRecordAdd"></follow-record-list-dialog> 
            <!-- 任务 dialog -->
            <renwu-dialog ref="renwuDialog"></renwu-dialog>
            <!-- 天眼查详情 -->
            <ui-tyc-detail ref="tycDetail"></ui-tyc-detail>
            <!-- 列表操作提示 -->
            <ui-list-option-tip></ui-list-option-tip>
            <!-- 添加协作人弹层 -->
            <partner-create-dialog ref="partnerCreateDialog" :success-fun="handlerAddPartner"></partner-create-dialog>
        </div>
    </div>

    <script src="/static/js/common/jquery.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/vue.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/axios.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/vuedraggable.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/moment.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/lodash.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/ui/ui.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/layout.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>

    <!-- 协作人 -->
    <script src="/static/js/common/components/business/partner.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>

     <!-- 任务记录 -->
     <script src="/static/js/common/components/business/renwu.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>

    <script src="/static/js/common/components/business/records.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/pages/businesschanceList.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
</body>
</html>
