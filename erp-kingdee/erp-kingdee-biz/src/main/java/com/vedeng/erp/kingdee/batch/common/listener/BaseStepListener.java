package com.vedeng.erp.kingdee.batch.common.listener;

import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.annotation.AfterStep;
import org.springframework.batch.core.annotation.BeforeStep;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: step监听器
 * @date 2023/2/21 16:47
 */
@Component
@Slf4j
public class BaseStepListener {

    @BeforeStep
    public void beforeStep(StepExecution stepExecution) {
        log.info("before step execute: " + stepExecution.getStepName());
    }

    @AfterStep
    public void afterStep(StepExecution stepExecution) {
        log.info("after step execute: " + stepExecution.getStepName());
    }
}
