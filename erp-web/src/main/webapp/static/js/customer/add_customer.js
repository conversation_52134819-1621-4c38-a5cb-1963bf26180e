$(function() {
	var needClick = window.localStorage.getItem('needClick');

	if(needClick){
		window.localStorage.setItem('needClick', '');
		$("#eye").click();
	}

	// 获取单选按钮
	var effectivenessRadios = document.querySelectorAll('input[name="effectiveness"]');
	// 获取隐藏的下拉框和输入框
	var invalidReasonList = document.getElementById('invalidReasonList');
	var reasonInput = document.getElementById('reasonInput');

	// 监听单选按钮的点击事件
	effectivenessRadios.forEach(radio => {
		radio.addEventListener('change', function() {
			if (this.value === '0') {
				// 如果选择“无效”，显示下拉框和输入框
				$('.queryInvalidReasonList').css('display', 'block');
				
			} else {
				// 如果选择“有效”，隐藏下拉框和输入框
				$('.queryInvalidReasonList').css('display', 'none');
				$('.reasonInput').css('display', 'none');
				// 清除值
				$("#queryInvalidReasonListSelect").val("");
				$("#otherReason").val("");
			}
		});
	});
	

	$("li[alt='fenxiao']").hide();
	$("li[alt='workAreaLi']").hide();
	$(".addtags").hide();
	//初始化客户分类
	initCustomerCategory();
	
	//员工人数\年销售额\客户来源\首次询价方式\战略合作伙伴\贝登VIP
	initCustomerInfo();
	
	//品牌信息
	initBrand();
	
	//经营地区更改
	$("select[name='bussiness_province']").change(function(){
		checkLogin();
		var regionId = $(this).val();
		if(regionId > 0){
			$.ajax({
				type : "POST",
				url : page_url+"/system/region/getregion.do",
				data :{'regionId':regionId},
				dataType : 'json',
				success : function(data) {
					$option = "<option value='0'>请选择</option>";
					$.each(data.listData,function(i,n){
						$option += "<option value='"+data.listData[i]['regionId']+"'>"+data.listData[i]['regionName']+"</option>";
					});
					$("select[name='bussiness_city'] option:gt(0)").remove();
					$("select[name='bussiness_zone'] option:gt(0)").remove();
					$("select[name='bussiness_city']").html($option);
					$("select[name='bussiness_zone']").html("<option value='0'>请选择</option>");
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
		}else if(regionId==0){
			$("select[name='bussiness_city'] option:gt(0)").remove();
			$("select[name='bussiness_zone'] option:gt(0)").remove();
		}
	});
	
	//经营地区更改
	$("select[name='bussiness_city']").change(function(){
		checkLogin();
		var regionId = $(this).val();
		if(regionId > 0){
			$.ajax({
				type : "POST",
				url : page_url+"/system/region/getregion.do",
				data :{'regionId':regionId},
				dataType : 'json',
				success : function(data) {
					$option = "<option value='0'>请选择</option>";
					$.each(data.listData,function(i,n){
						$option += "<option value='"+data.listData[i]['regionId']+"'>"+data.listData[i]['regionName']+"</option>";
					});
					$("select[name='bussiness_zone'] option:gt(0)").remove();
					$("select[name='bussiness_zone']").html($option);
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
		}
	});

	var invalidReasonLayCascader = null;
	$(document).ready(function(){
		initInvalidReasonSelect();

	});

	function initInvalidReasonSelect() {
		layui.use('layCascader', function () {
			var layCascader = layui.layCascader;
			let invalidReasonList = $("#invalidReason").val();
			if (invalidReasonList != null && invalidReasonList.length > 0) {
				invalidReasonList = JSON.parse(invalidReasonList);
			} else {
				invalidReasonList = [];//
			}
			invalidReasonLayCascader = layCascader({
				elem: '#queryInvalidReasonListSelect',
				placeholder: '请选择无效原因',
				clearable: true,
				collapseTags: true,
				filterable: true,
				value: invalidReasonList,
				minCollapseTagsNumber: 4,
				extendClass: true,
				extendStyle: true,
				props: {
					multiple: true
				},
				change(value){
					console.log(value);
				},
				options: [
					{value: "公司转行", label: "公司转行"},
					{value: "公司注销", label: "公司注销"},
					{value: "服务型公司", label: "服务型公司"},
					{value: "其他", label: "其他"},
				]
			});
			invalidReasonLayCascader.change(function (value, node) {
				// 遍历数组，
				// 如果包含7
				const shouldHide = value.includes("其他"); 

				// 根据判断结果隐藏或显示元素
				if (shouldHide) {
					$('.reasonInput').css('display', 'block');
				} else {
					$('.reasonInput').css('display', 'none');
					// 清除值
					$("#otherReason").val("");
				}
			});
		});
	}
	//经营地区更改
	$("select[name='work_province']").change(function(){
		checkLogin();
		var regionId = $(this).val();
		if(regionId > 0){
			$.ajax({
				type : "POST",
				url : page_url+"/system/region/getregion.do",
				data :{'regionId':regionId},
				dataType : 'json',
				success : function(data) {
					$option = "<option value='0'>请选择</option>";
					$.each(data.listData,function(i,n){
						$option += "<option value='"+data.listData[i]['regionId']+"'>"+data.listData[i]['regionName']+"</option>";
					});
					$("select[name='work_city'] option:gt(0)").remove();
					$("select[name='work_zone'] option:gt(0)").remove();
					$("select[name='work_city']").html($option);
					$("select[name='work_zone']").html("<option value='0'>请选择</option>");
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
		}else if(regionId==0){
			$("select[name='work_city'] option:gt(0)").remove();
			$("select[name='work_zone'] option:gt(0)").remove();
		}
	});

	//经营地区更改
	$("select[name='work_city']").change(function(){
		checkLogin();
		var regionId = $(this).val();
		if(regionId > 0){
			$.ajax({
				type : "POST",
				url : page_url+"/system/region/getregion.do",
				data :{'regionId':regionId},
				dataType : 'json',
				success : function(data) {
					$option = "<option value='0'>请选择</option>";
					$.each(data.listData,function(i,n){
						$option += "<option value='"+data.listData[i]['regionId']+"'>"+data.listData[i]['regionName']+"</option>";
					});
					$("select[name='work_zone'] option:gt(0)").remove();
					$("select[name='work_zone']").html($option);
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
		}
	});


	//业务模式
	$("input[name='traderCustomer.directSelling']").change(function(){
		checkLogin();
		if($("input[name='traderCustomer.directSelling']").val()*1 !=0 && $("input[name='traderCustomer.directSelling']").val()*1<=100){
			$("input[name='traderCustomer.wholesale']").val(100-$("input[name='traderCustomer.directSelling']").val()*1);
		}else{
			$("input[name='traderCustomer.wholesale']").val("");
		}
	});
	$("input[name='traderCustomer.wholesale']").change(function(){
		checkLogin();
		if($("input[name='traderCustomer.wholesale']").val()*1 !=0 && $("input[name='traderCustomer.wholesale']").val()*1<=100){
			$("input[name='traderCustomer.directSelling']").val(100-$("input[name='traderCustomer.wholesale']").val()*1);
		}else{
			$("input[name='traderCustomer.directSelling']").val("");
		}
	});

	function hasValueFive() {
		let hasFive = false; // 默认值为 false

		// 遍历所有 select 元素
		$("#customer_category_div").find("select").each(function() {
			if ($(this).val() == 5) {
				hasFive = true; // 如果找到值为 5 的 select，设置为 true
				return false; // 退出 each 循环
			}
		});

		return hasFive; // 返回结果
	}
	
	var beforeSubmit = function(){
		
		checkLogin();
		if (vm.checkOtherInstitutionType()) {
			return false;
		}

		if (vm.checkOtherAgencyBrand()) {
			return false;
		}
		if (vm.checkOtherAgencySku()) {
			return false;
		}
		if (vm.checkOtherGovernmentRelation()) {
			return false;
		}

		let val = $("[name=belongPlatform]").val();
		if (val == 0 || val == '') {
			layer.alert("请选择归属平台!")
			return false;
		}
		var hasFive = hasValueFive();
		if (hasFive){
			var selectVal2 = document.getElementById("queryInvalidReasonListSelect").value;
			var selectListJson2 = JSON.parse(selectVal2==""?"[]":selectVal2);
			var queryInvalidReasonList = selectListJson2.join(',');
			$("#invalidReason").val(queryInvalidReasonList);

			// 1有效 0无效
			var selectedValue = document.querySelector('input[name="effectiveness"]:checked').value;

			if (selectedValue == 0){
				// 判断原因是否为空
				if (queryInvalidReasonList == "") {
					layer.alert("请填写无效原因");
					return false;
				}
				if (queryInvalidReasonList.includes("其他")){
					if ($("#otherReason").val() == "") {
						layer.alert("请填写具体原因");
						return false;
					}
					if ($("#otherReason").val().length > 50) {
						layer.alert("具体原因不能超过50字");
						return false;
					}
				}
			}else{
				$("#invalidReason").val("");
				$("#otherReason").val("");
			}
		}else{
			$("#invalidReason").val("");
			$("#otherReason").val("");
		}
		

		let theDataOfThePage = vm.getTheDataOfThePage();
		$("#theDataOfThePage").val(theDataOfThePage)
		$(".warning").remove();
		$("select").removeClass("errorbor");
		var sb =true;

		var traderName = $.trim($("input[name='traderName']").val());
		$("input[name='traderName']").val(traderName);
		// var traderNameReg = /^[a-zA-Z0-9\u4e00-\u9fa5\.\(\)\,\（\）\s]{2,128}$/;
		if(traderName == ""){
			warnTips("eye","客户名称不允许为空");
			return false;
		}
		//客户属性
		console.log($("#client_property_li").css('display'))


		if(traderName.length < 2 || traderName.length > 128){
			warnTips("eye","客户名称长度为2-128个字符长度");
			return false;
		}
		
		
		// if(!traderNameReg.test(traderName)){
		// 	warnTips("eye","客户名称不允许使用特殊字符");
		// 	return false;
		// }

		//省是否选择
		var isProviceSelected = $("#warehouseProvince").val() != 0;
		//区是否选择
		var isZoneSelected = $("#warehouseZone").val() != 0;

		//详细地址是否填写
		var warehouseDetailAddressSelect = $("#warehouseDetailAddress").val() != '';

		if(isProviceSelected){
			if(!isZoneSelected){
				layer.alert("仓库地址省市区必须选择到区!")
				return false;
			}
			if(!warehouseDetailAddressSelect){
				layer.alert("请填写仓库地址详细地址!")
				return false;
			}
		}

		if(warehouseDetailAddressSelect){
			if(!isZoneSelected){
				layer.alert("请选择仓库地址省市区!")
				return false;
			}
		}

		if($("#warehouseDetailAddress").val() != "" && strlen($("#warehouseDetailAddress").val()) > 120){
			layer.alert("详细地址不能超过60个字");
			return false;
		}

		//客户是否存在
		$.ajax({
			type : "POST",
			url : page_url+"/trader/customer/gettraderbytradername.do",
			data : {traderName:traderName,'traderType':1},
			dataType : 'json',
			async : false,
			success : function(data) {
				if(data.code == 0){
					//客户已经存在
					if(data.data.traderCustomer != null && data.data.traderCustomer.traderCustomerId != undefined && data.data.traderCustomer.traderCustomerId > 0){
						sb = false;
						return false;
					}
				}
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});

		if(!sb){
			warnTips("eye","客户名称不允许重复");
			return false;
		}

		if($("select[name='province']").val() == 0){
			$("select[name='province']").addClass("errorbor");
			$("#zone").siblings('.warning').remove();
			$("#zone").after('<div class="warning">省份不允许为空</div>');
			return false;
		}
		if($("select[name='city']").val() == 0){
			$("select[name='city']").addClass("errorbor");
			$("#zone").siblings('.warning').remove();
			$("#zone").after('<div class="warning">地市不允许为空</div>');
			return false;
		}
		if($("select[name='zone']").val() == 0 && $("select[name='zone'] option").length > 1){
			$("select[name='zone']").addClass("errorbor");
			$("#zone").siblings('.warning').remove();
			$("#zone").after('<div class="warning">区县不允许为空</div>');
			return false;
		}



		var customer_category_ok = true;
		let value = $('select[name=customerCategory2]').val();
		let length = $("#customer_category_div select").length;
		
		$.each($("#customer_category_div select"),function(i,n){
			if (i <=length - 1) {
				if ($(this).val() == 0&&value!=6) {
					customer_category_ok = false;
				}
				if (value==6&&$(this).val() =='') {
					customer_category_ok = false;
				}
			} else {
				if (value == 6) {
					if ($(this).val() == '') {
						customer_category_ok = false;
					}
				}

			}

		});
		if(!customer_category_ok){

			$.each($("#customer_category_div select"),function(i,n){
				if (i <= length - 1) {
					if ($(this).val() == 0) {
						customer_category_ok = false;
						$(this).addClass("errorbor");
					}
				} else {
					if (value == 6) {
						if ($(this).val() == '') {
							customer_category_ok = false;
							$(this).addClass("errorbor");
						}
					}
				}

			});
			$("#customer_category_div").after('<div class="warning">客户类型不允许为空</div>');
			return false;
		}

		var show_fenxiao = false;
		// $.each($("#customer_category_div > select"),function(i,n){
		// 	var id = $(this).val();
		// 	show_fenxiao = id==3||id==5?true:false;
		// })//以上写法存有bug,修改成取第二个select
		$.each($('div#customer_category_div select:eq(1)'),function(i,n){
			var id = $(this).val();
			show_fenxiao = id==3||id==5?true:false;
		})

		if(show_fenxiao){
			if(($("input[name='traderCustomer.directSelling']").val()*1 !=0
					|| $("input[name='traderCustomer.wholesale']").val()*1 !=0)
				&& ($("input[name='traderCustomer.directSelling']").val()*1+$("input[name='traderCustomer.wholesale']").val()*1 )!=100
			){
				warnTips("wholesale","直销比例与批发比例总和应为100%");
				return false;
			}
		}
		var selectedValue = $('#customer_category').val();
		if (selectedValue == 2) {
			if($("select[name='work_province']").val() == 0){
				$("select[name='work_province']").addClass("errorbor");
				$("#work_zone").siblings('.warning').remove();
				$("#work_zone").after('<div class="warning">办公地区省必填</div>');
				return false;
			}
			if($("select[name='work_city']").val() == 0){
				$("select[name='work_city']").addClass("errorbor");
				$("#work_zone").siblings('.warning').remove();
				$("#work_zone").after('<div class="warning">办公地区市必填</div>');
				return false;
			}
			if($("select[name='work_zone']").val() == 0){
				$("select[name='work_zone']").addClass("errorbor");
				$("#work_zone").siblings('.warning').remove();
				$("#work_zone").after('<div class="warning">办公地区区必填</div>');
				return false;
			}
		}


		$.each($(".bt"),function(i,n){
			var tab = $(n).find('.infor_name').find('lable').html();
			var ok = false;
			$.each($(n).find('.inputradio').find("input[type='checkbox']"),function(j,m){
				if($(m).prop("checked")){
					ok = true;
				}
			});
			$.each($(n).find('.inputfloat').find("input[type='checkbox']"),function(j,m){
				if($(m).prop("checked")){
					ok = true;
				}
			});
			$.each($(n).find('.inputfloat').find("input[type='radio']"),function(o,p){
				if($(p).prop("checked")){
					ok = true;
				}
			});
			$.each($(n).find('.inputradio').find("input[type='radio']"),function(o,p){
				if($(p).prop("checked")){
					ok = true;
				}
			});
			$.each($(n).find("select"),function(k,l){
				if($(l).val() != 0){
					ok = true;
				}
			});
			if(!ok){
				sb = false;
				$.each($(n).find("select"),function(k,l){
					$(this).addClass("errorbor");
				});
				$(n).find("div:last").append('<div class="warning">'+'请选择'+tab+'</div>')
				return false;
			}
		});

		$.each($("input[name^='attributedesc']"),function(){
			var ok = true;
			if($(this).val().length >32){
				ok = false;
			}

			if(!ok){
				sb = false;
				layer.alert("其它长度不允许超过32个字符");
				return false;
			}
		});
		debugger;
		//验证，如果有代理商品或者品牌，则必填
		let theDataOfThePageObject = JSON.parse(theDataOfThePage)

		if(theDataOfThePageObject.principal.isProductCustomer  && theDataOfThePageObject.principal.customerHasAgency == 2){
			layer.alert("请选择是否有无代理商品或代理品牌");
			return false;
		}

		if(show_fenxiao && $('select[name="customer_category"]').val()=='2' && !_.isEmpty(theDataOfThePageObject.principal.customerHasAgency) && theDataOfThePageObject.principal.customerHasAgency == 1){
			debugger;
			if(_.isEmpty(theDataOfThePageObject.principal.agencySkuChecked) &&  _.isEmpty(theDataOfThePageObject.principal.agencyBrandChecked)
				&&  _.isEmpty(theDataOfThePageObject.principal.otherAgencySku) && _.isEmpty(theDataOfThePageObject.principal.otherAgencyBrand)
			&& theDataOfThePageObject.principal.isProductCustomer == 1){
				layer.alert("请在代理品牌或代理商品中，任意填写至少一项")
				return false;
			}
		}
		if(show_fenxiao && theDataOfThePageObject.principal.isChannelCustomer==true && _.size(theDataOfThePageObject.principal.governmentRelationChecked)==0){
			layer.alert("请勾选客户型渠道商")
			return false;
		}

		return sb;
	}

	$('.J-add').click(function (e) {
		e.preventDefault();
		if(beforeSubmit()){
			if($("[name=customerCategory6]").val() == 64){
				layer.confirm("请确认客户信息填写是否正确", {
					btn: ['确定', '取消'] //按钮
				}, function () {
					$("#customerForm").submit();
					var index = layer.load(1, {
						shade: [0.1,'#fff'] //0.1透明度的白色背景
					});
					setTimeout(function(){
						layer.close(index);
					}, 4000);
				}, function () {
					layer.close()
				});
			}else{
				$("#customerForm").submit();
				var index = layer.load(1, {
					shade: [0.1,'#fff'] //0.1透明度的白色背景
				});
				setTimeout(function(){
					layer.close(index);
				}, 4000);
			}
		}
	});

	// $("#customerForm").submit(function(){
	// 	return beforeSubmit();
	// });

	$('.J-submit').click(function () {
		if(beforeSubmit()){
			$.ajax({
				url: '/trader/customer/saveadd2.do',
				data: $('#customerForm').serialize(),
				type: 'post',
				dataType: 'json',
				success: function (res) {
					if(res.code == 0){
						window.parent && window.parent.newCustomer(res.data.traderId);
					}else{
						layer.alert(res.message)
					}
				}
			})
		}
	});
	
	$("input[name='traderName']").change(function(){
		checkLogin();
		$(".warning").remove();
		$("input[name='traderName']").removeClass("errorbor");
		if($(this).val() != ''){
			$.ajax({
				type : "POST",
				url : page_url+"/trader/customer/gettraderbytradername.do",
				data : {traderName:$(this).val(),'traderType':1},
				dataType : 'json',
				async : false,
				success : function(data) {
					if(data.code == 0){
						//客户已经存在
						if(data.data.traderCustomer != null && data.data.traderCustomer.traderCustomerId != undefined && data.data.traderCustomer.traderCustomerId > 0){
							warnTips("eye","客户名称不允许重复");
							return false;
						}
						//客户已经是供应商
						if(data.data.traderSupplier !=null && data.data.traderSupplier.traderSupplierId != undefined && data.data.traderSupplier.traderSupplierId > 0){
							var regionIds = data.data.areaIds.split(",");
							if(regionIds[0] != undefined){
								$("select[name='province']").val(regionIds[0]);
								$.ajax({
									type : "POST",
									url : page_url+"/system/region/getregion.do",
									data :{'regionId':regionIds[0]},
									dataType : 'json',
									async : false,
									success : function(data) {
										$option = "<option value='0'>请选择</option>";
										$.each(data.listData,function(i,n){
											var selected = data.listData[i]['regionId'] == regionIds[1] ? "selected" : "";
											$option += "<option value='"+data.listData[i]['regionId']+"' "+selected+">"+data.listData[i]['regionName']+"</option>";
										});
										$("select[name='city'] option:gt(0)").remove();
										$("select[name='zone'] option:gt(0)").remove();
										$("select[name='city']").html($option);
										$("select[name='zone']").html("<option value='0'>请选择</option>");
									}
								});
							}
							if(regionIds[1] != undefined){
								$.ajax({
									type : "POST",
									url : page_url+"/system/region/getregion.do",
									data :{'regionId':regionIds[1]},
									dataType : 'json',
									async : false,
									success : function(data) {
										$option = "<option value='0'>请选择</option>";
										$.each(data.listData,function(i,n){
											var selected = data.listData[i]['regionId'] == regionIds[2] ? "selected" : "";
											$option += "<option value='"+data.listData[i]['regionId']+"' "+selected+">"+data.listData[i]['regionName']+"</option>";
										});
										$("select[name='zone'] option:gt(0)").remove();
										$("select[name='zone']").html($option);
									}
								});
							}
						}
					}
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
		}
	});
	
});

//诊所单选控制
function attrCheck(obj){
	checkLogin();
	if($(obj).prop('checked')){
		$.each($(obj).parent().siblings('li').find("input[type='checkbox']"),function(i,n){
			if($(this).prop('checked')){
				$(this).prop('checked',false);
			}
		});
	}
	
}

function doHelp() {
	layer.open({
		type: 2,
		content: '/traderCustomerTerminal/help.do',
		area: ['600px', '450px'],
		title :'帮助',
		btnAlign: 'c',

	});
}

//员工人数\年销售额\客户来源\首次询价方式\战略合作伙伴
function initCustomerInfo(){
	checkLogin();
	$.ajax({
		type : "POST",
		url : page_url+"/trader/customer/gettradercustomeroption.do",
		dataType : 'json',
		success : function(data) {
			if(data.code == 0){
				var attr =  eval('(' + data.param + ')');
				$.each(attr,function(i,n){
					if(i == 1016){//员工人数
						var opt = "<option value='0'>请选择</option>";
						$.each(n,function(j,m){
							opt += "<option value='"+m['sysOptionDefinitionId']+"'>"+m['title']+"</option>";
							
						});
						$("select[name='traderCustomer.employees']").html(opt);
					}
					if(i == 1017){//年销售额
						var opt = "<option value='0'>请选择</option>";
						$.each(n,function(j,m){
							opt += "<option value='"+m['sysOptionDefinitionId']+"'>"+m['title']+"</option>";
							
						});
						$("select[name='traderCustomer.annualSales']").html(opt);
					}
					if(i == 1018){//销售模式
						var radio = "";
						$.each(n,function(j,m){
							radio += "<input type='radio' name='traderCustomer.salesModel' value='"+m['sysOptionDefinitionId']+"'/> <label class='mr8'>"+m['title']+"</label>";
							
						});
						$("#salesModel_div").html(radio);
					}
					if(i == 1009){//客户来源
						var radio = "<ul>";
						$.each(n,function(j,m){
							if(m['sysOptionDefinitionId'] != 3812) {
								//大数据拓客不在新增和编辑页展示
								radio += "<li><input type='radio' name='traderCustomer.customerFrom' value='" + m['sysOptionDefinitionId'] + "'/> <label class='mr8'>" + m['title'] + "</label></li>";
							}
						});
						radio += "</ul>";
						$("#customerFrom_div").html(radio);
					}
					if(i == 1010){//首次询价方式
						var radio = "<ul>";
						$.each(n,function(j,m){
							radio += "<li><input type='radio' name='traderCustomer.firstEnquiryType' value='"+m['sysOptionDefinitionId']+"'/> <label class='mr8'>"+m['title']+"</label></li>";
							
						});
						radio += "</ul>";
						$("#firstEnquiryType").html(radio);
					}
					if(i == 1033){//战略合作伙伴
						var li = "";
						$.each(n,function(j,m){
							li += "<li><input type='checkbox' name='attributeId' value='26_"+m['sysOptionDefinitionId']+"' /><label>"+m['title']+"</label></li>";
						});
						
						$("#zlhzhb_ul").html(li);
					}
				});
			}
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});
}

//客户分类初始化
function initCustomerCategory(){
	checkLogin();
	$.ajax({
		type : "POST",
		url : page_url+"/trader/customer/gettradercustomercategory.do",
		data :{'parentId':0},
		dataType : 'json',
		success : function(data) {
			if(data.code == 0){
				$option = "<option value='0'>请选择</option>";
				$.each(data.listData,function(i,n){
					$option += "<option value='"+data.listData[i]['traderCustomerCategoryId']+"'>"+data.listData[i]['customerCategoryName']+"</option>";
				});
				
				$("select[name='customer_category']").html($option);
			}
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});
}

function changeClientProperty(obj) {
	var id = $(obj).val();

	//如果是分公司 显示归属总公司
	if(id == 1){
		$("#ascription_headquarters_li").show().attr('disabled', false);
		$("#parentId").attr('disabled', false);
	}else{
		$("#ascription_headquarters_li").hide().attr('disabled', true);
		$("#parentId").attr('disabled', true);
	}
}

// 终端页选中后传递属性
function callbackTerminal(data) {
	console.log(data)

	setTimeout(function (){

		if (data.traderCustomerMarketingType != null && data.traderCustomerMarketingType != '') {

			if (checkIsExtend('customerCategory6',data.traderCustomerMarketingType)) {
				$('select[name="customerCategory6"]').val(data.traderCustomerMarketingType).trigger('change');
			}


		}

		setTimeout(function (){

			if (data.institutionNature != null &&data.institutionNature!=undefined && data.institutionNature != '') {

				if (checkIsExtend('traderCustomer.traderCustomerMarketingDto.institutionNature',data.institutionNature)) {
					$('select[name="traderCustomer.traderCustomerMarketingDto.institutionNature"]').val(data.institutionNature).trigger('change');
				}
			}


			if (data.institutionLevel!=null&&data.institutionLevel!=undefined &&data.institutionLevel!='') {
				debugger
				if (checkIsExtend('traderCustomer.traderCustomerMarketingDto.institutionLevel', data.institutionLevel)) {
					$('select[name="traderCustomer.traderCustomerMarketingDto.institutionLevel"]').val(data.institutionLevel).trigger('change');
				}

			}

			if (data.institutionType != null &&data.institutionType!=undefined && data.institutionType != '') {
				debugger
				if (checkIsExtend('traderCustomer.traderCustomerMarketingDto.institutionType', data.institutionType)) {
					$('select[name="traderCustomer.traderCustomerMarketingDto.institutionType"]').val(data.institutionType).trigger('change');
				}

			}

			setTimeout(function (){
				if (data.institutionTypeChild != null && data.institutionTypeChild != undefined && data.institutionTypeChild != '') {

					if (checkIsExtend('traderCustomer.traderCustomerMarketingDto.institutionTypeChild', data.institutionTypeChild)) {
						$('select[name="traderCustomer.traderCustomerMarketingDto.institutionTypeChild"]').val(data.institutionTypeChild).trigger('change');
					}

				}
			},500)

		},500)

	},500)

	$("#traderName").val(data.hosName)
	oneDataManagementShowOrhide(true);

	if (data.managementForms!=null&&data.managementForms!=undefined) {
		$("#managementForms").val(data.managementForms);
	}
	if (data.legalRepresentative!=null&&data.legalRepresentative!=undefined) {
		$("#legalRepresentative").val(data.legalRepresentative);
	}

	if (data.bedNumber!=null&&data.bedNumber!=undefined) {
		$("#bedNumber").val(data.bedNumber);
	}
	if (data.hospitalDepartment!=null&&data.hospitalDepartment!=undefined) {
		$("#hospitalDepartment").val(data.hospitalDepartment);
	}





}

function oneDataManagementShowOrhide(data) {
	if (data) {
		$("#oneDataManagement").show();
		$("#managementForms").val('');
		$("#legalRepresentative").val('');
		$("#bedNumber").val('');
		$("#hospitalDepartment").val('');
	} else {
		$("#oneDataManagement").hide();
		$("#managementForms").val('');
		$("#legalRepresentative").val('');
		$("#bedNumber").val('');
		$("#hospitalDepartment").val('');
	}
}

function dealerDataShow(data) {
	vm.changeClearAll()
	if (data) {
		$("#dealerData").show();
	} else {
		$("#dealerData").hide();
	}
}
function workAreaShow(data) {
	if (data) {
		$("li[alt='workAreaLi']").show();
	} else {
		debugger
		$("li[alt='workAreaLi']").hide();
	}
}

$('#bedNumber').on('input', function() {
	var inputValue = $(this).val();
	var numericValue = inputValue.replace(/\D/g, ''); // 保留数字

	if (numericValue > 10000) {
		numericValue = 10000; // 限制数字不超过10000
		layer.alert('床位数超过限制');
	}

	$(this).val(numericValue);
});

$('#legalRepresentative').on('input', function() {
	var legalRepresentative = $(this).val();

	if (legalRepresentative.length > 15) {
		layer.alert('法人代表不允许超过15个字');
		legalRepresentative = '';
	}

	$(this).val(legalRepresentative);
});

function checkIsExtend(selectName, data) {
	let isValueInOptions = false;
	$('select[name="'+selectName+'"] option').each(function() {
		if ($(this).val() == data) {
			isValueInOptions = true;  // 如果找到了该值，就将标志设置为true
			return false;  // 并停止遍历
		}
	});
	return isValueInOptions;
}

// 打开查询终端的页面
function queryTerminal() {
	let data = $("#traderName").val();

	if (data.length < 4) {
		layer.alert('请至少输入4个字同步终端库');
		return;
	}

	layer.open({
		type: 2,
		content: '/traderCustomerTerminal/queryView.do?query='+data,
		area: ['450px', '450px'],
		title :'终端列表',
		btnAlign: 'c',
		btn: ['提交', '返回'],
		yes: function(index, layero){
			//按钮【按钮一】的回调
			var iframeWin = window[layero.find('iframe')[0]['name']];
			// 通过window对象访问iframe内的JavaScript对象
			if (iframeWin.vm.radio == '') {
				layer.alert('请选择终端');
				return
			}

			let iframe = parentWindow[0].querySelector('.tab-pane.active').getElementsByTagName('iframe')[0];
			// console.log(iframe)
			let contentWindow = iframe.contentWindow;
			// console.log(contentWindow)
			if (typeof contentWindow.callbackTerminal==='function') {
				contentWindow.callbackTerminal(
					{
						bedNumber: iframeWin.vm.radio.bedNumber,
						hosName: iframeWin.vm.radio.hosName,
						hospitalDepartment: iframeWin.vm.radio.hospitalDepartment,
						institutionLevel: iframeWin.vm.radio.institutionLevel,
						institutionNature: iframeWin.vm.radio.institutionNature,
						institutionType: iframeWin.vm.radio.institutionType,
						institutionTypeChild: iframeWin.vm.radio.institutionTypeChild,
						legalRepresentative: iframeWin.vm.radio.legalRepresentative,
						managementForms: iframeWin.vm.radio.managementForms,
						traderCustomerMarketingType: iframeWin.vm.radio.traderCustomerMarketingType
					})
			}


			layer.close(index);
		}
		,btn2: function(index, layero){
			layer.close(index);
		}

	});


}

//客户分类更改
function changeCate(obj){
	checkLogin();
	var id = $(obj).val();

	$(obj).nextAll("select").remove();
	$(".querybtn").remove();
	if(id > 0){
		if (id != 6&&id!=5) {
			oneDataManagementShowOrhide(false)
			$.ajax({
				type : "POST",
				url : page_url+"/trader/customer/gettradercustomercategory.do",
				data :{'parentId':id},
				dataType : 'json',
				success : function(data) {
					if(data.code == 0){
						if(data.listData.length > 0){
							var name = "customerCategory"+id
							$option = " <select class='input-middle mr6' name='"+name+"' onchange='changeCate(this);'><option value='0'>请选择</option>";
							$.each(data.listData,function(i,n){
								$option += "<option value='"+data.listData[i]['traderCustomerCategoryId']+"'>"+data.listData[i]['customerCategoryName']+"</option>";
							});
							$option += "</select>";
							$(obj).after($option);
						}
					}
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
		}
		if (id == 5) {
			dealerDataShow(true)
		}else {
			dealerDataShow(false)
		}

		if (id == 6) {
			$.ajax({
				type : "POST",
				url : page_url+"/traderCustomerMarketingNodeApi/query.do",
				data :{'type':id,'isSort':false},
				dataType : 'json',
				async:false,
				success : function(data) {
					if(data.code == 0){
							var name = "customerCategory"+id
							$option = " <select class='input-middle mr6' name='"+name+"' onchange='changeNew(this);'><option value=''>请选择</option>";
							$.each(data.data,function(i,n){
								$option += "<option value='"+data.data[i].threeCustomerType.label+"'>"+data.data[i].threeCustomerType.value+"</option>";
							});
							$option += "</select>";
							// 查询按钮
							$option+='<button type="button" class="sucsess-ok querybtn" onclick="queryTerminal()">终端库查询</button>'

							$(obj).after($option);
					}
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
		}

	}
	$("#traderCustomerCategoryId").val(id);
	changeAttribute();
	
	//是否是临床分销
	var traderCustomerCategoryIds = "";
	$.each($(obj).parent().find("select"),function(){
		if($(this).val() == 5){
			$("input[name='isLcfx']").val(1);
		}
		traderCustomerCategoryIds += $(this).val() + ',';
	});
	$("#traderCustomerCategoryIds").val(traderCustomerCategoryIds);
	
	$("input[name='traderCustomer.wholesale']").val("");
	$("input[name='traderCustomer.directSelling']").val("");

	if ($('select[name="customerCategory2"]').length > 0) {
		workAreaShow(true)
	} else {
		workAreaShow(false)
		$('#work_province').prop('selectedIndex', 0).trigger('change');
	}
}
// 新数据三级 终端的
function changeNew(obj) {

	let id = $(obj).val();

	$(".customerAttribute").remove();
	
	$.ajax({
		type : "POST",
		url : page_url+"/traderCustomerMarketingNodeApi/query.do",
		data :{'type':6,'isSort':false},
		dataType : 'json',
		async:false,
		success : function(data) {
			
			if(data.code == 0){

				$.each(data.data,function(i,n){

					if (data.data[i].threeCustomerType.label != id) {
						return true;
					}

					let institutionLevel = data.data[i].institutionLevel;
					var li = '';

					li+='<li class="customerAttribute ">' +
						'<div class="infor_name mt0"><lable>机构性质</lable></div>' +
						'<div class="f_left">' +
						'<select class="input-middle" name="traderCustomer.traderCustomerMarketingDto.institutionNature">' +
						'<option value="">请选择</option>' +
						'<option value="0">公立</option>' +
						'<option value="1">非公</option>' +
						'</select></div></li>'
					let institutionType = data.data[i].institutionType;
					if (institutionLevel!=null&&institutionLevel.length>0) {
						 li += '<li class="customerAttribute '+'"><div class="infor_name mt0">'+'<lable>机构评级</lable></div>';

						li += '<div class="f_left">';

						li += '<select class="input-middle" name="traderCustomer.traderCustomerMarketingDto.institutionLevel"><option value="">请选择</option>';

						$.each(institutionLevel,function (k,n){
							li +="<option value='"+institutionLevel[k].label+"'>"+institutionLevel[k].value+"</option>";
						});

						li += '</select></div>';

						li += '</div>';
						li += '</li>';

					}

					if (institutionType!=null&&institutionType.length>0) {
						 li += '<li class="customerAttribute '+'"><div class="infor_name mt0">'+'<lable>机构类型</lable></div>';

						li += '<div class="f_left">';

						li += '<select class="input-middle" name="traderCustomer.traderCustomerMarketingDto.institutionType" onchange="changeTypeNew(this)"><option value="">请选择</option>';

						$.each(institutionType,function (k,n){
							li +="<option value='"+institutionType[k].label+"'>"+institutionType[k].value+"</option>";

						});

						li += '</select></div>';

						li += '</div>';
						li += '</li>';

					}

					$("#attr_brfore_li").after(li);

				});
			}
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});

	var traderCustomerCategoryIds = "";
	$.each($(obj).parent().find("select"),function(){
		if($(this).val() == 5){
			$("input[name='isLcfx']").val(1);
		}
		traderCustomerCategoryIds += $(this).val() + ',';
	});
	$("#traderCustomerCategoryIds").val(traderCustomerCategoryIds);

}

// 机构类型的子集
function changeTypeNew(obj) {

	let id = $(obj).val();

	let pid = $('select[name="customerCategory6"]').val();
	$(".institutionTypeChilds").remove();
	if (id!=''&& id!=undefined && id >= 0) {
		$.ajax({
			type : "POST",
			url : page_url+"/traderCustomerMarketingNodeApi/getInstitutionTypeChild.do",
			data :{'threeCustomerType':pid,'institutionType':id},
			dataType : 'json',
			success : function(data) {
				if(data.code == 0){

					if (data.data != null && data.data.length > 0) {
						var name = "traderCustomer.traderCustomerMarketingDto.institutionTypeChild";
						$option = " <select class='input-middle mr6 institutionTypeChilds' name='"
							+name+ "' ><option value='-1'>请选择</option>";
						$.each(data.data,function(i,n){
							$option += "<option value='"+data.data[i].label+"'>"+data.data[i].value+"</option>";
						});
						$option += "</select>";
						$(obj).after($option);
					}

				}
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	}


}
//更改属性
function changeAttribute(){
	checkLogin();
	$(".customerAttribute").remove();
	
	var show_fenxiao = false;
	var show_dealer5 = false;
	$.each($("#customer_category_div > select"),function(i,n){
		var id = $(this).val();
		if(id > 0){
			show_fenxiao = id==3;
			show_dealer5 = id==5;
			let change_show =  id==6;
			if (!show_dealer5) {
			$.ajax({
				type : "POST",
				url : page_url+"/trader/customer/gettradercustomerattribute.do",
				data :{'traderCustomerCategoryId':id},
				dataType : 'json',
				success : function(data) {
					if(data.code == 0){
						if(data.listData.length > 0){
							$.each(data.listData,function(i,n){
								var c = "";
								if(data.listData[i]['isSelected'] == 1){
									c = "bt";
								}
								var li = '<li class="customerAttribute '+c+'"><div class="infor_name mt0">';
								if(data.listData[i]['isSelected'] == 1){
									li += '<span>*</span>';
								}
								li += '<lable>'+data.listData[i]['categoryName']+'</lable>';
								li += '</div>';
								//console.log(data.listData[i]['inputType']);
								
								if(data.listData[i]['inputType'] == 0){//单选
									li += '<div class="f_left inputfloat inputradio"><ul>';
								}
								if(data.listData[i]['inputType'] == 1){//复选
									li += '<div class="f_left inputradio"><ul>';
								}
								if(data.listData[i]['inputType'] == 2){//下拉
									li += '<div class="f_left">';

									if(data.listData[i]['scope'] == 1013 ){ //所有制
										if (change_show) {
											return true;
										}
										li += '<select class="input-middle" name="traderCustomer.ownership"><option value="0">请选择</option>';

									}
									if(data.listData[i]['scope'] == 1014 ){ //医学类型
										if (change_show) {
											return true;
										}
										li += '<select class="input-middle" name="traderCustomer.medicalType"><option value="0">请选择</option>';
									}
									if(data.listData[i]['scope'] == 1015 ){ //医院等级
										if (change_show) {
											return true;
										}
										li += '<select class="input-middle" name="traderCustomer.hospitalLevel"><option value="0">请选择</option>';
									}

								}
								
								$.each(data.listData[i]['sysOptionDefinitions'],function(j,m){
									if(data.listData[i]['inputType'] == 0){//单选
										li += '<li>';
										li += '<input type="checkbox" onclick="attrCheck(this);" name="attributeId" value="'+data.listData[i]['attributeCategoryId']+'_'+data.listData[i]['sysOptionDefinitions'][j]['sysOptionDefinitionId']+'" /> <label>'+data.listData[i]['sysOptionDefinitions'][j]['title']+'</label>';
										if((data.listData[i]['scope'] == 1027 && data.listData[i]['sysOptionDefinitions'][j]['sysOptionDefinitionId'] == 109 ) 
												|| (data.listData[i]['scope'] == 1028 && data.listData[i]['sysOptionDefinitions'][j]['sysOptionDefinitionId'] == 115)
												|| (data.listData[i]['scope'] == 1032 && data.listData[i]['sysOptionDefinitions'][j]['sysOptionDefinitionId'] == 302)){
											li += '<input type="text" class="input-small mt0 heit18" name="attributedesc_'+data.listData[i]['attributeCategoryId']+'_'+data.listData[i]['sysOptionDefinitions'][j]['sysOptionDefinitionId']+'"/>';
										}
										
										li += '</li>';
									}
									
									if(data.listData[i]['inputType'] == 1){//复选
										var onclick = '';
										if(data.listData[i]['attributeCategoryId'] == 5 && data.listData[i]['sysOptionDefinitions'][j]['sysOptionDefinitionId'] == 93){
											onclick += 'onclick="getChildAttr(this,5)"';
										}
										
										li += '<li>';
										li += '<input type="checkbox" name="attributeId" value="'+data.listData[i]['attributeCategoryId']+'_'+data.listData[i]['sysOptionDefinitions'][j]['sysOptionDefinitionId']+'" '+onclick+' /> <label>'+data.listData[i]['sysOptionDefinitions'][j]['title']+'</label>';
										if((data.listData[i]['scope'] == 1027 && data.listData[i]['sysOptionDefinitions'][j]['sysOptionDefinitionId'] == 109 ) 
												|| (data.listData[i]['scope'] == 1028 && data.listData[i]['sysOptionDefinitions'][j]['sysOptionDefinitionId'] == 115)
												|| (data.listData[i]['scope'] == 1032 && data.listData[i]['sysOptionDefinitions'][j]['sysOptionDefinitionId'] == 302)){
											li += '<input type="text" class="input-small mt0 heit18" name="attributedesc_'+data.listData[i]['attributeCategoryId']+'_'+data.listData[i]['sysOptionDefinitions'][j]['sysOptionDefinitionId']+'"/>';
										}
										
										li += '</li>';
									}
									if(data.listData[i]['inputType'] == 2){//下拉
										if(data.listData[i]['scope'] == 1013
												|| data.listData[i]['scope'] == 1014
												|| data.listData[i]['scope'] == 1015
												){
											li += '<option value="'+data.listData[i]['sysOptionDefinitions'][j]['sysOptionDefinitionId']+'">'+data.listData[i]['sysOptionDefinitions'][j]['title']+'</option>';
										}else{
											li += '<option value="'+data.listData[i]['attributeCategoryId']+'_'+data.listData[i]['sysOptionDefinitions'][j]['sysOptionDefinitionId']+'">'+data.listData[i]['sysOptionDefinitions'][j]['title']+'</option>';
										}
									}
									
									
								});
								if(data.listData[i]['inputType'] == 0){//单选
									li += '</ul></div>';
								}
								if(data.listData[i]['inputType'] == 1){//复选
									li += '</ul></div>';
								}
								if(data.listData[i]['inputType'] == 2){//下拉
									li += '</selected></div>';
								}
								li += '</li>';
								
								$("#attr_brfore_li").after(li);
							});
							//其它信息录入控制
							$("input[name^='attributedesc']").bind("change",function(){
								if($(this).val() != ''){
									$(this).parent().find("input[name='attributeId']").attr('checked','checked');
								}
							});
						}
						if ($('select[name="customerCategory2"]').length > 0) {
							workAreaShow(true)
						} else {
							workAreaShow(false)
							$('#work_province').prop('selectedIndex', 0).trigger('change');
						}
					}
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
			}

		}
	});

	if(show_fenxiao){
		if (show_dealer5) {
			$(".dealer5").show();
			$(".dealerNotShow").hide();
			$('select[name="traderCustomer.employees"]').val('0')
			$('select[name="traderCustomer.annualSales"]').val('0')
		}else {
			$(".dealer5").hide();
			$("#bussinessArea_show").html("");
			$(".dealerNotShow").show();
			$('select[name="traderCustomer.employees"]').val('0')
			$('select[name="traderCustomer.annualSales"]').val('0')
		}
		$("li[alt='fenxiao']").show();
	}else{
		$("li[alt='fenxiao']").hide();
		$("#bussinessArea_show").html("");
		$("#bussinessBrand_show").html("");
		$("#agentBrand_show").html("");
		$("input[name='directSelling']").val('');
		$("input[name='wholesale']").val('');
		$.each($("input[name='salesModel']"),function(){
			if($(this).prop('checked')){
				$(this).prop('checked',false);
			}
		});
		if (show_dealer5) {
			$(".dealer5").show();
			$(".dealerNotShow").hide();
			$('select[name="traderCustomer.employees"]').val('0')
			$('select[name="traderCustomer.annualSales"]').val('0')
		}else {
			$(".dealer5").hide();
			$(".dealerNotShow").show();
			$("#bussinessArea_show").html("");
			$('select[name="traderCustomer.employees"]').val('0')
			$('select[name="traderCustomer.annualSales"]').val('0')
		}
	}


}

//获取子集属性 id=parentId
function getChildAttr(obj,id){
	checkLogin();
	if($(obj).prop("checked")){
		//选择属性
		if(id > 0){
			$.ajax({
				type : "POST",
				url : page_url+"/trader/customer/gettradercustomerchildattribute.do",
				data :{'attributeCategoryId':id},
				dataType : 'json',
				success : function(data) {
					if(data.code == 0){
						if(data.listData.length > 0){
							$.each(data.listData,function(i,n){
								var c = "";
								if(data.listData[i]['isSelected'] == 1){
									c = "bt";
								}
								var li = '<li class="customerAttribute '+c+'" alt="'+id+'"><div class="infor_name mt0">';
								if(data.listData[i]['isSelected'] == 1){
									li += '<span>*</span>';
								}
								li += '<lable>'+data.listData[i]['categoryName']+'</lable>';
								li += '</div><div class="f_left inputradio"><ul>';
								$.each(data.listData[i]['sysOptionDefinitions'],function(j,m){
									li += '<li><input type="checkbox" name="attributeId" value="'+data.listData[i]['attributeCategoryId']+'_'+data.listData[i]['sysOptionDefinitions'][j]['sysOptionDefinitionId']+'" '+onclick+' /> <label>'+data.listData[i]['sysOptionDefinitions'][j]['title']+'</label>';
									li += '</li>';
								});
								li += '</ul></div></li>';
								
								$(obj).parent().parent().parent().parent().after(li);
							});
						}
					}
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
		}
	}else{
		//取消属性
		$("li[alt="+id+"]").remove();
	}
}

//添加经营地区TAG
function addBussinessArea(){
	checkLogin();
	var province = $("select[name='bussiness_province']").val();
	var city = $("select[name='bussiness_city']").val();
	var zone = $("select[name='bussiness_zone']").val();
	if(province == 0 && city == 0 && zone == 0){
		return false;
	}
	
	var provinceText = $('select[name="bussiness_province"]').find(':selected').text();
	var cityText = $('select[name="bussiness_city"]').find(':selected').text();
	var zoneText = $('select[name="bussiness_zone"]').find(':selected').text();
	
	var area_id = '';
	var area_ids = '';
	var area_str = '';
	
	if(zone > 0){
		area_id = zone;
		area_ids = province+","+city+","+zone;
    	area_str = provinceText+" "+cityText +" "+zoneText;
	}else if(city > 0){
		area_id = city;
		area_ids = province+","+city;
    	area_str = provinceText+" "+cityText ;
	}else if(province > 0){
		area_id = province;
		area_ids = province;
    	area_str = provinceText;
	}
	
	var ok = true;
	$.each($("input[name='bussinessAreaId']"),function(i,n){
		if($(this).val() == area_id){
			ok = false;
		}
	});
	
	if(ok){
		var bussinsessLi = "<li class='bluetag'>"+area_str
		+"<input type='hidden' name='bussinessAreaId' value='"+area_id+"'><input type='hidden' name='bussinessAreaIds' value='"+area_ids+"'><i class='el-icon-close' onclick='delTag(this);'></i></li>";
		
		$("#bussinessArea_show").append(bussinsessLi);
		
		initBussinessRegion();
		$("#bussinessArea_show").parent(".addtags").show();
	}else{
		layer.tips("选择的经营地区已经存在","#addBussinessArea",{time:1000});
	}
}

//经营地区
function initBussinessRegion(){
	checkLogin();
	$.ajax({
		type : "POST",
		url : page_url+"/system/region/getregion.do",
		data :{'regionId':1},
		dataType : 'json',
		success : function(data) {
			$option = "<option value='0'>请选择</option>";
			$.each(data.listData,function(i,n){
				$option += "<option value='"+data.listData[i]['regionId']+"'>"+data.listData[i]['regionName']+"</option>";
			});
			$("select[name='bussiness_city'] option:gt(0)").remove();
			$("select[name='bussiness_zone'] option:gt(0)").remove();
			$("select[name='bussiness_province']").html($option);
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});
}

//品牌列表
function initBrand(){
	checkLogin();
	$.ajax({
		type : "POST",
		url : page_url+"/goods/brand/getallbrand.do",
		dataType : 'json',
		success : function(data) {
			$option = "<option value='0'>请选择</option>";
			$.each(data.listData,function(i,n){
				$option += "<option value='"+data.listData[i]['brandId']+"'>"+data.listData[i]['brandName']+"</option>";
			});
			$("select[name='bussinessBrands']").html($option);
			$("select[name='agentBrands']").html($option);
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});
}

//搜索经营品牌
function searchBussinessBrand(){
	checkLogin();
	var brand = $("input[name='bussinessBrandKey']").val();
	$.ajax({
		type : "POST",
		url : page_url+"/goods/brand/getallbrand.do",
		data : {brandName:brand},
		dataType : 'json',
		success : function(data) {
			$option = "<option value='0'>请选择</option>";
			$.each(data.listData,function(i,n){
				var selected = "";
				if(brand == data.listData[i]['brandName']){
					selected = "selected";
				}
				$option += "<option value='"+data.listData[i]['brandId']+"' "+selected+">"+data.listData[i]['brandName']+"</option>";
			});
			$("select[name='bussinessBrands']").html($option);
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});
}
//搜索代理品牌
function searchAgentBrand(){
	checkLogin();
	var brand = $("input[name='agentBrandKey']").val();
	$.ajax({
		type : "POST",
		url : page_url+"/goods/brand/getallbrand.do",
		data : {brandName:brand},
		dataType : 'json',
		success : function(data) {
			$option = "<option value='0'>请选择</option>";
			$.each(data.listData,function(i,n){
				var selected = "";
				if(brand == data.listData[i]['brandName']){
					selected = "selected";
				}
				$option += "<option value='"+data.listData[i]['brandId']+"' "+selected+">"+data.listData[i]['brandName']+"</option>";
			});
			$("select[name='agentBrands']").html($option);
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});
}
//添加经营品牌
function addBussinessBrand(){
	checkLogin();
	var brandId = $("select[name='bussinessBrands']").val();
	if(brandId == 0){
		return false;
	}
	
	brandName = $('select[name="bussinessBrands"]').find(':selected').text();
	
	var ok = true;
	$.each($("input[name='bussinessBrandId']"),function(i,n){
		if($(this).val() == brandId){
			ok = false;
		}
	});
	
	if(ok){
		var bussinsessLi = "<li class='bluetag'>"+brandName
		+"<input type='hidden' name='bussinessBrandId' value='"+brandId+"'><i class='iconbluecha' onclick='delTag(this);'></i></li>";
		
		$("#bussinessBrand_show").append(bussinsessLi);
		$('select[name="bussinessBrands"]').val(0);
		
		$("#bussinessBrand_show").parent(".addtags").show();
	}else{
		layer.tips("选择的品牌已经存在","#addBussinessBrand",{time:1000});
	}
}

//添加代理品牌
function addAgentBrand(){
	checkLogin();
	var brandId = $("select[name='agentBrands']").val();
	if(brandId == 0){
		return false;
	}
	
	brandName = $('select[name="agentBrands"]').find(':selected').text();
	
	var ok = true;
	$.each($("input[name='agentBrandId']"),function(i,n){
		if($(this).val() == brandId){
			ok = false;
		}
	});
	if(ok){
		var agentLi = "<li class='bluetag'>"+brandName
		+"<input type='hidden' name='agentBrandId' value='"+brandId+"'><i class='iconbluecha' onclick='delTag(this);'></i></li>";
		$("#agentBrand_show").append(agentLi);
		$('select[name="agentBrands"]').val(0);
		
		$("#agentBrand_show").parent(".addtags").show();
	}else{
		layer.tips("选择的品牌已经存在","#addAgentBrand",{time:1000});
	}
}

//删除标签
function delTag(obj){
	checkLogin();
	var div = $(obj).parent().parent().parent();
	$(obj).parent().remove();
	if($(div).find("li").length == 0)
	{
		$(div).hide();
	}
}
//天眼查参数
function upperCase(){
	var traderName = $("#traderName").val();
	//var traderNameReg = /^[a-zA-Z0-9\u4e00-\u9fa5\.\(\)\,\（\）\s]{2,128}$/;
	if(traderName == ""){
		warnTips("eye","客户名称不允许为空");
		return false;
	}
	if(traderName.length < 4 ){
		warnTips("eye","请至少输入4个字符再点击接口查询");
		return false;
	}
	// if(!traderNameReg.test(traderName)){
	// 	warnTips("eye","客户名称不允许使用特殊字符");
	// 	return false;
	// }
    layer.config({
        extend: 'vedeng.com/style.css', 
        skin: 'vedeng.com'
    });
    var layerParams = $("#eye").attr('layerParams');
    if (typeof(layerParams) == 'undefined') {
        alert('参数错误');
    } else {
        layerParams = $.parseJSON(layerParams);
    }
    var link = layerParams.link;
    if (link.indexOf("?") > 0 && (link.indexOf("?") + 1 == link.length)) {
        link += "pop=pop";
    } else if (link.indexOf("?") < 0) {
        link += "?pop=pop";
    } else if (link.indexOf("?") > 0 && (link.indexOf("?") + 1 != link.length)) {
        link += "&pop=pop";
    }
    var index = layer.open({
        type: 2,
        shadeClose: false, 
        area: [layerParams.width, layerParams.height],
        title: layerParams.title,
        content: encodeURI(link+"&traderName="+traderName),
        success: function(layero, index) {
        }
    });
	return  false;
}
