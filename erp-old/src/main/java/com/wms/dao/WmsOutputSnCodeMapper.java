package com.wms.dao;


import com.wms.model.WmsOutSnCodeOrder;
import com.wms.model.generate.VWmsOutSncodeOrder;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2020/11/179:56
 */
public interface WmsOutputSnCodeMapper{

    int deleteByPrimaryKey(Integer outSncodeOrderId);

    int insert(VWmsOutSncodeOrder record);

    int insertSelective(VWmsOutSncodeOrder record);

    VWmsOutSncodeOrder selectByPrimaryKey(Integer outSncodeOrderId);

    int updateByPrimaryKeySelective(VWmsOutSncodeOrder record);

    int updateByPrimaryKey(VWmsOutSncodeOrder record);

    /***
     * @description: 获取出库SN码信息
     * @param snCodeOrder
     * @return {@link WmsOutSnCodeOrder}
     * @throws
     * <AUTHOR>
     * @date 2020/12/1 13:20
     */
    WmsOutSnCodeOrder getOutputSnCode(WmsOutSnCodeOrder snCodeOrder);

    /**
     * @description: 根据SKU查询SnOrder
     * @param skuList
     * @return {@link List<WmsOutSnCodeOrder>}
     * @throws
     * <AUTHOR>
     * @date 2020/11/19 17:41
     */
    List<WmsOutSnCodeOrder> getSnCodeOrderByTime(@Param("skuList") List<String> skuList, @Param("startTime") Timestamp startTime, @Param("endTime")Timestamp endTime);


    int getSnNum(@Param("operateType") Integer operateType, @Param("orderNo") String orderNo, @Param("sku") Integer sku);


}
