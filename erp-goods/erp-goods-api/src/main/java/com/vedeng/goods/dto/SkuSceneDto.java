package com.vedeng.goods.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.vedeng.common.core.base.BaseDto;
import com.vedeng.common.core.utils.validator.group.AddGroup;
import com.vedeng.common.core.utils.validator.group.UpdateGroup;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.Date;
import java.util.List;

/**
 * 场景方案表
 */
@Getter
@Setter
public class SkuSceneDto extends BaseDto {
    /**
     * 主键
     */
    private Long id;

    /**
     * 场景编号（唯一）
     */
    private String sceneNo;

    /**
     * 场景名称
     */
    @NotEmpty(message = "方案名称不能为空", groups = {AddGroup.class, UpdateGroup.class})
    private String name;

    /**
     * 场景描述
     */
    private String description;

    /**
     * 状态（0=上架，1=下架）
     */
    private Integer status;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 异常数
     */
    private Integer errorCount;

    /**
     * 产品总数
     */
    private Integer productCount;

    /**
     * 场景分类
     */
    @NotEmpty(message = "场景不能为空", groups = {AddGroup.class, UpdateGroup.class})
    private List<SkuSceneCategoryDto> skuSceneCategoryDtoList;

    /**
     * 创建开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createStartTime;
    /**
     * 创建结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createEndTime;

    /**
     * 修改开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyStartTime;

    /**
     * 修改结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyEndTime;

    /**
     * 创建人集合
     */
    private List<Integer> createUserList;

    /**
     * 修改人集合
     */
    private List<Integer> modifyUserList;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Override
    public Date getAddTime() {
        return super.getAddTime();
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Override
    public Date getModTime() {
        return super.getModTime();
    }
}