package com.vedeng.erp.system.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * 人员与产线区域配置VO
 */
@Data
public class RoleUserRegionConfigVO {
    /**
     * 主键
     */
    @ExcelIgnore
    private Long id;

    /**
     * 线上销售用户ID
     */
    @ExcelIgnore
    private Integer onlineSalesId;

    /**
     * 线上销售用户名称
     */
    @ExcelProperty("线上销售")
    private String onlineSalesName;

    /**
     * 线上销售部门列表
     * 通过接口获取，不存储在数据库
     */
    @ExcelIgnore
    private List<Department> onlineSalesDepartments;

    /**
     * 线上销售在职状态（1-离职，0-在职）
     * 通过接口获取，不存储在数据库
     */
    @ExcelIgnore
    private Integer onlineSalesStatus;

    /**
     * 线上销售在职状态文本
     */
    @ExcelIgnore
    private String onlineSalesStatusText;

    /**
     * 线上销售用户头像
     */
    @ExcelIgnore
    private String onlineSalesAvatar;

    /**
     * 线下销售用户ID
     */
    @ExcelIgnore
    private Integer offlineSalesId;

    /**
     * 线下销售用户名称
     */
    @ExcelProperty("线下销售")
    private String offlineSalesName;

    /**
     * 线下销售部门列表
     */
    @ExcelIgnore
    private List<Department> offlineDepartments;

    /**
     * 线下销售在职状态 （1-离职，0-在职）
     * 通过接口获取，不存储在数据库
     */
    @ExcelIgnore
    private Integer offlineSalesStatus;

    /**
     * 线下销售在职状态文本
     */
    @ExcelIgnore
    private String offlineSalesStatusText;

    /**
     * 线下销售用户头像
     */
    @ExcelIgnore
    private String offlineSalesAvatar;

    /**
     * 产线人员用户ID
     */
    @ExcelIgnore
    private Integer productionUserId;

    /**
     * 产线人员用户名称
     */
    @ExcelProperty("销售产线人员")
    private String productionUserName;

    /**
     * 产线人员在职状态（1-离职，0-在职）
     * 通过接口获取，不存储在数据库
     */
    @ExcelIgnore
    private Integer productionUserStatus;

    /**
     * 产线人员在职状态文本
     */
    @ExcelIgnore
    private String productionUserStatusText;

    /**
     * 线上销售用户头像
     */
    @ExcelIgnore
    private String productionUserAvatar;

    /**
     * 省份
     */
    @ExcelIgnore
    private String province;

    /**
     * 省份ID
     */
    @ExcelIgnore
    private Integer provinceId;

    /**
     * 城市
     */
    @ExcelIgnore
    private String city;

    /**
     * 城市ID
     */
    @ExcelIgnore
    private Integer cityId;

    /**
     * 参考省（用于导出显示）
     */
    @ExcelProperty("参考省")
    private String provinceDisplay;

    /**
     * 参考市（用于导出显示）
     */
    @ExcelProperty("参考市")
    private String cityDisplay;

    /**
     * 完整省市名称
     */
    @ExcelIgnore
    private String fullRegionName;

    /**
     * 用户ID（用于业务人员列表）
     */
    @ExcelIgnore
    private Integer userId;

    /**
     * 用户名称（用于业务人员列表）
     */
    @ExcelIgnore
    private String userName;

    /**
     * 部门内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Department {
        @ExcelIgnore
        private Integer departmentId;
        @ExcelIgnore
        private String departmentName;
    }

}