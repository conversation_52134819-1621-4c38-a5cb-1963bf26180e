package com.vedeng.erp.kingdee.service;

import com.vedeng.erp.kingdee.dto.KingDeePayCommonDto;
import com.vedeng.erp.kingdee.dto.result.KingDeePayCommonQueryResultDto;
import com.vedeng.infrastructure.kingdee.service.KingDeeMqBaseService;

import java.util.List;

/**
 * <AUTHOR>
 * @description 其他入库单
 * @date 2023/3/6 8:59
 **/

public interface KingDeePayCommonApiService extends KingDeeMqBaseService<KingDeePayCommonDto> {

    /**
     * 根据采购票的id 查询应付的那
     * @param businessId 票id 业务id 兼容虚拟票
     * @return List<KingDeePayCommonQueryResultDto>
     */
    List<KingDeePayCommonQueryResultDto> getKingDeePayCommon(String businessId);
}
