package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.kingdee.batch.dto.BatchRInvoiceDetailJOperateLogDto;
import com.vedeng.erp.kingdee.batch.dto.BatchRVirtualInvoiceJWarehouseDto;
import com.vedeng.erp.kingdee.batch.dto.BatchVirtualInvoiceDto;
import com.vedeng.erp.kingdee.batch.dto.BatchVirtualInvoiceItemDto;
import com.vedeng.erp.kingdee.batch.handle.BuyOrderVirtualInvoiceHandle;
import com.vedeng.erp.kingdee.batch.repository.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 返利绑定货票 处理类
 * @date 2022/11/18 13:55
 */

@Service
@Slf4j
public class VirtualInvoiceBindProcessor implements ItemProcessor<BatchVirtualInvoiceDto, List<BatchRVirtualInvoiceJWarehouseDto>> {



    @Autowired
    private BatchVirtualInvoiceItemDtoMapper batchVirtualInvoiceItemDtoMapper;

    @Autowired
    private BatchRInvoiceDetailJOperateLogDtoMapper batchRInvoiceDetailJOperateLogDtoMapper;

    @Autowired
    private BuyOrderVirtualInvoiceHandle buyOrderVirtualInvoiceHandle;



    @Override
    public List<BatchRVirtualInvoiceJWarehouseDto> process(BatchVirtualInvoiceDto batchVirtualInvoiceDto) throws Exception {

        log.info("VirtualInvoiceBlueBindProcessor.process：{}", JSON.toJSONString(batchVirtualInvoiceDto));

        List<BatchVirtualInvoiceItemDto> virtualInvoiceItemDtos = batchVirtualInvoiceItemDtoMapper.selectBindableInvoiceItem(batchVirtualInvoiceDto.getVirtualInvoiceId());

        if (CollUtil.isEmpty(virtualInvoiceItemDtos)) {
            log.info("虚拟票无还需绑定明细：{}",JSON.toJSONString(batchVirtualInvoiceDto));
            return null;
        }

        Integer outInType = batchVirtualInvoiceDto.getColorType().equals(ErpConstant.TWO) ? ErpConstant.ONE : ErpConstant.SIX;

        List<BatchRVirtualInvoiceJWarehouseDto> result = virtualInvoiceItemDtos.stream().map(x -> {
            // 1.查询出所有的出库记录
            List<BatchRInvoiceDetailJOperateLogDto> validWarehousingLogByRelatedIds = batchRInvoiceDetailJOperateLogDtoMapper.getValidWarehousingLogByRelatedIds(Collections.singletonList(x.getBusinessOrderItemId()), outInType);

            validWarehousingLogByRelatedIds = buyOrderVirtualInvoiceHandle.checkAndProcessVirtualOperateLog(validWarehousingLogByRelatedIds);

            if (CollUtil.isNotEmpty(validWarehousingLogByRelatedIds)) {
                // 2.绑定
                return buyOrderVirtualInvoiceHandle.bindInvoice(validWarehousingLogByRelatedIds, x, outInType);
            }
            return new LinkedList<BatchRVirtualInvoiceJWarehouseDto>();

        }).filter(CollUtil::isNotEmpty).flatMap(List::stream).collect(Collectors.toList());

        if (CollUtil.isEmpty(result)) {
            return null;
        }

        return result;
    }



}
