package com.test.kingdee;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.domain.command.KingDeeReceiveCommonCommand;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeReceiveCommonMapper;
import com.vedeng.erp.kingdee.mapstruct.KingDeeReceiveCommonCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeReceiveCommonConvertor;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 应付单：标准应付单
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring.xml", "classpath:spring-mybatis.xml"})
public class TestKingDeeReceiveCommon {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private KingDeeReceiveCommonCommandConvertor commandConvertor;

    @Autowired
    private KingDeeReceiveCommonConvertor kingDeeReceiveCommonConvertor;

    @Autowired
    private KingDeeReceiveCommonMapper kingDeeReceiveCommonMapper;


    @Test
    public void kingDeePayCommon() {
        //一级dto
        KingDeeReceiveCommonDto kingDeeReceiveCommonDto = new KingDeeReceiveCommonDto();
        kingDeeReceiveCommonDto.setFId("0");
        kingDeeReceiveCommonDto.setFBillTypeId("YSD01_SYS");
        kingDeeReceiveCommonDto.setFDate("2022-09-17");
        kingDeeReceiveCommonDto.setFQzokBddjtId("贝登erp对应的单据头ID");
        kingDeeReceiveCommonDto.setFCustomerId("KH0001");
        kingDeeReceiveCommonDto.setFBusinessType("BZ");
        kingDeeReceiveCommonDto.setFSettleOrgId("101");
        kingDeeReceiveCommonDto.setFPayOrgId("101");
        kingDeeReceiveCommonDto.setFSetAccountType("1");

        List<KingDeeReceiveCommonDetailLinkDto> detailLinkList = new ArrayList<>();
        KingDeeReceiveCommonDetailLinkDto kingDeeReceiveCommonDetailLinkDto = new KingDeeReceiveCommonDetailLinkDto();
        kingDeeReceiveCommonDetailLinkDto.setFLinkId("0");
        kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFruleId("AR_OutStockToReceivableMap");
        kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFflowlineId("0");
        kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFstableId("0");
        kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFstableName("T_SAL_OUTSTOCKENTRY");
        kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFsbillId("104260");
        kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFsId("195014");
        kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFbasicunitqtyold("100");
        kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFbasicunitqty("40");
        kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFsalbaseqtyold("100");
        kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFsalbaseqty("40");
        kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFstockbaseqtyold("100");
        kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFstockbaseqty("40");
        kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFallamountforDold("11300");
        kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFallamountforD("4520");
        detailLinkList.add(kingDeeReceiveCommonDetailLinkDto);


        List<KingDeeReceiveCommonDetailDto> detailList = new ArrayList<>();
        KingDeeReceiveCommonDetailDto kingDeeReceiveCommonDetailDto = new KingDeeReceiveCommonDetailDto();
        kingDeeReceiveCommonDetailDto.setFMaterialId("sku0001");
        kingDeeReceiveCommonDetailDto.setFPriceQty(new BigDecimal(40.0));
        kingDeeReceiveCommonDetailDto.setFTaxPrice(new BigDecimal(113.0));
        kingDeeReceiveCommonDetailDto.setFEntryTaxRate(new BigDecimal(13.00));
        kingDeeReceiveCommonDetailDto.setFNoTaxAmountForD(new BigDecimal(4000.0));
        kingDeeReceiveCommonDetailDto.setFTaxAmountForD(new BigDecimal(520.0));
        kingDeeReceiveCommonDetailDto.setFAllAmountForD(new BigDecimal(4520.0));
        kingDeeReceiveCommonDetailDto.setFQzokBddjhId("贝登订单行ID");
        kingDeeReceiveCommonDetailDto.setFSourceType("SAL_OUTSTOCK");
        kingDeeReceiveCommonDetailDto.setFEntityDetail_Link(detailLinkList);
        detailList.add(kingDeeReceiveCommonDetailDto);
        kingDeeReceiveCommonDto.setFEntityDetail(detailList);


        KingDeeReceiveCommonCommand command = commandConvertor.toCommand(kingDeeReceiveCommonDto);

        System.out.println("入参："+JSON.toJSONString(command));
        kingDeeBaseApi.save(new SaveExtCommand<>(command, kingDeeReceiveCommonDto.getFormId()));
    }
}
