package com.vedeng.crm.feign.gpt;

import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.voice.api.response.ResponeInfo;
import com.vedeng.voice.domain.voicetask.common.chat.ChatCompletion;
import feign.Headers;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: ChatGPT服务接口
 * @date 2025/4/9 15:57
 */
@FeignApi(serverName = "gptServer")
public interface ChatGptApiService {

    @RequestLine("POST /api/chat/completions")
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    ResponeInfo completions(@RequestBody ChatCompletion chatCompletion);

}
