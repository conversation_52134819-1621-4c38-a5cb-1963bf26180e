package com.vedeng.infrastructure.bank.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.redis.redission.RedissonLockUtils;
import com.vedeng.infrastructure.bank.api.common.exception.AcceptanceBillException;
import com.vedeng.infrastructure.bank.api.common.service.B2eBankApiService;
import com.vedeng.infrastructure.bank.api.domain.*;
import com.vedeng.infrastructure.bank.api.domain.api.*;
import com.vedeng.infrastructure.bank.api.domain.base.ResponseHeader;
import com.vedeng.infrastructure.bank.api.domain.B2eSignRes;
import com.vedeng.infrastructure.bank.api.domain.dto.AcceptanceBillCreateDto;
import com.vedeng.infrastructure.bank.api.domain.entity.MinshengBankApiRecord;
import com.vedeng.infrastructure.bank.api.domain.entity.MinshengBankTransferRecord;
import com.vedeng.infrastructure.bank.api.enums.BankApiType;
import com.vedeng.infrastructure.bank.api.enums.BankStatusType;
import com.vedeng.infrastructure.bank.api.mapper.MinshengBankApiRecordMapper;
import com.vedeng.infrastructure.bank.api.mapper.MinshengBankTransferRecordMapper;
import com.vedeng.infrastructure.bank.api.service.AcceptanceBillApiService;
import com.vedeng.infrastructure.bank.api.util.BankApiXmlUtil;
import com.vedeng.infrastructure.bank.api.util.BankThreadPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.SAXReader;
import org.dom4j.io.XMLWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.StringReader;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @Author: Patric.Cheng
 * @CreateTime: 2024-08-14
 * @Description: 承兑汇票银行接口
 * @Version: 1.0
 */
@Slf4j
@Service
public class AcceptanceBillApiServiceImpl implements AcceptanceBillApiService {

    @Value("${bank.api.clientId:**********}")
    private String bankApiClientId;
    @Value("${bank.api.userId:**********888}")
    private String bankApiUserId;
    @Value("${bank.api.userPswd:1qqc49}")
    private String bankApiUserPswd;
    @Value("${bank.api.url:http://************:8080/eweb/b2e/connect.do}")
    private String bankApiUrl;

    //以下三个变量是收票方的账号和密码-许鱼鱼
    @Value("${bank.api.shouclientId:**********}")
    private String bankApiShouClientId;
    @Value("${bank.api.shouuserId:*************}")
    private String bankApiShouUserId;
    @Value("${bank.api.shouuserPswd:m058vr}")
    private String bankApiShouUserPswd;




    // 民生银行测试环境不支持线上签名，测试环境使用apollo配置进行验证
    /**
     * 是否本地加签
     */
    @Value("${bank.api.localSignature}")
    private Boolean LOCAL_SIGNATURE;

    /**
     * 加签内容
     */
    @Value("${bank.api.signature}")
    private String SIGNATURE;

    /**
     * 签约账号
     */
    @Value("${bank.api.issueAccountNo}")
    private String ISSUE_ACCOUNT_NO;
    /**
     * 保证金账号
     */
    @Value("${bank.api.bailAccountNo}")
    private String BAIL_ACCOUNT_NO;
    /**
     * 款项账号
     */
    @Value("${bank.api.accountNo}")
    private String ACCOUNT_NO;

    @Autowired
    private MinshengBankApiRecordMapper minshengBankApiRecordMapper;
    @Autowired
    private B2eBankApiService b2eBankApiService;
    @Autowired
    MinshengBankTransferRecordMapper minshengBankTransferRecordMapper;


    private final static String REDIS_KEY = "ERP:MSBANK:CREATE:";
    private final static long WAIT_TIME = 10;
    private final static long LOCK_SECONDS = 20;


    @Override
    @Transactional(rollbackFor = Throwable.class)
    public B2eDraftApplyRes b2eNbsAcceptanceBillCreate(AcceptanceBillCreateDto createDto) {
        log.info("创建承兑汇票请求入参：{}", JSON.toJSONString(createDto));

        B2eDraftApplyRes b2eDraftApplyRes = new B2eDraftApplyRes();
        this.validateCreateDto(createDto, b2eDraftApplyRes);

        String key = REDIS_KEY + createDto.getB2eBasicDraftApplyReq().getOutApplicationNo();
        MinshengBankTransferRecord bankTransferRecord = null;

        try {
            // 尝试获取锁，避免并发操作
            if (!RedissonLockUtils.tryLock(key, WAIT_TIME, LOCK_SECONDS, TimeUnit.SECONDS)) {
                log.error("相同流水号短时间连续请求民生银行转账接口：{}", JSON.toJSONString(createDto));
                b2eDraftApplyRes.checkAndSetResponseHeader(b2eDraftApplyRes, "该付款申请，正在发起承兑汇票，请勿重复操作！");
                return b2eDraftApplyRes;
            }

            bankTransferRecord = this.getTransferRecord(createDto);

            // 检查融资申请状态，避免重复操作
            if (this.isTransferInProgress(bankTransferRecord)) {
                log.error("业务流水号：{}的融资申请正在处理中，需人工确认处理！", createDto.getPayAppId());
                b2eDraftApplyRes.checkAndSetResponseHeader(b2eDraftApplyRes, "该付款申请，正在发起承兑汇票，请勿重复操作！");
                return b2eDraftApplyRes;
            }
            if (this.isTransferSuccessful(bankTransferRecord)) {
                b2eDraftApplyRes.checkAndSetResponseHeader(b2eDraftApplyRes, "该付款申请，已发起承兑汇票成功，请勿重复操作！");
                return b2eDraftApplyRes;
            }

            log.info("创建承兑汇票加锁成功, key = [{}]", key);

            // 执行承兑汇票的创建操作
            b2eDraftApplyRes = this.executeDraftCreation(createDto);

        } catch (AcceptanceBillException e) {
            log.error("承兑汇票创建过程中发生业务异常", e);
            b2eDraftApplyRes.fail(b2eDraftApplyRes, e.getMessage());
        } catch (Exception e) {
            log.error("创建承兑汇票异常", e);
            b2eDraftApplyRes.fail(b2eDraftApplyRes, e.getMessage());
        } finally {
            // 确保锁被释放
            RedissonLockUtils.unlock(key);
            log.info("创建承兑汇票释放锁成功, key = [{}]", key);
        }

        // 更新融资申请记录状态
        this.updateTransferRecordStatus(bankTransferRecord, b2eDraftApplyRes);

        log.info("创建承兑汇票响应：{}", JSON.toJSONString(b2eDraftApplyRes));

        return b2eDraftApplyRes;
    }

    private boolean isTransferInProgress(MinshengBankTransferRecord record) {
        return BankStatusType.CALL_START.getCode().equals(record.getResponseCode());
    }

    private boolean isTransferSuccessful(MinshengBankTransferRecord record) {
        return BankStatusType.SUCESS.getCode().equals(record.getResponseCode());
    }


    private B2eDraftApplyRes executeDraftCreation(AcceptanceBillCreateDto createDto) throws Exception {

        // 步骤1: 获取文件token
        String fileToken = this.getFileToken();

        // 步骤2: 上传文件
        B2eFileUploadApiRes fileUploadResponse = this.uploadFile(createDto.getFileUrl(), fileToken);

        // 步骤3: 融资预申请
        B2eBasicDraftApplyRes draftApplyResponse = this.applyForDraft(createDto, fileUploadResponse);

        // 步骤4: 融资申请提交
        return this.submitDraftApplication(draftApplyResponse);
    }

    /**
     * 参数验证
     */
    private void validateCreateDto(AcceptanceBillCreateDto createDto, B2eDraftApplyRes queryResult) {
        Assert.notNull(createDto, () -> new AcceptanceBillException("参数不能为空"));
        Assert.notEmpty(createDto.getFileUrl(), () -> new AcceptanceBillException("文件地址不能为空"));
        Assert.notNull(createDto.getPayAppId(), () -> new AcceptanceBillException("付款申请id不能为空"));

        boolean isValid = createDto.getB2eBasicDraftApplyReq() != null &&
                createDto.getB2eBasicDraftApplyReq().validate(queryResult);
        if (!isValid) {
            throw new AcceptanceBillException("验证失败：参数不合法");
        }

        log.info("参数校验通过");
    }

    /**
     * 获取文件Token
     */
    private String getFileToken() {
        B2eGetTokenReq req = new B2eGetTokenReq(IdUtil.simpleUUID());
        B2eGetTokenApiReq apiRequest = new B2eGetTokenApiReq(req);

        B2eGetTokenRes tokenResponse = b2eBankApiService.callRemote(apiRequest, B2eGetTokenRes.class);

        if (tokenResponse.isFail()) {
            throw new AcceptanceBillException("获取文件token失败：" + tokenResponse.getMessage());
        }

        String tokenId = tokenResponse.getXDataBody().getTokenId();
        if (StrUtil.isBlank(tokenId)) {
            throw new AcceptanceBillException("获取文件token异常，是空值");
        }
        log.info("获取文件token成功，TokenId: {}", tokenId);
        return tokenId;
    }

    /**
     * 上传文件
     */
    private B2eFileUploadApiRes uploadFile(String fileUrl, String fileToken) {
        B2eFileUploadApiReq uploadReq = new B2eFileUploadApiReq();
        uploadReq.setOssUrl(fileUrl);
        uploadReq.setFileToken(fileToken);
        uploadReq.setSceneId("B2eDraftContractAttach");

        B2eFileUploadApiRes uploadResponse = b2eBankApiService.upload(uploadReq);
        if (!B2eFileUploadApiRes.isSuccess(uploadResponse)) {
            throw new AcceptanceBillException("文件上传失败：" + uploadResponse.getMsg());
        }
        log.info("文件上传成功，文件信息: {}", JSON.toJSONString(uploadResponse.getFiles()));
        return uploadResponse;

    }

    /**
     * 融资预申请
     */
    private B2eBasicDraftApplyRes applyForDraft(AcceptanceBillCreateDto createDto, B2eFileUploadApiRes fileUploadResponse) {
        B2eBasicDraftApplyReq draftReq = createDto.getB2eBasicDraftApplyReq();

        List<B2eFileUploadApiRes.FileInfo> files = fileUploadResponse.getFiles();

        // 签约账号
        draftReq.setIssueAccountNo(ISSUE_ACCOUNT_NO);
        draftReq.setRepayAccountNo(ACCOUNT_NO);
        draftReq.setFeeAccountNo(ACCOUNT_NO);
        if ("1".equals(draftReq.getIsDeduct())) {
            draftReq.setDeductAccountNo(ACCOUNT_NO);
        }

        // 保证金账号
        if(Objects.equals(draftReq.getCreditType(),"01")){
            draftReq.getBailInfoList().forEach(bailInfo -> bailInfo.setBailAccountNo(BAIL_ACCOUNT_NO));
        }

        files.forEach(firstFile -> {
            B2eBasicDraftApplyReq.FileInfo fileInfo = B2eBasicDraftApplyReq.FileInfo.builder()
                    .fileName(firstFile.getRealName())
                    .imageId(firstFile.getImageId())
                    .fileType("04")
                    .sysNo("734_354")
                    .build();
            draftReq.getFileListInfo().add(fileInfo);
        });

        B2eBasicDraftApplyApiReq draftApiReq = new B2eBasicDraftApplyApiReq(draftReq);

        B2eBasicDraftApplyRes draftResponse = b2eBankApiService.callRemote(draftApiReq, B2eBasicDraftApplyRes.class);
        if (draftResponse.isFail()) {
            throw new AcceptanceBillException("融资预申请失败:" + draftResponse.getMessage());
        }
        log.info("融资预申请成功，响应: {}", JSON.toJSONString(draftResponse));
        return draftResponse;
    }


    private static final int MAX_RETRY_COUNT = 50;
    private static final int RETRY_DELAY_MS = 15000;

    /**
     * 融资申请提交
     */
    private B2eDraftApplyRes submitDraftApplication(B2eBasicDraftApplyRes draftResponse) {
        B2eDraftApplyReq draftApplyReq = buildDraftApplyRequest(draftResponse);

        List<B2eBasicDraftApplyRes.FileInfo> files = draftResponse.getXDataBody().getFileInfoList();
        if (files == null || files.isEmpty()) {
            throw new AcceptanceBillException("文件上传响应为空或文件列表为空，无法提交融资申请");
        }
        log.info("文件上传成功，文件信息，加签内容: {}", JSON.toJSONString(files));

        if (LOCAL_SIGNATURE) {
            this.processLocalSignature(files, draftApplyReq);
        } else {
            this.processRemoteSignature(files, draftApplyReq);
        }

        B2eDraftApplyApiReq draftApplyApiReq = new B2eDraftApplyApiReq(draftApplyReq);

        B2eDraftApplyRes b2eDraftApplyRes = b2eBankApiService.callRemote(draftApplyApiReq, B2eDraftApplyRes.class);
        if (b2eDraftApplyRes.isFail()) {
            throw new AcceptanceBillException("融资申请提交失败:" + b2eDraftApplyRes.getMessage());
        }
        return b2eDraftApplyRes;
    }

    private B2eDraftApplyReq buildDraftApplyRequest(B2eBasicDraftApplyRes draftResponse) {
        B2eDraftApplyReq draftApplyReq = new B2eDraftApplyReq();
        draftApplyReq.setTrnId(IdUtil.fastSimpleUUID());
        draftApplyReq.setInsId(draftResponse.getXDataBody().getOutApplicationNo() + IdUtil.fastSimpleUUID());
        draftApplyReq.setOutApplicationNo(draftResponse.getXDataBody().getOutApplicationNo());
        draftApplyReq.setCreditCode(draftResponse.getXDataBody().getCreditCode());
        return draftApplyReq;
    }

    private void processLocalSignature(List<B2eBasicDraftApplyRes.FileInfo> files, B2eDraftApplyReq draftApplyReq) {
        List<B2eDraftApplyReq.SignatureFile> signatureFiles = new ArrayList<>();
        files.forEach(fileInfo -> {
            B2eDraftApplyReq.SignatureFile signatureFile = buildSignatureFile(fileInfo);
            signatureFiles.add(signatureFile);
        });
        log.info("本地加签需要返回的加签内容，缺少签名: {}", JSON.toJSONString(signatureFiles));

        String signatureJson = getSignatureWithRetry();
        if (signatureJson == null) {
            throw new AcceptanceBillException("无法从 Apollo 读取到有效的 SIGNATURE");
        }
        List<B2eDraftApplyReq.SignatureFile> signatureFileList = JSON.parseArray(signatureJson, B2eDraftApplyReq.SignatureFile.class);
        log.info("本地加签返回的加签内容，已添加签名: {}", JSON.toJSONString(signatureFileList));
        if (CollUtil.isEmpty(signatureFileList)) {
            throw new AcceptanceBillException("本地加签返回的加签内容为空");
        }
        draftApplyReq.setSignatureFileList(signatureFileList);
    }

    private String getSignatureWithRetry() {
        int retryCount = 0;
        String signatureJson = SIGNATURE;

        while ((signatureJson == null || signatureJson.isEmpty()) && retryCount < MAX_RETRY_COUNT) {
            log.info("从 Apollo 中读取的 SIGNATURE 为空，等待 {} 秒后重试 (第 {} 次)...", RETRY_DELAY_MS / 1000, retryCount + 1);
            try {
                Thread.sleep(RETRY_DELAY_MS);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new AcceptanceBillException("本地加签线程中断异常", e);
            }
            retryCount++;
            signatureJson = SIGNATURE;
        }
        return signatureJson;
    }


    private void processRemoteSignature(List<B2eBasicDraftApplyRes.FileInfo> files, B2eDraftApplyReq draftApplyReq) {
        files.forEach(fileInfo -> {
            String fileSealHash = fileInfo.getFileSealHash();
            log.info("文件签名开始，文件SealHash: {}", fileSealHash);
            B2eSignRes b2eSignRes = b2eBankApiService.sendSignRequest(fileSealHash);
            String signature = b2eSignRes.getXDataBody().getSignResult();
            B2eDraftApplyReq.SignatureFile signatureFile = buildSignatureFile(fileInfo);
            signatureFile.setSignature(signature);
            draftApplyReq.getSignatureFileList().add(signatureFile);
        });
    }

    /**
     * 构建加签文件对象
     */
    private B2eDraftApplyReq.SignatureFile buildSignatureFile(B2eBasicDraftApplyRes.FileInfo fileInfo) {
        return B2eDraftApplyReq.SignatureFile.builder()
                .imageId(fileInfo.getImageId())
                .fileName(fileInfo.getFileName())
                .fileType(fileInfo.getFileType())
                .sysNo("734_734")
                .cntrNum(fileInfo.getCntrNum())
                .id(fileInfo.getId())
                .build();
    }


    private MinshengBankTransferRecord getTransferRecord(AcceptanceBillCreateDto createDto) {
        List<MinshengBankTransferRecord> transferRecords = minshengBankTransferRecordMapper.findByPayApplyId(createDto.getPayAppId());
        if (CollUtil.isEmpty(transferRecords)) {
            MinshengBankTransferRecord newRecord = new MinshengBankTransferRecord();
            newRecord.setPayApplyId(createDto.getPayAppId());
            newRecord.setCreditType(createDto.getB2eBasicDraftApplyReq().getCreditType());
            minshengBankTransferRecordMapper.insertSelective(newRecord);
            return newRecord;
        } else {
            return CollUtil.getFirst(transferRecords);
        }
    }


    private void updateTransferRecordStatus(MinshengBankTransferRecord record, B2eDraftApplyRes response) {
        if (response.isFail()) {
            record.setResponseMessage(response.getMessage());
            record.setResponseCode(BankStatusType.CALL_ERROR.getCode());
        } else {
            String code = response.getResponseHeader().getStatus().getCode();
            String message = StrUtil.sub(response.getResponseHeader().getStatus().getMessage(), 0, 50);
            record.setResponseCode(code);
            record.setResponseMessage(message);

            if (BankStatusType.SUCESS.getCode().equals(code)) {
                B2eDraftApplyRes.B2eBasicDraftApplyResBody xDataBody = response.getXDataBody();
                record.setSvrId(xDataBody.getSvrId());
                record.setInsId(xDataBody.getInsId());
                record.setTransferCode(xDataBody.getRetCode());
                record.setTransferMessage(xDataBody.getRetMsg());
            }
        }
        minshengBankTransferRecordMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 查询融资申请结果
     */
    @Override
    public B2eQueryBasicDraftResultRes queryDraftResult(B2eQueryBasicDraftResultReq queryReq) {
        queryReq.setStatus("A");

        B2eQueryBasicDraftResultApiReq queryApiReq = new B2eQueryBasicDraftResultApiReq(queryReq);
        B2eQueryBasicDraftResultRes queryResponse = b2eBankApiService.callRemote(queryApiReq, B2eQueryBasicDraftResultRes.class);
        if (queryResponse.isFail()) {
            throw new AcceptanceBillException("融资申请结果查询失败");
        }
        log.info("融资申请结果查询成功，响应: {}", JSON.toJSONString(queryResponse));
        return queryResponse;

    }


    /**
     * 持有票据查询(B2eNbsDraftHoldingBillsQry)
     *
     * @param body
     * @return
     */
    @Override
    public DraftHoldingBillsQryResponse b2eNbsDraftHoldingBillsQry(DraftHoldingBillsQryBody body) {
        log.info("持有票据查询(B2eNbsDraftHoldingBillsQry)请求入参：{}", JSON.toJSONString(body));

        DraftHoldingBillsQryResponse draftHoldingBillsQryResponse = new DraftHoldingBillsQryResponse();
        //必填参数校验
        boolean validate = body.validate(draftHoldingBillsQryResponse);
        if (Boolean.FALSE.equals(validate)) {
            return draftHoldingBillsQryResponse;
        }
        try {
            // 使用Dom4j构建XML
            Document document = DocumentHelper.createDocument();

            //封装请求头xml
            Element root = BankApiXmlUtil.createXmlRootAndHead(document, BankApiType.B2ENBS_DRAFT_HOLDING_BILLS_QRY.getTrnCode(), bankApiClientId, bankApiUserId, bankApiUserPswd);
            //封装请求体
            Element xDataBody = root.addElement("xDataBody");
            xDataBody.addElement("trnId").addText(StrUtil.nullToEmpty(body.getTrnId()));
            xDataBody.addElement("custAccount").addText(StrUtil.nullToEmpty(body.getCustAccount()));
            xDataBody.addElement("pageNo").addText(body.getPageNo() != null ? String.valueOf(body.getPageNo()) : "");
            xDataBody.addElement("pageSize").addText(body.getPageSize() != null ? String.valueOf(body.getPageSize()) : "");
            xDataBody.addElement("billType").addText(StrUtil.nullToEmpty(body.getBillType()));
            xDataBody.addElement("billNo").addText(StrUtil.nullToEmpty(body.getBillNo()));
            xDataBody.addElement("minBillMoney").addText(body.getMinBillMoney() != null ? String.valueOf(body.getMinBillMoney()) : "");
            xDataBody.addElement("maxBillMoney").addText(body.getMaxBillMoney() != null ? String.valueOf(body.getMaxBillMoney()) : "");
            xDataBody.addElement("beginAcptDt").addText(StrUtil.nullToEmpty(body.getBeginAcptDt()));
            xDataBody.addElement("endAcptDt").addText(StrUtil.nullToEmpty(body.getEndAcptDt()));
            xDataBody.addElement("beginEndDate").addText(StrUtil.nullToEmpty(body.getBeginEndDate()));
            xDataBody.addElement("endDate").addText(StrUtil.nullToEmpty(body.getEndDate()));
            xDataBody.addElement("acptName").addText(StrUtil.nullToEmpty(body.getAcptName()));
            xDataBody.addElement("oppName").addText(StrUtil.nullToEmpty(body.getOppName()));
            // 将XML转换为字符串
            StringWriter reqXml = new StringWriter();
            OutputFormat format = OutputFormat.createPrettyPrint();
            XMLWriter xmlWriter = new XMLWriter(reqXml, format);
            xmlWriter.write(document);
            xmlWriter.close();
            log.info("入参转化为xml：{}", reqXml);
            String res = HttpRequest.post(bankApiUrl)
                    .header("Content-type", "application/x-NS-BDES")
                    .body(reqXml.toString())
                    .timeout(60000)
                    .execute().body();
            log.info("持有票据查询(B2eNbsDraftHoldingBillsQry)返回结果：{}", res);
            // 解析xml，映射到实体类
            SAXReader reader = new SAXReader();
            Document resDocument = reader.read(new StringReader(res));

            //解析 responseHeader
            Element responseHeader = resDocument.getRootElement().element("responseHeader");
            //header xml转实体类
            ResponseHeader header = BankApiXmlUtil.getResponseHeader(responseHeader);
            draftHoldingBillsQryResponse.setResponseHeader(header);

            // 解析 xDataBody
            DraftHoldingBillsQryResponse.DraftHoldingBillsQryResBody resBody = new DraftHoldingBillsQryResponse.DraftHoldingBillsQryResBody();
            Element dataBody = resDocument.getRootElement().element("xDataBody");
            resBody.setTotal(Integer.parseInt(dataBody.elementText("total")));
            resBody.setSvrId(dataBody.elementText("svrId"));
            resBody.setTrnId(dataBody.elementText("trnId"));
            List<DraftHoldingBillsQryResponse.MapEntry> entries = new ArrayList<>();
            Element listElement = dataBody.element("List");

            for (Element mapElement : listElement.elements("Map")) {
                DraftHoldingBillsQryResponse.MapEntry entry = new DraftHoldingBillsQryResponse.MapEntry();
                entry.setBillNo(mapElement.elementText("billNo"));
                entry.setBillType(mapElement.elementText("billType"));
                entry.setBillClass(mapElement.elementText("billClass"));
                entry.setHldrName(mapElement.elementText("hldrName"));
                entry.setHldrAcctNo(mapElement.elementText("hldrAcctNo"));
                entry.setHldrBankNo(mapElement.elementText("hldrBankNo"));
                entry.setIsAllowSplitBill(mapElement.elementText("isAllowSplitBill"));
                entry.setRemitDt(mapElement.elementText("remitDt"));
                entry.setDueDt(mapElement.elementText("dueDt"));
                entry.setBillMoney(mapElement.elementText("billMoney"));
                entry.setDrwrName(mapElement.elementText("drwrName"));
                entry.setDrwrAcctNo(mapElement.elementText("drwrAcctNo"));
                entry.setDrwrAcctName(mapElement.elementText("drwrAcctName"));
                entry.setDrwrBankName(mapElement.elementText("drwrBankName"));
                entry.setDrwrBankNo(mapElement.elementText("drwrBankNo"));
                entry.setPyeeName(mapElement.elementText("pyeeName"));
                entry.setPyeeAcctNo(mapElement.elementText("pyeeAcctNo"));
                entry.setAcptAcctName(mapElement.elementText("acptAcctName"));
                entry.setPyeeBankName(mapElement.elementText("pyeeBankName"));
                entry.setPyeeBankNo(mapElement.elementText("pyeeBankNo"));
                entry.setAcptName(mapElement.elementText("acptName"));
                entry.setAcptAcctNo(mapElement.elementText("acptAcctNo"));
                entry.setAcptBankName(mapElement.elementText("acptBankName"));
                entry.setAcptBankNo(mapElement.elementText("acptBankNo"));
                entry.setBillRangeStart(mapElement.elementText("billRangeStart"));
                entry.setBillRangeEnd(mapElement.elementText("billRangeEnd"));
                entry.setTransAmt(mapElement.elementText("transAmt"));
                entry.setBanEndrsmtMark(mapElement.elementText("banEndrsmtMark"));
                entry.setBillStatus(mapElement.elementText("billStatus"));
                entry.setCirStatus(mapElement.elementText("cirStatus"));
                entry.setGrayDisplay(mapElement.elementText("grayDisplay"));
                entries.add(entry);
            }

            resBody.setList(entries);
            draftHoldingBillsQryResponse.setXDataBody(resBody);

            //异步保存银行操作记录
            insertBankApiRecord(reqXml.toString(), res, BankApiType.B2ENBS_DRAFT_HOLDING_BILLS_QRY.getCode(), body.getTrnId(), "");
        } catch (Exception e) {
            log.error("持有票据查询(B2eNbsDraftHoldingBillsQry)接口调用失败", e);
        }
        //如果响应头为null，则说明程序错误
        draftHoldingBillsQryResponse.checkAndSetResponseHeader(draftHoldingBillsQryResponse, "持有票据查询(B2eNbsDraftHoldingBillsQry)程序执行失败！");
        return draftHoldingBillsQryResponse;
    }

    /**
     * 票据详细信息查询(B2eNbsDraftDetail)
     *
     * @param body
     * @return
     */
    @Override
    public DraftDetailResponse b2eNbsDraftDetail(DraftDetailReqBody body) {
        log.info("票据详细信息查询(B2eNbsDraftDetail)请求入参：{}", JSON.toJSONString(body));

        DraftDetailResponse draftDetailResponse = new DraftDetailResponse();
        //必填参数校验
        boolean validate = body.validate(draftDetailResponse);
        if (Boolean.FALSE.equals(validate)) {
            return draftDetailResponse;
        }
        try {
            // 使用Dom4j构建XML
            Document document = DocumentHelper.createDocument();

            //封装请求头xml
            Element root = BankApiXmlUtil.createXmlRootAndHead(document, BankApiType.B2ENBS_DRAFT_DETAIL.getTrnCode(), bankApiClientId, bankApiUserId, bankApiUserPswd);
            //封装请求体
            Element xDataBody = root.addElement("xDataBody");
            xDataBody.addElement("trnId").addText(StrUtil.nullToEmpty(body.getTrnId()));
            xDataBody.addElement("custAccount").addText(StrUtil.nullToEmpty(body.getCustAccount()));
            xDataBody.addElement("billNo").addText(StrUtil.nullToEmpty(body.getBillNo()));
            xDataBody.addElement("billRangeStart").addText(StrUtil.nullToEmpty(body.getBillRangeStart()));
            xDataBody.addElement("billRangeEnd").addText(StrUtil.nullToEmpty(body.getBillRangeEnd()));
            xDataBody.addElement("showBillRemark").addText(StrUtil.nullToEmpty(body.getShowBillRemark()));
            // 将XML转换为字符串
            StringWriter reqXml = new StringWriter();
            OutputFormat format = OutputFormat.createPrettyPrint();
            XMLWriter xmlWriter = new XMLWriter(reqXml, format);
            xmlWriter.write(document);
            xmlWriter.close();
            log.info("入参转化为xml：{}", reqXml);
            String res = HttpRequest.post(bankApiUrl)
                    .header("Content-type", "application/x-NS-BDES")
                    .body(reqXml.toString())
                    .timeout(60000)
                    .execute().body();
            log.info("票据详细信息查询(B2eNbsDraftDetail)返回结果：{}", res);
            // 解析xml，映射到实体类
            SAXReader reader = new SAXReader();
            Document resDocument = reader.read(new StringReader(res));

            //解析 responseHeader
            Element responseHeader = resDocument.getRootElement().element("responseHeader");
            //header xml转实体类
            ResponseHeader header = BankApiXmlUtil.getResponseHeader(responseHeader);
            draftDetailResponse.setResponseHeader(header);

            // 解析 xDataBody
            DraftDetailResponse.DraftDetailResBody resBody = new DraftDetailResponse.DraftDetailResBody();
            Element dataBody = resDocument.getRootElement().element("xDataBody");
            // 赋值服务 ID 和交易 ID
            resBody.setSvrId(dataBody.elementText("svrId"));
            resBody.setTrnId(dataBody.elementText("trnId"));

            // 创建并赋值 BillInfo 对象
            DraftDetailResponse.BillInfo billInfo = new DraftDetailResponse.BillInfo();
            Element billInfoElement = dataBody.element("billInfo");

            if (billInfoElement != null) {
                billInfo.setBillNo(billInfoElement.elementText("billNo"));
                billInfo.setBillType(billInfoElement.elementText("billType"));
                billInfo.setBillClass(billInfoElement.elementText("billClass"));
                billInfo.setIsAllowSplitBill(billInfoElement.elementText("isAllowSplitBill"));
                billInfo.setRemitDt(billInfoElement.elementText("remitDt"));
                billInfo.setDueDt(billInfoElement.elementText("dueDt"));
                billInfo.setAcptDt(billInfoElement.elementText("acptDt"));
                billInfo.setBillMoney(billInfoElement.elementText("billMoney"));
                billInfo.setDrwrName(billInfoElement.elementText("drwrName"));
                billInfo.setDrwrAcctNo(billInfoElement.elementText("drwrAcctNo"));
                billInfo.setDrwrAcctName(billInfoElement.elementText("drwrAcctName"));
                billInfo.setDrwrBankName(billInfoElement.elementText("drwrBankName"));
                billInfo.setDrwrBankNo(billInfoElement.elementText("drwrBankNo"));
                billInfo.setPyeeName(billInfoElement.elementText("pyeeName"));
                billInfo.setPyeeAcctNo(billInfoElement.elementText("pyeeAcctNo"));
                billInfo.setPyeeAcctName(billInfoElement.elementText("pyeeAcctName"));
                billInfo.setPyeeBankName(billInfoElement.elementText("pyeeBankName"));
                billInfo.setPyeeBankNo(billInfoElement.elementText("pyeeBankNo"));
                billInfo.setAcptName(billInfoElement.elementText("acptName"));
                billInfo.setAcptAcctNo(billInfoElement.elementText("acptAcctNo"));
                billInfo.setAcptAcctName(billInfoElement.elementText("acptAcctName"));
                billInfo.setAcptBankName(billInfoElement.elementText("acptBankName"));
                billInfo.setAcptBankNo(billInfoElement.elementText("acptBankNo"));
                billInfo.setAcptBankType(billInfoElement.elementText("acptBankType"));
                billInfo.setBillRangeStart(billInfoElement.elementText("billRangeStart"));
                billInfo.setBillRangeEnd(billInfoElement.elementText("billRangeEnd"));
                billInfo.setTransAmt(billInfoElement.elementText("transAmt"));
                billInfo.setHldrBanEndrsmtMark(billInfoElement.elementText("hldrBanEndrsmtMark"));
                billInfo.setTransCtrctNo(billInfoElement.elementText("transCtrctNo"));
                billInfo.setInvoiceNo(billInfoElement.elementText("invoiceNo"));
                billInfo.setDrwrCreditRatgs(billInfoElement.elementText("drwrCreditRatgs"));
                billInfo.setDrwrCdtRatgAgcy(billInfoElement.elementText("drwrCdtRatgAgcy"));
                billInfo.setDrwrRatgDuedt(billInfoElement.elementText("drwrRatgDuedt"));

                billInfo.setAcptCdtRatgAgcy(billInfoElement.elementText("acptCdtRatgAgcy"));
                billInfo.setAcptCdtRatgs(billInfoElement.elementText("acptCdtRatgs"));
                billInfo.setAcptCdtRatgDueDt(billInfoElement.elementText("acptCdtRatgDueDt"));

                billInfo.setBillStatus(billInfoElement.elementText("billStatus"));
                billInfo.setCirStatus(billInfoElement.elementText("cirStatus"));
            }

            // 将 BillInfo 对象设置到 XDataBody 中
            resBody.setBillInfo(billInfo);

            draftDetailResponse.setXDataBody(resBody);

            //异步保存银行操作记录
            insertBankApiRecord(reqXml.toString(), res, BankApiType.B2ENBS_DRAFT_DETAIL.getCode(), body.getTrnId(), "");
        } catch (Exception e) {
            log.error("票据详细信息查询(B2eNbsDraftDetail)接口调用失败", e);
        }
        //如果响应头为null，则说明程序错误
        draftDetailResponse.checkAndSetResponseHeader(draftDetailResponse, "票据详细信息查询(B2eNbsDraftDetail)程序执行失败！");
        return draftDetailResponse;
    }

    /**
     * 可通用签收票据列表查询(B2eNbsQryStaySignUpDrafts)
     *
     * @param body
     * @return
     */
    private B2eNbsQryStaySignUpDraftsResponse B2eNbsQryStaySignUpDrafts(B2eNbsQryStaySignUpDraftsReqBody body) {
        log.info("可通用签收票据列表查询(B2eNbsQryStaySignUpDrafts)请求入参：{}", JSON.toJSONString(body));
        B2eNbsQryStaySignUpDraftsResponse b2eNbsDraftIssueAddResponse = new B2eNbsQryStaySignUpDraftsResponse();
        //必填参数校验
        boolean validate = body.validate(b2eNbsDraftIssueAddResponse);
        if (Boolean.FALSE.equals(validate)) {
            return b2eNbsDraftIssueAddResponse;
        }
        try {
            // 使用Dom4j构建XML
            Document document = DocumentHelper.createDocument();

            //封装请求头xml
            Element root = BankApiXmlUtil.createXmlRootAndHead(document, BankApiType.B2ENBS_QRY_STAY_SIGNUP_DRAFTS.getTrnCode(), bankApiShouClientId, bankApiShouUserId, bankApiShouUserPswd);
            //封装请求体
            Element xDataBody = root.addElement("xDataBody");
            xDataBody.addElement("trnId").addText(StrUtil.nullToEmpty(body.getTrnId()));
            xDataBody.addElement("custAccount").addText(StrUtil.nullToEmpty(body.getCustAccount()));
            xDataBody.addElement("queryType").addText(StrUtil.nullToEmpty(body.getQueryType()));
            //以将下自动 赋值
            xDataBody.addElement("pageNo").addText(StrUtil.nullToEmpty(body.getPageNo()));
            xDataBody.addElement("pageSize").addText(StrUtil.nullToEmpty(body.getPageSize()));
            xDataBody.addElement("billNo").addText(StrUtil.nullToEmpty(body.getBillNo()));
            xDataBody.addElement("billType").addText(StrUtil.nullToEmpty(body.getBillType()));
            xDataBody.addElement("minBillMoney").addText(StrUtil.nullToEmpty(body.getMinBillMoney()));
            xDataBody.addElement("maxBillMoney").addText(StrUtil.nullToEmpty(body.getMaxBillMoney()));
            xDataBody.addElement("beginAcptDt").addText(StrUtil.nullToEmpty(body.getBeginAcptDt()));
            xDataBody.addElement("endAcptDt").addText(StrUtil.nullToEmpty(body.getEndAcptDt()));
            xDataBody.addElement("beginEndDate").addText(StrUtil.nullToEmpty(body.getBeginEndDate()));
            xDataBody.addElement("endDate").addText(StrUtil.nullToEmpty(body.getEndDate()));

            // 将XML转换为字符串
            StringWriter reqXml = new StringWriter();
            OutputFormat format = OutputFormat.createPrettyPrint();
            XMLWriter xmlWriter = new XMLWriter(reqXml, format);
            xmlWriter.write(document);
            xmlWriter.close();
            log.info("入参转化为xml：{}", printLogXml(reqXml.toString()));
            String res = HttpRequest.post(bankApiUrl)
                    .header("Content-type", "application/x-NS-BDES")
                    .body(reqXml.toString())
                    .timeout(60000)
                    .execute().body();
            log.info("可通用签收票据列表查询(B2eNbsQryStaySignUpDrafts)返回结果：{}", res);
            // 解析xml，映射到实体类
            SAXReader reader = new SAXReader();
            Document resDocument = reader.read(new StringReader(res));

            //解析 responseHeader
            Element responseHeader = resDocument.getRootElement().element("responseHeader");
            //header xml转实体类
            ResponseHeader header = BankApiXmlUtil.getResponseHeader(responseHeader);
            b2eNbsDraftIssueAddResponse.setResponseHeader(header);

            // 解析 xDataBody
            B2eNbsQryStaySignUpDraftsResponse.B2eNbsQryStaySignUpDraftsResBody resBody = new B2eNbsQryStaySignUpDraftsResponse.B2eNbsQryStaySignUpDraftsResBody();
            Element dataBody = resDocument.getRootElement().element("xDataBody");


            // 解析 xDataBody


            if (dataBody != null) {
                resBody.setTrnId(dataBody.elementText("trnId"));
                resBody.setSvrId(dataBody.elementText("svrId"));
                List<B2eNbsQryStaySignUpDraftsResponse.BillSignMap> list = new ArrayList<>();
                Element listElement = dataBody.element("List");
                if (listElement != null) {
                    for (Element mapElement : listElement.elements("Map")) {
                        B2eNbsQryStaySignUpDraftsResponse.BillSignMap billSignMap = new B2eNbsQryStaySignUpDraftsResponse.BillSignMap();
                        billSignMap.setBillNo(mapElement.elementText("billNo"));
                        billSignMap.setTransId(mapElement.elementText("transId"));
                        list.add(billSignMap);
                    }
                }
                resBody.setList(list);
                // 将 BillInfo 对象设置到 XDataBody 中
                b2eNbsDraftIssueAddResponse.setXDataBody(resBody);
            }

            //异步保存银行操作记录
            insertBankApiRecord(reqXml.toString(), res, BankApiType.B2ENBS_QRY_STAY_SIGNUP_DRAFTS.getCode(), body.getTrnId(), "");
        } catch (Exception e) {
            log.error("可通用签收票据列表查询(B2eNbsQryStaySignUpDrafts)接口调用失败", e);
        }
        return b2eNbsDraftIssueAddResponse;
    }

    private B2eNbsDraftSignUpReqBody createB2eNbsDraftSignUpReqBody(B2eNbsQryStaySignUpDraftsReqBody body, String transId) {
        B2eNbsDraftSignUpReqBody detailReqBody = new B2eNbsDraftSignUpReqBody();
        detailReqBody.setTrnId(IdUtil.simpleUUID());
        detailReqBody.setInsId("InsId" + transId);
        detailReqBody.setCustAccount(body.getCustAccount());
        B2eNbsDraftSignUpReqBody.BillSignMap billSignMap = new B2eNbsDraftSignUpReqBody.BillSignMap();
        billSignMap.setTransId(transId);
        billSignMap.setTransCode("02");
        billSignMap.setSignFlag("SU00");
        List<B2eNbsDraftSignUpReqBody.BillSignMap> list = new ArrayList<>();
        list.add(billSignMap);
        detailReqBody.setList(list);
        return detailReqBody;
    }

    @Override
    public B2eNbsDraftSignUpResponse b2eNbsDraftSignUp(B2eNbsQryStaySignUpDraftsReqBody body) {
        log.info("业务发起签收申请，请求入参：{}", JSON.toJSONString(body));
        B2eNbsQryStaySignUpDraftsResponse step1Response = B2eNbsQryStaySignUpDrafts(body);
        String transId = null;
        if (CollectionUtil.isNotEmpty(step1Response.getXDataBody().getList())) {
            transId = step1Response.getXDataBody().getList().get(0).getTransId();
        }

        B2eNbsDraftSignUpResponse b2eNbsDraftSignUpResponse = new B2eNbsDraftSignUpResponse();

        if (StringUtils.isEmpty(transId)) {
            b2eNbsDraftSignUpResponse.checkAndSetResponseHeader(b2eNbsDraftSignUpResponse, "通用签收(B2eNbsDraftSignUp)程序执行失败,原因：未获取到签收列表中的transId！");
            return b2eNbsDraftSignUpResponse;
        }


        B2eNbsDraftSignUpReqBody b2eNbsDraftSignUpReqBody = createB2eNbsDraftSignUpReqBody(body, transId);
        log.info("通用签收(B2eNbsDraftSignUp)请求入参：{}", JSON.toJSONString(b2eNbsDraftSignUpReqBody));

        try {
            // 使用Dom4j构建XML
            Document document = DocumentHelper.createDocument();

            //封装请求头xml
            Element root = BankApiXmlUtil.createXmlRootAndHead(document, BankApiType.B2ENBS_DRAFT_SIGNUP.getTrnCode(), bankApiShouClientId, bankApiShouUserId, bankApiShouUserPswd);
            //封装请求体
            Element xDataBody = root.addElement("xDataBody");
            xDataBody.addElement("trnId").addText(StrUtil.nullToEmpty(b2eNbsDraftSignUpReqBody.getTrnId()));
            xDataBody.addElement("insId").addText(StrUtil.nullToEmpty(b2eNbsDraftSignUpReqBody.getInsId()));
            xDataBody.addElement("custAccount").addText(StrUtil.nullToEmpty(b2eNbsDraftSignUpReqBody.getCustAccount()));
            Element listItem = xDataBody.addElement("List");
            //List<BillSignMap> list
            for (B2eNbsDraftSignUpReqBody.BillSignMap billSignMap : b2eNbsDraftSignUpReqBody.getList()) {
                Element mapElement = listItem.addElement("Map");
                mapElement.addElement("transId").addText(StrUtil.nullToEmpty(billSignMap.getTransId()));
                mapElement.addElement("transCode").addText(StrUtil.nullToEmpty(billSignMap.getTransCode()));
                mapElement.addElement("signFlag").addText(StrUtil.nullToEmpty(billSignMap.getSignFlag()));
                mapElement.addElement("remark").addText(StrUtil.nullToEmpty(billSignMap.getRemark()));
                mapElement.addElement("dshnrCd").addText(StrUtil.nullToEmpty(billSignMap.getDshnrCd()));
                mapElement.addElement("guaranteeAdr").addText(StrUtil.nullToEmpty(billSignMap.getGuaranteeAdr()));
//                listItem.addElement("transId").addText(StrUtil.nullToEmpty(billSignMap.getTransId()));
//                //将billSignMap里的其他字段均赋值到listItem中
//                listItem.addElement("transCode").addText(StrUtil.nullToEmpty(billSignMap.getTransCode()));
//                listItem.addElement("signFlag").addText(StrUtil.nullToEmpty(billSignMap.getSignFlag()));
//                listItem.addElement("remark").addText(StrUtil.nullToEmpty(billSignMap.getRemark()));
//                listItem.addElement("dshnrCd").addText(StrUtil.nullToEmpty(billSignMap.getDshnrCd()));
//                listItem.addElement("guaranteeAdr").addText(StrUtil.nullToEmpty(billSignMap.getGuaranteeAdr()));
            }

            // 将XML转换为字符串
            StringWriter reqXml = new StringWriter();
            OutputFormat format = OutputFormat.createPrettyPrint();
            XMLWriter xmlWriter = new XMLWriter(reqXml, format);
            xmlWriter.write(document);
            xmlWriter.close();
            log.info("入参转化为xml：{}", printLogXml(reqXml.toString()));
            String res = HttpRequest.post(bankApiUrl)
                    .header("Content-type", "application/x-NS-BDES")
                    .body(reqXml.toString())
                    .timeout(60000)
                    .execute().body();
            log.info("通用签收(B2eNbsDraftSignUp)返回结果：{}", res);
            // 解析xml，映射到实体类
            SAXReader reader = new SAXReader();
            Document resDocument = reader.read(new StringReader(res));


            //解析 responseHeader
            Element responseHeader = resDocument.getRootElement().element("responseHeader");
            //header xml转实体类
            ResponseHeader header = BankApiXmlUtil.getResponseHeader(responseHeader);
            b2eNbsDraftSignUpResponse.setResponseHeader(header);

            // 解析 xDataBody
            B2eNbsDraftSignUpResponse.B2eNbsDraftSignUpResBody resBody = new B2eNbsDraftSignUpResponse.B2eNbsDraftSignUpResBody();
            Element dataBody = resDocument.getRootElement().element("xDataBody");

            if (dataBody != null) {
                // 赋值服务 ID 和交易 ID
                resBody.setSvrId(dataBody.elementText("svrId"));
                resBody.setTrnId(dataBody.elementText("trnId"));
                resBody.setInsId(dataBody.elementText("insId"));
                List<B2eNbsDraftSignUpResponse.BillSignResultMap> list = new ArrayList<>();
                Element listElement = dataBody.element("List");
                if (listElement != null) {
                    for (Element mapElement : listElement.elements("Map")) {
                        B2eNbsDraftSignUpResponse.BillSignResultMap billSignResultMap = new B2eNbsDraftSignUpResponse.BillSignResultMap();
                        billSignResultMap.setTransId(mapElement.elementText("transId"));
                        billSignResultMap.setRetCode(mapElement.elementText("retCode"));
                        billSignResultMap.setRetMsg(mapElement.elementText("retMsg"));
                        list.add(billSignResultMap);
                    }
                }
                resBody.setList(list);
                // 将 BillInfo 对象设置到 XDataBody 中
                b2eNbsDraftSignUpResponse.setXDataBody(resBody);
            }

            //异步保存银行操作记录
            insertBankApiRecord(reqXml.toString(), res, BankApiType.B2ENBS_DRAFT_SIGNUP.getCode(), body.getTrnId(), "");
            return b2eNbsDraftSignUpResponse;
        } catch (Exception e) {
            log.error("票据详细信息查询(B2eNbsDraftDetail)接口调用失败", e);
        }
        //如果响应头为null，则说明程序错误
        b2eNbsDraftSignUpResponse.checkAndSetResponseHeader(b2eNbsDraftSignUpResponse, "通用签收(B2eNbsDraftSignUp)程序执行失败！");
        return b2eNbsDraftSignUpResponse;

    }

    @Override
    public B2eNbsDraftDiscountResponse b2eNbsDraftDiscount(B2eNbsDraftDiscountReqBody b2eNbsDraftDiscountReqBody) {
        log.info("贴现申请(B2eNbsDraftDiscount)，请求入参：{}", JSON.toJSONString(b2eNbsDraftDiscountReqBody));

        B2eNbsDraftDiscountResponse b2eNbsDraftDiscountResponse = new B2eNbsDraftDiscountResponse();

        try {
            // 使用Dom4j构建XML
            Document document = DocumentHelper.createDocument();

            // 封装请求头xml
            Element root = BankApiXmlUtil.createXmlRootAndHead(document, BankApiType.B2ENBS_DRAFT_DISCOUNT.getTrnCode(), bankApiShouClientId, bankApiShouUserId, bankApiShouUserPswd);

            // 封装请求体
            Element xDataBody = root.addElement("xDataBody");
            xDataBody.addElement("trnId").addText(StrUtil.nullToEmpty(b2eNbsDraftDiscountReqBody.getTrnId()));
            xDataBody.addElement("insId").addText(StrUtil.nullToEmpty(b2eNbsDraftDiscountReqBody.getInsId()));
            xDataBody.addElement("custAccount").addText(StrUtil.nullToEmpty(b2eNbsDraftDiscountReqBody.getCustAccount()));
            xDataBody.addElement("discountBank").addText(StrUtil.nullToEmpty(b2eNbsDraftDiscountReqBody.getDiscountBank()));
            xDataBody.addElement("receiverAcctNo").addText(StrUtil.nullToEmpty(b2eNbsDraftDiscountReqBody.getReceiverAcctNo()));
            xDataBody.addElement("receiverBank").addText(StrUtil.nullToEmpty(b2eNbsDraftDiscountReqBody.getReceiverBank()));
            xDataBody.addElement("receiverBankCode").addText(StrUtil.nullToEmpty(b2eNbsDraftDiscountReqBody.getReceiverBankCode()));
            xDataBody.addElement("receiverBankNo").addText(StrUtil.nullToEmpty(b2eNbsDraftDiscountReqBody.getReceiverBankNo()));
            xDataBody.addElement("aoAcctNo").addText(StrUtil.nullToEmpty(b2eNbsDraftDiscountReqBody.getAoAcctNo()));
            xDataBody.addElement("aoAcctName").addText(StrUtil.nullToEmpty(b2eNbsDraftDiscountReqBody.getAoAcctName()));
            xDataBody.addElement("aoBankNo").addText(StrUtil.nullToEmpty(b2eNbsDraftDiscountReqBody.getAoBankNo()));
            xDataBody.addElement("aoAcctBankCode").addText(StrUtil.nullToEmpty(b2eNbsDraftDiscountReqBody.getAoAcctBankCode()));
            xDataBody.addElement("banEndrsmtMark").addText(StrUtil.nullToEmpty(b2eNbsDraftDiscountReqBody.getBanEndrsmtMark()));
            xDataBody.addElement("discType").addText(StrUtil.nullToEmpty(b2eNbsDraftDiscountReqBody.getDiscType()));
            xDataBody.addElement("sttlmMk").addText(StrUtil.nullToEmpty(b2eNbsDraftDiscountReqBody.getSttlmMk()));
            xDataBody.addElement("discRate").addText(StrUtil.nullToEmpty(b2eNbsDraftDiscountReqBody.getDiscRate()));
            xDataBody.addElement("discDt").addText(StrUtil.nullToEmpty(b2eNbsDraftDiscountReqBody.getDiscDt()));
            xDataBody.addElement("payType").addText(StrUtil.nullToEmpty(b2eNbsDraftDiscountReqBody.getPayType()));
            xDataBody.addElement("buyPayPcet").addText(StrUtil.nullToEmpty(b2eNbsDraftDiscountReqBody.getBuyPayPcet()));
            xDataBody.addElement("remark").addText(StrUtil.nullToEmpty(b2eNbsDraftDiscountReqBody.getRemark()));
            xDataBody.addElement("redeemOpenDt").addText(StrUtil.nullToEmpty(b2eNbsDraftDiscountReqBody.getRedeemOpenDt()));
            xDataBody.addElement("redeemDueDt").addText(StrUtil.nullToEmpty(b2eNbsDraftDiscountReqBody.getRedeemDueDt()));
            xDataBody.addElement("redeemRate").addText(StrUtil.nullToEmpty(b2eNbsDraftDiscountReqBody.getRedeemRate()));
            xDataBody.addElement("redeemRateType").addText(StrUtil.nullToEmpty(b2eNbsDraftDiscountReqBody.getRedeemRateType()));
            xDataBody.addElement("redeemAmt").addText(StrUtil.nullToEmpty(b2eNbsDraftDiscountReqBody.getRedeemAmt()));
            Element listItem = xDataBody.addElement("List");
            // List<BillInfo> list
            for (B2eNbsDraftDiscountReqBody.BillDiscountMap billInfo : b2eNbsDraftDiscountReqBody.getList()) {
                Element mapItem = listItem.addElement("Map");
                mapItem.addElement("billNo").addText(StrUtil.nullToEmpty(billInfo.getBillNo()));
                mapItem.addElement("billRangeStart").addText(StrUtil.nullToEmpty(billInfo.getBillRangeStart()));
                mapItem.addElement("billRangeEnd").addText(StrUtil.nullToEmpty(billInfo.getBillRangeEnd()));
                mapItem.addElement("dueDt").addText(StrUtil.nullToEmpty(billInfo.getDueDt()));
                mapItem.addElement("transAmt").addText(StrUtil.nullToEmpty(billInfo.getTransAmt()));
            }

            // 将XML转换为字符串
            StringWriter reqXml = new StringWriter();
            OutputFormat format = OutputFormat.createPrettyPrint();
            XMLWriter xmlWriter = new XMLWriter(reqXml, format);
            xmlWriter.write(document);
            xmlWriter.close();
            log.info("入参转化为xml：{}", printLogXml(reqXml.toString()));

            String res = HttpRequest.post(bankApiUrl)
                    .header("Content-type", "application/x-NS-BDES")
                    .body(reqXml.toString())
                    .timeout(60000)
                    .execute().body();
            log.info("贴现申请(B2eNbsDraftDiscount)返回结果：{}", res);

            // 解析xml，映射到实体类
            SAXReader reader = new SAXReader();
            Document resDocument = reader.read(new StringReader(res));

            // 解析 responseHeader
            Element responseHeader = resDocument.getRootElement().element("responseHeader");
            // header xml转实体类
            ResponseHeader header = BankApiXmlUtil.getResponseHeader(responseHeader);
            b2eNbsDraftDiscountResponse.setResponseHeader(header);

            // 解析 xDataBody
            B2eNbsDraftDiscountResponse.B2eNbsDraftDiscountResBody resBody = new B2eNbsDraftDiscountResponse.B2eNbsDraftDiscountResBody();
            Element dataBody = resDocument.getRootElement().element("xDataBody");

            if (dataBody != null) {
                // 赋值服务 ID 和交易 ID
                resBody.setSvrId(dataBody.elementText("svrId"));
                resBody.setTrnId(dataBody.elementText("trnId"));
                resBody.setInsId(dataBody.elementText("insId"));

                List<B2eNbsDraftDiscountResponse.BillDiscountResponse> list = new ArrayList<>();
                Element listElement = dataBody.element("List");
                if (listElement != null) {
                    for (Element mapElement : listElement.elements("Map")) {
                        B2eNbsDraftDiscountResponse.BillDiscountResponse billDiscountResult = new B2eNbsDraftDiscountResponse.BillDiscountResponse();
                        billDiscountResult.setBillNo(mapElement.elementText("billNo"));
                        billDiscountResult.setRetCode(mapElement.elementText("retCode"));
                        billDiscountResult.setRetMsg(mapElement.elementText("retMsg"));
                        list.add(billDiscountResult);
                    }
                }
                resBody.setList(list);
                // 将 BillInfo 对象设置到 XDataBody 中
                b2eNbsDraftDiscountResponse.setXDataBody(resBody);
            }

            // 异步保存银行操作记录
            insertBankApiRecord(reqXml.toString(), res, BankApiType.B2ENBS_DRAFT_DISCOUNT.getCode(), b2eNbsDraftDiscountReqBody.getTrnId(), "");
            return b2eNbsDraftDiscountResponse;
        } catch (Exception e) {
            log.error("贴现申请(B2eNbsDraftDiscount)接口调用失败", e);
        }

        // 如果响应头为null，则说明程序错误
        b2eNbsDraftDiscountResponse.checkAndSetResponseHeader(b2eNbsDraftDiscountResponse, "贴现申请(B2eNbsDraftDiscount)程序执行失败！");
        return b2eNbsDraftDiscountResponse;
    }

    @Override
    public B2eNbsQryDraftTransStatusResponse b2eNbsQryDraftTransStatus(B2eNbsQryDraftTransStatusReqBody body,boolean isShou) {
        log.info("票据交易状态查询(B2eNbsQryDraftTransStatus)，请求入参：{}", JSON.toJSONString(body));

        B2eNbsQryDraftTransStatusResponse response = new B2eNbsQryDraftTransStatusResponse();

        try {
            // 使用Dom4j构建XML
            Document document = DocumentHelper.createDocument();

            // 封装请求头xml
            Element root = null;
            if(isShou){//是否收票方，贴现操作按收票方查
                root= BankApiXmlUtil.createXmlRootAndHead(document, BankApiType.B2ENBS_QRY_PDRAFT_TRANSSTATUS.getTrnCode(), bankApiShouClientId, bankApiShouUserId, bankApiShouUserPswd);
            }else{
                root = BankApiXmlUtil.createXmlRootAndHead(document, BankApiType.B2ENBS_QRY_PDRAFT_TRANSSTATUS.getTrnCode(), bankApiClientId, bankApiUserId, bankApiUserPswd);
            }

            // 封装请求体
            Element xDataBody = root.addElement("xDataBody");
            xDataBody.addElement("trnId").addText(StrUtil.nullToEmpty(body.getTrnId()));
            xDataBody.addElement("custAccount").addText(StrUtil.nullToEmpty(body.getCustAccount()));
            xDataBody.addElement("operType").addText(StrUtil.nullToEmpty(body.getOperType()));
            xDataBody.addElement("insId").addText(StrUtil.nullToEmpty(body.getInsId()));
            xDataBody.addElement("busiStage").addText(StrUtil.nullToEmpty(body.getBusiStage()));
            xDataBody.addElement("billType").addText(StrUtil.nullToEmpty(body.getBillType()));
            xDataBody.addElement("beginAcptDt").addText(StrUtil.nullToEmpty(body.getBeginAcptDt()));
            xDataBody.addElement("endAcptDt").addText(StrUtil.nullToEmpty(body.getEndAcptDt()));
            xDataBody.addElement("beginEndDate").addText(StrUtil.nullToEmpty(body.getBeginEndDate()));
            xDataBody.addElement("endDate").addText(StrUtil.nullToEmpty(body.getEndDate()));
            xDataBody.addElement("beginApplyDt").addText(StrUtil.nullToEmpty(body.getBeginApplyDt()));
            xDataBody.addElement("endApplyDt").addText(StrUtil.nullToEmpty(body.getEndApplyDt()));
            xDataBody.addElement("billNo").addText(StrUtil.nullToEmpty(body.getBillNo()));
            xDataBody.addElement("pageNo").addText(StrUtil.nullToEmpty(body.getPageNo()));
            xDataBody.addElement("pageSize").addText(StrUtil.nullToEmpty(body.getPageSize()));

            // 将XML转换为字符串
            StringWriter reqXml = new StringWriter();
            OutputFormat format = OutputFormat.createPrettyPrint();
            XMLWriter xmlWriter = new XMLWriter(reqXml, format);
            xmlWriter.write(document);
            xmlWriter.close();
            log.info("入参转化为xml：{}", printLogXml(reqXml.toString()));

            String res = HttpRequest.post(bankApiUrl)
                    .header("Content-type", "application/x-NS-BDES")
                    .body(reqXml.toString())
                    .timeout(60000)
                    .execute().body();
            log.info("票据交易状态查询(B2eNbsQryDraftTransStatus)返回结果：{}", res);

            // 解析xml，映射到实体类
            SAXReader reader = new SAXReader();
            Document resDocument = reader.read(new StringReader(res));

            // 解析 responseHeader
            Element responseHeader = resDocument.getRootElement().element("responseHeader");
            // header xml转实体类
            ResponseHeader header = BankApiXmlUtil.getResponseHeader(responseHeader);
            response.setResponseHeader(header);

            // 解析 xDataBody
            B2eNbsQryDraftTransStatusResponse.B2eNbsQryDraftTransStatusResBody resBody = new B2eNbsQryDraftTransStatusResponse.B2eNbsQryDraftTransStatusResBody();
            Element dataBody = resDocument.getRootElement().element("xDataBody");

            if (dataBody != null) {
                // 赋值服务 ID 和交易 ID
                resBody.setSvrId(dataBody.elementText("svrId"));
                resBody.setTrnId(dataBody.elementText("trnId"));
                resBody.setTotal(dataBody.elementText("total") != null ? Integer.parseInt(dataBody.elementText("total")) : 0);

                List<B2eNbsQryDraftTransStatusResponse.BillInfo> list = new ArrayList<>();
                Element listElement = dataBody.element("List");
                if (listElement != null) {
                    for (Element mapElement : listElement.elements("Map")) {
                        B2eNbsQryDraftTransStatusResponse.BillInfo billInfo = new B2eNbsQryDraftTransStatusResponse.BillInfo();
                        billInfo.setBillNo(mapElement.elementText("billNo"));
                        billInfo.setBillType(mapElement.elementText("billType"));
                        billInfo.setIsAllowSplitBill(mapElement.elementText("isAllowSplitBill"));
                        billInfo.setRemitDt(mapElement.elementText("remitDt"));
                        billInfo.setDueDt(mapElement.elementText("dueDt"));
                        billInfo.setAcptDt(mapElement.elementText("acptDt"));
                        billInfo.setBillMoney(mapElement.elementText("billMoney"));
                        billInfo.setDrwrName(mapElement.elementText("drwrName"));
                        billInfo.setDrwrAcctNo(mapElement.elementText("drwrAcctNo"));
                        billInfo.setDrwrAcctName(mapElement.elementText("drwrAcctName"));
                        billInfo.setDrwrBankName(mapElement.elementText("drwrBankName"));
                        billInfo.setDrwrBankNo(mapElement.elementText("drwrBankNo"));
                        billInfo.setPyeeName(mapElement.elementText("pyeeName"));
                        billInfo.setPyeeAcctNo(mapElement.elementText("pyeeAcctNo"));
                        billInfo.setPyeeAcctName(mapElement.elementText("pyeeAcctName"));
                        billInfo.setPyeeBankName(mapElement.elementText("pyeeBankName"));
                        billInfo.setPyeeBankNo(mapElement.elementText("pyeeBankNo"));
                        billInfo.setAcptName(mapElement.elementText("acptName"));
                        billInfo.setAcptAcctNo(mapElement.elementText("acptAcctNo"));
                        billInfo.setAcptAcctName(mapElement.elementText("acptAcctName"));
                        billInfo.setAcptBankName(mapElement.elementText("acptBankName"));
                        billInfo.setAcptBankNo(mapElement.elementText("acptBankNo"));
                        billInfo.setBillRangeStart(mapElement.elementText("billRangeStart"));
                        billInfo.setBillRangeEnd(mapElement.elementText("billRangeEnd"));
                        billInfo.setHldrBanEndrsmtMark(mapElement.elementText("hldrBanEndrsmtMark"));
                        billInfo.setBillStatus(mapElement.elementText("billStatus"));
                        billInfo.setStatusCode(mapElement.elementText("statusCode"));
                        billInfo.setTransBusiStatus(mapElement.elementText("transBusiStatus"));
                        billInfo.setTransBusiStatusCode(mapElement.elementText("transBusiStatusCode"));
                        billInfo.setTransFromName(mapElement.elementText("transFromName"));
                        billInfo.setTransFromAcctNo(mapElement.elementText("transFromAcctNo"));
                        billInfo.setTransFromBankNo(mapElement.elementText("transFromBankNo"));
                        billInfo.setTransApplDt(mapElement.elementText("transApplDt"));
                        billInfo.setTransFromRemark(mapElement.elementText("transFromRemark"));
                        billInfo.setTranstoName(mapElement.elementText("transtoName"));
                        billInfo.setTranstoAcctNo(mapElement.elementText("transtoAcctNo"));
                        billInfo.setTranstoBankNo(mapElement.elementText("transtoBankNo"));
                        billInfo.setTransToRemark(mapElement.elementText("transToRemark"));
                        billInfo.setOnlineMark(mapElement.elementText("onlineMark"));
                        billInfo.setBusiStage(mapElement.elementText("busiStage"));
                        billInfo.setErrorMsg(mapElement.elementText("errorMsg"));
                        billInfo.setTransId(mapElement.elementText("transId"));
                        list.add(billInfo);
                    }
                }
                resBody.setList(list);
                // 将 BillInfo 对象设置到 XDataBody 中
                response.setXDataBody(resBody);
            }

            // 异步保存银行操作记录
            insertBankApiRecord(reqXml.toString(), res, BankApiType.B2ENBS_QRY_PDRAFT_TRANSSTATUS.getCode(), body.getTrnId(), "");
            return response;
        } catch (Exception e) {
            log.error("票据交易状态查询(B2eNbsQryDraftTransStatus)接口调用失败", e);
        }

        // 如果响应头为null，则说明程序错误
        response.checkAndSetResponseHeader(response, "票据交易状态查询(B2eNbsQryDraftTransStatus)程序执行失败！");
        return response;
    }

    @Override
    public B2eQueryTaskNoRes b2eQueryTaskNo(B2eQueryTaskNoReq b2eQueryTaskNoReq) {

        b2eQueryTaskNoReq.setTrnId(IdUtil.simpleUUID());

        B2eQueryTaskNoApiReq apiRequest = new B2eQueryTaskNoApiReq(b2eQueryTaskNoReq);

        B2eQueryTaskNoRes b2eQueryTaskNoRes = b2eBankApiService.callRemote(apiRequest, B2eQueryTaskNoRes.class);

        if (b2eQueryTaskNoRes.isFail()) {
            throw new AcceptanceBillException("银承中收减免审批单号查询失败：" + b2eQueryTaskNoRes.getMessage());
        }

        return b2eQueryTaskNoRes;
    }

    /**
     * 异步记录接口调用记录
     *
     * @param requestBody
     * @param responseBody
     * @param apiType
     * @param trnId
     * @param insId
     */
    private void insertBankApiRecord(String requestBody, String responseBody, Integer apiType, String trnId, String insId) {
        BankThreadPool.submit(() -> {
            MinshengBankApiRecord minshengBankApiRecord = new MinshengBankApiRecord();
            try {
                minshengBankApiRecord.setRequestBody(requestBody);
                //responseBody截位1000,
                String response = StrUtil.sub(responseBody, 0, 1000);
                minshengBankApiRecord.setResponseBody(StrUtil.nullToEmpty(response));
                minshengBankApiRecord.setApiType(apiType);
                minshengBankApiRecord.setTrnId(StrUtil.nullToEmpty(trnId));
                minshengBankApiRecord.setInsId(StrUtil.nullToEmpty(insId));
                minshengBankApiRecordMapper.insertRecord(minshengBankApiRecord);
            } catch (Exception e) {
                // 打印异常信息，
                log.error("异步保存民生银行操作记录失败", e);
            }
        });
    }


    private String printLogXml(String xml) {
        return xml.replaceAll("<userPswd>[^<]*</userPswd>", "<userPswd>********</userPswd>");
    }


}
