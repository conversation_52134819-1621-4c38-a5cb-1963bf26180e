package com.vedeng.todolist.dto;

import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
public class PurchaseRankingDto {

    private Integer type;

    private String skuNo;

    private Long dealTime;

    private String managerAsssistantIdStr;

    private String assignmentAsssistantIdStr;

    private List<Integer> managerAsssistantIdList;

    private List<Integer> assignmentAsssistantIdList;

    public Set<Integer> getAllAsssistantIdSet(){

        Set<Integer> asssistantIdSet = new HashSet<>();

        if(CollectionUtils.isNotEmpty(managerAsssistantIdList)){
            asssistantIdSet.addAll(managerAsssistantIdList);
        }

        if(CollectionUtils.isNotEmpty(assignmentAsssistantIdList)){
            asssistantIdSet.addAll(assignmentAsssistantIdList);
        }

        return asssistantIdSet;
    }

}
