package com.vedeng.mobile.system.mapper;

import com.vedeng.mobile.system.model.BussinessChanceEntity;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @description ${end}
 * @date 2022/7/13 10:21
 **/
@Repository("mobileBussinessChanceMapper")
public interface MobileBussinessChanceMapper {


    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(BussinessChanceEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(BussinessChanceEntity record);


    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(BussinessChanceEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(BussinessChanceEntity record);


}