package com.vedeng.erp.kingdee.mapstruct;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseReceiptEntity;
import com.vedeng.erp.kingdee.dto.KingDeePurchaseReceiptDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeePurchaseReceiptDto;
import org.mapstruct.*;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购入库单  贝登dto 转 金蝶 command
 * @date
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,builder = @Builder(disableBuilder = true))
public interface KingDeePurchaseReceiptConvertor extends BaseMapStruct<KingDeePurchaseReceiptEntity, KingDeePurchaseReceiptDto> {
	
	/**
     * DTO转Entity
     *
     * @param dto
     * @return
     */
    @Mapping(target = "FInStockEntry", source = "FInStockEntry", qualifiedByName = "expensesToJsonArray")
    @Override
    KingDeePurchaseReceiptEntity toEntity(KingDeePurchaseReceiptDto dto);

    /**
     * Entity转DTO
     *
     * @param entity
     * @return
     */
    @Mapping(target = "FInStockEntry", source = "FInStockEntry", qualifiedByName = "expensesJsonArrayToList")
    @Override
    KingDeePurchaseReceiptDto toDto(KingDeePurchaseReceiptEntity entity);
	
    
    /**
     * entity 中JSONArray 转 原对象
     *
     * @param array JSONArray
     * @return List<KingDeePayBillDto> dto中的对象
     */
    @Named("expensesJsonArrayToList")
    default List<KingDeePurchaseReceiptDetailDto> entryJsonArrayToList(JSONArray array) {
        if (CollUtil.isEmpty(array)) {
            return Collections.emptyList();
        }
        return array.toJavaList(KingDeePurchaseReceiptDetailDto.class);
    }

    /**
     * dto 原对象中 转 JSONArray
     *
     * @param source 对象
     * @return JSONArray JSONArray
     */
    @Named("expensesToJsonArray")
    default JSONArray entryListToJsonArray(List<KingDeePurchaseReceiptDetailDto> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSONArray.parseArray(JSON.toJSONString(source));
    }
    
}
