<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.goods.dao.GoodsExpirationWarnMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.goods.model.GoodsExpirationWarn" >
    <id column="GOODS_EXPIRATION_WARN_ID" property="goodsExpirationWarnId" jdbcType="INTEGER" />
    <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
    <result column="GOODS_ID" property="goodsId" jdbcType="INTEGER" />
    <result column="DAY" property="day" jdbcType="INTEGER" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    GOODS_EXPIRATION_WARN_ID, COMPANY_ID, GOODS_ID, DAY, ADD_TIME, CREATOR, MOD_TIME, 
    UPDATER
  </sql>
  <select id="selectByGoodsId" resultType="com.vedeng.goods.model.vo.GoodsExpirationWarnVo" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_GOODS_EXPIRATION_WARN
    where 
    GOODS_ID = #{goodsId,jdbcType=INTEGER}
    and
    COMPANY_ID = #{companyId,jdbcType=INTEGER}
  </select>
  <insert id="insert" parameterType="com.vedeng.goods.model.GoodsExpirationWarn" >
    insert into T_GOODS_EXPIRATION_WARN
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="goodsExpirationWarnId != null" >
        GOODS_EXPIRATION_WARN_ID,
      </if>
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      <if test="goodsId != null" >
        GOODS_ID,
      </if>
      <if test="day != null" >
        DAY,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="goodsExpirationWarnId != null" >
        #{goodsExpirationWarnId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null" >
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="day != null" >
        #{day,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByGoodsId" parameterType="com.vedeng.goods.model.GoodsExpirationWarn" >
    update T_GOODS_EXPIRATION_WARN
    <set >
      <if test="day != null" >
        DAY = #{day,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where GOODS_ID = #{goodsId,jdbcType=INTEGER}
  </update>
</mapper>