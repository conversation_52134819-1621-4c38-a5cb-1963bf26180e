package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @description: 采购入库单
 * @author: yana.jiang
 * @date: 2022/11/10
 */
@Data
@Getter
@Setter

public class KingDeePurchaseReceiptCommand {

    /**
     * 单据内码
     */
    private String fId;
    /**
     * 单据类型
     */
    private KingDeeNumberCommand fBillTypeId = new KingDeeNumberCommand();
    /**
     * 单据编号
     */
    private String fBillNo;
    /**
     * 贝登单据头ID
     */
    private String F_QZOK_BDDJTID;
    /**
     * 单据日期
     */
    private String fDate;
    /**
     * 库存组织
     */
    private KingDeeNumberCommand fStockOrgId = new KingDeeNumberCommand();
    /**
     * 供应商
     */
    private KingDeeNumberCommand fSupplierId = new KingDeeNumberCommand();
    /**
     * fInStockEntry
     */
    private List<KingDeePurchaseReceiptDetailCommand> fInStockEntry;

    @Data
    public static class KingDeePurchaseReceiptDetailCommand {

        /**
         * 物料
         */
        private KingDeeNumberCommand fMaterialId = new KingDeeNumberCommand();
        /**
         * 实发数量
         */
        private String fRealQty;
        /**
         * 含税单价
         */
        private String fTaxPrice;
        /**
         * 税率%
         */
        private String fEntryTaxRate;
        /**
         * 仓库
         */
        private KingDeeNumberCommand fStockId = new KingDeeNumberCommand();
        /**
         * 原始订单号
         */
        private String F_QZOK_YSDDH;
        /**
         * 归属业务单号
         */
        private String F_QZOK_GSYWDH;
        /**
         * 业务类型
         */
        private String F_QZOK_YWLX;
        /**
         * 批次号
         */
        private String F_QZOK_PCH;
        /**
         * 序列号
         */
        private String F_QZOK_XLH;
        /**
         * 授权类型
         */
        private String F_QZOK_SQLX;
        /**
         * 是否直发
         */
        private String F_QZOK_SFZF;
        /**
         * 贝登订单行ID
         */
        private String F_QZOK_BDDJHID;
    }
}
