package com.vedeng.erp.kingdee.domain.entity;

import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseEntity;
import com.vedeng.common.mybatis.jbatis.annotation.Column;
import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * KING_DEE_SALE_OUT_STOCK
 *
 * <AUTHOR>
@Getter
@Setter
@Table(name = "KING_DEE_SALE_OUT_STOCK")
public class KingDeeSaleOutStockEntity extends BaseEntity {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer kingDeeSaleOutStockId;

    /**
     * 单据内码
     */
    private String fid;

    /**
     * 单据类型
     */
    private String fBillTypeId;

    /**
     * 单据编号
     */
    private String fBillNo;

    /**
     * 贝登单据头ID
     */
    private String fQzokBddjtid;

    /**
     * 归属部门
     */
    private String fQzokGsbm;

    /**
     * 单据日期
     */
    private String fDate;

    /**
     * 销售组织
     */
    private String fSaleOrgId;

    /**
     * 库存组织
     */
    private String fStockOrgId;

    /**
     * 客户
     */
    private String fCustomerId;

    /**
     * F_ENTITY
     */
    @Column(jdbcType = "VARCHAR")
    private JSONArray fEntity;

}