package com.vedeng.crm.business.quote.domain.entity;

import java.util.Date;
import lombok.Data;

/**
 * 报价需求和商品明细关联表
 */
@Data
public class RQuoteorderNeedsJGoodsEntity {
    /**
     * 主键
     */
    private Long rQuoteorderNeedsJGoodsId;

    /**
     * 报价单ID
     */
    private Integer quoteorderId;

    /**
     * 报价需求表ID
     */
    private Long quoteorderNeedsId;

    /**
     * 报价商品明细ID
     */
    private Integer quoteorderGoodsId;

    /**
     * 是否删除 0否 1是
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 修改时间
     */
    private Date modTime;

    /**
     * 添加人ID
     */
    private Integer creator;

    /**
     * 添加人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Integer updater;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 更新备注
     */
    private String updateRemark;
}
