<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="资质过期列表" scope="application" />
<%@ include file="../../common/common.jsp"%>
<div class="main-container">
    <div class="list-pages-search">
        <form action="${pageContext.request.contextPath}/trader/supplier/qualificationExpiration.do" method="post" id="search">
            <input type="hidden" name="search" value="click">
            <ul>
                <li><label class="infor_name">供应商名称</label> <input type="text"
                                                                        class="input-middle" name="traderSupplierName"
                                                                        id="traderSupplierName"
                                                                        value="${traderSupplierVo.traderSupplierName}"></li>
                <li><label class="infor_name">归属采购</label> <select
                        class="input-middle f_left" name="homePurchasing">
                    <option value="">全部</option>
                    <c:if test="${not empty userList }">
                        <c:forEach items="${userList }" var="user">
                            <option value="${user.userId }"
                                    <c:if test="${traderSupplierVo.homePurchasing eq user.userId }">selected="selected"</c:if>>${user.username }</option>
                        </c:forEach>
                    </c:if>
                </select></li>

                <li>
                    <label class="infor_name">创建时间</label>
                    <div class="infor_name specialinfor" style="display: none;">
                        <select name="timeType" >
                            <option value="1" <c:if test="${traderSupplierVo.timeType eq 1 }">selected="selected"</c:if>>创建时间</option>
                        </select>
                    </div>
                    <input class="Wdate f_left input-smaller96 mr5" type="text" autocomplete="off" onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endTime\')}'})"
                           name="queryStartTime" id="startTime" value=<date:date value ="${traderSupplierVo.startTime} "/>><div class="gang">-</div>
                    <input class="Wdate f_left input-smaller96" type="text" autocomplete="off" onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'startTime\')}'})"
                           name="queryEndTime" id="endTime" value=<date:date value ="${traderSupplierVo.endTime} "/>>
                </li>


                <li><label class="infor_name">审核状态</label> <select
                        class="input-middle f_left" name="traderSupplierStatus">
                    <option value="">全部</option>
                    <option value="3" <c:if test="${traderSupplierVo.traderSupplierStatus eq 3 }">selected="selected"</c:if>>待审核</option>
                    <option value="0" <c:if test="${traderSupplierVo.traderSupplierStatus eq 0 }">selected="selected"</c:if>>审核中</option>
                    <option value="1" <c:if test="${traderSupplierVo.traderSupplierStatus eq 1 }">selected="selected"</c:if>>审核通过</option>
                    <option value="2" <c:if test="${traderSupplierVo.traderSupplierStatus eq 2 }">selected="selected"</c:if>>审核不通过</option>
                </select></li>




            </ul>
            <div class="tcenter">
                <span class="bg-light-blue bt-bg-style bt-small" id="searchSpan" onclick="search();">搜索</span>
                <span class="bt-small bg-light-blue bt-bg-style" onclick="reset();">重置</span>
            </div>
        </form>
    </div>
    <div class="fixdiv">
        <div class="superdiv">
            <table
                    class="table table-bordered table-striped table-condensed table-centered">
                <thead>
                <tr>
                    <th class="sorts">序号</th>
                    <th>供应商名称</th>
                    <th>地区</th>
                    <th>供应商类型</th>
                    <th>纳税人分类</th>
                    <th>创建时间</th>
                    <th>归属采购</th>
                    <th>更新时间</th>
                    <th>审核状态</th>
                </tr>
                </thead>

                <tbody class="employeestate">
                <c:if test="${not empty list}">
                    <c:forEach items="${list}" var="traderSupplierVo"
                               varStatus="status">
                        <tr>
                            <td>${status.count}</td>
                            <td class="text-left">
                                <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewsupplier<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
										"link":"./trader/supplier/baseinfo.do?traderId=${traderSupplierVo.traderId}","title":"供应商信息"}'>${traderSupplierVo.traderSupplierName}</a>
                                    ${traderSupplierVo.verifyStatus eq 0 && fn:contains(traderSupplierVo.verifyUsername, curr_user.username) ?"<font color='red'>[审]</font>":""}
                            </td>
                            <td>${traderSupplierVo.traderSupplierAddress }</td>
                            <td><c:if test="${traderSupplierVo.traderType eq 1}">生产厂家</c:if>
                                <c:if test="${traderSupplierVo.traderType eq 2}">渠道商</c:if></td>
                            <td><c:if test="${traderSupplierVo.taxPayerType eq 1}">一般纳税人</c:if>
                                <c:if test="${traderSupplierVo.taxPayerType eq 2}">小规模纳税人</c:if></td>
                            <td><date:date value="${traderSupplierVo.addTime} " /></td>
                            <td>${traderSupplierVo.personal}</td>
                            <td><date:date value="${traderSupplierVo.modTime} " /></td>
                            <td>
                                <c:if test="${empty traderSupplierVo.verifyStatus or traderSupplierVo.verifyStatus eq 3}">待审核</c:if>
                                <c:if test="${traderSupplierVo.verifyStatus eq 0}">审核中</c:if>
                                <c:if test="${traderSupplierVo.verifyStatus eq 1}">审核通过</c:if>
                                <c:if test="${traderSupplierVo.verifyStatus eq 2}">审核不通过</c:if>
                            </td>
                        </tr>
                    </c:forEach>
                </c:if>
                </tbody>
            </table>
            <c:if test="${empty list }">
                <!-- 查询无结果弹出 -->
                <div class="noresult">查询无结果！请尝试使用其他搜索条件。</div>
            </c:if>
        </div>
    </div>
    <tags:page page="${page}" />
</div>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/supplier/index.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/region/index.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp"%>