<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.repository.mappers.KingDeeStorageOutMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.domain.entity.KingDeeStorageOutEntity">
    <!--@mbg.generated-->
    <!--@Table KING_DEE_STORAGE_OUT-->
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="ID" jdbcType="INTEGER" property="id" />
    <result column="F_ID" jdbcType="VARCHAR" property="fId" />
    <result column="F_BILL_NO" jdbcType="VARCHAR" property="fBillNo" />
    <result column="F_QZOK_BDDJT_ID" jdbcType="VARCHAR" property="fQzokBddjtId" />
    <result column="F_STOCK_DIRECT" jdbcType="VARCHAR" property="fStockDirect" />
    <result column="F_DATE" jdbcType="VARCHAR" property="fDate" />
    <result column="F_BILL_TYPE_ID" jdbcType="VARCHAR" property="fBillTypeId" />
    <result column="F_STOCK_ORG_ID" jdbcType="VARCHAR" property="fStockOrgId" />
    <result column="F_CUST_ID" jdbcType="VARCHAR" property="fCustId" />
    <result column="F_DEPT_ID" jdbcType="VARCHAR" property="fDeptId" />

    <result column="F_ENTITY"
            javaType="com.vedeng.erp.kingdee.dto.KingDeeStorageOutDetailDto"
            property="fEntity"
            typeHandler="com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler"/>

  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, ID, F_ID, F_BILL_NO, 
    F_QZOK_BDDJT_ID, F_STOCK_DIRECT, F_DATE, F_BILL_TYPE_ID, F_STOCK_ORG_ID, F_CUST_ID, 
    F_DEPT_ID, F_ENTITY
  </sql>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeStorageOutEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_STORAGE_OUT (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      F_ID, F_BILL_NO, F_QZOK_BDDJT_ID, 
      F_STOCK_DIRECT, F_DATE, F_BILL_TYPE_ID, 
      F_STOCK_ORG_ID, F_CUST_ID, F_DEPT_ID, 
      F_ENTITY)
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{fId,jdbcType=VARCHAR}, #{fBillNo,jdbcType=VARCHAR}, #{fQzokBddjtId,jdbcType=VARCHAR}, 
      #{fStockDirect,jdbcType=VARCHAR}, #{fDate,jdbcType=VARCHAR}, #{fBillTypeId,jdbcType=VARCHAR}, 
      #{fStockOrgId,jdbcType=VARCHAR}, #{fCustId,jdbcType=VARCHAR}, #{fDeptId,jdbcType=VARCHAR}, 
      #{fEntity,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeStorageOutEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_STORAGE_OUT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="fId != null">
        F_ID,
      </if>
      <if test="fBillNo != null">
        F_BILL_NO,
      </if>
      <if test="fQzokBddjtId != null">
        F_QZOK_BDDJT_ID,
      </if>
      <if test="fStockDirect != null">
        F_STOCK_DIRECT,
      </if>
      <if test="fDate != null">
        F_DATE,
      </if>
      <if test="fBillTypeId != null">
        F_BILL_TYPE_ID,
      </if>
      <if test="fStockOrgId != null">
        F_STOCK_ORG_ID,
      </if>
      <if test="fCustId != null">
        F_CUST_ID,
      </if>
      <if test="fDeptId != null">
        F_DEPT_ID,
      </if>
      <if test="fEntity != null">
        F_ENTITY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fId != null">
        #{fId,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null">
        #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBddjtId != null">
        #{fQzokBddjtId,jdbcType=VARCHAR},
      </if>
      <if test="fStockDirect != null">
        #{fStockDirect,jdbcType=VARCHAR},
      </if>
      <if test="fDate != null">
        #{fDate,jdbcType=VARCHAR},
      </if>
      <if test="fBillTypeId != null">
        #{fBillTypeId,jdbcType=VARCHAR},
      </if>
      <if test="fStockOrgId != null">
        #{fStockOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fCustId != null">
        #{fCustId,jdbcType=VARCHAR},
      </if>
      <if test="fDeptId != null">
        #{fDeptId,jdbcType=VARCHAR},
      </if>
      <if test="fEntity != null">
        #{fEntity,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <insert id="batchInsert" keyColumn="ID" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_STORAGE_OUT
    (ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, F_ID, F_BILL_NO, 
      F_QZOK_BDDJT_ID, F_STOCK_DIRECT, F_DATE, F_BILL_TYPE_ID, F_STOCK_ORG_ID, F_CUST_ID, 
      F_DEPT_ID, F_ENTITY)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, 
        #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR}, 
        #{item.fId,jdbcType=VARCHAR}, #{item.fBillNo,jdbcType=VARCHAR}, #{item.fQzokBddjtId,jdbcType=VARCHAR}, 
        #{item.fStockDirect,jdbcType=VARCHAR}, #{item.fDate,jdbcType=VARCHAR}, #{item.fBillTypeId,jdbcType=VARCHAR}, 
        #{item.fStockOrgId,jdbcType=VARCHAR}, #{item.fCustId,jdbcType=VARCHAR}, #{item.fDeptId,jdbcType=VARCHAR}, 
        #{item.fEntity,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>