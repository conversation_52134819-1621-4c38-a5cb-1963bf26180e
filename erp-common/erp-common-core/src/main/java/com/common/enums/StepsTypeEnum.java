package com.common.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description 进度条状态枚举类
 * @date 2022/10/17 13:49
 **/
@Getter
public enum StepsTypeEnum {
    wait (1,"wait"),
    process(2,"process") ,
    finish (3,"finish"),
    error (4,"error"),
    success(5,"success");

    private final String type;
    private final Integer code;

    StepsTypeEnum(Integer code, String type) {
        this.type = type;
        this.code = code;
    }
}
