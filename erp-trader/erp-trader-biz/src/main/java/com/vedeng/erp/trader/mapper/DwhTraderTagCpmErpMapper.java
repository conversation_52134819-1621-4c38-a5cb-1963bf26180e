package com.vedeng.erp.trader.mapper;

import com.vedeng.erp.trader.domain.entity.DwhTraderTagCpmErpEntity;

import java.util.List;

public interface DwhTraderTagCpmErpMapper {
    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(DwhTraderTagCpmErpEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(DwhTraderTagCpmErpEntity record);

    List<DwhTraderTagCpmErpEntity> findByAll(DwhTraderTagCpmErpEntity dwhTraderTagCpmErpEntity);



}