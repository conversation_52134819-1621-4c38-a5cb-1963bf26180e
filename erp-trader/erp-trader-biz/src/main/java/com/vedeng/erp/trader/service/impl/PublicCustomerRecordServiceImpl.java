package com.vedeng.erp.trader.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.vedeng.erp.business.dto.PublicCustomerRecordDto;
import com.vedeng.erp.trader.domain.PublicCustomerRecord;
import com.vedeng.erp.trader.mapper.PublicCustomerRecordMapper;
import com.vedeng.erp.trader.service.PublicCustomerRecordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 客户进入公海记录服务层
 *
 */
@Service
public class PublicCustomerRecordServiceImpl implements PublicCustomerRecordService {
    private static final Logger logger = LoggerFactory.getLogger(PublicCustomerRecordServiceImpl.class);

    @Resource
    private PublicCustomerRecordMapper publicCustomerRecordMapper;

    @Override
    public PublicCustomerRecordDto getTopOne(Integer traderId) {
        PublicCustomerRecordDto dto = publicCustomerRecordMapper.getByTraderCustomerId(null,traderId,null);
        return dto;
    }
    
    @Override
    public void updateOrgUserId(List<Long> ids, Integer originUserId) {
        logger.info("更新原归属销售，公海记录id:{},orgUserId：{}", JSONUtil.toJsonStr(ids),originUserId);
        if (CollUtil.isEmpty(ids)){
            return;
        }
    	publicCustomerRecordMapper.updateOrgUserId(ids,originUserId);
    }

    @Override
    public List<PublicCustomerRecordDto> getPublicCustomerList(Integer userId, Integer areaId) {
        List<PublicCustomerRecordDto> publicCustomerList = publicCustomerRecordMapper.getPublicCustomerList(userId,areaId);
        return publicCustomerList;
    }
}
