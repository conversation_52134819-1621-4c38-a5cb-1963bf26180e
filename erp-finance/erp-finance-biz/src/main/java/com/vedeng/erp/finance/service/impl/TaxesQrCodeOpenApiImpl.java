package com.vedeng.erp.finance.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.vedeng.infrastructure.taxes.base.ITaxesParam;
import com.vedeng.infrastructure.taxes.base.ITaxesResult;
import com.vedeng.erp.finance.domain.dto.SaleInvoiceQrResponseDto;
import com.vedeng.erp.finance.service.AbstractTaxesOpenApiHandler;
import com.vedeng.infrastructure.taxes.domain.TaxesOpenApiResult;
import com.vedeng.infrastructure.taxes.enums.TaxesReturnCodeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;

import java.util.Objects;

/**
 * 销项票-二维码链接
 */
@Service
public class TaxesQrCodeOpenApiImpl extends AbstractTaxesOpenApiHandler {

    /**
     * 后置处理
     * @param taxesParam
     * @param executeResult
     * @return
     */
    @Override
    protected ITaxesResult afterExecute(ITaxesParam taxesParam, ITaxesResult executeResult) {
        TaxesOpenApiResult openapi = (TaxesOpenApiResult) executeResult;
        String decodeStr = new String(Base64Utils.decodeFromString(openapi.getData()));
        SaleInvoiceQrResponseDto taxesResult = JSONObject.parseObject(decodeStr, SaleInvoiceQrResponseDto.class);
        if (Objects.isNull(taxesResult)){
            taxesResult =  new SaleInvoiceQrResponseDto();
        }
        TaxesOpenApiResult.ReturnInfo return_info = openapi.getReturn_info();
        taxesResult.setReturnCode(return_info.getReturn_code());
        taxesResult.setReturnMessage(return_info.getReturn_message());
        if (TaxesReturnCodeEnum.SUCCESS.getCode().equals(return_info.getReturn_code()) && StringUtils.isNotBlank(taxesResult.getData())){
            taxesResult.setIsSuccess(Boolean.TRUE);
        }
        return taxesResult;
    }

}
