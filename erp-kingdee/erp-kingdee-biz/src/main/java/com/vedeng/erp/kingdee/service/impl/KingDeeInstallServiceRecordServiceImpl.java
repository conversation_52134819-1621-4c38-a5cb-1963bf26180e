package com.vedeng.erp.kingdee.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInItemDto;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInItemDtoMapper;
import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeeInstallServiceRecordCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeInstallServiceRecordEntity;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.dto.result.KingDeeSaleOutStockQueryResultDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeStorageInQueryResultDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeInstallServiceRecordCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeInstallServiceRecordConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeInstallServiceRecordRepository;
import com.vedeng.erp.kingdee.service.KingDeeInstallServiceRecordApiService;
import com.vedeng.erp.kingdee.service.KingDeeInstallServiceRecordService;
import com.vedeng.erp.kingdee.service.KingDeeSaleOutStockService;
import com.vedeng.erp.kingdee.service.KingDeeStorageInService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryExtParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryFilterDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 安调记录推送金蝶
 * @date 2023/02/21 15:00
 **/
@Service
@Slf4j
public class KingDeeInstallServiceRecordServiceImpl extends KingDeeBaseServiceImpl<
        KingDeeInstallServiceRecordEntity,
        KingDeeInstallServiceRecordDto,
        KingDeeInstallServiceRecordCommand,
        KingDeeInstallServiceRecordRepository,
        KingDeeInstallServiceRecordConvertor,
        KingDeeInstallServiceRecordCommandConvertor>
        implements KingDeeInstallServiceRecordService, KingDeeInstallServiceRecordApiService {

    @Autowired
    private KingDeeSaleOutStockService kingDeeSaleOutStockService;
    @Autowired
    private KingDeeStorageInService kingDeeStorageInService;
    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;
    @Autowired
    private BatchWarehouseGoodsOutInDtoMapper batchWarehouseGoodsOutInDtoMapper;


//    @Override
//    @Transactional(rollbackFor = Throwable.class)
//    public void savePost(Object... objects) {
//        // 安调新增后同步修改金蝶出库单
//        KingDeeInstallServiceRecordDto d = getD(objects);
//        log.info("安调新增后同步修改金蝶出库单{}", JSON.toJSONString(d));
//
//        // 根据erp出库单号获取具体是换货出库 销售出库
//        List<BatchWarehouseGoodsOutInItemDto> batchWarehouseGoodsOutInItemDto = batchWarehouseGoodsOutInItemDtoMapper.findByOutInNoAndBarcodeFactory(d.getFQzokCkdh(), d.getFQzokXlh());
//
//        if (CollUtil.isNotEmpty(batchWarehouseGoodsOutInItemDto)) {
//            BatchWarehouseGoodsOutInItemDto dto = CollUtil.getFirst(batchWarehouseGoodsOutInItemDto);
//            List<BatchWarehouseGoodsOutInDto> byOutInNo = batchWarehouseGoodsOutInDtoMapper.findByOutInNo(dto.getOutInNo());
//            if (CollUtil.isEmpty(byOutInNo)) {
//                log.error("安调新增后同步修改金蝶出库单异常，找不到出库单数据{}", JSON.toJSONString(d));
//                throw new KingDeeException("安调新增后同步修改金蝶出库单异常，找不到出库单数据");
//            }
//            BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto = CollUtil.getFirst(byOutInNo);
//            if (dto.getOperateType().equals(KingDeeConstant.TWO)) {
//                List<KingDeeSaleOutStockQueryResultDto> kingDeeSaleOutStock = kingDeeSaleOutStockService.getKingDeeSaleOutStock(
//                        batchWarehouseGoodsOutInDto.getWarehouseGoodsOutInId().toString(),
//                        dto.getWarehouseGoodsOutInDetailId().toString());
//
//                if (CollUtil.isNotEmpty(kingDeeSaleOutStock)) {
//                    KingDeeSaleOutStockQueryResultDto kingDeeSaleOutStockQueryResultDto = CollUtil.getFirst(kingDeeSaleOutStock);
//                    if (StrUtil.isBlank(kingDeeSaleOutStockQueryResultDto.getF_QZOK_XLH().trim())) {
//                        log.info("开始修改金蝶销售出库单{}", JSON.toJSONString(kingDeeSaleOutStockQueryResultDto));
//                        KingDeeSaleOutStockDto kingDeeSaleOutStockDto = new KingDeeSaleOutStockDto();
//                        kingDeeSaleOutStockDto.setFid(kingDeeSaleOutStockQueryResultDto.getFID());
//                        kingDeeSaleOutStockDto.setF_qzok_bddjtid(batchWarehouseGoodsOutInDto.getWarehouseGoodsOutInId().toString());
//                        KingDeeSaleOutStockDetailDto stockDetailDto = new KingDeeSaleOutStockDetailDto();
//                        stockDetailDto.setFEntryId(kingDeeSaleOutStockQueryResultDto.getFEntity_FENTRYID());
//                        stockDetailDto.setF_QZOK_XLH(d.getFQzokXlh());
//                        kingDeeSaleOutStockDto.getFEntity().add(stockDetailDto);
//                        kingDeeSaleOutStockService.update(kingDeeSaleOutStockDto);
//                    }
//                }
//
//            }
//
//            if (dto.getOperateType().equals(KingDeeConstant.FOUR)) {
//                List<KingDeeStorageInQueryResultDto> kingDeeStorageOut = kingDeeStorageInService.getKingDeeStorageIn(
//                        batchWarehouseGoodsOutInDto.getWarehouseGoodsOutInId().toString(),
//                        dto.getWarehouseGoodsOutInDetailId().toString());
//
//                if (CollUtil.isNotEmpty(kingDeeStorageOut)) {
//                    KingDeeStorageInQueryResultDto kingDeeStorageInQueryResultDto = CollUtil.getFirst(kingDeeStorageOut);
//                    if (StrUtil.isBlank(kingDeeStorageInQueryResultDto.getF_QZOK_XLH().trim())) {
//                        log.info("开始修改金蝶销售换货出库单{}", JSON.toJSONString(kingDeeStorageInQueryResultDto));
//                        KingDeeStorageInDto kingDeeStorageInDto = new KingDeeStorageInDto();
//                        kingDeeStorageInDto.setFId(kingDeeStorageInQueryResultDto.getFID());
//                        kingDeeStorageInDto.setFBillNo(dto.getOutInNo());
//                        KingDeeStorageInDetailDto stockDetailDto = new KingDeeStorageInDetailDto();
//                        stockDetailDto.setFEntryId(kingDeeStorageInQueryResultDto.getFEntity_FENTRYID());
//                        stockDetailDto.setFQzokXlh(d.getFQzokXlh());
//                        kingDeeStorageInDto.getFEntity().add(stockDetailDto);
//                        kingDeeStorageInService.update(kingDeeStorageInDto);
//                    }
//                }
//            }
//        }
//    }


    @Override
    public boolean byXlhIsExist(String xlh) {
        KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        queryParam.setFormId(KingDeeFormConstant.QZOK_ATHD);
        List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        queryFilterDtos.add(KingDeeQueryFilterDto.builder()
                .fieldName("f_QZOK_XLH")
                .value(xlh).build());
        queryParam.setFilterString(queryFilterDtos);
        queryParam.setFieldKeys("FID");
        List<Map<String, Object>> list = kingDeeBaseApi.queryReturnMap(queryParam);
        log.info("金蝶安调单返回值{}", list);
        return CollUtil.isNotEmpty(list);
    }
}
