<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.TraderSupplierMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.TraderSupplierEntity">
        <!--@mbg.generated-->
        <!--@Table T_TRADER_SUPPLIER-->
        <id column="TRADER_SUPPLIER_ID" jdbcType="INTEGER" property="traderSupplierId"/>
        <result column="TRADER_ID" jdbcType="INTEGER" property="traderId"/>
        <result column="AMOUNT" jdbcType="DECIMAL" property="amount"/>
        <result column="PERIOD_AMOUNT" jdbcType="DECIMAL" property="periodAmount"/>
        <result column="PERIOD_DAY" jdbcType="INTEGER" property="periodDay"/>
        <result column="IS_ENABLE" jdbcType="BOOLEAN" property="isEnable"/>
        <result column="IS_TOP" jdbcType="BOOLEAN" property="isTop"/>
        <result column="SUPPLY_BRAND" jdbcType="VARCHAR" property="supplyBrand"/>
        <result column="SUPPLY_PRODUCT" jdbcType="VARCHAR" property="supplyProduct"/>
        <result column="GRADE" jdbcType="INTEGER" property="grade"/>
        <result column="DISABLE_TIME" jdbcType="BIGINT" property="disableTime"/>
        <result column="DISABLE_REASON" jdbcType="VARCHAR" property="disableReason"/>
        <result column="COMMENTS" jdbcType="VARCHAR" property="comments"/>
        <result column="BRIEF" jdbcType="VARCHAR" property="brief"/>
        <result column="HOT_TELEPHONE" jdbcType="VARCHAR" property="hotTelephone"/>
        <result column="SERVICE_TELEPHONE" jdbcType="VARCHAR" property="serviceTelephone"/>
        <result column="LOGISTICS_NAME" jdbcType="VARCHAR" property="logisticsName"/>
        <result column="WEBSITE" jdbcType="VARCHAR" property="website"/>
        <result column="ADD_TIME" jdbcType="BIGINT" property="addTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="MOD_TIME" jdbcType="BIGINT" property="modTime"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="WAREHOUSE_AREA_ID" jdbcType="INTEGER" property="warehouseAreaId"/>
        <result column="WAREHOUSE_AREA_IDS" jdbcType="VARCHAR" property="warehouseAreaIds"/>
        <result column="WAREHOUSE_ADDRESS" jdbcType="VARCHAR" property="warehouseAddress"/>
        <result column="TRADER_TYPE" jdbcType="BOOLEAN" property="traderType"/>
        <result column="AFTER_SALE_MANAGER" jdbcType="VARCHAR" property="afterSaleManager"/>
        <result column="INSTALLSERVICE_CONTACTNAME" jdbcType="VARCHAR" property="installserviceContactname"/>
        <result column="INSTALLSERVICE_CONTACTWAY" jdbcType="VARCHAR" property="installserviceContactway"/>
        <result column="TECHNICALDIRECT_CONTACTNAME" jdbcType="VARCHAR" property="technicaldirectContactname"/>
        <result column="TECHNICALDIRECT_CONTACTWAY" jdbcType="VARCHAR" property="technicaldirectContactway"/>
        <result column="MAINTENANCE_CONTACTNAME" jdbcType="VARCHAR" property="maintenanceContactname"/>
        <result column="MAINTENANCE_CONTACTWAY" jdbcType="VARCHAR" property="maintenanceContactway"/>
        <result column="EXCHANGE_CONTACTNAME" jdbcType="VARCHAR" property="exchangeContactname"/>
        <result column="EXCHANGE_CONTACTWAY" jdbcType="VARCHAR" property="exchangeContactway"/>
        <result column="OTHER_CONTACTNAME" jdbcType="VARCHAR" property="otherContactname"/>
        <result column="OTHER_CONTACTWAY" jdbcType="VARCHAR" property="otherContactway"/>
        <result column="VAILD_TIME" jdbcType="TIMESTAMP" property="vaildTime"/>
        <result column="TAX_PAYER_TYPE" jdbcType="TINYINT" property="taxPayerType"/>
    </resultMap>

    <resultMap id="KingDeeResultMap" type="com.vedeng.erp.trader.dto.TraderSupplierDto">
        <id column="TRADER_SUPPLIER_ID" jdbcType="INTEGER" property="traderSupplierId"/>
        <result column="TRADER_ID" jdbcType="INTEGER" property="traderId"/>
        <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName"/>
        <collection property="traderFinanceDtoList" ofType="com.vedeng.erp.trader.dto.TraderFinanceDto">
            <result column="BANK" property="bank" jdbcType="VARCHAR" />
            <result column="BANK_CODE" property="bankCode" jdbcType="VARCHAR" />
            <result column="BANK_ACCOUNT" property="bankAccount" jdbcType="VARCHAR" />
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        TRADER_SUPPLIER_ID,
        TRADER_ID,
        AMOUNT,
        PERIOD_AMOUNT,
        PERIOD_DAY,
        IS_ENABLE,
        IS_TOP,
        SUPPLY_BRAND,
        SUPPLY_PRODUCT,
        GRADE,
        DISABLE_TIME,
        DISABLE_REASON,
        COMMENTS,
        BRIEF,
        HOT_TELEPHONE,
        SERVICE_TELEPHONE,
        LOGISTICS_NAME,
        WEBSITE,
        ADD_TIME,
        CREATOR,
        MOD_TIME,
        UPDATER,
        WAREHOUSE_AREA_ID,
        WAREHOUSE_AREA_IDS,
        WAREHOUSE_ADDRESS,
        TRADER_TYPE,
        AFTER_SALE_MANAGER,
        INSTALLSERVICE_CONTACTNAME,
        INSTALLSERVICE_CONTACTWAY,
        TECHNICALDIRECT_CONTACTNAME,
        TECHNICALDIRECT_CONTACTWAY,
        MAINTENANCE_CONTACTNAME,
        MAINTENANCE_CONTACTWAY,
        EXCHANGE_CONTACTNAME,
        EXCHANGE_CONTACTWAY,
        OTHER_CONTACTNAME,
        OTHER_CONTACTWAY,
        VAILD_TIME,
        TAX_PAYER_TYPE
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from T_TRADER_SUPPLIER
        where TRADER_SUPPLIER_ID = #{traderSupplierId,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete
        from T_TRADER_SUPPLIER
        where TRADER_SUPPLIER_ID = #{traderSupplierId,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="TRADER_SUPPLIER_ID" keyProperty="traderSupplierId"
            parameterType="com.vedeng.erp.trader.domain.entity.TraderSupplierEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_TRADER_SUPPLIER (TRADER_ID, AMOUNT, PERIOD_AMOUNT,
                                       PERIOD_DAY, IS_ENABLE, IS_TOP,
                                       SUPPLY_BRAND, SUPPLY_PRODUCT, GRADE,
                                       DISABLE_TIME, DISABLE_REASON, COMMENTS,
                                       BRIEF, HOT_TELEPHONE, SERVICE_TELEPHONE,
                                       LOGISTICS_NAME, WEBSITE, ADD_TIME,
                                       CREATOR, MOD_TIME, UPDATER,
                                       WAREHOUSE_AREA_ID, WAREHOUSE_AREA_IDS, WAREHOUSE_ADDRESS,
                                       TRADER_TYPE, AFTER_SALE_MANAGER, INSTALLSERVICE_CONTACTNAME,
                                       INSTALLSERVICE_CONTACTWAY, TECHNICALDIRECT_CONTACTNAME,
                                       TECHNICALDIRECT_CONTACTWAY, MAINTENANCE_CONTACTNAME,
                                       MAINTENANCE_CONTACTWAY, EXCHANGE_CONTACTNAME,
                                       EXCHANGE_CONTACTWAY, OTHER_CONTACTNAME, OTHER_CONTACTWAY,
                                       VAILD_TIME, TAX_PAYER_TYPE)
        values (#{traderId,jdbcType=INTEGER}, #{amount,jdbcType=DECIMAL}, #{periodAmount,jdbcType=DECIMAL},
                #{periodDay,jdbcType=INTEGER}, #{isEnable,jdbcType=BOOLEAN}, #{isTop,jdbcType=BOOLEAN},
                #{supplyBrand,jdbcType=VARCHAR}, #{supplyProduct,jdbcType=VARCHAR}, #{grade,jdbcType=INTEGER},
                #{disableTime,jdbcType=BIGINT}, #{disableReason,jdbcType=VARCHAR}, #{comments,jdbcType=VARCHAR},
                #{brief,jdbcType=VARCHAR}, #{hotTelephone,jdbcType=VARCHAR}, #{serviceTelephone,jdbcType=VARCHAR},
                #{logisticsName,jdbcType=VARCHAR}, #{website,jdbcType=VARCHAR}, #{addTime,jdbcType=BIGINT},
                #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER},
                #{warehouseAreaId,jdbcType=INTEGER}, #{warehouseAreaIds,jdbcType=VARCHAR},
                #{warehouseAddress,jdbcType=VARCHAR},
                #{traderType,jdbcType=BOOLEAN}, #{afterSaleManager,jdbcType=VARCHAR},
                #{installserviceContactname,jdbcType=VARCHAR},
                #{installserviceContactway,jdbcType=VARCHAR}, #{technicaldirectContactname,jdbcType=VARCHAR},
                #{technicaldirectContactway,jdbcType=VARCHAR}, #{maintenanceContactname,jdbcType=VARCHAR},
                #{maintenanceContactway,jdbcType=VARCHAR}, #{exchangeContactname,jdbcType=VARCHAR},
                #{exchangeContactway,jdbcType=VARCHAR}, #{otherContactname,jdbcType=VARCHAR},
                #{otherContactway,jdbcType=VARCHAR},
                #{vaildTime,jdbcType=TIMESTAMP}, #{taxPayerType,jdbcType=TINYINT})
    </insert>
    <insert id="insertSelective" keyColumn="TRADER_SUPPLIER_ID" keyProperty="traderSupplierId"
            parameterType="com.vedeng.erp.trader.domain.entity.TraderSupplierEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_TRADER_SUPPLIER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="traderId != null">
                TRADER_ID,
            </if>
            <if test="amount != null">
                AMOUNT,
            </if>
            <if test="periodAmount != null">
                PERIOD_AMOUNT,
            </if>
            <if test="periodDay != null">
                PERIOD_DAY,
            </if>
            <if test="isEnable != null">
                IS_ENABLE,
            </if>
            <if test="isTop != null">
                IS_TOP,
            </if>
            <if test="supplyBrand != null">
                SUPPLY_BRAND,
            </if>
            <if test="supplyProduct != null">
                SUPPLY_PRODUCT,
            </if>
            <if test="grade != null">
                GRADE,
            </if>
            <if test="disableTime != null">
                DISABLE_TIME,
            </if>
            <if test="disableReason != null">
                DISABLE_REASON,
            </if>
            <if test="comments != null">
                COMMENTS,
            </if>
            <if test="brief != null">
                BRIEF,
            </if>
            <if test="hotTelephone != null">
                HOT_TELEPHONE,
            </if>
            <if test="serviceTelephone != null">
                SERVICE_TELEPHONE,
            </if>
            <if test="logisticsName != null">
                LOGISTICS_NAME,
            </if>
            <if test="website != null">
                WEBSITE,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="warehouseAreaId != null">
                WAREHOUSE_AREA_ID,
            </if>
            <if test="warehouseAreaIds != null">
                WAREHOUSE_AREA_IDS,
            </if>
            <if test="warehouseAddress != null">
                WAREHOUSE_ADDRESS,
            </if>
            <if test="traderType != null">
                TRADER_TYPE,
            </if>
            <if test="afterSaleManager != null">
                AFTER_SALE_MANAGER,
            </if>
            <if test="installserviceContactname != null">
                INSTALLSERVICE_CONTACTNAME,
            </if>
            <if test="installserviceContactway != null">
                INSTALLSERVICE_CONTACTWAY,
            </if>
            <if test="technicaldirectContactname != null">
                TECHNICALDIRECT_CONTACTNAME,
            </if>
            <if test="technicaldirectContactway != null">
                TECHNICALDIRECT_CONTACTWAY,
            </if>
            <if test="maintenanceContactname != null">
                MAINTENANCE_CONTACTNAME,
            </if>
            <if test="maintenanceContactway != null">
                MAINTENANCE_CONTACTWAY,
            </if>
            <if test="exchangeContactname != null">
                EXCHANGE_CONTACTNAME,
            </if>
            <if test="exchangeContactway != null">
                EXCHANGE_CONTACTWAY,
            </if>
            <if test="otherContactname != null">
                OTHER_CONTACTNAME,
            </if>
            <if test="otherContactway != null">
                OTHER_CONTACTWAY,
            </if>
            <if test="vaildTime != null">
                VAILD_TIME,
            </if>
            <if test="taxPayerType != null">
                TAX_PAYER_TYPE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="traderId != null">
                #{traderId,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="periodAmount != null">
                #{periodAmount,jdbcType=DECIMAL},
            </if>
            <if test="periodDay != null">
                #{periodDay,jdbcType=INTEGER},
            </if>
            <if test="isEnable != null">
                #{isEnable,jdbcType=BOOLEAN},
            </if>
            <if test="isTop != null">
                #{isTop,jdbcType=BOOLEAN},
            </if>
            <if test="supplyBrand != null">
                #{supplyBrand,jdbcType=VARCHAR},
            </if>
            <if test="supplyProduct != null">
                #{supplyProduct,jdbcType=VARCHAR},
            </if>
            <if test="grade != null">
                #{grade,jdbcType=INTEGER},
            </if>
            <if test="disableTime != null">
                #{disableTime,jdbcType=BIGINT},
            </if>
            <if test="disableReason != null">
                #{disableReason,jdbcType=VARCHAR},
            </if>
            <if test="comments != null">
                #{comments,jdbcType=VARCHAR},
            </if>
            <if test="brief != null">
                #{brief,jdbcType=VARCHAR},
            </if>
            <if test="hotTelephone != null">
                #{hotTelephone,jdbcType=VARCHAR},
            </if>
            <if test="serviceTelephone != null">
                #{serviceTelephone,jdbcType=VARCHAR},
            </if>
            <if test="logisticsName != null">
                #{logisticsName,jdbcType=VARCHAR},
            </if>
            <if test="website != null">
                #{website,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="warehouseAreaId != null">
                #{warehouseAreaId,jdbcType=INTEGER},
            </if>
            <if test="warehouseAreaIds != null">
                #{warehouseAreaIds,jdbcType=VARCHAR},
            </if>
            <if test="warehouseAddress != null">
                #{warehouseAddress,jdbcType=VARCHAR},
            </if>
            <if test="traderType != null">
                #{traderType,jdbcType=BOOLEAN},
            </if>
            <if test="afterSaleManager != null">
                #{afterSaleManager,jdbcType=VARCHAR},
            </if>
            <if test="installserviceContactname != null">
                #{installserviceContactname,jdbcType=VARCHAR},
            </if>
            <if test="installserviceContactway != null">
                #{installserviceContactway,jdbcType=VARCHAR},
            </if>
            <if test="technicaldirectContactname != null">
                #{technicaldirectContactname,jdbcType=VARCHAR},
            </if>
            <if test="technicaldirectContactway != null">
                #{technicaldirectContactway,jdbcType=VARCHAR},
            </if>
            <if test="maintenanceContactname != null">
                #{maintenanceContactname,jdbcType=VARCHAR},
            </if>
            <if test="maintenanceContactway != null">
                #{maintenanceContactway,jdbcType=VARCHAR},
            </if>
            <if test="exchangeContactname != null">
                #{exchangeContactname,jdbcType=VARCHAR},
            </if>
            <if test="exchangeContactway != null">
                #{exchangeContactway,jdbcType=VARCHAR},
            </if>
            <if test="otherContactname != null">
                #{otherContactname,jdbcType=VARCHAR},
            </if>
            <if test="otherContactway != null">
                #{otherContactway,jdbcType=VARCHAR},
            </if>
            <if test="vaildTime != null">
                #{vaildTime,jdbcType=TIMESTAMP},
            </if>
            <if test="taxPayerType != null">
                #{taxPayerType,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.TraderSupplierEntity">
        <!--@mbg.generated-->
        update T_TRADER_SUPPLIER
        <set>
            <if test="traderId != null">
                TRADER_ID = #{traderId,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                AMOUNT = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="periodAmount != null">
                PERIOD_AMOUNT = #{periodAmount,jdbcType=DECIMAL},
            </if>
            <if test="periodDay != null">
                PERIOD_DAY = #{periodDay,jdbcType=INTEGER},
            </if>
            <if test="isEnable != null">
                IS_ENABLE = #{isEnable,jdbcType=BOOLEAN},
            </if>
            <if test="isTop != null">
                IS_TOP = #{isTop,jdbcType=BOOLEAN},
            </if>
            <if test="supplyBrand != null">
                SUPPLY_BRAND = #{supplyBrand,jdbcType=VARCHAR},
            </if>
            <if test="supplyProduct != null">
                SUPPLY_PRODUCT = #{supplyProduct,jdbcType=VARCHAR},
            </if>
            <if test="grade != null">
                GRADE = #{grade,jdbcType=INTEGER},
            </if>
            <if test="disableTime != null">
                DISABLE_TIME = #{disableTime,jdbcType=BIGINT},
            </if>
            <if test="disableReason != null">
                DISABLE_REASON = #{disableReason,jdbcType=VARCHAR},
            </if>
            <if test="comments != null">
                COMMENTS = #{comments,jdbcType=VARCHAR},
            </if>
            <if test="brief != null">
                BRIEF = #{brief,jdbcType=VARCHAR},
            </if>
            <if test="hotTelephone != null">
                HOT_TELEPHONE = #{hotTelephone,jdbcType=VARCHAR},
            </if>
            <if test="serviceTelephone != null">
                SERVICE_TELEPHONE = #{serviceTelephone,jdbcType=VARCHAR},
            </if>
            <if test="logisticsName != null">
                LOGISTICS_NAME = #{logisticsName,jdbcType=VARCHAR},
            </if>
            <if test="website != null">
                WEBSITE = #{website,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="warehouseAreaId != null">
                WAREHOUSE_AREA_ID = #{warehouseAreaId,jdbcType=INTEGER},
            </if>
            <if test="warehouseAreaIds != null">
                WAREHOUSE_AREA_IDS = #{warehouseAreaIds,jdbcType=VARCHAR},
            </if>
            <if test="warehouseAddress != null">
                WAREHOUSE_ADDRESS = #{warehouseAddress,jdbcType=VARCHAR},
            </if>
            <if test="traderType != null">
                TRADER_TYPE = #{traderType,jdbcType=BOOLEAN},
            </if>
            <if test="afterSaleManager != null">
                AFTER_SALE_MANAGER = #{afterSaleManager,jdbcType=VARCHAR},
            </if>
            <if test="installserviceContactname != null">
                INSTALLSERVICE_CONTACTNAME = #{installserviceContactname,jdbcType=VARCHAR},
            </if>
            <if test="installserviceContactway != null">
                INSTALLSERVICE_CONTACTWAY = #{installserviceContactway,jdbcType=VARCHAR},
            </if>
            <if test="technicaldirectContactname != null">
                TECHNICALDIRECT_CONTACTNAME = #{technicaldirectContactname,jdbcType=VARCHAR},
            </if>
            <if test="technicaldirectContactway != null">
                TECHNICALDIRECT_CONTACTWAY = #{technicaldirectContactway,jdbcType=VARCHAR},
            </if>
            <if test="maintenanceContactname != null">
                MAINTENANCE_CONTACTNAME = #{maintenanceContactname,jdbcType=VARCHAR},
            </if>
            <if test="maintenanceContactway != null">
                MAINTENANCE_CONTACTWAY = #{maintenanceContactway,jdbcType=VARCHAR},
            </if>
            <if test="exchangeContactname != null">
                EXCHANGE_CONTACTNAME = #{exchangeContactname,jdbcType=VARCHAR},
            </if>
            <if test="exchangeContactway != null">
                EXCHANGE_CONTACTWAY = #{exchangeContactway,jdbcType=VARCHAR},
            </if>
            <if test="otherContactname != null">
                OTHER_CONTACTNAME = #{otherContactname,jdbcType=VARCHAR},
            </if>
            <if test="otherContactway != null">
                OTHER_CONTACTWAY = #{otherContactway,jdbcType=VARCHAR},
            </if>
            <if test="vaildTime != null">
                VAILD_TIME = #{vaildTime,jdbcType=TIMESTAMP},
            </if>
            <if test="taxPayerType != null">
                TAX_PAYER_TYPE = #{taxPayerType,jdbcType=TINYINT},
            </if>
        </set>
        where TRADER_SUPPLIER_ID = #{traderSupplierId,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.TraderSupplierEntity">
        <!--@mbg.generated-->
        update T_TRADER_SUPPLIER
        set TRADER_ID                   = #{traderId,jdbcType=INTEGER},
            AMOUNT                      = #{amount,jdbcType=DECIMAL},
            PERIOD_AMOUNT               = #{periodAmount,jdbcType=DECIMAL},
            PERIOD_DAY                  = #{periodDay,jdbcType=INTEGER},
            IS_ENABLE                   = #{isEnable,jdbcType=BOOLEAN},
            IS_TOP                      = #{isTop,jdbcType=BOOLEAN},
            SUPPLY_BRAND                = #{supplyBrand,jdbcType=VARCHAR},
            SUPPLY_PRODUCT              = #{supplyProduct,jdbcType=VARCHAR},
            GRADE                       = #{grade,jdbcType=INTEGER},
            DISABLE_TIME                = #{disableTime,jdbcType=BIGINT},
            DISABLE_REASON              = #{disableReason,jdbcType=VARCHAR},
            COMMENTS                    = #{comments,jdbcType=VARCHAR},
            BRIEF                       = #{brief,jdbcType=VARCHAR},
            HOT_TELEPHONE               = #{hotTelephone,jdbcType=VARCHAR},
            SERVICE_TELEPHONE           = #{serviceTelephone,jdbcType=VARCHAR},
            LOGISTICS_NAME              = #{logisticsName,jdbcType=VARCHAR},
            WEBSITE                     = #{website,jdbcType=VARCHAR},
            ADD_TIME                    = #{addTime,jdbcType=BIGINT},
            CREATOR                     = #{creator,jdbcType=INTEGER},
            MOD_TIME                    = #{modTime,jdbcType=BIGINT},
            UPDATER                     = #{updater,jdbcType=INTEGER},
            WAREHOUSE_AREA_ID           = #{warehouseAreaId,jdbcType=INTEGER},
            WAREHOUSE_AREA_IDS          = #{warehouseAreaIds,jdbcType=VARCHAR},
            WAREHOUSE_ADDRESS           = #{warehouseAddress,jdbcType=VARCHAR},
            TRADER_TYPE                 = #{traderType,jdbcType=BOOLEAN},
            AFTER_SALE_MANAGER          = #{afterSaleManager,jdbcType=VARCHAR},
            INSTALLSERVICE_CONTACTNAME  = #{installserviceContactname,jdbcType=VARCHAR},
            INSTALLSERVICE_CONTACTWAY   = #{installserviceContactway,jdbcType=VARCHAR},
            TECHNICALDIRECT_CONTACTNAME = #{technicaldirectContactname,jdbcType=VARCHAR},
            TECHNICALDIRECT_CONTACTWAY  = #{technicaldirectContactway,jdbcType=VARCHAR},
            MAINTENANCE_CONTACTNAME     = #{maintenanceContactname,jdbcType=VARCHAR},
            MAINTENANCE_CONTACTWAY      = #{maintenanceContactway,jdbcType=VARCHAR},
            EXCHANGE_CONTACTNAME        = #{exchangeContactname,jdbcType=VARCHAR},
            EXCHANGE_CONTACTWAY         = #{exchangeContactway,jdbcType=VARCHAR},
            OTHER_CONTACTNAME           = #{otherContactname,jdbcType=VARCHAR},
            OTHER_CONTACTWAY            = #{otherContactway,jdbcType=VARCHAR},
            VAILD_TIME                  = #{vaildTime,jdbcType=TIMESTAMP},
            TAX_PAYER_TYPE              = #{taxPayerType,jdbcType=TINYINT}
        where TRADER_SUPPLIER_ID = #{traderSupplierId,jdbcType=INTEGER}
    </update>

    <select id="getTraderSupplierInfoById" resultType="com.vedeng.erp.trader.domain.dto.TraderSupplierDto">
        select *
        from T_TRADER_SUPPLIER
        where TRADER_ID = #{traderId,jdbcType=INTEGER}
    </select>

    <select id="selectPushKingDeeTraderSupplierData" resultType="com.vedeng.erp.trader.dto.TraderSupplierDto">
        SELECT ts.TRADER_SUPPLIER_ID,
               ts.TRADER_ID,
               t.TRADER_NAME
        FROM T_TRADER_SUPPLIER ts
                 LEFT JOIN T_TRADER t ON t.TRADER_ID = ts.TRADER_ID
                 LEFT JOIN T_VERIFIES_INFO a ON a.RELATE_TABLE_KEY = ts.TRADER_SUPPLIER_ID
            AND a.RELATE_TABLE = 'T_TRADER_SUPPLIER'
            AND a.VERIFIES_TYPE = 619
                 left join KING_DEE_SUPPLIER b on ts.TRADER_SUPPLIER_ID = b.F_NUMBER
        WHERE 1 = 1
          AND ts.IS_ENABLE = 1
          AND t.COMPANY_ID = 1
          and a.`STATUS` = 1
          and b.F_SUPPLIER_ID is null
        <if test="begin != null">
            and ts.ADD_TIME <![CDATA[>=]]> #{begin,jdbcType=BIGINT}
        </if>
        <if test="end != null">
            and ts.ADD_TIME <![CDATA[<=]]> #{end,jdbcType=BIGINT}
        </if>
        <if test="limit != null">
            limit #{limit,jdbcType=INTEGER}
        </if>
    </select>

    <select id="selectPushKingDeeTraderSupplierDataByIds" resultType="com.vedeng.erp.trader.dto.TraderSupplierDto">
        SELECT ts.TRADER_SUPPLIER_ID,
               ts.TRADER_ID,
               t.TRADER_NAME
        FROM T_TRADER_SUPPLIER ts
                 LEFT JOIN T_TRADER t ON t.TRADER_ID = ts.TRADER_ID
                 LEFT JOIN T_VERIFIES_INFO a ON a.RELATE_TABLE_KEY = ts.TRADER_SUPPLIER_ID
            AND a.RELATE_TABLE = 'T_TRADER_SUPPLIER'
            AND a.VERIFIES_TYPE = 619
                 left join KING_DEE_SUPPLIER b on ts.TRADER_SUPPLIER_ID = b.F_NUMBER
        WHERE 1 = 1
          AND ts.IS_ENABLE = 1
          AND t.COMPANY_ID = 1
          and a.`STATUS` = 1
          and b.F_SUPPLIER_ID is null
          and ts.TRADER_SUPPLIER_ID in
        <foreach collection="ids" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateTraderSupplierAmount">
        UPDATE T_TRADER_SUPPLIER A
        SET A.AMOUNT = A.AMOUNT + #{amount,jdbcType=DECIMAL}
        WHERE A.TRADER_ID = #{traderId,jdbcType=INTEGER}
    </update>


    <select id="findByTraderId" resultType="com.vedeng.erp.trader.dto.TraderSupplierDto">
        SELECT TTS.TRADER_SUPPLIER_ID,
               TTS.TRADER_ID,
               TT.TRADER_NAME,
                TTS.AMOUNT,
                TTS.PERIOD_AMOUNT
        FROM T_TRADER_SUPPLIER TTS
                 LEFT JOIN T_TRADER TT ON TT.TRADER_ID = TTS.TRADER_ID
        WHERE TTS.TRADER_ID = #{traderId,jdbcType=INTEGER}
    </select>

    <update id="updateSupplyBalance">
        UPDATE T_TRADER_SUPPLIER A
        SET A.AMOUNT = ABS(A.AMOUNT) + ABS(#{amount,jdbcType=DECIMAL})
        WHERE A.TRADER_ID = #{traderId,jdbcType=INTEGER} and A.amount = #{oldAmount,jdbcType=DECIMAL}
    </update>

    <select id="getSupplierInfoForKingDee" resultMap="KingDeeResultMap">
        SELECT
            tt.TRADER_ID,
            tts.TRADER_SUPPLIER_ID,
            tt.TRADER_NAME,
            ttf.BANK,
            ttf.BANK_CODE,
            ttf.BANK_ACCOUNT
        FROM
            T_TRADER tt
        LEFT JOIN T_TRADER_SUPPLIER tts ON
            tt.TRADER_ID = tts.TRADER_ID
        LEFT JOIN T_TRADER_FINANCE ttf ON
            tt.TRADER_ID = ttf.TRADER_ID AND ttf.TRADER_TYPE = 2
        WHERE
            tts.TRADER_SUPPLIER_ID = #{traderSupplierId,jdbcType=INTEGER}
            AND tts.IS_ENABLE = 1
    </select>


<!--auto generated by MybatisCodeHelper on 2024-02-22-->
    <select id="findTraderTypeByTraderSupplierId" resultType="java.lang.Integer">
        select TRADER_TYPE
        from T_TRADER_SUPPLIER
        where TRADER_SUPPLIER_ID=#{traderSupplierId,jdbcType=INTEGER}
    </select>
    <select id="findByTraderName" resultType="com.vedeng.erp.trader.dto.TraderSupplierDto">
        select A.TRADER_ID,A.TRADER_NAME
        from T_TRADER A
        LEFT JOIN T_TRADER_SUPPLIER B ON A.TRADER_ID=B.TRADER_ID
        WHERE A.TRADER_NAME LIKE CONCAT('%',#{name,jdbcType=VARCHAR},'%')
          AND B.IS_ENABLE=1   AND A.COMPANY_ID=1
            LIMIT #{start,jdbcType=INTEGER},#{pageSize,jdbcType=INTEGER}
    </select>
    <select id="countTraderSupplierByTraderName" resultType="java.lang.Long">
        select count(1)
        from T_TRADER A LEFT JOIN T_TRADER_SUPPLIER B ON A.TRADER_ID=B.TRADER_ID

        WHERE A.TRADER_NAME LIKE CONCAT('%',#{name,jdbcType=VARCHAR},'%')
          AND B.IS_ENABLE=1   AND A.COMPANY_ID=1
    </select>

    <select id="findByTraderTypeAndTraderName" resultType="com.vedeng.erp.system.dto.LikeTraderDto">
        SELECT
            t.TRADER_ID,
            t.TRADER_NAME,
            CASE
                WHEN t.TRADER_NAME IS NULL OR
                     t.AREA_IDS IS NULL OR t.AREA_IDS = '' OR
                     t.TRADER_TYPE IS NULL OR t.TRADER_TYPE = 0 OR
                     t.MEDICAL_QUALIFICATION IS NULL OR t.MEDICAL_QUALIFICATION = 1 OR
                     s.WAREHOUSE_AREA_ID IS NULL OR s.WAREHOUSE_AREA_ID = '' OR
                     s.WAREHOUSE_ADDRESS IS NULL OR s.WAREHOUSE_ADDRESS = '' OR
                     tc.TRADER_CERTIFICATE_ID_25 IS NULL OR
                     tc.TRADER_CERTIFICATE_ID_1100 IS NULL OR
                     tc.TRADER_CERTIFICATE_ID_896 IS NULL
                    THEN 0
                ELSE 1
                END AS riskPass
        FROM
            T_TRADER_SUPPLIER s
                LEFT JOIN T_TRADER t ON s.TRADER_ID = t.TRADER_ID
                LEFT JOIN (
                SELECT a.TRADER_ID,
                       MAX(CASE WHEN SYS_OPTION_DEFINITION_ID = 25 THEN TRADER_CERTIFICATE_ID END) AS TRADER_CERTIFICATE_ID_25,
                       MAX(CASE WHEN SYS_OPTION_DEFINITION_ID = 1100 THEN TRADER_CERTIFICATE_ID END) AS TRADER_CERTIFICATE_ID_1100,
                       MAX(CASE WHEN SYS_OPTION_DEFINITION_ID = 896 THEN TRADER_CERTIFICATE_ID END) AS TRADER_CERTIFICATE_ID_896
                FROM T_TRADER_CERTIFICATE a left join T_TRADER b on a.TRADER_ID = b.TRADER_ID
                WHERE a.IS_DELETE = 0 AND b.TRADER_NAME LIKE CONCAT('%', #{keyword,jdbcType=VARCHAR}, '%')
        GROUP BY a.TRADER_ID
            ) tc ON tc.TRADER_ID = t.TRADER_ID
        WHERE
            s.IS_ENABLE = 1
          AND t.IS_ENABLE = 1
          AND t.TRADER_NAME LIKE CONCAT('%', #{keyword,jdbcType=VARCHAR}, '%')
        GROUP BY
            t.TRADER_ID,
            t.TRADER_NAME
        ORDER BY
        riskPass DESC,
            TRADER_ID DESC
        LIMIT 100
    </select>
</mapper>
