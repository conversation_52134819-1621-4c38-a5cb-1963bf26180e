<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="编辑区域" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<%--<script src="${pageContext.request.contextPath}/static/js/trader/add_region_rules.js?rnd=${resourceVersionKey}" charset="utf-8"></script>--%>
<style type="text/css">

    .infor_namex{
        float: left;
        width: 70px;
        overflow: hidden;
        margin: 3px 10px 0px 81px;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    .bit-center{
        margin-left: 315px;
        margin-top: 75px;
    }
    .seacher-form{
        margin-left: 98px;
        margin-top: 36px;
    }
</style>
<span style="font-size: 20px !important;font-weight: bold">编辑区域</span>

<div class="searchfunc seacher-form">

    <form method="post" id="search" action="/trader/customer/regionRulesList.do">
        <input type="hidden" id="publicCustomerRegionRulesId" value="${publicCustomerRegionRulesVo.publicCustomerRegionRulesId}"/>
        <input type="hidden" id="regionId" value="${publicCustomerRegionRulesVo.regionId}"/>
        <input type="hidden" id = "checkData" value=""/>
        <ul>
            <%--<li>
                <label class="infor_namex"><span class="font-red">*</span>销售：</label>
                <select class="input-middle f_left" name="userId" id="userId" onchange="getUserData()">
                    <option value="-1">请选择</option>
                    <c:forEach var="list" items="${userList}">
                        <option value="${list.userId}"
                        <c:if test="${publicCustomerRegionRulesVo.userId == list.userId}">selected="selected"</c:if> >${list.username}</option>
                    </c:forEach>
                </select>
            </li>--%>
            <li>
                <label class="infor_namex"><span class="font-red">*</span>归属销售:</label>
                <select class="input-middle f_left" name="userId" id="userId" onchange="getUserData()">
                    <option value="-1">请选择</option>
                    <c:if test="${not empty userList }">
                        <c:forEach items="${userList }" var="user">
                            <option value="${user.userId }"
                                    <c:if test="${publicCustomerRegionRulesVo.userId == user.userId }">selected="selected"</c:if>>${user.username }</option>
                        </c:forEach>
                    </c:if>
                </select>
            </li>
        </ul>
        <ul>
            <li>
                <div class="font-red f_left inputfloat mt3" style="padding-left: 80px;font-size: 15px;" id="u-message"></div>
            </li>
        </ul>
        <ul>
            <li>
                <div class="font-grey6 f_left inputfloat mt3" style="padding-left: 80px;font-size: 15px;" id="user-message">归属部门：${publicCustomerRegionRulesVo.orgName}&nbsp;&nbsp;主管：${publicCustomerRegionRulesVo.userBossName}</div>
            </li>
        </ul>
        <ul>
            <li>
                <label class="infor_namex"><span class="font-red">*</span>选择区域：</label>
                <ul class="inputfloat f_left">
                    <li>
                        <select class="wid16 selector" name="province" id="province">
                            <option value="0">请选择</option>
                            <c:if test="${not empty provinceList }">
                                <c:forEach items="${provinceList }" var="prov">
                                    <option value="${prov.regionId }" <c:if test="${publicCustomerRegionRulesVo.provinceId==prov.regionId}">selected="selected"</c:if>>${prov.regionName }</option>
                                </c:forEach>
                            </c:if>
                        </select>
                    </li>
                    <li>
                        <select class="wid16 selector" name="city" id="city">
                            <option value="0">请选择</option>
                            <c:if test="${not empty cityList }">
                                <c:forEach items="${cityList }" var="cy">
                                    <option value="${cy.regionId }" <c:if test="${publicCustomerRegionRulesVo.cityId==cy.regionId}">selected="selected"</c:if>>${cy.regionName }</option>
                                </c:forEach>
                            </c:if>
                        </select>
                    </li>
                    <li>
                        <select class="wid16 selector" name="zone" id="zone" onchange="checkAllData()">
                            <option value="0">请选择</option>
                            <c:if test="${not empty zoneList }">
                                <c:forEach items="${zoneList }" var="zo">
                                    <option value="${zo.regionId }" <c:if test="${publicCustomerRegionRulesVo.zoneId==zo.regionId}">selected="selected"</c:if>>${zo.regionName }</option>
                                </c:forEach>
                            </c:if>
                        </select>
                    </li>


                </ul>
            </li>
        </ul>
        <ul>
            <li>
                <div class="font-red f_left inputfloat mt3" style="padding-left: 80px;font-size: 15px;" id="message"></div>
            </li>
        </ul>
        <div class=" bit-center  mt20" >
            <button type="button" class="bt-bg-style bg-light-blue bt-small mr10" id='submit'>提交</button>
            <button type="button" class="bt-bg-style bg-light-red bt-small mr10" onclick="custom_close()" id='cancel0'>取消</button>
        </div>
    </form>
</div>
</body>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/region/index.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/js/trader/edit_region_rules.js?rnd=${resourceVersionKey}" charset="utf-8"></script>

</html>
