package com.vedeng.erp.kingdee.service;

import com.vedeng.erp.kingdee.dto.KingDeeStorageOutDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeStorageOutQueryResultDto;
import com.vedeng.infrastructure.kingdee.service.KingDeeBaseService;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/26 15:18
 */
public interface KingDeeStorageOutService extends KingDeeBaseService<KingDeeStorageOutDto>{


    List<KingDeeStorageOutQueryResultDto> getKingDeeStorageOut(String f_qzok_bddjtid, String f_qzok_bddjhid);
}
