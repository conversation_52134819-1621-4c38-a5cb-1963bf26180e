package com.vedeng.erp.trader.domain.entity;

import java.util.Date;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * 开户行匹配配置分组表
 * <AUTHOR>
 */
@Getter
@Setter
public class BankMatchConfigGroupEntity extends BaseEntity {
    /**
    * 主键
    */
    private Long groupId;

    /**
    * 分组名称
    */
    private String groupName;

    /**
    * 是否删除 0否 1是
    */
    private Integer isDelete;

    /**
    * 更新备注
    */
    private String updateRemark;
}
