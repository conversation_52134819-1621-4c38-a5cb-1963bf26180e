package com.vedeng.crm.task.domain.dto;

import com.vedeng.common.core.utils.validator.group.AddGroup;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 任务记录表
 *
 * <AUTHOR>
 */
@Data
public class TaskHandleDto {
    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 我的任务id
     */
    @NotNull(message = "我的任务id不能为空", groups = AddGroup.class)
    private Long taskItemId;

    /**
     * 任务处理结果 1已处理 2已关闭
     */
    @NotNull(message = "任务处理结果不能为空", groups = AddGroup.class)
    private Integer doneStatus;

    /**
     * 处理结果说明
     */
    private String doneRemark;

    /**
     * 通过API接口传过来的用户信息-userId
     */
    private Integer apiUserId;
    /**
     * 通过API接口传过来的用户信息-userName
     */
    private String apiUserName;


}