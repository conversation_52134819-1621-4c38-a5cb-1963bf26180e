package com.vedeng.erp.buyorder.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/2/16 11:19
 * @describe ge审核页基础信息
 */
@Data
public class GeExamineBasicDto {

    private Integer geBussinessChanceId;

    //报价单号
    private String quoteorderNo;

    // 报单日期
    private Date addTime;

    //报单状态 0待审核 1可跟进 2不可跟进
    private Integer status;

    //商机状态 1跟进中 2赢单 3失单
    private Integer businessChanceStatus;

    // 终端名称
    private String terminalTraderName;

    // 医院性质 1公立 2非公
    private Integer hospitalType;

    // 商品名称 意向型号
    private String goodsName;

    // 联系详细地址
    private String address;

    //终端销售区域
    private String salesArea;

    //商机来源名称
    private String title;

    private String traderName;






}
