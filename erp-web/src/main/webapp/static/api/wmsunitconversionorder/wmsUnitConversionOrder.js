function saveAddWmsUnitConversionOrder(data) {
    return axios({
        url: '/wmsUnitConversionOrder/add.do',
        method: 'post',
        data: data
    })
}

function saveEditWmsUnitConversionOrder(data) {
    return axios({
        url: '/wmsUnitConversionOrder/update.do',
        method: 'post',
        data: data
    })
}

function viewWmsUnitConversionOrderDetail(data) {
    return axios({
        url: '/wmsUnitConversionOrder/getViewData.do',
        method: 'post',
        params: data
    })
}

function getVerifyInfo(data) {
    return axios({
        url: '/wmsUnitConversion/getVerifyInfo.do',
        method: 'post',
        params: data
    })
}

function getOutLog(data) {
    return axios({
        url: '/wmsUnitConversionOrder/getOutLog.do',
        method: 'post',
        params: data
    })
}

function getInLog(data) {
    return axios({
        url: '/wmsUnitConversionOrder/getInLog.do',
        method: 'post',
        params: data
    })
}

function doAudit(data) {
    return axios({
        url: '/wmsUnitConversion/doComplement.do',
        method: 'post',
        params: data
    })
}

function getStockNum(data) {
    return axios({
        url: '/wmsUnitConversionOrder/getStockNum.do',
        method: 'post',
        params: data
    })
}



