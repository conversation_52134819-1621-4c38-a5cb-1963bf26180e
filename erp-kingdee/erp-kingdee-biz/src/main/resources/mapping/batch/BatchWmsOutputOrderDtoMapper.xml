<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchWmsOutputOrderDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchWmsOutputOrderDto">
    <!--@mbg.generated-->
    <!--@Table T_WMS_OUTPUT_ORDER-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="verify_status" jdbcType="INTEGER" property="verifyStatus" />
    <result column="return_status" jdbcType="INTEGER" property="returnStatus" />
    <result column="out_status" jdbcType="INTEGER" property="outStatus" />
    <result column="borrow_reason" jdbcType="VARCHAR" property="borrowReason" />
    <result column="borrow_trader_id" jdbcType="BIGINT" property="borrowTraderId" />
    <result column="borrow_trader_name" jdbcType="VARCHAR" property="borrowTraderName" />
    <result column="logistic_commnet" jdbcType="VARCHAR" property="logisticCommnet" />
    <result column="belong_department" jdbcType="VARCHAR" property="belongDepartment" />
    <result column="scrap_type" jdbcType="INTEGER" property="scrapType" />
    <result column="scrap_level" jdbcType="INTEGER" property="scrapLevel" />
    <result column="scrap_deal_type" jdbcType="INTEGER" property="scrapDealType" />
    <result column="applyer" jdbcType="VARCHAR" property="applyer" />
    <result column="applyer_department" jdbcType="VARCHAR" property="applyerDepartment" />
    <result column="recipient_applyer" jdbcType="VARCHAR" property="recipientApplyer" />
    <result column="recipient_department" jdbcType="VARCHAR" property="recipientDepartment" />
    <result column="recipient_type" jdbcType="INTEGER" property="recipientType" />
    <result column="apple_out_date" jdbcType="VARCHAR" property="appleOutDate" />
    <result column="use_nature" jdbcType="VARCHAR" property="useNature" />
    <result column="use_name" jdbcType="VARCHAR" property="useName" />
    <result column="receiver" jdbcType="VARCHAR" property="receiver" />
    <result column="receiver_phone" jdbcType="VARCHAR" property="receiverPhone" />
    <result column="receiver_telphone" jdbcType="VARCHAR" property="receiverTelphone" />
    <result column="receiver_address" jdbcType="VARCHAR" property="receiverAddress" />
    <result column="detail_address" jdbcType="VARCHAR" property="detailAddress" />
    <result column="apply_reason" jdbcType="VARCHAR" property="applyReason" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="real_output_time" jdbcType="VARCHAR" property="realOutputTime" />
    <result column="customer_receive_time" jdbcType="VARCHAR" property="customerReceiveTime" />
    <result column="approval_time" jdbcType="VARCHAR" property="approvalTime" />
    <result column="is_delete" jdbcType="BOOLEAN" property="isDelete" />
    <result column="applyer_id" jdbcType="INTEGER" property="applyerId" />
    <result column="applyer_department_id" jdbcType="INTEGER" property="applyerDepartmentId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <collection property="batchWmsOutputOrderGoodsDtos" ofType="com.vedeng.erp.kingdee.batch.dto.BatchWmsOutputOrderGoodsDto">
      <id column="ORDER_GOODS_ID" jdbcType="BIGINT" property="id" />
      <result column="wms_output_order_id" jdbcType="BIGINT" property="wmsOutputOrderId" />
      <result column="sku_no" jdbcType="VARCHAR" property="skuNo" />
      <result column="SKU_ID" jdbcType="VARCHAR" property="goodsId" />
      <result column="output_num" jdbcType="INTEGER" property="outputNum" />
      <result column="already_output_num" jdbcType="INTEGER" property="alreadyOutputNum" />
      <result column="last_output_time" jdbcType="VARCHAR" property="lastOutputTime" />
      <result column="out_status" jdbcType="INTEGER" property="outStatus" />
      <result column="already_input_num" jdbcType="INTEGER" property="alreadyInputNum" />
      <result column="last_input_time" jdbcType="VARCHAR" property="lastInputTime" />
      <result column="input_status" jdbcType="INTEGER" property="inputStatus" />
      <result column="expect_returned_time" jdbcType="VARCHAR" property="expectReturnedTime" />
      <result column="LOGICAL_WAREHOUSE_ID" jdbcType="INTEGER" property="logicalWarehouseId" />
    </collection>
  </resultMap>

  <select id="findByOrderNo" resultMap="BaseResultMap">
    select
    TWOO.*,
    TWOOG.id ORDER_GOODS_ID,
    TWOOG.wms_output_order_id,
    TWOOG.sku_no,
    TWOOG.output_num,
    TWOOG.already_output_num,
    TWOOG.last_output_time,
    TWOOG.out_status,
    TWOOG.already_input_num,
    TWOOG.last_input_time,
    TWOOG.input_status,
    TWOOG.expect_returned_time,
    TWOOG.LOGICAL_WAREHOUSE_ID,
    VCS.SKU_ID
    from T_WMS_OUTPUT_ORDER TWOO
    left join T_WMS_OUTPUT_ORDER_GOODS TWOOG on TWOO.ID = TWOOG.WMS_OUTPUT_ORDER_ID
    left join V_CORE_SKU VCS on TWOOG.sku_no = VCS.SKU_NO
    where TWOO.ORDER_NO = #{orderNo,jdbcType=VARCHAR}
    and TWOO.IS_DELETE = 0
    and TWOOG.is_delete=0
  </select>


  <select id="getTraderCustomerIdByOrderId" resultType="java.lang.Integer">
    SELECT
    T2.TRADER_CUSTOMER_ID
    FROM
    T_WMS_OUTPUT_ORDER T1
    LEFT JOIN T_TRADER_CUSTOMER T2 ON T1.borrow_trader_id = T2.TRADER_ID
    WHERE
    T1.id = #{orderId,jdbcType=BIGINT}
    AND T2.IS_ENABLE = 1
    GROUP BY
    T2.TRADER_CUSTOMER_ID
  </select>

  <select id="findByOrderNoAndType" resultMap="BaseResultMap">
    select
      *
    from
      T_WMS_OUTPUT_ORDER
    where order_no = #{orderNo,jdbcType=VARCHAR} and type = #{type,jdbcType=INTEGER}
  </select>
</mapper>