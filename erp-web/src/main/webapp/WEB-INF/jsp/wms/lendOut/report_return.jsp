<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="验收报告" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript"
        src="<%= basePath %>static/js/jquery/ajaxfileupload.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src="<%= basePath %>static/js/report/add_report.js?rnd=${resourceVersionKey}"></script>
<script type="text/css">
    .warning {
        padding-left: 160px;
    }
</script>
<script>
    function reportSubmit() {
        checkLogin();
        let goodsId = $("#goodsId").val();
        if (goodsId == undefined) {
            warnTips("goodsId", "请选择产品！");
            return false;
        }
        let packaging = $("#packaging").val();
        if (packaging == undefined || packaging == '') {
            warnTips("packaging", "请填写产品外包装！");
            return false;
        }
        if (packaging.length > 50) {
            warnTips("packaging", "产品外包装不允许超过50个字！");
            return false;
        }
        let appearance = $("#appearance").val();
        if (appearance == undefined || appearance == '') {
            warnTips("appearance", "请填写产品外观！");
            return false;
        }
        if (appearance.length > 50) {
            warnTips("appearance", "产品外观不允许超过50个字！");
            return false;
        }

        let performance = $("#performance").val();
        if (performance == undefined || performance == '') {
            warnTips("performance", "请填写产品性能！");
            return false;
        }
        if (performance.length > 50) {
            warnTips("performance", "产品性能不允许超过50个字！");
            return false;
        }

        let backReason = $("#backReason").val();
        if (backReason == undefined || backReason == '') {
            warnTips("backReason", "请填写产品退回原因！");
            return false;
        }
        if (backReason.length > 500) {
            warnTips("backReason", "产品退回原因不允许超过500个字！");
            return false;
        }

        let description = $("#description").val();
        if (description == undefined || description == '') {
            warnTips("description", "请填写产品具体描述！");
            return false;
        }
        if (description.length > 500) {
            warnTips("description", "产品具体描述不允许超过500个字！");
            return false;
        }
        let name =  $("input[name='name_8']");
        if (name[0] == undefined || name[0].value == '') {
            layer.alert("请上传图片！");
            return;
        }
        $.ajax({
            url: './reportReturnSave.do',
            data: $('#report').serialize(),
            type: "POST",
            dataType: "json",
            async: false,
            success: function (data) {
                if (data.code == 0) {
                    window.parent.location.reload();
                } else {
                    layer.alert(data.message);
                }
            },
            error: function (data) {
                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    }
</script>
<div class="addElement">
    <div class="add-main adddepart">
        <form id="report" method="post" enctype="multipart/form-data">
            <div class="form-item">
                <ul>
                    <li>
                        <div class="infor_name sex_name" style="width: 150px">
                            <span style="color: red">*</span> <label>请选择产品：</label>
                        </div>
                        <div class="insertli insertli1">
                            <select class="input-text" name="goodsId" id="goodsId">
                                <c:forEach items="${lendOutGoodList}" var="var" varStatus="index">
                                    <option value="${var.skuId}"
                                            <c:if test="${report.goodsId eq var.skuId}">selected</c:if>
                                    >
                                            ${var.skuNo}
                                    </option>
                                </c:forEach>
                            </select>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="form-item">
                <ul>
                    <li>
                        <div class="infor_name sex_name" style="width: 150px">
                            <span style="color: red">*</span> <label>产品外包装：</label>
                        </div>
                        <div class="insertli insertli1">
                            <input type="text" autocomplete="off" name="packaging" id="packaging" placeholder=""
                                   valid-max="50"
                                   class="input-text" value="${report.packaging}">
                        </div>
                    </li>
                </ul>
            </div>
            <div class="form-item">
                <ul>
                    <li>
                        <div class="infor_name sex_name" style="width: 150px">
                            <span style="color: red">*</span> <label>产品外观：</label>
                        </div>
                        <div class="insertli insertli1">
                            <input type="text" autocomplete="off" name="appearance" id="appearance" placeholder=""
                                   valid-max="50"
                                   class="input-text" value="${report.appearance}">
                        </div>
                    </li>
                </ul>
            </div>
            <div class="form-item">
                <ul>
                    <li>
                        <div class="infor_name sex_name" style="width: 150px">
                            <span style="color: red">*</span> <label>产品性能：</label>
                        </div>
                        <div class="insertli insertli1">
                            <input type="text" autocomplete="off" name="performance" id="performance" placeholder=""
                                   valid-max="50"
                                   class="input-text" value="${report.performance}">
                        </div>
                    </li>
                </ul>
            </div>
            <div class="form-item">
                <ul>
                    <li>
                        <div class="infor_name sex_name" style="width: 150px">
                            <span style="color: red">*</span> <label>产品退回原因：</label>
                        </div>
                        <div class="insertli insertli1">
                            <input type="text" autocomplete="off" name="backReason" id="backReason" placeholder=""
                                   valid-max="500"
                                   class="input-text" value="${report.backReason}">
                        </div>
                    </li>
                </ul>
            </div>
            <div class="form-item">
                <ul>
                    <li>
                        <div class="infor_name sex_name" style="width: 150px">
                            <span style="color: red">*</span> <label>产品具体描述：</label>
                        </div>
                        <div class="insertli insertli1">
                    <textarea name="description" id='description' class="input-large" placeholder=""
                              rows="20"> ${report.description}</textarea>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="form-item">
                <div class="infor_name sex_name" style="width: 150px">
                    <span style="color: red">*</span><label>上传图片</label>
                </div>
                <div class="f_left insertli insertli1">
                    <ul>
                        <li style="margin-bottom:0;">
                            <c:choose>
                                <c:when test="${!empty zb }">
                                    <c:forEach items="${zb }" var="bus" varStatus="st">
                                        <div class="mb8 c_8">
                                            <div class="pos_rel f_left ">
                                                <input type="file" class="upload_file" name="lwfile"
                                                       id="file_8_${st.index}" style="display: none;"
                                                       onchange="uploadFile(this,8);changeInfo(8)"/>
                                                <c:choose>
                                                    <c:when test="${st.index == 0 }">
                                                        <input type="text" class="input-middle" id="name_8"
                                                               readonly="readonly"
                                                               placeholder="请上传招标文件" name="name_8"
                                                               onclick="file_8_${st.index}.click();"
                                                               value="${bus.name}">
                                                        <input type="hidden" id="uri_8_${st.index}" name="uri_8"
                                                               value="${bus.uri}">

                                                    </c:when>
                                                    <c:otherwise>
                                                        <input type="text" class="input-middle"
                                                               id="name_8_${st.index}" readonly="readonly"
                                                               placeholder="请上招标文件" name="name_8"
                                                               onclick="file_8_${st.index}.click();"
                                                               value="${bus.name}">
                                                        <input type="hidden" id="uri_8_${st.index}" name="uri_8"
                                                               value="${bus.uri}">
                                                    </c:otherwise>
                                                </c:choose>
                                                <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                       id="busUpload"
                                                       onclick="return $('#file_8_${st.index}').click();">浏览</label>
                                                <div class="font-red " style="display: none;">请选择招标文件</div>
                                            </div>

                                            <c:choose>
                                                <c:when test="${bus.uri ne null && bus.uri ne ''}">
                                                    <div class="f_left ">
                                                        <i class="iconsuccesss ml7" id="img_icon_4"></i>
                                                        <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4"
                                                           id="img_view_8">查看</a>
                                                        <c:choose>
                                                            <c:when test="${st.index == 0 }">
                                                                                                                    <span class="font-red cursor-pointer mt4"
                                                                                                                          onclick="del(8);changeInfo(8)"
                                                                                                                          id="img_del_8">删除</span>
                                                            </c:when>
                                                            <c:otherwise>
                                                                                                                    <span class="font-red cursor-pointer mt4"
                                                                                                                          onclick="delAttachment(this);changeInfo(8)"
                                                                                                                          id="img_del_8">删除</span>
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </div>
                                                </c:when>
                                                <c:otherwise>
                                                    <div class="f_left ">
                                                        <i class="iconsuccesss ml7 none" id="img_icon_4"></i>
                                                        <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                           id="img_view_8">查看</a>
                                                        <c:choose>
                                                            <c:when test="${st.index == 0 }">
                                                                                                                    <span class="font-red cursor-pointer mt4 none"
                                                                                                                          onclick="del(8);changeInfo(8)"
                                                                                                                          id="img_del_8">删除</span>
                                                            </c:when>
                                                            <c:otherwise>
                                                                                                                    <span class="font-red cursor-pointer mt4 none"
                                                                                                                          onclick="delAttachment(this);changeInfo(8)"
                                                                                                                          id="img_del_8">删除</span>
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </div>
                                                </c:otherwise>
                                            </c:choose>
                                            <div class="clear"></div>
                                        </div>
                                    </c:forEach>
                                </c:when>
                                <c:otherwise>
                                    <div class="mb8 c_8">
                                        <div class="f_left">
                                            <input type="file" class="upload_file" name="lwfile" id="file_8"
                                                   style="display: none;" onchange="uploadFile(this,8);changeInfo(8)"/>
                                            <input type="text" class="input-middle" id="name_8" readonly="readonly"
                                                   placeholder="请上传招标文件" name="name_8" onclick="file_8.click();"
                                                   value="${bus.name}">
                                            <input type="hidden" id="busUri" name="uri_8" value="${bus.uri}">
                                            <div class="font-red " style="display: none;">请上传招标文件</div>
                                        </div>
                                        <label class="bt-bg-style bt-middle bg-light-blue ml10" id="busUpload"
                                               onclick="return $('#file_8').click();">浏览</label>
                                        <!-- 上传成功出现 -->
                                        <c:choose>
                                            <c:when test="${!empty bus.uri}">
                                                <i class="iconsuccesss ml7" id="img_icon_4"></i>
                                                <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                   class="font-blue cursor-pointer mr5 ml10 mt4"
                                                   id="img_view_8">查看</a>
                                                <span class="font-red cursor-pointer mt4" onclick="del(8);changeInfo(8)"
                                                      id="img_del_8">删除</span>
                                            </c:when>
                                            <c:otherwise>
                                                <i class="iconsuccesss ml7 none" id="img_icon_4"></i>
                                                <a href="" target="_blank"
                                                   class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                   id="img_view_8">查看</a>
                                                <span class="font-red cursor-pointer mt4 none"
                                                      onclick="del(8);changeInfo(8)"
                                                      id="img_del_8">删除</span>
                                            </c:otherwise>
                                        </c:choose>
                                        <div class="clear"></div>
                                    </div>
                                </c:otherwise>
                            </c:choose>

                            <div class="clear" id="conadd8">
                                                                                    <span class="bt-border-style bt-small border-blue mt8"
                                                                                          onclick="con_add(8,'请上传招标文件');">继续添加</span>
                            </div>
                        </li>
                        <li>
                            <div class="f_left">
                                <div class="font-red " style="display: none;">开始时间不能为空</div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="form-item">
                <div class="infor_name sex_name" style="width: 150px">

                </div>
                <div class="f_left insertli insertli1">
                    <br/>1. 图片格式只能JPG、PNG、JPEG、BMP格式
                    <br/>2.图片大小不超过20M
                </div>
            </div>
            <div class="form-item">
                <div class="infor_name sex_name" style="width: 150px">
                </div>
                <div class="f_left insertli insertli1">
                    <input type="hidden" value="${report.orderNo}" name="orderNo" id="orderNo">
                    <input type="hidden" value="${report.goodsAcceptanceReportId}" name="goodsAcceptanceReportId"
                           id="goodsAcceptanceReportId">
                    <button type="button" class="bt-bg-style bg-deep-green" id="outIn_attach_return_submit"
                            onclick="reportSubmit()">提交
                    </button>
                    <button class="dele" type="button" id="close-layer">取消</button>
                </div>
            </div>
        </form>
    </div>
</div>
<%@ include file="../../common/footer.jsp" %>