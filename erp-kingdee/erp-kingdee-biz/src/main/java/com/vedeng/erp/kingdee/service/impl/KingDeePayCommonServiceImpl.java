package com.vedeng.erp.kingdee.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeePayCommonCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeePayCommonEntity;
import com.vedeng.erp.kingdee.dto.KingDeePayCommonDto;
import com.vedeng.erp.kingdee.dto.result.KingDeePayCommonQueryResultDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeePayCommonCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeePayCommonConvertor;
import com.vedeng.erp.kingdee.repository.KingDeePayCommonRepository;
import com.vedeng.erp.kingdee.service.KingDeePayCommonApiService;
import com.vedeng.erp.kingdee.service.KingDeePayCommonService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryExtParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryFilterDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 其他入库单接口实现类
 * @date 2023/3/6 9:00
 **/
@Service
@Slf4j
public class KingDeePayCommonServiceImpl extends KingDeeBaseServiceImpl<
        KingDeePayCommonEntity,
        KingDeePayCommonDto,
        KingDeePayCommonCommand,
        KingDeePayCommonRepository,
        KingDeePayCommonConvertor,
        KingDeePayCommonCommandConvertor> implements KingDeePayCommonApiService, KingDeePayCommonService {

    @Override
    public List<KingDeePayCommonQueryResultDto> getKingDeePayCommon(String businessId) {
        KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        queryParam.setFormId(KingDeeFormConstant.PAY_EXPENSES);
        List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        queryFilterDtos.add(KingDeeQueryFilterDto.builder()
                .fieldName("F_QZOK_BDDJTID")
                .value(businessId)
                .build());
        queryFilterDtos.add(KingDeeQueryFilterDto.builder()
                .fieldName("fBusinessType")
                .value("CG").build());
        queryParam.setFilterString(queryFilterDtos);
        return kingDeeBaseApi.query(queryParam, KingDeePayCommonQueryResultDto.class);
    }

    @Override
    public Boolean kingDeeIsExist(KingDeePayCommonDto kingDeePayCommonDto) {
        List<KingDeePayCommonQueryResultDto> kingDeePayCommon = getKingDeePayCommon(kingDeePayCommonDto.getFQzokBddjtId());
        return CollUtil.isNotEmpty(kingDeePayCommon);
    }
}
