<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.finance.dao.PayApplyMapper" >
    <resultMap id="BaseResultMap" type="com.vedeng.finance.model.PayApply" >
        <id column="PAY_APPLY_ID" property="payApplyId" jdbcType="INTEGER" />
        <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
        <result column="PAY_TYPE" property="payType" jdbcType="INTEGER" />
        <result column="RELATED_ID" property="relatedId" jdbcType="INTEGER" />
        <result column="TRADER_SUBJECT" property="traderSubject" jdbcType="BIT" />
        <result column="TRADER_MODE" property="traderMode" jdbcType="INTEGER" />
        <result column="TRADER_NAME" property="traderName" jdbcType="VARCHAR" />
        <result column="TRADER_ID" property="traderId" jdbcType="VARCHAR" />
        <result column="AMOUNT" property="amount" jdbcType="DECIMAL" />
        <result column="CURRENCY_UNIT_ID" property="currencyUnitId" jdbcType="INTEGER" />
        <result column="BANK" property="bank" jdbcType="VARCHAR" />
        <result column="BANK_ACCOUNT" property="bankAccount" jdbcType="VARCHAR" />
        <result column="BANK_CODE" property="bankCode" jdbcType="VARCHAR" />
        <result column="COMMENTS" property="comments" jdbcType="VARCHAR" />
        <result column="VALID_STATUS" property="validStatus" jdbcType="BIT" />
        <result column="VALID_TIME" property="validTime" jdbcType="BIGINT" />
        <result column="VALID_COMMENTS" property="validComments" jdbcType="VARCHAR" />
        <result column="IS_BILL" property="isBill" jdbcType="BIT" />
        <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
        <result column="CREATOR" property="creator" jdbcType="INTEGER" />
        <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
        <result column="UPDATER" property="updater" jdbcType="INTEGER" />
        <result column="USERNAME" property="creatorName" jdbcType="VARCHAR" />
        <result column="PAY_STATUS" property="payStatus" jdbcType="INTEGER"/>
        <result column="MOBILE" property="mobile" jdbcType="VARCHAR"/>
        <result column="CARD" property="card" jdbcType="VARCHAR"/>
        <result column="PAY_BANKTYPE_ID" property="payBankTypeId" jdbcType="INTEGER"/>
        <result column="PAY_BANKTYPE_NAME" property="payBankTypeName" jdbcType="VARCHAR"/>
        <result column="ACCOUNT_TYPE" property="accountType" jdbcType="INTEGER"/>
        <result column="BANK_REMARK" property="bankRemark" jdbcType="VARCHAR"/>
        <result column="BILL_TIME" property="billTime" jdbcType="TIMESTAMP"/>
        <result column="BILL_METHOD" property="billMethod" jdbcType="INTEGER"/>
        <result column="AUTO_BILL" property="autoBill" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap id="passedBuyorderExpenseGoodsIdResult"   type="HashMap">
        <result property="passedPayApplyNum" column="PASSED_NUM" />
        <result property="passedPayApplyAmount" column="PASSED_AMOUNT" />
    </resultMap>

    <sql id="Base_Column_List" >
    PAY_APPLY_ID, COMPANY_ID, PAY_TYPE, RELATED_ID, TRADER_SUBJECT, TRADER_MODE, TRADER_NAME, AMOUNT,
    CURRENCY_UNIT_ID, BANK, BANK_ACCOUNT, BANK_CODE, COMMENTS, VALID_STATUS, VALID_TIME, VALID_COMMENTS, IS_BILL,
    ADD_TIME, CREATOR, MOD_TIME, UPDATER,PAY_STATUS,PAY_BANKTYPE_ID,PAY_BANKTYPE_NAME,BANK_REMARK
  </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select
        <include refid="Base_Column_List" />
        from T_PAY_APPLY
        where PAY_APPLY_ID = #{payApplyId,jdbcType=INTEGER}
    </select>

    <select id="queryAutoPayConfig" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        select
        ENABLE_KING_DEE_AUTO_AUDIT
        from T_AUTO_PAY_CONFIG limit 1
    </select>

    <sql id="PayApply_Buyorder_Where_Str" >
        <if test="payApply.companyId!=null and payApply.companyId!=0">
            and a.COMPANY_ID = #{payApply.companyId}
        </if>
        <if test="payApply.buyorderNo!=null and payApply.buyorderNo!=''">
            and b.BUYORDER_NO like CONCAT('%',#{payApply.buyorderNo,jdbcType=VARCHAR},'%' )
        </if>
        <if test="payApply.buyorderTraderName!=null and payApply.buyorderTraderName!=''">
            and b.TRADER_NAME like CONCAT('%',#{payApply.buyorderTraderName,jdbcType=VARCHAR},'%' )
        </if>
        <if test="payApply.traderName!=null and payApply.traderName!=''">
            and a.TRADER_NAME like CONCAT('%',#{payApply.traderName,jdbcType=VARCHAR},'%' )
        </if>
        <if test="payApply.traderSubject!=null and payApply.traderSubject!=-1">
            and a.TRADER_SUBJECT = #{payApply.traderSubject,jdbcType=BIT}
        </if>
        <if test="payApply.traderMode!=null and payApply.traderMode!=-1">
            and a.TRADER_MODE = #{payApply.traderMode,jdbcType=INTEGER}
        </if>
        <if test="payApply.is_528 !=null and payApply.is_528 == 0">
            and a.TRADER_MODE <![CDATA[ != ]]> 528
        </if>
        <if test="payApply.is_528 !=null and payApply.is_528 == 1">
            and a.TRADER_MODE = 528
        </if>
        <if test="payApply.validStatus!=null and payApply.validStatus!=-1">
            and e.STATUS = #{payApply.validStatus,jdbcType=INTEGER}
        </if>
        <if test="payApply.isBill!=null and payApply.isBill!=-1">
            and a.IS_BILL = #{payApply.isBill,jdbcType=INTEGER}
        </if>
        <if test="payApply.searchBegintime!=null and payApply.searchBegintime!=0">
            and a.ADD_TIME &gt;= #{payApply.searchBegintime,jdbcType=BIGINT}
        </if>
        <if test="payApply.searchEndtime!=null and payApply.searchEndtime!=0">
            and a.ADD_TIME &lt;= #{payApply.searchEndtime,jdbcType=BIGINT}
        </if>
        <if test="payApply.searchBeginAmount!=null and payApply.searchBeginAmount!=0">
            and a.AMOUNT &gt;= #{payApply.searchBeginAmount,jdbcType=DECIMAL}
        </if>
        <if test="payApply.searchEndAmount!=null and payApply.searchEndAmount!=0">
            and a.AMOUNT &lt;= #{payApply.searchEndAmount,jdbcType=DECIMAL}
        </if>
        <if test="payApply.search != null">
            AND a.TRADER_MODE = 521 AND (b.BUYORDER_NO like CONCAT('%',#{payApply.search,jdbcType=VARCHAR},'%' ) OR a.TRADER_NAME like CONCAT('%',#{payApply.search,jdbcType=VARCHAR},'%' ))
        </if>
        <if test="payApply.validUserName!=null and payApply.validUserName!=''">
            AND FIND_IN_SET(#{payApply.validUserName,jdbcType=VARCHAR},e.VERIFY_USERNAME)
        </if>
        <!-- add by Tomcat.Hui 2019/9/11 13:05 .Desc: VDERP-1215 付款申请增加批量操作功能. start -->
        <if test="payApply.comments!=null and payApply.comments!=''">
            <choose>
                <when test="payApply.comments == 1">
                    AND length(a.COMMENTS) > 0
                </when>
                <when test="payApply.comments == 0">
                    AND (length(a.COMMENTS) = 0 OR a.COMMENTS is null)
                </when>
            </choose>
        </if>
        <!-- add by Tomcat.Hui 2019/9/11 13:05 .Desc: . end -->
        <if test="payApply.accountType!=null and payApply.accountType!=-1">
            AND a.ACCOUNT_TYPE = #{payApply.accountType,jdbcType=INTEGER}
        </if>
        <if test="payApply.autoBill!=null and payApply.autoBill!=-1">
            AND a.AUTO_BILL = #{payApply.autoBill,jdbcType=INTEGER}
        </if>
        <if test="payApply.billMethod!=null and payApply.billMethod!=-1">
            AND a.BILL_METHOD = #{payApply.billMethod,jdbcType=INTEGER}
        </if>
    </sql>

    <sql id="PayApply_After_Sales_Where_Str" >
        <if test="payApply.companyId!=null and payApply.companyId!=0">
            and a.COMPANY_ID = #{payApply.companyId}
        </if>
        <if test="payApply.buyorderNo!=null and payApply.buyorderNo!=''">
            and b.AFTER_SALES_NO like CONCAT('%',#{payApply.buyorderNo,jdbcType=VARCHAR},'%' )
        </if>
        <if test="payApply.buyorderTraderName!=null and payApply.buyorderTraderName!=''">
            and 1 = 2
        </if>
        <if test="payApply.traderName!=null and payApply.traderName!=''">
            and a.TRADER_NAME like CONCAT('%',#{payApply.traderName,jdbcType=VARCHAR},'%' )
        </if>
        <if test="payApply.traderSubject!=null and payApply.traderSubject!=-1">
            and a.TRADER_SUBJECT = #{payApply.traderSubject,jdbcType=BIT}
        </if>
        <if test="payApply.traderMode!=null and payApply.traderMode!=-1">
            and a.TRADER_MODE = #{payApply.traderMode,jdbcType=INTEGER}
        </if>
        <if test="payApply.is_528 !=null and payApply.is_528 == 0">
            and a.TRADER_MODE <![CDATA[ != ]]> 528
        </if>
        <if test="payApply.is_528 !=null and payApply.is_528 == 1">
            and a.TRADER_MODE = 528
        </if>
        <if test="payApply.validStatus!=null and payApply.validStatus!=-1">
            and e.STATUS = #{payApply.validStatus,jdbcType=INTEGER}
        </if>
        <if test="payApply.isBill!=null and payApply.isBill!=-1">
            and a.IS_BILL = #{payApply.isBill,jdbcType=INTEGER}
        </if>
        <if test="payApply.searchBegintime!=null and payApply.searchBegintime!=0">
            and a.ADD_TIME &gt;= #{payApply.searchBegintime,jdbcType=BIGINT}
        </if>
        <if test="payApply.searchEndtime!=null and payApply.searchEndtime!=0">
            and a.ADD_TIME &lt;= #{payApply.searchEndtime,jdbcType=BIGINT}
        </if>
        <if test="payApply.searchBeginAmount!=null and payApply.searchBeginAmount!=0">
            and a.AMOUNT &gt;= #{payApply.searchBeginAmount,jdbcType=DECIMAL}
        </if>
        <if test="payApply.searchEndAmount!=null and payApply.searchEndAmount!=0">
            and a.AMOUNT &lt;= #{payApply.searchEndAmount,jdbcType=DECIMAL}
        </if>
        <if test="payApply.search != null">
            AND a.TRADER_MODE = 521 AND (b.AFTER_SALES_NO like CONCAT('%',#{payApply.search,jdbcType=VARCHAR},'%' ) OR a.TRADER_NAME like CONCAT('%',#{payApply.search,jdbcType=VARCHAR},'%' ))
        </if>
        <if test="payApply.validUserName!=null and payApply.validUserName!=''">
            AND FIND_IN_SET(#{payApply.validUserName,jdbcType=VARCHAR},e.VERIFY_USERNAME)
        </if>
        <!-- add by Tomcat.Hui 2019/9/11 13:05 .Desc: VDERP-1215 付款申请增加批量操作功能. start -->
        <if test="payApply.comments!=null and payApply.comments!=''">
            <choose>
                <when test="payApply.comments == 1">
                    AND length(a.COMMENTS) > 0
                </when>
                <when test="payApply.comments == 0">
                    AND ( length(a.COMMENTS) = 0 OR a.COMMENTS is null)
                </when>
            </choose>
        </if>
        <if test="payApply.accountType!=null and payApply.accountType!=-1">
            AND a.ACCOUNT_TYPE = #{payApply.accountType,jdbcType=INTEGER}
        </if>
        <if test="payApply.autoBill!=null and payApply.autoBill!=-1">
            AND a.AUTO_BILL = #{payApply.autoBill,jdbcType=INTEGER}
        </if>
        <if test="payApply.billMethod!=null and payApply.billMethod!=-1">
            AND a.BILL_METHOD = #{payApply.billMethod,jdbcType=INTEGER}
        </if>
        <!-- add by Tomcat.Hui 2019/9/11 13:05 .Desc: . end -->
    </sql>
    <sql id="PayApply_BuyorderExpense_Where_Str" >
        <if test="payApply.companyId!=null and payApply.companyId!=0">
            and a.COMPANY_ID = #{payApply.companyId}
        </if>
        <if test="payApply.buyorderNo!=null and payApply.buyorderNo!=''">
            and b.BUYORDER_EXPENSE_NO like CONCAT('%',#{payApply.buyorderNo,jdbcType=VARCHAR},'%' )
        </if>
        <if test="payApply.buyorderTraderName!=null and payApply.buyorderTraderName!=''">
            and c.TRADER_NAME like CONCAT('%',#{payApply.buyorderTraderName,jdbcType=VARCHAR},'%' )
        </if>
        <if test="payApply.traderName!=null and payApply.traderName!=''">
            and c.TRADER_NAME like CONCAT('%',#{payApply.traderName,jdbcType=VARCHAR},'%' )
        </if>
        <if test="payApply.traderSubject!=null and payApply.traderSubject!=-1">
            and a.TRADER_SUBJECT = #{payApply.traderSubject,jdbcType=BIT}
        </if>
        <if test="payApply.traderMode!=null and payApply.traderMode!=-1">
            and a.TRADER_MODE = #{payApply.traderMode,jdbcType=INTEGER}
        </if>
        <if test="payApply.is_528 !=null and payApply.is_528 == 0">
            and a.TRADER_MODE <![CDATA[ != ]]> 528
        </if>
        <if test="payApply.is_528 !=null and payApply.is_528 == 1">
            and a.TRADER_MODE = 528
        </if>
        <if test="payApply.validStatus!=null and payApply.validStatus!=-1">
            and e.STATUS = #{payApply.validStatus,jdbcType=INTEGER}
        </if>
        <if test="payApply.isBill!=null and payApply.isBill!=-1">
            and a.IS_BILL = #{payApply.isBill,jdbcType=INTEGER}
        </if>
        <if test="payApply.searchBegintime!=null and payApply.searchBegintime!=0">
            and a.ADD_TIME &gt;= #{payApply.searchBegintime,jdbcType=BIGINT}
        </if>
        <if test="payApply.searchEndtime!=null and payApply.searchEndtime!=0">
            and a.ADD_TIME &lt;= #{payApply.searchEndtime,jdbcType=BIGINT}
        </if>
        <if test="payApply.searchBeginAmount!=null and payApply.searchBeginAmount!=0">
            and a.AMOUNT &gt;= #{payApply.searchBeginAmount,jdbcType=DECIMAL}
        </if>
        <if test="payApply.searchEndAmount!=null and payApply.searchEndAmount!=0">
            and a.AMOUNT &lt;= #{payApply.searchEndAmount,jdbcType=DECIMAL}
        </if>
        <if test="payApply.search != null">
            AND a.TRADER_MODE = 521 AND (b.BUYORDER_EXPENSE_NO like CONCAT('%',#{payApply.search,jdbcType=VARCHAR},'%' ) OR a.TRADER_NAME like CONCAT('%',#{payApply.search,jdbcType=VARCHAR},'%' ))
        </if>
        <if test="payApply.validUserName!=null and payApply.validUserName!=''">
            AND FIND_IN_SET(#{payApply.validUserName,jdbcType=VARCHAR},e.VERIFY_USERNAME)
        </if>
        <!-- add by Tomcat.Hui 2019/9/11 13:05 .Desc: VDERP-1215 付款申请增加批量操作功能. start -->
        <if test="payApply.comments!=null and payApply.comments!=''">
            <choose>
                <when test="payApply.comments == 1">
                    AND length(a.COMMENTS) > 0
                </when>
                <when test="payApply.comments == 0">
                    AND (length(a.COMMENTS) = 0 OR a.COMMENTS is null)
                </when>
            </choose>
        </if>
        <!-- add by Tomcat.Hui 2019/9/11 13:05 .Desc: . end -->
        <if test="payApply.accountType!=null and payApply.accountType!=-1">
            AND a.ACCOUNT_TYPE = #{payApply.accountType,jdbcType=INTEGER}
        </if>
        <if test="payApply.autoBill!=null and payApply.autoBill!=-1">
            AND a.AUTO_BILL = #{payApply.autoBill,jdbcType=INTEGER}
        </if>
        <if test="payApply.billMethod!=null and payApply.billMethod!=-1">
            AND a.BILL_METHOD = #{payApply.billMethod,jdbcType=INTEGER}
        </if>
    </sql>

    <select id="getPayApplyMaxRecord" resultMap="BaseResultMap">
       SELECT * FROM  T_PAY_APPLY
       WHERE RELATED_ID=#{relatedId}
       AND  PAY_TYPE=#{payType}
       AND ADD_TIME=(SELECT MAX(ADD_TIME) ss FROM T_PAY_APPLY WHERE
           RELATED_ID=#{relatedId})
    </select>

    <select id="getPayApplyRecord" resultMap="BaseResultMap">
        SELECT PAY_STATUS FROM  T_PAY_APPLY
       where PAY_APPLY_ID=#{payApplyId}
       AND  PAY_TYPE=#{payType}
    </select>

    <update id="updatePayStutas" parameterType="com.vedeng.finance.model.PayApply">
        UPDATE
        T_PAY_APPLY
        <set>
            <if test="payStatus!=null">
                PAY_STATUS=#{payStatus}
            </if>
        </set>
        WHERE
            PAY_APPLY_ID=#{payApplyId}
    </update>

    <update id="updateBillMethod" parameterType="com.vedeng.finance.model.PayApply">
        UPDATE
        T_PAY_APPLY
        set
        BILL_TIME = #{billTime}, BILL_METHOD = #{billMethod}
        WHERE
        PAY_APPLY_ID=#{payApplyId}
    </update>

    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.finance.model.PayApply">
        update T_PAY_APPLY
        <set >
            <if test="companyId != null" >
                COMPANY_ID = #{companyId,jdbcType=INTEGER},
            </if>
            <if test="payType != null" >
                PAY_TYPE = #{payType,jdbcType=INTEGER},
            </if>
            <if test="relatedId != null" >
                RELATED_ID = #{relatedId,jdbcType=INTEGER},
            </if>
            <if test="traderSubject != null" >
                TRADER_SUBJECT = #{traderSubject,jdbcType=BIT},
            </if>
            <if test="traderMode != null" >
                TRADER_MODE = #{traderMode,jdbcType=INTEGER},
            </if>
            <if test="traderName != null" >
                TRADER_NAME = #{traderName,jdbcType=VARCHAR},
            </if>
            <if test="amount != null" >
                AMOUNT = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="currencyUnitId != null" >
                CURRENCY_UNIT_ID = #{currencyUnitId,jdbcType=INTEGER},
            </if>
            <if test="bank != null" >
                BANK = #{bank,jdbcType=VARCHAR},
            </if>
            <if test="bankAccount != null" >
                BANK_ACCOUNT = #{bankAccount,jdbcType=VARCHAR},
            </if>
            <if test="bankCode != null" >
                BANK_CODE = #{bankCode,jdbcType=VARCHAR},
            </if>
            <if test="comments != null" >
                COMMENTS = #{comments,jdbcType=VARCHAR},
            </if>
            <if test="validStatus != null" >
                VALID_STATUS = #{validStatus,jdbcType=BIT},
            </if>
            <if test="validTime != null" >
                VALID_TIME = #{validTime,jdbcType=BIGINT},
            </if>
            <if test="validComments != null" >
                VALID_COMMENTS = #{validComments,jdbcType=VARCHAR},
            </if>
            <if test="isBill != null" >
                IS_BILL = #{isBill,jdbcType=BIT},
            </if>
            <if test="addTime != null" >
                ADD_TIME = #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null" >
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null" >
                MOD_TIME = #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null" >
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
        </set>
        where PAY_APPLY_ID = #{payApplyId,jdbcType=INTEGER}
    </update>


    <!-- 付款申请总额 -->
    <select id="getPayApplyTotalAmount" parameterType="Map" resultType="java.math.BigDecimal">
        SELECT sum(m.AMOUNT) from (
        select
        a.*, b.BUYORDER_NO, '' as AFTER_SALES_NO, b.TRADER_NAME as BUYORDER_TRADER_NAME, e.VERIFIES_TYPE, e.VERIFY_USERNAME, IFNULL(e.STATUS, -1) AS VERIFY_STATUS
        from T_PAY_APPLY a
        join T_BUYORDER b on a.RELATED_ID = b.BUYORDER_ID
        LEFT JOIN T_VERIFIES_INFO e ON a.PAY_APPLY_ID = e.RELATE_TABLE_KEY AND e.RELATE_TABLE = 'T_PAY_APPLY'
        <where>
            a.PAY_TYPE = 517
            <include refid="PayApply_Buyorder_Where_Str" />
        </where>

        UNION ALL

        select
        a.*, '' as BUYORDER_NO, b.AFTER_SALES_NO, '' as BUYORDER_TRADER_NAME, e.VERIFIES_TYPE, e.VERIFY_USERNAME, IFNULL(e.STATUS, -1) AS VERIFY_STATUS
        from T_PAY_APPLY a
        join T_AFTER_SALES b on a.RELATED_ID = b.AFTER_SALES_ID
        LEFT JOIN T_VERIFIES_INFO e ON a.PAY_APPLY_ID = e.RELATE_TABLE_KEY AND e.RELATE_TABLE = 'T_PAY_APPLY'
        <where>
            a.PAY_TYPE = 518
            <include refid="PayApply_After_Sales_Where_Str" />
        </where>

        UNION ALL

        select
        a.*, b.BUYORDER_EXPENSE_NO AS BUYORDER_NO, '' as AFTER_SALES_NO, c.TRADER_NAME as BUYORDER_TRADER_NAME, e.VERIFIES_TYPE, e.VERIFY_USERNAME, IFNULL(e.STATUS, -1) AS VERIFY_STATUS
        from T_PAY_APPLY a
        join T_BUYORDER_EXPENSE b on a.RELATED_ID = b.BUYORDER_EXPENSE_ID
        JOIN T_BUYORDER_EXPENSE_DETAIL c ON b.BUYORDER_EXPENSE_ID = c.BUYORDER_EXPENSE_ID
        LEFT JOIN T_VERIFIES_INFO e ON a.PAY_APPLY_ID = e.RELATE_TABLE_KEY AND e.RELATE_TABLE = 'T_PAY_APPLY' AND e.VERIFIES_TYPE = 644
        LEFT JOIN T_USER U ON U.USER_ID=a.Creator
        <where>
            a.PAY_TYPE = 4125
            <include refid="PayApply_BuyorderExpense_Where_Str" />
        </where>
        ) m
    </select>

    <!-- 付款申请付款总额 -->
    <select id="getPayApplyPayTotalAmount" parameterType="Map" resultType="java.math.BigDecimal">
        SELECT sum(m.AMOUNT) from (
        select
        a.*, b.BUYORDER_NO, '' as AFTER_SALES_NO, b.TRADER_NAME as BUYORDER_TRADER_NAME, e.VERIFIES_TYPE, e.VERIFY_USERNAME, IFNULL(e.STATUS, -1) AS VERIFY_STATUS
        from T_PAY_APPLY a
        join T_BUYORDER b on a.RELATED_ID = b.BUYORDER_ID
        LEFT JOIN T_VERIFIES_INFO e ON a.PAY_APPLY_ID = e.RELATE_TABLE_KEY AND e.RELATE_TABLE = 'T_PAY_APPLY'
        <where>
            a.PAY_TYPE = 517
            <include refid="PayApply_Buyorder_Where_Str" />
        </where>

        UNION ALL

        select
        a.*, '' as BUYORDER_NO, b.AFTER_SALES_NO, '' as BUYORDER_TRADER_NAME, e.VERIFIES_TYPE, e.VERIFY_USERNAME, IFNULL(e.STATUS, -1) AS VERIFY_STATUS
        from T_PAY_APPLY a
        join T_AFTER_SALES b on a.RELATED_ID = b.AFTER_SALES_ID
        LEFT JOIN T_VERIFIES_INFO e ON a.PAY_APPLY_ID = e.RELATE_TABLE_KEY AND e.RELATE_TABLE = 'T_PAY_APPLY'
        <where>
            a.PAY_TYPE = 518
            <include refid="PayApply_After_Sales_Where_Str" />
        </where>
        UNION ALL

        select
        a.*, b.BUYORDER_EXPENSE_NO AS BUYORDER_NO, '' as AFTER_SALES_NO, c.TRADER_NAME as BUYORDER_TRADER_NAME,e.VERIFIES_TYPE, e.VERIFY_USERNAME, IFNULL(e.STATUS, -1) AS VERIFY_STATUS
        from T_PAY_APPLY a
        join T_BUYORDER_EXPENSE b on a.RELATED_ID = b.BUYORDER_EXPENSE_ID
        JOIN T_BUYORDER_EXPENSE_DETAIL c ON b.BUYORDER_EXPENSE_ID = c.BUYORDER_EXPENSE_ID
        LEFT JOIN T_VERIFIES_INFO e ON a.PAY_APPLY_ID = e.RELATE_TABLE_KEY AND e.RELATE_TABLE = 'T_PAY_APPLY' AND e.VERIFIES_TYPE = 644
        LEFT JOIN T_USER U ON U.USER_ID=a.Creator
        <where>
            a.PAY_TYPE = 4125
            <include refid="PayApply_BuyorderExpense_Where_Str" />
        </where>
        ) m
        where m.VALID_STATUS = 1
    </select>


    <!-- 付款申请列表（分页） -->
    <select id="getPayApplyListPage" resultMap="BaseResultMap" parameterType="Map" >
        SELECT * from (
        select
        U.USERNAME,a.*, b.BUYORDER_NO, '' as AFTER_SALES_NO, b.TRADER_NAME as BUYORDER_TRADER_NAME,b.TRADER_ID as TRADER_ID, e.VERIFIES_TYPE, e.VERIFY_USERNAME, IFNULL(e.STATUS, -1) AS VERIFY_STATUS
        from T_PAY_APPLY a
        join T_BUYORDER b on a.RELATED_ID = b.BUYORDER_ID
        LEFT JOIN T_VERIFIES_INFO e ON a.PAY_APPLY_ID = e.RELATE_TABLE_KEY AND e.RELATE_TABLE = 'T_PAY_APPLY' AND e.VERIFIES_TYPE = 644
        LEFT JOIN T_USER U ON U.USER_ID=a.Creator
        <where>
            a.PAY_TYPE = 517
            <include refid="PayApply_Buyorder_Where_Str" />
        </where>

        UNION ALL

        select
        U.USERNAME,a.*, '' as BUYORDER_NO, b.AFTER_SALES_NO, '' as BUYORDER_TRADER_NAME,'' as TRADER_ID, e.VERIFIES_TYPE, e.VERIFY_USERNAME, IFNULL(e.STATUS, -1) AS VERIFY_STATUS
        from T_PAY_APPLY a
        join T_AFTER_SALES b on a.RELATED_ID = b.AFTER_SALES_ID
        LEFT JOIN T_VERIFIES_INFO e ON a.PAY_APPLY_ID = e.RELATE_TABLE_KEY AND e.RELATE_TABLE = 'T_PAY_APPLY' AND e.VERIFIES_TYPE = 644
        LEFT JOIN T_USER U ON U.USER_ID=a.Creator
        <where>
            a.PAY_TYPE = 518
            <include refid="PayApply_After_Sales_Where_Str" />
        </where>

        UNION ALL
        select
        U.USERNAME,a.*, b.BUYORDER_EXPENSE_NO AS BUYORDER_NO, '' as AFTER_SALES_NO, c.TRADER_NAME as BUYORDER_TRADER_NAME,c.TRADER_ID as TRADER_ID, e.VERIFIES_TYPE, e.VERIFY_USERNAME, IFNULL(e.STATUS, -1) AS VERIFY_STATUS
        from T_PAY_APPLY a
        join T_BUYORDER_EXPENSE b on a.RELATED_ID = b.BUYORDER_EXPENSE_ID
        JOIN T_BUYORDER_EXPENSE_DETAIL c ON b.BUYORDER_EXPENSE_ID = c.BUYORDER_EXPENSE_ID
        LEFT JOIN T_VERIFIES_INFO e ON a.PAY_APPLY_ID = e.RELATE_TABLE_KEY AND e.RELATE_TABLE = 'T_PAY_APPLY' AND e.VERIFIES_TYPE = 644
        LEFT JOIN T_USER U ON U.USER_ID=a.Creator
        <where>
            a.PAY_TYPE = 4125
            <include refid="PayApply_BuyorderExpense_Where_Str" />
        </where>
        ) m
        order by m.VALID_STATUS ASC, m.ADD_TIME ASC
    </select>


    <select id="getMatchInfo" resultType="com.vedeng.finance.model.BankBill">
        SELECT * FROM T_BANK_BILL bb
        <where>
            1=1
            <if test="accName1 != null and accName1 != ''">
                <!-- 对方账户名称 -->
                AND bb.ACC_NAME1 = #{accName1,jdbcType=VARCHAR}
            </if>
            <if test="amt != null and amt != ''">
                <!-- 发生额 -->
                AND bb.AMT = #{amt,jdbcType=DECIMAL}
            </if>
            <if test="searchBeginTime!=null">
                AND TRANDATE >= #{searchBeginTime,jdbcType=VARCHAR}
            </if>
            <if test="searchEndTime!=null">
                and TRANDATE <![CDATA[ <= ]]> #{searchEndTime,jdbcType=VARCHAR}
            </if>
            AND bb.FLAG1 = 0 AND bb.MATCHED_AMOUNT != bb.AMT
        </where>
    </select>
    <select id="getPayApplyMoreInfo" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select
        b.MOBILE, b.CARD
        from
        T_AFTER_SALES_INSTALLSTION a
        left join
        T_ENGINEER b on a.ENGINEER_ID = b.ENGINEER_ID
        where 1=1
        <if test="afterSalesId != null" >
            AND a.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
        </if>
        order by a.SERVICE_TIME desc
    </select>

    <insert id="insertSelective" parameterType="com.vedeng.finance.model.PayApply" useGeneratedKeys="true" keyProperty="payApplyId">
        insert into T_PAY_APPLY
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="payApplyId != null" >
                PAY_APPLY_ID,
            </if>
            <if test="companyId != null" >
                COMPANY_ID,
            </if>
            <if test="payType != null" >
                PAY_TYPE,
            </if>
            <if test="relatedId != null" >
                RELATED_ID,
            </if>
            <if test="traderSubject != null" >
                TRADER_SUBJECT,
            </if>
            <if test="traderMode != null" >
                TRADER_MODE,
            </if>
            <if test="traderName != null" >
                TRADER_NAME,
            </if>
            <if test="amount != null" >
                AMOUNT,
            </if>
            <if test="currencyUnitId != null" >
                CURRENCY_UNIT_ID,
            </if>
            <if test="bank != null" >
                BANK,
            </if>
            <if test="bankAccount != null" >
                BANK_ACCOUNT,
            </if>
            <if test="bankCode != null" >
                BANK_CODE,
            </if>
            <if test="comments != null" >
                COMMENTS,
            </if>
            <if test="validStatus != null" >
                VALID_STATUS,
            </if>
            <if test="validTime != null" >
                VALID_TIME,
            </if>
            <if test="validComments != null" >
                VALID_COMMENTS,
            </if>
            <if test="isBill != null" >
                IS_BILL,
            </if>
            <if test="addTime != null" >
                ADD_TIME,
            </if>
            <if test="creator != null" >
                CREATOR,
            </if>
            <if test="modTime != null" >
                MOD_TIME,
            </if>
            <if test="updater != null" >
                UPDATER,
            </if>
            <if test="bankRemark != null and bankRemark != ''">
                BANK_REMARK,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="payApplyId != null" >
                #{payApplyId,jdbcType=INTEGER},
            </if>
            <if test="companyId != null" >
                #{companyId,jdbcType=INTEGER},
            </if>
            <if test="payType != null" >
                #{payType,jdbcType=INTEGER},
            </if>
            <if test="relatedId != null" >
                #{relatedId,jdbcType=INTEGER},
            </if>
            <if test="traderSubject != null" >
                #{traderSubject,jdbcType=BIT},
            </if>
            <if test="traderMode != null" >
                #{traderMode,jdbcType=INTEGER},
            </if>
            <if test="traderName != null" >
                #{traderName,jdbcType=VARCHAR},
            </if>
            <if test="amount != null" >
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="currencyUnitId != null" >
                #{currencyUnitId,jdbcType=INTEGER},
            </if>
            <if test="bank != null" >
                #{bank,jdbcType=VARCHAR},
            </if>
            <if test="bankAccount != null" >
                #{bankAccount,jdbcType=VARCHAR},
            </if>
            <if test="bankCode != null" >
                #{bankCode,jdbcType=VARCHAR},
            </if>
            <if test="comments != null" >
                #{comments,jdbcType=VARCHAR},
            </if>
            <if test="validStatus != null" >
                #{validStatus,jdbcType=BIT},
            </if>
            <if test="validTime != null" >
                #{validTime,jdbcType=BIGINT},
            </if>
            <if test="validComments != null" >
                #{validComments,jdbcType=VARCHAR},
            </if>
            <if test="isBill != null" >
                #{isBill,jdbcType=BIT},
            </if>
            <if test="addTime != null" >
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null" >
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null" >
                #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null" >
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="bankRemark != null and bankRemark != ''">
                #{bankRemark,jdbcType=VARCHAR},
            </if>
        </trim>
        <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="payApplyId">
            SELECT LAST_INSERT_ID() AS payApplyId
        </selectKey>
    </insert>

    <select id="queryPassedByBuyorderExpenseGoodsId" resultMap="passedBuyorderExpenseGoodsIdResult" parameterType="java.lang.Integer">
        SELECT SUM(B.NUM) AS PASSED_NUM, SUM(B.TOTAL_AMOUNT) AS PASSED_AMOUNT
        FROM T_PAY_APPLY A
            JOIN T_PAY_APPLY_DETAIL B ON A.PAY_APPLY_ID = B.PAY_APPLY_ID
        WHERE A.VALID_STATUS = 1
          AND B.DETAILGOODS_ID = #{buyorderExpenseGoodsId,jdbcType=INTEGER}
          AND A.PAY_TYPE = 4125
    </select>

    <!-- 获取付款申请（不分页） -->
    <select id="getPayApplyList" resultMap="BaseResultMap" parameterType="com.vedeng.finance.model.PayApply" >
        select
        a.*
        from T_PAY_APPLY a
        <where>
            1=1
            <if test="companyId != null and companyId != ''">
                <!-- 订单状态 -->
                AND a.COMPANY_ID = #{companyId,jdbcType=INTEGER}
            </if>
            <if test="payType != null and payType != ''">
                AND a.PAY_TYPE = #{payType,jdbcType=INTEGER}
            </if>
            <if test="relatedId != null and relatedId != ''">
                AND a.RELATED_ID = #{relatedId,jdbcType=INTEGER}
            </if>
            <if test="validStatus != null">
                AND a.VALID_STATUS = #{validStatus,jdbcType=INTEGER}
            </if>
        </where>
        order by a.PAY_APPLY_ID DESC
    </select>

    <select id="getApplyPayTotalAmountByTraderSupplierId" parameterType="java.lang.Integer" resultType="java.math.BigDecimal">
        SELECT
            COALESCE(SUM(a.AMOUNT),0)
        FROM T_PAY_APPLY a
                 LEFT JOIN T_BUYORDER b ON a.RELATED_ID = b.BUYORDER_ID
        WHERE (a.PAY_TYPE = 517 OR a.PAY_TYPE = 4125) AND a.TRADER_MODE = 528
          AND a.VALID_STATUS = 0 AND b.VALID_STATUS = 1 AND b.STATUS = 1
          AND b.TRADER_ID = #{traderId,jdbcType=INTEGER} and a.COMPANY_ID = #{companyId,jdbcType=INTEGER}
    </select>

    <select id="getPayVedengBankList" resultType="com.vedeng.finance.dto.PayVedengBankDto">
        SELECT *
        FROM T_PAY_VEDENG_BANK
        WHERE IS_DELETE = 0
    </select>

    <update id="updatePayApplyIsBillInfo">
        update T_PAY_APPLY
        SET
            PAY_BANKTYPE_ID = #{payVedengBankId,jdbcType=INTEGER},
            PAY_BANKTYPE_NAME =  #{payBankTypeName,jdbcType=VARCHAR},
            VALID_COMMENTS =  #{validComments,jdbcType=VARCHAR}
        where PAY_APPLY_ID = #{payApplyId,jdbcType=INTEGER}
    </update>

    <update id="updatePayApplyComment">
        update T_PAY_APPLY
        SET
        VALID_COMMENTS =  #{validComments,jdbcType=VARCHAR}
        where PAY_APPLY_ID = #{payApplyId,jdbcType=INTEGER}
    </update>

    <select id="getPayVedengBankByPayBankId" resultType="com.vedeng.finance.dto.PayVedengBankDto">
        SELECT *
        FROM T_PAY_VEDENG_BANK
        WHERE PAY_VEDENG_BANK_ID = #{payVedengBankId,jdbcType=INTEGER}
    </select>
    <select id="getPayApplyListById" resultType="com.vedeng.finance.model.PayApply">
        SELECT *
        FROM T_PAY_APPLY a
        WHERE 1=1
        AND a.PAY_APPLY_ID in
        <foreach item="id" index="index" collection="ids" separator="," open="(" close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </select>
    <select id="getPayApplyListByOrderIdAndPayType" resultType="com.vedeng.finance.model.PayApply">
       SELECT *
        FROM T_PAY_APPLY a
        WHERE a.PAY_TYPE = #{payType,jdbcType=INTEGER}
        and a.RELATED_ID = #{relatedId,jdbcType=INTEGER}
        and a.VALID_STATUS = #{validStatus,jdbcType=INTEGER}
    </select>
</mapper>
