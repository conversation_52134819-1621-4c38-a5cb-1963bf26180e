<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
            + path + "/";
%>
<c:set var="path" value="<%=basePath%>" scope="application" />

<style>


</style>
<div id="app">
    <br>
    <br>
    <el-form ref="form" :model="traderCustomerTerminalChecked" size="small" label-width="120px">

        <el-form-item label="终端名称">
            <span>{{traderCustomerTerminalChecked.name}}</span>
        </el-form-item>
        <el-form-item label="沟通录音ID" style="width: 350px">
            <el-input v-model="traderCustomerTerminalChecked.cuid" maxlength="9" placeholder="请输入沟通记录录音ID"></el-input>
        </el-form-item>
        <el-form-item label="聊天记录截图" style="width: 1200px;">
            <el-upload
                    action="/orderstream/saleorder/uploadImg.do"
                    list-type="picture-card"
                    :on-preview="handlePictureCardPreview"
                    :on-success="(res,file, fileList) => handleAvatarSuccess(res,file, fileList, traderCustomerTerminalChecked)"
                    :on-remove="(file, fileList) => handleRemove(file, fileList,traderCustomerTerminalChecked)"
                    :before-upload="beforeAvatarUpload"
                    accept="image/jpg,image/jpeg,image/png"
                    limit="3"
                    :file-list="traderCustomerTerminalChecked.rowFileList"
            >
                <i class="el-icon-plus"></i>
            </el-upload>
            <el-dialog :visible.sync="pictureDialogVisible">
                <img width="100%" :src="followPic" alt="">
            </el-dialog>
        </el-form-item>
        <el-form-item  style="width: 300px">
            <span style="color: grey">最多可添加三张图片</span>
        </el-form-item>
        <el-form-item>
            <el-button type="primary" size="small" style="margin-left: 0px" :loading="subLoaging" @click="submitForm('form')">提交</el-button>
        </el-form-item>

    </el-form>


</div>

<script src="${pageContext.request.contextPath}/static/api/trader/traderCustomerTerminal.js?rnd=${resourceVersionKey}"></script>

<script type="text/javascript">
    const viewData={
        traderCustomerTerminalId : '${traderCustomerTerminalId}'
    }
    const sendThis = (_this) => {
        vm = _this;
    }
    new Vue({
        el: '#app',
        data() {


            return {
                queryLoading:false,
                radio:'',
                query:'',
                subLoaging:false,
                traderCustomerTerminalChecked:{},
                //图片展示
                pictureDialogVisible: false,
                followPic:'',
                fileList: [],

            }
        },

        mounted() {
            loadingApp()
        },

        created() {
            sendThis(this);
            this.getTerminalData();
        },

        methods: {
            getTerminalData() {
                traderCustomerTerminalData({traderCustomerTerminalId:viewData.traderCustomerTerminalId}).then(res=>{
                    res.data.data;

                    let pic2List = res.data.data.pic2List;
                    let x = {
                        name:res.data.data.terminalName,
                        cuid:res.data.data.communicateRecordId==0?'':res.data.data.communicateRecordId,
                        rowFileList:[],
                        traderId:res.data.data.traderId,
                        traderCustomerId:res.data.data.traderCustomerId,
                        uniqueId:res.data.data.dwhTerminalId,
                        traderCustomerTerminalId:res.data.data.traderCustomerTerminalId,
                        fileList:[]
                    };
                    if (pic2List !== null && pic2List !== undefined && pic2List.length > 0) {
                        pic2List.forEach(a=>{
                            x.fileList.push({filePath:a})
                            x.rowFileList.push({
                                "name": "",
                                "url":a,
                                "response": {
                                    "ossUrl":a
                                }
                            })
                        })
                    }


                    this.traderCustomerTerminalChecked = x;

                })
            },
            handlePictureCardPreview(file) {
                this.followPic = file.url;
                this.pictureDialogVisible = true;
            },
            // 上传成功
            handleAvatarSuccess(res,file, fileList,terminal) {
                terminal.fileList.push({filePath:res.ossUrl})
                this.subLoaging = false;
            },
            // 删除
            handleRemove(file, fileList, terminal) {
                let index = terminal.fileList.findIndex(i=>i.filePath == file.response.ossUrl);
                terminal.fileList.splice(index, 1);
            },
            // 校验
            beforeAvatarUpload(file) {

                var img = file.name.substring(file.name.lastIndexOf('.') + 1)
                img = img.toLocaleLowerCase();
                const suffix = img === 'jpg'
                const suffix2 = img === 'png'
                const suffix3 = img === 'jpeg'
                if (!suffix && !suffix2 && !suffix3) {
                    this.$message.error("只能上传图片！");
                    return false
                }

                const isLt2M = file.size / 1024 / 1024 <= 5;
                if (!isLt2M) {
                    this.$message.error('上传图片大小不能超过 5MB!');
                    return false
                }
                this.subLoaging = true;
                return true;
            },
            closeThis() {
                window.parent.closableTab.close($(window.parent.document).find('[role=presentation].active .glyphicon.small'), window.parent.closableTab.resizeMove);
            },
            checkNumber(data) {
                var re = /^[1-9]\d*$/;
                return re.test(data);
            },
            submitForm(formName) {

                let flag = true;
                let that = this;
                let item = this.traderCustomerTerminalChecked;
                if (item.cuid == '' && item.fileList.length == 0) {
                    this.$message.error(item.name+" 沟通记录ID和聊天记录截图至少填写一项");
                    flag = false;
                }
                if (item.cuid != '') {
                    if (!this.checkNumber(item.cuid)) {
                        this.$message.error(item.name+" 沟通记录ID只能为正整数");
                        flag = false;
                    }
                }
                if (flag) {
                    this.subLoaging = true;
                    let da =  {
                        traderId:item.traderId,
                        traderCustomerId:item.traderCustomerId,
                        terminalName:item.name,
                        dwhTerminalId:item.uniqueId,
                        communicateRecordId:item.cuid==''?0:item.cuid,
                        traderCustomerTerminalId:item.traderCustomerTerminalId,
                        picUrl: item.fileList.length > 0 ? item.fileList.map(item => item.filePath).join(','): ''
                    };



                    traderCustomerTerminalUpdate(da).then(res => {
                        if (res.data.code != 0) {
                            this.$message.error(res.data.message);
                            this.subLoaging = false;
                        } else {
                            this.$message({
                                message: '保存成功',
                                type: 'success',
                                duration:1000,
                                onClose: () => {
                                    parentWindow[0].location.reload(true);
                                }
                            });
                        }

                    });


                } else {
                    return false;
                }

            }


        }
    })
</script>
