package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.annotation.KingDeeID;
import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 推送金蝶收款单dto
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@WriteBackField(needBackField = {"FID"})
public class KingDeeAliReceiveBillDto extends KingDeeMqBaseDto  {
    private Integer kingDeeReceiveBillId;

    /**
     * 是否自动提交审核
     */
    private Boolean isAutoSubmitAndAudit;
    /**
     * 是否自动调整JSON字段顺序
     */
    private Boolean isAutoAdjustField;
    /**
     * 单据内码
     */
    @WriteBackField("FID")
    @KingDeeID
    private Integer fId;
    /**
     * 单据编号
     */
    @BusinessID
    private String fBillNo;
    /**
     * 单据类型
     */
    private String fBillTypeId;
    /**
     * 收款组织
     */
    private String fPayOrgId;
    /**
     * 单据日期
     */
    private String fDate;
    /**
     * 银行流水号
     */
    private String fQzokLsh;
    /**
     * 往来单位类型
     */
    private String fContactUnitType;
    /**
     * 往来单位
     */
    private String fContactUnit;
    /**
     * 交易主体
     */
    private String fQzokJyzt;
    /**
     * 交易类型
     */
    private String fQzokJylx;
    /**
     * 交易方式
     */
    private String fQzokJyfs;

    private Integer bankBillId;

    /**
     * 支付宝回单url地址
     */
    private String receiptUrl;

    private List<KingDeeAliReceiveBillEntryDto> fReceiveBillEntry;
    @Override
    public String getFormId() {
        return KingDeeFormConstant.RECEIVE_BILL;
    }
    public KingDeeAliReceiveBillDto(){
        this.fPayOrgId = KingDeeConstant.ORG_ID.toString();
    }
}
