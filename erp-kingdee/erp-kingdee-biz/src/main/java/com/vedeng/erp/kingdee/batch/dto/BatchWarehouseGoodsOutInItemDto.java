package com.vedeng.erp.kingdee.batch.dto;

import java.math.BigDecimal;

import com.vedeng.common.core.base.BaseDto;
import com.vedeng.erp.kingdee.batch.common.bean.BatchBaseDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description 出入库明细表
 * @date 2022/12/9 12:06
 **/

@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchWarehouseGoodsOutInItemDto extends BatchBaseDto {
    /**
     * 主键
     */
    private Long warehouseGoodsOutInDetailId;

    /**
     * ERP出入库单号
     */
    private String outInNo;

    /**
     * WMS出入库单号
     */
    private String wmsNo;

    /**
     * 产品条码ID
     */
    private Integer barcodeId;

    /**
     * 公司ID
     */
    private Integer companyId;

    /**
     * 日志类型 0入库 1出库
     */
    private Integer logType;

    /**
     * 操作类型 1采购入库 2销售出库 3销售换货入库 4销售换货出库 5销售退货入库 6采购退货出库 7采购换货出库 8采购换货入库 9外借入库 10外借出库  12盘盈入库 13报废出库 14领用出库 16 盘亏出库
     */
    private Integer operateType;

    /**
     * 关联采购、销售、售后产品ID
     */
    private Integer relatedId;

    /**
     * 出库时对应拣货单详细ID
     */
    private Integer warehousePickingDetailId;

    /**
     * 商品ID
     */
    private Integer goodsId;

    /**
     * SN码
     */
    private String barcodeFactory;

    /**
     * 产品数量
     */
    private BigDecimal num;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 库房ID
     */
    private Integer storageRoomId;

    /**
     * 货区ID
     */
    private Integer storageAreaId;

    /**
     * 库位ID
     */
    private Integer storageLocationId;

    /**
     * 货架ID
     */
    private Integer storageRackId;

    /**
     * 厂商批号
     */
    private String batchNumber;

    /**
     * 效期
     */
    private String expirationDate;

    /**
     * 入库验收 0未验收 1验收通过 2不通过
     */
    private Integer checkStatus;

    /**
     * 入库验收人
     */
    private Integer checkStatusUser;

    /**
     * 入库时间
     */
    private String checkStatusTime;

    /**
     * 出库复核 0未复核 1通过 2不通过
     */
    private Integer recheckStatus;

    /**
     * 出库复核人
     */
    private Integer recheckStatusUser;

    /**
     * 出库复核时间
     */
    private String recheckStatusTime;

    /**
     * 是否已绑定快递 0否 1是
     */
    private Integer isExpress;

    /**
     * 0 否 1 是
     */
    private Integer isProblem;

    /**
     * 问题备注
     */
    private String problemRemark;

    /**
     * 生产日期
     */
    private String productDate;

    /**
     * 成本价
     */
    private BigDecimal costPrice;

    /**
     * 入库条码是否使用 0未使用 1使用
     */
    private Integer isUse;

    /**
     * 逻辑仓id
     */
    private Integer logicalWarehouseId;

    /**
     * 贝登批次码
     */
    private String vedengBatchNumber;

    /**
     * 剩余库存数量
     */
    private Integer lastStockNum;

    /**
     * 灭菌批号
     */
    private String sterilizationBatchNumber;

    /**
     * 新成本价
     */
    private BigDecimal newCostPrice;

    /**
     * 专向发货关联VP单号
     */
    private String dedicatedBuyorderNo;

    /**
     * 是否删除 0否 1是
     */
    private Integer isDelete;

    /**
     * 备注
     */
    private String remark;

    /**
     * 更新备注
     */
    private String updateRemark;

    /**
     * sku
     */
    private String skuNo;

    /**
     * 出库单关联的销售单中的此商品的销售单价
     */
    private BigDecimal price;

    /**
     * 出库单关联的销售单中的税率
     */
    private BigDecimal ratio;

    /**
     * 是否直发
     */
    private Integer deliveryDirect;

    /**
     * 是否包含安调 0否 1是
     */
    private Integer haveInstallation;

    /**
     * 是否赠品
     */
    private Integer isGift;
    /**
     * 虚拟出入库记录红票商品明细id
     */
    private Integer invoiceDetailId;

    public Long warehouseGoodsOutInId;

    /**
     * sku 编码
     */
    private String sku;
}