package com.pricecenter.dto;

import java.math.BigDecimal;
import java.util.List;

public class SkuReferencePriceResponseDto {

    private Long id;

    private String skuNo;

    private BigDecimal referenceTerminalPrice;

    private BigDecimal referenceDistributionPrice;

    private BigDecimal referenceGroupPrice;

    private String belongProductManager;

    private String belongProductAssit;

    private String skuName;

    private String addTime;

    private String modTime;

    //单位
    private String unitName;

    private List<SkuReferencePriceHistoryDto> historyList;

    public String getSkuNo() {
        return skuNo;
    }

    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }

    public BigDecimal getReferenceTerminalPrice() {
        return referenceTerminalPrice;
    }

    public void setReferenceTerminalPrice(BigDecimal referenceTerminalPrice) {
        this.referenceTerminalPrice = referenceTerminalPrice;
    }

    public BigDecimal getReferenceDistributionPrice() {
        return referenceDistributionPrice;
    }

    public void setReferenceDistributionPrice(BigDecimal referenceDistributionPrice) {
        this.referenceDistributionPrice = referenceDistributionPrice;
    }

    public BigDecimal getReferenceGroupPrice() {
        return referenceGroupPrice;
    }

    public void setReferenceGroupPrice(BigDecimal referenceGroupPrice) {
        this.referenceGroupPrice = referenceGroupPrice;
    }

    public String getBelongProductManager() {
        return belongProductManager;
    }

    public void setBelongProductManager(String belongProductManager) {
        this.belongProductManager = belongProductManager;
    }

    public String getBelongProductAssit() {
        return belongProductAssit;
    }

    public void setBelongProductAssit(String belongProductAssit) {
        this.belongProductAssit = belongProductAssit;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getModTime() {
        return modTime;
    }

    public void setModTime(String modTime) {
        this.modTime = modTime;
    }

    public String getAddTime() {
        return addTime;
    }

    public void setAddTime(String addTime) {
        this.addTime = addTime;
    }

    public List<SkuReferencePriceHistoryDto> getHistoryList() {
        return historyList;
    }

    public void setHistoryList(List<SkuReferencePriceHistoryDto> historyList) {
        this.historyList = historyList;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }
}
