package com.vedeng.goods.model;

import java.util.ArrayList;
import java.util.List;

public class FirstEngageGenerateExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_FIRST_ENGAGE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_FIRST_ENGAGE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_FIRST_ENGAGE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public FirstEngageGenerateExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_FIRST_ENGAGE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andFirstEngageIdIsNull() {
            addCriterion("FIRST_ENGAGE_ID is null");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdIsNotNull() {
            addCriterion("FIRST_ENGAGE_ID is not null");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdEqualTo(Integer value) {
            addCriterion("FIRST_ENGAGE_ID =", value, "firstEngageId");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdNotEqualTo(Integer value) {
            addCriterion("FIRST_ENGAGE_ID <>", value, "firstEngageId");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdGreaterThan(Integer value) {
            addCriterion("FIRST_ENGAGE_ID >", value, "firstEngageId");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("FIRST_ENGAGE_ID >=", value, "firstEngageId");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdLessThan(Integer value) {
            addCriterion("FIRST_ENGAGE_ID <", value, "firstEngageId");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdLessThanOrEqualTo(Integer value) {
            addCriterion("FIRST_ENGAGE_ID <=", value, "firstEngageId");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdIn(List<Integer> values) {
            addCriterion("FIRST_ENGAGE_ID in", values, "firstEngageId");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdNotIn(List<Integer> values) {
            addCriterion("FIRST_ENGAGE_ID not in", values, "firstEngageId");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdBetween(Integer value1, Integer value2) {
            addCriterion("FIRST_ENGAGE_ID between", value1, value2, "firstEngageId");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdNotBetween(Integer value1, Integer value2) {
            addCriterion("FIRST_ENGAGE_ID not between", value1, value2, "firstEngageId");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdIsNull() {
            addCriterion("REGISTRATION_NUMBER_ID is null");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdIsNotNull() {
            addCriterion("REGISTRATION_NUMBER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdEqualTo(Integer value) {
            addCriterion("REGISTRATION_NUMBER_ID =", value, "registrationNumberId");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdNotEqualTo(Integer value) {
            addCriterion("REGISTRATION_NUMBER_ID <>", value, "registrationNumberId");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdGreaterThan(Integer value) {
            addCriterion("REGISTRATION_NUMBER_ID >", value, "registrationNumberId");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("REGISTRATION_NUMBER_ID >=", value, "registrationNumberId");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdLessThan(Integer value) {
            addCriterion("REGISTRATION_NUMBER_ID <", value, "registrationNumberId");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdLessThanOrEqualTo(Integer value) {
            addCriterion("REGISTRATION_NUMBER_ID <=", value, "registrationNumberId");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdIn(List<Integer> values) {
            addCriterion("REGISTRATION_NUMBER_ID in", values, "registrationNumberId");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdNotIn(List<Integer> values) {
            addCriterion("REGISTRATION_NUMBER_ID not in", values, "registrationNumberId");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdBetween(Integer value1, Integer value2) {
            addCriterion("REGISTRATION_NUMBER_ID between", value1, value2, "registrationNumberId");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdNotBetween(Integer value1, Integer value2) {
            addCriterion("REGISTRATION_NUMBER_ID not between", value1, value2, "registrationNumberId");
            return (Criteria) this;
        }

        public Criteria andGoodsTypeIsNull() {
            addCriterion("GOODS_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andGoodsTypeIsNotNull() {
            addCriterion("GOODS_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsTypeEqualTo(Integer value) {
            addCriterion("GOODS_TYPE =", value, "goodsType");
            return (Criteria) this;
        }

        public Criteria andGoodsTypeNotEqualTo(Integer value) {
            addCriterion("GOODS_TYPE <>", value, "goodsType");
            return (Criteria) this;
        }

        public Criteria andGoodsTypeGreaterThan(Integer value) {
            addCriterion("GOODS_TYPE >", value, "goodsType");
            return (Criteria) this;
        }

        public Criteria andGoodsTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("GOODS_TYPE >=", value, "goodsType");
            return (Criteria) this;
        }

        public Criteria andGoodsTypeLessThan(Integer value) {
            addCriterion("GOODS_TYPE <", value, "goodsType");
            return (Criteria) this;
        }

        public Criteria andGoodsTypeLessThanOrEqualTo(Integer value) {
            addCriterion("GOODS_TYPE <=", value, "goodsType");
            return (Criteria) this;
        }

        public Criteria andGoodsTypeIn(List<Integer> values) {
            addCriterion("GOODS_TYPE in", values, "goodsType");
            return (Criteria) this;
        }

        public Criteria andGoodsTypeNotIn(List<Integer> values) {
            addCriterion("GOODS_TYPE not in", values, "goodsType");
            return (Criteria) this;
        }

        public Criteria andGoodsTypeBetween(Integer value1, Integer value2) {
            addCriterion("GOODS_TYPE between", value1, value2, "goodsType");
            return (Criteria) this;
        }

        public Criteria andGoodsTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("GOODS_TYPE not between", value1, value2, "goodsType");
            return (Criteria) this;
        }

        public Criteria andBrandTypeIsNull() {
            addCriterion("BRAND_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andBrandTypeIsNotNull() {
            addCriterion("BRAND_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andBrandTypeEqualTo(Byte value) {
            addCriterion("BRAND_TYPE =", value, "brandType");
            return (Criteria) this;
        }

        public Criteria andBrandTypeNotEqualTo(Byte value) {
            addCriterion("BRAND_TYPE <>", value, "brandType");
            return (Criteria) this;
        }

        public Criteria andBrandTypeGreaterThan(Byte value) {
            addCriterion("BRAND_TYPE >", value, "brandType");
            return (Criteria) this;
        }

        public Criteria andBrandTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("BRAND_TYPE >=", value, "brandType");
            return (Criteria) this;
        }

        public Criteria andBrandTypeLessThan(Byte value) {
            addCriterion("BRAND_TYPE <", value, "brandType");
            return (Criteria) this;
        }

        public Criteria andBrandTypeLessThanOrEqualTo(Byte value) {
            addCriterion("BRAND_TYPE <=", value, "brandType");
            return (Criteria) this;
        }

        public Criteria andBrandTypeIn(List<Byte> values) {
            addCriterion("BRAND_TYPE in", values, "brandType");
            return (Criteria) this;
        }

        public Criteria andBrandTypeNotIn(List<Byte> values) {
            addCriterion("BRAND_TYPE not in", values, "brandType");
            return (Criteria) this;
        }

        public Criteria andBrandTypeBetween(Byte value1, Byte value2) {
            addCriterion("BRAND_TYPE between", value1, value2, "brandType");
            return (Criteria) this;
        }

        public Criteria andBrandTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("BRAND_TYPE not between", value1, value2, "brandType");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryTypeIsNull() {
            addCriterion("STANDARD_CATEGORY_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryTypeIsNotNull() {
            addCriterion("STANDARD_CATEGORY_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryTypeEqualTo(Byte value) {
            addCriterion("STANDARD_CATEGORY_TYPE =", value, "standardCategoryType");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryTypeNotEqualTo(Byte value) {
            addCriterion("STANDARD_CATEGORY_TYPE <>", value, "standardCategoryType");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryTypeGreaterThan(Byte value) {
            addCriterion("STANDARD_CATEGORY_TYPE >", value, "standardCategoryType");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("STANDARD_CATEGORY_TYPE >=", value, "standardCategoryType");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryTypeLessThan(Byte value) {
            addCriterion("STANDARD_CATEGORY_TYPE <", value, "standardCategoryType");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryTypeLessThanOrEqualTo(Byte value) {
            addCriterion("STANDARD_CATEGORY_TYPE <=", value, "standardCategoryType");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryTypeIn(List<Byte> values) {
            addCriterion("STANDARD_CATEGORY_TYPE in", values, "standardCategoryType");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryTypeNotIn(List<Byte> values) {
            addCriterion("STANDARD_CATEGORY_TYPE not in", values, "standardCategoryType");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryTypeBetween(Byte value1, Byte value2) {
            addCriterion("STANDARD_CATEGORY_TYPE between", value1, value2, "standardCategoryType");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("STANDARD_CATEGORY_TYPE not between", value1, value2, "standardCategoryType");
            return (Criteria) this;
        }

        public Criteria andNewStandardCategoryIdIsNull() {
            addCriterion("NEW_STANDARD_CATEGORY_ID is null");
            return (Criteria) this;
        }

        public Criteria andNewStandardCategoryIdIsNotNull() {
            addCriterion("NEW_STANDARD_CATEGORY_ID is not null");
            return (Criteria) this;
        }

        public Criteria andNewStandardCategoryIdEqualTo(Integer value) {
            addCriterion("NEW_STANDARD_CATEGORY_ID =", value, "newStandardCategoryId");
            return (Criteria) this;
        }

        public Criteria andNewStandardCategoryIdNotEqualTo(Integer value) {
            addCriterion("NEW_STANDARD_CATEGORY_ID <>", value, "newStandardCategoryId");
            return (Criteria) this;
        }

        public Criteria andNewStandardCategoryIdGreaterThan(Integer value) {
            addCriterion("NEW_STANDARD_CATEGORY_ID >", value, "newStandardCategoryId");
            return (Criteria) this;
        }

        public Criteria andNewStandardCategoryIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("NEW_STANDARD_CATEGORY_ID >=", value, "newStandardCategoryId");
            return (Criteria) this;
        }

        public Criteria andNewStandardCategoryIdLessThan(Integer value) {
            addCriterion("NEW_STANDARD_CATEGORY_ID <", value, "newStandardCategoryId");
            return (Criteria) this;
        }

        public Criteria andNewStandardCategoryIdLessThanOrEqualTo(Integer value) {
            addCriterion("NEW_STANDARD_CATEGORY_ID <=", value, "newStandardCategoryId");
            return (Criteria) this;
        }

        public Criteria andNewStandardCategoryIdIn(List<Integer> values) {
            addCriterion("NEW_STANDARD_CATEGORY_ID in", values, "newStandardCategoryId");
            return (Criteria) this;
        }

        public Criteria andNewStandardCategoryIdNotIn(List<Integer> values) {
            addCriterion("NEW_STANDARD_CATEGORY_ID not in", values, "newStandardCategoryId");
            return (Criteria) this;
        }

        public Criteria andNewStandardCategoryIdBetween(Integer value1, Integer value2) {
            addCriterion("NEW_STANDARD_CATEGORY_ID between", value1, value2, "newStandardCategoryId");
            return (Criteria) this;
        }

        public Criteria andNewStandardCategoryIdNotBetween(Integer value1, Integer value2) {
            addCriterion("NEW_STANDARD_CATEGORY_ID not between", value1, value2, "newStandardCategoryId");
            return (Criteria) this;
        }

        public Criteria andOldStandardCategoryIdIsNull() {
            addCriterion("OLD_STANDARD_CATEGORY_ID is null");
            return (Criteria) this;
        }

        public Criteria andOldStandardCategoryIdIsNotNull() {
            addCriterion("OLD_STANDARD_CATEGORY_ID is not null");
            return (Criteria) this;
        }

        public Criteria andOldStandardCategoryIdEqualTo(Integer value) {
            addCriterion("OLD_STANDARD_CATEGORY_ID =", value, "oldStandardCategoryId");
            return (Criteria) this;
        }

        public Criteria andOldStandardCategoryIdNotEqualTo(Integer value) {
            addCriterion("OLD_STANDARD_CATEGORY_ID <>", value, "oldStandardCategoryId");
            return (Criteria) this;
        }

        public Criteria andOldStandardCategoryIdGreaterThan(Integer value) {
            addCriterion("OLD_STANDARD_CATEGORY_ID >", value, "oldStandardCategoryId");
            return (Criteria) this;
        }

        public Criteria andOldStandardCategoryIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("OLD_STANDARD_CATEGORY_ID >=", value, "oldStandardCategoryId");
            return (Criteria) this;
        }

        public Criteria andOldStandardCategoryIdLessThan(Integer value) {
            addCriterion("OLD_STANDARD_CATEGORY_ID <", value, "oldStandardCategoryId");
            return (Criteria) this;
        }

        public Criteria andOldStandardCategoryIdLessThanOrEqualTo(Integer value) {
            addCriterion("OLD_STANDARD_CATEGORY_ID <=", value, "oldStandardCategoryId");
            return (Criteria) this;
        }

        public Criteria andOldStandardCategoryIdIn(List<Integer> values) {
            addCriterion("OLD_STANDARD_CATEGORY_ID in", values, "oldStandardCategoryId");
            return (Criteria) this;
        }

        public Criteria andOldStandardCategoryIdNotIn(List<Integer> values) {
            addCriterion("OLD_STANDARD_CATEGORY_ID not in", values, "oldStandardCategoryId");
            return (Criteria) this;
        }

        public Criteria andOldStandardCategoryIdBetween(Integer value1, Integer value2) {
            addCriterion("OLD_STANDARD_CATEGORY_ID between", value1, value2, "oldStandardCategoryId");
            return (Criteria) this;
        }

        public Criteria andOldStandardCategoryIdNotBetween(Integer value1, Integer value2) {
            addCriterion("OLD_STANDARD_CATEGORY_ID not between", value1, value2, "oldStandardCategoryId");
            return (Criteria) this;
        }

        public Criteria andEffectiveDayUnitIsNull() {
            addCriterion("EFFECTIVE_DAY_UNIT is null");
            return (Criteria) this;
        }

        public Criteria andEffectiveDayUnitIsNotNull() {
            addCriterion("EFFECTIVE_DAY_UNIT is not null");
            return (Criteria) this;
        }

        public Criteria andEffectiveDayUnitEqualTo(Byte value) {
            addCriterion("EFFECTIVE_DAY_UNIT =", value, "effectiveDayUnit");
            return (Criteria) this;
        }

        public Criteria andEffectiveDayUnitNotEqualTo(Byte value) {
            addCriterion("EFFECTIVE_DAY_UNIT <>", value, "effectiveDayUnit");
            return (Criteria) this;
        }

        public Criteria andEffectiveDayUnitGreaterThan(Byte value) {
            addCriterion("EFFECTIVE_DAY_UNIT >", value, "effectiveDayUnit");
            return (Criteria) this;
        }

        public Criteria andEffectiveDayUnitGreaterThanOrEqualTo(Byte value) {
            addCriterion("EFFECTIVE_DAY_UNIT >=", value, "effectiveDayUnit");
            return (Criteria) this;
        }

        public Criteria andEffectiveDayUnitLessThan(Byte value) {
            addCriterion("EFFECTIVE_DAY_UNIT <", value, "effectiveDayUnit");
            return (Criteria) this;
        }

        public Criteria andEffectiveDayUnitLessThanOrEqualTo(Byte value) {
            addCriterion("EFFECTIVE_DAY_UNIT <=", value, "effectiveDayUnit");
            return (Criteria) this;
        }

        public Criteria andEffectiveDayUnitIn(List<Byte> values) {
            addCriterion("EFFECTIVE_DAY_UNIT in", values, "effectiveDayUnit");
            return (Criteria) this;
        }

        public Criteria andEffectiveDayUnitNotIn(List<Byte> values) {
            addCriterion("EFFECTIVE_DAY_UNIT not in", values, "effectiveDayUnit");
            return (Criteria) this;
        }

        public Criteria andEffectiveDayUnitBetween(Byte value1, Byte value2) {
            addCriterion("EFFECTIVE_DAY_UNIT between", value1, value2, "effectiveDayUnit");
            return (Criteria) this;
        }

        public Criteria andEffectiveDayUnitNotBetween(Byte value1, Byte value2) {
            addCriterion("EFFECTIVE_DAY_UNIT not between", value1, value2, "effectiveDayUnit");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysIsNull() {
            addCriterion("EFFECTIVE_DAYS is null");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysIsNotNull() {
            addCriterion("EFFECTIVE_DAYS is not null");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysEqualTo(Integer value) {
            addCriterion("EFFECTIVE_DAYS =", value, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysNotEqualTo(Integer value) {
            addCriterion("EFFECTIVE_DAYS <>", value, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysGreaterThan(Integer value) {
            addCriterion("EFFECTIVE_DAYS >", value, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("EFFECTIVE_DAYS >=", value, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysLessThan(Integer value) {
            addCriterion("EFFECTIVE_DAYS <", value, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysLessThanOrEqualTo(Integer value) {
            addCriterion("EFFECTIVE_DAYS <=", value, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysIn(List<Integer> values) {
            addCriterion("EFFECTIVE_DAYS in", values, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysNotIn(List<Integer> values) {
            addCriterion("EFFECTIVE_DAYS not in", values, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysBetween(Integer value1, Integer value2) {
            addCriterion("EFFECTIVE_DAYS between", value1, value2, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("EFFECTIVE_DAYS not between", value1, value2, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andSortDaysIsNull() {
            addCriterion("SORT_DAYS is null");
            return (Criteria) this;
        }

        public Criteria andSortDaysIsNotNull() {
            addCriterion("SORT_DAYS is not null");
            return (Criteria) this;
        }

        public Criteria andSortDaysEqualTo(Integer value) {
            addCriterion("SORT_DAYS =", value, "sortDays");
            return (Criteria) this;
        }

        public Criteria andSortDaysNotEqualTo(Integer value) {
            addCriterion("SORT_DAYS <>", value, "sortDays");
            return (Criteria) this;
        }

        public Criteria andSortDaysGreaterThan(Integer value) {
            addCriterion("SORT_DAYS >", value, "sortDays");
            return (Criteria) this;
        }

        public Criteria andSortDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("SORT_DAYS >=", value, "sortDays");
            return (Criteria) this;
        }

        public Criteria andSortDaysLessThan(Integer value) {
            addCriterion("SORT_DAYS <", value, "sortDays");
            return (Criteria) this;
        }

        public Criteria andSortDaysLessThanOrEqualTo(Integer value) {
            addCriterion("SORT_DAYS <=", value, "sortDays");
            return (Criteria) this;
        }

        public Criteria andSortDaysIn(List<Integer> values) {
            addCriterion("SORT_DAYS in", values, "sortDays");
            return (Criteria) this;
        }

        public Criteria andSortDaysNotIn(List<Integer> values) {
            addCriterion("SORT_DAYS not in", values, "sortDays");
            return (Criteria) this;
        }

        public Criteria andSortDaysBetween(Integer value1, Integer value2) {
            addCriterion("SORT_DAYS between", value1, value2, "sortDays");
            return (Criteria) this;
        }

        public Criteria andSortDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("SORT_DAYS not between", value1, value2, "sortDays");
            return (Criteria) this;
        }

        public Criteria andConditionOneIsNull() {
            addCriterion("CONDITION_ONE is null");
            return (Criteria) this;
        }

        public Criteria andConditionOneIsNotNull() {
            addCriterion("CONDITION_ONE is not null");
            return (Criteria) this;
        }

        public Criteria andConditionOneEqualTo(Integer value) {
            addCriterion("CONDITION_ONE =", value, "conditionOne");
            return (Criteria) this;
        }

        public Criteria andConditionOneNotEqualTo(Integer value) {
            addCriterion("CONDITION_ONE <>", value, "conditionOne");
            return (Criteria) this;
        }

        public Criteria andConditionOneGreaterThan(Integer value) {
            addCriterion("CONDITION_ONE >", value, "conditionOne");
            return (Criteria) this;
        }

        public Criteria andConditionOneGreaterThanOrEqualTo(Integer value) {
            addCriterion("CONDITION_ONE >=", value, "conditionOne");
            return (Criteria) this;
        }

        public Criteria andConditionOneLessThan(Integer value) {
            addCriterion("CONDITION_ONE <", value, "conditionOne");
            return (Criteria) this;
        }

        public Criteria andConditionOneLessThanOrEqualTo(Integer value) {
            addCriterion("CONDITION_ONE <=", value, "conditionOne");
            return (Criteria) this;
        }

        public Criteria andConditionOneIn(List<Integer> values) {
            addCriterion("CONDITION_ONE in", values, "conditionOne");
            return (Criteria) this;
        }

        public Criteria andConditionOneNotIn(List<Integer> values) {
            addCriterion("CONDITION_ONE not in", values, "conditionOne");
            return (Criteria) this;
        }

        public Criteria andConditionOneBetween(Integer value1, Integer value2) {
            addCriterion("CONDITION_ONE between", value1, value2, "conditionOne");
            return (Criteria) this;
        }

        public Criteria andConditionOneNotBetween(Integer value1, Integer value2) {
            addCriterion("CONDITION_ONE not between", value1, value2, "conditionOne");
            return (Criteria) this;
        }

        public Criteria andTemperatureIsNull() {
            addCriterion("TEMPERATURE is null");
            return (Criteria) this;
        }

        public Criteria andTemperatureIsNotNull() {
            addCriterion("TEMPERATURE is not null");
            return (Criteria) this;
        }

        public Criteria andTemperatureEqualTo(String value) {
            addCriterion("TEMPERATURE =", value, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureNotEqualTo(String value) {
            addCriterion("TEMPERATURE <>", value, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureGreaterThan(String value) {
            addCriterion("TEMPERATURE >", value, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureGreaterThanOrEqualTo(String value) {
            addCriterion("TEMPERATURE >=", value, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureLessThan(String value) {
            addCriterion("TEMPERATURE <", value, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureLessThanOrEqualTo(String value) {
            addCriterion("TEMPERATURE <=", value, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureLike(String value) {
            addCriterion("TEMPERATURE like", value, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureNotLike(String value) {
            addCriterion("TEMPERATURE not like", value, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureIn(List<String> values) {
            addCriterion("TEMPERATURE in", values, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureNotIn(List<String> values) {
            addCriterion("TEMPERATURE not in", values, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureBetween(String value1, String value2) {
            addCriterion("TEMPERATURE between", value1, value2, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureNotBetween(String value1, String value2) {
            addCriterion("TEMPERATURE not between", value1, value2, "temperature");
            return (Criteria) this;
        }

        public Criteria andCheckAgainIsNull() {
            addCriterion("CHECK_AGAIN is null");
            return (Criteria) this;
        }

        public Criteria andCheckAgainIsNotNull() {
            addCriterion("CHECK_AGAIN is not null");
            return (Criteria) this;
        }

        public Criteria andCheckAgainEqualTo(Byte value) {
            addCriterion("CHECK_AGAIN =", value, "checkAgain");
            return (Criteria) this;
        }

        public Criteria andCheckAgainNotEqualTo(Byte value) {
            addCriterion("CHECK_AGAIN <>", value, "checkAgain");
            return (Criteria) this;
        }

        public Criteria andCheckAgainGreaterThan(Byte value) {
            addCriterion("CHECK_AGAIN >", value, "checkAgain");
            return (Criteria) this;
        }

        public Criteria andCheckAgainGreaterThanOrEqualTo(Byte value) {
            addCriterion("CHECK_AGAIN >=", value, "checkAgain");
            return (Criteria) this;
        }

        public Criteria andCheckAgainLessThan(Byte value) {
            addCriterion("CHECK_AGAIN <", value, "checkAgain");
            return (Criteria) this;
        }

        public Criteria andCheckAgainLessThanOrEqualTo(Byte value) {
            addCriterion("CHECK_AGAIN <=", value, "checkAgain");
            return (Criteria) this;
        }

        public Criteria andCheckAgainIn(List<Byte> values) {
            addCriterion("CHECK_AGAIN in", values, "checkAgain");
            return (Criteria) this;
        }

        public Criteria andCheckAgainNotIn(List<Byte> values) {
            addCriterion("CHECK_AGAIN not in", values, "checkAgain");
            return (Criteria) this;
        }

        public Criteria andCheckAgainBetween(Byte value1, Byte value2) {
            addCriterion("CHECK_AGAIN between", value1, value2, "checkAgain");
            return (Criteria) this;
        }

        public Criteria andCheckAgainNotBetween(Byte value1, Byte value2) {
            addCriterion("CHECK_AGAIN not between", value1, value2, "checkAgain");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`STATUS` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`STATUS` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("`STATUS` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("`STATUS` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("`STATUS` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("`STATUS` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("`STATUS` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("`STATUS` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("`STATUS` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("`STATUS` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("`STATUS` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("`STATUS` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("IS_DELETED is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("IS_DELETED is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Byte value) {
            addCriterion("IS_DELETED =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Byte value) {
            addCriterion("IS_DELETED <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Byte value) {
            addCriterion("IS_DELETED >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Byte value) {
            addCriterion("IS_DELETED >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Byte value) {
            addCriterion("IS_DELETED <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Byte value) {
            addCriterion("IS_DELETED <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Byte> values) {
            addCriterion("IS_DELETED in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Byte> values) {
            addCriterion("IS_DELETED not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Byte value1, Byte value2) {
            addCriterion("IS_DELETED between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Byte value1, Byte value2) {
            addCriterion("IS_DELETED not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("ADD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("ADD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Long value) {
            addCriterion("ADD_TIME =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Long value) {
            addCriterion("ADD_TIME <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Long value) {
            addCriterion("ADD_TIME >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("ADD_TIME >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Long value) {
            addCriterion("ADD_TIME <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Long value) {
            addCriterion("ADD_TIME <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Long> values) {
            addCriterion("ADD_TIME in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Long> values) {
            addCriterion("ADD_TIME not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Long value1, Long value2) {
            addCriterion("ADD_TIME between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Long value1, Long value2) {
            addCriterion("ADD_TIME not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("CREATOR is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("CREATOR is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(Integer value) {
            addCriterion("CREATOR =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(Integer value) {
            addCriterion("CREATOR <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(Integer value) {
            addCriterion("CREATOR >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("CREATOR >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(Integer value) {
            addCriterion("CREATOR <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(Integer value) {
            addCriterion("CREATOR <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<Integer> values) {
            addCriterion("CREATOR in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<Integer> values) {
            addCriterion("CREATOR not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNull() {
            addCriterion("MOD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNotNull() {
            addCriterion("MOD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andModTimeEqualTo(Long value) {
            addCriterion("MOD_TIME =", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotEqualTo(Long value) {
            addCriterion("MOD_TIME <>", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThan(Long value) {
            addCriterion("MOD_TIME >", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("MOD_TIME >=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThan(Long value) {
            addCriterion("MOD_TIME <", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThanOrEqualTo(Long value) {
            addCriterion("MOD_TIME <=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeIn(List<Long> values) {
            addCriterion("MOD_TIME in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotIn(List<Long> values) {
            addCriterion("MOD_TIME not in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeBetween(Long value1, Long value2) {
            addCriterion("MOD_TIME between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotBetween(Long value1, Long value2) {
            addCriterion("MOD_TIME not between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNull() {
            addCriterion("UPDATER is null");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNotNull() {
            addCriterion("UPDATER is not null");
            return (Criteria) this;
        }

        public Criteria andUpdaterEqualTo(Integer value) {
            addCriterion("UPDATER =", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotEqualTo(Integer value) {
            addCriterion("UPDATER <>", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThan(Integer value) {
            addCriterion("UPDATER >", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThanOrEqualTo(Integer value) {
            addCriterion("UPDATER >=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThan(Integer value) {
            addCriterion("UPDATER <", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThanOrEqualTo(Integer value) {
            addCriterion("UPDATER <=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterIn(List<Integer> values) {
            addCriterion("UPDATER in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotIn(List<Integer> values) {
            addCriterion("UPDATER not in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER not between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andCommentsIsNull() {
            addCriterion("COMMENTS is null");
            return (Criteria) this;
        }

        public Criteria andCommentsIsNotNull() {
            addCriterion("COMMENTS is not null");
            return (Criteria) this;
        }

        public Criteria andCommentsEqualTo(String value) {
            addCriterion("COMMENTS =", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsNotEqualTo(String value) {
            addCriterion("COMMENTS <>", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsGreaterThan(String value) {
            addCriterion("COMMENTS >", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsGreaterThanOrEqualTo(String value) {
            addCriterion("COMMENTS >=", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsLessThan(String value) {
            addCriterion("COMMENTS <", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsLessThanOrEqualTo(String value) {
            addCriterion("COMMENTS <=", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsLike(String value) {
            addCriterion("COMMENTS like", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsNotLike(String value) {
            addCriterion("COMMENTS not like", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsIn(List<String> values) {
            addCriterion("COMMENTS in", values, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsNotIn(List<String> values) {
            addCriterion("COMMENTS not in", values, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsBetween(String value1, String value2) {
            addCriterion("COMMENTS between", value1, value2, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsNotBetween(String value1, String value2) {
            addCriterion("COMMENTS not between", value1, value2, "comments");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_FIRST_ENGAGE
     *
     * @mbggenerated do_not_delete_during_merge Thu Jun 27 19:06:50 CST 2019
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_FIRST_ENGAGE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}