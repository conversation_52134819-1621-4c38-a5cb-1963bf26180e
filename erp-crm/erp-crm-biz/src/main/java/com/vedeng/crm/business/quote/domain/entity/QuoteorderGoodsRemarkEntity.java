package com.vedeng.crm.business.quote.domain.entity;

import com.vedeng.common.core.base.BaseDto;
import lombok.Data;

/**
 * 报价商品备注表
 */
@Data
public class QuoteorderGoodsRemarkEntity extends BaseDto {
    /**
     * 主键
     */
    private Long quoteorderGoodsRemarkId;

    /**
     * 报价商品ID
     */
    private Integer quoteorderGoodsId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否删除 0否 1是
     */
    private Boolean isDelete;

    /**
     * 更新备注
     */
    private String updateRemark;
}
