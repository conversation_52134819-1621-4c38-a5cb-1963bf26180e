package com.vedeng.erp.kingdee.domain.entity;

import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseEntity;
import java.util.Date;

import com.vedeng.common.mybatis.jbatis.annotation.Column;
import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
    * 标准应收单
    */
@Getter
@Setter
@ToString
@Table(name = "KING_DEE_RECEIVE_COMMON")
public class KingDeeReceiveCommonEntity extends BaseEntity {
    /**
    * ID
    */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
    * 单据内码 0：表示新增，非0：云星空系统单据FID值，表示修改
    */
    private String fId;

    /**
    * 格式yyyy-MM-dd
    */
    private String fDate;

    /**
    * 贝登单据头ID号（预留）
    */
    private String fQzokBddjtId;

    /**
    * 业务类型 默认BZ
    */
    private String fBusinessType;

    /**
    * 立账类型 默认填写1
    */
    private String fSetAccountType;

    /**
    * 填单据类型编码，默认"YSD01_SYS"
    */
    private String fBillTypeId;

    /**
    * 填写客户编码
    */
    private String fCustomerId;

    /**
    * 结算组织 填写组织编码
    */
    private String fSettleOrgId;

    /**
    * 付款组织 填写组织编码
    */
    private String fPayOrgId;

    /**
    * 明细
    */
    @Column(jdbcType = "VARCHAR")
    private JSONArray fEntityDetail;
}