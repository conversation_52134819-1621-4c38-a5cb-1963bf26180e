package com.vedeng.logistics.service;

import com.vedeng.logistics.model.vo.WarehouseGoodsOutLogVo;
import com.vedeng.logistics.model.vo.WarehouseGoodsOutVo;

import java.util.List;

/**
 * 相当于分类标识 通用模板方法抽取
 */
public interface WarehouseGoodsOutDetailService {
    /**
     * 根据出库单号获取出库单据详情
     * @param outInNo 出库单号
     * @return 出库单据详情
     */
    WarehouseGoodsOutVo detailWarehouseGoodsOut(String outInNo);

    /**
     * 关联单号详情重定向
     * @param relatedNo 关联单号
     * @return 重定向到的关联单号详情页面
     */
    String relatedNoDetailRedirect(String relatedNo);


    /**
     * 根据关联单号 查询所有出库记录
     * @param relatedNo 关联单号
     * @return 出库记录
     */
    List<WarehouseGoodsOutLogVo> relatedNoWarehouseGoodsOutLogList(String relatedNo);


}
