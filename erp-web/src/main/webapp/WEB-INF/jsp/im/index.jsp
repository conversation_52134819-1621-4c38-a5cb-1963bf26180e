<%--
  Created by IntelliJ IDEA.
  User: vedeng
  Date: 2020/8/3
  Time: 9:37
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="IM" scope="application" />
<%@ include file="../common/common.jsp"%>
<style>
    iframe {
        width: 100%;
        height: 100%;
    }
</style>
<body>
    <iframe src="${imIndexUrl}" frameborder="0"></iframe>
    <input type="hidden" id="goodsInfoUrl" value="${goodsInfoUrl}" >
</body>
<script>

    var newPage = function(link, name){
        var frontPageId = $(window.parent.document).find('.active').eq(1).attr('id');
        var item = { 'id': 'im_tab' + new Date().valueOf(), 'name': name, 'url': link, 'closable': true };
        self.parent.closableTab.addTab(item);
        self.parent.closableTab.resizeMove();

        var $tab = $(window.parent.document).find('.active').eq(1).children('iframe');
        $tab.attr('data-frontpageid', frontPageId);
    };

    var inter = null;

    window.addEventListener('message',function(event){
        if(event.data.pageType){
            switch (event.data.pageType) {
                case 'business': {
                    var pageParams = event.data.params;
                    var params = [];
                    
                    params.push('traderId=' + pageParams.traderId);
                    params.push('mobile=' + pageParams.mobile);

                    //进入IM页面的skuNo、categoryId、keyword
                    if(pageParams.skuSource == 1){
                        params.push('skuNo=' + pageParams.skuInformation);
                    }else if(pageParams.skuSource == 2){
                        params.push('categoryId=' + pageParams.skuInformation);
                    }else if(pageParams.skuSource == 3){
                        params.push('keyword=' + pageParams.skuInformation);
                    }

                    //用户埋点ID
                    params.push('trackId=' + pageParams.trackId);
                    
                    //IM商机来源页
                    params.push('pageFrom=' + pageParams.pageFrom);

                    var source = pageParams.portType || 1;

                    //1：总机 2：销售
                    if(pageParams.userType == 1){
                        newPage('${lxcrmUrl}/crm/businessLeads/profile/add?fromType=0&source=' + source + '&' + params.join('&'), '新增线索');
                    }else{
                        newPage('${lxcrmUrl}/crm/businessChance/profile/add?fromType=1&source=' + source + '&' + params.join('&'), '新增线索');
                    }
                    break;
                }
                case 'prod': {
                    newPage('/goods/vgoods/viewSkuBySkuId.do?skuNo=' + event.data.params.skuNo, '商品详情');
                    break;
                }
                case 'prodList': {
                    newPage($('#goodsInfoUrl').val() + '&searchValue=' + event.data.params.keywords, '商品信息查询');
                    break;
                }
                case 'order': {
                    newPage('/order/saleorder/view.do?saleorderId=' + event.data.params.orderId, '订单详情');
                    break;
                }
                case 'company': {
                    newPage('/trader/customer/baseinfo.do?traderId=' + event.data.params.traderId, '基本信息');
                    break;
                }
                case 'message': {
                    var $tab = $(window.parent.document).find('a[title^="IM"]').parent();
                    var i = 0;
                    inter = setInterval(function () {
                        if(i % 2 == 0){
                            $tab.css('background', '#fbe7ca');
                        }else{
                            $tab.css('background', 'transparent');
                        }

                        i++;

                        if(i > 6){
                            $tab.css('background', '#fbe7ca');
                            clearInterval(inter);
                        }
                    }, 300);
                    break;
                }
            }
        }
    });

    $(window.parent.document).find('a[title^="IM"]').click(function(){
        inter && clearInterval(inter);
        $(this).parent().css('background', 'transparent')
    });

</script>