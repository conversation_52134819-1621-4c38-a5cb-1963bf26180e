package com.vedeng.erp.trader.service;

import com.vedeng.erp.trader.dto.TraderCustomerMarketingTerminalDto;
import com.vedeng.erp.trader.dto.TraderDealerFrontDto;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 客户营销属性
 * @date 2023/8/7 10:17
 */
public interface TraderCustomerMarketingApiService {


    /**
     * 获取客户营销属性
     *
     * @param traderCustomerId 客户id
     * @return List<TraderCustomerMarketingDto>
     */
    List<TraderCustomerMarketingTerminalDto> getTraderCustomerMarketing(Integer traderCustomerId);


    /**
     * 保存标签 终端 单个
     * @param traderCustomerMarketingTerminalDto 数据
     */
    void add(TraderCustomerMarketingTerminalDto traderCustomerMarketingTerminalDto);

    /**
     * 更新标签 终端 单个
     * @param traderCustomerMarketingTerminalDto 数据
     */
    void update(TraderCustomerMarketingTerminalDto traderCustomerMarketingTerminalDto);

    /**
     * 根据前台传回的对象进行解析
     * @param traderDealerFrontDto 页面封装对象
     * @return List<TraderCustomerMarketingTerminalDto>
     */
    List<TraderCustomerMarketingTerminalDto> processFrontData(TraderDealerFrontDto traderDealerFrontDto);

    /**
     * 根据客户id封装
     * @param traderCustomerId
     * @return
     */
    List<TraderDealerFrontDto.Terminal> stealthFrontData(Integer traderCustomerId);

    /**
     * 保存标签 经销商 终端 多个
     * @param traderCustomerMarketingTerminalDtoList 数据
     */
    void addAll(List<TraderCustomerMarketingTerminalDto> traderCustomerMarketingTerminalDtoList);

    /**
     * 更新
     * @param terminalDtoList
     * @param traderCustomerId
     */
    void updateAll(List<TraderCustomerMarketingTerminalDto> terminalDtoList,Integer traderCustomerId);

    /**
     * 清空原始数据
     * @param traderCustomerId
     */
    void clearByTraderCustomer(Integer traderCustomerId);

    /**
     * 合并数据并保存
     * @param traderCustomerMarketingTerminalDto
     */
    void mergeNewData(TraderCustomerMarketingTerminalDto traderCustomerMarketingTerminalDto);
    
    /**
     * 
     * @param traderCustomerId
     * @return
     */
    List<TraderCustomerMarketingTerminalDto> getByTraderCustomerId(Integer traderCustomerId);
}
