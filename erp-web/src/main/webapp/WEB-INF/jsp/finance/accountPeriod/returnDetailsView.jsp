<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="归还明细" scope="application" />
<%@ include file="../../common/common.jsp"%>
<style>
    .bottom-r{
        position: fixed;
        bottom: 10px;
        right: 10px;
    }
    .bottom-bottom{
        position: fixed;
        bottom: 0px;
        right: 0px;
    }
</style>
<div class="formpublic">

    <table class="table table-bordered table-striped table-condensed table-centered">
        <thead>
        <tr>
            <th class="wid4">序号</th>
            <th class="wid10">归还金额</th>
            <th class="wid10">归还时间</th>
            <th class="wid10">归还方式</th>
            <th class="wid10">财务流水号/售后单号</th>
        </tr>
        </thead>
        <tbody>
            <c:forEach var="list" items="${customerBillPeriodUseDetail}" varStatus="num">
                <tr>
                    <td>${num.count}</td>
                    <td><c:if test="${empty list.amount}">0</c:if>
                        <fmt:formatNumber type="number" value="${list.amount}" pattern="0.00" maxFractionDigits="2" /></td>
                    <td><c:if test="${empty list.addTime}">-</c:if>
                        <date:date value="${list.addTime}" /></td>
                    </td>
                    <td>
                        <c:choose>
                            <c:when test="${empty list.useType}">-</c:when>
                            <c:when test="${list.useType == 1}">支付订单</c:when>
                            <c:when test="${list.useType == 2}">关闭订单</c:when>
                            <c:when test="${list.useType == 3}">售后</c:when>
                            <c:when test="${list.useType == 4}">还款</c:when>
                            <c:otherwise>-</c:otherwise>
                        </c:choose>
                    </td>
                    <td>
                        <c:choose>
                            <c:when test="${empty list.useType}">-</c:when>
                            <c:when test="${list.useType == 3}">${list.afterSalesNo}</c:when>
                            <c:when test="${list.useType == 4}">${list.tranFlow}</c:when>
                            <c:otherwise>-</c:otherwise>
                        </c:choose>
                    </td>
                </tr>
            </c:forEach>
            <c:if test="${empty customerBillPeriodUseDetail}">
                <tr>
                    <td colspan="5">
                        <!-- 查询无结果弹出 --> 查询无结果！
                    </td>
                </tr>
            </c:if>
        </tbody>
    </table>

    <div class="add-tijiao pt25 layui-layer-title bottom-bottom" style="width: 100%">
        <button class="bt-small bt-bg-style bt-middle bg-light-blue bottom-r"  id="close-layer" type="button" onclick="closeGoBack();">确定</button>
    </div>
</div>