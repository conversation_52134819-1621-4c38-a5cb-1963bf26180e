package com.vedeng.erp.kingdee.domain.entity;

import java.math.BigDecimal;

import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 付款单
 */
@Getter
@Setter
public class KingDeePayBillEntity extends BaseEntity {
    /**
     * 主键
     */
    private Integer kingDeePayBillId;

    /**
     * 云星空系统单据FID值
     */
    private String fId;

    /**
     * 单据编号
     */
    private String fBillNo;

    /**
     * 结算组织id
     */
    private String fBillTypeId;

    /**
     * 单据类型id
     */
    private String fSettleOrgId;

    /**
     * 付款组织id
     */
    private String fPayOrgId;

    /**
     * 单据日期
     */
    private String fDate;

    /**
     * 往来单位类型
     */
    private String fContactUnitType;

    /**
     * 往来单位id
     */
    private String fContactUnit;

    /**
     * 收款单位类型
     */
    private String fRectUnitType;

    /**
     * 收款单位id
     */
    private String fRectUnit;

    /**
     * 结算币别Id
     */
    private String fCurrencyId;

    /**
     * 汇率
     */
    private BigDecimal fExchangeRate;

    /**
     * 结算汇率
     */
    private BigDecimal fSettleRate;

    /**
     * 业务类型
     */
    private String fBusinessType;

    /**
     * 是否相同组织0否1是
     */
    private Boolean fIsSameOrg;

    /**
     * 是否信贷业务0否1是
     */
    private Boolean fIsCredit;

    /**
     * 结算币别id
     */
    private String fSettleCur;

    /**
     * 是否转销0否1是
     */
    private Boolean fIsWriteOff;

    /**
     * 实报实付0否1是
     */
    private Boolean fRealPay;

    /**
     * 备注
     */
    private String fRemark;

    /**
     * 是否下推携带汇率到结算汇率0否1是
     */
    private Boolean fIsCarryRate;

    /**
     * 结算本位币
     */
    private String fSettleMainBookId;

    /**
     * 多收款人0否1是
     */
    private Boolean fMoreReceive;

    /**
     * 来源系统
     */
    private String fSourceSystem;

    /**
     * 付款单条目
     */
    private JSONArray fPayBillEntry;

    /**
     * 付款单原单条目
     */
    private JSONArray fPayBillSrcEntry;

    /**
     * 收款帐户帐号
     */
    private String fOppositeBankAccount;

    /**
     * 收款帐户名称
     */
    private String fOppositeCcountName;

    /**
     * 收款单位开户行
     */
    private String fOppositeBankName;

    /**
     * 付款单位联行号
     */
    private String fCnaps;

    /**
     * 金蝶反写的银行流水号
     */
    private String fQzokLsh;

    /**
     * 电子回单
     */
    private String fQzokDzhzd;
    /**
     * 区分前端导入，默认为Y
     */
    private String fQzokDr;
    /**
     * 交易类型
     */
    private String fQzokJylx;

    /**
     * 交易主体
     */
    private String fQzokJyzt;

    /**
     * 付款erp业务单号
     */
    private String fQzokCgddh;

    /**
     * 自动提交银行标识 1是 2否
     */
    private String fQzokZdtjyh;
    /**
     * 是否删除0否1是
     */
    private Integer isDelete;
    private Integer erpBankBillId;
    private Integer fileIsPush;
}
