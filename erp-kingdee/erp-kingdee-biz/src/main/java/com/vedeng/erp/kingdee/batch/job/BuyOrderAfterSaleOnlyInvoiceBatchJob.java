package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.date.DateUtil;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.listener.BaseProcessListener;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.common.listener.JobListener;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import com.vedeng.erp.kingdee.batch.processor.BatchPurchaseOnlyNewInvoiceProcessor;
import com.vedeng.erp.kingdee.batch.processor.BatchPurchaseOnlyRedInvoiceProcessor;
import com.vedeng.erp.kingdee.batch.writer.BatchPurchaseOnlyNewInvoiceWriter;
import com.vedeng.erp.kingdee.batch.writer.BatchPurchaseOnlyRedInvoiceWriter;
import com.vedeng.erp.kingdee.dto.KingDeePayCommonAndInvoiceDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.item.ItemReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购单售后仅退票 job
 * @date 2022/10/25 16:00
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class BuyOrderAfterSaleOnlyInvoiceBatchJob extends BaseJob {


    @Autowired
    private BatchPurchaseOnlyRedInvoiceProcessor batchPurchaseOnlyRedInvoiceProcessor;

    @Autowired
    private BatchPurchaseOnlyRedInvoiceWriter batchPurchaseOnlyRedInvoiceWriter;

    @Autowired
    private BatchPurchaseOnlyNewInvoiceProcessor batchPurchaseOnlyNewInvoiceProcessor;

    @Autowired
    private BatchPurchaseOnlyNewInvoiceWriter batchPurchaseOnlyNewInvoiceWriter;



    public Job buyOrderAfterSaleOnlyInvoiceFlowJob() {
        return jobBuilderFactory.get("buyOrderAfterSaleOnlyInvoiceFlowJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(onlyRedInvoice())
                .next(newBlueInvoice())
                .build();
    }

    private Step onlyRedInvoice() {
        return stepBuilderFactory.get("采购单售后仅退票红票推送")
                .<BatchInvoiceDto, KingDeePayCommonAndInvoiceDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchBatchOnlyRedInvoiceDtoItemReader(null,null))
                .processor(batchPurchaseOnlyRedInvoiceProcessor)
                .writer(batchPurchaseOnlyRedInvoiceWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }


    /**
     * 新蓝字票推送
     * @return
     */
    private Step newBlueInvoice() {
        return stepBuilderFactory.get("采购单售后仅退票新蓝票推送")
                .<BatchInvoiceDto, KingDeePayCommonAndInvoiceDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchBatchNewBlueInvoiceDtoItemReader(null,null))
                .processor(batchPurchaseOnlyNewInvoiceProcessor)
                .writer(batchPurchaseOnlyNewInvoiceWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 新蓝字
     * @return
     */
    @Bean
    @StepScope
    public ItemReader<? extends BatchInvoiceDto> batchBatchNewBlueInvoiceDtoItemReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                       @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                // 红字有效
                .colorType(2)
                .isEnable(1)
                // 审核通过
                .validStatus(1)
                .companyId(1)
                // 采购开票
                .type(503)
                // 红票退票
                .colorComplementType(0)
                // 通过时间是当天的
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(beginTime).getTime())
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(endTime).getTime())
                .build();;
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(),"buyorderNewBlueEnableInvoicefindByAll", batchInvoiceDto);
    }


    /**
     * 采购仅退票红票
     * @return
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> batchBatchOnlyRedInvoiceDtoItemReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                          @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                // 红字有效
                .colorType(1)
                .isEnable(1)
                // 审核通过
                .validStatus(1)
                .companyId(1)
                // 采购开票
                .type(503)
                // 红票退票
                .colorComplementType(0)
                // 仅退票逻辑
                .isAfterBuyorderOnly(1)
                // 通过时间是当天的
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(beginTime).getTime())
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(endTime).getTime())
                .build();;
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(),"buyorderRedEnableInvoicefindByAll", batchInvoiceDto);
    }
}

