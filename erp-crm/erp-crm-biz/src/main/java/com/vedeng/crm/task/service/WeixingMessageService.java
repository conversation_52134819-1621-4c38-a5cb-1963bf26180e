package com.vedeng.crm.task.service;

import com.vedeng.crm.task.domain.entity.WeixingMessageEntity;
public interface WeixingMessageService{

    int deleteByPrimaryKey(Long id);

    int insert(WeixingMessageEntity record);

    int insertSelective(WeixingMessageEntity record);

    WeixingMessageEntity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WeixingMessageEntity record);

    int updateByPrimaryKey(WeixingMessageEntity record);

}
