package com.vedeng.flash.service.message;

import com.vedeng.flash.dto.SendMessageDto;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.BuyorderGoods;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.vo.BuyorderVo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ExpeditionTaskForSaleorderMessageSender extends AbstractMessageSender {

    @Resource
    BuyorderMapper buyorderMapper;

    @Resource
    BuyorderGoodsMapper buyorderGoodsMapper;
    @Override
    protected String getUrl(SendMessageDto sendMessageDto) {

        return null;
    }

    @Override
    protected List<Integer> getMessageUserIdList(SendMessageDto sendMessageDto) {
        BuyorderGoods buyorderGoods = buyorderGoodsMapper.selectByPrimaryKey(sendMessageDto.getEarlyWarningTask().getRelateBusinessId());
        List<Integer> userList = Arrays.asList(sendMessageDto.getSaleorderUser());
        return userList;
    }

    @Override
    protected Integer getMessageTemplateId() {
        return 181;
    }

    @Override
    protected Map getTemplateVariableMap(SendMessageDto sendMessageDto) {
        Map<String,String> params = new HashMap<>();
        BuyorderGoods buyorderGoods = buyorderGoodsMapper.selectByPrimaryKey(sendMessageDto.getEarlyWarningTask().getRelateBusinessId());
        List<Saleorder> saleorderByBuyorderGoodsId = buyorderMapper.getSaleorderByBuyorderGoodsId(sendMessageDto.getEarlyWarningTask().getRelateBusinessId());
        List<String> collect = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(saleorderByBuyorderGoodsId)){
            collect = saleorderByBuyorderGoodsId.stream().filter(e -> e.getUserId().equals(sendMessageDto.getSaleorderUser())).map(o -> o.getSaleorderNo()).collect(Collectors.toList());
        }
        params.put("orderNo",String.join(",",collect));
        params.put("sku",buyorderGoods.getSku());
        return params;
    }
}
