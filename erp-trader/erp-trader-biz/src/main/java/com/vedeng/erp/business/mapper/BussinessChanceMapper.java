package com.vedeng.erp.business.mapper;

import com.vedeng.erp.business.domain.dto.BusinessChanceDto;
import com.vedeng.erp.business.domain.dto.MergeChanceGoods;
import com.vedeng.erp.business.domain.entity.BussinessChanceEntity;

import java.util.List;
import java.util.Map;

import com.vedeng.erp.system.dto.UserDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @description ${end}
 * @date 2022/7/13 10:21
 **/
@Repository("newBussinessChanceMapper")
public interface BussinessChanceMapper {

    int deleteByPrimaryKey(Integer bussinessChanceId);

    int deleteByPrimaryKeyIn(List<Integer> list);

    int insert(BussinessChanceEntity record);

    int insertSelective(BussinessChanceEntity record);

    BussinessChanceEntity selectByPrimaryKey(Integer bussinessChanceId);

    BussinessChanceEntity findBussinessByBusinessChanceNo(String bussinessChanceNo);


    int updateByPrimaryKeySelective(BussinessChanceEntity record);

    int updateByPrimaryKey(BussinessChanceEntity record);

    int batchInsert(@Param("list") List<BussinessChanceEntity> list);

    /**
     * 分页查询商机列表
     *
     * @param businessChanceDto 分页查询条件
     * @return List<BusinessChanceDto>
     */
    List<BusinessChanceDto> findByAll(BusinessChanceDto businessChanceDto);

    List<BusinessChanceDto> findLatestCommuncationByAllIn(@Param("businessChanceIds") List<Integer> businessChanceIds);

    /**
     * 分页查询商机列表总数统计
     *
     * @param businessChanceDto 分页查询条件
     * @return List<BusinessChanceDto>
     */
    int findByAllCount(BusinessChanceDto businessChanceDto);

    /**
     * 查询商机详情
     *
     * @param businessChanceId 商机id
     * @param currentUserId 当前用户id
     * @return BusinessChanceDto
     */
    BusinessChanceDto getDetailById(@Param("businessChanceId") Integer businessChanceId, @Param("currentUserId") Integer currentUserId);

    /**
     * 根据商机编号获取被合并的商机
     * @param businessChanceNo 合并后的商机编号
     * @return  List<MergeChanceGoods>
     */
    List<MergeChanceGoods> getChancesByNoAfterMerged(@Param("businessChanceNo") String businessChanceNo);

    /**
     * 查询沟通记录关联的商机信息（id 商机编号）
     *
     * @param list 商机id集合
     * @return id 商机编号
     */
    List<Map<String, Object>> getCommunicateChanceInfo(@Param("list") List<Integer> list);

    Integer getConcernNum(@Param("userId") Integer userId, @Param("statusList") List<Integer> statusList);

    Integer getConcernNumByUserList(@Param("userIdList") List<Integer> userIdList, @Param("statusList") List<Integer> statusList);

    /**
     * 根据商机id获取报价单中能关联建档SKU在ERP的商品挡位
     * @param bussinessChanceId
     * @return
     */
    List<Integer> getGoodsPosition(Integer bussinessChanceId);
    
    int updateStageByBusinessChanceId(@Param("updatedStage")Integer updatedStage, @Param("bussinessChanceId")Integer bussinessChanceId);

    void updateStageTimeByBusinessChanceId(BussinessChanceEntity record);

    int updateSystemBusinessLevel(@Param("updatedSystemBusinessLevel") Integer updatedSystemBusinessLevel, @Param("bussinessChanceId") Integer bussinessChanceId);

    List<UserDto> findAllShareUser(@Param("name") String name);

    List<UserDto> findAllBelongUser(@Param("name") String name);

    List<UserDto> findAllBelongUserForBussiness(@Param("name") String name, @Param("allSubordinateUserIdList") List<Integer> allSubordinateUserIdList, @Param("userId") Integer userId);

    List<Integer> findAllBelongUserForBussinessStep1( @Param("allSubordinateUserIdList") List<Integer> allSubordinateUserIdList);

    List<Integer> findAllBelongUserForBussinessStep2( @Param("userId") Integer userId);

    List<UserDto> findAllBelongUserForBussinessStep3(@Param("name") String name, @Param("userIds") List<Integer> userIds);

    List<Integer> findAllCreatorForBussinessStep1( @Param("allSubordinateUserIdList") List<Integer> allSubordinateUserIdList);

    List<Integer> findAllCreatorForBussinessStep2( @Param("userId") Integer userId);

    List<UserDto> findAllCreatorForBussinessStep3(@Param("name") String name, @Param("userIds") List<Integer> userIds);



    BusinessChanceDto getLastTask(@Param("bussinessChanceId") Integer bussinessChanceId);

    List<Integer> findShareTag1(@Param("currentUserId") Integer currentUserId);

    List<Integer> findShareTag2(@Param("currentUserId") Integer currentUserId);
}
