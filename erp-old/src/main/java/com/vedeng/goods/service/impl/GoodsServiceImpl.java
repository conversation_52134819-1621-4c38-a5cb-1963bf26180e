package com.vedeng.goods.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.common.constants.Contant;
import com.fasterxml.jackson.core.type.TypeReference;
import com.newtask.service.ReportOrderSkuService;
import com.vedeng.authorization.dao.OrganizationMapper;
import com.vedeng.authorization.dao.RoleMapper;
import com.vedeng.authorization.model.Organization;
import com.vedeng.authorization.model.Role;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.ObjectsUtil;
import com.vedeng.goods.dao.*;
import com.vedeng.goods.model.*;
import com.vedeng.goods.model.vo.GoodsVo;
import com.vedeng.goods.service.GoodsDistributeService;
import com.vedeng.goods.service.GoodsService;
import com.vedeng.logistics.model.LogincalPermission;
import com.vedeng.logistics.model.WarehouseStock;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.dto.ProductManageAndAsistDto;
import com.vedeng.order.model.query.LabelQuery;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.order.model.vo.SkuVo;
import com.vedeng.price.api.price.dto.price.SkuPriceInfoDetailResponseDto;
import com.vedeng.price.api.price.dto.price.SkuPriceInfoPurchaseDto;
import com.vedeng.price.service.BasePriceService;
import com.vedeng.soap.service.VedengSoapService;
import com.vedeng.system.dao.RangeDictionaryMapper;
import com.vedeng.system.model.RangeDictionary;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.model.VerifiesInfo;
import com.vedeng.trader.model.TraderCustomer;
import com.vedeng.trader.service.TraderCustomerService;
import com.wms.constant.LogicalEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Service("goodsService")
public class GoodsServiceImpl extends BaseServiceimpl implements GoodsService {
	public static Logger logger = LoggerFactory.getLogger(GoodsServiceImpl.class);


	private VedengSoapService vedengSoapService;

	public void setVedengSoapService(VedengSoapService vedengSoapService) {
		this.vedengSoapService = vedengSoapService;
	}

	@Autowired
	@Qualifier("rCategoryJUserMapper")
	private RCategoryJUserMapper rCategoryJUserMapper;

	@Autowired
	@Qualifier("rManageCategoryJUserMapper")
	private RManageCategoryJUserMapper rManageCategoryJUserMapper;

	@Autowired
	@Qualifier("goodsSafeStockMapper")
	private GoodsSafeStockMapper goodsSafeStockMapper;

	@Autowired
	@Qualifier("goodsChannelPriceMapper")
	private GoodsChannelPriceMapper goodsChannelPriceMapper;

	@Autowired
	@Qualifier("goodsSettlementPriceMapper")
	private GoodsSettlementPriceMapper goodsSettlementPriceMapper;

	@Autowired
	@Qualifier("goodsChannelPriceExtendMapper")
	private GoodsChannelPriceExtendMapper goodsChannelPriceExtendMapper;

	@Resource
	private GoodsMapper goodsMapper;

	@Autowired
	private WarehouseStockService warehouseStockService;

	@Resource
	private RangeDictionaryMapper rangeDictionaryMapper;

	@Resource
	private CoreSkuGenerateMapper coreSkuGenerateMapper;

	@Autowired
	private OrganizationMapper organizationMapper;

	@Value("${logincal_orga_per}")
	private String logincalOrganizationPermission;

	@Value("${stock_detail_per}")
	private String stockDetailPer;

	@Resource
	private CoreSkuMapper coreSkuMapper;

	@Autowired
	private ReportOrderSkuService reportOrderSkuService;

	@Resource
	private RoleMapper roleMapper;

	@Autowired
	private GoodsDistributeService goodsDistributeService;

	@Autowired
	private BasePriceService basePriceService;

	@Autowired
	private TraderCustomerService traderCustomerService;

	@Override
	public Map<String, Object> getGoodsListPage(HttpServletRequest request, Goods goods, Page page,HttpSession session) {
		User user =(User)session.getAttribute(ErpConst.CURR_USER);
		if(StringUtils.isNotBlank(request.getParameter("searchBegintimeStr"))){
			goods.setSearchBegintime(DateUtil.convertLong(request.getParameter("searchBegintimeStr") + " 00:00:00", "yyyy-MM-dd HH:mm:ss"));
		}

		if(StringUtils.isNotBlank(request.getParameter("searchEndtimeStr"))){
			goods.setSearchEndtime(DateUtil.convertLong(request.getParameter("searchEndtimeStr") + " 23:59:59", "yyyy-MM-dd HH:mm:ss"));
		}

		List<Goods> list = null;
		Map<String,Object> map = new HashMap<>();
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<List<Goods>>> TypeRef = new TypeReference<ResultInfo<List<Goods>>>() {};
			String url=httpUrl + "goods/getgoodslistpage.htm";
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, goods,clientId,clientKey, TypeRef,page);
			list = (List<Goods>) result.getData();
			page = result.getPage();
			if(null != list && list.size() > 0){
				for(Goods g:list){
					g.setUserList(rCategoryJUserMapper.getUserByCategory(g.getCategoryId(), user.getCompanyId()));
					//g.setUserList(rManageCategoryJUserMapper.getUserByManageCategory(g.getManageCategory(), user.getCompanyId()));
				}
			}

			map.put("list", list);
			map.put("page", page);
		} catch (IOException e) {
//			logger.error(Contant.ERROR_MSG, e);
			logger.error("查询商品列表发生异常", e);
		}
		return map;
	}


	@Override
	public Goods saveAdd(Goods goods, HttpServletRequest request, HttpSession session) {
		User user =(User)session.getAttribute(ErpConst.CURR_USER);
		Long time = DateUtil.sysTimeMillis();

		goods.setCompanyId(user.getCompanyId());
		goods.setAddTime(time);
		goods.setCreator(user.getUserId());
		goods.setModTime(time);
		goods.setUpdater(user.getUserId());


		// 校验注册证名称
		if(null != goods.getRegistrationNumberName() && !"".equals(goods.getRegistrationNumberName())){
			// 注册证名称长度不能大于128
			if(goods.getRegistrationNumberName().length() > 128){
				return null;
			}
		}

		// 校验wiki链接字段
		if(null != goods.getHref() && !"".equals(goods.getHref())){
			// 注册证名称长度不能大于128
			if(goods.getHref().length() > 256){
				return null;
			}
		}



		if(request.getParameter("begintimeStr") != ""){
			goods.setBegintime(DateUtil.convertLong(request.getParameter("begintimeStr"), "yyyy-MM-dd"));
		}

		if(request.getParameter("endtimeStr") != ""){
			goods.setEndtime(DateUtil.convertLong(request.getParameter("endtimeStr"), "yyyy-MM-dd"));
		}

		//应用科室
		if(null != request.getParameterValues("attributeId")){
			String[] attributeIds = request.getParameterValues("attributeId");
			List<GoodsSysOptionAttribute> goodsSysOptionAttributes = new ArrayList<>();
			for(String attIds : attributeIds){
				GoodsSysOptionAttribute goodsSysOptionAttribute = new GoodsSysOptionAttribute();
				String[] attArr = attIds.split("_");

				goodsSysOptionAttribute.setAttributeType(Integer.parseInt(attArr[0]));
				goodsSysOptionAttribute.setAttributeId(Integer.parseInt(attArr[1]));

				goodsSysOptionAttributes.add(goodsSysOptionAttribute);
			}
			goods.setGoodsSysOptionAttributes(goodsSysOptionAttributes);
		}


		List<GoodsAttribute> goodsAttributes = new ArrayList<>();
		//最小分类通用属性（复选）
		if(null != request.getParameterValues("categoryAttributeId")){
			String[] categoryAttributeIds = request.getParameterValues("categoryAttributeId");
			//List<GoodsAttribute> goodsAttributes = new ArrayList<>();
			for(String attIds : categoryAttributeIds){
				GoodsAttribute goodsAttribute = new GoodsAttribute();
				String[] attArr = attIds.split("_");

				goodsAttribute.setCategoryAttributeId(Integer.parseInt(attArr[0]));
				goodsAttribute.setCategoryAttrValueId(Integer.parseInt(attArr[1]));

				goodsAttributes.add(goodsAttribute);
			}
			goods.setGoodsAttributes(goodsAttributes);
		}

		//最小分类通用属性（单选）
		if (null != request.getParameter("attr_id_str")) {
			String[] categoryAttributeIds2 = request.getParameter("attr_id_str").split("_");
			//List<GoodsAttribute> goodsAttributes2 = new ArrayList<>();
			for(String attIds2 : categoryAttributeIds2){
				if (attIds2 == "" || null == request.getParameter("categoryAttributeId_"+attIds2)) {
					continue;
				}
				GoodsAttribute goodsAttribute2 = new GoodsAttribute();
				String[] attArr2 = request.getParameter("categoryAttributeId_"+attIds2).split("_");

				goodsAttribute2.setCategoryAttributeId(Integer.parseInt(attArr2[0]));
				goodsAttribute2.setCategoryAttrValueId(Integer.parseInt(attArr2[1]));

				goodsAttributes.add(goodsAttribute2);
			}

		}
			goods.setGoodsAttributes(goodsAttributes);

		List<GoodsAttachment> goodsAttachments = new ArrayList<>();
		//产品图片
		if (null != request.getParameterValues("uri_343")) {
			String[] uri343 = request.getParameterValues("uri_343");
			for(String uri : uri343){
				if (uri == "") {
					continue;
				}
				GoodsAttachment goodsAttachment = new GoodsAttachment();
				goodsAttachment.setAttachmentType(343);
				goodsAttachment.setDomain(request.getParameter("domain"));
				goodsAttachment.setUri(uri);
				goodsAttachments.add(goodsAttachment);
			}
		}

		//产品检测报告
		if (null != request.getParameterValues("uri_658")) {
			String[] uri658 = request.getParameterValues("uri_658");
			for(String uri : uri658){
				if (uri == "") {
					continue;
				}
				GoodsAttachment goodsAttachment = new GoodsAttachment();
				goodsAttachment.setAttachmentType(658);
				goodsAttachment.setDomain(request.getParameter("domain"));
				goodsAttachment.setUri(uri);
				goodsAttachments.add(goodsAttachment);
			}
		}

		//产品专利文件
		if (null != request.getParameterValues("uri_659")) {
			String[] uri659 = request.getParameterValues("uri_659");
			for(String uri : uri659){
				if (uri == "") {
					continue;
				}
				GoodsAttachment goodsAttachment = new GoodsAttachment();
				goodsAttachment.setAttachmentType(659);
				goodsAttachment.setDomain(request.getParameter("domain"));
				goodsAttachment.setUri(uri);
				goodsAttachments.add(goodsAttachment);
			}
		}

		//注册证
		if (null != request.getParameterValues("uri_344")) {
			String[] uri344 = request.getParameterValues("uri_344");
			for (int i = 0; i < uri344.length; i++) {
				if (uri344[i] == "") {
					continue;
				}
				GoodsAttachment goodsAttachment = new GoodsAttachment();
				goodsAttachment.setAttachmentType(344);
				goodsAttachment.setDomain(request.getParameter("domain"));
				goodsAttachment.setUri(uri344[i]);
				goodsAttachments.add(goodsAttachment);
			}
		}

		// 备案文件
		if (null != request.getParameter("uri_680") && request.getParameter("uri_680") != "")
		{
			GoodsAttachment goodsAttachment = new GoodsAttachment();
			goodsAttachment.setAttachmentType(680);
			goodsAttachment.setDomain(request.getParameter("domain"));
			goodsAttachment.setUri(request.getParameter("uri_680"));
			goodsAttachments.add(goodsAttachment);
		}

		goods.setGoodsAttachments(goodsAttachments);

		//非医疗器械，管理类别置空
		if(goods.getStandardCategoryId()==1388){
			goods.setManageCategoryLevel(0);
		}
		//产品类型为试剂，新国标分类置空
		if(goods.getGoodsType()==318){
			goods.setStandardCategoryId(0);
		}
		// 对goods下面的String类型属性进行转义  at bug[3778 在产品名称和物料编码中，输入“A30-000001---”只会显示“A30-000001-” ] by franlin 2018-05-02  TODO 暂时不改成通用模式
		ObjectsUtil.resetStringValueByEscapeType(goods, 1);

		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Goods>> TypeRef2 = new TypeReference<ResultInfo<Goods>>() {};
		try {
			ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(httpUrl + "goods/saveadd.htm", goods,clientId,clientKey, TypeRef2);
			Goods res = (Goods) result2.getData();

			return res;
		} catch (IOException e) {
			return null;
		}
	}


	@Override
	public ResultInfo<?> isDiscardById(Goods goods) {
		ResultInfo<?> result = new ResultInfo<>();
		Integer goodsId = goods.getGoodsId();
		if(goodsId != null && goods.getIsDiscard() == 1) {
			String sku = "V" + goodsId;
			boolean flag = checkIsDisCardBySku(sku);
			if(flag){
				result.setMessage("此商品有库存不允许禁用!");
				return result;
			}
		}
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Goods>> TypeRef = new TypeReference<ResultInfo<Goods>>() {};
		String url=httpUrl + "goods/isdiscardbyid.htm";
		try {
			Long time = DateUtil.sysTimeMillis();
			if (goods.getIsDiscard() == 1) {
				goods.setDiscardTime(time);
			} else {
				goods.setModTime(time);
			}
			result = (ResultInfo<?>) HttpClientUtils.post(url, goods,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}

	@Override
	public boolean checkIsDisCardBySku(String sku) {
		Map<String, WarehouseStock> stockInfo = warehouseStockService.getStockInfo(Collections.singletonList(sku));
		WarehouseStock warehouseStock = stockInfo.get(sku);
		if(warehouseStock.getStockNum().equals(0)){
			return false;
		}
		return true;
	}

	@Override
	public String getUnitNameBySkuNo(String skuNo) {
		return goodsMapper.getUnitNameBySkuNo(skuNo);
	}

	@Override
	public CoreSkuGenerate getSkuAuthotizationInfoBySku(Long skuId) {
		if (skuId == null){
			return  null;
		}
		return goodsMapper.getSkuAuthotizationInfoBySku(skuId);
	}

	@Override
	public void updateStockInfoOfSku(List<String> skuList) {
		Map<String, WarehouseStock> warehouseStocks = warehouseStockService.getStockInfo(skuList);
		if (warehouseStocks != null){
			for (String sku : warehouseStocks.keySet()){
				if (StringUtils.isNotBlank(sku) && warehouseStocks.get(sku).getStockNum() != null && warehouseStocks.get(sku).getAvailableStockNum() != null){
					CoreSku updateOfSku = new CoreSku();
					updateOfSku.setStockNum(warehouseStocks.get(sku).getStockNum());
					updateOfSku.setAvailableStockNum(warehouseStocks.get(sku).getAvailableStockNum());
					updateOfSku.setSkuId(Integer.valueOf(sku.substring(1)));
					coreSkuMapper.updateByPrimaryKeySelective(updateOfSku);
				}
			}
		}
	}

	@Override
	public void updatePriceInfoOfSku(List<String> skuList) {
		try {
			List<SkuPriceInfoDetailResponseDto> skuPriceInfoDetailResponseDtoList = reportOrderSkuService.querySkuPriceInfoListFromPrice(skuList);
			if (skuPriceInfoDetailResponseDtoList != null && skuPriceInfoDetailResponseDtoList.size() > 0) {
				skuPriceInfoDetailResponseDtoList.forEach(item -> {
							logger.info("处理数据：{}",JSON.toJSONString(item));
							CoreSku updateOfSku = new CoreSku();
							updateOfSku.setSkuId(Integer.valueOf(item.getSkuNo().substring(1)));
							updateOfSku.setTerminalPrice(item.getTerminalPrice());
							updateOfSku.setDistributionPrice(item.getDistributionPrice());
							updateOfSku.setCostPrice(getCostPriceOfSku(item.getPurchaseList()));
							logger.info("更新数据：{}",JSON.toJSONString(updateOfSku));
							coreSkuMapper.updatePriceOfSku(updateOfSku);
						});
			}
		}catch (Exception e){
			logger.error("同步价格错误",e);
		}
	}


	/**
	 * 计算sku的成本价
	 * 当有多个供应商时，首先按照更新数据来降序排序，当更新时间一样时，按照价格来顺序排序
	 * @param skuPriceInfoPurchaseDtoList 供应商成本价
	 * @return sku成本价
	 */
	private BigDecimal getCostPriceOfSku(List<SkuPriceInfoPurchaseDto> skuPriceInfoPurchaseDtoList){
		if (skuPriceInfoPurchaseDtoList == null || skuPriceInfoPurchaseDtoList.size() == 0) {
			return null;
		}
		logger.info("getCostPriceOfSku：{}",JSON.toJSONString(skuPriceInfoPurchaseDtoList));
		List<SkuPriceInfoPurchaseDto> sortedPurchaseInfo = skuPriceInfoPurchaseDtoList.stream()
				.sorted((p1,p2) -> {
					long modTime1 = DateUtil.convertLong(p1.getModTime(),"yyyy-MM-dd HH:mm:ss");
					long modTime2 = DateUtil.convertLong(p2.getModTime(),"yyyy-MM-dd HH:mm:ss");
					if (modTime1 > modTime2){
						return 1;
					}
					if (modTime1 < modTime2){
						return -1;
					}
					return -(p1.getPurchasePrice().compareTo(p2.getPurchasePrice()));
				})
				.collect(Collectors.toList());
		logger.info("getCostPriceOfSku sortresult: {}",JSON.toJSONString(sortedPurchaseInfo));
		return sortedPurchaseInfo.get(0).getPurchasePrice();
	}


	@Override
	public Goods getGoodsById(Goods goods) {
		try {
			goods = goodsMapper.getGoodsById(goods.getGoodsId());
		} catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
		return goods;
	}

	@Override
	public Goods getSkuInfoBySku(String skuNo) {
		try {
			Goods goods = goodsMapper.getSkuInfoBySku(skuNo);
			return goods;
		} catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
 	}






	@Override
	public Goods saveBaseInfo(Goods goods, HttpServletRequest request, HttpSession session) {
		User user =(User)session.getAttribute(ErpConst.CURR_USER);
		Long time = DateUtil.sysTimeMillis();

		goods.setModTime(time);
		goods.setUpdater(user.getUserId());

		// 校验注册证名称
		if(null != goods.getRegistrationNumberName() && "".equals(goods.getRegistrationNumberName())){
			if(goods.getRegistrationNumberName().length() > 128){
				return null;
			}
		}
		// 校验wiki链接
		if(null != goods.getHref() && "".equals(goods.getHref())){
			if(goods.getHref().length() > 256){
				return null;
			}
		}

		if(StringUtils.isNotBlank(request.getParameter("begintimeStr"))){
			goods.setBegintime(DateUtil.convertLong(request.getParameter("begintimeStr"), "yyyy-MM-dd"));
		} else {
			goods.setBegintime(0l);
		}

		if(StringUtils.isNotBlank(request.getParameter("endtimeStr"))){
			goods.setEndtime(DateUtil.convertLong(request.getParameter("endtimeStr"), "yyyy-MM-dd"));
		} else {
			goods.setEndtime(0l);
		}

		List<GoodsAttribute> goodsAttributes = new ArrayList<>();
		//最小分类通用属性
		if(null != request.getParameterValues("categoryAttributeId")){
			String[] categoryAttributeIds = request.getParameterValues("categoryAttributeId");

			for(String attIds : categoryAttributeIds){
				GoodsAttribute goodsAttribute = new GoodsAttribute();
				String[] attArr = attIds.split("_");

				goodsAttribute.setCategoryAttributeId(Integer.parseInt(attArr[0]));
				goodsAttribute.setCategoryAttrValueId(Integer.parseInt(attArr[1]));

				goodsAttributes.add(goodsAttribute);
			}
			goods.setGoodsAttributes(goodsAttributes);
		}

		//最小分类通用属性（单选）
		if (null != request.getParameter("attr_id_str")) {
			String[] categoryAttributeIds2 = request.getParameter("attr_id_str").split("_");
			//List<GoodsAttribute> goodsAttributes2 = new ArrayList<>();
			for(String attIds2 : categoryAttributeIds2){
				if (attIds2 == "" || null == request.getParameter("categoryAttributeId_"+attIds2)) continue;
				GoodsAttribute goodsAttribute2 = new GoodsAttribute();
				String[] attArr2 = request.getParameter("categoryAttributeId_"+attIds2).split("_");

				goodsAttribute2.setCategoryAttributeId(Integer.parseInt(attArr2[0]));
				goodsAttribute2.setCategoryAttrValueId(Integer.parseInt(attArr2[1]));

				goodsAttributes.add(goodsAttribute2);
			}

		}
		goods.setGoodsAttributes(goodsAttributes);

		List<GoodsAttachment> goodsAttachments = new ArrayList<>();


		//产品图片
		if (null != request.getParameterValues("uri_343")) {
			String[] uri343 = request.getParameterValues("uri_343");
			for(String uri : uri343){
				if (uri == "") continue;
				GoodsAttachment goodsAttachment = new GoodsAttachment();
				goodsAttachment.setAttachmentType(343);
				goodsAttachment.setDomain(request.getParameter("domain_343"));
				goodsAttachment.setUri(uri);
				goodsAttachments.add(goodsAttachment);
			}
		}

		//产品检测报告
		if (null != request.getParameterValues("uri_658")) {
			String[] uri658 = request.getParameterValues("uri_658");
			for(String uri : uri658){
				if (uri == "") continue;
				GoodsAttachment goodsAttachment = new GoodsAttachment();
				goodsAttachment.setAttachmentType(658);
				goodsAttachment.setDomain(request.getParameter("domain_658"));
				goodsAttachment.setUri(uri);
				goodsAttachments.add(goodsAttachment);
			}
		}

		//产品专利文件
		if (null != request.getParameterValues("uri_659")) {
			String[] uri659 = request.getParameterValues("uri_659");
			for(String uri : uri659){
				if (uri == "") continue;
				GoodsAttachment goodsAttachment = new GoodsAttachment();
				goodsAttachment.setAttachmentType(659);
				goodsAttachment.setDomain(request.getParameter("domain_659"));
				goodsAttachment.setUri(uri);
				goodsAttachments.add(goodsAttachment);
			}
		}

		//注册证
		if (null != request.getParameterValues("uri_344")) {
			String[] uri344 = request.getParameterValues("uri_344");
			for (int i = 0; i < uri344.length; i++) {
				if (uri344[i] == "") continue;
				GoodsAttachment goodsAttachment = new GoodsAttachment();
				goodsAttachment.setAttachmentType(344);
				goodsAttachment.setDomain(request.getParameter("domain_344"));
				goodsAttachment.setUri(uri344[i]);
				goodsAttachments.add(goodsAttachment);
			}
		}
		// 备案文件
		if (null != request.getParameter("uri_680") && request.getParameter("uri_680") != "") {
			GoodsAttachment goodsAttachment = new GoodsAttachment();
			goodsAttachment.setAttachmentType(680);
			goodsAttachment.setDomain(request.getParameter("domain_680"));
			goodsAttachment.setUri(request.getParameter("uri_680"));
			goodsAttachments.add(goodsAttachment);
		}

		goods.setGoodsAttachments(goodsAttachments);

		//应用科室
		if(null != request.getParameterValues("attributeId")){
			String[] attributeIds = request.getParameterValues("attributeId");
			List<GoodsSysOptionAttribute> goodsSysOptionAttributes = new ArrayList<>();
			for(String attIds : attributeIds){
				GoodsSysOptionAttribute goodsSysOptionAttribute = new GoodsSysOptionAttribute();
				String[] attArr = attIds.split("_");

				goodsSysOptionAttribute.setAttributeType(Integer.parseInt(attArr[0]));
				goodsSysOptionAttribute.setAttributeId(Integer.parseInt(attArr[1]));

				goodsSysOptionAttributes.add(goodsSysOptionAttribute);
			}
			goods.setGoodsSysOptionAttributes(goodsSysOptionAttributes);
		}


		//产品类型为试剂，新国标分类置空
		if(null != goods.getGoodsType() && goods.getGoodsType() == 318)
		{
			goods.setStandardCategoryId(0);
		}

		//非医疗器械，管理类别置空  新国标分类可存在为空
		if(null != goods.getStandardCategoryId() && goods.getStandardCategoryId() == 1388)
		{
			goods.setManageCategoryLevel(0);
		}
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Goods>> TypeRef2 = new TypeReference<ResultInfo<Goods>>() {};

		// 对goods下面的String类型属性进行转义  at bug[3778 在产品名称和物料编码中，输入“A30-000001---”只会显示“A30-000001-” ] by franlin 2018-05-02  TODO 暂时不改成通用模式
		ObjectsUtil.resetStringValueByEscapeType(goods, 1);

		try {
			ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(httpUrl + "goods/savebaseinfo.htm", goods,clientId,clientKey, TypeRef2);
			if(result2 != null && result2.getCode() == 0){
				vedengSoapService.goodsIsNoReasonReturnSync(goods.getGoodsId(),goods.getIsNoReasonReturn());
			}
			Goods res = (Goods) result2.getData();
			return res;
		} catch (IOException e) {
			return null;
		}
	}


	@Override
	public Goods saveSaleInfo(Goods goods, HttpServletRequest request, HttpSession session) {
		User user =(User)session.getAttribute(ErpConst.CURR_USER);
		Long time = DateUtil.sysTimeMillis();

		goods.setModTime(time);
		goods.setUpdater(user.getUserId());

		if(request.getParameter("begintimeStr") != ""){
			goods.setBegintime(DateUtil.convertLong(request.getParameter("begintimeStr"), "yyyy-MM-dd"));
		} else {
			goods.setBegintime(0l);
		}

		if(request.getParameter("endtimeStr") != ""){
			goods.setEndtime(DateUtil.convertLong(request.getParameter("endtimeStr"), "yyyy-MM-dd"));
		} else {
			goods.setEndtime(0l);
		}

		//应用科室
		if(null != request.getParameterValues("attributeId")){
			String[] attributeIds = request.getParameterValues("attributeId");
			List<GoodsSysOptionAttribute> goodsSysOptionAttributes = new ArrayList<>();
			for(String attIds : attributeIds){
				GoodsSysOptionAttribute goodsSysOptionAttribute = new GoodsSysOptionAttribute();
				String[] attArr = attIds.split("_");

				goodsSysOptionAttribute.setAttributeType(Integer.parseInt(attArr[0]));
				goodsSysOptionAttribute.setAttributeId(Integer.parseInt(attArr[1]));

				goodsSysOptionAttributes.add(goodsSysOptionAttribute);
			}
			goods.setGoodsSysOptionAttributes(goodsSysOptionAttributes);
		}

		List<GoodsAttachment> goodsAttachments = new ArrayList<>();
		//注册证
		if (null != request.getParameter("uri_344") && null != request.getParameter("domain_344") && request.getParameter("uri_344") != "" && request.getParameter("domain_344") != "") {
			GoodsAttachment goodsAttachment = new GoodsAttachment();
			goodsAttachment.setAttachmentType(344);
			goodsAttachment.setDomain(request.getParameter("domain_344"));
			goodsAttachment.setUri(request.getParameter("uri_344"));
			goodsAttachments.add(goodsAttachment);
		}

		//注册标准
		if (null != request.getParameter("uri_345") && null != request.getParameter("domain_345") && request.getParameter("uri_345") != "" && request.getParameter("domain_345") != "") {
			GoodsAttachment goodsAttachment = new GoodsAttachment();
			goodsAttachment.setAttachmentType(345);
			goodsAttachment.setDomain(request.getParameter("domain_345"));
			goodsAttachment.setUri(request.getParameter("uri_345"));
			goodsAttachments.add(goodsAttachment);
		}
		goods.setGoodsAttachments(goodsAttachments);

		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Goods>> TypeRef2 = new TypeReference<ResultInfo<Goods>>() {};
		try {
			ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(httpUrl + "goods/savesaleinfo.htm", goods,clientId,clientKey, TypeRef2);
			Goods res = (Goods) result2.getData();
			return res;
		} catch (IOException e) {
			return null;
		}
	}


	@Override
	public List<GoodsAttribute> getGoodsAttributeList(GoodsAttribute goodsAttribute) {
		List<GoodsAttribute> list = null;
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<List<GoodsAttribute>>> TypeRef = new TypeReference<ResultInfo<List<GoodsAttribute>>>() {};
			String url=httpUrl + "goods/getgoodsattributelist.htm";
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, goodsAttribute,clientId,clientKey, TypeRef);
			list = (List<GoodsAttribute>) result.getData();
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return list;
	}


	@Override
	public List<GoodsSysOptionAttribute> getGoodsSysOptionAttributeList(Goods goods) {
		List<GoodsSysOptionAttribute> list = null;
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<List<GoodsSysOptionAttribute>>> TypeRef = new TypeReference<ResultInfo<List<GoodsSysOptionAttribute>>>() {};
			String url=httpUrl + "goods/getgoodssysoptionattributelist.htm";
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, goods,clientId,clientKey, TypeRef);
			list = (List<GoodsSysOptionAttribute>) result.getData();
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return list;
	}


	@Override
	public Map<String, Object> getGoodsOptListPage(GoodsOpt goodsOpt, Page page) {
		List<GoodsOpt> list = null;
		Map<String,Object> map = new HashMap<>();
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<List<GoodsOpt>>> TypeRef = new TypeReference<ResultInfo<List<GoodsOpt>>>() {};
			String url=httpUrl + "goods/getgoodsoptlistpage.htm";
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, goodsOpt,clientId,clientKey, TypeRef,page);
			list = (List<GoodsOpt>) result.getData();
			page = result.getPage();

			map.put("list", list);
			map.put("page", page);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return map;
	}


	@Override
	public ResultInfo<?> saveGoodsPackage(GoodsPackage goodsPackage) {
		ResultInfo<?> result = new ResultInfo<>();
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<GoodsPackage>> TypeRef = new TypeReference<ResultInfo<GoodsPackage>>() {};
		String url=httpUrl + "goods/savegoodspackage.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, goodsPackage,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}


	@Override
	public ResultInfo<?> saveGoodsRecommend(GoodsRecommend goodsRecommend) {
		ResultInfo<?> result = new ResultInfo<>();
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<GoodsRecommend>> TypeRef = new TypeReference<ResultInfo<GoodsRecommend>>() {};
		String url=httpUrl + "goods/savegoodsrecommend.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, goodsRecommend,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}


	@Override
	public ResultInfo<?> delGoodsPackageById(GoodsPackage goodsPackage) {
		ResultInfo<?> result = new ResultInfo<>();
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<GoodsPackage>> TypeRef = new TypeReference<ResultInfo<GoodsPackage>>() {};
		String url=httpUrl + "goods/delgoodspackagebyid.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, goodsPackage,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return result;
		}
		return result;
	}


	@Override
	public ResultInfo<?> delGoodsRecommendById(GoodsRecommend goodsRecommend) {
		ResultInfo<?> result = new ResultInfo<>();
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<GoodsRecommend>> TypeRef = new TypeReference<ResultInfo<GoodsRecommend>>() {};
		String url=httpUrl + "goods/delgoodsrecommendbyid.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, goodsRecommend,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return result;
		}
		return result;
	}


	@Override
	public List<GoodsAttachment> getGoodsAttachmentListByGoodsId(GoodsAttachment goodsAttachment) {
		List<GoodsAttachment> goodsAttachmentList = null;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<List<GoodsAttachment>>> TypeRef = new TypeReference<ResultInfo<List<GoodsAttachment>>>() {};
		String url=httpUrl + "goods/getgoodsattachmentlistbygoodsid.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, goodsAttachment,clientId,clientKey, TypeRef);
			goodsAttachmentList = (List<GoodsAttachment>) result.getData();
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
		return goodsAttachmentList;
	}

	@Override
	public List<GoodsOpt> getGoodsPackageList(GoodsOpt goodsOpt) {
		List<GoodsOpt> list = null;
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<List<GoodsOpt>>> TypeRef = new TypeReference<ResultInfo<List<GoodsOpt>>>() {};
			String url=httpUrl + "goods/getgoodspackagelist.htm";
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, goodsOpt,clientId,clientKey, TypeRef);
			list = (List<GoodsOpt>) result.getData();
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return list;
	}


	@Override
	public List<GoodsOpt> getGoodsRecommendList(GoodsOpt goodsOpt) {
		List<GoodsOpt> list = null;
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<List<GoodsOpt>>> TypeRef = new TypeReference<ResultInfo<List<GoodsOpt>>>() {};
			String url=httpUrl + "goods/getgoodsrecommendlist.htm";
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, goodsOpt,clientId,clientKey, TypeRef);
			list = (List<GoodsOpt>) result.getData();
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return list;
	}


	@Override
	public List<Goods> getGoodsUnitList(Goods goods) {
		List<Goods> list = null;
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<List<Goods>>> TypeRef = new TypeReference<ResultInfo<List<Goods>>>() {};
			String url=httpUrl + "goods/getgoodsunitlist.htm";
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, goods,clientId,clientKey, TypeRef);
			list = (List<Goods>) result.getData();
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return list;
	}

	@Override
	public ResultInfo<?> updateGoodsTaxCategoryNo(List<Goods> list){
		ResultInfo<?> result = null;
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
			String url=httpUrl + "goods/updategoodstaxcategoryno.htm";
			result = (ResultInfo<?>) HttpClientUtils.post(url, list,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}

	@Override
	public List<String> batchSaveVailGoodsName(List<Goods> list) {
		List<String> result_list = null;
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<List<String>>> TypeRef = new TypeReference<ResultInfo<List<String>>>() {};
			String url=httpUrl + "goods/batchsavevailgoodsname.htm";
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, list,clientId,clientKey, TypeRef);
			result_list = (List<String>) result.getData();
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result_list;
	}

	@Override
	public List<String> batchEditVailGoodsName(List<Goods> list) {
		List<String> result_list = null;
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<List<String>>> TypeRef = new TypeReference<ResultInfo<List<String>>>() {};
			String url=httpUrl + "goods/batcheditvailgoodsname.htm";
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, list,clientId,clientKey, TypeRef);
			result_list = (List<String>) result.getData();
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result_list;
	}

	@Override
	public ResultInfo<?> batchSaveGoods(List<Goods> list) {
		ResultInfo<?> result = null;
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
			String url=httpUrl + "goods/batchsavegoods.htm";
			result = (ResultInfo<?>) HttpClientUtils.post(url, list,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}


	@Override
	public ResultInfo<?> batchUpdateGoodsSave(List<Goods> list) {
		ResultInfo<?> result = null;
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
			String url=httpUrl + "goods/batchupdategoodssave.htm";
			result = (ResultInfo<?>) HttpClientUtils.post(url, list,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}


	@Override
	public ResultInfo<?> validGoodName(Goods goods) {
		ResultInfo<?> result = new ResultInfo<>();
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Goods>> TypeRef = new TypeReference<ResultInfo<Goods>>() {};
		String url=httpUrl + "goods/validgoodsname.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, goods,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}


	@Override
	public Map<String, Object> queryGoodsListPage(Goods goods,Page page,HttpSession session) {
		List<GoodsVo> list = null;
		Map<String,Object> map = new HashMap<>();
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<List<GoodsVo>>> TypeRef = new TypeReference<ResultInfo<List<GoodsVo>>>() {};
			String url=httpUrl + "goods/querygoodslistpage.htm";
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, goods,clientId,clientKey, TypeRef,page);
			list = (List<GoodsVo>) result.getData();
			page = result.getPage();
			map.put("list", list);
			map.put("page", page);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return map;
	}


	@Override
	public Map<String, Object> getlistGoodsStockPage(HttpServletRequest request, Goods goods, Page page ,User user) {
		List<GoodsVo> list = null;
		Map<String,Object> map = new HashMap<>();
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<List<GoodsVo>>> TypeRef = new TypeReference<ResultInfo<List<GoodsVo>>>() {};
			String url=httpUrl + "goods/getlistgoodsstockpage.htm";
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, goods,clientId,clientKey, TypeRef,page);
			list = (List<GoodsVo>) result.getData();
			page = result.getPage();
			if(!CollectionUtils.isEmpty(list)) {
				List<String> skus = list.stream().map(GoodsVo::getSku).collect(Collectors.toList());
				Map<String, WarehouseStock> logicalStockMapInfo = warehouseStockService.getLogicalStockMapInfo(skus);

				Map<String, WarehouseStock> map1 = warehouseStockService.getStockInfo(skus);
				if (map1 != null) {
					for (GoodsVo goodsVo : list) {
						WarehouseStock w = map1.get(goodsVo.getSku());
						if(w!=null) {
							goodsVo.setGoodsStock(w.getStockNum());
							goodsVo.setCanUseGoodsStock(w.getAvailableStockNum());
							goodsVo.setOccupyNum(w.getOccupyNum());
							/**
							 * 查询活动商品的锁定库存
							 */
							goodsVo.setActionLockCount(map1.get(goodsVo.getSku()).getActionLockNum());
						}
					}
				}

				Map<String, String> ownersBySkuMap = goodsDistributeService.getOwnersBySkus(skus);
				if (ownersBySkuMap != null) {
					for (GoodsVo goodsVo : list) {
						String owner = ownersBySkuMap.get(goodsVo.getSku());
						if (StringUtils.isNotEmpty(owner)) {
							// 产品归属
							goodsVo.setProUserName(owner);
						}
					}
				}

				try {
					if (MapUtils.isNotEmpty(logicalStockMapInfo)){
						list.stream().forEach(goodsVo -> {
							Map<String, Integer> availableStockMap = goodsVo.getAvailableStockMap();
							Map<String, Integer> occupyStockMap = goodsVo.getOccupyStockMap();
							for (LogicalEnum logicalEnum : LogicalEnum.values()) {
								WarehouseStock stockInfo = logicalStockMapInfo.get(goodsVo.getSku() + logicalEnum.getLogicalWarehouseId());
								availableStockMap.put(logicalEnum.getLogicalWarehouseCode(), stockInfo == null || stockInfo.getAvailableStockNum() == null ?
										0 : stockInfo.getAvailableStockNum());
								occupyStockMap.put(logicalEnum.getLogicalWarehouseCode(), stockInfo == null || stockInfo.getOccupyNum() == null ?
										0 : stockInfo.getOccupyNum());
							}
							//12.在库总数量
							setTotalNum(availableStockMap);
							setTotalNum(occupyStockMap);
						});
					}
				} catch (Exception e) {
					logger.error("逻辑仓库存查询失败error logicalStockMapInfo:{}" + logicalStockMapInfo);
				}
			}

			//逻辑仓角色权限控制
			try {
				if (user.getUserId() == 1 || user.getUserId() == 2){
					ArrayList<Integer> permissions = new ArrayList<>();
					//全部数量
					permissions.add(1700);
//					for (LogicalEnum logicalEnum : LogicalEnum.values()) {
//						permissions.add(logicalEnum.getLogicalWarehouseId());
//					}
					map.put("permissions", permissions);
					map.put("stockDetailPer", true);
				} else {
					Map<Integer, List<Integer>> organizationPermissionMap = JSONObject.parseArray(this.logincalOrganizationPermission,
							LogincalPermission.class).stream().collect(Collectors.toMap(LogincalPermission::getOrgId, LogincalPermission::getPermissions));
					//当前用户的所有部门信息
					List<Organization> organizations = organizationMapper.getOrganizationsByUserId(user.getUserId());
					ArrayList<Organization> threeLevelOrganizations = new ArrayList<>();
					if (CollectionUtils.isNotEmpty(organizations)){
						organizations.stream().forEach(organizationInfo -> {
							for (int index = 0; index < 5; index++) {
								if (Objects.isNull(organizationInfo)) {
									break;
								}
								if (organizationInfo.getLevel() != 3){
									Organization organizationForQuery = new Organization();
									organizationForQuery.setOrgId(organizationInfo.getParentId());
									Organization byOrg = organizationMapper.getByOrg(organizationForQuery);
									if (Objects.isNull(byOrg)) {
										break;
									} else {
										organizationInfo = byOrg;
									}
								} else {
									threeLevelOrganizations.add(organizationInfo);
									break;
								}
							}
						});
					}

					//取该用户再所有部门下的最后的展示权限
					List<Integer> orgPermissionsResult = null;
					Boolean stockDetailPermissionResult = null;
					if (CollectionUtils.isNotEmpty(threeLevelOrganizations)){
						for (Organization threelevelOrganization : threeLevelOrganizations) {
							List<Integer> organizationPermissions = organizationPermissionMap.get(threelevelOrganization.getOrgId());
							if (orgPermissionsResult == null){
								orgPermissionsResult =  organizationPermissions;
							}else {
								orgPermissionsResult = CollectionUtils.isNotEmpty(organizationPermissions) && orgPermissionsResult != null &&
										organizationPermissions.size() > orgPermissionsResult.size() ? organizationPermissions : orgPermissionsResult;
							}
							for (Integer stockDetailPer : JSONObject.parseArray(stockDetailPer, Integer.class)) {
							    if (stockDetailPermissionResult == null){
                                    stockDetailPermissionResult  = stockDetailPer.equals(threelevelOrganization.getOrgId()) ? true : null;
                                }
							}
						}
					}
					if(CollectionUtils.isNotEmpty(orgPermissionsResult)){
						//电商仓权限  角色判定（熙成）
						if(!orgPermissionsResult.contains(LogicalEnum.XC.getLogicalWarehouseId())){
							List<Role> userRoles = roleMapper.getUserRoles(user.getUserId());
							for (Role userRole : userRoles) {
								if (userRole.getRoleName().contains("查看熙成仓库存")) {
									orgPermissionsResult.add(LogicalEnum.XC.getLogicalWarehouseId());
									stockDetailPermissionResult = true;
								}
							}
						}
					}

					map.put("permissions", orgPermissionsResult);
					map.put("stockDetailPer", stockDetailPermissionResult);
				}
			} catch (Exception e) {
				logger.error("逻辑仓权限分配  userId:{}" , user.getUserId(),e);
			}
			map.put("goodsList", list);
			map.put("page", page);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return map;
	}

    /**
     * 设置可用库存与占用库存的总数量
     *
     * @param stockMap
     */
	private void setTotalNum(Map<String, Integer> stockMap) {
		Integer totalNum = 0;
		for (LogicalEnum logicalEnum : LogicalEnum.values()) {
			totalNum  += stockMap.get(logicalEnum.getLogicalWarehouseCode()) ;
		}
		stockMap.put("totalNum" ,totalNum);
	}

	@Override
	public ResultInfo<?> batchOptGoods(Goods goods) {
		ResultInfo<?> result = new ResultInfo<>();
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Goods>> TypeRef = new TypeReference<ResultInfo<Goods>>() {};
		String url=httpUrl + "goods/batchoptgoods.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, goods,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}

	@Override
	public List<BuyorderVo> getBuyorderVoList(Goods goods) {
		List<BuyorderVo> list = null;
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<List<BuyorderVo>>> TypeRef = new TypeReference<ResultInfo<List<BuyorderVo>>>() {};
			String url=httpUrl + "goods/getbuyordervolist.htm";
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, goods,clientId,clientKey, TypeRef);
			list = (List<BuyorderVo>) result.getData();
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return list;
	}


	@Override
	public List<SysOptionDefinition> getAssignList(List<SysOptionDefinition> optionDefinitions,HttpSession session) {
		if(null != optionDefinitions && optionDefinitions.size() > 0){
			List<SysOptionDefinition> definitions = null;
			try {
				// 定义反序列化 数据格式
				final TypeReference<ResultInfo<List<SysOptionDefinition>>> TypeRef = new TypeReference<ResultInfo<List<SysOptionDefinition>>>() {};
				String url=httpUrl + "goods/getassignlist.htm";
				ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, optionDefinitions,clientId,clientKey, TypeRef);
				definitions = (List<SysOptionDefinition>) result.getData();
			} catch (IOException e) {
				logger.error(Contant.ERROR_MSG, e);
			}
			User user = (User) session.getAttribute(ErpConst.CURR_USER);
			if(null != definitions){
				for(SysOptionDefinition optionDefinition : definitions){
					optionDefinition.setUserList(rManageCategoryJUserMapper.getUserByManageCategory(optionDefinition.getSysOptionDefinitionId(),user.getCompanyId()));
				}
			}


			return definitions;
		}
		return null;
	}


	@Override
	public List<User> getUserByManageCategory(Integer manageCategory, Integer companyId) {
		return rManageCategoryJUserMapper.getUserByManageCategory(manageCategory, companyId);
	}


	@Override
	@Transactional(rollbackFor = Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
	public Boolean saveEditCategoryOwner(List<Integer> userId, String manageCategories, HttpSession session) {
		if(userId.size() == 0 || null == userId || null == manageCategories || manageCategories.equals("")){
			return false;
		}
		User user = (User) session.getAttribute(ErpConst.CURR_USER);
		String[] manageCategoryList = manageCategories.split(",");
		for(String manageCategory : manageCategoryList){
			//删除归属
			rManageCategoryJUserMapper.deleteByCateCompany(Integer.parseInt(manageCategory),user.getCompanyId());

			for(Integer id : userId){
				RManageCategoryJUser rManageCategoryJUser = new RManageCategoryJUser();
				rManageCategoryJUser.setManageCategory(Integer.parseInt(manageCategory));
				rManageCategoryJUser.setUserId(id);

				rManageCategoryJUserMapper.insert(rManageCategoryJUser);
			}

		}
		return true;
	}


	@Override
	@Transactional(rollbackFor = Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
	public ResultInfo saveUplodeGoodsSafeSotck(List<GoodsSafeStock> list) {
		ResultInfo resultInfo = new ResultInfo<>();
		if(null != list && list.size() > 0){
			for(GoodsSafeStock safeStock : list){
				try {
					Goods goods = new Goods();
					goods.setSku(safeStock.getSku());
					goods.setCompanyId(safeStock.getCompanyId());
					// 定义反序列化 数据格式
					final TypeReference<ResultInfo<Goods>> TypeRef = new TypeReference<ResultInfo<Goods>>() {};
					String url=httpUrl + "goods/getgoodsbysku.htm";
					ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, goods,clientId,clientKey, TypeRef);
					Goods goodsInfo = (Goods) result.getData();

					if(null == goodsInfo){
						resultInfo.setMessage("订货号："+safeStock.getSku() + "不存在！");
						throw new Exception("订货号："+safeStock.getSku() + "不存在！");
					}

					safeStock.setGoodsId(goodsInfo.getGoodsId());

					GoodsSafeStock goodsSafeStock = goodsSafeStockMapper.selectByGoodsId(goodsInfo.getGoodsId(), safeStock.getCompanyId());
					if(null != goodsSafeStock){
						goodsSafeStockMapper.updateByGoodsId(safeStock);
					}else{
						safeStock.setAddTime(safeStock.getModTime());
						safeStock.setCreator(safeStock.getUpdater());
						safeStock.setCompanyId(goodsInfo.getCompanyId());
						goodsSafeStockMapper.insert(safeStock);
					}

				} catch (Exception e) {
					logger.error(Contant.ERROR_MSG, e);
					return resultInfo;
				}

			}
			resultInfo.setCode(0);
			resultInfo.setMessage("操作成功");
		}
		return resultInfo;
	}


	@Override
	@Transactional(rollbackFor = Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
	public ResultInfo<?> batchGoodsPriceSave(List<GoodsChannelPrice> list) throws Exception{

		//预处理list，将sku与省市id组合唯一的重复的GoodsChannelPrice取出存入map
		Map<String,List<GoodsChannelPrice>> map = new HashMap<String,List<GoodsChannelPrice>>();
		for (int i = 0; i < list.size(); i++)
		{
			List<GoodsChannelPrice> gpList = new ArrayList<GoodsChannelPrice>();
			for (int j = 0; j < list.size(); j++)
			{
				/*
				 * && list.get(i).getCustomerTypeComments().equals(list.get(j).
				 * getCustomerTypeComments())
				 */
				if (list.get(i).getSku().equals(list.get(j).getSku()) && list.get(i).getProvinceId().equals(list.get(j).getProvinceId()))
				{
					if (list.get(i).getCityId() == null)
					{
						gpList.add(list.get(j));
					}
					else if (list.get(i).getCityId() != null && (list.get(i).getCityId().equals(list.get(j).getCityId())))
					{
						gpList.add(list.get(j));
					}
				}
			}
			if (list.get(i).getCityId() == null)
			{
				list.get(i).setCityId(0);
			}
			String key = list.get(i).getSku() + list.get(i).getProvinceId() + list.get(i).getCityId();
			map.put(key, gpList);
		}

		for (GoodsChannelPrice goodsChannelPrice : list)
		{
			//
			List<GoodsChannelPrice> oldList = goodsChannelPriceMapper.getGoodsChannelByG(goodsChannelPrice);
			for (GoodsChannelPrice gp2 : oldList)
			{
				//删除核价信息
				goodsChannelPriceMapper.deleteGoodsPrice(gp2);
				//删除核价附属信息
				GoodsChannelPriceExtend gc = new GoodsChannelPriceExtend();
				gc.setGoodsChannelPriceId(gp2.getGoodsChannelPriceId());
				goodsChannelPriceExtendMapper.deleteGoodsChannelPriceExtend(gc);
			}

			//更新销售核价信息；sku与省市id组合唯一
			GoodsChannelPrice gp = new GoodsChannelPrice();
			gp.setType(0);   // 销售
			gp.setAddTime(goodsChannelPrice.getAddTime());
			gp.setBatchPolicy(goodsChannelPrice.getBatchPolicy());
			// 销售批量价
			gp.setBatchPrice(goodsChannelPrice.getBatchPrice());
			// min 销售批量数量
			gp.setBatchPriceMinNum(goodsChannelPrice.getBatchPriceMinNum());
			// max 销售批量数量
			gp.setBatchPriceMaxNum(goodsChannelPrice.getBatchPriceMaxNum());
			gp.setCityId(goodsChannelPrice.getCityId());
			gp.setCityName(goodsChannelPrice.getCityName());
			gp.setCostPriceEndTime(goodsChannelPrice.getCostPriceEndTime());
			gp.setCostPriceStartTime(goodsChannelPrice.getCostPriceStartTime());
			gp.setCreator(goodsChannelPrice.getCreator());
			gp.setCustomerTypeComments(goodsChannelPrice.getCustomerTypeComments());
			gp.setDistributionPrice(goodsChannelPrice.getDistributionPrice());
			gp.setGoodsId(goodsChannelPrice.getGoodsId());
			gp.setIsManufacturerAuthorization(goodsChannelPrice.getIsManufacturerAuthorization());
			gp.setIsReportTerminal(goodsChannelPrice.getIsReportTerminal());
			gp.setMarketPrice(goodsChannelPrice.getMarketPrice());
			gp.setMinAmount(goodsChannelPrice.getMinAmount());
			gp.setMinNum(goodsChannelPrice.getMinNum());
			gp.setModTime(goodsChannelPrice.getModTime());
			gp.setPeriodDate(goodsChannelPrice.getPeriodDateXs());
			gp.setPrivatePrice(goodsChannelPrice.getPrivatePrice());
			gp.setPublicPrice(goodsChannelPrice.getPublicPrice());
			gp.setProvinceId(goodsChannelPrice.getProvinceId());
			gp.setProvinceName(goodsChannelPrice.getProvinceName());
			gp.setUpdater(goodsChannelPrice.getUpdater());
			gp.setVipPrice(goodsChannelPrice.getVipPrice());
			int gcpId = goodsChannelPriceMapper.insertSelective(gp);
//			String key = goodsChannelPrice.getSku()+goodsChannelPrice.getProvinceId()+goodsChannelPrice.getCityId();
//			String keycg = goodsChannelPrice.getSku()+goodsChannelPrice.getProvinceId()+goodsChannelPrice.getCityId();
//			List<GoodsChannelPrice> gspList = map.get(key);
//			for (GoodsChannelPrice gPrice : gspList)
//			{
//				// 批量价
//				if(gPrice.getBatchPrice()!=null && gPrice.getBatchPriceMinNum() != null)
//				{
//					GoodsChannelPriceExtend gce = new GoodsChannelPriceExtend();
//					gce.setGoodsChannelPriceId(gp.getGoodsChannelPriceId());
//					gce.setPriceType(2);
//					gce.setConditionType(2);
//					gce.setMaxNum(gPrice.getBatchPriceMaxNum());
//					gce.setMinNum(gPrice.getBatchPriceMinNum());
//					gce.setBatchPrice(gPrice.getBatchPrice());
//					goodsChannelPriceExtendMapper.insertSelective(gce);
//				}
//			}

			//更新采购核价信息；sku与省市id组合唯一
			GoodsChannelPrice gpcg = new GoodsChannelPrice();
			gpcg.setType(1); // 采购
			gpcg.setAddTime(goodsChannelPrice.getAddTime());
			gpcg.setBatchPolicy(goodsChannelPrice.getBatchPolicyCg());
			gpcg.setBatchPrice(goodsChannelPrice.getBatchPriceCg());
			gpcg.setBatchPriceMaxNum(goodsChannelPrice.getBatchPriceMaxNumCg());
			gpcg.setBatchPriceMinNum(goodsChannelPrice.getBatchPriceMinNumCg());
			gpcg.setCityId(goodsChannelPrice.getCityId());
			gpcg.setCityName(goodsChannelPrice.getCityName());
			gpcg.setCostPriceEndTime(goodsChannelPrice.getCostPriceEndTime());
			gpcg.setCostPriceStartTime(goodsChannelPrice.getCostPriceStartTime());
			gpcg.setCreator(goodsChannelPrice.getCreator());
			gpcg.setCustomerTypeComments(goodsChannelPrice.getCustomerTypeComments());
			gpcg.setDistributionPrice(goodsChannelPrice.getDistributionPriceCg());
			gpcg.setGoodsId(goodsChannelPrice.getGoodsId());
			gpcg.setIsManufacturerAuthorization(goodsChannelPrice.getIsManufacturerAuthorizationCg());
			gpcg.setIsReportTerminal(goodsChannelPrice.getIsReportTerminalCg());
			gpcg.setMarketPrice(goodsChannelPrice.getMarketPrice());
			gpcg.setMinAmount(goodsChannelPrice.getMinAmountCg());
			gpcg.setMinNum(goodsChannelPrice.getMinNumCg());
			gpcg.setModTime(goodsChannelPrice.getModTime());
			gpcg.setPeriodDate(goodsChannelPrice.getPeriodDateCg());
			gpcg.setPrivatePrice(goodsChannelPrice.getPrivatePriceCg());
			gpcg.setPublicPrice(goodsChannelPrice.getPublicPriceCg());
			gpcg.setProvinceId(goodsChannelPrice.getProvinceId());
			gpcg.setProvinceName(goodsChannelPrice.getProvinceName());
			gpcg.setUpdater(goodsChannelPrice.getUpdater());
			gpcg.setVipPrice(goodsChannelPrice.getVipPrice());
			goodsChannelPriceMapper.insertSelective(gpcg);
			// 拼接key
			String key = goodsChannelPrice.getSku()+goodsChannelPrice.getProvinceId()+goodsChannelPrice.getCityId();
			List<GoodsChannelPrice> gspListCg = map.get(key);
			for (GoodsChannelPrice gPrice : gspListCg)
			{
				// 销售批量价
				if(gPrice.getBatchPrice()!=null && gPrice.getBatchPriceMinNum() != null)
				{
					GoodsChannelPriceExtend gce = new GoodsChannelPriceExtend();
					gce.setGoodsChannelPriceId(gp.getGoodsChannelPriceId());
					gce.setPriceType(2);
					gce.setConditionType(2);
					gce.setMaxNum(gPrice.getBatchPriceMaxNum());
					gce.setMinNum(gPrice.getBatchPriceMinNum());
					gce.setBatchPrice(gPrice.getBatchPrice());
					goodsChannelPriceExtendMapper.insertSelective(gce);
				}
				// 采购批量价
				if(gPrice.getBatchPriceCg()!=null && gPrice.getBatchPriceMinNumCg()!=null)
				{
					GoodsChannelPriceExtend gce = new GoodsChannelPriceExtend();
					gce.setGoodsChannelPriceId(gpcg.getGoodsChannelPriceId());
					gce.setPriceType(2);
					gce.setConditionType(2);
					gce.setMaxNum(gPrice.getBatchPriceMaxNumCg());
					gce.setMinNum(gPrice.getBatchPriceMinNumCg());
					gce.setBatchPrice(gPrice.getBatchPriceCg());
					goodsChannelPriceExtendMapper.insertSelective(gce);
				}
				//成本价
				GoodsChannelPriceExtend g = new GoodsChannelPriceExtend();
				g.setGoodsChannelPriceId(gpcg.getGoodsChannelPriceId());
				g.setPriceType(1);
				g.setConditionType(1);
				g.setStartTime(gPrice.getCostPriceStartTime());
				g.setEndTime(gPrice.getCostPriceEndTime());
				g.setBatchPrice(gPrice.getCostPrice());
				goodsChannelPriceExtendMapper.insertSelective(g);
			}
		}
	  return new ResultInfo<>(0,"操作成功");
	}


	/**
	 * 验证sku是否存在产品表
	 */
	@Override
	public List<Goods> batchVailGoodsSku(List<String> sku_list) {
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<List<Goods>>> TypeRef = new TypeReference<ResultInfo<List<Goods>>>() { };
			String url = httpUrl + "goods/batchvailgoodssku.htm";
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, sku_list, clientId, clientKey, TypeRef);
			if(result != null && result.getCode() == 0){
				List<Goods> list = (List<Goods>) result.getData();
				return list;
			}
		} catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);return null;
		}
		return null;
	}


	@Override
	public ResultInfo<?> batchGoodsSettelmentSave(List<GoodsSettlementPrice> list) {
		// 验证产品结算信息是否存在，存在-更新，不存在-新增；goodsId唯一
		int i = goodsSettlementPriceMapper.batchGoodsSettelmentSave(list);
		if(i >=0 ){
			return new ResultInfo<>(0,"操作成功");
		}
		return new ResultInfo<>();
	}

	/**
	 * <b>Description:</b><br>  获取供应商供应的产品ID集合
	 * @return
	 * @Note
	 * <b>Author:</b> Michael
	 * <br><b>Date:</b> 2017年12月5日 上午8:59:34
	 */
	@Override
	public List<Integer> getSupplierGoodsIds(Integer traderSupplierId) {
        	 try {
        		// 定义反序列化 数据格式
        		final TypeReference<ResultInfo<List<Integer>>> TypeRef = new TypeReference<ResultInfo<List<Integer>>>() { };
        		String url = httpUrl + "goods/getsuppliergoodsids.htm";
        		ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, traderSupplierId, clientId, clientKey, TypeRef);
        		if(result != null && result.getCode() == 0){
        			List<Integer> list = (List<Integer>) result.getData();
        			return list;
        		}
        	} catch (Exception e) {
        		logger.error(Contant.ERROR_MSG, e);
        		return null;
        	}
        	 return null;
	}


	@Override
	public ResultInfo<?> saveMainSupply(RGoodsJTraderSupplier rGoodsJTraderSupplier) {
		ResultInfo<?> result = new ResultInfo<>();
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<RGoodsJTraderSupplier>> TypeRef = new TypeReference<ResultInfo<RGoodsJTraderSupplier>>() {};
		String url=httpUrl + "goods/savemainsupply.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, rGoodsJTraderSupplier,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}


	@Override
	public ResultInfo<?> delMainSupply(RGoodsJTraderSupplier rGoodsJTraderSupplier) {
		ResultInfo<?> result = new ResultInfo<>();
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<RGoodsJTraderSupplier>> TypeRef = new TypeReference<ResultInfo<RGoodsJTraderSupplier>>() {};
		String url=httpUrl + "goods/delmainsupply.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, rGoodsJTraderSupplier,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}

	@Override
	public Map<String, Object> getGoodsBaseinfoListPage(Goods goods,Page page,HttpSession session) {
		List<GoodsVo> list = null;
		Map<String,Object> map = new HashMap<>();
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<List<GoodsVo>>> TypeRef = new TypeReference<ResultInfo<List<GoodsVo>>>() {};
			String url=httpUrl + "goods/getgoodsbaseinfolistpage.htm";
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, goods,clientId,clientKey, TypeRef,page);
			list = (List<GoodsVo>) result.getData();
			page = result.getPage();
			map.put("list", list);
			map.put("page", page);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return map;
	}


	@Override
	public ResultInfo restVerify(Goods goods) {
		final TypeReference<ResultInfo> TypeRef2 = new TypeReference<ResultInfo>() {};
		String uri=httpUrl + "verifiesrecord/restverify.htm";
		ResultInfo<?> rs = null;
		try {
			VerifiesInfo verifiesInfo = new VerifiesInfo();
			verifiesInfo.setRelateTable("T_GOODS");
			verifiesInfo.setRelateTableKey(goods.getGoodsId());
			rs = (ResultInfo<?>) HttpClientUtils.post(uri, verifiesInfo,clientId,clientKey, TypeRef2);

		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}

		return rs;
	}


	@Override
	public ResultInfo<?> getGoodsListExtraInfo(Goods goods) {
		ResultInfo<?> result = null;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
		String url=httpUrl + "goods/getgoodslistextrainfo.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, goods,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}


	@Override
	public GoodsExtend getGoodsExtend(Goods goods) {
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<GoodsExtend>> TypeRef = new TypeReference<ResultInfo<GoodsExtend>>() {};
		String url=httpUrl + "goods/getgoodsextend.htm";
		GoodsExtend goodsExtend = new GoodsExtend();
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, goods,clientId,clientKey, TypeRef);
			if(result != null){
				goodsExtend = (GoodsExtend) result.getData();
			}
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
		return goodsExtend;
	}


	@Override
	public ResultInfo<?> saveCommodityPropaganda(Map<String, Object> map) {
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
		};
		String url=httpUrl + "goods/savecommoditypropaganda.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, map, clientId, clientKey, TypeRef2);
			return result;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}


	@Override
	public ResultInfo<?> batchSaveGoodsExtend(List<GoodsExtend> list) {
		ResultInfo<?> result = null;
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<List<GoodsExtend>>> TypeRef = new TypeReference<ResultInfo<List<GoodsExtend>>>() {};
			String url=httpUrl + "goods/batchsavegoodsextend.htm";
			result = (ResultInfo<?>) HttpClientUtils.post(url, list,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}


	@Override
	public ResultInfo<?> copyGoods(Goods goods) {
	    	ResultInfo<?> result = null;
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<Goods>> TypeRef = new TypeReference<ResultInfo<Goods>>() {};
			String url=httpUrl + "goods/copygoods.htm";
			result = (ResultInfo<?>) HttpClientUtils.post(url, goods,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}


	@Override
	public GoodsVo getSaleJHGoodsDetail(Goods goods) {
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<GoodsVo>> TypeRef = new TypeReference<ResultInfo<GoodsVo>>() {};
			String url=httpUrl + "goods/getsalejhgoodsdetail.htm";
			ResultInfo<GoodsVo> result = (ResultInfo<GoodsVo>) HttpClientUtils.post(url, goods,clientId,clientKey, TypeRef);
			if(result == null || result.getCode() == -1){
				return null;
			}
			return result.getData();
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return null;
	}


	@Override
	public ResultInfo<?> saveGoodsVoFaqs(GoodsVo goodsVo) {
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<List<GoodsFaq>>> TypeRef = new TypeReference<ResultInfo<List<GoodsFaq>>>() {};
			String url=httpUrl + "goods/savegoodsfaqs.htm";
			ResultInfo<List<GoodsFaq>> result = (ResultInfo<List<GoodsFaq>>) HttpClientUtils.post(url, goodsVo,clientId,clientKey, TypeRef);
			return result;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return new ResultInfo<>();
		}
	}


	@Override
	public ResultInfo<?> getGoodsVoFaqs(Goods goods) {
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<List<GoodsFaq>>> TypeRef = new TypeReference<ResultInfo<List<GoodsFaq>>>() {};
			String url=httpUrl + "goods/getgoodsfaqsbygoodsid.htm";
			ResultInfo<List<GoodsFaq>> result = (ResultInfo<List<GoodsFaq>>) HttpClientUtils.post(url, goods,clientId,clientKey, TypeRef);
			return result;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return new ResultInfo<>();
		}

	}


	@Override
	public ResultInfo<?> updateGoodsInfoById(Goods goods) {
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
			String url=httpUrl + "goods/updategoodsinfobyid.htm";
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, goods,clientId,clientKey, TypeRef);
			if(result != null){
				return result;
			}
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return new ResultInfo<>();
	}


	@Override
	public Integer getGoodsIdBySku(String skuNo) {
		Integer goodsId = goodsMapper.getGoodsIdBySku(skuNo);
		return goodsId;
	}

	@Override
	public List<SaleorderGoods> getGoodsPriceList(Integer saleorderId) {
		return goodsMapper.getGoodsPriceList(saleorderId);
	}

	@Override
	public Goods getSkuInfo(Integer skuId) {
		Goods skuInfo = goodsMapper.getSkuInfo(skuId);
		Map<String, WarehouseStock> stockInfo = warehouseStockService.getStockInfo(Collections.singletonList(skuInfo.getSku()));
		WarehouseStock warehouseStock = stockInfo.get(skuInfo.getSku());
		skuInfo.setStockNum(warehouseStock.getStockNum());
		return skuInfo;
	}

	@Override
	public String getDeliveryRangeOfSku(String sku) {
		CoreSkuGenerate skuGenerate = coreSkuGenerateMapper.selectBySkuNo(sku);
		if (skuGenerate == null || skuGenerate.getStatus() == 0){
			return "";
		}
		List<RangeDictionary> rangeDictionaryList = rangeDictionaryMapper.getAllDict();
		try {
			if (StringUtils.isNotBlank(skuGenerate.getDeclareDeliveryRange())){
				skuGenerate.setDeliveryRange(skuGenerate.getDeclareDeliveryRange());
			} else {
				skuGenerate.setDeliveryRange(getDistinctRange(sku));
			}
			RangeDictionary dictionary = rangeDictionaryList.stream()
					.filter(d -> d.getName().equals(skuGenerate.getDeliveryRange()))
					.findFirst().orElse(null);
			return null == dictionary ? "" : dictionary.toString();
		} catch (Exception e){
			logger.error("解析预计可发货时间出现异常",e);
		}
		return null;
	}

	/**
	 * @description: .
	 * @jira: sku存在多个区间取最小区间.
	 * @notes: .
	 * @version: 1.0.
	 * @date: 2020/5/22 11:08 上午.
	 * @author: Tomcat.Hui.
	 * @param skuNo: .
	 * @return: java.lang.String.
	 * @throws: .
	 */
	private String getDistinctRange(String skuNo){
		List<CoreSkuGenerate> wtSku = coreSkuGenerateMapper.getWtSkuList(Arrays.asList(skuNo));
		if (null != wtSku && wtSku.size() > 0) {

			if ( wtSku.size() > 1) {
				//去重
				CoreSkuGenerate min = wtSku.stream().min(Comparator.comparing(this::getLeftRange)).orElse(null);
				return null == min ? null : min.getDeliveryRange();
			} else {
				return wtSku.get(0).getDeliveryRange();
			}
		}
		return null;
	}

	private Integer getLeftRange(CoreSkuGenerate sku){
		String numArr[] = sku.getDeliveryRange().replaceAll("[A-Z]+",",").split(",");
		return Integer.parseInt(numArr[1]);
	}

	@Override
	public Map<String, Object> getLogicallistGoodsStockPage(HttpServletRequest request, Goods goods, Page page, User user) {
		Map<String, Object> map = new HashMap<>();
		Map<String, Object> result = new HashMap<>();
		map.put("page", page);
		map.put("goods", goods);
		List<GoodsVo> goodsList = goodsMapper.getSkuInfoListPage(map);
		List<String> skuList = goodsList.stream().map(item -> item.getSku()).collect(Collectors.toList());
		Map<String, WarehouseStock> logicalStockMapInfo = warehouseStockService.getLogicalStockMapInfo(skuList);
		for (GoodsVo goodsVo : goodsList) {
			WarehouseStock warehouseStock = logicalStockMapInfo.get(goodsVo.getSku() + request.getAttribute("logicalId"));
			goodsVo.setStockNum(warehouseStock.getAvailableStockNum());
		}
		result.put("page", page);
		result.put("goodsList",goodsList);
		return result;
	}

	@Override
	public Map<String, Object> getAllLogicallistGoodsStockPage(Goods goods, Page page, User user) {
		Map<String, Object> map = new HashMap<>(2);
		Map<String, Object> result = new HashMap<>();
		map.put("page", page);
		map.put("goods", goods);
		List<GoodsVo> goodsList = goodsMapper.getSkuInfoListPage(map);
		List<String> skuList = goodsList.stream().map(Goods::getSku).collect(Collectors.toList());
		Map<String, WarehouseStock> logicalStockMapInfo = warehouseStockService.getLogicalStockMapInfo(skuList);
		for (GoodsVo goodsVo : goodsList) {
			LogicalEnum[] values = LogicalEnum.values();
//
			int size = (int) (values.length / 0.75f) + 1;
			Map<String, Integer> logicalStockData = new LinkedHashMap<>(size);
			for (LogicalEnum logicalEnum : values) {
				WarehouseStock warehouseStock = logicalStockMapInfo.get(goodsVo.getSku() + logicalEnum.getLogicalWarehouseId());
				if (Objects.nonNull(warehouseStock)) {
					logicalStockData.put(logicalEnum.getLogicalWarehouseName(), warehouseStock.getAvailableStockNum());
				}
			}
			if (CollUtil.isNotEmpty(logicalStockData)) {
				goodsVo.setAvailableStockMap(logicalStockData);
			}

		}
		result.put("page", page);
		result.put("goodsList",goodsList);
		return result;
	}

	@Override
	public ResultInfo<List<Goods>> getSpecialGoodsList(LabelQuery labelQuery) {
		ResultInfo<List<Goods>> resultInfo = new ResultInfo();
		List<Goods> goodsList =  goodsMapper.getSpecialGoodsList();
		resultInfo.setData(goodsList);
		List<SkuVo> skuVoList = labelQuery.getSkuList();
		if (CollectionUtils.isEmpty(skuVoList)) {
			resultInfo.setCode(0);
			resultInfo.setMessage("校验成功");
		}
		AtomicBoolean flag = new AtomicBoolean(false);
		// skuVoList是否包含特殊商品
		skuVoList.stream().anyMatch(skuVo -> {
			if (skuVo.getSkuId() == null) {
				return false;
			}
			List<Goods> newGoodsList = goodsList.stream().filter(goods -> goods.getGoodsId().intValue() == skuVo.getSkuId().intValue()).collect(Collectors.toList());
			if (CollectionUtils.isNotEmpty(newGoodsList)) {
				flag.set(true);
				return true;
			}
			return false;
		});
		if (!flag.get()) {
			resultInfo.setCode(0);
			resultInfo.setMessage("校验成功");
		} else {
			resultInfo.setCode(1);
			String msg = "商品为特殊商品，无需设置订单要求！";
			if (labelQuery.getIsAll() == ErpConst.ZERO) {
				msg = "商品中包含特殊商品，请重新选择商品！";
			}
			resultInfo.setMessage(msg);
		}
		return resultInfo;
	}

	@Override
	public List<GoodsVo> queryGoodsNewListPage(Goods goods,Page page) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("page", page);
		map.put("goods", goods);
		return coreSkuMapper.queryGoodsNewListPage(map);
	}

	@Override
	public Integer getAssignManageUserCountByGoods(Integer goodsId) {
		return goodsMapper.getAssignManageUserCountByGoods(goodsId);
	}

	@Override
	public List<User> getAssignManageUserByGoods(List<Integer> goodsIds) {
		return goodsMapper.getAssignManageUserByGoods(goodsIds);
	}

	@Override
	public List<User> getAssignUserByGoodsId(List<Integer> goodsIds) {
		return goodsMapper.getAssignUserByGoodsId(goodsIds);
	}

	@Override
	public ProductManageAndAsistDto queryProductManageAndAsist(String sku) {
		return coreSkuMapper.queryProductManageAndAsist(sku);
	}

	@Override
	public GoodsVo queryGoodsBySku(String sku) {
		return coreSkuMapper.queryGoodsBySku(sku);
	}

	@Override
	public Map<String, Object> getBhgLogicallistGoodsStockPage(HttpServletRequest request, Goods goods, Page page, User user) {
		Map<String, Object> map = new HashMap<>();
		Map<String, Object> result = new HashMap<>();
		map.put("page", page);
		map.put("goods", goods);
		List<GoodsVo> goodsList = goodsMapper.getCheckSkuInfoListPage(map);
		List<String> skuList = goodsList.stream().map(item -> item.getSku()).collect(Collectors.toList());
		Map<String, WarehouseStock> logicalStockMapInfo = warehouseStockService.getLogicalStockMapInfo(skuList);
		for (GoodsVo goodsVo : goodsList) {
			WarehouseStock warehouseStock = logicalStockMapInfo.get(goodsVo.getSku() + request.getAttribute("logicalId"));
			goodsVo.setStockNum(warehouseStock.getAvailableStockNum());
		}
		result.put("page", page);
		result.put("goodsList",goodsList);
		return result;
	}

	@Override
	public Map<String, Object> getFeeCategory(String sku) {
		return coreSkuMapper.getFeeCategory(sku);
	}

	@Override
	public Map<String, Object> getSampleOrderGoodsPage(Goods goods,Page page, HttpSession session, User user,Integer traderId) {
		List<GoodsVo> goodsList = new ArrayList<>();
		Map<String, Object> result = queryGoodsListPage(goods, page, session);
		if (MapUtils.isNotEmpty(result)){
			goodsList = (List<GoodsVo>)result.get("list");
		}
		if (CollectionUtils.isEmpty(goodsList)){
			return result;
		}
		List<String> skuList = goodsList.stream().map(Goods::getSku).collect(Collectors.toList());
		Map<String, WarehouseStock> stockInfoMap = warehouseStockService.getStockInfo(skuList);
		for (GoodsVo goodsVo : goodsList) {

			if (stockInfoMap.containsKey(goodsVo.getSku())){
				WarehouseStock warehouseStock = stockInfoMap.get(goodsVo.getSku());
				goodsVo.setAvailableStockNum(warehouseStock.getAvailableStockNum());
				goodsVo.setStockNum(warehouseStock.getStockNum());
			} else {
				goodsVo.setAvailableStockNum(0);
				goodsVo.setStockNum(0);
			}


			BigDecimal salePrice = BigDecimal.ZERO;
			BigDecimal middlePrice = BigDecimal.ZERO;
			TraderCustomer traderCustomer = traderCustomerService.getTraderCustomerId(traderId);
			com.vedeng.price.dto.SkuPriceInfoDetailResponseDto skuPriceInfoDetailResponseDto = this.basePriceService.findSkuPriceInfoBySkuNo(goodsVo.getSku());

			if (skuPriceInfoDetailResponseDto != null && traderCustomer != null) {
				if(ErpConst.CUSTOME_RNATURE.equals(traderCustomer.getCustomerNature())){
					//分销商
					salePrice = skuPriceInfoDetailResponseDto.getDistributionPrice();
				}else if(ErpConst.CUSTOME_INTERMIAL.equals(traderCustomer.getCustomerNature())){
					//终端
					salePrice = skuPriceInfoDetailResponseDto.getTerminalPrice();
				}
			}

			if (Objects.nonNull(skuPriceInfoDetailResponseDto)){
				List<BigDecimal> purchasePrice = skuPriceInfoDetailResponseDto.getPurchaseList()
						.stream().filter(apply -> apply != null && apply.getPurchasePrice() != null)
						.map(com.vedeng.price.dto.SkuPriceInfoPurchaseDto::getPurchasePrice).collect(Collectors.toList());

				if (CollectionUtils.isNotEmpty(purchasePrice)) {
					middlePrice = purchasePrice.size() == 1 ? purchasePrice.get(0) : Collections.max(purchasePrice);
				}
			}

			goodsVo.setPrice(salePrice);
			goodsVo.setPurchasePrice(middlePrice);
		}
		return result;
	}
}
