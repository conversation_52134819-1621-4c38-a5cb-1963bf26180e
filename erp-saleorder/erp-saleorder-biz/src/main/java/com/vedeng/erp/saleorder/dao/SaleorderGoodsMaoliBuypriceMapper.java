package com.vedeng.erp.saleorder.dao;

import com.vedeng.erp.saleorder.domain.SaleorderGoodsMaoliBuyprice;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/11/8
 */
public interface SaleorderGoodsMaoliBuypriceMapper {
    int deleteByPrimaryKey(Integer saleorderGoodsMaoliBuypriceId);

    int insert(SaleorderGoodsMaoliBuyprice record);

    int insertSelective(SaleorderGoodsMaoliBuyprice record);

    SaleorderGoodsMaoliBuyprice selectByPrimaryKey(Integer saleorderGoodsMaoliBuypriceId);

    int updateByPrimaryKeySelective(SaleorderGoodsMaoliBuyprice record);

    int updateByPrimaryKey(SaleorderGoodsMaoliBuyprice record);

    void batchInsert(List<SaleorderGoodsMaoliBuyprice> list);

    int deleteBySaleorderId(Integer saleorderId);

    List<Integer> selectUnReadMaoliSaleOrderId();

    List<SaleorderGoodsMaoliBuyprice> queryListBySaleorderId(Integer saleorderId);

    /**
     * 批量更新 buyPrice, priceStatus 和 modTime 字段
     *
     * @param list 需要更新的记录列表
     */
    void batchUpdate(List<SaleorderGoodsMaoliBuyprice> list);
}