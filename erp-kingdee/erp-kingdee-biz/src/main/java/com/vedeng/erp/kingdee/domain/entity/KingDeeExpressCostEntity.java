package com.vedeng.erp.kingdee.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.*;

/**
    * 金蝶快递成本表
    */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(name = "KING_DEE_EXPRESS_COST")
public class KingDeeExpressCostEntity extends BaseEntity {

    private static final long serialVersionUID = -3496869414556918813L;
    /**
    * 主键ID
    */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer kingDeeExpressCostId;

    /**
    * 单据内码
    */
    private String fid;

    /**
    * 单据编号（固定值：“”）该字段为金蝶唯一键
    */
    private String fBillNo;

    /**
    * 发货组织（固定值）
    */
    private String fQzokOrgId;

    /**
    * 原始业务单号，销售订单号
    */
    private String fQzokYsddh;

    /**
    * 归属业务单号，销售订单号
    */
    private String fQzokGsywdh;

    /**
    * 业务类型，固定值：销售订单
    */
    private String fQzokYwlx;

    /**
    * 出入库单号
    */
    private String fQzokCrkdh;

    /**
    * 快递单号
    */
    private String fQzokKddh;

    /**
    * 物料编码
    */
    private String fQzokWlbm;

    /**
    * 物料序列号
    */
    private String fQzokXlh;

    /**
    * 批次号
    */
    private String fQzokPch;

    /**
    * 发货数量
    */
    private String fQzokFhsl;

    /**
    * 快递成本（不含税快递费）
    */
    private String fQzokCb;

    /**
    * 收件人
    */
    private String fQzokSjr;

    /**
    * 收件人电话
    */
    private String fQzokDh;

    /**
    * 收件人地址
    */
    private String fQzokDz;

    /**
    * 贝登单据编号（T_EXPRESS_COST中唯一键）
    */
    private String fQzokBddjbh;

    /**
    * 是否已经删除（true,false）
    */
    private String fQzokSfsc;

    /**
    * 是否计入成本(是“Y”否“N”)，默认为Y，逻辑控制在大数据
    */
    private String fQzokSfjrcb;

    /**
    * 是否删除 0否 1是
    */
    private Integer isDelete;

    /**
    * 更新备注
    */
    private String updateRemark;

    /**
     * 物流公司
     */
    private String fQzokWlgs;

    /**
     * 是否赠品(默认0，赠品 1)
     */
    private Integer fQzokSfzp;

}