<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.system.mapper.TUserWorkdateMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.system.domain.TUserWorkdate">
    <!--@mbg.generated-->
    <!--@Table T_USER_WORKDATE-->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="WORK_DATE" jdbcType="DATE" property="workDate" />
    <result column="WORK_TYPE" jdbcType="INTEGER" property="workType" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="IS_DEL" jdbcType="INTEGER" property="isDel" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, WORK_DATE, WORK_TYPE, ADD_TIME, MOD_TIME, IS_DEL
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_USER_WORKDATE
    where ID = #{id,jdbcType=INTEGER}
  </select>

  <select id="selectByDateStr" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_USER_WORKDATE
    where WORK_DATE = #{workDate,jdbcType=VARCHAR}
  </select>

  <select id="selectByNow"  resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_USER_WORKDATE
    where WORK_DATE = CURRENT_DATE
  </select>


  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_USER_WORKDATE
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.system.domain.TUserWorkdate" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_USER_WORKDATE (WORK_DATE, WORK_TYPE, ADD_TIME, 
      MOD_TIME, IS_DEL)
    values (#{workDate,jdbcType=DATE}, #{workType,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, 
      #{modTime,jdbcType=TIMESTAMP}, #{isDel,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.system.domain.TUserWorkdate" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_USER_WORKDATE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="workDate != null">
        WORK_DATE,
      </if>
      <if test="workType != null">
        WORK_TYPE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="isDel != null">
        IS_DEL,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="workDate != null">
        #{workDate,jdbcType=DATE},
      </if>
      <if test="workType != null">
        #{workType,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.system.domain.TUserWorkdate">
    <!--@mbg.generated-->
    update T_USER_WORKDATE
    <set>
      <if test="workDate != null">
        WORK_DATE = #{workDate,jdbcType=DATE},
      </if>
      <if test="workType != null">
        WORK_TYPE = #{workType,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDel != null">
        IS_DEL = #{isDel,jdbcType=INTEGER},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.system.domain.TUserWorkdate">
    <!--@mbg.generated-->
    update T_USER_WORKDATE
    set WORK_DATE = #{workDate,jdbcType=DATE},
      WORK_TYPE = #{workType,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      IS_DEL = #{isDel,jdbcType=INTEGER}
    where ID = #{id,jdbcType=INTEGER}
  </update>
</mapper>