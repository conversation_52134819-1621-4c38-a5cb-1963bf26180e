package com.vedeng.erp.saleorder.dao;

import com.vedeng.erp.mobile.dto.SaleOrderGoodsListResultDto;
import com.vedeng.erp.saleorder.dto.SaleOrderGoodsDetailDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SaleOrderGoodsDetailMapper {
    /**
     * 根据销售详情id集合查询详情信息
     * @param saleorderGoodsIdList
     * @return
     */
    List<SaleOrderGoodsDetailDto> getRelatedDetail(@Param("saleorderGoodsIdList") List<Integer> saleorderGoodsIdList);

    /**
     * 查询销售商品id集合查询其中虚拟商品的信息
     * @param saleorderGoodsIdList
     * @return
     */
    List<SaleOrderGoodsDetailDto> getExpenseDetail(@Param("saleorderGoodsIdList") List<Integer> saleorderGoodsIdList);

    /**
     * 更新批量销售单收发货状态
     * @param saleOrderGoodsDetailDtos
     * @return
     */
    int updateExpenseGoodsDeliveryAndArrivalStatus(@Param("saleOrderGoodsDetailDtos") List<SaleOrderGoodsDetailDto> saleOrderGoodsDetailDtos);

    /**
     * 查询销售订单中商品信息信息
     * @param saleorderId
     * @return
     */
    List<SaleOrderGoodsDetailDto> findGoodsDetailByOrderId(@Param("saleorderId") Integer saleorderId);

    /**
     * 根据销售商品id查对应销售单中所有销售商品id
     * @param saleorderGoodsIdList
     * @return
     */
    List<SaleOrderGoodsDetailDto> findAllGoodsBySaleOrderGoods(@Param("saleorderGoodsIdList") List<Integer> saleorderGoodsIdList);

    /**
     * 根据销售单id查询销售单中所有商品id
     *
     * @param saleorderIds
     * @return
     */
    List<SaleOrderGoodsListResultDto> getSaleOrderGoodsBySaleorderIds(@Param("saleorderIds") List<Integer> saleorderIds);
}
