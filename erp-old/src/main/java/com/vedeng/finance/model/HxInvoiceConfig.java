package com.vedeng.finance.model;

import java.io.Serializable;
import java.util.Date;

/**
 * T_HX_INVOICE_CONFIG
 * <AUTHOR>
public class HxInvoiceConfig implements Serializable {
    private Integer id;

    /**
     * 配置类型 1字符剔除，2开户行，3账户 4注册地址，5注册电话
     */
    private Integer configType;

    /**
     * 配置内容
     */
    private String content;

    private Long uniqueId;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 修改人
     */
    private Integer updater;

    /**
     * 更新时间
     */
    private Long modTime;


    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getConfigType() {
        return configType;
    }

    public void setConfigType(Integer configType) {
        this.configType = configType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Long getUniqueId() {
        return uniqueId;
    }

    public void setUniqueId(Long uniqueId) {
        this.uniqueId = uniqueId;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    public Long getModTime() {
        return modTime;
    }

    public void setModTime(Long modTime) {
        this.modTime = modTime;
    }

}