package com.vedeng.erp.kingdee.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingdeeInternalProcurementCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeInternalProcurementEntity;
import com.vedeng.erp.kingdee.dto.KingDeeInternalProcurementDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeInternalProcurementQueryResultDto;
import com.vedeng.erp.kingdee.dto.result.KingDeePurchaseReceiptQueryResultDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeInternalProcurementCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeInternalProcurementConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeInternalProcurementRepository;
import com.vedeng.erp.kingdee.service.KingDeeInternalProcurementApiService;
import com.vedeng.erp.kingdee.service.KingDeeInternalProcurementService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryExtParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryFilterDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class KingDeeInternalProcurementServiceImpl extends KingDeeBaseServiceImpl<
        KingDeeInternalProcurementEntity,
        KingDeeInternalProcurementDto,
        KingdeeInternalProcurementCommand,
        KingDeeInternalProcurementRepository,
        KingDeeInternalProcurementConvertor,
        KingDeeInternalProcurementCommandConvertor
        >
        implements KingDeeInternalProcurementService, KingDeeInternalProcurementApiService {


    @Override
    public List<KingDeeInternalProcurementQueryResultDto> getKingDeeInternalProcurement(String flowOrderId) {
        KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        queryParam.setFormId(KingDeeFormConstant.INTERNAL_PROCUREMENT);
        List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("F_QZOK_BDDJTID").value(flowOrderId).build());
        queryParam.setFilterString(queryFilterDtos);
        log.info("金蝶采购入库单查询入参：{}", JSON.toJSONString(queryParam));
        return kingDeeBaseApi.query(queryParam, KingDeeInternalProcurementQueryResultDto.class);
    }


}
