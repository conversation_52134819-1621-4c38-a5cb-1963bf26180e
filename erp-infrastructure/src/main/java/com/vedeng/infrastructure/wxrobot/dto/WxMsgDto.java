package com.vedeng.infrastructure.wxrobot.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 **/
@Setter
@Getter
@NoArgsConstructor
public class WxMsgDto {

    private String msgtype = "markdown";

    private Content markdown = new Content();


    /**
     * 错误信息数据
     */
    @Setter
    @Getter
    @NoArgsConstructor
    public static class Content {

        private String content;

        private List<String> mentioned_list;

        private List<String> mentioned_mobile_list;

    }

    /**
     * 初始化消息
     * @param content content
     * @return WxMsgDto
     */
    public WxMsgDto initWxMsgDto(String content) {
        WxMsgDto wxMsg = new WxMsgDto();
        WxMsgDto.Content markdown = wxMsg.getMarkdown();
        markdown.setContent(content);
        wxMsg.setMarkdown(markdown);
        return wxMsg;
    }


}
