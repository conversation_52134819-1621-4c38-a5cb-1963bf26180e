function colse(){//关闭
	checkLogin();
	index = layer.confirm("您是否确认该操作？", {
		  btn: ['确定','取消'] //按钮
		}, function(){
			$.ajax({
				url:page_url+'/order/buyorder/saveCloseAfterSales.do',
				data:$('#myform').serialize(),
				type:"POST",
				dataType : "json",
				async: false,
				success:function(data)
				{
					if(data.code==0){
						self.location.reload();
					}else{
						layer.alert(data.message);
					}
					
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
			return false;
	layer.close(index);
		}, function(){
		});
}

function editAfterSales(){//编辑
	checkLogin();
	$("#myform").attr("action", page_url + "/order/newBuyorder/editAfterSalesPage.do");
	$("#myform").submit();
}

function editAfterSales2(){//编辑2 核通过后未执行退款运算可执行
	checkLogin();
	$("#myform").attr("action", page_url + "/order/newBuyorder/editAfterSalesPage2.do");
	$("#myform").submit();
}

//售后实退金额小于0，需要付款
function applyPay(){
	checkLogin();
	index = layer.confirm("是否确认当前操作？", {
		  btn: ['确定','取消'] //按钮
		}, function(){
			$.ajax({
				url:page_url+'/aftersales/order/saveRealRefundAmountApplyPay.do',
				data:$('#myform').serialize(),
				type:"POST",
				dataType : "json",
				async: false,
				success:function(data)
				{
					if(data.code==0){
						self.location.reload();
					}else{
						layer.alert(data.message);
					}
					
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
			return false;
	layer.close(index);
		}, function(){
		});
}

function applyAudit(){//申请审核
	checkLogin();
	index = layer.confirm("您是否确认该操作？", {
		  btn: ['确定','取消'] //按钮
		}, function(){
			$.ajax({
				url:page_url+'/order/buyorder/editApplyAudit.do',
				data:$('#myform').serialize(),
				type:"POST",
				dataType : "json",
				async: false,
				success:function(data)
				{
					if(data.code==0){
						self.location.reload();
					}else{
						layer.alert(data.message);
					}
					
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
			return false;
	layer.close(index);
		}, function(){
		});
}

function passAudit(){//审核通过
	checkLogin();
	var type = $("input[name='type']").val();
	var msg ="";
	if(type ==546){
		msg ="是否确认审核通过？您需要确认退货手续费及发票是否需寄回。";
	}else if(type ==547){
		msg ="是否确认审核通过？您需要确认换货手续费及发票是否需寄回。";
	}else{
		msg ="是否确认审核通过？";
	}
	index = layer.confirm(msg, {
		  btn: ['确定','取消'] //按钮
		}, function(){
			$.ajax({
				url:page_url+'/aftersales/order/editPassAudit.do',
				data:$('#myform').serialize(),
				type:"POST",
				dataType : "json",
				async: false,
				success:function(data)
				{
					if(data.code==0){
						self.location.reload();
					}else{
						layer.alert(data.message);
					}
					
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
			return false;
	layer.close(index);
		}, function(){
		});

}

//执行退款运算
function executeRefundOperation(){
	checkLogin();
	index = layer.confirm("是否确认当前操作？", {
		  btn: ['确定','取消'] //按钮
		}, function(){
			$.ajax({
				url:page_url+'/aftersales/order/executeRefundOperation.do',
				data:$('#myform').serialize(),
				type:"POST",
				dataType : "json",
				async: false,
				success:function(data)
				{
					if(data.code==0){
						self.location.reload();
					}else{
						layer.alert(data.message);
					}
					
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
					}
				}
			});
			return false;
	layer.close(index);
		}, function(){
		});
}

function confirmComplete(){//确认完成
	checkLogin();
	index = layer.confirm("您是否确认该订单已完成？确认后无法操作、无法更改状态！", {
		btn: ['确定','取消'] //按钮
	}, function(){
		$.ajax({
			url:page_url+'/order/newBuyorder/editConfirmComplete.do',
			data:$('#myform').serialize(),
			type:"POST",
			dataType : "json",
			async: false,
			success:function(data)
			{
				if(data.code==0){
					self.location.reload();
				}else{
					layer.alert(data.message);
				}

			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
		return false;
		layer.close(index);
	}, function(){
	});

}

function confirmCompleteAlert() {
	checkLogin();
	/**
	 * <input type="hidden" name="afterSalesId" value="${afterSalesVo.afterSalesId}"/>
	 <input type="hidden" name="orderId" value="${afterSalesVo.orderId}"/>
	 <input type="hidden" name="subjectType" value="${afterSalesVo.subjectType}"/>
	 <input type="hidden" name="type" value="${afterSalesVo.type}"/>
	 <input type="hidden" name="formToken" value="${formToken}"/>
	 */

	var afterSalesId = $("input[name=afterSalesId]").val();
	var orderId = $("input[name=orderId]").val();
	var subjectType = $("input[name=subjectType]").val();
	var type = $("input[name=type]").val();
	var formToken = $("input[name=formToken]").val();
	layer.open({
		type: 1,
		closeBtn: 1,
		title: "",
		shadeClose: true,
		area:["510px","250px"],
		content:

			"        <input type='hidden' name='afterSalesId' value='"+ afterSalesId +"'/>" +
			"        <input type='hidden' name='orderId' value='"+orderId+"'/>" +
			"        <input type='hidden' name='subjectType' value="+subjectType+"/>" +
			"        <input type='hidden' name='type' value='"+type+"'/>" +
			"        <input type='hidden' name='formToken' value='"+formToken+"'/>" +
			"<div style='width:500px;margin-left: 10px;margin-top: 10px'><p style='display: inline-block'>出库数量＜【申请退货数量-未收货数量】,是否继续确认完成?</p></div>"+
			"<div style='width:500px;margin-left: 10px;margin-top: 10px'><p style='display: inline-block'><span style='color: red'>*</span>备注</p><textarea type='text' style='margin-left: 10px;height: 100px;width: 400px' id='invalidReason' name='invalidReason' placeholder='请输入完成原因' value=''/></div>"+
			"<div style='text-align: center;margin-top: 20px'><button  class='layui-btn layui-btn-normal ' onclick='doComplete()'>确认</button><button class='layui-btn layui-btn-normal ' style='margin-left: 30px;background-color:  #ADADAD' onclick='closeLayer()' >取消</button></div>"
	});

}

function doComplete(){
	debugger;
	var afterSalesId = $("input[name=afterSalesId]").val();
	var orderId = $("input[name=orderId]").val();
	var subjectType = $("input[name=subjectType]").val();
	var type = $("input[name=type]").val();
	var formToken = $("input[name=formToken]").val();
	var completeReason = $("#invalidReason").val();
	console.log(afterSalesId);
	console.log(invalidReason);
	if(completeReason == null || completeReason == "" || completeReason == undefined){
		layer.alert("请填写原因")
	}

	$.ajax({
		url:page_url+'/order/newBuyorder/editConfirmCompleteAlert.do',
		data:{  afterSalesId: afterSalesId,
			    orderId: orderId,
				subjectType:subjectType,
			 	type: type,
				formToken: formToken,
				completeReason: completeReason
		},
		type:"POST",
		dataType : "json",
		async: false,
		success:function(data)
		{
			if(data.code==0){
				self.location.reload();
			}else{
				layer.alert(data.message);
			}

		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});
	return false;
    layer.closeAll();
}

function closeLayer(){
	layer.closeAll();
}

function confirmCompleteAlert2() {
    layer.confirm("换货出库或入库数量<申请换货数量，请确保换货完全在确认完成",{btn:"取消"})
}
var flag = true;
/**
 * 采购退票无需退票
 */
function withOutRetureTicket(afterSalesInvoiceId,afterSalesId){
	checkLogin();
	layer.confirm('您是否确认无需退票？', {
		btn : [ '确定', '取消' ] ,// 按钮
		cancel : function(index, layero) { // 取消操作，点击右上角的X
			flag = true;
		}
	}, function() { // 是
		if(flag){
			flag = false;
			$.ajax({
				async : false,
				url : page_url + '/after/newBuyorder/withOutRetureTicket.do',
				data : {"afterSalesInvoiceId":afterSalesInvoiceId,"afterSalesId":afterSalesId},
				type : "POST",
				dataType : "json",
				success : function(data) {
					layer.alert(data.message, {
						icon : (data.code == 0 ? 1 : 2)
					}, function(index) {
						if(data.code == 0){
							location.reload();
						}else{
							flag = true;
							layer.close(index);
						}
					});
				},error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			})
		}
	}, function() { //否
		flag = true;
	});
}

/**
 * 采购售后申请冲销
 */
function applyCoverTicket(invoiceId,afterSalesId){
	checkLogin();
	layer.confirm('您是否确认申请冲销？', {
		btn : [ '确定', '取消' ] ,// 按钮
		cancel : function(index, layero) { // 取消操作，点击右上角的X
			flag = true;
		}
	}, function() { // 是
		if(flag){
			flag = false;
			$.ajax({
				async : false,
				url : page_url + '/after/newBuyorder/applyCoverTicket.do',
				data : {"invoiceId":invoiceId,"afterSalesId":afterSalesId},
				type : "POST",
				dataType : "json",
				success : function(data) {
					layer.alert(data.message, {
						icon : (data.code == 0 ? 1 : 2)
					}, function(index) {
						if(data.code == 0){
							location.reload();
						}else{
							flag = true;
							layer.close(index);
						}
					});
				},error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			})
		}
	}, function() { //否
		flag = true;
	});
}
