package com.vedeng.flash.service.message;

import com.vedeng.common.util.MessageUtil;
import com.vedeng.flash.dto.SendMessageDto;
import com.vedeng.flash.model.EarlyWarningTask;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * 消息发送抽象类
 */
public abstract class AbstractMessageSender implements MessageSender {

    @Override
    public void sendMesssage(SendMessageDto sendMessageDto) {

        Integer messageTemplateId = getMessageTemplateId();

        List<Integer> userIdList = getMessageUserIdList(sendMessageDto);

        if (CollectionUtils.isEmpty(userIdList)) {
            return;
        }

        Map<String,String> templateVariableMap = getTemplateVariableMap(sendMessageDto);

        String url = getUrl(sendMessageDto);

        MessageUtil.sendMessage(messageTemplateId, userIdList, templateVariableMap, url);
    }

    protected abstract String getUrl(SendMessageDto sendMessageDto);

    protected abstract List<Integer> getMessageUserIdList(SendMessageDto sendMessageDto);

    protected abstract Integer getMessageTemplateId();

    protected abstract Map getTemplateVariableMap(SendMessageDto sendMessageDto);
}
