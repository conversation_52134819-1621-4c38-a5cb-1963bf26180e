package com.vedeng.erp.system.service;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.system.dto.RoleUserRegionConfigDto;
import com.vedeng.erp.system.vo.BusinessUserVO;
import com.vedeng.erp.system.vo.RoleUserRegionConfigQueryVO;
import com.vedeng.erp.system.vo.RoleUserRegionConfigVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 人员与产线区域配置Service接口
 */
public interface RoleUserRegionConfigApiService {
    
    /**
     * 分页查询人员与产线区域配置
     * @param pageParam 分页查询参数
     * @return 分页结果
     */
    PageInfo<RoleUserRegionConfigVO> pageRoleUserRegionConfig(PageParam<RoleUserRegionConfigQueryVO> pageParam);
    
    /**
     * 获取人员与产线区域配置详情
     * @param id 配置ID
     * @return 配置实体
     */
    RoleUserRegionConfigDto getById(Long id);
    
    /**
     * 新增人员与产线区域配置
     * @param dto 配置实体
     * @return 是否成功
     */
    boolean saveRoleUserRegionConfig(RoleUserRegionConfigDto dto);
    
    /**
     * 修改人员与产线区域配置
     * @param dto 配置实体
     * @return 是否成功
     */
    boolean updateRoleUserRegionConfig(RoleUserRegionConfigDto dto);
    
    /**
     * 删除人员与产线区域配置
     * @param id 配置ID
     * @return 是否成功
     */
    boolean deleteRoleUserRegionConfig(Long id);
    
    /**
     * 批量删除人员与产线区域配置
     * @param ids ID列表
     * @return 是否成功
     */
    boolean batchDeleteRoleUserRegionConfig(List<Long> ids);
    
    /**
     * 获取所有业务人员列表（线上/线下销售、产线人员）
     * @param userName 用户名称（可选）
     * @return 业务人员列表
     */
    List<BusinessUserVO> listAllBusinessUsers(String userName);

    /**
     * 获取人员与产线区域配置列表
     * @param query 查询条件
     * @return 人员与产线区域配置列表
     */
    List<RoleUserRegionConfigVO> listRoleUserRegionConfig(RoleUserRegionConfigQueryVO query);

    /**
     * 导入数据
     * @param file Excel文件
     * @return 导入结果
     */
    String importData(MultipartFile file);

    /**
     * 导出数据
     * @param query 查询条件
     * @param response HTTP响应
     * @return 处理结果消息
     */
    String exportData(RoleUserRegionConfigQueryVO query, HttpServletResponse response);

    /**
     * 下载模板
     * @param response HTTP响应
     * @return 处理结果消息
     */
    String downloadTemplate(HttpServletResponse response);


} 