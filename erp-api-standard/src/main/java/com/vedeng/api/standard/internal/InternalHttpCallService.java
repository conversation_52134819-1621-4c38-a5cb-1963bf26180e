package com.vedeng.api.standard.internal;

import com.vedeng.common.constant.ErpConst;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.stereotype.Service;
import org.springframework.context.ApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Map;

/**
 * 通用内部HTTP调用服务
 * 提供统一的内部Controller调用能力，支持各种业务模块
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-18
 */
@Service
public class InternalHttpCallService {
    
    private static final Logger logger = LoggerFactory.getLogger(InternalHttpCallService.class);
    
    @Autowired
    private ApplicationContext applicationContext;
    
    /**
     * 响应解析器（用于解析JSON响应中的错误信息）
     */
    private ResponseParser responseParser;
    
    @PostConstruct
    public void init() {
        this.responseParser = new StandardResponseParser();
        logger.info("InternalHttpCallService initialized with StandardResponseParser");
    }
    
    /**
     * 执行内部调用
     * 
     * @param request 内部调用请求
     * @return 调用结果
     */
    public InternalCallResult execute(InternalCallRequest request) {
        logger.info("开始内部调用: controller={}, method={}",
            request.getControllerBeanName(), request.getMethodName());
        
        InternalCallResult result = new InternalCallResult();

        // 保存原始的RequestAttributes
        ServletRequestAttributes originalAttributes =
            (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();

        try {
            // 1. 验证请求参数
            validateRequest(request);

            // 2. 创建模拟的HTTP环境
            MockHttpServletRequest httpRequest = createMockRequest(request);
            MockHttpServletResponse httpResponse = new MockHttpServletResponse();

            // 3. 设置RequestContextHolder以支持AOP切面
            ServletRequestAttributes requestAttributes = new ServletRequestAttributes(httpRequest, httpResponse);
            RequestContextHolder.setRequestAttributes(requestAttributes);

            // 4. 获取目标Controller
            Object controller = getController(request.getControllerBeanName());

            // 5. 执行方法调用
            Object methodResult = invokeMethod(controller, request, httpRequest, httpResponse);
            
            // 6. 处理调用结果并解析响应
            processResultWithParsing(methodResult, httpResponse, result);
            
            // 7. 根据响应解析结果设置最终状态
            if (result.getData() != null && responseParser.canParse(result.getData())) {
                ResponseParser.ParseResult parseResult = responseParser.parse(result.getData());
                if (!parseResult.isSuccess()) {
                    // 业务逻辑返回了错误，更新InternalCallResult状态
                    result.setSuccess(false);
                    result.setBusinessCode(parseResult.getBusinessCode());
                    result.setErrorMessage(parseResult.getErrorMessage());
                    logger.info("检测到业务错误: code={}, message={}", 
                        parseResult.getBusinessCode(), parseResult.getErrorMessage());
                } else {
                    result.setSuccess(true);
                    // 如果解析后的数据不同于原数据，则更新数据
                    if (parseResult.getData() != result.getData()) {
                        result.setData(parseResult.getData());
                    }
                }
            } else {
                result.setSuccess(true);
            }
            result.markCompleted();
            
            logger.info("内部调用成功: controller={}, method={}, executionTime={}ms", 
                request.getControllerBeanName(), request.getMethodName(), result.getExecutionTime());
            
        } catch (Exception e) {
            logger.error("内部调用失败: controller={}, method={}",
                request.getControllerBeanName(), request.getMethodName(), e);

            result.setSuccess(false);
            result.setException(e);
            result.setErrorMessage("内部调用失败: " + e.getMessage());
            result.markCompleted();
        } finally {
            // 恢复原始的RequestAttributes
            if (originalAttributes != null) {
                RequestContextHolder.setRequestAttributes(originalAttributes);
            } else {
                RequestContextHolder.resetRequestAttributes();
            }
        }

        return result;
    }
    
    /**
     * 验证请求参数
     */
    private void validateRequest(InternalCallRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("内部调用请求不能为空");
        }
        
        if (request.getControllerBeanName() == null || request.getControllerBeanName().trim().isEmpty()) {
            throw new IllegalArgumentException("Controller Bean名称不能为空");
        }
        
        if (request.getMethodName() == null || request.getMethodName().trim().isEmpty()) {
            throw new IllegalArgumentException("方法名称不能为空");
        }
        
        if (request.isNeedSession() && request.getCurrentUser() == null) {
            throw new IllegalArgumentException("需要会话支持时，当前用户不能为空");
        }
    }
    
    /**
     * 创建模拟的HTTP请求
     */
    private MockHttpServletRequest createMockRequest(InternalCallRequest request) {
        MockHttpServletRequest httpRequest = new MockHttpServletRequest();
        
        // 设置基本信息
        httpRequest.setMethod(request.getHttpMethod());
        httpRequest.setRequestURI("/internal/" + request.getControllerBeanName() + "/" + request.getMethodName());
        
        // 设置请求参数
        if (request.getParameters() != null) {
            for (Map.Entry<String, Object> entry : request.getParameters().entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                if (value != null) {
                    // 检查是否为数组参数
                    if (value instanceof String[]) {
                        // 字符串数组参数，直接设置
                        httpRequest.setParameter(key, (String[]) value);
                        logger.debug("设置数组参数: {}={}", key, java.util.Arrays.toString((String[]) value));
                    } else if (value instanceof Object[]) {
                        // 其他类型数组，转换为字符串数组
                        Object[] objectArray = (Object[]) value;
                        String[] stringArray = new String[objectArray.length];
                        for (int i = 0; i < objectArray.length; i++) {
                            stringArray[i] = objectArray[i] != null ? objectArray[i].toString() : null;
                        }
                        httpRequest.setParameter(key, stringArray);
                        logger.debug("设置对象数组参数: {}={}", key, java.util.Arrays.toString(stringArray));
                    } else {
                        // 单个值参数
                        httpRequest.setParameter(key, value.toString());
                        logger.debug("设置单值参数: {}={}", key, value.toString());
                    }
                }
            }
        }
        
        // 设置请求头
        if (request.getHeaders() != null) {
            for (Map.Entry<String, String> entry : request.getHeaders().entrySet()) {
                httpRequest.addHeader(entry.getKey(), entry.getValue());
            }
        }
        
        // 设置默认请求头
        httpRequest.addHeader("Content-Type", "application/x-www-form-urlencoded");
        httpRequest.addHeader("User-Agent", "Internal-API-Call/1.0");
        
        // 创建会话并设置用户信息
        if (request.isNeedSession() && request.getCurrentUser() != null) {
            MockHttpSession session = new MockHttpSession();
            session.setAttribute(ErpConst.CURR_USER, request.getCurrentUser());
            httpRequest.setSession(session);
        }
        
        logger.debug("创建模拟请求: method={}, uri={}, paramsCount={}",
            httpRequest.getMethod(), httpRequest.getRequestURI(),
            request.getParameters() != null ? request.getParameters().size() : 0);
        
        return httpRequest;
    }
    
    /**
     * 获取Controller实例
     */
    private Object getController(String beanName) {
        try {
            Object controller = applicationContext.getBean(beanName);
            if (controller == null) {
                throw new RuntimeException("无法获取Controller实例: " + beanName);
            }
            return controller;
        } catch (Exception e) {
            throw new RuntimeException("获取Controller失败: " + beanName, e);
        }
    }
    
    /**
     * 执行方法调用
     */
    private Object invokeMethod(Object controller, InternalCallRequest request, 
                               HttpServletRequest httpRequest, HttpServletResponse httpResponse) throws Exception {
        
        // 获取方法
        Method method = controller.getClass().getMethod(request.getMethodName(), request.getParameterTypes());
        
        // 准备方法参数
        Object[] args = prepareMethodArguments(request, httpRequest, httpResponse);
        
        // 执行方法调用
        return method.invoke(controller, args);
    }
    
    /**
     * 准备方法参数
     */
    private Object[] prepareMethodArguments(InternalCallRequest request,
                                          HttpServletRequest httpRequest,
                                          HttpServletResponse httpResponse) {
        Class<?>[] parameterTypes = request.getParameterTypes();
        Object[] customParameters = request.getCustomParameters();
        Object[] args = new Object[parameterTypes.length];

        logger.debug("准备方法参数: parameterTypes={}, customParametersLength={}",
                parameterTypes != null ? parameterTypes.length : 0,
                customParameters != null ? customParameters.length : 0);

        int customParamIndex = 0;

        for (int i = 0; i < parameterTypes.length; i++) {
            Class<?> paramType = parameterTypes[i];

            if (HttpServletRequest.class.isAssignableFrom(paramType)) {
                args[i] = httpRequest;
                logger.debug("参数[{}]: HttpServletRequest", i);
            } else if (HttpServletResponse.class.isAssignableFrom(paramType)) {
                args[i] = httpResponse;
                logger.debug("参数[{}]: HttpServletResponse", i);
            } else if (customParameters != null && customParamIndex < customParameters.length) {
                // 使用自定义参数
                args[i] = customParameters[customParamIndex];
                logger.info("参数[{}]: 自定义参数 type={}, value={}", i, paramType.getSimpleName(), args[i]);
                customParamIndex++;
            } else {
                args[i] = null;
                logger.warn("参数[{}]: null (type={})", i, paramType.getSimpleName());
            }
        }

        return args;
    }
    
    /**
     * 处理方法调用结果（包含响应解析）
     */
    private void processResultWithParsing(Object methodResult, MockHttpServletResponse httpResponse, InternalCallResult result) {
        // 设置HTTP状态码
        result.setHttpStatus(httpResponse.getStatus());
        
        // 处理响应头
        for (String headerName : httpResponse.getHeaderNames()) {
            result.addResponseHeader(headerName, httpResponse.getHeader(headerName));
        }
        
        if (methodResult instanceof ModelAndView) {
            // 处理ModelAndView结果
            ModelAndView mv = (ModelAndView) methodResult;
            result.setData(mv.getModel());
            result.addAttribute("viewName", mv.getViewName());
            result.addAttribute("resultType", "ModelAndView");
            
            logger.info("处理ModelAndView结果: viewName={}, modelSize={}",
                mv.getViewName(), mv.getModel() != null ? mv.getModel().size() : 0);
            
        } else {
            // 处理其他类型的结果
            result.setData(methodResult);
            result.addAttribute("resultType", methodResult != null ? methodResult.getClass().getSimpleName() : "null");
            
            logger.info("处理方法结果: type={}",
                methodResult != null ? methodResult.getClass().getSimpleName() : "null");
        }
    }
    

}
