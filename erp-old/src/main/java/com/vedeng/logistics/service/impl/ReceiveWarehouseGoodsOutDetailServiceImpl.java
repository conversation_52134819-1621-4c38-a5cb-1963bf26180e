package com.vedeng.logistics.service.impl;

import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.logistics.eums.WarehouseGoodsOutEnum;
import com.vedeng.logistics.model.WarehouseGoodsOutIn;
import com.vedeng.logistics.model.vo.WarehouseGoodsOutLogVo;
import com.vedeng.logistics.model.vo.WarehouseGoodsOutVo;
import com.wms.model.po.WmsOutputOrder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @description: 领用出库
 * @author: Zeno.zuo
 * @email: <EMAIL>
 * @date: 2022/11/16 15:35
 **/
@Service
@Slf4j
public class ReceiveWarehouseGoodsOutDetailServiceImpl extends BaseWarehouseGoodsOutDetailService {

    /**
     * wms出库单类型-领用出库
     */
    private final Integer WMS_OUTPUT_ORDER_TYPE_RECEIVE = 3;

    /**
     * 领用出库详情页
     */
    private final String DETAIL_URL_RECEIVE_OUT = "/wms/receiveOut/receiveDetail.do?receiveOutId=";



    @Override
    public WarehouseGoodsOutVo detailWarehouseGoodsOut(String outInNo) {
        WarehouseGoodsOutVo warehouseGoodsOutVo = commonDetailWarehouseGoodsOut(outInNo, WarehouseGoodsOutEnum.RECEIVE_WAREHOUSE_OUT.getErpCode());
        if (warehouseGoodsOutVo != null){
            String wmsReceiveOutOrderNo = warehouseGoodsOutVo.getRelateNo();
            if (StringUtils.isNotBlank(wmsReceiveOutOrderNo)){
                WmsOutputOrder wmsReceiveOutOrder = wmsOutputOrderMapper.getWmsOutputOrderByOrderNoAndType(wmsReceiveOutOrderNo, WMS_OUTPUT_ORDER_TYPE_RECEIVE);
                if(Objects.nonNull(wmsReceiveOutOrder)){
                    warehouseGoodsOutVo.setBelongUserName(wmsReceiveOutOrder.getCreator());
                    if(StringUtils.isNotBlank(wmsReceiveOutOrder.getCreator())){
                        User receiveOutCreator = userMapper.getUserByUserName(wmsReceiveOutOrder.getCreator());
                        if(receiveOutCreator != null && StringUtils.isNotBlank(receiveOutCreator.getOrgName())){
                            warehouseGoodsOutVo.setBelongUserOrgName(receiveOutCreator.getOrgName());
                        }
                    }
                }
            }
        }
        return warehouseGoodsOutVo;
    }

    @Override
    public List<WarehouseGoodsOutLogVo> relatedNoWarehouseGoodsOutLogList(String relatedNo) {
        List<WarehouseGoodsOutLogVo> warehouseGoodsOutLogVoList = warehouseGoodsOutInMapper.selectWarehouseGoodsOutLogListByRelatedNo(WarehouseGoodsOutEnum.RECEIVE_WAREHOUSE_OUT.getErpCode(), relatedNo);
        return CollectionUtils.isNotEmpty(warehouseGoodsOutLogVoList) ? warehouseGoodsOutLogVoList : new ArrayList<>();
    }

    @Override
    public String relatedNoDetailRedirect(String relatedNo) {
        WmsOutputOrder wmsOutputOrder = wmsOutputOrderMapper.getWmsOutputOrderByOrderNoAndType(relatedNo,WMS_OUTPUT_ORDER_TYPE_RECEIVE);
        String url = "";
        if (wmsOutputOrder != null){
            url = REDIRECT_URL_PREFIX + DETAIL_URL_RECEIVE_OUT + wmsOutputOrder.getId();
        }
        return url;
    }
}
