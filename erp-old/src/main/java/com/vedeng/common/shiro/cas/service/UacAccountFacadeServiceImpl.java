package com.vedeng.common.shiro.cas.service;

import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.uac.api.constants.UacConstants;
import com.vedeng.uac.api.dto.AccountSignOnLogDto;
import com.vedeng.uac.api.dto.MenuNodeDTO;
import com.vedeng.uac.api.dto.PermissionDTO;
import com.vedeng.uac.api.dto.UserDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class UacAccountFacadeServiceImpl implements UacAccountFacadeService {

    private final static Logger LOGGER = LoggerFactory.getLogger(UacAccountFacadeServiceImpl.class);


    @Value("${common.uacPrefixUrl}")
    private String uacPrefixUrl;

    private final RestTemplate restTemplate;

    @Value("${cas.client.platformNo}")
    private Integer platformNo;

    public UacAccountFacadeServiceImpl(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    @Override
    public UserDTO getUserByAccountId(Integer accountId) {
        Objects.requireNonNull(accountId, "accountId is required");
        RestfulResult<UserDTO> result = sendGetRequest(assembleRequestUrl(UacConstants.UAC_USER_URI),
                new ParameterizedTypeReference<RestfulResult<UserDTO>>() {
                },
                accountId);

        if (result == null || !result.isSuccess()) {
            return null;
        }
        return result.getData();
    }

    @Override
    public MenuNodeDTO getMenu(Integer accountId) {
        Objects.requireNonNull(accountId, "accountId is required");
        RestfulResult<MenuNodeDTO> result = new  RestfulResult<MenuNodeDTO>();
        try{
             result = sendGetRequest(assembleRequestUrl(UacConstants.UAC_MENU_URI), new ParameterizedTypeReference<RestfulResult<MenuNodeDTO>>() {},
                    accountId, platformNo );
        } catch (IllegalStateException ex){
            ex.printStackTrace();
        }

        if (result == null || !result.isSuccess()) {
            return null;
        }
        return result.getData();
    }

    @Override
    public List<PermissionDTO> listPermission(Integer accountId) {
        Objects.requireNonNull(accountId, "accountId is required");

        RestfulResult<List<PermissionDTO>> result = sendGetRequest(assembleRequestUrl(UacConstants.UAC_PERM_URI),
                new ParameterizedTypeReference<RestfulResult<List<PermissionDTO>>>() {
                }, accountId, platformNo);

        if (result == null || !result.isSuccess()) {
            return Collections.emptyList();
        }
        return result.getData();
    }


    @Override
    public boolean validAccount(Integer accountId) {
        Objects.requireNonNull(accountId, "accountId is required");
        RestfulResult<Void> result = sendGetRequest(assembleRequestUrl(UacConstants.UAC_MENU_URI),
                new ParameterizedTypeReference<RestfulResult<Void>>() {
                },
                accountId, platformNo);
        return result != null && result.isSuccess();
    }

    private <T> T sendGetRequest(String url,  ParameterizedTypeReference<T> typeReference, Object... uriVariables){
        return doSendHttpRequest(url,HttpMethod.GET, typeReference,null, uriVariables);
    }

    private <T> T sendPostRequest(String url,  ParameterizedTypeReference<T> typeReference, Object requestBody){
        return doSendHttpRequest(url, HttpMethod.POST, typeReference, requestBody);
    }

    private <T> T doSendHttpRequest(String url,HttpMethod method, ParameterizedTypeReference<T> typeReference, Object requestBody, Object... uriVariables) {
        ResponseEntity<T> responseEntity;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE);
            final HttpEntity<Object> entity;
            if(requestBody != null){
                entity = new HttpEntity<>(requestBody,headers);
            }else {
                 entity = new HttpEntity<>(headers);
            }

            responseEntity = restTemplate.exchange(url, method, entity, typeReference, uriVariables);
        } catch (RestClientException e) {
            LOGGER.error("请求UAC接口时发生错误， url: {}, params: {}", url, uriVariables, e);
            throw new IllegalStateException("请求UAC接口：" + url + "时发生错误");
        } catch (Exception e) {
            LOGGER.error("请求UAC接口时发生错误， url: {}, params: {}", url, uriVariables, e);
            throw new IllegalStateException();
        }

        if (responseEntity == null || !HttpStatus.OK.equals(responseEntity.getStatusCode())) {
            LOGGER.info("请求UAC接口返回错误 - url:{}, params:{}", url, uriVariables);
            return null;
        }

        return responseEntity.getBody();
    }


    private String assembleRequestUrl(String suffix) {
        return uacPrefixUrl + suffix;
    }

    @Override
    public void insertAccountSignOnLog(AccountSignOnLogDto accountSignOnLogDto) {
        RestfulResult<Void> result = sendPostRequest(assembleRequestUrl("/api/uac/accountSignLog/insert"), new ParameterizedTypeReference<RestfulResult<Void>>() {},
                    accountSignOnLogDto);
        if (result == null || !result.isSuccess()) {
            LOGGER.info("请求UAC接口返回错误 - url:{}, params:{}", "/api/uac/accountSignLog/insert", accountSignOnLogDto);
        }
    }

}
