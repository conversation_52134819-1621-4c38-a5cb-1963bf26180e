package com.vedeng.erp.trader.web.controller;


import com.vedeng.common.core.base.R;
import com.vedeng.erp.trader.service.TraderVerifiesCheckApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@RequestMapping("/trader/check")
@Controller
public class TraderVerifiesCheckController {

    @Autowired
    TraderVerifiesCheckApiService traderVerifiesCheckApiService;

    /**
     * 根据客户ID和客户类型查询客户资质
     * @param traderId 客户ID
     * @param traderType 客户类型 1经销商 2供应商
     * @return 客户是否通过资质审核 true 通过 ; false 不通过
     */
    @RequestMapping(value = "/traderVerifies")
    @ResponseBody
    R<Boolean> checkTraderVerifies(Integer traderId, Integer traderType){
        R<Boolean> booleanR = traderVerifiesCheckApiService.checkTraderVerifies(traderId, traderType);
        return booleanR;
    }
}
