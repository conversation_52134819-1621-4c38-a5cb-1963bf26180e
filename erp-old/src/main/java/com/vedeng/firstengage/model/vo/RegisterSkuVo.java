package com.vedeng.firstengage.model.vo;

import com.vedeng.system.model.Attachment;

import java.util.List;

public class RegisterSkuVo {
    private Integer supplierRegistrationNumberId;
    private Integer registrationNumberId;
    private String registrationNumber;
    private Long effectiveDate;
    private Integer checkStatus;
    private Long checkTime;
    private Integer checkUserId;
    private String checkUserName;
    private List<Attachment> attachments;
    private List<Sku> skuList;

    private String skuNoStr;

    private String attachmentMapStr;
    private Integer manageCategoryLevel;

    public Integer getManageCategoryLevel() {
        return manageCategoryLevel;
    }

    public void setManageCategoryLevel(Integer manageCategoryLevel) {
        this.manageCategoryLevel = manageCategoryLevel;
    }

    public String getAttachmentMapStr() {
        return attachmentMapStr;
    }

    public void setAttachmentMapStr(String attachmentMapStr) {
        this.attachmentMapStr = attachmentMapStr;
    }

    public Long getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(Long checkTime) {
        this.checkTime = checkTime;
    }

    public Integer getCheckUserId() {
        return checkUserId;
    }

    public void setCheckUserId(Integer checkUserId) {
        this.checkUserId = checkUserId;
    }

    public String getSkuNoStr() {
        return skuNoStr;
    }

    public void setSkuNoStr(String skuNoStr) {
        this.skuNoStr = skuNoStr;
    }

    public Integer getSupplierRegistrationNumberId() {
        return supplierRegistrationNumberId;
    }

    public void setSupplierRegistrationNumberId(Integer supplierRegisterNumberId) {
        this.supplierRegistrationNumberId = supplierRegisterNumberId;
    }

    public Integer getRegistrationNumberId() {
        return registrationNumberId;
    }

    public void setRegistrationNumberId(Integer registrationNumberId) {
        this.registrationNumberId = registrationNumberId;
    }

    public String getRegistrationNumber() {
        return registrationNumber;
    }

    public void setRegistrationNumber(String registrationNumber) {
        this.registrationNumber = registrationNumber;
    }

    public Long getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(Long effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public Integer getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(Integer checkStatus) {
        this.checkStatus = checkStatus;
    }

    public String getCheckUserName() {
        return checkUserName;
    }

    public void setCheckUserName(String checkUserName) {
        this.checkUserName = checkUserName;
    }

    public List<Attachment> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<Attachment> attachments) {
        this.attachments = attachments;
    }

    public List<Sku> getSkuList() {
        return skuList;
    }

    public void setSkuList(List<Sku> skuList) {
        this.skuList = skuList;
    }

    public static class Sku{
        private String skuNo;
        private String skuName;
        private String model;

        public String getSkuNo() {
            return skuNo;
        }

        public void setSkuNo(String skuNo) {
            this.skuNo = skuNo;
        }

        public String getSkuName() {
            return skuName;
        }

        public void setSkuName(String skuName) {
            this.skuName = skuName;
        }

        public String getModel() {
            return model;
        }

        public void setModel(String model) {
            this.model = model;
        }
    }
}
