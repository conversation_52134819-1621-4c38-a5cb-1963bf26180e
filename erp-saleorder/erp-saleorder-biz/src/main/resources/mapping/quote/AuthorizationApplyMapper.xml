<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.quote.mapper.AuthorizationApplyMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.quote.domain.AuthorizationApplyEntity">
    <!--@mbg.generated-->
    <!--@Table T_AUTHORIZATION_APPLY-->
    <id column="AUTHORIZATION_APPLY_ID" jdbcType="INTEGER" property="authorizationApplyId" />
    <result column="AUTHORIZATION_APPLY_NUM" jdbcType="VARCHAR" property="authorizationApplyNum" />
    <result column="QUOTEORDER_ID" jdbcType="INTEGER" property="quoteorderId" />
    <result column="SKU_ID" jdbcType="INTEGER" property="skuId" />
    <result column="PURCHASE_OR_BIDDING" jdbcType="VARCHAR" property="purchaseOrBidding" />
    <result column="PRODUCT_COMPANY" jdbcType="VARCHAR" property="productCompany" />
    <result column="NATURE_OF_OPERATION" jdbcType="INTEGER" property="natureOfOperation" />
    <result column="BRAND_NAME" jdbcType="VARCHAR" property="brandName" />
    <result column="SKU_NAME" jdbcType="VARCHAR" property="skuName" />
    <result column="SKU_MODEL" jdbcType="VARCHAR" property="skuModel" />
    <result column="DISTRIBUTIONS_TYPE" jdbcType="INTEGER" property="distributionsType" />
    <result column="AUTHORIZED_COMPANY" jdbcType="VARCHAR" property="authorizedCompany" />
    <result column="PURCHASE_PROJECT_NAME" jdbcType="VARCHAR" property="purchaseProjectName" />
    <result column="PURCHASE_PROJECT_NUM" jdbcType="VARCHAR" property="purchaseProjectNum" />
    <result column="FILE_TYPE" jdbcType="INTEGER" property="fileType" />
    <result column="AFTERSALES_COMPANY" jdbcType="VARCHAR" property="aftersalesCompany" />
    <result column="BEGIN_TIME" jdbcType="VARCHAR" property="beginTime" />
    <result column="END_TIME" jdbcType="VARCHAR" property="endTime" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATOR" jdbcType="INTEGER" property="updator" />
    <result column="APPLY_PERSON" jdbcType="VARCHAR" property="applyPerson" />
    <result column="REVIEWER" jdbcType="VARCHAR" property="reviewer" />
    <result column="DESCRIBED" jdbcType="VARCHAR" property="described" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
    <result column="APPLY_STATUS" jdbcType="INTEGER" property="applyStatus" />
    <result column="YEAR_AND_MONTH" jdbcType="VARCHAR" property="yearAndMonth" />
    <result column="STANDARD_TEMPLATE" jdbcType="INTEGER" property="standardTemplate" />
    <result column="COMMENT" jdbcType="VARCHAR" property="comment" />
    <result column="APPLY_YEAR" jdbcType="VARCHAR" property="applyYear" />
    <result column="APPLY_MONTH" jdbcType="VARCHAR" property="applyMonth" />
    <result column="APPLY_DAY" jdbcType="VARCHAR" property="applyDay" />
    <result column="NON_STANDARD_AUTHORIZATION_URL" jdbcType="VARCHAR" property="nonStandardAuthorizationUrl" />
    <result column="NON_STANDARD_AUTHORIZATION_NAME" jdbcType="VARCHAR" property="nonStandardAuthorizationName" />
    <result column="NON_STANDARD_AUTHORIZATION_SIGN_URL" jdbcType="VARCHAR" property="nonStandardAuthorizationSignUrl" />
    <result column="NON_STANDARD_AUTHORIZATION_SIGN_NAME" jdbcType="VARCHAR" property="nonStandardAuthorizationSignName" />
    <result column="WHETHER_SIGN" jdbcType="INTEGER" property="whetherSign" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    AUTHORIZATION_APPLY_ID, AUTHORIZATION_APPLY_NUM, QUOTEORDER_ID, SKU_ID, PURCHASE_OR_BIDDING,
    PRODUCT_COMPANY, NATURE_OF_OPERATION, BRAND_NAME, SKU_NAME, SKU_MODEL, DISTRIBUTIONS_TYPE,
    AUTHORIZED_COMPANY, PURCHASE_PROJECT_NAME, PURCHASE_PROJECT_NUM, FILE_TYPE, AFTERSALES_COMPANY,
    BEGIN_TIME, END_TIME, ADD_TIME, MOD_TIME, CREATOR, UPDATOR, APPLY_PERSON, REVIEWER,
    DESCRIBED, NUM, APPLY_STATUS, YEAR_AND_MONTH, STANDARD_TEMPLATE, `COMMENT`, APPLY_YEAR,
    APPLY_MONTH, APPLY_DAY, NON_STANDARD_AUTHORIZATION_URL, NON_STANDARD_AUTHORIZATION_NAME,
    NON_STANDARD_AUTHORIZATION_SIGN_URL, NON_STANDARD_AUTHORIZATION_SIGN_NAME, WHETHER_SIGN, SEAL_TYPE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_AUTHORIZATION_APPLY
    where AUTHORIZATION_APPLY_ID = #{authorizationApplyId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_AUTHORIZATION_APPLY
    where AUTHORIZATION_APPLY_ID = #{authorizationApplyId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="AUTHORIZATION_APPLY_ID" keyProperty="authorizationApplyId" parameterType="com.vedeng.erp.quote.domain.AuthorizationApplyEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AUTHORIZATION_APPLY (AUTHORIZATION_APPLY_NUM, QUOTEORDER_ID,
      SKU_ID, PURCHASE_OR_BIDDING, PRODUCT_COMPANY,
      NATURE_OF_OPERATION, BRAND_NAME, SKU_NAME,
      SKU_MODEL, DISTRIBUTIONS_TYPE, AUTHORIZED_COMPANY,
      PURCHASE_PROJECT_NAME, PURCHASE_PROJECT_NUM,
      FILE_TYPE, AFTERSALES_COMPANY, BEGIN_TIME,
      END_TIME, ADD_TIME, MOD_TIME,
      CREATOR, UPDATOR, APPLY_PERSON,
      REVIEWER, DESCRIBED, NUM,
      APPLY_STATUS, YEAR_AND_MONTH, STANDARD_TEMPLATE,
      `COMMENT`, APPLY_YEAR, APPLY_MONTH,
      APPLY_DAY, NON_STANDARD_AUTHORIZATION_URL,
      NON_STANDARD_AUTHORIZATION_NAME, NON_STANDARD_AUTHORIZATION_SIGN_URL,
      NON_STANDARD_AUTHORIZATION_SIGN_NAME, WHETHER_SIGN, SEAL_TYPE
      )
    values (#{authorizationApplyNum,jdbcType=VARCHAR}, #{quoteorderId,jdbcType=INTEGER},
      #{skuId,jdbcType=INTEGER}, #{purchaseOrBidding,jdbcType=VARCHAR}, #{productCompany,jdbcType=VARCHAR},
      #{natureOfOperation,jdbcType=INTEGER}, #{brandName,jdbcType=VARCHAR}, #{skuName,jdbcType=VARCHAR},
      #{skuModel,jdbcType=VARCHAR}, #{distributionsType,jdbcType=INTEGER}, #{authorizedCompany,jdbcType=VARCHAR},
      #{purchaseProjectName,jdbcType=VARCHAR}, #{purchaseProjectNum,jdbcType=VARCHAR},
      #{fileType,jdbcType=INTEGER}, #{aftersalesCompany,jdbcType=VARCHAR}, #{beginTime,jdbcType=VARCHAR},
      #{endTime,jdbcType=VARCHAR}, #{addTime,jdbcType=BIGINT}, #{modTime,jdbcType=BIGINT},
      #{creator,jdbcType=INTEGER}, #{updator,jdbcType=INTEGER}, #{applyPerson,jdbcType=VARCHAR},
      #{reviewer,jdbcType=VARCHAR}, #{described,jdbcType=VARCHAR}, #{num,jdbcType=INTEGER},
      #{applyStatus,jdbcType=INTEGER}, #{yearAndMonth,jdbcType=VARCHAR}, #{standardTemplate,jdbcType=INTEGER},
      #{comment,jdbcType=VARCHAR}, #{applyYear,jdbcType=VARCHAR}, #{applyMonth,jdbcType=VARCHAR},
      #{applyDay,jdbcType=VARCHAR}, #{nonStandardAuthorizationUrl,jdbcType=VARCHAR},
      #{nonStandardAuthorizationName,jdbcType=VARCHAR}, #{nonStandardAuthorizationSignUrl,jdbcType=VARCHAR},
      #{nonStandardAuthorizationSignName,jdbcType=VARCHAR}, #{whetherSign,jdbcType=INTEGER}, #{sealType,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="AUTHORIZATION_APPLY_ID" keyProperty="authorizationApplyId" parameterType="com.vedeng.erp.quote.domain.AuthorizationApplyEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AUTHORIZATION_APPLY
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="authorizationApplyNum != null and authorizationApplyNum != ''">
        AUTHORIZATION_APPLY_NUM,
      </if>
      <if test="quoteorderId != null">
        QUOTEORDER_ID,
      </if>
      <if test="skuId != null">
        SKU_ID,
      </if>
      <if test="purchaseOrBidding != null and purchaseOrBidding != ''">
        PURCHASE_OR_BIDDING,
      </if>
      <if test="productCompany != null and productCompany != ''">
        PRODUCT_COMPANY,
      </if>
      <if test="natureOfOperation != null">
        NATURE_OF_OPERATION,
      </if>
      <if test="brandName != null and brandName != ''">
        BRAND_NAME,
      </if>
      <if test="skuName != null and skuName != ''">
        SKU_NAME,
      </if>
      <if test="skuModel != null and skuModel != ''">
        SKU_MODEL,
      </if>
      <if test="distributionsType != null">
        DISTRIBUTIONS_TYPE,
      </if>
      <if test="authorizedCompany != null and authorizedCompany != ''">
        AUTHORIZED_COMPANY,
      </if>
      <if test="purchaseProjectName != null and purchaseProjectName != ''">
        PURCHASE_PROJECT_NAME,
      </if>
      <if test="purchaseProjectNum != null and purchaseProjectNum != ''">
        PURCHASE_PROJECT_NUM,
      </if>
      <if test="fileType != null">
        FILE_TYPE,
      </if>
      <if test="aftersalesCompany != null and aftersalesCompany != ''">
        AFTERSALES_COMPANY,
      </if>
      <if test="beginTime != null and beginTime != ''">
        BEGIN_TIME,
      </if>
      <if test="endTime != null and endTime != ''">
        END_TIME,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updator != null">
        UPDATOR,
      </if>
      <if test="applyPerson != null and applyPerson != ''">
        APPLY_PERSON,
      </if>
      <if test="reviewer != null and reviewer != ''">
        REVIEWER,
      </if>
      <if test="described != null and described != ''">
        DESCRIBED,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="applyStatus != null">
        APPLY_STATUS,
      </if>
      <if test="yearAndMonth != null and yearAndMonth != ''">
        YEAR_AND_MONTH,
      </if>
      <if test="standardTemplate != null">
        STANDARD_TEMPLATE,
      </if>
      <if test="comment != null and comment != ''">
        `COMMENT`,
      </if>
      <if test="applyYear != null and applyYear != ''">
        APPLY_YEAR,
      </if>
      <if test="applyMonth != null and applyMonth != ''">
        APPLY_MONTH,
      </if>
      <if test="applyDay != null and applyDay != ''">
        APPLY_DAY,
      </if>
      <if test="nonStandardAuthorizationUrl != null and nonStandardAuthorizationUrl != ''">
        NON_STANDARD_AUTHORIZATION_URL,
      </if>
      <if test="nonStandardAuthorizationName != null and nonStandardAuthorizationName != ''">
        NON_STANDARD_AUTHORIZATION_NAME,
      </if>
      <if test="nonStandardAuthorizationSignUrl != null and nonStandardAuthorizationSignUrl != ''">
        NON_STANDARD_AUTHORIZATION_SIGN_URL,
      </if>
      <if test="nonStandardAuthorizationSignName != null and nonStandardAuthorizationSignName != ''">
        NON_STANDARD_AUTHORIZATION_SIGN_NAME,
      </if>
      <if test="whetherSign != null">
        WHETHER_SIGN,
      </if>
      <if test="sealType != null">
        SEAL_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="authorizationApplyNum != null and authorizationApplyNum != ''">
        #{authorizationApplyNum,jdbcType=VARCHAR},
      </if>
      <if test="quoteorderId != null">
        #{quoteorderId,jdbcType=INTEGER},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=INTEGER},
      </if>
      <if test="purchaseOrBidding != null and purchaseOrBidding != ''">
        #{purchaseOrBidding,jdbcType=VARCHAR},
      </if>
      <if test="productCompany != null and productCompany != ''">
        #{productCompany,jdbcType=VARCHAR},
      </if>
      <if test="natureOfOperation != null">
        #{natureOfOperation,jdbcType=INTEGER},
      </if>
      <if test="brandName != null and brandName != ''">
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null and skuName != ''">
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="skuModel != null and skuModel != ''">
        #{skuModel,jdbcType=VARCHAR},
      </if>
      <if test="distributionsType != null">
        #{distributionsType,jdbcType=INTEGER},
      </if>
      <if test="authorizedCompany != null and authorizedCompany != ''">
        #{authorizedCompany,jdbcType=VARCHAR},
      </if>
      <if test="purchaseProjectName != null and purchaseProjectName != ''">
        #{purchaseProjectName,jdbcType=VARCHAR},
      </if>
      <if test="purchaseProjectNum != null and purchaseProjectNum != ''">
        #{purchaseProjectNum,jdbcType=VARCHAR},
      </if>
      <if test="fileType != null">
        #{fileType,jdbcType=INTEGER},
      </if>
      <if test="aftersalesCompany != null and aftersalesCompany != ''">
        #{aftersalesCompany,jdbcType=VARCHAR},
      </if>
      <if test="beginTime != null and beginTime != ''">
        #{beginTime,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null and endTime != ''">
        #{endTime,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updator != null">
        #{updator,jdbcType=INTEGER},
      </if>
      <if test="applyPerson != null and applyPerson != ''">
        #{applyPerson,jdbcType=VARCHAR},
      </if>
      <if test="reviewer != null and reviewer != ''">
        #{reviewer,jdbcType=VARCHAR},
      </if>
      <if test="described != null and described != ''">
        #{described,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="applyStatus != null">
        #{applyStatus,jdbcType=INTEGER},
      </if>
      <if test="yearAndMonth != null and yearAndMonth != ''">
        #{yearAndMonth,jdbcType=VARCHAR},
      </if>
      <if test="standardTemplate != null">
        #{standardTemplate,jdbcType=INTEGER},
      </if>
      <if test="comment != null and comment != ''">
        #{comment,jdbcType=VARCHAR},
      </if>
      <if test="applyYear != null and applyYear != ''">
        #{applyYear,jdbcType=VARCHAR},
      </if>
      <if test="applyMonth != null and applyMonth != ''">
        #{applyMonth,jdbcType=VARCHAR},
      </if>
      <if test="applyDay != null and applyDay != ''">
        #{applyDay,jdbcType=VARCHAR},
      </if>
      <if test="nonStandardAuthorizationUrl != null and nonStandardAuthorizationUrl != ''">
        #{nonStandardAuthorizationUrl,jdbcType=VARCHAR},
      </if>
      <if test="nonStandardAuthorizationName != null and nonStandardAuthorizationName != ''">
        #{nonStandardAuthorizationName,jdbcType=VARCHAR},
      </if>
      <if test="nonStandardAuthorizationSignUrl != null and nonStandardAuthorizationSignUrl != ''">
        #{nonStandardAuthorizationSignUrl,jdbcType=VARCHAR},
      </if>
      <if test="nonStandardAuthorizationSignName != null and nonStandardAuthorizationSignName != ''">
        #{nonStandardAuthorizationSignName,jdbcType=VARCHAR},
      </if>
      <if test="whetherSign != null">
        #{whetherSign,jdbcType=INTEGER},
      </if>
      <if test="sealType != null">
        #{sealType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.quote.domain.AuthorizationApplyEntity">
    <!--@mbg.generated-->
    update T_AUTHORIZATION_APPLY
    <set>
      <if test="authorizationApplyNum != null and authorizationApplyNum != ''">
        AUTHORIZATION_APPLY_NUM = #{authorizationApplyNum,jdbcType=VARCHAR},
      </if>
      <if test="quoteorderId != null">
        QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER},
      </if>
      <if test="skuId != null">
        SKU_ID = #{skuId,jdbcType=INTEGER},
      </if>
      <if test="purchaseOrBidding != null and purchaseOrBidding != ''">
        PURCHASE_OR_BIDDING = #{purchaseOrBidding,jdbcType=VARCHAR},
      </if>
      <if test="productCompany != null and productCompany != ''">
        PRODUCT_COMPANY = #{productCompany,jdbcType=VARCHAR},
      </if>
      <if test="natureOfOperation != null">
        NATURE_OF_OPERATION = #{natureOfOperation,jdbcType=INTEGER},
      </if>
      <if test="brandName != null and brandName != ''">
        BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null and skuName != ''">
        SKU_NAME = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="skuModel != null and skuModel != ''">
        SKU_MODEL = #{skuModel,jdbcType=VARCHAR},
      </if>
      <if test="distributionsType != null">
        DISTRIBUTIONS_TYPE = #{distributionsType,jdbcType=INTEGER},
      </if>
      <if test="authorizedCompany != null and authorizedCompany != ''">
        AUTHORIZED_COMPANY = #{authorizedCompany,jdbcType=VARCHAR},
      </if>
      <if test="purchaseProjectName != null and purchaseProjectName != ''">
        PURCHASE_PROJECT_NAME = #{purchaseProjectName,jdbcType=VARCHAR},
      </if>
      <if test="purchaseProjectNum != null and purchaseProjectNum != ''">
        PURCHASE_PROJECT_NUM = #{purchaseProjectNum,jdbcType=VARCHAR},
      </if>
      <if test="fileType != null">
        FILE_TYPE = #{fileType,jdbcType=INTEGER},
      </if>
      <if test="aftersalesCompany != null and aftersalesCompany != ''">
        AFTERSALES_COMPANY = #{aftersalesCompany,jdbcType=VARCHAR},
      </if>
      <if test="beginTime != null and beginTime != ''">
        BEGIN_TIME = #{beginTime,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null and endTime != ''">
        END_TIME = #{endTime,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updator != null">
        UPDATOR = #{updator,jdbcType=INTEGER},
      </if>
      <if test="applyPerson != null and applyPerson != ''">
        APPLY_PERSON = #{applyPerson,jdbcType=VARCHAR},
      </if>
      <if test="reviewer != null and reviewer != ''">
        REVIEWER = #{reviewer,jdbcType=VARCHAR},
      </if>
      <if test="described != null and described != ''">
        DESCRIBED = #{described,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="applyStatus != null">
        APPLY_STATUS = #{applyStatus,jdbcType=INTEGER},
      </if>
      <if test="yearAndMonth != null and yearAndMonth != ''">
        YEAR_AND_MONTH = #{yearAndMonth,jdbcType=VARCHAR},
      </if>
      <if test="standardTemplate != null">
        STANDARD_TEMPLATE = #{standardTemplate,jdbcType=INTEGER},
      </if>
      <if test="comment != null and comment != ''">
        `COMMENT` = #{comment,jdbcType=VARCHAR},
      </if>
      <if test="applyYear != null and applyYear != ''">
        APPLY_YEAR = #{applyYear,jdbcType=VARCHAR},
      </if>
      <if test="applyMonth != null and applyMonth != ''">
        APPLY_MONTH = #{applyMonth,jdbcType=VARCHAR},
      </if>
      <if test="applyDay != null and applyDay != ''">
        APPLY_DAY = #{applyDay,jdbcType=VARCHAR},
      </if>
      <if test="nonStandardAuthorizationUrl != null and nonStandardAuthorizationUrl != ''">
        NON_STANDARD_AUTHORIZATION_URL = #{nonStandardAuthorizationUrl,jdbcType=VARCHAR},
      </if>
      <if test="nonStandardAuthorizationName != null and nonStandardAuthorizationName != ''">
        NON_STANDARD_AUTHORIZATION_NAME = #{nonStandardAuthorizationName,jdbcType=VARCHAR},
      </if>
      <if test="nonStandardAuthorizationSignUrl != null and nonStandardAuthorizationSignUrl != ''">
        NON_STANDARD_AUTHORIZATION_SIGN_URL = #{nonStandardAuthorizationSignUrl,jdbcType=VARCHAR},
      </if>
      <if test="nonStandardAuthorizationSignName != null and nonStandardAuthorizationSignName != ''">
        NON_STANDARD_AUTHORIZATION_SIGN_NAME = #{nonStandardAuthorizationSignName,jdbcType=VARCHAR},
      </if>
      <if test="whetherSign != null">
        WHETHER_SIGN = #{whetherSign,jdbcType=INTEGER},
      </if>
      <if test="sealType != null">
        SEAL_TYPE = #{sealType,jdbcType=INTEGER},
      </if>
    </set>
    where AUTHORIZATION_APPLY_ID = #{authorizationApplyId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.quote.domain.AuthorizationApplyEntity">
    <!--@mbg.generated-->
    update T_AUTHORIZATION_APPLY
    set AUTHORIZATION_APPLY_NUM = #{authorizationApplyNum,jdbcType=VARCHAR},
      QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER},
      SKU_ID = #{skuId,jdbcType=INTEGER},
      PURCHASE_OR_BIDDING = #{purchaseOrBidding,jdbcType=VARCHAR},
      PRODUCT_COMPANY = #{productCompany,jdbcType=VARCHAR},
      NATURE_OF_OPERATION = #{natureOfOperation,jdbcType=INTEGER},
      BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      SKU_NAME = #{skuName,jdbcType=VARCHAR},
      SKU_MODEL = #{skuModel,jdbcType=VARCHAR},
      DISTRIBUTIONS_TYPE = #{distributionsType,jdbcType=INTEGER},
      AUTHORIZED_COMPANY = #{authorizedCompany,jdbcType=VARCHAR},
      PURCHASE_PROJECT_NAME = #{purchaseProjectName,jdbcType=VARCHAR},
      PURCHASE_PROJECT_NUM = #{purchaseProjectNum,jdbcType=VARCHAR},
      FILE_TYPE = #{fileType,jdbcType=INTEGER},
      AFTERSALES_COMPANY = #{aftersalesCompany,jdbcType=VARCHAR},
      BEGIN_TIME = #{beginTime,jdbcType=VARCHAR},
      END_TIME = #{endTime,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATOR = #{updator,jdbcType=INTEGER},
      APPLY_PERSON = #{applyPerson,jdbcType=VARCHAR},
      REVIEWER = #{reviewer,jdbcType=VARCHAR},
      DESCRIBED = #{described,jdbcType=VARCHAR},
      NUM = #{num,jdbcType=INTEGER},
      APPLY_STATUS = #{applyStatus,jdbcType=INTEGER},
      YEAR_AND_MONTH = #{yearAndMonth,jdbcType=VARCHAR},
      STANDARD_TEMPLATE = #{standardTemplate,jdbcType=INTEGER},
      `COMMENT` = #{comment,jdbcType=VARCHAR},
      APPLY_YEAR = #{applyYear,jdbcType=VARCHAR},
      APPLY_MONTH = #{applyMonth,jdbcType=VARCHAR},
      APPLY_DAY = #{applyDay,jdbcType=VARCHAR},
      NON_STANDARD_AUTHORIZATION_URL = #{nonStandardAuthorizationUrl,jdbcType=VARCHAR},
      NON_STANDARD_AUTHORIZATION_NAME = #{nonStandardAuthorizationName,jdbcType=VARCHAR},
      NON_STANDARD_AUTHORIZATION_SIGN_URL = #{nonStandardAuthorizationSignUrl,jdbcType=VARCHAR},
      NON_STANDARD_AUTHORIZATION_SIGN_NAME = #{nonStandardAuthorizationSignName,jdbcType=VARCHAR},
      WHETHER_SIGN = #{whetherSign,jdbcType=INTEGER},
      SEAL_TYPE = #{sealType,jdbcType=INTEGER}
    where AUTHORIZATION_APPLY_ID = #{authorizationApplyId,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2025-05-13-->
  <select id="findByAuthorizationApplyId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_AUTHORIZATION_APPLY
        where AUTHORIZATION_APPLY_ID=#{authorizationApplyId,jdbcType=INTEGER}
    </select>
</mapper>