<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>数据授权</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="modifyUserPers" datasource="erp-datasourcetarget" success_url="reload">
  <div id="APPEND_HEAD" class="append">
      <style>
          .layui-quote-nm{display: none;}
          .scroll-body{
              max-height: 200px !important;
          }
          button.layui-btn-primary{
              display: none;
          }
          .layui-input-inline{
              width:500px !important;
          }
      </style>
  </div>
  <div class="layui-container"> 
   <form id="inputForm" method="post" class="layui-form">

      <div class="layui-form-item ">
          <label class="layui-form-label">姓名</label>
            <div class="layui-input-inline form-group">
            <input    lay_verify="required"
                   type="span"    class="layui-input form-item"
                   value=""  name="USERNAME" item_desc="" maxlength="200">
            </div>
      </div>

       <div class="layui-form-item ">
           <label class="layui-form-label">部门</label>
           <div class="layui-input-inline form-group"><!--xmselectdepart  cascadersql-depart-->
               <object class="form-item" name="DEPART_ID" type="cascadersql-depart" value="" item_name="DEPART_ID" itemName="DEPART_ID" placeholder="" style="" alias="" jdbctype="" data="select
	ORG_ID K,
	ORG_NAME V,
	PARENT_ID P
from
	T_ORGANIZATION
WHERE
	COMPANY_ID = 1
	AND IS_DELETED=0
	AND LEVEL IN (3,4)
" datatype="KVSQL" item_desc="" group_data="" valid_rule="" valid_msg="" item_max_upload=""></object>
           </div>
       </div>

       <div class="layui-form-item ">
           <label class="layui-form-label">已选择</label>
           <div class="layui-input-inline form-group">
               <input    lay_verify=""
                         type="span"    class="layui-input form-item"
                         value=""  name="USERNAME_TEXT" item_desc="" maxlength="200">
           </div>
       </div>

       <div class="layui-form-item ">
           <label class="layui-form-label">USER_ID</label>
           <div class="layui-input-block form-group">
               <input class="form-item" name="USER_ID" type="hidden" value="" item_name="USER_ID" placeholder="" style="display:none" alias="" jdbctype="" data="" datatype="" lay_verify="" item_desc="" group_data="" valid_rule="" valid_msg="" item_max_upload="">
           </div>
       </div>

       <div class="layui-form-item ">
           <label class="layui-form-label">ORG_IDS_LIST</label>
           <div class="layui-input-block form-group">
               <input class="form-item" name="ORG_IDS_LIST" type="hidden" value="" item_name="ORG_IDS_LIST" placeholder="" style="display:none" alias="" jdbctype="" data="" datatype="" lay_verify="" item_desc="" group_data="" valid_rule="" valid_msg="" item_max_upload="">
           </div>
       </div>





   </form> 
   <form id="configForm" class="layui-form"> 
    <div class="layui-card" >
     <div class="layui-card-header">
       表单数据配置 
     </div> 
     <div class="layui-card-body"> 
     </div> 
    </div> 
    <div class="layui-form-item "><label class="layui-form-label">初始表达式</label> 
     <div class="layui-input-block form-group"> 
      <pre class="form-item layui-code" id="init" name="INIT_EXPRESS" type="">list=select("SELECT * FROM T_USER a
WHERE
	a.USER_ID= ${ID}");
return list.get(0);</pre> 
     </div> 
    </div>
    <div class="layui-form-item "> <label class="layui-form-label">提交表达式</label> 
     <div class="layui-input-block form-group"> 
      <pre class="form-item layui-code" id="submit" name="SUBMIT_EXPRESS" type="">
          sessionUserId=$$("EZ_SESSION_USER_ID_KEY");

update("
UPDATE T_USER
SET ORG_IDS_LIST = #{DEPART_ID},
UPDATER = #{EZ_SESSION_USER_ID_KEY},
MOD_TIME = UNIX_TIMESTAMP(NOW())
WHERE
        USER_ID = #{ID}");
return ID;
</pre>
     </div> 
    </div>
    <div class="layui-form-item "> <label class="layui-form-label">删除表达式</label> 
     <div class="layui-input-block form-group"> 
      <pre class="form-item layui-code" id="delete" name="DELETE_EXPRESS" type="">
          <!--if(null!=ID||IS_DELETE ==0){-->
            <!--update("UPDATE T_ASK_QUESTION_MANAGEMENT  SET IS_DELETE = 1, UPDATER = #{EZ_SESSION_USER_ID_KEY}, MOD_TIME = NOW() WHERE ID = #{ID}");-->
          <!--}else{-->
            <!--update(" UPDATE T_ASK_QUESTION_MANAGEMENT SET IS_DELETE = 0, UPDATER = #{EZ_SESSION_USER_ID_KEY}, MOD_TIME = NOW() WHERE ID = #{ID}");-->
          <!--}-->
          <!--update(" UPDATE T_ASK_QUESTION_MANAGEMENT SET IS_DELETE = abs(IS_DELETE-1), VECTOR_STATUS =2 ,UPDATER = #{EZ_SESSION_USER_ID_KEY}, MOD_TIME = NOW() WHERE ID = #{ID}");-->
          return ID;
      </pre>
     </div> 
    </div>

   </form> 
  </div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
  <div id="APPEND_FOOT" class="append"> 
   <script>

       function createParentChildStructure(jsonData) {
           // 解析 JSON 字符串
           const data = JSON.parse(jsonData);

           // 创建一个字典来保存所有节点，按照它们的 K 值索引
           const dataMap = data.reduce((map, node) => {
               map[node.K] = { ...node, child: [] };
                   return map;
               }, {});
               // 最终的父节点数组
               const result = [];

               // 遍历所有节点，构建父子关系
               data.forEach(node => {
                   // 如果 P 值为 "2"，则是顶层节点，添加到结果数组中
                   if (node.P === "2") {
                   result.push(dataMap[node.K]);
               } else if (node.P in dataMap) {
                   // 如果 P 值在 dataMap 中，则找到其父节点，并将当前节点添加为子节点
                   dataMap[node.P].child.push(dataMap[node.K]);
               }
           });

           return result;
       }

       function changeEvent(value, selectedData, elem) {
           // value 是一个数组，包含了选中项的值
           // selectedData 是一个数组，包含了选中项的完整数据对象
           // elem 是当前操作的DOM对象
           console.log('选中的值为：', value);
           console.log('选中的完整数据为：', selectedData);

           // 在这里可以根据选中的值做进一步的处理
           // 比如动态加载下一级的数据，或者更新其他组件的状态
       }


        $(function(){


            layui.use('layCascader', function () {
                var value="K";
                var label="V"
                var children="child";
                var objVisitorId = $("[class='ez-laycascader-2']");
                var json = objVisitorId.attr("itemsjson");

                var prop = {};
                prop.value = value;
                prop.label = label;
                prop.children = children;
                prop.multiple = true;
                var res = createParentChildStructure(json);
                console.log(JSON.stringify(res));
                // var itemsjson = JSON.parse(objVisitorId.attr("itemsjson"));
                var layCascader = layui.layCascader;
                var selectedValue = [];
                if($("#ITEM_ID_ORG_IDS_LIST").val().length >0){
                    //selectedValue = $("#ITEM_ID_ORG_IDS_LIST").val().split(',').map(number => `'${number}'`);
                    selectedValue = $("#ITEM_ID_ORG_IDS_LIST").val().split(',').map(number => `${number}`);

                }

                //console.log("已选择组织架构："+selectedValue);
                var demo = layCascader({
                    elem: $(".ez-laycascader-2"),
                    props: prop,
                    filterable: true,
                    filterMethod: function (node, val) {
                        if (val == node.data[label]) {

                            return true;
                        }
                        if ((node.data[label] + node.data[label]).indexOf(val) != -1) {
                            return true;
                        }
                        //  console.log(node.data.orgName+node.data.orgNames+'##'+(node.data.orgId+'').indexOf(val));
                        return !ezpingyin(val, (node.data[label] + node.data[label]), (node.data[value] + ''));
                    },
                   // changeEvent:changeEvent,

                    clearable: true,
                    placeholder: '请选择',
                    collapseTags: true,
                    value: selectedValue,
                    options: res
                });

                demo.change(function (value, node) {
                    //layer.msg('value:' + value + ',Node:' + JSON.stringify(node.data));
                    if($("#showText")){
                        $("#showText").remove();
                    }
                    var html = '<div class="label-content" id="showText">';
                    var va = "";
                    $(node).each(function (index,object) {
                        //console.log(object);
                        va = va+","+object.data.K;
                        html = html+ '<div class="xm-label-block " style="padding-left:6px;padding-right:6px;border-radius: 3px;background-color: rgb(0, 150, 136); display: inline-block;margin-right: 10px;margin-top:10px;"><span style="color: white;">'+object.data.V+'</span></div>';
                    })
                    html = html+'<small style="padding-top: 10px">共选择了'+node.length+'个部门</small>';
                    html = html+"</div>";
                    $("input[name='DEPART_ID']").val(va.length > 0?va.substring(1,va.length):"");
                    $("input[name='USERNAME_TEXT']").parent().append(html);
                });
            });


             var objVisitorId = $("[class='ez-laycascader-2']");
             var itemsjson = JSON.parse(objVisitorId.attr("itemsjson"));
             var initArray =[];
             if($("#ITEM_ID_ORG_IDS_LIST").val()!=undefined && $("#ITEM_ID_ORG_IDS_LIST").val().length>0){
                 initArray =  $("#ITEM_ID_ORG_IDS_LIST").val().split(",") ;
             }
             if(initArray.length >0){
                 var html = '<div class="label-content" id="showText">';
                 itemsjson.forEach(object => {
                     //console.log(object.K);
                     if(initArray.includes(object.K)){
                         console.log(object);
                         html = html+ '<div class="xm-label-block " style="padding-left:6px;padding-right:6px;border-radius: 3px;background-color: rgb(0, 150, 136); display: inline-block;margin-right: 10px;margin-top:10px;"><span style="color: white;">'+object.V+'</span></div>';
                     }
                 });
                 html = html+'<small style="padding-top: 10px">共选择了'+initArray.length+'个部门</small>';
                 html = html+"</div>";
                 $("input[name='USERNAME_TEXT']").parent().append(html);
             }else{
                 var html = '<div class="label-content" id="showText">';
                 html = html+'<small style="padding-top: 10px">当前未选择部门</small>';
                 html = html+"</div>";
                 $("input[name='USERNAME_TEXT']").parent().append(html);
             }




            //  var demo1 = xmSelect.render({
            //     el: objVisitorId[0],
            //     language: 'zn',
            //     filterable: true,
            //     filterMethod: function (val, item, index, prop) {//重写搜索方法。
            //         if(val == item.K){//把value相同的搜索出来
            //             return true;
            //         }
            //         if(item.V.indexOf(val) != -1){//名称中包含的搜索出来
            //             return true;
            //         }
            //         return !ezpingyin(val, item.V, item.K);
            //     },
            //     style: {
            //         height: '26px'  ,
            //     },
            //     prop: {
            //         name: 'V',
            //         value: 'K',
            //     },
            //     name: 'DEPART_ID',
            //     tips: "请选择",
            //     data:  itemsjson,
            //     initValue:  JSON.parse("["+$("#ITEM_ID_ORG_IDS_LIST").val()+"]")
            // });
            // demo1.update({
            //     on:function (data) {
            //         if($("#showText")){
            //             $("#showText").remove();
            //         }
            //         var html = '<div class="label-content" id="showText">';
            //         $(data.arr).each(function (index,object) {
            //             console.log(object);
            //             html = html+ '<div class="xm-label-block " style="padding-left:6px;padding-right:6px;border-radius: 3px;background-color: rgb(0, 150, 136); display: inline-block;margin-right: 10px;margin-top:10px;"><span style="color: white;">'+object.V+'</span></div>';
            //         })
            //         html = html+'<small style="padding-top: 10px">共选择了'+data.arr.length+'个部门</small>';
            //         html = html+"</div>";
            //         $("input[name='USERNAME_TEXT']").parent().append(html);
            //         //console.log(data.arr);
            //     }
            // })
            // $("#submitbtn").click(function () {
            //     var question = $("[name='QUESTION']").val();
            //     var answer = $("[name='ANSWER']").val();
            //     if($.trim(question) == ''){
            //         var layer = layui.layer; // 弹层模块
            //         // 当需要弹出提示信息时
            //         layer.msg('请输入提问内容');
            //         return false;
            //     }
            //     if($.trim(answer)  == ''){
            //         var layer = layui.layer; // 弹层模块
            //         // 当需要弹出提示信息时
            //         layer.msg('请输入答案');
            //         return false;
            //     }
            // })
            
        });
    </script> 
  </div> 
 </body>
</html>