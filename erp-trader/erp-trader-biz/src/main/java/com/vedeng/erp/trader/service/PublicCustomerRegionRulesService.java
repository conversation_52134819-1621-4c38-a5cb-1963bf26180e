package com.vedeng.erp.trader.service;

import com.vedeng.authorization.model.User;
import com.vedeng.common.page.Page;
import com.vedeng.erp.trader.domain.PublicCustomerRegionRules;
import com.vedeng.erp.trader.domain.dto.RegionRulesQueryDto;
import com.vedeng.erp.trader.domain.vo.PublicCustomerRegionRulesVo;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;

public interface PublicCustomerRegionRulesService {

    /**
     * 删除区域规则
     * @param publicCustomerRegionRulesId 主键
     */
    void deleteRegionRule(Integer publicCustomerRegionRulesId);


    /**
     * 批量删除
     * @param idList 主键集合
     */
    void batchDeleteRegionRule(Integer[] idList);

    /**
     * 更新，删除原有，新增新的
     * @param publicCustomerRegionRulesVo 这里的主键封装老数据id userid regionId 都是修改后的数据
     * @param userId 操作人id 避免old包下的类侵入
     */
    void updateInfo(PublicCustomerRegionRulesVo publicCustomerRegionRulesVo,Integer userId);

    /**
     * 批量添加
     * @param list 信息
     */
    void batchAddPublicCustomerCalculateRules(List<PublicCustomerRegionRules> list);


}
