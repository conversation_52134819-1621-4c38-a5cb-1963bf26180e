package com.vedeng.erp.kingdee.service.impl;

import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeeInventoryProfitCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeInventoryProfitEntity;
import com.vedeng.erp.kingdee.dto.KingDeeInventoryProfitDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeInventoryProfitCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeInventoryProfitConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeInventoryProfitRepository;
import com.vedeng.erp.kingdee.service.KingDeeInventoryProfitApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/18 14:09
 **/
@Service
@Slf4j
public class KingDeeInventoryProfitServiceImpl extends KingDeeBaseServiceImpl<
        KingDeeInventoryProfitEntity,
        KingDeeInventoryProfitDto,
        KingDeeInventoryProfitCommand,
        KingDeeInventoryProfitRepository,
        KingDeeInventoryProfitConvertor,
        KingDeeInventoryProfitCommandConvertor>  implements KingDeeInventoryProfitApiService {
}
