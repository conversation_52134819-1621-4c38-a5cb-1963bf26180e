<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.docSync.dao.DocSyncMapper">

    <insert id="saveActionLog" keyColumn="ID" keyProperty="id" useGeneratedKeys="true">
        insert into T_DOC_BUZ_TAG_MAINTAIN_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="buzType != null">
                BUZ_TYPE,
            </if>
            <if test="buzId != null">
                BUZ_ID,
            </if>
            <if test="buzTag != null">
                BUZ_TAG,
            </if>
            <if test="operateType != null">
                OPERATE_TYPE,
            </if>
            <if test="buzTagJFileId != null">
                BUZ_TAG_J_FILE_ID,
            </if>
            <if test="operator != null">
                `OPERATOR`,
            </if>
            <if test="operateTime != null">
                OPERATE_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="buzType != null">
                #{buzType,jdbcType=INTEGER},
            </if>
            <if test="buzId != null">
                #{buzId,jdbcType=INTEGER},
            </if>
            <if test="buzTag != null">
                #{buzTag,jdbcType=INTEGER},
            </if>
            <if test="operateType != null">
                #{operateType,jdbcType=INTEGER},
            </if>
            <if test="buzTagJFileId != null">
                #{buzTagJFileId,jdbcType=INTEGER},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=INTEGER},
            </if>
            <if test="operateTime != null">
                #{operateTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <insert id="batchSaveActionLog">
        INSERT INTO `T_DOC_BUZ_TAG_MAINTAIN_LOG`(`BUZ_TYPE`, `BUZ_ID`, `BUZ_TAG`, `OPERATE_TYPE`, `BUZ_TAG_J_FILE_ID`, `OPERATOR`, `OPERATE_TIME`) VALUES
        <foreach collection="list" item="docRBuzTagJFileExtDo" separator=",">
            (#{docRBuzTagJFileExtDo.buzType,jdbcType=INTEGER},
            #{docRBuzTagJFileExtDo.buzId,jdbcType=INTEGER},
            #{docRBuzTagJFileExtDo.buzTagId,jdbcType=INTEGER},
            #{docRBuzTagJFileExtDo.operateType,jdbcType=INTEGER},
            #{docRBuzTagJFileExtDo.id,jdbcType=INTEGER},
            #{docRBuzTagJFileExtDo.operator,jdbcType=INTEGER},
            #{docRBuzTagJFileExtDo.operateTime,jdbcType=BIGINT})
        </foreach>
    </insert>
    <insert id="saveDocFile" keyColumn="FILE_ID" keyProperty="fileId" useGeneratedKeys="true">
        insert into T_DOC_FILE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="domain != null">
                `DOMAIN`,
            </if>
            <if test="uri != null">
                URI,
            </if>
            <if test="suffix != null">
                SUFFIX,
            </if>
            <if test="md5 != null">
                MD5,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="isDelete != null">
                IS_DELETE,
            </if>
            <if test="originLink != null">
                ORIGIN_LINK,
            </if>
            <if test="syncTime != null">
                SYNC_TIME,
            </if>
            <if test="ossLinkMobile != null">
                OSS_LINK_MOBILE,
            </if>
            <if test="syncOssMobile != null">
                SYNC_OSS_MOBILE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="domain != null">
                #{domain,jdbcType=VARCHAR},
            </if>
            <if test="uri != null">
                #{uri,jdbcType=VARCHAR},
            </if>
            <if test="suffix != null">
                #{suffix,jdbcType=VARCHAR},
            </if>
            <if test="md5 != null">
                #{md5,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="originLink != null">
                #{originLink,jdbcType=VARCHAR},
            </if>
            <if test="syncTime != null">
                #{syncTime,jdbcType=BIGINT},
            </if>
            <if test="ossLinkMobile != null">
                #{ossLinkMobile,jdbcType=VARCHAR},
            </if>
            <if test="syncOssMobile != null">
                #{syncOssMobile,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <insert id="saveBuzTagJFile" keyColumn="ID" keyProperty="id" useGeneratedKeys="true">
        insert into T_DOC_R_BUZ_TAG_J_FILE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileId != null">
                FILE_ID,
            </if>
            <if test="fileName != null">
                FILE_NAME,
            </if>
            <if test="buzType != null">
                BUZ_TYPE,
            </if>
            <if test="buzId != null">
                BUZ_ID,
            </if>
            <if test="buzTagId != null">
                BUZ_TAG_ID,
            </if>
            <if test="validStartTime != null">
                VALID_START_TIME,
            </if>
            <if test="validEndTime != null">
                VALID_END_TIME,
            </if>
            <if test="hasStamp != null">
                HAS_STAMP,
            </if>
            <if test="externalUrl != null">
                EXTERNAL_URL,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="isDelete != null">
                IS_DELETE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileId != null">
                #{fileId,jdbcType=INTEGER},
            </if>
            <if test="fileName != null">
                #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="buzType != null">
                #{buzType,jdbcType=INTEGER},
            </if>
            <if test="buzId != null">
                #{buzId,jdbcType=INTEGER},
            </if>
            <if test="buzTagId != null">
                #{buzTagId,jdbcType=INTEGER},
            </if>
            <if test="validStartTime != null">
                #{validStartTime,jdbcType=BIGINT},
            </if>
            <if test="validEndTime != null">
                #{validEndTime,jdbcType=BIGINT},
            </if>
            <if test="hasStamp != null">
                #{hasStamp,jdbcType=TINYINT},
            </if>
            <if test="externalUrl != null">
                #{externalUrl,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>

    <update id="batchRemoveOldBuzTagFile">
        UPDATE
        T_DOC_R_BUZ_TAG_J_FILE
        SET IS_DELETE = 1
        WHERE
        ID IN
        <foreach collection="list" item="id" separator="," open="(" close=")">
            #{id,jdbcType = INTEGER}
        </foreach>
    </update>

    <update id="updateDocFileOriginLink" parameterType="com.vedeng.docSync.model.pojo.DocFileExtDo">
        UPDATE T_DOC_FILE SET
          Sync_Time = now(),
          Origin_Link = #{originLink,jdbcType=VARCHAR}
        WHERE FILE_ID = #{fileId,jdbcType=INTEGER}
    </update>
    <update id="updateDocFile" parameterType="com.vedeng.docSync.model.pojo.DocFileExtDo">
        UPDATE T_DOC_FILE SET
                              DOMAIN = #{domain,jdbcType=VARCHAR},
                              URI = #{uri,jdbcType=VARCHAR},
                              SUFFIX = #{suffix,jdbcType=VARCHAR},
                              MD5 = #{md5,jdbcType=VARCHAR},
                              FILE_NAME = #{fileName,jdbcType=VARCHAR}
        WHERE FILE_ID = #{fileId,jdbcType=INTEGER}
    </update>
    <update id="updateDocGoodsTitle">
        update T_DOC_OF_GOODS
        set
            DOC_TITLE = #{docOfGoodsExtDo.docTitle,jdbcType=VARCHAR}
        where ID = #{docOfGoodsExtDo.id,jdbcType=INTEGER}
    </update>

    <select id="getDocOfGoodsBySpuIdList" resultType="com.vedeng.docSync.model.pojo.DocOfGoodsExtDo">
        SELECT *
        FROM T_DOC_OF_GOODS
        WHERE SPU_ID in
        <foreach collection="spuIdList" item="spuId" separator="," open="(" close=")">
            #{spuId,jdbcType=INTEGER}
        </foreach>

    </select>

    <select id="listBuzTagFileForGoods" resultType="com.vedeng.docSync.model.pojo.DocRBuzTagJFileExtDo">
      SELECT DRBT.*
      FROM T_DOC_OF_GOODS DOG
      LEFT JOIN T_DOC_R_BUZ_TAG_J_FILE DRBT ON DRBT.BUZ_TYPE = 1 AND DRBT.BUZ_ID = DOG.ID AND DRBT.IS_DELETE = 0
      LEFT JOIN T_DOC_BUZ_TAG DBT ON DRBT.BUZ_TAG_ID = DBT.ID AND DBT.IS_DELETE = 0 AND DBT.BUZ_TYPE=1
      LEFT JOIN T_DOC_FILE DF ON DRBT.FILE_ID = DF.FILE_ID AND DF.IS_DELETE=0
      WHERE DOG.ID = #{docOfGoodsExtDoId,jdbcType=INTEGER}
      AND DBT.ID = #{docBuzTagExtDoId,jdbcType=INTEGER}
      AND DOG.IS_DELETE = 0
    </select>

    <select id="listBuzTagFileForSupplier" resultType="com.vedeng.docSync.model.pojo.DocRBuzTagJFileExtDo">
      SELECT DRBT.*
      FROM T_DOC_SUPPLIER DS
      LEFT JOIN T_DOC_R_BUZ_TAG_J_FILE DRBT ON DRBT.BUZ_TYPE = 2 AND DRBT.BUZ_ID = DS.ID AND DRBT.IS_DELETE = 0
      LEFT JOIN T_DOC_BUZ_TAG DBT ON DRBT.BUZ_TAG_ID = DBT.ID  AND DBT.BUZ_TYPE=2 AND DBT.IS_DELETE = 0
      LEFT JOIN T_DOC_FILE DF ON DRBT.FILE_ID = DF.FILE_ID AND DF.IS_DELETE=0
      WHERE DS.ID = #{supplierExtDoId,jdbcType=INTEGER}
      AND DBT.ID = #{docBuzTagExtDoId,jdbcType=INTEGER}
      AND DS.IS_DELETE=0
    </select>

    <resultMap id="DocSupplierExtResultMap" type="com.vedeng.docSync.model.pojo.DocSupplierExtDo">
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="SUPPLIER_ID" jdbcType="INTEGER" property="supplierId"/>
        <result column="SUPPLIER_NAME" jdbcType="VARCHAR" property="supplierName"/>
        <result column="ADD_TIME" jdbcType="BIGINT" property="addTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="MOD_TIME" jdbcType="BIGINT" property="modTime"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="DISABLE" jdbcType="TINYINT" property="disable"/>
        <result column="IS_DELETE" jdbcType="TINYINT" property="isDelete"/>
    </resultMap>

    <select id="getDocSupplierByTraderSupplier" resultMap="DocSupplierExtResultMap">
        SELECT *
        FROM T_DOC_SUPPLIER
        WHERE SUPPLIER_ID  = #{traderId,jdbcType=INTEGER}
        AND IS_DELETE = 0
    </select>

    <select id="getDocFileList" resultType="com.vedeng.docSync.model.pojo.DocFileExtDo">
        SELECT * FROM T_DOC_FILE WHERE IS_DELETE = 0 AND URI LIKE '%resourceId%'
    </select>
    <select id="listDocOfGoods" resultType="com.vedeng.docSync.model.pojo.DocOfGoodsExtDo">
        select
            *
        from T_DOC_OF_GOODS a
        where
            a.IS_DELETE = 0
            and a.DISABLE = 0
    </select>


    <update id="updateBCIdBySpuInfo">
        update T_DOC_OF_GOODS set THIRD_CATEGORY = #{spuInfoById.categoryId,jdbcType=INTEGER},
                                  BRAND_ID = #{spuInfoById.brandId,jdbcType=INTEGER},
                                  GOODS_NAME = #{spuInfoById.spuName,jdbcType=VARCHAR}
        where SPU_ID = #{spuInfoById.spuId,jdbcType=INTEGER} and SKU_ID is null
    </update>
    <update id="updateBCIdBySkuInfo">
        update T_DOC_OF_GOODS set THIRD_CATEGORY = #{skuDto.categoryId,jdbcType=INTEGER},
                                  BRAND_ID = #{skuDto.brandId,jdbcType=INTEGER},
                                  GOODS_NAME = #{skuDto.skuName,jdbcType=VARCHAR},
                                  SPU_ID =  #{skuDto.spuId,jdbcType=INTEGER}
        where SKU_ID = #{skuDto.skuId,jdbcType=INTEGER}
    </update>
    <select id="selectNotSyncSku" resultType="com.vedeng.docSync.model.TempSyncSku">
        SELECT
            A.SKU_ID,
            A.SPU_ID,
            B.FIRST_ENGAGE_ID,
            B.REGISTRATION_NUMBER_ID
        FROM
            (
                SELECT
                    R.ID AS RID,
                    DG.ID AS DGID,
                    SKU_ID,
                    SPU_ID,
                    R.VALID_START_TIME,
                    R.VALID_END_TIME
                FROM
                    T_DOC_OF_GOODS DG
                        LEFT JOIN T_DOC_R_BUZ_TAG_J_FILE R ON R.BUZ_ID = DG.ID
                        AND R.BUZ_TYPE = 1
                        AND R.BUZ_TAG_ID = 3
                WHERE
                    DG.SPU_ID IS NOT NULL
                  AND DG.SKU_ID IS NOT NULL
                  AND DG.IS_DELETE = 0
                  AND R.IS_DELETE = 0
                GROUP BY
                    DG.SKU_ID
            ) A
                LEFT JOIN (
                SELECT
                    FE.FIRST_ENGAGE_ID,
                    SKU.SPU_ID,
                    SKU.SKU_ID,
                    RN.REGISTRATION_NUMBER_ID,
                    RN.ISSUING_DATE,
                    RN.EFFECTIVE_DATE
                FROM
                    V_CORE_SKU SKU
                        INNER JOIN V_CORE_SPU SPU ON SKU.SPU_ID = SPU.SPU_ID
                        LEFT JOIN T_FIRST_ENGAGE FE ON SPU.FIRST_ENGAGE_ID = FE.FIRST_ENGAGE_ID
                        LEFT JOIN T_REGISTRATION_NUMBER RN ON RN.REGISTRATION_NUMBER_ID = FE.REGISTRATION_NUMBER_ID
                WHERE
                    FE.`STATUS` = 3
                  AND SKU.STATUS = 1
                  AND SKU.CHECK_STATUS = 3
            ) B ON A.SKU_ID = B.SKU_ID
         WHERE
            B.SKU_ID IS NOT NULL
          AND ( B.EFFECTIVE_DATE  <![CDATA[ != ]]> A.VALID_END_TIME OR A.VALID_START_TIME <![CDATA[ != ]]> B.ISSUING_DATE );

    </select>


</mapper>