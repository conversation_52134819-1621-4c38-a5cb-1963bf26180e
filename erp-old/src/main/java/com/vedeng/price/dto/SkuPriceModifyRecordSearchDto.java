package com.vedeng.price.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2021/8/19 10:01
 */
@Data
public class SkuPriceModifyRecordSearchDto {

    private String skuNoSearchString;

    private String skuNameSearchString;

    private String brandNameSearchString;

    private String holderNameSearchString;

    private int skuPriceModifyType;

    private String startTimeStr;
    private String endTimeStr;

    private List<Integer> isPriceChange;
    private int isMarketPriceChange;
    private int isTerminalPriceChange;
    private int isDistributionPriceChange;
    private int isPurchaseCostsChange;
    private int isGroupPriceChange;

    private Integer currentUserId;

    private Integer isHaveAffectOrder;
}
