<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.market.dao.MarketPlanExtendMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.market.domain.entity.MarketPlan">
    <!--@mbg.generated-->
    <!--@Table T_MARKETING_PLAN-->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="PLAN_NAME" jdbcType="VARCHAR" property="planName" />
    <result column="PLAN_CREATE_TIME" jdbcType="TIMESTAMP" property="planCreateTime" />
    <result column="PLAN_START_TIME" jdbcType="TIMESTAMP" property="planStartTime" />
    <result column="PLAN_END_TIME" jdbcType="TIMESTAMP" property="planEndTime" />
    <result column="CONTENT_TYPE" jdbcType="INTEGER" property="contentType" />
    <result column="PROMOTION_CHANNELS" jdbcType="INTEGER" property="promotionChannels" />
    <result column="POSTER_URL" jdbcType="LONGVARCHAR" property="posterUrl" />
    <result column="MARKETING_SCRIPT_LIST_JSON" jdbcType="LONGVARCHAR" property="marketingScriptListJson" />
    <result column="PLAN_STATUS" jdbcType="INTEGER" property="planStatus" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="CREATOR_ID" jdbcType="INTEGER" property="creatorId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="UPDATOR" jdbcType="VARCHAR" property="updator" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="SYSTEM_ADD_TIME" jdbcType="TIMESTAMP" property="systemAddTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, PLAN_NAME, PLAN_CREATE_TIME, PLAN_START_TIME, PLAN_END_TIME, CONTENT_TYPE, PROMOTION_CHANNELS, 
    POSTER_URL, MARKETING_SCRIPT_LIST_JSON, PLAN_STATUS, CREATOR_NAME, CREATOR_ID, ADD_TIME, 
    UPDATOR, MOD_TIME, SYSTEM_ADD_TIME
  </sql>

  <select id="queryMarketPlanTongqi" resultMap="BaseResultMap">
    select
    tmpt.*
    from T_MARKETING_PLAN tmpt
    JOIN T_MARKETING_PLAN_TRADER   pt ON tmpt.ID=pt.PLAN_ID
    JOIN  T_R_TRADER_J_USER tu     ON pt.TRADER_ID=tu.TRADER_ID
    where
    tmpt.ID !=#{planId}
    and pt.TRADER_ID=#{traderId}
    AND pt.TRADER_CUSTOMER_ID=#{traderCustomerId}
    AND tmpt.ID IN(
      SELECT
          market.ID
      FROM T_MARKETING_PLAN market
            JOIN T_MARKETING_PLAN_TRADER   pt ON market.ID=pt.PLAN_ID
            JOIN  T_R_TRADER_J_USER tu     ON pt.TRADER_ID=tu.TRADER_ID
      where  tu.USER_ID= #{userId}
        and pt.TRADER_ID=#{traderId} AND pt.TRADER_CUSTOMER_ID=#{traderCustomerId}
        and  market.PLAN_STATUS =1
        AND PLAN_START_TIME <![CDATA[ <= ]]> now()
        and PLAN_END_TIME <![CDATA[ >= ]]> NOW()
    )
  </select>

</mapper>