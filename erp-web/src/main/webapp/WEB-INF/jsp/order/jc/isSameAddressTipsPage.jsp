<%--
  Created by IntelliJ IDEA.
  User: vedeng
  Date: 2021/6/21
  Time: 13:18
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ include file="../../common/common.jsp"%>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<html>
<head>
    <title>Title</title>
</head>
<body style="padding: 20px">


<c:choose>
    <c:when test="${validStatus == 1}">
        <div style="text-align: center"><b>此订单修改将不满足原订单票货同行条件！</b></div>
    </c:when>
    <c:otherwise>
        <div style="text-align: center"><b>此订单暂不满足票货同行，请重新编辑后提交！</b></div>
    </c:otherwise>
</c:choose>

<c:set var="reasonNoStr" value="${reasonNo}"/>
<br>
<div style="color: darkgray">不满足"票货同行"原因如下：</div>
<br>
<div style="color:<c:choose><c:when test="${fn:contains(reasonNoStr,'1')}">red</c:when><c:otherwise>darkgray</c:otherwise></c:choose>">
    1、发票是否寄送：寄送；
    <c:if test="${fn:contains(reasonNoStr,'1')}">
        <font color="red">（不满足）</font>
    </c:if>
</div>
<div style="color:<c:choose><c:when test="${fn:contains(reasonNoStr,'2')}">red</c:when><c:otherwise>darkgray</c:otherwise></c:choose>">
    2、开票方式：自动电子发票/自动数电发票；
    <c:if test="${fn:contains(reasonNoStr,'2')}">
        <font color="red">（不满足）</font>
    </c:if>
</div>
<div style="color:<c:choose><c:when test="${fn:contains(reasonNoStr,'3')}">red</c:when><c:otherwise>darkgray</c:otherwise></c:choose>">
    3、订单中全部商品的发货方式为“普发”；
    <c:if test="${fn:contains(reasonNoStr,'3')}">
        <font color="red">（不满足）</font>
    </c:if>
</div>
<div style="color:<c:choose><c:when test="${fn:contains(reasonNoStr,'4')}">red</c:when><c:otherwise>darkgray</c:otherwise></c:choose>">
    4、订单无退票、退货售后单；
    <c:if test="${fn:contains(reasonNoStr,'4')}">
        <font color="red">（不满足）</font>
    </c:if>
</div>
<div style="color:<c:choose><c:when test="${fn:contains(reasonNoStr,'5')}">red</c:when><c:otherwise>darkgray</c:otherwise></c:choose>">
    5、 “货票客户”相同，且“货票地址”相同；
    <c:if test="${fn:contains(reasonNoStr,'5')}">
        <font color="red">（不满足）</font>
    </c:if>
</div>
<div style="color:<c:choose><c:when test="${fn:contains(reasonNoStr,'6')}">red</c:when><c:otherwise>darkgray</c:otherwise></c:choose>">
    6、 “票货是否同行”：票货同行；
    <c:if test="${fn:contains(reasonNoStr,'6')}">
        <font color="red">（不满足）</font>
    </c:if>
</div>
<br>

<c:if test="${validStatus == 1}">
    <div style="color: red">
     （Tips：）改订单为票货同行订单，当前数据修改操作影响订单票货同行状态
        <br>
        点击“确定”按钮继续操作，点击“取消”按钮取消操作。
    </div>
</c:if>

<c:choose>
    <c:when test="${validStatus == 1}">

        <div style="text-align: center">
            <button style="background: #1E9FFF" type="submit" class="layui-btn" onclick="submitParent()">确定</button>
            <button type="reset" class="layui-btn layui-btn-primary" onclick="closeTipsWindow()">取消</button>
        </div>
    </c:when>
    <c:otherwise>
        <div style="text-align: center"><input style="width: 100px" type="button" value="确定" onclick="closeTipsWindow()"></div>
    </c:otherwise>
</c:choose>

</body>

<script type="text/javascript">
    function closeTipsWindow() {
        var index=parent.layer.getFrameIndex(window.name); //获取当前窗口的name
        parent.layer.close(index);
    }

    function submitParent() {
        var index=parent.layer.getFrameIndex(window.name); //获取当前窗口的name
        parent.layer.close(index);
        $('#editForm', window.parent.document).submit();
    }
</script>
</html>
