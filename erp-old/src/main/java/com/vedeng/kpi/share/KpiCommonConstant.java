package com.vedeng.kpi.share;

public class KpiCommonConstant {


    /**
     * 计入绩效的订单毛利率
     */
    public static final double KPI_ROSS_MARGIN = 0.05;

    /**
     * 计入kpi绩效的订单累计付款比例
     */
    public static final double KPI_AMOUNT_COUNT_PROPORTION = 0.95;


    /**
     * 五行类别名称:业绩
     */
    public static final String KPI_CONFIG_WEIGHT_NAME_YJ = "五行 - 业绩";

    /**
     * 五行类别名称:客户
     */
    public static final String KPI_CONFIG_WEIGHT_NAME_KH = "五行 - 客户";

    /**
     * 五行类别名称:品牌
     */
    public static final String KPI_CONFIG_WEIGHT_NAME_PP = "五行 - 品牌";

    /**
     * 五行类别名称:通话
     */
    public static final String KPI_CONFIG_WEIGHT_NAME_TH = "五行 - 通话";

    /**
     * 五行类别名称:BD新客数
     */
    public static final String KPI_CONFIG_WEIGHT_NAME_BDKH = "五行 - BD客户数";

    /**
     * 五行类别名称:询价转化率
     */
    public static final String KPI_CONFIG_WEIGHT_NAME_XJ = "五行 - 询价转化率";

    /**
     * 客户合作取值天数 90
     */
    public static final Integer KPI_CUSTOMERS_DAYS_90 = 90;

    /**
     * 客户合作取值天数 180
     */
    public static final Integer KPI_CUSTOMERS_DAYS_180 = 180;

}
