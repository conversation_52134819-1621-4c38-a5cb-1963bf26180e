package com.newtask.finance;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.system.SystemUtil;
import com.itextpdf.text.pdf.BaseFont;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.finance.domain.entity.AcceptanceBillEntity;
import com.vedeng.erp.finance.dto.BankBillDto;
import com.vedeng.erp.finance.dto.CapitalBillDto;
import com.vedeng.erp.finance.service.AcceptanceBillService;
import com.vedeng.erp.settlement.domain.entity.BankBillEntity;
import com.vedeng.erp.settlement.service.BankBillService;
import com.vedeng.erp.settlement.service.CapitalBillService;
import com.vedeng.infrastructure.bank.api.domain.B2eNbsQryDraftTransStatusReqBody;
import com.vedeng.infrastructure.bank.api.domain.B2eNbsQryDraftTransStatusResponse;
import com.vedeng.infrastructure.bank.api.domain.DraftDetailReqBody;
import com.vedeng.infrastructure.bank.api.domain.DraftDetailResponse;
import com.vedeng.infrastructure.bank.api.service.AcceptanceBillApiService;
import com.vedeng.infrastructure.oss.service.OssUtilsService;
import com.vedeng.infrastructure.wxrobot.constants.WxRobotMsgTemple;
import com.vedeng.infrastructure.wxrobot.dto.WxMsgDto;
import com.vedeng.infrastructure.wxrobot.service.WxRobotService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.xhtmlrenderer.pdf.ITextFontResolver;
import org.xhtmlrenderer.pdf.ITextRenderer;

import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.PURE_DATE_PATTERN;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 创建承兑汇票
 * @date 2024/9/9 13:20
 */
@JobHandler(value = "AcceptanceBillCreateTask")
@Component
@Slf4j
public class AcceptanceBillCreateTask extends AbstractJobHandler {

    @Autowired
    private AcceptanceBillService acceptanceBillService;
    @Autowired
    private AcceptanceBillApiService acceptanceBillApiService;
    @Autowired
    private CapitalBillService capitalBillService;
    @Autowired
    private BankBillService bankBillService;
    @Autowired
    private OssUtilsService ossUtilsService;
    @Autowired
    private WxRobotService wxRobotService;


    @Value("${pay.bank.day}")
    private int PAY_BANK_DAY;
    @Value("${custAccount}")
    private String custAccount;

    @Value("${acceptance_bill_notice_robot_num}")
    public String acceptanceBillNoticeRobotNum;

    public final static String TEMPLATE_PATH = "template/acceptance_bill_template.html";


    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        // 获取银行承兑汇票数据
        B2eNbsQryDraftTransStatusReqBody body = this.createB2eNbsQryDraftTransStatusReqBody();
        B2eNbsQryDraftTransStatusResponse draftTransStatusResponse = acceptanceBillApiService.b2eNbsQryDraftTransStatus(body,false);

        if (draftTransStatusResponse.isSuccess()) {
            if (CollUtil.isNotEmpty(draftTransStatusResponse.getXDataBody().getList())) {
                this.processDraftHoldingBills(draftTransStatusResponse.getXDataBody().getList());
            }
        } else {
            log.error("获取银行承兑汇票数据失败，失败原因：{}", draftTransStatusResponse.getResponseHeader().getStatus().getMessage());
        }

        // 获取erp承兑票据产生的银行流水
        List<BankBillDto> acceptanceBill = bankBillService.findAcceptanceBill();

        if (CollUtil.isNotEmpty(acceptanceBill)) {
            this.processAcceptanceBills(acceptanceBill);
        }

        return ReturnT.SUCCESS;
    }


    /**
     * 创建 B2eNbsQryDraftTransStatusReqBody 对象，用于查询出票方开出的所有承兑汇票。
     *
     * @return B2eNbsQryDraftTransStatusReqBody 对象
     */
    private B2eNbsQryDraftTransStatusReqBody createB2eNbsQryDraftTransStatusReqBody() {
        Date nowDate = new Date();
        B2eNbsQryDraftTransStatusReqBody reqBody = new B2eNbsQryDraftTransStatusReqBody();
        reqBody.setTrnId(IdUtil.simpleUUID());
        reqBody.setCustAccount(custAccount);
        reqBody.setOperType("1");
        reqBody.setInsId("");
        reqBody.setBusiStage("01");//05: 出票登记 改成01: 出票交付（提示收票）
        reqBody.setBillType("AC01");
        reqBody.setBeginAcptDt("");
        reqBody.setEndAcptDt("");
        reqBody.setBeginEndDate("");
        reqBody.setEndDate("");
        reqBody.setBeginAcptDt(DateUtil.format(DateUtil.offsetDay(nowDate, -PAY_BANK_DAY), DatePattern.NORM_DATE_PATTERN));
        reqBody.setEndAcptDt(DateUtil.format(nowDate, DatePattern.NORM_DATE_PATTERN));
        reqBody.setBillNo("");
        reqBody.setPageNo("");
        reqBody.setPageSize("100");
        return reqBody;
    }

    /**
     * 处理银行承兑汇票数据。
     *
     * @param draftBillInfo 银行承兑汇票列表
     */
    private void processDraftHoldingBills(List<B2eNbsQryDraftTransStatusResponse.BillInfo> draftBillInfo) {
        draftBillInfo.stream()
                .filter(b -> StrUtil.isNotBlank(b.getBillNo()))
                .filter(this::isNewBill)
                .forEach(this::processDraftHoldingBill);
    }

    /**
     * 判断是否为新的银行承兑汇票。
     *
     * @param draftHoldingBill 银行承兑汇票对象
     * @return 如果该汇票是新的，则返回 true，否则返回 false
     */
    private boolean isNewBill(B2eNbsQryDraftTransStatusResponse.BillInfo draftHoldingBill) {
        List<AcceptanceBillEntity> byBillNumber = acceptanceBillService.findByBillNumber(draftHoldingBill.getBillNo());
        return CollUtil.isEmpty(byBillNumber);
    }

    /**
     * 处理单个银行承兑汇票。
     *
     * @param draftHoldingBill 银行承兑汇票对象
     */
    private void processDraftHoldingBill(B2eNbsQryDraftTransStatusResponse.BillInfo draftHoldingBill) {
        DraftDetailReqBody detailReqBody = this.createDraftDetailReqBody(draftHoldingBill);
        DraftDetailResponse draftDetailResponse = acceptanceBillApiService.b2eNbsDraftDetail(detailReqBody);

        if (!draftDetailResponse.isSuccess()) {
            log.error("获取银行承兑汇票详情失败，失败原因：{}", draftDetailResponse.getResponseHeader().getStatus().getMessage());
            return;
        }

        // 创建承兑汇票
        AcceptanceBillEntity acceptanceBillEntity = this.createAcceptanceBillEntity(draftDetailResponse.getXDataBody().getBillInfo());
        acceptanceBillService.insertSelective(acceptanceBillEntity);

        // 创建银行流水
        String receiptUrl = this.generateAndUploadPdf(acceptanceBillEntity);
        BankBillDto bankBillDto = this.createBankBill(draftDetailResponse.getXDataBody().getBillInfo(), receiptUrl);

        // 更新承兑汇票银行里流水关系
        acceptanceBillEntity.setBankBillId(bankBillDto.getBankBillId());
        acceptanceBillService.updateByPrimaryKey(acceptanceBillEntity);

    }

    /**
     * 创建 DraftDetailReqBody 对象，用于查询银行承兑汇票详情。
     *
     * @param draftHoldingBill 银行承兑汇票对象
     * @return DraftDetailReqBody 对象
     */
    private DraftDetailReqBody createDraftDetailReqBody(B2eNbsQryDraftTransStatusResponse.BillInfo draftHoldingBill) {
        DraftDetailReqBody detailReqBody = new DraftDetailReqBody();
        detailReqBody.setTrnId(IdUtil.simpleUUID());
        detailReqBody.setCustAccount(custAccount);
        detailReqBody.setBillNo(draftHoldingBill.getBillNo());
        detailReqBody.setBillRangeStart(draftHoldingBill.getBillRangeStart());
        detailReqBody.setBillRangeEnd(draftHoldingBill.getBillRangeEnd());
        return detailReqBody;
    }

    /**
     * 创建 AcceptanceBillEntity 对象，用于存储银行承兑汇票详情。
     *
     * @param billInfo 银行承兑汇票详情
     * @return AcceptanceBillEntity 对象
     */
    private AcceptanceBillEntity createAcceptanceBillEntity(DraftDetailResponse.BillInfo billInfo) {
        AcceptanceBillEntity acceptanceBillEntity = new AcceptanceBillEntity();
        // 1.民生银行
        acceptanceBillEntity.setAcceptanceBank(1);
        acceptanceBillEntity.setBillNumber(billInfo.getBillNo());
        acceptanceBillEntity.setSubBillIntervalStart(billInfo.getBillRangeStart());
        acceptanceBillEntity.setSubBillIntervalEnd(billInfo.getBillRangeEnd());
        acceptanceBillEntity.setBillType(billInfo.getBillType());
        acceptanceBillEntity.setIssueDate(DateUtil.parse(billInfo.getRemitDt(), PURE_DATE_PATTERN));
        acceptanceBillEntity.setMaturityDate(DateUtil.parse(billInfo.getDueDt(), PURE_DATE_PATTERN));
        acceptanceBillEntity.setBillAmount(new BigDecimal(billInfo.getBillMoney()));
        acceptanceBillEntity.setAcceptanceDate(DateUtil.parse(billInfo.getRemitDt(), PURE_DATE_PATTERN));
        acceptanceBillEntity.setTransactionContractNo(billInfo.getTransCtrctNo());
        acceptanceBillEntity.setDrawerName(billInfo.getDrwrName());
        acceptanceBillEntity.setDrawerAccount(billInfo.getDrwrAcctNo());
        acceptanceBillEntity.setDrawerBankName(billInfo.getDrwrBankName());
        acceptanceBillEntity.setDrawerBankCode(billInfo.getDrwrBankNo());
        acceptanceBillEntity.setPayeeName(billInfo.getPyeeName());
        acceptanceBillEntity.setPayeeAccount(billInfo.getPyeeAcctNo());
        acceptanceBillEntity.setPayeeBankName(billInfo.getPyeeBankName());
        acceptanceBillEntity.setPayeeBankCode(billInfo.getPyeeBankNo());
        acceptanceBillEntity.setAcceptorName(billInfo.getAcptName());
        acceptanceBillEntity.setAcceptorAccount(billInfo.getAcptAcctNo());
        acceptanceBillEntity.setAcceptorBankName(billInfo.getAcptBankName());
        acceptanceBillEntity.setAcceptorBankCode(billInfo.getAcptBankNo());
        acceptanceBillEntity.setBillStatus(mapBillStatus(billInfo.getBillStatus()));
        // 1.未贴现
        acceptanceBillEntity.setDiscountStatus(1);
        return acceptanceBillEntity;
    }

    /**
     * 生成并上传 PDF 文件。
     *
     * @param acceptanceBillEntity 银行承兑汇票实体对象
     * @return PDF 文件的 URL
     */
    private String generateAndUploadPdf(AcceptanceBillEntity acceptanceBillEntity) {
        String receiptUrl = "";
        try {
            String htmlContent = generatePdfHtml(TEMPLATE_PATH, acceptanceBillEntity);
            InputStream inputStream = generatePdf(htmlContent);
            receiptUrl = ossUtilsService.upload2OssForInputStream("pdf", acceptanceBillEntity.getBillNumber() + ".pdf", inputStream);
        } catch (Exception e) {
            log.error("生成 PDF 文件失败", e);
        }
        return receiptUrl;
    }

    /**
     * 创建银行流水记录。
     *
     * @param billInfo   银行承兑汇票详情
     * @param receiptUrl PDF 文件的 URL
     */
    private BankBillDto createBankBill(DraftDetailResponse.BillInfo billInfo, String receiptUrl) {
        BankBillDto bankBillDto = new BankBillDto();
        bankBillDto.setCompanyId(ErpConstant.ONE);
        bankBillDto.setReceiptUrl(receiptUrl);
        bankBillDto.setTranFlow(billInfo.getBillNo());
        bankBillDto.setAccBankno(billInfo.getPyeeBankNo());
        bankBillDto.setAccName1(billInfo.getPyeeAcctName());
        bankBillDto.setAccno2(billInfo.getPyeeAcctNo());
        bankBillDto.setAmt1("0");
        bankBillDto.setAmt(new BigDecimal(billInfo.getBillMoney()));
        bankBillDto.setBankTag(7);
        bankBillDto.setCadbankNm(billInfo.getPyeeBankName());
        bankBillDto.setFlag1(0);
        bankBillDto.setIsConsistency(0);
        bankBillDto.setIsFee(0);
        bankBillDto.setMatchedAmount(new BigDecimal("0"));
        bankBillDto.setMatchedObject(857);

        Date date = new Date();
        String remitDt = billInfo.getRemitDt();
        DateTime parseDateTime = DateUtil.parse(remitDt, PURE_DATE_PATTERN);
        String currentTime = DateUtil.formatTime(date);
        String parsedDate = DateUtil.formatDate(parseDateTime);
        String newDateTimeStr = parsedDate + " " + currentTime;
        DateTime newDateTime = DateUtil.parseDateTime(newDateTimeStr);
        bankBillDto.setRealTrandatetime(newDateTime);
        bankBillDto.setRealTrandate(parseDateTime);
        bankBillDto.setTrandate(parseDateTime);
        bankBillDto.setTrantime(date);
        bankBillDto.setStatus(0);
        bankBillDto.setSettlementMethod(3);

        bankBillService.create(bankBillDto);

        return bankBillDto;
    }

    /**
     * 处理承兑票据列表
     * 承兑汇票自动核销
     *
     * @param acceptanceBill 承兑票据列表
     */
    private void processAcceptanceBills(List<BankBillDto> acceptanceBill) {
        List<Integer> bankBillIds = acceptanceBill.stream()
                .map(BankBillDto::getBankBillId)
                .collect(Collectors.toList());

        List<AcceptanceBillEntity> acceptanceBillEntities = acceptanceBillService.findByBankBillIdIn(bankBillIds);
        Map<String, List<AcceptanceBillEntity>> groupByBillNumber = acceptanceBillEntities.stream()
                .collect(Collectors.groupingBy(AcceptanceBillEntity::getTransactionContractNo));

        List<String> transactionContractNoList = acceptanceBillEntities.stream()
                .map(AcceptanceBillEntity::getTransactionContractNo)
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(transactionContractNoList)) {
            transactionContractNoList.forEach(transactionContractNo -> {
                List<CapitalBillDto> capitalBillDtoList = capitalBillService.findAcceptanceBill(transactionContractNo);
                List<AcceptanceBillEntity> currentAcceptanceBillEntities = groupByBillNumber.get(transactionContractNo);

                if (CollUtil.isNotEmpty(capitalBillDtoList) && CollUtil.isNotEmpty(currentAcceptanceBillEntities)) {
                    updateCapitalBill(capitalBillDtoList, currentAcceptanceBillEntities);
                }

            });
        }
    }

    private void sendErrorMsg(String billNumber) {
        String format = StrUtil.format(WxRobotMsgTemple.ACCEPTANCE_BILL_ERROR, billNumber);
        WxMsgDto wxMsgDto = new WxMsgDto().initWxMsgDto(format);
        wxRobotService.send(acceptanceBillNoticeRobotNum, wxMsgDto);
    }

    /**
     * 更新流水记录
     * 绑定资金和银行流水关系
     * 更新银行流水已匹配金额
     *
     * @param capitalBillDtoList            资金流水记录列表
     * @param currentAcceptanceBillEntities 当前承兑票据实体列表
     */
    private void updateCapitalBill(List<CapitalBillDto> capitalBillDtoList, List<AcceptanceBillEntity> currentAcceptanceBillEntities) {
        currentAcceptanceBillEntities.forEach(acceptanceBillEntity -> {
            BigDecimal alreadyMatchedAmount = BigDecimal.ZERO;
            BankBillEntity oldBillEntity = bankBillService.selectByPrimaryKey(acceptanceBillEntity.getBankBillId());
            if (oldBillEntity != null && oldBillEntity.getMatchedAmount() != null) {
                alreadyMatchedAmount = oldBillEntity.getMatchedAmount();
            }

            //剩余核销金额(AMT-MATCHED AMOUNT)
            BigDecimal billAmount = acceptanceBillEntity.getBillAmount();
            billAmount = billAmount.subtract(alreadyMatchedAmount);

            BigDecimal amount = capitalBillDtoList.stream().map(CapitalBillDto::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            if (amount.compareTo(billAmount) == 0) {
                capitalBillDtoList.forEach(capitalBillDto -> {
                    capitalBillDto.setBankBillId(acceptanceBillEntity.getBankBillId());
                    capitalBillDto.setTranFlow(acceptanceBillEntity.getBillNumber());
                    capitalBillService.update(capitalBillDto);
                });


                // 更新银行流水 MATCHED_AMOUNT
                BankBillEntity bankBillEntity = new BankBillEntity();
                bankBillEntity.setBankBillId(acceptanceBillEntity.getBankBillId());
                bankBillEntity.setMatchedAmount(alreadyMatchedAmount.add(amount));
                bankBillService.updateByPrimaryKeySelective(bankBillEntity);

            } else {
                log.warn("当前承兑汇票金额与流水金额不一致，流水号：{}，承兑汇票金额：{}，流水金额：{}", acceptanceBillEntity.getTransactionContractNo(), billAmount, amount);
                sendErrorMsg(acceptanceBillEntity.getBillNumber());
            }
        });
    }


    /**
     * 读取 HTML 模板文件，并将占位符替换为实际值
     *
     * @param templatePath   HTML 模板文件路径
     * @param acceptanceBill AcceptanceBillEntity 对象，包含汇票数据
     * @return 完成替换的 HTML 字符串
     */
    public static String generatePdfHtml(String templatePath, AcceptanceBillEntity acceptanceBill) {
        // 读取 HTML 模板文件
        String htmlTemplate = ResourceUtil.readUtf8Str(templatePath);

        // 替换占位符
        htmlTemplate = htmlTemplate.replace("{{issueDate}}", DateUtil.formatDate(acceptanceBill.getIssueDate()));
        htmlTemplate = htmlTemplate.replace("{{maturityDate}}", DateUtil.formatDate(acceptanceBill.getMaturityDate()));
        htmlTemplate = htmlTemplate.replace("{{drawerName}}", acceptanceBill.getDrawerName());
        htmlTemplate = htmlTemplate.replace("{{drawerAccount}}", acceptanceBill.getDrawerAccount());
        htmlTemplate = htmlTemplate.replace("{{drawerBankName}}", acceptanceBill.getDrawerBankName());
        htmlTemplate = htmlTemplate.replace("{{drawerBankCode}}", acceptanceBill.getDrawerBankCode());
        htmlTemplate = htmlTemplate.replace("{{payeeName}}", acceptanceBill.getPayeeName());
        htmlTemplate = htmlTemplate.replace("{{payeeAccount}}", acceptanceBill.getPayeeAccount());
        htmlTemplate = htmlTemplate.replace("{{payeeBankName}}", acceptanceBill.getPayeeBankName());
        htmlTemplate = htmlTemplate.replace("{{payeeBankCode}}", acceptanceBill.getPayeeBankCode());
        htmlTemplate = htmlTemplate.replace("{{billNumber}}", acceptanceBill.getBillNumber());

        BigDecimal billAmount = acceptanceBill.getBillAmount();
        String chineseCapital = NumberToChineseUtil.numberToChineseCapital(billAmount);
        htmlTemplate = htmlTemplate.replace("{{amountInWords}}", chineseCapital);

        // 增加中文金额
        // "分", "角" "元", "十", "百", "千", "万", "十万", "百万", "千万", "亿", "十亿"
        Long amount_fen = NumberToChineseUtil.getNumberAtUnit(billAmount, "分");
        Long amount_jiao = NumberToChineseUtil.getNumberAtUnit(billAmount, "角");
        Long amount_yuan = NumberToChineseUtil.getNumberAtUnit(billAmount, "元");
        Long amount_shi = NumberToChineseUtil.getNumberAtUnit(billAmount, "十");
        Long amount_bai = NumberToChineseUtil.getNumberAtUnit(billAmount, "百");
        Long amount_qian = NumberToChineseUtil.getNumberAtUnit(billAmount, "千");
        Long amount_wan = NumberToChineseUtil.getNumberAtUnit(billAmount, "万");
        Long amount_shiwan = NumberToChineseUtil.getNumberAtUnit(billAmount, "十万");
        Long amount_baiwan = NumberToChineseUtil.getNumberAtUnit(billAmount, "百万");
        Long amount_qianwan = NumberToChineseUtil.getNumberAtUnit(billAmount, "千万");
        Long amount_yi = NumberToChineseUtil.getNumberAtUnit(billAmount, "亿");
        Long amount_shiyi = NumberToChineseUtil.getNumberAtUnit(billAmount, "十亿");

        htmlTemplate = htmlTemplate.replace("{{amount_fen}}", Convert.toStr(amount_fen, ""));
        htmlTemplate = htmlTemplate.replace("{{amount_jiao}}", Convert.toStr(amount_jiao, ""));
        htmlTemplate = htmlTemplate.replace("{{amount_yuan}}", Convert.toStr(amount_yuan, ""));
        htmlTemplate = htmlTemplate.replace("{{amount_shi}}", Convert.toStr(amount_shi, ""));
        htmlTemplate = htmlTemplate.replace("{{amount_bai}}", Convert.toStr(amount_bai, ""));
        htmlTemplate = htmlTemplate.replace("{{amount_qian}}", Convert.toStr(amount_qian, ""));
        htmlTemplate = htmlTemplate.replace("{{amount_wan}}", Convert.toStr(amount_wan, ""));
        htmlTemplate = htmlTemplate.replace("{{amount_shiwan}}", Convert.toStr(amount_shiwan, ""));
        htmlTemplate = htmlTemplate.replace("{{amount_baiwan}}", Convert.toStr(amount_baiwan, ""));
        htmlTemplate = htmlTemplate.replace("{{amount_qianwan}}", Convert.toStr(amount_qianwan, ""));
        htmlTemplate = htmlTemplate.replace("{{amount_yi}}", Convert.toStr(amount_yi, ""));
        htmlTemplate = htmlTemplate.replace("{{amount_shiyi}}", Convert.toStr(amount_shiyi, ""));

        htmlTemplate = htmlTemplate.replace("{{acceptorName}}", acceptanceBill.getAcceptorName());
        htmlTemplate = htmlTemplate.replace("{{acceptorAccount}}", acceptanceBill.getAcceptorAccount());
        htmlTemplate = htmlTemplate.replace("{{acceptorBankName}}", acceptanceBill.getAcceptorBankName());
        htmlTemplate = htmlTemplate.replace("{{acceptorBankCode}}", acceptanceBill.getAcceptorBankCode());
        htmlTemplate = htmlTemplate.replace("{{transactionCotractNo}}", acceptanceBill.getTransactionContractNo());
        htmlTemplate = htmlTemplate.replace("{{acceptanceDate}}", DateUtil.formatDate(acceptanceBill.getAcceptanceDate()));

        // 状态 1.已出票 2.已承兑 3.已收票 4.已到期 5.已终止 6.已结清
        htmlTemplate = htmlTemplate.replace("{{billStatus}}", "已承兑");
        htmlTemplate = htmlTemplate.replace("{{discountStatus}}", acceptanceBill.getDiscountStatus().toString());

        return htmlTemplate;
    }

    /**
     * 生成 PDF 文件并返回 InputStream
     *
     * @param htmlContent HTML 内容
     * @return 生成的 PDF 文件的 InputStream
     * @throws Exception 如果生成过程中发生错误
     */
    public InputStream generatePdf(String htmlContent) throws Exception {
        ITextRenderer renderer = new ITextRenderer();
        renderer.setDocumentFromString(htmlContent);

        // 解决中文支持
        ITextFontResolver fontResolver = renderer.getFontResolver();

        if (SystemUtil.getOsInfo().isLinux()) {
            fontResolver.addFont("/usr/share/fonts/chinese/simsun.ttc", BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
        } else {
            fontResolver.addFont("c:/Windows/Fonts/simsun.ttc", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        }

        renderer.layout();

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        renderer.createPDF(baos);

        return new ByteArrayInputStream(baos.toByteArray());
    }



    private Integer mapBillStatus(String billStatus) {
        switch (billStatus) {
            // 已出票
            case "CS01":
                return 1;
            // 已承兑
            case "CS02":
                return 2;
            // 已收票
            case "CS03":
                return 3;
            // 已到期
            case "CS04":
                return 4;
            // 已终止
            case "CS05":
                return 5;
            // 已结清
            case "CS06":
                return 6;
            default:
                return null;
        }
    }

    /**
     * 将数字转换为中文的工具类
     * 提供了将阿拉伯数字表示转换为对应的中文大写表示的功能
     * 主要用于财务相关场景，如将金额数字转换为中文大写金额
     */
    public static class NumberToChineseUtil {


        private static final String[] CHINESE_UNITS = {"角", "分", "元", "十", "百", "千", "万", "十万", "百万", "千万", "亿", "十亿", "百亿", "千亿", "万亿", "十万亿", "百万亿", "千万亿"};

        private static final String[] CHINESE_CAPITAL_NUMBERS = {"零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"};
        private static final String[] CHINESE_CAPITAL_UNITS = {"", "拾", "佰", "仟", "万", "拾万", "佰万", "仟万", "亿", "拾亿", "佰亿", "仟亿", "万亿", "拾万亿", "佰万亿", "仟万亿"};
        private static final String[] CHINESE_CAPITAL_DECIMAL_UNITS = {"角", "分"};

        // 获取指定单位位置的数字
        public static Long getNumberAtUnit(BigDecimal number, String unit) {
            int unitIndex = getUnitIndex(unit);

            if (unitIndex == -1) {
                throw new IllegalArgumentException("不支持的单位: " + unit);
            }

            // 检查单位是否超过输入金额的范围
            int maxUnitIndex = number.precision() - number.scale() + 1; // 计算最大单位索引
            if (unitIndex > maxUnitIndex) {
                return null;
            }

            // 处理小数部分
            if (unitIndex < 2) {
                BigDecimal decimalPart = number.subtract(number.setScale(0, RoundingMode.DOWN)).multiply(BigDecimal.TEN.pow(2)).setScale(0, RoundingMode.DOWN);
                return decimalPart.remainder(BigDecimal.TEN.pow(2 - unitIndex)).divide(BigDecimal.TEN.pow(1 - unitIndex), RoundingMode.DOWN).longValue();
            }

            // 处理整数部分
            BigDecimal integerPart = number.setScale(0, RoundingMode.DOWN);
            BigDecimal divisor = BigDecimal.TEN.pow(unitIndex - 2);
            return integerPart.divide(divisor, RoundingMode.DOWN).remainder(BigDecimal.TEN).longValue();
        }

        private static int getUnitIndex(String unit) {
            for (int i = 0; i < CHINESE_UNITS.length; i++) {
                if (CHINESE_UNITS[i].equals(unit)) {
                    return i;
                }
            }
            return -1;
        }

        // 输出中文的大写金额
        public static String numberToChineseCapital(BigDecimal number) {
            if (number.compareTo(BigDecimal.ZERO) == 0) {
                return "零元整";
            }

            StringBuilder result = new StringBuilder();

            // 处理整数部分
            BigDecimal integerPart = number.setScale(0, RoundingMode.DOWN);
            if (integerPart.compareTo(BigDecimal.ZERO) > 0) {
                result.append(convertIntegerPartCapital(integerPart)).append("元");
            }

            // 处理小数部分
            BigDecimal decimalPart = number.subtract(integerPart).multiply(BigDecimal.valueOf(100)).setScale(0, RoundingMode.HALF_UP);
            if (decimalPart.compareTo(BigDecimal.ZERO) > 0) {
                result.append(convertDecimalPartCapital(decimalPart));
            } else {
                result.append("整");
            }

            return result.toString();
        }

        private static String convertIntegerPartCapital(BigDecimal number) {
            StringBuilder result = new StringBuilder();
            int unitIndex = 0;
            boolean zero = false;
            boolean lastZero = false;

            while (number.compareTo(BigDecimal.ZERO) > 0) {
                int digit = number.remainder(BigDecimal.TEN).intValue();
                if (digit == 0) {
                    if (!zero && unitIndex > 0) {
                        result.insert(0, CHINESE_CAPITAL_NUMBERS[digit]);
                        zero = true;
                    }
                    lastZero = true;
                } else {
                    if (zero && !lastZero) {
                        result.insert(0, CHINESE_CAPITAL_NUMBERS[0]); // 插入零
                    }
                    result.insert(0, CHINESE_CAPITAL_UNITS[unitIndex]).insert(0, CHINESE_CAPITAL_NUMBERS[digit]);
                    zero = false;
                    lastZero = false;
                }
                number = number.divide(BigDecimal.TEN, RoundingMode.DOWN);
                unitIndex++;
            }

            // 处理万、亿单位
            if (result.toString().endsWith("亿")) {
                result.append("元");
            }

            return result.toString();
        }

        private static String convertDecimalPartCapital(BigDecimal number) {
            StringBuilder result = new StringBuilder();
            int unitIndex = 0;

            while (number.compareTo(BigDecimal.ZERO) > 0) {
                int digit = number.remainder(BigDecimal.TEN).intValue();
                if (digit != 0) {
                    result.append(CHINESE_CAPITAL_NUMBERS[digit]).append(CHINESE_CAPITAL_DECIMAL_UNITS[unitIndex]);
                }
                number = number.divide(BigDecimal.TEN, RoundingMode.DOWN);
                unitIndex++;
            }

            return result.toString();
        }
    }


}
