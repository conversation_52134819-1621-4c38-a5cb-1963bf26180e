package com.vedeng.erp.buyorder.service;

import com.vedeng.authorization.model.User;
import com.vedeng.erp.buyorder.dto.GeExamineFeedBackDto;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/2/16 13:12
 * @describe
 */
public interface GeBusinessChanceFeedbackService {
    GeExamineFeedBackDto queryFednBackInfo(Integer geBussinessChanceId);

    void checkGeExamine(GeExamineFeedBackDto geExamineFeedBackDto);

    void saveGeExamine(GeExamineFeedBackDto geExamineFeedBackDto, User user);
}
