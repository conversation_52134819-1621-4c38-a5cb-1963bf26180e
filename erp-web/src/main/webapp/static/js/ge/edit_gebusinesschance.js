//维护信息提交校验
function geEditSubmit(){
    $(".warning").remove();
    checkLogin();
    var $form = $("#editGeBusinessChanceForm");
    //销售额校验
    var salesAmount = $("#salesAmount").val();
    if(salesAmount != ''){
        if( ! /^[0-9]+(.[0-9]{0,8})?$/.test(salesAmount)){
            warnTips("salesAmountErrorMsg","只能输入数字,最多支持小数点后八位");
            return false;
        }
        if(salesAmount.length > 20){
            warnTips("salesAmountErrorMsg","金额过大");
            return false;
        }
    }
    //床位数
    var bedNum = $("#bedNum").val();
    if(bedNum != ''){
        if( ! /^[0-9]+(.[0-9]{0,8})?$/.test(bedNum)){
            warnTips("bedNumErrorMsg","只能输入数字,最多支持小数点后八位");
            return false;
        }
        if(bedNum.length > 20){
            warnTips("bedNumErrorMsg","金额过大");
            return false;
        }
    }
    //年营收
    var yearInSum = $("#yearInSum").val();
    if(yearInSum != '') {
        if (!/^[0-9]+(.[0-9]{0,8})?$/.test(yearInSum)) {
            warnTips("yearInSumErrorMsg", "只能输入数字,最多支持小数点后八位");
            return false;
        }
        if(yearInSum.length > 20){
            warnTips("yearInSumErrorMsg","金额过大");
            return false;
        }
    }
    //CT患者量（日）
    var ctDailyNum = $("#ctDailyNum").val();
    if(ctDailyNum != '') {
        if (!/^[0-9]+(.[0-9]{0,8})?$/.test(ctDailyNum)) {
            warnTips("ctDailyNum", "只能输入数字,最多支持小数点后八位");
            return false;
        }
        if(ctDailyNum.length > 20){
            warnTips("ctDailyNum","金额过大");
            return false;
        }
    }
    //判断终端信息模块是否改动
    var terminalPartIsChange = $("#terminalPartIsChange").val();
    if(terminalPartIsChange != 1){
        //终端信息模块为未改动的情况下--判断本次是否改动
        var hospitalSize = $("#hospitalSize").val();
        var isNewHospital = $("#isNewHospital").val();
        var newHospitalPercent = $("#newHospitalPercent").val();
        var expectBuyTime = $("#expectBuyTime").val();
        if((salesAmount != '' && salesAmount != 0.00) || hospitalSize != 0 || bedNum != '' || yearInSum != '' || ctDailyNum != ''
            || isNewHospital != '' || newHospitalPercent != 0 || expectBuyTime != ''){
            $("#terminalPartIsChange").val(1);
        }else {
            $("#terminalPartIsChange").val(0);
        }
    }
    //现有竞争对手
    var competeName = $("#competeName").val();
    if(competeName != '') {
        if (competeName.length > 100) {
            warnTips("competeName", "现有竞争对手不可超过100个汉字");
            return false;
        }
    }
    //竞争对手产品
    var competeSkuName = $("#competeSkuName").val();
    if(competeSkuName != '') {
        if (competeSkuName.length > 100) {
            warnTips("competeSkuName", "竞争对手产品不可超过100个汉字");
            return false;
        }
    }
    //竞品价格
    var competeSkuPrice = $("#competeSkuPrice").val();
    if(competeSkuPrice != '') {
        if (!/^[0-9]+(.[0-9]{0,8})?$/.test(competeSkuPrice)) {
            warnTips("competeSkuPrice", "只能输入数字,最多支持小数点后八位");
            return false;
        }
        if(competeSkuPrice.length > 20){
            warnTips("competeSkuPrice","金额过大");
            return false;
        }
    }
    //判断竞品信息模块是否改动
    var competePartIsChange = $("#competePartIsChange").val();
    if(competePartIsChange != 1){
        if(competeName != '' || competeSkuName != '' || (competeSkuPrice != '' && competeSkuPrice != 0.00)){
            $("#competePartIsChange").val(1);
        }else {
            $("#competePartIsChange").val(0);
        }
    }
    //原有品牌
    var originSkuBand = $("#originSkuBand").val();
    if(originSkuBand != '') {
        if (originSkuBand.length > 100) {
            warnTips("originSkuBand", "原有品牌不可超过100个汉字");
            return false;
        }
    }
    //原有型号
    var originSkuModel = $("#originSkuModel").val();
    if(originSkuModel != '') {
        if (originSkuModel.length > 100) {
            warnTips("originSkuModel", "原有型号不可超过100个汉字");
            return false;
        }
    }
    //销售公司
    var saleCompanyName = $("#saleCompanyName").val();
    if(saleCompanyName != '') {
        if (saleCompanyName.length > 100) {
            warnTips("saleCompanyName", "销售公司不可超过100个汉字");
            return false;
        }
    }
    //判断原有装机信息模块是否改动
    var originInstallPartIsChange = $("#originInstallPartIsChange").val();
    if(originInstallPartIsChange != 1){
        var isNewInstall = $("#isNewInstall").val();
        var installTime = $("#installTime").val();
        if(isNewInstall != '' || originSkuBand != '' || originSkuModel != '' || installTime != '' || saleCompanyName != ''){
            $("#originInstallPartIsChange").val(1);
        }else {
            $("#originInstallPartIsChange").val(0);
        }
    }
    //资金来源
    var moneySource = $("#moneySource").val();
    if(moneySource != '') {
        if (moneySource.length > 100) {
            warnTips("moneySource", "资金来源不可超过100个汉字");
            return false;
        }
    }
    //资金情况
    var moneySituation = $("#moneySituation").val();
    if(moneySituation != '') {
        if (moneySituation.length > 100) {
            warnTips("moneySituation", "资金情况不可超过100个汉字");
            return false;
        }
    }
    //预算/预备资金金额
    var prepareAmount = $("#prepareAmount").val();
    if(prepareAmount != '') {
        if (!/^[0-9]+(.[0-9]{0,8})?$/.test(prepareAmount)) {
            warnTips("prepareAmount", "只能输入数字,最多支持小数点后八位");
            return false;
        }
        if(prepareAmount.length > 20){
            warnTips("prepareAmount","金额过大");
            return false;
        }
    }
    //报价方案
    var quoteMethod = $("#quoteMethod").val();
    if(quoteMethod != '') {
        if (quoteMethod.length > 100) {
            warnTips("quoteMethod", "报价方案不可超过100个汉字");
            return false;
        }
    }
    //方案价格
    var quoteMethodPrice = $("#quoteMethodPrice").val();
    if(quoteMethodPrice != '') {
        if (!/^[0-9]+(.[0-9]{0,8})?$/.test(quoteMethodPrice)) {
            warnTips("quoteMethodPrice", "只能输入数字,最多支持小数点后八位");
            return false;
        }
        if(quoteMethodPrice.length > 20){
            warnTips("quoteMethodPrice","金额过大");
            return false;
        }
    }
    //判断商机详细信息模块是否改动
    var chanceDetailPartIsChange = $("#chanceDetailPartIsChange").val();
    if(chanceDetailPartIsChange != 1){
        var winOrderPercent = $("#winOrderPercent").val();
        var buyType = $("#buyType").val();
        var isApplyInspect = $("#isApplyInspect").val();
        var isEngineComplete = $("#isEngineComplete").val();
        if(winOrderPercent != '' || moneySource != '' || moneySituation != '' || (prepareAmount != '' && prepareAmount != 0.00)
            || buyType != 0 || isApplyInspect != '' || isEngineComplete != '' || quoteMethod != '' || (quoteMethodPrice != '' && quoteMethodPrice != 0.00)){
            $("#chanceDetailPartIsChange").val(1);
        }else {
            $("#chanceDetailPartIsChange").val(0);
        }
    }
    //报价方案
    var needComplete = $("#needComplete").val();
    if(needComplete != '') {
        if (needComplete.length > 100) {
            warnTips("needComplete", "报价方案不可超过100个汉字");
            return false;
        }
    }
    //协助部门
    var assistDepartment = $("#assistDepartment").val();
    if(assistDepartment != '') {
        if (assistDepartment.length > 100) {
            warnTips("assistDepartment", "协助部门不可超过100个汉字");
            return false;
        }
    }
    //解决进展
    var projectEvolve = $("#projectEvolve").val();
    if(projectEvolve != '') {
        if (projectEvolve.length > 100) {
            warnTips("projectEvolve", "解决进展不可超过100个汉字");
            return false;
        }
    }
    //下一步计划
    var nextPlan = $("#nextPlan").val();
    if(nextPlan != '') {
        if (nextPlan.length > 100) {
            warnTips("nextPlan", "下一步计划不可超过100个汉字");
            return false;
        }
    }
    //需要支持事宜
    var needSupport = $("#needSupport").val();
    if(needSupport != '') {
        if (needSupport.length > 100) {
            warnTips("needSupport", "需要支持事宜不可超过100个汉字");
            return false;
        }
    }
    //判断关键问题解决模块是否改动
    var keyDealPartIsChange = $("#keyDealPartIsChange").val();
    if(keyDealPartIsChange != 1){
        var projectPhase = $("#projectPhase").val();
        var needTime = $("#needTime").val();
        var evolveTime = $("#evolveTime").val();
        if(projectPhase != 0 || needComplete != '' || needTime != '' || assistDepartment != '' || projectEvolve != ''
            || evolveTime != '' || nextPlan != '' || needSupport != ''){
            $("#keyDealPartIsChange").val(1);
        }else {
            $("#keyDealPartIsChange").val(0);
        }
    }
    $form.submit();
}