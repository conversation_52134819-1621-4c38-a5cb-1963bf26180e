package com.newtask.quoteorder.strategy;

import com.newtask.quoteorder.TimeUtils;
import com.newtask.quoteorder.exception.QuotedAlarmUpgradedException;
import com.newtask.quoteorder.model.QuotedAlarmRecord;
import com.vedeng.order.enums.QuotedAlarmLevelEnum;
import com.vedeng.order.enums.QuotedAlarmModeEnum;
import org.joda.time.DateTime;
import org.joda.time.Interval;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.LinkedList;

/**
 * <AUTHOR> [<EMAIL>]
 */
public abstract class AbstractQuotedAlarmStrategy implements QuotedAlarmStrategy {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    private final static String PATTERN_ONE = "HH:mm:ss";

    private final static DateTimeFormatter DATETIME_FORMATTER_ONE = DateTimeFormat.forPattern(PATTERN_ONE);

    private final static DateTime BEGIN_TIME;
    private final static DateTime END_TIME;

    private final static Interval WORKING_DAY_INTERVAL;

    static {
        BEGIN_TIME = DATETIME_FORMATTER_ONE.parseDateTime("08:45:00");
        END_TIME = DATETIME_FORMATTER_ONE.parseDateTime("17:45:00");
        WORKING_DAY_INTERVAL = new Interval(BEGIN_TIME, END_TIME);
    }


    private boolean isWorkingTime(DateTime target) {
        return WORKING_DAY_INTERVAL.contains(convertByPatternOne(target));
    }

    private DateTime convertByPatternOne(DateTime target) {
        return new DateTime(DATETIME_FORMATTER_ONE.parseMillis(target.toString(PATTERN_ONE)));
    }


    private final static int UPGRADE_LEVEL_STEP = 1;

    protected Integer getNextLevel(Integer currentLevel) {
        Integer upgradedLevel = doUpgrade(currentLevel);
        if (upgradedLevel == null) {
            throw new QuotedAlarmUpgradedException("当前等级已超过预警最大级别, mode：" + getMode().getMessage() + "，currentLevel:" + currentLevel);
        }
        return upgradedLevel;
    }


    private Integer doUpgrade(Integer currentLevel) {
        if (currentLevel == null) {
            return null;
        }

        LinkedList<QuotedAlarmLevelEnum> levelQueue = getMode().getAllowedLevels();

        boolean match = false;
        int currIdx = 0;

        while (currIdx < levelQueue.size()) {
            if (currentLevel.equals(levelQueue.get(currIdx).getLevel())) {
                match = true;
                break;
            }

            currIdx++;
        }

        if (match) {
            return levelQueue.get(currIdx + UPGRADE_LEVEL_STEP).getLevel();
        }

        return null;
    }

    protected abstract QuotedAlarmModeEnum getMode();

    @Override
    public void upgradeForNextLevel(QuotedAlarmRecord quotedAlarmRecord, Integer purchaseTimeType) {
        //no-op
    }

    @Override
    public void upgradeForNextLevel(QuotedAlarmRecord quotedAlarmRecord) {
        //no-op
    }

    /**
     * 获取下一次触发时间戳，
     *
     * @param startTimeInMills
     * @param period
     * @return
     */
    protected long getNextTriggerTime(long startTimeInMills, Duration period, boolean enableWorkingDay) {
        int daysToAdd = 0;
        DateTime nextTriggerTime = new DateTime(startTimeInMills);
        if (!isWorkingTime(nextTriggerTime)) {
            if (WORKING_DAY_INTERVAL.isBefore(convertByPatternOne(nextTriggerTime))) {
                daysToAdd += 1;
            }
            nextTriggerTime = nextTriggerTime.withHourOfDay(BEGIN_TIME.getHourOfDay()).withMinuteOfHour(BEGIN_TIME.getMinuteOfHour());
        }

        if (period.toDays() < 1) {
            //时间间隔小于一天时
            nextTriggerTime = nextTriggerTime.plusMinutes(Math.toIntExact(period.toMinutes()));
        } else {
            //时间间隔大于等于一天时
            daysToAdd += Math.toIntExact(period.toDays());
        }

        if (enableWorkingDay) {
            try {
                nextTriggerTime = new DateTime(TimeUtils.calculate(nextTriggerTime.getMillis(), daysToAdd));
            } catch (Exception e) {
                //ignore
            }
        } else {
            nextTriggerTime = nextTriggerTime.plusDays(daysToAdd);
        }

        return nextTriggerTime.getMillis();
    }


    /**
     * 获取触发计算下一次触发时间点的开始时间
     *
     * @return
     */
    protected Long getBeginTimeForNextTrigger(QuotedAlarmRecord quotedAlarmRecord) {
        final Long beginTimeForNextTrigger;
        if (quotedAlarmRecord.hasTriggerTime()) {
            beginTimeForNextTrigger = quotedAlarmRecord.getTriggerTime();
        } else {
            beginTimeForNextTrigger = quotedAlarmRecord.getFirstMarkTime();
        }
        return beginTimeForNextTrigger;

    }
}
