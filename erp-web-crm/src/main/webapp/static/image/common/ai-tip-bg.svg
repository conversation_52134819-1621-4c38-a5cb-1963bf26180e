<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="193px" height="47px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="1497px" y="1070px" width="193px" height="47px" filterUnits="userSpaceOnUse" id="filter43">
      <feOffset dx="0" dy="3" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="3" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 0.117647058823529 0  " in="shadowComposite" />
    </filter>
    <g id="widget44">
      <path d="M 0 30  L 0 5  C 0 2.20000000000005  2.20000000000005 0  5 0  L 176 0  C 178.8 0  181 2.20000000000005  181 5  L 181 25  C 181 27.8  178.8 30  176 30  L 0 30  Z M 0 35  L 0 30  L 5 30  C 5 30  3.125 30.1875  1.625 31.75  C 0.125 33.3125  0 35  0 35  Z " fill-rule="nonzero" fill="#0099ff" stroke="none" transform="matrix(1 0 0 1 1503 1073 )" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -1497 -1070 )">
    <use xlink:href="#widget44" filter="url(#filter43)" />
    <use xlink:href="#widget44" />
  </g>
</svg>