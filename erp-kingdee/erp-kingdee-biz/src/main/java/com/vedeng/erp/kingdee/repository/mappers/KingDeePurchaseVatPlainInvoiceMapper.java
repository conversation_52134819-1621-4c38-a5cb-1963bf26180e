package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseVatPlainInvoiceEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface KingDeePurchaseVatPlainInvoiceMapper {
    /**
     * delete by primary key
     * @param purchaseVatPlainInvoiceId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer purchaseVatPlainInvoiceId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(KingDeePurchaseVatPlainInvoiceEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(KingDeePurchaseVatPlainInvoiceEntity record);

    /**
     * select by primary key
     * @param purchaseVatPlainInvoiceId primary key
     * @return object by primary key
     */
    KingDeePurchaseVatPlainInvoiceEntity selectByPrimaryKey(Integer purchaseVatPlainInvoiceId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(KingDeePurchaseVatPlainInvoiceEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(KingDeePurchaseVatPlainInvoiceEntity record);

    int updateBatchSelective(List<KingDeePurchaseVatPlainInvoiceEntity> list);

    int batchInsert(@Param("list") List<KingDeePurchaseVatPlainInvoiceEntity> list);

    List<KingDeePurchaseVatPlainInvoiceEntity> findByFQzokBddjtid(@Param("list") List<String> invoiceIds);

    KingDeePurchaseVatPlainInvoiceEntity selectByFQzokBddjtid(String FQzokBddjtid);
}