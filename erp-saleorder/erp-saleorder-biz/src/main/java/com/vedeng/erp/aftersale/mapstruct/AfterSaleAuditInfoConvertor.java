package com.vedeng.erp.aftersale.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.aftersale.domain.entity.AfterSaleAuditInfoEntity;
import com.vedeng.erp.aftersale.dto.AfterSaleAuditInfoDto;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface AfterSaleAuditInfoConvertor  extends BaseMapStruct<AfterSaleAuditInfoEntity, AfterSaleAuditInfoDto> {


}
