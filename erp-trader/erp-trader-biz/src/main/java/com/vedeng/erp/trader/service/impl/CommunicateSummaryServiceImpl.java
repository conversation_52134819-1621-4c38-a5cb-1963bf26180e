package com.vedeng.erp.trader.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.redis.utils.RedisUtil;
import com.vedeng.erp.trader.domain.dto.CommunicateAiSummaryDto;
import com.vedeng.erp.trader.dto.CommunicateRecordDto;
import com.vedeng.erp.trader.domain.entity.CommunicateRecordEntity;
import com.vedeng.erp.trader.domain.entity.CommunicateSummaryEntity;
import com.vedeng.erp.trader.dto.TraderUserDto;
import com.vedeng.erp.trader.mapper.CommunicateRecordMapper;
import com.vedeng.erp.trader.mapper.TraderCommunicateSummaryMapper;
import com.vedeng.erp.trader.mapper.TraderMapper;
import com.vedeng.erp.trader.mapstruct.CommunicateSummaryConvertor;
import com.vedeng.erp.trader.service.CommunicateSummaryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/18 14:56
 */
@Slf4j
@Service
public class CommunicateSummaryServiceImpl implements CommunicateSummaryService {

    @Resource
    private TraderCommunicateSummaryMapper traderCommunicateSummaryMapper;

    @Autowired
    private CommunicateSummaryConvertor communicateSummaryConvertor;

    @Autowired
    private CommunicateRecordMapper communicateRecordMapper;

    @Autowired
    @Qualifier("newTraderMapper")
    private TraderMapper traderMapper;

    private static final String SUMMARY_REDIS_KEY = "trader:summary:";

    @Override
    public CommunicateAiSummaryDto getByCommunicateSummaryId(Long communicateSummaryId) {
        CommunicateSummaryEntity summaryEntity = traderCommunicateSummaryMapper.selectByPrimaryKey(communicateSummaryId);
        CommunicateAiSummaryDto communicateAiSummaryDto;
        if (Objects.nonNull(summaryEntity.getUpdateLog())) {
            communicateAiSummaryDto = summaryEntity.getUpdateLog().toJavaObject(CommunicateAiSummaryDto.class);
        } else {
            communicateAiSummaryDto = communicateSummaryConvertor.toDto(summaryEntity);
        }

        // 查询沟通记录相关信息
        CommunicateRecordDto communicateRecordDto = new CommunicateRecordDto();
        communicateRecordDto.setCommunicateRecordId(summaryEntity.getCommunicateRecordId());
        CommunicateRecordEntity communicateRecordEntity = communicateRecordMapper.selectByPrimaryKey(communicateRecordDto);
        // 联系人信息
        communicateAiSummaryDto.setTraderContactName(communicateRecordEntity.getContact() + communicateRecordEntity.getContactMob());
        communicateAiSummaryDto.setCommunicateTimeRange(DateFormatUtils.format(communicateRecordEntity.getBegintime(), "yyyy-MM-dd HH:mm:ss") + "-" + DateFormatUtils.format(communicateRecordEntity.getEndtime(), "yyyy-MM-dd HH:mm:ss"));

        // 查询客户相关信息
        TraderUserDto traderUserDto = Optional.ofNullable(traderMapper.getTraderUserByTraderId(communicateRecordEntity.getTraderId())).orElse(new TraderUserDto());
        communicateAiSummaryDto.setTraderName(traderUserDto.getTraderName());
        communicateAiSummaryDto.setSaleUserName(traderUserDto.getUserName());
        return communicateAiSummaryDto;
    }

    @Override
    public CommunicateAiSummaryDto getByTraderId(Integer traderId, Integer currentUserId) {
        // 如果当前登录用户是该客户的归属销售，那么记录下此时的时间作为归属销售最近一次查看当前客户的AI助理信息的时间
        TraderUserDto traderUserDto = Optional.ofNullable(traderMapper.getTraderUserByTraderId(traderId)).orElse(new TraderUserDto());
        if (currentUserId.equals(traderUserDto.getUserId())) {
            log.info("刷新当前客户的summary查看时间, traderId:{}", traderId);
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date now = new Date();
            RedisUtil.StringOps.set(SUMMARY_REDIS_KEY + traderId, formatter.format(now));
        }
        CommunicateSummaryEntity summaryEntity = Optional.ofNullable(traderCommunicateSummaryMapper.getLatestSummaryByTraderId(traderId)).orElse(new CommunicateSummaryEntity());
        // 有修改记录的，展示修改过的信息
        if (Objects.nonNull(summaryEntity.getUpdateLog())) {
            CommunicateAiSummaryDto communicateAiSummaryDto = summaryEntity.getUpdateLog().toJavaObject(CommunicateAiSummaryDto.class);
            List<Map<String, String>> goodsList = communicateAiSummaryDto.getGoodsList();
            communicateAiSummaryDto.setIntentionGoods(goodsList.stream()
                    .map(map -> map.get("intentionGoods"))
                    .collect(Collectors.joining(",")));
            communicateAiSummaryDto.setBrands(goodsList.stream()
                    .map(map -> map.get("brands"))
                    .collect(Collectors.joining(",")));
            communicateAiSummaryDto.setModels(goodsList.stream()
                    .map(map -> map.get("models"))
                    .collect(Collectors.joining(",")));
            return communicateAiSummaryDto;
        }
        return communicateSummaryConvertor.toDto(summaryEntity);
    }

    @Override
    public void saveUpdate(CommunicateAiSummaryDto summaryDto) {
        CommunicateSummaryEntity update = new CommunicateSummaryEntity();
        update.setCommunicateSummaryId(summaryDto.getCommunicateSummaryId());
        summaryDto.setUpdateLog(null);
        update.setUpdateLog(JSONObject.parseObject(JSONObject.toJSONString(summaryDto)));
        update.setModTime(new Date());
        traderCommunicateSummaryMapper.updateByPrimaryKeySelective(update);
    }
}