<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="安全库存日志" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%= basePath %>static/js/flash/prepare/deal_stock.js?rnd=${resourceVersionKey}'></script>
<div class="content">
    <div class="sku-info">
        <ul>
            <li style="width: 200px;">
                <div class="infor_name">
                    <label class="info">订货号：</label>
                </div>
                <div class="info-name">
                    <label>${stockLog.prepareStock.skuNo}</label>
                </div>
            </li>
            <li>
                <div class="infor_name">
                    <label class="info">商品名称：</label>
                </div>
                <div class="info-name">
                    <label>${stockLog.prepareStock.skuName}</label>
                </div>
            </li>
            <li>
                <div class="infor_name">
                    <label class="info">安全库存：</label>
                </div>
                <div class="info-name">
                    <label>${stockLog.prepareStock.safeStock}&nbsp${stockLog.prepareStock.unit}</label>
                </div>
            </li>
            <span class="line"></span>
            <c:choose>
                <c:when test="${not empty stockLog.stockLogList}">
                    <c:forEach var="log" items="${stockLog.stockLogList}" varStatus="num">
                        <li class="li-wid" style="width: 300px;">
                            <div class="infor_name">
                                <label class="info">调整时间：</label>
                            </div>
                            <div class="info-name">
                                <label>${log.operateTimeStr}</label>
                            </div>
                        </li>
                        <li class="li-wid" style="width: 250px;">
                            <div class="infor_name">
                                <label class="info">操作者：</label>
                            </div>
                            <div class="info-name">
                                <label>${log.operateUserName}</label>
                            </div>
                        </li>
                        <li class="li-wid">
                            <div class="infor_name">
                                <label class="info">调整安全库存：</label>
                            </div>
                            <div class="info-name">
                                <label>${log.safeStock}&nbsp${stockLog.prepareStock.unit}</label>
                            </div>
                        </li>
                        <li class="li-wid">
                            <div class="infor_name">
                                <label class="info">原值：</label>
                            </div>
                            <div class="info-name">
                                <label>${log.originSafeStock}&nbsp${stockLog.prepareStock.unit}</label>
                            </div>
                        </li>
                        <br/>
                        <li class="li-wid" style="width: 100%; height: auto;">
                            <div class="infor_name">
                                <label class="info">调整原因：</label>
                            </div>
                            <div class="info-name" style="width: 82%;">
                                <label>${log.operateReason}</label>
                            </div>
                        </li>
                        <span class="line"></span>
                    </c:forEach>
                </c:when>
                <c:otherwise>
                    <div class="tcenter">
                        暂无安全库存日志
                    </div>
                </c:otherwise>
            </c:choose>
        </ul>
    </div>
</div>
<div class="submit-c tcenter">
    <button id="close-layer" type="button" class="bt-largest bt-bg-style bg-light-blue">确定</button>
</div>
<style>
    .sku-info {
        width: 100%;
        padding: 20px;
        font-size: 15px;
    }

    .sku-info ul li {
        float: left;
        height: 30px;
        line-height: 30px;
    }

    .info {
        font-weight: 500;
    }

    .sku-info .info-name {
        float: left;
        margin: 3px 10px 0 0;
    }

    .li-wid {
        width: 200px;
    }

    .line {
        width: 100%;
    }

    .submit-c {
        position: fixed;
        bottom: 10px;
        width: 100%;
    }

</style>
<%@ include file="../../common/footer.jsp"%>
