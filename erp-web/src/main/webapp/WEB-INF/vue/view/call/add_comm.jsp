<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<div id="app" style="display: flex; flex-direction: column; min-height: 100vh;">
    <div id="recordIds" style="margin-left: 10px">
        <div v-for="(input, index) in inputs" :key="index">
            <el-input type="text" v-model="inputs[index]" style="margin-top:10px ;width: 40%" maxlength="9" min="1"></el-input>
            <el-button @click="removeInput(index)"  class="el-icon-delete"></el-button>
        </div>
        <el-link  underline="false" type="primary" @click="addInput" style="margin-top: 10px">+添加</el-link>
    </div>
    <div style="position: fixed; bottom: 0;  right: 0; padding: 10px;">
        <el-button type="primary" @click="submit" :loading="loading">确定</el-button>
        <el-button @click="cancel">取消</el-button>
    </div>
</div>

<script src="${pageContext.request.contextPath}/static/api/trader/traderCustomer.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">
    const expressId = ${expressId};
new Vue({
    el: '#app',
    data() {
       return {
           loading: false,
           inputs: ['']
       }
    },
    // created() {
    //     this.initData();
    // },
    methods:{
        addInput() {
            this.inputs.push('');
        },
        removeInput(index) {
            this.inputs.splice(index, 1);
        },

        submit(){
            var data={
                expressId:expressId,
                communicateRecorderIds:this.inputs.join(',')
            }
            // 如果输入为空，直接关闭
            if (this.inputs.length == 0) {
                this.cancel();
                return;
            }
            bindCommunicationId(data).then(res => {
                if (res.data.code == 0) {
                    this.$message.success("提交成功");
                    this.loading = true;
                    parent.location.reload();
                } else {
                    this.$message.error(res.data.message);
                }
            }, err => {
                this.$message.error(err.data.message);
            });
        },
        cancel(){
            window.parent.layer.closeAll();
        },
        // async initData() {
        //     await getExpressCommunicationId(expressId).then(res => {
        //         debugger
        //         this.inputs = res.data.data;
        //     })
        // }
    }
});

</script>