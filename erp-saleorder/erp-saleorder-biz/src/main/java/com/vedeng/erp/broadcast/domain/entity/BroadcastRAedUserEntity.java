package com.vedeng.erp.broadcast.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * AED区域经理与ERP用户映射表
 */
@Getter
@Setter
public class BroadcastRAedUserEntity extends BaseEntity {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * ERP系统中的用户ID
     */
    private Integer erpUserId;

    /**
     * 配置的AED区域经理对应的用户ID
     */
    private Integer aedUserId;

    /**
     * 是否已删除（0=否，1=是）
     */
    private Integer isDeleted;
}
