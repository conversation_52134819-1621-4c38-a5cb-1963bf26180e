package com.vedeng.erp.finance.mapper;

import com.vedeng.erp.finance.domain.entity.InvoiceFile;

public interface InvoiceFileMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(InvoiceFile record);

    int insertSelective(InvoiceFile record);

    InvoiceFile selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(InvoiceFile record);

    int updateByPrimaryKey(InvoiceFile record);

    InvoiceFile getInvoiceFileByInvoiceNo(String invoiceNo);
}