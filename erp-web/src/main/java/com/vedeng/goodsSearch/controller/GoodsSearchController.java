package com.vedeng.goodsSearch.controller;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.docSync.service.SyncGoodsService;
import com.vedeng.goodsSearch.model.dto.GoodsSearchInfoResDto;
import com.vedeng.goodsSearch.model.dto.GoodsSearchReqDto;
import com.vedeng.goodsSearch.model.dto.GoodsSearchResDto;
import com.vedeng.goodsSearch.service.GoodsSearchService;
import com.vedeng.price.api.price.dto.price.SkuPriceInfoDetailResponseDto;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 综合询价控制层
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/goodsInfo")
public class GoodsSearchController extends BaseController {

    public Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private GoodsSearchService goodsSearchService;

    @Autowired
    private SyncGoodsService syncGoodsService;

    /**
     * 商品信息搜索
     *
     * @param goodsSearchReq
     * @return
     */
    @ResponseBody
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/search")
    public ResultInfo goodsSearch(GoodsSearchReqDto goodsSearchReq) {

        logger.info("goodsInfoSearch goodsSearchReq:{}", JSON.toJSONString(goodsSearchReq));
        return goodsSearchService.goodsSearch(goodsSearchReq);
    }

    /**
     * 开放给lxcrm查询的feign接口
     * @param goodsSearchReq
     * @return
     */
    @ResponseBody
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/searchForApi")
    public ResultInfo goodsSearchForApi(@RequestBody  GoodsSearchReqDto goodsSearchReq) {

        ResultInfo result =goodsSearch(goodsSearchReq);
        GoodsSearchResDto goodsSearchResInfo = (GoodsSearchResDto)result.getData();
        Page page = result.getPage();
        List<GoodsSearchInfoResDto> goodsSearchInfoList = result.getListData();
        SearchResultDto searchResultDto = new SearchResultDto(goodsSearchResInfo,page,goodsSearchInfoList);
        result.setData(searchResultDto);
        return result;
    }

    static class SearchResultDto{
        private  GoodsSearchResDto goodsSearchResInfo;
        private Page page;
        private List<GoodsSearchInfoResDto> goodsSearchInfoList;

        public SearchResultDto(GoodsSearchResDto goodsSearchResInfo, Page page, List<GoodsSearchInfoResDto> goodsSearchInfoList) {
            this.goodsSearchResInfo = goodsSearchResInfo;
            this.page = page;
            this.goodsSearchInfoList = goodsSearchInfoList;
        }

        public GoodsSearchResDto getGoodsSearchResInfo() {
            return goodsSearchResInfo;
        }

        public void setGoodsSearchResInfo(GoodsSearchResDto goodsSearchResInfo) {
            this.goodsSearchResInfo = goodsSearchResInfo;
        }

        public Page getPage() {
            return page;
        }

        public void setPage(Page page) {
            this.page = page;
        }

        public List<GoodsSearchInfoResDto> getGoodsSearchInfoList() {
            return goodsSearchInfoList;
        }

        public void setGoodsSearchInfoList(List<GoodsSearchInfoResDto> goodsSearchInfoList) {
            this.goodsSearchInfoList = goodsSearchInfoList;
        }
    }

    /**
     * 上报商品信息搜索结果为空
     *
     * @param goodsSearchReq
     * @return
     */
    @ResponseBody
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/search/resultIsEmpty")
    public ResultInfo resultIsEmpty(GoodsSearchReqDto goodsSearchReq) {

        return ResultInfo.success();
    }

    /**
     * 综合询价搜索页面
     *
     * @return
     */
    @ResponseBody
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/search/goods")
    public ModelAndView goodsInfo(String keywords) {
        ModelAndView modelAndView = new ModelAndView("/goodsSearch/goods_search");
        modelAndView.addObject("keywords", keywords);
        return modelAndView;
    }

    /**
     * 商品信息推送
     *
     * @param skuIds
     * @return
     */
    @ResponseBody
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/goods/goodsSync")
    public ResultInfo goodsSync(@RequestBody List<Integer> skuIds) {
        syncGoodsService.syncSkuInfo2EsBySkuIds(skuIds);
        return ResultInfo.success();
    }

    @ResponseBody
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/price/skuIdList")
    public ResultInfo<Map<Long, SkuPriceInfoDetailResponseDto>> getPriceBySkuIdList(@RequestBody List<Integer> skuIds) {
        if (CollectionUtils.isNotEmpty(skuIds)) {
            return ResultInfo.success(goodsSearchService.getPriceBySkuIdList(skuIds));
        }
        return ResultInfo.success(new HashMap<>());
    }
}
