void function () {
    new Vue({
        el: '#page-container',
        data: {
            filterSortList: [{
                label: '创建时间倒序',
                order: '',
            }, {
                label: '计划拜访时间倒序',
                order: 'plan_visit_date desc',
            }, {
                label: '拜访完成时间倒序',
                order: 'complete_datetime desc',
            }],
            statusList: [{
                label: '待拜访',
                value: '1'
            }, {
                label: '拜访中',
                value: '2'
            }, {
                label: '已拜访',
                value: '3'
            }, {
                label: '已关闭',
                value: '4'
            }],
            visitTargetList: [
                {
                    label: '客情维护',
                    value: 'C'
                }, {
                    label: '新客开发',
                    value: 'A'
                }, {
                    label: '会员签约',
                    value: 'D'
                }, {
                    label: '产品推广',
                    value: 'E'
                }, {
                    label: '线索商机跟进',
                    value: 'B'
                }
            ],
            customerNatureList: [
                {
                    label: '渠道商',
                    value: 465
                }, {
                    label: '终端',
                    value: 466
                },
            ],
            searchParams: {
                customerName: '',
                visitRecordNo: '',
                visitRecordStatusList: [],
                visitorIdList: [],
                visitorFilterItems: [],
                tongxingIdList: [],
                tongxingFilterItems: [],
                creatorIdList: [],
                creatorFilterItems: [],
                customerNature: '',
                contactName: '',
                contactMobile: '',
                contactTele: '',
                visitTargetList: [],
                bussinessChanceNo: '',
                commucateContent: '',
                planVisitDate: [],
                planVisitDateStart: '',
                planVisitDateEnd: '',
                completeDateStart: '',
                completeDateEnd: '',
                addTime: [],
                addTimeStart: '',
                addTimeEnd: ''
            },
            visitorList: [],
            tongxingList: [],
            creatorList: [],
        },
        created() {
            let planVisitDateStart = GLOBAL.getQuery('planVisitDateStart');
            let planVisitDateEnd = GLOBAL.getQuery('planVisitDateEnd');

            if(planVisitDateStart && planVisitDateEnd) {
                this.searchParams.planVisitDateStart = planVisitDateStart;
                this.searchParams.planVisitDateEnd = planVisitDateEnd;
                this.searchParams.planVisitDate = [planVisitDateStart, planVisitDateEnd];
            } else {
                let endTime = moment(new Date()).format('YYYY-MM-DD');
                let startTime = moment(new Date().getTime() - 90 * 24 * 60 * 60 * 1000).format('YYYY-MM-DD');

                this.searchParams.addTimeEnd = endTime;
                this.searchParams.addTimeStart = startTime;
                this.searchParams.addTime = [startTime, endTime];
            }

            this.getListUsers();
        },
        mounted() {
           
        },
        methods: {
            getListUsers() {
                return this.$axios.post('/crm/visitrecord/m/pageDetail').then(({data}) => {
                    if(data.success) {
                        this.visitorList = this.parseList(data.data.visitUserList || []);
                        this.tongxingList = this.parseList(data.data.tongXingUserList || []);
                        this.creatorList = this.parseList(data.data.createUserList || []);
                    }
                })
            },
            parseList(list) {
                let resList = [];

                list.forEach(item => {
                    resList.push({
                        label: item.username,
                        value: item.userId,
                        avatar: item.aliasHeadPicture
                    })
                });

                return resList;
            },
            getAddressStr(item) {
                let areaStrs = [];

                if (item.provinceName) {
                    areaStrs.push(item.provinceName)
                }

                if (item.cityName) {
                    areaStrs.push(item.cityName)
                }

                if (item.areaName) {
                    areaStrs.push(item.areaName)
                }

                if (areaStrs.length) {
                    return areaStrs.join('-');
                } else {
                    return '';
                } 
            },
            handlerDateChange(key, value) {
                if(value && value.length) {
                    this.searchParams[key + 'Start'] = value[0] || '';
                    this.searchParams[key + 'End'] = value[1] || '';
                } else {
                    this.searchParams[key + 'Start'] = '';
                    this.searchParams[key + 'End'] = ''; 
                }
            },
            gotoDetail(id) {
                window.location.href = '/crm/visitRecord/m/detail?id=' + id;
            }
        }
    })
}.call(this);