<html>
<head>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="修改线下销售所负责的区域" scope="application"/>
<link href="/webjars/ezadmin/layui/css/ezform.css?v=1710377825918" rel="stylesheet">
<link href="/webjars/ezadmin/plugins/layui/css/layui.css?v=2.7.6" rel="stylesheet">
<link href="/webjars/ezadmin/plugins/cascader/cascader.css" rel="stylesheet">

<script src="/webjars/ezadmin/plugins/jquery/jquery.min.js"></script>

<link  href="/webjars/ezadmin/plugins/tooltips/css/tooltipster.bundle.min.css" rel="stylesheet" type="text/css"/>
<link  href="/webjars/ezadmin/plugins/tooltips/css/plugins/tooltipster/sideTip/themes/tooltipster-sideTip-shadow.min.css" rel="stylesheet" type="text/css"/>

<!--     <script th:src="${'/webjars/ezadmin/plugins/wangEditor/wangEditor.js'}"></script>-->
<script src="/webjars/ezadmin/plugins/moment/moment.min.js"  type="text/javascript"  ></script>

<%--<script src="/webjars/ezadmin/plugins/pingyin/pinyin_dict_firstletter.js" ></script>
<script src="/webjars/ezadmin/plugins/pingyin/pinyin_dict_notone.js" ></script>
<script src="/webjars/ezadmin/plugins/pingyin/pinyinUtil.js" ></script>--%>

<script src="/webjars/ezadmin/plugins/layui/layui.js?v=2.7.6" type="text/javascript" ></script>

    <script src="/static/erp/cascader.js" type="text/javascript" ></script>

<%--<script src="/webjars/ezadmin/plugins/cascader/cascader.js" type="text/javascript" ></script>--%>
<script src="/webjars/ezadmin/plugins/cascader/xm-select.js" type="text/javascript" ></script>
<script type="text/javascript" src="/webjars/ezadmin/plugins/tooltips/js/tooltipster.bundle.min.js"></script>

<script src="/static/erp/core.js" type="text/javascript" ></script>

<%--<script src="/webjars/ezadmin/layui/js/core.js?v=1710377825918" type="text/javascript" ></script>--%>
<script src="/webjars/ezadmin/plugins/jquery/jquery.form.js?" ></script>
<script src="/webjars/ezadmin/plugins/jquery-validation/jquery.validate.min.js" ></script>
<script src="/webjars/ezadmin/plugins/jquery-validation/localization/messages_zh.min.js" ></script>
<script src="/webjars/ezadmin/plugins/viewer/viewer.min.js" type="text/javascript"   ></script>
<%--<script type='text/javascript'  src='/webjars/ezadmin/plugins/dragula/dragula.min.js'></script>--%>

<script src="/webjars/ezadmin/plugins/tinymce/tinymce.min.js" type="text/javascript"   ></script>


<script src="/webjars/ezadmin/layui/js/ezform.js?v=1710377825918" type="text/javascript"  ></script>

<style type="text/css">
    .el-cascader{
        width: 400px !important;
    }
    .layui-elem-quote{
        color:red !important;
    }
</style>
</head>
<body>
<div class="layui-container">

    <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">

        <div class="layui-tab-content">
            <div class="layui-tab-item layui-show">

                <blockquote  class="layui-elem-quote layui-quote-nm">未选择销售时，则会清空该区域的销售人员</blockquote>

                <input type="hidden" id="FORM_ID"  name="FORM_ID" value="modifyVisitor">
                <div class="layui-row">
                    <div class="layui-col-md12">
                        <form id="inputForm"   method="post" class="  layui-form">


                            <div class="layui-card" id="EZ_DEFAULT_GROUP">

                                <!--block start-->
                                <div class="layui-card-body dragula-container"  >
                                    <div ITEM_NAME='BELONG_SALES_ID'
                                         editor="formitem" class="formitem layui-form-item" >
                                        <label class="layui-form-label" for="areaId"   >
                                                    <span class="layui-required"
                                                    >*</span>
                                            负责区域
                                        </label>
                                        <div class="layui-input-block">

                                            <input placeholder="区域加载完成后可选择" value="" name="areaId" lay-verify="required"  id="search-itemName-REGION" class="ez-laycascader" ez_url="/ezadmin/api/region.html" ez_value="regionId" ez_label="regionName" ez_children="child" autocomplete="off">
                                        </div>
                                    </div>
                                    <div ITEM_NAME='SALE_ON_LINE'
                                         editor="formitem" class="formitem layui-form-item" >
                                        <!--block start-->

                                        <label class="layui-form-label" for="sharedSale"   >
                                                    <span class="layui-required"
                                                    ></span>
                                            线上销售
                                        </label>
                                        <div class="layui-input-block">
                                            <div id="sharedSaleOn" style="width: 220px;" lay-verify="required"  ></div>

                                        </div>
                                    </div>
                                    <div ITEM_NAME='SALE_DOWN_LINE'
                                         editor="formitem" class="formitem layui-form-item" >
                                        <!--block start-->

                                        <label class="layui-form-label" for="sharedSale"   >
                                                    <span class="layui-required"
                                                    ></span>
                                            线下销售
                                        </label>
                                        <div class="layui-input-block">
                                            <div id="sharedSaleDown" style="width: 220px;" lay-verify="required"  ></div>

                                        </div>

                                    </div>


                                </div>

                            </div>

                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <input type="hidden" id="formSubmitUrl" value="/visitrecord/profile/saveCustomerRegionSale.do"  />
                                    <button id="submitbtn"  type="button" onclick="submitFormA();" class="layui-btn" >立即提交</button>
                                    <%--<button type="reset" class="layui-btn layui-btn-primary">重置</button>   --%>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div><!--end layui-tab-content-->
    </div>
</div>

<script type="text/javascript">
    var sharedSale = xmSelect.render({
        el: '#sharedSaleOn',
        style: {
            minHeight: '26px',
            height: '36px'
        },
        toolbar: {
            show: false,
            showIcon: false
        },
        size: 'medium',
        filterable: true,
        remoteSearch: true,
        prop: {
            value: 'userId',
            name: 'username'
        },
        theme:{
            color: '#409eff'
        },
        placeholder:'请选择',
        data: [],
        delay: 500,
        radio: true,
        clickClose: true,
        remoteMethod: function (val, cb, show) {
            if (!val) {
                return cb([]);
            }
            $.ajax({
                type: "POST",
                url: "/system/user/search.do?type=ALL",
                data: {'username': val},
                dataType: 'json',
                success: function (data) {
                    cb(data.data)
                }
            });
        }
    });

    var sharedSale = xmSelect.render({
        el: '#sharedSaleDown',
        style: {
            minHeight: '26px',
            height: '36px'
        },
        toolbar: {
            show: false,
            showIcon: false
        },
        size: 'medium',
        filterable: true,
        remoteSearch: true,
        prop: {
            value: 'userId',
            name: 'username'
        },
        theme:{
            color: '#409eff'
        },
        placeholder:'请选择',
        data: [],
        delay: 500,
        radio: true,
        clickClose: true,
        remoteMethod: function (val, cb, show) {
            if (!val) {
                return cb([]);
            }
            $.ajax({
                type: "POST",
                url: "/system/user/search.do?type=ALL",
                data: {'username': val},
                dataType: 'json',
                success: function (data) {
                    cb(data.data)
                }
            });
        }
    });
    function submitFormA() {
        var userIdOn= $("#sharedSaleOn").find("input[name='select']").val();
        var userIdDown= $("#sharedSaleDown").find("input[name='select']").val();
        var areaId = $("input[name='areaId']").val();
        if(userIdOn==null || userIdOn==''){
            userIdOn = 0;
            // layer.alert("请选择线上销售");
            // return;
        }
        if(userIdDown==null || userIdDown==''){
            userIdDown = 0;
            // layer.alert("请选择线下销售");
            // return;
        }
        if(areaId==null || areaId==''){
            layer.alert("请选择负责区域");
            return;
        }
        var middlePart = areaId.substring(1, areaId.length - 1);
        var jsonData = {"userIdOn":userIdOn,"userIdDown":userIdDown,"areaIds":middlePart.split(",")};
        if(userIdOn ==0 || userIdDown == 0 ){
            var index  = layer.confirm('未选择销售时，则会清空该区域的销售人员，是否确认？', {
                btn: [ '取消','确定清除'],
                title: '提示'
            }, function(){
                layer.close(index);
                // 用户点击取消按钮的回调函数
                //layer.msg('已取消操作', {icon: 2});
            }, function(){
                // 用户点击确定按钮的回调函数
                $.ajax({
                    url:$("#formSubmitUrl").val() ,
                    data:JSON.stringify(jsonData),
                    dataType:  'json',
                    method:'POST',
                    contentType: 'application/json',
                    success: function(data){
                        if(data.code==0){
                            console.log("data::"+data.data);
                            layer.alert("保存成功",  function(index){
                                if('reload'==data.data){
                                    window.parent.location.reload();
                                }
                                else{
                                    location.href=data.data;
                                }
                            })
                        }else{
                            layer.alert("保存失败"+data.message)
                        }
                    },
                    error:function (e){
                        layer.alert("操作失败"+e);
                    }
                });
            });
        }else{
            $.ajax({
                url:$("#formSubmitUrl").val() ,
                data:JSON.stringify(jsonData),
                dataType:  'json',
                method:'POST',
                contentType: 'application/json',
                success: function(data){
                    if(data.code==0){
                        console.log("data::"+data.data);
                        layer.alert("保存成功",  function(index){
                            if('reload'==data.data){
                                window.parent.location.reload();
                            }
                            else{
                                location.href=data.data;
                            }
                        })
                    }else{
                        layer.alert("保存失败"+data.message)
                    }
                },
                error:function (e){
                    layer.alert("操作失败"+e);
                }
            });
        }
    }
    // $(document).ready(function (){
    //     // $(".ez-laycascader").each(function(){
    //     //
    //     //     var _this=$(this);
    //     //     console.log(_this);
    //     //     renderCascader(_this);
    //     // })
    //     initForm();
    //
    // });


</script>
<%@ include file="../../common/footer.jsp" %>
