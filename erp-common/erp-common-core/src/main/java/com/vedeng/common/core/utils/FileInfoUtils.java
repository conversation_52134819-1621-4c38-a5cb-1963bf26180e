package com.vedeng.common.core.utils;

import cn.hutool.core.io.FileTypeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.vedeng.common.core.domain.FileInfoDto;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.nio.channels.Channels;
import java.nio.channels.ReadableByteChannel;
import java.util.Base64;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

/**
 * ERP文件处理工具类
 */
public class FileInfoUtils {

    public static final Integer CHUNK_SIZE  = 1024*1024*2;
    /**
     * <AUTHOR>
     * @desc 根据oss文件url获取传送给金蝶的base64的
     * @param fileUrl
     * @return
     */
    public static FileInfoDto getBase64FromUrl(String fileUrl){
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        HttpUtil.download(fileUrl, outputStream, true);
        byte[] fileByte = outputStream.toByteArray();
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(outputStream.toByteArray());
        String suffix = FileTypeUtil.getType(byteArrayInputStream);
        FileInfoDto fileInfoDto = new FileInfoDto();
        fileInfoDto.setFileBase64(Base64.getEncoder().encodeToString(fileByte));
        fileInfoDto.setSuffix("." + suffix);
        return fileInfoDto;
    }

    public static String getSuffix(String fileUrl) {
        byte[] bytes = HttpUtil.downloadBytes(fileUrl);
        InputStream inputStream = new ByteArrayInputStream(bytes);
        String type = FileTypeUtil.getType(inputStream);
        return Objects.isNull(type) ? ".pdf" : "." + type;
    }

    /**
     * 分片
     * @param fileUrl
     * @return
     * @throws IOException
     */
    public static List<String> getBase64ListFromUrl(String fileUrl) throws IOException {
        List<String> slice = new LinkedList<>();
        byte[] bytes = HttpUtil.downloadBytes(fileUrl);
        Base64.Encoder encoder = Base64.getEncoder(); // 实例化一次并复用
        try (InputStream inputStream = new ByteArrayInputStream(bytes); ReadableByteChannel rbc = Channels.newChannel(inputStream)){

            ByteBuffer buffer = ByteBuffer.allocate(CHUNK_SIZE);
            int bytesRead;

            while ((bytesRead = rbc.read(buffer)) != -1) {
                buffer.flip();
                byte[] chunkData = new byte[bytesRead];
                buffer.get(chunkData); // 使用ByteBuffer的get方法来获取缓冲区的数据
                String string = encoder.encodeToString(chunkData); // 使用复用的Base64.Encoder
                slice.add(string);
                buffer.clear();
            }
        }
        return slice;
    }


    public static String getOssResourceIdFromStr(String str) {
        if(StrUtil.isEmpty(str)){
            return null;
        }
        int index=str.lastIndexOf("=");
        if(index<=0){
            return null;
        }
        return str.substring(index+1);
    }
}
