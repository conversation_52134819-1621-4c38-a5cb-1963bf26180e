package com.vedeng.erp.kingdee.batch.repository;
import org.apache.ibatis.annotations.Param;
import java.util.List;

import com.vedeng.erp.kingdee.batch.dto.BatchVirtualInvoiceItemDto;


/**
 * @description ${end}
 * <AUTHOR>
 * @date 2023/11/27 9:33
 **/
public interface BatchVirtualInvoiceItemDtoMapper {
    int deleteByPrimaryKey(Integer virtualInvoiceItemId);

    int insert(BatchVirtualInvoiceItemDto record);

    int insertSelective(BatchVirtualInvoiceItemDto record);

    BatchVirtualInvoiceItemDto selectByPrimaryKey(Integer virtualInvoiceItemId);

    int updateByPrimaryKeySelective(BatchVirtualInvoiceItemDto record);

    int updateByPrimaryKey(BatchVirtualInvoiceItemDto record);

    List<BatchVirtualInvoiceItemDto> selectByBusinessOrderItemIds(@Param("list") List<Integer> businessOrderItemIds, @Param("colorType") Integer colorType);

    List<BatchVirtualInvoiceItemDto> selectBySourceInvoiceItemId(@Param("sourceInvoiceItemId")Integer sourceInvoiceItemId);

    List<BatchVirtualInvoiceItemDto> selectBySourceInvoiceItemIds(@Param("list")List<Integer> sourceInvoiceItemIds);

    List<BatchVirtualInvoiceItemDto> selectBindableInvoiceItem(Integer virtualInvoiceId);

    List<BatchVirtualInvoiceItemDto> selectByVirtualInvoiceId(@Param("virtualInvoiceId")Integer virtualInvoiceId);


    List<BatchVirtualInvoiceItemDto> selectByVirtualInvoiceItemIds(@Param("list")List<Integer> list);

    int deleteByVirtualInvoiceId(@Param("virtualInvoiceId")Integer virtualInvoiceId);










}