package com.wms.model.ddi.generate;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * T_DDI_BUYORDER
 * <AUTHOR>
@Data
public class DdiBuyorder implements Serializable {
    private Integer tDdiBuyorderId;

    /**
     * 经销商
     */
    private String distributor;

    /**
     * GE合同编号
     */
    private String quoteId;

    /**
     * GE销售订单单号
     */
    private String saleorderNo;

    /**
     * 入库日期
     */
    private Date storageTime;

    /**
     * 产品类型
     */
    private String goodsType;

    /**
     * 产品名称
     */
    private String goodsName;

    /**
     * 产品型号
     */
    private String goodsModel;

    /**
     * 注册证号
     */
    private String registrationNumber;

    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 产品有效期
     */
    private Date effectiveDays;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 单位
     */
    private String unit;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商代码
     */
    private Integer supplierId;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 入库类型
     */
    private String purchaseType;

    /**
     * 入库单号
     */
    private String inOrderNo;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 更新时间
     */
    private Date modeTime;

    /**
     * 是否删除 0否 1是
     */
    private Boolean isDelete;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新人
     */
    private Integer updater;

    private static final long serialVersionUID = 1L;
}