<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.settlement.mapper.PayApplyDetailMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.settlement.domain.entity.PayApplyDetailEntity">
    <!--@mbg.generated-->
    <!--@Table T_PAY_APPLY_DETAIL-->
    <id column="PAY_APPLY_DETAIL_ID" jdbcType="INTEGER" property="payApplyDetailId" />
    <result column="PAY_APPLY_ID" jdbcType="INTEGER" property="payApplyId" />
    <result column="DETAILGOODS_ID" jdbcType="INTEGER" property="detailgoodsId" />
    <result column="PRICE" jdbcType="DECIMAL" property="price" />
    <result column="NUM" jdbcType="DECIMAL" property="num" />
    <result column="TOTAL_AMOUNT" jdbcType="DECIMAL" property="totalAmount" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    PAY_APPLY_DETAIL_ID, PAY_APPLY_ID, DETAILGOODS_ID, PRICE, NUM, TOTAL_AMOUNT
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_PAY_APPLY_DETAIL
    where PAY_APPLY_DETAIL_ID = #{payApplyDetailId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_PAY_APPLY_DETAIL
    where PAY_APPLY_DETAIL_ID = #{payApplyDetailId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="PAY_APPLY_DETAIL_ID" keyProperty="payApplyDetailId" parameterType="com.vedeng.erp.settlement.domain.entity.PayApplyDetailEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_PAY_APPLY_DETAIL (PAY_APPLY_ID, DETAILGOODS_ID, PRICE, 
      NUM, TOTAL_AMOUNT)
    values (#{payApplyId,jdbcType=INTEGER}, #{detailgoodsId,jdbcType=INTEGER}, #{price,jdbcType=DECIMAL}, 
      #{num,jdbcType=DECIMAL}, #{totalAmount,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" keyColumn="PAY_APPLY_DETAIL_ID" keyProperty="payApplyDetailId" parameterType="com.vedeng.erp.settlement.domain.entity.PayApplyDetailEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_PAY_APPLY_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="payApplyId != null">
        PAY_APPLY_ID,
      </if>
      <if test="detailgoodsId != null">
        DETAILGOODS_ID,
      </if>
      <if test="price != null">
        PRICE,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="totalAmount != null">
        TOTAL_AMOUNT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="payApplyId != null">
        #{payApplyId,jdbcType=INTEGER},
      </if>
      <if test="detailgoodsId != null">
        #{detailgoodsId,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="num != null">
        #{num,jdbcType=DECIMAL},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.settlement.domain.entity.PayApplyDetailEntity">
    <!--@mbg.generated-->
    update T_PAY_APPLY_DETAIL
    <set>
      <if test="payApplyId != null">
        PAY_APPLY_ID = #{payApplyId,jdbcType=INTEGER},
      </if>
      <if test="detailgoodsId != null">
        DETAILGOODS_ID = #{detailgoodsId,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        PRICE = #{price,jdbcType=DECIMAL},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=DECIMAL},
      </if>
      <if test="totalAmount != null">
        TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
      </if>
    </set>
    where PAY_APPLY_DETAIL_ID = #{payApplyDetailId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.settlement.domain.entity.PayApplyDetailEntity">
    <!--@mbg.generated-->
    update T_PAY_APPLY_DETAIL
    set PAY_APPLY_ID = #{payApplyId,jdbcType=INTEGER},
      DETAILGOODS_ID = #{detailgoodsId,jdbcType=INTEGER},
      PRICE = #{price,jdbcType=DECIMAL},
      NUM = #{num,jdbcType=DECIMAL},
      TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL}
    where PAY_APPLY_DETAIL_ID = #{payApplyDetailId,jdbcType=INTEGER}
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_PAY_APPLY_DETAIL
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="PAY_APPLY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.payApplyId != null">
            when PAY_APPLY_DETAIL_ID = #{item.payApplyDetailId,jdbcType=INTEGER} then #{item.payApplyId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="DETAILGOODS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.detailgoodsId != null">
            when PAY_APPLY_DETAIL_ID = #{item.payApplyDetailId,jdbcType=INTEGER} then #{item.detailgoodsId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.price != null">
            when PAY_APPLY_DETAIL_ID = #{item.payApplyDetailId,jdbcType=INTEGER} then #{item.price,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.num != null">
            when PAY_APPLY_DETAIL_ID = #{item.payApplyDetailId,jdbcType=INTEGER} then #{item.num,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="TOTAL_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.totalAmount != null">
            when PAY_APPLY_DETAIL_ID = #{item.payApplyDetailId,jdbcType=INTEGER} then #{item.totalAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
    </trim>
    where PAY_APPLY_DETAIL_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.payApplyDetailId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="PAY_APPLY_DETAIL_ID" keyProperty="payApplyDetailId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_PAY_APPLY_DETAIL
    (PAY_APPLY_ID, DETAILGOODS_ID, PRICE, NUM, TOTAL_AMOUNT)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.payApplyId,jdbcType=INTEGER}, #{item.detailgoodsId,jdbcType=INTEGER}, #{item.price,jdbcType=DECIMAL}, 
        #{item.num,jdbcType=DECIMAL}, #{item.totalAmount,jdbcType=DECIMAL})
    </foreach>
  </insert>
</mapper>