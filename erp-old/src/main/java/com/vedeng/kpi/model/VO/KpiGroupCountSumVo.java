package com.vedeng.kpi.model.VO;

import java.math.BigDecimal;

public class KpiGroupCountSumVo {

    private static final long serialVersionUID = -4573659593114870971L;

    private BigDecimal amountTarget;

    private BigDecimal sumAmount;

    private BigDecimal amountProgress;

    private BigDecimal aveAmountScore;

    private Integer amountSort;

    private BigDecimal amountScore;

    private BigDecimal sumBdNewCustomerNum;

    private BigDecimal sumBdLostCustomerNum;

    private BigDecimal aveBdCustomerNum;

    private BigDecimal aveBdCustomerScore;

    private Integer bdCustomerSort;

    private BigDecimal bdCustomerScore;

    private BigDecimal sumCoCustomerNum;

    private BigDecimal sumLostCustomerNum;

    private BigDecimal aveCustomerNum;

    private BigDecimal aveCustomerScore;

    private Integer customerSort;

    private BigDecimal customerScore;

    private Integer sumChanceSuccessNum;

    private Integer sumChancefailNum;

    private BigDecimal aveTransProportion;

    private Integer chanceSort;

    private BigDecimal chanceScore;

    private BigDecimal aveChanceScore;

    private Integer integrateSort;

    private BigDecimal integrateScore;

    public BigDecimal getAmountTarget() {
        return amountTarget;
    }

    public void setAmountTarget(BigDecimal amountTarget) {
        this.amountTarget = amountTarget;
    }

    public BigDecimal getSumAmount() {
        return sumAmount;
    }

    public void setSumAmount(BigDecimal sumAmount) {
        this.sumAmount = sumAmount;
    }

    public BigDecimal getAmountProgress() {
        return amountProgress;
    }

    public void setAmountProgress(BigDecimal amountProgress) {
        this.amountProgress = amountProgress;
    }

    public Integer getAmountSort() {
        return amountSort;
    }

    public void setAmountSort(Integer amountSort) {
        this.amountSort = amountSort;
    }

    public BigDecimal getAmountScore() {
        return amountScore;
    }

    public void setAmountScore(BigDecimal amountScore) {
        this.amountScore = amountScore;
    }

    public BigDecimal getSumBdNewCustomerNum() {
        return sumBdNewCustomerNum;
    }

    public void setSumBdNewCustomerNum(BigDecimal sumBdNewCustomerNum) {
        this.sumBdNewCustomerNum = sumBdNewCustomerNum;
    }

    public BigDecimal getSumBdLostCustomerNum() {
        return sumBdLostCustomerNum;
    }

    public void setSumBdLostCustomerNum(BigDecimal sumBdLostCustomerNum) {
        this.sumBdLostCustomerNum = sumBdLostCustomerNum;
    }

    public BigDecimal getAveBdCustomerNum() {
        return aveBdCustomerNum;
    }

    public void setAveBdCustomerNum(BigDecimal aveBdCustomerNum) {
        this.aveBdCustomerNum = aveBdCustomerNum;
    }

    public Integer getBdCustomerSort() {
        return bdCustomerSort;
    }

    public void setBdCustomerSort(Integer bdCustomerSort) {
        this.bdCustomerSort = bdCustomerSort;
    }

    public BigDecimal getBdCustomerScore() {
        return bdCustomerScore;
    }

    public void setBdCustomerScore(BigDecimal bdCustomerScore) {
        this.bdCustomerScore = bdCustomerScore;
    }

    public BigDecimal getSumCoCustomerNum() {
        return sumCoCustomerNum;
    }

    public void setSumCoCustomerNum(BigDecimal sumCoCustomerNum) {
        this.sumCoCustomerNum = sumCoCustomerNum;
    }

    public BigDecimal getSumLostCustomerNum() {
        return sumLostCustomerNum;
    }

    public void setSumLostCustomerNum(BigDecimal sumLostCustomerNum) {
        this.sumLostCustomerNum = sumLostCustomerNum;
    }

    public BigDecimal getAveCustomerNum() {
        return aveCustomerNum;
    }

    public void setAveCustomerNum(BigDecimal aveCustomerNum) {
        this.aveCustomerNum = aveCustomerNum;
    }

    public Integer getCustomerSort() {
        return customerSort;
    }

    public void setCustomerSort(Integer customerSort) {
        this.customerSort = customerSort;
    }

    public BigDecimal getCustomerScore() {
        return customerScore;
    }

    public void setCustomerScore(BigDecimal customerScore) {
        this.customerScore = customerScore;
    }

    public Integer getSumChanceSuccessNum() {
        return sumChanceSuccessNum;
    }

    public void setSumChanceSuccessNum(Integer sumChanceSuccessNum) {
        this.sumChanceSuccessNum = sumChanceSuccessNum;
    }

    public Integer getSumChancefailNum() {
        return sumChancefailNum;
    }

    public void setSumChancefailNum(Integer sumChancefailNum) {
        this.sumChancefailNum = sumChancefailNum;
    }

    public BigDecimal getAveTransProportion() {
        return aveTransProportion;
    }

    public void setAveTransProportion(BigDecimal aveTransProportion) {
        this.aveTransProportion = aveTransProportion;
    }

    public Integer getChanceSort() {
        return chanceSort;
    }

    public void setChanceSort(Integer chanceSort) {
        this.chanceSort = chanceSort;
    }

    public BigDecimal getChanceScore() {
        return chanceScore;
    }

    public void setChanceScore(BigDecimal chanceScore) {
        this.chanceScore = chanceScore;
    }

    public Integer getIntegrateSort() {
        return integrateSort;
    }

    public void setIntegrateSort(Integer integrateSort) {
        this.integrateSort = integrateSort;
    }

    public BigDecimal getIntegrateScore() {
        return integrateScore;
    }

    public void setIntegrateScore(BigDecimal integrateScore) {
        this.integrateScore = integrateScore;
    }

    public BigDecimal getAveAmountScore() {
        return aveAmountScore;
    }

    public void setAveAmountScore(BigDecimal aveAmountScore) {
        this.aveAmountScore = aveAmountScore;
    }

    public BigDecimal getAveBdCustomerScore() {
        return aveBdCustomerScore;
    }

    public void setAveBdCustomerScore(BigDecimal aveBdCustomerScore) {
        this.aveBdCustomerScore = aveBdCustomerScore;
    }

    public BigDecimal getAveCustomerScore() {
        return aveCustomerScore;
    }

    public void setAveCustomerScore(BigDecimal aveCustomerScore) {
        this.aveCustomerScore = aveCustomerScore;
    }

    public BigDecimal getAveChanceScore() {
        return aveChanceScore;
    }

    public void setAveChanceScore(BigDecimal aveChanceScore) {
        this.aveChanceScore = aveChanceScore;
    }
}
