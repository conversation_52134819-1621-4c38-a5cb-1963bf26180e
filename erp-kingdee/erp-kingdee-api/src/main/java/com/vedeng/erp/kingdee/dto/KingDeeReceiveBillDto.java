package com.vedeng.erp.kingdee.dto;
import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.annotation.KingDeeID;
import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import lombok.*;

import java.util.List;

/**
 * 推送金蝶收款单dto
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@WriteBackField(needBackField = "FID")
public class KingDeeReceiveBillDto extends KingDeeMqBaseDto {

    private Integer kingDeeReceiveBillId;

    /**
     * 单据内码
     */
    @KingDeeID
    @WriteBackField(backWriteByErpField = "fid")
    private String fId;
    /**
     * 单据编号
     */
    @BusinessID
    private String fBillNo;
    /**
     * 单据类型
     */
    private String fBillTypeId;
    /**
     * 收款组织
     */
    private String fPayOrgId;
    /**
     * 单据日期
     */
    private String fDate;
    /**
     * 银行流水号
     */
    private String fQzokLsh;
    /**
     * 往来单位类型
     */
    private String fContactUnitType;
    /**
     * 往来单位
     */
    private String fContactUnit;
    /**
     * 交易主体
     */
    private String fQzokJyzt;
    /**
     * 交易类型
     */
    private String fQzokJylx;
    /**
     * 交易方式
     */
    private String fQzokJyfs;

    private Integer bankBillId;

    /**
     * "往来单位" 所选择资金流水的订单单号
     * T_CAPITAL_BILL_DETAIL.ORDER_NO（订单单号）
     */
    private String FQzokPzgsywdh;

    private List<KingDeeReceiveBillEntryDto> fReceiveBillEntry;
    @Override
    public String getFormId() {
        return KingDeeFormConstant.RECEIVE_BILL;
    }
    public KingDeeReceiveBillDto(){
        this.fPayOrgId = "101";
    }
}
