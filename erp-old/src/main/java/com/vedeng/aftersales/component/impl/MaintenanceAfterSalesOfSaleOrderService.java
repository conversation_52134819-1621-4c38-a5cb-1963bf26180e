package com.vedeng.aftersales.component.impl;

import com.vedeng.aftersales.component.AbstractAfterSalesOrderServiceOfSaleOrder;
import com.vedeng.aftersales.component.AfterSalesTypeEnum;
import com.vedeng.aftersales.component.dto.*;
import com.vedeng.aftersales.component.exception.AfterSaleDetailException;
import com.vedeng.aftersales.component.exception.AfterSaleValidateException;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesDetail;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.trader.dao.TraderAddressMapper;
import com.vedeng.trader.model.TraderAddress;
import com.wms.service.context.ThreadLocalContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Author: daniel
 * @Date: 2021/6/21 15 55
 * @Description: 销售订单维修类型售后单实现类
 */
@Service
public class MaintenanceAfterSalesOfSaleOrderService extends AbstractAfterSalesOrderServiceOfSaleOrder {

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Resource
    private TraderAddressMapper traderAddressMapper;

    @Override
    protected void customerAfterSaleOrderCreateValidate(Object addDto) throws AfterSaleValidateException {

        AfterSaleOrderAddDto afterSaleOrderAddDto = (AfterSaleOrderAddDto)addDto;

        Saleorder saleorder = saleorderMapper.getSaleorderByOrderNo(afterSaleOrderAddDto.getSaleOrderNo());
        if(StringUtil.isEmpty(afterSaleOrderAddDto.getContactProvince())||
                StringUtil.isEmpty(afterSaleOrderAddDto.getContactCity())||
                StringUtil.isEmpty(afterSaleOrderAddDto.getContactArea())){
            throw new AfterSaleValidateException("地区参数为空！");
        }
        if(StringUtil.isEmpty(afterSaleOrderAddDto.getContactAddress())||afterSaleOrderAddDto.getContactAddress().length()> 256){
            throw new AfterSaleValidateException("详细地址为必填且不大于256个字符！");
        }
        if(afterSaleOrderAddDto.getGoodsList().isEmpty()){
            throw new AfterSaleValidateException("所选择的售后商品为空");
        }
        for(AfterSaleOrderGoodAddDto baseAfterSalesGoodsInput:afterSaleOrderAddDto.getGoodsList()){
            SaleorderGoods saleorderGoods = saleorderGoodsMapper
                    .getSaleorderGoodsBySaleorderIdAndSkuNo(saleorder.getSaleorderId(),baseAfterSalesGoodsInput.getSkuNo());
           if(baseAfterSalesGoodsInput.getNum() < 1 || computeAlreadyDeliveryNum(saleorderGoods) < baseAfterSalesGoodsInput.getNum()){
                throw new AfterSaleValidateException("所选择的SKU:"+baseAfterSalesGoodsInput.getSkuNo()+"数量等于0或数量大于发货数量！");
            }
        }
    }


    @Override
    protected void customerPropertySet(AfterSaleOrderAddDto afterSaleOrderAddDto, AfterSales afterSales) {
        afterSales.setType(AfterSalesTypeEnum.MAINTENANCE_SALEORDER.getCode());
    }

    @Override
    protected void customerAfterSaleOrderClose(AfterSaleOrderCloseDto afterSaleOrderCloseDto) {

    }

    @Override
    protected <T extends AbstractAfterSaleOrderDto> void customerAddAfterSaleDetail(AfterSalesDetail afterSalesDetail, T afterSaleOrderAddDto) throws AfterSaleDetailException {
        Saleorder saleorder = ThreadLocalContext.get("saleorder");
        if (null == saleorder || null == saleorder.getTraderId()) {
            throw new AfterSaleDetailException("售后单关联销售单不存在！");
        }
        String areaIds = afterSaleOrderAddDto.getContactProvince()+","+afterSaleOrderAddDto.getContactCity()+","+afterSaleOrderAddDto.getContactArea();
        TraderAddress traderAddress = traderAddressMapper.getAddressInfoByAddress(saleorder.getTraderId(), CommonConstants.TRADER_TYPE_1, areaIds, Integer.parseInt(afterSaleOrderAddDto.getContactArea()), afterSaleOrderAddDto.getContactAddress());
        if (traderAddress == null) {
            traderAddress = new TraderAddress();
            traderAddress.setAreaId(Integer.valueOf(afterSaleOrderAddDto.getContactArea()));
            traderAddress.setTraderType(CommonConstants.TRADER_TYPE_1);
            traderAddress.setIsEnable(ErpConst.ONE);
            traderAddress.setAddress(afterSaleOrderAddDto.getContactAddress());
            traderAddress.setAreaIds(areaIds);
            traderAddress.setTraderId(saleorder.getTraderId());
            traderAddress.setAddTime(DateUtil.sysTimeMillis());
            traderAddress.setCreator(2);
            traderAddress.setUpdater(2);
            traderAddress.setModTime(DateUtil.sysTimeMillis());
            traderAddressMapper.insertSelective(traderAddress);
        }
        afterSalesDetail.setAddressId(traderAddress.getTraderAddressId());
        afterSalesDetail.setAreaId(traderAddress.getAreaId());
        afterSalesDetail.setAddress(traderAddress.getAddress());
    }

    @Override
    protected void customerAddAfterSaleGood(AfterSaleOrderAddDto afterSaleOrderAddDto) throws Exception{
        addServiceChargesAftersaleorderGoodCreate();
    }

    @Override
    protected void customeAddDealOfSaleOrder(Object addDto) {

    }

    @Override
    protected void customerAfterSaleOrderModifyValidate(Object modifyDto) throws AfterSaleValidateException {

    }
}
