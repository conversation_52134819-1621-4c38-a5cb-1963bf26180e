<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.repository.mappers.KingDeeSaleReturnStockMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.domain.entity.KingDeeSaleReturnStockEntity">
    <!--@mbg.generated-->
    <!--@Table KING_DEE_SALE_RETURN_STOCK-->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="FID" jdbcType="VARCHAR" property="fid" />
    <result column="F_BILL_TYPE_ID" jdbcType="VARCHAR" property="fBillTypeId" />
    <result column="F_BILL_NO" jdbcType="VARCHAR" property="fBillNo" />
    <result column="F_QZOK_BDDJTID" jdbcType="VARCHAR" property="fQzokBddjtid" />
    <result column="F_DATE" jdbcType="VARCHAR" property="fDate" />
    <result column="F_SALE_ORG_ID" jdbcType="VARCHAR" property="fSaleOrgId" />
    <result column="F_STOCK_ORG_ID" jdbcType="VARCHAR" property="fStockOrgId" />
    <result column="F_RETCUST_ID" jdbcType="VARCHAR" property="fRetcustId" />
    <result column="F_ENTITY" jdbcType="VARCHAR" property="fEntity" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, FID, F_BILL_TYPE_ID, 
    F_BILL_NO, F_QZOK_BDDJTID, F_DATE, F_SALE_ORG_ID, F_STOCK_ORG_ID, F_RETCUST_ID, F_ENTITY
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from KING_DEE_SALE_RETURN_STOCK
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from KING_DEE_SALE_RETURN_STOCK
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeSaleReturnStockEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_SALE_RETURN_STOCK (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      FID, F_BILL_TYPE_ID, F_BILL_NO, 
      F_QZOK_BDDJTID, F_DATE, F_SALE_ORG_ID, 
      F_STOCK_ORG_ID, F_RETCUST_ID, F_ENTITY
      )
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{fid,jdbcType=VARCHAR}, #{fBillTypeId,jdbcType=VARCHAR}, #{fBillNo,jdbcType=VARCHAR}, 
      #{fQzokBddjtid,jdbcType=VARCHAR}, #{fDate,jdbcType=VARCHAR}, #{fSaleOrgId,jdbcType=VARCHAR}, 
      #{fStockOrgId,jdbcType=VARCHAR}, #{fRetcustId,jdbcType=VARCHAR}, #{fEntity,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeSaleReturnStockEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_SALE_RETURN_STOCK
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="fid != null">
        FID,
      </if>
      <if test="fBillTypeId != null">
        F_BILL_TYPE_ID,
      </if>
      <if test="fBillNo != null">
        F_BILL_NO,
      </if>
      <if test="fQzokBddjtid != null">
        F_QZOK_BDDJTID,
      </if>
      <if test="fDate != null">
        F_DATE,
      </if>
      <if test="fSaleOrgId != null">
        F_SALE_ORG_ID,
      </if>
      <if test="fStockOrgId != null">
        F_STOCK_ORG_ID,
      </if>
      <if test="fRetcustId != null">
        F_RETCUST_ID,
      </if>
      <if test="fEntity != null">
        F_ENTITY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fid != null">
        #{fid,jdbcType=VARCHAR},
      </if>
      <if test="fBillTypeId != null">
        #{fBillTypeId,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null">
        #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBddjtid != null">
        #{fQzokBddjtid,jdbcType=VARCHAR},
      </if>
      <if test="fDate != null">
        #{fDate,jdbcType=VARCHAR},
      </if>
      <if test="fSaleOrgId != null">
        #{fSaleOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fStockOrgId != null">
        #{fStockOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fRetcustId != null">
        #{fRetcustId,jdbcType=VARCHAR},
      </if>
      <if test="fEntity != null">
        #{fEntity,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeSaleReturnStockEntity">
    <!--@mbg.generated-->
    update KING_DEE_SALE_RETURN_STOCK
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fid != null">
        FID = #{fid,jdbcType=VARCHAR},
      </if>
      <if test="fBillTypeId != null">
        F_BILL_TYPE_ID = #{fBillTypeId,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null">
        F_BILL_NO = #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBddjtid != null">
        F_QZOK_BDDJTID = #{fQzokBddjtid,jdbcType=VARCHAR},
      </if>
      <if test="fDate != null">
        F_DATE = #{fDate,jdbcType=VARCHAR},
      </if>
      <if test="fSaleOrgId != null">
        F_SALE_ORG_ID = #{fSaleOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fStockOrgId != null">
        F_STOCK_ORG_ID = #{fStockOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fRetcustId != null">
        F_RETCUST_ID = #{fRetcustId,jdbcType=VARCHAR},
      </if>
      <if test="fEntity != null">
        F_ENTITY = #{fEntity,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeSaleReturnStockEntity">
    <!--@mbg.generated-->
    update KING_DEE_SALE_RETURN_STOCK
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      FID = #{fid,jdbcType=VARCHAR},
      F_BILL_TYPE_ID = #{fBillTypeId,jdbcType=VARCHAR},
      F_BILL_NO = #{fBillNo,jdbcType=VARCHAR},
      F_QZOK_BDDJTID = #{fQzokBddjtid,jdbcType=VARCHAR},
      F_DATE = #{fDate,jdbcType=VARCHAR},
      F_SALE_ORG_ID = #{fSaleOrgId,jdbcType=VARCHAR},
      F_STOCK_ORG_ID = #{fStockOrgId,jdbcType=VARCHAR},
      F_RETCUST_ID = #{fRetcustId,jdbcType=VARCHAR},
      F_ENTITY = #{fEntity,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update KING_DEE_SALE_RETURN_STOCK
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="FID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fid,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_BILL_TYPE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fBillTypeId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_BILL_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fBillNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_BDDJTID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokBddjtid,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_DATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fDate,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_SALE_ORG_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fSaleOrgId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_STOCK_ORG_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fStockOrgId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_RETCUST_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fRetcustId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_ENTITY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fEntity,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update KING_DEE_SALE_RETURN_STOCK
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fid != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fid,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_BILL_TYPE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fBillTypeId != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fBillTypeId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_BILL_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fBillNo != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fBillNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_BDDJTID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokBddjtid != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokBddjtid,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_DATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fDate != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fDate,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_SALE_ORG_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fSaleOrgId != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fSaleOrgId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_STOCK_ORG_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fStockOrgId != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fStockOrgId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_RETCUST_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fRetcustId != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fRetcustId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_ENTITY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fEntity != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fEntity,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="ID" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_SALE_RETURN_STOCK
    (ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, FID, F_BILL_TYPE_ID, 
      F_BILL_NO, F_QZOK_BDDJTID, F_DATE, F_SALE_ORG_ID, F_STOCK_ORG_ID, F_RETCUST_ID, 
      F_ENTITY)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, 
        #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR}, 
        #{item.fid,jdbcType=VARCHAR}, #{item.fBillTypeId,jdbcType=VARCHAR}, #{item.fBillNo,jdbcType=VARCHAR}, 
        #{item.fQzokBddjtid,jdbcType=VARCHAR}, #{item.fDate,jdbcType=VARCHAR}, #{item.fSaleOrgId,jdbcType=VARCHAR}, 
        #{item.fStockOrgId,jdbcType=VARCHAR}, #{item.fRetcustId,jdbcType=VARCHAR}, #{item.fEntity,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeSaleReturnStockEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_SALE_RETURN_STOCK
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      ADD_TIME,
      MOD_TIME,
      CREATOR,
      UPDATER,
      CREATOR_NAME,
      UPDATER_NAME,
      FID,
      F_BILL_TYPE_ID,
      F_BILL_NO,
      F_QZOK_BDDJTID,
      F_DATE,
      F_SALE_ORG_ID,
      F_STOCK_ORG_ID,
      F_RETCUST_ID,
      F_ENTITY,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      #{addTime,jdbcType=TIMESTAMP},
      #{modTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=INTEGER},
      #{updater,jdbcType=INTEGER},
      #{creatorName,jdbcType=VARCHAR},
      #{updaterName,jdbcType=VARCHAR},
      #{fid,jdbcType=VARCHAR},
      #{fBillTypeId,jdbcType=VARCHAR},
      #{fBillNo,jdbcType=VARCHAR},
      #{fQzokBddjtid,jdbcType=VARCHAR},
      #{fDate,jdbcType=VARCHAR},
      #{fSaleOrgId,jdbcType=VARCHAR},
      #{fStockOrgId,jdbcType=VARCHAR},
      #{fRetcustId,jdbcType=VARCHAR},
      #{fEntity,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        ID = #{id,jdbcType=INTEGER},
      </if>
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      FID = #{fid,jdbcType=VARCHAR},
      F_BILL_TYPE_ID = #{fBillTypeId,jdbcType=VARCHAR},
      F_BILL_NO = #{fBillNo,jdbcType=VARCHAR},
      F_QZOK_BDDJTID = #{fQzokBddjtid,jdbcType=VARCHAR},
      F_DATE = #{fDate,jdbcType=VARCHAR},
      F_SALE_ORG_ID = #{fSaleOrgId,jdbcType=VARCHAR},
      F_STOCK_ORG_ID = #{fStockOrgId,jdbcType=VARCHAR},
      F_RETCUST_ID = #{fRetcustId,jdbcType=VARCHAR},
      F_ENTITY = #{fEntity,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeSaleReturnStockEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_SALE_RETURN_STOCK
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="fid != null">
        FID,
      </if>
      <if test="fBillTypeId != null">
        F_BILL_TYPE_ID,
      </if>
      <if test="fBillNo != null">
        F_BILL_NO,
      </if>
      <if test="fQzokBddjtid != null">
        F_QZOK_BDDJTID,
      </if>
      <if test="fDate != null">
        F_DATE,
      </if>
      <if test="fSaleOrgId != null">
        F_SALE_ORG_ID,
      </if>
      <if test="fStockOrgId != null">
        F_STOCK_ORG_ID,
      </if>
      <if test="fRetcustId != null">
        F_RETCUST_ID,
      </if>
      <if test="fEntity != null">
        F_ENTITY,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fid != null">
        #{fid,jdbcType=VARCHAR},
      </if>
      <if test="fBillTypeId != null">
        #{fBillTypeId,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null">
        #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBddjtid != null">
        #{fQzokBddjtid,jdbcType=VARCHAR},
      </if>
      <if test="fDate != null">
        #{fDate,jdbcType=VARCHAR},
      </if>
      <if test="fSaleOrgId != null">
        #{fSaleOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fStockOrgId != null">
        #{fStockOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fRetcustId != null">
        #{fRetcustId,jdbcType=VARCHAR},
      </if>
      <if test="fEntity != null">
        #{fEntity,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        ID = #{id,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fid != null">
        FID = #{fid,jdbcType=VARCHAR},
      </if>
      <if test="fBillTypeId != null">
        F_BILL_TYPE_ID = #{fBillTypeId,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null">
        F_BILL_NO = #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBddjtid != null">
        F_QZOK_BDDJTID = #{fQzokBddjtid,jdbcType=VARCHAR},
      </if>
      <if test="fDate != null">
        F_DATE = #{fDate,jdbcType=VARCHAR},
      </if>
      <if test="fSaleOrgId != null">
        F_SALE_ORG_ID = #{fSaleOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fStockOrgId != null">
        F_STOCK_ORG_ID = #{fStockOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fRetcustId != null">
        F_RETCUST_ID = #{fRetcustId,jdbcType=VARCHAR},
      </if>
      <if test="fEntity != null">
        F_ENTITY = #{fEntity,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>