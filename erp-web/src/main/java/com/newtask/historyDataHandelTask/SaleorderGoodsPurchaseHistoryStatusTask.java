package com.newtask.historyDataHandelTask;

import com.newtask.goods.DBTableType;
import com.newtask.goods.JdbcSupport;
import com.newtask.goods.builder.SaleorderGoodsBuyStatusSQLStatementBuilder;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.saleorder.constant.SalesOrderTypeEnum;
import com.vedeng.erp.saleorder.service.BaseSaleOrderService;
import com.vedeng.erp.saleorder.service.SaleOrderServiceFactory;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import com.vedeng.order.service.SaleorderService;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Component;

import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR> [<EMAIL>]
 */

@JobHandler(value = SaleorderGoodsPurchaseHistoryStatusTask.JOB_NAME)
@Component
@Deprecated
public class SaleorderGoodsPurchaseHistoryStatusTask extends JdbcSupport {

    static final String JOB_NAME = "SaleorderGoodsBuyStatusTask";

    private final static int RETRY_COUNT = 3;

    private final static int DEFAULT_ENTRY_SIZE_FOR_PER_TASK = 1000;

    private final static BeanPropertyRowMapper<SaleorderGoodsVo> GOOODS_ROW_MAPPER = new BeanPropertyRowMapper<>(SaleorderGoodsVo.class);

    private final AtomicLong refreshedGoodsCount = new AtomicLong(0);

    @Autowired
    private BaseSaleOrderService baseSaleOrderService;

    @SuppressWarnings("all")
    @Override
    protected void processData() {
        long startTimeMillis = System.currentTimeMillis();

        boolean successFlag = true;
        try {
            //更新商品采购状态
            batchUpdateAboutGoodsTables();
        } catch (Exception e) {
            successFlag = false;
            throw e;
        } finally {
            String logPrefix;
            if (successFlag) {
                logPrefix = "【商品采购状态】商品采购状态批量更新成功";
            } else {
                logPrefix = "【商品采购状态】商品采购状态更新部分成功或全部失败";
            }

            printUpdatedStatisticalLog(logPrefix, TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis() - startTimeMillis));

            //reset each counter
            refreshedGoodsCount.set(0);

            //shutdown thread pool if all task work successfully.
            if (successFlag) {
                executorService.shutdown();
            }
        }
    }

    @Override
    protected String getJobName() {
        return JOB_NAME;
    }

    //~================================================================================= Private methods


    private void printUpdatedStatisticalLog(String prefix, long timeInSecond) {
        logger.info(prefix + "- 商品信息更新数量: {},耗时: {}", refreshedGoodsCount.get(), timeInSecond);
    }


    @SuppressWarnings("all")
    private String createGoodsUpdatedSql(DBTableType targetTable, Integer status,Integer buyNum, Integer primaryKey) {
        if (targetTable == null || status == null || buyNum == null || primaryKey == null) {
            throw new IllegalArgumentException("Argument is null");
        }

        if (targetTable == DBTableType.SALEORDER_GOODS) {
            return SaleorderGoodsBuyStatusSQLStatementBuilder.builder()
                    .setTable(targetTable.getTableName())
                    .setPrimaryKeyName(targetTable.getPrimaryKeyName())
                    .setKeyInInteger(primaryKey)
                    .setPurchaseStatusData(status)
                    .setBuyNumData(buyNum)
                    .build();
        } else {
            throw new UnsupportedOperationException("目前暂时支持此数据表【" + targetTable.getTableName() + "】存储条件的更新");
        }
    }


    private int countTableTotalEntrySelf() {
        return jdbcTemplate.queryForObject("SELECT count(*) FROM\n" +
                "    T_SALEORDER s\n" +
                "    INNER JOIN T_SALEORDER_GOODS sg ON s.SALEORDER_ID = sg.SALEORDER_ID AND sg.IS_DELETE =0\n" +
                "    WHERE COMPANY_ID =1 AND ORDER_TYPE IN (0,1,5,7,8,9) AND `STATUS` != 3 AND  PURCHASE_STATUS != 2 ", Integer.class);
    }


    @SuppressWarnings("all")
    private void batchUpdateAboutGoodsTables() {
        int totalEntryCount = countTableTotalEntrySelf();
        if (totalEntryCount <= 0) {
            return;
        }

        int taskCount = determineTaskCount(totalEntryCount);

        final CountDownLatch countDownLatch = new CountDownLatch(taskCount);

        //更新商品采购状态
        int offset = 0;
        for (int i = 0; i < taskCount; i++) {
            for (int count = 1; count <= RETRY_COUNT; count++) {
                try {
                    executorService.execute(new GoodsSQLOperationTask(offset, DEFAULT_ENTRY_SIZE_FOR_PER_TASK, countDownLatch));
                } catch (RejectedExecutionException e) {
                    logger.info("【商品采购状态历史记录】批量更新商品采购状态历史记录失败，稍后准备重试 - retryCount:{}", count);
                    continue;
                }

                break;
            }

            offset += DEFAULT_ENTRY_SIZE_FOR_PER_TASK;
        }

        try {
            //wait until all sub task finished
            countDownLatch.await(3, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            //skip
        }
    }


    private int determineTaskCount(int totalEntryCount) {
        return (int) Math.ceil(totalEntryCount / (DEFAULT_ENTRY_SIZE_FOR_PER_TASK * 1.0));
    }

    //~================================================================================= Inner class

    public final class GoodsSQLOperationTask extends BaseSQLOperationTask {

        GoodsSQLOperationTask(int offset, int size, CountDownLatch countDownLatch) {
            super(offset, size, countDownLatch);
        }

        @Override
        protected void doBatchUpdate() {
            final List<String> skuListToUpdate = new LinkedList<>();

            List<SaleorderGoodsVo> dataQuerySources = jdbcTemplate.query(" SELECT SALEORDER_GOODS_ID,NUM,s.IS_NEW,s.SALEORDER_ID  FROM\n" +
                    "    T_SALEORDER s\n" +
                    "    INNER JOIN T_SALEORDER_GOODS sg ON s.SALEORDER_ID = sg.SALEORDER_ID AND sg.IS_DELETE =0\n" +
                    "    WHERE COMPANY_ID =1 AND `STATUS` != 3 AND ORDER_TYPE IN (0,1,5,7,8,9) AND  PURCHASE_STATUS != 2 " + " limit " + offset + "," + size, GOOODS_ROW_MAPPER);

            List<SaleorderGoods> saleorderGoodsList = baseSaleOrderService.calculateGoodsBuyStatus(dataQuerySources);

            if (CollectionUtils.isNotEmpty(saleorderGoodsList)) {
                for (SaleorderGoods saleorderGoods : saleorderGoodsList) {

                    skuListToUpdate.add(createGoodsUpdatedSql(DBTableType.SALEORDER_GOODS, saleorderGoods.getPurchaseStatusData(),saleorderGoods.getBuyNum(), saleorderGoods.getSaleorderGoodsId()));
                }
                //finally batch update
                int effectiveGoodsTableRows = doBatchUpdateWithSqlList(skuListToUpdate);
                refreshedGoodsCount.addAndGet(effectiveGoodsTableRows);

                logger.info("【商品采购状态】批量更新商品采购状态历史记录信息 - updatedGoodsCount: {}", effectiveGoodsTableRows);

            }
        }

    }


}
