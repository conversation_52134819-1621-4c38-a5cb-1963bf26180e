package com.vedeng.crm.visitrecord.mapper;

import com.vedeng.crm.visitrecord.domain.vo.VisitTongxingUserVo;
import org.apache.ibatis.annotations.Param;
import java.util.List;

public interface VisitTongxingUserMapper {
    
    /**
     * 根据拜访记录ID查询同行人列表
     */
    List<VisitTongxingUserVo> selectTongxingUserByRecordId(@Param("recordId") Integer recordId);


    /**
     * 查询拜访记录的同行人列表
     */
    List<VisitTongxingUserVo> selectByVisitRecordId(@Param("recordId") Integer recordId);

    /**
     * 批量插入同行人
     */
    int batchInsert(@Param("list") List<VisitTongxingUserVo> list);

    /**
     * 删除不在列表中的同行人
     */
    int deleteNotInList(@Param("recordId") Integer recordId, @Param("tongxingUserIds") List<Integer> tongxingUserIds);

    int deleteByRecordId(@Param("recordId") Integer recordId);
} 