package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.date.DateUtil;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchCapitalBillDto;
import com.vedeng.erp.kingdee.batch.processor.BuyOrderAfterSaleBalanceProcessor;
import com.vedeng.erp.kingdee.batch.writer.BuyOrderAfterSaleBalanceWrite;
import com.vedeng.erp.kingdee.dto.KingDeeNeedPayDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Description 供应商余额收款对接Job
 * <p>
 * （采购售后收款 | 应付余额调整单，调整金额为负向）
 * @Date 2023/02/23 11:00
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class PayBuyOrderAfterSaleBalanceBatchJob extends BaseJob {

    @Autowired
    private BuyOrderAfterSaleBalanceProcessor buyOrderAfterSaleBalanceProcessor;
    @Autowired
    private BuyOrderAfterSaleBalanceWrite buyOrderAfterSaleBalanceWrite;



    public Job buyOrderAfterSaleBalanceFlowJob() {
        return jobBuilderFactory.get("buyOrderAfterSaleBalanceBatchJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(buyOrderAfterSaleBalanceBatchStep())
                .next(buyOrderSettleAfterSaleBalanceBatchStep())
                .build();
    }

    private Step buyOrderAfterSaleBalanceBatchStep() {
        return stepBuilderFactory.get("余额-采购售后收款流水推送")
                .<BatchCapitalBillDto, KingDeeNeedPayDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(buyOrderAfterSaleBalanceReader(null, null))
                .processor(buyOrderAfterSaleBalanceProcessor)
                .writer(buyOrderAfterSaleBalanceWrite)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

    private Step buyOrderSettleAfterSaleBalanceBatchStep() {
        return stepBuilderFactory.get("余额-采购结算收款流水推送")
                .<BatchCapitalBillDto, KingDeeNeedPayDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(buyOrderSettleAfterSaleBalanceReader(null, null))
                .processor(buyOrderAfterSaleBalanceProcessor)
                .writer(buyOrderAfterSaleBalanceWrite)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 1.退还至供应商账户
     * dbcenter:com.vedeng.service.aftersales.impl.AfterSalesMoneyServiceImpl#accountBalancesCapital
     * 2.费用退还至供应商账户
     * 同上
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchCapitalBillDto> buyOrderAfterSaleBalanceReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                       @Value("#{jobParameters['endTime']}") String endTime) {
        BatchCapitalBillDto batchCapitalBillDto = new BatchCapitalBillDto();
        batchCapitalBillDto.setBeginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(beginTime).getTime());
        batchCapitalBillDto.setEndTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(endTime).getTime());
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchCapitalBillDto.class.getSimpleName(), "buyOrderAfterSaleBalanceReader", batchCapitalBillDto);
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchCapitalBillDto> buyOrderSettleAfterSaleBalanceReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                       @Value("#{jobParameters['endTime']}") String endTime) {
        BatchCapitalBillDto batchCapitalBillDto = new BatchCapitalBillDto();
        batchCapitalBillDto.setBeginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(beginTime).getTime());
        batchCapitalBillDto.setEndTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(endTime).getTime());
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchCapitalBillDto.class.getSimpleName(), "buyOrderSettleAfterSaleBalanceReader", batchCapitalBillDto);
    }
}
