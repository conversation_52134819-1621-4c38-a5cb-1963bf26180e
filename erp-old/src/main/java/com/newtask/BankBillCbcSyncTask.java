package com.newtask;


import com.fasterxml.jackson.core.type.TypeReference;
import com.vedeng.billsync.task.service.BankBillDataSyncService;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.finance.model.BankBill;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 *  @Description  定时任务  建行同步到银行流水
 */
@Component
@JobHandler(value = "BankBillCbcSyncTask")
public class BankBillCbcSyncTask extends AbstractJobHandler {


    @Autowired
    BankBillDataSyncService bankBillDataSyncService;
    @Value("${http_url}")
    protected String httpUrl;

    @Value("${client_id}")
    protected String clientId;

    @Value("${client_key}")
    protected String clientKey;
    private static final Logger LOGGER  = LoggerFactory.getLogger(BankBillCbcSyncTask.class);

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        LOGGER.info("开始执行建设银行同步数据到银行流水表任务,时间:{}",param);
        // 调用银行流水列表
        String url = httpUrl + "finance/bankbill/getbankbillmatchlistpage.htm";
        BankBill bankBill=new BankBill();
        bankBill.setIsExport(2);
        bankBill.setCompanyId(1);
        final TypeReference<ResultInfo<List<BankBill>>> TypeRef = new TypeReference<ResultInfo<List<BankBill>>>() {
        };
        try {
            // 获取匹配过后的银行流水列表
           HttpClientUtils.post(url, bankBill, clientId, clientKey, TypeRef,
                    Page.newBuilder(1, 1, "/"));
        }catch (Exception e){
            LOGGER.error("");
        }
        return SUCCESS;
    }
}
