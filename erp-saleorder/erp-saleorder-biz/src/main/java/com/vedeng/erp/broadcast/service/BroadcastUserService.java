package com.vedeng.erp.broadcast.service;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.broadcast.domain.dto.BroadCastUserDetailDto;
import com.vedeng.erp.broadcast.domain.dto.BroadCastUserListDto;
import com.vedeng.erp.broadcast.domain.dto.BroadCastUserQueryDto;
import com.vedeng.erp.broadcast.domain.dto.BroadCastUserFormDto;

/**
 * 播报用户管理服务接口
 * 负责处理播报用户的增删改查等业务逻辑
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
public interface BroadcastUserService {
    
    /**
     * 分页查询播报用户列表
     * 支持用户名模糊查询和AED销售筛选
     * 
     * @param queryDto 查询条件DTO，包含分页参数和筛选条件
     * @return 分页播报用户列表，包含总数、页码等分页信息
     */
    PageInfo<BroadCastUserListDto> getBroadcastUserListPage(PageParam<BroadCastUserQueryDto> queryDto);
    
    /**
     * 获取全量AED销售列表
     * 用于下拉选择框等场景
     * 
     * @param username 可选的用户名搜索参数，支持模糊查询
     * @return AED销售列表
     */
    List<BroadCastUserDetailDto.BroadCastAedUserDto> getAllAedUserList(String username);
    
    /**
     * 保存播报用户关联关系
     * 新增播报用户与AED销售的关联关系
     * 
     * @param formDto 播报用户表单数据
     */
    void saveBroadcastUser(BroadCastUserFormDto formDto);
    
    /**
     * 更新播报用户关联关系
     * 编辑已存在的播报用户与AED销售的关联关系
     * 
     * @param formDto 播报用户表单数据，必须包含ID
     */
    void updateBroadcastUser(BroadCastUserFormDto formDto);
    
    /**
     * 删除播报用户关联关系
     * 逻辑删除播报用户与AED销售的关联关系
     * 
     * @param id 播报用户关联关系ID
     */
    void deleteBroadcastUser(Integer id);
} 