Vue.component('ui-more-contact', {
    template: `
        <div class="more-contact-component">
            <div class="contact-list">
                <div class="contact-item" v-for="(item, index) in list" :key="item.id + '' + index">
                    <ui-form-item :label="item.label" style="margin-bottom: 20px;">
                        <ui-input 
                            width="323px" maxlength="50"
                            v-model="item.value"
                            @input="onInput"
                        ></ui-input>
                    </ui-form-item>
                    <div class="delete" @click="handlerDel(index)">
                        <i class="vd-ui_icon icon-delete"></i>
                    </div>
                </div>
            </div>

            <div class="add-contact" v-if="list.length < size">
                <a @click="increase"><i class="vd-ui_icon icon-add"></i>添加其他联系方式（{{list.length}}/{{size}}）</a>
            </div>

            <ui-dialog
                :visible.sync="rotate"
                title="添加联系方式"
                width="720px"
            >
                <div v-if="rotate" class="form-wrap label-width-2">
                    <ui-form-item label="联系方式" :must="true" :text="true">
                        <div class="ui-col-8">
                            <ui-radio-group
                                :list="Radios"
                                :value.sync="radioVal"
                            ></ui-radio-group>
                        </div>
                        <div v-if="radioVal == 3" class="other">
                            <ui-input 
                                width="170px" 
                                maxlength="7"
                                v-model="inputKey"
                                :placeholder="placeholder"
                                :errorable="Boolean(inputError)" 
                                :error-msg="inputError" 
                                @blur="onBlur"
                            ></ui-input>
                            <p class="tips">- 建议名称设置简约、直接、准确，最多可输入7个字</p>
                        </div>
                    </ui-form-item>
                </div>
                <template slot="footer">
                    <div class="dlg-form-footer">
                        <ui-button @click="confirm" type="primary">确定</ui-button>
                        <ui-button @click="rotate=false" class="close">取消</ui-button>
                    </div>
                </template>
            </ui-dialog>
        </div>
    `,
    props: {
        value: {
            type: String,
            default: ''
        },
        width: {
            type: String,
            default: '300px'
        },
        size: {
            type: Number,
            default: 5
        },
        placeholder: {
            type: String
        }
    },
    data () {
        return {
            rotate: false,
            list: [],
            Radios: [
                { label: "微信", value: 1 },
                { label: "QQ", value: 2 },
                { label: "其他联系方式", value: 3 },
            ],
            radioVal: '', // 选项值
            inputKey: '',
            inputError: '',
            Labels: {
                1: '微信',
                2: 'QQ',
                3: '其他'
            }
        }
    },
    watch: {
        rotate (newV) {
            if (!newV) {
                this.radioVal = '';
                this.inputKey = '';
            }
        },
    },
    computed: {},
    mounted () {
        if (this.value) {
            this.initList();
        }
    },
    methods: {
        // 初始化
        initList() {
            let vals = this.value.split('##');
            let arr = [];
            vals.forEach(item => {
                let str = item.split(':');
                let label = str[0];
                str.splice(0,1);
                arr.push({
                    label,
                    value: str.join(':'),
                    id: Math.random()
                })
            })
            this.list = arr;
        },

        increase () {
            if (this.list.length < this.size) {
                this.inputKey = ''
                this.inputError = ''
                this.rotate = true;
            }
        },
        // 新加
        add () {
            let label = this.Labels[this.radioVal];
            this.radioVal == 3 && (label = this.inputKey.trim());
            this.list.push({
                label: label,
                value: '',
                id: Math.random()
            })

            this.rotate = false;
        },
        // 删除
        handlerDel(index) {
            console.log('index:::', index);
            this.list.splice(index, 1);
            this.pick();
        },
        onInput () {
            this.pick();
        },
        onBlur () {
            if (this.inputKey.trim()) {
                this.inputError = '';
            }
        },
        // 保存
        confirm () {
            if (!this.radioVal) return;
            if (this.radioVal == 3) {
                if (!this.inputKey.trim()) {
                    this.inputError = '请输入联系方式名称';
                    return;
                } else {
                    this.inputError = '';
                    this.add(this.radioVal);
                }
            } else {
                this.add();
            }
        },
        pick () {
            let arr = [];
            this.list.forEach(item => {
                if (item.value.trim()) {
                    arr.push(`${item.label}:${item.value.trim()}`);
                }
            });
            let str = arr.join('##');
            this.$emit("input", str); // 修改外层v-model值
            this.$emit('change', arr)
        },
    }
})