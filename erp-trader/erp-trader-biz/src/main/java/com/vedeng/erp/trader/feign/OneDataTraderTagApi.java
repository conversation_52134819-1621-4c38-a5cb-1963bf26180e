package com.vedeng.erp.trader.feign;

import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.onedataapi.api.tradertag.TraderTagApi;
import com.vedeng.onedataapi.api.tradertag.req.TraderTagApiReq;
import com.vedeng.onedataapi.api.tradertag.res.*;
import feign.Headers;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.trader.feign
 * @Date 2023/8/9 20:04
 */
@FeignApi(serverName = "onedataapi")
public interface OneDataTraderTagApi extends TraderTagApi {

    /**
     * 获取客户交易标签
     *
     * @param traderTagApiReq 客户ID
     * @return 客户交易标签
     */
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /trader/tag/transaction")
    @Override
    RestfulResult<TraderTransactionTag> getTraderTransactionTag(@RequestBody TraderTagApiReq traderTagApiReq);

    /**
     * 获取客户决策标签
     *
     * @param traderTagApiReq 客户ID
     * @return 客户决策标签
     */
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /trader/tag/decision")
    @Override
    RestfulResult<TraderDecisionTag> getTraderDecisionTag(@RequestBody TraderTagApiReq traderTagApiReq);

    /**
     * 获取客户询价标签
     *
     * @param traderTagApiReq 客户ID
     * @return 客户询价标签
     */
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /trader/tag/inquiry")
    @Override
    RestfulResult<TraderInquiryTag> getTraderInquiryTag(@RequestBody TraderTagApiReq traderTagApiReq);

    /**
     * 获取客户消费行为标签
     *
     * @param traderTagApiReq 客户ID
     * @return 客户消费行为标签
     */
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /trader/tag/saleAction")
    @Override
    RestfulResult<TraderSaleActionTag> getTraderSaleActionTag(@RequestBody TraderTagApiReq traderTagApiReq);

    /**
     * 获取经营终端标签顺序列表
     *
     * @return 经营终端标签顺序列表
     */
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /trader/tag/terminal/order")
    @Override
    RestfulResult<List<TraderBusinessTerminalTag>> getTraderBusinessTerminalLabelOrder();

    /**
     * cpm标签完善度系统设计
     *
     * @param var1 traderId
     * @return TraderPerfectTag
     */
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /trader/tag/perfect")
    @Override
    RestfulResult<TraderPerfectTag> getTraderCustomerPerfectTag(@RequestBody TraderTagApiReq var1);

}
