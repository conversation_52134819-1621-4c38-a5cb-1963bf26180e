package com.vedeng.finance.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import javax.swing.*;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;

/**
 * <AUTHOR>
 * @date 2020/08/08
 **/
public class WatermarkUtil {

    private static final Logger logger = LoggerFactory.getLogger(WatermarkUtil.class);

    public static OutputStream addWatermark(InputStream inputStream, String waterMarkContent, String fileExt){
        Font font = new Font("宋体", Font.BOLD, 40);//水印字体，大小
        Color markContentColor = Color.gray;//水印颜色
        int degree = 45;//设置水印文字的旋转角度
        float alpha = 0.5f;//设置水印透明度
        OutputStream outImgStream = new ByteArrayOutputStream();
        try {
            Image srcImg = ImageIO.read(inputStream);//文件转化为图片
            int srcImgWidth = srcImg.getWidth(null);//获取图片的宽
            int srcImgHeight = srcImg.getHeight(null);//获取图片的高
            // 加水印
            BufferedImage bufImg = new BufferedImage(srcImgWidth, srcImgHeight, BufferedImage.TYPE_INT_RGB);
            Graphics2D g = bufImg.createGraphics();//得到画笔
            g.drawImage(srcImg, 0, 0, srcImgWidth, srcImgHeight, null);
            g.setColor(markContentColor); //设置水印颜色
            g.setFont(font);              //设置字体
            g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP, alpha));//设置水印文字透明度
            g.rotate(Math.toRadians(degree));//设置水印旋转
            JLabel label = new JLabel(waterMarkContent);
            FontMetrics metrics = label.getFontMetrics(font);
            int width = metrics.stringWidth(label.getText());//文字水印的宽
            // 图片的高  除以  文字水印的宽    ——> 打印的行数(以文字水印的宽为间隔)
            int rowsNumber = srcImgHeight/width;
            //图片的宽 除以 文字水印的宽   ——> 每行打印的列数(以文字水印的宽为间隔)
            int columnsNumber = srcImgWidth/width;
            //防止图片太小而文字水印太长，所以至少打印一次
            if(rowsNumber < 1){
                rowsNumber = 1;
            }
            if(columnsNumber < 1){
                columnsNumber = 1;
            }
            for(int j=0;j<rowsNumber;j++){
                for(int i=0;i<columnsNumber;i++){
                    g.drawString(waterMarkContent, i*width + j*width, -i*width + j*width);//画出水印,并设置水印位置
                }
            }
            g.dispose();
            ImageIO.write(bufImg, fileExt, outImgStream);
            return outImgStream;
        } catch (Exception e) {
            logger.error("发票添加水印发生错误，错误信息：",e);
        } finally{
            try {
                outImgStream.flush();
                outImgStream.close();
            } catch (Exception e) {
                logger.error("发票添加水印，关闭输出流错误，错误信息：",e);
            }
        }
        return null;
    }
}
