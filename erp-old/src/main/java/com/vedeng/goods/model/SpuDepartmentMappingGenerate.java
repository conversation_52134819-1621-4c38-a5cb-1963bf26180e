package com.vedeng.goods.model;

import java.util.Date;

public class SpuDepartmentMappingGenerate {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_SPU_DEPARTMENT_MAPPING.SPU_DEPARTMENT_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer spuDepartmentId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_SPU_DEPARTMENT_MAPPING.SPU_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer spuId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_SPU_DEPARTMENT_MAPPING.DEPARTMENT_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer departmentId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_SPU_DEPARTMENT_MAPPING.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Date addTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_SPU_DEPARTMENT_MAPPING.CREATOR
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_SPU_DEPARTMENT_MAPPING.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Date modTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_SPU_DEPARTMENT_MAPPING.UPDATER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer updater;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_SPU_DEPARTMENT_MAPPING.STATUS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer status;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_SPU_DEPARTMENT_MAPPING.SPU_DEPARTMENT_ID
     *
     * @return the value of V_SPU_DEPARTMENT_MAPPING.SPU_DEPARTMENT_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getSpuDepartmentId() {
        return spuDepartmentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_SPU_DEPARTMENT_MAPPING.SPU_DEPARTMENT_ID
     *
     * @param spuDepartmentId the value for V_SPU_DEPARTMENT_MAPPING.SPU_DEPARTMENT_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setSpuDepartmentId(Integer spuDepartmentId) {
        this.spuDepartmentId = spuDepartmentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_SPU_DEPARTMENT_MAPPING.SPU_ID
     *
     * @return the value of V_SPU_DEPARTMENT_MAPPING.SPU_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getSpuId() {
        return spuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_SPU_DEPARTMENT_MAPPING.SPU_ID
     *
     * @param spuId the value for V_SPU_DEPARTMENT_MAPPING.SPU_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setSpuId(Integer spuId) {
        this.spuId = spuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_SPU_DEPARTMENT_MAPPING.DEPARTMENT_ID
     *
     * @return the value of V_SPU_DEPARTMENT_MAPPING.DEPARTMENT_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getDepartmentId() {
        return departmentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_SPU_DEPARTMENT_MAPPING.DEPARTMENT_ID
     *
     * @param departmentId the value for V_SPU_DEPARTMENT_MAPPING.DEPARTMENT_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_SPU_DEPARTMENT_MAPPING.ADD_TIME
     *
     * @return the value of V_SPU_DEPARTMENT_MAPPING.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Date getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_SPU_DEPARTMENT_MAPPING.ADD_TIME
     *
     * @param addTime the value for V_SPU_DEPARTMENT_MAPPING.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_SPU_DEPARTMENT_MAPPING.CREATOR
     *
     * @return the value of V_SPU_DEPARTMENT_MAPPING.CREATOR
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_SPU_DEPARTMENT_MAPPING.CREATOR
     *
     * @param creator the value for V_SPU_DEPARTMENT_MAPPING.CREATOR
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_SPU_DEPARTMENT_MAPPING.MOD_TIME
     *
     * @return the value of V_SPU_DEPARTMENT_MAPPING.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Date getModTime() {
        return modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_SPU_DEPARTMENT_MAPPING.MOD_TIME
     *
     * @param modTime the value for V_SPU_DEPARTMENT_MAPPING.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setModTime(Date modTime) {
        this.modTime = modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_SPU_DEPARTMENT_MAPPING.UPDATER
     *
     * @return the value of V_SPU_DEPARTMENT_MAPPING.UPDATER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getUpdater() {
        return updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_SPU_DEPARTMENT_MAPPING.UPDATER
     *
     * @param updater the value for V_SPU_DEPARTMENT_MAPPING.UPDATER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_SPU_DEPARTMENT_MAPPING.STATUS
     *
     * @return the value of V_SPU_DEPARTMENT_MAPPING.STATUS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_SPU_DEPARTMENT_MAPPING.STATUS
     *
     * @param status the value for V_SPU_DEPARTMENT_MAPPING.STATUS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setStatus(Integer status) {
        this.status = status;
    }
}