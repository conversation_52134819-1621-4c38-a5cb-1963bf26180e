package com.vedeng.erp.saleorder.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 报价商品信息
 */
@Data
public class QuoteGoodsInfoDto {

    /**
     * 商品id
     */
    private Integer goodsId;

    /**
     * sku唯一编码
     */
    private String sku;

    /**
     * 商品名称
     */
    private String goodsName;

    private Integer num;

    private BigDecimal price;

    /**
     * 交易者
     */
    private Integer traderId;

    /**
     * 报价单号
     */
    private String quoteorderNo;

    /**
     * 报价商品ID
     */
    private Integer quoteorderGoodsId;
}
