<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.crm.business.quote.mapper.QuoteorderNeedsMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.crm.business.quote.domain.entity.QuoteorderNeedsEntity">
    <!--@mbg.generated-->
    <!--@Table T_QUOTEORDER_NEEDS-->
    <id column="QUOTEORDER_NEEDS_ID" jdbcType="BIGINT" property="quoteorderNeedsId" />
    <result column="QUOTEORDER_ID" jdbcType="INTEGER" property="quoteorderId" />
    <result column="PRODUCT_NEEDS" jdbcType="VARCHAR" property="productNeeds" />
    <result column="NUM_NEEDS" jdbcType="VARCHAR" property="numNeeds" />
    <result column="BUDGETARY_NEEDS" jdbcType="VARCHAR" property="budgetaryNeeds" />
    <result column="DISTRIBUTE_BUDGET" jdbcType="VARCHAR" property="distributeBudget" />
    <result column="TERMINAL_BUDGET" jdbcType="VARCHAR" property="terminalBudget" />
    <result column="EXTRA_NEEDS" jdbcType="VARCHAR" property="extraNeeds" />
    <result column="HEAD_USER_ID" jdbcType="INTEGER" property="headUserId" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    QUOTEORDER_NEEDS_ID, QUOTEORDER_ID, PRODUCT_NEEDS, NUM_NEEDS,DISTRIBUTE_BUDGET,TERMINAL_BUDGET, BUDGETARY_NEEDS, EXTRA_NEEDS,HEAD_USER_ID,
    IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_QUOTEORDER_NEEDS
    where QUOTEORDER_NEEDS_ID = #{quoteorderNeedsId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_QUOTEORDER_NEEDS
    where QUOTEORDER_NEEDS_ID = #{quoteorderNeedsId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="QUOTEORDER_NEEDS_ID" keyProperty="quoteorderNeedsId" parameterType="com.vedeng.crm.business.quote.domain.entity.QuoteorderNeedsEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_QUOTEORDER_NEEDS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="quoteorderId != null">
        QUOTEORDER_ID,
      </if>
      <if test="productNeeds != null">
        PRODUCT_NEEDS,
      </if>
      <if test="numNeeds != null">
        NUM_NEEDS,
      </if>
      <if test="budgetaryNeeds != null">
        BUDGETARY_NEEDS,
      </if>
      <if test="extraNeeds != null">
        EXTRA_NEEDS,
      </if>
      <if test="distributeBudget != null">
        DISTRIBUTE_BUDGET,
      </if>
      <if test="terminalBudget != null">
        TERMINAL_BUDGET,
      </if>
      <if test="headUserId != null">
        HEAD_USER_ID,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="quoteorderId != null">
        #{quoteorderId,jdbcType=INTEGER},
      </if>
      <if test="productNeeds != null">
        #{productNeeds,jdbcType=VARCHAR},
      </if>
      <if test="numNeeds != null">
        #{numNeeds,jdbcType=VARCHAR},
      </if>
      <if test="budgetaryNeeds != null">
        #{budgetaryNeeds,jdbcType=VARCHAR},
      </if>
      <if test="extraNeeds != null">
        #{extraNeeds,jdbcType=VARCHAR},
      </if>
      <if test="distributeBudget != null">
        #{distributeBudget,jdbcType=VARCHAR},
      </if>
      <if test="terminalBudget != null">
        #{terminalBudget,jdbcType=VARCHAR},
      </if>
      <if test="headUserId != null">
        #{headUserId,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.crm.business.quote.domain.entity.QuoteorderNeedsEntity">
    <!--@mbg.generated-->
    update T_QUOTEORDER_NEEDS
    <set>
      <if test="quoteorderId != null">
        QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER},
      </if>
      <if test="productNeeds != null">
        PRODUCT_NEEDS = #{productNeeds,jdbcType=VARCHAR},
      </if>
      <if test="numNeeds != null">
        NUM_NEEDS = #{numNeeds,jdbcType=VARCHAR},
      </if>
      <if test="budgetaryNeeds != null">
        BUDGETARY_NEEDS = #{budgetaryNeeds,jdbcType=VARCHAR},
      </if>
      <if test="extraNeeds != null">
        EXTRA_NEEDS = #{extraNeeds,jdbcType=VARCHAR},
      </if>
      <if test="distributeBudget != null">
        DISTRIBUTE_BUDGET = #{distributeBudget,jdbcType=VARCHAR},
      </if>
      <if test="terminalBudget != null">
        TERMINAL_BUDGET = #{terminalBudget,jdbcType=VARCHAR},
      </if>
      <if test="headUserId != null">
        HEAD_USER_ID = #{headUserId,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where QUOTEORDER_NEEDS_ID = #{quoteorderNeedsId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.crm.business.quote.domain.entity.QuoteorderNeedsEntity">
    <!--@mbg.generated-->
    update T_QUOTEORDER_NEEDS
    set QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER},
      PRODUCT_NEEDS = #{productNeeds,jdbcType=VARCHAR},
      NUM_NEEDS = #{numNeeds,jdbcType=VARCHAR},
      BUDGETARY_NEEDS = #{budgetaryNeeds,jdbcType=VARCHAR},
      EXTRA_NEEDS = #{extraNeeds,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where QUOTEORDER_NEEDS_ID = #{quoteorderNeedsId,jdbcType=BIGINT}
  </update>

  <insert id="batchInsert" keyColumn="QUOTEORDER_NEEDS_ID" keyProperty="quoteorderNeedsId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_QUOTEORDER_NEEDS
    (QUOTEORDER_ID,PRODUCT_NEEDS, NUM_NEEDS, BUDGETARY_NEEDS, EXTRA_NEEDS,DISTRIBUTE_BUDGET, TERMINAL_BUDGET,
    ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME
    )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.quoteorderId,jdbcType=INTEGER},#{item.productNeeds,jdbcType=VARCHAR}, #{item.numNeeds,jdbcType=VARCHAR},
      #{item.budgetaryNeeds,jdbcType=VARCHAR}, #{item.extraNeeds,jdbcType=VARCHAR}, #{item.distributeBudget,jdbcType=VARCHAR}, #{item.terminalBudget,jdbcType=VARCHAR},
      #{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER},
      #{item.creatorName,jdbcType=VARCHAR}, #{item.updater,jdbcType=INTEGER}, #{item.updaterName,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="selectAllByQuoteorderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_QUOTEORDER_NEEDS
    where QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER} and IS_DELETE = 0
    order by QUOTEORDER_NEEDS_ID asc
    </select>

  <select id="selectByQuoteNeedsIdList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_QUOTEORDER_NEEDS
    where QUOTEORDER_NEEDS_ID in
    <foreach collection="quoteorderNeedsIdList" item="needsId" open="(" separator="," close=")">
      #{needsId,jdbcType=BIGINT}
    </foreach>
    and IS_DELETE = 0
  </select>


  <update id="batchDelete">
    update T_QUOTEORDER_NEEDS set IS_DELETE = 1
          where QUOTEORDER_NEEDS_ID in
    <foreach collection="list" item="item" open="(" separator="," close=")">
          #{item.quoteorderNeedsId,jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="queryQuoteGoodsByNeedsIdAndSkuNos"
          resultType="com.vedeng.crm.business.quote.domain.entity.CrmQuoteorderGoods">
      select b.SKU as sku
      from T_R_QUOTEORDER_NEEDS_J_GOODS a
               left join T_QUOTEORDER_GOODS b on a.QUOTEORDER_GOODS_ID = b.QUOTEORDER_GOODS_ID
      where a.IS_DELETE = 0
        and b.IS_DELETE = 0
        and a.QUOTEORDER_NEEDS_ID =  #{quoteorderNeedsId,jdbcType=INTEGER}
        and b.SKU in
    <foreach collection="skuNos" item="item" open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
  </select>
</mapper>
