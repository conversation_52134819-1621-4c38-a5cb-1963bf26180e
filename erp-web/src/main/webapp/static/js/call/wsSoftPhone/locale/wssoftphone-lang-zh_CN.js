var wsSoftPhoneLocale = {
    SPFunc_0 : " ",
    SPFunc_1 : "登录",
    SPFunc_2 : "退出",
    SPFunc_3 : "外拨",
    SPFunc_4 : "内呼",
    SPFunc_5 : "监听",
    SPFunc_6 : "保持",
    SPFunc_7 : "内部咨询",
    SPFunc_8 : "外部咨询",
    SPFunc_9 : "转接",
    SPFunc_10 : "结束通话",
    SPFunc_11 : "取回保持",
    SPFunc_12 : "取消外拨",
    SPFunc_13 : "取消咨询",
    SPFunc_14 : "结束咨询",
    SPFunc_15 : "咨询转接",
    SPFunc_16 : "保持咨询",
    SPFunc_17 : "咨询三方",
    SPFunc_18 : "取回咨询",
    SPFunc_19 : "取消外呼",
    SPFunc_20 : "保持外线",
    SPFunc_21 : "求助转出",
    SPFunc_22 : "结束求助",
    SPFunc_23 : "取回外线",
    SPFunc_24 : "取消监听",
    SPFunc_25 : "强拆",
    SPFunc_26 : "强拦",
    SPFunc_27 : "强插",
    SPFunc_28 : "结束监控",
    SPFunc_29 : "挂断客户",
    SPFunc_30 : "挂断座席",
    SPFunc_31 : "完成",
    SPFunc_32 : "挂断外线",
    SPFunc_33 : "离席",
    SPFunc_34 : "复席",
    SPFunc_35 : "取消内呼",
    SPFunc_36 : "工作模式",
    SPFunc_37 : "座席消息",
    SPFunc_38 : "取消外拨准备",
    SPFunc_39 : "取消监听准备",
    SPFunc_40 : "取消内呼准备",
    SPFunc_41 : "外线三方",
    SPFunc_42 : "软摘机",
    SPFunc_43 : "结束预览",
    SPFunc_44 : "预览外拨",
    SPFunc_45 : "删除预览任务",
    SPFunc_46 : "取消本次外拨",
    SPFunc_47 : "工作完成",
    SPFunc_48 : "结束会议",
    SPFunc_49 : "退出会议",
    SPFunc_50 : "挂断与会者",
    SPFunc_51 : "座席消息",
    SPFunc_52 : "取消等待",
    SPFunc_53 : "振铃转移",
    SPFunc_54 : "取消预离席",
    SPFunc_55 : "预离席",
    SPFunc_56 : "咨询",
    SPFunc_57 : "转接IVR",
    SPFunc_58 : "转接外线",
    SPFunc_59 : "转接坐席",
    SPFunc_60 : "麦克禁音",
    SPFunc_61 : "麦克启用",
    SPFunc_62 : "拨号盘",
    SPFunc_63 : "音量控制",
    SPFunc_64 : "配置",
    SPFunc_65 : "转接IVR",
    SPFunc_66 : "应答视频",
    SPFunc_67 : "结束视频",
    SPFunc_68 : "班长控制",

    AgentState_0:"未连接",
    AgentState_1:"初始化",
    AgentState_2:"检查电话",
    AgentState_3:"空闲",
    AgentState_4:"通话准备(播放工号)",
    AgentState_5:"通话准备(振铃)",
    AgentState_6:"通话",
    AgentState_7:"保持",
    AgentState_8:"外拨准备(输入号码)",
    AgentState_9:"外拨中",
    AgentState_10:"咨询请求中（振铃）",
    AgentState_11:"咨询",
    AgentState_12:"咨询保持",
    AgentState_13:"保持呼外线中",
    AgentState_14:"保持外线通话",
    AgentState_15:"通话保持外线",
    AgentState_16:"监听准备(选择座席)",
    AgentState_17:"监听",
    AgentState_18:"会议",
    AgentState_19:"话后",
    AgentState_20:"被咨询",
    AgentState_21:"会议",
    AgentState_22:"会议",
    AgentState_23:"离席",
    AgentState_24:"内呼准备(选择座席)",
    AgentState_25:"内呼中(振铃)",
    AgentState_26:"内部通话",
    AgentState_27:"等待物理连接",
    AgentState_28:"预览中",
    AgentState_29:"非实时业务",
    AgentState_30:"从属会议",
    AgentState_31:"等待IVR认证",
    AgentState_32:"转移中",
    AgentState_33:"咨询",
    AgentState_34:"在线",
    AgentState_35:"会议咨询",
    AgentState_36:"咨询切换",
    AgentState_37:"咨询切换",
    AgentState_38:"被保持",
    AgentState_39:"外线切换",
    AgentState_40:"外线切换",
    AgentState_41:"内呼保持",
    AgentState_42:"内呼被保持",
    AgentState_43:"外拨中转接",
    AgentState_44:"振铃转接",
    AgentState_45:"动态主叫外拨中",
    AgentState_46:"等待回振",
    AgentState_47:"等待回振",
    AgentState_48:"回振中",
    AgentState_49:"回振中",
    AgentState_50:"咨询排队",
    AgentState_51:"视频会议等待服务",
    AgentState_52:"坐席应答",
    AgentState_53:"会议",
    AgentState_54:"监听回振",

    AgentWorkMode_0 : "普通模式",
    AgentWorkMode_1 : "下班模式",
    AgentWorkMode_2 : "非实时模式",
    AgentWorkMode_3 : "外拨模式",
    AgentWorkMode_4 : "班长模式",
    AgentWorkMode_5 : "预览模式",
    AgentWorkMode_6 : "离开模式",
    AgentWorkMode_7 : "闭塞模式",
    AgentWorkMode_8 : "接线模式，不能外拨",

    DeviceCallState_0:"未连接",
    DeviceCallState_2:"内呼",
    DeviceCallState_4:"连接",
    DeviceCallState_5:"外拨振铃",
    DeviceCallState_6:"忙音",
    DeviceCallState_8:"振铃",
    DeviceCallState_10:"催挂音",
    DeviceCallState_11:"放音",
    DeviceCallState_12:"切换",
    DeviceCallState_13:"切换",
    DeviceCallState_14:"咨询保持",
    DeviceCallState_16:"外拨",
    DeviceCallState_32:"空闲",
    DeviceCallState_64:"偏转",
    DeviceCallState_128:"转接",
    DeviceCallState_256:"保持",
    DeviceCallState_512:"会议",
    DeviceCallState_1024:"监听",
    DeviceCallState_2048:"咨询",
    DeviceCallState_4096:"等待拨号音",
    DeviceCallState_8192:"失败",
    DeviceCallState_16384:"验证中状态",
    DeviceCallState_32768:"网络连接中状态",

    Transfer_IVR:"自动语音",
    Transfer_Agent:"座席",
    Transfer_OutLine:"外线",

    Caption_SkillCode:"技能码",
    Caption_SkillName:"技能名",
    Caption_Length:"长度",
    Caption_AllQueue:"总体队列",
    Caption_MyQueue:"我的队列",
    Caption_AutoAnswer:"自动应答",


    ShowLog : "显示日志",
    ClearLog : "清空日志",
    IVRPanelCaption : "IVR快捷组",
    ClearLogSucc : "清空完毕",

};

/*
 * 描述：动态取得本地资源文件内容
 *
 * 参数： key 对应的资源的key params 对应资源中的参数列表
 *
 * 返回：对应的资源内容
 *
 * 用法： getLocale("helloParam",{first:value1,second:value2});
 */
function getWSSoftPhoneText(key, params) {
    var result = ""; // 对应的资源的内容

    if (typeof (key) != 'undefined'
        && typeof (wsSoftPhoneLocale) != 'undefined') {
        // 根据key取得对应的资源内容，如果没有找到则返回key值
        if (wsSoftPhoneLocale[key] != undefined) {
            result = wsSoftPhoneLocale[key];
        } else {
            result = key;
        }

        if (typeof (params) != 'undefined') {
            // 替换对应参数为value的值
            var regExp = new RegExp(); // 替换资源中参数的正则
            for (var i = 0; i < params.length; i++) {
                regExp = eval("/{[" + i + "]}/g");
                result = result.replace(regExp, params[i]);
            }
        }
        // 如果没有找到对应的资源则返回 "No Value"
        if (/{[0-9]+}/.test(result)) {
            result = result.replace(/{[0-9]+}/g, "No Value");
        }
    }
    return result;
}

function getStateName(st) {
    return getWSSoftPhoneText("AgentState_" + st);
}

function getWorkModeName(wkmd) {
    return getWSSoftPhoneText("AgentWorkMode_" + wkmd);
}
function getWorkModeStateName(wkmd, st) {
    return getWSSoftPhoneText("AgentWorkMode_" + wkmd) + "-"
        + getWSSoftPhoneText("AgentState_" + st);
}

function getDeviceCallStateName(st){
    return getWSSoftPhoneText("DeviceCallState_" + st);
}

function getTransferName(item){
    return getWSSoftPhoneText("Transfer_" + item);
}

function getSoftPhoneCaption(key){
    return getWSSoftPhoneText("Caption_" + key);
}

