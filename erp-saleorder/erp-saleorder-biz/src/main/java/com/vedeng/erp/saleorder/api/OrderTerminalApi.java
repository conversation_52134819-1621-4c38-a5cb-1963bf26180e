package com.vedeng.erp.saleorder.api;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.erp.saleorder.dto.OrderTerminalDto;
import com.vedeng.erp.saleorder.dto.TerminalQueryDto;
import com.vedeng.erp.saleorder.service.OrderTerminalApiService;
import com.vedeng.erp.saleorder.service.OrderTerminalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@ExceptionController
@RestController
@RequestMapping("/api/terminal")
public class OrderTerminalApi {

    @Autowired
    private OrderTerminalService orderTerminalService;
    
    @Autowired
    private OrderTerminalApiService orderTerminalApiService;

    /**
     * 搜索终端信息
     */
    @RequestMapping(value = "/search", method = {RequestMethod.POST})
    @NoNeedAccessAuthorization
    public R<?> searchOneDataTerminal(@RequestBody TerminalQueryDto terminalQueryDto) {
        return R.success(orderTerminalService.getOneDataTerminalInfo(terminalQueryDto.getTerminalTraderName(), terminalQueryDto.getPageSize(), terminalQueryDto.getPageNum()));
    }

    /**
     * 查询终端信息
     */
    @RequestMapping(value = "/query", method = RequestMethod.GET)
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<OrderTerminalDto> getTerminal(@RequestParam("businessId") Integer businessId,@RequestParam("businessType") Integer businessType) {
        OrderTerminalDto dto = orderTerminalApiService.getTerminalInfoByBusinessIdAndBusinessType(businessId, businessType);
        return R.success(dto);
    }

}
