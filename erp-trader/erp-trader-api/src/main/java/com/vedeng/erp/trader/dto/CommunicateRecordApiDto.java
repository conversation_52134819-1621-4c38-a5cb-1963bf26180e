package com.vedeng.erp.trader.dto;

import lombok.Data;

import java.util.Date;

/**
 * 沟通记录
 *
 */
@Data
public class CommunicateRecordApiDto {

    private Integer communicateRecordId;

    /**
     * 类型SYS_OPTION_DEFINITION_ID(订单，报价，联系人...)
     */
    private Integer communicateType;

    /**
     * 公司ID
     */
    private Integer companyId;

    /**
     * 关联主表字段ID
     */
    private Integer relatedId;

    /**
     * 交易者ID
     */
    private Integer traderId;

    /**
     * 所属类型 1::经销商（包含终端）2:供应商
     */
    private Integer traderType;

    /**
     * 开始时间
     */
    private Long begintime;

    /**
     * 结束时间
     */
    private Long endtime;

    /**
     * 联系人ID
     */
    private Integer traderContactId;

    /**
     * 沟通方式SYS_OPTION_DEFINITION_ID
     */
    private Integer communicateMode;

    /**
     * 沟通目的SYS_OPTION_DEFINITION_ID
     */
    private Integer communicateGoal;

    /**
     * 下次联系日期
     */
    private Date nextContactDate;

    /**
     * 沟通电话
     */
    private String phone;

    /**
     * 呼叫中心COID
     */
    private String coid;

    /**
     * 1呼入2呼出
     */
    private Integer coidType;

    /**
     * 录音时长
     */
    private Integer coidLength;

    /**
     * 录音域名
     */
    private String coidDomain;

    /**
     * 录音地址
     */
    private String coidUri;

    /**
     * 下次沟通内容
     */
    private String nextContactContent;

    /**
     * 备注
     */
    private String comments;

    /**
     * 是否处理0否 1是（有下次沟通记录，无下次沟通时间默认为1）
     */
    private Integer isDone;

    /**
     * 售后对象ID
     */
    private Integer afterSalesTraderId;

    /**
     * 关联沟通ID
     */
    private Integer relateCommunicateRecordId;

    /**
     * 沟通内容
     */
    private String contactContent;

    /**
     * 联系人
     */
    private String contact;

    /**
     * 联系方式
     */
    private String contactMob;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 添加人
     */
    private Integer creator;

    /**
     * 最近一次编辑时间
     */
    private Long modTime;

    /**
     * 最近一次编辑人
     */
    private Integer updater;

    /**
     * 同步字段0否1是
     */
    private Integer syncStatus;

    /**
     * 是否加入语音转换队列，0-否，1-是
     */
    private Integer isLfasr;

    /**
     * 是否已点评0：否1：是
     */
    private Integer isComment;

    /**
     * 主叫号码
     */
    private String ttNumber;

    /**
     * 沟通内容后缀
     */
    private String contentSuffix;

    /**
     * 暂无下次沟通时间 0 否  1 是
     */
    private Integer noneNextDate;

}