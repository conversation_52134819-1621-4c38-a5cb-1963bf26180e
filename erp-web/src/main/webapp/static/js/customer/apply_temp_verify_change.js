$(function() {
    $("#billPeriodStart").attr("readonly","readonly");
    $("#billPeriodEnd").attr("readonly","readonly");
    $("#expectedMargin").attr("readonly","readonly");
    $("#applyReason").attr("readonly","readonly");

    $("#changeMount").change(function () {
        checkLogin();
        clearErroeMes();
        $("#changeMount").removeClass("errorbor");
        var changeMount = $("#changeMount").val();
        var totalAmount = $("#formerApplyAmount").val();
        var changeType = $("#changeType").val();
        var formerTotalAmount = $("#formerTotalAmount").val();

        var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
        if(changeMount.length>0 && !reg.test(changeMount)){
            warn2Tips("changeMount","调整额度输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
            return false;
        }
        var operateType = $("#operateType").val();
        if(changeType == "上调"){
            $("#applyAmount").text(Number(formerTotalAmount) + Number(totalAmount) + Number(changeMount));
            $("#finalApplyAmount").val(Number(totalAmount) + Number(changeMount));
        }else {
            $("#applyAmount").text((Number(formerTotalAmount) + Number(totalAmount) - Number(changeMount)).toFixed(2));
            $("#finalApplyAmount").val((Number(totalAmount) - Number(changeMount)).toFixed(2));
        }
    });


    $("#changeType").change(function () {
        checkLogin();
        clearErroeMes();
        $("#changeMount").removeClass("errorbor");
        var changeMount = $("#changeMount").val();
        var totalAmount = $("#formerApplyAmount").val();
        var changeType = $("#changeType").val();
        var formerTotalAmount = $("#formerTotalAmount").val();

        var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
        if(changeMount.length>0 && !reg.test(changeMount)){
            warn2Tips("changeMount","调整额度输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
            return false;
        }
        var operateType = $("#operateType").val();
        if(changeType == "上调"){
            $("#applyAmount").text(Number(formerTotalAmount) + Number(totalAmount) + Number(changeMount));
            $("#finalApplyAmount").val(Number(totalAmount) + Number(changeMount));
        }else {
            $("#applyAmount").text((Number(formerTotalAmount) + Number(totalAmount) - Number(changeMount)).toFixed(2));
            $("#finalApplyAmount").val((Number(totalAmount) - Number(changeMount)).toFixed(2));
        }
    });

//提交表单校验
    $('#myform').submit(function() {
        checkLogin();
        clearErroeMes();
        jQuery.ajax({
            url:'./saveAccountPeriodApplyNew.do',
            data:$('#myform').serialize(),
            type:"POST",
            dataType : "json",
            beforeSend:function(){
                var billPeriodStart = $("#billPeriodStart").val();
                var billPeriodEnd = $("#billPeriodEnd").val();
                if(billPeriodStart.length == 0){
                    warn2Tips("billPeriodStart","有效期开始时间不能为空");
                    return false;
                }
                if(billPeriodEnd.length == 0){
                    warn2Tips("billPeriodEnd","有效期结束时间不能为空");
                    return false;
                }
                if(billPeriodStart > billPeriodEnd){
                    warn2Tips("billPeriodEnd","有效期开始时间不能大于结束时间");
                    return false;
                }
                var applyAmount = $("#applyAmount").text();
                var usefulAmount = $("#usefulAmount").val();
                // if(!usefulAmount == "-"){
                    if(applyAmount < 0){
                        warn2Tips("changeMount","额度降低的范围不可小于0！");
                        $("#changeMount").addClass("errorbor");
                        return false;
                    }
                // }
                var re = /^[0-9]+$/;
                var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
                var settlementPeriod = $("#settlementPeriod").val();
                if(settlementPeriod.length == 0){
                    warn2Tips("settlementPeriod","结算周期不能为空");//文本框ID和提示用语
                    return false;
                }
                if(settlementPeriod.length>0 && !re.test(settlementPeriod)){
                    warn2Tips("settlementPeriod","结算周期输入错误！仅允许使用数字");//文本框ID和提示用语
                    return false;
                }
                var expectedMargin = $("#expectedMargin").val();
                if(expectedMargin.length>0 && !reg.test(expectedMargin)){
                    warn2Tips("expectedMargin","预期毛利率输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
                    return false;
                }
            },
            success:function(data){
                if(data.code == 0){
                    layer.alert(data.message,{
                        closeBtn: 0,
                        btn: ['确定'] //按钮
                    }, function(){
                        var st=data.data.split(",");
                        var str=page_url+"/finance/accountperiod/getAccountPeriodApply.do?billPeriodApplyId="+st[0]
                            +"&traderCustomerId="+st[1]+"&billPeriodType=2&traderAccountPeriodApplyId="+st[0]+"&verifyComment="+st[2]
                            +"&traderType=1";
                        $("#finace").attr('href',str);
                        window.parent.location.replace(str);
                    });
                }else{
                    layer.alert(data.message);
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
        return false;
    });
});