package com.vedeng.erp.trader.service;

import com.vedeng.erp.trader.dto.SupplierAssetApiDto;
import com.vedeng.erp.trader.dto.SupplierAssetChangeDto;

/**
 * <AUTHOR>
 */
public interface SupplierAssetApiService {


    /**
     * 获取某个供应商的资产
     *
     * @param traderSupplierId 供应商id
     * @param assetType        资产类型
     * @return SupplierAssetDto SupplierAssetDto
     */
    SupplierAssetApiDto getSupplierAsset(Integer traderSupplierId, Integer assetType);

    /**
     * 资产新增
     */
    void add(SupplierAssetChangeDto supplierAssetChangeDto);

    /**
     * 资产占用
     */
    void occupy(SupplierAssetChangeDto supplierAssetChangeDto);

    /**
     * 解除资产占用
     */
    void relieveOccupy(SupplierAssetChangeDto supplierAssetChangeDto);

    /**
     * 资产减少
     */
    void sub(SupplierAssetChangeDto supplierAssetChangeDto);
}