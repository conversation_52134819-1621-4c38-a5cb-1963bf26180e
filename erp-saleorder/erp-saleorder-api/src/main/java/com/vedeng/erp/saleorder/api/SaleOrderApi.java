package com.vedeng.erp.saleorder.api;

import com.vedeng.common.core.base.R;
import com.vedeng.erp.saleorder.dto.CapitalFlowDto;
import com.vedeng.erp.saleorder.dto.SaleOrderGoodsDetailDto;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 订单对外部服务api
 * @date 2022/12/23 10:53
 */
@RequestMapping("/api/saleOrder")
public interface SaleOrderApi {


    /**
     * 根据商机id或者订单id获取交易流水
     *
     * @param capitalFlowDto CapitalFlowDto
     * @return R<?>
     */
    @RequestMapping(value = "/getFlowAmount")
    R<CapitalFlowDto> getFlowAmount(@RequestBody CapitalFlowDto capitalFlowDto);


}
