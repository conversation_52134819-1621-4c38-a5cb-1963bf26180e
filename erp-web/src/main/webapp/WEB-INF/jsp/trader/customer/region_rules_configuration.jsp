<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ include file="../../common/common.jsp" %>
<script src="${pageContext.request.contextPath}/static/js/trader/region_rules_configuration.js?rnd=${resourceVersionKey}"
        charset="utf-8"></script>
<span style="font-size: 20px !important;font-weight: bold">区域维护</span>
<div class="main-container">
    <div class="searchfunc ">
        <form method="post" id="search" action="/trader/customer/regionRulesList.do">
            <ul>
                <li>
                    <label class="infor_name">归属销售：</label>
                    <select class="input-middle f_left selector" name="userId" id="userId">
                        <option value="-1">全部</option>
                        <c:forEach var="list" items="${userList}">
                            <option value="${list.userId}"
                                    <c:if test="${regionRulesQueryDto.userId == list.userId}">selected="selected"</c:if> >${list.username}</option>
                        </c:forEach>
                    </select>

                </li>
                <%--<li>
                    <label class="infor_name">销售</label>
                    <select class="input-middle f_left selector" name="userId" id="userId">
                        <option value="-1">全部</option>
                        <c:forEach var="list" items="${userList}">
                            <option value="${list.userId}" <c:if test="${regionRulesQueryDto.userId == list.userId}">selected="selected"</c:if> >${list.username}</option>
                        </c:forEach>
                    </select>
                </li>--%>
                <li>
                    <label class="infor_name">区域：</label>
                    <ul class="inputfloat f_left">
                        <li>
                            <select class="wid16 selector" name="province" id="province">
                                <option value="0">全部</option>
                                <c:if test="${not empty provinceList }">
                                    <c:forEach items="${provinceList }" var="prov">
                                        <option value="${prov.regionId }" <c:if test="${regionRulesQueryDto.province==prov.regionId}">selected="selected"</c:if>>${prov.regionName }</option>
                                    </c:forEach>
                                </c:if>
                            </select>
                        </li>
                        <li>
                            <select class="wid16 selector" name="city" id="city">
                                <option value="0">全部</option>
                                <c:if test="${not empty cityList }">
                                    <c:forEach items="${cityList }" var="cy">
                                        <option value="${cy.regionId }" <c:if test="${regionRulesQueryDto.city==cy.regionId}">selected="selected"</c:if>>${cy.regionName }</option>
                                    </c:forEach>
                                </c:if>
                            </select>
                        </li>
                        <li>
                            <select class="wid16 selector" name="zone" id="zone">
                                <option value="0">全部</option>
                                <c:if test="${not empty zoneList }">
                                    <c:forEach items="${zoneList }" var="zo">
                                        <option value="${zo.regionId }" <c:if test="${regionRulesQueryDto.zone==zo.regionId}">selected="selected"</c:if>>${zo.regionName }</option>
                                    </c:forEach>
                                </c:if>
                            </select>
                        </li>


                    </ul>
                </li>
            </ul>
            <div class="tcenter">
                    <span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();"
                          id="searchSpan">搜索</span>
                <span class="bt-small bg-light-blue bt-bg-style mr20" onclick="searchReset();">重置</span>
                <span class="bt-small bt-bg-style bg-deep-blue" onclick="deleteSelected()">删除选中</span>
                <span class="bt-small bg-light-orange bt-bg-style" onclick="addRegionRules()">新增区域</span>
            </div>
        </form>
        <br /> <input type="hidden" id="flag" value="${flag}">
        <div class="">
            <div class="superdiv" style="width:100%;">
                <table class="table table-bordered table-striped table-condensed table-centered">
                    <thead>
                    <tr>
                        <th rowspan="2" class="wid1" >
                            <div class="input-checkbox">
                                <label class="input-wrap">
                                    <input type="checkbox" id="reginTrAll" onclick="checkAll(this)" class="J-select-list-all">
                                    <span class="input-ctnr">选中本页</span>
                                </label>
                            </div>
                        </th>
                        <th rowspan="2" class="wid10"><span style="font-weight: bold">归属部门</span></th>
                        <th rowspan="2" class="wid3"><span style="font-weight: bold">归属销售</span></th>
                        <th rowspan="2" class="wid3"><span style="font-weight: bold">省</span></th>
                        <th rowspan="2" class="wid3"><span style="font-weight: bold">市</span></th>
                        <th rowspan="2" class="wid3"><span style="font-weight: bold">区/县</span></th>
                        <th rowspan="2" class="wid6"><span style="font-weight: bold">操作</span></th>
                    </tr>
                    </thead>

                    <tbody>
                    <c:if test="${not empty publicCustomerRegionRulesVos}">
                        <c:forEach items="${publicCustomerRegionRulesVos}" var="item" varStatus="status">
                            <tr>
                                <td>
                                    <div class="tr-item">
                                        <div class="input-checkbox">
                                            <label class="input-wrap">
                                                <input type="checkbox" class="J-select-sku" name="reginTrId" onclick="checkThis(this)" value="${item.publicCustomerRegionRulesId}">
                                            </label>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="tr-item">
                                        <span class="item-value" name="">${item.orgName}</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="tr-item">
                                        <span class="item-value">${item.username}</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="tr-item">
                                        <span class="item-value">${item.provinceName}</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="tr-item">
                                        <span class="item-value">${item.cityName}</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="tr-item">
                                        <span class="item-value">${item.zoneName}</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="tr-item">
                                                <span class="item-value" >
    <%--                                                <c:if test="${item.skuStatus == 0}">--%>
    <%--                                                <a href="javascript:void(0);" style="cursor:pointer" onclick="updateRegionRules(${item.publicCustomerRegionRulesId})">修改</a>--%>
                                                    <a href='./editRegionRules.do?publicCustomerRegionRulesId=${item.publicCustomerRegionRulesId}' style="cursor:pointer" <%--onclick="updateRegionRules(${item.publicCustomerRegionRulesId})"--%>>修改</a>
                                                        <a href="javascript:void(0);" style="cursor:pointer" onclick="delete0(${item.publicCustomerRegionRulesId})">删除</a>
    <%--                                                </c:if>--%>
                                                </span>
                                    </div>
                                </td>
                            </tr>
                        </c:forEach>
                    </c:if>

                    </tbody>
                </table>
            </div>
            <c:if test="${empty publicCustomerRegionRulesVos}">
                <!-- 查询无结果弹出 -->
                <div class="noresult">查询无结果！请尝试使用其他搜索条件。</div>
            </c:if>
        </div>

    </div>
    <tags:page page="${page}" />

</div>
<%--<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/region/index.js?rnd=${resourceVersionKey}"></script>--%>
<script>

    $(document).ready(function(){
        if ($("#flag").val() == 1) {
            layer.msg('操作成功', {
                icon: 1,
                time: 1000 //2秒关闭（如果不配置，默认是3秒）
            }, function(){
                //do something
            });
        }
    })
    function foldItem() {
        var s = $("#foldButton").text();
        if (s === '更多筛选条件') {
            $("#foldButton").text("收起筛选条件");
            $(".fold-li").css("display", "list-item");
        } else {
            $("#foldButton").text("更多筛选条件");
            $(".fold-li").css("display", "none");
        }

    }
    $(function(){
        $("select[name='province']").change(function(){
            checkLogin();
            var regionId = $(this).val();
            if(regionId > 0){
                $.ajax({
                    type : "POST",
                    url : page_url+"/system/region/getregion.do",
                    data :{'regionId':regionId},
                    dataType : 'json',
                    success : function(data) {
                        $option = "<option value='0'>全部</option>";
                        $.each(data.listData,function(i,n){
                            $option += "<option value='"+data.listData[i]['regionId']+"'>"+data.listData[i]['regionName']+"</option>";
                        });
                        $("select[name='city'] option:gt(0)").remove();
                        $("select[name='zone'] option:gt(0)").remove();
                        $("#zone").val("0").trigger("change");
                        $("#city").val("0").trigger("change");

                        $("select[name='city']").html($option);
                        $("select[name='zone']").html("<option value='0'>全部</option>");
                    },
                    error:function(data){
                        if(data.status ==1001){
                            layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                        }
                    }
                });
            }else if(regionId==0){
                $("select[name='city'] option:gt(0)").remove();
                $("select[name='zone'] option:gt(0)").remove();
            }
        });

        $("select[name='city']").change(function(){
            checkLogin();
            var regionId = $(this).val();
            if(regionId > 0){
                $.ajax({
                    type : "POST",
                    url : page_url+"/system/region/getregion.do",
                    data :{'regionId':regionId},
                    dataType : 'json',
                    success : function(data) {
                        $option = "<option value='0'>全部</option>";
                        $.each(data.listData,function(i,n){
                            $option += "<option value='"+data.listData[i]['regionId']+"'>"+data.listData[i]['regionName']+"</option>";
                        });
                        $("select[name='zone'] option:gt(0)").remove();

                        $("select[name='zone']").html($option);
                    },
                    error:function(data){
                        if(data.status ==1001){
                            layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                        }
                    }
                });
            }
        });
    });

    /**
     * 重置所有框内容
     */
    function searchReset() {
        $('#userId').val(-1);
        $('#city').val(0);
        $('#zone').val(0);
        $('#province').val(0);
        $('.selector').each(function (i, j) {
            var options = $(j).find("option");
            options.attr("selected", false);
            options.first().attr("selected", true);
        })

        var options = $('#city').find("option");
        options.attr("selected", false);
        options.first().attr("selected", true);
        $(options).each(function (i,j){
            if (i != 0) {
                $(j).remove()
            }

        })

        var options = $('#zone').find("option");
        options.attr("selected", false);
        options.first().attr("selected", true);
        $(options).each(function (i,j){
            if (i != 0) {
                $(j).remove()
            }

        })
    }

    /**
     * 校验数字框输入合法性
     */
    function checkValue(obj) {
        if ($(obj).val() != undefined && $(obj).val() != '') {
            var reg = /^\d+(?=\.{0,1}\d+$|$)/;
            if (!reg.test($(obj).val())) {
                layer.alert('请输入数字!');
                $(obj).val('');
            }
        }
    }

    /**
     * 抓取发票操作
     */
    function captureInvoiceInit() {
        var open = layer.open({
            type: 1,
            title: '抓取发票',
            shadeClose: false,
            area : ['600px', '220px'],
            content: '<iframe style="width: calc(100% - 10px);height: calc(100% - 10px); border: 0;" src="/finance/invoice/batchCaptureInvoiceInit.do"></iframe>',
            success: function(layero, index){

            },
            error: function (data) {
                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    }

    /**
     * 进项票导出页面
     */
    function reportHxInvoice() {
        //window.open("http://ezadmin.ivedeng.com/ezlist/list/list.html?pageId=301");
    }

    /**
     * 查看供应商详情
     *
     * @param trader
     */
    function viewTraderInfo(trader) {
        parent.viewTraderInfo(trader);
    }

    /**
     * 查看采购订单信息
     * */
    function viewBuyOrder(buyOrderId,type) {
        parent.viewBuyOrderInfo(buyOrderId,type);
    }

    function delete0(publicCustomerRegionRulesId){
        layer.confirm('是否删除本条记录?', {icon: 3, title:'提示'}, function(index){
            //do something
            layer.close(index);
            $.ajax({
                type: "POST",
                url: "/trader/customer/deleteRegionRule.do",
                data:  {"publicCustomerRegionRulesId":publicCustomerRegionRulesId},
                dataType:'json',
                success: function(data){
                    if (data.code == 0) {
                        layer.msg('操作成功', {
                            icon: 1,
                            time: 1000 //2秒关闭（如果不配置，默认是3秒）
                        }, function(){
                            //do something

                            $("#search").submit();
                        });
                        // debugger

                        // layer.alert("操作成功失败");
                    } else {
                        layer.msg('操作失败', {
                            icon: 2,
                            time: 1000 //2秒关闭（如果不配置，默认是3秒）
                        }, function(){
                            //do something
                            $("#search").submit();
                        });
                    }

                },
                error:function(data){
                    if (data.status == 1001) {
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
                    }
                }
            });
        });


    }
</script>
<%@ include file="../../common/footer.jsp" %>
