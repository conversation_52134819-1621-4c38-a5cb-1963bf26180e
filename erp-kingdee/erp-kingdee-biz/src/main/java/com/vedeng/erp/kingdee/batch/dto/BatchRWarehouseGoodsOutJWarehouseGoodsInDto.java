package com.vedeng.erp.kingdee.batch.dto;

import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
* <AUTHOR>
* @date 2023/1/31
* @apiNote 
*/

/**
 * 出入库关系表（细粒度到商品）
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchRWarehouseGoodsOutJWarehouseGoodsInDto {
    /**
     * 主键
     */
    private Long tRWarehouseGoodsOutJWarehouseGoodsInId;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 修改时间
     */
    private Date modTime;

    /**
     * 创建者id
     */
    private Integer creator;

    /**
     * 修改者id
     */
    private Integer updater;

    /**
     * 创建者名称
     */
    private String creatorName;

    /**
     * 修改者名称
     */
    private String updaterName;

    /**
     * 出库单id
     */
    private Long warehouseGoodsOutId;

    /**
     * 入库单id
     */
    private Long warehouseGoodsInId;

    /**
     * 出库单明细id
     */
    private Long warehouseGoodsOutItemId;

    /**
     * 入库单明细id
     */
    private Long warehouseGoodsInItemId;

    /**
     * 关系单类型， 1销售出库单和销售退货入库单关系绑定。
     * 2采购入库单和销售出库单关系绑定。
     * 3采购退货出库单和采购入库单关系绑定
     */
    private Integer relationType;

    /**
     * skuId
     */
    private Integer skuId;

    /**
     * sku
     */
    private String skuNo;

    /**
     * 关联数量
     */
    private BigDecimal num;
}