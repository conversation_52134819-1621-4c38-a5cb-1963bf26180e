// 三级分类 - 下拉联想
Vue.component('ui-search-related', {
    template: `
        <div class="vd-ui-search-category" :style="{width: Number(width)+40+'px'}">
            <div class="vd-ui-search-category-form">
                <div
                    class="vd-ui-search-category-item"
                    v-for="(item, index) in list" :key="index"
                >
                    <div >
                        <ui-input
                            :width="width+'px'"
                            :clearable="clearable"
                            v-model="item.val"
                            @input.native="oninput"
                            @compositionend.native="commentPress"
                            @clear="clear"
                            @focus="onFocus(index)"
                            @blur="onBlur(index)"
                        ></ui-input>
                        <div class="del" v-if="index > 0" @click="del(index)">
                            <i class="vd-ui_icon icon-delete"></i>
                        </div>
                    </div>

                    <transition name="fade">
                        <div class="search-panel" v-if="rotate == index" :style="{width: width+'px'}">
                            <div class="search-panel-inner">
                                <div class="search-loading" v-if="loading">
                                    <i class="vd-ui_icon icon-loading"></i>
                                    <span>加载中...</span>
                                </div>
                                <div class="search-panel-list" v-else-if="searchList && searchList.length">
                                    <div class="label">
                                        <span class="num">分类</span>
                                        <span class="num">产品数量</span>
                                    </div>
                                    <div class="search-list-content">
                                        <div class="search-panel-item"
                                            v-for="search in searchList" :key="search[_value_]"
                                            @click.stop="selectThis(item, search)"
                                        >
                                            <span v-html="light(search[_label_])"></span>
                                            <span v-if="search.countNum" class="num">{{ search.countNum }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div v-else class="null-data">
                                    <p>无匹配数据</p>
                                </div>
                            </div>
                        </div>
                    </transition>
                    <transition name="fade">
                        <div class="ui-related-search-history" v-if="historyRotate == index" :style="{width: width+'px'}">
                            <div class="ui-related-search-history-title">最近使用</div>
                            <div class="ui-related-search-history-list">
                                <div class="ui-related-search-history-item" @click.stop="selectHistory(item, hItem)" v-for="(hItem, hIndex) in historyList" :key="hIndex">{{ hItem }}</div>
                            </div>
                        </div>
                    </transition>
                </div>
            </div>
            <div class="vd-ui-input-error" v-if="errorable">
                <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i>
                <p class="vd-ui-input-error--errmsg">{{errorMsg}}</p>
            </div>
            <div class="vd-ui-search-category-btn" v-if="list.length < size">
                <a class="btn" @click="add"><i class="vd-ui_icon icon-add"></i>{{ btn }}</a>
            </div>
        </div>
    `,
    props: {
        value: {
            type: String,
            default: ''
        },
        // 是否需要清空功能
        clearable: {
            type: Boolean,
            default: false,
        },
        size: {
            type: Number,
            default: 5,
        },
        btn: {
            type: String,
            default: '添加分类'
        },
        width: {
            type: String,
            default: '300px'
        },
        _label_: {
            type: String,
            default: 'categoryName'
        },
        _value_: {
            type: String,
            default: 'categoryId'
        },
        baseUrl: {
            type: String,
            default: '/crm/category/public/findThreeCategory',
        },
        errorable: {
            type: Boolean,
            default: false
        },
        needHistory: {
            type: String,
            default: ''
        }
    },
    data () {
        return {
            rotate: null,
            historyRotate: null,
            list: [
                { val: '', id: 1 },
            ],
            errorable: false,
            errorMsg: '',
            inputTimeout: null,
            loading: false,

            indexFlag: null, // 当前操作下标
            searchList: [], // 当前搜索推荐
            historyList: [], //历史输入
            valueFromSelect: false,
        }
    },
    watch: {
        rotate (newV) {
            if (newV == null) {
               this.searchList = [];
            }
        }
    },
    computed: {
        // 高亮
        light () {
            return (name) => {
                let inputValue = this.list[this.indexFlag].val;
                console.log('inputValue:', inputValue);
                if (!inputValue) return name;
                const regExp = new RegExp(inputValue, 'g');
                name = (name || '').replace(regExp, `<font color='#FF6600'">${inputValue}</font>`);
                return name;
            }
        },
    },
    mounted () {
        if (this.value) {
            this.init();
        }

        if(this.needHistory) {
            let historyList = JSON.parse(localStorage.getItem(this.needHistory) || '[]');
            this.historyList = historyList;
        }

        this.$form.setValidEl(this);
        document.addEventListener('click',(e)=>{
            if(!this.$el.contains(e.target)) {
                this.rotate = null;
            }
        })
    },
    methods: {
        init () {
            if (!this.value) {
                this.list = [{val: '', id: 1, searchList: []}];
            } else {
                let arr = this.value.split('&&');
                let list_ = [];
                arr.forEach((item, index) => {
                    list_.push({
                        val: item,
                        id: list_.length + 1,
                        searchList: [],
                    })
                })
                this.list = list_;
            }
        },
        clear () {
            this.pick();
            this.rotate = null;
            this.checkHistory();
        },
        onFocus(index) {
            this.indexFlag = index;
            this.checkHistory();
        },
        onBlur() {
            setTimeout(()=> {
                this.historyRotate = null;

                if(!this.valueFromSelect) {
                    this.list[this.indexFlag].val = '';
                    this.pick();
                }
            }, 200)

            setTimeout(()=> {
                console.log('this.value:', this.value);
                this.checkValid(this.value || '');
            }, 500)
        },

        // 节流
        // handleInput: _.throttle(function (index) {
        //     this.oninput(index);
        // }, 200),
        checkHistory() {
            if(!this.list[this.indexFlag].val && this.needHistory && this.historyList.length) {
                this.historyRotate = this.indexFlag;
            }
        },
        oninput (event) {
            console.log('category:input')
            this.valueFromSelect = false;
            if(!event.target.value.trim()) {
                this.rotate = null;
                this.checkHistory();
            } else {
                this.historyRotate = null;
                this.rotate = this.indexFlag;
                if (event.inputType != 'insertCompositionText') {
                    // this.pick();
    
                    this.inputTimeout && clearTimeout(this.inputTimeout);
                    this.inputTimeout = setTimeout(() => {
                        this.searchRelated(event.target.value);
                    }, 300);
                }
            }
        },
        commentPress(event) {
            this.rotate = this.indexFlag;
            // this.pick();

            this.inputTimeout && clearTimeout(this.inputTimeout);
            this.inputTimeout = setTimeout(() => {
                this.searchRelated(event.target.value);
            }, 300);
        },
        async searchRelated (keyword) {
            if (!keyword.trim()) return;
            this.loading = true;
            try {
                let {data: searchRes} = await this.$axios.post(this.baseUrl, {
                    keyword: keyword,
                    pageNum: 1,
                    pageSize: 100
                })

                this.loading = false;
                if (searchRes.success) {
                    let res = searchRes.data.list|| [];
                    this.searchList = res;
                } else {
                    this.searchList = [];
                }
            } catch (err) {
                console.log('err:', err);
            }
        },
        add () {
            if (this.list.length < this.size) {
                this.list.push({
                    val: '',
                    id: this.list.length + 1,
                })
            }
        },
        del (index) {
            this.list.splice(index, 1);
            this.pick();
        },
        selectThis(item, search) {
            item.val = search[this._label_];
            this.rotate = null;

            this.pick();
        },
        selectHistory(item, val) {
            item.val = val;
            this.rotate = null;

            this.pick();
        },
        pick () {
            this.valueFromSelect = true;
            let arr = [];
            this.list.forEach(item => {
                if (item.val.trim()) {
                    arr.push(item.val.trim());
                }
            })
            let str = arr.join('&&');
            this.$emit("input", str); // 修改外层v-model值
            this.$emit('change', arr)
        },
        checkValid(newValue) {
            if(this.validKey && this.validValue) {
                if(this.$form.validList[this.validKey].indexOf(this.validValue) !== -1) {
                    let validData = this.$form.checkValid(this.validValue, newValue, this.validKey);

                    this.triggerError(validData);
                }
            }
        },
        triggerError(validData) {
            if(validData.result) {
                this.errorMsg = '';
                this.errorable = false;
            } else {
                this.errorMsg = validData.message;
                this.errorable = true;
            }
        }
    }
})