package com.vedeng.aftersales.component;

import com.vedeng.aftersales.component.impl.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: daniel
 * @Date: 2021/6/21 17 38
 * @Description:
 */
@Service
public class AfterSalesOrderServiceFactory {

    @Autowired
    private ReturnsAfterSalesOfSaleOrderService returnsAfterSalesOfSaleOrder;

    @Autowired
    private ExchangesAfterSalesOfSaleOrderService exchangesAfterSalesOfSaleOrder;

    @Autowired
    private InstallAndCommissionAfterSalesOfSaleOrderService installAndCommissionAfterSalesOfSaleOrder;

    @Autowired
    private MaintenanceAfterSalesOfSaleOrderService maintenanceAfterSalesOfSaleOrder;

    @Autowired
    private RefundTicketAfterSalesOfSaleOrderService refundTicketAfterSalesOfSaleOrder;

    @Autowired
    private LostTicketAfterSalesOfSaleOrderService lostTicketAfterSalesOfSaleOrder;

    @Autowired
    private TechConsultAfterSalesOfSaleOrderService techConsultAfterSalesOfSaleOrder;

    @Autowired
    private OthersAfterSalesOfSaleOrderService othersAfterSalesOfSaleOrder;

    @Autowired
    private NonperformencePaymentAfterSalesOfSaleOrderService nonperformencePaymentAfterSalesOfSaleOrderService;

    @Autowired
    private DeliveryComplaintAfterSalesOfSaleOrderService deliveryComplaintAfterSalesOfSaleOrderService;

    @Autowired
    private InstallComplaintAfterSalesOfSaleOrderService installComplaintAfterSalesOfSaleOrderService;

    @Autowired
    private MaintainComplaintAfterSalesOfSaleOrderService maintainComplaintAfterSalesOfSaleOrderService;

    public AfterSalesOrderService createAfterSalesInstance(Integer afterSaleTypeCode){

        AfterSalesTypeEnum afterSalesTypeEnum = AfterSalesTypeEnum.getEnumByCode(afterSaleTypeCode);

        AfterSalesOrderService afterSalesOrderService = null;

        switch (afterSalesTypeEnum){
            case RETURNS_SALEORDER:
                afterSalesOrderService = returnsAfterSalesOfSaleOrder;
                break;
            case EXCHANGES_SALEORDER:
                afterSalesOrderService = exchangesAfterSalesOfSaleOrder;
                break;
            case INSTALL_AFTERSALES_ATY:
                afterSalesOrderService = installAndCommissionAfterSalesOfSaleOrder;
                break;
            case INSTALL_AFTERSALES_ATN:
                afterSalesOrderService = installAndCommissionAfterSalesOfSaleOrder;
                break;
            case MAINTENANCE_SALEORDER:
                afterSalesOrderService = maintenanceAfterSalesOfSaleOrder;
                break;
            case REFUND_TICKET_SALEORDER:
                afterSalesOrderService = refundTicketAfterSalesOfSaleOrder;
                break;
            case LOST_TICKET_SALEORDER:
                afterSalesOrderService = lostTicketAfterSalesOfSaleOrder;
                break;
            case TECH_CONSULT_SALEORDER:
                afterSalesOrderService = techConsultAfterSalesOfSaleOrder;
                break;
            case OTHERS_SALEORDER:
                afterSalesOrderService = othersAfterSalesOfSaleOrder;
                break;
            case NONPERFORMENCE_PAYMENT:
                afterSalesOrderService = nonperformencePaymentAfterSalesOfSaleOrderService;
                break;
            case DELIVERY_COMPLAINT:
                afterSalesOrderService = deliveryComplaintAfterSalesOfSaleOrderService;
                break;
            case INSTALL_COMPLAINT:
                afterSalesOrderService = installComplaintAfterSalesOfSaleOrderService;
                break;
            case MAINTAIN_COMPLAINT:
                afterSalesOrderService = maintainComplaintAfterSalesOfSaleOrderService;
                break;
            default:
                break;
        }
        return afterSalesOrderService;
    }
}
