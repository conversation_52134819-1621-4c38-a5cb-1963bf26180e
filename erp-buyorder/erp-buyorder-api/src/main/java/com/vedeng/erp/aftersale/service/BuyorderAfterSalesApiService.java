package com.vedeng.erp.aftersale.service;

import com.vedeng.erp.aftersale.dto.*;

import java.util.List;

/**
 * 采购售后api方法
 */
public interface BuyorderAfterSalesApiService {
    /**
     * 保存采购售后仅退票发票信息
     * <AUTHOR>
     * @param afterBuyorderInvoiceDto
     * @return
     */
    int saveAfterBuyorderInvoiceInfo(AfterBuyorderInvoiceDto afterBuyorderInvoiceDto);

    /**
     * 根据售后单id查询仅退票蓝字暂存信息
     * <AUTHOR>
     * @param afterSalesId
     * @return
     */
    AfterBuyorderInvoiceDto queryInfoByAfterSalesId(Integer afterSalesId);

    /**
     * 更新仅退票发票暂存信息
     * <AUTHOR>
     * @param afterBuyorderInvoiceDto
     * @return
     */
    int updateByPrimaryKeySelective(AfterBuyorderInvoiceDto afterBuyorderInvoiceDto);

    /**
     * 条件检索售后商品ID
     * @param afterSaleGoodsIdQueryReqDto
     * @return
     */
    Integer getAfterGoodsIdByCondition(AfterSaleGoodsIdQueryReqDto afterSaleGoodsIdQueryReqDto);

    /**
     * 根据售后单id查询（只单表信息）
     *
     * @param afterSalesId 售后单id
     * @return BuyOrderAfterSalesDto
     */
    BuyOrderAfterSalesDto getByAfterSalesId(Integer afterSalesId);

    /**
     * 获取售后商品
     * @param afterSalesId
     * @return
     */
    List<AfterSalesGoodsDto> getAfterSalesGoodsByAfterSalesId(Integer afterSalesId);

    /**
     * 根据售后单id查询（包含单表信息）
     *
     * @param afterSalesId 售后单id
     * @return AfterSalesDto
     */
    AfterSalesApiDto getAfterSalesByAfterSalesId(Integer afterSalesId);

    /**
     * 根据采购单id查询所有售后单
     * @param buyOrderId
     * @return
     */
    List<AfterSalesApiDto> getAllAfterSalesByBuyOrderId(Integer buyOrderId);
}
