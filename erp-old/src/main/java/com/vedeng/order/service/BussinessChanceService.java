package com.vedeng.order.service;

import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.service.BaseService;
import com.vedeng.order.model.BussinessChance;
import com.vedeng.order.model.dto.NewSourceDicDto;
import com.vedeng.order.model.vo.BussinessChanceVo;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.trader.model.TraderContact;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import org.apache.ibatis.annotations.Param;

import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;

public interface BussinessChanceService extends BaseService {
	
	/**
	 * <b>Description:</b><br> 售后商机列表
	 * @param bussinessChance
	 * @param page
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年6月22日 上午10:26:32
	 */
	Map<String,Object> getServiceBussinessChanceListPage(BussinessChanceVo bussinessChance,Page page);
	
	/**
	 * <b>Description:</b><br> 销售商机列表
	 * @param bussinessChance
	 * @param page
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年6月22日 上午10:27:03
	 */
	Map<String,Object> getSaleBussinessChanceListPage(BussinessChanceVo bussinessChance,Page page);


	/**
	 * 联动关闭报价联动关闭商机
	 * @param quoteorderId
	 * @param optionType
	 * @param level 第几级联动关闭
	 * @return
	 */
	BussinessChance relateCloseBussChance(Integer quoteorderId, String optionType,Integer level);


	Integer checkBusiness2OtherSaleUser(Integer saleUserId);

	/**
	 * <b>Description:</b><br> 批量分配商机
	 * @param request
	 * @param session
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年6月22日 下午4:15:41
	 */
	ResultInfo assignBussinessChance(BussinessChanceVo bussinessChanceVo,HttpSession session);
	
	/**
	 * <b>Description:</b><br> 保存商机
	 * @param bussinessChance
	 * @param user
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年6月26日 下午2:17:38
	 */
	ResultInfo saveBussinessChance(BussinessChance bussinessChance,User user,Attachment attachment);

	BussinessChance getBusinessChanceByChanceNo(String bussinessChanceNo);

	/**
	 * <b>Description:</b>转换商机id如果被合并<br>
	 * @param
	 * @return
	 * @Note
	 * <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/6/18
	 */
	void convertBussinessChanceIfMerged(BussinessChance bussinessChance);
	/**
	 * <b>Description:</b><br> 查询售后商机详情
	 * @param bussinessChance
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年6月27日 下午6:54:40
	 */
	Map<String, Object> getAfterSalesDetail(BussinessChance bussinessChance,Page page);
	
	/**
	 * <b>Description:</b><br> 跳转编辑售后商机页面
	 * @param bussinessChance
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年6月29日 下午1:08:47
	 */
	BussinessChanceVo toAfterSalesEditPage(BussinessChance bussinessChance);
	
	/**
	 * <b>Description:</b><br> 保存确认的客户信息
	 * @param bussinessChance
	 * @param user
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年6月30日 下午5:42:28
	 */
	TraderCustomerVo saveConfirmCustomer(BussinessChance bussinessChance,User user,TraderContact traderContact);
	
	/**
	 * <b>Description:</b><br> 获取上传文件的域名
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年7月17日 下午3:47:39
	 */
	String getUploadDomain();
	/**
	 * 
	 * <b>Description:</b><br> 修改商机信息
	 * @param bussinessChance
	 * @return
	 * @Note
	 * <b>Author:</b> Michael
	 * <br><b>Date:</b> 2018年3月26日 上午10:45:36
	 */
	ResultInfo editBussinessChance(BussinessChance bussinessChance);
    /**
     * 
     * <b>Description:</b>根据商机id查询商机信息
     * @param bussinessChance
     * @return BussinessChance
     * @Note
     * <b>Author：</b> scott.zhu
     * <b>Date:</b> 2019年3月4日 下午1:06:30
     */
    BussinessChance getBussinessChanceInfo(BussinessChance bussinessChance);
    /**
     * 
     * <b>Description:</b>更新商机信息
     * @param bussinessChance
     * @return Boolean
     * @Note
     * <b>Author：</b> scott.zhu
     * <b>Date:</b> 2019年3月4日 下午2:10:15
     */
    Boolean saveAddBussinessStatus(BussinessChance bussinessChance);

	/**
	 * 获取指定客户未完结商机列表
	 * @param BussinessChanceVo
	 * @param page
	 * @return
	 */
	List<BussinessChanceVo> getTraderHistoryListPage(BussinessChanceVo bussinessChanceVo, Page page);

	/**
	 * 获取当前客户未处理和报价中商机
	 * @param traderId
	 * @return
	 */
    List<BussinessChanceVo> getTraderHasHistoryBussiness(Integer traderId);

    /**
     * <b>Description:</b>如果合并商机就发送消息给归属销售<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/6/16
     */
    void sendMessageIfMerge(Integer id,BussinessChance bussinessChance);

    /**
     * <b>Description:</b>获取可以合并商机的归属销售<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/6/18
     */
    ResultInfo<User> getOldChanceOwner(String mobile);

	/**
	 * @description:  获取关联商机和报价单信息
	 * @return:
	 * @author: Strange
	 * @date: 2020/7/8
	 **/
    ResultInfo getBussinessChanceAndQuoteInfo(Integer bussinessChanceId);
    /**
     * <b>Description:</b>关闭被关联的商机<br>
     * @param
     * @return
     * @Note
     * <b>@Author:calvin</b>
     * <br><b>@Date:</b> 2020/9/24
     */
    void closeBncAfterLink(BussinessChance bussinessChance);

    /**
     * <b>Description:</b>根据条件，更新商机状态<br>
     * @param
     * @return
     * @Note
     * <b>@Author:calvin</b>
     * <br><b>@Date:</b> 2020/8/27
     */
    void updateBcStatusByTerm(BussinessChance bussinessChance);

    int updateEditComments(BussinessChance bussinessChance);

	/**
	 * <b>Description:</b>订单全部付款后更新商机状态<br>
	 * @param
	 * @return
	 * @Note
	 * <b>@Author:calvin</b>
	 * <br><b>@Date:</b> 2020/8/28
	 */
	void updateBcStatusIfSaleorderPaid(Integer saleorderId,Integer status);

	/**
	 * <b>Description:</b><br>
	 * 保存商机确认客户信息
	 *
	 * @param bussinessChance
	 * @return
	 * @Note <b>Author:</b> east <br>
	 * 		<b>Date:</b> 2017年6月30日 下午6:01:21
	 */
	TraderCustomerVo saveConfirmCustomer(BussinessChance bussinessChance, TraderContact tc) throws Exception;

	/**
	 *
	 * @param parentId
	 * @return
	 */
	List<SysOptionDefinition> getBussInquiryData(Integer parentId);

	/**
	 * 根据客户手机号 和 source 查询商机历史
	 * @param loginMobile 客户手机号
	 * @param source source
	 * @return 商机历史列表
	 */
	BussinessChance listBussinessByMobileAndSource(String loginMobile,Integer source);

	/**
	 * 更新商机的首次查看时间
	 *
	 * @param bussinessChance 商机信息
	 */
	void updateFirstViewTime(BussinessChance bussinessChance);

	/**
	 * 组装商机渠道分类数据
	 * @return
	 */
    List<NewSourceDicDto> getNewSource(List<SysOptionDefinition> newSourceListAll);
}
