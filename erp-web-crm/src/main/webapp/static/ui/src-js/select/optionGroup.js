Vue.component('ui-option-group', {
    template: `
        <div class="vd-ui-group">
            <li class="vd-ui-group-title"><p>{{ group }}</p></li>
            <ui-option
                v-for="(item,index) in list"
                :key="index"
                :value="item.value"
                :label="item.label"
                :name="item.name"
                :disabled="item.disabled">
            </ui-option>
            <slot></slot>
        </div>
    `,
    props: {
        group: String,
        data: {
            type: Array,
            default: ()=> {
                return []
            }
        }
    },
    inject: {
        select: {
            default: ''
        }
    },
    computed: {
        list() {
            return this.data;
        },
    }
})