package com.vedeng.erp.saleorder.service.impl;


import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.Constants;
import com.vedeng.common.constant.OrderConstant;
import com.vedeng.common.util.HttpRestClientUtil;
import com.vedeng.erp.finance.service.InvoiceApiService;
import com.vedeng.erp.saleorder.dao.SaleOrderMapper;
import com.vedeng.erp.saleorder.domain.entity.SaleorderEntity;
import com.vedeng.erp.saleorder.service.JcWeChatApiService;
import com.vedeng.erp.system.dto.SysOptionDefinitionDto;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.SysOptionDefinitionApiService;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.passport.api.wechat.dto.req.ReqTemplateVariable;
import com.vedeng.passport.api.wechat.dto.res.ResWeChatDTO;
import com.vedeng.passport.api.wechat.dto.template.TemplateVar;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @Description 集采微信消息推送
 * @createTime 2021年02月24日 16:44:00
 */

@Service
@Slf4j
public class JcWeChatServiceImpl implements JcWeChatApiService {


    @Value("${jcvx_invoice_id}")
    private String jcVxInvoiceId;

    @Value("${jcvx_serviceUrl}")
    private String jcVxServiceUrl;

    @Autowired
    private SaleOrderMapper saleOrderMapper;

    @Autowired
    private UserApiService userApiService;

    @Autowired
    private InvoiceApiService invoiceApiService;

    @Autowired
    private SysOptionDefinitionApiService sysOptionDefinitionApiService;


    @Override
    public void sendMsg(Integer saleorderId) {
        log.info("集采订单全部开票发送消息,销售id:{}", saleorderId);
        try {
            //全部开票发送消息
            sendInvoiceTemplateMsg(saleorderId);
        } catch (Exception e) {
            log.error("集采订单全部开票发送消息异常", e);
        }
    }

    private void sendInvoiceTemplateMsg(Integer saleOrderId) {
        ReqTemplateVariable reqTemp = new ReqTemplateVariable();
        SaleorderEntity saleorder = saleOrderMapper.findBySaleorderId(saleOrderId);
        log.info("jc微信消息 订单信息:{}", JSON.toJSONString(saleorder));
        if (!OrderConstant.ORDER_TYPE_JCF.equals(saleorder.getOrderType()) && !OrderConstant.ORDER_TYPE_JCO.equals(saleorder.getOrderType())) {
            return;
        }
        if (!Constants.TWO.equals(saleorder.getInvoiceStatus())) {
            return;
        }

        reqTemp.setMobile(saleorder.getTraderContactMobile());
        reqTemp.setTemplateId(jcVxInvoiceId);

        TemplateVar first = new TemplateVar();
        first.setValue("尊敬的客户，您已成功开票:" + "\r\n");
        //订单的订单编号
        TemplateVar keyword1 = new TemplateVar();
        keyword1.setValue(saleorder.getSaleorderNo());

        //发票抬头：取开票记录列表页，发票号对应的“客户名称”信息
        TemplateVar keyword2 = new TemplateVar();
        keyword2.setValue(saleorder.getTraderName());

        SysOptionDefinitionDto definition = sysOptionDefinitionApiService.getOptionDefinitionById(saleorder.getInvoiceType());
        //发票类型：取发票类型
        TemplateVar keyword3 = new TemplateVar();
        keyword3.setValue(definition.getTitle());

        //开票金额：所有发票号的“发票金额”合计
        BigDecimal saleOpenInvoiceAmount = invoiceApiService.getSaleOpenInvoiceAmount(saleorder.getSaleorderId());
        TemplateVar keyword4 = new TemplateVar();
        keyword4.setValue(saleOpenInvoiceAmount.toString());

        UserDto user = this.getUser(saleorder.getTraderId());
        TemplateVar remark = getRemark(user);

        reqTemp.setFirst(first);
        reqTemp.setKeyword1(keyword1);
        reqTemp.setKeyword2(keyword2);
        reqTemp.setKeyword3(keyword3);
        reqTemp.setKeyword4(keyword4);
        reqTemp.setRemark(remark);

        sendMsg(reqTemp);
    }

    private UserDto getUser(Integer traderId) {
        return userApiService.getUserByTraderId(traderId);
    }

    private TemplateVar getRemark(UserDto user) {
        TemplateVar remark = new TemplateVar();
        remark.setValue("感谢您对医械购的支持与信任，如有疑问请联系:" + user.getUsername() + " " + user.getRealName() + "  " + user.getMobile());
        return remark;
    }

    /**
     * todo: 调用old模块方法，等有时间重构
     *
     * @param reqTemp reqTemp
     */
    private void sendMsg(ReqTemplateVariable reqTemp) {
        // 接口返回
        TypeReference<ResWeChatDTO> type = new TypeReference<ResWeChatDTO>() {
        };
        // 接口请求头
        HashMap<String, String> heads = new HashMap<String, String>();
        // 接口版本0
        heads.put(CommonConstants.INTER_VERSION, CommonConstants.INTER_VERSION_VALUE);
        // 调用接口
        HttpRestClientUtil.post2(jcVxServiceUrl + "/weChatContent/sendTemplateMessage", type, heads, reqTemp);
    }


}
