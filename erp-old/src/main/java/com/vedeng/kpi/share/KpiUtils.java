package com.vedeng.kpi.share;

import com.google.common.collect.Lists;
import com.vedeng.kpi.model.DO.KpiDailyCountDo;
import com.vedeng.kpi.model.DO.KpiOrderLogDo;
import com.vedeng.kpi.model.DTO.KpiDataQueryDto;
import com.vedeng.kpi.model.DTO.KpiUserInfoDto;
import org.joda.time.Months;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 工具类.
 * @jira: VDERP-2376【五行剑法】规则修改.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/6/9 3:12 下午.
 * @author: Tomcat.Hui.
 */
public class KpiUtils {
    /**
     * @description: 获取当前月第一天.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/6/9 3:36 下午.
     * @author: Tomcat.Hui.
     * @return: java.util.Date.
     * @throws: .
     */
    public static Date getMonthStart(Date date){
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        return Date.from(instant.atZone(zoneId).toLocalDate().withDayOfMonth(1).atStartOfDay(zoneId).toInstant());
    }

    /**
     * 根据字符串时间获取月初日期.
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/9/1 1:42 下午.
     * @author: Tomcat.Hui.
     * @param dateStr: .
     * @return: java.util.Date.
     * @throw: .
     */
    public static Date getMonthStart(String dateStr) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        Date date = sdf.parse(dateStr);
        return getMonthStart(date);
    }

    public static Date getMonthEnd(Date date){
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        return Date.from(instant.atZone(zoneId).toLocalDate()
                .with(TemporalAdjusters.lastDayOfMonth()).atStartOfDay(zoneId).toInstant());
    }

    /**
     * @description: 获取昨天.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/6/16 2:39 下午.
     * @author: Tomcat.Hui.
     * @param date: .
     * @return: java.util.Date.
     * @throws: .
     */
    public static Date getYesterday(Date date){
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        return Date.from(instant.atZone(zoneId).toLocalDate().plusDays(-1).atStartOfDay(zoneId).toInstant());
    }

    /**
     * @description: 获取上月最后一天.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/6/16 2:39 下午.
     * @author: Tomcat.Hui.
     * @param date: .
     * @return: java.util.Date.
     * @throws: .
     */
    public static Date getLastMonthLastDay(Date date){
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        return Date.from(instant.atZone(zoneId).toLocalDate().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth()).atStartOfDay(zoneId).toInstant());
    }

    /**
     * @description: 获取往前n月的月份列表.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/1 9:59 上午.
     * @author: Tomcat.Hui.
     * @param date: .
     * @param offset: .
     * @return: java.util.List<java.lang.String>.
     * @throws: .
     */
    public static List<String> getMonthListStr (Date date, Integer offset) throws ParseException {
        Date lastMont = getMonthStart(date);
        Instant instant = lastMont.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        List<String> monthList = Lists.newArrayList();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM");


        // add by Tomcat.Hui 2020/8/10 11:42 上午 .Desc: VDERP-3094【五行剑法】优化得分排名和转化率逻辑. start
        //历史月份的筛选项只需要从2020.7月份开始即可，在此之前的无需展示
        int monthDiff = Months.monthsBetween(formatter.parseDateTime("2020-07"),
                formatter.parseDateTime(sdf.format(lastMont))).getMonths();
        if (monthDiff <= 12) {
            offset = monthDiff;
        }
        // add by Tomcat.Hui 2020/8/10 11:42 上午 .Desc: VDERP-3094【五行剑法】优化得分排名和转化率逻辑. end

        for (int i = 0;i <= offset ; i++) {
            Date monthEnd = Date.from(instant.atZone(zoneId).toLocalDate().minusMonths(i)
                    .with(TemporalAdjusters.firstDayOfMonth()).atStartOfDay(zoneId).toInstant());

            monthList.add(sdf.format(monthEnd));
        }
        return monthList;
    }

    /**
     * 获取0点0分0秒.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/6/17 9:29 上午.
     * @author: Tomcat.Hui.
     * @param date: .
     * @return: java.util.Date.
     * @throws: .
     */
    public static Date getDateStart(Date date){
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        return Date.from(instant.atZone(zoneId).toLocalDate().atStartOfDay(zoneId).toInstant());
    }

    /**
     * 获取23:59:59.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/10 1:32 下午.
     * @author: Tomcat.Hui.
     * @param date: .
     * @return: java.util.Date.
     * @throw: .
     */
    public static Date getDateEnd(Date date){
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()),zoneId);;
        LocalDateTime endOfDay = localDateTime.with(LocalTime.MAX);
        return Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取日期差.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/10 4:26 下午.
     * @author: Tomcat.Hui.
     * @param dateBefore: .
     * @param dateAfter: .
     * @return: java.lang.Long.
     * @throw: .
     */
    public static Long getDateDiff(Date dateBefore,Date dateAfter){
        return (dateAfter.getTime() - dateBefore.getTime()) / 1000* 60 * 60 * 24;
    }

    public static void main(String[] args) throws ParseException {

        System.out.println(getDateEnd(new Date()));
    }
}
