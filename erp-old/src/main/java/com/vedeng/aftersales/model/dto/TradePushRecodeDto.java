package com.vedeng.aftersales.model.dto;

import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TradePushRecodeDto {
    /**
     * <pre>
     * 售后单号
     * 表字段 : V_TRADE_PUS@H_RECODE.AFTER_SALES_NO
     * </pre>
     *
     */
    private String afterSalesNo;
    /**
     * <pre>
     * 记账编号
     * 表字段 : V_TRADE_PUSH_RECODE.BILL_NO
     * </pre>
     *
     */
    private String billNo;
    /**
     * <pre>
     * 业务类型
     * 表字段 : V_TRADE_PUSH_RECODE.BUSINESS_TYPE
     * </pre>
     *
     */
    private String businessType;
    /**
     * <pre>
     * 交易时间
     * 表字段 : V_TRADE_PUSH_RECODE.TRADER_TIME
     * </pre>
     *
     */
    private Long traderTime;
    /**
     * <pre>
     * 交易主体
     * 表字段 : V_TRADE_PUSH_RECODE.TRADER_SUBJECT
     * </pre>
     *
     */
    private String traderSubject;
    /**
     * <pre>
     * 交易金额
     * 表字段 : V_TRADE_PUSH_RECODE.TRADER_PRICE
     * </pre>
     *
     */
    private BigDecimal traderPrice;
    /**
     * <pre>
     * 交易方式
     * 表字段 : V_TRADE_PUSH_RECODE.TRADER_WAY
     * </pre>
     *
     */
    private String traderWay;
    /**
     * <pre>
     * 交易备注
     * 表字段 : V_TRADE_PUSH_RECODE.TRADER_REMAKE
     * </pre>
     *
     */
    private String traderRemake;

    private String traderName;

}
