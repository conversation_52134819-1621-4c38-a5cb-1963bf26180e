function ch(authorizationApplyId) {
	/*var sug=$('#suggestion').val();*/
	checkLogin();

	$.ajax({
		type : "POST",
		url : page_url+"/order/quote/authorizationWithdrow.do",
		data :{'authorizationApplyId':authorizationApplyId},
		dataType : 'json',
		success : function(data) {
			refreshPageList()
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});

}