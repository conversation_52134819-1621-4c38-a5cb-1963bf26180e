<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.saleorder.dao.SaleOrderGoodsDetailMapper">

    <select id="getRelatedDetail" resultType="com.vedeng.erp.saleorder.dto.SaleOrderGoodsDetailDto">
        select b.SALEORDER_ID,
        b.SALEORDER_GOODS_ID,
        a.SALEORDER_NO,
        b.NUM,
        b.PRICE,
        b.DELIVERY_CYCLE,
        b.GOODS_ID,
        b.INSIDE_COMMENTS,
        b.GOODS_COMMENTS,
        a.USER_ID,
        a.TERMINAL_TRADER_NAME,
        c.USERNAME APPLICANT_NAME,
        a.ORDER_TYPE,
        b.DELIVERY_DIRECT
        from T_SALEORDER a
        left join T_SALEORDER_GOODS b on a.SALEORDER_ID = b.SALEORDER_ID
        left join T_USER c on a.USER_ID = c.USER_ID
        where SALEORDER_GOODS_ID in
        <foreach item="item" collection="saleorderGoodsIdList" open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
        order by a.SATISFY_DELIVERY_TIME
    </select>

    <select id="getExpenseDetail" resultType="com.vedeng.erp.saleorder.dto.SaleOrderGoodsDetailDto">
        select a.SALEORDER_ID,a.SALEORDER_GOODS_ID,a.NUM,a.DELIVERY_NUM,a.DELIVERY_STATUS,a.ARRIVAL_STATUS from T_SALEORDER_GOODS a left join V_CORE_SKU b on a.GOODS_ID = b.SKU_ID
        where b.STATUS = 1 and b.IS_VIRTURE_SKU = 1 and a.SALEORDER_GOODS_ID in
        <foreach item="item" collection="saleorderGoodsIdList" open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>


    <update id="updateExpenseGoodsDeliveryAndArrivalStatus">
        update T_SALEORDER_GOODS SET
        DELIVERY_TIME = CASE
        <foreach collection="saleOrderGoodsDetailDtos" item="item" index="index">
            WHEN SALEORDER_GOODS_ID = #{item.saleorderGoodsId} THEN #{item.deliveryTime}
        </foreach>
        END,
        ARRIVAL_TIME = CASE
        <foreach collection="saleOrderGoodsDetailDtos" item="item" index="index">
            WHEN SALEORDER_GOODS_ID = #{item.saleorderGoodsId} THEN #{item.arrivalTime}
        </foreach>
        END,
        DELIVERY_NUM = CASE
        <foreach collection="saleOrderGoodsDetailDtos" item="item" index="index">
            WHEN SALEORDER_GOODS_ID = #{item.saleorderGoodsId} THEN #{item.deliveryNum}
        </foreach>
        END,
        DELIVERY_STATUS = CASE
        <foreach collection="saleOrderGoodsDetailDtos" item="item" index="index">
            WHEN SALEORDER_GOODS_ID = #{item.saleorderGoodsId} THEN #{item.deliveryStatus}
        </foreach>
        END,
        ARRIVAL_STATUS = CASE
        <foreach collection="saleOrderGoodsDetailDtos" item="item" index="index">
            WHEN SALEORDER_GOODS_ID = #{item.saleorderGoodsId} THEN #{item.arrivalStatus}
        </foreach>
        END
        where SALEORDER_GOODS_ID in
        <foreach collection="saleOrderGoodsDetailDtos" index="index" item="item" open="(" separator="," close=")">
            #{item.saleorderGoodsId}
        </foreach>

    </update>

    <select id="findGoodsDetailByOrderId"
            resultType="com.vedeng.erp.saleorder.dto.SaleOrderGoodsDetailDto">
        select a.SALEORDER_NO,
               b.SALEORDER_GOODS_ID,
               b.NUM,
               b.GOODS_ID,
               b.DELIVERY_STATUS,
               b.ARRIVAL_STATUS,
               cs.IS_VIRTURE_SKU,
               cs.STATUS,
               scc.IS_NEED_PURCHASE,
               b.SKU SKU_NO,
               cs.SKU_NO skuNo,
               b.GOODS_NAME,
               b.UNIT_NAME,
               b.MODEL,
               b.BRAND_NAME,
               b.PRICE

        from T_SALEORDER a
                 left join T_SALEORDER_GOODS b
                           on a.SALEORDER_ID = b.SALEORDER_ID
                 left join V_CORE_SKU cs
                           on b.GOODS_ID = cs.SKU_ID
                 left join T_SYS_COST_CATEGORY scc
                           on cs.COST_CATEGORY_ID = scc.COST_CATEGORY_ID
        where a.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
          and b.SALEORDER_GOODS_ID is not null
          and b.IS_DELETE = 0
    </select>

    <select id="findAllGoodsBySaleOrderGoods" resultType="com.vedeng.erp.saleorder.dto.SaleOrderGoodsDetailDto">
        select a.SALEORDER_NO,
        b.SALEORDER_GOODS_ID,
        b.NUM,
        b.GOODS_ID,
        b.DELIVERY_STATUS,
        b.ARRIVAL_STATUS,
        cs.IS_VIRTURE_SKU,
        cs.STATUS,
        scc.IS_NEED_PURCHASE,
        b.SKU SKU_NO
        from T_SALEORDER a
        left join T_SALEORDER_GOODS b
        on a.SALEORDER_ID = b.SALEORDER_ID
        left join V_CORE_SKU cs
        on b.GOODS_ID = cs.SKU_ID
        left join T_SYS_COST_CATEGORY scc
        on cs.COST_CATEGORY_ID = scc.COST_CATEGORY_ID
        where b.SALEORDER_GOODS_ID is not null
        and b.IS_DELETE = 0
        and a.SALEORDER_ID in (select SALEORDER_ID
        from T_SALEORDER_GOODS a
        left join V_CORE_SKU b on a.GOODS_ID = b.SKU_ID
        where a.IS_DELETE = 0
        and (b.IS_VIRTURE_SKU != 1 or b.IS_VIRTURE_SKU is null)
        and b.STATUS = 1
        and SALEORDER_GOODS_ID in
        <foreach item="item" collection="saleorderGoodsIdList" open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>)
    </select>

    <select id="getSaleOrderGoodsBySaleorderIds" resultType="com.vedeng.erp.mobile.dto.SaleOrderGoodsListResultDto">
        SELECT A.SALEORDER_GOODS_ID as saleordergoodsId,
               A.SALEORDER_ID as saleorderId,
               A.GOODS_ID as goodsId,
               A.SKU as sku,
               A.GOODS_NAME as goodsName,
               A.PRICE as price,
               (ifnull(A.NUM, 0) - ifnull(A.AFTER_RETURN_NUM, 0)) as num
        FROM T_SALEORDER_GOODS A
        WHERE A.IS_DELETE = 0
            AND A.SALEORDER_ID IN
            <foreach item="item" collection="saleorderIds" open="(" separator="," close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
        order by A.SALEORDER_GOODS_ID, A.DELIVERY_DIRECT desc;
    </select>
</mapper>