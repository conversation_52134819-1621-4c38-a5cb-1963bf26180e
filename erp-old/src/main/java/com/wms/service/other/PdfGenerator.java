package com.wms.service.other;

import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
/**
 * <AUTHOR> ai
 */
@Slf4j
public class PdfGenerator {

    /**
     * 将多个图片字节数组转换为PDF输入流
     * @param imageBytesList 图片字节数组列表
     * @return PDF输入流
     */
    public static InputStream generatePdfFromImages(List<byte[]> imageBytesList) {
        if (imageBytesList == null || imageBytesList.isEmpty()) {
            return null;
        }

        try (PDDocument document = new PDDocument()) {
            // 遍历处理每个图片
            for (byte[] imageBytes : imageBytesList) {
                if (imageBytes == null || imageBytes.length == 0) {
                    continue;
                }

                // 创建图片对象
                PDImageXObject image = PDImageXObject.createFromByteArray(document, imageBytes, null);

                // 创建页面
                PDPage page = new PDPage(PDRectangle.A4);
                document.addPage(page);

                // 计算图片在页面中的位置和大小
                float pageWidth = page.getMediaBox().getWidth();
                float pageHeight = page.getMediaBox().getHeight();
                float imageWidth = image.getWidth();
                float imageHeight = image.getHeight();

                // 计算缩放比例，确保图片适应页面
                float scale = Math.min(pageWidth / imageWidth, pageHeight / imageHeight);
                float scaledWidth = imageWidth * scale;
                float scaledHeight = imageHeight * scale;

                // 计算居中位置
                float x = (pageWidth - scaledWidth) / 2;
                float y = (pageHeight - scaledHeight) / 2;

                // 将图片写入页面
                try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
                    contentStream.drawImage(image, x, y, scaledWidth, scaledHeight);
                }
            }

            // 将PDF文档转换为字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            document.save(outputStream);
            
            // 返回输入流
            return new ByteArrayInputStream(outputStream.toByteArray());

        } catch (IOException e) {
            log.error("",e);
            return null;
        }
    }

    /**
     * 测试代码
     */
    public static void main(String[] args) {
        // 测试用例
        try {
            // 假设我们有一些测试图片数据
            List<byte[]> testImages =new ArrayList<>();


                // 这里应该是你的图片字节数组
            testImages.add(getTestImage1Bytes());
            testImages.add(getTestImage2Bytes());


            // 生成PDF输入流
            try (InputStream pdfInputStream = generatePdfFromImages(testImages)) {
                if (pdfInputStream != null) {
                    // 可以在这里使用生成的PDF输入流
                    // 例如，保存到文件系统
                    try (FileOutputStream fos = new FileOutputStream("/Users/<USER>/Documents/图片/output.pdf")) {
                        byte[] buffer = new byte[1024];
                        int length;
                        while ((length = pdfInputStream.read(buffer)) > 0) {
                            fos.write(buffer, 0, length);
                        }
                    }
                    System.out.println("PDF生成成功！");
                }
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取测试图片数据（示例方法）
     */
    private static byte[] getTestImage1Bytes() throws IOException {
        // 这里应该返回实际的图片字节数组
        // 示例：从文件读取图片
        return Files.readAllBytes(Paths.get("/Users/<USER>/Documents/图片/小图.jpg"));
    }

    private static byte[] getTestImage2Bytes() throws IOException {
        // 这里应该返回实际的图片字节数组
        // 示例：从文件读取图片
        return Files.readAllBytes(Paths.get("/Users/<USER>/Documents/图片/downloadDesc (2).png"));
    }
}