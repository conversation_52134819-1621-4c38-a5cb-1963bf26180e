.dlg-sku-push-wrap {
  display: flex;
}
.dlg-sku-push-wrap .cnt-title {
  font-size: 14px;
  margin-bottom: 10px;
}
.dlg-sku-push-wrap .cnt-select-label {
  color: #999;
  padding: 10px 0;
}
.dlg-sku-push-wrap .left-wrap {
  width: 50%;
  padding-right: 10px;
  border-right: 1px solid #edf0f2;
  height: 480px;
}
.dlg-sku-push-wrap .left-wrap .input-textarea {
  height: 56px;
  flex: 1;
  resize: none;
  margin-right: 5px;
}
.dlg-sku-push-wrap .left-wrap .cnt-textarea-wrap {
  border-bottom: 1px solid #edf0f2;
  display: flex;
  padding-bottom: 10px;
  position: relative;
}
.dlg-sku-push-wrap .left-wrap .cnt-textarea-wrap .nouse-tip {
  position: absolute;
  top: 60px;
  left: 0;
  padding: 5px 10px;
  background: #fff;
  border-radius: 3px;
  box-shadow: 0px 3px 10px rgba(0, 0, 0, 0.1);
  padding-right: 30px;
}
.dlg-sku-push-wrap .left-wrap .cnt-textarea-wrap .nouse-tip .icon-delete {
  position: absolute;
  right: 5px;
  top: 7px;
  line-height: 1;
  cursor: pointer;
}
.dlg-sku-push-wrap .left-wrap .cnt-textarea-wrap .nouse-tip .icon-delete:hover {
  color: #f60;
}
.dlg-sku-push-wrap .left-wrap .cnt-sku-list {
  max-height: 350px;
  overflow-y: auto;
  display: flex;
  flex-wrap: wrap;
}
.dlg-sku-push-wrap .left-wrap .cnt-sku-list .cnt-sku-item {
  display: flex;
  height: 35px;
  align-items: center;
  padding: 0 10px;
  cursor: pointer;
  width: calc((100% - 20px)/3);
  margin-right: 10px;
  box-sizing: border-box;
}
.dlg-sku-push-wrap .left-wrap .cnt-sku-list .cnt-sku-item:nth-child(3n) {
  margin-right: 0;
}
.dlg-sku-push-wrap .left-wrap .cnt-sku-list .cnt-sku-item:hover {
  background: #f5f7fa;
}
.dlg-sku-push-wrap .left-wrap .cnt-sku-list .cnt-sku-item .item-no {
  white-space: nowrap;
  margin-right: 10px;
  flex: 1;
}
.dlg-sku-push-wrap .left-wrap .cnt-sku-list .cnt-sku-item .icon-delete {
  font-size: 16px;
}
.dlg-sku-push-wrap .left-wrap .cnt-sku-list .cnt-sku-item .icon-delete:hover {
  color: #f60;
}
.dlg-sku-push-wrap .right-wrap {
  padding-left: 10px;
  width: 50%;
}
.dlg-sku-push-wrap .right-wrap .input-text {
  width: 100%;
}
.dlg-sku-push-wrap .right-wrap .cnt-input-wrap {
  height: 67px;
  border-bottom: 1px solid #edf0f2;
  position: relative;
}
.dlg-sku-push-wrap .right-wrap .cnt-input-list {
  position: absolute;
  background: #fff;
  width: 100%;
  box-shadow: 0px 3px 10px rgba(0, 0, 0, 0.1);
  padding: 5px 0;
  display: none;
  max-height: 350px;
  overflow-y: auto;
}
.dlg-sku-push-wrap .right-wrap .cnt-input-list .empty {
  color: #999;
  text-align: center;
  padding: 50px 0;
}
.dlg-sku-push-wrap .right-wrap .cnt-input-list .cnt-list-item {
  padding: 0 10px;
  display: flex;
  align-items: center;
  height: 35px;
  cursor: pointer;
}
.dlg-sku-push-wrap .right-wrap .cnt-input-list .cnt-list-item:hover {
  background: #f5f7fa;
}
.dlg-sku-push-wrap .right-wrap .cnt-input-list .cnt-list-item .icon-checkbox1 {
  color: #666;
}
.dlg-sku-push-wrap .right-wrap .cnt-input-list .cnt-list-item .icon-checkbox2 {
  display: none;
  color: #09f;
}
.dlg-sku-push-wrap .right-wrap .cnt-input-list .cnt-list-item .item-name {
  flex: 1;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  margin-left: 10px;
}
.dlg-sku-push-wrap .right-wrap .cnt-input-list .cnt-list-item.checked .icon-checkbox1 {
  display: none;
}
.dlg-sku-push-wrap .right-wrap .cnt-input-list .cnt-list-item.checked .icon-checkbox2 {
  display: block;
}
.dlg-sku-push-wrap .right-wrap .cnt-mall-list {
  max-height: 320px;
  overflow-y: auto;
}
.dlg-sku-push-wrap .right-wrap .cnt-mall-list .cnt-mall-item {
  display: flex;
  align-items: center;
  height: 35px;
  padding: 0 10px;
}
.dlg-sku-push-wrap .right-wrap .cnt-mall-list .cnt-mall-item:hover {
  background-color: #f5f7fa;
}
.dlg-sku-push-wrap .right-wrap .cnt-mall-list .cnt-mall-item .item-txt {
  flex: 1;
}
.dlg-sku-push-wrap .right-wrap .cnt-mall-list .cnt-mall-item .icon-delete {
  font-size: 16px;
}
.dlg-sku-push-wrap .right-wrap .cnt-mall-list .cnt-mall-item .icon-delete:hover {
  color: #f60;
  cursor: pointer;
}
.push-loading-wrap {
  position: fixed;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 2111;
  font-size: 14px;
  top: 0;
  left: 0;
}
.push-loading-wrap .push-loading-cnt {
  width: 480px;
  min-height: 100px;
  overflow-y: auto;
  background: #fff;
  border-radius: 5px;
  padding: 20px;
  position: relative;
}
.push-loading-wrap .push-loading-close {
  position: absolute;
  right: 10px;
  top: 0;
  display: none;
  color: #999;
  cursor: pointer;
}
.push-loading-wrap .push-loading-close .icon-delete {
  font-size: 24px;
}
.push-loading-wrap .push-loading-close:hover {
  color: #333;
}
@keyframes liner {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
  }
}
.push-loading-wrap .push-loading-txt {
  margin-bottom: 5px;
  color: #f60;
}
.push-loading-wrap .push-loading-txt .finish-txt {
  margin-bottom: 10px;
  color: #88DF89;
}
.push-loading-wrap .push-loading-status {
  height: 10px;
  background: #09f;
  animation: liner 3s linear infinite;
  margin-bottom: 10px;
}
.push-loading-wrap .push-loading-block {
  margin-bottom: 10px;
}
.push-loading-wrap .push-block-label {
  color: #666;
  margin-bottom: 5px;
}
.push-loading-wrap .push-error-list {
  max-height: 300px;
  overflow-y: auto;
}
.push-loading-wrap .push-error-item {
  display: flex;
  margin-bottom: 5px;
  align-items: baseline;
}
.push-loading-wrap .push-error-item .error-skuNo {
  margin-right: 10px;
}
