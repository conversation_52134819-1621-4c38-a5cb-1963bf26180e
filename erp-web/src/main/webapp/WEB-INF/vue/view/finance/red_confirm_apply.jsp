<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<div id="app" style="display: none;">
    <el-form ref="confirmForm" :model="form" :rules="rules" label-width="120px">
        <el-row>
            <el-col :span="12">
                <el-form-item label="申请原因：">
                    <el-select v-model="form.applyReason" placeholder="请选择" @change="reasonChange">
                        <el-option label="开票有误" value="01"></el-option>
                        <el-option label="销货退回" value="02"></el-option>
                        <el-option label="服务中止" value="03"></el-option>
                        <el-option label="销售折让" value="04"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>

            <el-col :span="12">
                <el-form-item label="红冲范围：">
                    <el-radio-group v-model="form.redConfirmationScope" @change="scopeChange">
                        <el-radio :label="0">全部红冲</el-radio>
                        <el-radio :label="1" :disabled="canPartRed">部分红冲</el-radio>
                    </el-radio-group>

                    <template>
                        <el-popover
                                placement="top-start"
                                title=""
                                width="300"
                                trigger="hover">
                                <div class="custom-content">
                                    1. 蓝票未确认用途，不支持部分红冲<br>
                                    2. 申请原因为"开票有误"， 不支持部分红冲<br/>
                                </div>
                                <i class="el-icon-question" style="margin-left: 35px" slot="reference"></i>
                        </el-popover>
                    </template>

                </el-form-item>
            </el-col>
        </el-row>

        <el-row style="margin-top: 2%">
            <el-col :span="12" style="padding-left: 35px">
                <span>红字明细列表</span>
            </el-col>

            <el-col :span="12" style="padding-left: 35px">
                <span>蓝票号码：{{this.invoiceRedConfirmationDto.blueInvoiceNo}}</span>
            </el-col>
        </el-row>

        <el-row style="padding-left: 35px; margin-top: 10px" v-if="this.form.redConfirmationScope == 1">
            <el-radio-group v-model="form.detailRange" @change="detailRangeChange">
                <el-radio :label="1">显示全部项目</el-radio>
                <el-radio :label="2">显示未关联项目</el-radio>
            </el-radio-group>
        </el-row>

        <el-row style="padding-left: 35px; margin-top: 10px;height: 500px;overflow: auto;" v-if="this.form.redConfirmationScope == 0">
            <el-table
                    :data="blueInvoiceDetailList"
                    border
                    stripe
                    key="allRed"
                    :header-cell-style="{'text-align':'center'}"
                    :cell-style="{'text-align':'center'}"
                    style="width: 100%">
                <el-table-column
                        label="项目名称"
                        prop="projectName">
                </el-table-column>
                <el-table-column
                        label="规格型号"
                        min-width="20%"
                        prop="specifications">
                </el-table-column>
                <el-table-column
                        label="单位"
                        min-width="15%"
                        prop="unit">
                </el-table-column>
                <el-table-column
                        label="税收分类编码"
                        min-width="25%"
                        prop="taxCategoryNo">
                </el-table-column>
                <el-table-column
                        label="红字数量"
                        min-width="15%">
                    <template slot-scope="scope">
                        <el-row v-if="form.applyReason != '04'">
                            <span>{{scope.row.quantity}}</span>
                        </el-row>
                    </template>
                </el-table-column>
                <el-table-column
                        label="红字金额"
                        min-width="15%">
                    <template slot-scope="scope">
                        <span>{{(Number(scope.row.amount)).toFixed(2)}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="红字价税合计"
                        min-width="15%">
                    <template slot-scope="scope">
                        <span>{{Number(scope.row.pricePlusTaxes).toFixed(2)}}</span>
                    </template>
                </el-table-column>
            </el-table>
        </el-row>

        <el-row style="padding-left: 35px; margin-top: 10px; height: 500px;overflow: auto;" v-if="this.form.redConfirmationScope == 1">
            <el-table
                    :data="form.redInvoiceDetailList"
                    border
                    stripe
                    key="partRed"
                    :header-cell-style="{'text-align':'center'}"
                    :cell-style="{'text-align':'center'}"
                    :row-key="row => row.afterSalesGoodsId"
                    :expand-row-keys="this.expandRowKeys"
                    style="width: 100%">
                <el-table-column type="expand">
                    <template slot-scope="props">
                        <el-table
                                :data="props.row.chosenBlueInvoiceDetail"
                                border
                                stripe
                                :header-cell-style="{'text-align':'center', 'background-color': '#f0f9eb'}"
                                :cell-style="{'text-align':'center', 'background-color': '#f0f9eb'}"
                                style="width: 100%">
                            <el-table-column
                                    label="匹配度"
                                    min-width="20%">
                                <template slot-scope="scope">
                                    <span>{{scope.row.matchingDegree}}%</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                    prop="projectName"
                                    label="项目名称">
                            </el-table-column>
                            <el-table-column
                                    label="规格型号"
                                    min-width="15%"
                                    prop="specifications">
                            </el-table-column>
                            <el-table-column
                                    label="单位"
                                    min-width="15%"
                                    prop="unit">
                            </el-table-column>
                            <el-table-column
                                    label="税收分类编码"
                                    min-width="25%"
                                    prop="taxCategoryNo">
                            </el-table-column>

                            <el-table-column label="红字数量" prop="quantity" min-width="15%">
                                <template slot-scope="scope">
                                    <el-form-item
                                                  :prop="'redInvoiceDetailList.'+props.$index+'.chosenBlueInvoiceDetail.'+scope.$index+'.quantity'"
                                                  :rules="[
                                                  {
                                                  ...rules.quantity[1],
                                                  row: props.row,
                                                  }]"
                                                  label-width="0">
                                        <el-input v-model="scope.row.quantity" v-if="form.applyReason != '04'"
                                                  @input="handleInputQuantity(scope.row)"
                                                  @change="quantityChanged(scope.row)">
                                        </el-input>
                                    </el-form-item>
                                </template>
                            </el-table-column>

                            <el-table-column label="红字金额" prop="amount" min-width="15%">
                                <template slot-scope="scope">
                                    <el-form-item
                                                :prop="'redInvoiceDetailList.'+props.$index+'.chosenBlueInvoiceDetail.'+scope.$index+'.amount'"
                                                :rules="[
                                                  {
                                                  ...rules.amount[1],
                                                  row: props.row,
                                                  }]"
                                                  label-width="0">
                                        <el-input v-model="scope.row.amount" :disabled="form.applyReason != '04'"
                                                  @input="handleInputAmount(scope.row)"
                                                  @change="amountChanged(scope.row)">
                                        </el-input>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column
                                    min-width="15%"
                                    label="红字价税合计">
                                <template slot-scope="scope">
                                    <span>{{(Number(scope.row.pricePlusTaxes)).toFixed(2)}}</span>
                                </template>
                            </el-table-column>
                        </el-table>
                    </template>
                </el-table-column>
                <el-table-column
                        label="操作"
                        min-width="10%">
                    <template slot-scope="scope">
                        <el-button type="text" @click="associate(scope.row)">关联</el-button>
                    </template>
                </el-table-column>
                <el-table-column
                        label="产品名称"
                        prop="skuName">
                </el-table-column>
                <el-table-column
                        label="规格型号"
                        min-width="15%"
                        prop="spec">
                </el-table-column>
                <el-table-column
                        label="单位"
                        min-width="15%"
                        prop="unitName">
                </el-table-column>
                <el-table-column
                        label="税收分类编码"
                        prop="taxCategoryNo"
                        min-width="25%">
                </el-table-column>
                <el-table-column
                        label="售后数量"
                        min-width="15%"
                        prop="afterSaleNum">
                </el-table-column>
                <el-table-column
                        label="售后金额"
                        min-width="15%">
                    <template slot-scope="scope">
                        <span>{{Number(scope.row.afterSalesAmount).toFixed(2)}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="售后价税合计"
                        min-width="15%">
                    <template slot-scope="scope">
                        <span>{{Number(scope.row.afterSalesAmountAndTax).toFixed(2)}}</span>
                    </template>
                </el-table-column>
            </el-table>
        </el-row>

        <el-dialog
                title="关联蓝票明细"
                :visible.sync="dialogVisible"
                height="80%"
                :before-close="handleClose"
                width="65%">
            <span style="font-weight: 600">售后商品</span>
            <el-table
                    :data="chosenRedInvoiceDetail"
                    border
                    stripe
                    :header-cell-style="{'text-align':'center'}"
                    :cell-style="{'text-align':'center'}"
                    style="width: 100%; margin-top: 20px; margin-bottom: 35px">
                <el-table-column
                        label="产品名称"
                        prop="skuName">
                </el-table-column>
                <el-table-column
                        label="规格型号"
                        prop="spec">
                </el-table-column>
                <el-table-column
                        label="单位"
                        prop="unitName">
                </el-table-column>
                <el-table-column
                        label="税收分类编码"
                        prop="taxCategoryNo">
                </el-table-column>
                <el-table-column
                        label="售后数量"
                        prop="afterSaleNum">
                </el-table-column>
                <el-table-column
                        label="售后金额">
                    <template slot-scope="scope">
                        <span>{{Number(scope.row.afterSalesAmount).toFixed(2)}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="售后价税合计">
                    <template slot-scope="scope">
                        <span>{{Number(scope.row.afterSalesAmountAndTax).toFixed(2)}}</span>
                    </template>
                </el-table-column>
            </el-table>

            <span style="font-weight: 600">蓝票明细</span>
            <el-table
                    :data="blueInvoiceDetailList"
                    border
                    stripe
                    :header-cell-style="{'text-align':'center'}"
                    :cell-style="{'text-align':'center'}"
                    @row-click="singleElection"
                    highlight-current-row
                    style="width: 100%; margin-top: 20px">
                <el-table-column
                        label=""
                        width="80px">
                    <template slot-scope="scope">
                        <el-radio v-model="radioSelected" :label="scope.row.xh">{{""}}</el-radio>
                    </template>
                </el-table-column>
                <el-table-column
                        label="匹配度"
                        width="80px">
                    <template slot-scope="scope">
                        <span>{{scope.row.matchingDegree}}%</span>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="projectName"
                        label="项目名称"
                        width="200px">
                </el-table-column>
                <el-table-column
                        label="规格型号"
                        width="80px"
                        prop="specifications">
                </el-table-column>
                <el-table-column
                        label="单位"
                        width="80px"
                        prop="unit">
                </el-table-column>
                <el-table-column
                        label="税收分类编码"
                        width="200px"
                        prop="taxCategoryNo">
                </el-table-column>
                <el-table-column
                        label="剩余可红字数量"
                        width="180px"
                        prop="quantity">
                </el-table-column>
                <el-table-column
                        label="剩余可红字金额"
                        width="180px"
                        prop="amount">
                </el-table-column>
                <el-table-column
                        label="剩余可红字税额"
                        width="180px"
                        prop="uaxAmount">
                </el-table-column>
                <el-table-column
                        label="剩余可红字价税合计"
                        width="180px"
                        prop="pricePlusTaxes">
                    <template slot-scope="scope">
                        <span>{{(Number(scope.row.pricePlusTaxes)).toFixed(2)}}</span>
                    </template>
                </el-table-column>
            </el-table>
            <span slot="footer" class="dialog-footer" style="text-align: right">
                <el-button type="primary" @click="saveAssociateChoice()">确 定</el-button>
                <el-button @click="handleClose">取 消</el-button>
            </span>
        </el-dialog>

    </el-form>

    <el-divider style="margin-top: 10%"></el-divider>
    <el-row>
        <el-col :span="6">
            <span style="font-weight: 600">红字合计：</span>
            <span v-if="form.applyReason == '04'">数量：0</span>
            <span v-else-if="form.redConfirmationScope == 0">数量：{{this.totalNum}}</span>
            <span v-else-if="form.redConfirmationScope == 1">数量：{{this.partTotalNum}}</span>
            <span v-else>数量：0</span>
        </el-col>
        <el-col :span="6">
            <span v-if="form.redConfirmationScope == 0">金额：{{Number(this.totalAfterSalesAmount).toFixed(2)}}</span>
            <span v-if="form.redConfirmationScope == 1">金额：{{(Number(this.partTotalAfterSalesAmount)).toFixed(2)}}</span>
        </el-col>
        <el-col :span="6">
            <span v-if="form.redConfirmationScope == 0">税额：{{Number(this.totalTax).toFixed(2)}}</span>
            <span v-if="form.redConfirmationScope == 1">税额：{{Number(this.partTotalTax).toFixed(2)}}</span>
        </el-col>
        <el-col :span="6">
            <span v-if="form.redConfirmationScope == 0">价税合计：{{Number(this.totalAfterSalesAmountAndTax).toFixed(2)}}</span>
            <span v-if="form.redConfirmationScope == 1">价税合计：{{Number(this.partTotalAfterSalesAmountAndTax).toFixed(2)}}</span>
        </el-col>
    </el-row>
    <el-divider style="margin-top: 10%"></el-divider>
    <el-row>
        <el-col :span="12" style="padding-left: 2%; ">
            <el-button type="warning" plain round v-if="this.invoiceRedConfirmationDto.redConfirmationStatus == 0">初始化</el-button>
            <el-button type="warning" plain round v-if="this.invoiceRedConfirmationDto.redConfirmationStatus == 1">已申请</el-button>
            <el-button type="warning" plain round v-if="this.invoiceRedConfirmationDto.redConfirmationStatus == 2">已确认</el-button>
            <el-button type="warning" plain round v-if="this.invoiceRedConfirmationDto.redConfirmationStatus == 3">已开票</el-button>
            <el-button type="warning" plain round v-if="this.invoiceRedConfirmationDto.redConfirmationStatus == 4">已作废</el-button>
        </el-col>
        <el-col :span="12" style="text-align: right; padding-right: 5%">
            <template v-for="item in this.invoiceRedConfirmationDto.buttonList">
                <template v-if="item.show">
                    <template v-if="!item.needConfirm && item.button != 0">
                        <vxe-button @click="doButton(item)" size="small" status="primary" >{{item.buttonName}}</vxe-button>
                    </template>
                    <template v-if="!item.needConfirm && item.button == 0">
                        <vxe-button @click="apply('confirmForm')" size="small" status="primary" >{{item.buttonName}}</vxe-button>
                    </template>
                    <template v-if="item.needConfirm">
                        <vxe-button @click="confirmDoButton(item)" size="small" status="primary">{{item.buttonName}}</vxe-button>
                    </template>
                </template>
            </template>
            <el-button size="small" @click="close()">取消</el-button>
        </el-col>
    </el-row>
</div>
<script src="${pageContext.request.contextPath}/static/api/invoiceredconfirmation/red_confirm_apply.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">
    const invoiceRedConfirmationId = ${invoiceRedConfirmationId};
    new Vue({
        el: '#app',
        data() {
            var checkBuyNum = (rule, value, callback) => {
                if (0 < Number(value) && Number(value) <= Number(rule.row.afterSaleNum) || this.form.applyReason == '04') {
                    callback();
                }else if(Number(value) === 0){
                    callback(new Error("请填写红字数量"));
                }else {
                    callback(new Error("需在0到" + rule.row.afterSaleNum + "之间"));
                }
            };
            var checkPrice = (rule, value, callback) => {
                if (0 < Number(value) && Number(value) <= Number(rule.row.afterSalesAmount)) {
                    callback();
                }else if(Number(value) === 0){
                    callback(new Error("请填写红字金额"));
                }else {
                    callback(new Error("需在0到" + rule.row.afterSalesAmount + "之间"));
                }
            }

            return {
                invoiceRedConfirmationDto: {},
                form: {
                    applyReason: '01',
                    redConfirmationScope: 0,
                    detailRange: 1,
                    // 红字明细列表
                    redInvoiceDetailList: []
                },

                rules: {
                    amount: [
                        {required: true, message: "请填写红字金额", trigger: 'blur'},
                        {validator: checkPrice, trigger: 'blur'}
                    ],
                    quantity: [
                        {required: true, message: "请填写红字数量", trigger: 'blur'},
                        {type: 'number',validator: checkBuyNum, trigger: 'blur'}
                    ]
                },
                tempQuantityRule: [
                    {required: true, message: "请填写红字数量", trigger: 'blur'},
                    {validator: checkBuyNum, trigger: 'blur'}
                ],
                // 是否禁用部分红冲
                canPartRed: true,
                blueUsage: '',
                // 关联完蓝字明细的列表,这个数组不会减少，只会增加关联的联票明细，最终传入接口
                redAssociateBlueDetailList: [],
                // 蓝票明细列表
                blueInvoiceDetailList: [],
                dialogVisible: false,
                expandRowKeys: [],

                // 点击关联按钮后，传入弹窗的红票明细
                chosenRedInvoiceDetail:[],
                radioSelected: '',
                // 暂存选中的蓝票明细行
                checkList: [],
                currentRedDetailId: 0,
                // 全部红冲时：合计数据，这个数据是不变的
                totalNum: 0,
                totalAfterSalesAmount: 0.00,
                totalTax: 0.00,
                totalAfterSalesAmountAndTax: 0.00,

                // 部分红冲时的合计数据，这个是需要根据关联的蓝票明细计算的
                partTotalNum: 0,
                partTotalAfterSalesAmount: 0.00,
                partTotalTax: 0.00,
                partTotalAfterSalesAmountAndTax: 0.00,

                redConfirmReqApplyRequestDto: {},
                fullscreenLoading: false
            }
        },

        mounted() {
            loadingApp();
            this.initData()
        },

        methods: {
            // 页面初始化数据加载
            async initData() {
                const loading = this.$loading({
                    lock: true,
                    text: '初始化税金接口数据中，请稍等',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
               await getRedInvoiceDetailList({"invoiceRedConfirmationId" : invoiceRedConfirmationId}).then(res => {
                    this.form.redInvoiceDetailList = res.data.data;
                });

               await  getInvoiceRedConfirmationById({"invoiceRedConfirmationId" : invoiceRedConfirmationId}).then(res => {
                    this.invoiceRedConfirmationDto = res.data.data;
                    // 查询蓝票明细列表
                    getBlueInvoiceDetailList({"blueInvoiceId": this.invoiceRedConfirmationDto.blueInvoiceId}).then(res => {
                        this.blueInvoiceDetailList = res.data.data;
                        this.blueInvoiceDetailList.forEach(blue => {
                            blue.oldQuantity = blue.quantity;
                            blue.oldPricePlusTaxes = blue.pricePlusTaxes;
                        });
                        if (res.data.code == 500) {
                            this.$alert(res.data.message, '错误', {
                                confirmButtonText: '确定',
                                callback: action => {
                                    loading.close();
                                    parent.layer.closeAll();
                                }
                            });
                        } else {
                            this.blueUsage = this.blueInvoiceDetailList[0].blueUsage;
                            // 页面初始化时自动匹配蓝票明细
                            this.form.redInvoiceDetailList.forEach(item => {
                                item.chosenBlueInvoiceDetail = [];
                                this.blueInvoiceDetailList.forEach(blueDetail => {
                                    if (blueDetail.businessOrderItemId == item.orderDetailId) {
                                        blueDetail.matchingDegree = 0;
                                        if (item.skuName == blueDetail.projectName) {
                                            blueDetail.matchingDegree = blueDetail.matchingDegree + 25;
                                        }
                                        if (item.spec == blueDetail.specifications) {
                                            blueDetail.matchingDegree = blueDetail.matchingDegree + 25;
                                        }
                                        if (item.unitName == blueDetail.unit) {
                                            blueDetail.matchingDegree = blueDetail.matchingDegree + 25;
                                        }
                                        if (item.taxCategoryNo == blueDetail.taxCategoryNo) {
                                            blueDetail.matchingDegree = blueDetail.matchingDegree + 25;
                                        }
                                        item.chosenBlueInvoiceDetail.push({...blueDetail});
                                        this.expandRowKeys.push(item.afterSalesGoodsId);
                                        return;
                                    }
                                })
                            })

                            // 计算全部红冲时的合计信息
                            this.blueInvoiceDetailList.forEach(blueDetail => {
                                this.totalNum = _.add(this.totalNum , blueDetail.quantity);
                                this.totalAfterSalesAmount = _.add(this.totalAfterSalesAmount, blueDetail.amount);
                                this.totalTax = _.add(this.totalTax ,blueDetail.uaxAmount);
                                this.totalAfterSalesAmountAndTax = _.add(this.totalAfterSalesAmountAndTax , blueDetail.pricePlusTaxes);
                            })
                        }
                        this.redAssociateBlueDetailList = this.form.redInvoiceDetailList;
                        loading.close();
                    });
                });
            },

            // 申请原因变化监听
            reasonChange(val) {
                if (val == '01') {
                    this.form.redConfirmationScope = 0;
                    this.canPartRed = true;
                }
                if (val !== '01' && this.blueUsage == '01') {
                    this.canPartRed = false;
                }

                if (val == '04') {
                    // 销售折让时，不需要校验数量
                    this.$refs.confirmForm.clearValidate();
                } else {
                    // 重新计算
                    this.form.redInvoiceDetailList.forEach(item => {
                        if (item.chosenBlueInvoiceDetail != null && item.chosenBlueInvoiceDetail != undefined && item.chosenBlueInvoiceDetail.length > 0) {
                            var blueDetail = item.chosenBlueInvoiceDetail[0];
                            this.quantityChanged(blueDetail);
                        }

                        this.redAssociateBlueDetailList .forEach(item2 => {
                            if (item2.chosenBlueInvoiceDetail != null && item2.chosenBlueInvoiceDetail != undefined && item2.chosenBlueInvoiceDetail.length > 0) {
                                if (item2.chosenBlueInvoiceDetail[0].xh == blueDetail.xh) {
                                    item2.chosenBlueInvoiceDetail[0] = blueDetail;
                                }
                            }
                        });
                    });

                    this.partTotalNum = 0;
                    this.partTotalAfterSalesAmount = 0.00;
                    this.partTotalTax = 0.00;
                    this.partTotalAfterSalesAmountAndTax = 0.00;
                    this.redAssociateBlueDetailList.forEach(item => {
                        if(item.chosenBlueInvoiceDetail != null && item.chosenBlueInvoiceDetail != undefined && item.chosenBlueInvoiceDetail.length > 0) {
                            // 重新计算合计行
                            var chosenBlueDetail = item.chosenBlueInvoiceDetail[0];
                            this.partTotalNum = _.add(this.partTotalNum , Number(chosenBlueDetail.quantity));
                            this.partTotalAfterSalesAmount = _.add(this.partTotalAfterSalesAmount , Number(chosenBlueDetail.amount));
                            this.partTotalTax = _.add(this.partTotalTax , Number(chosenBlueDetail.uaxAmount));
                            this.partTotalAfterSalesAmountAndTax = _.add(this.partTotalAfterSalesAmountAndTax , Number(chosenBlueDetail.pricePlusTaxes));
                        }
                    });
                }
            },

            // 选择部分红冲时，重新计算合计数据
            scopeChange(val) {
                if (val == 1) {
                    this.partTotalNum = 0;
                    this.partTotalAfterSalesAmount = 0.00;
                    this.partTotalTax = 0.00;
                    this.partTotalAfterSalesAmountAndTax = 0.00;
                    this.redAssociateBlueDetailList.forEach(item => {
                        if (item.chosenBlueInvoiceDetail != null && item.chosenBlueInvoiceDetail != undefined && item.chosenBlueInvoiceDetail.length > 0) {
                            var blueDetail = item.chosenBlueInvoiceDetail[0];
                            this.partTotalNum = _.add(this.partTotalNum , Number(blueDetail.quantity));
                            this.partTotalAfterSalesAmount = _.add(this.partTotalAfterSalesAmount , Number(blueDetail.amount));
                            this.partTotalTax = _.add(this.partTotalTax , Number(blueDetail.uaxAmount));
                            this.partTotalAfterSalesAmountAndTax = _.add(this.partTotalAfterSalesAmountAndTax , Number(blueDetail.pricePlusTaxes));
                        }
                    })
                }
                if (val == 0) {
                    this.partTotalNum = 0;
                    this.partTotalAfterSalesAmount = 0.00;
                    this.partTotalTax = 0.00;
                    this.partTotalAfterSalesAmountAndTax = 0.00;
                }
            },

            // 点击关联按钮操作
            associate(row) {
                this.dialogVisible = true;
                // 回显当前关联行的红字明细
                this.chosenRedInvoiceDetail.push(row);
                // 记录下当前是操作的哪一条红票明细
                this.currentRedDetailId = row.afterSalesGoodsId;

                // 计算匹配度并排序
                this.blueInvoiceDetailList.forEach(item => {
                    item.matchingDegree = 0;
                    if (row.skuName == item.projectName) {
                        item.matchingDegree = item.matchingDegree + 25;
                    }
                    if (row.spec == item.specifications) {
                        item.matchingDegree = item.matchingDegree + 25;
                    }
                    if (row.unitName == item.unit) {
                        item.matchingDegree = item.matchingDegree + 25;
                    }
                    if (row.taxCategoryNo == item.taxCategoryNo) {
                        item.matchingDegree = item.matchingDegree + 25;
                    }
                    // 将售后数量冗余到蓝票明细行中。用于后续的输入大小校验
                    item.originalNum = row.afterSaleNum;
                });
                this.blueInvoiceDetailList.sort((a, b) => b.matchingDegree - a.matchingDegree);

                if (row.chosenBlueInvoiceDetail != null && row.chosenBlueInvoiceDetail.length > 0) {
                    this.blueInvoiceDetailList.forEach(item => {
                        if (item.xh == row.chosenBlueInvoiceDetail[0].xh) {
                            this.radioSelected = item.xh;
                        }
                    })
                }
            },

            // 弹窗关闭清空当前行的信息
            handleClose() {
                this.chosenRedInvoiceDetail = [];
                this.dialogVisible = false;
                this.radioSelected = '';
                this.currentRedDetailId = 0;

                // 关闭后需要再次重新计算一下父页面的各个匹配度
                this.form.redInvoiceDetailList.forEach(item => {
                    if (item.chosenBlueInvoiceDetail != null && item.chosenBlueInvoiceDetail != undefined && item.chosenBlueInvoiceDetail.length > 0) {
                        var blueDetail = item.chosenBlueInvoiceDetail[0];
                        blueDetail.matchingDegree = 0;
                        if (item.skuName == blueDetail.projectName) {
                            blueDetail.matchingDegree = blueDetail.matchingDegree + 25;
                        }
                        if (item.spec == blueDetail.specifications) {
                            blueDetail.matchingDegree = blueDetail.matchingDegree + 25;
                        }
                        if (item.unitName == blueDetail.unit) {
                            blueDetail.matchingDegree = blueDetail.matchingDegree + 25;
                        }
                        if (item.taxCategoryNo == blueDetail.taxCategoryNo) {
                            blueDetail.matchingDegree = blueDetail.matchingDegree + 25;
                        }
                    }
                })
            },

            // 关联弹窗保存
            saveAssociateChoice() {
                // 将选中的蓝票明细绑定到红字明细上
                if (this.checkList.length > 0) {
                    this.form.redInvoiceDetailList.forEach(item => {
                    // 如果当前选中的蓝票明细行，已经被其他的红字明细行选中了，则需要把之前的绑定关系删掉
                    // 需要先执行删除历史数据，再绑定本次数据
                        if(item.chosenBlueInvoiceDetail != null && item.chosenBlueInvoiceDetail != undefined && item.chosenBlueInvoiceDetail.length > 0) {
                            if (item.chosenBlueInvoiceDetail[0].xh == this.checkList[0].xh) {
                                item.chosenBlueInvoiceDetail = [];
                            }
                        }

                        if (item.afterSalesGoodsId == this.currentRedDetailId) {
                            item.chosenBlueInvoiceDetail = this.checkList;
                            this.expandRowKeys.push(item.afterSalesGoodsId);
                        }
                    });

                    // 更新暂存的红蓝票关联数组中的信息
                    this.redAssociateBlueDetailList.forEach(item => {
                        if(item.chosenBlueInvoiceDetail != null && item.chosenBlueInvoiceDetail != undefined && item.chosenBlueInvoiceDetail.length > 0) {
                            if (item.chosenBlueInvoiceDetail[0].xh == this.checkList[0].xh) {
                                item.chosenBlueInvoiceDetail = [];
                            }
                        }

                        if (item.afterSalesGoodsId == this.currentRedDetailId) {
                            item.chosenBlueInvoiceDetail = this.checkList;
                        }
                    });
                }

                this.partTotalNum = 0;
                this.partTotalAfterSalesAmount = 0.00;
                this.partTotalTax = 0.00;
                this.partTotalAfterSalesAmountAndTax = 0.00;
                this.redAssociateBlueDetailList.forEach(item => {
                    if(item.chosenBlueInvoiceDetail != null && item.chosenBlueInvoiceDetail != undefined && item.chosenBlueInvoiceDetail.length > 0) {
                        // 重新计算合计行
                        var chosenBlueDetail = item.chosenBlueInvoiceDetail[0];
                        this.partTotalNum = _.add(this.partTotalNum , Number(chosenBlueDetail.quantity));
                        this.partTotalAfterSalesAmount = _.add(this.partTotalAfterSalesAmount , Number(chosenBlueDetail.amount));
                        this.partTotalTax = _.add(this.partTotalTax , Number(chosenBlueDetail.uaxAmount));
                        this.partTotalAfterSalesAmountAndTax = _.add(this.partTotalAfterSalesAmountAndTax , Number(chosenBlueDetail.pricePlusTaxes));
                    }
                });
                // 清空checkList
                this.checkList = [];
                this.handleClose();
            },

            // 选中蓝票明细行
            singleElection(row) {
                this.templateSelection = row.xh;
                this.checkList = this.blueInvoiceDetailList.filter((item) => item.xh === row.xh);
            },

            // 监听是展示全部红字明细还是展示未关联的明细
            detailRangeChange(val) {
                if (val == 1) {
                    // 展示全部
                    this.form.redInvoiceDetailList = this.redAssociateBlueDetailList;
                    this.form.redInvoiceDetailList.forEach(item => {
                        if (item.chosenBlueInvoiceDetail != null && item.chosenBlueInvoiceDetail.length > 0) {
                            this.expandRowKeys.push(item.afterSalesGoodsId);
                        }
                    })
                }
                if (val == 2) {
                    // 展示未关联
                    this.form.redInvoiceDetailList = this.form.redInvoiceDetailList.filter((item) => item.chosenBlueInvoiceDetail == null || item.chosenBlueInvoiceDetail.length == 0);
                    this.expandRowKeys = [];
                }
            },

            // 申请按钮
            apply(form) {
                this.redConfirmReqApplyRequestDto.redConfirmationScope = this.form.redConfirmationScope;
                this.invoiceRedConfirmationDto.applyReason = this.form.applyReason;
                this.invoiceRedConfirmationDto.redConfirmationScope = this.form.redConfirmationScope;
                var validFlag = true;
                if (this.form.redConfirmationScope == 1) {
                    // 部分红冲,组装确认单明细数据
                    // 校验每一个红字明细行都需要绑定蓝票明细
                    var invoiceRedConfirmationItemDtoList = [];
                    this.redAssociateBlueDetailList.forEach(item => {
                        if (item.chosenBlueInvoiceDetail != null && item.chosenBlueInvoiceDetail != undefined && item.chosenBlueInvoiceDetail.length > 0) {
                            item.chosenBlueInvoiceDetail[0].businessOrderItemId = item.orderDetailId;
                            item.chosenBlueInvoiceDetail[0].afterSaleBusinessOrderItemId = item.afterSalesGoodsId;
                            item.chosenBlueInvoiceDetail[0].blueInvoiceItemId = item.blueInvoiceDetailId;
                            invoiceRedConfirmationItemDtoList.push(item.chosenBlueInvoiceDetail[0]);
                        } else {
                            this.$message.error('存在售后商品未关联蓝票明细，请检查');
                            validFlag = false;
                            return false;
                        }
                    })
                    this.invoiceRedConfirmationDto.invoiceRedConfirmationItemDtoList = invoiceRedConfirmationItemDtoList;
                }
                this.redConfirmReqApplyRequestDto.invoiceRedConfirmationDto = this.invoiceRedConfirmationDto;

                this.$refs[form].validate((valid) => {
                    if (valid && validFlag) {
                        const loading = this.$loading({
                            lock: true,
                            text: '正在申请中，请稍等',
                            spinner: 'el-icon-loading',
                            background: 'rgba(0, 0, 0, 0.7)'
                        });

                        apply(this.redConfirmReqApplyRequestDto).then(res => {
                            loading.close();
                            if (res.data.data != undefined && res.data.data.length > 0) {
                                VXETable.modal.alert({ content:res.data.data[0].failReason, title:'操作失败信息', status: 'error' });
                            } else {
                                VXETable.modal.message({content: '操作成功', status: 'success'});
                                parent.layer.closeAll();
                                parent.window.location.reload();
                            }

                        })
                    }
                })
            },

            // 取消按钮
            close() {
                parent.layer.closeAll();
            },

            doButton(button) {
                let pageParams=[];
                pageParams.push(invoiceRedConfirmationId);
                if (button.viewType == 1) {
                    openModelParam( button.url, button.buttonName, "70%", "100%");
                }
                if (button.viewType == 0) {
                    const loading = this.$loading({
                        lock: true,
                        text: 'Loading',
                        spinner: 'el-icon-loading',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });
                    axios({
                        url: button.url,
                        method: 'post',
                        data: {invoiceRedConfirmationIds:pageParams}
                    }).then(res => {
                        loading.close();
                    if (res.data.data != undefined && res.data.data.length > 0) {
                        VXETable.modal.alert({ content:res.data.data[0].failReason, title:'操作失败信息', status: 'error' });
                    } else {
                        VXETable.modal.message({content: '操作成功', status: 'success'});
                        parent.layer.closeAll();
                        parent.window.location.reload();
                    }

                });
                }
            },

            async confirmDoButton(button) {
                const type = await VXETable.modal.confirm('确定'+button.buttonName+'吗？');
                if (type == 'confirm') {

                    const loading = this.$loading({
                        lock: true,
                        text: 'Loading',
                        spinner: 'el-icon-loading',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });
                    let pageParams=[];
                    pageParams.push(invoiceRedConfirmationId);
                    let params = {invoiceRedConfirmationIds:pageParams};
                    if (button.button == 1) {
                        params = {invoiceRedConfirmationIds:pageParams,status:'Y'};
                    }
                    if (button.button == 2) {
                        params = {invoiceRedConfirmationIds:pageParams,status:'N'};
                    }

                    axios({
                        url: button.url,
                        method: 'post',
                        data: params
                    }).then(res => {
                        loading.close();
                    if (res.data.data != undefined && res.data.data.length > 0) {
                        VXETable.modal.alert({content: res.data.data[0].failReason, title: '操作失败信息', status: 'error'});
                    } else {
                        VXETable.modal.message({content: '操作成功', status: 'success'});
                        parent.layer.closeAll();
                        parent.window.location.reload();
                    }

                });
                }
            },

            // 部分红冲-校验输入数量
            handleInputQuantity(row) {
                row.quantity = this.limitInputNumber(row.quantity);
            },
            limitInputNumber(value) {
                if (value != undefined) {
                    value = value.replace(/[^0-9]/g, '');// 只能输入数字
                    value = value.replace(/^(0+)|[^\d]+/g, '');// 第一位0开头，0后面为数字，则过滤掉，取后面的数字
                }
                return value;
            },
            // 部分红冲-校验输入金额
            handleInputAmount(row) {
                row.amount = this.limitInputNumberDecimal(row.amount);
            },
            limitInputNumberDecimal(value) {
                if (value != undefined) {
                    value = value.replace(/[^\d.]/g, ''); // 只能输入数字和.
                    value = value.replace(/^\./g, '');  //第一个字符不能是.
                    value = value.replace(/\.{2,}/g, '.'); // 不能连续输入.
                    value = value.replace(/(\.\d+)\./g, '$1'); // .后面不能再输入.
                    value = value.replace(/^0+(\d)/, '$1'); // 第一位0开头，0后面为数字，则过滤掉，取后面的数字
                    value = value.replace(/(\.\d{2})\d*/, '$1');// 最多保留2位小数
                }
                return value;
            },

            // 部分红冲时，数量输入改变后重新计算金额
            quantityChanged(row) {
                const originBlue = this.blueInvoiceDetailList.find(item => item.xh == row.xh);
                if (originBlue != undefined) {
                    // 红字价税合计 = (蓝票含税金额 / 蓝票数量)* 红字数量  先乘后除
                    row.pricePlusTaxes = _.divide(_.multiply(Number(row.quantity), Number(originBlue.oldPricePlusTaxes)), Number(originBlue.oldQuantity)).toFixed(2);
                    // 红字税额 = （价税合计/（1+初始化返回的税率））*初始化返回的税率
                    row.uaxAmount = _.divide(_.multiply(Number(row.pricePlusTaxes), Number(row.taxRate)), _.add(1, Number(row.taxRate))).toFixed(2);
                    // 红字金额 = 红字价税合计 - 红字税额
                    row.amount = _.subtract(row.pricePlusTaxes, row.uaxAmount).toFixed(2);
                    this.calculatePartTotal(row);
                }
            },

            // 红字金额变化后，重新计算合计数据(仅限于 销售折让 可以修改金额)
            amountChanged(row) {
                // 税额 = 红字金额 * 税率
                // 红字价税合计 = 红字金额 + 红字税额
                row.uaxAmount = _.multiply(Number(row.amount) , Number(row.taxRate)).toFixed(2);
                row.pricePlusTaxes = _.add(Number(row.amount) , Number(row.uaxAmount)).toFixed(2);

                // 将页面更新的明细信息，同步到redAssociateBlueDetailList，并重新计算合计数据
                this.calculatePartTotal(row);
            },

            // 计算合计数据（部分红冲）
            calculatePartTotal(row) {
                this.partTotalNum = 0;
                this.partTotalAfterSalesAmount = 0.00;
                this.partTotalTax = 0.00;
                this.partTotalAfterSalesAmountAndTax = 0.00;
                this.redAssociateBlueDetailList .forEach(item => {
                    if (item.chosenBlueInvoiceDetail != null && item.chosenBlueInvoiceDetail != undefined && item.chosenBlueInvoiceDetail.length > 0) {
                        if (item.chosenBlueInvoiceDetail[0].xh == row.xh) {
                            item.chosenBlueInvoiceDetail[0] = row;
                        }
                        var chosenBlueDetail = item.chosenBlueInvoiceDetail[0];
                        this.partTotalNum = _.add(this.partTotalNum , Number(chosenBlueDetail.quantity));
                        this.partTotalAfterSalesAmount = _.add(this.partTotalAfterSalesAmount , Number(chosenBlueDetail.amount));
                        this.partTotalTax = _.add(this.partTotalTax , Number(chosenBlueDetail.uaxAmount));
                        this.partTotalAfterSalesAmountAndTax = _.add(this.partTotalAfterSalesAmountAndTax , Number(chosenBlueDetail.pricePlusTaxes));
                    }
                });
            }
        }
    })
</script>

<style>
</style>