package com.vedeng.erp.system.common.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 自定义数据插入类型枚举类     新增方式（1.代码 2.excel导入）
 * @date 2022/7/15 16:58
 */
public enum CustomDataLogSaveTypeEnums {


    /**
     * 代码
     **/
    CODE(1),
    /**
     * excel导入
     **/
    EXCEL_IMPORT(2);


    private final Integer type;


    public Integer getType() {
        return type;
    }

    CustomDataLogSaveTypeEnums(Integer type) {
        this.type = type;
    }
}
