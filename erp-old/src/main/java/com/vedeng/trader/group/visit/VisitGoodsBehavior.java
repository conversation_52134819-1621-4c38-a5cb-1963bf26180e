package com.vedeng.trader.group.visit;


import com.vedeng.track.api.dto.business.req.BusinessReqDto;
import org.apache.commons.collections.CollectionUtils;

import java.util.BitSet;
/**
 * <b>Description:</b>访问商品<br>
 * <b>Author:calvin</b>
 * <br><b>Date:</b> 2020/5/25
 */
public class VisitGoodsBehavior extends TrackBaseBehvior {
    @Override
    public BitSet calculate() {
        Integer type=getChooseType();
        if(type==0||type==1){
           getGoods(type);
        }
        BitSet middleResult=new BitSet();
        if(CollectionUtils.isNotEmpty(getSkuList())) {
            BusinessReqDto reqDto = new BusinessReqDto();
            reqDto.setDayType(getPeriod());
            reqDto.setSkuNoList(getSkuList());
            middleResult= getVisitData(reqDto);
        }
        BitSet result=getBaseTraderSet();
        result.and(middleResult);
        return result;
    }


}
