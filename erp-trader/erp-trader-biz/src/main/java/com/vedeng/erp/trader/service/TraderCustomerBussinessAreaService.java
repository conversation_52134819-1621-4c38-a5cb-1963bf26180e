package com.vedeng.erp.trader.service;

import com.vedeng.erp.trader.dto.TraderCustomerBussinessAreaDto;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/11 17:36
 **/
public interface TraderCustomerBussinessAreaService {

    /**
     * 批量新增
     * @param traderCustomerBussinessAreaDtoList 对象
     */
    void addAll(List<TraderCustomerBussinessAreaDto> traderCustomerBussinessAreaDtoList);

    /**
     * 根据客户id删除
     * @param traderCustomerId 客户id
     */
    void deleteByTraderCustomerId(Integer traderCustomerId);

    /**
     * 批量更新
     *
     * @param traderCustomerBussinessAreaDtoList
     */
    void batchUpdate(List<TraderCustomerBussinessAreaDto> traderCustomerBussinessAreaDtoList);
}
