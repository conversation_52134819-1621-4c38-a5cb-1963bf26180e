package com.vedeng.erp.broadcast.statistics.project;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.shiro.util.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.broadcast.service.QrMessage;
import com.vedeng.erp.common.broadcast.MessageSubjectEnum;
import com.vedeng.erp.common.broadcast.StatDateRangeEnum;
import com.vedeng.erp.common.broadcast.StatisticsTypeEnum;
import com.vedeng.erp.common.broadcast.config.BroadcastDeptConfigStatistics;
import com.vedeng.erp.common.broadcast.config.BroadcastGlobalConfigStatistics;
import com.vedeng.erp.common.broadcast.config.GlobalConfig;
import com.vedeng.erp.common.broadcast.param.QwMessageParam;
import com.vedeng.erp.common.broadcast.param.TargetOrgAndUser;
import com.vedeng.erp.common.broadcast.param.TimePeriod;

/**
 * 自定义播报项目
 * @ClassName:  BroadcastUserDefine   
 * @author: Neil.yang
 * @date:   2025年6月9日 上午9:30:08    
 * @Copyright:
 */
@Component
public class BroadcastUserDefine extends AbstractBroadcast {
	
	@Autowired
	private BroadcastMonthAed broadcastMonthAed;
	
	@Autowired
	private BroadcastMonthVd broadcastMonthVd;
	
	@Autowired
	private QrMessage qrMessage;
	
	//日志
	public static Logger LOGGER = LoggerFactory.getLogger(BroadcastUserDefine.class);
	
	@Override
	public int getLineNum() {
		return 6;
	}

	@Override
	public boolean isSaveDb() {
		return false;
	}
	
	@Override
	public List<MessageSubjectEnum> getMessageSubjectList(GlobalConfig globalConfig) {
		// TODO Auto-generated method stub
		return null;
	}

	public List<MessageSubjectEnum> getMessageSubjectList(GlobalConfig globalConfig, Integer deptId) {
		List<MessageSubjectEnum> messageSubjectList = new ArrayList<>();
		if(deptId == 1) {
			messageSubjectList = Arrays.asList(MessageSubjectEnum.SALES_SINGLE,MessageSubjectEnum.SALES_TEAM,MessageSubjectEnum.SALES_DEPT);
		}else {
			messageSubjectList = Arrays.asList(MessageSubjectEnum.SALES_SINGLE);
		}
		return messageSubjectList;
	}

	@Override
	public List<BroadcastDeptConfigStatistics> getBroadcastDeptConfigByProject(
			List<BroadcastDeptConfigStatistics> broadcastDeptConfigStatisticsList) {
		return broadcastDeptConfigStatisticsList.stream().filter(item -> item.getCustomFlag() == 1).collect(Collectors.toList());
	}
	
	/**
     * 获取统计时间周期
     * @return 统计时间周期
     */
    public TimePeriod getStatisticsTime(StatDateRangeEnum statDateRange) {
    	//日，当日零点到，当前时间
    	if(statDateRange.equals(StatDateRangeEnum.STATISTICS_DAY)) {
    		Calendar calendar = Calendar.getInstance();
    		//calendar.add(Calendar.YEAR, -2);
        	calendar.set(Calendar.HOUR_OF_DAY, 0);
        	calendar.set(Calendar.MINUTE, 0);
        	calendar.set(Calendar.SECOND, 0);
        	calendar.set(Calendar.MILLISECOND, 0);
        	Date midnight = calendar.getTime();
        	return new TimePeriod(null,null,midnight,new Date());
    	}
    	//周，当周第一天0点，到当前时间
		if(statDateRange.equals(StatDateRangeEnum.STATISTICS_WEEK)) {
			Calendar calendar = Calendar.getInstance();
	        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
	        calendar.set(Calendar.HOUR_OF_DAY, 0);
	        calendar.set(Calendar.MINUTE, 0);
	        calendar.set(Calendar.SECOND, 0);
	        calendar.set(Calendar.MILLISECOND, 0);
	        //本周的第一天作为周开始时间
	    	Date startDate = calendar.getTime();
	    	return new TimePeriod(null,null,startDate,new Date());
		}
		//月，当月第一天0点，到当前时间
		if(statDateRange.equals(StatDateRangeEnum.STATISTICS_MONTH)) {
			Calendar calendarMonth = Calendar.getInstance();
	    	calendarMonth.set(Calendar.DAY_OF_MONTH, 1);
	    	calendarMonth.set(Calendar.HOUR_OF_DAY, 0);
	    	calendarMonth.set(Calendar.MINUTE, 0);
	    	calendarMonth.set(Calendar.SECOND, 0);
	    	calendarMonth.set(Calendar.MILLISECOND, 0);
	    	//本月的第一天作为月开始时间
	    	Date monthStartDate = calendarMonth.getTime();
	    	return new TimePeriod(null,null,monthStartDate,new Date());
		}
		return null;
    }
    
	
	@Override
	public void invocation(TimePeriod timePeriod,boolean isSendQwMessageFlag) {
		try {
            //执行各类抽象方法
            GlobalConfig configs = getGlobalConfigList();
            LOGGER.info("获取配置configs信息：{}",JSON.toJSONString(configs));
            //获取播报项目需要发送到哪些播报目标类
            List<BroadcastDeptConfigStatistics> broadcastDeptConfigStatisticsList = configs.getBroadcastDeptConfigStatisticsList();
            if(CollectionUtils.isEmpty(broadcastDeptConfigStatisticsList)) {
            	return;
            }
            //获取播报目标要发送的消息主体，例如播报小群只需要播报个人，播报大群需要播报个人、小组、部门
            BroadcastGlobalConfigStatistics broadcastGlobalConfigStatistics = configs.getBroadcastGlobalConfigStatistics();
            //获取播报对象，设置播报对象
    		List<MessageSubjectEnum> messageSendEnumList = broadcastGlobalConfigStatistics.getMessageSubjectList();
    		//获取统计维度，判断是调用月度AED还是自有品牌
    		StatisticsTypeEnum statisticsType = broadcastGlobalConfigStatistics.getStatisticsType();
    		
    		//统计时间范围1.本日 2.本周 3.本月
    		StatDateRangeEnum statDateRange = broadcastGlobalConfigStatistics.getStatDateRange();
    		
            //获取对应播报项目的播报列表
            broadcastDeptConfigStatisticsList = getBroadcastDeptConfigByProject(broadcastDeptConfigStatisticsList);
            //遍历播报目标配置表，查询哪些播报目标需要进行播报项目的播报
            for (BroadcastDeptConfigStatistics broadcastDeptConfigStatistics : broadcastDeptConfigStatisticsList) {
            	//根据播报项目，获取对应的数据
				Integer deptId = broadcastDeptConfigStatistics.getBroadcastDeptId();
				LOGGER.info("执行播报目标dept_id：{},dept_name:{}",broadcastDeptConfigStatistics.getBroadcastDeptId(),broadcastDeptConfigStatistics.getBroadcastDeptName());
            	
            	//获取播报项目要统计的类型，例如日播报只需要播报个人，周播报需要播报个人、小组、部门
            	List<MessageSubjectEnum> subjects = getMessageSubjectList(configs,deptId);
            	if(CollectionUtils.isEmpty(subjects)) {
            		LOGGER.info("未配置播报项目要统计的类型，跳过...");
            		continue;
            	}
            	//根据消息主体，获取播报目标的业务部门ID列表
            	Map<MessageSubjectEnum,List<TargetOrgAndUser>> targetOrgAndUserMap = new HashMap<>();
            	for (MessageSubjectEnum messageSubjectEnum : subjects) {
            		//消息主体是个人
					if(messageSubjectEnum == MessageSubjectEnum.SALES_SINGLE && messageSendEnumList.contains(MessageSubjectEnum.SALES_SINGLE)) {
						targetOrgAndUserMap.put(MessageSubjectEnum.SALES_SINGLE, broadcastTarget.getOrgIdBySingle(deptId));
					}
					//消息是主体是小组（大群）
					if(messageSubjectEnum == MessageSubjectEnum.SALES_TEAM  && messageSendEnumList.contains(MessageSubjectEnum.SALES_TEAM)) {
						targetOrgAndUserMap.put(MessageSubjectEnum.SALES_TEAM, broadcastTarget.getOrgIdByTeam(deptId));
					}
					//消息是主体是部门（大群）
					if(messageSubjectEnum == MessageSubjectEnum.SALES_DEPT  && messageSendEnumList.contains(MessageSubjectEnum.SALES_DEPT)) {
						targetOrgAndUserMap.put(MessageSubjectEnum.SALES_DEPT, broadcastTarget.getOrgIdByDept(deptId));
					}
				}
            	LOGGER.info("执行播报项目：AbstractBroadcast：{}，播报目标:broadcastTarget：{},统计的范围：{},要发送的主体：{}",this.getClass().getName(),broadcastTarget.getClass().getName(),JSON.toJSONString(subjects),JSON.toJSONString(messageSendEnumList));
            	LOGGER.info("开始执行获取播报排行信息...");
            	Integer amountStep = broadcastTarget.getAmountStep(configs,deptId);
            	
            	List<QwMessageParam> qwMessageParamList = new ArrayList<>();
            	//出库量
            	if(statisticsType.equals(StatisticsTypeEnum.WAREHOUSE_SALES_NUM)){
            		qwMessageParamList = broadcastMonthAed.execute(configs,targetOrgAndUserMap,deptId,amountStep,1,getStatisticsTime(statDateRange),statDateRange,isSendQwMessageFlag);
            	}
            	//自有品牌出库金额
            	if(statisticsType.equals(StatisticsTypeEnum.WAREHOUSE_SALES_VD_AMOUNT)) {
            		qwMessageParamList = broadcastMonthVd.execute(configs,targetOrgAndUserMap,deptId,amountStep,1,getStatisticsTime(statDateRange),statDateRange,isSendQwMessageFlag);
            	}
            	if(CollectionUtils.isEmpty(qwMessageParamList)) {
            		LOGGER.info("未查询到需要播报的企微信息，直接跳过...");
            		//未查询到需要播报的企微信息，直接跳过
            		continue;
            	}
            	LOGGER.info("开始播报企微信息：{}",JSON.toJSONString(qwMessageParamList));
            	String webHookUrl = broadcastTarget.getWebHook(configs,deptId);
            	qrMessage.sendMessage(qwMessageParamList,webHookUrl);
			}
        } catch (Exception e) {
            LOGGER.error("到款通知执行失败",e);
        }
	}

	@Override
	public List<QwMessageParam> execute(GlobalConfig globalConfig,Map<MessageSubjectEnum, List<TargetOrgAndUser>> targetOrgAndUserMap,Integer deptId,Integer amountStep,Integer isUserDefine,TimePeriod timePeriod,StatDateRangeEnum statDateRange,boolean isSendQwMessageFlag) {
		return null;
	}

	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
}
