<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.finance.mapper.PayApplyDefineMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.PayApplyDefineEntity">
    <id column="PAY_APPLY_ID" property="payApplyId" jdbcType="INTEGER" />
    <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
    <result column="PAY_TYPE" property="payType" jdbcType="INTEGER" />
    <result column="RELATED_ID" property="relatedId" jdbcType="INTEGER" />
    <result column="TRADER_SUBJECT" property="traderSubject" jdbcType="BIT" />
    <result column="TRADER_MODE" property="traderMode" jdbcType="INTEGER" />
    <result column="TRADER_NAME" property="traderName" jdbcType="VARCHAR" />
    <result column="TRADER_ID" property="traderId" jdbcType="VARCHAR" />
    <result column="AMOUNT" property="amount" jdbcType="DECIMAL" />
    <result column="CURRENCY_UNIT_ID" property="currencyUnitId" jdbcType="INTEGER" />
    <result column="BANK" property="bank" jdbcType="VARCHAR" />
    <result column="BANK_ACCOUNT" property="bankAccount" jdbcType="VARCHAR" />
    <result column="BANK_CODE" property="bankCode" jdbcType="VARCHAR" />
    <result column="COMMENTS" property="comments" jdbcType="VARCHAR" />
    <result column="VALID_STATUS" property="validStatus" jdbcType="BIT" />
    <result column="VALID_TIME" property="validTime" jdbcType="BIGINT" />
    <result column="VALID_COMMENTS" property="validComments" jdbcType="VARCHAR" />
    <result column="IS_BILL" property="isBill" jdbcType="BIT" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
    <result column="USERNAME" property="creatorName" jdbcType="VARCHAR" />
    <result column="PAY_STATUS" property="payStatus" jdbcType="INTEGER"/>
    <result column="MOBILE" property="mobile" jdbcType="VARCHAR"/>
    <result column="CARD" property="card" jdbcType="VARCHAR"/>
    <result column="PAY_BANKTYPE_ID" property="payBankTypeId" jdbcType="INTEGER"/>
    <result column="PAY_BANKTYPE_NAME" property="payBankTypeName" jdbcType="VARCHAR"/>
    <result column="ACCOUNT_TYPE" property="accountType" jdbcType="INTEGER"/>
    <result column="BANK_REMARK" property="bankRemark" jdbcType="VARCHAR"/>
    <result column="BILL_TIME" property="billTime" jdbcType="TIMESTAMP"/>
    <result column="BILL_METHOD" property="billMethod" jdbcType="INTEGER"/>
    <result column="AUTO_BILL" property="autoBill" jdbcType="INTEGER"/>
  </resultMap>


  <select id="pageList"
          resultMap="BaseResultMap"
          parameterType="com.vedeng.erp.finance.dto.PayApplyReqDto" >

      SELECT * from (
      select
      U.USERNAME,a.*, b.BUYORDER_NO, '' as AFTER_SALES_NO, b.TRADER_NAME as BUYORDER_TRADER_NAME,b.TRADER_ID as TRADER_ID, e.VERIFIES_TYPE, e.VERIFY_USERNAME, IFNULL(e.STATUS, -1) AS VERIFY_STATUS
      from T_PAY_APPLY a
      join T_BUYORDER b on a.RELATED_ID = b.BUYORDER_ID
      LEFT JOIN T_VERIFIES_INFO e ON a.PAY_APPLY_ID = e.RELATE_TABLE_KEY AND e.RELATE_TABLE = 'T_PAY_APPLY' AND e.VERIFIES_TYPE = 644
      LEFT JOIN T_USER U ON U.USER_ID=a.Creator
      <where>
        a.PAY_TYPE = 517
        <include refid="PayApply_Buyorder_Where_Str" />
      </where>

      UNION ALL

      select
      U.USERNAME,a.*, '' as BUYORDER_NO, b.AFTER_SALES_NO, '' as BUYORDER_TRADER_NAME,'' as TRADER_ID, e.VERIFIES_TYPE, e.VERIFY_USERNAME, IFNULL(e.STATUS, -1) AS VERIFY_STATUS
      from T_PAY_APPLY a
      join T_AFTER_SALES b on a.RELATED_ID = b.AFTER_SALES_ID
      LEFT JOIN T_VERIFIES_INFO e ON a.PAY_APPLY_ID = e.RELATE_TABLE_KEY AND e.RELATE_TABLE = 'T_PAY_APPLY' AND e.VERIFIES_TYPE = 644
      LEFT JOIN T_USER U ON U.USER_ID=a.Creator
      <where>
        a.PAY_TYPE = 518
        <include refid="PayApply_After_Sales_Where_Str" />
      </where>

      UNION ALL
      select
      U.USERNAME,a.*, b.BUYORDER_EXPENSE_NO AS BUYORDER_NO, '' as AFTER_SALES_NO, c.TRADER_NAME as BUYORDER_TRADER_NAME,c.TRADER_ID as TRADER_ID, e.VERIFIES_TYPE, e.VERIFY_USERNAME, IFNULL(e.STATUS, -1) AS VERIFY_STATUS
      from T_PAY_APPLY a
      join T_BUYORDER_EXPENSE b on a.RELATED_ID = b.BUYORDER_EXPENSE_ID
      JOIN T_BUYORDER_EXPENSE_DETAIL c ON b.BUYORDER_EXPENSE_ID = c.BUYORDER_EXPENSE_ID
      LEFT JOIN T_VERIFIES_INFO e ON a.PAY_APPLY_ID = e.RELATE_TABLE_KEY AND e.RELATE_TABLE = 'T_PAY_APPLY' AND e.VERIFIES_TYPE = 644
      LEFT JOIN T_USER U ON U.USER_ID=a.Creator
      <where>
        a.PAY_TYPE = 4125
        <include refid="PayApply_BuyorderExpense_Where_Str" />
      </where>
      ) m
      order by m.VALID_STATUS ASC, m.ADD_TIME ASC
  </select>

  <sql id="PayApply_Buyorder_Where_Str" >
    <if test="companyId!=null and companyId!=0">
      and a.COMPANY_ID = #{companyId}
    </if>
    <if test="buyorderNo!=null and buyorderNo!=''">
      and b.BUYORDER_NO like CONCAT('%',#{buyorderNo,jdbcType=VARCHAR},'%' )
    </if>
    <if test="buyorderTraderName!=null and buyorderTraderName!=''">
      and b.TRADER_NAME like CONCAT('%',#{buyorderTraderName,jdbcType=VARCHAR},'%' )
    </if>
    <if test="traderName!=null and traderName!=''">
      and a.TRADER_NAME like CONCAT('%',#{traderName,jdbcType=VARCHAR},'%' )
    </if>
    <if test="traderSubject!=null and traderSubject!=-1">
      and a.TRADER_SUBJECT = #{traderSubject,jdbcType=BIT}
    </if>
    <if test="traderMode!=null and traderMode!=-1">
      and a.TRADER_MODE = #{traderMode,jdbcType=INTEGER}
    </if>
    <if test="validStatus!=null and validStatus!=-1">
      and e.STATUS = #{validStatus,jdbcType=INTEGER}
    </if>
    <if test="isBill!=null and isBill!=-1">
      and a.IS_BILL = #{isBill,jdbcType=INTEGER}
    </if>
    <if test="searchBegintime!=null and searchBegintime!=0">
      and a.ADD_TIME &gt;= #{searchBegintime,jdbcType=BIGINT}
    </if>
    <if test="searchEndtime!=null and searchEndtime!=0">
      and a.ADD_TIME &lt;= #{searchEndtime,jdbcType=BIGINT}
    </if>
    <if test="searchBeginAmount!=null and searchBeginAmount!=0">
      and a.AMOUNT &gt;= #{searchBeginAmount,jdbcType=DECIMAL}
    </if>
    <if test="searchEndAmount!=null and searchEndAmount!=0">
      and a.AMOUNT &lt;= #{searchEndAmount,jdbcType=DECIMAL}
    </if>
    <if test="search != null">
      AND a.TRADER_MODE = 521 AND (b.BUYORDER_NO like CONCAT('%',#{search,jdbcType=VARCHAR},'%' ) OR a.TRADER_NAME like CONCAT('%',#{search,jdbcType=VARCHAR},'%' ))
    </if>
    <if test="validUserName!=null and validUserName!=''">
      AND FIND_IN_SET(#{validUserName,jdbcType=VARCHAR},e.VERIFY_USERNAME)
    </if>
    <!-- add by Tomcat.Hui 2019/9/11 13:05 .Desc: VDERP-1215 付款申请增加批量操作功能. start -->
    <if test="comments!=null and comments!=''">
      <choose>
        <when test="comments == 1">
          AND length(a.COMMENTS) > 0
        </when>
        <when test="comments == 0">
          AND (length(a.COMMENTS) = 0 OR a.COMMENTS is null)
        </when>
      </choose>
    </if>
    <!-- add by Tomcat.Hui 2019/9/11 13:05 .Desc: . end -->
    <if test="accountType!=null and accountType!=-1">
      AND a.ACCOUNT_TYPE = #{accountType,jdbcType=INTEGER}
    </if>
    <if test="autoBill!=null and autoBill!=-1">
      AND a.AUTO_BILL = #{autoBill,jdbcType=INTEGER}
    </if>
    <if test="billMethod!=null and billMethod!=-1">
      AND a.BILL_METHOD = #{billMethod,jdbcType=INTEGER}
    </if>
  </sql>

  <sql id="PayApply_After_Sales_Where_Str" >
    <if test="companyId!=null and companyId!=0">
      and a.COMPANY_ID = #{companyId}
    </if>
    <if test="buyorderNo!=null and buyorderNo!=''">
      and b.AFTER_SALES_NO like CONCAT('%',#{buyorderNo,jdbcType=VARCHAR},'%' )
    </if>
    <if test="buyorderTraderName!=null and buyorderTraderName!=''">
      and 1 = 2
    </if>
    <if test="traderName!=null and traderName!=''">
      and a.TRADER_NAME like CONCAT('%',#{traderName,jdbcType=VARCHAR},'%' )
    </if>
    <if test="traderSubject!=null and traderSubject!=-1">
      and a.TRADER_SUBJECT = #{traderSubject,jdbcType=BIT}
    </if>
    <if test="traderMode!=null and traderMode!=-1">
      and a.TRADER_MODE = #{traderMode,jdbcType=INTEGER}
    </if>
    <if test="validStatus!=null and validStatus!=-1">
      and e.STATUS = #{validStatus,jdbcType=INTEGER}
    </if>
    <if test="isBill!=null and isBill!=-1">
      and a.IS_BILL = #{isBill,jdbcType=INTEGER}
    </if>
    <if test="searchBegintime!=null and searchBegintime!=0">
      and a.ADD_TIME &gt;= #{searchBegintime,jdbcType=BIGINT}
    </if>
    <if test="searchEndtime!=null and searchEndtime!=0">
      and a.ADD_TIME &lt;= #{searchEndtime,jdbcType=BIGINT}
    </if>
    <if test="searchBeginAmount!=null and searchBeginAmount!=0">
      and a.AMOUNT &gt;= #{searchBeginAmount,jdbcType=DECIMAL}
    </if>
    <if test="searchEndAmount!=null and searchEndAmount!=0">
      and a.AMOUNT &lt;= #{searchEndAmount,jdbcType=DECIMAL}
    </if>
    <if test="search != null">
      AND a.TRADER_MODE = 521 AND (b.AFTER_SALES_NO like CONCAT('%',#{search,jdbcType=VARCHAR},'%' ) OR a.TRADER_NAME like CONCAT('%',#{search,jdbcType=VARCHAR},'%' ))
    </if>
    <if test="validUserName!=null and validUserName!=''">
      AND FIND_IN_SET(#{validUserName,jdbcType=VARCHAR},e.VERIFY_USERNAME)
    </if>
    <!-- add by Tomcat.Hui 2019/9/11 13:05 .Desc: VDERP-1215 付款申请增加批量操作功能. start -->
    <if test="comments!=null and comments!=''">
      <choose>
        <when test="comments == 1">
          AND length(a.COMMENTS) > 0
        </when>
        <when test="comments == 0">
          AND ( length(a.COMMENTS) = 0 OR a.COMMENTS is null)
        </when>
      </choose>
    </if>
    <if test="accountType!=null and accountType!=-1">
      AND a.ACCOUNT_TYPE = #{accountType,jdbcType=INTEGER}
    </if>
    <if test="autoBill!=null and autoBill!=-1">
      AND a.AUTO_BILL = #{autoBill,jdbcType=INTEGER}
    </if>
    <if test="billMethod!=null and billMethod!=-1">
      AND a.BILL_METHOD = #{billMethod,jdbcType=INTEGER}
    </if>
    <!-- add by Tomcat.Hui 2019/9/11 13:05 .Desc: . end -->
  </sql>

  <sql id="PayApply_BuyorderExpense_Where_Str" >
    <if test="companyId!=null and companyId!=0">
      and a.COMPANY_ID = #{companyId}
    </if>
    <if test="buyorderNo!=null and buyorderNo!=''">
      and b.BUYORDER_EXPENSE_NO like CONCAT('%',#{buyorderNo,jdbcType=VARCHAR},'%' )
    </if>
    <if test="buyorderTraderName!=null and buyorderTraderName!=''">
      and c.TRADER_NAME like CONCAT('%',#{buyorderTraderName,jdbcType=VARCHAR},'%' )
    </if>
    <if test="traderName!=null and traderName!=''">
      and c.TRADER_NAME like CONCAT('%',#{traderName,jdbcType=VARCHAR},'%' )
    </if>
    <if test="traderSubject!=null and traderSubject!=-1">
      and a.TRADER_SUBJECT = #{traderSubject,jdbcType=BIT}
    </if>
    <if test="traderMode!=null and traderMode!=-1">
      and a.TRADER_MODE = #{traderMode,jdbcType=INTEGER}
    </if>
    <if test="validStatus!=null and validStatus!=-1">
      and e.STATUS = #{validStatus,jdbcType=INTEGER}
    </if>
    <if test="isBill!=null and isBill!=-1">
      and a.IS_BILL = #{isBill,jdbcType=INTEGER}
    </if>
    <if test="searchBegintime!=null and searchBegintime!=0">
      and a.ADD_TIME &gt;= #{searchBegintime,jdbcType=BIGINT}
    </if>
    <if test="searchEndtime!=null and searchEndtime!=0">
      and a.ADD_TIME &lt;= #{searchEndtime,jdbcType=BIGINT}
    </if>
    <if test="searchBeginAmount!=null and searchBeginAmount!=0">
      and a.AMOUNT &gt;= #{searchBeginAmount,jdbcType=DECIMAL}
    </if>
    <if test="searchEndAmount!=null and searchEndAmount!=0">
      and a.AMOUNT &lt;= #{searchEndAmount,jdbcType=DECIMAL}
    </if>
    <if test="search != null">
      AND a.TRADER_MODE = 521 AND (b.BUYORDER_EXPENSE_NO like CONCAT('%',#{search,jdbcType=VARCHAR},'%' ) OR a.TRADER_NAME like CONCAT('%',#{search,jdbcType=VARCHAR},'%' ))
    </if>
    <if test="validUserName!=null and validUserName!=''">
      AND FIND_IN_SET(#{validUserName,jdbcType=VARCHAR},e.VERIFY_USERNAME)
    </if>
    <!-- add by Tomcat.Hui 2019/9/11 13:05 .Desc: VDERP-1215 付款申请增加批量操作功能. start -->
    <if test="comments!=null and comments!=''">
      <choose>
        <when test="comments == 1">
          AND length(a.COMMENTS) > 0
        </when>
        <when test="comments == 0">
          AND (length(a.COMMENTS) = 0 OR a.COMMENTS is null)
        </when>
      </choose>
    </if>
    <!-- add by Tomcat.Hui 2019/9/11 13:05 .Desc: . end -->
    <if test="accountType!=null and accountType!=-1">
      AND a.ACCOUNT_TYPE = #{accountType,jdbcType=INTEGER}
    </if>
    <if test="autoBill!=null and autoBill!=-1">
      AND a.AUTO_BILL = #{autoBill,jdbcType=INTEGER}
    </if>
    <if test="billMethod!=null and billMethod!=-1">
      AND a.BILL_METHOD = #{billMethod,jdbcType=INTEGER}
    </if>
  </sql>

  <update id="updateOffline">
    update T_PAY_APPLY set OFFLINE = #{offline,jdbcType=INTEGER} where PAY_APPLY_ID = #{payApplyId,jdbcType=INTEGER}
  </update>
</mapper>
