package com.wms.service;

import com.google.gson.Gson;
import com.wms.constant.WMSContant;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.dto.*;
import com.wms.service.processor.AbstractInputOrOutputProcessor;
import com.wms.service.processor.WMSCalllBackProcessor;
import com.wms.service.processor.input.*;
import com.wms.service.processor.other.*;
import com.wms.service.processor.output.*;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * WMS接口的分发器
 */
@Service
public class WmsInterfaceDispatcher {

    @Autowired
    private PurchaseInputOrderProcessor purchaseInputOrderProcessor;

    @Autowired
    private InventoryAdjustmentProcessor inventoryAdjustmentProcessor;

    @Autowired
    private InventoryTransferProcessor inventoryTransferProcessor;

    @Autowired
    private PurchaseReturnOutProcessor purchaseReturnAuditPassProcessor;

    @Resource
    private PurchaseExgOutProcessor purchaseExgOutProcessor;

    @Resource
    private PurchaseExgInputProcessor purchaseExgInputProcessor;

    @Autowired
    private SaleorderOutputProcessor saleorderOutputProcessor;

    @Autowired
    private AfterSaleOutPutProcessor afterSaleOutPutProcessor;

    @Autowired
    private AfterSaleInPutProcessor afterSaleInPutProcessor;

    @Autowired
    private CsInputOrderProcessor csInputOrderProcessor;

    @Autowired
    private UpdateSkuLWHProcessor updateSkuLWHProcessor;

    @Autowired
    private ExpressCallbackProcessor expressCallbackProcessor;

    @Autowired
    private OpenInvoiceProcessor openInvoiceProcessor;

    @Autowired
    private WmsLendInputProcessor lendInputProcessor;

    @Autowired
    private WmsLendOutputProcessor lendOutputProcessor;

    @Autowired
    private WmsSurplusInOrderProcessor wmsSurplusInOrderProcessor;

    @Autowired
    private WmsScrappedOutputProcessor wmsScrappedOutputProcessor;

    @Autowired
    private WmsInventoryOutputProcessor wmsInventoryOutputProcessor;

    @Autowired
    private WmsUnitConversionOutputProcessor wmsUnitConversionOutputProcessor;

    @Autowired
    private WmsUnitConversionInOrderProcessor wmsUnitConversionInOrderProcessor;

    @Autowired
    private InputSnProcessor inputSNProcessor;

    @Autowired
    private OutputSnProcessor outputSnProcessor;

    @Autowired
    private DeliveryDirectPurchaseInputOrderProcessor deliveryDirectPurchaseInputOrderProcessor;

    @Autowired
    private WmsSampleOutputProcessor WmsSampleOutputProcessor;

    @Autowired
    private SaleorderDirectOutputProcessor saleorderDirectOutputProcessor;
    public void disPatch(WmsRequestDto wmsRequestDto) throws Exception{

        switch (wmsRequestDto.getMethod()) {

            //入库单回传接口
            case WMSContant.PUT_PURCHASE_ORDER_CALLBACK:
                dealWithInputOrder(wmsRequestDto.getData());
                break;

            //出库单回传接口
            case WMSContant.SORT:
                dealWithSort(wmsRequestDto.getData());
                break;

            //库存调整单
            case WMSContant.PUT_INVENTORY_ADJUSRMENT_CALLBACK:
                dealWithInventoryAdjustment(wmsRequestDto.getData());
                break;

            //库存转移单
            case WMSContant.PUT_INVENTORY_TRANSFER_CALLBACK:
                dealWithInventoryTransfer(wmsRequestDto.getData());
                break;

            //商品长宽高体积回传接口
            case WMSContant.PUT_LWH_DATA:
                dealWithLWH(wmsRequestDto.getData());
                break;

            //出库包裹回传
            case WMSContant.PUT_SOPAGD_CALLBACK:
                dealWithExpress(wmsRequestDto.getData());
                break;

            //WMS触发ERP开票回传
            case WMSContant.OPEN_INVOICE_CALLBACK:
                dealWithInvoiceCallback(wmsRequestDto.getData());
                break;

            //入库厂家SN码回传接口
            case WMSContant.IN_SN_CODE_CALLBACK:
                dealWithInputSnCallback(wmsRequestDto.getData());
                break;

            //出库厂家SN码回传接口
            case WMSContant.OUT_SN_CODE_CALLBACK:
                dealWithOutputSnCallback(wmsRequestDto.getData());
                break;
            default:
                return;

        }

    }

    /**
     * 商品长宽高体积处理器
     * @param data
     */
    private void dealWithLWH(String data) throws Exception{

        List<WmsLwhDto> wmsLwhDtoList = convertDataToBean(data,WmsLwhDto.class);

        if(CollectionUtils.isEmpty(wmsLwhDtoList)){
            throw new Exception("商品长宽高数据为空");
        }

        updateSkuLWHProcessor.dealWithRequest(wmsLwhDtoList);
    }

    /**
     * 出库单回传处理
     * @param data
     * @return
     * @throws Exception
     */
    private WMSCalllBackProcessor dealWithSort(String data) throws Exception{

        List<OutputDto> outputDtos = convertDataToBean(data,OutputDto.class);
        if(CollectionUtils.isEmpty(outputDtos)){
            throw new Exception("出库单回传列表数据为空");
        }

        for(OutputDto outputDto :  outputDtos){
            WMSCalllBackProcessor calllBackProcessor = getOutPutOrderProcess(outputDto);
            calllBackProcessor.dealWithRequest(outputDto);
        }

        return null;
    }

    /**
     * 出库单回传处理器
     * @param outputDto
     * @return
     */
    private WMSCalllBackProcessor getOutPutOrderProcess(OutputDto outputDto) {

        //采购退货单 处理器
        if(WmsInterfaceOrderType.OUT_PURCHASE_RETURN.equals(outputDto.getOrderType())){

            return purchaseReturnAuditPassProcessor;

        //采购换货出库 处理器
        }else if(WmsInterfaceOrderType.OUT_PURCHASE_EXG.equals(outputDto.getOrderType())){

            return purchaseExgOutProcessor;

        //销售出库单
        }else if(WmsInterfaceOrderType.OUT_SALE_OUT.equals(outputDto.getOrderType())){

            return saleorderOutputProcessor;

        //销售售后换货出库
        }else if(WmsInterfaceOrderType.EXCHANG_SALEORDER.equals(outputDto.getOrderType())){

            return afterSaleOutPutProcessor;

        //外借出库
        }else if(WmsInterfaceOrderType.OUT_LENDOUT.equals(outputDto.getOrderType())){

            return lendOutputProcessor;
            //出库单类型-领用出库
        }else if(WmsInterfaceOrderType.OUT_RECEIVER.equals(outputDto.getOrderType())){
            return wmsScrappedOutputProcessor;

            //出库单类型-报废出库单
        }else if(WmsInterfaceOrderType.OUT_SCRAPPED.equals(outputDto.getOrderType())){

            return wmsScrappedOutputProcessor;
        }else if(WmsInterfaceOrderType.OUT_DIRECT_SALE_OUT.equals(outputDto.getOrderType())){
            //出库单类型-直发出库单
            return saleorderDirectOutputProcessor;
        }else if(WmsInterfaceOrderType.INVENTORY_OUT.equals(outputDto.getOrderType())){
            //  wms 盘亏出库
            return wmsInventoryOutputProcessor;
        } else if (WmsInterfaceOrderType.UNIT_CONVERSION_OUT.equals(outputDto.getOrderType())) {
            return wmsUnitConversionOutputProcessor;
        } else if (WmsInterfaceOrderType.SAMPLE_ORDER_OUT.equals(outputDto.getOrderType())){
            return WmsSampleOutputProcessor;
        }
        return null;
    }


    /**
     * 入库单回传处理
     * @param data
     * @return
     */
    private AbstractInputOrOutputProcessor dealWithInputOrder(String data) throws Exception{

        List<InputOrderDto> inputOrderDtos = convertDataToBean(data,InputOrderDto.class);
        if(CollectionUtils.isEmpty(inputOrderDtos)){
            throw new Exception("入库单回传列表为空");
        }

        for(InputOrderDto inputOrderDto :  inputOrderDtos){
            WMSCalllBackProcessor calllBackProcessor = getInputOrderProcess(inputOrderDto);
            calllBackProcessor.dealWithRequest(inputOrderDto);
        }

        return null;
    }


    /**
     * 入库单处理器
     * @param inputOrderDto
     * @return
     */
    private WMSCalllBackProcessor getInputOrderProcess(InputOrderDto inputOrderDto) {

        //采购入库单的处理器
        if(WmsInterfaceOrderType.INPUT_PURCHASE.equals(inputOrderDto.getASNType())){
            return purchaseInputOrderProcessor;
        }

        //采购售后换货入库的处理器
        if(WmsInterfaceOrderType.INPUT_PURCHASE_EXG.equals(inputOrderDto.getASNType())){
            return purchaseExgInputProcessor;
        }
        //销售换货,退货入库
        if(WmsInterfaceOrderType.EXCHANG_SALEORDER.equals(inputOrderDto.getASNType()) || WmsInterfaceOrderType.INPUT_SALE_RETURN.equals(inputOrderDto.getASNType()) ){

            return afterSaleInPutProcessor;
        }

        if(WmsInterfaceOrderType.INPUT_LEND_IN.equals(inputOrderDto.getASNType())){
            return lendInputProcessor;
        }

        //平滑上线初始化处理器
        if(WmsInterfaceOrderType.INPUT_CS_INIT.equals(inputOrderDto.getASNType())){
            return csInputOrderProcessor;
        }
        //盘盈入库
        if(WmsInterfaceOrderType.INPUT_CHECK_MORE.equals(inputOrderDto.getASNType())){
            return wmsSurplusInOrderProcessor;
        }
        //直发采购入库分发器处理
        if(WmsInterfaceOrderType.DELIVERY_DIRECT_IN.equals(inputOrderDto.getASNType())){
            return deliveryDirectPurchaseInputOrderProcessor;
        }

        // 单位转换单入库
        if(WmsInterfaceOrderType.UNIT_CONVERSION_IN.equals(inputOrderDto.getASNType())){
            return wmsUnitConversionInOrderProcessor;
        }

        return null;
    }

    protected <T> List<T>  convertDataToBean(String data,Class<T> cls) {

        JSONObject dataObj = JSONObject.fromObject(data);
        JSONArray headerArray = dataObj.getJSONObject("xmldata").getJSONArray("header");

        Gson gson = new Gson();
        List<T> resultList = new ArrayList<>();

        headerArray.stream().forEach(jsonObj -> {
            resultList.add(gson.fromJson(jsonObj.toString(),cls));
        });

        return resultList;
    }

    /**
     * 库存调整单回传处理
     *
     * @param data
     * @return
     * @throws Exception
     */
    private InventoryAdjustmentProcessor dealWithInventoryAdjustment(String data) throws Exception{
        List<InventoryAdjustmentDto> inventoryAdjustmentDtos = convertDataToBean(data,InventoryAdjustmentDto.class);
        if (CollectionUtils.isEmpty(inventoryAdjustmentDtos)){
            return null;
        }
        for (InventoryAdjustmentDto inventoryAdjustmentDto : inventoryAdjustmentDtos) {
            inventoryAdjustmentProcessor.dealWithRequest(inventoryAdjustmentDto);
        }
        return inventoryAdjustmentProcessor;
    }

    /**
     * 库存转移单回传处理
     *
     * @param data
     * @return
     * @throws Exception
     */
    private InventoryTransferProcessor dealWithInventoryTransfer(String data) throws Exception{
        List<WmsInventoryTransferDto> wmsInventoryTransferDtos = convertDataToBean(data,WmsInventoryTransferDto.class);
        if (CollectionUtils.isEmpty(wmsInventoryTransferDtos)){
            return null;
        }
        for (WmsInventoryTransferDto wmsInventoryTransferDto : wmsInventoryTransferDtos) {
            inventoryTransferProcessor.dealWithRequest(wmsInventoryTransferDto);
        }
       return inventoryTransferProcessor;
    }

    /**
     * WMS发起开发票处理
     *
     * @param data
     * @return
     * @throws Exception
     */
    private OpenInvoiceProcessor dealWithInvoiceCallback(String data) throws Exception{
        List<WmsInvoiceCallbackDto> wmsInvoiceCallbackDtos = convertDataToBean(data, WmsInvoiceCallbackDto.class);
        if (CollectionUtils.isEmpty(wmsInvoiceCallbackDtos)){
            return null;
        }
        for (WmsInvoiceCallbackDto wmsInvoiceCallbackDto : wmsInvoiceCallbackDtos) {
            openInvoiceProcessor.dealWithRequest(wmsInvoiceCallbackDto);
        }
        return openInvoiceProcessor;
    }


    /**
     * 包裹信息回传处理
     *
     * @param data
     * @return
     * @throws Exception
     */
    private ExpressCallbackProcessor dealWithExpress(String data) throws Exception{
        List<ExpressHeaderDto> deliveryHeaderDtos = convertDataToBean(data,ExpressHeaderDto.class);
        if (CollectionUtils.isEmpty(deliveryHeaderDtos)){
            return null;
        }
        for (ExpressHeaderDto deliveryHeaderDto : deliveryHeaderDtos) {
            expressCallbackProcessor.dealWithRequest(deliveryHeaderDto);
        }
        return expressCallbackProcessor;
    }

    /**
     * @description: 入库厂家SN码回传处理
     * @param data
     * @return {@link InputSnProcessor}
     * @throws Exception
     * <AUTHOR>
     * @date 2020/11/16 10:48
     */
    public InputSnProcessor dealWithInputSnCallback(String data) throws Exception {
        List<InputSnDto> inputSnDtos = convertDataToBean(data, InputSnDto.class);
        if (CollectionUtils.isEmpty(inputSnDtos)){
            throw new Exception("入库厂家回传SN列表为空");
        }
        for (InputSnDto inputSNDto : inputSnDtos) {
            inputSNProcessor.dealWithRequest(inputSNDto);
        }
        return inputSNProcessor;
    }

    /**
     * @description: 出库厂家SN码回传处理
     * @param data
     * @return {@link OutputSnProcessor}
     * @throws Exception
     * <AUTHOR>
     * @date 2020/11/16 10:50
     */
    public OutputSnProcessor dealWithOutputSnCallback(String data) throws Exception {
        List<OutputSnDto> outputSnDtos = convertDataToBean(data, OutputSnDto.class);
        if (CollectionUtils.isEmpty(outputSnDtos)){
            throw new Exception("出库厂家回传SN列表为空");
        }
        for (OutputSnDto outputSnDto : outputSnDtos) {
            outputSnProcessor.dealWithRequest(outputSnDto);
        }
        return outputSnProcessor;
    }


}


