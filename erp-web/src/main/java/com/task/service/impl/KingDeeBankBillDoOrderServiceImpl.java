package com.task.service.impl;

import com.alibaba.fastjson.JSON;
import com.task.service.KingDeeBankBillDoOrderService;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.vo.AfterSalesDetailVo;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.authorization.model.Company;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.aftersale.service.AfterSalesApiService;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;
import com.vedeng.erp.buyorder.service.BuyorderExpenseApiService;
import com.vedeng.erp.finance.dto.BankBillDto;
import com.vedeng.erp.finance.dto.PayApplyDto;
import com.vedeng.erp.finance.dto.PayVedengBankDto;
import com.vedeng.erp.finance.service.BankBillApiService;
import com.vedeng.erp.finance.service.CapitalBillApiService;
import com.vedeng.erp.finance.service.PayApplyApiService;
import com.vedeng.erp.finance.service.PayVedengBankApiService;
import com.vedeng.erp.kingdee.dto.KingDeePayBillDto;
import com.vedeng.finance.model.CapitalBill;
import com.vedeng.finance.model.CapitalBillDetail;
import com.vedeng.finance.model.PayApplyDetail;
import com.vedeng.finance.service.CapitalBillService;
import com.vedeng.finance.service.InvoiceAfterService;
import com.vedeng.finance.service.PayApplyService;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.order.service.BuyorderService;
import com.vedeng.system.service.CompanyService;
import com.vedeng.system.service.UserService;
import com.vedeng.system.service.VerifiesRecordService;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @description 实现类
 * @date 2022/9/19 13:41
 **/
@Service
@Slf4j
public class KingDeeBankBillDoOrderServiceImpl implements KingDeeBankBillDoOrderService {

    @Autowired
    private CompanyService companyService;

    @Autowired
    private BuyorderService buyorderService;

    @Autowired
    private UserService userService;

    @Autowired
    private CapitalBillService capitalBillService;

    @Autowired
    private AfterSalesService afterSalesOrderService;

    @Autowired
    @Qualifier("payApplyService")
    private PayApplyService payApplyService;

    @Autowired
    @Qualifier("invoiceAfterService")
    private InvoiceAfterService invoiceAfterService;

    @Autowired
    private PayApplyApiService payApplyApiService;

    @Autowired
    private BankBillApiService bankBillApiService;

    @Autowired
    private PayVedengBankApiService payVedengBankApiService;

    @Autowired
    private BuyorderExpenseApiService buyorderExpenseApiService;

    @Autowired
    private CapitalBillApiService capitalBillApiService;
    
    @Autowired
    private AfterSalesApiService afterSalesApiService;


    @Override
    public void afterOrderVerify(PayApplyDto payApplyDto, BankBillDto bankBillDto, KingDeePayBillDto data) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        // 获取当前用户公司信息
        Company companyInfo = companyService.getCompanyByCompangId(currentUser.getCompanyId());
        // 售后类型，需要从售后单查是关联采购还是销售
        AfterSales afterSales = new AfterSales();
        afterSales.setAfterSalesId(payApplyDto.getRelatedId());
        afterSales.setCompanyId(currentUser.getCompanyId());
        AfterSalesVo afterSalesVo = afterSalesOrderService.getAfterSalesVoListById(afterSales);
        AfterSalesDetailVo afterDetailVo = new AfterSalesDetailVo();
        afterDetailVo.setAfterSalesId(payApplyDto.getRelatedId());
        afterDetailVo.setPayApplyId(payApplyDto.getPayApplyId());
        AfterSalesDetailVo afterSalesDetailVo = invoiceAfterService.getAfterCapitalBillInfo(afterDetailVo);
        // 如果不为null的话
        if (afterSalesDetailVo != null && afterSalesDetailVo.getTraderId() != null) {
            afterSalesVo.setTraderId(afterSalesDetailVo.getTraderId());
        }
        // 归属销售
        User belongUser = new User();
        // 安调维修工程师主键AFTER_SALES_INSTALLSTION_ID
        Integer afterSalesInstallstionId = 0;
        if (afterSalesVo.getTraderId() != null) {
            if (afterSalesVo.getSubjectType() == 535) {
                // 销售
                if (afterSalesVo.getTraderId() != null) {
                    belongUser = getBelongUser(afterSalesVo.getTraderId(), ErpConst.ONE);
                }
                // 销售安调
                if (afterSalesVo.getType() == 541 || afterSalesVo.getType() == 584 || afterSalesVo.getType() == 4090 || afterSalesVo.getType() == 4091) {
                    // 将安调对应的工程师查出
                    List<PayApplyDetail> payApplyDetailList =
                            payApplyService.getPayApplyDetailList(payApplyDto.getPayApplyId());
                    if (payApplyDetailList != null && payApplyDetailList.size() != 0) {
                        afterSalesInstallstionId = payApplyDetailList.get(0).getDetailgoodsId();
                    }
                }
            } else if (afterSalesVo.getSubjectType() == 536) {
                // 采购
                if (afterSalesVo.getTraderId() != null) {
                    belongUser = getBelongUser(afterSalesVo.getTraderId(), ErpConst.TWO);
                }
            } else if (afterSalesVo.getSubjectType() == 537) {
                // 第三方

                // 销售安调
                if (afterSalesVo.getType() == 550 || afterSalesVo.getType() == 585) {
                    // 将安调对应的工程师查出
                    List<PayApplyDetail> payApplyDetailList =
                            payApplyService.getPayApplyDetailList(payApplyDto.getPayApplyId());
                    if (payApplyDetailList != null && payApplyDetailList.size() != 0) {
                        afterSalesInstallstionId = payApplyDetailList.get(0).getDetailgoodsId();
                    }
                }
            }
        }
        // 添加资金流水记录
        Integer businessType = 0;
        // 销售订单退货,销售订单退款,第三方退款
        if (afterSalesDetailVo.getAfterType().intValue() == 539
                || afterSalesDetailVo.getAfterType().intValue() == 543
                || afterSalesDetailVo.getAfterType().intValue() == 551) {
            // 退款
            businessType = SysOptionConstant.ID_531;
        } else {// 订单付款
            businessType = SysOptionConstant.ID_525;
        }
        Integer traderType = 0;
        if (afterSalesVo.getSubjectType() == 535) {
            traderType = ErpConst.ONE;
        }
        if (afterSalesVo.getSubjectType() == 536) {
            traderType = ErpConst.TWO;
        }
        // 销售订单退货,销售订单退款,第三方退款
        if (afterSalesDetailVo.getAfterType().intValue() == 539
                || afterSalesDetailVo.getAfterType().intValue() == 543
                || afterSalesDetailVo.getAfterType().intValue() == 551) {
            kingDeeSaveCapitalBill(payApplyDto, bankBillDto, currentUser, belongUser, data, companyInfo, afterSalesVo.getAfterSalesNo()
                    , afterSalesVo.getTraderId(), afterSalesVo.getServiceUserId(), businessType, ErpConst.THREE, traderType, afterSalesInstallstionId, 1);
        } else {
            kingDeeSaveCapitalBill(payApplyDto, bankBillDto, currentUser, belongUser, data, companyInfo, afterSalesVo.getAfterSalesNo()
                    , afterSalesVo.getTraderId(), afterSalesVo.getServiceUserId(), businessType, ErpConst.THREE, traderType, afterSalesInstallstionId, 2);
        }

        // 修改订单结款匹配项目
        Integer matchedObject = 857;
        // 销售订单退货,销售订单退款,第三方退款
        if (afterSalesDetailVo.getAfterType().intValue() == 539
                || afterSalesDetailVo.getAfterType().intValue() == 543
                || afterSalesDetailVo.getAfterType().intValue() == 551) {
            // 客户退款
            matchedObject = 858;
            // 销售订单安调、第三方安调
        } else if (afterSalesDetailVo.getAfterType() == 541 || afterSalesDetailVo.getAfterType() == 550 || afterSalesDetailVo.getAfterType() == 4090 || afterSalesDetailVo.getAfterType() == 4091) {
            // 售后安调
            matchedObject = 859;
            // 销售订单维修,第三方维修
        } else if (afterSalesDetailVo.getAfterType().intValue() == 584
                || afterSalesDetailVo.getAfterType().intValue() == 585) {
            // 售后维修
            matchedObject = 860;
        }

        BankBillDto update = new BankBillDto();
        update.setBankBillId(bankBillDto.getBankBillId());
        update.setMatchedObject(matchedObject);
        bankBillApiService.update(update);

        log.info("手动匹配银行流水刷新售后单付款状态信息 payApplyInfo:{}", JSON.toJSONString(payApplyDto));
        if (!afterSalesVo.getSubjectType().equals(536)) {
            afterSalesOrderService.refreshAmountRefundStatus(payApplyDto.getRelatedId());
        }

    }


    @Override
    public void buyOrderVerify(PayApplyDto payApplyDto, BankBillDto bankBillDto, KingDeePayBillDto data) {
        CurrentUser user = CurrentUser.getCurrentUser();
        Company companyInfo = companyService.getCompanyByCompangId(user.getCompanyId());

        //根据采购订单ID获取订单号
        Buyorder buyorder = new Buyorder();
        buyorder.setBuyorderId(payApplyDto.getRelatedId());
        buyorder.setCompanyId(user.getCompanyId());
        BuyorderVo bv = buyorderService.getBuyOrderPrintInfo(buyorder);
        // 归属销售
        User belongUser = new User();
        if (bv.getTraderId() != null) {
            belongUser = getBelongUser(bv.getTraderId(), ErpConst.TWO);
        }

        kingDeeSaveCapitalBill(payApplyDto, bankBillDto, user, belongUser, data, companyInfo, bv.getBuyorderNo()
                , bv.getTraderId(), bv.getUserId(), SysOptionConstant.ID_525, ErpConst.TWO, ErpConst.TWO, null, 2);


        BankBillDto update = new BankBillDto();
        update.setBankBillId(bankBillDto.getBankBillId());
        update.setMatchedObject(857);
        bankBillApiService.update(update);
    }


    @Override
    public void buyOrderExpenseVerify(PayApplyDto payApplyDto, BankBillDto bankBillDto, KingDeePayBillDto data) {
        CurrentUser user = CurrentUser.getCurrentUser();
        Company companyInfo = companyService.getCompanyByCompangId(user.getCompanyId());

        BuyorderExpenseDto buyorderExpenseDto = buyorderExpenseApiService.getOrderMainData(payApplyDto.getRelatedId());
        // 归属销售
        User belongUser = new User();
        if (buyorderExpenseDto.getTraderId() != null) {
            belongUser = getBelongUser(buyorderExpenseDto.getTraderId(), ErpConst.TWO);
        }
        //判断流水是否为信用还款流水
        BigDecimal needReturnPeriod = capitalBillApiService.getExpenseDebtPeriodMoney(buyorderExpenseDto.getBuyorderExpenseId());
        if (needReturnPeriod.compareTo(new BigDecimal(0)) > 0) {
            //存在未还账期的条件下，添加流水--判定为信用还款
            kingDeeSaveCapitalBill(payApplyDto, bankBillDto, user, belongUser, data, companyInfo, buyorderExpenseDto.getBuyorderExpenseNo()
                    , buyorderExpenseDto.getTraderId(), buyorderExpenseDto.getCreator(), SysOptionConstant.ID_533, ErpConst.FOUR, ErpConst.TWO, null, 2);
        } else {
            kingDeeSaveCapitalBill(payApplyDto, bankBillDto, user, belongUser, data, companyInfo, buyorderExpenseDto.getBuyorderExpenseNo()
                    , buyorderExpenseDto.getTraderId(), buyorderExpenseDto.getCreator(), SysOptionConstant.ID_525, ErpConst.FOUR, ErpConst.TWO, null, 2);
        }

        BankBillDto update = new BankBillDto();
        update.setBankBillId(bankBillDto.getBankBillId());
        update.setMatchedObject(857);
        bankBillApiService.update(update);

        //如果信用支付的话，增加信用支付流水
        capitalBillApiService.saveBillPeriodCapitalBill(buyorderExpenseDto.getBuyorderExpenseId());

        //更新费用单状态
        if (ErpConst.ZERO.equals(buyorderExpenseDto.getOrderType())) {
            buyorderExpenseApiService.directPaymentStatus(buyorderExpenseDto.getBuyorderExpenseId());
            buyorderExpenseApiService.doArrivalStatus(buyorderExpenseDto.getBuyorderExpenseId());
        } else if (ErpConst.ONE.equals(buyorderExpenseDto.getOrderType())) {
            buyorderExpenseApiService.paymentStatus(buyorderExpenseDto.getBuyorderExpenseId());
        }
    }

    private User getBelongUser(Integer traderId, Integer traderType) {
        // 归属销售
        User belongUser = new User();
        if (traderId != null) {
            // 1客户，2供应商
            belongUser = userService.getUserByTraderId(traderId, traderType);
            if (belongUser != null && belongUser.getUserId() != null) {
                belongUser = userService.getUserById(belongUser.getUserId());
            }
        }
        return belongUser;
    }

    private void kingDeeSaveCapitalBill(PayApplyDto payApplyDto, BankBillDto bankBillDto, CurrentUser user,
                                        User belongUser, KingDeePayBillDto data, Company companyInfo, String orderNo, Integer traderId,
                                        Integer orderUserId, Integer businessType, Integer orderType,
                                        Integer traderType, Integer afterSalesInstallstionId, int saveFunction) {
        //添加资金流水记录
        CapitalBill capitalBill = new CapitalBill();
        if (user != null) {
            capitalBill.setCreator(user.getId());
            capitalBill.setAddTime(DateUtil.sysTimeMillis());
            capitalBill.setCompanyId(user.getCompanyId());
        }
        capitalBill.setCurrencyUnitId(1);
        capitalBill.setTraderTime(DateUtil.sysTimeMillis());
        Integer paymentType = 0;
        if (Objects.equals(bankBillDto.getBankTag(), 2)) {
            //南京银行
            paymentType = 641;
        } else if (Objects.equals(bankBillDto.getBankTag(), 3)) {
            //中国银行
            paymentType = 716;
        }
        capitalBill.setBankBillId(bankBillDto.getBankBillId());
        capitalBill.setTranFlow(bankBillDto.getTranFlow());
        capitalBill.setPaymentType(paymentType);
        capitalBill.setTraderType(payApplyDto.getTraderMode() == 528 ? 5 : 2);
        capitalBill.setTraderSubject(payApplyDto.getTraderSubject());
        capitalBill.setTraderMode(payApplyDto.getTraderMode());
        capitalBill.setAmount(payApplyDto.getAmount());
        capitalBill.setPayee(payApplyDto.getTraderName());
        capitalBill.setPayeeBankAccount(payApplyDto.getBankAccount());
        capitalBill.setPayeeBankName(payApplyDto.getBank());
        PayVedengBankDto payVedengBankDto = payVedengBankApiService.queryInfoByPayBankNo(data.getFQzokYhzhhm());
        if (Objects.nonNull(payVedengBankDto)) {
            capitalBill.setPayerBankName(payVedengBankDto.getPayBankName());
            capitalBill.setPayerBankAccount(payVedengBankDto.getPayBankNo());
        }
        capitalBill.setPayer(companyInfo.getCompanyName());
        //资金流水交易备注
        capitalBill.setComments("");
        List<CapitalBillDetail> capitalBillDetails = new ArrayList<>();
        CapitalBillDetail capitalBillDetail2 = new CapitalBillDetail();
        capitalBillDetail2.setAmount(payApplyDto.getAmount());
        //订单付款
        capitalBillDetail2.setBussinessType(businessType);
        //采购订单
        capitalBillDetail2.setOrderType(orderType);
        capitalBillDetail2.setRelatedId(payApplyDto.getRelatedId());
        capitalBillDetail2.setOrderNo(orderNo);
        capitalBillDetail2.setTraderId(traderId);
        capitalBillDetail2.setTraderType(traderType);
        capitalBillDetail2.setUserId(orderUserId);
        if (belongUser != null && belongUser.getOrgName() != null && belongUser.getOrgId() != null) {
            capitalBillDetail2.setOrgName(belongUser.getOrgName());
            capitalBillDetail2.setOrgId(belongUser.getOrgId());
        }
        if (afterSalesInstallstionId != null) {
            capitalBillDetail2.setAfterSalesInstallstionId(afterSalesInstallstionId);
        }

        capitalBillDetails.add(capitalBillDetail2);
        capitalBill.setCapitalBillDetails(capitalBillDetails);

        CapitalBillDetail capitalBillDetail = new CapitalBillDetail();
        capitalBillDetail.setAmount(payApplyDto.getAmount());
        //订单付款
        capitalBillDetail.setBussinessType(businessType);
        //采购订单
        capitalBillDetail.setOrderType(orderType);
        capitalBillDetail.setRelatedId(payApplyDto.getRelatedId());
        capitalBillDetail.setOrderNo(orderNo);
        capitalBillDetail.setTraderId(traderId);
        capitalBillDetail.setTraderType(traderType);
        capitalBillDetail.setUserId(orderUserId);
        if (belongUser != null && belongUser.getOrgName() != null && belongUser.getOrgId() != null) {
            capitalBillDetail.setOrgName(belongUser.getOrgName());
            capitalBillDetail.setOrgId(belongUser.getOrgId());
        }
        capitalBill.setCapitalBillDetail(capitalBillDetail);
        ResultInfo<?> resultInfo;
        if (saveFunction == 1) {
            resultInfo = capitalBillService.saveRefundCapitalBill(capitalBill);
        } else {
            resultInfo = capitalBillService.saveAddCapitalBill(capitalBill);
        }
        try {
            Map<String,Object> result_map = (Map<String,Object>) resultInfo.getData();
            Integer capitalBillId = Integer.parseInt(result_map.get("capitalBillId").toString());
            log.info("金蝶付款流水回传ERP,保存资金流水成功 capitalBillId:{},payApplyId:{}", capitalBillId, payApplyDto.getPayApplyId());
            if (Objects.nonNull(payApplyDto.getPayApplyId()) && Objects.nonNull(capitalBillId)) {
                capitalBillApiService.saveRPayApplyJCapitalBill(payApplyDto.getPayApplyId(), capitalBillId);
            }
            log.info("金蝶付款流水回传ERP,售后退货退款付款,更新客户账户信息 afterSalesId:{},payApplyId:{}", payApplyDto.getRelatedId(), payApplyDto.getPayApplyId());
            if (saveFunction == 1 && Objects.nonNull(payApplyDto.getPayApplyId()) && Objects.nonNull(payApplyDto.getRelatedId())) {
                afterSalesApiService.handleCustomerBankAccount(payApplyDto.getPayApplyId(), payApplyDto.getRelatedId());
            }
        } catch (Exception e) {
            log.error("金蝶付款流水回传ERP,保存付款申请与资金流水关系异常", e);
        }
    }
}
