package com.vedeng.erp.saleorder.service;

import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.erp.saleorder.dao.SaleOrderMapper;
import com.vedeng.erp.saleorder.dao.SaleorderGoodsMaoliBuypriceMapper;
import com.vedeng.erp.saleorder.domain.SaleorderGoodsMaoliBuyprice;
import com.vedeng.erp.saleorder.dto.SaleOrderGoodsImageDto;
import com.vedeng.erp.saleorder.dto.SaleOrderGoodsMaoLiBuyPriceDto;
import com.vedeng.erp.saleorder.feign.OneDataSkuPriceFeignApi;
import com.vedeng.onedataapi.api.skuinfo.req.SkuReqDto;
import com.vedeng.onedataapi.api.skuinfo.resp.SkuAvgPriceDto;
import com.vedeng.onedataapi.api.skuinfo.resp.SkuAvgPriceResDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/11/8
 */
@Service
@Slf4j
public class SaleOrderDataApiServiceImpl implements  SaleOrderDataApiService{
    static BigDecimal initPrice = new BigDecimal("0.00");
    static Integer STAY_QUERY_STATUS = 0 ;//0初始状态，待查询

    @Autowired
    private SaleOrderMapper saleOrderMapper;

    @Autowired
    private OneDataSkuPriceFeignApi oneDataSkuPriceFeignApi;

    @Autowired
    private SaleorderGoodsMaoliBuypriceMapper saleorderGoodsMaoliBuypriceMapper;

    @Override
    public void initSaleOrderGoodsMaoli(List<SaleOrderGoodsMaoLiBuyPriceDto> initList) {
        if(CollectionUtils.isEmpty(initList)){
            log.info("无待插入的毛利明细数据");
            return;
        }
        Map<String,String> checkRepeatMap = new HashMap<>();
        List<SaleorderGoodsMaoliBuyprice> batchList = new ArrayList();
        for(SaleOrderGoodsMaoLiBuyPriceDto dto : initList){
            if(!checkRepeatMap.containsKey(dto.getSkuNo())){//防重复检查
                SaleorderGoodsMaoliBuyprice price = getSaleorderGoodsMaoliBuyprice(dto);
                batchList.add(price);
                checkRepeatMap.put(dto.getSkuNo(),dto.getSkuNo());
            }
        }
        //先按订单号维度删除毛利数据，再重新插入，重新提交审核的情况
        saleorderGoodsMaoliBuypriceMapper.deleteBySaleorderId(initList.get(0).getSaleorderId());
        saleorderGoodsMaoliBuypriceMapper.batchInsert(batchList);
    }

    @Override
    public void updateSaleOrderGoodsMaoliForJob() {
        List<Integer> saleOrderIdList = saleorderGoodsMaoliBuypriceMapper.selectUnReadMaoliSaleOrderId();
        if(CollectionUtils.isNotEmpty(saleOrderIdList)){
            for(Integer saleOrderId : saleOrderIdList){
                log.info("正在获取销售订单{}的毛利",saleOrderId);
                doSaleOrderGoodsMaoliFromOneData(saleOrderId);
            }
        }
    }

    @Override
    public List<SaleOrderGoodsMaoLiBuyPriceDto> queryListForSaleOrderGoodsMaoli(Integer saleOrderId) {
        List<SaleorderGoodsMaoliBuyprice> buypriceList =  saleorderGoodsMaoliBuypriceMapper.queryListBySaleorderId(saleOrderId);
        List<SaleOrderGoodsMaoLiBuyPriceDto> priceResultList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(buypriceList)){
            for(SaleorderGoodsMaoliBuyprice buyprice : buypriceList){
                SaleOrderGoodsMaoLiBuyPriceDto priceDto = new SaleOrderGoodsMaoLiBuyPriceDto(buyprice.getSaleorderId(),buyprice.getSkuNo(),buyprice.getBuyPrice(),buyprice.getBuyPriceDesc(),buyprice.getPriceStatus());
                if(1== buyprice.getPriceStatus()){
                    priceResultList.add(priceDto);
                }
            }
        }
        return priceResultList;
    }

    @Override
    public List<SaleOrderGoodsImageDto> queryListForSaleOrderGoodsImage(List<String> skuNoList) {
        return saleOrderMapper.getSaleOrderGoodsImage(skuNoList);
    }

    /**
     *
     *时间区间
     *1:取近1个月采购均价
     *2:取近1个月和近3个月采购均价
     *3:取近1个月和近3 个月和近6个月采购均价
     *4:取近1 个月和近3 个月和近6 个月和近12个月采购均价
     *5:取近1个月和近3个月和近6 个月和近12 个月和近18个月采购均价
     *6:取近1个月和近3个月和近6 个月和近12 个月和近18个月和近24个月采购均价
     *
     * 根据销售订单从onedata拉取毛利价格
     * @param saleOrderId
     */
    private void doSaleOrderGoodsMaoliFromOneData(Integer saleOrderId){
        List<SaleorderGoodsMaoliBuyprice> priceList = saleorderGoodsMaoliBuypriceMapper.queryListBySaleorderId(saleOrderId);

        if(CollectionUtils.isEmpty(priceList)){
            log.info("销售订单{}下无商品",saleOrderId);
            return ;
        }
        List<String> skuNoList  = priceList.stream().map(SaleorderGoodsMaoliBuyprice::getSkuNo).collect(Collectors.toList());
        SkuReqDto skuReqDto = new SkuReqDto();
        skuReqDto.setSkuNo(skuNoList);
        skuReqDto.setTimeLimit(6);
        Date now = new Date();
        Map<String, SkuAvgPriceDto> priceMap = new HashMap<>();
        try{
            RestfulResult<List<SkuAvgPriceResDto>> result =  oneDataSkuPriceFeignApi.getCategorySkuRankList(skuReqDto);
            if(result.isSuccess()){//接口成功的情况下，下次不会再调用了
                List<SkuAvgPriceResDto> resultList =  result.getData();
                if(CollectionUtils.isNotEmpty(resultList)){
                    for(SkuAvgPriceResDto skuAvgPriceResDto : resultList){
                        if(CollectionUtils.isNotEmpty(skuAvgPriceResDto.getPriceList())){
                            priceMap.put(skuAvgPriceResDto.getSkuNo(),skuAvgPriceResDto.getPriceList().get(0));
                        }
                    }
                }

                //有可能存在skuAvgPriceResDto均没有取到的情况
                List<SaleorderGoodsMaoliBuyprice> updatedPriceList = priceList.stream()
                        .peek(item -> {
                            SkuAvgPriceDto priceDto = priceMap.get(item.getSkuNo());
                            if(priceDto !=null) {
                                BigDecimal price = priceDto.getPrice();
                                String priceDesc = priceDto.getTimeLimitDesc();
                                item.setBuyPrice(price != null ? price : initPrice);
                                item.setBuyPriceDesc(StringUtils.isNotBlank(priceDesc) ? priceDesc : "");
                            }else{
                                //近两年无采购价参考
                                item.setBuyPrice(initPrice);
                                item.setBuyPriceDesc("近两年无采购价参考");
                            }
                            item.setPriceStatus(1);//状态置为已成功获取
                            item.setModTime(now);
                        })
                        .collect(Collectors.toList());
                saleorderGoodsMaoliBuypriceMapper.batchUpdate(updatedPriceList);
            }else {
                throw new RuntimeException("查询订单毛利失败-from-oneData");
            }
        }catch (Exception e ){
            log.error("查询毛利失败",e);
            List<SaleorderGoodsMaoliBuyprice> updatedPriceList =
                priceList.stream()
                .peek(item -> {
                    item.setBuyPrice(initPrice);
                    item.setBuyPriceDesc("");
                    item.setPriceStatus(2);//状态置为失败2
                    item.setModTime(now);
                })
                .collect(Collectors.toList());
            //将标识更新为失败2
            saleorderGoodsMaoliBuypriceMapper.batchUpdate(updatedPriceList);
        }
    }

    private SaleorderGoodsMaoliBuyprice getSaleorderGoodsMaoliBuyprice(SaleOrderGoodsMaoLiBuyPriceDto dto) {
        SaleorderGoodsMaoliBuyprice price = new SaleorderGoodsMaoliBuyprice();
        price.setSaleorderId(dto.getSaleorderId());
        price.setBuyPrice(initPrice);
        price.setBuyPriceDesc("");//默认为默认空字符串，防止出现null的情况
        price.setSkuNo(dto.getSkuNo());
        price.setPriceStatus(STAY_QUERY_STATUS);

        Date now = new Date();
        price.setCreator(1);
        price.setAddTime(now);
        price.setCreatorName("SYSTEM");
        price.setUpdater(1);
        price.setUpdaterName("SYSTEM");
        price.setModTime(now);
        return price;
    }
}
