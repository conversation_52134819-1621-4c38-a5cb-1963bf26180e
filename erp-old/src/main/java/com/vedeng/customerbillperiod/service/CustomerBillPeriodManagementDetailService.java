package com.vedeng.customerbillperiod.service;

import com.vedeng.customerbillperiod.dto.GenerateCustomerBillPeriodManagementDetailDto;
import com.vedeng.customerbillperiod.dto.RollbackCustomerBillPeriodManagementDetailDto;
import com.vedeng.customerbillperiod.exception.CustomerBillPeriodException;
import com.vedeng.customerbillperiod.model.CustomerBillPeriodOverdueManagementDetail;
import com.vedeng.customerbillperiod.model.vo.CustomerBillPeriodOverdueManagementDetailVo;

import java.util.List;

/**
 * @Author: daniel
 * @Date: 2021/7/26 14 35
 * @Description: 客户账期管理明细服务接口
 */
public interface CustomerBillPeriodManagementDetailService {

    /**
     * 生成客户账期管理明细编码（发货/开票）
     * @param managementDetailDto 客户账期管理明细编码
     * @throws CustomerBillPeriodException 异常
     */
    void generateCustomerBillPeriodManagementDetail(GenerateCustomerBillPeriodManagementDetailDto managementDetailDto) throws CustomerBillPeriodException;


    /**
     * 1、当售后退货/售后退票的售后单完结时，生成负数的客户账期管理编码
     * 2、当作废订单物流时，生成负数的客户账期管理编码
     * @param rollbackCustomerBillPeriodManagementDetailDto 回滚客户账期管理编码传输类
     * @throws CustomerBillPeriodException 异常
     */
    void rollbackCustomerBillPeriodManagementDetail(RollbackCustomerBillPeriodManagementDetailDto rollbackCustomerBillPeriodManagementDetailDto) throws CustomerBillPeriodException;

    /**
     * @description: 获取逾期时间 大于等于 当前天数+入参天数  逾期管理明细
     * @return: 逾期管理明细已逾期结果
     * @author: Strange
     * @date: 2021/7/28
     **/
    List<CustomerBillPeriodOverdueManagementDetailVo> getCustomerBillPeriodOverDueInfoByOverdueDay(Integer dayNum);

    /**
     * @description: 处理已逾期数据
     * @return:
     * @author: Strange
     * @date: 2021/7/29
     **/
    void dealOverDuedcustomerBill(List<CustomerBillPeriodOverdueManagementDetailVo> overduedList);
}
