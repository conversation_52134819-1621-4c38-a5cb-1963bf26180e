package com.vedeng.erp.kingdee.task.batch;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.batch.job.PaySaleOrderAfterSaleBalanceBatchJob;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 销售售后退款至客户余额 流水推送job
 */
@JobHandler(value = "AfterSaleToBalanceBatchTask")
@Component
public class AfterSaleToBalanceBatchTask extends AbstractJobHandler {
    @Autowired
    private JobLauncher jobLauncher;
    @Autowired
    PaySaleOrderAfterSaleBalanceBatchJob batchJob;


    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("==================AfterSaleToBalanceBatchTask开始====================");
        JobParameters jobParameters = new TaskBatchHandle().buildJobParameters(param);
        Job job = batchJob.afterSaleToBalanceBatchJob();
        jobLauncher.run(job, jobParameters);
        XxlJobLogger.log("==================AfterSaleToBalanceBatchTask结束====================");
        return SUCCESS;
    }
}
