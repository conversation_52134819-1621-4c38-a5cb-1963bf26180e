package com.vedeng.aftersales.dao;


import com.vedeng.aftersales.model.RInvoiceJInvoice;

import java.math.BigDecimal;
import java.util.List;

public interface RInvoiceJInvoiceMapper {

    int deleteByPrimaryKey(Integer rInvoiceJInvoiceId);

    int insert(RInvoiceJInvoice record);

    int insertSelective(RInvoiceJInvoice record);

    RInvoiceJInvoice selectByPrimaryKey(Integer rInvoiceJInvoiceId);

    int updateByPrimaryKeySelective(RInvoiceJInvoice record);

    int updateByPrimaryKey(RInvoiceJInvoice record);
    
    /**
     * <b>Description:</b><br> 排除已退票的剩余金额
     * @param invoiceId
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2018年8月7日 下午3:12:57
     */
    BigDecimal getAfterInvoiceTotalAmount(Integer invoiceId);
    
    /**
     * <b>Description:</b><br> 查询关联发票关系表
     * @param invoiceId
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2018年8月7日 下午6:03:58
     */
    List<RInvoiceJInvoice> getRInvoiceJInvoiceList(Integer invoiceId);
}