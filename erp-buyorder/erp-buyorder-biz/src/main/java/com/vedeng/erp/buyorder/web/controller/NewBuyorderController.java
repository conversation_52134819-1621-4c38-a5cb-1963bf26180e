package com.vedeng.erp.buyorder.web.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.common.constants.Contant;
import com.vedeng.activiti.constants.ProcessConstants;
import com.vedeng.activiti.model.AssigneeVo;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.aftersales.constant.InstallPolicyInstallTypeEnum;
import com.vedeng.aftersales.model.*;
import com.vedeng.aftersales.model.vo.*;
import com.vedeng.aftersales.service.AfterSaleServiceStandardService;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.authorization.dao.UserDetailMapper;
import com.vedeng.authorization.model.Company;
import com.vedeng.authorization.model.User;
import com.vedeng.authorization.model.UserDetail;
import com.vedeng.common.annotation.SystemControllerLog;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.OrderDataUpdateConstant;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.annotation.ExcludeAuthorization;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.annotation.NoRepeatSubmit;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.enums.BusinessSourceTypeEnum;
import com.vedeng.common.core.enums.SupplierAssetEnum;
import com.vedeng.common.exception.ShowErrorMsgException;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.service.BaseService;
import com.vedeng.common.shiro.JedisUtils;
import com.vedeng.common.util.*;
import com.vedeng.common.validator.FormToken;
import com.vedeng.erp.aftersale.dto.AfterBuyorderInvoiceDto;
import com.vedeng.erp.aftersale.dto.AfterBuyorderInvoiceGoodsDto;
import com.vedeng.erp.aftersale.facade.BuyOrderAfterSalesDetailFacade;
import com.vedeng.erp.aftersale.service.BuyorderAfterSalesApiService;
import com.vedeng.erp.buyorder.bizProcessor.core.ext.OverBuyordeChangeAfterSalesCore;
import com.vedeng.erp.buyorder.bizProcessor.core.ext.OverBuyordeReturnAfterSalesCore;
import com.vedeng.erp.buyorder.bizProcessor.dto.BizDto;
import com.vedeng.erp.buyorder.bizProcessor.manager.OrderProcessorManager;
import com.vedeng.erp.buyorder.bizProcessor.processor.overBuyorderChangeSales.afterProcessor.ext.DealBuyorderForBuyChangeAfterProcessor;
import com.vedeng.erp.buyorder.bizProcessor.processor.overBuyorderChangeSales.afterProcessor.ext.DealSaleorderForBuyChangeAfterProcessor;
import com.vedeng.erp.buyorder.bizProcessor.processor.overBuyorderChangeSales.preProcessor.ext.WMSBASCProcessor;
import com.vedeng.erp.buyorder.bizProcessor.processor.overBuyorderReturnSales.afterProcessor.ext.DealBuyorderForBuyReturnAfterProcessor;
import com.vedeng.erp.buyorder.bizProcessor.processor.overBuyorderReturnSales.afterProcessor.ext.DealSaleorderForBuyReturnAfterProcessor;
import com.vedeng.erp.buyorder.bizProcessor.processor.overBuyorderReturnSales.afterProcessor.ext.DealTicketTaskForBuyReturnProcessor;
import com.vedeng.erp.buyorder.bizProcessor.processor.overBuyorderReturnSales.preProcessor.ext.WMSBASRProcessor;
import com.vedeng.erp.buyorder.common.constant.AfterSalesConstant;
import com.vedeng.erp.buyorder.common.constant.BuyOrderModifyStatus;
import com.vedeng.erp.buyorder.common.enums.AfterSalesStatus;
import com.vedeng.erp.buyorder.common.enums.AfterSalesTypeEnum;
import com.vedeng.erp.buyorder.common.utils.NewBuyOrderUtils;
import com.vedeng.erp.buyorder.common.utils.ValidStatusProcessUtil;
import com.vedeng.erp.buyorder.dto.*;
import com.vedeng.erp.buyorder.mapper.ExpressDetailMapper;
import com.vedeng.erp.buyorder.service.*;
import com.vedeng.erp.buyorder.vo.AuditRecordInstanceVo;
import com.vedeng.erp.buyorderexpense.mapstruct.SaleOrderGoodsDetailConvertor;
import com.vedeng.erp.buyorderexpense.service.BuyorderExpenseService;
import com.vedeng.erp.finance.cmd.SettlementBillQueryCmd;
import com.vedeng.erp.finance.dto.InvoiceApplyDto;
import com.vedeng.erp.finance.enums.InvoiceTaxTypeEnum;
import com.vedeng.erp.finance.service.SettlementBillApiService;
import com.vedeng.erp.saleorder.dto.SaleOrderGoodsDetailDto;
import com.vedeng.erp.saleorder.service.SaleOrderGoodsApiService;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.api.CommunicateVoiceTaskApi;
import com.vedeng.erp.trader.constant.AiConstant;
import com.vedeng.erp.trader.dto.CommunicateVoiceTaskDto;
import com.vedeng.erp.trader.dto.SupplierAssetApiDto;
import com.vedeng.erp.trader.dto.TraderSupplierDto;
import com.vedeng.erp.trader.dto.VoiceFieldResultDto;
import com.vedeng.erp.trader.service.SupplierAssetApiService;
import com.vedeng.erp.trader.service.TraderSupplierApiService;
import com.vedeng.finance.model.InvoiceApply;
import com.vedeng.finance.model.PayApply;
import com.vedeng.finance.service.InvoiceService;
import com.vedeng.finance.service.PayApplyService;
import com.vedeng.firstengage.dao.ProductCompanyMapper;
import com.vedeng.firstengage.dao.RegistrationNumberMapper;
import com.vedeng.firstengage.model.ProductCompany;
import com.vedeng.firstengage.model.RegistrationNumber;
import com.vedeng.goods.model.CoreSpuGenerate;
import com.vedeng.goods.model.dto.CoreSpuBaseDTO;
import com.vedeng.goods.service.BaseGoodsService;
import com.vedeng.goods.service.VgoodsService;
import com.vedeng.logistics.eums.WarehouseGoodsInEnum;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.Logistics;
import com.vedeng.logistics.model.WarehouseGoodsOutInItem;
import com.vedeng.logistics.model.outIn.OutInDetail;
import com.vedeng.logistics.service.*;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.BuyorderModifyApply;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.StepBar;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.model.vo.BuyorderModifyApplyVo;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import com.vedeng.order.service.*;
import com.vedeng.price.dto.SkuPriceInfoDetailResponseDto;
import com.vedeng.price.dto.SkuPriceInfoPurchaseDto;
import com.vedeng.price.service.BasePriceService;
import com.vedeng.system.dao.VerifiesInfoMapper;
import com.vedeng.system.model.*;
import com.vedeng.system.model.vo.AddressVo;
import com.vedeng.system.model.vo.ParamsConfigVo;
import com.vedeng.system.service.*;
import com.vedeng.trader.model.CommunicateRecord;
import com.vedeng.trader.model.TraderContact;
import com.vedeng.trader.model.TraderSupplier;
import com.vedeng.trader.model.vo.TraderAddressVo;
import com.vedeng.trader.model.vo.TraderContactVo;
import com.vedeng.trader.service.TraderCustomerService;
import com.vedeng.trader.service.TraderSupplierService;
import com.wms.service.CancelTypeService;
import com.xxl.rpc.util.IpUtil;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import org.activiti.engine.HistoryService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description: 订单流采购单模块
 * @date 2021/10/8 15:29
 */
@Slf4j
@Controller
@RequestMapping("/order/newBuyorder")
public class NewBuyorderController {

    /********************** todo：copy old模块下 BaseController 中代码， 后续进行重构  开始 *********************/
    @Autowired
    private SettlementBillApiService settlementBillApiService;

    @Autowired
    private TraderSupplierApiService traderSupplierApiService;

    @Autowired
    private SupplierAssetApiService supplierAssetApiService;

    @Autowired
    BuyOrderAfterSalesDetailFacade buyOrderAfterSalesDetailFacade;
    
    @Autowired
    private BuyorderApiService buyorderApiService;

    @Autowired
    private CommunicateVoiceTaskApi communicateVoiceTaskApi;


    /**
     * 直发采购快递确认收货按钮显隐，默认为 false
     */
    @Value("${deliveryDirectConfirmArrival:false}")
    private Boolean deliveryDirectConfirmArrival;
    
    @Autowired
    private ExpressDetailMapper expressDetailMapper;

    /**
     * 判断订单流新旧订单 - 采购修改单
     */
    protected Integer isNewBuyOrderModifyApply(Integer buyOrderModifyApplyId) throws InterruptedException {

        if(ObjectUtils.isEmpty(buyOrderModifyApplyId)){
            throw new IllegalArgumentException("RequestParam中修改单ID不存在");
        }
        return buyOrderModifyApplyService.judgeIsNewBuyOrderModifyApply(buyOrderModifyApplyId);
    }

    /**
     * 根据参数生成实体page对象（无记录总数）
     */
    protected Page getPageTag(HttpServletRequest request, Integer pageNo, Integer pageSize) {
        String path = request.getRequestURL().toString();
        try {
            String str = "";
            Map<?, ?> map = request.getParameterMap();
            if (map != null && (!map.isEmpty())) {
                path += "?";
                for (Object key : map.keySet()) {
                    if (!("pageNo".equals(key.toString()) || "pageSize".equals(key.toString()))) {
                        str = java.net.URLDecoder.decode(request.getParameter(key.toString()), "UTF-8");
                        str = java.net.URLEncoder.encode(str, "UTF-8");
                        path = path + key + "=" + str + "&";
                    }
                }
                path = path.substring(0, path.length() - 1);
            }
        } catch (Exception e) {
            logger.error("分页初始化失败", e);
            return Page.newBuilder(pageNo, pageSize, path);
        }
        return Page.newBuilder(pageNo, pageSize, path);
    }

    /**
     *获取session中用户信息
     *
     */
    public User getSessionUser(HttpServletRequest request) {
        return getSessionUser(request.getSession());
    }


    /**
     * 获取登录用户对象，此时创建一个新的对象防止逻辑中对该对象进行修改
     */
    protected User getSessionUser(HttpSession session){
        try{
            User user = (User) session.getAttribute(Consts.SESSION_USER);
            User returnUser = new User();
            BeanUtils.copyProperties(returnUser,user);
            return returnUser;
        }catch (Exception e){
            logger.info("获取session异常",e);
            return null;
        }

    }

    /**
     * 文件类型 OSS链接转化为下载
     */
    protected List<Attachment> setOSSDownloadUri(List<Attachment> attachmentList){
        for (Attachment attachment : attachmentList) {
            if(attachment.getAttachmentType().equals(SysOptionConstant.ID_461)){
                String attachmenturi = attachment.getUri();
                String[] split = attachmenturi.split(SysOptionConstant.OSS_DISPLAY);
                if(split != null && split.length >1){
                    String newUri = split[0] + SysOptionConstant.OSS_DOWNLOAD + split[1];
                    attachment.setUri(newUri);
                }
            }
        }
        return attachmentList;
    }

    protected ModelAndView pageNotFound(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView();
        String userName="";
        User user;
        try{
            user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
            userName=user.getUsername();
        }catch (Exception e2){

        }
        logger.error("global error handler pageNotFound "+"\t"+request.getRequestURI()+userName+ IpUtil.getIp()+
                request.getQueryString() );
        mv.addObject("userName",userName);
        mv.addObject("url",request.getRequestURI());
        mv.addObject("queryString",request.getQueryString());
        mv.addObject("ip",IpUtil.getIp());
        mv.addObject("time",System.currentTimeMillis());
        mv.addObject("method",request.getMethod());
        mv.addObject("type","找不到指定页面");
        mv.setViewName("common/500");
        return mv;
    }

    /**
     * 获取字典表中的集合
     */
    protected List<SysOptionDefinition> getSysOptionDefinitionList(Integer parentId) {
        List<SysOptionDefinition> resultList = null;
        if (JedisUtils.exists(dbType + ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + parentId)) {
            String jsonStr = JedisUtils.get(dbType + ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + parentId);
            // 避免json为空或null的字符串或[null]的字符串
            if (StringUtils.isNotBlank(jsonStr) && !"null".equalsIgnoreCase(jsonStr)
                    && !"[null]".equalsIgnoreCase(jsonStr)) {
                JSONArray json = JSONArray.fromObject(jsonStr);
                resultList = (List<SysOptionDefinition>) JSONArray.toCollection(json, SysOptionDefinition.class);
            }
        }
        // 从redis中获取为null，则从库中查询
        if (CollectionUtils.isEmpty(resultList)) {
            // 调用根据parendId获取数字字典子list
            resultList = baseService.getSysOptionDefinitionListByParentId(parentId);
        }

        //去重
        if (CollectionUtils.isNotEmpty(resultList)){
            resultList.stream().filter(Objects::nonNull).map(SysOptionDefinition::getSysOptionDefinitionId).distinct().collect(Collectors.toList());
        }
        return resultList;
    }


    /**
     * 设置工作流审核人全称
     */
    protected List<HistoricActivityInstance> setAssignRealNames(ModelAndView modelAndView, Map<String, Object> historicInfo) {
        Object assigneeVosObj = modelAndView.getModel().get("assigneeVos");
        ArrayList<AssigneeVo> assigneeVos = assigneeVosObj == null ? new ArrayList<>() : (ArrayList<AssigneeVo>) assigneeVosObj;

        List<HistoricActivityInstance> historicActivityInstance = (List<HistoricActivityInstance>)historicInfo
                .get("historicActivityInstance");
        if (CollectionUtils.isNotEmpty(historicActivityInstance)){
            for (HistoricActivityInstance historicActivityInstanceInfo : historicActivityInstance) {
                if (StringUtil.isBlank(historicActivityInstanceInfo.getAssignee())){
                    continue;
                }
                AssigneeVo assigneeVo = new AssigneeVo();
                assigneeVo.setAssignee(historicActivityInstanceInfo.getAssignee());
                assigneeVo.setRealName(getRealNameByUserName(historicActivityInstanceInfo.getAssignee()));
                assigneeVos.add(assigneeVo);
            }
        }
        ArrayList<AssigneeVo> assigneeVosResult = assigneeVos.stream()
                .filter(assigneeVo -> StringUtil.isNotBlank(assigneeVo.getAssignee()))
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(
                                Comparator.comparing(AssigneeVo::getAssignee))), ArrayList::new));
        modelAndView.addObject("assigneeVos", assigneeVosResult);
        return historicActivityInstance;
    }

    /**
     * 获取多个审核人员真实姓名
     */
    protected String getVerifyUserRealNames(String verifyUsers){
        if (verifyUsers == null){
            return null;
        }
        List<String> userNames = Arrays.stream(verifyUsers.split(","))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userNames)){
            return verifyUsers;
        }
        StringBuffer realNames = new StringBuffer();
        userNames.forEach(userName -> {
            realNames.append(getRealNameByUserName(userName)).append(",");
        });
        realNames.deleteCharAt(realNames.length() - 1);
        return  realNames.toString();
    }

    /**
     * 操作失败
     */
    protected ModelAndView fail(ModelAndView mv) {
        mv.setViewName("common/fail");
        return mv;
    }

    /**
     * 操作成功
     */
    protected ModelAndView success(ModelAndView mv) {
        mv.setViewName("common/success");
        return mv;
    }

    /**
     * 根据用户名获取真实姓名 xxx（xx）
     */
    protected String getRealNameByUserName(String userName){
        return userService.getRealNameByUserName(userName);
    }


    /**
     * 将修改前的数据保存进redis返回key
     */
    protected String saveBeforeParamToRedis(String beforeParams) {
        if (beforeParams == null) {
            beforeParams = "";
        }
        String redisKey = dbType + "beforeParams:" + UUID.randomUUID().toString();
        JedisUtils.set(redisKey, beforeParams, ErpConst.REDIS_USERID_SESSIONID_TIMEOUT);
        return redisKey;
    }

    /**
     * 获取字典表中对象
     */
    protected SysOptionDefinition getSysOptionDefinition(Integer sysOptionDefinitionId) {
        if (JedisUtils.exists(dbType + ErpConst.KEY_PREFIX_DATA_DICTIONARY_OBJECT + sysOptionDefinitionId)) {
            String jsonStr = JedisUtils
                    .get(dbType + ErpConst.KEY_PREFIX_DATA_DICTIONARY_OBJECT + sysOptionDefinitionId);
            net.sf.json.JSONObject json = net.sf.json.JSONObject.fromObject(jsonStr);
            SysOptionDefinition sys = (SysOptionDefinition) net.sf.json.JSONObject.toBean(json, SysOptionDefinition.class);
            return sys;
        } else {
            SysOptionDefinition sys = baseService.getSysOptionDefinitionById(sysOptionDefinitionId);
            return sys;
        }

    }

    /**
     * 根据companyId获取物流公司集合
     */
    protected List<Logistics> getLogisticsList(Integer companyId) {
        List<Logistics> logisticsList = null;
        if (JedisUtils.exists(dbType + ErpConst.KEY_PREFIX_LOGISTICS_LIST + companyId)) {
            JSONArray jsonaArray = JSONArray
                    .fromObject(JedisUtils.get(dbType + ErpConst.KEY_PREFIX_LOGISTICS_LIST + companyId));
            logisticsList = (List<Logistics>) JSONArray.toCollection(jsonaArray, Logistics.class);
        } else {
            logisticsList = logisticsService.getLogisticsList(companyId);
        }
        return logisticsList;
    }

    @Value("${oss_url}")
    protected String domain;
    @Value("${redis_dbtype}")
    protected String dbType;
    @Value("${ez_BuyorderInstock_Url}")
    protected String ezBuyorderInstockUrl;
    @Autowired
    @Qualifier("logisticsService")
    private LogisticsService logisticsService;
    @Resource
    private BaseService baseService;

    @Autowired
    private TraderSupplierService traderSupplierService;

    /********************** todo：copy old模块下 BaseController 中代码， 后续进行重构  结束 *********************/


    public static final Logger logger = LoggerFactory.getLogger(NewBuyorderController.class);


    @Autowired
    private BuyorderService buyorderService;

    @Autowired
    private VgoodsService vGoodsService;

    @Autowired
    private RiskCheckService riskCheckService;

    @Autowired
    private TraderCustomerService traderCustomerService;

    @Autowired
    private AfterSalesService afterSalesOrderService;

    @Autowired
    private PayApplyService payApplyService;

    @Autowired
    private ActionProcdefService actionProcdefService;

    @Autowired
    private UserService userService;

    @Autowired
    private ExpressService expressService;

    @Autowired
    private WarehouseInService warehouseInService;

    @Autowired
    private AfterSaleServiceStandardService afterSaleServiceStandardService;

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    @Autowired
    private BasePriceService basePriceService;

    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private TagService tagService;

    @Autowired
    private VerifiesRecordService verifiesRecordService;

    @Autowired
    private NewBuyOrderService newBuyOrderService;

    @Autowired
    private AfterSalesService afterSalesService;

    @Resource
    private OrderCommonService orderCommonService;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Autowired
    private SaleorderService saleorderService;
    @Resource
    private BuyOrderModifyApplyService buyOrderModifyApplyService;

    @Autowired
    private NewBuyOrderModifyApplyService newBuyOrderModifyApplyService;

    @Value("${vedeng_address_phone}")
    protected String vedeng_address_phone;

    @Autowired
    private BaseGoodsService baseGoodsService;

    @Resource
    private UserDetailMapper userDetailMapper;

    @Autowired
    private AddressService addressService;


    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Resource
    private BuyorderMapper buyorderMapper;

    @Autowired
    private WMSBASRProcessor wmsbasrProcessor;

    @Autowired
    private WMSBASCProcessor wmsbascProcessor;

    @Autowired
    private OverBuyordeReturnAfterSalesCore overBuyordeReturnAfterSalesCore;

    @Autowired
    private OverBuyordeChangeAfterSalesCore overBuyordeChangeAfterSalesCore;

    @Autowired
    private DealTicketTaskForBuyReturnProcessor dealTicketTaskForBuyReturnProcessor;

    @Autowired
    private DealBuyorderForBuyReturnAfterProcessor dealBuyorderForBuyReturnAfterProcessor;

    @Autowired
    private DealSaleorderForBuyReturnAfterProcessor dealSaleorderForBuyReturnAfterProcessor;

    @Autowired
    private DealBuyorderForBuyChangeAfterProcessor dealBuyorderForBuyChangeAfterProcessor;

    @Autowired
    private DealSaleorderForBuyChangeAfterProcessor dealSaleorderForBuyChangeAfterProcessor;

    @Resource
    private BuyorderElectronicSignatureService buyorderElectronicSignatureService;

    @Autowired
    private CancelTypeService cancelTypeService;

    @Autowired
    private PurchaseDeliveryBatchInfoService purchaseDeliveryBatchInfoService;

    @Autowired
    private AttachmentService attachmentService;

    @Autowired
    private BuyorderExpenseService buyorderExpenseService;

    @Autowired
    private UserApiService userApiService;

    @Autowired
    private BuyorderExpenseApiService buyorderExpenseApiService;

    @Autowired
    private WarehouseGoodsOutService warehouseGoodsOutService;

    @Autowired
    private NewAfterBuyorderService newAfterBuyorderService;

    @Autowired
    private WarehouseGoodsInService warehouseGoodsInService;

    @Autowired
    private BuyorderAfterSalesApiService buyorderAfterSalesApiService;

    @Resource
    private RBuyorderExpenseJSaleorderService rBuyorderExpenseJSaleorderService;

    @Autowired
    private SaleOrderGoodsDetailConvertor saleOrderGoodsDetailConvertor;

    @Autowired
    private SaleOrderGoodsApiService saleOrderGoodsService;

    @Autowired
    private RegistrationNumberMapper registrationNumberMapper;

    @Autowired
    private ProductCompanyMapper productCompanyMapper;

    @Qualifier("verifiesInfoMapper")
    @Autowired
    private VerifiesInfoMapper verifiesInfoMapper;

    /**
     * 采购单详情页面
     */
    @ResponseBody
    @RequestMapping(value = "newViewBuyOrderDetail")
    @NoNeedAccessAuthorization
    public ModelAndView viewBuyOrderDetail(HttpServletRequest request, Buyorder buyorder, String uri) {
        User user = getSessionUser(request);
        ModelAndView mav = new ModelAndView();
        BuyorderVo bv = buyorderService.getBuyorderVoDetailNew(buyorder, user);

        // 查询关联的采购费用单商品集合
        List<BuyorderExpenseItemDto> buyOrderExpenseGoodsList = buyorderExpenseService.getByBuyOrderId(buyorder.getBuyorderId());
        // 查询采购虚拟商品的关联信息
        if (buyOrderExpenseGoodsList.size()>0){
            List<RBuyorderExpenseJSaleorderDto> buyOrderExpenseJSaleList = rBuyorderExpenseJSaleorderService.getRelatedDetail(buyOrderExpenseGoodsList);
            // 获取关联销售订单saleordergoodsIds集合
            List<Integer> detailIds = buyOrderExpenseJSaleList.stream().map(RBuyorderExpenseJSaleorderDto::getSaleorderGoodsId).collect(Collectors.toList());
            // 获取关联销售订单信息
            List<SaleOrderGoodsDetailDto> saleOrderGoodsDetailDtos = saleOrderGoodsService.getRelatedDetail(detailIds);

            List<BuyOrderSaleOrderGoodsDetailDto> esgList = saleOrderGoodsDetailDtos.stream().map(so -> saleOrderGoodsDetailConvertor.toDto(so)).collect(Collectors.toList());


            buyorderExpenseApiService.getExpenseBuyNum(esgList,buyOrderExpenseJSaleList,"detail");

            //添加虚拟商品关联订单信息
            int ebuyorderSum = 0;
            BigDecimal ebuyorderAmount = BigDecimal.valueOf(0.00);
            for (BuyorderExpenseItemDto buyorderExpenseItemDto : buyOrderExpenseGoodsList) {
                List<BuyOrderSaleOrderGoodsDetailDto> list = new ArrayList<>();
                for (BuyOrderSaleOrderGoodsDetailDto saleOrderGoodsDetailDto : esgList) {
                    if (buyorderExpenseItemDto.getGoodsId().equals(saleOrderGoodsDetailDto.getGoodsId())){
                        list.add(saleOrderGoodsDetailDto);
                    }
                }
                ebuyorderSum += buyorderExpenseItemDto.getNum();
                BigDecimal price = buyorderExpenseItemDto.getBuyorderExpenseItemDetailDto().getPrice();
                ebuyorderAmount = ebuyorderAmount.add(price.multiply(new BigDecimal(buyorderExpenseItemDto.getNum())));
                buyorderExpenseItemDto.setBuyOrderSaleOrderGoodsDetailDtos(list);
            }
            boolean b = buyOrderExpenseGoodsList.stream().anyMatch(o -> o.getBuyOrderSaleOrderGoodsDetailDtos().size() == 0);
            mav.addObject("addVSkuFlag",b);
            bv.setEbuyorderAmount(ebuyorderAmount.setScale(2, RoundingMode.HALF_UP));
            bv.setEbuyorderSum(ebuyorderSum);
        }
        bv.setBuyorderExpenseItemDtos(buyOrderExpenseGoodsList);

        mav.addObject("uri", uri);

        // 订单流，新老订单分别跳转到对应的详情页
        if (ErpConst.ZERO.equals(bv.getIsNew()) && ErpConst.ONE.equals(bv.getValidStatus())) {
            mav.setViewName("redirect:/order/buyorder/viewBuyordersh.do?buyorderId=" + buyorder.getBuyorderId() );
            return mav;
        }
        if (ErpConst.ZERO.equals(bv.getIsNew()) && ErpConst.ZERO.equals(bv.getValidStatus())) {
            mav.setViewName("redirect:/order/buyorder/viewBuyorder.do?buyorderId=" + buyorder.getBuyorderId() );
            return mav;
        }

        // 已确认，此处依然实时读取新商品流的信息，生成的快照信息不在此处使用
        skuTip(mav, bv);
        //处理贝登售后标准
        dealWithBdAfterSaleStandard(bv);
        //init page
        initPageParams(mav, bv, uri, user, request);
        // 售后订单列表
        afterSaleList(mav, bv);
        // 获取付款申请列表
        Integer firstPayApplyId = payApplyList(mav, bv);
        //付款审核进度 StatusNodeList
        paymentCheckStatus(mav, firstPayApplyId, bv);
        // 订单审核进度 StatusNodeList
        buyorderCheckStatus(mav, bv);
        // 采购订单修改申请列表（不分页）
        modifyApplyList(mav, bv);
        //是否显示上传入库单按钮 0 否 1是
        showWarehouseUpload(user, mav, bv);
        // 入库附件
        warehouseAttr(mav, bv);
        //随货同行单模块
        queryDeliveryDirectBatch(mav,bv);
        //采购合同审核流
        purchaseContract(mav, bv);
        return mav;
    }

    private void purchaseContract(ModelAndView mav, BuyorderVo bv) {
        try {
            Map<Long, List<BuyOrderContractDto>> map = new HashMap<>();
            if (CollUtil.isNotEmpty(bv.getAttachmentList())){
                map = BeanUtil.copyToList(bv.getAttachmentList(),BuyOrderContractDto.class).stream()
                        .sorted(Comparator.comparingLong(BuyOrderContractDto::getAddTime))
                        .collect(Collectors.groupingBy(BuyOrderContractDto::getAddTime,LinkedHashMap :: new,Collectors.toList()));
                map.forEach((n,m) -> m.forEach(a -> {
                    a.setUsername(userApiService.getUserById(a.getCreator()).getUsername());
                    // 质量专员
                    List<String> nameList = userApiService.getUserListByPositionId(44);
                    nameList.add("Robin.xu");
                    a.setVerifyName(StrUtil.join("/",nameList));

                    Integer status = verifiesInfoMapper.getPurchaseContractVerify(bv.getBuyorderId(),n);
                    if (null == status){
                        a.setVerifyStatusStr("-");
                    }else {
                        a.setVerifyStatusStr(ErpConst.ZERO.equals(status) ? "审核中" : ErpConst.ONE.equals(status) ? "审核通过" : "审核不通过");
                    }
                    a.setVerifyStatus(status);
                }));
            }
            mav.addObject("voMap",map);
            //采购合同能否上传
            mav.addObject("canUploadPurchaseContract",this.canUploadPurchaseContract(bv.getBuyorderId()));
        }catch (Exception e){
            log.info("获取采购订单合同审核信息异常",e);
        }
    }

    private Boolean canUploadPurchaseContract(Integer buyorderId) {
        Boolean canUpload = Boolean.FALSE;
        //通过的采购合同审核流当前无审核中的流程-可以上传
        VerifiesInfo verifiesInfo = new VerifiesInfo();
        verifiesInfo.setRelateTable(ErpConst.PURCHASE_CONTRACT_VERIFY);
        verifiesInfo.setRelateTableKey(buyorderId);
        List<VerifiesInfo> list = verifiesInfoMapper.getVerifiesInfo(verifiesInfo);
        Optional<VerifiesInfo> verifyOpt = list.stream().filter(l -> ErpConst.ZERO.equals(l.getStatus())).findAny();
        if (!verifyOpt.isPresent()){
            canUpload = Boolean.TRUE;
        }
        return canUpload;
    }

    private void showWarehouseUpload(User user, ModelAndView mav, BuyorderVo bv) {
        Integer isShowWarehouseUpload = 0;
        //如果是普发而且用户是供应链人员则显示上传入库单按钮
        Boolean supplyChainFlagByUserId = userService.getSupplyChainFlagByUserId(user.getUserId());
        if (ErpConst.ZERO.equals(bv.getDeliveryDirect()) && supplyChainFlagByUserId) {
            isShowWarehouseUpload = 1;
        }
        mav.addObject("isShowWarehouseUpload", isShowWarehouseUpload);
    }

    /**
     * 查询采购单关联的同行单信息
     */
    private void queryDeliveryDirectBatch(ModelAndView mav,BuyorderVo bv){
        //直发采购单校验是否展示新增同行单按钮
        if(ErpConst.ONE.equals(bv.getDeliveryDirect())){
            List<Integer> buyorderGoodsIds = bv.getBuyorderGoodsVoList().stream().map(BuyorderGoodsVo::getBuyorderGoodsId).collect(Collectors.toList());
            mav.addObject("isShowDtBtn", purchaseDeliveryBatchInfoService.verifyIsShowDeliveryDirectBtn(buyorderGoodsIds));
        }
        //查询同行单信息
        List<PurchaseDeliveryBatchInfoDto> purchaseDeliveryDirectBatchInfoVoList = purchaseDeliveryBatchInfoService.queryPurchaseDeliveryInfoByBuyorderId(bv.getBuyorderId());
        for(PurchaseDeliveryBatchInfoDto purchaseDeliveryBatchInfoDto : purchaseDeliveryDirectBatchInfoVoList){
            //遍历同行单展示
            Attachment attachment = new Attachment();
            attachment.setAttachmentType(SysOptionConstant.ID_460);
            attachment.setAttachmentFunction(SysOptionConstant.ID_4080);
            attachment.setRelatedId(purchaseDeliveryBatchInfoDto.getPurchaseDeliveryDirectBatchInfoId());
            List<Attachment> attachmentList = attachmentService.queryAttachmentList(attachment);
            if(CollectionUtils.isEmpty(attachmentList)){
                purchaseDeliveryBatchInfoDto.setIsUploadFile(ErpConst.ZERO);
            }else {
                purchaseDeliveryBatchInfoDto.setIsUploadFile(ErpConst.ONE);
            }
        }
        mav.addObject("purchaseDeliveryDirectBatchInfoVoList",purchaseDeliveryDirectBatchInfoVoList);
    }

    // 入库附件
    private void warehouseAttr(ModelAndView mav, BuyorderVo bv) {
        if (bv == null) {
            return;
        }
        Attachment att = new Attachment();
        att.setRelatedId(bv.getBuyorderId());
        att.setAttachmentFunction(SysOptionConstant.ID_837);
        // att.setAttachmentType(460);
        List<Attachment> AttachmentList = warehouseInService.getAttachmentList(att);
        att.setAttachmentFunction(SysOptionConstant.ID_4330);
        List<Attachment> AttachmentList1 = warehouseInService.getAttachmentList(att);
        att.setAttachmentFunction(SysOptionConstant.ID_4331);
        List<Attachment> AttachmentList2 = warehouseInService.getAttachmentList(att);
        AttachmentList.addAll(AttachmentList1);
        AttachmentList.addAll(AttachmentList2);
        att.setAttachmentFunction(SysOptionConstant.BUYGOODS_DOC);
        List<Attachment> goodsDocAttachmentList = warehouseInService.getAttachmentList(att);

        mav.addObject("goodsDocAttachmentList", setOSSDownloadUri(goodsDocAttachmentList));
        mav.addObject("AttachmentList", AttachmentList);
    }

    private void initPageParams(ModelAndView mav, BuyorderVo bv, String uri, User user, HttpServletRequest request) {
        if (bv == null) {
            return;
        }
        mav.setViewName("orderstream/buyorder/buyorder_detail");
        mav.addObject("ezBuyorderInstockUrl", ezBuyorderInstockUrl);
        mav.addObject("curr_user", user);
        mav.addObject("deliveryDirectConfirmArrival", deliveryDirectConfirmArrival);

        riskCheckService.setBuyorderGoodsIsRiskInfo(bv, bv.getBuyorderGoodsVoList());

        Boolean prBoolean = riskCheckService.permoissionsFlag(user, ErpConst.QUALITY_ORG);
        mav.addObject("prBoolean",prBoolean);

        Integer riskFlag = riskCheckService.getRiskFlag(user, bv.getIsRisk());
        mav.addObject("riskFlag", riskFlag);

        // 计算采购单的主、子状态，并初始化 StatusNodeList
        String status = NewBuyOrderUtils.calcStatusAndSubStatusOfBuyOrder(bv);

        List<StatusNode> statusNodeList = NewBuyOrderUtils.lightStatusNode(bv, status);
        NewBuyOrderUtils.fillNodeInfo(bv,statusNodeList);

        mav.addObject("statusNodeList", JSON.toJSONString(statusNodeList));
        //设置合同下载地址
        String contractUrl = "";
        if(bv.getContractUrl() != null && !"".equals(bv.getContractUrl())){
            //有合同章
            contractUrl = bv.getContractUrl().replace("display","download");
            mav.addObject("contractUrl",contractUrl);
        }
        // 封装返利信息
        handleRebateInfo(bv, false, true);
        OrderPaymentDetailsDto orderPaymentDetailsDto = buyorderApiService.queryOrderPaymentDetails(bv.getBuyorderId(), ErpConstant.ZERO, ErpConstant.BANK_ACCEPTANCE);
        bv.setBankAcceptance(Objects.nonNull(orderPaymentDetailsDto) ? ErpConstant.ONE : ErpConstant.ZERO);

        // 判断所有商品是否都有快递信息
        // 查询采购商品总数 求和
        long orderNum = bv.getBuyorderGoodsVoList().stream().mapToLong(BuyorderGoodsVo::getNum).sum();
        if (Objects.isNull(orderNum)){
            orderNum = 0L;
        }
        // 快递已发货总数
        Long expressNum = expressDetailMapper.getBuyorderExpressInfo(bv.getBuyorderId());
        if (expressNum == null){
            expressNum = 0L;   
        }
        bv.setAllGoodsHaveExpressInfoFlag((orderNum >0  && expressNum >= orderNum) ? ErpConstant.ONE : ErpConstant.ZERO);
        mav.addObject("buyorderVo", bv);


        // 发票类型
        List<SysOptionDefinition> invoiceTypes = getSysOptionDefinitionList(SysOptionConstant.ID_428);
        mav.addObject("invoiceTypes", invoiceTypes);
        // 沟通记录
        CommunicateRecord communicateRecord = new CommunicateRecord();
        communicateRecord.setBuyorderId(bv.getBuyorderId());
        List<CommunicateRecord> crList = traderCustomerService.getCommunicateRecordList(communicateRecord);
        mav.addObject("communicateList", crList);

        // 付款计划
        List<SysOptionDefinition> paymentTermList = getSysOptionDefinitionList(SysOptionConstant.ID_418);
        mav.addObject("paymentTermList", paymentTermList);
        // 交易方式
        List<SysOptionDefinition> traderModeList = getSysOptionDefinitionList(519);
        SysOptionDefinition sysOptionDefinition = new SysOptionDefinition();
        sysOptionDefinition.setSysOptionDefinitionId(ErpConstant.BANK_ACCEPTANCE);
        sysOptionDefinition.setTitle("银行承兑汇票");
        traderModeList.add(sysOptionDefinition);
        mav.addObject("traderModeList", traderModeList);

        // 采购费用订单
        List<BuyorderExpenseDto> expenseDtoList = buyorderExpenseService.getBuyOrderExpenseDtoListByBuyOrderId(bv.getBuyorderId());
        mav.addObject("buyOrderExpenseDtoList", expenseDtoList);
    }

    // 售后订单列表
    private void afterSaleList(ModelAndView mav, BuyorderVo bv) {
        if (bv == null) {
            return;
        }
        AfterSalesVo as = new AfterSalesVo();
        as.setOrderId(bv.getBuyorderId());
        as.setSubjectType(536);
        List<AfterSalesVo> asList = afterSalesOrderService.getAfterSalesVoListByOrderId(as);
        if (asList != null && asList.size() > 0) {
            if (asList.get(0).getAtferSalesStatus() == 2 || asList.get(0).getAtferSalesStatus() == 3) {
                logger.info("新增售后按钮：afterSalesStatus:{}",asList.get(0).getAtferSalesStatus());
                mav.addObject("addAfterSales", 1);
            } else {
                mav.addObject("addAfterSales", 0);
                mav.addObject("lockedReason", "售后锁定");
            }
        } else {
            mav.addObject("addAfterSales", 1);
        }
        mav.addObject("asList", asList);
    }

    // 已确认，此处依然实时读取新商品流的信息，生成的快照信息不在此处使用
    private void skuTip(ModelAndView mav, BuyorderVo bv) {
        if (bv != null && !CollectionUtils.isEmpty(bv.getBuyorderGoodsVoList())) {
            List<Integer> skuIds = new ArrayList<>();
            bv.getBuyorderGoodsVoList().forEach(buyOrderGood -> {
                skuIds.add(buyOrderGood.getGoodsId());
            });
            List<Map<String, Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);
            try {
                Map<String, Map<String, Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key -> key.get("SKU_NO").toString(), v -> v, (k, v) -> v));
                mav.addObject("newSkuInfosMap", newSkuInfosMap);
            } catch (Exception e) {
                logger.error("skuIds:" + skuIds, e);
            }
        }
    }

    // 采购订单修改申请列表（不分页）
    private void modifyApplyList(ModelAndView mav, BuyorderVo bv) {
        BuyorderModifyApply buyorderModifyApply = new BuyorderModifyApply();
        buyorderModifyApply.setBuyorderId(bv.getBuyorderId());
        List<BuyorderModifyApply> buyorderModifyApplyList = buyorderService
                .getBuyorderModifyApplyList(buyorderModifyApply);
        mav.addObject("buyorderModifyApplyList", buyorderModifyApplyList);
        // 判断是否有正在审核中的付款申请
        for (BuyorderModifyApply buyorderModifyApply1 : buyorderModifyApplyList) {
            if (buyorderModifyApply1.getVerifyStatus() == 0) {
                mav.addObject("lockedReason", "采购单修改锁定");
            }
            break;
        }
    }

    //订单审核进度 StatusNodeList
    private void buyorderCheckStatus(ModelAndView mav, BuyorderVo bv) {
        if (bv == null) {
            return;
        }
        String processKey = getProcessKey(bv);
        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
                processKey + "_" + bv.getBuyorderId());
        mav.addObject("taskInfo", historicInfo.get("taskInfo"));
        mav.addObject("startUser", historicInfo.get("startUser") != null ?
                getRealNameByUserName(historicInfo.get("startUser").toString()) : null);
        mav.addObject("candidateUserMap", historicInfo.get("candidateUserMap"));
        // 最后审核状态
        mav.addObject("endStatus", historicInfo.get("endStatus"));

        List<HistoricActivityInstance> historic = setAssignRealNames(mav, historicInfo);

        boolean permoissionsFlag = false;
        if (historicInfo.get("startUser") != null){
            User startUser = userService.getByUsername(historicInfo.get("startUser").toString(), 1);
            permoissionsFlag = startUser != null ?  riskCheckService.isOrgFlag(startUser, ErpConst.QUALITY_ORG) : false;
        }
        mav.addObject("permoissionsFlag", permoissionsFlag);
        mav.addObject("historicActivityInstance", historic);
        //mav.addObject("historicActivityInstance", historicInfo.get("historicActivityInstance"));
        mav.addObject("commentMap", historicInfo.get("commentMap"));
        Task taskInfoPay = (Task) historicInfo.get("taskInfo");
        String verifyUsersPay = null;
        if (null != taskInfoPay) {

            Map candidateUserMap= (Map) historicInfo.get("candidateUserMap");

            //获取审核人候选组
            List<IdentityLink> candidateUserList = (List<IdentityLink>) candidateUserMap.get(taskInfoPay.getId());

            if(CollectionUtils.isNotEmpty(candidateUserList)){

                List<String> userNameList = candidateUserList.stream().map(IdentityLink::getUserId).collect(Collectors.toList());

                verifyUsersPay = StringUtils.join(userNameList,",");

            }

        }
        mav.addObject("verifyUsers", getVerifyUserRealNames(verifyUsersPay));



    }

    // 获取付款申请列表
    private Integer payApplyList(ModelAndView mav, BuyorderVo bv) {
        if (bv == null) {
            return null;
        }
        PayApply payApply = new PayApply();
        payApply.setCompanyId(ErpConst.NJ_COMPANY_ID);
        payApply.setPayType(517);// 采购付款申请
        payApply.setRelatedId(bv.getBuyorderId());
        List<PayApply> payApplyList = payApplyService.getPayApplyList(payApply);
        mav.addObject("payApplyList", payApplyList);
        // 判断是否有正在审核中的付款申请
        int isPayApplySh = 0;
        Integer payApplyId = 0;
        for (PayApply payApply1 : payApplyList) {
            if (payApply1.getValidStatus() == 0 || payApply1.getValidStatus() == 2) {
                if (payApply1.getValidStatus() == 0) {
                    isPayApplySh = 1;
                    mav.addObject("lockedReason", "付款申请锁定");
                }
                break;
            }
        }
        if (!payApplyList.isEmpty()) {
            payApplyId = payApplyList.get(0).getPayApplyId();
        }
        mav.addObject("isPayApplySh", isPayApplySh);
        mav.addObject("payApplyId", payApplyId);
        return payApplyId;
    }

    //付款审核进度 StatusNodeList
    private void paymentCheckStatus(ModelAndView mav, Integer payApplyId, BuyorderVo bv) {
        if (payApplyId == null) {
            return;
        }
        Map<String, Object> historicInfoPay = actionProcdefService.getHistoric(processEngine,
                "paymentVerify_" + payApplyId);
        Task taskInfoPay = (Task) historicInfoPay.get("taskInfo");
        mav.addObject("taskInfoPay", taskInfoPay);
        mav.addObject("startUserPay", historicInfoPay.get("startUser"));
        // 最后审核状态
        mav.addObject("endStatusPay", historicInfoPay.get("endStatus"));
        mav.addObject("historicActivityInstancePay", historicInfoPay.get("historicActivityInstance"));
        mav.addObject("commentMapPay", historicInfoPay.get("commentMap"));
        Map<String,Object> candidateUserMapPay = (Map)historicInfoPay.get("candidateUserMap");
        mav.addObject("candidateUserMapPay", candidateUserMapPay);
        // 当前审核人
        String verifyUsersPay = null;
        if (null != taskInfoPay) {
            Map<String, Object> taskInfoVariablesPay = actionProcdefService.getVariablesMap(taskInfoPay);
            verifyUsersPay = (String) taskInfoVariablesPay.get("verifyUsers");
        }
        // 当前付款审核人
        mav.addObject("verifyUsersPay", verifyUsersPay);

        // 将在前端中判断的逻辑移到后台代码中。这么多判断条件应该可以优化，很多不是必须的，留给后人统一优化
        boolean buyOrderPayApplyAuditFlag = false;
        boolean expensePayApplyAuditFlag = false;
        if (((taskInfoPay != null && taskInfoPay.getProcessInstanceId() != null && taskInfoPay.getAssignee() != null) || candidateUserMapPay.get(taskInfoPay != null ? taskInfoPay.getId() : null) != null) && "供应链产品总监".equals(historicInfoPay.get("endStatus"))) {
            buyOrderPayApplyAuditFlag = true;
        }

        // 查询当前采购单的直属费用单的付款申请信息
        BuyorderExpenseDto directBuyOrderExpenseDto = buyorderExpenseApiService.queryAttributiveExpenseInfoByBuyorderId(bv.getBuyorderId());
        if (!Objects.isNull(directBuyOrderExpenseDto)) {
            PayApply payApply = new PayApply();
            payApply.setCompanyId(ErpConst.NJ_COMPANY_ID);
            // 采购费用单付款申请
            payApply.setPayType(4125);
            payApply.setRelatedId(directBuyOrderExpenseDto.getBuyorderExpenseId());
            List<PayApply> payApplyList = payApplyService.getPayApplyList(payApply);
            if (CollectionUtils.isNotEmpty(payApplyList)) {
                mav.addObject("expenseId", directBuyOrderExpenseDto.getBuyorderExpenseId());

                Map<String, Object> expenseHistoricInfoPay = actionProcdefService.getHistoric(processEngine, "paymentVerify_" + payApplyList.get(0).getPayApplyId());
                Task expenseTaskInfoPay = (Task) expenseHistoricInfoPay.get("taskInfo");
                mav.addObject("expenseTaskInfoPay", expenseTaskInfoPay);
                mav.addObject("expenseStartUserPay", expenseHistoricInfoPay.get("startUser"));
                // 最后审核状态
                mav.addObject("expenseEndStatusPay", expenseHistoricInfoPay.get("endStatus"));
                Map<String,Object> expenseCandidateUserMapPay = (Map)expenseHistoricInfoPay.get("candidateUserMap");
                mav.addObject("expenseCandidateUserMapPay", expenseCandidateUserMapPay);

                if (((expenseTaskInfoPay != null && expenseTaskInfoPay.getProcessInstanceId() != null && expenseTaskInfoPay.getAssignee() != null) || expenseCandidateUserMapPay.get(expenseTaskInfoPay != null ? expenseTaskInfoPay.getId() : null) != null) && "供应链产品总监".equals(expenseHistoricInfoPay.get("endStatus"))) {
                    expensePayApplyAuditFlag = true;
                }
            }
        }
        mav.addObject("buyOrderPayApplyAuditFlag", buyOrderPayApplyAuditFlag);
        mav.addObject("expensePayApplyAuditFlag", expensePayApplyAuditFlag);
    }

    private void dealWithBdAfterSaleStandard(BuyorderVo buyorderVo) {

        if (buyorderVo.getTraderId() == null) {
            buyorderVo.getBuyorderGoodsVoList().forEach(buyOrderGood -> {
                buyOrderGood.setSupplyPolicyMaintained(0);
                buyOrderGood.setQualityPeriod("/");
                buyOrderGood.setInstallPolicy("/");
            });
            return;
        }

        buyorderVo.getBuyorderGoodsVoList().forEach(buyOrderGood -> {

            AfterSaleSupplyPolicy afterSaleSupplyPolicy = this.afterSaleServiceStandardService.findSupplyAfterSalePolicyBySkuNoAndTraderId(buyorderVo.getTraderId().longValue(), buyOrderGood.getSku());
            if (afterSaleSupplyPolicy == null) {
                buyOrderGood.setSupplyPolicyMaintained(0);
                buyOrderGood.setQualityPeriod("/");
                buyOrderGood.setInstallPolicy("/");
                return;
            }
            //处理安装政策
            dealWithInstallPolicy(afterSaleSupplyPolicy, buyOrderGood);

            //处理保修期
            buyOrderGood.setQualityPeriod("/");
            if (StringUtils.isNotEmpty(afterSaleSupplyPolicy.getGuaranteePolicyHostGuaranteePeriod())) {
                buyOrderGood.setQualityPeriod(afterSaleSupplyPolicy.getGuaranteePolicyHostGuaranteePeriod());
            }

            buyOrderGood.setSupplyPolicyMaintained(1);
        });
    }

    private void dealWithInstallPolicy(AfterSaleSupplyPolicy afterSaleSupplyPolicy, BuyorderGoodsVo buyOrderGood) {

        buyOrderGood.setInstallPolicy("/");

        if(ObjectUtils.notEmpty(afterSaleSupplyPolicy.getInstallPolicyInstallType())){
            if (InstallPolicyInstallTypeEnum.CHARGE.getType().equals(afterSaleSupplyPolicy.getInstallPolicyInstallType()) ||
                            InstallPolicyInstallTypeEnum.FREE.getType().equals(afterSaleSupplyPolicy.getInstallPolicyInstallType())) {
                buyOrderGood.setInstallPolicy(InstallPolicyInstallTypeEnum.getNameByValue(afterSaleSupplyPolicy.getInstallPolicyInstallType()));
                return;
            }
        }

        if (afterSaleSupplyPolicy.getTechnicalDirectSupplyMaintain() != null && afterSaleSupplyPolicy.getTechnicalDirectSupplyMaintain() == 1) {
            buyOrderGood.setInstallPolicy("提供远程指导");
        }

    }


    private String getProcessKey(BuyorderVo buyorderInfo) {
        User creatorInfo = userService.getUserById(buyorderInfo.getCreator());
        boolean isSupplyHCGroup = false;
        //是否是供应链耗材组
        if (!StringUtils.isEmpty(creatorInfo.getOrgName()) && creatorInfo.getOrgName().contains("供应链管理部耗材组")) {
            isSupplyHCGroup = true;
        }

        if (!isSupplyHCGroup) {
            return "buyorderVerify";
        }

        Long lastPublishTime = DateUtil.convertLong("2021-03-24 18:52:00", DateUtil.TIME_FORMAT);
        if (buyorderInfo.getAddTime() >= lastPublishTime) {
            return "buyorderVerify";
        }

        // 获取订单审核信息
        TaskService taskService = processEngine.getTaskService();
        // 获取当前活动节点
        Task taskInfo = taskService.createTaskQuery().processInstanceBusinessKey("buyorderVerify_HC_" + buyorderInfo.getBuyorderId()).singleResult();

        HistoryService historyService = processEngine.getHistoryService();
        List<HistoricProcessInstance> historicProcessInstanceList = historyService.createHistoricProcessInstanceQuery()
                .processInstanceBusinessKey("buyorderVerify_HC_" + buyorderInfo.getBuyorderId())
                .list();

        if (taskInfo != null || CollectionUtils.isNotEmpty(historicProcessInstanceList)) {
            return "buyorderVerify_HC";
        }
        return "buyorderVerify";
    }

    /**
     * 跳转到新增采购售后页面
     */
    @ResponseBody
    @RequestMapping(value = "/addAfterSalesPage")
    public ModelAndView addAfterSalesPage(HttpServletRequest request, Buyorder buyorder) {
        ModelAndView mav = new ModelAndView();


        BuyorderVo buyorderVo = buyorderService.getNewBuyorderGoodsVoList(buyorder);
        mav.addObject("buyorder", buyorderVo);
        // 获取退货原因
        List<SysOptionDefinition> sysList = getSysOptionDefinitionList(536);
        mav.addObject("sysList", sysList);
        mav.addObject("domain", domain);

        SettlementBillQueryCmd cmd = new SettlementBillQueryCmd(buyorder.getBuyorderId(), BusinessSourceTypeEnum.buyOrder);
        Integer buyOrderSettlementId = settlementBillApiService.getSettlementIdByBusiness(cmd);
        // 是否含供应商返利支付标识： 1是 0否
        int supplierRebateFlag = 0;
        if(Objects.nonNull(buyOrderSettlementId)){
            supplierRebateFlag = 1;
        }
        log.info("是否含供应商返利（ 1是 0否）：{},buyOrderSettlementId:{}",supplierRebateFlag,buyOrderSettlementId);

        mav.addObject("supplierRebateFlag",supplierRebateFlag);
        if ("th".equals(buyorder.getFlag())) {// 退货
            mav.setViewName("orderstream/buyorder/add_afterSale_th");
        } else if ("hh".equals(buyorder.getFlag())) {
            mav.setViewName("orderstream/buyorder/add_afterSale_hh");
        } else if("tp".equals(buyorder.getFlag())){
            //VDERP-12237按照SKU+发票号+发票代码作为唯一最小单元-start
            List<AfterBuyorderInvoiceGoodsDto> afterBuyorderInvoiceGoodsDtos = newAfterBuyorderService.queryAllCanBackInvoiceGoods(buyorderVo.getBuyorderId());
            mav.addObject("afterBuyorderInvoiceGoodsDtos",afterBuyorderInvoiceGoodsDtos);
            //获取发票种类字典库
            List<SysOptionDefinition> invoiceTypeList = getSysOptionDefinitionList(428);
            mav.addObject("invoiceTypeList",invoiceTypeList);
            //VDERP-12237按照SKU+发票号+发票代码作为唯一最小单元-end
            mav.setViewName("orderstream/buyorder/add_afterSale_tp");
        } else {
            return fail(mav);
        }
        return mav;
    }

    /**
     * 跳转到售后编辑页
     */
    @ResponseBody
    @RequestMapping(value = "/editAfterSalesPage")
    public ModelAndView editAfterSalesPage(HttpServletRequest request, AfterSalesVo afterSales) throws IOException {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ModelAndView mav = new ModelAndView();
        if (afterSales == null) {
            return pageNotFound(request);
        }
        afterSales.setTraderType(2);
        afterSales.setCompanyId(user.getCompanyId());
        afterSales = afterSalesOrderService.getAfterSalesVoDetail(afterSales);
        mav.addObject("afterSales", afterSales);

        // 获取退货原因
        List<SysOptionDefinition> sysList = getSysOptionDefinitionList(536);
        mav.addObject("sysList", sysList);
        mav.addObject("domain", domain);

        Buyorder buyorder = new Buyorder();
        buyorder.setBuyorderId(afterSales.getOrderId());
        buyorder.setFlag(AfterSalesTypeEnum.getType(afterSales.getType()));

        BuyorderVo sv = buyorderService.getNewBuyorderGoodsVoList(buyorder);

        if (sv == null) {
            return fail(mav);
        }
        mav.addObject("buyorder", sv);
        // if (afterSales.getType().equals(TH_BUYORDER_AFTER_SALE_CODE)) {// 退货
        if(afterSales.getType().equals(548)){
            List<AfterBuyorderInvoiceGoodsDto> afterBuyorderInvoiceGoodsDtos = newAfterBuyorderService.queryAllCanBackInvoiceGoods(afterSales.getOrderId());
            List<Integer> choosedGoodsId = afterSales.getAfterSalesGoodsList().stream().map(AfterSalesGoodsVo :: getOrderDetailId).collect(Collectors.toList());
            String afterInvoiceNo = afterSales.getAfterSalesInvoiceVoList().get(0).getInvoiceNo();
            afterBuyorderInvoiceGoodsDtos.stream().forEach(item -> {
                if(choosedGoodsId.contains(item.getDetailGoodsId()) && afterInvoiceNo.equals(item.getInvoiceNo())){
                    item.setChooseFlag(true);
                }else {
                    item.setChooseFlag(false);
                }
            });
            //查询售后暂存表发票信息
            AfterBuyorderInvoiceDto afterBuyorderInvoiceDto = buyorderAfterSalesApiService.queryInfoByAfterSalesId(afterSales.getAfterSalesId());
            mav.addObject("afterBuyorderInvoiceDto",afterBuyorderInvoiceDto);
            mav.addObject("afterBuyorderInvoiceGoodsDtos",afterBuyorderInvoiceGoodsDtos);
            //发票类型
            List<SysOptionDefinition> invoiceTypeList = getSysOptionDefinitionList(SysOptionConstant.ID_428);
            mav.addObject("invoiceTypeList", invoiceTypeList);
        }
        mav.setViewName("orderstream/buyorder/edit_afterSales_" + buyorder.getFlag());
        mav.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(sv)));
        return mav;
    }

    /**
     * 编辑2：审核通过后未执行退款运算可执行
     */
    @ResponseBody
    @RequestMapping(value = "/editAfterSalesPage2")
    public ModelAndView editAfterSalesPage2(HttpServletRequest request, AfterSalesVo afterSales) throws IOException {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ModelAndView mav = new ModelAndView();
        if (Objects.isNull(afterSales)) {
            return pageNotFound(request);
        }
        afterSales.setTraderType(2);
        afterSales.setCompanyId(user.getCompanyId());
        afterSales = afterSalesOrderService.getAfterSalesVoDetail(afterSales);
        if(!Objects.isNull(afterSales.getRefundAmountStatus())){
            log.info("{}提交失败,当前已执行退款预算{}",afterSales.getAfterSalesId(),afterSales.getRefundAmountStatus());
            return fail(mav);
        }

        setAttachmentList(afterSales);
        mav.addObject("afterSales", afterSales);

        // 获取退货原因
        List<SysOptionDefinition> sysList = getSysOptionDefinitionList(536);
        mav.addObject("sysList", sysList);
        mav.addObject("domain", domain);

        Buyorder buyorder = new Buyorder();
        buyorder.setBuyorderId(afterSales.getOrderId());
        buyorder.setFlag(AfterSalesTypeEnum.getType(afterSales.getType()));

        BuyorderVo sv = buyorderService.getNewBuyorderGoodsVoList(buyorder);
        if (Objects.isNull(sv)) {
            return fail(mav);
        }
        mav.addObject("buyorder", sv);
        mav.setViewName("orderstream/buyorder/edit_afterSales2_" + buyorder.getFlag());
        mav.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(sv)));
        return mav;
    }

    /**
     * 设置售后附件
     */
    private void setAttachmentList(AfterSalesVo afterSales){
        if(Objects.isNull(afterSales.getAfterSalesId()) || Objects.isNull(afterSales.getType())){
            return;
        }
        Attachment attachment = new Attachment();
        attachment.setRelatedId(afterSales.getAfterSalesId());
        attachment.setAttachmentFunction(afterSales.getType());
        afterSales.setAttachmentList(attachmentService.queryAttachmentList(attachment));
    }

    @ResponseBody
    @RequestMapping(value = "/saveDirectWarehouse")
    public ResultInfo saveDirectWarehouse(HttpServletRequest request,@RequestBody List<AfterSaleBuyorderDirectOutLog> afterSaleBuyorderDirectOutLogList) {
        //插入出入库明细主表
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        if(!CollectionUtils.isEmpty(afterSaleBuyorderDirectOutLogList)){
            List<WarehouseGoodsOutInItem> warehouseGoodsOutInItemList = new ArrayList<>();
            afterSaleBuyorderDirectOutLogList.forEach(goods->{
                WarehouseGoodsOutInItem inItem = new WarehouseGoodsOutInItem();
                inItem.setGoodsId(goods.getGoodsId());
                inItem.setNum(new BigDecimal(goods.getNum()));
                inItem.setVedengBatchNumber(goods.getVedengBatchNum());
                inItem.setProductDate(goods.getProduceTimeStr());
                inItem.setExpirationDate(goods.getValidTimeStr());
                inItem.setCheckStatusTime(goods.getOutTimeStr());
                inItem.setBatchNumber(goods.getIndustryBatchNumber());
                inItem.setSterilizationBatchNumber(goods.getSterilizationNumber());
                inItem.setRelatedId(goods.getAfterSalesGoodsId());
                warehouseGoodsOutInItemList.add(inItem);
            });
            try {
                warehouseGoodsInService.insertWarehouseGoodsPurchaseOutIn(user,afterSaleBuyorderDirectOutLogList.get(0).getAfterSalesId(),warehouseGoodsOutInItemList,WarehouseGoodsInEnum.BUYORDER_WAREHOUSE_CHANGE_IN);
            }catch (Exception e) {
                logger.error("insertWarehouseGoodsPurchaseOutIn error: " + e);
                return ResultInfo.error();
            }

        }
        return ResultInfo.success();
    }

    /**
     * 去新增直发入库页面
     */
    @RequestMapping("/toAddDirectWarehousePage")
    public ModelAndView toAddDirectWarehousePage(AfterSalesVo afterSales){
        if(Objects.isNull(afterSales.getAfterSalesNo()) || Objects.isNull(afterSales.getAfterSalesId())){
            return fail(new ModelAndView());
        }
        ModelAndView mav = new ModelAndView("orderstream/buyorder/add_direct_warehouse_record");
        //获取售后明细
        List<AfterSalesGoodsVo> afterSaleGoodsList = afterSalesService.getAfterSalesDetailForWareHouseGoods(afterSales);

        //已入库查询
        List<AfterSalesGoodsVo> afterSalesGoodsVoList = new ArrayList<>();
        List<WarehouseGoodsOutInItem> warehouseGoodsOutInItemList = warehouseGoodsInService.getWarehouseGoodsPurchaseOutInByPkId(afterSales.getAfterSalesId());
        Map<Integer, List<WarehouseGoodsOutInItem>> warehouseGoodsOutInItemListMap = new HashMap<>(16);
        if(CollectionUtils.isNotEmpty(warehouseGoodsOutInItemList)) {
        	warehouseGoodsOutInItemListMap = warehouseGoodsOutInItemList.stream().collect(Collectors.groupingBy(e -> e.getGoodsId()));
        }
        //比较售后明细和已入库信息
        //如果为直发，如果在入库信息不存在则
        if(CollectionUtils.isNotEmpty(afterSaleGoodsList)) {
        	for (AfterSalesGoodsVo afterSaleGoods : afterSaleGoodsList) {
        		Integer deliverDiret = afterSaleGoods.getDeliveryDirect();
        		//直发商品
        		if(deliverDiret.equals(1)) {
        			int afterGoodsNum = afterSaleGoods.getNum();
        			int goodsId = afterSaleGoods.getGoodsId();
        			if(Objects.nonNull(warehouseGoodsOutInItemListMap)) {
        				List<WarehouseGoodsOutInItem> warehouseGoodsOutInItem = warehouseGoodsOutInItemListMap.get(goodsId);
        				int wareHouseInNum = 0 ;
        				if(CollectionUtils.isNotEmpty(warehouseGoodsOutInItem)) {
        					wareHouseInNum = warehouseGoodsOutInItem.stream().mapToInt(item -> item.getNum().intValue()).sum();
        				}
        				int returnNum = afterGoodsNum - wareHouseInNum;
        				if(returnNum>0) {
        	        		//num需要重新计算
        					afterSaleGoods.setMaxOutNum(returnNum);
        	        		afterSalesGoodsVoList.add(afterSaleGoods);
        				}

        			}
        			//无入库信息
        			else {
        				afterSaleGoods.setNum(afterGoodsNum);
                		afterSalesGoodsVoList.add(afterSaleGoods);
        			}
        		}
			}
        }

        for (AfterSalesGoodsVo afterSalesGoodsVo : afterSalesGoodsVoList) {
        	afterSalesGoodsVo.getGoodsId();
        	OutInDetail outInDetail = new OutInDetail();
        	warehouseGoodsInService.querySkuInfo(outInDetail, afterSalesGoodsVo.getGoodsId());
        	afterSalesGoodsVo.setFirstEngageId(outInDetail.getFirstEngageId());
        	afterSalesGoodsVo.setRegistrationNumber(outInDetail.getRegistrationNumber());
		}

        mav.addObject("afterSalesGoodsVoList", afterSalesGoodsVoList);
        mav.addObject("afterSales",afterSales);
        return mav;
    }

    /**
     * 查看售后详情页
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping("/viewAfterSalesDetail")
    public ModelAndView viewAfterSalesDetail(HttpServletRequest request, AfterSalesVo afterSales) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        afterSales.setCompanyId(user.getCompanyId());
        ModelAndView mav = new ModelAndView();
        afterSales.setTraderType(2);
        //基本信息
        AfterSalesVo afterSalesVo = afterSalesOrderService.getbuyorderAfterSalesVoDetail(afterSales);
        if(afterSalesVo.getType().equals(548)){
            //采购仅退票售后跳转新详情页
            mav.setViewName("redirect:/after/newBuyorder/afterBuyorderTpDetail.do?afterSalesId="+afterSalesVo.getAfterSalesId());
            logger.info("采购售后单为仅退票售后跳转至新页面 afterSalesId:{}", afterSales.getAfterSalesId());
            return mav;
        }
        mav.addObject("isSupply", userService.getSupplyChainFlagByUserId(user.getUserId()));

        if (afterSalesVo == null) {

            logger.info("订单流采购售后单viewAfterSalesDetail-->getbuyorderAfterSalesVoDetail，订单参数：{}，订单查询为空", JSON.toJSONString(afterSales));
            return fail(mav);
        }


        try {
            setBaseAfterSalesInfo(user, mav, afterSalesVo);
        } catch (Exception e) {
            logger.error("订单流采购售后单viewAfterSalesDetail-->setBaseAfterSalesInfo，订单参数：{}，错误：{}", JSON.toJSONString(afterSales), e);
        }

        try {
            setCostInfo(mav, afterSalesVo);
        } catch (Exception e) {
            logger.error("订单流采购售后单viewAfterSalesDetail-->setCostInfo，订单参数：{}，错误：{}", JSON.toJSONString(afterSales), e);
        }

        try {
            setCommunicateInfo(mav, afterSalesVo);
        } catch (Exception e) {
            logger.error("订单流采购售后单viewAfterSalesDetail-->setCommunicateInfo，订单参数：{}，错误：{}", JSON.toJSONString(afterSales), e);
        }

        try {
            setAfterSaleVerifyInfo(mav, afterSalesVo,user);
        } catch (Exception e) {
            logger.error("订单流采购售后单viewAfterSalesDetail-->setAfterSaleVerifyInfo，订单参数：{}，错误：{}", JSON.toJSONString(afterSales), e);
        }

        try {
            setOverAfterSalesVerifyInfo(afterSalesVo, mav,user);
        } catch (Exception e) {
            logger.error("订单流采购售后单viewAfterSalesDetail-->setOverAfterSalesVerifyInfo，订单参数：{}，错误：{}", JSON.toJSONString(afterSales), e);
        }

        try {
            setPayVerifyInfo(mav, afterSalesVo);
        } catch (Exception e) {
            logger.error("订单流采购售后单viewAfterSalesDetail-->setInvoiceVerifyInfo，订单参数：{}，错误：{}", JSON.toJSONString(afterSales), e);
        }

        try {
            // 跳转页面方法 mav.setViewName
            setPageInfo(mav, afterSalesVo);
        } catch (Exception e) {
            logger.error("订单流采购售后单viewAfterSalesDetail-->setPageInfo，订单参数：{}，错误：{}", JSON.toJSONString(afterSales), e);
        }


        try {
            setInvoiceApplyVerifyInfo(mav, afterSalesVo);
        } catch (Exception e) {
            logger.error("订单流采购售后单viewAfterSalesDetail-->setInvoiceApplyVerifyInfo，订单参数：{}，错误：{}", JSON.toJSONString(afterSales), e);
        }

        //计算售后单状态
        try {
            String statusList = calculateStatus(afterSalesVo);
            mav.addObject("statusList", statusList);
        } catch (Exception e) {
            log.error("计算售后单状态出错，订单：{}，错误：{}", JSON.toJSONString(afterSalesVo), e);
        }

        //判断创建人
        checkCreater(mav, user, afterSalesVo);

        buyOrderAfterSalesDetailFacade.getBuyOrderDetail(mav, user, afterSalesVo);

        partShowStatusInit(mav, user, afterSalesVo);

        return mav;
    }

    private void partShowStatusInit(ModelAndView mav, User user, AfterSalesVo afterSalesVo) {
        if (AfterSalesConstant.TH_BUYORDER_AFTER_SALE_CODE.equals(afterSalesVo.getType())) {

                //审核中
                if (AfterSalesConstant.VALID_PROCRESSING_CODE.equals(afterSalesVo.getStatus())) {
                    mav.addObject("afterSaleVerifyFlag", 1);
                    mav.addObject("verifyShowFlag", 1);
                }

                if (AfterSalesConstant.VALID_FAIL_CODE.equals(afterSalesVo.getStatus()) && AfterSalesConstant.CONFIRM_CODE.equals(afterSalesVo.getAtferSalesStatus())) {
                    mav.addObject("afterSaleVerifyFlag", 1);
                    mav.addObject("verifyShowFlag", 1);
                }
                //进行中
                if (AfterSalesConstant.PROCRESSING_CODE.equals(afterSalesVo.getAtferSalesStatus())) {
                    mav.addObject("afterSaleVerifyFlag", 1);
                    mav.addObject("verifyShowFlag", 1);
                    if (mav.getModel().get("payVerifyInfoStr") != null) {
                        mav.addObject("payVerifyFlag", 1);
                        mav.addObject("verifyShowFlag", 3);
                    }
                    if ((mav.getModel().get("overAfterSaleVerifyInfoStr") != null)) {
                        mav.addObject("overAfterSaleVerifyFlag", 1);
                        mav.addObject("verifyShowFlag", 2);
                    }
                    mav.addObject("refoundGoodsFlag", 1);
                    mav.addObject("invoiceFlag", 1);
                    mav.addObject("payInfoFlag", 1);
                    if (mav.getModel().get("isPayApplySh") != null) {
                        mav.addObject("tabShowFlag", 2);
                    } else {
                        mav.addObject("tabShowFlag", 1);
                    }
                    mav.addObject("tradeInfoFlag", 1);

                    //直发退&进行中&记录中出库数量之和＜申请退货数量之和-未收货数量
                    List<AfterSalesGoodsVo> afterSalesGoodsVoList = afterSalesOrderService.getOutStockResult(afterSalesVo);
                    if (CollectionUtils.isNotEmpty(afterSalesGoodsVoList)) {
                        Optional<AfterSalesGoodsVo> any = afterSalesGoodsVoList.stream().filter(e -> ErpConst.ONE.equals(e.getDeliveryDirect()) && e.getFlagNum() < 0).findAny();
                        if (any.isPresent()) {
                            mav.addObject("outShowFlag", 1);
                        }
                        Optional<AfterSalesGoodsVo> alter = afterSalesGoodsVoList.stream().filter(e -> e.getFlagNum() < 0).findAny();
                        if (alter.isPresent()) {
                            mav.addObject("alertFlag", 1);
                        }
                    }
                }


                if ((AfterSalesConstant.CLOSED_CODE.equals(afterSalesVo.getAtferSalesStatus())
                        && (AfterSalesConstant.VALID_FAIL_CODE.equals(afterSalesVo.getStatus())
                        || AfterSalesConstant.VALID_SUCCESS_CODE.equals(afterSalesVo.getStatus())) || AfterSalesConstant.COMPLETED_CODE.equals(afterSalesVo.getAtferSalesStatus()))) {
                    mav.addObject("afterSaleVerifyFlag", 1);
                    mav.addObject("verifyShowFlag", 1);
                    if (mav.getModel().get("payVerifyInfoStr") != null) {
                        mav.addObject("payVerifyFlag", 1);
                    }
                    if ((mav.getModel().get("overAfterSaleVerifyInfoStr") != null)) {
                        mav.addObject("overAfterSaleVerifyFlag", 1);
                    }
                    mav.addObject("refoundGoodsFlag", 1);
                    mav.addObject("invoiceFlag", 1);
                    mav.addObject("payInfoFlag", 1);
                    mav.addObject("tradeInfoFlag", 1);
                }


            if (mav.getModel().get("isVerifyUser") != null) {
                //审核中
                mav.addObject("afterSaleVerifyFlag", 1);
                mav.addObject("verifyShowFlag", 1);
                //进行中
                if (AfterSalesConstant.VALID_SUCCESS_CODE.equals(afterSalesVo.getStatus())
                        || AfterSalesConstant.VALID_FAIL_CODE.equals(afterSalesVo.getStatus())) {
                    mav.addObject("afterSaleVerifyFlag", 1);
                    if (mav.getModel().get("payVerifyInfoStr") != null) {
                        mav.addObject("payVerifyFlag", 1);
                    }
                    if ((mav.getModel().get("overAfterSaleVerifyInfoStr") != null)) {
                        mav.addObject("overAfterSaleVerifyFlag", 1);
                    }
                    mav.addObject("refoundGoodsFlag", 1);
                    mav.addObject("invoiceFlag", 1);
                    mav.addObject("payInfoFlag", 1);
                    mav.addObject("tabShowFlag", 1);
                    mav.addObject("tradeInfoFlag", 1);
                }
            }


        } else if (AfterSalesConstant.HH_BUYORDER_AFTER_SALE_CODE.equals(afterSalesVo.getType())) {

                //审核中
                if (AfterSalesConstant.VALID_PROCRESSING_CODE.equals(afterSalesVo.getStatus())) {
                    mav.addObject("afterSaleVerifyFlag", 1);
                    mav.addObject("verifyShowFlag", 1);
                }
                //进行中
                if (AfterSalesConstant.PROCRESSING_CODE.equals(afterSalesVo.getAtferSalesStatus())) {
                    mav.addObject("afterSaleVerifyFlag", 1);
                    mav.addObject("verifyShowFlag", 1);
                    if (mav.getModel().get("payVerifyInfoStr") != null) {
                        mav.addObject("payVerifyFlag", 1);
                        mav.addObject("verifyShowFlag", 3);
                    }

                    mav.addObject("refoundOutGoodsFlag", 1);
                    mav.addObject("refoundInGoodsFlag", 1);
                    mav.addObject("invoiceFlag", 1);
                    mav.addObject("payInfoFlag", 1);
                    if (mav.getModel().get("isPayApplySh") != null) {
                        mav.addObject("tabShowFlag", 2);
                    } else {
                        mav.addObject("tabShowFlag", 1);
                    }
                    mav.addObject("tradeInfoFlag", 1);

                    List<AfterSalesGoodsVo> outStockList = afterSalesOrderService.getOutStockResult(afterSalesVo);
                    if (CollectionUtils.isNotEmpty(outStockList)) {
                        Optional<AfterSalesGoodsVo> optional = outStockList.stream().filter(e -> ErpConst.ONE.equals(e.getDeliveryDirect()) && e.getFlagNum() < 0).findAny();
                        if (optional.isPresent()) {
                            mav.addObject("outShowFlag", 1);
                        }
                    }
                    List<AfterSalesGoodsVo> afterSalesGoodsVoList = afterSalesOrderService.getInOrOutLog(afterSalesVo);
                    if (CollectionUtils.isNotEmpty(afterSalesGoodsVoList)) {
                        Optional<AfterSalesGoodsVo> any = afterSalesGoodsVoList.stream().filter(e -> e.getCloseFlag().equals(0)).findAny();
                        if (any.isPresent()) {
                            mav.addObject("alertFlag", 1);
                        }
                    }

                }
                if ((AfterSalesConstant.CLOSED_CODE.equals(afterSalesVo.getAtferSalesStatus())
                        && (AfterSalesConstant.VALID_FAIL_CODE.equals(afterSalesVo.getStatus())
                        || AfterSalesConstant.VALID_SUCCESS_CODE.equals(afterSalesVo.getStatus())))
                        || AfterSalesConstant.COMPLETED_CODE.equals(afterSalesVo.getAtferSalesStatus())) {
                    mav.addObject("afterSaleVerifyFlag", 1);
                    mav.addObject("verifyShowFlag", 1);
                    if (mav.getModel().get("payVerifyInfoStr") != null) {
                        mav.addObject("payVerifyFlag", 1);
                    }

                    mav.addObject("refoundOutGoodsFlag", 1);
                    mav.addObject("refoundInGoodsFlag", 1);
                    mav.addObject("invoiceFlag", 1);
                    mav.addObject("payInfoFlag", 1);
                    mav.addObject("tradeInfoFlag", 1);
                }


            if (mav.getModel().get("isVerifyUser") != null) {
                //审核中
                mav.addObject("afterSaleVerifyFlag", 1);
                mav.addObject("verifyShowFlag", 1);
                //进行中
                if (AfterSalesConstant.VALID_SUCCESS_CODE.equals(afterSalesVo.getStatus())
                        || AfterSalesConstant.VALID_FAIL_CODE.equals(afterSalesVo.getStatus())) {
                    mav.addObject("afterSaleVerifyFlag", 1);
                    if (mav.getModel().get("payVerifyInfoStr") != null) {
                        mav.addObject("payVerifyFlag", 1);
                    }
                    if ((mav.getModel().get("overAfterSaleVerifyInfoStr") != null)) {
                        mav.addObject("overAfterSaleVerifyFlag", 1);
                    }
                    mav.addObject("refoundOutGoodsFlag", 1);
                    mav.addObject("refoundInGoodsFlag", 1);
                    mav.addObject("invoiceFlag", 1);
                    mav.addObject("payInfoFlag", 1);
                    mav.addObject("tabShowFlag", 1);
                    mav.addObject("tradeInfoFlag", 1);
                }
            }
        }
    }

    /**
     * 创建人及上级
     * @param mav
     * @param user 当前登录人
     * @param afterSalesVo
     */
    private void checkCreater(ModelAndView mav, User user, AfterSalesVo afterSalesVo) {
        if (Objects.isNull(user.getUserId())){
            log.info("当前登录人userId为空：{},售后单号：{}",user.getUserId(),afterSalesVo.getAfterSalesNo());
            return;
        }
        if (afterSalesVo.getCreator().equals(user.getUserId()) || user.getUserId().equals(new Integer(2))) {
            mav.addObject("isCreator", 1);
        }
    }

    private void setInvoiceApplyVerifyInfo(ModelAndView mav, AfterSalesVo afterSalesVo) {
        //开票申请审核信息
        InvoiceApply invoiceApplyInfo = invoiceService.getInvoiceApplyByRelatedId(afterSalesVo.getAfterSalesId(), SysOptionConstant.ID_504, afterSalesVo.getCompanyId());
        mav.addObject("invoiceApply", invoiceApplyInfo);
        if (invoiceApplyInfo != null) {
            Map<String, Object> historicInfoInvoice = actionProcdefService.getHistoric(processEngine, "invoiceVerify_" + invoiceApplyInfo.getInvoiceApplyId());
            mav.addObject("taskInfoInvoice", historicInfoInvoice.get("taskInfo"));
            mav.addObject("startUserInvoice", historicInfoInvoice.get("startUser"));
            mav.addObject("candidateUserMapInvoice", historicInfoInvoice.get("candidateUserMap"));
            // 最后审核状态
            mav.addObject("endStatusInvoice", historicInfoInvoice.get("endStatus"));
            mav.addObject("historicActivityInstanceInvoice", historicInfoInvoice.get("historicActivityInstance"));
            mav.addObject("commentMapInvoice", historicInfoInvoice.get("commentMap"));

            Task taskInfoInvoice = (Task) historicInfoInvoice.get("taskInfo");
            //当前审核人
            String verifyUsersInvoice = null;
            if (null != taskInfoInvoice) {
                Map<String, Object> taskInfoVariables = actionProcdefService.getVariablesMap(taskInfoInvoice);
                verifyUsersInvoice = (String) taskInfoVariables.get("verifyUsers");
            }
            mav.addObject("verifyUsersInvoice", verifyUsersInvoice);
        }
    }

    private void setAfterSaleVerifyInfo(ModelAndView mav, AfterSalesVo afterSalesVo, User user) {

        //计算售后单审核状态
        Map<String, Object> leastHistoricInfo = actionProcdefService.getLeast(processEngine, "afterSalesVerify_" + afterSalesVo.getAfterSalesId());
        Task leastTaskInfo = (Task) leastHistoricInfo.get("taskInfo");
        List<IdentityLink> identityLinkList= new ArrayList<>();
        if (null != leastTaskInfo) {
            Map<String, Object> taskInfoVariables = actionProcdefService.getVariablesMap(leastTaskInfo);
            String verifyUsers1 = (String) taskInfoVariables.get("verifyUsers");
            Map<String, Object> candidateUserMap = (Map<String, Object>) leastHistoricInfo.get("candidateUserMap");
            if (candidateUserMap.get("belong") != null && (Boolean) candidateUserMap.get("belong")) {
                mav.addObject("isVerifyUser", 1);
            }
            mav.addObject("taskInfo", leastTaskInfo);
            identityLinkList = (List<IdentityLink>)candidateUserMap.get(leastTaskInfo.getId());

        }
        List<HistoricActivityInstance> historicActivityInstance = (List<HistoricActivityInstance>)leastHistoricInfo.get("historicActivityInstance");
        if(CollectionUtils.isNotEmpty(historicActivityInstance)){
            HistoricActivityInstance historicActivityInstance1 = historicActivityInstance.stream().filter(e -> "产品主管审核".equals(e.getActivityName())).findFirst().orElse(null);
            if(historicActivityInstance1 != null){
                if(user.getUsername().equals(historicActivityInstance1.getAssignee())){
                    mav.addObject("isVerifyUser", 1);
                }
            }
        }
        List<HistoricActivityInstance> leastHistoricActivityInstance = setAssignRealNames(mav, leastHistoricInfo);
        List<StatusNode> orderValidList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(leastHistoricActivityInstance)) {

//            dealWithStatusNodeInfo(leastHistoricActivityInstance,orderValidList,leastHistoricActivityInstance);
            Map<String, Object> commentMap = (Map<String, Object>) leastHistoricInfo.get("commentMap");
            leastHistoricActivityInstance.removeIf(e -> StringUtils.isBlank(e.getActivityName()));
            if (CollectionUtils.isNotEmpty(leastHistoricActivityInstance)) {
                String validString = getVerifyNodeslnfoStr(leastHistoricActivityInstance, orderValidList, commentMap, identityLinkList, ValidStatusProcessUtil.AFTER_SALE_TYPE);
                mav.addObject("afterSaleVerifyInfoStr", validString);
            }
        }
    }

    private String getVerifyNodeslnfoStr(List<HistoricActivityInstance> leastHistoricActivityInstance, List<StatusNode> orderValidList, Map<String, Object> commentMap, List<IdentityLink> identityLinkList, int type) {
        if ("审核完成".equals(leastHistoricActivityInstance.get(leastHistoricActivityInstance.size() - 1).getActivityName())
                || "驳回".equals(leastHistoricActivityInstance.get(leastHistoricActivityInstance.size() - 1).getActivityName())) {
            for (int i = 0; i < leastHistoricActivityInstance.size(); i++) {
                HistoricActivityInstance historicActivityInstance = leastHistoricActivityInstance.get(i);
                StatusNode statusNode = new StatusNode();
                statusNode.setLabel(historicActivityInstance.getActivityName());
                statusNode.setSort(i);
                String comments = (String) commentMap.get(historicActivityInstance.getTaskId());
                StringBuilder tip = new StringBuilder();
                if (StringUtils.isNotBlank(historicActivityInstance.getAssignee())) {
                    tip.append(historicActivityInstance.getAssignee());
                }
                if (historicActivityInstance.getEndTime() != null) {
                    tip.append("<br>").append(DateFormatUtils.format(historicActivityInstance.getEndTime(), "yyyy-MM-dd HH:mm:ss"));
                }
                if (comments != null) {
                    tip.append("<br>").append("<span title=\"").append(comments).append("\" class=\"no-wrap\">").append(comments).append("</span>");
                }
                statusNode.setTip(tip.toString());
                if ("驳回".equals(leastHistoricActivityInstance.get(leastHistoricActivityInstance.size() - 1).getActivityName())) {
                    statusNode.setFail(1);
                }
                orderValidList.add(statusNode);
            }
        } else {
            orderValidList = ValidStatusProcessUtil.initValidStatusNodeList(type);
            for (int i = 0; i < leastHistoricActivityInstance.size(); i++) {
                HistoricActivityInstance historicActivityInstance = leastHistoricActivityInstance.get(i);
                StatusNode statusNode = orderValidList.get(i);
                statusNode.setSort(i);
                String comments = (String) commentMap.get(historicActivityInstance.getTaskId());
                StringBuilder tip = new StringBuilder();
                if(i==leastHistoricActivityInstance.size()-1){
                    if (CollectionUtils.isNotEmpty(identityLinkList)) {
                        Set<String> userNameList = identityLinkList.stream().map(identityLink -> identityLink.getUserId()).collect(Collectors.toSet());
                        String verifyUsers = StringUtils.join(userNameList, ",");
                        tip.append("<span title=\"").append(verifyUsers).append("\" class=\"no-wrap\">").append(verifyUsers).append("</span>").append("<br>");
                    }
                }else if (StringUtils.isNotBlank(historicActivityInstance.getAssignee())) {
                    tip.append(historicActivityInstance.getAssignee()).append("<br>");
                }
                if (historicActivityInstance.getEndTime() != null) {
                    tip.append(DateFormatUtils.format(historicActivityInstance.getEndTime(), "yyyy-MM-dd HH:mm:ss")).append("<br>");
                }
                if (StringUtils.isNotBlank(comments)) {
                    tip.append("<span title=\"").append(comments).append("\" class=\"no-wrap\">").append(comments).append("</span>").append("<br>");;
                }
                statusNode.setTip(tip.toString());
            }
        }
        StatusNode.changeNodeStatus(orderValidList, leastHistoricActivityInstance.size() - 1);
        return JSON.toJSONString(orderValidList);
    }

    private void setOverAfterSalesVerifyInfo(AfterSalesVo afterSales, ModelAndView mav, User user) {

        //计算售后完结审核状态
        Map<String, Object> leastHistoricInfoOver = actionProcdefService.getLeast(processEngine, "overBuyorderAfterSalesVerify_" + afterSales.getAfterSalesId());
        Task leastTaskInfoOver = (Task) leastHistoricInfoOver.get("taskInfo");
        List<IdentityLink> identityLinkList = new ArrayList<>();
        if (leastTaskInfoOver != null) {
            Map<String, Object> taskInfoVariables = actionProcdefService.getVariablesMap(leastTaskInfoOver);
            String verifyUsers1 = (String) taskInfoVariables.get("verifyUsers");
            Map<String, Object> candidateUserMap = (Map<String, Object>) leastHistoricInfoOver.get("candidateUserMap");
            if (candidateUserMap.get("belong") != null && (Boolean) candidateUserMap.get("belong")) {
                mav.addObject("isVerifyUser", 1);
            }
            mav.addObject("taskInfo", leastTaskInfoOver);
            mav.addObject("overVerifyFlag", 1);
            identityLinkList = (List<IdentityLink>)candidateUserMap.get(leastTaskInfoOver.getId());
        }

        List<HistoricActivityInstance> historicActivityInstance = (List<HistoricActivityInstance>)leastHistoricInfoOver.get("historicActivityInstance");
        if(CollectionUtils.isNotEmpty(historicActivityInstance)){
            HistoricActivityInstance historicActivityInstance1 = historicActivityInstance.stream().filter(e -> "产品主管审核".equals(e.getActivityName())).findFirst().orElse(null);
            if(historicActivityInstance1 != null){
                if(user.getUsername().equals(historicActivityInstance1.getAssignee())){
                    mav.addObject("isVerifyUser", 1);
                }
            }
        }


        List<HistoricActivityInstance> leastHistoricActivityInstanceOver = setAssignRealNames(mav, leastHistoricInfoOver);
        if (null != leastTaskInfoOver) {
            mav.addObject("isOverVerify", 1);
        }

        List<StatusNode> closeValidList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(leastHistoricActivityInstanceOver)) {

            Map<String, Object> commentMap = (Map<String, Object>) leastHistoricInfoOver.get("commentMap");

            leastHistoricActivityInstanceOver.removeIf(e -> StringUtils.isBlank(e.getActivityName()));
            String verifyNodeslnfoStr = getVerifyNodeslnfoStr(leastHistoricActivityInstanceOver, closeValidList, commentMap, identityLinkList, ValidStatusProcessUtil.OVER_AFTER_SALE_TYPE);


            mav.addObject("overAfterSaleVerifyInfoStr", verifyNodeslnfoStr);
        }
    }

    private void setPayVerifyInfo(ModelAndView mav, AfterSalesVo afterSalesVo) {
        // 判断是否有正在审核中的付款申请
        Integer isPayApplySh = 0;
        Integer payApplyId = 0;
        if (CollectionUtils.isNotEmpty(afterSalesVo.getAfterPayApplyList())) {
            for (int i = 0; i < afterSalesVo.getAfterPayApplyList().size(); i++) {
                if (afterSalesVo.getAfterPayApplyList().get(i).getValidStatus() == 0) {
                    isPayApplySh = 1;
                    break;
                }
            }
            if (!afterSalesVo.getAfterPayApplyList().isEmpty() && payApplyId == 0) {
                payApplyId = afterSalesVo.getAfterPayApplyList().get(0).getPayApplyId();
            }

            //计算付款审核状态
            Map<String, Object> leastHistoricInfoOver = actionProcdefService.getLeast(processEngine, "paymentVerify_" + payApplyId);
            Task leastTaskInfoOver = (Task) leastHistoricInfoOver.get("taskInfo");
            List<HistoricActivityInstance> leastHistoricActivityInstanceOver = setAssignRealNames(mav, leastHistoricInfoOver);
            List<IdentityLink> identityLinkList = new ArrayList<>();
            if (null != leastTaskInfoOver) {
                mav.addObject("isPayApply", 1);
                Map<String, Object> candidateUserMap = (Map<String, Object>) leastHistoricInfoOver.get("candidateUserMap");
                 identityLinkList = (List<IdentityLink>)candidateUserMap.get(leastTaskInfoOver.getId());
            }
            List<StatusNode> closeValidList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(leastHistoricActivityInstanceOver)) {

                Map<String, Object> commentMap = (Map<String, Object>) leastHistoricInfoOver.get("commentMap");

                leastHistoricActivityInstanceOver.removeIf(e -> StringUtils.isBlank(e.getActivityName()));
                String verifyNodeslnfoStr = getVerifyNodeslnfoStr(leastHistoricActivityInstanceOver, closeValidList, commentMap,identityLinkList, ValidStatusProcessUtil.PAY_APPLY_TYPE);


                mav.addObject("payVerifyInfoStr", verifyNodeslnfoStr);
            }
        }
        if (isPayApplySh == 1) {
            mav.addObject("isPayApplySh", isPayApplySh);
        }


    }

    private void setPageInfo(ModelAndView mav, AfterSalesVo afterSalesVo) {
        //发票类型
        List<SysOptionDefinition> invoiceTypeList = getSysOptionDefinitionList(SysOptionConstant.ID_428);
        mav.addObject("invoiceTypeList", invoiceTypeList);

        //物流信息
        if (afterSalesVo.getSubjectType() == 536) {
            if (afterSalesVo.getType() == 546) {
                mav.setViewName("orderstream/buyorder/view_afterSale_th");
                if (afterSalesVo.getStatus() == 2) {
                    if (afterSalesVo.getAfterPayApplyList() != null && afterSalesVo.getAfterPayApplyList().size() > 0
                            && afterSalesVo.getAfterPayApplyList().get(0).getValidStatus() == 0) {
                        mav.addObject("payStatus", 0);//最新付款申请为待审核状态
                    } else {
                        mav.addObject("payStatus", 1);
                    }
                }
            } else {
                if (afterSalesVo.getStatus() == 2) {
                    if (afterSalesVo.getAfterPayApplyList() != null && afterSalesVo.getAfterPayApplyList().size() > 0
                            && afterSalesVo.getAfterPayApplyList().get(0).getValidStatus() == 0) {
                        mav.addObject("payStatus", 0);//最新付款申请为待审核状态
                    } else {
                        mav.addObject("payStatus", 1);
                    }
                }
                mav.setViewName("orderstream/buyorder/view_afterSale_hh");
            }
        }
        if(afterSalesVo.getIsNew() == null || ErpConst.ZERO.equals(afterSalesVo.getIsNew())){
            mav.setViewName("redirect:/aftersales/order/viewAfterSalesDetail.do?afterSalesId="+afterSalesVo.getAfterSalesId()+"&traderType=2");
        }
    }

    private void setCommunicateInfo(ModelAndView mav, AfterSalesVo afterSalesVo) {
        if (afterSalesVo.getType() != 544 && afterSalesVo.getType() != 545 && afterSalesVo.getType() != 552 && afterSalesVo.getType() != 553) {
            CommunicateRecord communicateRecord = new CommunicateRecord();
            communicateRecord.setAfterSalesId(afterSalesVo.getAfterSalesId());
            List<CommunicateRecord> crList = traderCustomerService.getCommunicateRecordList(communicateRecord);
            //遍历结果集循环获取客户名称
            for (CommunicateRecord cr : crList) {
                AfterSalesTrader afterSalesTrader = new AfterSalesTrader();
                afterSalesTrader.setAfterSalesTraderId(cr.getAfterSalesTraderId());
                AfterSalesTrader ast = afterSalesOrderService.getAfterSalesTrader(afterSalesTrader);
                if (ast != null) {
                    cr.setAfterSalesTraderName(ast.getTraderName());
                }
            }
            mav.addObject("communicateList", crList);
        }
    }

    private void setCostInfo(ModelAndView mav, AfterSalesVo afterSalesVo) {
        //查询订单的费用类型以及费用列表
        AfterSalesCost afterSalesCost = new AfterSalesCost();
        afterSalesCost.setAfterSalesId(afterSalesVo.getAfterSalesId());
        List<AfterSalesCostVo> costList = afterSalesOrderService.getAfterSalesCostListById(afterSalesCost);
        for (AfterSalesCostVo cost : costList) {
            cost.setCostTypeName(getSysOptionDefinition(cost.getType()).getComments());
            cost.setLastModTime(DateUtil.convertString(cost.getModTime(), ""));
        }
        mav.addObject("costList", costList);
    }

    private void setBaseAfterSalesInfo(User user, ModelAndView mav, AfterSalesVo afterSalesVo) {
        if (afterSalesVo.getRealRefundAmount().compareTo(BigDecimal.ZERO) > 0) {
            mav.addObject("flag", 1);
        } else {
            mav.addObject("flag", 2);
        }

        Buyorder buyorder = buyorderMapper.selectByPrimaryKey(afterSalesVo.getOrderId());
        mav.addObject("isGift",buyorder.getIsGift() != null ? buyorder.getIsGift() : 0);

        List<User> users = userService.getProductUserByRoleName();

        //售后单生效后，销售人员不显示"关闭订单"按钮；设置afterSalesVo.setCloseStatus(2)来实现隐藏"关闭订单"按钮
        if (afterSalesVo.getValidStatus() > 0 && null != user.getOrgId()) {
            if (SysOptionConstant.ID_535.equals(afterSalesVo.getSubjectType()) && 10 != user.getOrgId()) {//销售
                afterSalesVo.setCloseStatus(2);
            }
        }
        //查询关闭原因
        afterSalesVo.setAfterSalesStatusResonName((getSysOptionDefinition(afterSalesVo.getAfterSalesStatusReson()).getComments()));
        PayApply payApply = new PayApply();
        payApply.setRelatedId(afterSalesVo.getAfterSalesId());
        payApply.setPayType(ErpConst.AFTERSALES_ORDER_TYPE);//售后类型
        PayApply payApply1 = payApplyService.getPayApplyMaxRecord(payApply);
        //mav.addObject("curr_user", user);
        // 添加航信作废展示处理
        afterSalesOrderService.transAfterSalesVoForBlueInValid(afterSalesVo);

        mav.addObject("afterSalesVo", afterSalesVo);
        mav.addObject("payApply", payApply1);

        String sku = "";
        if (afterSalesVo.getAfterSalesGoodsList().size() > 0) {
            for (int i = 0; i < afterSalesVo.getAfterSalesGoodsList().size(); i++) {
                if (i == afterSalesVo.getAfterSalesGoodsList().size() - 1) {
                    sku += afterSalesVo.getAfterSalesGoodsList().get(i).getSku();
                } else {
                    sku += afterSalesVo.getAfterSalesGoodsList().get(i).getSku() + ",";
                }
            }
        }
        if (null != sku && !("".equals(sku))) {
            mav.addObject("sku", sku);
        }

        List<Integer> skuIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(afterSalesVo.getAfterReturnOutstockList())) {
            afterSalesVo.getAfterReturnOutstockList().stream().forEach(saleGood -> {
                skuIds.add(saleGood.getGoodsId());
            });
        }

        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 start
        if (!CollectionUtils.isEmpty(afterSalesVo.getAfterSalesGoodsList())) {
            afterSalesVo.getAfterSalesGoodsList().stream().forEach(saleGood -> {
                skuIds.add(saleGood.getGoodsId());
            });
            List<Map<String, Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);
            Map<String, Map<String, Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key -> key.get("SKU_NO").toString(), v -> v, (k, v) -> v));
            mav.addObject("newSkuInfosMap", newSkuInfosMap);
        }
        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 end
    }

    private String calculateStatus(AfterSalesVo afterSalesVo) {
        List<StatusNode> statusList = new ArrayList<>();
        statusList.add(new StatusNode(AfterSalesStatus.CONFIRM.getStatus(), 0, 1));
        statusList.add(new StatusNode(AfterSalesStatus.VALID.getStatus(), 0, 2));
        statusList.add(new StatusNode(AfterSalesStatus.PROCRESSING.getStatus(), 0, 3));
        statusList.add(new StatusNode(AfterSalesStatus.COMPLETED.getStatus(), 0, 4));
        if (afterSalesVo.getAtferSalesStatus().equals(0)) {
            if (afterSalesVo.getStatus().equals(1)) {
                statusList.get(1).setStatus(2);
//                StatusNode.changeNodeStatus(statusList, 1);
            } else {
                statusList.get(0).setStatus(2);
//                StatusNode.changeNodeStatus(statusList, 0);
            }
        } else if (afterSalesVo.getAtferSalesStatus().equals(1)) {
            statusList.get(2).setStatus(2);
//            StatusNode.changeNodeStatus(statusList, 2);
        } else if (afterSalesVo.getAtferSalesStatus().equals(2)) {
            statusList.get(3).setStatus(2);
//            StatusNode.changeNodeStatus(statusList, 3);
        } else {
            statusList.remove(3);
            statusList.add(new StatusNode(AfterSalesStatus.CLOSED.getStatus(), 2, 5));
//            StatusNode.changeNodeStatus(statusList, 5);
        }

        return JSON.toJSONString(statusList);
    }


    /**
     * 编辑采购单页面（从详情页跳转）
     */
    @ResponseBody
    @RequestMapping(value = "/newEditBuyorderPage")
    @FormToken(save = true)
    public ModelAndView editBuyOrderPage(HttpServletRequest request, Buyorder buyorder, String uri)
            throws IOException {
        ModelAndView mav = new ModelAndView("orderstream/buyorder/new_edit_buyorder");
        User user = getSessionUser(request);
        BuyorderVo bv = buyorderService.getBuyorderVoDetail(buyorder, user);
        boolean isBhOrder = bv.getOrderType() == 1;
        mav.addObject("isBhOrder",isBhOrder);
        riskCheckService.setBuyorderGoodsIsRiskInfo(bv, bv.getBuyorderGoodsVoList());

        // 查询关联的采购虚拟商品集合
        List<BuyorderExpenseItemDto> buyOrderExpenseGoodsList = buyorderExpenseService.getByBuyOrderId(buyorder.getBuyorderId());
        // 查询采购虚拟商品的关联信息
        if (buyOrderExpenseGoodsList.size()>0){
            List<RBuyorderExpenseJSaleorderDto> buyOrderExpenseJSaleList = rBuyorderExpenseJSaleorderService.getRelatedDetail(buyOrderExpenseGoodsList);
            // 获取关联销售订单saleordergoodsIds集合
            List<Integer> detailIds = buyOrderExpenseJSaleList.stream().map(RBuyorderExpenseJSaleorderDto::getSaleorderGoodsId).collect(Collectors.toList());
            // 获取关联销售订单信息
            List<SaleOrderGoodsDetailDto> saleOrderGoodsDetailDtos = saleOrderGoodsService.getRelatedDetail(detailIds);

            List<BuyOrderSaleOrderGoodsDetailDto> esgList = saleOrderGoodsDetailDtos.stream().map(so -> saleOrderGoodsDetailConvertor.toDto(so)).collect(Collectors.toList());

            //计算编辑页面需采数量
            buyorderExpenseApiService.getExpenseBuyNum(esgList,buyOrderExpenseJSaleList,"edit");

            //添加虚拟商品关联订单信息
            int ebuyorderSum = 0;
            int vskucount = 0;
            BigDecimal ebuyorderAmount = BigDecimal.valueOf(0.00);
            for (BuyorderExpenseItemDto buyorderExpenseItemDto : buyOrderExpenseGoodsList) {
                List<BuyOrderSaleOrderGoodsDetailDto> list = new ArrayList<>();
                for (BuyOrderSaleOrderGoodsDetailDto saleOrderGoodsDetailDto : esgList) {
                    if (buyorderExpenseItemDto.getGoodsId().equals(saleOrderGoodsDetailDto.getGoodsId())){
                        list.add(saleOrderGoodsDetailDto);
                        vskucount ++;
                    }
                }
                ebuyorderSum += buyorderExpenseItemDto.getNum();
                BigDecimal price = buyorderExpenseItemDto.getBuyorderExpenseItemDetailDto().getPrice();
                ebuyorderAmount = ebuyorderAmount.add(price.multiply(new BigDecimal(buyorderExpenseItemDto.getNum())));
                buyorderExpenseItemDto.setBuyOrderSaleOrderGoodsDetailDtos(list);
            }
            bv.setEbuyorderAmount(ebuyorderAmount.setScale(2, RoundingMode.HALF_UP));
            bv.setEbuyorderSum(ebuyorderSum);
            mav.addObject("vskucount", vskucount);
            boolean b = buyOrderExpenseGoodsList.stream().anyMatch(o -> o.getBuyOrderSaleOrderGoodsDetailDtos().size() == 0);
            mav.addObject("addVSkuFlag",b);
        }
        bv.setBuyorderExpenseItemDtos(buyOrderExpenseGoodsList);

        // 若存在虚拟商品信息，说明在待采购列表选中了虚拟商品并已经生成了采购费用订单，此时返回 该采购费用订单id
        Integer buyOrderExpenseId = buyOrderExpenseGoodsList.size() > 0 ? buyOrderExpenseGoodsList.get(0).getBuyorderExpenseId() : null;
        mav.addObject("buyOrderExpenseId", buyOrderExpenseId);


        // 封装返利信息
        handleRebateInfo(bv, false, false);
        if (ErpConstant.SUBSIDIARY_TRADER_ID.equals(bv.getTraderId())) {
            BuyorderActualSupplierDto actualSupplierDto = buyorderApiService.getActualSupplierByBuyorderId(bv.getBuyorderId());
            bv.setTraderIdAct(actualSupplierDto.getTraderId());
            bv.setTraderNameAct(actualSupplierDto.getTraderName());
            bv.setTraderContactIdAct(actualSupplierDto.getTraderContactId());
            bv.setTraderContactNameAct(actualSupplierDto.getTraderContactName());
            bv.setTraderContactMobileAct(actualSupplierDto.getTraderContactMobile());
            bv.setTraderContactTelephoneAct(actualSupplierDto.getTraderContactTelephone());
            bv.setPaymentTypeAct(actualSupplierDto.getPaymentType());
            bv.setPrepaidAmountAct(actualSupplierDto.getPrepaidAmount());
            bv.setAccountPeriodAmountAct(actualSupplierDto.getAccountPeriodAmount());
            bv.setRetainageAmountAct(actualSupplierDto.getRetainageAmount());
            bv.setRetainageAmountMonthAct(actualSupplierDto.getRetainageAmountMonth());
            bv.setInvoiceTypeAct(actualSupplierDto.getInvoiceType());
            bv.setNeedInvoice(actualSupplierDto.getNeedInvoice());
            OrderPaymentDetailsDto orderPaymentDetailsDto = buyorderApiService.queryOrderPaymentDetails(bv.getBuyorderId(), ErpConstant.ZERO, ErpConstant.BANK_ACCEPTANCE);
            bv.setBankAcceptance(Objects.nonNull(orderPaymentDetailsDto) ? ErpConstant.ONE : ErpConstant.ZERO);
        } else {
            bv.setPaymentTypeAct(bv.getPaymentType());
            bv.setPrepaidAmountAct(bv.getPrepaidAmount());
            bv.setAccountPeriodAmountAct(bv.getAccountPeriodAmount());
            bv.setRetainageAmountAct(bv.getRetainageAmount());
            bv.setRetainageAmountMonthAct(bv.getRetainageAmountMonth());
            bv.setInvoiceTypeAct(bv.getInvoiceType());
        } 
        mav.addObject("buyorderVo", bv);

        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 start
        if (!CollectionUtils.isEmpty(bv.getBuyorderGoodsVoList())) {
            List<Integer> skuIds = new ArrayList<>();
            bv.getBuyorderGoodsVoList().forEach(buyOrderGood -> {
                // 计算sku在途数量，sku维度
                buyOrderGood.setOnWay(newBuyOrderService.getSkuOnWayBySkuId(buyOrderGood.getGoodsId()));
                skuIds.add(buyOrderGood.getGoodsId());
            });
            List<Map<String, Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);
            Map<String, Map<String, Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key -> key.get("SKU_NO").toString(), v -> v, (k, v) -> v));
            mav.addObject("newSkuInfosMap", newSkuInfosMap);
        }
        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 end

        // 计算采购单的主、子状态，并初始化 StatusNodeList
        String status = NewBuyOrderUtils.calcStatusAndSubStatusOfBuyOrder(bv);
        List<StatusNode> statusNodeList = NewBuyOrderUtils.lightStatusNode(bv, status);
        NewBuyOrderUtils.fillNodeInfo(bv,statusNodeList);
        mav.addObject("statusNodeList", JSON.toJSONString(statusNodeList));

        //判断商品是否已经核价
        if (!CollectionUtils.isEmpty(bv.getBuyorderGoodsVoList())) {

            for (BuyorderGoodsVo buyorderGoodsVo : bv.getBuyorderGoodsVoList()) {

                SkuPriceInfoDetailResponseDto skuPriceInfoDetailResponseDto = basePriceService
                        .findSkuPriceInfoBySkuNo(buyorderGoodsVo.getSku());

                if (skuPriceInfoDetailResponseDto == null || CollectionUtils.isEmpty(skuPriceInfoDetailResponseDto.getPurchaseList())) {
                    buyorderGoodsVo.setAlreadyPrice(0);
                    continue;
                }

                int alreadyPrice = 0;
                for (SkuPriceInfoPurchaseDto purchaseInfo : skuPriceInfoDetailResponseDto.getPurchaseList()) {
                    if (purchaseInfo.getTraderId().intValue() == bv.getTraderId()) {
                        alreadyPrice = 1;
                        buyorderGoodsVo.setCostPirce(purchaseInfo.getPurchasePrice());
                        break;
                    }
                }
                buyorderGoodsVo.setAlreadyPrice(alreadyPrice);

            }
        }

        if (bv.getTraderId() != null && bv.getTraderId() != 0) {
            TraderContactVo traderContactVo = new TraderContactVo();
            traderContactVo.setTraderId(bv.getTraderId());
            traderContactVo.setTraderType(ErpConst.TWO);
            Map<String, Object> map = traderCustomerService.getTraderContactVoList(traderContactVo);
            String tastr = (String) map.get("contact");
            JSONArray json = JSONArray.fromObject(tastr);
            List<TraderContactVo> list = (List<TraderContactVo>) json.toCollection(json, TraderContactVo.class);
            List<TraderAddressVo> taList = (List<TraderAddressVo>) map.get("address");
            mav.addObject("contactList", list);
            mav.addObject("tarderAddressList", taList);
            bv.setCertificateOverdue(traderSupplierService.traderCertificateOverdue(bv.getTraderId()));
        }

        if (bv.getTraderIdAct() != null && bv.getTraderIdAct() != 0) {
            TraderContactVo traderContactVo = new TraderContactVo();
            traderContactVo.setTraderId(bv.getTraderIdAct());
            traderContactVo.setTraderType(ErpConst.TWO);
            Map<String, Object> map = traderCustomerService.getTraderContactVoList(traderContactVo);
            String tastr = (String) map.get("contact");
            JSONArray json = JSONArray.fromObject(tastr);
            List<TraderContactVo> list = (List<TraderContactVo>) json.toCollection(json, TraderContactVo.class);
            List<TraderAddressVo> taList = (List<TraderAddressVo>) map.get("address");
            mav.addObject("contactListAct", list);
            mav.addObject("tarderAddressListAct", taList);
            bv.setCertificateOverdueAct(traderSupplierService.traderCertificateOverdue(bv.getTraderIdAct()));
        }
        // 普发收货地址
        if (bv.getDeliveryDirect() == 0) {
            ParamsConfigValue paramsConfigValue = new ParamsConfigValue();
            paramsConfigValue.setCompanyId(user.getCompanyId());
            paramsConfigValue.setParamsConfigId(ErpConst.TWO);

            // 2018-2-4 查询全部收货地址
            mav.addObject("addressList", buyorderService.getAddressVoList(paramsConfigValue));
        }
        // 付款方式
        List<SysOptionDefinition> paymentTermList = getSysOptionDefinitionList(SysOptionConstant.ID_418);
        mav.addObject("paymentTermList", paymentTermList);

        // 收票种类
        List<SysOptionDefinition> receiptTypes = getSysOptionDefinitionList(SysOptionConstant.ID_428);
        mav.addObject("receiptTypes", receiptTypes);

        // 运费说明
        List<SysOptionDefinition> freightDescriptions = getSysOptionDefinitionList(SysOptionConstant.ID_469);
        mav.addObject("freightDescriptions", freightDescriptions);

        // 物流公司
        List<Logistics> logisticsList = getLogisticsList(user.getCompanyId());
        mav.addObject("logisticsList", logisticsList);
        if (uri == null || "".equals(uri)) {
            uri = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
                    + request.getContextPath() + "/order/newBuyorder/newEditBuyorderPage.do";
        }
        mav.addObject("uri", uri);
        mav.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(bv)));
        return mav;
    }

    /**
     * 生成采购订单（待采购列表页面按钮点击）
     */
    @ResponseBody
    @RequestMapping(value = "/newCreateBuyOrder")
    @SystemControllerLog(operationType = "add", desc = "生成采购订单")
    public ModelAndView saveAddBuyorder(HttpServletRequest request, BuyorderVo buyorderVo) {
        //打印日志
        logger.info("生成采购订单buyorderVo：{}", buyorderVo);
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);

        // VDERP-8571专项发货对应采购单校验
        ResultInfo resultInfo = buyorderService.checkBuyorderSpecial(buyorderVo,null);
        if (resultInfo.getCode() != 0) {
            ModelAndView mav = new ModelAndView("common/fail");
            mav.addObject("message", "专项发货不可混单采购,无法创建采购单");
            return mav;
        }

        //不刷新导致直发和普发错误校验
        if (buyorderVo.getDeliveryDirect()!=null){
            String[] saleorderGoodsIds = buyorderVo.getSaleorderGoodsIds().split(",");
            // 判断当前的销售产品是否有锁定，如果有全部返回失败
            List<Integer> idsListv = new ArrayList<>();
            CollectionUtils.collect(Arrays.asList(saleorderGoodsIds), input -> Integer.valueOf(input.toString().split("\\|")[1]), idsListv);

//            String[] saleorderGoodsIdsv = buyorderVo.getSaleorderGoodsIds().split(",");
//            for (String s : Arrays.asList(saleorderGoodsIdsv)) {
//                for (String s1 : s.split("\\|")) {
//                    idsListv.add(Integer.valueOf(s1));
//                }
//            }

            List<SaleorderGoodsVo> saleorderGoodsVosList = saleorderGoodsMapper.getSaleorderGoodsVosList(idsListv);
            List<Integer> scollect = saleorderGoodsVosList.stream().map(SaleorderGoodsVo::getDeliveryDirect).collect(Collectors.toList());
            if (buyorderVo.getDeliveryDirect().equals(1)){
                if (scollect.contains(0)){
                    ModelAndView mav = new ModelAndView("common/fail");
                    mav.addObject("message", "发货方式已修改,请刷新页面后重试");
                    return mav;
                }
            }else {
                if (scollect.contains(1)){
                    ModelAndView mav = new ModelAndView("common/fail");
                    mav.addObject("message", "发货方式已修改,请刷新页面后重试");
                    return mav;
                }
            }
        }


        // 迁移并重构了DB方法
        Integer buyOrderId = newBuyOrderService.createBuyOrder(buyorderVo, user);
        if (buyOrderId == null) {
            return new ModelAndView("common/fail");
        } else if (buyOrderId == -2) {
            ModelAndView mav = new ModelAndView("common/fail");
            mav.addObject("message", "含有已锁定商品,无法创建采购单");
            return mav;
        } else if (buyOrderId == -3) {
            ModelAndView mav = new ModelAndView("common/fail");
            mav.addObject("message", "同时含有备货单和销售单的商品,无法创建采购单");
            return mav;
        } else if (buyOrderId == -4) {
            ModelAndView mav = new ModelAndView("common/fail");
            mav.addObject("message", "所选商品全部为虚拟商品,无法创建采购单");
            return mav;
        } else {
            ModelAndView mav = new ModelAndView("redirect:/order/newBuyorder/newCreateBuyorderPage.do");
            mav.addObject("buyorderId", buyOrderId);
            mav.addObject("firstSendReceiveFlag", 1);
            return mav;
        }
    }


    /**
     * 新增采购单页面（生成采购单后跳转的页面）
     *
     * @param request 获取当前登陆人
     * @param buyorder 采购单信息
     * @param uri 跳转uri
     * @param firstSendReceiveFlag 用于判断是否是首次编辑采购单
     * @return 新增采购单页面
     * @throws IOException 异常
     */
    @ResponseBody
    @RequestMapping(value = "/newCreateBuyorderPage")
    @FormToken(save = true)
    public ModelAndView addBuyorderPage(HttpServletRequest request, Buyorder buyorder, String uri, Integer firstSendReceiveFlag) throws IOException {
        ModelAndView mav = new ModelAndView();
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        BuyorderVo bv = buyorderService.getAddBuyorderVoDetail(buyorder, user);
        //新增采购单默认已使用返利总额为0
        bv.setUsedTotalRebate(new BigDecimal("0.00"));
        boolean isBhOrder = bv.getOrderType() == 1;
        mav.addObject("isBhOrder",isBhOrder);
        // 查询关联的采购虚拟商品集合
        List<BuyorderExpenseItemDto> buyOrderExpenseGoodsList = buyorderExpenseService.getByBuyOrderId(buyorder.getBuyorderId());
        int ebuyorderSum = 0;
        if (buyOrderExpenseGoodsList.size()>0){
            // 查询采购虚拟商品的关联信息
            List<RBuyorderExpenseJSaleorderDto> buyOrderExpenseJSaleList = rBuyorderExpenseJSaleorderService.getRelatedDetail(buyOrderExpenseGoodsList);
            // 获取关联销售订单saleordergoodsIds集合
            List<Integer> detailIds = buyOrderExpenseJSaleList.stream().map(RBuyorderExpenseJSaleorderDto::getSaleorderGoodsId).collect(Collectors.toList());
            // 获取关联销售订单信息
            List<SaleOrderGoodsDetailDto> saleOrderGoodsDetailDtos = saleOrderGoodsService.getRelatedDetail(detailIds);

            List<BuyOrderSaleOrderGoodsDetailDto> esgList = saleOrderGoodsDetailDtos.stream().map(so -> saleOrderGoodsDetailConvertor.toDto(so)).collect(Collectors.toList());

            //添加销、费用关联主键
            for (BuyOrderSaleOrderGoodsDetailDto saleOrderGoodsDetailDto : esgList) {
                buyOrderExpenseJSaleList.forEach(o->{
                    if(o.getSaleorderGoodsId().equals(saleOrderGoodsDetailDto.getSaleorderGoodsId())){
                        saleOrderGoodsDetailDto.setBuyorderExpenseJSaleorderId(o.getTRBuyorderExpenseJSaleorderId());
                    }
                });
            }
            if (esgList.size()>0){
                buyorderExpenseApiService.getNeedBuyNum(esgList);
            }

            for (BuyorderExpenseItemDto buyorderExpenseItemDto : buyOrderExpenseGoodsList) {
                List<BuyOrderSaleOrderGoodsDetailDto> list = new ArrayList<>();
                int num = 0;
                for (BuyOrderSaleOrderGoodsDetailDto saleOrderGoodsDetailDto : esgList) {
                    if (buyorderExpenseItemDto.getGoodsId().equals(saleOrderGoodsDetailDto.getGoodsId())){
                        list.add(saleOrderGoodsDetailDto);
                        num += saleOrderGoodsDetailDto.getNum();
                    }
                }
                buyorderExpenseItemDto.setNum(num);
                ebuyorderSum += num;
                buyorderExpenseItemDto.setBuyOrderSaleOrderGoodsDetailDtos(list);
            }
        }
        bv.setEbuyorderAmount(BigDecimal.valueOf(0.00));
        bv.setEbuyorderSum(ebuyorderSum);
        bv.setBuyorderExpenseItemDtos(buyOrderExpenseGoodsList);

        Integer buyOrderExpenseId = buyOrderExpenseGoodsList.size() > 0 ? buyOrderExpenseGoodsList.get(0).getBuyorderExpenseId() : null;
        mav.addObject("buyOrderExpenseId", buyOrderExpenseId);

        // 计算sku在途数量，sku维度
        bv.getBuyorderGoodsVoList().forEach(buyorderGoodsVo -> {
            buyorderGoodsVo.setOnWay(newBuyOrderService.getSkuOnWayBySkuId(buyorderGoodsVo.getGoodsId()));
        });
        //判断商品是否均为赠品
        boolean b = bv.getBuyorderGoodsVoList().stream().allMatch(buyorderGoodsVo -> buyorderGoodsVo.getIsGift() == 1);
        mav.addObject("bgAllGift",b);
        // OrderPaymentDetailsDto orderPaymentDetailsDto = buyorderApiService.queryOrderPaymentDetails(bv.getBuyorderId(), ErpConstant.ZERO, ErpConstant.BANK_ACCEPTANCE);
        // bv.setBankAcceptance(Objects.nonNull(orderPaymentDetailsDto) ? ErpConstant.ONE : ErpConstant.ZERO);
        bv.setBankAcceptance(ErpConst.ONE);
        mav.addObject("buyorderVo", bv);
        if (ObjectUtils.notEmpty(bv.getTraderId())) {
            TraderContactVo traderContactVo = new TraderContactVo();
            traderContactVo.setTraderId(bv.getTraderId());
            traderContactVo.setTraderType(ErpConst.TWO);
            Map<String, Object> map = traderCustomerService.getTraderContactVoList(traderContactVo);
            String tastr = (String) map.get("contact");
            JSONArray json = JSONArray.fromObject(tastr);
            List<TraderContactVo> list = (List<TraderContactVo>) json.toCollection(json, TraderContactVo.class);
            List<TraderAddressVo> taList = (List<TraderAddressVo>) map.get("address");
            mav.addObject("contactList", list);
            mav.addObject("tarderAddressList", taList);
        }
        // 普发收货地址
        if (bv.getDeliveryDirect() == 0) {
            ParamsConfigValue paramsConfigValue = new ParamsConfigValue();
            paramsConfigValue.setCompanyId(user.getCompanyId());
            paramsConfigValue.setParamsConfigId(ErpConst.TWO);

            // 2018-2-4 查询全部收货地址
            mav.addObject("addressList", buyorderService.getAddressVoList(paramsConfigValue));
        }
        // 付款方式
        List<SysOptionDefinition> paymentTermList = getSysOptionDefinitionList(SysOptionConstant.ID_418);
        mav.addObject("paymentTermList", paymentTermList);

        // 收票种类
        List<SysOptionDefinition> receiptTypes = getSysOptionDefinitionList(SysOptionConstant.ID_428);
        mav.addObject("receiptTypes", receiptTypes);

        // 运费说明
        List<SysOptionDefinition> freightDescriptions = getSysOptionDefinitionList(SysOptionConstant.ID_469);
        mav.addObject("freightDescriptions", freightDescriptions);

        // 物流公司
        List<Logistics> logisticsList = getLogisticsList(user.getCompanyId());
        mav.addObject("logisticsList", logisticsList);
        if (uri == null || "".equals(uri)) {
            uri = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
                    + request.getContextPath() + "/order/newBuyorder/newCreateBuyorderPage.do";
        }
        mav.addObject("uri", uri);


        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 start
        if (!CollectionUtils.isEmpty(bv.getBuyorderGoodsVoList())) {
            List<Integer> skuIds = new ArrayList<>();
            bv.getBuyorderGoodsVoList().forEach(buyOrder -> {
                skuIds.add(buyOrder.getGoodsId());
            });
            List<Map<String, Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);
            Map<String, Map<String, Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key -> key.get("SKU_NO").toString(), v -> v, (k, v) -> v));
            mav.addObject("newSkuInfosMap", newSkuInfosMap);
        }
        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 end

        mav.setViewName("orderstream/buyorder/new_add_buyorder");
        mav.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(bv)));

        // RECORD.V01 VDERP-5951 采销节点信息互通】采购单支持填写预计发货时间和到货时间并通知销售
        // CAUSE.V01 参数firstSendReceiveFlag来源于待采购订单页面跳转附加，用于判断是否是首次编辑采购单
        if (Integer.valueOf(1).equals(firstSendReceiveFlag)) {
            mav.addObject("firstSendReceiveFlag", 1);
        }

        // needBuyNum为空则跳转错误页面
        if (isBuyorderVoHaveNeedBuyNumEqualsZero(bv)) {
            mav.setViewName("common/fail");
            mav.addObject("message", "已选列表存在采购单，请刷新待采购列表重试");
        }
        return mav;
    }


    private boolean isBuyorderVoHaveNeedBuyNumEqualsZero(BuyorderVo bv) {
        if (CollectionUtils.isEmpty(bv.getBuyorderGoodsVoList())) {
            return true;
        }
        for (BuyorderGoodsVo buyorderGoodsVo : bv.getBuyorderGoodsVoList()) {
            if (CollectionUtils.isEmpty(buyorderGoodsVo.getSaleorderGoodsVoList())) {
                return true;
            } else {
                return buyorderGoodsVo.getSaleorderGoodsVoList().stream().anyMatch(
                        saleorderGoodsVo -> isSaleorderGoodsVoHaveNeedBuyNumEqualsZero(saleorderGoodsVo, bv)
                );
            }
        }
        return true;
    }

    private boolean isSaleorderGoodsVoHaveNeedBuyNumEqualsZero(SaleorderGoodsVo saleorderGoodsVo, BuyorderVo bv) {
        if (ErpConst.ZERO.equals(bv.getDeliveryDirect())) {//普发销售单NeedBuyNum = NeedBuyNum
            return saleorderGoodsVo.getNeedBuyNum() == null || ErpConst.ZERO.equals(saleorderGoodsVo.getNeedBuyNum());
        } else if (ErpConst.ONE.equals(bv.getDeliveryDirect())) {//直发销售单NeedBuyNum = Num-BuyNum
            return   ErpConst.ZERO.equals(saleorderGoodsVo.getNeedBuyNum().intValue());
        }
        return true;
    }


    /**
     * 添加运费
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/addFreightPage")
    public ModelAndView addFreightPage(HttpServletRequest request, BuyorderGoodsVo buyorderGoodsVo) throws IOException {
        ModelAndView mav = new ModelAndView("order/buyorder/add_freight");
        buyorderGoodsVo = buyorderService.getFreightByBuyorderId(buyorderGoodsVo);
        mav.addObject("bgv", buyorderGoodsVo);
        mav.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(buyorderGoodsVo)));
        return mav;
    }

    /**
     * 新增或编辑采购单页面确定按钮
     *
     * @param request  HttpServletRequest
     * @param buyorder 采购单信息
     * @return ModelAndView
     */
    @ResponseBody
    @RequestMapping(value = "newSaveCreateOrEditBuyorder")
    @SystemControllerLog(operationType = "edit", desc = "新增/编辑采购页面提交")
    @FormToken(remove = true)
    public ModelAndView saveEditBuyorder(HttpServletRequest request, Buyorder buyorder) {
        log.info("/order/newBuyorder/newSaveCreateOrEditBuyorder --> " + buyorder.toString());
        //新增、编辑需采数量校验
        // 查询关联的采购虚拟商品集合
        List<BuyorderExpenseItemDto> buyOrderExpenseGoodsList = buyorderExpenseService.getByBuyOrderId(buyorder.getBuyorderId());
        // 查询采购虚拟商品的关联信息
        if (buyOrderExpenseGoodsList.size()>0) {
            List<RBuyorderExpenseJSaleorderDto> buyOrderExpenseJSaleList = rBuyorderExpenseJSaleorderService.getRelatedDetail(buyOrderExpenseGoodsList);
            // 获取关联销售订单saleordergoodsIds集合
            List<Integer> detailIds = buyOrderExpenseJSaleList.stream().map(RBuyorderExpenseJSaleorderDto::getSaleorderGoodsId).collect(Collectors.toList());
            // 获取关联销售订单信息
            List<SaleOrderGoodsDetailDto> saleOrderGoodsDetailDtos = saleOrderGoodsService.getRelatedDetail(detailIds);

            List<BuyOrderSaleOrderGoodsDetailDto> esgList = saleOrderGoodsDetailDtos.stream().map(so -> saleOrderGoodsDetailConvertor.toDto(so)).collect(Collectors.toList());

            //计算需采数量
            buyorderExpenseApiService.getExpenseBuyNum(esgList,buyOrderExpenseJSaleList,"edit");
            boolean buyNumCheck = false;
            for (BuyorderExpenseItemDto buyorderExpenseItemDto : buyorder.getBuyorderExpenseItemDtos()) {
                List<BuyOrderSaleOrderGoodsDetailDto> buyOrderSaleOrderGoodsDetailDtos = buyorderExpenseItemDto.getBuyOrderSaleOrderGoodsDetailDtos();
                if (!buyNumCheck && buyOrderSaleOrderGoodsDetailDtos != null) {
                    for (BuyOrderSaleOrderGoodsDetailDto detailDto : buyOrderSaleOrderGoodsDetailDtos) {
                        if (!buyNumCheck) {
                            buyNumCheck = esgList.stream().anyMatch(dto -> detailDto.getBuyorderExpenseJSaleorderId().equals(dto.getBuyorderExpenseJSaleorderId()) && detailDto.getBuyNum() > dto.getBuyNum());
                        }
                    }
                }
            }
            if(buyNumCheck){
                ModelAndView mav = new ModelAndView("common/fail");
                mav.addObject("message", "采购数量大于需采数量！");
                return mav;
            }
        }

        ResultInfo<?> res = buyorderService.saveEditBuyorderAndBuyorderGoods(buyorder, request);
        if (res != null && res.getCode() == 0) {
            ModelAndView mav = new ModelAndView("redirect:/order/newBuyorder/newViewBuyOrderDetail.do");
            mav.addObject("buyorderId", buyorder.getBuyorderId());
            return mav;
        } else {
            ModelAndView mav = new ModelAndView("common/fail");
            mav.addObject("message", res == null ? "" : res.getMessage());
            return mav;
        }
    }

    /**
     * .添加直发售后出库记录页面
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/11/18 16:53.
     * @author: Randy.Xu.
     * @param request
     * @param afterSalesVo
     * @return: org.springframework.web.servlet.ModelAndView.
     * @throws:  .
     */
    @ResponseBody
    @RequestMapping(value = "/addDirectAfterSaleOutLogPage")
    public ModelAndView addAfterSalesRecordPage(HttpServletRequest request, AfterSalesVo afterSalesVo) {
        ModelAndView mav = new ModelAndView("orderstream/buyorder/add_afterSales_record");
        List<AfterSalesGoodsVo> afterSalesGoodsVoList = afterSalesOrderService.getDirectBuyorderAfterSaleGoods(afterSalesVo);

        List<Integer> skuIds = new ArrayList<>();

        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 start
        if (!CollectionUtils.isEmpty(afterSalesGoodsVoList)) {
            afterSalesGoodsVoList.stream().forEach(saleGood -> {
                skuIds.add(saleGood.getGoodsId());
            });
            List<Map<String, Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);
            Map<String, Map<String, Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key -> key.get("SKU_NO").toString(), v -> v, (k, v) -> v));
            mav.addObject("newSkuInfosMap", newSkuInfosMap);
        }
        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 end
        mav.addObject("afterSalesGoodsVoList", afterSalesGoodsVoList);
        return mav;
    }


    /**
     * 保存直发售后出库记录
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/11/18 16:53.
     * @author: Randy.Xu.
     * @param request
     * @param afterSaleBuyorderDirectOutLogList
     * @return: com.vedeng.common.model.ResultInfo.
     * @throws:  .
     */
    @ResponseBody
    @RequestMapping(value = "/saveDirectAfterSaleOutLog")
    @Transactional(rollbackFor = Exception.class)
    public ResultInfo addAfterSalesRecordPage(HttpServletRequest request, @RequestBody List<AfterSaleBuyorderDirectOutLog> afterSaleBuyorderDirectOutLogList) {
        ResultInfo resultInfo = afterSalesOrderService.saveDirectAfterSaleOutLog(afterSaleBuyorderDirectOutLogList);
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        warehouseGoodsOutService.saveDirectOutInLog(user,afterSaleBuyorderDirectOutLogList);
        return resultInfo;
    }

    /**
     * 编辑的直发售后出库记录
     */
    @ResponseBody
    @RequestMapping(value = "/editDirectAfterSaleOutLogPage")
    public ModelAndView editDirectAfterSaleOutLogPage(HttpServletRequest request, AfterSaleBuyorderDirectOutLog afterSaleBuyorderDirectOutLog) {
        ModelAndView mav = new ModelAndView("orderstream/buyorder/edit_afterSales_record");
        AfterSaleBuyorderDirectOutLog afterSaleBuyorderDirectOutLogResult = afterSalesOrderService.getAfterSaleBuyorderDirectOutLog(afterSaleBuyorderDirectOutLog);
        if (afterSaleBuyorderDirectOutLogResult.getProduceTime() != null) {
            afterSaleBuyorderDirectOutLogResult.setProduceTimeStr(DateUtil.convertString(afterSaleBuyorderDirectOutLogResult.getProduceTime(), DateUtil.TIME_FORMAT));
        }
        if (afterSaleBuyorderDirectOutLogResult.getValidTime() != null) {
            afterSaleBuyorderDirectOutLogResult.setValidTimeStr(DateUtil.convertString(afterSaleBuyorderDirectOutLogResult.getValidTime(), DateUtil.TIME_FORMAT));
        }
        if (afterSaleBuyorderDirectOutLogResult.getOutTime() != null) {
            afterSaleBuyorderDirectOutLogResult.setOutTimeStr(DateUtil.convertString(afterSaleBuyorderDirectOutLogResult.getOutTime(), DateUtil.TIME_FORMAT));
        }
        mav.addObject("arg", afterSaleBuyorderDirectOutLogResult);
        return mav;
    }

    /**
     * 保存编辑的直发售后出库记录
     */
    @ResponseBody
    @RequestMapping(value = "/saveEditDirectAfterSaleOutLog", produces = "application/json;charset=UTF-8")
    public ResultInfo saveEditDirectAfterSaleOutLog(HttpServletRequest request, @RequestBody AfterSaleBuyorderDirectOutLog afterSaleBuyorderDirectOutLog) {

        if (StringUtils.isNotBlank(afterSaleBuyorderDirectOutLog.getProduceTimeStr())) {
            afterSaleBuyorderDirectOutLog.setProduceTime(DateUtil.convertLong(afterSaleBuyorderDirectOutLog.getProduceTimeStr(), DateUtil.TIME_FORMAT));
        }
        if (StringUtils.isNotBlank(afterSaleBuyorderDirectOutLog.getOutTimeStr())) {
            afterSaleBuyorderDirectOutLog.setOutTime(DateUtil.convertLong(afterSaleBuyorderDirectOutLog.getOutTimeStr(), DateUtil.TIME_FORMAT));
        }
        if (StringUtils.isNotBlank(afterSaleBuyorderDirectOutLog.getValidTimeStr())) {
            afterSaleBuyorderDirectOutLog.setValidTime(DateUtil.convertLong(afterSaleBuyorderDirectOutLog.getValidTimeStr(), DateUtil.TIME_FORMAT));
        }
        ResultInfo resultInfo = afterSalesOrderService.saveEditDirectAfterSaleOutLog(afterSaleBuyorderDirectOutLog);
        return resultInfo;
    }

    /**
     * 删除编辑的直发售后出库记录
     */
    @ResponseBody
    @RequestMapping(value = "/deleteDirectAfterSaleOutLog")
    public ResultInfo deleteDirectAfterSaleOutLog(HttpServletRequest request, AfterSaleBuyorderDirectOutLog afterSaleBuyorderDirectOutLog) {

        ResultInfo resultInfo = afterSalesOrderService.deleteDirectAfterSaleOutLog(afterSaleBuyorderDirectOutLog);
        return resultInfo;
    }

    /**
     * 保存新增售后
     */
    @ResponseBody
    @RequestMapping(value = "/saveAddAfterSales")
    @SystemControllerLog(operationType = "add", desc = "保存新增售后")
    public ModelAndView saveAddAfterSales(HttpServletRequest request, AfterSalesVo afterSalesVo,
                                          @RequestParam(required = false, value = "afterSaleNums") String[] afterSaleNums,
                                          @RequestParam(required = false, value = "fileName") String[] fileName,
                                          @RequestParam(required = false, value = "fileUri") String[] fileUri,
                                          @RequestParam(required = false, value = "invoiceIds") String[] invoiceIds) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ModelAndView mav = new ModelAndView();

        Buyorder buyorder = buyorderService.getBuyOrderByOrderId(afterSalesVo.getOrderId());

        // VDERP-10681 直发采购售后校验
        if (buyorder.getStatus() == 0 || buyorder.getStatus() == 3 || buyorder.getLockedStatus() == 1) {
            mav.addObject("message","采购单已锁定或已关闭,无法提交售后,请刷新页面!");
            return fail(mav);
        }

        //采购单退货且普发
        if (afterSalesVo.getType() == 546 && buyorder.getDeliveryDirect() == 0) {
            logger.info("新增退货单,请求WMS关闭原始的采购单" + buyorder.getBuyorderNo());
            try {
                if (!cancelTypeService.cancelInputPurchaseMethod(afterSalesVo.getOrderNo(),"采购新增售后关闭采购单")) {
                    mav.addObject("message", "采购单正在物流作业,无法提交售后,请稍后重试!");
                    return fail(mav);
                }
            } catch (Exception e) {
                logger.error("BuyorderController -> saveAddAfterSales error: ", e);
                return fail(mav);
            }
        }
        String[] receiveGoodsTimeStrings = request.getParameterValues("receiveGoodsTimeStr");
        logger.info("receiveGoodsTimeStrings:{}",receiveGoodsTimeStrings);
        afterSalesVo.setAfterSalesNum(afterSaleNums);
        afterSalesVo.setAttachName(fileName);
        afterSalesVo.setAttachUri(fileUri);
        afterSalesVo.setSubjectType(536);// 采购
        afterSalesVo.setAtferSalesStatus(0);
        afterSalesVo.setServiceUserId(user.getUserId());
        afterSalesVo.setStatus(0);
        afterSalesVo.setValidStatus(0);
        afterSalesVo.setDomain(domain);
        afterSalesVo.setInvoiceIds(invoiceIds);
        afterSalesVo.setCompanyId(user.getCompanyId());
        afterSalesVo.setPayee(user.getCompanyName());
        afterSalesVo.setTraderType(2);

        ResultInfo<?> res = afterSalesOrderService.saveAddAfterSales(afterSalesVo, user);

        if (res.getCode() == 0) {
            if(afterSalesVo.getType() == 548) {
                mav.addObject("url", "/after/newBuyorder/afterBuyorderTpDetail.do?afterSalesId=" + res.getData());
            }else {
                mav.addObject("url", "./viewAfterSalesDetail.do?traderType=2&afterSalesId=" + res.getData());
            }
            return success(mav);
        } else {
            return fail(mav);
        }
    }

    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/uploadInboundAttachments")
    @ExcludeAuthorization
    public ModelAndView uploadInboundAttachments(HttpServletRequest request, HttpSession session, Integer buyorderId, String altType) {
        ModelAndView mv = new ModelAndView();
        mv.addObject("buyorderId", buyorderId);
        mv.setViewName("orderstream/buyorder/upload_inbound_attachments");
        return mv;
    }

    @FormToken(remove = true)
    @RequestMapping("/saveInboundAttachments")
    @ResponseBody
    @NoNeedAccessAuthorization
    public ResultInfo<?> saveInboundAttachments(HttpServletRequest request, Attachment attachment, @RequestParam(required = false, value = "altType") Integer altType) {
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        if (attachment != null && (attachment.getUri().contains("jpg") || attachment.getUri().contains("png")
                || attachment.getUri().contains("gif") || attachment.getUri().contains("bmp"))) {
            attachment.setAttachmentType(SysOptionConstant.ID_460);
        } else {
            attachment.setAttachmentType(SysOptionConstant.ID_461);
        }

        if (user != null) {
            attachment.setCreator(user.getUserId());
            attachment.setAddTime(DateUtil.sysTimeMillis());
        }
        if (altType != null && altType == 1) {
            attachment.setAttachmentFunction(SysOptionConstant.ID_4330);
            attachment.setAlt("采购普发同行单");
        } else if (altType != null && altType == 0) {
            attachment.setAttachmentFunction(SysOptionConstant.ID_4331);
            attachment.setAlt("质检报告");
        }
        return saleorderService.saveSaleorderAttachment(attachment);
    }






    /**
     * VDERP-10681 采购退货校验
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/validDirectAfterSales")
    @NoNeedAccessAuthorization
    public ResultInfo validDirectSku(Integer type, String afterSaleNums) {

        List<String> afterSalesInfo = JSONObject.parseArray(afterSaleNums, String.class);
        if (type == 546) {
            for (String afterSaleNum : afterSalesInfo) {
                String[] info = afterSaleNum.split("\\|");
                if ("1".equals(info[2])) {
                    // step1 根据buyorderGoodsId查询对应的saleorderGoods
                    boolean res = buyorderService.validDirectAfterSales(Integer.valueOf(info[0]), Integer.valueOf(info[3]));
                    if (!res) {
                        return new ResultInfo(-1, "该直发退货商品已收货，无法直接提交采购售后，请先申请销售售后");
                    }
                }
            }
        }
        return new ResultInfo(0, "校验通过");
    }



    /**
     * 采购单申请修改页面
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "newApplyEditBuyOrder")
    public ModelAndView modifyApplyInit(HttpServletRequest request, Buyorder buyorder) {
        ModelAndView mv = new ModelAndView("orderstream/buyorder/new_apply_edit_buyorder");
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        buyorder.setCompanyId(user.getCompanyId());
        BuyorderVo bv = buyorderService.getBuyorderVoApplyUpdateDetail(buyorder);

        // 先校验是是否已锁定，防止页面未刷新时依然有按钮导致脏数据
        if (bv.getLockedStatus() == 1 || bv.getStatus() != 1) {
            mv.addObject("message", "当前采购单已锁定或已完结、已关闭，请刷新页面！");
            return fail(mv);
        }

        // 获取销售单的收货信息，用于普发改直发时 填充收货信息
        newBuyOrderService.getDirectDeliveryReceiveInfo(bv);

        // 查询直属费用单的商品信息集合
        List<BuyorderExpenseItemDto> buyOrderExpenseGoodsList = buyorderExpenseService.getByBuyOrderId(buyorder.getBuyorderId());
        mv.addObject("buyOrderExpenseGoodsList", buyOrderExpenseGoodsList);
        // 封装返利信息
        handleRebateInfo(bv, true,false);
        mv.addObject("bv", bv);

        // 普发收货地址
        ParamsConfigValue paramsConfigValue = new ParamsConfigValue();
        paramsConfigValue.setCompanyId(user.getCompanyId());
        paramsConfigValue.setParamsConfigId(ErpConst.TWO);
        mv.addObject("addressList", buyorderService.getAddressVoList(paramsConfigValue));

        // 获取公司信息
        Company company = companyService.getCompanyByCompangId(user.getCompanyId());
        mv.addObject("companyName", company.getCompanyName());

        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 start
        if (!CollectionUtils.isEmpty(bv.getBgvList())) {

            List<Integer> skuIds = new ArrayList<>();

            bv.getBgvList().forEach(buyOrderGoods -> {
                skuIds.add(buyOrderGoods.getGoodsId());
            });

            List<Map<String, Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);
            Map<String, Map<String, Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key -> key.get("SKU_NO").toString(), v -> v, (k, v) -> v));
            mv.addObject("newSkuInfosMap", newSkuInfosMap);
        }
        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 end

        // 收票种类
        List<SysOptionDefinition> receiptTypes = getSysOptionDefinitionList(SysOptionConstant.ID_428);
        mv.addObject("receiptTypes", receiptTypes);
        return mv;
    }

    private void handleRebateInfo(BuyorderVo bv, boolean isModify, boolean isView) {
        Integer traderId = bv.getTraderId();
        TraderSupplierDto traderSupplierDto = traderSupplierApiService.getTraderSupplierByTraderId(traderId);
        if (Objects.nonNull(traderSupplierDto)) {
            SupplierAssetApiDto supplierAsset = supplierAssetApiService.getSupplierAsset(traderSupplierDto.getTraderSupplierId(), SupplierAssetEnum.rebate.getCode());
            bv.setValidRebateCharge(Objects.nonNull(supplierAsset) ? supplierAsset.getApplyAsset() : BigDecimal.ZERO);
        }
        // 封装返利信息
        List<BuyorderGoodsVo> buyorderGoodsVoList = isModify ? bv.getBgvList() : bv.getBuyorderGoodsVoList();
        for (BuyorderGoodsVo buyorderGoodsVo : buyorderGoodsVoList) {
            BigDecimal rebatePrice = buyorderGoodsVo.getRebatePrice();
            BigDecimal rebateAmount = buyorderGoodsVo.getRebateAmount();
            //给详情页重新计算返利总额，考虑售后场景
            if (isView){
                BigDecimal afterSaleItemRebateAmount = buyorderGoodsMapper.getAfterSaleItemRebateAmount(buyorderGoodsVo.getBuyorderGoodsId(), BusinessSourceTypeEnum.buyOrderAfterSale.getCode());
                rebateAmount = rebateAmount.subtract(null == afterSaleItemRebateAmount ? BigDecimal.ZERO : afterSaleItemRebateAmount);
                buyorderGoodsVo.setRebateAmount(rebateAmount);
            }
            if (Objects.nonNull(rebatePrice) && Objects.nonNull(rebateAmount) &&
                    rebatePrice.compareTo(BigDecimal.ZERO) > 0 && rebateAmount.compareTo(BigDecimal.ZERO) > 0) {
                buyorderGoodsVo.setRebateAfterPrice(buyorderGoodsVo.getPrice().subtract(rebatePrice).setScale(2, RoundingMode.HALF_UP));
                buyorderGoodsVo.setRebateNum(BigDecimal.valueOf(buyorderGoodsVo.getNum()));
            }else {
                buyorderGoodsVo.setRebateAfterPrice(buyorderGoodsVo.getPrice());
            }
        }
        bv.setUsedTotalRebate(buyorderGoodsVoList.stream()
                .filter(Objects::nonNull)
                .map(BuyorderGoodsVo::getRebateAmount)
                .filter(amount -> Objects.nonNull(amount) && amount.compareTo(BigDecimal.ZERO) > 0)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
    }

    /**
     * 新增沟通记录
     */
    @FormToken(save = true)
    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping(value = "addCommunicateRecord")
    public ModelAndView addCommunicatePage(Buyorder buyorder, TraderSupplier traderSupplier, String flag,
                                           HttpServletRequest request) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ModelAndView mav = new ModelAndView("orderstream/buyorder/add_communicate_record");
        TraderContact traderContact = new TraderContact();
        // 联系人
        traderContact.setTraderId(traderSupplier.getTraderId());
        traderContact.setIsEnable(ErpConst.ONE);
        traderContact.setTraderType(ErpConst.TWO);
        List<TraderContact> contactList = traderCustomerService.getTraderContact(traderContact);
        // 客户标签
        Tag tag = new Tag();
        tag.setTagType(SysOptionConstant.ID_32);
        tag.setIsRecommend(ErpConst.ONE);
        tag.setCompanyId(user.getCompanyId());

        Integer pageNo = 1;
        Integer pageSize = 10;

        Page page = getPageTag(request, pageNo, pageSize);
        Map<String, Object> tagMap = tagService.getTagListPage(tag, page);

        mav.addObject("traderSupplier", traderSupplier);
        mav.addObject("buyorder", buyorder);
        mav.addObject("contactList", contactList);

        CommunicateRecord communicate = new CommunicateRecord();
        communicate.setBegintime(DateUtil.sysTimeMillis());
        communicate.setEndtime(DateUtil.sysTimeMillis() + 2 * 60 * 1000);
        mav.addObject("communicateRecord", communicate);

        // 沟通方式
        List<SysOptionDefinition> communicateList = getSysOptionDefinitionList(SysOptionConstant.ID_23);
        mav.addObject("communicateList", communicateList);

        mav.addObject("tagList", (List<Tag>) tagMap.get("list"));
        mav.addObject("page", (Page) tagMap.get("page"));
        mav.addObject("flag", flag);
        return mav;
    }


    /**
     * 编辑沟通记录
     */
    @ResponseBody
    @RequestMapping(value = "/editCommunicateRecord")
    public ModelAndView editCommunicateRecord(CommunicateRecord communicateRecord, TraderSupplier traderSupplier, String flag,
                                              Buyorder buyorder, HttpServletRequest request, HttpSession session) throws IOException {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ModelAndView mv = new ModelAndView("orderstream/buyorder/edit_communicate_record");
        CommunicateRecord communicate = traderCustomerService.getCommunicate(communicateRecord);
        communicate.setTraderSupplierId(communicateRecord.getTraderSupplierId());
        communicate.setTraderId(communicateRecord.getTraderId());
        communicate.setBuyorderId(communicateRecord.getBuyorderId());
        TraderContact traderContact = new TraderContact();
        // 联系人
        traderContact.setTraderId(communicateRecord.getTraderId());
        traderContact.setIsEnable(ErpConst.ONE);
        traderContact.setTraderType(ErpConst.TWO);
        List<TraderContact> contactList = traderCustomerService.getTraderContact(traderContact);

        // 沟通方式
        List<SysOptionDefinition> communicateList = getSysOptionDefinitionList(SysOptionConstant.ID_23);
        mv.addObject("communicateList", communicateList);

        // 客户标签
        Tag tag = new Tag();
        tag.setTagType(SysOptionConstant.ID_32);
        tag.setIsRecommend(ErpConst.ONE);
        tag.setCompanyId(user.getCompanyId());

        Integer pageNo = 1;
        Integer pageSize = 10;

        Page page = getPageTag(request, pageNo, pageSize);
        Map<String, Object> tagMap = tagService.getTagListPage(tag, page);

        mv.addObject("communicateRecord", communicate);

        mv.addObject("contactList", contactList);

        mv.addObject("tagList", (List<Tag>) tagMap.get("list"));
        mv.addObject("page", (Page) tagMap.get("page"));
        mv.addObject("method", "communicaterecord");
        mv.addObject("traderSupplier", traderSupplier);
        mv.addObject("buyorder", buyorder);
        mv.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(communicate)));
        mv.addObject("flag", flag);

        if(org.apache.commons.lang.StringUtils.isNotBlank(communicate.getCoidUri())){
            String voiceStatusGptSuccess = "9";//Gpt解析成功
            CommunicateVoiceTaskDto taskDto = communicateVoiceTaskApi.selectByCommunicateRecordIdAndSence(communicate.getCommunicateRecordId(),
                    AiConstant.getEnumByCommunicateType(communicate.getCommunicateType()).getCode());

            mv.addObject("communicateTypeName",AiConstant.getEnumByCommunicateType(communicate.getCommunicateType()).getName());

            if(taskDto != null && voiceStatusGptSuccess.equals(taskDto.getVoiceStatus()) ){
                List<VoiceFieldResultDto> voiceFieldList = communicateVoiceTaskApi.selectVoiceResultByCommunicateRecordIdAndSenceGroup(
                        communicate.getCommunicateRecordId(), AiConstant.getEnumByCommunicateType(communicate.getCommunicateType()).getCode(),
                        AiConstant.CODE_GROUP_SUMMARY);
                mv.addObject("voiceFieldList", voiceFieldList);

                List<VoiceFieldResultDto> voiceToDoFieldList = communicateVoiceTaskApi.selectVoiceResultByCommunicateRecordIdAndSenceGroup(
                        communicate.getCommunicateRecordId(), AiConstant.getEnumByCommunicateType(communicate.getCommunicateType()).getCode(),
                        AiConstant.CODE_GROUP_TODOTASK);
                mv.addObject("voiceToDoFieldList", voiceToDoFieldList);
            }else{
                String voiceStatusIgnore = "-1";//忽略的状态
                if(taskDto == null){
                    //如果解析任务不存在，即历史数据或忽略
                }else if(voiceStatusIgnore.equals(taskDto.getVoiceStatus())){
                    //忽略，则不做任务数据展示
                } else{  //非以上情况，即如果解析任务存在，但是在解析中，则展示AI解析中...
                    List<VoiceFieldResultDto> voiceFieldList = new ArrayList<>();
                    VoiceFieldResultDto tipVoiceField = new VoiceFieldResultDto();
                    tipVoiceField.setFieldName("提示");
                    tipVoiceField.setFieldResult("AI解析中...");
                    voiceFieldList.add(tipVoiceField);
                    mv.addObject("voiceFieldList", voiceFieldList);

                    List<VoiceFieldResultDto> voiceToDoFieldList = new ArrayList<>();
                    VoiceFieldResultDto tipVoiceToDoField = new VoiceFieldResultDto();
                    tipVoiceToDoField.setFieldName("提示");
                    tipVoiceToDoField.setFieldResult("AI解析中...");
                    voiceToDoFieldList.add(tipVoiceToDoField);
                    mv.addObject("voiceToDoFieldList", voiceToDoFieldList);
                }
            }
        }
        return mv;
    }


    /**
     * 保存“采购单申请修改”
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/saveBuyorderEditApply")
    @SystemControllerLog(operationType = "edit", desc = "保存采购申请修改")
    public ModelAndView saveBuyorderApplyUpdate(HttpServletRequest request, BuyorderModifyApply buyorderModifyApply) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ModelAndView mav = new ModelAndView();
        try {
            buyorderModifyApply.setCompanyId(ErpConst.NJ_COMPANY_ID);
            buyorderModifyApply.setAddTime(DateUtil.sysTimeMillis());
            buyorderModifyApply.setCreator(user.getUserId());
            // 订单流 - 新修改单标识
            buyorderModifyApply.setIsNew(1);

            // 先校验 普发且修改了物流备注
            if (buyorderModifyApply.getOldDeliveryDirect() == 0 && buyorderModifyApply.getDeliveryDirect() == 0 &&
                    !buyorderModifyApply.getLogisticsComments().equals(buyorderModifyApply.getOldLogisticsComments())) {
                // 调取wms取消 采购入库
                boolean cancelBuyorderOut = cancelTypeService.cancelInputPurchaseMethod(buyorderMapper.getBuyorderVoById(buyorderModifyApply.getBuyorderId()).getBuyorderNo(), "采购订单修改撤销");
                buyorderModifyApply.setSynWmsCancel(cancelBuyorderOut ? 1 : 0);
                // 取消失败，直接返回，并不插入采购修改申请
                if (!cancelBuyorderOut) {
                    mav.addObject("message", "普发采购单已经作业，不支持修改物流备注");
                    return fail(mav);
                }
            } else if (buyorderModifyApply.getOldDeliveryDirect() == 0 && buyorderModifyApply.getDeliveryDirect() == 1) {
                // 普改直
                // 先校验销售单中该sku未发货，根据saleorder goods的DELIVERY_NUM判断
                Boolean isDeliveryAlready = newBuyOrderModifyApplyService.checkSaleorderGoodsDeliveryStatus(buyorderModifyApply.getBuyorderModifyApplyGoodsList());
                if (!isDeliveryAlready) {
                    mav.addObject("message", "销售单内部分商品已发货，不允许修改发货方式");
                    return fail(mav);
                } else {
                    // 调取wms取消 采购入库
                    boolean cancelBuyorderOut = cancelTypeService.cancelInputPurchaseMethod(buyorderMapper.getBuyorderVoById(buyorderModifyApply.getBuyorderId()).getBuyorderNo(), "采购订单修改撤销");
                    buyorderModifyApply.setSynWmsCancel(cancelBuyorderOut ? 1 : 0);
                    // 取消失败，直接返回，并不插入采购修改申请
                    if (!cancelBuyorderOut) {
                        mav.addObject("message", "普发采购单已经作业，不支持修改物流备注");
                        return fail(mav);
                    }
                }
            } else if (buyorderModifyApply.getOldDeliveryDirect() == 1 && buyorderModifyApply.getDeliveryDirect() == 0) { // 直->普 默认下发
                buyorderModifyApply.setSynWmsCancel(1);
            }

            // 校验通过，再生成采购单修改申请
            Integer buyorderModifyApplyId = newBuyOrderModifyApplyService.saveBuyOrderEditApply(buyorderModifyApply, buyorderModifyApply.getBuyorderExpenseItemDtos());

            Objects.requireNonNull(buyorderModifyApplyId, "采购单修改记录编号为空");

            String businessKey = String.join("_", ProcessConstants.MODIFY_BUYORDER_APPLY_PROC_KEY, buyorderModifyApply.getBuyorderModifyApplyId().toString());

            // 获取订单修改信息
            buyorderModifyApply.setBuyorderModifyApplyId(buyorderModifyApplyId);
            BuyorderModifyApplyVo bmav = buyorderService.getBuyorderModifyApplyVoDetail(buyorderModifyApply);
            Map<String, Object> variableMap = new HashMap<String, Object>();
            // 开始生成流程(如果没有taskId表示新流程需要生成)
            variableMap.put("buyorderModifyApplyInfo", bmav);
            variableMap.put("orderId", buyorderModifyApply.getBuyorderId());
            variableMap.put("currentAssinee", user.getUsername());
            variableMap.put("processDefinitionKey", ProcessConstants.MODIFY_BUYORDER_APPLY_PROC_KEY);
            variableMap.put("businessKey", businessKey);
            variableMap.put("relateTableKey", buyorderModifyApply.getBuyorderModifyApplyId());
            variableMap.put("relateTable", "T_BUYORDER_MODIFY_APPLY");
            // 设置审核完成监听器回写参数
            variableMap.put("tableName", "T_BUYORDER_MODIFY_APPLY");
            variableMap.put("id", "BUYORDER_MODIFY_APPLY_ID");
            variableMap.put("idValue", buyorderModifyApply.getBuyorderModifyApplyId());
            variableMap.put("idValue1", buyorderModifyApply.getBuyorderId());
            variableMap.put("key", "VALID_STATUS");
            variableMap.put("value", 1);
            // 回写数据的表在db中
            variableMap.put("db", 1);

            boolean isChangeDeliveryWay = !Objects.equals(buyorderModifyApply.getOldDeliveryDirect(), buyorderModifyApply.getDeliveryDirect());
            boolean isChangeLogisticsComment = !Objects.equals(buyorderModifyApply.getOldLogisticsComments(), buyorderModifyApply.getLogisticsComments());

            variableMap.put("isChangeLogisticsComment", isChangeLogisticsComment);
            variableMap.put("isChangeDeliveryWay", isChangeDeliveryWay);
            if (isChangeDeliveryWay) {
                variableMap.put("deliverySelfOrWmsSwitch", CommonConstants.ON.equals(buyorderModifyApply.getOldDeliveryDirect()));
            }

            actionProcdefService.createProcessInstance(request, ProcessConstants.MODIFY_BUYORDER_APPLY_PROC_KEY, businessKey, variableMap);

            List<Integer> saleorderIdListByBuyorderId = newBuyOrderService.getSaleorderIdListByBuyorderId(buyorderModifyApply.getBuyorderId());


            // 默认申请人通过
            // 根据BusinessKey获取生成的审核实例
            Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, businessKey);
            if (!ProcessConstants.DEFAULT_AUDITED_ACTIVITY_NAME.equals(historicInfo.get("endStatus"))) {
                Task taskInfo = (Task) historicInfo.get("taskInfo");
                String taskId = taskInfo.getId();
                Authentication.setAuthenticatedUserId(user.getUsername());
                Map<String, Object> variables = new HashMap<>();
                // 产品总监默认审批通过
                String startUser = (String) historicInfo.get("startUser");
                boolean rolefalg = false;
                if (startUser != null) {
                    rolefalg = userService.isRoledirector(startUser);
                }


                String comment = "";
                boolean cancelFlag = true;
                //取消销售订单出库
                boolean isWmsCancel = false;
//                if (rolefalg) {
                    // 普 改 直
                    if (buyorderModifyApply.getOldDeliveryDirect() == 0 && buyorderModifyApply.getDeliveryDirect() == 1) {

                        // 先校验销售单中该sku未发货，根据saleorder goods的DELIVERY_NUM判断
                        Boolean isDeliveryAlready = newBuyOrderModifyApplyService.checkSaleorderGoodsDeliveryStatus(buyorderModifyApply.getBuyorderModifyApplyGoodsList());
                        if (!isDeliveryAlready) {
                            cancelFlag = false;
                            comment = "销售单内部分商品已发货，不允许审核通过";
                        } else {

                            // 校验是否可以wms取消 销售出库
                            cancelFlag = buyOrderModifyApplyService.cancelOutStorageRequestOfSaleOrder(saleorderMapper.selectByPrimaryKey(saleorderIdListByBuyorderId.get(0)).getSaleorderNo());
                            isWmsCancel = cancelFlag;
                            if (!cancelFlag) {
                                comment = "调取wms取消 销售出库失败，修改申请驳回";
                            }
                        }
                    }
//                }
                variables.put("cancelFlag", cancelFlag);

                if (saleorderIdListByBuyorderId.size() == 1 && isChangeDeliveryWay) {
                    //通知销售端
                    buyOrderModifyApplyService.syncModifyApplyInfoToSalesSide(saleorderIdListByBuyorderId.get(0), buyorderModifyApplyId,ProcessConstants.CheckStatus.CHECKING.getStatus(),isWmsCancel);
                }

                variables.put("pass", rolefalg & cancelFlag);
                ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, comment,
                        user.getUsername(), variables);
                // 如果未结束添加审核对应主表的审核状态
                if (!"endEvent".equals(complementStatus.getData())) {
                    verifiesRecordService.saveVerifiesInfo(taskId, ProcessConstants.CheckStatus.CHECKING.getStatus());
                }

                if (!cancelFlag) {
                    actionProcdefService.updateInfo("T_BUYORDER", "BUYORDER_ID", buyorderModifyApply.getBuyorderId(), "LOCKED_STATUS", 0, 2);
                    VerifiesInfo verifiesInfo = new VerifiesInfo();
                    verifiesInfo.setRelateTable("T_BUYORDER_MODIFY_APPLY");
                    verifiesInfo.setRelateTableKey(buyorderModifyApply.getBuyorderModifyApplyId());
                    verifiesInfo.setStatus(ErpConst.TWO);
                    //对应字典表editSaleorderVerify
                    verifiesInfo.setVerifiesType(754);
                    verifiesInfo.setModTime(System.currentTimeMillis());
                    verifiesRecordService.saveVerifiesInformation(verifiesInfo);

                }
            }


            // [订单流]跳转页面前,比较修改前后发货方式
            buyorderService.sendMsgByCompareNewOldDeliveryDirect(bmav.getBuyorderModifyApplyId());

            mav.addObject("url", "./viewModifyApply.do?buyorderModifyApplyId=" + buyorderModifyApplyId);
            return success(mav);
        } catch (Exception e) {
            logger.error("saveBuyorderApplyUpdate, buyorderId{}{}:", buyorderModifyApply.getBuyorderId(), e);
            mav.addObject("message", "任务完成操作失败：" + e.getMessage());
            return fail(mav);
        }
    }


    /**
     * 采购修改详情
     */
    @RequestMapping(value = "viewModifyApply")
    public ModelAndView viewModifyApply(HttpServletRequest request,
                                        BuyorderModifyApply buyorderModifyApply,
                                        ModelAndView modelAndView) throws Exception {

        // 校验订单流新旧跳转
        Integer newBuyOrderModifyApply = isNewBuyOrderModifyApply(buyorderModifyApply.getBuyorderModifyApplyId());
        if(Integer.valueOf(0).equals(newBuyOrderModifyApply)){
            modelAndView.setViewName("redirect:/order/buyorder/viewModifyApply.do?buyorderModifyApplyId="+buyorderModifyApply.getBuyorderModifyApplyId());
            return modelAndView;
        }

        User currentLoginUser = getSessionUser(request);
        modelAndView.addObject("curr_user", currentLoginUser);
        buyorderModifyApply.setCompanyId(currentLoginUser.getCompanyId());

        // 获取订单修改信息
        BuyorderModifyApplyVo bmav = buyorderService.getBuyorderModifyApplyVoDetail(buyorderModifyApply);
        try {
            setUserNameById(bmav);
        } catch (Exception e) {
            logger.error("采购修改详情获取创建者[UserId-{}]姓名失败！",bmav.getCreator());
        }
        modelAndView.addObject("bmav", bmav);

        // 查询直属费用单信息及修改信息
        List<BuyorderExpenseItemDto> buyorderExpenseItemDtoList = buyorderExpenseApiService.getExpenseItemModifyInfoList(buyorderModifyApply.getBuyorderModifyApplyId());
        modelAndView.addObject("buyorderExpenseItemDtoList", buyorderExpenseItemDtoList);

        // 新商品流替换SKU相关信息
        replaceNewGoodsStream(modelAndView, bmav);

        // 发票类型
        saveInvoiceType(modelAndView);

        // 订单修改审核信息
        Map<String, Object> historicInfo = actionProcdefService.getLeast(processEngine,
                "editBuyorderVerify_" + buyorderModifyApply.getBuyorderModifyApplyId());
        saveBuyOrderVerifyInfo(modelAndView, historicInfo);

        // 当前审核人
        Task taskInfo = (Task) historicInfo.get("taskInfo");
        String verifyUsers = null;
        List<String> verifyUserList = new ArrayList<>();
        if (null != taskInfo) {
            Map<String, Object> taskInfoVariables = actionProcdefService.getVariablesMap(taskInfo);
            verifyUsers = (String) taskInfoVariables.get("verifyUsers");
            String verifyUser = (String) taskInfoVariables.get("verifyUserList");
            if (null != verifyUser) {
                verifyUserList = Arrays.asList(verifyUser.split(","));
            }
        }
        List<String> verifyUsersList = new ArrayList<>();
        if (verifyUsers != null) {
            verifyUsersList = Arrays.asList(verifyUsers.split(","));
        }
        modelAndView.addObject("verifyUsers", verifyUsers);
        modelAndView.addObject("verifyUserList", verifyUserList);
        modelAndView.addObject("verifyUsersList", verifyUsersList);

        // 查询最近一次审核流程
        StepBar stepBar = buyorderService.generateStepBar(historicInfo,verifyUsersList,verifyUserList);

        // 生成审核进度步骤条
        pluginStepBar(stepBar);
        List<StatusNode> statusNodes = convertStepBarToStatusNode(stepBar);
        modelAndView.addObject("statusNodes", JsonUtils.convertConllectionToJsonStr(statusNodes));

        modelAndView.setViewName("orderstream/buyorder/view_modify_apply");
        return modelAndView;
    }

    private void saveBuyOrderVerifyInfo(ModelAndView modelAndView, Map<String, Object> historicInfo) {

        modelAndView.addObject("taskInfo", historicInfo.get("taskInfo"));
        modelAndView.addObject("startUser", historicInfo.get("startUser"));
        modelAndView.addObject("endStatus", historicInfo.get("endStatus"));
        modelAndView.addObject("historicActivityInstance", historicInfo.get("historicActivityInstance"));
        modelAndView.addObject("commentMap", historicInfo.get("commentMap"));
        modelAndView.addObject("candidateUserMap", historicInfo.get("candidateUserMap"));
    }

    private List<StatusNode> convertStepBarToStatusNode(StepBar stepBar) {

        if(CollectionUtils.isEmpty(stepBar.getStepList()) || stepBar.getActiveStatus() == null){
            return null;
        }

        List<StatusNode> statusNodes = new ArrayList<>();
        int currentTag = 0;
        for (StepBar.Step step: stepBar.getStepList()) {
            currentTag++;
            StatusNode statusNode = new StatusNode();
            if(Integer.valueOf(currentTag).compareTo(stepBar.getActiveStatus()) == 0){
                statusNode.setStatus(Integer.valueOf(2));
            }else if(Integer.valueOf(currentTag).compareTo(stepBar.getActiveStatus()) < 0){
                statusNode.setStatus(Integer.valueOf(1));
            }else{
                statusNode.setStatus(Integer.valueOf(0));
            }
            statusNode.setLabel(step.getTitle());
            statusNode.setTip((StringUtil.isNotBlank(step.getOperateName())?step.getOperateName()+"<br/>":"") +
                    (StringUtil.isNotBlank(step.getOperateTime())?step.getOperateTime()+"<br/>":"")+
                    (StringUtil.isNotBlank(step.getDesc())?step.getDesc()+"<br/>":""));
            statusNodes.add(statusNode);
        }
        if (stepBar.getStepList().get(stepBar.getStepList().size() - 1).getTitle().contains(BuyOrderModifyStatus.Son.NODE_REJECT.getNodeName())) {
            statusNodes.stream().forEach(item -> item.setFail(Integer.valueOf(1)));
        }

        return statusNodes;
    }

    private void pluginStepBar(StepBar stepBar) {
        List<StepBar.Step> stepList = stepBar.getStepList();
        if(CollectionUtils.isEmpty(stepList)){
            stepList.add(new StepBar.Step(BuyOrderModifyStatus.Son.NODE_START.getNodeName()));
            stepList.add(new StepBar.Step(BuyOrderModifyStatus.Son.NODE_APPLY.getNodeName()));
            stepList.add(new StepBar.Step(BuyOrderModifyStatus.Son.NODE_MANAGE.getNodeName()));
            stepList.add(new StepBar.Step(BuyOrderModifyStatus.Son.NODE_OVER.getNodeName()));
            stepBar.setActiveStatus(Integer.valueOf(0));
        }else{
            StepBar.Step lastStep = stepList.get(stepList.size() - 1);
            if(lastStep.getTitle().contains(BuyOrderModifyStatus.Son.NODE_OVER.getNodeName())){
                stepBar.setActiveStatus(stepList.size());
                return;
            }
            if(lastStep.getTitle().contains(BuyOrderModifyStatus.Son.NODE_REJECT.getNodeName())){
                stepBar.setActiveStatus(stepList.size());
                return;
            }
            if(lastStep.getTitle().contains(BuyOrderModifyStatus.Son.NODE_MANAGE.getNodeName())){
                stepList.add(new StepBar.Step(BuyOrderModifyStatus.Son.NODE_OVER.getNodeName()));
                stepBar.setActiveStatus(stepList.size()-1);
            }
            if(lastStep.getTitle().contains(BuyOrderModifyStatus.Son.NODE_APPLY.getNodeName())){
                stepList.add(new StepBar.Step(BuyOrderModifyStatus.Son.NODE_MANAGE.getNodeName()));
                stepList.add(new StepBar.Step(BuyOrderModifyStatus.Son.NODE_OVER.getNodeName()));
                stepBar.setActiveStatus(stepList.size()-2);
            }
            if(lastStep.getTitle().contains(BuyOrderModifyStatus.Son.NODE_START.getNodeName())){
                stepList.add(new StepBar.Step(BuyOrderModifyStatus.Son.NODE_APPLY.getNodeName()));
                stepList.add(new StepBar.Step(BuyOrderModifyStatus.Son.NODE_MANAGE.getNodeName()));
                stepList.add(new StepBar.Step(BuyOrderModifyStatus.Son.NODE_OVER.getNodeName()));
                stepBar.setActiveStatus(stepList.size()-3);
            }
        }
    }


    private void saveInvoiceType(ModelAndView modelAndView) {

        List<SysOptionDefinition> invoiceTypes = getSysOptionDefinitionList(428);
        modelAndView.addObject("invoiceTypes", invoiceTypes);
    }

    private void replaceNewGoodsStream(ModelAndView modelAndView, BuyorderModifyApplyVo bmav) {
        if(bmav==null){
            return;
        }
        if (!CollectionUtils.isEmpty(bmav.getBgvList())) {
            List<Integer> skuIds = new ArrayList<>();
            bmav.getBgvList().stream().forEach((BuyorderGoodsVo buyorderGoods) -> {
                skuIds.add(buyorderGoods.getGoodsId());
            });
            List<Map<String, Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);
            Map<String, Map<String, Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key -> key.get("SKU_NO").toString(), v -> v, (k, v) -> v));
            modelAndView.addObject("newSkuInfosMap", newSkuInfosMap);
        }
    }


    /**
     * 给UserName赋值
     *
     * @param obj
     */
    protected void setUserNameById(Object obj) throws Exception {
        if (obj instanceof BuyorderModifyApplyVo) {
            BuyorderModifyApplyVo tempObj = (BuyorderModifyApplyVo) obj;
            if (tempObj == null || tempObj.getCreator() == null) {
                return;
            }
            tempObj.setCreatorName(userService.getUserNameByUserId(tempObj.getCreator()));
        }
    }

    /**
     * 采购售后订单确认完成审核
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/editConfirmCompleteAlert")
    @SystemControllerLog(operationType = "edit", desc = "采购售后订单确认完成")
    public ResultInfo<?> editConfirmCompleteAlert(HttpServletRequest request, AfterSalesVo afterSales) {

        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);

        AfterSales afterSalesInfo = afterSalesOrderService.getAfterSalesById(afterSales.getAfterSalesId());
        if (afterSalesInfo == null) {
            return ResultInfo.error();
        }
        //将完成原因，完成备注，完成人员存在数据库
        afterSales.setAfterSalesStatusUser(user.getUserId());//设置完结人员Id
        ResultInfo<?> res = afterSalesOrderService.updateAfterSalesById(afterSales);

        afterSales.setAfterSalesNo(afterSalesInfo.getAfterSalesNo());
        afterSales.setAtferSalesStatus(2);//已完结
        afterSales.setCompanyId(user.getCompanyId());
        afterSales.setModTime(DateUtil.sysTimeMillis());
        afterSales.setUpdater(user.getUserId());
        afterSales.setAfterSalesStatusUser(user.getUserId());//设置完结人员Id

        try {
            // 售后单完成
            afterSales.setVerifiesType(1);
            Map<String, Object> variableMap = new HashMap<String, Object>();
            // 开始生成流程(如果没有taskId表示新流程需要生成)
            variableMap.put("afterSalesVo", afterSales);
            variableMap.put("aftersalesNo", afterSales.getAfterSalesNo());
            variableMap.put("afterSalesId", String.valueOf(afterSales.getAfterSalesId()));
            variableMap.put("afterSales", afterSales);
            variableMap.put("currentAssinee", user.getUsername());
            variableMap.put("processDefinitionKey", "overBuyorderAfterSalesVerify");
            variableMap.put("businessKey", "overBuyorderAfterSalesVerify_" + afterSalesInfo.getAfterSalesId());
            variableMap.put("relateTableKey", afterSalesInfo.getAfterSalesId());
            variableMap.put("relateTable", "T_AFTER_SALES");
            actionProcdefService.createProcessInstance(request, "overBuyorderAfterSalesVerify",
                    "overBuyorderAfterSalesVerify_" + afterSalesInfo.getAfterSalesId(), variableMap);
            // 默认申请人通过
            // 根据BusinessKey获取生成的审核实例
            Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
                    "overBuyorderAfterSalesVerify_" + afterSalesInfo.getAfterSalesId());
            User assigneeObj = userService.getUserParentInfo(user.getUsername(), 1);
            if (historicInfo.get("endStatus") != "审核完成") {
                Task taskInfo = (Task) historicInfo.get("taskInfo");
                String taskId = taskInfo.getId();
                Authentication.setAuthenticatedUserId(user.getUsername());
                // 默认审批通过
                ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, afterSales.getCompleteReason() == null ? "" : afterSales.getCompleteReason(),
                        user.getUsername(), variableMap);

                // 如果未结束添加审核对应主表的审核状态
                if (!"endEvent".equals(complementStatus.getData())) {
                    verifiesRecordService.saveVerifiesInfo(taskId, 0);
                }
            }

            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("aftersalesNo", afterSales.getAfterSalesNo());

            String url = "./order/newBuyorder/viewAfterSalesDetail.do?afterSalesId=" + afterSales.getAfterSalesId() + "&traderType=2";
            MessageUtil.sendMessage(210, Collections.singletonList(assigneeObj.getpUserId()), paramMap, url, null);
            return new ResultInfo(0, "操作成功");
        } catch (Exception e) {
            logger.error("activity error editConfirmComplete:", e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }

    }


    /**
     * 采购单审核
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/complement")
    public ModelAndView complement(HttpSession session, String taskId, Boolean pass, Integer type, Integer buyorderId) {
        ModelAndView mv = new ModelAndView();

        //防止多个用户点击审核操作，导致空指针的异常，如果查询不到对应的task，则弹出提示框，刷新父页面
        Task task = processEngine.getTaskService().createTaskQuery().taskId(taskId).singleResult();
        if (task == null) {
            mv.addObject("error_tips", "该采购单审核状态已发生变化，将刷新采购单详情页");
            mv.setViewName("order/buyorder/parent_reload");
            return mv;
        }

        mv.addObject("taskId", taskId);
        String taskName = task.getName();
        if (("供应主管审核".equals(taskName) || "产品主管审核".equals(taskName)) && pass) {
            StringBuffer tips = new StringBuffer();
            this.buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderIdNoSpecial(buyorderId)
                    .forEach(buyorderGoodsVo -> {
                        if (buyorderGoodsVo.getOriginalPurchasePrice() != null) {
                            tips.append("sku ").append(buyorderGoodsVo.getSku())
                                    .append("的采购价与价格中心的已核定成本不一致(价格中心成本为")
                                    .append(buyorderGoodsVo.getOriginalPurchasePrice())
                                    .append("，采购价为").append(buyorderGoodsVo.getPrice()).append("),");
                            mv.addObject("purchasePriceChange", "1");
                        }
                    });
            tips.append("确认审核通过么?");
            mv.addObject("tips", tips.toString());
        }

        mv.addObject("pass", pass);
        mv.addObject("type", type);
        if (null != buyorderId) {
            mv.addObject("buyorderId", buyorderId);
        } else {
            mv.addObject("buyorderId", 0);
        }
        mv.setViewName("orderstream/buyorder/new_complement");
        return mv;
    }


    /**
     * 完结采购售后
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/complementAfterSale")
    public ModelAndView complement(HttpSession session, HttpServletRequest request, String taskId, Boolean pass, Integer type, Integer saleorderId, Integer afterSalesId, Integer isLightning, Integer lightningAfterSalesId,
                                   @RequestParam(required = false) Integer afterSaleorderId) {
        ModelAndView mv = new ModelAndView();
        if (null != request.getParameter("sku") && !("".equals(request.getParameter("sku")))) {
            mv.addObject("sku", request.getParameter("sku"));
        }
        if (null != request.getParameter("orderId") && !("".equals(request.getParameter("orderId")))) {
            mv.addObject("orderId", request.getParameter("orderId"));
        }
        if (isLightning != null) {
            mv.addObject("isLightning", isLightning);
            mv.addObject("lightningAfterSalesId", lightningAfterSalesId);
        }
        mv.addObject("taskId", taskId);
        mv.addObject("saleorderId", saleorderId);
        mv.addObject("afterSalesId", afterSalesId);
        mv.addObject("afterSaleorderId", afterSaleorderId);
        mv.addObject("pass", pass);
        mv.addObject("type", type);
        mv.setViewName("orderstream/buyorder/complement");
        return mv;
    }


    /**
     * 订单审核操作
     */
    @ResponseBody
    @RequestMapping(value = "/complementAfterSaleTask")
    @SystemControllerLog(operationType = "add", desc = "订单审核操作")
    public ResultInfo<?> complementAfterSaleTask(HttpServletRequest request, String taskId, String comment, Boolean pass, Integer saleorderId, Integer isLightning, Integer lightningAfterSalesId, Integer afterSalesId,
                                                 HttpSession session, Integer afterSaleorderId) {
        // 获取session中user信息
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        Map<String, Object> variables = new HashMap<String, Object>();
        variables.put("pass", pass);
        ResultInfo backResultInfo = new ResultInfo(0, "操作成功");
        //审批操作
        try {
            //是否闪电换货
            try {
                if (lightningAfterSalesId != null && isLightning != null && pass != null && pass) {
                    afterSalesOrderService.addIsLightning(lightningAfterSalesId, isLightning);
                }
            } catch (Exception e) {
                logger.error("是否闪电退货添加失败", e);
            }
            //VDERP-6890直发的销售售后与关联的采购单售后强耦合---销售售后退换货单流程变更start
            ResultInfo resultInfo = new ResultInfo(0, "操作成功");
            if (pass) {
                if (lightningAfterSalesId != null || afterSaleorderId != null) {
                    HistoryService historyService = processEngine.getHistoryService(); // 任务相关service
                    HistoricTaskInstance historicTaskInstance = historyService.createHistoricTaskInstanceQuery()
                            .taskId(taskId).singleResult();
                    String processInstanceId = historicTaskInstance.getProcessInstanceId();
                    HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                            .processInstanceId(processInstanceId).singleResult();
                    String businessKey = historicProcessInstance.getBusinessKey();
                    TaskService taskService = processEngine.getTaskService();
                    Task taskInfo = taskService.createTaskQuery().processInstanceBusinessKey(businessKey).singleResult();
                    if ("售后主管审核".equals(taskInfo.getName())) {
                        if (lightningAfterSalesId != null) {
                            resultInfo = afterSalesService.directSalesAfterSales(lightningAfterSalesId, domain);
                        } else {
                            resultInfo = afterSalesService.directSalesAfterSales(afterSaleorderId, domain);
                        }
                        if (resultInfo.getCode() == -1) {
                            //审批不通过
                            pass = false;
                            //重新设定审批流中审批结果为false
                            variables.put("pass", pass);
                            backResultInfo = resultInfo;
                        }
                    }
                }
            }
            //VDERP-6890直发的销售售后与关联的采购单售后强耦合---销售售后退换货单流程变更end
            if (!pass) {
                //如果审核不通过
                TaskService taskService = processEngine.getTaskService();//获取任务的Service，设置和获取流程变量
                taskService.setVariable(taskId, "value", 3);
                String tableName = (String) taskService.getVariable(taskId, "tableName");
                String id = (String) taskService.getVariable(taskId, "id");
                Integer idValue = (Integer) taskService.getVariable(taskId, "idValue");
                String key = (String) taskService.getVariable(taskId, "key");
                if (tableName != null && id != null && idValue != null && key != null) {
                    actionProcdefService.updateInfo(tableName, id, idValue, key, 3, 2);
                }
                verifiesRecordService.saveVerifiesInfo(taskId, 2);
                // add by Randy.Xu 2021/7/15 9:29 .Desc:VDERP-7120 【售后在线化】系统设计 保存审核不通过信息 . begin
                if ("T_AFTER_SALES".equals(tableName) && "STATUS".equals(key) && "AFTER_SALES_ID".equals(id)) {
                    afterSalesService.saveVerifiesNotPassReason(idValue, comment);
                }
                // add by Randy.Xu 2021/7/15 9:29 .Desc: . end
            }
            ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, comment, user.getUsername(), variables);
            //如果审核没结束添加审核对应主表的审核状态
            if (!"endEvent".equals(complementStatus.getData())) {
                Integer status = 0;
                if (pass) {
                    //如果审核通过
                    status = 0;
                } else {
                    //如果审核不通过
                    status = 2;
                }
                verifiesRecordService.saveVerifiesInfo(taskId, status);

            }

//				// 销售订单退货,产品解锁
//				if (afterSalesInfo.getType().equals(539) || afterSalesInfo.getType().equals(540)) {
//	    	    	afterSalesOrderService.getNoLockSaleorderGoodsVo(afterSales);
//	    	    }
            if (null != request.getParameter("sku") && !("".equals(request.getParameter("sku"))) && null != request.getParameter("orderId") && !("".equals(request.getParameter("orderId")))) {
                AfterSalesVo afterSalesInfo = new AfterSalesVo();
                afterSalesInfo.setSku(request.getParameter("sku"));
                afterSalesInfo.setOrderId(Integer.valueOf(request.getParameter("orderId")));
                afterSalesOrderService.getNoLockSaleorderGoodsVo(afterSalesInfo);

                Saleorder saleorder = new Saleorder();
                saleorder.setSaleorderId(Integer.valueOf(request.getParameter("orderId")));
                saleorder.setCompanyId(user.getCompanyId());
                saleorderService.synchronousOrderStatus(user, saleorder);
            }
            if (saleorderId != null || (null != request.getParameter("orderId") && !("".equals(request.getParameter("orderId"))))) {
                Saleorder saleorder = new Saleorder();
                if (saleorderId != null) {
                    saleorder.setSaleorderId(saleorderId);
                } else {
                    saleorder.setSaleorderId(Integer.valueOf(request.getParameter("orderId")));
                }
                if ("endEvent".equals(complementStatus.getData()) && pass) {

                    // add by Tomcat.Hui 2020/3/6 1:57 下午 .Desc: VDERP-2057 BD订单流程优化-ERP部分  售后产生的付款状态变更需要通知mjx. start
                    afterSalesOrderService.notifyStatusToMjx(saleorder.getSaleorderId(), afterSalesId);
                    // add by Tomcat.Hui 2020/3/6 1:57 下午 .Desc: VDERP-2057 BD订单流程优化-ERP部分  售后产生的付款状态变更需要通知mjx. end

                    if (afterSalesId != null) {
                        AfterSales afterSales = new AfterSales();
                        afterSales.setAfterSalesId(afterSalesId);
                        afterSales.setBusinessType(1);
                        AfterSalesVo afterSalesVo = afterSalesOrderService.getAfterSalesVoListById(afterSales);
                        if (!org.springframework.util.StringUtils.isEmpty(afterSalesVo) && afterSalesVo.getType().equals(539)) {
                            saleorder.setOperateType(StockOperateTypeConst.AFTERORDER_BACK_FINSH);
                        }
                    }
                }
                //更新售后单updateDataTime
                orderCommonService.updateAfterOrderDataUpdateTime(afterSalesId, null, OrderDataUpdateConstant.AFTER_ORDER_END);
                //调用库存服务
                warehouseStockService.updateOccupyStockService(saleorder, 0);
            }

            return backResultInfo;
        } catch (Exception e) {
            logger.error(" activity error complementAfterSaleTask:" + afterSalesId, e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }


    }

    /**
     * 保存编辑售后
     */
    @ResponseBody
    @RequestMapping(value = "/saveEditAfterSales")
    @SystemControllerLog(operationType = "edit", desc = "保存编辑采购售后")
    @NoRepeatSubmit
    public ModelAndView saveEditAfterSales(HttpServletRequest request, AfterSalesVo afterSalesVo,
                                           @RequestParam(required = false, value = "afterSaleNums") String[] afterSaleNums,
                                           @RequestParam(required = false, value = "fileName") String[] fileName,
                                           @RequestParam(required = false, value = "fileUri") String[] fileUri,
                                           @RequestParam(required = false, value = "invoiceIds") String[] invoiceIds) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        afterSalesVo.setAfterSalesNum(afterSaleNums);
        afterSalesVo.setAttachName(fileName);
        afterSalesVo.setAttachUri(fileUri);
        afterSalesVo.setDomain(domain);
        afterSalesVo.setInvoiceIds(invoiceIds);
        afterSalesVo.setTraderType(1);
        afterSalesVo.setPayee(user.getCompanyName());
        ModelAndView mav = new ModelAndView();
        ResultInfo<?> res = afterSalesOrderService.saveEditAfterSales(afterSalesVo, user);
        // mav.addObject("refresh",
        // "true_false_true");//是否关闭当前页，是否刷新当前页，是否刷新上层页面----三个参数为必传项
        // mav.addObject("url","./viewBuyordersh.do?buyorderId="+afterSalesVo.getOrderId());
        if (res.getCode() == 0) {
            mav.addObject("url", "./viewAfterSalesDetail.do?traderType=2&afterSalesId=" + res.getData());
            return success(mav);
        } else {
            return fail(mav);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/checkChangeDeliveryType")
    public ResultInfo checkChangeDeliveryType(Integer type, Integer buyorderId) {
        try {
            newBuyOrderModifyApplyService.checkChangeDeliveryType(type, buyorderId);
        } catch (IllegalArgumentException e) {
            log.warn("【checkChangeDeliveryType】 处理异常",e);
            return ResultInfo.error(e.getMessage());
        }
        return ResultInfo.success();
    }

    /**
     * 采购单打印合同
     */
    @RequestMapping(value = "/printBuyOrder")
    @NoNeedAccessAuthorization
    public ModelAndView printBuyOrder(HttpServletRequest request, Buyorder buyorder,@RequestParam(required = false) Boolean autoGenerate) {

        User user = getSessionUser(request);
        ModelAndView mv = new ModelAndView();
        buyorder.setCompanyId(user == null ? 1 : user.getCompanyId());
        BuyorderVo buyorderVo = buyorderService.getBuyOrderPrintInfo(buyorder);

        //采购单中管是否包含医疗器械类型的SKU
        boolean haveMedicalApparatusFlag = false;
        if (CollectionUtils.isNotEmpty(buyorderVo.getBuyorderGoodsVoList())) {
            for (BuyorderGoodsVo buyOrderGood : buyorderVo.getBuyorderGoodsVoList()) {

                if (StringUtil.isEmpty(buyOrderGood.getSku())) {
                    continue;
                }

                CoreSpuGenerate spuInfo = this.vGoodsService.findSpuInfoBySkuNo(buyOrderGood.getSku());
                if (spuInfo == null) {
                    continue;
                }

                CoreSpuBaseDTO coreSpuDto = baseGoodsService.selectSpuBaseById(spuInfo.getSpuId());
                //医疗器械 注册证号不为空就是医疗机械
                if (StringUtils.isNotBlank(coreSpuDto.getRegistrationNumber())) {
                    buyOrderGood.setRegistrationNumber(coreSpuDto.getRegistrationNumber());
                    haveMedicalApparatusFlag = true;
                    //break;
                }
                if (Objects.nonNull(coreSpuDto.getRegistrationNumberId())) {
                    RegistrationNumber registrationNumber = registrationNumberMapper.selectByPrimaryKey(coreSpuDto.getRegistrationNumberId());
                    if (Objects.nonNull(registrationNumber)) {
                        ProductCompany productCompany = productCompanyMapper.selectByPrimaryKey(registrationNumber.getProductCompanyId());
                        buyOrderGood.setManufacturerName(Objects.nonNull(productCompany) ? StringUtils.isBlank(productCompany.getProductCompanyChineseName()) ? productCompany.getProductCompanyEnglishName() : productCompany.getProductCompanyChineseName() : "");
                    }
                }
            }
        }
        if (haveMedicalApparatusFlag) {
            mv.addObject("haveMedicalApparatus", 1);
        } else {
            mv.addObject("haveMedicalApparatus", 0);
        }

        dealWithBdAfterSaleStandard(buyorderVo);

        // 获取采购人员信息
        UserDetail detail = userDetailMapper.getUserDetail(buyorderVo.getUserId());
        User userInfo = userService.getUserById(buyorderVo.getUserId());
        mv.addObject("orgId", userInfo.getOrgId());
        String username = userService.getUserById(buyorderVo.getUserId()).getUsername();

        long currTime = DateUtil.sysTimeMillis();
        mv.addObject("currTime", DateUtil.convertString(currTime, "yyyy-MM-dd "));

        // 发票类型
        List<SysOptionDefinition> invoiceTypes = getSysOptionDefinitionList(428);
        mv.addObject("invoiceTypes", invoiceTypes);

        // 获取公司信息
        Company company = companyService.getCompanyByCompangId(buyorder.getCompanyId());
        mv.addObject("company", company);

        ParamsConfigVo paramsConfigVo = new ParamsConfigVo();
        paramsConfigVo.setCompanyId(company.getCompanyId());
        paramsConfigVo.setParamsKey(100);
        AddressVo delivery = addressService.getDeliveryAddress(paramsConfigVo);

        mv.addObject("delivery", delivery);
        mv.addObject("detail", detail);
        mv.addObject("username", username);
        OrderPaymentDetailsDto orderPaymentDetailsDto = buyorderApiService.queryOrderPaymentDetails(buyorderVo.getBuyorderId(), ErpConstant.ZERO, ErpConstant.BANK_ACCEPTANCE);
        buyorderVo.setBankAcceptance(Objects.nonNull(orderPaymentDetailsDto) ? ErpConstant.ONE : ErpConstant.ZERO);
        mv.addObject("buyorderVo", buyorderVo);

        // 运费类型
        List<SysOptionDefinition> yfTypes = getSysOptionDefinitionList(469);
        mv.addObject("yfTypes", yfTypes);

        List<BuyorderGoodsVo> buyOrderGoodsList = buyorderVo.getBuyorderGoodsVoList();
        // 查出采购单生效前关联的费用单商品
        List<BuyorderExpenseItemDto> buyOrderExpenseGoodsList = buyorderExpenseService.getByBuyOrderId(buyorder.getBuyorderId());
        buyOrderExpenseGoodsList.forEach(item -> {
            BuyorderGoodsVo temp = new BuyorderGoodsVo();
            temp.setIsDelete(0);
            temp.setSku(item.getBuyorderExpenseItemDetailDto().getSku());
            temp.setGoodsName(item.getBuyorderExpenseItemDetailDto().getGoodsName());
            temp.setBrandName(item.getBuyorderExpenseItemDetailDto().getBrandName());
            temp.setModel(item.getBuyorderExpenseItemDetailDto().getModel());
            temp.setSpec("/");
            temp.setNum(item.getNum());
            temp.setUnitName(item.getBuyorderExpenseItemDetailDto().getUnitName());
            temp.setPrice(item.getBuyorderExpenseItemDetailDto().getPrice());
            temp.setDeliveryCycle(" ");
            temp.setInstallPolicy("/");
            temp.setQualityPeriod("/");
            temp.setInsideComments(item.getBuyorderExpenseItemDetailDto().getInsideComments());
            buyOrderGoodsList.add(temp);
        });
        BuyorderExpenseDto buyorderExpenseDto = buyorderExpenseApiService.queryAttributiveExpenseInfoByBuyorderId(buyorder.getBuyorderId());
        mv.addObject("buyorderExpenseDto", buyorderExpenseDto);

        mv.addObject("buyorderGoodsList", buyOrderGoodsList);
        BigDecimal pageTotalPrice = new BigDecimal(0.00);
        BigDecimal zioe = pageTotalPrice;
        Integer flag = -1;
        for (BuyorderGoodsVo buyorderGoods : buyorderVo.getBuyorderGoodsVoList()) {
            BigDecimal priceAll = buyorderGoods.getPrice();
            BigDecimal rebatePrice = Objects.isNull(buyorderGoods.getRebatePrice())?BigDecimal.ZERO:buyorderGoods.getRebatePrice();
            BigDecimal realPrice = priceAll.subtract(rebatePrice);
            buyorderGoods.setPrice(realPrice);
            String price = NewBuyOrderUtils.getCommaFormat(realPrice);
            if (!price.contains(".")) {
                price += ".00";
            }
            buyorderGoods.setPrices(price);
            String allprice = NewBuyOrderUtils.getCommaFormat(buyorderGoods.getPrice().multiply(new BigDecimal(buyorderGoods.getNum())));
            if (!allprice.contains(".")) {
                allprice += ".00";
            }
            buyorderGoods.setAllPrice(allprice);
            pageTotalPrice = pageTotalPrice.add(buyorderGoods.getPrice().multiply(new BigDecimal(buyorderGoods.getNum())));
        }
        String totalAmount = NewBuyOrderUtils.getCommaFormat(pageTotalPrice);
        if (!totalAmount.contains(".")) {
            totalAmount += ".00";
        }
        mv.addObject("totalAmount", totalAmount);
        try {
            mv.addObject("chineseNumberTotalPrice", pageTotalPrice.compareTo(zioe) > 0
                    ? DigitToChineseUppercaseNumberUtils.numberToChineseNumber(pageTotalPrice) : null);
        } catch (ShowErrorMsgException e) {
            logger.error(Contant.ERROR_MSG, e);
        }
        mv.addObject("vedeng_address_phone", vedeng_address_phone.trim());
        mv.addObject("autoGenerate", autoGenerate);
        //处理合同日期
        Date contractDate = 0 == buyorderVo.getValidTime() ? new Date() : new Date(buyorderVo.getValidTime());
        List<String> contractDateList = Arrays.asList(Integer.toString(cn.hutool.core.date.DateUtil.year(contractDate))
                ,String.format("%02d",cn.hutool.core.date.DateUtil.month(contractDate)+1)
                ,String.format("%02d",cn.hutool.core.date.DateUtil.dayOfMonth(contractDate)));
        mv.addObject("contractDateList",contractDateList);
        if (buyorderVo.getCompanyId() == 10) {
            if (buyorderVo.getDeliveryDirect() == 0) {
                mv.setViewName("orderstream/buyorder/buyorder_pf_print");
            } else if (buyorderVo.getDeliveryDirect() == 1) {
                mv.setViewName("orderstream/buyorder/buyoredr_zf_print");
            }
        } else {
            if (buyorderVo.getDeliveryDirect() == 0) {
                mv.setViewName("orderstream/buyorder/order_pf_print");
            } else if (buyorderVo.getDeliveryDirect() == 1) {
                mv.setViewName("orderstream/buyorder/order_zf_print");
            }
        }
        return mv;
    }



    /**
     * .售后订单确认完成
     */
    @FormToken(remove=true)
    @ResponseBody
    @RequestMapping(value="/editConfirmComplete")
    @SystemControllerLog(operationType = "edit",desc = "售后订单确认完成")
    public ResultInfo<?> editConfirmComplete(HttpServletRequest request,AfterSalesVo afterSales){
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);

        //采购单不走审核流程售后
        afterSales.setAtferSalesStatus(2);//已完结
        afterSales.setCompanyId(user.getCompanyId());
        afterSales.setModTime(DateUtil.sysTimeMillis());
        afterSales.setUpdater(user.getUserId());
        afterSales.setAfterSalesStatusUser(user.getUserId());//设置完结人员Id

        OrderProcessorManager orderProcessorManager = new OrderProcessorManager();
        //VDERP-7931

        if(afterSales.getType() == 546){

            orderProcessorManager.setBizDto(new BizDto<AfterSalesVo>(afterSales)).setPre(wmsbasrProcessor).setCoreBiz(overBuyordeReturnAfterSalesCore)
                    .setAfter(dealSaleorderForBuyReturnAfterProcessor).setAfter(dealBuyorderForBuyReturnAfterProcessor).setAfter(dealTicketTaskForBuyReturnProcessor).excute();

        } else if(afterSales.getType() == 547){

            orderProcessorManager.setBizDto(new BizDto<AfterSalesVo>(afterSales)).setPre(wmsbascProcessor).setCoreBiz(overBuyordeChangeAfterSalesCore)
                    .setAfter(dealSaleorderForBuyChangeAfterProcessor).setAfter(dealBuyorderForBuyChangeAfterProcessor).excute();
        }

        return ResultInfo.success();
    }


    /**
     * 删除快递
     */
    @ResponseBody
    @RequestMapping(value = "/deleteExpress")
    public ResultInfo deleteExpress(Integer expressId) {

        // 校验签收状态
        Express currentExpress = expressService.getExpressInfoByPrimaryKey(expressId);
        if(!Objects.isNull(currentExpress) && ErpConst.TWO.equals(currentExpress.getArrivalStatus())) {
            return ResultInfo.error("当前快递" + currentExpress.getLogisticsNo() + "已签收，禁止删除");
        }

        //同行增加删除校验条件
        if(expressService.checkRelatedWarehouseInfo(expressId)){
            return ResultInfo.error("当前快递" + currentExpress.getLogisticsNo() + "不符合删除条件，禁止删除");
        }

        ResultInfo count = newBuyOrderService.deleteExpressByExpressId(expressId);

        if (count.getCode() == 0) {
            return ResultInfo.success();
        } else {
            return ResultInfo.error("删除快递信息失败");
        }
    }

    /**
     * 编辑售后服务费
     */
    @ResponseBody
    @RequestMapping(value="/editInstallstionPage")
    public ModelAndView editInstallstionPage(HttpServletRequest request,AfterSalesDetail afterSalesDetail,String flag) throws IOException{
        ModelAndView mav = new ModelAndView("orderstream/buyorder/edit_afterSales_installation");
        if(ObjectUtils.notEmpty(flag) && !"bth".equals(flag)){
            AfterSalesDetailVo afterSalesDetailVo = afterSalesOrderService.getAfterSalesDetailVoByParam(afterSalesDetail);
            mav.addObject("afterSalesDetail", afterSalesDetailVo);
            mav.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(afterSalesDetailVo)));
        }else{
            mav.addObject("afterSalesDetail", afterSalesDetail);
            mav.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(afterSalesDetail)));
        }
        mav.addObject("flag", flag);
        return mav;
    }

    /**
     * 采购单申请修改审核操作
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/complementBuyorderModifyApply")
    @SystemControllerLog(operationType = "edit", desc = "采购单申请修改审核操作")
    public ResultInfo<?> complementTask(HttpServletRequest request, String taskId, String comment, Boolean pass,
                                        Integer buyorderId, HttpSession session) {
        logger.info("taskId : "+taskId);
        // 获取session中user信息
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        Map<String, Object> variables = new HashMap<String, Object>();
        // 审批操作
        try {
            String tableName = null;
            // 如果审核没结束添加审核对应主表的审核状态
            Integer status = 0;
            TaskService taskService = processEngine.getTaskService();// 获取任务的Service，设置和获取流程变量
            String id = (String) taskService.getVariable(taskId, "id");
            Integer idValue = (Integer) taskService.getVariable(taskId, "idValue");
            String key = (String) taskService.getVariable(taskId, "key");
            tableName = (String) taskService.getVariable(taskId, "tableName");

            BuyorderModifyApplyVo buyorderModifyApply = (BuyorderModifyApplyVo) taskService.getVariable(taskId, "buyorderModifyApplyInfo");

            // 使用任务id,获取任务对象，获取流程实例id
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            String taskName = task.getName();


            variables.put("pass", pass);

            if (pass) {
                // 如果修改的主表是付款申请表，审核节点为财务制单，则修改制单状态
                status = 0;
                if ("T_PAY_APPLY".equals(tableName) && "财务制单".equals(taskName)) {
                    // 制单
                    actionProcdefService.updateInfo(tableName, id, idValue, "IS_BILL", 1, 2);
                }
            } else {
                // 如果审核不通过
                status = 2;
                // 回写数据的表在db中
                variables.put("db", 2);
                if ("T_BUYORDER".equals(tableName)) {
                    // 采购单申请不通过解锁
                    actionProcdefService.updateInfo(tableName, "BUYORDER_ID", buyorderId, "STATUS", 0, 2);
                    actionProcdefService.updateInfo(tableName, "BUYORDER_ID", buyorderId, "LOCKED_STATUS", 0, 2);
                } else if ("T_PAY_APPLY".equals(tableName)) {
                    buyorderId = (Integer) taskService.getVariable(taskId, "buyorderId");
                    // 采购单付款申请不通过解锁
                    actionProcdefService.updateInfo("T_BUYORDER", "BUYORDER_ID", buyorderId, "LOCKED_STATUS", 0, 2);
                    if (tableName != null && id != null && idValue != null && key != null) {
                        actionProcdefService.updateInfo(tableName, id, idValue, key, 2, 2);
                    }
                }else if("T_BUYORDER_MODIFY_APPLY".equals(tableName)){
                    actionProcdefService.updateInfo("T_BUYORDER", "BUYORDER_ID", buyorderId, "LOCKED_STATUS", 0, 2);
                }else {
                    if (tableName != null && id != null && idValue != null && key != null) {
                        actionProcdefService.updateInfo(tableName, id, idValue, key, 2, 2);
                    }
                }
                verifiesRecordService.saveVerifiesInfo(taskId, status);
                riskCheckService.resetBuyorderRiks(buyorderId);
            }
            ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, comment,
                    user.getUsername(), variables);
            // 如果未结束添加审核对应主表的审核状态
            if (!"endEvent".equals(complementStatus.getData())) {
                verifiesRecordService.saveVerifiesInfo(taskId, status);
            }
            Buyorder buyorder = new Buyorder();
            if (tableName != null && "T_PAY_APPLY".equals(tableName)) {
                return new ResultInfo(0, "操作成功");
            } else {
                if (buyorderId != null && buyorderId != 0) {
                    buyorder.setBuyorderId(buyorderId);
                    BuyorderVo buyorderInfo = buyorderService.getBuyorderVoDetail(buyorder, user);
                    Integer statusInfo = null;
                    Map<String, Object> data = new HashMap<String, Object>();
                    if (buyorderInfo.getValidStatus() == 1) {
                        statusInfo = 1;
                        data.put("buyorderId", buyorderInfo.getBuyorderId());
                    } else {
                        statusInfo = 0;
                    }
                    return new ResultInfo(0, "操作成功", statusInfo, data);
                } else {
                    return new ResultInfo(0, "操作成功");
                }
            }
        } catch (Exception e) {
            logger.error("buy order complementTask:", e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }
    }


    /**
     * 电子签章按钮
     */
    @ResponseBody
    @RequestMapping(value="/signature")
    @NoNeedAccessAuthorization
    public ResultInfo<?> signature(HttpSession session,Integer buyorderId ){
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        return buyorderElectronicSignatureService.signature(buyorderId,user);
    }

    /**
     *添加直发售后换货出库记录页面
     */
    @ResponseBody
    @RequestMapping(value = "/addDirectExchangeOutLogPage")
    @NoNeedAccessAuthorization
    public ModelAndView addDirectExchangeOutLogPage(HttpServletRequest request, AfterSalesVo afterSalesVo) {
        ModelAndView mav = new ModelAndView("orderstream/buyorder/add_afterSales_hh_record");
        List<AfterSalesGoodsVo> afterSalesGoodsVoList = afterSalesOrderService.getDirectBuyorderAfterSaleGoods(afterSalesVo);

        List<Integer> skuIds = new ArrayList<>();

        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 start
        if (!CollectionUtils.isEmpty(afterSalesGoodsVoList)) {
            afterSalesGoodsVoList.stream().forEach(saleGood -> {
                skuIds.add(saleGood.getGoodsId());
            });
            List<Map<String, Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);
            Map<String, Map<String, Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key -> key.get("SKU_NO").toString(), v -> v, (k, v) -> v));
            mav.addObject("newSkuInfosMap", newSkuInfosMap);
        }
        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 end
        mav.addObject("afterSalesGoodsVoList", afterSalesGoodsVoList);
        return mav;
    }

    /**
     *售后换货出库日志明细保存
     */
    @ResponseBody
    @RequestMapping(value = "/saveDirectAfterSaleExchangeOutLog")
    @Transactional(rollbackFor = Exception.class)
    @NoNeedAccessAuthorization
    public ResultInfo saveDirectAfterSaleExchangeOutLog(HttpServletRequest request, @RequestBody List<AfterSaleBuyorderDirectOutLog> afterSaleBuyorderDirectOutLogList) {
        ResultInfo resultInfo = afterSalesOrderService.saveDirectAfterSaleExchangeOutLog(afterSaleBuyorderDirectOutLogList);
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        warehouseGoodsOutService.saveDirectOutInLog(user,afterSaleBuyorderDirectOutLogList);
        return resultInfo;
    }

    @RequestMapping("/purchaseContractInfo")
    @NoNeedAccessAuthorization
    public ModelAndView purchaseContractInfo(Integer buyorderId){
        ModelAndView mv = new ModelAndView("vue/view/contract/purchaseContractInfo");
        List<AuditRecordInstanceVo> list = getPurchaseContractInfo(buyorderId);
        mv.addObject("instanceVo",JSON.toJSON(list));
        return mv;
    }

    public List<AuditRecordInstanceVo> getPurchaseContractInfo(Integer buyorderId) {
        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "purchaseContractVerify_" + buyorderId);
        List<HistoricActivityInstance> historicActivityInstances = (List<HistoricActivityInstance>) historicInfo.get("historicActivityInstance");
        Map<String, Object> commentMap = (Map<String, Object>) historicInfo.get("commentMap");
        User startUser = historicInfo.get("startUser") == null ? null : userService.getByUsername(historicInfo.get("startUser").toString(), ErpConst.NJ_COMPANY_ID);

        List<AuditRecordInstanceVo> instanceVos = new ArrayList<>();

        Task taskInfo = (Task) historicInfo.get("taskInfo");
        // 当前审核人
        String verifyUsers = null;
        List<String> verifyUserList = new ArrayList<>();
        if (null != taskInfo) {
            Map<String, Object> taskInfoVariables = actionProcdefService.getVariablesMap(taskInfo);
            verifyUsers = getVerifyUserRealNames((String) taskInfoVariables.get("verifyUsers"));
            String verifyUser = getVerifyUserRealNames((String) taskInfoVariables.get("verifyUserList"));
            if (null != verifyUser) {
                verifyUserList = Arrays.asList(verifyUser.split(","));
            }
        }
        List<String> verifyUsersList = new ArrayList<>();
        if (verifyUsers != null && !verifyUserList.isEmpty()) {
            verifyUsersList = Arrays.asList(verifyUsers.split(","));
        }

        for (int i = 0; i < historicActivityInstances.size(); i++) {
            HistoricActivityInstance item = historicActivityInstances.get(i);
            AuditRecordInstanceVo instanceVo = new AuditRecordInstanceVo();
            if (org.springframework.util.StringUtils.isEmpty(item.getActivityName())){
                continue;
            }
            //操作人
            if ("startEvent".equals(item.getActivityType())){
                instanceVo.setOperatorName(startUser == null ? "" : userService.getRealNameByUserName(startUser.getUsername()));
            } else if ("intermediateThrowEvent".equals(item.getActivityType())){
                instanceVo.setOperatorName(null);
            } else {
                if (i == historicActivityInstances.size() -1){
                    instanceVo.setOperatorName(StrUtil.join(",",verifyUsersList));
                    if (verifyUsersList.isEmpty() && org.springframework.util.StringUtils.isEmpty(item.getAssignee())){
                        instanceVo.setOperatorName(verifyUsers);
                    }
                }
                if (org.springframework.util.StringUtils.isEmpty(instanceVo.getOperatorName())){
                    instanceVo.setOperatorName(userService.getRealNameByUserName(item.getAssignee()));
                }
            }

            //操作时间
            instanceVo.setOperateTime(item.getEndTime() == null ? null : DateUtil.DateToString(item.getEndTime(),DateUtil.TIME_FORMAT));
            //操作事项
            String operateInstance = item.getActivityName();
            if ("startEvent".equals(item.getActivityType())){
                operateInstance = "开始";
            } else if ("intermediateThrowEvent".equals(item.getActivityType())){
                operateInstance = "结束";
            }
            instanceVo.setOperateInstance(operateInstance);
            //备注
            if (commentMap.containsKey(item.getTaskId())){
                instanceVo.setComment(commentMap.get(item.getTaskId()).toString());
            }
            instanceVos.add(instanceVo);
        }
        log.info("instanceVos:{}", JSONUtil.toJsonStr(instanceVos));
        return instanceVos;
    }

    @RequestMapping("/purchaseContractComplement")
    @NoNeedAccessAuthorization
    public ModelAndView purchaseContractComplement(Integer taskId,Integer buyorderId,Boolean pass,Long addTime){
        ModelAndView mv = new ModelAndView("orderstream/buyorder/purchaseContractComplement");
        logger.info("采购合同回传审核taskid{}",taskId);
        mv.addObject("taskId", taskId);
        mv.addObject("buyorderId", buyorderId);
        mv.addObject("pass", pass);
        mv.addObject("type", ErpConst.FOUR);
        mv.addObject("addTime", addTime);
        return mv;
    }

    @ResponseBody
    @RequestMapping("/complementPurchaseContractTask")
    @NoNeedAccessAuthorization
    public R<?> complementPurchaseContractTask(HttpServletRequest request,Integer taskId,Boolean pass,Integer buyorderId,String comment,Long addTime){
        // 2.审核通过或驳回
        if (StrUtil.isBlank(comment)){
            comment = pass ? "审核通过" : "审核不通过";
        }
        User user = getSessionUser(request);
        Map<String, Object> variables = new HashMap<>();
        variables.put("pass", pass);
        variables.put("updater", user.getUserId());
        variables.put("autoCheckAptitude", false);
        // 审批操作
        try {
            String businessKey = "purchaseContractVerify_" + buyorderId;
            Task taskInfo = processEngine.getTaskService().createTaskQuery().processInstanceBusinessKey(businessKey).singleResult();
            if (Objects.isNull(taskInfo)) {
                return R.error("流程已结束，请刷新");
            }
            String taskId2 = taskInfo.getId();
            int statusInfo = 0;
            if (StrUtil.isBlank(comment)){
                comment = "";
            }
            ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId2, comment, user.getUsername(), variables);
            if ("endEvent".equals(complementStatus.getData())){
                Map<String, Object> historicInfoPay = actionProcdefService.getVariablesMap(taskInfo);
                Buyorder buyorder = buyorderMapper.selectByPrimaryKey(buyorderId);
                Map<String, String> map = new HashMap<>();
                map.put("buyOrderNo", buyorder.getBuyorderNo());
                String url = "./order/newBuyorder/newViewBuyOrderDetail.do?buyorderId="+buyorderId;
                if (ObjectUtil.isNotNull(historicInfoPay.get("startUserId"))){
                    MessageUtil.sendMessage(pass ? 268 : 269, Collections.singletonList((Integer) historicInfoPay.get("startUserId")), map, url);
                }
            }
            //更新verifyInfo状态
            verifiesInfoMapper.updatePurchaseContractStatus(addTime,(pass ? 1 : 2));
        } catch (Exception e) {
            logger.error("采购订单合同审核异常", e);
            return R.error("");
        }
        return R.success();
    }

    /**
     * 查看实际供应商信息
     */
    @RequestMapping("/actualSupplierInfo")
    @ExcludeAuthorization
    public ModelAndView actualSupplierInfo(Integer buyorderId){
        ModelAndView mv = new ModelAndView("vue/view/buyorder/actual_supplier_info");
        BuyorderActualSupplierDto actualSupplier = buyorderApiService.getActualSupplierByBuyorderId(buyorderId);
        mv.addObject("actualSupplier", JSONUtil.parse(actualSupplier));
        mv.addObject("invoiceTypeStr", InvoiceTaxTypeEnum.getDesc(actualSupplier.getInvoiceType()));
        return mv;
    }

}

