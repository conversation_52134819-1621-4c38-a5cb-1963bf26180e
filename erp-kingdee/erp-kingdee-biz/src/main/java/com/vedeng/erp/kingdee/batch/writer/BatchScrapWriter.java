package com.vedeng.erp.kingdee.batch.writer;

import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeeStorageOutDto;
import com.vedeng.erp.kingdee.service.KingDeeStorageOutApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 报废出库单
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchScrapWriter extends BaseWriter<KingDeeStorageOutDto> {

    @Autowired
    private KingDeeStorageOutApiService kingDeeStorageOutApiService;

    @Override
    public void doWrite(KingDeeStorageOutDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
        dto.setKingDeeBizEnums(KingDeeBizEnums.saveStorageOut);
        kingDeeStorageOutApiService.register(dto,true);
    }

}
