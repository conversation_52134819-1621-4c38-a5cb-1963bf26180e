package com.vedeng.erp.settlement.domain.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 银行流水忽略项记录
 */
@Data
public class BankBillIgnoreRecordEntity implements Serializable {
    /**
     * 主键
     */
    private Integer bankBillIgnoreInfoId;

    /**
     * 银行流水ID
     */
    private Integer bankBillId;

    /**
     * 对方名称
     */
    private String accName;

    /**
     * 往来单位类型
     */
    private String contactUnitType;

    /**
     * 往来单位编码
     */
    private String contactUnitNo;

    /**
     * 往来单位
     */
    private String contactUnit;

    /**
     * 交易主体 对公 对私
     */
    private String tradeSubject;

    /**
     * 忽略原因(字典库parent=4286)
     */
    private Integer ignoreReason;

    /**
     * 是否删除 0否 1是
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 修改时间
     */
    private Date modTime;

    /**
     * 添加人ID
     */
    private Integer creator;

    /**
     * 添加人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Integer updater;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 更新备注
     */
    private String updateRemark;

    private Integer type;

    private static final long serialVersionUID = 1L;
}