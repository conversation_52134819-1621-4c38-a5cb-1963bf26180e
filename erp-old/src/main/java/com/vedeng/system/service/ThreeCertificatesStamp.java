package com.vedeng.system.service;

import com.vedeng.authorization.model.User;

/**
 * 三证盖章
 * <AUTHOR>
 */
public interface ThreeCertificatesStamp {

    /**
     * 电子签章
     *
     * @param manufacturerId 生产商id
     */
    void certificatesStamp(Integer manufacturerId, User user);


    /**
     * 电子签章-器械注册证
     *
     * @param firstEngageId 器械注册id
     */
    void certificatesStampRegistration(Integer firstEngageId, User user);



}
