package com.vedeng.erp.aftersale.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.*;

/**
 * T_EXPENSE_AFTER_SALES_ITEM
 *
 * <AUTHOR>
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExpenseAfterSalesItemEntity extends BaseEntity {
    /**
     * 主键
     */
    private Long expenseAfterSalesItemId;

    /**
     * 费用售后表主键ID
     */
    private Long expenseAfterSalesId;

    /**
     * 采购费用明细主键ID
     */
    private Integer buyorderExpenseItemId;

    /**
     * 订货号
     */
    private String sku;

    /**
     * 退货数量
     */
    private Integer returnNum;

    /**
     * 是否删除 0否 1是
     */
    private Integer isDelete;

    /**
     * 更新备注
     */
    private String updateRemark;

}