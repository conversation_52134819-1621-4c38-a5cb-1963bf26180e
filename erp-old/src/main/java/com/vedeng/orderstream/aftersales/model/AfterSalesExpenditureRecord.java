package com.vedeng.orderstream.aftersales.model;

import java.math.BigDecimal;

public class AfterSalesExpenditureRecord {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_AFTER_SALES_EXPENDITURE_RECORD.RECORD_ID
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    private Integer recordId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_AFTER_SALES_EXPENDITURE_RECORD.AFTER_SALES_ID
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    private Integer afterSalesId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_AFTER_SALES_EXPENDITURE_RECORD.TYPE
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    private Integer type;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_AFTER_SALES_EXPENDITURE_RECORD.AMOUNT
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    private BigDecimal amount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_AFTER_SALES_EXPENDITURE_RECORD.ORG_ID
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    private Integer orgId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_AFTER_SALES_EXPENDITURE_RECORD.PAYER
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    private String payer;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_AFTER_SALES_EXPENDITURE_RECORD.ADD_TIME
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    private Long addTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_AFTER_SALES_EXPENDITURE_RECORD.MODE_TIME
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    private Long modeTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_AFTER_SALES_EXPENDITURE_RECORD.CREATOR
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    private Integer creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_AFTER_SALES_EXPENDITURE_RECORD.UPDATER
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    private Integer updater;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_AFTER_SALES_EXPENDITURE_RECORD.IS_DELETE
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    private Boolean isDelete;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_AFTER_SALES_EXPENDITURE_RECORD.REMARK
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    private String remark;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_AFTER_SALES_EXPENDITURE_RECORD.RECORD_ID
     *
     * @return the value of T_AFTER_SALES_EXPENDITURE_RECORD.RECORD_ID
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    public Integer getRecordId() {
        return recordId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_AFTER_SALES_EXPENDITURE_RECORD.RECORD_ID
     *
     * @param recordId the value for T_AFTER_SALES_EXPENDITURE_RECORD.RECORD_ID
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    public void setRecordId(Integer recordId) {
        this.recordId = recordId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_AFTER_SALES_EXPENDITURE_RECORD.AFTER_SALES_ID
     *
     * @return the value of T_AFTER_SALES_EXPENDITURE_RECORD.AFTER_SALES_ID
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    public Integer getAfterSalesId() {
        return afterSalesId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_AFTER_SALES_EXPENDITURE_RECORD.AFTER_SALES_ID
     *
     * @param afterSalesId the value for T_AFTER_SALES_EXPENDITURE_RECORD.AFTER_SALES_ID
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    public void setAfterSalesId(Integer afterSalesId) {
        this.afterSalesId = afterSalesId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_AFTER_SALES_EXPENDITURE_RECORD.TYPE
     *
     * @return the value of T_AFTER_SALES_EXPENDITURE_RECORD.TYPE
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    public Integer getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_AFTER_SALES_EXPENDITURE_RECORD.TYPE
     *
     * @param type the value for T_AFTER_SALES_EXPENDITURE_RECORD.TYPE
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_AFTER_SALES_EXPENDITURE_RECORD.AMOUNT
     *
     * @return the value of T_AFTER_SALES_EXPENDITURE_RECORD.AMOUNT
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    public BigDecimal getAmount() {
        return amount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_AFTER_SALES_EXPENDITURE_RECORD.AMOUNT
     *
     * @param amount the value for T_AFTER_SALES_EXPENDITURE_RECORD.AMOUNT
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_AFTER_SALES_EXPENDITURE_RECORD.ORG_ID
     *
     * @return the value of T_AFTER_SALES_EXPENDITURE_RECORD.ORG_ID
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    public Integer getOrgId() {
        return orgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_AFTER_SALES_EXPENDITURE_RECORD.ORG_ID
     *
     * @param orgId the value for T_AFTER_SALES_EXPENDITURE_RECORD.ORG_ID
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_AFTER_SALES_EXPENDITURE_RECORD.PAYER
     *
     * @return the value of T_AFTER_SALES_EXPENDITURE_RECORD.PAYER
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    public String getPayer() {
        return payer;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_AFTER_SALES_EXPENDITURE_RECORD.PAYER
     *
     * @param payer the value for T_AFTER_SALES_EXPENDITURE_RECORD.PAYER
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    public void setPayer(String payer) {
        this.payer = payer;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_AFTER_SALES_EXPENDITURE_RECORD.ADD_TIME
     *
     * @return the value of T_AFTER_SALES_EXPENDITURE_RECORD.ADD_TIME
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    public Long getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_AFTER_SALES_EXPENDITURE_RECORD.ADD_TIME
     *
     * @param addTime the value for T_AFTER_SALES_EXPENDITURE_RECORD.ADD_TIME
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_AFTER_SALES_EXPENDITURE_RECORD.MODE_TIME
     *
     * @return the value of T_AFTER_SALES_EXPENDITURE_RECORD.MODE_TIME
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    public Long getModeTime() {
        return modeTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_AFTER_SALES_EXPENDITURE_RECORD.MODE_TIME
     *
     * @param modeTime the value for T_AFTER_SALES_EXPENDITURE_RECORD.MODE_TIME
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    public void setModeTime(Long modeTime) {
        this.modeTime = modeTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_AFTER_SALES_EXPENDITURE_RECORD.CREATOR
     *
     * @return the value of T_AFTER_SALES_EXPENDITURE_RECORD.CREATOR
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_AFTER_SALES_EXPENDITURE_RECORD.CREATOR
     *
     * @param creator the value for T_AFTER_SALES_EXPENDITURE_RECORD.CREATOR
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_AFTER_SALES_EXPENDITURE_RECORD.UPDATER
     *
     * @return the value of T_AFTER_SALES_EXPENDITURE_RECORD.UPDATER
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    public Integer getUpdater() {
        return updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_AFTER_SALES_EXPENDITURE_RECORD.UPDATER
     *
     * @param updater the value for T_AFTER_SALES_EXPENDITURE_RECORD.UPDATER
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_AFTER_SALES_EXPENDITURE_RECORD.IS_DELETE
     *
     * @return the value of T_AFTER_SALES_EXPENDITURE_RECORD.IS_DELETE
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    public Boolean getIsDelete() {
        return isDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_AFTER_SALES_EXPENDITURE_RECORD.IS_DELETE
     *
     * @param isDelete the value for T_AFTER_SALES_EXPENDITURE_RECORD.IS_DELETE
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    public void setIsDelete(Boolean isDelete) {
        this.isDelete = isDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_AFTER_SALES_EXPENDITURE_RECORD.REMARK
     *
     * @return the value of T_AFTER_SALES_EXPENDITURE_RECORD.REMARK
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    public String getRemark() {
        return remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_AFTER_SALES_EXPENDITURE_RECORD.REMARK
     *
     * @param remark the value for T_AFTER_SALES_EXPENDITURE_RECORD.REMARK
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }
}