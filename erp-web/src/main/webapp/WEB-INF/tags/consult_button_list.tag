<%@ tag language="java" pageEncoding="UTF-8"%>
<%@attribute name="consultRelatedId" type="java.lang.Integer" required="true" %>
<%@attribute name="consultType" type="java.lang.Integer" required="true" %>
<%@attribute name="goodsCount" type="java.lang.Integer" required="true" %>
<%@attribute name="roleType" type="java.lang.Integer" required="true" %>
<%@attribute name="orderStatus" type="java.lang.Integer" required="true" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>

<c:if test="${roleType == 0}">
    <button type="button" class="bt-bg-style bg-light-green bt-small mr10" onclick="consultManagerPage(${goodsCount},${orderStatus},${consultType})">咨询主管</button>
    <span id="consultManager" class="pop-new-data" layerParams='{"width":"40%","height":"200px","title":"咨询主管","link":"/order/quote/consult/managerPage.do?consultRelatedId=${consultRelatedId}&consultType=${consultType}"}'></span>
    <button type="button" class="bt-bg-style bg-light-green bt-small mr10" onclick="consultSupplyPage(${goodsCount},${orderStatus},${consultType})">咨询供应链</button>
    <span id="consultSupply" class="pop-new-data" layerParams='{"width":"60%","height":"400px","title":"咨询供应链","link":"/order/quote/consult/supplyPage.do?consultRelatedId=${consultRelatedId}&consultType=${consultType}"}'></span>
</c:if>
<c:choose>
    <%--        销售直接上级--%>
    <c:when test="${roleType == 1}">

        <button type="button" class="bt-bg-style bg-light-green bt-small mr10" onclick="managerOfSalerReplyConsult(${goodsCount},${consultRelatedId},${consultType});">回复</button>
    </c:when>
    <%--            供应链--%>
    <c:when test="${roleType == 2}">
        <button type="button" class="bt-bg-style bg-light-green bt-small mr10" onclick="replyQuoteConsult('${consultRelatedId}',2);">回复</button>
    </c:when>
</c:choose>

<script>

    //获取订单/报价单的关闭状态
    function  orderCloseStatus(status,orderType) {
        if (orderType === 1 && status === 3){
            return 1;
        }
        if (orderType === 2 && status === 2){
            return 1;
        }
        return 0;
    }

    function consultSupplyPage(num,status,consultType) {

        if (orderCloseStatus(status,consultType) === 1){
            layer.alert("报价单/订单已关闭，无法咨询");
            return false;
        }
        if (num <= 0){
            layer.alert("报价单/订单至少含有一个产品才可发起咨询");
            return false;
        }
        $("#consultSupply").click();
    }

    function consultManagerPage(num,status,consultType) {
        if (orderCloseStatus(status,consultType) === 1){
            layer.alert("报价单/订单已关闭，无法咨询");
            return false;
        }
        if (num <= 0){
            layer.alert("报价单/订单至少含有一个产品才可发起咨询");
            return false;
        }
        $("#consultManager").click();
    }


    function managerOfSalerReplyConsult(num,consultRelatedId,consultType) {

        if (num <= 0){
            layer.alert("报价单/订单至少含有一个产品才可发起咨询");
            return false;
        }
        if ($("#managerReplyContent").val().length == 0){
            layer.alert("咨询回复内容不可以为空")
            return false;
        }
        $.ajax({
            url:"/order/quote/consult/manager/reply.do",
            data: JSON.stringify({
                consultRelatedId: consultRelatedId,
                consultType: consultType,
                consultContent: $("#managerReplyContent").val()
            })
            ,
            type : "POST",
            contentType: 'application/json',
            dataType : "json",
            success : function(data) {
                if (data.code == 0){
                    layer.close()
                    window.location.reload();
                } else {
                    layer.alert(data.message);
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        })
    }

</script>

