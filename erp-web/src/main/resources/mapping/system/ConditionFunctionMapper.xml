<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.system.dao.ConditionFunctionMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.system.model.ConditionFunction" >
    <id column="CONDITION_FUNCTION_ID" property="conditionFunctionId" jdbcType="INTEGER" />
    <result column="NAME" property="name" jdbcType="VARCHAR" />
    <result column="CONDITION_FUNCTION_PARAMS" property="conditionFunctionParams" jdbcType="VARCHAR" />
    <result column="COMMENTS" property="comments" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    CONDITION_FUNCTION_ID, NAME, CONDITION_FUNCTION_PARAMS, COMMENTS
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_CONDITION_FUNCTION
    where CONDITION_FUNCTION_ID = #{conditionFunctionId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_CONDITION_FUNCTION
    where CONDITION_FUNCTION_ID = #{conditionFunctionId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.system.model.ConditionFunction" >
    insert into T_CONDITION_FUNCTION (CONDITION_FUNCTION_ID, NAME, CONDITION_FUNCTION_PARAMS, 
      COMMENTS)
    values (#{conditionFunctionId,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{conditionFunctionParams,jdbcType=VARCHAR}, 
      #{comments,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.system.model.ConditionFunction" >
    insert into T_CONDITION_FUNCTION
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="conditionFunctionId != null" >
        CONDITION_FUNCTION_ID,
      </if>
      <if test="name != null" >
        NAME,
      </if>
      <if test="conditionFunctionParams != null" >
        CONDITION_FUNCTION_PARAMS,
      </if>
      <if test="comments != null" >
        COMMENTS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="conditionFunctionId != null" >
        #{conditionFunctionId,jdbcType=INTEGER},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="conditionFunctionParams != null" >
        #{conditionFunctionParams,jdbcType=VARCHAR},
      </if>
      <if test="comments != null" >
        #{comments,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.system.model.ConditionFunction" >
    update T_CONDITION_FUNCTION
    <set >
      <if test="name != null" >
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="conditionFunctionParams != null" >
        CONDITION_FUNCTION_PARAMS = #{conditionFunctionParams,jdbcType=VARCHAR},
      </if>
      <if test="comments != null" >
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
    </set>
    where CONDITION_FUNCTION_ID = #{conditionFunctionId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.system.model.ConditionFunction" >
    update T_CONDITION_FUNCTION
    set NAME = #{name,jdbcType=VARCHAR},
      CONDITION_FUNCTION_PARAMS = #{conditionFunctionParams,jdbcType=VARCHAR},
      COMMENTS = #{comments,jdbcType=VARCHAR}
    where CONDITION_FUNCTION_ID = #{conditionFunctionId,jdbcType=INTEGER}
  </update>
</mapper>