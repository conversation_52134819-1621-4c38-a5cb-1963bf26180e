package com.vedeng.util;

import io.jsonwebtoken.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

@Slf4j
public class JwtUtilForAi {

    static String SECRETKEY = "KJHUhjjJYgYUllVbXhKDHXhkSyHjlNiVkYzWTBac1Yxkjhuad";

    /**
     * 由字符串生成加密key
     *
     * @return
     */
    public static SecretKey generalKey(String stringKey) {
        byte[] encodedKey = Base64.decodeBase64(stringKey);
        SecretKey key = new SecretKeySpec(encodedKey, 0, encodedKey.length, "HS256");
        return key;
    }

    /**
     * 创建jwt
     * * @return jwt token
     *
     * @throws Exception
     */
    public static String createJWT(String jobNumber, String appCode, long expirationDay) {
        JwtBuilder builder = Jwts.builder().claim("jobNumber", jobNumber).claim("appCode", appCode)
                .signWith(SignatureAlgorithm.HS256, SECRETKEY)
                .setExpiration((Date.from(LocalDateTime.now().plusDays(expirationDay).atZone(ZoneId.systemDefault()).toInstant())));
        return builder.compact();
    }

    /**
     * 解密jwt，获取实体
     *
     * @param jwt
     */
    public static Claims parseJWT(String jwt) {
        Claims claims = Jwts.parser().setSigningKey(SECRETKEY).parseClaimsJws(jwt).getBody();
        return claims;
    }

    /**
     * 实例演示
     */
    public static void main(String[] args) {
        try {
            //20天过期
            String token = createJWT("1339", "erp", 20L);
            System.out.println(token);
            Claims claims = parseJWT(token);
            System.out.println("解析jwt：jobNumber=" + claims.get("jobNumber") + "=========appCode=" + claims.get("appCode"));
            System.out.println(claims.getExpiration() + "///" + claims.getExpiration().getTime());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
