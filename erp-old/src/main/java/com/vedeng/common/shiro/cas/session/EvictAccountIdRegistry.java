package com.vedeng.common.shiro.cas.session;

import java.util.Collection;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class EvictAccountIdRegistry {

    private static final Integer DEFAULT_MAX_QUEUE_SIZE = 1000;

    private final BlockingQueue<Integer> internalQueue = new LinkedBlockingQueue<>(DEFAULT_MAX_QUEUE_SIZE);

    private final static EvictAccountIdRegistry INSTANCE = new EvictAccountIdRegistry();

    private EvictAccountIdRegistry() {
    }

    public static EvictAccountIdRegistry getInstance() {
        return INSTANCE;
    }

    public void add(Integer id) {
        if (id == null) {
            return;
        }

        internalQueue.add(id);
    }

    public Collection<Integer> takeAll() {
        Set<Integer> accountIdToUse = new HashSet<>();
        internalQueue.drainTo(accountIdToUse);
        return accountIdToUse;
    }


    public int size() {
        return internalQueue.size();
    }

}
