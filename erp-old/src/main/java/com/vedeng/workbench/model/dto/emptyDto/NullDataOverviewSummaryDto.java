package com.vedeng.workbench.model.dto.emptyDto;

import com.vedeng.workbench.model.dto.DataOverviewSummaryDto;

import java.math.BigDecimal;

/**
 * 防止空指针
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2020/11/7 22:05.
 * @author: <PERSON><PERSON>.
 */
public class NullDataOverviewSummaryDto extends DataOverviewSummaryDto {

    public NullDataOverviewSummaryDto() {
        this.setAvrBussinessAmount(BigDecimal.ZERO);
        this.setName("默认");
        this.setThisWeekBussinessAmount(BigDecimal.ZERO);
        this.setThisWeekBussinessNum(0);
        this.setTotalBussinessAmount(BigDecimal.ZERO);
        this.setTotalBussinessNum(0);
        this.setYesterdayBussinessAmount(BigDecimal.ZERO);
        this.setYesterdayBussinessNum(0);
    }
}
