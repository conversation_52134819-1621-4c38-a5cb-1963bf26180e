package com.wms.model.po;

import java.util.List;

public class WmsLendOutOrder extends WmsOutputOrder{
    private String goodsName;
    private String skuNo;
    private String brandName;
    private String model;
    private Long goodsId;

    private List<WmsLendOutOrder> wmsLendOutOrder;


    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getSkuNo() {
        return skuNo;
    }

    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public Long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    public List<WmsLendOutOrder> getWmsLendOutOrder() {
        return wmsLendOutOrder;
    }

    public void setWmsLendOutOrder(List<WmsLendOutOrder> wmsLendOutOrder) {
        this.wmsLendOutOrder = wmsLendOutOrder;
    }
}
