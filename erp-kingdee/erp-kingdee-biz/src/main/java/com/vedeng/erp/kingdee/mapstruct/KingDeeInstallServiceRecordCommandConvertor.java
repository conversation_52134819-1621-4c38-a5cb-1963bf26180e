package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeeInstallServiceRecordCommand;
import com.vedeng.erp.kingdee.dto.KingDeeInstallServiceRecordDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 销售安调 dto
 * @date
 */
@Mapper(componentModel = "spring")
public interface KingDeeInstallServiceRecordCommandConvertor extends BaseCommandMapStruct<KingDeeInstallServiceRecordCommand, KingDeeInstallServiceRecordDto> {
    @Mapping(target = "fid", source = "fid")
    @Mapping(target = "FBillNo", source = "FBillNo")
    @Mapping(target = "FQzokOrgId.FNumber", source = "FQzokOrgid")
    @Mapping(target = "f_QZOK_YSSJ", source = "FQzokYssj")
    @Mapping(target = "f_QZOK_Date", source = "FQzokDate")
    @Mapping(target = "f_QZOK_BCFWSL", source = "FQzokBcfwsl")
    @Mapping(target = "f_QZOK_QSSJ", source = "FQzokQssj")
    @Mapping(target = "f_QZOK_YSFS", source = "FQzokYsfs")
    @Mapping(target = "f_QZOK_YSJL", source = "FQzokYsjl")
    @Mapping(target = "f_QZOK_YSDDH", source = "FQzokYsddh")
    @Mapping(target = "f_QZOK_GSYWDH", source = "FQzokGsywdh")
    @Mapping(target = "f_QZOK_YWLX", source = "FQzokYwlx")
    @Mapping(target = "f_QZOK_WLBM.FNumber", source = "FQzokWlbm")
    @Mapping(target = "f_QZOK_XLH", source = "FQzokXlh")
    @Mapping(target = "f_QZOK_FID", source = "FQzokFid")
    @Mapping(target = "f_QZOK_FEntryId", source = "FQzokFentryid")
    @Mapping(target = "f_QZOK_CKDH", source = "FQzokCkdh")
    KingDeeInstallServiceRecordCommand toCommand(KingDeeInstallServiceRecordDto dto);

}
