package com.vedeng.goods.model;

import lombok.Builder;

import java.util.Date;

@Builder
public class LogCheckGenerate {

    /**
     *提交人id
     */
    private Integer submitUserId;

    /**
     *提交人名称
     */
    private String submitUserName;

    public Integer getSubmitUserId() {
        return submitUserId;
    }

    public void setSubmitUserId(Integer submitUserId) {
        this.submitUserId = submitUserId;
    }

    public String getSubmitUserName() {
        return submitUserName;
    }

    public void setSubmitUserName(String submitUserName) {
        this.submitUserName = submitUserName;
    }

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_LOG_CHECK.LOG_ID
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    private Integer logId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_LOG_CHECK.LOG_TYPE
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    private Integer logType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_LOG_CHECK.LOG_BIZ_ID
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    private Integer logBizId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_LOG_CHECK.LOG_STATUS
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    private Integer logStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_LOG_CHECK.LOG_MESSAGE
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    private String logMessage;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_LOG_CHECK.ADD_TIME
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    private Date addTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_LOG_CHECK.CREATOR
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    private Integer creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_LOG_CHECK.CREATOR_NAME
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    private String creatorName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_LOG_CHECK.LOG_STATUS_NAME
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    private String logStatusName;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_LOG_CHECK.LOG_ID
     *
     * @return the value of V_LOG_CHECK.LOG_ID
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    public Integer getLogId() {
        return logId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_LOG_CHECK.LOG_ID
     *
     * @param logId the value for V_LOG_CHECK.LOG_ID
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    public void setLogId(Integer logId) {
        this.logId = logId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_LOG_CHECK.LOG_TYPE
     *
     * @return the value of V_LOG_CHECK.LOG_TYPE
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    public Integer getLogType() {
        return logType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_LOG_CHECK.LOG_TYPE
     *
     * @param logType the value for V_LOG_CHECK.LOG_TYPE
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    public void setLogType(Integer logType) {
        this.logType = logType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_LOG_CHECK.LOG_BIZ_ID
     *
     * @return the value of V_LOG_CHECK.LOG_BIZ_ID
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    public Integer getLogBizId() {
        return logBizId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_LOG_CHECK.LOG_BIZ_ID
     *
     * @param logBizId the value for V_LOG_CHECK.LOG_BIZ_ID
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    public void setLogBizId(Integer logBizId) {
        this.logBizId = logBizId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_LOG_CHECK.LOG_STATUS
     *
     * @return the value of V_LOG_CHECK.LOG_STATUS
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    public Integer getLogStatus() {
        return logStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_LOG_CHECK.LOG_STATUS
     *
     * @param logStatus the value for V_LOG_CHECK.LOG_STATUS
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    public void setLogStatus(Integer logStatus) {
        this.logStatus = logStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_LOG_CHECK.LOG_MESSAGE
     *
     * @return the value of V_LOG_CHECK.LOG_MESSAGE
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    public String getLogMessage() {
        return logMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_LOG_CHECK.LOG_MESSAGE
     *
     * @param logMessage the value for V_LOG_CHECK.LOG_MESSAGE
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    public void setLogMessage(String logMessage) {
        this.logMessage = logMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_LOG_CHECK.ADD_TIME
     *
     * @return the value of V_LOG_CHECK.ADD_TIME
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    public Date getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_LOG_CHECK.ADD_TIME
     *
     * @param addTime the value for V_LOG_CHECK.ADD_TIME
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_LOG_CHECK.CREATOR
     *
     * @return the value of V_LOG_CHECK.CREATOR
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_LOG_CHECK.CREATOR
     *
     * @param creator the value for V_LOG_CHECK.CREATOR
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_LOG_CHECK.CREATOR_NAME
     *
     * @return the value of V_LOG_CHECK.CREATOR_NAME
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    public String getCreatorName() {
        return creatorName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_LOG_CHECK.CREATOR_NAME
     *
     * @param creatorName the value for V_LOG_CHECK.CREATOR_NAME
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_LOG_CHECK.LOG_STATUS_NAME
     *
     * @return the value of V_LOG_CHECK.LOG_STATUS_NAME
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    public String getLogStatusName() {
        return logStatusName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_LOG_CHECK.LOG_STATUS_NAME
     *
     * @param logStatusName the value for V_LOG_CHECK.LOG_STATUS_NAME
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    public void setLogStatusName(String logStatusName) {
        this.logStatusName = logStatusName;
    }

    public LogCheckGenerate() {
    }

    public LogCheckGenerate(Integer submitUserId, String submitUserName, Integer logId, Integer logType, Integer logBizId, Integer logStatus, String logMessage, Date addTime, Integer creator, String creatorName, String logStatusName) {
        this.submitUserId = submitUserId;
        this.submitUserName = submitUserName;
        this.logId = logId;
        this.logType = logType;
        this.logBizId = logBizId;
        this.logStatus = logStatus;
        this.logMessage = logMessage;
        this.addTime = addTime;
        this.creator = creator;
        this.creatorName = creatorName;
        this.logStatusName = logStatusName;
    }
}