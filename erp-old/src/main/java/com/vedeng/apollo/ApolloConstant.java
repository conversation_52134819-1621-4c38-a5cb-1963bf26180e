package com.vedeng.apollo;

import com.ctrip.framework.apollo.ConfigService;

public class ApolloConstant {
    private static String resourceVersionKey = null;

    private static String lxcrmUrl = null;  //erp需要跳转到lxcrm的地址 示例值：http://qa.lxcrm.ivedeng.com
    static {
        if(resourceVersionKey == null){
            resourceVersionKey = ConfigService.getAppConfig().getProperty("resourceVersionKey", "");
        }
        if(lxcrmUrl == null){
            lxcrmUrl = ConfigService.getAppConfig().getProperty("lxcrmUrl", "");
        }
    }

    public static String getResourceVersionKey() {
        return resourceVersionKey;
    }

    public static void setResourceVersionKey(String resourceVersionKey) {
        ApolloConstant.resourceVersionKey = resourceVersionKey;
    }


    public static String getLxcrmUrl() {
        return lxcrmUrl;
    }

    public static void setLxcrmUrl(String lxcrmUrl) {
        ApolloConstant.lxcrmUrl = lxcrmUrl;
    }
}
