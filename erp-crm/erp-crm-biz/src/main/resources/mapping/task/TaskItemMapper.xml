<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.crm.task.mapper.TaskItemMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.crm.task.domain.entity.TaskItemEntity">
        <!--@mbg.generated-->
        <!--@Table T_TASK_ITEM-->
        <id column="TASK_ITEM_ID" jdbcType="BIGINT" property="taskItemId"/>
        <result column="TASK_ID" jdbcType="BIGINT" property="taskId"/>
        <result column="TASK_USER" jdbcType="INTEGER" property="taskUser"/>
        <result column="TASK_USER_NAME" jdbcType="VARCHAR" property="taskUserName"/>
        <result column="DONE_STATUS" jdbcType="INTEGER" property="doneStatus"/>
        <result column="DONE_USER" jdbcType="INTEGER" property="doneUser"/>
        <result column="DONE_USER_NAME" jdbcType="VARCHAR" property="doneUserName"/>
        <result column="DONE_TIME" jdbcType="TIMESTAMP" property="doneTime"/>
        <result column="DONE_REMARK" jdbcType="VARCHAR" property="doneRemark"/>
        <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName"/>
        <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark"/>
        <result column="IS_DEADLINE_REMINDER" jdbcType="INTEGER" property="isDeadlineReminder"/>
        <result column="IS_TIMEOUT_REMINDER" jdbcType="INTEGER" property="isTimeoutReminder"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        TASK_ITEM_ID,
        TASK_ID,
        TASK_USER,
        TASK_USER_NAME,
        DONE_STATUS,
        DONE_USER,
        DONE_USER_NAME,
        DONE_TIME,
        DONE_REMARK,
        IS_DELETE,
        ADD_TIME,
        MOD_TIME,
        CREATOR,
        CREATOR_NAME,
        UPDATER,
        UPDATER_NAME,
        UPDATE_REMARK,
        IS_DEADLINE_REMINDER,
        IS_TIMEOUT_REMINDER
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from T_TASK_ITEM
        where TASK_ITEM_ID = #{taskItemId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete
        from T_TASK_ITEM
        where TASK_ITEM_ID = #{taskItemId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="TASK_ITEM_ID" keyProperty="taskItemId"
            parameterType="com.vedeng.crm.task.domain.entity.TaskItemEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_TASK_ITEM (TASK_ID, TASK_USER, TASK_USER_NAME,
                                 DONE_STATUS, DONE_USER, DONE_USER_NAME,
                                 DONE_TIME, DONE_REMARK, IS_DELETE,
                                 ADD_TIME, MOD_TIME, CREATOR,
                                 CREATOR_NAME, UPDATER, UPDATER_NAME,
                                 UPDATE_REMARK, IS_DEADLINE_REMINDER, IS_TIMEOUT_REMINDER)
        values (#{taskId,jdbcType=BIGINT}, #{taskUser,jdbcType=INTEGER}, #{taskUserName,jdbcType=VARCHAR},
                #{doneStatus,jdbcType=INTEGER}, #{doneUser,jdbcType=INTEGER}, #{doneUserName,jdbcType=VARCHAR},
                #{doneTime,jdbcType=TIMESTAMP}, #{doneRemark,jdbcType=VARCHAR}, #{isDelete,jdbcType=INTEGER},
                #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER},
                #{creatorName,jdbcType=VARCHAR}, #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR},
                #{updateRemark,jdbcType=VARCHAR}, #{isDeadlineReminder,jdbcType=INTEGER}, #{isTimeoutReminder,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" keyColumn="TASK_ITEM_ID" keyProperty="taskItemId"
            parameterType="com.vedeng.crm.task.domain.entity.TaskItemEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_TASK_ITEM
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">
                TASK_ID,
            </if>
            <if test="taskUser != null">
                TASK_USER,
            </if>
            <if test="taskUserName != null and taskUserName != ''">
                TASK_USER_NAME,
            </if>
            <if test="doneStatus != null">
                DONE_STATUS,
            </if>
            <if test="doneUser != null">
                DONE_USER,
            </if>
            <if test="doneUserName != null and doneUserName != ''">
                DONE_USER_NAME,
            </if>
            <if test="doneTime != null">
                DONE_TIME,
            </if>
            <if test="doneRemark != null and doneRemark != ''">
                DONE_REMARK,
            </if>
            <if test="isDelete != null">
                IS_DELETE,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="creatorName != null and creatorName != ''">
                CREATOR_NAME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="updaterName != null and updaterName != ''">
                UPDATER_NAME,
            </if>
            <if test="updateRemark != null and updateRemark != ''">
                UPDATE_REMARK,
            </if>
            <if test="isDeadlineReminder != null">
                IS_DEADLINE_REMINDER,
            </if>
            <if test="isTimeoutReminder != null">
                IS_TIMEOUT_REMINDER,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">
                #{taskId,jdbcType=BIGINT},
            </if>
            <if test="taskUser != null">
                #{taskUser,jdbcType=INTEGER},
            </if>
            <if test="taskUserName != null and taskUserName != ''">
                #{taskUserName,jdbcType=VARCHAR},
            </if>
            <if test="doneStatus != null">
                #{doneStatus,jdbcType=INTEGER},
            </if>
            <if test="doneUser != null">
                #{doneUser,jdbcType=INTEGER},
            </if>
            <if test="doneUserName != null and doneUserName != ''">
                #{doneUserName,jdbcType=VARCHAR},
            </if>
            <if test="doneTime != null">
                #{doneTime,jdbcType=TIMESTAMP},
            </if>
            <if test="doneRemark != null and doneRemark != ''">
                #{doneRemark,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null and creatorName != ''">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="updaterName != null and updaterName != ''">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateRemark != null and updateRemark != ''">
                #{updateRemark,jdbcType=VARCHAR},
            </if>
            <if test="isDeadlineReminder != null">
                #{isDeadlineReminder,jdbcType=INTEGER},
            </if>
            <if test="isTimeoutReminder != null">
                #{isTimeoutReminder,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.crm.task.domain.entity.TaskItemEntity">
        <!--@mbg.generated-->
        update T_TASK_ITEM
        <set>
            <if test="taskId != null">
                TASK_ID = #{taskId,jdbcType=BIGINT},
            </if>
            <if test="taskUser != null">
                TASK_USER = #{taskUser,jdbcType=INTEGER},
            </if>
            <if test="taskUserName != null and taskUserName != ''">
                TASK_USER_NAME = #{taskUserName,jdbcType=VARCHAR},
            </if>
            <if test="doneStatus != null">
                DONE_STATUS = #{doneStatus,jdbcType=INTEGER},
            </if>
            <if test="doneUser != null">
                DONE_USER = #{doneUser,jdbcType=INTEGER},
            </if>
            <if test="doneUserName != null and doneUserName != ''">
                DONE_USER_NAME = #{doneUserName,jdbcType=VARCHAR},
            </if>
            <if test="doneTime != null">
                DONE_TIME = #{doneTime,jdbcType=TIMESTAMP},
            </if>
            <if test="doneRemark != null and doneRemark != ''">
                DONE_REMARK = #{doneRemark,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                IS_DELETE = #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null and creatorName != ''">
                CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="updaterName != null and updaterName != ''">
                UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateRemark != null and updateRemark != ''">
                UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
            </if>
            <if test="isDeadlineReminder != null">
                IS_DEADLINE_REMINDER = #{isDeadlineReminder,jdbcType=INTEGER},
            </if>
            <if test="isTimeoutReminder != null">
                IS_TIMEOUT_REMINDER = #{isTimeoutReminder,jdbcType=INTEGER},
            </if>
        </set>
        where TASK_ITEM_ID = #{taskItemId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.crm.task.domain.entity.TaskItemEntity">
        <!--@mbg.generated-->
        update T_TASK_ITEM
        set TASK_ID        = #{taskId,jdbcType=BIGINT},
            TASK_USER      = #{taskUser,jdbcType=INTEGER},
            TASK_USER_NAME = #{taskUserName,jdbcType=VARCHAR},
            DONE_STATUS    = #{doneStatus,jdbcType=INTEGER},
            DONE_USER      = #{doneUser,jdbcType=INTEGER},
            DONE_USER_NAME = #{doneUserName,jdbcType=VARCHAR},
            DONE_TIME      = #{doneTime,jdbcType=TIMESTAMP},
            DONE_REMARK    = #{doneRemark,jdbcType=VARCHAR},
            IS_DELETE      = #{isDelete,jdbcType=INTEGER},
            ADD_TIME       = #{addTime,jdbcType=TIMESTAMP},
            MOD_TIME       = #{modTime,jdbcType=TIMESTAMP},
            CREATOR        = #{creator,jdbcType=INTEGER},
            CREATOR_NAME   = #{creatorName,jdbcType=VARCHAR},
            UPDATER        = #{updater,jdbcType=INTEGER},
            UPDATER_NAME   = #{updaterName,jdbcType=VARCHAR},
            UPDATE_REMARK  = #{updateRemark,jdbcType=VARCHAR},
            IS_DEADLINE_REMINDER = #{isDeadlineReminder,jdbcType=INTEGER},
            IS_TIMEOUT_REMINDER = #{isTimeoutReminder,jdbcType=INTEGER}
        where TASK_ITEM_ID = #{taskItemId,jdbcType=BIGINT}
    </update>

<!--auto generated by MybatisCodeHelper on 2024-07-31-->
    <select id="findAllByTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_TASK_ITEM
        where TASK_ID=#{taskId,jdbcType=BIGINT}
        AND IS_DELETE = 0
    </select>


    <select id="findAllByTaskIdNotDone" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_TASK_ITEM
        where TASK_ID=#{taskId,jdbcType=BIGINT}
        AND DONE_STATUS = 0
    </select>
    
    <select id="findDeadlineReminder" resultType="com.vedeng.crm.task.domain.dto.TaskReminder">
        SELECT DISTINCT TT.TASK_ID,
                        TTI.TASK_ITEM_ID,
                        TTI.TASK_USER,
                        TTI.TASK_USER_NAME,
                        TT.MAIN_TASK_TYPE,
                        TT.BIZ_TYPE,
                        TT.BIZ_ID,
                        TT.BIZ_NO,
                        TU.NUMBER
        FROM T_TASK_ITEM TTI
                 INNER JOIN T_TASK TT ON TT.TASK_ID = TTI.TASK_ID
                 INNER JOIN T_USER TU on TU.USER_ID = TTI.TASK_USER
        WHERE TT.DONE_STATUS = 0
          AND TTI.DONE_STATUS = 0
          AND TT.IS_DELETE = 0
          AND TTI.IS_DELETE = 0
          AND TTI.IS_DEADLINE_REMINDER = 0
          AND (
            (TT.MAIN_TASK_TYPE = 1 AND TT.DEADLINE - #{productSchemeWarningTime,jdbcType=INTEGER} &lt; NOW()) OR
            (TT.MAIN_TASK_TYPE = 2 AND TT.DEADLINE - #{itemInquiryWarningTime,jdbcType=INTEGER} &lt; NOW()) OR
            (TT.MAIN_TASK_TYPE = 3 AND TT.DEADLINE - #{generalInquiryWarningTime,jdbcType=INTEGER} &lt; NOW()) OR
            (TT.MAIN_TASK_TYPE = 6 AND TT.DEADLINE - #{quoteWarningTime,jdbcType=INTEGER} &lt; NOW())
            )
    </select>
    
<!--auto generated by MybatisCodeHelper on 2024-08-09-->
    <update id="updateIsDeadlineReminderByTaskItemId">
        update T_TASK_ITEM
        set IS_DEADLINE_REMINDER=#{updatedIsDeadlineReminder,jdbcType=INTEGER}
        where TASK_ITEM_ID=#{taskItemId,jdbcType=BIGINT}
    </update>
    
<!--auto generated by MybatisCodeHelper on 2024-08-09-->
    <update id="updateIsTimeoutReminderByTaskItemId">
        update T_TASK_ITEM
        set IS_TIMEOUT_REMINDER=#{updatedIsTimeoutReminder,jdbcType=INTEGER}
        where TASK_ITEM_ID=#{taskItemId,jdbcType=BIGINT}
    </update>


    <select id="findAlertBussinessChanceTask" resultType="com.vedeng.crm.task.domain.dto.TaskReminder">
        <![CDATA[
        SELECT DISTINCT TT.TASK_ID,
                        TTI.TASK_ITEM_ID,
                        TTI.TASK_USER,
                        TTI.TASK_USER_NAME,
                        TT.MAIN_TASK_TYPE,
                        TT.SUB_TASK_TYPE,
                        TT.BIZ_TYPE,
                        TT.BIZ_ID,
                        TT.BIZ_NO,
                        TU.NUMBER
        FROM T_TASK_ITEM TTI
                 INNER JOIN T_TASK TT ON TT.TASK_ID = TTI.TASK_ID
                 INNER JOIN T_USER TU on TU.USER_ID = TTI.TASK_USER
        WHERE TT.DEADLINE <= now()
          AND TT.DONE_STATUS = 0
          AND TT.IS_DELETE = 0
          AND TT.MAIN_TASK_TYPE = 4
          AND TTI.IS_TIMEOUT_REMINDER = 0
        ]]>
    </select>

    <select id="findTimeoutReminder" resultType="com.vedeng.crm.task.domain.dto.TaskReminder">
        SELECT DISTINCT TT.TASK_ID,
                        TTI.TASK_ITEM_ID,
                        TTI.TASK_USER,
                        TTI.TASK_USER_NAME,
                        TT.MAIN_TASK_TYPE,
                        TT.BIZ_TYPE,
                        TT.BIZ_ID,
                        TT.BIZ_NO,
                        TU.NUMBER
        FROM T_TASK_ITEM TTI
                 INNER JOIN T_TASK TT ON TT.TASK_ID = TTI.TASK_ID
                 INNER JOIN T_USER TU on TU.USER_ID = TTI.TASK_USER
        WHERE TT.DONE_STATUS = 0
          AND TTI.DONE_STATUS = 0
          AND TT.IS_DELETE = 0
          AND TTI.IS_DELETE = 0
          AND TTI.IS_TIMEOUT_REMINDER = 0
          AND TT.MAIN_TASK_TYPE in (1, 2, 3 ,6)
          AND TT.DEADLINE &lt; now()
    </select>
    
    <select id="countMyTodoTimeout" resultType="int">
        SELECT count(DISTINCT TTI.TASK_ITEM_ID)
        FROM T_TASK_ITEM TTI
                 INNER JOIN T_TASK TT ON TT.TASK_ID = TTI.TASK_ID
        WHERE TT.DONE_STATUS = 0
          AND TTI.DONE_STATUS = 0
          AND TT.IS_DELETE = 0
          AND TTI.IS_DELETE = 0
          AND TTI.TASK_USER in
          <foreach collection="subUserIds" item="userId" open="(" separator="," close=")">
              #{userId,jdbcType=INTEGER}
          </foreach>
          AND TT.DEADLINE &lt; DATE_ADD(CURDATE(), INTERVAL 1 DAY)
    </select>
    
    <select id="countMyInitiativeTimeout" resultType="int">
        SELECT count(DISTINCT TT.TASK_ID)
        FROM T_TASK TT
        WHERE TT.DONE_STATUS = 0
          AND TT.IS_DELETE = 0
          AND TT.CREATOR in
          <foreach collection="subUserIds" item="userId" open="(" separator="," close=")">
              #{userId,jdbcType=INTEGER}
          </foreach>
          AND TT.DEADLINE &lt; DATE_ADD(CURDATE(), INTERVAL 1 DAY)
    </select>


    <!--auto generated by MybatisCodeHelper on 2024-09-25-->
    <select id="findByBizIdAndBizTypeAndMainTaskType" resultType="com.vedeng.crm.task.domain.dto.TaskItemDto">
        SELECT
        TTI.*
        FROM
        T_TASK_ITEM TTI
        LEFT JOIN
        T_TASK TT ON TTI.TASK_ID = TT.TASK_ID
        WHERE
        TT.BIZ_ID = #{bizId,jdbcType=INTEGER}
        AND TT.BIZ_TYPE = #{bizType,jdbcType=INTEGER}
        AND TT.MAIN_TASK_TYPE = 5
        AND TTI.DONE_STATUS = 0

    </select>


    <select id="findByBizIdAndBizTypeForCloseBiz" resultType="com.vedeng.crm.task.domain.dto.TaskItemDto">
        SELECT
        TTI.*
        FROM
        T_TASK_ITEM TTI
        LEFT JOIN
        T_TASK TT ON TTI.TASK_ID = TT.TASK_ID
        WHERE
        TT.BIZ_ID = #{bizId,jdbcType=INTEGER}
        AND TT.BIZ_TYPE IN
        <foreach collection="bizTypeList" separator="," open="(" close=")" item="bizType"  >
            #{bizType,jdbcType=INTEGER}
        </foreach>
        AND TTI.DONE_STATUS = 0
    </select>

</mapper>