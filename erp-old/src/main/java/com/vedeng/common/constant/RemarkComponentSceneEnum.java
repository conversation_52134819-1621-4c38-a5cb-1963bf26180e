package com.vedeng.common.constant;

import com.vedeng.common.util.StringUtil;

/**
 * @Description:
 * @Author:       davis
 * @Date:         2021/4/16 下午4:43
 * @Version:      1.0
 */
public enum RemarkComponentSceneEnum {
    SALE_ORDER(0 , "销售单"),
    BUSINESS_CHANCE(1, "商机")
    ;

    RemarkComponentSceneEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private Integer code;

    private String msg;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    /**
     * 根据编码获取编码信息
     * @param code
     * @return
     */
    public static String codeToMessage(Integer code){
        if(code == null){
            return "";
        }
        for(RemarkComponentSceneEnum remarkComponentSceneEnum : RemarkComponentSceneEnum.values()){
            if(code.equals(remarkComponentSceneEnum.getCode())){
                return remarkComponentSceneEnum.getMsg();
            }
        }
        return "";
    }

}
