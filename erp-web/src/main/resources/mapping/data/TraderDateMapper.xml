<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.newtask.data.dao.TraderDateMapper">
    <select id="getTraderId" resultType="Integer">
        select distinct b.TRADER_ID   FROM
                T_AFTER_SALES a
                LEFT JOIN T_SALEORDER b ON a.ORDER_ID = b.SALEORDER_ID
                LEFT JOIN T_AFTER_SALES_GOODS c ON a.AFTER_SALES_ID = c.AFTER_SALES_ID
                left join T_TRADER_CUSTOMER ttc on b.TRADER_ID =ttc.TRADER_ID

        WHERE
                a.SUBJECT_TYPE = 535
                AND a.TYPE = 539
                AND a.ATFER_SALES_STATUS = 2
                AND ttc.IS_ENABLE =1
                and b.TRADER_ID is not null
        <if test="startTime!=0 and startTime!=null and endTime!=0 and endTime!=null">
                and a.MOD_TIME >= #{startTime}
                and a.MOD_TIME &lt;= #{endTime}
        </if>

    </select>

    <select id="findAfterSaleCountAndAmount" resultType="Map">
        select
        COUNT( * ) AS afterSaleCount,
                sum( c.NUM * c.PRICE ) AS afterSaleTotalAmount,
                b.TRADER_ID as traderId
        FROM
                T_AFTER_SALES a
                LEFT JOIN T_SALEORDER b ON a.ORDER_ID = b.SALEORDER_ID
                LEFT JOIN T_AFTER_SALES_GOODS c ON a.AFTER_SALES_ID = c.AFTER_SALES_ID
        WHERE
                a.SUBJECT_TYPE = 535
                AND a.TYPE = 539
                AND a.ATFER_SALES_STATUS = 2
                AND b.TRADER_ID in
                    <foreach collection="bizIds" open="(" close=")" index="index" separator="," item="traderId">
                        #{traderId,jdbcType=INTEGER}
                    </foreach>
        GROUP BY
                b.TRADER_ID
    </select>

    <select id="makeExist" resultType="long">
        select count(*) from T_TRADER_DATA WHERE TRADER_ID = #{traderId}
    </select>

    <insert id="insertTraderId">
        insert  into T_TRADER_DATA(TRADER_ID,ADD_TIME) values(#{traderId},#{nowDate})
    </insert>

    <update id="updateAfterSaleCountAndAmount">
        update T_TRADER_DATA set AFTERSALE_TOTAL_AMOUNT =#{afterSaleTotalAmount},AFTERSALE_COUNT =#{afterSaleCount},MOD_TIME=#{nowDate}
        WHERE TRADER_ID = #{traderId}
    </update>

    <select id="getSaleOrderTraderId" resultType="Integer">
        SELECT  distinct
                ts.TRADER_ID
        FROM
                T_SALEORDER ts
        left join T_TRADER_CUSTOMER ttc
        on ts.TRADER_ID =ttc.TRADER_ID
        WHERE
                VALID_STATUS = 1
                AND PAYMENT_STATUS != 0
                AND ttc.IS_ENABLE =1

            and UPDATE_DATA_TIME   >= #{startTime}
            and  UPDATE_DATA_TIME   &lt;= #{endTime}

    </select>

    <select id="findSaleOrderInfo" resultType="Map">
        SELECT
                count( * ) AS orderCount, -- 交易次数
                sum( TOTAL_AMOUNT ) AS orderTotalAmount, -- 交易金额
                min( VALID_TIME ) AS  firstTraderTime,-- 首次交易时间
                max( VALID_TIME )  AS  lastTraderTime,-- 最近交易时间
                TRADER_ID as traderId
        FROM
                T_SALEORDER
        WHERE
                VALID_STATUS = 1
                AND PAYMENT_STATUS != 0
                AND TRADER_ID in
                <foreach collection="bizIds" open="(" close=")" index="index" separator="," item="traderId">
                    #{traderId,jdbcType=INTEGER}
                </foreach>
                GROUP BY TRADER_ID
    </select>

    <update id="updateSaleOrderInfo">
        update T_TRADER_DATA set
            ORDER_COUNT=#{orderCount},
            ORDER_TOTAL_AMOUNT=#{orderTotalAmount},
            FIRST_TRADER_TIME=#{firstTraderTime},
            LAST_TRADER_TIME=#{lastTraderTime},
            MOD_TIME=#{nowDate}
        WHERE TRADER_ID = #{traderId}
    </update>

    <select id="findCheckStatus" resultType="Map">
        select
        ttc.TRADER_ID as traderId,
        case when tvi.STATUS*1 is null then 3
        else tvi.STATUS*1 end traderAttrCheckStatus
        from T_TRADER_CUSTOMER ttc left join T_VERIFIES_INFO tvi
        on tvi.RELATE_TABLE_KEY = ttc.TRADER_CUSTOMER_ID
        AND tvi.RELATE_TABLE = 'T_TRADER_CUSTOMER'  and tvi.VERIFIES_TYPE = 617

            where tvi.MOD_TIME  >=#{startTime}
            and tvi.MOD_TIME &lt;=#{endTime}


    </select>

    <update id="updateCheckStatus">
        update T_TRADER_DATA set
        TRADER_CHECK_STATUS = #{traderAttrCheckStatus},
        MOD_TIME=#{nowDate}
        where TRADER_ID = #{traderId}
    </update>

    <select id="findAttrCheckStatus" resultType="Map">
        select
        ttc.TRADER_ID as traderId,
        case when tvi.STATUS*1 is null then 3
        else tvi.STATUS*1 end traderAttrCheckStatus
        from T_TRADER_CUSTOMER ttc left join T_VERIFIES_INFO tvi
        on tvi.RELATE_TABLE_KEY = ttc.TRADER_CUSTOMER_ID
        AND tvi.RELATE_TABLE = 'T_CUSTOMER_APTITUDE'
             where tvi.MOD_TIME  >=#{startTime}
            and tvi.MOD_TIME &lt;= #{endTime}

        GROUP BY  ttc.TRADER_ID
    </select>

    <select id="updateAttrCheckStatus">
        update T_TRADER_DATA set
        TRADER_ATTR_CHECK_STATUS = #{traderAttrCheckStatus},
        MOD_TIME=#{nowDate}
        where TRADER_ID = #{traderId}
    </select>

    <select id="getQuteNumTraderId" resultType="integer">

        SELECT distinct
        tq.TRADER_ID
        FROM
        T_QUOTEORDER tq
        left join
        T_TRADER_CUSTOMER ttc
        on tq.TRADER_ID =ttc.TRADER_ID
        WHERE
        tq.TRADER_ID > 0
        AND company_id = 1
        AND VALID_STATUS = 1
        AND ttc.IS_ENABLE =1
             and tq.VALID_TIME >= #{startTime}
            and tq.VALID_TIME &lt;= #{endTime}
     </select>

    <select id="findQuteCount" resultType="Map">
        SELECT
            count( * ) AS quoteNum,
            TRADER_ID as traderId
        FROM
            T_QUOTEORDER
        WHERE
            TRADER_ID > 0
            AND company_id = 1
            AND VALID_STATUS = 1
            AND TRADER_ID in
        <foreach collection="bizIds" open="(" close=")" index="index" separator="," item="traderId">
            #{traderId,jdbcType=INTEGER}
        </foreach>
        GROUP BY TRADER_ID
    </select>

    <update id="updateQuteCount">
        update T_TRADER_DATA set
        QUOTE_COUNT = #{quoteNum},
        MOD_TIME=#{nowDate}
        where TRADER_ID = #{traderId}
    </update>

    <select id="getReceiveTotalAmoountTraderId" resultType="integer">
         SELECT distinct
                B.TRADER_ID
        FROM
                T_CAPITAL_BILL A
                left JOIN T_CAPITAL_BILL_DETAIL B ON A.CAPITAL_BILL_ID = B.CAPITAL_BILL_ID
                left join T_TRADER_CUSTOMER ttc on B.TRADER_ID =ttc.TRADER_ID


        WHERE
                B.ORDER_TYPE = 1
                AND A.TRADER_TYPE = 1
                AND ttc.IS_ENABLE =1
             and A.ADD_TIME >= #{startTime}
            and A.ADD_TIME &lt;= #{endTime}


    </select>

    <select id="findReceiveTotalAmoount" resultType="Map">
            SELECT
                SUM( ABS( B.AMOUNT ) ) AS ReceiveTotalAmount, -- 收款金额
                B.TRADER_ID  as traderId
        FROM
                T_CAPITAL_BILL A
                JOIN T_CAPITAL_BILL_DETAIL B ON A.CAPITAL_BILL_ID = B.CAPITAL_BILL_ID
        WHERE
                B.ORDER_TYPE = 1
                AND A.TRADER_TYPE = 1
        AND     B.TRADER_ID in
                <foreach collection="bizIds" open="(" close=")" index="index" separator="," item="traderId">
                    #{traderId,jdbcType=INTEGER}
                </foreach>
        GROUP BY
                B.TRADER_ID
    </select>

    <update id="updateReceiveTotalAmoount">
        update T_TRADER_DATA set
        RECEIVE_TOTAL_AMOUNT = #{receiveTotalAmount},
        MOD_TIME=#{nowDate}
        where TRADER_ID = #{traderId}
    </update>

    <select id="getSkuAndBrandNumTraderId" resultType="integer">
        SELECT  distinct
                b.TRADER_ID
        FROM
                T_SALEORDER_GOODS a
                LEFT JOIN T_SALEORDER b ON a.SALEORDER_ID = b.SALEORDER_ID
                left join T_TRADER_CUSTOMER ttc on b.TRADER_ID =ttc.TRADER_ID
        WHERE
                b.TRADER_ID > 0
                AND b.VALID_STATUS = 1
                and a.IS_DELETE=0
                AND ttc.IS_ENABLE =1
                     and(
                     a.UPDATE_DATA_TIME   >= #{startTime}
                    and  a.UPDATE_DATA_TIME   &lt;= #{endTime}
                    )

                    or (
                    b.MOD_TIME >= #{startTimeLong}
                    and b.MOD_TIME &lt;=#{endTimeLong}
                    )

     </select>

    <select id="findSkuNumAndBrandNum" resultType="Map">
        SELECT
                count( DISTINCT ( a.GOODS_ID ) ) AS orderSkuCount,
                count( DISTINCT ( a.BRAND_NAME) ) AS orderBrandCount,
                b.TRADER_ID as  traderId
        FROM
                T_SALEORDER_GOODS a
                LEFT JOIN T_SALEORDER b ON a.SALEORDER_ID = b.SALEORDER_ID
        WHERE
                b.TRADER_ID > 0
                AND b.VALID_STATUS = 1
                and a.IS_DELETE=0
                AND b.TRADER_ID in
                <foreach collection="bizIds" open="(" close=")" index="index" separator="," item="traderId">
                    #{traderId,jdbcType=INTEGER}
                </foreach>

        GROUP BY
                b.TRADER_ID
    </select>

    <update id="updateSkuNumAndBrandNum">
        update T_TRADER_DATA set
        ORDER_SKU_COUNT = #{orderSkuCount},
        ORDER_BRAND_COUNT = #{orderBrandCount},
        MOD_TIME=#{nowDate}
        where TRADER_ID = #{traderId}
    </update>

    <select id="getCommunicateNumTraderId" resultType="integer">
        SELECT distinct
                a.TRADER_ID
        FROM
                T_COMMUNICATE_RECORD a
                left join T_TRADER_CUSTOMER ttc on a.TRADER_ID =ttc.TRADER_ID
        WHERE
                TRADER_TYPE = 1
                AND a.TRADER_ID !=0
                AND ttc.IS_ENABLE =1
                     and a.MOD_TIME >= #{startTime}
                    and a.MOD_TIME &lt;= #{endTime}
     </select>

    <select id="findCommunicateNum" resultType="map">
        SELECT
                COUNT( * ) AS communcateCount,
                TRADER_ID traderId,
                max(ADD_TIME) AS lastCommuncateTime
        FROM
                T_COMMUNICATE_RECORD
        WHERE
                TRADER_TYPE = 1 AND TRADER_ID !=0
        AND  TRADER_ID in
        <foreach collection="bizIds" open="(" close=")" index="index" separator="," item="traderId">
            #{traderId,jdbcType=INTEGER}
        </foreach>
        GROUP BY
                TRADER_ID
    </select>

    <update id="updateCommunicateNum">
        update T_TRADER_DATA set
        COMMUNICATE_COUNT = #{communcateCount},
        LAST_COMUNICATE_TIME = #{lastCommuncateTime},
        MOD_TIME=#{nowDate}
        where TRADER_ID = #{traderId}
    </update>

    <select id="getPhoneNumTraderId" resultType="integer">
        SELECT distinct
            a.TRADER_ID
        FROM
            T_TRADER_CONTACT a
            left join T_TRADER_CUSTOMER ttc on a.TRADER_ID =ttc.TRADER_ID
        WHERE
            a.IS_ENABLE = 1
            AND a.TRADER_ID != 0
            AND ttc.IS_ENABLE =1
             and a.MOD_TIME >= #{startTime}
            and a.MOD_TIME &lt;= #{endTime}

    </select>

    <select id="findPhoneNum" resultType="map">
        SELECT
            TRADER_ID as traderId,
            COUNT( TRADER_ID ) mobileCount -- 手机号码个数
        FROM
            T_TRADER_CONTACT
        WHERE
            IS_ENABLE = 1
            AND TRADER_ID != 0
            AND TRADER_ID in
            <foreach collection="bizIds" open="(" close=")" index="index" separator="," item="traderId">
                #{traderId,jdbcType=INTEGER}
            </foreach>
        GROUP BY
            TRADER_ID
    </select>



    <update id="updatePhoneNum">
        update T_TRADER_DATA set
        MOBILE_COUNT = #{mobileCount},
        MOD_TIME=#{nowDate}
        where TRADER_ID = #{traderId}
    </update>

    <select id="getBussinessChanceNumTraderId" resultType="integer">
        select distinct a.TRADER_ID
        from T_BUSSINESS_CHANCE a
        left join T_TRADER_CUSTOMER ttc on a.TRADER_ID =ttc.TRADER_ID
        WHERE
        company_id = 1
        AND a.TRADER_ID > 0
        AND ttc.IS_ENABLE =1
             and a.MOD_TIME >= #{startTime}
            and a.MOD_TIME &lt;= #{endTime}

    </select>

    <select id="findBussinessChanceNum" resultType="map">
        select
        count( * ) AS bussinessChanceNum,
        a.TRADER_ID as traderId
        from T_BUSSINESS_CHANCE a
        left join T_TRADER_CUSTOMER ttc on a.TRADER_ID =ttc.TRADER_ID
        WHERE
        company_id = 1
        AND a.TRADER_ID > 0
        AND ttc.IS_ENABLE =1
        and a.TRADER_ID in
        <foreach collection="bizIds" open="(" close=")" index="index" separator="," item="traderId">
            #{traderId,jdbcType=INTEGER}
        </foreach>
        GROUP BY
        a.TRADER_ID
    </select>


    <update id="updateBussinessChanceNum">
        update T_TRADER_DATA set
        BUSSINESS_CHANCENUM=#{bussinessChanceNum},
        MOD_TIME=#{nowDate}
        where TRADER_ID = #{traderId}
    </update>
</mapper>