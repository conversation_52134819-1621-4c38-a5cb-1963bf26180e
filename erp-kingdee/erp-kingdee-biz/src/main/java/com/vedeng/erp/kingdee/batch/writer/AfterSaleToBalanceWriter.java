package com.vedeng.erp.kingdee.batch.writer;

import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeeNeedReceiveDto;
import com.vedeng.erp.kingdee.service.KingDeeNeedReceiveAdjustApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 销售订单售后退款至余额writer
 */
@Service
@Slf4j
public class AfterSaleToBalanceWriter extends BaseWriter<KingDeeNeedReceiveDto> {
    @Autowired
    KingDeeNeedReceiveAdjustApiService kingDeeNeedReceiveAdjustApiService;

    @Override
    public void doWrite(KingDeeNeedReceiveDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
//        kingDeeNeedReceiveService.save(item);
        dto.setKingDeeBizEnums(KingDeeBizEnums.saveNeedReceiveAdjust);
        kingDeeNeedReceiveAdjustApiService.register(dto,true);
    }
}
