package com.vedeng.erp.trader.service;

import com.vedeng.common.page.Page;
import com.vedeng.erp.trader.domain.dto.PublicCustomerExemptUsersDto;

import java.util.List;

/**
 * 公海规则豁免人员
 * <AUTHOR>
 * @date 2022/4/29 15:20
 **/
public interface PublicCustomerExemptUsersService {

    /**
     * 获取获取名单 未删除的
     * @param page 分页信息
     * @return List<PublicCustomerExemptUsersDto> 数据
     */
    List<PublicCustomerExemptUsersDto> queryExemptData(Page page);


    /**
     * 删除
     * @param publicCustomerExemptUsersDto 删除豁免人员对象
     * @return 1 success 2 记录已删除
     */
    Integer deleteExemptionUser(PublicCustomerExemptUsersDto publicCustomerExemptUsersDto);

    /**
     * 保存豁免人员
     * @param publicCustomerExemptUsersDto
     * @return 1 success 2 已存在
     */
    Integer saveExemptionUser(PublicCustomerExemptUsersDto publicCustomerExemptUsersDto);
}
