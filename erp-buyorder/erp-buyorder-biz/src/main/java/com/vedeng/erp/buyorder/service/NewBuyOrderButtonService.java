package com.vedeng.erp.buyorder.service;

import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.authorization.model.User;
import org.springframework.web.servlet.ModelAndView;

/**
 * @Description: 采购单详情页按钮状态初始化
 * @Param:
 * @return:
*/
public interface NewBuyOrderButtonService {

    /**
     * 采购单详情页按钮状态初始化
     * @param mav
     * @param currentUser
     * @param afterSalesVo
     */
    void buttonStatusInit(ModelAndView mav, User currentUser, AfterSalesVo afterSalesVo);
}
