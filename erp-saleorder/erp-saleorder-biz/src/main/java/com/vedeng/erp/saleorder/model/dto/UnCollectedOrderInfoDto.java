package com.vedeng.erp.saleorder.model.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.saleorder.model.dto
 * @Date 2021/12/4 16:31
 */
@Data
public class UnCollectedOrderInfoDto {

    /**
     * 总数
     */
    private Integer totalNum;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 未逾期
     */
    private BigDecimal notOverdue;

    /**
     * 0＜逾期＜60
     */
    private BigDecimal betweenZeroAndSixtyAmount;

    /**
     * 逾期＞60
     */
    private BigDecimal moreThanSixtyAmount;
}
