package com.newtask;

import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.StringUtil;
import com.vedeng.logistics.dao.WarehouseGoodsOperateLogMapper;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.logistics.service.WarehousesService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 *初始化在库商品成本价
 * <AUTHOR>
 * @date $
 */
@Component
@JobHandler(value="OrderCostPriceTask")
public class OrderCostPriceTask extends AbstractJobHandler {
    private Logger logger = LoggerFactory.getLogger(OrderCostPriceTask.class);
    @Autowired
    private WarehousesService warehousesService;

    @Resource
    private WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        updatePrderCostPrice(param);
        return SUCCESS;
    }

    private void updatePrderCostPrice(String param) {

        if(StringUtil.isBlank(param)) {
            //采购售后在库
            stockPrice(StockOperateTypeConst.BUYORDER_WAREHOUSE_CHANGE_IN);
            XxlJobLogger.log("采购售后完成");
            //销售售后在库
            stockPrice(StockOperateTypeConst.ORDER_WAREHOUSE_CHANGE_IN);
            stockPrice(StockOperateTypeConst.ORDER_WAREHOUSE_BACK_IN);
            XxlJobLogger.log("销售售后完成");
            //外借在库
            stockPrice(StockOperateTypeConst.LENDOUT_WAREHOUSE_IN);
            XxlJobLogger.log("外借完成");
            //转移入库
            stockPrice(StockOperateTypeConst.INVENTORY_WAREHOUSE_IN);
            XxlJobLogger.log("转移入库完成");

            //采购单
            stockPrice(StockOperateTypeConst.WAREHOUSE_IN);
            XxlJobLogger.log("采购单入库完成");
            //盘盈入库
            stockPrice(StockOperateTypeConst.SURPLUS_WAREHOUSE_IN);
            XxlJobLogger.log("盘盈入库完成");

        }else{

            if(param.equals("out")){
                saveOutPrice();
                XxlJobLogger.log("出库完成");
            }else{
                Integer id = Integer.valueOf(param);
                WarehouseGoodsOperateLog warehouseInfoById = warehouseGoodsOperateLogMapper.getWarehouseInfoById(id);
                warehousesService.savePriceInfoToWareHouseLog(Collections.singletonList(warehouseInfoById));
            }
        }
    }

    private void saveOutPrice() {
        int id = 0;
        int count = 1;
        while (count > 0){
            List<WarehouseGoodsOperateLog> outList = warehouseGoodsOperateLogMapper.getWarehouseHistoryOutList(5000,id);
            count = outList.size();
            if(count == 0){
                break;
            }
            warehousesService.saveOutLogPrice(outList);
            id = outList.get(count - 1).getWarehouseGoodsOperateLogId();
        }
    }

    private void stockPrice(Integer optType) {
        int id = 0;
        int count = 1;
        while (count > 0){
            List<WarehouseGoodsOperateLog> buyorderList = warehouseGoodsOperateLogMapper.getWarehouseZKlogLimit(optType , 5000 , id);
            count = buyorderList.size();
            if(count == 0){
                break;
            }
            warehousesService.savePriceInfoToWareHouseLog(buyorderList);
            id = buyorderList.get(count - 1).getWarehouseGoodsOperateLogId();
        }
    }

}
