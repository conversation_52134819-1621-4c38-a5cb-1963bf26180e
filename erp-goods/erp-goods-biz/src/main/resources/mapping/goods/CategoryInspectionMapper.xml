<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.mapper.CategoryInspectionMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.goods.domain.entity.CategoryInspection">
        <!--@mbg.generated-->
        <!--@Table V_CATEGORY_INSPECTION-->
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="CATEGORY_ID" jdbcType="INTEGER" property="categoryId"/>
        <result column="INSPECTION_ID" jdbcType="INTEGER" property="inspectionId"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID, CATEGORY_ID, INSPECTION_ID, CREATOR, UPDATER, ADD_TIME, MOD_TIME
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from V_CATEGORY_INSPECTION
        where ID = #{id,jdbcType=INTEGER}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete
        from V_CATEGORY_INSPECTION
        where ID = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" keyColumn="ID" keyProperty="id"
            parameterType="com.vedeng.goods.domain.entity.CategoryInspection"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into V_CATEGORY_INSPECTION (CATEGORY_ID, INSPECTION_ID,
                                           CREATOR, UPDATER, ADD_TIME,
                                           MOD_TIME)
        values (#{categoryId,jdbcType=INTEGER}, #{inspectionId,jdbcType=INTEGER},
                #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP},
                #{modTime,jdbcType=TIMESTAMP})
    </insert>

    <insert id="insertSelective" keyColumn="ID" keyProperty="id"
            parameterType="com.vedeng.goods.domain.entity.CategoryInspection" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into V_CATEGORY_INSPECTION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">
                CATEGORY_ID,
            </if>
            <if test="inspectionId != null">
                INSPECTION_ID,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">
                #{categoryId,jdbcType=INTEGER},
            </if>
            <if test="inspectionId != null">
                #{inspectionId,jdbcType=INTEGER},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.domain.entity.CategoryInspection">
        <!--@mbg.generated-->
        update V_CATEGORY_INSPECTION
        <set>
            <if test="categoryId != null">
                CATEGORY_ID = #{categoryId,jdbcType=INTEGER},
            </if>
            <if test="inspectionId != null">
                INSPECTION_ID = #{inspectionId,jdbcType=INTEGER},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where ID = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.domain.entity.CategoryInspection">
        <!--@mbg.generated-->
        update V_CATEGORY_INSPECTION
        set CATEGORY_ID   = #{categoryId,jdbcType=INTEGER},
            INSPECTION_ID = #{inspectionId,jdbcType=INTEGER},
            CREATOR       = #{creator,jdbcType=INTEGER},
            UPDATER       = #{updater,jdbcType=INTEGER},
            ADD_TIME      = #{addTime,jdbcType=TIMESTAMP},
            MOD_TIME      = #{modTime,jdbcType=TIMESTAMP}
        where ID = #{id,jdbcType=INTEGER}
    </update>

    <select id="findByCategoryId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from V_CATEGORY_INSPECTION
        where CATEGORY_ID=#{categoryId,jdbcType=INTEGER}
    </select>

</mapper>