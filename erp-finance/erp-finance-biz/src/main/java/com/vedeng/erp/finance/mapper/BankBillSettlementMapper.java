package com.vedeng.erp.finance.mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

import com.vedeng.erp.finance.domain.entity.BankBillSettlementEntity;

public interface BankBillSettlementMapper {
    /**
     * delete by primary key
     * @param bankBillSettlementId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long bankBillSettlementId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(BankBillSettlementEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(BankBillSettlementEntity record);

    /**
     * select by primary key
     * @param bankBillSettlementId primary key
     * @return object by primary key
     */
    BankBillSettlementEntity selectByPrimaryKey(Long bankBillSettlementId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(BankBillSettlementEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(BankBillSettlementEntity record);
}