$(function () {
    $('#table-smallest8-all').on('change',function () {
        if (this.checked) {
            $("#smallest8List :checkbox").prop("checked", true);
        } else {
            $("#smallest8List :checkbox").prop("checked", false);
        }
    })

    $('#smallest8List input').on('change', function () {
        var inputLength = $('#smallest8List input').length;
        var checkedLength = $('#smallest8List [name=check_value]:checked').length;
        if (checkedLength == inputLength) {
            $("#table-smallest8-all").prop("checked", true);
        } else {
            $("#table-smallest8-all").prop("checked", false);
        }
    })

    var callbackFuntion = $('#callbackFuntion').val();
    $("#small8-submit").click(function(){
        var valArr = new Array;
        $("#smallest8List input[name='check_value']").each(function(i){
            if($(this).prop("checked")){
                valArr[i]= [$(this).val()];
            }
        });

        $.each(valArr, function (i,value) {
            if(value !== null && value !== undefined && value !== ''){
                var split = value[0].split("%@;");
                selectGoods(callbackFuntion, split[0], split[1], split[2], split[3], split[4], split[5]);
            }

        })
    });

    $("#close_window").click(function () {
        eval("window.parent.closeTab()");
    });
})

function selectGoods(callbackFuntion,sku,goodsName,brandName,model,unitName,stockNum){
    //如果有回调函数就调用
    if(callbackFuntion != null && callbackFuntion != ''){
        if (typeof(stockNum) == "undefined") {
            stockNum = 0
        }
        eval("window.parent."+callbackFuntion+"('"+sku+"','"+goodsName+"','"+brandName+"','"+model+"','"+unitName+"','"+stockNum+"')");
    }
    return;
}
function search() {
    checkLogin();
    clearErroeMes();//清除錯誤提示信息
    if($("#searchContent").val()==undefined || $("#searchContent").val()==""){
        warnTips("errorMes","查询条件不能为空");//文本框ID和提示用语
        $("#searchContent").addClass("errorbor");
        return false;
    }
    $("#search").submit();
}

function searchAll() {
    checkLogin();
    clearErroeMes();//清除錯誤提示信息
    if($("#searchContent").val()==undefined || $("#searchContent").val()==""){
        warnTips("errorMes2","查询条件不能为空");//文本框ID和提示用语
        $("#searchContent").addClass("errorbor");
        return false;
    }
    $("#searchType").val(1);
    $("#search").submit();
}