package com.vedeng.erp.buyorder.domain.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TPurchaseDeliveryDirectBatchDetailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public TPurchaseDeliveryDirectBatchDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andPurchaseDeliveryDirectBatchDetailIdIsNull() {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL_ID is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchDetailIdIsNotNull() {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL_ID is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchDetailIdEqualTo(Integer value) {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL_ID =", value, "purchaseDeliveryDirectBatchDetailId");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchDetailIdNotEqualTo(Integer value) {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL_ID <>", value, "purchaseDeliveryDirectBatchDetailId");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchDetailIdGreaterThan(Integer value) {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL_ID >", value, "purchaseDeliveryDirectBatchDetailId");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchDetailIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL_ID >=", value, "purchaseDeliveryDirectBatchDetailId");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchDetailIdLessThan(Integer value) {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL_ID <", value, "purchaseDeliveryDirectBatchDetailId");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchDetailIdLessThanOrEqualTo(Integer value) {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL_ID <=", value, "purchaseDeliveryDirectBatchDetailId");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchDetailIdIn(List<Integer> values) {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL_ID in", values, "purchaseDeliveryDirectBatchDetailId");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchDetailIdNotIn(List<Integer> values) {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL_ID not in", values, "purchaseDeliveryDirectBatchDetailId");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchDetailIdBetween(Integer value1, Integer value2) {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL_ID between", value1, value2, "purchaseDeliveryDirectBatchDetailId");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchDetailIdNotBetween(Integer value1, Integer value2) {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL_ID not between", value1, value2, "purchaseDeliveryDirectBatchDetailId");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchInfoIdIsNull() {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchInfoIdIsNotNull() {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchInfoIdEqualTo(Integer value) {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID =", value, "purchaseDeliveryDirectBatchInfoId");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchInfoIdNotEqualTo(Integer value) {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID <>", value, "purchaseDeliveryDirectBatchInfoId");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchInfoIdGreaterThan(Integer value) {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID >", value, "purchaseDeliveryDirectBatchInfoId");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchInfoIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID >=", value, "purchaseDeliveryDirectBatchInfoId");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchInfoIdLessThan(Integer value) {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID <", value, "purchaseDeliveryDirectBatchInfoId");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchInfoIdLessThanOrEqualTo(Integer value) {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID <=", value, "purchaseDeliveryDirectBatchInfoId");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchInfoIdIn(List<Integer> values) {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID in", values, "purchaseDeliveryDirectBatchInfoId");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchInfoIdNotIn(List<Integer> values) {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID not in", values, "purchaseDeliveryDirectBatchInfoId");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchInfoIdBetween(Integer value1, Integer value2) {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID between", value1, value2, "purchaseDeliveryDirectBatchInfoId");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchInfoIdNotBetween(Integer value1, Integer value2) {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID not between", value1, value2, "purchaseDeliveryDirectBatchInfoId");
            return (Criteria) this;
        }

        public Criteria andExpressDetailIdIsNull() {
            addCriterion("EXPRESS_DETAIL_ID is null");
            return (Criteria) this;
        }

        public Criteria andExpressDetailIdIsNotNull() {
            addCriterion("EXPRESS_DETAIL_ID is not null");
            return (Criteria) this;
        }

        public Criteria andExpressDetailIdEqualTo(Integer value) {
            addCriterion("EXPRESS_DETAIL_ID =", value, "expressDetailId");
            return (Criteria) this;
        }

        public Criteria andExpressDetailIdNotEqualTo(Integer value) {
            addCriterion("EXPRESS_DETAIL_ID <>", value, "expressDetailId");
            return (Criteria) this;
        }

        public Criteria andExpressDetailIdGreaterThan(Integer value) {
            addCriterion("EXPRESS_DETAIL_ID >", value, "expressDetailId");
            return (Criteria) this;
        }

        public Criteria andExpressDetailIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("EXPRESS_DETAIL_ID >=", value, "expressDetailId");
            return (Criteria) this;
        }

        public Criteria andExpressDetailIdLessThan(Integer value) {
            addCriterion("EXPRESS_DETAIL_ID <", value, "expressDetailId");
            return (Criteria) this;
        }

        public Criteria andExpressDetailIdLessThanOrEqualTo(Integer value) {
            addCriterion("EXPRESS_DETAIL_ID <=", value, "expressDetailId");
            return (Criteria) this;
        }

        public Criteria andExpressDetailIdIn(List<Integer> values) {
            addCriterion("EXPRESS_DETAIL_ID in", values, "expressDetailId");
            return (Criteria) this;
        }

        public Criteria andExpressDetailIdNotIn(List<Integer> values) {
            addCriterion("EXPRESS_DETAIL_ID not in", values, "expressDetailId");
            return (Criteria) this;
        }

        public Criteria andExpressDetailIdBetween(Integer value1, Integer value2) {
            addCriterion("EXPRESS_DETAIL_ID between", value1, value2, "expressDetailId");
            return (Criteria) this;
        }

        public Criteria andExpressDetailIdNotBetween(Integer value1, Integer value2) {
            addCriterion("EXPRESS_DETAIL_ID not between", value1, value2, "expressDetailId");
            return (Criteria) this;
        }

        public Criteria andSkuIsNull() {
            addCriterion("SKU is null");
            return (Criteria) this;
        }

        public Criteria andSkuIsNotNull() {
            addCriterion("SKU is not null");
            return (Criteria) this;
        }

        public Criteria andSkuEqualTo(String value) {
            addCriterion("SKU =", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotEqualTo(String value) {
            addCriterion("SKU <>", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuGreaterThan(String value) {
            addCriterion("SKU >", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuGreaterThanOrEqualTo(String value) {
            addCriterion("SKU >=", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuLessThan(String value) {
            addCriterion("SKU <", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuLessThanOrEqualTo(String value) {
            addCriterion("SKU <=", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuLike(String value) {
            addCriterion("SKU like", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotLike(String value) {
            addCriterion("SKU not like", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuIn(List<String> values) {
            addCriterion("SKU in", values, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotIn(List<String> values) {
            addCriterion("SKU not in", values, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuBetween(String value1, String value2) {
            addCriterion("SKU between", value1, value2, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotBetween(String value1, String value2) {
            addCriterion("SKU not between", value1, value2, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNameIsNull() {
            addCriterion("SKU_NAME is null");
            return (Criteria) this;
        }

        public Criteria andSkuNameIsNotNull() {
            addCriterion("SKU_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andSkuNameEqualTo(String value) {
            addCriterion("SKU_NAME =", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameNotEqualTo(String value) {
            addCriterion("SKU_NAME <>", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameGreaterThan(String value) {
            addCriterion("SKU_NAME >", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameGreaterThanOrEqualTo(String value) {
            addCriterion("SKU_NAME >=", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameLessThan(String value) {
            addCriterion("SKU_NAME <", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameLessThanOrEqualTo(String value) {
            addCriterion("SKU_NAME <=", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameLike(String value) {
            addCriterion("SKU_NAME like", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameNotLike(String value) {
            addCriterion("SKU_NAME not like", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameIn(List<String> values) {
            addCriterion("SKU_NAME in", values, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameNotIn(List<String> values) {
            addCriterion("SKU_NAME not in", values, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameBetween(String value1, String value2) {
            addCriterion("SKU_NAME between", value1, value2, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameNotBetween(String value1, String value2) {
            addCriterion("SKU_NAME not between", value1, value2, "skuName");
            return (Criteria) this;
        }

        public Criteria andModelIsNull() {
            addCriterion("MODEL is null");
            return (Criteria) this;
        }

        public Criteria andModelIsNotNull() {
            addCriterion("MODEL is not null");
            return (Criteria) this;
        }

        public Criteria andModelEqualTo(String value) {
            addCriterion("MODEL =", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotEqualTo(String value) {
            addCriterion("MODEL <>", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThan(String value) {
            addCriterion("MODEL >", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualTo(String value) {
            addCriterion("MODEL >=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThan(String value) {
            addCriterion("MODEL <", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualTo(String value) {
            addCriterion("MODEL <=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLike(String value) {
            addCriterion("MODEL like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotLike(String value) {
            addCriterion("MODEL not like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelIn(List<String> values) {
            addCriterion("MODEL in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotIn(List<String> values) {
            addCriterion("MODEL not in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelBetween(String value1, String value2) {
            addCriterion("MODEL between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotBetween(String value1, String value2) {
            addCriterion("MODEL not between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andUnitIsNull() {
            addCriterion("UNIT is null");
            return (Criteria) this;
        }

        public Criteria andUnitIsNotNull() {
            addCriterion("UNIT is not null");
            return (Criteria) this;
        }

        public Criteria andUnitEqualTo(String value) {
            addCriterion("UNIT =", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualTo(String value) {
            addCriterion("UNIT <>", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThan(String value) {
            addCriterion("UNIT >", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualTo(String value) {
            addCriterion("UNIT >=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThan(String value) {
            addCriterion("UNIT <", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualTo(String value) {
            addCriterion("UNIT <=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLike(String value) {
            addCriterion("UNIT like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotLike(String value) {
            addCriterion("UNIT not like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitIn(List<String> values) {
            addCriterion("UNIT in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotIn(List<String> values) {
            addCriterion("UNIT not in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitBetween(String value1, String value2) {
            addCriterion("UNIT between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotBetween(String value1, String value2) {
            addCriterion("UNIT not between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andProductCompanyIsNull() {
            addCriterion("PRODUCT_COMPANY is null");
            return (Criteria) this;
        }

        public Criteria andProductCompanyIsNotNull() {
            addCriterion("PRODUCT_COMPANY is not null");
            return (Criteria) this;
        }

        public Criteria andProductCompanyEqualTo(String value) {
            addCriterion("PRODUCT_COMPANY =", value, "productCompany");
            return (Criteria) this;
        }

        public Criteria andProductCompanyNotEqualTo(String value) {
            addCriterion("PRODUCT_COMPANY <>", value, "productCompany");
            return (Criteria) this;
        }

        public Criteria andProductCompanyGreaterThan(String value) {
            addCriterion("PRODUCT_COMPANY >", value, "productCompany");
            return (Criteria) this;
        }

        public Criteria andProductCompanyGreaterThanOrEqualTo(String value) {
            addCriterion("PRODUCT_COMPANY >=", value, "productCompany");
            return (Criteria) this;
        }

        public Criteria andProductCompanyLessThan(String value) {
            addCriterion("PRODUCT_COMPANY <", value, "productCompany");
            return (Criteria) this;
        }

        public Criteria andProductCompanyLessThanOrEqualTo(String value) {
            addCriterion("PRODUCT_COMPANY <=", value, "productCompany");
            return (Criteria) this;
        }

        public Criteria andProductCompanyLike(String value) {
            addCriterion("PRODUCT_COMPANY like", value, "productCompany");
            return (Criteria) this;
        }

        public Criteria andProductCompanyNotLike(String value) {
            addCriterion("PRODUCT_COMPANY not like", value, "productCompany");
            return (Criteria) this;
        }

        public Criteria andProductCompanyIn(List<String> values) {
            addCriterion("PRODUCT_COMPANY in", values, "productCompany");
            return (Criteria) this;
        }

        public Criteria andProductCompanyNotIn(List<String> values) {
            addCriterion("PRODUCT_COMPANY not in", values, "productCompany");
            return (Criteria) this;
        }

        public Criteria andProductCompanyBetween(String value1, String value2) {
            addCriterion("PRODUCT_COMPANY between", value1, value2, "productCompany");
            return (Criteria) this;
        }

        public Criteria andProductCompanyNotBetween(String value1, String value2) {
            addCriterion("PRODUCT_COMPANY not between", value1, value2, "productCompany");
            return (Criteria) this;
        }

        public Criteria andProductionLicenceIsNull() {
            addCriterion("PRODUCTION_LICENCE is null");
            return (Criteria) this;
        }

        public Criteria andProductionLicenceIsNotNull() {
            addCriterion("PRODUCTION_LICENCE is not null");
            return (Criteria) this;
        }

        public Criteria andProductionLicenceEqualTo(String value) {
            addCriterion("PRODUCTION_LICENCE =", value, "productionLicence");
            return (Criteria) this;
        }

        public Criteria andProductionLicenceNotEqualTo(String value) {
            addCriterion("PRODUCTION_LICENCE <>", value, "productionLicence");
            return (Criteria) this;
        }

        public Criteria andProductionLicenceGreaterThan(String value) {
            addCriterion("PRODUCTION_LICENCE >", value, "productionLicence");
            return (Criteria) this;
        }

        public Criteria andProductionLicenceGreaterThanOrEqualTo(String value) {
            addCriterion("PRODUCTION_LICENCE >=", value, "productionLicence");
            return (Criteria) this;
        }

        public Criteria andProductionLicenceLessThan(String value) {
            addCriterion("PRODUCTION_LICENCE <", value, "productionLicence");
            return (Criteria) this;
        }

        public Criteria andProductionLicenceLessThanOrEqualTo(String value) {
            addCriterion("PRODUCTION_LICENCE <=", value, "productionLicence");
            return (Criteria) this;
        }

        public Criteria andProductionLicenceLike(String value) {
            addCriterion("PRODUCTION_LICENCE like", value, "productionLicence");
            return (Criteria) this;
        }

        public Criteria andProductionLicenceNotLike(String value) {
            addCriterion("PRODUCTION_LICENCE not like", value, "productionLicence");
            return (Criteria) this;
        }

        public Criteria andProductionLicenceIn(List<String> values) {
            addCriterion("PRODUCTION_LICENCE in", values, "productionLicence");
            return (Criteria) this;
        }

        public Criteria andProductionLicenceNotIn(List<String> values) {
            addCriterion("PRODUCTION_LICENCE not in", values, "productionLicence");
            return (Criteria) this;
        }

        public Criteria andProductionLicenceBetween(String value1, String value2) {
            addCriterion("PRODUCTION_LICENCE between", value1, value2, "productionLicence");
            return (Criteria) this;
        }

        public Criteria andProductionLicenceNotBetween(String value1, String value2) {
            addCriterion("PRODUCTION_LICENCE not between", value1, value2, "productionLicence");
            return (Criteria) this;
        }

        public Criteria andRegisterNumberIsNull() {
            addCriterion("REGISTER_NUMBER is null");
            return (Criteria) this;
        }

        public Criteria andRegisterNumberIsNotNull() {
            addCriterion("REGISTER_NUMBER is not null");
            return (Criteria) this;
        }

        public Criteria andRegisterNumberEqualTo(String value) {
            addCriterion("REGISTER_NUMBER =", value, "registerNumber");
            return (Criteria) this;
        }

        public Criteria andRegisterNumberNotEqualTo(String value) {
            addCriterion("REGISTER_NUMBER <>", value, "registerNumber");
            return (Criteria) this;
        }

        public Criteria andRegisterNumberGreaterThan(String value) {
            addCriterion("REGISTER_NUMBER >", value, "registerNumber");
            return (Criteria) this;
        }

        public Criteria andRegisterNumberGreaterThanOrEqualTo(String value) {
            addCriterion("REGISTER_NUMBER >=", value, "registerNumber");
            return (Criteria) this;
        }

        public Criteria andRegisterNumberLessThan(String value) {
            addCriterion("REGISTER_NUMBER <", value, "registerNumber");
            return (Criteria) this;
        }

        public Criteria andRegisterNumberLessThanOrEqualTo(String value) {
            addCriterion("REGISTER_NUMBER <=", value, "registerNumber");
            return (Criteria) this;
        }

        public Criteria andRegisterNumberLike(String value) {
            addCriterion("REGISTER_NUMBER like", value, "registerNumber");
            return (Criteria) this;
        }

        public Criteria andRegisterNumberNotLike(String value) {
            addCriterion("REGISTER_NUMBER not like", value, "registerNumber");
            return (Criteria) this;
        }

        public Criteria andRegisterNumberIn(List<String> values) {
            addCriterion("REGISTER_NUMBER in", values, "registerNumber");
            return (Criteria) this;
        }

        public Criteria andRegisterNumberNotIn(List<String> values) {
            addCriterion("REGISTER_NUMBER not in", values, "registerNumber");
            return (Criteria) this;
        }

        public Criteria andRegisterNumberBetween(String value1, String value2) {
            addCriterion("REGISTER_NUMBER between", value1, value2, "registerNumber");
            return (Criteria) this;
        }

        public Criteria andRegisterNumberNotBetween(String value1, String value2) {
            addCriterion("REGISTER_NUMBER not between", value1, value2, "registerNumber");
            return (Criteria) this;
        }

        public Criteria andBatchNumberIsNull() {
            addCriterion("BATCH_NUMBER is null");
            return (Criteria) this;
        }

        public Criteria andBatchNumberIsNotNull() {
            addCriterion("BATCH_NUMBER is not null");
            return (Criteria) this;
        }

        public Criteria andBatchNumberEqualTo(String value) {
            addCriterion("BATCH_NUMBER =", value, "batchNumber");
            return (Criteria) this;
        }

        public Criteria andBatchNumberNotEqualTo(String value) {
            addCriterion("BATCH_NUMBER <>", value, "batchNumber");
            return (Criteria) this;
        }

        public Criteria andBatchNumberGreaterThan(String value) {
            addCriterion("BATCH_NUMBER >", value, "batchNumber");
            return (Criteria) this;
        }

        public Criteria andBatchNumberGreaterThanOrEqualTo(String value) {
            addCriterion("BATCH_NUMBER >=", value, "batchNumber");
            return (Criteria) this;
        }

        public Criteria andBatchNumberLessThan(String value) {
            addCriterion("BATCH_NUMBER <", value, "batchNumber");
            return (Criteria) this;
        }

        public Criteria andBatchNumberLessThanOrEqualTo(String value) {
            addCriterion("BATCH_NUMBER <=", value, "batchNumber");
            return (Criteria) this;
        }

        public Criteria andBatchNumberLike(String value) {
            addCriterion("BATCH_NUMBER like", value, "batchNumber");
            return (Criteria) this;
        }

        public Criteria andBatchNumberNotLike(String value) {
            addCriterion("BATCH_NUMBER not like", value, "batchNumber");
            return (Criteria) this;
        }

        public Criteria andBatchNumberIn(List<String> values) {
            addCriterion("BATCH_NUMBER in", values, "batchNumber");
            return (Criteria) this;
        }

        public Criteria andBatchNumberNotIn(List<String> values) {
            addCriterion("BATCH_NUMBER not in", values, "batchNumber");
            return (Criteria) this;
        }

        public Criteria andBatchNumberBetween(String value1, String value2) {
            addCriterion("BATCH_NUMBER between", value1, value2, "batchNumber");
            return (Criteria) this;
        }

        public Criteria andBatchNumberNotBetween(String value1, String value2) {
            addCriterion("BATCH_NUMBER not between", value1, value2, "batchNumber");
            return (Criteria) this;
        }

        public Criteria andUnionSequenceIsNull() {
            addCriterion("UNION_SEQUENCE is null");
            return (Criteria) this;
        }

        public Criteria andUnionSequenceIsNotNull() {
            addCriterion("UNION_SEQUENCE is not null");
            return (Criteria) this;
        }

        public Criteria andUnionSequenceEqualTo(Integer value) {
            addCriterion("UNION_SEQUENCE =", value, "unionSequence");
            return (Criteria) this;
        }

        public Criteria andUnionSequenceNotEqualTo(Integer value) {
            addCriterion("UNION_SEQUENCE <>", value, "unionSequence");
            return (Criteria) this;
        }

        public Criteria andUnionSequenceGreaterThan(Integer value) {
            addCriterion("UNION_SEQUENCE >", value, "unionSequence");
            return (Criteria) this;
        }

        public Criteria andUnionSequenceGreaterThanOrEqualTo(Integer value) {
            addCriterion("UNION_SEQUENCE >=", value, "unionSequence");
            return (Criteria) this;
        }

        public Criteria andUnionSequenceLessThan(Integer value) {
            addCriterion("UNION_SEQUENCE <", value, "unionSequence");
            return (Criteria) this;
        }

        public Criteria andUnionSequenceLessThanOrEqualTo(Integer value) {
            addCriterion("UNION_SEQUENCE <=", value, "unionSequence");
            return (Criteria) this;
        }

        public Criteria andUnionSequenceIn(List<Integer> values) {
            addCriterion("UNION_SEQUENCE in", values, "unionSequence");
            return (Criteria) this;
        }

        public Criteria andUnionSequenceNotIn(List<Integer> values) {
            addCriterion("UNION_SEQUENCE not in", values, "unionSequence");
            return (Criteria) this;
        }

        public Criteria andUnionSequenceBetween(Integer value1, Integer value2) {
            addCriterion("UNION_SEQUENCE between", value1, value2, "unionSequence");
            return (Criteria) this;
        }

        public Criteria andUnionSequenceNotBetween(Integer value1, Integer value2) {
            addCriterion("UNION_SEQUENCE not between", value1, value2, "unionSequence");
            return (Criteria) this;
        }

        public Criteria andArrivalCountIsNull() {
            addCriterion("ARRIVAL_COUNT is null");
            return (Criteria) this;
        }

        public Criteria andArrivalCountIsNotNull() {
            addCriterion("ARRIVAL_COUNT is not null");
            return (Criteria) this;
        }

        public Criteria andArrivalCountEqualTo(Integer value) {
            addCriterion("ARRIVAL_COUNT =", value, "arrivalCount");
            return (Criteria) this;
        }

        public Criteria andArrivalCountNotEqualTo(Integer value) {
            addCriterion("ARRIVAL_COUNT <>", value, "arrivalCount");
            return (Criteria) this;
        }

        public Criteria andArrivalCountGreaterThan(Integer value) {
            addCriterion("ARRIVAL_COUNT >", value, "arrivalCount");
            return (Criteria) this;
        }

        public Criteria andArrivalCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("ARRIVAL_COUNT >=", value, "arrivalCount");
            return (Criteria) this;
        }

        public Criteria andArrivalCountLessThan(Integer value) {
            addCriterion("ARRIVAL_COUNT <", value, "arrivalCount");
            return (Criteria) this;
        }

        public Criteria andArrivalCountLessThanOrEqualTo(Integer value) {
            addCriterion("ARRIVAL_COUNT <=", value, "arrivalCount");
            return (Criteria) this;
        }

        public Criteria andArrivalCountIn(List<Integer> values) {
            addCriterion("ARRIVAL_COUNT in", values, "arrivalCount");
            return (Criteria) this;
        }

        public Criteria andArrivalCountNotIn(List<Integer> values) {
            addCriterion("ARRIVAL_COUNT not in", values, "arrivalCount");
            return (Criteria) this;
        }

        public Criteria andArrivalCountBetween(Integer value1, Integer value2) {
            addCriterion("ARRIVAL_COUNT between", value1, value2, "arrivalCount");
            return (Criteria) this;
        }

        public Criteria andArrivalCountNotBetween(Integer value1, Integer value2) {
            addCriterion("ARRIVAL_COUNT not between", value1, value2, "arrivalCount");
            return (Criteria) this;
        }

        public Criteria andWmsHandledArrivalCountIsNull() {
            addCriterion("WMS_HANDLED_ARRIVAL_COUNT is null");
            return (Criteria) this;
        }

        public Criteria andWmsHandledArrivalCountIsNotNull() {
            addCriterion("WMS_HANDLED_ARRIVAL_COUNT is not null");
            return (Criteria) this;
        }

        public Criteria andWmsHandledArrivalCountEqualTo(Integer value) {
            addCriterion("WMS_HANDLED_ARRIVAL_COUNT =", value, "wmsHandledArrivalCount");
            return (Criteria) this;
        }

        public Criteria andWmsHandledArrivalCountNotEqualTo(Integer value) {
            addCriterion("WMS_HANDLED_ARRIVAL_COUNT <>", value, "wmsHandledArrivalCount");
            return (Criteria) this;
        }

        public Criteria andWmsHandledArrivalCountGreaterThan(Integer value) {
            addCriterion("WMS_HANDLED_ARRIVAL_COUNT >", value, "wmsHandledArrivalCount");
            return (Criteria) this;
        }

        public Criteria andWmsHandledArrivalCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("WMS_HANDLED_ARRIVAL_COUNT >=", value, "wmsHandledArrivalCount");
            return (Criteria) this;
        }

        public Criteria andWmsHandledArrivalCountLessThan(Integer value) {
            addCriterion("WMS_HANDLED_ARRIVAL_COUNT <", value, "wmsHandledArrivalCount");
            return (Criteria) this;
        }

        public Criteria andWmsHandledArrivalCountLessThanOrEqualTo(Integer value) {
            addCriterion("WMS_HANDLED_ARRIVAL_COUNT <=", value, "wmsHandledArrivalCount");
            return (Criteria) this;
        }

        public Criteria andWmsHandledArrivalCountIn(List<Integer> values) {
            addCriterion("WMS_HANDLED_ARRIVAL_COUNT in", values, "wmsHandledArrivalCount");
            return (Criteria) this;
        }

        public Criteria andWmsHandledArrivalCountNotIn(List<Integer> values) {
            addCriterion("WMS_HANDLED_ARRIVAL_COUNT not in", values, "wmsHandledArrivalCount");
            return (Criteria) this;
        }

        public Criteria andWmsHandledArrivalCountBetween(Integer value1, Integer value2) {
            addCriterion("WMS_HANDLED_ARRIVAL_COUNT between", value1, value2, "wmsHandledArrivalCount");
            return (Criteria) this;
        }

        public Criteria andWmsHandledArrivalCountNotBetween(Integer value1, Integer value2) {
            addCriterion("WMS_HANDLED_ARRIVAL_COUNT not between", value1, value2, "wmsHandledArrivalCount");
            return (Criteria) this;
        }

        public Criteria andWmsHandledDeliveryCountIsNull() {
            addCriterion("WMS_HANDLED_DELIVERY_COUNT is null");
            return (Criteria) this;
        }

        public Criteria andWmsHandledDeliveryCountIsNotNull() {
            addCriterion("WMS_HANDLED_DELIVERY_COUNT is not null");
            return (Criteria) this;
        }

        public Criteria andWmsHandledDeliveryCountEqualTo(Integer value) {
            addCriterion("WMS_HANDLED_DELIVERY_COUNT =", value, "wmsHandledDeliveryCount");
            return (Criteria) this;
        }

        public Criteria andWmsHandledDeliveryCountNotEqualTo(Integer value) {
            addCriterion("WMS_HANDLED_DELIVERY_COUNT <>", value, "wmsHandledDeliveryCount");
            return (Criteria) this;
        }

        public Criteria andWmsHandledDeliveryCountGreaterThan(Integer value) {
            addCriterion("WMS_HANDLED_DELIVERY_COUNT >", value, "wmsHandledDeliveryCount");
            return (Criteria) this;
        }

        public Criteria andWmsHandledDeliveryCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("WMS_HANDLED_DELIVERY_COUNT >=", value, "wmsHandledDeliveryCount");
            return (Criteria) this;
        }

        public Criteria andWmsHandledDeliveryCountLessThan(Integer value) {
            addCriterion("WMS_HANDLED_DELIVERY_COUNT <", value, "wmsHandledDeliveryCount");
            return (Criteria) this;
        }

        public Criteria andWmsHandledDeliveryCountLessThanOrEqualTo(Integer value) {
            addCriterion("WMS_HANDLED_DELIVERY_COUNT <=", value, "wmsHandledDeliveryCount");
            return (Criteria) this;
        }

        public Criteria andWmsHandledDeliveryCountIn(List<Integer> values) {
            addCriterion("WMS_HANDLED_DELIVERY_COUNT in", values, "wmsHandledDeliveryCount");
            return (Criteria) this;
        }

        public Criteria andWmsHandledDeliveryCountNotIn(List<Integer> values) {
            addCriterion("WMS_HANDLED_DELIVERY_COUNT not in", values, "wmsHandledDeliveryCount");
            return (Criteria) this;
        }

        public Criteria andWmsHandledDeliveryCountBetween(Integer value1, Integer value2) {
            addCriterion("WMS_HANDLED_DELIVERY_COUNT between", value1, value2, "wmsHandledDeliveryCount");
            return (Criteria) this;
        }

        public Criteria andWmsHandledDeliveryCountNotBetween(Integer value1, Integer value2) {
            addCriterion("WMS_HANDLED_DELIVERY_COUNT not between", value1, value2, "wmsHandledDeliveryCount");
            return (Criteria) this;
        }

        public Criteria andManufactureDateTimeIsNull() {
            addCriterion("MANUFACTURE_DATE_TIME is null");
            return (Criteria) this;
        }

        public Criteria andManufactureDateTimeIsNotNull() {
            addCriterion("MANUFACTURE_DATE_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andManufactureDateTimeEqualTo(Date value) {
            addCriterion("MANUFACTURE_DATE_TIME =", value, "manufactureDateTime");
            return (Criteria) this;
        }

        public Criteria andManufactureDateTimeNotEqualTo(Date value) {
            addCriterion("MANUFACTURE_DATE_TIME <>", value, "manufactureDateTime");
            return (Criteria) this;
        }

        public Criteria andManufactureDateTimeGreaterThan(Date value) {
            addCriterion("MANUFACTURE_DATE_TIME >", value, "manufactureDateTime");
            return (Criteria) this;
        }

        public Criteria andManufactureDateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("MANUFACTURE_DATE_TIME >=", value, "manufactureDateTime");
            return (Criteria) this;
        }

        public Criteria andManufactureDateTimeLessThan(Date value) {
            addCriterion("MANUFACTURE_DATE_TIME <", value, "manufactureDateTime");
            return (Criteria) this;
        }

        public Criteria andManufactureDateTimeLessThanOrEqualTo(Date value) {
            addCriterion("MANUFACTURE_DATE_TIME <=", value, "manufactureDateTime");
            return (Criteria) this;
        }

        public Criteria andManufactureDateTimeIn(List<Date> values) {
            addCriterion("MANUFACTURE_DATE_TIME in", values, "manufactureDateTime");
            return (Criteria) this;
        }

        public Criteria andManufactureDateTimeNotIn(List<Date> values) {
            addCriterion("MANUFACTURE_DATE_TIME not in", values, "manufactureDateTime");
            return (Criteria) this;
        }

        public Criteria andManufactureDateTimeBetween(Date value1, Date value2) {
            addCriterion("MANUFACTURE_DATE_TIME between", value1, value2, "manufactureDateTime");
            return (Criteria) this;
        }

        public Criteria andManufactureDateTimeNotBetween(Date value1, Date value2) {
            addCriterion("MANUFACTURE_DATE_TIME not between", value1, value2, "manufactureDateTime");
            return (Criteria) this;
        }

        public Criteria andInvalidDateTimeIsNull() {
            addCriterion("INVALID_DATE_TIME is null");
            return (Criteria) this;
        }

        public Criteria andInvalidDateTimeIsNotNull() {
            addCriterion("INVALID_DATE_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andInvalidDateTimeEqualTo(Date value) {
            addCriterion("INVALID_DATE_TIME =", value, "invalidDateTime");
            return (Criteria) this;
        }

        public Criteria andInvalidDateTimeNotEqualTo(Date value) {
            addCriterion("INVALID_DATE_TIME <>", value, "invalidDateTime");
            return (Criteria) this;
        }

        public Criteria andInvalidDateTimeGreaterThan(Date value) {
            addCriterion("INVALID_DATE_TIME >", value, "invalidDateTime");
            return (Criteria) this;
        }

        public Criteria andInvalidDateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("INVALID_DATE_TIME >=", value, "invalidDateTime");
            return (Criteria) this;
        }

        public Criteria andInvalidDateTimeLessThan(Date value) {
            addCriterion("INVALID_DATE_TIME <", value, "invalidDateTime");
            return (Criteria) this;
        }

        public Criteria andInvalidDateTimeLessThanOrEqualTo(Date value) {
            addCriterion("INVALID_DATE_TIME <=", value, "invalidDateTime");
            return (Criteria) this;
        }

        public Criteria andInvalidDateTimeIn(List<Date> values) {
            addCriterion("INVALID_DATE_TIME in", values, "invalidDateTime");
            return (Criteria) this;
        }

        public Criteria andInvalidDateTimeNotIn(List<Date> values) {
            addCriterion("INVALID_DATE_TIME not in", values, "invalidDateTime");
            return (Criteria) this;
        }

        public Criteria andInvalidDateTimeBetween(Date value1, Date value2) {
            addCriterion("INVALID_DATE_TIME between", value1, value2, "invalidDateTime");
            return (Criteria) this;
        }

        public Criteria andInvalidDateTimeNotBetween(Date value1, Date value2) {
            addCriterion("INVALID_DATE_TIME not between", value1, value2, "invalidDateTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("ADD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("ADD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("ADD_TIME =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("ADD_TIME <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("ADD_TIME >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("ADD_TIME >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("ADD_TIME <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("ADD_TIME <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("ADD_TIME in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("ADD_TIME not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("ADD_TIME between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("ADD_TIME not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("CREATOR is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("CREATOR is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(Integer value) {
            addCriterion("CREATOR =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(Integer value) {
            addCriterion("CREATOR <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(Integer value) {
            addCriterion("CREATOR >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("CREATOR >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(Integer value) {
            addCriterion("CREATOR <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(Integer value) {
            addCriterion("CREATOR <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<Integer> values) {
            addCriterion("CREATOR in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<Integer> values) {
            addCriterion("CREATOR not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNull() {
            addCriterion("MOD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNotNull() {
            addCriterion("MOD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andModTimeEqualTo(Date value) {
            addCriterion("MOD_TIME =", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotEqualTo(Date value) {
            addCriterion("MOD_TIME <>", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThan(Date value) {
            addCriterion("MOD_TIME >", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("MOD_TIME >=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThan(Date value) {
            addCriterion("MOD_TIME <", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThanOrEqualTo(Date value) {
            addCriterion("MOD_TIME <=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeIn(List<Date> values) {
            addCriterion("MOD_TIME in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotIn(List<Date> values) {
            addCriterion("MOD_TIME not in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeBetween(Date value1, Date value2) {
            addCriterion("MOD_TIME between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotBetween(Date value1, Date value2) {
            addCriterion("MOD_TIME not between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNull() {
            addCriterion("UPDATER is null");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNotNull() {
            addCriterion("UPDATER is not null");
            return (Criteria) this;
        }

        public Criteria andUpdaterEqualTo(Integer value) {
            addCriterion("UPDATER =", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotEqualTo(Integer value) {
            addCriterion("UPDATER <>", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThan(Integer value) {
            addCriterion("UPDATER >", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThanOrEqualTo(Integer value) {
            addCriterion("UPDATER >=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThan(Integer value) {
            addCriterion("UPDATER <", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThanOrEqualTo(Integer value) {
            addCriterion("UPDATER <=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterIn(List<Integer> values) {
            addCriterion("UPDATER in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotIn(List<Integer> values) {
            addCriterion("UPDATER not in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER not between", value1, value2, "updater");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}