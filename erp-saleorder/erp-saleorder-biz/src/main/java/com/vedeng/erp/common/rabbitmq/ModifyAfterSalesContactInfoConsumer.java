package com.vedeng.erp.common.rabbitmq;

import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.vedeng.common.trace.mq.AbstractMessageListener;
import com.vedeng.erp.aftersale.domain.dto.ModifyAfterSalesContactInfoDto;
import com.vedeng.erp.aftersale.service.AfterSalesApiService;
import com.vedeng.erp.aftersale.service.AfterSalesCommonService;
import com.vedeng.erp.market.domain.vo.MarketPlanVo;
import com.vedeng.erp.market.service.MarketService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Component
@Slf4j
public class ModifyAfterSalesContactInfoConsumer extends AbstractMessageListener {


    @Autowired
    private AfterSalesCommonService afterSalesCommonService;

    @Override
    public void doBusiness(Message message, Channel channel) throws Exception {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("前台修改售后单联系人信息推送：[{}]", message);
        try {
            ModifyAfterSalesContactInfoDto modifyAfterSalesContactInfoDto = JSONObject.parseObject(messageBody, ModifyAfterSalesContactInfoDto.class);
            afterSalesCommonService.modifyAfterSalesContactInfo(modifyAfterSalesContactInfoDto);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            log.info("前台修改售后单联系人信息推送处理结束");
        } catch (Exception e) {
            log.error("前台修改售后单联系人信息推送发生错误:消息内容：[{}],异常信息:", message, e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            } catch (IOException ex) {
                log.error("前台修改售后单联系人信息推送消息发生错误:消息内容：[{}],异常信息:", message, e);
            }
        }
    }

}
