package com.vedeng.goods.manager.validator.model;

/**
 * <AUTHOR> [<EMAIL>]
 */
public abstract class GoodsValidObject {

    private Integer goodsLevelNo;

    private Integer goodsPositionNo;

    public Integer getGoodsLevelNo() {
        return goodsLevelNo;
    }

    private Integer storageConditionTemperature;

    private Float storageConditionTemperatureLowerValue;

    private Float storageConditionTemperatureUpperValue;

    private Float storageConditionHumidityLowerValue;

    private Float storageConditionHumidityUpperValue;

    private Integer[] storageConditionOthersArray;

    /**
     * wiki地址
     */
    private String wikiHref;


    public void setGoodsLevelNo(Integer goodsLevelNo) {
        this.goodsLevelNo = goodsLevelNo;
    }

    public Integer getGoodsPositionNo() {
        return goodsPositionNo;
    }

    public void setGoodsPositionNo(Integer goodsPositionNo) {
        this.goodsPositionNo = goodsPositionNo;
    }

    public boolean isNew() {
        return getId() == null || getId() <= 0;
    }

    public abstract Integer getId();

    public abstract String getName();

    public abstract void setName(String name);


    public Integer getStorageConditionTemperature() {
        return storageConditionTemperature;
    }

    public void setStorageConditionTemperature(Integer storageConditionTemperature) {
        this.storageConditionTemperature = storageConditionTemperature;
    }

    public Float getStorageConditionTemperatureLowerValue() {
        return storageConditionTemperatureLowerValue;
    }

    public void setStorageConditionTemperatureLowerValue(Float storageConditionTemperatureLowerValue) {
        this.storageConditionTemperatureLowerValue = storageConditionTemperatureLowerValue;
    }

    public Float getStorageConditionTemperatureUpperValue() {
        return storageConditionTemperatureUpperValue;
    }

    public void setStorageConditionTemperatureUpperValue(Float storageConditionTemperatureUpperValue) {
        this.storageConditionTemperatureUpperValue = storageConditionTemperatureUpperValue;
    }

    public Float getStorageConditionHumidityLowerValue() {
        return storageConditionHumidityLowerValue;
    }

    public void setStorageConditionHumidityLowerValue(Float storageConditionHumidityLowerValue) {
        this.storageConditionHumidityLowerValue = storageConditionHumidityLowerValue;
    }

    public Float getStorageConditionHumidityUpperValue() {
        return storageConditionHumidityUpperValue;
    }

    public void setStorageConditionHumidityUpperValue(Float storageConditionHumidityUpperValue) {
        this.storageConditionHumidityUpperValue = storageConditionHumidityUpperValue;
    }

    public Integer[] getStorageConditionOthersArray() {
        return storageConditionOthersArray;
    }

    public void setStorageConditionOthersArray(Integer[] storageConditionOthersArray) {
        this.storageConditionOthersArray = storageConditionOthersArray;
    }

    public String getWikiHref() {
        return wikiHref;
    }

    public void setWikiHref(String wikiHref) {
        this.wikiHref = wikiHref;
    }
}
