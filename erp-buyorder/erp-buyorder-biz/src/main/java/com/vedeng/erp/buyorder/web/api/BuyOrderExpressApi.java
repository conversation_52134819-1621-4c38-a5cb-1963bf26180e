package com.vedeng.erp.buyorder.web.api;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.buyorder.dto.BuyOrderExpressDto;
import com.vedeng.erp.buyorder.dto.BuyOrderRebateChargeApplyDto;
import com.vedeng.erp.buyorder.service.BuyOrderRebateChargeApplyService;
import com.vedeng.erp.buyorder.service.NewBuyOrderService;
import com.vedeng.erp.buyorder.service.TExpressCommunicateLogService;
import com.vedeng.erp.finance.domain.entity.TExpressCommunicateLog;
import com.vedeng.erp.system.dto.ActivityAuditDto;
import com.vedeng.infrastructure.oss.service.domain.FileInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * 采购返利结算收款申请 Api
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/22 16:26
 */
@ExceptionController
@Controller
@RequestMapping("/buyOrder/express/api")
@Slf4j
public class BuyOrderExpressApi {

    @Autowired
    private NewBuyOrderService newBuyOrderService;

    @Autowired
    private TExpressCommunicateLogService tExpressCommunicateLogService;

    /**
     * 分页查询直发快递待确认列表
     *
     * @param buyOrderExpressDtoPageParam 分页查询参数
     * @return PageInfo<BuyOrderExpressDto>
     */
    @RequestMapping("/needConfirmList")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<PageInfo<BuyOrderExpressDto>> needConfirmList(@RequestBody PageParam<BuyOrderExpressDto> buyOrderExpressDtoPageParam) {
        return R.success(newBuyOrderService.needConfirmList(buyOrderExpressDtoPageParam));
    }

    /**
     * 保存物流沟通记录
     *
     * @param   record 沟通记录
     * @return PageInfo<BuyOrderExpressDto>
     */
    @RequestMapping("/addCommunicationRecord")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<TExpressCommunicateLog> addCommunicationRecord(@RequestBody TExpressCommunicateLog record) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        try{
            record.setCreator(currentUser.getId());
            record.setAddUserName(currentUser.getUsername());
            record.setUpdater(currentUser.getId());
            record.setAddTime(new Date());
            record.setModTime(new Date());
            tExpressCommunicateLogService.insert(record);
            return R.success(record);
        }catch (Exception e){
            log.error("保存物流沟通记录失败", e);
            return R.error("保存失败");

        }
    }

    /**
     * 保存物流沟通记录
     *
     * @param   expressId 物流ID
     * @return PageInfo<BuyOrderExpressDto>
     */
    @RequestMapping("/needConfirmMessageList")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<List<TExpressCommunicateLog>> needConfirmMessageList(Integer expressId) {
         try{
            List<TExpressCommunicateLog> resultList= tExpressCommunicateLogService.selectByExpressId(expressId);
            return R.success(resultList);
        }catch (Exception e){
            log.error("查询物流沟通记录失败", e);
            return R.error("查询物流沟通记录失败");

        }
    }

}