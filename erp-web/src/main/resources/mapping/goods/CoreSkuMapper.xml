<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.dao.CoreSkuMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.model.CoreSku">
    <id column="SKU_ID" jdbcType="INTEGER" property="skuId" />
    <result column="SPU_ID" jdbcType="INTEGER" property="spuId" />
    <result column="CHECK_STATUS" jdbcType="TINYINT" property="checkStatus" />
    <result column="MODEL" jdbcType="VARCHAR" property="model" />
    <result column="SPEC" jdbcType="VARCHAR" property="spec" />
    <result column="SKU_NO" jdbcType="VARCHAR" property="skuNo" />
    <result column="SKU_NAME" jdbcType="VARCHAR" property="skuName" />
    <result column="SHOW_NAME" jdbcType="VARCHAR" property="showName" />
    <result column="MATERIAL_CODE" jdbcType="VARCHAR" property="materialCode" />
    <result column="SUPPLY_MODEL" jdbcType="VARCHAR" property="supplyModel" />
    <result column="IS_STOCKUP" jdbcType="VARCHAR" property="isStockup" />
    <result column="WIKI_HREF" jdbcType="VARCHAR" property="wikiHref" />
    <result column="TECHNICAL_PARAMETER" jdbcType="VARCHAR" property="technicalParameter" />
    <result column="PERFORMANCE_PARAMETER" jdbcType="VARCHAR" property="performanceParameter" />
    <result column="SPEC_PARAMETER" jdbcType="VARCHAR" property="specParameter" />
    <result column="BASE_UNIT_ID" jdbcType="INTEGER" property="baseUnitId" />
    <result column="MIN_ORDER" jdbcType="DECIMAL" property="minOrder" />
    <result column="GOODS_LENGTH" jdbcType="DECIMAL" property="goodsLength" />
    <result column="GOODS_WIDTH" jdbcType="DECIMAL" property="goodsWidth" />
    <result column="GOODS_HEIGHT" jdbcType="DECIMAL" property="goodsHeight" />
    <result column="PACKAGE_LENGTH" jdbcType="DECIMAL" property="packageLength" />
    <result column="PACKAGE_WIDTH" jdbcType="DECIMAL" property="packageWidth" />
    <result column="PACKAGE_HEIGHT" jdbcType="DECIMAL" property="packageHeight" />
    <result column="NET_WEIGHT" jdbcType="DECIMAL" property="netWeight" />
    <result column="GROSS_WEIGHT" jdbcType="DECIMAL" property="grossWeight" />
    <result column="UNIT_ID" jdbcType="INTEGER" property="unitId" />
    <result column="CHANGE_NUM" jdbcType="DECIMAL" property="changeNum" />
    <result column="PACKING_LIST" jdbcType="VARCHAR" property="packingList" />
    <result column="AFTER_SALE_CONTENT" jdbcType="VARCHAR" property="afterSaleContent" />
    <result column="QA_YEARS" jdbcType="VARCHAR" property="qaYears" />
    <result column="STORAGE_CONDITION_ONE" jdbcType="INTEGER" property="storageConditionOne" />
    <result column="STORAGE_CONDITION_ONE_LOWER_VALUE" jdbcType="FLOAT" property="storageConditionOneLowerValue" />
    <result column="STORAGE_CONDITION_ONE_UPPER_VALUE" jdbcType="FLOAT" property="storageConditionOneUpperValue" />
    <result column="STORAGE_CONDITION_HUMIDITY_LOWER_VALUE" jdbcType="FLOAT" property="storageConditionHumidityLowerValue" />
    <result column="STORAGE_CONDITION_HUMIDITY_UPPER_VALUE" jdbcType="FLOAT" property="storageConditionHumidityUpperValue" />
    <result column="STORAGE_CONDITION_TWO" jdbcType="VARCHAR" property="storageConditionTwo" />
    <result column="EFFECTIVE_DAY_UNIT" jdbcType="BOOLEAN" property="effectiveDayUnit" />
    <result column="EFFECTIVE_DAYS" jdbcType="VARCHAR" property="effectiveDays" />
    <result column="QA_RULE" jdbcType="VARCHAR" property="qaRule" />
    <result column="QA_OUT_PRICE" jdbcType="DECIMAL" property="qaOutPrice" />
    <result column="QA_RESPONSE_TIME" jdbcType="DECIMAL" property="qaResponseTime" />
    <result column="HAS_BACKUP_MACHINE" jdbcType="VARCHAR" property="hasBackupMachine" />
    <result column="SUPPLIER_EXTEND_GUARANTEE_PRICE" jdbcType="DECIMAL" property="supplierExtendGuaranteePrice" />
    <result column="CORE_PARTS_PRICE_FID" jdbcType="INTEGER" property="corePartsPriceFid" />
    <result column="RETURN_GOODS_CONDITIONS" jdbcType="BOOLEAN" property="returnGoodsConditions" />
    <result column="FREIGHT_INTRODUCTIONS" jdbcType="VARCHAR" property="freightIntroductions" />
    <result column="EXCHANGE_GOODS_CONDITIONS" jdbcType="VARCHAR" property="exchangeGoodsConditions" />
    <result column="EXCHANGE_GOODS_METHOD" jdbcType="VARCHAR" property="exchangeGoodsMethod" />
    <result column="GOODS_COMMENTS" jdbcType="VARCHAR" property="goodsComments" />
    <result column="STATUS" jdbcType="INTEGER" property="status" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CHECK_TIME" jdbcType="TIMESTAMP" property="checkTime" />
    <result column="CHECKER" jdbcType="INTEGER" property="checker" />
    <result column="OPERATE_INFO_ID" jdbcType="INTEGER" property="operateInfoId" />
    <result column="DELETE_REASON" jdbcType="VARCHAR" property="deleteReason" />
    <result column="LAST_CHECK_REASON" jdbcType="VARCHAR" property="lastCheckReason" />
    <result column="TAX_CATEGORY_NO" jdbcType="VARCHAR" property="taxCategoryNo" />
    <result column="JX_MARKET_PRICE" jdbcType="DECIMAL" property="jxMarketPrice" />
    <result column="JX_SALE_PRICE" jdbcType="DECIMAL" property="jxSalePrice" />
    <result column="JX_FLAG" jdbcType="INTEGER" property="jxFlag" />
    <result column="SOURCE" jdbcType="TINYINT" property="source" />
    <result column="PUSH_STATUS" jdbcType="INTEGER" property="pushStatus" />
    <result column="DECLARE_DELIVERY_RANGE" jdbcType="VARCHAR" property="declareDeliveryRange" />
    <result column="PRICE_VERIFY_STATUS" jdbcType="INTEGER" property="priceVerifyStatus" />
    <result column="AVGPRICE" jdbcType="DECIMAL" property="avgprice" />
    <result column="LATEST_VALID_ORDER_USER" jdbcType="INTEGER" property="latestValidOrderUser" />
    <result column="AVGPRICE_UPDATE_TIME" jdbcType="TIMESTAMP" property="avgpriceUpdateTime" />
    <result column="TERMINAL_PRICE" jdbcType="DECIMAL" property="terminalPrice" />
    <result column="DISTRIBUTION_PRICE" jdbcType="DECIMAL" property="distributionPrice" />
    <result column="COST_PRICE" jdbcType="DECIMAL" property="costPrice" />
    <result column="AVAILABLE_STOCK_NUM" jdbcType="INTEGER" property="availableStockNum" />
    <result column="STOCK_NUM" jdbcType="INTEGER" property="stockNum" />
    <result column="ON_SALE" jdbcType="TINYINT" property="onSale" />
    <result column="GOODS_BARCODE" jdbcType="VARCHAR" property="goodsBarcode" />
    <result column="CURING_TYPE" jdbcType="BOOLEAN" property="curingType" />
    <result column="CURING_REASON" jdbcType="VARCHAR" property="curingReason" />
    <result column="IS_NEED_TEST_REPROT" jdbcType="BOOLEAN" property="isNeedTestReprot" />
    <result column="IS_KIT" jdbcType="BOOLEAN" property="isKit" />
    <result column="KIT_DESC" jdbcType="VARCHAR" property="kitDesc" />
    <result column="IS_SAME_SN_CODE" jdbcType="BOOLEAN" property="isSameSnCode" />
    <result column="IS_FACTORY_SN_CODE" jdbcType="BOOLEAN" property="isFactorySnCode" />
    <result column="IS_MANAGE_VEDENG_CODE" jdbcType="BOOLEAN" property="isManageVedengCode" />
    <result column="IS_BAD_GOODS" jdbcType="BOOLEAN" property="isBadGoods" />
    <result column="IS_ENABLE_FACTORY_BATCHNUM" jdbcType="BOOLEAN" property="isEnableFactoryBatchnum" />
    <result column="IS_ENABLE_MULTISTAGE_PACKAGE" jdbcType="BOOLEAN" property="isEnableMultistagePackage" />
    <result column="MID_PACKAGE_NUM" jdbcType="INTEGER" property="midPackageNum" />
    <result column="BOX_PACKAGE_NUM" jdbcType="INTEGER" property="boxPackageNum" />
    <result column="IS_ENABLE_VALIDITY_PERIOD" jdbcType="TINYINT" property="isEnableValidityPeriod" />
    <result column="NEAR_TERM_WARN_DAYS" jdbcType="INTEGER" property="nearTermWarnDays" />
    <result column="OVER_NEAR_TERM_WARN_DAYS" jdbcType="INTEGER" property="overNearTermWarnDays" />
    <result column="INSTALL_TRAIN_TYPE" jdbcType="BOOLEAN" property="installTrainType" />
    <result column="LOGISTICS_DELIVERYTYPE" jdbcType="BOOLEAN" property="logisticsDeliverytype" />
    <result column="IS_NEED_REPORT" jdbcType="BOOLEAN" property="isNeedReport" />
    <result column="IS_AUTHORIZED" jdbcType="BOOLEAN" property="isAuthorized" />
    <result column="PUSHED_ORG_ID_LIST" jdbcType="VARCHAR" property="pushedOrgIdList" />
    <result column="HAVE_STOCK_MANAGE" jdbcType="INTEGER" property="haveStockManage"/>
    <result column="COST_CATEGORY_ID" jdbcType="INTEGER" property="costCategoryId"/>
    <result column="IS_VIRTURE_SKU" jdbcType="INTEGER" property="isVirtureSku"/>
    <result column="VIRTURE_TIME" jdbcType="DATE" property="virtureTime"/>
    <result column="VIRTURE_CREATOR" jdbcType="INTEGER" property="virtureCreator"/>
  </resultMap>
  <sql id="Base_Column_List">
    SKU_ID, SPU_ID, CHECK_STATUS, MODEL, SPEC, SKU_NO, SKU_NAME, SHOW_NAME, MATERIAL_CODE, 
    SUPPLY_MODEL, IS_STOCKUP, WIKI_HREF, TECHNICAL_PARAMETER, PERFORMANCE_PARAMETER, 
    SPEC_PARAMETER, BASE_UNIT_ID, MIN_ORDER, GOODS_LENGTH, GOODS_WIDTH, GOODS_HEIGHT, 
    PACKAGE_LENGTH, PACKAGE_WIDTH, PACKAGE_HEIGHT, NET_WEIGHT, GROSS_WEIGHT, UNIT_ID, 
    CHANGE_NUM, PACKING_LIST, AFTER_SALE_CONTENT, QA_YEARS, STORAGE_CONDITION_ONE, STORAGE_CONDITION_ONE_LOWER_VALUE, 
    STORAGE_CONDITION_ONE_UPPER_VALUE, STORAGE_CONDITION_HUMIDITY_LOWER_VALUE, STORAGE_CONDITION_HUMIDITY_UPPER_VALUE, 
    STORAGE_CONDITION_TWO, EFFECTIVE_DAY_UNIT, EFFECTIVE_DAYS, QA_RULE, QA_OUT_PRICE, 
    QA_RESPONSE_TIME, HAS_BACKUP_MACHINE, SUPPLIER_EXTEND_GUARANTEE_PRICE, CORE_PARTS_PRICE_FID, 
    RETURN_GOODS_CONDITIONS, FREIGHT_INTRODUCTIONS, EXCHANGE_GOODS_CONDITIONS, EXCHANGE_GOODS_METHOD, 
    GOODS_COMMENTS, `STATUS`, ADD_TIME, CREATOR, MOD_TIME, UPDATER, CHECK_TIME, CHECKER, 
    OPERATE_INFO_ID, DELETE_REASON, LAST_CHECK_REASON, TAX_CATEGORY_NO, JX_MARKET_PRICE, 
    JX_SALE_PRICE, JX_FLAG, `SOURCE`, PUSH_STATUS, DECLARE_DELIVERY_RANGE, PRICE_VERIFY_STATUS, 
    AVGPRICE, LATEST_VALID_ORDER_USER, AVGPRICE_UPDATE_TIME, TERMINAL_PRICE, DISTRIBUTION_PRICE, 
    COST_PRICE, AVAILABLE_STOCK_NUM, STOCK_NUM, ON_SALE, GOODS_BARCODE, CURING_TYPE, 
    CURING_REASON, IS_NEED_TEST_REPROT, IS_KIT, KIT_DESC, IS_SAME_SN_CODE, IS_FACTORY_SN_CODE, 
    IS_MANAGE_VEDENG_CODE, IS_BAD_GOODS, IS_ENABLE_FACTORY_BATCHNUM, IS_ENABLE_MULTISTAGE_PACKAGE, 
    MID_PACKAGE_NUM, BOX_PACKAGE_NUM, IS_ENABLE_VALIDITY_PERIOD, NEAR_TERM_WARN_DAYS, 
    OVER_NEAR_TERM_WARN_DAYS, INSTALL_TRAIN_TYPE, LOGISTICS_DELIVERYTYPE, IS_NEED_REPORT, 
    IS_AUTHORIZED, PUSHED_ORG_ID_LIST,IS_AVAILABLE_SALE,ORG_ID_LIST,HAVE_STOCK_MANAGE,COST_CATEGORY_ID,IS_VIRTURE_SKU,VIRTURE_TIME,VIRTURE_CREATOR
  </sql>


  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from V_CORE_SKU
    where SKU_ID = #{skuId,jdbcType=INTEGER}
  </select>
  <select id="getSkuListOfUninitPriceAndStock" resultType="com.vedeng.goods.model.CoreSku">
    SELECT * FROM V_CORE_SKU WHERE SKU_ID &gt; #{startId,jdbcType=INTEGER}
    <if test="restart == 0">
      AND TERMINAL_PRICE IS NULL AND STOCK_NUM IS NULL
    </if>
    AND STATUS = 1 limit #{limit,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from V_CORE_SKU
    where SKU_ID = #{skuId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="SKU_ID" keyProperty="skuId" parameterType="com.vedeng.goods.model.CoreSku" useGeneratedKeys="true">
    insert into V_CORE_SKU (SPU_ID, CHECK_STATUS, MODEL, 
      SPEC, SKU_NO, SKU_NAME, 
      SHOW_NAME, MATERIAL_CODE, SUPPLY_MODEL, 
      IS_STOCKUP, WIKI_HREF, TECHNICAL_PARAMETER, 
      PERFORMANCE_PARAMETER, SPEC_PARAMETER, BASE_UNIT_ID, 
      MIN_ORDER, GOODS_LENGTH, GOODS_WIDTH, 
      GOODS_HEIGHT, PACKAGE_LENGTH, PACKAGE_WIDTH, 
      PACKAGE_HEIGHT, NET_WEIGHT, GROSS_WEIGHT, 
      UNIT_ID, CHANGE_NUM, PACKING_LIST, 
      AFTER_SALE_CONTENT, QA_YEARS, STORAGE_CONDITION_ONE, 
      STORAGE_CONDITION_ONE_LOWER_VALUE, STORAGE_CONDITION_ONE_UPPER_VALUE, 
      STORAGE_CONDITION_HUMIDITY_LOWER_VALUE, STORAGE_CONDITION_HUMIDITY_UPPER_VALUE, 
      STORAGE_CONDITION_TWO, EFFECTIVE_DAY_UNIT, 
      EFFECTIVE_DAYS, QA_RULE, QA_OUT_PRICE, 
      QA_RESPONSE_TIME, HAS_BACKUP_MACHINE, SUPPLIER_EXTEND_GUARANTEE_PRICE, 
      CORE_PARTS_PRICE_FID, RETURN_GOODS_CONDITIONS, 
      FREIGHT_INTRODUCTIONS, EXCHANGE_GOODS_CONDITIONS, 
      EXCHANGE_GOODS_METHOD, GOODS_COMMENTS, `STATUS`, 
      ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER, CHECK_TIME, CHECKER, 
      OPERATE_INFO_ID, DELETE_REASON, LAST_CHECK_REASON, 
      TAX_CATEGORY_NO, JX_MARKET_PRICE, JX_SALE_PRICE, 
      JX_FLAG, `SOURCE`, PUSH_STATUS, 
      DECLARE_DELIVERY_RANGE, PRICE_VERIFY_STATUS, 
      AVGPRICE, LATEST_VALID_ORDER_USER, AVGPRICE_UPDATE_TIME, 
      TERMINAL_PRICE, DISTRIBUTION_PRICE, COST_PRICE, 
      AVAILABLE_STOCK_NUM, STOCK_NUM, ON_SALE, 
      GOODS_BARCODE, CURING_TYPE, CURING_REASON, 
      IS_NEED_TEST_REPROT, IS_KIT, KIT_DESC, 
      IS_SAME_SN_CODE, IS_FACTORY_SN_CODE, IS_MANAGE_VEDENG_CODE, 
      IS_BAD_GOODS, IS_ENABLE_FACTORY_BATCHNUM, IS_ENABLE_MULTISTAGE_PACKAGE, 
      MID_PACKAGE_NUM, BOX_PACKAGE_NUM, IS_ENABLE_VALIDITY_PERIOD, 
      NEAR_TERM_WARN_DAYS, OVER_NEAR_TERM_WARN_DAYS, 
      INSTALL_TRAIN_TYPE, LOGISTICS_DELIVERYTYPE, 
      IS_NEED_REPORT, IS_AUTHORIZED)
    values (#{spuId,jdbcType=INTEGER}, #{checkStatus,jdbcType=TINYINT}, #{model,jdbcType=VARCHAR}, 
      #{spec,jdbcType=VARCHAR}, #{skuNo,jdbcType=VARCHAR}, #{skuName,jdbcType=VARCHAR}, 
      #{showName,jdbcType=VARCHAR}, #{materialCode,jdbcType=VARCHAR}, #{supplyModel,jdbcType=VARCHAR}, 
      #{isStockup,jdbcType=VARCHAR}, #{wikiHref,jdbcType=VARCHAR}, #{technicalParameter,jdbcType=VARCHAR}, 
      #{performanceParameter,jdbcType=VARCHAR}, #{specParameter,jdbcType=VARCHAR}, #{baseUnitId,jdbcType=INTEGER}, 
      #{minOrder,jdbcType=DECIMAL}, #{goodsLength,jdbcType=DECIMAL}, #{goodsWidth,jdbcType=DECIMAL}, 
      #{goodsHeight,jdbcType=DECIMAL}, #{packageLength,jdbcType=DECIMAL}, #{packageWidth,jdbcType=DECIMAL}, 
      #{packageHeight,jdbcType=DECIMAL}, #{netWeight,jdbcType=DECIMAL}, #{grossWeight,jdbcType=DECIMAL}, 
      #{unitId,jdbcType=INTEGER}, #{changeNum,jdbcType=DECIMAL}, #{packingList,jdbcType=VARCHAR}, 
      #{afterSaleContent,jdbcType=VARCHAR}, #{qaYears,jdbcType=VARCHAR}, #{storageConditionOne,jdbcType=BOOLEAN}, 
      #{storageConditionOneLowerValue,jdbcType=FLOAT}, #{storageConditionOneUpperValue,jdbcType=FLOAT}, 
      #{storageConditionHumidityLowerValue,jdbcType=FLOAT}, #{storageConditionHumidityUpperValue,jdbcType=FLOAT}, 
      #{storageConditionTwo,jdbcType=VARCHAR}, #{effectiveDayUnit,jdbcType=BOOLEAN}, 
      #{effectiveDays,jdbcType=VARCHAR}, #{qaRule,jdbcType=VARCHAR}, #{qaOutPrice,jdbcType=DECIMAL}, 
      #{qaResponseTime,jdbcType=DECIMAL}, #{hasBackupMachine,jdbcType=VARCHAR}, #{supplierExtendGuaranteePrice,jdbcType=DECIMAL}, 
      #{corePartsPriceFid,jdbcType=INTEGER}, #{returnGoodsConditions,jdbcType=BOOLEAN}, 
      #{freightIntroductions,jdbcType=VARCHAR}, #{exchangeGoodsConditions,jdbcType=VARCHAR}, 
      #{exchangeGoodsMethod,jdbcType=VARCHAR}, #{goodsComments,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER},
      #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=TIMESTAMP}, 
      #{updater,jdbcType=INTEGER}, #{checkTime,jdbcType=TIMESTAMP}, #{checker,jdbcType=INTEGER}, 
      #{operateInfoId,jdbcType=INTEGER}, #{deleteReason,jdbcType=VARCHAR}, #{lastCheckReason,jdbcType=VARCHAR}, 
      #{taxCategoryNo,jdbcType=VARCHAR}, #{jxMarketPrice,jdbcType=DECIMAL}, #{jxSalePrice,jdbcType=DECIMAL}, 
      #{jxFlag,jdbcType=INTEGER}, #{source,jdbcType=TINYINT}, #{pushStatus,jdbcType=INTEGER}, 
      #{declareDeliveryRange,jdbcType=VARCHAR}, #{priceVerifyStatus,jdbcType=INTEGER}, 
      #{avgprice,jdbcType=DECIMAL}, #{latestValidOrderUser,jdbcType=INTEGER}, #{avgpriceUpdateTime,jdbcType=TIMESTAMP}, 
      #{terminalPrice,jdbcType=DECIMAL}, #{distributionPrice,jdbcType=DECIMAL}, #{costPrice,jdbcType=DECIMAL}, 
      #{availableStockNum,jdbcType=INTEGER}, #{stockNum,jdbcType=INTEGER}, #{onSale,jdbcType=TINYINT}, 
      #{goodsBarcode,jdbcType=VARCHAR}, #{curingType,jdbcType=BOOLEAN}, #{curingReason,jdbcType=VARCHAR}, 
      #{isNeedTestReprot,jdbcType=BOOLEAN}, #{isKit,jdbcType=BOOLEAN}, #{kitDesc,jdbcType=VARCHAR}, 
      #{isSameSnCode,jdbcType=BOOLEAN}, #{isFactorySnCode,jdbcType=BOOLEAN}, #{isManageVedengCode,jdbcType=BOOLEAN}, 
      #{isBadGoods,jdbcType=BOOLEAN}, #{isEnableFactoryBatchnum,jdbcType=BOOLEAN}, #{isEnableMultistagePackage,jdbcType=BOOLEAN}, 
      #{midPackageNum,jdbcType=INTEGER}, #{boxPackageNum,jdbcType=INTEGER}, #{isEnableValidityPeriod,jdbcType=TINYINT}, 
      #{nearTermWarnDays,jdbcType=INTEGER}, #{overNearTermWarnDays,jdbcType=INTEGER}, 
      #{installTrainType,jdbcType=BOOLEAN}, #{logisticsDeliverytype,jdbcType=BOOLEAN}, 
      #{isNeedReport,jdbcType=BOOLEAN}, #{isAuthorized,jdbcType=BOOLEAN})
  </insert>
  <insert id="insertSelective" keyColumn="SKU_ID" keyProperty="skuId" parameterType="com.vedeng.goods.model.CoreSku" useGeneratedKeys="true">
    insert into V_CORE_SKU
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="spuId != null">
        SPU_ID,
      </if>
      <if test="checkStatus != null">
        CHECK_STATUS,
      </if>
      <if test="model != null">
        MODEL,
      </if>
      <if test="spec != null">
        SPEC,
      </if>
      <if test="skuNo != null">
        SKU_NO,
      </if>
      <if test="skuName != null">
        SKU_NAME,
      </if>
      <if test="showName != null">
        SHOW_NAME,
      </if>
      <if test="materialCode != null">
        MATERIAL_CODE,
      </if>
      <if test="supplyModel != null">
        SUPPLY_MODEL,
      </if>
      <if test="isStockup != null">
        IS_STOCKUP,
      </if>
      <if test="wikiHref != null">
        WIKI_HREF,
      </if>
      <if test="technicalParameter != null">
        TECHNICAL_PARAMETER,
      </if>
      <if test="performanceParameter != null">
        PERFORMANCE_PARAMETER,
      </if>
      <if test="specParameter != null">
        SPEC_PARAMETER,
      </if>
      <if test="baseUnitId != null">
        BASE_UNIT_ID,
      </if>
      <if test="minOrder != null">
        MIN_ORDER,
      </if>
      <if test="goodsLength != null">
        GOODS_LENGTH,
      </if>
      <if test="goodsWidth != null">
        GOODS_WIDTH,
      </if>
      <if test="goodsHeight != null">
        GOODS_HEIGHT,
      </if>
      <if test="packageLength != null">
        PACKAGE_LENGTH,
      </if>
      <if test="packageWidth != null">
        PACKAGE_WIDTH,
      </if>
      <if test="packageHeight != null">
        PACKAGE_HEIGHT,
      </if>
      <if test="netWeight != null">
        NET_WEIGHT,
      </if>
      <if test="grossWeight != null">
        GROSS_WEIGHT,
      </if>
      <if test="unitId != null">
        UNIT_ID,
      </if>
      <if test="changeNum != null">
        CHANGE_NUM,
      </if>
      <if test="packingList != null">
        PACKING_LIST,
      </if>
      <if test="afterSaleContent != null">
        AFTER_SALE_CONTENT,
      </if>
      <if test="qaYears != null">
        QA_YEARS,
      </if>
      <if test="storageConditionOne != null">
        STORAGE_CONDITION_ONE,
      </if>
      <if test="storageConditionOneLowerValue != null">
        STORAGE_CONDITION_ONE_LOWER_VALUE,
      </if>
      <if test="storageConditionOneUpperValue != null">
        STORAGE_CONDITION_ONE_UPPER_VALUE,
      </if>
      <if test="storageConditionHumidityLowerValue != null">
        STORAGE_CONDITION_HUMIDITY_LOWER_VALUE,
      </if>
      <if test="storageConditionHumidityUpperValue != null">
        STORAGE_CONDITION_HUMIDITY_UPPER_VALUE,
      </if>
      <if test="storageConditionTwo != null">
        STORAGE_CONDITION_TWO,
      </if>
      <if test="effectiveDayUnit != null">
        EFFECTIVE_DAY_UNIT,
      </if>
      <if test="effectiveDays != null">
        EFFECTIVE_DAYS,
      </if>
      <if test="qaRule != null">
        QA_RULE,
      </if>
      <if test="qaOutPrice != null">
        QA_OUT_PRICE,
      </if>
      <if test="qaResponseTime != null">
        QA_RESPONSE_TIME,
      </if>
      <if test="hasBackupMachine != null">
        HAS_BACKUP_MACHINE,
      </if>
      <if test="supplierExtendGuaranteePrice != null">
        SUPPLIER_EXTEND_GUARANTEE_PRICE,
      </if>
      <if test="corePartsPriceFid != null">
        CORE_PARTS_PRICE_FID,
      </if>
      <if test="returnGoodsConditions != null">
        RETURN_GOODS_CONDITIONS,
      </if>
      <if test="freightIntroductions != null">
        FREIGHT_INTRODUCTIONS,
      </if>
      <if test="exchangeGoodsConditions != null">
        EXCHANGE_GOODS_CONDITIONS,
      </if>
      <if test="exchangeGoodsMethod != null">
        EXCHANGE_GOODS_METHOD,
      </if>
      <if test="goodsComments != null">
        GOODS_COMMENTS,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="checkTime != null">
        CHECK_TIME,
      </if>
      <if test="checker != null">
        CHECKER,
      </if>
      <if test="operateInfoId != null">
        OPERATE_INFO_ID,
      </if>
      <if test="deleteReason != null">
        DELETE_REASON,
      </if>
      <if test="lastCheckReason != null">
        LAST_CHECK_REASON,
      </if>
      <if test="taxCategoryNo != null">
        TAX_CATEGORY_NO,
      </if>
      <if test="jxMarketPrice != null">
        JX_MARKET_PRICE,
      </if>
      <if test="jxSalePrice != null">
        JX_SALE_PRICE,
      </if>
      <if test="jxFlag != null">
        JX_FLAG,
      </if>
      <if test="source != null">
        `SOURCE`,
      </if>
      <if test="pushStatus != null">
        PUSH_STATUS,
      </if>
      <if test="declareDeliveryRange != null">
        DECLARE_DELIVERY_RANGE,
      </if>
      <if test="priceVerifyStatus != null">
        PRICE_VERIFY_STATUS,
      </if>
      <if test="avgprice != null">
        AVGPRICE,
      </if>
      <if test="latestValidOrderUser != null">
        LATEST_VALID_ORDER_USER,
      </if>
      <if test="avgpriceUpdateTime != null">
        AVGPRICE_UPDATE_TIME,
      </if>
      <if test="terminalPrice != null">
        TERMINAL_PRICE,
      </if>
      <if test="distributionPrice != null">
        DISTRIBUTION_PRICE,
      </if>
      <if test="costPrice != null">
        COST_PRICE,
      </if>
      <if test="availableStockNum != null">
        AVAILABLE_STOCK_NUM,
      </if>
      <if test="stockNum != null">
        STOCK_NUM,
      </if>
      <if test="onSale != null">
        ON_SALE,
      </if>
      <if test="goodsBarcode != null">
        GOODS_BARCODE,
      </if>
      <if test="curingType != null">
        CURING_TYPE,
      </if>
      <if test="curingReason != null">
        CURING_REASON,
      </if>
      <if test="isNeedTestReprot != null">
        IS_NEED_TEST_REPROT,
      </if>
      <if test="isKit != null">
        IS_KIT,
      </if>
      <if test="kitDesc != null">
        KIT_DESC,
      </if>
      <if test="isSameSnCode != null">
        IS_SAME_SN_CODE,
      </if>
      <if test="isFactorySnCode != null">
        IS_FACTORY_SN_CODE,
      </if>
      <if test="isManageVedengCode != null">
        IS_MANAGE_VEDENG_CODE,
      </if>
      <if test="isBadGoods != null">
        IS_BAD_GOODS,
      </if>
      <if test="isEnableFactoryBatchnum != null">
        IS_ENABLE_FACTORY_BATCHNUM,
      </if>
      <if test="isEnableMultistagePackage != null">
        IS_ENABLE_MULTISTAGE_PACKAGE,
      </if>
      <if test="midPackageNum != null">
        MID_PACKAGE_NUM,
      </if>
      <if test="boxPackageNum != null">
        BOX_PACKAGE_NUM,
      </if>
      <if test="isEnableValidityPeriod != null">
        IS_ENABLE_VALIDITY_PERIOD,
      </if>
      <if test="nearTermWarnDays != null">
        NEAR_TERM_WARN_DAYS,
      </if>
      <if test="overNearTermWarnDays != null">
        OVER_NEAR_TERM_WARN_DAYS,
      </if>
      <if test="installTrainType != null">
        INSTALL_TRAIN_TYPE,
      </if>
      <if test="logisticsDeliverytype != null">
        LOGISTICS_DELIVERYTYPE,
      </if>
      <if test="isNeedReport != null">
        IS_NEED_REPORT,
      </if>
      <if test="isAuthorized != null">
        IS_AUTHORIZED,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="spuId != null">
        #{spuId,jdbcType=INTEGER},
      </if>
      <if test="checkStatus != null">
        #{checkStatus,jdbcType=TINYINT},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="spec != null">
        #{spec,jdbcType=VARCHAR},
      </if>
      <if test="skuNo != null">
        #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="showName != null">
        #{showName,jdbcType=VARCHAR},
      </if>
      <if test="materialCode != null">
        #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="supplyModel != null">
        #{supplyModel,jdbcType=VARCHAR},
      </if>
      <if test="isStockup != null">
        #{isStockup,jdbcType=VARCHAR},
      </if>
      <if test="wikiHref != null">
        #{wikiHref,jdbcType=VARCHAR},
      </if>
      <if test="technicalParameter != null">
        #{technicalParameter,jdbcType=VARCHAR},
      </if>
      <if test="performanceParameter != null">
        #{performanceParameter,jdbcType=VARCHAR},
      </if>
      <if test="specParameter != null">
        #{specParameter,jdbcType=VARCHAR},
      </if>
      <if test="baseUnitId != null">
        #{baseUnitId,jdbcType=INTEGER},
      </if>
      <if test="minOrder != null">
        #{minOrder,jdbcType=DECIMAL},
      </if>
      <if test="goodsLength != null">
        #{goodsLength,jdbcType=DECIMAL},
      </if>
      <if test="goodsWidth != null">
        #{goodsWidth,jdbcType=DECIMAL},
      </if>
      <if test="goodsHeight != null">
        #{goodsHeight,jdbcType=DECIMAL},
      </if>
      <if test="packageLength != null">
        #{packageLength,jdbcType=DECIMAL},
      </if>
      <if test="packageWidth != null">
        #{packageWidth,jdbcType=DECIMAL},
      </if>
      <if test="packageHeight != null">
        #{packageHeight,jdbcType=DECIMAL},
      </if>
      <if test="netWeight != null">
        #{netWeight,jdbcType=DECIMAL},
      </if>
      <if test="grossWeight != null">
        #{grossWeight,jdbcType=DECIMAL},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="changeNum != null">
        #{changeNum,jdbcType=DECIMAL},
      </if>
      <if test="packingList != null">
        #{packingList,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleContent != null">
        #{afterSaleContent,jdbcType=VARCHAR},
      </if>
      <if test="qaYears != null">
        #{qaYears,jdbcType=VARCHAR},
      </if>
      <if test="storageConditionOne != null">
        #{storageConditionOne,jdbcType=BOOLEAN},
      </if>
      <if test="storageConditionOneLowerValue != null">
        #{storageConditionOneLowerValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionOneUpperValue != null">
        #{storageConditionOneUpperValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionHumidityLowerValue != null">
        #{storageConditionHumidityLowerValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionHumidityUpperValue != null">
        #{storageConditionHumidityUpperValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionTwo != null">
        #{storageConditionTwo,jdbcType=VARCHAR},
      </if>
      <if test="effectiveDayUnit != null">
        #{effectiveDayUnit,jdbcType=BOOLEAN},
      </if>
      <if test="effectiveDays != null">
        #{effectiveDays,jdbcType=VARCHAR},
      </if>
      <if test="qaRule != null">
        #{qaRule,jdbcType=VARCHAR},
      </if>
      <if test="qaOutPrice != null">
        #{qaOutPrice,jdbcType=DECIMAL},
      </if>
      <if test="qaResponseTime != null">
        #{qaResponseTime,jdbcType=DECIMAL},
      </if>
      <if test="hasBackupMachine != null">
        #{hasBackupMachine,jdbcType=VARCHAR},
      </if>
      <if test="supplierExtendGuaranteePrice != null">
        #{supplierExtendGuaranteePrice,jdbcType=DECIMAL},
      </if>
      <if test="corePartsPriceFid != null">
        #{corePartsPriceFid,jdbcType=INTEGER},
      </if>
      <if test="returnGoodsConditions != null">
        #{returnGoodsConditions,jdbcType=BOOLEAN},
      </if>
      <if test="freightIntroductions != null">
        #{freightIntroductions,jdbcType=VARCHAR},
      </if>
      <if test="exchangeGoodsConditions != null">
        #{exchangeGoodsConditions,jdbcType=VARCHAR},
      </if>
      <if test="exchangeGoodsMethod != null">
        #{exchangeGoodsMethod,jdbcType=VARCHAR},
      </if>
      <if test="goodsComments != null">
        #{goodsComments,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="checkTime != null">
        #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checker != null">
        #{checker,jdbcType=INTEGER},
      </if>
      <if test="operateInfoId != null">
        #{operateInfoId,jdbcType=INTEGER},
      </if>
      <if test="deleteReason != null">
        #{deleteReason,jdbcType=VARCHAR},
      </if>
      <if test="lastCheckReason != null">
        #{lastCheckReason,jdbcType=VARCHAR},
      </if>
      <if test="taxCategoryNo != null">
        #{taxCategoryNo,jdbcType=VARCHAR},
      </if>
      <if test="jxMarketPrice != null">
        #{jxMarketPrice,jdbcType=DECIMAL},
      </if>
      <if test="jxSalePrice != null">
        #{jxSalePrice,jdbcType=DECIMAL},
      </if>
      <if test="jxFlag != null">
        #{jxFlag,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
      <if test="pushStatus != null">
        #{pushStatus,jdbcType=INTEGER},
      </if>
      <if test="declareDeliveryRange != null">
        #{declareDeliveryRange,jdbcType=VARCHAR},
      </if>
      <if test="priceVerifyStatus != null">
        #{priceVerifyStatus,jdbcType=INTEGER},
      </if>
      <if test="avgprice != null">
        #{avgprice,jdbcType=DECIMAL},
      </if>
      <if test="latestValidOrderUser != null">
        #{latestValidOrderUser,jdbcType=INTEGER},
      </if>
      <if test="avgpriceUpdateTime != null">
        #{avgpriceUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="terminalPrice != null">
        #{terminalPrice,jdbcType=DECIMAL},
      </if>
      <if test="distributionPrice != null">
        #{distributionPrice,jdbcType=DECIMAL},
      </if>
      <if test="costPrice != null">
        #{costPrice,jdbcType=DECIMAL},
      </if>
      <if test="availableStockNum != null">
        #{availableStockNum,jdbcType=INTEGER},
      </if>
      <if test="stockNum != null">
        #{stockNum,jdbcType=INTEGER},
      </if>
      <if test="onSale != null">
        #{onSale,jdbcType=TINYINT},
      </if>
      <if test="goodsBarcode != null">
        #{goodsBarcode,jdbcType=VARCHAR},
      </if>
      <if test="curingType != null">
        #{curingType,jdbcType=BOOLEAN},
      </if>
      <if test="curingReason != null">
        #{curingReason,jdbcType=VARCHAR},
      </if>
      <if test="isNeedTestReprot != null">
        #{isNeedTestReprot,jdbcType=BOOLEAN},
      </if>
      <if test="isKit != null">
        #{isKit,jdbcType=BOOLEAN},
      </if>
      <if test="kitDesc != null">
        #{kitDesc,jdbcType=VARCHAR},
      </if>
      <if test="isSameSnCode != null">
        #{isSameSnCode,jdbcType=BOOLEAN},
      </if>
      <if test="isFactorySnCode != null">
        #{isFactorySnCode,jdbcType=BOOLEAN},
      </if>
      <if test="isManageVedengCode != null">
        #{isManageVedengCode,jdbcType=BOOLEAN},
      </if>
      <if test="isBadGoods != null">
        #{isBadGoods,jdbcType=BOOLEAN},
      </if>
      <if test="isEnableFactoryBatchnum != null">
        #{isEnableFactoryBatchnum,jdbcType=BOOLEAN},
      </if>
      <if test="isEnableMultistagePackage != null">
        #{isEnableMultistagePackage,jdbcType=BOOLEAN},
      </if>
      <if test="midPackageNum != null">
        #{midPackageNum,jdbcType=INTEGER},
      </if>
      <if test="boxPackageNum != null">
        #{boxPackageNum,jdbcType=INTEGER},
      </if>
      <if test="isEnableValidityPeriod != null">
        #{isEnableValidityPeriod,jdbcType=TINYINT},
      </if>
      <if test="nearTermWarnDays != null">
        #{nearTermWarnDays,jdbcType=INTEGER},
      </if>
      <if test="overNearTermWarnDays != null">
        #{overNearTermWarnDays,jdbcType=INTEGER},
      </if>
      <if test="installTrainType != null">
        #{installTrainType,jdbcType=BOOLEAN},
      </if>
      <if test="logisticsDeliverytype != null">
        #{logisticsDeliverytype,jdbcType=BOOLEAN},
      </if>
      <if test="isNeedReport != null">
        #{isNeedReport,jdbcType=BOOLEAN},
      </if>
      <if test="isAuthorized != null">
        #{isAuthorized,jdbcType=BOOLEAN},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.model.CoreSku">
    update V_CORE_SKU
    <set>
      <if test="spuId != null">
        SPU_ID = #{spuId,jdbcType=INTEGER},
      </if>
      <if test="checkStatus != null">
        CHECK_STATUS = #{checkStatus,jdbcType=TINYINT},
      </if>
      <if test="model != null">
        MODEL = #{model,jdbcType=VARCHAR},
      </if>
      <if test="spec != null">
        SPEC = #{spec,jdbcType=VARCHAR},
      </if>
      <if test="skuNo != null">
        SKU_NO = #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        SKU_NAME = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="showName != null">
        SHOW_NAME = #{showName,jdbcType=VARCHAR},
      </if>
      <if test="materialCode != null">
        MATERIAL_CODE = #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="supplyModel != null">
        SUPPLY_MODEL = #{supplyModel,jdbcType=VARCHAR},
      </if>
      <if test="isStockup != null">
        IS_STOCKUP = #{isStockup,jdbcType=VARCHAR},
      </if>
      <if test="wikiHref != null">
        WIKI_HREF = #{wikiHref,jdbcType=VARCHAR},
      </if>
      <if test="technicalParameter != null">
        TECHNICAL_PARAMETER = #{technicalParameter,jdbcType=VARCHAR},
      </if>
      <if test="performanceParameter != null">
        PERFORMANCE_PARAMETER = #{performanceParameter,jdbcType=VARCHAR},
      </if>
      <if test="specParameter != null">
        SPEC_PARAMETER = #{specParameter,jdbcType=VARCHAR},
      </if>
      <if test="baseUnitId != null">
        BASE_UNIT_ID = #{baseUnitId,jdbcType=INTEGER},
      </if>
      <if test="minOrder != null">
        MIN_ORDER = #{minOrder,jdbcType=DECIMAL},
      </if>
      <if test="goodsLength != null">
        GOODS_LENGTH = #{goodsLength,jdbcType=DECIMAL},
      </if>
      <if test="goodsWidth != null">
        GOODS_WIDTH = #{goodsWidth,jdbcType=DECIMAL},
      </if>
      <if test="goodsHeight != null">
        GOODS_HEIGHT = #{goodsHeight,jdbcType=DECIMAL},
      </if>
      <if test="packageLength != null">
        PACKAGE_LENGTH = #{packageLength,jdbcType=DECIMAL},
      </if>
      <if test="packageWidth != null">
        PACKAGE_WIDTH = #{packageWidth,jdbcType=DECIMAL},
      </if>
      <if test="packageHeight != null">
        PACKAGE_HEIGHT = #{packageHeight,jdbcType=DECIMAL},
      </if>
      <if test="netWeight != null">
        NET_WEIGHT = #{netWeight,jdbcType=DECIMAL},
      </if>
      <if test="grossWeight != null">
        GROSS_WEIGHT = #{grossWeight,jdbcType=DECIMAL},
      </if>
      <if test="unitId != null">
        UNIT_ID = #{unitId,jdbcType=INTEGER},
      </if>
      <if test="changeNum != null">
        CHANGE_NUM = #{changeNum,jdbcType=DECIMAL},
      </if>
      <if test="packingList != null">
        PACKING_LIST = #{packingList,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleContent != null">
        AFTER_SALE_CONTENT = #{afterSaleContent,jdbcType=VARCHAR},
      </if>
      <if test="qaYears != null">
        QA_YEARS = #{qaYears,jdbcType=VARCHAR},
      </if>
      <if test="storageConditionOne != null">
        STORAGE_CONDITION_ONE = #{storageConditionOne,jdbcType=INTEGER},
      </if>
      <if test="storageConditionOneLowerValue != null">
        STORAGE_CONDITION_ONE_LOWER_VALUE = #{storageConditionOneLowerValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionOneUpperValue != null">
        STORAGE_CONDITION_ONE_UPPER_VALUE = #{storageConditionOneUpperValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionHumidityLowerValue != null">
        STORAGE_CONDITION_HUMIDITY_LOWER_VALUE = #{storageConditionHumidityLowerValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionHumidityUpperValue != null">
        STORAGE_CONDITION_HUMIDITY_UPPER_VALUE = #{storageConditionHumidityUpperValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionTwo != null">
        STORAGE_CONDITION_TWO = #{storageConditionTwo,jdbcType=VARCHAR},
      </if>
      <if test="effectiveDayUnit != null">
        EFFECTIVE_DAY_UNIT = #{effectiveDayUnit,jdbcType=BOOLEAN},
      </if>
      <if test="effectiveDays != null">
        EFFECTIVE_DAYS = #{effectiveDays,jdbcType=VARCHAR},
      </if>
      <if test="qaRule != null">
        QA_RULE = #{qaRule,jdbcType=VARCHAR},
      </if>
      <if test="qaOutPrice != null">
        QA_OUT_PRICE = #{qaOutPrice,jdbcType=DECIMAL},
      </if>
      <if test="qaResponseTime != null">
        QA_RESPONSE_TIME = #{qaResponseTime,jdbcType=DECIMAL},
      </if>
      <if test="hasBackupMachine != null">
        HAS_BACKUP_MACHINE = #{hasBackupMachine,jdbcType=VARCHAR},
      </if>
      <if test="supplierExtendGuaranteePrice != null">
        SUPPLIER_EXTEND_GUARANTEE_PRICE = #{supplierExtendGuaranteePrice,jdbcType=DECIMAL},
      </if>
      <if test="corePartsPriceFid != null">
        CORE_PARTS_PRICE_FID = #{corePartsPriceFid,jdbcType=INTEGER},
      </if>
      <if test="returnGoodsConditions != null">
        RETURN_GOODS_CONDITIONS = #{returnGoodsConditions,jdbcType=BOOLEAN},
      </if>
      <if test="freightIntroductions != null">
        FREIGHT_INTRODUCTIONS = #{freightIntroductions,jdbcType=VARCHAR},
      </if>
      <if test="exchangeGoodsConditions != null">
        EXCHANGE_GOODS_CONDITIONS = #{exchangeGoodsConditions,jdbcType=VARCHAR},
      </if>
      <if test="exchangeGoodsMethod != null">
        EXCHANGE_GOODS_METHOD = #{exchangeGoodsMethod,jdbcType=VARCHAR},
      </if>
      <if test="goodsComments != null">
        GOODS_COMMENTS = #{goodsComments,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `STATUS` = #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="checkTime != null">
        CHECK_TIME = #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checker != null">
        CHECKER = #{checker,jdbcType=INTEGER},
      </if>
      <if test="operateInfoId != null">
        OPERATE_INFO_ID = #{operateInfoId,jdbcType=INTEGER},
      </if>
      <if test="deleteReason != null">
        DELETE_REASON = #{deleteReason,jdbcType=VARCHAR},
      </if>
      <if test="lastCheckReason != null">
        LAST_CHECK_REASON = #{lastCheckReason,jdbcType=VARCHAR},
      </if>
      <if test="taxCategoryNo != null">
        TAX_CATEGORY_NO = #{taxCategoryNo,jdbcType=VARCHAR},
      </if>
      <if test="jxMarketPrice != null">
        JX_MARKET_PRICE = #{jxMarketPrice,jdbcType=DECIMAL},
      </if>
      <if test="jxSalePrice != null">
        JX_SALE_PRICE = #{jxSalePrice,jdbcType=DECIMAL},
      </if>
      <if test="jxFlag != null">
        JX_FLAG = #{jxFlag,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        `SOURCE` = #{source,jdbcType=TINYINT},
      </if>
      <if test="pushStatus != null">
        PUSH_STATUS = #{pushStatus,jdbcType=INTEGER},
      </if>
      <if test="declareDeliveryRange != null">
        DECLARE_DELIVERY_RANGE = #{declareDeliveryRange,jdbcType=VARCHAR},
      </if>
      <if test="priceVerifyStatus != null">
        PRICE_VERIFY_STATUS = #{priceVerifyStatus,jdbcType=INTEGER},
      </if>
      <if test="avgprice != null">
        AVGPRICE = #{avgprice,jdbcType=DECIMAL},
      </if>
      <if test="latestValidOrderUser != null">
        LATEST_VALID_ORDER_USER = #{latestValidOrderUser,jdbcType=INTEGER},
      </if>
      <if test="avgpriceUpdateTime != null">
        AVGPRICE_UPDATE_TIME = #{avgpriceUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="terminalPrice != null">
        TERMINAL_PRICE = #{terminalPrice,jdbcType=DECIMAL},
      </if>
      <if test="distributionPrice != null">
        DISTRIBUTION_PRICE = #{distributionPrice,jdbcType=DECIMAL},
      </if>
      <if test="costPrice != null">
        COST_PRICE = #{costPrice,jdbcType=DECIMAL},
      </if>
      <if test="availableStockNum != null">
        AVAILABLE_STOCK_NUM = #{availableStockNum,jdbcType=INTEGER},
      </if>
      <if test="stockNum != null">
        STOCK_NUM = #{stockNum,jdbcType=INTEGER},
      </if>
      <if test="onSale != null">
        ON_SALE = #{onSale,jdbcType=TINYINT},
      </if>
      <if test="goodsBarcode != null">
        GOODS_BARCODE = #{goodsBarcode,jdbcType=VARCHAR},
      </if>
      <if test="curingType != null">
        CURING_TYPE = #{curingType,jdbcType=BOOLEAN},
      </if>
      <if test="curingReason != null">
        CURING_REASON = #{curingReason,jdbcType=VARCHAR},
      </if>
      <if test="isNeedTestReprot != null">
        IS_NEED_TEST_REPROT = #{isNeedTestReprot,jdbcType=BOOLEAN},
      </if>
      <if test="isKit != null">
        IS_KIT = #{isKit,jdbcType=BOOLEAN},
      </if>
      <if test="kitDesc != null">
        KIT_DESC = #{kitDesc,jdbcType=VARCHAR},
      </if>
      <if test="isSameSnCode != null">
        IS_SAME_SN_CODE = #{isSameSnCode,jdbcType=BOOLEAN},
      </if>
      <if test="isFactorySnCode != null">
        IS_FACTORY_SN_CODE = #{isFactorySnCode,jdbcType=BOOLEAN},
      </if>
      <if test="isManageVedengCode != null">
        IS_MANAGE_VEDENG_CODE = #{isManageVedengCode,jdbcType=BOOLEAN},
      </if>
      <if test="isBadGoods != null">
        IS_BAD_GOODS = #{isBadGoods,jdbcType=BOOLEAN},
      </if>
      <if test="isEnableFactoryBatchnum != null">
        IS_ENABLE_FACTORY_BATCHNUM = #{isEnableFactoryBatchnum,jdbcType=BOOLEAN},
      </if>
      <if test="isEnableMultistagePackage != null">
        IS_ENABLE_MULTISTAGE_PACKAGE = #{isEnableMultistagePackage,jdbcType=BOOLEAN},
      </if>
      <if test="midPackageNum != null">
        MID_PACKAGE_NUM = #{midPackageNum,jdbcType=INTEGER},
      </if>
      <if test="boxPackageNum != null">
        BOX_PACKAGE_NUM = #{boxPackageNum,jdbcType=INTEGER},
      </if>
      <if test="isEnableValidityPeriod != null">
        IS_ENABLE_VALIDITY_PERIOD = #{isEnableValidityPeriod,jdbcType=TINYINT},
      </if>
      <if test="nearTermWarnDays != null">
        NEAR_TERM_WARN_DAYS = #{nearTermWarnDays,jdbcType=INTEGER},
      </if>
      <if test="overNearTermWarnDays != null">
        OVER_NEAR_TERM_WARN_DAYS = #{overNearTermWarnDays,jdbcType=INTEGER},
      </if>
      <if test="installTrainType != null">
        INSTALL_TRAIN_TYPE = #{installTrainType,jdbcType=BOOLEAN},
      </if>
      <if test="logisticsDeliverytype != null">
        LOGISTICS_DELIVERYTYPE = #{logisticsDeliverytype,jdbcType=BOOLEAN},
      </if>
      <if test="isNeedReport != null">
        IS_NEED_REPORT = #{isNeedReport,jdbcType=BOOLEAN},
      </if>
      <if test="isAuthorized != null">
        IS_AUTHORIZED = #{isAuthorized,jdbcType=BOOLEAN},
      </if>
      <if test="pushedOrgIdList != null">
        PUSHED_ORG_ID_LIST = #{pushedOrgIdList,jdbcType=VARCHAR},
      </if>
      <if test="isAvailableSale != null">
        IS_AVAILABLE_SALE = #{isAvailableSale,jdbcType=INTEGER},
      </if>
      <if test="disabledReason != null">
        DISABLED_REASON = #{disabledReason,jdbcType=VARCHAR},
      </if>
      <if test="orgIdList != null">
        ORG_ID_LIST = #{orgIdList, jdbcType=VARCHAR},
      </if>
      <if test="haveStockManage != null">
        HAVE_STOCK_MANAGE = #{haveStockManage, jdbcType=INTEGER},
      </if>
      <if test="costCategoryId != null">
        COST_CATEGORY_ID = #{costCategoryId, jdbcType=INTEGER},
      </if>
      <if test="isVirtureSku != null">
        IS_VIRTURE_SKU = #{isVirtureSku, jdbcType=INTEGER},
      </if>
      <if test="virtureTime != null">
        VIRTURE_TIME = #{virtureTime, jdbcType=TIMESTAMP},
      </if>
      <if test="virtureCreator != null">
        VIRTURE_CREATOR = #{virtureCreator, jdbcType=INTEGER},
      </if>
      <if test="taxCategoryNoRecord != null">
        TAX_CATEGORY_NO_RECORD = #{taxCategoryNoRecord, jdbcType=INTEGER},
      </if>
    </set>
    where SKU_ID = #{skuId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.model.CoreSku">
    update V_CORE_SKU
    set SPU_ID = #{spuId,jdbcType=INTEGER},
      CHECK_STATUS = #{checkStatus,jdbcType=TINYINT},
      MODEL = #{model,jdbcType=VARCHAR},
      SPEC = #{spec,jdbcType=VARCHAR},
      SKU_NO = #{skuNo,jdbcType=VARCHAR},
      SKU_NAME = #{skuName,jdbcType=VARCHAR},
      SHOW_NAME = #{showName,jdbcType=VARCHAR},
      MATERIAL_CODE = #{materialCode,jdbcType=VARCHAR},
      SUPPLY_MODEL = #{supplyModel,jdbcType=VARCHAR},
      IS_STOCKUP = #{isStockup,jdbcType=VARCHAR},
      WIKI_HREF = #{wikiHref,jdbcType=VARCHAR},
      TECHNICAL_PARAMETER = #{technicalParameter,jdbcType=VARCHAR},
      PERFORMANCE_PARAMETER = #{performanceParameter,jdbcType=VARCHAR},
      SPEC_PARAMETER = #{specParameter,jdbcType=VARCHAR},
      BASE_UNIT_ID = #{baseUnitId,jdbcType=INTEGER},
      MIN_ORDER = #{minOrder,jdbcType=DECIMAL},
      GOODS_LENGTH = #{goodsLength,jdbcType=DECIMAL},
      GOODS_WIDTH = #{goodsWidth,jdbcType=DECIMAL},
      GOODS_HEIGHT = #{goodsHeight,jdbcType=DECIMAL},
      PACKAGE_LENGTH = #{packageLength,jdbcType=DECIMAL},
      PACKAGE_WIDTH = #{packageWidth,jdbcType=DECIMAL},
      PACKAGE_HEIGHT = #{packageHeight,jdbcType=DECIMAL},
      NET_WEIGHT = #{netWeight,jdbcType=DECIMAL},
      GROSS_WEIGHT = #{grossWeight,jdbcType=DECIMAL},
      UNIT_ID = #{unitId,jdbcType=INTEGER},
      CHANGE_NUM = #{changeNum,jdbcType=DECIMAL},
      PACKING_LIST = #{packingList,jdbcType=VARCHAR},
      AFTER_SALE_CONTENT = #{afterSaleContent,jdbcType=VARCHAR},
      QA_YEARS = #{qaYears,jdbcType=VARCHAR},
      STORAGE_CONDITION_ONE = #{storageConditionOne,jdbcType=BOOLEAN},
      STORAGE_CONDITION_ONE_LOWER_VALUE = #{storageConditionOneLowerValue,jdbcType=FLOAT},
      STORAGE_CONDITION_ONE_UPPER_VALUE = #{storageConditionOneUpperValue,jdbcType=FLOAT},
      STORAGE_CONDITION_HUMIDITY_LOWER_VALUE = #{storageConditionHumidityLowerValue,jdbcType=FLOAT},
      STORAGE_CONDITION_HUMIDITY_UPPER_VALUE = #{storageConditionHumidityUpperValue,jdbcType=FLOAT},
      STORAGE_CONDITION_TWO = #{storageConditionTwo,jdbcType=VARCHAR},
      EFFECTIVE_DAY_UNIT = #{effectiveDayUnit,jdbcType=BOOLEAN},
      EFFECTIVE_DAYS = #{effectiveDays,jdbcType=VARCHAR},
      QA_RULE = #{qaRule,jdbcType=VARCHAR},
      QA_OUT_PRICE = #{qaOutPrice,jdbcType=DECIMAL},
      QA_RESPONSE_TIME = #{qaResponseTime,jdbcType=DECIMAL},
      HAS_BACKUP_MACHINE = #{hasBackupMachine,jdbcType=VARCHAR},
      SUPPLIER_EXTEND_GUARANTEE_PRICE = #{supplierExtendGuaranteePrice,jdbcType=DECIMAL},
      CORE_PARTS_PRICE_FID = #{corePartsPriceFid,jdbcType=INTEGER},
      RETURN_GOODS_CONDITIONS = #{returnGoodsConditions,jdbcType=BOOLEAN},
      FREIGHT_INTRODUCTIONS = #{freightIntroductions,jdbcType=VARCHAR},
      EXCHANGE_GOODS_CONDITIONS = #{exchangeGoodsConditions,jdbcType=VARCHAR},
      EXCHANGE_GOODS_METHOD = #{exchangeGoodsMethod,jdbcType=VARCHAR},
      GOODS_COMMENTS = #{goodsComments,jdbcType=VARCHAR},
      `STATUS` = #{status,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      CHECK_TIME = #{checkTime,jdbcType=TIMESTAMP},
      CHECKER = #{checker,jdbcType=INTEGER},
      OPERATE_INFO_ID = #{operateInfoId,jdbcType=INTEGER},
      DELETE_REASON = #{deleteReason,jdbcType=VARCHAR},
      LAST_CHECK_REASON = #{lastCheckReason,jdbcType=VARCHAR},
      TAX_CATEGORY_NO = #{taxCategoryNo,jdbcType=VARCHAR},
      JX_MARKET_PRICE = #{jxMarketPrice,jdbcType=DECIMAL},
      JX_SALE_PRICE = #{jxSalePrice,jdbcType=DECIMAL},
      JX_FLAG = #{jxFlag,jdbcType=INTEGER},
      `SOURCE` = #{source,jdbcType=TINYINT},
      PUSH_STATUS = #{pushStatus,jdbcType=INTEGER},
      DECLARE_DELIVERY_RANGE = #{declareDeliveryRange,jdbcType=VARCHAR},
      PRICE_VERIFY_STATUS = #{priceVerifyStatus,jdbcType=INTEGER},
      AVGPRICE = #{avgprice,jdbcType=DECIMAL},
      LATEST_VALID_ORDER_USER = #{latestValidOrderUser,jdbcType=INTEGER},
      AVGPRICE_UPDATE_TIME = #{avgpriceUpdateTime,jdbcType=TIMESTAMP},
      TERMINAL_PRICE = #{terminalPrice,jdbcType=DECIMAL},
      DISTRIBUTION_PRICE = #{distributionPrice,jdbcType=DECIMAL},
      COST_PRICE = #{costPrice,jdbcType=DECIMAL},
      AVAILABLE_STOCK_NUM = #{availableStockNum,jdbcType=INTEGER},
      STOCK_NUM = #{stockNum,jdbcType=INTEGER},
      ON_SALE = #{onSale,jdbcType=TINYINT},
      GOODS_BARCODE = #{goodsBarcode,jdbcType=VARCHAR},
      CURING_TYPE = #{curingType,jdbcType=BOOLEAN},
      CURING_REASON = #{curingReason,jdbcType=VARCHAR},
      IS_NEED_TEST_REPROT = #{isNeedTestReprot,jdbcType=BOOLEAN},
      IS_KIT = #{isKit,jdbcType=BOOLEAN},
      KIT_DESC = #{kitDesc,jdbcType=VARCHAR},
      IS_SAME_SN_CODE = #{isSameSnCode,jdbcType=BOOLEAN},
      IS_FACTORY_SN_CODE = #{isFactorySnCode,jdbcType=BOOLEAN},
      IS_MANAGE_VEDENG_CODE = #{isManageVedengCode,jdbcType=BOOLEAN},
      IS_BAD_GOODS = #{isBadGoods,jdbcType=BOOLEAN},
      IS_ENABLE_FACTORY_BATCHNUM = #{isEnableFactoryBatchnum,jdbcType=BOOLEAN},
      IS_ENABLE_MULTISTAGE_PACKAGE = #{isEnableMultistagePackage,jdbcType=BOOLEAN},
      MID_PACKAGE_NUM = #{midPackageNum,jdbcType=INTEGER},
      BOX_PACKAGE_NUM = #{boxPackageNum,jdbcType=INTEGER},
      IS_ENABLE_VALIDITY_PERIOD = #{isEnableValidityPeriod,jdbcType=TINYINT},
      NEAR_TERM_WARN_DAYS = #{nearTermWarnDays,jdbcType=INTEGER},
      OVER_NEAR_TERM_WARN_DAYS = #{overNearTermWarnDays,jdbcType=INTEGER},
      INSTALL_TRAIN_TYPE = #{installTrainType,jdbcType=BOOLEAN},
      LOGISTICS_DELIVERYTYPE = #{logisticsDeliverytype,jdbcType=BOOLEAN},
      IS_NEED_REPORT = #{isNeedReport,jdbcType=BOOLEAN},
      IS_AUTHORIZED = #{isAuthorized,jdbcType=BOOLEAN}
    where SKU_ID = #{skuId,jdbcType=INTEGER}
  </update>

  <update id="updatePriceOfSku">
    UPDATE V_CORE_SKU SET TERMINAL_PRICE = #{terminalPrice,jdbcType=DECIMAL},DISTRIBUTION_PRICE = #{distributionPrice,jdbcType=DECIMAL},COST_PRICE = #{costPrice,jdbcType=DECIMAL} WHERE SKU_ID = #{skuId,jdbcType=INTEGER}
  </update>

  <update id="initOneYearSaleNum">
     UPDATE V_CORE_SKU SET ONE_YEAR_SALE_NUM=0 WHERE SKU_ID>0
  </update>

  <update id="updateOneYearSaleNum" parameterType="com.newtask.model.SkuSaleNum">
    UPDATE V_CORE_SKU SET ONE_YEAR_SALE_NUM=#{saleNum} WHERE SKU_NO=#{skuNo}
  </update>
  <update id="update80PercentFlagBySkuIdList">
    update
        V_CORE_SKU
    <set>
      <if test="timeFrame != null and timeFrame == 1">
        ONE_MONTH_RATIO_EIGHTY_SORT =
            <foreach collection="toChangeSkuIdSortList" item="item" index="index"
                   separator=" " open="case" close="end">
              when SKU_ID = #{item.skuId} then #{item.sort}
            </foreach>
      </if>
      <if test="timeFrame != null and timeFrame == 3">
        THREE_MONTH_RATIO_EIGHTY_SORT =
            <foreach collection="toChangeSkuIdSortList" item="item" index="index"
                   separator=" " open="case" close="end">
              when SKU_ID = #{item.skuId} then #{item.sort}
            </foreach>
      </if>
      <if test="timeFrame != null and timeFrame == 12">
        LAST_YEAR_RATIO_EIGHTY_SORT =
            <foreach collection="toChangeSkuIdSortList" item="item" index="index"
                   separator=" " open="case" close="end">
              when SKU_ID = #{item.skuId} then #{item.sort}
            </foreach>
      </if>
    </set>
    where
        SKU_ID in
        <foreach collection="toChangeSkuIdSortList" item="item" separator="," open="(" close=")">
          #{item.skuId}
        </foreach>

  </update>
  <update id="update80PercentFlag">
    update
        V_CORE_SKU
    set
        ONE_MONTH_RATIO_EIGHTY_SORT = 0,
        THREE_MONTH_RATIO_EIGHTY_SORT = 0,
        LAST_YEAR_RATIO_EIGHTY_SORT = 0
  </update>
  <update id="updateSpuTypeBySpuId">
    <foreach collection="coreSpuList" item="item" index="index" open="" close="" separator=";">
      update V_CORE_SKU
      <set>
        SPU_TYPE=#{item.spuType}
      </set>
      where SPU_ID = #{item.spuId} and date( MOD_TIME ) = date_sub( #{nowDate}, INTERVAL 1 DAY )
    </foreach>
  </update>

  <select id="getSkuListByNoList" parameterType="java.util.List" resultType="com.vedeng.firstengage.model.vo.RegisterSkuVo$Sku">
     SELECT K.SKU_ID,K.SKU_NO,P.SPU_NAME AS SKU_NAME,P.SPECS_MODEL AS MODEL FROM V_CORE_SKU K LEFT JOIN V_CORE_SPU P ON K.SPU_ID=P.SPU_ID
     WHERE K.SKU_NO IN
    <foreach collection="list" item="no" separator="," open="(" close=")">
      #{no}
    </foreach>
     ORDER BY K.SKU_ID
  </select>
  <select id="getSkuInfoByNo" resultType="com.vedeng.goods.model.CoreSku">
    select
    *
    from V_CORE_SKU
    WHERE SKU_NO=#{skuNo}
  </select>
    <select id="getSkuBySpuId" resultType="com.vedeng.goods.model.CoreSku">

    SELECT
        A.*
    FROM
        V_CORE_SKU A
    WHERE
        A.SPU_ID = #{spuId,jdbcType=INTEGER}
        AND A.`STATUS` = 1
    </select>
    <select id="getSkuByFirstEnageId" resultType="com.vedeng.goods.model.CoreSku">
SELECT
    C.*
FROM
	T_FIRST_ENGAGE A
	LEFT JOIN V_CORE_SPU B ON B.FIRST_ENGAGE_ID = A.FIRST_ENGAGE_ID
	LEFT JOIN V_CORE_SKU C ON C.SPU_ID = B.SPU_ID
WHERE
	A.FIRST_ENGAGE_ID = #{firstEnageId,jdbcType=INTEGER} AND C.`STATUS`=1 AND B.`STATUS`=1
    </select>

  <select id="batchQueryProductManageAndAsist" resultType="com.vedeng.order.model.dto.ProductManageAndAsistDto">
        select
            K.SKU_NO,
            U.USER_ID as productAssitUserId,
            U.USERNAME as productAssitName,
            M.USER_ID AS productManageUserId,
            M.USERNAME AS productManageName
        from V_CORE_SKU K
        LEFT JOIN V_CORE_SPU P ON K.SPU_ID=P.SPU_ID
        LEFT JOIN T_USER U ON U.USER_ID=P.ASSIGNMENT_ASSISTANT_ID
        LEFT JOIN T_USER M ON M.USER_ID=P.ASSIGNMENT_MANAGER_ID
        where K.SKU_NO  in
        <foreach collection="list" item="sku" open="(" close=")" separator=",">
            #{sku,jdbcType=VARCHAR}
        </foreach>
    </select>

  <select id="queryProductManageAndAsist" resultType="com.vedeng.order.model.dto.ProductManageAndAsistDto">
    select
      K.SKU_NO,
      U.USER_ID as productAssitUserId,
      U.USERNAME as productAssitName,
      M.USER_ID AS productManageUserId,
      M.USERNAME AS productManageName
    from V_CORE_SKU K
    LEFT JOIN V_CORE_SPU P ON K.SPU_ID=P.SPU_ID
    LEFT JOIN T_USER U ON U.USER_ID=P.ASSIGNMENT_ASSISTANT_ID
    LEFT JOIN T_USER M ON M.USER_ID=P.ASSIGNMENT_MANAGER_ID
    where K.SKU_NO = #{sku,jdbcType=VARCHAR}
  </select>


  <select id="batchFindBySkuNos" parameterType="java.util.List" resultType="com.vedeng.goods.model.dto.CoreSkuDto">
    SELECT
        K.SKU_NO,
        K.IS_NEED_REPORT needReport,
        P.SPU_TYPE,
        K.SHOW_NAME skuName
    FROM V_CORE_SKU K
    LEFT JOIN V_CORE_SPU P ON K.SPU_ID=P.SPU_ID
    WHERE K.SKU_NO IN
    <foreach collection="list" item="skuNo" separator="," open="(" close=")">
      #{skuNo}
    </foreach>
  </select>
  <select id="getSkuList" resultType="com.vedeng.goods.model.CoreSku">
    select
        *
    from
        V_CORE_SKU
  </select>
  <select id="getSkuListOrderBySaleSumDesc" resultType="com.vedeng.flash.dto.temp.CalculatedSaleSumTemp">
    select temp.skuId,  ROUND(sum(temp.salesum), 2) as saleSum from (
    SELECT
    T1.SKU_ID as skuId, ifnull(round(T3.PRICE * T3.NUM, 2), 0) as salesum
    FROM V_CORE_SKU T1
    join V_CORE_SPU T2 on T1.SPU_ID = T2.SPU_ID
    join T_SALEORDER_GOODS T3 on T1.SKU_NO = T3.SKU
    join T_SALEORDER T4 on T3.SALEORDER_ID = T4.SALEORDER_ID
    where T3.IS_DELETE = 0
    and T4.STATUS = 2
    and T4.IS_DELETE = 0
    and T4.ORDER_TYPE != 2
    and not exists(select 1 from T_AFTER_SALES where ORDER_ID = T4.SALEORDER_ID and TYPE = 539)
    <if test="goodType != null and goodType.sysOptionDefinitionId != null">
      and T2.SPU_TYPE = #{goodType.sysOptionDefinitionId,jdbcType=INTEGER}
    </if>
    and T1.SKU_ID not in (SELECT COMMENTS FROM T_SYS_OPTION_DEFINITION WHERE PARENT_ID = 693)
    <if test="timeFrame != null and timeFrame != 12">
      and T4.VALID_TIME >= UNIX_TIMESTAMP(date_add(DATE_ADD(curdate(), interval -day(curdate()) + 1 day), interval - #{timeFrame,jdbcType=INTEGER} month)) *1000
      and T4.VALID_TIME &lt;= UNIX_TIMESTAMP(DATE_ADD(curdate(), interval -day(curdate()) + 1 day)) * 1000
    </if>
    <if test="timeFrame != null and timeFrame == 12">
      and T4.VALID_TIME >= UNIX_TIMESTAMP(date_add( date_add(date_add(curdate(), interval -day(curdate()) + 1 day), interval - month(curdate()) + 1 month),interval - 1 year)) * 1000
      and T4.VALID_TIME &lt;= UNIX_TIMESTAMP(date_add(date_add(curdate(), interval -day(curdate()) + 1 day), interval - month(curdate()) + 1 month)) * 1000
    </if>

    union all

    SELECT T1.SKU_ID as skuId,
    case T5.ATFER_SALES_STATUS
    when 2 then T3.NUM - T6.NUM
    when 3 then T3.NUM
    else 0 end * T3.PRICE as salesum
    FROM V_CORE_SKU T1
    join V_CORE_SPU T2 on T1.SPU_ID = T2.SPU_ID
    join T_SALEORDER_GOODS T3 on T1.SKU_NO = T3.SKU
    join T_SALEORDER T4 on T3.SALEORDER_ID = T4.SALEORDER_ID
    left join T_AFTER_SALES T5 ON T4.SALEORDER_NO = T5.ORDER_NO
    left join T_AFTER_SALES_GOODS T6 on T5.AFTER_SALES_ID = T6.AFTER_SALES_ID
    where T3.IS_DELETE = 0
    and T4.STATUS = 2
    and T4.IS_DELETE = 0
    and T4.ORDER_TYPE != 2
    and T5.ATFER_SALES_STATUS in (2, 3)
    and T5.TYPE = 539
    and T6.GOODS_ID = T1.SKU_ID
    <if test="goodType != null and goodType.sysOptionDefinitionId != null">
      and T2.SPU_TYPE = #{goodType.sysOptionDefinitionId,jdbcType=INTEGER}
    </if>
    and T1.SKU_ID not in (SELECT COMMENTS FROM T_SYS_OPTION_DEFINITION WHERE PARENT_ID = 693)
    <if test="timeFrame != null and timeFrame != 12">
      and T4.VALID_TIME >= UNIX_TIMESTAMP(date_add(DATE_ADD(curdate(), interval -day(curdate()) + 1 day), interval - #{timeFrame,jdbcType=INTEGER} month)) *1000
      and T4.VALID_TIME &lt;= UNIX_TIMESTAMP(DATE_ADD(curdate(), interval -day(curdate()) + 1 day)) * 1000
    </if>
    <if test="timeFrame != null and timeFrame == 12">
      and T4.VALID_TIME >= UNIX_TIMESTAMP(date_add( date_add(date_add(curdate(), interval -day(curdate()) + 1 day), interval - month(curdate()) + 1 month),interval - 1 year)) * 1000
      and T4.VALID_TIME &lt;= UNIX_TIMESTAMP(date_add(date_add(curdate(), interval -day(curdate()) + 1 day), interval - month(curdate()) + 1 month)) * 1000
    </if>
    ) temp group by temp.skuId order by sum(temp.salesum) desc
  </select>
  <select id="getSkuSum80Percent" resultType="java.math.BigDecimal">

    select ROUND(sum(tp.allsum)*0.8,2) from (
    select temp.skuId, ROUND(sum(temp.salesum),2) as allsum from (
         SELECT
            T1.SKU_ID as skuId, ROUND(ifnull(T3.PRICE * T3.NUM, 0), 2) as salesum
         FROM V_CORE_SKU T1
             join V_CORE_SPU T2 on T1.SPU_ID = T2.SPU_ID
             join T_SALEORDER_GOODS T3 on T1.SKU_NO = T3.SKU
             join T_SALEORDER T4 on T3.SALEORDER_ID = T4.SALEORDER_ID
         where T3.IS_DELETE = 0
             and T4.STATUS = 2
             and T4.IS_DELETE = 0
             and T4.ORDER_TYPE != 2
             and not exists(select 1 from T_AFTER_SALES where ORDER_ID = T4.SALEORDER_ID and TYPE = 539)
             <if test="goodType != null and goodType.sysOptionDefinitionId != null">
               and T2.SPU_TYPE = #{goodType.sysOptionDefinitionId,jdbcType=INTEGER}
             </if>
               and T1.SKU_ID not in (SELECT COMMENTS FROM T_SYS_OPTION_DEFINITION WHERE PARENT_ID = 693)
             <if test="timeFrame != null and timeFrame != 12">
               and T4.VALID_TIME >= UNIX_TIMESTAMP(date_add(DATE_ADD(curdate(), interval -day(curdate()) + 1 day), interval - #{timeFrame,jdbcType=INTEGER} month)) *1000
               and T4.VALID_TIME &lt;= UNIX_TIMESTAMP(DATE_ADD(curdate(), interval -day(curdate()) + 1 day)) * 1000
             </if>
             <if test="timeFrame != null and timeFrame == 12">
               and T4.VALID_TIME >= UNIX_TIMESTAMP(date_add( date_add(date_add(curdate(), interval -day(curdate()) + 1 day), interval - month(curdate()) + 1 month),interval - 1 year)) * 1000
               and T4.VALID_TIME &lt;= UNIX_TIMESTAMP(date_add(date_add(curdate(), interval -day(curdate()) + 1 day), interval - month(curdate()) + 1 month)) * 1000
             </if>

         union all

         SELECT T1.SKU_ID as skuId,
             case T5.ATFER_SALES_STATUS
             when 2 then T3.NUM - T6.NUM
             when 3 then T3.NUM
             else 0 end * T3.PRICE as salesum
         FROM V_CORE_SKU T1
             join V_CORE_SPU T2 on T1.SPU_ID = T2.SPU_ID
             join T_SALEORDER_GOODS T3 on T1.SKU_NO = T3.SKU
             join T_SALEORDER T4 on T3.SALEORDER_ID = T4.SALEORDER_ID
             left join T_AFTER_SALES T5 ON T4.SALEORDER_NO = T5.ORDER_NO
             left join T_AFTER_SALES_GOODS T6 on T5.AFTER_SALES_ID = T6.AFTER_SALES_ID
         where T3.IS_DELETE = 0
             and T4.STATUS = 2
             and T4.IS_DELETE = 0
             and T4.ORDER_TYPE != 2
             and T5.ATFER_SALES_STATUS in (2, 3)
             and T5.TYPE = 539
             and T6.GOODS_ID = T1.SKU_ID
             <if test="goodType != null and goodType.sysOptionDefinitionId != null">
               and T2.SPU_TYPE = #{goodType.sysOptionDefinitionId,jdbcType=INTEGER}
             </if>
             and T1.SKU_ID not in (SELECT COMMENTS FROM T_SYS_OPTION_DEFINITION WHERE PARENT_ID = 693)
             <if test="timeFrame != null and timeFrame != 12">
               and T4.VALID_TIME >= UNIX_TIMESTAMP(date_add(DATE_ADD(curdate(), interval -day(curdate()) + 1 day), interval - #{timeFrame,jdbcType=INTEGER} month)) *1000
               and T4.VALID_TIME &lt;= UNIX_TIMESTAMP(DATE_ADD(curdate(), interval -day(curdate()) + 1 day)) * 1000
             </if>
             <if test="timeFrame != null and timeFrame == 12">
               and T4.VALID_TIME >= UNIX_TIMESTAMP(date_add( date_add(date_add(curdate(), interval -day(curdate()) + 1 day), interval - month(curdate()) + 1 month),interval - 1 year)) * 1000
               and T4.VALID_TIME &lt;= UNIX_TIMESTAMP(date_add(date_add(curdate(), interval -day(curdate()) + 1 day), interval - month(curdate()) + 1 month)) * 1000
             </if>
        ) temp group by temp.skuId
    ) tp
  </select>
  <select id="selectBySkuNo" resultType="com.vedeng.goods.model.CoreSku">
    select
        *
    from
        V_CORE_SKU
    where SKU_NO = #{skuNo,jdbcType=VARCHAR}
    limit 1
  </select>
    <select id="getSkuinfoById" resultType="com.vedeng.goods.model.CoreSku">
      select *
      from V_CORE_SKU
      where SKU_ID = #{logBizId,jdbcType=INTEGER}
      limit 1
    </select>
  <select id="selectBySkuNoList" resultType="com.vedeng.flash.dto.temp.JoinOrderingPoolOperateTemp">
    select
        SKU_ID,
        SKU_NO
    from
        V_CORE_SKU
    where SKU_NO in #{joinOrderingPoolOperateTemps.skuNo,jdbcType=VARCHAR}
  </select>
  <select id="selectSkuBySkuNoList" resultType="com.vedeng.goods.model.CoreSku">
    select
        *
    from
        V_CORE_SKU
    where SKU_NO in <foreach collection="skuNoList" item="item" separator="," open="(" close=")">#{item,jdbcType=VARCHAR}</foreach>
  </select>
    <select id="selectNotDeleteBySkuNoList" resultType="java.lang.String">
        select
            b.SKU_NO
        from
            T_REGULAR_PREPARE_SKU a
            left join V_CORE_SKU b on a.SKU_ID = b.SKU_ID
        where
            a.SKU_STATUS = 0
            and
            b.SKU_NO in
            <foreach collection="skuNoList" item="item" separator="," open="(" close=")">#{item,jdbcType=VARCHAR}</foreach>
    </select>
  <select id="listSku" resultType="com.vedeng.goods.model.CoreSku">
    select * from V_CORE_SKU
  </select>

  <select id="getSkuInfoById" resultType="com.vedeng.order.model.vo.SkuDto">
    select
        a.SKU_ID as skuId,
        b.CATEGORY_ID as categoryId,
        b.BRAND_ID as brandId,
        a.SPU_ID as spuId,
        a.SKU_NAME as skuName
    from
        V_CORE_SKU a
        left join V_CORE_SPU b on a.SPU_ID = b.SPU_ID
    where
        a.SKU_ID = #{skuId,jdbcType=INTEGER}
    limit 1
  </select>
    <select id="getGoodsIdBySkuNo" resultType="java.lang.Integer">
      SELECT
	      DISTINCT GG.GOODS_ID
      FROM
          V_CORE_SKU SKU
          LEFT JOIN T_GOODS GG ON SKU.SKU_NO = GG.SKU
      WHERE
          SKU.SKU_NO = #{skuNo,jdbcType=VARCHAR}

    </select>
  <select id="getValidingSkuInfoListByNo" resultType="com.vedeng.goods.model.CoreSku">
    select *
    from V_CORE_SKU
    WHERE CHECK_STATUS IN (1)
    AND SKU_NO IN
    <foreach collection="skuNos" open="(" close=")" separator="," item="skuNo" >
      #{skuNo,jdbcType = VARCHAR}
    </foreach>

  </select>
    <select id="getSkuNoBySpuId" resultType="java.lang.String">
        SELECT SKU_NO
        FROM V_CORE_SKU
        WHERE SPU_ID=#{spuId,jdbcType =INTEGER}
    </select>

  <select id="queryGoodsNewListPage" parameterType="com.vedeng.goods.model.Goods" resultType="com.vedeng.goods.model.vo.GoodsVo">
    SELECT
    A.SKU_ID AS goodsId,
    A.SKU_NO AS sku,
    A.SHOW_NAME AS goodsName,
    D.BRAND_ID AS brandId,
    D.CATEGORY_ID AS categoryId,
    B.BRAND_NAME AS brandName,
    A.MODEL AS model,
    A.BASE_UNIT_ID AS unitId,
    C.UNIT_NAME AS unitName,
    A.MATERIAL_CODE AS materialCode,
    NUM.MANAGE_CATEGORY_LEVEL AS manageCategory,
    D.SPU_TYPE AS goodsType,
    D.SPU_LEVEL AS goodsLevel,
    D.SPU_ID as spuId,
    IFNULL(LV.LEVEL_NAME, '无')  AS goodsLevelName,
    IFNULL(POS.POSITION_NAME, '无档位') AS goodsPositionName,
    A.CHECK_STATUS verifyStatus,
    A.SOURCE,
    CONCAT(M.USERNAME,' ',U.USERNAME) AS proUserName
    ,xx.status as parentId,
    A.IS_DIRECT,
    A.TAX_CATEGORY_NO
    FROM
    V_CORE_SKU A
    LEFT JOIN V_CORE_SPU D ON A.SPU_ID=D.SPU_ID
    LEFT JOIN T_FIRST_ENGAGE F ON D.FIRST_ENGAGE_ID=F.FIRST_ENGAGE_ID
    LEFT JOIN T_REGISTRATION_NUMBER NUM ON NUM.REGISTRATION_NUMBER_ID=F.REGISTRATION_NUMBER_ID
    LEFT JOIN T_BRAND B ON D.BRAND_ID = B.BRAND_ID
    LEFT JOIN T_UNIT C ON A.BASE_UNIT_ID = C.UNIT_ID
    LEFT JOIN T_USER U ON U.USER_ID=D.ASSIGNMENT_ASSISTANT_ID
    LEFT JOIN T_USER M ON M.USER_ID=D.ASSIGNMENT_MANAGER_ID
    LEFT JOIN T_VERIFIES_INFO xx
    ON A.SKU_ID = xx.RELATE_TABLE_KEY AND xx.RELATE_TABLE = 'T_GOODS'
    LEFT JOIN V_GOODS_LEVEL LV ON A.GOODS_LEVEL_NO = LV.ID
    LEFT JOIN V_GOODS_POSITION POS ON A.GOODS_POSITION_NO = POS.ID
    WHERE A.STATUS = 1 and D.CHECK_STATUS != 4 and A.CHECK_STATUS = 3
    <if test="goods.searchContent!=null and goods.searchContent!=''">
      AND (
      A.SKU_NO LIKE CONCAT('%',#{goods.searchContent,jdbcType=VARCHAR},'%' )
      OR
      A.SHOW_NAME LIKE CONCAT('%',#{goods.searchContent,jdbcType=VARCHAR},'%' )
      OR
      B.BRAND_NAME LIKE CONCAT('%',#{goods.searchContent,jdbcType=VARCHAR},'%' )
      OR
      A.MODEL LIKE CONCAT('%',#{goods.searchContent,jdbcType=VARCHAR},'%' )
      )
    </if>
    <if test="goods.specModel!=null and goods.specModel!=''">
      AND(
      A.SPEC LIKE CONCAT('%',#{goods.specModel,jdbcType=VARCHAR},'%')
      OR
      A.MODEL LIKE CONCAT('%',#{goods.specModel,jdbcType=VARCHAR},'%')
      )
    </if>
    <if test="goods.goodsName!=null and goods.goodsName!=''">
      AND A.SHOW_NAME LIKE CONCAT('%',#{goods.goodsName,jdbcType=VARCHAR},'%' )
    </if>
    <if test="goods.sku!=null and goods.sku!=''">
      AND A.SKU_NO LIKE CONCAT('%',#{goods.sku,jdbcType=VARCHAR},'%' )
    </if>
    <if test="goods.brandName!=null and goods.brandName!=''">
      AND B.BRAND_NAME LIKE CONCAT('%',#{goods.brandName,jdbcType=VARCHAR},'%' )
    </if>
    <if test="goods.unitName!=null and goods.unitName!=''">
      AND C.UNIT_NAME LIKE CONCAT('%',#{goods.unitName,jdbcType=VARCHAR},'%' )
    </if>
    <if test="goods.brandId !=null and goods.brandId > 0">
      AND D.BRAND_ID = #{goods.brandId,jdbcType=INTEGER}
    </if>
    <if test="goods.goodsType != null">
      AND D.SPU_TYPE = #{goods.goodsType, jdbcType=INTEGER}
    </if>
  </select>

  <select id="queryGoodsBySku" parameterType="com.vedeng.goods.model.Goods" resultType="com.vedeng.goods.model.vo.GoodsVo">
    SELECT
    A.SKU_ID AS goodsId,
    A.SKU_NO AS sku,
    A.SHOW_NAME AS goodsName,
    D.BRAND_ID AS brandId,
    D.CATEGORY_ID AS categoryId,
    B.BRAND_NAME AS brandName,
    A.MODEL AS model,
    A.BASE_UNIT_ID AS unitId,
    C.UNIT_NAME AS unitName,
    A.MATERIAL_CODE AS materialCode,
    NUM.MANAGE_CATEGORY_LEVEL AS manageCategory,
    D.SPU_TYPE AS goodsType,
    D.SPU_LEVEL AS goodsLevel,
    D.SPU_ID as spuId,
    IFNULL(LV.LEVEL_NAME, '无')  AS goodsLevelName,
    IFNULL(POS.POSITION_NAME, '无档位') AS goodsPositionName,
    A.CHECK_STATUS verifyStatus,
    A.SOURCE,
    CONCAT(M.USERNAME,' ',U.USERNAME) AS proUserName
    ,xx.status as parentId,
    TSDD.DIRECT_DELIVERY_TIME_START,
    TSDD.DIRECT_DELIVERY_TIME_END,
    TSDD.COMMON_DELIVERY_TIME_START,
    TSDD.COMMON_DELIVERY_TIME_END,
    A.IS_DIRECT
    FROM
    V_CORE_SKU A
    LEFT JOIN V_CORE_SPU D ON A.SPU_ID=D.SPU_ID
    LEFT JOIN T_FIRST_ENGAGE F ON D.FIRST_ENGAGE_ID=F.FIRST_ENGAGE_ID
    LEFT JOIN T_REGISTRATION_NUMBER NUM ON NUM.REGISTRATION_NUMBER_ID=F.REGISTRATION_NUMBER_ID
    LEFT JOIN T_BRAND B ON D.BRAND_ID = B.BRAND_ID
    LEFT JOIN T_UNIT C ON A.BASE_UNIT_ID = C.UNIT_ID
    LEFT JOIN T_USER U ON U.USER_ID=D.ASSIGNMENT_ASSISTANT_ID
    LEFT JOIN T_USER M ON M.USER_ID=D.ASSIGNMENT_MANAGER_ID
    LEFT JOIN T_VERIFIES_INFO xx
    ON A.SKU_ID = xx.RELATE_TABLE_KEY AND xx.RELATE_TABLE = 'T_GOODS'
    LEFT JOIN V_GOODS_LEVEL LV ON A.GOODS_LEVEL_NO = LV.ID
    LEFT JOIN V_GOODS_POSITION POS ON A.GOODS_POSITION_NO = POS.ID
    LEFT JOIN T_SKU_DELIVERY_DATA TSDD ON TSDD.SKU_ID = A.SKU_ID AND TSDD.IS_DELETE = 0
    WHERE A.STATUS = 1 and D.CHECK_STATUS != 4 and A.CHECK_STATUS = 3
    AND A.SKU_NO = #{goods.sku,jdbcType=VARCHAR}
  </select>
    <select id="getSkuIdListExceptSpecial" resultType="java.lang.Integer">
        select SKU_ID from V_CORE_SKU where SKU_ID not in
        <foreach collection="specialSkuIdList" item="item" index="index" open="(" close=")" separator=",">
          #{item}
        </foreach>
        <if test="skuId != null and skuId > 0">
          and SKU_ID = #{skuId}
        </if>
        and SKU_ID > #{startSkuId} limit #{limit}
    </select>

  <select id="getAllVirtureSkuList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from V_CORE_SKU
    WHERE STATUS = 1 AND IS_VIRTURE_SKU = 1
  </select>

  <select id="getFeeCategory" resultType="java.util.Map">
    SELECT
    vcs.HAVE_STOCK_MANAGE haveStockManage, tscc.CATEGORY_NAME expenseCategoryName
    FROM
    V_CORE_SKU vcs
    LEFT JOIN
    T_SYS_COST_CATEGORY tscc ON vcs.COST_CATEGORY_ID = tscc.COST_CATEGORY_ID
    WHERE
    1=1
    <if test="skuNo != null and skuNo != ''">
      and vcs.SKU_NO = #{skuNo}
    </if>
  </select>
  <select id="getInvisibleSkuList" resultType="com.vedeng.goods.model.CoreSku">
    select cs.SKU_ID,cs.SKU_NO,cs.SHOW_NAME,cs.COST_CATEGORY_ID
    FROM V_CORE_SKU cs
           left join T_SYS_COST_CATEGORY scc on cs.COST_CATEGORY_ID = scc.COST_CATEGORY_ID
    where (IS_VIRTURE_SKU = 1 AND cs.STATUS = 1 AND scc.IS_NEED_PURCHASE = 0) or cs.SKU_NO in ('V127063','V251526','V256675','V253620','V251462');
  </select>

  <select id="getTaxCategoryNo" resultType="java.lang.String">
    select TAX_CATEGORY_NO from V_CORE_SKU where SKU_ID = #{goodsId,jdbcType=INTEGER}
    </select>
    <select id="searchSku" resultType="com.vedeng.goods.model.CoreSku">
      select
      <include refid="Base_Column_List" />
      from V_CORE_SKU
      WHERE STATUS = 1
      <if test="keyword != null and keyword != ''">
          and
          (
          SKU_NO LIKE CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
          OR SHOW_NAME LIKE CONCAT('%',#{keyword,jdbcType=VARCHAR},'%')
          )
      </if>
      limit 100
    </select>
</mapper>