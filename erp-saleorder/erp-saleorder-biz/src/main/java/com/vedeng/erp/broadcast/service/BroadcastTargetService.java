package com.vedeng.erp.broadcast.service;

import com.vedeng.erp.broadcast.domain.dto.BroadcastTargetImportResultDto;
import com.vedeng.erp.broadcast.domain.dto.BroadcastTargetQueryDto;
import com.vedeng.erp.broadcast.domain.dto.BroadcastTargetRespDto;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 业绩目标管理服务接口
 */
public interface BroadcastTargetService {

    /**
     * 导入Excel文件
     * 支持覆盖式导入（相同年度+目标类型的数据会被覆盖）
     *
     * @param file Excel文件，包含3个Sheet：个人目标、小组目标、部门目标
     * @return 导入结果
     * @throws IOException IO异常
     */
    BroadcastTargetImportResultDto importExcel(MultipartFile file) throws IOException;

    /**
     * 下载Excel导入模板
     * 模板中自动填充当前年度和部门/小组信息
     *
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    void downloadTemplate(HttpServletResponse response) throws IOException;

    /**
     * 查询业绩目标
     * 查询所有有效的广播目标数据（IS_DELETED=0），根据targetType字段对查询结果进行分组
     *
     * @param queryDto 查询条件
     * @return 按targetType分组的目标数据，格式为 Map<String, List<BroadcastTargetRespDto>>
     *         其中key为目标类型标识（person、group、department），value为对应的目标列表
     */
    Map<String, List<BroadcastTargetRespDto>> queryTargets(BroadcastTargetQueryDto queryDto);

    /**
     * 获取有数据的年度列表
     * 用于筛选条件
     *
     * @return 年度列表
     */
    List<Integer> getAvailableYears();
}
