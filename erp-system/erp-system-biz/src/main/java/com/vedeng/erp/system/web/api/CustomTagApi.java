package com.vedeng.erp.system.web.api;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.utils.validator.ValidatorUtils;
import com.vedeng.common.core.utils.validator.group.AddGroup;
import com.vedeng.common.core.utils.validator.group.UpdateGroup;
import com.vedeng.erp.system.dto.CustomTagDto;
import com.vedeng.erp.system.service.CustomTagService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 标签业务api
 * @date 2022/7/12 11:36
 */
@ExceptionController
@RestController
@RequestMapping("/customTag")
@Slf4j
public class CustomTagApi {

    @Autowired
    private CustomTagService customTagService;

    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public R<?> add(@RequestBody CustomTagDto customTagDto) {
        ValidatorUtils.validate(customTagDto, AddGroup.class);
        customTagService.add(customTagDto);
        return R.success();
    }

    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public R<?> update(@RequestBody CustomTagDto customTagDto) {
        ValidatorUtils.validate(customTagDto, UpdateGroup.class);
        customTagService.update(customTagDto);
        return R.success();
    }


    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public R<?> delete(@RequestParam Integer id) {
        customTagService.delete(CollUtil.newHashSet(id));
        return R.success();
    }

    @NoNeedAccessAuthorization
    @RequestMapping(value = "/getUserTags", method = RequestMethod.POST)
    public R<List<CustomTagDto>> getUserTags(Integer type) {
        return R.success(customTagService.getUserTags(type));
    }

    @NoNeedAccessAuthorization
    @RequestMapping(value = "/getUserTagsToMap", method = RequestMethod.POST)
    public R<Map<String, List<CustomTagDto>>> getUserTagsToMap(Integer type) {
        return R.success(customTagService.getUserTagsToMap(type));
    }

}
