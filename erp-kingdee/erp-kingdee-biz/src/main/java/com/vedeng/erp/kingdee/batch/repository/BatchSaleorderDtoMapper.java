package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchSaleorderDto;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2023/2/2
 * @apiNote
 */
public interface BatchSaleorderDtoMapper {
    int deleteByPrimaryKey(Integer saleorderId);

    int insert(BatchSaleorderDto record);

    int insertOrUpdate(BatchSaleorderDto record);

    int insertOrUpdateSelective(BatchSaleorderDto record);

    int insertSelective(BatchSaleorderDto record);

    BatchSaleorderDto selectByPrimaryKey(Integer saleorderId);

    int updateByPrimaryKeySelective(BatchSaleorderDto record);

    int updateByPrimaryKey(BatchSaleorderDto record);

    int updateBatch(List<BatchSaleorderDto> list);

    int updateBatchSelective(List<BatchSaleorderDto> list);

    int batchInsert(@Param("list") List<BatchSaleorderDto> list);

    BatchSaleorderDto selectBySaleorderNo(@Param("saleorderNo") String saleorderNo);

    String getTaxRateByInvoiceType(Integer InvoiceType);

    /**
     * 根据销售单id查询对应的销售单号
     *
     * @param salesOrderId 销售单id
     * @return 销售单号
     */
    String getSaleOrderNoById(@Param("salesOrderId") Integer salesOrderId);
}