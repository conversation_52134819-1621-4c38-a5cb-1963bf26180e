package com.vedeng.activiti.taskassign;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.xml.ws.WebServiceContext;

import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.wms.dao.WmsLogicalOrdergoodsMapper;
import com.wms.model.po.WmsLogicalOrdergoods;
import com.wms.service.LogicalAfterorderChooseService;
import com.wms.service.listenner.PutAfterReturnAuditFinishLister;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.DateUtil;
import com.vedeng.finance.model.TraderAccountPeriodApply;

public class RejectOverAfterSalesVerifyExecutionListener implements ExecutionListener {
    // 运行时注入service
    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
    private AfterSalesService afterSalesOrderService = (AfterSalesService) context.getBean("afterSalesOrderService");

	private static Logger logger = LoggerFactory.getLogger(RejectOverAfterSalesVerifyExecutionListener.class);

	private PutAfterReturnAuditFinishLister putAfterReturnAuditFinishLister = (PutAfterReturnAuditFinishLister) context.getBean("putAfterReturnAuditFinishLister");

	private WmsLogicalOrdergoodsMapper wmsLogicalOrdergoodsMapper= (WmsLogicalOrdergoodsMapper) context.getBean("wmsLogicalOrdergoodsMapper");

	private AfterSalesMapper afterSalesMapper = (AfterSalesMapper) context.getBean("afterSalesMapper");

	private LogicalAfterorderChooseService logicalAfterorderChooseService = (LogicalAfterorderChooseService) context.getBean("logicalAfterorderChooseService");
	//修改售后单结束触发器
    //根据穿参通用回写主表中状态
    public void notify(DelegateExecution execution) throws Exception {
		ServletRequestAttributes ra= (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		HttpServletRequest request =  ra.getRequest();
		User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		AfterSalesVo afterSalesInfo = (AfterSalesVo) execution.getVariable("afterSalesVo");
		logger.info("售后完结驳回触发器,信息{},", JSON.toJSONString(afterSalesInfo));
		if(afterSalesInfo != null){
		    AfterSalesVo afterSalesVo = new AfterSalesVo();
		    afterSalesVo.setAfterSalesId(afterSalesInfo.getAfterSalesId());
		    afterSalesVo.setAfterSalesStatusReson(0);
		    afterSalesVo.setAfterSalesStatusUser(0);
		    afterSalesVo.setAfterSalesStatusComments("");
		    afterSalesOrderService.updateAfterSalesById(afterSalesVo);
			AfterSales afterSales = afterSalesMapper.selectByPrimaryKey(afterSalesInfo.getAfterSalesId());
			//驳回重新下发售后单
			if((StockOperateTypeConst.AFTERORDER_BACK_FINSH.equals(afterSalesInfo.getType()))){
				//售后退货下发
				putAfterReturnAuditFinishLister.onActionHappen(afterSalesInfo,true,user);
			}else if(StockOperateTypeConst.AFTERORDER_CHANGE_FINSH.equals(afterSalesInfo.getType())){
				//售后换货下发
				List<WmsLogicalOrdergoods> list = wmsLogicalOrdergoodsMapper.getAfterorderLogicalChooseInfoByNo(afterSales.getAfterSalesNo());
				logicalAfterorderChooseService.putExchangeWMS(afterSalesInfo,list,user);
			}
		}
    }
}
/**
 * 
 * <AUTHOR>
 *
 */
