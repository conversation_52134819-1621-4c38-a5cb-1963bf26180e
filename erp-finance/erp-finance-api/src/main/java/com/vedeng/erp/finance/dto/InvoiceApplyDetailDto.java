package com.vedeng.erp.finance.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 开票申请明细表
 */
@Data
public class InvoiceApplyDetailDto implements Serializable {
    /**
     * 主键
     */
    private Integer invoiceApplyDetailId;

    /**
     * 申请表关联主键
     */
    private Integer invoiceApplyId;

    /**
     * 订单商品ID
     */
    private Integer detailgoodsId;

    /**
     * 开票单价
     */
    private BigDecimal price;

    /**
     * 开票数量
     */
    private BigDecimal num;

    /**
     * 开票总额
     */
    private BigDecimal totalAmount;

    /**
     * 修改后的商品名称
     */
    private String changedGoodsName;

    /**
     * 税收类别编号
     */
    private String taxCategoryNo;

    /**
     * 税收类别名简称
     */
    private String taxCategorySimpleName;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * sku
     */
    private String skuNo;

    /**
     * 型号
     */
    private String model;

    /**
     * 规格
     */
    private String spec;

    /**
     * 单位
     */
    private String unitName;

    /** 产品名称 */
    private String productName;
    /**
     * 初始产品名称
     */
    private String oldProductName;

    /** 规格型号 */
    private String specModel;
    /**
     * 初始规格型号
     */
    private String oldSpec;

    /** 单位 */
    private String unit;

    /** 税率 */
    private BigDecimal taxRate;

    /** 税额 */
    private BigDecimal taxAmount;

    /** 不含税单价 */
    private BigDecimal taxExclusivePrice;

    /** 不含税金额 */
    private BigDecimal taxExclusiveAmount;

    /**
     * 已申请数量
     */
    private BigDecimal appliedNum;

    /**
     * 已开票数量
     */
    private BigDecimal invoicedNum;

    /**
     * T+N未开票数量
     */
    private BigDecimal TNNum;

    /**
     * 满足T+N日期
     */
    private String TNDateStr;

    private String taxRateStr;


    private static final long serialVersionUID = 1L;
}