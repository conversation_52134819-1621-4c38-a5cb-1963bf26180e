package com.newtask;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.finance.dto.HxInvoiceRequest;
import com.vedeng.finance.service.HxInvoiceService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 下载销项发票图片和进项发票
 * <AUTHOR>
 * @date 2020/08/11
 **/
@JobHandler("hxInvoiceDownload")
@Component
public class HxInvoiceDownloadTask extends AbstractJobHandler {

    @Autowired
    private HxInvoiceService hxInvoiceService;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        HxInvoiceRequest request = new HxInvoiceRequest();
        if (StringUtils.isNotBlank(s)){
            request = JsonUtils.readValue(s, HxInvoiceRequest.class);
            getHxInvoiceList(request);

        } else {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate now = LocalDate.now();
            for (int i = 1; i <= 2; i++) {
                LocalDate startDate = now.plusMonths(-i);
                LocalDate endDate = now.plusMonths(-i+1);
                //定时任务从2020-11-01开始
                if (endDate.isBefore(LocalDate.of(2020,11,1))){
                    break;
                }
                //定时任务从2020-11-01开始
                if (startDate.isBefore(LocalDate.of(2020,11,1))){
                    startDate = LocalDate.of(2020,11,1);
                }
                request.setQsrq(startDate.format(formatter));
                request.setJzrq(endDate.format(formatter));
                getHxInvoiceList(request);
            }
        }
        return ReturnT.SUCCESS;
    }


    private void getHxInvoiceList(HxInvoiceRequest request){
        try {
            hxInvoiceService.getHxIncomeInvoiceList(request.getQsrq(),request.getJzrq());
        } catch (Exception e){
            XxlJobLogger.log("定时任务运行错误，e:",e);
        }
    }
}
