package com.vedeng.erp.aftersale.dto;

import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @description 销售退货 含虚拟商品采销联动逆向 商品
 * <AUTHOR>
 * @date 2023/1/5 15:49
 **/
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AfterSaleGoods2ExpenseDto {
    private Integer afterSalesGoodsId;

    private Integer afterSalesId;

    /**
     * 销售/采购订单详情ID
     */
    private Integer orderDetailId;

    /**
     * 产品类型0普通产品1特殊产品（手续费）
     */
    private Integer goodsType;

    /**
     * 产品ID
     */
    private Integer goodsId;

    /**
     * 售后数量
     */
    private Integer num;

    private BigDecimal price;

    /**
     * 是否直发 0否 1是
     */
    private Integer deliveryDirect;

    /**
     * 已到货数量
     */
    private Integer arrivalNum;

    /**
     * 收货时间
     */
    private Long arrivalTime;

    /**
     * 确认收货人
     */
    private Integer arrivalUserId;

    /**
     * 收货状态0未收货 1部分收货 2全部收货
     */
    private Integer arrivalStatus;

    /**
     * 已发货数量
     */
    private Integer deliveryNum;

    /**
     * 发货状态0未发货 1部分发货 2全部发货
     */
    private Integer deliveryStatus;

    /**
     * 发货时间
     */
    private Long deliveryTime;

    private BigDecimal skuRefundAmount;

    private BigDecimal skuOldRefundAmount;

    /**
     * 是否为活动商品   0否  1是
     */
    private Integer isActionGoods;

    /**
     * 售后单关联业务更新时间
     */
    private Date updateDataTime;

    /**
     * 厂家批次号/SN码
     */
    private String factoryCode;

    /**
     * 生产日期
     */
    private Long goodCreateTime;

    /**
     * 有效期至
     */
    private Long goodVaildTime;

    /**
     * 实际应退数量
     */
    private Integer rknum;

    /**
     * 退票数量(采购仅退票)
     */
    private BigDecimal afterInvoiceNum;
}