package com.vedeng.logistics.service.impl;

import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.logistics.dao.LendOutMapper;
import com.vedeng.logistics.dao.WarehouseGoodsOperateLogMapper;
import com.vedeng.logistics.dao.WarehouseGoodsOutInMapper;
import com.vedeng.logistics.eums.WarehouseGoodsOutEnum;
import com.vedeng.logistics.eums.WarehouseOutInSourceEnum;
import com.vedeng.logistics.model.vo.WarehouseGoodsOutLogVo;
import com.vedeng.logistics.model.vo.WarehouseGoodsOutVo;
import com.vedeng.logistics.service.WarehouseGoodsOutDetailService;
import com.vedeng.system.service.RegionService;
import com.vedeng.trader.dao.TraderAddressMapper;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.dao.TraderSupplierMapper;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.TraderAddress;
import com.vedeng.trader.model.TraderSupplier;
import com.vedeng.trader.model.vo.TraderSupplierVo;
import com.wms.dao.WmsOutputOrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 出库基础类
 * @author: Zeno.zuo
 * @email: <EMAIL>
 * @date: 2022/11/15 13:14
 **/
@Slf4j
public abstract class BaseWarehouseGoodsOutDetailService implements WarehouseGoodsOutDetailService {

    @Autowired
    protected WarehouseGoodsOutInMapper warehouseGoodsOutInMapper;

    @Autowired
    protected WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

    @Autowired
    protected AfterSalesMapper afterSalesMapper;

    @Autowired
    protected AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Autowired
    protected LendOutMapper lendOutMapper;

    @Autowired
    protected WmsOutputOrderMapper wmsOutputOrderMapper;

    @Autowired
    protected TraderMapper traderMapper;

    @Autowired
    protected TraderSupplierMapper traderSupplierMapper;

    @Autowired
    protected TraderAddressMapper traderAddressMapper;

    @Resource
    protected UserMapper userMapper;

    /**
     * 重定向URL前缀
     */
    protected final String REDIRECT_URL_PREFIX = "redirect:";

    protected final Integer TRADER_ADDRESS_ENABLE = 1;

    /**
     * 通用出库单据详情查询
     * @param outInNo 出库单号
     * @param outInType 入库单号
     * @return WarehouseGoodsOutVo
     */
    protected WarehouseGoodsOutVo commonDetailWarehouseGoodsOut(String outInNo, Integer outInType){
        WarehouseGoodsOutVo warehouseGoodsOutVo =  warehouseGoodsOutInMapper.selectWarehouseGoodsOutDetail(outInType,outInNo);
        if(warehouseGoodsOutVo == null){
            return warehouseGoodsOutVo;
        }
        int totalGoodsNum = 0;
        List<WarehouseGoodsOutLogVo> warehouseGoodsOutLogVoList = warehouseGoodsOutVo.getWarehouseGoodsOutLogList();
        if (CollectionUtils.isNotEmpty(warehouseGoodsOutLogVoList)){
            for (WarehouseGoodsOutLogVo warehouseGoodsOutLogVo : warehouseGoodsOutLogVoList) {
                totalGoodsNum += warehouseGoodsOutLogVo.getNum().intValue();
            }
        }
        warehouseGoodsOutVo.setTotalGoodsNum(totalGoodsNum);
        return warehouseGoodsOutVo;
    }


}
