<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN" "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <context id="context1">
        <property name="autoDelimitKeywords" value="true"/>
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>
        <property name="suppressAllComments" value="true"/>
        <property name="suppressDate" value="true"/>

        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="****************************************************************************************************************************"
                        userId="devuser"
                        password="devuser!@#$qwer" />
<!--        <javaModelGenerator targetPackage="com.vedeng.order.model"  targetProject="src/main/java" />-->
<!--        <sqlMapGenerator targetPackage="/mapping/order" targetProject="src/main/resources" />-->
<!--        <javaClientGenerator targetPackage="com.vedeng.order.dao" targetProject="src/main/java" type="XMLMAPPER" />-->
        <javaModelGenerator targetPackage="com.vedeng.price.model"  targetProject="src/main/java" />
        <sqlMapGenerator targetPackage="/mapping/price" targetProject="src/main/resources" />
        <javaClientGenerator targetPackage="com.vedeng.price.dao" targetProject="src/main/java" type="XMLMAPPER" />

<!--        <table tableName="T_SALEORDER" domainObjectName="SaleorderGenerate">-->
<!--            <generatedKey column="SALEORDER_ID" sqlStatement="MySql" identity="true"/>-->
<!--        <columnOverride column="Send_To_Pc" javaType="java.lang.Integer"/>-->
<!--            <columnOverride column="Order_Type" javaType="java.lang.Integer"/>-->
<!--            <columnOverride column="Company_Id" javaType="java.lang.Integer"/>-->
<!--            <columnOverride column="Source" javaType="java.lang.Integer"/>-->
<!--            <columnOverride column="Status" javaType="java.lang.Integer"/>-->
<!--            <columnOverride column="Invoice_Status" javaType="java.lang.Integer"/>-->
<!--            <columnOverride column="Payment_Status" javaType="java.lang.Integer"/>-->
<!--            <columnOverride column="Delivery_Status" javaType="java.lang.Integer"/>-->
<!--            <columnOverride column="Delivery_Direct" javaType="java.lang.Integer"/>-->

<!--        </table>-->
        <!--<table tableName="T_SALEORDER_GOODS" domainObjectName="SaleorderGoodsGenerate">-->
            <!--<generatedKey column="SALEORDER_GOODS_ID" sqlStatement="MySql" identity="true"/>-->

            <!--<columnOverride column="Is_Delete" javaType="java.lang.Integer"/>-->
            <!--<columnOverride column="Delivery_Direct" javaType="java.lang.Integer"/>-->
        <!--</table>-->

        <table tableName="T_SKU_PRICE_MODIFY_RECORD" domainObjectName="SkuPriceModifyRecord">
            <generatedKey column="T_REGULAR_OPERATE_LOG" sqlStatement="MySql" identity="true"/>

<!--            <columnOverride column="IS_ENABLE" javaType="java.lang.Integer"/>-->
        </table>

    </context>
</generatorConfiguration>
