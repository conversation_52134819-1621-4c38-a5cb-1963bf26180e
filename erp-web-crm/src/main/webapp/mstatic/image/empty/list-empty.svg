<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="图层_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 width="371px" height="200px" viewBox="0 0 371 200" enable-background="new 0 0 371 200" xml:space="preserve">
<g>
	<g>
		<g>
			
				<linearGradient id="路径_13_" gradientUnits="userSpaceOnUse" x1="-118.7646" y1="493.4424" x2="-117.6058" y2="490.5827" gradientTransform="matrix(63.9 0 0 -65.4 7615.1318 32247.7617)">
				<stop  offset="0" style="stop-color:#D2D2D2"/>
				<stop  offset="1" style="stop-color:#D2D2D2;stop-opacity:0"/>
			</linearGradient>
			<path id="路径" fill="url(#路径_13_)" d="M73.3,159.6l-3-1.6l-2.9-2.2l-2.7-2.5l-2.4-2.8l-1.8-3.1l-1.2-3.301l-0.5-2.199
				l0.4-4.601L61,137.4l1.4,1.199l3.4,4.801l3,3.399l1.5,1.3l1.7,0.5l0.5-0.5l0.4-1.1v-2.2l-0.6-4.5L68.1,121.9l-1-9.4l0.1-3.5
				l0.5-2.9l0.9-2.5l1.2-2.3l1.4-1.7l1.5-1.2l1.7-0.9l1.8-0.4l1.8,0.1l1.8,0.6l1.8,1.1l1.7,1.8l1.5,2.1l1.3,2.9l1.1,3.8l1.7,9.1
				l1.5,6.101l1.1,2.8l1.1,1.8l0.9,0.9l0.9,0.3l1-0.2l1-0.7l5.3-10.3l2.2-2.2l1.5-0.699l2.9-0.2l3.3,1.3l1.8,1.5l2.9,3.6l2.9,5.9
				l2.4,7.6l0.8,4l0.3,3.9l-0.4,5.1l-1.6,4.2l-1.3,1.8l-1.7,1.5l-4.5,2.301l-7.3,2.1l-5,0.9l-10.2,0.6l-5.1-0.2l-4.6-0.6l-4.3-0.9
				L73.3,159.6z"/>
			
				<linearGradient id="路径_14_" gradientUnits="userSpaceOnUse" x1="-116.9473" y1="492.6797" x2="-117.5922" y2="491.0152" gradientTransform="matrix(59.2 0 0 -64.9 7263.3291 32037.0117)">
				<stop  offset="0" style="stop-color:#D2D2D2"/>
				<stop  offset="1" style="stop-color:#D2D2D2;stop-opacity:0"/>
			</linearGradient>
			<path id="路径_1_" fill="url(#路径_14_)" d="M273.2,180.4L272.1,177.3l-0.8-3.6L271,170.1l0.2-3.699l0.399-2.4l1.601-4.4
				l2.7-3.699l1.8-1.4l0.7,0.6l0.8,1.601l-0.7,6.8l-0.1,4.5l0.3,2l0.399,1.1l0.5,0.4h0.7l1-0.6l1.5-1.601l2.601-3.8l9.3-16.4l2.6-4
				l2.9-3.699l2.399-2.5l2.4-1.801l2.2-1.3l2.399-0.899l2.2-0.301h2l1.9,0.601l1.5,0.899l1.3,1.301l1,1.6l0.5,2l0.1,2.6l-0.3,2.5
				l-1,3l-1.7,3.601l-6.399,10.7l-2.4,5.5l-0.5,2.1l0.101,1.3l0.5,0.8l0.8,0.601l1.2,0.1l10.899-4.2l3.2-0.1l1.5,0.5l1.3,0.7
				l1.8,2.2l0.7,2.1l0.4,4.6l-0.3,2.301l-1,3.399l-1.601,3.3l-2.1,3.2l-2.601,2.8l-4.3,2.9l-4.899,1.7l-2.7,0.3l-2.9-0.1l-9.7-2.2
				l-5.199-1.8l-5.101-2.2l-6.2-3.601l-5.1-4.3l-1.9-2.3L273.2,180.4z"/>
			
				<linearGradient id="路径_15_" gradientUnits="userSpaceOnUse" x1="-119.1978" y1="492.5371" x2="-119.1978" y2="489.3255" gradientTransform="matrix(370.5 0 0 -51.9 44348.25 25595.3867)">
				<stop  offset="0" style="stop-color:#D2D2D2"/>
				<stop  offset="1" style="stop-color:#D2D2D2;stop-opacity:0"/>
			</linearGradient>
			<path id="路径_2_" fill="url(#路径_15_)" d="M370.5,199.4l-8.8-5.801l-9.3-5.5l-9.9-5.3l-10.3-5l-10.5-4.6l-10.8-4.2
				l-11.4-3.9l-11.7-3.6l-11.8-3.2l-12.2-2.8l-12.5-2.4l-12.899-2l-12.801-1.5L212.4,148.4l-13.4-0.7l-13.7-0.2l-13.8,0.2l-13.4,0.7
				L145,149.6l-12.9,1.5l-12.9,2l-12.5,2.4l-12.2,2.8l-11.8,3.2L71,165.1L59.7,169l-10.9,4.2l-10.5,4.6l-10.3,5l-9.8,5.3l-9.4,5.5
				L0,199.4H370.5z"/>
			
				<linearGradient id="形状结合_1_" gradientUnits="userSpaceOnUse" x1="-115.8896" y1="491.7314" x2="-116.5022" y2="489.7063" gradientTransform="matrix(35.6 0 0 -55.7 4419.7427 27437.6934)">
				<stop  offset="0" style="stop-color:#D2D2D2"/>
				<stop  offset="1" style="stop-color:#D2D2D2;stop-opacity:0"/>
			</linearGradient>
			<path id="形状结合" fill="url(#形状结合_1_)" d="M293.5,103.1L291,107.2l-0.8,2.3l-0.4,2.4v2.399l0.601,2.4l0.8,1.399
				l3,3.101l0.6,1.3l0.2,2l-0.4,2.2l-1.8,4.1l-4.8,6.8l-2.8,3.101l1.6,1.7l1.101,2l0.6,2.1v2.4l-1.4,4.399l-1.3,1.9l-2.6,2.8
				l-1.3,0.8H279l-4.5-1l-4.4-1.5l-4.3-1.8l-3.8-2.3l-1.4-1.5l-1-1.8L259.4,146.3l1.3-1.8l3-1.8l1.1-1.101l0.7-1.699l1.1-5.7
				l0.601-6.4l1.2-3.2l0.899-0.899l1.3-0.7l5.2-1l3.101-1.8l2.399-3.7l2.8-6.1l1.7-2.601l1.101-1L293.5,103.1z M282.8,123.8
				l-6.5,9.4l-3.8,7.6l-1.4,4.2l-0.699,2.9l-0.5,5.699h4.3l1.7-10.199l1.1-4.5l3.4-10.2L282.8,123.8z"/>
			
				<linearGradient id="路径_16_" gradientUnits="userSpaceOnUse" x1="-116.0952" y1="484.6836" x2="-116.0952" y2="481.3824" gradientTransform="matrix(32.8 0 0 -22.7 3901.2322 10962.2871)">
				<stop  offset="0" style="stop-color:#D2D2D2"/>
				<stop  offset="1" style="stop-color:#D2D2D2;stop-opacity:0"/>
			</linearGradient>
			<path id="路径_3_" fill="url(#路径_16_)" d="M104.1,22l-0.2-2.3l-0.8-2.2l-1.2-1.8l-1.6-1.6l-2-1.1l-2.2-0.7l-2.4-0.1
				l-2.2,0.4l-2.1,0.9l-1.7,1.3l-1.5,1.8l-1,2.1L83,19l-2,0.8L79.3,21L78,22.8l-0.8,2L76.9,27l0.3,2.1l0.9,2l1.4,1.7l1.7,1.2
				l2.1,0.8l20,0.1l3.2-0.8l2.2-2.2l0.7-1.4l0.3-1.7l-0.1-1.6l-0.5-1.5l-2-2.5L104.1,22z"/>
			
				<linearGradient id="路径_17_" gradientUnits="userSpaceOnUse" x1="-116.75" y1="486.9082" x2="-116.75" y2="484.2162" gradientTransform="matrix(40.6 0 0 -28.4 4904.8364 13780.1377)">
				<stop  offset="0" style="stop-color:#D2D2D2"/>
				<stop  offset="1" style="stop-color:#D2D2D2;stop-opacity:0"/>
			</linearGradient>
			<path id="路径_4_" fill="url(#路径_17_)" d="M178.1,12.2l-0.6-3.8l-0.8-1.8l-1.5-2.3l-2-1.9l-1.6-1L168,0.2L166,0l-3.7,0.5
				l-2.5,1.2l-2.3,1.6l-1.2,1.4l-1.8,3.4l-3.6,0.7l-3,1.8l-2.1,2.7l-1.2,3.2l-0.1,1.9l0.7,3.5l1.8,3l2.6,2.2l3.3,1.2l1.9,0.1H177
				l2.1-0.3l1.8-0.7l1.7-1.2l1.2-1.5l0.9-1.8l0.4-2.1l-0.1-2.1l-0.7-1.9l-1.1-1.7l-1.4-1.3l-1.8-1L178.1,12.2z"/>
		</g>
		
			<linearGradient id="矩形_3_" gradientUnits="userSpaceOnUse" x1="-118.3569" y1="484.8838" x2="-118.3022" y2="484.8279" gradientTransform="matrix(106 0 0 -29 12669.3125 14219.0469)">
			<stop  offset="0" style="stop-color:#858585;stop-opacity:0"/>
			<stop  offset="1" style="stop-color:#616161;stop-opacity:0.5"/>
		</linearGradient>
		<path id="矩形" fill="url(#矩形_3_)" d="M61,141h52.8l53.2,29l-52.2-1.3L61,141z"/>
		
			<linearGradient id="路径_18_" gradientUnits="userSpaceOnUse" x1="-118.6143" y1="486.3281" x2="-117.8904" y2="486.2553" gradientTransform="matrix(75.5 0 0 -33.1 9121.9375 16189.0811)">
			<stop  offset="0" style="stop-color:#F2F2F2"/>
			<stop  offset="1" style="stop-color:#E9E9E9"/>
		</linearGradient>
		<path id="路径_5_" fill="url(#路径_18_)" d="M174.1,75.4l-0.5,33.1l75.5-22.9L174.1,75.4z"/>
		
			<linearGradient id="路径_19_" gradientUnits="userSpaceOnUse" x1="-118.1221" y1="480.1055" x2="-117.2135" y2="479.9861" gradientTransform="matrix(60.2 0 0 -20 7226.2368 9685.1406)">
			<stop  offset="0" style="stop-color:#CAC9C6"/>
			<stop  offset="1" style="stop-color:#DEDEDE"/>
		</linearGradient>
		<path id="路径_6_" fill="url(#路径_19_)" d="M113.9,88.7l60.2-13.1v20L113.9,88.7z"/>
		
			<linearGradient id="路径_20_" gradientUnits="userSpaceOnUse" x1="-117.5459" y1="492.0693" x2="-118.1857" y2="493.2038" gradientTransform="matrix(71.1 0 0 -97 8534.2129 47928.75)">
			<stop  offset="0" style="stop-color:#C5C5C5"/>
			<stop  offset="1" style="stop-color:#FFFFFF"/>
		</linearGradient>
		<path id="路径_7_" fill="url(#路径_20_)" d="M113.9,88.7l71.1,7.1v89.9l-71.1-17.2V88.7z"/>
		
			<linearGradient id="矩形_4_" gradientUnits="userSpaceOnUse" x1="-118.3032" y1="489.3271" x2="-117.7537" y2="489.8241" gradientTransform="matrix(71.1 0 0 -50.2 8534.2129 24692.5371)">
			<stop  offset="0" style="stop-color:#E3E3E3"/>
			<stop  offset="1" style="stop-color:#D3D3D3"/>
		</linearGradient>
		<path id="矩形_1_" fill="url(#矩形_4_)" d="M113.9,88.7l71.1,6.8l-7.7,43.4l-63.4-13.2V88.7z"/>
		<g>
			
				<linearGradient id="路径_21_" gradientUnits="userSpaceOnUse" x1="-117.8828" y1="493.0488" x2="-117.27" y2="491.8009" gradientTransform="matrix(64.1 0 0 -100.1 7765.3364 49464.8008)">
				<stop  offset="0" style="stop-color:#E5E5E5"/>
				<stop  offset="1" style="stop-color:#E8E8E8"/>
			</linearGradient>
			<path id="路径_8_" fill="url(#路径_21_)" fill-opacity="0.99" d="M185,95.8l64.1-10.2v80.5L185,185.7V95.8z"/>
		</g>
		
			<linearGradient id="矩形_5_" gradientUnits="userSpaceOnUse" x1="-117.6772" y1="489.6768" x2="-117.8596" y2="490.2396" gradientTransform="matrix(64.1 0 0 -52.2 7765.3364 25683.5371)">
			<stop  offset="0" style="stop-color:#DDDDDD"/>
			<stop  offset="1" style="stop-color:#D3D3D3"/>
		</linearGradient>
		<path id="矩形_2_" fill="url(#矩形_5_)" d="M185,96l58.1-9.3l6,38l-55.5,14.2L185,96z"/>
		
			<linearGradient id="路径_22_" gradientUnits="userSpaceOnUse" x1="-117.9932" y1="488.3701" x2="-118.7866" y2="488.9795" gradientTransform="matrix(88.2 0 0 -42.6 10569.1133 20919.1445)">
			<stop  offset="0" style="stop-color:#EEEEEE"/>
			<stop  offset="1" style="stop-color:#DADBDA"/>
		</linearGradient>
		<path id="路径_9_" fill="url(#路径_22_)" d="M113.9,88.7l-17.1,29l71.7,13.6L185,95.8L113.9,88.7z"/>
		<g>
			
				<linearGradient id="路径_23_" gradientUnits="userSpaceOnUse" x1="-118.46" y1="489.1338" x2="-117.4586" y2="488.602" gradientTransform="matrix(84.8 0 0 -45.7 10249.3271 22455.1934)">
				<stop  offset="0" style="stop-color:#FAFAFA"/>
				<stop  offset="1" style="stop-color:#EBEBEB"/>
			</linearGradient>
			<path id="路径_10_" fill="url(#路径_23_)" fill-opacity="0.99" d="M185,95.8l20.1,35.5l64.7-16.2l-20.7-29.5L185,95.8z"/>
		</g>
		<path id="路径_11_" fill="none" stroke="#DDDDDD" d="M176.2,93.1l1.2,0.1"/>
		<path id="路径_12_" fill="none" stroke="#DDDDDD" d="M220.7,63.4l0.399-1.1"/>
		<linearGradient id="路径-19_1_" gradientUnits="userSpaceOnUse" x1="-119.1606" y1="496.5" x2="-119.7591" y2="495.5377">
			<stop  offset="0" style="stop-color:#DDDDDD"/>
			<stop  offset="1" style="stop-color:#DCDCDC"/>
		</linearGradient>
		<path id="路径-19" fill="none" stroke="url(#路径-19_1_)" stroke-width="2" stroke-dasharray="4,3" d="M211,52l-8.8,6.1
			l-6.8,5.3l-4.2,3.8l-3,3.3l-2.101,2.9l-1.2,2.3l-0.5,2.1v1.8l0.5,1.6l1.3,2.1l2.5,2.4l1.5,0.7l2.6,0.3l1.9-0.5l1.6-0.9l1.2-1.3
			l0.7-1.3l0.3-1.5l-0.8-3.5l-0.9-1.5l-1.3-1.1l-1.7-0.6l-2.6,0.3l-5.2,3l-3.3,2.7l-3.2,3.1l-4.1,5.6l-1.8,4.6"/>
		<g>
			
				<linearGradient id="路径-16备份_2_" gradientUnits="userSpaceOnUse" x1="-117.8574" y1="480.749" x2="-117.1405" y2="480.8678" gradientTransform="matrix(59 0 0 -21.1 7169.25 10170.6904)">
				<stop  offset="0" style="stop-color:#EEEEEE"/>
				<stop  offset="1" style="stop-color:#DADBDA"/>
			</linearGradient>
			<path id="路径-16备份" fill="url(#路径-16备份_2_)" d="M259.9,15l-59,11.2l15,9.9L259.9,15z"/>
			
				<linearGradient id="路径-16备份-2_2_" gradientUnits="userSpaceOnUse" x1="-117.4604" y1="487.291" x2="-117.256" y2="487.4751" gradientTransform="matrix(44 0 0 -38 5384.25 18561.5313)">
				<stop  offset="0" style="stop-color:#E3E3E3"/>
				<stop  offset="1" style="stop-color:#D3D3D3"/>
			</linearGradient>
			<path id="路径-16备份-2" fill="url(#路径-16备份-2_2_)" d="M259.9,15l-44,21.1V53L259.9,15z"/>
			
				<linearGradient id="路径-16备份-2_3_" gradientUnits="userSpaceOnUse" x1="-117.3843" y1="485.2334" x2="-116.469" y2="485.6906" gradientTransform="matrix(44 0 0 -30.5 5384.25 14837.7813)">
				<stop  offset="0" style="stop-color:#CAC9C6"/>
				<stop  offset="1" style="stop-color:#DEDEDE"/>
			</linearGradient>
			<path id="路径-16备份-2_1_" fill="url(#路径-16备份-2_3_)" d="M259.9,15l-44,21.1l8.6,9.4L259.9,15z"/>
			
				<linearGradient id="路径-16备份_3_" gradientUnits="userSpaceOnUse" x1="-116.2744" y1="487.9102" x2="-116.7926" y2="487.0071" gradientTransform="matrix(37.7 0 0 -37 4634.5508 18065.0313)">
				<stop  offset="0" style="stop-color:#EEEEEE"/>
				<stop  offset="1" style="stop-color:#DADBDA"/>
			</linearGradient>
			<path id="路径-16备份_1_" fill="url(#路径-16备份_3_)" d="M259.9,15l-37.7,25.4L240.3,52L259.9,15z"/>
		</g>
	</g>
</g>
</svg>
