package com.vedeng.erp.saleorder.service;

import java.util.List;

/**
 * 销售订单自动关闭服务接口
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
public interface SaleOrderAutoCloseService {

    /**
     * 处理自动关闭订单
     * 检查审核通过且未付款且未开票超过60天的销售订单，自动关闭
     * 排除已有业务往来（收款或开票）的订单，确保不误关闭有业务往来的订单
     *
     * @return 自动关闭的订单数量
     */
    int processAutoCloseOrders();

    /**
     * 处理24小时预警通知
     * 对剩余时间小于24小时且未付款未开票的订单发送企业微信预警给销售人员
     * 排除已有业务往来（收款或开票）的订单
     *
     * @return 发送预警通知的数量
     */
    int processWarningNotifications();

    /**
     * 根据订单号列表处理消息提醒功能
     * 接收订单号作为参数，根据订单状态判断是否需要发送警告通知
     * 发送相应的消息提醒（如邮件、短信或系统通知）
     *
     * @param orderNoList 订单号列表
     * @return 发送预警通知的数量
     */
    int processWarningNotifications(List<String> orderNoList);

    /**
     * 批量处理历史数据
     * 系统上线前批量关闭超过60天且未付款未开票的历史订单
     * 排除已有业务往来（收款或开票）的订单
     *
     * @return 处理的订单数量
     */
    int processHistoricalData();

    /**
     * 根据订单号列表批量处理订单关闭
     * 对指定的订单号进行验证，确保订单存在且符合自动关闭条件
     * 只关闭同时满足以下条件的订单：
     * 1. 基本条件：审核通过、未付款(paymentStatus=0)、未开票(invoiceStatus=0)、进行中状态
     * 2. 时间条件：审核通过时间超过60天（当前时间 - validTime > 60天）
     *
     * 注意：此方法与自动定时关闭使用相同的业务规则，确保手动关闭与自动关闭的一致性
     *
     * @param orderNoList 订单号列表
     * @return 处理结果统计信息，包含成功关闭、失败、不符合条件、不满足60天条件的订单数量
     */
    String processOrdersByOrderNoList(List<String> orderNoList);

    /**
     * 清理过期的预警记录
     * 清理超过24小时的预警记录，释放缓存空间
     * 通常由定时任务调用
     *
     * @return 清理的记录数量
     */
    int cleanExpiredWarningRecords();
}
