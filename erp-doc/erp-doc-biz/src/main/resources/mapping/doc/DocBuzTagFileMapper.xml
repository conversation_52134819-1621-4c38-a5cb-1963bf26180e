<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.doc.mapper.DocBuzTagFileMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.doc.domain.entity.DocBuzTagFile">
            <id property="id" column="ID" jdbcType="INTEGER"/>
            <result property="fileId" column="FILE_ID" jdbcType="INTEGER"/>
            <result property="fileName" column="FILE_NAME" jdbcType="VARCHAR"/>
            <result property="buzType" column="BUZ_TYPE" jdbcType="INTEGER"/>
            <result property="buzId" column="BUZ_ID" jdbcType="INTEGER"/>
            <result property="buzTagId" column="BUZ_TAG_ID" jdbcType="INTEGER"/>
            <result property="validStartTime" column="VALID_START_TIME" jdbcType="BIGINT"/>
            <result property="validEndTime" column="VALID_END_TIME" jdbcType="BIGINT"/>
            <result property="hasStamp" column="HAS_STAMP" jdbcType="BOOLEAN"/>
            <result property="externalUrl" column="EXTERNAL_URL" jdbcType="VARCHAR"/>
            <result property="addTime" column="ADD_TIME" jdbcType="BIGINT"/>
            <result property="creator" column="CREATOR" jdbcType="INTEGER"/>
            <result property="modTime" column="MOD_TIME" jdbcType="BIGINT"/>
            <result property="updater" column="UPDATER" jdbcType="INTEGER"/>
            <result property="isDelete" column="IS_DELETE" jdbcType="BOOLEAN"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,FILE_ID,FILE_NAME,
        BUZ_TYPE,BUZ_ID,BUZ_TAG_ID,
        VALID_START_TIME,VALID_END_TIME,HAS_STAMP,
        EXTERNAL_URL,ADD_TIME,CREATOR,
        MOD_TIME,UPDATER,IS_DELETE
    </sql>
    <select id="selectDocGoodsListByBuzIdList" resultType="com.vedeng.doc.dto.DocBuzTagFileDto">
        SELECT
            d.ID,
            d.FILE_ID,
            d.FILE_NAME,
            d.BUZ_TYPE,
            d.BUZ_ID,
            d.BUZ_TAG_ID,
            d.VALID_START_TIME,
            d.VALID_END_TIME,
            d.HAS_STAMP,
            d.EXTERNAL_URL,
            f.FILE_NAME,
            f.ADD_TIME,
            f.DOMAIN,
            f.URI,
            f.MD5,
            f.SUFFIX,
            d.ADD_TIME as addTime,
            FROM_UNIXTIME(d.ADD_TIME/1000, '%Y-%m-%d %H:%i:%S' ) addTimeStr,
            (SELECT USERNAME FROM T_USER WHERE USER_ID = d.CREATOR) as createName,
            concat(f.DOMAIN,f.URI) ossUrl,
            f.OSS_LINK_MOBILE as ossLinkMobile
        FROM T_DOC_R_BUZ_TAG_J_FILE d
            LEFT JOIN T_DOC_FILE f ON d.FILE_ID = f.FILE_ID
        WHERE
            d.BUZ_TYPE = 1
            AND d.IS_DELETE = 0
            AND d.BUZ_ID in
                <foreach item="docBuzId" index="index" collection="docBuzIdList" separator="," open="(" close=")">
                    #{docBuzId,jdbcType=INTEGER}
                </foreach>
    </select>
    <select id="selectDocSupplierListByBuzIdList" resultType="com.vedeng.doc.dto.DocBuzTagFileDto">
        SELECT
            d.ID,
            d.FILE_ID,
            d.FILE_NAME,
            d.BUZ_TYPE,
            d.BUZ_ID,
            d.BUZ_TAG_ID,
            d.VALID_START_TIME,
            d.VALID_END_TIME,
            d.HAS_STAMP,
            d.EXTERNAL_URL,
            f.FILE_NAME,
            f.ADD_TIME,
            f.DOMAIN,
            f.URI,
            f.MD5,
            f.SUFFIX,
            d.ADD_TIME as addTime,
            FROM_UNIXTIME(d.ADD_TIME/1000, '%Y-%m-%d %H:%i:%S' ) addTimeStr,
            (SELECT USERNAME FROM T_USER WHERE USER_ID = d.CREATOR) as createName,
            concat(f.DOMAIN,f.URI) ossUrl,
            f.OSS_LINK_MOBILE as ossLinkMobile
        FROM T_DOC_R_BUZ_TAG_J_FILE d
            LEFT JOIN T_DOC_FILE f ON d.FILE_ID = f.FILE_ID
        WHERE
            d.BUZ_TYPE = 2
            AND d.IS_DELETE = 0
            AND d.BUZ_ID in
                <foreach item="docBuzId" index="index" collection="docBuzIdList" separator="," open="(" close=")">
                    #{docBuzId,jdbcType=INTEGER}
                </foreach>
    </select>
</mapper>
