<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.aftersale.mapper.AfterSaleServiceStandardInfoMapper" >

  <select id="selectBySkuNo" resultType="com.vedeng.erp.aftersale.dto.AfterSaleServiceStandardInfoDto" parameterType="java.lang.String" >
    select
   INSTALL_POLICY_INSTALL_TYPE,RETURN_POLICY_SUPPORT_RETURN,GU<PERSON>ANTEE_POLICY_IS_GUARANTEE,INSTALL_POLICY_FREE_REMOTE_INSTALL,INSTALL_POLICY_INSTALL_FEE,SERVICE_STANDARD_INFO_ID
    from T_AFTER_SALE_SERVICE_STANDARD_INFO
    where SKU_NO = #{skuNo,jdbcType=VARCHAR}
  </select>

</mapper>