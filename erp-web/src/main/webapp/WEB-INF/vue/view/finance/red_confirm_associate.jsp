<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<div id="app" style="display: none;">
    <el-form ref="form" :model="form" label-width="120px">
        <el-row>
            <el-col :span="12">
                <el-form-item label="申请原因：">
                    <el-select v-model="form.applyReason" disabled placeholder="请选择">
                        <el-option label="开票有误" value="01"></el-option>
                        <el-option label="销货退回" value="02"></el-option>
                        <el-option label="服务中止" value="03"></el-option>
                        <el-option label="销售折让" value="04"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>

            <el-col :span="12">
                <el-form-item label="红冲范围：">
                    <el-radio-group v-model="form.redConfirmationScope">
                        <el-radio :label="0">全部红冲</el-radio>
                        <el-radio :label="1" :disabled="canPartRed">部分红冲</el-radio>
                    </el-radio-group>

                    <template>
                        <el-popover
                                placement="top-start"
                                title=""
                                width="300"
                                trigger="hover">
                            <div class="custom-content">
                                1. 蓝票未确认用途，不支持部分红冲<br>
                                2. 申请原因为"开票有误"， 不支持部分红冲<br/>
                            </div>
                            <i class="el-icon-question" style="margin-left: 35px" slot="reference"></i>
                        </el-popover>
                    </template>

                </el-form-item>
            </el-col>
        </el-row>

        <el-row>
            <el-col :span="12">
                <el-form-item label="销售单号：">
                    <el-select v-model="associateSaleOrderId" style="width: 250px;" placeholder="请选择" @change="saleOrderNoChange">
                        <el-option
                                v-for="item in associateSaleOrderList"
                                :key="item.saleOrderId"
                                :label="item.saleOrderNo"
                                :value="item.saleOrderId">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-col>

            <el-col :span="12">
                <el-form-item label="售后单号：">
                    <el-select v-model="associateAfterSaleId" style="width: 250px;" placeholder="请选择" :disabled="haveAfterSale" @change="afterSaleNoChange">
                        <el-option
                                v-for="item in associateAfterSaleList"
                                :key="item.afterSalesId"
                                :label="item.afterSalesNo"
                                :value="item.afterSalesId">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-col>

        </el-row>

        <el-row style="margin-top: 2%">
            <el-col :span="12" style="padding-left: 35px">
                <span>红字明细列表</span>
            </el-col>

            <el-col :span="12" style="padding-left: 35px">
                <span>蓝票号码：{{this.invoiceRedConfirmationDto.blueInvoiceNo}}</span>
            </el-col>
        </el-row>

        <el-row style="padding-left: 35px; margin-top: 10px" v-if="this.form.redConfirmationScope == 1">
            <el-radio-group v-model="form.detailRange" @change="detailRangeChange">
                <el-radio :label="1">显示全部项目</el-radio>
                <el-radio :label="2">显示未关联项目</el-radio>
            </el-radio-group>
        </el-row>

        <el-row style="padding-left: 35px; margin-top: 10px;height: 500px;overflow: auto;" v-if="this.form.redConfirmationScope == 0">
            <el-table
                    :data="redInvoiceDetailList"
                    border
                    stripe
                    key="allRed"
                    :header-cell-style="{'text-align':'center'}"
                    :cell-style="{'text-align':'center'}"
                    style="width: 100%">
                <el-table-column
                        label="项目名称"
                        prop="projectName">
                </el-table-column>
                <el-table-column
                        label="规格型号"
                        min-width="20%"
                        prop="specifications">
                </el-table-column>
                <el-table-column
                        label="单位"
                        min-width="15%"
                        prop="unit">
                </el-table-column>
                <el-table-column
                        label="税收分类编码"
                        min-width="25%"
                        prop="taxCategoryNo">
                </el-table-column>
                <el-table-column
                        label="红字数量"
                        min-width="15%"
                        prop="quantity">
                </el-table-column>
                <el-table-column
                        label="红字金额"
                        min-width="15%"
                        prop="amount">
                </el-table-column>
                <el-table-column
                        label="红字价税合计"
                        min-width="15%"
                        prop="pricePlusTaxes">
                </el-table-column>
            </el-table>
        </el-row>

        <el-row style="padding-left: 35px; margin-top: 10px; height: 500px;overflow: auto;" v-if="this.form.redConfirmationScope == 1">
            <el-table
                    :data="redInvoiceDetailList"
                    border
                    stripe
                    key="partRed"
                    :header-cell-style="{'text-align':'center'}"
                    :cell-style="{'text-align':'center'}"
                    :row-key="row => row.invoiceRedConfirmationItemId"
                    :expand-row-keys="this.expandRowKeys"
                    style="width: 100%">
                <el-table-column type="expand">
                    <template slot-scope="props">
                        <el-table
                                :data="props.row.associateAfterSaleDetailDtoList"
                                border
                                stripe
                                :header-cell-style="{'text-align':'center', 'background-color': '#f0f9eb'}"
                                :cell-style="{'text-align':'center', 'background-color': '#f0f9eb'}"
                                style="width: 100%">
                            <el-table-column
                                    prop="matchingDegree"
                                    label="匹配度"
                                    min-width="20%">
                            </el-table-column>
                            <el-table-column
                                    prop="xmmc"
                                    label="产品名称">
                            </el-table-column>
                            <el-table-column
                                    label="规格型号"
                                    prop="ggxh"
                                    min-width="15%">
                            </el-table-column>
                            <el-table-column
                                    label="单位"
                                    prop="dw"
                                    min-width="15%">
                            </el-table-column>
                            <el-table-column
                                    label="税收分类编码"
                                    prop="sphfwssflhbbm"
                                    min-width="25%">
                            </el-table-column>
                            <el-table-column
                                    label="售后数量"
                                    prop="num"
                                    min-width="15%">
                            </el-table-column>
                            <el-table-column
                                    label="售后金额"
                                    prop="shje"
                                    min-width="15%">
                            </el-table-column>
                            <el-table-column
                                    label="售后价税合计"
                                    prop="shjshj"
                                    min-width="15%">
                            </el-table-column>
                        </el-table>
                    </template>
                </el-table-column>
                <el-table-column
                        label="操作"
                        min-width="10%">
                    <template slot-scope="scope">
                        <el-button type="text" @click="associate(scope.row)">关联</el-button>
                    </template>
                </el-table-column>
                <el-table-column
                        label="项目名称"
                        prop="projectName">
                </el-table-column>
                <el-table-column
                        label="规格型号"
                        min-width="15%"
                        prop="specifications">
                </el-table-column>
                <el-table-column
                        label="单位"
                        min-width="15%"
                        prop="unit">
                </el-table-column>
                <el-table-column
                        label="税收分类编码"
                        prop="taxCategoryNo"
                        min-width="25%">
                </el-table-column>
                <el-table-column
                        label="红字数量"
                        min-width="15%"
                        prop="quantity">
                </el-table-column>
                <el-table-column
                        label="红字金额"
                        min-width="15%"
                        prop="amount">
                </el-table-column>
                <el-table-column
                        label="红字价税合计"
                        min-width="15%"
                        prop="pricePlusTaxes">
                </el-table-column>
            </el-table>
        </el-row>

        <el-dialog
                title="关联售后明细"
                :visible.sync="dialogVisible"
                height="80%"
                :before-close="handleClose"
                width="65%">
            <span style="font-weight: 600">红字明细</span>
            <el-table
                    :data="chosenRedInvoiceDetail"
                    border
                    stripe
                    :header-cell-style="{'text-align':'center'}"
                    :cell-style="{'text-align':'center'}"
                    style="width: 100%; margin-top: 20px; margin-bottom: 35px">
                <el-table-column
                        label="项目名称"
                        prop="projectName">
                </el-table-column>
                <el-table-column
                        label="规格型号"
                        prop="specifications">
                </el-table-column>
                <el-table-column
                        label="单位"
                        prop="unit">
                </el-table-column>
                <el-table-column
                        label="税收分类编码"
                        prop="taxCategoryNo">
                </el-table-column>
                <el-table-column
                        label="红字数量"
                        prop="quantity">
                </el-table-column>
                <el-table-column
                        label="红字金额"
                        prop="amount">
                </el-table-column>
                <el-table-column
                        label="红字价税合计"
                        prop="pricePlusTaxes">
                </el-table-column>
            </el-table>

            <span style="font-weight: 600">售后明细</span>
            <el-table
                    :data="afterSaleDetailList"
                    border
                    stripe
                    :header-cell-style="{'text-align':'center'}"
                    :cell-style="{'text-align':'center'}"
                    @row-click="singleElection"
                    highlight-current-row
                    style="width: 100%; margin-top: 20px">
                <el-table-column
                        label=""
                        width="80px">
                    <template slot-scope="scope">
                        <el-radio v-model="radioSelected" :label="scope.row.saleOrderGoodsId">{{""}}</el-radio>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="matchingDegreeItem"
                        label="匹配度"
                        width="80px">
                </el-table-column>
                <el-table-column
                        prop="xmmc"
                        label="产品名称"
                        width="200px">
                </el-table-column>
                <el-table-column
                        label="规格型号"
                        width="80px"
                        prop="ggxh">
                </el-table-column>
                <el-table-column
                        label="单位"
                        width="80px"
                        prop="dw">
                </el-table-column>
                <el-table-column
                        label="税收分类编码"
                        width="200px"
                        prop="sphfwssflhbbm">
                </el-table-column>
                <el-table-column
                        label="售后数量"
                        width="180px"
                        prop="num">
                </el-table-column>
                <el-table-column
                        label="售后金额"
                        width="180px"
                        prop="shje">
                </el-table-column>
                <el-table-column
                        label="售后价税合计"
                        width="180px"
                        prop="shjshj">
                </el-table-column>
            </el-table>
            <span slot="footer" class="dialog-footer" style="text-align: right">
                <el-button type="primary" @click="saveAssociateChoice()">确 定</el-button>
                <el-button @click="handleClose">取 消</el-button>
            </span>
        </el-dialog>

    </el-form>

    <el-divider style="margin-top: 10%"></el-divider>
    <el-row>
        <el-col :span="6">
            <span style="font-weight: 600">红字合计：</span>
            <span>数量：{{this.totalNum}}</span>
        </el-col>
        <el-col :span="6">
            <span>金额：{{(this.totalAfterSalesAmount).toFixed(2)}}</span>
        </el-col>
        <el-col :span="6">
            <span>税额：{{(this.totalTax).toFixed(2)}}</span>
        </el-col>
        <el-col :span="6">
            <span>价税合计：{{(this.totalAfterSalesAmountAndTax).toFixed(2)}}</span>
        </el-col>
    </el-row>
    <el-divider style="margin-top: 10%"></el-divider>
    <el-row>
        <el-col :span="12" style="padding-left: 2%; ">
            <el-button type="warning" plain round v-if="this.invoiceRedConfirmationDto.redConfirmationStatus == 0">初始化</el-button>
            <el-button type="warning" plain round v-if="this.invoiceRedConfirmationDto.redConfirmationStatus == 1">已申请</el-button>
            <el-button type="warning" plain round v-if="this.invoiceRedConfirmationDto.redConfirmationStatus == 2">已确认</el-button>
            <el-button type="warning" plain round v-if="this.invoiceRedConfirmationDto.redConfirmationStatus == 3">已开票</el-button>
            <el-button type="warning" plain round v-if="this.invoiceRedConfirmationDto.redConfirmationStatus == 4">已作废</el-button>
        </el-col>
        <el-col :span="12" style="text-align: right; padding-right: 5%">
            <el-button type="success" @click="submit()" :disabled="isSubmit">提交</el-button>
            <el-button @click="close()">取消</el-button>
        </el-col>
    </el-row>
</div>
<script src="${pageContext.request.contextPath}/static/api/invoiceredconfirmation/red_confirm_associate.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">

    var invoiceRedConfirmationId = ${invoiceRedConfirmationId};

    new Vue({
        el: '#app',
        data() {
            return {
                invoiceRedConfirmationDto: {},
                form: {
                    applyReason: '',
                    redConfirmationScope: 0,
                    detailRange: 1
                },
                // 是否禁用部分红冲
                canPartRed: false,
                // 红字明细列表
                redInvoiceDetailList: [],
                // 关联完蓝字明细的列表,这个数组不会减少，只会增加关联的联票明细，最终传入接口
                redAssociateBlueDetailList: [],
                // 售后明细列表
                afterSaleDetailList: [],
                dialogVisible: false,
                expandRowKeys: [],

                // 点击关联按钮后，传入弹窗的红票明细
                chosenRedInvoiceDetail:[],
                radioSelected: '',
                // 暂存选中的蓝票明细行
                checkList: [],
                currentRedDetailId: 0,
                // 合计数据
                totalNum: 0,
                totalAfterSalesAmount: 0.00,
                totalTax: 0.00,
                totalAfterSalesAmountAndTax: 0.00,
                associateSaleOrderList: [],
                associateSaleOrderId: '',
                associateAfterSaleList: [],
                associateAfterSaleId: '',
                isSubmit: false,
                haveAfterSale: true,
            }
        },

        mounted() {
            loadingApp();
            this.initData(invoiceRedConfirmationId)
        },

        methods: {
            // 页面初始化数据加载
            initData(invoiceRedConfirmationId) {

                associateSubjectInfo({"invoiceRedConfirmationId": invoiceRedConfirmationId})
                    .then(res => {
                        if (res.data.code !== 0) {
                            this.$message.error("数据异常：" + res.data.message);
                            return;
                        }
                        this.invoiceRedConfirmationDto = res.data.data.invoiceRedConfirmationDto;
                        if (this.invoiceRedConfirmationDto.applyReason === '01' || (this.invoiceRedConfirmationDto.valueAddedTaxStatus === '03' && this.invoiceRedConfirmationDto.enterAccountStatus === '00')) {
                            this.canPartRed = true;
                        }
                        if (this.invoiceRedConfirmationDto.applyReason != null && this.invoiceRedConfirmationDto.applyReason.trim() !== '') {
                            // 仅当 this.invoiceRedConfirmationDto.applyReason 不为空且不为空字符串时才将其赋值给 this.form.applyReason
                            this.form.applyReason = this.invoiceRedConfirmationDto.applyReason;
                        }
                        this.redInvoiceDetailList = res.data.data.invoiceRedConfirmationDto.invoiceRedConfirmationItemDtoList;
                        this.redInvoiceDetailList.forEach(item => {
                            this.totalNum = this.totalNum + item.quantity;
                            this.totalAfterSalesAmount = this.totalAfterSalesAmount + item.amount;
                            this.totalTax = this.totalTax + item.uaxAmount;
                            this.totalAfterSalesAmountAndTax = this.totalAfterSalesAmountAndTax + item.pricePlusTaxes;
                        });
                        if (res.data.data.associateSaleOrderDtoList != null && res.data.data.associateSaleOrderDtoList.length >= 0) {
                            this.associateSaleOrderList = res.data.data.associateSaleOrderDtoList;
                        }
                        this.redAssociateBlueDetailList = this.redInvoiceDetailList;
                    })
                    .catch(err => {
                        this.$message.error("系统异常：" + err.message);
                    });

            },

            // 销售单号变化监听
            saleOrderNoChange(val) {
                console.log('选中的销售单号值为:', val);
                this.redInvoiceDetailList.forEach(item => {
                    item.associateAfterSaleDetailDtoList = [];
                });
                this.redAssociateBlueDetailList.forEach(item => {
                    item.associateAfterSaleDetailDtoList = [];
                });
                this.associateAfterSaleList = [];
                this.associateAfterSaleId = '';

                let saleOrderInfo;
                this.associateSaleOrderList.forEach(item => {
                    if(item.saleOrderId === val) {
                        saleOrderInfo = item;
                    }
                });
                if (saleOrderInfo.associateAfterOrderDtoList != null && saleOrderInfo.associateAfterOrderDtoList.length > 0) {
                    this.associateAfterSaleList = saleOrderInfo.associateAfterOrderDtoList;
                    this.haveAfterSale = false
                    //this.associateAfterSaleId = this.associateAfterSaleList[0].afterSalesId;
                } else {
                    this.associateAfterSaleList = [];
                    this.associateAfterSaleId = '';
                    this.$message.error("销售单号：" + saleOrderInfo.saleOrderNo + "，没有进行中的退货、退票售后单。");
                }
            },

            // 售后单号变化监听
            afterSaleNoChange(val) {
                if (this.associateSaleOrderId == null || this.associateSaleOrderId === '') {
                    this.$message.error("请选择销售单号");
                    return;
                }
                if (this.associateAfterSaleId == null || this.associateAfterSaleId === '') {
                    this.$message.error("请选择售后单号");
                    return;
                }
                this.redInvoiceDetailList.forEach(item => {
                    item.associateAfterSaleDetailDtoList = [];
                });
                this.redAssociateBlueDetailList.forEach(item => {
                    item.associateAfterSaleDetailDtoList = [];
                });
                // 查询售后明细列表
                getAfterSaleDetailList({
                    "afterSaleId": this.associateAfterSaleId, "saleOrderId": this.associateSaleOrderId,
                    "blueInvoiceNo": this.invoiceRedConfirmationDto.blueInvoiceNo
                }).then(res => {
                    if (res.data.code !== 0) {
                        this.$message.error("数据异常：" + res.data.message);
                    } else {
                        this.afterSaleDetailList = res.data.data;
                        // 页面初始化时自动匹配售后明细
                        this.redInvoiceDetailList.forEach(item => {
                            item.associateAfterSaleDetailDtoList = [];
                            this.afterSaleDetailList.forEach(afterDetail => {
                                if (afterDetail.xh == item.serialNumber) {
                                    if (item.projectName == afterDetail.xmmc) {
                                        afterDetail.matchingDegree = afterDetail.matchingDegree + 25;
                                    }
                                    if (item.specifications == afterDetail.ggxh) {
                                        afterDetail.matchingDegree = afterDetail.matchingDegree + 25;
                                    }
                                    if (item.unit == afterDetail.dw) {
                                        afterDetail.matchingDegree = afterDetail.matchingDegree + 25;
                                    }
                                    if (item.taxCategoryNo == afterDetail.sphfwssflhbbm) {
                                        afterDetail.matchingDegree = afterDetail.matchingDegree + 25;
                                    }
                                    item.associateAfterSaleDetailDtoList.push(afterDetail);
                                    this.expandRowKeys.push(item.invoiceRedConfirmationItemId);
                                    return;
                                }
                            })
                        })
                        this.redAssociateBlueDetailList = this.redInvoiceDetailList;
                    }
                }).catch(err => {
                    this.$message.error("系统异常：" + err.message);
                });
            },

            // 点击关联按钮操作
            async associate(row) {
                if (this.associateSaleOrderId == null || this.associateSaleOrderId === '') {
                    this.$message.error("请选择销售单号");
                    return;
                }
                if (this.associateAfterSaleId == null || this.associateAfterSaleId === '') {
                    this.$message.error("请选择售后单号");
                    return;
                }

                if (this.afterSaleDetailList == null || this.afterSaleDetailList.length === 0) {
                    this.$message.error("售后单号：" + this.associateAfterSaleId + "，没有售后明细。");
                    return;
                }

                this.dialogVisible = true;
                // 回显当前关联行的红字明细
                this.chosenRedInvoiceDetail.push(row);
                // 记录下当前是操作的哪一条红票明细
                this.currentRedDetailId = row.invoiceRedConfirmationItemId;

                // 计算匹配度
                this.afterSaleDetailList.forEach(item => {
                    item.matchingDegreeItem = 0;
                    if (row.projectName == item.xmmc) {
                        item.matchingDegreeItem = item.matchingDegreeItem + 25;
                    }
                    if (row.specifications == item.ggxh) {
                        item.matchingDegreeItem = item.matchingDegreeItem + 25;
                    }
                    if (row.unit == item.dw) {
                        item.matchingDegreeItem = item.matchingDegreeItem + 25;
                    }
                    if (row.taxCategoryNo == item.sphfwssflhbbm) {
                        item.matchingDegreeItem = item.matchingDegreeItem + 25;
                    }
                });

                this.afterSaleDetailList.sort((a, b) => b.matchingDegreeItem - a.matchingDegreeItem);

                if (row.associateAfterSaleDetailDtoList != null && row.associateAfterSaleDetailDtoList.length > 0) {
                    this.afterSaleDetailList.forEach(item => {
                        if (item.saleOrderGoodsId == row.associateAfterSaleDetailDtoList[0].saleOrderGoodsId) {
                            this.radioSelected = item.saleOrderGoodsId;
                            this.checkList.push(item);
                        }
                    })
                }
            },

            // 弹窗关闭清空当前行的信息
            handleClose() {
                this.chosenRedInvoiceDetail = [];
                this.dialogVisible = false;
                this.radioSelected = '';
                this.currentRedDetailId = 0;
            },

            // 关联弹窗保存
            saveAssociateChoice() {
                if (this.checkList.length > 0) {
                    // 将选中的蓝票明细绑定到红字明细上
                    this.redInvoiceDetailList.forEach(item => {
                        // 如果当前选中的蓝票明细行，已经被其他的红字明细行选中了，则需要把之前的绑定关系删掉
                        // 需要先执行删除历史数据，再绑定本次数据
                        if (item.associateAfterSaleDetailDtoList != null && item.associateAfterSaleDetailDtoList.length > 0) {
                            if (item.associateAfterSaleDetailDtoList[0].saleOrderGoodsId == this.checkList[0].saleOrderGoodsId) {
                                item.associateAfterSaleDetailDtoList = [];
                            }
                        }

                        if (item.invoiceRedConfirmationItemId == this.currentRedDetailId) {
                            item.associateAfterSaleDetailDtoList = this.checkList;
                            item.associateAfterSaleDetailDtoList.forEach(item => {
                                item.matchingDegree = item.matchingDegreeItem;
                            })
                            this.expandRowKeys.push(item.invoiceRedConfirmationItemId);
                        }
                    });
                    // 更新暂存的红蓝票关联数组中的信息
                    this.redAssociateBlueDetailList.forEach(item => {
                        if (item.associateAfterSaleDetailDtoList != null && item.associateAfterSaleDetailDtoList.length > 0) {
                            if (item.associateAfterSaleDetailDtoList[0].saleOrderGoodsId == this.checkList[0].saleOrderGoodsId) {
                                item.associateAfterSaleDetailDtoList = [];
                            }
                        }

                        if(item.invoiceRedConfirmationItemId == this.currentRedDetailId) {
                            item.associateAfterSaleDetailDtoList = this.checkList;
                        }
                    });
                }
                // 清空checkList
                this.checkList = [];
                this.handleClose();
            },

            // 选中蓝票明细行
            singleElection(row) {
                console.log("选中的售后明细为:", row);
                this.templateSelection = row.saleOrderGoodsId;
                this.checkList = this.afterSaleDetailList.filter((item) => item.saleOrderGoodsId === row.saleOrderGoodsId);
            },

            // 监听是展示全部红字明细还是展示未关联的明细
            detailRangeChange(val) {
                if (val == 1) {
                    // 展示全部
                    this.redInvoiceDetailList = this.redAssociateBlueDetailList;
                    this.redInvoiceDetailList.forEach(item => {
                        if (item.associateAfterSaleDetailDtoList != null && item.associateAfterSaleDetailDtoList.length > 0) {
                            this.expandRowKeys.push(item.invoiceRedConfirmationItemId);
                        }
                    })
                }
                if (val == 2) {
                    // 展示未关联
                    this.redInvoiceDetailList = this.redInvoiceDetailList.filter((item) => item.associateAfterSaleDetailDtoList == null || item.associateAfterSaleDetailDtoList.length == 0);
                    this.expandRowKeys = [];
                }
            },

            // 取消按钮
            close() {
                parent.layer.closeAll();
            },

            submit(){
                this.isSubmit = true;
                if (this.form.redConfirmationScope === 0 || this.form.redConfirmationScope === 1) {
                    // 全部红冲
                    if (this.associateSaleOrderId == null || this.associateSaleOrderId === '') {
                        this.$message.error("请选择销售单号");
                        this.isSubmit = false;
                        return;
                    }
                    if (this.associateAfterSaleId == null || this.associateAfterSaleId === '') {
                        this.$message.error("请选择售后单号");
                        this.isSubmit = false;
                        return;
                    }
                    if (this.form.redConfirmationScope === 1) {
                        // 检查是否所有红字明细均已关联售后明细
                        let errorFound = false; // 创建一个变量用于标记是否发现错误
                        for (let item of this.redAssociateBlueDetailList) {
                            if (item.associateAfterSaleDetailDtoList == null || item.associateAfterSaleDetailDtoList.length === 0) {
                                this.$message.error("红字明细：" + item.projectName + "，没有关联售后明细。");
                                errorFound = true; // 设置错误标记为 true
                                break; // 中止循环
                            }
                        }
                        if (errorFound) {
                            this.isSubmit = false;
                            return; // 如果发现错误，中止整个函数
                        }

                        // 检查是否红字明细数量与关联的售后明细数量一致
                        for (let item of this.redAssociateBlueDetailList) {
                            if (item.associateAfterSaleDetailDtoList != null && item.associateAfterSaleDetailDtoList.length > 0) {
                                if (Math.abs(item.quantity) != item.associateAfterSaleDetailDtoList[0].num) {
                                    this.$message.error("红字明细：" + item.projectName + "，关联的售后明细数量与红字明细数量不一致。");
                                    errorFound = true; // 设置错误标记为 true
                                    break; // 中止循环
                                }
                            }
                        }

                        if (errorFound) {
                            this.isSubmit = false;
                            return; // 如果发现错误，中止整个函数
                        }

                    }

                    let saleOrderInfo = this.associateSaleOrderList.filter((item) => item.saleOrderId === this.associateSaleOrderId);
                    let afterSaleInfo = this.associateAfterSaleList.filter((item) => item.afterSalesId === this.associateAfterSaleId);
                    let submitDto = this.invoiceRedConfirmationDto;
                    submitDto.redConfirmationScope = this.form.redConfirmationScope
                    submitDto.businessOrderId = saleOrderInfo[0].saleOrderId;
                    submitDto.businessOrderNo = saleOrderInfo[0].saleOrderNo;
                    submitDto.afterSaleBusinessOrderId = afterSaleInfo[0].afterSalesId;
                    submitDto.afterSaleBusinessOrderNo = afterSaleInfo[0].afterSalesNo;
                    submitDto.invoiceRedConfirmationItemDtoList = this.redAssociateBlueDetailList;
                    console.log(submitDto);

                    associateAftersale(submitDto)
                        .then(res => {
                            if (res.data.code !== 0) {
                                this.$message.error("数据异常：" + res.data.message);
                                return;
                            }
                            this.$message.success("提交成功");
                            parent.layer.closeAll();
                            parent.window.location.reload();
                        })
                        .catch(err => {
                            this.$message.error("系统异常：" + err.message);
                        });
                }
            }
        }
    })
</script>

<style>
</style>