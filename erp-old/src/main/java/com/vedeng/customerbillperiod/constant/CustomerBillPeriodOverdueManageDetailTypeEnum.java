package com.vedeng.customerbillperiod.constant;

/**
 * @Author: daniel
 * @Date: 2021/7/27 16 34
 * @Description: 客户账期逾期管理明细类型枚举类
 */
public enum CustomerBillPeriodOverdueManageDetailTypeEnum {

    /**
     * 订单发货
     */
    ORDER_EXPRESS(1,"订单发货"),

    ORDER_INVOICE(2,"订单开票"),

    AFTER_SALES_RETURN(3,"售后退货"),

    AFTER_SALES_INVOICE(4,"售后退票"),

    REPAYMENT(5,"还款"),

    ORDER_EXPRESS_INVALID(6,"订单物流作废")

    ;

    private Integer code;

    private String desc;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    CustomerBillPeriodOverdueManageDetailTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
