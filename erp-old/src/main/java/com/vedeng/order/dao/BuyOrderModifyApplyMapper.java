package com.vedeng.order.dao;

import com.vedeng.order.model.BuyorderModifyApply;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.order.model.vo.SaleorderVo;
import com.vedeng.erp.buyorder.dto.BuyOrderModifyApply;

import java.util.List;

/**
* @Entity com.vedeng.order.dao.BuyOrderModifyApply
*/
public interface BuyOrderModifyApplyMapper {


    BuyOrderModifyApply selectSingleById(Integer buyOrderModifyApplyId);

    List<SaleorderVo> selectRelatedSaleOrderInfoById(Integer buyOrderModifyApplyId);

    List<BuyorderVo> selectRelatedBuyOrderInfoById(Integer buyOrderModifyApplyId);

    /**
     * 订单流迁移db方法
     */
    int insertSelective(BuyorderModifyApply record);

    /**
     * 订单流迁移db方法
     */
    int updateByPrimaryKeySelective(BuyorderModifyApply record);
}
