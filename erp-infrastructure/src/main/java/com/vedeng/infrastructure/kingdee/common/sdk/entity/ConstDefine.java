package com.vedeng.infrastructure.kingdee.common.sdk.entity;

/* loaded from: k3cloud-webapi-sdk7.9.2.jar:com/kingdee/bos/webapi/entity/ConstDefine.class */
public class ConstDefine {
    public static final String X_KD_AppKey = "X-Kd-Appkey";
    public static final String X_KD_AppData = "X-Kd-Appdata";
    public static final String X_KD_Signature = "X-Kd-Signature";
    public static final String KDSERVICE_SESSIONID = "kdservice-sessionid";
    public static final String SessionId = "ASP.NET_SessionId";
    public static final String X_KD_SID = "SID";
    public static final String X_Api_ClientID = "X-Api-ClientID";
    public static final String X_Api_Auth_Version = "X-Api-Auth-Version";
    public static final String X_Api_Signheaders = "x-api-signheaders";
    public static final String X_Api_Nonce = "x-api-nonce";
    public static final String X_Api_Timestamp = "x-api-timestamp";
    public static final String X_Api_Signature = "X-Api-Signature";
    public static final String Cookie_Set = "Set-Cookie";
    public static final String BEGINMETHOD_Header = "beginmethod";
    public static final String QUERYMETHOD_Header = "querymethod";
    public static final String BEGINMETHOD_Method = "BeginQueryImpl";
    public static final String QUERYMETHOD_Method = "QueryAsyncResult";
    public static final int QueryInvode_State_Pending = 0;
    public static final int QueryInvode_State_Running = 1;
    public static final int QueryInvode_State_Complete = 2;
}
