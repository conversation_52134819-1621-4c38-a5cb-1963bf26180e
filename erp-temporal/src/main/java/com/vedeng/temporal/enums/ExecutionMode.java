package com.vedeng.temporal.enums;

/**
 * 执行模式枚举
 * 定义多公司业务流程的执行模式
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-15
 */
public enum ExecutionMode {
    
    /**
     * 串行执行模式
     * 公司按顺序依次执行，后一个公司等待前一个公司完成
     * 适用场景：付款流程、采购-销售-库存流程等有严格依赖关系的业务
     */
    SEQUENTIAL("SEQUENTIAL", "串行执行", "公司按顺序依次执行，后一个公司等待前一个公司完成"),

    /**
     * 并行执行模式
     * 所有公司同时启动，独立执行，无相互依赖
     * 适用场景：发票录票流程等无依赖关系的业务
     */
    PARALLEL("PARALLEL", "并行执行", "所有公司同时启动，独立执行，无相互依赖");
    
    /**
     * 执行模式代码
     */
    private final String code;
    
    /**
     * 执行模式名称
     */
    private final String name;
    
    /**
     * 执行模式描述
     */
    private final String description;
    
    /**
     * 构造函数
     * 
     * @param code 执行模式代码
     * @param name 执行模式名称
     * @param description 执行模式描述
     */
    ExecutionMode(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }
    
    /**
     * 获取执行模式代码
     * 
     * @return 执行模式代码
     */
    public String getCode() {
        return code;
    }
    
    /**
     * 获取执行模式名称
     * 
     * @return 执行模式名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 获取执行模式描述
     * 
     * @return 执行模式描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取执行模式
     * 
     * @param code 执行模式代码
     * @return 执行模式枚举，如果未找到则返回 SEQUENTIAL
     */
    public static ExecutionMode fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return SEQUENTIAL; // 默认返回串行执行
        }
        
        for (ExecutionMode mode : ExecutionMode.values()) {
            if (mode.getCode().equalsIgnoreCase(code.trim())) {
                return mode;
            }
        }
        
        return SEQUENTIAL; // 默认返回串行执行
    }
    
    /**
     * 判断是否为串行执行模式
     * 
     * @return true 如果是串行执行模式，false 否则
     */
    public boolean isSequential() {
        return this == SEQUENTIAL;
    }
    
    /**
     * 判断是否为并行执行模式
     * 
     * @return true 如果是并行执行模式，false 否则
     */
    public boolean isParallel() {
        return this == PARALLEL;
    }
    
    /**
     * 判断是否支持并行处理
     *
     * @return true 如果支持并行处理，false 否则
     */
    public boolean supportsParallelProcessing() {
        return this == PARALLEL;
    }
    
    /**
     * 获取执行模式的详细信息
     * 
     * @return 包含代码、名称和描述的字符串
     */
    public String getDetailedInfo() {
        return String.format("ExecutionMode{code='%s', name='%s', description='%s'}", 
                code, name, description);
    }
    
    @Override
    public String toString() {
        return String.format("%s(%s)", name, code);
    }
}
