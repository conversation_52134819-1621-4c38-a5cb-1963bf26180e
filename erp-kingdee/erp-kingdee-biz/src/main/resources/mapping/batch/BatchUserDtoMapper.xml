<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchUserDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchUserDto">
    <!--@mbg.generated-->
    <!--@Table T_USER-->
    <id column="USER_ID" jdbcType="INTEGER" property="userId" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="USERNAME" jdbcType="VARCHAR" property="username" />
    <result column="NUMBER" jdbcType="VARCHAR" property="number" />
    <result column="PASSWORD" jdbcType="VARCHAR" property="password" />
    <result column="SALT" jdbcType="VARCHAR" property="salt" />
    <result column="PARENT_ID" jdbcType="INTEGER" property="parentId" />
    <result column="IS_ADMIN" jdbcType="BOOLEAN" property="isAdmin" />
    <result column="IS_DISABLED" jdbcType="BOOLEAN" property="isDisabled" />
    <result column="DISABLED_REASON" jdbcType="VARCHAR" property="disabledReason" />
    <result column="LAST_LOGIN_TIME" jdbcType="BIGINT" property="lastLoginTime" />
    <result column="LAST_LOGIN_IP" jdbcType="VARCHAR" property="lastLoginIp" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="STAFF" jdbcType="BOOLEAN" property="staff" />
    <result column="SYSTEM" jdbcType="VARCHAR" property="system" />
    <result column="USER_BELONG_COMPANY_ID" jdbcType="INTEGER" property="userBelongCompanyId" />
    <result column="TT_NUMBER" jdbcType="VARCHAR" property="ttNumber" />
    <result column="ORG_IDS_LIST" jdbcType="VARCHAR" property="orgIdsList" />
    <result column="TELECOM_LINE" jdbcType="VARCHAR" property="telecomLine" />
    <result column="MOBILE_LINE" jdbcType="VARCHAR" property="mobileLine" />
    <result column="UNICOM_LINE" jdbcType="VARCHAR" property="unicomLine" />
    <result column="CUSTOMER_LINE" jdbcType="VARCHAR" property="customerLine" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    USER_ID, COMPANY_ID, USERNAME, `NUMBER`, `PASSWORD`, SALT, PARENT_ID, IS_ADMIN, IS_DISABLED, 
    DISABLED_REASON, LAST_LOGIN_TIME, LAST_LOGIN_IP, ADD_TIME, CREATOR, MOD_TIME, UPDATER, 
    STAFF, `SYSTEM`, USER_BELONG_COMPANY_ID, TT_NUMBER, ORG_IDS_LIST, TELECOM_LINE, MOBILE_LINE, 
    UNICOM_LINE, CUSTOMER_LINE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_USER
    where USER_ID = #{userId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_USER
    where USER_ID = #{userId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="USER_ID" keyProperty="userId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchUserDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_USER (COMPANY_ID, USERNAME, `NUMBER`, 
      `PASSWORD`, SALT, PARENT_ID, 
      IS_ADMIN, IS_DISABLED, DISABLED_REASON, 
      LAST_LOGIN_TIME, LAST_LOGIN_IP, ADD_TIME, 
      CREATOR, MOD_TIME, UPDATER, 
      STAFF, `SYSTEM`, USER_BELONG_COMPANY_ID, 
      TT_NUMBER, ORG_IDS_LIST, TELECOM_LINE, 
      MOBILE_LINE, UNICOM_LINE, CUSTOMER_LINE
      )
    values (#{companyId,jdbcType=INTEGER}, #{username,jdbcType=VARCHAR}, #{number,jdbcType=VARCHAR}, 
      #{password,jdbcType=VARCHAR}, #{salt,jdbcType=VARCHAR}, #{parentId,jdbcType=INTEGER}, 
      #{isAdmin,jdbcType=BOOLEAN}, #{isDisabled,jdbcType=BOOLEAN}, #{disabledReason,jdbcType=VARCHAR}, 
      #{lastLoginTime,jdbcType=BIGINT}, #{lastLoginIp,jdbcType=VARCHAR}, #{addTime,jdbcType=BIGINT}, 
      #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}, 
      #{staff,jdbcType=BOOLEAN}, #{system,jdbcType=VARCHAR}, #{userBelongCompanyId,jdbcType=INTEGER}, 
      #{ttNumber,jdbcType=VARCHAR}, #{orgIdsList,jdbcType=VARCHAR}, #{telecomLine,jdbcType=VARCHAR}, 
      #{mobileLine,jdbcType=VARCHAR}, #{unicomLine,jdbcType=VARCHAR}, #{customerLine,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="USER_ID" keyProperty="userId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchUserDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_USER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="username != null and username != ''">
        USERNAME,
      </if>
      <if test="number != null and number != ''">
        `NUMBER`,
      </if>
      <if test="password != null and password != ''">
        `PASSWORD`,
      </if>
      <if test="salt != null and salt != ''">
        SALT,
      </if>
      <if test="parentId != null">
        PARENT_ID,
      </if>
      <if test="isAdmin != null">
        IS_ADMIN,
      </if>
      <if test="isDisabled != null">
        IS_DISABLED,
      </if>
      <if test="disabledReason != null and disabledReason != ''">
        DISABLED_REASON,
      </if>
      <if test="lastLoginTime != null">
        LAST_LOGIN_TIME,
      </if>
      <if test="lastLoginIp != null and lastLoginIp != ''">
        LAST_LOGIN_IP,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="staff != null">
        STAFF,
      </if>
      <if test="system != null and system != ''">
        `SYSTEM`,
      </if>
      <if test="userBelongCompanyId != null">
        USER_BELONG_COMPANY_ID,
      </if>
      <if test="ttNumber != null and ttNumber != ''">
        TT_NUMBER,
      </if>
      <if test="orgIdsList != null and orgIdsList != ''">
        ORG_IDS_LIST,
      </if>
      <if test="telecomLine != null and telecomLine != ''">
        TELECOM_LINE,
      </if>
      <if test="mobileLine != null and mobileLine != ''">
        MOBILE_LINE,
      </if>
      <if test="unicomLine != null and unicomLine != ''">
        UNICOM_LINE,
      </if>
      <if test="customerLine != null and customerLine != ''">
        CUSTOMER_LINE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="username != null and username != ''">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="number != null and number != ''">
        #{number,jdbcType=VARCHAR},
      </if>
      <if test="password != null and password != ''">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="salt != null and salt != ''">
        #{salt,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=INTEGER},
      </if>
      <if test="isAdmin != null">
        #{isAdmin,jdbcType=BOOLEAN},
      </if>
      <if test="isDisabled != null">
        #{isDisabled,jdbcType=BOOLEAN},
      </if>
      <if test="disabledReason != null and disabledReason != ''">
        #{disabledReason,jdbcType=VARCHAR},
      </if>
      <if test="lastLoginTime != null">
        #{lastLoginTime,jdbcType=BIGINT},
      </if>
      <if test="lastLoginIp != null and lastLoginIp != ''">
        #{lastLoginIp,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="staff != null">
        #{staff,jdbcType=BOOLEAN},
      </if>
      <if test="system != null and system != ''">
        #{system,jdbcType=VARCHAR},
      </if>
      <if test="userBelongCompanyId != null">
        #{userBelongCompanyId,jdbcType=INTEGER},
      </if>
      <if test="ttNumber != null and ttNumber != ''">
        #{ttNumber,jdbcType=VARCHAR},
      </if>
      <if test="orgIdsList != null and orgIdsList != ''">
        #{orgIdsList,jdbcType=VARCHAR},
      </if>
      <if test="telecomLine != null and telecomLine != ''">
        #{telecomLine,jdbcType=VARCHAR},
      </if>
      <if test="mobileLine != null and mobileLine != ''">
        #{mobileLine,jdbcType=VARCHAR},
      </if>
      <if test="unicomLine != null and unicomLine != ''">
        #{unicomLine,jdbcType=VARCHAR},
      </if>
      <if test="customerLine != null and customerLine != ''">
        #{customerLine,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchUserDto">
    <!--@mbg.generated-->
    update T_USER
    <set>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="username != null and username != ''">
        USERNAME = #{username,jdbcType=VARCHAR},
      </if>
      <if test="number != null and number != ''">
        `NUMBER` = #{number,jdbcType=VARCHAR},
      </if>
      <if test="password != null and password != ''">
        `PASSWORD` = #{password,jdbcType=VARCHAR},
      </if>
      <if test="salt != null and salt != ''">
        SALT = #{salt,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        PARENT_ID = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="isAdmin != null">
        IS_ADMIN = #{isAdmin,jdbcType=BOOLEAN},
      </if>
      <if test="isDisabled != null">
        IS_DISABLED = #{isDisabled,jdbcType=BOOLEAN},
      </if>
      <if test="disabledReason != null and disabledReason != ''">
        DISABLED_REASON = #{disabledReason,jdbcType=VARCHAR},
      </if>
      <if test="lastLoginTime != null">
        LAST_LOGIN_TIME = #{lastLoginTime,jdbcType=BIGINT},
      </if>
      <if test="lastLoginIp != null and lastLoginIp != ''">
        LAST_LOGIN_IP = #{lastLoginIp,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="staff != null">
        STAFF = #{staff,jdbcType=BOOLEAN},
      </if>
      <if test="system != null and system != ''">
        `SYSTEM` = #{system,jdbcType=VARCHAR},
      </if>
      <if test="userBelongCompanyId != null">
        USER_BELONG_COMPANY_ID = #{userBelongCompanyId,jdbcType=INTEGER},
      </if>
      <if test="ttNumber != null and ttNumber != ''">
        TT_NUMBER = #{ttNumber,jdbcType=VARCHAR},
      </if>
      <if test="orgIdsList != null and orgIdsList != ''">
        ORG_IDS_LIST = #{orgIdsList,jdbcType=VARCHAR},
      </if>
      <if test="telecomLine != null and telecomLine != ''">
        TELECOM_LINE = #{telecomLine,jdbcType=VARCHAR},
      </if>
      <if test="mobileLine != null and mobileLine != ''">
        MOBILE_LINE = #{mobileLine,jdbcType=VARCHAR},
      </if>
      <if test="unicomLine != null and unicomLine != ''">
        UNICOM_LINE = #{unicomLine,jdbcType=VARCHAR},
      </if>
      <if test="customerLine != null and customerLine != ''">
        CUSTOMER_LINE = #{customerLine,jdbcType=VARCHAR},
      </if>
    </set>
    where USER_ID = #{userId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchUserDto">
    <!--@mbg.generated-->
    update T_USER
    set COMPANY_ID = #{companyId,jdbcType=INTEGER},
      USERNAME = #{username,jdbcType=VARCHAR},
      `NUMBER` = #{number,jdbcType=VARCHAR},
      `PASSWORD` = #{password,jdbcType=VARCHAR},
      SALT = #{salt,jdbcType=VARCHAR},
      PARENT_ID = #{parentId,jdbcType=INTEGER},
      IS_ADMIN = #{isAdmin,jdbcType=BOOLEAN},
      IS_DISABLED = #{isDisabled,jdbcType=BOOLEAN},
      DISABLED_REASON = #{disabledReason,jdbcType=VARCHAR},
      LAST_LOGIN_TIME = #{lastLoginTime,jdbcType=BIGINT},
      LAST_LOGIN_IP = #{lastLoginIp,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      STAFF = #{staff,jdbcType=BOOLEAN},
      `SYSTEM` = #{system,jdbcType=VARCHAR},
      USER_BELONG_COMPANY_ID = #{userBelongCompanyId,jdbcType=INTEGER},
      TT_NUMBER = #{ttNumber,jdbcType=VARCHAR},
      ORG_IDS_LIST = #{orgIdsList,jdbcType=VARCHAR},
      TELECOM_LINE = #{telecomLine,jdbcType=VARCHAR},
      MOBILE_LINE = #{mobileLine,jdbcType=VARCHAR},
      UNICOM_LINE = #{unicomLine,jdbcType=VARCHAR},
      CUSTOMER_LINE = #{customerLine,jdbcType=VARCHAR}
    where USER_ID = #{userId,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_USER
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="COMPANY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.companyId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="USERNAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.username,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`NUMBER` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.number,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`PASSWORD` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.password,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="SALT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.salt,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="PARENT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.parentId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_ADMIN = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.isAdmin,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="IS_DISABLED = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.isDisabled,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="DISABLED_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.disabledReason,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="LAST_LOGIN_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.lastLoginTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="LAST_LOGIN_IP = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.lastLoginIp,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.addTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.modTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="STAFF = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.staff,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="`SYSTEM` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.system,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="USER_BELONG_COMPANY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.userBelongCompanyId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="TT_NUMBER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.ttNumber,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ORG_IDS_LIST = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.orgIdsList,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TELECOM_LINE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.telecomLine,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="MOBILE_LINE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.mobileLine,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UNICOM_LINE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.unicomLine,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="CUSTOMER_LINE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.customerLine,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where USER_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.userId,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_USER
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="COMPANY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.companyId != null">
            when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.companyId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="USERNAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.username != null">
            when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.username,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`NUMBER` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.number != null">
            when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.number,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`PASSWORD` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.password != null">
            when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.password,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SALT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.salt != null">
            when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.salt,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PARENT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.parentId != null">
            when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.parentId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_ADMIN = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isAdmin != null">
            when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.isAdmin,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DISABLED = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDisabled != null">
            when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.isDisabled,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="DISABLED_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.disabledReason != null">
            when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.disabledReason,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="LAST_LOGIN_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.lastLoginTime != null">
            when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.lastLoginTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="LAST_LOGIN_IP = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.lastLoginIp != null">
            when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.lastLoginIp,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.addTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.modTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="STAFF = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.staff != null">
            when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.staff,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="`SYSTEM` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.system != null">
            when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.system,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="USER_BELONG_COMPANY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.userBelongCompanyId != null">
            when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.userBelongCompanyId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="TT_NUMBER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.ttNumber != null">
            when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.ttNumber,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ORG_IDS_LIST = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orgIdsList != null">
            when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.orgIdsList,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TELECOM_LINE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.telecomLine != null">
            when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.telecomLine,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOBILE_LINE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.mobileLine != null">
            when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.mobileLine,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UNICOM_LINE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.unicomLine != null">
            when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.unicomLine,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="CUSTOMER_LINE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.customerLine != null">
            when USER_ID = #{item.userId,jdbcType=INTEGER} then #{item.customerLine,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where USER_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.userId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="USER_ID" keyProperty="userId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_USER
    (COMPANY_ID, USERNAME, `NUMBER`, `PASSWORD`, SALT, PARENT_ID, IS_ADMIN, IS_DISABLED, 
      DISABLED_REASON, LAST_LOGIN_TIME, LAST_LOGIN_IP, ADD_TIME, CREATOR, MOD_TIME, UPDATER, 
      STAFF, `SYSTEM`, USER_BELONG_COMPANY_ID, TT_NUMBER, ORG_IDS_LIST, TELECOM_LINE, 
      MOBILE_LINE, UNICOM_LINE, CUSTOMER_LINE)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.companyId,jdbcType=INTEGER}, #{item.username,jdbcType=VARCHAR}, #{item.number,jdbcType=VARCHAR}, 
        #{item.password,jdbcType=VARCHAR}, #{item.salt,jdbcType=VARCHAR}, #{item.parentId,jdbcType=INTEGER}, 
        #{item.isAdmin,jdbcType=BOOLEAN}, #{item.isDisabled,jdbcType=BOOLEAN}, #{item.disabledReason,jdbcType=VARCHAR}, 
        #{item.lastLoginTime,jdbcType=BIGINT}, #{item.lastLoginIp,jdbcType=VARCHAR}, #{item.addTime,jdbcType=BIGINT}, 
        #{item.creator,jdbcType=INTEGER}, #{item.modTime,jdbcType=BIGINT}, #{item.updater,jdbcType=INTEGER}, 
        #{item.staff,jdbcType=BOOLEAN}, #{item.system,jdbcType=VARCHAR}, #{item.userBelongCompanyId,jdbcType=INTEGER}, 
        #{item.ttNumber,jdbcType=VARCHAR}, #{item.orgIdsList,jdbcType=VARCHAR}, #{item.telecomLine,jdbcType=VARCHAR}, 
        #{item.mobileLine,jdbcType=VARCHAR}, #{item.unicomLine,jdbcType=VARCHAR}, #{item.customerLine,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="USER_ID" keyProperty="userId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchUserDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_USER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        USER_ID,
      </if>
      COMPANY_ID,
      USERNAME,
      `NUMBER`,
      `PASSWORD`,
      SALT,
      PARENT_ID,
      IS_ADMIN,
      IS_DISABLED,
      DISABLED_REASON,
      LAST_LOGIN_TIME,
      LAST_LOGIN_IP,
      ADD_TIME,
      CREATOR,
      MOD_TIME,
      UPDATER,
      STAFF,
      `SYSTEM`,
      USER_BELONG_COMPANY_ID,
      TT_NUMBER,
      ORG_IDS_LIST,
      TELECOM_LINE,
      MOBILE_LINE,
      UNICOM_LINE,
      CUSTOMER_LINE,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      #{companyId,jdbcType=INTEGER},
      #{username,jdbcType=VARCHAR},
      #{number,jdbcType=VARCHAR},
      #{password,jdbcType=VARCHAR},
      #{salt,jdbcType=VARCHAR},
      #{parentId,jdbcType=INTEGER},
      #{isAdmin,jdbcType=BOOLEAN},
      #{isDisabled,jdbcType=BOOLEAN},
      #{disabledReason,jdbcType=VARCHAR},
      #{lastLoginTime,jdbcType=BIGINT},
      #{lastLoginIp,jdbcType=VARCHAR},
      #{addTime,jdbcType=BIGINT},
      #{creator,jdbcType=INTEGER},
      #{modTime,jdbcType=BIGINT},
      #{updater,jdbcType=INTEGER},
      #{staff,jdbcType=BOOLEAN},
      #{system,jdbcType=VARCHAR},
      #{userBelongCompanyId,jdbcType=INTEGER},
      #{ttNumber,jdbcType=VARCHAR},
      #{orgIdsList,jdbcType=VARCHAR},
      #{telecomLine,jdbcType=VARCHAR},
      #{mobileLine,jdbcType=VARCHAR},
      #{unicomLine,jdbcType=VARCHAR},
      #{customerLine,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      COMPANY_ID = #{companyId,jdbcType=INTEGER},
      USERNAME = #{username,jdbcType=VARCHAR},
      `NUMBER` = #{number,jdbcType=VARCHAR},
      `PASSWORD` = #{password,jdbcType=VARCHAR},
      SALT = #{salt,jdbcType=VARCHAR},
      PARENT_ID = #{parentId,jdbcType=INTEGER},
      IS_ADMIN = #{isAdmin,jdbcType=BOOLEAN},
      IS_DISABLED = #{isDisabled,jdbcType=BOOLEAN},
      DISABLED_REASON = #{disabledReason,jdbcType=VARCHAR},
      LAST_LOGIN_TIME = #{lastLoginTime,jdbcType=BIGINT},
      LAST_LOGIN_IP = #{lastLoginIp,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      STAFF = #{staff,jdbcType=BOOLEAN},
      `SYSTEM` = #{system,jdbcType=VARCHAR},
      USER_BELONG_COMPANY_ID = #{userBelongCompanyId,jdbcType=INTEGER},
      TT_NUMBER = #{ttNumber,jdbcType=VARCHAR},
      ORG_IDS_LIST = #{orgIdsList,jdbcType=VARCHAR},
      TELECOM_LINE = #{telecomLine,jdbcType=VARCHAR},
      MOBILE_LINE = #{mobileLine,jdbcType=VARCHAR},
      UNICOM_LINE = #{unicomLine,jdbcType=VARCHAR},
      CUSTOMER_LINE = #{customerLine,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="USER_ID" keyProperty="userId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchUserDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_USER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="username != null and username != ''">
        USERNAME,
      </if>
      <if test="number != null and number != ''">
        `NUMBER`,
      </if>
      <if test="password != null and password != ''">
        `PASSWORD`,
      </if>
      <if test="salt != null and salt != ''">
        SALT,
      </if>
      <if test="parentId != null">
        PARENT_ID,
      </if>
      <if test="isAdmin != null">
        IS_ADMIN,
      </if>
      <if test="isDisabled != null">
        IS_DISABLED,
      </if>
      <if test="disabledReason != null and disabledReason != ''">
        DISABLED_REASON,
      </if>
      <if test="lastLoginTime != null">
        LAST_LOGIN_TIME,
      </if>
      <if test="lastLoginIp != null and lastLoginIp != ''">
        LAST_LOGIN_IP,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="staff != null">
        STAFF,
      </if>
      <if test="system != null and system != ''">
        `SYSTEM`,
      </if>
      <if test="userBelongCompanyId != null">
        USER_BELONG_COMPANY_ID,
      </if>
      <if test="ttNumber != null and ttNumber != ''">
        TT_NUMBER,
      </if>
      <if test="orgIdsList != null and orgIdsList != ''">
        ORG_IDS_LIST,
      </if>
      <if test="telecomLine != null and telecomLine != ''">
        TELECOM_LINE,
      </if>
      <if test="mobileLine != null and mobileLine != ''">
        MOBILE_LINE,
      </if>
      <if test="unicomLine != null and unicomLine != ''">
        UNICOM_LINE,
      </if>
      <if test="customerLine != null and customerLine != ''">
        CUSTOMER_LINE,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="username != null and username != ''">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="number != null and number != ''">
        #{number,jdbcType=VARCHAR},
      </if>
      <if test="password != null and password != ''">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="salt != null and salt != ''">
        #{salt,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=INTEGER},
      </if>
      <if test="isAdmin != null">
        #{isAdmin,jdbcType=BOOLEAN},
      </if>
      <if test="isDisabled != null">
        #{isDisabled,jdbcType=BOOLEAN},
      </if>
      <if test="disabledReason != null and disabledReason != ''">
        #{disabledReason,jdbcType=VARCHAR},
      </if>
      <if test="lastLoginTime != null">
        #{lastLoginTime,jdbcType=BIGINT},
      </if>
      <if test="lastLoginIp != null and lastLoginIp != ''">
        #{lastLoginIp,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="staff != null">
        #{staff,jdbcType=BOOLEAN},
      </if>
      <if test="system != null and system != ''">
        #{system,jdbcType=VARCHAR},
      </if>
      <if test="userBelongCompanyId != null">
        #{userBelongCompanyId,jdbcType=INTEGER},
      </if>
      <if test="ttNumber != null and ttNumber != ''">
        #{ttNumber,jdbcType=VARCHAR},
      </if>
      <if test="orgIdsList != null and orgIdsList != ''">
        #{orgIdsList,jdbcType=VARCHAR},
      </if>
      <if test="telecomLine != null and telecomLine != ''">
        #{telecomLine,jdbcType=VARCHAR},
      </if>
      <if test="mobileLine != null and mobileLine != ''">
        #{mobileLine,jdbcType=VARCHAR},
      </if>
      <if test="unicomLine != null and unicomLine != ''">
        #{unicomLine,jdbcType=VARCHAR},
      </if>
      <if test="customerLine != null and customerLine != ''">
        #{customerLine,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="username != null and username != ''">
        USERNAME = #{username,jdbcType=VARCHAR},
      </if>
      <if test="number != null and number != ''">
        `NUMBER` = #{number,jdbcType=VARCHAR},
      </if>
      <if test="password != null and password != ''">
        `PASSWORD` = #{password,jdbcType=VARCHAR},
      </if>
      <if test="salt != null and salt != ''">
        SALT = #{salt,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        PARENT_ID = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="isAdmin != null">
        IS_ADMIN = #{isAdmin,jdbcType=BOOLEAN},
      </if>
      <if test="isDisabled != null">
        IS_DISABLED = #{isDisabled,jdbcType=BOOLEAN},
      </if>
      <if test="disabledReason != null and disabledReason != ''">
        DISABLED_REASON = #{disabledReason,jdbcType=VARCHAR},
      </if>
      <if test="lastLoginTime != null">
        LAST_LOGIN_TIME = #{lastLoginTime,jdbcType=BIGINT},
      </if>
      <if test="lastLoginIp != null and lastLoginIp != ''">
        LAST_LOGIN_IP = #{lastLoginIp,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="staff != null">
        STAFF = #{staff,jdbcType=BOOLEAN},
      </if>
      <if test="system != null and system != ''">
        `SYSTEM` = #{system,jdbcType=VARCHAR},
      </if>
      <if test="userBelongCompanyId != null">
        USER_BELONG_COMPANY_ID = #{userBelongCompanyId,jdbcType=INTEGER},
      </if>
      <if test="ttNumber != null and ttNumber != ''">
        TT_NUMBER = #{ttNumber,jdbcType=VARCHAR},
      </if>
      <if test="orgIdsList != null and orgIdsList != ''">
        ORG_IDS_LIST = #{orgIdsList,jdbcType=VARCHAR},
      </if>
      <if test="telecomLine != null and telecomLine != ''">
        TELECOM_LINE = #{telecomLine,jdbcType=VARCHAR},
      </if>
      <if test="mobileLine != null and mobileLine != ''">
        MOBILE_LINE = #{mobileLine,jdbcType=VARCHAR},
      </if>
      <if test="unicomLine != null and unicomLine != ''">
        UNICOM_LINE = #{unicomLine,jdbcType=VARCHAR},
      </if>
      <if test="customerLine != null and customerLine != ''">
        CUSTOMER_LINE = #{customerLine,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

<!--auto generated by MybatisCodeHelper on 2023-05-06-->
  <select id="findUsernameByUserId" resultType="java.lang.String">
        select USERNAME
        from T_USER
        where USER_ID=#{userId,jdbcType=INTEGER}
    </select>

  <select id="getUserInfoByTraderId" resultMap="BaseResultMap">
    select
    b.USER_ID,b.USERNAME,d.ORG_ID,e.ORG_NAME
    from
    T_R_TRADER_J_USER a
    left join
    T_USER b
    on
    a.USER_ID = b.USER_ID
    left join
    T_R_USER_POSIT c
    on
    b.USER_ID = c.USER_ID
    left join
    T_POSITION d
    on
    d.POSITION_ID = c.POSITION_ID
    left join
    T_ORGANIZATION e
    on
    d.ORG_ID = e.ORG_ID
    where
    a.TRADER_ID = #{traderId}
    and
    a.TRADER_TYPE = #{traderType}
    limit 1
  </select>
</mapper>