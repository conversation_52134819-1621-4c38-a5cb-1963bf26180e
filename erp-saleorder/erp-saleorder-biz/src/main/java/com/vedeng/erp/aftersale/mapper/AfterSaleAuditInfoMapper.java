package com.vedeng.erp.aftersale.mapper;
import org.apache.ibatis.annotations.Param;

import com.vedeng.erp.aftersale.domain.entity.AfterSaleAuditInfoEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AfterSaleAuditInfoMapper {
    /**
     * delete by primary key
     * @param afterSaleAuditInfoId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer afterSaleAuditInfoId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(AfterSaleAuditInfoEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(AfterSaleAuditInfoEntity record);

    /**
     * select by primary key
     * @param afterSaleAuditInfoId primary key
     * @return object by primary key
     */
    AfterSaleAuditInfoEntity selectByPrimaryKey(Integer afterSaleAuditInfoId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(AfterSaleAuditInfoEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(AfterSaleAuditInfoEntity record);

    List<AfterSaleAuditInfoEntity> findAuditListByAfterSaleorderId(AfterSaleAuditInfoEntity entity);

    /**
     * 根据售后单id查询
     * @param afterSaleId 售后单id
     * @return List<AfterSaleAuditInfoEntity>
     */
    List<AfterSaleAuditInfoEntity> selectByAfterSaleId(@Param("afterSaleId")Integer afterSaleId);



    List<String> getCannotReturnSkuByAfterSalesId(@Param("afterSaleId")Integer afterSaleId);

    void clearAuditInfoByAfterSaleId(@Param("afterSaleId") Integer afterSaleId);
}