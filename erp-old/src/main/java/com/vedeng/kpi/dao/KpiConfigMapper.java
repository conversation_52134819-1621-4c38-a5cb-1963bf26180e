package com.vedeng.kpi.dao;

import com.vedeng.kpi.model.DTO.KpiConfigQueryDto;
import com.vedeng.kpi.model.DTO.KpiUserConfigDto;
import com.vedeng.kpi.model.DTO.KpiGroupConfigDto;

import java.util.List;

public interface KpiConfigMapper {

    List<Integer> getUserIds();

    List<Integer> getGroupIds();

    List<KpiUserConfigDto> getUserOrganization();

    List<KpiUserConfigDto> getBatchUserConfig(KpiConfigQueryDto query);

    List<KpiGroupConfigDto> getBatchGroupConfig(KpiConfigQueryDto query);

    KpiGroupConfigDto getGroupConfig(KpiConfigQueryDto query);

    KpiUserConfigDto getUserConfig(KpiConfigQueryDto query);

    List<KpiUserConfigDto> getHisMonthTarget();
}
