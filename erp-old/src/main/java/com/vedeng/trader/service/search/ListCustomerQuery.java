package com.vedeng.trader.service.search;

import lombok.Data;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Data
public class ListCustomerQuery {

    public static final List<Integer> NOT_FOUND_SENTINEL = Collections.singletonList(-1);

    private String searchMsg;//信息搜索 ：沟通记录内容，商机的询价产品


    private String customerName;
    
    private Integer lockStatus;

    /**
     * 客户类型 426科研医疗 427临床医疗 (字典库)
     */
    private Integer customerType;

    /**
     * 客户性质 465分销 466终端 （字典库）
     */
    private Integer customerNature;

    private Collection<Integer> traderIdList;

    private Integer source;

    private Integer aptitudeStatus;

    private Integer customerStatus;

    private Integer financeCheckStatus;

    private Integer isLimitedPrice;

    /**
     * 客户分类，0为未交易客户，1为新客户,2为流失客户，3为留存客户
     */
    private Integer customerCategory;

    /**
     * 所属区域id
     */
    private Integer areaId;

    private Integer smallScore;// 页面搜索条件，客户得分小分数
    private Integer bigScore;// 页面搜索条件，客户得分大分数

    private Integer customerLevel;//客户等级

    private Integer userEvaluate;

    private Integer customerScore;//客户得分

    private Integer basicMedicalDealer;

    private Integer timeType;// 页面搜索时间：1、创建时间；2、交易时间3、更新时间

    private Long startTime;// 开始时间，用于页面搜索
    private Long endTime;// 结束时间，用于页面搜索


    private Integer customerAlert; //客户提醒

    /**
     * 公私海规则下的TraderId集合
     */
    private List<Integer> publicTraderCustomerIdList;

    /**
     * 公海列表原归属销售
     */
    private Integer originUserId;

    /**
     * 公海 挖掘价值条件类型
     */
    private Integer conditionType;
    /**
     * 分类属性
     */
    private List<Integer> categoryList;

    private List<Integer> userIdList;

    private Integer platformNo;

    private Integer hasQuoted;

    private Integer isCooperated;

    private List<Integer> capitalBillTraderIdList;//交易流水中的客户主键值

    private List<Integer> wxTraderIdList;//添加微信的客户

    private List<Integer> parenterTraderIdList;//战略合作伙伴客户

    private List<Integer> searchTraderGroupList;

    //经销商有效性
    private Integer effectiveness;
    //终端机构性质
    private String institutionNature;
    //终端机构评级
    private List<Integer> institutionLevel;
    //主营商品范畴
    private List<Integer> traderCustomerMainCategory;
    //主营商品范畴类别
    private String traderCustomerMainCategoryType;
    //主营商品类型
    private Integer skuType;
    //销售类别
    private String traderCustomerOwnership;
    //核心资源
    private Integer traderCustomerDevelopLevel;
    //客户等级
    private String traderCustomerLevelGrade;
    //生命周期
    private String traderCustomerLifeCycle;
    //交易分类
    private List<Integer> traderCustomerTrade;

    //交易分类类别
    private Integer traderCustomerTradeType;
    //交易品牌
    private List<Integer> traderCustomerBrand;
    //交易品牌类型
    private Integer traderCustomerBrandType;
    //沟通记录
    private Integer traderCustomerCommunicate;
    //历史沟通次数
    private Integer traderCustomerCommunicateTimes;
    //有无手机
    private Integer traderCustomerIsMobile;
    //是否公海
    private Integer traderCustomerIsSea;
    //贝登会员
    private Integer traderCustomerIsMember;
    //地区类型
    private Integer areaType;

    /**
     * 原归属销售
     */
    private List<Integer> originUserList;

    /**
     * 营销客户类型
     */
    private List<String> traderCustomerMarketingTypeList = new ArrayList<>();

    /**
     * 机构类型
     */
    private List<String> institutionTypeList = new ArrayList<>();

    /**
     * 机构类型子集
     */
    private List<String> institutionTypeChildList = new ArrayList<>();

    // 标签完善度 类型
    private Integer selectLabelPerfectionValue;
    // 标签完善度 多选
    private List<Integer> selectLabelPerfectionChildValue;
    // 标签完善度
    private Integer selectLabelPerfectionChildValueOne;
    // 人工更新标签
    private Integer manualUpdate;

    private String customerSearchOrderBy = "Y";  //给一个默认值

    /**
     * T_TRADER_CUSTOMER_MARKETING_PRINCIPAL 代理品牌id集合
     */
    private List<Integer> agencyBrandIdList;

    /**
     * T_TRADER_CUSTOMER_MARKETING_PRINCIPAL 其他代理品牌
     */
    private String otherAgencyBrand;

    /**
     * T_TRADER_CUSTOMER_MARKETING_PRINCIPAL 代理商品
     */
    private List<Integer> agencySkuList;

    /**
     * T_TRADER_CUSTOMER_MARKETING_PRINCIPAL 其他代理商品
     */
    private String otherAgencySku;

    /**
     * T_TRADER_CUSTOMER_MARKETING_PRINCIPAL 政府关系
     */
    private String governmentRelation;

    /**
     * T_TRADER_CUSTOMER_MARKETING_PRINCIPAL 其他政府关系
     */
    private String otherGovernmentRelation;

    /**
     * 分享的销售id
     */
    private Integer sharedSaleId;

    /**全部标签完善度评分最低值*/
    private Integer selectLabelPerfectionMin;

    /**全部标签完善度评分最高值*/
    private Integer selectLabelPerfectionMax;

    /**
     * 查询对象
     */
    private List<Integer> queryGongHaiLevelList;

    /**
     * 查询对象
     */
    private List<Integer> queryInvalidReasonList;
}
