<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.CommunicateVoiceAiLogMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.CommunicateVoiceAiLogEntity">
    <!--@mbg.generated-->
    <!--@Table T_COMMUNICATE_VOICE_AI_LOG-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="COMMUNICATE_RECORD_ID" jdbcType="INTEGER" property="communicateRecordId" />
    <result column="SENCE_CODE" jdbcType="VARCHAR" property="senceCode" />
    <result column="GROUP_CODE" jdbcType="VARCHAR" property="groupCode" />
    <result column="GPT_VERSION" jdbcType="VARCHAR" property="gptVersion" />
    <result column="START_TIME" jdbcType="TIMESTAMP" property="startTime" />
    <result column="END_TIME" jdbcType="TIMESTAMP" property="endTime" />
    <result column="REQUEST_TEXT" jdbcType="VARCHAR" property="requestText" />
    <result column="RESPONSE_TEXT" jdbcType="VARCHAR" property="responseText" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, COMMUNICATE_RECORD_ID, SENCE_CODE, GROUP_CODE, GPT_VERSION, START_TIME, END_TIME, 
    REQUEST_TEXT, RESPONSE_TEXT, CREATOR, ADD_TIME, MOD_TIME
  </sql>


  <select id="selectByCommunicateAndSenceCodeGroupCode"  resultType="com.vedeng.erp.trader.dto.CommunicateVoiceAiLogApiDto">
    <!--@mbg.generated-->
    select
    *
    from T_COMMUNICATE_VOICE_AI_LOG
    where COMMUNICATE_RECORD_ID = #{communicateRecordId,jdbcType=INTEGER}
    and SENCE_CODE = #{senceCode,jdbcType=VARCHAR}
    and GROUP_CODE = #{groupCode,jdbcType=VARCHAR}
    AND RESPONSE_TEXT IS NOT NULL
    ORDER BY ID DESC
    LIMIT 1
  </select>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_COMMUNICATE_VOICE_AI_LOG
    where ID = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_COMMUNICATE_VOICE_AI_LOG
    where ID = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.trader.domain.entity.CommunicateVoiceAiLogEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_COMMUNICATE_VOICE_AI_LOG (COMMUNICATE_RECORD_ID, SENCE_CODE, GROUP_CODE, 
      GPT_VERSION, START_TIME, END_TIME, 
      REQUEST_TEXT, RESPONSE_TEXT, CREATOR, 
      ADD_TIME, MOD_TIME)
    values (#{communicateRecordId,jdbcType=INTEGER}, #{senceCode,jdbcType=VARCHAR}, #{groupCode,jdbcType=VARCHAR}, 
      #{gptVersion,jdbcType=VARCHAR}, #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, 
      #{requestText,jdbcType=VARCHAR}, #{responseText,jdbcType=VARCHAR}, #{creator,jdbcType=INTEGER}, 
      #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.trader.domain.entity.CommunicateVoiceAiLogEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_COMMUNICATE_VOICE_AI_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="communicateRecordId != null">
        COMMUNICATE_RECORD_ID,
      </if>
      <if test="senceCode != null and senceCode != ''">
        SENCE_CODE,
      </if>
      <if test="groupCode != null and groupCode != ''">
        GROUP_CODE,
      </if>
      <if test="gptVersion != null and gptVersion != ''">
        GPT_VERSION,
      </if>
      <if test="startTime != null">
        START_TIME,
      </if>
      <if test="endTime != null">
        END_TIME,
      </if>
      <if test="requestText != null and requestText != ''">
        REQUEST_TEXT,
      </if>
      <if test="responseText != null and responseText != ''">
        RESPONSE_TEXT,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="communicateRecordId != null">
        #{communicateRecordId,jdbcType=INTEGER},
      </if>
      <if test="senceCode != null and senceCode != ''">
        #{senceCode,jdbcType=VARCHAR},
      </if>
      <if test="groupCode != null and groupCode != ''">
        #{groupCode,jdbcType=VARCHAR},
      </if>
      <if test="gptVersion != null and gptVersion != ''">
        #{gptVersion,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="requestText != null and requestText != ''">
        #{requestText,jdbcType=VARCHAR},
      </if>
      <if test="responseText != null and responseText != ''">
        #{responseText,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.CommunicateVoiceAiLogEntity">
    <!--@mbg.generated-->
    update T_COMMUNICATE_VOICE_AI_LOG
    <set>
      <if test="communicateRecordId != null">
        COMMUNICATE_RECORD_ID = #{communicateRecordId,jdbcType=INTEGER},
      </if>
      <if test="senceCode != null and senceCode != ''">
        SENCE_CODE = #{senceCode,jdbcType=VARCHAR},
      </if>
      <if test="groupCode != null and groupCode != ''">
        GROUP_CODE = #{groupCode,jdbcType=VARCHAR},
      </if>
      <if test="gptVersion != null and gptVersion != ''">
        GPT_VERSION = #{gptVersion,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        START_TIME = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        END_TIME = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="requestText != null and requestText != ''">
        REQUEST_TEXT = #{requestText,jdbcType=VARCHAR},
      </if>
      <if test="responseText != null and responseText != ''">
        RESPONSE_TEXT = #{responseText,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.CommunicateVoiceAiLogEntity">
    <!--@mbg.generated-->
    update T_COMMUNICATE_VOICE_AI_LOG
    set COMMUNICATE_RECORD_ID = #{communicateRecordId,jdbcType=INTEGER},
      SENCE_CODE = #{senceCode,jdbcType=VARCHAR},
      GROUP_CODE = #{groupCode,jdbcType=VARCHAR},
      GPT_VERSION = #{gptVersion,jdbcType=VARCHAR},
      START_TIME = #{startTime,jdbcType=TIMESTAMP},
      END_TIME = #{endTime,jdbcType=TIMESTAMP},
      REQUEST_TEXT = #{requestText,jdbcType=VARCHAR},
      RESPONSE_TEXT = #{responseText,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=BIGINT}
  </update>
</mapper>