package com.vedeng.flash.service.message;

import com.vedeng.flash.dto.SendMessageDto;
import com.vedeng.flash.model.EarlyWarningTask;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 催货任务创建
 */
@Service
public class ExpeditingTaskCreateMessageSender extends AbstractMessageSender{

    @Override
    protected String getUrl(SendMessageDto sendMessageDto) {
        return null;
    }

    @Override
    protected List<Integer> getMessageUserIdList(SendMessageDto sendMessageDto) {
        return null;
    }

    @Override
    protected Integer getMessageTemplateId() {
        return null;
    }

    @Override
    protected Map getTemplateVariableMap(SendMessageDto sendMessageDto) {
        return null;
    }
}
