package com.vedeng.infrastructure.bank.api.domain;

import cn.hutool.core.util.StrUtil;
import com.vedeng.infrastructure.bank.api.domain.base.BankResponse;
import lombok.*;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 融资预申请请求体
 * @date 2024/10/12 11:26
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class B2eBasicDraftApplyReq {

    /**
     * 客户技术请求流水号
     */
    private String trnId;

    /**
     * 外部融资申请编号，请勿重复
     */
    private String outApplicationNo;

    /**
     * 产品编码
     * ******** 基础银承
     * ******** 票据管家
     */
    private String productCode;

    /**
     * 是否可分包(1-可分包 0-不可分包)
     */
    private String isAllowSplitBill;

    /**
     * 授信类型
     * 01-综合授信
     * 02-单笔授信(含低风险业务)
     */
    private String creditType;

    /**
     * 融资金额
     */
    private BigDecimal financingAmount;

    /**
     * 出账起始日 yyyy-MM-dd 当天
     */
    private String validFrom;

    /**
     * 出账到期日 yyyy-MM-dd
     */
    private String validTo;

    /**
     * 公司客户联系人手机号码，短信业务通知使用
     */
    private String custPhone;

    /**
     * 保证金账户口类型
     * 01-活期 02-定期
     */
    private String accountType;

    /**
     * 还款账号
     * (银承客户协议查询接口查询)
     */
    private String repayAccountNo;

    /**
     * 手续费扣费账号
     * (银承客户协议查询接口查询)
     */
    private String feeAccountNo;

    /**
     * 协议双方约定事项
     */
    private String bothAgreedMatter;

    /**
     * 银承中收减免审批单号
     * (减免审批单接口查询)
     */
    private String taskNo;

    /**
     * 是否从扣款账号中扣划至保证金账户0-否，1-是
     * 保证金类型为活期时，必输，只支持划扣至活期保证金账户，定期保证金账户则该字段无需送值
     */
    private String isDeduct;

    /**
     * 扣款账号
     * 选择是从扣款账号扣款之后，选择的扣款账户，划扣至活期保证金账户
     * (银承客户协议查询接口查询)
     */
    private String deductAccountNo;

    /**
     * 是否提示收票 0 否 1 是
     */
    private String issuanceFlag;

    /**
     * 是否合并票面信息 0 否 1 是
     * 如果需要合并票面，则会将收款人名称、开户行行名、收款账号相同的支付信息合并成一条票面信息，金额累加作为新的票面金额
     */
    private String isAutoDelPayeeInfo;

    /**
     * 电票签约账号
     * (银承客户协议查询接口查询)
     */
    private String issueAccountNo;

    /**
     * 是否可背书转让
     * EM00 可转让 EM01 不可转让
     */
    private String endoFlag;

    /**
     * 支付信息和贸易合同信息
     */
    @XmlElementWrapper(name = "counterPartyList")
    @XmlElement(name = "Map")
    private List<CounterParty> counterPartyList = new ArrayList<>();

    /**
     * 出票保证金账号列表
     */
    @XmlElementWrapper(name = "bailInfoList")
    @XmlElement(name = "Map")
    private List<BailInfo> bailInfoList;

    /**
     * 融资预申请或融资申请提交
     */
    @XmlElementWrapper(name = "fileListInfo")
    @XmlElement(name = "Map")
    private List<FileInfo> fileListInfo = new ArrayList<>();


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class FileInfo {
        /**
         * 文件类型
         * 04 贸易合同附件
         * 135 增值税纳税申报表附件(低风险自动授信)
         * 136 审计报告附件(低风险自动授信)
         * 139 担保资金证明材料附件
         * 140 其他材料附件
         */
        private String fileType;

        /**
         * 附件文件名
         */
        private String fileName;

        /**
         * 文件内容
         */
        private String fileContent;

        /**
         * 影像 id
         */
        private String imageId;

        /**
         * 系统编号(上送 734_354)
         */
        private String sysNo;
    }


    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class BailInfo {
        /**
         * 账号类型
         * 01 活期 02 定期
         */
        private String accountType;

        /**
         * 保证金账号
         */
        private String bailAccountNo;

        /**
         * 开票保证金金额
         */
        private BigDecimal addAmount;
    }

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class CounterParty {
        /**
         * 交易对手名称
         */
        private String partyName;

        /**
         * 贸易合同号(非一户一策白名单，必输，具体请咨询客户经理)
         */
        private String tradeContractNo;

        /**
         * 贸易合同金额(非一户一策白名单，必输，具体请咨询客户经理)
         */
        private BigDecimal tradeContractAmount;

        /**
         * 贸易合同签署日 yyyy-MM-dd(非一户一策白名单，必输，具体请咨询客户经理)
         */
        private String tradeValidFrom;

        /**
         * 贸易合同到期日 yyyy-MM-dd(非一策一户白名单，且 isValid 为 0（贸易合同非长期有效）则必输，具体请咨询客户经理)
         */
        private String tradeValidTo;

        /**
         * 贸易合同付款期限是否长期有效,0 否1
         * 是 当为 1 时 贸易合同到期日将被忽略(非一户一策白名单，必输，具体请咨询客户经理)
         */
        private String isValid;

        /**
         * 是否本行 0 否 1 是
         */
        private String isSelfBank;

        /**
         * 收款人开户行行号 大额行号
         */
        private String depositBankNo;

        /**
         * 收款人开户行名称
         */
        private String depositBankName;

        /**
         * 收款人账号
         */
        private String payeeAccountNo;

        /**
         * 票面金额(本次支付金额/本次出账金额)
         */
        private BigDecimal paymentAmount;

        /**
         * 票面备注
         */
        private String remark;
    }

    /**
     * 校验必填参数
     *
     * @param response response
     * @return true 如果必填参数有效，false 如果缺失
     */
    public boolean validate(BankResponse response) {
        if (StrUtil.isBlank(trnId)) {
            response.checkParamResponseHeader(response, "trnId不能为空");
            return false;
        }
        if (StrUtil.isBlank(outApplicationNo)) {
            response.checkParamResponseHeader(response, "outApplicationNo不能为空");
            return false;
        }
        if (StrUtil.isBlank(productCode)) {
            response.checkParamResponseHeader(response, "productCode不能为空");
            return false;
        }
        if (StrUtil.isBlank(isAllowSplitBill)) {
            response.checkParamResponseHeader(response, "isAllowSplitBill不能为空");
            return false;
        }
        if (StrUtil.isBlank(creditType)) {
            response.checkParamResponseHeader(response, "creditType不能为空");
            return false;
        }
        if (financingAmount == null) {
            response.checkParamResponseHeader(response, "financingAmount不能为空");
            return false;
        }
        if (StrUtil.isBlank(validFrom)) {
            response.checkParamResponseHeader(response, "validFrom不能为空");
            return false;
        }
        if (StrUtil.isBlank(validTo)) {
            response.checkParamResponseHeader(response, "validTo不能为空");
            return false;
        }
        if (StrUtil.isBlank(custPhone)) {
            response.checkParamResponseHeader(response, "custPhone不能为空");
            return false;
        }
        if (StrUtil.isBlank(issuanceFlag)) {
            response.checkParamResponseHeader(response, "issuanceFlag不能为空");
            return false;
        }
        if (StrUtil.isBlank(isAutoDelPayeeInfo)) {
            response.checkParamResponseHeader(response, "isAutoDelPayeeInfo不能为空");
            return false;
        }
        if (StrUtil.isBlank(endoFlag)) {
            response.checkParamResponseHeader(response, "endoFlag不能为空");
            return false;
        }

        // 校验 counterPartyList 是否为空
        if (counterPartyList == null || counterPartyList.isEmpty()) {
            response.checkParamResponseHeader(response, "counterPartyList不能为空");
            return false;
        }

        // 校验 counterPartyList 中的每个元素的属性
        for (CounterParty counterParty : counterPartyList) {
            if (StrUtil.isBlank(counterParty.getPartyName())) {
                response.checkParamResponseHeader(response, "partyName不能为空");
                return false;
            }
            if (StrUtil.isBlank(counterParty.getIsSelfBank())) {
                response.checkParamResponseHeader(response, "isSelfBank不能为空");
                return false;
            }
            if (StrUtil.isBlank(counterParty.getDepositBankNo())) {
                response.checkParamResponseHeader(response, "depositBankNo不能为空");
                return false;
            }
            if (StrUtil.isBlank(counterParty.getDepositBankName())) {
                response.checkParamResponseHeader(response, "depositBankName不能为空");
                return false;
            }
            if (StrUtil.isBlank(counterParty.getPayeeAccountNo())) {
                response.checkParamResponseHeader(response, "payeeAccountNo不能为空");
                return false;
            }
            if (counterParty.getPaymentAmount() == null) {
                response.checkParamResponseHeader(response, "paymentAmount不能为空");
                return false;
            }
        }

        if(Objects.equals(creditType,"1")){
            // 校验 bailInfoList 是否为空
            if (bailInfoList == null || bailInfoList.isEmpty()) {
                response.checkParamResponseHeader(response, "bailInfoList不能为空");
                return false;
            }

            // 校验 bailInfoList 中的每个元素的属性
            for (BailInfo bailInfo : bailInfoList) {
                if (StrUtil.isBlank(bailInfo.getAccountType())) {
                    response.checkParamResponseHeader(response, "accountType不能为空");
                    return false;
                }
                if (bailInfo.getAddAmount() == null) {
                    response.checkParamResponseHeader(response, "addAmount不能为空");
                    return false;
                }
            }
        }

        return true;
    }
}