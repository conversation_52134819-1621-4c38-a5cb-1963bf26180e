package com.vedeng.erp.finance.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 关联的售后明细
 */
@Getter
@Setter
public class AssociateAfterSaleDetailDto {

    /**
     * 序号 (表示蓝票序号)
     */
    private Integer xh;

    /**
     * 项目名称
     */
    private String xmmc;

    /**
     * 规格型号
     */
    private String ggxh;

    /**
     * 单位
     */
    private String dw;

    /**
     * 税收分类编码
     */
    private String sphfwssflhbbm;

    /**
     * 售后数量
     */
    private BigDecimal num;

    /**
     * 售后金额
     */
    private BigDecimal shje;

    /**
     * 售后价税合计
     */
    private BigDecimal shjshj;

    /**
     * 匹配度
     */
    private Integer matchingDegree = 0;

    /**
     * 销售商品id
     */
    private Integer saleOrderGoodsId;

    /**
     * 售后商品id
     */
    private Integer afterSaleGoodsId;

    /**
     * 蓝票明细ID
     */
    private Integer blueInvoiceItemId;

    /**
     * 售后类型
     */
    private Integer afterSaletype;
}