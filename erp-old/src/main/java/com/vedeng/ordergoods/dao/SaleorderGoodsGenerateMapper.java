package com.vedeng.ordergoods.dao;

import com.vedeng.ordergoods.model.SaleorderGoodsGenerate;
import com.vedeng.ordergoods.model.SaleorderGoodsGenerateExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SaleorderGoodsGenerateMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_GOODS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    int countByExample(SaleorderGoodsGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_GOODS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    //int deleteByExample(SaleorderGoodsGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_GOODS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    //int deleteByPrimaryKey(Integer saleorderGoodsId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_GOODS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    int insert(SaleorderGoodsGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_GOODS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
     int insertSelective(SaleorderGoodsGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_GOODS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    List<SaleorderGoodsGenerate> selectByExample(SaleorderGoodsGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_GOODS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    SaleorderGoodsGenerate selectByPrimaryKey(Integer saleorderGoodsId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_GOODS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
   // int updateByExampleSelective(@Param("record") SaleorderGoodsGenerate record, @Param("example") SaleorderGoodsGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_GOODS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
   // int updateByExample(@Param("record") SaleorderGoodsGenerate record, @Param("example") SaleorderGoodsGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_GOODS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
   // int updateByPrimaryKeySelective(SaleorderGoodsGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_GOODS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    //int updateByPrimaryKey(SaleorderGoodsGenerate record);
}