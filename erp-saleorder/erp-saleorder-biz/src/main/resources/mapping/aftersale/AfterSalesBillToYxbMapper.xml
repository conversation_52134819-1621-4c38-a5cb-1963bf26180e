<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.aftersale.mapper.AfterSalesBillToYxbMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.aftersale.domain.entity.AfterSalesBillToYxbEntity">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="AFTER_SALES_BILL_TO_YXB_ID" jdbcType="INTEGER" property="afterSalesBillToYxbId" />
    <result column="CAPITAL_BILL_NO" jdbcType="VARCHAR" property="capitalBillNo" />
    <result column="CAPITAL_BILL_ID" jdbcType="INTEGER" property="capitalBillId" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="TRANSACTION_MODE" jdbcType="VARCHAR" property="transactionMode" />
    <result column="TRADER_SUBJECT" jdbcType="INTEGER" property="traderSubject" />
    <result column="RESULT" jdbcType="CHAR" property="result" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="PAYER" jdbcType="VARCHAR" property="payer" />
    <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
    <result column="AFTER_SALES_ORDER_ID" jdbcType="INTEGER" property="afterSalesOrderId" />
    <result column="AFTER_SALES_ORDER_NO" jdbcType="VARCHAR" property="afterSalesOrderNo" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    delete from T_AFTER_SALES_BILL_TO_YXB
    where AFTER_SALES_BILL_TO_YXB_ID = #{afterSalesBillToYxbId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesBillToYxbEntity">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    insert into T_AFTER_SALES_BILL_TO_YXB (AFTER_SALES_BILL_TO_YXB_ID, CAPITAL_BILL_NO, 
      CAPITAL_BILL_ID, AMOUNT, TRANSACTION_MODE, 
      TRADER_SUBJECT, `RESULT`, IS_DELETE, 
      PAYER, COMMENTS, AFTER_SALES_ORDER_ID, 
      AFTER_SALES_ORDER_NO, ADD_TIME, MOD_TIME, 
      CREATOR, UPDATER, CREATOR_NAME, 
      UPDATER_NAME)
    values (#{afterSalesBillToYxbId,jdbcType=INTEGER}, #{capitalBillNo,jdbcType=VARCHAR}, 
      #{capitalBillId,jdbcType=INTEGER}, #{amount,jdbcType=DECIMAL}, #{transactionMode,jdbcType=VARCHAR}, 
      #{traderSubject,jdbcType=INTEGER}, #{result,jdbcType=CHAR}, #{isDelete,jdbcType=INTEGER}, 
      #{payer,jdbcType=VARCHAR}, #{comments,jdbcType=VARCHAR}, #{afterSalesOrderId,jdbcType=INTEGER}, 
      #{afterSalesOrderNo,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{updaterName,jdbcType=VARCHAR})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesBillToYxbEntity">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    update T_AFTER_SALES_BILL_TO_YXB
    set CAPITAL_BILL_NO = #{capitalBillNo,jdbcType=VARCHAR},
      CAPITAL_BILL_ID = #{capitalBillId,jdbcType=INTEGER},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      TRANSACTION_MODE = #{transactionMode,jdbcType=VARCHAR},
      TRADER_SUBJECT = #{traderSubject,jdbcType=INTEGER},
      `RESULT` = #{result,jdbcType=CHAR},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      PAYER = #{payer,jdbcType=VARCHAR},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      AFTER_SALES_ORDER_ID = #{afterSalesOrderId,jdbcType=INTEGER},
      AFTER_SALES_ORDER_NO = #{afterSalesOrderNo,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR}
    where AFTER_SALES_BILL_TO_YXB_ID = #{afterSalesBillToYxbId,jdbcType=INTEGER}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    select AFTER_SALES_BILL_TO_YXB_ID, CAPITAL_BILL_NO, CAPITAL_BILL_ID, AMOUNT, TRANSACTION_MODE, 
    TRADER_SUBJECT, `RESULT`, IS_DELETE, PAYER, COMMENTS, AFTER_SALES_ORDER_ID, AFTER_SALES_ORDER_NO, 
    ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME
    from T_AFTER_SALES_BILL_TO_YXB
    where AFTER_SALES_BILL_TO_YXB_ID = #{afterSalesBillToYxbId,jdbcType=INTEGER}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    select AFTER_SALES_BILL_TO_YXB_ID, CAPITAL_BILL_NO, CAPITAL_BILL_ID, AMOUNT, TRANSACTION_MODE, 
    TRADER_SUBJECT, `RESULT`, IS_DELETE, PAYER, COMMENTS, AFTER_SALES_ORDER_ID, AFTER_SALES_ORDER_NO, 
    ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME
    from T_AFTER_SALES_BILL_TO_YXB
  </select>
    <select id="selectByCondition"
            resultType="com.vedeng.erp.aftersale.domain.entity.AfterSalesBillToYxbEntity">
        select
            AFTER_SALES_BILL_TO_YXB_ID, CAPITAL_BILL_NO, CAPITAL_BILL_ID, AMOUNT, TRANSACTION_MODE,
            TRADER_SUBJECT, `RESULT`, IS_DELETE, PAYER, COMMENTS, AFTER_SALES_ORDER_ID, AFTER_SALES_ORDER_NO,
            ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME
        from T_AFTER_SALES_BILL_TO_YXB
        where 1 = 1
        <if test="afterSalesOrderId != null">
            and AFTER_SALES_ORDER_ID = #{afterSalesOrderId}
        </if>
        <if test="afterSalesOrderNo != null">
            and AFTER_SALES_ORDER_NO = #{afterSalesOrderNo}
        </if>
        <if test="capitalBillId != null">
            and CAPITAL_BILL_ID = #{capitalBillId}
        </if>
        <if test="capitalBillNo != null">
            and CAPITAL_BILL_NO = #{capitalBillNo}
        </if>
        <if test="amount != null">
            and AMOUNT = #{amount}
        </if>
        <if test="transactionMode != null">
            and TRANSACTION_MODE = #{transactionMode}
        </if>
        <if test="traderSubject != null">
            and TRADER_SUBJECT = #{traderSubject}
        </if>
        <if test="result != null">
            and `RESULT` = #{result}
        </if>
        <if test="isDelete != null">
            and IS_DELETE = #{isDelete}
        </if>
        <if test="payer != null">
            and PAYER = #{payer}
        </if>
        <if test="comments != null">
            and COMMENTS = #{comments}
        </if>
    </select>
</mapper>