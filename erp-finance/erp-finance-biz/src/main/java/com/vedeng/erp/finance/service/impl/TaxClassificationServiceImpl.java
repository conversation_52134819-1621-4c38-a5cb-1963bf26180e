package com.vedeng.erp.finance.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.finance.domain.entity.TaxClassificationEntity;
import com.vedeng.erp.finance.domain.entity.TaxcodeClassificationEntity;
import com.vedeng.erp.finance.dto.TaxClassificationDto;
import com.vedeng.erp.finance.dto.TaxcodeClassificationDto;
import com.vedeng.erp.finance.mapper.TaxClassificationMapper;
import com.vedeng.erp.finance.mapper.TaxcodeClassificationEntityMapper;
import com.vedeng.erp.finance.mapstruct.TaxClassificationConvertor;
import com.vedeng.erp.finance.mapstruct.TaxcodeClassificationConvertor;
import com.vedeng.erp.finance.service.TaxClassificationService;
import com.vedeng.erp.finance.service.TaxcodeClassificationApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class TaxClassificationServiceImpl implements TaxClassificationService, TaxcodeClassificationApiService {

    @Autowired
    private TaxcodeClassificationEntityMapper taxcodeClassificationEntityMapper;
    @Autowired
    private TaxcodeClassificationConvertor taxcodeClassificationConvertor;

    @Override
    public TaxcodeClassificationDto findByCode(String code) {
        List<TaxcodeClassificationEntity> taxcodeClassificationEntityList = taxcodeClassificationEntityMapper.findByFinalCode(code);
        if (CollUtil.isEmpty(taxcodeClassificationEntityList)) {
            return null;
        }
        return taxcodeClassificationConvertor.toDto(taxcodeClassificationEntityList.get(0));
    }

    @Override
    public PageInfo<TaxcodeClassificationDto> findPageList(PageParam<TaxcodeClassificationDto> pageParam) {
        return PageHelper.startPage(pageParam)
                .doSelectPageInfo(() -> taxcodeClassificationEntityMapper.findByAll(pageParam.getParam()));
    }
}
