<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
<c:set var="title" value="催票任务列表" scope="application" />
<%@ include file="../../common/common.jsp"%>

<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css" media="all">
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/tag.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src='<%= basePath %>static/js/flash/earlyWarningTicksTask/earlyWarningTicks_list.js?rnd=${resourceVersionKey}'></script>
<div class="main-container">
    <div class="searchfunc">
        <div class="layui-tab layui-tab-brief" lay-filter="test">
            <ul class="layui-tab-title">
                <li onclick="changeTab(0)" <c:if test="${tabFlag eq 0}">class="layui-this"</c:if>>催票列表</li>
                <li onclick="changeTab(1)" <c:if test="${tabFlag eq 1}">class="layui-this"</c:if>>跟进记录</li>
            </ul>
        </div>
        <form method="post" id="search" action="<%=basePath%>/flash/earlyWarningTicksTask/earlyWarningTicksTask.do" >
            <ul>
                <li>
                    <label class="infor_name">产品</label>
                    <input type="text" class="input-middle" name="sku" id="sku" value="${earlyWarningTicksSearchDto.sku}"/>
                    <input type="hidden" name="userIds"  id="userIds" value="${earlyWarningTicksSearchDto.userIds}" >
                    <input type="hidden" name="tabFlag"  id="tabFlag" value="${tabFlag}" >
                </li>
                <li>
                    <label class="infor_name">供应商</label>
                    <input type="text" class="input-middle" name="traderName" id="traderName" value="${earlyWarningTicksSearchDto.traderName}"/>
                </li>
                <li>
                    <label class="infor_name">采购单号</label>
                    <input type="text" class="input-middle" name="saleorderNo" id="saleorderNo" value="${earlyWarningTicksSearchDto.saleorderNo}"/>
                </li>
                <c:if test="${tabFlag eq 0}">
                    <li>
                        <label class="infor_name" style="width: 105px;">跟进状态</label>
                        <select class="input-middle" name="followUpStatus" id="followUpStatus" value="${earlyWarningTicksSearchDto.followUpStatus}">
                            <option value="0" <c:if test="${earlyWarningTicksSearchDto.followUpStatus eq 0}">selected="selected"</c:if>>全部</option>
                            <option value="1" <c:if test="${earlyWarningTicksSearchDto.followUpStatus eq 1}">selected="selected"</c:if>>已跟进</option>
                            <option value="2" <c:if test="${earlyWarningTicksSearchDto.followUpStatus eq 2}">selected="selected"</c:if>>未跟进</option>
                        </select>
                    </li>
                </c:if>
                <c:if test="${isAdmin eq 1}">
                    <li>
                        <label class="infor_name" style="width: 105px;">处理人</label>
                        <select class="input-middle" name="delaer" id="delaer" value="${earlyWarningTicksSearchDto.delaer}">
                            <option value="0">全部</option>
                            <c:forEach items="${delaerUserList}" var="user">
                                <option value="${user.userId}" <c:if test="${earlyWarningTicksSearchDto.delaer eq user.userId}">selected="selected"</c:if>>${user.userName}</option>
                            </c:forEach>
                        </select>
                    </li>
                </c:if>
                <c:if test="${tabFlag eq 1 and isAdmin eq 1}">
                    <li>
                        <div class="infor_name">
                            <lable>可开时间</lable>
                        </div>
                        <div class="f_left inputfloat ">
                            <div>
                                <input class="Wdate input-small mr0" type="text" placeholder="请选择时间" autocomplete="off" onFocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',maxDate:'#F{$dp.$D(\'endTimeStr\')}'})"
                                       name="startTimeStr" id="startTimeStr" value="<date:date value ="${earlyWarningTicksSearchDto.startTimeStr} "/>"/><div class="gang">-</div>
                                <input class="Wdate input-small ml5" type="text" placeholder="请选择时间" autocomplete="off" onFocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',minDate:'#F{$dp.$D(\'startTimeStr\')}'})"
                                       name="endTimeStr" id="endTimeStr" onchange="changeDate(this);" value="<date:date value ="${earlyWarningTicksSearchDto.endTimeStr} "/>"/>
                            </div>
                            <div id="timeError"></div>
                        </div>
                    </li>
                </c:if>
            </ul>
            <div class="tcenter">
                <span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();" id="searchSpan">搜索</span>
                <span class="bt-small bg-light-blue bt-bg-style mr20" onclick="reset();">重置</span>
            </div>
        </form>
    </div>
    <div class="tleft" style="padding: 10px 0 10px 10px;">
        <c:if test="${tabFlag eq 0}">
            <span class="bt-largest bt-bg-style bg-light-blue" id="showFollowUp" >催票跟进</span>
            <a class="addtitle_dyn can-click"
               href="javascript:void(0);"
               tabTitle='{"num":"supplyAftersalePolicyCountGroupByGrade",
						"link":"/supplyChain/invoice/hx_invoice_wait.do"}'>

                <span class="bt-largest bg-light-blue bt-bg-style" onclick="showTicks();" id="showTicks" >查看发票</span>
            </a>
        </c:if>
    </div>

    <div class="content">
        <div class="fixdiv">
            <div class="superdiv" style='width:2200px;'>
                <c:if test="${tabFlag eq 0}">
                    <table class="table table-bordered table-striped table-condensed table-centered">
                        <thead>
                        <tr>
                            <th class="wid5">
                                <input type="checkbox"  name="b_checknox" autocomplete="off" onclick="selectAll(this);" >
                            </th>
                            <th class="wid10">供应商</th>
                            <th class="wid10">采购单号</th>
                            <th class="wid5">订货号</th>
                            <th class="wid10">产品名称</th>
                            <th class="wid5">单位</th>
                            <th class="wid5">采购单价</th>
                            <th class="wid5">采购数量</th>
                            <th class="wid10">本次到货时间</th>
                            <th class="wid5">到货数量</th>
                            <th class="wid5">催票金额</th>
                            <th class="wid10">录票数量/录票金额【审核通过】</th>
                            <th class="wid5">跟进状态</th>
                            <c:if test="${isAdmin eq 1}">
                                <th class="wid5">处理人</th>
                            </c:if>
                            <th class="wid6">创建时间</th>
                        </tr>
                        </thead>
                        <tbody>
                        <c:choose>
                            <c:when test="${not empty earlyWarningTicksList}">
                                <c:forEach var="earlyWarningTicksList" items="${earlyWarningTicksList}" varStatus="num">
                                    <tr>
                                        <td>
                                            <input type="checkbox" name="checkOne" value="${earlyWarningTicksList.earlyWarningTaskId}" autocomplete="off">
                                            <input type="hidden" name="earlyWarningTaskId" value="${earlyWarningTicksList.earlyWarningTaskId}" >
                                            <input type="hidden" name="urgingTicketNum" value="${earlyWarningTicksList.urgingTicketNum}">
                                            <input type="hidden" name="urgingTicketAmount" value="${earlyWarningTicksList.urgingTicketAmount-earlyWarningTicksList.alreadyInputAmount}">
                                        </td>
                                        <td>${earlyWarningTicksList.traderName}</td>
                                        <td>${earlyWarningTicksList.saleorderNo}</td>
                                        <td>${earlyWarningTicksList.skuNo}</td>
                                        <td>${earlyWarningTicksList.skuName}</td>
                                        <td>${earlyWarningTicksList.skuUnit}</td>
                                        <td>${earlyWarningTicksList.price}</td>
                                        <td>${earlyWarningTicksList.num}</td>
                                        <td>${earlyWarningTicksList.addtime}</td>
                                        <td>${earlyWarningTicksList.urgingTicketNum}</td>
                                        <td>${earlyWarningTicksList.urgingTicketAmount-earlyWarningTicksList.alreadyInputAmount}</td>
                                        <td>${earlyWarningTicksList.alreadyInputNum}/${earlyWarningTicksList.alreadyInputAmount}</td>
                                        <td>
                                            <c:choose>
                                                <c:when test="${earlyWarningTicksList.followUpStatus == 1}">未跟进</c:when>
                                                <c:when test="${earlyWarningTicksList.followUpStatus == 2}">已跟进</c:when>
                                                <c:otherwise>-</c:otherwise>
                                            </c:choose>
                                        </td>
                                        <c:if test="${isAdmin eq 1}">
                                            <td>${earlyWarningTicksList.taskDealerName}</td>
                                        </c:if>
                                        <td>${earlyWarningTicksList.addtime}</td>
                                    </tr>
                                </c:forEach>
                            </c:when>
                            <c:otherwise>
                                <tr><td colspan="14">暂无数据</td></tr>
                            </c:otherwise>
                        </c:choose>
                        </tbody>
                    </table>
                </c:if>
                <c:if test="${tabFlag eq 1}">
                    <table class="table table-bordered table-striped table-condensed table-centered">
                        <thead>
                        <tr>
                            <th class="wid5">
                                <input type="checkbox"  name="b_checknox" autocomplete="off" onclick="selectAll(this);" >
                            </th>
                            <th class="wid10">供应商</th>
                            <th class="wid10">采购单号</th>
                            <th class="wid5">订货号</th>
                            <th class="wid10">产品名称</th>
                            <th class="wid5">单位</th>
                            <th class="wid5">采购单价</th>
                            <th class="wid5">催票数量/催票金额</th>
                            <th class="wid10">本次到货时间</th>
                            <th class="wid10">最新跟进时间</th>
                            <th class="wid5">跟进结果</th>
                            <th class="wid5">可开金额</th>
                            <th class="wid10">可开时间</th>
                            <th class="wid10">不可开原因</th>
                            <c:if test="${isAdmin eq 1}">
                                <th class="wid5">处理人</th>
                            </c:if>
                        </tr>
                        </thead>
                        <tbody>
                        <c:choose>
                            <c:when test="${not empty earlyWarningTicksList}">
                                <c:forEach var="earlyWarningTicksList" items="${earlyWarningTicksList}" varStatus="num">
                                    <tr>
                                        <td><input type="checkbox" name="checkOne"
                                                   value="${earlyWarningTicksList.earlyWarningTaskId}" autocomplete="off"></td>
                                        <input type="hidden" name="earlyWarningTaskId" value="${earlyWarningTicksList.earlyWarningTaskId}" >
                                        </td>
                                        <td>${earlyWarningTicksList.traderName}</td>
                                        <td>${earlyWarningTicksList.saleorderNo}</td>
                                        <td>${earlyWarningTicksList.skuNo}</td>
                                        <td>${earlyWarningTicksList.skuName}</td>
                                        <td>${earlyWarningTicksList.skuUnit}</td>
                                        <td>${earlyWarningTicksList.price}</td>
                                        <td>${earlyWarningTicksList.urgingTicketNumFollowUp}/${earlyWarningTicksList.urgingTicketAmountFollowUp}</td>
                                        <td>${earlyWarningTicksList.addtime}</td>
                                        <td>${earlyWarningTicksList.followUpTime}</td>
                                        <td>${earlyWarningTicksList.followUpResult}</td>
                                        <td>${earlyWarningTicksList.canTicketAmount}</td>
                                        <td>${earlyWarningTicksList.canTicketTime}</td>
                                        <td>${earlyWarningTicksList.followUpComment}</td>
                                        <c:if test="${isAdmin eq 1}">
                                            <td>${earlyWarningTicksList.taskDealerName}</td>
                                        </c:if>
                                    </tr>
                                </c:forEach>
                            </c:when>
                            <c:otherwise>
                                <tr><td colspan="14">暂无数据</td></tr>
                            </c:otherwise>
                        </c:choose>
                        </tbody>
                    </table>
                </c:if>
            </div>
        </div>
    </div>
    <span style="display:none;">
        <div class="title-click nobor  pop-new-data" id="followUoView"></div>
    </span>
    <div>
        <tags:page page="${page}" />
        <div class="clear"></div>
        <div class="fixtablelastline">
            【催票金额：${totalPageAmount}】
            <c:if test="${tabFlag eq 1}">
                【可开金额：${totalPageCanTicketAmount}】
            </c:if>
        </div>
    </div>
</div>
<%@ include file="../../common/footer.jsp"%>

