package com.test.kingdee;

import cn.hutool.core.collection.CollUtil;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.domain.command.KingDeePayExpensesCommand;
import com.vedeng.erp.kingdee.dto.KingDeePayExpensesDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeePayExpensesDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeePayExpensesMapper;
import com.vedeng.erp.kingdee.mapstruct.KingDeePayExpensesCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeePayExpensesConvertor;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.RepoStatus;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * 应付单：费用应付单
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring.xml", "classpath:spring-mybatis.xml"})
public class TestKingDeePayExpenses {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private KingDeePayExpensesCommandConvertor commandConvertor;

    @Autowired
    private KingDeePayExpensesConvertor kingDeePayExpensesConvertor;

    @Autowired
    private KingDeePayExpensesMapper kingDeePayExpensesMapper;


    @Test
    public void kingDeePayExpenses() {
    	KingDeePayExpensesDto dto = new KingDeePayExpensesDto();
        dto.setFId(0);
        dto.setFBillTypeId("YFD02_SYS");
        dto.setFBillNo("1112");
        dto.setFDate("2022-09-19 00:00:00");
        dto.setFSupplierId("VEN0006");
        dto.setFCurrencyId("PRE001");
        dto.setFIsPriceExcludeTax(true);
        dto.setFIsTax(true);
        dto.setFBusinessType("FY");
        dto.setFSettleOrgId("101");
        dto.setFPayOrgId("101");
        dto.setFSetAccountType("1");
        dto.setFQzokBddjtId("贝登erp对应的单据头ID");
        
        List<KingDeePayExpensesDetailDto>  FEntityDetail = new ArrayList<>();
        KingDeePayExpensesDetailDto detailDto = new KingDeePayExpensesDetailDto();
        detailDto.setFCOSTID("026");
        detailDto.setFPriceQty("1");
        detailDto.setFTaxPrice("106");
        detailDto.setFEntryTaxRate("6");
        detailDto.setFINCLUDECOST(false);
        detailDto.setFCOSTDEPARTMENTID("BM9999");
        detailDto.setF_QZOK_BDDJHID("贝登订单行ID");
        detailDto.setF_QZOK_WLBM("贝登SKU");
        FEntityDetail.add(detailDto);
        dto.setFEntityDetail(FEntityDetail);

        KingDeePayExpensesCommand command = commandConvertor.toCommand(dto);


        System.out.println("入参："+JSON.toJSONString(command));
        RepoStatus save = kingDeeBaseApi.save(new SaveExtCommand<>(command, dto.getFormId()));
        ArrayList<SuccessEntity> successEntities = save.getSuccessEntitys();
        if (CollUtil.isNotEmpty(successEntities)) {
            SuccessEntity successEntity = CollUtil.getFirst(successEntities);
            System.out.println("数据库入参："+JSON.toJSONString(kingDeePayExpensesConvertor.toEntity(dto)));
            kingDeePayExpensesMapper.insertSelective(kingDeePayExpensesConvertor.toEntity(dto));
        }
    }
}
