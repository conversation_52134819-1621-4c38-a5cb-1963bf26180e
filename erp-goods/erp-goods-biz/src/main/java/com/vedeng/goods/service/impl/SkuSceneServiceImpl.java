package com.vedeng.goods.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.eventbus.EventBus;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.core.base.BaseResponseCode;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.UserComponent;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.listenerEvent.goods.SyncSkuInfo2EsEvent;
import com.vedeng.common.core.utils.DiffUtils;
import com.vedeng.common.core.utils.numgenerator.BillNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.goods.domain.dto.*;
import com.vedeng.goods.domain.entity.GoodsLevelEntity;
import com.vedeng.goods.domain.entity.GoodsPositionEntity;
import com.vedeng.goods.domain.entity.SkuSceneCategoryEntity;
import com.vedeng.goods.domain.entity.SkuSceneEntity;
import com.vedeng.goods.dto.*;
import com.vedeng.goods.feign.price.RemotePriceApiService;
import com.vedeng.goods.mapper.*;
import com.vedeng.goods.mapstruct.SkuSceneCategoryConvertor;
import com.vedeng.goods.mapstruct.SkuSceneConvertor;
import com.vedeng.goods.service.SkuSceneService;
import com.vedeng.price.api.price.dto.ResultInfo;
import com.vedeng.price.api.price.dto.price.BatchSkuPriceInfoDetailRequestDto;
import com.vedeng.price.api.price.dto.price.SkuPriceChangeApplyDto;
import com.vedeng.price.api.price.dto.price.SkuPriceChangeApplyPageQueryDto;
import com.vedeng.price.api.price.dto.price.SkuPriceInfoDetailResponseDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SkuSceneServiceImpl implements SkuSceneService {


    @Autowired
    private SkuSceneMapper skuSceneMapper;
    @Autowired
    private SkuSceneCategoryMapper skuSceneCategoryMapper;
    @Autowired
    private SkuSceneConvertor skuSceneConvertor;
    @Autowired
    private SkuSceneCategoryConvertor skuSceneCategoryConvertor;

    @Autowired
    private RemotePriceApiService remotePriceApiService;
    @Autowired
    private UserApiService userApiService;

    @Autowired
    private CoreSkuMapper coreSkuMapper;
    @Autowired
    private GoodsLevelMapper goodsLevelMapper;
    @Autowired
    private GoodsPositionMapper goodsPositionMapper;

    @Autowired
    @Qualifier(value = "eventBusCenter")
    private EventBus eventBusCenter;


    @Override
    public PageInfo<SkuSceneDto> page(PageParam<SkuSceneDto> pageParam) {
        // 检查并设置排序字段
        if (StrUtil.isEmpty(pageParam.getOrderBy())) {
            pageParam.setOrderBy("SORT ASC");
        }

        PageInfo<SkuSceneDto> pageInfo = PageHelper.startPage(pageParam).doSelectPageInfo(() -> skuSceneMapper.findByAll(pageParam.getParam()));

        if (CollUtil.isNotEmpty(pageInfo.getList())) {
            // 收集所有DTO的distinctSkuIds，并设置productCount
            Map<Long, List<String>> dtoSkuMap = new HashMap<>();
            List<String> allSkuNos = new ArrayList<>();

            for (SkuSceneDto dto : pageInfo.getList()) {
                List<SkuSceneCategoryEntity> bySceneIdAndIsDelete = skuSceneCategoryMapper.findBySceneIdAndIsDelete(dto.getId());
                dto.setSkuSceneCategoryDtoList(skuSceneCategoryConvertor.toDto(bySceneIdAndIsDelete));
                List<String> distinctSkuNos = getDistinctSkuNos(dto);
                dto.setProductCount(distinctSkuNos.size());
                dtoSkuMap.put(dto.getId(), distinctSkuNos);
                allSkuNos.addAll(distinctSkuNos);
            }

            // 查询异常产品
            List<CoreSkuDto> coreSkuDtos = coreSkuMapper.findBySkuNoIn(allSkuNos);
            Set<String> errorSkuNos = coreSkuDtos.stream()
                    .filter(sku -> sku.getGoodsLevelNo() == 5 || sku.getCheckStatus() != 3)
                    .map(CoreSkuDto::getSku)
                    .collect(Collectors.toSet());

            // 设置每个DTO的errorCount
            for (SkuSceneDto dto : pageInfo.getList()) {
                List<String> skuNos = dtoSkuMap.get(dto.getId());
                long errorCount = skuNos.stream().filter(errorSkuNos::contains).count();
                dto.setErrorCount((int) errorCount);
            }
        }

        return pageInfo;
    }

    private List<String> getDistinctSkuNos(SkuSceneDto dto) {
        return dto.getSkuSceneCategoryDtoList().stream()
                .map(SkuSceneCategoryDto::getSkuNos)
                .flatMap(s -> Arrays.stream(s.split(",")))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public SkuSceneDto getById(Long id) {
        SkuSceneDto skuSceneDto = skuSceneMapper.findById(id);
        List<SkuSceneCategoryEntity> bySceneIdAndIsDelete = skuSceneCategoryMapper.findBySceneIdAndIsDelete(id);
        skuSceneDto.setSkuSceneCategoryDtoList(skuSceneCategoryConvertor.toDto(bySceneIdAndIsDelete));
        skuSceneDto.getSkuSceneCategoryDtoList().forEach(dto -> {
            List<String> list = Arrays.stream(dto.getSkuNos().split(",")).collect(Collectors.toList());
            List<SkuDto> productInfoList = this.getProductInfo(list);
            dto.setProductInfoList(productInfoList);
        });
        return skuSceneDto;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void create(SkuSceneDto skuSceneDto) {
        log.info("创建方案，参数：{}", skuSceneDto);

        boolean check = this.check(skuSceneDto.getName(), null);
        if (!check) {
            throw new ServiceException("方案名称重复");
        }

        Integer maxSort = skuSceneMapper.findMaxSort();
        BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.SCENE_BILL);
        String no = new BillNumGenerator().distribution(billGeneratorBean);
        skuSceneDto.setSceneNo(no);
        skuSceneDto.setStatus(0);
        skuSceneDto.setSort(maxSort + 1);
        SkuSceneEntity skuSceneEntity = skuSceneConvertor.toEntity(skuSceneDto);
        // 插入并排序
        skuSceneMapper.insertSelective(skuSceneEntity);
        skuSceneDto.setId(skuSceneEntity.getId());

        this.sort(skuSceneDto.getId(), 1);

        List<SkuSceneCategoryDto> sceneDtoList = skuSceneDto.getSkuSceneCategoryDtoList();
        if (CollUtil.isNotEmpty(sceneDtoList)) {
            sceneDtoList.forEach(dto -> {
                dto.setSceneId(skuSceneEntity.getId());
                SkuSceneCategoryEntity skuSceneCategoryEntity = skuSceneCategoryConvertor.toEntity(dto);
                skuSceneCategoryMapper.insertSelective(skuSceneCategoryEntity);
            });
        }

        this.syncSkuSceneCategory(skuSceneDto.getSkuSceneCategoryDtoList());
    }

    private void syncSkuSceneCategory(List<SkuSceneCategoryDto> skuSceneCategoryDtoList) {
        // 将分类sku整合成skuIds
        List<Integer> skuIds = skuSceneCategoryDtoList.stream().map(SkuSceneCategoryDto::getSkuNos)
                .flatMap(s -> Arrays.stream(s.split(",")))
                .map(String::trim)
                .map(s -> s.replace("V", ""))
                .map(Integer::valueOf)
                .distinct()
                .collect(Collectors.toList());
        log.info("整合后的skuIds：{}", JSON.toJSONString(skuIds));

        SyncSkuInfo2EsEvent syncSkuInfo2EsEvent = new SyncSkuInfo2EsEvent();
        syncSkuInfo2EsEvent.setSkuIds(skuIds);
        eventBusCenter.post(syncSkuInfo2EsEvent);
    }

    @Override
    public void update(SkuSceneDto skuSceneDto) {
        log.info("修改方案，参数：{}", skuSceneDto);

        boolean check = this.check(skuSceneDto.getName(), skuSceneDto.getId());
        if (!check) {
            throw new ServiceException("方案名称重复");
        }

        SkuSceneEntity skuSceneEntity = skuSceneConvertor.toEntity(skuSceneDto);
        skuSceneMapper.updateByPrimaryKeySelective(skuSceneEntity);


        // 修改场景
        List<SkuSceneCategoryEntity> skuSceneCategoryEntityListFromDb = skuSceneCategoryMapper.findBySceneIdAndIsDelete(skuSceneDto.getId());
        log.info("修改之前数据库中存在的场景分类{}", JSON.toJSONString(skuSceneCategoryEntityListFromDb));

        List<SkuSceneCategoryEntity> skuSceneCategoryEntityList = skuSceneCategoryConvertor.toEntity(skuSceneDto.getSkuSceneCategoryDtoList());
        skuSceneCategoryEntityList.forEach(item -> item.setSceneId(skuSceneDto.getId()));
        log.info("修改时传入的场景分类数据{}", JSON.toJSONString(skuSceneCategoryEntityList));

        List<DiffUtils.ContentValue<Long, SkuSceneCategoryEntity>> skuSceneCategoryFromDb =
                skuSceneCategoryEntityListFromDb.stream().map(item -> DiffUtils.ContentValue.of(item.getId(), item)).collect(Collectors.toList());
        List<DiffUtils.ContentValue<Long, SkuSceneCategoryEntity>> skuSceneCategoryFromParams =
                skuSceneCategoryEntityList.stream().map(item -> DiffUtils.ContentValue.of(item.getId(), item)).collect(Collectors.toList());

        Map<DiffUtils.Type, Consumer<List<SkuSceneCategoryEntity>>> actionMap = new EnumMap<>(DiffUtils.Type.class);
        actionMap.put(DiffUtils.Type.INSERT, this::batchInsertSkuSceneCategory);
        actionMap.put(DiffUtils.Type.UPDATE, this::batchUpdateSkuSceneCategory);
        actionMap.put(DiffUtils.Type.DELETE, this::batchDeleteSkuSceneCategory);
        DiffUtils.doDiff(skuSceneCategoryFromDb, skuSceneCategoryFromParams, actionMap);
    }

    private void batchInsertSkuSceneCategory(List<SkuSceneCategoryEntity> skuSceneCategoryEntityList) {
        if (CollUtil.isNotEmpty(skuSceneCategoryEntityList)) {
            skuSceneCategoryEntityList.forEach(item -> item.setIsDelete(ErpConstant.F));
            skuSceneCategoryMapper.batchInsert(skuSceneCategoryEntityList);

            this.syncSkuSceneCategory(skuSceneCategoryConvertor.toDto(skuSceneCategoryEntityList));
        }
    }

    private void batchUpdateSkuSceneCategory(List<SkuSceneCategoryEntity> skuSceneCategoryEntityList) {
        if (CollUtil.isNotEmpty(skuSceneCategoryEntityList)) {
            skuSceneCategoryMapper.updateBatchSelective(skuSceneCategoryEntityList);

            this.syncSkuSceneCategory(skuSceneCategoryConvertor.toDto(skuSceneCategoryEntityList));
        }
    }

    private void batchDeleteSkuSceneCategory(List<SkuSceneCategoryEntity> skuSceneCategoryEntityList) {
        if (CollUtil.isNotEmpty(skuSceneCategoryEntityList)) {
            skuSceneCategoryEntityList.forEach(item -> skuSceneCategoryMapper.updateIsDeleteById(item.getId()));

            this.syncSkuSceneCategory(skuSceneCategoryConvertor.toDto(skuSceneCategoryEntityList));
        }
    }

    @Override
    public void updateStatus(Long id, Integer status) {
        SkuSceneEntity skuPlanEntity = new SkuSceneEntity();
        skuPlanEntity.setId(id);
        skuPlanEntity.setStatus(status);
        skuSceneMapper.updateByPrimaryKeySelective(skuPlanEntity);
    }

    @Override
    public void delete(Long id) {
        skuSceneMapper.updateIsDeleteById(id);
    }


    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void sort(Long id, Integer newSort) {
        // 查询当前记录
        SkuSceneEntity currentEntity = skuSceneMapper.selectByPrimaryKey(id);
        if (currentEntity == null) {
            throw new ServiceException("记录不存在");
        }

        Integer oldSort = currentEntity.getSort();
        if (oldSort == null) {
            throw new ServiceException("排序值未设置");
        }

        // 如果排序值未变化，直接返回
        if (newSort.equals(oldSort)) {
            return;
        }

        // 查询当前最大的排序值
        Integer maxSort = skuSceneMapper.findMaxSort();

        if (newSort > maxSort) {
            // 特殊场景 3：新排序值大于最大排序值
            // 将最大排序值前面的数据依次减 1
            adjustSortValues(oldSort + 1, maxSort, -1);

            // 将当前记录设置为最大排序值
            currentEntity.setSort(maxSort);
            currentEntity.setModTime(currentEntity.getModTime());
            skuSceneMapper.updateByPrimaryKeySelective(currentEntity);

        } else {
            // 普通场景：调整排序值
            if (newSort > oldSort) {
                // 新排序值大于旧值 -> 修改旧值后到新值之间的数据，排序 -1
                adjustSortValues(oldSort + 1, newSort, -1);
            } else {
                // 新排序值小于旧值 -> 修改新值到旧值之间的数据，排序 +1
                adjustSortValues(newSort, oldSort - 1, 1);
            }

            currentEntity.setSort(newSort);
            currentEntity.setModTime(currentEntity.getModTime());
            skuSceneMapper.updateByPrimaryKeySelective(currentEntity);
        }
    }


    /**
     * 调整排序值的方法
     *
     * @param start 起始排序值（包含）
     * @param end   结束排序值（包含）
     * @param delta 调整的增量（+1 或 -1）
     */
    private void adjustSortValues(int start, int end, int delta) {
        List<SkuSceneEntity> entities = skuSceneMapper.findBySortBetween(start, end);
        if (CollUtil.isNotEmpty(entities)) {
            for (SkuSceneEntity entity : entities) {
                entity.setSort(entity.getSort() + delta);
                entity.setModTime(entity.getModTime());
                skuSceneMapper.updateByPrimaryKeySelective(entity);
            }
        }
    }


    @Override
    public List<UserComponent> getCreateUser() {
        return skuSceneMapper.findCreator();
    }

    @Override
    public List<UserComponent> getEditUser() {
        return skuSceneMapper.findUpdater();
    }

    @Override
    public List<SkuDto> getProductInfo(List<String> skuNos) {

        Map<String, SkuImageDto> imageMap = this.toMap(coreSkuMapper.getSkuImage(skuNos), SkuImageDto::getSkuNo);
        Map<String, SkuPriceInfoDetailResponseDto> skuPriceMap = this.toMap(getSaleSkuPrice(skuNos), SkuPriceInfoDetailResponseDto::getSkuNo);
        Map<String, ProductManageAndAsistDto> skuManageMap = this.toMap(coreSkuMapper.batchQueryProductManageAndAsist(skuNos), ProductManageAndAsistDto::getSkuNo);
        Map<Integer, UserDto> userDtoMap = this.getUserDtoMap(skuManageMap);
        Map<String, SkuPriceChangeApplyDto> skuPriceChangeApplyMap = this.toMap(findBySkuPriceChangeApply(skuNos), SkuPriceChangeApplyDto::getSkuNo);

        List<CoreSkuDto> coreSkuList = coreSkuMapper.getInfoBySkuNos(skuNos);
        if (CollUtil.isEmpty(coreSkuList)) {
            return Collections.emptyList();
        }

        return coreSkuList.stream().map(coreSku -> this.buildSkuDto(coreSku,
                        imageMap, skuPriceMap, skuManageMap, userDtoMap, skuPriceChangeApplyMap))
                .collect(Collectors.toList());
    }

    @Override
    public List<SceneSkuPoiDto> exportExcel(Long id) {

        List<SceneSkuPoiDto> sceneSkuPoiDtoList = new ArrayList<>();
        SkuSceneEntity skuSceneEntity = skuSceneMapper.selectByPrimaryKey(id);

        List<SkuSceneCategoryEntity> bySceneIdAndIsDelete = skuSceneCategoryMapper.findBySceneIdAndIsDelete(id);
        bySceneIdAndIsDelete.forEach(dto -> {
            List<String> skuNos = Arrays.asList(dto.getSkuNos().split(","));
            List<SkuDto> productInfo = this.getProductInfo(skuNos);

            SceneSkuPoiDto sceneSkuPoiDto = new SceneSkuPoiDto();
            sceneSkuPoiDto.setSkuSceneName(skuSceneEntity.getName());
            sceneSkuPoiDto.setSkuSceneCategoryName(dto.getName());
            List<SceneSkuPoiDto.SkuInfo> skuInfoList = sceneSkuPoiDto.getSkuInfoList();
            productInfo.forEach(info -> {
                SceneSkuPoiDto.SkuInfo skuInfo = new SceneSkuPoiDto.SkuInfo();

                SkuDto.BasicInfo basicInfo = info.getBasicInfo();
                skuInfo.setSkuNo(basicInfo.getSkuNo());
                skuInfo.setSkuName(basicInfo.getSkuName());
                skuInfo.setBrandName(basicInfo.getBrandName());
                skuInfo.setModelOrSpec(basicInfo.getModelOrSpec());

                SkuDto.MainParameters mainParameters = info.getMainParameters();
                skuInfo.setMainParam(mainParameters.getMainParam().stream().map(String::valueOf).collect(Collectors.joining("\n")));

                SkuDto.PriceInfo priceInfo = info.getPriceInfo();
                skuInfo.setDistributionPrice(priceInfo.getDistributionPrice());
                skuInfo.setTerminalPrice(priceInfo.getTerminalPrice());

                SkuDto.AfterSaleInfo afterSaleInfo = info.getAfterSaleInfo();
                skuInfo.setWarrantyInfo(afterSaleInfo.getWarrantyInfo());
                skuInfo.setUseLife(afterSaleInfo.getUseLife());
                skuInfo.setIsInstall(afterSaleInfo.getIsInstall() == 1 ? "是" : "否");

                SkuDto.ProductStatus productStatus = info.getProductStatus();
                skuInfo.setAuditStatus(this.getAuditStatus(productStatus.getAuditStatus()));
                skuInfo.setGoodsLevelNo(productStatus.getGoodsLevelNoLabel());
                skuInfo.setGoodsPositionNo(productStatus.getGoodsPositionNoLabel());

                skuInfo.setProductManager(info.getProductManager().getUsername());
                skuInfo.setProductAssistant(info.getProductAssistant().getUsername());

                skuInfoList.add(skuInfo);
            });

            sceneSkuPoiDtoList.add(sceneSkuPoiDto);
        });

        return sceneSkuPoiDtoList;
    }

    @Override
    public boolean check(String name, Long id) {
        List<SkuSceneEntity> byName = skuSceneMapper.findByName(name);
        if (CollUtil.isEmpty(byName)) {
            return true;
        }
        if (id == null) {
            return false;
        }
        return byName.stream().anyMatch(entity -> id.equals(entity.getId()));
    }

    @Override
    public List<SkuSceneDto> getSceneAndCategory() {
        List<SkuSceneEntity> skuSceneEntities = skuSceneMapper.findAll();
        List<SkuSceneDto> skuSceneDtos = skuSceneConvertor.toDto(skuSceneEntities);
        if (CollUtil.isNotEmpty(skuSceneDtos)) {
            skuSceneDtos.forEach(dto -> {
                List<SkuSceneCategoryEntity> skuSceneCategoryEntities = skuSceneCategoryMapper.findBySceneIdAndIsDelete(dto.getId());
                dto.setSkuSceneCategoryDtoList(skuSceneCategoryConvertor.toDto(skuSceneCategoryEntities));
            });
        }
        return skuSceneDtos;
    }

    private SkuDto buildSkuDto(CoreSkuDto coreSku,
                               Map<String, SkuImageDto> imageMap,
                               Map<String, SkuPriceInfoDetailResponseDto> skuPriceMap,
                               Map<String, ProductManageAndAsistDto> skuManageMap,
                               Map<Integer, UserDto> userDtoMap,
                               Map<String, SkuPriceChangeApplyDto> skuPriceChangeApplyMap) {
        SkuDto skuDto = new SkuDto();

        // 基础信息
        SkuImageDto imageDto = imageMap.get(coreSku.getSku());
        SkuDto.BasicInfo basicInfo = SkuDto.BasicInfo.builder()
                .skuNo(coreSku.getSku())
                .skuName(coreSku.getSkuName())
                .brandName(coreSku.getBrandName())
                .modelOrSpec(coreSku.getModelOrSpec())
                .imageUrl(imageDto != null ? imageDto.getImgUrl() : null)
                .build();
        skuDto.setBasicInfo(basicInfo);

        // 主要参数
        skuDto.setMainParameters(new SkuDto.MainParameters(getMainParams(coreSku.getTechnicalParameter())));

        // 价格信息
        SkuPriceInfoDetailResponseDto priceInfo = skuPriceMap.get(coreSku.getSku());
        SkuPriceChangeApplyDto skuPriceChangeApplyDto = skuPriceChangeApplyMap.get(coreSku.getSku());
        int priceStatus = 0;
        if (skuPriceChangeApplyDto != null) {
            if (skuPriceChangeApplyDto.getAuditPass() != null
                    && skuPriceChangeApplyDto.getAuditPass() == 1
                    && skuPriceChangeApplyDto.getDisabled() != null
                    && skuPriceChangeApplyDto.getDisabled() == 0) {
                priceStatus = 1;
            }
        }

        skuDto.setPriceInfo(SkuDto.PriceInfo.builder()
                .distributionPrice(priceInfo != null ? priceInfo.getDistributionPrice()  : null)//这里变量定义 反了
                .terminalPrice(priceInfo != null ? priceInfo.getTerminalPrice(): null)
                .priceStatus(priceStatus)
                .build());

        // 售后信息
        SkuDto.AfterSaleInfo afterSaleInfo = buildAfterSaleInfo(coreSku);
        skuDto.setAfterSaleInfo(afterSaleInfo);

        // 产品状态
        skuDto.setProductStatus(SkuDto.ProductStatus.builder()
                .auditStatus(coreSku.getCheckStatus())
                .goodsLevelNo(coreSku.getGoodsLevelNo())
                .goodsLevelNoLabel(this.goodsLevelNoLabel(coreSku.getGoodsLevelNo()))
                .goodsPositionNo(coreSku.getGoodsPositionNo())
                .goodsPositionNoLabel(this.goodsPositionNoLabel(coreSku.getGoodsPositionNo()))
                .build());


        // 产品经理信息
        ProductManageAndAsistDto manageInfo = skuManageMap.get(coreSku.getSku());
        UserDto managerUser = userDtoMap.get(manageInfo.getProductManageUserId());
        UserDto assitUser = userDtoMap.get(manageInfo.getProductAssitUserId());
        skuDto.setProductManager(managerUser);
        skuDto.setProductAssistant(assitUser);

        return skuDto;
    }

    private String goodsLevelNoLabel(Integer goodsLevelNo) {
        List<GoodsLevelEntity> goodsLevelList = goodsLevelMapper.findAll();
        return goodsLevelList.stream().filter(goodsLevelEntity -> goodsLevelEntity.getId().equals(goodsLevelNo))
                .findFirst().map(GoodsLevelEntity::getLevelName).orElse("");
    }

    private String goodsPositionNoLabel(Integer goodsPositionNo) {
        List<GoodsPositionEntity> goodsPositionList = goodsPositionMapper.findAll();
        return goodsPositionList.stream().filter(goodsPositionEntity -> goodsPositionEntity.getId().equals(goodsPositionNo))
                .findFirst().map(GoodsPositionEntity::getPositionName).orElse("");
    }

    private SkuDto.AfterSaleInfo buildAfterSaleInfo(CoreSkuDto coreSku) {
        SkuDto.AfterSaleInfo afterSaleInfo = SkuDto.AfterSaleInfo.builder().isInstall(coreSku.getIsInstallable()).build();
        String warrantyInfo = getWarrantyInfo(coreSku.getHostPeriod());
        if (StringUtils.isNotBlank(warrantyInfo)) {
            afterSaleInfo.setWarrantyInfo(warrantyInfo);
        }
        String useLife = this.getUseLife(coreSku);
        afterSaleInfo.setUseLife(useLife);
        return afterSaleInfo;
    }

    private String getWarrantyInfo(String hostPeriod) {
        if (StringUtils.isBlank(hostPeriod)) {
            return null;
        }
        String[] split = hostPeriod.split(",");
        return (split.length > 1 && StrUtil.isNotBlank(split[0])) ? split[0] + "月" : null;
    }

    private String getUseLife(CoreSkuDto coreSku) {
        if (coreSku.getSpuType() == 317 && StringUtils.isNotBlank(coreSku.getEffectiveDays())) {
            return coreSku.getEffectiveDays() + "天";
        } else if (coreSku.getSpuType() != 317 && Objects.nonNull(coreSku.getServiceLife())) {
            return coreSku.getServiceLife() + "年";
        }
        return "";
    }

    private Map<Integer, UserDto> getUserDtoMap(Map<String, ProductManageAndAsistDto> skuManageMap) {
        List<Integer> userIds = skuManageMap.values().stream()
                .flatMap(manageInfo -> Stream.of(manageInfo.getProductManageUserId(), manageInfo.getProductAssitUserId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<UserDto> userList = userApiService.getUserInfoByUserIds(userIds);
        return toMap(userList, UserDto::getUserId);
    }

    private <T, K> Map<K, T> toMap(List<T> list, Function<T, K> keyMapper) {
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(keyMapper, Function.identity()));
    }

    private List<String> getMainParams(String technicalParameter) {
        if (StringUtils.isBlank(technicalParameter)) {
            return Collections.emptyList();
        }
        return Arrays.stream(technicalParameter.split(";")).limit(6).collect(Collectors.toList());
    }

    private List<SkuPriceInfoDetailResponseDto> getSaleSkuPrice(List<String> skuNoList) {
        BatchSkuPriceInfoDetailRequestDto request = new BatchSkuPriceInfoDetailRequestDto();
        request.setSkuNo(skuNoList);
        return getBatchPriceCenterInfo(request);
    }

    private List<SkuPriceInfoDetailResponseDto> getBatchPriceCenterInfo(BatchSkuPriceInfoDetailRequestDto request) {
        log.info("批量获取核价信息，入参[{}]", JSON.toJSONString(request));
        RestfulResult<List<SkuPriceInfoDetailResponseDto>> result = remotePriceApiService.findSkuPriceInfoBySkuNos(request);
        if (result == null) {
            log.error("批量获取核价信息, 调用价格中心异常");
            throw new ServiceException(BaseResponseCode.SYSTEM_BUSY);
        }
        log.info("批量获取核价信息，返回值[{}]", JSON.toJSONString(result));
        return result.getData();
    }

    private List<SkuPriceChangeApplyDto> findBySkuPriceChangeApply(List<String> skuNos) {
        SkuPriceChangeApplyPageQueryDto skuPriceChangeApplyPageQueryDto = new SkuPriceChangeApplyPageQueryDto();
        skuPriceChangeApplyPageQueryDto.setPageNo(1);
        skuPriceChangeApplyPageQueryDto.setPageSize(100);
        skuPriceChangeApplyPageQueryDto.setIncludeSkuNosStr(String.join(",", skuNos));
        log.info("findPriceChangeApply->findByPage 请求参数:{}", skuPriceChangeApplyPageQueryDto);
        ResultInfo<Map<String, Object>> bySkuPriceChangeApplyPage = remotePriceApiService.findBySkuPriceChangeApplyPage(skuPriceChangeApplyPageQueryDto);
        log.info("findPriceChangeApply->findByPage 响应:{}", bySkuPriceChangeApplyPage.toString());

        if (!"success".equals(bySkuPriceChangeApplyPage.getCode())) {
            return new ArrayList<>();
        }
        List<Object> objectList = (List<Object>)bySkuPriceChangeApplyPage.getData().get("priceChangeApplyList");
        String jsonArrayString = JSONArray.toJSONString(objectList);
        List<SkuPriceChangeApplyDto> priceChangeApplyList = JSONArray.parseArray(jsonArrayString, SkuPriceChangeApplyDto.class);
//        List<SkuPriceChangeApplyDto> priceChangeApplyList = (List<SkuPriceChangeApplyDto>) bySkuPriceChangeApplyPage.getData().get("priceChangeApplyList");
        return priceChangeApplyList;
    }


    public String getAuditStatus(Integer status) {
        Map<Integer, String> auditStatusMap = new HashMap<>();
        auditStatusMap.put(0, "待完善");
        auditStatusMap.put(1, "审核中");
        auditStatusMap.put(2, "审核不通过");
        auditStatusMap.put(3, "审核通过");
        auditStatusMap.put(4, "删除");
        auditStatusMap.put(5, "待提交审核");
        return auditStatusMap.getOrDefault(status, "未知状态");
    }


}

