package com.vedeng.erp.trader.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashSet;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 客户已交易账号接口
 * @date 2022/8/16 14:30
 */
public interface TraderPaidAccountService {

    /**
     * 删除
     * @param newHashSet
     */
    void delete(HashSet<Integer> newHashSet);

    /**
     * 导入
     * @param file
     */
    void importFile(MultipartFile file) throws Exception;
}
