package com.vedeng.finance.dao;

import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesDetail;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.aftersales.model.vo.AfterSalesDetailVo;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.aftersales.model.vo.AfterSalesInvoiceVo;
import com.vedeng.finance.model.CapitalBill;
import com.vedeng.finance.model.Invoice;
import com.vedeng.finance.model.InvoiceAfter;
import com.vedeng.finance.model.PayApply;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Named("invoiceAfterMapper")
public interface InvoiceAfterMapper {

    /**
     * <b>Description:</b><br> 查询财务模块售后列表
     *
     * @param map
     * @return
     * @Note <b>Author:</b> duke
     * <br><b>Date:</b> 2017年10月17日 下午5:14:38
     */
    List<InvoiceAfter> getFinanceAfterListPage(Map<String, Object> map);

    /**
     * <b>Description:</b><br> 根据售后ID获取交易对象信息
     *
     * @return
     * @Note <b>Author:</b> duke
     * <br><b>Date:</b> 2017年10月24日 上午10:26:19
     */
    AfterSalesDetailVo getAfterCapitalBillInfo(AfterSalesDetailVo afterSalesDetailVo);

    /**
     * <b>Description:</b><br> 获取售后订单多项款项金额（订单总额-退款总额-账期金额-手续费等）
     *
     * @param afterSalesId
     * @Note <b>Author:</b> duke
     * <br><b>Date:</b> 2017年10月24日 下午3:21:54
     */
    Map<String, String> getAfterOrderMultipleAmount(@Param(value = "afterSalesId") Integer afterSalesId);

    /**
     * <b>Description:</b><br> 修改售后订单手续费状态
     *
     * @param asd
     * @return
     * @Note <b>Author:</b> duke
     * <br><b>Date:</b> 2017年10月25日 上午9:57:59
     */
    int updateAfterDetailServiceStatus(AfterSalesDetailVo asd);

    /**
     * <b>Description:</b><br> 获取售后发票信息
     *
     * @param afterSalesInvoiceVo
     * @return
     * @Note <b>Author:</b> duke
     * <br><b>Date:</b> 2017年10月25日 下午2:37:20
     */
    AfterSalesInvoiceVo getAfterReturnInvoiceInfo(AfterSalesInvoiceVo afterSalesInvoiceVo);

    /**
     * <b>Description:</b><br> 查询销售发票中产品信息
     *
     * @return
     * @Note <b>Author:</b> duke
     * <br><b>Date:</b> 2017年10月25日 下午5:50:49
     */
    List<AfterSalesGoodsVo> getAfterReturnInvoiceGoodsList(AfterSalesInvoiceVo afterSalesInvoiceVo);

    /**
     * <b>Description:</b><br> 根据售后ID修改退票状态
     *
     * @return
     * @Note <b>Author:</b> duke
     * <br><b>Date:</b> 2017年10月27日 下午1:00:27
     */
    int updaeAfterInvoiceStatus(@Param(value = "afterSalesInvoiceId") Integer afterSalesInvoiceId);

    /**
     * <b>Description:</b><br> 财务-售后-安调-付款申请记录
     *
     * @param afterSalesId
     * @return
     * @Note <b>Author:</b> duke
     * <br><b>Date:</b> 2017年10月30日 下午3:29:11
     */
    List<PayApply> getAfterAtPaymentApply(@Param(value = "afterSalesId") Integer afterSalesId);

    /**
     * <b>Description:</b><br> 获取售后安调开具发票信息
     *
     * @param afterSalesGoodsVo
     * @return
     * @Note <b>Author:</b> duke
     * <br><b>Date:</b> 2017年10月31日 上午10:00:40
     */
    AfterSalesGoodsVo getAfterSalesGoodsAtInfo(AfterSalesGoodsVo afterSalesGoodsVo);

    /**
     * <b>Description:</b><br> 获取售后安调-已开票信息
     *
     * @param afterSalesId
     * @return
     * @Note <b>Author:</b> duke
     * <br><b>Date:</b> 2017年10月31日 上午10:38:56
     */
    Map<String, BigDecimal> getAfterOpenInvoiceInfoAt(@Param(value = "afterSalesId") Integer afterSalesId);

    /**
     * <b>Description:</b><br> 根据售后单号获取售后详情记录
     *
     * @param afterSalesId
     * @return
     * @Note <b>Author:</b> duke
     * <br><b>Date:</b> 2017年11月2日 下午2:51:46
     */
    AfterSalesDetailVo getAfterSalesDetail(@Param(value = "afterSalesId") Integer afterSalesId);

    /**
     * <b>Description:</b><br> 修改售后订单详情
     *
     * @param asd
     * @return
     * @Note <b>Author:</b> duke
     * <br><b>Date:</b> 2017年11月7日 下午4:44:16
     */
    int updateAfterSalesDetail(AfterSalesDetail asd);

    /**
     * <b>Description:</b><br> 根据售后单号查询开票状态
     *
     * @param afterIdList
     * @return
     * @Note <b>Author:</b> duke
     * <br><b>Date:</b> 2017年11月8日 上午9:08:56
     */
    List<Map<String, Integer>> getAfterOpenInvoiceStatus(@Param(value = "afterIdList") List<Integer> afterIdList);

    /**
     * <b>Description:</b><br> 根据流水类型查询流水总额
     *
     * @param cb
     * @return
     * @Note <b>Author:</b> duke
     * <br><b>Date:</b> 2017年11月22日 下午4:51:52
     */
    BigDecimal getCapitalBillTotalAmount(CapitalBill cb);

    /**
     * <b>Description:</b><br> 根据售后单号查询售后产品
     *
     * @param afterSalesId
     * @return
     * @Note <b>Author:</b> Administrator
     * <br><b>Date:</b> 2017年12月7日 下午3:52:46
     */
    List<AfterSalesGoods> getSaleAfterGoodsListByAfterId(@Param("afterSalesId") Integer afterSalesId);

    /**
     * <b>Description:</b><br> 根据售后单号查询售后详情
     *
     * @param afterInvoiceId
     * @return
     * @Note <b>Author:</b> Administrator
     * <br><b>Date:</b> 2017年12月14日 下午5:21:14
     */
    AfterSales getAfterSalesByAfterInvoiceId(@Param("afterInvoiceId") Integer afterInvoiceId);

    /**
     * <b>Description:</b><br>
     *
     * @param afterSalesId
     * @return
     * @Note <b>Author:</b> Administrator
     * <br><b>Date:</b> 2017年12月15日 上午11:09:49
     */
    AfterSales getAfterSalesByAfterId(@Param("afterSalesId") Integer afterSalesId);

    /**
     * <b>Description:</b><br> 修改在供应商处的余额
     *
     * @param traderId
     * @param amount
     * @return
     * @Note <b>Author:</b> duke
     * <br><b>Date:</b> 2018年2月10日 下午9:10:11
     */
    Integer updateSupplyBalance(@Param("traderId") Integer traderId, @Param("amount") BigDecimal amount);

    /**
     * <b>Description:</b><br> 修改售后申请开票状态
     *
     * @return
     * @Note <b>Author:</b> duke
     * <br><b>Date:</b> 2018年4月17日 上午9:10:09
     */
    int updateAfterOpenInvoiceApply(Invoice invoice);

    /**
     * <b>Description:</b><br> 查询售后退货数量
     *
     * @param companyId
     * @param afterType 535销售   536采购
     * @return
     * @Note <b>Author:</b> duke
     * <br><b>Date:</b> 2018年5月9日 下午6:50:35
     */
    Integer getGoodsAfterReturnNum(@Param("saleorderGoodsId") Integer saleorderGoodsId, @Param("companyId") Integer companyId, @Param("afterType") Integer afterType);

    /**
     * <b>Description:</b><br> 查询售后退货数量
     *
     * @param companyId
     * @param afterType 535销售   536采购
     * @return
     * @Note <b>Author:</b> duke
     * <br><b>Date:</b> 2018年5月9日 下午6:50:35
     */
    Integer getGoodsAfterReturnNumNew(@Param("saleorderGoodsId") Integer saleorderGoodsId, @Param("companyId") Integer companyId, @Param("afterType") Integer afterType);

    /**
     * 查询售后退货数量(批量)
     * <b>Description:</b><br>
     *
     * @param goodsIds
     * @param companyId
     * @param afterType
     * @return
     * @Note <b>Author:</b> Cooper
     * <br><b>Date:</b> 2018年7月27日 下午2:15:50
     */
    List<AfterSalesGoods> getGoodsAfterReturnNumByIdList(@Param("goodsIds") List<Integer> goodsIds,
                                                         @Param("companyId") Integer companyId, @Param("afterType") Integer afterType);

    /**
     * <b>Description:</b><br>
     *
     * @param map
     * @return
     * @Note <b>Author:</b> Cooper
     * <br><b>Date:</b> 2018年6月27日 下午2:59:41
     */
    List<AfterSalesGoods> getGoodsAfterReturnNumBuyList(Map<String, Object> map);

    /**
     * <b>Description:</b><br> 查询售后已退票信息
     *
     * @param afterSalesId
     * @return
     * @Note <b>Author:</b> duke
     * <br><b>Date:</b> 2018年5月21日 上午10:06:45
     */
    List<Invoice> getAfterReturnInvoiceList(@Param(value = "orderId") Integer orderId,
                                            @Param(value = "afterSalesId") Integer afterSalesId, @Param(value = "type") Integer type);

    /**
     * <b>Description:</b><br> 售后开票后修改-售后单中开票状态
     *
     * @return
     * @Note <b>Author:</b> duke
     * <br><b>Date:</b> 2018年6月1日 下午1:40:08
     */
    int updateAfterDetailInvoiceStatus(AfterSalesDetailVo afterSalesDetailVo);

    /**
     * <b>Description:</b><br> 根据售后单ID修改发票退票状态（电子票全部退，也要一次全部作废）
     *
     * @param afterSalesId
     * @return
     * @Note <b>Author:</b> duke
     * <br><b>Date:</b> 2018年7月4日 下午4:48:10
     */
    int updateAfterInvoiceStatus(@Param(value = "afterSalesId") Integer afterSalesId, @Param(value = "invoiceId") Integer invoiceId,
                                 @Param("updater") Integer updater, @Param("modTime") Long modTime);

    /**
     * <b>Description:查询红字有效的已开票总额</b><br>
     *
     * @param
     * @return
     * @Note <b>Author:</b> Barry
     * <br><b>Date:</b> 2018年08月14日 09:09
     */
    BigDecimal selectRedInvoiceAmountCount(@Param("invoiceId") Integer invoiceId);

    /**
     * 根据售后单id查询发票退票商品
     * <AUTHOR>
     * @param afterSalesId
     * @return
     */
    List<AfterSalesGoodsVo> queryTpAfterSalesGoods(@Param("afterSalesId")Integer afterSalesId);
}
