package com.vedeng.erp.aftersale.facade;

import com.vedeng.erp.aftersale.dto.CreateRefundApplyRequest;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 销售售后订单相关
 * @date 2024/9/2 9:58
 */
public interface AfterSalesOrderFacade {

    /**
     * 新增退款申请
     *
     * @param createRefundApplyRequest 请求参数
     */
    void createRefundApply(CreateRefundApplyRequest createRefundApplyRequest);

    /**
     * 判断是否可以原路退回
     * 1. 售后订单来源：耗材  SOURCE = 1
     * 2. 售后类型：退货 TYPE = 539;
     * 3. 销售单支付方式：线上
     * 4. 销售单支付类型：支付宝或微信
     * 5. 退还方式 = 退还账户
     *
     * @param afterSalesId afterSalesId
     * @return boolean
     */
    boolean isRefundableToOrigin(Integer afterSalesId);
}
