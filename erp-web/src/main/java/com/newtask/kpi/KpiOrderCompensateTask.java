package com.newtask.kpi;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.kpi.dao.KpiOrderLogMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/18 9:16
 */
@Component
@JobHandler(value = "kpiOrderCompensateTask")
@Slf4j
public class KpiOrderCompensateTask extends AbstractJobHandler {

    @Resource
    private KpiOrderLogMapper kpiOrderLogMapper;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        LocalDate localDate = LocalDate.now().minusDays(1);
        Integer logNum = kpiOrderLogMapper.getKpiOrderLogNum(localDate);
        if (logNum < 1) {
            log.error("五行报表T_KPI_ORDER_LOG " + localDate + "无数据，请关注");
        } else {
            log.info("五行报表T_KPI_ORDER_LOG " + localDate + "有数据，正常");
        }
        Integer dailyCount = kpiOrderLogMapper.getKpiDailyCount(localDate);
        if (dailyCount < 1) {
            log.error("五行报表T_KPI_DAILY_COUNT " + localDate + "无数据，请关注");
        } else {
            log.info("五行报表T_KPI_DAILY_COUNT " + localDate + "有数据，正常");
        }
        return SUCCESS;
    }
}
