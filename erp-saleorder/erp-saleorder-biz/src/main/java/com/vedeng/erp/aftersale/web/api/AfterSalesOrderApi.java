package com.vedeng.erp.aftersale.web.api;

import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.annotation.NoRepeatSubmit;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.erp.aftersale.domain.dto.AfterSalesRefundInfoDto;
import com.vedeng.erp.aftersale.dto.CreateRefundApplyRequest;
import com.vedeng.erp.aftersale.facade.AfterSalesOrderFacade;
import com.vedeng.erp.aftersale.service.AfterSalesCommonService;
import com.vedeng.erp.finance.api.CustomerAccountApiService;
import com.vedeng.erp.finance.dto.CustomerAccountReqDto;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import com.vedeng.erp.trader.api.CustomerAccountApi;
import com.vedeng.erp.trader.dto.CustomerBankAccountApiDto;
import com.vedeng.erp.trader.dto.CustomerBankAccountQueryReqDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * 销售售后信息
 *
 * <AUTHOR>
 */
@ExceptionController
@RestController
@Slf4j
@RequestMapping("/afterSalesOrder")
public class AfterSalesOrderApi {

    @Autowired
    private AfterSalesCommonService afterSalesCommonService;
    @Autowired
    private CustomerAccountApiService customerAccountApiService;
    @Autowired
    private CustomerAccountApi customerAccountApi;
    @Autowired
    private AfterSalesOrderFacade afterSalesOrderFacade;
    @Autowired
    private SaleOrderApiService saleOrderApiService;


    /**
     * 售后退款信息详情
     */
    @RequestMapping("/refundDetail")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<AfterSalesRefundInfoDto> refundDetail(Integer afterSalesId) {
        AfterSalesRefundInfoDto afterSaleApplyInfoDto = afterSalesCommonService.refundDetail(afterSalesId);
        return R.success(afterSaleApplyInfoDto);
    }

    /**
     * 更新售后退款信息
     */
    @RequestMapping("/updateRefundInfo")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<Void> updateRefundInfo(@RequestBody AfterSalesRefundInfoDto afterSalesRefundInfoDto, HttpServletRequest request) {
        afterSalesCommonService.updateRefundInfo(afterSalesRefundInfoDto, request);
        return R.success();
    }

    /**
     * 销售售后付款申请：获取客户账户信息
     */
    @RequestMapping("/getCustomerBankAccount")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<List<CustomerBankAccountApiDto>> getCustomerBankAccount(@RequestParam("afterSalesId") Integer afterSalesId,
                                                                     @RequestParam("accountType") Integer accountType) {
        List<CustomerBankAccountApiDto> customerBankAccount = customerAccountApiService.getCustomerAccount(new CustomerAccountReqDto(afterSalesId, accountType));
        return R.success(customerBankAccount);
    }

    /**
     * 销售售后付款申请：获取客户账户信息
     */
    @RequestMapping("/getCustomerBankAccountByBankAccount")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<List<CustomerBankAccountApiDto>> getCustomerBankAccountByBankAccount(CustomerBankAccountQueryReqDto queryReqDto) {
        SaleorderInfoDto saleorderInfoDto = saleOrderApiService.getByAfterSaleId(queryReqDto.getAfterSalesId());
        if (saleorderInfoDto == null) {
            return R.error("订单不存在");
        }
        queryReqDto.setTraderId(saleorderInfoDto.getTraderId());
        List<CustomerBankAccountApiDto> customerBankAccount = customerAccountApi.query(queryReqDto);
        return R.success(customerBankAccount);
    }

    /**
     * 销售售后付款申请：更新客户账户信息使用时间
     */
    @RequestMapping("/updateLastUseTime")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<Void> updateLastUseTime(@RequestParam("customerBankAccountId") Long customerBankAccountId) {
        customerAccountApiService.updateLastUseTime(customerBankAccountId);
        return R.success();
    }

    /**
     * 销售售后付款申请：创建退款申请
     */
    @RequestMapping("/createRefundApply")
    @ResponseBody
    @NoNeedAccessAuthorization
    @NoRepeatSubmit
    public R<Void> createRefundApply(@RequestBody CreateRefundApplyRequest createRefundApplyRequest) {
        afterSalesOrderFacade.createRefundApply(createRefundApplyRequest);
        return R.success();
    }

    /**
     * 判断是否可以原路退回
     */
    @RequestMapping("/isRefundableToOrigin")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<Boolean> isRefundableToOrigin(Integer afterSalesId) {
        return R.success(afterSalesOrderFacade.isRefundableToOrigin(afterSalesId));
    }
}
