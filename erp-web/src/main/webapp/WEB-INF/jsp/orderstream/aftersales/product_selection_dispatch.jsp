<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>下派商品选择</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/webjars/ezadmin/plugins/layui/css/layui.css" media="all">
    <script src="${pageContext.request.contextPath}/webjars/ezadmin/layui/js/core.js?rnd=${resourceVersionKey}"></script>
    <style>

        .layui-table-grid-down {
            display: none;
        }
        .layui-icon {
            height: auto;
            background: none;
        }
    </style>
</head>
<body>
<table id="userTable" lay-filter="userTable"></table>
<div class="layui-form-item">
    <label class="layui-form-label" style="width: 100px;">*客服备注:</label>
    <div class="layui-input-block" style="margin-left: 100px;">
        <input type="text" name="afterSalesQuantity" id="afterSalesQuantity" class="layui-input" placeholder="请输入备注,500字以内" maxlength="499" autocomplete="off">
    </div>
</div>
<script>
    layui.use('table', function () {
        var table = layui.table;

        // 获取后端传递过来的用户数据
        var products = ${products};

        // 渲染表格
        table.render({
            elem: '#userTable',
            data: products,
            height: '400px', // 设置表格高度，单位为像素
            limit:1000,
            cols: [[
                {type: 'checkbox', fixed: 'left',title: '选择'},
                {field: 'productName', title: '产品名称'},
                {field: 'productBrand', title: '品牌'},
                {field: 'productModel', title: '型号'},
                {field: 'afterSalesQuantity', title: '下派数量'}
            ]]
        });
    });
</script>
<%@ include file="../../common/footer.jsp"%>
</body>
</html>
