package com.vedeng.infrastructure.bank.api.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 文件token响应
 * @date 2024/10/11 9:44
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@XmlAccessorType(XmlAccessType.FIELD)
public class B2eGetTokenReq {

    /**
     * 客户端交易的唯一标志
     */
    private String trnId;
}
