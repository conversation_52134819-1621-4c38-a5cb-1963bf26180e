package com.vedeng.finance.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.ConfigService;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.common.redis.utils.RedisUtil;
import com.vedeng.erp.finance.dto.*;
import com.vedeng.erp.finance.service.BankBillApiService;
import com.vedeng.erp.finance.service.BankBillIgnoreRecordApiService;
import com.vedeng.erp.finance.service.PayVedengBankApiService;
import com.vedeng.erp.kingdee.enums.KingdeeIgnoreBillTypeEnums;
import com.vedeng.erp.kingdee.enums.KingdeeIgnoreUnitTypeEnums;
import com.vedeng.finance.service.BankBillAutoIgnoreService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/8 16:21
 **/
@Service
@Slf4j
public class BankBillAutoIgnoreServiceImpl implements BankBillAutoIgnoreService {

    @Autowired
    private BankBillApiService bankBillApiService;

    @Autowired
    private PayVedengBankApiService payVedengBankApiService;

    @Autowired
    private BankBillIgnoreRecordApiService bankBillIgnoreRecordApiService;

    private List<String> PARENT_COMPANY = CollUtil.newArrayList("南京贝登医疗股份有限公司");




    private final static String REDIS_KEY_IGNORE_LOAD_TOTAL = "ERP:BATCH:IGNORE:LOADING:TOTAL";
    private final static String REDIS_KEY_IGNORE_LOAD_DONE = "ERP:BATCH:IGNORE:LOADING:DONE";


    @Override
    public void doWithPage(Date beginTime, Date endTime, boolean loading) {
        // 分页
        int pageSize = 100;
        int pageNum = 1;
        PageParam<BankBillQueryDto> page = new PageParam<>();
        page.setPageSize(pageSize);
        page.setPageNum(pageNum);
        BankBillQueryDto bankBillDto = new BankBillQueryDto();
        //1）非手续费：T_BANK_BILL.IS_FEE = 0 OR T_BANK_BILL.IS_FEE IS NULL
        //2）未忽略：T_BANK_BILL.STATUS = 0
        //3）未结算：T_BANK_BILL.MATCHED_AMOUNT = 0
        //4）摘要含有"A资金划转#"：T_BANK_BILL.MESSAGE LIKE '%A资金划转#%'
        bankBillDto.setBeginTime(beginTime);
        bankBillDto.setEndTime(endTime);
        bankBillDto.setMessage("A资金划转#");
        page.setParam(bankBillDto);

        PageInfo<BankBillDto> data = bankBillApiService.queryIgnoreBankBill(page);
        // 总条数
        if (loading) {
            long total = data.getTotal();
            RedisUtil.KeyOps.delete(REDIS_KEY_IGNORE_LOAD_TOTAL);
            RedisUtil.KeyOps.delete(REDIS_KEY_IGNORE_LOAD_DONE);
            RedisUtil.StringOps.incrBy(REDIS_KEY_IGNORE_LOAD_TOTAL, total);
            RedisUtil.StringOps.incrBy(REDIS_KEY_IGNORE_LOAD_DONE, 0);
        }

        pageNum = data.getPages();

        while (true) {
            log.info("分页执行进度 {},{}", pageSize, pageNum);
            // 调用开票
            PageParam<BankBillQueryDto> pageDes = new PageParam<>();
            pageDes.setPageSize(pageSize);
            pageDes.setPageNum(pageNum);
            pageDes.setParam(bankBillDto);
            PageInfo<BankBillDto> bankBillDtoPageInfo = bankBillApiService.queryIgnoreBankBill(pageDes);
            List<BankBillDto> list = bankBillDtoPageInfo.getList();
            if (list.isEmpty()) {
                break;
            }
            list.forEach(x-> {
                try {
                    doIgnore(x);
                } catch (Exception e) {
                    log.error("流水忽略：{}, doWithPage 执行失败", JSON.toJSONString(x),e);
                }
                if (loading) {
                    RedisUtil.StringOps.incrBy(REDIS_KEY_IGNORE_LOAD_DONE, 1);
                }

            });

            if (!bankBillDtoPageInfo.isHasPreviousPage()) {
                break;
            }
            pageNum = bankBillDtoPageInfo.getPrePage();
            log.info("分页查询进度 {},{}", pageSize, pageNum);
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    public void doIgnore(BankBillDto data) {


        BankBillDto bankBillDto = bankBillApiService.checkIgnoreBankBill(data.getBankBillId());
        if (Objects.isNull(bankBillDto)) {
            return;
        }

        boolean pContain = PARENT_COMPANY.contains(data.getAccName1());
        List<String> SUB_COMPANY = StrUtil.split(ConfigService.getAppConfig().getProperty("SUB_COMPANY", ""), StrUtil.COMMA);
        boolean cContain = SUB_COMPANY.contains(data.getAccName1());
        if (!pContain && !cContain) {
            return;
        }

        PayVedengBankDto payVedengBankDto = payVedengBankApiService.queryBankInfo(data.getBankTag());
        String payBankNo = payVedengBankDto.getPayBankNo();

        //T_BANK_BILL_IGNORE_RECORD新增记录
        BankBillIgnoreRecordDto addDto = new BankBillIgnoreRecordDto();
        addDto.setBankBillId(data.getBankBillId());
        addDto.setAccName(data.getAccName1());
        addDto.setContactUnitType(KingdeeIgnoreUnitTypeEnums.BANK.getUnitType());
        //ContactUnitNo用erp维护的银行编码
        addDto.setContactUnitNo(payVedengBankApiService.getKingDeeBankCodeByBankNo(payBankNo));

        addDto.setContactUnit(payBankNo);
        addDto.setTradeSubject("对公");
        addDto.setIgnoreReason(pContain?KingdeeIgnoreBillTypeEnums.CURRENT_ACCOUNTS_VEDENG.getSysId():KingdeeIgnoreBillTypeEnums.CURRENT_ACCOUNTS_VEDENG_SON.getSysId());
        addDto.setIsDelete(ErpConst.ZERO);
        addDto.setAddTime(new Date());
        addDto.setModTime(new Date());
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        addDto.setCreator(currentUser.getId());
        addDto.setCreatorName(currentUser.getUsername());
        addDto.setUpdater(currentUser.getId());
        addDto.setUpdaterName(currentUser.getUsername());
        addDto.setType(1);
        bankBillIgnoreRecordApiService.insertSelective(addDto);
        //原业务'忽略'逻辑
        BankBillDto bankBill = new BankBillDto();
        bankBill.setBankBillId(data.getBankBillId());
        bankBill.setMatchedObject(addDto.getIgnoreReason());
        bankBill.setStatus(ErpConst.ONE);
        bankBillApiService.update(bankBill);


    }
}
