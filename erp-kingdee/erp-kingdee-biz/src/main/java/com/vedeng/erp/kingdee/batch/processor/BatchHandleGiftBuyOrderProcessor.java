package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.BatchAfterSalesDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchBuyorderDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInItemDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDto;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BatchHandleGiftBuyOrderProcessor implements ItemProcessor<BatchWarehouseGoodsOutInDto, KingDeeStorageInDto> {
    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;
    @Autowired
    private BatchAfterSalesDtoMapper batchAfterSalesDtoMapper;
    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;

    @Autowired
    private BatchBuyorderDtoMapper batchBuyorderDtoMapper;

    @Override
    public KingDeeStorageInDto process(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto) throws Exception {
        //ERP_LV_2023_48排除借用采购换货入库类型的数据,updateRemark = '21-22其他'
        if ("21-22其他".equals(batchWarehouseGoodsOutInDto.getUpdateRemark())){
            log.info("排除借用采购换货入库类型的数据,{}",JSON.toJSONString(batchWarehouseGoodsOutInDto));
            return null;
        }
        KingDeeStorageInDto dto = new KingDeeStorageInDto();
        dto.setFBillNo(batchWarehouseGoodsOutInDto.getOutInNo());
        // 判断是否数据已存在
        boolean old = kingDeeBaseApi.isExist(dto);
        if(old){
            log.info("采购赠品单换货出入库,数据已存在:{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));
            return null;
        }

        // 补充详细单数据
        batchWarehouseGoodsOutInDto.setBatchWarehouseGoodsOutInItemDtos(batchWarehouseGoodsOutInItemDtoMapper.findByOutInNo(batchWarehouseGoodsOutInDto.getOutInNo()));
        log.info("采购赠品单换货出入库 BatchHandleGiftBuyOrderProcessor：{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));
        // 根据入库单关系 获取售后单
        BatchAfterSalesDto batchAfterSalesDto = batchAfterSalesDtoMapper.findByAfterSalesNoAndSubjectType(
                batchWarehouseGoodsOutInDto.getRelateNo(), 536);
        if (Objects.isNull(batchAfterSalesDto)) {
            return null;
        }
        BatchBuyorderDto batchBuyorderDto = batchBuyorderDtoMapper.selectByBuyorderNo(batchAfterSalesDto.getOrderNo());
        if (Objects.isNull(batchBuyorderDto)) {
            log.warn("未查到采购赠品单售后单原采购单信息");
            return null;
        }
        log.info("采购赠品单售后换货,组装数据,采购单号:{},采购单信息:{}",batchAfterSalesDto.getOrderNo(), JSONUtil.toJsonStr(batchBuyorderDto));
        Map<Integer, BatchAfterSalesGoodsDto> map = batchAfterSalesDto.getBatchAfterSalesGoodsDtoList()
                .stream().collect(Collectors.toMap(BatchAfterSalesGoodsDto::getAfterSalesGoodsId, c -> c, (k1, k2) -> k1));
        dto.setFId("0");
        dto.setFBillNo(batchWarehouseGoodsOutInDto.getOutInNo());
        dto.setFDate(DateUtil.formatDate(batchWarehouseGoodsOutInDto.getOutInTime()));
        if (ErpConstant.SEVEN.equals(batchWarehouseGoodsOutInDto.getOutInType())){
            dto.setFStockDirect("RETURN");
        }else {
            dto.setFStockDirect("GENERAL");
        }
        dto.setFSupplierId(batchBuyorderDto.getTraderSupplierId().toString());
        dto.setFQzokBddjtId(batchWarehouseGoodsOutInDto.getWarehouseGoodsOutInId().toString());


        List<KingDeeStorageInDetailDto> detailDtoList = new ArrayList<>();
        List<BatchWarehouseGoodsOutInItemDto> byOutInNo = batchWarehouseGoodsOutInItemDtoMapper.findByOutInNo(batchWarehouseGoodsOutInDto.getOutInNo());
        if (CollUtil.isEmpty(byOutInNo)) {
            log.error("未能查到出入库单no:{}子单信息",JSON.toJSONString(batchWarehouseGoodsOutInDto.getOutInNo()));
            throw new KingDeeException("未能查到出入库单no:" + JSON.toJSONString(batchWarehouseGoodsOutInDto.getOutInNo())+"子单信息");
        }
        batchWarehouseGoodsOutInDto.setBatchWarehouseGoodsOutInItemDtos(byOutInNo);
        batchWarehouseGoodsOutInDto.getBatchWarehouseGoodsOutInItemDtos().forEach(l -> {
            KingDeeStorageInDetailDto detailDto = new KingDeeStorageInDetailDto();
            BatchAfterSalesGoodsDto afterSalesGoodsDto = map.get(l.getRelatedId());
            if (Objects.isNull(afterSalesGoodsDto)) {
                log.error("采购赠品售后换货未能查到具体的商品明细：{}",JSON.toJSONString(l));
                throw new KingDeeException("采购赠品售后换货未能查到具体的商品明细");
            }
            detailDto.setFMaterialId(afterSalesGoodsDto.getSku());
            detailDto.setFStockId("CK9997");
            BigDecimal num = l.getNum().abs();
            detailDto.setFQty(num.toString());
            detailDto.setFPrice("0.01");
            detailDto.setFAmount(new BigDecimal("0.01").multiply(num).toString());
            detailDto.setFQzokYsddh(batchAfterSalesDto.getOrderNo());
            //VDERP-14859 调整 setFQzokXlh setFQzokSfzf
            detailDto.setFQzokGsywdh(batchAfterSalesDto.getAfterSalesNo());
            if (ErpConstant.SEVEN.equals(batchWarehouseGoodsOutInDto.getOutInType())){
                detailDto.setFQzokYwlx("采购赠品售后换货出库");
            }else {
                detailDto.setFQzokYwlx("采购赠品售后换货入库");
            }
            detailDto.setFQzokPch(l.getBatchNumber());
            detailDto.setFQzokXlh(l.getBarcodeFactory());
            detailDto.setFQzokSfzf(afterSalesGoodsDto.getDeliveryDirect().equals(0) ? "否" : "是");
            detailDto.setFQzokBddjhId(l.getWarehouseGoodsOutInDetailId().toString());
            detailDto.setFQzokSqlx("");

            detailDtoList.add(detailDto);
        });

        dto.setFEntity(detailDtoList);


        return dto;
    }
}
