package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/7 13:14
 */
@NoArgsConstructor
@Data
@Getter
@Setter
public class KingDeeSaleOutStockCommand {
    /**
     * 单据内码
     */
    private Integer fid;
    /**
     * 单据类型
     */
    private KingDeeNumberCommand fBillTypeID = new KingDeeNumberCommand();
    /**
     * 单据编号
     */
    private String fBillNo;
    /**
     * 贝登单据头ID
     */
    private String F_QZOK_BDDJTID;
    /**
     * 归属部门
     */
    private String F_QZOK_GSBM;
    /**
     * 单据日期
     */
    private String fDate;
    /**
     * 销售组织
     */
    private KingDeeNumberCommand fSaleOrgId = new KingDeeNumberCommand();
    /**
     * 库存组织
     */
    private KingDeeNumberCommand fStockOrgId = new KingDeeNumberCommand();
    /**
     * 客户编码
     */
    private KingDeeNumberCommand fCustomerID = new KingDeeNumberCommand();
    /**
     * fEntity
     */
    private List<FEntity> fEntity;

    /**
     * FEntity
     */
    @NoArgsConstructor
    @Data
    public static class FEntity {

        private String FENTRYID;
        /**
         * 物料编码
         */
        private KingDeeNumberCommand fMaterialID = new KingDeeNumberCommand();
        /**
         * 实发数量
         */
        private BigDecimal fRealQty;
        /**
         * 是否赠品 销售：false     赠品 true
         */
        private Boolean fIsFree;
        /**
         * 含税单价
         */
        private BigDecimal fTaxPrice;
        /**
         * 税率
         */
        private BigDecimal fEntryTaxRate;
        /**
         * 仓库 填写仓库编码,默认ck9999
         */
        private KingDeeNumberCommand fStockID = new KingDeeNumberCommand();
        /**
         * 是否直发
         */
        private String F_QZOK_SFZF;
        /**
         * 原始订单号
         */
        private String F_QZOK_YSDDH;
        /**
         * 归属业务单号
         */
        private String F_QZOK_GSYWDH;
        /**
         * 业务类型
         */
        private String F_QZOK_YWLX;
        /**
         * 批次号
         */
        private String F_QZOK_PCH;
        /**
         * 序列号
         */
        private String F_QZOK_XLH;
        /**
         * 是否安调 填写文本“是”or “否”
         */
        private String F_QZOK_SFAT2;
        /**
         * 贝登订单行ID
         */
        private String F_QZOK_BDDJHID;

    }
}