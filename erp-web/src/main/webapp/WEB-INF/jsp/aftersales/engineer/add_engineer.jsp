<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增售后服务公司" scope="application" />
<%@ include file="../../common/common.jsp"%>
<div class="content mt10 ">
	<div class="parts">
		<div class="title-container title-container-blue">
			<div class="table-title nobor">新增售后服务公司</div>
		</div>
<div class="formpublic formpublic1">
	<input id="companyName" type="hidden" value="${companyName }">
	<form method="post" action="${pageContext.request.contextPath}/aftersales/engineer/saveadd.do" id="myform">
		<ul>
			<li>
				<div class="form-tips">
					<span>*</span>
					<lable>公司名称</lable>
				</div>
				<div class="f_left">
					<div class='inputfloat'>
						<span class="none" id="name"></span>
						<label class="bt-bg-style bg-light-blue bt-small none" onclick="research();"
							   id="research">重新搜索</label>
						<input type="text" placeholder="请输入供应商名称" class="input-largest" name="searchTraderName"
							   id="searchTraderName" >
						<label class="bt-bg-style bg-light-blue bt-small" onclick="searchSupplier();"
							   id="errorMes">搜索</label>
						<span style="display:none;">
									<!-- 弹框 -->
									<div class="title-click nobor  pop-new-data" id="popSupplier"></div>
								</span>
						<input type="hidden" id="traderSupplierId" name="traderSupplierId"/>
						<input type="hidden" id="traderId" name="traderId"/>
						<input type="hidden" id="traderName" name="traderName" />
						<input type="hidden" id="owner" name="owner" value="2"/>
						<input type="hidden" name="name"/>
					</div>
					<div class="font-red none"></div>
				</div>

			</li>
			<li>
				<div class="form-tips">
					<lable>开户银行</lable>
				</div>
				<div class="f_left ">
					<div class="form-blanks">
						<input type="text" class=" input-large" name="bank" id="bank" />
					</div>
				</div>
			</li>
			<li>
				<div class="form-tips">
					<lable>开户账号</lable>
				</div>
				<div class="f_left ">
					<div class="form-blanks">
						<input type="text" onkeyup="this.value=this.value.replace(/[^\d]/g,'') " onafterpaste="this.value=this.value.replace(/[^\d]/g,'')"
							   placeholder="账号只允许输入阿拉伯数字" class=" input-large" name="bankAccount" id="bankAccount" />
					</div>
				</div>
			</li>
			<li>
				<div class="form-tips">
					<lable>开户支行联行号</lable>
				</div>
				<div class="f_left ">
					<div class="form-blanks">
						<input type="text" onkeyup="this.value=this.value.replace(/[^\d]/g,'') " onafterpaste="this.value=this.value.replace(/[^\d]/g,'')"
							   placeholder="账号只允许输入阿拉伯数字" class=" input-large" name="bankCode" id="bankCode" />
					</div>
				</div>
			</li>
		</ul>
		<div class="add-tijiao tcenter mt15">
			<input type="hidden" name="formToken" value="${formToken}"/>
			<button type="submit" id="submit_add">提交</button>
		</div>
	</form>
</div>
	</div>
</div>
<script type="text/javascript"
	src="${pageContext.request.contextPath}/static/js/aftersales/engineer/add_engineer.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
		src="${pageContext.request.contextPath}/static/js/aftersales/engineer/search_company.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
	src="${pageContext.request.contextPath}/static/js/region/index.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp"%>