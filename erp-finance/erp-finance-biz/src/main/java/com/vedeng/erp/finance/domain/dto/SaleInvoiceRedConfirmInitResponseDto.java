package com.vedeng.erp.finance.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * 红字确认单初始化出参
 */
@Data
public class SaleInvoiceRedConfirmInitResponseDto extends TaxesReturnInfo {
    /**
     * 返回值
     */
    private String code;

    /**
     * 消息报文
     */
    private String message;

    /**
     * 销售方税号
     */
    private String xsfnsrsbh;

    /**
     * 销售方名称
     */
    private String xsfmc;

    /**
     * 购买方税号
     */
    private String gmfnsrsbh;

    /**
     * 购买方名称
     */
    private String gmfmc;

    /**
     * 蓝字发票代码
     */
    private String lzfpdm;

    /**
     * 蓝字发票号码
     */
    private String lzfphm;

    /**
     * 开票日期 (YYYY-MM-DD hh:mm:ss)
     */
    private String lzkprq;

    /**
     * 蓝字合计金额
     */
    private double lzhjje;

    /**
     * 蓝字合计税额
     */
    private double lzhjse;

    /**
     * 发票来源代码
     */
    private String fplyDm;

    /**
     * 发票入账状态代码 (00 未入账, 01 已入账)
     */
    private String fprzztDm;

    /**
     * 蓝字发票代码 (01 增值税专用发票, 02 普通发票, ...)
     */
    private String lzfppzDm;

    /**
     * 开票方纳税人识别号
     */
    private String kpfnsrsbh;

    /**
     * 归集标签 (1 开具发票, 2 取得发票)
     */
    private String gjbq;

    /**
     * 红字冲销金额
     */
    private String hzcxje;

    /**
     * 红字冲销税额
     */
    private String hzcxse;

    /**
     * 蓝字发票特定要素类型代码
     */
    private String lzfpTdyslxDm;

    /** 蓝字发票代码2 (81-数电发票（增值税专用发票）, 82-数电发票（普通发票）, ...) */
    private String lzfplxDm2;

    /** 增值税用途代码 (00 已勾选未确认, 01 已确认, 03 未勾选) */
    private String zzsytDm;

    private String ybfhcbz;

    /**
     * 红字确认单明细数量
     */
    private String hzqrdmxsl;

    /**
     * 开具发票方省市级税务机关代码
     */
    private String xsfssjswjgDm;

    /**
     * 开具发票方地市级税务机关代码
     */
    private String xsfdsjswjgDm;

    /**
     * 是否“确认即开票”标识 (Y 是, N 否)
     */
    private String qrjkpbz;

    /**
     * 确认即开票可修改标识 (Y 可修改项目信息, N 不可修改项目信息)
     */
    private String qrjkpxgbz;

    /**
     * 确认即开票标识
     */
    private String qrjkpBztxBz;

    /**
     * 明细列表
     */
    private List<Hzqrxxmx> hzqrxxmxList;

    @Data
    public static class Hzqrxxmx {

        /**
         * 序号 (表示蓝票序号)
         */
        private String xh;

        /**
         * 项目名称
         */
        private String xmmc;

        /**
         * 项目简称
         */
        private String spfwjc;

        /**
         * 项目全称
         */
        private String hwhyslwfwmc;

        /**
         * 税收分类编码
         */
        private String sphfwssflhbbm;

        /**
         * 规格型号
         */
        private String ggxh;

        /**
         * 单位
         */
        private String dw;

        /**
         * 单价
         */
        private String fpspdj;

        /**
         * 数量
         */
        private String fpspsl;

        /**
         * 金额
         */
        private String je;

        /**
         * 税率
         */
        private String sl1;

        /**
         * 税额
         */
        private String se;
    }
}
