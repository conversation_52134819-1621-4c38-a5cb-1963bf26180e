package com.vedeng.erp.kingdee.batch.processor;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.domain.FileInfoDto;
import com.vedeng.common.core.utils.FileInfoUtils;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchBankBillDto;
import com.vedeng.erp.kingdee.dto.KingDeeAliReceiveBillDto;
import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import com.vedeng.erp.kingdee.service.KingDeeAliReceiveBillService;
import com.vedeng.erp.kingdee.service.KingDeeFileDataService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 支付宝收款单附件
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class BatchAliPayReceiptFormProcessor extends BaseProcessor<BatchBankBillDto, KingDeeFileDataDto> {

    public static final String ZERO = "0";

    @Value("${oss_http}")
    private String ossHttp;

    @Autowired
    private KingDeeAliReceiveBillService kingDeeAliReceiveBillService;

    @Autowired
    private KingDeeFileDataService kingDeeFileDataService;

    @Override
    public KingDeeFileDataDto doProcess(BatchBankBillDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("开始处理支付宝回单附件：{}", JSON.toJSONString(dto));
        if (StringUtils.isBlank(dto.getReceiptUrl())) {
            log.info("该支付宝流水不存在回单信息");
            return null;
        }

        KingDeeFileDataDto query = KingDeeFileDataDto.builder()
                .formId(KingDeeFormConstant.RECEIVE_BILL)
                .erpId(dto.getBankBillId().toString())
                .url(dto.getReceiptUrl())
                .build();
        List<KingDeeFileDataDto> existFile = kingDeeFileDataService.getByBusinessIdAndUri(query);
        if (!CollectionUtils.isEmpty(existFile)) {
            log.info("当前附件已经推送过金蝶，{}", JSON.toJSONString(query));
            return null;
        }

        KingDeeAliReceiveBillDto aliReceiveBillDto = new KingDeeAliReceiveBillDto();
        aliReceiveBillDto.setFBillNo(dto.getTranFlow());
        kingDeeAliReceiveBillService.query(aliReceiveBillDto);
        if (aliReceiveBillDto.getFId() == null || aliReceiveBillDto.getFId() == 0) {
            log.info("支付宝收款单未推送金蝶，无法推送附件：{}", dto.getTranFlow());
            return null;
        }
//        FileInfoDto base64FromUrl = FileInfoUtils.getBase64FromUrl(dto.getReceiptUrl());
        return KingDeeFileDataDto.builder()
                .fileName(dto.getTranFlow() + ".pdf")
//                .sendByte(base64FromUrl.getFileBase64())
                .aliasFileName(dto.getTranFlow() + ".pdf")
                .billNo(dto.getTranFlow())
                .formId(aliReceiveBillDto.getFormId())
                .isLast(true)
                .fId(aliReceiveBillDto.getFId().toString())
                .url(dto.getReceiptUrl())
                .erpId(dto.getBankBillId().toString())
                .businessId(aliReceiveBillDto.getFormId() + dto.getBankBillId().toString())
                .build();
    }
}
