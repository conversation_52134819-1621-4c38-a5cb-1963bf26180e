package com.vedeng.erp.buyorderexpense.mapper;

import com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseDetailEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2022/10/17 15:17
 **/
@Repository
public interface BuyorderExpenseDetailMapper {
    /**
     * delete by primary key
     * @param buyorderExpenseDetailId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer buyorderExpenseDetailId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(BuyorderExpenseDetailEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(BuyorderExpenseDetailEntity record);

    /**
     * select by primary key
     * @param buyorderExpenseDetailId primary key
     * @return object by primary key
     */
    BuyorderExpenseDetailEntity selectByPrimaryKey(Integer buyorderExpenseDetailId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(BuyorderExpenseDetailEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(BuyorderExpenseDetailEntity record);

    int batchInsert(List<BuyorderExpenseDetailEntity> list);

    BuyorderExpenseDetailEntity selectByBuyorderExpenseId(@Param("buyorderExpenseId")Integer buyorderExpenseId);

    /**
     * 根据费用单id删除费用单detail信息
     *
     * @param record 费用单
     */
    void deleteExpenseDetailByExpenseId(BuyorderExpenseDetailEntity record);

    /**
     * 根据buyorderId查询对应的直属费用单详情信息
     *
     * @param buyorderId 采购单id
     * @return 直属费用单详情信息
     */
    BuyorderExpenseDetailEntity selectByBuyorderId(@Param("buyorderId") Integer buyorderId);

    /**
     * 根据 采购费用订单ID 列表查询 采购费用单详情
     * @param buyorderExpenseIdList 采购费用订单ID 列表
     * @return 采购费用单信息
     */
    List<BuyorderExpenseDetailEntity> selectByBuyorderExpenseIdList(@Param("buyorderExpenseIdList")List<Integer> buyorderExpenseIdList);

    /**
     * 根据 采购费用订单ID(BUYORDER_EXPENSE_ID) 更新 订单说明(ORDER_DESC)
     * @param record update record
     * @return update count
     */
    int updateOrderDescByBuyorderExpenseId(BuyorderExpenseDetailEntity record);

    /**
     * 获取采购费用item明细
     * @param buyorderExpenseItemId
     * @return
     */
    BuyorderExpenseDetailEntity selectByBuyorderExpenseItemId(Integer buyorderExpenseItemId);
}