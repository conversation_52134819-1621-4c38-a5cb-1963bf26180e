package com.vedeng.erp.finance.service;

import cn.hutool.core.date.DateUtil;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.redis.redission.RedissonLockUtils;
import com.vedeng.erp.finance.common.exception.InvoiceException;
import com.vedeng.erp.finance.constants.FinanceConstant;
import com.vedeng.erp.finance.domain.dto.SaleInvoiceOpenResponseDto;
import com.vedeng.erp.finance.dto.InvoiceApplyDto;
import com.vedeng.erp.finance.dto.InvoiceDetailDto;
import com.vedeng.erp.finance.dto.InvoiceDto;
import com.vedeng.erp.finance.dto.OpenInvoiceResultDto;
import com.vedeng.erp.finance.facade.SalesOpenInvoiceFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 销项开票抽象类
 */
@Slf4j
public abstract class AbstractSalesOpenInvoice implements IOpenInvoiceInterface{

    private final static String REDIS_KEY = "ERP:TAX:OPENINVOICE:";

    private final static long WAIT_TIME = 10;

    private final static long LOCK_SECONDS = 20;

    @Autowired
    SalesOpenInvoiceFacade salesOpenInvoiceFacade;

    @Override
    public OpenInvoiceResultDto openInvoice(InvoiceApplyDto invoiceApplyDto) {
        try {
            // 加锁
            tryLock(invoiceApplyDto);
            // 前置处理-校验、初始化
            beforeProcess(invoiceApplyDto);
            // 执行开票、下载
            SaleInvoiceOpenResponseDto responseDto = executeProcess(invoiceApplyDto);
            // 后置处理-修改状态，交付
            afterProcess(invoiceApplyDto,responseDto);
            return new OpenInvoiceResultDto().success();
        }
        catch (InvoiceException e) {
            log.info("数电发票销项票开票:已知异常", e);
            salesOpenInvoiceFacade.openBlueInvoiceFail(invoiceApplyDto, e);
            return new OpenInvoiceResultDto().fail(e.getMessage());

        }
        catch (ServiceException e) {
            log.info("数电发票销项票开票:业务异常", e);
            throw e;

        }
        catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("数电发票销项票开票:系统未知异常", e);
            throw new ServiceException("数电发票销项票开票:系统未知异常", e);
        }
        catch (Exception e) {
            log.error("数电发票销项票开票:系统未知异常", e);
            return new OpenInvoiceResultDto().fail();
        }
        finally {
            // 释放锁
            releaseLock(invoiceApplyDto);
        }
    }

    private void tryLock(InvoiceApplyDto invoiceApplyDto) throws Exception {
        String key = REDIS_KEY + invoiceApplyDto.getInvoiceApplyId();
        // 加锁
        boolean isLock = RedissonLockUtils.tryLock(key, WAIT_TIME, LOCK_SECONDS, TimeUnit.SECONDS);
        if (!isLock){
            log.error("数电发票销项票开票:获取锁失败,超出等待时长, key = [{}]", key);
            throw new ServiceException("数电发票销项票开票:获取锁失败,超出等待时长");
        }
    }

    private void releaseLock(InvoiceApplyDto invoiceApplyDto) {
        String key = REDIS_KEY + invoiceApplyDto.getInvoiceApplyId();
        RedissonLockUtils.unlock(key);
        log.info("数电发票销项票开票:释放锁成功, key = [{}]", key);
    }

    /**
     * 开票前的处理
     * @param invoiceApplyDto
     */
    private void beforeProcess(InvoiceApplyDto invoiceApplyDto) {
        // 初始化发票相关信息
        invoiceApplyDto.initInvoiceInfo();
        // 初始化发票金额信息
        invoiceApplyDto.initInvoiceAmount();
        // 开票校验
        salesOpenInvoiceFacade.checkOpenInvoice(invoiceApplyDto);
    }

    /**
     * 执行开票
     * @param invoiceApplyDto
     */
    @Transactional(rollbackFor = Throwable.class)
    public SaleInvoiceOpenResponseDto executeProcess(InvoiceApplyDto invoiceApplyDto) {
        // 调用开票并尝试下载发票
        SaleInvoiceOpenResponseDto saleInvoiceOpenResponseDto = salesOpenInvoiceFacade.invokeSaleOrderInvoiceApi(invoiceApplyDto);
        return saleInvoiceOpenResponseDto;
    }

    /**
     * 开票后置处理
     * @param invoiceApplyDto
     * @param responseDto
     */
    private void afterProcess(InvoiceApplyDto invoiceApplyDto, SaleInvoiceOpenResponseDto responseDto) {
        if (!responseDto.getIsSuccess()) {
            throw new InvoiceException(responseDto);
        }
        // 开票成功后，后续业务处理
        InvoiceDto invoiceDto = successOpenInvoice(invoiceApplyDto, responseDto);
        // 初始化交付信息
        initHandOverInfo(invoiceDto);
        // 交付
        salesOpenInvoiceFacade.handOverInvoice(invoiceDto);
    }

    protected abstract void initHandOverInfo(InvoiceDto invoiceDto);

    /**
     * 开票成功后，后续业务处理
     * @param invoiceApplyDto
     * @param responseDto
     * @return
     */
    protected abstract InvoiceDto successOpenInvoice(InvoiceApplyDto invoiceApplyDto, SaleInvoiceOpenResponseDto responseDto);

    /**
     * 组装开票信息
     * @param invoiceApplyDto
     * @param responseDto
     * @return
     */
    protected InvoiceDto assembleCreateInvoice(InvoiceApplyDto invoiceApplyDto, SaleInvoiceOpenResponseDto responseDto) {
        InvoiceDto invoice = new InvoiceDto();
        invoice.setCompanyId(invoiceApplyDto.getCompanyId());
        invoice.setInvoiceProperty(FinanceConstant.FULLY_DIGITALIZE);
        invoice.setInvoiceCode(FinanceConstant.INVOICE_CODE);
        invoice.setInvoiceNo(responseDto.getFphm());
        invoice.setType(invoiceApplyDto.getType());
        invoice.setTag(FinanceConstant.OPEN);
        invoice.setRelatedId(invoiceApplyDto.getRelatedId());
        invoice.setInvoiceType(invoiceApplyDto.getInvoiceType());
        invoice.setRatio(invoiceApplyDto.getTax());
        invoice.setColorType(FinanceConstant.BLUE);
        invoice.setIsEnable(ErpConstant.T);
        invoice.setAmount(invoiceApplyDto.getTotalAmount());
        invoice.setValidStatus(ErpConstant.AUDIT_PASS);
        invoice.setAddTime(DateUtil.current());
        invoice.setCreator(ErpConstant.INVOICE_CREATE_TYPE_MANUAL.equals(invoiceApplyDto.getCreateType()) ? invoiceApplyDto.getUpdater() : ErpConstant.ADMIN_ID);
        invoice.setInvoiceApplyId(invoiceApplyDto.getInvoiceApplyId());
        invoice.setOpenInvoiceTime(DateUtil.parseDateTime(responseDto.getKprq()));
        List<InvoiceDetailDto> invoiceDetailDtoList = new ArrayList<>();
        invoice.setInvoiceDetailDtos(invoiceDetailDtoList);

        invoiceApplyDto.getInvoiceApplyDetailDtoList().forEach(d -> {
            InvoiceDetailDto detailDto = new InvoiceDetailDto();
            detailDto.setDetailgoodsId(d.getDetailgoodsId());
            detailDto.setPrice(d.getPrice());
            detailDto.setNum(d.getNum());
            detailDto.setTotalAmount(d.getTotalAmount());
            invoiceDetailDtoList.add(detailDto);
        });

       return invoice;
    }
}
