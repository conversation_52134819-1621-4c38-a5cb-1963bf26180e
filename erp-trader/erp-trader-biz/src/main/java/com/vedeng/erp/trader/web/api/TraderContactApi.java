package com.vedeng.erp.trader.web.api;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.trader.dto.TraderContactDto;
import com.vedeng.erp.trader.service.TraderContactService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 交易者联系人(TraderContact)表控制层
 *
 * <AUTHOR>
 * @since 2022-07-12 16:53:41
 */
@ExceptionController
@RestController
@RequestMapping("/traderContact")
@Slf4j
public class TraderContactApi {

    @Autowired
    private TraderContactService traderContactService;

    /**
     * 分页查询
     *
     * @param recordDtoPageParam 筛选条件
     * @return 查询结果
     */
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> page(@RequestBody PageParam<TraderContactDto> recordDtoPageParam) {
        return R.success(traderContactService.erpPage(recordDtoPageParam));
    }

    /**
     *匹配联系人模糊查询
     *
     * @param traderContactDto 筛选条件
     * @return 查询结果
     */
    @RequestMapping(value = "/matchTraderContractSearch", method = RequestMethod.POST)
    public R<?> matchTraderContractSearch(@RequestBody TraderContactDto traderContactDto) {
        return R.success(traderContactService.matchTraderContractSearch(traderContactDto));
    }


    /**
     * 新增数据
     *
     * @param traderContactDto 实体
     * @return 新增结果
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public R<?> add(@RequestBody TraderContactDto traderContactDto) {
        traderContactService.add(traderContactDto);
        return R.success();
    }
}

