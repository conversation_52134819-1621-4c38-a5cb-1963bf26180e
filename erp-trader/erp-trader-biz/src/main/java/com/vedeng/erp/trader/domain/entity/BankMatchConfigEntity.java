package com.vedeng.erp.trader.domain.entity;

import java.util.Date;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Data;

/**
    * 开户行匹配配置表
    */
@Data
public class BankMatchConfigEntity extends BaseEntity {
    /**
    * 主键
    */
    private Long bankMatchConfigId;

    /**
    * 分组ID
    */
    private Long groupId;

    /**
    * 银行名称关键字
    */
    private String keyWords;

    /**
    * 是否删除 0否 1是
    */
    private Integer isDelete;

    /**
    * 更新备注
    */
    private String updateRemark;
}
