package com.wms.service.processor.other;

import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.common.util.DateUtil;
import com.vedeng.logistics.dao.WarehouseGoodsOutInItemMapper;
import com.vedeng.logistics.eums.WarehouseGoodsInEnum;
import com.vedeng.logistics.model.WarehouseGoodsOutInItem;
import com.vedeng.logistics.model.WarehouseGoodsOutInItemSn;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.BuyorderGoods;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.constant.WmsOperateType;
import com.wms.dao.*;
import com.wms.dto.InputSnDetailDto;
import com.wms.dto.InputSnDto;
import com.wms.model.WmsInSnCodeOrder;
import com.wms.model.po.WmsInputOrder;
import com.wms.model.po.WmsInputOrderGoods;
import com.wms.model.po.WmsOutputOrder;
import com.wms.model.po.WmsOutputOrderGoods;
import com.wms.service.processor.AbstractWMSCalllBackProcessor;
import com.wms.service.util.WmsCommonUtil;
import com.wms.unitconversionorder.dao.WmsUnitConversionOrderMapper;
import com.wms.unitconversionorder.model.entity.WmsUnitConversionOrder;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:入库厂家SN码回传处理
 * @date 2020/11/169:52
 */
@Service
public class InputSnProcessor extends AbstractWMSCalllBackProcessor<InputSnDto> {

    private static final Logger logger = LoggerFactory.getLogger(InputSnProcessor.class);

    @Autowired
    private AfterSalesMapper afterSalesMapper;

    @Autowired
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Autowired
    private BuyorderMapper buyorderMapper;

    @Autowired
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Autowired
    private WmsInputSnCodeMapper wmsInputSnCodeMapper;

    @Autowired
    private WmsInputOrderMapper wmsInputOrderMapper;

    @Autowired
    private WmsInputOrderGoodsMapper wmsInputOrderGoodsMapper;

    @Autowired
    private WmsOutputOrderMapper wmsOutputOrderMapper;

    @Autowired
    private WmsOutputOrderGoodsMapper wmsOutputOrderGoodsMapper;

    @Autowired
    private WmsUnitConversionOrderMapper wmsUnitConversionOrderMapper;

    @Autowired
    private WarehouseGoodsOutInItemMapper warehouseGoodsOutInItemMapper;

    /**
     * 需要更新SN码的出入库单类型
     */
    private static final List<Integer> NEED_UPDATE_SN_WAREHOUSE_TYPE = Arrays.asList(
            WarehouseGoodsInEnum.PURCHASE_IN.getErpCode(),
            WarehouseGoodsInEnum.ORDER_WAREHOUSE_CHANGE_IN.getErpCode(),
            WarehouseGoodsInEnum.ORDER_WAREHOUSE_BACK_IN.getErpCode());

    @Override
    protected String getBusinessKey(InputSnDto inputSnDto) {
        return "SN_IN" + inputSnDto.getAsnNo();
    }

    @Override
    protected boolean needIdempotentValidator() {
        return false;
    }

    @Override
    protected void commonValidator(InputSnDto inputSnDto) throws Exception {
        if (inputSnDto == null || CollectionUtils.isEmpty(inputSnDto.getDetails())) {
            logger.error("入库厂家SN码回传单信息异常" + inputSnDto.toString());
            throw new Exception("入库厂家SN码回传单信息异常" + inputSnDto.toString());
        }
    }

    public boolean validateNum(InputSnDto inputSnDto, Integer skuId) throws Exception{
        String orderNo = WmsCommonUtil.getOriginalOrderNo(inputSnDto.getAsnReference1());
        int operateType = this.getOperateType(inputSnDto);
        Integer inputNum = wmsInputSnCodeMapper.getSnNum(operateType,orderNo);
        Integer num = 0;
        if (WmsOperateType.WAREHOUSE_IN.equals(operateType)){
            BuyorderGoods buyorderGoods = buyorderGoodsMapper.getBuyOrderGoodsByOrderNo(orderNo,skuId);
            num = buyorderGoods.getNum();
        }else if (WmsOperateType.BUYORDER_WAREHOUSE_IN.equals(operateType) || WmsOperateType.ORDER_WAREHOUSE_IN.equals(operateType)){
            AfterSalesGoods afterSalesGood = afterSalesGoodsMapper.getAfterSalesGoodsBySalesNo(orderNo,1);
            num = afterSalesGood.getNum();
        }else if (WmsOperateType.SURPLUS_WAREHOUSE_IN.equals(operateType)){
            WmsInputOrderGoods wmsInputOrderGoods = wmsInputOrderGoodsMapper.getOrderGoods(orderNo,skuId);
            num = wmsInputOrderGoods.getInputNum();
        }else if (WmsOperateType.LENDOUT_WAREHOUSE_IN.equals(operateType)){
            WmsOutputOrderGoods wmsOutputOrderGoods = wmsOutputOrderGoodsMapper.getOrderGoods(orderNo,"V"+skuId);
            num = wmsOutputOrderGoods.getOutputNum();
        }
        if (inputNum.equals(num)){
            return true;
        }
        return false;
    }

    @Override
    protected void doDealWithRequest(InputSnDto inputSnDto) throws Exception {
        logger.info("入库厂家SN码 info:{}",JSON.toJSONString(inputSnDto));
        int operateType = this.getOperateType(inputSnDto);
        List<InputSnDetailDto> snDetailDtoList = inputSnDto.getDetails();
        for (InputSnDetailDto detail : snDetailDtoList) {
            WmsInSnCodeOrder wmsInSnCodeOrder = new WmsInSnCodeOrder();
            wmsInSnCodeOrder.setSku(detail.getSku());
            wmsInSnCodeOrder.setSnCode(detail.getSecondSerialNo());
            wmsInSnCodeOrder.setSerialNo(detail.getSerialNo());

            if (!WmsOperateType.INPUT_CS_INIT.equals(operateType)) {
                if (StringUtils.isBlank(detail.getDedi04())){
                    logger.info("入库厂家SN码单号的ERP订单商品ID为空,{},info:{}",inputSnDto.getAsnReference1(),JSON.toJSONString(inputSnDto));
                    throw new Exception("入库厂家SN码单号:" + inputSnDto.getAsnReference1() + "的ERP订单商品ID为空!");
                }
            }
            int orderGoodsId = Integer.valueOf(detail.getDedi04());;
            int relateId = getRelateId(inputSnDto, operateType);

            wmsInSnCodeOrder.setOrderId(relateId);
            wmsInSnCodeOrder.setOperateType(operateType);
            wmsInSnCodeOrder.setOrderGoodsId(orderGoodsId);
            WmsInSnCodeOrder order = wmsInputSnCodeMapper.getInputSnCode(wmsInSnCodeOrder);
            // 入库回传sn码时，存在多次全量回传场景，仅更新已回传入库单的Sn码
            if (null != order){
                logger.info("入库厂家SN码的回传数据重复,跳过:{}",JSON.toJSONString(order));
                continue;
            }
            WmsInSnCodeOrder wms = new WmsInSnCodeOrder();

//            //校验入回传SN数量
//            if (validateNum(inputSnDto,Integer.valueOf(detail.getSku().substring(1)))){
//                continue;
//            }
            wms.setOrderId(relateId);
            wms.setOrderGoodsId(orderGoodsId);
            wms.setOperateType(operateType);
            wms.setWmsOrderNo(inputSnDto.getAsnNo());
            wms.setSku(detail.getSku());
            wms.setSkuId(Integer.valueOf(detail.getSku().substring(1)));
            wms.setSnCode(detail.getSecondSerialNo());
            wms.setSerialNo(detail.getSerialNo());
            wms.setInTime(DateUtil.StringToDate(inputSnDto.getReceivedTime(),"yyyy-MM-dd HH:mm:ss"));
            if (StringUtils.isNotEmpty(detail.getLotAtt01())){
                wms.setProductDate(DateUtil.StringToDate(detail.getLotAtt01() + " 00:00:00","yyyy-MM-dd HH:mm:ss"));
            }
            if (StringUtils.isNotEmpty(detail.getLotAtt02())){
                wms.setExpirationDate(DateUtil.StringToDate(detail.getLotAtt02() + " 00:00:00","yyyy-MM-dd HH:mm:ss"));
            }
            wms.setBatchNumber(detail.getLotAtt04());
            wms.setVedengBatchNumer(detail.getLotAtt11());
            wms.setComments(inputSnDto.getAsnReference1());
            wms.setIsDelete(false);
            wms.setAddTime(new Date());
            wms.setModeTime(new Date());
            wms.setCreator(0);
            wms.setUpdater(0);
            wmsInputSnCodeMapper.insertSelective(wms);
        }
        logger.info("wmsInputSnCode保存成功,单号{}",inputSnDto.getAsnReference1());
        Integer outInType = WarehouseGoodsInEnum.getErpCodeByCode(inputSnDto.getAsnType());
        // 更新SN码
        if (NEED_UPDATE_SN_WAREHOUSE_TYPE.contains(outInType)){
            this.updateSn(outInType,snDetailDtoList,inputSnDto);
        }
    }

    /**
     * 更新Sn码
     * @param outInType
     * @param snDetailDtoList
     * @param inputSnDto
     */
    private void updateSn(Integer outInType, List<InputSnDetailDto> snDetailDtoList, InputSnDto inputSnDto){
        logger.info("更新SN码：{},入参：{},inputSnDto:{}",outInType,JSON.toJSONString(snDetailDtoList),JSON.toJSONString(inputSnDto));
        // WMS: key:SKU - value:SN码集合
        Map<String, List<InputSnDetailDto>> collect = snDetailDtoList.stream()
                .filter(e->StringUtils.isNotBlank(e.getSku()))
                .collect(Collectors.groupingBy(e -> e.getSku()));
        // ERP: key:SKU
        String relateNo = WmsCommonUtil.getOriginalOrderNo(inputSnDto.getAsnReference1());
        List<WarehouseGoodsOutInItemSn> outInItemByRelateNoDB = warehouseGoodsOutInItemMapper.getOutInItemByRelateNo(relateNo, outInType);
        if (CollectionUtils.isEmpty(outInItemByRelateNoDB)) {
            logger.info("未查询到空SN码的入库单relateNo：{}，outInType：{}",relateNo,outInType);
            return;
        }
        List<WarehouseGoodsOutInItemSn> outInItemByRelateNo = outInItemByRelateNoDB.stream()
                .filter(e -> StringUtils.isBlank(e.getBarcodeFactory()))
                .collect(Collectors.toList());
        logger.info("更新SN码过滤后的入参：{}",JSON.toJSONString(outInItemByRelateNo));
        for (WarehouseGoodsOutInItemSn outInItemSn : outInItemByRelateNo ){
            List<InputSnDetailDto> inputSnDetailDtos = collect.get(outInItemSn.getSku());
            logger.info("入库单：{}匹配到SKU:{}详情：{}",outInItemSn.getOutInNo(),outInItemSn.getSku(),JSON.toJSONString(inputSnDetailDtos));
            if (CollectionUtils.isEmpty(inputSnDetailDtos)){
                continue;
            }
            InputSnDetailDto inputSnDetailDto = inputSnDetailDtos.get(0);
            if (Objects.isNull(inputSnDetailDto)){
                break;
            }
            inputSnDetailDtos.remove(inputSnDetailDto);

            WarehouseGoodsOutInItem updateItem = new WarehouseGoodsOutInItem();
            updateItem.setWarehouseGoodsOutInDetailId(outInItemSn.getWarehouseGoodsOutInDetailId());
            updateItem.setBarcodeFactory(inputSnDetailDto.getSecondSerialNo());
            logger.info("入库单：{}匹配到SKU:{}执行修改sn码入参：{}",outInItemSn.getOutInNo(),outInItemSn.getSku(),JSON.toJSONString(updateItem));
            warehouseGoodsOutInItemMapper.updateByPrimaryKeySelective(updateItem);
        }
    }

    public int getRelateId(InputSnDto inputSnDto, int operateType) throws Exception {
        String orderNo = WmsCommonUtil.getOriginalOrderNo(inputSnDto.getAsnReference1());
       try {
           if (WmsOperateType.BUYORDER_WAREHOUSE_IN.equals(operateType) || WmsOperateType.ORDER_WAREHOUSE_IN.equals(operateType)) {
               //售后入库
               AfterSales afterSale = afterSalesMapper.getAfterSalesByNo(orderNo);
               return afterSale.getAfterSalesId();
           } else if (WmsOperateType.WAREHOUSE_IN.equals(operateType)||WmsOperateType.DELIVERY_DIRECT_WAREHOUSE_IN.equals(operateType)) {
               //采购入库
               Buyorder buyOrder = buyorderMapper.getBuyOrderByOrderNo(orderNo);
               return buyOrder.getBuyorderId();
           }else if (WmsOperateType.SURPLUS_WAREHOUSE_IN.equals(operateType)){
               //盘盈入库
               WmsInputOrder wmsInputOrder = wmsInputOrderMapper.getWmsInputOrderByNo(orderNo);
               return wmsInputOrder.getWmsInputOrderId();
           }else if (WmsOperateType.LENDOUT_WAREHOUSE_IN.equals(operateType)){
               //借货归还入库
               WmsOutputOrder wmsOutputOrder = wmsOutputOrderMapper.selectByOrderNo(orderNo);
               return wmsOutputOrder.getId().intValue();
           }else if (WmsOperateType.UNIT_CONVERSION_IN.equals(operateType)){
               // 单位转换单
               WmsUnitConversionOrder wmsUnitConversionOrder = wmsUnitConversionOrderMapper.selectByWmsUnitConversionOrderNo(orderNo);
               return wmsUnitConversionOrder.getWmsUnitConversionOrderId();
           }else {
               throw new Exception("入库厂家SN码单号:" + inputSnDto.getAsnReference1() + "的订单类型异常!");
           }
       }catch (Exception e){
           throw new Exception("入库厂家SN码单号:" + inputSnDto.getAsnReference1() + "的关联Relateid为空!");
       }
    }

    public int getOperateType(InputSnDto inputSnDto) throws Exception {
        //采购入库
        if(WmsInterfaceOrderType.INPUT_PURCHASE.equals(inputSnDto.getAsnType())){
            return WmsOperateType.WAREHOUSE_IN;
        }
        // 直发采购入库
        if(WmsInterfaceOrderType.DELIVERY_DIRECT_IN.equals(inputSnDto.getAsnType())){
            return WmsOperateType.DELIVERY_DIRECT_WAREHOUSE_IN;
        }
        //销售售后入库
        else if(WmsInterfaceOrderType.EXCHANG_SALEORDER.equals(inputSnDto.getAsnType()) || WmsInterfaceOrderType.INPUT_SALE_RETURN.equals(inputSnDto.getAsnType()) ){
            return WmsOperateType.ORDER_WAREHOUSE_IN;
        }

        //采购售后换货入库
        else if(WmsInterfaceOrderType.INPUT_PURCHASE_EXG.equals(inputSnDto.getAsnType())){
            return WmsOperateType.BUYORDER_WAREHOUSE_IN;
        }
        //盘盈入库
        else if(WmsInterfaceOrderType.INPUT_CHECK_MORE.equals(inputSnDto.getAsnType())){
            return WmsOperateType.SURPLUS_WAREHOUSE_IN;
        }
        //借货归还入库单
        else if(WmsInterfaceOrderType.INPUT_LEND_IN.equals(inputSnDto.getAsnType())){
            return WmsOperateType.LENDOUT_WAREHOUSE_IN;
        }
        //库存初始化入库单
        else if(WmsInterfaceOrderType.INPUT_CS_INIT.equals(inputSnDto.getAsnType())){
            return WmsOperateType.INPUT_CS_INIT;
        }
        //赠品入库
        else if (WmsInterfaceOrderType.INPUT_GIFT.equals(inputSnDto.getAsnType())){
            return WmsOperateType.INPUT_GIFT;
        }
        // 单位转换入库
        else if(WmsInterfaceOrderType.UNIT_CONVERSION_IN.equals(inputSnDto.getAsnType())){
            return WmsOperateType.UNIT_CONVERSION_IN;
        }else {
            throw new Exception("入库厂家SN码单号:" + inputSnDto.getAsnReference1() + "的订单类型异常!");
        }
    }
}
