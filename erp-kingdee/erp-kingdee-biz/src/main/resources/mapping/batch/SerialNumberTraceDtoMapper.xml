<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.SerialNumberTraceDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.SerialNumberTraceDto">
    <!--@mbg.generated-->
    <!--@Table SERIAL_NUMBER_TRACE-->
    <id column="SERIAL_NUMBER_TRACE_ID" jdbcType="INTEGER" property="serialNumberTraceId" />
    <result column="SERIAL_NUMBER_TRACE_SOURCE" jdbcType="INTEGER" property="serialNumberTraceSource" />
    <result column="OUT_IN_NO" jdbcType="VARCHAR" property="outInNo" />
    <result column="SERIAL_NUMBER" jdbcType="VARCHAR" property="serialNumber" />
    <result column="WAREHOUSE_GOODS_OUT_IN_ID" jdbcType="INTEGER" property="warehouseGoodsOutInId" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_ID" jdbcType="INTEGER" property="warehouseGoodsOutInDetailId" />
    <result column="CHANGE_WAREHOUSE_GOODS_OUT_IN_DETAIL_ID" jdbcType="INTEGER" property="changeWarehouseGoodsOutInDetailId" />
    <result column="SALE_ORDER_ID" jdbcType="INTEGER" property="saleOrderId" />
    <result column="SALE_ORDER_GOODS_ID" jdbcType="INTEGER" property="saleOrderGoodsId" />
    <result column="AFTER_SALES_SERVICE_ID" jdbcType="INTEGER" property="afterSalesServiceId" />
    <result column="AFTER_SALES_SERVICE_DETAIL_ID" jdbcType="INTEGER" property="afterSalesServiceDetailId" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="SOURCE_TIME" jdbcType="TIMESTAMP" property="sourceTime" />
    <result column="IS_PUSH" jdbcType="INTEGER" property="isPush" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SERIAL_NUMBER_TRACE_ID, SERIAL_NUMBER_TRACE_SOURCE, OUT_IN_NO, SERIAL_NUMBER, WAREHOUSE_GOODS_OUT_IN_ID,
    WAREHOUSE_GOODS_OUT_IN_DETAIL_ID, CHANGE_WAREHOUSE_GOODS_OUT_IN_DETAIL_ID, SALE_ORDER_ID, 
    SALE_ORDER_GOODS_ID, AFTER_SALES_SERVICE_ID, AFTER_SALES_SERVICE_DETAIL_ID, SKU, 
    SOURCE_TIME, IS_PUSH
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from SERIAL_NUMBER_TRACE
    where SERIAL_NUMBER_TRACE_ID = #{serialNumberTraceId,jdbcType=INTEGER}
  </select>
  <select id="findByAll" resultMap="BaseResultMap" parameterType="com.vedeng.erp.kingdee.batch.dto.SerialNumberTraceDto">
    select
    <include refid="Base_Column_List" />
    from SERIAL_NUMBER_TRACE
    WHERE IS_PUSH = 0
    and SERIAL_NUMBER_TRACE_SOURCE in (2,3)
      limit #{_pagesize} OFFSET #{_skiprows}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from SERIAL_NUMBER_TRACE
    where SERIAL_NUMBER_TRACE_ID = #{serialNumberTraceId,jdbcType=INTEGER}
  </delete>

  <update id="updateIsPushByPrimaryKey" parameterType="com.vedeng.erp.kingdee.batch.dto.SerialNumberTraceDto">
    update SERIAL_NUMBER_TRACE
    set IS_PUSH = #{isPush,jdbcType=INTEGER}
    where SERIAL_NUMBER_TRACE_ID = #{serialNumberTraceId,jdbcType=INTEGER}
  </update>
</mapper>