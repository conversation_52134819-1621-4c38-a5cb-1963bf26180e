package com.vedeng.erp.buyorder.service;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.buyorder.dto.BuyOrderRebateChargeApplyDto;
import com.vedeng.infrastructure.oss.service.domain.FileInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.buyorder.service
 * @Date 2023/11/22 17:00
 */
public interface BuyOrderRebateChargeApplyService {

    /**
     * 分页查询 采购返利结算收款申请
     *
     * @param rebateChargeApplyDtoPageParam 分页参数
     * @return PageInfo<BuyOrderRebateChargeApplyDto>
     */
    PageInfo<BuyOrderRebateChargeApplyDto> page(PageParam<BuyOrderRebateChargeApplyDto> rebateChargeApplyDtoPageParam);

    /**
     * 保存新增 采购返利结算收款申请
     *
     * @param rebateChargeApplyDto 新增后的主键id，用于跳转到详情页
     * @return buyOrderRebateChargeId
     */
    Integer add(BuyOrderRebateChargeApplyDto rebateChargeApplyDto);

    /**
     * 根据主键id查询 采购返利结算收款申请 详情
     *
     * @param buyOrderRebateChargeId 主键id
     * @return BuyOrderRebateChargeApplyDto
     */
    BuyOrderRebateChargeApplyDto getDetail(Integer buyOrderRebateChargeId);

    /**
     * 开启结算申请单审核流
     *
     * @param rebateChargeId ID
     * @param buyOrderRebateChargeNo 单号
     * @param userName       申请人
     */
    void startRebateChargeProcess(Integer rebateChargeId,String buyOrderRebateChargeNo, CurrentUser currentUser);

    /**
     * 完成结算申请单审核节点
     *
     * @param rebateChargeId ID
     * @param pass           是否通过
     * @param comment        备注
     */
    void complementRebateChargeProcess(Integer rebateChargeId, Boolean pass, String comment, String taskId);

    /**
     * 针对明细附件进行内容校验，通过后再进行文件上传
     *
     * @param request HttpServletRequest
     * @param lwfile  MultipartFile
     * @return FileInfo
     */
    FileInfo checkAndUploadDetailFile(HttpServletRequest request, MultipartFile lwfile);

}
