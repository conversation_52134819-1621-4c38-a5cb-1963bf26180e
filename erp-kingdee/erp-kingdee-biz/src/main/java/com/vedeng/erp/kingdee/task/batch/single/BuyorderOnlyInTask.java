package com.vedeng.erp.kingdee.task.batch.single;

import cn.hutool.core.date.DateUtil;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.batch.job.BuyOrderBatchJob;
import com.vedeng.erp.kingdee.task.batch.TaskBatchHandle;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购入库
 * @date 2022/5/16 10:57
 */
@JobHandler(value = "BuyorderOnlyInTask")
@Component
public class BuyorderOnlyInTask extends AbstractJobHandler {


    @Autowired
    private BuyOrderBatchJob batchJob;

    @Autowired
    private JobLauncher jobLauncher;


    /**
     * {"beginTime":"2022-11-01 00:00:00",
     * "endTime":"2022-12-01 00:00:00",
     * "timestamp":"1666687179395"}
     */
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        if (StringUtils.isBlank(param)){
            // 近一天
            Date now = new Date();
            String endTime = DateUtil.format(now, "yyyy-MM-dd HH:mm:ss");
            String startTime = DateUtil.format(DateUtil.offsetDay(now, -1), "yyyy-MM-dd HH:mm:ss");
            param = "{\"beginTime\":\"" + startTime + "\",\"endTime\":\"" + endTime + "\"}";
        }
        XxlJobLogger.log("==================采购订单入库流程batch开始====================");
        JobParameters jobParameters = new TaskBatchHandle().buildJobParameters(param);
        Job job = batchJob.buyOrderOnlyInJob();
        jobLauncher.run(job, jobParameters);
        XxlJobLogger.log("==================采购订单入库流程batch结束====================");
        return SUCCESS;
    }
}
