package com.vedeng.erp.finance.domain.dto;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Data;

/**
 * 发票序号
 */
@Data
public class InvoiceApplyDetailXhDto extends BaseEntity {
    /**
    * 主键
    */
    private Long invoiceApplyDetailXhId;

    /**
     * 发票申请ID
     */
    private Integer invoiceApplyId;

    /**
    * 发票申请明细ID
    */
    private Integer invoiceApplyDetailId;

    /**
    * 序号
    */
    private Integer xh;

    /**
    * 是否删除 0否 1是
    */
    private Integer isDelete;

    /**
    * 更新备注
    */
    private String updateRemark;

}
