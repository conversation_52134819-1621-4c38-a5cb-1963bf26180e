<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="物流轨迹" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/tailwind/tailwind.min.css">

<div class="max-w-4xl mx-auto bg-white p-6 shadow-md rounded-lg">
    <div class="border-b pb-4 mb-4">
        <div class="text-center text-lg font-semibold mb-4">
            快递物流信息
        </div>

        <div class="flex justify-between items-start text-sm text-gray-600">
            <div class="text-left">
                <div class="p-1">订单号: ${logisticsInfoDto.orderNo}</div>
                <div class="p-1">快递公司: ${logisticsInfoDto.logisticsName}</div>
                <div class="p-1">发货时间: ${logisticsInfoDto.sendTime}</div>
            </div>
            <div class="text-left">
                <div class="p-1">名称: ${logisticsInfoDto.traderCustomerName}</div>
                <div class="p-1">快递单号: ${logisticsInfoDto.logisticsNo}</div>
                <div class="p-1">签收时间: ${logisticsInfoDto.signTime}</div>
            </div>
        </div>
    </div>
    <div class="mt-4">
        <table class="w-full text-sm border-collapse">
            <thead>
            <tr class="bg-gray-100">
                <th class="p-2 text-center border-b">日期</th>
                <th class="p-2 text-center border-b">时间</th>
                <th class="p-2 text-center border-b">物流信息</th>
            </tr>
            </thead>
            <tbody>
            <c:forEach var="list" items="${ldList}" varStatus="num">
                <tr class="hover:bg-gray-50">
                    <td class="p-1 text-center border-b w-14">${list.dateTime}</td>
                    <td class="p-1 text-center border-b w-2">${list.timeMillis}</td>
                    <td class="p-1 text-left border-b w-40">${list.detail}</td>
                </tr>
            </c:forEach>
            </tbody>
        </table>
    </div>
</div>

<%@ include file="../../common/footer.jsp" %>