package com.vedeng.erp.trader.mapper;

import com.vedeng.erp.trader.domain.entity.RTraderGroupJTraderEntity;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.trader.mapper
 * @Date 2023/8/4 17:39
 */
@Named("NewRTraderGroupJTraderMapper")
public interface RTraderGroupJTraderMapper {

    /**
     * 根据分组id和客户id查询客户分组信息
     *
     * @param traderGroupId 客户分组id
     * @param traderId      客户id
     * @return RTraderGroupJTraderEntity
     */
    RTraderGroupJTraderEntity getByTraderGroupIdAndTraderId(@Param("traderGroupId") Integer traderGroupId, @Param("traderId") Integer traderId);
}
