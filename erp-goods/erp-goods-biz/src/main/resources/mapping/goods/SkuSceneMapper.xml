<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.mapper.SkuSceneMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.goods.domain.entity.SkuSceneEntity">
        <!--@mbg.generated-->
        <!--@Table T_SKU_SCENE-->
        <id column="ID" jdbcType="BIGINT" property="id"/>
        <result column="SCENE_NO" jdbcType="VARCHAR" property="sceneNo"/>
        <result column="NAME" jdbcType="VARCHAR" property="name"/>
        <result column="DESCRIPTION" jdbcType="LONGVARCHAR" property="description"/>
        <result column="SORT" jdbcType="INTEGER" property="sort"/>
        <result column="STATUS" jdbcType="TINYINT" property="status"/>
        <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName"/>
        <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID,
        SCENE_NO,
        `NAME`,
        DESCRIPTION,
        SORT,
        `STATUS`,
        IS_DELETE,
        CREATOR,
        UPDATER,
        CREATOR_NAME,
        UPDATER_NAME,
        ADD_TIME,
        MOD_TIME
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from T_SKU_SCENE
        where ID = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete
        from T_SKU_SCENE
        where ID = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.goods.domain.entity.SkuSceneEntity"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_SKU_SCENE (SCENE_NO, `NAME`, DESCRIPTION,
                                 SORT, `STATUS`, IS_DELETE,
                                 CREATOR, UPDATER, CREATOR_NAME,
                                 UPDATER_NAME, ADD_TIME, MOD_TIME)
        values (#{sceneNo,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{description,jdbcType=LONGVARCHAR},
                #{sort,jdbcType=INTEGER}, #{status,jdbcType=TINYINT}, #{isDelete,jdbcType=INTEGER},
                #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR},
                #{updaterName,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="ID" keyProperty="id"
            parameterType="com.vedeng.goods.domain.entity.SkuSceneEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_SKU_SCENE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sceneNo != null and sceneNo != ''">
                SCENE_NO,
            </if>
            <if test="name != null and name != ''">
                `NAME`,
            </if>
            <if test="description != null and description != ''">
                DESCRIPTION,
            </if>
            <if test="sort != null">
                SORT,
            </if>
            <if test="status != null">
                `STATUS`,
            </if>
            <if test="isDelete != null">
                IS_DELETE,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="creatorName != null and creatorName != ''">
                CREATOR_NAME,
            </if>
            <if test="updaterName != null and updaterName != ''">
                UPDATER_NAME,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sceneNo != null and sceneNo != ''">
                #{sceneNo,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != ''">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="description != null and description != ''">
                #{description,jdbcType=LONGVARCHAR},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null and creatorName != ''">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updaterName != null and updaterName != ''">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.domain.entity.SkuSceneEntity">
        <!--@mbg.generated-->
        update T_SKU_SCENE
        <set>
            <if test="sceneNo != null and sceneNo != ''">
                SCENE_NO = #{sceneNo,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != ''">
                `NAME` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="description != null and description != ''">
                DESCRIPTION = #{description,jdbcType=LONGVARCHAR},
            </if>
            <if test="sort != null">
                SORT = #{sort,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                `STATUS` = #{status,jdbcType=TINYINT},
            </if>
            <if test="isDelete != null">
                IS_DELETE = #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null and creatorName != ''">
                CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updaterName != null and updaterName != ''">
                UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where ID = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.domain.entity.SkuSceneEntity">
        <!--@mbg.generated-->
        update T_SKU_SCENE
        set SCENE_NO     = #{sceneNo,jdbcType=VARCHAR},
            `NAME`       = #{name,jdbcType=VARCHAR},
            DESCRIPTION  = #{description,jdbcType=LONGVARCHAR},
            SORT         = #{sort,jdbcType=INTEGER},
            `STATUS`     = #{status,jdbcType=TINYINT},
            IS_DELETE    = #{isDelete,jdbcType=INTEGER},
            CREATOR      = #{creator,jdbcType=INTEGER},
            UPDATER      = #{updater,jdbcType=INTEGER},
            CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
            UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
            ADD_TIME     = #{addTime,jdbcType=TIMESTAMP},
            MOD_TIME     = #{modTime,jdbcType=TIMESTAMP}
        where ID = #{id,jdbcType=BIGINT}
    </update>


    <!--auto generated by MybatisCodeHelper on 2024-12-02-->
    <select id="findByAll" resultType="com.vedeng.goods.dto.SkuSceneDto">
        select
        <include refid="Base_Column_List"/>
        from T_SKU_SCENE TSS
        <where>
            <if test="id != null">
                and ID = #{id,jdbcType=BIGINT}
            </if>
            <if test="sceneNo != null and sceneNo != ''">
                and SCENE_NO like concat('%',#{sceneNo,jdbcType=VARCHAR},'%')
            </if>

            <if test="name != null and name != ''">
                and `NAME` like concat('%',#{name,jdbcType=VARCHAR},'%')
            </if>
            <if test="description != null and description != ''">
                and DESCRIPTION = #{description,jdbcType=LONGVARCHAR}
            </if>
            <if test="status != null">
                and `STATUS` = #{status,jdbcType=TINYINT}
            </if>
            <if test="createUserList != null and createUserList.size() > 0">
                and CREATOR in
                <foreach collection="createUserList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="modifyUserList != null and modifyUserList.size() > 0">
                and UPDATER in
                <foreach collection="modifyUserList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="createStartTime != null and createEndTime != null">
                and ADD_TIME between #{createStartTime,jdbcType=TIMESTAMP} and #{createEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="modifyStartTime != null and modifyEndTime != null">
                and MOD_TIME between #{modifyStartTime,jdbcType=TIMESTAMP} and #{modifyEndTime,jdbcType=TIMESTAMP}
            </if>

        </where>
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-12-02-->
    <select id="findBySortGreaterThan" resultMap="BaseResultMap">
        select ID,
               SORT
        from T_SKU_SCENE
        where SORT <![CDATA[>]]> #{minSort,jdbcType=INTEGER}
          and IS_DELETE = 0
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-12-02-->
    <select id="findBySortLessThan" resultMap="BaseResultMap">
        select ID,
               SORT
        from T_SKU_SCENE
        where SORT <![CDATA[<]]> #{maxSort,jdbcType=INTEGER}
          and IS_DELETE = 0
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-12-02-->
    <update id="updateIsDeleteById">
        update T_SKU_SCENE
        set IS_DELETE=1
        where ID = #{id,jdbcType=BIGINT}
    </update>

    <!--auto generated by MybatisCodeHelper on 2024-12-03-->
    <select id="findByName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_SKU_SCENE
        where `NAME` = #{name,jdbcType=VARCHAR}
          and IS_DELETE = 0
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-12-05-->
    <select id="findById" resultType="com.vedeng.goods.dto.SkuSceneDto">
        select *
        from T_SKU_SCENE TSS
        where TSS.ID = #{id,jdbcType=BIGINT}
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-12-05-->
    <select id="findBySortBetween" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_SKU_SCENE
        where SORT between #{minSort,jdbcType=INTEGER} and #{maxSort,jdbcType=INTEGER}
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-12-05-->
    <select id="findMaxSort" resultType="java.lang.Integer">
        SELECT COALESCE(MAX(SORT), 0) AS maxsort
        FROM T_SKU_SCENE;
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-12-05-->
    <select id="findCreator" resultType="com.vedeng.common.core.domain.UserComponent">
        select DISTINCT TU.USER_ID,
                        TU.USERNAME,
                        TUD.ALIAS_HEAD_PICTURE
        from T_SKU_SCENE TSS
                 left join T_USER TU on TSS.CREATOR = TU.USER_ID
                 left join
             T_USER_DETAIL TUD on TU.USER_ID = TUD.USER_ID
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-12-05-->
    <select id="findUpdater" resultType="com.vedeng.common.core.domain.UserComponent">
        select DISTINCT TU.USER_ID,
                        TU.USERNAME,
                        TUD.ALIAS_HEAD_PICTURE
        from T_SKU_SCENE TSS
                 left join T_USER TU on TSS.UPDATER = TU.USER_ID
                 left join
             T_USER_DETAIL TUD on TU.USER_ID = TUD.USER_ID
    </select>

<!--auto generated by MybatisCodeHelper on 2024-12-07-->
    <select id="findAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_SKU_SCENE
        where IS_DELETE = 0
        order by SORT
    </select>
</mapper>