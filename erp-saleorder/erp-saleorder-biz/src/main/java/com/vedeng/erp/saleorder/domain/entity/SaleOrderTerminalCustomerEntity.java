package com.vedeng.erp.saleorder.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Data;

/**
 * T_SALE_ORDER_TERMINAL_CUSTOMER
 * <AUTHOR>
@Data
public class SaleOrderTerminalCustomerEntity extends BaseEntity {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 销售单id
     */
    private Integer saleOrderId;

    /**
     * 交易者id
     */
    private Integer traderId;

    /**
     * 客户id
     */
    private Integer traderCustomerId;

    /**
     * 终端名称
     */
    private String traderName;

    /**
     * 是否删除
     */
    private Integer isDeleted;

}