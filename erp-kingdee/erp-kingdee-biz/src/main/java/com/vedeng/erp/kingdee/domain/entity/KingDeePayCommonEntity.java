package com.vedeng.erp.kingdee.domain.entity;

import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseEntity;
import com.vedeng.common.mybatis.jbatis.annotation.Column;
import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
* 标准应付单
*/
@Getter
@Setter
@ToString
@Table(name="KING_DEE_PAY_COMMON")
public class KingDeePayCommonEntity extends BaseEntity {
    /**
    * ID
    */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
    * fId 0：表示新增 非0：云星空系统单据FID值，表示修改
    */
    private String fId;

    /**
    * 单据日期 格式yyyy-MM-dd fDate
    */
    private String fDate;

    /**
    * 贝登单据头ID
    */
    private String fQzokBddjtId;

    /**
    * 业务类型 默认CG
    */
    private String fBusinessType;

    /**
    * 对应入库单 默认CG
    */
    private String fInStockBusType;

    /**
    * 立账类型 默认填写1
    */
    private String fSetAccountType;

    /**
    * 单据类型 填单据类型编码，默认YFD01_SYS
    */
    private String fBillTypeId;

    /**
    * 供应商 填写供应商编码
    */
    private String fSupplierId;

    /**
    * 结算组织 默认101,配置化
    */
    private String fSettleOrgId;

    /**
    * 付款组织 默认101,配置化
    */
    private String fPayOrgId;

    /**
    * json明细
    */
    @Column(jdbcType = "VARCHAR")
    private JSONArray fEntityDetail;
}