package com.vedeng.erp.business.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 关联单据
 */
@Data
public class RelatedOrderDto {

    /**
     * 单据类型
     */
    private String type;

    /**
     * 系统编码 T_BASE_COMPANY_INFO：FRONT_END_SEQ
     */
    private Integer systemCode;

    /**
     * 关联单据ID
     */
    private Integer relatedId;
    
    /**
     * 关联单据编号
     */
    private String relatedNo;

    /**
     * 归属销售id
     */
    private Integer belongUserId;

    /**
     * 归属销售名称
     */
    private String belongUserName;

    /**
     * 归属销售头像
     */
    private String belongUserPic;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date addTimeDate;

    /**
     * 跳转ERP的链接
     */
    private String jumpErpUrl;

    /**
     * 跳转ERP的内部链接
     */
    private String jumpErpInnerUrl;

}
