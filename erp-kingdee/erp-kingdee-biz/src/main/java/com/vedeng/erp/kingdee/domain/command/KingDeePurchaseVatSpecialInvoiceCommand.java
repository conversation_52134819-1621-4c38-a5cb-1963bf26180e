package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶采购 专票 实际接受参数类
 * @date 2022/11/09 10:11
 *
 *
 * 专票缺 结算组织  和 发票类型 是不是确定?
 */
@Data
public class KingDeePurchaseVatSpecialInvoiceCommand {
    /**
     * 单据内码
     */
    private String FID;
    /**
     * 业务日期  录票时间
     */
    private String FDATE;

    /**
     *  发票日期 2022-09-22 00:00:00
     */
    private String FINVOICEDATE;
    /**
     * 贝登erp对应的单据头ID
     */
    private String F_QZOK_BDDJTID;
    /**
     * 发票号23456
     */
    private String FINVOICENO;
    /**
     * 发票代码
     */
    private String F_QZOK_FPDM;
    /**
     * 供应商
     */
    private KingDeeNumberCommand FSUPPLIERID = new KingDeeNumberCommand();
    /**
     * 单据状态 默认:Z
     */
    private String FDOCUMENTSTATUS;
    /**
     * 单据类型 CGPTFP01_SYS            采购增值税普通发票
     */
    private KingDeeNumberCommand FBillTypeID = new KingDeeNumberCommand();
    /**
     * 结算组织 默认101,配置化
     */
    private KingDeeNumberCommand FSETTLEORGID = new KingDeeNumberCommand();

    /**
     * 采购组织
     */
    private KingDeeNumberCommand FPURCHASEORGID = new KingDeeNumberCommand();
    /**
     * 作废状态 A （正常）
     */
    private String FCancelStatus;
    /**
     * 红蓝字标识 0 蓝字  1 红字
     */
    private String fRedBlue;
    /**
     * 发票明细
     */
    private List<PurchaseVatSpecialInvoiceDetailCommand> FPURCHASEICENTRY = new ArrayList<>();

    @Data
    public static class PurchaseVatSpecialInvoiceDetailCommand{
        /**
         * 物料编码 SKU
         */
        private KingDeeNumberCommand FMATERIALID = new KingDeeNumberCommand();
        /**
         * 计价数量 填上发票的入库数量，
         * 如果是红字发票，则数量*-1
         */
        private String FPRICEQTY;
        /**
         * 含税单价
         */
        private BigDecimal FAUXTAXPRICE;
        /**
         * 税率
         */
        private String FTAXRATE;
        /**
         * 不含税金额
         */
        private BigDecimal FAMOUNTFOR;
        /**
         * 税额
         */
        private BigDecimal FDETAILTAXAMOUNTFOR;

        /**
         * 价税合计
         */
        private BigDecimal FALLAMOUNTFOR;
        /**
         * 贝登单据行ID
         */
        private String F_QZOK_BDDJHID;
        /**
         * 原始订单号
         */
        private String F_QZOK_YSDDH;
        /**
         * 归属业务单号
         */
        private String F_QZOK_GSYWDH;
        /**
         * 业务类型
         */
        private String F_QZOK_YWLX;
        /**
         * AP_Payable
         */
        private String FSOURCETYPE;
        /**
         * fpurchaseicentryLink
         */
        private List<PurchaseVatSpecialInvoiceDetailLinkCommand> FPURCHASEICENTRY_Link = new ArrayList<>();
    }

    @Data
    public static class PurchaseVatSpecialInvoiceDetailLinkCommand{
        /**
         * 实体主键
         */
        private String FLinkId;
        /**
         * fpurchaseicentryLinkFflowid
         */
        private String FPURCHASEICENTRY_Link_FFlowId;
        /**
         * fpurchaseicentryLinkFflowlineid
         */
        private String FPURCHASEICENTRY_Link_FFlowLineId;
        /**
         * 应付单关联  ：IV_PayableToPurchaseIC
         * 蓝字发票关联：IV_BlueToRedPurchaseIC
         */
        private String FPURCHASEICENTRY_Link_FRuleId;
        /**
         * 源单表内码
         */
        private String FPURCHASEICENTRY_Link_FSTableId;
        /**
         * 应付单关联  ：T_AP_PAYABLEENTRY
         * 蓝字发票关联：T_IV_PURCHASEICENTRY
         */
        private String FPURCHASEICENTRY_Link_FSTableName;
        /**
         *  源单内码
         *  应付单:金蝶应付单表头ID
         * 蓝字发票：此红票对应蓝票金蝶表头ID
         */
        private String FPURCHASEICENTRY_Link_FSBillId;
        /**
         * 源单分录内码
         * 应付单:金蝶应付单表体行ID
         * 蓝字发票关联：此红票对应蓝票金蝶表体行ID
         */
        private String FPURCHASEICENTRY_Link_FSId;
        /**
         * 关联单的表体行数量，即开票数量（红票负数）
         */
        private String FPURCHASEICENTRY_Link_FBASICUNITQTYOld;
        /**
         * 关联单的表体行数量，即开票数量（红票负数）
         */
        private String FPURCHASEICENTRY_Link_FBASICUNITQTY;
        /**
         * 关联单的表体行数量*含税单价（红票负数）
         */
        private BigDecimal FPURCHASEICENTRY_Link_FALLAMOUNTFOROld;
        /**
         * 开票数量*不含税单价（红票负数）
         */
        private BigDecimal FPURCHASEICENTRY_Link_FALLAMOUNTFOR;
    }

}
