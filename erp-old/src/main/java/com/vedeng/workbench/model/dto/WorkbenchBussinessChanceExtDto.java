package com.vedeng.workbench.model.dto;

import com.vedeng.workbench.model.dto.generate.WorkbenchBussinessChanceDo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@Data
public class WorkbenchBussinessChanceExtDto extends WorkbenchBussinessChanceDo {


    /**
     * 归属销售名称
     */
    private String UserName;

    /**
     * 最后一次沟通记录
     */
    private BussinessCommunicateRecordDto lastCommunicateRecord ;

    /**
     * 预计成单时间
     */
    private Date expectOrderTime;

    /**
     * 订单关闭时间
     */
    private Date closeDate;

    /**
     * 沟通电话
     */
    private String contactNum;

    /**
     * 优先报价单金额，then预计金额
     */
    private BigDecimal bussinessAmount;

    /**
     * 客户展示姓名
     */
    private String displayTraderName;

    /**
     * 创建时间
     */
    private Date addDate;

    /**
     * 最近沟通日期
     */
    private Date endDate;

    /**
     * 是否今日待沟通 -1待沟通，1已沟通
     */
    private Integer contactType ;
    /**
     * 沟通记录list
     */
    List<BussinessCommunicateRecordDto> bussinessCommunicateRecordDtoList;
}
