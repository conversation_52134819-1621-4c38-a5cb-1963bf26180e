package com.vedeng.erp.system.domain.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 业务流转单主表
 */
@Getter
@Setter
public class FlowOrderRespDto implements Serializable {

    private static final long serialVersionUID = 2045743174754383596L;
    /**
     * 主键
     */
    private Long flowOrderId;

    /**
     * 业务流转单编号
     */
    private String flowOrderNo;

    /**
     * 推送方向 1:金蝶, 2:ERP
     */
    private Integer pushDirection;
    
    private String pushDirectionName;

    /**
     * 来源ERP系统
     */
    private String sourceErp;
    
    private String sourceErpName;
    
    /**
     * 基础业务订单ID
     */
    private Integer baseOrderId;

    /**
     * 基础业务订单编号
     */
    private String baseOrderNo;

    /**
     * 基础业务类型 1.采购 2.销售
     */
    private Integer baseBusinessType;

    /**
     * 类型
     */
    private String baseBusinessTypeStr;

    /**
     * 审核状态，0:未审核, 1:已审核
     */
    private Integer auditStatus;

    /**
     * 审核状态
     */
    private String auditStatusStr;

    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private String addTime;

    /**
     * 审核人
     */
    private String auditUsername;

    /**
     * 审核时间
     */
    private String auditTime;

    /**
     * 推送结果
     */
    private String isPushed;

    /**
     * 1级名称
     */
    private String firstTraderName;

    /**
     * 末级名称
     */
    private String lastTraderName;

    /**
     * 末级合同状态：0-无需上传（末级节点为贝登子公司），1-未上传（末级节点非贝登子公司且未上传合同），2-已上传（末级节点非贝登子公司且已上传合同）
     * 注意：此字段从T_FLOW_ORDER表中获取
     */
    private Integer contractStatus;
}
