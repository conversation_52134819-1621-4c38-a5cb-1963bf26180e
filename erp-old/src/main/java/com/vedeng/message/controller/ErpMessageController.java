package com.vedeng.message.controller;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.R;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.infrastructure.message.dto.MessageDto;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/7/18
 */
@RestController
@RequestMapping("/api/message")
public class ErpMessageController {


    @NoNeedAccessAuthorization
    @RequestMapping("/send")
    public R<?> send(@RequestBody MessageDto messageDto) {
        boolean result = MessageUtil.sendMessage(messageDto.getMessageTemplateId(), messageDto.getUserIds(), messageDto.getParams(), messageDto.getUrl());
        if(result){
            return R.success("发送成功");
        }else{
            return R.error("发送失败");
        }
    }


}
