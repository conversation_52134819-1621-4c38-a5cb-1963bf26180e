package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeeInventoryProfitCommand;
import com.vedeng.erp.kingdee.dto.KingDeeInventoryProfitDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeInventoryProfitDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * @description:盘盈单
 * @author: yana.jiang
 * @date: 2022/11/10
 */
@Mapper(componentModel = "spring")
public interface KingDeeInventoryProfitCommandConvertor extends BaseCommandMapStruct<KingDeeInventoryProfitCommand, KingDeeInventoryProfitDto> {

    @Mapping(target = "FId", source = "FId")
    @Mapping(target = "FBillTypeId.FNumber", source = "FBillTypeId")
    @Mapping(target = "FBillNo", source = "FBillNo")
    @Mapping(target = "f_QZOK_BDDJTID", source = "FQzokBddjtId")
    @Mapping(target = "FStockOrgId.FNumber", source = "FStockOrgId")
    @Mapping(target = "FOwnerTypeIdHead", source = "FOwnerTypeIdHead")
    @Mapping(target = "FDate", source = "FDate")
    @Mapping(target = "FDeptId.FNumber", source = "FDeptId")
    KingDeeInventoryProfitCommand toCommand(KingDeeInventoryProfitDto dto);

    @Mapping(target = "FMaterialId.FNumber", source = "FMaterialId")
    @Mapping(target = "FStockId.FNumber", source = "FStockId")
    @Mapping(target = "FStockStatusId.FNumber", source = "FStockStatusId")
    @Mapping(target = "FBaseGainQty", source = "FBaseGainQty")
    @Mapping(target = "FPrice", source = "FPrice")
    @Mapping(target = "FAmount", source = "FAmount")
    @Mapping(target = "f_QZOK_YSDDH", source = "FQzokYsddh")
    @Mapping(target = "f_QZOK_GSYWDH", source = "FQzokGsywdh")
    @Mapping(target = "f_QZOK_YWLX", source = "FQzokYwlx")
    @Mapping(target = "f_QZOK_PCH", source = "FQzokPch")
    @Mapping(target = "f_QZOK_XLH", source = "FQzokXlh")
    KingDeeInventoryProfitCommand.KingDeeInventoryProfitDetailCommand toCommand(KingDeeInventoryProfitDetailDto dto);
}
