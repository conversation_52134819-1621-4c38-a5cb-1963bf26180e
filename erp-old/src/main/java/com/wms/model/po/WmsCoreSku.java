package com.wms.model.po;

import com.vedeng.goods.model.CoreSkuGenerate;

/**
 * <AUTHOR>
 * @ClassName WmsCoreSku
 * @Description 商品档案下传WMS
 * @Date 2020/7/28 13:19
 */
public class WmsCoreSku extends CoreSkuGenerate {

    /**
     * 最小售卖单位名称
     */
    private String unitName;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 商品品牌 （品牌性质 1国产 2进口）
     */
    private Integer brandNature;

    /**
     * 管理类别
     */
    private Integer manageCategoryLevel;

    /**
     * 商品类型
     */
    private Integer spuType;

    /**
     * 生产企业
     */
    private String productCompanyChineseName;

    /**
     * 生产企业生产许可证号/备案凭证号
     */
    private String registrationNumber;

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public Integer getBrandNature() {
        return brandNature;
    }

    public void setBrandNature(Integer brandNature) {
        this.brandNature = brandNature;
    }

    public Integer getManageCategoryLevel() {
        return manageCategoryLevel;
    }

    public void setManageCategoryLevel(Integer manageCategoryLevel) {
        this.manageCategoryLevel = manageCategoryLevel;
    }

    public Integer getSpuType() {
        return spuType;
    }

    public void setSpuType(Integer spuType) {
        this.spuType = spuType;
    }

    public String getProductCompanyChineseName() {
        return productCompanyChineseName;
    }

    public void setProductCompanyChineseName(String productCompanyChineseName) {
        this.productCompanyChineseName = productCompanyChineseName;
    }

    public String getRegistrationNumber() {
        return registrationNumber;
    }

    public void setRegistrationNumber(String registrationNumber) {
        this.registrationNumber = registrationNumber;
    }
}
