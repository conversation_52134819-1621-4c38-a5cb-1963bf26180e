package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchSaleSettlementAdjustmentItemDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BatchSaleSettlementAdjustmentItemDtoMapper {

    int deleteByPrimaryKey(Integer saleSettlementAdjustmentItemId);

    int insert(BatchSaleSettlementAdjustmentItemDto record);

    int insertSelective(BatchSaleSettlementAdjustmentItemDto record);

    BatchSaleSettlementAdjustmentItemDto selectByPrimaryKey(Integer saleSettlementAdjustmentItemId);

    int updateByPrimaryKeySelective(BatchSaleSettlementAdjustmentItemDto record);

    int updateByPrimaryKey(BatchSaleSettlementAdjustmentItemDto record);


    /**
     * 根据调整单id查找调整单明细
     *
     * @param adjustmentId
     * @return
     */
    List<BatchSaleSettlementAdjustmentItemDto> findByAdjustmentId(Integer adjustmentId);

    int batchInsert(@Param("list") List<BatchSaleSettlementAdjustmentItemDto> list);
}