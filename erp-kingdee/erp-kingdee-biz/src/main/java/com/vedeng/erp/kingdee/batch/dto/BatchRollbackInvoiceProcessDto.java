package com.vedeng.erp.kingdee.batch.dto;

import com.vedeng.erp.kingdee.batch.common.bean.BatchBaseDto;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BatchRollbackInvoiceProcessDto extends BatchBaseDto {

    /**
     * 票id
     */
    private  Integer invoiceId;

    private  String orgId;

    /**
     * 金蝶formId
     */
    private String formId;

    /**
     * 金蝶id
     */
    private String ids;

    /**
     * 是否是应付单
     */
    private Boolean isPay = Boolean.FALSE;

    private String uuid;

    private Integer virtualInvoiceId;

}
