# InventoryReceiptStepV3 - 快递增量同步版本

## 版本概述

`InventoryReceiptStepV3` 是入库单步骤的第三个版本，专门为解决快递增量同步问题而设计。

### 核心改进

**V2 版本问题：**
- 等待上游公司采购单中的**所有快递**都完成才开始同步下游
- 即使某个快递已经可以创建，也要等待同一采购单下的其他快递
- 造成下游同步延迟，影响业务效率

**V3 版本解决方案：**
- 当上游采购单中的**单个快递**达到可创建状态时，立即为该快递创建对应的下游快递
- 正确的完成判断逻辑：**下游已创建快递数量 = 预期快递数量**
- 完全符合 Temporal 框架要求，支持可靠的重放和故障恢复

## 技术架构

### 1. 核心流程（修正版）

```
1. 持续监控上游快递新增情况 (30秒轮询)
   ↓
2. 发现新快递时立即创建对应下游快递
   ↓
3. 跟踪上游快递创建进度和下游快递创建进度
   ↓
4. 当上游快递数量 >= 发货数量时，表示上游快递创建完成
   ↓
5. 继续执行库存查询和同行单创建流程
```

### 2. 关键方法

#### 主要方法：
- `monitorAndCreateExpressIncrement()` - 增量监控和创建主流程
- `createDownstreamExpressAndReturnId()` - 单个下游快递创建
- 保留 V2 的签收逻辑：`executeAsyncExpressReceiptV2()`

#### 新增 Activity 方法：
- `checkUpstreamExpressList()` - 检查上游快递列表变化，返回发货数量信息
- `waitForExpressReady()` - 等待快递状态就绪
- `createSingleExpress()` - 创建单个快递

### 3. 数据结构

```java
// 快递创建结果
ExpressCreationResult
├── createdExpressIds      // 已创建的下游快递ID列表
├── processedUpstreamIds   // 已处理的上游快递ID列表
├── createdExpressCount    // 已创建数量
├── expectedCount          // 预期总数
└── success               // 是否成功

// 快递列表信息（修正版）
ExpressListInfo
├── newExpressList       // 新增快递列表
├── allExpressList       // 所有快递列表
├── currentExpressCount  // 当前快递数量
├── shipmentCount        // 发货数量
└── isCompleted          // 是否完成 (快递数量 >= 发货数量)
```

## Temporal 兼容性

### 1. 确定性保证
- ✅ 所有外部 API 调用通过 Activity 执行
- ✅ Workflow 状态正确持久化，支持重放
- ✅ 使用确定性的状态变更逻辑

### 2. 状态管理
- `createdDownstreamExpressIds` - 已创建的下游快递ID (Workflow状态)
- `processedUpstreamExpressIds` - 已处理的上游快递ID (Workflow状态)
- `expectedExpressCount` - 预期快递数量 (Workflow状态)

### 3. 重放安全
- 系统宕机重启后，重放时会恢复到正确的状态
- 不会重复处理已处理的快递
- 不会重复创建已创建的下游快递

## API 依赖

### 新增 API 端点：
1. `/api/v1/express/listWithStatus.do` - 获取快递列表和发货数量信息
2. `/api/v1/express/checkStatus.do` - 检查单个快递状态
3. `/api/v1/express/createSingle.do` - 创建单个快递

### 复用现有 API：
- `/api/v1/express/signCheck.do` - 签收状态检查
- `/api/v1/express/sign.do` - 执行签收
- `/api/v1/peerlist/queryStockRecords.do` - 查询库存记录
- `/api/v1/peerlist/create.do` - 创建同行单

## 业务价值

### 1. 响应时间提升
- **之前**：等待采购单中最慢的快递，所有快递完成后才开始下游同步
- **现在**：第一个快递完成后立即开始下游同步

### 2. 并行处理效率
- **场景**：一个采购单包含5个快递
- **之前**：串行等待，总时间 = max(快递1, 快递2, 快递3, 快递4, 快递5)
- **现在**：并行处理，总时间 = 各快递处理时间的并集

### 3. 用户体验改善
- 下游用户更快收到可处理的快递信息
- 减少业务流程的等待时间
- 提高整体业务效率

## 部署和使用

### 1. 向后兼容
- V2 版本继续存在，不影响现有功能
- 可以通过配置选择使用 V2 或 V3 版本

### 2. 渐进迁移
- 建议先在测试环境验证 V3 版本功能
- 确认无误后再切换到生产环境
- 可以按业务模块逐步迁移

### 3. 监控指标
- **同步延迟**：从快递创建到下游同步完成的时间
- **并发处理数**：同时处理的快递数量
- **成功率**：快递同步成功的比例
- **处理进度**：已创建/预期总数的比例

## 风险控制

### 1. 并发控制
- 限制同时处理的快递数量，避免系统过载
- 完善的错误处理，单个快递失败不影响其他快递

### 2. 幂等性保证
- 支持重复执行不会造成问题
- 确保快递不会被重复创建或处理

### 3. 异常恢复
- 系统宕机后能正确恢复处理状态
- 支持从中断点继续执行，不丢失进度

## 总结

`InventoryReceiptStepV3` 通过引入增量监控机制，彻底解决了快递同步的延迟问题，实现了真正的实时同步。在保持 Temporal 框架兼容性的同时，显著提升了业务处理效率和用户体验。