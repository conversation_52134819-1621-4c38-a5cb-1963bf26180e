<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.saleorder.dao.OnlineSignatureMapper">


    <insert id="insert" parameterType="com.vedeng.erp.saleorder.model.po.ExpressOnlineReceiptRecordPo"
            useGeneratedKeys="true" keyProperty="expressOnlineReceiptRecordId">
        insert into T_EXPRESS_ONLINE_RECCEIPT_REORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="expressOnlineReceiptRecordId != null">
                EXPRESS_ONLINE_RECCEIPT_REORD_ID,
            </if>
            <if test="orderId != null">
                ORDER_ID,
            </if>
            <if test="traderName != null">
                TRADER_NAME,
            </if>
            <if test="traderId != null">
                TRADER_ID,
            </if>
            <if test="mobile != null">
                MOBILE,
            </if>
            <if test="userId != null">
                USER_ID,
            </if>
            <if test="comments != null">
                COMMENTS,
            </if>
            <if test="isEnable != null">
                IS_ENABLE,
            </if>
            <if test="signTime != null">
                SIGN_TIME,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="expressOnlineReceiptRecordId != null">
                #{expressOnlineReceiptRecordId,jdbcType=INTEGER},
            </if>
            <if test="orderId != null">
                #{orderId,jdbcType=INTEGER},
            </if>
            <if test="traderName != null">
                #{traderName,jdbcType=VARCHAR},
            </if>
            <if test="traderId != null">
                #{traderId,jdbcType=INTEGER},
            </if>
            <if test="mobile != null">
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=INTEGER},
            </if>
            <if test="comments != null">
                #{comments,jdbcType=VARCHAR},
            </if>
            <if test="isEnable != null">
                #{isEnable,jdbcType=INTEGER},
            </if>
            <if test="signTime != null">
                #{signTime,jdbcType=BIGINT},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>


</mapper>