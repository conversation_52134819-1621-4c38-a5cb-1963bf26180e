package com.vedeng.order.dao;


import com.vedeng.erp.buyorder.dto.BuyorderModifyApplyGoods;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Entity com.vedeng.order.dao.domain.TBuyorderModifyApplyGoods
 */
public interface BuyorderModifyApplyGoodsMapper {

    int deleteByPrimaryKey(Long id);

    int insert(BuyorderModifyApplyGoods record);

    int insertSelective(BuyorderModifyApplyGoods record);

    BuyorderModifyApplyGoods selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BuyorderModifyApplyGoods record);

    int updateByPrimaryKey(BuyorderModifyApplyGoods record);

    List<BuyorderModifyApplyGoods> selectAllByApplyId(Integer buyOrderModifyApplyId);

    /**
     * 订单流 迁移DB方法
     */
    int batchInsertSelective(@Param("records")List<com.vedeng.order.model.BuyorderModifyApplyGoods> records);
}
