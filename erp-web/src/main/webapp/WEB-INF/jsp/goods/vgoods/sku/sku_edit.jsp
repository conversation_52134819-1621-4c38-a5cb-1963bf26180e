<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>
        <c:if test="${command.skuId ==null or command.skuId <=0}">新增SKU</c:if>
        <c:if test="${command.skuId >0}">修改SKU</c:if>
    </title>
    <link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css" media="all">
    <script src="/webjars/ezadmin/plugins/layui/layui.js" charset="utf-8"></script>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/suggestSelect.css?rnd=${resourceVersionKey}">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/pages/goods/vgoods/sku/sku_edit.css?rnd=${resourceVersionKey}">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/pages/goods/vgoods/sku/chosen.min.css?rnd=${resourceVersionKey}">
    <style>
        .layui-form-radio{
            margin-top:0px !important;
        }
    </style>
<%--    <link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css?1">--%>
</head>

<body>
    <form action="${pageContext.request.contextPath}/goods/vgoods/saveSku.do" id="form_submit" class="J-form layui-form " method="POST">
        <input type="hidden" name="skuId" value="${command.skuId}">
        <input type="hidden" name="spuId" value="${command.spuId}">
        <input type="hidden" name="coreSkuHistory" value='${coreSkuHistory}'>

        <!--1 器械  2 耗材-->
        <input type="hidden" name="skuType" class="J-sku-type" value="${command.skuType}">
<%-- spuType:318-试剂 316-设备 317-耗材|| skuType :2-试剂或者耗材 1-设备--%>
        <input type="hidden"  class="J-spu-type" value="${coreSpuDto.spuType}">

        <div class="form-wrap">
            <div class="form-container base-form form-span-7">
                <div class="form-title">
                    <c:if test="${command.skuId ==null or command.skuId <=0}">新增SKU</c:if>
                    <c:if test="${command.skuId >0}">修改SKU</c:if>
                </div>

                <!-- 后台报错的区域 -->
                <c:forEach var="error" items="${command.errors}" varStatus="status">
                    <div class="vd-tip tip-red">
                        <i class="vd-tip-icon vd-icon icon-error2"></i>
                        <div class="vd-tip-cnt">${error}</div>
                    </div>
                </c:forEach>
                <c:forEach var="tip" items="${command.tips}" varStatus="status">
                    <div class="vd-tip tip-green">
                        <i class="vd-tip-icon vd-icon icon-yes1"></i>
                        <div class="vd-tip-cnt">${tip}</div>
                    </div>
                </c:forEach>
                <!-- end -->

                <%--SPU基本信息--%>
                <div class="form-block form-info">
                    <div class="form-block-title">SPU信息</div>
                    <div class="table-wrap">
                        <table class="table table-base">
                            <colgroup>
                                <col width="15%">
                                <col width="8%">
                                <col>
                                <col>
                                <col>
                                <col>
                                <col>

                            </colgroup>
                            <tbody>
                                <tr>
                                    <th>商品名称</th>
                                    <th>商品类型</th>
                                    <th>商品等级</th>
                                    <th>商品档位</th>
                                    <th>注册证号</th>
                                    <th>审核状态</th>
                                    <th>更新时间</th>
<%--                                    <th>Wiki资料</th>--%>
                                </tr>
                                <tr>
                                    <td class="J-spu-name" data-html="${coreSpuDto.spuShowName}">
                                        <a tabTitle='{"num":"vgoodsview${command.spuId}","link":"/goods/vgoods/viewSpu.do?spuId=${command.spuId}","title":"查看SPU"}' href="javascript:void(0);" class="">${coreSpuDto.spuShowName}</a>
                                    </td>
                                    <td>
                                        <c:forEach var="spuType" items="${spuTypeList}" varStatus="status">
                                            <c:if test="${coreSpuDto.spuType == spuType.sysOptionDefinitionId}"> ${spuType.title} </c:if>
                                        </c:forEach>
                                    </td>
                                    <td>${coreSpuDto.goodsLevelName}</td>
                                    <td>${coreSpuDto.goodsPositionName}</td>
                                    <td>${coreSpuDto.registrationNumber}</td>

                                    <td>${coreSpuDto.checkStatusShow}
                                    </td>

                                    <td>${coreSpuDto.modTimeShow}</td>
<%--                                    <td>--%>
<%--                                        <c:if test="${not empty coreSpuDto.wikiHref}">--%>
<%--                                        <a href="${coreSpuDto.wikiHref}" target="_blank">查看Wiki</a>--%>
<%--                                        </c:if>--%>
<%--                                    </td>--%>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <%--等级与档位--%>
                <div class="form-block form-info">
                    <div class="form-block-title">等级与档位</div>
                    <div class="form-item">
                        <div class="form-label"><span class="must">*</span>商品等级：</div>
                        <div class="form-fields  " style="width:300px">
                            <select class="J-select" name="goodsLevelNo"  lay-filter="goodsLevelNo">
                                <option value>请选择</option>
                                <c:forEach items="${goodsLevelList}" var="goodsLevel">
                                    <option value="${goodsLevel.id}"  <c:if test="${command.goodsLevelNo eq goodsLevel.id}">selected</c:if> >${goodsLevel.uniqueIdentifier}-${goodsLevel.levelName}</option>
                                </c:forEach>
                            </select>
                            <%--<input type="hidden" name="goodsLevelNo" value="${command.goodsLevelNo}">--%>
                            <div class="feedback-block" wrapfor="goodsLevelNo"></div>
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label"><span class="must">*</span>商品档位：</div>
                        <div class="form-fields l " style="width:300px">
                            <select class="J-select" name="goodsPositionNo">
                                <option value>请选择</option>
                                <c:forEach items="${goodsPositionList}" var="goodsPosition">
                                    <option value="${goodsPosition.id}" <c:if test="${command.goodsPositionNo eq goodsPosition.id}">selected</c:if> >${goodsPosition.positionName}</option>
                                </c:forEach>
                            </select>
                            <%--<input type="hidden" name="goodsPositionNo" value="${command.goodsPositionNo}">--%>
                            <div class="feedback-block" wrapfor="goodsPositionNo"></div>
                        </div>
                    </div>
                </div>

                <%--基本信息--%>
                <div class="form-block">
                    <div class="form-block-title">基本信息 </div>

                    <c:if test="${command.skuId>0}">
                        <div class="form-item">
                            <div class="form-label">订货号：</div>
                            <div class="form-fields">
                                <input type="hidden" name="skuNo" value="${skuGenerate.skuNo}">
                                <div class="fields-label">${skuGenerate.skuNo}</div>
                            </div>
                        </div>
                    </c:if>

                    <c:if test="${not empty coreSpuDto.spuName}">
                        <div class="form-item">
                            <div class="form-label"> <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">产品名称（注册证/备案凭证）：</div>
                            <div class="form-fields">
                                <div class="fields-label">${coreSpuDto.spuName}</div>
                            </div>
                        </div>
                    </c:if>

                    <c:if test="${not empty coreSpuDto.specsModel}">
                        <div class="form-item">
                            <div class="form-label"> <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">规格、型号（注册证/备案凭证）：</div>
                            <div class="form-fields">
                                <div class="fields-label">${coreSpuDto.specsModel}</div>
                            </div>
                        </div>
                    </c:if>

                    <div class="form-item">
                        <div class="form-label">
                            <c:if test="${command.skuType==1}"> <span class="must">*</span> </c:if> 制造商型号：
                        </div>
                        <div class="form-fields">
                            <div class="form-col col-6">
                                <input type="text" maxlength="50"  class='input-text <c:if test="${command.skuType==1}">J-model</c:if>' name="model" value="${skuGenerate.model}">
                                <div class="form-fields-tip">
                                    - 由于开票原因，制造商型号不能超过40个字符。<br>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label">
                            <c:if test="${command.skuType!=1}"> <span class="must">*</span> </c:if>规格：
                        </div>
                        <div class="form-fields">
                            <div class="form-col col-6">
                                <input   type="text" maxlength="50" class='input-text <c:if test="${command.skuType!=1}">J-model</c:if>' name="spec" value="${skuGenerate.spec}">
                                <div class="form-fields-tip">
                                    - 由于开票原因，规格不能超过40个字符。<br>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="J-optional-more show">
                    <div class="form-item">
                        <div class="form-label"><span class="must">*</span>商品名称：</div>
                        <div class="form-fields">
                            <div class="form-col col-6">
                                <input type="text"  maxlength="100"  class="input-text J-prod-name" name="skuName" value="${skuGenerate.skuName}">
                                <div class="form-fields-tip">
                                    - 由于开票原因，商品名称不能输入 * < > ',且不能超过100个字符。<br>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label"><span class="must">*</span>物料编码：</div>
                        <div class="form-fields">
                            <div class="form-col col-6">
                                <input type="text" class="input-text" name="materialCode" value="${skuGenerate.materialCode}">
                            </div>
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label"><span class="must">*</span>供应商型号：</div>
                        <div class="form-fields">
                            <div class="form-col col-6">
                                <input type="text" class="input-text" name="supplyModel" value="${skuGenerate.supplyModel}">
                            </div>
                            <div class="feedback-block" wrapfor="supplyModel"></div>
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="form-label">
                            <span class="must">*</span>商品条形码（69码）：
                        </div>
                        <div class="form-fields">
                            <div class="form-col col-6">
                                <input type="text" maxlength="50" class='input-text J-model' name="goodsBarcode" value="${skuGenerate.goodsBarcode}">
                            </div>
                            <div class="feedback-block" wrapfor="goodsBarcode"></div>
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label" style="cursor: help;" title="
                        UDI指医疗器械唯一标识，由产品标识DI和生产标识PI组成；
UDI-DI指医疗器械唯一标识中产品标识的部分，其代码指向厂商、产品、规格型号以及包装的属性。
                        ">
                            商品UDI-DI码&nbsp;<i    class="vd-tip-icon vd-icon icon-info2 goods-required-item" style="vertical-align: -3px;color: #09f; background: none;"></i>：
                        </div>
                        <div class="form-fields">
                            <div class="form-col col-6">
                                <input type="text" maxlength="20" onblur="" class='input-text J-model' name="diCode" value="${skuGenerate.diCode}">
                                <div class="form-fields-tip">
                                    - 若有，请务必填写。填写时，无需填写（01）前缀，如，06973720135656<br>
                                </div>
                            </div>
                            <div class="feedback-block" wrapfor="diCode"></div>
                        </div>
                    </div>

                    <!--器械设备 显示养护类型字段 -->
                    <c:if test="${coreSpuDto.spuType == 316}">
                        <div class="form-item">
                            <div class="form-label"><span class="must">*</span>定期维护：</div>
                            <div class="form-fields " style="width:300px" >
                                <select class="J-select" name="regularMaintainType" lay-filter="regularMaintainType" id="regularMaintainType">
                                    <option value="">请选择定期维护</option>
                                    <option value="0" <c:if test="${skuGenerate.regularMaintainType == 0}"> selected </c:if> >不维护</option>
                                    <option value="1" <c:if test="${skuGenerate.regularMaintainType == 1}"> selected </c:if> >一般维护</option>
                                    <option value="2" <c:if test="${skuGenerate.regularMaintainType == 2}"> selected </c:if> >定期维护</option>
                                </select>
                                <div class="feedback-block" wrapfor="regularMaintainType"></div>
                            </div>
                        </div>

                        <div class="form-item" id="regularMaintainReasonDiv" style="display: none;">
                            <div class="form-label">维护原因：</div>
                            <div class="form-fields">
                                <div class="form-col col-6">
                                    <textarea class="input-textarea" name="regularMaintainReason" id="regularMaintainReason" disabled>${skuGenerate.regularMaintainReason}</textarea>
                                </div>
                            </div>
                        </div>
				    </c:if>

                    <div class="form-item">
                        <div class="form-label"><span class="must">*</span>税收编码：</div>
                        <div class="form-fields">
                            <div class="form-col col-6">
                                <input type="text" readonly placeholder="请根据税收编码库选择" class="input-text" autocomplete="off" name="taxCategoryNo" valid-max="200" maxlength="19" value="${skuGenerate.taxCategoryNo}">
                            </div>
                            <button id="openDialog"  class="btn btn-blue btn-large" type="button">税收编码库</button>
                        </div>
                        <div class="form-fields">
                            <div class="form-col col-6">
                                <input type="text" readonly placeholder="自动带出简称" class="input-text" autocomplete="off" name="taxCodeSimpleName" valid-max="200" value="${skuGenerate.taxCodeSimpleName}">
                            </div>
                        </div>
                    </div>

                        <div class="form-item">
                        <div class="form-label"><span class="must">*</span>是否必须检测报告：</div>
                        <div class="form-fields">
                            <div class="input-radio">

                                    <input type="radio" name="isNeedTestReprot" title="是" value="1" <c:if test="${ skuGenerate.isNeedTestReprot ==1  }"> checked </c:if> >

                                    <input type="radio" name="isNeedTestReprot" title="否" value="0" <c:if test="${ skuGenerate.isNeedTestReprot ==0  }"> checked </c:if> >

                            </div>
							<div class="form-fields-tip">
							    - 若选是，则商品在入库时，物流必须该商品，出库的时候，物流会自行打印该批次的检测报告<br>
							</div>
                            <div class="feedback-block" wrapfor="isNeedTestReprot"></div>

                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label"><span class="must">*</span>是否套件：</div>
                        <div class="form-fields">
                            <div class="input-radio">

                                    <input type="radio" name="isKit" lay-filter="isKit" value="1" title="是" <c:if test="${ skuGenerate.isKit ==1  }"> checked </c:if> >

                                    <input type="radio" name="isKit" lay-filter="isKit"  title="否" value="0" <c:if test="${ skuGenerate.isKit ==0  }"> checked </c:if> >

                            </div>
							<div class="form-fields-tip">
							    - 商品在是否有多个组件组成<br>
							</div>
                            <div class="feedback-block" wrapfor="isKit"></div>

                        </div>
                    </div>

					<div class="form-item" id="kitDescDiv" style="display: none;">
						<div class="form-label"><span class="must">*</span>有哪几个套件：</div>
						<div class="form-fields">
							<div class="form-col col-6">
								<textarea class="input-textarea" name="kitDesc" id="kitDesc">${skuGenerate.kitDesc}</textarea>
							</div>
							<div class="form-fields-tip">
							    - 描述一下该套件的组成部分以及数量<br>
							</div>
                            <div class="feedback-block" wrapfor="kitDesc"></div>

                        </div>
					</div>

                    <div class="form-item" id="isSameSnCodeDiv" style="display: none;">
                        <div class="form-label"><span class="must">*</span>套件中子件的SN码是否必须一致：</div>
                        <div class="form-fields">
                            <div class="input-radio">

                                    <input type="radio" name="isSameSnCode" title="是" value="1" <c:if test="${ skuGenerate.isSameSnCode ==1  }"> checked </c:if> >

                                    <input type="radio" name="isSameSnCode" title="否" value="0" <c:if test="${ skuGenerate.isSameSnCode ==0  }"> checked </c:if> >

                            </div>
							<div class="form-fields-tip">
							    - 有些套件在销售过程中有具体的1对1的对应关系，若比较复杂，可在有哪几个套件的文本框中详细说明<br>
							</div>
                            <div class="feedback-block" wrapfor="isSameSnCode"></div>

                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label"><span class="must">*</span>是否厂家赋SN码：</div>
                        <div class="form-fields">
                            <div class="input-radio">
                                <label>
                                    <input id="isNS" type="radio" name="isFactorySnCode" title="是" value="1" <c:if test="${ skuGenerate.isFactorySnCode ==1  }"> checked </c:if> >
                                </label>
                                <label>
                                    <input id="isNotNS" type="radio" name="isFactorySnCode" title="否" value="0" <c:if test="${ skuGenerate.isFactorySnCode ==0  }"> checked </c:if> >
                                </label>
                            </div>
							<div class="form-fields-tip">
							    - SN码作为厂家赋予商品的唯一标识，若有，需要物流记录并管理<br>
							</div>
                            <div class="feedback-block" wrapfor="isFactorySnCode"></div>

                        </div>
                    </div>
                    <div class="form-item">
<%--                        <div class="form-label"><span class="must">*</span>是否管理贝登追溯码：</div>--%>
                        <div class="form-fields">
                            <div class="input-radio">
                                <label>
                                    <input id="isManage" type="hidden" name="isManageVedengCode" value="<c:if test="${skuGenerate.isManageVedengCode ==1 }">1</c:if><c:if test="${skuGenerate.isManageVedengCode ==0 }">0</c:if>">
                                </label>
                            </div>
<%--							<div class="form-fields-tip">--%>
<%--							    - 若需要管理到一物一码，则需要，否则不需要，例如呼吸机需要管理贝登追溯码，口罩则不需要<br>--%>
<%--							</div>--%>
                            <div class="feedback-block" wrapfor="isManageVedengCode"></div>

                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label"><span class="must">*</span>是否异形品：</div>
                        <div class="form-fields">
                            <div class="input-radio">

                                    <input type="radio" name="isBadGoods" title="是" value="1" <c:if test="${ skuGenerate.isBadGoods ==1  }"> checked </c:if> >

                                    <input type="radio" name="isBadGoods" title="否" value="0" <c:if test="${ skuGenerate.isBadGoods ==0  }"> checked </c:if> >

                            </div>
							<div class="form-fields-tip">
							    - 商品包装过大，或者形状不规则，则为异形品，例如康复床，球形商品等<br>
							</div>
                            <div class="feedback-block" wrapfor="isBadGoods"></div>
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label"><span class="must">*</span>是否启用厂家批号：</div>
                        <div class="form-fields">
                            <div class="input-radio">

                                    <input type="radio" name="isEnableFactoryBatchnum" title="是" value="1" <c:if test="${ skuGenerate.isEnableFactoryBatchnum ==1  }"> checked </c:if> >

                                    <input type="radio" name="isEnableFactoryBatchnum" title="否" value="0" <c:if test="${ skuGenerate.isEnableFactoryBatchnum ==0  }"> checked </c:if> >

                            </div>
							<div class="form-fields-tip">
							    - 厂家批号作为辅助管理商品的条码，若有，可填是<br>
							</div>
                            <div class="feedback-block" wrapfor="isEnableFactoryBatchnum"></div>
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="form-label"><span class="must">*</span>产品是否可安装：</div>
                        <div class="form-fields">
                            <div class="input-radio">

                                    <input type="radio" name="isInstallable" title="是" value="1" <c:if test="${skuGenerate.isInstallable eq 1}"> checked </c:if> >

                                    <input type="radio" name="isInstallable" title="否" value="0"  <c:if test="${skuGenerate.isInstallable ne 1}"> checked </c:if>>

                                <div class="tip-wrap"  style="position: absolute; z-index: 10">
                                    <i class="vd-icon icon-info2" >
                                        <div class="tip arrow-left">
                                            <div class="tip-con" >
                                                说明：商品本身是否具有安装特<br>性，
                                                如口罩为"不可安装"
                                            </div>
                                            <span class="arrow arrow-out"><span class="arrow arrow-in"></span></span>
                                        </div>
                                    </i>
                                </div>
                            </div>
                            <div class="form-fields-tip">
                                -  商品本身是否具有安装特性， 如口罩为"不可安装"<br>
                            </div>
                            <div class="feedback-block" wrapfor="isInstallable"></div>
                        </div>
                    </div>


                        <%--<div class="form-item">--%>
                            <%--<div class="form-label"><span class="must">*</span>是否可售：</div>--%>
                            <%--<div class="form-fields">--%>
                                <%--<div class="input-radio">--%>
                                    <%--<label class="input-wrap">--%>
                                        <%--<input type="radio" name="isAvailableSale" value="1" <c:if test="${skuGenerate.isAvailableSale ne 2}"> checked </c:if> >--%>
                                        <%--<span class="input-ctnr"></span>是--%>
                                    <%--</label>--%>
                                    <%--<label class="input-wrap">--%>
                                        <%--<input type="radio" name="isAvailableSale" value="2"  <c:if test="${skuGenerate.isAvailableSale eq 2}"> checked </c:if>>--%>
                                        <%--<span class="input-ctnr"></span>否--%>
                                    <%--</label>--%>
                                <%--</div>--%>

                                <%--<div class="feedback-block" wrapfor="isAvailableSale"></div>--%>
                            <%--</div>--%>
                        <%--</div>--%>




                    </div>
                    <div class="detail-optional J-toggle-show">
                        <span class="toggle-txt J-more" style="display: none;">展开更多选项信息<i class="vd-icon icon-down"></i></span>
                        <span class="toggle-txt J-less">收起<i class="vd-icon icon-up"></i></span>
                    </div>
                </div>


                <c:if test="${coreSpuDto.spuType == 316 && command.skuType==1}">
                    <div class="form-block">
                        <div class="form-block-title">配置清单</div>
                        <div class="form-item">
                            <div class="form-label">配置内容：</div>
                            <div class="form-fields J-conf-wrap J-conf-params" data-index="0">
                                <div class="sort-wrap J-sort-list">
                                    <div class="sort-item-wrap th-wrap cf">
                                        <div class="sort-item col-1">排序值</div>
                                        <div class="sort-item col-2">配置名称</div>
                                        <div class="sort-item col-3">配置数量</div>
                                    </div>
                                    <div id="ex2">
                                        <c:if test="${not empty command.configurationName}">
                                            <c:forEach items="${command.configurationName}" var="params" varStatus="status">
                                                <c:if test="${not empty command.configurationName[status.index] and command.configurationQuantity[status.index] ne null}">
                                                    <div class="sort-item-wrap J-conf-item cf">
                                                        <div class="sort-item col-1 item-center">
                                                            <input type="text" name="sort" class="input-text J-sort-num" maxlength="2">
                                                        </div>
                                                        <div class="sort-item col-2">
                                                            <input type="text" name="configurationName" value="${command.configurationName[status.index]}" autocomplete="off" valid-max="30" class="input-text J-conf-name">
                                                        </div>
                                                        <div class="sort-item col-3">
                                                            <input type="text" name="configurationQuantity" value="${command.configurationQuantity[status.index]}" autocomplete="off" valid-max="30" class="input-text J-conf-value">
                                                        </div>
                                                        <div class="col-1">
                                                            <i class="vd-icon icon-recycle J-conf-del" style="display: inline;"></i>
                                                        </div>
                                                    </div>
                                                </c:if>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${empty command.configurationName}">
                                            <div class="sort-item-wrap J-conf-item cf">
                                                <div class="sort-item col-1 item-center">
                                                    <input type="text" name="sort" class="input-text J-sort-num" maxlength="2">
                                                </div>
                                                <div class="sort-item col-2">
                                                    <input type="text" name="configurationName" autocomplete="off" valid-max="30" class="input-text J-conf-name">
                                                </div>
                                                <div class="sort-item col-3">
                                                    <input type="text" name="configurationQuantity" autocomplete="off" valid-max="30" class="input-text J-conf-value">
                                                </div>
                                                <div class="col-1">
                                                    <i class="vd-icon icon-recycle J-conf-del" style="display: none;"></i>
                                                </div>
                                            </div>
                                        </c:if>
                                    </div>
                                </div>
                                <input type="hidden" name="configurationValid">
                                <c:if test="${  empty command.configurationName || fn:length(command.configurationName)  <30}">
                                    <div class="sort-add J-sort-add-option">
                                        <a href="javascript:void(0);" class="sort-add-btn J-sort-add-conf">
                                            <i class="vd-icon icon-add"></i>新增配置
                                        </a>
                                        <a href="javascript:void(0);" class="sort-add-btn J-conf-import">复制文本导入配置</a>
                                    </div>
                                </c:if>
                            </div>
                        </div>
                    </div>
                </c:if>



                <c:if test="${not empty baseAttributeVoList}">
                <div class="form-block">
                    <div class="form-block-title">商品属性</div>
                    <c:forEach items="${baseAttributeVoList}" var="baseAttribute" varStatus="index">
                        <div class="form-item">
                            <div class="form-label"><span class="must attr-required-icon" style="display: inline-block;">*</span>${baseAttribute.baseAttributeName}：</div>
                            <div class="form-fields">
                                <div class="input-radio">
                                    <c:forEach items="${baseAttribute.attrValue}" var="attrValue">
                                        <label class="input-wrap">
                                            <input type="radio" content1="${baseAttribute.baseAttributeName}" title="${attrValue.attrValue}" name="baseAttributeValueId[${index.index}]" <c:if test="${ attrValue.selected  }"> checked </c:if> value="${attrValue.baseAttributeValueId}">

                                        </label>
                                    </c:forEach>
                                    <div class="feedback-block" wrapfor="baseAttributeValueId[${index.index}]"></div>
                                </div>
                            </div>
                        </div>
                    </c:forEach>
                </div>
                </c:if>

                <c:if test="${command.skuType==1}">
                    <div class="form-block">
                        <div class="form-block-title">参数信息</div>
                        <div class="form-item">
                            <div class="form-label"><span class="must">*</span>技术参数：</div>
                            <div class="form-fields J-sort-wrap J-tech-params" data-index="0">
                                <div class="sort-wrap J-sort-list">
                                    <div class="sort-item-wrap th-wrap cf">
                                        <div class="sort-item col-1">排序值</div>
                                        <div class="sort-item col-2">参数名称</div>
                                        <div class="sort-item col-3">参数值</div>
                                    </div>
									<div id="ex1">
                                    <c:if test="${not empty command.paramsName1}">
                                        <c:forEach items="${command.paramsName1}" var="params" varStatus="status">
                                            <c:if test="${not empty command.paramsName1[status.index] and command.paramsValue1[status.index] ne null}">
                                                <div class="sort-item-wrap J-sort-item cf">
                                                    <div class="sort-item col-1 item-center">
                                                        <input type="text" name="sort" class="input-text J-sort-num" maxlength="2">
                                                    </div>
                                                    <div class="sort-item col-2">
                                                        <input type="text" name="paramsName1"  value="${command.paramsName1[status.index]}" autocomplete="off" valid-max="30" class="input-text J-sort-name">
                                                    </div>
                                                    <div class="sort-item col-3">
                                                        <input type="text" name="paramsValue1" value="${command.paramsValue1[status.index]}" autocomplete="off" valid-max="30" class="input-text J-sort-value">
                                                    </div>
                                                    <div class="col-1">
                                                        <i class="vd-icon icon-recycle J-sort-del" style="display: inline;"></i>
                                                    </div>
                                                </div>
                                            </c:if>
                                        </c:forEach>
                                    </c:if>
                                    <c:if test="${empty command.paramsName1}">
                                        <div class="sort-item-wrap J-sort-item cf">
                                            <div class="sort-item col-1 item-center">
                                                <input type="text" name="sort" class="input-text J-sort-num" maxlength="2">
                                            </div>
                                            <div class="sort-item col-2">
                                                <input type="text" name="paramsName1" autocomplete="off" valid-max="30" class="input-text J-sort-name">
                                            </div>
                                            <div class="sort-item col-3">
                                                <input type="text" name="paramsValue1" autocomplete="off" valid-max="30" class="input-text J-sort-value">
                                            </div>
                                            <div class="col-1">
                                                <i class="vd-icon icon-recycle J-sort-del" style="display: none;"></i>
                                            </div>
                                        </div>
                                    </c:if>
                                    </div>
                                </div>
                                <input type="hidden" name="paramsValid">
                                <c:if test="${  empty command.paramsName1 || fn:length(command.paramsName1)  <30}">
                                <div class="sort-add J-sort-add-option">
                                    <a href="javascript:void(0);" class="sort-add-btn J-sort-add">
                                        <i class="vd-icon icon-add"></i>新增参数
                                    </a>
                                    <a href="javascript:void(0);" class="sort-add-btn J-sort-import">复制文本导入参数</a>
                                </div>
                                </c:if>
                            </div>
                        </div>
                    </div>
                </c:if>

                <div class="form-block">
                    <div class="form-block-title">物流和包装</div>
                    <c:if test="${isCopy == 1}">
                        <div class="form-item"  >
                            <div class="form-label"><span class="must">*</span>SKU商品单位：</div>
                            <div class="form-fields    " style="  width: 300px;"  >
                                <select lay-search   lay-filter="lay-baseUnitId" name="baseUnitId" id="baseUnitId" style= "width:200px;height: 28px">
                                    <option value="">请选择单位</option>
                                    <c:if test="${not empty unitList }">
                                        <c:forEach var="unit" items="${unitList}">
                                            <option value="${unit.unitId}" <c:if test="${ skuGenerate.baseUnitId == unit.unitId  }"> selected
                                            </c:if> >${unit.unitName}</option>
                                        </c:forEach>
                                    </c:if>
                                </select>
                                <div class="feedback-block" wrapfor="baseUnitId"></div>

                            </div>
                        </div>
                    </c:if>
                    <c:if test="${1 ne isCopy}">
                        <div class="form-item"  >
                            <div class="form-label"><span class="must">*</span>SKU商品单位：</div>
                            <div class="form-fields    " style="  width: 300px;"  >
                                <select lay-search   lay-filter="lay-baseUnitId" name="baseUnitId" id="baseUnitId" style= "width:200px;height: 28px" <c:if test="${!unitModifiable}"> disabled</c:if>>
                                    <option value="">请选择单位</option>
                                    <c:if test="${not empty unitList }">
                                        <c:forEach var="unit" items="${unitList}">
                                            <option value="${unit.unitId}" <c:if test="${ skuGenerate.baseUnitId == unit.unitId  }"> selected</c:if> >${unit.unitName}</option>
                                        </c:forEach>
                                    </c:if>
                                </select>
                                <c:if test="${!unitModifiable}">
                                    <span style="color: red">该sku商品已使用，不允许修改单位</span>
                                </c:if>
                                <div class="feedback-block" wrapfor="baseUnitId"></div>

                            </div>
                        </div>
                    </c:if>


                    <div class="J-optional-more show">





                        <div class="form-item">
                            <div class="form-label"><span class="must">*</span>发货方式：</div>
                            <div class="form-fields">
                                <div class="input-radio">
                                    <label class="input-wrap">
                                        <input type="radio" lay-filter="isDirect" name="isDirect" title="直发" value="1" <c:if test="${ skuGenerate.isDirect ==1   }"> checked </c:if> >
                                    </label>
                                    <label class="input-wrap">
                                        <input type="radio" lay-filter="isDirect" name="isDirect" title="普发" value="0" <c:if test="${ skuGenerate.isDirect ==0 or skuGenerate.isDirect==null }"> checked </c:if> >
                                    </label>

                                </div>
                                <div class="feedback-block" wrapfor="isDirect"></div>

                            </div>
                        </div>

                    <div class="form-item">
                        <div class="form-label"><span class="must">*</span>是否启用多级包装：</div>
                        <div class="form-fields">
                            <div class="input-radio">
                                <label class="input-wrap">
                                    <input type="radio" lay-filter="isEnableMultistagePackage" title="是" name="isEnableMultistagePackage" value="1" <c:if test="${ skuGenerate.isEnableMultistagePackage ==1  }"> checked </c:if> >
                                </label>
                                <label class="input-wrap">
                                    <input type="radio" lay-filter="isEnableMultistagePackage" title="否" name="isEnableMultistagePackage" value="0" <c:if test="${ skuGenerate.isEnableMultistagePackage ==0 or ((empty skuGenerate.isEnableMultistagePackage) and (coreSpuDto.spuType == 316 || coreSpuDto.spuType == 1008))}"> checked </c:if> >
                                    <!-- ||coreSpuDto.spuType == 316 || coreSpuDto.spuType == 1008 -->
                                </label>

                            </div>
							<div class="form-fields-tip">
							    - 商品是否允许拆零到最小销售单位，若允许，则为是<br>
							</div>
                            <div class="feedback-block" wrapfor="isEnableMultistagePackage"></div>

                        </div>
                    </div>



            <div class="form-item" id="midPackageNumDiv" style="display: none;">
                <div class="form-label">中包装数量：</div>
                <div class="form-fields">
                    <div class="form-col col-2">
                        <input type="text" class="input-text defaultZero" id="midPackageNum"  name="midPackageNum" value="${ skuGenerate.midPackageNum}" placeholder="">
                    </div>
                              <div class="form-fields-tip">
                                    - 若一个中包装含有100个sku商品单位的商品，则为100<br>
                                </div>
                    <div class="feedback-block" wrapfor="midPackageNum"></div>

                    <!-- <div class="form-col col-2">
                        <span class="J-sku-unit-value"></span>
                    </div>
                    <div class="feedback-block" wrapfor="changeNum"></div> -->
                </div>
            </div>
            <div class="form-item" id="boxPackageNumDiv" style="display: none;">
                <div class="form-label"><span class="must">*</span>箱包装数量：</div>
                <div class="form-fields">
                    <div class="form-col col-2">
                        <input type="text" class="input-text defaultZero" id="boxPackageNum" name="boxPackageNum" value="${ skuGenerate.boxPackageNum}" placeholder="">
                    </div>
                              <div class="form-fields-tip">
                                    - 若一个箱包装含有1000个sku商品单位的商品，则为1000<br>
                                </div>
                    <div class="feedback-block" wrapfor="boxPackageNum"></div>

                    <!-- <div class="form-col col-2">
                        <span class="J-sku-unit-value"></span>
                    </div>
                    <div class="feedback-block" wrapfor="changeNum"></div> -->
                </div>
            </div>

                    <%--VDERP-2212 ERP新商品流-新增/编辑SKU（器械设备、配件），增加最小起订量字段--%>
                    <%--<c:if test="${coreSpuDto.spuType == 316 || coreSpuDto.spuType == 1008}">--%>
                        <div class="form-item">
                            <div class="form-label">
                                <span class="must">*</span>最小起订量：</div>
                            <div class="form-fields">
                                <div class="form-col col-2">
                                    <input id="minOrder" type="text" class="input-text" name="minOrder"
                                           <%--onchange="checkMinOrder()"--%>
                                           value="<fmt:formatNumber  value="${skuGenerate.minOrder}"  pattern="0"/>" placeholder="">
                                    <%--<span style="color:red;" id="minOrderMsg"></span>--%>
                                </div>
                                <div class="form-col col-2">
                                    <span class="sku-unit-value unit"></span>
                                </div>
                                <div class="feedback-block" wrapfor="minOrder"></div>
                            </div>
                        </div>
                    <%--</c:if>--%>

                    <c:if test="${command.skuType!=1}">
                        <div class="form-item"  >
                            <div class="form-label">
                                <span class="must">*</span>商品最小单位：
                            </div>
                            <div class="form-fields " style="  width: 300px;">
<%--                                <select class="J-select J-sku-unit" name="unitId" id="unitId">--%>
<%--                                    <option value="">请选择单位</option>--%>
<%--                                    <c:if test="${not empty unitList }">--%>
<%--                                        <c:forEach var="unit" items="${unitList}">--%>
<%--                                            <option value="${unit.unitId}" <c:if test="${ skuGenerate.unitId ==unit.unitId  }"> selected--%>
<%--                                    </c:if> >${unit.unitName}</option>--%>
<%--                                    </c:forEach>--%>
<%--                                    </c:if>--%>
<%--                                </select>--%>
                                <select   lay-search lay-filter="lay-unitId" name="unitId" id="unitId" autocomplete="false" style= "width:200px;height: 28px">
                                    <option value="">请选择单位</option>
                                    <c:if test="${not empty unitList }">
                                        <c:forEach var="unit" items="${unitList}">
                                            <option value="${unit.unitId}" <c:if test="${ skuGenerate.unitId == unit.unitId  }"> selected
                                            </c:if> >${unit.unitName}</option>
                                        </c:forEach>
                                    </c:if>
                                </select>
                                <div class="feedback-block" wrapfor="minUnitId"></div>
                                <div style="display: none;" id="unitListsName">
                                    <c:if test="${not empty unitList }">
                                        <c:forEach var="unit" items="${unitList}">
                                            <c:if test="${skuGenerate.unitId ==unit.unitId }">
                                                 <span class="unitName_spad">${unit.unitName}</span>
                                            </c:if>
                                        </c:forEach>
                                    </c:if>
                                </div>
                            </div>
                        </div>

            <div class="form-item">
                <div class="form-label">
                    <span class="must">*</span>内含最小商品数量：
                </div>
                <div class="form-fields">
                    <div class="form-col col-2">
                        <input type="text" class="input-text" name="changeNum" value="${ skuGenerate.changeNum}" placeholder="">
                    </div>
                    <div class="form-col col-2">
                        <span class="J-sku-unit-value unit"></span>
                    </div>
                    <div class="feedback-block" wrapfor="changeNum"></div>
                </div>
            </div>

            <%--<div class="form-item">--%>
                <%--<div class="form-label">--%>
                    <%--<img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">--%>
                    <%--<span class="must">*</span>最小起订量：--%>
                <%--</div>--%>
                <%--<div class="form-fields">--%>
                    <%--<div class="form-col col-2">--%>
                        <%--<input id="minOrder" type="text" class="input-text" name="minOrder"--%>
                               <%--&lt;%&ndash;onchange="checkMinOrder()"&ndash;%&gt;--%>
                               <%--value="<fmt:formatNumber value="${skuGenerate.minOrder}" pattern="0"/>" placeholder="">--%>
                        <%--<span style="color:red;" id="minOrderMsg"></span>--%>
                    <%--</div>--%>
                    <%--<div class="form-col col-2">--%>
                        <%--<span class="sku-unit-value"></span>--%>
                    <%--</div>--%>
                    <%--<div class="form-fields-tip">--%>
                        <%---针对sku--%>
                    <%--</div>--%>
                    <%--<div class="feedback-block" wrapfor="minOrder"></div>--%>
                <%--</div>--%>
            <%--</div>--%>

            </c:if>


            <div class="form-item">
                <div class="form-label"><span class="must">*</span>箱包装体积：</div>
                <div class="form-fields">
                    <div class="form-col col-2">
                        <input type="text" class="input-text defaultZero" name="packageLength" value="${skuGenerate.packageLength}" placeholder="长">
                    </div>
                    <div class="form-col col-2">
                        <input type="text" class="input-text defaultZero" name="packageWidth" value="${skuGenerate.packageWidth}" placeholder="宽">
                    </div>
                    <div class="form-col col-2">
                        <input type="text" class="input-text defaultZero" name="packageHeight" value="${skuGenerate.packageHeight}" placeholder="高">
                    </div>
                    <span class="unit">厘米</span>
                    <div class="feedback-block" wrapfor="packageLength"></div>
                    <div class="feedback-block" wrapfor="packageWidth"></div>
                    <div class="feedback-block" wrapfor="packageHeight"></div>
                </div>
            </div>

            <div class="form-item">
                <div class="form-label"><span class="must">*</span>毛重：</div>
                <div class="form-fields">
                    <div class="form-col col-2">
                        <input type="text" class="input-text defaultZero" name="grossWeight" value="${skuGenerate.grossWeight}">
                    </div>
                    <span class="unit">KG</span>
                    <div class="feedback-block" wrapfor="grossWeight"></div>
                </div>
            </div>

            <c:if test="${command.skuType==1}">
                <div class="form-item">
                    <div class="form-label"><span class="must">*</span>包装清单：</div>
                    <div class="form-fields">
                        <div class="form-col col-6">
                            <textarea class="input-textarea" name="packingList">${skuGenerate.packingList}</textarea>
                        </div>
                        <div class="form-fields-tip">
                            -例：手机*1，电源适配器*1，USB Type-c数据线*1，SIM卡插针*1，高透软胶保护套*1，三包凭证/入门指南*1<br>
                        </div>
                        <div class="feedback-block" wrapfor="packingList"></div>
                    </div>
                </div>
            </c:if>

            </div>
            <div class="detail-optional J-toggle-show">
                <span class="toggle-txt J-more" style="display: none;">展开更多选项信息<i class="vd-icon icon-down"></i></span>
                <span class="toggle-txt J-less">收起<i class="vd-icon icon-up"></i></span>
            </div>
        </div>

        <%--<c:if test="${command.skuType!=1}">--%>
        <%--存储条件与效期--%>
            <div class="form-block">
                <div class="form-block-title">存储与效期</div>

                <%--新增页面时，提示框--%>
                <c:if test="${not empty storageCondAndEffectiveDateTips}">
                    <div class="expire-date-tips">
                        <div style="float:right;display:inline-block;">
                            <i class="vd-icon icon-delete" style="cursor: pointer;color: rgba(8,18,13,0.47);" onclick="closeExpireDateTips()"> </i>
                        </div>
                        <div style="float: left;padding-left: 20px;display:inline-block;">
                            <div style="float: left;">
                                <i class="vd-tip-icon vd-icon icon-info2" style="color: #2E8AE6;"></i>
                            </div>
                            <div style="float:left;display:inline-block;">
                                <span>《注册证/备案信息》</span> <br>
                                <span>产品存储及有效期：</span>
                                <span class="form-block-title-tip" style="margin-left: 0px;">${storageCondAndEffectiveDateTips}</span>
                            </div>
                        </div>
                    </div>
                </c:if>


                <%--存储条件,新增时带入所属spu的存储条件--%>
                <c:choose>
                    <c:when test="${command.skuId ==null or command.skuId <=0}">
                        <c:set var="goodsStorageConditionVo" value="${coreSpuDto}" scope="request"/>
                    </c:when>
                    <c:otherwise>
                        <c:set var="goodsStorageConditionVo" value="${goodsStorageConditionVo}" scope="request"/>
                    </c:otherwise>
                </c:choose>
                <c:set var="fromViewType" value="sku" scope="request"/>
                <jsp:include page="../commons/goodsStorageCondition.jsp" flush="true" ></jsp:include>

                <div class="J-optional-more show">
                    <div class="form-item">
                        <div class="form-label"> 使用年限：</div>
                        <div class="form-fields">
                            <input type="number" class="input-text"  name="serviceLife" id="serviceLife" maxlength="10" placeholder="正整数，最长10位"  value="${skuGenerate.serviceLife}" onblur="checkInteger(this)"> &nbsp;年
                        </div>
                        <script>
                            function checkInteger(inputElement) {
                                // 获取输入的值
                                var value = inputElement.value;

                                // 使用正则表达式检查是否为整数
                                var integerPattern = /^(0*[1-9]\d*)$/;

                                if (!integerPattern.test(value) || value.length >10) {
                                    // 如果不是整数，清空输入框
                                    inputElement.value = '';
                                }
                            }
                        </script>
                    </div>

                <div class="form-item">
                    <div class="form-label">
                        <span class="must">*</span>是否启用效期管理：
                    </div>
                    <div class="form-fields">
                        <div class="input-radio">
                            <label class="input-wrap">
                                <input type="radio" name="isEnableValidityPeriod" title="是"   lay-filter="isEnableValidityPeriod" value="1" <c:if test="${ skuGenerate.isEnableValidityPeriod ==1  }"> checked </c:if> >
                            </label>
                            <label class="input-wrap">
                                <input type="radio" name="isEnableValidityPeriod"  title="否"  lay-filter="isEnableValidityPeriod" value="0" <c:if test="${ skuGenerate.isEnableValidityPeriod ==0  }"> checked </c:if> >

                            </label>
                        </div>
                        <div class="feedback-block" wrapfor="isEnableValidityPeriod"></div>

                    </div>
                </div>

                <div id="isEnableValidityPeriodDiv" style="display: none;">
                <div class="form-item">
            <div class="form-label">
                <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                <span class="must">*</span>有效期：</div>
            <div class="form-fields">
                <div class="form-col col-2">
                    <input type="text" autocomplete="false" class="input-text defaultZero" id="effectiveDays" name="effectiveDays" value="${skuGenerate.effectiveDays}">
                </div>
                <span class="unit">天</span>
                <input type="hidden"   name="effectiveDayUnit" value="1">
<%--                <select class="J-select" name="effectiveDayUnit">--%>
<%--                    <option value="1" selected>天 </option>--%>
<%--&lt;%&ndash;                    <option value="2" <c:if test="${ skuGenerate.effectiveDayUnit==2 }"> selected </c:if> >月 </option>--%>
<%--                    <option value="3" <c:if test="${ skuGenerate.effectiveDayUnit==3 }"> selected </c:if> >年 </option>&ndash;%&gt;--%>
<%--                </select>--%>
            </div>
        </div>

                <div class="form-item">
                    <div class="form-label"> 近效期预警天数：</div>
                    <div class="form-fields">
<%--                        <div class="form-col  ">--%>
<%--                            <span class="unit" id="nearTermWarnDays">${skuGenerate.nearTermWarnDays}</span>--%>
<%--&lt;%&ndash;                            <input type="text" class="input-text defaultZero" name="nearTermWarnDays" value="${skuGenerate.nearTermWarnDays}"  readonly = "readonly" >&ndash;%&gt;--%>
<%--                        </div>--%>
                        <span class="unit" id="nearTermWarnDaysSpan">${skuGenerate.nearTermWarnDays}天</span>
                        <input type="hidden"  name="nearTermWarnDays" id="nearTermWarnDays" value="${skuGenerate.nearTermWarnDays}">

                    </div>
                </div>

                <div class="form-item">
                    <div class="form-label"> 超近效期预警天数：</div>
                    <div class="form-fields">
<%--                        <div class="form-col col-2">--%>
<%--                            <input type="text" class="input-text defaultZero" name="overNearTermWarnDays" value="${skuGenerate.overNearTermWarnDays}"  readonly = "readonly" >--%>
<%--                        </div>--%>
                        <span class="unit" id="overNearTermWarnDaysSpan">${skuGenerate.overNearTermWarnDays}天</span>
<%--                        <span class="unit">天</span>--%>
<%--                        <div class="feedback-block" wrapfor="overNearTermWarnDays"></div>--%>
    <input type="hidden"  name="overNearTermWarnDays" id="overNearTermWarnDays" value="${skuGenerate.overNearTermWarnDays}">
                    </div>
                </div>
                </div>

                <!--器械设备 显示养护类型字段 -->
                <c:if test="${coreSpuDto.spuType == 316}">
                    <div class="form-item">
                        <div class="form-label">养护类型（质量专用）：</div>
                        <div class="form-fields">
                            <div class="fields-label">
                                ${not empty command.curingTypeDesc?command.curingTypeDesc:'不养护'}
                            </div>
                            <input type="hidden" name="curingType" value=" ${not empty command.curingType?command.curingType:2}">
                        </div>
                    </div>
                </c:if>
                </div>
                <div class="detail-optional J-toggle-show">
                    <span class="toggle-txt J-more" style="display: none;">展开更多选项信息<i class="vd-icon icon-down"></i></span>
                    <span class="toggle-txt J-less">收起<i class="vd-icon icon-up"></i></span>
                </div>
        </div>

                <div class="form-block">
                    <div class="form-block-title">报备信息</div>

                    <div class="form-item">
                        <div class="form-label"><span class="must">*</span>是否需报备：</div>
                        <div class="form-fields">
                            <div class="input-radio">
                                <label class="input-wrap">
                                    <input type="radio" name="isNeedReport" value="1" title="需报备" <c:if
                                            test="${skuGenerate.isNeedReport == 1}"> CHECKED
                                    </c:if> onclick="changeauthorization()">
                                </label>
                                <label class="input-wrap">
                                    <input type="radio" name="isNeedReport" value="0" title="无需报备" onclick="changeauthorization()" <c:if
                                            test="${skuGenerate.isNeedReport == 0}"> CHECKED </c:if>>
                                </label>
                            </div>
                            <span style="color:red;" id="isNeedReportMsg"></span>
                            <input type="hidden"  id="isNeedReport" value="${skuGenerate.isNeedReport}">
                            <div class="feedback-block" wrapfor="isNeedReport"></div>
                        </div>
                    </div>

                    <div class="J-optional-more show">

                    <div class="form-item" id="isAuthorizedItem">
                        <div class="form-label"><span class="must">*</span>是否获得授权：</div>
                        <div class="form-fields">
                            <div class="input-radio">
                                <label class="input-wrap">
                                    <input type="radio" name="isAuthorized" title="有授权" value="1" onclick="changeauthorization()"
                                    <c:if test="${skuGenerate.isAuthorized == 1}"> checked </c:if> >
                                </label>
                                    <label class="input-wrap">
                                    <input type="radio" name="isAuthorized"title="无授权" value="0" onclick="changeauthorization()"
                                    <c:if test="${skuGenerate.isAuthorized == 0}"> checked </c:if> >
                                    </label>
                            </div>
                            <input type="hidden" id="isAuthorized" value="${skuGenerate.isAuthorized}">
                            <div class="feedback-block" wrapfor="isEnableValidityPeriod"></div>
                            <span style="color:red;" id="isAuthorizedMsg"></span>
                        </div>
                    </div>


                    <div class="form-item" id="authorizationItem">
                        <div class="form-label"><span class="must">*</span>授权范围：</div>
                        <c:forEach items="${skuAuthorizationResponses}" var="skuAuthorizationResponse" varStatus="index">
                            <div class="form-fields J-shouquan-item " style="display: flex">
                                <div class="fanwei-item">
                                    <div class="J-muiti-select fields-select-suggest"></div>
                                    <input type="hidden" class="J-value J-shouquan-input-1" name="regionsStr"
                                           value="${skuAuthorizationResponse.regionsStr}">
                                    <input type="hidden" class="J-shouquan-input-2" name="snowFlakeId"
                                           value="${skuAuthorizationResponse.snowFlakeId}">
                                    <select class="J-select" name="" id="">
                                        <c:forEach items="${regions}" var="region">
                                            <option value="${region.regionId}">${region.regionName}</option>
                                        </c:forEach>
                                    </select>
                                </div>
                                <div class="fanwei-item">
                                    <div class="J-muiti-select fields-select-suggest"></div>
                                    <input type="hidden" class="J-value J-shouquan-input-3" name="terminalTypesStr"
                                           value="${skuAuthorizationResponse.terminalTypesStr}">
                                    <select class="J-select" name="terminalType">
                                        <c:forEach items="${terminalTypes}" var="terminalType">
                                            <option value="${terminalType.sysOptionDefinitionId}">${terminalType.title}</option>
                                        </c:forEach>
                                    </select>
                                </div>
                                <i class="vd-icon icon-recycle J-sort-del authorization-del" style="display: inline;"></i>
                                <div class="feedback-block" wrapfor="curingType"></div>
                            </div>
                        </c:forEach>
                        <input type="hidden" id="authorizationValue" name="authorizationValue">
                        <div class="sort-add J-sort-add-option form-fields" style="display: flex">
                            <a href="javascript:void(0);" class="sort-add-btn J-sort-add authorization_add">
                                <i class="vd-icon icon-add authorization_add"></i>新增范围
                            </a>
                        </div>
                        <div class="sort-add  form-fields" style="display: flex">
                            <span style="color:red;" id="skuAuthorizationMsg"></span>
                        </div>
                    </div>
                    </div>
                    <div class="detail-optional J-toggle-show">
                        <span class="toggle-txt J-more" style="display: none;">展开更多选项信息<i class="vd-icon icon-down"></i></span>
                        <span class="toggle-txt J-less">收起<i class="vd-icon icon-up"></i></span>
                    </div>
                </div>

                <%--<div class="form-block">--%>
                    <%--<div class="form-block-title">归属信息</div>--%>
                    <%--<div class="form-item">--%>
                        <%--<div class="form-label">归属：</div>--%>
                        <%--<div class="form-fields">--%>
                            <%--<div class="input-checkbox">--%>
                                <%--<c:forEach var="org" items="${orgList}">--%>
                                    <%--<label class="input-wrap">--%>
                                        <%--<input type="checkbox"--%>
                                    <%--<c:forEach var="orgId" items="${skuGenerate.orgIdArray}">--%>
                                        <%--<c:if test="${orgId eq org.orgId}"> checked </c:if>--%>
                                        <%--</c:forEach>--%>
                                               <%--name="orgIdArray" value="${org.orgId}">--%>
                                        <%--<span class="input-ctnr"></span>${org.orgName}--%>
                                    <%--</label>--%>
                                <%--</c:forEach>--%>
                            <%--</div>--%>
                        <%--</div>--%>
                    <%--</div>--%>

                    <%--<div class="J-optional-more show"></div>--%>

                    <%--<div class="detail-optional J-toggle-show">--%>
                        <%--<span class="toggle-txt J-more" style="display: none;">展开更多选项信息<i class="vd-icon icon-down"></i></span>--%>
                        <%--<span class="toggle-txt J-less">收起<i class="vd-icon icon-up"></i></span>--%>
                    <%--</div>--%>
                <%--</div>--%>

                <div class="form-block">
                    <div class="form-block-title" style="display:inline-block;">产品资料</div>
                    <div class="detail-block-tip vd-tip tip-blue" style="display:inline-block;width: 85%;">
                        <i class="vd-tip-icon vd-icon icon-info2"></i>
                        <div class="vd-tip-cnt">最多可上传10张，图片格式为JPG/JPEG/PNG/BMP/PDF，文件大小不超过5M</div>
                    </div>
                    <div class="form-item" style="display:none">
                        <div class="form-label"><span class="must">*</span>Wiki链接：</div>
                        <div class="form-fields">
                            <div class="form-col col-6">
                                <input type="text" class="input-text" name="wikiHref" value="${skuGenerate.wikiHref}">
                            </div>
                            <div class="feedback-block" wrapfor="wikiHref"></div>
                        </div>
                    </div>

                    <div class="J-optional-more show">

                    <div class="form-item">
                        <div class="form-label">检测报告：</div>
                        <div class="form-fields">
                            <input type="hidden" class="J-upload-data" value='${command.skuCheckFilesJson}'>
                            <div class="J-upload" type="goodsDetectReportAttachment"></div>
                            <div class="form-fields-tip">
                                - 最多上传10个文件；<br>
                                - 检测报告原件扫描件；<br>
                                - 文件格式为JPG、JPEG、PNG、BMP、PDF；<br>
                                - 文件大小不超过5M；
                            </div>
                            <div class="feedback-block J-upload-error">
                                <label for="" class="error" style="display: none;"></label>
                            </div>
                        </div>
                    </div>
                    </div>
                    <div class="detail-optional J-toggle-show">
                        <span class="toggle-txt J-more" style="display: none;">展开更多选项信息<i class="vd-icon icon-down"></i></span>
                        <span class="toggle-txt J-less">收起<i class="vd-icon icon-up"></i></span>
                    </div>
                </div>

                <div class="form-btn">
                    <div class="form-fields">
                        <button type="button" class="btn btn-large close-spu-edit">取消</button>
                        <button type="submit" class="btn btn-blue btn-large" style="margin-left: 10px">提交</button>
                    </div>
                </div>
        </div>
        </div>
    </form>
    <script type="text/tmpl" class="J-sort-tmpl">
        <div class="sort-item-wrap J-sort-item cf">
            <div class="sort-item col-1 item-center">
                <input type="text" name="sort" class="input-text J-sort-num" maxlength="2">
            </div>
            <div class="sort-item col-2">
                <input type="text" name="{{=itemName}}" autocomplete="off" valid-max="30" class="input-text J-sort-name" value="{{=name}}">
                <div class="feedback-block"></div>
            </div>
            <div class="sort-item col-3">
                <input type="text" name="{{=itemValue}}" autocomplete="off" valid-max="30" class="input-text J-sort-value" value="{{=value}}">
            </div>
            <div class="col-1">
                <i class="vd-icon icon-recycle J-sort-del"></i>
            </div>
        </div>
    </script>

    <script type="text/tmpl" class="J-conf-tmpl">
        <div class="sort-item-wrap J-conf-item cf">
            <div class="sort-item col-1 item-center">
                <input type="text" name="sort" class="input-text J-sort-num" maxlength="2">
            </div>
            <div class="sort-item col-2">
                <input type="text" name="{{=confName}}" autocomplete="off" valid-max="30" class="input-text J-conf-name" value="{{=name}}">
                <div class="feedback-block"></div>
            </div>
            <div class="sort-item col-3">
                <input type="text" name="{{=confValue}}" autocomplete="off" valid-max="30" class="input-text J-conf-value" value="{{=value}}">
            </div>
            <div class="col-1">
                <i class="vd-icon icon-recycle J-conf-del"></i>
            </div>
        </div>
    </script>

    <script type="text/tmpl" class="J-import-tmpl-conf">
        <div class="import-wrap form-span-4 base-form">
            <div class="form-item">
                <div class="form-label">配置文本：</div>
                <div class="form-fields">
                    <div class="form-col col-11">
                        <textarea name="" id="" cols="30" rows="10" class="input-textarea J-import-cnt" placeholder='填写“配置1:配置数量1;配置2:配置数量2;配置3:配置数量3;”将进行自动拆分'></textarea>
                    </div>
                </div>
            </div>
        </div>
    </script>


    <script type="text/tmpl" class="J-import-tmpl">
        <div class="import-wrap form-span-4 base-form">
            <div class="form-item">
                <div class="form-label">参数文本：</div>
                <div class="form-fields">
                    <div class="form-col col-11">
                        <textarea name="" id="" cols="30" rows="10" class="input-textarea J-import-cnt" placeholder='填写“属性1:属性值1;属性2:属性值2;属性3:属性值3;”将进行自动拆分'></textarea>
                    </div>
                </div>
            </div>
        </div>
    </script>
    <script type="text/tmpl" class="J-shouquan-tmpl">
        <div class="form-fields J-shouquan-item" style="display: flex">
            <div class="fanwei-item">
                <div class="J-muiti-select fields-select-suggest"></div>
                <input type="hidden" class="J-value J-shouquan-input-1" name="region_value"
                       value="">
                <input type="hidden" class="J-shouquan-input-2" name="snowFlake_value"
                       value="">
                <select class="J-select" name="" id="">
                    <c:forEach items="${regions}" var="region">
                        <option value="${region.regionId}">${region.regionName}</option>
                    </c:forEach>
                </select>
            </div>
            <div class="fanwei-item">
                <div class="J-muiti-select fields-select-suggest"></div>
                <input type="hidden" class="J-value J-shouquan-input-3" name="terminalType_value"
                       value="">
                <select class="J-select" name="terminalType">
                    <c:forEach items="${terminalTypes}" var="terminalType">
                        <option value="${terminalType.sysOptionDefinitionId}">${terminalType.title}</option>
                    </c:forEach>
                </select>
            </div>
            <i class="vd-icon icon-recycle J-sort-del authorization-del"  style="display: inline;"></i>
            <div class="feedback-block" wrapfor="curingType"></div>
        </div>
    </script>
    <script>
        layui.use(['layer'], function() {
            var layer = layui.layer;

            document.getElementById('openDialog').onclick = function() {
                $.get('/category/base/taxCodePopup.do', function(htmlContent) {
                    layer.open({
                        type: 0,
                        title: '税务编码分页列表查询',
                        content: htmlContent,
                        area: ['1200px', '800px'],
                        btn: ['确定', '取消'],
                        yes: function(index, layero) {
                            debugger
                            var table = layui.table;
                            var checkStatus = table.checkStatus('dataTable');
                            var data = checkStatus.data;
                            // 如果没有选中任何数据，返回
                            if (data.length === 0) {
                                layer.msg('请先选择一个税收编码');
                                return;
                            }

                            // 获取选中的第一条数据
                            var selectedData = data[0];

                            // 将选中的 code 赋值给税收编码的 input 框
                            document.getElementsByName('taxCategoryNo')[0].value = selectedData.finalCode.trim();

                            // 将选中的 simpleName 赋值给自动带出简称的 input 框
                            document.getElementsByName('taxCodeSimpleName')[0].value = selectedData.classificationAbbreviation;

                            // 关闭弹出层
                            layer.close(index);
                        }
                    });
                });
            };
        });
    </script>
    <script src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
<%--    <script src="/webjars/ezadmin/plugins/layui/layui.js?1"></script>--%>
    <script src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
    <script src="${pageContext.request.contextPath}/static/new/js/common/util.js?rnd=${resourceVersionKey}"></script>
    <script src="${pageContext.request.contextPath}/static/new/js/common/select.js?rnd=${resourceVersionKey}"></script>
    <script src="${pageContext.request.contextPath}/static/new/js/common/upload.js?rnd=${resourceVersionKey}"></script>
    <script src="${pageContext.request.contextPath}/static/new/js/common/jquery.validate.js"></script>
    <script src="${pageContext.request.contextPath}/static/new/js/common/artDialog/2.0.0/artDialog.js?rnd=${resourceVersionKey}"></script>
    <script src="${pageContext.request.contextPath}/static/new/js/pages/goods/vgoods/sku_edit.js?rnd23=${resourceVersionKey}"></script>
    <script src="${pageContext.request.contextPath}/static/new/js/common/suggestSelect.js?rnd=${resourceVersionKey}"></script>
    <script src="${pageContext.request.contextPath}/static/js/Sortable.min.js?rnd=${resourceVersionKey}"></script>

    <script src="${pageContext.request.contextPath}/static/new/js/pages/goods/commons/goodsCommon.js?rnd=${resourceVersionKey}"></script>


<%@ include file="../../../common/footer.jsp"%>