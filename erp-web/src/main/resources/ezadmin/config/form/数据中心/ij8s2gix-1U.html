<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>售后银行信息修改</title> 
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="ij8s2gix-1U" datasource="erp-datasourcetarget" success_url="reload">
  <div id="APPEND_HEAD" class="append"></div> 
  <div class="layui-container"> 
   <form id="inputForm" method="post" class="layui-form"> 
    <div class="layui-card" group_name="基础信息">
     <div class="layui-card-header">
       基础信息 
     </div>
     <div class="layui-card-body">
      <div class="layui-form-item "> <label class="layui-form-label">售后单号</label> 
       <div class="layui-input-block form-group"> 
        <input class="form-item" name="AFTER_SALES_NO" type="span" value="" item_name="AFTER_SALES_NO" placeholder="" style="" alias="" jdbctype="" data="" datatype="" lay_verify="" item_desc="" group_data="基础信息" valid_rule="" valid_msg="" item_max_upload=""> 
       </div> 
      </div>
     </div>
    </div>
    <div class="layui-card" group_name="银行信息">
     <div class="layui-card-header">
       银行信息 
     </div>
     <div class="layui-card-body">
      <div class="layui-form-item "><label class="layui-form-label">开户行</label>
       <div class="layui-input-block form-group">
        <input class="form-item" name="BANK" type="text" value="" item_name="BANK" placeholder="" style="" alias="" jdbctype="" data="" datatype="" lay_verify="" item_desc="" group_data="银行信息" valid_rule="" valid_msg="" item_max_upload="">
       </div>
      </div>
      <div class="layui-form-item ">
       <label class="layui-form-label">银行账号</label>
       <div class="layui-input-block form-group">
        <input class="form-item" name="BANK_ACCOUNT" type="text" value="" item_name="BANK_ACCOUNT" placeholder="" style="" alias="" jdbctype="" data="" datatype="" lay_verify="" item_desc="" group_data="银行信息" valid_rule="" valid_msg="" item_max_upload="">
       </div>
      </div>
      <div class="layui-form-item "><label class="layui-form-label">开户行支付联行号</label>
       <div class="layui-input-block form-group">
        <input class="form-item" name="BANK_CODE" type="text" value="" item_name="BANK_CODE" placeholder="" style="" alias="" jdbctype="" data="" datatype="" lay_verify="" item_desc="" group_data="银行信息" valid_rule="" valid_msg="" item_max_upload="">
       </div>
      </div>
     </div>
    </div>
    <div class="layui-card" group_name="售后信息">
     <div class="layui-card-header">
       售后信息 
     </div>
     <div class="layui-card-body">
      <div class="layui-form-item ">
       <label class="layui-form-label">付款方</label>
       <div class="layui-input-block form-group">
        <input class="form-item" name="PAYEE" type="text" value="" item_name="PAYEE" placeholder="" style="" alias="" jdbctype="" data="" datatype="" lay_verify="" item_desc="" group_data="售后信息" valid_rule="" valid_msg="" item_max_upload="">
       </div>
      </div>
      <div class="layui-form-item ">
       <label class="layui-form-label">交易主体</label>
       <div class="layui-input-block form-group"><select class="form-item" name="TRADER_SUBJECT" type="search" value="" item_name="TRADER_SUBJECT" placeholder="" style="" alias="" jdbctype="" data="[{&quot;V&quot;:&quot;无&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;对公&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;对私&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" lay_verify="" item_desc="" group_data="售后信息" valid_rule="" valid_msg="" item_max_upload=""></select>
       </div>
      </div>
      <div class="layui-form-item ">
       <label class="layui-form-label">款项退还</label>
       <div class="layui-input-block form-group">
        <select class="form-item" name="REFUND" type="search" value="" item_name="REFUND" placeholder="" style="" alias="" jdbctype="" data="[{&quot;V&quot;:&quot;无&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;退到客户余额&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;退给客户&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" lay_verify="" item_desc="" group_data="售后信息" valid_rule="" valid_msg="" item_max_upload=""></select>
       </div>
      </div>
      <div class="layui-form-item ">
       <label class="layui-form-label">交易方式</label>
       <div class="layui-input-block form-group"><select class="form-item" name="TRADER_MODE" type="search" value="" item_name="TRADER_MODE" placeholder="" style="" alias="" jdbctype="" data="select SYS_OPTION_DEFINITION_ID K,TITLE V
from T_SYS_OPTION_DEFINITION WHERE PARENT_ID=519" datatype="KVSQLCACHE" lay_verify="" item_desc="" group_data="售后信息" valid_rule="" valid_msg="" item_max_upload=""></select>
       </div>
      </div>
     </div>
    </div>

   </form> 
   <form id="configForm" class="layui-form"> 
    <div class="layui-card" group_name="表单数据配置"> 
     <div class="layui-card-header">
       表单数据配置 
     </div> 
     <div class="layui-card-body"> 
     </div> 
    </div> 
    <div class="layui-form-item "><label class="layui-form-label">初始表达式</label> 
     <div class="layui-input-block form-group"> 
      <pre class="form-item layui-code" id="init" name="INIT_EXPRESS" type="">list=select("SELECT
        a.AFTER_SALES_ID ID,b.TRADER_MODE,
	a.AFTER_SALES_NO,
	a.ATFER_SALES_STATUS,
       b.BANK,b.BANK_ACCOUNT,b.BANK_CODE,b.PAYEE,b.REFUND,b.TRADER_SUBJECT
FROM
	T_AFTER_SALES a
	LEFT JOIN T_AFTER_SALES_DETAIL b ON a.AFTER_SALES_ID = b.AFTER_SALES_ID


WHERE
	a.TYPE   IN ( 543, 539 )
	AND a.COMPANY_ID = 1
	AND a.ATFER_SALES_STATUS  in (0,1) and b.AFTER_SALES_ID= ${ID}");
return list.get(0);</pre> 
     </div> 
    </div>
    <div class="layui-form-item "> <label class="layui-form-label">提交表达式</label> 
     <div class="layui-input-block form-group"> 
      <pre class="form-item layui-code" id="submit" name="SUBMIT_EXPRESS" type="">if(null==ID||"".equals(ID)){
     return 0;
}else{
update("
UPDATE T_AFTER_SALES_DETAIL
SET BANK_ACCOUNT = #{BANK_ACCOUNT},
BANK = #{BANK},
REFUND= #{REFUND},TRADER_SUBJECT= #{TRADER_SUBJECT},

BANK_CODE = #{BANK_CODE} ,PAYEE=#{PAYEE},TRADER_MODE=#{TRADER_MODE}
WHERE
        AFTER_SALES_ID = #{ID}");
return ID;
}</pre> 
     </div> 
    </div>
    <div class="layui-form-item "> <label class="layui-form-label">删除表达式</label> 
     <div class="layui-input-block form-group"> 
      <pre class="form-item layui-code" id="delete" name="DELETE_EXPRESS" type=""></pre> 
     </div> 
    </div>
    <div class="layui-form-item "><label class="layui-form-label">分组表达式</label> 
     <div class="layui-input-block form-group"> 
      <pre class="form-item layui-code" name="GROUP_DATA" type="">[ {"V":"基础信息","K":"基础信息","type":"inline"}, {"V":"银行信息","K":"银行信息","type":"block"}, {"V":"售后信息","K":"售后信息","type":"block"} ]</pre> 
     </div> 
    </div>
   </form> 
  </div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
  <div id="APPEND_FOOT" class="append"> 
   <script>

    </script> 
  </div> 
 </body>
</html>