package com.newtask.logistics;

import com.alibaba.fastjson.JSON;
import com.ezadmin.common.utils.Utils;
import com.github.pagehelper.PageHelper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.model.OssInfo;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.ftpclient.client.FTPClientHelper;
import com.vedeng.logistics.dao.ext.WmsWarehouseGoodsOperateLogDataExtMapper;
import com.vedeng.logistics.model.WmsWarehouseGoodsOperateLogData;
import com.vedeng.logistics.model.WmsWarehouseGoodsOperateLogDataExample;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.service.OrgService;
import com.vedeng.system.service.OssUtilsService;
import com.vedeng.system.service.UserService;
import com.vedeng.system.service.impl.OssUtilsServiceImpl;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.service.TraderCustomerService;
import com.wms.service.WmsImagesService;
import com.wms.service.util.WMSFTPUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import top.ezadmin.common.utils.JSONUtils;
import top.ezadmin.common.utils.StringUtils;
import top.ezadmin.dao.Dao;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@JobHandler(value = "warehouseGoodsOperateLogDataTask")
@Slf4j
public class WarehouseGoodsOperateLogDataTask extends AbstractJobHandler {

    @Autowired
    @Qualifier("userService")
    private UserService userService;// 自动注入userService

    @Autowired
    @Qualifier("saleorderService")
    private SaleorderService saleorderService;

    @Autowired
    @Qualifier("orgService")
    private OrgService orgService;

    @Value("${wms.oracle.username:WMS_USER_TEST}")
    String wmsOracleUser;
    @Resource
    private AttachmentMapper attachmentMapper;

    @Resource
    WmsWarehouseGoodsOperateLogDataExtMapper wmsWarehouseGoodsOperateLogDataExtMapper;
    private static Integer MIN_ID = 6465643;
    @Autowired
    @Qualifier("wmsDataSourceTarget")
    DataSource wmsDatasource;

    @Autowired
    @Qualifier("wmsFtpClientHelper")
    private FTPClientHelper wmsFtpClientHelper;

    @Autowired
    private OssUtilsService ossUtilsService;

    @Autowired
    WmsImagesService wmsImagesService;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        //快递重新初始化
        if(StringUtils.equals(param,"initExpress")){
            WmsWarehouseGoodsOperateLogDataExample exampleExpress= new WmsWarehouseGoodsOperateLogDataExample();
            exampleExpress.createCriteria().andIsDeleteEqualTo(new Byte("0"));
            PageHelper.startPage(1,1000);
            List<WmsWarehouseGoodsOperateLogData> existList=wmsWarehouseGoodsOperateLogDataExtMapper.selectByExample(exampleExpress);
            if(CollectionUtils.isNotEmpty(existList)){
                existList.forEach(item->{
                    int businessType = 0;
                    String expressNos="";
                    if(item.getOperateType()==2){
                        businessType=496;
                        expressNos = wmsWarehouseGoodsOperateLogDataExtMapper.selectExpressNosByRidAndBtype(item.getRelatedId(),businessType);
                    }else if(item.getOperateType()==4){
                        businessType=582;
                        expressNos = wmsWarehouseGoodsOperateLogDataExtMapper.selectExpressNosByRidAndBtype(item.getRelatedId(),businessType);
                    }
                    WmsWarehouseGoodsOperateLogData data1 = new WmsWarehouseGoodsOperateLogData();
                    data1.setIsDelete(new Byte("2"));
                    data1.setId(item.getId());
                    if(StringUtils.isNotBlank(expressNos)){
                        data1.setExpressNos(expressNos);
                    }else{
                        data1.setExpressNos("-");
                    }
                    //售后重新计算归属销售与客户
                    if(item.getOperateType()==4){
                        if (item.getTraderId() != null) {
                            Trader trader = traderCustomerService.getTraderByTraderId(item.getTraderId());
                            if (trader != null) {
                                data1.setTraderName(trader.getTraderName());
                            }
                            User user = userService.getUserByTraderId(item.getTraderId(), 1);
                            if (user != null) {
                                data1.setUserName(user.getUsername());
                                User userOrg = orgService.getTraderUserAndOrgByTraderId(item.getTraderId(), 1);
                                data1.setDeptName(userOrg == null ? "" : userOrg.getOrgName());
                            }
                        }
                    }
                    XxlJobLogger.log("更新 {} {}  ",JSON.toJSONString(data1));
                    wmsWarehouseGoodsOperateLogDataExtMapper.updateByPrimaryKeySelective(data1);
                });
            }

            PageHelper.clearPage();
            return ReturnT.SUCCESS;
        }

        if(StringUtils.startsWith(param,"ids,")){
            //根据参数ID区间跑数据，最大1000条
            param=param.replace("ids,","");
            String[] ids = StringUtils.split(param,",");
            List<Integer> list = Arrays.stream(ids)
                    .map(s->Integer.parseInt(s.trim()))
                    .collect(Collectors.toList());
            List<WmsWarehouseGoodsOperateLogData> logs = wmsWarehouseGoodsOperateLogDataExtMapper
                    .selectByIds(list);
            for (WmsWarehouseGoodsOperateLogData log : logs) {
                doLog(log);
            }
            return ReturnT.SUCCESS;
        }
        if(StringUtils.isNotBlank(param)){
            //根据参数ID区间跑数据，最大1000条
            String p[]=StringUtils.split(param,",");
            int limit=Math.min(1000,Integer.parseInt(p[1]));
            List<WmsWarehouseGoodsOperateLogData> logs = wmsWarehouseGoodsOperateLogDataExtMapper
                    .selectByMinId(Integer.parseInt(p[0]),limit);
            for (WmsWarehouseGoodsOperateLogData log : logs) {
                doLog(log);
            }
            return ReturnT.SUCCESS;
        }
        Integer maxLogId = wmsWarehouseGoodsOperateLogDataExtMapper.selectMaxLogId();
        int startId = 6465643;
        if (maxLogId != null) {
            startId = Math.max(maxLogId, startId);
        }
        log.info("startId:{}", startId);

        List<WmsWarehouseGoodsOperateLogData> logs = wmsWarehouseGoodsOperateLogDataExtMapper
                .selectByMinId(startId, 1000);
        //  do {
        for (WmsWarehouseGoodsOperateLogData log : logs) {
            doLog(log);
        }
        //  startId=logs.get(logs.size()-1).getWarehouseGoodsOperateLogId();
//            logs = wmsWarehouseGoodsOperateLogDataExtMapper.selectByMinId(startId,1000);
//        }while(CollectionUtils.isNotEmpty(logs));

        return ReturnT.SUCCESS;
    }

    @Autowired
    @Qualifier("traderCustomerService")
    private TraderCustomerService traderCustomerService;

    private void doLog(WmsWarehouseGoodsOperateLogData data) {
        data.setIsDelete(new Byte("2"));
        //补充缺的数据，再保存
        if (data.getOperateType() == 2||data.getOperateType() == 4) {
            //客户归属销售归属部门
            if (data.getTraderId() != null) {
                Trader trader = traderCustomerService.getTraderByTraderId(data.getTraderId());
                if (trader != null) {
                    data.setTraderName(trader.getTraderName());
                }
                User user = userService.getUserByTraderId(data.getTraderId(), 1);
                if (user != null) {
                    data.setUserName(user.getUsername());
                    User userOrg = orgService.getTraderUserAndOrgByTraderId(data.getTraderId(), 1);
                    data.setDeptName(userOrg == null ? "" : userOrg.getOrgName());
                }
            }
            //补充快递单
            String expressNos = wmsWarehouseGoodsOperateLogDataExtMapper.selectExpressNosByRidAndBtype(data.getRelatedId(),data.getOperateType());
            data.setExpressNos(expressNos);
        }
        //从WMS中找到检测报告
        String lastestReport = null;String lastestReportOss = null;
        try {
            if (StringUtils.isNotBlank(data.getBatchNumber())) {
                WmsWarehouseGoodsOperateLogDataExample example2 = new WmsWarehouseGoodsOperateLogDataExample();
                example2.createCriteria().andSkuNoEqualTo(data.getSkuNo()).andBatchNumberEqualTo(data.getBatchNumber());
                List<WmsWarehouseGoodsOperateLogData> existList=wmsWarehouseGoodsOperateLogDataExtMapper.selectByExample(example2);
                if(CollectionUtils.isNotEmpty(existList)){
                    lastestReport=existList.get(0).getQualityReport();
                    lastestReportOss=existList.get(0).getQualityReportOss();
                }
                long start=System.currentTimeMillis();
                String files = wmsImagesService.doReportBySkuAndBatchNum(data.getSkuNo(), data.getBatchNumber(),lastestReport,lastestReportOss);
                if (StringUtils.isNotBlank(files)) {
                    String fileArray[] = files.split("@@");
                    data.setQualityReportOss(fileArray[0]);
                    data.setQualityReport(fileArray[1]);
                }
                log.info("查询检测报告耗时{}",System.currentTimeMillis()-start);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        //如果已经存在
        WmsWarehouseGoodsOperateLogDataExample example = new WmsWarehouseGoodsOperateLogDataExample();
        example.createCriteria().andWarehouseGoodsOperateLogIdEqualTo(data.getWarehouseGoodsOperateLogId());
        List<WmsWarehouseGoodsOperateLogData> list = wmsWarehouseGoodsOperateLogDataExtMapper.selectByExample(example);
        XxlJobLogger.log("库存日志宽表{} {} {} ", data.getWarehouseGoodsOperateLogId(),
                data.getSkuNo(), data.getBatchNumber(), data.getQualityReportOss());
        if (CollectionUtils.isNotEmpty(list)) {
            log.info("更新库存日志宽表{}", data.getWarehouseGoodsOperateLogId());
            data.setId(list.get(0).getId());
            wmsWarehouseGoodsOperateLogDataExtMapper.updateByPrimaryKeySelective(data);
        } else {
            log.info("新增库存日志宽表{}", data.getWarehouseGoodsOperateLogId());
            wmsWarehouseGoodsOperateLogDataExtMapper.insert(data);
        }
        //如果检测报告没有变化，不更新
        if(StringUtils.equals(lastestReportOss,data.getQualityReportOss())){
            return;
        }
        //按照批次+sku更新全量数据
        if (StringUtils.isNotBlank(data.getBatchNumber()) && StringUtils.isNotBlank(data.getQualityReportOss())
        &&!StringUtils.equals("NO-LOT",data.getBatchNumber())) {
            {
                WmsWarehouseGoodsOperateLogDataExample example2 = new WmsWarehouseGoodsOperateLogDataExample();
                example2.createCriteria().andSkuNoEqualTo(data.getSkuNo()).andBatchNumberEqualTo(data.getBatchNumber());

                List<WmsWarehouseGoodsOperateLogData> existList=wmsWarehouseGoodsOperateLogDataExtMapper.selectByExample(example2);
                if(CollectionUtils.isEmpty(existList)){
                    return;
                }
                if (existList.size()>1000){
                    log.error("批次{}{}数量大于1000,需人工检查",data.getSkuNo(),data.getBatchNumber());
                    return;
                }
                existList.forEach(item->{
                    if(!StringUtils.equals(Utils.trimNull(data.getQualityReportOss()),Utils.trimNull(item.getQualityReportOss()))){
                        WmsWarehouseGoodsOperateLogData data1 = new WmsWarehouseGoodsOperateLogData();
                        data1.setQualityReportOss(Utils.trimNull(data.getQualityReportOss()));
                        data1.setQualityReport(Utils.trimNull(data.getQualityReport()));
                        data1.setId(item.getId());
                        wmsWarehouseGoodsOperateLogDataExtMapper.updateByPrimaryKeySelective(data1);
                        log.info("更新{}",item.getWarehouseGoodsOperateLogId());
                    }
                });
                log.info("全部批次更新{}", data.getWarehouseGoodsOperateLogId());
            }
        }
    }
}
