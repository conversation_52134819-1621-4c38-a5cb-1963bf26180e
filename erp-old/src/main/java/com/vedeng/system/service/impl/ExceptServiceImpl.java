package com.vedeng.system.service.impl;

import com.vedeng.erp.kingdee.service.KingDeeEventMsgApiService;
import com.vedeng.system.service.ExceptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2023/7/15
 */
@Service("exceptService")
public class ExceptServiceImpl implements ExceptService {

    @Autowired
    private KingDeeEventMsgApiService kingDeeEventMsgApiService;

    public void deleteEventMsgByMessageid(Integer kingDeeEventMsgId, Integer userId){
        kingDeeEventMsgApiService.deleteEventMsgByMessageid(kingDeeEventMsgId,userId);
    }

}
