<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.order.dao.AdkGoodsMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.order.model.adk.TAdkGoods">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 20:42:17 CST 2019.
    -->
    <id column="adk_goods_id" jdbcType="INTEGER" property="adkGoodsId" />
    <result column="adk_goods_code" jdbcType="VARCHAR" property="adkGoodsCode" />
    <result column="adk_goods_name" jdbcType="VARCHAR" property="adkGoodsName" />
    <result column="adk_goods_model" jdbcType="VARCHAR" property="adkGoodsModel" />
    <result column="adk_goods_unit" jdbcType="VARCHAR" property="adkGoodsUnit" />
    <result column="erp_goods_sku" jdbcType="VARCHAR" property="erpGoodsSku" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="erp_goods_id" jdbcType="INTEGER" property="erpGoodsId" />
    <result column="add_no" jdbcType="VARCHAR" property="addNo" />
    <result column="add_name" jdbcType="VARCHAR" property="addName" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_no" jdbcType="VARCHAR" property="updateNo" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="change_num" jdbcType="INTEGER" property="changeNum" />
    <result column="erp_Sku_Price" jdbcType="DECIMAL" property="erpSkuPrice" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 20:42:17 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 20:42:17 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 20:42:17 CST 2019.
    -->
    adk_goods_id, adk_goods_code, adk_goods_name, adk_goods_model, adk_goods_unit, erp_goods_sku, 
    status, erp_goods_id, add_no, add_name, add_time, update_no, update_name, update_time,change_num,erp_Sku_Price
  </sql>
  <select id="selectByExample" parameterType="com.vedeng.order.model.adk.TAdkGoodsExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 20:42:17 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_ADK_GOODS
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 20:42:17 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from T_ADK_GOODS
    where adk_goods_id = #{adkGoodsId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 20:42:17 CST 2019.
    -->
    delete from T_ADK_GOODS
    where adk_goods_id = #{adkGoodsId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.order.model.adk.TAdkGoodsExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 20:42:17 CST 2019.
    -->
    delete from T_ADK_GOODS
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.order.model.adk.TAdkGoods">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 20:42:17 CST 2019.
    -->
    <selectKey keyProperty="adkGoodsId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_ADK_GOODS (adk_goods_code, adk_goods_name, adk_goods_model, 
      adk_goods_unit, erp_goods_sku, status, 
      erp_goods_id, add_no, add_name, 
      add_time, update_no, update_name, 
      update_time)
    values (#{adkGoodsCode,jdbcType=VARCHAR}, #{adkGoodsName,jdbcType=VARCHAR}, #{adkGoodsModel,jdbcType=VARCHAR}, 
      #{adkGoodsUnit,jdbcType=VARCHAR}, #{erpGoodsSku,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, 
      #{erpGoodsId,jdbcType=INTEGER}, #{addNo,jdbcType=VARCHAR}, #{addName,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateNo,jdbcType=VARCHAR}, #{updateName,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.order.model.adk.TAdkGoods">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 20:42:17 CST 2019.
    -->
    <selectKey keyProperty="adkGoodsId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_ADK_GOODS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="adkGoodsCode != null">
        adk_goods_code,
      </if>
      <if test="adkGoodsName != null">
        adk_goods_name,
      </if>
      <if test="adkGoodsModel != null">
        adk_goods_model,
      </if>
      <if test="adkGoodsUnit != null">
        adk_goods_unit,
      </if>
      <if test="erpGoodsSku != null">
        erp_goods_sku,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="erpGoodsId != null">
        erp_goods_id,
      </if>
      <if test="addNo != null">
        add_no,
      </if>
      <if test="addName != null">
        add_name,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateNo != null">
        update_no,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="adkGoodsCode != null">
        #{adkGoodsCode,jdbcType=VARCHAR},
      </if>
      <if test="adkGoodsName != null">
        #{adkGoodsName,jdbcType=VARCHAR},
      </if>
      <if test="adkGoodsModel != null">
        #{adkGoodsModel,jdbcType=VARCHAR},
      </if>
      <if test="adkGoodsUnit != null">
        #{adkGoodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="erpGoodsSku != null">
        #{erpGoodsSku,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="erpGoodsId != null">
        #{erpGoodsId,jdbcType=INTEGER},
      </if>
      <if test="addNo != null">
        #{addNo,jdbcType=VARCHAR},
      </if>
      <if test="addName != null">
        #{addName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateNo != null">
        #{updateNo,jdbcType=VARCHAR},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.order.model.adk.TAdkGoodsExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 20:42:17 CST 2019.
    -->
    select count(*) from T_ADK_GOODS
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 20:42:17 CST 2019.
    -->
    update T_ADK_GOODS
    <set>
      <if test="record.adkGoodsId != null">
        adk_goods_id = #{record.adkGoodsId,jdbcType=INTEGER},
      </if>
      <if test="record.adkGoodsCode != null">
        adk_goods_code = #{record.adkGoodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.adkGoodsName != null">
        adk_goods_name = #{record.adkGoodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.adkGoodsModel != null">
        adk_goods_model = #{record.adkGoodsModel,jdbcType=VARCHAR},
      </if>
      <if test="record.adkGoodsUnit != null">
        adk_goods_unit = #{record.adkGoodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.erpGoodsSku != null">
        erp_goods_sku = #{record.erpGoodsSku,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.erpGoodsId != null">
        erp_goods_id = #{record.erpGoodsId,jdbcType=INTEGER},
      </if>
      <if test="record.addNo != null">
        add_no = #{record.addNo,jdbcType=VARCHAR},
      </if>
      <if test="record.addName != null">
        add_name = #{record.addName,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateNo != null">
        update_no = #{record.updateNo,jdbcType=VARCHAR},
      </if>
      <if test="record.updateName != null">
        update_name = #{record.updateName,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 20:42:17 CST 2019.
    -->
    update T_ADK_GOODS
    set adk_goods_id = #{record.adkGoodsId,jdbcType=INTEGER},
      adk_goods_code = #{record.adkGoodsCode,jdbcType=VARCHAR},
      adk_goods_name = #{record.adkGoodsName,jdbcType=VARCHAR},
      adk_goods_model = #{record.adkGoodsModel,jdbcType=VARCHAR},
      adk_goods_unit = #{record.adkGoodsUnit,jdbcType=VARCHAR},
      erp_goods_sku = #{record.erpGoodsSku,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      erp_goods_id = #{record.erpGoodsId,jdbcType=INTEGER},
      add_no = #{record.addNo,jdbcType=VARCHAR},
      add_name = #{record.addName,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_no = #{record.updateNo,jdbcType=VARCHAR},
      update_name = #{record.updateName,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.order.model.adk.TAdkGoods">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 20:42:17 CST 2019.
    -->
    update T_ADK_GOODS
    <set>
      <if test="adkGoodsCode != null">
        adk_goods_code = #{adkGoodsCode,jdbcType=VARCHAR},
      </if>
      <if test="adkGoodsName != null">
        adk_goods_name = #{adkGoodsName,jdbcType=VARCHAR},
      </if>
      <if test="adkGoodsModel != null">
        adk_goods_model = #{adkGoodsModel,jdbcType=VARCHAR},
      </if>
      <if test="adkGoodsUnit != null">
        adk_goods_unit = #{adkGoodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="erpGoodsSku != null">
        erp_goods_sku = #{erpGoodsSku,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="erpGoodsId != null">
        erp_goods_id = #{erpGoodsId,jdbcType=INTEGER},
      </if>
      <if test="addNo != null">
        add_no = #{addNo,jdbcType=VARCHAR},
      </if>
      <if test="addName != null">
        add_name = #{addName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateNo != null">
        update_no = #{updateNo,jdbcType=VARCHAR},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where adk_goods_id = #{adkGoodsId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.order.model.adk.TAdkGoods">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 20:42:17 CST 2019.
    -->
    update T_ADK_GOODS
    set adk_goods_code = #{adkGoodsCode,jdbcType=VARCHAR},
      adk_goods_name = #{adkGoodsName,jdbcType=VARCHAR},
      adk_goods_model = #{adkGoodsModel,jdbcType=VARCHAR},
      adk_goods_unit = #{adkGoodsUnit,jdbcType=VARCHAR},
      erp_goods_sku = #{erpGoodsSku,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      erp_goods_id = #{erpGoodsId,jdbcType=INTEGER},
      add_no = #{addNo,jdbcType=VARCHAR},
      add_name = #{addName,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_no = #{updateNo,jdbcType=VARCHAR},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where adk_goods_id = #{adkGoodsId,jdbcType=INTEGER}
  </update>
  <select id="batchVailGoodsSku" parameterType="java.util.List" resultType="com.vedeng.goods.model.Goods">
		SELECT A.SKU,A.GOODS_ID
		FROM T_GOODS A
		WHERE A.SKU IN 
		<foreach collection="skuList" item="sku" open="(" close=")" separator=",">
			#{sku,jdbcType=VARCHAR}
		</foreach>
	</select>
</mapper>