package com.newtask;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.StringUtil;
import com.vedeng.logistics.service.WarehouseStockService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *库存服务定时任务
 * <AUTHOR>
 * @date $
 */
@Component
@JobHandler(value="StockInfoTask")
public class StockServiceTask extends AbstractJobHandler {
    private Logger logger = LoggerFactory.getLogger(StockServiceTask.class);
    @Autowired
    private WarehouseStockService warehouseStockService;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        updateStockInfo(param);
        return SUCCESS;
    }

    public ResultInfo updateStockInfo(String param){
        XxlJobLogger.log("StockServiceTask.updateStockInfo | begin ...............");
        logger.info("StockServiceTask.updateStockInfo | begin ...............");
        ResultInfo resultInfo = new ResultInfo();
        try {
            int goodsId = 0;
            int count = 0;
            int optype = 0;
            String re = "";
            if(StringUtil.isNotBlank(param)){
                String[] split = param.split(",");
                count  = Integer.valueOf(split[0]);
                goodsId  = Integer.valueOf(split[1]);
                if(split.length == 3){
                    optype = Integer.valueOf(split[2]);
                }
            }else if(StringUtil.isBlank(param)){
                re = warehouseStockService.insertNewStock(count, goodsId);
            }
            if (optype == 2) {
                re = warehouseStockService.insertNewStock(count, goodsId);
            } else if(optype == 1){
                re = warehouseStockService.oldinsertNewStock(count, goodsId);
            }
            XxlJobLogger.log("StockServiceTask.updateStockInfo | result:{}",re);
            logger.info("StockServiceTask.updateStockInfo | result:{}",re);
            resultInfo.setMessage(re);
            resultInfo.setCode(0);
            return resultInfo;
        }catch (Exception e){
            XxlJobLogger.log("StockServiceTask.updateStockInfo error:{}",e);
            logger.error("StockServiceTask.updateStockInfo error:",e);
        }
        return resultInfo;
    }
}
