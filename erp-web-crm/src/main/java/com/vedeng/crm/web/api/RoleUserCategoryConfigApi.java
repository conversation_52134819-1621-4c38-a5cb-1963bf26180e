package com.vedeng.crm.web.api;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.annotation.MenuDesc;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.system.dto.RoleUserCategoryConfigDto;
import com.vedeng.erp.system.service.RoleUserCategoryConfigApiService;
import com.vedeng.erp.system.vo.RoleUserCategoryConfigQueryVO;
import com.vedeng.erp.system.vo.RoleUserCategoryConfigVO;
import com.vedeng.erp.system.vo.BusinessUserVO;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 人员与商品分类配置
 * @menu 人员与商品分类配置
 */
@ExceptionController
@RestController
@RequestMapping("/crm/role/profile")
@Slf4j
public class RoleUserCategoryConfigApi {


    @Autowired
    RoleUserCategoryConfigApiService roleUserCategoryConfigApiService;

    /**
     * 分页查询人员与商品分类配置
     */
    @MenuDesc(menuValue = "C06", menuDesc = "商品分类人员配置")
    @RequestMapping("/role-user-category-config/page")
    public R<PageInfo<RoleUserCategoryConfigVO>> page(@RequestBody PageParam<RoleUserCategoryConfigQueryVO> pageParam) {
        return R.success(roleUserCategoryConfigApiService.pageRoleUserCategoryConfig(pageParam));
    }


    /**
     * 获取所有业务人员信息
     */
    @MenuDesc(menuValue = "C06", menuDesc = "商品分类人员配置")
    @RequestMapping("/role-user-category-config/all-business-users")
    public R<List<BusinessUserVO>> getAllBusinessUsers(@RequestParam(value = "userName", required = false) String userName) {
        return R.success(roleUserCategoryConfigApiService.getAllBusinessUsers(userName));
    }


    /**
     * 新增人员与商品分类配置
     */
    @MenuDesc(menuValue = "C0601", menuDesc = "新建")
    @RequestMapping("/role-user-category-config/add")
    public R<Boolean> add(@RequestBody RoleUserCategoryConfigDto dto) {
        return R.success(roleUserCategoryConfigApiService.saveRoleUserCategoryConfig(dto));
    }

    /**
     * 修改人员与商品分类配置
     */
    @MenuDesc(menuValue = "C0602", menuDesc = "编辑")
    @RequestMapping("/role-user-category-config/update")
    public R<Boolean> update(@RequestBody RoleUserCategoryConfigDto dto) {
        return R.success(roleUserCategoryConfigApiService.updateRoleUserCategoryConfig(dto));
    }

    /**
     * 批量删除人员与商品分类配置
     */
    @MenuDesc(menuValue = "C0603", menuDesc = "删除")
    @RequestMapping("/role-user-category-config/batch-delete")
    public R<Boolean> batchRemove(@RequestBody List<Long> ids) {
        return R.success(roleUserCategoryConfigApiService.batchDeleteRoleUserCategoryConfig(ids));
    }

} 