<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>京东订单</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="jd_saleorder" datasource="erp-datasourcetarget" fixednumber="" fixednumberright="" append_column_url="" append_row_url="" empty_show="" firstcol="numbers">
<div id="appendHead"></div>
<div class="layui-fluid">
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">
        </ul>
    </div>
    <form class="layui-form" id="search">
        <div class=" layui-inline "><label class="layui-form-label">京东订单号</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="JD_SALEORDER_NO" placeholder style alias="vjs" jdbctype data datatype oper validate_rules validate_messages>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">贝登订单号</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="SALEORDER_NO" placeholder style alias="vjs" jdbctype data datatype oper validate_rules validate_messages>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">制单状态</label>
            <div class="layui-input-inline"><select class=" layui-input list-search-item " type="search" name="ORDER_GENERATE_STATUS" placeholder style="" alias="vjs" jdbctype="" data='[{"V":"待制单","K":"1"},{"V":"支付成功","K":"2"},{"V":"制单失败","K":"3"},{"V":"上传失败","K":"4"},{"V":"待支付（制单成功）","K":"5"}]'  datatype="JSON" oper="" validate_rules="" validate_messages=""></select>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">审核状态</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="xm-select" name="VERIFY_STATUS" placeholder="" style="" alias="" jdbctype="" data="[{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;待审核&quot;,&quot;K&quot;:&quot;3&quot;}]" datatype="JSON" oper="IN"></object>
            </div>
        </div>

        <div class=" layui-inline "><label class="layui-form-label">收款状态</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="xm-select" name="PAYMENT_STATUS" placeholder="" style="" alias="" jdbctype="" data="[{&quot;V&quot;:&quot;未收款&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分收款&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部收款&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" oper="IN"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">发货状态</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="xm-select" name="DELIVERY_STATUS" placeholder="" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;未发货&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分发货&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部发货&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" oper="IN"></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">收货状态</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="xm-select" name="ARRIVAL_STATUS" placeholder="" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;未收货&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分收货&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部收货&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" oper="IN"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">开票状态</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="xm-select" name="INVOICE_STATUS" placeholder="" style="" alias="" jdbctype="" data="[{&quot;V&quot;:&quot;未开票&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分开票&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部开票&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" oper="IN"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">导入时间</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="daterange" name="ADD_TIME" placeholder style="" alias="vjs" jdbctype="" data="" datatype="" oper="BETWEEN" validate_rules="" validate_messages=""></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">错误原因</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="REASON" placeholder style  jdbctype="BODY" data datatype oper validate_rules validate_messages>
            </div>
        </div>
    </form>
    <hr class="layui-border-blue">
    <div class="btn-group   bd-highlight" id="tableButton">
        <button type="table" class="layui-btn" url="/orderstream/saleorder/initJdBatchContractFile.do" opentype="SELF" area="900px,450px"  windowname="批量上传合同确认单">+
            批量上传合同确认单
        </button>
        <button type="table" style="display: none;" class="layui-btn btn-define-hidden" url="/orderstream/jdsaleorder/upLoadJdFile.do" opentype="SELF" area="900px,450px"  windowname="批量制单">批量制单
        </button>


    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">
            <th type="rowbutton" id="rowbutton"></th>
            <th item_name="JD_SALEORDER_NO" name="JD_SALEORDER_NO" body="td-text" order="1" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="">京东订单号</th>
            <th item_name="SALEORDER_NO" name="SALEORDER_NO" body="td-link" order="" url="/orderstream/saleorder/detail.do?saleOrderId=${VD_SALEORDER_ID}&scene=0" opentype="PARENT" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">贝登订单号</th>
            <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-link" order="" url="/trader/customer/baseinfo.do?traderId=${TRADER_ID}" opentype="PARENT" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">客户主体名称</th>
            <th item_name="ORDER_GENERATE_STATUS" name="ORDER_GENERATE_STATUS" body="td-select" order="" url="" opentype="" windowname="" datatype="JSON" data='[{"V":"待制单","K":"1"},{"V":"支付成功","K":"2"},{"V":"制单失败","K":"3"},{"V":"上传失败","K":"4"},{"V":"待支付（制单成功）","K":"5"}]' style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="">制单状态</th>
            <th item_name="PAYMENT_STATUS" name="PAYMENT_STATUS" body="td-select" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;未收款&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分收款&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部收款&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" head="th">收款状态</th>
            <th item_name="PURCHASE_STATUS" name="PURCHASE_STATUS" body="td-select" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;未采购&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分采购&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部采购&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" head="th">采购状态</th>
            <th item_name="DELIVERY_STATUS" name="DELIVERY_STATUS" body="td-select" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;未发货&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分发货&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部发货&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" head="th">发货状态</th>
            <th item_name="ARRIVAL_STATUS" name="ARRIVAL_STATUS" body="td-select" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;未收货&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分收货&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部收货&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" head="th">收货状态</th>
            <th item_name="VERIFY_STATUS" name="VERIFY_STATUS" body="td-select" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;待审核&quot;,&quot;K&quot;:&quot;3&quot;}]" style="" head="th">审核状态</th>
            <th item_name="INVOICE_STATUS" name="INVOICE_STATUS" body="td-select" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;未开票&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分开票&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部开票&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" head="th">开票状态</th>
            <th item_name="TOTAL_AMOUNT" name="TOTAL_AMOUNT" body="td-text" url="" opentype="MODEL" windowname=""  data="" style="" head="th">订单金额</th>

            <th item_name="ADD_TIME" name="ADD_TIME" body="td-text" order="1" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="DATETIME" head="th" edit_flag="" edit_express="" edit_plugin="">导入时间</th>
            <th item_name="MOD_TIME" name="MOD_TIME" body="td-text" order="1" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="DATETIME" head="th" edit_flag="" edit_express="" edit_plugin="">生成时间</th>
            <th item_name="ERROR_REASON" name="ERROR_REASON" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="">错误原因</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td> express:</td>
            <td colspan="100"><pre id="express" class="layui-code" orderby=" order by ADD_TIME desc " groupby="">StringBuilder sql=new StringBuilder();
 sql.append("
   SELECT
        vjs.JD_SALEORDER_NO,
        vjs.VD_SALEORDER_ID,
        vjs.SALEORDER_NO,
        vjs.TRADER_ID,
        trader.TRADER_NAME,
        vjs.ORDER_GENERATE_STATUS,
        vjs.ADD_TIME, vjs.MOD_TIME,
        vjs.ERROR_REASON,a.DELIVERY_STATUS,a.PAYMENT_STATUS,b.VERIFY_STATUS
        ,a.INVOICE_STATUS,a.ARRIVAL_STATUS,a.PURCHASE_STATUS,a.TOTAL_AMOUNT
    FROM
        V_JD_SALEORDER vjs left join T_SALEORDER a ON a.SALEORDER_ID=vjs.VD_SALEORDER_ID
        LEFT JOIN T_TRADER trader ON  vjs.TRADER_ID = trader.TRADER_ID
    LEFT JOIN T_SALEORDER_DATA b ON a.SALEORDER_ID = b.SALEORDER_ID
    where 1=1  ");
 if(isNotBlank("REASON")){
	sql.append(" and vjs.ERROR_REASON  LIKE '%" +$("REASON") +"%'");
 }
 return search(sql);</pre></td>
        </tr>
        <tr>
            <td> count:</td>
            <td colspan="100"><pre id="count" class="layui-code"></pre></td>
        </tr>
        </tbody>
    </table>
</div>
<div id="appendFoot">
    <style>
        .btn-define-hidden{
            display: none;
        }
    </style>
    <script>
        $('button[item_name="批量制单"]').hide();
        $(function(){
            $.ajax({
                url: '/system/user/hasPermission.do',
                data: {'uri':'/orderstream/jdsaleorder/upLoadJdFile.do'},
                type: 'post',
                dataType : "json",
                success: function (res) {
                    if (res.code == 0 && res.data == true) {
                        // 有权限，根据条件隐藏
                        $("button[item_name='批量制单']").show();
                    } else {
                        //console.log('批量制单无权限，不展示按钮');
                    }
                }
            });
        })
    </script>
</div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>

</body>
</html>