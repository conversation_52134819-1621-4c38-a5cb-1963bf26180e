$(function () {

    $.each($("input[name='isGiftCheck']"), function (i, n) {
        var isBhOrder = JSON.parse($("input[name='isBhOrder']").val());
        var goodsId = $(this).attr("altGiftCheck");
        var buyorderGoodsId = $(this).attr("altGiftBuyorderGoodsId");
        var checkGift = $("input[altGiftCheck='" + goodsId + "']").is(":checked") == true;
        if ($("input[altGiftCheck='" + goodsId + "']").attr("altIsGift") == 1) {
            $("input[altTotal='" + goodsId + "']").val(Number(0.00).toFixed(2));
            $("input[alt='" + goodsId + "']").val(buyorderGoodsId + '|' + Number(0.00).toFixed(2));
            $("span[alt='" + buyorderGoodsId + "']").html(Number(0.00).toFixed(2));
            $("input[altTotal='" + goodsId + "']").attr("readonly", "readonly");
            reCalPayInfo()
            reCalPayInfoAct()
        }
        if (checkGift && !isBhOrder){
            $("input[altGift='" + buyorderGoodsId + "']").val(buyorderGoodsId + '|' + '1');
            $("input[altTotal='" + goodsId + "']").val(Number(0.00).toFixed(2));
            $("input[alt='" + goodsId + "']").val(buyorderGoodsId + '|' + Number(0.00).toFixed(2));
            $("span[alt='" + buyorderGoodsId + "']").html(Number(0.00).toFixed(2));
            $("input[altTotal='" + goodsId + "']").attr("readonly","readonly");
            $("input[alt3='" + goodsId + "']").removeAttr("readonly","readonly");
            // 当选为赠品的时候，价格为0，需要重新计算总金额、付款计划等信息
            reCalPayInfo()
            reCalPayInfoAct()
        }if (!checkGift && !isBhOrder){
            $("input[altGift='" + buyorderGoodsId + "']").val(buyorderGoodsId + '|' + '0');
            $("input[altTotal='" + goodsId + "']").removeAttr("readonly","readonly");
            $("input[alt3='" + goodsId + "']").attr("readonly","readonly");
        }
    });

    var uri = $("input[name='uri']").val();
    var buyorderId = $("input[name='buyorderId']").val();
    var url = uri + "?&buyorderId=" + buyorderId + '&viewType=3';

    if ($(window.frameElement).attr('src').indexOf("viewType=3") < 0) {
        $(window.frameElement).attr('data-url', url);
    }
    reCalPayInfo()
    reCalPayInfoAct()
    $("#sub").click(function () {
        checkLogin();

        expensePriceFlag = true
        expenseNumFlag = true
        $.each($("input[vbuy='buyprice']"),function (i,n){
            if ($(this).parent().parent().attr("class") != "appendhide") {
                if (Number($(this).val()) == 0.00 || Number($(this).val()) == ''){
                    expensePriceFlag = false
                }
            }
        });

        $.each($("input[vbuy='buynum']"),function (i,n){
            if ($(this).parent().parent().attr("class") != "appendhide") {
                if (Number($(this).val()) == 0 || Number($(this).val()) == ''){
                    expenseNumFlag = false
                }
            }
        });

        if (!expenseNumFlag) {
            layer.alert("采购数量不可为0！");
            return false;
        }

        if (!expensePriceFlag) {
            layer.alert("单价不可为0！");
            return false;
        }



        if ($("#traderId").val() == 0) {
            $("#searchTraderName").parent("div").siblings("div").removeClass("none").html("未关联供应商");
            $("#searchTraderName").addClass("errorbor");
            return false;
        } else {
            $("#searchTraderName").parent("div").siblings("div").addClass("none");
            $("#searchTraderName").removeClass("errorbor");
        }
        if ($("#traderContactId").val() == '') {
            $("#traderContactId").parent("div").siblings("div").removeClass("none");
            $("#traderContactId").addClass("errorbor");
            return false;
        } else {
            $("#traderContactId").parent("div").siblings("div").addClass("none");
            $("#traderContactId").removeClass("errorbor");
        }
        if ($("#traderAddressId").val() == '') {
            $("#traderAddressId").parent("div").siblings("div").removeClass("none");
            $("#traderAddressId").addClass("errorbor");
            return false;
        } else {
            $("#traderAddressId").parent("div").siblings("div").addClass("none");
            $("#traderAddressId").removeClass("errorbor");
        }
        var numReg = /^([1]?\d{1,10})$/;
        var priceReg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
        var flag1 = true;
        var flag2 = true;
        var flag3 = true;
        var flag6 = true;
        $.each($("input[name='xprice']"), function (i, n) {
            var goodsId = $(this).attr("altTotal");
            var price = $(this).val();
            if (price == '' || price == undefined) {
                $("input[alt='" + goodsId + "']").addClass("errorbor");
                flag1 = false;
                return false;
            }
            if (price.length > 0 && !priceReg.test(price)) {
                $("input[alt='" + goodsId + "']").addClass("errorbor");
                flag2 = false;
                return false;
            }
            if (Number(price) > *********) {
                $("input[alt='" + goodsId + "']").addClass("errorbor");
                flag3 = false;
                return false;
            }
            var oneAllAmount = $("span[altGood = '" + goodsId + "']").html();
            if (Number(oneAllAmount) > *********) {
                $("input[alt='" + goodsId + "']").addClass("errorbor");
                flag6 = false;
                return false;
            }
        });
        if (!flag1) {
            layer.alert("单价不允许为空");
            return false;
        }
        if (!flag2) {
            layer.alert("单价输入错误！仅允许使用数字，最多精确到小数点后两位");
            return false;
        }
        if (!flag3) {
            layer.alert("单价不允许超过三亿");
            return false;
        }
        if (!flag6) {
            layer.alert("单个商品总价不允许超过三亿");
            return false;
        }



        var flagIsgift1 = true;
        var flagIsgift2 = true;
        var flagIsgift3 = true;
        var isGift =$("#isGiftOrder").val();
        var isBhOrder = JSON.parse($("#isBhOrder").val())
        var allGiftCheck=[];
        var isAdd = JSON.parse($("#isAdd").val());
        $.each($("input[name='xreferPrice']"), function (i, n) {
            var price = $(this).val();
            var goodsId = $(this).attr("alt3");
            var isGiftCheck = $("input[altGiftCheck='" + goodsId + "']").is(":checked") == true
            allGiftCheck.push(isGiftCheck)
            if (price === '' && isGift == 1) {
                $("input[alt3='" + goodsId + "']").addClass("errorbor");
                flagIsgift1 = false;
                return false;
            }
            if (price == 0.00 && isGift == 1 && isBhOrder){
                $("input[alt3='" + goodsId + "']").addClass("errorbor");
                flagIsgift2 = false;
                return false;
            }
            if (price == 0 && isGift == 1 && isBhOrder) {
                $("input[alt3='" + goodsId + "']").addClass("errorbor");
                flagIsgift2 = false;
                return false;
            }
            if (price == 0.00 && !isBhOrder && isGiftCheck){
                $("input[alt3='" + goodsId + "']").addClass("errorbor");
                flagIsgift2 = false;
                return false;
            }
            var a = /(\d{11})\d*/;
            var b =/(\.\d{3})\d*/;
            if (!(price != '' && (priceReg.test(price) && !a.test(price) && !b.test(price)))){
                $("input[alt3='" + goodsId + "']").addClass("errorbor");
                flagIsgift3 = false;
                return false;
            }

            if (!priceReg.test(price)) {
                $("input[alt3='" + goodsId + "']").addClass("errorbor");
                flagIsgift3 = false;
                return false;
            }

        });
        if(isAdd && allGiftCheck.includes(true) && allGiftCheck.includes(false)){
            layer.alert("需把所有赠品都改为非赠品，才允许將赠品订单改为非赠品订单")
            return false;
        }
        if(!isAdd && isGift == 1 && allGiftCheck.includes(true) && allGiftCheck.includes(false)){
            layer.alert("需把所有赠品都改为非赠品，才允许將赠品订单改为非赠品订单")
            return false;
        }
        if(!flagIsgift1){
            layer.alert("请填写赠品参考价");
            return false;
        }
        if(!flagIsgift2){
            layer.alert("赠品参考价不可为0");
            return false;
        }
        if(!flagIsgift3){
            layer.alert("赠品参考价输入错误！仅允许使用数字，最多精确到小数点后两位");
            return false;
        }

        /*【采购预计发货日】和【采购预计收货日】校验*/
        var sendGoodsTimeFlag = true;
        var receiveGoodsTimeFlag = true;
        var dateCompareFlag = true;
        // 当前系统日期(时间戳),精确到 日
        var nowDateStr = new Date().getFullYear() + '-' + (new Date().getMonth() + 1) + '-' + new Date().getDate();
        var nowDate = (new Date(nowDateStr)).valueOf();

        var lateThanNow = true;

        $.each($("input[name='xSendGoodsTime']"), function (i, n) {
            var sendTimeStr = $(this).val();
            if (sendTimeStr == '' || sendTimeStr == undefined) {
                $(this).addClass("errorbor");
                sendGoodsTimeFlag = false;
                return false;
            }

            var sendTimeStamp = (new Date(sendTimeStr)).valueOf();
            if (sendTimeStr != '' && sendTimeStamp < nowDate) {
                $(this).addClass("errorbor");
                lateThanNow = false;
                return false;
            }

            var receviceDateStr = $(this).parent().next().children().val();
            if (receviceDateStr != '' && receviceDateStr < sendTimeStr) {
                $(this).parent().next().children().addClass("errorbor");
                dateCompareFlag = false;
                return false;
            }
        });
        $.each($("input[name='xReceiveGoodsTime']"), function (i, n) {
            var sendTimeStr = $(this).val();
            if (sendTimeStr == '' || sendTimeStr == undefined) {
                $(this).addClass("errorbor");
                receiveGoodsTimeFlag = false;
                return false;
            }
        });
        if (!sendGoodsTimeFlag) {
            layer.alert("请填写采购预计发货日");
            return false;
        }
        if (!lateThanNow) {
            layer.alert("采购预计发货日必须是当前时间之后的日期");
            return false;
        }
        if (!receiveGoodsTimeFlag) {
            layer.alert("请填写采购预计到货日");
            return false;
        }
        if (!dateCompareFlag) {
            layer.alert("采购预计到货日应该大于或等于采购预计发货日");
            return false;
        }


        var flag4 = true;
        var flag5 = true;
        var sum = Number(0);
        $.each($("input[name='saleorderGoodsNum']"), function (i, n) {
            var goodsId = $(this).attr("alt1");
            var num = $(this).val();
            if (num == '' || num == undefined || num == 0) {
                $("input[alt1='" + goodsId + "']").addClass("errorbor");
                flag4 = false;
                return false;
            }
            if (!numReg.test(num)) {
                $("input[alt1='" + goodsId + "']").addClass("errorbor");
                flag5 = false;
                return false;
            }
            sum += Number(num);
        });
        if (!flag4) {
            layer.alert("数量不允许为空");
            return false;
        }
        if (!flag5) {
            layer.alert("数量必须为大于等于1的正整数");
            return false;
        }
        if (sum == Number(0)) {
            layer.alert("订单中应至少有一个商品数量大于0");
            return false;
        }
        var flag6 = true;
        $.each($("textarea[name='insideCommentsDispaly']"), function (i, n) {
            var val = $(this).val();
            if (val != '' && val.length > 512) {
                flag6 = false;
                return false;
            }
        })
        if (!flag6) {
            layer.alert("采购备注不允许超过512个字符");
            return false;
        }

        let expenseCommentsFlag = true;
        $.each($("input[name*='.buyorderExpenseItemDetailDto.insideComments']"), function (i, n) {
            var val = $(this).val();
            if (val != '' && val.length > 100) {
                expenseCommentsFlag = false;
                return false;
            }
        });
        if (!expenseCommentsFlag) {
            layer.alert("虚拟商品采购备注不允许超过100个字符");
            return false;
        }


        //货期货期（天） ： 必填；
        var blank = true;
        $.each($("input[name='deliveryCycleDispaly']"), function (i, n) {
            var val = $(this).val();
            if (val != '' || val == undefined) {
                blank = false;
                return false;
            }
        })
        if (blank) {
            layer.alert("请填写货期");
            return false;
        }

        //安调信息： 最多输入20个字符；
        var flag622 = true;
        $.each($("textarea[name='installationDispaly']"), function (i, n) {
            var val = $(this).val();
            if (val != '' && val.length > 20) {
                flag622 = false;
                return false;
            }
        });
        if (!flag622) {
            layer.alert("安调信息不允许超过20个字符");
            return false;
        }

        //“内部备注”最多输入60个字符
        var flag633 = true;
        $.each($("textarea[name='goodsCommentsDispaly']"), function (i, n) {
            var val = $(this).val();
            if (val != '' && val.length > 60) {
                flag633 = false;
                return false;
            }
        });
        if (!flag633) {
            layer.alert("内部备注不允许超过60个字符");
            return false;
        }

        var prepaidAmount = $("#prepaidAmount").val();
        if (prepaidAmount.length > 14) {
            warnErrorTips("prepaidAmount", "prepaidAmountError", "预付金额输入错误！长度应该在1-12个数字之间");//文本框ID和提示用语
            return false;
        }
        if (prepaidAmount != "") {
            if (!priceReg.test(prepaidAmount)) {
                warnErrorTips("prepaidAmount", "prepaidAmountError", "预付金额输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
                return false;
            }
        } else {
            warnErrorTips("prepaidAmount", "prepaidAmountError", "预付金额必须大于0");//文本框ID和提示用语
            return false;
        }
        var accountPeriodAmount = $("#accountPeriodAmount").val();
        var periodBalance = $("#periodBalance").val();
        if ($("#paymentType").val() != 419 && accountPeriodAmount != '' && !priceReg.test(accountPeriodAmount)) {
            warnErrorTips("accountPeriodAmount", "accountPeriodAmountError", "账期支付输入错误！仅允许使用数字，最多精确到小数点后两位");
            return false;
        } else if ($("#paymentType").val() != 419 && accountPeriodAmount != '' && Number(accountPeriodAmount) > Number(periodBalance)) {
            warnErrorTips("accountPeriodAmount", "accountPeriodAmountError", "账期余额不足");
            $("#accountPeriodAmount").val("0");
            return false;
        }
        if (accountPeriodAmount != "" && accountPeriodAmount != undefined && accountPeriodAmount != 0) {
            $("#haveAccountPeriod").val(1);
        } else {
            $("#haveAccountPeriod").val(0);
        }
        var retainageAmount = $("#retainageAmount").val();
        if ($("#paymentType").val() == 424) {
            if (retainageAmount.length > 14) {
                warnErrorTips("retainageAmount", "retainageAmountError", "尾款输入错误！长度应该在1-12个数字之间");//文本框ID和提示用语
                return false;
            }
            if (retainageAmount != "") {
                if (!priceReg.test(retainageAmount)) {
                    warnErrorTips("retainageAmount", "retainageAmountError", "尾款输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
                    return false;
                }
                if (Number(retainageAmount) <= 0) {
                    warnErrorTips("retainageAmount", "retainageAmountError", "尾款必须大于0");//文本框ID和提示用语
                    return false;
                }
            } else {
                warnErrorTips("retainageAmount", "retainageAmountError", "尾款必须大于0");//文本框ID和提示用语
                return false;
            }
            var retainageAmountMonth = $("#retainageAmountMonth").val();
            if (retainageAmountMonth.length > 0) {
                var re = /^[0-9]+$/;
                if (retainageAmountMonth == "0" || !re.test(retainageAmountMonth)) {
                    warnErrorTips("retainageAmountMonth", "retainageAmountError", "数量必须为大于0的正整数");//文本框ID和提示用语
                    return false;
                } else if (Number(retainageAmountMonth) > 24) {
                    warnErrorTips("retainageAmountMonth", "retainageAmountError", "尾款期限不允许超过24个月");//文本框ID和提示用语
                    return false;
                }
            }
            if (prepaidAmount != "" && accountPeriodAmount != "" && retainageAmount != "") {
                var goodsTotleMoney = $("#zMoney").html();
                if (Number($("#rebateTotal").html()) + Number(prepaidAmount) + Number(accountPeriodAmount) + Number(retainageAmount) != Number(goodsTotleMoney)) {
                    warnErrorTips("retainageAmount", "pay", "支付金额总额与总金额不符，请验证");//文本框ID和提示用语
                    return false;
                }
            }
        }
        if ($("#paymentComments").val() != '' && $("#paymentComments").val().length > 256) {
            warnErrorTips("paymentComments", "paymentCommentsError", "付款备注不允许超过256个字符");
            return false;
        }
        if ($("#invoiceComments").val() != '' && $("#invoiceComments").val().length > 256) {
            warnErrorTips("invoiceComments", "invoiceCommentsError", "收票备注不允许超过256个字符");
            return false;
        }
        if ($("#logisticsComments").val() != '' && $("#logisticsComments").val().length > 256) {
            warnErrorTips("logisticsComments", "logisticsCommentsError", "物流备注不允许超过256个字符");
            return false;
        }
        if ($("#additionalClause").val() != '' && $("#additionalClause").val().length > 256) {
            warnErrorTips("additionalClause", "additionalClauseError", "补充条款不允许超过256个字符");
            return false;
        }
        if ($("#comments").val() != '' && $("#comments").val().length > 256) {
            warnErrorTips("comments", "commentsError", "内部备注不允许超过256个字符");
            return false;
        }

        var actflag3 = true;
        var actflag4 = true;
        $.each($("input[name='xactualPurchasePrice']"), function (i, n) {
            var price = $(this).val();
            var goodsId = $(this).attr("alt4");

            if (price) {
                // 使用正则表达式校验inputPrice是否为有效数字
                var isNumber = /^[0-9]+(\.[0-9]{1,2})?$/.test(price);

                // 如果inputPrice不是有效数字，则返回报错
                if (!isNumber) {
                    $("input[alt4='" + goodsId + "']").addClass("errorbor");
                    actflag3 = false;
                    return false;
                }

                // 如果inputPrice是数字，再判断是否超过*********
                if (Number(price) > *********) {
                    $("input[alt4='" + goodsId + "']").addClass("errorbor");
                    actflag4 = false;
                    return false;
                }
            }
        });
        if (!actflag3) {
            layer.alert("实际采购价输入错误！仅允许使用数字，最多精确到小数点后两位");
            return false;
        }
        if (!actflag4) {
            layer.alert("实际采购价不允许超过三亿");
            return false;
        }

        var bankAcceptance = parseInt($("input[name='bankAcceptance']").val(), 10) || 0;
        var paymentTypeValue = $("#paymentType").val();
        var eSum = parseInt(document.getElementById("eSum")?.innerText || '0');
        var eMoney = parseInt(document.getElementById("eMoney")?.innerText || '0');
        // 实际供应商信息校验
        if ($("#traderId").val() == 613042) {

            if ($("#traderIdAct").val() == 0) {
                $("#searchTraderNameAct").parent("div").siblings("div").removeClass("none").html("未关联供应商");
                $("#searchTraderNameAct").addClass("errorbor");
                return false;
            } else {
                $("#searchTraderNameAct").parent("div").siblings("div").addClass("none");
                $("#searchTraderNameAct").removeClass("errorbor");
            }
            if ($("#traderContactIdAct").val() == '') {
                $("#traderContactIdAct").parent("div").siblings("div").removeClass("none");
                $("#traderContactIdAct").addClass("errorbor");
                return false;
            } else {
                $("#traderContactIdAct").parent("div").siblings("div").addClass("none");
                $("#traderContactIdAct").removeClass("errorbor");
            }

            var actflag1 = true;
            var actflag2 = true;
            var actflag3 = true;
            var actflag4 = true;
            $.each($("input[name='xactualPurchasePrice']"), function (i, n) {
                var price = $(this).val();
                var goodsId = $(this).attr("alt4");
                if (price === '') {
                    $("input[alt4='" + goodsId + "']").addClass("errorbor");
                    actflag1 = false;
                    return false;
                }
                if (price == 0.00){
                    $("input[alt4='" + goodsId + "']").addClass("errorbor");
                    actflag2 = false;
                    return false;
                }
                if (price == 0) {
                    $("input[alt4='" + goodsId + "']").addClass("errorbor");
                    actflag2 = false;
                    return false;
                }
                if (Number(price) > *********) {
                    $("input[alt4='" + goodsId + "']").addClass("errorbor");
                    actflag4 = false;
                    return false;
                }
                var a = /(\d{11})\d*/;
                var b =/(\.\d{3})\d*/;
                if (!(price != '' && (priceReg.test(price) && !a.test(price) && !b.test(price)))){
                    $("input[alt4='" + goodsId + "']").addClass("errorbor");
                    actflag3 = false;
                    return false;
                }

                if (!priceReg.test(price)) {
                    $("input[alt4='" + goodsId + "']").addClass("errorbor");
                    actflag3 = false;
                    return false;
                }

            });
            if(!actflag1){
                layer.alert("请填写实际采购价");
                return false;
            }
            if(!actflag2){
                layer.alert("实际采购价不可为0");
                return false;
            }
            if(!actflag3){
                layer.alert("实际采购价输入错误！仅允许使用数字，最多精确到小数点后两位");
                return false;
            }
            if (!actflag4) {
                layer.alert("实际采购价不允许超过三亿");
                return false;
            }

            var prepaidAmountAct = $("#prepaidAmountAct").val();
            if (prepaidAmountAct.length > 14) {
                warnErrorTips("prepaidAmountAct", "prepaidAmountErrorAct", "预付金额输入错误！长度应该在1-12个数字之间");//文本框ID和提示用语
                return false;
            }
            if (prepaidAmountAct != "") {
                if (!priceReg.test(prepaidAmountAct)) {
                    warnErrorTips("prepaidAmountAct", "prepaidAmountErrorAct", "预付金额输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
                    return false;
                }
            } else {
                warnErrorTips("prepaidAmountAct", "prepaidAmountErrorAct", "预付金额必须大于0");//文本框ID和提示用语
                return false;
            }
            var accountPeriodAmountAct = $("#accountPeriodAmountAct").val();
            if ($("#paymentTypeAct").val() != 419 && accountPeriodAmountAct != '' && !priceReg.test(accountPeriodAmountAct)) {
                warnErrorTips("accountPeriodAmountAct", "accountPeriodAmountErrorAct", "账期支付输入错误！仅允许使用数字，最多精确到小数点后两位");
                return false;
            }
            var retainageAmountAct = $("#retainageAmountAct").val();
            if ($("#paymentTypeAct").val() == 424) {
                if (retainageAmountAct.length > 14) {
                    warnErrorTips("retainageAmountAct", "retainageAmountErrorAct", "尾款输入错误！长度应该在1-12个数字之间");//文本框ID和提示用语
                    return false;
                }
                if (retainageAmountAct != "") {
                    if (!priceReg.test(retainageAmountAct)) {
                        warnErrorTips("retainageAmountAct", "retainageAmountErrorAct", "尾款输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
                        return false;
                    }
                    if (Number(retainageAmountAct) <= 0) {
                        warnErrorTips("retainageAmountAct", "retainageAmountErrorAct", "尾款必须大于0");//文本框ID和提示用语
                        return false;
                    }
                } else {
                    warnErrorTips("retainageAmountAct", "retainageAmountErrorAct", "尾款必须大于0");//文本框ID和提示用语
                    return false;
                }
                var retainageAmountMonthAct = $("#retainageAmountMonthAct").val();
                if (retainageAmountMonthAct.length > 0) {
                    var re = /^[0-9]+$/;
                    if (retainageAmountMonthAct == "0" || !re.test(retainageAmountMonthAct)) {
                        warnErrorTips("retainageAmountMonthAct", "retainageAmountErrorAct", "数量必须为大于0的正整数");//文本框ID和提示用语
                        return false;
                    } else if (Number(retainageAmountMonth) > 24) {
                        warnErrorTips("retainageAmountMonthAct", "retainageAmountErrorAct", "尾款期限不允许超过24个月");//文本框ID和提示用语
                        return false;
                    }
                }
                if (prepaidAmountAct != "" && accountPeriodAmountAct != "" && retainageAmountAct != "") {
                    var goodsTotleMoney = $("#zMoneyAct").html();
                    if (Number(prepaidAmountAct) + Number(accountPeriodAmountAct) + Number(retainageAmountAct) != Number(goodsTotleMoney)) {
                        warnErrorTips("retainageAmountAct", "retainageAmountErrorAct", "支付金额总额与总金额不符，请验证");//文本框ID和提示用语
                        return false;
                    }
                }
            }

            var paymentTypeAct = $("select[name='paymentTypeAct']").val();
            var fkAct = Number(0);
            if (paymentTypeAct == 419) {
                fkAct = Number(prepaidAmountAct);
                $("#accountPeriodAmountAct").val(0);
                $("#retainageAmountAct").val(0);
            } else if (paymentTypeAct == 420 || paymentTypeAct == 421 || paymentTypeAct == 422 || paymentTypeAct == 423 || paymentTypeAct == 3174 || paymentTypeAct == 3175) {
                fkAct = Number(prepaidAmountAct) + Number(accountPeriodAmountAct);
                $("#retainageAmountAct").val(0);
            } else if (paymentTypeAct == 424) {
                fkAct = Number(prepaidAmountAct) + Number(accountPeriodAmountAct) + Number(retainageAmountAct);
            }
            if (fkAct != Number($("#zMoneyAct").html())) {
                layer.alert("实际付款金额小于或大于订单实际金额请确认！");
                return false;
            }

            if (bankAcceptance === 1 && paymentTypeValue == 419 && (eSum > 0 || eMoney > 0)) {
                layer.alert("银行承兑汇票付款的采购单不可添加虚拟商品，请将虚拟商品单独建立采购费用单");
                return false;
            }
        }

        var paymentType = Number($("select[name='paymentType']").val());
        var prepaid = Number(prepaidAmount);
        var accountPeriod = Number(accountPeriodAmount);
        var retainage = Number(retainageAmount);
        var zMoney = Number($("#zMoney").html());
        var usedRebate = Number(usedTotalRebate());

        var fk = prepaid;

        switch (paymentType) {
            case 419:
                $("#accountPeriodAmount").val(0);
                $("#retainageAmount").val(0);
                break;
            case 420:
            case 421:
            case 422:
            case 423:
            case 3174:
            case 3175:
                fk += accountPeriod;
                $("#retainageAmount").val(0);
                break;
            case 424:
                fk += accountPeriod + retainage;
                break;
        }
        var orderAmount = (zMoney - usedRebate).toFixed(2);
        fk = fk.toFixed(2);
        console.log("traderId", $("#traderId").val())
        console.log("bankAcceptance", bankAcceptance)
        if (fk > orderAmount) {
            layer.confirm("付款金额大于订单金额请确认！", {
                btn: ['确定', '取消'] //按钮
            }, function () {
                if ($("#traderId").val() == 613042 && $("#paymentType").val() == 419 && bankAcceptance === 0) {
                    layer.confirm("供应商为鸿瑞但未勾选银行承兑汇票，是否继续？", {
                        btn: ['继续', '取消'] //按钮
                    }, function () {
                        $("#myform").submit();
                    });
                } else {
                    $("#myform").submit();
                }
            });
        } else if (fk === orderAmount) {
            if ($("#traderId").val() == 613042 && $("#paymentType").val() == 419 && bankAcceptance === 0) {
                layer.confirm("供应商为鸿瑞但未勾选银行承兑汇票，是否继续？", {
                    btn: ['继续', '取消'] //按钮
                }, function () {
                    $("#myform").submit();
                });
            } else {
                $("#myform").submit();
            }
        } else {
            layer.alert("付款金额小于订单金额请确认！");
            return false;
        }

    });

    $("#traderContactId").change(function () {
        if ($("#traderContactId").hasClass("errorbor")) {
            $("#traderContactId").parent("div").siblings("div").addClass("none");
            $("#traderContactId").removeClass("errorbor");
        }
    });

    $("#traderAddressId").change(function () {
        if ($("#traderAddressId").hasClass("errorbor")) {
            $("#traderAddressId").parent("div").siblings("div").addClass("none");
            $("#traderAddressId").removeClass("errorbor");
        }
    })

    $('#needInvoiceCheck').change(function () {
        $('#needInvoice').val(this.checked ? '1' : '0');
    });

});

//已使用返利总额
function usedTotalRebate(){
    var usedTotalRebate = Number(0);
    $.each($("span[name='totalRebate']"), function (i, n) {
        usedTotalRebate += Number($(this).html())
    })
    return usedTotalRebate;
}

function changeSendGoodsTime(obj, buyorderGoodsId) {
    $("#sendGoodsTimeValue" + buyorderGoodsId).val(buyorderGoodsId + '|' + $(obj).val());
}

function changeReceiveGoodsTime(obj, buyorderGoodsId) {
    $("#receiveGoodsTimeValue" + buyorderGoodsId).val(buyorderGoodsId + '|' + $(obj).val());
}


function changPrice(obj, buyorderGoodsId, goodsId) {
    checkLogin();
    var price = $(obj).val();
    var goodsId = $(obj).attr("altTotal");
    var priceReg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
    if (price == '') {
        $("input[alt='" + goodsId + "']").addClass("errorbor");
        layer.alert("单价不允许为空");
        return false;
    }
    if (price != '' && (price.length > 13 || !priceReg.test(price))) {
        $("input[alt='" + goodsId + "']").addClass("errorbor");
        layer.alert("单价输入错误！仅允许使用数字，最多精确到小数点后两位");
        return false;
    }
    if (Number(price) > *********) {
        $("input[alt='" + goodsId + "']").addClass("errorbor");
        layer.alert("单价不允许超过三亿");
        return false;
    }

    $(obj).siblings().val(buyorderGoodsId + "|" + price);

    var num = $("span[altTotal='" + goodsId + "']").html();
    $("span[alt='" + buyorderGoodsId + "']").html((Number(num) * Number(price)).toFixed(2));
    if ((Number(num) * Number(price)) > *********) {
        layer.alert("单个商品总价不允许超过三亿");
        return false;
    }
    //单价是否变化
    var tempPrice = $("#tempPriceV"+goodsId).val();
    if (Number(tempPrice).toFixed(2) != Number(price).toFixed(2)){
        $("#totalRebate"+goodsId).text('');
        $("#rebatePrice"+goodsId).text('');
        $("#afterRebatePrice"+goodsId).text('');
        $("#tempPriceV"+goodsId).val(price)
    }
    reCalPayInfo();
    reCalPayInfoAct()
}

function changComments(obj, buyorderGoodsId) {

    checkLogin();
    var insideComments = $(obj).val();
    if (insideComments != undefined) {
        $(obj).siblings().val(buyorderGoodsId + "|" + insideComments);
    }

}

function changDeliveryCycleComments(obj, buyorderGoodsId) {

    checkLogin();
    var insideComments = $(obj).val();
    //校验格式
    var reg = /^[0-9]*$/;
    var test = reg.test(insideComments);
    if (test) {
        if (insideComments != undefined) {
            $(obj).siblings().val(buyorderGoodsId + "|" + insideComments);
        }
    } else {
        layer.alert("货期只能输入数字");
        $(obj).val("");
    }
}

function changeAuth(obj, buyorderGoodsId) {
    checkLogin();
    var isHaveAuth = $(obj).val();
    $(obj).siblings().val(buyorderGoodsId + "|" + isHaveAuth);
}

//该函数用于输出框，当输入值大于指定的最大值的时候，返回当长度等于Max的值
function getInputNum(inputNum, max) {
    var returnValue = '';
    var byteValLen = 0;
    var numReg = /^([1]?\d{1,10})$/;
    for (var i = 0; i < inputNum.length; i++) {
        if (!numReg.test(inputNum[i])) {
            break;
        } else {
            byteValLen += 1;
        }
        if (byteValLen > max)
            break;
        returnValue += inputNum[i];
    }
    returnValue = returnValue.replace(/^0+(\d)/, '$1');
    return returnValue;
}

//该函数用于检验输入值，在该函数中调用了上个函数
function checkInputNum(id, max) {
    var txt = document.getElementById(id).value;
    $("#" + id).val(getInputNum(txt, max));
}

var expenseNumFlag = true;
function expenseNum(obj) {
    var inputNum = $(obj).val();
    var goodsId = $(obj).attr("alt1");

    var reg = /^[0-9]*$/;
    if (!reg.test(inputNum)) {
        $("input[alt1='" + goodsId + "']").addClass("errorbor");
        layer.alert("采购数量只能输入数字");
        $(obj).val(0);
        expenseNumFlag = false;
        return false;
    }

    if (inputNum === '') {
        $("input[alt1='" + goodsId + "']").addClass("errorbor");
        layer.alert("请填写采购数量");
        expenseNumFlag = false;
        return false;
    }

    if (inputNum == 0) {
        $("input[alt1='" + goodsId + "']").addClass("errorbor");
        layer.alert("采购数量不可为0");
        expenseNumFlag = false;
        return false;
    }
    expenseNumFlag = true;

    // 计算每一行的总价
    var price = $("input[alt2='" + goodsId + "']").val();
    $("span[altVirtual='" + goodsId + "']").html((Number(inputNum) * Number(price)).toFixed(2));

    //计算虚拟商品
    var Sum = Number(0)
    $.each($(".ebuySum"), function (i, n) {
        if ($(this).parent().parent().attr("class") != "appendhide"){
            Sum += parseInt($(this).html()||$(this).val());
        }
    });
    $("#eSum").html(Sum);

    var allEMoney = Number(0)
    $.each($(".oneAllEMoney"),function (i,n){
        if ($(this).parent().parent().attr("class") != "appendhide") {
            allEMoney += Number($(this).html());
        }
    });
    $("#eMoney").html(allEMoney.toFixed(2));
}

function getInputPrice(inputPrice, max) {
    if (inputPrice != undefined) {
        inputPrice = inputPrice.replace(/[^\d.]/g, ''); // 只能输入数字和.
        inputPrice = inputPrice.replace(/^\./g, '');  //第一个字符不能是.
        inputPrice = inputPrice.replace(/\.{2,}/g, '.'); // 不能连续输入.
        inputPrice = inputPrice.replace(/(\.\d+)\./g, '$1'); // .后面不能再输入.
        inputPrice = inputPrice.replace(/^0+(\d)/, '$1'); // 第一位0开头，0后面为数字，则过滤掉，取后面的数字
        inputPrice = inputPrice.replace(/(\d{10})\d*/, '$1'); // 最多保留10位整数
        inputPrice = inputPrice.replace(/(\.\d{2})\d*/, '$1');// 最多保留2位小数
    }
    return inputPrice;
}

function checkInputPrice(id, max) {
    var txt = document.getElementById(id).value;
    $("#" + id).val(getInputPrice(txt, max));
}

var expensePriceFlag = true;
function expensePrice(obj) {
    var inputPrice = $(obj).val();
    var goodsId = $(obj).attr("alt2");

    var priceReg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;


    if (inputPrice === '') {
        $("input[alt2='" + goodsId + "']").addClass("errorbor");
        layer.alert("请填写单价");
        expensePriceFlag = false;
        return false;
    }

    if (inputPrice == 0) {
        $("input[alt2='" + goodsId + "']").addClass("errorbor");
        layer.alert("单价不可为0");
        expensePriceFlag = false;
        return false;
    }

    if (!priceReg.test(inputPrice)) {
        $("input[alt2='" + goodsId + "']").addClass("errorbor");
        layer.alert("单价仅允许输入数字，最多精确到小数点后两位");
        $(obj).val(0.00);
        expensePriceFlag = false;
        return false;
    }

    expensePriceFlag = true;
    // 计算每一行的总价
    var oneAllNum = $("span[altTotal='"+ goodsId + "']").html();
    if (oneAllNum == null){
        oneAllNum = $("input[alt1='" + goodsId + "']").val();
    }
    $("span[altVirtual='" + goodsId + "']").html((Number(oneAllNum) * Number(inputPrice)).toFixed(2));

    var allEMoney = Number(0)
    $.each($(".oneAllEMoney"),function (i,n){
        if ($(this).parent().parent().attr("class") != "appendhide") {
            allEMoney += Number($(this).html());
        }
    });
    $("#eMoney").html(allEMoney.toFixed(2));

}
function expensePrice2(obj,buyorderGoodsId) {
    var isGiftCheck = $("input[altGiftBuyorderGoodsId='" + buyorderGoodsId + "']").is(":checked") == true
    var inputPrice = $(obj).val();
    var goodsId = $(obj).attr("alt3");
    var isGift = $(obj).attr("altIsGift");
    var isBhOrder = JSON.parse($("#isBhOrder").val())
    var priceReg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
    if (inputPrice === '') {
        $("input[alt3='" + goodsId + "']").addClass("errorbor");
        layer.alert("请填写赠品参考价");
        return false;
    }

    if (inputPrice == 0 && isGift == 1 && isBhOrder) {
        $("input[alt3='" + goodsId + "']").addClass("errorbor");
        layer.alert("赠品参考价不可为0");
        return false;
    }
    if (!isBhOrder && isGiftCheck == 1 && inputPrice == 0){
        $("input[alt3='" + goodsId + "']").addClass("errorbor");
        layer.alert("赠品参考价不可为0");
        return false;
    }

    var a = /(\d{11})\d*/;
    var b =/(\.\d{3})\d*/;
    if (!(inputPrice != '' && (priceReg.test(inputPrice) && !a.test(inputPrice) && !b.test(inputPrice)))){
        $("input[alt3='" + goodsId + "']").addClass("errorbor");
        layer.alert("赠品参考价输入错误！仅允许使用数字，最多精确到小数点后两位");
        return false;
    }

    // if (!priceReg.test(inputPrice)) {
    //     $(obj).val(0.00);
    //     return false;
    // }
    $(obj).siblings().val(buyorderGoodsId + "|" + inputPrice);
}

function actualPurchasePrice(obj,buyorderGoodsId,traderId) {
    checkLogin();
    var inputPrice = $(obj).val();
    var goodsId = $(obj).attr("alt4");
    var priceReg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;

    if (inputPrice) {
        // 使用正则表达式校验inputPrice是否为有效数字
        var isNumber = /^[0-9]+(\.[0-9]{1,2})?$/.test(inputPrice);

        // 如果inputPrice不是有效数字，则返回报错
        if (!isNumber) {
            $("input[alt4='" + goodsId + "']").addClass("errorbor");
            layer.alert("实际采购价输入错误！仅允许使用数字，最多精确到小数点后两位");
            return false;
        }

        // 如果inputPrice是数字，再判断是否超过*********
        if (Number(inputPrice) > *********) {
            $("input[alt4='" + goodsId + "']").addClass("errorbor");
            layer.alert("实际单价不允许超过三亿");
            return false;
        }

        var num = $("span[altTotal='" + goodsId + "']").html();
        $("span[altAct='" + buyorderGoodsId + "']").html((Number(num) * Number(inputPrice)).toFixed(2));
        if ((Number(num) * Number(inputPrice)) > *********) {
            layer.alert("单个商品实际总价不允许超过三亿");
            return false;
        }
    }
    if ($("#traderId").val() == 613042) {
        if (inputPrice === '') {
            $("input[alt4='" + goodsId + "']").addClass("errorbor");
            layer.alert("请填写实际采购价");
            return false;
        }
        if (inputPrice == 0) {
            $("input[alt4='" + goodsId + "']").addClass("errorbor");
            layer.alert("实际采购价不可为0");
            return false;
        }
        if (Number(inputPrice) > *********) {
            $("input[alt4='" + goodsId + "']").addClass("errorbor");
            layer.alert("实际单价不允许超过三亿");
            return false;
        }
        var a = /(\d{11})\d*/;
        var b =/(\.\d{3})\d*/;
        if (!(inputPrice != '' && (priceReg.test(inputPrice) && !a.test(inputPrice) && !b.test(inputPrice)))){
            $("input[alt4='" + goodsId + "']").addClass("errorbor");
            layer.alert("实际采购价输入错误！仅允许使用数字，最多精确到小数点后两位");
            return false;
        }
        var num = $("span[altTotal='" + goodsId + "']").html();
        $("span[altAct='" + buyorderGoodsId + "']").html((Number(num) * Number(inputPrice)).toFixed(2));
        if ((Number(num) * Number(inputPrice)) > *********) {
            layer.alert("单个商品实际总价不允许超过三亿");
            return false;
        }
    }
    $("#goodsListActualPurchasePrice" + goodsId).val(inputPrice);
    reCalPayInfoAct()
}

function bankAcceptanceChecked() {
    checkLogin();
    if(typeof($("input[name='bankAcceptanceCheckbox']:checked").val()) == "undefined") {
        $("input[name='bankAcceptance']").val(0);
    } else {
        $("input[name='bankAcceptance']").val(1);
    }
}

function addNum(obj, num, buyorderGoodsId, saleorderGoodsId) {
    checkLogin();
    var srnum = $(obj).val();
    var goodsId = $(obj).attr("alt1");
    var numReg = /^([1]?\d{1,10})$/;
    if (srnum == '') {
        $("input[alt1='" + goodsId + "']").addClass("errorbor");
        layer.alert("数量不允许为空")
        return false;
    }
    if (srnum != '' && !numReg.test(srnum)) {
        $("input[alt1='" + goodsId + "']").addClass("errorbor");
        layer.alert("数量必须为正整数且小于11位数字");
        return false;
    }
    if (Number(srnum) < 1 || Number(srnum) > Number(num)) {
        $("input[alt1='" + goodsId + "']").addClass("errorbor");
        layer.alert("数量必须大于等于1且小于等于" + num);
        $(obj).val(0);
        return false;
    }
    $(obj).siblings("input").val(buyorderGoodsId + "|" + saleorderGoodsId + "|" + srnum);
    var sum = Number(0);
    var goodsId = $(obj).attr("alt1");
    $.each($("input[alt1='" + goodsId + "']"), function (i, n) {
        sum += parseInt($(this).val());
    });
    $("span[altTotal='" + goodsId + "']").html(sum);
    $("span[altTotal='" + goodsId + "']").siblings("input").val(buyorderGoodsId + "|" + sum);
    //计算总件数
    var zSum = Number(0);
    $.each($(".buySum"), function (i, n) {
        zSum += parseInt($(this).html());
    });
    $("#zSum").html(zSum);

    //计算单个总额
    var price = $("input[altTotal='" + goodsId + "']").val();
    if (price != undefined && price != "") {
        $("span[alt='" + buyorderGoodsId + "']").html((Number(sum) * Number(price)).toFixed(2));
    }
    var priceAct = $("input[alt4='" + goodsId + "']").val();
    if (priceAct != undefined && priceAct != "") {
        $("span[altAct='" + buyorderGoodsId + "']").html((Number(sum) * Number(priceAct)).toFixed(2));
    }
    if ((Number(srnum) * Number(price)) > *********) {
        layer.alert("单个商品总价不允许超过三亿");
        return false;
    }
    //数量是否变化
    var tempNum = $("#tempNum"+goodsId).val();
    if (Number(tempNum) != Number(srnum)){
        $("#totalRebate"+goodsId).text('');
        $("#rebatePrice"+goodsId).text('');
        $("#afterRebatePrice"+goodsId).text('');
        $("#tempNum"+goodsId).val(srnum)
    }
    reCalPayInfo();
    reCalPayInfoAct()
}


function searchSupplier() {
    checkLogin();
    var searchTraderName = $("#searchTraderName").val() == undefined ? "" : $("#searchTraderName").val();
    var isGift =$("#isGiftOrder").val();
    if (searchTraderName == '南京ZP' && isGift == 1){
        $("#searchTraderName").parent("div").siblings("div").removeClass("none").html("请选择真实的供应商！");
        $("#searchTraderName").addClass("errorbor");
        $("#searchTraderName").val('');
        return false;
    }
    if ($("#searchTraderName").val() == '') {
        $("#searchTraderName").parent("div").siblings("div").removeClass("none").html("查询条件不允许为空");
        $("#searchTraderName").addClass("errorbor");
        return false;
    } else {
        $("#searchTraderName").parent("div").siblings("div").addClass("none");
        $("#searchTraderName").removeClass("errorbor");
    }

    var searchUrl = page_url + "/order/buyorder/getSupplierByName.do?supplierName=" + encodeURI(searchTraderName) + "&callbackFuntion=recieveTraderInfo&isGift="+isGift;
    $("#popSupplier").attr('layerParams', '{"width":"800px","height":"500px","title":"搜索供应商","link":"' + searchUrl + '"}');
    $("#popSupplier").click();

}

function searchSupplierAct() {
    checkLogin();
    var searchTraderName = $("#searchTraderNameAct").val() == undefined ? "" : $("#searchTraderNameAct").val();
    var isGift =$("#isGiftOrder").val();
    if (searchTraderName == '南京ZP' && isGift == 1){
        $("#searchTraderNameAct").parent("div").siblings("div").removeClass("none").html("请选择真实的供应商！");
        $("#searchTraderNameAct").addClass("errorbor");
        $("#searchTraderNameAct").val('');
        return false;
    }
    if ($("#searchTraderNameAct").val() == '') {
        $("#searchTraderNameAct").parent("div").siblings("div").removeClass("none").html("查询条件不允许为空");
        $("#searchTraderNameAct").addClass("errorbor");
        return false;
    } else {
        $("#searchTraderNameAct").parent("div").siblings("div").addClass("none");
        $("#searchTraderNameAct").removeClass("errorbor");
    }

    var searchUrl = page_url + "/order/buyorder/getSupplierByNameAct.do?supplierName=" + encodeURI(searchTraderName) + "&isGift="+isGift;
    $("#popSupplierAct").attr('layerParams', '{"width":"800px","height":"500px","title":"搜索实际供应商","link":"' + searchUrl + '"}');
    $("#popSupplierAct").click();

}


function payment(obj) {
    checkLogin();
    var pay = $(obj).val();
    var zonMoneyHtml = $("#zMoney").html();
    var zonMoney = isNaN(parseFloat(zonMoneyHtml)) ? 0 : parseFloat(zonMoneyHtml);
    var usedTotalRebateValue = usedTotalRebate();
    var usedTotalRebateMoney = isNaN(parseFloat(usedTotalRebateValue)) ? 0 : parseFloat(usedTotalRebateValue).toFixed(2);
    var prepaidAmount = $("#prepaidAmount");
    var accountPeriodAmount = $("#accountPeriodAmount");
    var retainageAmount = $("#retainageAmount");
    var retainageAmountMonth = $("#retainageAmountMonth");
    var accountPeriodLi = $("#accountPeriodLi");
    var retainageLi = $("#retainageLi");

    prepaidAmount.val('0.00');
    accountPeriodAmount.val('0.00');
    retainageAmount.val('0.00');
    retainageAmountMonth.val('');

    // 控制银行承兑汇票的显示与隐藏
    if ($("#traderId").val() == 613042 && pay == 419) {
        $("#displayBankAcceptance").removeClass("none"); // 显示
    } else {
        $("#displayBankAcceptance").addClass("none"); // 隐藏
    }

    function setPayment(percentage) {
        var money = (zonMoney - usedTotalRebateMoney) * percentage;
        prepaidAmount.val(money.toFixed(2));
        accountPeriodAmount.val((zonMoney - money - usedTotalRebateMoney).toFixed(2));
        prepaidAmount.attr("readonly", true);
        accountPeriodAmount.attr("readonly", true);
        accountPeriodLi.show();
        retainageLi.hide();
    }

    switch (parseInt(pay)) {
        case 419:
            prepaidAmount.val((zonMoney - usedTotalRebateMoney).toFixed(2));
            accountPeriodLi.hide();
            retainageLi.hide();
            break;
        case 420:
            setPayment(0.8);
            break;
        case 421:
            setPayment(0.5);
            break;
        case 422:
            setPayment(0.3);
            break;
        case 423:
            accountPeriodAmount.val((zonMoney - usedTotalRebateMoney).toFixed(2));
            prepaidAmount.val(0);
            prepaidAmount.attr("readonly", true);
            accountPeriodAmount.attr("readonly", false);
            accountPeriodLi.show();
            retainageLi.hide();
            break;
        case 424:
            prepaidAmount.removeAttr("readonly");
            accountPeriodAmount.removeAttr("readonly");
            accountPeriodLi.show();
            retainageLi.show();
            break;
        case 3175:
            setPayment(0.9);
            break;
        case 3174:
            setPayment(0.1);
            break;
    }
}

function paymentAct(obj) {
    checkLogin();
    var pay = $(obj).val();
    var zonMoney = $("#zMoneyAct").html();
    $("#prepaidAmountAct").val('0.00');
    $("#accountPeriodAmountAct").val('0.00');
    $("#retainageAmountAct").val('0.00');
    $("#retainageAmountMonthAct").val('');
    if (pay == 419) {
        $("#prepaidAmountAct").val(Number(zonMoney).toFixed(2));
        $("#accountPeriodLiAct").hide();
        $("#retainageLiAct").hide();
    } else if (pay == 420) {
        var money = (Number(zonMoney) * Number(0.8)).toFixed(2);
        $("#prepaidAmountAct").val(money);
        $("#accountPeriodAmountAct").val((Number(zonMoney) - Number(money)).toFixed(2));
        $("#prepaidAmountAct").attr("readonly", true);
        $("#accountPeriodAmountAct").attr("readonly", true);
        $("#accountPeriodLiAct").show();
        $("#retainageLiAct").hide();
    } else if (pay == 421) {
        var money = (Number(zonMoney) * Number(0.5)).toFixed(2);
        $("#prepaidAmountAct").val(money);
        $("#accountPeriodAmountAct").val((Number(zonMoney) - Number(money)).toFixed(2));
        $("#prepaidAmountAct").attr("readonly", true);
        $("#accountPeriodAmountAct").attr("readonly", true);
        $("#accountPeriodLiAct").show();
        $("#retainageLiAct").hide();
    } else if (pay == 422) {
        var money = (Number(zonMoney) * Number(0.3)).toFixed(2);
        $("#prepaidAmountAct").val(money);
        $("#accountPeriodAmountAct").val((Number(zonMoney) - Number(money)).toFixed(2));
        $("#prepaidAmountAct").attr("readonly", true);
        $("#accountPeriodAmountAct").attr("readonly", true);
        $("#accountPeriodLiAct").show();
        $("#retainageLiAct").hide();
    } else if (pay == 423) {
        $("#prepaidAmountAct").attr("readonly", false);
        $("#accountPeriodAmountAct").attr("readonly", false);
        $("#accountPeriodAmountAct").val(Number(zonMoney));
        $("#prepaidAmountAct").attr("readonly", true);
        $("#prepaidAmountAct").val(0);
        $("#accountPeriodLiAct").show();
        $("#retainageLiAct").hide();
    } else if (pay == 424) {
        $("#prepaidAmountAct").removeAttr("readonly");
        $("#accountPeriodAmountAct").removeAttr("readonly");
        $("#accountPeriodLiAct").show();
        $("#retainageLiAct").show();
        $("#prepaidAmountAct").val('0.00');
        $("#accountPeriodAmountAct").val('0.00');
        $("#retainageAmountAct").val('0.00');
        $("#retainageAmountMonthAct").val('');
    } else if (pay == 3175) {
        var money = (Number(zonMoney) * Number(0.9)).toFixed(2);
        $("#prepaidAmountAct").val(money);
        $("#accountPeriodAmountAct").val((Number(zonMoney) - Number(money)).toFixed(2));
        $("#prepaidAmountAct").attr("readonly", true);
        $("#accountPeriodAmountAct").attr("readonly", true);
        $("#accountPeriodLiAct").show();
        $("#retainageLiAct").hide();
    } else if (pay == 3174) {
        var money = (Number(zonMoney) * Number(0.1)).toFixed(2);
        $("#prepaidAmountAct").val(money);
        $("#accountPeriodAmountAct").val((Number(zonMoney) - Number(money)).toFixed(2));
        $("#prepaidAmountAct").attr("readonly", true);
        $("#accountPeriodAmountAct").attr("readonly", true);
        $("#accountPeriodLiAct").show();
        $("#retainageLiAct").hide();
    }

}

function changeTraderId(obj) {
    checkLogin();
    var traderId = $("#traderId").val();
    $.ajax({
        url: page_url + '/order/buyorder/getContactsAddress.do',
        data: {"traderId": traderId},
        type: "POST",
        dataType: "json",
        async: false,
        success: function (data) {
            if (data.code == 0) {
                $option1 = "<option value='0'>请选择</option>";
                $.each(data.data, function (i, n) {
                    $option1 += "<option value='" + data.data[i]['traderContactId'] + "'>" + data.data[i]['name'] + "</option>";
                });
                $("select[name='traderContactId'] option:gt(0)").remove();
                $("select[name='traderContactId'] option:gt(0)").html($option1);

                $option = "<option value='0'>请选择</option>";
                $.each(data.listData, function (i, n) {
                    $option += "<option value='" + data.listData[i]['traderAddress']['traderAddressId'] + "'>"
                        + data.listData[i]['area'] + data.listData[i]['traderAddress']['address'] + "</option>";
                });
                $("select[name='traderAddressId'] option:gt(0)").remove();
                $("select[name='traderAddressId'] option:gt(0)").html($option);
            }
        },
        error: function (data) {
            if (data.status == 1001) {
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    });
}

function research() {
    checkLogin();
    $("#validRebateCharge").val(null);
    $.each($("span[name='totalRebate']"), function (i, n) {
        $(this).html(null)
    })
    $.each($("span[name='rebatePrice']"), function (i, n) {
        $(this).html(null)
    })
    $.each($("span[name='afterRebatePrice']"), function (i, n) {
        $(this).html(null)
    })
    $("#searchTraderName").val("");
    $("#searchTraderName").show();
    $("#name").addClass("none");
    $("#errorMes").removeClass("none");
    $("#research").addClass("none");
    $("#certificateOverdue").hide();
    $("#showOverdue").hide();
    $("#traderId").val("");
    warnTips("traderNameErr","");
}

function researchAct() {
    checkLogin();
    $("#searchTraderNameAct").val("");
    $("#searchTraderNameAct").show();
    $("#nameAct").addClass("none");
    $("#errorMesAct").removeClass("none");
    $("#researchAct").addClass("none");
    $("#certificateOverdueAct").hide();
    $("#showOverdueAct").hide();
    warnTips("traderNameErr","");
}

function delBuyorderGoods(buyorderGoodsId) {
    checkLogin();
    index = layer.confirm("您是否确认删除？", {
        btn: ['确定', '取消'] //按钮
    }, function () {
        $("table[altTable='" + buyorderGoodsId + "']").remove();
        //计算总件数
        var zSum = Number(0);
        $.each($(".buySum"), function (i, n) {
            zSum += parseInt($(this).html());
        });
        $("#zSum").html(zSum);
        reCalPayInfo()
        reCalPayInfoAct()
        var buyorderGoodsIds = $("input[name = 'delBuyGoodsIds']").val();
        if (buyorderGoodsIds == '') {
            buyorderGoodsIds = buyorderGoodsId + ",";
        } else {
            buyorderGoodsIds += buyorderGoodsId + ",";
        }
        $("input[name = 'delBuyGoodsIds']").val(buyorderGoodsIds);
        layer.close(index);
    }, function () {
    });
}

function checkGeInfo(buyorderId) {
    checkLogin();
    var traderId = $("#traderId").val();
    var traderName = $("#traderName").val();
    var traderContactStr = $("#traderContactId").val();
    var traderAddressStr = $("#traderAddressId").val();
    var traderInfo = {traderId: "", traderName: "", traderContactStr: "", traderAddressStr: "", buyorderId: ""};
    traderInfo.traderId = traderId;
    traderInfo.traderName = traderName;
    traderInfo.traderAddressStr = traderAddressStr;
    traderInfo.traderContactStr = traderContactStr;
    traderInfo.buyorderId = buyorderId;
    var traderInfoJSON = JSON.stringify(traderInfo);
    if (traderId == 0) {
        alert("供应商为空！")
        return false;
    }
    var geBuyorderGoodsIds = "";
    $.each($("input[name='oneSelect']"), function (i, n) {
        if ($(this).prop("checked")) {
            geBuyorderGoodsIds += $(this).siblings("input[name='geBuyorderGoodsId']").val() + ",";
        }
    });
    if (geBuyorderGoodsIds == "") {
        layer.alert("未选择任何信息");
        return false;
    }
    $.ajax({
        type: "POST",
        url: page_url + "/order/georder/checkGeInfo.do",
        data: {"buyorderId": buyorderId, "traderId": traderId, "traderInfoJSON": traderInfoJSON},
        dataType: 'json',
        success: function (data) {
            if (data.code == 0) {
                layer.open({
                    type: 2,
                    shadeClose: false, //点击遮罩关闭
                    area: ["500px", "350px"],
                    title: "GE采集信息",
                    content: ["/order/georder/geInfoCollection.do?geBuyorderGoodsIds=" + geBuyorderGoodsIds, 'no']
                });
            } else {
                layer.alert(data.message)
            }
        },
        error: function (data) {
            layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
        }
    });
}


function delGeInfoCollection(buyorderGoodsId) {
    checkLogin();
    if (confirm("确定要删除吗？") == true) {
        $.ajax({
            type: "POST",
            url: page_url + "/order/georder/delGeInfoCollection.do",
            data: {"buyorderGoodsId": buyorderGoodsId},
            dataType: 'json',
            success: function (data) {
                if (data.code == 0) {
                    window.location.reload();
                } else {
                    layer.alert(data.message)
                    return false;
                }
            },
            error: function (data) {
                if (data.code == 1001) {
                    layer.alert("没有权限");
                    return false;
                }
            }
        });
    } else {
        return false;
    }


}

function addSncode(sku, goodsId, buyorderGoodsId) {
    checkLogin();
    layer.open({
        type: 2,
        shadeClose: false, //点击遮罩关闭
        area: ["800px", "550px"],
        title: "添加序列号",
        content: "/order/georder/addSncode.do?sku=" + sku + "&&goodsId=" + goodsId + "&&buyorderGoodsId=" + buyorderGoodsId,
    });
}

function updateGeInfoCollection(buyorderGoodsId, geContractNo, geSaleContractNo, sku) {
    checkLogin();
    layer.open({
        type: 2,
        shadeClose: false, //点击遮罩关闭
        area: ["500px", "390px"],
        title: "GE采集信息",
        content: ["/order/georder/geInfoCollection.do?geBuyorderGoodsIds=" + buyorderGoodsId + "&&geContractNo=" + geContractNo + "&&geSaleContractNo=" + geSaleContractNo + "&&sku=" + sku, 'no']
    });
}

//选择商品后回调
function ez_callback_chooseSku(ids, lines) {
    var skuInfo = JSON.parse(lines)[0];
    // 查出当前已选择的所有虚拟商品的sku_id

    var exitGoodsIs = [];
    // 这边需要排除掉隐藏的tr（点击每一行的删除按钮，但此时还有没提交）
    $("#virtualGoodsAdd tr").each(function () {
        let isDelete = $(this).find('input:eq(3)').val();
        var goodsId = $(this).find('input:eq(2)').val();
        if (isDelete != undefined && isDelete == 0) {
            exitGoodsIs.push(Number(goodsId));
        }
    });

    if (exitGoodsIs.indexOf(Number(skuInfo.SKU_ID)) > -1) {
        layer.confirm("该虚拟商品已存在", {
            btn: ['确定']
        }, function () {
            layer.closeAll();
            return false;
        });
    } else {
        // buyOrderExpenseGoodsDtoList下标从0开始，列表序号从1开始
        var oldlength = $("#virtualGoods table").length + 1;
        var addlength = $("#virtualGoodsAdd tbody tr").length;
        console.log(addlength)
        var buyOrderExpenseId = $('input[name=buyorderExpenseId]').val();
        if (buyOrderExpenseId == undefined){
            buyOrderExpenseId = ""
        }
        var haveStockManage = skuInfo.HAVE_STOCK_MANAGE === "0" ? '否' : (skuInfo.HAVE_STOCK_MANAGE === "1" ? '是' : '-');
        if (addlength===0){
            var thHtml = "<thead>\n" +
                "                            <th width=\"5%\">序号</th>\n" +
                "                            <th width=\"10%\">订货号</th>\n" +
                "                            <th width=\"22%\">产品名称</th>\n" +
                "                            <th width=\"10%\">费用类别</th>\n" +
                "                            <th width=\"8%\">是否可库存管理</th>\n" +
                "                            <th width=\"10%\">采购数量</th>\n" +
                "                            <th width=\"10%\">单价</th>\n" +
                "                            <th width=\"10%\">总额</th>\n" +
                "                            <th width=\"10%\">采购备注</th>\n" +
                "                            <th width=\"10%\">操作</th>\n" +
                "                    </thead>"
            $('#virtualGoodsAdd').append(thHtml);
        }else (
            addlength = addlength
        )
        var length = oldlength + addlength;
        var trHtml = "<tr>\n" +
            "<input type=\"hidden\" name=\"buyorderExpenseItemDtos[" + (length - 1) + "].buyorderExpenseItemId\" value=\"\"/>\n" +
            "<input type=\"hidden\" name=\"buyorderExpenseItemDtos[" + (length - 1) + "].buyorderExpenseId\" value=\"" + buyOrderExpenseId + "\"/>\n" +
            "<input type=\"hidden\" name=\"buyorderExpenseItemDtos[" + (length - 1) + "].goodsId\" value=\"" + skuInfo.SKU_ID + "\"/>\n" +
            "<input type=\"hidden\" name=\"buyorderExpenseItemDtos[" + (length - 1) + "].isDelete\" value=\"0\"/>\n" +
            // js中会按照顺序取goodsId，因此如果要在前三行添加或删除input 需要修改js

            "<input type=\"hidden\" name=\"buyorderExpenseItemDtos[" + (length - 1) + "].buyorderExpenseItemDetailDto.sku\" value=\"" + skuInfo.SKU_NO + "\"/>\n" +
            "<input type=\"hidden\" name=\"buyorderExpenseItemDtos[" + (length - 1) + "].buyorderExpenseItemDetailDto.goodsName\" value=\"" + skuInfo.SHOW_NAME + "\"/>\n" +
            "<input type=\"hidden\" name=\"buyorderExpenseItemDtos[" + (length - 1) + "].buyorderExpenseItemDetailDto.buyorderExpenseItemDetailId\" value=\"\"/>\n" +
            "<input type=\"hidden\" name=\"buyorderExpenseItemDtos[" + (length - 1) + "].buyorderExpenseItemDetailDto.buyorderExpenseItemId\" value=\"\"/>\n" +
            "<input type=\"hidden\" name=\"buyorderExpenseItemDtos[" + (length - 1) + "].buyorderExpenseItemDetailDto.expenseCategoryId\" value=\"" + Number(skuInfo.COST_CATEGORY_ID) + "\"/>\n" +
            "<input type=\"hidden\" name=\"buyorderExpenseItemDtos[" + (length - 1) + "].buyorderExpenseItemDetailDto.expenseCategoryName\" value=\"" + skuInfo.CATEGORY_NAME + "\"/>\n" +
            "<input type=\"hidden\" name=\"buyorderExpenseItemDtos[" + (length - 1) + "].buyorderExpenseItemDetailDto.haveStockManage\" value=\"" + Number(skuInfo.HAVE_STOCK_MANAGE) + "\"/>\n" +

            "<td>" + length + "</td>\n" +
            "<td>" + skuInfo.SKU_NO + "</td>\n" +
            "<td>" + skuInfo.SHOW_NAME + "</td>\n" +
            "<td>" + skuInfo.CATEGORY_NAME + "</td>\n" +
            "<td>" + haveStockManage + "</td>\n" +
            "<td>\n" +
            "<input name=\"buyorderExpenseItemDtos[" + (length - 1) + "].num\" type=\"text\" class=\"ebuySum warning-color1\" vbuy = \"buynum\" value=\"0\" alt1=\"" + skuInfo.SKU_ID + "\" id=\"num" + (length - 1) + "\"\n" +
            "onkeydown=\"checkInputNum('num" + (length - 1) + "',10);\" onkeyup=\"checkInputNum('num" + (length - 1) + "',10);\" \n" +
            "onblur=\"expenseNum(this)\"> \n" +
            "</td>\n" +
            "<td>\n" +
            "<input name=\"buyorderExpenseItemDtos[" + (length - 1) + "].buyorderExpenseItemDetailDto.price\" vbuy = \"buyprice\" type=\"text\" class=\"wid11 warning-color1\" value=\"0.00\" alt2=\"" + skuInfo.SKU_ID + "\" id=\"price" + (length - 1) + "\"\n" +
            "onkeydown=\"checkInputPrice('price" + (length - 1) + "',10);\" onkeyup=\"checkInputPrice('price" + (length - 1) + "',10);\"\n" +
            "onblur=\"expensePrice(this)\">\n" +
            "</td>\n" +
            "<td><span class=\"oneAllEMoney\" altVirtual=\"" + skuInfo.SKU_ID + "\">0.00</span></td>\n" +
            "<td>\n" +
            "<input name=\"buyorderExpenseItemDtos[" + (length - 1) + "].buyorderExpenseItemDetailDto.insideComments\" type=\"text\" class=\"wid8\">\n" +
            "</td>\n" +
            "<td>\n" +
            "<span class=\"edit-user forbid clcforbid\" onclick=\"delExpenseGoods(" + addlength + ");\">删除</span>\n" +
            "</td>\n" +
            "</tr>";

        $('#virtualGoodsAdd tbody').append(trHtml);
        layer.closeAll();
        expenseNumFlag = false;
        expensePriceFlag = false;
    }
}


function changeBuyOrderIsGift(buyorderGoodsId, goodsId) {
    $("#totalRebate"+goodsId).text('');
    $("#rebatePrice"+goodsId).text('');
    $("#afterRebatePrice"+goodsId).text('');
    var isBhOrder = JSON.parse($("#isBhOrder").val())
    var checkGift = $("input[altGiftCheck='" + goodsId + "']").is(":checked") == true
    if (checkGift && isBhOrder) {
        $("input[altGift='" + buyorderGoodsId + "']").val(buyorderGoodsId + '|' + '1');
        $("input[altTotal='" + goodsId + "']").val(Number(0.00).toFixed(2));
        $("input[alt='" + goodsId + "']").val(buyorderGoodsId + '|' + Number(0.00).toFixed(2));
        $("span[alt='" + buyorderGoodsId + "']").html(Number(0.00).toFixed(2));
        $("input[altTotal='" + goodsId + "']").attr("readonly","readonly");
        // 当选为赠品的时候，价格为0，需要重新计算总金额、付款计划等信息
        reCalPayInfo()
        reCalPayInfoAct()
    } if (!checkGift && isBhOrder) {
        $("input[altGift='" + buyorderGoodsId + "']").val(buyorderGoodsId + '|' + '0');
        $("input[altTotal='" + goodsId + "']").removeAttr("readonly","readonly");
    } if (checkGift && !isBhOrder){
        $("input[altGift='" + buyorderGoodsId + "']").val(buyorderGoodsId + '|' + '1');
        $("input[altTotal='" + goodsId + "']").val(Number(0.00).toFixed(2));
        $("input[alt='" + goodsId + "']").val(buyorderGoodsId + '|' + Number(0.00).toFixed(2));
        $("span[alt='" + buyorderGoodsId + "']").html(Number(0.00).toFixed(2));
        $("input[altTotal='" + goodsId + "']").attr("readonly","readonly");
        $("input[alt3='" + goodsId + "']").removeAttr("readonly","readonly");
        // 当选为赠品的时候，价格为0，需要重新计算总金额、付款计划等信息
        reCalPayInfo()
        reCalPayInfoAct()
    }if (!checkGift && !isBhOrder){
        $("input[altGift='" + buyorderGoodsId + "']").val(buyorderGoodsId + '|' + '0');
        $("input[altTotal='" + goodsId + "']").removeAttr("readonly","readonly");
        $("input[alt3='" + goodsId + "']").val(Number(0.00).toFixed(2));
        $("input[alt3='" + goodsId + "']").attr("readonly","readonly");
    }
}

// 删除虚拟商品
function delExpenseGoods(rowIndex) {
    console.log($("#virtualGoodsAdd tr"))
    $("#virtualGoodsAdd tbody tr").eq(rowIndex).find('input:eq(3)').val(1);
    $("#virtualGoodsAdd tbody tr").eq(rowIndex).hide().addClass("appendhide");
    var Sum = Number(0)
    $.each($(".ebuySum"), function (i, n) {
        if ($(this).parent().parent().attr("class") != "appendhide"){
            Sum += parseInt($(this).html()||$(this).val());
        }
    });
    $("#eSum").html(Sum);

    var allEMoney = Number(0)
    $.each($(".oneAllEMoney"),function (i,n){
        if ($(this).parent().parent().attr("class") != "appendhide") {
            allEMoney += Number($(this).html());
        }
    });
    $("#eMoney").html(allEMoney.toFixed(2));
}


function checkInputENum(id){
    var txt = document.getElementById(id).value;
    $("#" + id).val(getInputENum(txt));
}

function getInputENum(num){
    if (num != undefined) {
        num = num.replace(/[^\d]/g, ''); // 只能输入数字和.
        num = num.replace(/^\./g, '');  //第一个字符不能是.
        num = num.replace(/\.{2,}/g, '.'); // 不能连续输入.
        num = num.replace(/(\.\d+)\./g, '$1'); // .后面不能再输入.
        num = num.replace(/^0+(\d)/, '$1'); // 第一位0开头，0后面为数字，则过滤掉，取后面的数字
        num = num.replace(/(\.\d{0})\d*/, '$1')
    }
    return num;
}

function addENum(obj,num,buyorderExpenseItemId,saleorderGoodsId){
    checkLogin();
    var srnum = $(obj).val();
    var goodsId = $(obj).attr("alt1");
    if (srnum == '') {
        $("input[alt1='" + goodsId + "']").addClass("errorbor");
        layer.alert("请填写采购数量")
        expensePriceFlag = false;
        return false;
    }
    if (Number(srnum) == 0) {
        $("input[alt1='" + goodsId + "']").addClass("errorbor");
        layer.alert("采购数量不可为0");
        expensePriceFlag = false;
        return false;
    }
    if (Number(srnum) < 1 || Number(srnum) > Number(num)) {
        $("input[alt1='" + goodsId + "']").addClass("errorbor");
        layer.alert("数量必须大于等于1且小于等于" + num);
        $(obj).val(0);
        expensePriceFlag = false;
        return false;
    }
    expensePriceFlag = true;

    $(obj).siblings("input").val(buyorderExpenseItemId + "|" + saleorderGoodsId + "|" + srnum);
    var sum = Number(0);
    var goodsId = $(obj).attr("alt1");
    $.each($("input[alt1='" + goodsId + "']"), function (i, n) {
        sum += parseInt($(this).val());
    });
    $("span[altTotal='" + goodsId + "']").html(sum);
    $("span[altTotal='" + goodsId + "']").siblings("input").val(sum);
    //计算虚拟商品
    var eSum = Number(0);
    $.each($(".ebuySum"), function (i, n) {
        if ($(this).parent().parent().attr("class") != "appendhide"){
            eSum += parseInt($(this).html()||$(this).val());
        }
    });
    $("#eSum").html(eSum);
    // 计算每一行的总价
    var oneAllNum = $("span[altTotal='"+ goodsId + "']").html();
    if (oneAllNum == null){
        oneAllNum = $("input[alt1='" + goodsId + "']").val();
    }
    var inputPrice = $("input[alt2='" + goodsId + "']").val();
    $("span[altVirtual='" + goodsId + "']").html((Number(oneAllNum) * Number(inputPrice)).toFixed(2));

    var allEMoney = Number(0)
    $.each($(".oneAllEMoney"),function (i,n){
        if ($(this).parent().parent().attr("class") != "appendhide") {
            allEMoney += Number($(this).html());
        }
    });
    $("#eMoney").html(allEMoney.toFixed(2));
}

function getValidRebateCharge(obj){
    $("#validRebateCharge").val(obj.validRebateCharge)
    layer.closeAll();
}

function submitRebateChargeItem(obj){
    $("#totalRebate"+obj.goodsId).text(obj.rebateTotalAmount)
    $("#rebatePrice"+obj.goodsId).text(obj.rebatePrice)
    $("#afterRebatePrice"+obj.goodsId).text(obj.afterRebatePrice)
    $("#goodsListRebateAmount"+obj.goodsId).val(obj.rebateTotalAmount)
    $("#goodsListRebatePrice"+obj.goodsId).val(obj.rebatePrice)
    $("#goodsListRebateAfterPrice"+obj.goodsId).val(obj.afterRebatePrice)
    $("#goodsListRebateNum"+obj.goodsId).val(obj.num)
    reCalPayInfo();
}
// 重新计算付款信息(预付金额、账期支付)
function reCalPayInfo(){
    //计算总金额
    var allMoney = Number(0);
    $.each($(".oneAllMoney"), function (i, n) {
        allMoney += Number($(this).html());
    });
    $("#zMoney").html(allMoney.toFixed(2));
    //计算返利总额
    $("#rebateTotal").html(Number(usedTotalRebate()).toFixed(2));
    //应付金额=总金额-返利总额
    $("#rebateNeedPay").html(Number(allMoney - usedTotalRebate()).toFixed(2));
    var pay = $("#paymentType").val();
    allMoney = Number($("#zMoney").html()).toFixed(2);
    var usedTotalRebateMoney = Number(usedTotalRebate()).toFixed(2);
    if (pay == 419) {
        $("#prepaidAmount").val(Number(allMoney-usedTotalRebateMoney).toFixed(2));
    } else if (pay == 420) {
        var money = (Number(allMoney-usedTotalRebateMoney) * Number(0.8)).toFixed(2);
        $("#prepaidAmount").val(money);
        $("#accountPeriodAmount").val((Number(allMoney) - Number(money) - Number(usedTotalRebateMoney)).toFixed(2));
    } else if (pay == 421) {
        var money = (Number(allMoney-usedTotalRebateMoney) * Number(0.5)).toFixed(2);
        $("#prepaidAmount").val(money);
        $("#accountPeriodAmount").val((Number(allMoney) - Number(money) - Number(usedTotalRebateMoney)).toFixed(2));
    } else if (pay == 422) {
        var money = (Number(allMoney-usedTotalRebateMoney) * Number(0.3)).toFixed(2);
        $("#prepaidAmount").val(money);
        $("#accountPeriodAmount").val((Number(allMoney) - Number(money) - Number(usedTotalRebateMoney)).toFixed(2));
    } else if (pay == 423) {
        $("#prepaidAmount").val(0);
        $("#accountPeriodAmount").val((Number(allMoney-usedTotalRebateMoney)).toFixed(2));
    }else if (pay == 3174) {
        var money = (Number(allMoney-usedTotalRebateMoney) * Number(0.1)).toFixed(2);
        $("#prepaidAmount").val(money);
        $("#accountPeriodAmount").val((Number(allMoney) - Number(money) - Number(usedTotalRebateMoney)).toFixed(2));
    }else if (pay == 3175) {
        var money = (Number(allMoney-usedTotalRebateMoney) * Number(0.9)).toFixed(2);
        $("#prepaidAmount").val(money);
        $("#accountPeriodAmount").val((Number(allMoney) - Number(money) - Number(usedTotalRebateMoney)).toFixed(2));
    }

}
function reCalPayInfoAct(){
    //计算总金额
    var allMoney = Number(0);
    $.each($(".oneAllMoneyAct"), function (i, n) {
        allMoney += Number($(this).html());
    });
    $("#zMoneyAct").html(allMoney.toFixed(2));
    var pay = $("#paymentTypeAct").val();
    allMoney = Number($("#zMoneyAct").html()).toFixed(2);
    if (pay == 419) {
        $("#prepaidAmountAct").val(Number(allMoney).toFixed(2));
    } else if (pay == 420) {
        var money = (Number(allMoney) * Number(0.8)).toFixed(2);
        $("#prepaidAmountAct").val(money);
        $("#accountPeriodAmountAct").val((Number(allMoney) - Number(money)).toFixed(2));
    } else if (pay == 421) {
        var money = (Number(allMoney) * Number(0.5)).toFixed(2);
        $("#prepaidAmountAct").val(money);
        $("#accountPeriodAmountAct").val((Number(allMoney) - Number(money)).toFixed(2));
    } else if (pay == 422) {
        var money = (Number(allMoney) * Number(0.3)).toFixed(2);
        $("#prepaidAmountAct").val(money);
        $("#accountPeriodAmountAct").val((Number(allMoney) - Number(money)).toFixed(2));
    } else if (pay == 423) {
        $("#prepaidAmountAct").val(0);
        $("#accountPeriodAmountAct").val((Number(allMoney)).toFixed(2));
    }else if (pay == 3174) {
        var money = (Number(allMoney) * Number(0.1)).toFixed(2);
        $("#prepaidAmountAct").val(money);
        $("#accountPeriodAmountAct").val((Number(allMoney) - Number(money)).toFixed(2));
    }else if (pay == 3175) {
        var money = (Number(allMoney) * Number(0.9)).toFixed(2);
        $("#prepaidAmountAct").val(money);
        $("#accountPeriodAmountAct").val((Number(allMoney) - Number(money)).toFixed(2));
    }

}
function rebate(buyorderId,buyorderGoodsId,goodsId){
    debugger
    var traderId = $("#traderId").val();
    if (traderId == 0 && traderId == "") {
        layer.alert("请选择供应商!");
        return false;
    }

    var totalRebate = $("#totalRebate"+goodsId).html();
    var validRebateCharge = $("#validRebateCharge").val();
    var usedRebateCharge = Number(0);
    $.each($("span[name='totalRebate']"), function (i, n) {
        usedRebateCharge += Number($(this).html());
    });
    usedRebateCharge -= Number($("#totalRebate"+goodsId).html());

    let price = $("#priceV"+goodsId).val();
    var num = parseInt($("span[altTotal='"+ goodsId + "']").html())

    var searchUrl = page_url + "/buyOrder/rebatePayment/add.do?buyorderGoodsId=" + buyorderGoodsId + "&buyorderId="+buyorderId + "&traderId="+traderId  +"&validRebateCharge="+validRebateCharge+"&usedRebateCharge="+usedRebateCharge+"&price="+price+"&num="+num+"&goodsId="+goodsId+"&totalRebate="+totalRebate;
    $("#rebateIframe").attr('layerParams', '{"width":"600px","height":"400px","title":"返利填写","link":"' + searchUrl + '"}');
    $("#rebateIframe").click();
}



function updateNeedInvoice() {
    var needInvoiceCheck = document.getElementById("needInvoiceCheck");
    var needInvoice = document.getElementById("needInvoice");
    needInvoice.value = needInvoiceCheck.checked ? "1" : "0";
}