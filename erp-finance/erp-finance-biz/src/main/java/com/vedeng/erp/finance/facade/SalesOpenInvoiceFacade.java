package com.vedeng.erp.finance.facade;

import com.vedeng.erp.finance.common.exception.InvoiceException;
import com.vedeng.erp.finance.domain.dto.SaleInvoiceOpenResponseDto;
import com.vedeng.erp.finance.dto.InvoiceApplyDto;
import com.vedeng.erp.finance.dto.InvoiceDto;

/**
 * 销项票门面层
 */
public interface SalesOpenInvoiceFacade {

    /**
     * 校验开票
     * @param invoiceApplyDto
     */
    void checkOpenInvoice(InvoiceApplyDto invoiceApplyDto);

    /**
     * 调用开票接口
     * @param invoiceApplyDto
     * @return
     */
    SaleInvoiceOpenResponseDto invokeSaleOrderInvoiceApi(InvoiceApplyDto invoiceApplyDto);

    /**
     * 交付发票
     * @param invoiceDto
     */
    void handOverInvoice(InvoiceDto invoiceDto);

    /**
     * 开票失败
     * @param invoiceApplyDto
     * @param invoiceException
     */
    void openBlueInvoiceFail(InvoiceApplyDto invoiceApplyDto, InvoiceException invoiceException);

}
