package com.newtask;

import com.vedeng.billsync.task.service.BankBillDataSyncService;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.DateUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *  @Description  修复23年后医械购前台传入的微信/支付宝 收付款数据交易流水与资金流水没有关联的数据，并推送金蝶
 *  @autohr xyx
 */
@Component
@JobHandler(value = "FixUnassociatedWeChatAndAlipayTask")
public class FixUnassociatedWeChatAndAlipayTask extends AbstractJobHandler {
    private static final Logger LOGGER  = LoggerFactory.getLogger(FixUnassociatedWeChatAndAlipayTask.class);
    @Autowired
    BankBillDataSyncService bankBillDataSyncService;
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        //param根据逗号分隔
        if (StringUtils.isBlank(param)){
            LOGGER.info("FixUnassociatedWeChatAndAlipayTask param is null");
            return FAIL;
        }
        String[] split = param.split(",");
        LOGGER.info("FixUnassociatedWeChatAndAlipayTask start");
        List<String> dates = getDatesBetween("2023-01-01", split[0]);
        if (split[1].equals("1")){
            LOGGER.info("FixUnassociatedWeChatAndAlipayTask start log");
            dates.forEach(x->{
                Date queryDate = new Date(DateUtil.convertLong(x, DateUtil.DATE_FORMAT));
                ResultInfo syncResult = bankBillDataSyncService.syncBillDate2bankBillHistory(queryDate,4, "log");
            });
            dates.forEach(x->{
                Date queryDate = new Date(DateUtil.convertLong(x, DateUtil.DATE_FORMAT));
                ResultInfo syncResult = bankBillDataSyncService.syncBillDate2bankBillHistory(queryDate,5, "log");
            });
        }else {
            LOGGER.info("FixUnassociatedWeChatAndAlipayTask start history");
            dates.forEach(x->{
                Date queryDate = new Date(DateUtil.convertLong(x, DateUtil.DATE_FORMAT));
                ResultInfo syncResult = bankBillDataSyncService.syncBillDate2bankBillHistory(queryDate,4, "history");
            });
            dates.forEach(x->{
                Date queryDate = new Date(DateUtil.convertLong(x, DateUtil.DATE_FORMAT));
                ResultInfo syncResult = bankBillDataSyncService.syncBillDate2bankBillHistory(queryDate,5, "history");
            });
        }

        LOGGER.info("FixUnassociatedWeChatAndAlipayTask end");
        return SUCCESS;
    }

    private List<String> getDatesBetween(String start, String end){
        List<String> dates = new ArrayList<>();
        LocalDate startDate = LocalDate.parse(start);
        LocalDate endDate = LocalDate.parse(end);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        while (!startDate.isAfter(endDate)) {
            dates.add(startDate.format(formatter));
            startDate = startDate.plusDays(1);
        }
        return dates;
    }

}
