<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="确认审核" scope="application" />
<%@ include file="../common/common.jsp"%>
<script type="text/javascript">

    function complementTask() {
        checkLogin();
        var comment = $("#comment").val()
        var pass = $("#pass").val()

        if(pass =="false" && comment == ""){
            warnTips("comment","请填写备注");
            return false;
        }
        if(comment.length > 1024){
            warnTips("comment","备注内容不允许超过256个字符");
            return false;
        }



        //审核通过
        if(pass == "true"){
            var lessThanMinGross = ${lessThanMinGross};
            var isOnSale = ${isOnSale};
            //VDERP-17872 需求要求不再提醒该消息
            lessThanMinGross = false;
            isOnSale = false;
            if(lessThanMinGross ||  isOnSale){
                layer.confirm(getMessage(lessThanMinGross,isOnSale), {
                    btn: ['确定', '取消']
                }, function () {
                    submitRequest();
                }, function () {

                });
            }else {
                submitRequest();
            }
        //不通过直接提交
        }else{
            submitRequest();
        }
    }

    //获取提示消息的内容
    function getMessage(lessThanMinGross,isOnSale){
        var mesage = "";

        if(lessThanMinGross && isOnSale){
            mesage = "经销价/终端价/科研终端价/电商价的毛利低于8%，且商品已经在${platStr}上架，确认继续操作吗？";
        }else if(lessThanMinGross){
            mesage = "经销价/终端价/科研终端价/电商价的毛利低于8%，确认继续操作吗？";
        }else if(isOnSale){
            mesage = "商品已经在${platStr}上架，确认继续操作吗？";
        }

        return mesage;
    }

    function submitRequest(){
        $.ajax({
            type: "POST",
            url: "./complementTask.do",
            data: $('#complement').serialize(),
            dataType:'json',
            success: function(data){
                if (data.code == 0) {
                    layer.close(index);
                    window.parent.location.reload();
                } else {
                    layer.alert(data.message);
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    }

</script>
<div class="formpublic">
    <form method="post" action="" id="complement">
        <ul>
            <li>
                <div class="infor_name">
                    <c:if test="${pass==false}">
                        <span>*</span>
                    </c:if>
                    <lable for='name'>备注</lable>
                </div>
                <div class="f_left">
                    <input type="text" name="comment" id="comment" class="input-larger" value="" />
                </div>
            </li>
        </ul>
        <div class="add-tijiao tcenter">
            <input type="hidden" value="${taskId}" name="taskId" id="taskId">
            <input type="hidden" value="${skuPriceChangeApplyId}" name="skuPriceChangeApplyId" id="skuPriceChangeApplyId">
            <input type="hidden" value="${pass}" name="pass" id="pass">
            <button type="button" class="bg-light-green" onclick="complementTask()">提交</button>
            <button class="dele" type="button" id="close-layer">取消</button>
        </div>
    </form>
</div>
<%@ include file="../common/footer.jsp"%>