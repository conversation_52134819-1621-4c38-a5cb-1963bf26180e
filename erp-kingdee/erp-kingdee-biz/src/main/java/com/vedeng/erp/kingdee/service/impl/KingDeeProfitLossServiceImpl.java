package com.vedeng.erp.kingdee.service.impl;

import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeeInventoryProfitCommand;
import com.vedeng.erp.kingdee.domain.command.KingDeeProfitLossCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeInventoryProfitEntity;
import com.vedeng.erp.kingdee.domain.entity.KingDeeProfitLossEntity;
import com.vedeng.erp.kingdee.dto.KingDeeInventoryProfitDto;
import com.vedeng.erp.kingdee.dto.KingDeeProfitLossDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeInventoryProfitCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeInventoryProfitConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeProfitLossCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeProfitLossConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeInventoryProfitRepository;
import com.vedeng.erp.kingdee.repository.KingDeeProfitLossRepository;
import com.vedeng.erp.kingdee.service.KingDeeInventoryProfitApiService;
import com.vedeng.erp.kingdee.service.KingDeeProfitLossApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/18 14:09
 **/
@Service
@Slf4j
public class KingDeeProfitLossServiceImpl extends KingDeeBaseServiceImpl<
        KingDeeProfitLossEntity,
        KingDeeProfitLossDto,
        KingDeeProfitLossCommand,
        KingDeeProfitLossRepository,
        KingDeeProfitLossConvertor,
        KingDeeProfitLossCommandConvertor>  implements KingDeeProfitLossApiService {
}
