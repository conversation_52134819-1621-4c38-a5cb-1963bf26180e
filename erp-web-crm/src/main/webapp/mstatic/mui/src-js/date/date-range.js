Vue.component('ui-daterange-picker', {
    template: `
        <div class="vd-ui-daterange-wrapper">
            <div class="vd-ui-daterange-input-wrap">
                <div class="vd-ui-daterange-input" :class="{'active': focusInput == 1}">
                    <input
                        type="text"
                        :readonly="true"
                        :placeholder="startPlaceholder"
                        v-model="startTime"
                        @click="toggleFocus(1)"
                    />
                </div>
                <div class="vd-ui-daterange-gap">-</div>
                <div class="vd-ui-daterange-input" :class="{'active': focusInput == 2}">
                    <input
                        type="text"
                        :readonly="true"
                        :placeholder="endPlaceholder"
                        v-model="endTime"
                        @click="toggleFocus(2)"
                    />
                </div>
            </div>

            <div class="vd-ui-daterange-panel">
                <div class="vd-ui-range-item" v-show="focusInput == 1">
                    <ui-date-picker
                        type="date"
                        v-model="startTime"
                        :max="endTime"
                        @input="handlerChange"
                    ></ui-date-picker>
                </div>
                <div class="vd-ui-range-item" v-show="focusInput == 2">
                    <ui-date-picker
                        type="date"
                        v-model="endTime"
                        :min="startTime"
                        @input="handlerChange"
                    ></ui-date-picker>
                </div>
            </div>
        </div>
    `,
    data() {
        return {
            startTime: '',
            endTime: '',
            focusInput: 1, // 1开始 2结束
        }
    },
    props: {
        value: {
            type: Array,
            default: () => ([])
        },
        startPlaceholder: String,
        endPlaceholder: String,
    },
    computed: {

    },
    watch: {
        "value": {
            handler (newV) {
                this.startTime = newV[0] || '';
                this.endTime = newV[1] || '';

                if (!this.startTime) {
                    this.toggleFocus(1);
                } else if (!this.endTime) {
                    this.toggleFocus(2);
                } else {
                    this.toggleFocus(2);
                }
            },
            deep: true,
            immediate: true
        }
    },
    mounted() {
    },
    methods: {
        toggleFocus (flag) {
            this.focusInput = flag;
        },
        handlerChange() {
            // 选择开始日期后,自动获焦结束时间
            if (this.startTime && this.focusInput == 1) {
                this.toggleFocus(2);
            }

            let value = [this.startTime, this.endTime];
            this.value = value;
            this.$emit('input', value);
            this.$emit('change', value);
        }
    }
})
