package com.vedeng.erp.kingdee.batch.chain.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileAppender;
import cn.hutool.core.io.file.FileWriter;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.chain.CommonSaleBackOutInBindMethod;
import com.vedeng.erp.kingdee.batch.chain.SaleBackOutInBindChain;
import com.vedeng.erp.kingdee.batch.chain.context.SaleBackOutInBindContext;
import com.vedeng.erp.kingdee.batch.dto.BatchRWarehouseGoodsOutJWarehouseGoodsInDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInItemDto;
import com.vedeng.erp.kingdee.batch.repository.BatchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInItemDtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/01/15 15:26
 * @description 第三步销售退货出入库单绑定逻辑：SN、批次号无需对上，绑定有SN、有批次号的出库单商品，无SN无批次号根据商品类型判断
 */
@Service
@Slf4j
@Order(3)
public class ThirdSaleBackOutInBindChain extends CommonSaleBackOutInBindMethod implements SaleBackOutInBindChain {
    @Autowired
    private BatchWarehouseGoodsOutInDtoMapper batchWarehouseGoodsOutInDtoMapper;

    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;

    @Autowired
    private BatchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper batchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper;

    @Autowired
    private FourSaleBackOutInBindChain fourSaleBackOutInBindChain;

    // 第二次SN未匹配List
    private List<BatchWarehouseGoodsOutInItemDto> snList2;
    // 第二次批次号未匹配List
    private List<BatchWarehouseGoodsOutInItemDto> batchList2;
    // 第二次SN和批次号未匹配List
    private List<BatchWarehouseGoodsOutInItemDto> snAndBatchList2;
    // 第三次SN和批次号未匹配List
    private List<BatchWarehouseGoodsOutInItemDto> snAndBatchList3;

    // 匹配后剩余未匹配SN的出库商品，第二次匹配时需清空
    private List<BatchWarehouseGoodsOutInItemDto> remainSnOutGoods;
    // 匹配后剩余未匹配批次号的出库商品，第二次匹配时需清空
    private List<BatchWarehouseGoodsOutInItemDto> remainBatchOutGoods;
    // 匹配后剩余未匹配SN和批次号的出库商品
    private List<BatchWarehouseGoodsOutInItemDto> remainSnBatchOutGoods;

    // 上一次遍历的入库单单号
    private String outInNo;

    // 匹配SN后剩余未匹配的入库商品数量
    private BigDecimal remainSnInNum = BigDecimal.ZERO;
    // 匹配批次号后剩余未匹配的入库商品数量
    private BigDecimal remainBatchInNum = BigDecimal.ZERO;
    // 匹配SN和批次号后剩余未匹配的入库商品数量
    private BigDecimal remainSnBatchInNum = BigDecimal.ZERO;

    private Long validTime;



    @Override
    public void handler(SaleBackOutInBindContext context) {
        setBase(context);


        // 第二次匹配依然没有匹配上SN的入库单商品
        if (snList2.size() > 0) {
            remainSnOutGoods.clear();
            outInNo = null;

            in:
            for (BatchWarehouseGoodsOutInItemDto inGoods : snList2) {
                List<BatchWarehouseGoodsOutInItemDto> noSnOutItems = batchWarehouseGoodsOutInItemDtoMapper.findAllOutByIn(inGoods, true, null, validTime);
                if (CollUtil.isEmpty(noSnOutItems)) {
                    log.info("第三次绑定失败，入库单商品未有对应的出库商品，入库单商品详情：{}", JSON.toJSONString(inGoods));
                    snAndBatchList3.add(inGoods);
                    continue;
                }
                // 匹配SN后剩余未匹配的入库商品数量，不为0时需要继续遍历其他出库单商品
                remainSnInNum = BigDecimal.ZERO;

                // 该入库商品的SKU
                String inSku = batchWarehouseGoodsOutInItemDtoMapper.getSkuByIn(inGoods.getOutInNo(), inGoods.getRelatedId());
                // 对应的入库单
                BatchWarehouseGoodsOutInDto oneInOrder = getOutInOrder(inGoods);

                if (StringUtils.isNotEmpty(outInNo) && outInNo.equals(inGoods.getOutInNo())) {
                    matchFirst(inGoods, remainSnOutGoods);
                    for (BatchWarehouseGoodsOutInItemDto snOutGoods : remainSnOutGoods) {
                        // 该出库商品的SKU
                        String outSku = batchWarehouseGoodsOutInItemDtoMapper.getSkuByOut(snOutGoods.getOutInNo(), snOutGoods.getRelatedId());
                        // 第三次匹配寻找有SN的多余出库单商品，无需SN匹配上
                        if (StringUtils.isNotEmpty(outSku) && outSku.equals(inSku)) {
                            // 对应出库单
                            BatchWarehouseGoodsOutInDto oneOutOrder = getOutInOrder(snOutGoods);

                            BatchRWarehouseGoodsOutJWarehouseGoodsInDto bindOutIn = buildBaseDto(oneInOrder.getRelateNo(), inGoods.getRelatedId(), oneOutOrder.getWarehouseGoodsOutInId(), snOutGoods.getWarehouseGoodsOutInDetailId(),
                                    oneInOrder.getWarehouseGoodsOutInId(), inGoods.getWarehouseGoodsOutInDetailId());

                            // 前入库单商品绑定中未绑定的数量为0时，从当前商品扣除数量
                            if (remainSnInNum.compareTo(BigDecimal.ZERO) == 0) {
                                bind(1, inGoods.getNum(), snOutGoods.getNum(), bindOutIn, snOutGoods, false);
                                if (inGoods.getNum().compareTo(snOutGoods.getNum()) <= 0) {
                                    continue in;
                                }
                                // 不为0时，从先前未被绑定完的入库单商品数量中扣除数量
                            } else {
                                bind(1, remainSnInNum, snOutGoods.getNum(), bindOutIn, snOutGoods, true);
                                if (remainSnInNum.compareTo(BigDecimal.ZERO) <= 0) {
                                    continue in;
                                }
                            }
                        }
                    }
                }
                outInNo = inGoods.getOutInNo();

                int index = 0;
                matchFirst(inGoods, noSnOutItems);
                for (BatchWarehouseGoodsOutInItemDto noSnOutItem : noSnOutItems) {
                    index++;
                    // 假设此时是第二个入库商品，考虑的情况：一个出库商品被完全绑定，一个出库商品有剩余的数量可以绑定
                    // 入库单商品数量 - 关系表中已绑定的数量 = 剩余数量
                    // 剩余数量 = 0 表示该商品已被全部绑定，直接continue
                    // 剩余数量 > 0 表示该商品有可以被绑定的数量
                    BigDecimal remainOutNum = batchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper.getUnboundOutInAmountByItemId(noSnOutItem.getWarehouseGoodsOutInDetailId(), true);
                    // remainOutNum的备份，用于对比remainOutNum数量是否变化，未变化时是所有出库单商品都未匹配上的情况
                    BigDecimal copyRemainOutNum = new BigDecimal(String.valueOf(remainOutNum));
                    if (remainOutNum.compareTo(BigDecimal.ZERO) > 0) {

                        // 根据出库单商品找到出库单
                        BatchWarehouseGoodsOutInDto oneOutOrder = batchWarehouseGoodsOutInDtoMapper.queryInfoByNo(noSnOutItem.getOutInNo(), 2);

                        // 该出库商品的SKU
                        String outSku = batchWarehouseGoodsOutInItemDtoMapper.getSkuByOut(noSnOutItem.getOutInNo(), noSnOutItem.getRelatedId());
                        // 第三次匹配寻找有SN的多余出库单商品，无需SN匹配上
                        if (StringUtils.isNotEmpty(outSku) && outSku.equals(inSku)) {
                            // 基础插入数据
                            BatchRWarehouseGoodsOutJWarehouseGoodsInDto bindOutIn = buildBaseDto(oneInOrder.getRelateNo(), inGoods.getRelatedId(), oneOutOrder.getWarehouseGoodsOutInId(), noSnOutItem.getWarehouseGoodsOutInDetailId(),
                                    oneInOrder.getWarehouseGoodsOutInId(), inGoods.getWarehouseGoodsOutInDetailId());

                            // 前入库单商品绑定中未绑定的数量为0时，从当前商品扣除数量
                            if (remainSnInNum .compareTo(BigDecimal.ZERO)== 0) {
                                bind(1, inGoods.getNum(), remainOutNum, bindOutIn, noSnOutItem, false);
                                if (inGoods.getNum().compareTo(remainOutNum) <= 0) {
                                    continue in;
                                }
                                // 不为0时，从先前未被绑定完的入库单商品数量中扣除数量
                            } else {
                                bind(1, remainSnInNum, remainOutNum, bindOutIn, noSnOutItem, true);
                                if (remainSnInNum.compareTo(BigDecimal.ZERO) <= 0) {
                                    continue in;
                                }
                            }
                        }
                    }
                    // 一次都没有匹配上，且可用的出库单数量都为0
                    if (remainOutNum.compareTo(BigDecimal.ZERO) == 0 && remainSnInNum.compareTo(BigDecimal.ZERO) == 0 && index == noSnOutItems.size()) {
                        remainSnInNum = inGoods.getNum();
                        log.info("第三次绑定，符合条件的出库单商品数量为0：{}", JSON.toJSONString(inGoods));
                    }
                    // 一次都没有匹配上正确的SKU的情况
                    else if (copyRemainOutNum.compareTo(remainOutNum) == 0 && remainSnInNum.compareTo(BigDecimal.ZERO) == 0 && index == noSnOutItems.size()) {
                        remainSnInNum = inGoods.getNum();
                        log.info("第三次绑定，未匹配上正确的SKU：{}", JSON.toJSONString(inGoods));
                    }
                }

                // 第三次遍历后有SN的入库单商品依然有未匹配上的情况:异常处理
                if (remainSnInNum.compareTo(BigDecimal.ZERO) > 0) {
                    log.info("第三次绑定失败，入库单根据SN码绑定出库单失败，入库单商品：{}，剩余未匹配数量：{}", JSON.toJSONString(inGoods), remainSnInNum);
                    BatchWarehouseGoodsOutInItemDto snBatchInGoods = new BatchWarehouseGoodsOutInItemDto();
                    BeanUtils.copyProperties(inGoods, snBatchInGoods);
                    snBatchInGoods.setNum(remainSnBatchInNum);
                    snAndBatchList3.add(snBatchInGoods);
                }
            }
        }


        // 第二次匹配依然没有匹配上批次号的入库单商品
        if (batchList2.size() > 0) {
            remainBatchOutGoods.clear();
            outInNo = null;

            in:
            for (BatchWarehouseGoodsOutInItemDto inGoods : batchList2) {
                List<BatchWarehouseGoodsOutInItemDto> noBatchOutItems = batchWarehouseGoodsOutInItemDtoMapper.findAllOutByIn(inGoods, null, true, validTime);
                if (CollUtil.isEmpty(noBatchOutItems)) {
                    log.info("第三次绑定失败，入库单商品未有对应的出库商品，入库单商品详情：{}", JSON.toJSONString(inGoods));
                    snAndBatchList3.add(inGoods);
                    continue;
                }
                // 匹配批次号后剩余未匹配的入库商品数量，不为0时需要继续遍历其他出库单商品
                remainBatchInNum = BigDecimal.ZERO;

                // 该入库商品的SKU
                String inSku = batchWarehouseGoodsOutInItemDtoMapper.getSkuByIn(inGoods.getOutInNo(), inGoods.getRelatedId());
                // 对应的入库单
                BatchWarehouseGoodsOutInDto oneInOrder = getOutInOrder(inGoods);

                if (StringUtils.isNotEmpty(outInNo) && outInNo.equals(inGoods.getOutInNo())) {
                    matchFirst(inGoods, remainBatchOutGoods);
                    for (int i = 0; i < remainBatchOutGoods.size(); i++) {
                        BatchWarehouseGoodsOutInItemDto batchOutGoods = remainBatchOutGoods.get(i);
                        // 该出库商品的SKU
                        String outSku = batchWarehouseGoodsOutInItemDtoMapper.getSkuByOut(batchOutGoods.getOutInNo(), batchOutGoods.getRelatedId());
                        // 第三次匹配寻找有批次号的多余出库单商品，无需批次号匹配上
                        if (StringUtils.isNotEmpty(outSku) && outSku.equals(inSku)) {
                            // 对应出库单
                            BatchWarehouseGoodsOutInDto oneOutOrder = getOutInOrder(batchOutGoods);

                            BatchRWarehouseGoodsOutJWarehouseGoodsInDto bindOutIn = buildBaseDto(oneInOrder.getRelateNo(), inGoods.getRelatedId(), oneOutOrder.getWarehouseGoodsOutInId(), batchOutGoods.getWarehouseGoodsOutInDetailId(),
                                    oneInOrder.getWarehouseGoodsOutInId(), inGoods.getWarehouseGoodsOutInDetailId());

                            // 前入库单商品绑定中未绑定的数量为0时，从当前商品扣除数量
                            if (remainBatchInNum.compareTo(BigDecimal.ZERO) == 0) {
                                bind(2, inGoods.getNum(), batchOutGoods.getNum(), bindOutIn, batchOutGoods, false);
                                if (inGoods.getNum().compareTo(batchOutGoods.getNum())<= 0) {
                                    continue in;
                                }
                                // 不为0时，从先前未被绑定完的入库单商品数量中扣除数量
                            } else {
                                bind(2, remainBatchInNum, batchOutGoods.getNum(), bindOutIn, batchOutGoods, true);
                                if (remainBatchInNum .compareTo(BigDecimal.ZERO)  <= 0) {
                                    continue in;
                                }
                            }
                        }
                    }
                }
                outInNo = inGoods.getOutInNo();

                int index = 0;
                matchFirst(inGoods, noBatchOutItems);
                for (BatchWarehouseGoodsOutInItemDto noBatchOutItem : noBatchOutItems) {
                    index++;
                    // 假设此时是第二个入库商品，考虑的情况：一个出库商品被完全绑定，一个出库商品有剩余的数量可以绑定
                    // 入库单商品数量 - 关系表中已绑定的数量 = 剩余数量
                    // 剩余数量 = 0 表示该商品已被全部绑定，直接continue
                    // 剩余数量 > 0 表示该商品有可以被绑定的数量
                    BigDecimal remainOutNum = batchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper.getUnboundOutInAmountByItemId(noBatchOutItem.getWarehouseGoodsOutInDetailId(), true);
                    // remainOutNum的备份，用于对比remainOutNum数量是否变化，未变化时是所有出库单商品都未匹配上的情况
                    BigDecimal copyRemainOutNum = new BigDecimal(String.valueOf(remainOutNum));
                    if (remainOutNum.compareTo(BigDecimal.ZERO) > 0) {

                        // 根据出库单商品找到出库单
                        BatchWarehouseGoodsOutInDto oneOutOrder = batchWarehouseGoodsOutInDtoMapper.queryInfoByNo(noBatchOutItem.getOutInNo(), 2);

                        // 该出库商品的SKU
                        String outSku = batchWarehouseGoodsOutInItemDtoMapper.getSkuByOut(noBatchOutItem.getOutInNo(), noBatchOutItem.getRelatedId());
                        // 第三次匹配寻找有批次号的多余出库单商品，无需批次号匹配上
                        if (StringUtils.isNotEmpty(outSku) && outSku.equals(inSku)) {
                            // 基础插入数据
                            BatchRWarehouseGoodsOutJWarehouseGoodsInDto bindOutIn = buildBaseDto(oneInOrder.getRelateNo(), inGoods.getRelatedId(), oneOutOrder.getWarehouseGoodsOutInId(), noBatchOutItem.getWarehouseGoodsOutInDetailId(),
                                    oneInOrder.getWarehouseGoodsOutInId(), inGoods.getWarehouseGoodsOutInDetailId());

                            // 前入库单商品绑定中未绑定的数量为0时，从当前商品扣除数量
                            if (remainBatchInNum.compareTo(BigDecimal.ZERO) == 0) {
                                bind(2, inGoods.getNum(), remainOutNum, bindOutIn, noBatchOutItem, false);
                                if (inGoods.getNum() .compareTo(remainOutNum)  <= 0) {
                                    continue in;
                                }
                                // 不为0时，从先前未被绑定完的入库单商品数量中扣除数量
                            } else {
                                bind(2, remainBatchInNum, remainOutNum, bindOutIn, noBatchOutItem, true);
                                if (remainBatchInNum.compareTo(BigDecimal.ZERO) <= 0) {
                                    continue in;
                                }
                            }
                        }
                    }
                    // 一次都没有匹配上，且可用的出库单数量都为0
                    if (remainOutNum.compareTo(BigDecimal.ZERO) == 0 && remainBatchInNum.compareTo(BigDecimal.ZERO) == 0 && index == noBatchOutItems.size()) {
                        remainBatchInNum = inGoods.getNum();
                        log.info("第三次绑定，符合条件的出库单商品数量为0：{}", JSON.toJSONString(inGoods));
                    }
                    // 一次都没有匹配上正确的SKU的情况
                    else if (copyRemainOutNum.compareTo(remainOutNum) == 0 && remainBatchInNum.compareTo(BigDecimal.ZERO) == 0 && index == noBatchOutItems.size()) {
                        remainBatchInNum = inGoods.getNum();
                        log.info("第三次绑定，未匹配上正确的SKU：{}", JSON.toJSONString(inGoods));
                    }
                }

                // 第三次遍历后有批次号的入库单商品依然有未匹配上的情况:异常处理
                if (remainBatchInNum.compareTo(BigDecimal.ZERO) > 0) {
                    log.info("入库单根据批次号绑定出库单失败，入库单商品：{}，剩余未匹配数量：{}", JSON.toJSONString(inGoods), remainBatchInNum);
                    BatchWarehouseGoodsOutInItemDto snBatchInGoods = new BatchWarehouseGoodsOutInItemDto();
                    BeanUtils.copyProperties(inGoods, snBatchInGoods);
                    snBatchInGoods.setNum(remainSnBatchInNum);
                    snAndBatchList3.add(snBatchInGoods);
                }
            }
        }


        // 第二次匹配依然没有匹配上SN和批次号的入库单商品
        if (snAndBatchList2.size() > 0) {
            remainSnOutGoods.clear();
            remainBatchOutGoods.clear();
            remainSnBatchOutGoods.clear();
            outInNo = null;

            in:
            for (BatchWarehouseGoodsOutInItemDto inGoods : snAndBatchList2) {
                // 有SN的出库商品
                List<BatchWarehouseGoodsOutInItemDto> noSnOutItems = batchWarehouseGoodsOutInItemDtoMapper.findAllOutByIn(inGoods, true, false, validTime);
                // 有批次号的出库商品
                List<BatchWarehouseGoodsOutInItemDto> noBatchOutItems = batchWarehouseGoodsOutInItemDtoMapper.findAllOutByIn(inGoods, false, true, validTime);
                if (CollUtil.isEmpty(noSnOutItems) && CollUtil.isEmpty(noBatchOutItems)) {
                    log.info("第三次绑定失败，入库单商品未有对应的出库商品，入库单商品详情：{}", JSON.toJSONString(inGoods));
                    snAndBatchList3.add(inGoods);
                    continue;
                }

                // 匹配SN和批次号后剩余未匹配的入库商品数量，不为0时需要继续遍历其他出库单商品
                remainSnBatchInNum = BigDecimal.ZERO;

                // 该入库商品的SKU
                String inSku = batchWarehouseGoodsOutInItemDtoMapper.getSkuByIn(inGoods.getOutInNo(), inGoods.getRelatedId());
                // 对应的入库单
                BatchWarehouseGoodsOutInDto oneInOrder = getOutInOrder(inGoods);

                Integer spuType = batchWarehouseGoodsOutInItemDtoMapper.getSpuTypeByIn(inGoods);
                if (spuType == null){
                    log.error("第三次绑定失败，无SN和批次号入库单商品，根据商品类型SPU_TYPE匹配，SPU_TYPE为空：{}", JSON.toJSONString(inGoods));
                    continue;
                }

                if (StringUtils.isNotEmpty(outInNo) && outInNo.equals(inGoods.getOutInNo())) {
                    // 试剂或耗材
                    if (spuType == 317 || spuType == 318) {
                        matchFirst(inGoods, remainBatchOutGoods);
                        for (BatchWarehouseGoodsOutInItemDto batchOutGoods : remainBatchOutGoods) {
                            // 该出库商品的SKU
                            String outSku = batchWarehouseGoodsOutInItemDtoMapper.getSkuByOut(batchOutGoods.getOutInNo(), batchOutGoods.getRelatedId());
                            // SKU匹配上
                            if (StringUtils.isNotEmpty(outSku) && outSku.equals(inSku)) {
                                // 对应出库单
                                BatchWarehouseGoodsOutInDto oneOutOrder = getOutInOrder(batchOutGoods);

                                BatchRWarehouseGoodsOutJWarehouseGoodsInDto bindOutIn = buildBaseDto(oneInOrder.getRelateNo(), inGoods.getRelatedId(), oneOutOrder.getWarehouseGoodsOutInId(), batchOutGoods.getWarehouseGoodsOutInDetailId(),
                                        oneInOrder.getWarehouseGoodsOutInId(), inGoods.getWarehouseGoodsOutInDetailId());

                                // 前入库单商品绑定中未绑定的数量为0时，从当前商品扣除数量
                                if (remainSnBatchInNum.compareTo(BigDecimal.ZERO) == 0) {
                                    snBatchBind(2, inGoods.getNum(), batchOutGoods.getNum(), bindOutIn, batchOutGoods, false);
                                    if (inGoods.getNum().compareTo(batchOutGoods.getNum()) <= 0) {
                                        continue in;
                                    }
                                    // 不为0时，从先前未被绑定完的入库单商品数量中扣除数量
                                } else {
                                    snBatchBind(2, remainSnBatchInNum, batchOutGoods.getNum(), bindOutIn, batchOutGoods, true);
                                    if (remainSnBatchInNum.compareTo(BigDecimal.ZERO) <= 0) {
                                        continue in;
                                    }
                                }
                            }
                        }
                        matchFirst(inGoods, remainSnBatchOutGoods);
                        for (BatchWarehouseGoodsOutInItemDto snBatchOutGoods : remainSnBatchOutGoods) {
                            // 该出库商品的SKU
                            String outSku = batchWarehouseGoodsOutInItemDtoMapper.getSkuByOut(snBatchOutGoods.getOutInNo(), snBatchOutGoods.getRelatedId());
                            // SKU匹配上
                            if (StringUtils.isNotEmpty(outSku) && outSku.equals(inSku)) {
                                // 对应出库单
                                BatchWarehouseGoodsOutInDto oneOutOrder = getOutInOrder(snBatchOutGoods);

                                BatchRWarehouseGoodsOutJWarehouseGoodsInDto bindOutIn = buildBaseDto(oneInOrder.getRelateNo(), inGoods.getRelatedId(), oneOutOrder.getWarehouseGoodsOutInId(), snBatchOutGoods.getWarehouseGoodsOutInDetailId(),
                                        oneInOrder.getWarehouseGoodsOutInId(), inGoods.getWarehouseGoodsOutInDetailId());

                                // 前入库单商品绑定中未绑定的数量为0时，从当前商品扣除数量
                                if (remainSnBatchInNum.compareTo(BigDecimal.ZERO) == 0) {
                                    snBatchBind(2, inGoods.getNum(), snBatchOutGoods.getNum(), bindOutIn, snBatchOutGoods, false);
                                    if (inGoods.getNum().compareTo(snBatchOutGoods.getNum()) <= 0) {
                                        continue in;
                                    }
                                    // 不为0时，从先前未被绑定完的入库单商品数量中扣除数量
                                } else {
                                    snBatchBind(2, remainSnBatchInNum, snBatchOutGoods.getNum(), bindOutIn, snBatchOutGoods, true);
                                    if (remainSnBatchInNum.compareTo(BigDecimal.ZERO) <= 0) {
                                        continue in;
                                    }
                                }
                            }
                        }
                    }

                    // 设备、配件或其他
                    else {
                        matchFirst(inGoods, remainSnOutGoods);
                        for (BatchWarehouseGoodsOutInItemDto snOutGoods : remainSnOutGoods) {
                            // 该出库商品的SKU
                            String outSku = batchWarehouseGoodsOutInItemDtoMapper.getSkuByOut(snOutGoods.getOutInNo(), snOutGoods.getRelatedId());
                            // SKU匹配上
                            if (StringUtils.isNotEmpty(outSku) && outSku.equals(inSku)) {
                                // 对应出库单
                                BatchWarehouseGoodsOutInDto oneOutOrder = getOutInOrder(snOutGoods);

                                BatchRWarehouseGoodsOutJWarehouseGoodsInDto bindOutIn = buildBaseDto(oneInOrder.getRelateNo(), inGoods.getRelatedId(), oneOutOrder.getWarehouseGoodsOutInId(), snOutGoods.getWarehouseGoodsOutInDetailId(),
                                        oneInOrder.getWarehouseGoodsOutInId(), inGoods.getWarehouseGoodsOutInDetailId());

                                // 前入库单商品绑定中未绑定的数量为0时，从当前商品扣除数量
                                if (remainSnBatchInNum.compareTo(BigDecimal.ZERO) == 0) {
                                    snBatchBind(1, inGoods.getNum(), snOutGoods.getNum(), bindOutIn, snOutGoods, false);
                                    if (inGoods.getNum().compareTo(snOutGoods.getNum()) <= 0) {
                                        continue in;
                                    }
                                    // 不为0时，从先前未被绑定完的入库单商品数量中扣除数量
                                } else {
                                    snBatchBind(1, remainSnBatchInNum, snOutGoods.getNum(), bindOutIn, snOutGoods, true);
                                    if (remainSnBatchInNum.compareTo(BigDecimal.ZERO) <= 0) {
                                        continue in;
                                    }
                                }
                            }
                        }
                        matchFirst(inGoods, remainSnBatchOutGoods);
                        for (BatchWarehouseGoodsOutInItemDto snBatchOutGoods : remainSnBatchOutGoods) {
                            // 该出库商品的SKU
                            String outSku = batchWarehouseGoodsOutInItemDtoMapper.getSkuByOut(snBatchOutGoods.getOutInNo(), snBatchOutGoods.getRelatedId());
                            // SKU匹配上
                            if (StringUtils.isNotEmpty(outSku) && outSku.equals(inSku)) {
                                // 对应出库单
                                BatchWarehouseGoodsOutInDto oneOutOrder = getOutInOrder(snBatchOutGoods);

                                BatchRWarehouseGoodsOutJWarehouseGoodsInDto bindOutIn = buildBaseDto(oneInOrder.getRelateNo(), inGoods.getRelatedId(), oneOutOrder.getWarehouseGoodsOutInId(), snBatchOutGoods.getWarehouseGoodsOutInDetailId(),
                                        oneInOrder.getWarehouseGoodsOutInId(), inGoods.getWarehouseGoodsOutInDetailId());

                                // 前入库单商品绑定中未绑定的数量为0时，从当前商品扣除数量
                                if (remainSnBatchInNum.compareTo(BigDecimal.ZERO) == 0) {
                                    snBatchBind(1, inGoods.getNum(), snBatchOutGoods.getNum(), bindOutIn, snBatchOutGoods, false);
                                    if (inGoods.getNum().compareTo(snBatchOutGoods.getNum()) <= 0) {
                                        continue in;
                                    }
                                    // 不为0时，从先前未被绑定完的入库单商品数量中扣除数量
                                } else {
                                    snBatchBind(1, remainSnBatchInNum, snBatchOutGoods.getNum(), bindOutIn, snBatchOutGoods, true);
                                    if (remainSnBatchInNum.compareTo(BigDecimal.ZERO) <= 0) {
                                        continue in;
                                    }
                                }
                            }
                        }
                    }
                }

                outInNo = inGoods.getOutInNo();
                // 试剂或耗材
                if (spuType == 317 || spuType == 318) {
                    if(CollUtil.isEmpty(noBatchOutItems)){
                        snAndBatchList3.add(inGoods);
                        log.error("第三次绑定，无SN无批次号入库商品匹配无SN无批次号出库单商品失败，未有对应的出库商品，随后尝试根据商品类型匹配出库单商品：{}", JSON.toJSONString(inGoods));
                        continue;
                    }
                    int index = 0;
                    matchFirst(inGoods, noBatchOutItems);
                    for (BatchWarehouseGoodsOutInItemDto noBatchOutItem : noBatchOutItems) {
                        index++;
                        // 假设此时是第二个入库商品，考虑的情况：一个出库商品被完全绑定，一个出库商品有剩余的数量可以绑定
                        // 入库单商品数量 - 关系表中已绑定的数量 = 剩余数量
                        // 剩余数量 = 0 表示该商品已被全部绑定，直接continue
                        // 剩余数量 > 0 表示该商品有可以被绑定的数量
                        BigDecimal remainOutNum = batchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper.getUnboundOutInAmountByItemId(noBatchOutItem.getWarehouseGoodsOutInDetailId(), true);
                        // remainOutNum的备份，用于对比remainOutNum数量是否变化，未变化时是所有出库单商品都未匹配上的情况
                        BigDecimal copyRemainOutNum = new BigDecimal(String.valueOf(remainOutNum));
                        if (remainOutNum.compareTo(BigDecimal.ZERO) > 0) {

                            // 根据出库单商品找到出库单
                            BatchWarehouseGoodsOutInDto oneOutOrder = batchWarehouseGoodsOutInDtoMapper.queryInfoByNo(noBatchOutItem.getOutInNo(), 2);

                            // 该出库商品的SKU
                            String outSku = batchWarehouseGoodsOutInItemDtoMapper.getSkuByOut(noBatchOutItem.getOutInNo(), noBatchOutItem.getRelatedId());
                            // SKU匹配上
                            if (StringUtils.isNotEmpty(outSku) && outSku.equals(inSku)) {
                                // 基础插入数据
                                BatchRWarehouseGoodsOutJWarehouseGoodsInDto bindOutIn = buildBaseDto(oneInOrder.getRelateNo(), inGoods.getRelatedId(), oneOutOrder.getWarehouseGoodsOutInId(), noBatchOutItem.getWarehouseGoodsOutInDetailId(),
                                        oneInOrder.getWarehouseGoodsOutInId(), inGoods.getWarehouseGoodsOutInDetailId());

                                // 前入库单商品绑定中未绑定的数量为0时，从当前商品扣除数量
                                if (remainSnBatchInNum.compareTo(BigDecimal.ZERO) == 0) {
                                    snBatchBind(2, inGoods.getNum(), remainOutNum, bindOutIn, noBatchOutItem, false);
                                    if (inGoods.getNum().compareTo(remainOutNum) <= 0) {
                                        continue in;
                                    }
                                    // 不为0时，从先前未被绑定完的入库单商品数量中扣除数量
                                } else {
                                    snBatchBind(2, remainSnBatchInNum, remainOutNum, bindOutIn, noBatchOutItem, true);
                                    if (remainSnBatchInNum.compareTo(BigDecimal.ZERO) <= 0) {
                                        continue in;
                                    }
                                }
                            }
                        }
                        // 一次都没有匹配上，且可用的出库单数量都为0
                        if (remainOutNum.compareTo(BigDecimal.ZERO) == 0 && remainSnBatchInNum.compareTo(BigDecimal.ZERO) == 0 && index == noSnOutItems.size()) {
                            remainSnBatchInNum = inGoods.getNum();
                            log.info("第三次绑定，符合条件的出库单商品数量为0：{}", JSON.toJSONString(inGoods));
                        }
                        // 一次都没有匹配上正确的SKU的情况
                        else if (copyRemainOutNum.compareTo(remainOutNum) == 0 && remainSnBatchInNum.compareTo(BigDecimal.ZERO) == 0 && index == noSnOutItems.size()) {
                            remainSnBatchInNum = inGoods.getNum();
                            log.info("第三次绑定，未匹配上正确的SKU：{}", JSON.toJSONString(inGoods));
                        }
                    }
                }

                // 设备、配件或其他
                else {
                    if(CollUtil.isEmpty(noSnOutItems)){
                        snAndBatchList3.add(inGoods);
                        log.error("第三次绑定，无SN无批次号入库商品匹配无SN无批次号出库单商品失败，未有对应的出库商品，随后尝试根据商品类型匹配出库单商品：{}", JSON.toJSONString(inGoods));
                        continue;
                    }
                    int index = 0;
                    matchFirst(inGoods, noSnOutItems);
                    for (BatchWarehouseGoodsOutInItemDto noSnOutItem : noSnOutItems) {
                        index++;
                        // 假设此时是第二个入库商品，考虑的情况：一个出库商品被完全绑定，一个出库商品有剩余的数量可以绑定
                        // 入库单商品数量 - 关系表中已绑定的数量 = 剩余数量
                        // 剩余数量 = 0 表示该商品已被全部绑定，直接continue
                        // 剩余数量 > 0 表示该商品有可以被绑定的数量
                        BigDecimal remainOutNum = batchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper.getUnboundOutInAmountByItemId(noSnOutItem.getWarehouseGoodsOutInDetailId(), true);
                        // remainOutNum的备份，用于对比remainOutNum数量是否变化，未变化时是所有出库单商品都未匹配上的情况
                        BigDecimal copyRemainOutNum = new BigDecimal(String.valueOf(remainOutNum));
                        if (remainOutNum.compareTo(BigDecimal.ZERO) > 0) {

                            // 根据出库单商品找到出库单
                            BatchWarehouseGoodsOutInDto oneOutOrder = batchWarehouseGoodsOutInDtoMapper.queryInfoByNo(noSnOutItem.getOutInNo(), 2);

                            // 该出库商品的SKU
                            String outSku = batchWarehouseGoodsOutInItemDtoMapper.getSkuByOut(noSnOutItem.getOutInNo(), noSnOutItem.getRelatedId());
                            // SKU匹配上
                            if (StringUtils.isNotEmpty(outSku) && outSku.equals(inSku)) {
                                // 基础插入数据
                                BatchRWarehouseGoodsOutJWarehouseGoodsInDto bindOutIn = buildBaseDto(oneInOrder.getRelateNo(), inGoods.getRelatedId(), oneOutOrder.getWarehouseGoodsOutInId(), noSnOutItem.getWarehouseGoodsOutInDetailId(),
                                        oneInOrder.getWarehouseGoodsOutInId(), inGoods.getWarehouseGoodsOutInDetailId());

                                // 前入库单商品绑定中未绑定的数量为0时，从当前商品扣除数量
                                if (remainSnBatchInNum.compareTo(BigDecimal.ZERO) == 0) {
                                    snBatchBind(1, inGoods.getNum(), remainOutNum, bindOutIn, noSnOutItem, false);
                                    if (inGoods.getNum().compareTo(remainOutNum) <= 0) {
                                        continue in;
                                    }
                                    // 不为0时，从先前未被绑定完的入库单商品数量中扣除数量
                                } else {
                                    snBatchBind(1, remainSnBatchInNum, remainOutNum, bindOutIn, noSnOutItem, true);
                                    if (remainSnBatchInNum.compareTo(BigDecimal.ZERO) <= 0) {
                                        continue in;
                                    }
                                }
                            }
                        }
                        // 一次都没有匹配上，且可用的出库单数量都为0
                        if (remainOutNum.compareTo(BigDecimal.ZERO) == 0 && remainSnBatchInNum.compareTo(BigDecimal.ZERO) == 0 && index == noSnOutItems.size()) {
                            remainSnBatchInNum = inGoods.getNum();
                            log.info("第三次绑定，符合条件的出库单商品数量为0：{}", JSON.toJSONString(inGoods));
                        }
                        // 一次都没有匹配上正确的SKU的情况
                        else if (copyRemainOutNum.compareTo(remainOutNum) == 0 && remainSnBatchInNum.compareTo(BigDecimal.ZERO) == 0 && index == noSnOutItems.size()) {
                            remainSnBatchInNum = inGoods.getNum();
                            log.error("第三次绑定，未匹配上正确的SKU：{}", JSON.toJSONString(inGoods));
                        }
                    }
                }

                // 第三次遍历后无SN无批次号的入库单商品依然有未匹配上的情况:异常处理
                if (remainSnBatchInNum.compareTo(BigDecimal.ZERO)> 0) {
                    BatchWarehouseGoodsOutInItemDto snBatchInGoods = new BatchWarehouseGoodsOutInItemDto();
                    BeanUtils.copyProperties(inGoods, snBatchInGoods);
                    snBatchInGoods.setNum(remainSnBatchInNum);
                    snAndBatchList3.add(snBatchInGoods);
                }
            }
        }
        this.doNext(context);
    }

    @Override
    public void doNext(SaleBackOutInBindContext context) {
        fourSaleBackOutInBindChain.handler(context);
    }

    @Override
    public void setBase(SaleBackOutInBindContext context) {
        snList2 = context.getSnList2();
        batchList2 = context.getBatchList2();
        snAndBatchList2 = context.getSnAndBatchList2();
        snAndBatchList3 = context.getSnAndBatchList3();
        remainSnOutGoods = context.getRemainSnOutGoods();
        remainBatchOutGoods = context.getRemainBatchOutGoods();
        remainSnBatchOutGoods = context.getRemainSnBatchOutGoods();
        outInNo = context.getOutInNo();
        validTime = context.getValidTime();
    }

    @Override
    public void bind(int type, BigDecimal inGoodsNum, BigDecimal outGoodsNum, BatchRWarehouseGoodsOutJWarehouseGoodsInDto
            bindOutIn, BatchWarehouseGoodsOutInItemDto outGoods, boolean isRemain) {
        if (inGoodsNum .compareTo(outGoodsNum)  == 0) {
            // 数量正好能匹配上
            bindOutIn.setNum(inGoodsNum);
            if (type == 1) {
                if (isRemain) {
                    this.remainSnInNum = BigDecimal.ZERO;
                }
                log.info("第三次绑定，有SN入库商品匹配有SN出库商品数量正好，{}", JSON.toJSONString(bindOutIn));
            }
            if (type == 2) {
                if (isRemain) {
                    this.remainBatchInNum = BigDecimal.ZERO;
                }
                log.info("第三次绑定，有批次号入库商品匹配有批次号出库商品数量正好，{}", JSON.toJSONString(bindOutIn));
            }
            // 入库小于出库，出库商品多余的数量保存到list中下次使用
        } else if (inGoodsNum .compareTo(outGoodsNum)  < 0) {
            bindOutIn.setNum(inGoodsNum);
            BatchWarehouseGoodsOutInItemDto remainGoods = new BatchWarehouseGoodsOutInItemDto();
            BeanUtils.copyProperties(outGoods, remainGoods);
            remainGoods.setNum(outGoodsNum .subtract(inGoodsNum) );
            this.remainSnOutGoods.add(remainGoods);
            if (type == 1) {
                if (isRemain) {
                    this.remainSnInNum = BigDecimal.ZERO;
                }
                log.info("第三次绑定，有SN入库商品匹配有SN出库商品，入库小于出库，{}", JSON.toJSONString(bindOutIn));
            }
            if (type == 2) {
                if (isRemain) {
                    this.remainBatchInNum = BigDecimal.ZERO;
                }
                log.info("第三次绑定，有批次号入库商品匹配有批次号出库商品，入库小于出库，{}", JSON.toJSONString(bindOutIn));
            }
            // 入库大于出库，多余的数量保存到list中继续下次遍历
        } else if (inGoodsNum .compareTo(outGoodsNum)  > 0) {
            bindOutIn.setNum(outGoodsNum);
            if (type == 1) {
                this.remainSnInNum = inGoodsNum .subtract(outGoodsNum) ;
                log.info("第三次绑定，有SN入库商品匹配有SN出库商品，入库大于出库，{}", JSON.toJSONString(bindOutIn));
            }
            if (type == 2) {
                this.remainBatchInNum = inGoodsNum .subtract(outGoodsNum) ;
                log.info("第三次绑定，有批次号入库商品匹配有批次号出库商品，入库大于出库，{}", JSON.toJSONString(bindOutIn));
            }
        }
        batchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper.insertSelective(bindOutIn);
    }


    /**
     * 无SN无批次号入库单商品绑定
     *
     * @param type        匹配SN：1；匹配批次号：2
     * @param inGoodsNum  入库单商品可用数量
     * @param outGoodsNum 出库单商品可用数量
     * @param bindOutIn   待插入的DTO
     * @param outGoods    当前的出库单商品
     * @param isRemain    true：入库单商品数量 > 出库单商品数量的情况下，多余的入库单商品数量去匹配其他的出库单商品
     */
    public void snBatchBind(int type, BigDecimal inGoodsNum, BigDecimal outGoodsNum, BatchRWarehouseGoodsOutJWarehouseGoodsInDto
            bindOutIn, BatchWarehouseGoodsOutInItemDto outGoods, boolean isRemain) {
        if (inGoodsNum .compareTo(outGoodsNum)  == 0) {
            // 数量正好能匹配上
            bindOutIn.setNum(inGoodsNum);
            if (isRemain) {
                this.remainSnBatchInNum = BigDecimal.ZERO;
            }
            if (type == 1) {
                log.info("第三次绑定，无SN无批次号入库商品匹配SN出库商品数量正好，{}", JSON.toJSONString(bindOutIn));
            }
            if (type == 2) {
                log.info("第三次绑定，无SN无批次号入库商品匹配批次号出库商品数量正好，{}", JSON.toJSONString(bindOutIn));
            }
            // 入库小于出库，出库商品多余的数量保存到list中下次使用
        } else if (inGoodsNum .compareTo(outGoodsNum)  < 0) {
            bindOutIn.setNum(inGoodsNum);
            BatchWarehouseGoodsOutInItemDto remainGoods = new BatchWarehouseGoodsOutInItemDto();
            BeanUtils.copyProperties(outGoods, remainGoods);
            remainGoods.setNum(outGoodsNum .subtract(inGoodsNum) );

            if (isRemain) {
                this.remainSnBatchInNum =BigDecimal.ZERO;
            }
            if (type == 1) {
                this.remainSnOutGoods.add(remainGoods);
                log.info("第三次绑定，无SN无批次号入库商品匹配SN出库商品，入库小于出库，{}", JSON.toJSONString(bindOutIn));
            }
            if (type == 2) {
                this.remainBatchOutGoods.add(remainGoods);
                log.info("第三次绑定，无SN无批次号入库商品匹配批次号出库商品，入库小于出库，{}", JSON.toJSONString(bindOutIn));
            }
            // 入库大于出库，多余的数量保存到list中继续下次遍历
        } else if (inGoodsNum .compareTo(outGoodsNum)  > 0) {
            bindOutIn.setNum(outGoodsNum);
            this.remainSnBatchInNum = inGoodsNum .subtract(outGoodsNum);
            if (type == 1) {
                log.info("第三次绑定，无SN无批次号入库商品匹配SN出库商品，入库大于出库，{}", JSON.toJSONString(bindOutIn));
            }
            if (type == 2) {
                log.info("第三次绑定，无SN无批次号入库商品匹配批次号出库商品，入库大于出库，{}", JSON.toJSONString(bindOutIn));
            }
        }
        batchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper.insertSelective(bindOutIn);
    }
}
