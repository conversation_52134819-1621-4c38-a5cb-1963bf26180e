$(function () {
    $("[name=submit]").click(function () {
            checkLogin();
            if ($("#applyerDepartmentId").val() == '' || $("#applyerDepartmentId").val() == 0) {
                layer.alert("申请部门不能为空");
                return false;
            }
            if ($("#applyerId").val() == '' || $("#applyerId").val() == null || $("#applyerId").val() == 0) {
                layer.alert("申请人不能为空");
                return false;
            }

            if ($("#remark").val().length > 256) {
                layer.alert("备注超过256字符!");
                return false;
            }
            var trs = $('.J-prod-item');
            if (trs.length == 0) {
                layer.alert("已选出库产品不能为空");
                return false;
            }
            debugger
            var re = /(^[1-9]\d*$)/;
            var submitFlag = true;
            for (var i = 0; i < trs.length; i++) {
                var period = $(".J-item-num").eq(i).val().trim();
                var stock = $(".J-stock-num").eq(i).val().trim();
                if(submitFlag){
                    if (period.length == 0) {
                        submitFlag = false;
                        layer.alert("请输入出库数量!");
                        break;

                    }
                    if (period.length > 0 && !re.test(period)) {
                        submitFlag = false;
                        layer.alert("出库数量只可输入正整数!");
                        break;
                    }
                    if (period.length > 10) {
                        submitFlag = false;
                        layer.alert("出库数量只可输入10位!");
                        break;
                    }
                    if (Number(period) > Number(stock)) {
                        submitFlag = false;
                        layer.alert("盘亏数量不得大于库存数量!");
                        break;
                    }
                }
            }

            if(!submitFlag){
                return false;
            }

            var hasAuthority = true;
            var validataPass = true;
            var message = "";

            $.ajax({
                url:page_url+'/wms/inventoryOut/validateApplyerInfo.do',
                type:"POST",
                dataType : "json",
                async: false,
                success:function(data)
                {
                    debugger;

                    if(data.code == -1){
                        validataPass = false;
                        message = data.message;
                    }
                },
                error:function(data){
                    if(data.status ==1001){
                        hasAuthority = false;
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
                    }
                }
            });

            if(!hasAuthority){
                return false;
            }

            if(!validataPass){
                layer.alert(message);
                return false;
            }
        }
    )});

// 删除
function deleteRow(row){
    $(row).parent().parent().remove();
    resetName();
}

function resetName(){
    $('.J-prod-item').each(function(i){
        $(this).find('input').each(function () {
            var name = $(this).attr('name');
            name = name.replace(/wmsOutputOrderGoods\[\S*\]/g, 'wmsOutputOrderGoods[' + i + ']');
            $(this).attr('name', name);
        })
    });
}
function checkSku(sku,logicalName) {
    var flag = false;
    $('.J-prod-item').each(function () {
        if ($(this).find('.J-sku').val() == sku) {
            if ($(this).find(".J-logicalName").val()==logicalName) {
                flag = true;
            }

        }
    });

    return flag;
}
function setGoodsData(skuNo,showName,brandName,model,unitName,logicalName,stockNum) {
    if (checkSku(skuNo,logicalName)) {
        layer.alert('已选过当前产品当前逻辑仓');
        return;
    }
    layer.closeAll();
    var prodTmpl = template($('.J-prod-tmpl').html());
    var goods = {
        goodsName: showName,
        sku:skuNo,
        brandName: brandName,
        model:model,
        logicalName:logicalName,
        stockNum:stockNum,
        unitName:unitName,
    };
    $('.J-prod-list').append(prodTmpl(goods));
    resetName();
    /*var tbody = $(".J-prod-list");
    var tr = $("<tr class=\"J-prod-item\">\n" +
        "     <td>\n" +
        "     " +skuNo+ "\n" +
        "       <input type=\"hidden\" name=\"skuNo\" value=\""+skuNo+"\">\n" +
        "     </td>\n" +
        "     <td>"+showName+"</td>\n" +
        "     <td>"+brandName+"</td>\n" +
        "     <td>"+model+"</td>\n" +
        "     <td>"+unitName+"</td>\n" +
        "     <td>"+"<input type=\"text\" class=\"input-middle J-item-num\" name=\"inputNum\">"+"</td>\n" +
        "     <td><a href=\"#\" onclick=\"deleteRow(this)\">删除</a></td>\n" +
        "     </tr>");
    tbody.append(tr);*/

}

function selectOrgUser() {
    checkLogin();
    debugger;
    var orgId = $("select[name='applyerDepartmentId']").val();
    if (orgId > 0) {
        $.ajax({
            type: "POST",
            url: page_url + '/wms/scrapOut/getUserNameByOrgId.do',
            data: {'orgId': orgId},
            dataType: 'json',
            success: function (data) {
                if (data.code == 0) {
                    var posit = data.listData;
                    var option = "";
                    $.each(posit, function (i, n) {
                        option += "<option value='" + posit[i]['userId'] + "'>" + posit[i]['username'] + "</option>";
                    });
                    $("select[name='applyerId'] option:gt(0)").remove();
                    $("select[name='applyerId']").append(option);
                } else {
                    layer.msg(data.message);
                }
            },
            error: function (data) {
                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    } else {
        $("select[name='applyerId'] option:gt(0)").remove();
    }
}