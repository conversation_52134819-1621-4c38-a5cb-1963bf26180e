package com.vedeng.erp.trader.service.impl;

import com.vedeng.authorization.model.User;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.EmptyUtils;
import com.vedeng.erp.trader.constant.TraderConstant;
import com.vedeng.erp.trader.domain.dto.SkuSupplyAuth;
import com.vedeng.erp.trader.domain.dto.SkuSupplyAuthDetail;
import com.vedeng.erp.trader.domain.dto.TraderSupplierDto;
import com.vedeng.erp.trader.domain.vo.SkuSupplyAuthVo;
import com.vedeng.erp.trader.mapper.SkuSupplyAuthDetailMapper;
import com.vedeng.erp.trader.mapper.SkuSupplyAuthMapper;
import com.vedeng.erp.trader.mapper.TraderSupplierMapper;
import com.vedeng.erp.trader.service.TraderSupplierBizService;
import com.vedeng.goods.service.BrandApiService;
import com.vedeng.infrastructure.file.domain.Attachment;
import com.vedeng.infrastructure.file.mapper.AttachmentMapper;
import com.vedeng.trader.model.TraderPeriodData;
import com.vedeng.trader.service.TraderDataService;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class TraderSupplierBizServiceImpl implements TraderSupplierBizService {
    @Value("${api_http}")
    protected String api_http;
    @Resource
    private SkuSupplyAuthMapper skuSupplyAuthMapper;

    @Autowired
    private AttachmentMapper attachmentMapper;

    @Autowired
    private BrandApiService brandApiService;

    @Resource
    private SkuSupplyAuthDetailMapper skuSupplyAuthDetailMapper;

    @Autowired
    @Qualifier("newTraderSupplierMapper")
    private TraderSupplierMapper traderSupplierMapper;

    @Autowired
    private TraderDataService traderDataService;

    @Override
    public List<SkuSupplyAuthVo> queryAuthInfoByTraderSupplierId(Integer traderSupplierId) {
        //获取商品授权书信息
        List<SkuSupplyAuthVo> skuSupplyAuthVos = skuSupplyAuthMapper.queryAuthInfoBySupplierId(traderSupplierId);
        skuSupplyAuthVos.stream().forEach(item -> {
            //所有文件
            List<Attachment> allAttachmentList = attachmentMapper.queryListByRelatedIdAndFunction(item.getSkuSupplyAuthId(), TraderConstant.SKU_AUTH_ATTACHMENT_FUNCTION_ID_4101);
            List<Attachment> pictureList = allAttachmentList.stream().filter(
                    file -> TraderConstant.SKU_AUTH_ATTACHMENT_TYPE_ID_460.equals(file.getAttachmentType()) && !EmptyUtils.isEmpty(file.getUri())
            ).collect(Collectors.toList());
            List<Attachment> fileList = allAttachmentList.stream().filter(
                    file -> TraderConstant.SKU_AUTH_ATTACHMENT_TYPE_ID_461.equals(file.getAttachmentType())
            ).collect(Collectors.toList());
            item.setFileAttachment(fileList);
            item.setPictureAttachment(pictureList);
            if(CollectionUtils.isEmpty(fileList) && CollectionUtils.isEmpty(pictureList)){
                item.setAuthStatusShow("审核不通过");
            }else{
                item.setAuthStatusShow("审核通过");
            }

            //按照五个为一组分割list,用于页面展示图片
            List<List<Attachment>> pictureArrayList = partition(pictureList,5);
            List<String> pictureAttachmentJsonStrings = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(pictureArrayList)){
                pictureArrayList.stream().forEach(picture -> {
                    pictureAttachmentJsonStrings.add(convertAttachmentList2JsonStr(picture));
                });
                //如果最后一行满5个则需要新起一行
                if(pictureArrayList.get(pictureArrayList.size() - 1).size() == 5){
                    pictureAttachmentJsonStrings.add(convertAttachmentList2JsonStr(new ArrayList<>()));
                }
            }else{
                pictureAttachmentJsonStrings.add(convertAttachmentList2JsonStr(new ArrayList<>()));
            }
            item.setPictureAttachmentJsonStrings(pictureAttachmentJsonStrings);
            if (item.getBrandIds() != null && !"".equals(item.getBrandIds())) {
                String[] brands = item.getBrandIds().split(",");
                StringBuffer brandNameBuffer = new StringBuffer("");
                for (String str : brands) {
                    brandNameBuffer.append(brandApiService.queryBrandNameById(Integer.parseInt(str)));
                    brandNameBuffer.append(",");
                }
                item.setBrandNameString(brandNameBuffer.toString());
            }

        });
        return skuSupplyAuthVos;
    }

    /**
     * <AUTHOR>
     * @desc 按照指定长度分割字符串
     * @param list
     * @param size
     * @param <T>
     * @return
     */
    private static <T> List<List<T>> partition(final List<T> list, final int size) {
        Integer limit = (list.size() + size - 1) / size;
        List<List<T>> mglist = new ArrayList<List<T>>();
        Stream.iterate(0, n -> n + 1).limit(limit).forEach(i -> {
            mglist.add(list.stream().skip(i * size).limit(size).collect(Collectors.toList()));
        });
        return mglist;
    }
    /**
     * @param list
     * @return
     * @desc 转为json串给前台使用
     */
    private String convertAttachmentList2JsonStr(List<Attachment> list) {
        if (CollectionUtils.isEmpty(list)) {
            return "[]";
        }
        JSONArray jsonArray = new JSONArray();
        for (Attachment a : list) {
            JSONObject attachmentMap = new JSONObject();
            attachmentMap.put("message", "操作成功");
            attachmentMap.put("httpUrl", api_http + a.getDomain());
            // uri
            String uri = a.getUri();
            if (EmptyUtils.isEmpty(uri)) {
                continue;
            }
            String[] uriArray = uri.split("/");
            String fileName = uriArray[uriArray.length - 1];
            String fileNameTemp = "/" + fileName;
            // 文件后缀
            String[] prefixArray = fileNameTemp.split("\\.");
            String prefix = prefixArray[prefixArray.length - 1];
            // 去除路径名
            String filePath = uri.replaceAll(fileNameTemp, "");
            attachmentMap.put("fileName", fileName);
            attachmentMap.put("filePath", uri);
            attachmentMap.put("prefix", prefix);
            attachmentMap.put("domain", a.getDomain());
            attachmentMap.put("id", a.getAttachmentId());
            jsonArray.add(attachmentMap);
        }
        return jsonArray.toString();
    }

    @Override
    public void delAttachmentByPrimaryKey(Integer attachmentId, User user) {
        attachmentMapper.delAttachmentByPrimaryKey(attachmentId);
    }

    @Override
    public Attachment addTraderSupplierAuthAttachment(Attachment attachment, User user) {
        attachment.setAttachmentType(TraderConstant.SKU_AUTH_ATTACHMENT_TYPE_ID_460);
        attachment.setAttachmentFunction(TraderConstant.SKU_AUTH_ATTACHMENT_FUNCTION_ID_4101);
        attachment.setAddTime(DateUtil.sysTimeMillis());
        attachment.setCreator(user.getUserId());
        attachmentMapper.insertSelective(attachment);
        return attachment;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delSkuSupplyAuthById(Integer skuApplyAuthId, User user) {
        //作废主表信息
        SkuSupplyAuth skuSupplyAuth = new SkuSupplyAuth();
        skuSupplyAuth.setSkuSupplyAuthId(skuApplyAuthId);
        skuSupplyAuth.setModTime(new Date());
        skuSupplyAuth.setUpdater(user.getUserId());
        skuSupplyAuthMapper.delSkuSupplyAuthById(skuSupplyAuth);
        //作废明细表信息
        SkuSupplyAuthDetail skuSupplyAuthDetail = new SkuSupplyAuthDetail();
        skuSupplyAuthDetail.setSkuSupplyAuthId(skuApplyAuthId);
        skuSupplyAuthDetail.setModTime(new Date());
        skuSupplyAuthDetail.setUpdater(user.getUserId());
        skuSupplyAuthDetailMapper.delSkuSupplyAuthByAuthId(skuSupplyAuthDetail);
    }

    @Override
    @Transactional
    public int batchInsertRelatedSku(List<SkuSupplyAuthDetail> paramSkuList) {
        // 过滤掉当前授权书下已关联的商品的skuId集合
        Integer skuSupplyAuthId = paramSkuList.get(0).getSkuSupplyAuthId();
        List<Integer> exitSkuIdList = skuSupplyAuthDetailMapper.selectRelatedSkuByAuthId(skuSupplyAuthId).stream().map(SkuSupplyAuthDetail::getSkuId).collect(Collectors.toList());
        List<SkuSupplyAuthDetail> checkedSkuList = paramSkuList.stream().filter(item -> !exitSkuIdList.contains(item.getSkuId())).collect(Collectors.toList());
        return checkedSkuList.size() == 0 ? 0 : skuSupplyAuthDetailMapper.insertBatch(checkedSkuList);
    }

    @Override
    public List<SkuSupplyAuthDetail> selectRelatedSkuByAuthId(Integer skuSupplyAuthId) {
        return skuSupplyAuthDetailMapper.selectRelatedSkuByAuthId(skuSupplyAuthId);
    }

    @Override
    public void saveDeleteRelatedSku(List<Integer> skuSupplyAuthDetailIdList, Integer updater) {
        Date modTime = new Date();
        skuSupplyAuthDetailMapper.saveDeleteRelatedSku(skuSupplyAuthDetailIdList, updater, modTime);
    }

    @Override
    public TraderSupplierDto getTraderSupplierInfoById(Integer traderId) {
        TraderSupplierDto traderSupplierInfoById = traderSupplierMapper.getTraderSupplierInfoById(traderId);
        //查询供应商可用账期余额
        // 老逻辑，不知道业务，不进行抽取
        TraderPeriodData traderPeriodData = traderDataService.getTraderPeriodData(traderId, 2);
        if (traderPeriodData != null && traderPeriodData.getPeriodAmount() != null && traderPeriodData.getPeriodAmountOccupy() != null
                && traderPeriodData.getPeriodAmountUsed() != null) {
            traderSupplierInfoById.setPeriodAmount(traderPeriodData.getPeriodAmount().subtract(traderPeriodData.getPeriodAmountOccupy()).subtract(traderPeriodData.getPeriodAmountUsed()));
        }
        return traderSupplierInfoById;
    }

}
