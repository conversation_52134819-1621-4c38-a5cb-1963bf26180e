package com.vedeng.erp.kingdee.mapstruct;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeeSaleOutStockEntity;
import com.vedeng.erp.kingdee.dto.KingDeeSaleOutStockDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeSaleOutStockDto;
import org.mapstruct.*;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.kingdee.mapstruct
 * @Date 2023/1/7 16:22
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, builder = @Builder(disableBuilder = true))
public interface KingDeeSaleOutStockConvertor extends BaseMapStruct<KingDeeSaleOutStockEntity, KingDeeSaleOutStockDto> {

    /**
     * dto转entity
     *
     * @param dto KingDeeSaleOutStockDto
     * @return KingDeeSaleOutStockEntity
     */
    @Mapping(target = "FEntity", source = "FEntity", qualifiedByName = "fEntityListToJsonArray")
    @Mapping(target = "FBillTypeId", source = "FBillTypeID")
    @Mapping(target = "FQzokBddjtid", source = "f_qzok_bddjtid")
    @Mapping(target = "FQzokGsbm", source = "f_qzok_gsbm")
    @Mapping(target = "FCustomerId", source = "FCustomerID")
    @Override
    KingDeeSaleOutStockEntity toEntity(KingDeeSaleOutStockDto dto);

    /**
     * entity转dto
     *
     * @param entity KingDeeSaleOutStockEntity
     * @return KingDeeSaleOutStockDto
     */
    @Mapping(target = "FEntity", source = "FEntity", qualifiedByName = "fEntityJsonArrayToList")
    @Override
    KingDeeSaleOutStockDto toDto(KingDeeSaleOutStockEntity entity);

    /**
     * entity 中JSONArray 转 原对象
     *
     * @param jsonArray JSONArray
     * @return List<KingDeeSaleOutStockDto> dto中的对象
     */
    @Named("fEntityJsonArrayToList")
    default List<KingDeeSaleOutStockDetailDto> entryJsonArrayToList(JSONArray jsonArray) {
        if (CollUtil.isEmpty(jsonArray)) {
            return Collections.emptyList();
        }
        return jsonArray.toJavaList(KingDeeSaleOutStockDetailDto.class);
    }

    /**
     * dto 原对象中 转 JSONArray
     *
     * @param source 对象
     * @return JSONArray JSONArray
     */
    @Named("fEntityListToJsonArray")
    default JSONArray entryListToJsonArray(List<KingDeeSaleOutStockDetailDto> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSONArray.parseArray(JSON.toJSONString(source));
    }
}
