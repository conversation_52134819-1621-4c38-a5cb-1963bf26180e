package com.vedeng.im.controller;

import com.vedeng.authorization.model.User;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.system.service.UserApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpSession;

@Controller
@RequestMapping("/im/base")
public class ImController extends  BaseController{

    @Value("${im_index_url}")
    protected String imIndexUrl;

    @Value("${lxcrmUrl}")
    protected  String lxcrmUrl;

    @Autowired
    private UserApiService userApiService;

    @RequestMapping("/index")
    public ModelAndView index( HttpSession session){
        String url=imIndexUrl;
        if(session!=null){
            User user = (User) session.getAttribute(Consts.SESSION_USER);
            if(user!=null&&user.getUserId()!=null){
                url=url+"?userId="+user.getUserId();
            }
        }
        ModelAndView mv=new ModelAndView("/im/index");
        mv.addObject("imIndexUrl",url);
        mv.addObject("goodsInfoUrl",goodsInfoUrl);
        mv.addObject("lxcrmUrl",lxcrmUrl);
        return mv;
    }

    @NoNeedAccessAuthorization
    @ResponseBody
    @RequestMapping("/checkNeedOpenIm")
    public ResultInfo checkNeedOpenIm(HttpSession session,Integer positionType){
         if(session!=null){
            User user = (User) session.getAttribute(Consts.SESSION_USER);
            if(user!=null&&user.getUserId()!=null){
                boolean isB2b = userApiService.checkUserIsB2b(user.getUserId(),positionType);
                if(isB2b){
                    return ResultInfo.success("当前用户为B2B岗位，可以打开");
                }
            }
        }
        return ResultInfo.error("当前用户不自动打开Im");
    }

    @RequestMapping("/notCreateBCReason")
    public ModelAndView notCreateBCReason(){
        ModelAndView mv=new ModelAndView("/im/not_create_bc_reason");
        String url=ezNotCreateBcReasonUrl;
        mv.addObject("ezUrl",url);
        return mv;
    }
}
