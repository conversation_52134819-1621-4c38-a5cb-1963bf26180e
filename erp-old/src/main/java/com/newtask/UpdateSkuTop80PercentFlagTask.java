package com.newtask;

import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.service.BaseService;
import com.vedeng.flash.dto.temp.CalculatedSaleSumTemp;
import com.vedeng.flash.dto.temp.OrderingPoolSortTemp;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.system.model.SysOptionDefinition;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @program: erp.vedeng.com
 * @description: 更新Sku中是销售额前80%的标识任务
 * @author: Pusan
 * @create: 2021-05-18 21:05
 **/
@Slf4j
@JobHandler(value = "updateSkuTop80PercentFlagTask")
@Component
public class UpdateSkuTop80PercentFlagTask extends AbstractJobHandler {

    @Resource
    private CoreSkuMapper coreSkuMapper;

    @Autowired
    private BaseService baseService;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        log.info("更新Sku中是销售额前80%的标识开始,时间:{}",param);
        XxlJobLogger.log("更新Sku中是销售额前80%的标识开始,时间:{}",param);

        // 清空sku表三个标识排序字段
        coreSkuMapper.update80PercentFlag();

        // 每种商品类型 -- 过滤 319 & 653
        List<SysOptionDefinition> goodsTypes = baseService.getSysOptionDefinitionListByParentId(SysOptionConstant.ID_315);
        goodsTypes = goodsTypes.stream()
                .filter(item -> !Integer.valueOf(653).equals(item.getSysOptionDefinitionId()) && !Integer.valueOf(319).equals(item.getSysOptionDefinitionId()))
                .collect(Collectors.toList());
        log.info("商品类型 goodsTypes -- {}",goodsTypes);

        // 不同时间范围
        List<Integer> timeFrameList = Arrays.asList(Integer.valueOf(1), Integer.valueOf(3), Integer.valueOf(12));
        log.info("时间范围 timeFrameList -- {}",timeFrameList);
        // 1.计算出每种商品类型
        for(SysOptionDefinition goodType:goodsTypes) {
            // 2.去年:12 前一个月:1 前三个月:3
            for (Integer timeFrame:timeFrameList){
                // 获取要改变标志集合&顺序号
                Integer indexNum = 1;
                List<OrderingPoolSortTemp> toChangeSkuIdSortList = new ArrayList<>();
                // 销售额80%
                BigDecimal skuSum80Percent = coreSkuMapper.getSkuSum80Percent(goodType,timeFrame);
                // 计算出每个Sku的销售额
                List<CalculatedSaleSumTemp> calculatedSaleSumTemps = coreSkuMapper.getSkuListOrderBySaleSumDesc(goodType,timeFrame);
                if(CollectionUtils.isEmpty(calculatedSaleSumTemps)){continue;}
                // 初始化 用于比较销售额80%
                BigDecimal toSumTemp = BigDecimal.ZERO;
                // 每个Sku 不同时间范围内
                for (CalculatedSaleSumTemp sumTemp : calculatedSaleSumTemps) {
                    toSumTemp = toSumTemp.add(sumTemp.getSaleSum()==null?BigDecimal.ZERO:sumTemp.getSaleSum());
                    toChangeSkuIdSortList.add(OrderingPoolSortTemp.builder().skuId(sumTemp.getSkuId()).sort(indexNum++).build());
                    if(toSumTemp.compareTo(skuSum80Percent) >= 0){
                        break;
                    }
                }
                log.info("要调整Top80%标识id toChangeSkuIdSortList -- 商品类型:{} 时间范围:{} -- {}",goodType.getTitle(),timeFrame,toChangeSkuIdSortList);
                if(CollectionUtils.isEmpty(toChangeSkuIdSortList)){
                    continue;
                }
                coreSkuMapper.update80PercentFlagBySkuIdList(toChangeSkuIdSortList,timeFrame);
            }
        }

        log.info("更新Sku中是销售额前80%的标识结束,时间:{}",param);
        XxlJobLogger.log("更新Sku中是销售额前80%的标识结束,时间:{}",param);
        return SUCCESS;
    }
}
