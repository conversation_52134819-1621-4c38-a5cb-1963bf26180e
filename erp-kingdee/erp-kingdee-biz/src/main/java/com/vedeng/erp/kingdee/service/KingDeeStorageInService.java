package com.vedeng.erp.kingdee.service;

import com.vedeng.erp.kingdee.dto.KingDeeStorageInDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeStorageInQueryResultDto;
import com.vedeng.infrastructure.kingdee.service.KingDeeBaseService;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/3/6 9:09
 **/
public interface KingDeeStorageInService extends KingDeeBaseService<KingDeeStorageInDto> {


    List<KingDeeStorageInQueryResultDto> getKingDeeStorageIn(String f_qzok_bddjtid, String f_qzok_bddjhid);
}
