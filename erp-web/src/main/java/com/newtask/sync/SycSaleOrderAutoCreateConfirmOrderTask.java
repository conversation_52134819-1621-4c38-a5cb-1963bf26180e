package com.newtask.sync;

import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.Constants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.FileInfo;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import com.vedeng.erp.system.SyncDataTypeEnum;
import com.vedeng.erp.system.api.SyncDataErpApiService;
import com.vedeng.erp.system.dto.SyncDataErpDto;
import com.vedeng.logistics.dao.ExpressMapper;
import com.vedeng.logistics.model.*;
import com.vedeng.logistics.model.vo.BatchExpressVo;
import com.vedeng.logistics.service.ConfirmationFormRecodeService;
import com.vedeng.logistics.service.ExpressService;
import com.vedeng.logistics.service.WarehouseGoodsOperateLogVirtualService;
import com.vedeng.logistics.service.WarehouseOutService;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.system.model.vo.UrlToPdfParam;
import com.vedeng.system.service.OssUtilsService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@JobHandler(value="SycSaleOrderAutoCreateConfirmOrderTask")
public class SycSaleOrderAutoCreateConfirmOrderTask  extends AbstractJobHandler {

    private Logger logger = LoggerFactory.getLogger(SycSaleOrderAutoCreateConfirmOrderTask.class);


    @Value("${oss_http}")
    private String ossHttp;

    /**
     * OSS地址
     */
    @Value("${oss_url}")
    private String ossDomain;


    @Value("${erp_url}")
    private String erpUrl ;

    @Value("${wms_client_key}")
    private String wmsClientKey;

    @Value("${html2Pdf.domain}")
    private String html2PdfDomain;

    @Autowired
    private OssUtilsService ossUtilsService;

    @Autowired
    private SyncDataErpApiService syncDataErpApiService;

    @Autowired
    @Qualifier("warehouseOutService")
    private WarehouseOutService warehouseOutService;

    @Autowired
    private WarehouseGoodsOperateLogVirtualService warehouseGoodsOperateLogVirtualService;

    @Autowired
    private SaleOrderApiService saleOrderApiService;

    @Autowired
    private ExpressService expressService;

    @Autowired
    @Qualifier("confirmationFormRecodeService")
    protected ConfirmationFormRecodeService confirmationFormRecodeService;

    @Autowired
    @Qualifier("saleorderMapper")
    private SaleorderMapper saleorderMapper;

    @Autowired
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Resource
    private ExpressMapper expressMapper;

    /**
     * 全部发货
     */
    private static final Integer DELIVERY_STATUS_ALL = 2;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        String dataType = SyncDataTypeEnum.SALEORDER_AUTO_COMFIRM_ORDER.getDataType();//待同步确认单的数据
        List<SyncDataErpDto> syncDataErpDtoList =  syncDataErpApiService.selectByProcessStatus(ErpConst.ZERO,dataType);
        if(CollectionUtils.isEmpty(syncDataErpDtoList)){
            log.info("当前无待同步确认单的数据自动同步到销售订单下的确认单信息");
            XxlJobLogger.log("当前无待同步确认单的数据自动同步到销售订单下的确认单信息");
            return ReturnT.SUCCESS;
        }
        for(SyncDataErpDto syncDataErpDto : syncDataErpDtoList) {
            String businessNo = syncDataErpDto.getBusinessNo();
            XxlJobLogger.log("当前处理到了" + businessNo);
            try {
                SaleorderInfoDto saleorderInfoDto = saleOrderApiService.getBySaleOrderNo(syncDataErpDto.getBusinessNo());
                if(saleorderInfoDto == null || saleorderInfoDto.getStatus().equals(3)){//  `STATUS` tinyint(1) unsigned DEFAULT '0' COMMENT '订单状态：0待确认（默认）、1进行中、2已完结、3已关闭 4待用户确认',
                    syncDataErpDto.setRemark("E00,源订单不存在或已关闭");
                    syncDataErpDto.setProcessStatus(ErpConst.THREE);
                    syncDataErpDto.setUpdateTime(new Date());
                    syncDataErpApiService.updateByPrimaryKeySelective(syncDataErpDto);
                }else {
                    //发货状态
                    Integer deliveryStatus = saleorderInfoDto.getDeliveryStatus();
                    if(!DELIVERY_STATUS_ALL.equals(deliveryStatus)){//没有全部发货
                        syncDataErpDto.setRemark("S01,当前订单没有全部发货，待继续同步，当前不做任何操作");
                        syncDataErpDto.setUpdateTime(new Date());
                        syncDataErpApiService.updateByPrimaryKeySelective(syncDataErpDto);
                    }else if(saleorderInfoDto.getConfirmationFormAudit()!=null&&saleorderInfoDto.getConfirmationFormAudit()>0){
                        //确认单审核状态：0：待提交审核；1.审核中；2.审核通过；3.审核不通过、但凡符合这个数据，直接结束不用跑了
                        syncDataErpDto.setRemark("E01,当前订单已有确认单数据，直接跳过不跑了:"+saleorderInfoDto.getConfirmationFormAudit());
                        syncDataErpDto.setUpdateTime(new Date());
                        syncDataErpDto.setProcessStatus(ErpConst.TWO);//标记为左边，下次也不会再跑了
                        syncDataErpApiService.updateByPrimaryKeySelective(syncDataErpDto);
                    }else{
                        //进入业务逻辑检查点 是否已经上传过了收货确认单
                        Saleorder saleorder = saleorderMapper.selectByPrimaryKey(saleorderInfoDto.getSaleorderId());
                        if (saleorder != null && saleorder.getConfirmationFormUpload() == 1) {
                            // 已上传收货确认单
                            syncDataErpDto.setProcessStatus(ErpConst.TWO);
                            syncDataErpDto.setUpdateTime(new Date());
                            syncDataErpDto.setRemark("E02,已上传收货确认单，任务不执行这一条。");
                            syncDataErpApiService.updateByPrimaryKeySelective(syncDataErpDto);
                        }

                        //处理生成的逻辑
                        boolean result = syncDataErpForConfirmOrder(saleorderInfoDto);
                        if(result){
                            syncDataErpDto.setProcessStatus(ErpConst.TWO);
                            syncDataErpDto.setUpdateTime(new Date());
                            syncDataErpDto.setRemark("S03,成功");
                            syncDataErpApiService.updateByPrimaryKeySelective(syncDataErpDto);
                        }else{
                            //同步没有成功，需要检查是什么原因
                            syncDataErpDto.setRemark("S02,当前订单没有同步成功，待下一次继续");
                            syncDataErpDto.setUpdateTime(new Date());
                            syncDataErpApiService.updateByPrimaryKeySelective(syncDataErpDto);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("同步确认单的数据失败"+businessNo,e);
            }
        }
        return ReturnT.SUCCESS;

    }


    private boolean syncDataErpForConfirmOrder(SaleorderInfoDto saleorderInfoDto ) throws MalformedURLException {
        List<Map<String,Object>> warehouseLogList = warehouseOutService.getWarehouseBySaleorderId(saleorderInfoDto.getSaleorderId());
        if(CollectionUtils.isEmpty(warehouseLogList)){
            return false;//无任何出库记录
        }
        log.info("销售订单的出入库日志如下：{}",JSON.toJSONString(warehouseLogList));
        for(Map<String,Object> warehouseLog : warehouseLogList){
            Integer isAllOut = MapUtils.getInteger(warehouseLog, "IS_ALL_OUT");//判断是否全部出库了，根据普发+直发的出入库日志来计算
            if(isAllOut == 0){
                return false;
            }
        }

        List<OutboundBatchesRecode> outboundBatchesRecodes = expressService.getSaleOrderOutboundBatchesRecode(null, saleorderInfoDto.getSaleorderId());
        if (CollectionUtils.isEmpty(outboundBatchesRecodes)) {
            return false;
        } else {
            for (OutboundBatchesRecode batch : outboundBatchesRecodes) {
                StringBuilder wlogIds = new StringBuilder();
                StringBuilder directWlogIds = new StringBuilder();
                String batchNo = batch.getBatchNo();

                Saleorder saleorder = new Saleorder();
                saleorder.setSaleorderId(saleorderInfoDto.getSaleorderId());
                // 获取订单详情
                saleorder.setCompanyId(1);
                saleorder.setBussinessType(2);
                Long arrivalTime = 0L;

                //List<BatchExpressVo> batchExpressByIds = new ArrayList<>();
                if (batch.getBatchType().equals(1)) {
                    List<WarehouseGoodsOperateLog> wgOlistByComments = warehouseOutService.getWGOlistByComments(batchNo, saleorderInfoDto.getSaleorderId());
                    if (wgOlistByComments.size() == 0) {
                        continue;
                    }
                    //物流单号
                    List<Express> expressList = batch.getExpressList();
                    if (CollectionUtils.isNotEmpty(expressList)) {
                        arrivalTime = expressList.get(0).getArrivalTime();
                    }
                    List<String> expressLogiscsNo = expressList.stream().map(Express::getLogisticsNo).collect(Collectors.toList());
                    String logiscsNos = String.join(",", expressLogiscsNo);

                    saleorder.setBatchNoComments(batchNo);
                    // 普发出库记录清单
                    List<WarehouseGoodsOperateLog> warehouseOutList = warehouseOutService.getOutDetil(saleorder);
                    if (null != warehouseOutList) {
                        for (WarehouseGoodsOperateLog warehouseGoodsOperateLog : warehouseOutList) {
                            wlogIds.append(warehouseGoodsOperateLog.getWarehouseGoodsOperateLogId()).append("_");
                        }
                    }
                } else {
                    List<BatchExpressVo> batchExpressByIds = new ArrayList<>();
                    directBatchNos(saleorderInfoDto.getSaleorderId(),batch,batchExpressByIds);
                    batch.setBatchExpressVos(batchExpressByIds);


                    List<WarehouseGoodsOperateLogVirtual> warehouseDirectOutList = warehouseGoodsOperateLogVirtualService.getOutDetil(saleorder);
                    if(CollectionUtils.isNotEmpty(warehouseDirectOutList)){
                        for (WarehouseGoodsOperateLogVirtual warehouseGoodsOperateLogVirtual : warehouseDirectOutList) {
                            directWlogIds.append(warehouseGoodsOperateLogVirtual.getWarehouseGoodsOperateLogVirtualId()).append("_");
                        }
                    }

                    if (CollectionUtils.isNotEmpty(batch.getExpressList())) {
                        arrivalTime = batch.getExpressList().get(0).getArrivalTime();
                    }

                }
                wlogIds.append("#6");//onclick="printOutOrder('c_checknox','6');"贝登不带价格出库单
                directWlogIds.append("#6");

                String encode3 = URLUtil.encode(wlogIds.toString());
                String encode4 = URLUtil.encode(directWlogIds.toString());

                StringBuffer path = new StringBuffer(erpUrl)
                        .append("/warehouse/warehousesout/printOutOrder.do?")
                        .append("orderId=" + saleorderInfoDto.getSaleorderId())
                        .append("&printerButtonFlag=no")
                        .append("&bussinessType=496&bussinessNo=" + saleorderInfoDto.getSaleorderNo())
                        .append("&type_f=")
                        .append("6")
                        .append("&wdlIds=").append(encode3)
                        .append("&directWlogIds=").append(encode4)
                        .append("&autoConfirmOrder=Y")
                        .append("&arrivalTime=").append(arrivalTime);

                String printOutOrderUrl = path.toString();

                printOutOrderUrl = printOutOrderUrl + "&wms_client_key=" + wmsClientKey + "&wms_client_userName=vedeng";
                logger.info("出库单信息上传OSS start saleOrderNo:{},printOutOrderUrl:{}", saleorderInfoDto.getSaleorderNo(), printOutOrderUrl);
                String html2PdfUrl = html2PdfDomain + "/api/render";
                UrlToPdfParam urlToPdfParam = new UrlToPdfParam();
                urlToPdfParam.setUrl(printOutOrderUrl);
                UrlToPdfParam.Pdf pdf = new UrlToPdfParam.Pdf();
                UrlToPdfParam.Pdf.PdfMargin margin = new UrlToPdfParam.Pdf.PdfMargin("1cm", "1cm", "1cm", "1cm");
                pdf.setMargin(margin);
                urlToPdfParam.setPdf(pdf);
                String ossUrl = ossUtilsService.migrateFile2Oss(html2PdfUrl, "pdf", "出库单" + saleorderInfoDto.getSaleorderNo(), urlToPdfParam);
                if (StringUtil.isBlank(ossUrl)) {
                    logger.warn("html2PdfUrl warn，saleOrderNo:{}", saleorderInfoDto.getSaleorderNo());
                    return false;
                }
                logger.info("出库单信息生成成功，开始上传确认单， saleOrderNo:{},ossUrl:{}", saleorderInfoDto.getSaleorderNo(), ossUrl);
                URL url = new URL(ossUrl);

                String path1 = url.getPath();         // /file/display
                String query1 = url.getQuery();       // resourceId=xxxxxx
                String result = path1 + (query1 != null ? "?" + query1 : "");

                String protocol = url.getProtocol();         // http或http
                String host = url.getHost();                 // file.ivedeng.com
                int port = url.getPort();                    // 8080
                String baseUrl = protocol + "://" + host + (port != -1 ? ":" + port : "");


                // 构建 fileInfos
                List<FileInfo> fileInfos = new ArrayList<>();
                FileInfo fileInfo = new FileInfo();
                fileInfo.setCode(0);
                fileInfo.setMessage("上传成功");
                fileInfo.setFileName("验收单" + batch.getBatchNo() + ".pdf");
                fileInfo.setFilePath(result);
                fileInfo.setHttpUrl(ossHttp + ossDomain);
                fileInfo.setPrefix("pdf");
                fileInfo.setPdfFlag(true);
                fileInfo.setDomain(ossDomain);
                fileInfo.setOssResourceId(result.split("=")[1]);
                fileInfo.setFullPath(ossUrl);
                fileInfos.add(fileInfo);

                List<BatchExpressVo> batchExpressVos = batch.getBatchExpressVos(); // 查询/组装每个批次下的物流明细
                // 构建 batchesRecodes
                List<OutboundBatchesRecode> batchesRecodes = new ArrayList<>();
                OutboundBatchesRecode recode = new OutboundBatchesRecode();
                recode.setBatchNo(batch.getBatchNo());
                recode.setCreator(batch.getCreator());
                recode.setAddTime(batch.getAddTime());
                recode.setDeliveryTime(batch.getDeliveryTime());
                recode.setUploadStatus(0);
                recode.setOnlineConfirm(0);
                recode.setBatchExpressVos(batchExpressVos);
                recode.setBatchTime(batch.getBatchTime());
                recode.setIsEnable(batch.getIsEnable());
                recode.setUpdater(batch.getUpdater());
                recode.setModTime(batch.getModTime());
                recode.setId(batch.getId());
                recode.setBatchType(batch.getBatchType());
                batchesRecodes.add(recode);

                // 构建最终参数
                ConfirmationFormRecode confirmationFormRecode = new ConfirmationFormRecode();
                confirmationFormRecode.setSaleOrderId(saleorderInfoDto.getSaleorderId());
                confirmationFormRecode.setFileInfos(fileInfos);
                confirmationFormRecode.setBatchesRecodes(batchesRecodes);
                log.info("构造确认单上传开始：{}", JSON.toJSONString(confirmationFormRecode));
                // 调用接口
                boolean resultUpload = confirmationFormRecodeService.uploadConfirmationFormForAutoConfirmOrder(confirmationFormRecode);
                if (!resultUpload) {
                    return false;
                }
            }
            log.info("更新确认单审核状态开始");
            Saleorder saleorderUpdate = new Saleorder();
            saleorderUpdate.setSaleorderId(saleorderInfoDto.getSaleorderId());
            saleorderUpdate.setConfirmationFormUpload(Constants.TWO);
            saleorderUpdate.setConfirmationFormAudit(Constants.TWO);
            saleorderUpdate.setConfirmationSubmitTime(System.currentTimeMillis());
            saleorderMapper.updateByPrimaryKeySelective(saleorderUpdate);
            return true;
        }


    }


    public void directBatchNos(Integer saleorderId, OutboundBatchesRecode outboundBatchesRecode, List<BatchExpressVo> batchExpressByIds) {
        String batchNo = outboundBatchesRecode.getBatchNo();
        Express expressQueryVo = new Express();
        List<SaleorderGoods> saleorderGoods = saleorderGoodsMapper.getAllSaleorderGoodsBySaleorderId(saleorderId);
        List<Express> buyExpressList = new ArrayList<>();
        if (saleorderGoods.size()>0){
            List<Integer> listSale = saleorderGoods.stream().map(SaleorderGoods::getSaleorderGoodsId).collect(Collectors.toList());
            expressQueryVo.setRelatedIds(listSale);
            expressQueryVo.setBatchNo(batchNo);
            buyExpressList = expressMapper.getBuyExpressList(expressQueryVo);
            if(CollectionUtils.isNotEmpty(buyExpressList)){
                outboundBatchesRecode.setExpressList(buyExpressList);
            }
        }


        Map<String, BatchExpressVo> skuNum = new HashMap<>();
        for (int j = 0; j < buyExpressList.size(); j++) {
            Express express = buyExpressList.get(j);
            String logisticsNo = express.getLogisticsNo();
            List<ExpressDetail> expressDetail = express.getExpressDetail();
            List<String> checkSku = new ArrayList<>();
            outboundBatchesRecode.setBatchTime(DateUtil.convertString(express.getDeliveryTime(), "yyyy-MM-dd"));

            for (ExpressDetail detail : expressDetail) {
                String sku = detail.getSku();
                if (skuNum.containsKey(sku)) {
                    BatchExpressVo batchExpressVo = skuNum.get(sku);
                    Integer num = batchExpressVo.getNum();
                    num = num+detail.getNum();
                    batchExpressVo.setNum(num);
                    List<String> logisticsOrderNoUse = batchExpressVo.getLogisticsOrderNoUse();
                    if (!logisticsOrderNoUse.contains(logisticsNo)) {
                        logisticsOrderNoUse.add(logisticsNo);
                        batchExpressVo.setLogisticsOrderNo(String.join(",",logisticsOrderNoUse));
                    }
                }else{
                    BatchExpressVo batchExpressVo = new BatchExpressVo();
                    batchExpressVo.setNum(detail.getNum());
                    List<String> logisticsOrderNoUse = new ArrayList<>();
                    logisticsOrderNoUse.add(logisticsNo);
                    batchExpressVo.setLogisticsOrderNoUse(logisticsOrderNoUse);
                    batchExpressVo.setLogisticsOrderNo(logisticsNo);
                    batchExpressVo.setSku(detail.getSku());
                    batchExpressVo.setModel(detail.getModel());
                    batchExpressVo.setBrand(detail.getBrandName());
                    batchExpressVo.setGoodsName(detail.getGoodName());
                    skuNum.put(detail.getSku(),batchExpressVo);
                }

            }
        }

        Set<String> keySet = skuNum.keySet();
        for (String s : keySet) {
            BatchExpressVo batchExpressVo = skuNum.get(s);
            batchExpressByIds.add(batchExpressVo);
        }
    }







}
