package com.wms.dto;

import java.math.BigDecimal;

public class WmsLwhDto {

    private String customerId;

    private String sku;

    private String Descr_C;

    //高（箱）
    private BigDecimal skuHigh;

    //长（箱）
    private BigDecimal skuLength;

    //宽（箱）
    private BigDecimal skuWidth;

    //包装体积（箱）
    private BigDecimal cube;

    //毛重（箱）
    private BigDecimal grossWeight;

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getDescr_C() {
        return Descr_C;
    }

    public void setDescr_C(String descr_C) {
        Descr_C = descr_C;
    }

    public BigDecimal getSkuHigh() {
        return skuHigh;
    }

    public void setSkuHigh(BigDecimal skuHigh) {
        this.skuHigh = skuHigh;
    }

    public BigDecimal getSkuLength() {
        return skuLength;
    }

    public void setSkuLength(BigDecimal skuLength) {
        this.skuLength = skuLength;
    }

    public BigDecimal getSkuWidth() {
        return skuWidth;
    }

    public void setSkuWidth(BigDecimal skuWidth) {
        this.skuWidth = skuWidth;
    }

    public BigDecimal getCube() {
        return cube;
    }

    public void setCube(BigDecimal cube) {
        this.cube = cube;
    }

    public BigDecimal getGrossWeight() {
        return grossWeight;
    }

    public void setGrossWeight(BigDecimal grossWeight) {
        this.grossWeight = grossWeight;
    }
}
