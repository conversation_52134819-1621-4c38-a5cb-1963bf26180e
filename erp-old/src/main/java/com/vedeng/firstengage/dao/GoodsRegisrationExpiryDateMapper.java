package com.vedeng.firstengage.dao;

import com.vedeng.firstengage.model.GoodsRegisrationExpiryDate;
import com.vedeng.firstengage.model.GoodsRegisrationExpiryDateExample;

import java.util.HashMap;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface GoodsRegisrationExpiryDateMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_REGISTRATION_EXPIRY_DATE
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    int countByExample(GoodsRegisrationExpiryDateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_REGISTRATION_EXPIRY_DATE
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    int deleteByExample(GoodsRegisrationExpiryDateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_REGISTRATION_EXPIRY_DATE
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    int deleteByPrimaryKey(Integer goodsRegistrationExpiryDateId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_REGISTRATION_EXPIRY_DATE
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    int insert(GoodsRegisrationExpiryDate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_REGISTRATION_EXPIRY_DATE
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    int insertSelective(GoodsRegisrationExpiryDate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_REGISTRATION_EXPIRY_DATE
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    List<GoodsRegisrationExpiryDate> selectByExample(GoodsRegisrationExpiryDateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_REGISTRATION_EXPIRY_DATE
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    GoodsRegisrationExpiryDate selectByPrimaryKey(Integer goodsRegistrationExpiryDateId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_REGISTRATION_EXPIRY_DATE
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    int updateByExampleSelective(@Param("record") GoodsRegisrationExpiryDate record, @Param("example") GoodsRegisrationExpiryDateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_REGISTRATION_EXPIRY_DATE
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    int updateByExample(@Param("record") GoodsRegisrationExpiryDate record, @Param("example") GoodsRegisrationExpiryDateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_REGISTRATION_EXPIRY_DATE
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    int updateByPrimaryKeySelective(GoodsRegisrationExpiryDate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_REGISTRATION_EXPIRY_DATE
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    int updateByPrimaryKey(GoodsRegisrationExpiryDate record);

    /**
     * 根据注册证id批量删除
     */
    int deleteByRegistrationNumberId(@Param("registrationNumberId") Integer registrationNumberId);

    /**
     * 根据注册证id查询有效期
     */
    List<GoodsRegisrationExpiryDate> getListByRegistrationNumberId(@Param("registrationNumberId") Integer registrationNumberId);

    /**
     * 根据注册证id和类型查询有效期
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/1/18 9:55.
     * @author: Randy.Xu.
     * @param paramMap
     * @return: com.vedeng.firstengage.model.GoodsRegisrationExpiryDate.
     * @throws:  .
     */
    GoodsRegisrationExpiryDate getValidTimeByRegistrationNumber(HashMap paramMap);

}