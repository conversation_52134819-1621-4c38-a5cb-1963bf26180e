package com.vedeng.erp.trader.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.page.Page;
import com.vedeng.erp.kingdee.dto.KingDeeSupplierBankDto;
import com.vedeng.erp.kingdee.dto.KingDeeSupplierDto;
import com.vedeng.erp.kingdee.service.KingDeeSupplierApiService;
import com.vedeng.erp.system.dto.LikeTraderDto;
import com.vedeng.erp.trader.domain.entity.TraderSupplierEntity;
import com.vedeng.erp.trader.dto.TraderContactDto;
import com.vedeng.erp.trader.dto.TraderSupplierDto;
import com.vedeng.erp.trader.mapper.TraderContactMapper;
import com.vedeng.erp.trader.mapper.TraderSupplierMapper;
import com.vedeng.erp.trader.service.TraderSupplierApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class TraderSupplierServiceImpl implements TraderSupplierApiService {

    @Autowired
    private TraderContactMapper traderContactMapper;

    @Autowired
    private TraderSupplierMapper traderSupplierMapper;

    @Autowired
    private KingDeeSupplierApiService kingDeeSupplierApiService;

    @Override
    public List<TraderContactDto> getSupplierContact(Integer traderId) {
        List<TraderContactDto> list = traderContactMapper.getSupplierContact(traderId);
        return list;
    }


    @Override
    public List<TraderSupplierDto> selectPushKingDeeTraderSupplierData(Long begin, Long end, Integer limit) {
        return traderSupplierMapper.selectPushKingDeeTraderSupplierData(begin, end, limit);
    }

    @Override
    public List<TraderSupplierDto> selectPushKingDeeTraderSupplierDataByIds(List<Integer> ids) {
        return traderSupplierMapper.selectPushKingDeeTraderSupplierDataByIds(ids);
    }

    @Override
    public Boolean updateSupplierAmount(Integer traderId, BigDecimal amount) {
        int count = traderSupplierMapper.updateTraderSupplierAmount(traderId, amount);
        if (count > 0) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public TraderSupplierDto getTraderSupplierByTraderId(Integer traderId) {
        return traderSupplierMapper.findByTraderId(traderId);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void increaseSupplierBalance(Integer traderId, BigDecimal amount) {
        int retryCount = 3;
        for (int j = 0; j < retryCount; j++) {
            // 重试3次
            com.vedeng.erp.trader.domain.dto.TraderSupplierDto traderSupplierDto = traderSupplierMapper.getTraderSupplierInfoById(traderId);
            BigDecimal oldAmount = traderSupplierDto.getAmount();
            // 乐观锁
            int t = traderSupplierMapper.updateSupplyBalance(traderId, amount, oldAmount);
            if (t > 0) {
                log.info("采购费用售后增加供应商余额成功，余额：{}", oldAmount.subtract(amount));
                break;
            }
            if (t == 0 && j == retryCount - 1) {
                log.info("增加供应商处余额失败:供应商：{}", traderId);
                throw new ServiceException("增加供应商处余额失败");
            }
        }
    }

    @Override
    public KingDeeSupplierDto getKingDeeSupplierInfo(Integer traderSupplierId) {
        TraderSupplierDto supplierDto = traderSupplierMapper.getSupplierInfoForKingDee(traderSupplierId);
        if (supplierDto == null) {
            return null;
        }

        KingDeeSupplierDto kingDeeSupplierDto = new KingDeeSupplierDto();
        kingDeeSupplierDto.setFNumber(supplierDto.getTraderSupplierId());
        kingDeeSupplierApiService.query(kingDeeSupplierDto);
        kingDeeSupplierDto.setFName(supplierDto.getTraderName());
        if (ObjectUtil.isEmpty(kingDeeSupplierDto.getId())) {
            kingDeeSupplierDto.setKingDeeBizEnums(KingDeeBizEnums.saveSupplier);
            kingDeeSupplierDto.setFSupplierId("0");
        } else {
            kingDeeSupplierDto.setKingDeeBizEnums(KingDeeBizEnums.updateSupplier);
        }
        Integer traderType = traderSupplierMapper.findTraderTypeByTraderSupplierId(traderSupplierId);
        if (ErpConstant.ONE.equals(traderType)) {
            kingDeeSupplierDto.setFSupplierClassify("01");
        } else if (ErpConstant.TWO.equals(traderType)) {
            kingDeeSupplierDto.setFSupplierClassify("02");
        } else {
            kingDeeSupplierDto.setFSupplierClassify("");
        }
        List<KingDeeSupplierBankDto> fBankInfo = new ArrayList<>();
        supplierDto.getTraderFinanceDtoList().forEach(item -> {
            KingDeeSupplierBankDto temp = new KingDeeSupplierBankDto();
            temp.setFBankCode(item.getBankAccount());
            temp.setFBankHolder(supplierDto.getTraderName());
            temp.setFOpenBankName(item.getBank());
            temp.setFCNAPS(item.getBankCode());
            fBankInfo.add(temp);
        });
        kingDeeSupplierDto.setFBankInfo(fBankInfo);
        return kingDeeSupplierDto;
    }

    /**
     * 根据交易者id获取供应商
     *
     * @param name  供应商名称
     * @param pageNo pageSize
     * @return
     */
    @Override
    public List<TraderSupplierDto> getTraderSupplierByTraderName(String name, int start,int pageSize  ) {
        if(StringUtils.isBlank(name)){
            return Collections.emptyList();
        }
        return traderSupplierMapper.findByTraderName(name,start,pageSize);
    }

    @Override
    public long countTraderSupplierByTraderName(String keyword) {
        if(StringUtils.isBlank(keyword)){
            return 0;
        }
        return traderSupplierMapper.countTraderSupplierByTraderName(keyword);
    }

    @Override
    public List<LikeTraderDto> findByTraderTypeAndTraderName(String keyword) {
        return traderSupplierMapper.findByTraderTypeAndTraderName(keyword);
    }
}
