package com.vedeng.erp.kingdee.dto.result;

import lombok.Data;

@Data
public class KingDeeHongRuiBuyOrderGoodsDto {

    private Integer id;

    /**
     * 物料编码
     */
    private String sku;

    /**
     * 物料名称
     */
    private String skuName;

    /**
     * 数量
     */
    private String num;

    /**
     * 单位
     */
    private String unit;

    /**
     * 采购含税单价（采购实际含税单价）
     */
    private String buyRealPrice;

    /**
     * 销售含税单价（采购价）
     */
    private String salePrice;

    /**
     * 采购税率 （实际供应商 - 收票种类中税率	）
     */
    private String buyRate;

    /**
     * 销售税率 （供应商 - 收票种类中税率	）
     */
    private String saleRate;

    /**
     * 采购价税合计（实际：采购价 * 数量	）
     */
    private String buyTotal;

    /**
     * 销售价税合计（采购价 * 数量	）
     */
    private String saleTotal;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 规格型号
     */
    private String model;

    /**
     * 注册证号/备案凭证号
     */
    private String registrationNumber;

    /**
     * ⽣产⼚商
     */
    private String manufacturer;

    /**
     * 货期 (⽇)
     */
    private String deliveryDay;

    /**
     * 安装政策
     */
    private String installPolicy;

    /**
     * 质保期
     */
    private String warrantyPeriod;

    /**
     * 备注
     */
    private String remark;


}
