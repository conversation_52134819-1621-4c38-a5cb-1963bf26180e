package com.wms.service.output;

import com.vedeng.common.util.StringUtil;
import com.wms.constant.WMSContant;
import com.wms.dto.PutSaleOrderDto;
import com.wms.dto.WmsRequest;
import com.wms.service.AbstractWmsInterface;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;

/**
 * 出库单的推送(包括各种各样的出库单，参数需要自己转换下)
 */
@Service
public class PutSaleOrderInterface extends AbstractWmsInterface {

    @Override
    protected void setMethodAndMessageId(WmsRequest wmsRequest) {
        wmsRequest.setMethod(WMSContant.PUT_ORIGINAL_SALESORDER);
        wmsRequest.setMessageId(WMSContant.PUT_ORIGINAL_SALESORDER);
    }

    @Override
    protected JSONObject getXmlDate(Object... param) {

        if(param == null ||param.length == 0){
            return null;
        }

        PutSaleOrderDto putSaleOrderDto = (PutSaleOrderDto)param[0];

        JSONArray headArray = new JSONArray();

        JSONObject headItem = new JSONObject();
        String warehouseId = putSaleOrderDto.getWarehouseId();
        if(StringUtil.isBlank(warehouseId)){
            warehouseId = "NJ01";
        }
        headItem.put("customerId","VEDENG");
        headItem.put("warehouseId",warehouseId);
        headItem.put("docNo",putSaleOrderDto.getDocNo());
        headItem.put("orderType", putSaleOrderDto.getOrderType());
        headItem.put("orderTime",putSaleOrderDto.getOrderTime());
        headItem.put("expectedShipmentTime1",putSaleOrderDto.getExpectedShipmentTime1());
        headItem.put("expectedShipmentTime2",putSaleOrderDto.getExpectedShipmentTime2());
        headItem.put("soReferenceA",putSaleOrderDto.getSoReferenceA());
        headItem.put("soReferenceB",putSaleOrderDto.getSoReferenceB());

        headItem.put("consigneeId",putSaleOrderDto.getConsigneeId());
        headItem.put("consigneeName",putSaleOrderDto.getConsigneeName());

        headItem.put("consigneeProvince",putSaleOrderDto.getConsigneeProvince());
        headItem.put("consigneeCity",putSaleOrderDto.getConsigneeCity());
        headItem.put("consigneeDistrict",putSaleOrderDto.getConsigneeDistrict());
        headItem.put("consigneeAddress1",putSaleOrderDto.getConsigneeAddress1());
        headItem.put("consigneeContact",putSaleOrderDto.getConsigneeContact());
        headItem.put("consigneeTel1",putSaleOrderDto.getConsigneeTel1());
        headItem.put("consigneeTel2",putSaleOrderDto.getConsigneeTel2());

        headItem.put("hedi01",putSaleOrderDto.getHedi01());
        headItem.put("hedi03",putSaleOrderDto.getHedi03());
        headItem.put("hedi04",putSaleOrderDto.getHedi04());
        headItem.put("hedi05",putSaleOrderDto.getHedi05());
        headItem.put("hedi06",putSaleOrderDto.getHedi06());
        headItem.put("hedi07",putSaleOrderDto.getHedi07());
        headItem.put("hedi08", putSaleOrderDto.getHedi08());
        headItem.put("notes",putSaleOrderDto.getNotes());
        headItem.put("carrierId",putSaleOrderDto.getCarrierId());
        headItem.put("carrierName",putSaleOrderDto.getCarrierName());

        JSONArray detailArray = new JSONArray();

        putSaleOrderDto.getDetails().stream().forEach(putSaleOrderGoodsDto -> {
            JSONObject detailItem = new JSONObject();

            detailItem.put("lineNo",putSaleOrderGoodsDto.getLineNo());

            detailItem.put("customerId","VEDENG");
            detailItem.put("sku",putSaleOrderGoodsDto.getSku());
            detailItem.put("qtyOrdered",putSaleOrderGoodsDto.getQtyOrdered());

            //产品质量状态
            detailItem.put("lotAtt08",putSaleOrderGoodsDto.getLotAtt08());
            //专项发货关联VP单号
            detailItem.put("lotAtt07",putSaleOrderGoodsDto.getLotAtt07());

            //产品是否首次交易
            detailItem.put("dedi04",putSaleOrderGoodsDto.getDedi04());

            //来源逻辑仓
            detailItem.put("dedi05",putSaleOrderGoodsDto.getDedi05());

            //目标逻辑仓
            detailItem.put("dedi06",putSaleOrderGoodsDto.getDedi06());

            //ERP订单商品id
            detailItem.put("dedi07",putSaleOrderGoodsDto.getDedi07());
            //ERP订单商品单价
            detailItem.put("dedi09",putSaleOrderGoodsDto.getDedi09());

            // erp直发出库时间
            detailItem.put("dedi13", putSaleOrderGoodsDto.getDedi13());


            detailArray.add(detailItem);
        });

        headItem.put("details",detailArray);
        headArray.add(headItem);

        JSONObject headObject = new JSONObject();
        headObject.put("header",headArray);
        return headObject;
    }

    @Override
    protected <T> T parseResponseDate(JSONObject returnObject) {
        return null;
    }
}
