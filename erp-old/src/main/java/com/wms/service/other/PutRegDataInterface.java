package com.wms.service.other;

import com.vedeng.common.util.DateUtil;
import com.wms.constant.WMSContant;
import com.wms.dto.WmsRequest;
import com.wms.model.po.WmsSkuReg;
import com.wms.service.AbstractWmsInterface;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;

import java.util.Arrays;

/**
 * 商品多注册证下传
 */
@Service
public class PutRegDataInterface extends AbstractWmsInterface {


    @Override
    protected void setMethodAndMessageId(WmsRequest wmsRequest) {
        wmsRequest.setMethod(WMSContant.PUT_REG);
        wmsRequest.setMessageId(WMSContant.PUT_REG);
    }

    @Override
    protected JSONObject getXmlDate(Object... param) {

        if(param == null ||param.length == 0){
            return null;
        }

        JSONArray headArray = new JSONArray();

        Arrays.stream(param).forEach(paramItem -> {

            JSONObject headItem = parseParamToJsonObject(paramItem);

            headArray.add(headItem);
        });

        JSONObject headObject = new JSONObject();
        headObject.put("header",headArray);
        return headObject;
    }

    private JSONObject parseParamToJsonObject(Object paramItem) {

        WmsSkuReg reg = (WmsSkuReg)paramItem;
        //WmsSkuReg reg=registrationNumberMapper.getWmsSkuRegData(registrationNumber.getRegistrationNumberId());
        JSONObject headItem = new JSONObject();
        headItem.put("CustomerID","VEDENG");
        //商品内码(SKU)
        headItem.put("SKU",reg.getSkuNo());
        //产品注册证号/备案号
        headItem.put("ApprovalNo",reg.getRegistrationNumber()==null?"VEDENG":reg.getRegistrationNumber());

        //备注
        headItem.put("Memo",reg.getMemo());

        headItem.put("ApprovalNoValidFrom", DateUtil.convertString(reg.getApprovalNoValidFrom(),"yyyy-MM-dd") );
        headItem.put("ApprovalNoValidTo",DateUtil.convertString(reg.getApprovalNoValidTo(),"yyyy-MM-dd"));

        return headItem;
    }

    @Override
    protected <T> T parseResponseDate(JSONObject returnObject) {
        return null;
    }
}
