package com.vedeng.finance.constant;

/**
 * 付款申请类型
 *
 * <AUTHOR>
 */
public enum PayApplyTypeEnum {
    /**
     * 采购付款
     */
    BUY_ORDER(517, "采购"),


    /**
     * 采购费用付款
     */
    BUY_EXPENSE_ORDER(4125, "采购费用"),


    /**
     * 售后付款
     */
    AFTER_SALES(518, "售后"),

    /**
     * 默认枚举
     */
    DEFAULT_TYPE(0, "");

    /**
     * 代码
     */
    private Integer code;

    /**
     * 描述
     */
    private String desc;

    PayApplyTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static PayApplyTypeEnum getInstance(Integer code) {
        for (PayApplyTypeEnum value : PayApplyTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return DEFAULT_TYPE;
    }
}
