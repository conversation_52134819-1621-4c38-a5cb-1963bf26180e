package com.vedeng.department.model;

import java.util.List;

public class DepartmentsHospital {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_DEPARTMENTS_HOSPITAL.DEPARTMENT_ID
	 * @mbg.generated  Tue Apr 09 19:02:26 CST 2019
	 */
	private Integer departmentId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_DEPARTMENTS_HOSPITAL.DEPARTMENT_NAME
	 * @mbg.generated  Tue Apr 09 19:02:26 CST 2019
	 */
	private String departmentName;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_DEPARTMENTS_HOSPITAL.DESCRIPTION
	 * @mbg.generated  Tue Apr 09 19:02:26 CST 2019
	 */
	private String description;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_DEPARTMENTS_HOSPITAL.IS_DELETE
	 * @mbg.generated  Tue Apr 09 19:02:26 CST 2019
	 */
	private Integer isDelete;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_DEPARTMENTS_HOSPITAL.UPDATER
	 * @mbg.generated  Tue Apr 09 19:02:26 CST 2019
	 */
	private Integer updater;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_DEPARTMENTS_HOSPITAL.MOD_TIME
	 * @mbg.generated  Tue Apr 09 19:02:26 CST 2019
	 */
	private Long modTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_DEPARTMENTS_HOSPITAL.CREATOR
	 * @mbg.generated  Tue Apr 09 19:02:26 CST 2019
	 */
	private Integer creator;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_DEPARTMENTS_HOSPITAL.ADD_TIME
	 * @mbg.generated  Tue Apr 09 19:02:26 CST 2019
	 */
	private Long addTime;

	/**
	 * 更新时间
	 */
	private String modTimeStr;

	/**
	 * 关键词搜索类型
	 */
	private Integer searchStatus;

	/**
	 * 更新时间开始时间
	 */
	private String updateStartDate;

	/**
	 * 更新时间结束时间
	 */
	private String updateEndDate;

	/**
	 * 关键词
	 */
	private String keyWords;
	
	/**
	 * 收费项目
	 */
	private String feePro;

	/**
	 * 商品数量
	 */
	private Integer goodsNum;

	/**
	 * 收费项目
	 */
	private List<DepartmentFeeItems> departmentFeeItems;

	/**
	 * 时间排序
	 */
	private Integer timeSort;

	/**
	 * 排序
	 */
	private Integer sort;

	public Integer getSort() {
		return sort;
	}

	public void setSort(Integer sort) {
		this.sort = sort;
	}

	public Integer getGoodsNum() {
		return goodsNum;
	}

	public void setGoodsNum(Integer goodsNum) {
		this.goodsNum = goodsNum;
	}

	public List<DepartmentFeeItems> getDepartmentFeeItems() {
		return departmentFeeItems;
	}

	public void setDepartmentFeeItems(List<DepartmentFeeItems> departmentFeeItems) {
		this.departmentFeeItems = departmentFeeItems;
	}

	public Integer getTimeSort() {
        return timeSort;
    }

    public void setTimeSort(Integer timeSort) {
        this.timeSort = timeSort;
    }

    public String getUpdateStartDate() {
        return updateStartDate;
    }

    public void setUpdateStartDate(String updateStartDate) {
        this.updateStartDate = updateStartDate;
    }

    public String getUpdateEndDate() {
        return updateEndDate;
    }

    public void setUpdateEndDate(String updateEndDate) {
        this.updateEndDate = updateEndDate;
    }

    public String getKeyWords() {
		return keyWords;
	}

	public void setKeyWords(String keyWords) {
		this.keyWords = keyWords;
	}

	public Integer getSearchStatus() {
		return searchStatus;
	}

	public void setSearchStatus(Integer searchStatus) {
		this.searchStatus = searchStatus;
	}

	public String getModTimeStr() {
		return modTimeStr;
	}

	public void setModTimeStr(String modTimeStr) {
		this.modTimeStr = modTimeStr;
	}

	public String getFeePro() {
		return feePro;
	}

	public void setFeePro(String feePro) {
		this.feePro = feePro;
	}

	/**
	 * @description 收费项目中间表
	 * <AUTHOR>
	 * @param
	 * @date 2019/4/16
	 */
	private List<DepartmentFeeItemsMapping> departmentFeeItemsMappings;

	/**
	 * 收费项目最小级id
	 */
	private List<Integer> feeProsList;

	public List<Integer> getFeePros() {
		return feeProsList;
	}

	public void setFeePros(List<Integer> feePros) {
		this.feeProsList = feePros;
	}

	public List<DepartmentFeeItemsMapping> getDepartmentFeeItemsMappings() {
		return departmentFeeItemsMappings;
	}

	public void setDepartmentFeeItemsMappings(List<DepartmentFeeItemsMapping> departmentFeeItemsMappings) {
		this.departmentFeeItemsMappings = departmentFeeItemsMappings;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_DEPARTMENTS_HOSPITAL.DEPARTMENT_ID
	 * @return  the value of T_DEPARTMENTS_HOSPITAL.DEPARTMENT_ID
	 * @mbg.generated  Tue Apr 09 19:02:26 CST 2019
	 */
	public Integer getDepartmentId() {
		return departmentId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_DEPARTMENTS_HOSPITAL.DEPARTMENT_ID
	 * @param departmentId  the value for T_DEPARTMENTS_HOSPITAL.DEPARTMENT_ID
	 * @mbg.generated  Tue Apr 09 19:02:26 CST 2019
	 */
	public void setDepartmentId(Integer departmentId) {
		this.departmentId = departmentId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_DEPARTMENTS_HOSPITAL.DEPARTMENT_NAME
	 * @return  the value of T_DEPARTMENTS_HOSPITAL.DEPARTMENT_NAME
	 * @mbg.generated  Tue Apr 09 19:02:26 CST 2019
	 */
	public String getDepartmentName() {
		return departmentName;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_DEPARTMENTS_HOSPITAL.DEPARTMENT_NAME
	 * @param departmentName  the value for T_DEPARTMENTS_HOSPITAL.DEPARTMENT_NAME
	 * @mbg.generated  Tue Apr 09 19:02:26 CST 2019
	 */
	public void setDepartmentName(String departmentName) {
		this.departmentName = departmentName == null ? null : departmentName.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_DEPARTMENTS_HOSPITAL.DESCRIPTION
	 * @return  the value of T_DEPARTMENTS_HOSPITAL.DESCRIPTION
	 * @mbg.generated  Tue Apr 09 19:02:26 CST 2019
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_DEPARTMENTS_HOSPITAL.DESCRIPTION
	 * @param description  the value for T_DEPARTMENTS_HOSPITAL.DESCRIPTION
	 * @mbg.generated  Tue Apr 09 19:02:26 CST 2019
	 */
	public void setDescription(String description) {
		this.description = description == null ? null : description.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_DEPARTMENTS_HOSPITAL.IS_DELETE
	 * @return  the value of T_DEPARTMENTS_HOSPITAL.IS_DELETE
	 * @mbg.generated  Tue Apr 09 19:02:26 CST 2019
	 */
	public Integer getIsDelete() {
		return isDelete;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_DEPARTMENTS_HOSPITAL.IS_DELETE
	 * @param isDelete  the value for T_DEPARTMENTS_HOSPITAL.IS_DELETE
	 * @mbg.generated  Tue Apr 09 19:02:26 CST 2019
	 */
	public void setIsDelete(Integer isDelete) {
		this.isDelete = isDelete;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_DEPARTMENTS_HOSPITAL.UPDATER
	 * @return  the value of T_DEPARTMENTS_HOSPITAL.UPDATER
	 * @mbg.generated  Tue Apr 09 19:02:26 CST 2019
	 */
	public Integer getUpdater() {
		return updater;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_DEPARTMENTS_HOSPITAL.UPDATER
	 * @param updater  the value for T_DEPARTMENTS_HOSPITAL.UPDATER
	 * @mbg.generated  Tue Apr 09 19:02:26 CST 2019
	 */
	public void setUpdater(Integer updater) {
		this.updater = updater;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_DEPARTMENTS_HOSPITAL.MOD_TIME
	 * @return  the value of T_DEPARTMENTS_HOSPITAL.MOD_TIME
	 * @mbg.generated  Tue Apr 09 19:02:26 CST 2019
	 */
	public Long getModTime() {
		return modTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_DEPARTMENTS_HOSPITAL.MOD_TIME
	 * @param modTime  the value for T_DEPARTMENTS_HOSPITAL.MOD_TIME
	 * @mbg.generated  Tue Apr 09 19:02:26 CST 2019
	 */
	public void setModTime(Long modTime) {
		this.modTime = modTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_DEPARTMENTS_HOSPITAL.CREATOR
	 * @return  the value of T_DEPARTMENTS_HOSPITAL.CREATOR
	 * @mbg.generated  Tue Apr 09 19:02:26 CST 2019
	 */
	public Integer getCreator() {
		return creator;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_DEPARTMENTS_HOSPITAL.CREATOR
	 * @param creator  the value for T_DEPARTMENTS_HOSPITAL.CREATOR
	 * @mbg.generated  Tue Apr 09 19:02:26 CST 2019
	 */
	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_DEPARTMENTS_HOSPITAL.ADD_TIME
	 * @return  the value of T_DEPARTMENTS_HOSPITAL.ADD_TIME
	 * @mbg.generated  Tue Apr 09 19:02:26 CST 2019
	 */
	public Long getAddTime() {
		return addTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_DEPARTMENTS_HOSPITAL.ADD_TIME
	 * @param addTime  the value for T_DEPARTMENTS_HOSPITAL.ADD_TIME
	 * @mbg.generated  Tue Apr 09 19:02:26 CST 2019
	 */
	public void setAddTime(Long addTime) {
		this.addTime = addTime;
	}
}