<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.department.dao.DepartmentsHospitalGenerateMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.department.model.DepartmentsHospitalGenerate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 15 13:53:39 CST 2019.
    -->
    <id column="DEPARTMENT_ID" jdbcType="INTEGER" property="departmentId" />
    <result column="DEPARTMENT_NAME" jdbcType="VARCHAR" property="departmentName" />
    <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
    <result column="IS_DELETE" jdbcType="TINYINT" property="isDelete" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 15 13:53:39 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 15 13:53:39 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 15 13:53:39 CST 2019.
    -->
    DEPARTMENT_ID, DEPARTMENT_NAME, DESCRIPTION, IS_DELETE, UPDATER, MOD_TIME, CREATOR, 
    ADD_TIME
  </sql>
  <select id="selectByExample" parameterType="com.vedeng.department.model.DepartmentsHospitalGenerateExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 15 13:53:39 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_DEPARTMENTS_HOSPITAL
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 15 13:53:39 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from T_DEPARTMENTS_HOSPITAL
    where DEPARTMENT_ID = #{departmentId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 15 13:53:39 CST 2019.
    -->
    delete from T_DEPARTMENTS_HOSPITAL
    where DEPARTMENT_ID = #{departmentId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.department.model.DepartmentsHospitalGenerateExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 15 13:53:39 CST 2019.
    -->
    delete from T_DEPARTMENTS_HOSPITAL
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.department.model.DepartmentsHospitalGenerate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 15 13:53:39 CST 2019.
    -->
    <selectKey keyProperty="departmentId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_DEPARTMENTS_HOSPITAL (DEPARTMENT_NAME, DESCRIPTION, IS_DELETE, 
      UPDATER, MOD_TIME, CREATOR, 
      ADD_TIME)
    values (#{departmentName,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{isDelete,jdbcType=TINYINT}, 
      #{updater,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, 
      #{addTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.department.model.DepartmentsHospitalGenerate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 15 13:53:39 CST 2019.
    -->
    <selectKey keyProperty="departmentId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_DEPARTMENTS_HOSPITAL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="departmentName != null">
        DEPARTMENT_NAME,
      </if>
      <if test="description != null">
        DESCRIPTION,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="departmentName != null">
        #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.department.model.DepartmentsHospitalGenerateExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 15 13:53:39 CST 2019.
    -->
    select count(*) from T_DEPARTMENTS_HOSPITAL
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <select id="listDepartmentDoByThirdLevelCategoryId" resultMap="BaseResultMap">
    select
      dep.DEPARTMENT_ID, dep.DEPARTMENT_NAME, dep.DESCRIPTION, dep.IS_DELETE, dep.UPDATER, dep.MOD_TIME, dep.CREATOR, dep.ADD_TIME
    from T_DEPARTMENTS_HOSPITAL dep
      left join V_CATEGORY_DEPARTMENT map on dep.DEPARTMENT_ID=map.DEPARTMENT_ID and IS_DELETED=#{deleted,jdbcType=BIT}
      left join V_BASE_CATEGORY ca on map.DEPARTMENT_ID=ca.BASE_CATEGORY_ID and ca.BASE_CATEGORY_LEVEL=3
    where
      ca.BASE_CATEGORY_ID = #{thirdLevelCategoryId, jdbcType=INTEGER}
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 15 13:53:39 CST 2019.
    -->
    update T_DEPARTMENTS_HOSPITAL
    <set>
      <if test="record.departmentId != null">
        DEPARTMENT_ID = #{record.departmentId,jdbcType=INTEGER},
      </if>
      <if test="record.departmentName != null">
        DEPARTMENT_NAME = #{record.departmentName,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        DESCRIPTION = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.isDelete != null">
        IS_DELETE = #{record.isDelete,jdbcType=TINYINT},
      </if>
      <if test="record.updater != null">
        UPDATER = #{record.updater,jdbcType=INTEGER},
      </if>
      <if test="record.modTime != null">
        MOD_TIME = #{record.modTime,jdbcType=BIGINT},
      </if>
      <if test="record.creator != null">
        CREATOR = #{record.creator,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        ADD_TIME = #{record.addTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 15 13:53:39 CST 2019.
    -->
    update T_DEPARTMENTS_HOSPITAL
    set DEPARTMENT_ID = #{record.departmentId,jdbcType=INTEGER},
      DEPARTMENT_NAME = #{record.departmentName,jdbcType=VARCHAR},
      DESCRIPTION = #{record.description,jdbcType=VARCHAR},
      IS_DELETE = #{record.isDelete,jdbcType=TINYINT},
      UPDATER = #{record.updater,jdbcType=INTEGER},
      MOD_TIME = #{record.modTime,jdbcType=BIGINT},
      CREATOR = #{record.creator,jdbcType=INTEGER},
      ADD_TIME = #{record.addTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.department.model.DepartmentsHospitalGenerate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 15 13:53:39 CST 2019.
    -->
    update T_DEPARTMENTS_HOSPITAL
    <set>
      <if test="departmentName != null">
        DEPARTMENT_NAME = #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        DESCRIPTION = #{description,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
    </set>
    where DEPARTMENT_ID = #{departmentId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.department.model.DepartmentsHospitalGenerate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 15 13:53:39 CST 2019.
    -->
    update T_DEPARTMENTS_HOSPITAL
    set DEPARTMENT_NAME = #{departmentName,jdbcType=VARCHAR},
      DESCRIPTION = #{description,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=TINYINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=BIGINT}
    where DEPARTMENT_ID = #{departmentId,jdbcType=INTEGER}
  </update>

  <select id="getDepartmentsHospitalGenerateByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_DEPARTMENTS_HOSPITAL
    where DEPARTMENT_ID IN
    <foreach collection="departmentIds" item="departmentId" separator="," open="(" close=")">
      #{departmentId,jdbcType=INTEGER}
    </foreach>
  </select>
</mapper>