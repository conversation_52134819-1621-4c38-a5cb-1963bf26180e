package com.vedeng.erp.kingdee.service;

import com.vedeng.infrastructure.kingdee.service.KingDeeBaseService;
import com.vedeng.erp.kingdee.dto.KingDeeSaleOutStockDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeSaleOutStockQueryResultDto;

import java.util.List;

/**
 * @description: 销售出库单
 * @author: guoning
 * @date: 2023/2/2 10:00
 **/
public interface KingDeeSaleOutStockService extends KingDeeBaseService<KingDeeSaleOutStockDto> {


    /**
     * 根据ERP出入库单号（OUT_IN_NO）获取金蝶销售出库单
     *
     * @param outInNo ERP出入库单号
     * @return List<KingDeeSaleOutStockQueryResultDto>
     */
    List<KingDeeSaleOutStockQueryResultDto> getKingDeeSaleOutStock(String outInNo);


    List<KingDeeSaleOutStockQueryResultDto> getKingDeeSaleOutStock(String f_qzok_bddjtid, String f_qzok_bddjhid);
}
