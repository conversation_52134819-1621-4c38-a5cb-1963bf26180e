package com.vedeng.erp.finance.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.finance.api.InvoiceFileApi;
import com.vedeng.erp.finance.domain.entity.InvoiceEntity;
import com.vedeng.erp.finance.domain.entity.InvoiceFile;
import com.vedeng.erp.finance.domain.entity.InvoiceVoucherEntity;
import com.vedeng.erp.finance.domain.entity.TransactionVoucherEntity;
import com.vedeng.erp.finance.dto.InvoiceFileDto;
import com.vedeng.erp.finance.dto.VoucherDto;
import com.vedeng.erp.finance.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;


@Service
@Slf4j
public class InvoiceFileServiceImpl implements InvoiceFileApi {


    @Autowired
    private InvoiceFileMapper invoiceFileMapper;

    @Autowired
    private InvoiceMapper invoiceMapper;
    @Autowired
    TransactionVoucherMapper transactionVoucherMapper;
    @Autowired
    InvoiceVoucherMapper invoiceVoucherMapper;


    @Transactional
    @Override
    public boolean handleInvoiceFile(InvoiceFileDto invoiceFileDto) {
        log.info("ocr更新发票附件:{}",invoiceFileDto);
        InvoiceFile invoiceFile = new InvoiceFile();
        invoiceFile.setInvoiceCode(invoiceFileDto.getInvoiceCode());
        invoiceFile.setInvoiceNo(invoiceFileDto.getInvoiceNo());
        invoiceFile.setFileUrl(invoiceFileDto.getFileUrl());
        invoiceFile.setAddTime(new Date());
        invoiceFile.setModTime(new Date());
        invoiceFile.setTag(invoiceFileDto.getTag());
        invoiceFileMapper.insertSelective(invoiceFile);

        String invoiceNo = invoiceFile.getInvoiceNo();
        String invoiceHref = invoiceFile.getFileUrl();
        Integer tag = invoiceFileDto.getTag();
        String ossUrl = invoiceFile.getFileUrl();
        String resourceId = invoiceFile.getFileUrl().split("=")[1];
        long modTime = System.currentTimeMillis();
        invoiceMapper.updateInvoiceOssUrlByOcr(invoiceNo,ossUrl,invoiceHref,resourceId,modTime,tag);
//        int updateInvoiceOssUrlByOcr(@Param("invoiceNo") String invoiceNo, @Param("ossUrl") String ossUrl,@Param("invoiceHref") String invoiceHref
//                ,@Param("resourceId") String resourceId,@Param("modTime") String modTime,@Param("tag") Integer tag);

        return true;
    }

    @Override
    public boolean getInvoiceByInvoiceNoAndTag(String invoiceNo,Integer tag) {
        InvoiceEntity invoiceEntity = invoiceMapper.findByInvoiceNoAndTag(invoiceNo,tag);
        return (invoiceEntity==null )?false:true;
    }




    @Override
    public boolean getInvoiceFileByInvoiceNo(String invoiceNo) {
        InvoiceFile invoiceFile = invoiceFileMapper.getInvoiceFileByInvoiceNo(invoiceNo);
        return invoiceFile==null?false:true;
    }

    @Override
    public VoucherDto judgeTransactionOrInvoice(String voucherDate, String voucherNo) {
        TransactionVoucherEntity transactionVoucher = transactionVoucherMapper.getByVoucherDateAndNo(voucherDate,voucherNo);
        if (ObjectUtil.isNotNull(transactionVoucher)){
            return new VoucherDto(ErpConstant.ONE, transactionVoucher.getTransactionVoucherId());
        }
        InvoiceVoucherEntity invoiceVoucher = invoiceVoucherMapper.getByVoucherDateAndNo(voucherDate,voucherNo);
        if (ObjectUtil.isNotNull(invoiceVoucher)){
            return new VoucherDto(ErpConstant.TWO,invoiceVoucher.getInvoiceVoucherId());
        }
        log.info("没有找到对应的凭证记录,voucherDate:{},voucherNo:{}",voucherDate,voucherNo);
        return null;
    }

    @Override
    public int updateVoucherUrl(VoucherDto voucherDto, String fileUrl,String voucherDate, String voucherNo) {
        if (ErpConstant.ONE.equals(voucherDto.getType())){
            TransactionVoucherEntity updateEntity = new TransactionVoucherEntity();
            updateEntity.setTransactionVoucherId(voucherDto.getId());
            updateEntity.setVoucherUrl(fileUrl);
            updateEntity.setVoucherDate(voucherDate);
            updateEntity.setVoucherNo(voucherNo);
            return transactionVoucherMapper.updateFileUrlByVoucherUrlAndVoucherNo(updateEntity);
        }
        InvoiceVoucherEntity updateInvoice = new InvoiceVoucherEntity();
        updateInvoice.setInvoiceVoucherId(voucherDto.getId());
        updateInvoice.setVoucherUrl(fileUrl);
        updateInvoice.setVoucherDate(voucherDate);
        updateInvoice.setVoucherNo(voucherNo);
        return invoiceVoucherMapper.updateFileUrlByVoucherUrlAndVoucherNo(updateInvoice);
    }
}
