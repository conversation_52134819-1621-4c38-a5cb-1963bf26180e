<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib prefix="tags" tagdir="/WEB-INF/tags"%>

<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>商品分类管理</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/pages/goods/vgoods/goodsLevelAndPosition.css?rnd=${resourceVersionKey}">
</head>

<body>
<div class="erp-wrap">
    <div class="erp-block erp-block-list">
        <div class="erp-title">
            <div class="erp-title-txt" style="font-size: 14px;margin-bottom: 10px;margin-left: 10px;">商品等级概览表：</div>
        </div>
        <div class="list-table">
            <div class="table-th">
                <div class="th" >商品等级</div>
                <div class="th">商品名称</div>
                <div class="th">商品总数</div>
                <div class="th">待完善商品数</div>
                <div class="th">操作</div>
            </div>

            <c:if test="${goodsTodoSummaryVo eq null or empty goodsTodoSummaryVo.goodsLevelTodoList}">
                <div class="table-tr no-data">
                    <div><i class="vd-icon icon-caution1"></i></div>
                    没有匹配的数据
                </div>
            </c:if>
            <c:if test="${goodsTodoSummaryVo ne null and not empty goodsTodoSummaryVo.goodsLevelTodoList}">
                <c:forEach var="goodsLevelTodo" items="${goodsTodoSummaryVo.goodsLevelTodoList}">
                    <div class="table-tr">
                        <div class="tr-lv1 J-item-wrap hidden">
                            <div class="tr-list">
                                <div class="tr-item">
                                    <div class="tr-name-txt">
                                        ${goodsLevelTodo.uniqueIdentifier}
                                    </div>
                                </div>
                                <div class="tr-item">
                                    ${goodsLevelTodo.goodsLevelName}
                                </div>
                                <div class="tr-item">
                                    <a href="javascript:void(0);" tabTitle='{"link":"/goods/vgoods/list.do?goodsLevelNo=${goodsLevelTodo.goodsLevelNo}","title":"新商品流页面"}'> ${goodsLevelTodo.totalCount}</a>
                                </div>
                                <div class="tr-item">
                                    <a href="javascript:void(0);" tabTitle='{"link":"/goods/vgoods/list.do?goodsTodoItemSearchFlag=1&goodsLevelNo=${goodsLevelTodo.goodsLevelNo}","title":"新商品流页面"}'> ${goodsLevelTodo.todoCount}</a>
                                </div>
                                <div class="tr-item">
                                    <div class="view-detail btn">查看</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </c:forEach>
            </c:if>
        </div>
    </div>
    <div class="erp-block erp-block-list">
        <div class="erp-title">
            <div class="erp-title-txt" style="font-size: 14px;margin-bottom: 10px;margin-left: 10px;">商品档位概览表：</div>
        </div>
        <div class="list-table">
            <div class="table-th">
                <div class="th">商品档位</div>
                <div class="th">档位名称</div>
                <div class="th">商品总数</div>
                <div class="th" >A类商品总数</div>
                <div class="th">B类商品总数</div>
                <div class="th">C类商品总数</div>
                <div class="th">D类商品总数</div>
                <div class="th">E类商品总数</div>
                <div class="th">操作</div>
            </div>

            <c:if test="${goodsTodoSummaryVo eq null or empty goodsTodoSummaryVo.goodsLevelTodoList}">
                <div class="table-tr no-data">
                    <div><i class="vd-icon icon-caution1"></i></div>
                    没有匹配的数据
                </div>
            </c:if>
            <c:if test="${goodsTodoSummaryVo ne null and not empty goodsTodoSummaryVo.goodsPositionTodoList}">
                <c:forEach var="goodsPositionTodo" items="${goodsTodoSummaryVo.goodsPositionTodoList}">
                    <div class="table-tr">
                        <div class="tr-lv1 J-item-wrap hidden">
                            <div class="tr-list">
                                <div class="tr-item" width="26%">
                                    <div class="tr-name-txt">
                                        ${goodsPositionTodo.showName}
                                    </div>
                                </div>
                                <div class="tr-item">
                                    ${goodsPositionTodo.goodsPositionName}
                                </div>
                                <div class="tr-item">
                                    <a href="javascript:void(0);" tabTitle='{"link":"/goods/vgoods/list.do?goodsPositionNo=${goodsPositionTodo.goodsPositionNo}","title":"新商品流页面"}'> ${goodsPositionTodo.totalCount}</a>
                                </div>
                                <div class="tr-item">
                                    <a href="javascript:void(0);" tabTitle='{"link":"/goods/vgoods/list.do?goodsPositionNo=${goodsPositionTodo.goodsPositionNo}&goodsLevelNo=1","title":"新商品流页面"}'> ${goodsPositionTodo.levelACount}</a>
                                </div>
                                <div class="tr-item">
                                        <a href="javascript:void(0);" tabTitle='{"link":"/goods/vgoods/list.do?goodsPositionNo=${goodsPositionTodo.goodsPositionNo}&goodsLevelNo=2","title":"新商品流页面"}'> ${goodsPositionTodo.levelBCount}</a>
                                </div>
                                <div class="tr-item">
                                    <a href="javascript:void(0);" tabTitle='{"link":"/goods/vgoods/list.do?goodsPositionNo=${goodsPositionTodo.goodsPositionNo}&goodsLevelNo=3","title":"新商品流页面"}'> ${goodsPositionTodo.levelCCount}</a>
                                </div>
                                <div class="tr-item">
                                    <a href="javascript:void(0);" tabTitle='{"link":"/goods/vgoods/list.do?goodsPositionNo=${goodsPositionTodo.goodsPositionNo}&goodsLevelNo=4","title":"新商品流页面"}'> ${goodsPositionTodo.levelDCount}</a>
                                </div>
                                <div class="tr-item" style="width: 78px" >
                                    <a href="javascript:void(0);" tabTitle='{"link":"/goods/vgoods/list.do?goodsPositionNo=${goodsPositionTodo.goodsPositionNo}&goodsLevelNo=5","title":"新商品流页面"}'> ${goodsPositionTodo.levelECount}</a>
                                </div>
                                <div class="tr-item">
                                    <div class="view-detail btn" >查看</div>
                                </div>
                                </div>
                            </div>
                    </div>
                </c:forEach>
            </c:if>
        </div>
    </div>
</div>


<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/layer.js"></script>
<script type="text/javascript" charset="UTF-8" src='${pageContext.request.contextPath}/static/js/form.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/util.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/select.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/pages/modules/list.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">
    $('.view-detail').click(function () {
        layer.alert('尚未开发，敬请期待',{icon: 6});
    });
</script>
</body>