package com.vedeng.trader.model.vo;

public class RegistrationAttachment {
    /**
     * 供应商注册证ID
     */
    private Integer supplierRegistrationNumberId;
    /**
     * 客户id
     */
    private Integer traderId;
    /**
     * 注册证Id
     */
    private Integer registrationNumberId;
    /**
     * 图片名称
     */
    private String name;
    /**
     * 图片域
     */
    private String domain;
    /**
     * 图片路径
     */
    private String uri;

    /**
     * 附件Id
     */
    private Integer attachmentId;

    public Integer getSupplierRegistrationNumberId() {
        return supplierRegistrationNumberId;
    }

    public void setSupplierRegistrationNumberId(Integer supplierRegistrationNumberId) {
        this.supplierRegistrationNumberId = supplierRegistrationNumberId;
    }

    public Integer getTraderId() {
        return traderId;
    }

    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }

    public Integer getRegistrationNumberId() {
        return registrationNumberId;
    }

    public void setRegistrationNumberId(Integer registrationNumberId) {
        this.registrationNumberId = registrationNumberId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public Integer getAttachmentId() {
        return attachmentId;
    }

    public void setAttachmentId(Integer attachmentId) {
        this.attachmentId = attachmentId;
    }
}
