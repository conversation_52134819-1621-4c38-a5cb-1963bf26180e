package com.wms.service.listenner;

import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.Buyorder;
import com.wms.service.CancelTypeService;
import com.wms.service.LogicalAfterorderChooseService;
import com.wms.service.WMSInterfaceFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 采购换货单确认完成的监听器
 */
@Service
public class PurchaseExgConfirmListener extends AbstractErpListenner{

    public static Logger LOGGER = LoggerFactory.getLogger(PurchaseExgConfirmListener.class);

    @Resource
    private BuyorderMapper buyorderMapper;

    @Autowired
    private WMSInterfaceFactory wmsInterfaceFactory;

    @Resource
    private AfterSalesService afterSalesService;

    @Autowired
    private CancelTypeService cancelTypeService;

    @Autowired
    private LogicalAfterorderChooseService logicalAfterorderChooseService;

    @Override
    protected void doOnActionHappen(Object[] params) throws Exception {


        Integer afterSaleId = (Integer) params[0];

        AfterSales afterSales = afterSalesService.getAfterSalesById(afterSaleId);

        Buyorder buyorder = buyorderMapper.selectByPrimaryKey(afterSales.getOrderId());

        LOGGER.info("采购单:"+buyorder.getBuyorderNo()+"的换货单:"+afterSales.getAfterSalesNo()+"确认完成,start========================");

        LOGGER.info("采购单:"+buyorder.getBuyorderNo()+"的换货单:"+afterSales.getAfterSalesNo()+"确认完成,取消原先的换货单=======");
       /* //撤销换货单
        CancelPoDto cancelPoDto = new CancelPoDto();
        cancelPoDto.setDocNo(afterSales.getAfterSalesNo());
        cancelPoDto.setPoType(WmsInterfaceOrderType.EXG_PURCHASE);
        cancelPoDto.setErpCancelReason("采购售后换货单确认完成,请求取消");


        //取消换货单 TODO Holiis ok
        WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.CANCEL_PO);
        WmsResponse wmsResponse = wmsInterface.request(cancelPoDto);*/
        boolean result = cancelTypeService.cancelExgPurchaseMethod(afterSales.getAfterSalesNo(), "采购售后换货单确认完成,请求取消");
        if(!result){
            throw new Exception("物流正在进行货物操作，请等待作业完成后操作，如紧急请直接联系物流部。");
        }
        logicalAfterorderChooseService.confirmExchangeAfterOrder(afterSales.getAfterSalesId());

        LOGGER.info("采购单:"+buyorder.getBuyorderNo()+"的换货单:"+afterSales.getAfterSalesNo()+"确认完成,取消结果:" + result) ;
    }

}
