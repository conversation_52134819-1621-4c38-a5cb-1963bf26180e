package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchCoreSpuDto;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BatchCoreSpuDtoMapper {
    int deleteByPrimaryKey(Integer spuId);

    int insert(BatchCoreSpuDto record);

    int insertOrUpdate(BatchCoreSpuDto record);

    int insertOrUpdateSelective(BatchCoreSpuDto record);

    int insertSelective(BatchCoreSpuDto record);

    BatchCoreSpuDto selectByPrimaryKey(Integer spuId);

    int updateByPrimaryKeySelective(BatchCoreSpuDto record);

    int updateByPrimaryKey(BatchCoreSpuDto record);

    int updateBatch(List<BatchCoreSpuDto> list);

    int updateBatchSelective(List<BatchCoreSpuDto> list);

    int batchInsert(@Param("list") List<BatchCoreSpuDto> list);
}