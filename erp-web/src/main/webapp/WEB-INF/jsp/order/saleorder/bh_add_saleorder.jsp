<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增备货" scope="application" />
<%@ include file="../../common/common.jsp"%>
<div class="form-list">
    <form method="post" id="addBhSaleorderForm" action="./saveAddBhSaleorder.do" id="form_submit"  onsubmit="return checkAddBhSaleorderForm();">
        <ul>
            <li>
                <div class="form-tips">
                    <lable>申请人</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                    ${username}
                    </div>
                </div>
            </li>
            <li>
                <div class="form-tips">
                    <lable style="color: red">*</lable>
                    <lable>申请原因</lable>
                </div>
                <select class="input-middle" name="prepareReaseonType" id="prepareReaseonType"  onchange="updateReaseon(this)">
                    <option value="-1">请选择</option>
                    <c:forEach var="list" items="${reaseonType}">
                        <option value="${list.sysOptionDefinitionId}">${list.title}</option>
                    </c:forEach>
                </select>
            </li>
                <%--<div class="f_left "  id="prepareCommentsDiv">
                    <div class="form-blanks">
                        <input type="text" class="input-largest" name="prepareComments" id="prepareComments" />
                    </div>
                </div>--%>
            <li  id="prepareCommentsDiv" style="display: none;">
                <div class="infor_name infor_name96">
                    <label></label>
                </div>
                <div class="form-item " >
                    <div class="form-fields form-blanks ">
                        <div class="form-col col-6">
                            <textarea class="input-textarea" name="prepareComments" id="prepareComments"></textarea>
                        </div>
                    </div>
                </div>
            </li>
            <div id="prepareCommentsErr" style="clear:both"></div>
            <li>
                <div class="form-tips">
                    <lable>后期销售方案</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <input type="text" class="input-largest" name="marketingPlan" id="marketingPlan" />
                    </div>
                </div>
            </li>
        </ul>
        <div class="add-tijiao tcenter">
            <button type="submit">下一步</button>
        </div>
    </form>
</div>
<script type="text/javascript" src='<%= basePath %>static/js/order/saleorder/bh_add_saleorder.js?rnd=${resourceVersionKey}'></script>
<%--<script src="/webjars/ezadmin/plugins/layui/layui.js?1"></script>--%>
<%@ include file="../../common/footer.jsp"%>
