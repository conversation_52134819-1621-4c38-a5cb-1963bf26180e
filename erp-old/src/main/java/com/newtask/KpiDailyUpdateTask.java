package com.newtask;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@JobHandler(value="KpiDailyUpdateTask")
public class KpiDailyUpdateTask  extends AbstractJobHandler {

    private static final Logger log = LoggerFactory.getLogger("kpilog");

    private void updateKpiDaily(){
    }

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        XxlJobLogger.log("XXL-JOB, Hello World.");
        updateKpiDaily();
        return SUCCESS;
    }
}
