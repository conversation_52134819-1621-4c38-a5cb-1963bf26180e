package com.vedeng.erp.finance.mapper;
import org.apache.ibatis.annotations.Param;

import com.vedeng.erp.finance.domain.entity.TaxcodeClassificationEntity;
import com.vedeng.erp.finance.dto.TaxClassificationDto;
import com.vedeng.erp.finance.dto.TaxcodeClassificationDto;

import java.util.List;

public interface TaxcodeClassificationEntityMapper {

    int deleteByPrimaryKey(Integer taxcodeClassificationId);


    int insert(TaxcodeClassificationEntity row);


    TaxcodeClassificationEntity selectByPrimaryKey(Integer taxcodeClassificationId);


    List<TaxcodeClassificationEntity> selectAll();


    int updateByPrimaryKey(TaxcodeClassificationEntity row);

    List<TaxcodeClassificationDto> findByAll(TaxcodeClassificationDto param);

    List<TaxcodeClassificationEntity> findByFinalCode(@Param("finalCode")String finalCode);

    List<TaxcodeClassificationDto> findBySkuId(Integer skuId);
}
