package com.vedeng.erp.finance.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;


/**
 * @description 流水结款类型
 * <AUTHOR>
 * @date 2024/1/8 13:50
 **/

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class CapitalBillSettlementTypeEntity extends BaseEntity {
    /**
    * id
    */
    private Integer id;

    /**
    * 流水表id
    */
    private Integer capitalBillId;

    /**
    * 0 系统自动结款
    */
    private Integer type;
}