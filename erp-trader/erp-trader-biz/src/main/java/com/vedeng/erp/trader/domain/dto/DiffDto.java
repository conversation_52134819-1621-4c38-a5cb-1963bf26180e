package com.vedeng.erp.trader.domain.dto;

import com.vedeng.erp.trader.dto.TraderCustomerTagChangeRecordDto;
import lombok.*;

import java.util.function.Function;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/17 17:49
 **/
@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DiffDto {

    private boolean needConvertorCollection;

    private String tagModel;

    private String tag;

    private String newValue;

    private String oldValue;

    private Integer traderId;

    private Integer traderCustomerId;

    private Function<TraderCustomerTagChangeRecordDto, String> method;




}
