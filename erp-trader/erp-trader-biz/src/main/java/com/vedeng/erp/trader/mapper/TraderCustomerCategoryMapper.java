package com.vedeng.erp.trader.mapper;


import com.vedeng.erp.trader.domain.entity.TraderCustomerCategoryEntity;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("newTraderCustomerCategoryMapper")
public interface TraderCustomerCategoryMapper {
	/**
	 * <b>Description:</b><br> 获取子集分类
	 * @param parentId
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年5月11日 上午9:17:10
	 */
	List<TraderCustomerCategoryEntity> getTraderCustomerCategoryByParentId(Integer parentId);
	
	/**
	 * <b>Description:</b><br> 查询分类
	 * @param traderCustomerCategoryId
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年5月16日 下午4:02:26
	 */
	TraderCustomerCategoryEntity getTraderCustomerCategoryById(Integer traderCustomerCategoryId);
	
}