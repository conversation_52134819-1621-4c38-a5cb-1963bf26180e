<!doctype html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>货期修改</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
 </head>
 <body id="RWUZeh75EEs" datasource="erp-datasourcetarget" success_url="reload">
  <div id="APPEND_HEAD" class="append"></div>
  <div class="layui-container">
   <form id="inputForm" method="post" class="layui-form">
    <div class="layui-card" group_name="EZ_DEFAULT_GROUP">
     <div class="layui-card-header">
       EZ_DEFAULT_GROUP 
     </div>
     <div class="layui-card-body">
      <div class="layui-form-item ">
       <label class="layui-form-label">订货号</label>
       <div class="layui-input-block form-group">
       </div>
      </div>
      <div class="layui-form-item "><label class="layui-form-label">省级行政区</label>
       <div class="layui-input-block form-group">
        <input class="form-item" name="PROVINCE_NAME" type="span" value="" item_name="PROVINCE_NAME" placeholder="" style="" alias="" jdbctype="" data="" datatype="" lay_verify="" item_desc="" group_data="" valid_rule="" valid_msg="" item_max_upload="">
       </div>
      </div>
      <div class="layui-form-item ">
       <label class="layui-form-label">地级行政区</label>
       <div class="layui-input-block form-group">
        <input class="form-item" name="CITY_NAME" type="span" value="" item_name="CITY_NAME" placeholder="" style="" alias="" jdbctype="" data="" datatype="" lay_verify="" item_desc="" group_data="" valid_rule="" valid_msg="" item_max_upload="">
       </div>
      </div>
      <div class="layui-form-item "><label class="layui-form-label">发货到货时长（工作日）</label>
       <div class="layui-input-block form-group">
        <input class="form-item" name="DELIVER_TIME" type="text" value="" item_name="DELIVER_TIME" placeholder="" style="" alias="" jdbctype="" data="" datatype="" lay_verify="required|number" item_desc="" group_data="" valid_rule="" valid_msg="" item_max_upload="">
       </div>
      </div>
     </div>
    </div>
   </form>
   <form id="configForm" class="layui-form">
    <div class="layui-card" group_name="表单数据配置">
     <div class="layui-card-header">
       表单数据配置 
     </div>
     <div class="layui-card-body">
     </div>
    </div>
    <div class="layui-form-item "><label class="layui-form-label">初始表达式</label>
     <div class="layui-input-block form-group">
      <pre class="form-item layui-code" id="init" name="INIT_EXPRESS" type="">list=select("SELECT RES.DELIVER_TIME_ID, R2.REGION_NAME AS PROVINCE_NAME,RES.REGION_NAME AS CITY_NAME, RES.DELIVER_TIME
FROM(
SELECT R.REGION_NAME, DT.DELIVER_TIME, DT.AREA_ID, DT.AREA_LEVEL,DT.DELIVER_TIME_ID,R.PARENT_ID
FROM T_DELIVER_TIME DT
LEFT JOIN T_REGION R ON DT.AREA_ID = R.REGION_ID AND DT.AREA_LEVEL = R.REGION_TYPE) RES
LEFT JOIN T_REGION R2 ON RES.PARENT_ID = R2.REGION_ID
	WHERE RES.DELIVER_TIME_ID=${ID} ");
return list.get(0)</pre>
     </div>
    </div>
    <div class="layui-form-item ">
     <label class="layui-form-label">提交表达式</label>
     <div class="layui-input-block form-group">
      <pre class="form-item layui-code" id="submit" name="SUBMIT_EXPRESS" type="">if(isNotBlank("DELIVER_TIME")){
update("update T_DELIVER_TIME SET DELIVER_TIME = ${DELIVER_TIME},MOD_TIME =now()  
 WHERE DELIVER_TIME_ID= ${ID}");
}else{
update("update T_DELIVER_TIME SET DELIVER_TIME =null,MOD_TIME =now()  
 WHERE DELIVER_TIME_ID= ${ID}");
}
update("update T_DELIVER_TIME SET MOD_TIME =now() WHERE DELIVER_TIME_ID= ${ID}");</pre>
     </div>
    </div>
    <div class="layui-form-item ">
     <label class="layui-form-label">删除表达式</label>
     <div class="layui-input-block form-group">
      <pre class="form-item layui-code" id="delete" name="DELETE_EXPRESS" type=""></pre>
     </div>
    </div>
    <div class="layui-form-item "><label class="layui-form-label">分组表达式</label>
     <div class="layui-input-block form-group">
      <pre class="form-item layui-code" name="GROUP_DATA" type=""></pre>
     </div>
    </div>
   </form>
  </div>
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
  <script>
    layui.use(function () {

    })
</script>
  <div id="APPEND_FOOT" class="append">
  </div>
 </body>
</html>