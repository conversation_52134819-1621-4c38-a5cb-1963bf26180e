package com.vedeng.call.dto;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Data
@Setter
@Getter
public class CallStaticDto {
    /** 呼入次数 */
    private int callIn;

    /** 呼入成功次数 */
    private int callSucc;

    /** 外呼次数 */
    private int callOut;

    /** 外呼成功次数 */
    private int callOutSucc;

    /** 通话大于2分钟的总次数 */
    private int callTimeSucc;

    /** 外呼大于2分钟的总次数 */
    private int callOutTimeSucc;

    /** 通话大于1分钟的总次数 */
    private int callTimeOneSucc;

    /** 外呼大于1分钟的总次数 */
    private int callOutTimeOneSucc;

    /** 通话总时长（分钟） */
    private BigDecimal callTime;

    /** 用户名 */
    private String Name;
}
