package com.vedeng.order.dao;

import com.vedeng.order.model.ge.VBuyorderSncodeMaster;

import java.util.List;

public interface VBuyorderSncodeMasterMapper {
    int delGeMasterInfoCollection(Integer buyorderGoodsId);

    int insertSelective(VBuyorderSncodeMaster record);

    VBuyorderSncodeMaster selectByPrimaryKey(VBuyorderSncodeMaster vBuyorderSncodeMaster);

    List<VBuyorderSncodeMaster> getGeMasterInfo(Integer buyorderGoodsId);

    List<VBuyorderSncodeMaster>  checkMasterSno(VBuyorderSncodeMaster vBuyorderSncodeMaster);
}