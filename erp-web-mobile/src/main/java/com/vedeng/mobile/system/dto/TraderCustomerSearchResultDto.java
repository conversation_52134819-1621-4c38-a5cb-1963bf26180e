package com.vedeng.mobile.system.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2023/12/5
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TraderCustomerSearchResultDto {
    private Integer traderId;//交易者ID

    private Integer traderCustomerId;//客户ID

    private String traderName;//客户名称

    private Integer userId;//归属销售

    private Integer customerNature;//客户类型 (ERP客户时自动带入且不能修改，其他必填。465分销466终端)

    private Integer tzCustomer;//是否同舟会员 0 否 1 是

    private Integer tzCustomerLevel;//同舟会员等级 0无等级，不展示 1 金牌 2 银牌

    private Integer customerFrom;//1erp2终端库3天眼查

    private Integer areaId;

    private String areaName;

    private Integer cityId;

    private String cityName;

    private Integer provinceId;

    private String provinceName;



}
