<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>
        编辑价格
    </title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/pages/goods/vgoods/sku/sku_edit.css?rnd=${resourceVersionKey}">
</head>

<body>

<form action="./saveSku.do" id="form_submit" class="J-form" method="POST">

    <div class="form-wrap">
        <div class="form-container base-form form-span-7">

            <div class="form-title">
                订货号：${skuPriceChangeApplyDto.skuNo}   &nbsp;&nbsp; 商品名称：${skuPriceChangeApplyDto.skuName}
            </div>

            <div class="form-title">
                采购成本：${skuPriceChangeApplyDto.middlePrice}
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <a href="${pageContext.request.contextPath}/price/center/trajectory.do?skuId=${skuPriceChangeApplyDto.skuId}"><span style="color: #00b7ee">查看轨迹</span></a>
            </div>


            <div class="form-block form-info">
                <div class="form-block-title">已生效</div>
                <div class="table-wrap">
                    <table class="table table-base">
                        <tbody>
                        <%--<tr>
                            <th>供应商名称</th>
                            <th>采购成本</th>
                            <th>生效时间</th>
                        </tr>--%>
                        <c:forEach var="purchaseDetail" items="${skuPriceChangeApplyDto.effectPurchaseList}">
                            <tr>
                                <td>供应商名称:${purchaseDetail.supplierId}</td>
                                <td>采购成本:${purchaseDetail.purchasePrice}</td>
                                <td>生效时间:${purchaseDetail.purchasePrice}</td>
                            </tr>
                        </c:forEach>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="form-block">
                <div class="form-block-title">本次更新</div>
                <div class="form-item">
                    <div class="form-fields J-sort-wrap" data-index="2">
                        <div class="sort-wrap J-sort-list">
                            <div class="sort-item-wrap th-wrap cf">
                                <div class="sort-item col-1">排序值</div>
                                <div class="sort-item col-2">供应商名称</div>
                                <div class="sort-item col-3">采购成本(元)</div>
                            </div>
                            <c:if test="${not empty command.paramsName3}">
                                <c:forEach items="${command.paramsName3}" var="params" varStatus="status">
                                    <c:if test="${not empty command.paramsName3[status.index] and not empty command.paramsValue3[status.index]}">
                                        <div class="sort-item-wrap J-sort-item cf">
                                            <div class="sort-item col-1 item-center">
                                                <input type="text" name="sort" class="input-text J-sort-num" maxlength="2">
                                            </div>
                                            <div class="sort-item col-2">
                                                <input type="text" name="paramsName3"  value="${command.paramsName3[status.index]}" autocomplete="off" valid-max="30" class="input-text J-sort-name">
                                                <div class="feedback-block"></div>
                                            </div>
                                            <div class="sort-item col-3">
                                                <input type="text" name="paramsValue3" value="${command.paramsValue3[status.index]}" autocomplete="off" valid-max="30" class="input-text J-sort-value">
                                            </div>
                                            <div class="col-1">
                                                <i class="vd-icon icon-recycle J-sort-del" style="display: inline;"></i>
                                            </div>
                                        </div>
                                    </c:if>
                                </c:forEach>
                            </c:if>
                            <div class="sort-item-wrap J-sort-item cf">
                                <div class="sort-item col-1 item-center">
                                    <input type="text" name="sort" class="input-text J-sort-num" maxlength="2">
                                </div>
                                <div class="sort-item col-2">
                                    <input type="text" name="paramsName3" autocomplete="off" valid-max="30" class="input-text J-sort-name">
                                    <div class="feedback-block"></div>
                                </div>
                                <div class="sort-item col-3">
                                    <input type="text" name="paramsValue3" autocomplete="off" valid-max="30" class="input-text J-sort-value">
                                </div>
                                <div class="col-1">
                                    <i class="vd-icon icon-recycle J-sort-del" style="display: inline;"></i>
                                </div>
                            </div>
                        </div>
                        <div class="sort-add J-sort-add-option">
                            <a href="javascript:void(0);" class="sort-add-btn J-sort-add">
                                <i class="vd-icon icon-add"></i>新增供应商采购价
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-block">
                <div class="form-block-title">销售价核价方式</div>
                <div class="form-item">
                    <div class="form-label">核价方式：</div>
                    <div class="form-fields">
                        <div class="input-radio">
                            <label class="input-wrap">
                                <input type="radio" name="returnGoodsConditions" value="1" checked>
                                <span class="input-ctnr"></span>固定报价
                            </label>
                            <%--<label class="input-wrap">
                                <input type="radio" name="returnGoodsConditions" value="0">
                                <span class="input-ctnr"></span>梯度报价
                            </label>--%>
                            <div class="feedback-block" wrapfor="returnGoodsConditions"></div>
                        </div>
                    </div>
                </div>

                <div id="fixPriceDiv">
                    <div class="form-item">
                        <div class="form-label">市场价：</div>
                        <div class="form-fields">
                            <div class="form-col col-6">
                                <input placeholder="请填写市场价" class="input-text" name="freightIntroductions">${skuGenerate.freightIntroductions}</input>
                            </div>
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label">终端价：</div>
                        <div class="form-fields">
                            <div class="form-col col-6">
                                <input placeholder="请填写终端价" class="input-text" name="exchangeGoodsConditions">${skuGenerate.exchangeGoodsConditions}</input>
                            </div>
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label">经销价：</div>
                        <div class="form-fields">
                            <div class="form-col col-6">
                                <input placeholder="请填写经销价" class="input-text" name="exchangeGoodsMethod">${skuGenerate.exchangeGoodsMethod}</input>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <div class="form-btn">
                <div class="form-fields">
                    <button type="submit" class="btn btn-blue btn-large">提交</button>
                </div>
            </div>
        </div>
    </div>
</form>
<script type="text/tmpl" class="J-sort-tmpl">
        <div class="sort-item-wrap J-sort-item cf">
            <div class="sort-item col-1 item-center">
                <input type="text" name="sort" class="input-text J-sort-num" maxlength="2">
            </div>
            <div class="sort-item col-2">
                <input type="text" name="{{=itemName}}" autocomplete="off" valid-max="30" class="input-text J-sort-name" value="{{=name}}">
                <div class="feedback-block"></div>
            </div>
            <div class="sort-item col-3">
                <input type="text" name="{{=itemValue}}" autocomplete="off" valid-max="30" class="input-text J-sort-value" value="{{=value}}">
            </div>
            <div class="col-1">
                <i class="vd-icon icon-recycle J-sort-del"></i>
            </div>
        </div>
    </script>

<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/util.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/select.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/upload.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.validate.js"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/artDialog/2.0.0/artDialog.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/pages/goods/vgoods/sku_edit.js?rnd=${resourceVersionKey}"></script>
</body>

</html>