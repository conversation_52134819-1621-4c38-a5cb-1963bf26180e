package com.vedeng.temporal.workflow.activity;

import io.temporal.activity.ActivityInterface;
import io.temporal.activity.ActivityMethod;

import java.util.List;
import java.util.Map;

/**
 * 公司执行顺序活动接口
 * 定义获取公司执行顺序和流程节点相关的活动方法
 * 基于T_FLOW_NODE表的动态配置实现
 *
 * <AUTHOR>
 * @version 3.1
 * @since 2024-12-20
 */
@ActivityInterface
public interface CompanySequenceActivity {

    /**
     * 根据流转单ID获取公司执行顺序
     * 基于T_FLOW_NODE表查询流转单的公司执行顺序
     *
     * @param flowOrderId 流转单ID（必填）
     * @return 公司代码列表（按执行顺序排序）
     */
    @ActivityMethod
    List<String> getCompanySequenceByFlowOrder(Long flowOrderId);

    /**
     * 获取单个公司的流程节点ID
     *
     * @param businessId 业务ID（实际上是 flowOrderId）
     * @param companyCode 公司代码
     * @return 流程节点ID，如果获取失败则返回null
     * @throws RuntimeException 如果获取过程中发生异常
     */
    @ActivityMethod
    Long getFlowNodeId(String businessId, String companyCode);

    /**
     * 批量获取多个公司的流程节点ID
     * 性能优化方法，减少Activity调用次数
     *
     * @param businessId 业务ID（实际上是 flowOrderId）
     * @param companyCodes 公司代码列表
     * @return 公司代码到流程节点ID的映射，如果某个公司获取失败则对应值为null
     * @throws RuntimeException 如果批量获取过程中发生异常
     */
    @ActivityMethod
    Map<String, Long> getFlowNodeIds(String businessId, List<String> companyCodes);
}
