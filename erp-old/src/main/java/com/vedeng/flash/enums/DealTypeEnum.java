package com.vedeng.flash.enums;

/**
 * @Description:  暂不备货处理方式
 * @Author:       davis
 * @Date:         2021/5/24 上午11:03
 * @Version:      1.0
 */
public enum DealTypeEnum {

    PREPARE_DELETE(0 , "删除"),
    PREPARE_REPLACE(1, "替换"),
    PREPARE_CONVERT(2, "库存转换"),
    PREPARE_NO(3, "暂不备货"),
    PREPARE_STOCK(4, "备货");

    DealTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private Integer code;

    private String msg;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    /**
     * 根据编码获取编码信息
     * @param code
     * @return
     */
    public static String codeToMessage(Integer code){
        if(code == null){
            return "";
        }
        for(DealTypeEnum enums : DealTypeEnum.values()){
            if(code.equals(enums.getCode())){
                return enums.getMsg();
            }
        }
        return "";
    }

}
