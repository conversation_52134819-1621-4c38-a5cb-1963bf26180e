package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchRWarehouseGoodsOutJWarehouseGoodsInDto;
import java.math.BigDecimal;import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2023/1/31
 * @apiNote
 */
public interface BatchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper {
    int deleteByPrimaryKey(Long tRWarehouseGoodsOutJWarehouseGoodsInId);

    int insert(BatchRWarehouseGoodsOutJWarehouseGoodsInDto record);

    int insertOrUpdate(BatchRWarehouseGoodsOutJWarehouseGoodsInDto record);

    int insertOrUpdateSelective(BatchRWarehouseGoodsOutJWarehouseGoodsInDto record);

    int insertSelective(BatchRWarehouseGoodsOutJWarehouseGoodsInDto record);

    BatchRWarehouseGoodsOutJWarehouseGoodsInDto selectByPrimaryKey(Long tRWarehouseGoodsOutJWarehouseGoodsInId);

    int updateByPrimaryKeySelective(BatchRWarehouseGoodsOutJWarehouseGoodsInDto record);

    int updateByPrimaryKey(BatchRWarehouseGoodsOutJWarehouseGoodsInDto record);

    int updateBatch(List<BatchRWarehouseGoodsOutJWarehouseGoodsInDto> list);

    int updateBatchSelective(List<BatchRWarehouseGoodsOutJWarehouseGoodsInDto> list);

    int batchInsert(@Param("list") List<BatchRWarehouseGoodsOutJWarehouseGoodsInDto> list);

    List<BatchRWarehouseGoodsOutJWarehouseGoodsInDto> findByWarehouseGoodsInIdAndWarehouseGoodsInItemIdAndRelationType(@Param("warehouseGoodsInId") Long warehouseGoodsInId, @Param("warehouseGoodsInItemId") Long warehouseGoodsInItemId, @Param("relationType") Integer relationType);

    /**
     * 根据售后单号查询关系表需要的sku
     *
     * @return
     */
    BatchRWarehouseGoodsOutJWarehouseGoodsInDto findSkuByAfterSaleNo(@Param("relateNo") String relateNo, @Param("relatedId") Integer relatedId);

    /**
     * 查找未被绑定的出入库单商品数量
     *
     * @param warehouseGoodsOutItemId
     * @param isOut
     * @return
     */
    BigDecimal getUnboundOutInAmountByItemId(@Param("warehouseGoodsOutItemId") Long warehouseGoodsOutItemId, @Param("isOut") boolean isOut);

    /**
     * 根据入库明细id查询对应的出库明细id
     * @param warehouseGoodsInItemId
     * @return
     */
    List<BatchRWarehouseGoodsOutJWarehouseGoodsInDto> getByWarehouseGoodsInItemId(@Param("warehouseGoodsInItemId") Long warehouseGoodsInItemId);

    List<BatchRWarehouseGoodsOutJWarehouseGoodsInDto>  findByWarehouseGetBatchRWarehouseGoodsOutJWarehouseGoodsInDto( @Param("aftersaleNo") String aftersaleNo,
                                                                                                                      @Param("saleorderNo") String saleorderNo);

}