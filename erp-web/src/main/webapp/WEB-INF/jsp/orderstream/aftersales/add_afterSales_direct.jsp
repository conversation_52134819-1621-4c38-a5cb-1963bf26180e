<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增直发记录" scope="application" />
<%@ include file="../../common/common.jsp"%>

<script type="text/javascript" >
    $('#close-layer').on('click', function() {
        var layerIndex = $("#layerIndex").val();
        layer.close(layerIndex);
    })
    $(function(){
        $("#directform").submit(function(){
            var layerIndex = $("#layerIndex").val();
            checkLogin();
            var remainNum = $("#remainNum").val();
            var num = $("#num").val();
            if(Number(num) > Number(remainNum)){
                warnErrorTips("num","reasonError1","数量之和大于总售后数量");//文本框ID和提示用语
                return false;
            }
            if(num.length == 0 || num == 0 || num < 0){
                warnErrorTips("num","reasonError2","录入数量不可为空或小于等于0");//文本框ID和提示用语
                return false;
            }
            $.ajax({
                url:page_url+'/order/afterSalesCommon/saveDirectStockInfo.do',
                data:$('#directform').serialize(),
                type:"POST",
                dataType : "json",
                success:function(data) {
                    if(data.code==0){
                        layer.alert(data.message);
                        layer.close(layerIndex);
                        window.location.reload();
                    }else{
                        layer.alert(data.message);
                    }

                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });
            return false;
        })
    })
</script>

<div class="form-list form-tips8">
    <form method="post" id="directform" >
        <table class="table  table-style6">
            <thead>
            <th>订货号</th>
            <th>产品名称</th>
            <th>品牌</th>
            <th>制造商型号</th>
            <th>单位</th>
            <c:if test="${type == 1}">
                <th>换出数量</th>
            </c:if>
            <c:if test="${type == 2}">
                <th>入库数量</th>
            </c:if>
            </thead>
            <tbody>
                <td>${afterSalesGoodsVo.sku}</td>
                <td>${afterSalesGoodsVo.goodsName}</td>
                <td>${afterSalesGoodsVo.brandName}</td>
                <td>${afterSalesGoodsVo.model}</td>
                <td>${afterSalesGoodsVo.unitName}</td>
                <td>${afterSalesGoodsVo.rknum - afterSalesGoodsVo.deliveryNum}</td>
            </tbody>
        </table>
        <ul>
            <li>
                <input type="hidden" id="remainNum" value="${afterSalesGoodsVo.rknum - afterSalesGoodsVo.deliveryNum}">
                <%--1出库，2入库--%>
                <c:if test="${type == 1}">
                    <div class="form-tips">
                        <span>*</span>
                        <lable>客户换出数量：</lable>
                    </div>
                </c:if>
                <c:if test="${type == 2}">
                    <div class="form-tips">
                        <span>*</span>
                        <lable>客户寄回数量：</lable>
                    </div>
                </c:if>
                <div class="f_left ">
                    <div class="form-blanks">
                        <input type="number" name="num" id="num" placeholder="请输入本次直发售后的数量" style="width: 200px">
                        <div id="reasonError1" class="font-red " style="display: none">已录入数量和本次数量之和超出总售后数量</div>
                        <div id="reasonError2" class="font-red " style="display: none">数量不可为空或0</div>
                        <input type="hidden" name="type" value="${type}" />
                    </div>
                </div>
            </li>

            <li>
                <div class="form-tips">
                    <lable>生产日期：</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <input type="text" name="goodCreateTimeStr" class="input-smaller Wdate" onFocus="WdatePicker({dateFmt:'yyyy-MM-dd'})" autocomplete="off"
                               value=""/>
                    </div>
                </div>
            </li>
            <li>
                <div class="form-tips">
                    <lable>有效期至：</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <input type="text" name="goodVaildTimeStr" class="input-smaller Wdate" onFocus="WdatePicker({dateFmt:'yyyy-MM-dd'})" autocomplete="off"
                               value=""/>
                    </div>
                </div>
            </li>
            <li>
                <div class="form-tips">
                    <lable>厂家批次号/SN码：</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <input type="text" name="factoryCode" placeholder="请输入退货商品批次号/SN码" style="width: 200px">
                    </div>
                </div>
            </li>
        </ul>
        <div class="add-tijiao tcenter">
            <input type="hidden" name="afterSalesId" value='${afterSalesGoodsVo.afterSalesId}'/>
            <input type="hidden" name="afterSalesGoodsId" value="${afterSalesGoodsVo.afterSalesGoodsId}" />
            <input type="hidden" id="layerIndex">
            <input type="hidden" name="formToken" value="${formToken}"/>
            <button type="submit" id="sub">提交</button>
            <button type="button" class="dele" id="close-layer">取消</button>
        </div>
    </form>
</div>
<%@ include file="../../common/footer.jsp"%>