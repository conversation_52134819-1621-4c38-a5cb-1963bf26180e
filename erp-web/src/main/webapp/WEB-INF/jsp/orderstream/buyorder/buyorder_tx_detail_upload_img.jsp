<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="myfn" uri="/WEB-INF/tld/myfn.tld" %>
<c:set var="title" value="采购详情" scope="application"/>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../../common/common.jsp" %>

<!DOCTYPE html>

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/order/style.css">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/orderstream/buyorder-detail.css">
    <link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">

    <style>
        .block {
            height: 400px;
            background: #ccc;
            margin: 40px 0;
        }
    </style>

    <style>
        a, a:hover, a:focus {
            text-decoration: none;
            outline-style: none;
            color: #3384ef;
            cursor: pointer;
        }
    </style>
    <style>
        .layui-table, .layui-table-view {
             margin: 0px 0;
        }
        .table, .competitive-analysis table {
            border: 1px solid #ddd;
            margin-bottom: 15px;
            border-collapse: collapse;
            border-spacing: 0;
            width: 100%;
            margin: 0 auto 0px auto; !important;
            table-layout: fixed;
            color: #333;
        }
        i {
            display: inline-block;
            height: 12px;
            background: none;
            margin-bottom: -2px;
        }
        .layui-table-cell {
            height: auto ;
            white-space: normal;
        }
        th{
            text-align: center;
        }
    </style>

    <script>
        //json说明
        var data = [
            {
                label: 'XXX', //标签文字
                jump: '#XXX', //需要跳转的元素的ID
                tip: '', //文字下面的备注
                lock: '', //是否需要上面的锁和文字
                status: 3, //当前的状态，默认是0  0：未进行 1：已进行 2：正在进行 3：正在同步进行
                fail: 0 // 是否驳回，0：否 1：是
            }]
    </script>
</head>

<body>
    <%--<input type="hidden" id="expressId" value="${expressId}">--%>
    <div id="file_upload_div" <%--style="display: none"--%> class="<%--text-center--%>" style="margin-top: 5px">
        <form class="layui-form">
            <div class="layui-form-item" >
                <label class="layui-form-label" style="width: 105px;">上传同行单:</label>
                    <%--<input type="text" name="title" required  lay-verify="required" placeholder="请输入标题" autocomplete="off" class="layui-input">--%>
                        <input type="text"  readonly="readonly" class="layui-input-inline" autocomplete="off"  style="height: 36px;">
                        <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" style="height: 32px;" id="test8">浏览</button>
                        <input type="hidden" id="oss" name="oss-file" readonly="readonly" class="layui-input-inline" autocomplete="off"  style="height: 36px;">
            </div>
            <div class="layui-form-item" id="upload" style="text-align: left">
                <label class="layui-form-label" style="width: 105px;"></label>
                <button type="button" class="layui-btn layui-btn-normal layui-btn-sm" id="test10">继续添加</button>
            </div>
            <div class="layui-card-body" style="text-align: left;color: grey">
                1.支持格式: jpg、png、 jpeg<br>
                2.上传文件不能超过20MB
            </div>
            <div class="layui-form-item text-center">
                <%--<button type="button" id="cancel-btn" class="layui-btn layui-btn-primary layui-btn-sm" >取消</button>--%>
                <button type="button" class="layui-btn layui-btn-normal layui-btn-sm " id="test9">提交</button>
            </div>
            <div class="layui-btn-container">
                <div class="layui-form-item">

                </div>
            </div>
        </form>

    </div>
</body>


</html>

<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<script type="text/javascript" src='${pageContext.request.contextPath}/static/js/jquery.min.js'></script>
<script type="text/javascript" src='${pageContext.request.contextPath}/static/js/orderstream/index.js?rnd=${resourceVersionKey}'></script>

<script type="text/javascript"
        src='${pageContext.request.contextPath}/static/js/order/saleorder/saleorder_common.js?rnd=${resourceVersionKey}'></script>

<script>


    layui.use('form', function(){
        var form = layui.form;

        //监听提交
    });
    layui.use('upload',function(){
        var upload =  layui.upload;
        console.log("上传")
        // var value = $("#expressId").val();
        uploadListIns = upload.render({
            elem: '#test8'
            ,url: '/vgoods/operate/fileUploadImg.do' //此处配置你自己的上传接口即可
            // ,data:{expressId:value}
            ,auto: true
            ,multiple: false
            // ,bindAction: '#test9'
            ,accept:'images'
            ,exts:  'jpg|png|jpeg'
            ,size:20480
            ,acceptMime: '.jpg, .png, .jpeg'
            ,choose:function (obj){
                var item = this.item;
                console.log(item.get(0).parentNode.children[1])

                console.log(obj)

                //预读本地文件，如果是多文件，则会遍历。(不支持ie8/9)
                obj.preview(function (index, file, result) {
                    // console.log(index); //得到文件索引
                    // console.log(file); //得到文件对象
                    // console.log(result); //得到文件base64编码，比如图片
                    //获取文件名***************************
                    item.get(0).parentNode.children[1].value = file.name

                    //这里还可以做一些 append 文件列表 DOM 的操作

                    //obj.upload(index, file); //对上传失败的单个文件重新上传，一般在某个事件中使用


                });
            }
            ,done: function(res){
                console.log(res)
                if (res.code == 0) {
                    var item = this.item;
                    console.log(item.get(0).parentNode)
                    item.get(0).parentNode.children[4].value=res.ossUrl
                } else {
                    layer.alert(res.msg);
                }

            }
        });

    })
    var fileUrl=[];
    function getCheckData(){
        fileUrl=[];
        $("input[name='oss-file']").each(function (){
            if ($(this).val()!='') {
                fileUrl.push($(this).val());
            }

        })
        // console.log(fileUrl)
        return fileUrl;
    }
    var itemNum = 1000;
    $(function (){
        $("#cancel-btn").click(function (){
            console.log(parent)
            var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
            // parent.layer.close(index);
        })

        $("#test10").click(function (){
            itemNum=itemNum+1;
            var htm =
                '<div class="layui-form-item" >'+
                '<label class="layui-form-label" style="width: 105px;">上传同行单:</label>'+
                        '<input type="text"  readonly="readonly" class="layui-input-inline" autocomplete="off"  style="height: 36px;">'+
                        '<button type="button" class="layui-btn layui-btn-sm layui-btn-normal " style="height: 32px;" id="myImg'+itemNum+'">浏览</button>&nbsp;&nbsp;&nbsp;'+
                '<input type="hidden" id="oss'+itemNum+'" name="oss-file"  style="height: 36px;">'+
                        '<button type="button" class="layui-btn layui-btn-sm layui-btn-danger mydell "   style="height: 32px;" ">删除</button>'+
                '</div>'
            $("#upload").before(htm)

            var upload =  layui.upload;
            console.log("上传")
            uploadListIns = upload.render({
                elem: '#myImg'+itemNum
                ,url: '/vgoods/operate/fileUploadImg.do' //此处配置你自己的上传接口即可
                // ,data:{expressId:value}
                ,auto: true
                ,multiple: false
                // ,bindAction: '#test9'
                ,accept:'images'
                ,exts:  'jpg|png|jpeg'
                ,acceptMime: '.jpg, .png, .jpeg'
                ,choose:function (obj){
                    //将每次选择的文件追加到文件队列
                    // loadIndex = layer.load(1);


                    // var files = obj.pushFile();
                    console.log(obj)
                    var item = this.item;
                    console.log(item.get(0).parentNode.children[1])

                    //预读本地文件，如果是多文件，则会遍历。(不支持ie8/9)
                    obj.preview(function (index, file, result) {
                        // console.log(index); //得到文件索引
                        // console.log(file); //得到文件对象
                        // console.log(result); //得到文件base64编码，比如图片
                        //获取文件名***************************
                        item.get(0).parentNode.children[1].value = file.name

                        //这里还可以做一些 append 文件列表 DOM 的操作

                        //obj.upload(index, file); //对上传失败的单个文件重新上传，一般在某个事件中使用


                    });
                }
                ,done: function(res){
                    console.log(res)
                    if (res.code == 0) {
                        // layer.msg('上传成功');
                        var item = this.item;
                        console.log(item.get(0).parentNode)
                        item.get(0).parentNode.children[4].value=res.ossUrl
                    } else {
                        layer.alert(res.msg);
                    }

                }
            });
        })

        $("#test9").click(function (){
            debugger
            var checkData = getCheckData();
            if (checkData.length > 0) {
                layer.msg('上传成功', {
                    time: 2000
                }, function () {
                    var checkData = getCheckData();
                    parent.setFileUrls(checkData);
                    var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
                    parent.layer.close(index);
                });
            } else {
                layer.alert('未选择上传同行单图片')
                /*var checkData = getCheckData();
                parent.setFileUrls(checkData);
                var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
                parent.layer.close(index);*/
            }
        })




    })

    $(function(){
        $("body").on("click",".mydell",function(){
            console.log(11)
            console.log($(this))
            $(this).get(0).parentNode.remove()
            console.log()

        })
    })



</script>
<script type="text/javascript" src='${pageContext.request.contextPath}/static/js/orderstream/buyorder/buyorder_tx_detail_upload_excel.js?rnd=${resourceVersionKey}'></script>

