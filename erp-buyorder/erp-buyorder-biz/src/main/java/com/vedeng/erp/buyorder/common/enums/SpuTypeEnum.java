package com.vedeng.erp.buyorder.common.enums;

import lombok.Getter;

/**
 * spu 类型枚举类
 *
 * <AUTHOR>
 * @date 2022/4/14 13:45
 **/
@Getter
public enum SpuTypeEnum {

    EQUIPMENT(316, "设备"),
    CONSUMABLES(317, "耗材"),
    REAGENT(318, "试剂"),
    // spu此时属性作废
    OTHER(319, "其他"),
    // spu此时属性作废
    HIGH_VALUE_CONSUMABLES(653, "高值耗材"),
    PARTS(1008, "配件");

    /**
     * 编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;

    SpuTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


}
