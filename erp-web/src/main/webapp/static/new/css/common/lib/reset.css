@charset "UTF-8";

/*
 * aid brush 颜色最浅 笔刷越大颜色越深
 * [mini, tiny, small, medium, large, big, huge, massive]
 */
/*
 * extend color
 * Roman Numbers
 * i: 1, ii: 2, iii: 3, iv: 4, v: 5, vi: 6, vii: 7, viii: 8, ix: 9, x: 10
 */
html {
    color: #333;
    background: #fff;
}

body,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
code,
form,
fieldset,
legend,
input,
textarea,
blockquote,
th,
td,
hr,
button,
article,
aside,
details,
figcaption,
figure,
footer,
header,
group,
menu,
nav,
section {
    margin: 0;
    padding: 0;
}

/* for html5 tag */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
    display: block;
}

audio,
canvas,
video {
    display: inline-block;
    *display: inline;
    *zoom: 1;
}

body,
button,
input,
select,
textarea {
    font: 12px/1.5 "Microsoft YaHei", Aria<PERSON>, sans-serif;
}

input,
select,
textarea {
    vertical-align: middle;
    color: inherit;
}

button,
input {
    *overflow: visible;
    line-height: normal;
}

::-webkit-input-placeholder {
    /* WebKit browsers */
    color: #999;
}

:-moz-placeholder {
    /* Mozilla Firefox 4 to 18 */
    color: #999;
}

::-moz-placeholder {
    /* Mozilla Firefox 19+ */
    color: #999;
}

:-ms-input-placeholder {
    /* Internet Explorer 10+ */
    color: #999;
}

button {
    cursor: pointer;
}

button::-moz-focus-inner,
input::-moz-focus-inner {
    padding: 0;
    border: 0;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

th {
    text-align: inherit;
}

fieldset,
img {
    border: 0;
}

iframe {
    display: block;
}

blockquote {
    margin: 1em 4em 1em 2em;
    padding: 0.6em 1em;
    background: #f1f1f1;
}

blockquote blockquote {
    padding: 0 0 0 1em;
    margin-left: 2em;
    border-left: 0.4em solid #ddd;
}

acronym,
abbr {
    border-bottom: 1px dotted;
    font-variant: normal;
}

abbr {
    cursor: help;
}

del {
    text-decoration: line-through;
}

address,
caption,
cite,
code,
den,
em,
th,
var {
    font-style: normal;
    font-weight: 500;
}

ul,
ol {
    list-style: none;
}

caption,
th {
    text-align: left;
}

q:before,
q:after {
    content: '';
}

sub,
sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
}

sup {
    top: -0.5em;
}

sub {
    bottom: -0.25em;
}

/* link color */
a {
    color: #2E8AE6;
    text-decoration: none;
}

a:hover {
    color: #FF7733;
}

ins,
a {
    text-decoration: none;
}

small {
    font-size: 11px;
    color: #999;
}

:focus {
    outline: none;
}

/* standard color */
h1,
h2,
h3,
h4,
h5,
h6 {
    font-size: 100%;
    font-weight: bold;
    margin: 0;
}