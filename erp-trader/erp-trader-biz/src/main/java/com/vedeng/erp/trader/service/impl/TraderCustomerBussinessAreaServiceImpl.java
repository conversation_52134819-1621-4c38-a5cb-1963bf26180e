package com.vedeng.erp.trader.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.erp.trader.dto.TraderCustomerBussinessAreaDto;
import com.vedeng.erp.trader.mapper.TraderCustomerBussinessAreaMapper;
import com.vedeng.erp.trader.mapstruct.TraderCustomerBussinessAreaConvertor;
import com.vedeng.erp.trader.service.TraderCustomerBussinessAreaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/11 17:36
 **/
@Service
@Slf4j
public class TraderCustomerBussinessAreaServiceImpl implements TraderCustomerBussinessAreaService {

    @Autowired
    private TraderCustomerBussinessAreaMapper newTraderCustomerBussinessAreaMapper;

    @Autowired
    private TraderCustomerBussinessAreaConvertor traderCustomerBussinessAreaConvertor;

    @Override
    public void addAll(List<TraderCustomerBussinessAreaDto> traderCustomerBussinessAreaDtoList) {

        if (CollUtil.isEmpty(traderCustomerBussinessAreaDtoList)) {
            return;
        }
        newTraderCustomerBussinessAreaMapper.batchInsert(traderCustomerBussinessAreaConvertor.toEntity(traderCustomerBussinessAreaDtoList));

    }

    @Override
    public void deleteByTraderCustomerId(Integer traderCustomerId) {
        if (Objects.isNull(traderCustomerId)) {
            return;
        }
        newTraderCustomerBussinessAreaMapper.deleteByTraderCustomerId(traderCustomerId);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void batchUpdate(List<TraderCustomerBussinessAreaDto> traderCustomerBussinessAreaDtoList) {

        List<Integer> traderCustomerIds = traderCustomerBussinessAreaDtoList.stream().map(TraderCustomerBussinessAreaDto::getTraderCustomerId).collect(Collectors.toList());
        newTraderCustomerBussinessAreaMapper.deleteByTraderCustomerIdList(traderCustomerIds);
        newTraderCustomerBussinessAreaMapper.batchInsert(traderCustomerBussinessAreaConvertor.toEntity(traderCustomerBussinessAreaDtoList));

    }
}
