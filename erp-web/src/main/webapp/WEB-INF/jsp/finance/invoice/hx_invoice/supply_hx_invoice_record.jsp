<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="录票" scope="application"/>
<%@ include file="../../../common/common.jsp" %>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css" media="all">
<link rel="stylesheet"
      href="${pageContext.request.contextPath}/static/new/css/common/global.css">
<link rel="stylesheet"
      href="${pageContext.request.contextPath}/static/new/css/pages/goods/basecategory/view.css?rnd=${resourceVersionKey}">
<style type="text/css">
    #invoiceInfo {
        border-top: 1px solid #B8B8B8;
        border-bottom: 1px solid #B8B8B8;
        border-left: 1px solid #B8B8B8;
        border-right: 1px solid #B8B8B8;
        width: 100%;
        height: 50px;
    }

    .placeholder {
        height: 100px;
        border: 1px dashed #ccc;
    }

    .list-wrap {
        padding-bottom: 50px;
    }

    .search_button {
        text-align: right;
    }

</style>

<div class="search_button">
    <span class="layui-btn" onclick="cleanInvoiceStash(${hxInvoiceVo.hxInvoiceId})">清空</span>
    <span class="layui-btn" onclick="saveInvoiceEntryStash(${hxInvoiceVo.hxInvoiceId})">暂存</span>
    <span class="layui-btn" onclick="saveHxInvoiceDetail(${hxInvoiceVo.hxInvoiceId})">提交</span>
    &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
</div>
<div class="main-container" style='padding-top:15px;'>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                发票信息
            </div>
        </div>
        <div class="table-title" id="invoiceInfo" style="font-size: small">
            供应商名称：${hxInvoiceVo.salerName}
            &nbsp; &nbsp; &nbsp; &nbsp;
            发票号：${hxInvoiceVo.invoiceNum}
            &nbsp; &nbsp; &nbsp; &nbsp;
            发票总额：
            <span id="hxInvoiceAmount">
                <fmt:formatNumber type="number" value="${hxInvoiceVo.amount}" pattern="0.00"
                                  maxFractionDigits="2"/>
            </span>
            &nbsp; &nbsp; &nbsp; &nbsp;
            已录票总额：
            <span id="hxInvoiceRecordedAmount" style="color: #438DEF">
                 <fmt:formatNumber type="number" value="${hxInvoiceVo.recordedAmount}" pattern="0.00"
                                   maxFractionDigits="2"/>
            </span>
            &nbsp; &nbsp; &nbsp; &nbsp;
            剩余可录票金额：
            <c:choose>
                <c:when test="${hxInvoiceVo.canRecordAmount lt 0}">
                    <span id="canRecordAmount" style="color: red">
                        <fmt:formatNumber type="number" value="${hxInvoiceVo.canRecordAmount}" pattern="0.00"
                                          maxFractionDigits="2"/>
                    </span>
                </c:when>
                <c:when test="${hxInvoiceVo.canRecordAmount ge 0}">
                    <span id="canRecordAmount" style="color: #438DEF">
                        <fmt:formatNumber type="number" value="${hxInvoiceVo.canRecordAmount}" pattern="0.00"
                                          maxFractionDigits="2"/>
                    </span>
                </c:when>
            </c:choose>
        </div>
        <input id="hxInvoiceId" type="hidden" value="${hxInvoiceVo.hxInvoiceId}">
    </div>

    <div class="title-container">
        <div class="table-title nobor">
            发票商品明细
        </div>
    </div>

    <div class="table-style5 list-wrap J-list">
        <table class="table">
            <c:if test="${!empty hxInvoiceDetailVos}">
                <c:forEach items="${hxInvoiceDetailVos}" varStatus="status" var="hxInvoiceDetailVo">
                    <tr>
                        <td colspan="10"></td>
                    </tr>
                    <tr style="background-color: #DDDDDD">
                        <th style="width:60px;">序号</th>
                        <th style="width:95px;">产品名称</th>
                        <th style="width:107px;">规格型号</th>
                        <th style="width: 74px;">单位</th>
                        <th style="width: 74px;">数量</th>
                        <th style="width: 146px;">价格（不含税）</th>
                        <th style="width: 146px;">商品总额</th>
                        <th style="width: 146px;">已录票金额</th>
                        <th style="width: 146px;">剩余可录票金额</th>
                        <th style="width: 84px;">操作</th>
                    </tr>
                    <tr class="J-skuno" data-id="${hxInvoiceDetailVo.hxInvoiceDetailId}">
                        <td>
                                ${status.count}
                            <input class="hxInvoiceDetailIdFlag" type="hidden" value="${hxInvoiceDetailVo.hxInvoiceDetailId}">
                        </td>
                        <td>
                                ${hxInvoiceDetailVo.goodsName}
                        </td>
                        <td>
                                ${hxInvoiceDetailVo.specification}
                        </td>
                        <td>
                                ${hxInvoiceDetailVo.unit}
                        </td>
                        <td>
                                ${hxInvoiceDetailVo.quantity}
                        </td>
                        <td>
                            <c:if test="${hxInvoiceDetailVo.price ne null}">
                                <fmt:formatNumber type="number" value="${hxInvoiceDetailVo.price}" pattern="0.00"
                                                  maxFractionDigits="2"/>
                            </c:if>
                        </td>
                        <td id="amount${hxInvoiceDetailVo.hxInvoiceDetailId}">
                            <fmt:formatNumber type="number" value=" ${hxInvoiceDetailVo.amount + hxInvoiceDetailVo.taxAmount}" pattern="0.00"
                                              maxFractionDigits="2"/>
                        </td>
                        <td>
                            <span id="recordAmount${hxInvoiceDetailVo.hxInvoiceDetailId}">
                                    ${hxInvoiceDetailVo.recordedAmount}
                            </span>
                        </td>
                        <td>
                            <span id="canRecordAmount${hxInvoiceDetailVo.hxInvoiceDetailId}"
                                   <c:if test="${hxInvoiceDetailVo.canRecordAmount < 0}">style="color: red" </c:if>>
                                <fmt:formatNumber type="number" value="${hxInvoiceDetailVo.canRecordAmount}" pattern="0.00"
                                                  maxFractionDigits="2"/>
                            </span>
                        </td>
                        <td>
                              <c:choose>
                                  <c:when test="${hxInvoiceVo.invoiceStatus eq 1}">
                                      <a class="pop-new-data" layerparams='{"width":"1400px","height":"750px","title":"选择录票商品",
                        "link":"/supplyChain/invoice/supply_hx_invoice_goods_choice.do?invoiceCategory=${hxInvoiceVo.invoiceCategory}&firstSearch=1&createTime=${hxInvoiceVo.createTime}&invoiceDetailId=${hxInvoiceDetailVo.hxInvoiceDetailId}&hxInvoiceId=${hxInvoiceVo.hxInvoiceId}&salerName=${hxInvoiceVo.salerName}"}'>
                                          录票
                                      </a>
                                  </c:when>
                                  <c:otherwise>
                                      <a href="javascript:layer.alert('只有待录票的发票才能进行录票!');">录票</a>
                                  </c:otherwise>
                              </c:choose>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="10"></td>
                    </tr>
                    <tr style="background-color: #EEEEEE" class="J-title">
                        <th style="width:60px;">订单号</th>
                        <th style="width:95px;">供应商名称</th>
                        <th style="width:107px;">产品名称</th>
                        <th style="width: 74px;">型号</th>
                        <th style="width: 74px;">采购价</th>
                        <th style="width: 146px;">已入库数量</th>
                        <th style="width: 146px;">已录票数量</th>
                        <th style="width: 146px;">本次录票数量</th>
                        <th style="width: 146px;">本次录票金额</th>
                        <th style="width: 84px;">操作</th>
                    </tr>
                    <tr class="J-place" style="height: 5px;">
                        <td colspan="10" style="height: 5px;padding: 0"></td>
                    </tr>
                    <c:if test="${!empty hxInvoiceDetailVo.invoiceEntryStashVos}">
                        <c:forEach items="${hxInvoiceDetailVo.invoiceEntryStashVos}" var="invoiceEntryStashVo">
                            <tr class="J-item" data-id="${invoiceEntryStashVo.invoiceEntryStashId}">
                                <td>
                                        ${invoiceEntryStashVo.buyorderNo}
                                </td>
                                <td>
                                        ${invoiceEntryStashVo.traderName}
                                </td>
                                <td>
                                        ${invoiceEntryStashVo.goodsName}
                                </td>
                                <td>
                                        ${invoiceEntryStashVo.model}
                                </td>
                                <td id="price${invoiceEntryStashVo.invoiceEntryStashId}">
                                        ${invoiceEntryStashVo.price}
                                </td>
                                <td id="arrivalNum_${invoiceEntryStashVo.invoiceEntryStashId}">
                                        ${invoiceEntryStashVo.arrivalNum}
                                </td>
                                <td id="hasEntryCount_${invoiceEntryStashVo.invoiceEntryStashId}">
                                        ${invoiceEntryStashVo.hasEntryCount}
                                </td>
                                <td>
                                    <input id="hasEntryCount${invoiceEntryStashVo.invoiceEntryStashId}"
                                           value="${invoiceEntryStashVo.hasEntryCount}"
                                           onchange="changeRecordNum(${invoiceEntryStashVo.invoiceEntryStashId},
                                               ${hxInvoiceDetailVo.hxInvoiceDetailId})">
                                </td>
                                <td>
                                    <span id="invoiceDetailAmountStash${invoiceEntryStashVo.invoiceEntryStashId}">
                                         <fmt:formatNumber type="number" pattern="0.00"
                                                           maxFractionDigits="2"
                                                           value="${invoiceEntryStashVo.hasEntryCount * invoiceEntryStashVo.price}"/>
                                    </span>
                                </td>
                                <td>
                                    <a>
                                        <font style="color: #438DEF" class="J-item-sort">按住拖动</font>
                                    </a>
                                    </a>
                                    <a>
                                        <font style="color: #438DEF"
                                              onclick="deleteInvoiceEntryStash(${invoiceEntryStashVo.invoiceEntryStashId})">删除</font>
                                    </a>
                                </td>
                            </tr>
                        </c:forEach>
                    </c:if>
                </c:forEach>
            </c:if>
            <c:if test="${empty hxInvoiceDetailVos}">该发票商品明细中暂无商品！</c:if>
        </table>
    </div>
</div>

<script src="${pageContext.request.contextPath}/static/new/js/common/sort.js?rnd=${resourceVersionKey}" charset="utf-8"></script>
<script src="${pageContext.request.contextPath}/static/js/finance/invoice/hx_invoice/supply_hx_invoice_record.js?rnd=${resourceVersionKey}"
        charset="utf-8"></script>