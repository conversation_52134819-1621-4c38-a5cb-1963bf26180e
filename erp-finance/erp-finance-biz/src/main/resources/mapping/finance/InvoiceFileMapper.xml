<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.finance.mapper.InvoiceFileMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.InvoiceFile">
    <!--@mbg.generated-->
    <!--@Table T_INVOICE_FILE-->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="INVOICE_NO" jdbcType="VARCHAR" property="invoiceNo" />
    <result column="INVOICE_CODE" jdbcType="VARCHAR" property="invoiceCode" />
    <result column="TAG" jdbcType="INTEGER" property="tag" />
    <result column="FILE_URL" jdbcType="VARCHAR" property="fileUrl" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, INVOICE_NO, INVOICE_CODE,TAG, FILE_URL, ADD_TIME, MOD_TIME, REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_INVOICE_FILE
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_INVOICE_FILE
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceFile" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE_FILE (INVOICE_NO, INVOICE_CODE, TAG, FILE_URL,
      ADD_TIME, MOD_TIME, REMARK
      )
    values (#{invoiceNo,jdbcType=VARCHAR}, #{invoiceCode,jdbcType=VARCHAR}, #{tag,jdbcType=INTEGER}, #{fileUrl,jdbcType=VARCHAR},
      #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceFile" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE_FILE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceNo != null">
        INVOICE_NO,
      </if>
      <if test="invoiceCode != null">
        INVOICE_CODE,
      </if>
      <if test="tag != null">
        TAG,
      </if>
      <if test="fileUrl != null">
        FILE_URL,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="invoiceNo != null">
        #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceCode != null">
        #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="tag != null">
        #{tag,jdbcType=INTEGER},
      </if>

      <if test="fileUrl != null">
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceFile">
    <!--@mbg.generated-->
    update T_INVOICE_FILE
    <set>
      <if test="invoiceNo != null">
        INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceCode != null">
        INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="tag != null">
        #{tag,jdbcType=INTEGER},
      </if>
      <if test="fileUrl != null">
        FILE_URL = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceFile">
    <!--@mbg.generated-->
    update T_INVOICE_FILE
    set INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR},
      INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR},
      TAG = #{tag,jdbcType=INTEGER},
      FILE_URL = #{fileUrl,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      REMARK = #{remark,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=INTEGER}
  </update>

  <select id="getInvoiceFileByInvoiceNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_INVOICE_FILE
    where INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
    ORDER BY ID ASC
  </select>
</mapper>