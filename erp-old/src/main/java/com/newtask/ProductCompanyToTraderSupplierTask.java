package com.newtask;

import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.firstengage.dao.FirstengageBrandMapper;
import com.vedeng.firstengage.model.ProductCompany;
import com.vedeng.firstengage.model.vo.BrandManufactorMapping;
import com.vedeng.goods.manufacturer.dao.ManufacturerMapper;
import com.vedeng.goods.manufacturer.model.Manufacturer;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.dao.TraderSupplierMapper;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.TraderSupplier;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 品牌关联生产企业转换成供应链生产厂商-关联关系挪移
 *
 * <AUTHOR>
 * @create 2022/2/9 15:47
 */
@Slf4j
@Component
@JobHandler(value = "productCompanyToTraderSupplierTask")
public class ProductCompanyToTraderSupplierTask extends AbstractJobHandler {

    @Autowired
    private FirstengageBrandMapper firstengageBrandMapper;

    @Autowired
    private TraderSupplierMapper traderSupplierMapper;

    @Autowired
    private TraderMapper traderMapper;

    @Autowired
    private ManufacturerMapper manufacturerMapper;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {

        // 先备份所有ProductCompany
        List<ProductCompany> productCompanies = firstengageBrandMapper.getProductCompanyInIds(null);
        productCompanies.stream().forEach(
                productCompany -> {
                    if (Objects.isNull(productCompany.getProductCompanyChineseName())) {
                        return;
                    }
                    {
                        // traderSupplier 新增一份
                        TraderSupplier requestTraderSupplier = traderSupplierMapper.getOneByName(productCompany.getProductCompanyChineseName());
                        if (requestTraderSupplier == null) {
                            Trader trader = new Trader();
                            trader.setCompanyId(1);
                            trader.setIsEnable(ErpConst.ONE);
                            trader.setAddTime(System.currentTimeMillis());
                            trader.setModTime(System.currentTimeMillis());
                            trader.setTraderName(productCompany.getProductCompanyChineseName());
                            traderMapper.insert(trader);
                            TraderSupplier traderSupplier = new TraderSupplier();
                            traderSupplier.setTraderId(trader.getTraderId());
                            traderSupplier.setTraderType(ErpConst.ONE);
                            traderSupplier.setIsEnable(ErpConst.ONE);
                            traderSupplier.setAddTime(System.currentTimeMillis());
                            traderSupplier.setCreator(2);
                            traderSupplier.setModTime(System.currentTimeMillis());
                            traderSupplier.setUpdater(2);
                            traderSupplierMapper.insertSelective(traderSupplier);
                        }
                    }

                    {
                        // manufacture 也新增一份
                        Manufacturer existManufacturer = manufacturerMapper.getOneByName(productCompany.getProductCompanyChineseName());
                        if (Objects.isNull(existManufacturer)) {
                            Manufacturer manufacturer = new Manufacturer();
                            manufacturer.setManufacturerName(productCompany.getProductCompanyChineseName());
                            manufacturerMapper.insertSelective(manufacturer);
                        }
                    }
                }
        );

        List<BrandManufactorMapping> brandManufactorMappings = firstengageBrandMapper.getAllRelatedBrandProductCompany();
        brandManufactorMappings.stream().forEach(brandManufactorMapping -> {

            Integer integer = traderSupplierMapper.getSupplierIdByProductCompanyId(brandManufactorMapping.getProductCompanyId());
            if (integer == null) {
                log.info("× productCompany -- {} 获取 supplier 失败", brandManufactorMapping.getProductCompanyId());
                return;
            }
            log.info("√ productCompany -- {} 对应 supplier -- {}", brandManufactorMapping.getProductCompanyId(), integer);

            BrandManufactorMapping brandManufactorMapping1 = new BrandManufactorMapping();
            brandManufactorMapping1.setBrandId(brandManufactorMapping.getBrandId());
            brandManufactorMapping1.setProductCompanyId(integer);
            BrandManufactorMapping brandManufactorMapping2 = firstengageBrandMapper.getBrandTraderSupplier(brandManufactorMapping1);
            if(brandManufactorMapping2 == null) {
                firstengageBrandMapper.insertBrandTraderSupplier(brandManufactorMapping1);
            }
        });

        return ReturnT.SUCCESS;
    }
}
