package com.newtask.service;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.crm.api.dto.customergroup.TraderGroupEXTDto;
import com.vedeng.crm.api.dto.customergroup.TraderLabelEXTDto;

import java.util.List;

public interface TraderLabelService {
    /**
     * <b>Description:</b>处理客户标签集合计算<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/5/26
     */
    ResultInfo<?> handleTraderLabel(List<TraderLabelEXTDto> traderLabelList, List<Integer> organizations);

    /**
     * <b>Description:</b>处理单个客户标签计算<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/5/26
     */
    ResultInfo handleOneLabel(TraderLabelEXTDto group,List<Integer> organizations);
}
