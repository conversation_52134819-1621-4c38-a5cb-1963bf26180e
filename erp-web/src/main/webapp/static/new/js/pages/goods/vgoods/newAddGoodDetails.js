$(function(){
    //简约、详情切换
    $('.J-page-toggle').click(function(){
        if($(this).hasClass('show')){
            $('.J-detail-simple').css('display', 'flex');
            $('.J-detail-more').hide();
            $(this).removeClass('show').find('.toggle-btn').html();
            $('.page-title').html('简略：');
        }else{
            $('.J-detail-simple').hide();
            $('.J-detail-more').show();
            $(this).addClass('show').find('.toggle-btn').html();
            $('.page-title').html('详情：');
        }
    	var now = getNow();
        $(".mask_div").remove();
        var currentUserName = $("#currentUserName").val();
        watermark({"watermark_txt":currentUserName+now});
    });

    function showViewModeTips() {
        $('.J-page-toggle-tip').show();

        setTimeout(function(){
            $('.J-page-toggle-tip').hide();
        }, 5000)
    }

    //显示5秒模式切换提示气泡
    showViewModeTips();

    var clickTrigger = false;

    //导航
    $('.J-side-wrap').find('.side-item').each(function(i){
        $(this).click(function(){
            clickTrigger = true;
            $('.J-side-wrap').find('.side-item').removeClass('selected');
            $(this).addClass('selected');
            $('html, body').animate({ scrollTop: $('.J-detail-more>.detail-block').eq(i).offset().top});
            setTimeout(function(){
                clickTrigger = false;
            }, 500)
        })
    })

    var sideTop = $('.J-detail-simple').offset().top;

    $(window).scroll(function(){
        var winTop = $(window).scrollTop();
        if (sideTop < winTop){
            $('.J-side-wrap').addClass('onfixed').css('right', 'calc(50% - ' + ($('.detail-wrap').width()/2) + 'px)');
            
        }else{
            $('.J-side-wrap').removeClass('onfixed');
        }

        if (!clickTrigger){
            $('.J-detail-more>.detail-block').each(function (i) {
                if ($(this).offset().top - 40 < winTop) {
                    $('.J-side-wrap').find('.side-item').removeClass('selected');
                    $('.J-side-wrap').find('.side-item').eq(i).addClass('selected');
                }
            })
        }
    })


    //查看大图
    GLOBAL.showLargePic('.J-show-big');

    $(".printAtta").each(function(){
        $(this).click(function(){
            $(this).hide();
            $(this).prev().printArea();
            $(this).show()
        })
    })


    $('.J-show-file').click(function () {
        var targetUrl = $(this).attr('data-src');
        window.open(targetUrl,'_blank');
    });


    UE.getEditor('content',{
        toolbars:[],
        // initialFrameWidth: 730,
        initialFrameHeight: 400,
        autoHeightEnabled: false,
        imageBlockLine: 'center',
        topOffset:0,
        readonly:true,
        wordCount:false
    });

    //去除顿号
    $('.J-spring-filter').each(function(){
        var val = $.trim($(this).html());
        if (val && /、$/.test(val)){
            $(this).html(val.substring(0, val.lastIndexOf('、')));
        }
    })
});


$('.vd-tip-icon.vd-icon.icon-problem1').click(function () {
    var spanName = $(this).next().text();
    //todo 暂时写死，以后建库
    var content;
    if (spanName==='商品等级：') {
        content =$('#goods-level-desc').html();
    }
    if(spanName==='商品档位：'){
        content =$('#goods-position-desc').html();
    }

    if (content == undefined || content === '') {
        return
    }
    artDialog.tip(content,'名词解释',{
        width:1000,
        height:500,
        resize:true,
        padding: '5px 5px'
    });
});

