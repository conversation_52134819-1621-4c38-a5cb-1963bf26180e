package com.vedeng.erp.aftersale.dto;

import lombok.Data;

import java.math.BigDecimal;


/**
 * 仅退票发票暂存信息
 */
@Data
public class AfterBuyorderInvoiceDto {

    /**   AFTER_BUYORDER_INVOICE_ID **/
    private Integer afterBuyorderInvoiceId;

    /** 售后主表ID  AFTER_SALES_ID **/
    private Integer afterSalesId;

    /** 新录入发票号  INVOICE_NO **/
    private String invoiceNo;

    /** 新录入发票代码  INVOICE_CODE **/
    private String invoiceCode;

    /** 是否删除0否1是  IS_DELETE **/
    private Integer isDelete;

    /**
     * 创建者id
     */
    private Integer creator;

    /**
     * 修改者id
     */
    private Integer updater;
    /**
     * 新录入发票类型
     */
    private Integer invoiceType;
    /**
     * 新录入发票税率
     */
    private BigDecimal ratio;
}
