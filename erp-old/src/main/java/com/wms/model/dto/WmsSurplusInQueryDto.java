package com.wms.model.dto;

import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName WmsSurplusInQueryDto.java
 * @Description TODO
 * @createTime 2020年09月21日 14:51:00
 */
public class WmsSurplusInQueryDto {
    /** 入库单号  ORDER_NO **/
    private String orderNo;

    /** 审核状态:0:待审核,1:审核中,2:审核通过,3:审核不通过  VERIFY_STATUS **/
    private Integer verifyStatus;

    /** 入库状态:0-未入库 1-部分入库 2 全部入库  ARRIVAL_STATUS **/
    private Integer arrivalStatus;

    /** 申请入库时间  APPLY_INTIME **/
    private Date applyIntimeStrat;
    private Date applyIntimeEnd;

    /** sku编号  SKU_NO **/
    private String skuNo;

    private String brandName;

    private String goodsName;

    private String model;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getVerifyStatus() {
        return verifyStatus;
    }

    public void setVerifyStatus(Integer verifyStatus) {
        this.verifyStatus = verifyStatus;
    }

    public Integer getArrivalStatus() {
        return arrivalStatus;
    }

    public void setArrivalStatus(Integer arrivalStatus) {
        this.arrivalStatus = arrivalStatus;
    }

    public Date getApplyIntimeStrat() {
        return applyIntimeStrat;
    }

    public void setApplyIntimeStrat(Date applyIntimeStrat) {
        this.applyIntimeStrat = applyIntimeStrat;
    }

    public Date getApplyIntimeEnd() {
        return applyIntimeEnd;
    }

    public void setApplyIntimeEnd(Date applyIntimeEnd) {
        this.applyIntimeEnd = applyIntimeEnd;
    }

    public String getSkuNo() {
        return skuNo;
    }

    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }
}
