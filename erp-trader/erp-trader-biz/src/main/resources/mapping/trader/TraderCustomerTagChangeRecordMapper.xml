<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.TraderCustomerTagChangeRecordMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.TraderCustomerTagChangeRecordEntity">
    <!--@mbg.generated-->
    <!--@Table T_TRADER_CUSTOMER_TAG_CHANGE_RECORD-->
    <id column="TRADER_CUSTOMER_TAG_CHANGE_ID" jdbcType="INTEGER" property="traderCustomerTagChangeId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_CUSTOMER_ID" jdbcType="INTEGER" property="traderCustomerId" />
    <result column="SOURCE" jdbcType="INTEGER" property="source" />
    <result column="OPER_TIME" jdbcType="TIMESTAMP" property="operTime" />
    <result column="OPER_TYPE" jdbcType="INTEGER" property="operType" />
    <result column="TAG_MODEL_NAME" jdbcType="VARCHAR" property="tagModelName" />
    <result column="TAG_NAME" jdbcType="VARCHAR" property="tagName" />
    <result column="OLD_TAG_LABEL" jdbcType="VARCHAR" property="oldTagLabel" />
    <result column="NEW_TAG_LABEL" jdbcType="VARCHAR" property="newTagLabel" />
    <result column="TAG_CHANGE_LOG" jdbcType="VARCHAR" property="tagChangeLog" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    TRADER_CUSTOMER_TAG_CHANGE_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME,
    UPDATER_NAME, TRADER_ID, TRADER_CUSTOMER_ID, `SOURCE`, OPER_TIME, OPER_TYPE, TAG_MODEL_NAME,
    TAG_NAME, OLD_TAG_LABEL, NEW_TAG_LABEL, TAG_CHANGE_LOG
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_TRADER_CUSTOMER_TAG_CHANGE_RECORD
    where TRADER_CUSTOMER_TAG_CHANGE_ID = #{traderCustomerTagChangeId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_TRADER_CUSTOMER_TAG_CHANGE_RECORD
    where TRADER_CUSTOMER_TAG_CHANGE_ID = #{traderCustomerTagChangeId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="TRADER_CUSTOMER_TAG_CHANGE_ID" keyProperty="traderCustomerTagChangeId" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerTagChangeRecordEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_CUSTOMER_TAG_CHANGE_RECORD (ADD_TIME, MOD_TIME, CREATOR,
    UPDATER, CREATOR_NAME, UPDATER_NAME,
    TRADER_ID, TRADER_CUSTOMER_ID, `SOURCE`,
    OPER_TIME, OPER_TYPE, TAG_MODEL_NAME,
    TAG_NAME, OLD_TAG_LABEL, NEW_TAG_LABEL,
    TAG_CHANGE_LOG)
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER},
    #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR},
    #{traderId,jdbcType=INTEGER}, #{traderCustomerId,jdbcType=INTEGER}, #{source,jdbcType=INTEGER},
    #{operTime,jdbcType=TIMESTAMP}, #{operType,jdbcType=INTEGER}, #{tagModelName,jdbcType=VARCHAR},
    #{tagName,jdbcType=VARCHAR}, #{oldTagLabel,jdbcType=VARCHAR}, #{newTagLabel,jdbcType=VARCHAR},
    #{tagChangeLog,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="TRADER_CUSTOMER_TAG_CHANGE_ID" keyProperty="traderCustomerTagChangeId" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerTagChangeRecordEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_CUSTOMER_TAG_CHANGE_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderCustomerId != null">
        TRADER_CUSTOMER_ID,
      </if>
      <if test="source != null">
        `SOURCE`,
      </if>
      <if test="operTime != null">
        OPER_TIME,
      </if>
      <if test="operType != null">
        OPER_TYPE,
      </if>
      <if test="tagModelName != null">
        TAG_MODEL_NAME,
      </if>
      <if test="tagName != null">
        TAG_NAME,
      </if>
      <if test="oldTagLabel != null">
        OLD_TAG_LABEL,
      </if>
      <if test="newTagLabel != null">
        NEW_TAG_LABEL,
      </if>
      <if test="tagChangeLog != null">
        TAG_CHANGE_LOG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderCustomerId != null">
        #{traderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="operTime != null">
        #{operTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operType != null">
        #{operType,jdbcType=INTEGER},
      </if>
      <if test="tagModelName != null">
        #{tagModelName,jdbcType=VARCHAR},
      </if>
      <if test="tagName != null">
        #{tagName,jdbcType=VARCHAR},
      </if>
      <if test="oldTagLabel != null">
        #{oldTagLabel,jdbcType=VARCHAR},
      </if>
      <if test="newTagLabel != null">
        #{newTagLabel,jdbcType=VARCHAR},
      </if>
      <if test="tagChangeLog != null">
        #{tagChangeLog,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerTagChangeRecordEntity">
    <!--@mbg.generated-->
    update T_TRADER_CUSTOMER_TAG_CHANGE_RECORD
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderCustomerId != null">
        TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        `SOURCE` = #{source,jdbcType=INTEGER},
      </if>
      <if test="operTime != null">
        OPER_TIME = #{operTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operType != null">
        OPER_TYPE = #{operType,jdbcType=INTEGER},
      </if>
      <if test="tagModelName != null">
        TAG_MODEL_NAME = #{tagModelName,jdbcType=VARCHAR},
      </if>
      <if test="tagName != null">
        TAG_NAME = #{tagName,jdbcType=VARCHAR},
      </if>
      <if test="oldTagLabel != null">
        OLD_TAG_LABEL = #{oldTagLabel,jdbcType=VARCHAR},
      </if>
      <if test="newTagLabel != null">
        NEW_TAG_LABEL = #{newTagLabel,jdbcType=VARCHAR},
      </if>
      <if test="tagChangeLog != null">
        TAG_CHANGE_LOG = #{tagChangeLog,jdbcType=VARCHAR},
      </if>
    </set>
    where TRADER_CUSTOMER_TAG_CHANGE_ID = #{traderCustomerTagChangeId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerTagChangeRecordEntity">
    <!--@mbg.generated-->
    update T_TRADER_CUSTOMER_TAG_CHANGE_RECORD
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
    MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
    CREATOR = #{creator,jdbcType=INTEGER},
    UPDATER = #{updater,jdbcType=INTEGER},
    CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
    UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
    TRADER_ID = #{traderId,jdbcType=INTEGER},
    TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER},
    `SOURCE` = #{source,jdbcType=INTEGER},
    OPER_TIME = #{operTime,jdbcType=TIMESTAMP},
    OPER_TYPE = #{operType,jdbcType=INTEGER},
    TAG_MODEL_NAME = #{tagModelName,jdbcType=VARCHAR},
    TAG_NAME = #{tagName,jdbcType=VARCHAR},
    OLD_TAG_LABEL = #{oldTagLabel,jdbcType=VARCHAR},
    NEW_TAG_LABEL = #{newTagLabel,jdbcType=VARCHAR},
    TAG_CHANGE_LOG = #{tagChangeLog,jdbcType=VARCHAR}
    where TRADER_CUSTOMER_TAG_CHANGE_ID = #{traderCustomerTagChangeId,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="TRADER_CUSTOMER_TAG_CHANGE_ID" keyProperty="traderCustomerTagChangeId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_CUSTOMER_TAG_CHANGE_RECORD
    (ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, TRADER_ID, TRADER_CUSTOMER_ID,
    `SOURCE`, OPER_TIME, OPER_TYPE, TAG_MODEL_NAME, TAG_NAME, OLD_TAG_LABEL, NEW_TAG_LABEL,
    TAG_CHANGE_LOG)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER},
      #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR},
      #{item.traderId,jdbcType=INTEGER}, #{item.traderCustomerId,jdbcType=INTEGER}, #{item.source,jdbcType=INTEGER},
      #{item.operTime,jdbcType=TIMESTAMP}, #{item.operType,jdbcType=INTEGER}, #{item.tagModelName,jdbcType=VARCHAR},
      #{item.tagName,jdbcType=VARCHAR}, #{item.oldTagLabel,jdbcType=VARCHAR}, #{item.newTagLabel,jdbcType=VARCHAR},
      #{item.tagChangeLog,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <!--auto generated by MybatisCodeHelper on 2023-08-18-->
  <select id="selectByTraderCustomerId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_TRADER_CUSTOMER_TAG_CHANGE_RECORD
    where TRADER_CUSTOMER_ID=#{traderCustomerId,jdbcType=INTEGER}
  </select>
</mapper>