package com.vedeng.kpi.model.VO;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

public class KpiDetailYjOverviewVo extends KpiDetailBaseOverviewVo {

    private static final long serialVersionUID = 8377844503727023173L;

    private Integer userId;

    private String userName;

    private BigDecimal yjAmount;

    private BigDecimal monthTarget;

    private BigDecimal progress;

    private BigDecimal groupAverageProgress;

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public BigDecimal getYjAmount() {
        return yjAmount;
    }

    public void setYjAmount(BigDecimal yjAmount) {
        this.yjAmount = yjAmount;
    }

    public BigDecimal getMonthTarget() {
        return monthTarget;
    }

    public void setMonthTarget(BigDecimal monthTarget) {
        this.monthTarget = monthTarget;
    }

    public BigDecimal getProgress() {
        return progress;
    }

    public void setProgress(BigDecimal progress) {
        this.progress = progress;
    }

    public BigDecimal getGroupAverageProgress() {
        return groupAverageProgress;
    }

    public void setGroupAverageProgress(BigDecimal groupAverageProgress) {
        this.groupAverageProgress = groupAverageProgress;
    }
}
