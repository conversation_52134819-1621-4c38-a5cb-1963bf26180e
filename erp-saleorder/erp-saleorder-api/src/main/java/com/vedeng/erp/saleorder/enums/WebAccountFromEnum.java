package com.vedeng.erp.saleorder.enums;

/**
 * 注册用户 来源
 * 来源1PC端,2M端,3APP端,4APP端(历史原因，原分为ios，安卓),5其它,7自动注册-商机,8自动注册-订单
 * <AUTHOR>
 * @date 2021/12/28 13:46
 **/
public enum WebAccountFromEnum {
    /**
     * 类型
     */
    PC(1,"PC端"),
    M(2,"M端"),
    APP(3,"APP端"),
    APP2(4,"APP端"),
    OTHER(5,"其他"),
    // 兼容前台属性
    AUTO_BUSINESS(9,"自动注册-商机"),
    AUTO_ORDER(8,"自动注册-订单")
    ;

    private Integer code;
    private String desc;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    WebAccountFromEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
