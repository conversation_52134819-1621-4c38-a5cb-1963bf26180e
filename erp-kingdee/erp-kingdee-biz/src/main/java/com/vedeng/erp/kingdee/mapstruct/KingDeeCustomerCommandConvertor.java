package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeeCustomerCommand;
import com.vedeng.erp.kingdee.dto.KingDeeCustomerDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeCustomerDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * dto 转 command
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface KingDeeCustomerCommandConvertor extends BaseCommandMapStruct<KingDeeCustomerCommand, KingDeeCustomerDto> {

    /**
     * 转换
     *
     * @param dto KingDeeCustomerDto
     * @return KingDeeCustomerCommand
     */
    @Mapping(target = "FCUSTID", source = "FCustId")
    @Mapping(target = "FUseOrgId.FNumber", source = "FUseOrgId")
    @Mapping(target = "FCreateOrgId.FNumber", source = "FCreateOrgId")
    @Mapping(target = "FCustTypeId.FNumber", source = "FCustTypeId")
    @Mapping(target = "FTaxRate.FNumber", source = "FTaxRate")
    @Mapping(target = "FTradingCurrid.FNumber", source = "FTradingCurrid")
    @Mapping(target = "FSettleTypeId.FNumber", source = "FSettleTypeId")
    @Mapping(target = "FSaldeptId.FNumber", source = "FSaldeptId")
    @Mapping(target = "f_QZOK_TradeTerms", source = "FQZOKTradeTerms")
    @Mapping(target = "f_QZOK_SYYYGT", source = "FQzokSyyygt")
    @Mapping(target = "f_QZOK_SSJT", source = "FQzokSsjt")
    @Mapping(target = "f_QZOK_XYM", source = "FQzokXym")
    @Mapping(target = "f_QZOK_KHXZTEXT", source = "FQzokKhxztext")
    @Mapping(target = "f_QZOK_ZDKHFLTEXT", source = "FQzokZdkhfltext")
    @Mapping(target = "f_QZOK_YLJGFLTEXT", source = "FQzokYljgfltext")
    @Mapping(target = "f_QZOK_KHXFLTEXT", source = "FQzokKhxfltext")
    @Mapping(target = "f_QZOK_YYDJTEXT", source = "FQzokYydjtext")
    @Mapping(target = "f_QZOK_KHDJ", source = "FQzokKhdj")
    @Mapping(target = "FPROVINCE.FNumber", source = "fprovince")
    @Override
    KingDeeCustomerCommand toCommand(KingDeeCustomerDto dto);


    @Mapping(target = "FBANKCODE", source = "fbankcode")
    @Mapping(target = "FACCOUNTNAME", source = "faccountname")
    @Mapping(target = "FOPENBANKNAME", source = "fopenbankname")
    @Mapping(target = "FCNAPS", source = "fcnaps")
    KingDeeCustomerCommand.KingDeeCustomerDetailCommand toCommand(KingDeeCustomerDetailDto dto);
}

