package com.pricecenter.dto;

import com.vedeng.goods.model.vo.SkuAuthorizationVo;

public class SkuPriceChangeApplyDtoVo extends SkuPriceChangeApplyDto{
    private String unitName;

    /**
     * '是否需报备 0否 1是'
     */
    private Integer isNeedReport;

    /**
     * 是否获得授权 0否 1是
     */
    private  Integer isAuthorized;

    private SkuAuthorizationVo skuAuthorizationVo;

    public Integer getIsNeedReport() {
        return isNeedReport;
    }

    public void setIsNeedReport(Integer isNeedReport) {
        this.isNeedReport = isNeedReport;
    }

    public Integer getIsAuthorized() {
        return isAuthorized;
    }

    public void setIsAuthorized(Integer isAuthorized) {
        this.isAuthorized = isAuthorized;
    }

    public SkuAuthorizationVo getSkuAuthorizationVo() {
        return skuAuthorizationVo;
    }

    public void setSkuAuthorizationVo(SkuAuthorizationVo skuAuthorizationVo) {
        this.skuAuthorizationVo = skuAuthorizationVo;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }
}
