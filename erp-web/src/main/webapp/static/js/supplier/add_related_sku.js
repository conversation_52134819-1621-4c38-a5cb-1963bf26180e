$(function () {
    $("#search").click(function () {
        checkLogin();
        let skuName = $("input[name='skuName']").val();
        let skuNo = $("input[name='skuNo']").val();
        let brandName = $("input[name='brandName']").val();
        let specModel = $("input[name='specModel']").val();

        if (skuName.length > 100) {
            layer.alert("产品名称最多输入100个字符！");
            return false;
        }

        if (skuNo.length > 100) {
            layer.alert("订货号最多输入100个字符！");
            return false;
        }

        if (brandName.length > 100) {
            layer.alert("品牌最多输入100个字符！");
            return false;
        }

        if (specModel.length > 100) {
            layer.alert("规格/型号最多输入100个字符！");
            return false;
        }


        $("#isInit").val(1);
        $("#addRelatedSku").submit();
    });

});

function saveRelatedSku() {
    checkLogin();
    let skuInfoList = [];
    let skuSupplyAuthId = $("input[name='skuSupplyAuthId']").val();
    let userId = $("input[name='userId']").val();
    $.each($("input[name='checkOne']"), function (i, n) {
        if ($(this).prop("checked")) {
            let spuType = $(this).next().val();
            let tempModel = '';
            let tempSpec = '';
            if (spuType == 316 || spuType == 1008) {
                tempModel = $(this).parent().parent().find('td').eq(4).text();
            } else {
                tempSpec = $(this).parent().parent().find('td').eq(4).text();
            }

            debugger;
            let skuInfo = {
                "skuSupplyAuthId": skuSupplyAuthId,
                "skuId": $(this).val(),
                "skuNo": $(this).parent().parent().find('td').eq(1).text(),
                "skuName": $(this).parent().parent().find('td').eq(2).text(),
                "brandName": $(this).parent().parent().find('td').eq(3).text(),
                "spec": tempSpec,
                "model": tempModel,
                "creator": userId
            };
            skuInfoList.push(skuInfo);
        }
    });

    if (skuInfoList.length == 0) {
        layer.alert("请选择要新增关联的商品！");
        return false;
    }

    $.ajax({
        url: './saveAddRelatedSku.do',
        data: {skuInfoListStr: JSON.stringify(skuInfoList)},
        type: "POST",
        dataType: "json",
        success: function (data) {
            if (data.data == 0) {
                layer.alert("当前选中的商品已全部关联此授权书，请重新选择商品关联！");
            } else {
                parent.location.reload();
            }
        },
        error: function (data) {
            if (data.status == 1001) {
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    });
}

function saveDeleteRelatedSku() {
    checkLogin();
    let userId = $("input[name='userId']").val();
    let skuSupplyAuthDetailIdList = "";
    $.each($("input[name='checkOne']"), function (i, n) {
        if ($(this).prop("checked")) {
            skuSupplyAuthDetailIdList = skuSupplyAuthDetailIdList + ',' + $(this).val();
        }
    });

    if (skuSupplyAuthDetailIdList == "") {
        layer.alert("请选择要删除关联的商品！");
        return false;
    }

    $.ajax({
        url: './saveDeleteRelatedSku.do',
        data: {updater: userId, skuSupplyAuthDetailIdList: skuSupplyAuthDetailIdList},
        type: "POST",
        dataType: "json",
        success: function (data) {
            parent.location.reload();
        },
        error: function (data) {
            if (data.status == 1001) {
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    });
}

function selectAll(obj){
    checkLogin();
    if($(obj).is(":checked")){
        $("input[type='checkbox']").not(':disabled').prop("checked",true);
    }else{
        $("input[type='checkbox']").not(':disabled').prop('checked',false);
    }
}
