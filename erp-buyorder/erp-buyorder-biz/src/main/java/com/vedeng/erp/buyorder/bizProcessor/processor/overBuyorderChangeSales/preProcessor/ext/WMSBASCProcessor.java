package com.vedeng.erp.buyorder.bizProcessor.processor.overBuyorderChangeSales.preProcessor.ext;

import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.erp.buyorder.bizProcessor.dto.BizDto;
import com.vedeng.erp.buyorder.bizProcessor.exception.PreProcessorException;
import com.vedeng.erp.buyorder.bizProcessor.processor.overBuyorderChangeSales.preProcessor.OverBuyorderAfterSaleChangeOrderPreProcessor;
import com.wms.service.listenner.PurchaseExgConfirmListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class WMSBASCProcessor extends OverBuyorderAfterSaleChangeOrderPreProcessor {

    @Autowired
    private PurchaseExgConfirmListener purchaseExgConfirmListener;

    @Override
    public void doProcess(BizDto bizDto) throws PreProcessorException {
        log.info("处理采购换货完成前置wms下传处理逻辑,售后单:{}", JSON.toJSONString(bizDto));
        try {
            AfterSalesVo afterSalesVo = (AfterSalesVo) bizDto.getBizData();
            purchaseExgConfirmListener.onActionHappen(afterSalesVo.getAfterSalesId());
        } catch (Exception e) {
            log.error("wms下传处理逻辑报错,售后单:{},错误:{}",JSON.toJSONString(bizDto),e);
            throw new PreProcessorException("WMSBASRProcessor处理报错  wms下发报错");
        }
    }
}
