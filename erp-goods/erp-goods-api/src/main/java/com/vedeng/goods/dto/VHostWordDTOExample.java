package com.vedeng.goods.dto;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class VHostWordDTOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public VHostWordDTOExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andHostWordIdIsNull() {
            addCriterion("HOST_WORD_ID is null");
            return (Criteria) this;
        }

        public Criteria andHostWordIdIsNotNull() {
            addCriterion("HOST_WORD_ID is not null");
            return (Criteria) this;
        }

        public Criteria andHostWordIdEqualTo(Integer value) {
            addCriterion("HOST_WORD_ID =", value, "hostWordId");
            return (Criteria) this;
        }

        public Criteria andHostWordIdNotEqualTo(Integer value) {
            addCriterion("HOST_WORD_ID <>", value, "hostWordId");
            return (Criteria) this;
        }

        public Criteria andHostWordIdGreaterThan(Integer value) {
            addCriterion("HOST_WORD_ID >", value, "hostWordId");
            return (Criteria) this;
        }

        public Criteria andHostWordIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("HOST_WORD_ID >=", value, "hostWordId");
            return (Criteria) this;
        }

        public Criteria andHostWordIdLessThan(Integer value) {
            addCriterion("HOST_WORD_ID <", value, "hostWordId");
            return (Criteria) this;
        }

        public Criteria andHostWordIdLessThanOrEqualTo(Integer value) {
            addCriterion("HOST_WORD_ID <=", value, "hostWordId");
            return (Criteria) this;
        }

        public Criteria andHostWordIdIn(List<Integer> values) {
            addCriterion("HOST_WORD_ID in", values, "hostWordId");
            return (Criteria) this;
        }

        public Criteria andHostWordIdNotIn(List<Integer> values) {
            addCriterion("HOST_WORD_ID not in", values, "hostWordId");
            return (Criteria) this;
        }

        public Criteria andHostWordIdBetween(Integer value1, Integer value2) {
            addCriterion("HOST_WORD_ID between", value1, value2, "hostWordId");
            return (Criteria) this;
        }

        public Criteria andHostWordIdNotBetween(Integer value1, Integer value2) {
            addCriterion("HOST_WORD_ID not between", value1, value2, "hostWordId");
            return (Criteria) this;
        }

        public Criteria andOpHostWordIdIsNull() {
            addCriterion("OP_HOST_WORD_ID is null");
            return (Criteria) this;
        }

        public Criteria andOpHostWordIdIsNotNull() {
            addCriterion("OP_HOST_WORD_ID is not null");
            return (Criteria) this;
        }

        public Criteria andOpHostWordIdEqualTo(Integer value) {
            addCriterion("OP_HOST_WORD_ID =", value, "opHostWordId");
            return (Criteria) this;
        }

        public Criteria andOpHostWordIdNotEqualTo(Integer value) {
            addCriterion("OP_HOST_WORD_ID <>", value, "opHostWordId");
            return (Criteria) this;
        }

        public Criteria andOpHostWordIdGreaterThan(Integer value) {
            addCriterion("OP_HOST_WORD_ID >", value, "opHostWordId");
            return (Criteria) this;
        }

        public Criteria andOpHostWordIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("OP_HOST_WORD_ID >=", value, "opHostWordId");
            return (Criteria) this;
        }

        public Criteria andOpHostWordIdLessThan(Integer value) {
            addCriterion("OP_HOST_WORD_ID <", value, "opHostWordId");
            return (Criteria) this;
        }

        public Criteria andOpHostWordIdLessThanOrEqualTo(Integer value) {
            addCriterion("OP_HOST_WORD_ID <=", value, "opHostWordId");
            return (Criteria) this;
        }

        public Criteria andOpHostWordIdIn(List<Integer> values) {
            addCriterion("OP_HOST_WORD_ID in", values, "opHostWordId");
            return (Criteria) this;
        }

        public Criteria andOpHostWordIdNotIn(List<Integer> values) {
            addCriterion("OP_HOST_WORD_ID not in", values, "opHostWordId");
            return (Criteria) this;
        }

        public Criteria andOpHostWordIdBetween(Integer value1, Integer value2) {
            addCriterion("OP_HOST_WORD_ID between", value1, value2, "opHostWordId");
            return (Criteria) this;
        }

        public Criteria andOpHostWordIdNotBetween(Integer value1, Integer value2) {
            addCriterion("OP_HOST_WORD_ID not between", value1, value2, "opHostWordId");
            return (Criteria) this;
        }

        public Criteria andWordNameIsNull() {
            addCriterion("WORD_NAME is null");
            return (Criteria) this;
        }

        public Criteria andWordNameIsNotNull() {
            addCriterion("WORD_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andWordNameEqualTo(String value) {
            addCriterion("WORD_NAME =", value, "wordName");
            return (Criteria) this;
        }

        public Criteria andWordNameNotEqualTo(String value) {
            addCriterion("WORD_NAME <>", value, "wordName");
            return (Criteria) this;
        }

        public Criteria andWordNameGreaterThan(String value) {
            addCriterion("WORD_NAME >", value, "wordName");
            return (Criteria) this;
        }

        public Criteria andWordNameGreaterThanOrEqualTo(String value) {
            addCriterion("WORD_NAME >=", value, "wordName");
            return (Criteria) this;
        }

        public Criteria andWordNameLessThan(String value) {
            addCriterion("WORD_NAME <", value, "wordName");
            return (Criteria) this;
        }

        public Criteria andWordNameLessThanOrEqualTo(String value) {
            addCriterion("WORD_NAME <=", value, "wordName");
            return (Criteria) this;
        }

        public Criteria andWordNameLike(String value) {
            addCriterion("WORD_NAME like", value, "wordName");
            return (Criteria) this;
        }

        public Criteria andWordNameNotLike(String value) {
            addCriterion("WORD_NAME not like", value, "wordName");
            return (Criteria) this;
        }

        public Criteria andWordNameIn(List<String> values) {
            addCriterion("WORD_NAME in", values, "wordName");
            return (Criteria) this;
        }

        public Criteria andWordNameNotIn(List<String> values) {
            addCriterion("WORD_NAME not in", values, "wordName");
            return (Criteria) this;
        }

        public Criteria andWordNameBetween(String value1, String value2) {
            addCriterion("WORD_NAME between", value1, value2, "wordName");
            return (Criteria) this;
        }

        public Criteria andWordNameNotBetween(String value1, String value2) {
            addCriterion("WORD_NAME not between", value1, value2, "wordName");
            return (Criteria) this;
        }

        public Criteria andOperateStatusIsNull() {
            addCriterion("OPERATE_STATUS is null");
            return (Criteria) this;
        }

        public Criteria andOperateStatusIsNotNull() {
            addCriterion("OPERATE_STATUS is not null");
            return (Criteria) this;
        }

        public Criteria andOperateStatusEqualTo(Integer value) {
            addCriterion("OPERATE_STATUS =", value, "operateStatus");
            return (Criteria) this;
        }

        public Criteria andOperateStatusNotEqualTo(Integer value) {
            addCriterion("OPERATE_STATUS <>", value, "operateStatus");
            return (Criteria) this;
        }

        public Criteria andOperateStatusGreaterThan(Integer value) {
            addCriterion("OPERATE_STATUS >", value, "operateStatus");
            return (Criteria) this;
        }

        public Criteria andOperateStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("OPERATE_STATUS >=", value, "operateStatus");
            return (Criteria) this;
        }

        public Criteria andOperateStatusLessThan(Integer value) {
            addCriterion("OPERATE_STATUS <", value, "operateStatus");
            return (Criteria) this;
        }

        public Criteria andOperateStatusLessThanOrEqualTo(Integer value) {
            addCriterion("OPERATE_STATUS <=", value, "operateStatus");
            return (Criteria) this;
        }

        public Criteria andOperateStatusIn(List<Integer> values) {
            addCriterion("OPERATE_STATUS in", values, "operateStatus");
            return (Criteria) this;
        }

        public Criteria andOperateStatusNotIn(List<Integer> values) {
            addCriterion("OPERATE_STATUS not in", values, "operateStatus");
            return (Criteria) this;
        }

        public Criteria andOperateStatusBetween(Integer value1, Integer value2) {
            addCriterion("OPERATE_STATUS between", value1, value2, "operateStatus");
            return (Criteria) this;
        }

        public Criteria andOperateStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("OPERATE_STATUS not between", value1, value2, "operateStatus");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("ADD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("ADD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("ADD_TIME =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("ADD_TIME <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("ADD_TIME >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("ADD_TIME >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("ADD_TIME <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("ADD_TIME <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("ADD_TIME in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("ADD_TIME not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("ADD_TIME between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("ADD_TIME not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andModeTimeIsNull() {
            addCriterion("MODE_TIME is null");
            return (Criteria) this;
        }

        public Criteria andModeTimeIsNotNull() {
            addCriterion("MODE_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andModeTimeEqualTo(Date value) {
            addCriterion("MODE_TIME =", value, "modeTime");
            return (Criteria) this;
        }

        public Criteria andModeTimeNotEqualTo(Date value) {
            addCriterion("MODE_TIME <>", value, "modeTime");
            return (Criteria) this;
        }

        public Criteria andModeTimeGreaterThan(Date value) {
            addCriterion("MODE_TIME >", value, "modeTime");
            return (Criteria) this;
        }

        public Criteria andModeTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("MODE_TIME >=", value, "modeTime");
            return (Criteria) this;
        }

        public Criteria andModeTimeLessThan(Date value) {
            addCriterion("MODE_TIME <", value, "modeTime");
            return (Criteria) this;
        }

        public Criteria andModeTimeLessThanOrEqualTo(Date value) {
            addCriterion("MODE_TIME <=", value, "modeTime");
            return (Criteria) this;
        }

        public Criteria andModeTimeIn(List<Date> values) {
            addCriterion("MODE_TIME in", values, "modeTime");
            return (Criteria) this;
        }

        public Criteria andModeTimeNotIn(List<Date> values) {
            addCriterion("MODE_TIME not in", values, "modeTime");
            return (Criteria) this;
        }

        public Criteria andModeTimeBetween(Date value1, Date value2) {
            addCriterion("MODE_TIME between", value1, value2, "modeTime");
            return (Criteria) this;
        }

        public Criteria andModeTimeNotBetween(Date value1, Date value2) {
            addCriterion("MODE_TIME not between", value1, value2, "modeTime");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNull() {
            addCriterion("IS_DELETE is null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNotNull() {
            addCriterion("IS_DELETE is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteEqualTo(Integer value) {
            addCriterion("IS_DELETE =", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotEqualTo(Integer value) {
            addCriterion("IS_DELETE <>", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThan(Integer value) {
            addCriterion("IS_DELETE >", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThanOrEqualTo(Integer value) {
            addCriterion("IS_DELETE >=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThan(Integer value) {
            addCriterion("IS_DELETE <", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThanOrEqualTo(Integer value) {
            addCriterion("IS_DELETE <=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIn(List<Integer> values) {
            addCriterion("IS_DELETE in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotIn(List<Integer> values) {
            addCriterion("IS_DELETE not in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteBetween(Integer value1, Integer value2) {
            addCriterion("IS_DELETE between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotBetween(Integer value1, Integer value2) {
            addCriterion("IS_DELETE not between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("CREATOR is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("CREATOR is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(String value) {
            addCriterion("CREATOR =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(String value) {
            addCriterion("CREATOR <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(String value) {
            addCriterion("CREATOR >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(String value) {
            addCriterion("CREATOR >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(String value) {
            addCriterion("CREATOR <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(String value) {
            addCriterion("CREATOR <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLike(String value) {
            addCriterion("CREATOR like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotLike(String value) {
            addCriterion("CREATOR not like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<String> values) {
            addCriterion("CREATOR in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<String> values) {
            addCriterion("CREATOR not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(String value1, String value2) {
            addCriterion("CREATOR between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(String value1, String value2) {
            addCriterion("CREATOR not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNull() {
            addCriterion("UPDATER is null");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNotNull() {
            addCriterion("UPDATER is not null");
            return (Criteria) this;
        }

        public Criteria andUpdaterEqualTo(String value) {
            addCriterion("UPDATER =", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotEqualTo(String value) {
            addCriterion("UPDATER <>", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThan(String value) {
            addCriterion("UPDATER >", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThanOrEqualTo(String value) {
            addCriterion("UPDATER >=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThan(String value) {
            addCriterion("UPDATER <", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThanOrEqualTo(String value) {
            addCriterion("UPDATER <=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLike(String value) {
            addCriterion("UPDATER like", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotLike(String value) {
            addCriterion("UPDATER not like", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterIn(List<String> values) {
            addCriterion("UPDATER in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotIn(List<String> values) {
            addCriterion("UPDATER not in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterBetween(String value1, String value2) {
            addCriterion("UPDATER between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotBetween(String value1, String value2) {
            addCriterion("UPDATER not between", value1, value2, "updater");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
