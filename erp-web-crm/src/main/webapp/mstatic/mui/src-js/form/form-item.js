Vue.component('ui-form-item', {
    template: `
        <div class="form-item" :class="{'vertical': vertical}">
            <div
                class="form-label" 
                :class="{'middle': labelMiddle}"
                :style="{'width': labelWidth}"
            ><span class="must" v-if="must">*</span><span v-html="label"></span></div>
            <div class="form-fields" :class="{'no-padding': noPadding}">
                <slot></slot>
            </div>
        </div>
    `,
    props: {
        label: {
            type: String,
            default: ''
        },
        labelMiddle: {
            type: Boolean,
            default: false,
        },
        must: {
            type: Boolean,
            default: false
        },
        noPadding: {
            type: Boolean,
            default: false
        },
        labelWidth: {
            type: String,
            default: ''
        },
        // 内容是否换行
        vertical: {
            type: Boolean,
            default: false
        },
    },
    mounted() {
        this.label = this.label.replace(/\n/g, '<br/>')
    },
    methods: {
       
    }
})