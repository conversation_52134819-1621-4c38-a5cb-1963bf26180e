<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.system.mapper.UserMapper">

    <select id="getUserByPositTypesAndOrgIdList" resultType="com.vedeng.erp.system.dto.UserDto">
        select
        DISTINCT a.USER_ID,  a.USERNAME, a.PARENT_ID
        from
        T_USER a
        left join
        T_R_USER_POSIT b on a.USER_ID=b.USER_ID
        left join
        T_POSITION d ON d.POSITION_ID = b.POSITION_ID
        left join
        T_USER_DETAIL c on a.USER_ID = c.USER_ID
        <where>
            1=1

            <if test="positionTypes != null and positionTypes.size > 0">
                and d.TYPE IN
                <foreach item="positionType" index="index" collection="positionTypes" open="(" separator="," close=")">
                    #{positionType}
                </foreach>
            </if>
            <if test="companyId != null">
                and a.COMPANY_ID = #{companyId,jdbcType=INTEGER}
            </if>
            <if test="orgIds != null and orgIds.size > 0">
                and d.ORG_ID in
                <foreach item="orgItem" index="index" collection="orgIds" open="(" separator="," close=")">
                    #{orgItem}
                </foreach>
            </if>
            <if test="isDisabled != null">
                and a.IS_DISABLED = #{isDisabled,jdbcType=INTEGER}
            </if>
            ORDER BY a.USERNAME
        </where>
    </select>

    <select id="getAllSubUserList" resultType="com.vedeng.erp.system.dto.UserDto">
        select
        DISTINCT a.USER_ID, a.COMPANY_ID, a.USERNAME, a.NUMBER, a.PASSWORD, a.SALT, a.PARENT_ID, a.IS_DISABLED, a.DISABLED_REASON,
        a.LAST_LOGIN_TIME, a.LAST_LOGIN_IP, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER,
        c.CC_NUMBER
        from
        T_USER a
        left join
        T_R_USER_POSIT b on a.USER_ID=b.USER_ID
        left join
        T_POSITION d ON d.POSITION_ID = b.POSITION_ID
        left join
        T_USER_DETAIL c on a.USER_ID = c.USER_ID
        <where>
            1=1

            <if test="positionTypes != null and positionTypes.size > 0">
                and d.TYPE IN
                <foreach item="positionType" index="index" collection="positionTypes" open="(" separator="," close=")">
                    #{positionType}
                </foreach>
            </if>
            <if test="companyId != null">
                and a.COMPANY_ID = #{companyId,jdbcType=INTEGER}
            </if>
            <if test="isDisabled != null">
                and a.IS_DISABLED = #{isDisabled,jdbcType=INTEGER}
            </if>
            ORDER BY a.USERNAME
        </where>
    </select>

    <select id="getAllUserNotDisabled" resultType="com.vedeng.erp.system.dto.UserDto">
        select
        DISTINCT a.USER_ID, a.COMPANY_ID, a.USERNAME, a.NUMBER,a.PARENT_ID,
        a.LAST_LOGIN_TIME, a.LAST_LOGIN_IP, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER,
        c.CC_NUMBER,c.ALIAS_HEAD_PICTURE
        from
        T_USER a
        left join
        T_USER_DETAIL c on a.USER_ID = c.USER_ID
        where
            1=1 and a.IS_DISABLED = 0
        <if test="name != null and name !=''">
            and a.USERNAME like CONCAT('%',#{name,jdbcType=VARCHAR},'%' )
        </if>
        ORDER BY a.USERNAME ASC
        LIMIT 100
    </select>

    <select id="getUserBaseInfo" resultType="com.vedeng.erp.system.dto.UserDto">
        select
            DISTINCT a.USER_ID, a.COMPANY_ID, a.USERNAME, a.NUMBER,a.PARENT_ID,
                     a.LAST_LOGIN_TIME, a.LAST_LOGIN_IP, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER,
                     c.CC_NUMBER,c.ALIAS_HEAD_PICTURE,c.MOBILE
        from
            T_USER a
                left join
            T_USER_DETAIL c on a.USER_ID = c.USER_ID
        where
            a.USER_ID = #{userId,jdbcType=INTEGER}
    </select>

    <select id="getUserBaseInfoByJobNumber" resultType="com.vedeng.erp.system.dto.UserDto">
        select
        DISTINCT a.USER_ID, a.COMPANY_ID, a.USERNAME, a.NUMBER,a.PARENT_ID,
        a.LAST_LOGIN_TIME, a.LAST_LOGIN_IP, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER,
        c.CC_NUMBER,c.ALIAS_HEAD_PICTURE,c.MOBILE
        from
        T_USER a
        left join
        T_USER_DETAIL c on a.USER_ID = c.USER_ID
        where
        a.NUMBER = #{jobNumber,jdbcType=VARCHAR}
        and a.IS_DISABLED = 0
        limit 1
    </select>

    <select id="getUserInfoByUserIds" resultType="com.vedeng.erp.system.dto.UserDto">
        select
        A.*,C.*,D.REAL_NAME,D.ALIAS_HEAD_PICTURE
        from T_USER A
        LEFT JOIN T_R_USER_POSIT B ON A.USER_ID = B.USER_ID
        LEFT JOIN T_POSITION C ON B.POSITION_ID = C.POSITION_ID
        LEFT JOIN T_USER_DETAIL D ON A.USER_ID = D.USER_ID
        where
        A.USER_ID in
        <foreach item="userId" index="index" collection="list" open="(" separator="," close=")">
            #{userId,jdbcType=INTEGER}
        </foreach>
        group by A.USER_ID,A.USERNAME ORDER BY A.USERNAME
    </select>

    <select id="getUserByUserId" resultType="com.vedeng.erp.system.dto.UserDto">
        SELECT
            A.*,C.*,D.REAL_NAME,D.SEX,D.MOBILE,D.TELEPHONE,E.*
        FROM
            T_USER A
        LEFT JOIN T_R_USER_POSIT B ON A.USER_ID = B.USER_ID
        LEFT JOIN T_POSITION C ON B.POSITION_ID = C.POSITION_ID
        LEFT JOIN T_ORGANIZATION E ON C.ORG_ID = E.ORG_ID
        LEFT JOIN T_USER_DETAIL D ON A.USER_ID = D.USER_ID
        WHERE
            A.USER_ID = #{userId,jdbcType=INTEGER}
        GROUP BY A.USER_ID
    </select>

    <select id="getUserByOrgIdsAndPositon" resultType="com.vedeng.erp.system.dto.UserDto">
        select a.USER_ID,a.USERNAME
        from T_USER a
        left join T_R_USER_POSIT b on a.USER_ID = b.USER_ID
        left join T_POSITION c on c.POSITION_ID = b.POSITION_ID
        left join T_ORGANIZATION d on d.ORG_ID = c.ORG_ID
        <where>
            <if test="positionTypes != null and positionTypes.size > 0">
                and c.TYPE IN
                <foreach item="positionType" index="index" collection="positionTypes" open="(" separator="," close=")">
                    #{positionType}
                </foreach>
            </if>
            and a.COMPANY_ID =1
            <if test="isDisabled != null">
                and a.IS_DISABLED = #{isDisabled,jdbcType=INTEGER}
            </if>
            <if test="orgIds != null and orgIds.size > 0">
                and c.ORG_ID IN
                <foreach item="org" index="index" collection="orgIds" open="(" separator="," close=")">
                    #{org,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
        ORDER BY a.USERNAME
    </select>

    <select id="getUserByTraderId" resultType="com.vedeng.erp.system.dto.UserDto">
        select
        DISTINCT a.USER_ID, a.COMPANY_ID, a.USERNAME, a.NUMBER, a.PASSWORD, a.SALT, a.PARENT_ID, a.IS_DISABLED, a.DISABLED_REASON,
        a.LAST_LOGIN_TIME, a.LAST_LOGIN_IP, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER,UD.REAL_NAME,UD.MOBILE
        from
        T_USER a
        left join T_R_TRADER_J_USER tr on tr.USER_ID =a.USER_ID
        left join T_USER_DETAIL UD ON a.USER_ID=UD.USER_ID
        where
        tr.TRADER_ID = #{traderId,jdbcType=INTEGER}
        and tr.TRADER_TYPE=1
        LIMIT 1
    </select>
    <select id="getAllValidUserByCompanyId" resultType="com.vedeng.erp.system.dto.UserDto">
        SELECT * FROM T_USER WHERE COMPANY_ID = #{companyId,jdbcType=INTEGER} AND IS_DISABLED = 0
    </select>

    <select id="getAllSubordinateByUserIdForVisit" resultType="java.lang.Integer">
        <![CDATA[
        SELECT TORG.USER_ID
        FROM (
        SELECT @ids AS ids,
        (SELECT @ids := GROUP_CONCAT(USER_ID)
        FROM T_USER
        WHERE FIND_IN_SET(PARENT_ID, @ids)) AS cids,
        @l := @l + 1 AS LEVEL
        FROM T_USER,
        (SELECT @ids := #{userId,jdbcType=INTEGER}, @l := 0) b
        WHERE @ids IS NOT NULL AND @L < 10
        ) ID,
        T_USER TORG
        WHERE FIND_IN_SET(TORG.USER_ID, ID.ids) AND TORG.IS_DISABLED=0
        ORDER BY ID.LEVEL
        ]]>
    </select>



    <select id="getAllUserByCompanyId" resultType="com.vedeng.erp.system.dto.UserDto">
        SELECT * FROM T_USER WHERE COMPANY_ID = #{companyId,jdbcType=INTEGER}
    </select>

    <select id="getUserByTraderIdList" resultType="com.vedeng.erp.system.dto.UserDto">
        SELECT B.*, A.TRADER_ID
        FROM T_R_TRADER_J_USER A LEFT JOIN T_USER B ON A.USER_ID = B.USER_ID
        WHERE A.TRADER_TYPE = 1 AND A.TRADER_ID in
        <foreach collection="traderIdList" item="traderId" index="index"
                 open="(" close=")" separator=",">
            #{traderId,jdbcType=INTEGER}
        </foreach>
    </select>

    <select id="getUserListByPositionId" resultType="java.lang.String">
        select TU.USERNAME
        from T_POSITION TP
        JOIN T_R_USER_POSIT TRP ON TP.POSITION_ID = TRP.POSITION_ID
        JOIN T_USER TU
        ON TU.USER_ID = TRP.USER_ID
        WHERE TP.POSITION_ID = #{positionId,jdbcType=INTEGER}
        AND TU.IS_DISABLED = 0
    </select>
    <select id="getUserByPositionType" resultType="com.vedeng.erp.system.dto.UserDto">
        SELECT
            tu.*
        FROM
            T_USER tu
                LEFT JOIN T_R_USER_POSIT trup ON
                tu.USER_ID = trup.USER_ID
                LEFT JOIN T_POSITION tp ON
                tp.POSITION_ID = trup.POSITION_ID
        WHERE
            tp.`TYPE` = #{positionType,jdbcType=INTEGER}
          AND tu.IS_DISABLED = 0
          group by tu.USER_ID
    </select>

    <select id="getUserByNumber" resultType="com.vedeng.erp.system.dto.UserDto">
        SELECT
        A.*,C.*,D.REAL_NAME,D.MOBILE,D.TELEPHONE,E.*
        FROM
        T_USER A
        LEFT JOIN T_R_USER_POSIT B ON A.USER_ID = B.USER_ID
        LEFT JOIN T_POSITION C ON B.POSITION_ID = C.POSITION_ID
        LEFT JOIN T_ORGANIZATION E ON C.ORG_ID = E.ORG_ID
        LEFT JOIN T_USER_DETAIL D ON A.USER_ID = D.USER_ID
        WHERE
        A.NUMBER = #{jobNumber,jdbcType=INTEGER}
        GROUP BY A.USER_ID
    </select>


    <select id="getUserByRoleName" resultType="com.vedeng.erp.system.dto.UserDto">
        SELECT TU.USERNAME,
               TU.USER_ID,
               TD.ORG_ID
        from T_USER TU
                 LEFT JOIN T_R_USER_ROLE TRUR on TU.USER_ID = TRUR.USER_ID
                 LEFT JOIN T_ROLE TR on TRUR.ROLE_ID = TR.ROLE_ID
                 LEFT JOIN T_R_USER_POSIT TRUP on TU.USER_ID = TRUP.USER_ID
                 LEFT JOIN T_POSITION TP on TRUP.POSITION_ID = TP.POSITION_ID
                 LEFT JOIN T_ORGANIZATION TD on TP.ORG_ID = TD.ORG_ID
        where TR.ROLE_NAME = #{roleName,jdbcType=VARCHAR}
    </select>

    <select id="getUserBynumberList" resultType="com.vedeng.erp.system.dto.UserDto">
        SELECT A.*
        FROM T_USER A
        WHERE
        A.NUMBER in
        <foreach collection="numberList" item="number" index="index"
                 open="(" close=")" separator=",">
            #{number,jdbcType=INTEGER}
        </foreach>
    </select>


</mapper>
