<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-3.0.xsd 
		http://www.springframework.org/schema/tx 
		http://www.springframework.org/schema/tx/spring-tx-3.0.xsd">

    <!-- 数据源 -->
    <bean name="dataSourceTarget" class="com.alibaba.druid.pool.DruidDataSource" init-method="init" destroy-method="close">
        <property name="url" value="****************************************************************************************************************************"/>
        <property name="username" value="devuser"/>
        <property name="password" value="devuser!@#$qwer"/>

<!--        <property name="url" value="***************************************************************************************************************************" />-->
<!--        <property name="username" value="fatread" />-->
<!--        <property name="password" value="fatread" />-->
        <property name="minIdle" value="5"/>
        <property name="maxActive" value="10"/>
        <property name="maxWait" value="60000"/>
    </bean>

    <!-- SqlSessionFactory -->
    <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <!-- 引用上面已经配置好的数据库连接池 -->
        <property name="dataSource" ref="dataSourceTarget"/>
        <property name="configLocation" value="classpath:configuration.xml"/>
        <!-- mapper配置路径 -->
        <property name="mapperLocations" value="classpath:mapping/*/*.xml"/>
    </bean>

    <!-- Mapper扫描配置 -->
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.vedeng.*.dao,com.smallhospital.dao,com.wms.dao,com.wms.*.dao"/>
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"/>
    </bean>

    <!-- Mybatis事务管理配置 -->
    <bean id="transactionManager"
          class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dataSourceTarget"/>
    </bean>

    <!-- 事务控制 -->
    <tx:annotation-driven proxy-target-class="true"/>
</beans>