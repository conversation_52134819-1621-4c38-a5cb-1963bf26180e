package com.vedeng.trader.model.vo;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/21 18:56
 * @describe 推送前台实体
 */
public class TraderReception {
    private Integer traderId;    //客户id
    private Integer parentTraderId;// 客户父级id(分公司对应总公司的id,若为总公司可为null)
    private String traderName; //客户名称
    private Integer traderType;//客户类别，0 总公司，1 分公司

    public Integer getTraderId() {
        return traderId;
    }

    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }

    public Integer getParentTraderId() {
        return parentTraderId;
    }

    public void setParentTraderId(Integer parentTraderId) {
        this.parentTraderId = parentTraderId;
    }

    public String getTraderName() {
        return traderName;
    }

    public void setTraderName(String traderName) {
        this.traderName = traderName;
    }

    public Integer getTraderType() {
        return traderType;
    }

    public void setTraderType(Integer traderType) {
        this.traderType = traderType;
    }
}
