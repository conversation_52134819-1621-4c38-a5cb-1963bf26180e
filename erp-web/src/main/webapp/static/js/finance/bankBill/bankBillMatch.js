//金额变动验证
function check_pay(obj,residue_money,checkout_money,now_money){
	var reg1 = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
	if($(obj).val().length>0 && !reg1.test($(obj).val())){
		layer.alert("结款金额输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
		$(obj).val(now_money);
	}
	if($(obj).val()>residue_money || $(obj).val()>checkout_money){
		if($(obj).val()<=residue_money && $(obj).val()>checkout_money){
			layer.confirm("超出可结款金额确认要结款？", {
				  btn: ['确定','取消'] //按钮
				}, function(index){
					layer.close(index);
				}, function(index){
					$(obj).val(now_money);
			});

		}else{
			layer.alert("超出可结款金额");
			$(obj).val(now_money);
		}
	}
}
//异步忽略操作
function ignoreadd(obj,id,type){
	checkLogin();
	var ignoreBanktag = $("#ignoreBankTag").val();
	var matchedObject = $("#matchedObject").val();
	$.ajax({
		async : false,
		url : page_url + '/finance/bankbill/editBankBill.do',
		data : {
			"bankBillId" : id,
			"status":1,
			"matchedObject" : matchedObject
		},
		type : "POST",
		dataType : "json",
		success : function(data) {
			if(data.code == 0){
				console.log(ignoreBanktag);
				if(ignoreBanktag == 4 || ignoreBanktag == 5){
					var currentUrl = parent.$("#currentUrl").val();
					console.log(currentUrl);
					parent.location.href=currentUrl;
				}else{
					parent.layer.close(index);
					parent.location.reload();
				}
			}else{
				layer.alert(data.message, { icon : 2});
			}
		},error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	})
}

function addCapitalBill(id, saleorderId, payer, amount, comments,traderSubject, receivedAmount, existingParent, formToken ) {
	$.ajax({
		async : false,
		url : page_url + '/finance/bankbill/addCapitalBill.do',
		data : {
			"bankBillId" : id,
			"saleorderId" : saleorderId,
			"payer":payer,
			"amount":amount,
			"comments" : comments,
			"traderSubject":traderSubject,
			"receivedAmount":receivedAmount,
			"formToken":formToken
		},
		type : "POST",
		dataType : "json",
		success : function(data) {
			if(data.code == 0){
				var message = data.message;
				var formTokenNew = "";
				var successMessage = "";

				if(message.indexOf("::") != -1){
					successMessage = message.split("::")[0];
					formTokenNew = message.split("::")[1];
				}
				layer.alert(successMessage, { icon : 1}, function () {
					layer.closeAll();
					if(existingParent){
						window.parent.layer.close(index);
						window.parent.location.reload();
					}else {
						window.location.reload();
					}
				});
			}else{
				layer.alert(data.message, { icon : 2});

				if(!existingParent){
					window.location.reload();
				}
			}
		},error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	})
}

function newAddCapitalBill(id, saleorderId, payer, amount, comments,traderSubject, receivedAmount, existingParent, formToken,obj ) {
	$.ajax({
		async : false,
		url : page_url + '/finance/bankbill/addCapitalBill.do',
		data : {
			"bankBillId" : id,
			"saleorderId" : saleorderId,
			"payer":payer,
			"amount":amount,
			"comments" : comments,
			"traderSubject":traderSubject,
			"receivedAmount":receivedAmount,
			"formToken":formToken
		},
		type : "POST",
		dataType : "json",
		success : function(data) {
			if(data.code == 0){
				var message = data.message;
				var formTokenNew = "";
				var successMessage = "";

				if(message.indexOf("::") != -1){
					successMessage = message.split("::")[0];
					formTokenNew = message.split("::")[1];
					$("[name='formToken']").val(formTokenNew);
				}
				layer.alert(successMessage, { icon : 1}, function () {
					// layer.closeAll();
					// if(existingParent){
					// 	window.parent.layer.close(index);
					// 	window.parent.location.reload();
					// }else {
					// 	window.location.reload();
					// }
					layer.closeAll();
					var loading = layer.load('刷新数据中...', {
						shade: [0.5,'#000'] //0.1透明度的白色背景
					});
					var objTarget = $(obj).parents('.table-style7:first');
					var tableIdStr = objTarget.find('tbody:first').find('td:first').html();
					$.ajax({
						type: "GET",
						url: page_url + '/finance/bankbill/getbankBillMatchListDetail.do?bankBillIdForPay='+id,
						success: function(response) {
							// 将数据追加到table中显示
							objTarget.html(response );
							var td = objTarget.find('tbody:first').find('td:first');
							if(td != undefined){
								td.html(tableIdStr);
							}

							layer.close(loading);
						}
					});
				});
				///finance/bankbill/getbankBillMatchListDetail.do

				//$(obj).parents().find(".table-style7").html("");
			}else{
				layer.alert(data.message, { icon : 2});

				if(!existingParent){
					window.location.reload();
				}
			}
		},error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	})
}


function addCapitalBillForImport(bankBillId) {
	debugger
	$(".table-style7").each(function(index, value) {
		if($(this).find("[name='bankBillId']").val() == bankBillId){
			var loading = layer.load('刷新数据中...', {
				shade: [0.5,'#000'] //0.1透明度的白色背景
			});
			var objTarget = $(this);
			var tableIdStr = objTarget.find('tbody:first').find('td:first').html();
			$.ajax({
				type: "GET",
				url: page_url + '/finance/bankbill/getbankBillMatchListDetail.do?bankBillIdForPay='+bankBillId,
				success: function(response) {
					// 将数据追加到table中显示
					objTarget.html(response );
					var td = objTarget.find('tbody:first').find('td:first');
					if(td != undefined){
						td.html(tableIdStr);
					}
					layer.close(loading);
				}
			});

		}
	});


}

function checkSaleOrderResidueAmount(amount, saleOrderId) {
	resultCode=0
	$.ajax({
		async : false,
		url : page_url + '/finance/bankbill/checkSaleOrderResidueAmount.do',
		data : {
			"amount" : amount,
			"saleOrderId" : saleOrderId,
		},
		type : "POST",
		dataType : "json",
		success : function(data) {
			if(data.code==0){
				resultCode=0
			} else if(data.code == -2){
				resultCode = -2
			} else {
				resultCode=-1
			}
		},error:function(data){
			if(data.status == 1001){
				resultCode=1001
			}
		}
	});

	return resultCode
}
//手动匹配确认结款
function salemoneyaddorhand(id){
	checkLogin();
	if($("input[name='checked']:checked").length ==0){
		layer.alert("请选择需要结款的订单");
		return false;
	}
	var lock = false;

	var formToken = $("input[name='formToken']").val();
	var payer = $("input[name='checked']:checked").parent().nextAll().find("input[name='payer']").val();
	var comments = $("input[name='checked']:checked").parent().nextAll().find("input[name='comments']").val();
	var saleorderId = $("input[name='checked']:checked").parent().nextAll().find("input[name='saleorderId']").val();
	var receivedAmount = $("input[name='checked']:checked").parent().nextAll().find("input[name='receivedAmount']").val();
	var amount = $("input[name='checked']:checked").parent().nextAll().find("input[name='amount']").val();
	var traderSubject = $("input[name='checked']:checked").parent().nextAll().find("select[name='traderSubject']").val();

	if (payer.length > 128 ){
		layer.alert("拟结款名称不允许超过128个字符");
		return false;
	}
	if(payer.length  == 0){
		layer.alert("拟结款名称不允许为空");
		return false;
	}
	if (comments.length > 512 ){
		layer.alert("备注不允许超过512个字符");
		return false;
	}

	//校验是否超出销售订单结余金额
	var resultCode = checkSaleOrderResidueAmount(amount, saleorderId);
	if(resultCode===0) {
		layer.confirm('确认结款?', {title:'操作确认'},function(index){
			if(!lock) {
				lock = true;
				addCapitalBill(id,saleorderId,payer,amount,comments,traderSubject, receivedAmount,true, formToken);
				// $.ajax({
				// 	async : false,
				// 	url : page_url + '/finance/bankbill/addCapitalBill.do',
				// 	data : {
				// 		"bankBillId" : id,
				// 		"saleorderId" : saleorderId,
				// 		"payer":payer,
				// 		"amount":amount,
				// 		"comments" : comments,
				// 		"traderSubject":traderSubject,
				// 		"receivedAmount":receivedAmount,
				// 	},
				// 	type : "POST",
				// 	dataType : "json",
				// 	success : function(data) {
				// 		if(data.code == 0){
				// 			window.parent.layer.close(index);
				// 			window.parent.location.reload();
				// 		}else{
				// 			layer.alert(data.message, { icon : 2});
				// 		}
				// 	},error:function(data){
				// 		if(data.status ==1001){
				// 			layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				// 		}
				// 	}
				// })
			}
		});
	}else if(resultCode===-1){
		layer.confirm('超出订单的需结款金额，确认要结款?',{ title:'操作确认', icon : 2},function(index) {
			if(!lock) {
				lock = true;
				addCapitalBill(id,saleorderId,payer,amount,comments,traderSubject, receivedAmount, true, formToken);
			}
			layer.close(index);
		});
	}else if(resultCode === -2){
		layer.alert("账期订单未信用支付，请联系销售处理");
	}else if(resultCode===1001){
		layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
	}
}

function matchBankBill(bankBillId,payApplyId,tranFlow,taskId,bankTag,payType){
	layer.confirm('确认匹配?', {title:'操作确认'},function(index){
			var paymentType = 0;
			if(bankTag == 2){
				//南京银行
				paymentType = 641;
			}else if(bankTag == 3){
				//中国银行
				paymentType = 716;
			}
			var formToken = $("input[name='formToken']").val();
			if(payType == 517){
				var url = page_url + '/finance/buyorder/payApplyPassByBankBill.do';
			}else{
				var url = page_url + '/finance/capitalbill/saveAddAfterCapitalBillForBank.do';
			}
			$.ajax({
				async : false,
				url : url,
				data : {
					"payApplyId" : payApplyId,
					"bankBillId" : bankBillId,
					"tranFlow" : tranFlow,
					"taskId":taskId,
					"paymentType":paymentType,
					"formToken":formToken
				},
				type : "POST",
				dataType : "json",
				success : function(data) {
					if(data.code == 0){
							layer.alert(data.message, { icon : 1});
							window.location.reload();
					}else{
						layer.alert(data.message, { icon : 2});
						window.location.reload();
					}
				},error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			})
		  layer.close(index);
		});
}

//确认借款
function salemoneyadd(id,saleorderId,receivedAmount,obj){
	checkLogin();
	var lock = false;
	var formToken = $("input[name='formToken']").val();
	var payer = $(obj).parent().prevAll().find("input[name='payer']").val();
	var comments = $(obj).parent().prevAll().find("input[name='comments']").val();
	var amount = $(obj).parent().prevAll().find("input[name='amount']").val();
	var traderSubject = $(obj).parent().prevAll().find("select[name='traderSubject']").val();
	if (payer.length > 128 ){
		layer.alert("拟结款名称不允许超过128个字符");
		return false;
	}
	if(payer.length  == 0){
		layer.alert("拟结款名称不允许为空");
		return false;
	}
	if (comments.length > 512 ){
		layer.alert("备注不允许超过512个字符");
		return false;
	}

	//校验是否超出销售订单结余金额
	var resultCode = checkSaleOrderResidueAmount(amount, saleorderId);
	if(resultCode==0) {
		layer.confirm('确认结款?', {title:'操作确认'},function(index){
			if(!lock) {
				lock = true;
				newAddCapitalBill(id,saleorderId,payer,amount,comments,traderSubject,receivedAmount,false, formToken,obj);
			}
			layer.close(index);
		});

	}else if(resultCode==-1){
		layer.confirm('超出订单的需结款金额，确认要结款?',{ title:'操作确认', icon : 2},function(index) {
			if(!lock) {
				lock = true;
				newAddCapitalBill(id, saleorderId, payer, amount, comments, traderSubject, receivedAmount, false, formToken,obj);
			}
			layer.close(index);
		});
	}else if(resultCode == -2){
		layer.alert("账期订单未信用支付，请联系销售处理");
	}else if(resultCode==1001){
		layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
	}


}

function searchReset() {
	$("form").find("input[type='text']").val('');
	$.each($("form select"),function(i,n){
		$(this).children("option:first").prop("selected",true);
	});
	//交易时间重置默认
	var nowDate = $("form").find("input[name='nowDate']").val();
	$("form").find("input[name='beginTime']").val(nowDate);
	$("form").find("input[name='endTime']").val(nowDate);
}
function noEmpty(obj,val){
	if($(obj).val() == ""){
		$(obj).val(val);
	}
}
function search() {
	if($("input[name='search']").val() == ""){
		layer.alert("请输入关键词");
		return false;
	}
	$("#searchSpan").hide();
	$("#searchSpanHide").show();
	$("#search").submit();
}

function manualMatchInfo(obj,bankBillId){
	$("#terminalDiv")
	.attr(
			'layerParams',
			'{"width":"70%","height":"80%","title":"手动匹配","link":"'
					+ page_url
					+ '/finance/bankbill/getManualMatchInfo.do?bankBillId='
					+ bankBillId + '"}');
	$("#terminalDiv").click();

}
//手动匹配付款
function manualMatchPayInfo(obj,bankBillId){
	$("#terminalDiv")
	.attr(
			'layerParams',
			'{"width":"70%","height":"80%","title":"手动匹配","link":"'
					+ page_url
					+ '/finance/bankbill/getManualMatchPayInfo.do?bankBillId='
					+ bankBillId + '"}');
	$("#terminalDiv").click();

}

//手动匹配确认结款
function paymoneyaddorhand(bankBillId,tranFlow,bankTag,residueAmount){
	checkLogin();
	if($("input[name='checked']:checked").length ==0){
		layer.alert("请选择需要付款的订单");
		return false;
	}
	var lock = false;
	var amount = $("input[name='checked']:checked").parent().nextAll().find("input[name='amount']").val();
	if(amount*1>residueAmount*1){
		layer.alert("付款申请金额不能大于银行流水剩余金额");
		return false;
	}
	layer.confirm('确认匹配?', {title:'操作确认'},function(index){
		var paymentType = 0;
		if(bankTag == 2){
			//南京银行
			paymentType = 641;
		}else if(bankTag == 3){
			//中国银行
			paymentType = 716;
		}
		var formToken = $("input[name='formToken']").val();
		var payApplyId = $("input[name='checked']:checked").parent().nextAll().find("input[name='payApplyId']").val();
		var taskId = $("input[name='checked']:checked").parent().nextAll().find("input[name='taskId']").val();
		var payType = $("input[name='checked']:checked").parent().nextAll().find("input[name='payType']").val();
		if(payType == 517){
			var url = page_url + '/finance/buyorder/payApplyPassByBankBill.do';
		}else{
			var url = page_url + '/finance/capitalbill/saveAddAfterCapitalBillForBank.do';
		}
		$.ajax({
			async : false,
			url : url,
			data : {
				"payApplyId" : payApplyId,
				"bankBillId" : bankBillId,
				"tranFlow" : tranFlow,
				"taskId":taskId,
				"paymentType":paymentType,
				"formToken":formToken
			},
			type : "POST",
			dataType : "json",
			success : function(data) {
				if(data.code == 0){
						layer.alert(data.message, { icon : 1});
						window.location.reload();
				}else if(data.code==1){
					layer.alert("该流水已经生成");
					window.location.reload();
				}

				else{
					layer.alert(data.message, { icon : 2});
					window.location.reload();
				}
			},error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		})
	  layer.close(index);
	});

}



// 批量确认
function numbersConfirm(){
	// 获取所有选中的单选框
	var checkedObj = $(".check_box_checked:checked");
	if(checkedObj && checkedObj.length > 0){
		// 再次确认是否批量操作
		layer.confirm('您确定要批量操作吗?', {title:'操作确认'},function(index){
				layer.close(index);
				// 循环
				for (var i = 0; i < checkedObj.length; i++) {
					// 找到被选中的单选框
					if($(checkedObj[i]).is(":checked")){
						// 银行流水id
						var bankBillId = $(checkedObj[i]).val();
						// 付款申请id
						var payApplyId = $(checkedObj[i]).attr("payapplyid");
						// 流水
						var tranFlow = $(checkedObj[i]).attr("tranflow");
						// taskId
						var taskId = $(checkedObj[i]).attr("taskinfopayid");
						// 银行标识
						var bankTag = $(checkedObj[i]).attr("banktag");
						// 支付类型
						var payType = $(checkedObj[i]).attr("paytype");
						//token
						var paymentType = 0;
						if(bankTag == 2){
							//南京银行
							paymentType = 641;
						}else if(bankTag == 3){
							//中国银行
							paymentType = 716;
						}
						if(payType == 517){
							var url = page_url + '/finance/buyorder/payApplyPassByBankBillNoFormToken.do';
						}else{
							var url = page_url + '/finance/capitalbill/saveAddAfterCapitalBillForBankNoFormToken.do';
						}
						$.ajax({
							async : false,
							url : url,
							data : {
								"payApplyId" : payApplyId,
								"bankBillId" : bankBillId,
								"tranFlow" : tranFlow,
								"taskId":taskId,
								"paymentType":paymentType,
								"formToken":$("input[name='formToken']").val()
							},
							type : "POST",
							dataType : "json",
							// 成功不做操作
							success : function(data) {
								if(data.code == 0){
									layer.close(index);
								}if(data.code == 1){

									layer.alert("该流水已经生成");
									window.location.reload();
								}

								else{
									console.log("操作失败");
								}

							},
							// 操作失败
							error:function(data){
								if(data.status ==1001){
									layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
								}
							}
						})
					}
				}
				layer.close(index);
				// 页面重新加载
				window.location.reload();
		});
	}
	// 如果没有选中的单选框
	else{
		layer.alert("当前无选中的按钮");
	}

}

//支付宝结款确认更新资金流水
function  updateCapitalBill(bankBillId,capitalBillId) {
	checkLogin();

	layer.confirm('确认结款?', {title:'操作确认'},function(index){
		$.ajax({
			async:false,
			url:page_url + '/finance/capitalbill/updateCapitalBill.do',
			data:{
				bankBillId:bankBillId,
				capitalBillId:capitalBillId
			},
			type : "POST",
			dataType : "json",
			success:function (result) {
				if(result.code == 0){
					layer.alert(result.message, { icon : 1})
				}else{
					layer.alert(result.message, { icon : 2})
				}
				var currentUrl = $("#currentUrl").val();
				console.log(currentUrl);
				window.location.href=currentUrl;
				layer.close(index);

			},
			error:function(result){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		})
	});
}


//支付宝批量确认
function updateBatchCapitalBill(obj,bankBillId) {
	checkLogin();
	var selectedCapitalBillIds = "";
	$(obj).parent().parent().parent().find('input[name="checkOne"]:checked').each(function () {
		selectedCapitalBillIds += $(this).val()+",";
	});
	console.log(selectedCapitalBillIds);
	if(selectedCapitalBillIds ==""){
		layer.alert("请先选择数据");
		return;
	}

	index=layer.confirm("确定批量匹配流水吗？", {
		btn: ['确定','取消'] //按钮
	}, function(){
		$.ajax({
			type: "POST",
			url: page_url + '/finance/capitalbill/batchUpdateCapitalBill.do',
			data: {"capitalBillIds":selectedCapitalBillIds,"bankBillId":bankBillId},
			dataType:'json',
			success: function(result){
				if(result.code == 0){
					layer.alert(result.message, { icon : 1})
				}else{
					layer.alert(result.message, { icon : 2})
				}
				var currentUrl = $("#currentUrl").val();
				console.log(currentUrl);
				window.location.href=currentUrl;
				layer.close(index);
			},
			error:function(result){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		});
	});
}

function manualAlipayMatchInfo(obj,bankBillId,bankTag){
	$("#terminalDiv")
		.attr(
			'layerParams',
			'{"width":"70%","height":"80%","title":"手动匹配","link":"'
			+ page_url
			+ '/finance/bankbill/getManualMatchInfo.do?bankBillId='
			+ bankBillId
			+ '&bankTag='
			+ bankTag
			+ '"}');
	$("#terminalDiv").click();

}

function handUpdateCapitalBill(bankBillId) {
	checkLogin();
	if($("input[name='checked']:checked").length ==0){
		layer.alert("请选择需要结款的订单");
		return false;
	}
	var capitalBillId = $("input[name='checked']:checked").val();
	layer.confirm('确认结款?', {title:'操作确认'},function(index){
		$.ajax({
			async:false,
			url:page_url + '/finance/capitalbill/updateCapitalBill.do',
			data:{
				bankBillId:bankBillId,
				capitalBillId:capitalBillId
			},
			type : "POST",
			dataType : "json",
			success:function (result) {
				if(result.code == 0){
					layer.alert(result.message, { icon : 1})
				}else{
					layer.alert(result.message, { icon : 2})
				}
				layer.close(index);
				var currentUrl = $("#currentUrl").val();
				console.log(currentUrl);
				window.location.href=currentUrl;
			},
			error:function(result){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
			}
		})
	});

}

//支付宝或者卫星手动匹配付款
function manualBDpayMatchInfo(obj,bankBillId,bankTag){
	$("#terminalDiv")
		.attr(
			'layerParams',
			'{"width":"70%","height":"80%","title":"手动匹配","link":"'
			+ page_url
			+ '/finance/bankbill/getManualMatchPayInfo.do?bankBillId='
			+ bankBillId
			+ '&bankTag='
			+ bankTag
			+ '"}');
	$("#terminalDiv").click();

}

function handUpdateCapitalBillPay() {
	checkLogin();
	if($("input[name='checked']:checked").length ==0){
		layer.alert("请选择需要付款的订单");
		return false;
	}
	var capitalBillId = $("input[name='checked']:checked").val();
	layer.confirm('确认付款?', {title:'操作确认'},function(index){
		$.ajax({
			async:false,
			url:page_url + '/finance/capitalbill/updateCapitalBill.do',
			data:{
				bankBillId:bankBillId,
				capitalBillId:capitalBillId
			},
			type : "POST",
			dataType : "json",
			success:function (result) {
				if(result.code == 0){
					layer.alert(result.message, { icon : 1})
				}else{
					layer.alert(result.message, { icon : 2})
				}
				layer.close(index);
				var currentUrl = $("#currentUrl").val();
				console.log(currentUrl);
				window.location.href=currentUrl;
			},
			error:function(result){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
			}
		})
	});

}

function batchUpdateCapitalBill(obj){
	checkLogin();
	var capitalList = [];

	if($('input[name="checkOne"]:checked').length==0){
		layer.alert("请先选择数据");
		return;
	}
	$('input[name="checkOne"]:checked').each(function () {
		var capital ={};
		capital.bankBillId=$(this).attr("bankBillValue");
		capital.capitalBillId=$(this).attr("capitalBillValue");
		console.log(capital);
		capitalList.push(capital);
	});
	var s = JSON.stringify(capitalList);
	index=layer.confirm("确定批量匹配流水吗？", {
		btn: ['确定','取消'] //按钮
	}, function(){
		$.ajax({
			async:false,
			type: "POST",
			url: page_url + '/finance/capitalbill/batchUpdateCapitalBill.do',
			data: JSON.stringify(capitalList),
			dataType:'json',
			contentType : "application/json",
			success: function(result){
				if(result.code == 0){
					layer.alert(result.message, { icon : 1})
				}else{
					layer.alert(result.message, { icon : 2})
				}
				var currentUrl = $("#currentUrl").val();
				console.log(currentUrl);
				window.location.href=currentUrl;
				layer.close(index);
			},
			error:function(result){
				layer.alert("匹配失败")
			}
		});
	});
}

$(function (){
	$(document).on('click', '.J-pop-new-data', function () {
		layer.config({
			extend: 'vedeng.com/style.css', //加载您的扩展样式
			skin: 'vedeng.com'
		});
		var layerParams = $(this).attr('layerParams');
		if (typeof(layerParams) == 'undefined') {
			alert('参数错误');
		} else {
			layerParams = $.parseJSON(layerParams);
		}
		var link = layerParams.link;
		if (link.indexOf("?") > 0 && (link.indexOf("?") + 1 == link.length)) {
			link += "pop=pop";
		} else if (link.indexOf("?") < 0) {
			link += "?pop=pop";
		} else if (link.indexOf("?") > 0 && (link.indexOf("?") + 1 != link.length)) {
			link += "&pop=pop";
		}
		var index = layer.open({
			type: 2,
			shadeClose: false, //点击遮罩关闭
			//area: 'auto',
			area: [layerParams.width, layerParams.height],
			title: layerParams.title,
			content: layerParams.noEncodeURI ? encodeURI(link): encodeURI(encodeURI(link)),
			success: function(layero, index) {
				//layer.iframeAuto(index);
			}
		});
	});

	$(document).on('click', '.J-addtitle', function () {
		var frontPageId = $(window.parent.document).find('.active').eq(1).attr('id');
		var newPageId;
		var tabTitle = $(this).attr('tabTitle');
		if (typeof(tabTitle) == 'undefined') {
			alert('参数错误');
		} else {
			tabTitle = $.parseJSON(tabTitle);
		}
		var id = tabTitle.num;
		// var id = 'index' + Date.parse(new Date()) + Math.floor(Math.random()*1000);
		var name = tabTitle.title;
		var uri = tabTitle.link;

		window.parent.postMessage({
			from:'olderp',
			name: name,
			url:uri,
			id:id
		}, '*');


		var closable = 1;
		var item = { 'id': id, 'name': name, 'url': uri, 'closable': closable == 1 ? true : false };
		// self.parent.closableTab.addTab(item);
		// self.parent.closableTab.resizeMove();
		// $(window.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);

		if (typeof(self.parent.closableTab) != 'undefined') {
			self.parent.closableTab.addTab(item);
			self.parent.closableTab.resizeMove();
			$(window.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
		}else{
			try{
				var uniqueName = uri.replace('./', '').replace(/["&'./:=%?[\]]/gi, '-').replace(/(--)/gi, '');
				window.parent.postMessage({
					from:'ez',
					name: title,
					url:uri,
					id:"tab-"+uniqueName
				}, '*');
			}catch (e){}
		}
	});
})










