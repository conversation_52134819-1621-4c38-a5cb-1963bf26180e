package com.vedeng.erp.kingdee.domain.entity;

import java.util.Date;

import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseEntity;
import com.vedeng.common.mybatis.jbatis.annotation.Column;
import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.*;

/**
* <AUTHOR>
* @date 2023/1/9
* @apiNote 
*/
/**
    * 销售退货入库单
    */
@Getter
@Setter
@ToString
@Table(name = "KING_DEE_SALE_RETURN_STOCK")
public class KingDeeSaleReturnStockEntity extends BaseEntity {
    /**
    * 主键
    */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
    * 单据内码0：表示新增非0：云星空系统单据FID值，表示修改
    */
    private String fid;

    /**
    * 单据类型填单据类型编码，默认填 "XSTHD01_SYS"
    */
    private String fBillTypeId;

    /**
    * 单据编号
    */
    private String fBillNo;

    /**
    * 贝登单据头ID
    */
    private String fQzokBddjtid;

    /**
    * 单据日期
    */
    private String fDate;

    /**
    * 退货销售组织
    */
    private String fSaleOrgId;

    /**
    * 退货库存组织
    */
    private String fStockOrgId;

    /**
    * 客户编码
    */
    private String fRetcustId;

    /**
    * F_ENTITY
    */
    @Column(jdbcType = "VARCHAR")
    private JSONArray fEntity;
}