package com.vedeng.logistics.dao.ext;

import com.vedeng.logistics.dao.WmsWarehouseGoodsOperateLogDataMapper;
import com.vedeng.logistics.model.WmsWarehouseGoodsOperateLogData;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WmsWarehouseGoodsOperateLogDataExtMapper extends WmsWarehouseGoodsOperateLogDataMapper {
    List<WmsWarehouseGoodsOperateLogData> selectByMinId(@Param("minId") int minId, @Param("limit") int limit);
    List<WmsWarehouseGoodsOperateLogData> selectByIds(@Param("ids") List<Integer> ids );
    Integer selectMaxLogId();
    String selectExpressNosByExpressId(@Param("warehouseGoodsOperateLogId") Integer warehouseGoodsOperateLogId);

    WmsWarehouseGoodsOperateLogData selectByLogId(@Param("logId") int logId);

    WmsWarehouseGoodsOperateLogData selectLatestReportBySkuAndBatchNum(@Param("sku") String sku,@Param("batchNum") String batchNum);

    List<WmsWarehouseGoodsOperateLogData> selectLatestIdByExpressModTime(@Param("preSystemTime") Long preSystemTime);

    String selectExpressNosByRidAndBtype(@Param("relatedId")int relatedId,@Param("businessType")int businessType);
}
