<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="采购价编辑" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript">
    function subForm(){

        if($("#price").val() == ""){
            layer.alert("价格不能为空");
            return;
        }

        if($("#couponReason").val() == ""){
            layer.alert("优惠原因不能为空");
            return;
        }

        if(Number($("#price").val()) > Number(${price})){
            layer.alert("采购价不能大于已核价的采购成本" + ${price});
            return;
        }

        $.ajax({
            type: "POST",
            url: "./editPurchasePrice.do",
            data: $('#editPurchasePrice').serialize(),
            dataType:'json',
            success: function(data){
                if (data.code == 0) {

                    parent.layer.close(index);
                    window.parent.setPurcharsePrice(Number($("#price").val()),"${skuNo}");

                } else {
                    layer.alert(data.message);
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    }
</script>
<div class="form-list">
    <form id="editPurchasePrice" method="post" enctype="multipart/form-data">

        <input type="hidden" name="buyorderGoodsId" value="${buyorderGoodsId}"/>
        <input type="hidden" name="originalPurchasePrice" value="${price}"/>

        <ul>
            <li>
                <div class="form-tips">
                    <lable><font color="red">*</font> 本次采购价:</lable>
                </div>
                <div class='f_left'>
                    <div class="form-blanks">
                        <div class="pos_rel">
                            <input type="text" class="input-middle" value="${price}" id="price" name="price">
                        </div>
                    </div>
                </div>
            </li>

            <li>
                <div class="form-tips">
                    <lable><font color="red">*</font> 本次优惠原因:</lable>
                </div>
                <div class='f_left'>
                    <div class="form-blanks">
                        <div class="pos_rel">
                            <input type="text" class="input-middle" id="couponReason" name="couponReason">
                        </div>
                    </div>
                </div>
            </li>

            <li>
                <div class="form-tips">
                    <lable>
                        &nbsp;
                    </lable>
                </div>
                <div class='f_left'>
                    <div class="form-blanks">
                        <div class="pos_rel">
                            注:该SKU的采购成本已在价格中心维护,采购价不得超过已核价的采购成本
                        </div>
                    </div>
                </div>
            </li>

        </ul>

        <div class="add-tijiao tcenter">
            <button class="dele" type="button" id="close-layer">取消</button>
            <button type="button" class="bt-bg-style bg-deep-green" onclick="subForm();">提交</button>
        </div>
    </form>
</div>
<%@ include file="../../common/footer.jsp"%>