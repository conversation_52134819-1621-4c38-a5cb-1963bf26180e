package com.vedeng.order.model.adk;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TAdkSupplierExample {
    /**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table T_ADK_SUPPLIER
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table T_ADK_SUPPLIER
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table T_ADK_SUPPLIER
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_ADK_SUPPLIER
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public TAdkSupplierExample() {
		oredCriteria = new ArrayList<Criteria>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_ADK_SUPPLIER
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_ADK_SUPPLIER
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_ADK_SUPPLIER
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_ADK_SUPPLIER
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_ADK_SUPPLIER
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_ADK_SUPPLIER
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_ADK_SUPPLIER
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_ADK_SUPPLIER
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_ADK_SUPPLIER
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_ADK_SUPPLIER
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table T_ADK_SUPPLIER
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<Criterion>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andAdkSupplierIdIsNull() {
			addCriterion("adk_supplier_id is null");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierIdIsNotNull() {
			addCriterion("adk_supplier_id is not null");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierIdEqualTo(Integer value) {
			addCriterion("adk_supplier_id =", value, "adkSupplierId");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierIdNotEqualTo(Integer value) {
			addCriterion("adk_supplier_id <>", value, "adkSupplierId");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierIdGreaterThan(Integer value) {
			addCriterion("adk_supplier_id >", value, "adkSupplierId");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierIdGreaterThanOrEqualTo(Integer value) {
			addCriterion("adk_supplier_id >=", value, "adkSupplierId");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierIdLessThan(Integer value) {
			addCriterion("adk_supplier_id <", value, "adkSupplierId");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierIdLessThanOrEqualTo(Integer value) {
			addCriterion("adk_supplier_id <=", value, "adkSupplierId");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierIdIn(List<Integer> values) {
			addCriterion("adk_supplier_id in", values, "adkSupplierId");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierIdNotIn(List<Integer> values) {
			addCriterion("adk_supplier_id not in", values, "adkSupplierId");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierIdBetween(Integer value1, Integer value2) {
			addCriterion("adk_supplier_id between", value1, value2, "adkSupplierId");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierIdNotBetween(Integer value1, Integer value2) {
			addCriterion("adk_supplier_id not between", value1, value2, "adkSupplierId");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierCodeIsNull() {
			addCriterion("adk_supplier_code is null");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierCodeIsNotNull() {
			addCriterion("adk_supplier_code is not null");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierCodeEqualTo(String value) {
			addCriterion("adk_supplier_code =", value, "adkSupplierCode");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierCodeNotEqualTo(String value) {
			addCriterion("adk_supplier_code <>", value, "adkSupplierCode");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierCodeGreaterThan(String value) {
			addCriterion("adk_supplier_code >", value, "adkSupplierCode");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierCodeGreaterThanOrEqualTo(String value) {
			addCriterion("adk_supplier_code >=", value, "adkSupplierCode");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierCodeLessThan(String value) {
			addCriterion("adk_supplier_code <", value, "adkSupplierCode");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierCodeLessThanOrEqualTo(String value) {
			addCriterion("adk_supplier_code <=", value, "adkSupplierCode");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierCodeLike(String value) {
			addCriterion("adk_supplier_code like", value, "adkSupplierCode");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierCodeNotLike(String value) {
			addCriterion("adk_supplier_code not like", value, "adkSupplierCode");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierCodeIn(List<String> values) {
			addCriterion("adk_supplier_code in", values, "adkSupplierCode");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierCodeNotIn(List<String> values) {
			addCriterion("adk_supplier_code not in", values, "adkSupplierCode");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierCodeBetween(String value1, String value2) {
			addCriterion("adk_supplier_code between", value1, value2, "adkSupplierCode");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierCodeNotBetween(String value1, String value2) {
			addCriterion("adk_supplier_code not between", value1, value2, "adkSupplierCode");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierNameIsNull() {
			addCriterion("adk_supplier_name is null");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierNameIsNotNull() {
			addCriterion("adk_supplier_name is not null");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierNameEqualTo(String value) {
			addCriterion("adk_supplier_name =", value, "adkSupplierName");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierNameNotEqualTo(String value) {
			addCriterion("adk_supplier_name <>", value, "adkSupplierName");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierNameGreaterThan(String value) {
			addCriterion("adk_supplier_name >", value, "adkSupplierName");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierNameGreaterThanOrEqualTo(String value) {
			addCriterion("adk_supplier_name >=", value, "adkSupplierName");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierNameLessThan(String value) {
			addCriterion("adk_supplier_name <", value, "adkSupplierName");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierNameLessThanOrEqualTo(String value) {
			addCriterion("adk_supplier_name <=", value, "adkSupplierName");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierNameLike(String value) {
			addCriterion("adk_supplier_name like", value, "adkSupplierName");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierNameNotLike(String value) {
			addCriterion("adk_supplier_name not like", value, "adkSupplierName");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierNameIn(List<String> values) {
			addCriterion("adk_supplier_name in", values, "adkSupplierName");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierNameNotIn(List<String> values) {
			addCriterion("adk_supplier_name not in", values, "adkSupplierName");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierNameBetween(String value1, String value2) {
			addCriterion("adk_supplier_name between", value1, value2, "adkSupplierName");
			return (Criteria) this;
		}

		public Criteria andAdkSupplierNameNotBetween(String value1, String value2) {
			addCriterion("adk_supplier_name not between", value1, value2, "adkSupplierName");
			return (Criteria) this;
		}

		public Criteria andAddNoIsNull() {
			addCriterion("add_no is null");
			return (Criteria) this;
		}

		public Criteria andAddNoIsNotNull() {
			addCriterion("add_no is not null");
			return (Criteria) this;
		}

		public Criteria andAddNoEqualTo(String value) {
			addCriterion("add_no =", value, "addNo");
			return (Criteria) this;
		}

		public Criteria andAddNoNotEqualTo(String value) {
			addCriterion("add_no <>", value, "addNo");
			return (Criteria) this;
		}

		public Criteria andAddNoGreaterThan(String value) {
			addCriterion("add_no >", value, "addNo");
			return (Criteria) this;
		}

		public Criteria andAddNoGreaterThanOrEqualTo(String value) {
			addCriterion("add_no >=", value, "addNo");
			return (Criteria) this;
		}

		public Criteria andAddNoLessThan(String value) {
			addCriterion("add_no <", value, "addNo");
			return (Criteria) this;
		}

		public Criteria andAddNoLessThanOrEqualTo(String value) {
			addCriterion("add_no <=", value, "addNo");
			return (Criteria) this;
		}

		public Criteria andAddNoLike(String value) {
			addCriterion("add_no like", value, "addNo");
			return (Criteria) this;
		}

		public Criteria andAddNoNotLike(String value) {
			addCriterion("add_no not like", value, "addNo");
			return (Criteria) this;
		}

		public Criteria andAddNoIn(List<String> values) {
			addCriterion("add_no in", values, "addNo");
			return (Criteria) this;
		}

		public Criteria andAddNoNotIn(List<String> values) {
			addCriterion("add_no not in", values, "addNo");
			return (Criteria) this;
		}

		public Criteria andAddNoBetween(String value1, String value2) {
			addCriterion("add_no between", value1, value2, "addNo");
			return (Criteria) this;
		}

		public Criteria andAddNoNotBetween(String value1, String value2) {
			addCriterion("add_no not between", value1, value2, "addNo");
			return (Criteria) this;
		}

		public Criteria andAddNameIsNull() {
			addCriterion("add_name is null");
			return (Criteria) this;
		}

		public Criteria andAddNameIsNotNull() {
			addCriterion("add_name is not null");
			return (Criteria) this;
		}

		public Criteria andAddNameEqualTo(String value) {
			addCriterion("add_name =", value, "addName");
			return (Criteria) this;
		}

		public Criteria andAddNameNotEqualTo(String value) {
			addCriterion("add_name <>", value, "addName");
			return (Criteria) this;
		}

		public Criteria andAddNameGreaterThan(String value) {
			addCriterion("add_name >", value, "addName");
			return (Criteria) this;
		}

		public Criteria andAddNameGreaterThanOrEqualTo(String value) {
			addCriterion("add_name >=", value, "addName");
			return (Criteria) this;
		}

		public Criteria andAddNameLessThan(String value) {
			addCriterion("add_name <", value, "addName");
			return (Criteria) this;
		}

		public Criteria andAddNameLessThanOrEqualTo(String value) {
			addCriterion("add_name <=", value, "addName");
			return (Criteria) this;
		}

		public Criteria andAddNameLike(String value) {
			addCriterion("add_name like", value, "addName");
			return (Criteria) this;
		}

		public Criteria andAddNameNotLike(String value) {
			addCriterion("add_name not like", value, "addName");
			return (Criteria) this;
		}

		public Criteria andAddNameIn(List<String> values) {
			addCriterion("add_name in", values, "addName");
			return (Criteria) this;
		}

		public Criteria andAddNameNotIn(List<String> values) {
			addCriterion("add_name not in", values, "addName");
			return (Criteria) this;
		}

		public Criteria andAddNameBetween(String value1, String value2) {
			addCriterion("add_name between", value1, value2, "addName");
			return (Criteria) this;
		}

		public Criteria andAddNameNotBetween(String value1, String value2) {
			addCriterion("add_name not between", value1, value2, "addName");
			return (Criteria) this;
		}

		public Criteria andAddTimeIsNull() {
			addCriterion("add_time is null");
			return (Criteria) this;
		}

		public Criteria andAddTimeIsNotNull() {
			addCriterion("add_time is not null");
			return (Criteria) this;
		}

		public Criteria andAddTimeEqualTo(Date value) {
			addCriterion("add_time =", value, "addTime");
			return (Criteria) this;
		}

		public Criteria andAddTimeNotEqualTo(Date value) {
			addCriterion("add_time <>", value, "addTime");
			return (Criteria) this;
		}

		public Criteria andAddTimeGreaterThan(Date value) {
			addCriterion("add_time >", value, "addTime");
			return (Criteria) this;
		}

		public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
			addCriterion("add_time >=", value, "addTime");
			return (Criteria) this;
		}

		public Criteria andAddTimeLessThan(Date value) {
			addCriterion("add_time <", value, "addTime");
			return (Criteria) this;
		}

		public Criteria andAddTimeLessThanOrEqualTo(Date value) {
			addCriterion("add_time <=", value, "addTime");
			return (Criteria) this;
		}

		public Criteria andAddTimeIn(List<Date> values) {
			addCriterion("add_time in", values, "addTime");
			return (Criteria) this;
		}

		public Criteria andAddTimeNotIn(List<Date> values) {
			addCriterion("add_time not in", values, "addTime");
			return (Criteria) this;
		}

		public Criteria andAddTimeBetween(Date value1, Date value2) {
			addCriterion("add_time between", value1, value2, "addTime");
			return (Criteria) this;
		}

		public Criteria andAddTimeNotBetween(Date value1, Date value2) {
			addCriterion("add_time not between", value1, value2, "addTime");
			return (Criteria) this;
		}

		public Criteria andUpdateNoIsNull() {
			addCriterion("update_no is null");
			return (Criteria) this;
		}

		public Criteria andUpdateNoIsNotNull() {
			addCriterion("update_no is not null");
			return (Criteria) this;
		}

		public Criteria andUpdateNoEqualTo(String value) {
			addCriterion("update_no =", value, "updateNo");
			return (Criteria) this;
		}

		public Criteria andUpdateNoNotEqualTo(String value) {
			addCriterion("update_no <>", value, "updateNo");
			return (Criteria) this;
		}

		public Criteria andUpdateNoGreaterThan(String value) {
			addCriterion("update_no >", value, "updateNo");
			return (Criteria) this;
		}

		public Criteria andUpdateNoGreaterThanOrEqualTo(String value) {
			addCriterion("update_no >=", value, "updateNo");
			return (Criteria) this;
		}

		public Criteria andUpdateNoLessThan(String value) {
			addCriterion("update_no <", value, "updateNo");
			return (Criteria) this;
		}

		public Criteria andUpdateNoLessThanOrEqualTo(String value) {
			addCriterion("update_no <=", value, "updateNo");
			return (Criteria) this;
		}

		public Criteria andUpdateNoLike(String value) {
			addCriterion("update_no like", value, "updateNo");
			return (Criteria) this;
		}

		public Criteria andUpdateNoNotLike(String value) {
			addCriterion("update_no not like", value, "updateNo");
			return (Criteria) this;
		}

		public Criteria andUpdateNoIn(List<String> values) {
			addCriterion("update_no in", values, "updateNo");
			return (Criteria) this;
		}

		public Criteria andUpdateNoNotIn(List<String> values) {
			addCriterion("update_no not in", values, "updateNo");
			return (Criteria) this;
		}

		public Criteria andUpdateNoBetween(String value1, String value2) {
			addCriterion("update_no between", value1, value2, "updateNo");
			return (Criteria) this;
		}

		public Criteria andUpdateNoNotBetween(String value1, String value2) {
			addCriterion("update_no not between", value1, value2, "updateNo");
			return (Criteria) this;
		}

		public Criteria andUpdateNameIsNull() {
			addCriterion("update_name is null");
			return (Criteria) this;
		}

		public Criteria andUpdateNameIsNotNull() {
			addCriterion("update_name is not null");
			return (Criteria) this;
		}

		public Criteria andUpdateNameEqualTo(String value) {
			addCriterion("update_name =", value, "updateName");
			return (Criteria) this;
		}

		public Criteria andUpdateNameNotEqualTo(String value) {
			addCriterion("update_name <>", value, "updateName");
			return (Criteria) this;
		}

		public Criteria andUpdateNameGreaterThan(String value) {
			addCriterion("update_name >", value, "updateName");
			return (Criteria) this;
		}

		public Criteria andUpdateNameGreaterThanOrEqualTo(String value) {
			addCriterion("update_name >=", value, "updateName");
			return (Criteria) this;
		}

		public Criteria andUpdateNameLessThan(String value) {
			addCriterion("update_name <", value, "updateName");
			return (Criteria) this;
		}

		public Criteria andUpdateNameLessThanOrEqualTo(String value) {
			addCriterion("update_name <=", value, "updateName");
			return (Criteria) this;
		}

		public Criteria andUpdateNameLike(String value) {
			addCriterion("update_name like", value, "updateName");
			return (Criteria) this;
		}

		public Criteria andUpdateNameNotLike(String value) {
			addCriterion("update_name not like", value, "updateName");
			return (Criteria) this;
		}

		public Criteria andUpdateNameIn(List<String> values) {
			addCriterion("update_name in", values, "updateName");
			return (Criteria) this;
		}

		public Criteria andUpdateNameNotIn(List<String> values) {
			addCriterion("update_name not in", values, "updateName");
			return (Criteria) this;
		}

		public Criteria andUpdateNameBetween(String value1, String value2) {
			addCriterion("update_name between", value1, value2, "updateName");
			return (Criteria) this;
		}

		public Criteria andUpdateNameNotBetween(String value1, String value2) {
			addCriterion("update_name not between", value1, value2, "updateName");
			return (Criteria) this;
		}

		public Criteria andUpdateTimeIsNull() {
			addCriterion("update_time is null");
			return (Criteria) this;
		}

		public Criteria andUpdateTimeIsNotNull() {
			addCriterion("update_time is not null");
			return (Criteria) this;
		}

		public Criteria andUpdateTimeEqualTo(Date value) {
			addCriterion("update_time =", value, "updateTime");
			return (Criteria) this;
		}

		public Criteria andUpdateTimeNotEqualTo(Date value) {
			addCriterion("update_time <>", value, "updateTime");
			return (Criteria) this;
		}

		public Criteria andUpdateTimeGreaterThan(Date value) {
			addCriterion("update_time >", value, "updateTime");
			return (Criteria) this;
		}

		public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
			addCriterion("update_time >=", value, "updateTime");
			return (Criteria) this;
		}

		public Criteria andUpdateTimeLessThan(Date value) {
			addCriterion("update_time <", value, "updateTime");
			return (Criteria) this;
		}

		public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
			addCriterion("update_time <=", value, "updateTime");
			return (Criteria) this;
		}

		public Criteria andUpdateTimeIn(List<Date> values) {
			addCriterion("update_time in", values, "updateTime");
			return (Criteria) this;
		}

		public Criteria andUpdateTimeNotIn(List<Date> values) {
			addCriterion("update_time not in", values, "updateTime");
			return (Criteria) this;
		}

		public Criteria andUpdateTimeBetween(Date value1, Date value2) {
			addCriterion("update_time between", value1, value2, "updateTime");
			return (Criteria) this;
		}

		public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
			addCriterion("update_time not between", value1, value2, "updateTime");
			return (Criteria) this;
		}

		public Criteria andStatusIsNull() {
			addCriterion("status is null");
			return (Criteria) this;
		}

		public Criteria andStatusIsNotNull() {
			addCriterion("status is not null");
			return (Criteria) this;
		}

		public Criteria andStatusEqualTo(String value) {
			addCriterion("status =", value, "status");
			return (Criteria) this;
		}

		public Criteria andStatusNotEqualTo(String value) {
			addCriterion("status <>", value, "status");
			return (Criteria) this;
		}

		public Criteria andStatusGreaterThan(String value) {
			addCriterion("status >", value, "status");
			return (Criteria) this;
		}

		public Criteria andStatusGreaterThanOrEqualTo(String value) {
			addCriterion("status >=", value, "status");
			return (Criteria) this;
		}

		public Criteria andStatusLessThan(String value) {
			addCriterion("status <", value, "status");
			return (Criteria) this;
		}

		public Criteria andStatusLessThanOrEqualTo(String value) {
			addCriterion("status <=", value, "status");
			return (Criteria) this;
		}

		public Criteria andStatusLike(String value) {
			addCriterion("status like", value, "status");
			return (Criteria) this;
		}

		public Criteria andStatusNotLike(String value) {
			addCriterion("status not like", value, "status");
			return (Criteria) this;
		}

		public Criteria andStatusIn(List<String> values) {
			addCriterion("status in", values, "status");
			return (Criteria) this;
		}

		public Criteria andStatusNotIn(List<String> values) {
			addCriterion("status not in", values, "status");
			return (Criteria) this;
		}

		public Criteria andStatusBetween(String value1, String value2) {
			addCriterion("status between", value1, value2, "status");
			return (Criteria) this;
		}

		public Criteria andStatusNotBetween(String value1, String value2) {
			addCriterion("status not between", value1, value2, "status");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table T_ADK_SUPPLIER
	 * @mbg.generated  Thu Apr 11 13:17:53 CST 2019
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_ADK_SUPPLIER
     *
     * @mbg.generated do_not_delete_during_merge Thu Apr 11 09:50:49 CST 2019
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }
}