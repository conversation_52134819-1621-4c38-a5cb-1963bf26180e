package com.vedeng.erp.kingdee.batch.writer;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * @description 虚拟票 并跟新 返利收票状态
 */
@Service
@Slf4j
public class VirtualInvoiceBindWriter extends BaseWriter<List<BatchRVirtualInvoiceJWarehouseDto>> {

    @Autowired
    private BatchRVirtualInvoiceJWarehouseDtoMapper batchRVirtualInvoiceJWarehouseDtoMapper;

    @Override
    public void doWrite(List<BatchRVirtualInvoiceJWarehouseDto> item, JobParameters params, ExecutionContext stepContext) throws Exception {

        log.info("VirtualInvoiceBindWriter.doWrite:{}", JSON.toJSONString(item));

        if (CollUtil.isEmpty(item)) {
            return;
        }
        item.forEach(x-> batchRVirtualInvoiceJWarehouseDtoMapper.insertSelective(x));

    }

}
