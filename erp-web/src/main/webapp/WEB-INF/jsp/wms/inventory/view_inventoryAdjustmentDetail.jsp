<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date" %>
<%@taglib prefix="shiro" uri="http://shiro.apache.org/tags" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jstl/fmt_rt" %>

<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>查看库存调整单信息</title>
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/new/css/common/global.css">
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/new/css/pages/goods/vgoods/spu/spu_view.css?rnd=${resourceVersionKey}">
    <style>
        .detail-block {
            position: relative;
        }

        .mytitle {
            background: #4169E1;
            color: white
        }
    </style>
</head>

<body>

<div class="detail-wrap">
    <div class="detail-block">
        <div class="block-title mytitle">
            基本信息
        </div>
        <div class="goodDetails J-toggle-show-cnt">
            <div class="detail-table">
                <div class="table-item">
                    <div class="table-th">库存调整单单号：</div>
                    <div class="table-td">
                        ${inventoryAdjustmentVo.inventoryAdjustmentNo}
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-th">单据类型：</div>
                    <div class="table-td">
                        ${inventoryAdjustmentVo.typeStr}
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-th">仓库ID：</div>
                    <div class="table-td">
                        ${inventoryAdjustmentVo.warehouseId}
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-th">货主：</div>
                    <div class="table-td">
                        ${inventoryAdjustmentVo.customer}
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-th">调整原因：</div>
                    <div class="table-td">
                        ${inventoryAdjustmentVo.reason}
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-th">创建时间：</div>
                    <div class="table-td">
                        <div class="info-pic">
                            <date:date value="${inventoryAdjustmentVo.creatTime}"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="detail-block block-nohidden">
            <div class="block-title mytitle">产品信息</div>
            <div class="goodDetails J-toggle-show-cnt">
                <div class="detail-table">
                    <table class="table table-base">
                        <tbody>
                        <tr>
                            <th>序号</th>
                            <th>订货号</th>
                            <th>产品名称</th>
                            <th>调整变动数量</th>
                            <c:if test="${havePermission}">
                                <th>单价（元）</th>
                                <th>总额（元）</th>
                            </c:if>
                            <th>入库单号</th>
                            <th>逻辑库</th>
                            <th>生产日期</th>
                            <th>有效期至</th>
                            <th>入库日期</th>
                            <th>厂家批号</th>
                            <th>灭菌批号</th>
                            <th>注册证号</th>
                        </tr>
                        <c:if test="${not empty inventoryAdjustmentDetailVos}">
                            <c:forEach items="${inventoryAdjustmentDetailVos}" var="inventoryAdjustmentDetailVo"
                                       varStatus="status">
                                <tr>
                                    <td>
                                            ${status.count}
                                    </td>
                                    <td>
                                            ${inventoryAdjustmentDetailVo.skuNo}
                                    </td>
                                    <td>
                                            ${inventoryAdjustmentDetailVo.goodsName}
                                    </td>
                                    <td>
                                        <fmt:formatNumber type="number" value="${inventoryAdjustmentDetailVo.num}"
                                                          pattern="0.00" maxFractionDigits="2"/>
                                    </td>
                                    <c:if test="${havePermission}">
                                        <td>
                                                ${inventoryAdjustmentDetailVo.price}
                                        </td>
                                        <td>
                                                ${inventoryAdjustmentDetailVo.totalPrice}
                                        </td>
                                    </c:if>
                                    <td>
                                            ${inventoryAdjustmentDetailVo.orderNo}
                                    </td>
                                    <td>
                                            ${inventoryAdjustmentDetailVo.logicalWarehouseStr}
                                    </td>
                                    <td>
                                        <date:date value="${inventoryAdjustmentDetailVo.productionTime}"
                                                   format="yyyy-MM-dd"/>
                                    </td>
                                    <td>
                                        <date:date value="${inventoryAdjustmentDetailVo.validUntilTime}"
                                                   format="yyyy-MM-dd"/>
                                    </td>
                                    <td>
                                        <date:date value="${inventoryAdjustmentDetailVo.warehousingTime}"
                                                   format="yyyy-MM-dd"/>
                                    </td>
                                    <td>
                                            ${inventoryAdjustmentDetailVo.manufacturerBatchNo}
                                    </td>
                                    <td>
                                            ${inventoryAdjustmentDetailVo.sterilizationBatchNo}
                                    </td>
                                    <td>
                                            ${inventoryAdjustmentDetailVo.registrationNumber}
                                    </td>
                                </tr>
                            </c:forEach>
                        </c:if>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
    <script src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/static/new/js/common/artDialog/2.0.0/artDialog.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/static/new/js/common/template.js?rnd=${resourceVersionKey}"></script>

</body>