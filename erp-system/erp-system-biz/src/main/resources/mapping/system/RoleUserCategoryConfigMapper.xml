<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.system.mapper.RoleUserCategoryConfigMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.system.domain.entity.RoleUserCategoryConfigEntity">
    <!--@mbg.generated-->
    <!--@Table ROLE_USER_CATEGORY_CONFIG-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="USER_ID" jdbcType="INTEGER" property="userId" />
    <result column="USER_NAME" jdbcType="VARCHAR" property="userName" />
    <result column="CATEGORY_IDS" jdbcType="VARCHAR" property="categoryIds" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, USER_ID, USER_NAME, CATEGORY_IDS, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, 
    ADD_TIME, MOD_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from ROLE_USER_CATEGORY_CONFIG
    where ID = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from ROLE_USER_CATEGORY_CONFIG
    where ID = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.system.domain.entity.RoleUserCategoryConfigEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into ROLE_USER_CATEGORY_CONFIG (USER_ID, USER_NAME, CATEGORY_IDS, 
      CREATOR, UPDATER, CREATOR_NAME, 
      UPDATER_NAME, ADD_TIME, MOD_TIME
      )
    values (#{userId,jdbcType=INTEGER}, #{userName,jdbcType=VARCHAR}, #{categoryIds,jdbcType=VARCHAR}, 
      #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{updaterName,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.system.domain.entity.RoleUserCategoryConfigEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into ROLE_USER_CATEGORY_CONFIG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="userName != null and userName != ''">
        USER_NAME,
      </if>
      <if test="categoryIds != null and categoryIds != ''">
        CATEGORY_IDS,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="userName != null and userName != ''">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="categoryIds != null and categoryIds != ''">
        #{categoryIds,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.system.domain.entity.RoleUserCategoryConfigEntity">
    <!--@mbg.generated-->
    update ROLE_USER_CATEGORY_CONFIG
    <set>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      <if test="userName != null and userName != ''">
        USER_NAME = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="categoryIds != null and categoryIds != ''">
        CATEGORY_IDS = #{categoryIds,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.system.domain.entity.RoleUserCategoryConfigEntity">
    <!--@mbg.generated-->
    update ROLE_USER_CATEGORY_CONFIG
    set USER_ID = #{userId,jdbcType=INTEGER},
      USER_NAME = #{userName,jdbcType=VARCHAR},
      CATEGORY_IDS = #{categoryIds,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update ROLE_USER_CATEGORY_CONFIG
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="USER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.userId != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.userId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="USER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.userName != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.userName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="CATEGORY_IDS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.categoryIds != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.categoryIds,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
    </trim>
    where ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="ID" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into ROLE_USER_CATEGORY_CONFIG
    (USER_ID, USER_NAME, CATEGORY_IDS, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, 
      ADD_TIME, MOD_TIME)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.userId,jdbcType=INTEGER}, #{item.userName,jdbcType=VARCHAR}, #{item.categoryIds,jdbcType=VARCHAR}, 
        #{item.creator,jdbcType=INTEGER}, #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, 
        #{item.updaterName,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  
  <!-- 查询人员与商品分类配置列表 -->
  <select id="selectRoleUserCategoryConfigList" parameterType="com.vedeng.erp.system.vo.RoleUserCategoryConfigQueryVO" resultType="com.vedeng.erp.system.vo.RoleUserCategoryConfigVO">
    select 
      t.ID as id,
      t.USER_ID as userId,
      t.USER_NAME as userName,
      t.CATEGORY_IDS as categoryIds,
      c.BASE_CATEGORY_NAME as categoryName,
      t.CREATOR as creator,
      t.UPDATER as updater,
      t.CREATOR_NAME as createBy,
      t.UPDATER_NAME as updateBy,
      t.ADD_TIME as createTime,
      t.MOD_TIME as updateTime
    from ROLE_USER_CATEGORY_CONFIG t
    left join V_BASE_CATEGORY c on t.CATEGORY_IDS = c.BASE_CATEGORY_ID
    <where>
      <if test="userIds != null and userIds.size() &gt; 0">
        and t.USER_ID in
        <foreach close=")" collection="userIds" item="userId" open="(" separator=",">
          #{userId}
        </foreach>
      </if>

      <if test="businessUserIds != null and businessUserIds.size() &gt; 0">
        and t.USER_ID in
        <foreach close=")" collection="businessUserIds" item="userId" open="(" separator=",">
          #{userId}
        </foreach>
      </if>
      
      <if test="categoryIdList != null and categoryIdList.size() &gt; 0">
        and (
        <foreach collection="categoryIdList" item="categoryId" separator=" OR ">
          FIND_IN_SET(#{categoryId}, t.CATEGORY_IDS) > 0
        </foreach>
        )
      </if>
    </where>
    order by t.MOD_TIME desc, t.USER_NAME asc
  </select>
  
  <!-- 批量删除记录 -->
  <delete id="batchDelete" parameterType="java.util.List">
    delete from ROLE_USER_CATEGORY_CONFIG
    where ID in
    <foreach close=")" collection="ids" item="id" open="(" separator=",">
      #{id}
    </foreach>
  </delete>
  
  <!-- 统计用户关联的商品分类数量 -->
  <select id="countByUserId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
    select count(1)
    from ROLE_USER_CATEGORY_CONFIG
    where USER_ID = #{userId}
  </select>
  
  <!-- 获取所有业务人员信息 -->
  <select id="selectAllBusinessUsers" resultType="com.vedeng.erp.system.vo.BusinessUserVO">
    SELECT DISTINCT
      t.USER_ID AS userId,
      t.USER_NAME AS userName,
      d.ALIAS_HEAD_PICTURE AS userAvatar
    FROM ROLE_USER_CATEGORY_CONFIG t
    LEFT JOIN T_USER_DETAIL d ON t.USER_ID = d.USER_ID
    <where>
      <if test="userName != null and userName != ''">
        AND t.USER_NAME LIKE CONCAT('%', #{userName}, '%')
      </if>
    </where>
    ORDER BY t.USER_NAME ASC
    LIMIT 100
  </select>
</mapper>