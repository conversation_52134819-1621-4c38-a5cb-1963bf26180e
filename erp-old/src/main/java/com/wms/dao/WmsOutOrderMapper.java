package com.wms.dao;
import org.apache.ibatis.annotations.Param;
import java.util.List;


import com.wms.dto.WmsOutOrderDto;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2023/12/11 8:54
 **/
public interface WmsOutOrderMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(WmsOutOrderDto record);

    int insertSelective(WmsOutOrderDto record);

    WmsOutOrderDto selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(WmsOutOrderDto record);

    int updateByPrimaryKey(WmsOutOrderDto record);

    List<WmsOutOrderDto> selectByWmsNo(@Param("wmsNo")String wmsNo);


}