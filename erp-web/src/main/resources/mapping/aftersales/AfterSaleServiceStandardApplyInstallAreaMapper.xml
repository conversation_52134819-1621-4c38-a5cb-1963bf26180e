<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.aftersales.dao.AfterSaleServiceStandardApplyInstallAreaMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.aftersales.model.AfterSaleServiceStandardApplyInstallArea" >
    <id column="INSTALL_AREA_ID" property="installAreaId" jdbcType="BIGINT" />
    <result column="SERVICE_STANDARD_APPLY_ID" property="serviceStandardApplyId" jdbcType="BIGINT" />
    <result column="PROVINCE_CITY_JSONVALUE" property="provinceCityJsonvalue" jdbcType="VARCHAR" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="UPDATOR" property="updator" jdbcType="INTEGER" />
    <result column="ADD_TIME" property="addTime" jdbcType="VARCHAR" />
    <result column="MOD_TIME" property="modTime" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    INSTALL_AREA_ID, SERVICE_STANDARD_APPLY_ID, PROVINCE_CITY_JSONVALUE, CREATOR, UPDATOR,
    ADD_TIME, MOD_TIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from T_AFTER_SALE_SERVICE_STANDARD_APPLY_INSTALL_AREA
    where INSTALL_AREA_ID = #{installAreaId,jdbcType=BIGINT}
  </select>

  <select id="queryInstallAreaByApplyId" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from T_AFTER_SALE_SERVICE_STANDARD_APPLY_INSTALL_AREA
    where SERVICE_STANDARD_APPLY_ID = #{serviceStandardApplyId,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from T_AFTER_SALE_SERVICE_STANDARD_APPLY_INSTALL_AREA
    where INSTALL_AREA_ID = #{installAreaId,jdbcType=BIGINT}
  </delete>

  <insert id="insertSelective" parameterType="com.vedeng.aftersales.model.AfterSaleServiceStandardApplyInstallArea" useGeneratedKeys="true" keyProperty="installAreaId">
    insert into T_AFTER_SALE_SERVICE_STANDARD_APPLY_INSTALL_AREA
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="serviceStandardApplyId != null" >
        SERVICE_STANDARD_APPLY_ID,
      </if>
      <if test="provinceCityJsonvalue != null" >
        PROVINCE_CITY_JSONVALUE,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="updator != null" >
        UPDATOR,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="serviceStandardApplyId != null" >
        #{serviceStandardApplyId,jdbcType=BIGINT},
      </if>
      <if test="provinceCityJsonvalue != null" >
        #{provinceCityJsonvalue,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updator != null" >
        #{updator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.aftersales.model.AfterSaleServiceStandardApplyInstallArea" >
    update T_AFTER_SALE_SERVICE_STANDARD_APPLY_INSTALL_AREA
    <set >
      <if test="serviceStandardApplyId != null" >
        SERVICE_STANDARD_APPLY_ID = #{serviceStandardApplyId,jdbcType=BIGINT},
      </if>
      <if test="provinceCityJsonvalue != null" >
        PROVINCE_CITY_JSONVALUE = #{provinceCityJsonvalue,jdbcType=VARCHAR},
      </if>
      <if test="updator != null" >
        UPDATOR = #{updator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=VARCHAR},
      </if>
    </set>
    where INSTALL_AREA_ID = #{installAreaId,jdbcType=BIGINT}
  </update>

</mapper>