package com.vedeng.trader.model.model.vo;

import com.vedeng.customerbillperiod.model.CustomerBillPeriodOverdueManagementDetail;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName CustomerBillPeriodOverdueManagementDetailVo.java
 * @Description TODO
 * @createTime 2021年07月28日 15:32:00
 */
@Data
public class CustomerBillPeriodOverdueManagementDetailTaskVo extends CustomerBillPeriodOverdueManagementDetail {
    //订单id
    private Integer saleorderId;
    //订单号
    private String saleorderNo;
    //客户名称
    private String traderName;
    //归属销售id
    private Integer userId;
}
