<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.system.mapper.SystemRoleMapper">

    <select id="getRoleListByUserId" resultType="com.vedeng.erp.system.dto.RoleDto">
        SELECT
            tr.ROLE_ID ,
            tr.ROLE_NAME
        FROM
            T_ROLE tr
        LEFT JOIN T_R_USER_ROLE trur ON
            tr.ROLE_ID = trur.ROLE_ID
        WHERE
            tr.COMPANY_ID = 1
             AND trur.USER_ID = #{userId,jdbcType=INTEGER}
    </select>
</mapper>