<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.oa.dao.FileDeliveryAddressExtMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.oa.model.FileDeliveryAddress">
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="APPLY_ID" jdbcType="INTEGER" property="applyId" />
    <result column="FILE_DELIVERY_ID" jdbcType="INTEGER" property="fileDeliveryId" />
    <result column="TRADER_TYPE" jdbcType="INTEGER" property="traderType" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName" />
    <result column="CONTACT_TYPE" jdbcType="INTEGER" property="contactType" />
    <result column="TRADER_CONTACT_ID" jdbcType="INTEGER" property="traderContactId" />
    <result column="TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="traderContactName" />
    <result column="TRADER_CONTACT_MOBILE" jdbcType="VARCHAR" property="traderContactMobile" />
    <result column="TRADER_CONTACT_ADDRESS_ID" jdbcType="INTEGER" property="traderContactAddressId" />
    <result column="AREA_ID" jdbcType="INTEGER" property="areaId" />
    <result column="TRADER_CONTACT_ADDRESS" jdbcType="VARCHAR" property="traderContactAddress" />
    <result column="IS_DELETE" jdbcType="TINYINT" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
    <result column="CITY_ID" jdbcType="INTEGER" property="cityId" />
    <result column="PROVINCE_ID" jdbcType="INTEGER" property="provinceId" />
    <result column="DELIVERY_STATUS" jdbcType="INTEGER" property="deliveryStatus" />
    <result column="LOGISTICS_NO" jdbcType="VARCHAR" property="logisticsNo" />
  </resultMap>
  
  <insert id="batchInsert" parameterType="java.util.List">
    insert into T_FILE_DELIVERY_ADDRESS (APPLY_ID, FILE_DELIVERY_ID, TRADER_TYPE,
      TRADER_ID, TRADER_NAME, CONTACT_TYPE, 
      TRADER_CONTACT_ID, TRADER_CONTACT_NAME, TRADER_CONTACT_MOBILE, 
      TRADER_CONTACT_ADDRESS_ID, AREA_ID, TRADER_CONTACT_ADDRESS,
      IS_DELETE, ADD_TIME, MOD_TIME, 
      CREATOR, UPDATER, UPDATE_REMARK, 
      CITY_ID, PROVINCE_ID, DELIVERY_STATUS, 
      LOGISTICS_NO,TRADER_CONTACT_ADDRESS_INFO)
    values
    <foreach collection="list" index="index" item="fileDeliveryAddress" separator=",">
     (#{fileDeliveryAddress.applyId,jdbcType=INTEGER}, 	#{fileDeliveryAddress.fileDeliveryId,jdbcType=INTEGER}, #{fileDeliveryAddress.traderType,jdbcType=INTEGER},
      #{fileDeliveryAddress.traderId,jdbcType=INTEGER}, #{fileDeliveryAddress.traderName,jdbcType=VARCHAR}, 	#{fileDeliveryAddress.contactType,jdbcType=INTEGER}, 
      #{fileDeliveryAddress.traderContactId,jdbcType=INTEGER}, #{fileDeliveryAddress.traderContactName,jdbcType=VARCHAR}, #{fileDeliveryAddress.traderContactMobile,jdbcType=VARCHAR}, 
      #{fileDeliveryAddress.traderContactAddressId,jdbcType=INTEGER}, #{fileDeliveryAddress.areaId,jdbcType=INTEGER}, 	  #{fileDeliveryAddress.traderContactAddress,jdbcType=VARCHAR}, 
      #{fileDeliveryAddress.isDelete,jdbcType=TINYINT}, #{fileDeliveryAddress.addTime,jdbcType=TIMESTAMP}, 	#{fileDeliveryAddress.modTime,jdbcType=TIMESTAMP}, 
      #{fileDeliveryAddress.creator,jdbcType=INTEGER}, 	#{fileDeliveryAddress.updater,jdbcType=INTEGER},   	#{fileDeliveryAddress.updateRemark,jdbcType=VARCHAR}, 
      #{fileDeliveryAddress.cityId,jdbcType=INTEGER}, 	#{fileDeliveryAddress.provinceId,jdbcType=INTEGER}, #{fileDeliveryAddress.deliveryStatus,jdbcType=INTEGER}, 
      #{fileDeliveryAddress.logisticsNo,jdbcType=VARCHAR},#{fileDeliveryAddress.traderContactAddressInfo,jdbcType=VARCHAR})
    </foreach>
  </insert>
  
</mapper>