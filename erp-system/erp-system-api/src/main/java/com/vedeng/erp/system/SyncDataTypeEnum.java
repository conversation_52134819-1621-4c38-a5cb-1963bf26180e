package com.vedeng.erp.system;

/**
 * T_SYNC_DATA_ERP表的枚举值
 */
public enum SyncDataTypeEnum {

    BUYORDER_TO_SALEORDER("BUYORDER_TO_SALEORDER","采购单转销售单","/orderstream/saleorder/receiveSaleorderFromOtherErp.do"),
    BUYORDERCONTACT_TO_SALECONTACT("BUYORDERCONTACT_TO_SALECONTACT","采购订单合同同步到另一个erp的销售订单合同","/orderstream/saleorder/contractReturnSaveForOtherErp.do"),
    SALEORDER_LOGISTICS_TO_BUYORDER("SALEORDER_LOGISTICS_TO_BUYORDER","销售订单物流信息同步给来源ERP系统的采购订单物流信息","/order/buyorder/savebuyorderLogisticsSaveFromOtherErp.do"),
    SALEORDER_AUTO_COMFIRM_ORDER("SALEORDER_AUTO_COMFIRM_ORDER","自动给自己的销售订单生成确认单","/order/buyorder/savebuyorderLogisticsSaveFromOtherErp.do");


    private String dataType;
    private String dataTypeName;
    private String dataInputUrl;

    SyncDataTypeEnum(String dataType, String dataTypeName, String dataInputUrl) {
        this.dataType = dataType;
        this.dataTypeName = dataTypeName;
        this.dataInputUrl = dataInputUrl;
    }

    public String getDataType() {
        return dataType;
    }
    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getDataTypeName() {
        return dataTypeName;
    }
    public void setDataTypeName(String dataTypeName) {
        this.dataTypeName = dataTypeName;
    }

    public String getDataInputUrl() {
        return dataInputUrl;
    }
    public void setDataInputUrl(String dataInputUrl) {
        this.dataInputUrl = dataInputUrl;
    }
}
