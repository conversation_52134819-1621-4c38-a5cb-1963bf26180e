package com.vedeng.goods.manager.validator.model;

import com.vedeng.goods.model.CoreSkuGenerate;
import com.vedeng.goods.utils.GoodsStorageConditionUtils;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Data
public class SkuValidObject extends GoodsValidObject {

    private Integer skuId;

    private Integer skuType;

    private String skuName;

    /**
     * 制造商型号
     */
    private String model;

    /**
     * 规格
     */
    private String spec;

    private Integer baseUnitId;

    private Integer unitId;

    /**
     * 是否启用多级包装
     */
    private Integer isEnableMultistagePackage;

    /**
     * 是否需报备
     */
    private Integer isNeedReport;


    /**
     * 是否套套件
     */
    private Integer isKit;


    /**
     * 是否管理贝登追溯码
     */
    private Integer isManageVedengCode;


    /**
     * 是否厂家赋SN码
     */
    private Integer isFactorySnCode;

    /**
     * 是否异形品
     */
    private Integer isBadGoods;

    /**
     * 是否启用厂家批号
     */
    private Integer isEnableFactoryBatchnum;

    private Integer isInstallable;


    /**
     * 商品条形码
     */
    private String goodsBarcode;

    /**
     * 是否必须检测报告
     */
    private Integer isNeedTestReprot;


    /**
     * 毛重
     */
    private BigDecimal grossWeight;

    /**
     * 包装的长
     */
    private BigDecimal packageLength;


    /**
     * 包装的宽
     */
    private BigDecimal packageWidth;

    /**
     * 包装的高
     */
    private BigDecimal packageHeight;

    /**
     * 包装清单
     */
    private String packingList;

    /**
     * 内含最小商品数量
     */
    private Long changeNum;

    private BigDecimal minOrder;

    /**
     * 技术参数
     */
    private String technicalParameter;

    /**
     * 物料编号
     */
    private String materialCode;


    private String supplyModel;


    /**
     * 请选择是否启用效期
     */
    private Integer isEnableValidityPeriod;


    private Integer effectiveDayUnit;


    private String effectiveDays;

    /**
     * 属性值Id
     */
    private Integer[] baseAttributeValueId;


    @Override
    public Integer getId() {
        return getSkuId();
    }

    @Override
    public String getName() {
        return skuName;
    }

    @Override
    public void setName(String name) {
        skuName = name;
    }


    public static SkuValidObject createCoreSkuGenerate(CoreSkuGenerate skuGenerate) {
        Objects.requireNonNull(skuGenerate);

        SkuValidObject skuValidObject = new SkuValidObject();
        skuValidObject.setSkuId(skuGenerate.getSkuId());

        skuValidObject.setGoodsLevelNo(skuGenerate.getGoodsLevelNo());
        skuValidObject.setGoodsPositionNo(skuGenerate.getGoodsPositionNo());
        skuValidObject.setName(skuGenerate.getSkuName());
        skuValidObject.setModel(skuGenerate.getModel());
        skuValidObject.setSpec(skuGenerate.getSpec());
        skuValidObject.setBaseUnitId(skuGenerate.getBaseUnitId());
        skuValidObject.setUnitId(skuGenerate.getUnitId());
        skuValidObject.setIsEnableMultistagePackage(skuGenerate.getIsEnableMultistagePackage());

        skuValidObject.setEffectiveDayUnit(skuGenerate.getEffectiveDayUnit());
        skuValidObject.setEffectiveDays(skuGenerate.getEffectiveDays());
        skuValidObject.setIsKit(skuGenerate.getIsKit());

        skuValidObject.setIsManageVedengCode(skuGenerate.getIsManageVedengCode());
        skuValidObject.setIsFactorySnCode(skuGenerate.getIsFactorySnCode());
        skuValidObject.setIsBadGoods(skuGenerate.getIsBadGoods());
        skuValidObject.setIsEnableFactoryBatchnum(skuGenerate.getIsEnableFactoryBatchnum());
        skuValidObject.setIsInstallable(skuGenerate.getIsInstallable());
        skuValidObject.setGoodsBarcode(skuGenerate.getGoodsBarcode());
        skuValidObject.setIsNeedTestReprot(skuGenerate.getIsNeedTestReprot());
        skuValidObject.setGrossWeight(skuGenerate.getGrossWeight());
        skuValidObject.setPackageLength(skuGenerate.getPackageLength());
        skuValidObject.setPackageWidth(skuGenerate.getPackageWidth());
        skuValidObject.setPackageHeight(skuGenerate.getPackageHeight());

        skuValidObject.setIsNeedReport(skuGenerate.getIsNeedReport());
        skuValidObject.setPackingList(skuGenerate.getPackingList());
        skuValidObject.setChangeNum(skuGenerate.getChangeNum());
        skuValidObject.setMinOrder(skuGenerate.getMinOrder());
        skuValidObject.setMaterialCode(skuGenerate.getMaterialCode());
        skuValidObject.setSupplyModel(skuGenerate.getSupplyModel());
        skuValidObject.setIsEnableValidityPeriod(skuGenerate.getIsEnableValidityPeriod());
        skuValidObject.setWikiHref(skuGenerate.getWikiHref());
        return skuValidObject;
    }

}
