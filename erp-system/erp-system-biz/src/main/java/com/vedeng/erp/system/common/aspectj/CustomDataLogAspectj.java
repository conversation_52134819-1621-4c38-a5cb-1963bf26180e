package com.vedeng.erp.system.common.aspectj;

import cn.hutool.core.util.StrUtil;
import com.vedeng.common.core.base.BaseException;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.system.common.annotation.CustomDataLogAnnotation;
import com.vedeng.erp.system.dto.CustomDataLogDto;
import com.vedeng.erp.system.domain.entity.CustomDataLogEntity;
import com.vedeng.erp.system.mapper.CustomDataLogMapper;
import com.vedeng.erp.system.mapstruct.CustomDataLogConvertor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 自定义新增数据日志aop
 * @date 2022/7/15 23:43
 */
@Component
@Aspect
@Slf4j
public class CustomDataLogAspectj {

    @Autowired
    private CustomDataLogConvertor customDataLogConvertor;
    @Autowired
    private CustomDataLogMapper customDataLogMapper;


    /**
     * 配置织入点
     */
    @Pointcut("@annotation(logAnnotation)")
    public void logPointCut(CustomDataLogAnnotation logAnnotation) {
    }


    @Around(value = "logPointCut(logAnnotation)", argNames = "pjp,logAnnotation")
    public Object around(ProceedingJoinPoint pjp, CustomDataLogAnnotation logAnnotation) throws Throwable {
        log.info("自定义新增数据日志-->执行方法前处理");
        CustomDataLogDto customDataLogDto = this.buildCustomDataLogDto(logAnnotation);
        Object result = pjp.proceed();
        log.info("自定义新增数据操作日志-->执行方法后处理");
        try {
            if (result instanceof List) {
                Set<Integer> resultList = new HashSet<>(((List<Integer>) result));
                customDataLogDto.setResultInfo(StrUtil.format(logAnnotation.successTpl(), resultList.size()));
                customDataLogDto.setRelatedIds(resultList.stream().map(String::valueOf).collect(Collectors.joining(",")));
                this.success(customDataLogDto);
            }
        } catch (Exception e) {
            log.error("返回类型不符合要求", e);
        }
        return result;
    }


    /**
     * 定义一个异常通知函数：
     * 只有在方法抛出异常时，该方法才会执行，而且可以接受异常对象。
     */
    @AfterThrowing(value = "logPointCut(logAnnotation)", throwing = "ex", argNames = "logAnnotation,ex")
    public void afterThrowingMethod(CustomDataLogAnnotation logAnnotation, Exception ex) {
        CustomDataLogDto customDataLogDto = this.buildCustomDataLogDto(logAnnotation);
        try {
            if (ex != null) {
                StringBuilder msg = new StringBuilder(logAnnotation.failTpl());
                if (ex.getMessage()!=null&&ex.getMessage().length()<=20) {
                    msg.append(ex.getMessage());
                }
                customDataLogDto.setResultInfo(msg.toString());
                customDataLogDto.setRelatedIds(StrUtil.EMPTY);
                this.fail(customDataLogDto);
            }
        } catch (Exception e) {
            log.error("返回类型不符合要求", e);
        }
    }

    /**
     * 构建日志对象
     *
     * @param logAnnotation logAnnotation
     * @return CustomDataLogDto
     */
    private CustomDataLogDto buildCustomDataLogDto(CustomDataLogAnnotation logAnnotation) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        CustomDataLogDto customDataLogDto = new CustomDataLogDto();
        customDataLogDto.setBelongerId(currentUser.getId());
        customDataLogDto.setBelonger(currentUser.getUsername());
        customDataLogDto.setSaveType(logAnnotation.dataLogType().getType());
        customDataLogDto.setType(logAnnotation.operBizType().getType());
        return customDataLogDto;
    }

    /**
     * 业务成功记录日志
     *
     * @param customDataLogDto customDataLogDto
     */
    private void success(CustomDataLogDto customDataLogDto) {
        customDataLogDto.setSuccessFlag(true);
        CustomDataLogEntity customDataLogEntity = customDataLogConvertor.toEntity(customDataLogDto);
        customDataLogMapper.insert(customDataLogEntity);
    }


    /**
     * 业务失败记录日志
     *
     * @param customDataLogDto customDataLogDto
     */
    private void fail(CustomDataLogDto customDataLogDto) {
        customDataLogDto.setSuccessFlag(false);
        CustomDataLogEntity customDataLogEntity = customDataLogConvertor.toEntity(customDataLogDto);
        customDataLogMapper.insert(customDataLogEntity);
    }

}
