package com.vedeng.erp.kingdee.batch.job;


import cn.hutool.core.date.DateUtil;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.processor.BatchSaleExchangeInAcceptanceFormProcessor;
import com.vedeng.erp.kingdee.batch.processor.BatchSaleExchangeInProcessor;
import com.vedeng.erp.kingdee.batch.processor.BatchSaleExchangeOutAcceptanceFormProcessor;
import com.vedeng.erp.kingdee.batch.processor.BatchSaleExchangeOutProcessor;
import com.vedeng.erp.kingdee.batch.writer.BatchSaleExchangeInWriter;
import com.vedeng.erp.kingdee.batch.writer.BatchSaleExchangeOutWriter;
import com.vedeng.erp.kingdee.batch.writer.CommonFileDataWriter;
import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 销售单换货出入库 job
 * @date 2023/1/6 10:30
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class SaleOrderAfterSaleExchangeBatchJob extends BaseJob {

    @Autowired
    private BatchSaleExchangeInProcessor batchSaleExchangeInProcessor;
    @Autowired
    private BatchSaleExchangeInWriter batchSaleExchangeInWriter;

    @Autowired
    private BatchSaleExchangeOutProcessor batchSaleExchangeOutProcessor;
    @Autowired
    private BatchSaleExchangeOutWriter batchSaleExchangeOutWriter;

    @Autowired
    private BatchSaleExchangeInAcceptanceFormProcessor saleExchangeInAcceptanceFormProcessor;

    @Autowired
    private BatchSaleExchangeOutAcceptanceFormProcessor saleExchangeOutAcceptanceFormProcessor;

    @Autowired
    private CommonFileDataWriter commonFileDataWriter;

    public Job saleOrderAfterSaleExchangeFlowJob() {
        return jobBuilderFactory.get("saleOrderAfterSaleExchangeFlowJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(saleExchangeInBill())
                .next(acceptanceForm())
                .next(saleExchangeOutBill())
                .next(acceptanceOutForm())
                .build();
    }


    /**
     * 换货入库
     */
    private Step saleExchangeInBill() {
        return stepBuilderFactory.get("销售换货入库")
                .<BatchWarehouseGoodsOutInDto, KingDeeStorageInDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchAfterSaleOrderExchangeInItemReader(null, null))
                .processor(batchSaleExchangeInProcessor)
                .writer(batchSaleExchangeInWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 换货出库
     */
    private Step saleExchangeOutBill() {
        return stepBuilderFactory.get("销售换货出库")
                .<BatchWarehouseGoodsOutInDto, KingDeeStorageInDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchAfterSaleOrderExchangeOutItemReader(null, null))
                .processor(batchSaleExchangeOutProcessor)
                .writer(batchSaleExchangeOutWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }


    /**
     * 验收单
     *
     * @return
     */
    private Step acceptanceForm() {
        return stepBuilderFactory.get("销售换货入库验收单")
                .<BatchWarehouseGoodsOutInDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchAfterSaleOrderExchangeInItemReader(null, null))
                .processor(saleExchangeInAcceptanceFormProcessor)
                .writer(commonFileDataWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 复核单
     *
     * @return
     */
    private Step acceptanceOutForm() {
        return stepBuilderFactory.get("销售换货出库复核单")
                .<BatchWarehouseGoodsOutInDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchAfterSaleOrderExchangeOutItemReader(null, null))
                .processor(saleExchangeOutAcceptanceFormProcessor)
                .writer(commonFileDataWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }


    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> batchAfterSaleOrderExchangeOutItemReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        return getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(beginTime, endTime, 4);
    }


    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> batchAfterSaleOrderExchangeInItemReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        return getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(beginTime, endTime, 3);
    }

    private CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(
            String beginTime, String endTime, Integer outInType) {
        BatchWarehouseGoodsOutInDto warehouseGoodsOutInDto = BatchWarehouseGoodsOutInDto
                .builder()
                .outInType(outInType)
                .isDelete(0)
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(beginTime))
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(endTime))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchWarehouseGoodsOutInDto.class.getSimpleName(), warehouseGoodsOutInDto);
    }


}
