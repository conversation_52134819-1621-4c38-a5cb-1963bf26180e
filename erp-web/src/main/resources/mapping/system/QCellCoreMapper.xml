<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.system.dao.QCellCoreMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.system.model.QCellCore" >
    <id column="QCELLCORE_ID" property="qcellcoreId" jdbcType="INTEGER" />
    <result column="PHONE" property="phone" jdbcType="VARCHAR" />
    <result column="PROVINCE" property="province" jdbcType="VARCHAR" />
    <result column="CITY" property="city" jdbcType="VARCHAR" />
    <result column="OPERATPR" property="operatpr" jdbcType="VARCHAR" />
    <result column="CODE" property="code" jdbcType="VARCHAR" />
    <result column="ZIP" property="zip" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    QCELLCORE_ID, PHONE, PROVINCE, CITY, OPERATPR, CODE, ZIP
  </sql>
  <insert id="saveQCellCore">
    insert into T_QCELLCORE (PHONE, PROVINCE, CITY, OPERATPR, CODE, ZIP) value (
        #{phone},#{province},#{city},#{operatpr},#{code},#{zip}
    )
  </insert>

  <select id="getQCellCoreByPhone" resultMap="BaseResultMap" parameterType="java.lang.String">
  	select 
    <include refid="Base_Column_List" />
    from T_QCELLCORE
    where PHONE = #{phone,jdbcType=VARCHAR}
  </select>
  <select id="getQCellCoreByCode" resultMap="BaseResultMap" parameterType="java.lang.String">
  	select 
    <include refid="Base_Column_List" />
    from T_QCELLCORE
    where CODE = #{code,jdbcType=VARCHAR} limit 1
  </select>
</mapper>