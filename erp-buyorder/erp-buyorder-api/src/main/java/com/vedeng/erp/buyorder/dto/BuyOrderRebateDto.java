package com.vedeng.erp.buyorder.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 采购订单返利Dto
 */
@Data
public class BuyOrderRebateDto {

    /**
     * 采购单id
     */
    private Integer buyorderId;

    /**
     * 采购单号
     */
    private String buyorderNo;

    /**
     * 供应商id
     */
    private Integer traderId;

    /**
     * 之前使用返利的供应商id
     */
    private Integer oldTraderId;

    /**
     * 采购商品返利集合
     */
    private List<BuyOrderGoodsRebateDto> newbuyOrderGoodsRebateList;

    /**
     * 之前使用的采购商品返利集合
     */
    private List<BuyOrderGoodsRebateDto> oldBuyOrderGoodsRebateList;

    @Data
    public static class BuyOrderGoodsRebateDto {

        /**
         * 采购商品id
         */
        private Integer buyorderGoodsId;

        /**
         * 商品名称
         */
        private String goodsName;

        /**
         * 返利单价
         */
        private BigDecimal rebatePrice;

        /**
         * 返利数量
         */
        private BigDecimal rebateNum;

        /**
         * 返利总额
         */
        private BigDecimal rebateAmount;

    }

}
