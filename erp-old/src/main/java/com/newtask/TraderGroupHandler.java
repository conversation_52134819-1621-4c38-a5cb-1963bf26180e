package com.newtask;

import com.alibaba.fastjson.TypeReference;
import com.newtask.service.TraderGroupService;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.http.HttpRestClientUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.crm.api.dto.customergroup.TraderGroupEXTDto;
import com.vedeng.system.service.OrganizationService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@JobHandler(value = "traderGroupHandler")
@Component
public class TraderGroupHandler extends AbstractJobHandler {

    private Logger logger= LoggerFactory.getLogger(TraderGroupHandler.class);
    @Value("${crm_url}")
    protected String crmUrl;

    @Value("${b2b_department_name}")
    protected String departmentName;
    @Autowired
    private OrganizationService organizationService;

    @Autowired
    private TraderGroupService traderGroupService;
    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        XxlJobLogger.log("开始处理客户分群的计算");
        logger.info("开始处理客户分群的计算");
        List<TraderGroupEXTDto> groups=getGroups();
        if(CollectionUtils.isEmpty(groups)){
            XxlJobLogger.log("没有要分群的群组");
            return ReturnT.SUCCESS;
        }
        List<Integer> organizations=organizationService.getChildOrgByName(departmentName);
        if(StringUtil.isNotBlank(s)&&StringUtil.isNumeric(s)){
            Integer id=Integer.valueOf(s);
            boolean has=false;
            for(TraderGroupEXTDto g:groups){
                if(g!=null&&id.equals(g.getTraderGroupId())){
                    traderGroupService.handleOneGroup(g,organizations);
                    has=true;
                }
            }
            if(!has){
                XxlJobLogger.log("该群组不存在");
            }
            XxlJobLogger.log("结束处理客户分群的计算");
            logger.info("结束处理客户分群的计算");
            return ReturnT.SUCCESS;
        }

        traderGroupService.handleTraderGroup(groups,organizations);
        XxlJobLogger.log("结束处理客户分群的计算");
        logger.info("结束处理客户分群的计算");
        return ReturnT.SUCCESS;
    }

    private List<TraderGroupEXTDto> getGroups(){
        TypeReference<RestfulResult<List<TraderGroupEXTDto>>> typeReference=new TypeReference<RestfulResult<List<TraderGroupEXTDto>>>(){};
        String url=crmUrl+"api/customergroup/index";
        RestfulResult<List<TraderGroupEXTDto>> result = HttpRestClientUtil.restPost(url, typeReference, null, null);
        if(result!=null&&result.isSuccess()){
            return result.getData();
        }
        return null;
    }
}
