package com.vedeng.goods.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class GoodsGenerateExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_GOODS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_GOODS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_GOODS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public GoodsGenerateExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_GOODS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andGoodsIdIsNull() {
            addCriterion("GOODS_ID is null");
            return (Criteria) this;
        }

        public Criteria andGoodsIdIsNotNull() {
            addCriterion("GOODS_ID is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsIdEqualTo(Integer value) {
            addCriterion("GOODS_ID =", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdNotEqualTo(Integer value) {
            addCriterion("GOODS_ID <>", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdGreaterThan(Integer value) {
            addCriterion("GOODS_ID >", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("GOODS_ID >=", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdLessThan(Integer value) {
            addCriterion("GOODS_ID <", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdLessThanOrEqualTo(Integer value) {
            addCriterion("GOODS_ID <=", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdIn(List<Integer> values) {
            addCriterion("GOODS_ID in", values, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdNotIn(List<Integer> values) {
            addCriterion("GOODS_ID not in", values, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdBetween(Integer value1, Integer value2) {
            addCriterion("GOODS_ID between", value1, value2, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdNotBetween(Integer value1, Integer value2) {
            addCriterion("GOODS_ID not between", value1, value2, "goodsId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("COMPANY_ID is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("COMPANY_ID is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Integer value) {
            addCriterion("COMPANY_ID =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Integer value) {
            addCriterion("COMPANY_ID <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Integer value) {
            addCriterion("COMPANY_ID >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("COMPANY_ID >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Integer value) {
            addCriterion("COMPANY_ID <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Integer value) {
            addCriterion("COMPANY_ID <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Integer> values) {
            addCriterion("COMPANY_ID in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Integer> values) {
            addCriterion("COMPANY_ID not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Integer value1, Integer value2) {
            addCriterion("COMPANY_ID between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Integer value1, Integer value2) {
            addCriterion("COMPANY_ID not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andParentIdIsNull() {
            addCriterion("PARENT_ID is null");
            return (Criteria) this;
        }

        public Criteria andParentIdIsNotNull() {
            addCriterion("PARENT_ID is not null");
            return (Criteria) this;
        }

        public Criteria andParentIdEqualTo(Integer value) {
            addCriterion("PARENT_ID =", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotEqualTo(Integer value) {
            addCriterion("PARENT_ID <>", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdGreaterThan(Integer value) {
            addCriterion("PARENT_ID >", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("PARENT_ID >=", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdLessThan(Integer value) {
            addCriterion("PARENT_ID <", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdLessThanOrEqualTo(Integer value) {
            addCriterion("PARENT_ID <=", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdIn(List<Integer> values) {
            addCriterion("PARENT_ID in", values, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotIn(List<Integer> values) {
            addCriterion("PARENT_ID not in", values, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdBetween(Integer value1, Integer value2) {
            addCriterion("PARENT_ID between", value1, value2, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotBetween(Integer value1, Integer value2) {
            addCriterion("PARENT_ID not between", value1, value2, "parentId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNull() {
            addCriterion("CATEGORY_ID is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNotNull() {
            addCriterion("CATEGORY_ID is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdEqualTo(Integer value) {
            addCriterion("CATEGORY_ID =", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotEqualTo(Integer value) {
            addCriterion("CATEGORY_ID <>", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThan(Integer value) {
            addCriterion("CATEGORY_ID >", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("CATEGORY_ID >=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThan(Integer value) {
            addCriterion("CATEGORY_ID <", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThanOrEqualTo(Integer value) {
            addCriterion("CATEGORY_ID <=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIn(List<Integer> values) {
            addCriterion("CATEGORY_ID in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotIn(List<Integer> values) {
            addCriterion("CATEGORY_ID not in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdBetween(Integer value1, Integer value2) {
            addCriterion("CATEGORY_ID between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotBetween(Integer value1, Integer value2) {
            addCriterion("CATEGORY_ID not between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andBrandIdIsNull() {
            addCriterion("BRAND_ID is null");
            return (Criteria) this;
        }

        public Criteria andBrandIdIsNotNull() {
            addCriterion("BRAND_ID is not null");
            return (Criteria) this;
        }

        public Criteria andBrandIdEqualTo(Integer value) {
            addCriterion("BRAND_ID =", value, "brandId");
            return (Criteria) this;
        }

        public Criteria andBrandIdNotEqualTo(Integer value) {
            addCriterion("BRAND_ID <>", value, "brandId");
            return (Criteria) this;
        }

        public Criteria andBrandIdGreaterThan(Integer value) {
            addCriterion("BRAND_ID >", value, "brandId");
            return (Criteria) this;
        }

        public Criteria andBrandIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("BRAND_ID >=", value, "brandId");
            return (Criteria) this;
        }

        public Criteria andBrandIdLessThan(Integer value) {
            addCriterion("BRAND_ID <", value, "brandId");
            return (Criteria) this;
        }

        public Criteria andBrandIdLessThanOrEqualTo(Integer value) {
            addCriterion("BRAND_ID <=", value, "brandId");
            return (Criteria) this;
        }

        public Criteria andBrandIdIn(List<Integer> values) {
            addCriterion("BRAND_ID in", values, "brandId");
            return (Criteria) this;
        }

        public Criteria andBrandIdNotIn(List<Integer> values) {
            addCriterion("BRAND_ID not in", values, "brandId");
            return (Criteria) this;
        }

        public Criteria andBrandIdBetween(Integer value1, Integer value2) {
            addCriterion("BRAND_ID between", value1, value2, "brandId");
            return (Criteria) this;
        }

        public Criteria andBrandIdNotBetween(Integer value1, Integer value2) {
            addCriterion("BRAND_ID not between", value1, value2, "brandId");
            return (Criteria) this;
        }

        public Criteria andIsOnSaleIsNull() {
            addCriterion("IS_ON_SALE is null");
            return (Criteria) this;
        }

        public Criteria andIsOnSaleIsNotNull() {
            addCriterion("IS_ON_SALE is not null");
            return (Criteria) this;
        }

        public Criteria andIsOnSaleEqualTo(Byte value) {
            addCriterion("IS_ON_SALE =", value, "isOnSale");
            return (Criteria) this;
        }

        public Criteria andIsOnSaleNotEqualTo(Byte value) {
            addCriterion("IS_ON_SALE <>", value, "isOnSale");
            return (Criteria) this;
        }

        public Criteria andIsOnSaleGreaterThan(Byte value) {
            addCriterion("IS_ON_SALE >", value, "isOnSale");
            return (Criteria) this;
        }

        public Criteria andIsOnSaleGreaterThanOrEqualTo(Byte value) {
            addCriterion("IS_ON_SALE >=", value, "isOnSale");
            return (Criteria) this;
        }

        public Criteria andIsOnSaleLessThan(Byte value) {
            addCriterion("IS_ON_SALE <", value, "isOnSale");
            return (Criteria) this;
        }

        public Criteria andIsOnSaleLessThanOrEqualTo(Byte value) {
            addCriterion("IS_ON_SALE <=", value, "isOnSale");
            return (Criteria) this;
        }

        public Criteria andIsOnSaleIn(List<Byte> values) {
            addCriterion("IS_ON_SALE in", values, "isOnSale");
            return (Criteria) this;
        }

        public Criteria andIsOnSaleNotIn(List<Byte> values) {
            addCriterion("IS_ON_SALE not in", values, "isOnSale");
            return (Criteria) this;
        }

        public Criteria andIsOnSaleBetween(Byte value1, Byte value2) {
            addCriterion("IS_ON_SALE between", value1, value2, "isOnSale");
            return (Criteria) this;
        }

        public Criteria andIsOnSaleNotBetween(Byte value1, Byte value2) {
            addCriterion("IS_ON_SALE not between", value1, value2, "isOnSale");
            return (Criteria) this;
        }

        public Criteria andIsDiscardIsNull() {
            addCriterion("IS_DISCARD is null");
            return (Criteria) this;
        }

        public Criteria andIsDiscardIsNotNull() {
            addCriterion("IS_DISCARD is not null");
            return (Criteria) this;
        }

        public Criteria andIsDiscardEqualTo(Integer value) {
            addCriterion("IS_DISCARD =", value, "isDiscard");
            return (Criteria) this;
        }

        public Criteria andIsDiscardNotEqualTo(Integer value) {
            addCriterion("IS_DISCARD <>", value, "isDiscard");
            return (Criteria) this;
        }

        public Criteria andIsDiscardGreaterThan(Integer value) {
            addCriterion("IS_DISCARD >", value, "isDiscard");
            return (Criteria) this;
        }

        public Criteria andIsDiscardGreaterThanOrEqualTo(Integer value) {
            addCriterion("IS_DISCARD >=", value, "isDiscard");
            return (Criteria) this;
        }

        public Criteria andIsDiscardLessThan(Integer value) {
            addCriterion("IS_DISCARD <", value, "isDiscard");
            return (Criteria) this;
        }

        public Criteria andIsDiscardLessThanOrEqualTo(Integer value) {
            addCriterion("IS_DISCARD <=", value, "isDiscard");
            return (Criteria) this;
        }

        public Criteria andIsDiscardIn(List<Integer> values) {
            addCriterion("IS_DISCARD in", values, "isDiscard");
            return (Criteria) this;
        }

        public Criteria andIsDiscardNotIn(List<Integer> values) {
            addCriterion("IS_DISCARD not in", values, "isDiscard");
            return (Criteria) this;
        }

        public Criteria andIsDiscardBetween(Integer value1, Integer value2) {
            addCriterion("IS_DISCARD between", value1, value2, "isDiscard");
            return (Criteria) this;
        }

        public Criteria andIsDiscardNotBetween(Integer value1, Integer value2) {
            addCriterion("IS_DISCARD not between", value1, value2, "isDiscard");
            return (Criteria) this;
        }

        public Criteria andSkuIsNull() {
            addCriterion("SKU is null");
            return (Criteria) this;
        }

        public Criteria andSkuIsNotNull() {
            addCriterion("SKU is not null");
            return (Criteria) this;
        }

        public Criteria andSkuEqualTo(String value) {
            addCriterion("SKU =", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotEqualTo(String value) {
            addCriterion("SKU <>", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuGreaterThan(String value) {
            addCriterion("SKU >", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuGreaterThanOrEqualTo(String value) {
            addCriterion("SKU >=", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuLessThan(String value) {
            addCriterion("SKU <", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuLessThanOrEqualTo(String value) {
            addCriterion("SKU <=", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuLike(String value) {
            addCriterion("SKU like", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotLike(String value) {
            addCriterion("SKU not like", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuIn(List<String> values) {
            addCriterion("SKU in", values, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotIn(List<String> values) {
            addCriterion("SKU not in", values, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuBetween(String value1, String value2) {
            addCriterion("SKU between", value1, value2, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotBetween(String value1, String value2) {
            addCriterion("SKU not between", value1, value2, "sku");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIsNull() {
            addCriterion("GOODS_NAME is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIsNotNull() {
            addCriterion("GOODS_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNameEqualTo(String value) {
            addCriterion("GOODS_NAME =", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotEqualTo(String value) {
            addCriterion("GOODS_NAME <>", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameGreaterThan(String value) {
            addCriterion("GOODS_NAME >", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameGreaterThanOrEqualTo(String value) {
            addCriterion("GOODS_NAME >=", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLessThan(String value) {
            addCriterion("GOODS_NAME <", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLessThanOrEqualTo(String value) {
            addCriterion("GOODS_NAME <=", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLike(String value) {
            addCriterion("GOODS_NAME like", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotLike(String value) {
            addCriterion("GOODS_NAME not like", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIn(List<String> values) {
            addCriterion("GOODS_NAME in", values, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotIn(List<String> values) {
            addCriterion("GOODS_NAME not in", values, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameBetween(String value1, String value2) {
            addCriterion("GOODS_NAME between", value1, value2, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotBetween(String value1, String value2) {
            addCriterion("GOODS_NAME not between", value1, value2, "goodsName");
            return (Criteria) this;
        }

        public Criteria andAliasNameIsNull() {
            addCriterion("ALIAS_NAME is null");
            return (Criteria) this;
        }

        public Criteria andAliasNameIsNotNull() {
            addCriterion("ALIAS_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andAliasNameEqualTo(String value) {
            addCriterion("ALIAS_NAME =", value, "aliasName");
            return (Criteria) this;
        }

        public Criteria andAliasNameNotEqualTo(String value) {
            addCriterion("ALIAS_NAME <>", value, "aliasName");
            return (Criteria) this;
        }

        public Criteria andAliasNameGreaterThan(String value) {
            addCriterion("ALIAS_NAME >", value, "aliasName");
            return (Criteria) this;
        }

        public Criteria andAliasNameGreaterThanOrEqualTo(String value) {
            addCriterion("ALIAS_NAME >=", value, "aliasName");
            return (Criteria) this;
        }

        public Criteria andAliasNameLessThan(String value) {
            addCriterion("ALIAS_NAME <", value, "aliasName");
            return (Criteria) this;
        }

        public Criteria andAliasNameLessThanOrEqualTo(String value) {
            addCriterion("ALIAS_NAME <=", value, "aliasName");
            return (Criteria) this;
        }

        public Criteria andAliasNameLike(String value) {
            addCriterion("ALIAS_NAME like", value, "aliasName");
            return (Criteria) this;
        }

        public Criteria andAliasNameNotLike(String value) {
            addCriterion("ALIAS_NAME not like", value, "aliasName");
            return (Criteria) this;
        }

        public Criteria andAliasNameIn(List<String> values) {
            addCriterion("ALIAS_NAME in", values, "aliasName");
            return (Criteria) this;
        }

        public Criteria andAliasNameNotIn(List<String> values) {
            addCriterion("ALIAS_NAME not in", values, "aliasName");
            return (Criteria) this;
        }

        public Criteria andAliasNameBetween(String value1, String value2) {
            addCriterion("ALIAS_NAME between", value1, value2, "aliasName");
            return (Criteria) this;
        }

        public Criteria andAliasNameNotBetween(String value1, String value2) {
            addCriterion("ALIAS_NAME not between", value1, value2, "aliasName");
            return (Criteria) this;
        }

        public Criteria andModelIsNull() {
            addCriterion("MODEL is null");
            return (Criteria) this;
        }

        public Criteria andModelIsNotNull() {
            addCriterion("MODEL is not null");
            return (Criteria) this;
        }

        public Criteria andModelEqualTo(String value) {
            addCriterion("MODEL =", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotEqualTo(String value) {
            addCriterion("MODEL <>", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThan(String value) {
            addCriterion("MODEL >", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualTo(String value) {
            addCriterion("MODEL >=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThan(String value) {
            addCriterion("MODEL <", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualTo(String value) {
            addCriterion("MODEL <=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLike(String value) {
            addCriterion("MODEL like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotLike(String value) {
            addCriterion("MODEL not like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelIn(List<String> values) {
            addCriterion("MODEL in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotIn(List<String> values) {
            addCriterion("MODEL not in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelBetween(String value1, String value2) {
            addCriterion("MODEL between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotBetween(String value1, String value2) {
            addCriterion("MODEL not between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeIsNull() {
            addCriterion("MATERIAL_CODE is null");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeIsNotNull() {
            addCriterion("MATERIAL_CODE is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeEqualTo(String value) {
            addCriterion("MATERIAL_CODE =", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeNotEqualTo(String value) {
            addCriterion("MATERIAL_CODE <>", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeGreaterThan(String value) {
            addCriterion("MATERIAL_CODE >", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeGreaterThanOrEqualTo(String value) {
            addCriterion("MATERIAL_CODE >=", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeLessThan(String value) {
            addCriterion("MATERIAL_CODE <", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeLessThanOrEqualTo(String value) {
            addCriterion("MATERIAL_CODE <=", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeLike(String value) {
            addCriterion("MATERIAL_CODE like", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeNotLike(String value) {
            addCriterion("MATERIAL_CODE not like", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeIn(List<String> values) {
            addCriterion("MATERIAL_CODE in", values, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeNotIn(List<String> values) {
            addCriterion("MATERIAL_CODE not in", values, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeBetween(String value1, String value2) {
            addCriterion("MATERIAL_CODE between", value1, value2, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeNotBetween(String value1, String value2) {
            addCriterion("MATERIAL_CODE not between", value1, value2, "materialCode");
            return (Criteria) this;
        }

        public Criteria andBaseUnitIdIsNull() {
            addCriterion("BASE_UNIT_ID is null");
            return (Criteria) this;
        }

        public Criteria andBaseUnitIdIsNotNull() {
            addCriterion("BASE_UNIT_ID is not null");
            return (Criteria) this;
        }

        public Criteria andBaseUnitIdEqualTo(Integer value) {
            addCriterion("BASE_UNIT_ID =", value, "baseUnitId");
            return (Criteria) this;
        }

        public Criteria andBaseUnitIdNotEqualTo(Integer value) {
            addCriterion("BASE_UNIT_ID <>", value, "baseUnitId");
            return (Criteria) this;
        }

        public Criteria andBaseUnitIdGreaterThan(Integer value) {
            addCriterion("BASE_UNIT_ID >", value, "baseUnitId");
            return (Criteria) this;
        }

        public Criteria andBaseUnitIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("BASE_UNIT_ID >=", value, "baseUnitId");
            return (Criteria) this;
        }

        public Criteria andBaseUnitIdLessThan(Integer value) {
            addCriterion("BASE_UNIT_ID <", value, "baseUnitId");
            return (Criteria) this;
        }

        public Criteria andBaseUnitIdLessThanOrEqualTo(Integer value) {
            addCriterion("BASE_UNIT_ID <=", value, "baseUnitId");
            return (Criteria) this;
        }

        public Criteria andBaseUnitIdIn(List<Integer> values) {
            addCriterion("BASE_UNIT_ID in", values, "baseUnitId");
            return (Criteria) this;
        }

        public Criteria andBaseUnitIdNotIn(List<Integer> values) {
            addCriterion("BASE_UNIT_ID not in", values, "baseUnitId");
            return (Criteria) this;
        }

        public Criteria andBaseUnitIdBetween(Integer value1, Integer value2) {
            addCriterion("BASE_UNIT_ID between", value1, value2, "baseUnitId");
            return (Criteria) this;
        }

        public Criteria andBaseUnitIdNotBetween(Integer value1, Integer value2) {
            addCriterion("BASE_UNIT_ID not between", value1, value2, "baseUnitId");
            return (Criteria) this;
        }

        public Criteria andChangeNumIsNull() {
            addCriterion("CHANGE_NUM is null");
            return (Criteria) this;
        }

        public Criteria andChangeNumIsNotNull() {
            addCriterion("CHANGE_NUM is not null");
            return (Criteria) this;
        }

        public Criteria andChangeNumEqualTo(Integer value) {
            addCriterion("CHANGE_NUM =", value, "changeNum");
            return (Criteria) this;
        }

        public Criteria andChangeNumNotEqualTo(Integer value) {
            addCriterion("CHANGE_NUM <>", value, "changeNum");
            return (Criteria) this;
        }

        public Criteria andChangeNumGreaterThan(Integer value) {
            addCriterion("CHANGE_NUM >", value, "changeNum");
            return (Criteria) this;
        }

        public Criteria andChangeNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("CHANGE_NUM >=", value, "changeNum");
            return (Criteria) this;
        }

        public Criteria andChangeNumLessThan(Integer value) {
            addCriterion("CHANGE_NUM <", value, "changeNum");
            return (Criteria) this;
        }

        public Criteria andChangeNumLessThanOrEqualTo(Integer value) {
            addCriterion("CHANGE_NUM <=", value, "changeNum");
            return (Criteria) this;
        }

        public Criteria andChangeNumIn(List<Integer> values) {
            addCriterion("CHANGE_NUM in", values, "changeNum");
            return (Criteria) this;
        }

        public Criteria andChangeNumNotIn(List<Integer> values) {
            addCriterion("CHANGE_NUM not in", values, "changeNum");
            return (Criteria) this;
        }

        public Criteria andChangeNumBetween(Integer value1, Integer value2) {
            addCriterion("CHANGE_NUM between", value1, value2, "changeNum");
            return (Criteria) this;
        }

        public Criteria andChangeNumNotBetween(Integer value1, Integer value2) {
            addCriterion("CHANGE_NUM not between", value1, value2, "changeNum");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNull() {
            addCriterion("UNIT_ID is null");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNotNull() {
            addCriterion("UNIT_ID is not null");
            return (Criteria) this;
        }

        public Criteria andUnitIdEqualTo(Integer value) {
            addCriterion("UNIT_ID =", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotEqualTo(Integer value) {
            addCriterion("UNIT_ID <>", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThan(Integer value) {
            addCriterion("UNIT_ID >", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("UNIT_ID >=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThan(Integer value) {
            addCriterion("UNIT_ID <", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThanOrEqualTo(Integer value) {
            addCriterion("UNIT_ID <=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdIn(List<Integer> values) {
            addCriterion("UNIT_ID in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotIn(List<Integer> values) {
            addCriterion("UNIT_ID not in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdBetween(Integer value1, Integer value2) {
            addCriterion("UNIT_ID between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotBetween(Integer value1, Integer value2) {
            addCriterion("UNIT_ID not between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andGrossWeightIsNull() {
            addCriterion("GROSS_WEIGHT is null");
            return (Criteria) this;
        }

        public Criteria andGrossWeightIsNotNull() {
            addCriterion("GROSS_WEIGHT is not null");
            return (Criteria) this;
        }

        public Criteria andGrossWeightEqualTo(BigDecimal value) {
            addCriterion("GROSS_WEIGHT =", value, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightNotEqualTo(BigDecimal value) {
            addCriterion("GROSS_WEIGHT <>", value, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightGreaterThan(BigDecimal value) {
            addCriterion("GROSS_WEIGHT >", value, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("GROSS_WEIGHT >=", value, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightLessThan(BigDecimal value) {
            addCriterion("GROSS_WEIGHT <", value, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightLessThanOrEqualTo(BigDecimal value) {
            addCriterion("GROSS_WEIGHT <=", value, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightIn(List<BigDecimal> values) {
            addCriterion("GROSS_WEIGHT in", values, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightNotIn(List<BigDecimal> values) {
            addCriterion("GROSS_WEIGHT not in", values, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("GROSS_WEIGHT between", value1, value2, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("GROSS_WEIGHT not between", value1, value2, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andNetWeightIsNull() {
            addCriterion("NET_WEIGHT is null");
            return (Criteria) this;
        }

        public Criteria andNetWeightIsNotNull() {
            addCriterion("NET_WEIGHT is not null");
            return (Criteria) this;
        }

        public Criteria andNetWeightEqualTo(BigDecimal value) {
            addCriterion("NET_WEIGHT =", value, "netWeight");
            return (Criteria) this;
        }

        public Criteria andNetWeightNotEqualTo(BigDecimal value) {
            addCriterion("NET_WEIGHT <>", value, "netWeight");
            return (Criteria) this;
        }

        public Criteria andNetWeightGreaterThan(BigDecimal value) {
            addCriterion("NET_WEIGHT >", value, "netWeight");
            return (Criteria) this;
        }

        public Criteria andNetWeightGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("NET_WEIGHT >=", value, "netWeight");
            return (Criteria) this;
        }

        public Criteria andNetWeightLessThan(BigDecimal value) {
            addCriterion("NET_WEIGHT <", value, "netWeight");
            return (Criteria) this;
        }

        public Criteria andNetWeightLessThanOrEqualTo(BigDecimal value) {
            addCriterion("NET_WEIGHT <=", value, "netWeight");
            return (Criteria) this;
        }

        public Criteria andNetWeightIn(List<BigDecimal> values) {
            addCriterion("NET_WEIGHT in", values, "netWeight");
            return (Criteria) this;
        }

        public Criteria andNetWeightNotIn(List<BigDecimal> values) {
            addCriterion("NET_WEIGHT not in", values, "netWeight");
            return (Criteria) this;
        }

        public Criteria andNetWeightBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("NET_WEIGHT between", value1, value2, "netWeight");
            return (Criteria) this;
        }

        public Criteria andNetWeightNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("NET_WEIGHT not between", value1, value2, "netWeight");
            return (Criteria) this;
        }

        public Criteria andGoodsLengthIsNull() {
            addCriterion("GOODS_LENGTH is null");
            return (Criteria) this;
        }

        public Criteria andGoodsLengthIsNotNull() {
            addCriterion("GOODS_LENGTH is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsLengthEqualTo(BigDecimal value) {
            addCriterion("GOODS_LENGTH =", value, "goodsLength");
            return (Criteria) this;
        }

        public Criteria andGoodsLengthNotEqualTo(BigDecimal value) {
            addCriterion("GOODS_LENGTH <>", value, "goodsLength");
            return (Criteria) this;
        }

        public Criteria andGoodsLengthGreaterThan(BigDecimal value) {
            addCriterion("GOODS_LENGTH >", value, "goodsLength");
            return (Criteria) this;
        }

        public Criteria andGoodsLengthGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("GOODS_LENGTH >=", value, "goodsLength");
            return (Criteria) this;
        }

        public Criteria andGoodsLengthLessThan(BigDecimal value) {
            addCriterion("GOODS_LENGTH <", value, "goodsLength");
            return (Criteria) this;
        }

        public Criteria andGoodsLengthLessThanOrEqualTo(BigDecimal value) {
            addCriterion("GOODS_LENGTH <=", value, "goodsLength");
            return (Criteria) this;
        }

        public Criteria andGoodsLengthIn(List<BigDecimal> values) {
            addCriterion("GOODS_LENGTH in", values, "goodsLength");
            return (Criteria) this;
        }

        public Criteria andGoodsLengthNotIn(List<BigDecimal> values) {
            addCriterion("GOODS_LENGTH not in", values, "goodsLength");
            return (Criteria) this;
        }

        public Criteria andGoodsLengthBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("GOODS_LENGTH between", value1, value2, "goodsLength");
            return (Criteria) this;
        }

        public Criteria andGoodsLengthNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("GOODS_LENGTH not between", value1, value2, "goodsLength");
            return (Criteria) this;
        }

        public Criteria andGoodsWidthIsNull() {
            addCriterion("GOODS_WIDTH is null");
            return (Criteria) this;
        }

        public Criteria andGoodsWidthIsNotNull() {
            addCriterion("GOODS_WIDTH is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsWidthEqualTo(BigDecimal value) {
            addCriterion("GOODS_WIDTH =", value, "goodsWidth");
            return (Criteria) this;
        }

        public Criteria andGoodsWidthNotEqualTo(BigDecimal value) {
            addCriterion("GOODS_WIDTH <>", value, "goodsWidth");
            return (Criteria) this;
        }

        public Criteria andGoodsWidthGreaterThan(BigDecimal value) {
            addCriterion("GOODS_WIDTH >", value, "goodsWidth");
            return (Criteria) this;
        }

        public Criteria andGoodsWidthGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("GOODS_WIDTH >=", value, "goodsWidth");
            return (Criteria) this;
        }

        public Criteria andGoodsWidthLessThan(BigDecimal value) {
            addCriterion("GOODS_WIDTH <", value, "goodsWidth");
            return (Criteria) this;
        }

        public Criteria andGoodsWidthLessThanOrEqualTo(BigDecimal value) {
            addCriterion("GOODS_WIDTH <=", value, "goodsWidth");
            return (Criteria) this;
        }

        public Criteria andGoodsWidthIn(List<BigDecimal> values) {
            addCriterion("GOODS_WIDTH in", values, "goodsWidth");
            return (Criteria) this;
        }

        public Criteria andGoodsWidthNotIn(List<BigDecimal> values) {
            addCriterion("GOODS_WIDTH not in", values, "goodsWidth");
            return (Criteria) this;
        }

        public Criteria andGoodsWidthBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("GOODS_WIDTH between", value1, value2, "goodsWidth");
            return (Criteria) this;
        }

        public Criteria andGoodsWidthNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("GOODS_WIDTH not between", value1, value2, "goodsWidth");
            return (Criteria) this;
        }

        public Criteria andGoodsHeightIsNull() {
            addCriterion("GOODS_HEIGHT is null");
            return (Criteria) this;
        }

        public Criteria andGoodsHeightIsNotNull() {
            addCriterion("GOODS_HEIGHT is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsHeightEqualTo(BigDecimal value) {
            addCriterion("GOODS_HEIGHT =", value, "goodsHeight");
            return (Criteria) this;
        }

        public Criteria andGoodsHeightNotEqualTo(BigDecimal value) {
            addCriterion("GOODS_HEIGHT <>", value, "goodsHeight");
            return (Criteria) this;
        }

        public Criteria andGoodsHeightGreaterThan(BigDecimal value) {
            addCriterion("GOODS_HEIGHT >", value, "goodsHeight");
            return (Criteria) this;
        }

        public Criteria andGoodsHeightGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("GOODS_HEIGHT >=", value, "goodsHeight");
            return (Criteria) this;
        }

        public Criteria andGoodsHeightLessThan(BigDecimal value) {
            addCriterion("GOODS_HEIGHT <", value, "goodsHeight");
            return (Criteria) this;
        }

        public Criteria andGoodsHeightLessThanOrEqualTo(BigDecimal value) {
            addCriterion("GOODS_HEIGHT <=", value, "goodsHeight");
            return (Criteria) this;
        }

        public Criteria andGoodsHeightIn(List<BigDecimal> values) {
            addCriterion("GOODS_HEIGHT in", values, "goodsHeight");
            return (Criteria) this;
        }

        public Criteria andGoodsHeightNotIn(List<BigDecimal> values) {
            addCriterion("GOODS_HEIGHT not in", values, "goodsHeight");
            return (Criteria) this;
        }

        public Criteria andGoodsHeightBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("GOODS_HEIGHT between", value1, value2, "goodsHeight");
            return (Criteria) this;
        }

        public Criteria andGoodsHeightNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("GOODS_HEIGHT not between", value1, value2, "goodsHeight");
            return (Criteria) this;
        }

        public Criteria andPackageLengthIsNull() {
            addCriterion("PACKAGE_LENGTH is null");
            return (Criteria) this;
        }

        public Criteria andPackageLengthIsNotNull() {
            addCriterion("PACKAGE_LENGTH is not null");
            return (Criteria) this;
        }

        public Criteria andPackageLengthEqualTo(BigDecimal value) {
            addCriterion("PACKAGE_LENGTH =", value, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthNotEqualTo(BigDecimal value) {
            addCriterion("PACKAGE_LENGTH <>", value, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthGreaterThan(BigDecimal value) {
            addCriterion("PACKAGE_LENGTH >", value, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("PACKAGE_LENGTH >=", value, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthLessThan(BigDecimal value) {
            addCriterion("PACKAGE_LENGTH <", value, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthLessThanOrEqualTo(BigDecimal value) {
            addCriterion("PACKAGE_LENGTH <=", value, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthIn(List<BigDecimal> values) {
            addCriterion("PACKAGE_LENGTH in", values, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthNotIn(List<BigDecimal> values) {
            addCriterion("PACKAGE_LENGTH not in", values, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("PACKAGE_LENGTH between", value1, value2, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("PACKAGE_LENGTH not between", value1, value2, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageWidthIsNull() {
            addCriterion("PACKAGE_WIDTH is null");
            return (Criteria) this;
        }

        public Criteria andPackageWidthIsNotNull() {
            addCriterion("PACKAGE_WIDTH is not null");
            return (Criteria) this;
        }

        public Criteria andPackageWidthEqualTo(BigDecimal value) {
            addCriterion("PACKAGE_WIDTH =", value, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthNotEqualTo(BigDecimal value) {
            addCriterion("PACKAGE_WIDTH <>", value, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthGreaterThan(BigDecimal value) {
            addCriterion("PACKAGE_WIDTH >", value, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("PACKAGE_WIDTH >=", value, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthLessThan(BigDecimal value) {
            addCriterion("PACKAGE_WIDTH <", value, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthLessThanOrEqualTo(BigDecimal value) {
            addCriterion("PACKAGE_WIDTH <=", value, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthIn(List<BigDecimal> values) {
            addCriterion("PACKAGE_WIDTH in", values, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthNotIn(List<BigDecimal> values) {
            addCriterion("PACKAGE_WIDTH not in", values, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("PACKAGE_WIDTH between", value1, value2, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("PACKAGE_WIDTH not between", value1, value2, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageHeightIsNull() {
            addCriterion("PACKAGE_HEIGHT is null");
            return (Criteria) this;
        }

        public Criteria andPackageHeightIsNotNull() {
            addCriterion("PACKAGE_HEIGHT is not null");
            return (Criteria) this;
        }

        public Criteria andPackageHeightEqualTo(BigDecimal value) {
            addCriterion("PACKAGE_HEIGHT =", value, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightNotEqualTo(BigDecimal value) {
            addCriterion("PACKAGE_HEIGHT <>", value, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightGreaterThan(BigDecimal value) {
            addCriterion("PACKAGE_HEIGHT >", value, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("PACKAGE_HEIGHT >=", value, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightLessThan(BigDecimal value) {
            addCriterion("PACKAGE_HEIGHT <", value, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightLessThanOrEqualTo(BigDecimal value) {
            addCriterion("PACKAGE_HEIGHT <=", value, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightIn(List<BigDecimal> values) {
            addCriterion("PACKAGE_HEIGHT in", values, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightNotIn(List<BigDecimal> values) {
            addCriterion("PACKAGE_HEIGHT not in", values, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("PACKAGE_HEIGHT between", value1, value2, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("PACKAGE_HEIGHT not between", value1, value2, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andGoodsTypeIsNull() {
            addCriterion("GOODS_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andGoodsTypeIsNotNull() {
            addCriterion("GOODS_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsTypeEqualTo(Integer value) {
            addCriterion("GOODS_TYPE =", value, "goodsType");
            return (Criteria) this;
        }

        public Criteria andGoodsTypeNotEqualTo(Integer value) {
            addCriterion("GOODS_TYPE <>", value, "goodsType");
            return (Criteria) this;
        }

        public Criteria andGoodsTypeGreaterThan(Integer value) {
            addCriterion("GOODS_TYPE >", value, "goodsType");
            return (Criteria) this;
        }

        public Criteria andGoodsTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("GOODS_TYPE >=", value, "goodsType");
            return (Criteria) this;
        }

        public Criteria andGoodsTypeLessThan(Integer value) {
            addCriterion("GOODS_TYPE <", value, "goodsType");
            return (Criteria) this;
        }

        public Criteria andGoodsTypeLessThanOrEqualTo(Integer value) {
            addCriterion("GOODS_TYPE <=", value, "goodsType");
            return (Criteria) this;
        }

        public Criteria andGoodsTypeIn(List<Integer> values) {
            addCriterion("GOODS_TYPE in", values, "goodsType");
            return (Criteria) this;
        }

        public Criteria andGoodsTypeNotIn(List<Integer> values) {
            addCriterion("GOODS_TYPE not in", values, "goodsType");
            return (Criteria) this;
        }

        public Criteria andGoodsTypeBetween(Integer value1, Integer value2) {
            addCriterion("GOODS_TYPE between", value1, value2, "goodsType");
            return (Criteria) this;
        }

        public Criteria andGoodsTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("GOODS_TYPE not between", value1, value2, "goodsType");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelIsNull() {
            addCriterion("GOODS_LEVEL is null");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelIsNotNull() {
            addCriterion("GOODS_LEVEL is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelEqualTo(Integer value) {
            addCriterion("GOODS_LEVEL =", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelNotEqualTo(Integer value) {
            addCriterion("GOODS_LEVEL <>", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelGreaterThan(Integer value) {
            addCriterion("GOODS_LEVEL >", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelGreaterThanOrEqualTo(Integer value) {
            addCriterion("GOODS_LEVEL >=", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelLessThan(Integer value) {
            addCriterion("GOODS_LEVEL <", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelLessThanOrEqualTo(Integer value) {
            addCriterion("GOODS_LEVEL <=", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelIn(List<Integer> values) {
            addCriterion("GOODS_LEVEL in", values, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelNotIn(List<Integer> values) {
            addCriterion("GOODS_LEVEL not in", values, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelBetween(Integer value1, Integer value2) {
            addCriterion("GOODS_LEVEL between", value1, value2, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelNotBetween(Integer value1, Integer value2) {
            addCriterion("GOODS_LEVEL not between", value1, value2, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andManageCategoryIsNull() {
            addCriterion("MANAGE_CATEGORY is null");
            return (Criteria) this;
        }

        public Criteria andManageCategoryIsNotNull() {
            addCriterion("MANAGE_CATEGORY is not null");
            return (Criteria) this;
        }

        public Criteria andManageCategoryEqualTo(Integer value) {
            addCriterion("MANAGE_CATEGORY =", value, "manageCategory");
            return (Criteria) this;
        }

        public Criteria andManageCategoryNotEqualTo(Integer value) {
            addCriterion("MANAGE_CATEGORY <>", value, "manageCategory");
            return (Criteria) this;
        }

        public Criteria andManageCategoryGreaterThan(Integer value) {
            addCriterion("MANAGE_CATEGORY >", value, "manageCategory");
            return (Criteria) this;
        }

        public Criteria andManageCategoryGreaterThanOrEqualTo(Integer value) {
            addCriterion("MANAGE_CATEGORY >=", value, "manageCategory");
            return (Criteria) this;
        }

        public Criteria andManageCategoryLessThan(Integer value) {
            addCriterion("MANAGE_CATEGORY <", value, "manageCategory");
            return (Criteria) this;
        }

        public Criteria andManageCategoryLessThanOrEqualTo(Integer value) {
            addCriterion("MANAGE_CATEGORY <=", value, "manageCategory");
            return (Criteria) this;
        }

        public Criteria andManageCategoryIn(List<Integer> values) {
            addCriterion("MANAGE_CATEGORY in", values, "manageCategory");
            return (Criteria) this;
        }

        public Criteria andManageCategoryNotIn(List<Integer> values) {
            addCriterion("MANAGE_CATEGORY not in", values, "manageCategory");
            return (Criteria) this;
        }

        public Criteria andManageCategoryBetween(Integer value1, Integer value2) {
            addCriterion("MANAGE_CATEGORY between", value1, value2, "manageCategory");
            return (Criteria) this;
        }

        public Criteria andManageCategoryNotBetween(Integer value1, Integer value2) {
            addCriterion("MANAGE_CATEGORY not between", value1, value2, "manageCategory");
            return (Criteria) this;
        }

        public Criteria andManageCategoryLevelIsNull() {
            addCriterion("MANAGE_CATEGORY_LEVEL is null");
            return (Criteria) this;
        }

        public Criteria andManageCategoryLevelIsNotNull() {
            addCriterion("MANAGE_CATEGORY_LEVEL is not null");
            return (Criteria) this;
        }

        public Criteria andManageCategoryLevelEqualTo(Integer value) {
            addCriterion("MANAGE_CATEGORY_LEVEL =", value, "manageCategoryLevel");
            return (Criteria) this;
        }

        public Criteria andManageCategoryLevelNotEqualTo(Integer value) {
            addCriterion("MANAGE_CATEGORY_LEVEL <>", value, "manageCategoryLevel");
            return (Criteria) this;
        }

        public Criteria andManageCategoryLevelGreaterThan(Integer value) {
            addCriterion("MANAGE_CATEGORY_LEVEL >", value, "manageCategoryLevel");
            return (Criteria) this;
        }

        public Criteria andManageCategoryLevelGreaterThanOrEqualTo(Integer value) {
            addCriterion("MANAGE_CATEGORY_LEVEL >=", value, "manageCategoryLevel");
            return (Criteria) this;
        }

        public Criteria andManageCategoryLevelLessThan(Integer value) {
            addCriterion("MANAGE_CATEGORY_LEVEL <", value, "manageCategoryLevel");
            return (Criteria) this;
        }

        public Criteria andManageCategoryLevelLessThanOrEqualTo(Integer value) {
            addCriterion("MANAGE_CATEGORY_LEVEL <=", value, "manageCategoryLevel");
            return (Criteria) this;
        }

        public Criteria andManageCategoryLevelIn(List<Integer> values) {
            addCriterion("MANAGE_CATEGORY_LEVEL in", values, "manageCategoryLevel");
            return (Criteria) this;
        }

        public Criteria andManageCategoryLevelNotIn(List<Integer> values) {
            addCriterion("MANAGE_CATEGORY_LEVEL not in", values, "manageCategoryLevel");
            return (Criteria) this;
        }

        public Criteria andManageCategoryLevelBetween(Integer value1, Integer value2) {
            addCriterion("MANAGE_CATEGORY_LEVEL between", value1, value2, "manageCategoryLevel");
            return (Criteria) this;
        }

        public Criteria andManageCategoryLevelNotBetween(Integer value1, Integer value2) {
            addCriterion("MANAGE_CATEGORY_LEVEL not between", value1, value2, "manageCategoryLevel");
            return (Criteria) this;
        }

        public Criteria andPurchaseRemindIsNull() {
            addCriterion("PURCHASE_REMIND is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseRemindIsNotNull() {
            addCriterion("PURCHASE_REMIND is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseRemindEqualTo(String value) {
            addCriterion("PURCHASE_REMIND =", value, "purchaseRemind");
            return (Criteria) this;
        }

        public Criteria andPurchaseRemindNotEqualTo(String value) {
            addCriterion("PURCHASE_REMIND <>", value, "purchaseRemind");
            return (Criteria) this;
        }

        public Criteria andPurchaseRemindGreaterThan(String value) {
            addCriterion("PURCHASE_REMIND >", value, "purchaseRemind");
            return (Criteria) this;
        }

        public Criteria andPurchaseRemindGreaterThanOrEqualTo(String value) {
            addCriterion("PURCHASE_REMIND >=", value, "purchaseRemind");
            return (Criteria) this;
        }

        public Criteria andPurchaseRemindLessThan(String value) {
            addCriterion("PURCHASE_REMIND <", value, "purchaseRemind");
            return (Criteria) this;
        }

        public Criteria andPurchaseRemindLessThanOrEqualTo(String value) {
            addCriterion("PURCHASE_REMIND <=", value, "purchaseRemind");
            return (Criteria) this;
        }

        public Criteria andPurchaseRemindLike(String value) {
            addCriterion("PURCHASE_REMIND like", value, "purchaseRemind");
            return (Criteria) this;
        }

        public Criteria andPurchaseRemindNotLike(String value) {
            addCriterion("PURCHASE_REMIND not like", value, "purchaseRemind");
            return (Criteria) this;
        }

        public Criteria andPurchaseRemindIn(List<String> values) {
            addCriterion("PURCHASE_REMIND in", values, "purchaseRemind");
            return (Criteria) this;
        }

        public Criteria andPurchaseRemindNotIn(List<String> values) {
            addCriterion("PURCHASE_REMIND not in", values, "purchaseRemind");
            return (Criteria) this;
        }

        public Criteria andPurchaseRemindBetween(String value1, String value2) {
            addCriterion("PURCHASE_REMIND between", value1, value2, "purchaseRemind");
            return (Criteria) this;
        }

        public Criteria andPurchaseRemindNotBetween(String value1, String value2) {
            addCriterion("PURCHASE_REMIND not between", value1, value2, "purchaseRemind");
            return (Criteria) this;
        }

        public Criteria andLicenseNumberIsNull() {
            addCriterion("LICENSE_NUMBER is null");
            return (Criteria) this;
        }

        public Criteria andLicenseNumberIsNotNull() {
            addCriterion("LICENSE_NUMBER is not null");
            return (Criteria) this;
        }

        public Criteria andLicenseNumberEqualTo(String value) {
            addCriterion("LICENSE_NUMBER =", value, "licenseNumber");
            return (Criteria) this;
        }

        public Criteria andLicenseNumberNotEqualTo(String value) {
            addCriterion("LICENSE_NUMBER <>", value, "licenseNumber");
            return (Criteria) this;
        }

        public Criteria andLicenseNumberGreaterThan(String value) {
            addCriterion("LICENSE_NUMBER >", value, "licenseNumber");
            return (Criteria) this;
        }

        public Criteria andLicenseNumberGreaterThanOrEqualTo(String value) {
            addCriterion("LICENSE_NUMBER >=", value, "licenseNumber");
            return (Criteria) this;
        }

        public Criteria andLicenseNumberLessThan(String value) {
            addCriterion("LICENSE_NUMBER <", value, "licenseNumber");
            return (Criteria) this;
        }

        public Criteria andLicenseNumberLessThanOrEqualTo(String value) {
            addCriterion("LICENSE_NUMBER <=", value, "licenseNumber");
            return (Criteria) this;
        }

        public Criteria andLicenseNumberLike(String value) {
            addCriterion("LICENSE_NUMBER like", value, "licenseNumber");
            return (Criteria) this;
        }

        public Criteria andLicenseNumberNotLike(String value) {
            addCriterion("LICENSE_NUMBER not like", value, "licenseNumber");
            return (Criteria) this;
        }

        public Criteria andLicenseNumberIn(List<String> values) {
            addCriterion("LICENSE_NUMBER in", values, "licenseNumber");
            return (Criteria) this;
        }

        public Criteria andLicenseNumberNotIn(List<String> values) {
            addCriterion("LICENSE_NUMBER not in", values, "licenseNumber");
            return (Criteria) this;
        }

        public Criteria andLicenseNumberBetween(String value1, String value2) {
            addCriterion("LICENSE_NUMBER between", value1, value2, "licenseNumber");
            return (Criteria) this;
        }

        public Criteria andLicenseNumberNotBetween(String value1, String value2) {
            addCriterion("LICENSE_NUMBER not between", value1, value2, "licenseNumber");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdIsNull() {
            addCriterion("FIRST_ENGAGE_ID is null");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdIsNotNull() {
            addCriterion("FIRST_ENGAGE_ID is not null");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdEqualTo(Integer value) {
            addCriterion("FIRST_ENGAGE_ID =", value, "firstEngageId");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdNotEqualTo(Integer value) {
            addCriterion("FIRST_ENGAGE_ID <>", value, "firstEngageId");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdGreaterThan(Integer value) {
            addCriterion("FIRST_ENGAGE_ID >", value, "firstEngageId");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("FIRST_ENGAGE_ID >=", value, "firstEngageId");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdLessThan(Integer value) {
            addCriterion("FIRST_ENGAGE_ID <", value, "firstEngageId");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdLessThanOrEqualTo(Integer value) {
            addCriterion("FIRST_ENGAGE_ID <=", value, "firstEngageId");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdIn(List<Integer> values) {
            addCriterion("FIRST_ENGAGE_ID in", values, "firstEngageId");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdNotIn(List<Integer> values) {
            addCriterion("FIRST_ENGAGE_ID not in", values, "firstEngageId");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdBetween(Integer value1, Integer value2) {
            addCriterion("FIRST_ENGAGE_ID between", value1, value2, "firstEngageId");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdNotBetween(Integer value1, Integer value2) {
            addCriterion("FIRST_ENGAGE_ID not between", value1, value2, "firstEngageId");
            return (Criteria) this;
        }

        public Criteria andRecordNumberIsNull() {
            addCriterion("RECORD_NUMBER is null");
            return (Criteria) this;
        }

        public Criteria andRecordNumberIsNotNull() {
            addCriterion("RECORD_NUMBER is not null");
            return (Criteria) this;
        }

        public Criteria andRecordNumberEqualTo(String value) {
            addCriterion("RECORD_NUMBER =", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberNotEqualTo(String value) {
            addCriterion("RECORD_NUMBER <>", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberGreaterThan(String value) {
            addCriterion("RECORD_NUMBER >", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberGreaterThanOrEqualTo(String value) {
            addCriterion("RECORD_NUMBER >=", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberLessThan(String value) {
            addCriterion("RECORD_NUMBER <", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberLessThanOrEqualTo(String value) {
            addCriterion("RECORD_NUMBER <=", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberLike(String value) {
            addCriterion("RECORD_NUMBER like", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberNotLike(String value) {
            addCriterion("RECORD_NUMBER not like", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberIn(List<String> values) {
            addCriterion("RECORD_NUMBER in", values, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberNotIn(List<String> values) {
            addCriterion("RECORD_NUMBER not in", values, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberBetween(String value1, String value2) {
            addCriterion("RECORD_NUMBER between", value1, value2, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberNotBetween(String value1, String value2) {
            addCriterion("RECORD_NUMBER not between", value1, value2, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIsNull() {
            addCriterion("REGISTRATION_NUMBER is null");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIsNotNull() {
            addCriterion("REGISTRATION_NUMBER is not null");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberEqualTo(String value) {
            addCriterion("REGISTRATION_NUMBER =", value, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberNotEqualTo(String value) {
            addCriterion("REGISTRATION_NUMBER <>", value, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberGreaterThan(String value) {
            addCriterion("REGISTRATION_NUMBER >", value, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberGreaterThanOrEqualTo(String value) {
            addCriterion("REGISTRATION_NUMBER >=", value, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberLessThan(String value) {
            addCriterion("REGISTRATION_NUMBER <", value, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberLessThanOrEqualTo(String value) {
            addCriterion("REGISTRATION_NUMBER <=", value, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberLike(String value) {
            addCriterion("REGISTRATION_NUMBER like", value, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberNotLike(String value) {
            addCriterion("REGISTRATION_NUMBER not like", value, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIn(List<String> values) {
            addCriterion("REGISTRATION_NUMBER in", values, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberNotIn(List<String> values) {
            addCriterion("REGISTRATION_NUMBER not in", values, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberBetween(String value1, String value2) {
            addCriterion("REGISTRATION_NUMBER between", value1, value2, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberNotBetween(String value1, String value2) {
            addCriterion("REGISTRATION_NUMBER not between", value1, value2, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andBegintimeIsNull() {
            addCriterion("BEGINTIME is null");
            return (Criteria) this;
        }

        public Criteria andBegintimeIsNotNull() {
            addCriterion("BEGINTIME is not null");
            return (Criteria) this;
        }

        public Criteria andBegintimeEqualTo(Long value) {
            addCriterion("BEGINTIME =", value, "begintime");
            return (Criteria) this;
        }

        public Criteria andBegintimeNotEqualTo(Long value) {
            addCriterion("BEGINTIME <>", value, "begintime");
            return (Criteria) this;
        }

        public Criteria andBegintimeGreaterThan(Long value) {
            addCriterion("BEGINTIME >", value, "begintime");
            return (Criteria) this;
        }

        public Criteria andBegintimeGreaterThanOrEqualTo(Long value) {
            addCriterion("BEGINTIME >=", value, "begintime");
            return (Criteria) this;
        }

        public Criteria andBegintimeLessThan(Long value) {
            addCriterion("BEGINTIME <", value, "begintime");
            return (Criteria) this;
        }

        public Criteria andBegintimeLessThanOrEqualTo(Long value) {
            addCriterion("BEGINTIME <=", value, "begintime");
            return (Criteria) this;
        }

        public Criteria andBegintimeIn(List<Long> values) {
            addCriterion("BEGINTIME in", values, "begintime");
            return (Criteria) this;
        }

        public Criteria andBegintimeNotIn(List<Long> values) {
            addCriterion("BEGINTIME not in", values, "begintime");
            return (Criteria) this;
        }

        public Criteria andBegintimeBetween(Long value1, Long value2) {
            addCriterion("BEGINTIME between", value1, value2, "begintime");
            return (Criteria) this;
        }

        public Criteria andBegintimeNotBetween(Long value1, Long value2) {
            addCriterion("BEGINTIME not between", value1, value2, "begintime");
            return (Criteria) this;
        }

        public Criteria andEndtimeIsNull() {
            addCriterion("ENDTIME is null");
            return (Criteria) this;
        }

        public Criteria andEndtimeIsNotNull() {
            addCriterion("ENDTIME is not null");
            return (Criteria) this;
        }

        public Criteria andEndtimeEqualTo(Long value) {
            addCriterion("ENDTIME =", value, "endtime");
            return (Criteria) this;
        }

        public Criteria andEndtimeNotEqualTo(Long value) {
            addCriterion("ENDTIME <>", value, "endtime");
            return (Criteria) this;
        }

        public Criteria andEndtimeGreaterThan(Long value) {
            addCriterion("ENDTIME >", value, "endtime");
            return (Criteria) this;
        }

        public Criteria andEndtimeGreaterThanOrEqualTo(Long value) {
            addCriterion("ENDTIME >=", value, "endtime");
            return (Criteria) this;
        }

        public Criteria andEndtimeLessThan(Long value) {
            addCriterion("ENDTIME <", value, "endtime");
            return (Criteria) this;
        }

        public Criteria andEndtimeLessThanOrEqualTo(Long value) {
            addCriterion("ENDTIME <=", value, "endtime");
            return (Criteria) this;
        }

        public Criteria andEndtimeIn(List<Long> values) {
            addCriterion("ENDTIME in", values, "endtime");
            return (Criteria) this;
        }

        public Criteria andEndtimeNotIn(List<Long> values) {
            addCriterion("ENDTIME not in", values, "endtime");
            return (Criteria) this;
        }

        public Criteria andEndtimeBetween(Long value1, Long value2) {
            addCriterion("ENDTIME between", value1, value2, "endtime");
            return (Criteria) this;
        }

        public Criteria andEndtimeNotBetween(Long value1, Long value2) {
            addCriterion("ENDTIME not between", value1, value2, "endtime");
            return (Criteria) this;
        }

        public Criteria andAuthorizationCertificateUrlIsNull() {
            addCriterion("AUTHORIZATION_CERTIFICATE_URL is null");
            return (Criteria) this;
        }

        public Criteria andAuthorizationCertificateUrlIsNotNull() {
            addCriterion("AUTHORIZATION_CERTIFICATE_URL is not null");
            return (Criteria) this;
        }

        public Criteria andAuthorizationCertificateUrlEqualTo(String value) {
            addCriterion("AUTHORIZATION_CERTIFICATE_URL =", value, "authorizationCertificateUrl");
            return (Criteria) this;
        }

        public Criteria andAuthorizationCertificateUrlNotEqualTo(String value) {
            addCriterion("AUTHORIZATION_CERTIFICATE_URL <>", value, "authorizationCertificateUrl");
            return (Criteria) this;
        }

        public Criteria andAuthorizationCertificateUrlGreaterThan(String value) {
            addCriterion("AUTHORIZATION_CERTIFICATE_URL >", value, "authorizationCertificateUrl");
            return (Criteria) this;
        }

        public Criteria andAuthorizationCertificateUrlGreaterThanOrEqualTo(String value) {
            addCriterion("AUTHORIZATION_CERTIFICATE_URL >=", value, "authorizationCertificateUrl");
            return (Criteria) this;
        }

        public Criteria andAuthorizationCertificateUrlLessThan(String value) {
            addCriterion("AUTHORIZATION_CERTIFICATE_URL <", value, "authorizationCertificateUrl");
            return (Criteria) this;
        }

        public Criteria andAuthorizationCertificateUrlLessThanOrEqualTo(String value) {
            addCriterion("AUTHORIZATION_CERTIFICATE_URL <=", value, "authorizationCertificateUrl");
            return (Criteria) this;
        }

        public Criteria andAuthorizationCertificateUrlLike(String value) {
            addCriterion("AUTHORIZATION_CERTIFICATE_URL like", value, "authorizationCertificateUrl");
            return (Criteria) this;
        }

        public Criteria andAuthorizationCertificateUrlNotLike(String value) {
            addCriterion("AUTHORIZATION_CERTIFICATE_URL not like", value, "authorizationCertificateUrl");
            return (Criteria) this;
        }

        public Criteria andAuthorizationCertificateUrlIn(List<String> values) {
            addCriterion("AUTHORIZATION_CERTIFICATE_URL in", values, "authorizationCertificateUrl");
            return (Criteria) this;
        }

        public Criteria andAuthorizationCertificateUrlNotIn(List<String> values) {
            addCriterion("AUTHORIZATION_CERTIFICATE_URL not in", values, "authorizationCertificateUrl");
            return (Criteria) this;
        }

        public Criteria andAuthorizationCertificateUrlBetween(String value1, String value2) {
            addCriterion("AUTHORIZATION_CERTIFICATE_URL between", value1, value2, "authorizationCertificateUrl");
            return (Criteria) this;
        }

        public Criteria andAuthorizationCertificateUrlNotBetween(String value1, String value2) {
            addCriterion("AUTHORIZATION_CERTIFICATE_URL not between", value1, value2, "authorizationCertificateUrl");
            return (Criteria) this;
        }

        public Criteria andOtherQualificationUrlIsNull() {
            addCriterion("OTHER_QUALIFICATION_URL is null");
            return (Criteria) this;
        }

        public Criteria andOtherQualificationUrlIsNotNull() {
            addCriterion("OTHER_QUALIFICATION_URL is not null");
            return (Criteria) this;
        }

        public Criteria andOtherQualificationUrlEqualTo(String value) {
            addCriterion("OTHER_QUALIFICATION_URL =", value, "otherQualificationUrl");
            return (Criteria) this;
        }

        public Criteria andOtherQualificationUrlNotEqualTo(String value) {
            addCriterion("OTHER_QUALIFICATION_URL <>", value, "otherQualificationUrl");
            return (Criteria) this;
        }

        public Criteria andOtherQualificationUrlGreaterThan(String value) {
            addCriterion("OTHER_QUALIFICATION_URL >", value, "otherQualificationUrl");
            return (Criteria) this;
        }

        public Criteria andOtherQualificationUrlGreaterThanOrEqualTo(String value) {
            addCriterion("OTHER_QUALIFICATION_URL >=", value, "otherQualificationUrl");
            return (Criteria) this;
        }

        public Criteria andOtherQualificationUrlLessThan(String value) {
            addCriterion("OTHER_QUALIFICATION_URL <", value, "otherQualificationUrl");
            return (Criteria) this;
        }

        public Criteria andOtherQualificationUrlLessThanOrEqualTo(String value) {
            addCriterion("OTHER_QUALIFICATION_URL <=", value, "otherQualificationUrl");
            return (Criteria) this;
        }

        public Criteria andOtherQualificationUrlLike(String value) {
            addCriterion("OTHER_QUALIFICATION_URL like", value, "otherQualificationUrl");
            return (Criteria) this;
        }

        public Criteria andOtherQualificationUrlNotLike(String value) {
            addCriterion("OTHER_QUALIFICATION_URL not like", value, "otherQualificationUrl");
            return (Criteria) this;
        }

        public Criteria andOtherQualificationUrlIn(List<String> values) {
            addCriterion("OTHER_QUALIFICATION_URL in", values, "otherQualificationUrl");
            return (Criteria) this;
        }

        public Criteria andOtherQualificationUrlNotIn(List<String> values) {
            addCriterion("OTHER_QUALIFICATION_URL not in", values, "otherQualificationUrl");
            return (Criteria) this;
        }

        public Criteria andOtherQualificationUrlBetween(String value1, String value2) {
            addCriterion("OTHER_QUALIFICATION_URL between", value1, value2, "otherQualificationUrl");
            return (Criteria) this;
        }

        public Criteria andOtherQualificationUrlNotBetween(String value1, String value2) {
            addCriterion("OTHER_QUALIFICATION_URL not between", value1, value2, "otherQualificationUrl");
            return (Criteria) this;
        }

        public Criteria andColorPageUrlIsNull() {
            addCriterion("COLOR_PAGE_URL is null");
            return (Criteria) this;
        }

        public Criteria andColorPageUrlIsNotNull() {
            addCriterion("COLOR_PAGE_URL is not null");
            return (Criteria) this;
        }

        public Criteria andColorPageUrlEqualTo(String value) {
            addCriterion("COLOR_PAGE_URL =", value, "colorPageUrl");
            return (Criteria) this;
        }

        public Criteria andColorPageUrlNotEqualTo(String value) {
            addCriterion("COLOR_PAGE_URL <>", value, "colorPageUrl");
            return (Criteria) this;
        }

        public Criteria andColorPageUrlGreaterThan(String value) {
            addCriterion("COLOR_PAGE_URL >", value, "colorPageUrl");
            return (Criteria) this;
        }

        public Criteria andColorPageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("COLOR_PAGE_URL >=", value, "colorPageUrl");
            return (Criteria) this;
        }

        public Criteria andColorPageUrlLessThan(String value) {
            addCriterion("COLOR_PAGE_URL <", value, "colorPageUrl");
            return (Criteria) this;
        }

        public Criteria andColorPageUrlLessThanOrEqualTo(String value) {
            addCriterion("COLOR_PAGE_URL <=", value, "colorPageUrl");
            return (Criteria) this;
        }

        public Criteria andColorPageUrlLike(String value) {
            addCriterion("COLOR_PAGE_URL like", value, "colorPageUrl");
            return (Criteria) this;
        }

        public Criteria andColorPageUrlNotLike(String value) {
            addCriterion("COLOR_PAGE_URL not like", value, "colorPageUrl");
            return (Criteria) this;
        }

        public Criteria andColorPageUrlIn(List<String> values) {
            addCriterion("COLOR_PAGE_URL in", values, "colorPageUrl");
            return (Criteria) this;
        }

        public Criteria andColorPageUrlNotIn(List<String> values) {
            addCriterion("COLOR_PAGE_URL not in", values, "colorPageUrl");
            return (Criteria) this;
        }

        public Criteria andColorPageUrlBetween(String value1, String value2) {
            addCriterion("COLOR_PAGE_URL between", value1, value2, "colorPageUrl");
            return (Criteria) this;
        }

        public Criteria andColorPageUrlNotBetween(String value1, String value2) {
            addCriterion("COLOR_PAGE_URL not between", value1, value2, "colorPageUrl");
            return (Criteria) this;
        }

        public Criteria andTechnicalParameterUrlIsNull() {
            addCriterion("TECHNICAL_PARAMETER_URL is null");
            return (Criteria) this;
        }

        public Criteria andTechnicalParameterUrlIsNotNull() {
            addCriterion("TECHNICAL_PARAMETER_URL is not null");
            return (Criteria) this;
        }

        public Criteria andTechnicalParameterUrlEqualTo(String value) {
            addCriterion("TECHNICAL_PARAMETER_URL =", value, "technicalParameterUrl");
            return (Criteria) this;
        }

        public Criteria andTechnicalParameterUrlNotEqualTo(String value) {
            addCriterion("TECHNICAL_PARAMETER_URL <>", value, "technicalParameterUrl");
            return (Criteria) this;
        }

        public Criteria andTechnicalParameterUrlGreaterThan(String value) {
            addCriterion("TECHNICAL_PARAMETER_URL >", value, "technicalParameterUrl");
            return (Criteria) this;
        }

        public Criteria andTechnicalParameterUrlGreaterThanOrEqualTo(String value) {
            addCriterion("TECHNICAL_PARAMETER_URL >=", value, "technicalParameterUrl");
            return (Criteria) this;
        }

        public Criteria andTechnicalParameterUrlLessThan(String value) {
            addCriterion("TECHNICAL_PARAMETER_URL <", value, "technicalParameterUrl");
            return (Criteria) this;
        }

        public Criteria andTechnicalParameterUrlLessThanOrEqualTo(String value) {
            addCriterion("TECHNICAL_PARAMETER_URL <=", value, "technicalParameterUrl");
            return (Criteria) this;
        }

        public Criteria andTechnicalParameterUrlLike(String value) {
            addCriterion("TECHNICAL_PARAMETER_URL like", value, "technicalParameterUrl");
            return (Criteria) this;
        }

        public Criteria andTechnicalParameterUrlNotLike(String value) {
            addCriterion("TECHNICAL_PARAMETER_URL not like", value, "technicalParameterUrl");
            return (Criteria) this;
        }

        public Criteria andTechnicalParameterUrlIn(List<String> values) {
            addCriterion("TECHNICAL_PARAMETER_URL in", values, "technicalParameterUrl");
            return (Criteria) this;
        }

        public Criteria andTechnicalParameterUrlNotIn(List<String> values) {
            addCriterion("TECHNICAL_PARAMETER_URL not in", values, "technicalParameterUrl");
            return (Criteria) this;
        }

        public Criteria andTechnicalParameterUrlBetween(String value1, String value2) {
            addCriterion("TECHNICAL_PARAMETER_URL between", value1, value2, "technicalParameterUrl");
            return (Criteria) this;
        }

        public Criteria andTechnicalParameterUrlNotBetween(String value1, String value2) {
            addCriterion("TECHNICAL_PARAMETER_URL not between", value1, value2, "technicalParameterUrl");
            return (Criteria) this;
        }

        public Criteria andInstructionsUrlIsNull() {
            addCriterion("INSTRUCTIONS_URL is null");
            return (Criteria) this;
        }

        public Criteria andInstructionsUrlIsNotNull() {
            addCriterion("INSTRUCTIONS_URL is not null");
            return (Criteria) this;
        }

        public Criteria andInstructionsUrlEqualTo(String value) {
            addCriterion("INSTRUCTIONS_URL =", value, "instructionsUrl");
            return (Criteria) this;
        }

        public Criteria andInstructionsUrlNotEqualTo(String value) {
            addCriterion("INSTRUCTIONS_URL <>", value, "instructionsUrl");
            return (Criteria) this;
        }

        public Criteria andInstructionsUrlGreaterThan(String value) {
            addCriterion("INSTRUCTIONS_URL >", value, "instructionsUrl");
            return (Criteria) this;
        }

        public Criteria andInstructionsUrlGreaterThanOrEqualTo(String value) {
            addCriterion("INSTRUCTIONS_URL >=", value, "instructionsUrl");
            return (Criteria) this;
        }

        public Criteria andInstructionsUrlLessThan(String value) {
            addCriterion("INSTRUCTIONS_URL <", value, "instructionsUrl");
            return (Criteria) this;
        }

        public Criteria andInstructionsUrlLessThanOrEqualTo(String value) {
            addCriterion("INSTRUCTIONS_URL <=", value, "instructionsUrl");
            return (Criteria) this;
        }

        public Criteria andInstructionsUrlLike(String value) {
            addCriterion("INSTRUCTIONS_URL like", value, "instructionsUrl");
            return (Criteria) this;
        }

        public Criteria andInstructionsUrlNotLike(String value) {
            addCriterion("INSTRUCTIONS_URL not like", value, "instructionsUrl");
            return (Criteria) this;
        }

        public Criteria andInstructionsUrlIn(List<String> values) {
            addCriterion("INSTRUCTIONS_URL in", values, "instructionsUrl");
            return (Criteria) this;
        }

        public Criteria andInstructionsUrlNotIn(List<String> values) {
            addCriterion("INSTRUCTIONS_URL not in", values, "instructionsUrl");
            return (Criteria) this;
        }

        public Criteria andInstructionsUrlBetween(String value1, String value2) {
            addCriterion("INSTRUCTIONS_URL between", value1, value2, "instructionsUrl");
            return (Criteria) this;
        }

        public Criteria andInstructionsUrlNotBetween(String value1, String value2) {
            addCriterion("INSTRUCTIONS_URL not between", value1, value2, "instructionsUrl");
            return (Criteria) this;
        }

        public Criteria andBiddingDataUrlIsNull() {
            addCriterion("BIDDING_DATA_URL is null");
            return (Criteria) this;
        }

        public Criteria andBiddingDataUrlIsNotNull() {
            addCriterion("BIDDING_DATA_URL is not null");
            return (Criteria) this;
        }

        public Criteria andBiddingDataUrlEqualTo(String value) {
            addCriterion("BIDDING_DATA_URL =", value, "biddingDataUrl");
            return (Criteria) this;
        }

        public Criteria andBiddingDataUrlNotEqualTo(String value) {
            addCriterion("BIDDING_DATA_URL <>", value, "biddingDataUrl");
            return (Criteria) this;
        }

        public Criteria andBiddingDataUrlGreaterThan(String value) {
            addCriterion("BIDDING_DATA_URL >", value, "biddingDataUrl");
            return (Criteria) this;
        }

        public Criteria andBiddingDataUrlGreaterThanOrEqualTo(String value) {
            addCriterion("BIDDING_DATA_URL >=", value, "biddingDataUrl");
            return (Criteria) this;
        }

        public Criteria andBiddingDataUrlLessThan(String value) {
            addCriterion("BIDDING_DATA_URL <", value, "biddingDataUrl");
            return (Criteria) this;
        }

        public Criteria andBiddingDataUrlLessThanOrEqualTo(String value) {
            addCriterion("BIDDING_DATA_URL <=", value, "biddingDataUrl");
            return (Criteria) this;
        }

        public Criteria andBiddingDataUrlLike(String value) {
            addCriterion("BIDDING_DATA_URL like", value, "biddingDataUrl");
            return (Criteria) this;
        }

        public Criteria andBiddingDataUrlNotLike(String value) {
            addCriterion("BIDDING_DATA_URL not like", value, "biddingDataUrl");
            return (Criteria) this;
        }

        public Criteria andBiddingDataUrlIn(List<String> values) {
            addCriterion("BIDDING_DATA_URL in", values, "biddingDataUrl");
            return (Criteria) this;
        }

        public Criteria andBiddingDataUrlNotIn(List<String> values) {
            addCriterion("BIDDING_DATA_URL not in", values, "biddingDataUrl");
            return (Criteria) this;
        }

        public Criteria andBiddingDataUrlBetween(String value1, String value2) {
            addCriterion("BIDDING_DATA_URL between", value1, value2, "biddingDataUrl");
            return (Criteria) this;
        }

        public Criteria andBiddingDataUrlNotBetween(String value1, String value2) {
            addCriterion("BIDDING_DATA_URL not between", value1, value2, "biddingDataUrl");
            return (Criteria) this;
        }

        public Criteria andPackingListIsNull() {
            addCriterion("PACKING_LIST is null");
            return (Criteria) this;
        }

        public Criteria andPackingListIsNotNull() {
            addCriterion("PACKING_LIST is not null");
            return (Criteria) this;
        }

        public Criteria andPackingListEqualTo(String value) {
            addCriterion("PACKING_LIST =", value, "packingList");
            return (Criteria) this;
        }

        public Criteria andPackingListNotEqualTo(String value) {
            addCriterion("PACKING_LIST <>", value, "packingList");
            return (Criteria) this;
        }

        public Criteria andPackingListGreaterThan(String value) {
            addCriterion("PACKING_LIST >", value, "packingList");
            return (Criteria) this;
        }

        public Criteria andPackingListGreaterThanOrEqualTo(String value) {
            addCriterion("PACKING_LIST >=", value, "packingList");
            return (Criteria) this;
        }

        public Criteria andPackingListLessThan(String value) {
            addCriterion("PACKING_LIST <", value, "packingList");
            return (Criteria) this;
        }

        public Criteria andPackingListLessThanOrEqualTo(String value) {
            addCriterion("PACKING_LIST <=", value, "packingList");
            return (Criteria) this;
        }

        public Criteria andPackingListLike(String value) {
            addCriterion("PACKING_LIST like", value, "packingList");
            return (Criteria) this;
        }

        public Criteria andPackingListNotLike(String value) {
            addCriterion("PACKING_LIST not like", value, "packingList");
            return (Criteria) this;
        }

        public Criteria andPackingListIn(List<String> values) {
            addCriterion("PACKING_LIST in", values, "packingList");
            return (Criteria) this;
        }

        public Criteria andPackingListNotIn(List<String> values) {
            addCriterion("PACKING_LIST not in", values, "packingList");
            return (Criteria) this;
        }

        public Criteria andPackingListBetween(String value1, String value2) {
            addCriterion("PACKING_LIST between", value1, value2, "packingList");
            return (Criteria) this;
        }

        public Criteria andPackingListNotBetween(String value1, String value2) {
            addCriterion("PACKING_LIST not between", value1, value2, "packingList");
            return (Criteria) this;
        }

        public Criteria andTosIsNull() {
            addCriterion("TOS is null");
            return (Criteria) this;
        }

        public Criteria andTosIsNotNull() {
            addCriterion("TOS is not null");
            return (Criteria) this;
        }

        public Criteria andTosEqualTo(String value) {
            addCriterion("TOS =", value, "tos");
            return (Criteria) this;
        }

        public Criteria andTosNotEqualTo(String value) {
            addCriterion("TOS <>", value, "tos");
            return (Criteria) this;
        }

        public Criteria andTosGreaterThan(String value) {
            addCriterion("TOS >", value, "tos");
            return (Criteria) this;
        }

        public Criteria andTosGreaterThanOrEqualTo(String value) {
            addCriterion("TOS >=", value, "tos");
            return (Criteria) this;
        }

        public Criteria andTosLessThan(String value) {
            addCriterion("TOS <", value, "tos");
            return (Criteria) this;
        }

        public Criteria andTosLessThanOrEqualTo(String value) {
            addCriterion("TOS <=", value, "tos");
            return (Criteria) this;
        }

        public Criteria andTosLike(String value) {
            addCriterion("TOS like", value, "tos");
            return (Criteria) this;
        }

        public Criteria andTosNotLike(String value) {
            addCriterion("TOS not like", value, "tos");
            return (Criteria) this;
        }

        public Criteria andTosIn(List<String> values) {
            addCriterion("TOS in", values, "tos");
            return (Criteria) this;
        }

        public Criteria andTosNotIn(List<String> values) {
            addCriterion("TOS not in", values, "tos");
            return (Criteria) this;
        }

        public Criteria andTosBetween(String value1, String value2) {
            addCriterion("TOS between", value1, value2, "tos");
            return (Criteria) this;
        }

        public Criteria andTosNotBetween(String value1, String value2) {
            addCriterion("TOS not between", value1, value2, "tos");
            return (Criteria) this;
        }

        public Criteria andTaxCategoryNoIsNull() {
            addCriterion("TAX_CATEGORY_NO is null");
            return (Criteria) this;
        }

        public Criteria andTaxCategoryNoIsNotNull() {
            addCriterion("TAX_CATEGORY_NO is not null");
            return (Criteria) this;
        }

        public Criteria andTaxCategoryNoEqualTo(String value) {
            addCriterion("TAX_CATEGORY_NO =", value, "taxCategoryNo");
            return (Criteria) this;
        }

        public Criteria andTaxCategoryNoNotEqualTo(String value) {
            addCriterion("TAX_CATEGORY_NO <>", value, "taxCategoryNo");
            return (Criteria) this;
        }

        public Criteria andTaxCategoryNoGreaterThan(String value) {
            addCriterion("TAX_CATEGORY_NO >", value, "taxCategoryNo");
            return (Criteria) this;
        }

        public Criteria andTaxCategoryNoGreaterThanOrEqualTo(String value) {
            addCriterion("TAX_CATEGORY_NO >=", value, "taxCategoryNo");
            return (Criteria) this;
        }

        public Criteria andTaxCategoryNoLessThan(String value) {
            addCriterion("TAX_CATEGORY_NO <", value, "taxCategoryNo");
            return (Criteria) this;
        }

        public Criteria andTaxCategoryNoLessThanOrEqualTo(String value) {
            addCriterion("TAX_CATEGORY_NO <=", value, "taxCategoryNo");
            return (Criteria) this;
        }

        public Criteria andTaxCategoryNoLike(String value) {
            addCriterion("TAX_CATEGORY_NO like", value, "taxCategoryNo");
            return (Criteria) this;
        }

        public Criteria andTaxCategoryNoNotLike(String value) {
            addCriterion("TAX_CATEGORY_NO not like", value, "taxCategoryNo");
            return (Criteria) this;
        }

        public Criteria andTaxCategoryNoIn(List<String> values) {
            addCriterion("TAX_CATEGORY_NO in", values, "taxCategoryNo");
            return (Criteria) this;
        }

        public Criteria andTaxCategoryNoNotIn(List<String> values) {
            addCriterion("TAX_CATEGORY_NO not in", values, "taxCategoryNo");
            return (Criteria) this;
        }

        public Criteria andTaxCategoryNoBetween(String value1, String value2) {
            addCriterion("TAX_CATEGORY_NO between", value1, value2, "taxCategoryNo");
            return (Criteria) this;
        }

        public Criteria andTaxCategoryNoNotBetween(String value1, String value2) {
            addCriterion("TAX_CATEGORY_NO not between", value1, value2, "taxCategoryNo");
            return (Criteria) this;
        }

        public Criteria andManufacturerIsNull() {
            addCriterion("MANUFACTURER is null");
            return (Criteria) this;
        }

        public Criteria andManufacturerIsNotNull() {
            addCriterion("MANUFACTURER is not null");
            return (Criteria) this;
        }

        public Criteria andManufacturerEqualTo(String value) {
            addCriterion("MANUFACTURER =", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotEqualTo(String value) {
            addCriterion("MANUFACTURER <>", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerGreaterThan(String value) {
            addCriterion("MANUFACTURER >", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerGreaterThanOrEqualTo(String value) {
            addCriterion("MANUFACTURER >=", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLessThan(String value) {
            addCriterion("MANUFACTURER <", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLessThanOrEqualTo(String value) {
            addCriterion("MANUFACTURER <=", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLike(String value) {
            addCriterion("MANUFACTURER like", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotLike(String value) {
            addCriterion("MANUFACTURER not like", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerIn(List<String> values) {
            addCriterion("MANUFACTURER in", values, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotIn(List<String> values) {
            addCriterion("MANUFACTURER not in", values, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerBetween(String value1, String value2) {
            addCriterion("MANUFACTURER between", value1, value2, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotBetween(String value1, String value2) {
            addCriterion("MANUFACTURER not between", value1, value2, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andProductionLicenseIsNull() {
            addCriterion("PRODUCTION_LICENSE is null");
            return (Criteria) this;
        }

        public Criteria andProductionLicenseIsNotNull() {
            addCriterion("PRODUCTION_LICENSE is not null");
            return (Criteria) this;
        }

        public Criteria andProductionLicenseEqualTo(String value) {
            addCriterion("PRODUCTION_LICENSE =", value, "productionLicense");
            return (Criteria) this;
        }

        public Criteria andProductionLicenseNotEqualTo(String value) {
            addCriterion("PRODUCTION_LICENSE <>", value, "productionLicense");
            return (Criteria) this;
        }

        public Criteria andProductionLicenseGreaterThan(String value) {
            addCriterion("PRODUCTION_LICENSE >", value, "productionLicense");
            return (Criteria) this;
        }

        public Criteria andProductionLicenseGreaterThanOrEqualTo(String value) {
            addCriterion("PRODUCTION_LICENSE >=", value, "productionLicense");
            return (Criteria) this;
        }

        public Criteria andProductionLicenseLessThan(String value) {
            addCriterion("PRODUCTION_LICENSE <", value, "productionLicense");
            return (Criteria) this;
        }

        public Criteria andProductionLicenseLessThanOrEqualTo(String value) {
            addCriterion("PRODUCTION_LICENSE <=", value, "productionLicense");
            return (Criteria) this;
        }

        public Criteria andProductionLicenseLike(String value) {
            addCriterion("PRODUCTION_LICENSE like", value, "productionLicense");
            return (Criteria) this;
        }

        public Criteria andProductionLicenseNotLike(String value) {
            addCriterion("PRODUCTION_LICENSE not like", value, "productionLicense");
            return (Criteria) this;
        }

        public Criteria andProductionLicenseIn(List<String> values) {
            addCriterion("PRODUCTION_LICENSE in", values, "productionLicense");
            return (Criteria) this;
        }

        public Criteria andProductionLicenseNotIn(List<String> values) {
            addCriterion("PRODUCTION_LICENSE not in", values, "productionLicense");
            return (Criteria) this;
        }

        public Criteria andProductionLicenseBetween(String value1, String value2) {
            addCriterion("PRODUCTION_LICENSE between", value1, value2, "productionLicense");
            return (Criteria) this;
        }

        public Criteria andProductionLicenseNotBetween(String value1, String value2) {
            addCriterion("PRODUCTION_LICENSE not between", value1, value2, "productionLicense");
            return (Criteria) this;
        }

        public Criteria andDiscardReasonIsNull() {
            addCriterion("DISCARD_REASON is null");
            return (Criteria) this;
        }

        public Criteria andDiscardReasonIsNotNull() {
            addCriterion("DISCARD_REASON is not null");
            return (Criteria) this;
        }

        public Criteria andDiscardReasonEqualTo(String value) {
            addCriterion("DISCARD_REASON =", value, "discardReason");
            return (Criteria) this;
        }

        public Criteria andDiscardReasonNotEqualTo(String value) {
            addCriterion("DISCARD_REASON <>", value, "discardReason");
            return (Criteria) this;
        }

        public Criteria andDiscardReasonGreaterThan(String value) {
            addCriterion("DISCARD_REASON >", value, "discardReason");
            return (Criteria) this;
        }

        public Criteria andDiscardReasonGreaterThanOrEqualTo(String value) {
            addCriterion("DISCARD_REASON >=", value, "discardReason");
            return (Criteria) this;
        }

        public Criteria andDiscardReasonLessThan(String value) {
            addCriterion("DISCARD_REASON <", value, "discardReason");
            return (Criteria) this;
        }

        public Criteria andDiscardReasonLessThanOrEqualTo(String value) {
            addCriterion("DISCARD_REASON <=", value, "discardReason");
            return (Criteria) this;
        }

        public Criteria andDiscardReasonLike(String value) {
            addCriterion("DISCARD_REASON like", value, "discardReason");
            return (Criteria) this;
        }

        public Criteria andDiscardReasonNotLike(String value) {
            addCriterion("DISCARD_REASON not like", value, "discardReason");
            return (Criteria) this;
        }

        public Criteria andDiscardReasonIn(List<String> values) {
            addCriterion("DISCARD_REASON in", values, "discardReason");
            return (Criteria) this;
        }

        public Criteria andDiscardReasonNotIn(List<String> values) {
            addCriterion("DISCARD_REASON not in", values, "discardReason");
            return (Criteria) this;
        }

        public Criteria andDiscardReasonBetween(String value1, String value2) {
            addCriterion("DISCARD_REASON between", value1, value2, "discardReason");
            return (Criteria) this;
        }

        public Criteria andDiscardReasonNotBetween(String value1, String value2) {
            addCriterion("DISCARD_REASON not between", value1, value2, "discardReason");
            return (Criteria) this;
        }

        public Criteria andDiscardTimeIsNull() {
            addCriterion("DISCARD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andDiscardTimeIsNotNull() {
            addCriterion("DISCARD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andDiscardTimeEqualTo(Long value) {
            addCriterion("DISCARD_TIME =", value, "discardTime");
            return (Criteria) this;
        }

        public Criteria andDiscardTimeNotEqualTo(Long value) {
            addCriterion("DISCARD_TIME <>", value, "discardTime");
            return (Criteria) this;
        }

        public Criteria andDiscardTimeGreaterThan(Long value) {
            addCriterion("DISCARD_TIME >", value, "discardTime");
            return (Criteria) this;
        }

        public Criteria andDiscardTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("DISCARD_TIME >=", value, "discardTime");
            return (Criteria) this;
        }

        public Criteria andDiscardTimeLessThan(Long value) {
            addCriterion("DISCARD_TIME <", value, "discardTime");
            return (Criteria) this;
        }

        public Criteria andDiscardTimeLessThanOrEqualTo(Long value) {
            addCriterion("DISCARD_TIME <=", value, "discardTime");
            return (Criteria) this;
        }

        public Criteria andDiscardTimeIn(List<Long> values) {
            addCriterion("DISCARD_TIME in", values, "discardTime");
            return (Criteria) this;
        }

        public Criteria andDiscardTimeNotIn(List<Long> values) {
            addCriterion("DISCARD_TIME not in", values, "discardTime");
            return (Criteria) this;
        }

        public Criteria andDiscardTimeBetween(Long value1, Long value2) {
            addCriterion("DISCARD_TIME between", value1, value2, "discardTime");
            return (Criteria) this;
        }

        public Criteria andDiscardTimeNotBetween(Long value1, Long value2) {
            addCriterion("DISCARD_TIME not between", value1, value2, "discardTime");
            return (Criteria) this;
        }

        public Criteria andSupplyModelIsNull() {
            addCriterion("SUPPLY_MODEL is null");
            return (Criteria) this;
        }

        public Criteria andSupplyModelIsNotNull() {
            addCriterion("SUPPLY_MODEL is not null");
            return (Criteria) this;
        }

        public Criteria andSupplyModelEqualTo(String value) {
            addCriterion("SUPPLY_MODEL =", value, "supplyModel");
            return (Criteria) this;
        }

        public Criteria andSupplyModelNotEqualTo(String value) {
            addCriterion("SUPPLY_MODEL <>", value, "supplyModel");
            return (Criteria) this;
        }

        public Criteria andSupplyModelGreaterThan(String value) {
            addCriterion("SUPPLY_MODEL >", value, "supplyModel");
            return (Criteria) this;
        }

        public Criteria andSupplyModelGreaterThanOrEqualTo(String value) {
            addCriterion("SUPPLY_MODEL >=", value, "supplyModel");
            return (Criteria) this;
        }

        public Criteria andSupplyModelLessThan(String value) {
            addCriterion("SUPPLY_MODEL <", value, "supplyModel");
            return (Criteria) this;
        }

        public Criteria andSupplyModelLessThanOrEqualTo(String value) {
            addCriterion("SUPPLY_MODEL <=", value, "supplyModel");
            return (Criteria) this;
        }

        public Criteria andSupplyModelLike(String value) {
            addCriterion("SUPPLY_MODEL like", value, "supplyModel");
            return (Criteria) this;
        }

        public Criteria andSupplyModelNotLike(String value) {
            addCriterion("SUPPLY_MODEL not like", value, "supplyModel");
            return (Criteria) this;
        }

        public Criteria andSupplyModelIn(List<String> values) {
            addCriterion("SUPPLY_MODEL in", values, "supplyModel");
            return (Criteria) this;
        }

        public Criteria andSupplyModelNotIn(List<String> values) {
            addCriterion("SUPPLY_MODEL not in", values, "supplyModel");
            return (Criteria) this;
        }

        public Criteria andSupplyModelBetween(String value1, String value2) {
            addCriterion("SUPPLY_MODEL between", value1, value2, "supplyModel");
            return (Criteria) this;
        }

        public Criteria andSupplyModelNotBetween(String value1, String value2) {
            addCriterion("SUPPLY_MODEL not between", value1, value2, "supplyModel");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryIdIsNull() {
            addCriterion("STANDARD_CATEGORY_ID is null");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryIdIsNotNull() {
            addCriterion("STANDARD_CATEGORY_ID is not null");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryIdEqualTo(Integer value) {
            addCriterion("STANDARD_CATEGORY_ID =", value, "standardCategoryId");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryIdNotEqualTo(Integer value) {
            addCriterion("STANDARD_CATEGORY_ID <>", value, "standardCategoryId");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryIdGreaterThan(Integer value) {
            addCriterion("STANDARD_CATEGORY_ID >", value, "standardCategoryId");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("STANDARD_CATEGORY_ID >=", value, "standardCategoryId");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryIdLessThan(Integer value) {
            addCriterion("STANDARD_CATEGORY_ID <", value, "standardCategoryId");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryIdLessThanOrEqualTo(Integer value) {
            addCriterion("STANDARD_CATEGORY_ID <=", value, "standardCategoryId");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryIdIn(List<Integer> values) {
            addCriterion("STANDARD_CATEGORY_ID in", values, "standardCategoryId");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryIdNotIn(List<Integer> values) {
            addCriterion("STANDARD_CATEGORY_ID not in", values, "standardCategoryId");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryIdBetween(Integer value1, Integer value2) {
            addCriterion("STANDARD_CATEGORY_ID between", value1, value2, "standardCategoryId");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryIdNotBetween(Integer value1, Integer value2) {
            addCriterion("STANDARD_CATEGORY_ID not between", value1, value2, "standardCategoryId");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryLevelIsNull() {
            addCriterion("STANDARD_CATEGORY_LEVEL is null");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryLevelIsNotNull() {
            addCriterion("STANDARD_CATEGORY_LEVEL is not null");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryLevelEqualTo(Integer value) {
            addCriterion("STANDARD_CATEGORY_LEVEL =", value, "standardCategoryLevel");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryLevelNotEqualTo(Integer value) {
            addCriterion("STANDARD_CATEGORY_LEVEL <>", value, "standardCategoryLevel");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryLevelGreaterThan(Integer value) {
            addCriterion("STANDARD_CATEGORY_LEVEL >", value, "standardCategoryLevel");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryLevelGreaterThanOrEqualTo(Integer value) {
            addCriterion("STANDARD_CATEGORY_LEVEL >=", value, "standardCategoryLevel");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryLevelLessThan(Integer value) {
            addCriterion("STANDARD_CATEGORY_LEVEL <", value, "standardCategoryLevel");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryLevelLessThanOrEqualTo(Integer value) {
            addCriterion("STANDARD_CATEGORY_LEVEL <=", value, "standardCategoryLevel");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryLevelIn(List<Integer> values) {
            addCriterion("STANDARD_CATEGORY_LEVEL in", values, "standardCategoryLevel");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryLevelNotIn(List<Integer> values) {
            addCriterion("STANDARD_CATEGORY_LEVEL not in", values, "standardCategoryLevel");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryLevelBetween(Integer value1, Integer value2) {
            addCriterion("STANDARD_CATEGORY_LEVEL between", value1, value2, "standardCategoryLevel");
            return (Criteria) this;
        }

        public Criteria andStandardCategoryLevelNotBetween(Integer value1, Integer value2) {
            addCriterion("STANDARD_CATEGORY_LEVEL not between", value1, value2, "standardCategoryLevel");
            return (Criteria) this;
        }

        public Criteria andSpecIsNull() {
            addCriterion("SPEC is null");
            return (Criteria) this;
        }

        public Criteria andSpecIsNotNull() {
            addCriterion("SPEC is not null");
            return (Criteria) this;
        }

        public Criteria andSpecEqualTo(String value) {
            addCriterion("SPEC =", value, "spec");
            return (Criteria) this;
        }

        public Criteria andSpecNotEqualTo(String value) {
            addCriterion("SPEC <>", value, "spec");
            return (Criteria) this;
        }

        public Criteria andSpecGreaterThan(String value) {
            addCriterion("SPEC >", value, "spec");
            return (Criteria) this;
        }

        public Criteria andSpecGreaterThanOrEqualTo(String value) {
            addCriterion("SPEC >=", value, "spec");
            return (Criteria) this;
        }

        public Criteria andSpecLessThan(String value) {
            addCriterion("SPEC <", value, "spec");
            return (Criteria) this;
        }

        public Criteria andSpecLessThanOrEqualTo(String value) {
            addCriterion("SPEC <=", value, "spec");
            return (Criteria) this;
        }

        public Criteria andSpecLike(String value) {
            addCriterion("SPEC like", value, "spec");
            return (Criteria) this;
        }

        public Criteria andSpecNotLike(String value) {
            addCriterion("SPEC not like", value, "spec");
            return (Criteria) this;
        }

        public Criteria andSpecIn(List<String> values) {
            addCriterion("SPEC in", values, "spec");
            return (Criteria) this;
        }

        public Criteria andSpecNotIn(List<String> values) {
            addCriterion("SPEC not in", values, "spec");
            return (Criteria) this;
        }

        public Criteria andSpecBetween(String value1, String value2) {
            addCriterion("SPEC between", value1, value2, "spec");
            return (Criteria) this;
        }

        public Criteria andSpecNotBetween(String value1, String value2) {
            addCriterion("SPEC not between", value1, value2, "spec");
            return (Criteria) this;
        }

        public Criteria andProductAddressIsNull() {
            addCriterion("PRODUCT_ADDRESS is null");
            return (Criteria) this;
        }

        public Criteria andProductAddressIsNotNull() {
            addCriterion("PRODUCT_ADDRESS is not null");
            return (Criteria) this;
        }

        public Criteria andProductAddressEqualTo(String value) {
            addCriterion("PRODUCT_ADDRESS =", value, "productAddress");
            return (Criteria) this;
        }

        public Criteria andProductAddressNotEqualTo(String value) {
            addCriterion("PRODUCT_ADDRESS <>", value, "productAddress");
            return (Criteria) this;
        }

        public Criteria andProductAddressGreaterThan(String value) {
            addCriterion("PRODUCT_ADDRESS >", value, "productAddress");
            return (Criteria) this;
        }

        public Criteria andProductAddressGreaterThanOrEqualTo(String value) {
            addCriterion("PRODUCT_ADDRESS >=", value, "productAddress");
            return (Criteria) this;
        }

        public Criteria andProductAddressLessThan(String value) {
            addCriterion("PRODUCT_ADDRESS <", value, "productAddress");
            return (Criteria) this;
        }

        public Criteria andProductAddressLessThanOrEqualTo(String value) {
            addCriterion("PRODUCT_ADDRESS <=", value, "productAddress");
            return (Criteria) this;
        }

        public Criteria andProductAddressLike(String value) {
            addCriterion("PRODUCT_ADDRESS like", value, "productAddress");
            return (Criteria) this;
        }

        public Criteria andProductAddressNotLike(String value) {
            addCriterion("PRODUCT_ADDRESS not like", value, "productAddress");
            return (Criteria) this;
        }

        public Criteria andProductAddressIn(List<String> values) {
            addCriterion("PRODUCT_ADDRESS in", values, "productAddress");
            return (Criteria) this;
        }

        public Criteria andProductAddressNotIn(List<String> values) {
            addCriterion("PRODUCT_ADDRESS not in", values, "productAddress");
            return (Criteria) this;
        }

        public Criteria andProductAddressBetween(String value1, String value2) {
            addCriterion("PRODUCT_ADDRESS between", value1, value2, "productAddress");
            return (Criteria) this;
        }

        public Criteria andProductAddressNotBetween(String value1, String value2) {
            addCriterion("PRODUCT_ADDRESS not between", value1, value2, "productAddress");
            return (Criteria) this;
        }

        public Criteria andStorageRequirementsIsNull() {
            addCriterion("STORAGE_REQUIREMENTS is null");
            return (Criteria) this;
        }

        public Criteria andStorageRequirementsIsNotNull() {
            addCriterion("STORAGE_REQUIREMENTS is not null");
            return (Criteria) this;
        }

        public Criteria andStorageRequirementsEqualTo(Integer value) {
            addCriterion("STORAGE_REQUIREMENTS =", value, "storageRequirements");
            return (Criteria) this;
        }

        public Criteria andStorageRequirementsNotEqualTo(Integer value) {
            addCriterion("STORAGE_REQUIREMENTS <>", value, "storageRequirements");
            return (Criteria) this;
        }

        public Criteria andStorageRequirementsGreaterThan(Integer value) {
            addCriterion("STORAGE_REQUIREMENTS >", value, "storageRequirements");
            return (Criteria) this;
        }

        public Criteria andStorageRequirementsGreaterThanOrEqualTo(Integer value) {
            addCriterion("STORAGE_REQUIREMENTS >=", value, "storageRequirements");
            return (Criteria) this;
        }

        public Criteria andStorageRequirementsLessThan(Integer value) {
            addCriterion("STORAGE_REQUIREMENTS <", value, "storageRequirements");
            return (Criteria) this;
        }

        public Criteria andStorageRequirementsLessThanOrEqualTo(Integer value) {
            addCriterion("STORAGE_REQUIREMENTS <=", value, "storageRequirements");
            return (Criteria) this;
        }

        public Criteria andStorageRequirementsIn(List<Integer> values) {
            addCriterion("STORAGE_REQUIREMENTS in", values, "storageRequirements");
            return (Criteria) this;
        }

        public Criteria andStorageRequirementsNotIn(List<Integer> values) {
            addCriterion("STORAGE_REQUIREMENTS not in", values, "storageRequirements");
            return (Criteria) this;
        }

        public Criteria andStorageRequirementsBetween(Integer value1, Integer value2) {
            addCriterion("STORAGE_REQUIREMENTS between", value1, value2, "storageRequirements");
            return (Criteria) this;
        }

        public Criteria andStorageRequirementsNotBetween(Integer value1, Integer value2) {
            addCriterion("STORAGE_REQUIREMENTS not between", value1, value2, "storageRequirements");
            return (Criteria) this;
        }

        public Criteria andSourceIsNull() {
            addCriterion("`SOURCE` is null");
            return (Criteria) this;
        }

        public Criteria andSourceIsNotNull() {
            addCriterion("`SOURCE` is not null");
            return (Criteria) this;
        }

        public Criteria andSourceEqualTo(Integer value) {
            addCriterion("`SOURCE` =", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotEqualTo(Integer value) {
            addCriterion("`SOURCE` <>", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThan(Integer value) {
            addCriterion("`SOURCE` >", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThanOrEqualTo(Integer value) {
            addCriterion("`SOURCE` >=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThan(Integer value) {
            addCriterion("`SOURCE` <", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThanOrEqualTo(Integer value) {
            addCriterion("`SOURCE` <=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceIn(List<Integer> values) {
            addCriterion("`SOURCE` in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotIn(List<Integer> values) {
            addCriterion("`SOURCE` not in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceBetween(Integer value1, Integer value2) {
            addCriterion("`SOURCE` between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotBetween(Integer value1, Integer value2) {
            addCriterion("`SOURCE` not between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andIsRecommedIsNull() {
            addCriterion("IS_RECOMMED is null");
            return (Criteria) this;
        }

        public Criteria andIsRecommedIsNotNull() {
            addCriterion("IS_RECOMMED is not null");
            return (Criteria) this;
        }

        public Criteria andIsRecommedEqualTo(Byte value) {
            addCriterion("IS_RECOMMED =", value, "isRecommed");
            return (Criteria) this;
        }

        public Criteria andIsRecommedNotEqualTo(Byte value) {
            addCriterion("IS_RECOMMED <>", value, "isRecommed");
            return (Criteria) this;
        }

        public Criteria andIsRecommedGreaterThan(Byte value) {
            addCriterion("IS_RECOMMED >", value, "isRecommed");
            return (Criteria) this;
        }

        public Criteria andIsRecommedGreaterThanOrEqualTo(Byte value) {
            addCriterion("IS_RECOMMED >=", value, "isRecommed");
            return (Criteria) this;
        }

        public Criteria andIsRecommedLessThan(Byte value) {
            addCriterion("IS_RECOMMED <", value, "isRecommed");
            return (Criteria) this;
        }

        public Criteria andIsRecommedLessThanOrEqualTo(Byte value) {
            addCriterion("IS_RECOMMED <=", value, "isRecommed");
            return (Criteria) this;
        }

        public Criteria andIsRecommedIn(List<Byte> values) {
            addCriterion("IS_RECOMMED in", values, "isRecommed");
            return (Criteria) this;
        }

        public Criteria andIsRecommedNotIn(List<Byte> values) {
            addCriterion("IS_RECOMMED not in", values, "isRecommed");
            return (Criteria) this;
        }

        public Criteria andIsRecommedBetween(Byte value1, Byte value2) {
            addCriterion("IS_RECOMMED between", value1, value2, "isRecommed");
            return (Criteria) this;
        }

        public Criteria andIsRecommedNotBetween(Byte value1, Byte value2) {
            addCriterion("IS_RECOMMED not between", value1, value2, "isRecommed");
            return (Criteria) this;
        }

        public Criteria andRegisterNameIsNull() {
            addCriterion("REGISTER_NAME is null");
            return (Criteria) this;
        }

        public Criteria andRegisterNameIsNotNull() {
            addCriterion("REGISTER_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andRegisterNameEqualTo(String value) {
            addCriterion("REGISTER_NAME =", value, "registerName");
            return (Criteria) this;
        }

        public Criteria andRegisterNameNotEqualTo(String value) {
            addCriterion("REGISTER_NAME <>", value, "registerName");
            return (Criteria) this;
        }

        public Criteria andRegisterNameGreaterThan(String value) {
            addCriterion("REGISTER_NAME >", value, "registerName");
            return (Criteria) this;
        }

        public Criteria andRegisterNameGreaterThanOrEqualTo(String value) {
            addCriterion("REGISTER_NAME >=", value, "registerName");
            return (Criteria) this;
        }

        public Criteria andRegisterNameLessThan(String value) {
            addCriterion("REGISTER_NAME <", value, "registerName");
            return (Criteria) this;
        }

        public Criteria andRegisterNameLessThanOrEqualTo(String value) {
            addCriterion("REGISTER_NAME <=", value, "registerName");
            return (Criteria) this;
        }

        public Criteria andRegisterNameLike(String value) {
            addCriterion("REGISTER_NAME like", value, "registerName");
            return (Criteria) this;
        }

        public Criteria andRegisterNameNotLike(String value) {
            addCriterion("REGISTER_NAME not like", value, "registerName");
            return (Criteria) this;
        }

        public Criteria andRegisterNameIn(List<String> values) {
            addCriterion("REGISTER_NAME in", values, "registerName");
            return (Criteria) this;
        }

        public Criteria andRegisterNameNotIn(List<String> values) {
            addCriterion("REGISTER_NAME not in", values, "registerName");
            return (Criteria) this;
        }

        public Criteria andRegisterNameBetween(String value1, String value2) {
            addCriterion("REGISTER_NAME between", value1, value2, "registerName");
            return (Criteria) this;
        }

        public Criteria andRegisterNameNotBetween(String value1, String value2) {
            addCriterion("REGISTER_NAME not between", value1, value2, "registerName");
            return (Criteria) this;
        }

        public Criteria andHrefIsNull() {
            addCriterion("HREF is null");
            return (Criteria) this;
        }

        public Criteria andHrefIsNotNull() {
            addCriterion("HREF is not null");
            return (Criteria) this;
        }

        public Criteria andHrefEqualTo(String value) {
            addCriterion("HREF =", value, "href");
            return (Criteria) this;
        }

        public Criteria andHrefNotEqualTo(String value) {
            addCriterion("HREF <>", value, "href");
            return (Criteria) this;
        }

        public Criteria andHrefGreaterThan(String value) {
            addCriterion("HREF >", value, "href");
            return (Criteria) this;
        }

        public Criteria andHrefGreaterThanOrEqualTo(String value) {
            addCriterion("HREF >=", value, "href");
            return (Criteria) this;
        }

        public Criteria andHrefLessThan(String value) {
            addCriterion("HREF <", value, "href");
            return (Criteria) this;
        }

        public Criteria andHrefLessThanOrEqualTo(String value) {
            addCriterion("HREF <=", value, "href");
            return (Criteria) this;
        }

        public Criteria andHrefLike(String value) {
            addCriterion("HREF like", value, "href");
            return (Criteria) this;
        }

        public Criteria andHrefNotLike(String value) {
            addCriterion("HREF not like", value, "href");
            return (Criteria) this;
        }

        public Criteria andHrefIn(List<String> values) {
            addCriterion("HREF in", values, "href");
            return (Criteria) this;
        }

        public Criteria andHrefNotIn(List<String> values) {
            addCriterion("HREF not in", values, "href");
            return (Criteria) this;
        }

        public Criteria andHrefBetween(String value1, String value2) {
            addCriterion("HREF between", value1, value2, "href");
            return (Criteria) this;
        }

        public Criteria andHrefNotBetween(String value1, String value2) {
            addCriterion("HREF not between", value1, value2, "href");
            return (Criteria) this;
        }

        public Criteria andJxMarketPriceIsNull() {
            addCriterion("JX_MARKET_PRICE is null");
            return (Criteria) this;
        }

        public Criteria andJxMarketPriceIsNotNull() {
            addCriterion("JX_MARKET_PRICE is not null");
            return (Criteria) this;
        }

        public Criteria andJxMarketPriceEqualTo(BigDecimal value) {
            addCriterion("JX_MARKET_PRICE =", value, "jxMarketPrice");
            return (Criteria) this;
        }

        public Criteria andJxMarketPriceNotEqualTo(BigDecimal value) {
            addCriterion("JX_MARKET_PRICE <>", value, "jxMarketPrice");
            return (Criteria) this;
        }

        public Criteria andJxMarketPriceGreaterThan(BigDecimal value) {
            addCriterion("JX_MARKET_PRICE >", value, "jxMarketPrice");
            return (Criteria) this;
        }

        public Criteria andJxMarketPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("JX_MARKET_PRICE >=", value, "jxMarketPrice");
            return (Criteria) this;
        }

        public Criteria andJxMarketPriceLessThan(BigDecimal value) {
            addCriterion("JX_MARKET_PRICE <", value, "jxMarketPrice");
            return (Criteria) this;
        }

        public Criteria andJxMarketPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("JX_MARKET_PRICE <=", value, "jxMarketPrice");
            return (Criteria) this;
        }

        public Criteria andJxMarketPriceIn(List<BigDecimal> values) {
            addCriterion("JX_MARKET_PRICE in", values, "jxMarketPrice");
            return (Criteria) this;
        }

        public Criteria andJxMarketPriceNotIn(List<BigDecimal> values) {
            addCriterion("JX_MARKET_PRICE not in", values, "jxMarketPrice");
            return (Criteria) this;
        }

        public Criteria andJxMarketPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("JX_MARKET_PRICE between", value1, value2, "jxMarketPrice");
            return (Criteria) this;
        }

        public Criteria andJxMarketPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("JX_MARKET_PRICE not between", value1, value2, "jxMarketPrice");
            return (Criteria) this;
        }

        public Criteria andJxSalePriceIsNull() {
            addCriterion("JX_SALE_PRICE is null");
            return (Criteria) this;
        }

        public Criteria andJxSalePriceIsNotNull() {
            addCriterion("JX_SALE_PRICE is not null");
            return (Criteria) this;
        }

        public Criteria andJxSalePriceEqualTo(BigDecimal value) {
            addCriterion("JX_SALE_PRICE =", value, "jxSalePrice");
            return (Criteria) this;
        }

        public Criteria andJxSalePriceNotEqualTo(BigDecimal value) {
            addCriterion("JX_SALE_PRICE <>", value, "jxSalePrice");
            return (Criteria) this;
        }

        public Criteria andJxSalePriceGreaterThan(BigDecimal value) {
            addCriterion("JX_SALE_PRICE >", value, "jxSalePrice");
            return (Criteria) this;
        }

        public Criteria andJxSalePriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("JX_SALE_PRICE >=", value, "jxSalePrice");
            return (Criteria) this;
        }

        public Criteria andJxSalePriceLessThan(BigDecimal value) {
            addCriterion("JX_SALE_PRICE <", value, "jxSalePrice");
            return (Criteria) this;
        }

        public Criteria andJxSalePriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("JX_SALE_PRICE <=", value, "jxSalePrice");
            return (Criteria) this;
        }

        public Criteria andJxSalePriceIn(List<BigDecimal> values) {
            addCriterion("JX_SALE_PRICE in", values, "jxSalePrice");
            return (Criteria) this;
        }

        public Criteria andJxSalePriceNotIn(List<BigDecimal> values) {
            addCriterion("JX_SALE_PRICE not in", values, "jxSalePrice");
            return (Criteria) this;
        }

        public Criteria andJxSalePriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("JX_SALE_PRICE between", value1, value2, "jxSalePrice");
            return (Criteria) this;
        }

        public Criteria andJxSalePriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("JX_SALE_PRICE not between", value1, value2, "jxSalePrice");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("ADD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("ADD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Long value) {
            addCriterion("ADD_TIME =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Long value) {
            addCriterion("ADD_TIME <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Long value) {
            addCriterion("ADD_TIME >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("ADD_TIME >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Long value) {
            addCriterion("ADD_TIME <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Long value) {
            addCriterion("ADD_TIME <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Long> values) {
            addCriterion("ADD_TIME in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Long> values) {
            addCriterion("ADD_TIME not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Long value1, Long value2) {
            addCriterion("ADD_TIME between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Long value1, Long value2) {
            addCriterion("ADD_TIME not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("CREATOR is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("CREATOR is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(Integer value) {
            addCriterion("CREATOR =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(Integer value) {
            addCriterion("CREATOR <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(Integer value) {
            addCriterion("CREATOR >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("CREATOR >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(Integer value) {
            addCriterion("CREATOR <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(Integer value) {
            addCriterion("CREATOR <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<Integer> values) {
            addCriterion("CREATOR in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<Integer> values) {
            addCriterion("CREATOR not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNull() {
            addCriterion("MOD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNotNull() {
            addCriterion("MOD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andModTimeEqualTo(Long value) {
            addCriterion("MOD_TIME =", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotEqualTo(Long value) {
            addCriterion("MOD_TIME <>", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThan(Long value) {
            addCriterion("MOD_TIME >", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("MOD_TIME >=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThan(Long value) {
            addCriterion("MOD_TIME <", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThanOrEqualTo(Long value) {
            addCriterion("MOD_TIME <=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeIn(List<Long> values) {
            addCriterion("MOD_TIME in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotIn(List<Long> values) {
            addCriterion("MOD_TIME not in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeBetween(Long value1, Long value2) {
            addCriterion("MOD_TIME between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotBetween(Long value1, Long value2) {
            addCriterion("MOD_TIME not between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNull() {
            addCriterion("UPDATER is null");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNotNull() {
            addCriterion("UPDATER is not null");
            return (Criteria) this;
        }

        public Criteria andUpdaterEqualTo(Integer value) {
            addCriterion("UPDATER =", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotEqualTo(Integer value) {
            addCriterion("UPDATER <>", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThan(Integer value) {
            addCriterion("UPDATER >", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThanOrEqualTo(Integer value) {
            addCriterion("UPDATER >=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThan(Integer value) {
            addCriterion("UPDATER <", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThanOrEqualTo(Integer value) {
            addCriterion("UPDATER <=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterIn(List<Integer> values) {
            addCriterion("UPDATER in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotIn(List<Integer> values) {
            addCriterion("UPDATER not in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER not between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andToSkuFlagIsNull() {
            addCriterion("TO_SKU_FLAG is null");
            return (Criteria) this;
        }

        public Criteria andToSkuFlagIsNotNull() {
            addCriterion("TO_SKU_FLAG is not null");
            return (Criteria) this;
        }

        public Criteria andToSkuFlagEqualTo(Integer value) {
            addCriterion("TO_SKU_FLAG =", value, "toSkuFlag");
            return (Criteria) this;
        }

        public Criteria andToSkuFlagNotEqualTo(Integer value) {
            addCriterion("TO_SKU_FLAG <>", value, "toSkuFlag");
            return (Criteria) this;
        }

        public Criteria andToSkuFlagGreaterThan(Integer value) {
            addCriterion("TO_SKU_FLAG >", value, "toSkuFlag");
            return (Criteria) this;
        }

        public Criteria andToSkuFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("TO_SKU_FLAG >=", value, "toSkuFlag");
            return (Criteria) this;
        }

        public Criteria andToSkuFlagLessThan(Integer value) {
            addCriterion("TO_SKU_FLAG <", value, "toSkuFlag");
            return (Criteria) this;
        }

        public Criteria andToSkuFlagLessThanOrEqualTo(Integer value) {
            addCriterion("TO_SKU_FLAG <=", value, "toSkuFlag");
            return (Criteria) this;
        }

        public Criteria andToSkuFlagIn(List<Integer> values) {
            addCriterion("TO_SKU_FLAG in", values, "toSkuFlag");
            return (Criteria) this;
        }

        public Criteria andToSkuFlagNotIn(List<Integer> values) {
            addCriterion("TO_SKU_FLAG not in", values, "toSkuFlag");
            return (Criteria) this;
        }

        public Criteria andToSkuFlagBetween(Integer value1, Integer value2) {
            addCriterion("TO_SKU_FLAG between", value1, value2, "toSkuFlag");
            return (Criteria) this;
        }

        public Criteria andToSkuFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("TO_SKU_FLAG not between", value1, value2, "toSkuFlag");
            return (Criteria) this;
        }

        public Criteria andIsNoReasonReturnIsNull() {
            addCriterion("IS_NO_REASON_RETURN is null");
            return (Criteria) this;
        }

        public Criteria andIsNoReasonReturnIsNotNull() {
            addCriterion("IS_NO_REASON_RETURN is not null");
            return (Criteria) this;
        }

        public Criteria andIsNoReasonReturnEqualTo(Byte value) {
            addCriterion("IS_NO_REASON_RETURN =", value, "isNoReasonReturn");
            return (Criteria) this;
        }

        public Criteria andIsNoReasonReturnNotEqualTo(Byte value) {
            addCriterion("IS_NO_REASON_RETURN <>", value, "isNoReasonReturn");
            return (Criteria) this;
        }

        public Criteria andIsNoReasonReturnGreaterThan(Byte value) {
            addCriterion("IS_NO_REASON_RETURN >", value, "isNoReasonReturn");
            return (Criteria) this;
        }

        public Criteria andIsNoReasonReturnGreaterThanOrEqualTo(Byte value) {
            addCriterion("IS_NO_REASON_RETURN >=", value, "isNoReasonReturn");
            return (Criteria) this;
        }

        public Criteria andIsNoReasonReturnLessThan(Byte value) {
            addCriterion("IS_NO_REASON_RETURN <", value, "isNoReasonReturn");
            return (Criteria) this;
        }

        public Criteria andIsNoReasonReturnLessThanOrEqualTo(Byte value) {
            addCriterion("IS_NO_REASON_RETURN <=", value, "isNoReasonReturn");
            return (Criteria) this;
        }

        public Criteria andIsNoReasonReturnIn(List<Byte> values) {
            addCriterion("IS_NO_REASON_RETURN in", values, "isNoReasonReturn");
            return (Criteria) this;
        }

        public Criteria andIsNoReasonReturnNotIn(List<Byte> values) {
            addCriterion("IS_NO_REASON_RETURN not in", values, "isNoReasonReturn");
            return (Criteria) this;
        }

        public Criteria andIsNoReasonReturnBetween(Byte value1, Byte value2) {
            addCriterion("IS_NO_REASON_RETURN between", value1, value2, "isNoReasonReturn");
            return (Criteria) this;
        }

        public Criteria andIsNoReasonReturnNotBetween(Byte value1, Byte value2) {
            addCriterion("IS_NO_REASON_RETURN not between", value1, value2, "isNoReasonReturn");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_GOODS
     *
     * @mbggenerated do_not_delete_during_merge Sat Aug 10 17:18:06 CST 2019
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_GOODS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}