package com.vedeng.activiti.taskassign;

import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;
import com.vedeng.erp.buyorder.service.BuyorderExpenseApiService;
import com.vedeng.erp.saleorder.service.SaleOrderGoodsApiService;
import com.vedeng.finance.model.CapitalBill;
import com.vedeng.finance.model.CapitalBillDetail;
import com.vedeng.finance.service.CapitalBillService;
import com.vedeng.system.service.UserService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class EditBuyorderExpenseExecutionListener implements ExecutionListener {

    Logger logger = LoggerFactory.getLogger(EditBuyorderExpenseExecutionListener.class);
    // 运行时注入service
    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
    private CapitalBillService capitalBillService = (CapitalBillService) context.getBean("capitalBillService");
    private UserService userService = (UserService) context.getBean("userService");
    private BuyorderExpenseApiService buyorderExpenseApiService = (BuyorderExpenseApiService) context.getBean("buyorderExpenseServiceImpl");
    private SaleOrderGoodsApiService saleOrderGoodsApiService = (SaleOrderGoodsApiService) context.getBean("saleOrderGoodsServiceImpl");

    //采购费用单订单审核触发器
    //根据穿参通用回写主表中状态
    public void notify(DelegateExecution execution) throws Exception {
        logger.info("准备设置采购订单的状态");

        BuyorderExpenseDto buyorderInfo = (BuyorderExpenseDto) execution.getVariable("buyorderExpenseInfo");
        if (buyorderInfo != null) {
            //如果预付款金额等于0，添加触发账期流水
            if (buyorderInfo.getBuyorderExpenseDetailDto().getPrepaidAmount().compareTo(BigDecimal.ZERO) == 0) {
                User user = getUser();
                BuyorderExpenseDto update = new BuyorderExpenseDto();
                update.setBuyorderExpenseId(buyorderInfo.getBuyorderExpenseId());
                if (buyorderInfo.getBuyorderExpenseDetailDto().getRetainageAmount().compareTo(BigDecimal.ZERO) == 0) {
                    //如果尾款等于0.付款状态为全部付款
                    update.setPaymentStatus(2);
                } else {
                    //如果尾款不等于0.付款状态为部分付款
                    update.setPaymentStatus(1);
                }
                update.setPaymentTime(new Date());
                //添加啊流水
                // 归属销售
                User belongUser = new User();
                if (buyorderInfo.getBuyorderExpenseDetailDto().getTraderId() != null) {
                    belongUser = userService.getUserByTraderId(buyorderInfo.getBuyorderExpenseDetailDto().getTraderId(), 2);// 1客户，2供应商
                    if (belongUser != null && belongUser.getUserId() != null) {
                        belongUser = userService.getUserById(belongUser.getUserId());
                    }
                }
                CapitalBill capitalBill = new CapitalBill();
                capitalBill.setCompanyId(user.getCompanyId());
                //信用支付
                capitalBill.setTraderMode(527);
                capitalBill.setCurrencyUnitId(1);
                capitalBill.setTraderTime(DateUtil.sysTimeMillis());
                //交易类型 转移
                capitalBill.setTraderType(3);
                capitalBill.setPayee(buyorderInfo.getBuyorderExpenseDetailDto().getTraderName());
                capitalBill.setPayer(user.getCompanyName());

                List<CapitalBillDetail> capitalBillDetails = new ArrayList<>();
                CapitalBillDetail capitalBillDetail = new CapitalBillDetail();
                //订单类型   采购费用订单
                capitalBillDetail.setOrderType(4);
                capitalBillDetail.setOrderNo(buyorderInfo.getBuyorderExpenseNo());
                capitalBillDetail.setRelatedId(buyorderInfo.getBuyorderExpenseId());
                //所属类型  经销商（包含终端）
                capitalBillDetail.setTraderType(2);
                capitalBillDetail.setTraderId(buyorderInfo.getBuyorderExpenseDetailDto().getTraderId());
                capitalBillDetail.setUserId(buyorderInfo.getCreator());
                //业务类型  订单收款
                capitalBillDetail.setBussinessType(525);
                capitalBillDetail.setAmount(buyorderInfo.getBuyorderExpenseDetailDto().getAccountPeriodAmount());
                if (belongUser != null && belongUser.getOrgName() != null && belongUser.getOrgId() != null) {
                    capitalBillDetail.setOrgName(belongUser.getOrgName());
                    capitalBillDetail.setOrgId(belongUser.getOrgId());
                }
                capitalBillDetails.add(capitalBillDetail);

                capitalBill.setCapitalBillDetails(capitalBillDetails);
                CapitalBillDetail capitalBillDetailInfo = new CapitalBillDetail();
                capitalBillDetailInfo.setOrderType(4);
                capitalBillDetailInfo.setOrderNo(buyorderInfo.getBuyorderExpenseNo());
                capitalBillDetailInfo.setRelatedId(buyorderInfo.getBuyorderExpenseId());
                capitalBillDetailInfo.setTraderType(2);
                capitalBillDetailInfo.setTraderId(buyorderInfo.getBuyorderExpenseDetailDto().getTraderId());
                capitalBillDetailInfo.setUserId(buyorderInfo.getCreator());
                //业务类型  订单收款
                capitalBillDetailInfo.setBussinessType(525);
                capitalBillDetailInfo.setAmount(buyorderInfo.getBuyorderExpenseDetailDto().getAccountPeriodAmount());
                if (belongUser != null && belongUser.getOrgName() != null && belongUser.getOrgId() != null) {
                    capitalBillDetailInfo.setOrgName(belongUser.getOrgName());
                    capitalBillDetailInfo.setOrgId(belongUser.getOrgId());
                }
                capitalBill.setCapitalBillDetail(capitalBillDetailInfo);
                //添加当前登陆人
                capitalBill.setCreator(user.getUserId());
                capitalBillService.saveCapitalBill(capitalBill);
                update.setStatus(1);
                logger.info("准备设置采购费用订单的状态" + buyorderInfo.getBuyorderExpenseId());
                buyorderExpenseApiService.updateBuyorderExpenseStatusByVerity(update);
                buyorderExpenseApiService.doDeliveryStatus(update.getBuyorderExpenseId());
                buyorderExpenseApiService.doArrivalStatus(update.getBuyorderExpenseId());
                saleOrderGoodsApiService.pay4SaleStatus(update.getBuyorderExpenseId());

            } else {
                BuyorderExpenseDto buyorder = new BuyorderExpenseDto();
                buyorder.setBuyorderExpenseId(buyorderInfo.getBuyorderExpenseId());
                buyorder.setStatus(1);
                logger.info("准备设置采购费用订单的状态" + buyorderInfo.getBuyorderExpenseId());
                buyorderExpenseApiService.updateBuyorderExpenseStatusByVerity(buyorder);
            }



        }

    }

    private User getUser() {
        User user = null;
        ServletRequestAttributes ra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (ra != null) {
            HttpServletRequest request = ra.getRequest();
            if (request != null && request.getSession() != null) {
                user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
            }
        }
        if (user == null) {
            user = new User();
            user.setCompanyId(1);
            user.setUserId(2);
            user.setCompanyName("南京贝登医疗有限公司");
            user.setUsername("njadmin");
        }
        return user;
    }

}
