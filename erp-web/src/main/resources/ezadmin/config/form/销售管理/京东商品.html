<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>京东商品</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="jd_goods" datasource="erp-datasourcetarget" success_url="reload">
<div id="APPEND_HEAD" class="append"></div>
<div class="layui-container">
    <form id="inputForm" method="post" class="layui-form">
        <div class="layui-card" group_name="EZ_DEFAULT_GROUP">
            <div class="layui-card-header">
                EZ_DEFAULT_GROUP
            </div>
            <div class="layui-card-body">
                <div class="layui-form-item ">
                    <label class="layui-form-label">京东商品ID</label>
                    <div class="layui-input-block form-group">
                        <input class="form-item" name="JD_GOODS_ID" type="text" value="" item_name="JD_GOODS_ID" placeholder="" style="" alias="" jdbctype="" data="" datatype="" lay_verify="required" item_desc="请输入京东商品编码" group_data="" valid_rule="" valid_msg="" item_max_upload="">
                    </div>
                </div>
                <div class="layui-form-item "><label class="layui-form-label">贝登SKU</label>
                    <div class="layui-input-block form-group">
                        <input class="form-item" name="VD_SKU_NO" type="text" value="" item_name="VD_SKU_NO" placeholder="" style="" alias="" jdbctype="" data="" datatype="" lay_verify="required" item_desc="请输入贝登订货号" group_data="" valid_rule="" valid_msg="" item_max_upload="">
                    </div>
                </div>
            </div>
        </div>
    </form>
    <form id="configForm" class="layui-form">
        <div class="layui-card" group_name="表单数据配置">
            <div class="layui-card-header">
                表单数据配置
            </div>
            <div class="layui-card-body">
            </div>
        </div>
        <div class="layui-form-item "><label class="layui-form-label">初始表达式</label>
            <div class="layui-input-block form-group">
      <pre class="form-item layui-code" id="init" name="INIT_EXPRESS" type="">StringBuilder sql=new StringBuilder();
 sql.append("select JD_GOODS_ID,VD_SKU_NO from V_JD_GOODS where  ID=${ID}  ");
return select(sql).get(0);</pre>
            </div>
        </div>
        <div class="layui-form-item ">
            <label class="layui-form-label">提交表达式</label>
            <div class="layui-input-block form-group">
      <pre class="form-item layui-code" id="submit" name="SUBMIT_EXPRESS" type="">
          import com.ezadmin.web.EzResult;
          sessionUserId=$$("EZ_SESSION_USER_ID_KEY");
 if(isBlank("ID")){
       exist=count("select JD_GOODS_ID from V_JD_GOODS where JD_GOODS_ID=#{JD_GOODS_ID}");
        if(exist>0){
            return EzResult.instance().fail().setMessage("已经存在，不能新增");
        }
	id=insert("insert into V_JD_GOODS(
				JD_GOODS_ID
,		VD_SKU_NO


		,ADD_TIME,MOD_TIME
		,CREATOR
	)VALUES(
				#{JD_GOODS_ID}
,		#{VD_SKU_NO}


		,NOW()  ,NOW()
		,"+sessionUserId+"

		  )");
 	return id;
}else{
	StringBuilder updateSql=new StringBuilder();
	updateSql.append("update V_JD_GOODS set ");
	updateSql.append("		 		VD_SKU_NO = #{VD_SKU_NO}
");
	updateSql.append("		,MOD_TIME=now()
		,UPDATER="+sessionUserId+"

");
	updateSql.append(" where ID=#{ID} ");

	update(updateSql);
	return ID;
}</pre>
            </div>
        </div>
        <div class="layui-form-item ">
            <label class="layui-form-label">删除表达式</label>
            <div class="layui-input-block form-group">
                <pre class="form-item layui-code" id="delete" name="DELETE_EXPRESS" type=""></pre>
            </div>
        </div>
        <div class="layui-form-item "><label class="layui-form-label">分组表达式</label>
            <div class="layui-input-block form-group">
                <pre class="form-item layui-code" name="GROUP_DATA" type=""></pre>
            </div>
        </div>
    </form>
</div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
<div id="APPEND_FOOT" class="append">
    <script>
        $(function(){
            if($("#ID").val()>0){
                $("#DESC_JD_GOODS_ID").html("京东ID不可修改");
                $("#ITEM_ID_JD_GOODS_ID").attr("disabled","disabled");
                $("#ITEM_ID_JD_GOODS_ID").css("border-color","#fff");
            }
        })

    </script>
</div>
</body>
</html>