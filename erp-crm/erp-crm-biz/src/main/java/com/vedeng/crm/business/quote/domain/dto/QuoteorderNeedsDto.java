package com.vedeng.crm.business.quote.domain.dto;

import com.vedeng.erp.system.dto.UserDto;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
    * 报价需求表
    */
@Data
public class QuoteorderNeedsDto {

    /**
     * 报价单ID
     */
    private Integer quoteorderId;

    /**
    * 需求主键
    */
    private Long quoteorderNeedsId;

    // 需求产品信息
    /**
    * 需求产品
    */
    private String productNeeds;

    /**
    * 需求数量
    */
    private String numNeeds;

    /**
     * 经销预算
     */
    private String distributeBudget;

    /**
     * 终端预算
     */
    private String terminalBudget;

    /**
     * 导入时间
     */
    private String addTime;

    // 需求备注
    /**
    * 需求备注
    */
    private String extraNeeds;

    /**
     * 需求负责人，当skuNo为空时，负责人展示该字段
     */
    private List<UserDto> headUserList;

    /**
     * 关联报价商品明细id
     */
    private List<Integer> quoteorderGoodsIds;

}
