<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<style>
    .el-dialog__body{
        border-top:solid 1px #f0f0f0;
        border-bottom:solid 1px #f0f0f0;
    }
    .el-message__icon {
        color: green !important;
    }
</style>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/tailwind/tailwind.min.css">

<div id="app" style="display: none;" class="p-8">
    <el-form :inline="true" :model="searchForm" label-position="right" label-width="100px"  size="mini">
        <el-form-item label="票据号码">
            <el-input v-model="searchForm.billNumber" maxlength="100"></el-input>
        </el-form-item>
        <el-form-item label="交易合同号">
            <el-input v-model="searchForm.transactionContractNo" maxlength="100"></el-input>
        </el-form-item>
        <el-form-item label="承兑银行">
            <el-select v-model="searchForm.acceptanceBank" placeholder="承兑银行" clearable>
                <el-option label="全部" value=""></el-option>
                <el-option label="民生银行" value="1"></el-option>
                <el-option label="交通银行" value="2"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="收票人名称">
            <el-input v-model="searchForm.payeeName" maxlength="100"></el-input>
        </el-form-item>
        <el-form-item label="票据状态">
            <el-select v-model="searchForm.billStatus" placeholder="票据状态" clearable>
                <el-option label="全部" value=""></el-option>
                <el-option label="已出票" value="1"></el-option>
                <el-option label="已承兑" value="2"></el-option>
                <el-option label="已收票" value="3"></el-option>
                <el-option label="已到期" value="4"></el-option>
                <el-option label="已终止" value="5"></el-option>
                <el-option label="已结清" value="6"></el-option>
            </el-select>
        </el-form-item>
        <div style="display: flex;justify-content: center;">
            <el-form-item>
                <el-button type="primary" @click="search" size="mini">搜索</el-button>
            </el-form-item>
            <el-form-item>
                <el-button @click="reset" size="mini">重置</el-button>
            </el-form-item>
        </div>
    </el-form>

    <el-table :data="items" style="width: 100%; margin-top: 20px;" border highlight-current-row>
        <el-table-column prop="billNumber" label="票据号码" header-align="center" align="center"
                         show-overflow-tooltip></el-table-column>
        <el-table-column prop="transactionContractNo" label="交易合同号" header-align="center"
                         align="center" show-overflow-tooltip></el-table-column>
        <el-table-column prop="acceptanceBank" label="承兑银行" width="100" header-align="center" align="center"
                         :formatter="formatAcceptanceBank" show-overflow-tooltip></el-table-column>
        <el-table-column prop="issueDate" label="出票日期" width="150" header-align="center"
                         align="center" show-overflow-tooltip>
            <template slot-scope="scope">
                <span>{{ parseTime(scope.row.issueDate, '{y}-{m}-{d}') }}</span>
            </template>
        </el-table-column>
        <el-table-column prop="maturityDate" label="票面到期日" width="150" header-align="center"
                         align="center" show-overflow-tooltip>
            <template slot-scope="scope">
                <span>{{ parseTime(scope.row.maturityDate, '{y}-{m}-{d}') }}</span>
            </template>
        </el-table-column>
        <el-table-column prop="maturityDate" label="承兑日期" width="150" header-align="center"
                         align="center" show-overflow-tooltip>
            <template slot-scope="scope">
                <span>{{ parseTime(scope.row.acceptanceDate, '{y}-{m}-{d}') }}</span>
            </template>
        </el-table-column>
        <el-table-column prop="billAmount" label="金额" width="120" header-align="center"
                         align="center"></el-table-column>
        <el-table-column prop="payeeName" label="收票人名称" header-align="center" align="center"
                         show-overflow-tooltip></el-table-column>
        <el-table-column prop="payeeAccount" label="收票人账号" header-align="center" align="center"
                         show-overflow-tooltip></el-table-column>
        <el-table-column prop="billStatus" label="票据状态" width="80" header-align="center" align="center"
                         :formatter="formatBillStatus"></el-table-column>
        <el-table-column prop="discountStatus" label="贴现状态" width="80" header-align="center" align="center"
                         :formatter="formatDiscountStatus"></el-table-column>
        <el-table-column label="操作" width="120" header-align="center" align="center" fixed="right">
            <template slot-scope="scope">
                <!-- 签收按钮，仅在 billStatus 为已承兑 2 时展示 -->
                <shiro:hasPermission name="/acceptanceBill/signUp.do">
                <el-button
                        type="text"
                        size="small"
                        @click="signReceipt(scope.row)"
                         v-if="scope.row.billStatus === 2" 
                >签收</el-button>
                </shiro:hasPermission>

                <!-- 贴现按钮，仅在 billStatus 为已收票 3 或已到期 4 且贴现状态 discountStatus 为未贴现 1 或贴现失败 4 时展示 -->
                <shiro:hasPermission name="/acceptanceBill/discount.do">
                <el-button
                        type="text"
                        size="small"
                        @click="showDiscountDialog(scope.row)"
                         v-if="(scope.row.billStatus === 3 || scope.row.billStatus === 4) &&
                  (scope.row.discountStatus === 1 || scope.row.discountStatus === 4)" 
                >贴现</el-button>
                </shiro:hasPermission>
            </template>
        </el-table-column>
    </el-table>

    <div style="display: flex; justify-content: flex-end;">
        <el-pagination
                layout="total, prev, pager, next, jumper"
                :total="total"
                :page-size="pageSize"
                :current-page.sync="pageNum"
                @current-change="handlePageChange"
        ></el-pagination>
    </div>

    <!-- 贴现弹框 -->
    <el-dialog title="贴现" :visible.sync="discountDialogVisible" width="30%">
        <el-form :model="discountForm" :rules="discountFormRules" ref="discountForm" label-width="80px">
            <el-form-item label="贴现利率" prop="rate">
                <el-input v-model="discountForm.rate" placeholder="0-99.9999" type="number" min="0" max="99.9999" step="0.0001" maxlength="6" style="width: 120px;"></el-input>&nbsp;&nbsp;%
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button @click="discountDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="confirmDiscount">确 定</el-button>
        </span>
    </el-dialog>
</div>

<script>
    new Vue({
        el: '#app',
        data: function () {
            return {
                searchForm: {
                    billNumber: '',
                    transactionContractNo: '',
                    acceptanceBank: '',
                    payeeName: '',
                    billStatus: ''
                },
                items: [],
                total: 0,
                pageSize: 20,
                pageNum: 1,
                discountDialogVisible: false, // 贴现弹框是否显示
                discountForm: {
                    billNumber: '',
                    rate: 0
                },
                discountFormRules: {
                    rate: [
                        { required: true, message: '请输入贴现利率', trigger: 'blur' }
                    ]
                },
                currentRow: null // 当前选中的行
            };
        },
        created() {
            this.fetchData();
        },
        mounted() {
            loadingApp()
        },
        methods: {
            fetchData() {
                axios({
                    url: '/acceptanceBill/page.do',
                    method: 'post',
                    data: {
                        pageNum: this.pageNum,
                        pageSize: this.pageSize,
                        param: this.searchForm
                    }
                }).then(response => {
                    this.items = response.data.data.list;
                    this.total = response.data.data.total;
                }).catch(error => {
                    console.error('Error fetching data:', error);
                });
            },
            search() {
                this.currentPage = 1;
                this.fetchData();
            },
            reset() {
                this.searchForm = {
                    billNumber: '',
                    transactionContractNo: '',
                    acceptanceBank: '',
                    payeeName: '',
                    billStatus: ''
                };
                this.search();
            },
            handlePageChange(newPage) {
                this.pageNum = newPage;
                this.fetchData();
            },
            formatBillStatus(row) {
                const statusMap = {
                    '1': '已出票',
                    '2': '已承兑',
                    '3': '已收票',
                    '4': '已到期',
                    '5': '已终止',
                    '6': '已结清'
                };
                return statusMap[row.billStatus] || '未知状态';
            },
            formatDiscountStatus(row) {
                const statusMap = {
                    '1': '未贴现',
                    '2': '贴现中',
                    '3': '已贴现',
                    '4': '贴现失败'
                };
                return statusMap[row.discountStatus] || '未知状态';
            },
            formatAcceptanceBank(row) {
                const bankMap = {
                    '1': '民生银行',
                    '2': '交通银行'
                };
                return bankMap[row.acceptanceBank] || '未知银行';
            },
            signReceipt(row) {
                // 弹出确认对话框
                this.$confirm('确定签收吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    const loadingInstance = this.$loading({
                        lock: true,
                        text: 'Loading',
                        spinner: 'el-icon-loading',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });

                    // 调用签收接口
                    axios({
                        url: '/acceptanceBill/signUp.do',
                        method: 'post',
                        params: {
                            billNumber: row.billNumber
                        }
                    }).then(response => {
                        if (response.data.code == 0) {
                            this.$message.info('签收成功');
                            this.search();
                            loadingInstance.close();
                        } else {
                            this.$message.error('签收失败：' + response.data.message);
                            loadingInstance.close();
                        }
                    }).catch(error => {
                        loadingInstance.close();
                        console.error('Error fetching data:', error);
                    });
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消签收'
                    });
                });
            },
            showDiscountDialog(row) {
                this.currentRow = row;
                this.discountForm.billNumber = row.billNumber;
                this.discountForm.rate = 0;
                this.discountDialogVisible = true;
            },
            confirmDiscount() {
                this.$refs['discountForm'].validate((valid) => {
                    if (valid) {
                        const loadingInstance = this.$loading({
                            lock: true,
                            text: 'Loading',
                            spinner: 'el-icon-loading',
                            background: 'rgba(0, 0, 0, 0.7)'
                        });

                        axios({
                            url: '/acceptanceBill/discount.do',
                            method: 'post',
                            params: {
                                billNumber: this.discountForm.billNumber,
                                rate: this.discountForm.rate
                            }
                        }).then(response => {
                            if (response.data.code == 0) {//0表示成功
                                this.search();
                                this.discountDialogVisible = false;
                                loadingInstance.close();
                                this.$message.info("贴现处理成功");
                            } else {
                                this.$message.error('贴现失败：' + response.data.message);
                                loadingInstance.close();
                            }
                        }).catch(error => {
                            loadingInstance.close();
                            console.error('Error fetching data:', error);
                        });
                    } else {
                        //this.$message.error('请输入贴现利率');
                        return false;
                    }
                });
            }
        }
    });
</script>