package com.vedeng.crm.visitrecord.service;

import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.crm.visitrecord.domain.dto.VisitRecordInputDto;
import com.vedeng.crm.visitrecord.domain.vo.VisitRecordVo;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/3/10
 */
public interface CrmVisitMessageService {

    /**
     *  发送消息通知
     * @param visitRecordVo
     */
    void sendMessage(VisitRecordInputDto visitRecordInputDto, Integer recordId, List<Integer>  tongxingIdsList, CurrentUser currentUser);

}
