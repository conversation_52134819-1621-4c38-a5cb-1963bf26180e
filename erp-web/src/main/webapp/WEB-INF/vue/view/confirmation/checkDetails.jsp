<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<!-- 引入样式 -->
<%--<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/element-ui/css/index.css">--%>

<style>
    .tab-link {
        color: #09f;
    }
</style>
<!-- 引入脚本 -->
<%--<script src="${pageContext.request.contextPath}/static/vue/element-ui/js/vue.js"></script>--%>
<%--<script src="${pageContext.request.contextPath}/static/vue/element-ui/js/ele-index.js"></script>--%>
<script src="${pageContext.request.contextPath}/static/js/confirmation/confirmation.js?rnd=${resourceVersionKey}"></script>

<div id="app">
    <el-row>
        <el-col>
            <%--页头--%>
            <div>
                <div  style="cursor: pointer;margin: 10px 0;" v-for="(d,index) in list" @click="changeSelect(d, index)">
                    确认单回传{{index + 1}}&ensp;&ensp;
                    <span class="tab-link" :class="{active: selectIndex == index}">
                        {{d.confirmationName}}
                    </span>
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <span>确认单类型:
                        <span v-if ="d.confirmationType == 1">销售上传</span>
                        <span v-if ="d.confirmationType == 2">物流上传</span>
                        <span v-if ="d.confirmationType != 1 && d.confirmationType != 2">其他确认</span>
                    </span>
                </div>
            </div>
        </el-col>
    </el-row>
    <el-row :gutter="30" style="padding-top: 30px">

        <el-col :span="14">
            <%--列表展示--%>
            <div class="grid-content bg-purple-light">
                <div>
                    确认单商品&ensp;&ensp;{{selected.confirmationName}}
                </div>
                <div v-for="p in batchInfoList" v-if="isShowConfirm" style="padding-top: 20px;padding-left: 80px">
                    <div v-if="approveList[p.id] || p.auditStatus == 1">审核通过</div>
                    <div v-if="p.auditStatus == 0">驳回：{{p.comments}}</div>
                    <div v-if="refuseList[p.id]">驳回：{{refuseList[p.id].refuseReason}}</div>
                    <el-checkbox
                                 :checked="approveList[p.id] || refuseList[p.id] || tempList[p.id] || p.auditStatus == 0 || p.auditStatus == 1"
                                 :disabled="approveList[p.id] || refuseList[p.id] || p.auditStatus == 0 || p.auditStatus == 1"
                                 @change="changeTempItem(p, $event)">
                        <div style="margin-bottom: 8px">
                            <span style="padding-left: 20px">{{p.batchNo}}</span>
                            <span style="padding-left: 20px">{{p.batchTime}}</span>
                            <el-button v-show="approveList[p.id] || refuseList[p.id]" @click="releaseCheck(p.id)" type="primary" size="mini" plain>
                                取消审核
                            </el-button>
                        </div>
                    </el-checkbox>
                    <el-table border stripe :data="p.batchExpressVos" style="font-size: 13px">
                        <el-table-column type="index" label="序号"></el-table-column>
                        <el-table-column prop="sku" label="订货号"></el-table-column>
                        <el-table-column prop="goodsName" label="产品名称"></el-table-column>
                        <el-table-column prop="brand" label="品牌"></el-table-column>
                        <el-table-column prop="model" label="型号"></el-table-column>
                        <el-table-column prop="num" label="实际签收数量"></el-table-column>
                        <el-table-column prop="logisticsOrderNo" label="物流单号"></el-table-column>
                    </el-table>
                </div>
                <div style="padding-top: 20px">
                批量操作：
                <el-checkbox
                             v-model="checkAll"
                             @change="handleCheckAllChange" style="padding-left: 7px">
                    全选
                </el-checkbox>
                </div>
                <p style="display: block;text-align: center">
                    <el-button type="primary" @click="approveItems">审核通过</el-button>
                    <el-button @click="refuseItems">驳回</el-button>
                </p>
            </div>
        </el-col>

        <el-col :span="9">
            <%--图片展示--%>
            <div class="grid-content bg-purple-light">
                <div style="padding-top: 20px;padding-bottom: 24px">
                    确认单预览&ensp;&ensp;{{selected.confirmationName}}
                </div>
                <div style="height: 630px;overflow: auto">
                <div v-for="pdf in selected.pdfList">
                    <a href="javascript:void(0)" @click = "showImg(pdf)">预览</a>
                <iframe :src="pdf" style="width: 100%;height: 400px"></iframe>
                </div>
                <%--pdf列表--%>
<%--                <div v-for="pdf in selected.pdfList">--%>
<%--                    <a href="javascript:void(0)" @click = "showPdf">{{pdf}}</a>--%>
<%--                <el-drawer--%>
<%--                        title="pdf预览"--%>
<%--                        :visible.sync="drawer"--%>
<%--                        :with-header="false">--%>
<%--                    <iframe :src="pdf" frameborder="0" style="width: 100%; height: 100%"></iframe>--%>
<%--                </el-drawer>--%>
<%--                </div>--%>
                <div style="padding-top: 20px">
                    <div v-for="image in selected.urlList" @click="showImg(image)" style="width: 100%">
                        <el-image style="width: 100%;height: 500px" :src="image"></el-image>
                    </div>
                </div>
                </div>
            </div>
        </el-col>
    </el-row>
    <el-row>
        <el-col>
            <p style="display: block;text-align: center;padding-top: 35px">
                <el-button type="primary" @click="selectInvoiceItems" >提 交</el-button>
                <el-button @click="cancel()">取 消</el-button>
            </p>
        </el-col>
    </el-row>

    <%--驳回弹框--%>
    <el-dialog
            title="填写驳回原因"
            :visible.sync="centerDialogVisible"
            width="30%"
            center>
        <el-form ref="ruleForm">
            <div style="padding-top: 20px" v-for="(r, index) in tempList">
                <el-form-item>
                    <span >批次：{{r.batchTime}}</span>
                    <el-input
                            type="textarea"
                            :rows="2"
                            placeholder="请输入内容"
                            v-model="r.refuseReason"
                            maxlength="100"
                            show-word-limit
                            @input="changeInput"
                            >
                    </el-input>
                    <div class="error" v-if="r.error" style="color: #e64545;">
                        请填写驳回原因
                    </div>
                </el-form-item>
            </div>
                <el-form-item>
                    <el-button type="primary" @click="submitRefuse">提 交</el-button>
                </el-form-item>
        </el-form>
    </el-dialog>
</div>

<script type="text/javascript">
    let saleOrderId = '${saleOrderId}';
    let vm = new Vue({
        el: '#app',
        data() {
            return {
                selectIndex: 0,
                purple: '',
                drawer: false,
                pdfSrc: '',
                selected: {
                    id: null,
                    confirmationName: '',
                    urlList: [],
                    pdfList: [],
                },
                batchInfoList: [{
                    id: null,
                    refuseReason: '',
                    batchNo: '',
                    batchExpressVos: [{
                        skuNo: '',
                        productName: '',
                        brandName: '',
                        model: '',
                        checkNum: null,
                        logisticsNo: '',
                        deliveryTime: null,
                        batchTime: '',
                    }],
                    auditStatus: null,
                    comments: '',
                }],
                isShowConfirm: false,
                replyMsg: {
                    saleOrderId: null,
                    approveList: [],
                    refuseList: [],
                },
                approveList: {},
                approveArr: [],
                refuseList: {},
                refuseArr: [],
                tempList: {},
                allList: {},
                refuseReason: '',
                confirmationInfo: {},
                centerDialogVisible: false,
                isIndeterminate: true,
                checkAll: false,
                selectedBatch: [],
                list: [],
                confirmationFormRecodes: [],
                rules: {
                    name: [
                            {required: true, message: '请填写驳回原因', trigger: 'change'}
                        ]
                    },
                ruleForm: {
                    list: [],
                },
                ruleForm1: {
                    list: [{
                        name: '',
                    }, {
                        name: ''
                    }],
                },
                isShow: false,
            }},

        created:
            function () {
                let loading = this.$loading({
                    lock: true
                })
                getConfirmationInfo(saleOrderId).then(res => {
                    let resData = res.data.data || {};
                    loading.close();
                    if (resData.confirmationFormRecodes && resData.confirmationFormRecodes.length){
                        this.list = resData.confirmationFormRecodes;
                        this.changeSelect(this.list[0], 0);
                        this.isShow=true;
                    }else {
                        this.$message.error("未查询到确认单详情数据")
                    }
                });

            },
        methods: {
            changeInput(){
              this.$forceUpdate()
            },
            changeColor() {
                return {active : true}
            },
            showPdf() {
                this.drawer = true
            },
            showImg(url) {
                window.open(url)
            },
            /*提交*/
            selectInvoiceItems() {
                let failList = [];
                this.list.forEach(item => {
                    let flag = true;
                    item.batchesRecodes.forEach(item1 => {
                        if (item1.auditStatus != 0 && item1.auditStatus != 1){
                            if(!this.approveList[item1.id] && !this.refuseList[item1.id]){
                                flag = false;
                            }
                        }
                    })
                    if(!flag){
                        failList.push(item.confirmationName);
                    }
                })
                if(failList.length){
                    this.$confirm("请完成确认单"+failList.join(",")+"的审核", {
                        showCancelButton: false,
                        type: 'error',
                        center: true
                    })
                    return false;
                }
                //提交
                for (item in this.approveList){
                    this.replyMsg.approveList.push(this.approveList[item])
                }
                for (item in this.refuseList){
                    this.replyMsg.refuseList.push(this.refuseList[item])
                }
                this.replyMsg.saleOrderId= saleOrderId;
                let data = JSON.parse(JSON.stringify(this.replyMsg));
                if (this.replyMsg.approveList.length === 0 && this.replyMsg.refuseList.length === 0){
                    this.$message.error("未选择任何批次")
                    return false;
                }
                axios({
                    url: '/orderstream/saleorder/checkConfirmationDetail.do',
                    method: 'post',
                    data: data
                }).then(response => {
                    if (response.status === 200) {
                        if (response.data.code === -1) {
                            layer.alert(response.data.message)
                        }
                    }
                });
                setTimeout(() => {
                    this.cancel();
                    parent.window.location.reload()
                },3000);
            },

            /*取消*/
            cancel() {
                window.parent.closableTab.close(
                    $(window.parent.document).find('[role=presentation].active .glyphicon.small')
                    ,window.parent.closableTab.resizeMove);
            },
            /*变更确认单*/
            changeSelect(d, index){
                this.selectIndex = index;
                this.isShow = false;
                this.isShowConfirm = false;
                this.selected.confirmationName = d.confirmationName
                this.selected.id=d.id
                this.selected.urlList = d.urlList
                this.selected.pdfList = d.pdfList
                this.batchInfoList = d.batchesRecodes
                this.confirmationInfo = d
                setTimeout(() => {
                    this.isShowConfirm = true;
                })
                setTimeout(() => {
                    this.isShow = true;
                })
            },
            changeTempItem(item, checked){
                console.log(item)
                let checkCount = item.length;
                this.checkAll = checkCount === this.list.length;
                if(checked){
                    if(!this.tempList[item.id]){
                        this.tempList[item.id] = item;
                    }
                }else{
                    if(this.tempList[item.id]){
                        delete this.tempList[item.id];
                    }
                }
            },
            /*审核通过*/
            approveItems(){
                // item是批次id
                let arr = Object.keys(this.tempList);
                if (arr.length === 0){
                    this.$message.closeAll();
                    this.$message.error("请勾选批次")
                }
                for(let item in this.tempList) {
                    if (!this.approveList[item]) {
                        this.approveList[item] = this.tempList[item];
                        this.approveArr.push(this.approveList[item]);
                    }
                }
                this.tempList = {};
                this.$forceUpdate();
            },
            /*驳回*/
            refuseItems(){
                let arr = Object.keys(this.tempList);
                if (arr.length === 0){
                    this.$message.closeAll();
                    this.$message.error("请勾选批次")
                }
                if(Object.keys(this.tempList).length){
                    this.centerDialogVisible = true;
                }

            },
            /*取消审核*/
            releaseCheck(id){
                this.isShowConfirm = false;
                if(this.approveList[id]){
                    delete this.approveList[id];
                }
                if (this.refuseList[id]){
                    this.refuseList[id].refuseReason='';
                    delete this.refuseList[id];
                }
                this.$forceUpdate();
                setTimeout(() => {
                    this.isShowConfirm = true;
                })
            },
            /*提交驳回原因*/
            submitRefuse(){
                let flag = true;
                for(let item in this.tempList){
                    if(!this.tempList[item].refuseReason || !this.tempList[item].refuseReason.trim()){
                        flag = false;
                        this.tempList[item].error = 1;
                    }else{
                        this.tempList[item].error = 0;
                    }
                }
                if(flag){
                    for(let item in this.tempList) {
                        if (!this.refuseList[item]) {
                            this.refuseList[item] = this.tempList[item];
                        }
                    };
                    this.centerDialogVisible = false
                    this.tempList = {};
                    this.$forceUpdate();
                }else{
                    this.$forceUpdate();
                }
            },
            /*全选*/
            handleCheckAllChange(v) {
                this.isShow=false;
                if(v){
                    this.list.forEach(item => {
                        item.batchesRecodes.forEach(brItem => {
                            if(!this.refuseList[brItem.id] && !this.approveList[brItem.id] && brItem.auditStatus !== 0 && brItem.auditStatus !== 1){
                                this.tempList[brItem.id] = brItem;
                            }
                        })
                    })
                }else {
                    this.tempList = {};
                }

                this.isShowConfirm = false;
                setTimeout(() => {
                    this.isShowConfirm = true;
                    this.isShow=true;
                })
            }
        },
    });

</script>

<style>
    .active {
        color: purple;
    }
    #app {
        padding-left: 13px;
        font-size: 13px;
    }
    .bg-purple-light {
    }
    .grid-content {
        padding-top: 8px;
        border-radius: 4px;
        min-height: 300px;
    }
    el-table-column {
        padding-top: 10px;
    }



</style>