package com.vedeng.erp.kingdee.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.dto.result.KingDeeSaleOutInitStockQueryResultDto;
import com.vedeng.erp.kingdee.service.KingDeeSaleInitOutStockService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryExtParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryFilterDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class KingDeeSaleInitOutStockServiceImpl implements KingDeeSaleInitOutStockService  {

    @Autowired
    KingDeeBaseApi kingDeeBaseApi;

    @Override
    public List<KingDeeSaleOutInitStockQueryResultDto> getKingDeeSaleInitOutStock(String outInNo) {
        // 调用查询金蝶接口
        KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        queryParam.setFormId(KingDeeFormConstant.SAL_INIT_OUT_STOCK);
        List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("fBillNo").value(outInNo).build());
        queryParam.setFilterString(queryFilterDtos);
        log.info("金蝶期初销售出库单查询入参：{}", JSON.toJSONString(queryParam));
        return kingDeeBaseApi.query(queryParam, KingDeeSaleOutInitStockQueryResultDto.class);
    }


}
