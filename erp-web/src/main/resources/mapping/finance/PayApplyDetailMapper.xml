<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.finance.dao.PayApplyDetailMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.finance.model.PayApplyDetail" >
    <id column="PAY_APPLY_DETAIL_ID" property="payApplyDetailId" jdbcType="INTEGER" />
    <result column="PAY_APPLY_ID" property="payApplyId" jdbcType="INTEGER" />
    <result column="DETAILGOODS_ID" property="detailgoodsId" jdbcType="INTEGER" />
    <result column="PRICE" property="price" jdbcType="DECIMAL" />
    <result column="NUM" property="num" jdbcType="DECIMAL" />
    <result column="TOTAL_AMOUNT" property="totalAmount" jdbcType="DECIMAL" />
  </resultMap>
  
  <sql id="Base_Column_List" >
    PAY_APPLY_DETAIL_ID, PAY_APPLY_ID, DETAILGOODS_ID, PRICE, NUM, TOTAL_AMOUNT
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_PAY_APPLY_DETAIL
    where PAY_APPLY_DETAIL_ID = #{payApplyDetailId,jdbcType=INTEGER}
  </select>

  <select id="getPayApplyDetailVo" parameterType="com.vedeng.finance.model.vo.PayApplyDetailVo" resultType="com.vedeng.finance.model.vo.PayApplyDetailVo">
    select
    COALESCE(SUM(NUM),0) as APPLICATION_SUM , COALESCE(SUM(TOTAL_AMOUNT),0) as TOTAL_AMOUNT_SUM
    from T_PAY_APPLY_DETAIL a
    left join T_PAY_APPLY b on a.PAY_APPLY_ID = b.PAY_APPLY_ID
    where b.VALID_STATUS in (0,1)
    <if test="detailgoodsId != null" >
      and a.DETAILGOODS_ID = #{detailgoodsId,jdbcType=INTEGER}
    </if>
    <if test="payType != null" >
      and b.PAY_TYPE = #{payType,jdbcType=INTEGER}
    </if>
    <if test="relatedId != null" >
      and b.RELATED_ID = #{relatedId,jdbcType=INTEGER}
    </if>
  </select>

    <select id="getPayApplyDetailList" resultMap="BaseResultMap" resultType="com.vedeng.finance.model.PayApplyDetail">
        select
        <include refid="Base_Column_List" />
        from T_PAY_APPLY_DETAIL
        where PAY_APPLY_ID = #{payApplyId,jdbcType=INTEGER}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_PAY_APPLY_DETAIL
    where PAY_APPLY_DETAIL_ID = #{payApplyDetailId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.finance.model.PayApplyDetail" >
    insert into T_PAY_APPLY_DETAIL (PAY_APPLY_DETAIL_ID, PAY_APPLY_ID, DETAILGOODS_ID, 
      PRICE, NUM, TOTAL_AMOUNT
      )
    values (#{payApplyDetailId,jdbcType=INTEGER}, #{payApplyId,jdbcType=INTEGER}, #{detailgoodsId,jdbcType=INTEGER}, 
      #{price,jdbcType=DECIMAL}, #{num,jdbcType=DECIMAL}, #{totalAmount,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.finance.model.PayApplyDetail" >
    insert into T_PAY_APPLY_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="payApplyDetailId != null" >
        PAY_APPLY_DETAIL_ID,
      </if>
      <if test="payApplyId != null" >
        PAY_APPLY_ID,
      </if>
      <if test="detailgoodsId != null" >
        DETAILGOODS_ID,
      </if>
      <if test="price != null" >
        PRICE,
      </if>
      <if test="num != null" >
        NUM,
      </if>
      <if test="totalAmount != null" >
        TOTAL_AMOUNT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="payApplyDetailId != null" >
        #{payApplyDetailId,jdbcType=INTEGER},
      </if>
      <if test="payApplyId != null" >
        #{payApplyId,jdbcType=INTEGER},
      </if>
      <if test="detailgoodsId != null" >
        #{detailgoodsId,jdbcType=INTEGER},
      </if>
      <if test="price != null" >
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="num != null" >
        #{num,jdbcType=DECIMAL},
      </if>
      <if test="totalAmount != null" >
        #{totalAmount,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.finance.model.PayApplyDetail" >
    update T_PAY_APPLY_DETAIL
    <set >
      <if test="payApplyId != null" >
        PAY_APPLY_ID = #{payApplyId,jdbcType=INTEGER},
      </if>
      <if test="detailgoodsId != null" >
        DETAILGOODS_ID = #{detailgoodsId,jdbcType=INTEGER},
      </if>
      <if test="price != null" >
        PRICE = #{price,jdbcType=DECIMAL},
      </if>
      <if test="num != null" >
        NUM = #{num,jdbcType=DECIMAL},
      </if>
      <if test="totalAmount != null" >
        TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
      </if>
    </set>
    where PAY_APPLY_DETAIL_ID = #{payApplyDetailId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.finance.model.PayApplyDetail" >
    update T_PAY_APPLY_DETAIL
    set PAY_APPLY_ID = #{payApplyId,jdbcType=INTEGER},
      DETAILGOODS_ID = #{detailgoodsId,jdbcType=INTEGER},
      PRICE = #{price,jdbcType=DECIMAL},
      NUM = #{num,jdbcType=DECIMAL},
      TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL}
    where PAY_APPLY_DETAIL_ID = #{payApplyDetailId,jdbcType=INTEGER}
  </update>

  <!-- 批量新增付款申请详情 -->
  <insert id="batchInsertApplyDetail" parameterType="com.vedeng.finance.model.PayApply">
    insert into T_PAY_APPLY_DETAIL
    <trim prefix="(" suffix=")">
      PAY_APPLY_DETAIL_ID, PAY_APPLY_ID, DETAILGOODS_ID, PRICE, NUM, TOTAL_AMOUNT
    </trim>
    values
    <foreach item="data" index="index" collection="detailList" separator=",">
      <trim prefix="(" suffix=")">
        NULL,
        #{payApplyId,jdbcType=INTEGER},
        #{data.detailgoodsId,jdbcType=INTEGER},
        #{data.price,jdbcType=DECIMAL},
        #{data.num,jdbcType=DECIMAL},
        #{data.totalAmount,jdbcType=DECIMAL}
      </trim>
    </foreach>

  </insert>
</mapper>