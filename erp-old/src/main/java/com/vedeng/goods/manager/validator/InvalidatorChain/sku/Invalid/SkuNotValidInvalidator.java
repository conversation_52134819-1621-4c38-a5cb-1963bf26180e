package com.vedeng.goods.manager.validator.InvalidatorChain.sku.Invalid;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.exception.InvalidatorChainException;
import com.vedeng.goods.manager.validator.GoodsInvalidatorChain;
import com.vedeng.goods.model.CoreSku;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

import static com.vedeng.goods.manager.validator.enums.GoodsInvalidatorErrorEnum.*;

/**
 * .(9)	该SKU的审核状态不处于审核中状态
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2021/9/1 16:05.
 * @author: Randy.Xu.
 */
@Service
public class SkuNotValidInvalidator extends GoodsInvalidatorChain {

    @Resource
    CoreSkuMapper coreSkuMapper;

    @Override
    public void doInvalid() {
        String skuNo = getSkuNo();
        List<String> skuNos = Collections.singletonList(skuNo);
        List<CoreSku> skuListByNoList = coreSkuMapper.getValidingSkuInfoListByNo(skuNos);
        if(CollectionUtils.isNotEmpty(skuListByNoList)){
            throw new InvalidatorChainException(SkuNoNotValidInvalidatorError.getCode(),SkuNoNotValidInvalidatorError.getMessage());
        }
    }
}
