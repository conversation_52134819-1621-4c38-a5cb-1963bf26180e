package com.smallhospital.service.impl;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.smallhospital.dao.ELContractSkuModifyMapper;
import com.smallhospital.dao.ElContractSkuMapper;
import com.smallhospital.model.ContractSku;
import com.smallhospital.model.ELCategory;
import com.smallhospital.model.ElContractSku;
import com.smallhospital.model.vo.ElContractSkuVO;
import com.smallhospital.service.ELContractSkuService;
import com.vedeng.common.util.JsonUtils;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;

/**
 * 医疗合同sku
 */
@Service
public class ELContractSkuServiceImpl implements ELContractSkuService {

    Logger logger= LoggerFactory.getLogger(ELContractSkuServiceImpl.class);

    @Autowired
    private ElContractSkuMapper contractSkuMapper;

    @Autowired
    private ELContractSkuModifyMapper modifyMapper;

    @Override
    public ElContractSkuVO findSkuNameById(Integer skuId) {
        return contractSkuMapper.findSkuNameById(skuId);
    }

    @Override
    public void batchUpdateContractSkus(List<ContractSku> skuLists) {
        contractSkuMapper.batchUpdateContractSkus(skuLists);
    }

    @Override
    public List<ElContractSkuVO> getModifySkus(Integer contractId) {
        return modifyMapper.findByModifyContractId(contractId);
    }

    @Override
    public List<ElContractSkuVO> findByContractId(Integer contractId) {
        return contractSkuMapper.findByContractId(contractId);
    }

    @Override
    public void batchAddContractSkus(List<ElContractSku> skuLists) {
        contractSkuMapper.batchAddContractSkus(skuLists);
    }

    @Override
    public void batchModifyContractSkus(List<ElContractSku> skuLists) {
        modifyMapper.batchAddContractSkus(skuLists);
    }

    @Override
    public void deleteById(Integer contactSkuId) {
        contractSkuMapper.deleteByPrimaryKey(contactSkuId);
    }

    @Override
    public void deleteModifySkuById(Integer contactModifySkuId) {
        modifyMapper.deleteByPrimaryKey(contactModifySkuId);
    }

    @Override
    public ElContractSku findById(Integer contactSkuId) {
        return contractSkuMapper.selectByPrimaryKey(contactSkuId);
    }

    @Override
    public void modify(ElContractSku sku) {
        contractSkuMapper.updateByPrimaryKeySelective(sku);
    }
}
