package com.vedeng.erp.kingdee.service.impl;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.erp.kingdee.domain.command.KingDeeInPutFeePlainInvoiceCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeInPutFeePlainInvoiceEntity;
import com.vedeng.erp.kingdee.dto.InPutFeePlainInvoiceDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeInPutFeePlainInvoiceMapper;
import com.vedeng.erp.kingdee.mapstruct.KingDeeInPutFeePlainInvoiceCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeInPutFeePlainInvoiceConvertor;
import com.vedeng.erp.kingdee.service.KingDeeIFPInvoiceApiService;
import com.vedeng.erp.kingdee.service.KingDeeIFPInvoiceService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.RepoStatus;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.OperateExtCommand;
import com.vedeng.infrastructure.kingdee.domain.command.UpdateExtCommand;
import com.vedeng.infrastructure.kingdee.service.impl.KingDeeMqBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

/**
 * @description: 进项费用普通发票类
 * @return:
 * @author: Suqin
 * @date: 2022/12/2 17:38
 **/
@Service
@Slf4j
public class KingDeeIFPInvoiceServiceImpl extends KingDeeMqBaseServiceImpl<InPutFeePlainInvoiceDto> implements KingDeeIFPInvoiceService, KingDeeIFPInvoiceApiService {

    @Autowired
    KingDeeInPutFeePlainInvoiceMapper kingDeeInPutFeePlainInvoiceMapper;

    @Autowired
    KingDeeInPutFeePlainInvoiceCommandConvertor kingDeeInPutFeePlainInvoiceCommandConvertor;

    @Autowired
    KingDeeInPutFeePlainInvoiceConvertor kingDeeInPutFeePlainInvoiceConvertor;

    private static final Logger logger = LoggerFactory.getLogger(KingDeeIFPInvoiceServiceImpl.class);

    @Override
    public void update(InPutFeePlainInvoiceDto inPutFeePlainInvoiceDto) {

        KingDeeInPutFeePlainInvoiceEntity entity = kingDeeInPutFeePlainInvoiceMapper.selectByFQzokBddjtid(inPutFeePlainInvoiceDto.getFQzokBddjtid());
        if (entity == null) {
            logger.info("当前客户信息尚未推送过金蝶，客户信息：{}", JSONObject.toJSONString(inPutFeePlainInvoiceDto));
            return;
        }

        inPutFeePlainInvoiceDto.setFid(entity.getFid());
        OperateExtCommand operateExtCommand = new OperateExtCommand(inPutFeePlainInvoiceDto.getFormId(), inPutFeePlainInvoiceDto.getFid(),
                        KingDeeConstant.ORG_ID.toString(), null);
        ArrayList<SuccessEntity> successUnAudit = kingDeeBaseApi.unAudit(operateExtCommand);

        if (CollUtil.isNotEmpty(successUnAudit)) {
            logger.info("反审核成功:{}",successUnAudit);
        }
        KingDeeInPutFeePlainInvoiceCommand command = kingDeeInPutFeePlainInvoiceCommandConvertor.toCommand(inPutFeePlainInvoiceDto);
        command.setFID(entity.getFid());
        command.setFDATE(entity.getFdate());
        RepoStatus update = kingDeeBaseApi.update(new UpdateExtCommand<>(command, inPutFeePlainInvoiceDto.getFormId()));
        ArrayList<SuccessEntity> successEntities = update.getSuccessEntitys();
        if (CollUtil.isNotEmpty(successEntities)) {
            KingDeeInPutFeePlainInvoiceEntity invoiceEntity = kingDeeInPutFeePlainInvoiceConvertor.toEntity(inPutFeePlainInvoiceDto);
            invoiceEntity.setInPutFeePlainInvoiceId(entity.getInPutFeePlainInvoiceId());
            kingDeeInPutFeePlainInvoiceMapper.updateByPrimaryKeySelective(invoiceEntity);
        }


        logger.info("修改进项费用专用发票时间推送金蝶：{}", JSON.toJSONString(inPutFeePlainInvoiceDto));

    }
}
