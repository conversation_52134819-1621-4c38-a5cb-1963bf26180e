package com.vedeng.common.listener;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.eventbus.Subscribe;
import com.vedeng.common.core.config.IObserver;
import com.vedeng.erp.finance.dto.InvoiceNumApiDto;
import com.vedeng.erp.saleorder.dto.SaleOrderGoodsDetailDto;
import com.vedeng.erp.saleorder.service.SaleOrderGoodsApiService;
import com.vedeng.logistics.model.ExpressDetail;
import com.vedeng.logistics.service.ExpressService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 发票相关业务监听器
 * @date 2023/10/20 16:56
 */
@Component
@Slf4j
public class ExpressListener implements IObserver {


    @Autowired
    private ExpressService expressService;

    @Autowired
    private SaleOrderGoodsApiService saleOrderGoodsApiService;

    /**
     * 计算收货数量监听事件
     *
     * @param invoiceNumApiDto invoiceRedConfirmationDto
     */
    @Subscribe
    public void getArrivalNum(InvoiceNumApiDto invoiceNumApiDto) {
        log.info("红字确认单信息 -> {}", JSON.toJSONString(invoiceNumApiDto));
        List<ExpressDetail> seGoodsNum = expressService.getSEGoodsNum(invoiceNumApiDto.getGoodsIds());

        if (CollUtil.isEmpty(seGoodsNum)) {
            return;
        }
        List<SaleOrderGoodsDetailDto> allVirtualGoods = saleOrderGoodsApiService.findAllVirtualGoodsBySaleorderId(invoiceNumApiDto.getSaleOrderId());
        for (SaleOrderGoodsDetailDto allVirtualGood : allVirtualGoods) {
            for (ExpressDetail expressDetail : seGoodsNum) {
                if (allVirtualGood.getSaleorderGoodsId().equals(expressDetail.getSaleOrderGoodsId())){
                    if (allVirtualGood.getArrivalStatus() ==2){
                        expressDetail.setArriveNum(allVirtualGood.getNum());
                    }
                    break;
                }
            }
        }

        List<InvoiceNumApiDto.SalesOrderInvoiceDto> salesOrderInvoiceList = invoiceNumApiDto.getGoodsIds().stream().map(x -> {

            BigDecimal arrivalNum = seGoodsNum.stream()
                    .filter(a -> a.getSaleOrderGoodsId().equals(x))
                    .map(a -> new BigDecimal(a.getArriveNum()))
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

            InvoiceNumApiDto.SalesOrderInvoiceDto salesOrderInvoiceDto = new InvoiceNumApiDto.SalesOrderInvoiceDto();

            salesOrderInvoiceDto.setSalesOrderGoodsId(x);
            salesOrderInvoiceDto.setArrivalNum(arrivalNum);
            return salesOrderInvoiceDto;
        }).collect(Collectors.toList());

        invoiceNumApiDto.setGoodsList(salesOrderInvoiceList);
    }
}
