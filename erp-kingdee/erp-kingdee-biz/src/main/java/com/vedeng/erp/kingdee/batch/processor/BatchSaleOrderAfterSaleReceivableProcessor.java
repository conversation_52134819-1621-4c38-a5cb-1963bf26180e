package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDetailDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchRInvoiceDetailJOperateLogDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInItemDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveCommonDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveCommonDetailLinkDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveCommonDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeReceiveQueryResultDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeSaleInStockQueryResultDto;
import com.vedeng.erp.kingdee.service.KingDeeReceiveCommonService;
import com.vedeng.erp.kingdee.service.KingDeeSaleInStockService;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 销售售后的负向标准应收单组装
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchSaleOrderAfterSaleReceivableProcessor implements ItemProcessor<BatchInvoiceDto, KingDeeReceiveCommonDto> {

    @Autowired
    private BatchInvoiceDetailDtoMapper batchInvoiceDetailDtoMapper;
    @Autowired
    private BatchRInvoiceDetailJOperateLogDtoMapper batchRInvoiceDetailJOperateLogDtoMapper;
    @Autowired
    private KingDeeReceiveCommonService kingDeeReceiveCommonService;
    @Autowired
    private KingDeeSaleInStockService kingDeeSaleInStockService;
    @Autowired
    private BatchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper batchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper;

    @Override
    public KingDeeReceiveCommonDto process(BatchInvoiceDto batchInvoiceDto) throws Exception {
        log.info("销售单的负向标准应收单{}", JSON.toJSONString(batchInvoiceDto));
        if (StrUtil.isBlank(batchInvoiceDto.getInvoiceNo()) || StrUtil.isBlank(batchInvoiceDto.getInvoiceCode())) {
            log.warn("红票无发票号或者发票代码");
            return null;
        }

        // 判断标准应收数据是否存在
        List<KingDeeReceiveQueryResultDto> kingDeeSaleReceivable = kingDeeReceiveCommonService.getKingDeeReceiveCommon(batchInvoiceDto.getInvoiceId().toString());
        if (CollUtil.isNotEmpty(kingDeeSaleReceivable)) {
            log.info("销售单的负向标准应收单,数据已存在:{}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }

        // 获取红字有效发票 关联的 入库单信息
        List<BatchRInvoiceDetailJOperateLogDto> jOperateLogDtos = batchRInvoiceDetailJOperateLogDtoMapper.
                findByInvoiceIdAndOperateType(batchInvoiceDto.getInvoiceId(), 5);
        batchInvoiceDto.setBatchRInvoiceDetailJOperateLogDtos(jOperateLogDtos);
        if (CollUtil.isEmpty(jOperateLogDtos)) {
            log.error(batchInvoiceDto.getInvoiceId() + "当前销售单红字有效未关联到入库单信息");
            return null;
        }

        // 获取红票和入库单关联关系map
        Map<Integer, List<BatchRInvoiceDetailJOperateLogDto>> listMap = jOperateLogDtos
                .stream().collect(Collectors.groupingBy(BatchRInvoiceDetailJOperateLogDto::getInvoiceDetailId));

        // 实物商品明细
        List<BatchInvoiceDetailDto> saleOrderInvoiceDetailList;
        if (CollectionUtils.isEmpty(batchInvoiceDto.getBatchInvoiceDetailDtoList())) {
            // 发票明细为空，则表示是非历史数据，直接取数据库查询发票明细
            log.info("开始处理销售负向标准应收单发票明细：{}", JSON.toJSONString(batchInvoiceDto));
            saleOrderInvoiceDetailList = batchInvoiceDetailDtoMapper.getSaleOrderInvoiceDetailList(batchInvoiceDto.getInvoiceId());
        } else {
            // 否则，则证明是从Excel中读取的发票明细
            log.info("开始处理21-22历史销售负向标准应收单发票明细：{}", JSON.toJSONString(batchInvoiceDto));
            saleOrderInvoiceDetailList = batchInvoiceDto.getBatchInvoiceDetailDtoList();
        }
        saleOrderInvoiceDetailList = saleOrderInvoiceDetailList.stream().filter(detailDto -> !Convert.toBool(detailDto.getIsVirtureSku(), false)).collect(Collectors.toList());
        // 获取发票明细map
        Map<Integer, BatchInvoiceDetailDto> invoiceDetail2Map = saleOrderInvoiceDetailList.stream()
                .collect(Collectors.toMap(BatchInvoiceDetailDto::getInvoiceDetailId, c -> c, (k1, k2) -> k1));

        // 构建负数应收单对象
        KingDeeReceiveCommonDto dto = new KingDeeReceiveCommonDto();
        dto.setFQzokBddjtId(batchInvoiceDto.getInvoiceId().toString());
        // VDERP-15040
        Date outInTime = batchRInvoiceDetailJOperateLogDtoMapper.findWarehouseOutByInvoiceId(batchInvoiceDto.getInvoiceId(), 5);
        dto.setFDate(DateUtil.formatDateTime(new Date(batchInvoiceDto.getAddTime()).compareTo(outInTime) < 0 ? outInTime : new Date(batchInvoiceDto.getAddTime())));
        dto.setFId("0");
        dto.setFCustomerId(batchInvoiceDto.getTraderCustomerId().toString());
        BigDecimal taxRate = Objects.isNull(batchInvoiceDto.getRatio()) ? BigDecimal.ZERO : batchInvoiceDto.getRatio().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);

        if (CollUtil.isEmpty(listMap)) {
            return null;
        }

        if (CollUtil.isEmpty(invoiceDetail2Map)) {
            return null;
        }

        // 先校验每一个发票的明细行都有对应的货票关系数据，由于listMap和invoiceDetail2Map都是根据invoiceDetailId聚合的，因此直接比较大小
        if (listMap.size() != invoiceDetail2Map.size()) {
            log.warn("当前发票存在明细行无对应的货票关系，整张票都不推送应收单,发票信息：{}", JSON.toJSONString(invoiceDetail2Map));
            return null;
        }

        for (Map.Entry<Integer, List<BatchRInvoiceDetailJOperateLogDto>> entry : listMap.entrySet()) {
            Integer k = entry.getKey();
            List<BatchRInvoiceDetailJOperateLogDto> v = entry.getValue();
            // 再比较发票明细的数量和绑定出库的数量是否相等
            BigDecimal outRelationNum = v.stream().map(BatchRInvoiceDetailJOperateLogDto::getNum).reduce(BigDecimal.ZERO, BigDecimal::add);
            BatchInvoiceDetailDto data = invoiceDetail2Map.get(k);
            if (data.getNum().abs().compareTo(outRelationNum) != 0) {
                log.warn("当前发票明细行中的数量不等于已绑定出库关系的数量，整张票均不推送标准应收单:{}", JSON.toJSONString(data));
                return null;
            }

            // 总金额
            BigDecimal totalAmount = Objects.isNull(data.getTotalAmount()) ? BigDecimal.ZERO : data.getTotalAmount();
            // 含税单价
            BigDecimal priceAverage = totalAmount.divide(data.getNum(), 6, RoundingMode.HALF_UP);

            // 先组装三级明细，基于三级明细组装二级明细
            List<KingDeeReceiveCommonDetailLinkDto> kingDeeReceiveCommonDetailLinkDtoList = this.assembleThirdDetails(k, v, priceAverage);

            // 组装金蝶应收单二级明细
            List<KingDeeReceiveCommonDetailDto> kingDeeReceiveCommonDetailDtoList = this.assembleSecondaryDetails(kingDeeReceiveCommonDetailLinkDtoList, taxRate, priceAverage);
            dto.getFEntityDetail().addAll(kingDeeReceiveCommonDetailDtoList);

            // 判断是会否所有二级明细下均有三级明细
            List<KingDeeReceiveCommonDetailDto> collect = kingDeeReceiveCommonDetailDtoList
                    .stream()
                    .filter(d -> CollUtil.isEmpty(d.getFEntityDetail_Link()))
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(collect)) {
                log.error("应收单所有二级明细下不存在三级明细，{}",JSON.toJSONString(collect));
                return null;
            }

        }


        if (CollUtil.isEmpty(dto.getFEntityDetail())) {
            log.error("应收单无二级明细，{}",JSON.toJSONString(dto));
            return null;
        }

        log.info("销售单的负向标准应收单构造成功{}", JSON.toJSONString(dto));
        return dto;
    }

    /**
     * 组装金蝶应收单二级明细
     *
     * @param taxRate      税率
     * @param priceAverage 含税单价
     */
    private List<KingDeeReceiveCommonDetailDto> assembleSecondaryDetails(List<KingDeeReceiveCommonDetailLinkDto> kingDeeReceiveCommonDetailLinkDtoList,
                                                                         BigDecimal taxRate,
                                                                         BigDecimal priceAverage) {
        List<KingDeeReceiveCommonDetailDto> kingDeeReceiveCommonDetailDtoList = new ArrayList<>();


        kingDeeReceiveCommonDetailLinkDtoList.forEach(link -> {
            // 计价数量
            BigDecimal totalNum = new BigDecimal(link.getFEntityDetailLinkFsalbaseqty());

            // 税率(这边是小数0.13类型的)
            BigDecimal rate = taxRate.divide(new BigDecimal("100"), 10, RoundingMode.HALF_UP);
            // 价税合计=含税单价*计价数量
            BigDecimal priceIncludeTax = priceAverage.multiply(totalNum).abs().negate();
            // 税额=[价税合计/（1+税率）]*税率 用乘法交换律做，防止两次除法过早丢失精度
            BigDecimal taxAmount = priceIncludeTax.multiply(rate).divide(BigDecimal.ONE.add(rate), 2, RoundingMode.HALF_UP).abs().negate();
            // 不含税金额=价税合计-税额
            BigDecimal nonTaxAmount = priceIncludeTax.subtract(taxAmount).abs().negate();

            KingDeeReceiveCommonDetailDto kingDeeReceiveCommonDetailDto = new KingDeeReceiveCommonDetailDto();
            kingDeeReceiveCommonDetailDto.setFMaterialId(link.getSku());
            // 商品在当前发票行的对应开票总数
            kingDeeReceiveCommonDetailDto.setFPriceQty(totalNum);
            // 数组中商品的录票单价平均值
            kingDeeReceiveCommonDetailDto.setFTaxPrice(priceAverage.abs());
            // 税率
            kingDeeReceiveCommonDetailDto.setFEntryTaxRate(taxRate);
            // erp 票的 明细
            kingDeeReceiveCommonDetailDto.setFQzokBddjhId(link.getFQzokBddjhId());
            kingDeeReceiveCommonDetailDto.setFSourceType("SAL_RETURNSTOCK");
            // 价税合计 发票sku商品录入的总额
            kingDeeReceiveCommonDetailDto.setFAllAmountForD(priceIncludeTax);
            kingDeeReceiveCommonDetailDto.setFNoTaxAmountForD(nonTaxAmount);
            kingDeeReceiveCommonDetailDto.setFTaxAmountForD(taxAmount);
            kingDeeReceiveCommonDetailDto.setFIsFree(false);

            kingDeeReceiveCommonDetailDto.setFEntityDetail_Link(CollUtil.newArrayList(link));

            kingDeeReceiveCommonDetailDtoList.add(kingDeeReceiveCommonDetailDto);

        });

        return kingDeeReceiveCommonDetailDtoList;
    }


    /**
     * 组装金蝶应收单三级明细
     * 需要 和二级明细 一对一产生关联关系
     *
     * @param v            发票详情和出入库详情关系表
     * @param priceAverage 含税单价
     */
    private List<KingDeeReceiveCommonDetailLinkDto> assembleThirdDetails(Integer k,
                                                                         List<BatchRInvoiceDetailJOperateLogDto> v,
                                                                         BigDecimal priceAverage) {

        List<KingDeeReceiveCommonDetailLinkDto> kingDeeReceiveCommonDetailLinkDtoList = new ArrayList<>();

        v.forEach(outInItem -> {
            log.info("开始组装金蝶应收单三级明细,货票关系{}", JSON.toJSONString(outInItem));
            // 金蝶销售售后入库单
            List<KingDeeSaleInStockQueryResultDto> query = kingDeeSaleInStockService.getKingDeeSaleInStocks(outInItem.getOutInNo());
            if (CollUtil.isEmpty(query)) {
                log.error("无法查询金蝶销售售后入库单{}", JSON.toJSONString(query));
                throw new KingDeeException("无法查询销售售后入库单");
            }
            // 金蝶入库单主单id
            KingDeeSaleInStockQueryResultDto kingDeeSaleInStockQueryResultDto = CollUtil.getFirst(query);

            // 获取金蝶出库单明细id
            // 1. 先根据发票和入库单关系表中的明细id进行过滤查询，如果存在则取对应的数量
            KingDeeSaleInStockQueryResultDto kingDeeSaleInStockQueryResultDtoDetail = query.stream()
                    .filter(q -> q.getF_QZOK_BDDJHID().equals(outInItem.getOperateLogId().toString())).findFirst().orElse(null);

            if (kingDeeSaleInStockQueryResultDtoDetail != null) {
                // 源单数据，销售退货入库单信息
                KingDeeReceiveCommonDetailLinkDto kingDeeReceiveCommonDetailLinkDto = new KingDeeReceiveCommonDetailLinkDto();
                kingDeeReceiveCommonDetailLinkDto.setFLinkId("0");
                // 金蝶入库单id
                kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFsbillId(kingDeeSaleInStockQueryResultDto.getFID());
                // 金蝶入库行id
                kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFsId(kingDeeSaleInStockQueryResultDtoDetail.getFEntity_FENTRYID());

                // 退货数量
                BigDecimal fRealQty = outInItem.getNum();
                kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFbasicunitqtyold(fRealQty.abs().negate().toString());
                kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFbasicunitqty(fRealQty.abs().negate().toString());
                kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFsalbaseqty(fRealQty.abs().negate().toString());
                kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFsalbaseqtyold(fRealQty.abs().negate().toString());
                kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFstockbaseqty(fRealQty.abs().negate().toString());
                kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFstockbaseqtyold(fRealQty.abs().negate().toString());

                BigDecimal thisLine = priceAverage.multiply(fRealQty).setScale(10, RoundingMode.HALF_UP);
                kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFallamountforDold(thisLine.toString());
                kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFallamountforD(thisLine.toString());


                kingDeeReceiveCommonDetailLinkDto.setSku(v.get(0).getSku());
                kingDeeReceiveCommonDetailLinkDto.setFQzokBddjhId(k.toString() + "-" + outInItem.getOperateLogId());

                kingDeeReceiveCommonDetailLinkDtoList.add(kingDeeReceiveCommonDetailLinkDto);
            } else {
                // 2. 当根据入库单原始明细id无法获取时，则可能是有出入库单明细id拼接，
                // 根据出入库关系表，拿到对应的出库单明细id，和入库单明细id，按照拼接规则 去获取对应的入库单
                List<BatchRWarehouseGoodsOutJWarehouseGoodsInDto> itemList = batchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper
                        .getByWarehouseGoodsInItemId(outInItem.getOperateLogId().longValue());
                for (BatchRWarehouseGoodsOutJWarehouseGoodsInDto item : itemList) {
                    String bbdjhid = outInItem.getOperateLogId().toString() + "-" + item.getWarehouseGoodsOutItemId().toString();
                    KingDeeSaleInStockQueryResultDto kingDeeSaleInStockQueryResultDtoSpecial = query.stream()
                            .filter(q -> q.getF_QZOK_BDDJHID().equals(bbdjhid)).findFirst().orElse(null);

                    if (Objects.isNull(kingDeeSaleInStockQueryResultDtoSpecial)) {
                        log.info("查询不到销售售后入库单明细单{},贝登单据行id{}", JSON.toJSONString(k), bbdjhid);
                        continue;
                    }

                    // 源单数据，销售退货入库单信息
                    KingDeeReceiveCommonDetailLinkDto kingDeeReceiveCommonDetailLinkDto = new KingDeeReceiveCommonDetailLinkDto();
                    kingDeeReceiveCommonDetailLinkDto.setFLinkId("0");
                    // 金蝶入库单id
                    kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFsbillId(kingDeeSaleInStockQueryResultDto.getFID());
                    // 金蝶入库行id
                    kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFsId(kingDeeSaleInStockQueryResultDtoSpecial.getFEntity_FENTRYID());

                    // 退票数量
                    BigDecimal returnInvoiceNum = outInItem.getNum();
                    // 实际退货数量
                    BigDecimal returnGoodsNum = new BigDecimal(kingDeeSaleInStockQueryResultDtoSpecial.getFRealQty());
                    // 当退票数量小于退货数量，取退票数量；否则取退货数量
                    BigDecimal fRealQty = returnInvoiceNum.compareTo(returnGoodsNum) < 0 ? returnInvoiceNum : returnGoodsNum;

                    kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFbasicunitqtyold(fRealQty.abs().negate().toString());
                    kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFbasicunitqty(fRealQty.abs().negate().toString());
                    kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFsalbaseqty(fRealQty.abs().negate().toString());
                    kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFsalbaseqtyold(fRealQty.abs().negate().toString());
                    kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFstockbaseqty(fRealQty.abs().negate().toString());
                    kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFstockbaseqtyold(fRealQty.abs().negate().toString());

                    BigDecimal thisLine = priceAverage.multiply(fRealQty).setScale(10, RoundingMode.HALF_UP);
                    kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFallamountforDold(thisLine.toString());
                    kingDeeReceiveCommonDetailLinkDto.setFEntityDetailLinkFallamountforD(thisLine.toString());

                    kingDeeReceiveCommonDetailLinkDto.setSku(v.get(0).getSku());
                    kingDeeReceiveCommonDetailLinkDto.setFQzokBddjhId(k.toString() + "-" + outInItem.getOperateLogId());

                    kingDeeReceiveCommonDetailLinkDtoList.add(kingDeeReceiveCommonDetailLinkDto);
                }
            }

        });
        return kingDeeReceiveCommonDetailLinkDtoList;
    }

}
