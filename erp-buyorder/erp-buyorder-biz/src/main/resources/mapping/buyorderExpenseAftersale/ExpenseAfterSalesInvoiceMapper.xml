<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.aftersale.mapper.ExpenseAfterSalesInvoiceMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesInvoiceEntity">
    <id column="EXPENSE_AFTER_SALES_INVOICE_ID" jdbcType="BIGINT" property="expenseAfterSalesInvoiceId" />
    <result column="EXPENSE_AFTER_SALES_ID" jdbcType="BIGINT" property="expenseAfterSalesId" />
    <result column="BUYORDER_EXPENSE_ITEM_ID" jdbcType="INTEGER" property="buyorderExpenseItemId" />
    <result column="INVOICE_CODE" jdbcType="VARCHAR" property="invoiceCode" />
    <result column="INVOICE_NO" jdbcType="VARCHAR" property="invoiceNo" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="RETURN_NUM" jdbcType="DECIMAL" property="returnNum" />
    <result column="IS_REFUND_INVOICE" jdbcType="INTEGER" property="isRefundInvoice" />
    <result column="RETURN_INVOICE_STATUS" jdbcType="INTEGER" property="returnInvoiceStatus" />
    <result column="INVOICE_ID" jdbcType="INTEGER" property="invoiceId" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
  </resultMap>
  <sql id="Base_Column_List">
    EXPENSE_AFTER_SALES_INVOICE_ID, EXPENSE_AFTER_SALES_ID, BUYORDER_EXPENSE_ITEM_ID, 
    INVOICE_CODE, INVOICE_NO, SKU, RETURN_NUM, IS_REFUND_INVOICE, RETURN_INVOICE_STATUS, 
    INVOICE_ID, IS_DELETE, CREATOR, CREATOR_NAME, MOD_TIME, UPDATER, UPDATER_NAME, REMARK,
    UPDATE_REMARK, ADD_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_EXPENSE_AFTER_SALES_INVOICE
    where EXPENSE_AFTER_SALES_INVOICE_ID = #{expenseAfterSalesInvoiceId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from T_EXPENSE_AFTER_SALES_INVOICE
    where EXPENSE_AFTER_SALES_INVOICE_ID = #{expenseAfterSalesInvoiceId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="EXPENSE_AFTER_SALES_INVOICE_ID" keyProperty="expenseAfterSalesInvoiceId" parameterType="com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesInvoiceEntity" useGeneratedKeys="true">
    insert into T_EXPENSE_AFTER_SALES_INVOICE (EXPENSE_AFTER_SALES_ID, BUYORDER_EXPENSE_ITEM_ID, 
      INVOICE_CODE, INVOICE_NO, SKU, 
      RETURN_NUM, IS_REFUND_INVOICE, RETURN_INVOICE_STATUS, 
      INVOICE_ID, IS_DELETE, CREATOR,
      CREATOR_NAME, MOD_TIME, UPDATER, 
      UPDATER_NAME, REMARK, UPDATE_REMARK,
      ADD_TIME)
    values (#{expenseAfterSalesId,jdbcType=BIGINT}, #{buyorderExpenseItemId,jdbcType=INTEGER}, 
      #{invoiceCode,jdbcType=VARCHAR}, #{invoiceNo,jdbcType=VARCHAR}, #{sku,jdbcType=VARCHAR}, 
      #{returnNum,jdbcType=DECIMAL}, #{isRefundInvoice,jdbcType=INTEGER}, #{returnInvoiceStatus,jdbcType=INTEGER},
      #{invoiceId,jdbcType=INTEGER}, #{isDelete,jdbcType=INTEGER}, #{creator,jdbcType=INTEGER},
      #{creatorName,jdbcType=VARCHAR}, #{modTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER}, 
      #{updaterName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR},
      #{addTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="EXPENSE_AFTER_SALES_INVOICE_ID" keyProperty="expenseAfterSalesInvoiceId" parameterType="com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesInvoiceEntity" useGeneratedKeys="true">
    insert into T_EXPENSE_AFTER_SALES_INVOICE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="expenseAfterSalesId != null">
        EXPENSE_AFTER_SALES_ID,
      </if>
      <if test="buyorderExpenseItemId != null">
        BUYORDER_EXPENSE_ITEM_ID,
      </if>
      <if test="invoiceCode != null">
        INVOICE_CODE,
      </if>
      <if test="invoiceNo != null">
        INVOICE_NO,
      </if>
      <if test="sku != null">
        SKU,
      </if>
      <if test="returnNum != null">
        RETURN_NUM,
      </if>
      <if test="isRefundInvoice != null">
        IS_REFUND_INVOICE,
      </if>
      <if test="returnInvoiceStatus != null">
        RETURN_INVOICE_STATUS,
      </if>
      <if test="invoiceId != null">
        INVOICE_ID,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="expenseAfterSalesId != null">
        #{expenseAfterSalesId,jdbcType=BIGINT},
      </if>
      <if test="buyorderExpenseItemId != null">
        #{buyorderExpenseItemId,jdbcType=INTEGER},
      </if>
      <if test="invoiceCode != null">
        #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNo != null">
        #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="returnNum != null">
        #{returnNum,jdbcType=DECIMAL},
      </if>
      <if test="isRefundInvoice != null">
        #{isRefundInvoice,jdbcType=INTEGER},
      </if>
      <if test="returnInvoiceStatus != null">
        #{returnInvoiceStatus,jdbcType=INTEGER},
      </if>
      <if test="invoiceId != null">
        #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesInvoiceEntity">
    update T_EXPENSE_AFTER_SALES_INVOICE
    <set>
      <if test="expenseAfterSalesId != null">
        EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=BIGINT},
      </if>
      <if test="buyorderExpenseItemId != null">
        BUYORDER_EXPENSE_ITEM_ID = #{buyorderExpenseItemId,jdbcType=INTEGER},
      </if>
      <if test="invoiceCode != null">
        INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNo != null">
        INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="returnNum != null">
        RETURN_NUM = #{returnNum,jdbcType=DECIMAL},
      </if>
      <if test="isRefundInvoice != null">
        IS_REFUND_INVOICE = #{isRefundInvoice,jdbcType=INTEGER},
      </if>
      <if test="returnInvoiceStatus != null">
        RETURN_INVOICE_STATUS = #{returnInvoiceStatus,jdbcType=INTEGER},
      </if>
      <if test="invoiceId != null">
        INVOICE_ID = #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where EXPENSE_AFTER_SALES_INVOICE_ID = #{expenseAfterSalesInvoiceId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesInvoiceEntity">
    <!--@mbg.generated-->
    update T_EXPENSE_AFTER_SALES_INVOICE
    set EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=BIGINT},
      BUYORDER_EXPENSE_ITEM_ID = #{buyorderExpenseItemId,jdbcType=INTEGER},
      INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR},
      INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR},
      SKU = #{sku,jdbcType=VARCHAR},
      RETURN_NUM = #{returnNum,jdbcType=DECIMAL},
      IS_REFUND_INVOICE = #{isRefundInvoice,jdbcType=INTEGER},
      RETURN_INVOICE_STATUS = #{returnInvoiceStatus,jdbcType=INTEGER},
      INVOICE_ID = #{invoiceId,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP}
    where EXPENSE_AFTER_SALES_INVOICE_ID = #{expenseAfterSalesInvoiceId,jdbcType=BIGINT}
  </update>

  <insert id="batchPartInsertSelective">
    insert into T_EXPENSE_AFTER_SALES_INVOICE
    (EXPENSE_AFTER_SALES_ID, BUYORDER_EXPENSE_ITEM_ID, INVOICE_NO,INVOICE_CODE, SKU, RETURN_NUM)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.expenseAfterSalesId,jdbcType=BIGINT}, #{item.buyorderExpenseItemId,jdbcType=INTEGER},#{item.invoiceNo,jdbcType=VARCHAR}, #{item.invoiceCode,jdbcType=VARCHAR},#{item.sku,jdbcType=VARCHAR},
      #{item.returnNum,jdbcType=DECIMAL})
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2022-10-27-->
  <select id="findByExpenseAfterSalesId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_EXPENSE_AFTER_SALES_INVOICE
        where EXPENSE_AFTER_SALES_ID=#{expenseAfterSalesId,jdbcType=BIGINT}
        AND IS_DELETE = 0
    </select>

  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_EXPENSE_AFTER_SALES_INVOICE
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="EXPENSE_AFTER_SALES_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPENSE_AFTER_SALES_INVOICE_ID = #{item.expenseAfterSalesInvoiceId,jdbcType=BIGINT} then #{item.expenseAfterSalesId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="BUYORDER_EXPENSE_ITEM_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPENSE_AFTER_SALES_INVOICE_ID = #{item.expenseAfterSalesInvoiceId,jdbcType=BIGINT} then #{item.buyorderExpenseItemId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="INVOICE_CODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPENSE_AFTER_SALES_INVOICE_ID = #{item.expenseAfterSalesInvoiceId,jdbcType=BIGINT} then #{item.invoiceCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="INVOICE_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPENSE_AFTER_SALES_INVOICE_ID = #{item.expenseAfterSalesInvoiceId,jdbcType=BIGINT} then #{item.invoiceNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="SKU = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPENSE_AFTER_SALES_INVOICE_ID = #{item.expenseAfterSalesInvoiceId,jdbcType=BIGINT} then #{item.sku,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="RETURN_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPENSE_AFTER_SALES_INVOICE_ID = #{item.expenseAfterSalesInvoiceId,jdbcType=BIGINT} then #{item.returnNum,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_REFUND_INVOICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPENSE_AFTER_SALES_INVOICE_ID = #{item.expenseAfterSalesInvoiceId,jdbcType=BIGINT} then #{item.isRefundInvoice,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="RETURN_INVOICE_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPENSE_AFTER_SALES_INVOICE_ID = #{item.expenseAfterSalesInvoiceId,jdbcType=BIGINT} then #{item.returnInvoiceStatus,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="INVOICE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPENSE_AFTER_SALES_INVOICE_ID = #{item.expenseAfterSalesInvoiceId,jdbcType=BIGINT} then #{item.invoiceId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPENSE_AFTER_SALES_INVOICE_ID = #{item.expenseAfterSalesInvoiceId,jdbcType=BIGINT} then #{item.isDelete,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPENSE_AFTER_SALES_INVOICE_ID = #{item.expenseAfterSalesInvoiceId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPENSE_AFTER_SALES_INVOICE_ID = #{item.expenseAfterSalesInvoiceId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPENSE_AFTER_SALES_INVOICE_ID = #{item.expenseAfterSalesInvoiceId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPENSE_AFTER_SALES_INVOICE_ID = #{item.expenseAfterSalesInvoiceId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPENSE_AFTER_SALES_INVOICE_ID = #{item.expenseAfterSalesInvoiceId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPENSE_AFTER_SALES_INVOICE_ID = #{item.expenseAfterSalesInvoiceId,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATE_REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPENSE_AFTER_SALES_INVOICE_ID = #{item.expenseAfterSalesInvoiceId,jdbcType=BIGINT} then #{item.updateRemark,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPENSE_AFTER_SALES_INVOICE_ID = #{item.expenseAfterSalesInvoiceId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
    </trim>
    where EXPENSE_AFTER_SALES_INVOICE_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.expenseAfterSalesInvoiceId,jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="getReturnInvoiceGoodsData" resultType="com.vedeng.erp.aftersale.dto.ReturnInvoiceGoodsDto">
    select
    TEASI.EXPENSE_AFTER_SALES_ID,
    SUM(if(TEASI.RETURN_NUM is null,0,TEASI.RETURN_NUM)) returnNum,
    TEASI.INVOICE_CODE,
    TEASI.INVOICE_NO,
    TBEI.BUYORDER_EXPENSE_ITEM_ID,
    SUM(if(TBEI.NUM is null,0,TBEI.NUM)) num,
    TBEI.GOODS_ID,
    TBEID.GOODS_NAME,
    TBEID.PRICE,
    TBEID.MODEL,
    TBEID.BRAND_NAME,
    VCS.SKU_NO sku,
    TBEID.UNIT_NAME unit
    from T_EXPENSE_AFTER_SALES_INVOICE TEASI
    left join T_BUYORDER_EXPENSE_ITEM TBEI on TEASI.BUYORDER_EXPENSE_ITEM_ID = TBEI.BUYORDER_EXPENSE_ITEM_ID
    left join V_CORE_SKU VCS on TBEI.GOODS_ID = VCS.SKU_ID
    left join T_BUYORDER_EXPENSE_ITEM_DETAIL TBEID on TBEI.BUYORDER_EXPENSE_ITEM_ID = TBEID.BUYORDER_EXPENSE_ITEM_ID
    where TEASI.EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=BIGINT}
    and TEASI.INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
    and TEASI.INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR}
    and TEASI.IS_REFUND_INVOICE = 1
    and TEASI.RETURN_INVOICE_STATUS = 0
    and TEASI.IS_DELETE = 0
    group by TEASI.BUYORDER_EXPENSE_ITEM_ID,TEASI.INVOICE_CODE,TEASI.INVOICE_NO;
  </select>
    <select id="queryReturnInvoiceInfo" resultType="com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesInvoiceEntity">
      SELECT
        EXPENSE_AFTER_SALES_INVOICE_ID,
        RETURN_NUM
      FROM
        T_EXPENSE_AFTER_SALES_INVOICE
      WHERE
        EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=BIGINT}
        AND BUYORDER_EXPENSE_ITEM_ID = #{buyorderExpenseItemId,jdbcType=INTEGER}
        AND INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
        AND INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR}
        AND IS_DELETE = 0
    </select>

    <update id="deleteInvoiceByExpenseAfterSalesId">
        UPDATE T_EXPENSE_AFTER_SALES_INVOICE
        SET IS_DELETE = 1
        WHERE EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=INTEGER}
              AND IS_DELETE = 0
    </update>



  <select id="queryNotSuccessData" resultMap="BaseResultMap">
    select
    TEASI.EXPENSE_AFTER_SALES_INVOICE_ID, TEASI.EXPENSE_AFTER_SALES_ID, TEASI.BUYORDER_EXPENSE_ITEM_ID,
    TEASI.INVOICE_CODE, TEASI.INVOICE_NO, TEASI.SKU, TEASI.RETURN_NUM, TEASI.IS_REFUND_INVOICE,
    TEASI.RETURN_INVOICE_STATUS, TEASI.INVOICE_ID, TEASI.IS_DELETE, TEASI.CREATOR, TEASI.CREATOR_NAME, TEASI.MOD_TIME,
    TEASI.UPDATER, TEASI.UPDATER_NAME, TEASI.REMARK, TEASI.UPDATE_REMARK, TEASI.ADD_TIME
    from T_EXPENSE_AFTER_SALES_INVOICE TEASI left join T_INVOICE TI on TEASI.INVOICE_CODE = TI.INVOICE_CODE
    and TEASI.INVOICE_NO = TI.INVOICE_NO
    where TI.INVOICE_ID in
    <foreach collection="list" item="item" separator="," open="(" close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
    <!-- 需要退票 待录票 或审核中  -->
    and TEASI.RETURN_INVOICE_STATUS in (0,2)
    and TEASI.IS_REFUND_INVOICE = 1
    and TEASI.IS_DELETE = 0
  </select>

  <update id="batchReturnInvoiceStatusSuccess">
    update T_EXPENSE_AFTER_SALES_INVOICE set RETURN_INVOICE_STATUS = 1 where
    EXPENSE_AFTER_SALES_INVOICE_ID in
    <foreach collection="list" item="item" open="(" close=")" separator=",">
      #{item,jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="selectNeedRefund" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_EXPENSE_AFTER_SALES_INVOICE TEASI
    where  TEASI.INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
    and TEASI.INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR}
    and TEASI.EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=BIGINT}
    and TEASI.IS_REFUND_INVOICE = 1
    <!-- 需要退票 待录票   -->
    and TEASI.RETURN_INVOICE_STATUS =0
    and TEASI.IS_DELETE = 0
  </select>

  <update id="writebackInvocie">
    update T_EXPENSE_AFTER_SALES_INVOICE TEASI
    set TEASI.RETURN_INVOICE_STATUS = 1 ,TEASI.INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
    <if test="remark!=null ">
      ,UPDATE_REMARK = remark
    </if>
    where TEASI.EXPENSE_AFTER_SALES_INVOICE_ID in
    <foreach collection="list" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
  </update>
  <update id="updateReturnInvoiceStatus">
    update T_EXPENSE_AFTER_SALES_INVOICE TEASI
    set TEASI.RETURN_INVOICE_STATUS = 0
    where TEASI.EXPENSE_AFTER_SALES_INVOICE_ID in
    <foreach collection="list" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
  </update>

  <select id="selectNeedReversal" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_EXPENSE_AFTER_SALES_INVOICE TEASI
    where  TEASI.INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
    and TEASI.INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR}
    and TEASI.EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=BIGINT}
    and TEASI.IS_REFUND_INVOICE = 1
    <!-- 需要退票 审核中  -->
    and TEASI.RETURN_INVOICE_STATUS =2
    and TEASI.IS_DELETE = 0
  </select>
    <select id="getAlreadyReturnInvoice" resultType="java.lang.Integer">
      SELECT
          teasi.INVOICE_ID
      FROM
          T_EXPENSE_AFTER_SALES_INVOICE teasi
      LEFT JOIN T_EXPENSE_AFTER_SALES_STATUS teass ON teasi.EXPENSE_AFTER_SALES_ID = teass.EXPENSE_AFTER_SALES_ID
      WHERE
          teasi.EXPENSE_AFTER_SALES_ID IN
          <foreach collection="afterSaleIdList" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=BIGINT}
          </foreach>
          AND teasi.INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
          AND teasi.INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR}
          AND teasi.RETURN_INVOICE_STATUS = 1
          AND teasi.IS_DELETE = 0
          AND teass.AFTER_SALES_STATUS = 2
    </select>
    <select id="getReturnInvoiceGoodsInfoList" resultType="com.vedeng.erp.aftersale.dto.ReturnInvoiceGoodsDto">
      SELECT
          tbeid.GOODS_NAME ,
          tbeid.BRAND_NAME ,
          tbeid.MODEL ,
          tbeid.PRICE,
          tbei.NUM ,
          tbeid.SKU,
          tbeid.UNIT_NAME ,
          teasi.RETURN_NUM ,
          teasi.RETURN_INVOICE_STATUS,
          tbei.GOODS_ID
      FROM
          T_EXPENSE_AFTER_SALES_INVOICE teasi
      LEFT JOIN T_BUYORDER_EXPENSE_ITEM tbei ON
          teasi.BUYORDER_EXPENSE_ITEM_ID = tbei.BUYORDER_EXPENSE_ITEM_ID
      LEFT JOIN T_BUYORDER_EXPENSE_ITEM_DETAIL tbeid ON
          tbeid.BUYORDER_EXPENSE_ITEM_ID = tbei.BUYORDER_EXPENSE_ITEM_ID
      WHERE
          teasi.EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=BIGINT}
          AND teasi.IS_DELETE = 0
          AND tbei.IS_DELETE = 0
          AND tbeid.IS_DELETE = 0
    </select>


  <select id="findByExpenseAfterSalesIdAndInvoiceCodeAndInvoiceNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_EXPENSE_AFTER_SALES_INVOICE
    where EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=BIGINT}
    and INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR}
    and INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
  </select>
</mapper>