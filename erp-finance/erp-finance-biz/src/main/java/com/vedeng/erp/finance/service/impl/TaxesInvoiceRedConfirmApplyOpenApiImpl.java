package com.vedeng.erp.finance.service.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.erp.finance.domain.dto.*;
import com.vedeng.erp.finance.dto.InvoiceDto;
import com.vedeng.erp.finance.enums.TaxesInterfaceCodeEnum;
import com.vedeng.erp.finance.service.AbstractTaxesOpenApiHandler;
import com.vedeng.erp.finance.service.InvoiceApplyDetailXhService;
import com.vedeng.erp.finance.service.InvoiceService;
import com.vedeng.erp.finance.service.TaxesOpenApiService;
import com.vedeng.infrastructure.taxes.base.ITaxesParam;
import com.vedeng.infrastructure.taxes.base.ITaxesResult;
import com.vedeng.infrastructure.taxes.domain.TaxesOpenApiResult;
import com.vedeng.infrastructure.taxes.enums.TaxesReturnCodeEnum;
import com.vedeng.infrastructure.taxes.sevice.ITaxesBaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.vedeng.infrastructure.taxes.common.constant.TaxesConstant.Y;

/**
 * 红字确认单申请
 */
@Service
@Slf4j
public class TaxesInvoiceRedConfirmApplyOpenApiImpl extends AbstractTaxesOpenApiHandler {

    @Autowired
    ITaxesBaseService iTaxesBaseService;

    @Autowired
    TaxesOpenApiService taxesOpenApiService;

    @Autowired
    private InvoiceApplyDetailXhService invoiceApplyDetailXhService;

    @Autowired
    private InvoiceService invoiceService;


    /**
     * 留给后人
     * 红字确认单申请时，所需入参需要根据初始化接口，进行删减，但由于初始化接口返回的出参属性不固定，所以申请前先查一次初始化
     * @param taxesParam
     * @param taxesInterfaceCodeEnum
     * @return
     */
    @Override
    public ITaxesResult execute(ITaxesParam taxesParam, TaxesInterfaceCodeEnum taxesInterfaceCodeEnum){
        // 返回初始化接口
        SaleInvoiceRedConfirmInitRequestDto initRequestDto = (SaleInvoiceRedConfirmInitRequestDto) taxesParam;
        ITaxesResult openapi = taxesOpenApiService.openapi(initRequestDto, TaxesInterfaceCodeEnum.COMFIRM_INIT);
        SaleInvoiceRedConfirmInitOrgResponseDto responseDto = (SaleInvoiceRedConfirmInitOrgResponseDto) openapi;

        String orgStr = responseDto.getOrgStr();
        JSONObject jsonObject = JSONObject.parseObject(orgStr);
        SaleInvoiceRedConfirmApplyRequestDto applyRequestDto = (SaleInvoiceRedConfirmApplyRequestDto) taxesParam;
        String applyRequest = assemble(jsonObject, applyRequestDto);
        TaxesOpenApiResult resultStr = (TaxesOpenApiResult)iTaxesBaseService.openapi(applyRequest, taxesInterfaceCodeEnum.getInterfaceCode());
        return resultStr;
    }

    /**
     * 后置处理
     * @param taxesParam
     * @param executeResult
     * @return
     */
    @Override
    protected ITaxesResult afterExecute(ITaxesParam taxesParam, ITaxesResult executeResult) {
        TaxesOpenApiResult openapi = (TaxesOpenApiResult) executeResult;
        String decodeStr = new String(Base64Utils.decodeFromString(openapi.getData()));
        SaleInvoiceRedConfirmApplyResponseDto taxesResult = JSONObject.parseObject(decodeStr, SaleInvoiceRedConfirmApplyResponseDto.class);
        if (Objects.isNull(taxesResult)){
            taxesResult = new SaleInvoiceRedConfirmApplyResponseDto();
        }
        TaxesOpenApiResult.ReturnInfo return_info = openapi.getReturn_info();

        String message = super.getMessage(return_info, taxesResult.getMessage());
        taxesResult.setReturnCode(return_info.getReturn_code());
        taxesResult.setReturnMessage(message);
        if (TaxesReturnCodeEnum.SUCCESS.getCode().equals(return_info.getReturn_code()) && Y.equals(taxesResult.getCode())){
            taxesResult.setIsSuccess(Boolean.TRUE);
        }
        return taxesResult;
    }

    /**
     * 封装
     * 接口文档：https://devproj.yuque.com/org-wiki-devproj-mr68ca/interface/ig52chopvwfofqzx?singleDoc#
     * @param jsonObject
     * @param applyRequestDto
     * @return
     */
    private String assemble(JSONObject jsonObject,SaleInvoiceRedConfirmApplyRequestDto applyRequestDto){
        log.info("红字申请组装入参：{}",JSONObject.toJSON(applyRequestDto));
        // 这里使用的是原始报文，所以要反解析序号，才能找到对应的序号 detailId -> 1,2,3
        InvoiceDto invoiceDto = invoiceService.findByInvoiceNo(applyRequestDto.getLzfpqdhm());
        if (Objects.isNull(invoiceDto)){
            log.info("红字申请组装数据后置处理结果为空：{}",applyRequestDto.getLzfpqdhm());
            return "";
        }
        List<SaleInvoiceRedConfirmApplyRequestDto.Hzqrxxmx> sourceList = applyRequestDto.getHzqrxxmxList();
        List<InvoiceApplyDetailXhDto> xhDto = invoiceApplyDetailXhService.getByApplyId(invoiceDto.getInvoiceApplyId());
        if (Objects.nonNull(xhDto)){
            // 转换id
            Map<Integer, InvoiceApplyDetailXhDto> map = xhDto.stream().collect(Collectors.toMap(InvoiceApplyDetailXhDto::getInvoiceApplyDetailId, Function.identity(),(key1, key2) -> key1));
            if (MapUtil.isEmpty(map)){
                map = MapUtil.newHashMap();
            }

            for (SaleInvoiceRedConfirmApplyRequestDto.Hzqrxxmx mxzb : sourceList) {
                InvoiceApplyDetailXhDto dto = map.get(Integer.valueOf(mxzb.getLzmxxh()));
                if (Objects.nonNull(dto)){
                    mxzb.setLzmxxh(dto.getXh());
                }
            }
        }
        
        // 获得的是 1，2，,3序号
        Map<Integer,SaleInvoiceRedConfirmApplyRequestDto.Hzqrxxmx> sourceMap = sourceList.stream().collect(Collectors.toMap(SaleInvoiceRedConfirmApplyRequestDto.Hzqrxxmx::getLzmxxh, Function.identity(),(key1, key2)->key1));

        JSONArray tagList = (JSONArray) jsonObject.get("hzqrxxmxList");
        JSONArray newTag = new JSONArray();
        tagList.stream().filter(Objects::nonNull).forEach(object->{
            JSONObject tag = (JSONObject) object;
            Integer xh = Integer.valueOf(tag.get("xh").toString());
            SaleInvoiceRedConfirmApplyRequestDto.Hzqrxxmx source = sourceMap.get(xh);
            if (Objects.isNull(source)){
               return;
            }
            tag.put("lzmxxh",source.getLzmxxh());
            tag.put("oldje",source.getOldje());
            tag.put("oldfpspsl",source.getOldfpspsl());
            tag.put("oldfpspdj",source.getOldfpspdj());
            tag.put("taxfpspdj",source.getTaxfpspdj());
            tag.put("taxje",source.getTaxje());

            tag.put("fpspdj",source.getFpspdj());
            tag.put("fpspsl",source.getFpspsl());
            tag.put("je",source.getJe());
            tag.put("se",source.getSe());
            newTag.add(tag);
        });

        jsonObject.put("chyyDm",applyRequestDto.getChyyDm());
        jsonObject.put("hzqrdmxsl",applyRequestDto.getHzqrdmxsl());
        jsonObject.put("hzcxje",applyRequestDto.getHzcxje());
        jsonObject.put("hzcxse",applyRequestDto.getHzcxse());
        jsonObject.put("hzqrxxmxList",newTag);

        JSONObject applyJson = new JSONObject();
        applyJson.put("nsrsbh",applyRequestDto.getNsrsbh());
        applyJson.put("data",jsonObject);
        log.info("红字申请组装数据后置处理结果：{}",applyJson.toJSONString());
        return applyJson.toString();
    }

}
