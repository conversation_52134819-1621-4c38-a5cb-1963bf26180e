package com.vedeng.erp.aftersale.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class AfterSalesInstallServiceRecord extends BaseEntity {
    /**
     * AFTER_SALES_SERVICE_ID
     **/
    private Integer afterSalesServiceId;

    /**
     * 售后单ID  AFTER_SALES_ID
     **/
    private Integer afterSalesId;

    /**
     * 本次验收时间  CHECK_DATE
     **/
    private Date checkDate;

    /**
     * 验收方式，1电话回访2纸单验收3短信通知  CHECK_TYPE
     **/
    private Integer checkType;

    /**
     * 录音ID  RECORD_ID
     **/
    private String recordId;

    /**
     * 验收结论(字典)  CHECK_CONCLUSION
     **/
    private Integer checkConclusion;

    /**
     * 是否删除 0否 1是  IS_DELETE
     **/
    private Integer isDelete;


}