package com.vedeng.erp.quote.manager.esign;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.base.api.dto.reqParam.BusinessInfo;
import com.vedeng.base.api.dto.reqParam.FileInfo;
import com.vedeng.base.api.dto.reqParam.SignCallbackDto;
import com.vedeng.base.api.dto.resParam.SignCompanyInfo;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.quote.domain.AuthorizationApplyEntity;
import com.vedeng.erp.quote.mapper.AuthorizationApplyMapper;
import com.vedeng.erp.quote.service.AuthorizationApplyApiService;
import com.vedeng.erp.system.service.UserWorkApiService;
import com.vedeng.infrastructure.esign.domain.ElectronicSignParam;
import com.vedeng.infrastructure.esign.domain.entity.ElectronicSignRecordEntity;
import com.vedeng.infrastructure.esign.enums.ElectronicSignBusinessEnums;
import com.vedeng.infrastructure.esign.handle.AbstractElectronicSignHandle;
import com.vedeng.infrastructure.esign.service.ElectronicSignRecordService;
import com.vedeng.system.model.vo.UrlToPdfParam;
import com.vedeng.system.service.OssUtilsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @description 授权书e签宝实现类
 * @date 2022/8/26 9:26
 */
@Component
@Slf4j
public class AuthorizationElectronicSignHandle extends AbstractElectronicSignHandle {

    @Autowired
    private OssUtilsService ossUtilsService;
    @Autowired
    private AuthorizationApplyMapper authorizationApplyMapper;
    @Autowired
    private AuthorizationApplyApiService authorizationApplyApiService;
    @Autowired
    private UserWorkApiService userWorkApiService;

    @Value("${erp.domain}")
    private String erpDomain;

    @Value("${html2Pdf.domain}")
    private String html2PdfDomain;

    @Value("${ht_remove_blank_page}")
    private Boolean isRemoveBlankPage;


    private static final String PRINT_CONTRACT_URL = "/order/quote/printAuthorizationElectronicSign.do?authorizationApplyId=";

    /**
     * 授权书详情页
     */
    private static final String AUTHORIZATION_VIEW = "{}/order/quote/authorizationView.do?quoteorderId={}&authorizationApplyId={}";

    private static final String RENDER_URL = "/api/render";

    private static final String FILENAME_PREFIX = "授权书";


    private static final String SUCCESS_TEMPLATE = "<font color=\"warning\">** 授权书盖章通知 **</font>\r\n> {}公司的{}产品授权书已完成盖章，<a href=\"{}\">点击查看</a>";
    private static final String ERROR_TEMPLATE = "<font color=\"warning\">** 授权书盖章通知 **</font>\r\n> {}公司的{}产品授权书自动签章失败，请联系Aadi。<a href=\"{}\">点击查看</a>";


    @Override
    protected void electronicSignFailCompensate(String error, ElectronicSignParam electronicSignParam) {
        log.error("授权书异步签章任务失败日志[{}],参数[{}]", error, JSON.toJSONString(electronicSignParam));

        List<ElectronicSignRecordEntity> electronicSignRecordList = electronicSignRecordService.getElectronicSignRecord(
                electronicSignParam.getAuthorizationApplyId().toString(),
                ElectronicSignBusinessEnums.SALE_AUTHORIZATION.getType());
        if (CollUtil.isEmpty(electronicSignRecordList)) {
            return;
        }
        electronicSignRecordList.forEach(electronicSignRecord -> {
            if (electronicSignRecord.getRetryNum() > MAX_RETRY_COUNT) {
                try {
                    AuthorizationApplyEntity authorizationApplyEntity = authorizationApplyMapper.selectByPrimaryKey(electronicSignParam.getAuthorizationApplyId());
                    String url = StrUtil.format(AUTHORIZATION_VIEW, erpDomain, authorizationApplyEntity.getQuoteorderId(), authorizationApplyEntity.getAuthorizationApplyId());
                    String msg = StrUtil.format(ERROR_TEMPLATE,
                            authorizationApplyEntity.getPurchaseOrBidding(),
                            authorizationApplyEntity.getSkuName(), url);
                    userWorkApiService.sendInvoiceMsg(authorizationApplyEntity.getCreator(), msg);
                } catch (Exception e) {
                    log.error("授权书异步签章任务成功日志,发送通知失败：{}", JSON.toJSONString(electronicSignRecord));
                }
            }
        });
    }

    @Override
    protected FileInfo toPdfGetFile(ElectronicSignParam electronicSignParam) {
        if (electronicSignParam.getAuthorizationApplyId() == null) {
            throw new ServiceException("电子签章:授权书id不能为空");
        }
        electronicSignParam.getBusinessInfo().setOrderNo(electronicSignParam.getAuthorizationApplyId().toString());

        AuthorizationApplyEntity authorizationApplyEntity = authorizationApplyMapper.selectByPrimaryKey(electronicSignParam.getAuthorizationApplyId());
        if (authorizationApplyEntity.getStandardTemplate() == 1) {
            FileInfo fileInfo = new FileInfo();
            // 增加溯源码
            String traceCodeOssUrl = authorizationApplyApiService.createTraceCode(ossHttp + ossUrl + authorizationApplyEntity.getNonStandardAuthorizationUrl(),
                    electronicSignParam.getAuthorizationApplyId());

            this.updateTraceCodeOssUrl(electronicSignParam, traceCodeOssUrl, authorizationApplyEntity.getNonStandardAuthorizationName());

            fileInfo.setFileName(FILENAME_PREFIX+ authorizationApplyEntity.getAuthorizationApplyNum() + ".pdf");
            fileInfo.setFilePath(traceCodeOssUrl);
            return fileInfo;
        }

        String contractTemplateUrl = erpDomain + PRINT_CONTRACT_URL + electronicSignParam.getAuthorizationApplyId();
        String html2PdfUrl = html2PdfDomain + RENDER_URL;
        UrlToPdfParam urlToPdfParam = UrlToPdfParam.defaultUrlToPdfParam(contractTemplateUrl);
        urlToPdfParam.getPdf().setScale(1.3);
        String migrateFile2OssUrl = ossUtilsService.migrateFile2Oss(html2PdfUrl, "pdf", "授权书", urlToPdfParam);
        log.info("电子签章:自动生成授权书模板合同,授权书id[{}]，合同url[{}]", electronicSignParam.getAuthorizationApplyId(), migrateFile2OssUrl);

        if (isRemoveBlankPage) {
            AtomicInteger retryCount = new AtomicInteger(0);
            migrateFile2OssUrl = removeBlankPdfPagesAndSaveFile2Oss(migrateFile2OssUrl, retryCount, null);
        }

        // 增加溯源码
        String traceCodeOssUrl = authorizationApplyApiService.createTraceCode(migrateFile2OssUrl, electronicSignParam.getAuthorizationApplyId());
        if (StringUtils.isBlank(traceCodeOssUrl)) {
            log.error("电子签章:自动生成授权书模板合同失败，授权书ID[{}]", electronicSignParam.getAuthorizationApplyId());
            throw new ServiceException("电子签章:自动生成授权书失败");
        }
        log.info("电子签章:自动生成授权书模板合同,授权书id[{}]，合同url[{}]", electronicSignParam.getAuthorizationApplyId(), traceCodeOssUrl);

        String fileName = "授权书(无章)"+ authorizationApplyEntity.getAuthorizationApplyNum() + ".pdf";
        this.updateTraceCodeOssUrl(electronicSignParam, traceCodeOssUrl, fileName);

        FileInfo fileInfo = new FileInfo();
        fileInfo.setFileName(FILENAME_PREFIX + authorizationApplyEntity.getAuthorizationApplyNum() + ".pdf");
        fileInfo.setFilePath(traceCodeOssUrl);
        return fileInfo;
    }

    @Override
    public ElectronicSignParam buildElectronicSignParam(Integer buyorderId, BusinessInfo businessInfo) {
        return null;
    }

    private void updateTraceCodeOssUrl(ElectronicSignParam electronicSignParam, String traceCodeOssUrl, String fileName) {
        //将增加溯源码的文件存到数据库
        AuthorizationApplyEntity apply = new AuthorizationApplyEntity();
        apply.setAuthorizationApplyId(electronicSignParam.getAuthorizationApplyId());

        String updateTraceCodeOssUrl = "";
        String uri = ossHttp + ossUrl;
        if (traceCodeOssUrl.startsWith(uri)) {
            updateTraceCodeOssUrl = traceCodeOssUrl.substring(uri.length());
        }
        apply.setNonStandardAuthorizationUrl(updateTraceCodeOssUrl);
        apply.setNonStandardAuthorizationName(fileName);
        authorizationApplyMapper.updateByPrimaryKeySelective(apply);
    }

    @Override
    protected void electronicSignSuccessCompensate(ElectronicSignParam electronicSignParam) {
    }


    @Override
    protected void preMqProcessors(SignCallbackDto signCallbackDto) {
        log.info("进入授权书实际消费业务类前置处理器,编号{}", signCallbackDto.getOrderNo());
        AtomicInteger retryCount = new AtomicInteger(0);
        AuthorizationApplyEntity authorizationApplyEntity = authorizationApplyMapper.selectByPrimaryKey(Integer.parseInt(signCallbackDto.getOrderNo()));
        String  fileName = FILENAME_PREFIX + authorizationApplyEntity.getAuthorizationApplyNum();
        log.info("消费-》oss保存文件名：{}", fileName);
        this.saveFile2Oss(signCallbackDto, retryCount, fileName);
    }


    @Override
    protected void mqProcessors(SignCallbackDto signCallbackDto) {
        log.info("进入授权书实际消费业务类,编号{}", signCallbackDto.getOrderNo());
        AuthorizationApplyEntity authorizationApplyEntity = authorizationApplyMapper.selectByPrimaryKey(Integer.parseInt(signCallbackDto.getOrderNo()));
        if (authorizationApplyEntity == null) {
            log.error("授权书不存在,编号{}", signCallbackDto.getOrderNo());
            return;
        }
        authorizationApplyEntity.setNonStandardAuthorizationSignUrl(signCallbackDto.getFileUrl());
        authorizationApplyEntity.setNonStandardAuthorizationSignName(FILENAME_PREFIX + authorizationApplyEntity.getAuthorizationApplyNum());
        authorizationApplyMapper.updateByPrimaryKeySelective(authorizationApplyEntity);
    }

    @Override
    protected void postMqProcessors(SignCallbackDto signCallbackDto) {
        log.info("授权书异步签章任务成功日志[{}]", JSON.toJSONString(signCallbackDto));
        List<ElectronicSignRecordEntity> electronicSignRecordList = electronicSignRecordService.getElectronicSignRecord(
                signCallbackDto.getOrderNo(),
                ElectronicSignBusinessEnums.SALE_AUTHORIZATION.getType());
        if (CollUtil.isEmpty(electronicSignRecordList)) {
            return;
        }


        try {
            log.info("授权书异步签章任务成功日志,发送企业微信内容:{}", JSON.toJSONString(signCallbackDto));
            AuthorizationApplyEntity authorizationApplyEntity = authorizationApplyMapper.selectByPrimaryKey(Integer.parseInt(signCallbackDto.getOrderNo()));
            String url = StrUtil.format(AUTHORIZATION_VIEW, erpDomain, authorizationApplyEntity.getQuoteorderId(), authorizationApplyEntity.getAuthorizationApplyId());
            String msg = StrUtil.format(SUCCESS_TEMPLATE,
                    authorizationApplyEntity.getPurchaseOrBidding(),
                    authorizationApplyEntity.getSkuName(),
                    url);
            userWorkApiService.sendInvoiceMsg(authorizationApplyEntity.getCreator(), msg);
        } catch (Exception e) {
            log.error("授权书异步签章任务成功日志,发送通知失败：{}", JSON.toJSONString(signCallbackDto));
        }
    }

    @Override
    protected void dealWithByMqException(String errorMsg, SignCallbackDto signCallbackDto) {
        List<ElectronicSignRecordEntity> electronicSignRecordList = electronicSignRecordService.getElectronicSignRecord(
                signCallbackDto.getOrderNo(),
                ElectronicSignBusinessEnums.SALE_AUTHORIZATION.getType());
        if (CollUtil.isEmpty(electronicSignRecordList)) {
            return;
        }
        electronicSignRecordList.forEach(electronicSignRecord -> {
            if (electronicSignRecord.getRetryNum() > MAX_RETRY_COUNT) {
                try {
                    AuthorizationApplyEntity authorizationApplyEntity = authorizationApplyMapper.selectByPrimaryKey(Integer.parseInt(signCallbackDto.getOrderNo()));
                    String url = StrUtil.format(AUTHORIZATION_VIEW, erpDomain, authorizationApplyEntity.getQuoteorderId(), authorizationApplyEntity.getAuthorizationApplyId());
                    String msg = StrUtil.format(ERROR_TEMPLATE,
                            authorizationApplyEntity.getPurchaseOrBidding(),
                            authorizationApplyEntity.getSkuName(), url);
                    userWorkApiService.sendInvoiceMsg(authorizationApplyEntity.getCreator(), msg);
                } catch (Exception e) {
                    log.error("授权书异步签章任务成功日志,发送通知失败：{}", JSON.toJSONString(signCallbackDto));
                }
            }
        });
    }

    @Override
    protected void getSignCompanyInfoList(ElectronicSignParam electronicSignParam,List<SignCompanyInfo> signCompanyInfoList) {
        //do nothing 由子实现类去做
        signCompanyInfoList.addAll(electronicSignParam.getSignCompanyInfoList());
    }


}
