package com.vedeng.erp.kingdee.service;

import com.vedeng.erp.kingdee.dto.KingDeeCustomerDto;
import com.vedeng.infrastructure.kingdee.service.KingDeeMqBaseService;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶供应商apiService
 * @date 2022/8/26 15:18
 */
public interface KingDeeCustomerApiService extends KingDeeMqBaseService<KingDeeCustomerDto> {


    void query(KingDeeCustomerDto kingDeeCustomerDto);


}
