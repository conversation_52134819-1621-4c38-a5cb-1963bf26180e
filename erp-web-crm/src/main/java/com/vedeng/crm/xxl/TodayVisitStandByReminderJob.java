package com.vedeng.crm.xxl;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/3/11
 */

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.crm.visitrecord.service.CrmVisitRecordService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 拜访当天通知信息-不可重复执行，会重复发送提醒
 * -根据计划拜访日期，设定当天上午9：00，发送通知
 */
@Component
@JobHandler(value="TodayVisitStandByReminderJob")
@Slf4j
public class TodayVisitStandByReminderJob extends AbstractJobHandler {

    @Autowired
    private CrmVisitRecordService crmVisitRecordService;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("==================拜访当天通知信息job开始====================");
        crmVisitRecordService.remindVisitToday();
        XxlJobLogger.log("==================拜访当天通知信息job结束====================");
        return SUCCESS;
    }


}
