package com.vedeng.erp.trader.dto;

import com.vedeng.common.core.base.BaseDto;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/1/4 15:35
 * @version 1.0
 */
@Getter
@Setter
public class RSalesJTraderDto extends BaseDto {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 交易者ID
     */
    private Integer traderId;

    /**
     * 客户名称
     */
    private String traderName;

    /**
     * 客户ID
     */
    private Integer traderCustomerId;

    /**
     * 销售人员ID
     */
    private Integer saleUserId;

    /**
     * 销售人员名称
     */
    private String saleUserName;

    /**
     * 销售人员中文名称
     */
    private String saleUserChineseName;

    /**
     * 操作人员中文名称
     */
    private String creatorChineseName;

    /**
     * 操作类型 delete insert
     */
    private String operateType;

    /**
     * 是否删除
     */
    private Integer isDeleted;
}