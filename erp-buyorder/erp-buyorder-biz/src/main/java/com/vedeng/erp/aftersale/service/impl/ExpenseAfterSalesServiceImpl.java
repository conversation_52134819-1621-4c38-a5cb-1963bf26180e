package com.vedeng.erp.aftersale.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.common.dto.StepsNodeDto;
import com.common.enums.StepsTypeEnum;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.utils.CustomUtils;
import com.vedeng.common.core.utils.ErpSpringBeanUtil;
import com.vedeng.common.core.utils.numgenerator.BillNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.bean.NoGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.aftersale.api.AftersaleInfoApi;
import com.vedeng.erp.aftersale.common.constant.ExpenseAfterSalesConstant;
import com.vedeng.erp.aftersale.domain.entity.*;
import com.vedeng.erp.aftersale.dto.*;
import com.vedeng.erp.aftersale.enums.ExpenseAfterSalesInvoiceStatusEnum;
import com.vedeng.erp.aftersale.enums.ExpenseAfterSalesStatusEnum;
import com.vedeng.erp.aftersale.mapper.*;
import com.vedeng.erp.aftersale.mapstruct.ExpenseAfterSalesConverter;
import com.vedeng.erp.aftersale.mapstruct.ExpenseAfterSalesItemConverter;
import com.vedeng.erp.aftersale.mapstruct.RExpenseAfterSalesJSaleorderConvert;
import com.vedeng.erp.aftersale.service.AfterSalesApiService;
import com.vedeng.erp.aftersale.service.ExpenseAfterSalesApiService;
import com.vedeng.erp.aftersale.service.ExpenseAfterSalesService;
import com.vedeng.erp.buyorder.dto.*;
import com.vedeng.erp.buyorder.service.BuyorderExpenseApiService;
import com.vedeng.erp.buyorderexpense.common.enums.AuditEnum;
import com.vedeng.erp.buyorderexpense.common.constant.BuyorderExpenseConstant;
import com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseDetailEntity;
import com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseEntity;
import com.vedeng.erp.buyorderexpense.domain.entity.RBuyorderExpenseJSaleorderEntity;
import com.vedeng.erp.buyorderexpense.mapper.BuyorderExpenseDetailMapper;
import com.vedeng.erp.buyorderexpense.mapper.BuyorderExpenseItemMapper;
import com.vedeng.erp.buyorderexpense.mapper.BuyorderExpenseMapper;
import com.vedeng.erp.buyorderexpense.mapper.RBuyorderExpenseJSaleorderMapper;
import com.vedeng.erp.buyorderexpense.service.BuyorderExpenseService;
import com.vedeng.erp.finance.dto.*;
import com.vedeng.erp.finance.service.BankBillApiService;
import com.vedeng.erp.finance.service.CapitalBillApiService;
import com.vedeng.erp.finance.service.InvoiceApiService;
import com.vedeng.erp.finance.service.InvoiceReversalApiService;
import com.vedeng.erp.system.dto.OrganizationDto;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.OrganizationApiService;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.dto.TraderSupplierDto;
import com.vedeng.erp.trader.service.TraderSupplierApiService;
import com.vedeng.finance.constant.CapitalBillBussinessTypeEnum;
import com.vedeng.finance.constant.CapitalBillOrderTypeEnum;
import com.vedeng.infrastructure.file.domain.Attachment;
import com.vedeng.infrastructure.file.mapper.AttachmentMapper;
import com.vedeng.trader.model.TraderBussinessData;
import com.vedeng.trader.service.TraderDataService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/20 10:45
 */
@Service
public class ExpenseAfterSalesServiceImpl implements ExpenseAfterSalesService, ExpenseAfterSalesApiService {

    public static final Logger log = LoggerFactory.getLogger(ExpenseAfterSalesServiceImpl.class);

    @Autowired
    private ExpenseAfterSalesItemMapper expenseAfterSalesItemMapper;

    @Autowired
    private BuyorderExpenseItemMapper buyorderExpenseItemMapper;

    @Autowired
    private BuyorderExpenseMapper buyorderExpenseMapper;

    @Autowired
    private ExpenseAfterSalesMapper expenseAfterSalesMapper;

    @Autowired
    private ExpenseAfterSalesConverter expenseAfterSalesConverter;

    @Autowired
    private ExpenseAfterSalesInvoiceMapper expenseAfterSalesInvoiceMapper;

    @Autowired
    private ExpenseAfterSalesStatusMapper expenseAfterSalesStatusMapper;

    @Autowired
    private BuyorderExpenseDetailMapper buyorderExpenseDetailMapper;

    @Autowired
    private CapitalBillApiService capitalBillApiService;

    @Autowired
    private InvoiceApiService invoiceApiService;

    @Autowired
    private TraderSupplierApiService traderSupplierApiService;

    @Autowired
    private InvoiceReversalApiService invoiceReversalApiService;

    @Autowired
    private BuyorderExpenseApiService buyorderExpenseApiService;

    @Autowired
    private BankBillApiService bankBillApiService;

    @Autowired
    private OrganizationApiService organizationApiService;

    @Autowired
    private AttachmentMapper attachmentMapper;

    @Autowired
    private TraderDataService traderDataService;

    @Autowired
    private UserApiService userApiService;

    @Autowired
    private RExpenseAfterSalesJSaleorderMapper rExpenseAfterSalesJSaleorderMapper;

    @Autowired
    private RExpenseAfterSalesJSaleorderConvert rExpenseAfterSalesJSaleorderConvert;

    @Autowired
    private RBuyorderExpenseJSaleorderMapper rBuyorderExpenseJSaleorderMapper;

    @Autowired
    private BuyorderExpenseService buyorderExpenseService;

    @Autowired
    private AfterSalesApiService afterSalesApiService;

    @Autowired
    private ExpenseAfterSalesItemConverter expenseAfterSalesItemConverter;


    @Override
    public ExpenseAfterSalesDto getAddEditExpenseAfterSalesInfo(Long expenseAfterSalesId, Integer buyorderExpenseId) {
        ExpenseAfterSalesDto expenseAfterSalesDto = new ExpenseAfterSalesDto();
        // 编辑费用单
        if (expenseAfterSalesId != null) {
            ExpenseAfterSalesEntity expenseAfterSalesEntity = expenseAfterSalesMapper.selectByPrimaryKey(expenseAfterSalesId);
            expenseAfterSalesDto = expenseAfterSalesConverter.toDto(expenseAfterSalesEntity);
            expenseAfterSalesDto.setAttachmentList(attachmentMapper.queryListByRelatedIdAndFunction(expenseAfterSalesId.intValue(), 4134));
            // 退货，此时类型不可修改，因此只需要查出退货商品信息列表
            if (expenseAfterSalesDto.getExpenseAfterSalesType().equals(ExpenseAfterSalesConstant.EXPENSE_RETURN_GOODS)) {
                expenseAfterSalesDto.setExpenseItemList(getExpenseItemInfoForAfterSale(expenseAfterSalesId, buyorderExpenseId));
            }
            if (expenseAfterSalesDto.getExpenseAfterSalesType().equals(ExpenseAfterSalesConstant.EXPENSE_RETURN_INVOICE)) {
                // 退票，此时类型不可修改，因此只需要查出 退票信息列表
                expenseAfterSalesDto.setItemAndInvoiceDtoList(getExpenseItemAndInvoiceForAfterSale(expenseAfterSalesId, buyorderExpenseId));
            }
        } else {
            // 新增费用售后单，默认只查出 退货商品列表
            expenseAfterSalesDto.setExpenseItemList(getExpenseItemInfoForAfterSale(null, buyorderExpenseId));
            // 查出费用单的 traderContactId
            BuyorderExpenseDetailEntity expenseDetailEntity = Optional.ofNullable(buyorderExpenseDetailMapper.selectByBuyorderExpenseId(buyorderExpenseId)).orElse(new BuyorderExpenseDetailEntity());
            expenseAfterSalesDto.setTraderContactId(expenseDetailEntity.getTraderContactId());
        }
        return expenseAfterSalesDto;
    }

    @Override
    public List<BuyorderExpenseItemDto> getExpenseItemInfoForAfterSale(Long expenseAfterSalesId, Integer buyOrderExpenseId) {
        // step1 先查出费用单的所有商品信息
        List<BuyorderExpenseItemDto> expenseItemDtoList = buyorderExpenseItemMapper.selectByBuyorderExpenseIdAndIsDelete(buyOrderExpenseId, 0);

        // step2 查出费用单对应的 已完结售后费用单的商品退货数量之和
        List<Integer> expenseItemIdList = expenseItemDtoList.stream().map(BuyorderExpenseItemDto::getBuyorderExpenseItemId).collect(Collectors.toList());
        List<ExpenseAfterSalesItemEntity> itemReturnNumList = expenseAfterSalesItemMapper.getItemReturnNum(expenseItemIdList);

        // step3 过滤出 可售后数量大于0的所有费用单商品
        List<BuyorderExpenseItemDto> buyOrderExpenseItemDtoList = new ArrayList<>();
        Map<Integer, Integer> map = itemReturnNumList.stream().collect(Collectors.toMap(ExpenseAfterSalesItemEntity::getBuyorderExpenseItemId, ExpenseAfterSalesItemEntity::getReturnNum));
        expenseItemDtoList.forEach(dto -> {
            Integer afterSalesQuantity = map.get(dto.getBuyorderExpenseItemId());
            dto.setAfterSalesQuantity(afterSalesQuantity != null ? dto.getNum() - afterSalesQuantity : dto.getNum());
            if (dto.getAfterSalesQuantity() > 0) {
                buyOrderExpenseItemDtoList.add(dto);
            }
        });

        // step4 计算 已收(录)票数量
        buyOrderExpenseItemDtoList.forEach(item -> {
            item.setInvoicedNum(invoiceApiService.getAlreadyInvoiceNumByGoodsId(item.getBuyorderExpenseItemId()));
            // step5 编辑时 查出 售后数量 和 费用售后itemId
            if (expenseAfterSalesId != null) {
                Optional<ExpenseAfterSalesItemEntity> afterSalesItemEntity = Optional.ofNullable(expenseAfterSalesItemMapper.getReturnNum(expenseAfterSalesId, item.getBuyorderExpenseItemId()));
                if (afterSalesItemEntity.isPresent()) {
                    item.setReturnNum(afterSalesItemEntity.get().getReturnNum());
                    item.setExpenseAfterSalesItemId(afterSalesItemEntity.get().getExpenseAfterSalesItemId());
                }
            }
        });
        return buyOrderExpenseItemDtoList;
    }

    @Override
    public List<ExpenseItemAndInvoiceDto> getExpenseItemAndInvoiceForAfterSale(Long expenseAfterSalesId, Integer buyorderExpenseId) {
        // 查出所有的可退票信息集合
        List<ExpenseItemAndInvoiceDto> expenseItemAndInvoiceList = buyorderExpenseItemMapper.getExpenseItemAndInvoiceList(buyorderExpenseId);

        // 减去已完结的费用售后退票 （红票）
        // 先查出该费用单关联的所有已完结的费用售后单id集合
        List<Long> afterSaleIdList = expenseAfterSalesMapper.getAllCompletedAfterSales(buyorderExpenseId);
        if (afterSaleIdList.size() > 0) {
            expenseItemAndInvoiceList.forEach(item -> {
                List<Integer> invoiceIdList = expenseAfterSalesInvoiceMapper.getAlreadyReturnInvoice(afterSaleIdList, item.getInvoiceNo(), item.getInvoiceCode());
                if (invoiceIdList.size() > 0) {
                    BigDecimal redInvoiceNum = invoiceApiService.getAlreadyRedInvoiceNum(invoiceIdList, item.getBuyorderExpenseItemId());
                    item.setInvoiceNum(item.getInvoiceNum().subtract(redInvoiceNum));
                }
            });
        }

        // 这边要过滤掉InvoiceNum小于等于0的情况
        List<ExpenseItemAndInvoiceDto> newExpenseItemList = expenseItemAndInvoiceList.stream().filter(item -> item.getInvoiceNum().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());

        // 编辑时，查询 退票数量和 EXPENSE_AFTER_SALES_INVOICE_ID
        if (expenseAfterSalesId != null) {
            newExpenseItemList.forEach(item -> {
                ExpenseAfterSalesInvoiceEntity query = new ExpenseAfterSalesInvoiceEntity();
                query.setExpenseAfterSalesId(expenseAfterSalesId);
                query.setBuyorderExpenseItemId(item.getBuyorderExpenseItemId());
                query.setInvoiceNo(item.getInvoiceNo());
                query.setInvoiceCode(item.getInvoiceCode());
                Optional<ExpenseAfterSalesInvoiceEntity> invoiceEntity = Optional.ofNullable(expenseAfterSalesInvoiceMapper.queryReturnInvoiceInfo(query));
                if (invoiceEntity.isPresent()) {
                    item.setReturnNum(invoiceEntity.get().getReturnNum());
                    item.setExpenseAfterSalesInvoiceId(invoiceEntity.get().getExpenseAfterSalesInvoiceId());
                }
            });
        }
        return newExpenseItemList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveAddExpenseAfterSale(ExpenseAfterSalesDto expenseAfterSalesDto) {

        log.info("saveAddExpenseAfterSale 入参:{}", JSON.toJSONString(expenseAfterSalesDto));
        // 先校验费用单的状态
        BuyorderExpenseEntity buyorderExpenseEntity = buyorderExpenseMapper.selectByPrimaryKey(expenseAfterSalesDto.getBuyorderExpenseId());
        if (!(buyorderExpenseEntity.getValidStatus() == 1 && buyorderExpenseEntity.getStatus() != 3 && buyorderExpenseEntity.getLockedStatus() == 0)) {
            throw new ServiceException("当前该采购费用订单状态不支持创建售后订单！");
        }

        ExpenseAfterSalesEntity expenseAfterSalesEntity = expenseAfterSalesConverter.toEntity(expenseAfterSalesDto);

        // 售后单号规则区分订单类型
        BillGeneratorBean billGeneratorBean = new BillGeneratorBean(expenseAfterSalesDto.getExpenseAfterSalesType() == 4121 ?
                BillType.BUY_ORDER_EXPENSE_AFTER_SALES : BillType.BUY_ORDER_EXPENSE_AFTER_SALES_REFUND_INVOICE);
        String expenseAfterSalesNo = new BillNumGenerator().distribution(billGeneratorBean);
        expenseAfterSalesEntity.setExpenseAfterSalesNo(expenseAfterSalesNo);

        expenseAfterSalesMapper.insertSelective(expenseAfterSalesEntity);
        long expenseAfterSalesId = expenseAfterSalesEntity.getExpenseAfterSalesId();

        // 保存售后单状态表（先给默认值，后续的退款和退票状态需要根据售后类型重新计算更新,比如getReturnPaymentData这个方法就需要先查status信息，所以这边先给默认值）

        ExpenseAfterSalesStatusEntity statusEntity = ExpenseAfterSalesStatusEntity.builder()
                .expenseAfterSalesId(expenseAfterSalesId)
                .afterSalesStatus(ExpenseAfterSalesConstant.ZERO)
                .auditStatus(ExpenseAfterSalesConstant.ZERO)
                .validStatus(ExpenseAfterSalesConstant.ZERO)
                .refundStatus(ExpenseAfterSalesConstant.NEGATIVE_ONE)
                .returnInvoiceStatus(ExpenseAfterSalesConstant.ZERO)
                .isDelete(ExpenseAfterSalesConstant.ZERO)
                .build();
        // 自动创建的售后单值为审核通过 生效
        if (Objects.nonNull(expenseAfterSalesDto.getIsAuto())  && expenseAfterSalesDto.getIsAuto().equals(1)) {
            statusEntity.setAfterSalesStatus(ExpenseAfterSalesConstant.ONE);
            statusEntity.setValidStatus(ExpenseAfterSalesConstant.ONE);
            statusEntity.setValidTime(new Date());
            statusEntity.setAuditStatus(ExpenseAfterSalesConstant.TWO);
        }
        expenseAfterSalesStatusMapper.insertSelective(statusEntity);

        expenseAfterSalesDto.setExpenseAfterSalesId(expenseAfterSalesId);
        // 退货
        if (expenseAfterSalesDto.getExpenseAfterSalesType().equals(ExpenseAfterSalesConstant.EXPENSE_RETURN_GOODS)) {

            // 兼容自动转单 自动创建售后单 逻辑
            if (ExpenseAfterSalesConstant.EXPENSE_AFTER_SALES_REASON_SALES.equals(expenseAfterSalesDto.getExpenseAfterSalesReason())) {
                expenseAfterSalesDto.getExpenseAfterSalesItemDtoList().forEach(item->{
                    item.setExpenseAfterSalesId(expenseAfterSalesId);
                    ExpenseAfterSalesItemEntity expenseAfterSalesItemEntity = expenseAfterSalesItemConverter.toEntity(item);
                    expenseAfterSalesItemMapper.insertSelective(expenseAfterSalesItemEntity);
                    item.setExpenseAfterSalesItemId(expenseAfterSalesItemEntity.getExpenseAfterSalesItemId());
                });
                // 查询是否有 可退票的信息，若有，同时生成退票信息
                handleInvoiceInfo(expenseAfterSalesDto,ExpenseAfterSalesConstant.ONE);

            } else {
                expenseAfterSalesDto.getExpenseItemList().forEach(item -> {
                    ExpenseAfterSalesItemEntity temp = ExpenseAfterSalesItemEntity.builder()
                            .expenseAfterSalesId(expenseAfterSalesId)
                            .buyorderExpenseItemId(item.getBuyorderExpenseItemId())
                            .sku(item.getBuyorderExpenseItemDetailDto().getSku())
                            .returnNum(item.getReturnNum())
                            .build();
                    expenseAfterSalesItemMapper.insertSelective(temp);
                    item.setExpenseAfterSalesItemId(temp.getExpenseAfterSalesItemId());
                });

                // 查询是否有 可退票的信息，若有，同时生成退票信息
                handleInvoiceInfo(expenseAfterSalesDto, ExpenseAfterSalesConstant.ONE);
            }


            // 自动创建的费用单是完结状态所以不需要进行锁单
            if (Objects.isNull(expenseAfterSalesDto.getIsAuto()) || expenseAfterSalesDto.getIsAuto().equals(0)) {
                // 锁定关联的费用单
                BuyorderExpenseEntity lock = new BuyorderExpenseEntity();
                lock.setBuyorderExpenseId(expenseAfterSalesDto.getBuyorderExpenseId());
                lock.setLockedStatus(1);
                buyorderExpenseMapper.updateByPrimaryKeySelective(lock);
                log.info("新增费用售后退货单锁定关联的采购费用单，采购费用单id：{}， 费用售后单id：{}", expenseAfterSalesDto.getBuyorderExpenseId(), expenseAfterSalesId);
            }

        }
        if (expenseAfterSalesDto.getExpenseAfterSalesType().equals(ExpenseAfterSalesConstant.EXPENSE_RETURN_INVOICE)) {
            // 仅退票
            List<ExpenseAfterSalesInvoiceEntity> invoiceList = new ArrayList<>();
            expenseAfterSalesDto.getItemAndInvoiceDtoList().forEach(invoiceItem -> {
                ExpenseAfterSalesInvoiceEntity temp = ExpenseAfterSalesInvoiceEntity.builder()
                        .expenseAfterSalesId(expenseAfterSalesId)
                        .buyorderExpenseItemId(invoiceItem.getBuyorderExpenseItemId())
                        .invoiceNo(invoiceItem.getInvoiceNo())
                        .invoiceCode(invoiceItem.getInvoiceCode())
                        .returnNum(invoiceItem.getReturnNum())
                        .sku(invoiceItem.getSku())
                        .build();
                invoiceList.add(temp);
            });
            expenseAfterSalesInvoiceMapper.batchPartInsertSelective(invoiceList);

            // 仅退票的订单，更新为 无退款/未退票
            ExpenseAfterSalesStatusEntity updateStatus = new ExpenseAfterSalesStatusEntity();
            updateStatus.setExpenseAfterSalesStatusId(statusEntity.getExpenseAfterSalesStatusId());
            updateStatus.setReturnInvoiceStatus(1);
            updateStatus.setRefundStatus(0);
            expenseAfterSalesStatusMapper.updateByPrimaryKeySelective(updateStatus);
        }

        // 处理附件信息
        if (expenseAfterSalesDto.getAttachmentList() != null && expenseAfterSalesDto.getAttachmentList().size() > 0) {
            expenseAfterSalesDto.getAttachmentList().forEach(item -> {
                item.setRelatedId((int) expenseAfterSalesId);
                attachmentMapper.insertSelective(item);
            });
        }

        // 非采销联动的(即供应链手动创建的退货)要进行关系分摊 逻辑优先分摊有售后预警的
        if (expenseAfterSalesDto.getExpenseAfterSalesType().equals(ExpenseAfterSalesConstant.EXPENSE_RETURN_GOODS)&&!expenseAfterSalesDto.getExpenseAfterSalesReason().equals(4284)) {
            bindSalesOrderRelation(expenseAfterSalesDto);
        }

        if (expenseAfterSalesDto.getExpenseAfterSalesType().equals(ExpenseAfterSalesConstant.EXPENSE_RETURN_GOODS)) {
            // 绑定 售后明细 和原始订单的关系
            handleExpenseAfterSalesAndSalesRelation(expenseAfterSalesDto);
        }

        return expenseAfterSalesId;
    }


    /**
     * 兼容原始创建售后单时候对象使用的是 BuyorderExpenseItemDto 而不是 ExpenseAfterSalesItemDto
     * 绑定 售后明细 和原始订单的关系
     * @param expenseAfterSalesDto 元数据
     */
    private void bindSalesOrderRelation(ExpenseAfterSalesDto expenseAfterSalesDto) {

        log.info("非采销联动，手动创建费用售后单绑定原始销售单关系");

        List<Integer> buyorderExpenseItemIds = expenseAfterSalesDto.getExpenseItemList()
                .stream().map(BuyorderExpenseItemDto::getBuyorderExpenseItemId).collect(Collectors.toList());

        if (CollUtil.isEmpty(buyorderExpenseItemIds)) {
            log.error("费用单明细id为空");
            return;
        }

        List<RBuyorderExpenseJSaleorderDto> rBuyorderExpenseJSaleorderEntities = rBuyorderExpenseJSaleorderMapper.selectByBuyorderExpenseItemId(buyorderExpenseItemIds);

        if (CollUtil.isEmpty(rBuyorderExpenseJSaleorderEntities)) {
            return;
        }
        List<RExpenseAfterSalesJSaleorderDto> rExpenseAfterSalesJSaleorderDtos = rExpenseAfterSalesJSaleorderMapper.selectByBuyorderExpenseId(expenseAfterSalesDto.getBuyorderExpenseId());

        rBuyorderExpenseJSaleorderEntities.forEach(c->{
            int sum = rExpenseAfterSalesJSaleorderDtos.stream().filter(x -> Objects.nonNull(x) && x.getSaleorderId().equals(c.getSaleorderId()) && x.getSaleorderGoodsId().equals(c.getSaleorderGoodsId())).mapToInt(RExpenseAfterSalesJSaleorderDto::getAfterSalesNum).sum();
            c.setNum(c.getNum()-sum);
        });
        List<RBuyorderExpenseJSaleorderDto> collectFilter = rBuyorderExpenseJSaleorderEntities.stream().filter(x -> Objects.nonNull(x.getNum()) && x.getNum() > 0).collect(Collectors.toList());
        if (CollUtil.isEmpty(collectFilter)) {
            log.info("费用单：{} 的正向的关联关系已经用尽",JSON.toJSONString(expenseAfterSalesDto.getBuyorderExpenseId()));
            return;
        }
        Map<Integer, List<RBuyorderExpenseJSaleorderDto>> integerListMap = collectFilter.stream()
                .collect(Collectors.groupingBy(RBuyorderExpenseJSaleorderDto::getBuyorderExpenseItemId));

        BuyorderExpenseDto orderMainData = buyorderExpenseApiService.getOrderMainData(expenseAfterSalesDto.getBuyorderExpenseId());
        if (Objects.isNull(orderMainData) || Objects.isNull(orderMainData.getBuyorderExpenseDetailDto())) {
            log.error("未查到费用单数据");
            return;
        }

        List<OrderRemarkDto> orderDesc = CollUtil.isNotEmpty(orderMainData.getBuyorderExpenseDetailDto().getOrderDesc()) ? orderMainData.getBuyorderExpenseDetailDto().getOrderDesc() : new ArrayList<>();
        List<OriginalOrderDto> originalOrderDtos = orderDesc.stream().filter(xx->Objects.nonNull(xx.getType())&&xx.getType().equals(1)).map(OrderRemarkDto::getOriginalOrderDto).collect(Collectors.toList());
        List<ExpenseAfterSalesItemDto> expenseAfterSalesItemDtoList = new ArrayList<>();
        expenseAfterSalesDto.getExpenseItemList().forEach(c -> {

            AtomicInteger atomicInteger = new AtomicInteger(c.getReturnNum());
            List<RBuyorderExpenseJSaleorderDto> relation = integerListMap.get(c.getBuyorderExpenseItemId());
            if (CollUtil.isEmpty(relation)) {
                return;
            }
            // 优先找预警
            Map<Integer,Integer> warnSaleOrderIds = new ConcurrentHashMap<>();
            if (CollUtil.isNotEmpty(originalOrderDtos)) {
                originalOrderDtos.forEach(a -> {
                    List<OriginalOrderGoodsDto> goodsDtos = a.getGoodsDtos();
                    if (CollUtil.isEmpty(goodsDtos)) {
                        return;
                    }
                    Optional<OriginalOrderGoodsDto> any = goodsDtos.stream().filter(b ->
                            Objects.nonNull(b.getGoodsId())&&b.getGoodsId().equals(c.getGoodsId())
                                    &&Objects.nonNull(b.getX())&&b.getX()!=0
                                    &&Objects.nonNull(b.getY())&&b.getY()!=0)
                            .findAny();
                    any.ifPresent(originalOrderGoodsDto -> warnSaleOrderIds.put(a.getOrderId(), originalOrderGoodsDto.getY()));
                });
            }
            Set<Integer> saleOrders = warnSaleOrderIds.keySet();
            List<RBuyorderExpenseJSaleorderDto> collect = relation.stream()
                    .filter(rel -> saleOrders.contains(rel.getSaleorderId()) && c.getGoodsId().equals(rel.getSkuId())).sorted(Comparator.comparing(RBuyorderExpenseJSaleorderDto::getSatisfyDeliveryTime,Comparator.nullsLast(Long::compare)))
                    .collect(Collectors.toList());
            List<RExpenseAfterSalesJSaleorderDto> afterSaleRelation = new ArrayList<>();

            ExpenseAfterSalesItemDto expenseAfterSalesItemDto = new ExpenseAfterSalesItemDto();

            expenseAfterSalesItemDto.setExpenseAfterSalesItemId(c.getExpenseAfterSalesItemId());
            expenseAfterSalesItemDto.setSku(c.getSku());
            expenseAfterSalesItemDto.setReturnNum(c.getReturnNum());
            // 预警分摊
            if (CollUtil.isNotEmpty(collect)) {
                collect.forEach(x -> doShareByWarn(atomicInteger, afterSaleRelation, x,warnSaleOrderIds));
            }
            if (atomicInteger.get() <= 0) {
                if (CollUtil.isNotEmpty(afterSaleRelation)) {
                    expenseAfterSalesItemDto.setRExpenseAfterSalesJSaleorderDtos(afterSaleRelation);
                    expenseAfterSalesItemDtoList.add(expenseAfterSalesItemDto);
                }
                return;
            }

            // 剩余分摊
            List<RBuyorderExpenseJSaleorderDto> other = relation.stream()
                    .sorted(Comparator.comparing(RBuyorderExpenseJSaleorderDto::getSatisfyDeliveryTime,Comparator.nullsLast(Long::compare)))
                    .collect(Collectors.toList());

            other.forEach(x -> doShare(atomicInteger, afterSaleRelation, x));

            if (CollUtil.isNotEmpty(afterSaleRelation)) {
                Map<Integer, List<RExpenseAfterSalesJSaleorderDto>> merge = afterSaleRelation.stream().collect(Collectors.groupingBy(RExpenseAfterSalesJSaleorderDto::getSaleorderGoodsId));
                List<RExpenseAfterSalesJSaleorderDto> result = new ArrayList<>();
                merge.forEach((k,v)->{
                    if (CollUtil.isEmpty(v)) {
                        return;
                    }
                    if (v.size() == 1) {
                        result.addAll(v);
                    } else {
                        int sum = v.stream().mapToInt(RExpenseAfterSalesJSaleorderDto::getAfterSalesNum).sum();
                        RExpenseAfterSalesJSaleorderDto rExpenseAfterSalesJSaleorderDto = v.get(0);
                        rExpenseAfterSalesJSaleorderDto.setAfterSalesNum(sum);
                        result.add(rExpenseAfterSalesJSaleorderDto);
                    }
                });
                expenseAfterSalesItemDto.setRExpenseAfterSalesJSaleorderDtos(result);
            }

            expenseAfterSalesItemDtoList.add(expenseAfterSalesItemDto);

        });

        expenseAfterSalesDto.setExpenseAfterSalesItemDtoList(expenseAfterSalesItemDtoList);
    }

    /**
     *
     * @param atomicInteger 原始数量
     * @param afterSaleRelation 组装的数据
     * @param x 当前正向关联关系
     * @param warnSaleOrderIds 预警数据
     */
    private void doShareByWarn(AtomicInteger atomicInteger, List<RExpenseAfterSalesJSaleorderDto> afterSaleRelation, RBuyorderExpenseJSaleorderDto x, Map<Integer, Integer> warnSaleOrderIds) {

        log.info("doShareByWarn 入参：atomicInteger:{},x:{},warn:{}", JSON.toJSONString(atomicInteger),  JSON.toJSONString(x),JSON.toJSONString(warnSaleOrderIds));

        if (atomicInteger.get() <= 0||x.getNum()<=0) {
            return;
        }

        Integer integer = warnSaleOrderIds.get(x.getSaleorderId());
        if (integer <= 0) {
            return;
        }
        RExpenseAfterSalesJSaleorderDto build = RExpenseAfterSalesJSaleorderDto.builder()
                .saleorderId(x.getSaleorderId()).saleorderGoodsId(x.getSaleorderGoodsId())
                .skuId(x.getSkuId()).skuNo(x.getSkuNo()).build();

        if (integer >= atomicInteger.get()) {
            build.setAfterSalesNum(atomicInteger.get());
            x.setNum(x.getNum()-atomicInteger.get());
            warnSaleOrderIds.put(x.getSaleorderId(), integer - atomicInteger.get());
            atomicInteger.addAndGet(atomicInteger.get() * -1);

        } else {
            build.setAfterSalesNum(integer);
            warnSaleOrderIds.put(x.getSaleorderId(), 0);
            x.setNum(x.getNum()-integer);
            atomicInteger.addAndGet(integer * -1);
        }

        log.info("doShareByWarn 此次新增的关系:{}", JSON.toJSONString(build));

        afterSaleRelation.add(build);
    }

    /**
     * 分摊
     * @param atomicInteger 原始数量
     * @param afterSaleRelation 组装的数据
     * @param x 当前正向关联关系
     */
    public static void doShare(AtomicInteger atomicInteger,  List<RExpenseAfterSalesJSaleorderDto> afterSaleRelation, RBuyorderExpenseJSaleorderDto x) {

        log.info("doShare 入参：atomicInteger:{},haveShare:{}", JSON.toJSONString(atomicInteger),  JSON.toJSONString(x));

        if (atomicInteger.get() <= 0||x.getNum()<=0) {
            return;
        }

        RExpenseAfterSalesJSaleorderDto build = RExpenseAfterSalesJSaleorderDto.builder()
                .saleorderId(x.getSaleorderId()).saleorderGoodsId(x.getSaleorderGoodsId())
                .skuId(x.getSkuId()).skuNo(x.getSkuNo()).build();

        if (x.getNum() >= atomicInteger.get()) {
            build.setAfterSalesNum(atomicInteger.get());
            atomicInteger.addAndGet(atomicInteger.get() * -1);
        } else {
            build.setAfterSalesNum(x.getNum());
            atomicInteger.addAndGet(x.getNum() * -1);
        }

        log.info("doShare 此次新增的关系:{}",JSON.toJSONString(build));

        afterSaleRelation.add(build);
    }


    @Transactional(rollbackFor = Throwable.class)
    public void handleExpenseAfterSalesAndSalesRelation(ExpenseAfterSalesDto expenseAfterSalesDto) {
        log.info("handleExpenseAfterSalesAndSalesRelation 入参:{}", JSON.toJSONString(expenseAfterSalesDto));
        // 先删除所有的售后关系
        rExpenseAfterSalesJSaleorderMapper.deleteByExpenseAfterSalesId(expenseAfterSalesDto.getExpenseAfterSalesId());
        if (CollUtil.isEmpty(expenseAfterSalesDto.getExpenseAfterSalesItemDtoList())) {
            return;
        }
        expenseAfterSalesDto.getExpenseAfterSalesItemDtoList().forEach(c -> {
            List<RExpenseAfterSalesJSaleorderDto> rExpenseAfterSalesJSaleorderDtos = c.getRExpenseAfterSalesJSaleorderDtos();
            if (CollUtil.isNotEmpty(rExpenseAfterSalesJSaleorderDtos)) {
                rExpenseAfterSalesJSaleorderDtos.forEach(a -> {
                    a.setExpenseAfterSalesId(expenseAfterSalesDto.getExpenseAfterSalesId());
                    a.setExpenseAfterSalesItemId(c.getExpenseAfterSalesItemId());
                });
            }
        });
        List<RExpenseAfterSalesJSaleorderDto> collect = expenseAfterSalesDto.getExpenseAfterSalesItemDtoList().stream()
                .map(ExpenseAfterSalesItemDto::getRExpenseAfterSalesJSaleorderDtos)
                .filter(CollUtil::isNotEmpty)
                .flatMap(List::stream)
                .collect(Collectors.toList());
        List<RExpenseAfterSalesJSaleorderEntity> rExpenseAfterSalesJSaleorderEntities = rExpenseAfterSalesJSaleorderConvert.toEntity(collect);
        log.info("费用售后和销售单的关系：{}", JSON.toJSONString(rExpenseAfterSalesJSaleorderEntities));
        rExpenseAfterSalesJSaleorderEntities.forEach(c->{
            rExpenseAfterSalesJSaleorderMapper.insertSelective(c);
        });

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveEditExpenseAfterSale(ExpenseAfterSalesDto expenseAfterSalesDto) {
        // 先校验售后单的状态
        ExpenseAfterSalesStatusEntity statusEntity = expenseAfterSalesStatusMapper.selectByExpenseAfterSalesId(expenseAfterSalesDto.getExpenseAfterSalesId());
        if (!(statusEntity.getAfterSalesStatus() == 0 && statusEntity.getAuditStatus() != 1)) {
            throw new ServiceException("当前售后单状态不支持编辑，请返回详情页刷新后重试！");
        }

        expenseAfterSalesMapper.updateByPrimaryKeySelective(expenseAfterSalesConverter.toEntity(expenseAfterSalesDto));
        // 退货退票
        if (expenseAfterSalesDto.getExpenseAfterSalesType().equals(ExpenseAfterSalesConstant.EXPENSE_RETURN_GOODS)) {
            // step1: 先查出已有的EXPENSE_AFTER_SALES_ITEM id集合
            List<Long> existingIdList = expenseAfterSalesItemMapper.getExpenseAfterSalesItemListByAfterSalesId(expenseAfterSalesDto.getExpenseAfterSalesId())
                    .stream().map(ExpenseAfterSalesItemDto::getExpenseAfterSalesItemId).collect(Collectors.toList());

            // step2：与本次编辑时传入的item比较，多删少增
            expenseAfterSalesDto.getExpenseItemList().forEach(item -> {
                if (existingIdList.contains(item.getExpenseAfterSalesItemId())) {
                    // 已存在的，则更新退货数量（页面上只可以修改这个字段）
                    ExpenseAfterSalesItemEntity updateEntity = ExpenseAfterSalesItemEntity.builder()
                            .expenseAfterSalesItemId(item.getExpenseAfterSalesItemId())
                            .returnNum(item.getReturnNum())
                            .updateRemark("编辑费用售后单触发更新费用售后商品信息")
                            .build();
                    expenseAfterSalesItemMapper.updateByPrimaryKeySelective(updateEntity);
                } else {
                    // 不存在的，直接新增
                    ExpenseAfterSalesItemEntity insertEntity = ExpenseAfterSalesItemEntity.builder()
                            .expenseAfterSalesId(expenseAfterSalesDto.getExpenseAfterSalesId())
                            .buyorderExpenseItemId(item.getBuyorderExpenseItemId())
                            .sku(item.getBuyorderExpenseItemDetailDto().getSku())
                            .returnNum(item.getReturnNum())
                            .build();
                    expenseAfterSalesItemMapper.insertSelective(insertEntity);
                    item.setExpenseAfterSalesItemId(insertEntity.getExpenseAfterSalesItemId());
                }
            });
            // 删除
            List<Long> newIdList = expenseAfterSalesDto.getExpenseItemList().stream().map(BuyorderExpenseItemDto::getExpenseAfterSalesItemId).collect(Collectors.toList());
            existingIdList.forEach(itemId -> {
                if (!newIdList.contains(itemId)) {
                    ExpenseAfterSalesItemEntity deleteEntity = ExpenseAfterSalesItemEntity.builder()
                            .expenseAfterSalesItemId(itemId)
                            .isDelete(ExpenseAfterSalesConstant.ONE)
                            .updateRemark("编辑费用退货售后单触发删除费用售后商品")
                            .build();
                    expenseAfterSalesItemMapper.updateByPrimaryKeySelective(deleteEntity);
                }
            });

            // step3：计算并处理退票信息
            handleInvoiceInfo(expenseAfterSalesDto, ExpenseAfterSalesConstant.TWO);

            // 非采销联动的(即供应链手动创建的)要进行关系分摊 逻辑优先分摊有售后预警的
            if (!expenseAfterSalesDto.getExpenseAfterSalesReason().equals(ExpenseAfterSalesConstant.EXPENSE_AFTER_SALES_REASON_SALES)) {
                bindSalesOrderRelation(expenseAfterSalesDto);
                // 绑定 售后明细 和原始订单的关系
                handleExpenseAfterSalesAndSalesRelation(expenseAfterSalesDto);
            }
        }

        // 仅退票
        if (expenseAfterSalesDto.getExpenseAfterSalesType().equals(ExpenseAfterSalesConstant.EXPENSE_RETURN_INVOICE)) {
            // 先查出 T_EXPENSE_AFTER_SALES_INVOICE 的信息
            List<Long> existingList = expenseAfterSalesInvoiceMapper.findByExpenseAfterSalesId(expenseAfterSalesDto.getExpenseAfterSalesId()).stream().map(ExpenseAfterSalesInvoiceEntity::getExpenseAfterSalesInvoiceId).collect(Collectors.toList());
            expenseAfterSalesDto.getItemAndInvoiceDtoList().forEach(item -> {
                if (existingList.contains(item.getExpenseAfterSalesInvoiceId())) {
                    // 已存在，更新信息
                    ExpenseAfterSalesInvoiceEntity update = ExpenseAfterSalesInvoiceEntity.builder()
                            .expenseAfterSalesInvoiceId(item.getExpenseAfterSalesInvoiceId())
                            .returnNum(item.getReturnNum())
                            .updateRemark("编辑费用退票售后单触发更新退票信息")
                            .build();
                    expenseAfterSalesInvoiceMapper.updateByPrimaryKeySelective(update);
                } else {
                    // 新增
                    ExpenseAfterSalesInvoiceEntity insert = ExpenseAfterSalesInvoiceEntity.builder()
                            .expenseAfterSalesId(expenseAfterSalesDto.getExpenseAfterSalesId())
                            .buyorderExpenseItemId(item.getBuyorderExpenseItemId())
                            .invoiceNo(item.getInvoiceNo())
                            .invoiceCode(item.getInvoiceCode())
                            .sku(item.getSku())
                            .returnNum(item.getReturnNum())
                            .build();
                    expenseAfterSalesInvoiceMapper.insertSelective(insert);
                }
            });

            // 删除
            List<Long> newIdList = expenseAfterSalesDto.getItemAndInvoiceDtoList().stream().map(ExpenseItemAndInvoiceDto::getExpenseAfterSalesInvoiceId).collect(Collectors.toList());
            existingList.forEach(exist -> {
                if (!newIdList.contains(exist)) {
                    ExpenseAfterSalesInvoiceEntity delete = ExpenseAfterSalesInvoiceEntity.builder()
                            .expenseAfterSalesInvoiceId(exist)
                            .isDelete(ExpenseAfterSalesConstant.ONE)
                            .updateRemark("编辑费用退票售后单触发删除退票信息")
                            .build();
                    expenseAfterSalesInvoiceMapper.updateByPrimaryKeySelective(delete);
                }
            });
        }

        // 编辑时 将该订单已有的附件和本次编辑时传入的附件筛选
        List<Integer> idList = expenseAfterSalesDto.getAttachmentList().stream().filter(item -> item.getAttachmentId() != null).map(Attachment::getAttachmentId).collect(Collectors.toList());
        attachmentMapper.queryListByRelatedIdAndFunction(expenseAfterSalesDto.getExpenseAfterSalesId().intValue(), 4134).forEach(attachment -> {
            // 现有的附件id不在本次编辑时的附件内，就证明删除
            if (!idList.contains(attachment.getAttachmentId())) {
                attachment.setIsDeleted(1);
                attachmentMapper.updateByPrimaryKeySelective(attachment);
                log.info("编辑费用售后单时删除附件信息，附件id：{}", attachment.getAttachmentId());
            }
        });

        expenseAfterSalesDto.getAttachmentList().forEach(item -> {
            // 本次新增的，插入
            if (item.getAttachmentId() == null) {
                item.setRelatedId(expenseAfterSalesDto.getExpenseAfterSalesId().intValue());
                attachmentMapper.insertSelective(item);
            }
        });
        return expenseAfterSalesDto.getExpenseAfterSalesId();
    }

    /**
     * 退货退票类型，校验并新增 费用单售后退票信息
     * 兼容自动创建售后单和自动转单售后单的创建
     * @param expenseAfterSalesDto 费用售后单信息
     * @param type                 新增1，编辑2
     */
    @Transactional(rollbackFor = Throwable.class)
    public void handleInvoiceInfo(ExpenseAfterSalesDto expenseAfterSalesDto, Integer type) {
        ExpenseAfterSalesStatusEntity updateStatus = expenseAfterSalesStatusMapper.selectByExpenseAfterSalesId(expenseAfterSalesDto.getExpenseAfterSalesId());

        // 编辑时，先全部删除该售后单的 退票信息
        if (type.equals(ExpenseAfterSalesConstant.TWO)) {
            expenseAfterSalesInvoiceMapper.deleteInvoiceByExpenseAfterSalesId(ExpenseAfterSalesInvoiceEntity.builder().expenseAfterSalesId(expenseAfterSalesDto.getExpenseAfterSalesId()).build());
            log.info("编辑费用售后单时逻辑删除上一次的退票信息，售后单id：{}", expenseAfterSalesDto.getExpenseAfterSalesId());
        }

        List<ExpenseItemAndInvoiceDto> invoiceDtoList = getExpenseItemAndInvoiceForAfterSale(type == 1 ? null : expenseAfterSalesDto.getExpenseAfterSalesId(), expenseAfterSalesDto.getBuyorderExpenseId());
        // 只有该费用单存在可退票信息，才去判断每个商品的退票信息
        if (invoiceDtoList.size() > 0) {
            List<ExpenseAfterSalesInvoiceEntity> insertEntity = new ArrayList<>();
            // 编辑或则 新增的时候不是采销联动的
            if (type == 2||(type==1&&!ExpenseAfterSalesConstant.EXPENSE_AFTER_SALES_REASON_SALES.equals(expenseAfterSalesDto.getExpenseAfterSalesReason()))) {
                // 同一个商品对应多个发票号时，需要分摊并插入多条记录
                expenseAfterSalesDto.getExpenseItemList().forEach(item -> {
                    // 查出当前商品对应的所有invoiceNo
                    List<ExpenseItemAndInvoiceDto> matchInvoiceList = invoiceDtoList.stream().filter(invoice -> item.getBuyorderExpenseItemId().equals(invoice.getBuyorderExpenseItemId())).collect(Collectors.toList());

                    // 只有该商品有录票信息才会产生退票
                    if (matchInvoiceList.size() > 0) {
                        // 实际需要退票的数量
                        BigDecimal totalReturnNum = new BigDecimal(item.getReturnNum());
                        for (ExpenseItemAndInvoiceDto dto : matchInvoiceList) {
                            if (totalReturnNum.compareTo(BigDecimal.ZERO) > 0) {
                                ExpenseAfterSalesInvoiceEntity temp = ExpenseAfterSalesInvoiceEntity.builder()
                                        .expenseAfterSalesId(expenseAfterSalesDto.getExpenseAfterSalesId())
                                        .buyorderExpenseItemId(dto.getBuyorderExpenseItemId())
                                        .invoiceCode(dto.getInvoiceCode())
                                        .invoiceNo(dto.getInvoiceNo())
                                        .sku(dto.getSku())
                                        .returnNum(dto.getInvoiceNum().compareTo(totalReturnNum) >= 0 ? totalReturnNum : dto.getInvoiceNum())
                                        .build();
                                insertEntity.add(temp);
                                totalReturnNum = totalReturnNum.subtract(temp.getReturnNum());
                            } else {
                                break;
                            }
                        }
                    }
                });
            } else   {

                // 同一个商品对应多个发票号时，需要分摊并插入多条记录
                expenseAfterSalesDto.getExpenseAfterSalesItemDtoList().forEach(item -> {
                    // 查出当前商品对应的所有invoiceNo
                    List<ExpenseItemAndInvoiceDto> matchInvoiceList = invoiceDtoList.stream().filter(invoice -> item.getBuyorderExpenseItemId().equals(invoice.getBuyorderExpenseItemId())).collect(Collectors.toList());

                    // 只有该商品有录票信息才会产生退票
                    if (matchInvoiceList.size() > 0) {
                        // 实际需要退票的数量
                        BigDecimal totalReturnNum = new BigDecimal(item.getReturnNum());
                        for (ExpenseItemAndInvoiceDto dto : matchInvoiceList) {
                            if (totalReturnNum.compareTo(BigDecimal.ZERO) > 0) {
                                ExpenseAfterSalesInvoiceEntity temp = ExpenseAfterSalesInvoiceEntity.builder()
                                        .expenseAfterSalesId(expenseAfterSalesDto.getExpenseAfterSalesId())
                                        .buyorderExpenseItemId(dto.getBuyorderExpenseItemId())
                                        .invoiceCode(dto.getInvoiceCode())
                                        .invoiceNo(dto.getInvoiceNo())
                                        .sku(dto.getSku())
                                        .returnNum(dto.getInvoiceNum().compareTo(totalReturnNum) >= 0 ? totalReturnNum : dto.getInvoiceNum())
                                        .build();
                                insertEntity.add(temp);
                                totalReturnNum = totalReturnNum.subtract(temp.getReturnNum());
                            } else {
                                break;
                            }
                        }
                    }
                });


            }

            if (insertEntity.size() > 0) {
                // 有要存入的退票信息，证明是 未退票
                updateStatus.setReturnInvoiceStatus(1);
                expenseAfterSalesInvoiceMapper.batchPartInsertSelective(insertEntity);
            }
        }

        // 这个方法新增编辑都需要调用，因此这边限制只有在新增时才会计算并更新退票
        if (type == 1) {
            expenseAfterSalesStatusMapper.updateByPrimaryKeySelective(updateStatus);
        }
    }

    @Override
    public ExpenseAfterSalesDto getExpenseAfterSalesDetail(Long expenseAfterSalesId) {
        ExpenseAfterSalesDto expenseAfterSalesDetail = expenseAfterSalesMapper.getExpenseAfterSalesDetail(expenseAfterSalesId);
        BuyorderExpenseDto buyOrderExpense = buyorderExpenseMapper.getBuyOrderExpenseInfoById(expenseAfterSalesDetail.getBuyorderExpenseId());

        TraderBussinessData traderBussinessData = traderDataService.getTraderBussinessData(expenseAfterSalesDetail.getTraderId(), 2);
        if (traderBussinessData != null) {
            buyOrderExpense.getBuyorderExpenseDetailDto().setOrderCount(traderBussinessData.getOrderTimes());
            buyOrderExpense.getBuyorderExpenseDetailDto().setOrderTotalAmount(traderBussinessData.getOrderTotalAmount());
            buyOrderExpense.getBuyorderExpenseDetailDto().setLastOrderTime(traderBussinessData.getLastOrderTime());
        }
        expenseAfterSalesDetail.setBuyorderExpenseDto(buyOrderExpense);

        if (expenseAfterSalesDetail.getExpenseAfterSalesType().equals(ExpenseAfterSalesConstant.EXPENSE_RETURN_GOODS)) {
            expenseAfterSalesDetail.setExpenseAfterSalesItemDtoList(expenseAfterSalesItemMapper.getExpenseAfterSalesItemListByAfterSalesId(expenseAfterSalesId));
        }

        if (expenseAfterSalesDetail.getExpenseAfterSalesType().equals(ExpenseAfterSalesConstant.EXPENSE_RETURN_INVOICE)) {
            expenseAfterSalesDetail.setReturnInvoiceGoodsDtoList(expenseAfterSalesInvoiceMapper.getReturnInvoiceGoodsInfoList(expenseAfterSalesId));
        }
        expenseAfterSalesDetail.setAttachmentList(attachmentMapper.queryListByRelatedIdAndFunction(expenseAfterSalesId.intValue(), 4134));
        return expenseAfterSalesDetail;
    }

    @Override
    public List<ExpenseAfterSalesDto> getExpenseAfterSalesBybuyorderExpenseId(Integer buyorderExpenseId) {
        Assert.notNull(buyorderExpenseId, "采购费用单id不可为空");
        return expenseAfterSalesMapper.selectByExpenseId(buyorderExpenseId);
    }

    /**
     * 预退金额 = 现金支付 + 账期支付 - （现金退款 +账期退款） + 本次退款 -（订单总额-已售后）
     *
     * @param executeRefundDto
     * @return
     */
    @Override
    public Boolean executeRefundOperation(ExecuteRefundDto executeRefundDto) {
        ExpenseAfterSalesEntity expenseAfterSalesEntity = expenseAfterSalesMapper.selectByPrimaryKey(executeRefundDto.getExpenseAfterSalesId());
        BuyorderExpenseDetailEntity buyorderExpenseDetailEntity = buyorderExpenseDetailMapper.selectByBuyorderExpenseId(executeRefundDto.getBuyorderExpenseId());
        String traderName = Optional.ofNullable(buyorderExpenseDetailEntity).map(BuyorderExpenseDetailEntity::getTraderName).orElseThrow(() -> new ServiceException("交易者名称不能为空"));
        ExpenseAfterSalesStatusEntity expenseAfterSalesStatusEntity = expenseAfterSalesStatusMapper.selectByExpenseAfterSalesId(executeRefundDto.getExpenseAfterSalesId());
        checkRefund(expenseAfterSalesStatusEntity);
        // 支付金额 = 现金支付 + 账期支付
        BigDecimal expensePayMoney = capitalBillApiService.getExpensePayMoney(executeRefundDto.getBuyorderExpenseId());
        // 退款金额 = 现金退款 + 账期退款
        BigDecimal expenseRefundMoney = getRefundMoney(executeRefundDto.getBuyorderExpenseId());
        // 本次退款
        BigDecimal totalAmount = executeRefundDto.getTotalAmount();
        // 订单除售后金额
        BigDecimal expenseMoneyWithoutAfterSales = getExpenseMoneyWithoutAfterSales(executeRefundDto.getBuyorderExpenseId());
        // 账期金额
        BigDecimal expenseDebtPeriodMoney = capitalBillApiService.getExpenseDebtPeriodMoney(executeRefundDto.getBuyorderExpenseId());
        // 预退金额
        BigDecimal refundReadyMoney = expensePayMoney.subtract(expenseRefundMoney).add(totalAmount).subtract(expenseMoneyWithoutAfterSales);
        log.info("{}支付金额：{}，退款金额：{}，本次退款：{}，订单除售后金额：{}，账期金额：{}，预退金额：{}", expenseAfterSalesEntity.getExpenseAfterSalesNo(), expensePayMoney, expenseRefundMoney, totalAmount, expenseMoneyWithoutAfterSales, expenseDebtPeriodMoney, refundReadyMoney);
        // 执行退款
        ErpSpringBeanUtil.getBean(ExpenseAfterSalesServiceImpl.class).execute(expenseDebtPeriodMoney, refundReadyMoney, traderName, expenseAfterSalesEntity);
        // 尝试自动关闭
        try {
            this.completeExpenseAfterSales(expenseAfterSalesEntity.getExpenseAfterSalesId());
        } catch (Exception e) {
            log.error("{}尝试自动关闭失败：原因{},{}", expenseAfterSalesEntity.getExpenseAfterSalesNo(), expenseAfterSalesEntity.getExpenseAfterSalesId(), e.getMessage());
        }

        return Boolean.TRUE;
    }

    /**
     * 获取费用订单除售后总金额
     *
     * @param buyOrderExpenseId
     * @return
     */
    private BigDecimal getExpenseMoneyWithoutAfterSales(Integer buyOrderExpenseId) {
        BuyorderExpenseDetailEntity buyorderExpenseDetailEntity = buyorderExpenseDetailMapper.selectByBuyorderExpenseId(buyOrderExpenseId);
        List<ExpenseAfterSalesDto> expenseAfterSalesDtos = expenseAfterSalesMapper.selectByExpenseId(buyOrderExpenseId);
        // 费用售后已完结总金额
        BigDecimal afterSalesTotalMoney = expenseAfterSalesDtos.stream().filter(e -> ExpenseAfterSalesStatusEnum.COMPLETED.getCode().equals(e.getExpenseAfterSalesStatusDto().getAfterSalesStatus())).map(ExpenseAfterSalesDto::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 费用单金额
        BigDecimal totalAmount = buyorderExpenseDetailEntity.getTotalAmount();
        return totalAmount.subtract(afterSalesTotalMoney);
    }

    /**
     * 获取所有已完结售后的退款金额
     *
     * @param buyOrderExpenseId
     * @return
     */
    @Override
    public BigDecimal getRefundMoney(Integer buyOrderExpenseId) {
        ExpenseAfterSalesEntity expenseAfterSalesEntity = new ExpenseAfterSalesEntity();
        expenseAfterSalesEntity.setBuyorderExpenseId(buyOrderExpenseId);

        List<ExpenseAfterSalesDto> expenseAfterSalesDtos = expenseAfterSalesMapper.selectByExpenseId(buyOrderExpenseId);
        List<Long> expenseAfterSalesIds = expenseAfterSalesDtos.stream()
                .filter(e -> ExpenseAfterSalesStatusEnum.COMPLETED.getCode().equals(e.getExpenseAfterSalesStatusDto().getAfterSalesStatus()))
                .map(ExpenseAfterSalesDto::getExpenseAfterSalesId)
                .collect(Collectors.toList());

        BigDecimal refundMoney = BigDecimal.ZERO;
        for (Long expenseAfterSalesId : expenseAfterSalesIds) {
            BigDecimal expenseRefundMoney = getExpenseRealRefundMoney(expenseAfterSalesId);
            refundMoney = refundMoney.add(expenseRefundMoney);
        }
        return refundMoney;
    }

    /**
     * 采销联动退货采购费用订单，退货预警
     *
     * @param afterSalesId
     */
    @Override
    public List<BuyorderExpenseDto> expenseReturnAndWarning(Integer afterSalesId) {

        //查询对应采购费用订单针对该销售订单当前订货号实际采购数量大于0
        List<RBuyorderExpenseJSaleorderEntity> expenses = rBuyorderExpenseJSaleorderMapper.getBuyorderExpenseByAfterSalesId(afterSalesId);
        List<RBuyorderExpenseJSaleorderEntity> expenseList =new ArrayList<>();
        for (RBuyorderExpenseJSaleorderEntity entity : expenses) {
            RBuyorderExpenseJSaleorderEntity totalNumBySaleorderGoodsIdAndBuyorderExpenseId = rBuyorderExpenseJSaleorderMapper.getTotalNumBySaleorderGoodsIdAndBuyorderExpenseId(entity.getSaleorderGoodsId(), entity.getBuyorderExpenseId());
            RBuyorderExpenseJSaleorderEntity returnNumByBuyorderExpenseIdAndSaleorderGoodsId = rBuyorderExpenseJSaleorderMapper.getReturnNumByBuyorderExpenseIdAndSaleorderGoodsId(entity.getSaleorderGoodsId(), entity.getBuyorderExpenseId());
            Integer totalNum = totalNumBySaleorderGoodsIdAndBuyorderExpenseId == null ? 0 : totalNumBySaleorderGoodsIdAndBuyorderExpenseId.getNum();
            Integer returnNum = returnNumByBuyorderExpenseIdAndSaleorderGoodsId == null ? 0 : returnNumByBuyorderExpenseIdAndSaleorderGoodsId.getNum();
            Integer realNum = totalNum - returnNum;
            if(realNum.compareTo(0) > 0){
                expenseList.add(entity);
            }
        }
        log.info("退货预警采购数量大于0的采购费用订单afterSalesId：{} expenseList：{}",afterSalesId,JSON.toJSONString(expenseList));
        //查询当前退货的销售订单，当前的销售订单的产品归属人在审核阶段选择虚拟商品不可退。
        List<String> cannotReturnSkuList = afterSalesApiService.getCannotReturnSkuByAfterSalesId(afterSalesId);
        log.info("退货预警虚拟商品不可退afterSalesId：{} cannotReturnSkuList：{}",afterSalesId,JSON.toJSONString(cannotReturnSkuList));
        //符合触发退货预警条件的list
        List<RBuyorderExpenseJSaleorderEntity> matchList = new ArrayList<>();
        List<BuyorderExpenseDto> buyorderExpenseDtos = new ArrayList<>();
        Set<Integer> expenseIdList=new HashSet<>();
        //在遍历对应的采购费用订单，同时查出所关联的销售订单，大于1时，则当前采购费用订单进行退货预警
        for (RBuyorderExpenseJSaleorderEntity rBuyorderExpenseJSaleorderEntity : expenseList) {
            Integer buyorderExpenseId = rBuyorderExpenseJSaleorderEntity.getBuyorderExpenseId();
            //根据采购费用订单id查询关联销售订单id
            List<Integer> saleorderIdList = rBuyorderExpenseJSaleorderMapper.getSaleorderByBuyorderExpenseId(buyorderExpenseId);
            log.info("退货预警查询关联销售订单afterSalesId：{} saleorderIdList：{}",afterSalesId,JSON.toJSONString(saleorderIdList));
            if (CollectionUtils.isNotEmpty(saleorderIdList) && saleorderIdList.size() > 1) {
                //对该采购费用单进行退货预警
                log.info("场景1：关联多个销售单触发退货预警afterSalesId：{} rBuyorderExpenseJSaleorderEntity：{}",afterSalesId,JSON.toJSONString(rBuyorderExpenseJSaleorderEntity));
                matchList.add(rBuyorderExpenseJSaleorderEntity);
                continue;
            }
            //当前采购费用订单是否满足新增售后条件
            boolean canAfterSales = buyorderExpenseService.checkExpenseCanAfterSales(buyorderExpenseId);
            log.info("退货预警是否满足新增售后条件afterSalesId：{} canAfterSales：{}",afterSalesId,canAfterSales);
            //先判断当前销售订单有N个采购费用订单，过滤掉已关闭的采购费用订单，遍历N个采购费用单判断，是否仅有一个销售订单
            if (CollectionUtils.isNotEmpty(saleorderIdList) && saleorderIdList.size() == 1) {
                BuyorderExpenseEntity buyorderExpenseEntity = buyorderExpenseMapper.selectByPrimaryKey(buyorderExpenseId);
                log.info("退货预警对应采购费用订单状态afterSalesId：{} buyorderExpenseEntity：{}",afterSalesId,JSON.toJSONString(buyorderExpenseEntity));
                if (!ErpConstant.THREE.equals(buyorderExpenseEntity.getStatus()) && !canAfterSales) {
                    //对该采购费用单进行退货预警
                    log.info("场景2：不满足新增售后条件触发退货预警afterSalesId：{} rBuyorderExpenseJSaleorderEntity：{}",afterSalesId,JSON.toJSONString(rBuyorderExpenseJSaleorderEntity));
                    matchList.add(rBuyorderExpenseJSaleorderEntity);
                    continue;
                }
                //查询当前退货的销售订单，当前的销售订单的产品归属人在审核阶段选择虚拟商品不可退。
                //同时查询当前采购费用订单对应的销售单，有且仅有一条时，且当前采购费用订单满足新增售后条件。
                if (CollectionUtils.isNotEmpty(cannotReturnSkuList) && canAfterSales) {
                    //判断其付款状态为未付款或部分付款
                    if (BuyorderExpenseConstant.PAYMENT_STATUS_NO.equals(buyorderExpenseEntity.getPaymentStatus()) ||
                            BuyorderExpenseConstant.PAYMENT_STATUS_PART.equals(buyorderExpenseEntity.getPaymentStatus())) {
                        //对该采购费用单进行退货预警
                        log.info("场景3：不可退且未付款或部分付款触发退货预警afterSalesId：{} rBuyorderExpenseJSaleorderEntity：{}",afterSalesId,JSON.toJSONString(rBuyorderExpenseJSaleorderEntity));
                        matchList.add(rBuyorderExpenseJSaleorderEntity);
                        continue;
                    }

                    List<String> invoiceList = invoiceApiService.getRedValidInvoiceListByExpenseId(buyorderExpenseId);
                    log.info("退货预警红字有效发票afterSalesId：{} invoiceList：{}",afterSalesId,JSON.toJSONString(invoiceList));
                    //对应采购费用订单中发票中有红字有效发票
                    if (CollectionUtils.isNotEmpty(invoiceList)) {
                        //对该采购费用单进行退货预警
                        log.info("场景4：采购费用订单中发票中有红字有效发票触发退货预警afterSalesId：{} rBuyorderExpenseJSaleorderEntity：{}",afterSalesId,JSON.toJSONString(rBuyorderExpenseJSaleorderEntity));
                        matchList.add(rBuyorderExpenseJSaleorderEntity);
                        continue;
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(matchList)) {
            //转化为ExpenseReturnEarlyWarnEntity的list
            Map<String, List<RBuyorderExpenseJSaleorderEntity>> matchMap = matchList.stream().collect(Collectors.groupingBy(o -> o.getSaleorderId() + "_" + o.getSkuId()));
            for (Map.Entry<String, List<RBuyorderExpenseJSaleorderEntity>> entry : matchMap.entrySet()) {
                List<Integer> buyorderExpenseIdList = new ArrayList<>();
                ExpenseReturnEarlyWarnEntity expenseReturnEarlyWarnEntity = new ExpenseReturnEarlyWarnEntity();
                for (RBuyorderExpenseJSaleorderEntity rBuyorderExpenseJSaleorderEntity : entry.getValue()) {
                    expenseReturnEarlyWarnEntity.setAfterSalesOrderId(afterSalesId);
                    expenseReturnEarlyWarnEntity.setSaleorderId(rBuyorderExpenseJSaleorderEntity.getSaleorderId());
                    expenseReturnEarlyWarnEntity.setNum(rBuyorderExpenseJSaleorderEntity.getNum());
                    expenseReturnEarlyWarnEntity.setSaleorderGoodsId(rBuyorderExpenseJSaleorderEntity.getSaleorderGoodsId());
                    expenseReturnEarlyWarnEntity.setSkuId(rBuyorderExpenseJSaleorderEntity.getSkuId());
                    expenseReturnEarlyWarnEntity.setSkuNo(rBuyorderExpenseJSaleorderEntity.getSkuNo());
                    buyorderExpenseIdList.add(rBuyorderExpenseJSaleorderEntity.getBuyorderExpenseId());
                }
                expenseReturnEarlyWarnEntity.setBuyorderExpenseIdList(buyorderExpenseIdList);
                //订单说明数据插入，预警状态修改
                log.info("订单说明数据插入，预警状态修改afterSalesId：{} expenseReturnEarlyWarnEntity:{}", afterSalesId,JSON.toJSONString(expenseReturnEarlyWarnEntity));
                List<Integer> buyorderExpenseIds= buyorderExpenseService.returnAlertBuyOrderExpenseGoodsShare(expenseReturnEarlyWarnEntity);
                if (CollUtil.isNotEmpty(buyorderExpenseIds)){
                    for (Integer buyorderExpenseId : buyorderExpenseIds) {
                        expenseIdList.add(buyorderExpenseId);
                    }
                }
            }
            //站内信对象封装
            for (Integer buyorderExpenseId : expenseIdList) {
                BuyorderExpenseDto buyorderExpenseDto = new BuyorderExpenseDto();
                BuyorderExpenseEntity buyorderExpenseEntity = buyorderExpenseMapper.selectByPrimaryKey(buyorderExpenseId);
                buyorderExpenseDto.setBuyorderExpenseId(buyorderExpenseId);
                buyorderExpenseDto.setCreator(buyorderExpenseEntity.getCreator());
                buyorderExpenseDto.setBuyorderExpenseNo(buyorderExpenseEntity.getBuyorderExpenseNo());
                buyorderExpenseDtos.add(buyorderExpenseDto);
            }
        }
        return buyorderExpenseDtos;
    }

    /**
     * 销售售后单关闭预警解除
     * @param afterSalesId
     */
    @Override
    public void releaseReturnEarlyWarn(Integer afterSalesId) {
        List<RBuyorderExpenseJSaleorderEntity> list = rBuyorderExpenseJSaleorderMapper.getReleaseReturnEarlyWarnByAfterSalesId(afterSalesId);
        for (RBuyorderExpenseJSaleorderEntity rBuyorderExpenseJSaleorderEntity : list) {
            ExpenseReturnEarlyWarnEntity expenseReturnEarlyWarnEntity = new ExpenseReturnEarlyWarnEntity();
            expenseReturnEarlyWarnEntity.setBuyorderExpenseIdList(Arrays.asList(rBuyorderExpenseJSaleorderEntity.getBuyorderExpenseId()));
            expenseReturnEarlyWarnEntity.setSaleorderId(rBuyorderExpenseJSaleorderEntity.getSaleorderId());
            expenseReturnEarlyWarnEntity.setSaleorderGoodsId(rBuyorderExpenseJSaleorderEntity.getSaleorderGoodsId());
            expenseReturnEarlyWarnEntity.setSkuNo(rBuyorderExpenseJSaleorderEntity.getSkuNo());
            expenseReturnEarlyWarnEntity.setSkuId(rBuyorderExpenseJSaleorderEntity.getSkuId());
            expenseReturnEarlyWarnEntity.setAfterSalesOrderId(afterSalesId);
            // 删除预警详情行
            log.info("销售售后关闭，解除预警，expenseReturnEarlyWarnEntity：{}", JSON.toJSONString(expenseReturnEarlyWarnEntity));
            buyorderExpenseService.freePartReturnAlertBuyOrderExpenseGoodsByCompute(expenseReturnEarlyWarnEntity);
        }
    }


    /**
     * 校验
     *
     * @param expenseAfterSalesStatusEntity
     * @return
     */
    public void checkRefund(ExpenseAfterSalesStatusEntity expenseAfterSalesStatusEntity) {
        //退款状态 -1 未执行退款运算 0无退款 1未退款 2部分退款 3全部退款
        if (expenseAfterSalesStatusEntity.getRefundStatus() == 2 || expenseAfterSalesStatusEntity.getRefundStatus() == 3 || expenseAfterSalesStatusEntity.getRefundStatus() == 0) {
            log.info("退款状态校验未通过:{}", expenseAfterSalesStatusEntity.getRefundStatus());
            throw new ServiceException("退款状态校验未通过");
        }
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void execute(BigDecimal debtPeriodMoney, BigDecimal refundReadyMoney, String traderName, ExpenseAfterSalesEntity expenseAfterSalesEntity) {
        // 归还账期
        ErpSpringBeanUtil.getBean(ExpenseAfterSalesServiceImpl.class).periodBack(debtPeriodMoney, refundReadyMoney, traderName, expenseAfterSalesEntity);
        // 退还余额
        ErpSpringBeanUtil.getBean(ExpenseAfterSalesServiceImpl.class).refundAmount(debtPeriodMoney, refundReadyMoney, traderName, expenseAfterSalesEntity);
        // 更新状态
        ErpSpringBeanUtil.getBean(ExpenseAfterSalesServiceImpl.class).updatePayStatus(expenseAfterSalesEntity.getExpenseAfterSalesId());
    }

    public void periodBack(BigDecimal debtPeriodMoney, BigDecimal refundReadyMoney, String traderName, ExpenseAfterSalesEntity expenseAfterSalesEntity) {
        log.info("{}step1:归还账期，账期金额：{}", expenseAfterSalesEntity.getExpenseAfterSalesNo(), debtPeriodMoney);
        if (debtPeriodMoney.compareTo(BigDecimal.ZERO) <= 0) {
            log.info("{}账期金额为：{}，无需归还账期", expenseAfterSalesEntity.getExpenseAfterSalesNo(), debtPeriodMoney);
            // 将当前售后单的 偿还账期
            updateRepaymentPeriod(expenseAfterSalesEntity.getExpenseAfterSalesId());
            return;
        }
        if (refundReadyMoney.compareTo(BigDecimal.ZERO) <= 0) {
            log.info("{}预退金额为：{}，无需归还账期", expenseAfterSalesEntity.getExpenseAfterSalesNo(), refundReadyMoney);
            // 将当前售后单的 偿还账期
            updateRepaymentPeriod(expenseAfterSalesEntity.getExpenseAfterSalesId());
            return;
        }
        ErpSpringBeanUtil.getBean(ExpenseAfterSalesServiceImpl.class).periodBackCapital(refundReadyMoney.compareTo(debtPeriodMoney) > 0 ? debtPeriodMoney : refundReadyMoney, traderName, expenseAfterSalesEntity);
        // 将当前售后单的 偿还账期
        updateRepaymentPeriod(expenseAfterSalesEntity.getExpenseAfterSalesId());

    }

    /**
     * 更新售后单的偿还账期的金额
     */
    public void updateRepaymentPeriod(Long expenseAfterSalesId) {

        BigDecimal repaymentPeriod = getExpenseRepaymentPeriod(expenseAfterSalesId);
        log.info("updateRepaymentPeriod 采购费用售后单：{}偿还账期的金额{}", expenseAfterSalesId, repaymentPeriod);
        ExpenseAfterSalesStatusEntity update = new ExpenseAfterSalesStatusEntity();
        update.setExpenseAfterSalesId(expenseAfterSalesId);
        update.setRepaymentPeriod(repaymentPeriod);
        expenseAfterSalesStatusMapper.updateByExpenseAfterSalesIdSelective(update);
    }

    public void refundAmount(BigDecimal debtPeriodMoney, BigDecimal refundReadyMoney, String traderName, ExpenseAfterSalesEntity expenseAfterSalesEntity) {
        log.info("{}step2:退还余额：{}", expenseAfterSalesEntity.getExpenseAfterSalesNo(), refundReadyMoney.subtract(debtPeriodMoney));
        if (refundReadyMoney.subtract(debtPeriodMoney).compareTo(BigDecimal.ZERO) <= 0) {
            log.info("{}退还余额：{}，无需退还余额", expenseAfterSalesEntity.getExpenseAfterSalesNo(), debtPeriodMoney);
            updateNeedReturnAmount(expenseAfterSalesEntity.getExpenseAfterSalesId(), BigDecimal.ZERO);
            return;
        }
        BigDecimal subtract = refundReadyMoney.subtract(debtPeriodMoney);

        // 退还余额
        if (expenseAfterSalesEntity.getRefundMethod().equals(ExpenseAfterSalesConstant.COMPANY_BALANCE)) {
            ErpSpringBeanUtil.getBean(ExpenseAfterSalesServiceImpl.class).accountBalancesCapital(subtract, traderName, expenseAfterSalesEntity);
        }

        updateNeedReturnAmount(expenseAfterSalesEntity.getExpenseAfterSalesId(), subtract);

    }

    /**
     * 更新售后单的因退金额
     */
    public void updateNeedReturnAmount(Long expenseAfterSalesId, BigDecimal needReturnAmount) {

        log.info("updateRepaymentPeriod 采购费用售后单：{}需要退还的金额{}", expenseAfterSalesId, needReturnAmount);
        ExpenseAfterSalesStatusEntity update = new ExpenseAfterSalesStatusEntity();
        update.setExpenseAfterSalesId(expenseAfterSalesId);
        update.setNeedReturnAmount(needReturnAmount);
        expenseAfterSalesStatusMapper.updateByExpenseAfterSalesIdSelective(update);
    }

    /**
     * 偿还账期
     *
     * @param debtPeriodMoney
     * @param traderName
     * @param expenseAfterSalesEntity
     */
    public void periodBackCapital(BigDecimal debtPeriodMoney, String traderName, ExpenseAfterSalesEntity expenseAfterSalesEntity) {
        log.info("{}费用售后执行退款运算：偿还账期", expenseAfterSalesEntity.getExpenseAfterSalesNo());
        CapitalBillDto cb = new CapitalBillDto();
        cb.setAmount(debtPeriodMoney);
        //退还信用
        cb.setTraderMode(529);
        //转移
        cb.setTraderType(3);
        cb.setPayee(traderName);
        cb.setPayer(BuyorderExpenseConstant.VEDENG);
        CapitalBillDetailDto cbd = new CapitalBillDetailDto();
        //信用还款
        cbd.setBussinessType(533);
        if (Objects.nonNull(expenseAfterSalesEntity.getIsAuto()) && expenseAfterSalesEntity.getIsAuto() == 1) {
            cbd.setUserId(BuyorderExpenseConstant.NJADMIN_ID);
            cb.setCreator(BuyorderExpenseConstant.NJADMIN_ID);
            cb.setComments(BuyorderExpenseConstant.PAY_COMMENT);
        } else {
            cbd.setUserId(CurrentUser.getCurrentUser().getId());
        }

        ErpSpringBeanUtil.getBean(ExpenseAfterSalesServiceImpl.class).saveCapitalBill(expenseAfterSalesEntity, cb, cbd, 2);
    }

    /**
     * 更新余额
     *
     * @param accountBalanceMoney
     * @param traderName
     * @param expenseAfterSalesEntity
     */
    public void accountBalancesCapital(BigDecimal accountBalanceMoney, String traderName, ExpenseAfterSalesEntity expenseAfterSalesEntity) {
        CapitalBillDto cb = new CapitalBillDto();
        CapitalBillDetailDto cbd = new CapitalBillDetailDto();
        // 取正值
        cb.setAmount(accountBalanceMoney);
        //退还余额
        cb.setTraderMode(530);
        //转入
        cb.setTraderType(4);
        cb.setPayee(BuyorderExpenseConstant.VEDENG);
        cb.setPayer(traderName);
        //退款
        cbd.setBussinessType(531);
        if (Objects.nonNull(expenseAfterSalesEntity.getIsAuto()) && expenseAfterSalesEntity.getIsAuto() == 1) {
            cbd.setUserId(BuyorderExpenseConstant.NJADMIN_ID);
            cb.setCreator(BuyorderExpenseConstant.NJADMIN_ID);
            cb.setComments(BuyorderExpenseConstant.PAY_COMMENT);
        } else {
            cbd.setUserId(CurrentUser.getCurrentUser().getId());
        }
        ErpSpringBeanUtil.getBean(ExpenseAfterSalesServiceImpl.class).saveCapitalBill(expenseAfterSalesEntity, cb, cbd, 2);

        BigDecimal amount = BigDecimal.ZERO;
        if (expenseAfterSalesEntity.getRefundMethod() == 1 || expenseAfterSalesEntity.getRefundMethod() == 2) {
            amount = accountBalanceMoney;
        }
        if (Objects.isNull(expenseAfterSalesEntity.getIsAuto()) || expenseAfterSalesEntity.getIsAuto().equals(0)) {
            Boolean isSuccess = traderSupplierApiService.updateSupplierAmount(expenseAfterSalesEntity.getTraderId(), amount);
            if (!isSuccess) {
                log.error("更新供应:{},商余额:{},失败",expenseAfterSalesEntity.getTraderId(),JSON.toJSONString(amount));
            }
        }

    }

    /**
     * 保存交易流水
     *
     * @param expenseAfterSalesEntity
     * @param cb
     * @param cbd
     * @param type
     */
    public void saveCapitalBill(ExpenseAfterSalesEntity expenseAfterSalesEntity, CapitalBillDto cb, CapitalBillDetailDto cbd, Integer type) {
        log.info("开始保存交易流水【expenseAfterSalesEntity】:{}", JSONObject.toJSONString(expenseAfterSalesEntity));
        CapitalBillDto capitalBill = new CapitalBillDto();
        capitalBill.setCreator(cbd.getUserId());
        capitalBill.setAddTime(DateUtil.sysTimeMillis());
        capitalBill.setCurrencyUnitId(1);
        capitalBill.setTraderTime(DateUtil.sysTimeMillis());
        capitalBill.setTraderType(cb.getTraderType());
        capitalBill.setTraderSubject(1);
        capitalBill.setTraderMode(cb.getTraderMode());
        capitalBill.setAmount(cb.getAmount());
        capitalBill.setPayee(cb.getPayee());//收款方
        capitalBill.setPayer(cb.getPayer());//付款方
        capitalBill.setCompanyId(BuyorderExpenseConstant.VEDENG_ID);
        capitalBill.setComments(cb.getComments());
        capitalBillApiService.insertCapitalBill(capitalBill);
        Integer capitalBillId = capitalBill.getCapitalBillId();
        CapitalBillDto capitalBillExtra = new CapitalBillDto();
        capitalBillExtra.setCapitalBillId(capitalBillId);
        capitalBillExtra.setCapitalBillNo(generatorNum(capitalBillId));
        capitalBillApiService.updateCapitalBillNo(capitalBillExtra);


        List<CapitalBillDetailDto> capitalBillDetailDtoList = new ArrayList<>();
        CapitalBillDetailDto capitalBillDetailDto = new CapitalBillDetailDto();
        capitalBillDetailDto.setAmount(capitalBill.getAmount());
        capitalBillDetailDto.setBussinessType(cbd.getBussinessType());//退款
        capitalBillDetailDto.setOrderType(5);//费用售后
        capitalBillDetailDto.setRelatedId(expenseAfterSalesEntity.getExpenseAfterSalesId().intValue());
        capitalBillDetailDto.setOrderNo(expenseAfterSalesEntity.getExpenseAfterSalesNo());
        capitalBillDetailDto.setUserId(cbd.getUserId());
        capitalBillDetailDto.setOrgId(cbd.getOrgId());
        capitalBillDetailDto.setOrgName(cbd.getOrgName());
        capitalBillDetailDto.setTraderId(expenseAfterSalesEntity.getTraderId());//交易者id
        capitalBillDetailDto.setTraderType(type);//客户/供应商
        capitalBillDetailDto.setCapitalBillId(capitalBillId);
        capitalBillDetailDtoList.add(capitalBillDetailDto);
        capitalBillApiService.insertCapitalBillDetail(capitalBillDetailDtoList);
    }

    /**
     * 生成资金流水号
     *
     * @param capitalBillId
     * @return
     */
    private String generatorNum(Integer capitalBillId) {
        BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.CAPITAL_NO, NoGeneratorBean.builder().dateFormat(ErpConstant.YYYYMMDDHHMMSS).id(capitalBillId).numberOfDigits(9).build());
        return new BillNumGenerator().distribution(billGeneratorBean);
    }

    /**
     * 更新售后单退款状态
     *
     * @param expenseAfterSalesId 售后单id
     */
    @Override
    public void updatePayStatus(Long expenseAfterSalesId) {

        log.info("step2:更新售后单退款状态");
        ExpenseAfterSalesEntity expenseAfterSalesEntity = expenseAfterSalesMapper.selectByPrimaryKey(expenseAfterSalesId);
        ExpenseAfterSalesStatusEntity expenseAfterSalesStatusEntity = expenseAfterSalesStatusMapper.selectByExpenseAfterSalesId(expenseAfterSalesEntity.getExpenseAfterSalesId());
        ExpenseAfterSalesStatusEntity afterSales = new ExpenseAfterSalesStatusEntity();
        afterSales.setExpenseAfterSalesStatusId(expenseAfterSalesStatusEntity.getExpenseAfterSalesStatusId());
        //退到贝登在供应商的余额
        BigDecimal realReturnAmount = getRealReturnAmount(expenseAfterSalesEntity.getExpenseAfterSalesId());
        BigDecimal needReturnAmount = expenseAfterSalesStatusEntity.getNeedReturnAmount();
        int refundStatus = getRefundStatus(needReturnAmount, realReturnAmount);
        afterSales.setRefundStatus(refundStatus);
        log.info("更新售后单退款状态入参:{}", JSONObject.toJSONString(afterSales));
        expenseAfterSalesStatusMapper.updateByPrimaryKeySelective(afterSales);

    }


    /**
     * 退款状态 0无退款 1未退款 2部分退款 3全部退款
     * 无退款 应退金额≤0
     * 未退款：应退金额>0，且无交易流水
     * 部分退款：应退金额>0，且交易流水金额之和＜应退金额+偿还账期
     * 全部退款：应退金额>0，且交易流水金额之和=应退金额+偿还账期；
     */
    public int getRefundStatus(BigDecimal refundReadyMoney, BigDecimal thisRefundMoney) {
        if (refundReadyMoney.compareTo(BigDecimal.ZERO) <= 0) {
            return 0;
        }
        if (thisRefundMoney.compareTo(BigDecimal.ZERO) <= 0) {
            return 1;
        }
        if (thisRefundMoney.compareTo(refundReadyMoney) >= 0) {
            return 3;
        }
        return 2;
    }

    @Override
    public List<StepsNodeDto> getStepNodeList(Long expenseAfterSalesId) {
        List<StepsNodeDto> stepsNodeDtoList = new ArrayList<>();
        handleOrderStatusNode(expenseAfterSalesId, stepsNodeDtoList);
        return stepsNodeDtoList;
    }

    /**
     * 费用售后单 订单状态 进度条
     *
     * @param expenseAfterSalesId 售后单id
     * @param nodeList            进度条节点
     */
    private void handleOrderStatusNode(Long expenseAfterSalesId, List<StepsNodeDto> nodeList) {
        ExpenseAfterSalesStatusEntity statusEntity = expenseAfterSalesStatusMapper.selectByExpenseAfterSalesId(expenseAfterSalesId);
        ExpenseAfterSalesEntity afterSalesEntity = expenseAfterSalesMapper.selectByPrimaryKey(expenseAfterSalesId);

        StepsNodeDto validNode = StepsNodeDto.builder().title("待确认").type(StepsTypeEnum.wait.getType()).build();
        StepsNodeDto verifyNode = StepsNodeDto.builder().title("待审核").type(StepsTypeEnum.wait.getType()).build();
        StepsNodeDto payNode = StepsNodeDto.builder().title("待退款").type(StepsTypeEnum.wait.getType()).build();
        StepsNodeDto invoiceNode = StepsNodeDto.builder().title("待退票").type(StepsTypeEnum.wait.getType()).build();
        StepsNodeDto endNode = StepsNodeDto.builder().title("已完结").type(StepsTypeEnum.wait.getType()).build();
        StepsNodeDto processNode = StepsNodeDto.builder().title("进行中").type(StepsTypeEnum.wait.getType()).build();

        // 退货退票类型的售后单
        if (afterSalesEntity.getExpenseAfterSalesType().equals(ExpenseAfterSalesConstant.EXPENSE_RETURN_GOODS)) {
            if (statusEntity.getAfterSalesStatus() == 3) {
                // 结束节点是已关闭的情况
                endNode.setTitle("已关闭");
                endNode.setType(StepsTypeEnum.error.getType());
                switch (statusEntity.getAuditStatus()) {
                    case 0:
                        nodeList.add(endNode);
                        return;
                    case 2:
                        validNode.setTitle("已确认");
                        validNode.setType(StepsTypeEnum.success.getType());
                        verifyNode.setTitle("审核通过");
                        verifyNode.setType(StepsTypeEnum.success.getType());
                        Collections.addAll(nodeList, validNode, verifyNode, endNode);
                        return;
                    case 3:
                        validNode.setTitle("已确认");
                        validNode.setType(StepsTypeEnum.success.getType());
                        verifyNode.setTitle("审核不通过");
                        verifyNode.setType(StepsTypeEnum.error.getType());
                        Collections.addAll(nodeList, validNode, verifyNode, endNode);
                        return;
                    default:
                }
            }

            if (statusEntity.getAfterSalesStatus() == 2) {
                // 正向完结
                validNode.setTitle("已确认");
                validNode.setType(StepsTypeEnum.success.getType());
                verifyNode.setTitle("审核通过");
                verifyNode.setType(StepsTypeEnum.success.getType());
                payNode.setTitle(statusEntity.getRefundStatus() == 0 ? "无退款" : "全部退款");
                payNode.setType(StepsTypeEnum.success.getType());
                invoiceNode.setTitle(statusEntity.getReturnInvoiceStatus() == 0 ? "无退票" : "全部退票");
                invoiceNode.setType(StepsTypeEnum.success.getType());
                endNode.setType(StepsTypeEnum.success.getType());
                Collections.addAll(nodeList, validNode, verifyNode, payNode, invoiceNode, endNode);
            }

            if (statusEntity.getAfterSalesStatus() == 1) {
                // 进行中，需要各个节点单独判断
                validNode.setTitle("已确认");
                validNode.setType(StepsTypeEnum.success.getType());
                verifyNode.setTitle("审核通过");
                verifyNode.setType(StepsTypeEnum.success.getType());
                if (statusEntity.getRefundStatus() == 0 || statusEntity.getRefundStatus() == 3) {
                    payNode.setTitle(statusEntity.getRefundStatus() == 0 ? "无退款" : "全部退款");
                    payNode.setType(StepsTypeEnum.success.getType());
                }
                if (statusEntity.getRefundStatus() == 1 || statusEntity.getRefundStatus() == 2) {
                    payNode.setTitle(statusEntity.getRefundStatus() == 1 ? "待退款" : "部分退款");
                }

                if (statusEntity.getRefundStatus() == -1) {
                    payNode.setTitle("待退款");
                }

                if (statusEntity.getReturnInvoiceStatus() == 0 || statusEntity.getReturnInvoiceStatus() == 3) {
                    invoiceNode.setTitle(statusEntity.getReturnInvoiceStatus() == 0 ? "无退票" : "全部退票");
                    invoiceNode.setType(StepsTypeEnum.success.getType());
                }
                if (statusEntity.getReturnInvoiceStatus() == 1 || statusEntity.getReturnInvoiceStatus() == 2) {
                    invoiceNode.setTitle(statusEntity.getReturnInvoiceStatus() == 1 ? "待退票" : "部分退票");
                }
                Collections.addAll(nodeList, validNode, verifyNode, payNode, invoiceNode, endNode);
            }

            if (statusEntity.getAfterSalesStatus() == 0) {
                // 待确认时
                if (statusEntity.getAuditStatus() == 1) {
                    // 审核中
                    validNode.setTitle("已确认");
                    validNode.setType(StepsTypeEnum.success.getType());
                    verifyNode.setTitle("审核中");
                }

                if (statusEntity.getAuditStatus() == 3) {
                    // 审核不通过
                    validNode.setTitle("已确认");
                    validNode.setType(StepsTypeEnum.success.getType());
                    verifyNode.setTitle("审核不通过");
                    verifyNode.setType(StepsTypeEnum.error.getType());
                }

                Collections.addAll(nodeList, validNode, verifyNode, payNode, invoiceNode, endNode);
            }
        }

        // 仅退票
        if (afterSalesEntity.getExpenseAfterSalesType().equals(ExpenseAfterSalesConstant.EXPENSE_RETURN_INVOICE)) {
            if (statusEntity.getAfterSalesStatus() == 0) {
                // 待确认，此时判断下是否在审核中
                validNode.setType(StepsTypeEnum.success.getType());
                if (statusEntity.getAuditStatus() == 1) {
                    validNode.setTitle("已确认");
                    verifyNode.setTitle("审核中");
                    verifyNode.setType(StepsTypeEnum.success.getType());
                }
                if (statusEntity.getAuditStatus() == 3) {
                    validNode.setTitle("已确认");
                    verifyNode.setTitle("审核不通过");
                    verifyNode.setType(StepsTypeEnum.error.getType());
                }

            }

            if (statusEntity.getAfterSalesStatus() == 1) {
                validNode.setTitle("已确认");
                validNode.setType(StepsTypeEnum.success.getType());
                verifyNode.setTitle("审核通过");
                verifyNode.setType(StepsTypeEnum.success.getType());
                processNode.setType(StepsTypeEnum.success.getType());
            }

            if (statusEntity.getAfterSalesStatus() == 2) {
                validNode.setTitle("已确认");
                validNode.setType(StepsTypeEnum.success.getType());
                verifyNode.setTitle("审核通过");
                verifyNode.setType(StepsTypeEnum.success.getType());
                processNode.setType(StepsTypeEnum.success.getType());
                endNode.setType(StepsTypeEnum.success.getType());
            }

            if (statusEntity.getAfterSalesStatus() == 3) {
                if (statusEntity.getAuditStatus() == 0) {
                    validNode.setType(StepsTypeEnum.error.getType());
                    verifyNode.setType(StepsTypeEnum.error.getType());
                    processNode.setType(StepsTypeEnum.error.getType());
                }

                if (statusEntity.getAuditStatus() == 3) {
                    validNode.setTitle("已确认");
                    validNode.setType(StepsTypeEnum.success.getType());
                    verifyNode.setTitle("审核不通过");
                    verifyNode.setType(StepsTypeEnum.error.getType());
                    processNode.setType(StepsTypeEnum.error.getType());
                }

                endNode.setTitle("已关闭");
                endNode.setType(StepsTypeEnum.error.getType());
            }

            Collections.addAll(nodeList, validNode, verifyNode, processNode, endNode);
        }

    }

    @Override
    public List<ExpenseAfterSalesItemDto> getExpenseAfterSalesReturnGoodsBybuyorderExpenseId(Integer buyorderExpenseId) {
        Assert.notNull(buyorderExpenseId, "采购费用单id不可为空");
        return expenseAfterSalesItemMapper.getExpenseAfterSalesReturnGoodsBybuyorderExpenseId(buyorderExpenseId);
    }

    @Override
    public Map<Integer, Integer> getExpenseAfterSalesReturnGoodsBybuyorderExpenseIdAndSaleOrderId(Integer buyorderExpenseId, Integer saleOrderId) {
        Assert.notNull(buyorderExpenseId, "采购费用单id不可为空");
        Assert.notNull(saleOrderId, "销售单id不可为空");
        List<ExpenseAfterSalesItemDto> expenseIdAndSaleOrderId = expenseAfterSalesItemMapper.getExpenseAfterSalesReturnGoodsBybuyorderExpenseIdAndSaleOrderId(buyorderExpenseId, saleOrderId);
        return expenseIdAndSaleOrderId.stream().collect(Collectors.toMap(ExpenseAfterSalesItemDto::getBuyorderExpenseItemId, ExpenseAfterSalesItemDto::getReturnNum, (k1, k2) -> k2));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void doExpenseAfterSalesInvoiceStatusByInvalid(List<Integer> invoiceIds) {
        log.info("doExpenseAfterSalesInvoiceStatusByInvalid 蓝字作废来后 将未完成记录置为以退票:入参：{}", JSON.toJSONString(invoiceIds));
        if (CollUtil.isEmpty(invoiceIds)) {
            return;
        }

        // 根据蓝票id查询 售后未完成的待录票的记录
        List<ExpenseAfterSalesInvoiceEntity> queryNotSuccessData = expenseAfterSalesInvoiceMapper.queryNotSuccessData(invoiceIds);
        log.info("查询到的需要置为完成的信息:{}", JSON.toJSONString(queryNotSuccessData));

        List<Long> collect = queryNotSuccessData.stream().map(ExpenseAfterSalesInvoiceEntity::getExpenseAfterSalesInvoiceId).collect(Collectors.toList());
        List<Long> longs = queryNotSuccessData.stream().map(ExpenseAfterSalesInvoiceEntity::getExpenseAfterSalesId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            expenseAfterSalesInvoiceMapper.batchReturnInvoiceStatusSuccess(collect);
        }
        log.info("更新采购售后单收票状态:{}", JSON.toJSONString(longs));
        if (CollUtil.isNotEmpty(longs)) {
            longs.forEach(this::calculateAndUpdateInvoiceReturnStatus);
        }

    }

    @Override
    public ExpenseAfterSalesDto getAfterSalesAndStatusInfo(Long expenseAfterSalesId) {
        return expenseAfterSalesMapper.getExpenseAfterSalesDetail(expenseAfterSalesId);
    }

    @Override
    public int doAudit(Long expenseAfterSalesId) {
        Assert.notNull(expenseAfterSalesId, "发起审核采购费用售后单id不可为空");

        ExpenseAfterSalesStatusEntity update = new ExpenseAfterSalesStatusEntity();
        update.setExpenseAfterSalesId(expenseAfterSalesId);
        update.setAuditStatus(AuditEnum.PROCESS.getAuditStatus());

        return expenseAfterSalesStatusMapper.updateByExpenseAfterSalesIdSelective(update);
    }

    @Override
    public int audit(Long expenseAfterSalesId) {
        Assert.notNull(expenseAfterSalesId, "发起审核采购费用售后单id不可为空");

        ExpenseAfterSalesStatusEntity update = new ExpenseAfterSalesStatusEntity();
        update.setExpenseAfterSalesId(expenseAfterSalesId);
        update.setAuditStatus(AuditEnum.SUCCESS.getAuditStatus());
        update.setValidTime(new Date());
        update.setValidStatus(ExpenseAfterSalesConstant.ONE);
        return expenseAfterSalesStatusMapper.updateByExpenseAfterSalesIdSelective(update);
    }

    @Override
    public int unAudit(Long expenseAfterSalesId) {
        Assert.notNull(expenseAfterSalesId, "发起审核采购费用售后单id不可为空");

        ExpenseAfterSalesStatusEntity update = new ExpenseAfterSalesStatusEntity();
        update.setExpenseAfterSalesId(expenseAfterSalesId);
        update.setAuditStatus(AuditEnum.ERROR.getAuditStatus());

        return expenseAfterSalesStatusMapper.updateByExpenseAfterSalesIdSelective(update);
    }

    @Override
    public List<RefundInvoiceRecordDto> getCanRefundInvoicesByRelatedId(Long expenseAfterSalesId) {
        ExpenseAfterSalesEntity expenseAfterSalesEntity = expenseAfterSalesMapper.selectByPrimaryKey(expenseAfterSalesId);
        if (expenseAfterSalesEntity == null) {
            throw new ServiceException("采购费用售后单不存在或已删除");
        }

        List<ExpenseAfterSalesInvoiceEntity> expenseAfterSalesInvoices = expenseAfterSalesInvoiceMapper.findByExpenseAfterSalesId(expenseAfterSalesId);
        if (CollUtil.isEmpty(expenseAfterSalesInvoices)) {
            return ListUtil.empty();
        }

        List<RefundInvoiceRecordDto> refundInvoiceRecordDtos = new ArrayList<>();
        List<InvoiceDto> invoiceDtos = invoiceApiService.getInvoicesByRelatedId(expenseAfterSalesEntity.getBuyorderExpenseId());
        if (CollUtil.isEmpty(invoiceDtos)) {
            return ListUtil.empty();
        }

        Map<String, List<ExpenseAfterSalesInvoiceEntity>> map = expenseAfterSalesInvoices.stream()
                .collect(Collectors.groupingBy(e -> e.getInvoiceNo() + StrUtil.DASHED + e.getInvoiceCode()));
        map.forEach((k, v) -> invoiceDtos.forEach(i -> {
            String key = i.getInvoiceNo() + StrUtil.DASHED + i.getInvoiceCode();
            if (k.equals(key)) {
                RefundInvoiceRecordDto refundInvoiceRecordDto = this.invoiceToRefundInvoiceRecordDto(i, CollUtil.getFirst(v));
                refundInvoiceRecordDtos.add(refundInvoiceRecordDto);
            }
        }));
        return refundInvoiceRecordDtos;
    }

    @Override
    public void noNeedRefundInvoice(ReturnInvoiceWriteBackDto returnInvoiceWriteBackDto) {
        List<ExpenseAfterSalesInvoiceEntity> expenseAfterSalesInvoices = expenseAfterSalesInvoiceMapper.selectNeedRefund(returnInvoiceWriteBackDto);
        if (CollUtil.isEmpty(expenseAfterSalesInvoices)) {
            throw new ServiceException("当前无发票可退，请刷新后重试");
        }
        expenseAfterSalesInvoices.forEach(expenseAfterSalesInvoiceEntity -> expenseAfterSalesInvoiceEntity.setIsRefundInvoice(0));
        expenseAfterSalesInvoiceMapper.updateBatchSelective(expenseAfterSalesInvoices);

        //调用费用售后单状态更新方法
        this.calculateAndUpdateInvoiceReturnStatus(returnInvoiceWriteBackDto.getExpenseAfterSalesId());
        try {
            this.completeExpenseAfterSales(returnInvoiceWriteBackDto.getExpenseAfterSalesId());
        } catch (Exception e) {
            log.error("自动完结失败，费用售后单ID[{}]，失败原因[{}]", returnInvoiceWriteBackDto.getExpenseAfterSalesId(), e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public InvoiceReversalDto reversalInvoices(Long expenseAfterSalesId, String invoiceCode, String invoiceNo) {
        ExpenseAfterSalesEntity expenseAfterSalesEntity = expenseAfterSalesMapper.selectByPrimaryKey(expenseAfterSalesId);
        List<ExpenseAfterSalesInvoiceEntity> list = expenseAfterSalesInvoiceMapper.findByExpenseAfterSalesIdAndInvoiceCodeAndInvoiceNo(expenseAfterSalesId, invoiceCode, invoiceNo);
        if (CollUtil.isEmpty(list)) {
            throw new ServiceException("当前无可申请冲销发票，请刷新后重试");
        }

        if (CollUtil.isNotEmpty(list)) {
            list.forEach(l -> l.setReturnInvoiceStatus(2));
            expenseAfterSalesInvoiceMapper.updateBatchSelective(list);
        }

        if (expenseAfterSalesEntity == null) {
            throw new ServiceException("采购费用售后单不存在或已删除");
        }
        List<InvoiceDto> invoiceDtos = invoiceApiService.getInvoicesByRelatedId(expenseAfterSalesEntity.getBuyorderExpenseId());
        if (CollUtil.isEmpty(invoiceDtos)) {
            return null;
        }
        invoiceDtos = invoiceDtos.stream().filter(i -> i.getInvoiceCode().equals(invoiceCode) && i.getInvoiceNo().equals(invoiceNo)).collect(Collectors.toList());
        TraderSupplierDto supplier = traderSupplierApiService.getTraderSupplierByTraderId(expenseAfterSalesEntity.getTraderId());
        InvoiceDto invoiceDto = CollUtil.getFirst(invoiceDtos);
        InvoiceReversalDto invoiceReversalDto = new InvoiceReversalDto();
        invoiceReversalDto.setSaleName(supplier.getTraderName());
        invoiceReversalDto.setReversalBillType(ErpConst.TWO);
        invoiceReversalDto.setReversalBillNo(expenseAfterSalesEntity.getExpenseAfterSalesNo());
        invoiceReversalDto.setInvoiceCode(invoiceDto.getInvoiceCode());
        invoiceReversalDto.setInvoiceNo(invoiceDto.getInvoiceNo());
        invoiceReversalDto.setInvoiceType(invoiceDto.getInvoiceType());
        invoiceReversalDto.setReversalAuditStatus(ErpConst.ZERO);
        invoiceReversalDto.setAuditApplyTime(new Date());
        invoiceReversalDto.setInvoiceId(ErpConstant.DEFAULT_ID);
        if (Objects.nonNull(expenseAfterSalesEntity.getIsAuto())&&expenseAfterSalesEntity.getIsAuto().equals(1)) {
            invoiceReversalDto.setCreator(BuyorderExpenseConstant.NJADMIN_ID);
            invoiceReversalDto.setCreatorName(BuyorderExpenseConstant.NJADMIN_NAME);
        }
        return invoiceReversalApiService.insertSelective(invoiceReversalDto);
    }

    private RefundInvoiceRecordDto invoiceToRefundInvoiceRecordDto(InvoiceDto invoiceDto, ExpenseAfterSalesInvoiceEntity expenseAfterSalesInvoice) {
        RefundInvoiceRecordDto refundInvoiceRecordDto = new RefundInvoiceRecordDto();
        refundInvoiceRecordDto.setInvoiceNo(invoiceDto.getInvoiceNo());
        refundInvoiceRecordDto.setInvoiceAmount(invoiceDto.getAmount());
        refundInvoiceRecordDto.setInvoiceCode(invoiceDto.getInvoiceCode());
        refundInvoiceRecordDto.setInvoiceType(invoiceDto.getInvoiceType());

        Integer refundInvoiceStatus = expenseAfterSalesInvoice.getReturnInvoiceStatus();
        if (expenseAfterSalesInvoice.getIsRefundInvoice() == 0) {
            refundInvoiceStatus = ExpenseAfterSalesInvoiceStatusEnum.DONT_NEED.getCode();
        }
        refundInvoiceRecordDto.setRefundInvoiceStatus(refundInvoiceStatus);
        return refundInvoiceRecordDto;
    }

    @Override
    public ReturnInvoiceDto getReturnInvoiceGoodsData(ExpenseAfterSalesViewDto query) {

        log.info("getReturnInvoiceGoodsData 入参：{}", JSON.toJSONString(query));
        Assert.notNull(query, "查询退票信息参数不可为空");
        Assert.notNull(query.getExpenseAfterSalesId(), "查询退票信息参数不可为空");
        Assert.notNull(query.getInvoiceNo(), "查询退票信息参数不可为空");
        Assert.notNull(query.getInvoiceCode(), "查询退票信息参数不可为空");

        List<ReturnInvoiceGoodsDto> returnInvoiceGoodsData = expenseAfterSalesInvoiceMapper.getReturnInvoiceGoodsData(query);
        if (CollUtil.isEmpty(returnInvoiceGoodsData)) {
            log.error("无待录票信息：{}", JSON.toJSONString(query));
            return new ReturnInvoiceDto();
        }
        List<ExpenseAfterSalesItemDto> afterSalesItems = expenseAfterSalesItemMapper.getExpenseAfterSalesItemListByAfterSalesId(query.getExpenseAfterSalesId());
        Map<String, Integer> return2Map = afterSalesItems.stream().collect(Collectors.toMap(ExpenseAfterSalesItemDto::getSku, c -> c.getReturnNum() == null ? 0 : c.getReturnNum(), Integer::sum));
        // 退货数量
        returnInvoiceGoodsData.forEach(c -> {
            c.setAfterReturnNum(return2Map.get(c.getSku()) == null ? 0 : return2Map.get(c.getSku()));
            c.setTotalAmount(c.getPrice().multiply(c.getReturnNum()).setScale(2, RoundingMode.HALF_UP));
        });

        ReturnInvoiceDto returnInvoiceDto = new ReturnInvoiceDto();
        returnInvoiceDto.setInvoiceNo(query.getInvoiceNo());
        returnInvoiceDto.setExpenseAfterSalesId(query.getExpenseAfterSalesId());
        returnInvoiceDto.setInvoiceCode(query.getInvoiceCode());
        returnInvoiceDto.setReturnInvoiceGoodsDtos(returnInvoiceGoodsData);
        ExpenseAfterSalesDto expenseAfterSalesDetail = expenseAfterSalesMapper.getExpenseAfterSalesDetail(query.getExpenseAfterSalesId());
        if (Objects.isNull(expenseAfterSalesDetail)) {
            throw new ServiceException("关联的售后单不存在：" + query.getExpenseAfterSalesId());
        }
        returnInvoiceDto.setExpenseAfterSalesType(expenseAfterSalesDetail.getExpenseAfterSalesType());

        // 查询蓝字有效
        InvoiceDto build = InvoiceDto.builder()
                .relatedId(expenseAfterSalesDetail.getBuyorderExpenseId())
                .type(ExpenseAfterSalesConstant.INVOICE_TYPE_EXPENSE)
                .invoiceNo(query.getInvoiceNo())
                .invoiceCode(query.getInvoiceCode())
                .colorType(2)
                .isEnable(1)
                .validStatus(1)
                .build();
        List<InvoiceDto> oneInvoiceByNoCodeType = invoiceApiService.getOneInvoiceByNoCodeType(build);
        if (CollUtil.isNotEmpty(oneInvoiceByNoCodeType)) {
            BigDecimal amount = oneInvoiceByNoCodeType.stream().map(InvoiceDto::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            returnInvoiceDto.setInvoiceType(oneInvoiceByNoCodeType.get(0).getInvoiceType());
            returnInvoiceDto.setInvoiceTypeStr(oneInvoiceByNoCodeType.get(0).getInvoiceTypeStr());
            returnInvoiceDto.setAmount(amount);
            returnInvoiceDto.setRatio(oneInvoiceByNoCodeType.get(0).getRatio());
        } else {
            // 默认发票类型
            returnInvoiceDto.setInvoiceType(ExpenseAfterSalesConstant.INVOICE_TYPE);
            returnInvoiceDto.setInvoiceTypeStr(ExpenseAfterSalesConstant.INVOICE_TYPE_STR);
            returnInvoiceDto.setAmount(BigDecimal.ZERO);
            returnInvoiceDto.setRatio(new BigDecimal(ExpenseAfterSalesConstant.INVOICE_TYPE_RATIO));
        }
        if (ExpenseAfterSalesConstant.EXPENSE_RETURN_INVOICE.equals(returnInvoiceDto.getExpenseAfterSalesType())) {
            returnInvoiceDto.setInvoiceNoRed(returnInvoiceDto.getInvoiceNo());
            returnInvoiceDto.setInvoiceCodeRed(returnInvoiceDto.getInvoiceCode());
        }
        return returnInvoiceDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void closeExpenseAfterSales(Long expenseAfterSalesId, Integer buyorderExpenseId) {
        // 再次校验售后单状态
        ExpenseAfterSalesStatusEntity statusEntity = expenseAfterSalesStatusMapper.selectByExpenseAfterSalesId(expenseAfterSalesId);
        if (!(statusEntity.getAfterSalesStatus() == 0 && statusEntity.getAuditStatus() != 1)) {
            throw new ServiceException("采购费用售后单状态已发生变化，请刷新页面后重试");
        }
        ExpenseAfterSalesStatusEntity colseEntity = new ExpenseAfterSalesStatusEntity();
        colseEntity.setExpenseAfterSalesStatusId(statusEntity.getExpenseAfterSalesStatusId());
        colseEntity.setAfterSalesStatus(ExpenseAfterSalesConstant.AFTER_SALES_STATUS_CLOSED);
        colseEntity.setUpdateRemark("关闭采购费用售后单触发更新");
        expenseAfterSalesStatusMapper.updateByPrimaryKeySelective(colseEntity);
        buyorderExpenseApiService.unlockBuyOrderExpense(buyorderExpenseId);
        rExpenseAfterSalesJSaleorderMapper.deleteByExpenseAfterSalesId(expenseAfterSalesId);
        log.info("关闭采购费用售后单触发解锁采购费用单，费用售后单id：{}", expenseAfterSalesId);
    }

    @Override
    public List<InvoiceDto> getInvoiceListByAfterSalesId(Long expenseAfterSalesId) {
        return invoiceApiService.getInvoiceListByAfterSalesId(expenseAfterSalesId.intValue(), ExpenseAfterSalesConstant.INVOICE_TYPE_EXPENSE);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Integer recordRefundInvoice(ReturnInvoiceDto data) {
        log.info("recordRefundInvoice 入参：{}", JSON.toJSONString(data));
        validatorInvoice(data);

        InvoiceDto invoiceDto = bindInvoiceData(data);

        Integer invoiceId = invoiceApiService.saveRedInvoice(invoiceDto);
        ReturnInvoiceWriteBackDto returnInvoiceWriteBackDto = ReturnInvoiceWriteBackDto.builder()
                .invoiceId(invoiceId)
                .invoiceNo(data.getInvoiceNo())
                .invoiceCode(data.getInvoiceCode())
                .expenseAfterSalesId(data.getExpenseAfterSalesId())
                .build();
        updateReturnInvoiceGoodsData(returnInvoiceWriteBackDto);
        // 更新售后单退票状态
        ExpenseAfterSalesServiceImpl bean = SpringUtil.getBean(ExpenseAfterSalesServiceImpl.class);
        bean.calculateAndUpdateInvoiceReturnStatus(data.getExpenseAfterSalesId());
        // 自动完结
        try {
            bean.completeExpenseAfterSales(data.getExpenseAfterSalesId());
        } catch (Exception e) {
            log.error("尝试关闭采购费用售后单", e);
        }
        return invoiceId;
    }

    /**
     * 退票后更新记录
     *
     * @param returnInvoiceWriteBackDto 蓝票code 蓝票no 售后id 要回写的红票
     * @return 影响行数
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public Integer updateReturnInvoiceGoodsData(ReturnInvoiceWriteBackDto returnInvoiceWriteBackDto) {

        log.info("updateReturnInvoiceGoodsData 入参：{}", JSON.toJSONString(returnInvoiceWriteBackDto));
        Assert.notNull(returnInvoiceWriteBackDto, "退票回写参数不可为空");
        Assert.notNull(returnInvoiceWriteBackDto.getInvoiceNo(), "退票回写参数不可为空");
        Assert.notNull(returnInvoiceWriteBackDto.getInvoiceCode(), "退票回写参数不可为空");
        Assert.notNull(returnInvoiceWriteBackDto.getExpenseAfterSalesId(), "退票回写参数不可为空");
        Assert.notNull(returnInvoiceWriteBackDto.getInvoiceId(), "退票回写参数不可为空");

        List<ExpenseAfterSalesInvoiceEntity> expenseAfterSalesInvoiceEntities = expenseAfterSalesInvoiceMapper.selectNeedRefund(returnInvoiceWriteBackDto);

        List<Long> collect = expenseAfterSalesInvoiceEntities.stream().map(ExpenseAfterSalesInvoiceEntity::getExpenseAfterSalesInvoiceId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            return expenseAfterSalesInvoiceMapper.writebackInvocie(collect, returnInvoiceWriteBackDto.getInvoiceId(), "退票");
        }
        return 0;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public Integer updateReversalInvoiceGoodsData(ReturnInvoiceWriteBackDto returnInvoiceWriteBackDto) {

        log.info("updateReversalInvoiceGoodsData 入参：{}", JSON.toJSONString(returnInvoiceWriteBackDto));
        Assert.notNull(returnInvoiceWriteBackDto, "冲销回写参数不可为空");
        Assert.notNull(returnInvoiceWriteBackDto.getInvoiceNo(), "冲销回写参数不可为空");
        Assert.notNull(returnInvoiceWriteBackDto.getInvoiceCode(), "冲销回写参数不可为空");
        Assert.notNull(returnInvoiceWriteBackDto.getExpenseAfterSalesId(), "冲销回写参数不可为空");
        Assert.notNull(returnInvoiceWriteBackDto.getInvoiceId(), "冲销回写参数不可为空");

        List<ExpenseAfterSalesInvoiceEntity> expenseAfterSalesInvoiceEntities = expenseAfterSalesInvoiceMapper.selectNeedReversal(returnInvoiceWriteBackDto);

        List<Long> collect = expenseAfterSalesInvoiceEntities.stream().map(ExpenseAfterSalesInvoiceEntity::getExpenseAfterSalesInvoiceId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            return expenseAfterSalesInvoiceMapper.writebackInvocie(collect, returnInvoiceWriteBackDto.getInvoiceId(), "冲销");
        }
        return 0;
    }

    /**
     * 校验入参信息
     * 参数不可为空
     * 带录票记录可不为空
     *
     * @param data 入参
     */
    private void validatorInvoice(ReturnInvoiceDto data) {

        log.info("validatorInvoice 入参：{}", JSON.toJSONString(data));
        Assert.notNull(data, "保存退票信息参数不可为空");
        Assert.notNull(data.getExpenseAfterSalesId(), "保存退票信息参数不可为空");
        Assert.notNull(data.getInvoiceNo(), "保存退票信息参数不可为空");
        Assert.notNull(data.getInvoiceCode(), "保存退票信息参数不可为空");
        Assert.notEmpty(data.getReturnInvoiceGoodsDtos(), "保存退票信息商品明细不可为空");
        ExpenseAfterSalesViewDto query = ExpenseAfterSalesViewDto.builder()
                .expenseAfterSalesId(data.getExpenseAfterSalesId())
                .invoiceNo(data.getInvoiceNo())
                .invoiceCode(data.getInvoiceCode())
                .build();
        List<ReturnInvoiceGoodsDto> returnInvoiceGoodsData = expenseAfterSalesInvoiceMapper.getReturnInvoiceGoodsData(query);
        if (CollUtil.isEmpty(returnInvoiceGoodsData)) {
            log.error("无待录票信息：{}", JSON.toJSONString(data));
            throw new ServiceException("无待录票信息");
        }
    }

    /**
     * 组装财务录票的红票信息
     * 此处要记录原始蓝票的蓝票航信id
     *
     * @param data 参数
     * @return 给财务模块的保存对象
     */
    private InvoiceDto bindInvoiceData(ReturnInvoiceDto data) {

        log.info("bindInvoiceData 入参：{}", JSON.toJSONString(data));
        long time = System.currentTimeMillis();
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        ExpenseAfterSalesDto expenseAfterSalesDetail = expenseAfterSalesMapper.getExpenseAfterSalesDetail(data.getExpenseAfterSalesId());

        List<ReturnInvoiceGoodsDto> returnInvoiceGoodsDtos = data.getReturnInvoiceGoodsDtos();
        BigDecimal amount = returnInvoiceGoodsDtos.stream().map(ReturnInvoiceGoodsDto::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        // 保存 原蓝字的航行id
        InvoiceDto build = InvoiceDto.builder()
                .relatedId(expenseAfterSalesDetail.getBuyorderExpenseId())
                .type(ExpenseAfterSalesConstant.INVOICE_TYPE_EXPENSE)
                .invoiceNo(data.getInvoiceNo())
                .invoiceCode(data.getInvoiceCode())
                .invoiceProperty(data.getInvoiceProperty())
                .colorType(2)
                .isEnable(1)
                .validStatus(1)
                .build();
        List<InvoiceDto> oneInvoiceByNoCodeType = invoiceApiService.getOneInvoiceByNoCodeType(build);
        InvoiceDto one = null;
        if (CollUtil.isNotEmpty(oneInvoiceByNoCodeType)) {
            one = oneInvoiceByNoCodeType.get(0);
        }
        InvoiceDto invoiceDto = Optional.ofNullable(one).orElse(InvoiceDto.builder().build());
        List<Integer> invoiceIds = oneInvoiceByNoCodeType.stream().map(InvoiceDto::getInvoiceId).collect(Collectors.toList());

        String validComments = "";
        if (ExpenseAfterSalesConstant.EXPENSE_RETURN_INVOICE.equals(data.getExpenseAfterSalesType())) {
            validComments = ExpenseAfterSalesConstant.VALID_COMMENTS;
        }
        InvoiceDto newData = InvoiceDto.builder()
                .invoiceType(data.getInvoiceType())
                .ratio(data.getRatio())
                .invoiceNo(data.getInvoiceNoRed())
                .invoiceCode(data.getInvoiceCodeRed())
                .type(ExpenseAfterSalesConstant.INVOICE_TYPE_EXPENSE)
                .invoiceProperty(1)
                .tag(2)
                .companyId(1)
                .relatedId(expenseAfterSalesDetail.getBuyorderExpenseId())
                .afterSalesId(Math.toIntExact(data.getExpenseAfterSalesId()))
                // 红字有效
                .colorType(1)
                .isEnable(1)
                // 默认审核通过
                .validStatus(1)
                .validTime(time)
                .addTime(time)
                .modTime(time)
                .validUserid(currentUser.getId())
                .creator(currentUser.getId())
                .updater(currentUser.getId())
                .validComments(validComments)
                // 存的原始蓝字有效的航信票id
                .hxInvoiceId(invoiceDto.getHxInvoiceId())
                .invoiceFrom(invoiceDto.getHxInvoiceId() != null && invoiceDto.getHxInvoiceId() != 0 ? 1 : 0)
                .amount(amount.negate())
                .invoiceIds(invoiceIds)
                .build();
        List<InvoiceDetailDto> invoiceDetailDtos = bindInvoiceDetail(returnInvoiceGoodsDtos);
        newData.setInvoiceDetailDtos(invoiceDetailDtos);
        log.info("bindInvoiceData 返回结果：{}", JSON.toJSONString(newData));
        return newData;
    }

    private List<InvoiceDetailDto> bindInvoiceDetail(List<ReturnInvoiceGoodsDto> returnInvoiceGoodsDtos) {
        // 组装
        return returnInvoiceGoodsDtos.stream().map(c -> InvoiceDetailDto.builder()
                .detailgoodsId(c.getBuyorderExpenseItemId())
                .num(c.getReturnNum())
                .price(c.getInvoicePrice() == null ? c.getPrice().negate() : c.getInvoicePrice().negate())
                .totalAmount(c.getAmount().negate())
                .build()).collect(Collectors.toList());
    }

    @Override
    public ExpenseAfterSalesDto queryExpenseAfterSalesInfoByNo(String expenseAfterSalesNo) {
        return expenseAfterSalesMapper.queryInfoByNo(expenseAfterSalesNo);
    }

    @Override
    public void calculateAndUpdateInvoiceReturnStatus(Long expenseAfterSalesId) {
        ExpenseAfterSalesStatusEntity statusEntity = new ExpenseAfterSalesStatusEntity();
        statusEntity.setExpenseAfterSalesId(expenseAfterSalesId);

        List<ExpenseAfterSalesInvoiceEntity> invoiceEntities = expenseAfterSalesInvoiceMapper.findByExpenseAfterSalesId(expenseAfterSalesId);
        if (invoiceEntities.size() == 0) {
            // 无退票
            statusEntity.setReturnInvoiceStatus(ExpenseAfterSalesConstant.NO_REFUND);
            this.updateExpenseAfterSales(statusEntity);
            return;
        }

        // 判断是否存在 非（需要退票 且 未退票）的发票记录
        Optional<ExpenseAfterSalesInvoiceEntity> invoiceEntity = invoiceEntities.stream()
                .filter(item -> !(item.getReturnInvoiceStatus() == 0 && item.getIsRefundInvoice() == 1)).findAny();
        if (!invoiceEntity.isPresent()) {
            // 不存在，则为 未退票
            statusEntity.setReturnInvoiceStatus(ExpenseAfterSalesConstant.HAVE_NOT_REFUND);
            this.updateExpenseAfterSales(statusEntity);
            return;
        }

        // 判断是否存在 非 （已退票 || 无需退票）的信息
        Optional<ExpenseAfterSalesInvoiceEntity> any = invoiceEntities.stream()
                .filter(item -> !(item.getIsRefundInvoice() == 0 || item.getReturnInvoiceStatus() == 1)).findAny();
        // 若存在则为 部分退票  否则为 全部退票
        statusEntity.setReturnInvoiceStatus(any.isPresent() ? ExpenseAfterSalesConstant.PART_REFUND : ExpenseAfterSalesConstant.ALL_REFUND);
        this.updateExpenseAfterSales(statusEntity);
    }

    private void updateExpenseAfterSales(ExpenseAfterSalesStatusEntity statusEntity) {
        log.info("计算并更新采购费用售后单退票状态，售后单信息：{}", JSONObject.toJSONString(statusEntity));
        expenseAfterSalesStatusMapper.updateByExpenseAfterSalesIdSelective(statusEntity);
    }

    @Override
    public void completeExpenseAfterSales(Long expenseAfterSalesId) {
        ExpenseAfterSalesStatusEntity statusEntity = expenseAfterSalesStatusMapper.selectByExpenseAfterSalesId(expenseAfterSalesId);
        if (statusEntity.getRefundStatus() == 1 || statusEntity.getRefundStatus() == 2 || statusEntity.getRefundStatus() == -1) {
            log.info("{}执行退款运算，退款状态校验拦截，当前状态为：{}", expenseAfterSalesId, statusEntity.getRefundStatus());
            throw new ServiceException("该订单尚未全部退款，请完成退款操作！");
        }

        if (statusEntity.getReturnInvoiceStatus() == 1 || statusEntity.getReturnInvoiceStatus() == 2) {
            log.info("{}执行退款运算，发票状态校验拦截，当前状态为：{}", expenseAfterSalesId, statusEntity.getReturnInvoiceStatus());
            throw new ServiceException("该订单尚未全部退票，请完成退票操作！");
        }
        // 更新为 已完结
        statusEntity.setAfterSalesStatus(ExpenseAfterSalesConstant.AFTER_SALES_STATUS_COMPLETED);
        expenseAfterSalesStatusMapper.updateByPrimaryKeySelective(statusEntity);

        // 解锁关联的费用单
        Integer buyorderExpenseId = expenseAfterSalesMapper.selectByPrimaryKey(expenseAfterSalesId).getBuyorderExpenseId();
        buyorderExpenseApiService.unlockBuyOrderExpense(buyorderExpenseId);
        log.info("采购费用售后单完结解锁采购费用单，费用售后单id：{}", expenseAfterSalesId);

        buyorderExpenseApiService.directPaymentStatus(buyorderExpenseId);
        buyorderExpenseApiService.doBuyOrderExpenseInvoiceStatus(buyorderExpenseId);
        buyorderExpenseApiService.completeBuyOrderExpense(buyorderExpenseId);
        log.info("采购费用售后单完结,更新采购费用单完结状态，费用售后单id：{}", expenseAfterSalesId);
        //对应采购费用单（判断已预警）
        try{
            Integer returnEarlyWarn = buyorderExpenseApiService.getReturnEarlyWarnByBuyorderExpenseId(buyorderExpenseId);
            if (ErpConstant.ONE.equals(returnEarlyWarn)) {
                log.info("采购费用售后单完结,退货预警解除开始，费用售后单id：{}", expenseAfterSalesId);
                releaseReturnEarlyWarnWhenExpense(buyorderExpenseId,expenseAfterSalesId);
            }
        }catch (Exception e){
            log.error("采购费用售后单完结,退货预警解除 error",e);
        }

    }

    public void releaseReturnEarlyWarnWhenExpense(Integer buyorderExpenseId, Long expenseAfterSalesId) {
        //1.查询该费用售后单，按saleorderid、sku分组计算数量
        List<RExpenseAfterSalesJSaleorderDto> list = rExpenseAfterSalesJSaleorderMapper.getReturnNumByBuyorderExpenseId(buyorderExpenseId);
        //2.根据saleorderId、sku查询销售售后的售后数量(排除已关闭)
        for (RExpenseAfterSalesJSaleorderDto rExpenseAfterSalesJSaleorderDto : list) {
            Integer afterSalesExpenseNum = rExpenseAfterSalesJSaleorderDto.getAfterSalesNum();
            Integer saleorderId = rExpenseAfterSalesJSaleorderDto.getSaleorderId();
            Integer saleorderGoodsId = rExpenseAfterSalesJSaleorderDto.getSaleorderGoodsId();
            Integer afterSalesNum = afterSalesApiService.getAfterSalesNumBySaleorderGoodsId(saleorderGoodsId);
            ExpenseReturnEarlyWarnEntity expenseReturnEarlyWarnEntity = new ExpenseReturnEarlyWarnEntity();expenseReturnEarlyWarnEntity.setSaleorderId(saleorderId);
            expenseReturnEarlyWarnEntity.setSaleorderGoodsId(saleorderGoodsId);
            expenseReturnEarlyWarnEntity.setBuyorderExpenseIdList(Arrays.asList(buyorderExpenseId));
            expenseReturnEarlyWarnEntity.setSkuId(rExpenseAfterSalesJSaleorderDto.getSkuId());
            expenseReturnEarlyWarnEntity.setSkuNo(rExpenseAfterSalesJSaleorderDto.getSkuNo());
            expenseReturnEarlyWarnEntity.setNum(rExpenseAfterSalesJSaleorderDto.getAfterSalesNum());
            if (Objects.nonNull(afterSalesExpenseNum) && Objects.nonNull(afterSalesNum) && afterSalesExpenseNum.intValue() >= afterSalesNum.intValue()) {
                log.info("采购费用售后单完结,预警商品完全解除，expenseReturnEarlyWarnEntity：{}", JSON.toJSONString(expenseReturnEarlyWarnEntity));
                buyorderExpenseService.freePartReturnAlertBuyOrderExpenseGoods(expenseReturnEarlyWarnEntity);
            }else{
                rExpenseAfterSalesJSaleorderDto.setExpenseAfterSalesId(expenseAfterSalesId);
                RExpenseAfterSalesJSaleorderDto dto =rExpenseAfterSalesJSaleorderMapper.getReturnNumByExpenseAfterSalesId(rExpenseAfterSalesJSaleorderDto);
                if (Objects.nonNull(dto)&&Objects.nonNull(dto.getAfterSalesNum())){
                    expenseReturnEarlyWarnEntity.setNum(dto.getAfterSalesNum());
                    log.info("采购费用售后单完结,预警商品部分解除，进行扣减，expenseReturnEarlyWarnEntity：{}", JSON.toJSONString(expenseReturnEarlyWarnEntity));
                    buyorderExpenseService.freePartReturnAlertBuyOrderExpenseGoodsByCompute(expenseReturnEarlyWarnEntity);
                }
            }
        }
    }

    @Override
    public ExpenseAfterSalesPaymentDto getReturnPaymentData(Long expenseAfterSalesId) {
        log.info("getReturnPaymentData 入参：{}", JSON.toJSONString(expenseAfterSalesId));
        Assert.notNull(expenseAfterSalesId, "采购费用售后id不可为空");

        // 当前售后单的退款总额
        ExpenseAfterSalesEntity data = expenseAfterSalesMapper.selectByPrimaryKey(expenseAfterSalesId);
        if (Objects.isNull(data)) {
            throw new ServiceException("未根据此采购售后单id" + expenseAfterSalesId + "查询到信息");
        }
        ExpenseAfterSalesStatusEntity expenseAfterSalesStatusEntity = expenseAfterSalesStatusMapper.selectByExpenseAfterSalesId(expenseAfterSalesId);

        List<ExpenseAfterSalesDto> expenseAfterSales = this.getExpenseAfterSalesBybuyorderExpenseId(data.getBuyorderExpenseId());
        List<ExpenseAfterSalesDto> collect = expenseAfterSales.stream().filter(c -> Objects.equals(c.getExpenseAfterSalesStatusDto().getAfterSalesStatus(), 2)).collect(Collectors.toList());

        // 售后已退金额
        BigDecimal afterSalesMoney = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(collect)) {
            afterSalesMoney = collect.stream().map(c -> getRealReturnAmount(c.getExpenseAfterSalesId())).reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        // 已付含账期
        BigDecimal expensePayMoney = capitalBillApiService.getExpensePayMoney(data.getBuyorderExpenseId());
        // 待还账期
        BigDecimal expenseDebtPeriodMoney = capitalBillApiService.getExpenseDebtPeriodMoney(data.getBuyorderExpenseId());


        // 当前应付实际已付订单
        BigDecimal thisOrderexpenseRefundMoney = getRealReturnAmount(expenseAfterSalesId);


        return ExpenseAfterSalesPaymentDto.builder()
                .paymentAmount(expensePayMoney.subtract(afterSalesMoney).subtract(expenseDebtPeriodMoney))
                .returnAmount(data.getTotalAmount())
                .repaymentPeriod(expenseAfterSalesStatusEntity.getRepaymentPeriod())
                .needReturnAmount(expenseAfterSalesStatusEntity.getNeedReturnAmount())
                .haveReturnAmount(thisOrderexpenseRefundMoney)
                .refundStatus(expenseAfterSalesStatusEntity.getRefundStatus())
                .refundMethod(data.getRefundMethod())
                .build();
    }

    /**
     * 费用单 售后 订单维度的退款金额 totalAmount
     *
     * @param expenseAfterSalesId
     * @return
     */
    private BigDecimal getExpenseRefundMoney(Long expenseAfterSalesId) {
        ExpenseAfterSalesEntity expenseAfterSalesEntity = expenseAfterSalesMapper.selectByPrimaryKey(expenseAfterSalesId);
        BigDecimal result = BigDecimal.ZERO;
        if (Objects.nonNull(expenseAfterSalesEntity)) {
            result = expenseAfterSalesEntity.getTotalAmount();
        }
        return result;
    }

    /**
     * 获取订单的实际退款金额 退款 + 偿还账期
     *
     * @param expenseAfterSalesId 售后单id
     * @return
     */
    private BigDecimal getExpenseRealRefundMoney(Long expenseAfterSalesId) {
        ExpenseAfterSalesEntity expenseAfterSalesEntity = expenseAfterSalesMapper.selectByPrimaryKey(expenseAfterSalesId);
        ExpenseAfterSalesStatusEntity expenseAfterSalesStatusEntity = expenseAfterSalesStatusMapper.selectByExpenseAfterSalesId(expenseAfterSalesId);
        if (Objects.isNull(expenseAfterSalesEntity) || Objects.isNull(expenseAfterSalesStatusEntity)) {
            throw new ServiceException("根据次采购费用售后单expenseAfterSalesId：" + expenseAfterSalesId + "查询不到信息");
        }
        // 应退+偿还
        // 查询所有流水
        BigDecimal expenseRepaymentPeriod = getExpenseRepaymentPeriod(expenseAfterSalesId);
        BigDecimal realReturnAmount = getRealReturnAmount(expenseAfterSalesId);
        BigDecimal result = expenseRepaymentPeriod.add(realReturnAmount);
        log.info("getExpenseRealRefundMoney 费用售后单：{}，实际退款 + 偿还账期：{}", JSON.toJSONString(expenseAfterSalesId), JSON.toJSONString(result));
        return result;
    }

    /**
     * 偿还账期金额
     *
     * @param expenseAfterSalesId 售后单id
     * @return
     */
    private BigDecimal getExpenseRepaymentPeriod(Long expenseAfterSalesId) {
        CapitalBillDetailDto build = CapitalBillDetailDto.builder()
                .relatedId(Math.toIntExact(expenseAfterSalesId))
                .orderType(CapitalBillOrderTypeEnum.EXPENSE_AFTER_SALE_ORDER.getCode())
                .bussinessType(CapitalBillBussinessTypeEnum.CREDIT_PAYMENT.getCode())
                .build();
        List<CapitalBillDto> capitalBillData = capitalBillApiService.getCapitalBillData(build, null);
        // 账期还款
        BigDecimal result = capitalBillData.stream().map(CapitalBillDto::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("getExpenseRepaymentPeriod 费用售后单：{}，偿还账期金额：{}", JSON.toJSONString(expenseAfterSalesId), JSON.toJSONString(result));
        return result;
    }

    /**
     * 实际退款 退到余额 和 退到账户 返回的值都是正数
     *
     * @param expenseAfterSalesId 售后单id
     */
    private BigDecimal getRealReturnAmount(Long expenseAfterSalesId) {

        ExpenseAfterSalesEntity expenseAfterSalesEntity = expenseAfterSalesMapper.selectByPrimaryKey(expenseAfterSalesId);
        BigDecimal result = BigDecimal.ZERO;
        // 公司账户
        if (expenseAfterSalesEntity.getRefundMethod().equals(ExpenseAfterSalesConstant.COMPANY_ACCOUNT)) {
            CapitalBillDetailDto build = CapitalBillDetailDto.builder()
                    .relatedId(Math.toIntExact(expenseAfterSalesId))
                    .orderType(CapitalBillOrderTypeEnum.EXPENSE_AFTER_SALE_ORDER.getCode())
                    .bussinessType(CapitalBillBussinessTypeEnum.ORDER_REFUND.getCode())
                    // 退还银行 兼容手动录入的各种退款
                    .build();
            List<CapitalBillDto> capitalBillData = capitalBillApiService.getCapitalBillData(build, null);
            // 银行
            result = capitalBillData.stream().map(CapitalBillDto::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add).abs();
        } else {
            CapitalBillDetailDto build = CapitalBillDetailDto.builder()
                    .relatedId(Math.toIntExact(expenseAfterSalesId))
                    .orderType(CapitalBillOrderTypeEnum.EXPENSE_AFTER_SALE_ORDER.getCode())
                    .bussinessType(CapitalBillBussinessTypeEnum.ORDER_REFUND.getCode())
                    // 退还余额
                    .traderMode(530)
                    .build();
            List<CapitalBillDto> capitalBillData = capitalBillApiService.getCapitalBillData(build, null);
            // 公司余额
            result = capitalBillData.stream().map(c -> c.getAmount().abs()).reduce(BigDecimal.ZERO, BigDecimal::add).abs();

        }
        log.info("getRealReturnAmount 费用售后单：{}，实际退款：{}", JSON.toJSONString(expenseAfterSalesId), JSON.toJSONString(result));
        return result;
    }

    @Override
    public List<CapitalBillDto> getExpenseAfterSalesCapitalBill(Long expenseAfterSalesId) {
        CapitalBillDetailDto capitalBillDetailDto = new CapitalBillDetailDto();
        capitalBillDetailDto.setOrderType(5);
        capitalBillDetailDto.setRelatedId(Convert.toInt(expenseAfterSalesId));
        List<CapitalBillDto> capitalBillData = capitalBillApiService.getCapitalBillData(capitalBillDetailDto, null);
        capitalBillData.forEach(c -> {
            UserDto userDto = userApiService.getUserById(c.getCreator());
            c.setCreatorName(userDto.getUsername());
        });
        capitalBillData.sort(Comparator.comparingInt(CapitalBillDto::getCapitalBillId));
        return capitalBillData;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void saveExpenseAfterSalesCapitalBill(CapitalBillDto capitalBillDto) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        Long expenseAfterSalesId = Convert.toLong(capitalBillDto.getCapitalBillDetailDto().getRelatedId());
        ExpenseAfterSalesEntity expenseAfterSalesEntity = expenseAfterSalesMapper
                .selectByPrimaryKey(expenseAfterSalesId);
        // 实际已退款
        BigDecimal realReturnAmount = getRealReturnAmount(expenseAfterSalesId);
        // 需退款 expenseAfterSalesStatus.getNeedReturnAmount()
        ExpenseAfterSalesStatusEntity expenseAfterSalesStatus = expenseAfterSalesStatusMapper.selectByExpenseAfterSalesId(expenseAfterSalesId);
        if (expenseAfterSalesStatus.getRefundStatus() == -1) {
            throw new ServiceException("请先执行退款运算");
        }
        if (realReturnAmount.add(capitalBillDto.getAmount()).compareTo(expenseAfterSalesStatus.getNeedReturnAmount()) > 0) {
            throw new ServiceException("交易金额不得大于应退金额");
        }

        // 根据银行流水号添加增加流水记录
        if (capitalBillDto.getBankBillId() != null) {
            // 计算结款金额, 获取流水记录中所有当前银行流水的合计总额
            BankBillDto bankBillDto = bankBillApiService.getBankBillByBankBillId(capitalBillDto.getBankBillId());
            List<CapitalBillDto> capitalBillDtoList = capitalBillApiService.getCapitalBillByBankBillId(capitalBillDto.getBankBillId());
            // 已结款金额
            BigDecimal matchedAmount = BigDecimal.ZERO;
            if (CollUtil.isNotEmpty(capitalBillDtoList)) {
                matchedAmount = capitalBillDtoList.stream().map(CapitalBillDto::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            // 剩余结款金额
            BigDecimal surplusAmount = bankBillDto.getAmt().subtract(matchedAmount);
            if (capitalBillDto.getAmount().compareTo(bankBillDto.getAmt()) > 0) {
                throw new ServiceException("交易金额不允许大于当前流水金额");
            }
            if (capitalBillDto.getAmount().compareTo(surplusAmount) > 0) {
                throw new ServiceException("交易金额不得大于剩余结款金额");
            }
            // 更新银行流水中已结款金额
            bankBillDto.setMatchedAmount(matchedAmount.add(capitalBillDto.getAmount()));
            bankBillApiService.update(bankBillDto);
        }

        capitalBillDto.setCreator(currentUser.getId());
        capitalBillDto.setAddTime(DateUtil.sysTimeMillis());
        capitalBillDto.setCurrencyUnitId(1);
        capitalBillDto.setTraderTime(DateUtil.sysTimeMillis());
        capitalBillDto.setCompanyId(BuyorderExpenseConstant.VEDENG_ID);
        capitalBillDto.setPaymentType(0);
        capitalBillDto.setTraderType(1);
        capitalBillDto.setPayee(BuyorderExpenseConstant.VEDENG);
        capitalBillApiService.insertCapitalBill(capitalBillDto);
        capitalBillDto.setCapitalBillNo(generatorNum(capitalBillDto.getCapitalBillId()));
        capitalBillApiService.updateCapitalBillNo(capitalBillDto);

        OrganizationDto organization = organizationApiService.getOrganizationById(currentUser.getOrgId());
        // 详细表
        capitalBillDto.getCapitalBillDetailDto().setCapitalBillId(capitalBillDto.getCapitalBillId());
        capitalBillDto.getCapitalBillDetailDto().setAmount(capitalBillDto.getAmount());
        //退款
        capitalBillDto.getCapitalBillDetailDto().setBussinessType(531);
        //费用售后
        capitalBillDto.getCapitalBillDetailDto().setOrderType(5);
        capitalBillDto.getCapitalBillDetailDto().setUserId(currentUser.getId());
        capitalBillDto.getCapitalBillDetailDto().setOrgId(currentUser.getOrgId());
        capitalBillDto.getCapitalBillDetailDto().setOrgName(organization.getOrgName());
        capitalBillDto.getCapitalBillDetailDto().setOrderNo(expenseAfterSalesEntity.getExpenseAfterSalesNo());
        capitalBillDto.getCapitalBillDetailDto().setTraderId(expenseAfterSalesEntity.getTraderId());
        //供应商
        capitalBillDto.getCapitalBillDetailDto().setTraderType(2);
        capitalBillApiService.insertCapitalBillDetail(Lists.newArrayList(capitalBillDto.getCapitalBillDetailDto()));

        // 更新售后单退款状态
        this.updatePayStatus(expenseAfterSalesEntity.getExpenseAfterSalesId());
        // 尝试自动关闭
        try {
            this.completeExpenseAfterSales(expenseAfterSalesEntity.getExpenseAfterSalesId());
        } catch (Exception e) {
            log.error("{}尝试自动关闭失败：原因{},{}", expenseAfterSalesEntity.getExpenseAfterSalesNo(), expenseAfterSalesEntity.getExpenseAfterSalesId(), e.getMessage());
        }
    }

    @Override
    public List<BankBillDto> getBankBill(String flowNo) {
        return bankBillApiService.getByTranFlowLike(CustomUtils.like(flowNo));
    }

    @Override
    public void updateReturnInvoiceStatus(ReturnInvoiceWriteBackDto returnInvoiceWriteBackDto) {
        List<ExpenseAfterSalesInvoiceEntity> expenseAfterSalesInvoiceEntities = expenseAfterSalesInvoiceMapper.selectNeedReversal(returnInvoiceWriteBackDto);
        List<Long> collect = expenseAfterSalesInvoiceEntities.stream().map(ExpenseAfterSalesInvoiceEntity::getExpenseAfterSalesInvoiceId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            expenseAfterSalesInvoiceMapper.updateReturnInvoiceStatus(collect);
        }
    }
}