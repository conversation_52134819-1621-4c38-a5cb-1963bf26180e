package com.vedeng.trader.model.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * 账期申请列表item
 */
@Data
public class CustomerBillPeriodApplyItemDto {

    /**
     * 申请ID
     */
    private Long billPeriodApplyId;

    /**
     * 关联的账期ID，如果是新增账期，则为0
     */
    private Long billPeriodId;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 账期申请者
     */
    private Integer creator;

    /**
     * 账期申请时间
     */
    private Long addTime;

    /**
     * 账期类型  @CustomerBillPeriodTypeEnum
     */
    private Integer billPeriodType;

    /**
     * 申请操作类型，1新增，2调整
     */
    private Integer operateType;

    /**
     * 账期原额度
     */
    private BigDecimal beforeApplyAmount;

    /**
     * 账期可用额
     */
    private BigDecimal creditUsableAmount;

    /**
     * 逾期次数
     */
    private Integer countOfOverDue;

    /**
     * 逾期金额
     */
    private BigDecimal unReturnedOverDueAmount;

    /**
     * 之前的账期有效期开始时间
     */
    private Long beforeBillPeriodStart;

    /**
     * 之前的账期有效期截止时间
     */
    private Long beforeBillPeriodEnd;

    /**
     * 账期申请额度（非调整额度，无正负值）
     */
    private BigDecimal applyAmount;

    /**
     * 账期结算周期
     */
    private Integer settlementPeriod;


    /**
     * 账期有效期开始时间
     */
    private Long billPeriodStart;

    /**
     * 账期有效期截止时间
     */
    private Long billPeriodEnd;

    /**
     * 账期申请审核状态，0审核中，1审核通过，2审核不通过
     */
    private Integer checkStatus;

    /**
     * 账期结算标准
     */
    private Integer settlementType;
}
