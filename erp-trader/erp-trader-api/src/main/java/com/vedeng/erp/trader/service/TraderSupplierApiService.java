package com.vedeng.erp.trader.service;

import com.vedeng.common.core.base.Page;
import com.vedeng.erp.kingdee.dto.KingDeeSupplierDto;
import com.vedeng.erp.system.dto.LikeTraderDto;
import com.vedeng.erp.trader.dto.TraderContactDto;
import com.vedeng.erp.trader.dto.TraderSupplierDto;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2022年08月24日 11:14:00
 */
public interface TraderSupplierApiService {
    /**
     * @description: 获取供应商联系人
     * @return:
     * @author: Strange
     * @date: 2022/8/24
     **/
    List<TraderContactDto> getSupplierContact(Integer traderId);

    /**
     * 查询时间范围内的审核通过的供应商id
     * @param begin 开始时间
     * @param end 结束时间
     * @return List<TraderSupplierDto>
     */
    List<TraderSupplierDto> selectPushKingDeeTraderSupplierData(Long begin, Long end,Integer limit);

    /**
     * 审核通过的供应商
     * @param ids 供应商id集合
     * @return List<TraderSupplierDto>
     */
    List<TraderSupplierDto> selectPushKingDeeTraderSupplierDataByIds(List<Integer> ids);

    /**
     * 更新供应商余额
     * @param traderId
     * @return
     */
    Boolean updateSupplierAmount(Integer traderId, BigDecimal amount);

    /**
     * 根据交易者id获取供应商
     * @param traderId traderId
     * @return
     */
    TraderSupplierDto getTraderSupplierByTraderId(Integer traderId);

    /**
     * 增加供应商余额
     *
     * @param traderId 交易者ID
     * @param amount 金额
     */
    void increaseSupplierBalance(Integer traderId, BigDecimal amount);

    /**
     * 查询并组装金蝶供应商信息
     *
     * @param traderSupplierId 供应商id
     * @return KingDeeSupplierDto
     */
    KingDeeSupplierDto getKingDeeSupplierInfo(Integer traderSupplierId);

    /**
     * 根据交易者id获取供应商
     * @param name 供应商名称
     * @return
     */
    public List<TraderSupplierDto> getTraderSupplierByTraderName(String name, int start,int limit);

    long countTraderSupplierByTraderName(String keyword);

    List<LikeTraderDto> findByTraderTypeAndTraderName(String keyword);
}
