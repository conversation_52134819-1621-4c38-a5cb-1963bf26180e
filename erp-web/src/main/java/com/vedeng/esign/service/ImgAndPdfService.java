package com.vedeng.esign.service;

import com.vedeng.system.model.Attachment;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/12/18 13:57
 **/
public interface ImgAndPdfService {

    /**
     * 更新盖章后的信息
     *
     * @param firstEngageId 首营id
     * @param relatedId 附件关联id 兼容工厂和首营
     * @param userId 用户id
     * @param oldB 老数据
     * @param fileUrl 文件类型及地址
     * @param isManufacturer 区分厂商和首营产品
     */
    void updateBCertificates(Integer firstEngageId, Integer relatedId, Integer userId, List<Attachment> oldB, Map<String, String> fileUrl, boolean isManufacturer);

    /**
     * 图片转pdf
     * @param sources 附件信息
     * @param target 文件名
     * @return String pdf url
     */
    String convertImg2Pdf(List<Attachment> sources, String target);

}
