package com.vedeng.erp.aftersale.facade.impl;

import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.authorization.model.User;
import com.vedeng.erp.aftersale.facade.BuyOrderAfterSalesDetailFacade;
import com.vedeng.erp.buyorder.service.NewBuyOrderButtonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.ModelAndView;

/**
 * 订单详情门面层
 */
@Service
public class BuyOrderAfterSalesDetailFacadeImpl implements BuyOrderAfterSalesDetailFacade {

    @Autowired
    NewBuyOrderButtonService newBuyOrderButtonService;

    @Override
    public void getBuyOrderDetail(ModelAndView mav, User currentUser, AfterSalesVo afterSalesVo) {
        newBuyOrderButtonService.buttonStatusInit(mav, currentUser, afterSalesVo);
    }
}
