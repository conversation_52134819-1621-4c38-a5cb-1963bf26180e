<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.finance.mapper.RInvoiceJInvoiceMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.RInvoiceJInvoiceEntity">
    <!--@mbg.generated-->
    <!--@Table T_R_INVOICE_J_INVOICE-->
    <id column="R_INVOICE_J_INVOICE_ID" jdbcType="INTEGER" property="rInvoiceJInvoiceId" />
    <result column="INVOICE_ID" jdbcType="INTEGER" property="invoiceId" />
    <result column="RELATE_INVOICE_ID" jdbcType="INTEGER" property="relateInvoiceId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    R_INVOICE_J_INVOICE_ID, INVOICE_ID, RELATE_INVOICE_ID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_R_INVOICE_J_INVOICE
    where R_INVOICE_J_INVOICE_ID = #{rInvoiceJInvoiceId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_R_INVOICE_J_INVOICE
    where R_INVOICE_J_INVOICE_ID = #{rInvoiceJInvoiceId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="R_INVOICE_J_INVOICE_ID" keyProperty="rInvoiceJInvoiceId" parameterType="com.vedeng.erp.finance.domain.entity.RInvoiceJInvoiceEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_INVOICE_J_INVOICE (INVOICE_ID, RELATE_INVOICE_ID)
    values (#{invoiceId,jdbcType=INTEGER}, #{relateInvoiceId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="R_INVOICE_J_INVOICE_ID" keyProperty="rInvoiceJInvoiceId" parameterType="com.vedeng.erp.finance.domain.entity.RInvoiceJInvoiceEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_INVOICE_J_INVOICE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceId != null">
        INVOICE_ID,
      </if>
      <if test="relateInvoiceId != null">
        RELATE_INVOICE_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="invoiceId != null">
        #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="relateInvoiceId != null">
        #{relateInvoiceId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.finance.domain.entity.RInvoiceJInvoiceEntity">
    <!--@mbg.generated-->
    update T_R_INVOICE_J_INVOICE
    <set>
      <if test="invoiceId != null">
        INVOICE_ID = #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="relateInvoiceId != null">
        RELATE_INVOICE_ID = #{relateInvoiceId,jdbcType=INTEGER},
      </if>
    </set>
    where R_INVOICE_J_INVOICE_ID = #{rInvoiceJInvoiceId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.finance.domain.entity.RInvoiceJInvoiceEntity">
    <!--@mbg.generated-->
    update T_R_INVOICE_J_INVOICE
    set INVOICE_ID = #{invoiceId,jdbcType=INTEGER},
      RELATE_INVOICE_ID = #{relateInvoiceId,jdbcType=INTEGER}
    where R_INVOICE_J_INVOICE_ID = #{rInvoiceJInvoiceId,jdbcType=INTEGER}
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_R_INVOICE_J_INVOICE
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="INVOICE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceId != null">
            when R_INVOICE_J_INVOICE_ID = #{item.rInvoiceJInvoiceId,jdbcType=INTEGER} then #{item.invoiceId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="RELATE_INVOICE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.relateInvoiceId != null">
            when R_INVOICE_J_INVOICE_ID = #{item.rInvoiceJInvoiceId,jdbcType=INTEGER} then #{item.relateInvoiceId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
    </trim>
    where R_INVOICE_J_INVOICE_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.rInvoiceJInvoiceId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="R_INVOICE_J_INVOICE_ID" keyProperty="rInvoiceJInvoiceId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_INVOICE_J_INVOICE
    (INVOICE_ID, RELATE_INVOICE_ID)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.invoiceId,jdbcType=INTEGER}, #{item.relateInvoiceId,jdbcType=INTEGER})
    </foreach>
  </insert>
</mapper>