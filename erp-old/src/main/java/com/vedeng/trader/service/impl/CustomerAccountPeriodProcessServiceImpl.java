package com.vedeng.trader.service.impl;

import cn.hutool.core.util.StrUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.vedeng.activiti.ProcessInstanceContext;
import com.vedeng.activiti.ProcessSupport;
import com.vedeng.activiti.constants.ProcessConstants;
import com.vedeng.authorization.model.User;
import com.vedeng.common.annotation.MethodLock;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.util.DateUtil;
import com.vedeng.customerbillperiod.constant.CustomerBillPeriodApplyCheckStatusEnum;
import com.vedeng.customerbillperiod.constant.CustomerBillPeriodApplyTypeEnum;
import com.vedeng.customerbillperiod.constant.CustomerBillPeriodTypeEnum;
import com.vedeng.customerbillperiod.dto.CustomerBillPeriodApplyDto;
import com.vedeng.customerbillperiod.dto.CustomerBillPeriodApplyModifyDto;
import com.vedeng.customerbillperiod.exception.CustomerBillPeriodException;
import com.vedeng.customerbillperiod.model.CustomerBillPeriod;
import com.vedeng.customerbillperiod.model.CustomerBillPeriodApply;
import com.vedeng.customerbillperiod.service.CustomerBillPeriodApplyService;
import com.vedeng.customerbillperiod.service.CustomerBillPeriodService;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.system.service.UserService;
import com.vedeng.system.service.VerifiesRecordService;
import com.vedeng.trader.dao.TraderCustomerMapper;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.model.vo.CustomerBillPeriodApplyVo;
import com.vedeng.trader.service.CustomerAccountPeriodProcessService;
import com.vedeng.trader.service.TraderCustomerService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.task.Comment;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Service
public class CustomerAccountPeriodProcessServiceImpl extends ProcessSupport implements CustomerAccountPeriodProcessService {

    private static final String COMMENT_TYPE = "审核人修改";

    private final static Comparator<BigDecimal> AMOUNT_COMPARATOR = Comparator.comparing(amount -> Optional.ofNullable(amount).orElse(BigDecimal.ZERO));

    @Resource
    private TraderCustomerService traderCustomerService;
    @Resource
    private UserService userService;
    @Resource
    private CustomerBillPeriodApplyService customerBillPeriodApplyService;
    @Resource
    private SaleorderService saleorderService;
    @Resource
    private CustomerBillPeriodService customerBillPeriodService;

    @Resource
    private TraderCustomerMapper traderCustomerMapper;

    @Autowired
    public CustomerAccountPeriodProcessServiceImpl(ProcessEngine processEngine, VerifiesRecordService verifiesRecordService) {
        super(processEngine, verifiesRecordService);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @MethodLock(className = CustomerBillPeriodApplyVo.class, field = "traderId", time = 10000)
    public void saveOrUpdateAccountPeriodApply(User operator, CustomerBillPeriodApplyVo customerBillPeriodApplyVo)
            throws CustomerBillPeriodException {

        //pre check
        ensureApplyInfoValidated(customerBillPeriodApplyVo.getCustomerId(), operator, customerBillPeriodApplyVo.isNew());

        CustomerBillPeriodApplyDto customerBillPeriodApplyDto = new CustomerBillPeriodApplyDto();
        BeanUtils.copyProperties(customerBillPeriodApplyVo, customerBillPeriodApplyDto);
        if (customerBillPeriodApplyVo.getStartTime() != null) {
            customerBillPeriodApplyDto.setBillPeriodStart(DateUtil.convertLong(customerBillPeriodApplyVo.getStartTime(), "yyyy-MM-dd"));
        }
        if (customerBillPeriodApplyVo.getEndTime() != null) {
            String billPeriodEnd = customerBillPeriodApplyVo.getEndTime() + " 23:59:59.999";
            customerBillPeriodApplyDto.setBillPeriodEnd(DateUtil.convertLong(billPeriodEnd, "yyyy-MM-dd HH:mm:ss.SSS"));
        }
        if (CustomerBillPeriodTypeEnum.ORDER.getCode().equals(customerBillPeriodApplyVo.getBillPeriodType())) {
            Saleorder saleorder = saleorderService.getBySaleOrderNo(customerBillPeriodApplyVo.getSaleorderNo());
            if (saleorder == null) {
                throw new CustomerBillPeriodException("查无订单号,请检查输入的订单号是否准确");
            }
            customerBillPeriodApplyDto.setRelatedOrderId(saleorder.getSaleorderId());
            customerBillPeriodApplyDto.setBillPeriodStart(DateUtil.getNowDayMillisecond());
            customerBillPeriodApplyDto.setBillPeriodEnd(253370736000000l);
        }
        customerBillPeriodApplyDto.setCreator(operator.getUserId());

        if (CustomerBillPeriodApplyTypeEnum.ADD.getCode().equals(customerBillPeriodApplyVo.getOperateType())) {
            Long applyId = customerBillPeriodApplyService.saveCustomerBillPeriodApply(customerBillPeriodApplyDto);

            // 新增客户申请时，发起账期审核请求
            this.doStartProcessInstance(applyId);
        } else if (CustomerBillPeriodApplyTypeEnum.MODIFY.getCode().equals(customerBillPeriodApplyVo.getOperateType())) {
            if (customerBillPeriodApplyVo.getBillPeriodApplyId() != null && customerBillPeriodApplyVo.getBillPeriodApplyId() != 0) {
                //修改客户账期信息时追加备注 (if any)
                if (CommonConstants.ON.equals(customerBillPeriodApplyVo.getIsVerifyChange())) {
                    this.commentWhenModifyingApply(customerBillPeriodApplyVo.getBillPeriodApplyId(),
                            customerBillPeriodApplyVo.getSettlementPeriod(),
                            customerBillPeriodApplyVo.getApplyAmount());
                }

                //修改客户账期申请（额度、结算周期信息）
                CustomerBillPeriodApplyModifyDto customerBillPeriodApplyModifyDto = new CustomerBillPeriodApplyModifyDto();
                customerBillPeriodApplyModifyDto.setBillPeriodApplyId(customerBillPeriodApplyVo.getBillPeriodApplyId());
                customerBillPeriodApplyModifyDto.setApplyAmount(customerBillPeriodApplyVo.getApplyAmount());
                customerBillPeriodApplyModifyDto.setSettlementPeriod(customerBillPeriodApplyVo.getSettlementPeriod());
                customerBillPeriodApplyModifyDto.setUpdater(operator.getUserId());
                customerBillPeriodApplyService.modifyCustomerBillPeriodApply(customerBillPeriodApplyModifyDto);
            } else {
                if (customerBillPeriodApplyDto.getBillPeriodStart() == null || customerBillPeriodApplyDto.getBillPeriodEnd() == null) {
                    CustomerBillPeriod customerBillPeriod = customerBillPeriodService.selectByprimaryKey(customerBillPeriodApplyDto.getBillPeriodId());
                    customerBillPeriodApplyDto.setBillPeriodStart(customerBillPeriod.getBillPeriodStart());
                    customerBillPeriodApplyDto.setBillPeriodEnd(customerBillPeriod.getBillPeriodEnd());
                }

                Long applyId = customerBillPeriodApplyService.saveCustomerBillPeriodApply(customerBillPeriodApplyDto);

                // 修改原审批通过客户申请时，发起账期审核请求
                this.doStartProcessInstance(applyId);
            }

        } else {
            throw new UnsupportedOperationException("暂不支持此操作，type: " + customerBillPeriodApplyVo.getOperateType());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void completeTask(String taskId, Integer operatorId, String comment, Boolean pass) {
        Objects.requireNonNull(taskId, "taskId is null");
        Objects.requireNonNull(pass, "pass is null");

        User assignee = checkCurrentAssignee(operatorId);

        Map<String, Object> variables = Maps.newHashMapWithExpectedSize(3);
        variables.put("pass", pass);
        variables.put("updater", operatorId);
        if (Boolean.FALSE.equals(pass)) {
            variables.put("value", 2);
        }

        final Task currentTask = getTaskByTaskId(taskId);
        if (currentTask == null) {
            throw new IllegalStateException("Task '" + taskId + "' is not exist");
        }

        List<String> comments = new LinkedList<>();
        if (StringUtils.isNotEmpty(comment)) {
            comments.add(comment);
        }

        StringJoiner commentJoiner = new StringJoiner(ErpConst.Symbol.COMMA);
        List<Comment> commentList = getTaskService().getTaskComments(currentTask.getId(), COMMENT_TYPE);
        for (Comment commentEntry : commentList) {
            String fullMessage = commentEntry.getFullMessage();
            if (!Strings.isNullOrEmpty(fullMessage)) {
                int i = fullMessage.indexOf("||");
                if (i != -1 && i + 2 < fullMessage.length()) {
                    fullMessage = commentEntry.getFullMessage().substring(i + 2);
                }
                commentJoiner.add(fullMessage);
            }

            getTaskService().deleteComment(commentEntry.getId());
        }

        if (commentJoiner.length() > 0) {
            comments.add(commentJoiner.toString());
        }

        String commentToUse = String.join("||", comments);

        int checkStatus = pass ? ProcessConstants.CheckStatus.CHECKING.getStatus() : ProcessConstants.CheckStatus.REJECTED.getStatus();
        doCompleteTask(currentTask, assignee.getUsername(), commentToUse, variables, checkStatus);
    }


    private void ensureApplyInfoValidated(Long customerId, User operator, boolean saveOpt) {
        Objects.requireNonNull(customerId, "assignId is null");
        Objects.requireNonNull(operator, "operator is null");

        checkCurrentAssignee(operator.getUserId());

        checkCustomerIfExist(customerId);

        if (saveOpt) {
            if (CollectionUtils.isEmpty(operator.getPositions())) {
                throw new IllegalStateException("当前操作人没有职位信息");
            }
        }
    }


    private User checkCurrentAssignee(Integer assigneeId) {
        Objects.requireNonNull(assigneeId, "assignId is null");

        User assignee = userService.getUserById(assigneeId);
        if (assignee == null || CommonConstants.ON.equals(assignee.getIsDisabled())) {

            logger.info("保存客户账期时操作人不存在或已被禁用 -  userId: {}", Optional.ofNullable(assignee)
                    .orElseGet(User::new).getUserId());

            throw new CustomerBillPeriodException("操作人不存在或已被禁用");
        }

        return assignee;
    }


    private Trader checkCustomerIfExist(Long customerId) {
        Objects.requireNonNull(customerId, "customerId is null");

        Trader customer = traderCustomerService.getTraderByCustomerId(customerId.intValue());
        if (customer == null || !CommonConstants.ENABLE.equals(customer.getIsEnable())) {

            logger.info("保存客户账期时客户不存在或已被禁用，customerID: {}", customerId);

            throw new IllegalArgumentException("客户不存在或已被禁用");
        }

        return customer;
    }

    private void doStartProcessInstance(final Long applyId) {
        CustomerBillPeriodApply applyInfo = checkAndGetAccountPeriodApplyInfo(applyId);

        Trader customerBaseInfoQuery = checkCustomerIfExist(applyInfo.getCustomerId());

        User assignee = checkCurrentAssignee(applyInfo.getCreator());

        ProcessInstanceContext processInstanceContext = new ProcessInstanceContext();
        processInstanceContext.setProcessDefinitionKey(getProcessDefinitionPrefixKey());
        processInstanceContext.setAssignee(assignee.getUsername());

        processInstanceContext.set(ProcessInstanceContext.ATTR_NAME_ASSIGNEE_ORG_ID, assignee.getOrgId());

        boolean flag = StrUtil.isNotBlank(assignee.getOrgName()) && assignee.getOrgName().contains("营销中心应急业务部") && Objects.equals(applyInfo.getBillPeriodType(), 3);
        processInstanceContext.set(ProcessInstanceContext.IS_YJ_PERIOD, flag ? 1 : 0);
        processInstanceContext.setRelatedId(applyInfo.getBillPeriodApplyId().intValue());
        processInstanceContext.set(ProcessInstanceContext.ATTR_NAME_RELATED_TABLE_NAME, "T_CUSTOMER_BILL_PERIOD_APPLY");
        processInstanceContext.set(ProcessInstanceContext.ATTR_NAME_ACCOUNT_PERIOD_APPLY_AMOUNT, applyInfo.getApplyAmount());

        //账期有效期
        int applyDays = getEffectiveDate(applyInfo.getBillPeriodStart(), applyInfo.getBillPeriodEnd());
        if (applyDays > 0) {
            processInstanceContext.set(ProcessInstanceContext.ATTR_NAME_ACCOUNT_PERIOD_APPLY_DAYS, applyDays);
        } else {
            throw new IllegalStateException("客户账期申请applyId:[" + applyId + "]有效期信息无效");
        }

        processInstanceContext.set(ProcessInstanceContext.ATTR_NAME_ACCOUNT_PERIOD_APPLY_TYPE, applyInfo.getBillPeriodType());
        // VDERP-10664 查询客户的归属平台
        processInstanceContext.set(ProcessInstanceContext.TRADER_BELONG, traderCustomerMapper.getTraderBelongByCustomerId(applyInfo.getCustomerId().intValue()));
        processInstanceContext.set(ProcessInstanceContext.ATTR_NAME_TRADER_ID, customerBaseInfoQuery.getTraderId());
        processInstanceContext.set(ProcessInstanceContext.ATTR_NAME_CUSTOMER_ID, applyInfo.getCustomerId());
        processInstanceContext.set(ProcessInstanceContext.ATTR_NAME_CUSTOMER_NAME, customerBaseInfoQuery.getTraderName());

        if (processInstanceContext.getBusinessKey() == null) {
            processInstanceContext.setBusinessKey(generateBusinessKey(processInstanceContext.getRelatedId()));
        }

        Objects.requireNonNull(processInstanceContext.getRelatedId(), "relatedId is null");

        //开启流程
        Task task = getTaskByBusinessKey(processInstanceContext.getBusinessKey());
        if (task != null) {
            if (logger.isDebugEnabled()) {
                logger.info("Business Key: {} is already started.", processInstanceContext.getBusinessKey());
            }

            throw new IllegalStateException("Process instance :" + processInstanceContext.getBusinessKey() + " is already started.");
        }

        startProcessInstance(processInstanceContext);

        //默认申请人通过
        Task taskInfo = getTaskByBusinessKey(processInstanceContext.getBusinessKey());
        Map<String, Object> attributes = Collections.singletonMap("verifiesType", ProcessConstants.VERIFY_TYPE_CUSTOMER_ACCOUNT_PERIOD);

        doCompleteTask(taskInfo, processInstanceContext.getAssignee(), applyInfo.getApplyReason(), attributes, ProcessConstants.CheckStatus.CHECKING.getStatus());
    }


    private void commentWhenModifyingApply(Long applyId, Integer toSettlementPeriod, BigDecimal toApplyAmount) {
        Objects.requireNonNull(toSettlementPeriod, "toSettlementPeriod is null");
        Objects.requireNonNull(toApplyAmount, "toApplyAmount is null");

        CustomerBillPeriodApply applyInfo = checkAndGetAccountPeriodApplyInfo(applyId);

        boolean amountChanged = AMOUNT_COMPARATOR.compare(toApplyAmount, applyInfo.getApplyAmount()) != 0;
        boolean periodChanged = !Objects.equals(toSettlementPeriod, applyInfo.getSettlementPeriod());

        if (!amountChanged && !periodChanged) {
            return;
        }

        Map<String, Object> attributes = Maps.newHashMapWithExpectedSize(3);

        StringJoiner comment = new StringJoiner(";");
        if (amountChanged) {
            boolean increased = AMOUNT_COMPARATOR.compare(toApplyAmount, applyInfo.getApplyAmount()) > 0;
            comment.add("额度由" + applyInfo.getApplyAmount() + "调" + (increased ? "增" : "减") + "改为" + toApplyAmount.setScale(2, RoundingMode.UNNECESSARY));

            attributes.put(ProcessInstanceContext.ATTR_NAME_ACCOUNT_PERIOD_APPLY_AMOUNT, toApplyAmount);
        }
        if (periodChanged) {
            comment.add("周期由" + applyInfo.getSettlementPeriod() + "天改为" + toSettlementPeriod + "天");
            attributes.put(ProcessInstanceContext.ATTR_NAME_ACCOUNT_PERIOD_APPLY_DAYS, toSettlementPeriod);
        }

        final String businessKey = generateBusinessKey(applyId);
        Task taskQuery = getTaskByBusinessKey(businessKey);
        if (taskQuery == null) {
            throw new IllegalStateException("流程尚未启动，businessKey: " + businessKey);
        }

        List<Comment> commentList = getTaskService().getTaskComments(taskQuery.getId(), COMMENT_TYPE);
        if (CollectionUtils.isNotEmpty(commentList)) {
            for (Comment commentToDelete : commentList) {
                getTaskService().deleteComment(commentToDelete.getId());
            }
        }

        try {
            getTaskService().setVariables(taskQuery.getId(), attributes);
            getTaskService().addComment(taskQuery.getId(), taskQuery.getProcessInstanceId(), COMMENT_TYPE, String.join("||", StringUtils.SPACE, comment.toString()));
        } catch (Exception e) {
            logger.error("增加评论时发生错误, businessKey: {}, taskId: {}", businessKey, taskQuery.getId(), e);
            throw new IllegalStateException(e);
        }
    }


    private CustomerBillPeriodApply checkAndGetAccountPeriodApplyInfo(Long applyId) {
        Objects.requireNonNull(applyId, "applyId is null");

        CustomerBillPeriodApply applyInfo = customerBillPeriodApplyService.selectByPrimaryKey(applyId);
        if (applyInfo == null || !CustomerBillPeriodApplyCheckStatusEnum.IN_CHECK.getCode().equals(applyInfo.getCheckStatus())) {
            logger.info("查询客户账期申请记录失败，applyId: {}", applyId);
            throw new IllegalArgumentException("查询客户账期申请记录失败");
        }

        Objects.requireNonNull(applyInfo.getCustomerId(), "customerId is null");

        return applyInfo;
    }


    @Override
    public String getProcessDefinitionPrefixKey() {
        return ProcessConstants.APPLY_ACCOUNT_PERIOD_PROC_KEY;
    }


    private static final int MIN_DAYS_WHEN_CHANGED = 1;

    private static int getEffectiveDate(Long fromMillis, Long toMillis) {
        if (fromMillis != null && toMillis != null && fromMillis < toMillis) {
            long diffInMillis = Math.subtractExact(toMillis, fromMillis);
            return Math.max(MIN_DAYS_WHEN_CHANGED, Math.toIntExact(TimeUnit.MILLISECONDS.toDays(diffInMillis)));
        }
        return 0;
    }
}
