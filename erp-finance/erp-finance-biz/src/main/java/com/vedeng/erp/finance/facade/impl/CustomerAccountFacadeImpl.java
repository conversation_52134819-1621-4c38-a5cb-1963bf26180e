package com.vedeng.erp.finance.facade.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.aftersale.dto.AfterSalesDto;
import com.vedeng.erp.aftersale.service.AfterSalesApiService;
import com.vedeng.erp.trader.dto.CustomerAccountAssembleDto;
import com.vedeng.erp.finance.domain.dto.BankAliasDto;
import com.vedeng.erp.finance.domain.dto.BankBillCustomerAccountDto;
import com.vedeng.erp.finance.domain.dto.BankReceiptRecordDto;
import com.vedeng.erp.finance.domain.entity.RPayApplyJBankReceiptEntity;
import com.vedeng.erp.finance.dto.CustomerAccountReqDto;
import com.vedeng.erp.finance.dto.RPayApplyJBankReceiptDto;
import com.vedeng.erp.finance.facade.CustomerAccountFacade;
import com.vedeng.erp.finance.service.BankAliasService;
import com.vedeng.erp.finance.service.BankReceiptRecordService;
import com.vedeng.erp.finance.service.RPayApplyJBankReceiptService;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import com.vedeng.erp.settlement.service.BankBillService;
import com.vedeng.erp.system.dto.BankInfoDto;
import com.vedeng.erp.system.service.BankApiService;
import com.vedeng.erp.trader.api.CustomerAccountApi;
import com.vedeng.erp.trader.dto.CustomerAccountCreateDto;
import com.vedeng.erp.trader.dto.CustomerBankAccountApiDto;
import com.vedeng.erp.trader.dto.CustomerBankAccountQueryReqDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CustomerAccountFacadeImpl implements CustomerAccountFacade {

    @Autowired
    private BankBillService bankBillService;
    @Autowired
    private CustomerAccountApi customerAccountApi;

    @Autowired
    private SaleOrderApiService saleOrderApiService;

    @Autowired
    private AfterSalesApiService afterSalesApiService;

    @Autowired
    private BankAliasService bankAliasService;

    @Autowired
    private RPayApplyJBankReceiptService rPayApplyJBankReceiptService;

    @Autowired
    private BankReceiptRecordService bankReceiptRecordService;

    @Autowired
    private BankApiService bankApiService;

    @Override
    public void tryCreateCustomerAccount(Integer afterSalesId) {
        log.info("创建客户账户开始,afterSalesId:{}", afterSalesId);
        AfterSalesDto afterSalesDto = afterSalesApiService.getAfterSalesById(afterSalesId);
        if (Objects.isNull(afterSalesDto)) {
            return;
        }
        SaleorderInfoDto saleorderInfoDto = saleOrderApiService.getByAfterSaleId(afterSalesId);
        if (Objects.isNull(saleorderInfoDto)) {
            return;
        }

        Integer saleorderId = saleorderInfoDto.getSaleorderId();
        // 查询流水
        List<BankBillCustomerAccountDto> bankBillDto = bankBillService.queryBankBillBySaleOrderId(saleorderId);
        log.info("根据销售单ID获取到流水：{}", JSON.toJSON(bankBillDto));

        // 解析回单获取开户行名称集合
        List<BankBillCustomerAccountDto> receiptUrlDtoList = bankBillDto.stream().filter(e -> Objects.nonNull(e.getReceiptUrl())).collect(Collectors.toList());
        List<String> bankNameList = new ArrayList<>();
        Map<Integer, String> receiptBankNameMap = new HashMap<>();
        Map<Integer, String> receiptBankUrlMap = new HashMap<>();
        for (BankBillCustomerAccountDto bankBillCustomerAccountDto : receiptUrlDtoList) {
            String bankName = this.getBankName(bankBillCustomerAccountDto.getReceiptUrl());
            if (StrUtil.isBlank(bankName)){
                log.info("解析回单获取开户行名称为空");
                continue;
            }
            log.info("解析回单获取开户行名称：{}", bankName);
            bankNameList.add(bankName);
            receiptBankNameMap.put(bankBillCustomerAccountDto.getBankBillId(), bankName);
            receiptBankUrlMap.put(bankBillCustomerAccountDto.getBankBillId(), bankBillCustomerAccountDto.getReceiptUrl());

            BankReceiptRecordDto bankReceiptRecordDto = new BankReceiptRecordDto();
            Integer accountType = getAccountType(bankBillCustomerAccountDto.getBankTag());
            bankReceiptRecordDto.setAccountType( accountType);
            bankReceiptRecordDto.setAccountNo(bankBillCustomerAccountDto.getAccountNo());
            bankReceiptRecordDto.setBizType(0);
            bankReceiptRecordDto.setBizId(saleorderId);
            bankReceiptRecordDto.setBankNameAlias(bankName);
            // 保存解析记录
            bankReceiptRecordService.tryCreateBankReceiptRecord(bankReceiptRecordDto);
        }
        List<BankInfoDto> bankList = bankApiService.queryByBankName(bankNameList);
        Map<String, Integer> bankMap = bankList.stream().collect(Collectors.toMap(BankInfoDto::getBankName, BankInfoDto::getBankId));
        if (CollUtil.isEmpty(bankMap)){
            bankMap = MapUtil.newHashMap();
        }

        // 查询银行别名表
        List<BankAliasDto> bankAlias = bankAliasService.getBankAlias(bankNameList);
        Map<String, Integer> bankAliasMap = bankAlias.stream().collect(Collectors.toMap(BankAliasDto::getAlias, BankAliasDto::getBankId));
        if (CollUtil.isEmpty(bankAliasMap)){
            bankAliasMap = MapUtil.newHashMap();
        }

        for (BankBillCustomerAccountDto bankBillCustomerAccountDto : bankBillDto) {
            Integer bankTag = bankBillCustomerAccountDto.getBankTag();
            // 解析回单获取名称
            String bankName = receiptBankNameMap.get(bankBillCustomerAccountDto.getBankBillId());
            log.info("map获取到回单bankName:{}", bankName);

            CustomerAccountCreateDto dto = new CustomerAccountCreateDto();
            dto.setTraderId(saleorderInfoDto.getTraderId());
            dto.setAccountType(getAccountType(bankTag));
            dto.setAccountNo(bankBillCustomerAccountDto.getAccountNo());
            dto.setAccountName(bankBillCustomerAccountDto.getAccountName());

            CustomerAccountAssembleDto customerAccountAssembleDto = new CustomerAccountAssembleDto();
            customerAccountAssembleDto.setDto(dto);
            customerAccountAssembleDto.setBankName(bankName);
            customerAccountAssembleDto.setBankId(this.getBankId(bankMap, bankAliasMap, bankName));
            customerAccountAssembleDto.setTranFlow(bankBillCustomerAccountDto.getTranFlow());
            customerAccountAssembleDto.setAfterSalesNo(afterSalesDto.getAfterSalesNo());
            customerAccountAssembleDto.setReceiptUrl(receiptBankUrlMap.get(bankBillCustomerAccountDto.getBankBillId()));
            // 创建客户账户
            customerAccountApi.create(customerAccountAssembleDto);
        }

    }

    private Integer getBankId(Map<String, Integer> bankMap, Map<String, Integer> bankAliasMap, String bankName){
        if(StrUtil.isBlank(bankName)){
            log.info("未解析到开户行名称");
            return null;
        }
        Integer bankIdByName = bankMap.get(bankName);
        if (Objects.nonNull(bankIdByName)){
            log.info("根据银行表直接找到银行ID：{}", bankIdByName);
            return bankIdByName;
        }
        Integer bankIdByAliasName = bankAliasMap.get(bankName);
        if (Objects.nonNull(bankIdByAliasName)){
            log.info("根据开户行别名表直接找到银行ID：{}", bankIdByAliasName);
            return bankIdByAliasName;
        }
        log.info("根据银行表和开户行表均未找到银行ID,{}",bankName);
        return null;
    }

    @Override
    public void updateLastUseTime(Long customerBankAccountId,CustomerBankAccountQueryReqDto queryReqDto) {
        if (Objects.isNull(customerBankAccountId)) {
            CustomerBankAccountQueryReqDto reqDto = new CustomerBankAccountQueryReqDto();
            reqDto.setTraderId(queryReqDto.getTraderId());
            reqDto.setAccountNo(queryReqDto.getAccountNo());
            reqDto.setAccountType(queryReqDto.getAccountType());
            List<CustomerBankAccountApiDto> query = customerAccountApi.query(reqDto);
            if (CollUtil.isEmpty(query)) {
                return;
            }
            CustomerBankAccountApiDto customerBankAccountApiDto = query.get(0);
            customerBankAccountId = customerBankAccountApiDto.getCustomerBankAccountId();
        }

        customerAccountApi.updateLastUseTime(customerBankAccountId);
    }


    @Override
    public List<CustomerBankAccountApiDto> getCustomerBankAccount(CustomerAccountReqDto customerAccountReqDto) {
        SaleorderInfoDto saleorderInfoDto = saleOrderApiService.getByAfterSaleId(customerAccountReqDto.getAfterSalesId());
        List<CustomerBankAccountApiDto> customerBankAccountApiDtoList = customerAccountApi
                .findByTraderIdAndAccountType(saleorderInfoDto.getTraderId(), customerAccountReqDto.getAccountType());
        if (CollUtil.isNotEmpty(customerBankAccountApiDtoList)) {
            customerBankAccountApiDtoList.forEach(customerBankAccountApiDto -> {
                // 关联回单银行
                List<BankReceiptRecordDto> byAccountNoAndAccountType = bankReceiptRecordService
                        .findByAccountNoAndAccountType(customerBankAccountApiDto.getAccountNo(), customerAccountReqDto.getAccountType());
                if (CollUtil.isNotEmpty(byAccountNoAndAccountType)) {
                    customerBankAccountApiDto.setReceiptBankName(CollUtil.getFirst(byAccountNoAndAccountType).getBankNameAlias());
                    customerBankAccountApiDto.setBankReceiptAliasId(CollUtil.getFirst(byAccountNoAndAccountType).getBankReceiptAliasId());
                }

                // 查询流水
                List<BankBillCustomerAccountDto> bankBillDto = bankBillService.queryBankBillBySaleOrderId(saleorderInfoDto.getSaleorderId());
                log.info("根据销售单ID获取到流水：{}", JSON.toJSON(bankBillDto));
                // 标签
                int tag = 1;
                if (CollUtil.isNotEmpty(bankBillDto)) {
                    List<BankBillCustomerAccountDto> collect = bankBillDto.stream().filter(b -> b.getAccountNo().equals(customerBankAccountApiDto.getAccountNo())).collect(Collectors.toList());
                    //直接收款账号=0：关联销售订单客户使用的付款账号
                    if (CollUtil.isNotEmpty(collect)) {
                        tag = 0;
                    }
                }
                customerBankAccountApiDto.setTag(tag);

                // 获取银行名称
                if (Objects.nonNull(customerBankAccountApiDto.getBankId())) {
                    BankInfoDto byBankId = bankApiService.findByBankId(customerBankAccountApiDto.getBankId());
                    if (Objects.nonNull(byBankId)) {
                        customerBankAccountApiDto.setBankName(byBankId.getBankName());
                        customerBankAccountApiDto.setBankCode(byBankId.getBankNo());
                    } else {
                        customerBankAccountApiDto.setBankId(null);
                    }
                }
            });
        }
        // 排序规则：先按照直接收款账号倒序,在基于最近使用时间倒序
        customerBankAccountApiDtoList.sort(
                Comparator.comparing(CustomerBankAccountApiDto::getTag)
                        .thenComparing(CustomerBankAccountApiDto::getLastUseTime, Comparator.reverseOrder())
        );
        return customerBankAccountApiDtoList;
    }

    @Override
    public void tryCreateRelation(RPayApplyJBankReceiptDto rPayApplyJBankReceiptDto) {
        log.info("付款申请回单关联表:{}", JSON.toJSON(rPayApplyJBankReceiptDto));
        List<RPayApplyJBankReceiptEntity> byPayApplyId = rPayApplyJBankReceiptService.findByPayApplyId(rPayApplyJBankReceiptDto.getPayApplyId());
        if (CollUtil.isNotEmpty(byPayApplyId)) {
            log.info("已经存在关系，直接返回");
            return;
        }
        rPayApplyJBankReceiptService.insertSelective(rPayApplyJBankReceiptDto);
    }

    @Override
    public void tryCreateBankAlias(Integer payApplyId, Integer bankId) {
        log.info("付款申请回单关联表:payApplyId:{},bankId:{}", payApplyId,bankId);
        // 查询关联表
        List<RPayApplyJBankReceiptEntity> receiptEntities = rPayApplyJBankReceiptService.findByPayApplyId(payApplyId);
        if (CollUtil.isEmpty(receiptEntities)) {
            log.info("付款申请回单关联表为空，直接返回");
            return;
        }
        RPayApplyJBankReceiptEntity rPayApplyJBankReceiptEntity = receiptEntities.get(0);
        Long bankReceiptAliasId = rPayApplyJBankReceiptEntity.getBankReceiptAliasId();
        // 查询回单解析表
        BankReceiptRecordDto bankReceiptRecordDto = bankReceiptRecordService.getById(bankReceiptAliasId);
        if (Objects.isNull(bankReceiptRecordDto)) {
            log.info("回单解析表为空，直接返回");
            return;
        }
        String bankNameAlias = bankReceiptRecordDto.getBankNameAlias();
        if (StrUtil.isBlank(bankNameAlias)) {
            log.info("回单解析表bankNameAlias为空，直接返回");
            return;
        }
        List<String> receiptBankNameList = Arrays.asList(bankNameAlias.split(","));

        bankAliasService.createOrUpdateBankAlias(receiptBankNameList, rPayApplyJBankReceiptEntity.getBankId());
    }

    /**
     * 获取账户类型 1银行 2微信 3支付宝
     *
     * @param bankTag
     * @return
     */
    private Integer getAccountType(Integer bankTag) {
        // bankTag： 银行标示：1建设银行 2南京银行 3中国银行4支付宝5微信6交通银行7民生银行
        switch (bankTag) {
            case 1: // 1建设银行
            case 2: // 2南京银行
            case 3: // 3中国银行
            case 6: // 6交通银行
            case 7: // 7民生银行
                return 1;
            case 5: // 5微信
                return 2;
            case 4: // 4支付宝
                return 3;
            default:
                return 0;
        }
    }

//    public static void main(String[] args) {
//        String url = "https://file.vedeng.com/file/display?resourceId=609ec3edd57d48bca50f51f7ca47728a";
//        System.out.println(getBankName(url));
//    }

    private String getBankName(String url) {
        try {
            String text = getPdfText(url);
            if (StrUtil.isBlank(text)) {
                return null;
            }
            //List<String> aList = splitTextByLineSeparator(text);
            return getBankNameByText(text);
        } catch (Exception e) {
            log.error("getBankName error");
        }
        return null;
    }

    private String getPdfText(String url) {
        try (InputStream inputStream = getInputStreamFromUrl(url)) {
            PDDocument document = PDDocument.load(inputStream);
            PDFTextStripper tStripper = new PDFTextStripper();
            tStripper.setSortByPosition(false);
            String info = tStripper.getText(document);
            return info;
        } catch (IOException e) {
            log.error("getPdfText error");
        } catch (Exception e) {
            log.error("getPdfText error", e);
        }
        return null;
    }


    public String getBankNameByText(String text){
        String keyword1 = "开户行";
        String keyword2 = "开户行";
        return extractTextBetweenKeywords(text,keyword1,keyword2);
    }

    public String extractTextBetweenKeywords(String text, String keyword1, String keyword2) {
        int startIndex = text.indexOf(keyword1) + keyword1.length();
        int endIndex = text.indexOf(keyword2, startIndex);

        if (startIndex == -1 || endIndex == -1) {
            return "";
        }
        return text.substring(startIndex, endIndex).trim().replace("\n","").replace("\r","");
    }

    private InputStream getInputStreamFromUrl(String fileUrl) throws IOException {
        URL url = new URL(fileUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.connect();

        // 检查响应码
        if (connection.getResponseCode() != HttpURLConnection.HTTP_OK) {
            throw new IOException("Failed to connect: " + connection.getResponseCode());
        }

        // 返回输入流
        return connection.getInputStream();
    }

}
