package com.vedeng.crm.business.quote.facade.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.crm.business.quote.domain.dto.CrmQuoteOrderDto;
import com.vedeng.crm.business.quote.domain.entity.QuoteorderNeedsEntity;
import com.vedeng.crm.business.quote.facade.QuoteNeedsFacade;
import com.vedeng.crm.business.quote.service.CrmQuoteOrderService;
import com.vedeng.crm.business.quote.service.QuoteOrderNeedsService;
import com.vedeng.crm.business.quote.service.RQuoteNeedsJGoodsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class QuoteNeedsFacadeImpl implements QuoteNeedsFacade {

    @Autowired
    private QuoteOrderNeedsService quoteOrderNeedsService;

    @Autowired
    private CrmQuoteOrderService crmQuoteOrderService;

    @Autowired
    private RQuoteNeedsJGoodsService rQuoteNeedsJGoodsService;

    @Override
    public void cleanNeeds(Integer businessChanceId) {
        log.info("清除报价需求，businessChanceId:{}",businessChanceId);
        CrmQuoteOrderDto crmQuoteOrderDto = crmQuoteOrderService.selectByBusinessChanceId(businessChanceId);
        if (Objects.isNull(crmQuoteOrderDto)){
            log.info("暂无报价");
            return;
        }
        List<QuoteorderNeedsEntity> quoteorderNeedsEntities = quoteOrderNeedsService.selectByQuoteorderId(crmQuoteOrderDto.getQuoteorderId());
        log.info("删除报价需求：{}", JSON.toJSON(quoteorderNeedsEntities));
        quoteOrderNeedsService.deleteByQuoteorderNeedsId(quoteorderNeedsEntities);
        log.info("删除报价需求和商品关系");
        rQuoteNeedsJGoodsService.batchDeleteByQuoteorderNeedsId(quoteorderNeedsEntities);
    }
}
