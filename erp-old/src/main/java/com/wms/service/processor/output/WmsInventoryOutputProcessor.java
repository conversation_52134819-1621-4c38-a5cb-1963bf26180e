package com.wms.service.processor.output;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.util.DateUtil;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.constant.WmsLogicalOperateTypeEnum;
import com.wms.dao.WmsOutputOrderGoodsMapper;
import com.wms.dao.WmsOutputOrderMapper;
import com.wms.dto.OutputDto;
import com.wms.dto.OutputGoodDto;
import com.wms.dto.StockCalculateDto;
import com.wms.model.dto.WmsOutputOrderGoodsDto;
import com.wms.model.po.WmsOutputOrder;
import com.wms.model.po.WmsOutputOrderGoods;
import com.wms.service.WmsInventoryOutService;
import com.wms.service.context.ThreadLocalContext;
import com.wms.service.processor.AbstractOutputOrderProcessor;
import com.wms.service.stockcalculate.SaleorderOutCaculateImpl;
import com.wms.service.util.WmsCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 盘亏出库wms回传
 */
@Service
@Slf4j
public class WmsInventoryOutputProcessor extends AbstractOutputOrderProcessor {

    @Autowired
    @Qualifier("saleorderOutCaculateImpl")
    private SaleorderOutCaculateImpl stockinfoCaculateInterface;

    @Autowired
    private WmsInventoryOutService wmsInventoryOutService;

    @Autowired
    private WmsOutputOrderMapper outputOrderMapper;

    @Autowired
    private WmsOutputOrderGoodsMapper outputOrderGoodsMapper;


    @Override
    protected void commonValidator(OutputDto requestBean) throws Exception {
        //  hollis wms 接口标识
        if(WmsInterfaceOrderType.INVENTORY_OUT.equals(requestBean.getOrderType())){
            ThreadLocalContext.put("operateType",StockOperateTypeConst.INVENTORY_WAREHOUSE_OUT);
        }
    }

    @Override
    protected void updateOrderData(OutputDto requestBean) throws Exception {

        String orderNo = WmsCommonUtil.getOriginalOrderNo(requestBean.getSOReference1());
        WmsOutputOrder wmsOutputOrder = wmsInventoryOutService.getInventoryOutByOrderNo(orderNo);

        Assert.notNull(wmsOutputOrder, "wms盘亏出库单回传未查此单：" + orderNo);

        Map<Integer, WmsOutputOrderGoods> wmsOutputOrderGoodsMap = new HashMap<>();
        List<WmsOutputOrderGoodsDto> wmsOutputOrderGoodsList = wmsInventoryOutService.getWmsInputOrderGoodsDto(wmsOutputOrder.getId());
        for (WmsOutputOrderGoods wmsOutputOrderGood : wmsOutputOrderGoodsList) {
            wmsOutputOrderGoodsMap.put(wmsOutputOrderGood.getId().intValue(),wmsOutputOrderGood);
        }

        List<OutputGoodDto> details = requestBean.getDetails();
        String currentTime = DateUtil.convertString(System.currentTimeMillis(),DateUtil.TIME_FORMAT);
        Map<Integer,Integer> outNumMap = new HashMap<>();
        for (OutputGoodDto detail : details) {
            int detailId = getRelateId(detail);
            Integer outNum = outNumMap.get(detailId) == null ? 0 : outNumMap.get(detailId);
            outNumMap.put(detailId,outNum + detail.getQtyShipped().intValue());
        }

        // 匹配 数据，更新出库数据量，出库状态，累加
        for (Integer detailId : outNumMap.keySet()) {
            WmsOutputOrderGoods wmsOutputOrderGoods = wmsOutputOrderGoodsMap.get(detailId);
            Assert.notNull(wmsOutputOrderGoods, "wms盘亏出库单:" + orderNo + "回传未查到此商品明细:" + detailId);
            Integer outNum = outNumMap.get(detailId);
            WmsOutputOrderGoods update = new WmsOutputOrderGoods();
            update.setId(wmsOutputOrderGoods.getId());
            update.setAlreadyOutputNum(wmsOutputOrderGoods.getAlreadyOutputNum() + outNum);
            update.setLastOutputTime(currentTime);
            update.setOutStatus(wmsOutputOrderGoods.getOutputNum() <= update.getAlreadyOutputNum() ? 2 : 1);
            update.setUpdateTime(currentTime);
            log.info("盘亏出库单：{}明细更新：id:{},data:{}",orderNo,wmsOutputOrderGoods.getId(), JSON.toJSONString(update));
            outputOrderGoodsMapper.updateByPrimaryKeySelective(update);
        }


        List<WmsOutputOrderGoodsDto> wmsOutputOrderGoodsList1 = wmsInventoryOutService.getWmsInputOrderGoodsDto(wmsOutputOrder.getId());
        WmsOutputOrder updateOrder = new WmsOutputOrder();
        updateOrder.setOutStatus(2);
        updateOrder.setId(wmsOutputOrder.getId());
        updateOrder.setRealOutputTime(currentTime);
        updateOrder.setUpdateTime(currentTime);
        for (WmsOutputOrderGoods wmsOutputOrderGoods : wmsOutputOrderGoodsList1) {
            if(!wmsOutputOrderGoods.getOutStatus().equals(2)){
                updateOrder.setOutStatus(1);
                break;
            }
        }
        log.info("盘亏出库单：{}更新：data:{}",orderNo,JSON.toJSONString(updateOrder));
        outputOrderMapper.updateByPrimaryKeySelective(updateOrder);
    }

    @Override
    protected int getOperateType(OutputDto requestBean) {
        return StockOperateTypeConst.INVENTORY_WAREHOUSE_OUT;
    }

    @Override
    protected int getWmsLogicalOperateType() {
        return WmsLogicalOperateTypeEnum.INVENTORY_OUT.getOperateTypeCode();
    }

    /**
     * todo hollis 待确认
     * @param outputGoodDto data
     * @return int
     */
    @Override
    protected int getRelateId(OutputGoodDto outputGoodDto) {
        return Integer.parseInt(outputGoodDto.getUserDefine1());
    }

    @Override
    protected List<WarehouseDto> getWarehouseStockList(List<StockCalculateDto> stockCalculateList) {
        return stockinfoCaculateInterface.calculateStockInfo(stockCalculateList);
    }
}
