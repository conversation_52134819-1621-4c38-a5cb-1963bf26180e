package com.wms.service.chain.step;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.util.DateUtil;
import com.wms.constant.VerifyStatusEnum;
import com.wms.constant.WmsLogicalOperateTypeEnum;
import com.wms.dao.WmsLogicalOrdergoodsMapper;
import com.wms.dao.WmsOutputOrderGoodsMapper;
import com.wms.dao.WmsOutputOrderMapper;
import com.wms.dto.StockAllocationRequest;
import com.wms.dto.StockAllocationResult;
import com.wms.model.dto.WmsOutputOrderGoodsDto;
import com.wms.model.po.WmsLogicalOrdergoods;
import com.wms.model.po.WmsOutputOrder;
import com.wms.model.po.WmsOutputOrderGoods;
import com.wms.service.chain.AbstractHandlerStep;
import com.wms.service.chain.HandlerStepContext;
import com.wms.service.stockallocation.StockAllocationStrategy;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 盘亏出库 逻辑单处理
 * <AUTHOR>
 */
@Service
@Slf4j
public class InventoryOutOrderHandler extends AbstractHandlerStep {

    @Resource
    private WmsLogicalOrdergoodsMapper wmsLogicalOrdergoodsMapper;

    @Autowired
    private WmsOutputOrderGoodsMapper wmsOutputOrderGoodsMapper;


    @Override
    protected void doDealWith(HandlerStepContext context) throws Exception {

        Long inventoryOutOrderId = context.get("inventoryOutOrderId");

        // 新增逻辑订单数据
        List<WmsLogicalOrdergoods> wmsLogicalOrdergoodsList = new ArrayList<>();
        List<WmsOutputOrderGoodsDto> wmsOutputOrderGoods = wmsOutputOrderGoodsMapper.queryOutputGoodsByWmsOutPutOrderId(inventoryOutOrderId);


        CurrentUser currentUser = CurrentUser.getCurrentUser();
        for (WmsOutputOrderGoods wmsOutputOrderGood : wmsOutputOrderGoods) {
            WmsLogicalOrdergoods insert = new WmsLogicalOrdergoods();
            insert.setNum(wmsOutputOrderGood.getOutputNum());
            insert.setRelatedId(wmsOutputOrderGood.getId().intValue());
            insert.setOccupyNum(wmsOutputOrderGood.getOutputNum());
            insert.setSku(wmsOutputOrderGood.getSkuNo());
            insert.setGoodsId(Integer.valueOf(wmsOutputOrderGood.getSkuNo().substring(1)));
            insert.setLogicalWarehouseId(wmsOutputOrderGood.getLogicalWarehouseId());
            insert.setOperateType(WmsLogicalOperateTypeEnum.INVENTORY_OUT.getOperateTypeCode());
            insert.setCreator(currentUser.getId());
            insert.setUpdater(currentUser.getId());
            log.info("盘亏单:"+inventoryOutOrderId+",新增逻辑订单数据:{}" , JSON.toJSONString(insert));
            wmsLogicalOrdergoodsMapper.insertSelective(insert);
            wmsLogicalOrdergoodsList.add(insert);
        }
        context.put("wmsLogicalOrdergoodsList",wmsLogicalOrdergoodsList);
    }

}
