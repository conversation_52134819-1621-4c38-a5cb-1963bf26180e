package com.vedeng.orderstream.aftersales.model;

public class AfterSalesDirectInfo {
    /**
     * 主键id
     */
    private Integer afterSalesDirectId;

    /**
     * 售后单id
     */
    private Integer afterSalesId;

    /**
     * 售后商品表id
     */
    private Integer afterSalesGoodsId;

    /**
     *0默认1出库2入库
     */
    private Integer type;

    /**
     * 厂家批次号/sn码
     */
    private String factoryCode;

    /**
     * 生产日期
     */
    private Long goodCreateTime;

    /**
     * 有效期至
     */
    private Long goodVaildTime;

    /**
     * 出/入库数量
     */
    private Integer num;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 修改时间
     */
    private Long modeTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 修改人
     */
    private Integer updater;

    /**
     * 是否删除
     */
    private Integer isDelete;

    private String goodCreateTimeStr;

    private String goodVaildTimeStr;

    public String getGoodCreateTimeStr() {
        return goodCreateTimeStr;
    }

    public void setGoodCreateTimeStr(String goodCreateTimeStr) {
        this.goodCreateTimeStr = goodCreateTimeStr;
    }

    public String getGoodVaildTimeStr() {
        return goodVaildTimeStr;
    }

    public void setGoodVaildTimeStr(String goodVaildTimeStr) {
        this.goodVaildTimeStr = goodVaildTimeStr;
    }

    public Integer getAfterSalesDirectId() {
        return afterSalesDirectId;
    }

    public void setAfterSalesDirectId(Integer afterSalesDirectId) {
        this.afterSalesDirectId = afterSalesDirectId;
    }

    public Integer getAfterSalesId() {
        return afterSalesId;
    }

    public void setAfterSalesId(Integer afterSalesId) {
        this.afterSalesId = afterSalesId;
    }

    public Integer getAfterSalesGoodsId() {
        return afterSalesGoodsId;
    }

    public void setAfterSalesGoodsId(Integer afterSalesGoodsId) {
        this.afterSalesGoodsId = afterSalesGoodsId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getFactoryCode() {
        return factoryCode;
    }

    public void setFactoryCode(String factoryCode) {
        this.factoryCode = factoryCode;
    }

    public Long getGoodCreateTime() {
        return goodCreateTime;
    }

    public void setGoodCreateTime(Long goodCreateTime) {
        this.goodCreateTime = goodCreateTime;
    }

    public Long getGoodVaildTime() {
        return goodVaildTime;
    }

    public void setGoodVaildTime(Long goodVaildTime) {
        this.goodVaildTime = goodVaildTime;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    public Long getModeTime() {
        return modeTime;
    }

    public void setModeTime(Long modeTime) {
        this.modeTime = modeTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }
}