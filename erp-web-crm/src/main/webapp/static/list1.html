<!DOCTYPE html>

<html>

<head>
    <title>CRM-商机</title>
    <link rel="stylesheet" href="./css/common/common.css">
    <link rel="stylesheet" href="./ui/ui.css">
    <link rel="stylesheet" href="./css/pages/businessChance.css">
</head>

<body>
    <input type="hidden" id="isHiddenLayout" value="0">
    <div class="page-wrap" id="page-container">
        <page-header></page-header>
        <div class="page-container">
            <page-side></page-side>
            <div class="page-main">
                <div class="list-title">商机</div>
                <div class="list-top-option">
                    <ui-button type="primary" icon="icon-add">新建</ui-button>
                </div>
                <ui-search-container ref="listContainer" list-name="01" :default-tab="defaultTab" :headers="tableHeaders" url="http://172.16.2.59:7777/crm/businessChance/profile/page" :search-params="searchParams" :left-fixed-number="1" @edit="tableItemEdit" @scroll="listScroll">
                    <template v-slot:filter-list>
                        <ui-search-item label="商机编号">
                            <ui-input v-model="searchParams.bussinessChanceNo"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="商机类型">
                            <ui-select dictionary="5700" multiple-type="fixed" placeholder="全部" v-model="searchParams.businessTypeList" clearable></ui-select>
                        </ui-search-item>
                        <ui-search-item label="商机等级">
                            <ui-select dictionary="938" multiple-type="fixed" placeholder="全部" v-model="searchParams.systemBusinessLevelList" clearable ></ui-select>
                        </ui-search-item>
                        <ui-search-item label="商机阶段">
                            <ui-select :data="stageList"  multiple-type="fixed" placeholder="全部" v-model="searchParams.stageList" clearable></ui-select>
                        </ui-search-item>
                        <ui-search-item label="最近跟进日期">
                            <ui-date-picker v-model="searchParams.latestFollowUpTimeStart" placeholder="请选择日期"></ui-date-picker>
                            <!-- latestFollowUpTimeEnd -->
                        </ui-search-item>
                        <ui-search-item label="任务截至日期">
                            <ui-date-picker v-model="searchParams.deadlineDateStart" placeholder="请选择日期"></ui-date-picker>
                            <!-- deadlineDateEnd -->
                        </ui-search-item>
                        <ui-search-item label="任务处理">
                            <ui-select placeholder="全部" :data="doneStatusList" v-model="searchParams.doneStatus" clearable></ui-select>
                        </ui-search-item>
                        <ui-search-item label="创建时间">
                            <ui-date-picker v-model="searchParams.addTimeStart" placeholder="请选择日期"></ui-date-picker>
                            <!-- addTimeEnd -->
                        </ui-search-item>
                        <ui-search-item label="归属销售">
                            <ui-select :remote="true" :avatar="true" multiple-type="fixed" :default-multi="searchParams.belongerMultiItems" placeholder="全部" v-model="searchParams.belongerIdList" clearable :remote-info="belongerListRemoteInfo" @change="remoteSelectChange('belongerMultiItems', $event)"></ui-select>
                        </ui-search-item>
                        <ui-search-item label="协作人">
                            <ui-select :remote="true" :avatar="true" multiple-type="fixed" :default-multi="searchParams.collaboratorMultiItems" placeholder="全部" v-model="searchParams.collaboratorIdList" clearable :remote-info="shareUserListRemoteInfo" @change="remoteSelectChange('collaboratorMultiItems', $event)"></ui-select>
                        </ui-search-item>
                        <ui-search-item label="我关注的">
                            <ui-select placeholder="全部" :data="attentionStateList" v-model="searchParams.attentionState" clearable></ui-select>
                        </ui-search-item>
                        <ui-search-item label="预计成单金额">
                            <ui-range-input :min.sync="searchParams.amountMin" :max.sync="searchParams.amountMax" placeholder="单位：万元"></ui-range-input>
                        </ui-search-item>
                        <ui-search-item label="预计成单日期">
                            <ui-date-picker v-model="searchParams.orderTimeStart" placeholder="请选择日期"></ui-date-picker>
                            <!-- orderTimeEnd -->
                        </ui-search-item>
                        <ui-search-item label="客户名称">
                            <ui-input v-model="searchParams.traderName"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="联系人">
                            <ui-input v-model="searchParams.traderContactName"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="手机">
                            <ui-input v-model="searchParams.mobile"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="固话">
                            <ui-input v-model="searchParams.telephone"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="产品需求">
                            <ui-input v-model="searchParams.productCommentsSale"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="备注">
                            <ui-input v-model="searchParams.comments"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="标签">
                            <ui-select :remote="true" multiple-type="fixed" :default-multi="searchParams.tagsMultiItems" placeholder="全部" v-model="searchParams.tagIdList" clearable :remote-info="tagsRemoteInfo" @change="remoteSelectChange('tagsMultiItems', $event)"></ui-select>
                        </ui-search-item>
                        <ui-search-item label="终端名称">
                            <ui-input v-model="searchParams.terminalTraderName"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="终端性质">
                            <ui-select dictionary="5600" multiple-type="fixed" placeholder="全部" v-model="searchParams.terminalTraderNatureList" clearable ></ui-select>
                        </ui-search-item>
                        <ui-search-item label="终端区域">
                            <ui-select :data="selectList1"></ui-select>
                        </ui-search-item>
                        <ui-search-item label="客情关系">
                            <ui-select placeholder="全部" :data="customerRelationshipList" multiple-type="fixed" v-model="searchParams.customerRelationshipList" clearable></ui-select>
                        </ui-search-item>
                        <ui-search-item label="采购方式">
                            <ui-select dictionary="404" placeholder="全部" v-model="searchParams.purchasingType" clearable ></ui-select>
                        </ui-search-item>
                        <ui-search-item label="招标阶段">
                            <ui-select dictionary="5800" multiple-type="fixed" placeholder="全部" v-model="searchParams.biddingPhaseList" clearable ></ui-select>
                        </ui-search-item>
                        <ui-search-item label="招标参数">
                            <ui-select placeholder="全部" :data="biddingParameterList" v-model="searchParams.biddingParameter" clearable></ui-select>
                        </ui-search-item>
                        <ui-search-item label="商机来源">
                            <ui-select dictionary="390" placeholder="全部" v-model="searchParams.type" clearable ></ui-select>
                        </ui-search-item>
                        <ui-search-item label="线索编号">
                            <ui-input v-model="searchParams.leadsNo"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="订货号">
                            <ui-input v-model="searchParams.skuNo"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="产品名称">
                            <ui-input v-model="searchParams.productName"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="品牌">
                            <ui-input v-model="searchParams.brandName"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="型号">
                            <ui-input v-model="searchParams.modelNumber"></ui-input>
                        </ui-search-item>
                    </template>
                    <template v-slot:list-button>
                        <ui-button @click="multiAttention">关注</ui-button>
                    </template>
                    <template v-slot:bussinessChanceNo="{ row }">
                        <div class="td-link" @click="GLOBAL.link(row.bussinessChanceNo)">{{row.bussinessChanceNo}}</div>
                    </template>
                    <template v-slot:latestCommunicateRecordContent="{ row }">
                        <div class="record-wrap" v-if="row.latestCommunicateRecordContent" :title="row.latestCommunicateRecordContent">
                            <i class="vd-ui_icon icon-sms"></i>
                            <div class="record-txt text-line-1 " >{{ row.latestCommunicateRecordContent || '-' }}</div>
                        </div>
                        <template v-else>-</template>
                    </template>
                    <template v-slot:latestTaskContent="{ row }">
                        <div class="record-wrap" v-if="row.latestTaskContent" :title="row.latestTaskContent">
                            <i class="vd-ui_icon icon-list"></i>
                            <div class="record-txt text-line-1 " >{{ row.latestTaskContent || '-' }}</div>
                        </div>
                        <template v-else>-</template>
                    </template>
                    <template v-slot:customerRelationship="{ row }">
                        <div class="text-line-1">{{ getCustomerRelationshipStr(row.customerRelationship) }}</div>
                    </template>
                    <template v-slot:biddingParameter="{ row }">
                        <div class="text-line-1">{{ {1:'可调整', 2:'不可调整'}[row.biddingParameter] }}</div>
                    </template>
                    <template v-slot:tags="{ row }">
                        <div class="text-line-1" :title="getTagsStr(row)">{{ getTagsStr(row) }}</div>
                    </template>
                    <template v-slot:leadsNo="{ row }">
                        <div class="td-link" @click="GLOBAL.link(row.businessLeadsId)">{{row.leadsNo}}</div>
                    </template>
                    <template v-slot:option="{ row }">
                        <div class="option-wrap">
                            <a class="table-edit" @click="attentionItem(row, 1)" v-if="row.attentionState == 0">关注</a>
                            <a class="table-edit" @click="attentionItem(row, 0)" v-if="row.attentionState == 1">取消关注</a>
                        </div>
                    </template>
                </ui-search-container>
            </div>
        </div>
        
        <ui-poper :show="isShowEditWrapper" position="middle" :el="tableEditEl" ref="editDropwrap">
            <div class="table-edit-wrap">
                <div class="edit-content">
                    <template v-if="editKey === 'amount'">
                        <ui-input v-model="edit_amount"></ui-input>
                    </template>
                    <template v-if="editKey === 'traderContactName'">
                        <ui-input v-model="edit_traderContactName"></ui-input>
                    </template>
                    <template v-if="editKey === 'productCommentsSale'">
                        <ui-input v-model="edit_productCommentsSale"></ui-input>
                    </template>
                    <template v-if="editKey === 'comments'">
                        <ui-input v-model="edit_comments"></ui-input>
                    </template>
                    <template v-if="editKey === 'terminalTraderName'">
                        <ui-input v-model="edit_terminalTraderName"></ui-input>
                    </template>
                    <template v-if="editKey === 'orderTime'">
                        <ui-date-picker v-model="edit_orderTime" placeholder="请选择日期"></ui-date-picker>
                    </template>
                    <template v-if="editKey === 'tags'">
                        <ui-select :remote="true" multiple-type="fixed" :default-multi="edit_tagsSelectedItems" placeholder="请选择" v-model="edit_tagIds" clearable :remote-info="tagsRemoteInfo"></ui-select>
                    </template>
                    <template v-if="editKey === 'terminalTraderNatureStr'">
                        <ui-select dictionary="5600" placeholder="全部" v-model="edit_terminalTraderNature" clearable ></ui-select>
                    </template>
                    <template v-if="editKey === 'amount'">
                        <ui-input v-model="edit_amount"></ui-input>
                    </template>
                </div>
                <div class="edit-option">
                    <ui-button type="primary" icon="icon-selected2" @click="submitEdit"></ui-button>
                    <ui-button icon="icon-delete" @click="cancelEdit"></ui-button>
                </div>
            </div>
        </ui-poper>
    </div>
    <script src="./js/common/vue.js"></script>
    <script src="./js/common/axios.js"></script>
    <script src="./js/common/lodash.min.js"></script>
    <script src="./ui/ui.js"></script>
    <script src="./js/common/layout.js"></script>
    <script src="./js/pages/businesschanceList.js"></script>
</body>

</html>