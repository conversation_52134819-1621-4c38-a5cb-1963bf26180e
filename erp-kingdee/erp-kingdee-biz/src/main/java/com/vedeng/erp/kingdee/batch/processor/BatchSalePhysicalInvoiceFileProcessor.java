package com.vedeng.erp.kingdee.batch.processor;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import com.vedeng.erp.kingdee.batch.handle.InvoiceFileHandle;
import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import com.vedeng.erp.kingdee.dto.KingDeeSalesVatPlainInvoiceDto;
import com.vedeng.erp.kingdee.dto.KingDeeSalesVatSpecialInvoiceDto;
import com.vedeng.erp.kingdee.service.KingDeeFileDataService;
import com.vedeng.erp.kingdee.service.KingDeeSalesVatPlainInvoiceService;
import com.vedeng.erp.kingdee.service.KingDeeSalesVatSpecialInvoiceService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 销售实物发票附件处理（红发票的formId是一样的，所以可以公用，Pdf和Xml公用）
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/1 11:05
 */
@Service
@Slf4j
public class BatchSalePhysicalInvoiceFileProcessor extends BaseProcessor<BatchInvoiceDto, KingDeeFileDataDto> {

    @Autowired
    private KingDeeFileDataService kingDeeFileDataService;

    @Autowired
    private KingDeeSalesVatPlainInvoiceService kingDeeSalesVatPlainInvoiceService;

    @Autowired
    private KingDeeSalesVatSpecialInvoiceService kingDeeSalesVatSpecialInvoiceService;

    @Autowired
    private InvoiceFileHandle invoiceFileHandle;

    @Override
    public KingDeeFileDataDto doProcess(BatchInvoiceDto invoiceDto, JobParameters params, ExecutionContext stepContext) throws Exception {
        // 不需要判断是Pdf还是Xml的reader读出来的附件，sql统一用ossFileUrl接收，只要有附件，就推送
        if (StringUtils.isBlank(invoiceDto.getOssFileUrl())) {
            log.info("当前销售实物发票没有回传附件, invoice:{}", JSON.toJSONString(invoiceDto));
            return null;
        }

        // 专票
        if (invoiceDto.getInvoiceTypeName().contains(InvoiceFileHandle.SPECIAL_INVOICE)) {
            // step 1: 校验对应的发票 有没有推送至金蝶（若发票已经推送至金蝶，则证明满足了所有的校验条件，因此这边不需要再校验蓝字作废、实物or费用等条件了）
            KingDeeSalesVatSpecialInvoiceDto query = new KingDeeSalesVatSpecialInvoiceDto();
            query.setFQzokBddjtid(invoiceDto.getInvoiceId().toString());
            kingDeeSalesVatSpecialInvoiceService.query(query);
            if (query.getFid() == null || InvoiceFileHandle.ZERO.equals(query.getFid())) {
                log.info("当前销售实物发票专票未推送金蝶,无需推送附件：{}", JSON.toJSONString(invoiceDto));
                return null;
            }

            // step2： 校验当前这个附件 有没有推送过（根据本地附件回写表判断）
            KingDeeFileDataDto existPdfParam = KingDeeFileDataDto.builder()
                    .formId(KingDeeFormConstant.SALE_VAT_SPECIAL_INVOICE)
                    .erpId(invoiceDto.getInvoiceId().toString())
                    .url(invoiceDto.getOssFileUrl())
                    .build();
            List<KingDeeFileDataDto> existFile = kingDeeFileDataService.getByBusinessIdAndUri(existPdfParam);
            if (CollectionUtils.isEmpty(existFile)) {
                return invoiceFileHandle.createKingDeeFileDataDto(invoiceDto, KingDeeFormConstant.SALE_VAT_SPECIAL_INVOICE, query.getFid());
            }
        }

        // 普票
        if (invoiceDto.getInvoiceTypeName().contains(InvoiceFileHandle.PLAIN_INVOICE)) {
            // step 1: 校验对应的发票 有没有推送至金蝶（若发票已经推送至金蝶，则证明满足了所有的校验条件，因此这边不需要再校验蓝字作废、实物or费用等条件了）
            KingDeeSalesVatPlainInvoiceDto query = new KingDeeSalesVatPlainInvoiceDto();
            query.setFQzokBddjtid(invoiceDto.getInvoiceId().toString());
            kingDeeSalesVatPlainInvoiceService.query(query);
            if (query.getFid() == null || InvoiceFileHandle.ZERO.equals(query.getFid())) {
                log.info("当前销售实物发票普票未推送金蝶,无需推送附件：{}", JSON.toJSONString(invoiceDto));
                return null;
            }
            // step2： 校验当前这个附件 有没有推送过（根据本地附件回写表判断）
            KingDeeFileDataDto existPdfParam = KingDeeFileDataDto.builder()
                    .formId(KingDeeFormConstant.SALE_VAT_PLAIN_INVOICE)
                    .erpId(invoiceDto.getInvoiceId().toString())
                    .url(invoiceDto.getOssFileUrl())
                    .build();
            List<KingDeeFileDataDto> existFile = kingDeeFileDataService.getByBusinessIdAndUri(existPdfParam);
            if (CollectionUtils.isEmpty(existFile)) {
                return invoiceFileHandle.createKingDeeFileDataDto(invoiceDto, KingDeeFormConstant.SALE_VAT_PLAIN_INVOICE, query.getFid());
            }
        }
        return null;
    }
}