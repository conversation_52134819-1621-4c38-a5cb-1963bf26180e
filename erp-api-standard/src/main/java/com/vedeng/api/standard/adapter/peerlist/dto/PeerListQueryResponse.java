package com.vedeng.api.standard.adapter.peerlist.dto;

import com.vedeng.erp.buyorder.domain.vo.PurchaseDeliveryDirectBatchInfoVo;
import lombok.Data;

import java.util.List;

/**
 * 查询直发同行单响应DTO
 */
@Data
public class PeerListQueryResponse {

    /**
     * 响应代码 0-成功，其他-失败
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 同行单明细列表
     */
    private PurchaseDeliveryDirectBatchInfoVo data;
} 
