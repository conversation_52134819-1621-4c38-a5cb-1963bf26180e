let GLOBAL = {
    // 复制
    copyTextToClipboard(text, queryThis, duration=800) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text);
        } else {
            let input = document.createElement('textarea');
            input.value = text;
            document.body.appendChild(input);
            input.select();
            document.execCommand('copy');
            document.body.removeChild(input);
        }

        queryThis.$message({
            message: '复制成功',
            duration: duration
        });
    },
    // 全局加载状态
    showGlobalLoading(font) {
        console.log('font:', font);
        let loadingEle = document.createElement('div');
        loadingEle.setAttribute('class', 'global__loading__wrap');
        loadingEle.setAttribute('id', 'J-global-loading-wrap');
        loadingEle.innerHTML = `<i class="vd-ui_icon icon-loading"></i>`;
        if (font) {
            let fontEle = document.createElement('p');
            fontEle.setAttribute('class', 'global__loading__p');
            fontEle.innerText = font;
            loadingEle.appendChild(fontEle);
        }
        document.body.appendChild(loadingEle);
    },
    hideGlobalLoading() {
        var element = document.getElementById('J-global-loading-wrap');
        if (element && element.parentNode) {
            element.parentNode.removeChild(element);
        }
    },
    //获取路由参数
    getQuery(key) {
        let params = new URLSearchParams(window.location.search);
        return params.get(key) || '';
    },
    defaultAvatar: '/static/image/crm-user-avatar.svg',
    avatarerror(e) {
        console.log(e)
    },
    BUSINESSLEADS_STATUS: {
        0: "待分配",
        1: "待跟进",
        2: "跟进中",
        3: "已关闭",
        4: "已商机"
    },
    BUSINESSCHANCE_STAGE: {
        1: "初步洽谈",
        2: "商机验证",
        3: "初步方案",
        4: "最终方案",
        5: "赢单",
        6: "关闭"
    },
    auth(key) {
        if (!key) {
            return false;
        }

        let auths = document.querySelector('#golbal_permissions') ? document.querySelector('#golbal_permissions').value : '';
        let authList = auths.split(',');

        if (authList.indexOf(key) !== -1) {
            return true;
        } else {
            return false;
        }
    },
    showNoAuth() {
        if (Vue) {
            Vue.prototype.$popup.warn({
                title: '抱歉，当前您没有访问权限',
                message: '权限开通：请企业微信联系研发部Aadi。',
                buttons: [{
                    txt: '我知道了',
                    btnClass: 'confirm',
                }]
            })
        }
    },
    download(url, name) {
        axios.get(url, {
            responseType: 'blob',
        }).then(res => {
            let blob = new Blob([res.data]);
            let objectUrl = URL.createObjectURL(blob) // 创建URL

            let link = document.createElement('a');
            link.href = objectUrl;
            link.download = name;
            document.body.appendChild(link)

            let event = new MouseEvent('click')
            link.dispatchEvent(event)
            setTimeout(() => {
                document.body.removeChild(link)
            }, 500)
        }).catch(() => { })
    },
    wxregister(jsApiList, callback) {
        axios.get('/crm/wx/getWeiXinPermissionsValidationConfig?url=' + encodeURIComponent(window.location.href.split("#")[0]))
            .then(({ data }) => {
                if (data.success) {
                    try {
                        ww.register({
                            agentId: data.data.agentid,
                            corpId: data.data.corpid,
                            jsApiList: jsApiList,

                            // crm企微应用ticket
                            // getAgentConfigSignature() {
                            //     return {
                            //         nonceStr: data.data.nonceStr,
                            //         timestamp: data.data.timestamp,
                            //         signature: data.data.signature
                            //     };
                            // },
                            // async onAgentConfigSuccess(ee) {
                            //     console.log(' --------------- register success --------------- ');
                            // },
                            // onAgentConfigFail(ee) {
                            //     console.log('register Fail', ee);
                            // },

                            // 企微企业ticket信息
                            getConfigSignature() {
                                return {
                                    nonceStr: data.data.nonceStr,
                                    timestamp: data.data.timestamp,
                                    signature: data.data.signature
                                };
                            },
                            onConfigSuccess(ee) {
                                console.log('register success 2 =====>', ee);
                                if (callback) {
                                    callback();
                                }
                            },
                            onConfigFail (ee) {
                                console.log('register Fail 2 =====>', ee);
                            }
                        })

                        ww.checkJsApi({
                            jsApiList: jsApiList,
                            success(res) {
                                console.log('checkJsApi res:', res);
                            },
                            fail(err) {
                                console.log('checkJsApi err:', err);
                            }
                        })
                    } catch (errr) {
                        console.log('errr:', errr);
                    }
                }
            })
    },

    /* 打开企微会话
     * queryThis: 必须   调用处this指向
     * userId     必须   用户id
     * isQwId     非必须 是否是企微id
    */
    openQwChat(queryThis, userId, isQwId) {
        if (!userId) return;
        userId = userId + '';

        if (isQwId) {
            ww.openEnterpriseChat({
                userIds: [userId],
            })
        }
        else {
            axios.get('/crm/visitrecord/m/getUserInfo?userId='+userId)
            .then(({data}) => {
                if (data.success) {
                    let qwId = data.data.number + '';
                    ww.openEnterpriseChat({
                        userIds: [qwId],
                    })
                } else {
                    queryThis.$message({
                        message: data.message,
                        duration: 800
                    });
                }
            }).catch(() => { })
        }
    },
};

window.onload = () => {
    document.querySelector('#page-container').classList.add('show');
}
