<!doctype html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>(销售工作台)未收款/部分收款订单</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
 </head>
 <body id="tzodxBz0gNY" datasource="erp-reportdatasource" fixednumber="2" fixednumberright="0" append_column_url="" append_row_url="" empty_show="" firstcol="">
  <div id="appendHead"></div>
  <div class="layui-fluid">
   <div class="layui-tab">
    <ul class="layui-tab-title" id="tab">
    </ul>
   </div>
   <form class="layui-form" id="search">
    <div class=" layui-inline ">
     <label class="layui-form-label">订单号</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="SALEORDER_NO" placeholder="请输入" style="" alias="s" jdbctype="" data="" datatype="" oper="LIKE" ></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">收款状态</label>
     <div class="layui-input-inline">
      <xm class=" layui-input list-search-item " type="select" name="PAYMENT_STATUS" placeholder="" style="" alias="s" jdbctype="" data="[{&quot;V&quot;:&quot;未收款&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分收款&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" oper="IN" ></xm> 
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">归属销售</label>
     <div class="layui-input-inline"><xm class=" layui-input list-search-item " type="select" name="CURRENT_USER_ID" placeholder="默认全部" style="" alias="sd" jdbctype="" data="${session.EZ_SESSION_MY_USER_MAP_KEY}" datatype="JSON" oper="IN" ></xm> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">直发</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="21" name="DELIVERY_DIRECT" placeholder="" style="" alias="s" jdbctype="" data="[{&quot;V&quot;:&quot;直发&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;普发&quot;,&quot;K&quot;:&quot;0&quot;}]" datatype="JSON" oper="" ></object> 
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">付款计划</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="21" name="PAYMENT_TYPE" placeholder="" style="" alias="s" jdbctype="" data="[{&quot;V&quot;:&quot;全账期&quot;,&quot;K&quot;:&quot;423&quot;}]" datatype="JSON" oper="EQ" ></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">合并日期搜索</label>
     <div class="layui-input-inline">
      <search class=" layui-input list-search-item " type="date" name="ADD_TIME,VALID_TIME,PAYMENT_TIME,DELIVERY_TIME,INVOICE_TIME,ARRIVAL_TIME" placeholder="" style="" alias="" jdbctype="" data="" datatype="" oper="" ></search> 
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">收货时间</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="hidden" name="ARRIVAL_TIME" placeholder="" style="" alias="FROM_UNIXTIME( IF ( a.ARRIVAL_TIME= 0,         NULL,         a.ARRIVAL_TIME) / 1000,         '%Y-%m-%d %H:%i:%S' )" jdbctype="DATE" data="" datatype="" oper="BETWEEN" ></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">开票时间</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="hidden" name="INVOICE_TIME" placeholder="" style="" alias="FROM_UNIXTIME( IF ( s.INVOICE_TIME= 0,         NULL,         s.INVOICE_TIME) / 1000,         '%Y-%m-%d %H:%i:%S' )" jdbctype="DATE" data="" datatype="" oper="BETWEEN" ></object> 
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">发货时间</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="hidden" name="DELIVERY_TIME" placeholder="" style="" alias="FROM_UNIXTIME( IF ( s.DELIVERY_TIME= 0,         NULL,         s.DELIVERY_TIME) / 1000,         '%Y-%m-%d %H:%i:%S' )" jdbctype="DATE" data="" datatype="" oper="BETWEEN" ></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">付款时间</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="hidden" name="PAYMENT_TIME" placeholder="" style="" alias="FROM_UNIXTIME( IF ( s.PAYMENT_TIME= 0,         NULL,         s.PAYMENT_TIME) / 1000,         '%Y-%m-%d %H:%i:%S' )" jdbctype="DATE" data="" datatype="" oper="BETWEEN" ></object> 
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">生效时间</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="hidden" name="VALID_TIME" placeholder="" style="" alias="FROM_UNIXTIME( IF ( s.VALID_TIME= 0,         NULL,         s.VALID_TIME) / 1000,         '%Y-%m-%d %H:%i:%S' )" jdbctype="DATE" data="" datatype="" oper="BETWEEN" ></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">创建时间</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="hidden" name="ADD_TIME" placeholder="请选择日期" style="" alias="FROM_UNIXTIME( IF ( s.ADD_TIME= 0,         NULL,         s.ADD_TIME) / 1000,         '%Y-%m-%d %H:%i:%S' )" jdbctype="DATE" data="" datatype="" oper="BETWEEN" ></object> 
     </div>
    </div>
   </form>
   <hr class="layui-border-blue">
   <div class="btn-group   bd-highlight" id="tableButton">
   </div>
   <table id="table" class="layui-table" style=" width:100%">
    <thead>
     <tr id="column">
      <th type="rowbutton" id="rowbutton"></th>
      <th item_name="SALEORDER_NO" name="SALEORDER_NO" body="td-link" order="0" url="/orderstream/saleorder/detail.do?saleOrderId=${SALEORDER_ID}&amp;scene=0" opentype="PARENT" windowname="${SALEORDER_NO}" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订单号</th>
      <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-link" order="0" url="/trader/customer/baseinfo.do?traderId=${TRADER_ID}" opentype="PARENT" windowname="${TRADER_NAME}" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">客户名称</th>
      <th item_name="CURRENT_USER_ID" name="CURRENT_USER_ID" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="SELECT A.USER_ID K,A.USERNAME V  FROM T_USER A
where  IS_DISABLED=0  AND COMPANY_ID=1" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">归属销售</th>
      <th item_name="DELIVERY_DIRECT" name="DELIVERY_DIRECT" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;直发&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;普发&quot;,&quot;K&quot;:&quot;0&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">是否直发</th>
      <th item_name="TOTAL_AMOUNT" name="TOTAL_AMOUNT" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订单原金额</th>
      <th item_name="COUPONMONEY" name="COUPONMONEY" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">优惠金额</th>
      <th item_name="REAL_TOTAL_AMOUNT" name="REAL_TOTAL_AMOUNT" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订单实际金额</th>
      <th item_name="REAL_PAY_AMOUNT" name="REAL_PAY_AMOUNT" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">客户实付金额</th>
      <th item_name="VERIFY_STATUS" name="VERIFY_STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;待审核&quot;,&quot;K&quot;:&quot;3&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">审核状态</th>
      <th item_name="VALID_STATUS" name="VALID_STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;未生效&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;已生效&quot;,&quot;K&quot;:&quot;1&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">生效状态</th>
      <th item_name="PAYMENT_STATUS" name="PAYMENT_STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;未收款&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分收款&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部收款&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">收款状态</th>
      <th item_name="CURRENT_ORG_ID" name="CURRENT_ORG_ID" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="select ORG_ID K,ORG_NAME V from T_ORGANIZATION where IS_DELETED = 0" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">销售部门</th>
      <th item_name="ADD_TIME" name="ADD_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">创建时间</th>
      <th item_name="VALID_TIME" name="VALID_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">生效时间</th>
     </tr>
    </thead>
    <tbody>
     <tr>
      <td> express:</td>
      <td colspan="100"><pre id="express" class="layui-code" orderby="order by s.SALEORDER_ID desc" groupby="">StringBuilder sql=new StringBuilder("
SELECT
	  s.SALEORDER_ID,
	s.SALEORDER_NO,
        s.TRADER_ID,
	s.TRADER_NAME,
	sd.CURRENT_USER_ID,
	s.DELIVERY_DIRECT,
	s.TOTAL_AMOUNT,
	s.COUPONMONEY,
	 COALESCE ( s.REAL_TOTAL_AMOUNT, 0.00 ) REAL_TOTAL_AMOUNT,
         COALESCE ( s.REAL_PAY_AMOUNT, 0.00 ) REAL_PAY_AMOUNT,
	IFNULL(v.STATUS,'3') VERIFY_STATUS,
	s.VALID_STATUS,
	s.PAYMENT_STATUS,
	sd.CURRENT_ORG_ID,
        FROM_UNIXTIME( IF ( s.ADD_TIME= 0, NULL, s.ADD_TIME) / 1000, '%Y-%m-%d %H:%i:%s' ) AS ADD_TIME,
        FROM_UNIXTIME( IF ( s.VALID_TIME= 0, NULL, s.VALID_TIME) / 1000, '%Y-%m-%d %H:%i:%s' ) AS VALID_TIME
FROM
	T_SALEORDER s
	LEFT JOIN T_SALEORDER_DATA sd ON s.SALEORDER_ID = sd.SALEORDER_ID 
	LEFT JOIN T_VERIFIES_INFO v ON s.SALEORDER_ID = v.RELATE_TABLE_KEY AND v.RELATE_TABLE = 'T_SALEORDER'  AND v.VERIFIES_TYPE = 623
WHERE
	s.COMPANY_ID = 1 AND  s.STATUS != 3 and s.VALID_STATUS = 1   AND s.ORDER_TYPE not in (2,3,4,6) and s.PAYMENT_STATUS in (0,1)
");


if ( isNotBlank("EZ_SESSION_MY_USER_KEY","session")) {
sql.append ( " AND  sd.CURRENT_USER_ID  in (" );
sql.append ( $$("EZ_SESSION_MY_USER_KEY") );
sql.append (  ") " );
}
list=search(sql);
return list;</pre></td>
     </tr>
     <tr>
      <td> count:</td>
      <td colspan="100"><pre id="count" class="layui-code"></pre></td>
     </tr>
    </tbody>
   </table>
  </div>
  <div id="appendFoot"></div>
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
  <script>
    layui.use(function () {

    })
</script>
 </body>
</html>