package com.vedeng.erp.trader.web.api;

import cn.hutool.core.date.DateUtil;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.BaseController;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.erp.trader.dto.RCommunicateTodoJAiDto;
import com.vedeng.erp.trader.service.CommunicateRecordService;
import com.vedeng.erp.trader.service.RCommunicateTodoJAiService;
import com.vedeng.erp.trader.service.VoiceFieldResultService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/18 15:26
 */
@ExceptionController
@RestController
@RequestMapping("/communicate")
public class CommunicateInfoApi extends BaseController {


    @Autowired
    CommunicateRecordService communicateRecordService;
    @Autowired
    VoiceFieldResultService voiceFieldResultService;
    @Autowired
    RCommunicateTodoJAiService rCommunicateTodoJAiService;

    /**
     * 获取当日通话记录
     */
    @RequestMapping(value = "/getCommunicateRecordToday")
    @NoNeedAccessAuthorization
    public R<?> getCommunicateRecordToday() {
        Date date = new Date();
        Long minBegintime = DateUtil.beginOfDay(date).getTime();
        Long maxBegintime = DateUtil.endOfDay(date).getTime();
        return R.success(communicateRecordService.findByBegintimeBetween(minBegintime, maxBegintime));
    }


    /**
     * 获取通话记录信息
     */
    @RequestMapping(value = "/getAiInfo")
    @NoNeedAccessAuthorization
    public R<?> getAiInfo(@RequestParam("communicateRecordId") Integer communicateRecordId) {
        // 根据沟通记录获取所有的ai分析结果
        return R.success(voiceFieldResultService.selectByCommunicateRecordId(communicateRecordId));
    }


    /**
     * ai分析处理结果：添加沟通记录
     */
    @RequestMapping(value = "/addCommunicateRecord")
    @NoNeedAccessAuthorization
    public R<?> addCommunicateRecord(@RequestBody RCommunicateTodoJAiDto rCommunicateTodoJAiDto) {
        rCommunicateTodoJAiService.addCommunicateRecord(rCommunicateTodoJAiDto);
        return R.success();
    }


    /**
     * ai分析处理结果：生成商机
     */
    @RequestMapping(value = "/createBusinessChange")
    @NoNeedAccessAuthorization
    public R<?> createBusinessChange(@RequestBody RCommunicateTodoJAiDto rCommunicateTodoJAiDto) {
        rCommunicateTodoJAiService.createBusinessChange(rCommunicateTodoJAiDto);
        return R.success();
    }


    /**
     * ai分析处理结果：无需生成商机
     */
    @RequestMapping(value = "/noCreateBusinessChange")
    @NoNeedAccessAuthorization
    public R<?> noCreateBusinessChange(@RequestBody RCommunicateTodoJAiDto rCommunicateTodoJAiDto) {
        rCommunicateTodoJAiService.noCreateBusinessChange(rCommunicateTodoJAiDto);
        return R.success();
    }


    /**
     * ai分析处理结果：添加修改客户标签
     */
    @RequestMapping(value = "/updateTraderSign")
    @NoNeedAccessAuthorization
    public R<?> updateTraderSign(@RequestBody RCommunicateTodoJAiDto rCommunicateTodoJAiDto) {
        rCommunicateTodoJAiService.updateTraderSign(rCommunicateTodoJAiDto);
        return R.success();
    }


    /**
     * ai分析处理结果：无需添加修改客户标签
     */
    @RequestMapping(value = "/noUpdateTraderSign")
    @NoNeedAccessAuthorization
    public R<?> noUpdateTraderSign(@RequestBody RCommunicateTodoJAiDto rCommunicateTodoJAiDto) {
        rCommunicateTodoJAiService.noUpdateTraderSign(rCommunicateTodoJAiDto);
        return R.success();
    }

    /**
     * ai分析处理结果：同步联系人职位信息
     */
    @RequestMapping(value = "/syncContactPosition")
    @NoNeedAccessAuthorization
    public R<?> syncContactPosition(@RequestBody RCommunicateTodoJAiDto rCommunicateTodoJAiDto) {
        rCommunicateTodoJAiService.syncContactPosition(rCommunicateTodoJAiDto);
        return R.success();
    }

    /**
     * 获取通话记录汇总
     */
    @RequestMapping(value = "/summary")
    @NoNeedAccessAuthorization
    public R<?> summary(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")Date date) {
        CurrentUser user = CurrentUser.getCurrentUser();
        // 根据沟通记录获取所有的ai分析结果
        return R.success(voiceFieldResultService.summary(date,user.getId()));
    }
}