package com.vedeng.erp.activiti.taskassign;

import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.erp.buyorder.bizProcessor.dto.BizDto;
import com.vedeng.erp.buyorder.bizProcessor.core.ext.OverBuyordeReturnAfterSalesCore;
import com.vedeng.erp.buyorder.bizProcessor.manager.OrderProcessorManager;
import com.vedeng.erp.buyorder.bizProcessor.processor.overBuyorderReturnSales.afterProcessor.ext.DealBuyorderForBuyReturnAfterProcessor;
import com.vedeng.erp.buyorder.bizProcessor.processor.overBuyorderReturnSales.afterProcessor.ext.DealSaleorderForBuyReturnAfterProcessor;
import com.vedeng.erp.buyorder.bizProcessor.processor.overBuyorderReturnSales.afterProcessor.ext.DealTicketTaskForBuyReturnProcessor;
import com.vedeng.erp.buyorder.bizProcessor.processor.overBuyorderReturnSales.preProcessor.ext.WMSBASRProcessor;

import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.interceptor.DefaultTransactionAttribute;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * .采购售后完结审核监听器
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2021/11/24 17:48.
 * @author: Randy.Xu.
 */
public class OverBuyorderAfterSalesVerifyExecutionListener implements ExecutionListener {

    private static final Logger logger = LoggerFactory.getLogger(OverBuyorderAfterSalesVerifyExecutionListener.class);

    // 运行时注入service
    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();

    private AfterSalesService afterSalesService = context.getBean("afterSalesOrderService",AfterSalesService.class);

    private DataSourceTransactionManager dataSourceTransactionManager = context.getBean("transactionManager", DataSourceTransactionManager.class);
    private WMSBASRProcessor wmsbasrProcessor = context.getBean("WMSBASRProcessor", WMSBASRProcessor.class);
    private DealTicketTaskForBuyReturnProcessor dealTicketTaskProcessor = context.getBean("dealTicketTaskForBuyReturnProcessor", DealTicketTaskForBuyReturnProcessor.class);
    private DealSaleorderForBuyReturnAfterProcessor dealSaleorderAfterProcessor = context.getBean("dealSaleorderForBuyReturnAfterProcessor", DealSaleorderForBuyReturnAfterProcessor.class);
    private DealBuyorderForBuyReturnAfterProcessor dealBuyorderAfterProcessor = context.getBean("dealBuyorderForBuyReturnAfterProcessor", DealBuyorderForBuyReturnAfterProcessor.class);
    private OverBuyordeReturnAfterSalesCore overBuyorderAfterSalesCore = context.getBean("overBuyordeReturnAfterSalesCore", OverBuyordeReturnAfterSalesCore.class);



    @Override
    public void notify(DelegateExecution execution) throws Exception {

        ServletRequestAttributes ra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = ra.getRequest();
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        AfterSalesVo afterSalesInfo = (AfterSalesVo) execution.getVariable("afterSalesVo");
        //采购单售后不走审核流程
        afterSalesInfo.setAtferSalesStatus(2);//已完结

        //采购退货单确认完成
        DefaultTransactionAttribute defaultTransactionAttribute = new DefaultTransactionAttribute();
        defaultTransactionAttribute.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);

        TransactionStatus transactionStatus = dataSourceTransactionManager.getTransaction(defaultTransactionAttribute);
        logger.info("采购退货单" + afterSalesInfo.getAfterSalesNo() + "确认完成,start====================");

        try {
            dataSourceTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            logger.error("purchaseAuditFinishListener->onActionHappen:", e);
            dataSourceTransactionManager.rollback(transactionStatus);
            throw e;
        }
        AfterSales afterSalesById = afterSalesService.getAfterSalesById(afterSalesInfo.getAfterSalesId());
        OrderProcessorManager orderProcessorManager = new OrderProcessorManager();
        orderProcessorManager.setBizDto(new BizDto<AfterSalesVo>(afterSalesInfo)).setPre(wmsbasrProcessor).setCoreBiz(overBuyorderAfterSalesCore)
                .setAfter(dealSaleorderAfterProcessor).setAfter(dealBuyorderAfterProcessor).setAfter(dealTicketTaskProcessor).excute();
    }
}
