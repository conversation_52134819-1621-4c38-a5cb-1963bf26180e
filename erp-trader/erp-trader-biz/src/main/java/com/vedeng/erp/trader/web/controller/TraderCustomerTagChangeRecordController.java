package com.vedeng.erp.trader.web.controller;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/18 13:21
 **/
@RequestMapping("/traderCustomerTagChangeRecord")
@Controller
public class TraderCustomerTagChangeRecordController {

    @RequestMapping(value = "/list")
    @NoNeedAccessAuthorization
    public ModelAndView terminalView(Integer traderCustomerId) {
        ModelAndView mv = new ModelAndView("vue/view/tagchangerecord/detail");
        mv.addObject("traderCustomerId",traderCustomerId);
        return mv;
    }

}
