package com.vedeng.wechat.service.impl;

import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.OrderConstant;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.util.DateUtil;
import com.vedeng.logistics.dao.ExpressMapper;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.ExpressDetail;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.passport.api.wechat.dto.req.ReqTemplateVariable;
import com.vedeng.passport.api.wechat.dto.template.TemplateVar;
import com.vedeng.trader.model.Trader;
import com.vedeng.wechat.JcWeChatEnum;
import com.vedeng.wechat.model.JcWeChatModel;
import com.vedeng.wechat.service.JcWeChatArrService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName JcWeChatArrServiceImpl.java
 * @Description TODO 集采微信消息推送
 * @createTime 2021年02月24日 16:44:00
 */

@Service
public class JcWeChatArrServiceImpl extends BaseServiceimpl implements JcWeChatArrService {

    private final static Logger LOGGER = LoggerFactory.getLogger(JcWeChatArrServiceImpl.class);


    @Value("${jcvx_cofirmorder_id}")
    private String jcVxConfirmOrderId;

    @Value("${jcvx_delivery_id}")
    private String jcVxdeliveryId;

    @Value("${jcvx_serviceUrl}")
    private String jcVxServiceUrl;

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    private ExpressMapper expressMapper;

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Autowired
    private BuyorderGoodsMapper buyorderGoodsMapper;


    @Override
    public void sendTemplateMsgJcorder(JcWeChatModel jcWeChatModel , JcWeChatEnum jcWeChatEnum, Boolean isDeliveryDirect) {
        try {
            if(JcWeChatEnum.SAVE_ORDER.equals(jcWeChatEnum)){
                //待用户确认发送消息
                sendSaveOrderTemplateMsg(jcWeChatModel);

            }else if(JcWeChatEnum.SEND_DELIVERY.equals(jcWeChatEnum)){
                //发货发送消息
                sendDeliverTemplateMsg(jcWeChatModel,isDeliveryDirect);
            }
        }catch (Exception e){
            LOGGER.error("sendTemplateMsgJcorder error",e);
        }


    }

    private void sendDeliverTemplateMsg(JcWeChatModel jcWeChatModel, Boolean isDeliveryDirect) {
        ReqTemplateVariable reqTemp = new ReqTemplateVariable();

        Saleorder saleorder = saleorderMapper.getSaleOrderById(jcWeChatModel.getOrderId());

        if(!OrderConstant.ORDER_TYPE_JCF.equals(saleorder.getOrderType()) && !OrderConstant.ORDER_TYPE_JCO.equals(saleorder.getOrderType())){
            return;
        }

        LOGGER.info("jc微信消息 sendDeliverTemplateMsg orderNo:{},expressId:{}",saleorder.getSaleorderNo(),jcWeChatModel.getExpressId());


        if(jcWeChatModel.getExpressId() == null || jcWeChatModel.getExpressId() == 0){
            return;
        }

        User user = getUser(saleorder);

        Express express = expressMapper.getExpressInfoById(jcWeChatModel.getExpressId());

        List<ExpressDetail> expressDetailList = expressMapper.getExpressDetailsList(express);


        ExpressDetail expressDetail = expressDetailList.stream().findFirst().get();

        SaleorderGoods saleorderGoodsByExpressDetailId;
        //直发并且采购到付
        if (isDeliveryDirect && expressDetail.getBusinessType() == 515){
            saleorderGoodsByExpressDetailId = buyorderGoodsMapper.getBuyorderGoodsNameByExpressDetailId(expressDetail.getExpressDetailId());
        } else {
            saleorderGoodsByExpressDetailId = saleorderGoodsMapper.getSaleorderGoodsByExpressDetailId(expressDetail.getExpressDetailId());
        }

        List<SaleorderGoods> saleorderGoodsById = saleorderGoodsMapper.getSaleorderGoodsBySaleorderId(saleorder.getSaleorderId());

        //订单商品实际数量
        int allGoodsNum = 0;
        for (SaleorderGoods saleorderGoods : saleorderGoodsById) {
            allGoodsNum = allGoodsNum + saleorderGoods.getNum() -
                    (saleorderGoods.getAfterReturnNum() == null ? 0 : saleorderGoods.getAfterReturnNum());
        }

        //本次快递数量
        int expressGoodsNum = 0;
        for (ExpressDetail detail : expressDetailList) {
            expressGoodsNum = expressGoodsNum + detail.getNum();
        }

        reqTemp.setMobile(saleorder.getTraderContactMobile());
        reqTemp.setTemplateId(jcVxdeliveryId);
        TemplateVar first = new TemplateVar();
        first.setValue("尊敬的客户，您的订单已发货，请注意查收:" + "\r\n");

        //客户信息
        TemplateVar keyword1 = new TemplateVar();
        keyword1.setValue(saleorder.getTakeTraderContactName());

        //商品名称
        TemplateVar keyword2 = new TemplateVar();
        keyword2.setValue(saleorderGoodsByExpressDetailId.getGoodsName() + " 等"+ allGoodsNum +"个商品");

        //物流信息
        TemplateVar keyword3 = new TemplateVar();
        keyword3.setValue(express.getLogisticsName());

        TemplateVar keyword4 = new TemplateVar();
        keyword4.setValue(express.getLogisticsNo());

        TemplateVar remark = getRemark(user);

        reqTemp.setFirst(first);
        reqTemp.setKeyword1(keyword1);
        reqTemp.setKeyword2(keyword2);
        reqTemp.setKeyword3(keyword3);
        reqTemp.setKeyword4(keyword4);
        reqTemp.setRemark(remark);

        LOGGER.info("jc微信消息 sendDeliverTemplateMsg reqTempMobile:{}",reqTemp.getMobile());

        sendMsg(reqTemp);

    }


    private void sendSaveOrderTemplateMsg(JcWeChatModel jcWeChatModel) throws Exception {
        ReqTemplateVariable reqTemp = new ReqTemplateVariable();

        Saleorder saleorder = saleorderMapper.getSaleOrderById(jcWeChatModel.getOrderId());

        if (!OrderConstant.ORDER_TYPE_JCF.equals(saleorder.getOrderType())) {
            return;
        }

        List<SaleorderGoods> saleorderGoodsList = saleorderGoodsMapper.getSaleordergoodsList(saleorder);

        if(CollectionUtils.isEmpty(saleorderGoodsList)){
            throw new Exception("集采线下订单发送微信消息失败 商品信息为空");
        }
        Integer allGoodsNum = saleorderGoodsList.stream().collect(Collectors.summingInt(item -> item.getNum()));

        User user = getUser(saleorder);

        reqTemp.setMobile(saleorder.getTraderContactMobile());
        reqTemp.setTemplateId(jcVxConfirmOrderId);

        LOGGER.info("jc微信消息 sendSaveOrderTemplateMsg reqTempMobile:{},orderNo:{}",reqTemp.getMobile(),saleorder.getSaleorderNo());

        TemplateVar first = new TemplateVar();
        first.setValue("尊敬的客户，您的订单已创建，请您完成确认:" + "\r\n");

        //归属平台
        TemplateVar keyword1 = new TemplateVar();
        keyword1.setValue("医械购集采平台");

        //时间
        TemplateVar keyword2 = new TemplateVar();
        keyword2.setValue(DateUtil.convertString(saleorder.getAddTime(),DateUtil.TIME_FORMAT));

        //订单金额
        TemplateVar keyword3 = new TemplateVar();
        keyword3.setValue(saleorder.getTotalAmount().toString());

        //明细条目
        TemplateVar keyword4 = new TemplateVar();
        keyword4.setValue(allGoodsNum.toString());

        //订单状态
        TemplateVar keyword5 = new TemplateVar();
        keyword5.setValue("待确认");

        TemplateVar remark = getRemark(user);

        reqTemp.setFirst(first);
        reqTemp.setKeyword1(keyword1);
        reqTemp.setKeyword2(keyword2);
        reqTemp.setKeyword3(keyword3);
        reqTemp.setKeyword4(keyword4);
        reqTemp.setKeyword5(keyword5);
        reqTemp.setRemark(remark);

        sendMsg(reqTemp);

    }

    private TemplateVar getRemark(User user) {
        TemplateVar remark = new TemplateVar();
        remark.setValue("感谢您对医械购的支持与信任，如有疑问请联系:" + user.getUsername() + " " + user.getRealName() + "  " + user.getMobile());
        return remark;
    }

    private User getUser(Saleorder saleorder) {
        Trader trader = new Trader();
        trader.setTraderId(saleorder.getTraderId());
        User user = userMapper.getNewUserByTraderId(trader);
        return user;
    }

    private void sendMsg(ReqTemplateVariable reqTemp) {
        sendTemplateMsg(jcVxServiceUrl+"/weChatContent/sendTemplateMessage", reqTemp);
    }
}
