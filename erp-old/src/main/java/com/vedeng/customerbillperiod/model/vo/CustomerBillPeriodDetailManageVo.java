package com.vedeng.customerbillperiod.model.vo;

import com.vedeng.customerbillperiod.dto.CustomerBillPeriodDetailManageDto;

import java.math.BigDecimal;

/**
 * <AUTHOR> simgo.wang
 * @date : 2021/8/6 10:38
 * @desc :
 */
public class CustomerBillPeriodDetailManageVo extends CustomerBillPeriodDetailManageDto {

    //物流日志/发票票号
    private String relatedInfo;

    //金额
    private BigDecimal productAmount;

    //是否直发
    private Integer deliveryDirect;

    /**
     * 售后退货、还款产生的明细对应的支付订单使用ID
     */
    private Long parentUseDetailId;

    /**
     * 账期使用关联表的No
     */
    private String orderRelatedNo;

    /**
     * 是否展示
     */
    private Integer isShow;

    public Integer getIsShow() {
        return isShow;
    }

    public void setIsShow(Integer isShow) {
        this.isShow = isShow;
    }

    public String getOrderRelatedNo() {
        return orderRelatedNo;
    }

    public void setOrderRelatedNo(String orderRelatedNo) {
        this.orderRelatedNo = orderRelatedNo;
    }

    public Integer getDeliveryDirect() {
        return deliveryDirect;
    }

    public void setDeliveryDirect(Integer deliveryDirect) {
        this.deliveryDirect = deliveryDirect;
    }

    public String getRelatedInfo() {
        return relatedInfo;
    }

    public void setRelatedInfo(String relatedInfo) {
        this.relatedInfo = relatedInfo;
    }

    public BigDecimal getProductAmount() {
        return productAmount;
    }

    public void setProductAmount(BigDecimal productAmount) {
        this.productAmount = productAmount;
    }

    public Long getParentUseDetailId() {
        return parentUseDetailId;
    }

    public void setParentUseDetailId(Long parentUseDetailId) {
        this.parentUseDetailId = parentUseDetailId;
    }
}
