package com.vedeng.erp.mobile.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class SaleOrderExpressDetailInfoDto {

    /**
     * 快递详情id
     */
    private Integer expressDetailId;

    /**
     * 快递id
     */
    private Integer expressId;

    /**
     * 快递类型
     */
    private Integer businessType;

    /**
     * 关联商品id
     */
    private Integer relatedId;

    /**
     * 发货数量
     */
    private Integer expressDetailNum;

    /**
     * SKU
     */
    private String sku;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品单价
     */
    private BigDecimal price;
}
