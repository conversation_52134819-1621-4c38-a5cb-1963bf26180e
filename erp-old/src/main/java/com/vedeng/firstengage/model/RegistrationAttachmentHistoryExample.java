package com.vedeng.firstengage.model;

import java.util.ArrayList;
import java.util.List;

public class RegistrationAttachmentHistoryExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_REIGISTRATION_ATTACHMENT_HISTORY
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_REIGISTRATION_ATTACHMENT_HISTORY
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_REIGISTRATION_ATTACHMENT_HISTORY
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REIGISTRATION_ATTACHMENT_HISTORY
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public RegistrationAttachmentHistoryExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REIGISTRATION_ATTACHMENT_HISTORY
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REIGISTRATION_ATTACHMENT_HISTORY
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REIGISTRATION_ATTACHMENT_HISTORY
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REIGISTRATION_ATTACHMENT_HISTORY
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REIGISTRATION_ATTACHMENT_HISTORY
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REIGISTRATION_ATTACHMENT_HISTORY
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REIGISTRATION_ATTACHMENT_HISTORY
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REIGISTRATION_ATTACHMENT_HISTORY
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REIGISTRATION_ATTACHMENT_HISTORY
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REIGISTRATION_ATTACHMENT_HISTORY
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_REIGISTRATION_ATTACHMENT_HISTORY
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andReigistrationAttachmentHistoryIdIsNull() {
            addCriterion("REIGISTRATION_ATTACHMENT_HISTORY_ID is null");
            return (Criteria) this;
        }

        public Criteria andReigistrationAttachmentHistoryIdIsNotNull() {
            addCriterion("REIGISTRATION_ATTACHMENT_HISTORY_ID is not null");
            return (Criteria) this;
        }

        public Criteria andReigistrationAttachmentHistoryIdEqualTo(Integer value) {
            addCriterion("REIGISTRATION_ATTACHMENT_HISTORY_ID =", value, "reigistrationAttachmentHistoryId");
            return (Criteria) this;
        }

        public Criteria andReigistrationAttachmentHistoryIdNotEqualTo(Integer value) {
            addCriterion("REIGISTRATION_ATTACHMENT_HISTORY_ID <>", value, "reigistrationAttachmentHistoryId");
            return (Criteria) this;
        }

        public Criteria andReigistrationAttachmentHistoryIdGreaterThan(Integer value) {
            addCriterion("REIGISTRATION_ATTACHMENT_HISTORY_ID >", value, "reigistrationAttachmentHistoryId");
            return (Criteria) this;
        }

        public Criteria andReigistrationAttachmentHistoryIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("REIGISTRATION_ATTACHMENT_HISTORY_ID >=", value, "reigistrationAttachmentHistoryId");
            return (Criteria) this;
        }

        public Criteria andReigistrationAttachmentHistoryIdLessThan(Integer value) {
            addCriterion("REIGISTRATION_ATTACHMENT_HISTORY_ID <", value, "reigistrationAttachmentHistoryId");
            return (Criteria) this;
        }

        public Criteria andReigistrationAttachmentHistoryIdLessThanOrEqualTo(Integer value) {
            addCriterion("REIGISTRATION_ATTACHMENT_HISTORY_ID <=", value, "reigistrationAttachmentHistoryId");
            return (Criteria) this;
        }

        public Criteria andReigistrationAttachmentHistoryIdIn(List<Integer> values) {
            addCriterion("REIGISTRATION_ATTACHMENT_HISTORY_ID in", values, "reigistrationAttachmentHistoryId");
            return (Criteria) this;
        }

        public Criteria andReigistrationAttachmentHistoryIdNotIn(List<Integer> values) {
            addCriterion("REIGISTRATION_ATTACHMENT_HISTORY_ID not in", values, "reigistrationAttachmentHistoryId");
            return (Criteria) this;
        }

        public Criteria andReigistrationAttachmentHistoryIdBetween(Integer value1, Integer value2) {
            addCriterion("REIGISTRATION_ATTACHMENT_HISTORY_ID between", value1, value2, "reigistrationAttachmentHistoryId");
            return (Criteria) this;
        }

        public Criteria andReigistrationAttachmentHistoryIdNotBetween(Integer value1, Integer value2) {
            addCriterion("REIGISTRATION_ATTACHMENT_HISTORY_ID not between", value1, value2, "reigistrationAttachmentHistoryId");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdIsNull() {
            addCriterion("REGISTRATION_NUMBER_ID is null");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdIsNotNull() {
            addCriterion("REGISTRATION_NUMBER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdEqualTo(Integer value) {
            addCriterion("REGISTRATION_NUMBER_ID =", value, "registrationNumberId");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdNotEqualTo(Integer value) {
            addCriterion("REGISTRATION_NUMBER_ID <>", value, "registrationNumberId");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdGreaterThan(Integer value) {
            addCriterion("REGISTRATION_NUMBER_ID >", value, "registrationNumberId");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("REGISTRATION_NUMBER_ID >=", value, "registrationNumberId");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdLessThan(Integer value) {
            addCriterion("REGISTRATION_NUMBER_ID <", value, "registrationNumberId");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdLessThanOrEqualTo(Integer value) {
            addCriterion("REGISTRATION_NUMBER_ID <=", value, "registrationNumberId");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdIn(List<Integer> values) {
            addCriterion("REGISTRATION_NUMBER_ID in", values, "registrationNumberId");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdNotIn(List<Integer> values) {
            addCriterion("REGISTRATION_NUMBER_ID not in", values, "registrationNumberId");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdBetween(Integer value1, Integer value2) {
            addCriterion("REGISTRATION_NUMBER_ID between", value1, value2, "registrationNumberId");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIdNotBetween(Integer value1, Integer value2) {
            addCriterion("REGISTRATION_NUMBER_ID not between", value1, value2, "registrationNumberId");
            return (Criteria) this;
        }

        public Criteria andAttachmentTypeIsNull() {
            addCriterion("ATTACHMENT_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andAttachmentTypeIsNotNull() {
            addCriterion("ATTACHMENT_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andAttachmentTypeEqualTo(Integer value) {
            addCriterion("ATTACHMENT_TYPE =", value, "attachmentType");
            return (Criteria) this;
        }

        public Criteria andAttachmentTypeNotEqualTo(Integer value) {
            addCriterion("ATTACHMENT_TYPE <>", value, "attachmentType");
            return (Criteria) this;
        }

        public Criteria andAttachmentTypeGreaterThan(Integer value) {
            addCriterion("ATTACHMENT_TYPE >", value, "attachmentType");
            return (Criteria) this;
        }

        public Criteria andAttachmentTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("ATTACHMENT_TYPE >=", value, "attachmentType");
            return (Criteria) this;
        }

        public Criteria andAttachmentTypeLessThan(Integer value) {
            addCriterion("ATTACHMENT_TYPE <", value, "attachmentType");
            return (Criteria) this;
        }

        public Criteria andAttachmentTypeLessThanOrEqualTo(Integer value) {
            addCriterion("ATTACHMENT_TYPE <=", value, "attachmentType");
            return (Criteria) this;
        }

        public Criteria andAttachmentTypeIn(List<Integer> values) {
            addCriterion("ATTACHMENT_TYPE in", values, "attachmentType");
            return (Criteria) this;
        }

        public Criteria andAttachmentTypeNotIn(List<Integer> values) {
            addCriterion("ATTACHMENT_TYPE not in", values, "attachmentType");
            return (Criteria) this;
        }

        public Criteria andAttachmentTypeBetween(Integer value1, Integer value2) {
            addCriterion("ATTACHMENT_TYPE between", value1, value2, "attachmentType");
            return (Criteria) this;
        }

        public Criteria andAttachmentTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("ATTACHMENT_TYPE not between", value1, value2, "attachmentType");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("`NAME` is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("`NAME` is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("`NAME` =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("`NAME` <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("`NAME` >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("`NAME` >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("`NAME` <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("`NAME` <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("`NAME` like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("`NAME` not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("`NAME` in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("`NAME` not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("`NAME` between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("`NAME` not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andDomainIsNull() {
            addCriterion("`DOMAIN` is null");
            return (Criteria) this;
        }

        public Criteria andDomainIsNotNull() {
            addCriterion("`DOMAIN` is not null");
            return (Criteria) this;
        }

        public Criteria andDomainEqualTo(String value) {
            addCriterion("`DOMAIN` =", value, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainNotEqualTo(String value) {
            addCriterion("`DOMAIN` <>", value, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainGreaterThan(String value) {
            addCriterion("`DOMAIN` >", value, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainGreaterThanOrEqualTo(String value) {
            addCriterion("`DOMAIN` >=", value, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainLessThan(String value) {
            addCriterion("`DOMAIN` <", value, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainLessThanOrEqualTo(String value) {
            addCriterion("`DOMAIN` <=", value, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainLike(String value) {
            addCriterion("`DOMAIN` like", value, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainNotLike(String value) {
            addCriterion("`DOMAIN` not like", value, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainIn(List<String> values) {
            addCriterion("`DOMAIN` in", values, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainNotIn(List<String> values) {
            addCriterion("`DOMAIN` not in", values, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainBetween(String value1, String value2) {
            addCriterion("`DOMAIN` between", value1, value2, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainNotBetween(String value1, String value2) {
            addCriterion("`DOMAIN` not between", value1, value2, "domain");
            return (Criteria) this;
        }

        public Criteria andUriIsNull() {
            addCriterion("URI is null");
            return (Criteria) this;
        }

        public Criteria andUriIsNotNull() {
            addCriterion("URI is not null");
            return (Criteria) this;
        }

        public Criteria andUriEqualTo(String value) {
            addCriterion("URI =", value, "uri");
            return (Criteria) this;
        }

        public Criteria andUriNotEqualTo(String value) {
            addCriterion("URI <>", value, "uri");
            return (Criteria) this;
        }

        public Criteria andUriGreaterThan(String value) {
            addCriterion("URI >", value, "uri");
            return (Criteria) this;
        }

        public Criteria andUriGreaterThanOrEqualTo(String value) {
            addCriterion("URI >=", value, "uri");
            return (Criteria) this;
        }

        public Criteria andUriLessThan(String value) {
            addCriterion("URI <", value, "uri");
            return (Criteria) this;
        }

        public Criteria andUriLessThanOrEqualTo(String value) {
            addCriterion("URI <=", value, "uri");
            return (Criteria) this;
        }

        public Criteria andUriLike(String value) {
            addCriterion("URI like", value, "uri");
            return (Criteria) this;
        }

        public Criteria andUriNotLike(String value) {
            addCriterion("URI not like", value, "uri");
            return (Criteria) this;
        }

        public Criteria andUriIn(List<String> values) {
            addCriterion("URI in", values, "uri");
            return (Criteria) this;
        }

        public Criteria andUriNotIn(List<String> values) {
            addCriterion("URI not in", values, "uri");
            return (Criteria) this;
        }

        public Criteria andUriBetween(String value1, String value2) {
            addCriterion("URI between", value1, value2, "uri");
            return (Criteria) this;
        }

        public Criteria andUriNotBetween(String value1, String value2) {
            addCriterion("URI not between", value1, value2, "uri");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("ADD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("ADD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Long value) {
            addCriterion("ADD_TIME =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Long value) {
            addCriterion("ADD_TIME <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Long value) {
            addCriterion("ADD_TIME >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("ADD_TIME >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Long value) {
            addCriterion("ADD_TIME <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Long value) {
            addCriterion("ADD_TIME <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Long> values) {
            addCriterion("ADD_TIME in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Long> values) {
            addCriterion("ADD_TIME not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Long value1, Long value2) {
            addCriterion("ADD_TIME between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Long value1, Long value2) {
            addCriterion("ADD_TIME not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNull() {
            addCriterion("MOD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNotNull() {
            addCriterion("MOD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andModTimeEqualTo(Long value) {
            addCriterion("MOD_TIME =", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotEqualTo(Long value) {
            addCriterion("MOD_TIME <>", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThan(Long value) {
            addCriterion("MOD_TIME >", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("MOD_TIME >=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThan(Long value) {
            addCriterion("MOD_TIME <", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThanOrEqualTo(Long value) {
            addCriterion("MOD_TIME <=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeIn(List<Long> values) {
            addCriterion("MOD_TIME in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotIn(List<Long> values) {
            addCriterion("MOD_TIME not in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeBetween(Long value1, Long value2) {
            addCriterion("MOD_TIME between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotBetween(Long value1, Long value2) {
            addCriterion("MOD_TIME not between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("CREATOR is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("CREATOR is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(Integer value) {
            addCriterion("CREATOR =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(Integer value) {
            addCriterion("CREATOR <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(Integer value) {
            addCriterion("CREATOR >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("CREATOR >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(Integer value) {
            addCriterion("CREATOR <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(Integer value) {
            addCriterion("CREATOR <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<Integer> values) {
            addCriterion("CREATOR in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<Integer> values) {
            addCriterion("CREATOR not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andUpdatorIsNull() {
            addCriterion("UPDATOR is null");
            return (Criteria) this;
        }

        public Criteria andUpdatorIsNotNull() {
            addCriterion("UPDATOR is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatorEqualTo(Integer value) {
            addCriterion("UPDATOR =", value, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorNotEqualTo(Integer value) {
            addCriterion("UPDATOR <>", value, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorGreaterThan(Integer value) {
            addCriterion("UPDATOR >", value, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("UPDATOR >=", value, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorLessThan(Integer value) {
            addCriterion("UPDATOR <", value, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorLessThanOrEqualTo(Integer value) {
            addCriterion("UPDATOR <=", value, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorIn(List<Integer> values) {
            addCriterion("UPDATOR in", values, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorNotIn(List<Integer> values) {
            addCriterion("UPDATOR not in", values, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorBetween(Integer value1, Integer value2) {
            addCriterion("UPDATOR between", value1, value2, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorNotBetween(Integer value1, Integer value2) {
            addCriterion("UPDATOR not between", value1, value2, "updator");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_REIGISTRATION_ATTACHMENT_HISTORY
     *
     * @mbggenerated do_not_delete_during_merge Tue Nov 24 19:24:12 CST 2020
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_REIGISTRATION_ATTACHMENT_HISTORY
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}