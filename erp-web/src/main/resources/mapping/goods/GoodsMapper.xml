<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.goods.dao.GoodsMapper" >
	<resultMap id="BaseResultMap" type="com.vedeng.order.model.SaleorderGoods" >
    <id column="SALEORDER_GOODS_ID" property="saleorderGoodsId" jdbcType="INTEGER" />
    <result column="SALEORDER_ID" property="saleorderId" jdbcType="INTEGER" />
    <result column="GOODS_ID" property="goodsId" jdbcType="INTEGER" />
    <result column="SKU" property="sku" jdbcType="VARCHAR" />
    <result column="GOODS_NAME" property="goodsName" jdbcType="VARCHAR" />
    <result column="BRAND_NAME" property="brandName" jdbcType="VARCHAR" />
    <result column="MODEL" property="model" jdbcType="VARCHAR" />
    <result column="UNIT_NAME" property="unitName" jdbcType="VARCHAR" />
    <!-- 单价 在耗材商城订单中为优惠后的实际单价  erp的单价 -->
    <result column="PRICE" property="price" jdbcType="DECIMAL" />
    <!-- 实际单价, 耗材商城订单优惠前原来的单价 -->
    <result column="REAL_PRICE" property="realPrice" jdbcType="DECIMAL" />
    <result column="CURRENCY_UNIT_ID" property="currencyUnitId" jdbcType="INTEGER" />
    <result column="NUM" property="num" jdbcType="INTEGER" />
    <result column="BUY_NUM" property="dbBuyNum" jdbcType="INTEGER" />
    <result column="DELIVERY_NUM" property="deliveryNum" jdbcType="INTEGER" />
    <result column="DELIVERY_STATUS" property="deliveryStatus" jdbcType="BIT" />
    <result column="DELIVERY_TIME" property="deliveryTime" jdbcType="BIGINT" />
    <result column="IS_IGNORE" property="isIgnore" jdbcType="BIT" />
    <result column="IGNORE_TIME" property="ignoreTime" jdbcType="BIGINT" />
    <result column="IGNORE_USER_ID" property="ignoreUserId" jdbcType="INTEGER" />
    <result column="PURCHASING_PRICE" property="purchasingPrice" jdbcType="VARCHAR" />
    <result column="DELIVERY_CYCLE" property="deliveryCycle" jdbcType="VARCHAR" />
    <result column="DELIVERY_DIRECT" property="deliveryDirect" jdbcType="BIT" />
    <result column="DELIVERY_DIRECT_COMMENTS" property="deliveryDirectComments" jdbcType="VARCHAR" />
    <result column="REGISTRATION_NUMBER" property="registrationNumber" jdbcType="VARCHAR" />
    <result column="SUPPLIER_NAME" property="supplierName" jdbcType="VARCHAR" />
    <result column="REFERENCE_COST_PRICE" property="referenceCostPrice" jdbcType="DECIMAL" />
    <result column="REFERENCE_PRICE" property="referencePrice" jdbcType="VARCHAR" />
    <result column="REFERENCE_DELIVERY_CYCLE" property="referenceDeliveryCycle" jdbcType="VARCHAR" />
    <result column="REPORT_STATUS" property="reportStatus" jdbcType="BIT" />
    <result column="REPORT_COMMENTS" property="reportComments" jdbcType="VARCHAR" />
    <result column="HAVE_INSTALLATION" property="haveInstallation" jdbcType="BIT" />
    <result column="GOODS_COMMENTS" property="goodsComments" jdbcType="VARCHAR" />
    <result column="INSIDE_COMMENTS" property="insideComments" jdbcType="VARCHAR" />
    <result column="ARRIVAL_USER_ID" property="arrivalUserId" jdbcType="INTEGER" />
    <result column="ARRIVAL_STATUS" property="arrivalStatus" jdbcType="BIT" />
    <result column="ARRIVAL_TIME" property="arrivalTime" jdbcType="BIGINT" />
    <result column="IS_DELETE" property="isDelete" jdbcType="BIT" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
    <result column="LOCKED_STATUS" property="lockedStatus" jdbcType="INTEGER" />
    <!-- 当前sku最大退款金额即当前订单商品实际金额(含退货) -->
    <result column="MAX_SKU_REFUND_AMOUNT" property="maxSkuRefundAmount" jdbcType="DECIMAL" />
    <result column="JX_MARKET_PRICE" property="jxMarketPrice" jdbcType="DECIMAL" />
    <result column="JX_SALE_PRICE" property="jxSalePrice" jdbcType="DECIMAL" />
    <result column="IS_GIFT" property="isGift" jdbcType="INTEGER" />

  </resultMap>

    <resultMap id="VoResultMap" type="com.vedeng.goods.model.vo.GoodsVo" extends="BaseResultMap" >
        <result column="BRAND_NAME" property="brandName" jdbcType="VARCHAR" />
        <result column="TITLE" property="goodsLevelName" jdbcType="VARCHAR" />
        <result column="UNIT_NAME" property="unitName" jdbcType="VARCHAR" />
        <result column="CATEGORY_NAME" property="categoryName" jdbcType="VARCHAR" />
        <!--新商品流-->
        <result column="SPEC" property="spec" jdbcType="VARCHAR" />
    </resultMap>
	<select id="getGoodsIdBySku" parameterType="java.lang.String"  resultType="Integer" >
	SELECT
	A.`GOODS_ID`
	FROM
	`T_GOODS` A
	WHERE A.`SKU` = #{skuNo};
	</select>

	<select id="getGoodsPriceList" resultMap="BaseResultMap" parameterType="java.lang.Integer">
	SELECT
	*
	FROM
	`T_SALEORDER_GOODS` A
	LEFT JOIN `V_CORE_SKU` B
	ON A.`SKU` = B.`SKU_NO`
	WHERE A.`SALEORDER_ID` = #{saleorderId}  AND A.IS_DELETE=0;
  </select>
  <select id="getGoodsInfoById" resultMap="BaseResultMap">
  SELECT
	A.*
	FROM
	`T_GOODS` A
	WHERE A.`GOODS_ID` = #{goodsId}
  </select>

    <select id="getGoodsInfoByGoodsId" resultMap="VoResultMap" parameterType="java.lang.Integer">
        SELECT
            A.*,
            B.`BRAND_NAME`,
            C.`UNIT_NAME`
        FROM
            T_GOODS A
                LEFT JOIN `T_BRAND` B ON A.`BRAND_ID`=B.`BRAND_ID`
                LEFT JOIN `T_UNIT` C ON A.`UNIT_ID`=C.`UNIT_ID`
        WHERE A.`GOODS_ID` = #{goodsId}
    </select>
    <select id="getSkuByGoodsId" resultType="java.lang.String">
      SELECT SKU FROM T_GOODS WHERE GOODS_ID=#{goodsId}
    </select>


    <select id="getGoodsMedicalOffice" parameterType="java.util.List" resultType="java.lang.String">
        SELECT distinct h.DEPARTMENT_NAME FROM V_CORE_SKU sku LEFT JOIN V_CORE_SPU spu on sku.SPU_ID=spu.SPU_ID
        LEFT JOIN V_SPU_DEPARTMENT_MAPPING dm ON dm.SPU_ID=spu.SPU_ID
        LEFT JOIN T_DEPARTMENTS_HOSPITAL h on h.DEPARTMENT_ID=dm.DEPARTMENT_ID
        where sku.SKU_NO in
        <foreach collection="list" item="sku" close=")" open="(" separator=",">
            #{sku}
        </foreach>
        AND h.DEPARTMENT_NAME is not null
    </select>

    <select id="selectGoodsExtendInfo" parameterType="java.lang.String" resultType="com.vedeng.goods.model.Goods">
		SELECT
		       A.SKU_ID GOODS_ID,
		       B.UNIT_NAME,
		       C.BRAND_NAME
		FROM V_CORE_SKU  A LEFT JOIN V_CORE_SPU AA ON A.SPU_ID=AA.SPU_ID
		     LEFT JOIN T_UNIT B ON A.BASE_UNIT_ID = B.UNIT_ID
		     LEFT JOIN T_BRAND C ON AA.BRAND_ID = C.BRAND_ID
		WHERE A.SKU_NO = #{sku,jdbcType=VARCHAR}
	</select>


    <select id="getAssignManageUserByGoods" resultType="com.vedeng.authorization.model.User" parameterType="java.util.List">
        SELECT DISTINCT u.USER_ID,u.USERNAME from T_GOODS g1 LEFT JOIN V_CORE_SKU sku1 ON g1.SKU=sku1.SKU_NO
        LEFT JOIN V_CORE_SPU spu1 ON sku1.SPU_ID = spu1.SPU_ID
        LEFT JOIN T_USER u ON u.USER_ID=spu1.ASSIGNMENT_MANAGER_ID
        WHERE g1.GOODS_ID IN
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="getAssignManageUserCountByGoods" resultType="java.lang.Integer" parameterType="java.lang.Integer">
        SELECT COUNT(u.USER_ID) from T_GOODS g1 LEFT JOIN V_CORE_SKU sku1 ON g1.SKU=sku1.SKU_NO
        LEFT JOIN V_CORE_SPU spu1 ON sku1.SPU_ID = spu1.SPU_ID
        LEFT JOIN T_USER u ON u.USER_ID=spu1.ASSIGNMENT_MANAGER_ID
        WHERE g1.GOODS_ID = #{goodsId}
    </select>


    <select id="getAssignUserIdsByGoods" resultType="java.lang.Integer" parameterType="java.util.List">
        SELECT DISTINCT e.USER_ID FROM (
        SELECT spu1.ASSIGNMENT_MANAGER_ID as USER_ID from T_GOODS g1 LEFT JOIN V_CORE_SKU sku1 ON g1.SKU=sku1.SKU_NO
        LEFT JOIN V_CORE_SPU spu1 ON sku1.SPU_ID = spu1.SPU_ID
        WHERE g1.GOODS_ID IN
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        UNION
        SELECT spu2.ASSIGNMENT_ASSISTANT_ID as USER_ID from T_GOODS g2 LEFT JOIN V_CORE_SKU sku2 ON g2.SKU=sku2.SKU_NO
        LEFT JOIN V_CORE_SPU spu2 ON sku2.SPU_ID = spu2.SPU_ID
        WHERE g2.GOODS_ID IN
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        ) e
    </select>
    <select id="getGoodsSpuType" resultType="integer">
  SELECT
	C.SPU_TYPE
    FROM
	T_GOODS A
	LEFT JOIN V_CORE_SKU B ON A.SKU = B.SKU_NO
	LEFT JOIN V_CORE_SPU C ON B.SPU_ID = C.SPU_ID
    WHERE
	A.GOODS_ID = #{goodsId}
    </select>

    <select id="getGoodsInfoTips" resultType="map">
        select
            K.MATERIAL_CODE,
            ifnull(NUM.REGISTRATION_NUMBER,'') REGISTRATION_NUMBER,
            CASE NUM.MANAGE_CATEGORY_LEVEL
                WHEN 968 THEN '一类医疗器械'
                WHEN  969 THEN '二类医疗器械'
                WHEN  970 THEN '三类医疗器械'
            ELSE '' END AS MANAGE_CATEGORY_LEVEL,
            M.USERNAME,
            U.USERNAME AS 'ASSIS',
            ifnull(K.PACKING_LIST,'') PACKING_LIST ,
            CASE K.CHECK_STATUS
                WHEN 0 THEN '待完善'
                WHEN 1 THEN '审核中'
                WHEN 2 THEN '审核不通过'
                WHEN 3 THEN '审核通过'
            else '' end as CHECK_STATUS,
            K.SKU_NO ,
            K.SHOW_NAME,
            B.BRAND_NAME,
            ifnull(K.MODEL,'')  MODEL,
            UN.UNIT_NAME,ifnull(K.QA_YEARS,'') QA_YEARS,
            concat(CAT1.BASE_CATEGORY_NAME,'->',CAT2.BASE_CATEGORY_NAME,'->',CAT3.BASE_CATEGORY_NAME)
            BASE_CATEGORY_NAME,
            CAT3.BASE_CATEGORY_ID,
            CASE P.SPU_LEVEL
                WHEN 0 THEN '其他产品'
                WHEN 1 THEN '核心产品'
                WHEN 2 THEN '临时产品'
            ELSE '' END AS GOODS_LEVEL_NAME
        from V_CORE_SKU K LEFT JOIN V_CORE_SPU P ON K.SPU_ID=P.SPU_ID
        LEFT JOIN T_FIRST_ENGAGE F ON P.FIRST_ENGAGE_ID=F.FIRST_ENGAGE_ID
        LEFT JOIN T_REGISTRATION_NUMBER NUM ON NUM.REGISTRATION_NUMBER_ID=F.REGISTRATION_NUMBER_ID
        LEFT JOIN T_USER U ON U.USER_ID=P.ASSIGNMENT_ASSISTANT_ID
        LEFT JOIN T_USER M ON M.USER_ID=P.ASSIGNMENT_MANAGER_ID
		LEFT JOIN T_BRAND B ON B.BRAND_ID=P.BRAND_ID
		LEFT JOIN V_BASE_CATEGORY CAT3 ON CAT3.BASE_CATEGORY_ID=P.CATEGORY_ID
		LEFT JOIN V_BASE_CATEGORY CAT2 ON CAT2.BASE_CATEGORY_ID=CAT3.PARENT_ID
		LEFT JOIN V_BASE_CATEGORY CAT1 ON CAT1.BASE_CATEGORY_ID=CAT2.PARENT_ID
        left join T_UNIT UN ON UN.UNIT_ID=K.BASE_UNIT_ID
        where K.SKU_ID=#{sku}
    </select>

    <select id="skuTipList" parameterType="list" resultType="map">
        select
        F.FIRST_ENGAGE_ID FIRST_ENGAGE_ID,F.STANDARD_CATEGORY_TYPE,
        F.NEW_STANDARD_CATEGORY_ID,F.OLD_STANDARD_CATEGORY_ID,
        P.SPU_ID,P.SPU_TYPE,
        K.MATERIAL_CODE,
        ifnull(NUM.REGISTRATION_NUMBER,'') REGISTRATION_NUMBER,
        CASE NUM.MANAGE_CATEGORY_LEVEL
            WHEN 968 THEN '一类医疗器械'
            WHEN  969 THEN '二类医疗器械'
            WHEN  970 THEN '三类医疗器械'
        ELSE '' END AS MANAGE_CATEGORY_LEVEL,
        M.USERNAME,
        U.USERNAME AS 'ASSIS',
        ifnull(K.PACKING_LIST,'') PACKING_LIST ,
        CASE K.CHECK_STATUS
            WHEN 0 THEN '待完善'
            WHEN 1 THEN '审核中'
            WHEN 2 THEN '审核不通过'
            WHEN 3 THEN '审核通过'
        else '' end as CHECK_STATUS,
        K.SKU_ID,
        K.SKU_NO,
        K.SHOW_NAME,
        IFNULL(K.SPEC,'') SPEC,
        B.BRAND_NAME,
        ifnull(K.MODEL,'')  MODEL,
        ifnull(UN.UNIT_NAME,'') UNIT_NAME,
        CASE QA_YEARS
          WHEN NULL THEN ''
          WHEN '' THEN ''
        else CONCAT(QA_YEARS,"年") end as QA_YEARS,
        CASE P.SPU_LEVEL
            WHEN 0 THEN '其他产品'
            WHEN 1 THEN '核心产品'
            WHEN 2 THEN '临时产品'
        ELSE '' END AS GOODS_LEVEL_NAME,
        OPT.TITLE AS GOODS_TYPE_NAME,IFNULL(K.AVAILABLE_STOCK_NUM,0) AVAILABLESTOCKNUM,IFNULL(K.STOCK_NUM,0) STOCKNUM,IFNULL(K.ORDER_OCCUPY_NUM,0) OCCUPYNUM,P.FIRST_ENGAGE_ID, P.SPU_TYPE
        from V_CORE_SKU K LEFT JOIN V_CORE_SPU P
        ON K.SPU_ID=P.SPU_ID
        LEFT JOIN T_FIRST_ENGAGE F ON P.FIRST_ENGAGE_ID=F.FIRST_ENGAGE_ID
        LEFT JOIN T_REGISTRATION_NUMBER NUM ON NUM.REGISTRATION_NUMBER_ID=F.REGISTRATION_NUMBER_ID
        LEFT JOIN T_USER U ON U.USER_ID=P.ASSIGNMENT_ASSISTANT_ID
        LEFT JOIN T_USER M ON M.USER_ID=P.ASSIGNMENT_MANAGER_ID
        LEFT JOIN T_BRAND B ON B.BRAND_ID=P.BRAND_ID
        left join T_UNIT UN ON UN.UNIT_ID=K.BASE_UNIT_ID
        LEFT JOIN T_SYS_OPTION_DEFINITION OPT ON P.SPU_TYPE = OPT.SYS_OPTION_DEFINITION_ID
        where K.SKU_ID in
        <foreach collection="list" item="skuId" index="index"
                 open="(" close=")" separator=",">
            #{skuId}
        </foreach>
    </select>

    <select id="findSpuNamesBySpuIds" parameterType="list" resultType="com.vedeng.goods.model.CoreSpuGenerate">
        select
        SKU_ID AS SPU_ID,SKU_NAME AS SPU_NAME
        from V_CORE_SKU K
        where K.SKU_ID in
        <foreach collection="list" item="spuId" index="index"
                 open="(" close=")" separator=",">
            #{spuId}
        </foreach>
    </select>

    <select id="findSpuInfoBySkuNo" parameterType="java.lang.String" resultType="com.vedeng.goods.model.CoreSpuGenerate">
        SELECT
          spu.SPU_ID,
          spu.CATEGORY_ID,
          spu.BRAND_ID,
          spu.APPARATUS_TYPE,
          spu.SPU_TYPE
        FROM V_CORE_SKU K
        LEFT JOIN V_CORE_SPU spu ON K.SPU_ID = spu.SPU_ID
        WHERE K.SKU_NO = #{skuNo,jdbcType=VARCHAR}
    </select>

    <select id="getBuyPricesDerictOrderDetailId" resultType="com.vedeng.logistics.model.vo.WarehouseGoodsOperateLogVo">
    select B.PRICE AS price,D.BUYORDER_ID  as buyorderId,D.BUYORDER_NO AS buyorderNo,C.NUM num  from T_BUYORDER_GOODS B
    LEFT JOIN T_R_BUYORDER_J_SALEORDER C ON C.BUYORDER_GOODS_ID = B.BUYORDER_GOODS_ID
    left join T_BUYORDER D ON B.BUYORDER_ID=D.BUYORDER_ID
    where
    C.SALEORDER_GOODS_ID =#{orderDetailId} AND D.STATUS IN(1,2) AND B.IS_DELETE=0
 </select>
    <select id="getBuyPricesNoDirectDetailId" resultType="com.vedeng.logistics.model.vo.WarehouseGoodsOperateLogVo">
    SELECT
	B.PRICE AS price ,D.BUYORDER_ID as buyorderId,D.BUYORDER_NO AS buyorderNo,A.NUM num
FROM
	T_WAREHOUSE_GOODS_OPERATE_LOG A
	LEFT JOIN T_BUYORDER_GOODS B ON A.RELATED_ID = B.BUYORDER_GOODS_ID
	LEFT JOIN T_R_BUYORDER_J_SALEORDER C ON C.BUYORDER_GOODS_ID = B.BUYORDER_GOODS_ID
	left join T_BUYORDER D ON B.BUYORDER_ID=D.BUYORDER_ID
WHERE
A.OPERATE_TYPE  IN(1,3,5,8,9)
	AND A.BARCODE_ID !=0
	AND A.IS_ENABLE=1
	AND C.SALEORDER_GOODS_ID =#{orderDetailId} AND D.STATUS IN(1,2) AND B.IS_DELETE=0

 </select>
    <select id="getBuyPricesOrderId" resultType="com.vedeng.logistics.model.vo.WarehouseGoodsOperateLogVo">
    SELECT
        B.PRICE AS price ,B.BUYORDER_ID as buyorderId
    ,C.BUYORDER_NO AS buyorderNo,RELATED_ID relatedId,A.NUM num
FROM
	T_WAREHOUSE_GOODS_OPERATE_LOG A left join T_BUYORDER_GOODS B ON A.RELATED_ID=B.BUYORDER_GOODS_ID
        left join T_BUYORDER C ON C.BUYORDER_ID=B.BUYORDER_ID
    WHERE
        A.OPERATE_TYPE =1
        AND IS_ENABLE = 1
        AND C.STATUS IN(1,2)
        AND B.IS_DELETE=0
        AND A.BARCODE_ID IN (
            SELECT
            A.BARCODE_ID
            FROM
            T_WAREHOUSE_GOODS_OPERATE_LOG A
            LEFT JOIN T_SALEORDER_GOODS B ON A.RELATED_ID = B.SALEORDER_GOODS_ID
            WHERE
            A.OPERATE_TYPE IN ( 2 )
            AND A.BARCODE_ID != 0
            AND A.IS_ENABLE = 1
            AND B.IS_DELETE= 0
            AND B.SALEORDER_GOODS_ID = #{orderDetailId}
        )
    </select>
    <select id="getOrderDetailIdsByRelatedIds" resultType="integer">
    select ORDER_DETAIL_ID from T_AFTER_SALES_GOODS A
	WHERE AFTER_SALES_GOODS_ID IN
        <foreach collection="relatedIds" item="creatorId" index="index"
                 open="(" close=")" separator=",">
            #{creatorId}
        </foreach>
    </select>
    <select id="getAfterSaleOrderDetailId" resultType="integer">
    SELECT
     DISTINCT B.ORDER_DETAIL_ID AS detailGoodsId
    FROM
    T_WAREHOUSE_GOODS_OPERATE_LOG A left join T_AFTER_SALES_GOODS B ON A.RELATED_ID=B.AFTER_SALES_GOODS_ID
    WHERE
    A.OPERATE_TYPE IN (5 )
    AND IS_ENABLE = 1
    AND A.BARCODE_ID IN (
    SELECT
    A.BARCODE_ID
    FROM
    T_WAREHOUSE_GOODS_OPERATE_LOG A
    LEFT JOIN T_SALEORDER_GOODS B ON A.RELATED_ID = B.SALEORDER_GOODS_ID
    WHERE
    A.OPERATE_TYPE IN ( 2,4,6,7,10 )
    AND A.BARCODE_ID != 0
    AND A.IS_ENABLE = 1
    AND B.IS_DELETE=0
    AND A.RELATED_ID IN (  #{orderDetailId}  )
    )
</select>

    <select id="getBuyPriceByBuyorderAftersale" resultType="com.vedeng.logistics.model.vo.WarehouseGoodsOperateLogVo">
    SELECT
       C.PRICE AS price ,C.BUYORDER_ID as buyorderId
    ,D.BUYORDER_NO AS buyorderNo,A.NUM as num
    FROM
    T_WAREHOUSE_GOODS_OPERATE_LOG A left join T_AFTER_SALES_GOODS B ON A.RELATED_ID=B.AFTER_SALES_GOODS_ID
    left join T_BUYORDER_GOODS C ON C.BUYORDER_GOODS_ID=B.ORDER_DETAIL_ID
      left join T_BUYORDER D ON C.BUYORDER_ID=D.BUYORDER_ID
    WHERE
    A.OPERATE_TYPE IN (6,8 )
    AND IS_ENABLE = 1
    and C.IS_DELETE=0
    AND D.STATUS IN(1,2)
    AND A.BARCODE_ID IN (
    SELECT
    A.BARCODE_ID
    FROM
    T_WAREHOUSE_GOODS_OPERATE_LOG A
    LEFT JOIN T_SALEORDER_GOODS B ON A.RELATED_ID = B.SALEORDER_GOODS_ID
    WHERE
    A.OPERATE_TYPE IN ( 2,4,6,7,10 )
    AND A.BARCODE_ID != 0
    AND A.IS_ENABLE = 1
    AND B.IS_DELETE=0
    AND B.ORDER_DETAIL_ID IN (  #{orderDetailId}  )
    )
</select>
    <select id="getSkuInfo" resultType="com.vedeng.goods.model.Goods">
         SELECT
        A.SKU_ID AS goodsId,
        A.SKU_NO AS sku,
        A.SHOW_NAME AS goodsName,
        D.BRAND_ID AS brandId,
        D.CATEGORY_ID AS categoryId,
        B.BRAND_NAME AS brandName,
        A.MODEL AS model,
        A.BASE_UNIT_ID AS unitId,
        C.UNIT_NAME AS unitName,
        A.MATERIAL_CODE AS materialCode,
        NUM.MANAGE_CATEGORY_LEVEL AS manageCategory,
        D.SPU_LEVEL AS goodsLevel,
        CASE D.SPU_LEVEL
        WHEN 0 THEN '其他产品'
        WHEN 1 THEN '核心产品'
        WHEN 2 THEN '临时产品'
        ELSE '' END AS goodsLevelName,
        A.CHECK_STATUS verifyStatus,
        A.SOURCE,
        CONCAT(M.USERNAME,',',U.USERNAME) AS proUserName
        ,xx.status as parentId,
        A.IS_DIRECT
        FROM
        V_CORE_SKU A
        LEFT JOIN V_CORE_SPU D ON A.SPU_ID=D.SPU_ID
        LEFT JOIN T_FIRST_ENGAGE F ON D.FIRST_ENGAGE_ID=F.FIRST_ENGAGE_ID
        LEFT JOIN T_REGISTRATION_NUMBER NUM ON NUM.REGISTRATION_NUMBER_ID=F.REGISTRATION_NUMBER_ID
        LEFT JOIN T_BRAND B ON D.BRAND_ID = B.BRAND_ID
        LEFT JOIN T_UNIT C ON A.BASE_UNIT_ID = C.UNIT_ID
        LEFT JOIN T_USER U ON U.USER_ID=D.ASSIGNMENT_ASSISTANT_ID
        LEFT JOIN T_USER M ON M.USER_ID=D.ASSIGNMENT_MANAGER_ID
        LEFT JOIN T_VERIFIES_INFO xx
        ON A.SKU_ID = xx.RELATE_TABLE_KEY AND xx.RELATE_TABLE = 'T_GOODS'
        WHERE A.STATUS = 1 and D.CHECK_STATUS != 4
        and A.SKU_ID=#{skuId}
    </select>

    <select id="getGoodNameBySkuNo" resultType="java.lang.String">
        SELECT
		       SKU_NAME
		FROM V_CORE_SKU
		WHERE SKU_NO = #{sku,jdbcType=VARCHAR}
    </select>

    <select id="getUnitNameBySkuNo" resultType="java.lang.String">
        select T.UNIT_NAME from V_CORE_SKU V left join T_UNIT T on V.BASE_UNIT_ID=T.UNIT_ID where T.IS_DEL=0 and SKU_NO=#{skuNo}
    </select>

    <select id="getSkuAuthotizationInfoBySku" resultType="com.vedeng.goods.model.CoreSkuGenerate">
        SELECT
            SKU_ID,
            IS_NEED_REPORT,
            IS_AUTHORIZED
        FROM
            V_CORE_SKU
        WHERE
            SKU_ID = #{skuId,jdbcType=BIGINT}
    </select>


    <select id="getSpuIdBySkuId" resultType="java.lang.Integer">
        select SPU_ID from V_CORE_SKU where SKU_ID= #{skuId}
    </select>

    <select id="getSkuIdBySkuNo" resultType="java.lang.Integer">
        select SKU_ID from V_CORE_SKU where SKU_NO= #{skuNo}
    </select>
    <select id="getGoodsByIdsNew" resultType="com.vedeng.goods.model.vo.GoodsVo">
        select
        k.SKU_ID GOODS_ID,k.SHOW_NAME GOODS_NAME, k.MODEL,k.MATERIAL_CODE,k.SKU_NO SKU,
        a.IS_ON_SALE,k.STATUS IS_DISCARD,k.SPEC,t.UNIT_NAME,
        b.BRAND_NAME
        from
        V_CORE_SKU k
        left join T_GOODS a on k.SKU_ID=a.GOODS_ID
        left join V_CORE_SPU p on k.SPU_ID=p.SPU_ID
        left join T_UNIT t on k.BASE_UNIT_ID=t.UNIT_ID
        left join T_BRAND b on  p.BRAND_ID = b.BRAND_ID
        where k.SKU_ID in
        <foreach collection="goodsIds" item="goodsId" open="(" close=")" separator=",">
            #{goodsId,jdbcType=INTEGER}
        </foreach>
    </select>
    <select id="getSkuInfoListPage" resultType="com.vedeng.goods.model.vo.GoodsVo" parameterType="Map">
        SELECT
        A.SKU_ID AS goodsId,
        A.SKU_NO AS sku,
        A.SHOW_NAME AS goodsName,
        D.BRAND_ID AS brandId,
        D.CATEGORY_ID AS categoryId,
        B.BRAND_NAME AS brandName,
        A.MODEL AS model,
        A.BASE_UNIT_ID AS unitId,
        C.UNIT_NAME AS unitName,
        A.MATERIAL_CODE AS materialCode,
        NUM.MANAGE_CATEGORY_LEVEL AS manageCategory,
        D.SPU_LEVEL AS goodsLevel,
        D.SPU_ID as spuId,
        CASE D.SPU_LEVEL
        WHEN 0 THEN '其他产品'
        WHEN 1 THEN '核心产品'
        WHEN 2 THEN '临时产品'
        ELSE '' END AS goodsLevelName,
        A.CHECK_STATUS verifyStatus,
        A.SOURCE,
        CONCAT(M.USERNAME,',',U.USERNAME) AS proUserName
        ,xx.status as parentId
        FROM
        V_CORE_SKU A
        LEFT JOIN V_CORE_SPU D ON A.SPU_ID=D.SPU_ID
        LEFT JOIN T_FIRST_ENGAGE F ON D.FIRST_ENGAGE_ID=F.FIRST_ENGAGE_ID
        LEFT JOIN T_REGISTRATION_NUMBER NUM ON NUM.REGISTRATION_NUMBER_ID=F.REGISTRATION_NUMBER_ID
        LEFT JOIN T_BRAND B ON D.BRAND_ID = B.BRAND_ID
        LEFT JOIN T_UNIT C ON A.BASE_UNIT_ID = C.UNIT_ID
        LEFT JOIN T_USER U ON U.USER_ID=D.ASSIGNMENT_ASSISTANT_ID
        LEFT JOIN T_USER M ON M.USER_ID=D.ASSIGNMENT_MANAGER_ID
        LEFT JOIN T_VERIFIES_INFO xx
        ON A.SKU_ID = xx.RELATE_TABLE_KEY AND xx.RELATE_TABLE = 'T_GOODS'
        WHERE A.STATUS = 1 and D.CHECK_STATUS != 4
        <if test="goods.searchContent!=null and goods.searchContent!=''">
            AND (
            A.SKU_NO LIKE CONCAT('%',#{goods.searchContent,jdbcType=VARCHAR},'%' )
            OR
            A.SHOW_NAME LIKE CONCAT('%',#{goods.searchContent,jdbcType=VARCHAR},'%' )
            OR
            B.BRAND_NAME LIKE CONCAT('%',#{goods.searchContent,jdbcType=VARCHAR},'%' )
            OR
            A.MODEL LIKE CONCAT('%',#{goods.searchContent,jdbcType=VARCHAR},'%' )
            )
        </if>
        <if test="goods.goodsName!=null and goods.goodsName!=''">
            AND A.SHOW_NAME LIKE CONCAT('%',#{goods.goodsName,jdbcType=VARCHAR},'%' )
        </if>
        <if test="goods.goodsType!=null and goods.goodsType!=''">
            AND A.SKU_NO IN
            <foreach collection="goods.goodsIdArr" item="sku" open="(" close=")" separator=",">
                #{sku,jdbcType=VARCHAR}
            </foreach>
        </if>
        GROUP BY A.SKU_ID
    </select>
    <select id="getSkuInfoBySku" resultType="com.vedeng.goods.model.Goods">
        SELECT
            A.SKU_ID AS goodsId,
            A.SKU_NO AS sku,
            A.SHOW_NAME AS goodsName,
            D.BRAND_ID AS brandId,
            D.CATEGORY_ID AS categoryId,
            B.BRAND_NAME AS brandName,
            A.MODEL AS model,
            A.BASE_UNIT_ID AS unitId,
            C.UNIT_NAME AS unitName,
            A.MATERIAL_CODE AS materialCode,
            NUM.MANAGE_CATEGORY_LEVEL AS manageCategory,
            D.SPU_LEVEL AS goodsLevel,
            CASE D.SPU_LEVEL
                WHEN 0 THEN '其他产品'
                WHEN 1 THEN '核心产品'
                WHEN 2 THEN '临时产品'
                ELSE '' END AS goodsLevelName,
            A.CHECK_STATUS verifyStatus,
            A.SOURCE,
            CONCAT(IFNULL(M.USERNAME,''),' ',IFNULL(U.USERNAME,'')) AS proUserName
                ,xx.status as parentId,
            T5.CATEGORY_NAME,
            M.USER_ID AS managerUserId,
            U.USERNAME AS assistantUserName,
            M.USERNAME AS managerUserName
        FROM
            V_CORE_SKU A
        LEFT JOIN V_CORE_SPU D ON A.SPU_ID=D.SPU_ID
        LEFT JOIN T_FIRST_ENGAGE F ON D.FIRST_ENGAGE_ID=F.FIRST_ENGAGE_ID
        LEFT JOIN T_REGISTRATION_NUMBER NUM ON NUM.REGISTRATION_NUMBER_ID=F.REGISTRATION_NUMBER_ID
        LEFT JOIN T_BRAND B ON D.BRAND_ID = B.BRAND_ID
        LEFT JOIN T_UNIT C ON A.BASE_UNIT_ID = C.UNIT_ID
        LEFT JOIN T_USER U ON U.USER_ID=D.ASSIGNMENT_ASSISTANT_ID
        LEFT JOIN T_USER M ON M.USER_ID=D.ASSIGNMENT_MANAGER_ID
        LEFT JOIN T_VERIFIES_INFO xx
                  ON A.SKU_ID = xx.RELATE_TABLE_KEY AND xx.RELATE_TABLE = 'T_GOODS'
        LEFT JOIN T_CATEGORY T5 ON T5.CATEGORY_ID = D.CATEGORY_ID
		WHERE A.SKU_NO=#{sku,jdbcType=VARCHAR}
    </select>

    <select id="getSpecialGoodsList" resultType="com.vedeng.goods.model.Goods">
        SELECT
            TITLE AS goodsName,
            COMMENTS AS goodsId
        FROM
            T_SYS_OPTION_DEFINITION
        WHERE
            PARENT_ID = 693
    </select>

    <update id="updateSkuOcc">
        update V_CORE_SKU SET ORDER_OCCUPY_NUM=#{occ}

        WHERE SKU_NO=#{skuNo}
    </update>
    <update id="updateSkuLock">
        update V_CORE_SKU SET ACTION_LOCK_NUM=#{lock}
        WHERE SKU_NO=#{skuNo}
    </update>

    <select id="getGoodsById" resultType="com.vedeng.goods.model.Goods">
           select a.REGISTER_NAME as registrationNumberName, k.SHOW_NAME as GOODS_NAME, k.MODEL as MODEL,k.SKU_NO as SKU, b.BRAND_NAME, c.UNIT_NAME, d.CATEGORY_NAME , e.CATEGORY_NAME as STANDARDCATEGORY_NAME,
    	a.GOODS_ID, a.COMPANY_ID, a.PARENT_ID, a.CATEGORY_ID, a.BRAND_ID, a.IS_ON_SALE, a.IS_DISCARD, a.ALIAS_NAME, a.MATERIAL_CODE,
    	k.BASE_UNIT_ID as UNIT_ID, a.CHANGE_NUM, k.UNIT_ID as BASE_UNIT_ID, a.GROSS_WEIGHT, a.NET_WEIGHT, a.GOODS_LENGTH, a.GOODS_WIDTH, a.GOODS_HEIGHT, a.PACKAGE_LENGTH,
    	a.PACKAGE_WIDTH, a.PACKAGE_HEIGHT, a.GOODS_TYPE, a.GOODS_LEVEL, a.MANAGE_CATEGORY, a.MANAGE_CATEGORY_LEVEL, a.PURCHASE_REMIND, a.LICENSE_NUMBER,
    	a.FIRST_ENGAGE_ID, a.RECORD_NUMBER, a.REGISTRATION_NUMBER, a.BEGINTIME, a.ENDTIME, a.AUTHORIZATION_CERTIFICATE_URL, a.OTHER_QUALIFICATION_URL,
    	a.COLOR_PAGE_URL, a.TECHNICAL_PARAMETER_URL, a.INSTRUCTIONS_URL, a.BIDDING_DATA_URL, a.PACKING_LIST, a.TOS, a.TECHNICAL_PARAMETER, a.PERFORMANCE_PARAMETER,
    	a.SPEC_PARAMETER, a.TAX_CATEGORY_NO, a.MANUFACTURER, a.PRODUCTION_LICENSE, a.DISCARD_REASON, a.DISCARD_TIME, a.SUPPLY_MODEL, a.STANDARD_CATEGORY_ID,
    	a.STANDARD_CATEGORY_LEVEL, a.SPEC, a.PRODUCT_ADDRESS, a.STORAGE_REQUIREMENTS, a.SOURCE, a.IS_RECOMMED, a.HREF, a.REGISTER_NAME, a.JX_MARKET_PRICE,
    	a.JX_SALE_PRICE, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER, a.TO_SKU_FLAG, a.IS_NO_REASON_RETURN,
    	j.LAST_VERIFY_USERNAME,
  		j.VERIFY_USERNAME,
        k.TAX_CATEGORY_NO as taxClassificationCode,
  		j.STATUS as VERIFY_STATUS from
    T_GOODS a
    left join T_UNIT c on a.UNIT_ID = c.UNIT_ID
    left join T_CATEGORY d on a.CATEGORY_ID = d.CATEGORY_ID
    left join T_STANDARD_CATEGORY e on a.STANDARD_CATEGORY_ID = e.STANDARD_CATEGORY_ID
    left join T_VERIFIES_INFO j on j.RELATE_TABLE_KEY = a.GOODS_ID and j.RELATE_TABLE = "T_GOODS"
    left join V_CORE_SKU k on a.SKU = k.SKU_NO
    left join V_CORE_SPU l on k.SPU_ID = l.SPU_ID
    left join T_BRAND b on b.BRAND_ID = l.BRAND_ID
    where a.GOODS_ID = #{goodsId,jdbcType=INTEGER}
    </select>

    <select id="getCheckSkuInfoListPage" resultType="com.vedeng.goods.model.vo.GoodsVo" parameterType="Map">
        SELECT
        A.SKU_ID AS goodsId,
        A.SKU_NO AS sku,
        A.SHOW_NAME AS goodsName,
        D.BRAND_ID AS brandId,
        D.CATEGORY_ID AS categoryId,
        B.BRAND_NAME AS brandName,
        A.MODEL AS model,
        A.BASE_UNIT_ID AS unitId,
        C.UNIT_NAME AS unitName,
        A.MATERIAL_CODE AS materialCode,
        NUM.MANAGE_CATEGORY_LEVEL AS manageCategory,
        D.SPU_LEVEL AS goodsLevel,
        D.SPU_ID as spuId,
        CASE D.SPU_LEVEL
        WHEN 0 THEN '其他产品'
        WHEN 1 THEN '核心产品'
        WHEN 2 THEN '临时产品'
        ELSE '' END AS goodsLevelName,
        A.CHECK_STATUS verifyStatus,
        A.SOURCE,
        CONCAT(M.USERNAME,',',U.USERNAME) AS proUserName
        ,xx.status as parentId
        FROM
        V_CORE_SKU A
        LEFT JOIN V_CORE_SPU D ON A.SPU_ID=D.SPU_ID
        LEFT JOIN T_FIRST_ENGAGE F ON D.FIRST_ENGAGE_ID=F.FIRST_ENGAGE_ID
        LEFT JOIN T_REGISTRATION_NUMBER NUM ON NUM.REGISTRATION_NUMBER_ID=F.REGISTRATION_NUMBER_ID
        LEFT JOIN T_BRAND B ON D.BRAND_ID = B.BRAND_ID
        LEFT JOIN T_UNIT C ON A.BASE_UNIT_ID = C.UNIT_ID
        LEFT JOIN T_USER U ON U.USER_ID=D.ASSIGNMENT_ASSISTANT_ID
        LEFT JOIN T_USER M ON M.USER_ID=D.ASSIGNMENT_MANAGER_ID
        LEFT JOIN T_VERIFIES_INFO xx
        ON A.SKU_ID = xx.RELATE_TABLE_KEY AND xx.RELATE_TABLE = 'T_GOODS'
        WHERE A.STATUS = 1 and D.CHECK_STATUS != 4 and A.CHECK_STATUS = 3
        <if test="goods.searchContent!=null and goods.searchContent!=''">
            AND (
            A.SKU_NO LIKE CONCAT('%',#{goods.searchContent,jdbcType=VARCHAR},'%' )
            OR
            A.SHOW_NAME LIKE CONCAT('%',#{goods.searchContent,jdbcType=VARCHAR},'%' )
            OR
            B.BRAND_NAME LIKE CONCAT('%',#{goods.searchContent,jdbcType=VARCHAR},'%' )
            OR
            A.MODEL LIKE CONCAT('%',#{goods.searchContent,jdbcType=VARCHAR},'%' )
            )
        </if>
        <if test="goods.goodsName!=null and goods.goodsName!=''">
            AND A.SHOW_NAME LIKE CONCAT('%',#{goods.goodsName,jdbcType=VARCHAR},'%' )
        </if>
        <if test="goods.goodsType!=null and goods.goodsType!=''">
            AND A.SKU_NO IN
            <foreach collection="goods.goodsIdArr" item="sku" open="(" close=")" separator=",">
                #{sku,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="goods.specModel!=null and goods.specModel!=''">
            AND(
            A.SPEC LIKE CONCAT('%',#{goods.specModel,jdbcType=VARCHAR},'%')
            OR
            A.MODEL LIKE CONCAT('%',#{goods.specModel,jdbcType=VARCHAR},'%')
            )
        </if>
        <if test="goods.brandName!=null and goods.brandName!=''">
            AND B.BRAND_NAME LIKE CONCAT('%',#{goods.brandName,jdbcType=VARCHAR},'%' )
        </if>
        <if test="goods.unitName!=null and goods.unitName!=''">
            AND C.UNIT_NAME LIKE CONCAT('%',#{goods.unitName,jdbcType=VARCHAR},'%' )
        </if>
        GROUP BY A.SKU_ID
    </select>
    <select id="getSkuGoodesUnrealfoBySku" resultType="com.vedeng.goods.model.CoreSkuGenerate">
        SELECT A.SKU_ID,A.SPU_ID,A.SKU_NAME,A.SKU_NO,
                A.STATUS,A.TAX_CATEGORY_NO,
               CONCAT_WS('&amp;',D.USERNAME,E.USERNAME) AS ASSIGNMENTS,
               A.COST_CATEGORY_ID,A.HAVE_STOCK_MANAGE,A.VIRTURE_CREATOR
        FROM V_CORE_SKU A
                 LEFT JOIN V_CORE_SPU B ON A.SPU_ID = B.SPU_ID
                 LEFT JOIN T_USER C ON A.VIRTURE_CREATOR = C.USER_ID
                 LEFT JOIN T_USER D ON B.ASSIGNMENT_MANAGER_ID = D.USER_ID
                 LEFT JOIN T_USER E ON B.ASSIGNMENT_ASSISTANT_ID = E.USER_ID
        WHERE A.IS_VIRTURE_SKU = 1
             AND A.SKU_ID = #{skuId,jdbcType=BIGINT}
    </select>

    <select id="checkSkuIsVirtual" resultType="java.lang.Integer">
        select  IFNULL(IS_VIRTURE_SKU,0)+IFNULL(STATUS,0) isVirtual  from V_CORE_SKU
        where SKU_NO= #{skuNo}
    </select>
    <select id="getAssignUserByGoodsId" resultType="com.vedeng.authorization.model.User">
        SELECT DISTINCT u.USER_ID,u.USERNAME from T_GOODS g1 LEFT JOIN V_CORE_SKU sku1 ON g1.SKU=sku1.SKU_NO
        LEFT JOIN V_CORE_SPU spu1 ON sku1.SPU_ID = spu1.SPU_ID
        LEFT JOIN T_USER u ON u.USER_ID=spu1.ASSIGNMENT_ASSISTANT_ID
        WHERE g1.GOODS_ID IN
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
</mapper>
