package com.vedeng.erp.kingdee.service;

import com.vedeng.erp.kingdee.dto.KingDeePurchaseBackDto;
import com.vedeng.erp.kingdee.dto.result.KingDeePurchaseBackQueryResultDto;
import com.vedeng.erp.kingdee.dto.result.KingDeePurchaseReceiptQueryResultDto;
import com.vedeng.infrastructure.kingdee.service.KingDeeMqBaseService;

import java.util.List;

/**
 * <AUTHOR>
 * @description 采购金蝶退货出库单
 * @date 2023/3/7 16:09
 **/
public interface KingDeePurchaseBackApiService extends KingDeeMqBaseService<KingDeePurchaseBackDto> {

    /**
     * 查询
     * @param outInNo
     * @return
     */
    List<KingDeePurchaseBackQueryResultDto> queryByOutInNo(String outInNo);
}
