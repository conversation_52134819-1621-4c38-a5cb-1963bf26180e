package com.vedeng.logistics.service.impl;

import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.logistics.eums.WarehouseGoodsOutEnum;
import com.vedeng.logistics.eums.WarehouseOutInSourceEnum;
import com.vedeng.logistics.model.vo.WarehouseGoodsOutLogVo;
import com.vedeng.logistics.model.vo.WarehouseGoodsOutVo;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.TraderAddress;
import com.vedeng.trader.model.TraderSupplier;
import com.vedeng.trader.model.vo.TraderSupplierVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @description: 销售换货出库
 * @author: Zeno.zuo
 * @email: <EMAIL>
 * @date: 2022/11/16 15:31
 **/
@Service
@Slf4j
public class SaleExchangeWarehouseGoodsOutDetailServiceImpl extends BaseWarehouseGoodsOutDetailService{

    /**
     * 销售换货售后详情页
     */
    private final String DETAIL_URL_AFTER_SALE_EXCHANGE = "/order/aftersalesUpgrade/viewAfterSalesDetail.do?afterSalesId=";



    @Override
    public WarehouseGoodsOutVo detailWarehouseGoodsOut(String outInNo) {
        WarehouseGoodsOutVo warehouseGoodsOutVo = commonDetailWarehouseGoodsOut(outInNo, WarehouseGoodsOutEnum.SALE_EXCHANGE_WAREHOUSE_OUT.getErpCode());
        if(warehouseGoodsOutVo != null){
            // 查询出库单据 关联的售后单信息
            warehouseGoodsOutRelateSaleOrderAfterSalesInfo(warehouseGoodsOutVo);

            if(WarehouseOutInSourceEnum.DIRECT_DELIVERY.getSource().equalsIgnoreCase(warehouseGoodsOutVo.getSource())){
                List<WarehouseGoodsOutLogVo> warehouseGoodsOutLogVoList = warehouseGoodsOutVo.getWarehouseGoodsOutLogList();
                for (WarehouseGoodsOutLogVo warehouseGoodsOutLogVo : warehouseGoodsOutLogVoList) {
                    warehouseGoodsOutLogVo.setSterilizationBatchNumber("");
                    warehouseGoodsOutLogVo.setVedengBatchNumber("");
                }

            }

        }
        return warehouseGoodsOutVo;
    }



    @Override
    public List<WarehouseGoodsOutLogVo> relatedNoWarehouseGoodsOutLogList(String relatedNo) {
        List<WarehouseGoodsOutLogVo> warehouseGoodsOutLogVoList = warehouseGoodsOutInMapper.selectWarehouseGoodsOutLogListByRelatedNo(WarehouseGoodsOutEnum.SALE_EXCHANGE_WAREHOUSE_OUT.getErpCode(), relatedNo);
        return CollectionUtils.isNotEmpty(warehouseGoodsOutLogVoList) ? warehouseGoodsOutLogVoList : new ArrayList<>();
    }

    @Override
    public String relatedNoDetailRedirect(String relatedNo) {
        AfterSales afterSales = afterSalesMapper.getAfterSalesByNo(relatedNo);
        String url = "";
        if (afterSales != null){
            url = REDIRECT_URL_PREFIX + DETAIL_URL_AFTER_SALE_EXCHANGE + afterSales.getAfterSalesId();
        }
        return url;
    }


    /**
     * 出库单详情 关联销售售后单信息
     * @param warehouseGoodsOutVo
     */
    protected void warehouseGoodsOutRelateSaleOrderAfterSalesInfo(WarehouseGoodsOutVo warehouseGoodsOutVo){
        String afterSalesNo = warehouseGoodsOutVo.getRelateNo();
        log.info("warehouseGoodsOutRelateSaleOrderAfterSalesInfo 出库单{} 关联查询 销售售后单 {} 信息", warehouseGoodsOutVo.getOutInNo(), afterSalesNo);
        if (StringUtils.isNotBlank(afterSalesNo)){
            AfterSalesVo afterSalesQuery = new AfterSalesVo();
            afterSalesQuery.setAfterSalesNo(afterSalesNo);
            AfterSalesVo afterSales = afterSalesMapper.viewAfterSalesDetailSaleorderByAfterSalesNo(afterSalesQuery);
            if (afterSales != null){
                // 归属人 收货客户 收货联系人信息
                warehouseGoodsOutVo.setBelongUserId(afterSales.getCreator());
                warehouseGoodsOutVo.setTakeTraderContactName(afterSales.getTraderContactName());
                warehouseGoodsOutVo.setTakeTraderContactTelephone(afterSales.getTraderContactTelephone());
                warehouseGoodsOutVo.setTakeTraderContactMobile(afterSales.getTraderContactMobile());
                warehouseGoodsOutVo.setTakeTraderName(afterSales.getTraderName());
                warehouseGoodsOutVo.setTakeArea(afterSales.getArea());
                warehouseGoodsOutVo.setTakeAddress(afterSales.getAddress());

                // 查询售后商品, 对金额，数量累计求和
                BigDecimal orderTotalAmount = new BigDecimal(0);
                if (afterSales.getAfterSalesId() != null){
                    AfterSalesGoods afterSalesGoodsQuery = new AfterSalesGoods();
                    afterSalesGoodsQuery.setAfterSalesId(afterSales.getAfterSalesId());
                    List<AfterSalesGoodsVo> afterSalesGoodsVoList = afterSalesGoodsMapper.getAfterSalesGoodsVosList(afterSalesGoodsQuery);
                    for (AfterSalesGoodsVo afterSalesGoodsVo : afterSalesGoodsVoList) {
                        orderTotalAmount = orderTotalAmount.add(afterSalesGoodsVo.getPrice().multiply(new BigDecimal(afterSalesGoodsVo.getNum())));
                    }
                }
                // 查询用户部门
                if(afterSales.getCreator() != null){
                    User belongUser = userMapper.selectByPrimaryKey(afterSales.getCreator());
                    warehouseGoodsOutVo.setBelongUserName(belongUser.getUsername());
                    warehouseGoodsOutVo.setBelongUserOrgName(belongUser.getOrgName());
                }
                warehouseGoodsOutVo.setOrderTotalAmount(orderTotalAmount);
            }
        }
    }
}
