<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.system.mapper.SysOptionDefinitionMapper">


    <select id="getOptionDefinitionListByParentId" resultType="com.vedeng.erp.system.dto.SysOptionDefinitionDto">
        SELECT
            SYS_OPTION_DEFINITION_ID,
            TITLE,
            SORT,
            PARENT_ID
        FROM
            T_SYS_OPTION_DEFINITION
        WHERE
            PARENT_ID = #{parentId,jdbcType=INTEGER}
            AND STATUS = 1
        order by sort
    </select>
    <select id="getOptionDefinitionByCode" resultType="com.vedeng.erp.system.dto.SysOptionDefinitionDto">
        SELECT
            SYS_OPTION_DEFINITION_ID
        FROM
            T_SYS_OPTION_DEFINITION
        WHERE
            OPTION_TYPE = #{code,jdbcType=VARCHAR}
            AND STATUS = 1
    </select>

    <select id="getOptionDefinitionByOptType" resultType="com.vedeng.erp.system.dto.SysOptionDefinitionDto">
        SELECT
            SYS_OPTION_DEFINITION_ID,
            TITLE,
            SORT,
            PARENT_ID
        FROM
            T_SYS_OPTION_DEFINITION
        WHERE
            OPTION_TYPE = #{code,jdbcType=VARCHAR}
          AND STATUS = 1
        LIMIT 100
    </select>
    <select id="getById" resultType="com.vedeng.erp.system.dto.SysOptionDefinitionDto">
        SELECT
        SYS_OPTION_DEFINITION_ID,
        STATUS,
        TITLE,
        PARENT_ID,
        SORT,
        COMMENTS
        FROM
            T_SYS_OPTION_DEFINITION
        WHERE
            SYS_OPTION_DEFINITION_ID = #{id,jdbcType=INTEGER}
            AND STATUS = 1
        LIMIT 1
    </select>


    <select id="getByIds" resultType="com.vedeng.erp.system.dto.SysOptionDefinitionDto">
        SELECT
        SYS_OPTION_DEFINITION_ID,
        STATUS,
        TITLE,
        PARENT_ID,
        SORT
        from T_SYS_OPTION_DEFINITION
        where
            1=1 and
        SYS_OPTION_DEFINITION_ID in
        <foreach item="sysOptionDefinitionId" index="index" collection="list" open="(" separator="," close=")">
            #{sysOptionDefinitionId}
        </foreach>
    </select>
    <select id="getByParentIdList" resultType="com.vedeng.erp.system.dto.SysOptionDefinitionDto">
        SELECT
        SYS_OPTION_DEFINITION_ID,
        TITLE,
        PARENT_ID,
        SORT
        from T_SYS_OPTION_DEFINITION
        where
        `STATUS`=1
          <if test="list.size() > 0">
              and PARENT_ID in
              <foreach item="parentId" index="index" collection="list" open="(" separator="," close=")">
                  #{parentId}
              </foreach>
          </if>
        order by SORT asc
    </select>

    <update id="updateInvoiceSwitch">
        update T_SYS_OPTION_DEFINITION set STATUS = #{status,jdbcType=INTEGER}
        where SYS_OPTION_DEFINITION_ID = #{sysOptionDefinitionId,jdbcType=INTEGER}
        and STATUS = #{oldStatus,jdbcType=INTEGER}
    </update>
</mapper>