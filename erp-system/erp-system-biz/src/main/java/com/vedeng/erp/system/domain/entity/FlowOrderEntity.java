package com.vedeng.erp.system.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 业务流转单主表
 */
@Getter
@Setter
public class FlowOrderEntity extends BaseEntity {
    /**
     * 主键
     */
    private Long flowOrderId;

    /**
     * 业务流转单编号
     */
    private String flowOrderNo;

    /**
     * 基础业务订单ID
     */
    private Integer baseOrderId;

    /**
     * 基础业务订单编号
     */
    private String baseOrderNo;

    /**
     * 基础业务类型 1.采购 2.销售
     */
    private Integer baseBusinessType;

    /**
     * 审核状态，0:未审核, 1:已审核
     */
    private Integer auditStatus;

    /**
     * 审核人id
     */
    private Integer auditUserId;

    /**
     * 审核人
     */
    private String auditUsername;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 末级合同状态：0-无需上传（末级节点为贝登子公司），1-未上传（末级节点非贝登子公司且未上传合同），2-已上传（末级节点非贝登子公司且已上传合同）
     */
    private Integer contractStatus;

    /**
     * 推送方向：1-金蝶, 2-ERP
     */
    private Integer pushDirection;

    /**
     * 来源ERP系统名称
     */
    private String sourceErp;
}