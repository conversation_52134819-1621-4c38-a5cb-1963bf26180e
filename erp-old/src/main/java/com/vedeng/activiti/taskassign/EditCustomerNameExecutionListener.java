package com.vedeng.activiti.taskassign;

import java.math.BigDecimal;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.xml.ws.WebServiceContext;

import com.alibaba.fastjson.JSON;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.vedeng.trader.model.vo.TraderSupplierVo;
import com.wms.constant.WMSContant;
import com.wms.dto.WmsResponse;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.WmsInterface;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.DateUtil;
import com.vedeng.finance.model.TraderAccountPeriodApply;
import com.vedeng.goods.model.Goods;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderModifyApply;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.soap.service.VedengSoapService;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.TraderCustomer;
import com.vedeng.trader.service.TraderCustomerService;

public class EditCustomerNameExecutionListener implements ExecutionListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(EditCustomerNameExecutionListener.class);
    // 运行时注入service
    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
    private TraderCustomerService traderCustomerService = (TraderCustomerService) context.getBean("traderCustomerService");
    @Resource
    private WebServiceContext webServiceContext;
    private WMSInterfaceFactory wmsInterfaceFactory=(WMSInterfaceFactory)context.getBean("wmsInterfaceFactory");

    //产品审核触发器
    //根据穿参通用回写主表中状态
    public void notify(DelegateExecution execution) throws Exception {
		ServletRequestAttributes ra= (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		HttpServletRequest request =  ra.getRequest();
		User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		Trader trader = (Trader) execution.getVariable("trader");
		ResultInfo res = traderCustomerService.saveTraderName(trader);

		//客户名称修改审核成功 下传WMS
        try{
            trader.setCustomerType(1);//客户
            LOGGER.info("WMS客户资料下传接口的请求:" + JSON.toJSONString(trader));
            WmsInterface putCustomerInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_CUSTOMER);
            WmsResponse response = putCustomerInterface.request(trader);
            LOGGER.info("WMS客户资料下传接口的响应:" + JSON.toJSONString(response));
        }catch (Exception e){
            LOGGER.error("客户名称修改审核通过,请求下传WMS接口报错",e);
        }


    }
}

