$(function() {
	$("#form").submit(function(){
		checkLogin();
		$(".warning").remove();
		$("input").removeClass("errorbor");
		$("select").removeClass("errorbor");
		if($("#traderContactId").val() == 0 && !($("#coidTypeWx").val() == "Y")){
			warnTips("traderContactId","联系人不允许为空");
			return false;
		}
		
		if($("#begin").val() == ''){
			$("#begin").addClass("errorbor");
			$("#end").after('<div class="warning">沟通开始时间不允许为空</div>');
			return false;
		}
		
		if($("#end").val() == ''){
			$("#end").addClass("errorbor");
			$("#end").after('<div class="warning">沟通结束时间不允许为空</div>');
			return false;
		}
		/*	2020.12http://jira.ivedeng.com/browse/VDERP-5008 需求中要求删除的字段*/
	/*	if($("input[name='communicateGoal']:checked").length == 0){
			$("#communicateGoalDiv").after('<div class="warning">沟通目的不允许为空</div>');
			return false;
		}
		if($("input[name='communicateMode']:checked").length == 0){
			$("#communicateModeDiv").after('<div class="warning">沟通方式不允许为空</div>');
			return false;
		}*/
		
		if($("input[name='tagId']").length == 0 && $("input[name='tagName']").length == 0 && $("textarea[name='contentSuffix']").val() == ''){
			$(".addtags").show();
			warnTips("tag_show_ul"," 沟通内容不允许为空");
			return false;
		}

		if ($("textarea[name='contentSuffix']").val().length > 200){
			warnTips("contentSuffixError"," 沟通内容最多输入200字符，请检查后提交");
			return false;
		}
		
		var nextDate = $("input[name='nextDate']").val();

		if (! $('#noneNextDate').is(':checked') && nextDate.length == 0){
			warnTips("nextDate"," 下次沟通时间不允许为空");
			return false;
		}

		if(nextDate.length != 0 && $("#begin").val()>=nextDate+' 23:59:59'){
			warnTipsDate("nextDate","下次沟通时间不能在沟通时间之前");
			return false;
		}
		
		if($("#nextContactContent").val().length > 256){
			warnTips("nextContactContent"," 下次沟通内容长度不允许超过256个字符");
			return false;
		}
		if($("#comments").val().length > 128){
			warnTips("comments"," 备注长度不允许超过128个字符");
			return false;
		}
		return true;
	});

	$('#noneNextDate').click(function () {
		var noneNextDate = $('#noneNextDate').is(':checked');
		if (noneNextDate){
			$('#nextDate').val('');
			$('#nextDate').css('background-color', '#CCCCCC');
			$('#nextDate').attr('disabled', true);
			$('#noneNextDateVal').val(1);
		} else {
			$('#nextDate').css('background-color', 'white');
			$('#nextDate').attr('disabled', false);
			$('#noneNextDateVal').val(0);
		}
	})
});
