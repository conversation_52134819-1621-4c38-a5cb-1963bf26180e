package com.vedeng.order.service.impl;

import com.vedeng.aftersales.dao.AfterSalesInstallstionMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.order.service.AfterSalesDataService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

@Service("afterSalesDataService")
public class AfterSalesDataServiceImpl implements AfterSalesDataService {
    @Resource
    private AfterSalesMapper afterSalesMapper;

    @Resource
    private AfterSalesInstallstionMapper afterSalesInstallstionMapper;

    @Override
    public BigDecimal getPayAmount(Integer afterSalesId, Integer orderType) {
        return afterSalesMapper.getPayAmount(afterSalesId, orderType);
    }

    @Override
    public BigDecimal getRefundAmount(Integer afterSalesId, Integer orderType) {
        return afterSalesMapper.getRefundAmount(afterSalesId, orderType);
    }

    @Override
    public BigDecimal getSaleorderAfterSalesPayAmount(Integer afterSalesId) {
        return afterSalesMapper.getSaleorderAfterSalesPayAmount(afterSalesId);
    }

    @Override
    public BigDecimal getPeriodAmount(Integer afterSalesId, Integer orderType) {
        return afterSalesMapper.getPeriodAmount(afterSalesId, orderType);
    }

    @Override
    public BigDecimal getRefundTraderAmount(Integer afterSalesId, Integer orderType) {
        return afterSalesMapper.getRefundTraderAmount(afterSalesId, orderType);
    }

    @Override
    public BigDecimal getRefundVedengAmount(Integer afterSalesId, int orderType) {
        return afterSalesMapper.getRefundVedengAmount(afterSalesId, orderType);
    }

    @Override
    public BigDecimal getRefundAmountBySaleorderId(Integer saleorderId) {
        return afterSalesMapper.getRefundAmountBySaleorderId(saleorderId);
    }

    @Override
    public BigDecimal getRefundBalanceAmountBySaleorderId(Integer saleorderId) {
        return afterSalesMapper.getRefundBalanceAmountBySaleorderId(saleorderId);
    }

    @Override
    public BigDecimal getAfterSalesInstallstionAmount(Integer afterSalesId) {
        return afterSalesInstallstionMapper.getAfterSalesInstallstionAmount(afterSalesId);
    }

}
