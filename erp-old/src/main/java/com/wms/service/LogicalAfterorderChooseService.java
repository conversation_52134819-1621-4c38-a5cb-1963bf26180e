package com.wms.service;

import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.authorization.model.User;
import com.wms.model.po.WmsLogicalOrdergoods;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName LogicalAfterorderChooseService.java
 * @Description TODO
 * @createTime 2020年07月28日 14:19:00
 */
public interface LogicalAfterorderChooseService {
    /**
     * @description: 售后单指定逻辑仓
     * @return:
     * @author: Strange
     * @date: 2020/7/28
     **/
    public void chooseLogicalAfterorder(AfterSales afterSales, User user) throws Exception;

    /**
     * @description: 下发换货单
     * @return:
     * @author: Strange
     * @date: 2020/8/14
     **/
    public void putExchangeWMS(AfterSales afterSales, List<WmsLogicalOrdergoods> wmsLogicalOrdergoodsList, User user) throws Exception;

    /**
     * @description: 关闭生效换货单 归还库存占用
     * @return:
     * @author: Strange
     * @date: 2021/7/7
     **/
    void closeExchangeAfterOrder(AfterSalesVo afterSales);

    /**
     * 采购换货单确认完成
     * @param afterSaleId 采购售后id
     */
    void confirmExchangeAfterOrder(Integer afterSaleId);
}
