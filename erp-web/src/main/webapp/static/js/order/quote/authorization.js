// 全选/反选功能
function checkAllProduct(checkbox) {
	var checkboxes = document.querySelectorAll('.productCheck');
	checkboxes.forEach(function(cb) {
		if (!cb.disabled) {
			cb.checked = checkbox.checked;
		}
	});
}
function showSku() {
	// 动态生成产品列表
	var productList = document.getElementById('productList');
	productList.innerHTML = ''; // 清空之前的列表

	// var productSelectMessage  = document.getElementById('productSelectMessage');
	// productSelectMessage.innerHTML = ''; // 清空之前提示
	var selectLi = $("#applyList li");//第一次进页面，未选择商品，选择2个，确定，再点击进行选择，再反选1个，再确定，如果发现已经有选中的商品，需要将products里的变量check设置为未选中
	if (selectLi.length > 0) {
		products.forEach(function(cb) {
			if(cb.check != 2){//如果是禁用的，则不能覆盖值
				cb.check = 0;
			}
		});
	}
	products.forEach(function(cb) {
		//取ul中的元素
		var skuId = cb.skuId;
		var targetLi = $("#applyList li[id='" + skuId + "']");
		// 现在 targetLi 就是你要找的 li 元素
		if (targetLi.length > 0) {
			console.log("找到了匹配的 li 元素:", targetLi);
			// 获取 li 元素的 data 属性值（假设 data 是一个 JSON 字符串）
			var data = targetLi.attr("data");
			try {
				// 将 data 解析成 JSON 对象
				var parsedData = JSON.parse(data);
				// 覆盖 cb 的值
				Object.assign(cb, parsedData);
				console.log("cb 的值已更新:", cb);
			} catch (e) {
				console.error("解析 data 失败:", e);
			}
		} else {
			console.log("没有找到匹配的 li 元素");
		}
	})




	var rows = products.map((product, index) => {
		return `<tr>
                <td><input type="checkbox" class="productCheck" ${product.check == 1?'checked':'' }  ${product.check == 2?'disabled':'' }  value="${index}" data="${product.skuId}" skuName="${product.skuName}" skuModel="${product.skuModel}" brandName="${product.brandName}" style="margin-left: 10px;">${product.check == 2?'<font color="red">已授权</font>':'' }</td>
                <td>${product.brandName} </td>
                <td>${product.skuName}</td>
                <td><input type="text" class="input-print xl" value="${product.productCompany || ''}" onblur="if($(this).val().trim().length>0){$(this).parent().find('.errorTips').hide();}"
                               name="productCompany" maxlength="200" placeholder="请填写生产厂家/代理" style="width:190px;" ${product.check == 2?'disabled':'' }
                               maxlength="200">
                     <span class="errorTips" name="productCompanyError">请填写厂家/代理名称</span>
                     <span class="errorTips" name="productCompanyError2">厂家/代理名称至少4位</span>
                               </td>
                <td>
                 	<div class="radio-group">
						<label>
							<input type="radio" class="natureOfOperation" ${product.check == 2?'disabled':'' } onclick="$(this).parent().parent().find('.errorTips').hide();" name="natureOfOperation_${index}" value="1" ${product.natureOfOperation == 1 ? 'checked' : ''} /> 生产
						</label>
						<br/>
						<label>
							<input type="radio" class="natureOfOperation"  ${product.check == 2?'disabled':'' } onclick="$(this).parent().parent().find('.errorTips').hide();" name="natureOfOperation_${index}" value="2" ${product.natureOfOperation == 2 ? 'checked' : ''} /> 代理销售
						</label>
						<span class="errorTips" name="natureOfOperationError">请选择类型</span>
                     </div>
                </td>
                <td>
                    <div class="radio-group">
                        <label>
                            <input type="radio" class="distributionsType"  ${product.check == 2?'disabled':'' } onclick="$(this).parent().parent().find('.errorTips').hide();" name="distributionsType_${index}" value="1" ${product.distributionsType == 1 ? 'checked' : ''} /> 独家经销商
                        </label>
                        <label>
                            <input type="radio"  class="distributionsType" ${product.check == 2?'disabled':'' }  onclick="$(this).parent().parent().find('.errorTips').hide();" name="distributionsType_${index}" value="2" ${product.distributionsType == 2 ? 'checked' : ''} /> 经销商
                        </label>
                        <label>
                            <input type="radio" class="distributionsType"  ${product.check == 2?'disabled':'' }  onclick="$(this).parent().parent().find('.errorTips').hide();" name="distributionsType_${index}" value="3" ${product.distributionsType == 3 ? 'checked' : ''} /> 代理商
                        </label>
                        <span class="errorTips" name="distributionsTypeError">请选择贝登渠道</span>
                    </div>
                </td>
            </tr>`;
	}).join('');

	productList.innerHTML = rows;
	//check
	var s = document.getElementById('productSelectDialog');
	// 打开弹框
	layer.open({
		type: 1,
		title: '选择授权产品',
		content: $(s) ,
		area: ['800px', '400px'],
		btn: ['确认', '取消'],
		yes: function(index, layero){
			// productSelectMessage.innerHTML = '';
			// 获取用户选择的产品信息
			var selectedProducts = [];
			var hasError = false;
			document.querySelectorAll('.productCheck').forEach(function(checkbox) {
				if( !checkbox.checked) {//反选的清况下
					// 遍历 products 数组
					products.forEach(function(product) {
						// 如果 skuId 等于 'A'，则将 skuName 清空
						if (product.skuId == $(checkbox).attr("data") && product.check != 2) {
							product.natureOfOperation = '';
							product.distributionsType = '';
							product.productCompany = '';
						}
					});
				}
				if (checkbox.checked) {
					var skuId = $(checkbox).attr("data");
					var skuName = $(checkbox).attr("skuName");
					var brandName = $(checkbox).attr("brandName");
					var skuModel = $(checkbox).attr("skuModel");
					var productCompany = $(checkbox).parent().parent().find("[name='productCompany']").val();
					if(productCompany.length<1){
						hasError = true;
						$(checkbox).parent().parent().find("[name='productCompanyError']").show();
					}else if(productCompany.length<4){
						hasError = true;
						$(checkbox).parent().parent().find("[name='productCompanyError2']").show();
					}
					var natureOfOperation = $(checkbox).parent().parent().find(".natureOfOperation:checked").val();
					var distributionsType = $(checkbox).parent().parent().find(".distributionsType:checked").val();
					if(natureOfOperation == undefined){
						hasError = true;
						$(checkbox).parent().parent().find("[name='natureOfOperationError']").show();
					}
					if(distributionsType == undefined){
						hasError = true;
						$(checkbox).parent().parent().find("[name='distributionsTypeError']").show();
					}

					// var product = products.find(p => p.skuId === checkbox.value);
					selectedProducts.push({
						"check":1,
						"skuName":skuName,
						"brandName":  brandName,
						"distributionsType": distributionsType, // 假设值，需要根据实际情况获取
						"natureOfOperation": natureOfOperation,
						"productCompany": productCompany,
						"skuId": skuId,
						"skuModel":skuModel
					});
				}
			});
			if(hasError){ // 如果有报错。中断操作，错误已在遍历中展示
				return ;
			}
			if(selectedProducts.length <1){
                layer.msg('请勾选授权产品');
				return ;
                // productSelectMessage.innerHTML = '';
			}
			//将selectedProducts遍历一遍
			showUl(selectedProducts);
			// 关闭弹框
			layer.close(index);

			// 返回选择的产品信息
			console.log(selectedProducts);
		},
		btn2: function(index, layero){
			layer.close(index);
		}
	});
}

function showUl(selectedProducts){
	if(selectedProducts && selectedProducts.length <1){
		return ;
	}
	var lis = selectedProducts.map((product, index) => {
		var productStr = JSON.stringify(product);
		return `<li data='${productStr}' title="第${index+1}页 ${product.skuName}" onclick="showLi(this,${index+1})" id="${product.skuId}"><span>${product.skuName}</span></li>`;
	}).join('');
	$("#draftSkuJson").val(JSON.stringify(selectedProducts));//将商品信息全部存入到draftSkuJson对象中。
	$("#applyList").find("ul").html(lis);
	$("#applyList").show();
	$("#applyList-page-line").show();
	$("#applyList").find("li")[0].click();
}

// 全选/反选功能
function checkAll(checkbox) {
	var checkboxes = document.querySelectorAll('.productCheck');
	checkboxes.forEach(function(cb) {
		cb.checked = checkbox.checked;
	});
}

function showLi(t,num){
	var disabledArrayObj = '#purchaseOrBidding, #traderName, #purchaseProjectName, #purchaseProjectNum, #fileType, #authorizedCompany, #aftersalesCompany, #beginTime, #endTime, #applyYear, #applyMonth, #applyDay';
	if(num==1){//第一个
		$(disabledArrayObj).prop('readonly', false);
		$(disabledArrayObj).css("background-color","");
		$(disabledArrayObj).off("click");
		$(".select-print").css("border-color","#0099FF");
		$(".input-print").css("border-color","#0099FF");
	}else{
		$(disabledArrayObj).prop('readonly', true);
		$(disabledArrayObj).css("background-color","#F5F7FA")
		$(disabledArrayObj).css("border-color","");
		$(disabledArrayObj).on("click",function(){
			setTimeout(function () {
				console.log('1000模拟点击一下');
				console.log($(".title:last"));
				$(document).click();
				// document.onclick();
			},1000);
			layer.open({
				type: 1,
				title: ' ',
				content: '<div style="font-size: 14px;margin-left: 20px;margin-top:10px;">批量申请授权时主体信息需相同，可至第一页编辑主体信息。</div>' ,
				area: ['430px', '160px'],
				btn: ['去编辑', '取消'],
				yes: function(index, layero){
					$("#applyList").find("li").first().click();
					// 关闭弹框
					layer.close(index);
				},
				btn2: function(index, layero){
					layer.close(index);
				}
			});
		})
		// $(".select-print").css("border-color","");
		// $(".input-print").css("border-color","");
	}
	var obj = $(t).attr("data");
	var jsonObj = JSON.parse(obj);
	var skuName = jsonObj.skuName,
		brandName = jsonObj.brandName,
		distributionsType = jsonObj.distributionsType,
		natureOfOperation = jsonObj.natureOfOperation,
		skuModel = jsonObj.skuModel,
		productCompany = jsonObj.productCompany;
	$(t).parent().find("li").removeClass("active");
	$(t).addClass("active");
	$("#productCompany").val(productCompany);
	$("#model").val(skuModel);
	$("#natureOfOperation").val(natureOfOperation);
	$("#brandName").val(brandName);
	$("#skuName").val(skuName);
	$("#distributionsType").val(distributionsType);

}


function conadd(){
	var num = Number($("#conadd").siblings(".form-blanks").length)+Number(1);
	var div = '<div class="form-blanks mt10">'+
		'<div class="pos_rel f_left mb8 ">'+
		'<input type="file" class="upload_file" id="file_'+num+'" name="lwfile" style="display: none;" onchange="uploadFile(this,'+num+');">'+
		'<input type="text" class="input-middle" style="margin-right:10px;" id="name_'+num+'" readonly="readonly" placeholder="请上传附件" name="fileName" onclick="file_'+num+'.click();" value ="">'+
		'<input type="hidden" id="uri_'+num+'" name="fileUri" >'+
		'<label class="bt-bg-style bt-middle bg-light-blue ml4" type="file" onclick="return $(\'#file_'+ num +'\').click();">浏览</label>'+
		'</div>'+
		'<div class="f_left ">'+
		'<i class="iconsuccesss mt5 none" id="img_icon_'+num+'"></i>'+
		'<a href="" target="_blank" class="font-blue cursor-pointer mr5 ml10 mt4 none" id="img_view_'+num+'" style="margin:0px 8px 0 13px;">查看</a>'+
		'<span class="font-red cursor-pointer mt4" onclick="del('+num+')" id="img_del_'+num+'">删除</span>'+
		'</div>'+
		'<div class="clear"></div></div>';
	$("#conadd").before(div);
}

function clickNotBiaoZhun(){
	if($('#img_del_non') && !$('#img_del_non').is(':hidden')){
		var num = 'non';
		var sum = Number($("#conadd").siblings(".form-blanks").length);
		var uri = $("#uri_"+num).val();
		if(uri == '' && sum > 1){
			$("#img_del_"+num).parent().parent(".form-blanks").remove();
		}else{
			$("#img_icon_" + num).hide();
			$("#img_view_" + num).hide();
			if (num == 1 || num == "non") {
				$("#img_del_" + num).hide();
			}
			$("#whether_sign_tip").hide();
			$("#name_"+ num).val("");
			$("#uri_"+ num).val("");
			$("#file_"+ num).val("");
		}
	}
	$("#notBiaoZhunDiv").show();

}

function clickBiaoZhun(){
	if($('#img_del_non') && !$('#img_del_non').is(':hidden')){
		var num = 'non';
		var sum = Number($("#conadd").siblings(".form-blanks").length);
		var uri = $("#uri_"+num).val();
		if(uri == '' && sum > 1){
			$("#img_del_"+num).parent().parent(".form-blanks").remove();
		}else{
			$("#img_icon_" + num).hide();
			$("#img_view_" + num).hide();
			if (num == 1 || num == "non") {
				$("#img_del_" + num).hide();
			}
			$("#whether_sign_tip").hide();
			$("#name_"+ num).val("");
			$("#uri_"+ num).val("");
			$("#file_"+ num).val("");
		}
	}
	$("#notBiaoZhunDiv").hide();

}


function del(num){
	var sum = Number($("#conadd").siblings(".form-blanks").length);
	var uri = $("#uri_"+num).val();
	if(uri == '' && sum > 1){
		$("#img_del_"+num).parent().parent(".form-blanks").remove();
	}else{
		index = layer.confirm("您是否确认该操作？", {
			btn: ['确定','取消'] //按钮
		}, function(){
			$("#img_icon_" + num).hide();
			$("#img_view_" + num).hide();
			if (num == 1 || num == "non") {
				$("#img_del_" + num).hide();
			}
			$("#whether_sign_tip").hide();
			$("#name_"+ num).val("");
			$("#uri_"+ num).val("");
			$("#file_"+ num).val("");
			layer.close(index);
		}, function(){
		});
	}
}

function uploadFile(obj,num){
	checkLogin();
	var imgPath = $(obj).val();
	if(imgPath == '' || imgPath == undefined){
		return false;
	}
	var oldName=imgPath.substr(imgPath.lastIndexOf('\\')+1);
	var domain = $("#domain").val();
	//判断上传文件的后缀名
	var strExtension = imgPath.substr(imgPath.lastIndexOf('.') + 1);
	if (strExtension != 'jpg' && strExtension != 'JPG' && strExtension != 'png' && strExtension != 'PNG'
		&& strExtension != 'pdf' && strExtension != 'PDF' && strExtension != 'doc' && strExtension != 'DOC' && strExtension != 'docx' && strExtension != 'DOCX') {
		layer.alert("文件格式不正确,仅支持PDF、WORD、JPG、PNG");
		// 清空url
		$(obj).val("");
		return false;
	}
	var fileSize = 0;
	var isIE = /msie/i.test(navigator.userAgent) && !window.opera;
	if (isIE && !obj.files) {
		var filePath = obj.value;
		var fileSystem = new ActiveXObject("Scripting.FileSystemObject");
		var file = fileSystem.GetFile (filePath);
		fileSize = file.Size;
	}else {
		fileSize = obj.files[0].size;
	}
	fileSize=Math.round(fileSize/1024*100)/100; //单位为KB
	if(fileSize>20480){
		layer.alert("上传附件不得超过20M");
		return false;
	}
	$.ajaxFileUpload({
		url : page_url + '/fileUpload/ajaxFileUploadAuthorization.do', //用于文件上传的服务器端请求地址
		secureuri : false, //一般设置为false
		fileElementId : $(obj).attr("id"),
		dataType : 'json',//返回值类型 一般设置为json
		complete : function() {//只要完成即执行，最后执行
		},
		//服务器成功响应处理函数
		success : function(data) {
			if (data.code == 0) {
				$("#name_"+num ).val(oldName);
				$("#uri_"+num ).val(data.filePath);
				$("#img_icon_" + num).attr("class", "iconsuccesss ml7").show();
				$("#img_view_" + num).attr("href", 'http://' + domain + data.filePath).show();
				$("#img_del_" + num).show();
			} else {
				layer.alert(data.message);
			}
		},
		//服务器响应失败处理函数
		error : function(data, status, e) {
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}else{
				layer.alert(data.responseText);
			}

		}
	});
}

/**
 * 上传文件-非标授权书附件
 * @param obj
 * @param num
 * @returns {boolean}
 */
function uploadFileNonStandardAuthorization(obj, num) {
	checkLogin();
	var imgPath = $(obj).val();
	if (imgPath == '' || imgPath == undefined) {
		return false;
	}
	var oldName = imgPath.substr(imgPath.lastIndexOf('\\') + 1);
	var domain = $("#domain").val();
	//判断上传文件的后缀名
	var strExtension = imgPath.substr(imgPath.lastIndexOf('.') + 1);
	if (strExtension != 'pdf' && strExtension != 'PDF') {
		layer.alert("仅限上传PDF");
		// 清空url
		$(obj).val("");
		return false;
	}

	var fileSize = 0;
	var isIE = /msie/i.test(navigator.userAgent) && !window.opera;
	if (isIE && !obj.files) {
		var filePath = obj.value;
		var fileSystem = new ActiveXObject("Scripting.FileSystemObject");
		var file = fileSystem.GetFile(filePath);
		fileSize = file.Size;
	} else {
		fileSize = obj.files[0].size;
	}
	fileSize = Math.round(fileSize / 1024 * 100) / 100; //单位为KB
	if (fileSize > 20480) {
		layer.alert("上传附件不得超过20M");
		return false;
	}

	$.ajaxFileUpload({
		url: page_url + '/fileUpload/ajaxFileUploadAuthorization.do', //用于文件上传的服务器端请求地址
		secureuri: false, //一般设置为false
		fileElementId: $(obj).attr("id"),
		dataType: 'json',//返回值类型 一般设置为json
		complete: function () {//只要完成即执行，最后执行
		},
		//服务器成功响应处理函数
		success: function (data) {
			if (data.code == 0) {
				$("#name_" + num).val(oldName);
				$("#uri_" + num).val(data.filePath);
				$("#img_icon_" + num).attr("class", "iconsuccesss ml7").show();
				$("#img_view_" + num).attr("href", 'http://' + domain + data.filePath).show();
				$("#img_del_" + num).show();
				$.ajax({
					url: page_url + '/order/quote/whetherIncludeText.do?url=' + data.filePath + '&sealType=' + $("#sealType").val(),
					type: "POST",
					dataType: "json",
					success: function (req) {
						if(req.code == 0){
							$("#whether_sign").val(1);
							$("#whether_sign_tip").hide();
						}
						if(req.code == -1){
							$("#whether_sign").val(0);
							$("#whether_sign_tip").show();
						}
					},
					error: function (req) {
					}
				});
			} else {
				layer.alert(data.message);
			}
		},
		//服务器响应失败处理函数
		error: function (data, status, e) {
			if (data.status == 1001) {
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。请求的接口地址是：" + ((this.url && this.url.startsWith("http")) == true ? (new URL(this.url).pathname) : this.url));
			} else {
				layer.alert(data.responseText);
			}

		}
	});
}


function bc(){
	var draftSkuJson = $("#draftSkuJson").val();
	if (draftSkuJson.length<1){
		layer.confirm("请选择商品", {
			btn : [ '确定' ]
			//按钮
		}, function() {
			layer.closeAll();
		}, function() {
		});
		return;
	}
	var loadingIndex = layer.open({
		type: 3,
		content: '提交中...',
		shade: [0.5, '#000']
	});
	checkLogin();

	$.ajax({
		url:page_url + '/order/quote/authorizationStorage.do',
		data:$('#tjform').serialize(),
		type:"POST",
		dataType : "json",
		success:function(data){
			layer.close(loadingIndex);
			location.reload();
		},
		error:function(data){
			layer.close(loadingIndex);
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});
}

function tj(){
	var loadingIndex = layer.open({
		type: 3,
		content: '提交中...',
		shade: [0.5, '#000']
	});
	checkLogin();
	var s=$('#purchaseOrBidding').val();
	var purchaseOrBiddingNum=$('#purchaseOrBidding').val().length;
	if (purchaseOrBiddingNum<4 || purchaseOrBiddingNum>200){
		layer.close(loadingIndex);
		layer.confirm("采购单位/招标公司请务必输入4-200的字符", {
			btn : [ '确定' ]
			//按钮
		}, function() {
			layer.closeAll();
		}, function() {
		});
		return;
	}
	var productCompanyNum=$('#productCompany').val().length;
	if (productCompanyNum<4 || productCompanyNum>200){
		layer.close(loadingIndex);
		layer.confirm("生产厂家请务必输入4-200的字符", {
			btn : [ '确定' ]
			//按钮
		}, function() {
			layer.closeAll();
		}, function() {
		});
		return;
	}
	var purchaseProjectNameNum=$('#purchaseProjectName').val().length;
	var aftersalesCompanyNum=$('#aftersalesCompany').val().length;
	if (aftersalesCompanyNum<4 || aftersalesCompanyNum>200){
		layer.confirm("售后支持公司全称请务必输入4-200的字符", {
			btn : [ '确定' ]
			//按钮
		}, function() {
			layer.closeAll();
		}, function() {
		});
		return;
	}

	var skuNameNum=$('#skuName').val().length;
	var brandNameNum=$('#brandName').val().length;
	if (skuNameNum==0 || brandNameNum==0){
		layer.close(loadingIndex);
		layer.confirm("产品品牌/型号/名称不得为空", {
			btn : [ '确定' ]
			//按钮
		}, function() {
			layer.closeAll();
		}, function() {
		});
		return;
	}

	var beginTimeNum=$('#beginTime').val().length;
	var endTimeNum=$('#endTime').val().length;
	if (beginTimeNum==0 || endTimeNum==0){
		layer.close(loadingIndex);
		layer.confirm("开始时间/结束时间不能为空", {
			btn : [ '确定' ]
			//按钮
		}, function() {
			layer.closeAll();
		}, function() {
		});
		return;
	}

	var startDate=$('#beginTime').val()
	var endDate=$('#endTime').val()
	var startTime = new Date(Date.parse(startDate.replace(/-/g, "/"))).getTime();
	var endTime = new Date(Date.parse(endDate.replace(/-/g, "/"))).getTime();
	var dates = (endTime - startTime)/(1000*60*60*24);
	var authType=$("input[name='authType']:checked").val();
	if(authType == undefined){
		layer.close(loadingIndex);
		layer.confirm("请选择授权类型", {
			btn : [ '确定' ]
			//按钮
		}, function() {
			layer.closeAll();
		}, function() {
		});
		return;
	}

	if (dates>90 && authType == 0){
		layer.close(loadingIndex);
		layer.confirm("授权日期不能超过90天", {
			btn : [ '确定' ]
			//按钮
		}, function() {
			layer.closeAll();
		}, function() {
		});
		return;
	}

	if (dates>365 && authType == 1){
		layer.close(loadingIndex);
		layer.confirm("授权日期不能超过90天", {
			btn : [ '确定' ]
			//按钮
		}, function() {
			layer.closeAll();
		}, function() {
		});
		return;
	}

	if (dates<0){
		layer.close(loadingIndex);
		layer.confirm("开始日期不得大于结束日期", {
			btn : [ '确定' ]
			//按钮
		}, function() {
			layer.closeAll();
		}, function() {
		});
		return;
	}

	var auNumNum=$('#auNum').val().length;
	if (auNumNum==0){
		layer.close(loadingIndex);
		layer.confirm("请输入正整数的份数", {
			btn : [ '确定' ]
			//按钮
		}, function() {
			layer.closeAll();
		}, function() {
		});
		return;
	}

    var purchaseProjectNumNum1=$('#purchaseProjectNum').val().length;
    var describedNum=$('#described').val().length;
    var standardTemplateStr=$("input[name='standardTemplate']:checked").val();

	var nonStandardAuthorizationUrl = $('[name="nonStandardAuthorizationUrl"]').val().length;
	if(standardTemplateStr==1 && nonStandardAuthorizationUrl==0){
		layer.close(loadingIndex);
		layer.confirm("非标授权,须上传“非标授权附件”", {
			btn : [ '确定' ]
			//按钮
		}, function() {
			layer.closeAll();
		}, function() {
		});
		return;
	}

    if(standardTemplateStr==1 && describedNum==0){
		layer.close(loadingIndex);
        layer.confirm("非标准模板,请填写事项描述", {
            btn : [ '确定' ]
            //按钮
        }, function() {
            layer.closeAll();
        }, function() {
        });
        return;
    }



	// if ((purchaseProjectNameNum==0 || purchaseProjectNumNum1==0) && standardTemplateStr==0){
	// 	layer.confirm("标准模板，请填写项目名称和项目编号", {
	// 		btn : [ '确定' ]
	// 		//按钮
	// 	}, function() {
	// 		layer.closeAll();
	// 	}, function() {
	// 	});
	// 	return
	// }





	$.ajax({
		async:false,
		url:page_url + '/order/quote/checkAuthorizationApply.do',
		data:$('#tjform').serialize(),
		type:"POST",
		dataType : "json",
		success:function(data){
			if (data.data){
					$.ajax({
						async:false,
						url:page_url + '/order/quote/authorizationApply.do',
						data:$('#tjform').serialize(),
						type:"POST",
						dataType : "json",
						success:function(data2){
							if (data2.data){
								layer.close(loadingIndex);
								pagesContrlpages(true,false,true);
							} else {
								layer.close(loadingIndex);
								layer.confirm(data2.message, {
									btn : [ '确定']
									//按钮
								}, function() {
									window.location.reload();
								});
							}
						},
						error:function(data2){
							layer.close(loadingIndex);
							if(data2.status ==1001){
								layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
							}
						}
					});

			} else {
				layer.close(loadingIndex);
				layer.confirm(data.message, {
					btn : [ '确定']
					//按钮
				}, function() {
					layer.closeAll();
				});

			}
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));

			}
		}
	});


}


function delAttachment(obj) {
	var uri = $(obj).parent().find("a").attr("href");
	if (uri == '') {
		$(obj).parent().parent().remove();
	} else {
		index = layer.confirm("您是否确认该操作？", {
			btn: ['确定','取消'] //按钮
		}, function(){
			var length = $(obj).parent().parent().parent().find("input[type='file']").length;
			if (length == 1) {
				$(obj).parent().find("i").hide();
				$(obj).parent().find("a").hide();
				$(obj).parent().find("span").hide();
			} else {
				$(obj).parent().parent().remove();
			}
			layer.close(index);
		}, function(){
		});
	}
}

$(function () {
	// 页面加载完成后立即调用changeSealType函数，确保公司名称与选择的公章类型一致
	changeSealType();

	var authType=$("input[name='authType']:checked").val();
	if(authType == undefined || authType==0){//默认是项目授权
		initDateForApply(90,true);
	}else{//经销授权
		initDateForApply(365,true);
	}
});

function initDateForApply(day,first){
	if(day>90){
		$("#pikadayNum").text("经销授权，时间不得超过365天");
	}else{
		$("#pikadayNum").text("项目授权，时间不得超过90天");
	}

	var strTimeToNum = function (str) {
		var num = new Date(str).valueOf();

		return num - 8 * 60 * 60 * 1000;
	};
	if(!first){//非初始进页面，则在切换类型时，需要将原值清空
		$('.J-date-range input').val('');
	}
	$('.J-date-range').each(function () {
		var $input1 = $(this).find('input').eq(0);
		var $input2 = $(this).find('input').eq(1);

		if ($input1.data('pikaday')) {
			$input1.data('pikaday').destroy();
		}
		if ($input2.data('pikaday')) {
			$input2.data('pikaday').destroy();
		}
	});
	//日期空间初始化
	$('.J-date-range').each(function () {
		var year = new Date().getFullYear();
		var $input1 = $(this).find('input').eq(0);
		var $input2 = $(this).find('input').eq(1);
		var pikadayStart = new Pikaday({
			format: 'yyyy-mm-dd',
			field: $(this).find('input').eq(0)[0],
			firstDay: 1,
			yearRange: [year, year + 10],
			onSelect: function (val) {
				console.log("start"+val1);
				pikadayEnd.setMinDate(val);
				pikadayEnd.setMaxDate(new Date(val).valueOf() + day * 1000 * 60 * 60 * 24);
			}
		});
		$input1.data('pikaday', pikadayStart);

		var pikadayEnd = new Pikaday({
			format: 'yyyy-mm-dd',
			field: $(this).find('input').eq(1)[0],
			firstDay: 1,
			yearRange: [year, year + 10],
			onSelect: function (val) {
				console.log("end"+val);
			}
		});
		$input2.data('pikaday', pikadayEnd);
		var val1 = $(this).find('input').eq(0).val();
		var val2 = $(this).find('input').eq(1).val();
		if (val1) {
			var times = strTimeToNum($.trim(val1))
			pikadayEnd.setMinDate(new Date(times));
			pikadayEnd.setMaxDate(new Date(times + day * 1000 * 60 * 60 * 24));
		}

		// 重置 Pikaday 的日期范围
		pikadayStart.setMinDate(null);
		pikadayStart.setMaxDate(null);
		pikadayEnd.setMinDate(null);
		pikadayEnd.setMaxDate(null);
	})
}


function findSku(quoteorderId){
	layer.open({
		type:2,
		shadeClose: false, //点击遮罩关闭
		area: ['800px', '500px'],
		title: '选择sku',
		btn: null,
		content: page_url + '/order/quote/selSkuByQuote.do?quoteorderId='+quoteorderId,
	});
}

function reLook(skuName,brandName,skuModel,skuId) {
	$('#skuName').val(skuName);
	$('#brandName').val(brandName);
	$('#model').val(skuModel);
	$('#skuId').val(skuId);
	layer.closeAll()
}

// 公章类型选择事件处理
function changeSealType() {
	var sealType = $("#sealType").val();
	// 获取隐藏域中的公司信息列表
	var companyInfoListStr = $("#companyInfoListWithSeq").val();
	var companyInfoList = JSON.parse(companyInfoListStr);
	var companyNameNow = "";

	// 根据sealType获取对应的公司名称
	if(companyInfoList && companyInfoList.length > 0) {
		for(var i = 0; i < companyInfoList.length; i++) {
			if(companyInfoList[i].frontEndSeq == sealType) {
				companyNameNow = companyInfoList[i].companyName;
				break;
			}
		}
	}
	updateCompanyName(companyNameNow);

	// 根据公章类型动态更新页面上的公司名称
	// if (sealType == "2") {
	// 	// 医购优选公章
	// 	updateCompanyName("南京医购优选供应链管理有限公司");
	// } else {
	// 	// 贝登公章（默认）
	// 	updateCompanyName("南京贝登医疗股份有限公司");
	// }

	// 如果已上传非标授权书附件，需要重新检查是否可以签章
	var nonStandardAuthorizationUrl = $("#uri_non").val();
	if (nonStandardAuthorizationUrl && nonStandardAuthorizationUrl.length > 0) {
		$.ajax({
			url: page_url + '/order/quote/whetherIncludeText.do?url=' + nonStandardAuthorizationUrl + '&sealType=' + sealType,
			type: "POST",
			dataType: "json",
			success: function (req) {
				if(req.code == 0){
					$("#whether_sign").val(1);
					$("#whether_sign_tip").hide();
				}
				if(req.code == -1){
					$("#whether_sign").val(0);
					$("#whether_sign_tip").show();
				}
			},
			error: function (req) {
				console.error("检查签章失败", req);
			}
		});
	}
}

// 更新页面上所有公司名称文本的辅助函数
function updateCompanyName(companyName) {
	var companyInfoListStr = $("#companyInfoListWithSeq").val();
	var companyInfoList = JSON.parse(companyInfoListStr);
	var companyInfoNameArray =[];

	// 根据sealType获取对应的公司名称
	if(companyInfoList && companyInfoList.length > 0) {
		for(var i = 0; i < companyInfoList.length; i++) {
			companyInfoNameArray.push(companyInfoList[i].companyName);
		}
	}
	var companyNameLastStr = companyInfoNameArray.join("|");


	// 更新授权书正文中的所有公司名称实例
	// 第一段：作为...的经销商
	var contentParagraphs = $(".content p");
	if (contentParagraphs.length > 0) {
		var firstParagraph = contentParagraphs.eq(1);
		var text = firstParagraph.html();
		// 使用正则表达式替换公司名称
		// text = text.replace(/(南京贝登医疗股份有限公司|南京医购优选供应链管理有限公司)/, companyName);
		text = text.replace(new RegExp(companyNameLastStr, "g"), companyName);
		firstParagraph.html(text);
	}

	// 更新授权书正文中的公司名称（未经...书面同意部分）
	if (contentParagraphs.length > 3) {
		var fourthParagraph = contentParagraphs.eq(3);
		var text = fourthParagraph.html();
		// 使用正则表达式替换公司名称
		// text = text.replace(/(南京贝登医疗股份有限公司|南京医购优选供应链管理有限公司)/, companyName);
		text = text.replace(new RegExp(companyNameLastStr, "g"), companyName);
		fourthParagraph.html(text);
	}

	// 更新授权书落款处的公司名称
	var rightParagraph = $(".content p.top.right");
	if (rightParagraph.length > 0) {
		rightParagraph.text(companyName);
	}

	// 更新页面顶部的logo
	if (!(companyName === "南京贝登医疗股份有限公司")) {
		// 医购优选公章类型不显示logo
		$("#company-logo").hide();
	} else {
		$("#company-logo").show();
		$("#company-logo").attr("src", page_url + "/static/images/logonew.jpg");
	}
}

function authorizationPre() {
	var purchaseOrBidding=$("#purchaseOrBidding").val();
	var productCompany=$("#productCompany").val();
	var natureOfOperation=$("#natureOfOperation").val();
	var brandName=$("#brandName").val();
	var skuName=$("#skuName").val();
	var distributionsType=$("#distributionsType").val();
	var purchaseProjectName=$("#purchaseProjectName").val();
	var purchaseProjectNum=$("#purchaseProjectNum").val();
	var fileType=$("#fileType").val();
	var authorizedCompany=$("#authorizedCompany").val();
	var aftersalesCompany=$("#aftersalesCompany").val();
	var beginTime=$("#beginTime").val();
	var endTime=$("#endTime").val();
	var applyYear=$("#applyYear").val();
	var applyMonth=$("#applyMonth").val();
	var applyDay=$("#applyDay").val();
	var sealType=$("#sealType").val();
	url = page_url + "/order/quote/authorizationPreview.do?purchaseOrBidding="+purchaseOrBidding+"&productCompany="+productCompany+"&natureOfOperation="+natureOfOperation
		+"&brandName="+brandName
		+"&skuName="+skuName
		+"&distributionsType="+distributionsType
		+"&authorizedCompany="+authorizedCompany
		+"&purchaseProjectName="+purchaseProjectName
		+"&purchaseProjectNum="+purchaseProjectNum
		+"&fileType="+fileType
		+"&sealType="+sealType
		+"&aftersalesCompany="+aftersalesCompany
		+"&beginTime="+beginTime
		+"&endTime="+endTime
		+"&applyYear="+applyYear
		+"&applyMonth="+applyMonth
		+"&applyDay="+applyDay
	;
	layer.open({
		title: '授权书预览',
		content: url,
		type: 2,
		area: ['95%', '95%']
		,btn: ['关闭预览']
		,btnAlign: 'c'
		,btn1: function(index, layero){
			layer.close(index);
		}
	})
}