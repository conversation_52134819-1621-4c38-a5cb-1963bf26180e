package com.vedeng.erp.trader.web.api;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.util.MapUtils;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.trader.domain.dto.CustomerBankAccountDownloadDto;
import com.vedeng.erp.trader.domain.dto.CustomerBankAccountDto;
import com.vedeng.erp.trader.dto.TraderDealerFrontDto;
import com.vedeng.erp.trader.service.CustomerBankAccountService;
import com.vedeng.erp.trader.service.TraderDealerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 经销商 相关数据
 * @date 2023/8/17 8:54
 **/
@ExceptionController
@RestController
@RequestMapping("/customerBankAccountApi")
@Slf4j
public class CustomerBankAccountApi {


    @Autowired
    private CustomerBankAccountService customerBankAccountService;
    
    @RequestMapping("/list")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<PageInfo<CustomerBankAccountDto>> list(@RequestBody PageParam<CustomerBankAccountDto> customerBankAccountDtoPageParam) {
        return R.success(customerBankAccountService.list(customerBankAccountDtoPageParam));
    }

    @RequestMapping("/delete")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<?> delete(@RequestBody CustomerBankAccountDto customerBankAccountDto) {
        customerBankAccountService.delete(customerBankAccountDto);
        return R.success();
    }

    @RequestMapping("/update")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<?> update(@RequestBody CustomerBankAccountDto customerBankAccountDto) {
        customerBankAccountService.update(customerBankAccountDto);
        return R.success();
    }

    @RequestMapping("/upload")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<?> upload(@RequestPart("file") MultipartFile file) {
        List<CustomerBankAccountDto> errorList = new ArrayList<>();
        try {
            errorList = customerBankAccountService.upload(file);
        } catch (Exception e) {
            log.error("客户账户批量上传失败",e);
            return R.error(e.getMessage());
        }
        return R.success(errorList);
    }

    @RequestMapping("/download")
    @NoNeedAccessAuthorization
    public void download(HttpServletResponse response, @RequestBody CustomerBankAccountDownloadDto dto) throws IOException {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode(dto.getFileName() + "导入失败明细", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            // 这里需要设置不关闭流
            EasyExcel.write(response.getOutputStream(), CustomerBankAccountDto.class).autoCloseStream(Boolean.FALSE).sheet("部分导入失败")
                    .doWrite(dto.getList());
        } catch (Exception e) {
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            Map<String, String> map = MapUtils.newHashMap();
            map.put("status", "failure");
            map.put("message", "下载文件失败" + e.getMessage());
            response.getWriter().println(JSON.toJSONString(map));
        }
    }
}
