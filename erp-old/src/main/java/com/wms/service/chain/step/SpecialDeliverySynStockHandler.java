package com.wms.service.chain.step;

import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.order.model.Saleorder;
import com.vedeng.stock.api.stock.dto.StockInfoDto;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.wms.constant.LogicalEnum;
import com.wms.dao.WmsLogicalOrdergoodsMapper;
import com.wms.dto.StockCalculateDto;
import com.wms.model.po.WmsLogicalOrdergoods;
import com.wms.service.chain.AbstractHandlerStep;
import com.wms.service.chain.HandlerStepContext;
import com.wms.service.stockcalculate.OutputOrderAuditPassCaculateImpl;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
public class SpecialDeliverySynStockHandler extends AbstractHandlerStep {

    private static Logger logger = LoggerFactory.getLogger(SpecialDeliverySynStockHandler.class);

    @Resource
    private WarehouseStockService warehouseStockService;

    @Autowired
    private OutputOrderAuditPassCaculateImpl outputOrderAuditPassCaculateImpl;

    @Override
    protected void doDealWith(HandlerStepContext handlerStepContext) throws Exception {

        Saleorder saleorder = handlerStepContext.get("saleorder");

        List<WmsLogicalOrdergoods> wmsLogicalOrdergoodsList = handlerStepContext.get("wmsLogicalOrdergoodsList");

        //同步库存服务
        sysnchStock(saleorder, wmsLogicalOrdergoodsList);
    }

    private void sysnchStock(Saleorder order, List<WmsLogicalOrdergoods> wmsLogicalOrdergoodsList) throws Exception {

        if(CollectionUtils.isEmpty(wmsLogicalOrdergoodsList)){
            return;
        }

        List<StockCalculateDto> stockCalculateList  = new ArrayList<>();
        wmsLogicalOrdergoodsList.stream().forEach( wmsLogicalOrdergoods -> {
            StockCalculateDto stockCalculateDto = new StockCalculateDto();
            if(wmsLogicalOrdergoods.getOccupyNum().equals(0)){
                return;
            }
            stockCalculateDto.setSku(wmsLogicalOrdergoods.getSku());
            stockCalculateDto.setOccupyNum(wmsLogicalOrdergoods.getOccupyNum());
            stockCalculateDto.setStockNum(0);
            stockCalculateDto.setLogicalWarehouseId(wmsLogicalOrdergoods.getLogicalWarehouseId());
            stockCalculateList.add(stockCalculateDto);
        });

        Integer actionId = order.getActionId();
        //库存策略计算
        StockInfoDto stockInfoDto  = new StockInfoDto();
        stockInfoDto.setRelatedNo(order.getSaleorderNo());
        stockInfoDto.setOccupyType(order.getOperateType());
        List<WarehouseDto> warehouseDtos = outputOrderAuditPassCaculateImpl.calculateStockInfo(stockCalculateList);
        for (WarehouseDto warehouseDto : warehouseDtos) {
            if(LogicalEnum.HDC.getLogicalWarehouseId().equals(warehouseDto.getLogicalWarehouseId())){
                warehouseDto.setActionId(actionId);
            }
        }
        stockInfoDto.setWarehouseStockList(warehouseDtos);

        //更新库存服务
        warehouseStockService.updateStockInfo(stockInfoDto);
    }
}
