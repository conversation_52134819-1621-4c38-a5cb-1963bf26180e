package com.vedeng.erp.business.service.impl;

import com.vedeng.erp.business.service.BusinessChanceService;
import com.vedeng.erp.business.service.BusinessLeadsService;
import com.vedeng.erp.business.service.InitializationService;
import com.vedeng.erp.system.common.annotation.CustomDataLogAnnotation;
import com.vedeng.erp.system.common.enums.CustomDataLogSaveTypeEnums;
import com.vedeng.erp.system.common.enums.CustomDataOperBizTypeEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @description 初始化服务接口实现类
 * @date 2022/7/28 9:02
 **/
@Service
@Slf4j
public class InitializationServiceImpl implements InitializationService {

    @Autowired
    private BusinessChanceService businessChanceService;

    @Autowired
    private BusinessLeadsService businessLeadsService;

    @Override
    @CustomDataLogAnnotation(successTpl = "成功导入了{}个销售商机",
            failTpl = "尝试导入销售商机，未能成功",
            operBizType = CustomDataOperBizTypeEnums.BUSINESS_CHANCE,
            dataLogType = CustomDataLogSaveTypeEnums.EXCEL_IMPORT)
    public List<Integer> importBusinessChance(MultipartFile file) throws IOException {
        return businessChanceService.importExcel(file);
    }

    @Override
    @CustomDataLogAnnotation(successTpl = "成功导入了{}个销售线索",
            failTpl = "尝试导入销售线索，未能成功",
            operBizType = CustomDataOperBizTypeEnums.BUSINESS_LEADS,
            dataLogType = CustomDataLogSaveTypeEnums.EXCEL_IMPORT)
    public List<Integer> importBusinessLeads(MultipartFile file) throws IOException {
        return businessLeadsService.importExcel(file);
    }
}
