Vue.component('ui-tab', {
    template: `<div class="vd-ui-tab">
        <div
            class="vd-ui-tab-list"
            :class="{ 'vd-ui-tab-list-more': type === 'with-more' }"
            ref="tabList"
        >
            <div
                class="vd-ui-tab-item"
                :class="[
                    { 'vd-ui-tab-item-active': item.id === current },
                    { 'vd-ui-tab-item-line': type === 'with-line' },
                ]"
                v-for="(item, index) in list"
                :key="index"
                @click="handlerChangeCurrent(item)"
                :ref="'tabItem' + item.id"
                v-show="!hiddenList['hide' + item.id]"
            >
                <div class="vd-ui-tab-item-inner">
                    <div class="vd-ui-tab-item-txt">
                        {{ item.label }}
                    </div>
                    <div v-if="item.isCustom" class="vd-ui-tab-custom-wrap"> 
                        <i class="vd-ui_icon icon-app-more" @click.stop="showCustomOption(index)"></i>
                        <div class="vd-ui-tab-custom-option" @click.stop v-show="index == customActiveIndex">
                            <div class="vd-ui-tab-custom-option-item" @click="customOption(item, 'rename')">重命名</div>
                            <div class="vd-ui-tab-custom-option-item red" @click="customOption(item, 'delete')">删除</div>
                        </div>
                    </div>
                </div>
            </div>
            <div
                class="vd-ui-tab-item vd-ui-tab-item-more"
                @mouseenter="isHideMore = false"
                @mouseleave="isHideMore = true"
                :class="[
                    {
                        'vd-ui-tab-item-more-active':
                            hiddenList['hide' + current],
                    },
                ]"
                v-show="Object.keys(hiddenList).length"
            >
                <span class="vd-ui-tab-item-txt">{{
                    hiddenList['hide' + current]
                        ? hiddenList['hide' + current].label
                        : "更多"
                }}</span>
                <i class="vd-ui_icon icon-down" :class="{hover: !isHideMore}"></i>
                <div
                    class="vd-ui-tab-more-list"
                    ref="moreList"
                    v-show="!isHideMore"
                >
                    <template v-for="(item, index) in hiddenList">
                        <div
                            class="vd-ui-tab-more-item"
                            :key="index"
                            @click="handlerChangeCurrent(item)"
                        >
                            {{ item.label }}
                        </div>
                    </template>
                </div>
            </div>
        </div>
        <div
            class="vd-ui-tab-bar"
            :style="'width:' + barWidth + 'px;left:' + barLeft + 'px;'"
            v-show="isShowTabBar && type !== 'with-line'"
        ></div>
    </div>`,
    data() {
        return {
            barWidth: null, //下滑线宽
            barLeft: null, //下滑线左定位
            current: null,
            list: [],
            hiddenList: {},
            isShowTabBar: false,
            isHideMore: true,
            customActiveIndex: -1
        };
    },
    props: {
        tabList: {
            type: Array,
            default: () => [],
        },
        active: {
            type: Number,
            default: 0,
        },
        type: {
            type: String,
            default: "normal", //normal,with-more,with-line
        },
    },
    watch: {
        current() {
            this.$nextTick(() => {
                this.checkShowTabBar();
            });
        },
    },
    mounted() {
        this.parseList();
        this.current = this.active;
        if (this.type === "with-more") {
            this.getItemWidth();

            window.addEventListener('resize', () => {
                this.calcTabShow();
                this.checkShowTabBar();
                this.calcBarStyle();
            })
        }
        this.calcBarStyle();
        document.onclick = () => {
            this.customActiveIndex = -1;
        }
    },
    methods: {
        checkShowTabBar() {
            if (this.hiddenList[`hide${this.current}`]) {
                this.isShowTabBar = false;
            } else {
                setTimeout(() => {
                    this.isShowTabBar = true;
                }, 50);
            }
        },
        getItemWidth() {
            this.$nextTick(() => {
                this.list.forEach((item) => {
                    item.width = this.$refs[`tabItem${item.id}`][0].offsetWidth;
                    item.left = this.$refs[`tabItem${item.id}`][0].offsetLeft;
                });

                this.calcTabShow();
                this.checkShowTabBar();
            });
        },
        parseList() {
            let list = this.tabList;

            list.forEach((item, index) => {
                item.id = item.id || index;
            });

            this.list = this.tabList;
        },
        calcTabShow() {
            let tabWidth = this.$refs.tabList.offsetWidth;
            let showWidth = 0;
            let hideObj = {};
            this.list.forEach((item) => {
                showWidth += item.width + 40;
                let moreWidth = 102;
                if (this.hiddenList[`hide${this.current}`]) {
                    moreWidth =
                        60 + this.hiddenList[`hide${this.current}`].width;
                }
                if (showWidth > tabWidth - moreWidth) {
                    hideObj[`hide${item.id}`] = item;
                }
            });

            this.hiddenList = hideObj;
        },
        calcBarStyle() {
            this.$nextTick(() => {
                if(this.$refs[`tabItem${this.current}`]){
                    let currentDom = this.$refs[`tabItem${this.current}`][0];
                    this.barLeft = currentDom.offsetLeft;
                    this.barWidth = currentDom.offsetWidth;
                }
            });
        },
        handlerChangeCurrent(item) {
            this.current = item.id;
            if (this.type === "with-more") {
                this.calcTabShow();
                this.isHideMore = true;
            }
            this.calcBarStyle();
            this.$emit("change", item.id, item);
        },
        showCustomOption(index) {
            this.customActiveIndex = index;
        },
        customOption(item, type) {
            this.customActiveIndex = -1;
            this.$emit('customOption', {
                item,
                type
            })
        }
    },
})