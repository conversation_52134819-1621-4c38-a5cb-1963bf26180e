package com.vedeng.activiti.taskassign;

import com.vedeng.activiti.service.ActionProcdefService;
import org.activiti.engine.task.Task;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import java.util.List;

public class AutoCompleteExecutionListener   implements ExecutionListener {

    private static final Logger log = LoggerFactory.getLogger(AutoCompleteExecutionListener.class);
    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
    private ActionProcdefService actionProcdefService = context.getBean(ActionProcdefService.class);
    private ProcessEngine getProcessEngine() {
        return ProcessEngines.getDefaultProcessEngine();// 流程引擎
    }

    public void notify(DelegateExecution execution) throws Exception {

        String processInstanceId = execution.getProcessInstanceId();
        TaskService taskService = this.getProcessEngine().getTaskService();
        List<Task> tasks = taskService.createTaskQuery()
                .processInstanceId(processInstanceId)
                .list();
        if (!tasks.isEmpty()) {
            String taskId = tasks.get(0).getId();  // 使用 taskId 进行后续操作
            actionProcdefService.completeTaskByAdmin(taskId,"抄送自动完成",execution.getVariables());

        }else{
            log.warn("未找到要审批的");
        }

    }
}
