package com.vedeng.doc.domain.entity;


import lombok.Data;

import java.io.Serializable;

/**
 * 资料库业务标签表
 *
 * <AUTHOR>
 * @TableName T_DOC_BUZ_TAG
 */
@Data
public class DocBuzTag implements Serializable {
    /**
     *
     */
    private Integer id;

    /**
     * 业务类型，1：商品资料，2：厂商资质
     */
    private Integer buzType;

    /**
     * 业务标签名称
     */
    private String buzTagName;

    /**
     * 是否必填项，0：否，1：是
     */
    private Integer required;

    /**
     * 支持上传文件个数
     */
    private Integer maxCount;

    /**
     * 是否需要维护有效期，0：否，1：是
     */
    private Integer maintainValidPeriod;

    /**
     * 是否需要保存变更历史记录，0：否，1：是
     */
    private Integer saveMaintainLog;

    /**
     * 是否需要提供含章文件，0：否，1：是，2：都需要
     */
    private Integer needHasStamp;

    /**
     * 文件来源，0：资料库系统内部，1：外部
     */
    private Integer source;

    /**
     * 外部跳转链接
     */
    private String externalUrl;

    /**
     * 外部资源功能标识
     */
    private String externalFunctionId;

    /**
     * 参数展示或隐藏，0：展示，1：隐藏
     */
    private Integer hidden;

    /**
     * 是否迁移oss，掌上小贝使用
     */
    private Integer transferOssMobile;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 创建者
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Long modTime;

    /**
     * 更新人
     */
    private Integer updater;

    private static final long serialVersionUID = 1L;
}