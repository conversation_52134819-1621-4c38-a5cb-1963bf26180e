package com.newtask.data.trader;

import com.newtask.data.AbstractDataSync;
import com.newtask.data.dao.TraderDateMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 查报价数量
 */
@Component
public class TraderQuoteNumSync extends AbstractDataSync {

    @Autowired
    private TraderDateMapper traderDateMapper;
    public void process(String startTime){
        super.process(startTime);
    }

    /**
     * 查询某一时间段需要更新的客户
     * @param startTime
     * @return
     */
    @Override
    public List<Integer> loadBizId(Long startTime,Long endTime) {
        List<Integer> list=   traderDateMapper.getQuteNumTraderId(startTime,endTime);
        return list;
    }

    @Override
    public List<Map<String, Object>> loadBizData(List<Integer> bizIds) {
        // 根据 traderId查 报价数量
        if(CollectionUtils.isNotEmpty(bizIds)){
            List<Map<String, Object>> list = traderDateMapper.findQuteCount(bizIds);
            return list;
        }
        return null;
    }

    @Override
    public void updateData(List<Map<String, Object>> dataList) {
        if(CollectionUtils.isNotEmpty(dataList)){
            dataList.forEach(item->{
                Long nowDate = new Date().getTime();
                Integer traderId= NumberUtils.toInt(item.get("traderId")+"");
                //报价数量
                Integer quoteNum=NumberUtils.toInt(item.get("quoteNum")+"");
                //判断宽表中是否存在这条记录
                makeExist(traderId,nowDate);
                traderDateMapper.updateQuteCount(traderId,quoteNum,nowDate);
            });
        }
    }

    @Override
    public void makeExist(Integer traderId,Long nowDate) {
        Long count = traderDateMapper.makeExist(traderId);
        if (count <= 0) {
            //往宽表里添加数据
            traderDateMapper.insertTraderId(traderId,nowDate);
        }
    }
}
