package com.vedeng.erp.aftersale.domain.entity;

import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.*;

import java.math.BigDecimal;

/**
 * T_EXPENSE_AFTER_SALES
 * <AUTHOR>
@EqualsAndHashCode(callSuper = true)
@Data
public class ExpenseAfterSalesEntity extends BaseEntity {
    /**
     * 主键
     */
    private Long expenseAfterSalesId;

    /**
     * 费用售后订单号
     */
    private String expenseAfterSalesNo;

    /**
     * 采购费用单主键ID
     */
    private Integer buyorderExpenseId;

    /**
     * 费用售后类型（字典库）退货退票、退票
     */
    private Integer expenseAfterSalesType;

    /**
     * 费用售后原因（字典库）退货、退款、重做、其他
     */
    private Integer expenseAfterSalesReason;

    /**
     * 详细说明
     */
    private String expenseAfterSalesComments;

    /**
     * 订单详细说明 转单等相关信息
     */
    private JSONArray orderDesc;

    /**
     * 是否自动转单创建 0 否 1是
     */
    private Integer isAuto;

    /**
     * 客户ID
     */
    private Integer traderId;

    /**
     * 联系人ID
     */
    private Integer traderContactId;

    /**
     * 款项退还 1退至公司账户 2退至供应商余额
     */
    private Integer refundMethod;

    /**
     * 是否删除 0否 1是
     */
    private Integer isDelete;

    /**
     * 更新备注
     */
    private String updateRemark;

    /**
     * 售后总额
     */
    private BigDecimal totalAmount;

}