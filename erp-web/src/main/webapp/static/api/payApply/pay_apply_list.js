function getPayApplyList(param) {
    return axios({
        url: '/finance/payapply/getPayApplyListPageForJson.do',
        method: 'post',
        params: param,
        headers: {
            'Content-Type': 'application/json'  //如果写成contentType会报错,如果不写这条也报错
        }
    })
}

function agreeBuyOrderBankAcceptance(param) {
    return axios({
        url: '/order/buyorder/complementTask.do',
        method: 'post',
        params: param,
        headers: {
            'Content-Type': 'application/json'  //如果写成contentType会报错,如果不写这条也报错
        }
    })
}

function agreeBankAcceptanceSubmit(param) {
    return axios({
        url: '/payApply/api/acceptancePay.do',
        method: 'post',
        data: param,
        headers: {
            'Content-Type': 'application/json'  //如果写成contentType会报错,如果不写这条也报错
        }
    })
}

function passBuyOrderBankAcceptance (param) {
    return axios({
        url: '/finance/buyorder/payApplyPass.do',
        method: 'post',
        params: param,
        headers: {
            'Content-Type': 'application/json'  //如果写成contentType会报错,如果不写这条也报错
        }
    })
}

function batchAgreeBuyOrderBankAcceptance(param) {
    return axios({
        url: '/order/buyorder/batchComplementTask.do',
        method: 'post',
        params: param,
        headers: {
            'Content-Type': 'application/json'  //如果写成contentType会报错,如果不写这条也报错
        }
    })
}

function getDiscountNumber(param) {
    return axios({
        url: '/payApply/api/getDiscountNumber.do',
        method: 'post',
        params: param
    })
}
