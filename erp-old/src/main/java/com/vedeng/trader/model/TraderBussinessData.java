package com.vedeng.trader.model;

import java.math.BigDecimal;

/**
 * <b>Description:</b><br> 交易者数据信息（交易）
 * <AUTHOR>
 * @Note
 * <b>ProjectName:</b> dbcenter
 * <br><b>PackageName:</b> com.vedeng.model.common
 * <br><b>ClassName:</b> TraderData
 * <br><b>Date:</b> 2017年9月5日 下午1:28:17
 */
public class TraderBussinessData {
    
    private Integer quoteTimes;//客户报价次数
    
    private Integer orderTimes;//客户交易次数
    
    private BigDecimal orderTotalAmount;//客户交易金额
    
    private Long lastOrderTime;//上次交易时间
    
	public Integer getQuoteTimes() {
		return quoteTimes;
	}

	public void setQuoteTimes(Integer quoteTimes) {
		this.quoteTimes = quoteTimes;
	}

	public Integer getOrderTimes() {
		return orderTimes;
	}

	public void setOrderTimes(Integer orderTimes) {
		this.orderTimes = orderTimes;
	}

	public BigDecimal getOrderTotalAmount() {
		return orderTotalAmount;
	}

	public void setOrderTotalAmount(BigDecimal orderTotalAmount) {
		this.orderTotalAmount = orderTotalAmount;
	}

	public Long getLastOrderTime() {
		return lastOrderTime;
	}

	public void setLastOrderTime(Long lastOrderTime) {
		this.lastOrderTime = lastOrderTime;
	}

	
}
