package com.newtask.celery.service;

import com.newtask.celery.model.TaskJob;

import java.util.List;
import java.util.Set;

/**
 * @Author: Cherny.chen
 * @Create: 2021/7/12 12:59
 */
public interface TaskJobService {

    /**
     * 保存
     *
     * @param taskJob 任务
     */
    void addTaskJob(TaskJob taskJob);

    /**
     * 更新
     *
     * @param taskJob 任务
     */
    void updateTaskJob(TaskJob taskJob);

    /**
     * 获取待执行队列
     *
     * @param queueKey
     * @param lastId
     * @return
     */
    List<TaskJob> listTaskJobByQueue(Integer queueKey, Long lastId);


    /**
     * 获取待执行队列
     *
     * @return
     */
    Set<Integer> getActiveQueueSet();

    /**
     * 幂等校验
     * @param uuid
     * @return
     */
    boolean isRunSuccess(String uuid);

    /**
     *  移除redis中已经完成k
     * @param queueKey k
     */
    void removeFinishQueue(Integer queueKey);

}
