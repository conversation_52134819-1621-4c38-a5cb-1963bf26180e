package com.vedeng.erp.trader.service;

import com.vedeng.erp.trader.domain.entity.CommunicateVoiceTaskEntity;

public interface CommunicateVoiceTaskService {

    int insertSelective(CommunicateVoiceTaskEntity record);

    int deleteByPrimaryKey(Long id);

    int insert(CommunicateVoiceTaskEntity record);

    CommunicateVoiceTaskEntity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CommunicateVoiceTaskEntity record);

    int updateByPrimaryKey(CommunicateVoiceTaskEntity record);

    CommunicateVoiceTaskEntity selectByCommunicateRecordId(Integer communicateRecordId);

    CommunicateVoiceTaskEntity selectByCommunicateRecordIdAndSence(Integer communicateRecordId,String sence);
}

