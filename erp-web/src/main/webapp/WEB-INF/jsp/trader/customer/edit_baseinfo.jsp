<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="编辑基本信息" scope="application" />
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
%>
<c:set var="path" value="<%=basePath%>" scope="application" />
<script type="text/javascript" src="${pageContext.request.contextPath}/webjars/ezadmin/plugins/layui/layui.js"></script>
<link rel="stylesheet" href="${pageContext.request.contextPath}/webjars/ezadmin/plugins/layui/css/layui.css"/>
<script type="text/javascript"
		src="${pageContext.request.contextPath}/webjars/ezadmin/plugins/cascader/cascader.js?rnd=${resourceVersionKey}"></script>
<link rel="stylesheet" href="<%=basePath%>static/css/general.css?rnd=${resourceVersionKey}" />
<link rel="stylesheet" href="<%=basePath%>static/css/manage.css" />
<!-- 模糊搜索下拉框css引入 -->
<link rel="stylesheet" href="<%=basePath%>static/libs/searchableSelect/jquery.searchableSelect.css" />
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/labelMark.css">
<link rel="stylesheet" href="<%=basePath%>static/css/manage.css" />
<%@ include file="../../../vue/view/common/common.jsp" %>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/select2.css" />
<style>
	i {
		background: none !important;;
	}
	.el-cascader__tags{
		/* width:50px;*/
		/*padding-top: 5px;*/
	}
	.list-pages-search .el-cascader {
		line-height: 26px;
		height: 26px;
		width: 70%
	}
	.el-cascader__search-input {
		margin-top: 2px ;
		font-size: 12px;
	}
	.el-cascader .el-input .el-input__inner {
		height: 28px;
		line-height: 22px;
	}

	.el-cascader i, .el-cascader-panel i {
		height: auto;
		background: none;
	}

	.el-cascader__tags .el-tag .el-icon-close {
		-webkit-box-flex: 0;
		-ms-flex: none;
		flex: none;
		background-color: #C0C4CC !important;
	}

	.el-cascader .el-cascader__tags {
		flex-wrap: inherit;
	}
	.el-cascader__search-input{
		min-width: 10px;
	}
	.el-cascader-menu{
		width : 350px !important;
		min-width: 300px;
		height: 150px;
	}
	/*修改菜单高度*/
	.el-cascader-menu__wrap {
		height: 200px;
	}
	.el-input__suffix-inner{
		margin-top: 10px;
	}
</style>
<%@ include file="customer_tag.jsp"%>
<style type="text/css">
    .submit{
		margin-right: 6px;
		background: rgb(92, 184, 92);
		border-width: 1px;
		border-style: solid;
		border-color: rgb(76, 174, 76);
		border-image: initial;
	}
</style>
<div style="text-align: right;padding-right: 10px;padding-bottom: 12px;">
	<el-button type="primary" style="color: #00a0e9" onclick="queryChange()">查看更新记录</el-button>
	<el-button type="primary" style="color: #00a0e9" onclick="doHelp()">帮助</el-button>
</div>

<form method="post"
	  action="${pageContext.request.contextPath}/trader/customer/saveeditbaseinfo.do"
	  id="customerForm">
<div class="baseinforcontainer" style="padding-bottom: 15px;">
	<div class="border">
		<div class="baseinfor f_left">基本信息</div>
		<div class="clear"></div>
	</div>
	<div class="baseinforeditform">

			<ul>
				<li>
					<div class="infor_name">
						<span>*</span>
						<lable>客户名称</lable>
					</div>
					<div class="f_left">
						<input  type="text" class="input-largest errobor" name="traderName"
							id="traderName" value="${traderCustomer.trader.traderName }" />
					</div>
				</li>
				<li>
					<div class="infor_name">
						<span>*</span>
						<lable>注册地区</lable>
					</div>
					<div class="f_left">
						<select class="input-middle mr6" name="province"   >
							<option value="0">请选择</option>
							<c:if test="${not empty provinceList }">
								<c:forEach items="${provinceList }" var="province">
									<option value="${province.regionId }"
										<c:if test="${ not empty provinceRegion &&  province.regionId == provinceRegion.regionId }">selected="selected"</c:if>>${province.regionName }</option>
								</c:forEach>
							</c:if>
						</select> <select class="input-middle mr6" name="city"  >
							<option value="0">请选择</option>
							<c:if test="${not empty cityList }">
								<c:forEach items="${cityList }" var="city">
									<option value="${city.regionId }"
										<c:if test="${ not empty cityRegion &&  city.regionId == cityRegion.regionId }">selected="selected"</c:if>>${city.regionName }</option>
								</c:forEach>
							</c:if>
						</select> <select class="input-middle mr6" name="zone" id="zone"  >
							<option value="0">请选择</option>
							<c:if test="${not empty zoneList }">
								<c:forEach items="${zoneList }" var="zone">
									<option value="${zone.regionId }"
										<c:if test="${ not empty zoneRegion &&  zone.regionId == zoneRegion.regionId }">selected="selected"</c:if>>${zone.regionName }</option>
								</c:forEach>
							</c:if>
						</select>
					</div>
				</li>
				<li id="attr_brfore_lix">
					<div class="infor_name">
						<img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="18px" height="18px">
						<lable>仓库地址</lable>
					</div>
					<div class="f_left">

						<select class="input-middle mr6" name="warehouseProvince" id="warehouseProvince">
							<option value="0">请选择</option>
							<c:if test="${not empty provinceList }">
								<c:forEach items="${provinceList }" var="province">
									<option value="${province.regionId }"
											<c:if test="${ not empty provinceRegion &&  province.regionId == warehouseProvinceRegion.regionId }">selected="selected"</c:if>>${province.regionName }</option>
								</c:forEach>
							</c:if>
						</select>

						<select class="input-middle mr6" name="warehouseCity"  id="warehouseCity">
							<option value="0">请选择</option>
							<c:if test="${not empty warehouseCityList }">
								<c:forEach items="${warehouseCityList }" var="city">
									<option value="${city.regionId }"
											<c:if test="${ not empty cityRegion &&  city.regionId == warehouseCityRegion.regionId }">selected="selected"</c:if>>${city.regionName }</option>
								</c:forEach>
							</c:if>
						</select>

						<select class="input-middle mr6" name="warehouseZone" id="warehouseZone">
							<option value="0">请选择</option>
							<c:if test="${not empty warehouseZoneList }">
								<c:forEach items="${warehouseZoneList }" var="zone">
									<option value="${zone.regionId }"
											<c:if test="${ not empty zoneRegion &&  zone.regionId == warehouseZoneRegion.regionId }">selected="selected"</c:if>>${zone.regionName }</option>
								</c:forEach>
							</c:if>
						</select>

						<input type="text" class="input-small mt0 heit18" style="width:300px;height: 26px" placeholder="请填写详细地址信息" id="warehouseDetailAddress" name="warehouseDetailAddress" value="${traderCustomer.trader.warehouseDetailAddress}"/>
						<span style="color: red">
							若客户暂无仓库，可填写客户注册地址或经营地址。
						</span>
					</div>
				</li>
				<li id="attr_brfore_lis">
					<div class="infor_name">
						<span>*</span>
						<lable>归属平台</lable>
					</div>
					<div class="f_left">
						<div id="belongPlatform_div">
							<select class="input-middle mr6" name="belongPlatform" <c:if test="${traderCustomer.belongPlatform==6}" > disabled </c:if>
									id="belong_Platform">
								<option <c:if test="${traderCustomer.belongPlatform==null}">selected="selected"</c:if> value="0">请选择</option>
								<option  <c:if test="${traderCustomer.belongPlatform==1}">selected="selected"</c:if> value="1">贝登医疗</option>
								<option  <c:if test="${traderCustomer.belongPlatform==2}">selected="selected"</c:if> value="2">医械购</option>
								<option <c:if test="${traderCustomer.belongPlatform==3}">selected="selected"</c:if>  value="3">科研购</option>
<%--								<option <c:if test="${traderCustomer.belongPlatform==5}">selected="selected"</c:if>  value="5">其他</option>--%>
								<c:if test="${traderCustomer.belongPlatform==6}">
									<option selected="selected" value="6">集采</option>
								</c:if>

							</select>
							<span style="color: red">
								默认选择"贝登医疗"，科研选择"科研购"
							</span>
						</div>

					</div>
				</li>
				<li id="attr_brfore_li">
					<div class="infor_name">
						<span>*</span>
						<lable>客户类型</lable>
					</div>
					<div class="f_left">
						<div id="customer_category_div">
						<c:forEach items="${traderCustomer.customerCategoriesMap }"
							var="category">
							<select class="input-middle mr6"
								name="customer_category${category.key }"
								onchange="changeCate(this);" id="customer_category">
								<option selected="selected" value="0">请选择</option>
								<c:forEach items="${category.value }" var="cates">
									<option
										<c:if test="${cates.traderCustomerCategoryId==category.key}"> selected="selected" </c:if>
										value="${cates.traderCustomerCategoryId}">${cates.customerCategoryName }</option>
								</c:forEach>
							</select>
						</c:forEach>
							<c:if test="${isTerminal}">
								<select class="input-middle mr6 showTerminal"
										name="customer_category_three"
										onchange="changeNew(this);" id="customer_category">
									<option selected="selected" value="">请选择</option>
									<c:forEach items="${threeCustomerType }" var="cates">
										<option
												<c:if test="${cates.label==traderCustomerMarketing.traderCustomerMarketingType}"> selected="selected" </c:if>
												value="${cates.label}">${cates.value }</option>
									</c:forEach>
								</select>
								<button type="button" class="sucsess-ok querybtn showTerminal" onclick="queryTerminal()">终端库查询</button>
							</c:if>
						</div>
					</div>
				</li>
				<c:if test="${isDealer}">
					<li class="dealer5" style="display: none">
						<div class="infor_name mt0">
							<lable>客户有效性</lable>
						</div>
						<div class="f_left inputradio">
							<ul>
								<li><input type="radio" name="effectiveness" <c:if test="${traderCustomer.trader.effectiveness==1}">checked</c:if> value="1"/> <label>有效</label></li>
								<li><input type="radio" name="effectiveness" <c:if test="${traderCustomer.trader.effectiveness==0}">checked</c:if> value="0"/> <label>无效</label></li>
								<li class="queryInvalidReasonList" <c:if test="${traderCustomer.trader.effectiveness==1}">hidden </c:if>>
									<div class="input-middle f_left" style="width: 500px;">
										<input type="hidden" id="invalidReason" name="invalidReason" value="${traderCustomer.invalidReason}"  >
										<input id="queryInvalidReasonListSelect" name="queryInvalidReasonListSelect" style="width: 100%;">
									</div>
								</li>
								<li class="reasonInput" <c:if test="${traderCustomer.trader.effectiveness==1 || traderCustomer.otherReason == ''}">hidden </c:if>>
									<input type="text" 	id="otherReason" name="otherReason" placeholder="请填写具体原因（不超过50字）" value="${traderCustomer.otherReason}" maxlength="50" style="width:620px;margin-left: 10px">
								</li>
							</ul>
						</div>
					</li>
				</c:if>
				<c:if test="${isTerminal}">
				<li class="customerAttribute">
					<div class="infor_name mt0">
						<lable>机构性质</lable>
					</div>
					<div class="f_left">
						<select class="input-middle"
								name="traderCustomer.traderCustomerMarketingDto.institutionNature">
							<option value="">请选择</option>
							<option value="0" <c:if test="${'0'==traderCustomerMarketing.institutionNature}"> selected="selected" </c:if>>公立</option>
							<option value="1" <c:if test="${'1'==traderCustomerMarketing.institutionNature}"> selected="selected" </c:if>>非公</option>


						</select>
					</div>
				</li>
					<c:if test="${institutionLevel != null and not empty institutionLevel}">
						<li class="customerAttribute ">
							<div class="infor_name mt0">
								<lable>机构评级</lable>
							</div>
							<div class="f_left"><select class="input-middle"
														name="traderCustomer.traderCustomerMarketingDto.institutionLevel">
								<option value="">请选择</option>
								<c:forEach items="${institutionLevel }" var="cates">
									<option
											<c:if test="${cates.label==traderCustomerMarketing.institutionLevel}"> selected="selected" </c:if>
											value="${cates.label}">${cates.value }</option>
								</c:forEach>
							</select></div>

						</li>
					</c:if>
					<c:if test="${institutionType != null and not empty institutionType}">
						<li class="customerAttribute ">
							<div class="infor_name mt0">
								<lable>机构类型</lable>
							</div>
							<div class="f_left"><select class="input-middle"
														name="traderCustomer.traderCustomerMarketingDto.institutionType"
														onchange="changeTypeNew(this)">
								<option value="">请选择</option>
								<c:forEach items="${institutionType }" var="cates">
									<option
											<c:if test="${cates.label==traderCustomerMarketing.institutionType}"> selected="selected" </c:if>
											value="${cates.label}">${cates.value }</option>
								</c:forEach>
							</select>
								<c:if test="${institutionTypeChild != null && not empty institutionTypeChild}">
									<select class="input-middle mr6 institutionTypeChilds"
											name="traderCustomer.traderCustomerMarketingDto.institutionTypeChild">
										<option value="">请选择</option>
										<c:forEach items="${institutionTypeChild }" var="cates">
											<option
													<c:if test="${cates.label==traderCustomerMarketing.institutionTypeChild}"> selected="selected" </c:if>
													value="${cates.label}">${cates.value }</option>
										</c:forEach>
									</select>
								</c:if>

							</div>


						</li>

					</c:if>

				</c:if>

				<!-- 非公集团的值 64 -->
				<input type="hidden" class="J-prev-id" value="${traderCustomer.trader.parentId}">

				<!-- 归属平台  -->
				<input type="hidden" class="J-prev-belong_platform" name="belongPlatform" value="${traderCustomer.trader.belongPlatform}">


				<c:if test="${traderCustomer.trader.belongPlatform ==6 }">
					<li id="client_property_li_1">
						<div class="infor_name">
							<lable>客户属性</lable>
						</div>

						<div class="f_left">

							<c:choose>
								<c:when test="${traderCustomer.trader.parentId > 0}">
									<input  type="text"  name="" disabled
											value="分公司" />
								</c:when>

								<c:otherwise>
									<input  type="text"  name="" disabled
											value="总公司" />
								</c:otherwise>
							</c:choose>
						</div>
					</li>
				</c:if>



				<c:if test="${parentTraderName !=null && parentTraderName!='' }">
					<li  id="pName">
						<div class="infor_name ">
							<label>归属总公司</label>
						</div>
						<div>
							<input  type="text"  name="traderName" disabled
									 value="${parentTraderName}" />
						</div>
					</li>
				</c:if>



				<c:if test="${not empty attributes}">
					<c:forEach items="${attributes }" var="listData">
						<li
							class="customerAttribute <c:if test="${listData.isSelected==1 }">bt</c:if>"
							<c:if test="${listData.parentId ==5 }">alt="${listData.parentId }"</c:if>>
							<div class="infor_name mt0">
								<c:if test="${listData.isSelected==1 }">
									<span>*</span>
								</c:if>
								<lable>${listData.categoryName }</lable>
							</div> <c:if test="${listData.inputType == 0 }">
								<div class="f_left inputfloat inputradio">
									<ul>
							</c:if> <c:if test="${listData.inputType == 1 }">
								<div class="f_left inputradio">
									<ul>
							</c:if> <c:forEach items="${listData.sysOptionDefinitions }"
								var="option">
								<c:if test="${listData.inputType == 0 }">
									<li><input type="checkbox" onclick="attrCheck(this);"
										name="attributeId"
										value="${listData.attributeCategoryId}_${option.sysOptionDefinitionId}"
										<c:if test="${listData.attributeCategoryId == 5 && option.sysOptionDefinitionId == 93 }">onclick="getChildAttr(this,5)"</c:if>
										<c:if test="${not empty traderCustomer.attributeMap}">
											<c:forEach items="${traderCustomer.attributeMap}" var="attibute">
												<c:forEach items="${attibute.value }" var="attri">
													<c:if test="${attri.attributeId == option.sysOptionDefinitionId}">
													checked="checked"
													</c:if>
							</c:forEach>
					</c:forEach>
				</c:if>


				/>
				<label>${option.title }</label>
				</li>
				</c:if>
				<c:if test="${listData.inputType == 1 }">
					<li><input type="checkbox" name="attributeId"
						value="${listData.attributeCategoryId}_${option.sysOptionDefinitionId}"
						<c:if test="${listData.attributeCategoryId == 5 && option.sysOptionDefinitionId == 93 }">onclick="getChildAttr(this,5)"</c:if>
						<c:if test="${not empty traderCustomer.attributeMap}">
											<c:forEach items="${traderCustomer.attributeMap}" var="attibute">
												<c:forEach items="${attibute.value }" var="attri">
													<c:if test="${attri.attributeId == option.sysOptionDefinitionId}">
													checked="checked"
													</c:if>
					</c:forEach> </c:forEach>
				</c:if>


				/>
				<label>${option.title }</label>
				<c:if
					test="${(listData.scope == 1027 && option.sysOptionDefinitionId==109)
										 			|| (listData.scope == 1028 && option.sysOptionDefinitionId==115)
										 			|| (listData.scope == 1032 && option.sysOptionDefinitionId==302)}">
					<input type="text" class="input-small mt0 heit18"
						name="attributedesc_${listData.attributeCategoryId}_${option.sysOptionDefinitionId}"
						<c:if test="${not empty traderCustomer.attributeMap}">
											<c:forEach items="${traderCustomer.attributeMap}" var="attibute">
												<c:forEach items="${attibute.value }" var="attri">
													<c:if test="${attri.attributeId == option.sysOptionDefinitionId}">
												 	value="${attri.attributeOther }"
												 	</c:if>
					</c:forEach>
					</c:forEach>
				</c:if>
				/>
				</c:if>
				</li>
				</c:if>
				</c:forEach>
				<c:if test="${listData.inputType == 1 }">
			</ul>
	</div>
	</c:if>
	<c:if test="${listData.inputType == 0 }">
		</ul>
</div>
</c:if>
</li>
</c:forEach>
</c:if>

<li class="specialli dealer5" alt="fenxiao">
	<div class="infor_name">
		<label>经营区域</label>
	</div>
	<div class="readyadd f_left">
		<div class="career-one">
			<select class="input-middle f_left mr8" name="bussiness_province">
				<option value="0">请选择</option>
				<c:if test="${not empty provinceList }">
					<c:forEach items="${provinceList }" var="province">
						<option value="${province.regionId }">${province.regionName }</option>
					</c:forEach>
				</c:if>
			</select> <select class="input-middle f_left mr8" name="bussiness_city">
				<option value="0">请选择</option>
			</select> <select class="input-middle f_left mr8" name="bussiness_zone">
				<option value="0">请选择</option>
			</select>
			<div
				class="f_left bt-bg-style bg-light-blue bt-smaller mr8 addaddress"
				onclick="addBussinessArea();" id="addBussinessArea">添加</div>
		</div>
		<div
			class="addtags mt8 addaddresstags <c:if test="${empty bussinessMap }">none</c:if>">
			<ul id="bussinessArea_show">
				<c:if test="${not empty bussinessMap }">
					<c:forEach items="${bussinessMap }" var="bussinessArea">
						<li class="bluetag">${bussinessArea.value.get("areaStr") }<input
							type="hidden" name="bussinessAreaId" value="${bussinessArea.key}"><input
							type="hidden" name="bussinessAreaIds"
							value="${bussinessArea.value.get('areaIds') }"><i
							class="el-icon-close" onclick="delTag(this);"></i>
						</li>
					</c:forEach>
				</c:if>
			</ul>
		</div>
	</div>
</li>
				<li class="specialli" alt="workAreaLi" >
					<div class="infor_name">
						<span>*</span>
						<label>办公区域</label>
					</div>
					<div class="readyadd f_left">
						<div class="career-one">
							<select class="input-middle f_left mr8"
									name="work_province" id="work_province">
								<option value="0">请选择</option>
								<c:forEach items="${workProvinceList }" var="province">
									<option value="${province.regionId }"
											<c:if test="${   province.regionId == workProvince }">selected="selected"</c:if>>${province.regionName }</option>
								</c:forEach>
							</select> <select class="input-middle f_left mr8" name="work_city" id="work_city">
							<option value="0">请选择</option>
							<c:forEach items="${workCityList }" var="city">
								<option value="${city.regionId }"
										<c:if test="${   city.regionId == workCity }">selected="selected"</c:if>>${city.regionName }</option>
							</c:forEach>
						</select> <select class="input-middle f_left mr8" name="work_zone" id="work_zone">
							<option value="0">请选择</option>
							<c:forEach items="${workZoneList }" var="zone">
								<option value="${zone.regionId }"
										<c:if test="${   zone.regionId == workZone }">selected="selected"</c:if>>${zone.regionName }</option>
							</c:forEach>
						</select>

						</div>
						<div class="addtags mt8 addaddresstags " id="workArea_show">

						</div>
					</div>
				</li>
<li alt="fenxiao">
	<div class="infor_name ">
		<label>经营品牌</label>
	</div>
	<div class="f_left inputradio">
		<div class="career-one">
			<input class="input-middle f_left mr8 mb8 mt0" placeholder="请输入关键词查询"
				name="bussinessBrandKey" />
			<div
				class="f_left bt-bg-style bg-light-blue bt-small mr8 searchbrand"
				onclick="searchBussinessBrand()">搜索</div>
			<select class="input-middle f_left mr8 mb8 mt0"
				name="bussinessBrands">
			</select>
			<div class="f_left bt-bg-style bg-light-blue bt-small mr8 addbrand"
				onclick="addBussinessBrand()" id="addBussinessBrand">添加</div>
		</div>
		<div
			class="addtags addbrandtags <c:if test="${empty traderCustomer.traderCustomerBussinessBrands }">none</c:if>">
			<ul id="bussinessBrand_show">
				<c:if
					test="${not empty traderCustomer.traderCustomerBussinessBrands }">
					<c:forEach items="${traderCustomer.traderCustomerBussinessBrands }"
						var="bussinessBrand">
						<li class="bluetag">${bussinessBrand.brand.brandName }<input
							type="hidden" name="bussinessBrandId"
							value="${bussinessBrand.brandId }"><i class="iconbluecha"
							onclick="delTag(this);"></i></li>
</li>
</c:forEach>
</c:if>
</ul>
</div>
</div>
</li>
<li alt="fenxiao">
	<div class="infor_name ">
		<label>代理品牌</label>
	</div>
	<div class="f_left inputradio">
		<div class="career-one">
			<input class="input-middle f_left mr8 mt0" placeholder="请输入关键词查询"
				name="agentBrandKey" />
			<div
				class="f_left bt-bg-style bg-light-blue bt-small mr8 searchbrand"
				onclick="searchAgentBrand()">搜索</div>
			<select class="input-middle f_left mr8" name="agentBrands">
			</select>
			<div class="f_left bt-bg-style bg-light-blue bt-small mr8 addbrand"
				onclick="addAgentBrand()" id="addAgentBrand">添加</div>
		</div>
		<div
			class="addtags addbrandtags mt8 <c:if test="${empty traderCustomer.traderCustomerAgentBrands }">none</c:if>">
			<ul id="agentBrand_show">
				<c:if test="${not empty traderCustomer.traderCustomerAgentBrands }">
					<c:forEach items="${traderCustomer.traderCustomerAgentBrands }"
						var="agentBrand">
						<li class="bluetag">${agentBrand.brand.brandName }<input
							type="hidden" name="agentBrandId" value="${agentBrand.brandId }"><i
							class="iconbluecha" onclick="delTag(this);"></i></li>
</li>
</c:forEach>
</c:if>
</ul>
</div>
</div>
</li>
<li alt="fenxiao">
	<div class="infor_name mt3">
		<label>业务模式</label>
	</div>
	<div class="f_left">
		<span class="mr4">直销比例 </span> <input type="text"
			class="input-smallest mr8 " name="traderCustomer.directSelling"
			<c:if test="${traderCustomer.directSelling > 0 }"> value="${traderCustomer.directSelling }"</c:if> /><span
			class="mr8">%</span> <span class="mr4">批发比例</span> <input type="text"
			class="input-smallest mr8" name="traderCustomer.wholesale"
			 <c:if test="${traderCustomer.wholesale > 0 }"> value="${traderCustomer.wholesale }"</c:if>/><span
			id="wholesale">%</span>
	</div>
</li>
<li alt="fenxiao">
	<div class="infor_name mt0">
		<label>销售模式</label>
	</div>
	<div class="f_left inputradio" id="salesModel_div">
		<c:forEach items="${salesModel }" var="model">
			<input type='radio' name="traderCustomer.salesModel"
				value="${model.sysOptionDefinitionId }"
				<c:if test="${model.sysOptionDefinitionId == traderCustomer.salesModel}">checked</c:if>>
			<label class='mr8'>${model.title }</label>
		</c:forEach>
	</div>
</li>
<li class="dealerNoShow">
	<div class="infor_name">
		<label>注册年份</label>
	</div>
	<div class="f_left inputradio">
		<input type="text" name="traderCustomer.registeredDateStr"
			class="input-middle Wdate"
			onFocus="WdatePicker({dateFmt:'yyyy-MM-dd'})" autocomplete="off"
			value="<fmt:formatDate value="${traderCustomer.registeredDate }" pattern="yyyy-MM-dd" />" />
	</div>
</li>
<li class="dealerNoShow">
	<div class="infor_name ">
		<label>注册资金</label>
	</div>
	<div class="f_left inputradio">
		<input  type="text" name="traderCustomer.registeredCapital"
			id="registeredCapital" class="input-middle"
			value="${traderCustomer.registeredCapital }" /><span
			class="mt6" id="registeredCapitalSpan"></span>
	</div>
</li>
<li class="dealerNoShow">
	<div class="infor_name mt0">
		<label>员工人数</label>
	</div>
	<div class="f_left inputradio">
		<select class="input-middle f_left mr8"
			name="traderCustomer.employees">
			<option selected="selected" value="0">请选择</option>
			<c:forEach items="${employees}" var="employee">
				<option value="${employee.sysOptionDefinitionId }"
					<c:if test="${employee.sysOptionDefinitionId == traderCustomer.employees}">selected="selected"</c:if>>${employee.title }</option>
			</c:forEach>
		</select>
	</div>
</li>
<li class="dealerNoShow">
	<div class="infor_name mt0">
		<label>年销售额</label>
	</div>
	<div class="f_left inputradio">
		<select class="input-middle f_left mr8"
			name="traderCustomer.annualSales">
			<option value="0">请选择</option>
			<c:forEach items="${annualSales}" var="annualSale">
				<option value="${annualSale.sysOptionDefinitionId }"
					<c:if test="${annualSale.sysOptionDefinitionId == traderCustomer.annualSales}">selected="selected"</c:if>>${annualSale.title }</option>
			</c:forEach>
		</select>
	</div>
</li>

				<c:if test="${traderCustomer.trader.belongPlatform ==2}">
					<li id="client_property_li_1">
						<div class="infor_name">
							<lable>客户属性</lable>
						</div>

						<div class="f_left">

							<select class="input-middle mr61" id="client_property" name="traderType" onchange="changeClientProperty(this)" <c:if test="${traderCustomer.trader.belongPlatform == 6||traderCustomer.trader.traderType==0||traderCustomer.trader.traderType>0}">disabled </c:if> >
								<option value="">请选择</option>
								<option value="0" <c:if test="${traderCustomer.trader.traderType == 0 and traderCustomer.trader.parentId> 0}">selected </c:if> >总公司</option>
								<option value="1" <c:if test="${traderCustomer.trader.traderType == 1 and traderCustomer.trader.parentId> 0}">selected </c:if> >分公司</option>
							</select>
						</div>
					</li>
				</c:if>
				<li id="ascription_headquarters_li" <c:if test="${traderCustomer.trader.parentId != 1}">style="display: none" </c:if> >
					<div class="infor_name ">
						<span>*</span>
						<label>归属总公司</label>
					</div>
					<select id="parentId" name="parentId">
						<option value="">请选择</option>
						<c:forEach var="list" items="${noPublicGroupList}">
						<option <c:if test="${traderCustomer.trader.parentId==list.traderId}">selected</c:if> value="${list.traderId}">${list.traderName}</option>
						</c:forEach>
					</select>
				</li>
</ul>


</div>
</div>
		<div class="baseinforcontainer mt10 pb20" id="oneDataManagement"  >
			<div class="border overflow-hidden">
				<div class="baseinfor f_left">终端同步信息</div>
			</div>
			<div class="baseinforeditform ">
				<ul>
					<li>
						<div class="infor_name mt0">
							<lable>经营状态</lable>
						</div>
						<div class="f_left " >
							<input type="text" class="input-largest"  id="managementForms" name="traderCustomer.traderCustomerMarketingDto.managementForms" value="${traderCustomerMarketing.managementForms}"/>
						</div>
					</li>
					<li>
						<div class="infor_name mt0">
							<lable>法人代表</lable>
						</div>
						<div class="f_left" >
							<input type="text" class="input-largest" id="legalRepresentative" name="traderCustomer.traderCustomerMarketingDto.legalRepresentative" value="${traderCustomerMarketing.legalRepresentative}"/>
						</div>
					</li>
					<li>
						<div class="infor_name mt0">
							<lable>床位数</lable>
						</div>
						<div class="f_left">
							<input type="text" class="input-largest" id="bedNumber" name="traderCustomer.traderCustomerMarketingDto.bedNumber" value="${traderCustomerMarketing.bedNumber}"/>
						</div>
					</li>
					<li>
						<div class="infor_name mt0">
							<lable>科室</lable>
						</div>
						<div class="f_left">
							<input type="text" class="input-largest" id="hospitalDepartment" name="traderCustomer.traderCustomerMarketingDto.hospitalDepartment" value="${traderCustomerMarketing.hospitalDepartment}"/>
						</div>
					</li>
				</ul>
			</div>
		</div>
	<input type="hidden" id="theDataOfThePage" name="theDataOfThePage" value="">
	<div style="display: none" id="dealerData">
		<jsp:include page="/traderCustomer/dealerEditView.do?traderCustomerId=${traderCustomer.traderCustomerId}"/>
	</div>
	<br>
	<br>
	<div class="add-tijiao tcenter">
		<input type="hidden" name="isAiAssistant" value="${isAiAssistant}"/>
		<input type="hidden" name="aiCommunicateRecordId" value="${aiCommunicateRecordId}"/>
		<input type="hidden" name="traderNameBefore"
			   id="traderNameBefore" value="${traderCustomer.trader.traderName }" />
		<input type="hidden" name="traderId" value="${traderCustomer.traderId }">
		<input type="hidden" name="isLcfx" value="0">
		<input type="hidden" id="traderCustomerId" name="traderCustomer.traderCustomerId" value="${traderCustomer.traderCustomerId }">
		<input type="hidden" name="traderCustomer.traderCustomerCategoryId" id="traderCustomerCategoryId" value="${traderCustomer.traderCustomerCategoryId }">
		<input type="hidden" name="traderCustomerCategoryIds" id="traderCustomerCategoryIds">
		<input type="hidden" id="show_fenxiao" value="${show_fenxiao}">
		<input type="hidden" id="isDealer" value="${isDealer}">
		<input type="hidden" id="isTerminal" value="${isTerminal}">
		<input type="hidden" name="beforeParams" value='${beforeParams}'/>
		<input type="hidden" name="customerNatureBefore" id="customerNatureBefore" value="0"/>
		<input type="hidden" id="isCheckAptitudeStatus" name="isCheckAptitudeStatus" value="0"/>
		<input type="hidden" id="oldTraderType" name="oldTraderType" value="${traderCustomer.trader.traderType}"/>
		<button id="save" style="display: none" type="submit"></button>
		<button  type="button" class="submit" onclick="saveBaseInfo()">提交</button>
		<button class="dele" id="close-layer" type="button"
				onclick="goUrl('${pageContext.request.contextPath}/trader/customer/baseinfo.do?traderCustomerId=${traderCustomer.traderCustomerId}')">取消</button>

	</div>
	<br>
	<br>
</form>
<script type="text/javascript">
	$(function() {
		$("li[alt='fenxiao']").hide();
		$("li[alt='workAreaLi']").hide();

		if ($("#show_fenxiao").val() == "true") {
			$("li[alt='fenxiao']").show();
		}
		if ($("#isDealer").val() == "true") {
			$(".dealer5").show();
			$("#dealerData").show();
			$(".dealerNoShow").hide();
			$("select[name='traderCustomer.employees']").val('0');
			$("select[name='traderCustomer.annualSales']").val('0');
			$("input[name='traderCustomer.registeredDateStr']").val('');
			$("input[name='traderCustomer.registeredCapital']").val('');
		} else {
			$(".dealer5").hide();
		}
        if ($("#isTerminal").val() == 'true') {
            oneDataManagementShowOrhide(true);
        } else {
            oneDataManagementShowOrhide(false);
        }
		var selectedValue = $('#customer_category').val();

        if (selectedValue == 2) {
            $("li[alt='workAreaLi']").show();
        }



		$("select[name='warehouseProvince']").change(function(){
			checkLogin();
			var regionId = $(this).val();
			if(regionId > 0){
				$.ajax({
					type : "POST",
					url : page_url+"/system/region/getregion.do",
					data :{'regionId':regionId},
					dataType : 'json',
					success : function(data) {
						debugger;
						$option = "<option value='0'>请选择</option>";
						$.each(data.listData,function(i,n){
							$option += "<option value='"+data.listData[i]['regionId']+"'>"+data.listData[i]['regionName']+"</option>";
						});
						$("select[name='warehouseCity'] option:gt(0)").remove();
						$("select[name='warehouseZone'] option:gt(0)").remove();
						$("#warehouseCity").val("0").trigger("change");
						$("#warehouseZone").val("0").trigger("change");

						$("select[name='warehouseCity']").html($option);
						$("select[name='warehouseZone']").html("<option value='0'>请选择</option>");
					},
					error:function(data){
						if(data.status ==1001){
							layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
						}
					}
				});
			}else if(regionId==0){
				$("select[name='warehouseCity'] option:gt(0)").remove();
				$("select[name='warehouseZone'] option:gt(0)").remove();
			}
		});

		$("select[name='warehouseCity']").change(function(){
			checkLogin();
			var regionId = $(this).val();
			if(regionId > 0){
				$.ajax({
					type : "POST",
					url : page_url+"/system/region/getregion.do",
					data :{'regionId':regionId},
					dataType : 'json',
					success : function(data) {
						$option = "<option value='0'>请选择</option>";
						$.each(data.listData,function(i,n){
							$option += "<option value='"+data.listData[i]['regionId']+"'>"+data.listData[i]['regionName']+"</option>";
						});
						$("select[name='warehouseZone'] option:gt(0)").remove();

						$("select[name='warehouseZone']").html($option);
					},
					error:function(data){
						if(data.status ==1001){
							layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
						}
					}
				});
			}
		});



	});
</script>
<script type="text/javascript"
	src="${pageContext.request.contextPath}/static/js/customer/edit_baseinfo.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
	src="${pageContext.request.contextPath}/static/js/region/index.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp"%>
