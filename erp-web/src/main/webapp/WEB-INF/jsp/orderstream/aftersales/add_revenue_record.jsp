<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增售后收入记录" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%=basePath%>static/js/orderstream/aftersales/add_revenue_record.js?rnd=${resourceVersionKey}'></script>
<div class="formpublic">
    <form method="post" action="" id="addRevenueRecord">
        <ul>
            <li>
                <div class="infor_name">
                    <lable>费用类型：</lable>
                </div>
                <div class="f_left  ">
                    <select class="mr5" name="type" id="type">
                        <option value="">无</option>
                        <c:forEach var="list" items="${typeList}" varStatus="num">
                            <option value="${list.sysOptionDefinitionId}">${list.title}</option>
                        </c:forEach>
                    </select>
                </div>
            </li>
            <li>
                <div class="infor_name">
                    <lable>支付方：</lable>
                </div>
                <div class="f_left">
                    <input type="text" style="width: 450px;" name="payer" id="payer" placeholder="请输入支付方名称" maxlength="100">
                    <div id="payerError"></div>
                </div>
            </li>
            <li>
                <div class="infor_name">
                    <lable>支付金额：</lable>
                </div>
                <div class="f_left table-largest">
                    <div>
                        <input type="text" name="amount" onkeyup='clearNoNum(this)' id="amount">
                        <div id="amountError"></div>
                    </div>
                </div>
            </li>
            <li>
                <div class="infor_name">
                    <lable>备注：</lable>
                </div>
                <div class="f_left table-largest">
                    <div>
                        <input type="text" name="remark"  id="remark" maxlength="200">
                        <div id="remarkError"></div>
                    </div>
                </div>
            </li>
        </ul>
        <div class="add-tijiao tcenter">
            <input type="hidden" name="formToken" value="${formToken}"/>
            <input type="hidden" name="afterSalesId" value="${afterSalesRevenueRecord.afterSalesId}">
            <button class="dele" id="close-layer" type="button"  style="background-color: #f5f7fa;border-color: #ced2d9;color: black;">取消</button>
            <button type="submit" id="submit" style="background-color: #3384ef;border-color: #fff;">确定</button>
        </div>
    </form>
</div>
<%@ include file="../../common/footer.jsp"%>