package com.vedeng.erp.trader.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;


/**
 * @description 客户经营区域
 * <AUTHOR>
 * @date 2023/8/11 13:35
 **/
@Getter
@Setter
@ToString
@NoArgsConstructor
public class TraderCustomerBussinessAreaDto {
    private Integer traderCustomerBussinessAreaId;

    /**
    * 客户ID
    */
    private Integer traderCustomerId;

    /**
    * 最小级地区ID
    */
    private Integer areaId;

    /**
    * 多级地址逗号“,”拼接（冗余字段）
    */
    private String areaIds;
}