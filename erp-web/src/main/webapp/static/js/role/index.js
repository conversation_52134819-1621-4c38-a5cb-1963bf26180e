$(function(){
});


//删除角色
function delRole(roleId){
	checkLogin();
	if(roleId > 0){
		layer.confirm("您是否确认删除？", {
			  btn: ['确定','取消'] //按钮
			}, function(){
				$.ajax({
					type: "POST",
					url: "./deleterole.do",
					data: {'roleId':roleId},
					dataType:'json',
					success: function(data){
						refreshPageList(data);
					},
					error:function(data){
						if(data.status ==1001){
							layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
						}
					}
				});
			}, function(){
		});
	}
}