package com.vedeng.erp.trader.service;

import com.vedeng.erp.trader.dto.TraderAddressDto;

import java.util.List;

/**
 * <AUTHOR>
 * @description 客户供应商地址
 * @date 2022/10/20 19:14
 **/
public interface TraderAddressApiService {

    TraderAddressDto findTraderAddressById(Integer traderAddressId);

    /**
     * 根据查询条件查询客户地址  traderId 必填
     * @param recordDtoPageParam 查询数据
     * @return 地址数据
     */
    List<TraderAddressDto> list(TraderAddressDto recordDtoPageParam);

    /**
     * 查询最近交易过的2个客户联系地址
     *
     * @param traderId traderId
     * @return TraderAddressDto
     */
    List<TraderAddressDto> getLatestTransactionAddress(Integer traderId);

    /**
     * 根据地址模糊查询
     */
    List<TraderAddressDto> findByTraderIdAndTraderTypeAndAddressLike(Integer traderId,
                                                                     Integer traderType,
                                                                     String keywords);
}
