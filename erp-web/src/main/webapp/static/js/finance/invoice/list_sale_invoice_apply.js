
function resetPage(){
	reset();
	$("#validStatus").val(0);
    resetTicketReasonEqualList();
}
function saveOpenInvoiceAudit(invoiceApplyId,status){
	layer.confirm("您是否确认审核不通过？", {
		btn : [ '确定', '取消' ]
	}, function() {
		checkLogin();
		$.ajax({
			async : true,
			url : './saveOpenInvoiceAudit.do',
			data : {"invoiceApplyId":invoiceApplyId,"validStatus":status},
			type : "POST",
			dataType : "json",
			success : function(data) {
				refreshNowPageList(data);
			},error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	});
}
function exportInvoiceApplyList(){
	checkLogin();
	location.href = page_url + '/report/finance/exportInvoiceApplyList.do?' + $("#search").serialize();
}

function openEInvoice(invoiceApplyId,relatedId,formToken){
	$("#openEinvoiceId").attr("disabled", true);
	var lock = false;//默认未锁定
	layer.confirm("确定开具发票？", {
		  btn: ['确定','取消'] //按钮
		}, function(){
			if (!lock) {
				lock = true;// 锁定

                // 关闭确认对话框
                layer.closeAll('dialog');
                // 显示加载中的提示
                var loadingIndex = layer.msg('加载中', { icon: 16, shade: 0.01, time: 0 });

                //校验开票申请状态
                var validStatus;
                $.ajax({
                    async : false,
                    url:'/invoiceApply/api/checkInvoiceApplyStStatus.do?invoiceApplyId='+invoiceApplyId,
                    type:'GET',
                    dataType:"json",
                    success: function (res){
                        if (1 == res.data){
                            validStatus = 1;
                        }
                    }
                })
                if (1 == validStatus){
                    layer.alert("数据状态为已开票，不可开票");
                    // 隐藏加载中的提示
                    layer.close(loadingIndex);
                    $("#openEinvoiceId").attr("disabled", false);
                    return false;
                }
				$.ajax({
					type: "POST",
					url: "./openEInvoicePush.do",
					data: {"invoiceApplyId":invoiceApplyId,"relatedId":relatedId,"formToken":formToken},
					dataType:'json',
					success: function(data){
                        if (data.message == 'fail'){
                            layer.confirm(data.data,{
                                closeBtn: 0,
                                btn: ['确定']
                            }, function() {
                                window.location.reload();
                            })
                        }else {
                            refreshPageList(data)
                        }
                        // 隐藏加载中的提示
                        layer.close(loadingIndex);
					},
					error: function(data){
						if(data.status ==1001){
							layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
						}
                        // 隐藏加载中的提示
                        layer.close(loadingIndex);
                        return false;
					}
				});
			}
		}, function(){
			$("#openEinvoiceId").attr("disabled", false);
		});
}

function checkedOnly(obj){
    //$(obj).attr("disabled",true)
    //setTimeout(function(){$(obj).attr("disabled",false)}, 1500);
	var checkNum = Number($("#checkNum").html());
    var checkAmount = parseFloat($("#checkAmount").html());
    if($(obj).is(":checked")){
        var n = 0;
        $("#invoice_apply_list_tab").find("input[type='checkbox'][name='checkName']").each(function(){
            if($(this).is(":checked")){
                n++;
            }else{
                return false;
            }
        });
        if($("#invoice_apply_list_tab").find("input[type='checkbox'][name='checkName']").length == n){
            $("#invoice_apply_list_tab").find("input[type='checkbox'][name='checkAllOpt']").prop("checked",true);
        }
        checkNum++;
        checkAmount = checkAmount + parseFloat($(obj).attr("amount"));
    }else{
        $("#invoice_apply_list_tab").find("input[type='checkbox'][name='checkAllOpt']").prop("checked",false);
        checkNum--;
        checkAmount = checkAmount - parseFloat($(obj).attr("amount"));
    }
    $("#checkNum").html(checkNum);
    $("#checkAmount").html(checkAmount.toFixed(2));
}

function checkAllOpt(obj){
    var checkNum = 0;
    var checkAmount = 0.00;
    if($(obj).is(":checked")){
        $("#invoice_apply_list_tab").find("input[type='checkbox'][name='checkName']").each(function(){
            $(this).prop("checked",true);
            checkNum++;
            checkAmount = checkAmount + parseFloat($(this).attr("amount"));
        });
    }else{
        $("#invoice_apply_list_tab").find("input[type='checkbox'][name='checkName']").each(function(){
            $(this).prop("checked",false);
        });
    }
    $("#checkNum").html(checkNum);
    $("#checkAmount").html(checkAmount.toFixed(2));
}

function invoiceSign(sign) {
    var invoiceApplyIdArr = [];var num = 0;
    $("#invoice_apply_list_tab").find("input[type='checkbox'][name='checkName']:checked").each(function(){
        if($(this).is(":checked")){
            invoiceApplyIdArr.push($(this).val());
        }
        if($(this).attr("isSign") == sign) {
        	num++;
        }
    });
    if(num > 0){
        var str = "";
        if(sign == 0){
            str = "已选择的记录中存在未标记发票，请验证！";
        } else {
            str = "已选择的记录中存在已标记发票，请验证！"
        }
		layer.alert(str, {icon: 2},
			function (index) {
				layer.close(index);
			}
		);
		return false;
	}
    if(invoiceApplyIdArr.length == 0){
        var str = "";
        if(sign == 0){
            str = "请选择需要取消标记的发票记录！";
        } else {
            str = "请选择需要标记的发票记录！"
        }
        layer.alert(str, {icon: 2},
            function (index) {
                layer.close(index);
            }
        );
        return false;
    }
    var str = "";
    if(sign == 0){
        str = "您是否确认取消标记已选择的发票记录？";
    } else {
        str = "您是否确认标记已选择的发票记录？"
    }
    layer.confirm(str, {
        btn : [ '确定', '取消' ]
    }, function() {
        $.ajax({
            async : false,
            url : './updateInvoiceApplySign.do',
            data : {"invoiceApplyIdArr":JSON.stringify(invoiceApplyIdArr),"isSign":sign},
            type : "POST",
            dataType : "json",
            success : function(data) {
                refreshNowPageList(data);
            }
        });
    });
}

function invoiceBatch(formToken){
    var invoiceApplyIdArr = [];var num = 0;
    $("#invoice_apply_list_tab").find("input[type='checkbox'][name='checkName']:checked").each(function(){
        if($(this).is(":checked")){
            invoiceApplyIdArr.push($(this).val());
        }

    });

    if(invoiceApplyIdArr.length == 0){
        var str = "请选择需要打印的发票记录";
        /*if(sign == 0){
            str = "请选择需要取消标记的发票记录！";
        } else {
            str = "请选择需要标记的发票记录！"
        }*/
        layer.alert(str, {icon: 2},
            function (index) {
                layer.close(index);
            }
        );
        return false;
    }
    var str = "您是否确认批量开票";
    var lock = false;//默认未锁定
    /*if(sign == 0){
        str = "您是否确认取消标记已选择的发票记录？";
    } else {
        str = "您是否确认标记已选择的发票记录？"
    }*/
    layer.confirm(str, {
        btn : [ '确定', '取消' ]
    }, function() {
        if (!lock) {
            lock = true;// 锁定
            layer.closeAll();
            var index = layer.load(1);
            $.ajax({
                type: "POST",
                url: "./openEInvoiceBatchPush.do",
                data: {"invoiceApplyIdArr": JSON.stringify(invoiceApplyIdArr), "formToken": formToken},
                dataType: 'json',
                success: function (data) {
                    if (data.message == 'failList' && data.data.length != 0){
                        layer.closeAll();
                        var failList = data.data;
                        console.log(failList)
                        var trList = [];
                        $.each(failList, function (i, item) {
                            trList.push(`<tr>
                                        <td>${item.invoiceApplyId}</td>
                                        <td>${item.saleorderNo}</td>
                                        <td>${item.failMessage}</td>
                                    </tr>`);
                        })
                        layer.open({
                            type: 1,
                            area: ['800px', '400px'], // 宽高
                            title: '操作失败信息', // 不显示标题栏
                            closeBtn: 1,
                            shadeClose: true, // 点击遮罩关闭层
                            content: `
                                <table class="table table-bordered table-striped table-condensed table-centered">
                                    <thead>
                                        <tr>
                                            <th>开票申请ID</th>
                                            <th>订单号</th>
                                            <th>失败原因</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${trList.join('')}
                                    </tbody>
                                </table>
                            `,
                            end: function (){
                                parent.layer.closeAll();
                                window.location.reload();
                            }
                        })
                    }else {
                        refreshPageList(data)
                    }
                },
                error: function (data) {
                    if (data.status == 1001) {
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                },
                complete: function () {
                    layer.close(index);
                }

            });
        }
    });
}

function invoiceBatchRefuse(formToken) {
    const invoiceApplyIdArr = $("#invoice_apply_list_tab input[type='checkbox'][name='checkName']:checked")
        .map(function() {
            return $(this).val();
        })
        .get();

    if (invoiceApplyIdArr.length === 0) {
        const str = "请选择需要驳回的发票记录";
        layer.alert(str, { icon: 2 }, function(index) {
            layer.close(index);
        });
        return false;
    }

    layer.open({
        title: "批量驳回",
        area: ["500px", "200px"],
        content:
            '<div class="infor_name infor_name1"><span>*</span>审核不通过原因</div><input type="text" class="input-larger" name="comments" id="comments">',
        yes: function(index, layero) {
            const c = $("#comments").val();
            if (!c) {
                layer.alert("驳回原因不能为空");
                return;
            }
            if (c.length > 128) {
                layer.alert("驳回原因不能超过128位字符");
                return;
            }

            checkLogin();
            $.ajax({
                async: true,
                url: "./refuseOpenInvoiceAuditList.do",
                data: { invoiceApplyIds: JSON.stringify(invoiceApplyIdArr), validComments: c },
                type: "POST",
                dataType: "json",
                success: function(data) {
                    if (data.code == 0) {
                        refreshNowPageList(data);
                    } else {

                        layui.use(['layer', 'table'], function () {
                            var layer = layui.layer;
                            var table = layui.table;
                            layer.open({
                                title: '异常信息',
                                area: ['800px', '400px'],
                                content: '<table id="errorTable"></table>', // 弹出层中的内容是一个表格
                                success: function(layero, index){
                                    // 在弹出层创建成功后，使用 layui 的 table 模块渲染表格
                                    table.render({
                                        elem: '#errorTable',
                                        data: data.data,
                                        cols: [[
                                            {field: 'invoiceApplyId', title: 'ID'},
                                            {field: 'orderNo', title: '编码'},
                                            {field: 'msg', title: '失败原因'}
                                        ]]
                                    });
                                },
                                btn:[],
                                cancel: function () {
                                    layer.closeAll();
                                },
                            });
                        });

                    }
                },
                error: function(data) {
                    if (data.status === 1001) {
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
                    }
                }
            });
        }
    });
}

function showCustomIcon() {
    let status = 0;
    $.ajax({
        type: "GET",
        url: "/fullyDigitalInvoice/api/getCurrentInvoiceSwitch.do",
        dataType: "json",
        async: false,
        success: function (data) {
            status = data.data
        }
    })
    if (status == 1) {
        layer.open({
            type: 1,
            title: '设置',
            offset: 'r',
            anim: 'slideLeft', // 从右往左
            area: ['320px', '100%'],
            shade: 0.1,
            shadeClose: true,
            id: 'ID-demo-layer-direction-r',
            content: '<input type="checkbox" checked name="checkAllOpt" id="changeStatus" style="padding-top: 20px;padding-left: 3px" onchange="changeStatus();">自动开具数电发票'
        })
    } else {
        layer.open({
            type: 1,
            title: '设置',
            offset: 'r',
            anim: 'slideLeft', // 从右往左
            area: ['320px', '100%'],
            shade: 0.1,
            shadeClose: true,
            id: 'ID-demo-layer-direction-r',
            content: '<input type="checkbox" name="checkAllOpt" id="changeStatus" style="padding-top: 20px;padding-left: 3px" onchange="changeStatus();">自动开具数电发票'
        })
    }
}
function changeStatus(){
    var status = $('#changeStatus').prop('checked')
    $.ajax({
        type: "GET",
        url: "/fullyDigitalInvoice/api/changeInvoiceSwitch.do?status="+status
    })
}
function showInvoiceLimit(){
    var limitIndex = layer.load(0,{shade: [0.1,'#fff']});
    $.ajax({
        type: "GET",
        url: "/fullyDigitalInvoice/api/getInvoiceLimit.do",
        dataType:'json',
        success: function(res){
            console.log(res.data);
            var sfsb = res.data.sbbz == 'N' ? '否': '是'
            layer.open({
                type: 1,
                title: '查看开票额度',
                offset: 'r',
                anim: 'slideLeft', // 从右往左
                area: ['320px', '100%'],
                shade: 0.1,
                shadeClose: true,
                id: 'ID-layer-invoice-limit',
                content: '<div style="padding-top: 14px">可用授信额度：'+res.data.sysxed+'</div>'+
                    '<div style="padding-top: 14px">总授信额度：'+res.data.zsxed+'</div>'+
                    '<div style="padding-top: 14px">蓝字发票开具金额：'+res.data.fphjje+'</div>'+
                    '<div style="padding-top: 14px">累计税额：'+res.data.fpejse+'</div>'+
                    '<div style="padding-top: 14px">申报标志：'+sfsb+'</div>'
            })
        }
    })
    setTimeout(() => {
        layer.close(limitIndex)
    },500);

}

document.addEventListener('DOMContentLoaded', function () {
    // 获取 <span> 元素
    var invoiceConfigurationSpan = document.getElementById('invoiceConfigurationSpan');
    // 获取关联的 <a> 元素
    var invoiceConfigurationLink = document.getElementById('invoiceConfigurationLink');

    // 为 <span> 元素添加点击事件监听器
    invoiceConfigurationSpan.addEventListener('click', function () {
        // 触发关联的 <a> 标签的 click 事件
        invoiceConfigurationLink.click();
    });
});

