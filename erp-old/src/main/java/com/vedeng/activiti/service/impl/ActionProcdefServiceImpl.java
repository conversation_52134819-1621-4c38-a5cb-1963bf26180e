package com.vedeng.activiti.service.impl;

import com.alibaba.fastjson.JSON;
import com.common.constants.Contant;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Splitter;
import com.vedeng.activiti.dao.ActionProcdefMapper;
import com.vedeng.activiti.model.ActionProcdef;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.exception.ShowErrorMsgException;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.common.util.StringUtil;
import com.vedeng.system.service.UserService;
import net.sf.json.JSONObject;
import org.activiti.engine.*;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.history.HistoricVariableInstance;
import org.activiti.engine.impl.persistence.entity.CommentEntity;
import org.activiti.engine.impl.persistence.entity.HistoricActivityInstanceEntity;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Comment;
import org.activiti.engine.task.Event;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;

@Service("actionProcdefService")
public class ActionProcdefServiceImpl implements ActionProcdefService {
	public static Logger logger = LoggerFactory.getLogger(ActionProcdefServiceImpl.class);

    	@Value("${http_url}")
	protected String httpUrl;
    	
    	@Value("${client_id}")
	protected String clientId;
	
	@Value("${client_key}")
	protected String clientKey;
    
	@Autowired
	@Qualifier("actionProcdefMapper")
	private ActionProcdefMapper actionProcdefMapper;

	@Resource
	private UserService userService;


	private ProcessEngine getProcessEngine() {
		return ProcessEngines.getDefaultProcessEngine();// 流程引擎
	}

	@Override
	public List<ActionProcdef> getList() {
		ActionProcdef actionProcdef = new ActionProcdef();
		return actionProcdefMapper.getList(actionProcdef);
	}

	@Override
	public List<ActionProcdef> getList(ActionProcdef actionProcdef) {
		return actionProcdefMapper.getList(actionProcdef);
	}

	@Override
	public List<ActionProcdef> getList(int companyId) {
		ActionProcdef actionProcdef = new ActionProcdef();
		actionProcdef.setCompanyId(companyId);
		return actionProcdefMapper.getList(actionProcdef);
	}

	@Override
	public List<ActionProcdef> getByAcitonId(int actionId, int companyId) {
		ActionProcdef actionProcdef = new ActionProcdef();
		actionProcdef.setCompanyId(companyId);
		actionProcdef.setActionId(actionId);
		return actionProcdefMapper.getList(actionProcdef);
	}

	@Override
	public List<ActionProcdef> getByAcitonId(int actionId) {
		ActionProcdef actionProcdef = new ActionProcdef();
		actionProcdef.setActionId(actionId);
		return actionProcdefMapper.getList(actionProcdef);
	}

	@Override
	public List<ActionProcdef> getByPreBusinessKey(String preBusinessKey, int companyId) {
		ActionProcdef actionProcdef = new ActionProcdef();
		actionProcdef.setCompanyId(companyId);
		actionProcdef.setPreBusinessKey(preBusinessKey);
		return actionProcdefMapper.getList(actionProcdef);
	}

	@Override
	public List<ActionProcdef> getByPreBusinessKey(String preBusinessKey) {
		ActionProcdef actionProcdef = new ActionProcdef();
		actionProcdef.setPreBusinessKey(preBusinessKey);
		return actionProcdefMapper.getList(actionProcdef);
	}

	@Override
	public List<ActionProcdef> getByProcdefId(String procdefId, int companyId) {
		ActionProcdef actionProcdef = new ActionProcdef();
		actionProcdef.setCompanyId(companyId);
		actionProcdef.setProcdefId(procdefId);
		return actionProcdefMapper.getList(actionProcdef);
	}

	@Override
	public List<ActionProcdef> getByProcdefId(String procdefId) {
		ActionProcdef actionProcdef = new ActionProcdef();
		actionProcdef.setProcdefId(procdefId);
		return actionProcdefMapper.getList(actionProcdef);
	}

	@Override
	public int insert(ActionProcdef actionProcdef) {
		return actionProcdefMapper.insert(actionProcdef);
	}

	@Override
	public int replace(ActionProcdef actionProcdef) {
		return actionProcdefMapper.replace(actionProcdef);
	}

	@Override
	public List<ActionProcdef> getListPage(ActionProcdef actionProcdef, Page page) {
		Map<String, Object> map = new HashMap<>();
		map.put("actionProcdef", actionProcdef);
		map.put("page", page);
		return actionProcdefMapper.getListPage(map);
	}

	@Override
	public List<ActionProcdef> getListPage(Page page) {
		Map<String, Object> map = new HashMap<>();
		map.put("page", page);
		return actionProcdefMapper.getListPage(map);
	}

	@Override
	public Map<String, Object> getHistoric(ProcessEngine processEngine,String businessKey) {
		 ServletRequestAttributes ra= (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		HttpServletRequest request=null;
		 if(ra!=null) {
			 request = ra.getRequest();
		 }
		 User user;

		 if(request!=null && null != request.getSession()){
		    user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);}
		 else {
		 	user=new User();
		 	user.setCompanyId(1);
		 	user.setUserId(2);
		 	user.setUsername("njadmin");
		 }
		 return getHistoricByUser(processEngine,businessKey,user);
	}

	@Override
	public Map<String, Object> getHistoricForNjadmin(ProcessEngine processEngine,String businessKey) {
		User user =new User();
		user.setUserId(2);
		user.setUsername("njadmin");
		return getHistoricByUser(processEngine,businessKey,user);
	}

	private Map<String, Object> getHistoricByUser(ProcessEngine processEngine,String businessKey,User user) {
		Map<String, Object> map = new HashMap<>();
		// 获取订单审核信息
		TaskService taskService = processEngine.getTaskService(); // 任务相关service
		// 获取当前活动节点
		Task taskInfo =null;

		try {
			taskInfo=taskService.createTaskQuery().processInstanceBusinessKey(businessKey)
					.singleResult();
		} catch (Exception e) {
			logger.error("taskInfo",e);
			List<Task> taskInfoList=taskService.createTaskQuery().processInstanceBusinessKey(businessKey)
					.list();
			if (CollectionUtils.isNotEmpty(taskInfoList)){
				taskInfo=taskInfoList.get(0);
			}
		}

		map.put("taskInfo", taskInfo);
		List<HistoricProcessInstance> historicProcessInstance = new ArrayList<>();
		List<HistoricActivityInstance> historicActivityInstance = new ArrayList<>();
		Map<String, Object> commentMap = new HashMap<String, Object>();
		Map<String, Object> candidateUserMap = new HashMap<String, Object>();
		candidateUserMap.put("belong", false);
		String startUser = null;
		String processInstanceId = null;
		String endStatus = null;
		// 获取该订单所有审核流程历史纪录
		HistoryService historyService = processEngine.getHistoryService(); // 任务相关service
		historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
				.processInstanceBusinessKey(businessKey).orderByProcessInstanceStartTime().asc().list();
		// 如果不为空，循环获取每个审核流程中的记录
		if (null != historicProcessInstance && !historicProcessInstance.isEmpty()) {
			for (HistoricProcessInstance hi : historicProcessInstance) {
				List<HistoricActivityInstance> hia = historyService.createHistoricActivityInstanceQuery()
						.processInstanceId(hi.getId()).orderByHistoricActivityInstanceStartTime().asc().list();
				processInstanceId = hi.getId();

				//根据流程ID去查询对应的节点备注信息
				List<Comment> commentList = taskService.getProcessInstanceComments(processInstanceId);
				//获取候选人员信息表
				for (Comment c : commentList) {
					// 备注信息塞入
					commentMap.put(c.getTaskId(), c.getFullMessage());
				}

				//since ERP_LV_2020_69 审核历史记录增加伪数据--审核供应供应商时
				if(StringUtils.isNotEmpty(businessKey)&&businessKey.startsWith("traderSupplierVerify_")) {
					if (hia!=null&&hia.size()>0) {
						for (HistoricActivityInstance currentHistoricalRecord : hia) {
							if(StringUtil.isNotEmpty(currentHistoricalRecord.getAssignee())){
								if(!(currentHistoricalRecord instanceof HistoricActivityInstanceEntity)){
									continue;
								}

								StringJoiner assigneeNameJoiner=new StringJoiner(ErpConst.Symbol.COMMA);
								Iterable<String> assigneeNames = Splitter.on(ErpConst.Symbol.COMMA).trimResults().split(currentHistoricalRecord.getAssignee());
								assigneeNames.forEach(assigneeName->{
									if (!assigneeName.contains("(")) {
										User userQuery = userService.getByUsername(assigneeName, 1);
										if(userQuery!=null&&StringUtil.isNotEmpty(userQuery.getRealName())){
											assigneeNameJoiner.add(userQuery.getUsername()+"("+userQuery.getRealName()+")");
										}
									} else {
										assigneeNameJoiner.add(assigneeName);
									}
								});

								((HistoricActivityInstanceEntity)currentHistoricalRecord).setAssignee(assigneeNameJoiner.toString());
							}
						}
					}

					if(hia!=null && hia.size()>1){
						HistoricActivityInstance activityAuditRecord = hia.stream().filter(record -> record != null && Objects.equals(record.getActivityName(), "审核完成")).findFirst().orElse(null);
						if(activityAuditRecord!=null){
							HistoricActivityInstance lastAuditRecord = hia.stream().filter(record -> record != null && Objects.equals(record.getActivityName(), "供应管理组助理审核")).findFirst().orElse(null);
							if(lastAuditRecord!=null){

								HistoricActivityInstanceEntity tempRecordToCheck = new HistoricActivityInstanceEntity();
								tempRecordToCheck.setAssignee("Jessie(罗璇)");
								tempRecordToCheck.setActivityName("质量管理部审核");

								if( lastAuditRecord.getEndTime()!=null){
									Date targetTime = new DateTime(lastAuditRecord.getEndTime()).plusMinutes(5).toDate();
									tempRecordToCheck.setEndTime(targetTime);
									if(activityAuditRecord instanceof HistoricActivityInstanceEntity){
										((HistoricActivityInstanceEntity )activityAuditRecord).setEndTime(targetTime);
									}
								}

								LinkedList<HistoricActivityInstance> eachRecordList =new LinkedList<>(Arrays.asList(hia.toArray(new HistoricActivityInstance[0])));
								eachRecordList.add(hia.size()-1, tempRecordToCheck);
								hia = eachRecordList;
							}

						}
					}
				}

				historicActivityInstance.addAll(hia);
			}
			endStatus = historicActivityInstance.get(historicActivityInstance.size() - 1).getActivityName();

		}
		if (null != processInstanceId) {
			startUser = processEngine.getHistoryService().createHistoricProcessInstanceQuery()
					.processInstanceId(processInstanceId).singleResult().getStartUserId();

		}
		try
		{
			if (Objects.nonNull(taskInfo) && Objects.nonNull(taskInfo.getId())) {
				//获取当前审核候选人
				List<IdentityLink> candidateUserList = taskService.getIdentityLinksForTask(taskInfo.getId());
				candidateUserMap.put(taskInfo.getId(),candidateUserList);
				if(candidateUserList!=null && candidateUserList.size()>0){
					for(IdentityLink identityLink:candidateUserList){
						if(identityLink.getUserId().equalsIgnoreCase(user.getUsername())){
							candidateUserMap.put("belong",true);
						}

					}
				}
			}

		}catch (Exception e) {
			//ignor
			//logger.error("getHistoric exception",e);
		}
		map.put("candidateUserMap", candidateUserMap);
		map.put("startUser", startUser);
		// 最后审核状态
		map.put("endStatus", endStatus);
		map.put("historicActivityInstance", historicActivityInstance);
		map.put("commentMap", commentMap);
		return map;
	}

	@Override
	public int updateVerifyInfo(String tableName,Integer idValue,Integer value) {
		try {
			actionProcdefMapper.updateVerifyInfo(tableName,idValue,value);
			return 1;
		} catch (Exception e) {
			logger.error("updateVerifyInfo",e);
			return -1;
		}
	}

	@Transactional(propagation = Propagation.REQUIRES_NEW)
	@Override
	public int updateInfo(String tableName, String id, Integer idValue, String key, Integer value,Integer db) {
		if(db == 1){
			return actionProcdefMapper.updateInfo(tableName,id,idValue,key,value);
		}else{
			try {
				// 带修改时间和审核时间
				if (StringUtils.isNotBlank(key) && key.equals("LOCKED_STATUS")) {
					actionProcdefMapper.updateInfo(tableName, id,idValue,key,value);

				} else {
					actionProcdefMapper.updateInfoTime(tableName,id,
							idValue,key, value,
							DateUtil.sysTimeMillis(), DateUtil.sysTimeMillis());
				}
			} catch (Exception e) {
				logger.error("updateInfo: {} {} {} {} {} ",tableName,id,idValue,key,value,e);
				// 如果没有修改时间和审核时间字段
				actionProcdefMapper.updateInfo(tableName, id,
						idValue, key, value);
				return -1;
			}
			return 1;
		}
	}

	@Override
	public void addCandidate(String taskId, List<String> userName) {
		if(CollectionUtils.isEmpty(userName)){
			return;
		}
//		HistoryService historyService = this.getProcessEngine().getHistoryService(); // 任务相关service
		TaskService taskService = this.getProcessEngine().getTaskService();
		for(String n:userName) {
			taskService.addCandidateUser(taskId, n);
		}
	}

	@Override
	public ResultInfo<?> complementTask(HttpServletRequest request, String taskId, String comment,String assignee,
		Map<String, Object> variables) {
			logger.info("================开始完成审核节点===taskId:"+taskId+"===comment:"+comment+"===assignee:"+assignee+"================");
		User user = null;
		if(request!=null) {
			user=(User) request.getSession().getAttribute(ErpConst.CURR_USER);
		}else{
			user=new User();
			user.setUserId(2);
			user.setUsername("njadmin");
			user.setCompanyId(1);
		}
		HistoryService historyService = this.getProcessEngine().getHistoryService(); // 任务相关service
		TaskService taskService = this.getProcessEngine().getTaskService();
		// 使用任务id,获取任务对象，获取流程实例id
		Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
		if(task == null){
			return new ResultInfo<>(-1, "流程此节点已操作完成，请确认");
		}
		// 利用任务对象，获取流程实例id
		String processInstancesId = task.getProcessInstanceId();
		if(comment != null){
		    taskService.addComment(taskId, processInstancesId, comment);
		}
		if(assignee != null){
		    taskService.setAssignee(taskId, assignee);
		}

		try {
			taskService.complete(taskId, variables);
			logger.info("================完成审核节点==={}:================",taskId);
			List<HistoricActivityInstance> hia = historyService.createHistoricActivityInstanceQuery()
					.processInstanceId(processInstancesId).orderByHistoricActivityInstanceStartTime().asc().list();
			String endStatus = hia.get(hia.size() - 1).getActivityType();
//			//打印历史节点日志
//			for(HistoricActivityInstance hiai:hia){
//				logger.info("================历史审核节点对应信息===taskId:"+taskId+"=================ActivityType:"+(hiai!=null ? hiai.getActivityType() : ""));
//			}
//            logger.info("================结束完成审核节点===taskId:"+taskId+"===comment:"+comment+"===assignee:"+assignee+"=====endStatus:"+endStatus+"===========");
			return new ResultInfo(0, "操作成功",endStatus);
		} catch (Exception e) {
			logger.error("complementTask任务完成操作失败,TaskId:{},data:{}" , taskId, JsonUtils.translateToJsonDefaultEmpty(variables),e);
			return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
		}
	}

	@Override
	public ResultInfo<?> completeTaskByAdmin(String taskId, String comment, Map<String, Object> variables) {
		logger.info("系统自动审核完成任务：{}，备注：{}，审核操作参数：{}",taskId,comment, JSONObject.fromObject(variables));
		TaskService taskService = this.getProcessEngine().getTaskService();
		Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
		if(task == null){
			logger.info("系统自动审核完成任务未找到");
			return new ResultInfo<>(-1, "流程此节点已操作完成，请确认");
		}
		String processInstancesId = task.getProcessInstanceId();
		try {
			if(comment != null){
				taskService.addComment(taskId, processInstancesId, comment);
			}
			taskService.setAssignee(taskId,"njadmin");
			taskService.complete(taskId,variables);
			logger.info("系统完成自动审核任务：{}",taskId);
			return ResultInfo.success();
		} catch (Exception e){
			logger.error("系统自动审核任务：" + taskId +"失败，e：",e);
		}
		return ResultInfo.error("系统自动审核任务失败");
	}

	@Override
	public ResultInfo<?> createProcessInstance(HttpServletRequest request, String processDefinitionKey,
		String businessKey, Map<String, Object> variables)throws Exception {
		logger.info("创建流程实例：" + processDefinitionKey + "，业务key：" + businessKey + "，参数：" + JSON.toJSONString(variables));
	    /**
	     * 流程名称saleorder+variables+businessKey
	     */
		TaskService taskService = this.getProcessEngine().getTaskService(); // 任务相关service
		Task taskInfo = taskService.createTaskQuery().processInstanceBusinessKey(businessKey)
				.singleResult();
		if(taskInfo != null){
			throw new ShowErrorMsgException("流程已发起请勿重复提交");
		}else{
			Object startUserObject = variables.get("startUser");
			User user = setAutenticatedUser(request, startUserObject != null ? startUserObject.toString() : null);
			// 运行时Service
			RuntimeService runtimeService = this.getProcessEngine().getRuntimeService();
			try {
				ProcessInstance	processInstance = runtimeService.startProcessInstanceByKeyAndTenantId(processDefinitionKey,
						businessKey, variables, "erp:company:" + user.getCompanyId());
				return new ResultInfo(0, "操作成功");
			} catch (Exception e) {
				logger.error(" error createProcessInstance任务完成操作失败",e);
				return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
			}
		}

	}

	@Override
	public Map<String, Object> getVariablesMap(Task taskInfo) {
	    if(null!=taskInfo){
		    //把list转为Map
		    Map<String, Object> variablesMap = new HashMap<String, Object>();
		    try {
			HistoryService historyService = this.getProcessEngine().getHistoryService(); // 任务相关service
			HistoricTaskInstance historicTaskInstance = historyService.createHistoricTaskInstanceQuery().taskId(taskInfo.getId()).singleResult();
			String processInstanceId = historicTaskInstance.getProcessInstanceId();
			List<HistoricVariableInstance> historicVariableInstanceList= historyService.createHistoricVariableInstanceQuery().processInstanceId(processInstanceId).list();
			
			for(HistoricVariableInstance hvi:historicVariableInstanceList){
				variablesMap.put(hvi.getVariableName(), hvi.getValue());
			}
			return variablesMap;
		    } catch (Exception e) {
				logger.error("getVariablesMap",e);
			return variablesMap;
		    }
	    	    
	    }else{
		return null;
	    }
	}
	
	@Override
	public Map<String, Object> getVariablesMap(String businessKey) {
	    if(null!=businessKey){
		    //把list转为Map
		    Map<String, Object> variablesMap = new HashMap<String, Object>();
		    try {
			HistoryService historyService = this.getProcessEngine().getHistoryService(); // 任务相关service
			List<HistoricProcessInstance> historicProcessInstance = new ArrayList<>();
			String processInstanceId = null;
			historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
	 			.processInstanceBusinessKey(businessKey).orderByProcessInstanceStartTime().asc().list();
                        	 // 如果不为空，循环获取每个审核流程中的记录
                        	 if (null != historicProcessInstance && !historicProcessInstance.isEmpty()) {
                        	 	for (HistoricProcessInstance hi : historicProcessInstance) {
                        	 	List<HistoricActivityInstance> hia = historyService.createHistoricActivityInstanceQuery()
                        	 					.processInstanceId(hi.getId()).orderByHistoricActivityInstanceStartTime().asc().list();
                        	 		processInstanceId = hi.getId();
                        	 	}
                        	 	
                        	 }
                        if(processInstanceId != null){
                            List<HistoricVariableInstance> historicVariableInstanceList= historyService.createHistoricVariableInstanceQuery().processInstanceId(processInstanceId).list();
                            for(HistoricVariableInstance hvi:historicVariableInstanceList){
                        	variablesMap.put(hvi.getVariableName(), hvi.getValue());
                            }
                        }
			return variablesMap;
		    } catch (Exception e) {
				logger.error("getVariablesMap",e);

			return variablesMap;
		    }
	    	    
	    }else{
		return null;
	    }
	}

	@Override
	public ResultInfo deleteProcessInstance(String taskId){
		if(StringUtil.isNotBlank(taskId)){
			try {
				HistoryService historyService = this.getProcessEngine().getHistoryService(); // 任务相关service
				HistoricTaskInstance historicTaskInstance = historyService.createHistoricTaskInstanceQuery().taskId(taskId).singleResult();
				String processInstanceId = historicTaskInstance.getProcessInstanceId();
				RuntimeService runtimeService = this.getProcessEngine().getRuntimeService();
				runtimeService.suspendProcessInstanceById(processInstanceId);
				runtimeService.deleteProcessInstance(processInstanceId,"销售撤回");
				return new ResultInfo(0,"操作成功");
			}catch (Exception ex){
				logger.error(Contant.ERROR_MSG,ex);
				return new ResultInfo(-1,"操作失败");
			}

		}
		return new ResultInfo(-1,"操作失败,任务id为空");
	}

	@Override
	public ResultInfo<?> createProcessInstanceByVersionId(HttpServletRequest request, String VersionId, String businessKey, Map<String, Object> variables)throws Exception {
		/**
		 * 流程版本id+variables+businessKey
		 */
		// 任务相关service
		TaskService taskService = this.getProcessEngine().getTaskService();
		Task taskInfo = taskService.createTaskQuery().processInstanceBusinessKey(businessKey)
				.singleResult();
		if(taskInfo != null){
			throw new Exception("流程已发起请勿重复提交");
		}else{
			Object startUserObject = variables.get("startUser");
			setAutenticatedUser(request, startUserObject != null ? startUserObject.toString() : null);
			// 运行时Service
			RuntimeService runtimeService = this.getProcessEngine().getRuntimeService();
			try {
				ProcessInstance processInstance = runtimeService.startProcessInstanceById(VersionId,businessKey,variables);
				return new ResultInfo(0, "操作成功");
			} catch (Exception e) {
				logger.error("任务完成操作失败",e);
				return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
			}
		}
	}

	private User setAutenticatedUser(HttpServletRequest request, String userName) {
		User user = null;
		if (StringUtil.isNotBlank(userName)){
			user = new User();
			user.setUsername(userName);
			user.setCompanyId(1);
			user = userService.getUserByName(user);
		} else {
			if(request != null){
				user  = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
			}
			if(request == null || user == null){
				user = new User();
				user.setUsername("njadmin");
				user.setCompanyId(1);
				user = userService.getUserByName(user);
			}
		}
		IdentityService identityService = this.getProcessEngine().getIdentityService();
		// 设置当前审核人(订单归属人)
		identityService.setAuthenticatedUserId(user.getUsername());
		return  user;
	}



	@Override
	public Map<String, Object> getLeast(ProcessEngine processEngine, String businessKey) {
		ServletRequestAttributes ra= (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		HttpServletRequest request=null;
		if(ra!=null) {
			request = ra.getRequest();
		}
		User user;

		if(request!=null && null != request.getSession()){
			user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);}
		else {
			user=new User();
			user.setCompanyId(1);
			user.setUserId(2);
			user.setUsername("njadmin");
		}
		return getLeastByUser(processEngine,businessKey,user);
	}

	@Override
	public String getLeastTaskOfBusinessKey(String businessKey) {
		Task taskInfo =null;
		TaskService taskService = this.getProcessEngine().getTaskService();
		try {
			taskInfo=taskService.createTaskQuery().processInstanceBusinessKey(businessKey)
					.singleResult();
		} catch (Exception e) {
			logger.error("taskInfo",e);
			List<Task> taskInfoList=taskService.createTaskQuery().processInstanceBusinessKey(businessKey)
					.list();
			if (CollectionUtils.isNotEmpty(taskInfoList)){
				taskInfo=taskInfoList.get(0);
			}
		}
		return taskInfo.getId();
	}

	@Override
	public void manualAddCommentForTask(String processInstanceId, String taskId, String comment) {
		TaskService taskService = this.getProcessEngine().getTaskService();
		List<Comment> commentList = taskService.getTaskComments(taskId);
		if (CollectionUtils.isNotEmpty(commentList)){
			actionProcdefMapper.updateCommentByPrimaryKey(commentList.get(commentList.size() - 1).getId(),comment);
		} else {
			CommentEntity commentEntity = new CommentEntity();
			commentEntity.setId(taskId + "-1");
			commentEntity.setTaskId(taskId);
			commentEntity.setProcessInstanceId(processInstanceId);
			commentEntity.setType(CommentEntity.TYPE_COMMENT);
			commentEntity.setTime(new Date());
			commentEntity.setAction(Event.ACTION_ADD_COMMENT);
			commentEntity.setUserId(null);
			commentEntity.setMessage(comment);
			commentEntity.setFullMessage(comment);
			actionProcdefMapper.insertComment(commentEntity);
		}
	}


	@Override
	public List<HistoricActivityInstance> getHistoricActivityInstanceListByBusinessKey(String businessKey) {
		List<HistoricActivityInstance> historicActivityInstanceList = new ArrayList<>();
		HistoryService historyService = this.getProcessEngine().getHistoryService(); // 任务相关service
		List<HistoricProcessInstance> historicProcessInstanceList = historyService.createHistoricProcessInstanceQuery()
				.processInstanceBusinessKey(businessKey).orderByProcessInstanceStartTime().asc().list();
		for (HistoricProcessInstance instance : historicProcessInstanceList){
			historicActivityInstanceList.addAll(historyService.createHistoricActivityInstanceQuery()
					.processInstanceId(instance.getId()).orderByHistoricActivityInstanceStartTime().asc().list());
		}

		return historicActivityInstanceList;
	}

	private Map<String, Object> getLeastByUser(ProcessEngine processEngine, String businessKey, User user) {
		Map<String, Object> map = new HashMap<>();
		// 获取订单审核信息
		TaskService taskService = processEngine.getTaskService(); // 任务相关service
		// 获取当前活动节点
		Task taskInfo =null;

		try {
			taskInfo=taskService.createTaskQuery().processInstanceBusinessKey(businessKey)
					.singleResult();
		} catch (Exception e) {
			logger.error("taskInfo",e);
			List<Task> taskInfoList=taskService.createTaskQuery().processInstanceBusinessKey(businessKey)
					.list();
			if (CollectionUtils.isNotEmpty(taskInfoList)){
				taskInfo=taskInfoList.get(0);
			}
		}

		map.put("taskInfo", taskInfo);
		List<HistoricProcessInstance> historicProcessInstance = new ArrayList<>();
		List<HistoricActivityInstance> historicActivityInstance = new ArrayList<>();
		Map<String, Object> commentMap = new HashMap<String, Object>();
		Map<String, Object> candidateUserMap = new HashMap<String, Object>();
		candidateUserMap.put("belong", false);
		String startUser = null;
		String processInstanceId = null;
		String endStatus = null;
		// 获取该订单所有审核流程历史纪录
		HistoryService historyService = processEngine.getHistoryService(); // 任务相关service
		historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
				.processInstanceBusinessKey(businessKey).orderByProcessInstanceStartTime().asc().list();
		// 如果不为空，获取最近的审核实例
		if (null != historicProcessInstance && !historicProcessInstance.isEmpty()) {
			HistoricProcessInstance hi = historicProcessInstance.get(historicProcessInstance.size() - 1);
				List<HistoricActivityInstance> hia = historyService.createHistoricActivityInstanceQuery()
						.processInstanceId(hi.getId()).orderByHistoricActivityInstanceStartTime().asc().list();
				processInstanceId = hi.getId();

				//根据流程ID去查询对应的节点备注信息
				List<Comment> commentList = taskService.getProcessInstanceComments(processInstanceId);
				//获取候选人员信息表
				for (Comment c : commentList) {
					// 备注信息塞入
					commentMap.put(c.getTaskId(), c.getFullMessage());
				}

				//since ERP_LV_2020_69 审核历史记录增加伪数据--审核供应供应商时
				if(StringUtils.isNotEmpty(businessKey)&&businessKey.startsWith("traderSupplierVerify_")) {
					if (hia!=null&&hia.size()>0) {
						for (HistoricActivityInstance currentHistoricalRecord : hia) {
							if(StringUtil.isNotEmpty(currentHistoricalRecord.getAssignee())){
								if(!(currentHistoricalRecord instanceof HistoricActivityInstanceEntity)){
									continue;
								}

								StringJoiner assigneeNameJoiner=new StringJoiner(ErpConst.Symbol.COMMA);
								Iterable<String> assigneeNames = Splitter.on(ErpConst.Symbol.COMMA).trimResults().split(currentHistoricalRecord.getAssignee());
								assigneeNames.forEach(assigneeName->{
									if (!assigneeName.contains("(")) {
										User userQuery = userService.getByUsername(assigneeName, 1);
										if(userQuery!=null&&StringUtil.isNotEmpty(userQuery.getRealName())){
											assigneeNameJoiner.add(userQuery.getUsername()+"("+userQuery.getRealName()+")");
										}
									} else {
										assigneeNameJoiner.add(assigneeName);
									}
								});

								((HistoricActivityInstanceEntity)currentHistoricalRecord).setAssignee(assigneeNameJoiner.toString());
							}
						}
					}

					if(hia!=null && hia.size()>1){
						HistoricActivityInstance activityAuditRecord = hia.stream().filter(record -> record != null && Objects.equals(record.getActivityName(), "审核完成")).findFirst().orElse(null);
						if(activityAuditRecord!=null){
							HistoricActivityInstance lastAuditRecord = hia.stream().filter(record -> record != null && Objects.equals(record.getActivityName(), "供应管理组助理审核")).findFirst().orElse(null);
							if(lastAuditRecord!=null){
								//应付药监局检查伪造数据
								HistoricActivityInstanceEntity tempRecordToCheck = new HistoricActivityInstanceEntity();
								tempRecordToCheck.setAssignee("Jessie(罗璇)");
								tempRecordToCheck.setActivityName("质量管理部审核");

								if( lastAuditRecord.getEndTime()!=null){
									Date targetTime = new DateTime(lastAuditRecord.getEndTime()).plusMinutes(5).toDate();
									tempRecordToCheck.setEndTime(targetTime);
									if(activityAuditRecord instanceof HistoricActivityInstanceEntity){
										((HistoricActivityInstanceEntity )activityAuditRecord).setEndTime(targetTime);
									}
								}

								LinkedList<HistoricActivityInstance> eachRecordList =new LinkedList<>(Arrays.asList(hia.toArray(new HistoricActivityInstance[0])));
								eachRecordList.add(hia.size()-1, tempRecordToCheck);
								hia = eachRecordList;
							}
						}
					}

				}
			historicActivityInstance.addAll(hia);
			endStatus = historicActivityInstance.get(historicActivityInstance.size() - 1).getActivityName();

		}
		if (null != processInstanceId) {
			startUser = processEngine.getHistoryService().createHistoricProcessInstanceQuery()
					.processInstanceId(processInstanceId).singleResult().getStartUserId();

		}
		try
		{
			if(taskInfo != null) {
				//获取当前审核候选人
				List<IdentityLink> candidateUserList = taskService.getIdentityLinksForTask(taskInfo.getId());
				candidateUserMap.put(taskInfo.getId(), candidateUserList);
				if (candidateUserList != null && candidateUserList.size() > 0) {
					for (IdentityLink identityLink : candidateUserList) {
						if (identityLink.getUserId().equalsIgnoreCase(user.getUsername())) {
							candidateUserMap.put("belong", true);
						}

					}
				}
			}

		}catch (Exception e) {
			//ignor
			logger.error("getleast exception",e);
		}
		map.put("candidateUserMap", candidateUserMap);
		map.put("startUser", startUser);
		// 最后审核状态
		map.put("endStatus", endStatus);
		map.put("historicActivityInstance", historicActivityInstance);
		map.put("commentMap", commentMap);
		return map;
	}

}
