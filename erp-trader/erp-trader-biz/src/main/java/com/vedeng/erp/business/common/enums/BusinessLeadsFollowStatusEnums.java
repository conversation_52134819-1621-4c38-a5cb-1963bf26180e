package com.vedeng.erp.business.common.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 线索跟进枚举
 * @date 2022/7/25 0:58
 */
public enum BusinessLeadsFollowStatusEnums {


    /**
     * 线索跟进状态枚举
     */
    NO_FENGPEI(0, "待分配"),

    NO_PROCESS(1, "待跟进"),

    FOLLOWING(2, "跟进中"),

    CLOSE(3, "已关闭"),

    OPPORTUNITY(4, "已商机");


    private final Integer type;

    private final String name;

    BusinessLeadsFollowStatusEnums(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }
}
