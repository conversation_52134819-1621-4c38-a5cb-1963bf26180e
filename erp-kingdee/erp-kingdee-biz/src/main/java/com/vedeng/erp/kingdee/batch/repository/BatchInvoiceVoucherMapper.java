package com.vedeng.erp.kingdee.batch.repository;
import java.util.Collection;
import org.apache.ibatis.annotations.Param;
import java.util.List;

import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceVoucherDto;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2022/12/5 12:20
 **/
public interface BatchInvoiceVoucherMapper {
    int deleteByPrimaryKey(Long invoiceVoucherId);

    int insert(BatchInvoiceVoucherDto record);

    int insertSelective(BatchInvoiceVoucherDto record);

    BatchInvoiceVoucherDto selectByPrimaryKey(Long invoiceVoucherId);

    int updateByPrimaryKeySelective(BatchInvoiceVoucherDto record);

    int updateByPrimaryKey(BatchInvoiceVoucherDto record);

    List<BatchInvoiceVoucherDto> findByInvoiceIdIn(@Param("invoiceIdCollection")Collection<Integer> invoiceIdCollection);

    int batchInsert(List<BatchInvoiceVoucherDto> list);

    List<BatchInvoiceVoucherDto> selectByInvoiceId(@Param("invoiceId")String invoiceId);



}