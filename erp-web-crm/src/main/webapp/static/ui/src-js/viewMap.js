Vue.component('ui-view-map', {
    template: `<div class="ui-view-map-wrap">
        <div class="ui-view-map-btn" @click="show">
            <i class="vd-ui_icon icon-address2"></i>
            <span v-if="!notxt">查看地图</span>
        </div>
        <ui-dialog
            :visible.sync="isShow"
            title="查看地图"
            width="960px"
            class="ui-view-map-dialog"
        >
            <iframe class="ui-view-map-iframe" :src="url" frameborder="0" v-if="isShow"></iframe>
        </ui-dialog>
    <div>`,
    props: {
        address: {
            type: String,
            default: ''
        },
        notxt: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            isShow: false,
            url: ''
        }
    },
    methods: {
        show() {
            this.isShow = true;
            this.url = 'https://www.amap.com/search?query=' + encodeURI(this.address);
        },
        hide() {
            this.isShow = false;
        }
    }
})

Vue.component('ui-location-map', {
    template: `<div class="ui-view-map-wrap">
        <div class="ui-view-map-btn" @click="show">
            <i class="vd-ui_icon icon-address2"></i>
            <span v-if="!notxt">查看位置</span>
        </div>
        <ui-dialog
            :visible.sync="isShow"
            title="打卡信息"
            width="960px"
            class="ui-view-map-dialog"
        >
            <div class="ui-location-map-wrap" v-if="isShow" id="uiLocationMapWrap"></div>
        </ui-dialog>
    <div>`,
    props: {
        location: {
            type: String,
            default: ''
        },
        notxt: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            isShow: false,
            url: '',
            position: [],
        }
    },
    created() {
        if (this.location) {
            this.position = this.location.split(',');
        }

        if(!window.gd_map_key && !window.gd_map_loading) {
            this.getMapKey();
        }
    },
    methods: {
        getMapKey() {
            window.gd_map_loading = true;
            this.$axios.get('/crm/visitrecord/profile/config').then(({data}) => {
                if(data.success) {
                    let keyCode = data.data.split(',');
                    window.gd_map_key = keyCode[0];
                    window.gd_map_code = keyCode[1];
                }

                window.gd_map_loading = false;
            })
        },
        initMap() {
            if(window.gd_map_loader) {
                this.setlocation();
            } else {
                // var mapEl = document.createElement("script");
                // mapEl.src = '/static/js/common/map.js'
                // document.body.appendChild(mapEl);

                window._AMapSecurityConfig = {
                    securityJsCode: window.gd_map_code,
                }
                AMapLoader.load({
                    "key": window.gd_map_key,              // 申请好的Web端开发者Key，首次调用 load 时必填
                    "version": "2.0",   // 指定要加载的 JS API 的版本，缺省时默认为 1.4.15
                    // "plugins": ['AMap.PlaceSearch'],           // 需要使用的的插件列表，如比例尺'AMap.Scale'等
                    "AMapUI": {             // 是否加载 AMapUI，缺省不加载
                        "version": '1.1',   // AMapUI 版本
                        "plugins": ['overlay/SimpleMarker'],       // 需要加载的 AMapUI ui插件
                    },
                    "Loca": {                // 是否加载 Loca， 缺省不加载
                        "version": '2.0'  // Loca 版本
                    },
                }).then((AMap) => {
                    window.gd_map_loader = AMap;
                    this.setlocation();
                }).catch((e) => {
                    console.error(e);  //加载错误提示
                });
            }
        },
        setlocation() {
            let map = new gd_map_loader.Map('uiLocationMapWrap');
            map.setZoomAndCenter(16, this.position, true)
            new gd_map_loader.Marker({ map: map, position: this.position });
        },
        show() {
            if(!this.location) {
                this.$message.warn('历史打卡数据，无法地图查看');
                return;
            }
            this.isShow = true;
            this.$nextTick(() => {
                this.initMap();
            })
        },
        hide() {
            this.isShow = false;
        }
    }
})