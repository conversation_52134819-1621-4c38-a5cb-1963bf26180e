package com.vedeng.temporal.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 公司执行顺序信息DTO
 * 基于T_FLOW_NODE关联查询的结果对象
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CompanySequenceInfo {
    
    /**
     * 公司ID（T_BASE_COMPANY_INFO.ID）
     */
    private Integer companyId;
    
    /**
     * 公司名称
     */
    private String companyName;
    
    /**
     * 公司简称
     */
    private String companyShortName;
    
    /**
     * 金蝶账套编码
     */
    private String kingdeeAccountCode;
    
    /**
     * 节点级别（执行顺序）
     */
    private Integer nodeLevel;
    
    /**
     * 交易者ID（T_TRADER.TRADER_ID）
     */
    private Integer traderId;
    
    /**
     * 交易者名称
     */
    private String traderName;
    
    /**
     * 流转节点ID
     */
    private Long flowNodeId;

    /**
     * 获取公司代码（直接返回公司简称）
     */
    public String getCompanyCode() {
        return companyShortName;
    }

    /**
     * 获取显示名称（优先使用公司名称，其次交易者名称）
     */
    public String getDisplayName() {
        if (companyName != null && !companyName.trim().isEmpty()) {
            return companyName;
        }
        return traderName != null ? traderName : "未知公司";
    }
}
