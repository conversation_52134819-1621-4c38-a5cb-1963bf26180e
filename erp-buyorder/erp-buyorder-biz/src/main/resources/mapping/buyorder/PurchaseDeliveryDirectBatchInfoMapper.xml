<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.buyorder.mapper.PurchaseDeliveryDirectBatchInfoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.buyorder.domain.entity.PurchaseDeliveryDirectBatchInfo">
    <id column="PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID" jdbcType="INTEGER" property="purchaseDeliveryDirectBatchInfoId" />
    <result column="BUYORDER_ID" jdbcType="INTEGER" property="buyorderId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
  </resultMap>
  <resultMap id="ResultVoMap" type="com.vedeng.erp.buyorder.domain.vo.PurchaseDeliveryDirectBatchInfoVo" extends="BaseResultMap">
    <collection property="list" javaType="java.util.List" ofType="com.vedeng.erp.buyorder.domain.vo.PurchaseDeliveryDirectBatchDetailVo" >
      <result column="PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL_ID" property="purchaseDeliveryDirectBatchDetailId"/>
      <result column="PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID" property="purchaseDeliveryDirectBatchInfoId"/>
      <result column="EXPRESS_DETAIL_ID" property="expressDetailId"/>
      <result column="SKU" property="sku"/>
      <result column="SKU_NAME" property="skuName"/>
      <result column="MODEL" property="model"/>
      <result column="UNIT" property="unit"/>
      <result column="PRODUCT_COMPANY" property="productCompany"/>
      <result column="PRODUCTION_LICENCE" property="productionLicence"/>
      <result column="REGISTER_NUMBER" property="registerNumber"/>
      <result column="BATCH_NUMBER" property="batchNumber"/>
      <result column="UNION_SEQUENCE" property="unionSequence"/>
      <result column="ARRIVAL_COUNT" property="arrivalCount"/>
      <result column="WMS_HANDLED_ARRIVAL_COUNT" property="wmsHandledArrivalCount"/>
      <result column="WMS_HANDLED_DELIVERY_COUNT" property="wmsHandledDeliveryCount"/>
      <result column="MANUFACTURE_DATE_TIME" property="manufactureDateTime"/>
      <result column="INVALID_DATE_TIME" property="invalidDateTime"/>
      <result column="ADD_TIME" property="addTime"/>
      <result column="CREATOR" property="creator"/>
      <result column="MOD_TIME" property="modTime"/>
      <result column="UPDATER" property="updater"/>
      <result column="creatorName" property="creatorName"/>
    </collection>
  </resultMap>
  <resultMap id="ResultDtoMap" type="com.vedeng.erp.buyorder.dto.PurchaseDeliveryBatchInfoDto" extends="BaseResultMap">
    <collection property="list" javaType="java.util.List" ofType="com.vedeng.erp.buyorder.dto.PurchaseDeliveryBatchDetailDto" >
      <result column="PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL_ID" property="purchaseDeliveryDirectBatchDetailId"/>
      <result column="PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID" property="purchaseDeliveryDirectBatchInfoId"/>
      <result column="EXPRESS_DETAIL_ID" property="expressDetailId"/>
      <result column="SKU" property="sku"/>
      <result column="SKU_NAME" property="skuName"/>
      <result column="MODEL" property="model"/>
      <result column="UNIT" property="unit"/>
      <result column="PRODUCT_COMPANY" property="productCompany"/>
      <result column="PRODUCTION_LICENCE" property="productionLicence"/>
      <result column="REGISTER_NUMBER" property="registerNumber"/>
      <result column="BATCH_NUMBER" property="batchNumber"/>
      <result column="UNION_SEQUENCE" property="unionSequence"/>
      <result column="ARRIVAL_COUNT" property="arrivalCount"/>
      <result column="WMS_HANDLED_ARRIVAL_COUNT" property="wmsHandledArrivalCount"/>
      <result column="WMS_HANDLED_DELIVERY_COUNT" property="wmsHandledDeliveryCount"/>
      <result column="MANUFACTURE_DATE_TIME" property="manufactureDateTime"/>
      <result column="INVALID_DATE_TIME" property="invalidDateTime"/>
      <result column="ADD_TIME" property="addTime"/>
      <result column="CREATOR" property="creator"/>
      <result column="MOD_TIME" property="modTime"/>
      <result column="UPDATER" property="updater"/>
      <result column="creatorName" property="creatorName"/>
    </collection>
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID, BUYORDER_ID, ADD_TIME, CREATOR, MOD_TIME, 
    UPDATER
  </sql>
  <select id="selectByExample" parameterType="com.vedeng.erp.buyorder.domain.entity.TPurchaseDeliveryDirectBatchInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_PURCHASE_DELIVERY_DIRECT_BATCH_INFO
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_PURCHASE_DELIVERY_DIRECT_BATCH_INFO
    where PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID = #{purchaseDeliveryDirectBatchInfoId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_PURCHASE_DELIVERY_DIRECT_BATCH_INFO
    where PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID = #{purchaseDeliveryDirectBatchInfoId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.erp.buyorder.domain.entity.TPurchaseDeliveryDirectBatchInfoExample">
    delete from T_PURCHASE_DELIVERY_DIRECT_BATCH_INFO
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID" keyProperty="purchaseDeliveryDirectBatchInfoId" parameterType="com.vedeng.erp.buyorder.domain.entity.PurchaseDeliveryDirectBatchInfo" useGeneratedKeys="true">
    insert into T_PURCHASE_DELIVERY_DIRECT_BATCH_INFO (BUYORDER_ID, ADD_TIME, CREATOR, 
      MOD_TIME, UPDATER)
    values (#{buyorderId,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{modTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID" keyProperty="purchaseDeliveryDirectBatchInfoId" parameterType="com.vedeng.erp.buyorder.domain.entity.PurchaseDeliveryDirectBatchInfo" useGeneratedKeys="true">
    insert into T_PURCHASE_DELIVERY_DIRECT_BATCH_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="buyorderId != null">
        BUYORDER_ID,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="buyorderId != null">
        #{buyorderId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.erp.buyorder.domain.entity.TPurchaseDeliveryDirectBatchInfoExample" resultType="java.lang.Long">
    select count(*) from T_PURCHASE_DELIVERY_DIRECT_BATCH_INFO
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update T_PURCHASE_DELIVERY_DIRECT_BATCH_INFO
    <set>
      <if test="record.purchaseDeliveryDirectBatchInfoId != null">
        PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID = #{record.purchaseDeliveryDirectBatchInfoId,jdbcType=INTEGER},
      </if>
      <if test="record.buyorderId != null">
        BUYORDER_ID = #{record.buyorderId,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creator != null">
        CREATOR = #{record.creator,jdbcType=INTEGER},
      </if>
      <if test="record.modTime != null">
        MOD_TIME = #{record.modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updater != null">
        UPDATER = #{record.updater,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update T_PURCHASE_DELIVERY_DIRECT_BATCH_INFO
    set PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID = #{record.purchaseDeliveryDirectBatchInfoId,jdbcType=INTEGER},
      BUYORDER_ID = #{record.buyorderId,jdbcType=INTEGER},
      ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      CREATOR = #{record.creator,jdbcType=INTEGER},
      MOD_TIME = #{record.modTime,jdbcType=TIMESTAMP},
      UPDATER = #{record.updater,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.buyorder.domain.entity.PurchaseDeliveryDirectBatchInfo">
    update T_PURCHASE_DELIVERY_DIRECT_BATCH_INFO
    <set>
      <if test="buyorderId != null">
        BUYORDER_ID = #{buyorderId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID = #{purchaseDeliveryDirectBatchInfoId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.buyorder.domain.entity.PurchaseDeliveryDirectBatchInfo">
    update T_PURCHASE_DELIVERY_DIRECT_BATCH_INFO
    set BUYORDER_ID = #{buyorderId,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER}
    where PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID = #{purchaseDeliveryDirectBatchInfoId,jdbcType=INTEGER}
  </update>

  <select id="queryPurchaseDeliveryInfoByBuyorderId" resultMap="ResultDtoMap">
    SELECT A.*,B.*,C.USERNAME AS creatorName
    FROM T_PURCHASE_DELIVERY_DIRECT_BATCH_INFO A
    LEFT JOIN T_PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL B ON A.PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID = B.PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID
    LEFT JOIN T_USER C ON B.CREATOR = C.USER_ID
    WHERE BUYORDER_ID = #{buyorderId,jdbcType=INTEGER}
  </select>
</mapper>