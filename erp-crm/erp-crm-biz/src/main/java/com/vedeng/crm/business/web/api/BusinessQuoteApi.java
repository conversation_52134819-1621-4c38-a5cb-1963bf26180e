package com.vedeng.crm.business.web.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.core.annotation.MenuDesc;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.annotation.NoRepeatSubmit;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.utils.validator.ValidatorUtils;
import com.vedeng.common.core.utils.validator.group.DefaultGroup;
import com.vedeng.crm.business.quote.domain.dto.*;
import com.vedeng.crm.business.quote.facade.QuoteFacade;
import com.vedeng.crm.business.quote.service.CrmQuoteOrderService;
import com.vedeng.crm.feign.brand.BrandSearchApiService;
import com.vedeng.crm.feign.goods.GoodsSelectSearchApiService;
import com.vedeng.crm.feign.goods.dto.GoodsSearchReqDto;
import com.vedeng.crm.task.service.TaskService;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.goods.dto.SkuSceneDto;
import com.vedeng.goods.service.GoodsApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 协同报价
 */
@ExceptionController
@RestController
@RequestMapping("/crm/quote/profile")
@Slf4j
public class BusinessQuoteApi {

    @Value("${confirm_order_url}")
    private String confirmOrderUrl;

    @Autowired
    private QuoteFacade quoteFacade;

    @Autowired
    private CrmQuoteOrderService crmQuoteOrderService;


    @Autowired
    private GoodsSelectSearchApiService goodsSelectSearchApiService;

    @Autowired
    private GoodsApiService goodsApiService;

    @Autowired
    private TaskService taskService;

    /**
     * 1、根据quoteorderId查询报价单详情
     * @param quoteorderId
     * @return
     */
    @ResponseBody
    @RequestMapping("/queryQuoteDetail")
    @NoNeedAccessAuthorization
    public R<QuoteSyncInfoDto> queryQuoteDetail(@RequestParam Integer quoteorderId) {
        QuoteSyncInfoDto quoteSyncInfoDto = quoteFacade.queryQuoteDetail(quoteorderId);
//        quoteSyncInfoDto.getQuoteConfigDto().setSyncLocal(60);
//        quoteSyncInfoDto.getQuoteConfigDto().setSyncLocal(60);
        return R.success(quoteSyncInfoDto);
    }


    /**
     * 23、分享报价单
     * @param quoteorderId  213771
     * @return
     *  {"code":0,"message":"成功","success":true,"time":"2024-09-29 09:38:45","data":{"quoteorderNo":"VD240924241712332","traderName":"苏州大学","traderContactName":"111","mobile":"12345678999","quoteMoney":138.00,"goodsCount":5,"totalNum":8,"freightDescriptionName":null,"shardUrl":"https://wxtest.vedeng.com/bj-VD240924241712332.html","now":"2024-09-29","end":"2024-10-12","day":14,"onlineShareTime":"2024-09-26 17:21:00"}}
     */
    @ResponseBody
    @RequestMapping("/shareQuoteDetail")
    @NoNeedAccessAuthorization
    public R<CrmQuoteShareDto> shareQuoteDetail(@RequestParam Integer quoteorderId) {
        Integer unSku = quoteFacade.getQuoteWithNoSkuInfoById(quoteorderId);
        if(unSku > 0 ){
            return R.error("“分享线上报价”时需把手动添加产品换成ERP已建档产品");
        }
        String skuCheckStatusStr = quoteFacade.checkQuoteSkuCheckStatus(quoteorderId);
        if(StringUtils.isNotBlank(skuCheckStatusStr)){
            return R.error(skuCheckStatusStr+"未审核通过，无法分享线上报价");
        }
        CrmQuoteShareDto crmQuoteShareDto = quoteFacade.getQuoteShardInfoById(quoteorderId);
        if(crmQuoteShareDto == null) {
            return R.error("请输入正确的报价单号");
        }
        DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime end = now.plusDays(13);
        crmQuoteShareDto.setDay(14);//有效期天数
        crmQuoteShareDto.setNow(now.format(format));
        crmQuoteShareDto.setEnd(end.format(format));
        crmQuoteShareDto.setShardUrl(confirmOrderUrl+"bj-"+crmQuoteShareDto.getQuoteorderNo()+".html");
        //${confirmOrderUrl}bj-${quoteordervo.quoteorderNo}.html
        return R.success(crmQuoteShareDto);
    }

    /**
     * 商品选型-搜索商品
     * @param goodsSearchReq
     * @return
     */
    @ResponseBody
    @RequestMapping("/searchGoodsInfo")
    @NoNeedAccessAuthorization
    public R<Object> searchGoodsInfo(GoodsSearchReqDto goodsSearchReq) {
        try{
            return goodsSelectSearchApiService.searchForApi(goodsSearchReq);
        }catch (Exception e){
            log.error(e.getMessage());
            return R.error(e.getMessage());
        }
    }

    /**
     * 获取场景和场景分类级联
     */
    @ResponseBody
    @NoNeedAccessAuthorization
    @RequestMapping("/getSceneAndCategory")
    public R<List<SkuSceneDto>> getSceneAndCategory() {
        List<SkuSceneDto> sceneAndCategory = goodsApiService.getSceneAndCategory();
        return R.success(sceneAndCategory);
    }

    /**
     * 2.1、根据quoteorderId授权申请书信息
     * @param quoteorderId
     * 成功示例：http://qa.lxcrm.ivedeng.com/crm/quote/profile/queryQuoteApply?quoteorderId=210927
     * {
     *     "code": 0,
     *     "message": "成功",
     *     "success": true,
     *     "time": "2024-08-06 13:23:31",
     *     "data": {
     *         "quoteorderId": null,
     *         "flag": 1,
     *         "applyName": "申请授权书",
     *         "sqnum": 1,
     *         "quoteApplyLink": "http://localhost:8080//index.do?target=%2Forder%2Fquote%2Fapply.do%3FquoteorderId%3D210927&title=%E6%8E%88%E6%9D%83%E4%B9%A6%E7%94%B3%E8%AF%B7",
     *         "quoteApplyInnerLink": "/order/quote/apply.do?quoteorderId=210927"
     *     }
     * }
     *
     * 失败示例：http://qa.lxcrm.ivedeng.com/crm/quote/profile/queryQuoteApply?quoteorderId=1
     * {
     *     "code": 500000,
     *     "message": "此报价单中不含公司产品库中产品，需要添加后才可申请授权书！",
     *     "success": false,
     *     "time": "2024-08-06 13:27:11",
     *     "data": null
     * }
     *
     *
     *
     * @return
     */
    @ResponseBody
    @RequestMapping("/queryQuoteApply")
    @NoRepeatSubmit
    @MenuDesc(menuValue = "C0214",menuDesc = "授权申请书")
    public R<QuoteApplyDto> queryQuoteApply(@RequestParam Integer quoteorderId) {
        List<CrmQuoteOrderCoreSkuDto> quoteOrderGoods = quoteFacade.findQuoteOrderGoods(quoteorderId);
        QuoteApplyDto quoteApplyDto =  quoteFacade.queryQuoteApply(quoteorderId,quoteOrderGoods);
        if(quoteApplyDto.getSqnum() < 1){
            return R.error("此报价单中不含公司产品库中产品，需要添加后才可申请授权书！");
        }
        return R.success(quoteApplyDto);
    }

    /**
     * 2.2、校验授权申请书
     * @param quoteorderId
     * @return
     */
    @ResponseBody
    @RequestMapping("/checkQuoteApply")
    @NoRepeatSubmit
    @MenuDesc(menuValue = "C0214",menuDesc = "校验授权申请书")
    public R<CheckAuthorityRepDto> checkQuoteApply(@RequestParam Integer quoteorderId) {
        CheckAuthorityRepDto quoteApplyDto =  quoteFacade.checkQuoteApply(quoteorderId);
        return R.success(quoteApplyDto);
    }


    /**
     * 3、根据skuNo查询商品选型信息
     * @param requestDto
     * @return
     */
    @RequestMapping(value = "/queryBySkuNo",method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<CrmCoreSkuInfoDto> queryInfoBySkuNo(@RequestBody QuerySkuRequestDto requestDto) {
        log.info("根据skuNo查询商品选型信息:{}", JSON.toJSON(requestDto));
        ValidatorUtils.validate(requestDto, DefaultGroup.class);
        CrmCoreSkuInfoDto skuInfoDto = quoteFacade.queryInfoBySkuNo(requestDto);
        return R.success(skuInfoDto);
    }

    /**
     * 4、分配供应链人员下拉
     * @return
     */
    @ResponseBody
    @RequestMapping("/querySupplierUser")
    @MenuDesc(menuValue = "C0219",menuDesc = "选择产品负责人")
    public R<List<CrmUserDto>> querySupplierUser() {
        List<CrmUserDto> userDtos = quoteFacade.querySupplierUser();
        return R.success(userDtos);
    }

    

    /**
     * 群聊审批
     * @param quoteorderId 报价ID
     * @param approverStatus 审批状态【0：驳回；1：审批通过；2：提交审批】
     * @return
     */
    @ResponseBody
    @RequestMapping("/approverAppChat")
    @NoNeedAccessAuthorization
    public R<QuoteCreateAppChat> approverAppChat(@RequestBody ApproverAppChatDto approverAppChatDto) {
        try {
        	log.info("群聊审批,入参：{}",JSON.toJSONString(approverAppChatDto));
        	if(Objects.isNull(approverAppChatDto)) {
        		return R.error("网络错误，请稍后重试...");
        	}
        	if(Objects.isNull(approverAppChatDto.getApproverStatus()) || Objects.isNull(approverAppChatDto.getQuoteorderId()))  {
        		return R.error("网络错误，请稍后重试...");
        	}
        	
        	//查询群状态
    		CrmQuoteOrderDto crmQuoteOrderDto = crmQuoteOrderService.selectOne(approverAppChatDto.getQuoteorderId());
    		Integer isBuildChat = crmQuoteOrderDto.getIsBuildChat();	
    		if(isBuildChat == approverAppChatDto.getApproverStatus()) {
    			return R.error("操作已执行，请勿重复操作...");
    		}
    		//群聊已创建，不可驳回
    		if(approverAppChatDto.getApproverStatus() == 0 && isBuildChat == 1) {
    			return R.error("群聊已创建，不可驳回...");
    		}
    		//群聊已驳回，不可审核通过
    		if(approverAppChatDto.getApproverStatus() == 1 && isBuildChat == 0) {
    			throw new Exception("群聊已驳回，不可审核通过...");
    		}
        	
        	CurrentUser currentUser = CurrentUser.getCurrentUser();
        	QuoteCreateAppChat appChat = quoteFacade.approverAppChat(approverAppChatDto,currentUser);
        	return R.success(appChat);
        }catch(Exception e) {
        	log.error("群聊审批失败",e);
        	if(approverAppChatDto.getApproverStatus() == 1) {
        		return R.error("群聊创建失败...");
        	}else {
        		return R.error("网络错误，请稍后重试...");
        	}
        }
    }
    
    /**
     * 5、根据quoteorderId查询拉群成员
     * @param quoteorderId
     * @return
     */
    @ResponseBody
    @RequestMapping("/queryUserByQuoteorderId")
    @NoNeedAccessAuthorization
    public R<AppChatDto> queryUserByQuoteorderId(@RequestParam Integer quoteorderId) {
    	AppChatDto appChatDto = new AppChatDto();
        List<AppChatUserDto> list = quoteFacade.queryUserByQuoteorderId(quoteorderId);
        QuoteSyncInfoDto quoteSyncInfoDto = quoteFacade.queryQuoteDetail(quoteorderId);
        appChatDto.setUserList(list);        
        UserDto userDto = quoteFacade.queryChargeInfo(quoteorderId);
        if(Objects.nonNull(userDto)) {
        	Integer userId = userDto.getUserId();
        	appChatDto.setChargeId(userId);
        	String userName = userDto.getDisplayName();
        	appChatDto.setChargeName(userName);
        	String aliasHeadPicture = userDto.getAliasHeadPicture();
        	appChatDto.setChargeNameHeadPicture(aliasHeadPicture);
        }
        if(Objects.nonNull(quoteSyncInfoDto) && Objects.nonNull(quoteSyncInfoDto.getCrmQuoteOrderDto())) {
        	CrmQuoteOrderDto crmQuoteOrderDto = quoteSyncInfoDto.getCrmQuoteOrderDto();
        	appChatDto.setIsBuildChat(crmQuoteOrderDto.getIsBuildChat());
        	
        	String buildChatUserIds = crmQuoteOrderDto.getBuildChatUserIds();
        	if(StringUtils.isNotEmpty(buildChatUserIds)) {
        		List<Integer> buildChatUserIdArray = Arrays.stream(buildChatUserIds.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        		appChatDto.setUserIdList(buildChatUserIdArray);
        	}else {
        		appChatDto.setUserIdList(new ArrayList<>());
        	}
        }
        return R.success(appChatDto);
    }

    /**
     * 6、创建群聊
     * @param createAppChatDto
     * @return
     */
    @RequestMapping(value = "/createAppChat", method = RequestMethod.POST)
    @NoRepeatSubmit
    @MenuDesc(menuValue = "C0215",menuDesc = "建群协同")
    public R<QuoteCreateAppChat> create(@RequestBody CreateAppChatDto createAppChatDto) {
        log.info("创建群聊:{}", JSON.toJSON(createAppChatDto));
        ValidatorUtils.validate(createAppChatDto, DefaultGroup.class);
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        log.info("当前操作人:{}", JSON.toJSON(currentUser));

        QuoteCreateAppChat appChat = quoteFacade.createAppChat(createAppChatDto, currentUser);
        return R.success(appChat);
    }

    /**
     * 7、群聊发送信息
     * @param businessChanceId
     * @return
     */
    @ResponseBody
    @RequestMapping("/sendAppChat")
    @NoNeedAccessAuthorization
    public R<QuoteCreateAppChat> sendAppChat(@RequestParam Integer businessChanceId) {
        quoteFacade.sendAppChat(businessChanceId);
        return R.success();
    }

    /**
     * 8、导入客户需求数据
     * @param file
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/import", method = RequestMethod.POST)
    @ResponseBody
    @NoRepeatSubmit
    @MenuDesc(menuValue = "C0211",menuDesc = "导入客户需求")
    public R<?> importExcel(@RequestPart("file") MultipartFile file,@RequestParam Integer quoteorderId) throws Exception {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        log.info("当前操作人:{}", JSON.toJSON(currentUser));
        String result = quoteFacade.importNeedsExcelFile(file, quoteorderId, currentUser);
        return R.success(result);
    }

    /**
     * 9、批量添加商品
     * （普通模式）
     * @param batchAddQuoteGoods
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/batchAddGoods", method = RequestMethod.POST)
    @NoRepeatSubmit
    @MenuDesc(menuValue = "C0210",menuDesc = "批量/添加产品")
    public R<String> batchAddQuoteGoods(@RequestBody BatchAddQuoteGoods batchAddQuoteGoods) throws Exception {
        log.info("批量添加商品:{}", JSON.toJSON(batchAddQuoteGoods));
        ValidatorUtils.validate(batchAddQuoteGoods, DefaultGroup.class);

        CurrentUser currentUser = CurrentUser.getCurrentUser();
        log.info("当前操作人:{}", JSON.toJSON(currentUser));
        String result = quoteFacade.batchAddQuoteGoods(batchAddQuoteGoods,currentUser);
        return R.success(result);
    }

    @Autowired
    private BrandSearchApiService brandSearchApiService;

    /**
     * 9.1、搜索品牌
     * （普通模式）
     * @param brandName
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/searchBrand")
    @MenuDesc(menuValue = "C0210",menuDesc = "添加自定义商品/批量/添加产品")
    public R<Object> searchBrand(@RequestParam(required = false) String brandName) throws Exception {
        log.info("添加自定义商品:{}", JSON.toJSON(brandName));
        return brandSearchApiService.searchForApi(brandName);
    }

    /**
     * 9.2、手工添加自定义商品
     * （普通模式）
     * @param crmQuoteOrderGoodsNoSkuDto
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/addQuoteGoodsNoSku", method = RequestMethod.POST)
    @MenuDesc(menuValue = "C0210",menuDesc = "添加自定义商品/批量/添加产品")
    public R<String> addQuoteGoodsNoSku(@RequestBody CrmQuoteOrderGoodsNoSkuDto crmQuoteOrderGoodsNoSkuDto) throws Exception {
        log.info("添加自定义商品:{}", JSON.toJSON(crmQuoteOrderGoodsNoSkuDto));
        ValidatorUtils.validate(crmQuoteOrderGoodsNoSkuDto, DefaultGroup.class);
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        String result = quoteFacade.addQuoteGoodsNoSku(crmQuoteOrderGoodsNoSkuDto,currentUser);
        return R.success(result);
    }

    /**
     * 10、导出数据
     * @param quoteorderId
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/export", method = RequestMethod.GET)
    @ResponseBody
    @NoRepeatSubmit
    @MenuDesc(menuValue = "C0222",menuDesc = "导出报价")
    public void exportExcel(HttpServletResponse response, @RequestParam Integer quoteorderId) throws Exception {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        log.info("当前操作人:{}", JSON.toJSON(currentUser));
        quoteFacade.exportExcel(response,quoteorderId,currentUser);
//        return R.success();
    }


    /**
     * 11、轮询可编辑字段
     * @param quoteorderId
     * @return
     */
    @RequestMapping(value = "/syncEdit",method = RequestMethod.GET)
    @NoNeedAccessAuthorization
    public R<QuoteSyncInfoDto> syncEdit(@RequestParam Integer quoteorderId) {
//        if (true){
//            return R.success();
//        }
        return R.success(quoteFacade.syncEdit(quoteorderId));
    }

    /**
     * 12、位置上报
     * @param quoteSyncReqDto
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/sync", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<QuoteSyncResponseDto> sync(@RequestBody QuoteSyncRequestDto quoteSyncReqDto) throws Exception {
//        if (true){
//            return R.success();
//        }
        log.info("位置上报:{}",quoteSyncReqDto);
        ValidatorUtils.validate(quoteSyncReqDto, DefaultGroup.class);
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        log.info("当前操作人:{}", JSON.toJSON(currentUser));
        QuoteSyncResponseDto sync = quoteFacade.sync(quoteSyncReqDto, currentUser);
        return R.success(sync);
    }

    /**
     * 13、报价单商品新增/修改保存
     * @param requestDto
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/insertOrUpdate", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> insertOrUpdateQuoteGoods(@RequestBody QuoteGoodsRequestDto requestDto) throws Exception {
        log.info("报价单商品insertOrUpdateQuoteGoods:{}",requestDto);
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        log.info("当前操作人:{}", JSON.toJSON(currentUser));
        quoteFacade.insertOrUpdate(requestDto, currentUser);
        return R.success();
    }

    /**
     * 14、-1报价单商品删除
     * @param deleteRequestDto
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/deleteQuoteGoods", method = RequestMethod.POST)
    @NoRepeatSubmit
    @MenuDesc(menuValue = "C0223",menuDesc = "删除报价商品/需求-商品")
    public R<?> deleteQuoteGoods(@RequestBody QuoteGoodsDeleteRequestDto deleteRequestDto) throws Exception {
        log.info("报价单商品删除deleteQuoteGoods:{}",deleteRequestDto);
        ValidatorUtils.validate(deleteRequestDto, DefaultGroup.class);
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        log.info("当前操作人:{}", JSON.toJSON(currentUser));
        quoteFacade.deleteQuoteGoods(deleteRequestDto, currentUser);
        return R.success();
    }

    /**
     * 14、-2报价单商品需求
     * @param deleteRequestDto
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/deleteQuoteNeeds", method = RequestMethod.POST)
    @NoRepeatSubmit
    @MenuDesc(menuValue = "C0223",menuDesc = "删除报价商品/需求-需求")
    public R<?> deleteQuoteNeeds(@RequestBody QuoteNeedsDeleteRequestDto deleteRequestDto) throws Exception {
        log.info("报价单商品删除deleteQuoteGoods:{}",deleteRequestDto);
        ValidatorUtils.validate(deleteRequestDto, DefaultGroup.class);
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        log.info("当前操作人:{}", JSON.toJSON(currentUser));
        quoteFacade.deleteQuoteNeeds(deleteRequestDto, currentUser);
        return R.success();
    }

    /**
     * 15、报价单生效状态更新
     * 需要确认时，code为1
     * @param quoteValidRequestDto
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/updateValid", method = RequestMethod.POST)
    @NoRepeatSubmit
    @MenuDesc(menuValue = "C0212,C0213",menuDesc = "生效报价/撤销生效")
    @ResponseBody
    public R<?> updateValid(@RequestBody QuoteValidRequestDto quoteValidRequestDto) throws Exception {
        log.info("报价单生效状态更新updateValid:{}",quoteValidRequestDto);
        ValidatorUtils.validate(quoteValidRequestDto, DefaultGroup.class);
        if(quoteValidRequestDto.isValid() && quoteValidRequestDto.getConfirmed() !=null && !quoteValidRequestDto.getConfirmed().booleanValue()){
            CrmQuoteOrderDto crmQuoteOrderDto = crmQuoteOrderService.selectOne(quoteValidRequestDto.getQuoteorderId());
            if(crmQuoteOrderDto==null){
                return R.error("出错了，报价单未找到！");
            }
            Integer businessChanceId = crmQuoteOrderDto.getBussinessChanceId();
            Integer taskCount = taskService.queryTaskForBussinessChance(businessChanceId);
            if(taskCount> 0 ){
                return R.confirm("该报价单中供应链还有待办任务未完成，报价生效后将无法操作，是否继续？");
            }
        }

        CurrentUser currentUser = CurrentUser.getCurrentUser();
        log.info("当前操作人:{}", JSON.toJSON(currentUser));
        quoteFacade.validQuote(quoteValidRequestDto, currentUser);
        return R.success();
    }

    /**
     * 16、发起咨询
     * @param consultationReportDto
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/consultationReport", method = RequestMethod.POST)
    @NoRepeatSubmit
    @MenuDesc(menuValue = "C0217",menuDesc = "咨询报备")
    public R<?> consultationReport(@RequestBody ConsultationReportDto consultationReportDto) throws Exception {
        log.info("咨询报备consultationReport:{}",JSON.toJSON(consultationReportDto));
        ValidatorUtils.validate(consultationReportDto, DefaultGroup.class);
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        log.info("当前操作人:{}", JSON.toJSON(currentUser));
        quoteFacade.goConsultationReport(consultationReportDto, currentUser);
        return R.success();
    }


    /**
     * 16.1 根据SKU获取咨询人员信息
     * 示例：
     * 入参：无sku时POST[]即可，即空数组
     *      有sku时POST skunos集合 ["V123456","V123567"]
     * 返回：
     * {
     *     "code": 0,
     *     "message": "成功",
     *     "success": true,
     *     "time": "2024-08-07 12:57:45",
     *     "data": [
     *         {
     *             "userId": 880,
     *             "userName": "Hanne",
     *             "headPic": "https://wework.qpic.cn/wwhead/dx4Y70y9XcvSiazHvUicn0EzDUuiaoTCQbLF7Ciah3AsMD4xOMdictDqp2GK5k411ibVh2tDvCCDvod8Y/100"
     *         }
     *     ]
     * }
     * @param skuNos
     * @return
     */
    @ResponseBody
    @RequestMapping("/queryAskUser")
    @NoNeedAccessAuthorization
    public R<List<CrmUserDto>> queryAskUser(@RequestBody List<String> skuNos) {
        try{
            List<CrmUserDto> crmUserDtos = quoteFacade.queryAskUser(skuNos);
            return R.success(crmUserDtos);
        }catch (Exception e){
            log.error("获取咨询人员信息失败"+skuNos,e);
            return R.error("未获取到咨询人员信息");
        }

    }

    /**
     * 17、完成咨询
     */
    @RequestMapping(value = "/consultationReportFinish")
    @NoRepeatSubmit
    public R<?> consultationReportFinish(@RequestParam Integer quoteorderId) throws Exception {
        log.info("完成咨询报价单id:{}",quoteorderId);
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        log.info("当前操作人:{}", JSON.toJSON(currentUser));

        quoteFacade.goFinishConsultationReport(quoteorderId, currentUser);
        return R.success();
    }

    /**
     * 18、添加商品/重新添加商品
     * （需求模式）
     */
    @RequestMapping(value = "/singleAddGoods", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> singleAddGoods(@RequestBody SingleAddGoodsRequestDto singleAddGoodsRequestDto) throws Exception {
        log.info("添加商品/重新添加商品singleAddGoods:{}",singleAddGoodsRequestDto);
        ValidatorUtils.validate(singleAddGoodsRequestDto, DefaultGroup.class);
        quoteFacade.singleAddGoods(singleAddGoodsRequestDto, CurrentUser.getCurrentUser());
        return R.success();
    }

    /**
     * 18.1、批量添加商品（需求模式）
     */
    @RequestMapping(value = "/batchAddGoodsNeeds", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> batchAddGoodsNeeds(@RequestBody BatchAddGoodsNeedsRequestDto batchAddGoodsNeedsRequestDto) throws Exception {
        log.info("批量添加商品batchAddGoodsNeeds:{}", JSONObject.toJSONString(batchAddGoodsNeedsRequestDto));
        ValidatorUtils.validate(batchAddGoodsNeedsRequestDto, DefaultGroup.class);
        String message = quoteFacade.batchAddGoodsNeeds(batchAddGoodsNeedsRequestDto, CurrentUser.getCurrentUser());
        return R.success(message);
    }

    /**
     * 19、报备状态修改
     * @param requestDto
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/updateReportStatus", method = RequestMethod.POST)
    @NoRepeatSubmit
    @MenuDesc(menuValue = "C0220",menuDesc = "报备状态修改")
    public R<?> updateReportStatus(@RequestBody QuoteGoodsRequestDto requestDto) throws Exception {
        log.info("报备状态修改updateReportStatus:{}",requestDto);
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        log.info("当前操作人:{}", JSON.toJSON(currentUser));
        quoteFacade.updateReportStatus(requestDto, currentUser);
        return R.success();
    }


    /**
     * 20、消息提醒数
     */
    @RequestMapping(value = "/taskTips")
    @ResponseBody
    public R<QuoteTaskTipsDto> taskTips(@RequestParam Integer quoteorderId) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        log.info("当前操作人:{}", JSON.toJSON(currentUser));
        QuoteTaskTipsDto quoteTaskTipsDto = quoteFacade.taskTips(currentUser, quoteorderId);
        return R.success(quoteTaskTipsDto);
    }

    /**
     * 21、报价单汇总信息
     */
    @RequestMapping(value = "/summaryInfo")
    @ResponseBody
    public R<QuoteSummaryInfoDto> summaryInfo(@RequestParam Integer quoteorderId) {
        QuoteSummaryInfoDto quoteSummaryInfoDto = quoteFacade.summaryInfo(quoteorderId);
        return R.success(quoteSummaryInfoDto);
    }

    /**
     * 22 添加报价需求描述
     * @param requestDto
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/addQuoteNeedsDesc", method = RequestMethod.POST)
    @NoRepeatSubmit
    @MenuDesc(menuValue = "C0230",menuDesc = "添加报价需求描述")
    public R<?> addQuoteNeedsDesc(@RequestBody QuoteNeedsDescReqDto requestDto) throws Exception {
        log.info("添加报价需求描述addQuoteNeedsDesc:{}",JSONObject.toJSONString(requestDto));
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        log.info("当前操作人:{}", JSON.toJSON(currentUser));
        quoteFacade.addQuoteNeedsDesc(requestDto, currentUser);
        return R.success();
    }

    /**
     * 23 添加报价商品备注
     * @param requestDto
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/addQuoteGoodsRemark", method = RequestMethod.POST)
    @NoRepeatSubmit
    @NoNeedAccessAuthorization
    public R<?> addQuoteGoodsRemark(@RequestBody QuoteGoodsRemarkReqDto requestDto) throws Exception {
        log.info("添加报价商品备注addQuoteGoodsRemark:{}",JSONObject.toJSONString(requestDto));
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        log.info("当前操作人:{}", JSON.toJSON(currentUser));
        quoteFacade.addQuoteGoodsRemark(requestDto, currentUser);
        return R.success();
    }

    /**
     * 24 查询报价商品备注列表
     * @param quoteorderGoodsId
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/queryAllRemark")
    @ResponseBody
    @NoRepeatSubmit
    @NoNeedAccessAuthorization
    public R<List<QuoteorderGoodsRemarkDto>> queryAllRemark(@RequestParam Integer quoteorderGoodsId) throws Exception {
        log.info("查询报价商品备注列表quoteorderGoodsId:{}",quoteorderGoodsId);
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        log.info("当前操作人:{}", JSON.toJSON(currentUser));
        List<QuoteorderGoodsRemarkDto> list = quoteFacade.queryAllRemark(quoteorderGoodsId);
        return R.success(list);
    }
}
