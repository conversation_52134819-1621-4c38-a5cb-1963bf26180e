package com.vedeng.kpi.service;

import com.vedeng.kpi.model.DO.KpiDailyCountDo;
import com.vedeng.kpi.model.DO.KpiOrderLogDo;
import com.vedeng.kpi.model.DTO.KpiDailyCountExtDto;
import com.vedeng.kpi.model.DTO.KpiDataQueryDto;
import com.vedeng.kpi.model.DTO.KpiUserInfoDto;
import com.vedeng.kpi.model.VO.KpiBaseInfoVo;
import com.vedeng.kpi.model.VO.KpiDetailYjOverviewVo;
import com.vedeng.kpi.model.base.KpiBaseParams;

import java.util.Date;
import java.util.List;

/**
 * @description: user信息service.
 * @jira: VDERP-2376【五行剑法】规则修改.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/6/15 1:58 下午.
 * @author: Tomcat.Hui.
 */
public interface KpiParamTransService {

    /**
     * @description: 类型转换.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/6/9 3:12 下午.
     * @author: Tomcat.Hui.
     * @param source: .
     * @param target: .
     * @return: void.
     * @throws: .
     */
    KpiDailyCountDo baseParamsToCount(KpiUserInfoDto source);

    /**
     * @description: 类型转换.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/6/9 3:12 下午.
     * @author: Tomcat.Hui.
     * @param source: .
     * @param target: .
     * @return: void.
     * @throws: .
     */
    KpiDataQueryDto baseParamsToQuery(KpiUserInfoDto source);

    /**
     * @description: 类型转换.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: 设置参数.
     * @version: 1.0.
     * @date: 2020/6/9 3:12 下午.
     * @author: Tomcat.Hui.
     * @param source: .
     * @return: void.
     * @throws: .
     */
    KpiUserInfoDto logToUserInfo(KpiOrderLogDo source, Date updateKpiDate);

    /**
     * @description: 参数赋值.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/6/17 10:43 上午.
     * @author: Tomcat.Hui.
     * @param baseList: .
     * @return: java.util.List<? extends com.vedeng.kpi.model.base.KpiBaseParams>.
     * @throws: .
     */
    List<? extends KpiBaseParams> transParams(List<? extends KpiBaseParams> baseList);

    /**
     * @description: 基础字段赋值.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: 从缓存中获取.
     * @version: 1.0.
     * @date: 2020/6/15 1:58 下午.
     * @author: Tomcat.Hui.
     * @param userInfo: .
     * @return: com.vedeng.kpi.model.DTO.KpiUserInfoDto.
     * @throws: .
     */
    KpiBaseParams getCurrentUserInfo(KpiBaseParams userInfo);

    KpiBaseInfoVo getKpiBaseGroupInfo(Integer groupId);

}
