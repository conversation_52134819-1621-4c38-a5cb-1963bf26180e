package com.vedeng.erp.buyorder.service.impl.api;

import com.vedeng.erp.buyorder.dto.BuyOrderRebateChargeApplyApiDto;
import com.vedeng.erp.buyorder.dto.BuyOrderRebateChargeApplyDto;
import com.vedeng.erp.buyorder.mapper.BuyorderRebateChargeApplyMapper;
import com.vedeng.erp.buyorder.mapstruct.BuyOrderRebateChargeApplyApiConvertor;
import com.vedeng.erp.buyorder.service.BuyOrderRebateChargeApplyApiService;
import com.vedeng.erp.buyorder.service.BuyOrderRebateChargeApplyService;
import com.vedeng.infrastructure.file.domain.Attachment;
import com.vedeng.infrastructure.file.mapper.AttachmentMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.buyorder.service
 * @Date 2023/11/22 17:00
 */
@Service
public class BuyOrderRebateChargeApplyApiServiceImpl implements BuyOrderRebateChargeApplyApiService {

    @Autowired
    BuyOrderRebateChargeApplyApiConvertor buyOrderRebateChargeApplyApiConvertor;
    @Autowired
    BuyorderRebateChargeApplyMapper buyorderRebateChargeApplyMapper;
    @Autowired
    AttachmentMapper attachmentMapper;

    @Override
    public BuyOrderRebateChargeApplyApiDto getBuyOrderRebateChargeApplyApiDtoById(Integer buyOrderRebateChargeId) {
        BuyOrderRebateChargeApplyDto buyOrderRebateChargeApplyDto = buyorderRebateChargeApplyMapper.findById(buyOrderRebateChargeId);
        if (StringUtils.isNotBlank(buyOrderRebateChargeApplyDto.getDetailUrlIds())) {
            List<Long> idList = Arrays.stream(buyOrderRebateChargeApplyDto.getDetailUrlIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
            List<Attachment> attachmentList = attachmentMapper.selectAllByIds(idList);
            buyOrderRebateChargeApplyDto.setDetailFileList(attachmentList);
        }
        return buyOrderRebateChargeApplyApiConvertor.to(buyOrderRebateChargeApplyDto);
    }

}
