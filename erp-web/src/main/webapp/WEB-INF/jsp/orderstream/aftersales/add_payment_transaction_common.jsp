<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<script src="${pageContext.request.contextPath}/static/js/orderstream/openfile.js?rnd=${resourceVersionKey}"></script>

<div class="parts">
    <c:set var="amountReceivedTablel" value="0.00"></c:set>
    <c:set var="amountReceivableTable" value="0.00"></c:set>
    <c:choose>
        <c:when test="${not empty afterSalesVo.afterCapitalBillList}">
            <c:forEach items="${afterSalesVo.afterCapitalBillList}" var="acd">
                <c:if test="${acd.capitalBillDetail.bussinessType eq 525}">
                    <c:set value="${acd.amount + amountReceivedTablel}" var="amountReceivedTablel"></c:set>
                </c:if>
            </c:forEach>
        </c:when>
    </c:choose>
    <%--<c:choose>
        <c:when test="${not empty afterSalesVo.afterPayApplyList}">
            <c:forEach items="${afterSalesVo.afterPayApplyList}" var="apalt">
                &lt;%&ndash;仅审核通过&ndash;%&gt;
                <c:if test="${apalt.validStatus eq 1}">
                    <c:set value="${apalt.amount + amountReceivableTable}" var="amountReceivableTable"></c:set>
                </c:if>
            </c:forEach>
        </c:when>
    </c:choose>--%>
    <%--需求变更,固定金额--%>
    <c:choose>
        <c:when test="${not empty afterSalesVo.afterSalesInstallstionVoList}">
            <c:forEach items="${afterSalesVo.afterSalesInstallstionVoList}" var="apalt">
                <c:set value="${apalt.engineerAmount + amountReceivableTable}" var="amountReceivableTable"></c:set>
            </c:forEach>
        </c:when>
    </c:choose>
    <table class="table">
        <thead>
        <tr>
            <td colspan="11" style="clear: both;">
                <div style="float: left;margin-left: 30px;background: orange;clip-path: circle(50%); height: 1em; width: 1em;margin-top: 2px;">
                    <div style="background: white; width: 0.1em;height: 0.5em; margin: 25% 50%;"></div>
                </div>
                <div style="float: left;margin-left: 20px;">实际应付金额：
                    <fmt:formatNumber type="number" value="${amountReceivableTable}" pattern="0.00" maxFractionDigits="2" />
                </div>
                <div style="float: left;margin-left: 100px;">已付金额：
                    ${amountReceivedTablel}
                </div>
                <div style="float: left;margin-left: 100px;">付款状态：
                    <c:choose>
                        <%--修复0.00的问题--%>
                        <c:when test="${(amountReceivableTable) le 0.00}">
                            无付款
                        </c:when>
                        <c:when test="${(amountReceivableTable) ne 0.00 && amountReceivedTablel le 0.00}">
                            未付款
                        </c:when>
                        <c:when test="${(amountReceivableTable) ne 0.00 && amountReceivedTablel gt 0.00 && amountReceivedTablel lt amountReceivableTable}">
                            部分付款
                        </c:when>
                        <c:when test="${(amountReceivableTable) ne 0.00 && amountReceivedTablel ge amountReceivableTable}">
                            全部付款
                        </c:when>
                    </c:choose>
                </div>
            </td>
        </tr>
        <tr>
            <td>记账编号</td>
            <td>业务类型</td>
            <td>交易时间</td>
            <td>交易主体</td>
            <td>交易金额</td>
            <td>交易方式</td>
            <td>交易名称</td>
            <td>交易备注</td>
            <td>操作时间</td>
            <td>操作人</td>
            <td>操作</td>
        </tr>
        </thead>
        <tbody>
        <c:choose>
            <c:when test="${not empty afterSalesVo.afterCapitalBillList}">
                <c:forEach items="${afterSalesVo.afterCapitalBillList}" var="acb">
                    <c:if test="${acb.capitalBillDetail.bussinessType eq 525}">
                        <tr>
                            <td>${acb.capitalBillNo}</td>
                            <td>
                                <c:if test="${acb.capitalBillDetail.bussinessType eq 525}">订单付款</c:if>
                                <c:if test="${acb.capitalBillDetail.bussinessType eq 526}">订单收款</c:if>
                                <c:if test="${acb.capitalBillDetail.bussinessType eq 531}">退款</c:if>
                                <c:if test="${acb.capitalBillDetail.bussinessType eq 532}">资金转移</c:if>
                                <c:if test="${acb.capitalBillDetail.bussinessType eq 533}">信用还款</c:if>
                            </td>
                            <td>
                                <c:if test="${acb.traderTime != 0}">
                                    <date:date value="${acb.traderTime}" />
                                </c:if>
                            </td>
                            <td>
                                <c:if test="${acb.traderSubject == 1}">
                                    对公
                                </c:if>
                                <c:if test="${acb.traderSubject == 2}">
                                    对私
                                </c:if>
                            </td>
                            <td><fmt:formatNumber type="number" value="${acb.amount}" pattern="0.00" maxFractionDigits="2" />
                            </td>
                            <td>
                                <c:if test="${acb.traderMode eq 520}">支付宝</c:if>
                                <c:if test="${acb.traderMode eq 521}">银行</c:if>
                                <c:if test="${acb.traderMode eq 522}">微信</c:if>
                                <c:if test="${acb.traderMode eq 522}">现金</c:if>
                                <c:if test="${acb.traderMode eq 527}">信用支付</c:if>
                                <c:if test="${acb.traderMode eq 528}">余额支付</c:if>
                                <c:if test="${acb.traderMode eq 529}">退还信用</c:if>
                                <c:if test="${acb.traderMode eq 530}">退还余额</c:if>
                            </td>
                            <td>${acb.payer}</td>
                            <td>${acb.comments}</td>
                            <td>
                                <c:if test="${acb.addTime != 0}">
                                    <date:date value="${acb.addTime}" />
                                </c:if>
                            </td>
                            <td>
                                <c:choose>
                                    <c:when test="${empty acb.creatorName || acb.creatorName eq ''}">
                                        <c:forEach var="user" items="${createList}">
                                            <c:if test="${user.userId eq acb.creator}">${user.username}</c:if>
                                        </c:forEach>
                                    </c:when>
                                    <c:otherwise>
                                        ${acb.creatorName}
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td>
                                <c:if test="${(acb.traderType == 2 || acb.traderType == 5) && acb.bankBillId != 0}">
                                    <div class="caozuo">
                                        <c:choose>
                                            <c:when test="${not empty acb.receiptUrl}">
                                                <span class="caozuo-blue" onclick="openFile('${acb.receiptUrl}')">回单</span>
                                            </c:when>
                                            <c:otherwise>
                                                <span class="caozuo-blue addtitle"
                                                      tabTitle='{"num":"credentials${acb.bankBillId}", "link":"<%=basePath%>finance/capitalbill/credentials.do?bankBillId=${acb.bankBillId}","title":"电子回执单"}'>预览</span>
                                            </c:otherwise>
                                        </c:choose>
                                    </div>
                                </c:if>
                            </td>
                        </tr>
                    </c:if>
                </c:forEach>
            </c:when>
            <c:otherwise>
                <tr>
                    <td colspan="11">暂无付款交易信息！</td>
                </tr>
            </c:otherwise>
        </c:choose>
        </tbody>
    </table>
</div>
