package com.vedeng.billsync.task.service.impl;

import com.alibaba.fastjson.TypeReference;
import com.vedeng.billsync.dao.TmpAlipayBillDataExtMapper;
import com.vedeng.billsync.task.model.entity.TmpAlipayBillDataExtDo;
import com.vedeng.billsync.task.model.entity.TmpAlipayBillDataExtPo;
import com.vedeng.billsync.task.model.entity.generate.TmpAlipayBillDataDo;
import com.vedeng.billsync.task.service.AlipayBillSyncTaskService;
import com.vedeng.common.constant.ApiUrlConstant;
import com.vedeng.common.http.HttpRestClientUtil;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;

/**
 * <AUTHOR>
 * @Date: 2020/12/16 11:11
 */
@Service
public class AlipayBillSyncTaskServiceImpl implements AlipayBillSyncTaskService, ApiUrlConstant {

    public final static Logger logger = LoggerFactory.getLogger(AlipayBillSyncTaskServiceImpl.class);

    @Resource
    TmpAlipayBillDataExtMapper tmpAlipayBillDataExtMapper;

    @Value("${api_url}")
    protected String apiUrl;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultInfo syncAlipayBillList(String billDate) {
        //总条数
        int totalItem;
        //成功插入条数
        int successInsertItem = 0;
        try {
            String url = apiUrl + API_ALIPAY_SYNC_BILL_DATA;
            TypeReference<ResultInfo<List<TmpAlipayBillDataExtPo>>> typeReference = new TypeReference<ResultInfo<List<TmpAlipayBillDataExtPo>>>() {
            };
            Map<String, String> mapParam = new HashMap<String, String>(1);
            mapParam.put("billDate", billDate);
            Map<String, String> mapHeaders = new HashMap<String, String>(1);
            mapHeaders.put("version", "v1");
            ResultInfo<List<TmpAlipayBillDataExtPo>> result = HttpRestClientUtil.restGet(url, typeReference, mapHeaders, mapParam);
            if (null != result && result.getCode() == 0) {
                List<TmpAlipayBillDataExtPo> tmpAlipayBillDataExtPoList = result.getData();
                if (CollectionUtils.isNotEmpty(tmpAlipayBillDataExtPoList)) {
                    // add by hollis 2022/9/6 16:19 VDERP-11794 去除删除逻辑 start
//                    Integer deleteResult = tmpAlipayBillDataExtMapper.deleteBySycTime(billDate);
//                    logger.info("类型 支付宝 操作 删除 {} 账单 删除数据返回结果 {}", billDate , deleteResult);
                    // add by hollis 2022/9/6 16:19 VDERP-11794 去除删除逻辑 end

                    totalItem = tmpAlipayBillDataExtPoList.size();
                    for (TmpAlipayBillDataExtPo alipayBillPoData : tmpAlipayBillDataExtPoList) {
                        TmpAlipayBillDataDo oldData =  tmpAlipayBillDataExtMapper.selectByFinancialTurnover(alipayBillPoData.getFinancialTurnover());
                        if(oldData != null){
                            logger.info("类型支付宝操作插入 {} 账单，财务流水号 {} 已存在，不再插入数据库",  billDate , alipayBillPoData.getFinancialTurnover());
                            continue;
                        }
                        TmpAlipayBillDataExtDo tmpAlipayBillDataExtDo = new TmpAlipayBillDataExtDo();
                        tmpAlipayBillDataExtDo.setFinancialTurnover(alipayBillPoData.getFinancialTurnover());
                        tmpAlipayBillDataExtDo.setBusinessTurnover(alipayBillPoData.getBusinessTurnover());
                        tmpAlipayBillDataExtDo.setOrderNo(alipayBillPoData.getOrderNo());
                        tmpAlipayBillDataExtDo.setSkuName(alipayBillPoData.getSkuName());
                        tmpAlipayBillDataExtDo.setOccurrenceTime(alipayBillPoData.getOccurrenceTime());
                        tmpAlipayBillDataExtDo.setOtherAccount(alipayBillPoData.getOtherAccount());
                        Optional.ofNullable(alipayBillPoData.getIncome()).ifPresent(item -> tmpAlipayBillDataExtDo.setIncome(new BigDecimal(item)));
                        Optional.ofNullable(alipayBillPoData.getOutlay()).ifPresent(item -> tmpAlipayBillDataExtDo.setOutlay(new BigDecimal(item)));
                        Optional.ofNullable(alipayBillPoData.getBalance()).ifPresent(item -> tmpAlipayBillDataExtDo.setBalance(new BigDecimal(item)));
                        tmpAlipayBillDataExtDo.setTradingChannel(alipayBillPoData.getTradingChannel());
                        tmpAlipayBillDataExtDo.setBusinessType(alipayBillPoData.getBusinessType());
                        tmpAlipayBillDataExtDo.setRemark(alipayBillPoData.getRemark());
                        tmpAlipayBillDataExtDo.setSyncTime(DateUtil.StringToDate(billDate));
                        tmpAlipayBillDataExtDo.setAddTime(new Timestamp(System.currentTimeMillis()));
                        Integer insertResult = tmpAlipayBillDataExtMapper.insert(tmpAlipayBillDataExtDo);
                        if (insertResult == 1) {
                            successInsertItem++;
                        }
                        logger.info("类型 支付宝 操作 插入 {} 账单 财务流水号 {} 插入数据库返回结果 {}",  billDate , tmpAlipayBillDataExtDo.getFinancialTurnover(), insertResult);
                    }
                } else {
                    return new ResultInfo(-1, "支付宝账单插入失败！数据返回为空！");
                }
            } else {
                if (null == result){
                    result = new ResultInfo(-1,"Result接受参数为NULL,请检查API地址是否正确！");
                }
                return new ResultInfo(-1, "同步支付宝账单失败！" + " Message:" + result.getMessage() + " 状态码：" + result.getStatus());
            }
        } catch (Exception e) {
            logger.error("同步支付宝账单失败！", e);
            throw e;
        }
        return new ResultInfo(0, "支付宝账单插入成功！总条数[ " + totalItem + " ],成功插入条数[ " + successInsertItem + " ]。");
    }
}
