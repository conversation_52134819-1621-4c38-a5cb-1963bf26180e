package com.vedeng.erp.finance.domain.dto;

import com.vedeng.common.core.base.BaseDto;
import com.vedeng.erp.finance.dto.AssociateAfterSaleDetailDto;
import com.vedeng.erp.saleorder.dto.AfterSalesGoodsDto;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 发票红字确认单
 * @date 2023/10/13 9:33
 */
@Getter
@Setter
public class InvoiceRedConfirmationItemDto extends BaseDto {

    private Integer invoiceRedConfirmationItemId;


    /**
     * 红字确认单id
     */
    private Integer invoiceRedConfirmationId;

    /**
     * 红字确认单唯一标识符
     */
    private String uuid;

    /**
     * 税务局定义的红字确认单单据编号
     */
    private String invoiceRedConfirmationNo;

    /**
     * 序号
     */
    private Integer serialNumber;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目简称
     */
    private String projectSimpleName;

    /**
     * 项目全称
     */
    private String projectFullName;

    /**
     * 税收类别编号
     */
    private String taxCategoryNo;

    /**
     * 规格型号
     */
    private String specifications;

    /**
     * 单位
     */
    private String unit;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 数量
     */
    private BigDecimal quantity;

    /**
     * 金额 (不含税)
     */
    private BigDecimal amount;

    /**
     * 单价（不含税）
     */
    private BigDecimal unitPrice;

    /**
     * 税额
     */
    private BigDecimal uaxAmount;

    /**
     * 价税合计
     */
    private BigDecimal pricePlusTaxes;

    /**
     * 源单业务单据明细ID
     */
    private Integer businessOrderItemId;

    /**
     * 售后业务明细ID
     */
    private Integer afterSaleBusinessOrderItemId;

    /**
     * 蓝票明细ID
     */
    private Integer blueInvoiceItemId;

    /**
     * 本次剩余可红字数量
     */
    private BigDecimal thisTimeSurplusNumber;

    /**
     * 本次剩余可红字金额 (不含税)
     */
    private BigDecimal thisTimeSurplusAmount;

    /**
     * 本次剩余可红字税额
     */
    private BigDecimal thisTimeSurplusUaxAmount;

    /**
     * 本次剩余可红字价税合计
     */
    private BigDecimal thisTimeSurplusPricePlusTaxes;

    /**
     * 确认单明细关联的售后商品明细（理论上是一对一，只有一个的）
     */
    private List<AfterSalesGoodsDto> afterSalesGoodsDtoList = new ArrayList<>();

    /**
     * 红字确认单明细关联的售后商品明细
     */
    private List<AssociateAfterSaleDetailDto> associateAfterSaleDetailDtoList;

    /**
     * 蓝票是否确认用途: 未确认用途：蓝票入账状态 = 未入账 and 蓝票确认状态 = 未勾选
     */
    private String blueUsage;

    /**
     * 序号 (表示蓝票序号)
     */
    private String xh;
}
