package com.vedeng.doc.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.doc.common.BuzTypeEnum;
import com.vedeng.doc.common.constant.ErpDocConstant;
import com.vedeng.doc.domain.entity.DocBuzTag;
import com.vedeng.doc.domain.entity.DocOfGoods;
import com.vedeng.doc.domain.entity.DocSupplier;
import com.vedeng.doc.dto.DocBuzTagFileDto;
import com.vedeng.doc.dto.DocFileDto;
import com.vedeng.doc.dto.DocOfGoodsQueryDetailDto;
import com.vedeng.doc.query.DocOfGoodsDetailQuery;
import com.vedeng.doc.mapper.DocBuzTagFileMapper;
import com.vedeng.doc.mapper.DocBuzTagMapper;
import com.vedeng.doc.mapper.DocOfGoodsMapper;
import com.vedeng.doc.mapper.DocSupplierMapper;
import com.vedeng.doc.service.DocOfGoodsService;
import com.vedeng.goods.service.RegistrationNumberApiService;
import com.vedeng.goods.dto.AttachmentRegistrationNumberDto;
import com.vedeng.goods.dto.RegistrationNumberSpuDto;
import com.vedeng.goods.query.AttachmentRegistrationNumberQuery;
import com.vedeng.goods.query.RegistrationNumberSpuQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DocOfGoodsServiceImpl implements DocOfGoodsService {

    private final DocOfGoodsMapper docOfGoodsMapper;

    private final RegistrationNumberApiService registrationNumberApiService;

    private final DocSupplierMapper docSupplierMapper;

    private final DocBuzTagMapper docBuzTagMapper;

    private final DocBuzTagFileMapper docBuzTagFileMapper;

    @Value("${oss_http}")
    private String oss_http;

    @Override
    public List<DocOfGoodsQueryDetailDto> queryDocOfGoodsDetail(DocOfGoodsDetailQuery docOfGoodsDetailQuery) {

        Integer skuId = docOfGoodsDetailQuery.getSkuId();
        if (Objects.isNull(skuId)) {
            throw new ServiceException("未获取到有效id数据");
        }
        DocOfGoods docOfGoods = docOfGoodsMapper.findBySkuIdAndDisableAndIsDelete(skuId,ErpDocConstant.Number.ZERO,ErpDocConstant.Number.ZERO)
                .stream().findFirst().orElse(new DocOfGoods());
        if (Objects.isNull(docOfGoods.getGoodsName())) {
            throw new ServiceException("该商品暂无资料");
        }

        String skuName = docOfGoodsMapper.selectCoreSkuByPrimaryKey(docOfGoods.getSkuId());
        if(Objects.nonNull(skuName)){
            docOfGoods.setGoodsName(skuName);
        }

        List<DocOfGoodsQueryDetailDto> docOfGoodsQueryDetailDtoList = new ArrayList<>();


        // 商品资料 -- 下载文件集合处理
        dealDocOfGoodsDownloadFile(docOfGoodsQueryDetailDtoList, docOfGoods);

        // 厂商资质 -- 下载文件集合处理
        dealDocSupplierDownloadFile(docOfGoodsQueryDetailDtoList, docOfGoods);

        log.info("去除无效链接之前JSON {}", JSON.toJSON(docOfGoodsQueryDetailDtoList));
        // 去除无效链接
        removeNullFileUrlList(docOfGoodsQueryDetailDtoList);
        log.info("去除无效链接之后JSON {}", JSON.toJSON(docOfGoodsQueryDetailDtoList));

        return docOfGoodsQueryDetailDtoList;
    }

    /**
     * 去除无效链接
     * @param docOfGoodsQueryDetailDtoList
     */
    private void removeNullFileUrlList(List<DocOfGoodsQueryDetailDto> docOfGoodsQueryDetailDtoList) {

        docOfGoodsQueryDetailDtoList.stream().forEach(docOfGoodsQueryDetailDto -> {
            if (Objects.isNull(docOfGoodsQueryDetailDto) || CollectionUtil.isEmpty(docOfGoodsQueryDetailDto.getTagFileDtoList())) {
                return;
            }
            docOfGoodsQueryDetailDto.getTagFileDtoList().stream().forEach(tagFileDto -> {
                if (Objects.isNull(tagFileDto) || CollectionUtil.isEmpty(tagFileDto.getFileList())) {
                    return;
                }
                removeNullFileUrl(tagFileDto.getFileList());
            });
        });

        docOfGoodsQueryDetailDtoList.stream().forEach(docOfGoodsQueryDetailDto -> {
            if (Objects.isNull(docOfGoodsQueryDetailDto) || CollectionUtil.isEmpty(docOfGoodsQueryDetailDto.getTagFileDtoList())) {
                return;
            }
            removeNullTagFileUrl(docOfGoodsQueryDetailDto.getTagFileDtoList());
        });

        removeNullTotalFileUrl(docOfGoodsQueryDetailDtoList);
    }

    private void removeNullTotalFileUrl(List<DocOfGoodsQueryDetailDto> docOfGoodsQueryDetailDtoList) {
        Iterator<DocOfGoodsQueryDetailDto> iterator = docOfGoodsQueryDetailDtoList.iterator();
        while (iterator.hasNext()) {
            DocOfGoodsQueryDetailDto next = iterator.next();
            if (Objects.isNull(next) || CollectionUtil.isEmpty(next.getTagFileDtoList())) {
                iterator.remove();
            }
        }
    }

    private void removeNullTagFileUrl(List<DocOfGoodsQueryDetailDto.TagFileDto> tagFileDtoList) {
        Iterator<DocOfGoodsQueryDetailDto.TagFileDto> iterator = tagFileDtoList.iterator();
        while (iterator.hasNext()) {
            DocOfGoodsQueryDetailDto.TagFileDto next = iterator.next();
            if (Objects.isNull(next) || CollectionUtil.isEmpty(next.getFileList())) {
                iterator.remove();
            }
        }
    }

    private void removeNullFileUrl(List<DocFileDto> fileList) {

        Iterator<DocFileDto> iterator = fileList.iterator();
        while (iterator.hasNext()) {
            DocFileDto next = iterator.next();
            if (Objects.isNull(next) || Objects.isNull(next.getDownloadUrl())) {
                iterator.remove();
            }
        }
    }

    /**
     * 注册证单独处理 spuId >>> 注册证
     *
     * @param docOfGoods
     */
    private RegistrationNumberSpuDto dealRegistrationNumberBuzTagFile(DocOfGoods docOfGoods) {

        if (Objects.isNull(docOfGoods.getSpuId())) {
            return null;
        }
        RegistrationNumberSpuQuery registrationNumberSpuQuery = RegistrationNumberSpuQuery
                .builder()
                .spuId(docOfGoods.getSpuId())
                .build();
        RegistrationNumberSpuDto registrationNumberSpuDto = registrationNumberApiService.queryRegistrationNumberBySpuId(registrationNumberSpuQuery);
        if (Objects.isNull(registrationNumberSpuDto) || Objects.isNull(registrationNumberSpuDto.getRegistrationNumberId())) {
            return null;
        }
        return registrationNumberSpuDto;

    }

    /**
     * 注册证 >>> 附件下载
     *
     * @param registrationNumberSpuDto
     * @return
     */
    private List<AttachmentRegistrationNumberDto> dealAttachmentRegistrationNumberDtoList(RegistrationNumberSpuDto registrationNumberSpuDto) {

        if (Objects.isNull(registrationNumberSpuDto) || Objects.isNull(registrationNumberSpuDto.getRegistrationNumberId())) {
            return null;
        }
        AttachmentRegistrationNumberQuery attachmentRegistrationNumberQuery = AttachmentRegistrationNumberQuery
                .builder()
                .registrationNumberId(registrationNumberSpuDto.getRegistrationNumberId())
                .attachmentFunction(ErpDocConstant.ATTACHMENT_FUNCTION_975)
                .attachmentType(ErpDocConstant.ATTACHMENT_TYPE_974)
                .build();
        List<AttachmentRegistrationNumberDto> attachmentRegistrationNumberDtoList = registrationNumberApiService.queryAttachmentsByRegistrationNumberId(attachmentRegistrationNumberQuery);
        if (CollectionUtils.isEmpty(attachmentRegistrationNumberDtoList)) {
            return null;
        }
        return attachmentRegistrationNumberDtoList;
    }

    /**
     * 处理资料库商品资料下载文件
     *
     * @param docOfGoodsQueryDetailDtoList 返回值
     * @param docOfGoods                   入参
     */
    public void dealDocOfGoodsDownloadFile(List<DocOfGoodsQueryDetailDto> docOfGoodsQueryDetailDtoList,
                                           DocOfGoods docOfGoods) {

        // 查询所有资料库商品资料标签Tag
        List<DocBuzTag> docBuzTagList = docBuzTagMapper.selectListByBuzType(BuzTypeEnum.DOC_OF_GOODS.getBuzType());

        // 查询该商品资料所有下载信息(所有Tag文件汇总)
        List<Integer> docGoodsIdList = ListUtil.of(docOfGoods.getId());
        List<DocBuzTagFileDto> docBuzTagFileList = docBuzTagFileMapper.selectDocGoodsListByBuzIdList(docGoodsIdList);

        // 转换基础数据集合类型
        List<DocOfGoodsQueryDetailDto.TagFileDto> tagFileDtoList = new ArrayList<>();
        docBuzTagList.forEach(docBuzTag -> {

            // 商品资料所有下载信息(所有Tag文件汇总) 根据Tag分类获取指定Tag下の下载文件
            List<DocBuzTagFileDto> docBuzTagFileDtoList = docBuzTagFileList
                    .stream()
                    .filter(docBuzTagFile -> docBuzTag.getId().equals(docBuzTagFile.getBuzTagId()))
                    .collect(Collectors.toList());

            // 注册证单独处理
            if (ErpDocConstant.Number.ONE.equals(docBuzTag.getSource())
                    && ErpDocConstant.REGISTRATION_ID_STR.equals(docBuzTag.getExternalFunctionId())) {

                RegistrationNumberSpuDto registrationNumberSpuDto = dealRegistrationNumberBuzTagFile(docOfGoods);
                List<AttachmentRegistrationNumberDto> attachmentRegistrationNumberDtoList = dealAttachmentRegistrationNumberDtoList(registrationNumberSpuDto);
                if (!CollectionUtils.isEmpty(attachmentRegistrationNumberDtoList)) {

                    docBuzTagFileDtoList = convertAttachmentToDocBuzTagFileDto(attachmentRegistrationNumberDtoList, registrationNumberSpuDto, docBuzTag);
                }

            }

            // 区分 Tag 含章-不含章
            if (ErpDocConstant.Number.ONE.equals(docBuzTag.getNeedHasStamp())) {

                distinguishBetweenChaptersWithoutChapters(tagFileDtoList, docBuzTag, docBuzTagFileDtoList, ErpDocConstant.Number.ONE, docOfGoods);

                distinguishBetweenChaptersWithoutChapters(tagFileDtoList, docBuzTag, docBuzTagFileDtoList, ErpDocConstant.Number.ZERO, docOfGoods);

            } else {

                dealTagNoHasStamp(tagFileDtoList, docBuzTag, docBuzTagFileDtoList, docOfGoods);
            }

        });

        // 装载返回数据
        if (CollectionUtils.isEmpty(tagFileDtoList)) {
            return;
        }
        DocOfGoodsQueryDetailDto docOfGoodsQueryDetailDto = new DocOfGoodsQueryDetailDto();
        docOfGoodsQueryDetailDto.setBuzType(BuzTypeEnum.DOC_OF_GOODS.getBuzType());
        docOfGoodsQueryDetailDto.setTagFileDtoList(tagFileDtoList);

        docOfGoodsQueryDetailDtoList.add(docOfGoodsQueryDetailDto);
    }

    /**
     * 转换Attachment >>> DocBuzTagFileDto
     *
     * @param attachmentRegistrationNumberDtoList
     * @param registrationNumberSpuDto
     * @param docBuzTag
     * @return
     */
    private List<DocBuzTagFileDto> convertAttachmentToDocBuzTagFileDto(List<AttachmentRegistrationNumberDto> attachmentRegistrationNumberDtoList, RegistrationNumberSpuDto registrationNumberSpuDto, DocBuzTag docBuzTag) {

        return attachmentRegistrationNumberDtoList
                .stream()
                .map(attachmentRegistrationNumberDto ->

                        DocBuzTagFileDto
                                .builder()
                                .suffix(attachmentRegistrationNumberDto.getSuffix())
                                .fileName(attachmentRegistrationNumberDto.getName())
                                .fileName(docBuzTag.getBuzTagName().concat("_含章_").concat(DateUtil.formatDate(DateUtil.date())))
                                .ossUrl((Objects.nonNull(attachmentRegistrationNumberDto.getDomain())
                                        && Objects.nonNull(attachmentRegistrationNumberDto.getUri())) ?
                                        (oss_http + attachmentRegistrationNumberDto.getDomain().concat(attachmentRegistrationNumberDto.getUri())) : null)
                                .ossLinkMobile((Objects.nonNull(attachmentRegistrationNumberDto.getDomain())
                                        && Objects.nonNull(attachmentRegistrationNumberDto.getUri())) ?
                                        (oss_http + attachmentRegistrationNumberDto.getDomain().concat(attachmentRegistrationNumberDto.getUri())) : null)
                                .addTime(System.currentTimeMillis())
                                .originalFileName(ErpConstant.EMPTY)
                                .validStartTime(registrationNumberSpuDto.getIssuingDate())
                                .validEndTime(registrationNumberSpuDto.getEffectiveDate())
                                .hasStamp(docBuzTag.getNeedHasStamp())
                                .buzTagId(docBuzTag.getId())
                                .build()

                ).collect(Collectors.toList());
    }

    /**
     * Tag 不含章处理
     *
     * @param tagFileDtoList
     * @param docBuzTag
     * @param docBuzTagFileDtoList
     * @param docOfGoods
     */
    private void dealTagNoHasStamp(List<DocOfGoodsQueryDetailDto.TagFileDto> tagFileDtoList, DocBuzTag docBuzTag, List<DocBuzTagFileDto> docBuzTagFileDtoList, DocOfGoods docOfGoods) {

        List<DocFileDto> hasOrNotHasStampDocFileList = dealTypeConvertDocFileDtoList(docBuzTagFileDtoList, docBuzTag, docOfGoods);
        if (CollectionUtil.isEmpty(hasOrNotHasStampDocFileList)) {
            return;
        }

        // 标签文件(包含标签名、标签文件信息) - 不含章
        DocOfGoodsQueryDetailDto.TagFileDto tagFileDto = new DocOfGoodsQueryDetailDto.TagFileDto();

        tagFileDto.setTagName(docBuzTag.getBuzTagName());
        tagFileDto.setFileList(hasOrNotHasStampDocFileList);

        // 维护Tag维护效期
        maintenancePeriod(tagFileDtoList, docBuzTag, docBuzTagFileDtoList, tagFileDto);
    }

    /**
     * 区分含章不含章 、拆分成两个Tag
     *
     * @param tagFileDtoList
     * @param docBuzTag
     * @param docBuzTagFileDtoList
     * @param docOfGoods
     */
    private void distinguishBetweenChaptersWithoutChapters(List<DocOfGoodsQueryDetailDto.TagFileDto> tagFileDtoList,
                                                           DocBuzTag docBuzTag,
                                                           List<DocBuzTagFileDto> docBuzTagFileDtoList,
                                                           Integer hasStampFlag,
                                                           DocOfGoods docOfGoods) {

        List<DocBuzTagFileDto> hasOrNotStampDocBuzTagFileDtoList = docBuzTagFileDtoList
                .stream()
                .filter(docBuzTagFileDto -> hasStampFlag.equals(docBuzTagFileDto.getHasStamp()))
                .collect(Collectors.toList());

        if (docBuzTag.getBuzTagName().endsWith(ErpDocConstant.HasOrNotStamp.HAS)) {
            docBuzTag.setBuzTagName(docBuzTag.getBuzTagName().substring(ErpDocConstant.Number.ZERO, docBuzTag.getBuzTagName().indexOf(ErpDocConstant.HasOrNotStamp.HAS)));
        }
        if (docBuzTag.getBuzTagName().endsWith(ErpDocConstant.HasOrNotStamp.HAS)) {
            docBuzTag.setBuzTagName(docBuzTag.getBuzTagName().substring(ErpDocConstant.Number.ZERO, docBuzTag.getBuzTagName().indexOf(ErpDocConstant.HasOrNotStamp.HAS)));
        }
        StringBuilder tagName = new StringBuilder();
        tagName.append(docBuzTag.getBuzTagName());
        tagName.append(ErpDocConstant.Number.ONE.equals(hasStampFlag) ? ErpDocConstant.HasOrNotStamp.HAS : ErpDocConstant.HasOrNotStamp.NOT_HAS);
        docBuzTag.setBuzTagName(tagName.toString());

        List<DocFileDto> hasOrNotHasStampDocFileList = dealTypeConvertDocFileDtoList(hasOrNotStampDocBuzTagFileDtoList, docBuzTag, docOfGoods);
        if (CollectionUtil.isEmpty(hasOrNotHasStampDocFileList)) {
            return;
        }

        // 标签文件(包含标签名、标签文件信息) - 不含章
        DocOfGoodsQueryDetailDto.TagFileDto hasOrNotHasStampTagFileDto = new DocOfGoodsQueryDetailDto.TagFileDto();

        hasOrNotHasStampTagFileDto.setTagName(tagName.toString());
        hasOrNotHasStampTagFileDto.setFileList(hasOrNotHasStampDocFileList);

        // 维护Tag维护效期
        maintenancePeriod(tagFileDtoList, docBuzTag, docBuzTagFileDtoList, hasOrNotHasStampTagFileDto);
    }

    /**
     * 维护有效期
     *
     * @param tagFileDtoList
     * @param docBuzTag
     * @param docBuzTagFileDtoList
     * @param hasOrNotHasStampTagFileDto
     */
    private void maintenancePeriod(List<DocOfGoodsQueryDetailDto.TagFileDto> tagFileDtoList, DocBuzTag docBuzTag, List<DocBuzTagFileDto> docBuzTagFileDtoList, DocOfGoodsQueryDetailDto.TagFileDto hasOrNotHasStampTagFileDto) {

        if (ErpDocConstant.Number.ONE.equals(docBuzTag.getMaintainValidPeriod()) && CollectionUtil.isNotEmpty(docBuzTagFileDtoList)) {

            DocBuzTagFileDto docBuzTagFileDto = docBuzTagFileDtoList.stream().findAny().orElse(null);
            if (Objects.nonNull(docBuzTagFileDto.getValidStartTime())) {
                hasOrNotHasStampTagFileDto.setValidStartTime(DateUtil.formatDate(DateUtil.date(docBuzTagFileDto.getValidStartTime())));
                if (Objects.nonNull(docBuzTagFileDto.getValidEndTime())) {
                    hasOrNotHasStampTagFileDto.setValidEndTime(DateUtil.formatDate(DateUtil.date(docBuzTagFileDto.getValidEndTime())));
                }
            }
        }

        tagFileDtoList.add(hasOrNotHasStampTagFileDto);
    }

    /**
     * 判断是否超过有效期
     *
     * @param expireTime
     * @return
     */
    private boolean ifOrNotOutOfExpire(Long expireTime) {
        return Objects.isNull(expireTime) ? false : expireTime < System.currentTimeMillis();
    }

    private boolean ifOutOfExpire(String expireTime) {
        return !isNotBlank(expireTime).isPresent() ?
                false : strConvert2LongByDate(expireTime, "yyyy-MM-dd") < System.currentTimeMillis();
    }

    private static Long strConvert2LongByDate(String datetime, String format) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern(format);
        return LocalDateTime.of(LocalDate.parse(datetime, fmt), LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 标签文件类型(字段定义)转换
     *
     * @param docBuzTagFileDtoList
     * @param docBuzTag
     * @param docOfGoods
     * @return
     */
    private List<DocFileDto> dealTypeConvertDocFileDtoList(List<DocBuzTagFileDto> docBuzTagFileDtoList, DocBuzTag docBuzTag, DocOfGoods docOfGoods) {

        List<DocFileDto> docFileList = new ArrayList<>();

        AtomicInteger fileNameIndex = new AtomicInteger(1);
        docBuzTagFileDtoList.forEach(docBuzTagFileDto -> {

            if(Objects.isNull(docBuzTagFileDto.getOssLinkMobile())){
                return;
            }

            // 标签文件信息(文件基本信息)
            DocFileDto docFileDto = new DocFileDto();
//            docFileDto.setDownloadUrl(replaceOssLink(docBuzTagFileDto.getOssUrl()));
            docFileDto.setDownloadUrl(docBuzTagFileDto.getOssLinkMobile());
            docFileDto.setSuffix(docBuzTagFileDto.getSuffix());
            docFileDto.setFileName(docOfGoods.getGoodsName().replace("/","每").concat(" ").concat(" ").concat(docBuzTag.getBuzTagName()).concat(String.valueOf(fileNameIndex.getAndIncrement())));
            docFileList.add(docFileDto);
        });
        return docFileList;
    }

    /**
     * 下载文件重命名
     *
     * @param tagName
     * @param addTime
     * @param suffix
     * @param index
     * @return
     */
    protected String renameFile(String tagName, Long addTime, String suffix, Integer index) {

        StringBuilder showFileName = new StringBuilder();
        if (StringUtils.isNotEmpty(tagName)) {
            showFileName.append(tagName);
        }
        showFileName.append('_');
        if (Objects.isNull(addTime)) {
            addTime = 0L;
        }
        showFileName.append(DateUtil.format(DateUtil.date(addTime), ErpDocConstant.Date.TIME_FORMAT_LONG));
        showFileName.append('_');
        showFileName.append(index);
        showFileName.append('.');

        if (StringUtils.isNotEmpty(tagName)) {
            showFileName.append(suffix);
        }
        return showFileName.toString();
    }

    /**
     * 下载文件重命名 1
     *
     * @param docTitle
     * @param tagName
     * @param suffix
     * @param index
     * @return
     */
    protected String renameFile1(String docTitle, String tagName, String suffix, Integer index) {
        return isNotBlank(docTitle)
                .map(t -> t.replaceAll("\\s*资料$", StringUtils.EMPTY))
                .map(s -> s.concat(tagName).concat(index.toString()))
                .map(s -> s.concat(".").concat(isNotBlank(suffix).orElse(""))).orElse("");
    }

    /**
     * 下载文件Link调整
     *
     * @param url
     * @return
     */
    private String replaceOssLink(String url) {
        return isNotBlank(url).filter(s -> s.contains("file.vedeng.com"))
                .map(s -> s.replace("display", "download"))
                .orElse(url);
    }

    private Optional<String> isNotBlank(String str) {
        return Optional.ofNullable(str).filter(s -> !s.equals(""));
    }

    /**
     * 处理资料库厂商资质下载文件
     *
     * @param docOfGoodsQueryDetailDtoList 返回值
     * @param docOfGoods                   入参
     */
    public void dealDocSupplierDownloadFile(List<DocOfGoodsQueryDetailDto> docOfGoodsQueryDetailDtoList, DocOfGoods docOfGoods) {

        Integer docSupplierId = docOfGoods.getDocSupplierId();
        DocSupplier docSupplier = new DocSupplier();
        if (Objects.isNull(docSupplierId)) {

            Integer brandId = docOfGoods.getBrandId();
            if (Objects.isNull(brandId)) {
                return;
            }
            List<DocSupplier> docSuppliers = docSupplierMapper.selectListByBrandId(brandId);
            if (CollectionUtil.isEmpty(docSuppliers)) {
                return;
            }
            docSupplier = docSuppliers.get(ErpDocConstant.Number.ZERO);
            if (Objects.isNull(docSupplier) || Objects.isNull(docSupplier.getSupplierId()) || Objects.isNull(docSupplier.getSupplierName())) {
                return;
            }
            docSupplierId = docSupplier.getSupplierId();
            if (Objects.isNull(docSupplierId)) {
                return;
            }

        }else{

            docSupplier = docSupplierMapper.selectByPrimaryKey(docSupplierId);
            if (Objects.isNull(docSupplier) || Objects.isNull(docSupplier.getSupplierName())) {
                return;
            }
        }



        // 查询所有资料库厂商资质标签Tag
        List<DocBuzTag> docBuzTagList = docBuzTagMapper.selectListByBuzType(BuzTypeEnum.DOC_SUPPLIER.getBuzType());

        // 查询该商品资料所有下载信息(所有Tag文件汇总)
        List<Integer> docSupplierIdList = ListUtil.of(docSupplierId);
        List<DocBuzTagFileDto> docBuzTagFileList = docBuzTagFileMapper.selectDocSupplierListByBuzIdList(docSupplierIdList);

        // 转换基础数据集合类型
        List<DocOfGoodsQueryDetailDto.TagFileDto> tagFileDtoList = new ArrayList<>();
        DocSupplier finalDocSupplier = docSupplier;
        List<DocOfGoodsQueryDetailDto.TagFileDto> finalTagFileDtoList = tagFileDtoList;
        docBuzTagList.forEach(docBuzTag -> {

            // 商品资料所有下载信息(所有Tag文件汇总) 根据Tag分类获取指定Tag下の下载文件
            List<DocBuzTagFileDto> docBuzTagFileDtoList = docBuzTagFileList
                    .stream()
                    .filter(docBuzTagFile -> docBuzTag.getId().equals(docBuzTagFile.getBuzTagId()))
//                    .filter(docBuzTagFile -> ErpMobileConst.Number.ZERO.equals(docBuzTag.getMaintainValidPeriod()) || !ifOrNotOutOfExpire(docBuzTagFile.getValidEndTime()))
                    .collect(Collectors.toList());

            // 区分 Tag 含章-不含章
            if (ErpDocConstant.Number.ONE.equals(docBuzTag.getNeedHasStamp())) {

                distinguishSupplierBetweenChaptersWithoutChapters(finalTagFileDtoList, docBuzTag, docBuzTagFileDtoList, ErpDocConstant.Number.ONE, finalDocSupplier);

                distinguishSupplierBetweenChaptersWithoutChapters(finalTagFileDtoList, docBuzTag, docBuzTagFileDtoList, ErpDocConstant.Number.ZERO, finalDocSupplier);

            } else {

                dealSupplierTagNoHasStamp(finalTagFileDtoList, docBuzTag, docBuzTagFileDtoList, docOfGoods, finalDocSupplier);
            }

        });

        tagFileDtoList = tagFileDtoList.stream()
                .filter(tagFileDto -> !ifOutOfExpire(tagFileDto.getValidEndTime()))
                .collect(Collectors.toList());

        // 装载返回数据
        if (CollectionUtils.isEmpty(tagFileDtoList)) {
            return;
        }
        DocOfGoodsQueryDetailDto docOfGoodsQueryDetailDto = new DocOfGoodsQueryDetailDto();
        docOfGoodsQueryDetailDto.setBuzType(BuzTypeEnum.DOC_SUPPLIER.getBuzType());
        docOfGoodsQueryDetailDto.setTagFileDtoList(tagFileDtoList);

        docOfGoodsQueryDetailDtoList.add(docOfGoodsQueryDetailDto);

    }

    private void dealSupplierTagNoHasStamp(List<DocOfGoodsQueryDetailDto.TagFileDto> tagFileDtoList, DocBuzTag docBuzTag, List<DocBuzTagFileDto> docBuzTagFileDtoList, DocOfGoods docOfGoods, DocSupplier docSupplier) {

        List<DocFileDto> hasOrNotHasStampDocFileList = dealSupplierTypeConvertDocFileDtoList(docBuzTagFileDtoList, docBuzTag, docSupplier);
        if (CollectionUtil.isEmpty(hasOrNotHasStampDocFileList)) {
            return;
        }

        // 标签文件(包含标签名、标签文件信息) - 不含章
        DocOfGoodsQueryDetailDto.TagFileDto tagFileDto = new DocOfGoodsQueryDetailDto.TagFileDto();

        tagFileDto.setTagName(docBuzTag.getBuzTagName());
        tagFileDto.setFileList(hasOrNotHasStampDocFileList);

        // 维护Tag维护效期
        maintenancePeriod(tagFileDtoList, docBuzTag, docBuzTagFileDtoList, tagFileDto);
    }

    /**
     * @param tagFileDtoList
     * @param docBuzTag
     * @param docBuzTagFileDtoList
     * @param hasStampFlag
     */
    private void distinguishSupplierBetweenChaptersWithoutChapters(List<DocOfGoodsQueryDetailDto.TagFileDto> tagFileDtoList,
                                                                   DocBuzTag docBuzTag,
                                                                   List<DocBuzTagFileDto> docBuzTagFileDtoList,
                                                                   Integer hasStampFlag,
                                                                   DocSupplier docSupplier) {

        List<DocBuzTagFileDto> hasOrNotStampDocBuzTagFileDtoList = docBuzTagFileDtoList
                .stream()
                .filter(docBuzTagFileDto -> hasStampFlag.equals(docBuzTagFileDto.getHasStamp()))
                .collect(Collectors.toList());

        if (docBuzTag.getBuzTagName().endsWith(ErpDocConstant.HasOrNotStamp.HAS)) {
            docBuzTag.setBuzTagName(docBuzTag.getBuzTagName().substring(ErpDocConstant.Number.ZERO, docBuzTag.getBuzTagName().indexOf(ErpDocConstant.HasOrNotStamp.HAS)));
        }
        if (docBuzTag.getBuzTagName().endsWith(ErpDocConstant.HasOrNotStamp.HAS)) {
            docBuzTag.setBuzTagName(docBuzTag.getBuzTagName().substring(ErpDocConstant.Number.ZERO, docBuzTag.getBuzTagName().indexOf(ErpDocConstant.HasOrNotStamp.HAS)));
        }
        StringBuilder tagName = new StringBuilder();
        tagName.append(docBuzTag.getBuzTagName());
        tagName.append(ErpDocConstant.Number.ONE.equals(hasStampFlag) ? ErpDocConstant.HasOrNotStamp.HAS : ErpDocConstant.HasOrNotStamp.NOT_HAS);

        docBuzTag.setBuzTagName(tagName.toString());

        List<DocFileDto> hasOrNotHasStampDocFileList = dealSupplierTypeConvertDocFileDtoList(hasOrNotStampDocBuzTagFileDtoList, docBuzTag, docSupplier);
        if (CollectionUtil.isEmpty(hasOrNotHasStampDocFileList)) {
            return;
        }

        // 标签文件(包含标签名、标签文件信息) - 不含章
        DocOfGoodsQueryDetailDto.TagFileDto hasOrNotHasStampTagFileDto = new DocOfGoodsQueryDetailDto.TagFileDto();

        hasOrNotHasStampTagFileDto.setTagName(tagName.toString());
        hasOrNotHasStampTagFileDto.setFileList(hasOrNotHasStampDocFileList);

        // 维护Tag维护效期
        maintenancePeriod(tagFileDtoList, docBuzTag, docBuzTagFileDtoList, hasOrNotHasStampTagFileDto);
    }

    private List<DocFileDto> dealSupplierTypeConvertDocFileDtoList(List<DocBuzTagFileDto> docBuzTagFileDtoList, DocBuzTag docBuzTag, DocSupplier docSupplier) {

        List<DocFileDto> docFileList = new ArrayList<>();

        AtomicInteger fileNameIndex = new AtomicInteger(1);
        docBuzTagFileDtoList.forEach(docBuzTagFileDto -> {

            if(Objects.isNull(docBuzTagFileDto.getOssLinkMobile())){
                return;
            }

            // 标签文件信息(文件基本信息)
            DocFileDto docFileDto = new DocFileDto();
//            docFileDto.setDownloadUrl(docBuzTagFileDto.getDomain().concat(docBuzTagFileDto.getUri()));
            docFileDto.setDownloadUrl(docBuzTagFileDto.getOssLinkMobile());
            docFileDto.setSuffix(docBuzTagFileDto.getSuffix());
            docFileDto.setFileName(docSupplier.getSupplierName().replace("/","每").concat(" ").concat(" ").concat(docBuzTag.getBuzTagName()).concat(String.valueOf(fileNameIndex.getAndIncrement())));
            docFileList.add(docFileDto);
        });
        return docFileList;
    }
}




