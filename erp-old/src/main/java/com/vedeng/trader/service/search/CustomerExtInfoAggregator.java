package com.vedeng.trader.service.search;

import cn.hutool.core.convert.Convert;
import com.google.common.collect.Maps;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.util.NumberUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.constant.InvalidReasonEnum;
import com.vedeng.erp.trader.service.VisitRecordApiService;
import com.vedeng.system.dao.SysOptionDefinitionMapper;
import com.vedeng.system.dao.VerifiesInfoMapper;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.model.VerifiesInfo;
import com.vedeng.trader.dao.CommunicateRecordMapper;
import com.vedeng.trader.dao.TraderCustomerCategoryMapper;
import com.vedeng.trader.dao.TraderCustomerMapper;
import com.vedeng.trader.model.CommunicateRecord;
import com.vedeng.trader.model.TraderCustomerCategory;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Component
public class CustomerExtInfoAggregator extends BaseServiceimpl {

    private static final Integer TRADER_TYPE_CUSTOMER = 1;
    private static final Integer CUSTOMER_TYPE_OPTION_KEY = SysOptionConstant.ID_425;
    private static final Integer CUSTOMER_NATURE_OPTION_KEY = SysOptionConstant.ID_464;

    private Map<Integer, Map<Integer, String>> optionsAboutCustomerMap;

    @Resource
    private UserMapper userMapper;
    @Resource
    private CommunicateRecordMapper communicateRecordMapper;
    @Resource
    private VerifiesInfoMapper verifiesInfoMapper;
    @Resource
    private TraderCustomerMapper traderCustomerMapper;
    @Resource
    private SysOptionDefinitionMapper sysOptionDefinitionMapper;
    @Resource
    private TraderCustomerCategoryMapper traderCustomerCategoryMapper;



    @Autowired
    private UserApiService userApiService;
    @Autowired
    private VisitRecordApiService visitRecordApiService;

    @PostConstruct
    public void initCustomerOptionsFromDb() {
        Map<Integer, Map<Integer, String>> map = Maps.newHashMap();
        map.put(CUSTOMER_NATURE_OPTION_KEY, loadOptionsByParentIdFromDb(CUSTOMER_NATURE_OPTION_KEY));
        map.put(CUSTOMER_TYPE_OPTION_KEY, loadOptionsByParentIdFromDb(CUSTOMER_TYPE_OPTION_KEY));

        optionsAboutCustomerMap = Collections.unmodifiableMap(map);
    }


    public void aggregate(List<TraderCustomerVo> customerVoList, List<Integer> userIdList, Integer customerAptitudeStatus, Integer customerStatus) {
        if (CollectionUtils.isEmpty(customerVoList)) {
            return;
        }

        List<Integer> originUserIds = new ArrayList<>();
        for (TraderCustomerVo customerVo : customerVoList) {
            customerVo.setCustomerTypeStr(findFromLocalMap(CUSTOMER_TYPE_OPTION_KEY, customerVo.getCustomerType()));
            customerVo.setCustomerNatureStr(findFromLocalMap(CUSTOMER_NATURE_OPTION_KEY, customerVo.getCustomerNature()));
            // 客户分类类型
            customerVo.setTraderCustomerCategoryNames(this.getTraderCustomerCategoryName(customerVo.getTraderCustomerCategoryId()));
            if (!Objects.isNull(customerVo.getOriginUserId())) {
                originUserIds.add(customerVo.getOriginUserId());
            }
            if (Objects.equals(customerVo.getGonghaiLevel(),1)){
                String invalidReason = customerVo.getInvalidReason();
                if (StringUtil.isNotBlank(invalidReason)){
                    List<Integer> list = Arrays.stream(invalidReason.split(",")).map(Integer::valueOf).collect(Collectors.toList());
                    customerVo.setSystemJudge(InvalidReasonEnum.getInvalidReason(list,"系统判定"));
                    customerVo.setUserJudge(InvalidReasonEnum.getInvalidReason(list,"销售标记"));
                }
                
            }
        }

        // 公海原归属销售
        Map<Integer, User> user2Map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(originUserIds)) {
            List<User> userByUserIds = userMapper.getUserByUserIds(originUserIds);
            if (CollectionUtils.isNotEmpty(userByUserIds)) {
                user2Map = userByUserIds.stream().collect(Collectors.toMap(User::getUserId, user -> user, (key1, key2) -> key2));
            }
        }

        final List<Integer> traderIdListToUse = customerVoList.stream().map(TraderCustomerVo::getTraderId).collect(Collectors.toList());
        final List<Integer> customerIdListToUse = customerVoList.stream().map(TraderCustomerVo::getTraderCustomerId).collect(Collectors.toList());

        //批量查询报价数量
        List<TraderCustomerVo> hasQuoteOrderList = traderCustomerMapper.getCustomerQuoteorderInfoByTraderCustomerVo(traderIdListToUse);
        //批量查询  交易次数\交易金额\上次交易时间\均单价\交易频次——当交易次数>1时，(客户最近一次购买时间 - 客户第一次购买时间)/（交易次数-1）
        List<TraderCustomerVo> hasSaleOrderList = traderCustomerMapper.getCustomerSaleorderInfoList(traderIdListToUse);
        //获取客户分群id
        List<TraderCustomerVo> traderGroupInfoList = traderCustomerMapper.getTraderGroupIdListByTraderId(traderIdListToUse);
        for (TraderCustomerVo customerVo : customerVoList) {
            if (user2Map != null && !user2Map.isEmpty()) {
                User user = user2Map.get(customerVo.getOriginUserId());
                if (user != null && user.getUsername() != null) {
                    customerVo.setOriginUserName(user.getUsername());
                }
            }

            customerVo.setPartners(getAndJoinCooperativePartners(customerVo.getTraderCustomerId()));
            for (TraderCustomerVo tcv2 : hasQuoteOrderList) {
                if (tcv2.getTraderId() != null && customerVo.getTraderId() != null && customerVo.getTraderId().equals(tcv2.getTraderId())) {
                    customerVo.setQuoteCount(tcv2.getQuoteCount());
                    break;
                }
            }

            for (TraderCustomerVo tcv3 : hasSaleOrderList) {
                if (tcv3.getTraderId() != null && customerVo.getTraderId() != null && customerVo.getTraderId().equals(tcv3.getTraderId())) {
                    customerVo.setBuyCount(tcv3.getBuyCount());
                    customerVo.setBuyMoney(tcv3.getBuyMoney());
                    break;
                }
            }

            for (TraderCustomerVo traderGroup : traderGroupInfoList) {
                if (customerVo.getTraderId() != null && traderGroup.getTraderId() != null && traderGroup.getTraderId().equals(customerVo.getTraderId())) {
                    String traderGroupIdStr = traderGroup.getTraderGroupIdStr();
                    if (StringUtil.isNotEmpty(traderGroupIdStr)) {
                        List<Integer> traderGroupIdList = Arrays.stream(traderGroupIdStr.split(ErpConst.Symbol.COMMA))
                                .map(Integer::valueOf).collect(Collectors.toList());
                        customerVo.setTraderGroupIdList(traderGroupIdList);
                    }
                }
            }
        }

        //如果客户列表没有客户审核状态和客户资质审核状态的筛选，那么对查询出的客户集合，补充其客户审核状态和资质审核状态
        if (customerAptitudeStatus == null) {
            Map<Integer, Integer> aptitudeStatusOfTraderCustomer =
                    verifiesInfoMapper.batchSelectVerifiesInfoByRelatedTableAndKeyAndType(customerIdListToUse, "T_CUSTOMER_APTITUDE", null)
                            .stream()
                            .collect(Collectors.toMap(VerifiesInfo::getRelateTableKey, VerifiesInfo::getStatus));
            customerVoList.parallelStream().forEach(item -> {
                if (aptitudeStatusOfTraderCustomer.containsKey(item.getTraderCustomerId())) {
                    item.setAptitudeStatus(aptitudeStatusOfTraderCustomer.get(item.getTraderCustomerId()));
                }
            });
        }

        if (customerStatus == null) {
            Map<Integer, VerifiesInfo> customerStatusList =
                    verifiesInfoMapper.batchSelectVerifiesInfoByRelatedTableAndKeyAndType(customerIdListToUse, "T_TRADER_CUSTOMER", 617)
                            .stream()
                            .collect(Collectors.toMap(VerifiesInfo::getRelateTableKey, item -> item));
            customerVoList.parallelStream().forEach(item -> {
                if (customerStatusList.containsKey(item.getTraderCustomerId())) {
                    item.setVerifyStatus(customerStatusList.get(item.getTraderCustomerId()).getStatus());
                    item.setVerifyUsername(customerStatusList.get(item.getTraderCustomerId()).getVerifyUsername());
                }
            });
        }

        //批量查询客户的沟通次数
        List<CommunicateRecord> communicateRecordList = communicateRecordMapper.getTraderCommunicateCountList(TRADER_TYPE_CUSTOMER, traderIdListToUse);
        for (TraderCustomerVo customerVo : customerVoList) {
            //取客户的沟通次数
            for (CommunicateRecord communicateRecord : communicateRecordList) {
                if (Objects.equals(customerVo.getTraderId(), communicateRecord.getTraderId())) {
                    customerVo.setCommuncateCount(communicateRecord.getCommunicateCount() == null ? 0 : communicateRecord.getCommunicateCount());
                    break;
                }
            }

            CurrentUser currentUser = CurrentUser.getCurrentUser();
            User user = userMapper.getUserByTraderId(customerVo.getTraderId(), ErpConst.ONE);
            if (user != null && CommonConstants.OFF.equals(user.getIsDisabled())) {
                customerVo.setPersonal(user.getUsername());
            }
            // 获取当前用户的所有下级销售(包含自己)
            List<Integer> allSubordinateByUserIdForVisit = userApiService.getAllSubordinateByUserIdForVisit(currentUser.getId());
            // 根据客户查看是否属于该销售查看范围
            boolean traderExists = visitRecordApiService.checkIfTraderExists(allSubordinateByUserIdForVisit, customerVo.getTraderId(),null);
            customerVo.setIsView(Convert.toInt(traderExists,0));

            if (NumberUtil.isPositive(customerVo.getAreaId())) {
                customerVo.setAddress(getAddressByAreaId(customerVo.getAreaId()));
            }
        }
    }

    /**
     * 获取战略合作伙伴
     *
     * @param customerId
     * @return
     */
    private String getAndJoinCooperativePartners(Integer customerId) {
        StringJoiner stringJoiner = new StringJoiner(" ");
        List<SysOptionDefinition> optionList = sysOptionDefinitionMapper.getList(customerId, SysOptionConstant.ID_CUSTOMER_ATTRIBUTE_CATEGORY_26);
        for (SysOptionDefinition option : optionList) {
            stringJoiner.add(option.getTitle());
        }
        return stringJoiner.toString();
    }


    private Map<Integer, String> loadOptionsByParentIdFromDb(Integer parentId) {
        List<SysOptionDefinition> options = sysOptionDefinitionMapper.getDictionaryByParentId(parentId);
        if (CollectionUtils.isEmpty(options)) {
            throw new IllegalStateException("Not found options by parentId: " + parentId);
        }

        return options.stream().collect(Collectors.toMap(SysOptionDefinition::getSysOptionDefinitionId, SysOptionDefinition::getTitle));
    }

    private String findFromLocalMap(Integer type, Integer optionId) {
        Map<Integer, String> options = optionsAboutCustomerMap.get(type);
        if (MapUtils.isEmpty(options)) {
            return null;
        }
        return options.get(optionId);
    }


    /**
     * 递归获取客户分类类型
     *
     * @param id 客户分类类型id
     * @return 从一级部门客户分类类型到当前节点的所有客户分类类型名称
     */
    private String getTraderCustomerCategoryName(Integer id) {
        TraderCustomerCategory traderCustomerCategory = traderCustomerCategoryMapper.selectAllByTraderCustomerCategoryId(id);
        if (traderCustomerCategory == null) {
            return "";
        }
        if (traderCustomerCategory.getParentId() != null) {
            String configName = " " + traderCustomerCategory.getCustomerCategoryName();
            String departmentName = getTraderCustomerCategoryName(traderCustomerCategory.getParentId());
            return departmentName + configName;
        } else {
            return "";
        }
    }

}
