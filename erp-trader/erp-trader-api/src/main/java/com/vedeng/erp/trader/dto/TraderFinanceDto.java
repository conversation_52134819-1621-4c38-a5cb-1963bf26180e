package com.vedeng.erp.trader.dto;

import lombok.Data;

/**
 * @description 交易者财务信息
 * <AUTHOR>
 * @date 2022/9/8 9:49
 **/
@Data
public class TraderFinanceDto {
    private Integer traderFinanceId;

    /**
     * 交易者ID
     */
    private Integer traderId;

    /**
     * 交易者名称
     */
    private String traderName;

    /**
     * 所属类型 1::经销商（包含终端）2:供应商
     */
    private Integer traderType;

    /**
     * 信用评估scope :
     */
    private Integer creditRating;

    /**
     * 注册地址
     */
    private String regAddress;

    /**
     * 注册电话
     */
    private String regTel;

    /**
     * 税务登记号
     */
    private String taxNum;

    /**
     * 域名
     */
    private String averageTaxpayerDomain;

    /**
     * 一般纳税人资质地址
     */
    private String averageTaxpayerUri;

    /**
     * 开户银行
     */
    private String bank;

    /**
     * 开户行支付联行号
     */
    private String bankCode;

    /**
     * 银行帐号
     */
    private String bankAccount;

    /**
     * 备注
     */
    private String comments;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 添加人
     */
    private Integer creator;

    /**
     * 最近一次编辑时间
     */
    private Long modTime;

    /**
     * 最近一次编辑人
     */
    private Integer updater;

    /**
     * OSS的唯一id
     */
    private String ossResourceId;

    /**
     * 原始的文件路径
     */
    private String originalFilepath;

    /**
     * 同步标志:0-未同步 1-同步成功 2-同步失败
     */
    private Integer synSuccess;

    /**
     * 耗时
     */
    private Long costTime;
}