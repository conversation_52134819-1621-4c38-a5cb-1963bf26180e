package com.vedeng.mobile.config;

import com.vedeng.erp.trader.service.api.TraderAddressApiServiceImpl;
import com.vedeng.erp.trader.service.api.TraderCustomerMarketingNodeApiServiceImpl;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 掌上小贝按需映入erp客户模块中的service
 * @date 2024/2/5 9:18
 */
@Configuration
@Import({
        com.vedeng.erp.trader.service.impl.TraderCustomerBaseServiceImpl.class,
        com.vedeng.erp.trader.service.impl.TraderContactServiceImpl.class,
        TraderAddressApiServiceImpl.class,
        TraderCustomerMarketingNodeApiServiceImpl.class,
        com.vedeng.erp.trader.service.impl.TraderCustomerMarketingPrincipalServiceImpl.class,
        com.vedeng.erp.trader.service.impl.TraderCustomerMarketingServiceImpl.class,
        com.vedeng.erp.trader.service.impl.TraderCustomerTagChangeRecordServiceImpl.class,


        com.vedeng.erp.trader.service.api.TraderCustomerBussinessAreaApiServiceImpl.class,
        com.vedeng.erp.trader.service.api.TraderContactApiServiceImpl.class,

        com.vedeng.erp.trader.service.api.VisitRecordApiServiceImpl.class
})
public class TraderImportConfig {
}
