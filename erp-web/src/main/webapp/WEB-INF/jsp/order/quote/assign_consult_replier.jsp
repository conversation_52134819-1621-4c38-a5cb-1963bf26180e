<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="分配处理人" scope="application" />
<%@ include file="../../common/common.jsp"%>

<div class="form-list form-tips7" style="">
    <input id="quoteorderId" value="${quoteorderId}" type="hidden" />
    <input id="quoteorderGoodsId" value="${quoteorderGoodsId}" type="hidden" />
    <form method="post" id="myform">
        <ul>

            <li>
                <div class="form-tips">
                    <span>*</span>
                    <lable>分配处理人：</lable>
                </div>
                <div class="f_left ">
                    <select id="assignUser">
                        <option value="0">请选择</option>
                        <c:if test="${not empty userList }">
                            <c:forEach items="${userList }" var="user">
                                <option value="${user.userId }">${user.username }</option>
                            </c:forEach>
                        </c:if>
                    </select>
                </div>
            </li>

        </ul>
        <div class="add-tijiao tcenter">
            <button type="button" class="bt-bg-style bg-light-green bt-small" onclick="saveConsultReplier()">确定</button>
            <button class="dele" type="button" id="close-layer">取消</button>
        </div>
    </form>
</div>

<script>
    function saveConsultReplier(){
        debugger
        if ($("#assignUser").val() == 0){
            layer.alert("请选择分配人")
            return false;
        }

        $.ajax({
            url: '/order/quote/consult/assign/save.do',
            contentType: 'application/json',
            data: JSON.stringify({
                quoteorderId: Number($("#quoteorderId").val()),
                quoteorderGoodsId: $("#quoteorderGoodsId").val(),
                consultReplier: $("#assignUser").val()
            }),
            type:"POST",
            dataType : "json",
            success:function(data)
            {
                layer.alert(data.message, {
                    icon : (data.code == 0 ? 1 : 2)
                }, function() {
                    // if (parent.layer != undefined) {
                    //     this.layer.close(index);
                    // }
                    parent.location.reload();
                    // parent&parent.updateAssignUser($("#assignUser").HTML)
                });
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        })
    }

</script>