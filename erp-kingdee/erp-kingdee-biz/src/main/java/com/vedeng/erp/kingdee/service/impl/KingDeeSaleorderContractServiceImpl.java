package com.vedeng.erp.kingdee.service.impl;

import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeeSaleorderContractCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeSaleorderContractEntity;
import com.vedeng.erp.kingdee.dto.KingDeeSaleorderContractDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSaleorderContractCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSaleorderContractConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeSaleorderContractRepository;
import com.vedeng.erp.kingdee.service.KingDeeSaleorderContractApiService;
import com.vedeng.erp.kingdee.service.KingDeeSaleorderContractService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class KingDeeSaleorderContractServiceImpl extends KingDeeBaseServiceImpl<
        KingDeeSaleorderContractEntity,
        KingDeeSaleorderContractDto,
        KingDeeSaleorderContractCommand,
        KingDeeSaleorderContractRepository,
        KingDeeSaleorderContractConvertor,
        KingDeeSaleorderContractCommandConvertor> implements KingDeeSaleorderContractService, KingDeeSaleorderContractApiService {


}
