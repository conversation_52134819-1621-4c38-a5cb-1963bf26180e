package com.vedeng.erp.trader.dto;

import com.vedeng.common.core.enums.BusinessSourceTypeEnum;
import com.vedeng.common.core.enums.BusinessTypeEnum;
import com.vedeng.common.core.enums.SupplierAssetEnum;
import lombok.*;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 供应商资产变化
 * @date 2023/11/16 20:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SupplierAssetChangeDto {


    /**
     * 资产变化数量
     */
    @NotNull
    private BigDecimal quantity;

    /**
     * 交易者id
     */
    @NotNull
    private Integer traderId;


    /**
     * 供应商id
     */
    @NotNull
    private Integer traderSupplierId;

    /**
     * 业务来源编号
     */
    @NotEmpty
    private String businessNumber;

    /**
     * 业务类型
     */
    private BusinessSourceTypeEnum businessSourceType;

    /**
     * 资产类型
     */
    private SupplierAssetEnum supplierAsset;

    /**
     * 备注
     */
    private String remark;

}
