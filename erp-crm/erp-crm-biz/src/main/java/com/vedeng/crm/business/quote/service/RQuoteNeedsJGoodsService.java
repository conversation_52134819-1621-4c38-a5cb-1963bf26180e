package com.vedeng.crm.business.quote.service;

import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.crm.business.quote.domain.entity.QuoteorderNeedsEntity;
import com.vedeng.crm.business.quote.domain.entity.RQuoteorderNeedsJGoodsEntity;

import java.util.List;

public interface RQuoteNeedsJGoodsService {

    /**
     * 根据报价商品id删除关系
     */
    void deleteByQuoteGoodsId(Integer quoteGoodsId, CurrentUser currentUser);

    int insertSelective( RQuoteorderNeedsJGoodsEntity rQuoteorderNeedsJGoodsEntity, CurrentUser currentUser);

    int updateByPrimaryKeySelective(Long id ,Long needsId, CurrentUser currentUser);

    /**
     * 一个商品id仅绑定一个需求
     * @param quoteGoodsId
     * @return
     */
    RQuoteorderNeedsJGoodsEntity selectByQuoteGoodsId(Integer quoteGoodsId);

    List<RQuoteorderNeedsJGoodsEntity> selectByQuoteorderId(Integer quoteorderId);

    void batchDeleteByQuoteorderNeedsId(List<QuoteorderNeedsEntity> extraOldList);
}
