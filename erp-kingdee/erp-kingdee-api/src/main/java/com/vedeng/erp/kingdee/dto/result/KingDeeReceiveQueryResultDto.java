package com.vedeng.erp.kingdee.dto.result;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 应收单查询结果(包括标准应收单和费用应收单)
 * @date 2022/9/22 12:31
 **/
@Data
public class KingDeeReceiveQueryResultDto {

    private String FID;
    private String F_QZOK_BDDJHID;
    private String FEntityDetail_FEntryId;
    /**
     * 计价数量 填写数量（退货负数）
     */
    private String FPriceQty;
    /**
     * 含税单价
     */
    private BigDecimal FTaxPrice;
    /**
     * 不含税金额
     */
    private String FNoTaxAmountFor_D;
    /**
     * 税额
     */
    private String FTAXAMOUNTFOR_D;
    /**
     * 价税合计
     */
    private String FALLAMOUNTFOR_D;
}
