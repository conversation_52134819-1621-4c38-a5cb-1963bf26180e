package com.vedeng.todolist.service;


import com.vedeng.authorization.model.User;
import com.vedeng.firstengage.model.FirstEngage;
import com.vedeng.firstengage.model.ProductCompany;
import com.vedeng.firstengage.model.RegistrationNumber;
import com.vedeng.goods.manufacturer.model.Manufacturer;
import com.vedeng.goods.model.CoreSkuGenerate;
import com.vedeng.goods.model.CoreSpuGenerate;
import com.vedeng.goods.model.Goods;
import com.vedeng.goods.model.vo.GoodsStorageConditionVo;
import com.vedeng.todolist.dto.QualityDepartmentTodoListDto;
import com.vedeng.todolist.dto.QualityDepartmentWarnListDto;
import com.vedeng.todolist.dto.SupplyChainTodoListDto;
import com.vedeng.todolist.model.TodoList;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @Author: daniel
 * @Date: 2020/12/15 09 25
 * @Description:
 */
public interface TodoListService {


    SupplyChainTodoListDto getSupplyChainTodoListByUser(List<Integer> userIdList);

    QualityDepartmentTodoListDto getQualityDepartmentTodoList();
    /**
     * <b>Description:</b><br>
     * 获取质量管理部工作台预警
     *
     * @param
     * @return com.vedeng.todolist.dto.QualityDepartmentTodoListDto
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/7/19 11:11
     */
    QualityDepartmentWarnListDto getQualityDepartmentWarnList();

    Map<String, Object> getRiskSkuInfo(Goods goods);

    void updateSku(CoreSkuGenerate skuGenerate, GoodsStorageConditionVo goodsStorageConditionVo);

    void updateSpu(CoreSpuGenerate spuGenerate);

    void updateFirstEngage(FirstEngage firstEngage, RegistrationNumber registrationNumber, ProductCompany productCompany, User currentUser);

    List<TodoList> getTodoListByExtraProperty(String extra, String orderNo);

    Manufacturer getManufacturerDetail(Integer id);

    void deleteByBuzTypeAndBuzExtra(Integer buzType,String buzExtra);


    /**
     * 获取crm任务数量
     */
    Integer getCrmTaskCount(@Param("userId") Integer userId);

    /**
     * 获取crm待办任务数量
     */
    Integer getCrmTodoTaskCount(@Param("userId") Integer userId);

}
