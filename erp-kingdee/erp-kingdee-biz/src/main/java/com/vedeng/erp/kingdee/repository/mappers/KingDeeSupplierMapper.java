package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeeSupplierEntity;
import com.vedeng.erp.kingdee.dto.KingDeeSupplierDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

public interface KingDeeSupplierMapper {
    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(KingDeeSupplierEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(KingDeeSupplierEntity record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    KingDeeSupplierEntity selectByPrimaryKey(Integer id);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(KingDeeSupplierEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(KingDeeSupplierEntity record);

    int updateBatchSelective(List<KingDeeSupplierEntity> list);

    int batchInsert(@Param("list") List<KingDeeSupplierEntity> list);

    /**
     * 根据erp供应商id查询
     * @param fNumber  erp供应商id
     * @return KingDeeSupplierEntity
     */
    KingDeeSupplierEntity selectByFNumber(@Param("fNumber") Integer fNumber);

    /**
     * <AUTHOR>
     * @desc 根据erp供应商ID查询金蝶供应商编码
     * @param traderSupplierId
     * @return
     */
    KingDeeSupplierDto queryInfoBySupplierId(Integer traderSupplierId);

    /**
     * 根据供应商名称查询金蝶编码
     * <AUTHOR>
     * @param traderSupperlierName
     * @return
     */
    KingDeeSupplierEntity queryInfoBySupplierName(String traderSupperlierName);
}