<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
            + path + "/";
%>
<c:set var="path" value="<%=basePath%>" scope="application" />
<link rel="stylesheet" href="<%=basePath%>static/css/general.css?rnd=${resourceVersionKey}" />
<link rel="stylesheet" href="<%=basePath%>static/css/manage.css" />
<%@ include file="../../../jsp/trader/customer/customer_tag.jsp"%>
<div id="app" style="display: none;">
    <el-card class="box-card J-block">
        <div slot="header" class="clearfix">
            <span>分享客户</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="shareDialogVisible = true" v-if="isBelongSale == 'true'">分享</el-button>
            <el-popover
                    v-if="isBelongSale == 'false'"
                    style="float: right; margin-top: -8px;"
                    placement="top-start"
                    width="200"
                    trigger="hover"
                    :content="cancelContent">
                <el-button type="text" slot="reference" disabled>分享</el-button>
            </el-popover>
        </div>
        <template>
            <el-table
                    :data="traderShareList"
                    border
                    :cell-style="{'text-align':'center'}"
                    :header-cell-style="{'text-align':'center'}"
                    style="width: 100%">
                <el-table-column
                        prop="saleUserName"
                        label="接收人英文"
                        width="180">
                </el-table-column>
                <el-table-column
                        prop="saleUserChineseName"
                        label="接收人中文"
                        width="180">
                </el-table-column>
                <el-table-column
                        prop="addTime"
                        label="分享时间">
                    <template slot-scope="scope">
                        <span>{{parseTime(scope.row.addTime)}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="creatorName"
                        label="操作人英文"
                        width="180">
                </el-table-column>
                <el-table-column
                        prop="creatorChineseName"
                        label="操作人中文"
                        width="180">
                </el-table-column>
                <el-table-column
                        label="操作">
                    <template slot-scope="scope">
                        <el-button v-if="isBelongSale == 'true'" type="text" @click="cancelShare(scope.row)">取消分享</el-button>
                        <el-popover
                                v-if="isBelongSale == 'false'"
                                placement="top-start"
                                width="200"
                                trigger="hover"
                                :content="cancelContent">
                            <el-button type="text" slot="reference" disabled>取消分享</el-button>
                        </el-popover>
                    </template>
                </el-table-column>
            </el-table>
        </template>

        <el-dialog title="分享客户" :visible.sync="shareDialogVisible" width="30%" @close="cancelSaveShare()"
                   :show-close="false" :close-on-click-modal="false" :close-on-press-escape="false">
            <el-select
                    v-model="salesJTraderDto.saleUserId"
                    ref="headUserIdRef"
                    filterable
                    remote
                    reserve-keyword
                    placeholder="输入销售英文/中文全名搜索"
                    :remote-method="searchUser"
                    style="width: 100%"
                    @change="userSelected()"
                    :loading="loading">
                <el-option
                        v-for="item in userOptionList"
                        :key="item.userId"
                        :label="item.username + '/' + item.realName"
                        :value="item.userId">
                </el-option>
            </el-select>
            <el-row v-if="showError" style="color: red">
                <span>该客户已分享过了</span>
            </el-row>
            <div slot="footer" class="dialog-footer" style="text-align: center">
                <el-button type="primary" @click="saveShare()">确 认</el-button>
                <el-button @click="shareDialogVisible = false">取 消</el-button>
            </div>
        </el-dialog>

        <el-dialog title="取消分享" :visible.sync="cancelShareDialogVisible" width="30%" @close="cancelShareDialogClose()"
                   :show-close="false" :close-on-click-modal="false" :close-on-press-escape="false">
            <span>{{this.content}}</span>
            <div slot="footer" class="dialog-footer" style="text-align: center">
                <el-button type="primary" @click="saveCancelShare()">确 认</el-button>
                <el-button @click="cancelShareDialogVisible = false">取 消</el-button>
            </div>
        </el-dialog>
    </el-card>
</div>

<script src="${pageContext.request.contextPath}/static/api/trader/traderCustomer.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/js/d3.js" charset="utf-8"></script>

<script type="text/javascript">
    const traderId = '${traderCustomer.traderId}';
    const traderCustomerId = '${traderCustomer.traderCustomerId}';
    const isBelongSale = '${isBelongSale}';
    const saleName = '${saleName}';

    new Vue({
        el: '#app',
        data() {
            return {
                traderShareList: [],
                shareDialogVisible: false,
                cancelShareDialogVisible: false,

                userOptionList: [],
                value: [],
                loading: false,

                salesJTraderDto: {
                    traderId: traderId,
                    traderCustomerId: traderCustomerId,
                    saleUserId: null,
                    saleUserName: '',
                    operateType: ''
                },
                content: '',
                globalId: 0,
                isBelongSale : isBelongSale,
                saleName: saleName,
                cancelContent: '该客户归属不是您，请联系' + saleName + '进行操作',
                // 已经分享过的销售
                sharedSaleIdList: [],
                showError: false
            }
        },

        mounted() {
            loadingApp();
        },

        created() {
            this.getShareTraderList();
        },

        methods: {
            searchUser(query) {
                if (query !== '' && query.length > 2) {
                    this.loading = true;
                    searchUser({"username" : query}).then(res => {
                        this.userOptionList = res.data.data;
                        this.loading = false;
                    })
                } else {
                    this.userOptionList = [];
                }
            },

            saveShare() {
                if (!this.showError) {
                    this.salesJTraderDto.operateType = 'insert';
                    this.salesJTraderDto.saleUserName = this.$refs.headUserIdRef.selected.label.split("/")[0];
                    saveShareTrader(this.salesJTraderDto).then(res => {
                        if (res.data.code == 0) {
                            this.$message({
                                message: '客户分享成功',
                                type: 'success'
                            });
                            this.getShareTraderList();
                        } else {
                            this.$message.error('客户分享失败');
                        }
                        this.shareDialogVisible = false;
                        this.salesJTraderDto = {
                            traderId: traderId,
                            traderCustomerId: traderCustomerId,
                            saleUserId: null,
                            saleUserName: '',
                            operateType: ''
                        };
                    })
                }
            },

            getShareTraderList() {
                getShareTraderList({"traderId" : traderId}).then(res => {
                    this.traderShareList = res.data.data;
                    // 维护一个数组存储已经分享过的销售，每当保存分享或者取消分享后，都会更新这个数组
                    this.sharedSaleIdList = res.data.data.map(item => item.saleUserId);
                })
            },

            cancelShare(row) {
                this.cancelShareDialogVisible = true;
                this.globalId = row.id;
                this.content = '确认取消"' + row.traderName + '"分享给"' + row.saleUserName + '"，取消后该用户将无法查看到相关的数据？';
            },

            saveCancelShare() {
                saveCancelShare({"id" : this.globalId}).then(res => {
                    if (res.data.code == 0) {
                        this.$message({
                            message: '取消分享成功',
                            type: 'success'
                        });
                        this.getShareTraderList();
                    } else {
                        this.$message.error('取消分享失败');
                    }
                    this.globalId = 0;
                    this.cancelShareDialogVisible = false;
                })
            },

            // 弹窗关闭事件
            cancelSaveShare() {
                this.salesJTraderDto.saleUserId = null;
                this.showError = false;
                this.userOptionList = [];
            },

            cancelShareDialogClose() {
                this.globalId = 0;
                this.content = '';
            },

            // 选择销售时校验是否已经被分享过了
            userSelected() {
                this.showError = !!this.sharedSaleIdList.includes(this.salesJTraderDto.saleUserId);
            }
        }
    })
</script>

<style>
    .el-card__header {
        height: 34px;
        background-color: #c5ddfb;
        box-sizing: border-box;
        padding-left: 15px;
        font-weight: 700;
        padding-top: 9px;
        padding-bottom: 30px;
        color: #303133;
    }

    .el-select i {
        background: none !important;
    }

    .el-dialog i {
        background: none !important;
    }
</style>