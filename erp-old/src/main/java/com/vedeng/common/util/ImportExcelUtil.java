package com.vedeng.common.util;

import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @describe Excel 上传工具类
 * @date 2020/12/12
 */
public class ImportExcelUtil {
    /**
     * 检查sheet有效行数
     *
     * @param sheet
     * @param flag  有效列
     * @return
     */
    public static int findRealRows(Sheet sheet, int... flag) {
        int row_real = 0;
        int rows = sheet.getPhysicalNumberOfRows();
        try {
            for (int i = 1; i < rows; i++) {
                Row row = sheet.getRow(i);
                int total = 0;
                ArrayList<Integer> blank = new ArrayList<Integer>();
                CellType type = CellType._NONE;
                String s = null;
                for (int j : flag) {
                    if (row.getCell(j) != null && row.getCell(j).getCellType().getCode() < 2) {
                        type = row.getCell(j).getCellType();
                        row.getCell(j).setCellType(CellType.STRING);
                    }

                    if (row.getCell(j) == null || row.getCell(j).getStringCellValue().matches("^\\s+$") || row.getCell(j).getCellType().getCode() > 2) {
                        total++;

                        if (row.getCell(j) != null && row.getCell(j).getCellType().getCode() < 2) {
                            row.getCell(j).setCellType(type);
                        }
                        blank.add(j);

                    }
                }
                // 如果4列都是空说明就该返回
                if (total == flag.length) {

                    return row_real;
                } else if (total == 0) {
                    row_real++;

                } else {
                    String h = "";
                    for (Integer b : blank) {

                        h = h + "第" + (b + 1) + "列" + " ";
                    }
                    throw new RuntimeException("第" + (i + 1) + "行" + h + "不能为空!");
                }

            }
        } catch (NullPointerException e) {
            throw new RuntimeException("excel格式异常,请检查excel格式有无数据缺失,无效数据行!");
        }
        return row_real;
    }
}
