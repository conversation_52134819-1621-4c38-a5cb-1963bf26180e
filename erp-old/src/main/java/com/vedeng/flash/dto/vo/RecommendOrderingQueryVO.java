package com.vedeng.flash.dto.vo;

import lombok.*;

/**
 * @program: erp.vedeng.com
 * @description: 推荐定品查询条件
 * @author: Pusan
 * @create: 2021-05-19 16:15
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class RecommendOrderingQueryVO {

    /** 时间范围 近一个月 :1 近半年: 6 */
    private Integer timeFrame;

    /** 占用比例 比如:80 */
    private Integer rate;

    /** 商品类型 */
    private Integer goodsType;

    /** 是否是定品标识 */
    private Integer orderingFlag;
}
