package com.wms.service.stockallocation;

import com.wms.dto.StockAllocationRequest;
import com.wms.dto.StockAllocationResult;

import java.util.List;

/**
 * 库存分配策略算法
 */
public interface StockAllocationStrategy {

    List<StockAllocationResult> stockAllocation(StockAllocationRequest stockAllocationRequest) throws Exception;

    List<StockAllocationResult> lentOutStockAllocation(StockAllocationRequest stockAllocationRequest) throws Exception;
}
