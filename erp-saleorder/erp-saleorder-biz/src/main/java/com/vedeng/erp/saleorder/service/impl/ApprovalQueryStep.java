package com.vedeng.erp.saleorder.service.impl;

import com.vedeng.erp.saleorder.constant.SaleorderWorkbenchConstant;
import com.vedeng.erp.saleorder.model.dto.BaseDataInfoDto;
import com.vedeng.erp.saleorder.model.dto.ext.ApprovalDataInfo;
import com.vedeng.erp.saleorder.service.WorkbenchQueryStep;
import com.vedeng.order.model.dto.SaleorderUserInfoDto;
import com.vedeng.system.dao.MessageMapper;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricActivityInstanceQuery;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.history.HistoricProcessInstanceQuery;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.runtime.ProcessInstanceQuery;
import org.activiti.engine.task.Task;
import org.activiti.engine.task.TaskQuery;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class ApprovalQueryStep implements WorkbenchQueryStep {

    @Autowired
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    @Autowired
    private MessageMapper messageMapper;

    @Override
    public BaseDataInfoDto doQuery(SaleorderUserInfoDto userInfoDto) {
        ApprovalDataInfo approvalDataInfo = new ApprovalDataInfo();
        ProcessInstanceQuery processQueryForRemind = processEngine.getRuntimeService().createProcessInstanceQuery();
        ProcessInstanceQuery processQueryForSubmitUnfinish = processEngine.getRuntimeService().createProcessInstanceQuery();

        // 任务相关service
        // 创建历史实例查询
        HistoricProcessInstanceQuery historicProcessInstanceQuery = processEngine.getHistoryService().createHistoricProcessInstanceQuery();


        TaskService taskService = processEngine.getTaskService();
        TaskQuery taskInstanceQueryForRemind = taskService.createTaskQuery();
        TaskQuery taskInstanceQueryForSubmitUnfinish = taskService.createTaskQuery();

        taskInstanceQueryForRemind.taskCandidateOrAssigned(userInfoDto.getUserName());
        // 用户任务
        List<Task> historicTaskInstanceList = taskInstanceQueryForRemind.list();
        Set<String> processInstanceIds = new HashSet<>();
        for (Task historicTaskInstance : historicTaskInstanceList) {
            processInstanceIds.add(historicTaskInstance.getProcessInstanceId());
        }
        List<ProcessInstance> processInstanceList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(processInstanceIds)) {
            List<String> soProcessKeyList = SaleorderWorkbenchConstant.SO_PROCESS_KEY_LIST;
            processQueryForRemind.processDefinitionKeys(new HashSet<>(soProcessKeyList));
            processQueryForRemind.processInstanceIds(processInstanceIds);

            long count = processQueryForRemind.count();
            approvalDataInfo.setRemindNum((int) count);
        }


        List<ProcessInstance> list1= new ArrayList<>();
        long count1 = historicProcessInstanceQuery.processDefinitionKeyIn(SaleorderWorkbenchConstant.SO_PROCESS_KEY_LIST).unfinished().startedBy(userInfoDto.getUserName()).count();
        approvalDataInfo.setSubmitNum((int) count1);

        Integer unMessageCount = messageMapper.getUnMessageCount(userInfoDto.getUserId());
        approvalDataInfo.setMessageNum(unMessageCount);

        return approvalDataInfo;
    }
}
