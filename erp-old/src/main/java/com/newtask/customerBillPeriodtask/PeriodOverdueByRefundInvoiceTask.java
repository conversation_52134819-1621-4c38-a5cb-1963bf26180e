package com.newtask.customerBillPeriodtask;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.finance.model.Invoice;
import com.vedeng.order.service.OrderAccountPeriodService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 退票逾期编码处理
 *
 * <AUTHOR>
 */
@Component
@JobHandler(value = "periodOverdueByRefundInvoiceTask")
public class PeriodOverdueByRefundInvoiceTask extends AbstractJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(PeriodOverdueByRefundInvoiceTask.class);

    @Autowired
    private OrderAccountPeriodService orderAccountPeriodService;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        XxlJobLogger.log("PeriodOverdueByRefundInvoiceTask-JOB, START");
        if (StringUtils.isEmpty(s)) {
            XxlJobLogger.log("补偿必须拥有相应参数");
            return FAIL;
        }
        logger.info("退票逾期编码处理开始 s:{}", s);
        orderAccountPeriodService.dealAccountPeriodOverdueCodeByRefundInvoice(JSON.parseObject(s, Invoice.class));
        return SUCCESS;
    }
}
