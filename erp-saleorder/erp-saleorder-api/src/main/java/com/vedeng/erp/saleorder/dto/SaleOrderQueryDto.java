package com.vedeng.erp.saleorder.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/6 10:50
 **/
@Setter
@Getter
public class SaleOrderQueryDto {

    /**
     * 生效状态
     */
    private Integer validStatus;

    /**
     * 订单状态
     */
    private Integer status;

    /**
     * 收款状态 0  未收款
     */
    private Integer paymentStatus;

    /**
     *  锁定状态0未锁定 1已锁定
     */
    private Integer lockedStatus;

    /**
     * 含有账期支付 0无 1有
     */
    private Integer haveAccountPeriod;


    /**
     * 订单实际金额
     */
    private BigDecimal realTotalAmount;

    /**
     * 客户名
     */
    private String traderName;

    /**
     * 生效开始时间
     */
    private Long validTimeBegin;

    /**
     * 生效结束时间
     */
    private Long validTimeEnd;

}
