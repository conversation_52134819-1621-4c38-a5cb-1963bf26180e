package com.vedeng.erp.settlement.service.impl.api;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.erp.finance.dto.BankBillDto;
import com.vedeng.erp.finance.dto.MatchSaleOrderDto;
import com.vedeng.erp.finance.service.SettlementInfoApiService;
import com.vedeng.erp.settlement.mapper.BankBillMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class SettlementInfoApiServiceImpl implements SettlementInfoApiService {

    @Autowired
    private BankBillMapper bankBillMapper;
    @Override
    public Boolean matchBankBill(BankBillDto bankBillDto) {
        BigDecimal amt = bankBillDto.getAmt();
        Integer bankBillId = bankBillDto.getBankBillId();
        String accName1 = bankBillDto.getAccName1();
        List<MatchSaleOrderDto> data = new ArrayList<>();
        String accName = null;
        data = bankBillMapper.getMatchInfo(amt, accName1, bankBillId);
        if (null == data || data.isEmpty()) {
            if (accName1.indexOf("（") > -1 || accName1.indexOf("）") > -1) {
                accName = accName1.replace("（", "(");
                accName = accName1.replace("）", ")");
                data = bankBillMapper.getMatchInfo(amt, accName, bankBillId);
                if (null == data || data.isEmpty()) {
                    if (accName1.indexOf("(") > -1 || accName1.indexOf(")") > -1) {
                        accName = accName1.replace("(", "（");
                        accName = accName1.replace(")", "）");
                        data = bankBillMapper.getMatchInfo(amt, accName, bankBillId);
                    }
                }
            }
        }
        // 根据规则排序
        data = rulesSort(data, amt, accName1);
        if (CollUtil.isEmpty(data)){
            return false;
        }
        return true;
    }

    public List<MatchSaleOrderDto> rulesSort(List<MatchSaleOrderDto> data, BigDecimal amt, String accName1) {
        List<MatchSaleOrderDto> list = new ArrayList<>();
        if (!data.isEmpty()) {
            // 根据金额和客户名称查找生效订单，按生效时间倒序
            for (MatchSaleOrderDto s : data) {
                if (amt.doubleValue() == (s.getTotalAmount().doubleValue() - s.getReceivedAmount().doubleValue())
                        && accName1.equals(s.getTraderName())) {
                    // 对公收款
                    list.add(s);
                }
            }
            // 根据金额和客户名称查找生效订单，按生效时间倒序(算上尾款)
            for (MatchSaleOrderDto s : data) {
                try {
                    if(s.getRetainageAmount()==null){
                        continue;
                    }
                    if (amt.doubleValue() == (s.getTotalAmount().doubleValue() - s.getReceivedAmount().doubleValue()
                            - s.getRetainageAmount().doubleValue()) && accName1.equals(s.getTraderName())) {
                        // 对公收款
                        list.add(s);
                    }
                }catch(Exception e){
                    log.error("",e);
                }
            }

            // 根据金额和订单联系人名称查找生效订单，按生效时间倒序
            for (MatchSaleOrderDto s : data) {
                if (amt.doubleValue() == (s.getTotalAmount().doubleValue() - s.getReceivedAmount().doubleValue())
                        && accName1.equals(s.getTraderContactName())) {
                    // 对私收款
                    list.add(s);
                }
            }

            // 根据金额和订单联系人名称查找生效订单，按生效时间倒序(算上尾款)
            for (MatchSaleOrderDto s : data) {
                if(s.getRetainageAmount()==null){
                    continue;
                }
                if (amt.doubleValue() == (s.getTotalAmount().doubleValue() - s.getReceivedAmount().doubleValue()
                        - s.getRetainageAmount().doubleValue()) && accName1.equals(s.getTraderContactName())) {
                    // 对私收款
                    list.add(s);
                }
            }
            // 根据totalAmount倒序排列
            if (!data.isEmpty()) {
                Boolean desc = false;
                if (desc) {
                    for (int i = 0; i < data.size(); i++) {
                        for (int j = 0; j < data.size() - i - 1; j++) {
                            if (data.get(j).getTotalAmount().compareTo(data.get(j + 1).getTotalAmount()) == -1) {
                                MatchSaleOrderDto r = data.get(j);
                                data.set(j, data.get(j + 1));
                                data.set(j + 1, r);
                            }
                        }
                    }
                } else {
                    for (int i = 0; i < data.size(); i++) {
                        for (int j = 0; j < data.size() - 1; j++) {
                            if (data.get(i).getTotalAmount().compareTo(data.get(j).getTotalAmount()) == 1) {
                                MatchSaleOrderDto r = data.get(i);
                                data.set(i, data.get(j));
                                data.set(j, r);
                            }
                        }
                    }
                }
            }

            // 根据金额和客户名称查找生效订单，按生效时间倒序
            for (MatchSaleOrderDto s : data) {
                if (amt.doubleValue() != (s.getTotalAmount().doubleValue() - s.getReceivedAmount().doubleValue())
                        && accName1.equals(s.getTraderName())) {
                    // 对公收款
                    list.add(s);
                }
            }
            // 根据金额和客户名称查找生效订单，按生效时间倒序(算上尾款)
            for (MatchSaleOrderDto s : data) {
                if(s.getRetainageAmount()==null){
                    continue;
                }
                if (amt.doubleValue() != (s.getTotalAmount().doubleValue() - s.getReceivedAmount().doubleValue()
                        - s.getRetainageAmount().doubleValue()) && accName1.equals(s.getTraderName())) {
                    // 对公收款
                    list.add(s);
                }
            }

            // 根据金额和订单联系人名称查找生效订单，按生效时间倒序
            for (MatchSaleOrderDto s : data) {
                if (amt.doubleValue() != (s.getTotalAmount().doubleValue() - s.getReceivedAmount().doubleValue())
                        && accName1.equals(s.getTraderContactName())) {
                    // 对私收款
                    list.add(s);
                }
            }

            // 根据金额和订单联系人名称查找生效订单，按生效时间倒序(算上尾款)
            for (MatchSaleOrderDto s : data) {
                if(s.getRetainageAmount()==null){
                    continue;
                }
                if (amt.doubleValue() != (s.getTotalAmount().doubleValue() - s.getReceivedAmount().doubleValue()
                        - s.getRetainageAmount().doubleValue()) && accName1.equals(s.getTraderContactName())) {
                    // 对私收款
                    list.add(s);
                }
            }
            // 排重
            if (null != list && !list.isEmpty()) {
                for (int i = 0; i < list.size() - 1; i++) {
                    for (int j = list.size() - 1; j > i; j--) {
                        if (list.get(j).getSaleorderId() == list.get(i).getSaleorderId()) {
                            list.remove(j);
                        }
                    }
                }
                return list;
            }

        }
        return list;
    }
}
