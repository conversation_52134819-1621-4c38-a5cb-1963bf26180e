package com.vedeng.erp.saleorder.controller;

import com.vedeng.common.core.annotation.ExcludeAuthorization;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.saleorder.service.JdSaleorderService;
import com.vedeng.erp.trader.dto.TraderDto;
import com.vedeng.erp.trader.service.TraderApiService;
import com.vedeng.order.model.Saleorder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * @title: JDSaleorderController
 * <AUTHOR>
 * @Date: 2022/12/7 9:26
 *
 *
 *
 */
@Controller
@RequestMapping("/orderstream/jdsaleorder")
@ExceptionController
@Slf4j
public class JDSaleorderController {
    @Autowired
    private JdSaleorderService jdSaleorderService;
    @Autowired
    private TraderApiService traderApiService;

    @Value("${jd.upload.tradeIdList:194723}")
    private String jdTraderIds;


    @NoNeedAccessAuthorization
    @RequestMapping("/test")
    public ModelAndView test(HttpServletRequest request, Saleorder saleOrder,
                                            ModelAndView mv){
        return null;
    }


//    @NoNeedAccessAuthorization
    @RequestMapping("/upLoadJdFile")
    public ModelAndView upLoadFile(){
        ModelAndView mv = new ModelAndView();
        mv.setViewName("vue/view/jd/upLoadFile");
        return mv;
    }

    @RequestMapping(value = "/getTraderListForImport")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<List<TraderDto>> getTraderList(){
        // 将字符串分割成数组
        String[] idsArray = jdTraderIds.split(",");
        List<Integer> traderIdList = Arrays.stream(idsArray)
                .map(Integer::parseInt)
                .collect(Collectors.toList());
        List<TraderDto> traderDtoList = traderApiService.getTraderList(traderIdList);
        return R.success(traderDtoList);
    }


    /**
     * 导入数据
     * @param file
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/import", method = RequestMethod.POST)
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<?> importExcel(@RequestPart("file") MultipartFile file, @RequestParam(required = true,name = "traderId") Integer traderId) {
        try {
            long start = System.currentTimeMillis();
            jdSaleorderService.importExcelFile(file,traderId);
            log.info("导入京东订单总耗时--cost:{}ms", (System.currentTimeMillis() - start));
        } catch (ServiceException e) {
            log.error("上传失败，请检查文件",e);
            throw new ServiceException(e.getMessage());
        } catch (Exception e) {
            log.error("上传失败，请检查文件",e);
            throw new ServiceException("上传失败，请检查文件");
        }
        return R.success();
    }



}
