package com.wms.service.chain.step;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.flash.enums.WmsSendOrderTypeEnum;
import com.vedeng.trader.dao.TraderMapper;
import com.wms.constant.LogicalEnum;
import com.wms.constant.WMSContant;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.constant.WmsOutputOrderTypeConstant;
import com.wms.dao.WmsLogicalOrdergoodsMapper;
import com.wms.dao.WmsOutputOrderGoodsMapper;
import com.wms.dao.WmsOutputOrderMapper;
import com.wms.dao.WmsSendOrderMapper;
import com.wms.dto.PutSaleOrderDto;
import com.wms.dto.PutSaleOrderGoodsDto;
import com.wms.dto.WmsResponse;
import com.wms.model.dto.WmsOutputOrderGoodsDto;
import com.wms.model.po.WmsOutputOrder;
import com.wms.model.po.WmsOutputOrderGoods;
import com.wms.model.po.WmsSendOrder;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.WmsInterface;
import com.wms.service.WmsInventoryOutService;
import com.wms.service.chain.AbstractHandlerStep;
import com.wms.service.chain.HandlerStepContext;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 盘亏单出库 下午发wms
 * <AUTHOR>
 */
@Service
@Slf4j
public class InventoryOutWmsRequestHandler extends AbstractHandlerStep {

    // 盘库出库默认的出库人地址等信息
    public static final String CONSIGNEE_ID = "169873";
    public static final String CONSIGNEE_NAME = "南京BF（仓库）";
    public static final String PROVINCE = "江苏省";
    public static final String CITY = "南京市";
    public static final String DISTRICT = "栖霞区";
    public static final String CONSIGNEE_ADDRESS_1 = "经天路6号中电熊猫物流园区4#库二层";
    public static final String CONSIGNEE_CONTACT = "Judi";
    public static final String CONSIGNEE_TEL_1 = "13057686452";
    @Resource
    private WMSInterfaceFactory wmsInterfaceFactory;


    @Autowired
    private WmsOutputOrderGoodsMapper wmsOutputOrderGoodsMapper;

    @Autowired
    private WmsOutputOrderMapper wmsOutputOrderMapper;

    @Autowired
    private WmsSendOrderMapper wmsSendOrderMapper;



    @Override
    protected void doDealWith(HandlerStepContext context) throws Exception {

        Long inventoryOutOrderId = context.get("inventoryOutOrderId");

        WmsOutputOrder wmsOutputOrder = wmsOutputOrderMapper.selectByPrimaryKey(inventoryOutOrderId);
        InventoryOutWmsRequestHandler bean = SpringUtil.getBean(InventoryOutWmsRequestHandler.class);
        Integer wmsSendOrderId = bean.saveWmsSendOrder(wmsOutputOrder);
        PutSaleOrderDto putSaleOrderDto = new PutSaleOrderDto();
        putSaleOrderDto.setDocNo(wmsOutputOrder.getOrderNo());
        if(wmsOutputOrder.getType().equals(WmsOutputOrderTypeConstant.INVENTORY_OUT)){
            //  hollis 待产品和wms确认
            putSaleOrderDto.setOrderType(WmsInterfaceOrderType.INVENTORY_OUT);
        }else {
            throw new Exception("盘亏出库单业务类型为空 单号:"+wmsOutputOrder.getOrderNo());
        }

        putSaleOrderDto.setOrderTime(wmsOutputOrder.getAddTime());
        putSaleOrderDto.setExpectedShipmentTime1(wmsOutputOrder.getAppleOutDate());
        putSaleOrderDto.setSoReferenceA(wmsOutputOrder.getApplyerDepartment());
        putSaleOrderDto.setSoReferenceB(wmsOutputOrder.getApplyer());
        putSaleOrderDto.setConsigneeId("0");
        //
        putSaleOrderDto.setHedi07("A");
        putSaleOrderDto.setNotes(wmsOutputOrder.getRemark());
        putSaleOrderDto.setConsigneeId(CONSIGNEE_ID);
        putSaleOrderDto.setConsigneeName(CONSIGNEE_NAME);
        putSaleOrderDto.setConsigneeProvince(PROVINCE);
        putSaleOrderDto.setConsigneeCity(CITY);
        putSaleOrderDto.setConsigneeDistrict(DISTRICT);

        putSaleOrderDto.setConsigneeAddress1(CONSIGNEE_ADDRESS_1);
        putSaleOrderDto.setConsigneeContact(CONSIGNEE_CONTACT);
        putSaleOrderDto.setConsigneeTel1(CONSIGNEE_TEL_1);

        List<PutSaleOrderGoodsDto> details = new ArrayList<>();
        List<WmsOutputOrderGoodsDto> wmsOutputOrderGoods = wmsOutputOrderGoodsMapper.queryOutputGoodsByWmsOutPutOrderId(inventoryOutOrderId);
        for (WmsOutputOrderGoods wmsOutputOrderGood : wmsOutputOrderGoods) {
            PutSaleOrderGoodsDto putSaleOrderGoodsDto = new PutSaleOrderGoodsDto();
            putSaleOrderGoodsDto.setSku(wmsOutputOrderGood.getSkuNo());
            putSaleOrderGoodsDto.setQtyOrdered(wmsOutputOrderGood.getOutputNum());
            putSaleOrderGoodsDto.setLotAtt08(LogicalEnum.getLogicalWarehouseCode(wmsOutputOrderGood.getLogicalWarehouseId()));
            putSaleOrderGoodsDto.setDedi07(wmsOutputOrderGood.getId().toString());
            details.add(putSaleOrderGoodsDto);
        }
        putSaleOrderDto.setDetails(details);

        try {
            log.info("WMS盘亏出库单下传的 单号:{},请求:{}",wmsOutputOrder.getOrderNo(), JSON.toJSONString(putSaleOrderDto));
            log.info("WMS盘亏出库单下传的 单号:{},请求:{}",wmsOutputOrder.getOrderNo(), JSON.toJSONString(putSaleOrderDto));
            WmsInterface putSaleOrderOutputInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_ORIGINAL_SALESORDER);

            WmsResponse response = putSaleOrderOutputInterface.request(putSaleOrderDto);
            //请求失败 补偿
            if("1".equals(response.getReturnFlag())){
                WmsSendOrder updateWmsSendOrder = new WmsSendOrder();
                updateWmsSendOrder.setWmsSendOrderId(wmsSendOrderId);
                updateWmsSendOrder.setSendStatus(ErpConst.ONE);
                wmsSendOrderMapper.updateByPrimaryKeySelective(updateWmsSendOrder);
            }
            log.info("WMS盘亏出库单下传的 单号:{},响应:{}",wmsOutputOrder.getOrderNo(),JSON.toJSONString(response));
            //log.info("WMS盘亏出库单下传的 单号:{},响应:{}",wmsOutputOrder.getOrderNo(),JSON.toJSONString(response));
        } catch (Exception e) {
            log.error("下发盘亏出库单失败error 单号:"+wmsOutputOrder.getOrderNo(),e);
            //XxlJobLogger.log("下发盘亏出库单失败error 单号:"+wmsOutputOrder.getOrderNo(),e);
        }


    }

    @Transactional(propagation= Propagation.REQUIRES_NEW)
    public Integer saveWmsSendOrder(WmsOutputOrder wmsOutputOrder) {
        WmsSendOrder wmsSendOrder = new WmsSendOrder();
        try {
            wmsSendOrder.setOrderType(WmsSendOrderTypeEnum.INVENTORY_OUT.getCode());
            Long id = wmsOutputOrder.getId();
            wmsSendOrder.setOrderId(Math.toIntExact(id));
            wmsSendOrder.setOrderNo(wmsOutputOrder.getOrderNo());
            WmsSendOrder oldInfo = wmsSendOrderMapper.getWmsSendOrderInfo(wmsSendOrder);
            if(oldInfo != null){
                WmsSendOrder update = new WmsSendOrder();
                update.setSendStatus(0);
                update.setWmsSendOrderId(oldInfo.getWmsSendOrderId());
                wmsSendOrderMapper.updateByPrimaryKeySelective(update);
                return oldInfo.getWmsSendOrderId();
            }
            Integer userId = CurrentUser.getCurrentUser().getId();
            wmsSendOrder.setCreator(userId);
            wmsSendOrder.setUpdater(userId);
            wmsSendOrder.setSendStatus(0);
            wmsSendOrderMapper.insertSelective(wmsSendOrder);
        } catch (Exception e) {
            log.error("saveWmsSendOrder error，下发盘亏出库单：{}",wmsOutputOrder.getOrderNo(),e);
            //XxlJobLogger.log("saveWmsSendOrder error，下发盘亏出库单：{}",wmsOutputOrder.getOrderNo(),e);
        }
        return wmsSendOrder.getWmsSendOrderId();
    }

}

