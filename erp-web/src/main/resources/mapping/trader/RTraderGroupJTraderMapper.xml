<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.trader.dao.RTraderGroupJTraderMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.trader.model.RTraderGroupJTrader">
    <id column="R_TRADER_GROUP_J_TRADER" jdbcType="BIGINT" property="rTraderGroupJTrader" />
    <result column="TRADER_GROUP_ID" jdbcType="INTEGER" property="traderGroupId" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
  </resultMap>
  <sql id="Base_Column_List">
    R_TRADER_GROUP_J_TRADER, TRADER_GROUP_ID, TRADER_ID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_R_TRADER_GROUP_J_TRADER
    where R_TRADER_GROUP_J_TRADER = #{rTraderGroupJTrader,jdbcType=BIGINT}
  </select>

  <select id="getTraderIdsListPage" parameterType="map" resultType="java.lang.Integer">
    select
    TRADER_ID
    from T_R_TRADER_GROUP_J_TRADER
    where TRADER_GROUP_ID = #{groupId,jdbcType=BIGINT}
  </select>

  <select id="getTraderGroupById" parameterType="java.lang.Integer" resultType="com.vedeng.trader.model.RTraderGroupJTrader">
    select
    <include refid="Base_Column_List" />
    from T_R_TRADER_GROUP_J_TRADER
    where 1=1
    <if test="traderGroupId != null">
        AND TRADER_GROUP_ID = #{traderGroupId}
    </if>
    <if test="traderIds != null">
      AND TRADER_ID IN
      <foreach collection="traderIds" index="i" item="traderId" open="(" separator="," close=")">
        #{traderId}
      </foreach>
    </if>
  </select>


  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from T_R_TRADER_GROUP_J_TRADER
    where R_TRADER_GROUP_J_TRADER = #{rTraderGroupJTrader,jdbcType=BIGINT}
  </delete>

  <delete id="deleteByGroupIdAndTraderIds" parameterType="java.lang.Long">
    delete from T_R_TRADER_GROUP_J_TRADER
    where TRADER_GROUP_ID = #{groupId}
    <if test="traderIds!=null">
      AND TRADER_ID IN
      <foreach collection="traderIds" item="id" separator="," open="(" close=")">
          #{id}
      </foreach>
    </if>
  </delete>
  <insert id="insert" keyColumn="R_TRADER_GROUP_J_TRADER" keyProperty="rTraderGroupJTrader" parameterType="com.vedeng.trader.model.RTraderGroupJTrader" useGeneratedKeys="true">
    insert into T_R_TRADER_GROUP_J_TRADER (TRADER_GROUP_ID, TRADER_ID)
    values (#{traderGroupId,jdbcType=INTEGER}, #{traderId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="R_TRADER_GROUP_J_TRADER" keyProperty="rTraderGroupJTrader" parameterType="com.vedeng.trader.model.RTraderGroupJTrader" useGeneratedKeys="true">
    insert into T_R_TRADER_GROUP_J_TRADER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="traderGroupId != null">
        TRADER_GROUP_ID,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="traderGroupId != null">
        #{traderGroupId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.trader.model.RTraderGroupJTrader">
    update T_R_TRADER_GROUP_J_TRADER
    <set>
      <if test="traderGroupId != null">
        TRADER_GROUP_ID = #{traderGroupId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
    </set>
    where R_TRADER_GROUP_J_TRADER = #{rTraderGroupJTrader,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.trader.model.RTraderGroupJTrader">
    update T_R_TRADER_GROUP_J_TRADER
    set TRADER_GROUP_ID = #{traderGroupId,jdbcType=INTEGER},
      TRADER_ID = #{traderId,jdbcType=INTEGER}
    where R_TRADER_GROUP_J_TRADER = #{rTraderGroupJTrader,jdbcType=BIGINT}
  </update>
</mapper>