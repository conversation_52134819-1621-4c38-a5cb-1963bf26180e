package com.vedeng.erp.kingdee.batch.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class BatchWmsOutputOrderGoodsDto {
    private Long id;

    /**
    * 出库单的id
    */
    private Long wmsOutputOrderId;

    /**
     * 商品id
     */
    private Integer goodsId;

    /**
    * sku编号
    */
    private String skuNo;

    /**
    * 出库数量
    */
    private Integer outputNum;

    /**
    * 已经出库数量
    */
    private Integer alreadyOutputNum;

    /**
    * 最后一次出库时间
    */
    private String lastOutputTime;

    /**
    * 出库状态:0未出库 1:部分出库 2.已出库
    */
    private Integer outStatus;

    /**
    * 已经入库数量
    */
    private Integer alreadyInputNum;

    /**
    * 最后一次入库时间
    */
    private String lastInputTime;

    /**
    * 入库状态: 0未入库 1:部分入库 2:已入库
    */
    private Integer inputStatus;

    /**
    * 预计归还时间
    */
    private String expectReturnedTime;

    /**
    * 创建时间
    */
    private String addTime;

    /**
    * 修改时间
    */
    private String updateTime;

    /**
    * 是否删除:0-未删除 1-已删除
    */
    private Integer isDelete;

    /**
    * 逻辑仓id
    */
    private Integer logicalWarehouseId;
}