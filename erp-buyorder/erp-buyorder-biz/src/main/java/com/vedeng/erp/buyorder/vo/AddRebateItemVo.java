package com.vedeng.erp.buyorder.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class AddRebateItemVo {
   //采购id
   private Integer buyorderId;
   //客户id
   private Integer traderId;
   //采购明细id
   private Integer buyorderGoodsId;
   //可用返利总额
   private String validRebateCharge;
   //已用返利金额
   private BigDecimal usedRebateCharge;
   //单价
   private String price;
   //数量
   private Integer num;
   //商品id
   private String goodsId;
   //明细返利总额
   private String totalRebate;
}
