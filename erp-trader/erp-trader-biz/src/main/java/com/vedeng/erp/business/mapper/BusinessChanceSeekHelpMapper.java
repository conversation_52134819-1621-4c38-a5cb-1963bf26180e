package com.vedeng.erp.business.mapper;

import com.vedeng.erp.business.domain.dto.ConsultationPendingRequestDto;
import com.vedeng.erp.business.domain.dto.ConsultationPendingResponseDto;
import com.vedeng.erp.business.domain.entity.BusinessChanceSeekHelp;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BusinessChanceSeekHelpMapper {
    int deleteByPrimaryKey(Long businessChanceSeekHelpId);

    int insert(BusinessChanceSeekHelp record);

    int insertOrUpdate(BusinessChanceSeekHelp record);

    int insertOrUpdateSelective(BusinessChanceSeekHelp record);

    int insertSelective(BusinessChanceSeekHelp record);

    BusinessChanceSeekHelp selectByPrimaryKey(Long businessChanceSeekHelpId);

    int updateByPrimaryKeySelective(BusinessChanceSeekHelp record);

    int updateByPrimaryKey(BusinessChanceSeekHelp record);

    int updateBatch(List<BusinessChanceSeekHelp> list);

    int updateBatchSelective(List<BusinessChanceSeekHelp> list);

    int batchInsert(@Param("list") List<BusinessChanceSeekHelp> list);

    Integer countConsultationPendingByUserId(@Param("userId") Integer userId);

    List<ConsultationPendingResponseDto> getConsultationPendingInfo(@Param("param") ConsultationPendingRequestDto param);
    
    int updateStatusByBusinessChanceSeekHelpId(@Param("updatedStatus")Integer updatedStatus,@Param("businessChanceSeekHelpId")Long businessChanceSeekHelpId);
    
}