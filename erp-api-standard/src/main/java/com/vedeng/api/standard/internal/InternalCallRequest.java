package com.vedeng.api.standard.internal;

import com.vedeng.authorization.model.User;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 内部调用请求封装类
 * 用于封装内部HTTP调用的请求信息
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-18
 */
public class InternalCallRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 目标Controller的Bean名称
     */
    private String controllerBeanName;
    
    /**
     * 目标方法名称
     */
    private String methodName;

    /**
     * HTTP方法（GET、POST等）
     */
    private String httpMethod;
    
    /**
     * 请求参数
     */
    private Map<String, Object> parameters;
    
    /**
     * 请求头
     */
    private Map<String, String> headers;
    
    /**
     * 当前用户
     */
    private User currentUser;
    
    /**
     * 方法参数类型（用于反射调用）
     */
    private Class<?>[] parameterTypes;

    /**
     * 自定义参数对象数组（用于传递复杂对象参数）
     */
    private Object[] customParameters;

    /**
     * 是否需要会话支持
     */
    private boolean needSession;
    
    /**
     * 超时时间（毫秒）
     */
    private long timeout;
    
    /**
     * 默认构造函数
     */
    public InternalCallRequest() {
        this.parameters = new HashMap<>();
        this.headers = new HashMap<>();
        this.httpMethod = "POST";
        this.needSession = true;
        this.timeout = 30000; // 默认30秒超时
        this.parameterTypes = new Class<?>[]{javax.servlet.http.HttpServletRequest.class, javax.servlet.http.HttpServletResponse.class};
    }
    
    /**
     * 构造函数
     *
     * @param controllerBeanName Controller Bean名称
     * @param methodName 方法名称
     */
    public InternalCallRequest(String controllerBeanName, String methodName) {
        this();
        this.controllerBeanName = controllerBeanName;
        this.methodName = methodName;
    }
    
    /**
     * 静态工厂方法 - 创建POST请求
     *
     * @param controllerBeanName Controller Bean名称
     * @param methodName 方法名称
     * @return 内部调用请求
     */
    public static InternalCallRequest post(String controllerBeanName, String methodName) {
        return new InternalCallRequest(controllerBeanName, methodName);
    }

    /**
     * 静态工厂方法 - 创建GET请求
     *
     * @param controllerBeanName Controller Bean名称
     * @param methodName 方法名称
     * @return 内部调用请求
     */
    public static InternalCallRequest get(String controllerBeanName, String methodName) {
        InternalCallRequest request = new InternalCallRequest(controllerBeanName, methodName);
        request.setHttpMethod("GET");
        return request;
    }
    
    /**
     * 添加请求参数
     * 
     * @param key 参数名
     * @param value 参数值
     * @return 当前对象（支持链式调用）
     */
    public InternalCallRequest addParameter(String key, Object value) {
        this.parameters.put(key, value);
        return this;
    }
    
    /**
     * 批量添加请求参数
     * 
     * @param params 参数Map
     * @return 当前对象（支持链式调用）
     */
    public InternalCallRequest addParameters(Map<String, Object> params) {
        if (params != null) {
            this.parameters.putAll(params);
        }
        return this;
    }
    
    /**
     * 添加请求头
     * 
     * @param key 头名称
     * @param value 头值
     * @return 当前对象（支持链式调用）
     */
    public InternalCallRequest addHeader(String key, String value) {
        this.headers.put(key, value);
        return this;
    }
    
    /**
     * 设置当前用户
     * 
     * @param user 用户对象
     * @return 当前对象（支持链式调用）
     */
    public InternalCallRequest withUser(User user) {
        this.currentUser = user;
        return this;
    }
    
    /**
     * 设置方法参数类型
     *
     * @param types 参数类型数组
     * @return 当前对象（支持链式调用）
     */
    public InternalCallRequest withParameterTypes(Class<?>... types) {
        this.parameterTypes = types;
        return this;
    }

    /**
     * 设置自定义参数对象
     *
     * @param customParams 自定义参数对象数组
     * @return 当前对象（支持链式调用）
     */
    public InternalCallRequest withCustomParameters(Object... customParams) {
        this.customParameters = customParams;
        return this;
    }
    
    /**
     * 设置超时时间
     * 
     * @param timeoutMs 超时时间（毫秒）
     * @return 当前对象（支持链式调用）
     */
    public InternalCallRequest withTimeout(long timeoutMs) {
        this.timeout = timeoutMs;
        return this;
    }
    
    /**
     * 设置是否需要会话
     * 
     * @param needSession 是否需要会话
     * @return 当前对象（支持链式调用）
     */
    public InternalCallRequest withSession(boolean needSession) {
        this.needSession = needSession;
        return this;
    }
    
    // Getter and Setter methods
    public String getControllerBeanName() {
        return controllerBeanName;
    }
    
    public void setControllerBeanName(String controllerBeanName) {
        this.controllerBeanName = controllerBeanName;
    }
    
    public String getMethodName() {
        return methodName;
    }
    
    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }
    
    public String getHttpMethod() {
        return httpMethod;
    }
    
    public void setHttpMethod(String httpMethod) {
        this.httpMethod = httpMethod;
    }
    
    public Map<String, Object> getParameters() {
        return parameters;
    }
    
    public void setParameters(Map<String, Object> parameters) {
        this.parameters = parameters;
    }
    
    public Map<String, String> getHeaders() {
        return headers;
    }
    
    public void setHeaders(Map<String, String> headers) {
        this.headers = headers;
    }
    
    public User getCurrentUser() {
        return currentUser;
    }
    
    public void setCurrentUser(User currentUser) {
        this.currentUser = currentUser;
    }
    
    public Class<?>[] getParameterTypes() {
        return parameterTypes;
    }
    
    public void setParameterTypes(Class<?>[] parameterTypes) {
        this.parameterTypes = parameterTypes;
    }
    
    public boolean isNeedSession() {
        return needSession;
    }
    
    public void setNeedSession(boolean needSession) {
        this.needSession = needSession;
    }
    
    public long getTimeout() {
        return timeout;
    }
    
    public void setTimeout(long timeout) {
        this.timeout = timeout;
    }

    public Object[] getCustomParameters() {
        return customParameters;
    }

    public void setCustomParameters(Object[] customParameters) {
        this.customParameters = customParameters;
    }
    
    @Override
    public String toString() {
        return "InternalCallRequest{" +
                "controllerBeanName='" + controllerBeanName + '\'' +
                ", methodName='" + methodName + '\'' +
                ", httpMethod='" + httpMethod + '\'' +
                ", parametersCount=" + (parameters != null ? parameters.size() : 0) +
                ", currentUser=" + (currentUser != null ? currentUser.getUsername() : "null") +
                ", needSession=" + needSession +
                ", timeout=" + timeout +
                '}';
    }
}
