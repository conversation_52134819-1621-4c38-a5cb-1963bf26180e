<%--
  Created by IntelliJ IDEA.
  User: vedeng
  Date: 2022/11/17
  Time: 15:27
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="商品领用详情" scope="application"/>
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript"></script>
<div class="content">
    <div id="desc_div">
        <form method="post" id="viewForm">
            <ul class="payplan">
                <div class="parts">

                    <div class="title-container title-container-blue">
                        <div class="table-title nobor">
                            基本信息
                        </div>
                    </div>
                    <table class="table table-bordered table-striped table-condensed table-centered">
                        <tbody>
                        <tr>
                            <td class="table-smaller">领用单号</td>
                            <td>${receiveOut.orderNo}</td>
                            <td class="table-smaller">创建人</td>
                            <td>${receiveOut.creator}</td>
                        </tr>
                        <tr>
                            <td>创建人部门</td>
                            <td>${receiveOut.belongDepartment}</td>
                            <td>创建时间</td>
                            <td>${receiveOut.addTime}</td>
                        </tr>
                        <tr>
                            <td>审核状态</td>
                            <td>
                                <c:choose>
                                    <c:when test="${receiveOut.verifyStatus eq 0}">
                                        审核中
                                    </c:when>
                                    <c:when test="${receiveOut.verifyStatus eq 1}">
                                        审核中
                                    </c:when>
                                    <c:when test="${receiveOut.verifyStatus eq 2}">
                                        审核通过
                                    </c:when>
                                    <c:when test="${receiveOut.verifyStatus eq 3}">
                                        审核不通过
                                    </c:when>
                                    <c:when test="${receiveOut.verifyStatus eq 4}">
                                        已关闭
                                    </c:when>
                                </c:choose>
                            </td>
                            <td>领用状态</td>
                            <td>
                                <c:choose>
                                <c:when test="${alreadyOutNum eq 0}">
                                未领用
                                </c:when>
                                <c:when test="${alreadyOutNum < outNum}">
                                部分领用
                                </c:when>
                                <c:when test="${alreadyOutNum eq outNum}">
                                全部领用
                                </c:when>
                                </c:choose>
                        </tr>
                        <tr>
                            <td>物流备注</td>
                            <td colspan="3">${receiveOut.logisticCommnet}</td>
                        </tr>
                    </table>


                    <li>
                        <div class="parts">
                            <div class="title-container title-container-blue">
                                <div class="table-title nobor">产品信息</div>
                            </div>
                            <table class="table  table-bordered table-striped table-condensed table-centered">
                                <thead>
                                <tr>
                                    <th style="width:80px">序号</th>
                                    <th style="width:80px">订货号</th>
                                    <th style="width:80px">产品名称</th>
                                    <th style="width:80px">品牌</th>
                                    <th style="width:80px">型号</th>
                                    <th style="width:80px">数量</th>
                                </tr>
                                </thead>
                                <tbody id="thisTimeUpdateTbody">
                                <c:forEach var="receiveOutGood" items="${receiveOutGoodList}" varStatus="staut">
                                    <tr>
                                        <td>${staut.count}</td>
                                        <td>${receiveOutGood.skuNo}</td>
                                        <td>${receiveOutGood.showName}</td>
                                        <td>${receiveOutGood.brandName}</td>
                                        <td>${receiveOutGood.model}</td>
                                        <td>${receiveOutGood.outputNum}</td>
                                    </tr>
                                </c:forEach>
                                </tbody>
                            </table>
                        </div>
                    </li>


                    <li>
                        <div class="parts">
                            <div class="title-container title-container-blue">
                                <div class="table-title nobor">出库记录</div>
                            </div>
                            <table class="table  table-bordered table-striped table-condensed table-centered">
                                <thead>
                                <tr>
                                    <th style="width:80px">出库单据</th>
                                    <th class="wid5">序号</th>
                                    <th class="wid10">订货号</th>
                                    <th>产品名称</th>
                                    <th>品牌</th>
                                    <th>型号</th>
                                    <th>实际出库数量</th>
                                    <th class="wid4">单位</th>
                                    <th>贝登批次码</th>
                                    <th>生产日期</th>
                                    <th>有效期至</th>
                                    <th>出库时间</th>
                                    <th>入库日期</th>
                                    <th>生产批号</th>
                                    <th>SN码</th>
                                    <th>灭菌编号</th>
                                    <th class="wid12">注册证号</th>
                                </tr>
                                </thead>
                                <tbody>
                                <c:forEach var="wlist" items="${wlogList}" varStatus="num1">
                                    <tr>
                                        <td>
                                            <a class="addtitle"
                                               href="javascript:void(0);"
                                               tabTitle='{ "link":"./warehouse/warehousesout/detail.do?outInNo=${wlist.outInNo}&outInType=${wlist.operateType}","title":"出库单详情页"}'>${wlist.outInNo}</a>
                                        </td>
                                        <td>${num1.count}</td>
                                        <td>${newSkuInfosMap[wlist.skuId].SKU_NO}</td>
                                        <td class="text-left">
                                            <a class="addtitle" href="javascript:void(0);"
                                               tabTitle='{"num":"viewgoods${wlist.skuId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${wlist.skuId}","title":"产品信息"}'>${newSkuInfosMap[wlist.skuId].SHOW_NAME}</a>
                                        </td>
                                        <td>${newSkuInfosMap[wlist.skuId].BRAND_NAME}</td>
                                        <td>${newSkuInfosMap[wlist.skuId].MODEL}</td>
                                        <td>${wlist.num}</td>
                                        <td>${newSkuInfosMap[wlist.skuId].UNIT_NAME}</td>
                                        <td>${ wlist.vedengBatchNumber}</td>
                                        <td><date:date value="${wlist.productDate}" format="yyyy-MM-dd HH:mm:ss"/></td>
                                        <td><date:date value="${wlist.expirationDate}" format="yyyy-MM-dd HH:mm:ss"/></td>
                                        <td>${wlist.addTimeStr}</td>
                                        <td><date:date value="${wlist.checkStatusTime}" format="yyyy-MM-dd HH:mm:ss"/></td>
                                        <td>${ wlist.batchNumber}</td>
                                        <td>${ wlist.barcodeFactory}</td>
                                        <td>${ wlist.sterilizationBatchNumber}</td>
                                        <td class="text-left">
                                            <a class="addtitle" href="javascript:void(0);"
                                               tabTitle='{"num":"viewfirstgoods${newSkuInfosMap[wlist.skuId].FIRST_ENGAGE_ID}","link":"./firstengage/baseinfo/getFirstSearchDetail.do?firstEngageId=${newSkuInfosMap[wlist.skuId].FIRST_ENGAGE_ID}","title":"首营信息"}'>${newSkuInfosMap[wlist.skuId].REGISTRATION_NUMBER}</a>
                                        </td>
                                    </tr>
                                </c:forEach>
                                <!-- 查询无结果弹出 -->
                                <c:if test="${empty wlogList}">
                                    <tr>
                                        <td colspan="17">暂无出库记录。</td>
                                    </tr>
                                </c:if>
                                </tbody>
                            </table>
                        </div>
                    </li>


                    <li>
                        <div class="parts">
                            <div class="title-container title-container-blue">
                                <div class="table-title nobor ">审核记录</div>
                            </div>
                            <table class="table  table-bordered table-striped table-condensed table-centered">
                                <thead>
                                <tr>
                                    <th style="width:50px">操作人</th>
                                    <th style="width:80px">操作时间</th>
                                    <th style="width:80px">操作事项</th>
                                    <th style="width:80px">备注</th>
                                </tr>
                                </thead>
                                <tbody>
                                <c:forEach var="hi" items="${historicActivityInstance}" varStatus="status">
                                    <c:if test="${not empty  hi.activityName}">
                                        <tr>
                                            <td>
                                                <c:choose>
                                                    <c:when test="${hi.activityType == 'startEvent'}">
                                                        ${startUser}
                                                    </c:when>
                                                    <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                                    </c:when>
                                                    <c:otherwise>
                                                        <c:if test="${historicActivityInstance.size() == status.count}">
                                                            <c:forEach var="vs" items="${verifyUsersList}"
                                                                       varStatus="status">
                                                                <c:if test="${fn:contains(verifyUserList, vs)}">
                                                                    <span class="font-green">${vs}</span>&nbsp;
                                                                </c:if>
                                                                <c:if test="${!fn:contains(verifyUserList, vs)}">
                                                                    <span>${vs}</span>&nbsp;
                                                                </c:if>
                                                            </c:forEach>

                                                            <c:if test="${empty verifyUsersList && empty hi.assignee}">
                                                                ${verifyUsers}
                                                            </c:if>
                                                        </c:if>
                                                        <c:if test="${historicActivityInstance.size() != status.count}">
                                                            ${hi.assignee}
                                                        </c:if>
                                                    </c:otherwise>
                                                </c:choose>


                                            </td>
                                            <td><fmt:formatDate value="${hi.endTime}"
                                                                pattern="yyyy-MM-dd HH:mm:ss"/></td>
                                            <td>
                                                <c:choose>
                                                    <c:when test="${hi.activityType == 'startEvent'}">
                                                        开始
                                                    </c:when>
                                                    <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                                        结束
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${hi.activityName}
                                                    </c:otherwise>
                                                </c:choose>
                                            </td>
                                            <td class="font-red">${commentMap[hi.taskId]}</td>
                                        </tr>
                                    </c:if>
                                </c:forEach>
                                <!-- 查询无结果弹出 -->

                                <c:if test="${empty historicActivityInstance}">
                                    <!-- 查询无结果弹出 -->
                                    <tr>
                                        <td colspan="4">暂无审核记录。</td>
                                    </tr>
                                </c:if>
                                </tbody>
                            </table>
                        </div>
                    </li>
                    <div class="add-tijiao tcenter mt10">
                        <c:if test="${null!=historicInfo.taskInfo and (taskInfo.assignee == curr_user.username or historicInfo.candidateUserMap['belong']) }">
                            <button type="button" class="bt-bg-style bg-light-green bt-small mr10 pop-new-data"
                                    layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./auditResult.do?taskId=${taskId}&receiveOutId=${receiveOut.id}&pass=true"}'>
                                审核通过
                            </button>
                            <button type="button" class="bt-bg-style bg-light-orange bt-small mr10 pop-new-data"
                                    layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./auditResult.do?taskId=${taskId}&receiveOutId=${receiveOut.id}&pass=false"}'>
                                审核不通过
                            </button>
                        </c:if>
                    </div>

                </div>
            </ul>
        </form>
    </div>
</div>