package com.vedeng.mobile.goods.controller;

import com.vedeng.common.core.base.R;
import com.vedeng.goods.dto.CoreSkuDetailTabsQueryAllDto;
import com.vedeng.goods.dto.CoreSkuQueryDetailDto;
import com.vedeng.goods.dto.SearchGoodsDto;
import com.vedeng.goods.query.CoreSkuDetailTabsQuery;
import com.vedeng.goods.query.CoreSkuDetailQuery;
import com.vedeng.goods.query.SearchGoodsQuery;
import com.vedeng.goods.service.CoreSkuDetailTabsService;
import com.vedeng.goods.service.CoreSkuService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/mobile/sku")
@RequiredArgsConstructor
public class CoreSkuController {

    private final CoreSkuService coreSkuService;

    private final CoreSkuDetailTabsService coreSkuDetailTabsService;


    /**
     * 商品详情Tabs
     *
     * @param coreSkuDetailTabsQuery 参数
     * @return CoreSkuQueryDetailDto
     */
    @PostMapping(value = "/tabs")
    public R<List<CoreSkuDetailTabsQueryAllDto>> queryAllTabs(@RequestBody CoreSkuDetailTabsQuery coreSkuDetailTabsQuery) {
        return R.success(coreSkuDetailTabsService.queryAllTabs(coreSkuDetailTabsQuery));
    }

    /**
     * 搜索基本商品信息
     *
     * @param serachGoodsQuery 参数
     * @return CoreSkuSearchListDto
     */
    @PostMapping(value = "/search")
    public R<SearchGoodsDto> searchCoreSkuList(@RequestBody SearchGoodsQuery serachGoodsQuery) {
        return R.success(coreSkuService.searchCoreSkuList(serachGoodsQuery));
    }

    /**
     * 搜索基本商品信息数量
     *
     * @param serachGoodsQuery 参数
     * @return CoreSkuSearchListDto
     */
    @PostMapping(value = "/searchCount")
    public R<SearchGoodsDto> searchCoreSkuCount(@RequestBody SearchGoodsQuery serachGoodsQuery) {
        serachGoodsQuery.setCountFlag(true);
        return R.success(coreSkuService.searchCoreSkuList(serachGoodsQuery));
    }

    /**
     * 查询商品信息详情
     *
     * @param coreSkuDetailQuery 参数
     * @return CoreSkuQueryDetailDto
     */
    @PostMapping(value = "/queryDetail")
    public R<CoreSkuQueryDetailDto> queryCoreSkuDetail(@RequestBody CoreSkuDetailQuery coreSkuDetailQuery) {
        return R.success(coreSkuService.queryCoreSkuDetail(coreSkuDetailQuery));
    }


}
