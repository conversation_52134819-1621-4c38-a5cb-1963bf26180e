package com.vedeng.erp.trader.mapper;

import com.vedeng.erp.trader.domain.dto.TraderCustomerFinanceExcelDto;
import com.vedeng.erp.trader.domain.dto.TraderFinanceDetail;
import com.vedeng.erp.trader.domain.entity.TraderCustomerFinance;

import java.util.List;

public interface TraderCustomerFinanceExtMapper extends  TraderCustomerFinanceMapper{

    /**
     * 根据traderCustomerFinanceId 查客户回显使用
     * @param traderCustomerFinanceId
     * @return
     */
    TraderFinanceDetail getTraderFinancialById(Integer traderCustomerFinanceId);

    /**
     * 批量新增客户
     * @param list
     */
    void batchInsert(List<TraderCustomerFinanceExcelDto> list);


    int updateBatch(List<TraderCustomerFinanceExcelDto> list);

    int updateBatchSelective(List<TraderCustomerFinanceExcelDto> list);

    /**
     * 根据客户id查找
     * @param traderId
     * @return
     */
    TraderCustomerFinance getTraderFinancialByTraderId(Integer traderId);
    
    /**
     * 批量初始化导入
     */
    void batchInsertSelect(List<Integer> traderIdList);

    /**
     * 查询ID是否存在
     * @param traderIdList
     * @return
     */
	List<Integer> selectByTraderIds(List<Integer> traderIdList);

}