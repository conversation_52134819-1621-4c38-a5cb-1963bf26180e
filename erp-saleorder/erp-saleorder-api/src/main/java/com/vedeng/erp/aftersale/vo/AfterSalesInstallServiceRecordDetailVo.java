package com.vedeng.erp.aftersale.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.aftersale.vo
 * @Date 2022/5/12 15:31
 */
@Getter
@Setter
public class AfterSalesInstallServiceRecordDetailVo {

    /**
     * AFTER_SALES_SERVICE_DETAIL_ID
     **/
    private Integer afterSalesServiceDetailId;

    /**
     * 售后安调服务记录主档ID  AFTER_SALES_SERVICE_ID
     **/
    private Integer afterSalesServiceId;

    /**
     * sku  SKU
     **/
    private String sku;

    /**
     * 产品名称  SKU_NAME
     **/
    private String skuName;

    /**
     * 品牌  BAND
     **/
    private String brand;

    /**
     * 型号  MODEL
     **/
    private String model;

    /**
     * 本次服务数量  NUM
     **/
    private Integer num;

    /**
     * SN码  SERIAL_NUMBER
     **/
    private String serialNumber;

    /**
     * 创建时间  ADD_TIME
     **/
    private Date addTime;

    /**
     * 创建者  CREATOR
     **/
    private Integer creator;

    /**
     * 更新时间  MOD_TIME
     **/
    private Date modTime;

    /**
     * 更新者  UPDATER
     **/
    private Integer updater;

    /**
     * 是否删除 0否 1是  IS_DELETE
     **/
    private Integer isDelete;

    /**
     * 售后单商品id  AFTER_SALES_GOODS_ID
     **/
    private Integer afterSalesGoodsId;

    /**
     * 补充码
     * VDERP-14089 新添加字段
     */
    private String supplCode;

}
