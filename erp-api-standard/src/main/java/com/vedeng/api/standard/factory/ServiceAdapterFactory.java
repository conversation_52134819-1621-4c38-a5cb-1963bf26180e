package com.vedeng.api.standard.factory;

import com.vedeng.api.standard.core.ServiceAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * 服务适配器工厂
 * 负责管理和获取各种业务模块的服务适配器
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-18
 */
@Component
public class ServiceAdapterFactory {
    
    private static final Logger logger = LoggerFactory.getLogger(ServiceAdapterFactory.class);
    
    @Autowired
    private ApplicationContext applicationContext;
    
    /**
     * 服务适配器缓存
     * key: 模块名称, value: 服务适配器实例
     */
    private final Map<String, ServiceAdapter> adapterCache = new HashMap<>();
    
    /**
     * 初始化方法，扫描并注册所有服务适配器
     */
    @PostConstruct
    public void init() {
        logger.info("开始初始化服务适配器工厂");
        
        // 获取所有ServiceAdapter实现类
        Map<String, ServiceAdapter> adapters = applicationContext.getBeansOfType(ServiceAdapter.class);
        
        for (Map.Entry<String, ServiceAdapter> entry : adapters.entrySet()) {
            String beanName = entry.getKey();
            ServiceAdapter adapter = entry.getValue();
            
            // 获取模块名称
            String moduleName = getModuleName(beanName, adapter);
            
            // 注册适配器
            registerAdapter(moduleName, adapter);
            
            logger.info("注册服务适配器: module={}, beanName={}, class={}", 
                moduleName, beanName, adapter.getClass().getSimpleName());
        }
        
        logger.info("服务适配器工厂初始化完成，共注册{}个适配器", adapterCache.size());
    }
    
    /**
     * 获取服务适配器
     * 
     * @param moduleName 模块名称
     * @return 服务适配器实例，如果不存在则返回null
     */
    public ServiceAdapter getServiceAdapter(String moduleName) {
        if (moduleName == null || moduleName.trim().isEmpty()) {
            logger.warn("模块名称为空，无法获取服务适配器");
            return null;
        }
        
        String normalizedModuleName = moduleName.toLowerCase().trim();
        ServiceAdapter adapter = adapterCache.get(normalizedModuleName);
        
        if (adapter == null) {
            logger.warn("未找到模块的服务适配器: {}", moduleName);
        }
        
        return adapter;
    }
    
    /**
     * 注册服务适配器
     * 
     * @param moduleName 模块名称
     * @param adapter 服务适配器实例
     */
    public void registerAdapter(String moduleName, ServiceAdapter adapter) {
        if (moduleName == null || moduleName.trim().isEmpty()) {
            logger.warn("模块名称为空，跳过注册");
            return;
        }
        
        if (adapter == null) {
            logger.warn("服务适配器为空，跳过注册");
            return;
        }
        
        String normalizedModuleName = moduleName.toLowerCase().trim();
        
        // 检查是否已存在
        if (adapterCache.containsKey(normalizedModuleName)) {
            logger.warn("模块{}的服务适配器已存在，将被覆盖", moduleName);
        }
        
        adapterCache.put(normalizedModuleName, adapter);
        logger.debug("成功注册服务适配器: module={}", normalizedModuleName);
    }
    
    /**
     * 移除服务适配器
     * 
     * @param moduleName 模块名称
     * @return 被移除的适配器实例，如果不存在则返回null
     */
    public ServiceAdapter removeAdapter(String moduleName) {
        if (moduleName == null || moduleName.trim().isEmpty()) {
            return null;
        }
        
        String normalizedModuleName = moduleName.toLowerCase().trim();
        ServiceAdapter removed = adapterCache.remove(normalizedModuleName);
        
        if (removed != null) {
            logger.info("移除服务适配器: module={}", normalizedModuleName);
        }
        
        return removed;
    }
    
    /**
     * 获取所有已注册的模块名称
     * 
     * @return 模块名称集合
     */
    public String[] getRegisteredModules() {
        return adapterCache.keySet().toArray(new String[0]);
    }
    
    /**
     * 检查模块是否已注册
     * 
     * @param moduleName 模块名称
     * @return 是否已注册
     */
    public boolean isModuleRegistered(String moduleName) {
        if (moduleName == null || moduleName.trim().isEmpty()) {
            return false;
        }
        
        String normalizedModuleName = moduleName.toLowerCase().trim();
        return adapterCache.containsKey(normalizedModuleName);
    }
    
    /**
     * 获取已注册适配器的数量
     * 
     * @return 适配器数量
     */
    public int getAdapterCount() {
        return adapterCache.size();
    }
    
    /**
     * 从Bean名称或适配器实例中提取模块名称
     * 
     * @param beanName Bean名称
     * @param adapter 适配器实例
     * @return 模块名称
     */
    private String getModuleName(String beanName, ServiceAdapter adapter) {
        // 首先尝试从适配器实例获取模块名称
        try {
            String moduleName = adapter.getModuleName();
            if (moduleName != null && !moduleName.trim().isEmpty()) {
                return moduleName.toLowerCase().trim();
            }
        } catch (Exception e) {
            logger.debug("从适配器实例获取模块名称失败: {}", e.getMessage());
        }
        
        // 从Bean名称中提取模块名称
        if (beanName != null && !beanName.trim().isEmpty()) {
            String name = beanName.toLowerCase();
            
            // 移除常见的后缀
            if (name.endsWith("serviceadapter")) {
                name = name.substring(0, name.length() - "serviceadapter".length());
            } else if (name.endsWith("adapter")) {
                name = name.substring(0, name.length() - "adapter".length());
            } else if (name.endsWith("service")) {
                name = name.substring(0, name.length() - "service".length());
            }
            
            return name.trim();
        }
        
        // 最后从类名中提取
        String className = adapter.getClass().getSimpleName();
        if (className.endsWith("ServiceAdapter")) {
            return className.substring(0, className.length() - "ServiceAdapter".length()).toLowerCase();
        } else if (className.endsWith("Adapter")) {
            return className.substring(0, className.length() - "Adapter".length()).toLowerCase();
        }
        
        return className.toLowerCase();
    }
    
    /**
     * 获取适配器的详细信息（用于调试和监控）
     * 
     * @return 适配器信息Map
     */
    public Map<String, Object> getAdapterInfo() {
        Map<String, Object> info = new HashMap<>();
        
        for (Map.Entry<String, ServiceAdapter> entry : adapterCache.entrySet()) {
            String moduleName = entry.getKey();
            ServiceAdapter adapter = entry.getValue();
            
            Map<String, Object> adapterInfo = new HashMap<>();
            adapterInfo.put("className", adapter.getClass().getName());
            adapterInfo.put("supportedActions", adapter.getSupportedActions());
            
            info.put(moduleName, adapterInfo);
        }
        
        return info;
    }
}
