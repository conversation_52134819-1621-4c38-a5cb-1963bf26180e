<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wms.dao.WmsIdempotentCheckMapper" >

  <resultMap id="BaseResultMap" type="com.wms.model.po.WmsIdempotentCheck" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="business_key" property="businessKey" jdbcType="VARCHAR" />
    <result column="consume_result" property="consumeResult" jdbcType="INTEGER" />
    <result column="message_body" property="messageBody" jdbcType="LONGVARCHAR" />
    <result column="add_time" property="addTime" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="Base_Column_List" >
    id, business_key, consume_result, message_body, add_time, update_time
  </sql>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from T_WMS_IDEMPOTENT_CHECK
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByBusinessKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from T_WMS_IDEMPOTENT_CHECK
    where business_key = #{businessKey,jdbcType=VARCHAR}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from T_WMS_IDEMPOTENT_CHECK
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insertSelective" parameterType="com.wms.model.po.WmsIdempotentCheck" useGeneratedKeys="true" keyProperty="id">
    insert into T_WMS_IDEMPOTENT_CHECK
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="businessKey != null" >
        business_key,
      </if>
      <if test="consumeResult != null" >
        consume_result,
      </if>
      <if test="messageBody != null" >
        message_body,
      </if>
      <if test="addTime != null" >
        add_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="businessKey != null" >
        #{businessKey,jdbcType=VARCHAR},
      </if>
      <if test="consumeResult != null" >
        #{consumeResult,jdbcType=INTEGER},
      </if>
      <if test="messageBody != null" >
        #{messageBody,jdbcType=LONGVARCHAR},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.wms.model.po.WmsIdempotentCheck" >
    update T_WMS_IDEMPOTENT_CHECK
    <set >
      <if test="businessKey != null and businessKey != ''" >
        business_key = #{businessKey,jdbcType=VARCHAR},
      </if>
      <if test="consumeResult != null" >
        consume_result = #{consumeResult,jdbcType=INTEGER},
      </if>
      <if test="messageBody != null and messageBody != ''" >
        message_body = #{messageBody,jdbcType=LONGVARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

</mapper>