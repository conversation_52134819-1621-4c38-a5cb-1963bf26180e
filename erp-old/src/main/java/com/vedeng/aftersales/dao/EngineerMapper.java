package com.vedeng.aftersales.dao;

import com.vedeng.aftersales.model.Engineer;
import com.vedeng.aftersales.model.vo.EngineerVo;

import javax.inject.Named;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Named("engineerMapper")
public interface EngineerMapper {
    /**
     * 主键检索工程师
     *
     * @param engineerId
     * @return
     */
    Engineer select<PERSON>y<PERSON>rimary<PERSON><PERSON>(Integer engineerId);

    /**
     * 搜索工程师信息
     * @param map
     * @return
     */
    List<EngineerVo> getEngineerVoListPage(Map<String, Object> map);

    /**
     * <b>Description:</b><br> 根据主键更新工程师
     * @param engineer
     * @return
     * @Note
     * <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年9月25日 上午11:13:39
     */
    int updateBy<PERSON><PERSON>ry<PERSON><PERSON>(Engineer engineer);

    /**
     * 分页查询售后安调公司列表
     * @param map
     * @return
     */
    List<EngineerVo> queryCompanyListPage(Map<String, Object> map);
}
