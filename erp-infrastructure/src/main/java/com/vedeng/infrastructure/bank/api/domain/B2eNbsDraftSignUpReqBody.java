package com.vedeng.infrastructure.bank.api.domain;

import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/10/11
 */
@Data
public class B2eNbsDraftSignUpReqBody {

    /**
     * 客户基数请求流水号
     */
    private String trnId;
    /**
     * 签约账号
     */
    private String custAccount;

    /**
     * 客户业务请求流水号
     */
    private String insId;

    private List<BillSignMap> list;


    /**
     * 持有流水号和票据流水号
     */
    @Data
    public static class BillSignMap {

        /**
         * 交易流水号
         */
        private String transId;

        /**
         * 交易类型:
         * 01:提示承兑签收 （承兑申请签 收）
         * 02:提示收票签收 （出票交付签 收）
         * 03:背书转让签收
         * 04:质押签收（质 押申请签收）
         * 05:质押解除签收
         * 06:提示付款签收 （委托收款申请 签收）
         * 07:保证签收（保 证申请签收）
         * 08:同意清偿申请 签收
         * 09:不可转让撤销 签收（不得转让 撤销申请签收）
         */
        private String transCode;

        /**
         * 签收标识
         * SU00：同意签收
         * SU01：拒绝签收
         */
        private String signFlag;

        /**
         * 回复人备注
         */
        private String remark;

        /**
         * 拒绝原因: CP01:背书签章未 依次前后衔接
         * CP02:背书记载不 清晰
         * CP03:背书人签章 缺少单位印章、 法定代表人或其 授权的代理人签 章
         * CP04:背书粘单未 加盖骑缝章、骑 缝章不连续或骑 缝章不清
         * CP05:背书不规 范、文字有歧义
         * CP06:其他
         * CP07:自动拒付
         * CP08:与自己有直 接债权债务关系 的持票人未履行 约定义务
         * CP09:持票人以欺 诈、偷盗或者胁 迫等手段取得票 据
         * CP10:持票人明知 有欺诈、偷盗或 者胁迫等情形， 出于恶意取得票 据
         * CP11:持票人明知 债务人与出票人 或者持票人的前 手之间存在抗辩 事由而取得票据
         * CP12:持票人因重 大过失取得不符 合《票据法》规 定的票据
         * CP13:被法院冻结 或收到法院止付 通知书
         * CP14:票据未到期
         * CP15:商业承兑汇 票承兑人账户余 额不足
         * CP16:承兑人应答 同意，扣款确认 行未应答
         * CP17承:兑人应答 同意，扣款确认 行拒绝
         */
        private String dshnrCd;

        /**
         * 保证人地址
         */
        private String guaranteeAdr;





    }

}
