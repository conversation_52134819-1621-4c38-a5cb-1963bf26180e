package com.test.kingdee;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.domain.command.KingDeePurchaseBackCommand;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.repository.mappers.KingDeePurchaseBackMapper;
import com.vedeng.erp.kingdee.mapstruct.KingDeePurchaseBackCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeePurchaseBackConvertor;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * 应付单：采购退料单
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring.xml", "classpath:spring-mybatis.xml"})
public class TestKingDeePurchaseBack {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private KingDeePurchaseBackCommandConvertor commandConvertor;

    @Autowired
    private KingDeePurchaseBackConvertor kingDeePurchaseBackConvertor;

    @Autowired
    private KingDeePurchaseBackMapper kingDeePurchaseBackMapper;


    @Test
    public void kingDeePurchaseBack() {
        //一级dto
        KingDeePurchaseBackDto kingDeePurchaseBackDto = new KingDeePurchaseBackDto();
        kingDeePurchaseBackDto.setFId("0");
        kingDeePurchaseBackDto.setFBillTypeId("TLD01_SYS");
        kingDeePurchaseBackDto.setFBillNo("");
        kingDeePurchaseBackDto.setFQzokBddjtId("贝登erp对应的单据头ID");
        kingDeePurchaseBackDto.setFDate("2022-09-17");
        kingDeePurchaseBackDto.setFMrType("B");
        kingDeePurchaseBackDto.setFMrMode("B");
        kingDeePurchaseBackDto.setFStockOrgId("101");
        kingDeePurchaseBackDto.setFSupplierId("ven0006");
        //二级dto
        List<KingDeePurchaseBackDetailDto> kingDeePayCommonDetailDtoList = new ArrayList<>();
        KingDeePurchaseBackDetailDto  kingDeePurchaseBackDetailDto = new KingDeePurchaseBackDetailDto();
        kingDeePurchaseBackDetailDto.setFMaterialId("sku0001");
        kingDeePurchaseBackDetailDto.setFRmrealqty("10.0");
        kingDeePurchaseBackDetailDto.setFStockId("ck9999");
        kingDeePurchaseBackDetailDto.setFTaxPrice("113");
        kingDeePurchaseBackDetailDto.setFEntryTaxRate("13.00");
        kingDeePurchaseBackDetailDto.setFNote("备注");
        kingDeePurchaseBackDetailDto.setF_qzok_ysddh("原始订单号");
        kingDeePurchaseBackDetailDto.setF_qzok_gsywdh("归属业务单号");
        kingDeePurchaseBackDetailDto.setF_qzok_ywlx("业务类型");
        kingDeePurchaseBackDetailDto.setF_qzok_pch("批次号");
        kingDeePurchaseBackDetailDto.setF_qzok_xlh("序列号");
        kingDeePurchaseBackDetailDto.setF_qzok_sqlx("授权类型");
        kingDeePurchaseBackDetailDto.setF_qzok_sfzf("是否直发");
        kingDeePurchaseBackDetailDto.setF_qzok_bddjhid("贝登订单行ID");
        kingDeePurchaseBackDetailDto.setFsourcetype("STK_InStock");
        kingDeePayCommonDetailDtoList.add(kingDeePurchaseBackDetailDto);
        //三级dto
        List<KingDeePurchaseBackDetailLinkDto> kingDeePurchaseBackDetailLinkDtoList = new ArrayList<>();
        KingDeePurchaseBackDetailLinkDto kingDeePurchaseBackDetailLinkDto = new KingDeePurchaseBackDetailLinkDto();
        kingDeePurchaseBackDetailLinkDto.setFLinkId("0");
        kingDeePurchaseBackDetailLinkDto.setFpurmrbentry_link_fruleid ("STK_InStock-PUR_MRB");
        kingDeePurchaseBackDetailLinkDto.setFpurmrbentry_link_fflowlineid ("0");
        kingDeePurchaseBackDetailLinkDto.setFpurmrbentry_link_fstableid ("0");
        kingDeePurchaseBackDetailLinkDto.setFpurmrbentry_link_fstablename ("T_STK_INSTOCKENTRY");
        kingDeePurchaseBackDetailLinkDto.setFpurmrbentry_link_fsbillid ("100042");
        kingDeePurchaseBackDetailLinkDto.setFpurmrbentry_link_fsid ("100052");
        kingDeePurchaseBackDetailLinkDto.setFpurmrbentry_link_fbasicunitqtyold ("-10");
        kingDeePurchaseBackDetailLinkDto.setFpurmrbentry_link_fbasicunitqty ("-10");
        kingDeePurchaseBackDetailLinkDto.setFpurmrbentry_link_fcarrybaseqtyold ("-10");
        kingDeePurchaseBackDetailLinkDto.setFpurmrbentry_link_fcarrybaseqty ("-10");

        kingDeePurchaseBackDetailLinkDtoList.add(kingDeePurchaseBackDetailLinkDto);
//        kingDeePurchaseBackDetailDto.setFpurmrbentry_link(kingDeePurchaseBackDetailLinkDtoList);
        kingDeePurchaseBackDto.setFpurmrbentry(kingDeePayCommonDetailDtoList);

        KingDeePurchaseBackCommand command = commandConvertor.toCommand(kingDeePurchaseBackDto);

        System.out.println("dto入参："+JSON.toJSONString(kingDeePurchaseBackDto));
        System.out.println("入参："+JSON.toJSONString(command));
   kingDeeBaseApi.save(new SaveExtCommand<>(command, kingDeePurchaseBackDto.getFormId()));

    }
}
