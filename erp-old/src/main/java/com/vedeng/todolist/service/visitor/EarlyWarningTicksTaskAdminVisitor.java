package com.vedeng.todolist.service.visitor;

import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.Organization;
import com.vedeng.flash.dao.EarlyWarningTaskMapper;
import com.vedeng.todolist.dto.EarlyWarningTicksTaskTodoDto;
import com.vedeng.todolist.dto.SupplyChainAdminTodoDtoBuild;
import com.wms.service.context.ThreadLocalContext;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2021/5/21 9:22
 */
@Service
public class EarlyWarningTicksTaskAdminVisitor implements TodoVisitor<SupplyChainAdminTodoDtoBuild>{

    @Resource
    private EarlyWarningTaskMapper earlyWarningTaskMapper;

    @Resource
    private UserMapper userMapper;

    private Map<Integer,List<Integer>> userIdByOrgaIdMap = new HashedMap();

    @Override
    public void visitor(SupplyChainAdminTodoDtoBuild supplyChainAdminTodoDtoBuild) {

        List<Organization> orgaList =  ThreadLocalContext.get("orgaList");

        Set<Integer> userIdSet = new HashSet<>();

        orgaList.stream().forEach(organization -> {
            List<Integer> userIdList = userMapper.getUserIdListByOrgId(organization.getOrgId());
            userIdSet.addAll(userIdList);
            userIdByOrgaIdMap.put(organization.getOrgId(),userIdList);
        });

        List<Integer> userIdAllList = new ArrayList<>(userIdSet);
        EarlyWarningTicksTaskTodoDto earlyWarningTicksTaskTodoDto = earlyWarningTaskMapper.getEarlyWarningTicksTaskTodoDtoByuserIds(userIdAllList);
        earlyWarningTicksTaskTodoDto.setUserIdList(userIdAllList);
        supplyChainAdminTodoDtoBuild.get().setEarlyWarningTicksTaskTodoDto(earlyWarningTicksTaskTodoDto);
    }
}
