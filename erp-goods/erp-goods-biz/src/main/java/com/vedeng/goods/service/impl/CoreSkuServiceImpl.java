package com.vedeng.goods.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.comparator.CompareUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.core.base.BaseResponseCode;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.aftersale.dto.AfterSaleServiceStandardInfoDto;
import com.vedeng.erp.buyorder.dto.BuyOrderDto;
import com.vedeng.erp.saleorder.dto.SaleOrderGoodsImageDto;
import com.vedeng.erp.saleorder.service.SaleOrderDataApiService;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.enums.TraderNatureEnum;
import com.vedeng.goods.common.constant.ErpGoodsConstant;
import com.vedeng.goods.domain.dto.SkuImageDto;
import com.vedeng.goods.domain.entity.GoodsAttachmentEntity;
import com.vedeng.goods.dto.*;
import com.vedeng.goods.domain.dto.GoodsDto;
import com.vedeng.goods.domain.dto.SkuAttrDto;
import com.vedeng.goods.feign.buyorder.RemoteBuyorderInfoApiService;
import com.vedeng.goods.feign.price.RemotePriceApiService;
import com.vedeng.goods.feign.saleorder.RemoteAftersaleInfoApiService;
import com.vedeng.goods.mapper.CoreSkuMapper;
import com.vedeng.goods.mapper.SkuAttrMappingGenerateMapper;
import com.vedeng.goods.query.CoreSkuDetailQuery;
import com.vedeng.goods.query.SearchGoodsQuery;
import com.vedeng.goods.service.CoreSkuApiService;
import com.vedeng.goods.service.CoreSkuService;
import com.vedeng.price.api.price.dto.ResultInfo;
import com.vedeng.price.api.price.dto.price.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.vedeng.common.core.base.R.SUCCESS_CODE;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CoreSkuServiceImpl implements CoreSkuService, CoreSkuApiService {

    @Autowired
    private CoreSkuMapper coreSkuMapper;

    @Autowired
    private SkuAttrMappingGenerateMapper skuAttrMappingGenerateMapper;

    @Autowired
    private RemotePriceApiService remotePriceApiService;

    @Autowired
    private RemoteAftersaleInfoApiService remoteAftersaleInfoApiService;

    @Autowired
    private RemoteBuyorderInfoApiService remoteBuyorderInfoApiService;

    @Autowired
    private UserApiService userApiService;

    @Override
    public SearchGoodsDto searchCoreSkuList(SearchGoodsQuery serachGoodsQuery) {

        SearchGoodsDto searchGoodsDto = new SearchGoodsDto();

        //查询商品基本信息
        List<GoodsDto> searchGoodsDtos = coreSkuMapper.listGoodsInfoForSerachGoodsQuery(serachGoodsQuery);
        searchGoodsDto.setTotal(searchGoodsDtos.size());
        searchGoodsDto.setTotalStr(getSkuCountStr(searchGoodsDto.getTotal()));
        if (serachGoodsQuery.getCountFlag()) {
            return searchGoodsDto;
        }
        searchGoodsDto.setPageNumber(serachGoodsQuery.getPageNumber());
        searchGoodsDto.setPageSize(serachGoodsQuery.getPageSize());

        //数据处理
        if (!CollectionUtils.isEmpty(searchGoodsDtos)) {

            //分页商品信息
            int[] pages = PageUtil.transToStartEnd(Convert.toInt(serachGoodsQuery.getPageNumber()) - 1, Convert.toInt(serachGoodsQuery.getPageSize()));
            List<GoodsDto> searchGoodsDtosSub = CollUtil.sub(searchGoodsDtos, pages[0], pages[1]);
            List<SearchGoodsDto.QueryGoodsInfo> queryGoodsInfoList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(searchGoodsDtosSub)) {
                searchGoodsDtosSub.forEach(item -> {
                    SearchGoodsDto.QueryGoodsInfo queryGoodsInfo =
                            SearchGoodsDto.QueryGoodsInfo.builder().skuId(item.getSkuId()).sku(item.getSku()).skuName(item.getSkuName())
                                    .brandName(item.getBrandName()).modelOrSpec(item.getModelOrSpec()).terminalPrice(item.getTerminalPrice())
                                    .distributionPrice(item.getDistributionPrice()).unitName(item.getUnitName()).availableStockNum(item.getAvailableStockNum()).build();
                    queryGoodsInfoList.add(queryGoodsInfo);
                });
            }
            searchGoodsDto.setQueryGoodsInfoList(queryGoodsInfoList);

            //根据搜索参数品牌列表
            getBrandListFromGoodsDtos(searchGoodsDtos, searchGoodsDto);
            //根据搜索参数分类列表
            getCategoryListFromGoodsDtos(searchGoodsDtos, searchGoodsDto);

        }

        return searchGoodsDto;
    }


    @Override
    public CoreSkuQueryDetailDto queryCoreSkuDetail(CoreSkuDetailQuery coreSkuDetailQuery) {

        CoreSkuQueryDetailDto coreSkuQueryDetailDto = new CoreSkuQueryDetailDto();

        //组装商品基础信息
        CoreSkuDto coreSkuBo = coreSkuMapper.getCoreSkuInfoBySkuId(coreSkuDetailQuery.getSkuId());
        CoreSkuQueryDetailDto.BaseInfo baseInfo = CoreSkuQueryDetailDto.BaseInfo.builder()
                .skuName(coreSkuBo.getSkuName())
                .sku(coreSkuBo.getSku())
                .brandName(coreSkuBo.getBrandName())
                .spec(coreSkuBo.getSpec())
                .model(coreSkuBo.getModel())
                .unitName(coreSkuBo.getUnitName())
                .minOrder(coreSkuBo.getMinOrder())
                .attributes(getGoodsAttribute(coreSkuBo.getSku()))
                .manufacturerModel(ErpGoodsConstant.SPU_TYPE_DEVICE.equals(coreSkuBo.getSpuType()) || ErpGoodsConstant.SPU_TYPE_REPLACEMENT.equals(coreSkuBo.getSpuType()) ? coreSkuBo.getModel() : null)
                .build();
        coreSkuQueryDetailDto.setBaseInfo(baseInfo);

        //组装库存信息
        CoreSkuQueryDetailDto.Inventory inventory = CoreSkuQueryDetailDto.Inventory.builder()
                .availableStockNum(coreSkuBo.getAvailableStockNum())
                .stockNum(coreSkuBo.getStockNum())
                .occupyNum(coreSkuBo.getOrderOccupyNum())
                .actionLockNum(coreSkuBo.getActionLockNum())
                .onWayData(getGoodsOnWayInfo(coreSkuBo.getSkuId()))
                .purchaseTime(coreSkuBo.getPurchaseTime())
                .build();
        coreSkuQueryDetailDto.setInventory(inventory);

        //组装核价信息
        SkuPriceInfoDetailRequestDto skuPriceInfoDetailRequestDto = new SkuPriceInfoDetailRequestDto();
        skuPriceInfoDetailRequestDto.setSkuNo(coreSkuBo.getSku());
        SkuPriceInfoDetailResponseDto skuCheckPriceInfo = getSkuCheckPriceInfo(skuPriceInfoDetailRequestDto);

        if (Objects.nonNull(skuCheckPriceInfo)) {
            CoreSkuQueryDetailDto.CheckPriceInfo checkPriceInfo = CoreSkuQueryDetailDto.CheckPriceInfo.builder()
                    .isNeedReport(coreSkuBo.getIsNeedReport())
                    .marketPrice(skuCheckPriceInfo.getMarketPrice())
                    .terminalPrice(skuCheckPriceInfo.getTerminalPrice())
                    .distributionPrice(skuCheckPriceInfo.getDistributionPrice())
                    .build();
            coreSkuQueryDetailDto.setCheckPriceInfo(checkPriceInfo);
        }
        if (Objects.isNull(skuCheckPriceInfo)){
            CoreSkuQueryDetailDto.CheckPriceInfo checkPriceInfo = CoreSkuQueryDetailDto.CheckPriceInfo.builder()
                    .isNeedReport(coreSkuBo.getIsNeedReport()).build();
            coreSkuQueryDetailDto.setCheckPriceInfo(checkPriceInfo);
        }


        //组装售后政策
        AfterSaleServiceStandardInfoDto effectAfterSalePolicy = getEffectAfterSalePolicyFromErp(coreSkuBo.getSku());
        if (Objects.nonNull(effectAfterSalePolicy)) {
            CoreSkuQueryDetailDto.AfterSalesPolicy afterSalesPolicy = CoreSkuQueryDetailDto.AfterSalesPolicy.builder()
                    .isInstallable(coreSkuBo.getIsInstallable())
                    .installPolicyInstallType(effectAfterSalePolicy.getInstallPolicyInstallType())
                    .guaranteePolicyIsGuarantee(effectAfterSalePolicy.getGuaranteePolicyIsGuarantee())
                    .returnPolicySupportReturn(effectAfterSalePolicy.getReturnPolicySupportReturn())
                    .installPolicyFreeRemoteInstall(effectAfterSalePolicy.getInstallPolicyFreeRemoteInstall())
                    .installPolicyInstallFee(effectAfterSalePolicy.getInstallPolicyInstallFee())
                    .provinceCityJsonvalue(effectAfterSalePolicy.getProvinceCityJsonvalue())
                    .build();
            coreSkuQueryDetailDto.setAfterSalesPolicy(afterSalesPolicy);
        }
        if (Objects.isNull(effectAfterSalePolicy)){
            CoreSkuQueryDetailDto.AfterSalesPolicy afterSalesPolicy = CoreSkuQueryDetailDto.AfterSalesPolicy.builder()
                    .isInstallable(coreSkuBo.getIsInstallable())
                    .build();
            coreSkuQueryDetailDto.setAfterSalesPolicy(afterSalesPolicy);
        }

        return coreSkuQueryDetailDto;
    }

    private String formatDeliveryCycle(String deliveryCycle){
        if (StringUtils.isBlank(deliveryCycle)){
            return "0";
        }
        // 将字符串转换为 BigDecimal
        BigDecimal deliveryCycleBigDecimal = new BigDecimal(deliveryCycle);

        // 向上取整
        BigDecimal roundedUpValue = deliveryCycleBigDecimal.setScale(0, RoundingMode.CEILING);

        return roundedUpValue.toPlainString();
    }

    @Override
    public List<CoreSkuInfoDto> getCoreSkuInfoDtoBySkuNo(List<String> skuNos,Integer customerNature){
        log.info("查询SKU选项信息，getCoreSkuInfoDtoBySkuNo:{},customerNature:{}",skuNos,customerNature);
        //组装商品基础信息
        List<CoreSkuDto> coreSkuBos = coreSkuMapper.getInfoBySkuNos(skuNos);
        if(CollectionUtils.isEmpty(coreSkuBos)){
            throw new ServiceException(BaseResponseCode.CRM_NO_DATA_NOT_EXIST.getCode(),"“"+skuNos+"”订货号不存在");
        }

        //将归属产品经理和归属产品助理都转换成一个Map,以例进行批量查询
        List<ProductManageAndAsistDto> productManageAndAsistDtos = coreSkuMapper.batchQueryProductManageAndAsist(skuNos);
        Map<String, ProductManageAndAsistDto> skuNoToDtoMap = productManageAndAsistDtos.stream()
            .collect(Collectors.toMap(
                ProductManageAndAsistDto::getSkuNo,
                dto -> dto
            ));
        List<Integer> productUserIdList = productManageAndAsistDtos.stream()
            .flatMap(dto -> {
                List<Integer> userIds = new ArrayList<>();
                if (dto.getProductAssitUserId() != null) {
                    userIds.add(dto.getProductAssitUserId());
                }
                if (dto.getProductManageUserId() != null) {
                    userIds.add(dto.getProductManageUserId());
                }
                return userIds.stream();
            })
            .collect(Collectors.toList());
        List<UserDto> productUserList = userApiService.getUserInfoByUserIds(productUserIdList);
        Map<Integer,UserDto> userIdToDtoMap = productUserList.stream().collect(Collectors.toMap(
            UserDto::getUserId,
            dto -> dto
        ));
        List<CoreSkuInfoDto> resultList = new ArrayList<>();
        for(CoreSkuDto coreSkuBo:coreSkuBos){
            log.info("查询SKU选项信息，coreSkuBo:{}",JSON.toJSON(coreSkuBo));
            CoreSkuInfoDto dto = new CoreSkuInfoDto();
            dto.setCheckStatus(coreSkuBo.getCheckStatus());
            dto.setBrandName(coreSkuBo.getBrandName());
            dto.setIsNeedReport(coreSkuBo.getIsNeedReport());
            dto.setIsInstall(coreSkuBo.getIsInstallable());
            dto.setSkuId(coreSkuBo.getSkuId());
            dto.setSkuNo(coreSkuBo.getSku());
            dto.setSkuName(coreSkuBo.getSkuName());
            dto.setBrandName(coreSkuBo.getBrandName());
            dto.setModelOrSpec(coreSkuBo.getModelOrSpec());
            dto.setExpectDeliveryTime(formatDeliveryCycle(coreSkuBo.getExpectDeliveryTime()));
            dto.setNum(coreSkuBo.getMinOrder());
            dto.setUnitName(coreSkuBo.getUnitName());
            dto.setAvailableStockNum(Objects.isNull(coreSkuBo.getAvailableStockNum()) ? "0" : String.valueOf(coreSkuBo.getAvailableStockNum()));
            dto.setGoodsPositionNo(coreSkuBo.getGoodsPositionNo());
            dto.setCompanyName(coreSkuBo.getCompanyName());
            dto.setRegistrationNumber(coreSkuBo.getRegistrationNumber());
            String technicalParameter = coreSkuBo.getTechnicalParameter();
            // 主要参数
            List<String> mainParam = getMainParams(technicalParameter);
            if (CollUtil.isNotEmpty(mainParam)){
                dto.setMainParam(mainParam);
            }
            // 保修期
            String hostPeriod = coreSkuBo.getHostPeriod();
            if (StringUtils.isNotBlank(hostPeriod)){
                String[] split = hostPeriod.split(",");
                if (split.length > 1 && StrUtil.isNotBlank(split[0]) ){
//                    dto.setWarrantyInfo(split[0]+"月");
                    dto.setWarrantyInfo(split[0]+split[1]);//5,年 36,月  这种格式的值
                }
            }
            if (coreSkuBo.getSpuType() == 317 && StringUtils.isNotBlank(coreSkuBo.getEffectiveDays())){
                dto.setUseLife(coreSkuBo.getEffectiveDays()+"天");
            }
            else if (coreSkuBo.getSpuType() != 317 && Objects.nonNull(coreSkuBo.getServiceLife())) {
                dto.setUseLife(coreSkuBo.getServiceLife()+"年");
            }
            else {
                dto.setUseLife("");
            }

            //先根据sku取skuNoToDtoMap
            ProductManageAndAsistDto productToDto = skuNoToDtoMap.get(dto.getSkuNo());
            Integer productAssitUserId = productToDto.getProductAssitUserId();
            Integer productManageUserId = productToDto.getProductManageUserId();
            List<Integer> userIdList = Arrays.asList(productManageUserId,productAssitUserId).stream().filter(Objects::nonNull).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(userIdList)){
                List<UserDto> filteredUserDtoList = userIdList.stream()
                        .filter(userIdToDtoMap::containsKey) // 过滤出 userIdToDtoMap 中存在的 userId
                        .map(userIdToDtoMap::get)            // 获取对应的 UserDto
                        .collect(Collectors.toList());       // 收集到 List 中
                dto.setProductManager(filteredUserDtoList);
            }


            resultList.add(dto);
        }
        //批量设置商品主图
        setBatchSkuImg(resultList);

        //批量查询价格中心的价格
        setSaleSkuPrice(resultList,customerNature);
        return resultList;
    }


    public void setSaleSkuPrice(List<CoreSkuInfoDto> resultList,Integer customerNature ){
        if(CollectionUtils.isEmpty(resultList)){
            return;
        }
        List<String> skuNoList = resultList.stream().map(CoreSkuInfoDto::getSkuNo).collect(Collectors.toList());
        BatchSkuPriceInfoDetailRequestDto batchSkuPriceInfoDetailRequestDto = new BatchSkuPriceInfoDetailRequestDto();
        batchSkuPriceInfoDetailRequestDto.setSkuNo(skuNoList);
        List<SkuPriceInfoDetailResponseDto> priceInfoDetailResponseDtoList =  getBatchPriceCenterInfo(batchSkuPriceInfoDetailRequestDto);
        Map<String,SkuPriceInfoDetailResponseDto> skuToPriceMap = priceInfoDetailResponseDtoList.stream().collect(Collectors.toMap(
                SkuPriceInfoDetailResponseDto::getSkuNo,
                item -> item
        ));
        resultList.forEach(skuInfoDto -> {
            // 调用价格中心
            SkuPriceInfoDetailResponseDto priceCenterInfo = skuToPriceMap.get(skuInfoDto.getSkuNo());
            if (Objects.nonNull(priceCenterInfo)){
                // 终端
                if (TraderNatureEnum.ZD.getCode().equals(customerNature)){
                    skuInfoDto.setDealerPrice(priceCenterInfo.getTerminalPrice());
                }
                // 分销
                if (TraderNatureEnum.FX.getCode().equals(customerNature)){
                    skuInfoDto.setDealerPrice(priceCenterInfo.getDistributionPrice());
                }
            }
        });
    }

    /**
     * 批量设置商品主图
     * @param resultList
     */
    private void setBatchSkuImg(List<CoreSkuInfoDto> resultList ){
        if(CollectionUtils.isEmpty(resultList)){
            return;
        }
        List<String> skuNoList = resultList.stream().map(CoreSkuInfoDto::getSkuNo).collect(Collectors.toList());
        List<SkuImageDto> imageList = coreSkuMapper.getSkuImage(skuNoList);
        Map<String, SkuImageDto> imageMap = imageList.stream()
                .collect(Collectors.toMap(
                        SkuImageDto::getSkuNo, // key
                        item -> item // value
                ));
        resultList.forEach(skuInfoDto -> {
            SkuImageDto imageDto = imageMap.get(skuInfoDto.getSkuNo());
            if (imageDto != null) {
                skuInfoDto.setImageUrl(imageDto.getImgUrl());
            }
        });
    }


    @Override
    public CoreSkuInfoDto getCoreSkuInfoDtoBySkuNo(String skuNo,Integer customerNature) {
        log.info("查询SKU选项信息，getCoreSkuInfoDtoBySkuNo:{},customerNature:{}",skuNo,customerNature);
        //组装商品基础信息
        CoreSkuDto coreSkuBo = coreSkuMapper.getInfoBySkuNo(skuNo);
        if (Objects.isNull(coreSkuBo)){
            throw new ServiceException(BaseResponseCode.CRM_NO_DATA_NOT_EXIST.getCode(),"“"+skuNo+"”订货号不存在");
        }
        log.info("查询SKU选项信息，coreSkuBo:{}",JSON.toJSON(coreSkuBo));
        CoreSkuInfoDto dto = new CoreSkuInfoDto();
        dto.setBrandName(coreSkuBo.getBrandName());
        dto.setIsNeedReport(coreSkuBo.getIsNeedReport());
        dto.setIsInstall(coreSkuBo.getIsInstallable());
        dto.setSkuId(coreSkuBo.getSkuId());
        dto.setSkuNo(coreSkuBo.getSku());
        dto.setSkuName(coreSkuBo.getSkuName());
        dto.setBrandName(coreSkuBo.getBrandName());
        dto.setModelOrSpec(coreSkuBo.getModelOrSpec());
        dto.setExpectDeliveryTime(formatDeliveryCycle(coreSkuBo.getExpectDeliveryTime()));
        dto.setNum(coreSkuBo.getMinOrder());
        dto.setUnitName(coreSkuBo.getUnitName());
        dto.setAvailableStockNum(Objects.isNull(coreSkuBo.getAvailableStockNum()) ? "0" : String.valueOf(coreSkuBo.getAvailableStockNum()));
        dto.setGoodsPositionNo(coreSkuBo.getGoodsPositionNo());
        dto.setCompanyName(coreSkuBo.getCompanyName());
        dto.setRegistrationNumber(coreSkuBo.getRegistrationNumber());
        String technicalParameter = coreSkuBo.getTechnicalParameter();
        // 主要参数
        List<String> mainParam = getMainParams(technicalParameter);
        if (CollUtil.isNotEmpty(mainParam)){
            dto.setMainParam(mainParam);
        }
        // 保修期
        String hostPeriod = coreSkuBo.getHostPeriod();
        if (StringUtils.isNotBlank(hostPeriod)){
            String[] split = hostPeriod.split(",");
            if (split.length > 1 && StrUtil.isNotBlank(split[0]) ){
//                dto.setWarrantyInfo(split[0]+"月");
                dto.setWarrantyInfo(split[0]+split[1]);//5,年 36,月  这种格式的值
            }
        }


        if (coreSkuBo.getSpuType() == 317 && StringUtils.isNotBlank(coreSkuBo.getEffectiveDays())){
            dto.setUseLife(coreSkuBo.getEffectiveDays()+"天");
        }
        else if (coreSkuBo.getSpuType() != 317 && Objects.nonNull(coreSkuBo.getServiceLife())) {
            dto.setUseLife(coreSkuBo.getServiceLife()+"年");
        }
        else {
            dto.setUseLife("");
        }


        Integer skuId = coreSkuBo.getSkuId();
        if (Objects.nonNull(skuId)){
            GoodsAttachmentEntity goodsAttachment = coreSkuMapper.getGoodsAttachment(skuId);
            log.info("查询SKU图片信息，goodsAttachment:{}",JSON.toJSON(goodsAttachment));
            // 图片
            dto.setImageUrl(Optional.ofNullable(goodsAttachment).map(GoodsAttachmentEntity::getUrl).orElse(""));
        }
        SkuPriceInfoDetailRequestDto skuQueryDto = new SkuPriceInfoDetailRequestDto();
        skuQueryDto.setSkuNo(coreSkuBo.getSku());

        // 调用价格中心
        SkuPriceInfoDetailResponseDto priceCenterInfo = getPriceCenterInfo(skuQueryDto);
        if (Objects.nonNull(priceCenterInfo)){
            // 终端
            if (TraderNatureEnum.ZD.getCode().equals(customerNature)){
                dto.setDealerPrice(priceCenterInfo.getTerminalPrice());
            }
            // 分销
            if (TraderNatureEnum.FX.getCode().equals(customerNature)){
                dto.setDealerPrice(priceCenterInfo.getDistributionPrice());
            }
        }
        List<ProductManageAndAsistDto> productManageAndAsistDtos = coreSkuMapper.batchQueryProductManageAndAsist(Arrays.asList(skuNo));
        if (CollUtil.isNotEmpty(productManageAndAsistDtos)){
            ProductManageAndAsistDto productManageAndAsistDto = productManageAndAsistDtos.get(0);
            Integer productAssitUserId = productManageAndAsistDto.getProductAssitUserId();
            Integer productManageUserId = productManageAndAsistDto.getProductManageUserId();
            List<Integer> nonNullUserIds = Arrays.asList(productAssitUserId, productManageUserId).stream().filter(Objects::nonNull).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(nonNullUserIds)){
                List<UserDto> userInfoByUserIds = userApiService.getUserInfoByUserIds(nonNullUserIds);
                dto.setProductManager(userInfoByUserIds);
            }

        }
        return dto;
    }

    @Override
    public List<CoreSkuDto> getInfoBySkuNos(List<String> skuNos){
        return coreSkuMapper.getInfoBySkuNos(skuNos);
    }

    @Override
    public CoreSkuInfoDto getCoreSkuInfo(String skuNo) {
        log.info("查询SKU，getCoreSkuInfoDtoBySkuNo:{}",skuNo);
        //组装商品基础信息
        CoreSkuDto coreSkuBo = coreSkuMapper.getInfoBySkuNo(skuNo);
        String technicalParameter = coreSkuBo.getTechnicalParameter();
        CoreSkuInfoDto infoDto = new CoreSkuInfoDto();
        // 主要参数
        List<String> mainParam = getMainParams(technicalParameter);
        infoDto.setMainParam(mainParam);
        return infoDto;
    }

    private List<String> getMainParams( String technicalParameter){
        List<String> mainParam = new ArrayList<>();
        // 主要参数
        if (StringUtils.isNotBlank(technicalParameter)){
            String[] split = technicalParameter.split(";");
            List<String> list = Arrays.asList(split);
            // 取前6条
            if (list.size() > 6){
                mainParam = list.subList(0,6);
            }
            else {
                mainParam = list;
            }
        }
        return mainParam;
    }


    @Override
    public CoreSkuDto getCoreSkuDtoBySkuId(Integer goodsId) {

        return coreSkuMapper.getCoreSkuInfoBySkuId(goodsId);
    }

    @Override
    public CoreSkuDto getCoreSkuDtoBySkuNo(String skuNo) {
        return coreSkuMapper.getCoreSkuInfoBySkuNo(skuNo);
    }

    /**
     * 获取商品种类数字符串
     *
     * @param total
     * @return
     */
    private static String getSkuCountStr(int total) {
        String goodsNumberSuffix = "{}{}件商品";
        String skuCountStr = "";
        if (0 == total) {
            skuCountStr =StrUtil.format(goodsNumberSuffix, total, "");
        }
        if (0 < total && total < 10) {
            skuCountStr = StrUtil.format(goodsNumberSuffix, total, "");
        }

        if (10 <= total && total < 100) {
            skuCountStr = StrUtil.format(goodsNumberSuffix, total / 10 * 10, "+");
        }

        if (100 <= total && total < 1000) {
            skuCountStr = StrUtil.format(goodsNumberSuffix, total / 100 * 100, "+");
        }

        if (1000 <= total && total < 10000) {
            skuCountStr = StrUtil.format(goodsNumberSuffix, total / 1000 * 1000, "+");
        }

        if (10000 <= total) {
            skuCountStr = StrUtil.format(goodsNumberSuffix, total / 10000, "万+");
        }
        return skuCountStr;
    }


    /**
     * 获取商品属性
     *
     * @return
     */
    private String getGoodsAttribute(String skuNo) {

        List<SkuAttrDto> goodsAttribute = skuAttrMappingGenerateMapper.getGoodsAttribute(skuNo);
        if (!CollectionUtils.isEmpty(goodsAttribute)) {
            StringBuilder attributeStr = new StringBuilder();
            for (SkuAttrDto skuAttrDto : goodsAttribute) {
                if (Objects.nonNull(skuAttrDto.getBaseAttributeName())) {
                    attributeStr.append(skuAttrDto.getBaseAttributeName()).append(":");
                }
                if (Objects.nonNull(skuAttrDto.getAttrValue())) {
                    attributeStr.append(skuAttrDto.getAttrValue());
                }
                if (Objects.nonNull(skuAttrDto.getUnitName())) {
                    attributeStr.append(skuAttrDto.getUnitName());
                }
                attributeStr.append(" ");
            }
            return attributeStr.toString();
        }
        return null;
    }

    /**
     * 根据skuId获取商品在途信息
     *
     * @param skuId
     * @return
     */
    private String getGoodsOnWayInfo(Integer skuId) {
        List<BuyOrderDto> buyOrderDtos = listGoodsOnwayInfoFromErp(skuId);
        if (!CollectionUtils.isEmpty(buyOrderDtos)) {
            StringBuilder onWayStr = new StringBuilder();
            for (BuyOrderDto buyOrderDto : buyOrderDtos) {
                onWayStr.append(buyOrderDto.getBuyorderNo()).append("/").append(buyOrderDto.getOnWayNum()).append("/");
                if (!Objects.isNull(buyOrderDto.getEstimateArrivalTime()) && buyOrderDto.getEstimateArrivalTime() > 0) {
                    onWayStr.append(DateUtil.formatDate(DateUtil.date(buyOrderDto.getEstimateArrivalTime())));
                } else {
                    onWayStr.append("--");
                }
                onWayStr.append(" ");
            }
            return onWayStr.toString();
        }
        return null;
    }

    /**
     * 根据搜索参数获取品牌列表
     *
     * @param searchGoodsDtos
     * @param searchGoodsDto
     */
    @SuppressWarnings("all")
    private void getBrandListFromGoodsDtos(List<GoodsDto> searchGoodsDtos, SearchGoodsDto searchGoodsDto) {

        List<GoodsDto> searchGoodsBrandList = searchGoodsDtos.stream().filter(item -> StringUtils.isNotBlank(item.getBrandName())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(searchGoodsBrandList)) {
            searchGoodsBrandList = searchGoodsBrandList
                    .stream()
                    .collect(Collectors.groupingBy(GoodsDto::getBrandId))
                    .entrySet()
                    .stream()
                    .sorted((e, o) -> CompareUtil.compare(e.getValue().size(), o.getValue().size()))
                    .map(e -> e.getValue().get(0))
                    .collect(Collectors.toList());
            Collections.reverse(searchGoodsBrandList);
            List<SearchGoodsDto.QueryBrandsInfo> queryBrandsInfoList = new ArrayList<>();
            searchGoodsBrandList.forEach(item -> {
                SearchGoodsDto.QueryBrandsInfo queryBrandsInfo = SearchGoodsDto.QueryBrandsInfo.builder().brandId(item.getBrandId()).brandName(item.getBrandName()).build();
                queryBrandsInfoList.add(queryBrandsInfo);
            });
            searchGoodsDto.setQueryBrandsInfoList(queryBrandsInfoList);
        }
    }

    /**
     * 根据搜索参数获取分类列表
     */
    @SuppressWarnings("all")
    private void getCategoryListFromGoodsDtos(List<GoodsDto> searchGoodsDtos, SearchGoodsDto searchGoodsDto) {

        List<GoodsDto> searchGoodsCategoryList = searchGoodsDtos.stream().filter(item -> StringUtils.isNotBlank(item.getBaseCategoryName())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(searchGoodsCategoryList)) {
            searchGoodsCategoryList = searchGoodsCategoryList
                    .stream()
                    .collect(Collectors.groupingBy(GoodsDto::getCategoryId))
                    .entrySet()
                    .stream()
                    .sorted((e, o) -> CompareUtil.compare(e.getValue().size(), o.getValue().size()))
                    .map(e -> e.getValue().get(0))
                    .collect(Collectors.toList());
            Collections.reverse(searchGoodsCategoryList);
            List<SearchGoodsDto.QueryCategoriesInfo> queryCategoriesInfoList = new ArrayList<>();
            searchGoodsCategoryList.forEach(item -> {
                SearchGoodsDto.QueryCategoriesInfo queryCategoriesInfo = SearchGoodsDto.QueryCategoriesInfo.builder().categoryId(item.getCategoryId())
                        .baseCategoryName(item.getBaseCategoryName()).build();
                queryCategoriesInfoList.add(queryCategoriesInfo);
            });
            searchGoodsDto.setQueryCategoriesInfoList(queryCategoriesInfoList);
        }
    }

    /**
     * 从价格中心获取sku价格信息
     */
    private SkuPriceInfoDetailResponseDto getSkuCheckPriceInfo(SkuPriceInfoDetailRequestDto skuPriceInfoDetailRequestDto) {
        log.info("掌上小贝详情页：获取核价信息，入参[{}]", JSON.toJSONString(skuPriceInfoDetailRequestDto));
        RestfulResult<SkuPriceInfoDetailResponseDto> responseDtoRestfulResult = remotePriceApiService.findSkuPriceInfoBySkuNo(skuPriceInfoDetailRequestDto);
        if (Objects.isNull(responseDtoRestfulResult)) {
            log.error("掌上小贝详情页：获取核价信息,调用价格中心异常");
            throw new ServiceException(BaseResponseCode.SYSTEM_BUSY);
        }
        log.info("掌上小贝详情页：获取核价信息，返回值[{}]", JSON.toJSONString(responseDtoRestfulResult));
        return responseDtoRestfulResult.getData();
    }

    /**
     * 从价格中心获取sku价格信息
     */
    private List<SkuPriceInfoDetailResponseDto> getBatchPriceCenterInfo(BatchSkuPriceInfoDetailRequestDto batchSkuPriceInfoDetailRequestDto) {
        log.info("批量获取核价信息，入参[{}]", JSON.toJSONString(batchSkuPriceInfoDetailRequestDto));
        RestfulResult<List<SkuPriceInfoDetailResponseDto>> responseDtoRestfulResult = remotePriceApiService.findSkuPriceInfoBySkuNos(batchSkuPriceInfoDetailRequestDto);
        if (Objects.isNull(responseDtoRestfulResult)) {
            log.error("批量获取核价信息,调用价格中心异常");
            throw new ServiceException(BaseResponseCode.SYSTEM_BUSY);
        }
        log.info("批量获取核价信息，返回值[{}]", JSON.toJSONString(responseDtoRestfulResult));
        return responseDtoRestfulResult.getData();
    }

    /**
     * 从价格中心获取sku价格信息
     */
    private SkuPriceInfoDetailResponseDto getPriceCenterInfo(SkuPriceInfoDetailRequestDto skuPriceInfoDetailRequestDto) {
        log.info("获取核价信息，入参[{}]", JSON.toJSONString(skuPriceInfoDetailRequestDto));
        RestfulResult<SkuPriceInfoDetailResponseDto> responseDtoRestfulResult = remotePriceApiService.findSkuPriceInfoBySkuNo(skuPriceInfoDetailRequestDto);
        if (Objects.isNull(responseDtoRestfulResult)) {
            log.error("获取核价信息,调用价格中心异常");
            throw new ServiceException(BaseResponseCode.SYSTEM_BUSY);
        }
        log.info("获取核价信息，返回值[{}]", JSON.toJSONString(responseDtoRestfulResult));
        return responseDtoRestfulResult.getData();
    }

    /**
     * 从ERP获取商品在途信息
     */
    private List<BuyOrderDto> listGoodsOnwayInfoFromErp(Integer skuId) {
        List<BuyOrderDto> buyOrderDtos = null;
        try {
            log.info("掌上小贝详情页：获取商品在途信息，入参[{}]", skuId);
            buyOrderDtos = remoteBuyorderInfoApiService.listGoodsOnwayInfo(skuId);
            if (CollectionUtils.isEmpty(buyOrderDtos)) {
                log.warn("掌上小贝详情页：获取商品在途信息为空");
                return Collections.emptyList();
            }
            log.info("掌上小贝详情页：获取商品在途信息，返回值[{}]", JSON.toJSONString(buyOrderDtos));
        } catch (Exception e) {
            log.error("掌上小贝详情页：获取商品在途信息异常",e);
        }
        return buyOrderDtos;
    }


    /**
     * 从ERP获取售后政策
     */
    private AfterSaleServiceStandardInfoDto getEffectAfterSalePolicyFromErp(String skuNo) {
        AfterSaleServiceStandardInfoDto effectAfterSalePolicy = null;
        try {
            log.info("掌上小贝详情页：获取商品售后政策，入参[{}]", skuNo);
            effectAfterSalePolicy = remoteAftersaleInfoApiService.getEffectAfterSalePolicy(skuNo);
            if (Objects.isNull(effectAfterSalePolicy)) {
                log.info("掌上小贝详情页：获取商品售后政策为空");
                return null;
            }
            log.info("掌上小贝详情页：获取商品售后政策，返回值[{}]", JSON.toJSONString(effectAfterSalePolicy));
        } catch (Exception e) {
            log.error("掌上小贝详情页：获取商品售后政策异常",e);
        }
        return effectAfterSalePolicy;
    }


}




