package com.vedeng.goods.manager.validator;

import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.exception.*;
import com.wms.service.context.ThreadLocalContext;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Data
public abstract class GoodsInvalidatorChain {

    public ResultInfo errorResult;

    public abstract void doInvalid();

    public String getSkuNo(){
        String invalidSkuNo = (String) ThreadLocalContext.get("invalidSkuNo");
        return invalidSkuNo;
    }

    public String getSpuNo(){
        String invalidSpuNo = (String) ThreadLocalContext.get("invalidSpuNo");
        return invalidSpuNo;
    }

    public List<String> getSkuNoList(){
        List<String> invalidSkuNoList = (List<String>) ThreadLocalContext.get("invalidSkuNoList");
        return invalidSkuNoList;
    }

    public User getUser(){
        User invalidUser = (User) ThreadLocalContext.get("invalidUser");
        return invalidUser;
    }

    public String getReason(){
        String invalidReason = (String) ThreadLocalContext.get("invalidReason");
        return invalidReason;
    }
}
