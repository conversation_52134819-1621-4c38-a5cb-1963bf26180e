package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.SerialNumberTraceDto;

import java.util.List;


/**
 * @description ${end}
 * <AUTHOR>
 * @date 2023/6/28 23:22
 **/
public interface SerialNumberTraceDtoMapper {
    int deleteByPrimaryKey(Integer serialNumberTraceId);

    SerialNumberTraceDto selectByPrimaryKey(Integer serialNumberTraceId);

    int updateIsPushByPrimaryKey(SerialNumberTraceDto record);

    List<SerialNumberTraceDto> findByAll(SerialNumberTraceDto serialNumberTraceDto);
}