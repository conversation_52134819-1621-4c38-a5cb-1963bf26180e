package com.vedeng.activiti.taskassign;

import com.vedeng.activiti.service.ActionProcdefService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.activiti.engine.task.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

public class AutoCompleteTaskListener implements TaskListener {
    private static final Logger log = LoggerFactory.getLogger(AutoCompleteExecutionListener.class);
    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
    private ActionProcdefService actionProcdefService = context.getBean(ActionProcdefService.class);

    private ProcessEngine processEngine ;

    private ProcessEngine getProcessEngine() {
        if (processEngine == null) {
            processEngine =  ProcessEngines.getDefaultProcessEngine();// 流程引擎
        }
        return processEngine;
    }

    @Override
    public void notify(DelegateTask delegateTask) {
//        actionProcdefService.completeTaskByAdmin(delegateTask.getId(),"抄送自动完成",delegateTask.getVariables());
        try{
            TaskService taskService = this.getProcessEngine().getTaskService();
            Task task = taskService.createTaskQuery().taskId(delegateTask.getId()).singleResult();
            if(task != null){
                taskService.complete(task.getId());
            }
        }catch (Exception e){
            log.error("抄送发起失败",e);
        }

    }
}
