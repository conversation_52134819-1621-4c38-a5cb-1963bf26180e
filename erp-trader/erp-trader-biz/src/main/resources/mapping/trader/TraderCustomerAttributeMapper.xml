<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.TraderCustomerAttributeMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.TraderCustomerAttributeEntity">
        <!--@mbg.generated-->
        <!--@Table T_TRADER_CUSTOMER_ATTRIBUTE-->
        <id column="TRADER_CUSTOMER_ATTRIBUTE_ID" jdbcType="INTEGER" property="traderCustomerAttributeId" />
        <result column="TRADER_CUSTOMER_ID" jdbcType="INTEGER" property="traderCustomerId" />
        <result column="ATTRIBUTE_CATEGORY_ID" jdbcType="INTEGER" property="attributeCategoryId" />
        <result column="ATTRIBUTE_ID" jdbcType="INTEGER" property="attributeId" />
        <result column="ATTRIBUTE_OTHER" jdbcType="VARCHAR" property="attributeOther" />
        <result column="SUB_CATEGORY_IDS" jdbcType="VARCHAR" property="subCategoryIds" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        TRADER_CUSTOMER_ATTRIBUTE_ID, TRADER_CUSTOMER_ID, ATTRIBUTE_CATEGORY_ID, ATTRIBUTE_ID,
        ATTRIBUTE_OTHER, SUB_CATEGORY_IDS
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List" />
        from T_TRADER_CUSTOMER_ATTRIBUTE
        where TRADER_CUSTOMER_ATTRIBUTE_ID = #{traderCustomerAttributeId,jdbcType=INTEGER}
    </select>
    <select id="getCustomerAttributeByCustomerId" resultType="com.vedeng.erp.trader.dto.TraderCustomerAttributeDto">
        SELECT
            parent.OPTION_TYPE AS ATTRIBUTE_NAME,
            GROUP_CONCAT(tsod.TITLE SEPARATOR '、') AS ATTRIBUTE_VALUE
        FROM
            T_TRADER_CUSTOMER_ATTRIBUTE ttca
                LEFT JOIN T_SYS_OPTION_DEFINITION tsod ON
                ttca.ATTRIBUTE_ID = tsod.SYS_OPTION_DEFINITION_ID
                LEFT JOIN T_SYS_OPTION_DEFINITION parent ON
                tsod.PARENT_ID = parent.SYS_OPTION_DEFINITION_ID
        WHERE
            ttca.TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER}
        GROUP BY
            parent.OPTION_TYPE
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from T_TRADER_CUSTOMER_ATTRIBUTE
        where TRADER_CUSTOMER_ATTRIBUTE_ID = #{traderCustomerAttributeId,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="TRADER_CUSTOMER_ATTRIBUTE_ID" keyProperty="traderCustomerAttributeId" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerAttributeEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_TRADER_CUSTOMER_ATTRIBUTE (TRADER_CUSTOMER_ID, ATTRIBUTE_CATEGORY_ID,
        ATTRIBUTE_ID, ATTRIBUTE_OTHER, SUB_CATEGORY_IDS
        )
        values (#{traderCustomerId,jdbcType=INTEGER}, #{attributeCategoryId,jdbcType=INTEGER},
        #{attributeId,jdbcType=INTEGER}, #{attributeOther,jdbcType=VARCHAR}, #{subCategoryIds,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertSelective" keyColumn="TRADER_CUSTOMER_ATTRIBUTE_ID" keyProperty="traderCustomerAttributeId" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerAttributeEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_TRADER_CUSTOMER_ATTRIBUTE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="traderCustomerId != null">
                TRADER_CUSTOMER_ID,
            </if>
            <if test="attributeCategoryId != null">
                ATTRIBUTE_CATEGORY_ID,
            </if>
            <if test="attributeId != null">
                ATTRIBUTE_ID,
            </if>
            <if test="attributeOther != null">
                ATTRIBUTE_OTHER,
            </if>
            <if test="subCategoryIds != null">
                SUB_CATEGORY_IDS,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="traderCustomerId != null">
                #{traderCustomerId,jdbcType=INTEGER},
            </if>
            <if test="attributeCategoryId != null">
                #{attributeCategoryId,jdbcType=INTEGER},
            </if>
            <if test="attributeId != null">
                #{attributeId,jdbcType=INTEGER},
            </if>
            <if test="attributeOther != null">
                #{attributeOther,jdbcType=VARCHAR},
            </if>
            <if test="subCategoryIds != null">
                #{subCategoryIds,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerAttributeEntity">
        <!--@mbg.generated-->
        update T_TRADER_CUSTOMER_ATTRIBUTE
        <set>
            <if test="traderCustomerId != null">
                TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER},
            </if>
            <if test="attributeCategoryId != null">
                ATTRIBUTE_CATEGORY_ID = #{attributeCategoryId,jdbcType=INTEGER},
            </if>
            <if test="attributeId != null">
                ATTRIBUTE_ID = #{attributeId,jdbcType=INTEGER},
            </if>
            <if test="attributeOther != null">
                ATTRIBUTE_OTHER = #{attributeOther,jdbcType=VARCHAR},
            </if>
            <if test="subCategoryIds != null">
                SUB_CATEGORY_IDS = #{subCategoryIds,jdbcType=VARCHAR},
            </if>
        </set>
        where TRADER_CUSTOMER_ATTRIBUTE_ID = #{traderCustomerAttributeId,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerAttributeEntity">
        <!--@mbg.generated-->
        update T_TRADER_CUSTOMER_ATTRIBUTE
        set TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER},
        ATTRIBUTE_CATEGORY_ID = #{attributeCategoryId,jdbcType=INTEGER},
        ATTRIBUTE_ID = #{attributeId,jdbcType=INTEGER},
        ATTRIBUTE_OTHER = #{attributeOther,jdbcType=VARCHAR},
        SUB_CATEGORY_IDS = #{subCategoryIds,jdbcType=VARCHAR}
        where TRADER_CUSTOMER_ATTRIBUTE_ID = #{traderCustomerAttributeId,jdbcType=INTEGER}
    </update>
    <insert id="batchInsert" keyColumn="TRADER_CUSTOMER_ATTRIBUTE_ID" keyProperty="traderCustomerAttributeId" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_TRADER_CUSTOMER_ATTRIBUTE
        (TRADER_CUSTOMER_ID, ATTRIBUTE_CATEGORY_ID, ATTRIBUTE_ID, ATTRIBUTE_OTHER, SUB_CATEGORY_IDS
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.traderCustomerId,jdbcType=INTEGER}, #{item.attributeCategoryId,jdbcType=INTEGER},
            #{item.attributeId,jdbcType=INTEGER}, #{item.attributeOther,jdbcType=VARCHAR},
            #{item.subCategoryIds,jdbcType=VARCHAR})
        </foreach>
    </insert>

<!--auto generated by MybatisCodeHelper on 2023-08-14-->
    <delete id="deleteByTraderCustomerId">
        delete from T_TRADER_CUSTOMER_ATTRIBUTE
        where TRADER_CUSTOMER_ID=#{traderCustomerId,jdbcType=INTEGER}
    </delete>
</mapper>