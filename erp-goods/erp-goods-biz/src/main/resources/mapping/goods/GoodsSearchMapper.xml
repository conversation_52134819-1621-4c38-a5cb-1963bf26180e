<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.mapper.GoodsSearchMapper">

    <resultMap id="goodsSearchBaseResultMap" type="com.vedeng.goods.domain.dto.GoodsSearchBaseInfoDto">
        <result column="SKU_ID" jdbcType="INTEGER" property="skuId"/>
        <result column="SKU_NO" jdbcType="VARCHAR" property="skuNo"/>
        <result column="TITLE" jdbcType="VARCHAR" property="skuName"/>
        <result column="MODEL" jdbcType="VARCHAR" property="model"/>
        <result column="SPEC" jdbcType="VARCHAR" property="spec"/>
        <result column="GOODS_LEVEL" jdbcType="VARCHAR" property="goodsLevel"/>
        <result column="POSITION_NAME" jdbcType="VARCHAR" property="goodsPosition"/>
        <result column="TERMINAL_PRICE" jdbcType="VARCHAR" property="terminalPrice"/>
        <result column="DISTRIBUTION_PRICE" jdbcType="VARCHAR" property="distributionPrice"/>
        <result column="AVAILABLE_STOCK_NUM" jdbcType="VARCHAR" property="availableStock"/>
        <result column="ONWAYNUM" jdbcType="VARCHAR" property="onWayNum"/>
        <result column="STOCK_NUM" jdbcType="VARCHAR" property="stock"/>
        <result column="PURCHASE_TIME" jdbcType="VARCHAR" property="purchaseTime"/>
        <result column="UNIT_NAME" jdbcType="VARCHAR" property="unit"/>
        <result column="USERNAME" jdbcType="VARCHAR" property="salerOfLastOrder"/>
        <result column="REGISTRATION_NUMBER" jdbcType="VARCHAR" property="registerNumber"/>
        <result column="REGISTRATION_NUMBER_ID" jdbcType="INTEGER" property="registerNumberId"/>
        <result column="FIRST_ENGAGE_ID" jdbcType="INTEGER" property="firstEngageId"/>
        <result column="CHECK_STATUS" jdbcType="VARCHAR" property="checkStatus"/>
        <result column="TAX_CATEGORY_NO" jdbcType="VARCHAR" property="taxCategoryNo"/>
        <result column="CLASSIFICATION_ABBREVIATION" jdbcType="VARCHAR" property="classIficationAbbreviation"/>
        <result column="SPU_TYPE" jdbcType="VARCHAR" property="spuType"/>
        <result column="CATEGORY_NAME" jdbcType="VARCHAR" property="category"/>
        <result column="BRAND_NAME" jdbcType="VARCHAR" property="brandName"/>
        <result column="SALE_COUNT_OF_LAST_YEAR" jdbcType="VARCHAR" property="saleCountOfLastYear"/>
        <result column="AVG_PRICE_OF_LAST_YEAR" jdbcType="VARCHAR" property="avgPriceOfLastYear"/>
        <result column="SKU_ASSIGNMENTS" jdbcType="VARCHAR" property="skuAssignments"/>
        <result column="ORDER_OCCUPY_NUM" jdbcType="VARCHAR" property="occupyNum"/>
        <result column="DIRECT_DELIVERY_TIME" jdbcType="VARCHAR" property="directDeliveryTime"/>
        <result column="COMMON_DELIVERY_TIME" jdbcType="VARCHAR" property="commonDeliveryTime"/>
        <result column="SERVICE_LIFE" jdbcType="INTEGER" property="serviceLife"/>
        <result column="DI_CODE" jdbcType="VARCHAR" property="diCode"/>
    </resultMap>

    <sql id="Base_Column_List">
        A.SKU_ID,A.SERVICE_LIFE,A.DI_CODE,A.SKU_NO,A.SKU_NAME,A.MODEL,A.SPEC,LV.LEVEL_NAME GOODS_LEVEL,POS.POSITION_NAME,A.TERMINAL_PRICE,A.DISTRIBUTION_PRICE,
        A.AVAILABLE_STOCK_NUM,TT.ONWAYNUM,A.STOCK_NUM,A.PURCHASE_TIME,UU.UNIT_NAME,SALE.USERNAME,D.REGISTRATION_NUMBER,D.REGISTRATION_NUMBER_ID,
        RPAD(A.TAX_CATEGORY_NO, 19, '0')  TAX_CATEGORY_NO,TCC.CLASSIFICATION_ABBREVIATION,PT.TITLE SPU_TYPE,CONCAT( EB.BASE_CATEGORY_NAME, '/', DB.BASE_CATEGORY_NAME, '/', CB.BASE_CATEGORY_NAME ) CATEGORY_NAME,E.BRAND_NAME,
        A.ONE_YEAR_SALE_NUM SALE_COUNT_OF_LAST_YEAR,IFNULL( A.AVGPRICE, '0' ) AVG_PRICE_OF_LAST_YEAR,CONCAT( F.USERNAME, '&amp;', G.USERNAME ) SKU_ASSIGNMENTS,
        CONCAT( A.ORDER_OCCUPY_NUM, '/', A.ACTION_LOCK_NUM ) ORDER_OCCUPY_NUM ,C.FIRST_ENGAGE_ID,
        CASE A.CHECK_STATUS WHEN 0 THEN '待完善'  WHEN 1 THEN  '审核中'  WHEN 2 THEN  '审核不通过'  WHEN 3 THEN  '审核通过'  WHEN 4 THEN  '删除 ' ELSE '待提交审核'  END CHECK_STATUS
    </sql>


    <select id="getGoodsSearchBaseInfoBySkuIds" resultMap="goodsSearchBaseResultMap">
        SELECT
        IF (TSDD.DIRECT_DELIVERY_TIME_START = TSDD.DIRECT_DELIVERY_TIME_END,TSDD.DIRECT_DELIVERY_TIME_START,CONCAT(TSDD.DIRECT_DELIVERY_TIME_START, '-', TSDD.DIRECT_DELIVERY_TIME_END)) AS DIRECT_DELIVERY_TIME,
        IF (TSDD.COMMON_DELIVERY_TIME_START = TSDD.COMMON_DELIVERY_TIME_END,TSDD.COMMON_DELIVERY_TIME_START,CONCAT(TSDD.COMMON_DELIVERY_TIME_START, '-', TSDD.COMMON_DELIVERY_TIME_END))  AS COMMON_DELIVERY_TIME,
        <include refid="Base_Column_List"/>
        FROM
        V_CORE_SKU A
        LEFT JOIN T_TAXCODE_CLASSIFICATION TCC ON RPAD(A.TAX_CATEGORY_NO, 19, '0') = TCC.FINAL_CODE
        LEFT JOIN T_SKU_DELIVERY_DATA TSDD ON TSDD.SKU_ID = A.SKU_ID AND TSDD.IS_DELETE = 0
        LEFT JOIN V_GOODS_LEVEL LV ON A.GOODS_LEVEL_NO = LV.ID
        LEFT JOIN V_GOODS_POSITION POS ON A.GOODS_POSITION_NO = POS.ID
        LEFT JOIN V_CORE_SPU B ON A.SPU_ID = B.SPU_ID
        LEFT JOIN T_SYS_OPTION_DEFINITION PT ON B.SPU_TYPE = PT.SYS_OPTION_DEFINITION_ID
        LEFT JOIN T_FIRST_ENGAGE C ON C.FIRST_ENGAGE_ID = B.FIRST_ENGAGE_ID
        LEFT JOIN T_REGISTRATION_NUMBER D ON C.REGISTRATION_NUMBER_ID = D.REGISTRATION_NUMBER_ID
        LEFT JOIN T_BRAND E ON B.BRAND_ID = E.BRAND_ID
        LEFT JOIN T_USER F ON B.ASSIGNMENT_MANAGER_ID = F.USER_ID
        LEFT JOIN T_USER G ON B.ASSIGNMENT_ASSISTANT_ID = G.USER_ID
        LEFT JOIN V_BASE_CATEGORY CB ON B.CATEGORY_ID = CB.BASE_CATEGORY_ID
        LEFT JOIN V_BASE_CATEGORY DB ON CB.PARENT_ID = DB.BASE_CATEGORY_ID
        LEFT JOIN V_BASE_CATEGORY EB ON DB.PARENT_ID = EB.BASE_CATEGORY_ID
        LEFT JOIN V_CORE_OPERATE_INFO COI ON A.SPU_ID = COI.SKU_ID
        AND COI.OPERATE_INFO_TYPE = 2
        LEFT JOIN T_USER SALE ON SALE.USER_ID = A.LATEST_VALID_ORDER_USER
        LEFT JOIN T_UNIT UU ON UU.UNIT_ID = A.BASE_UNIT_ID
        LEFT JOIN (
        SELECT
        SUM( T.ONWAYNUM ) AS 'ONWAYNUM',
        T.GOODS_ID
        FROM
        (
        SELECT
        a.GOODS_ID,
        COALESCE ( SUM( ( a.NUM - IFNULL( a.ARRIVAL_NUM, 0 ) - IFNULL( TT.SHNUM, 0 ) ) ), 0 ) ONWAYNUM,
        b.BUYORDER_NO,
        a.ESTIMATE_ARRIVAL_TIME,
        b.VALID_TIME,
        b.BUYORDER_ID
        FROM
        T_BUYORDER_GOODS a
        LEFT JOIN T_BUYORDER b ON a.BUYORDER_ID = b.BUYORDER_ID
        LEFT JOIN (
        SELECT
        IFNULL( SUM( d.NUM ), 0 ) SHNUM,
        c.ORDER_ID,
        d.GOODS_ID,
        d.ORDER_DETAIL_ID
        FROM
        T_AFTER_SALES c
        LEFT JOIN T_AFTER_SALES_GOODS d ON c.AFTER_SALES_ID = d.AFTER_SALES_ID
        WHERE
        c.TYPE = 546
        AND c.VALID_STATUS = 1
        AND c.ATFER_SALES_STATUS = 2
        AND c.SUBJECT_TYPE = 536
        AND d.GOODS_ID IN
        <foreach collection="skuIds" item="skuId" index="index" open="(" close=")" separator=",">
            #{skuId}
        </foreach>
        GROUP BY
        d.ORDER_DETAIL_ID
        ) TT ON a.BUYORDER_GOODS_ID = TT.ORDER_DETAIL_ID
        WHERE
        b.VALID_STATUS = 1
        AND b.PAYMENT_STATUS IN ( 1, 2 )
        AND b.DELIVERY_DIRECT = 0
        AND b.ARRIVAL_STATUS IN ( 0, 1 )
        AND b.STATUS != 3
        AND a.GOODS_ID IN
        <foreach collection="skuIds" item="skuId" index="index" open="(" close=")" separator=",">
            #{skuId}
        </foreach>
        GROUP BY
        a.BUYORDER_GOODS_ID
        ) T
        WHERE
        T.ONWAYNUM > 0
        GROUP BY
        T.GOODS_ID
        ) TT ON TT.GOODS_ID = A.SKU_ID
        WHERE
        A.STATUS = 1
        AND B.STATUS = 1
        AND A.SKU_ID IN
        <foreach collection="skuIds" item="skuId" index="index" open="(" close=")" separator=",">
            #{skuId}
        </foreach>
        GROUP BY
        A.SKU_ID
    </select>

    <select id="defaultGoodsSearchBaseInfoListPage" resultMap="goodsSearchBaseResultMap">
        SELECT
        CONCAT(TSDD.DIRECT_DELIVERY_TIME_START, '-', TSDD.DIRECT_DELIVERY_TIME_END)  AS DIRECT_DELIVERY_TIME,
        CONCAT(TSDD.COMMON_DELIVERY_TIME_START, '-', TSDD.COMMON_DELIVERY_TIME_END)  AS COMMON_DELIVERY_TIME,
        <include refid="Base_Column_List"/>
        FROM
        V_CORE_SKU A
        LEFT JOIN T_TAXCODE_CLASSIFICATION TCC ON RPAD(A.TAX_CATEGORY_NO, 19, '0') = TCC.FINAL_CODE
        LEFT JOIN T_SKU_DELIVERY_DATA TSDD ON TSDD.SKU_ID = A.SKU_ID AND TSDD.IS_DELETE = 0
        LEFT JOIN V_GOODS_LEVEL LV ON A.GOODS_LEVEL_NO = LV.ID
        LEFT JOIN V_GOODS_POSITION POS ON A.GOODS_POSITION_NO = POS.ID
        LEFT JOIN V_CORE_SPU B ON A.SPU_ID = B.SPU_ID
        LEFT JOIN T_SYS_OPTION_DEFINITION PT ON B.SPU_TYPE = PT.SYS_OPTION_DEFINITION_ID
        LEFT JOIN T_FIRST_ENGAGE C ON C.FIRST_ENGAGE_ID = B.FIRST_ENGAGE_ID
        LEFT JOIN T_REGISTRATION_NUMBER D ON C.REGISTRATION_NUMBER_ID = D.REGISTRATION_NUMBER_ID
        LEFT JOIN T_BRAND E ON B.BRAND_ID = E.BRAND_ID
        LEFT JOIN T_USER F ON B.ASSIGNMENT_MANAGER_ID = F.USER_ID
        LEFT JOIN T_USER G ON B.ASSIGNMENT_ASSISTANT_ID = G.USER_ID
        LEFT JOIN V_BASE_CATEGORY CB ON B.CATEGORY_ID = CB.BASE_CATEGORY_ID
        LEFT JOIN V_BASE_CATEGORY DB ON CB.PARENT_ID = DB.BASE_CATEGORY_ID
        LEFT JOIN V_BASE_CATEGORY EB ON DB.PARENT_ID = EB.BASE_CATEGORY_ID
        LEFT JOIN V_CORE_OPERATE_INFO COI ON A.SPU_ID = COI.SKU_ID
        AND COI.OPERATE_INFO_TYPE = 2
        LEFT JOIN T_USER SALE ON SALE.USER_ID = A.LATEST_VALID_ORDER_USER
        LEFT JOIN T_UNIT UU ON UU.UNIT_ID = A.BASE_UNIT_ID
        LEFT JOIN (
        SELECT
        SUM( T.ONWAYNUM ) AS 'ONWAYNUM',
        T.GOODS_ID
        FROM
        (
        SELECT
        a.GOODS_ID,
        COALESCE ( SUM( ( a.NUM - IFNULL( a.ARRIVAL_NUM, 0 ) - IFNULL( TT.SHNUM, 0 ) ) ), 0 ) ONWAYNUM,
        b.BUYORDER_NO,
        a.ESTIMATE_ARRIVAL_TIME,
        b.VALID_TIME,
        b.BUYORDER_ID
        FROM
        T_BUYORDER_GOODS a
        LEFT JOIN T_BUYORDER b ON a.BUYORDER_ID = b.BUYORDER_ID
        LEFT JOIN (
        SELECT
        IFNULL( SUM( d.NUM ), 0 ) SHNUM,
        c.ORDER_ID,
        d.GOODS_ID,
        d.ORDER_DETAIL_ID
        FROM
        T_AFTER_SALES c
        LEFT JOIN T_AFTER_SALES_GOODS d ON c.AFTER_SALES_ID = d.AFTER_SALES_ID
        WHERE
        c.TYPE = 546
        AND c.VALID_STATUS = 1
        AND c.ATFER_SALES_STATUS = 2
        AND c.SUBJECT_TYPE = 536
        GROUP BY
        d.ORDER_DETAIL_ID
        ) TT ON a.BUYORDER_GOODS_ID = TT.ORDER_DETAIL_ID
        WHERE
        b.VALID_STATUS = 1
        AND b.PAYMENT_STATUS IN ( 1, 2 )
        AND b.DELIVERY_DIRECT = 0
        AND b.ARRIVAL_STATUS IN ( 0, 1 )
        AND b.STATUS != 3
        GROUP BY
        a.BUYORDER_GOODS_ID
        ) T
        WHERE
        T.ONWAYNUM > 0
        GROUP BY
        T.GOODS_ID
        ) TT ON TT.GOODS_ID = A.SKU_ID
        WHERE
        A.STATUS = 1
        AND B.STATUS = 1
        GROUP BY
        A.SKU_ID
        <if test="sortParam != null">
        ORDER BY
            ${sortParam}
        </if>
    </select>

    <select id="defaultSkuIdsSearchListPage" resultType="java.lang.Integer">
        SELECT SKU_ID FROM V_CORE_SKU WHERE STATUS = 1
    </select>
</mapper>
