<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="供应链工作台" scope="application" />
<%@ include file="../common/common.jsp"%>
<link rel="stylesheet" href="<%=basePath%>static/css/workbench/base.css?rnd=${resourceVersionKey}" />
<link rel="stylesheet" href="<%=basePath%>static/css/workbench/personnel.css?rnd=${resourceVersionKey}" />
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        .tab{
            font-size: 15px;
            text-align: center;
            position: relative;
            display: inline-block;
            width: 32%;
        }
        .subTab{
            color: red;!important;
            text-decoration: underline;
        }
        .cannot-click {
            pointer-events: none;
            cursor: default;
        }
    </style>
</head>
<body>
<div class="personnel-wrapper">
    <div class="pw-left">
        <div class="common-box">
            <div class="cb-title">
                订单交易待办项
            </div>
            <div class="cb-content">
                <div class="info-box">
                    <div class="ib-title">
                        交易合规风控任务
                    </div>
                    <div class="ib-content">
                        <div class="ib-list">

                            <div class="ib-item">
                                <div class="item-left"></div>
                                <div class="item-right">
                                    <div class="item-r-title">
                                        商品信息待办项
                                    </div>
                                    <div class="item-r-content">
                                        <div class="total-num">
                                            <a class="addtitle_dyn"
                                               href="javascript:void(0);"
                                               tabTitle='{"num":"riskCheckSkuTodoListCount<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"/ezadmin/list/list-goodsTodo?ASSIGNMENT_ASSISTANT_ID=${userId}&ASSIGNMENT_MANAGER_ID=${userId}",
											              "title":"商品信息完善"}'>
                                                    ${staffTodoDto.riskSkuTodoDto.skuTodoListCount == null ? 0 : staffTodoDto.riskSkuTodoDto.skuTodoListCount}
                                            </a>
                                        </div>
                                        <div class="total-desc">
                                            <div class="td-item">
                                                等候的订单数：${staffTodoDto.riskSkuTodoDto.skuTodoListCountGroupByOrder == null ? 0 : staffTodoDto.riskSkuTodoDto.skuTodoListCountGroupByOrder}
                                            </div>
                                            <div class="td-item">
                                                等候平均时长：${staffTodoDto.riskSkuTodoDto.avgWaitTime == null ? 0 : staffTodoDto.riskSkuTodoDto.avgWaitTime}小时
                                            </div>
                                            <div class="td-item">
                                                等候最长时长：${staffTodoDto.riskSkuTodoDto.maxWaitTime == null ? 0 : staffTodoDto.riskSkuTodoDto.maxWaitTime}小时
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="ib-item">
                                <div class="item-left"></div>
                                <div class="item-right">
                                    <div class="item-r-title">
                                        供应商信息待办项
                                    </div>
                                    <div class="item-r-content">
                                        <div class="total-num">
                                            <a class="addtitle_dyn"
                                               href="javascript:void(0);"
                                               tabTitle='{"num":"riskCheckTraderTodoListCount<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"/ezadmin/list/list-supplierTodo?USER_LIST=${userId}",
											              "title":"供应商信息待完善"}'>
                                                ${staffTodoDto.riskTraderTodoDto.traderSupplyTodoListCount == null? 0 : staffTodoDto.riskTraderTodoDto.traderSupplyTodoListCount}
                                            </a>
                                        </div>
                                        <div class="total-desc">
                                            <div class="td-item">
                                                等候的订单数：${staffTodoDto.riskTraderTodoDto.traderSupplyTodoListCountGroupByOrder == null ? 0 : staffTodoDto.riskTraderTodoDto.traderSupplyTodoListCountGroupByOrder}
                                            </div>
                                            <div class="td-item">
                                                等候平均时长：${staffTodoDto.riskTraderTodoDto.avgWaitTime == null ? 0 : staffTodoDto.riskTraderTodoDto.avgWaitTime}小时
                                            </div>
                                            <div class="td-item">
                                                等候最长时长：${staffTodoDto.riskTraderTodoDto.maxWaitTime == null ? 0 : staffTodoDto.riskTraderTodoDto.maxWaitTime}小时
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="info-box">
                    <div class="ib-title">
                        采购
                    </div>
                    <div class="ib-content">
                        <div class="ib-list">

                            <div class="ib-item">
                                <div class="item-left"></div>
                                <div class="item-right">
                                    <div class="item-r-title">
                                        采购任务
                                    </div>
                                    <div class="item-r-content">
                                        <ul class="task-list" id="purchaseTaskul">
                                            <li class="task-item">
                                                <div class="ti-num">
                                                    <a class="addtitle_dyn can-click"
                                                       href="javascript:void(0);"
                                                       tabTitle='{"num":"purchaseTask<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"/order/buyorder/indexPendingPurchase.do?tabFlag=0&orderAsistIdList=-1"}'>
                                                        0
                                                    </a>
                                                </div>
                                                <div class="ti-name">总任务</div>
                                            </li>
                                            <li class="task-item">
                                                <div class="ti-num">
                                                    <a class="addtitle_dyn can-click"
                                                       href="javascript:void(0);"
                                                       tabTitle='{"num":"purchaseTask<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"/order/buyorder/indexPendingPurchase.do?tabFlag=0&orderAsistIdList=-1"}'>
                                                        0
                                                    </a>
                                                </div>
                                                <div class="ti-name">临期数</div>
                                            </li>
                                            <li class="task-item">
                                                <div class="ti-num">
                                                    <a class="addtitle_dyn can-click" href="javascript:void(0);"
                                                       tabTitle='{"num":"purchaseTask<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"/order/buyorder/indexPendingPurchase.do?tabFlag=0&orderAsistIdList=-1"}'>
                                                        0
                                                    </a>
                                                </div>
                                                <div class="ti-name">逾期数</div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="ib-item">
                                <div class="item-left"></div>
                                <div class="item-right">
                                    <div class="item-r-title">
                                        催货任务
                                    </div>
                                    <div class="item-r-content">
                                        <ul class="task-list" id="expeditingGoodsTask">
                                            <li class="task-item">
                                                <div class="ti-num">
                                                    <a class="addtitle_dyn can-click"
                                                       href="javascript:void(0);"
                                                       tabTitle='{"num":"expeditingGoodsTask","link":"/ezadmin/list/list-${ezUrlCg}?CREATOR=EZ_SESSION_USER_ID_KEY&_SEARCH_ITEM_DISPLAY=1&EXPEDITING_STATUS=2","title":"采购订单列表"}'>
                                                        ${staffTodoDto.earlyWarningGoodsTaskToDoDto.totalExpeditingTaskNum ==null? 0 : staffTodoDto.earlyWarningGoodsTaskToDoDto.totalExpeditingTaskNum}
                                                    </a>
                                                </div>
                                                <div class="ti-name">总任务</div>
                                            </li>
                                            <li class="task-item">
                                                <div class="ti-num">
                                                    <a class="addtitle_dyn can-click"
                                                       href="javascript:void(0);"
                                                       tabTitle='{"num":"expeditingGoodsTask<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"/ezadmin/list/list-${ezUrlCg}?CREATOR=EZ_SESSION_USER_ID_KEY&_SEARCH_ITEM_DISPLAY=1&EXPEDITING_STATUS=1","title":"采购订单列表"}'>
                                                        ${staffTodoDto.earlyWarningGoodsTaskToDoDto.adventExpeditingTaskNum ==null? 0 : staffTodoDto.earlyWarningGoodsTaskToDoDto.adventExpeditingTaskNum}
                                                    </a>
                                                </div>
                                                <div class="ti-name">临期数</div>
                                            </li>
                                            <li class="task-item">
                                                <div class="ti-num">
                                                    <a class="addtitle_dyn can-click" href="javascript:void(0);"
                                                       tabTitle='{"num":"expeditingGoodsTask<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"/ezadmin/list/list-${ezUrlCg}?CREATOR=EZ_SESSION_USER_ID_KEY&_SEARCH_ITEM_DISPLAY=1&EXPEDITING_STATUS=2","title":"采购订单列表"}'>
                                                        ${staffTodoDto.earlyWarningGoodsTaskToDoDto.overdueExpeditingTaskNum ==null? 0 : staffTodoDto.earlyWarningGoodsTaskToDoDto.overdueExpeditingTaskNum}
                                                    </a>
                                                </div>
                                                <div class="ti-name">逾期数</div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="ib-item">
                                <div class="item-left"></div>
                                <div class="item-right">
                                    <div class="item-r-title">
                                        催票任务
                                    </div>
                                    <div class="item-r-content">
                                        <ul class="task-list" id="earlyWarningTicksTask">
                                            <li class=" task-item">
                                                <div class="ti-num">
                                                    <a class="can-click"
                                                       href="javascript:void(0);" onclick="showEarlyWarningTicksView(${staffTodoDto.earlyWarningTicksTaskTodoDto.userIdList})" >
                                                        ${staffTodoDto.earlyWarningTicksTaskTodoDto.traderNum == null? 0 : staffTodoDto.earlyWarningTicksTaskTodoDto.traderNum}
                                                    </a>
                                                </div>
                                                <div class="ti-name">供应商数</div>
                                            </li>
                                            <li class="task-item">
                                                <div class="ti-num">
                                                    <a class="can-click"
                                                       href="javascript:void(0);" onclick="showEarlyWarningTicksView(${staffTodoDto.earlyWarningTicksTaskTodoDto.userIdList})" >
                                                        ${staffTodoDto.earlyWarningTicksTaskTodoDto.taskNum == null? 0 : staffTodoDto.earlyWarningTicksTaskTodoDto.taskNum}
                                                    </a>
                                                </div>
                                                <div class="ti-name">催票数</div>
                                            </li>
                                            <li class="task-item">
                                                <div class="ti-num">
                                                    <a class="can-click"
                                                       href="javascript:void(0);" onclick="showEarlyWarningTicksView(${staffTodoDto.earlyWarningTicksTaskTodoDto.userIdList})" >
                                                        ￥${staffTodoDto.earlyWarningTicksTaskTodoDto.totalTicksAmount == null? 0 : staffTodoDto.earlyWarningTicksTaskTodoDto.totalTicksAmount}
                                                    </a>
                                                </div>
                                                <div class="ti-name">催票金额</div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="ib-item">
                                <div class="item-left"></div>
                                <div class="item-right">
                                    <div class="item-r-title">
                                        备货任务
                                    </div>
                                    <div class="item-r-content">
                                        <ul class="task-list">
                                            <li class="task-item">
                                                <div class="ti-num">
                                                    <a class="addtitle_dyn can-click"
                                                       href="javascript:void(0);"
                                                       tabTitle='{"num":"prepareStockTask<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"/flash/prepare/list.do?warnLevel=-1&warnAll=1&taskDealerId=${staffTodoDto.prepareStockTaskToDoDto.userId}"}'>
                                                        ${staffTodoDto.prepareStockTaskToDoDto.totalTaskNum == null? 0 : staffTodoDto.prepareStockTaskToDoDto.totalTaskNum}
                                                    </a>
                                                </div>
                                                <div class="ti-name">总任务</div>
                                            </li>
                                            <li class="task-item">
                                                <div class="ti-num">
                                                    <a class="addtitle_dyn can-click"
                                                       href="javascript:void(0);"
                                                       tabTitle='{"num":"prepareStockTask<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"/flash/prepare/list.do?warnLevel=2&taskDealerId=${staffTodoDto.prepareStockTaskToDoDto.userId}"}'>
                                                        ${staffTodoDto.prepareStockTaskToDoDto.firstLevelTaskNum == null? 0 : staffTodoDto.prepareStockTaskToDoDto.firstLevelTaskNum}
                                                    </a>
                                                </div>
                                                <div class="ti-name">一级预警</div>
                                            </li>
                                            <li class="task-item">
                                                <div class="ti-num">
                                                    <a class="addtitle_dyn can-click" href="javascript:void(0);"
                                                       tabTitle='{"num":"prepareStockTask<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"/flash/prepare/list.do?warnLevel=1&taskDealerId=${staffTodoDto.prepareStockTaskToDoDto.userId}"}'>
                                                        ${staffTodoDto.prepareStockTaskToDoDto.secondLevelTaskNum == null? 0 : staffTodoDto.prepareStockTaskToDoDto.secondLevelTaskNum}
                                                    </a>
                                                </div>
                                                <div class="ti-name">二级预警</div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="common-box">
            <div class="cb-title">
                日常管理任务项
            </div>
            <c:if test="${isShow == 1}">
                <div class="cb-content">
                    <div class="table-box">
                        <div class="tb-title">
                            商品分级分档信息完善待办项
                        </div>
                        <div class="tb-content">
                            <div class="thead">
                                <div class="thead-tr">
                                    <div class="thead-th">
                                        待办事项
                                    </div>
                                     <div class="thead-th">
                                        S+级
                                    </div>
                                      <div class="thead-th">
                                        S级
                                    </div>
                                    <div class="thead-th">
                                        A级
                                    </div>
                                    <div class="thead-th">
                                        B级
                                    </div>
                                    <div class="thead-th">
                                        C级
                                    </div>
                                    <div class="thead-th">
                                        D级
                                    </div>
                                    <div class="thead-th">
                                        E级
                                    </div>
                                </div>
                            </div>
                            <div class="tbody">
                                <div class="tbody-tr">
                                    <div class="tbody-td">
                                        商品信息完善
                                    </div>
                                    <div class="tbody-td">
                                        <a class="addtitle_dyn can-click"
                                           href="javascript:void(0);"
                                           tabTitle='{"num":"maintainSkuAndSpuCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                      "link":"/goods/vgoods/list.do?buzTypeFromTodoList=9&goodsLevelFromTodoList=7&subordinateList=${userId}"}'>
                                            ${staffTodoDto.dailyManagementTodoDto.skuAndSpuCountGroupByGrade[7] == null ? 0 : staffTodoDto.dailyManagementTodoDto.skuAndSpuCountGroupByGrade[7]}
                                        </a>
                                    </div>
                                      <div class="tbody-td">
                                        <a class="addtitle_dyn can-click"
                                           href="javascript:void(0);"
                                           tabTitle='{"num":"maintainSkuAndSpuCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                      "link":"/goods/vgoods/list.do?buzTypeFromTodoList=9&goodsLevelFromTodoList=6&subordinateList=${userId}"}'>
                                            ${staffTodoDto.dailyManagementTodoDto.skuAndSpuCountGroupByGrade[6] == null ? 0 : staffTodoDto.dailyManagementTodoDto.skuAndSpuCountGroupByGrade[6]}
                                        </a>
                                    </div>
                                    <div class="tbody-td">
                                        <a class="addtitle_dyn can-click"
                                           href="javascript:void(0);"
                                           tabTitle='{"num":"maintainSkuAndSpuCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                      "link":"/goods/vgoods/list.do?buzTypeFromTodoList=9&goodsLevelFromTodoList=1&subordinateList=${userId}"}'>
                                            ${staffTodoDto.dailyManagementTodoDto.skuAndSpuCountGroupByGrade[1] == null ? 0 : staffTodoDto.dailyManagementTodoDto.skuAndSpuCountGroupByGrade[1]}
                                        </a>
                                    </div>
                                    <div class="tbody-td can-click">
                                        <a class="addtitle_dyn can-click"
                                           href="javascript:void(0);"
                                           tabTitle='{"num":"maintainSkuAndSpuCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                      "link":"/goods/vgoods/list.do?buzTypeFromTodoList=9&goodsLevelFromTodoList=2&subordinateList=${userId}"}'>
                                            ${staffTodoDto.dailyManagementTodoDto.skuAndSpuCountGroupByGrade[2] == null ? 0 : staffTodoDto.dailyManagementTodoDto.skuAndSpuCountGroupByGrade[2]}
                                        </a>
                                    </div>
                                    <div class="tbody-td can-click">
                                        <a class="addtitle_dyn can-click"
                                           href="javascript:void(0);"
                                           tabTitle='{"num":"maintainSkuAndSpuCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                      "link":"/goods/vgoods/list.do?buzTypeFromTodoList=9&goodsLevelFromTodoList=3&subordinateList=${userId}"}'>
                                            ${staffTodoDto.dailyManagementTodoDto.skuAndSpuCountGroupByGrade[3] == null ? 0 : staffTodoDto.dailyManagementTodoDto.skuAndSpuCountGroupByGrade[3]}
                                        </a>
                                    </div>
                                    <div class="tbody-td can-click">
                                        <a class="addtitle_dyn can-click"
                                           href="javascript:void(0);"
                                           tabTitle='{"num":"maintainSkuAndSpuCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                      "link":"/goods/vgoods/list.do?buzTypeFromTodoList=9&goodsLevelFromTodoList=4&subordinateList=${userId}"}'>
                                            ${staffTodoDto.dailyManagementTodoDto.skuAndSpuCountGroupByGrade[4] == null ? 0 : staffTodoDto.dailyManagementTodoDto.skuAndSpuCountGroupByGrade[4]}
                                        </a>
                                    </div>
                                    <div class="tbody-td can-click">
                                        <a class="addtitle_dyn can-click"
                                           href="javascript:void(0);"
                                           tabTitle='{"num":"maintainSkuAndSpuCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                      "link":"/goods/vgoods/list.do?buzTypeFromTodoList=9&goodsLevelFromTodoList=5&subordinateList=${userId}"}'>
                                                ${staffTodoDto.dailyManagementTodoDto.skuAndSpuCountGroupByGrade[5] == null ? 0 : staffTodoDto.dailyManagementTodoDto.skuAndSpuCountGroupByGrade[5]}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </c:if>

            <div class="cb-content">
                <div class="table-box">
                    <div class="tb-title">
                        商品推送待办项
                    </div>
                    <div class="tb-content">
                        <div class="thead">
                            <div class="thead-tr">
                                <div class="thead-th">
                                    待办事项
                                </div>
                                 <div class="thead-th">
                                    S+级
                                </div>
                                 <div class="thead-th">
                                    S级
                                </div>
                                <div class="thead-th">
                                    A级
                                </div>
                                <div class="thead-th">
                                    B级
                                </div>
                                <div class="thead-th">
                                    C级
                                </div>
                                <div class="thead-th">
                                    D级
                                </div>
                            </div>
                        </div>
                        <div class="tbody">
                           <%-- <div class="tbody-tr">
                                <div class="tbody-td">
                                    预计可发货时间维护
                                </div>
                                <div class="tbody-td">

                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainDeliveryTimeCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"${ezDomain}/ezlist/list/list.html?pageId=${ezIdArray[2]}&GOODS_LEVEL_NO=1&USER_LIST=${userId}",
											      "title":"预计可发货时间"}'>

                                        ${staffTodoDto.dailyManagementTodoDto.deliveryTimeCountGroupByGrade[1] == null ? 0 : staffTodoDto.dailyManagementTodoDto.deliveryTimeCountGroupByGrade[1]}
                                    </a>

                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainDeliveryTimeCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"${ezDomain}/ezlist/list/list.html?pageId=${ezIdArray[2]}&GOODS_LEVEL_NO=2&USER_LIST=${userId}",
											      "title":"预计可发货时间"}'>
                                        ${staffTodoDto.dailyManagementTodoDto.deliveryTimeCountGroupByGrade[2] == null ? 0 : staffTodoDto.dailyManagementTodoDto.deliveryTimeCountGroupByGrade[2]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainDeliveryTimeCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"${ezDomain}/ezlist/list/list.html?pageId=${ezIdArray[3]}&GOODS_LEVEL_NO=3&USER_LIST=${userId}",
											      "title":"预计可发货时间"}'>
                                        ${staffTodoDto.dailyManagementTodoDto.deliveryTimeCountGroupByGrade[3] == null ? 0 : staffTodoDto.dailyManagementTodoDto.deliveryTimeCountGroupByGrade[3]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainDeliveryTimeCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"${ezDomain}/ezlist/list/list.html?pageId=${ezIdArray[4]}&GOODS_LEVEL_NO=4&USER_LIST=${userId}",
											      "title":"预计可发货时间"}'>
                                        ${staffTodoDto.dailyManagementTodoDto.deliveryTimeCountGroupByGrade[4] == null ? 0 : staffTodoDto.dailyManagementTodoDto.deliveryTimeCountGroupByGrade[4]}
                                    </a>
                                </div>
                            </div>--%>
                            <div class="tbody-tr">
                                <div class="tbody-td">
                                    商品核价维护
                                </div>
                                <div class="tbody-td">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainPriceCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/price/basePriceMaintain/index.do?subordinateList=${userId}&includeSkuNosStr=${staffTodoDto.dailyManagementTodoDto.priceSkuList[7]}"}'>
                                        ${staffTodoDto.dailyManagementTodoDto.priceCountGroupByGrade[7] == null ? 0 : staffTodoDto.dailyManagementTodoDto.priceCountGroupByGrade[7]}
                                    </a>
                                </div>
                                <div class="tbody-td">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainPriceCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/price/basePriceMaintain/index.do?subordinateList=${userId}&includeSkuNosStr=${staffTodoDto.dailyManagementTodoDto.priceSkuList[6]}"}'>
                                        ${staffTodoDto.dailyManagementTodoDto.priceCountGroupByGrade[6] == null ? 0 : staffTodoDto.dailyManagementTodoDto.priceCountGroupByGrade[6]}
                                    </a>
                                </div>
                                <div class="tbody-td">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainPriceCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/price/basePriceMaintain/index.do?subordinateList=${userId}&includeSkuNosStr=${staffTodoDto.dailyManagementTodoDto.priceSkuList[1]}"}'>
                                        ${staffTodoDto.dailyManagementTodoDto.priceCountGroupByGrade[1] == null ? 0 : staffTodoDto.dailyManagementTodoDto.priceCountGroupByGrade[1]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainPriceCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/price/basePriceMaintain/index.do?subordinateList=${userId}&includeSkuNosStr=${staffTodoDto.dailyManagementTodoDto.priceSkuList[2]}"}'>
                                        ${staffTodoDto.dailyManagementTodoDto.priceCountGroupByGrade[2] == null ? 0 : staffTodoDto.dailyManagementTodoDto.priceCountGroupByGrade[2]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainPriceCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/price/basePriceMaintain/index.do?subordinateList=${userId}&includeSkuNosStr=${staffTodoDto.dailyManagementTodoDto.priceSkuList[3]}"}'>
                                        ${staffTodoDto.dailyManagementTodoDto.priceCountGroupByGrade[3] == null ? 0 : staffTodoDto.dailyManagementTodoDto.priceCountGroupByGrade[3]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainPriceCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/price/basePriceMaintain/index.do?subordinateList=${userId}&includeSkuNosStr=${staffTodoDto.dailyManagementTodoDto.priceSkuList[4]}"}'>
                                        ${staffTodoDto.dailyManagementTodoDto.priceCountGroupByGrade[4] == null ? 0 : staffTodoDto.dailyManagementTodoDto.priceCountGroupByGrade[4]}
                                    </a>
                                </div>
                            </div>
                            <div class="tbody-tr">
                                <div class="tbody-td">
                                    贝登售后政策维护
                                </div>
                                 <div class="tbody-td">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"aftersalePolicyCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/aftersale/serviceStandard/index.do?subordinateList=${userId}&buzTypeFromTodoList=12&goodsLevelFromTodoList=7"}'>

                                        ${staffTodoDto.dailyManagementTodoDto.aftersalePolicyCountGroupByGrade[7] == null ? 0 : staffTodoDto.dailyManagementTodoDto.aftersalePolicyCountGroupByGrade[7]}
                                    </a>
                                </div>
                                 <div class="tbody-td">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"aftersalePolicyCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/aftersale/serviceStandard/index.do?subordinateList=${userId}&buzTypeFromTodoList=12&goodsLevelFromTodoList=6"}'>

                                        ${staffTodoDto.dailyManagementTodoDto.aftersalePolicyCountGroupByGrade[6] == null ? 0 : staffTodoDto.dailyManagementTodoDto.aftersalePolicyCountGroupByGrade[6]}
                                    </a>
                                </div>
                                <div class="tbody-td">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"aftersalePolicyCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/aftersale/serviceStandard/index.do?subordinateList=${userId}&buzTypeFromTodoList=12&goodsLevelFromTodoList=1"}'>

                                        ${staffTodoDto.dailyManagementTodoDto.aftersalePolicyCountGroupByGrade[1] == null ? 0 : staffTodoDto.dailyManagementTodoDto.aftersalePolicyCountGroupByGrade[1]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"aftersalePolicyCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/aftersale/serviceStandard/index.do?subordinateList=${userId}&buzTypeFromTodoList=12&goodsLevelFromTodoList=2"}'>
                                        ${staffTodoDto.dailyManagementTodoDto.aftersalePolicyCountGroupByGrade[2] == null ? 0 : staffTodoDto.dailyManagementTodoDto.aftersalePolicyCountGroupByGrade[2]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"aftersalePolicyCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/aftersale/serviceStandard/index.do?subordinateList=${userId}&buzTypeFromTodoList=12&goodsLevelFromTodoList=3"}'>
                                        ${staffTodoDto.dailyManagementTodoDto.aftersalePolicyCountGroupByGrade[3] == null ? 0 : staffTodoDto.dailyManagementTodoDto.aftersalePolicyCountGroupByGrade[3]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"aftersalePolicyCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/aftersale/serviceStandard/index.do?subordinateList=${userId}&buzTypeFromTodoList=12&goodsLevelFromTodoList=4"}'>
                                        ${staffTodoDto.dailyManagementTodoDto.aftersalePolicyCountGroupByGrade[4] == null ? 0 : staffTodoDto.dailyManagementTodoDto.aftersalePolicyCountGroupByGrade[4]}
                                    </a>
                                </div>
                            </div>
                            <div class="tbody-tr">
                                <div class="tbody-td">
                                    供应商售后政策维护
                                </div>
                                <div class="tbody-td">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"supplyAftersalePolicyCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/aftersale/serviceStandard/index.do?subordinateList=${userId}&buzTypeFromTodoList=13&goodsLevelFromTodoList=7"}'>

                                        ${staffTodoDto.dailyManagementTodoDto.supplyAftersalePolicyCountGroupByGrade[7] == null ? 0 : staffTodoDto.dailyManagementTodoDto.supplyAftersalePolicyCountGroupByGrade[7]}
                                    </a>
                                </div>
                                <div class="tbody-td">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"supplyAftersalePolicyCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/aftersale/serviceStandard/index.do?subordinateList=${userId}&buzTypeFromTodoList=13&goodsLevelFromTodoList=6"}'>

                                        ${staffTodoDto.dailyManagementTodoDto.supplyAftersalePolicyCountGroupByGrade[6] == null ? 0 : staffTodoDto.dailyManagementTodoDto.supplyAftersalePolicyCountGroupByGrade[6]}
                                    </a>
                                </div>
                                <div class="tbody-td">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"supplyAftersalePolicyCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/aftersale/serviceStandard/index.do?subordinateList=${userId}&buzTypeFromTodoList=13&goodsLevelFromTodoList=1"}'>

                                        ${staffTodoDto.dailyManagementTodoDto.supplyAftersalePolicyCountGroupByGrade[1] == null ? 0 : staffTodoDto.dailyManagementTodoDto.supplyAftersalePolicyCountGroupByGrade[1]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"supplyAftersalePolicyCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/aftersale/serviceStandard/index.do?subordinateList=${userId}&buzTypeFromTodoList=13&goodsLevelFromTodoList=2"}'>
                                        ${staffTodoDto.dailyManagementTodoDto.supplyAftersalePolicyCountGroupByGrade[2] == null ? 0 : staffTodoDto.dailyManagementTodoDto.supplyAftersalePolicyCountGroupByGrade[2]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"supplyAftersalePolicyCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/aftersale/serviceStandard/index.do?subordinateList=${userId}&buzTypeFromTodoList=13&goodsLevelFromTodoList=3"}'>
                                        ${staffTodoDto.dailyManagementTodoDto.supplyAftersalePolicyCountGroupByGrade[3] == null ? 0 : staffTodoDto.dailyManagementTodoDto.supplyAftersalePolicyCountGroupByGrade[3]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"supplyAftersalePolicyCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/aftersale/serviceStandard/index.do?subordinateList=${userId}&buzTypeFromTodoList=13&goodsLevelFromTodoList=4"}'>
                                        ${staffTodoDto.dailyManagementTodoDto.supplyAftersalePolicyCountGroupByGrade[4] == null ? 0 : staffTodoDto.dailyManagementTodoDto.supplyAftersalePolicyCountGroupByGrade[4]}
                                    </a>
                                </div>
                            </div>
                            <div class="tbody-tr">
                                <div class="tbody-td">
                                    运营信息维护
                                </div>
                                <div class="tbody-td">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainOperationInfoCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/goods/vgoods/list.do?subordinateList=${userId}&buzTypeFromTodoList=16&goodsLevelFromTodoList=7"}'>

                                        ${staffTodoDto.dailyManagementTodoDto.operationInfoCountGroupByGrade[7] == null ? 0 : staffTodoDto.dailyManagementTodoDto.operationInfoCountGroupByGrade[7]}
                                    </a>
                                </div>
                                <div class="tbody-td">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainOperationInfoCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/goods/vgoods/list.do?subordinateList=${userId}&buzTypeFromTodoList=16&goodsLevelFromTodoList=6"}'>

                                        ${staffTodoDto.dailyManagementTodoDto.operationInfoCountGroupByGrade[6] == null ? 0 : staffTodoDto.dailyManagementTodoDto.operationInfoCountGroupByGrade[6]}
                                    </a>
                                </div>
                                <div class="tbody-td">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainOperationInfoCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/goods/vgoods/list.do?subordinateList=${userId}&buzTypeFromTodoList=16&goodsLevelFromTodoList=1"}'>

                                        ${staffTodoDto.dailyManagementTodoDto.operationInfoCountGroupByGrade[1] == null ? 0 : staffTodoDto.dailyManagementTodoDto.operationInfoCountGroupByGrade[1]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainOperationInfoCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/goods/vgoods/list.do?subordinateList=${userId}&buzTypeFromTodoList=16&goodsLevelFromTodoList=2"}'>
                                        ${staffTodoDto.dailyManagementTodoDto.operationInfoCountGroupByGrade[2] == null ? 0 : staffTodoDto.dailyManagementTodoDto.operationInfoCountGroupByGrade[2]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainOperationInfoCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/goods/vgoods/list.do?subordinateList=${userId}&buzTypeFromTodoList=16&goodsLevelFromTodoList=3"}'>
                                        ${staffTodoDto.dailyManagementTodoDto.operationInfoCountGroupByGrade[3] == null ? 0 : staffTodoDto.dailyManagementTodoDto.operationInfoCountGroupByGrade[3]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainOperationInfoCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/goods/vgoods/list.do?subordinateList=${userId}&buzTypeFromTodoList=16&goodsLevelFromTodoList=4"}'>
                                        ${staffTodoDto.dailyManagementTodoDto.operationInfoCountGroupByGrade[4] == null ? 0 : staffTodoDto.dailyManagementTodoDto.operationInfoCountGroupByGrade[4]}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="table-box">
                    <div class="tb-title">
                        <span>同行单录入待办项</span>
                    </div>
                    <div class="tb-content">
                        <div class="thead">
                            <div class="thead-tr">
                                <div class="thead-th">
                                    待办事项
                                </div>
                                <div class="thead-th">
                                    总待办
                                </div>
                                <div class="thead-th">
                                    紧急待办
                                </div>
                                <div class="thead-th">
                                    日常待办
                                </div>
                            </div>
                        </div>
                        <div class="tbody" id="skuAndSpuTbody">
                            <div class="tbody-tr">
                                <div class="tbody-td">
                                    同行单数据录入
                                </div>
                                <div class="tbody-td">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"receiptRecordCountGroupByCategory<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                                      "link":"/ezadmin/list/list-${ezIdArray[3]}","title":"同行单录入待办列表页"}'>
                                        ${staffTodoDto.dailyManagementTodoDto.receiptRecordCountGroupByCategory[1] == null ? 0 : staffTodoDto.dailyManagementTodoDto.receiptRecordCountGroupByCategory[1]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"receiptRecordCountGroupByCategory<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                                      "link":"/ezadmin/list/list-${ezIdArray[3]}?URGED_MAINTAIN_BATCH_INFO=1","title":"同行单录入待办列表页"}'>
                                        ${staffTodoDto.dailyManagementTodoDto.receiptRecordCountGroupByCategory[2] == null ? 0 : staffTodoDto.dailyManagementTodoDto.receiptRecordCountGroupByCategory[2]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"receiptRecordCountGroupByCategory<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                                      "link":"/ezadmin/list/list-${ezIdArray[3]}?URGED_MAINTAIN_BATCH_INFO=0","title":"同行单录入待办列表页"}'>
                                        ${staffTodoDto.dailyManagementTodoDto.receiptRecordCountGroupByCategory[3] == null ? 0 : staffTodoDto.dailyManagementTodoDto.receiptRecordCountGroupByCategory[3]}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="table-box">
                    <div class="tb-title">
                        <span>直发可确认收货待办项</span>
                    </div>
                    <div class="tb-content">
                        <div class="thead">
                            <div class="thead-tr">
                                <div class="thead-th">
                                    待办事项
                                </div>
                                <div class="thead-th">
                                    总订单数
                                </div>
                                <div class="thead-th">
                                    近30日订单数
                                </div>
                                <div class="thead-th">
                                    本月订单数
                                </div>
                            </div>
                        </div>
                        <div class="tbody" id="enableReceiveToDo">
                            <div class="tbody-tr">
                                <div class="tbody-td">
                                    直发可确认收货订单数
                                </div>
                                <div class="tbody-td">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"enableReceiveToDoDto<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                                      "link":"/ezadmin/list/list-7ILK0W3lzBg?CREATOR=${userId}&ADDTIMESTR_START=&ADDTIMESTR_END=&DELIVERY_DIRECT=1&VALID_STATUS=1&ARRIVAL_STATUS=0%2C1&EXPRESS_ENABLE_RECEIVE=1&_SEARCH_ITEM_DISPLAY=1","title":"(订单流)采购订单列表"}'>
                                        ${staffTodoDto.enableReceiveToDoDto.totalNum == null ? 0 : staffTodoDto.enableReceiveToDoDto.totalNum}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"enableReceiveToDoDto<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                                      "link":"/ezadmin/list/list-7ILK0W3lzBg?CREATOR=${userId}&ADDTIMESTR_START=${staffTodoDto.enableReceiveToDoDto.recent30DaysDate}&ADDTIMESTR_END=${staffTodoDto.enableReceiveToDoDto.nowDate}&DELIVERY_DIRECT=1&VALID_STATUS=1&ARRIVAL_STATUS=0%2C1&EXPRESS_ENABLE_RECEIVE=1&_SEARCH_ITEM_DISPLAY=1","title":"(订单流)采购订单列表"}'>
                                        ${staffTodoDto.enableReceiveToDoDto.recent30DaysNum == null ? 0 : staffTodoDto.enableReceiveToDoDto.recent30DaysNum}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"enableReceiveToDoDto<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                                      "link":"/ezadmin/list/list-7ILK0W3lzBg?CREATOR=${userId}&ADDTIMESTR_START=${staffTodoDto.enableReceiveToDoDto.firstOfMonth}&ADDTIMESTR_END=${staffTodoDto.enableReceiveToDoDto.nowDate}&DELIVERY_DIRECT=1&VALID_STATUS=1&ARRIVAL_STATUS=0%2C1&EXPRESS_ENABLE_RECEIVE=1&_SEARCH_ITEM_DISPLAY=1","title":"(订单流)采购订单列表"}'>
                                        ${staffTodoDto.enableReceiveToDoDto.currentMonthNum == null ? 0 : staffTodoDto.enableReceiveToDoDto.currentMonthNum}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="pw-right">
        <input type="hidden" class="J-crm-task-url" value="${crmTodoTaskUrl}">
        <tags:welcome_task/>
        <div class="message-total">
            <div class="" style="width: 100%">
                <div class="item-right">
                    <div class="item-r-content">
                        <div style="  font-size: 16px;font-weight: bold;padding: 15px 20px;border-bottom: 1px solid #ebebeb;">
                            ERP（客户&订单）
                        </div>
                        <ul class="task-list" id="messageDiv">
                            <li class="task-item tab" >
                                <div class="ti-name">我的待办</div>
                                <div class="ti-num subTab" >
                                    <a class="addtitle_dyn can-click"  style="color: red"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"purchaseTask<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"/review/workbench/remindReview.do?tabStatus=1"}'>
                                        ${staffTodoDto.reviewTaskToDoDto.remainReviewNum == null? 0 : staffTodoDto.reviewTaskToDoDto.remainReviewNum}
                                    </a>
                                </div>
                            </li>
                            <li class="task-item tab" >
                                <div class="ti-name">我的申请</div>
                                <div class="ti-num subTab">
                                    <a class="addtitle_dyn can-click"  style="color: red"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"purchaseTask<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"/review/workbench/remindReview.do?tabStatus=2"}'>
                                        ${staffTodoDto.reviewTaskToDoDto.submitUnfinishNum == null? 0 : staffTodoDto.reviewTaskToDoDto.submitUnfinishNum }
                                    </a>
                                </div>
                            </li>
                            <li class="task-item tab">
                                <div class="ti-name">未读消息</div>
                                <div class="ti-num subTab">
                                    <a class="addtitle_dyn can-click"  style="color: red"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"moreMessage<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                      "link":"/system/message/index.do?isView=0"}'>
                                        ${staffTodoDto.messageTodoDto.unReadMessageCount}
                                    </a>
                                </div>

                            </li>
                        </ul>
                     </div>
                 </div>
            </div>
        </div>
        <div class="message-list">
            <div class="ml-ul">
                <c:forEach items="${staffTodoDto.messageTodoDto.unReadMessageList}" var="message" varStatus="status">
                    <div class="ml-item">
                        <div class="item-desc">
                            【${message.categoryName}】${message.title}
                        </div>
                        <div class="item-link">
                            <span class="addtitle font-blue"
                                  tabTitle='{"num":"warehousesout<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"${message.url }","title":"消息详情"}'
                                  onclick="read('${message.messageUser.userId}','${message.messageUser.messageUserId}')">${message.content}</span>
                        </div>
                        <div class="item-time">
                            <date:date value ="${message.addTime}" format="yyyy-MM-dd HH:mm:ss"/>
                        </div>
                    </div>
                </c:forEach>

            </div>

            <div class="ml-link-more">
                <a class="addtitle_dyn can-click" href="javascript:void(0);" tabTitle='{"num":"moreMessage<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
							  "link":"/system/message/index.do?isView=0"}'>
                    查看更多
                </a>
                <span style="display:none;"><a class="addtitle can-click" href="javascript:void(0);" id="openList"></a></span>
            </div>
        </div>
    </div>
<script>
    /*
 * 置顶
 */
    function read(userId,messageUserId){
        checkLogin();
        if(userId > 0){
            $.ajax({
                type: "POST",
                url: page_url+"/system/message/editMessageUserIsView.do",
                data: {'userId':userId,'messageUserId':messageUserId},
                dataType:'json',
                success: function(data){
                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            })
        }

    }
    function showEarlyWarningTicksView(userIds){
        var random = Math.ceil(Math.random()*10000);
        if(userIds.length > 0){
            var userIdsString="";
            userIds.forEach(function(e){
                userIdsString = userIdsString+e+",";
            });
            var tital= "{\"num\":\"purchaseTask"+random+"\",\"link\":\"/flash/earlyWarningTicksTask/earlyWarningTicksTask.do?userIds="+escape(userIdsString)+"\"}";
            $("#openList").attr('tabTitle',tital);
            document.getElementById("openList").click()
        }
    }
    function dealwithPurchaseTaskStaff() {
        var random_1 = Math.ceil(Math.random()*10000);
        var random_2 = Math.ceil(Math.random()*10000);
        var random_3 = Math.ceil(Math.random()*10000);
        $.ajax({
            type: "POST",
            async : true,
            url: "./dealwithPurchaseTaskStaff.do",
            dataType:'json',
            success: function(data){
                if(data.code == '0'){

                    var purchaseInfoTotoDto = data.data;

                    //总任务数量
                    var totalTasks = purchaseInfoTotoDto.totalTasks == undefined ? 0 : purchaseInfoTotoDto.totalTasks;

                    //临期任务数
                    var adventTask = purchaseInfoTotoDto.adventTask == undefined ? 0 : purchaseInfoTotoDto.adventTask;

                    //逾期任务数
                    var overdueTask = purchaseInfoTotoDto.overdueTask == undefined ? 0 : purchaseInfoTotoDto.overdueTask;

                    var orderAssitIdList = purchaseInfoTotoDto.orderAssitIdList == undefined ? -1 : purchaseInfoTotoDto.orderAssitIdList;

                    var divs = $("#purchaseTaskul .ti-num");

                    divs.eq(0).find("a").eq(0).html(totalTasks);
                    divs.eq(0).find("a").eq(0).attr("tabTitle",'{"num":"purchaseTask'+random_1+'","link":"/order/buyorder/indexPendingPurchase.do?tabFlag=0&orderAssId='+orderAssitIdList+'"}');

                    divs.eq(1).find("a").eq(0).html(adventTask);
                    divs.eq(1).find("a").eq(0).attr("tabTitle",'{"num":"purchaseTask'+random_2+'","link":"/order/buyorder/indexPendingPurchase.do?tabFlag=0&aging=1&orderAssId='+orderAssitIdList+'"}');

                    divs.eq(2).find("a").eq(0).html(overdueTask);
                    divs.eq(2).find("a").eq(0).attr("tabTitle",'{"num":"purchaseTask'+random_3+'","link":"/order/buyorder/indexPendingPurchase.do?tabFlag=0&aging=2&orderAssId='+orderAssitIdList+'"}');

                }else{
                    layer.alert(data.message);
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    }

    dealwithPurchaseTaskStaff();
</script>
</body>

</html>
