package com.vedeng.erp.buyorder.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.dao.AfterSalesInvoiceMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesInvoice;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.aftersale.domain.entity.AfterBuyorderInvoiceEntity;
import com.vedeng.erp.aftersale.domain.entity.BuyOrderAfterSalesEntity;
import com.vedeng.erp.aftersale.domain.entity.BuyOrderAfterSalesGoodsEntity;
import com.vedeng.erp.aftersale.dto.*;
import com.vedeng.erp.aftersale.mapper.AfterBuyorderGoodsMapper;
import com.vedeng.erp.aftersale.mapper.AfterBuyorderInvoiceMapper;
import com.vedeng.erp.aftersale.mapper.AfterSalesBuyOrderMapper;
import com.vedeng.erp.aftersale.mapstruct.AfterBuyorderInvoiceConvert;
import com.vedeng.erp.aftersale.mapstruct.AfterSalesBuyOrderConvert;
import com.vedeng.erp.aftersale.mapstruct.AfterSalesConvert;
import com.vedeng.erp.aftersale.mapstruct.AfterSalesGoodsConvert;
import com.vedeng.erp.aftersale.service.BuyorderAfterSalesApiService;
import com.vedeng.erp.aftersale.service.AfterSalesApiService;
import com.vedeng.erp.aftersale.service.ExpenseAfterSalesApiService;
import com.vedeng.erp.buyorder.domain.entity.Buyorder;
import com.vedeng.erp.buyorder.mapper.BuyorderMapper;
import com.vedeng.erp.buyorder.service.BuyorderInfoSyncService;
import com.vedeng.erp.buyorder.service.NewAfterBuyorderService;
import com.vedeng.erp.buyorderexpense.common.constant.BuyorderExpenseConstant;
import com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseDetailEntity;
import com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseEntity;
import com.vedeng.erp.buyorderexpense.mapper.BuyorderExpenseDetailMapper;
import com.vedeng.erp.buyorderexpense.mapper.BuyorderExpenseMapper;
import com.vedeng.erp.finance.dto.InvoiceDto;
import com.vedeng.erp.finance.dto.InvoiceReversalDto;
import com.vedeng.erp.finance.service.HxInvoiceApiService;
import com.vedeng.erp.finance.service.InvoiceApiService;
import com.vedeng.erp.finance.service.InvoiceReversalApiService;
import com.vedeng.finance.dao.HxInvoiceMapper;
import com.vedeng.finance.dao.InvoiceMapper;
import com.vedeng.finance.model.HxInvoice;
import com.vedeng.finance.model.Invoice;
import com.vedeng.goods.dto.CoreSkuDto;
import com.vedeng.goods.service.CoreSkuApiService;
import com.vedeng.infrastructure.file.domain.Attachment;
import com.vedeng.infrastructure.file.mapper.AttachmentMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class NewAfterBuyorderServiceImpl implements NewAfterBuyorderService, BuyorderAfterSalesApiService {
    @Resource
    private AfterSalesInvoiceMapper afterSalesInvoiceMapper;

    @Resource
    private AfterSalesMapper afterSalesMapper;

    @Autowired
    private InvoiceReversalApiService invoiceReversalApiService;

    @Autowired
    private InvoiceApiService invoiceApiService;

    @Autowired
    private BuyorderInfoSyncService buyorderInfoSyncService;

    @Autowired
    private ExpenseAfterSalesApiService expenseAfterSalesApiService;

    @Autowired
    private AfterSalesApiService afterSalesApiService;

    @Resource
    @Qualifier("invoiceMapper")
    private InvoiceMapper invoiceMapper;

    @Autowired
    private BuyorderMapper buyorderMapper;

    @Autowired
    private BuyorderExpenseMapper buyorderExpenseMapper;

    @Autowired
    private BuyorderExpenseDetailMapper buyorderExpenseDetailMapper;

    @Autowired
    private CoreSkuApiService coreSkuApiService;

    @Autowired
    private HxInvoiceApiService hxInvoiceApiService;

    @Autowired
    private AfterBuyorderInvoiceConvert afterBuyorderInvoiceConvert;

    @Autowired
    private AfterBuyorderInvoiceMapper afterBuyorderInvoiceMapper;

    @Autowired
    private AfterBuyorderGoodsMapper afterBuyorderGoodsMapper;

    @Autowired
    private HxInvoiceMapper hxInvoiceMapper;

    @Autowired
    private AttachmentMapper globalAttachmentMapper;

    @Autowired
    private AfterSalesConvert afterSalesConvert;

    @Autowired
    private AfterSalesGoodsConvert afterSalesGoodsConvert;

    @Autowired
    private AfterSalesBuyOrderMapper afterSalesBuyOrderMapper;

    @Autowired
    private AfterSalesBuyOrderConvert afterSalesBuyOrderConvert;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void withOutRetureTicket(Integer afterSalesInvoiceId, Integer afterSalesId) {
        CurrentUser user = CurrentUser.getCurrentUser();
        //更新售后退票发票状态为无需退票
        AfterSalesInvoice afterSalesInvoice = new AfterSalesInvoice();
        afterSalesInvoice.setAfterSalesInvoiceId(afterSalesInvoiceId);
        afterSalesInvoice.setIsRefundInvoice(ErpConst.ZERO);
        afterSalesInvoice.setUpdater(user.getId());
        afterSalesInvoice.setModTime(DateUtil.sysTimeMillis());
        afterSalesInvoiceMapper.update(afterSalesInvoice);
        //更新售后单整单退票状态
        buyorderInfoSyncService.calculateAndUpdateInvoiceRefundStatus(afterSalesId);
    }

    @Override
    public void applyCoverTicket(Integer invoiceId, Integer afterSalesId) {
        InvoiceReversalDto invoiceReversalDto = new InvoiceReversalDto();
        invoiceReversalDto.setInvoiceId(invoiceId);
        Invoice invoice = invoiceMapper.selectByPrimaryKey(invoiceId);
        if(SysOptionConstant.ID_503.equals(invoice.getType())){
            //采购票
            Buyorder buyorder = buyorderMapper.selectByPrimaryKey(invoice.getRelatedId());
            //采购售后单
            AfterSales afterSales = afterSalesMapper.selectByPrimaryKey(afterSalesId);
            invoiceReversalDto.setSaleName(buyorder.getTraderName());
            invoiceReversalDto.setReversalBillType(ErpConst.ONE);
            invoiceReversalDto.setReversalBillNo(afterSales.getAfterSalesNo());
        }else if(SysOptionConstant.ID_4126.equals(invoice.getType())){
            //采购费用票
            BuyorderExpenseDetailEntity buyorderExpenseDetailEntity = buyorderExpenseDetailMapper.selectByBuyorderExpenseId(invoice.getRelatedId());
            //采购费用售后单
            ExpenseAfterSalesDto expenseAfterSalesDto = expenseAfterSalesApiService.getAfterSalesAndStatusInfo(Long.parseLong(afterSalesId.toString()));
            BuyorderExpenseEntity buyorderExpenseEntity = buyorderExpenseMapper.selectByPrimaryKey(invoice.getRelatedId());
            invoiceReversalDto.setSaleName(buyorderExpenseDetailEntity.getTraderName());
            invoiceReversalDto.setReversalBillType(ErpConst.TWO);
            invoiceReversalDto.setReversalBillNo(expenseAfterSalesDto.getExpenseAfterSalesNo());
        }else {
            log.info("发票ID{},发票信息{},",invoiceId, JSON.toJSONString(invoice));
            throw new ServiceException(-1,"发票类型错误，申请冲销失败");
        }
        invoiceReversalDto.setInvoiceCode(invoice.getInvoiceCode());
        invoiceReversalDto.setInvoiceNo(invoice.getInvoiceNo());
        invoiceReversalDto.setInvoiceType(invoice.getInvoiceType());
        invoiceReversalDto.setReversalAuditStatus(ErpConst.ZERO);
        invoiceReversalDto.setAuditApplyTime(new Date());
        invoiceReversalApiService.insertSelective(invoiceReversalDto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void verifyCoverSave(Integer invoiceReversalId, boolean isPass, String auditComment) {
        CurrentUser user = CurrentUser.getCurrentUser();
        InvoiceReversalDto invoiceReversalDto = new InvoiceReversalDto();
        //申请冲销信息
        InvoiceReversalDto invoiceReversalInfo = invoiceReversalApiService.queryByPrimaryKey(invoiceReversalId);
        if(!ErpConst.ZERO.equals(invoiceReversalInfo.getReversalAuditStatus())){
            throw new ServiceException("该冲销申请不为待审核状态，无法操作");
        }
        //售后退票的id
        Integer afterSalesInvoiceId = invoiceReversalInfo.getInvoiceId();
        invoiceReversalDto.setInvoiceReversalId(invoiceReversalId);
        invoiceReversalDto.setAuditComments(auditComment);
        invoiceReversalInfo.setAuditComments(auditComment);

        if(ErpConstant.ONE.equals(invoiceReversalInfo.getReversalBillType())) {
            //采购售后单
            AfterSalesDto afterSalesDto = afterSalesApiService.queryInfoByNo(invoiceReversalInfo.getReversalBillNo());
            if(afterSalesDto != null) {
                invoiceReversalInfo.setReversalBillId(afterSalesDto.getAfterSalesId());
                invoiceReversalInfo.setOrderId(afterSalesDto.getOrderId());
            }else {
                log.info("查询采购售后单信息为空，单号{},",invoiceReversalInfo.getInvoiceNo());
                throw new ServiceException("查询采购售后单信息为空");
            }
        }else if(ErpConstant.TWO.equals(invoiceReversalInfo.getReversalBillType())){
            //采购费用单售后
            ExpenseAfterSalesDto expenseAfterSalesDto = expenseAfterSalesApiService.queryExpenseAfterSalesInfoByNo(invoiceReversalInfo.getReversalBillNo());
            if(expenseAfterSalesDto != null) {
                invoiceReversalInfo.setReversalBillId(Integer.parseInt(expenseAfterSalesDto.getExpenseAfterSalesId().toString()));
                invoiceReversalInfo.setOrderId(expenseAfterSalesDto.getBuyorderExpenseId());
                // 自动转单的冲销票审核人为njadmin
                if (Objects.nonNull(expenseAfterSalesDto.getIsAuto()) && expenseAfterSalesDto.getIsAuto().equals(1)) {
                    user.setId(BuyorderExpenseConstant.NJADMIN_ID);
                    user.setUsername(BuyorderExpenseConstant.NJADMIN_NAME);
                }
            }else {
                log.info("查询采购费用售后单信息为空，单号{},",invoiceReversalInfo.getInvoiceNo());
                throw new ServiceException("查询采购费用售后单信息为空");
            }
        }else {
            log.info("冲销审核通过，售后单类型错误，售后单号{},类型为{},",invoiceReversalInfo.getInvoiceNo(),invoiceReversalInfo.getReversalBillType());
            throw new ServiceException("售后单类型错误");
        }
        if(isPass){
            //true 审核通过
            invoiceReversalDto.setReversalAuditStatus(ErpConst.ONE);
            //生成发票针对该采购单的录入信息的冲销蓝字作废信息（红票）
            Integer coverInvoiceId = invoiceApiService.coverInvoiceInfoSave(invoiceReversalInfo,invoiceReversalInfo.getReversalBillNo(),invoiceReversalInfo.getReversalBillType(),user.getId());
            //保存冲销生成的发票信息
            invoiceReversalDto.setInvoiceId(coverInvoiceId);
            //更新退票状态
            if(ErpConst.ONE.equals(invoiceReversalInfo.getReversalBillType())){
                //采购售后
                afterSalesApiService.updateAfterSalesInvoiceStatus(invoiceReversalInfo.getReversalBillId(),afterSalesInvoiceId);
            }else {
                //采购费用售后
                long expenseAfterSalesId = Long.parseLong(invoiceReversalInfo.getReversalBillId().toString());
                ReturnInvoiceWriteBackDto  returnInvoiceWriteBackDto = new ReturnInvoiceWriteBackDto();
                returnInvoiceWriteBackDto.setInvoiceNo(invoiceReversalInfo.getInvoiceNo());
                returnInvoiceWriteBackDto.setExpenseAfterSalesId(expenseAfterSalesId);
                returnInvoiceWriteBackDto.setInvoiceId(coverInvoiceId);
                returnInvoiceWriteBackDto.setInvoiceCode(invoiceReversalInfo.getInvoiceCode());
                expenseAfterSalesApiService.updateReversalInvoiceGoodsData(returnInvoiceWriteBackDto);
                //调用费用售后单状态更新方法
                expenseAfterSalesApiService.calculateAndUpdateInvoiceReturnStatus(expenseAfterSalesId);
                try {
                    expenseAfterSalesApiService.completeExpenseAfterSales(expenseAfterSalesId);
                } catch (Exception e) {
                    log.error("自动完结失败，费用售后单ID[{}]，失败原因[{}]", expenseAfterSalesId, e.getMessage());
                }
            }

            saveRelatedIssueLogId(invoiceReversalInfo);
        }else {
            //审核不通过
            invoiceReversalDto.setReversalAuditStatus(ErpConst.TWO);
            // 采购费用售后，退票状态回滚
            if (!ErpConst.ONE.equals(invoiceReversalInfo.getReversalBillType())) {
                long expenseAfterSalesId = Long.parseLong(invoiceReversalInfo.getReversalBillId().toString());
                ReturnInvoiceWriteBackDto returnInvoiceWriteBackDto = new ReturnInvoiceWriteBackDto();
                returnInvoiceWriteBackDto.setInvoiceNo(invoiceReversalInfo.getInvoiceNo());
                returnInvoiceWriteBackDto.setExpenseAfterSalesId(expenseAfterSalesId);
                returnInvoiceWriteBackDto.setInvoiceCode(invoiceReversalInfo.getInvoiceCode());
                expenseAfterSalesApiService.updateReturnInvoiceStatus(returnInvoiceWriteBackDto);
            }
        }
        invoiceReversalDto.setReversalAuditUser(user.getId());
        invoiceReversalApiService.updateByPrimaryKeySelective(invoiceReversalDto);
    }

    /**
     * 冲销处理出库记录关联关系
     * @param invoiceReversalInfo
     */
    private void saveRelatedIssueLogId(InvoiceReversalDto invoiceReversalInfo) {
        try {
            log.info("冲销审核处理出库关系业务 invoiceReversalInfo:{}", JSON.toJSONString(invoiceReversalInfo));
            if (!ErpConstant.ONE.equals(invoiceReversalInfo.getReversalBillType())) {
                return;
            }

            AfterSalesDto afterSalesDto = afterSalesApiService.queryInfoByNo(invoiceReversalInfo.getReversalBillNo());

            if (!Objects.nonNull(afterSalesDto)){
                throw new RuntimeException("冲销处理出库记录关联关系检索售后信息异常");
            }

            log.info("冲销审核通过调用出库日志关联接口 invoiceReversalInfo:{},orderId:{}",
                    JSON.toJSONString(invoiceReversalInfo), afterSalesDto.getOrderId());
            invoiceApiService.rollbackRelatedWarehousingLog(invoiceReversalInfo.getInvoiceNo()
                    , invoiceReversalInfo.getInvoiceCode(), afterSalesDto.getOrderId());
        } catch (Exception e) {
            log.error("冲销审核处理出库关系业务错误 invoiceReversalInfo:{}", JSON.toJSONString(invoiceReversalInfo), e);
        }
    }

    @Override
    public List<AfterBuyorderInvoiceGoodsDto> queryAllCanBackInvoiceGoods(Integer buyorderId) {
        //采购单所有审核通过的蓝字有效发票的正向录票数量
        List<AfterBuyorderInvoiceGoodsDto> plusInvoiceGoodsList = invoiceMapper.queryPlusInvoiceBuyorderGoods(buyorderId);
        log.info("查询到采购单正向录票信息{},",JSON.toJSONString(plusInvoiceGoodsList));
        //采购单所有审核通过的负向发票数量
        List<AfterBuyorderInvoiceGoodsDto> minusInvoiceGoodsList = invoiceMapper.queryMinusInvoiceBuyorderGoods(buyorderId);
        log.info("查询到采购单负向录票信息{},",JSON.toJSONString(minusInvoiceGoodsList));
        plusInvoiceGoodsList.stream().forEach(item -> {
            //遍历蓝字有效发票商品信息，剔除已退票的数量
            for(AfterBuyorderInvoiceGoodsDto afterBuyorderInvoiceGoodsDto : minusInvoiceGoodsList){
                if((item.getInvoiceNo().equals(afterBuyorderInvoiceGoodsDto.getInvoiceNo()) || item.getInvoiceNo().equals(afterBuyorderInvoiceGoodsDto.getOriginInvoiceNo()))
                        && item.getDetailGoodsId().equals(afterBuyorderInvoiceGoodsDto.getDetailGoodsId())){
                    //蓝字作废发票号=蓝字有效发票号，红字有效原发票号=蓝字有效发票号
                    item.setNum(item.getInvoiceNum().subtract(afterBuyorderInvoiceGoodsDto.getInvoiceNum().abs()));
                    //已扣除的数量置为0
                    afterBuyorderInvoiceGoodsDto.setInvoiceNum(BigDecimal.ZERO);
                }
            }
        });
        log.info("扣除售后后的录票信息{},",JSON.toJSONString(plusInvoiceGoodsList));
        plusInvoiceGoodsList = plusInvoiceGoodsList.stream().filter(item -> item.getNum().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        log.info("数量大于0的录票信息可申请退票{}",JSON.toJSONString(plusInvoiceGoodsList));
        //查询商品信息
        for(AfterBuyorderInvoiceGoodsDto afterBuyorderInvoiceGoodsDto : plusInvoiceGoodsList) {
            CoreSkuDto coreSkuDto = coreSkuApiService.getCoreSkuDtoBySkuId(afterBuyorderInvoiceGoodsDto.getGoodsId());
            if(coreSkuDto == null){
                throw new ServiceException("存在售后商品不为启用状态");
            }
            afterBuyorderInvoiceGoodsDto.setBrandName(coreSkuDto.getBrandName());
            afterBuyorderInvoiceGoodsDto.setMaterialCode(coreSkuDto.getMaterialCode());
            afterBuyorderInvoiceGoodsDto.setModel(coreSkuDto.getModel());
            afterBuyorderInvoiceGoodsDto.setSpec(coreSkuDto.getSpec());
            afterBuyorderInvoiceGoodsDto.setGoodsName(coreSkuDto.getSkuName());
        }
        return plusInvoiceGoodsList;
    }

    @Override
    public boolean verifyHxInvoiceLeftAmount(String invoiceNo, String invocieCode, BigDecimal amount) {
        //查询航信发票可录票金额
        BigDecimal leftInvoiceAmount = hxInvoiceApiService.queryLeftAmountByInvoiceNo(invoiceNo,invocieCode);
        if(leftInvoiceAmount == null){
            log.info("未查询到航信发票信息{},{}",invoiceNo,invocieCode);
            return true;
        }else {
            log.info("查询到发票{},可录票金额{},售后发票金额{},",invoiceNo,leftInvoiceAmount,amount);
            BigDecimal leftInvoiceAmountDown = leftInvoiceAmount.subtract(new BigDecimal(1));
            BigDecimal leftInvoiceAmountUp = leftInvoiceAmount.add(new BigDecimal(1));
            if(amount.compareTo(leftInvoiceAmountUp) <= 0){
                return true;
            }else {
                return false;
            }
        }
    }

    @Override
    public AfterSalesDto viewTpDetail(Integer afterSalesId) {
        AfterSalesDto afterSalesDto = afterSalesApiService.queryInfoById(afterSalesId);
        //查询仅退票商品信息
        List<AfterBuyorderGoodsDto> afterBuyorderGoodsDtoList = afterBuyorderGoodsMapper.queryAfterBuyorderGoodsById(afterSalesId);
        afterSalesDto.setAfterBuyorderGoodsDtoList(afterBuyorderGoodsDtoList);
        //计算退票金额
        BigDecimal afterReturnAmount = afterBuyorderGoodsDtoList.stream().map(item -> {
            BigDecimal afterAmount = item.getBuyPrice().multiply(item.getAfterInvoiceNum());
            return afterAmount;
        }).reduce(BigDecimal.ZERO,BigDecimal :: add);
        if(CollectionUtils.isNotEmpty(afterSalesDto.getReturnBuyorderInvoiceDto())){
            afterSalesDto.getReturnBuyorderInvoiceDto().get(0).setAfterInvoiceAmount(afterReturnAmount);
            //查询需退票发票信息
            InvoiceDto invoiceDto = invoiceApiService.queryInvoiceByInvoiceNoAndRelatedId(afterSalesDto.getReturnBuyorderInvoiceDto().get(0).getInvoiceNo(),
                    afterSalesDto.getOrderId(),SysOptionConstant.ID_503);
            afterSalesDto.getReturnBuyorderInvoiceDto().get(0).setInvoiceType(invoiceDto.getInvoiceType());
        }
        //查询售后发票信息
        List<InvoiceDto> invoiceDtoList = invoiceApiService.getInvoiceListByAfterSalesId(afterSalesDto.getAfterSalesId(),503);
        afterSalesDto.setInvoiceDtoList(invoiceDtoList);
        //查询附件信息
        List<Attachment> attachments = globalAttachmentMapper.queryListByRelatedIdAndFunction(afterSalesDto.getAfterSalesId(),afterSalesDto.getType());
        afterSalesDto.setAttachmentList(attachments);
        return afterSalesDto;
    }

    @Override
    public int saveAfterBuyorderInvoiceInfo(AfterBuyorderInvoiceDto afterBuyorderInvoiceDto) {
        AfterBuyorderInvoiceEntity afterBuyorderInvoiceEntity = afterBuyorderInvoiceConvert.toEntity(afterBuyorderInvoiceDto);
        int i = afterBuyorderInvoiceMapper.insertSelective(afterBuyorderInvoiceEntity);
        return i;
    }

    @Override
    public void closeTpAfterSales(Integer afterSalesId, Integer buyorderId) {
        //更新售后表状态--已关闭
        afterSalesApiService.updateAfterStatusById(afterSalesId,ErpConst.THREE);
        //解锁采购单
        Buyorder buyorder = new Buyorder();
        buyorder.setBuyorderId(buyorderId);
        buyorder.setLockedStatus(ErpConst.ZERO);
        buyorderMapper.updateByPrimaryKeySelective(buyorder);
    }

    @Override
    public void saveTpReturnInvoice(Invoice invoice, List<Integer> detailGoodsIdList, List<BigDecimal> invoiceAmountList, List<BigDecimal> invoicePriceList, List<BigDecimal> invoiceNumList) {
        //仅退票发票退票
        AfterSales afterSales = afterSalesMapper.selectByPrimaryKey(invoice.getAfterSalesId());
        if(ErpConst.THREE.equals(afterSales.getInvoiceRefundStatus())){
            //全部退票的情况下，不允许退票
            throw new ServiceException("售后单已全部退票");
        }
        log.info("手动录票检查发票信息 invoice:{}", JSON.toJSONString(invoice));
        HxInvoice hxInvoice = hxInvoiceMapper.getRecentHxInvoiceInfoByCondition(invoice.getInvoiceCode(), invoice.getInvoiceNo());
        if (hxInvoice != null){
            //维护ERP发票信息和航信发票信息关联关系
            invoice.setHxInvoiceId(hxInvoice.getHxInvoiceId());
            invoice.setInvoiceFrom(ErpConst.ONE);
            switch (hxInvoice.getInvoiceStatus()) {
                case 3: {
                    throw new ServiceException("该发票航信已审核通过，无法再次录票。");
                }
                case 4: {
                    throw new ServiceException("该发票航信已推送，处于待认领环节，请联系财务处理。");
                }
                case 6: {
                    throw new ServiceException("该发票航信已推送，已被标记为异常发票，请联系财务处理。");
                }
                case 7: {
                    throw new ServiceException("该发票为负数票，无法录票。");
                }
                case 12:{
                    throw new ServiceException("当前发票已经作废，不可录入！");
                }
            }
        }
        InvoiceDto invoiceDto = new InvoiceDto();
        invoiceDto.setInvoiceNo(invoice.getInvoiceNo());
        invoiceDto.setInvoiceCode(invoice.getInvoiceCode());
        invoiceDto.setRelatedId(invoice.getRelatedId());
        invoiceDto.setAfterSalesId(invoice.getAfterSalesId());
        invoiceDto.setInvoiceType(invoice.getInvoiceType());
        invoiceDto.setValidStatus(invoice.getValidStatus());
        invoiceDto.setTag(invoice.getTag());
        invoiceDto.setType(invoice.getType());
        invoiceDto.setInvoiceProperty(invoice.getInvoiceProperty());
        invoiceDto.setRatio(invoice.getRatio());
        invoiceDto.setOldInvoiceNo(invoice.getOldInvoiceNo());
        invoiceApiService.saveBuyorderTpRedInfo(invoiceDto,detailGoodsIdList,invoiceAmountList,invoicePriceList,invoiceNumList);
        //更新退票状态
        List<AfterSalesInvoice> afterSalesInvoiceList = afterSalesInvoiceMapper.getAfterSalesInvoiceByAfterSaleId(invoice.getAfterSalesId());
        AfterSalesInvoice afterSalesInvoice = new AfterSalesInvoice();
        afterSalesInvoice.setAfterSalesInvoiceId(afterSalesInvoiceList.get(0).getAfterSalesInvoiceId());
        afterSalesInvoice.setStatus(ErpConst.ONE);
        afterSalesInvoiceMapper.update(afterSalesInvoice);
        //计算售后单退票状态(默认红字退票限制只能退一次)
        buyorderInfoSyncService.calculateAndUpdateInvoiceRefundStatus(invoice.getAfterSalesId());
        //更新保存新蓝字有效发票
        invoiceApiService.saveTpNewInvoiceInfo(invoiceDto,invoice.getAfterSalesId(),detailGoodsIdList, invoiceAmountList,invoicePriceList,invoiceNumList);
        //更新售后单状态为已完结
        afterSalesApiService.updateAfterStatusById(invoice.getAfterSalesId(),ErpConst.TWO);
        //解锁采购单
        Buyorder buyorder = new Buyorder();
        buyorder.setBuyorderId(invoice.getRelatedId());
        buyorder.setLockedStatus(ErpConst.ZERO);
        // FAQ5589
        buyorder.setServiceStatus(ErpConst.TWO);
        buyorderMapper.updateByPrimaryKeySelective(buyorder);
    }

    @Override
    public AfterBuyorderInvoiceDto queryInfoByAfterSalesId(Integer afterSalesId) {
        return afterBuyorderInvoiceMapper.queryInfoByAfterSalesId(afterSalesId);
    }

    @Override
    public int updateByPrimaryKeySelective(AfterBuyorderInvoiceDto afterBuyorderInvoiceDto) {
        AfterBuyorderInvoiceEntity afterBuyorderInvoiceEntity = afterBuyorderInvoiceConvert.toEntity(afterBuyorderInvoiceDto);
        return afterBuyorderInvoiceMapper.updateByPrimaryKeySelective(afterBuyorderInvoiceEntity);
    }

    @Override
    public Integer getAfterGoodsIdByCondition(AfterSaleGoodsIdQueryReqDto afterSaleGoodsIdQueryReqDto) {
        log.info("条件检索售后商品ID afterSaleGoodsIdQueryReqDto:{}", JSON.toJSONString(afterSaleGoodsIdQueryReqDto));

        if (!AfterSaleGoodsIdQueryReqDto.verifyParam(afterSaleGoodsIdQueryReqDto)){
            log.error("条件检索售后商品ID参数不合规 afterSaleGoodsIdQueryReqDto:{}", JSON.toJSONString(afterSaleGoodsIdQueryReqDto));
            throw new RuntimeException("条件检索售后商品ID参数不合规");
        }
        return afterBuyorderGoodsMapper.getAfterGoodsIdByCondition(afterSaleGoodsIdQueryReqDto);
    }

    @Override
    public BuyOrderAfterSalesDto getByAfterSalesId(Integer afterSalesId) {
        return afterSalesConvert.toDto(afterSalesMapper.selectByPrimaryKey(afterSalesId));
    }

    /**
     * 根据售后单ID查询普通售后商品信息(普通商品)
     * @param afterSalesId
     * @return
     */
    @Override
    public List<AfterSalesGoodsDto> getAfterSalesGoodsByAfterSalesId(Integer afterSalesId) {
        log.info("根据售后单ID查询普通售后商品信息(普通商品) afterSalesId:{}", afterSalesId);
        List<BuyOrderAfterSalesGoodsEntity> allByAfterSalesId = afterBuyorderGoodsMapper.findAllByAfterSalesId(afterSalesId);
        List<AfterSalesGoodsDto> afterSalesGoodsDtos = afterSalesGoodsConvert.toDto(allByAfterSalesId);
        return afterSalesGoodsDtos;
    }

    /**
     * 根据售后单ID查询售后信息
     * @param afterSalesId
     * @return
     */
    @Override
    public AfterSalesApiDto getAfterSalesByAfterSalesId(Integer afterSalesId) {
        BuyOrderAfterSalesEntity afterSalesEntity = afterSalesBuyOrderMapper.selectByPrimaryKey(afterSalesId);
        AfterSalesApiDto afterSalesApiDto = afterSalesBuyOrderConvert.toDto(afterSalesEntity);
        return afterSalesApiDto;
    }

    /**
     * 根据购买单ID查询售后信息
     * @param buyOrderId
     * @return
     */
    @Override
    public List<AfterSalesApiDto> getAllAfterSalesByBuyOrderId(Integer buyOrderId) {
        List<BuyOrderAfterSalesEntity> afterSalesEntityList = afterSalesBuyOrderMapper.findByOrderId(buyOrderId);
        if (CollUtil.isEmpty(afterSalesEntityList)){
            log.info("根据购买单ID查询售后信息为空 buyOrderId:{}", buyOrderId);
            return null;
        }
        List<AfterSalesApiDto> afterSalesApiDto = afterSalesBuyOrderConvert.toDto(afterSalesEntityList);
        return afterSalesApiDto;
    }

}
