package com.vedeng.ordergoods.model;

import java.math.BigDecimal;
import java.util.Date;

public class SaleorderGoodsGenerate {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.SALEORDER_GOODS_ID
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Integer saleorderGoodsId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.SALEORDER_ID
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Integer saleorderId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.GOODS_ID
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Integer goodsId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.SKU
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private String sku;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.GOODS_NAME
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private String goodsName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.BRAND_NAME
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private String brandName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.MODEL
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private String model;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.UNIT_NAME
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private String unitName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.PRICE
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private BigDecimal price;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.REAL_PRICE
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private BigDecimal realPrice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.PURCHASING_PRICE
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private BigDecimal purchasingPrice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.CURRENCY_UNIT_ID
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Integer currencyUnitId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.NUM
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Integer num;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.BUY_NUM
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Integer buyNum;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.DELIVERY_NUM
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Integer deliveryNum;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.DELIVERY_STATUS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Byte deliveryStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.DELIVERY_TIME
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Long deliveryTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.DELIVERY_CYCLE
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private String deliveryCycle;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.DELIVERY_DIRECT
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Integer deliveryDirect;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.DELIVERY_DIRECT_COMMENTS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private String deliveryDirectComments;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.REGISTRATION_NUMBER
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private String registrationNumber;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.SUPPLIER_NAME
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private String supplierName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.REFERENCE_COST_PRICE
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private BigDecimal referenceCostPrice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.REFERENCE_PRICE
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private String referencePrice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.REFERENCE_DELIVERY_CYCLE
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private String referenceDeliveryCycle;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.REPORT_STATUS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Byte reportStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.REPORT_COMMENTS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private String reportComments;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.HAVE_INSTALLATION
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Byte haveInstallation;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.GOODS_COMMENTS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private String goodsComments;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.INSIDE_COMMENTS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private String insideComments;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.ARRIVAL_USER_ID
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Integer arrivalUserId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.ARRIVAL_STATUS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Byte arrivalStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.ARRIVAL_TIME
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Long arrivalTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.IS_DELETE
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Integer isDelete;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.IS_IGNORE
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Byte isIgnore;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.IGNORE_TIME
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Long ignoreTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.IGNORE_USER_ID
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Integer ignoreUserId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.LOCKED_STATUS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Byte lockedStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.MAX_SKU_REFUND_AMOUNT
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private BigDecimal maxSkuRefundAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.ADD_TIME
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Long addTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.CREATOR
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Integer creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.MOD_TIME
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Long modTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.UPDATER
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Integer updater;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.OCCUPY_NUM
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Integer occupyNum;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.IS_ACTION_GOODS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Byte isActionGoods;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.ACTION_OCCUPY_NUM
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Integer actionOccupyNum;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.IS_COUPONS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Byte isCoupons;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.EL_ORDERLIST_ID
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Integer elOrderlistId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.UPDATE_DATA_TIME
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Date updateDataTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.REAL_PAY_AMOUNT
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private BigDecimal realPayAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.AFTER_RETURN_AMOUNT
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private BigDecimal afterReturnAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_GOODS.AFTER_RETURN_NUM
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    private Integer afterReturnNum;

    private Integer isGift;

    public Integer getIsGift() {
        return isGift;
    }

    public void setIsGift(Integer isGift) {
        this.isGift = isGift;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.SALEORDER_GOODS_ID
     *
     * @return the value of T_SALEORDER_GOODS.SALEORDER_GOODS_ID
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Integer getSaleorderGoodsId() {
        return saleorderGoodsId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.SALEORDER_GOODS_ID
     *
     * @param saleorderGoodsId the value for T_SALEORDER_GOODS.SALEORDER_GOODS_ID
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setSaleorderGoodsId(Integer saleorderGoodsId) {
        this.saleorderGoodsId = saleorderGoodsId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.SALEORDER_ID
     *
     * @return the value of T_SALEORDER_GOODS.SALEORDER_ID
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Integer getSaleorderId() {
        return saleorderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.SALEORDER_ID
     *
     * @param saleorderId the value for T_SALEORDER_GOODS.SALEORDER_ID
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setSaleorderId(Integer saleorderId) {
        this.saleorderId = saleorderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.GOODS_ID
     *
     * @return the value of T_SALEORDER_GOODS.GOODS_ID
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Integer getGoodsId() {
        return goodsId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.GOODS_ID
     *
     * @param goodsId the value for T_SALEORDER_GOODS.GOODS_ID
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.SKU
     *
     * @return the value of T_SALEORDER_GOODS.SKU
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public String getSku() {
        return sku;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.SKU
     *
     * @param sku the value for T_SALEORDER_GOODS.SKU
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setSku(String sku) {
        this.sku = sku;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.GOODS_NAME
     *
     * @return the value of T_SALEORDER_GOODS.GOODS_NAME
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public String getGoodsName() {
        return goodsName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.GOODS_NAME
     *
     * @param goodsName the value for T_SALEORDER_GOODS.GOODS_NAME
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.BRAND_NAME
     *
     * @return the value of T_SALEORDER_GOODS.BRAND_NAME
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public String getBrandName() {
        return brandName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.BRAND_NAME
     *
     * @param brandName the value for T_SALEORDER_GOODS.BRAND_NAME
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.MODEL
     *
     * @return the value of T_SALEORDER_GOODS.MODEL
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public String getModel() {
        return model;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.MODEL
     *
     * @param model the value for T_SALEORDER_GOODS.MODEL
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setModel(String model) {
        this.model = model;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.UNIT_NAME
     *
     * @return the value of T_SALEORDER_GOODS.UNIT_NAME
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public String getUnitName() {
        return unitName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.UNIT_NAME
     *
     * @param unitName the value for T_SALEORDER_GOODS.UNIT_NAME
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.PRICE
     *
     * @return the value of T_SALEORDER_GOODS.PRICE
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.PRICE
     *
     * @param price the value for T_SALEORDER_GOODS.PRICE
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.REAL_PRICE
     *
     * @return the value of T_SALEORDER_GOODS.REAL_PRICE
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public BigDecimal getRealPrice() {
        return realPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.REAL_PRICE
     *
     * @param realPrice the value for T_SALEORDER_GOODS.REAL_PRICE
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setRealPrice(BigDecimal realPrice) {
        this.realPrice = realPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.PURCHASING_PRICE
     *
     * @return the value of T_SALEORDER_GOODS.PURCHASING_PRICE
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public BigDecimal getPurchasingPrice() {
        return purchasingPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.PURCHASING_PRICE
     *
     * @param purchasingPrice the value for T_SALEORDER_GOODS.PURCHASING_PRICE
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setPurchasingPrice(BigDecimal purchasingPrice) {
        this.purchasingPrice = purchasingPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.CURRENCY_UNIT_ID
     *
     * @return the value of T_SALEORDER_GOODS.CURRENCY_UNIT_ID
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Integer getCurrencyUnitId() {
        return currencyUnitId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.CURRENCY_UNIT_ID
     *
     * @param currencyUnitId the value for T_SALEORDER_GOODS.CURRENCY_UNIT_ID
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setCurrencyUnitId(Integer currencyUnitId) {
        this.currencyUnitId = currencyUnitId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.NUM
     *
     * @return the value of T_SALEORDER_GOODS.NUM
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Integer getNum() {
        return num;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.NUM
     *
     * @param num the value for T_SALEORDER_GOODS.NUM
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setNum(Integer num) {
        this.num = num;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.BUY_NUM
     *
     * @return the value of T_SALEORDER_GOODS.BUY_NUM
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Integer getBuyNum() {
        return buyNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.BUY_NUM
     *
     * @param buyNum the value for T_SALEORDER_GOODS.BUY_NUM
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setBuyNum(Integer buyNum) {
        this.buyNum = buyNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.DELIVERY_NUM
     *
     * @return the value of T_SALEORDER_GOODS.DELIVERY_NUM
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Integer getDeliveryNum() {
        return deliveryNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.DELIVERY_NUM
     *
     * @param deliveryNum the value for T_SALEORDER_GOODS.DELIVERY_NUM
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setDeliveryNum(Integer deliveryNum) {
        this.deliveryNum = deliveryNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.DELIVERY_STATUS
     *
     * @return the value of T_SALEORDER_GOODS.DELIVERY_STATUS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Byte getDeliveryStatus() {
        return deliveryStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.DELIVERY_STATUS
     *
     * @param deliveryStatus the value for T_SALEORDER_GOODS.DELIVERY_STATUS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setDeliveryStatus(Byte deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.DELIVERY_TIME
     *
     * @return the value of T_SALEORDER_GOODS.DELIVERY_TIME
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Long getDeliveryTime() {
        return deliveryTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.DELIVERY_TIME
     *
     * @param deliveryTime the value for T_SALEORDER_GOODS.DELIVERY_TIME
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setDeliveryTime(Long deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.DELIVERY_CYCLE
     *
     * @return the value of T_SALEORDER_GOODS.DELIVERY_CYCLE
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public String getDeliveryCycle() {
        return deliveryCycle;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.DELIVERY_CYCLE
     *
     * @param deliveryCycle the value for T_SALEORDER_GOODS.DELIVERY_CYCLE
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setDeliveryCycle(String deliveryCycle) {
        this.deliveryCycle = deliveryCycle;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.DELIVERY_DIRECT
     *
     * @return the value of T_SALEORDER_GOODS.DELIVERY_DIRECT
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Integer getDeliveryDirect() {
        return deliveryDirect;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.DELIVERY_DIRECT
     *
     * @param deliveryDirect the value for T_SALEORDER_GOODS.DELIVERY_DIRECT
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setDeliveryDirect(Integer deliveryDirect) {
        this.deliveryDirect = deliveryDirect;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.DELIVERY_DIRECT_COMMENTS
     *
     * @return the value of T_SALEORDER_GOODS.DELIVERY_DIRECT_COMMENTS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public String getDeliveryDirectComments() {
        return deliveryDirectComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.DELIVERY_DIRECT_COMMENTS
     *
     * @param deliveryDirectComments the value for T_SALEORDER_GOODS.DELIVERY_DIRECT_COMMENTS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setDeliveryDirectComments(String deliveryDirectComments) {
        this.deliveryDirectComments = deliveryDirectComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.REGISTRATION_NUMBER
     *
     * @return the value of T_SALEORDER_GOODS.REGISTRATION_NUMBER
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public String getRegistrationNumber() {
        return registrationNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.REGISTRATION_NUMBER
     *
     * @param registrationNumber the value for T_SALEORDER_GOODS.REGISTRATION_NUMBER
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setRegistrationNumber(String registrationNumber) {
        this.registrationNumber = registrationNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.SUPPLIER_NAME
     *
     * @return the value of T_SALEORDER_GOODS.SUPPLIER_NAME
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public String getSupplierName() {
        return supplierName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.SUPPLIER_NAME
     *
     * @param supplierName the value for T_SALEORDER_GOODS.SUPPLIER_NAME
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.REFERENCE_COST_PRICE
     *
     * @return the value of T_SALEORDER_GOODS.REFERENCE_COST_PRICE
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public BigDecimal getReferenceCostPrice() {
        return referenceCostPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.REFERENCE_COST_PRICE
     *
     * @param referenceCostPrice the value for T_SALEORDER_GOODS.REFERENCE_COST_PRICE
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setReferenceCostPrice(BigDecimal referenceCostPrice) {
        this.referenceCostPrice = referenceCostPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.REFERENCE_PRICE
     *
     * @return the value of T_SALEORDER_GOODS.REFERENCE_PRICE
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public String getReferencePrice() {
        return referencePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.REFERENCE_PRICE
     *
     * @param referencePrice the value for T_SALEORDER_GOODS.REFERENCE_PRICE
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setReferencePrice(String referencePrice) {
        this.referencePrice = referencePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.REFERENCE_DELIVERY_CYCLE
     *
     * @return the value of T_SALEORDER_GOODS.REFERENCE_DELIVERY_CYCLE
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public String getReferenceDeliveryCycle() {
        return referenceDeliveryCycle;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.REFERENCE_DELIVERY_CYCLE
     *
     * @param referenceDeliveryCycle the value for T_SALEORDER_GOODS.REFERENCE_DELIVERY_CYCLE
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setReferenceDeliveryCycle(String referenceDeliveryCycle) {
        this.referenceDeliveryCycle = referenceDeliveryCycle;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.REPORT_STATUS
     *
     * @return the value of T_SALEORDER_GOODS.REPORT_STATUS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Byte getReportStatus() {
        return reportStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.REPORT_STATUS
     *
     * @param reportStatus the value for T_SALEORDER_GOODS.REPORT_STATUS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setReportStatus(Byte reportStatus) {
        this.reportStatus = reportStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.REPORT_COMMENTS
     *
     * @return the value of T_SALEORDER_GOODS.REPORT_COMMENTS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public String getReportComments() {
        return reportComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.REPORT_COMMENTS
     *
     * @param reportComments the value for T_SALEORDER_GOODS.REPORT_COMMENTS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setReportComments(String reportComments) {
        this.reportComments = reportComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.HAVE_INSTALLATION
     *
     * @return the value of T_SALEORDER_GOODS.HAVE_INSTALLATION
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Byte getHaveInstallation() {
        return haveInstallation;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.HAVE_INSTALLATION
     *
     * @param haveInstallation the value for T_SALEORDER_GOODS.HAVE_INSTALLATION
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setHaveInstallation(Byte haveInstallation) {
        this.haveInstallation = haveInstallation;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.GOODS_COMMENTS
     *
     * @return the value of T_SALEORDER_GOODS.GOODS_COMMENTS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public String getGoodsComments() {
        return goodsComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.GOODS_COMMENTS
     *
     * @param goodsComments the value for T_SALEORDER_GOODS.GOODS_COMMENTS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setGoodsComments(String goodsComments) {
        this.goodsComments = goodsComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.INSIDE_COMMENTS
     *
     * @return the value of T_SALEORDER_GOODS.INSIDE_COMMENTS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public String getInsideComments() {
        return insideComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.INSIDE_COMMENTS
     *
     * @param insideComments the value for T_SALEORDER_GOODS.INSIDE_COMMENTS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setInsideComments(String insideComments) {
        this.insideComments = insideComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.ARRIVAL_USER_ID
     *
     * @return the value of T_SALEORDER_GOODS.ARRIVAL_USER_ID
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Integer getArrivalUserId() {
        return arrivalUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.ARRIVAL_USER_ID
     *
     * @param arrivalUserId the value for T_SALEORDER_GOODS.ARRIVAL_USER_ID
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setArrivalUserId(Integer arrivalUserId) {
        this.arrivalUserId = arrivalUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.ARRIVAL_STATUS
     *
     * @return the value of T_SALEORDER_GOODS.ARRIVAL_STATUS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Byte getArrivalStatus() {
        return arrivalStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.ARRIVAL_STATUS
     *
     * @param arrivalStatus the value for T_SALEORDER_GOODS.ARRIVAL_STATUS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setArrivalStatus(Byte arrivalStatus) {
        this.arrivalStatus = arrivalStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.ARRIVAL_TIME
     *
     * @return the value of T_SALEORDER_GOODS.ARRIVAL_TIME
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Long getArrivalTime() {
        return arrivalTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.ARRIVAL_TIME
     *
     * @param arrivalTime the value for T_SALEORDER_GOODS.ARRIVAL_TIME
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setArrivalTime(Long arrivalTime) {
        this.arrivalTime = arrivalTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.IS_DELETE
     *
     * @return the value of T_SALEORDER_GOODS.IS_DELETE
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Integer getIsDelete() {
        return isDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.IS_DELETE
     *
     * @param isDelete the value for T_SALEORDER_GOODS.IS_DELETE
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.IS_IGNORE
     *
     * @return the value of T_SALEORDER_GOODS.IS_IGNORE
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Byte getIsIgnore() {
        return isIgnore;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.IS_IGNORE
     *
     * @param isIgnore the value for T_SALEORDER_GOODS.IS_IGNORE
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setIsIgnore(Byte isIgnore) {
        this.isIgnore = isIgnore;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.IGNORE_TIME
     *
     * @return the value of T_SALEORDER_GOODS.IGNORE_TIME
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Long getIgnoreTime() {
        return ignoreTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.IGNORE_TIME
     *
     * @param ignoreTime the value for T_SALEORDER_GOODS.IGNORE_TIME
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setIgnoreTime(Long ignoreTime) {
        this.ignoreTime = ignoreTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.IGNORE_USER_ID
     *
     * @return the value of T_SALEORDER_GOODS.IGNORE_USER_ID
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Integer getIgnoreUserId() {
        return ignoreUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.IGNORE_USER_ID
     *
     * @param ignoreUserId the value for T_SALEORDER_GOODS.IGNORE_USER_ID
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setIgnoreUserId(Integer ignoreUserId) {
        this.ignoreUserId = ignoreUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.LOCKED_STATUS
     *
     * @return the value of T_SALEORDER_GOODS.LOCKED_STATUS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Byte getLockedStatus() {
        return lockedStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.LOCKED_STATUS
     *
     * @param lockedStatus the value for T_SALEORDER_GOODS.LOCKED_STATUS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setLockedStatus(Byte lockedStatus) {
        this.lockedStatus = lockedStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.MAX_SKU_REFUND_AMOUNT
     *
     * @return the value of T_SALEORDER_GOODS.MAX_SKU_REFUND_AMOUNT
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public BigDecimal getMaxSkuRefundAmount() {
        return maxSkuRefundAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.MAX_SKU_REFUND_AMOUNT
     *
     * @param maxSkuRefundAmount the value for T_SALEORDER_GOODS.MAX_SKU_REFUND_AMOUNT
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setMaxSkuRefundAmount(BigDecimal maxSkuRefundAmount) {
        this.maxSkuRefundAmount = maxSkuRefundAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.ADD_TIME
     *
     * @return the value of T_SALEORDER_GOODS.ADD_TIME
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Long getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.ADD_TIME
     *
     * @param addTime the value for T_SALEORDER_GOODS.ADD_TIME
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.CREATOR
     *
     * @return the value of T_SALEORDER_GOODS.CREATOR
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.CREATOR
     *
     * @param creator the value for T_SALEORDER_GOODS.CREATOR
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.MOD_TIME
     *
     * @return the value of T_SALEORDER_GOODS.MOD_TIME
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Long getModTime() {
        return modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.MOD_TIME
     *
     * @param modTime the value for T_SALEORDER_GOODS.MOD_TIME
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setModTime(Long modTime) {
        this.modTime = modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.UPDATER
     *
     * @return the value of T_SALEORDER_GOODS.UPDATER
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Integer getUpdater() {
        return updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.UPDATER
     *
     * @param updater the value for T_SALEORDER_GOODS.UPDATER
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.OCCUPY_NUM
     *
     * @return the value of T_SALEORDER_GOODS.OCCUPY_NUM
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Integer getOccupyNum() {
        return occupyNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.OCCUPY_NUM
     *
     * @param occupyNum the value for T_SALEORDER_GOODS.OCCUPY_NUM
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setOccupyNum(Integer occupyNum) {
        this.occupyNum = occupyNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.IS_ACTION_GOODS
     *
     * @return the value of T_SALEORDER_GOODS.IS_ACTION_GOODS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Byte getIsActionGoods() {
        return isActionGoods;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.IS_ACTION_GOODS
     *
     * @param isActionGoods the value for T_SALEORDER_GOODS.IS_ACTION_GOODS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setIsActionGoods(Byte isActionGoods) {
        this.isActionGoods = isActionGoods;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.ACTION_OCCUPY_NUM
     *
     * @return the value of T_SALEORDER_GOODS.ACTION_OCCUPY_NUM
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Integer getActionOccupyNum() {
        return actionOccupyNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.ACTION_OCCUPY_NUM
     *
     * @param actionOccupyNum the value for T_SALEORDER_GOODS.ACTION_OCCUPY_NUM
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setActionOccupyNum(Integer actionOccupyNum) {
        this.actionOccupyNum = actionOccupyNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.IS_COUPONS
     *
     * @return the value of T_SALEORDER_GOODS.IS_COUPONS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Byte getIsCoupons() {
        return isCoupons;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.IS_COUPONS
     *
     * @param isCoupons the value for T_SALEORDER_GOODS.IS_COUPONS
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setIsCoupons(Byte isCoupons) {
        this.isCoupons = isCoupons;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.EL_ORDERLIST_ID
     *
     * @return the value of T_SALEORDER_GOODS.EL_ORDERLIST_ID
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Integer getElOrderlistId() {
        return elOrderlistId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.EL_ORDERLIST_ID
     *
     * @param elOrderlistId the value for T_SALEORDER_GOODS.EL_ORDERLIST_ID
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setElOrderlistId(Integer elOrderlistId) {
        this.elOrderlistId = elOrderlistId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.UPDATE_DATA_TIME
     *
     * @return the value of T_SALEORDER_GOODS.UPDATE_DATA_TIME
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Date getUpdateDataTime() {
        return updateDataTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.UPDATE_DATA_TIME
     *
     * @param updateDataTime the value for T_SALEORDER_GOODS.UPDATE_DATA_TIME
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setUpdateDataTime(Date updateDataTime) {
        this.updateDataTime = updateDataTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.REAL_PAY_AMOUNT
     *
     * @return the value of T_SALEORDER_GOODS.REAL_PAY_AMOUNT
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public BigDecimal getRealPayAmount() {
        return realPayAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.REAL_PAY_AMOUNT
     *
     * @param realPayAmount the value for T_SALEORDER_GOODS.REAL_PAY_AMOUNT
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setRealPayAmount(BigDecimal realPayAmount) {
        this.realPayAmount = realPayAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.AFTER_RETURN_AMOUNT
     *
     * @return the value of T_SALEORDER_GOODS.AFTER_RETURN_AMOUNT
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public BigDecimal getAfterReturnAmount() {
        return afterReturnAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.AFTER_RETURN_AMOUNT
     *
     * @param afterReturnAmount the value for T_SALEORDER_GOODS.AFTER_RETURN_AMOUNT
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setAfterReturnAmount(BigDecimal afterReturnAmount) {
        this.afterReturnAmount = afterReturnAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_GOODS.AFTER_RETURN_NUM
     *
     * @return the value of T_SALEORDER_GOODS.AFTER_RETURN_NUM
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public Integer getAfterReturnNum() {
        return afterReturnNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_GOODS.AFTER_RETURN_NUM
     *
     * @param afterReturnNum the value for T_SALEORDER_GOODS.AFTER_RETURN_NUM
     *
     * @mbggenerated Fri Jun 05 17:05:41 CST 2020
     */
    public void setAfterReturnNum(Integer afterReturnNum) {
        this.afterReturnNum = afterReturnNum;
    }
}