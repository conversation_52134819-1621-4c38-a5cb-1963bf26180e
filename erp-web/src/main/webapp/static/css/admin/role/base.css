@charset "UTF-8";
body, h1, h2, h3, h4, h5, h6, hr, p, blockquote, dl, dt, dd, ul, ol, li, pre, form, fieldset, legend, button, input, textarea, th, td {
  margin: 0;
  padding: 0;
}

body, button, input, select, textarea {
  font: 12px/1.5tahoma, arial, \5b8b\4f53;
}

h1, h2, h3, h4, h5, h6 {
  font-size: 100%;
}

address, cite, dfn, em, var {
  font-style: normal;
}

code, kbd, pre, samp {
  font-family: couriernew, courier, monospace;
}

small {
  font-size: 12px;
}

ul, ol {
  list-style: none;
}

a {
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

sup {
  vertical-align: text-top;
}

sub {
  vertical-align: text-bottom;
}

legend {
  color: #000;
}

fieldset, img {
  border: 0;
}

button, input, select, textarea {
  font-size: 100%;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

/*css为clearfix，清除浮动*/
.clearfix::before,
.clearfix::after {
  content: "";
  height: 0;
  line-height: 0;
  display: block;
  visibility: hidden;
  clear: both;
}

.clearfix:after {
  clear: both;
}

.clearfix {
  *zoom: 1;
  /*IE/7/6*/
}

a {
  text-decoration: none;
  color: #333;
}

a:link {
  text-decoration: none;
  /* 指正常的未被访问过的链接*/
}

a:visited {
  text-decoration: none;
  /*指已经访问过的链接*/
}

a:hover {
  text-decoration: none;
  color: #C81623;
  /*指鼠标在链接*/
}

a:active {
  text-decoration: none;
  /* 指正在点的链接*/
}
