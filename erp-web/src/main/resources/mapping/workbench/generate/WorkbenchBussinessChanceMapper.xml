<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.workbench.dao.WorkbenchBussinessChanceExtMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.workbench.model.dto.WorkbenchBussinessChanceExtDto">
    <id column="BUSSINESS_CHANCE_ID" jdbcType="INTEGER" property="bussinessChanceId" />
    <result column="WEB_BUSSINESS_CHANCE_ID" jdbcType="INTEGER" property="webBussinessChanceId" />
    <result column="BUSSINESS_CHANCE_NO" jdbcType="VARCHAR" property="bussinessChanceNo" />
    <result column="WEB_ACCOUNT_ID" jdbcType="INTEGER" property="webAccountId" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="ORG_ID" jdbcType="INTEGER" property="orgId" />
    <result column="USER_ID" jdbcType="INTEGER" property="userId" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="CHECK_TRADER_NAME" jdbcType="VARCHAR" property="checkTraderName" />
    <result column="CHECK_TRADER_AREA" jdbcType="VARCHAR" property="checkTraderArea" />
    <result column="CHECK_TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="checkTraderContactName" />
    <result column="CHECK_TRADER_CONTACT_MOBILE" jdbcType="VARCHAR" property="checkTraderContactMobile" />
    <result column="CHECK_TRADER_CONTACT_TELEPHONE" jdbcType="VARCHAR" property="checkTraderContactTelephone" />
    <result column="TYPE" jdbcType="INTEGER" property="type" />
    <result column="RECEIVE_TIME" jdbcType="BIGINT" property="receiveTime" />
    <result column="SOURCE" jdbcType="INTEGER" property="source" />
    <result column="COMMUNICATION" jdbcType="INTEGER" property="communication" />
    <result column="CONTENT" jdbcType="VARCHAR" property="content" />
    <result column="GOODS_CATEGORY" jdbcType="INTEGER" property="goodsCategory" />
    <result column="GOODS_BRAND" jdbcType="VARCHAR" property="goodsBrand" />
    <result column="GOODS_NAME" jdbcType="VARCHAR" property="goodsName" />
    <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName" />
    <result column="AREA_ID" jdbcType="INTEGER" property="areaId" />
    <result column="AREA_IDS" jdbcType="VARCHAR" property="areaIds" />
    <result column="TRADER_CONTACT_ID" jdbcType="INTEGER" property="traderContactId" />
    <result column="TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="traderContactName" />
    <result column="MOBILE" jdbcType="VARCHAR" property="mobile" />
    <result column="TELEPHONE" jdbcType="VARCHAR" property="telephone" />
    <result column="OTHER_CONTACT" jdbcType="VARCHAR" property="otherContact" />
    <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
    <result column="ASSIGN_TIME" jdbcType="BIGINT" property="assignTime" />
    <result column="FIRST_VIEW_TIME" jdbcType="BIGINT" property="firstViewTime" />
    <result column="STATUS" jdbcType="TINYINT" property="status" />
    <result column="STATUS_COMMENTS" jdbcType="INTEGER" property="statusComments" />
    <result column="CLOSED_COMMENTS" jdbcType="VARCHAR" property="closedComments" />
    <result column="WENXIN_OPEN_ID" jdbcType="VARCHAR" property="wenxinOpenId" />
    <result column="BUSSINESS_LEVEL" jdbcType="INTEGER" property="bussinessLevel" />
    <result column="BUSSINESS_STAGE" jdbcType="INTEGER" property="bussinessStage" />
    <result column="ENQUIRY_TYPE" jdbcType="INTEGER" property="enquiryType" />
    <result column="ORDER_RATE" jdbcType="INTEGER" property="orderRate" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="ORDER_TIME" jdbcType="BIGINT" property="orderTime" />
    <result column="CANCEL_REASON" jdbcType="INTEGER" property="cancelReason" />
    <result column="OTHER_REASON" jdbcType="VARCHAR" property="otherReason" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="ENTRANCES" jdbcType="INTEGER" property="entrances" />
    <result column="FUNCTIONS" jdbcType="INTEGER" property="functions" />
    <result column="IS_NEW" jdbcType="BOOLEAN" property="isNew" />
    <result column="PRODUCT_COMMENTS" jdbcType="VARCHAR" property="productComments" />
    <result column="MERGE_STATUS" jdbcType="TINYINT" property="mergeStatus" />
    <result column="OLD_CHANCE_NO" jdbcType="VARCHAR" property="oldChanceNo" />
    <result column="BUSSINESS_PARENT_ID" jdbcType="INTEGER" property="bussinessParentId" />
    <result column="PRODUCT_COMMENTS_SALE" jdbcType="VARCHAR" property="productCommentsSale" />
    <result column="CLOSE_CHECK_STATUS" jdbcType="BOOLEAN" property="closeCheckStatus" />
    <result column="BNC_LINK" jdbcType="VARCHAR" property="bncLink" />
    <result column="IS_LINK_BD" jdbcType="BOOLEAN" property="isLinkBd" />
  </resultMap>
  <sql id="Base_Column_List">
    BUSSINESS_CHANCE_ID, WEB_BUSSINESS_CHANCE_ID, BUSSINESS_CHANCE_NO, WEB_ACCOUNT_ID, 
    COMPANY_ID, ORG_ID, USER_ID, TRADER_ID, CHECK_TRADER_NAME, CHECK_TRADER_AREA, CHECK_TRADER_CONTACT_NAME, 
    CHECK_TRADER_CONTACT_MOBILE, CHECK_TRADER_CONTACT_TELEPHONE, `TYPE`, RECEIVE_TIME, 
    `SOURCE`, COMMUNICATION, CONTENT, GOODS_CATEGORY, GOODS_BRAND, GOODS_NAME, TRADER_NAME, 
    AREA_ID, AREA_IDS, TRADER_CONTACT_ID, TRADER_CONTACT_NAME, MOBILE, TELEPHONE, OTHER_CONTACT, 
    COMMENTS, ASSIGN_TIME, FIRST_VIEW_TIME, `STATUS`, STATUS_COMMENTS, CLOSED_COMMENTS, 
    WENXIN_OPEN_ID, BUSSINESS_LEVEL, BUSSINESS_STAGE, ENQUIRY_TYPE, ORDER_RATE, AMOUNT, 
    ORDER_TIME, CANCEL_REASON, OTHER_REASON, ADD_TIME, CREATOR, MOD_TIME, UPDATER, ENTRANCES, 
    FUNCTIONS, IS_NEW, PRODUCT_COMMENTS, MERGE_STATUS, OLD_CHANCE_NO, BUSSINESS_PARENT_ID, 
    PRODUCT_COMMENTS_SALE, CLOSE_CHECK_STATUS, BNC_LINK, IS_LINK_BD
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_BUSSINESS_CHANCE
    where BUSSINESS_CHANCE_ID = #{bussinessChanceId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_BUSSINESS_CHANCE
    where BUSSINESS_CHANCE_ID = #{bussinessChanceId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="BUSSINESS_CHANCE_ID" keyProperty="bussinessChanceId" parameterType="com.vedeng.workbench.model.dto.generate.WorkbenchBussinessChanceDo" useGeneratedKeys="true">
    insert into T_BUSSINESS_CHANCE (WEB_BUSSINESS_CHANCE_ID, BUSSINESS_CHANCE_NO, 
      WEB_ACCOUNT_ID, COMPANY_ID, ORG_ID, 
      USER_ID, TRADER_ID, CHECK_TRADER_NAME, 
      CHECK_TRADER_AREA, CHECK_TRADER_CONTACT_NAME, 
      CHECK_TRADER_CONTACT_MOBILE, CHECK_TRADER_CONTACT_TELEPHONE, 
      `TYPE`, RECEIVE_TIME, `SOURCE`, 
      COMMUNICATION, CONTENT, GOODS_CATEGORY, 
      GOODS_BRAND, GOODS_NAME, TRADER_NAME, 
      AREA_ID, AREA_IDS, TRADER_CONTACT_ID, 
      TRADER_CONTACT_NAME, MOBILE, TELEPHONE, 
      OTHER_CONTACT, COMMENTS, ASSIGN_TIME, 
      FIRST_VIEW_TIME, `STATUS`, STATUS_COMMENTS, 
      CLOSED_COMMENTS, WENXIN_OPEN_ID, BUSSINESS_LEVEL, 
      BUSSINESS_STAGE, ENQUIRY_TYPE, ORDER_RATE, 
      AMOUNT, ORDER_TIME, CANCEL_REASON, 
      OTHER_REASON, ADD_TIME, CREATOR, 
      MOD_TIME, UPDATER, ENTRANCES, 
      FUNCTIONS, IS_NEW, PRODUCT_COMMENTS, 
      MERGE_STATUS, OLD_CHANCE_NO, BUSSINESS_PARENT_ID, 
      PRODUCT_COMMENTS_SALE, CLOSE_CHECK_STATUS, 
      BNC_LINK, IS_LINK_BD)
    values (#{webBussinessChanceId,jdbcType=INTEGER}, #{bussinessChanceNo,jdbcType=VARCHAR}, 
      #{webAccountId,jdbcType=INTEGER}, #{companyId,jdbcType=INTEGER}, #{orgId,jdbcType=INTEGER}, 
      #{userId,jdbcType=INTEGER}, #{traderId,jdbcType=INTEGER}, #{checkTraderName,jdbcType=VARCHAR}, 
      #{checkTraderArea,jdbcType=VARCHAR}, #{checkTraderContactName,jdbcType=VARCHAR}, 
      #{checkTraderContactMobile,jdbcType=VARCHAR}, #{checkTraderContactTelephone,jdbcType=VARCHAR}, 
      #{type,jdbcType=INTEGER}, #{receiveTime,jdbcType=BIGINT}, #{source,jdbcType=INTEGER}, 
      #{communication,jdbcType=INTEGER}, #{content,jdbcType=VARCHAR}, #{goodsCategory,jdbcType=INTEGER}, 
      #{goodsBrand,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, #{traderName,jdbcType=VARCHAR}, 
      #{areaId,jdbcType=INTEGER}, #{areaIds,jdbcType=VARCHAR}, #{traderContactId,jdbcType=INTEGER}, 
      #{traderContactName,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, #{telephone,jdbcType=VARCHAR}, 
      #{otherContact,jdbcType=VARCHAR}, #{comments,jdbcType=VARCHAR}, #{assignTime,jdbcType=BIGINT}, 
      #{firstViewTime,jdbcType=BIGINT}, #{status,jdbcType=TINYINT}, #{statusComments,jdbcType=INTEGER}, 
      #{closedComments,jdbcType=VARCHAR}, #{wenxinOpenId,jdbcType=VARCHAR}, #{bussinessLevel,jdbcType=INTEGER}, 
      #{bussinessStage,jdbcType=INTEGER}, #{enquiryType,jdbcType=INTEGER}, #{orderRate,jdbcType=INTEGER}, 
      #{amount,jdbcType=DECIMAL}, #{orderTime,jdbcType=BIGINT}, #{cancelReason,jdbcType=INTEGER}, 
      #{otherReason,jdbcType=VARCHAR}, #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, 
      #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}, #{entrances,jdbcType=INTEGER}, 
      #{functions,jdbcType=INTEGER}, #{isNew,jdbcType=BOOLEAN}, #{productComments,jdbcType=VARCHAR}, 
      #{mergeStatus,jdbcType=TINYINT}, #{oldChanceNo,jdbcType=VARCHAR}, #{bussinessParentId,jdbcType=INTEGER}, 
      #{productCommentsSale,jdbcType=VARCHAR}, #{closeCheckStatus,jdbcType=BOOLEAN}, 
      #{bncLink,jdbcType=VARCHAR}, #{isLinkBd,jdbcType=BOOLEAN})
  </insert>
  <insert id="insertSelective" keyColumn="BUSSINESS_CHANCE_ID" keyProperty="bussinessChanceId" parameterType="com.vedeng.workbench.model.dto.generate.WorkbenchBussinessChanceDo" useGeneratedKeys="true">
    insert into T_BUSSINESS_CHANCE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="webBussinessChanceId != null">
        WEB_BUSSINESS_CHANCE_ID,
      </if>
      <if test="bussinessChanceNo != null">
        BUSSINESS_CHANCE_NO,
      </if>
      <if test="webAccountId != null">
        WEB_ACCOUNT_ID,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="orgId != null">
        ORG_ID,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="checkTraderName != null">
        CHECK_TRADER_NAME,
      </if>
      <if test="checkTraderArea != null">
        CHECK_TRADER_AREA,
      </if>
      <if test="checkTraderContactName != null">
        CHECK_TRADER_CONTACT_NAME,
      </if>
      <if test="checkTraderContactMobile != null">
        CHECK_TRADER_CONTACT_MOBILE,
      </if>
      <if test="checkTraderContactTelephone != null">
        CHECK_TRADER_CONTACT_TELEPHONE,
      </if>
      <if test="type != null">
        `TYPE`,
      </if>
      <if test="receiveTime != null">
        RECEIVE_TIME,
      </if>
      <if test="source != null">
        `SOURCE`,
      </if>
      <if test="communication != null">
        COMMUNICATION,
      </if>
      <if test="content != null">
        CONTENT,
      </if>
      <if test="goodsCategory != null">
        GOODS_CATEGORY,
      </if>
      <if test="goodsBrand != null">
        GOODS_BRAND,
      </if>
      <if test="goodsName != null">
        GOODS_NAME,
      </if>
      <if test="traderName != null">
        TRADER_NAME,
      </if>
      <if test="areaId != null">
        AREA_ID,
      </if>
      <if test="areaIds != null">
        AREA_IDS,
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID,
      </if>
      <if test="traderContactName != null">
        TRADER_CONTACT_NAME,
      </if>
      <if test="mobile != null">
        MOBILE,
      </if>
      <if test="telephone != null">
        TELEPHONE,
      </if>
      <if test="otherContact != null">
        OTHER_CONTACT,
      </if>
      <if test="comments != null">
        COMMENTS,
      </if>
      <if test="assignTime != null">
        ASSIGN_TIME,
      </if>
      <if test="firstViewTime != null">
        FIRST_VIEW_TIME,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
      <if test="statusComments != null">
        STATUS_COMMENTS,
      </if>
      <if test="closedComments != null">
        CLOSED_COMMENTS,
      </if>
      <if test="wenxinOpenId != null">
        WENXIN_OPEN_ID,
      </if>
      <if test="bussinessLevel != null">
        BUSSINESS_LEVEL,
      </if>
      <if test="bussinessStage != null">
        BUSSINESS_STAGE,
      </if>
      <if test="enquiryType != null">
        ENQUIRY_TYPE,
      </if>
      <if test="orderRate != null">
        ORDER_RATE,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="orderTime != null">
        ORDER_TIME,
      </if>
      <if test="cancelReason != null">
        CANCEL_REASON,
      </if>
      <if test="otherReason != null">
        OTHER_REASON,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="entrances != null">
        ENTRANCES,
      </if>
      <if test="functions != null">
        FUNCTIONS,
      </if>
      <if test="isNew != null">
        IS_NEW,
      </if>
      <if test="productComments != null">
        PRODUCT_COMMENTS,
      </if>
      <if test="mergeStatus != null">
        MERGE_STATUS,
      </if>
      <if test="oldChanceNo != null">
        OLD_CHANCE_NO,
      </if>
      <if test="bussinessParentId != null">
        BUSSINESS_PARENT_ID,
      </if>
      <if test="productCommentsSale != null">
        PRODUCT_COMMENTS_SALE,
      </if>
      <if test="closeCheckStatus != null">
        CLOSE_CHECK_STATUS,
      </if>
      <if test="bncLink != null">
        BNC_LINK,
      </if>
      <if test="isLinkBd != null">
        IS_LINK_BD,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="webBussinessChanceId != null">
        #{webBussinessChanceId,jdbcType=INTEGER},
      </if>
      <if test="bussinessChanceNo != null">
        #{bussinessChanceNo,jdbcType=VARCHAR},
      </if>
      <if test="webAccountId != null">
        #{webAccountId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="checkTraderName != null">
        #{checkTraderName,jdbcType=VARCHAR},
      </if>
      <if test="checkTraderArea != null">
        #{checkTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="checkTraderContactName != null">
        #{checkTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="checkTraderContactMobile != null">
        #{checkTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="checkTraderContactTelephone != null">
        #{checkTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="receiveTime != null">
        #{receiveTime,jdbcType=BIGINT},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="communication != null">
        #{communication,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="goodsCategory != null">
        #{goodsCategory,jdbcType=INTEGER},
      </if>
      <if test="goodsBrand != null">
        #{goodsBrand,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="traderName != null">
        #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null">
        #{areaId,jdbcType=INTEGER},
      </if>
      <if test="areaIds != null">
        #{areaIds,jdbcType=VARCHAR},
      </if>
      <if test="traderContactId != null">
        #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null">
        #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="otherContact != null">
        #{otherContact,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="assignTime != null">
        #{assignTime,jdbcType=BIGINT},
      </if>
      <if test="firstViewTime != null">
        #{firstViewTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="statusComments != null">
        #{statusComments,jdbcType=INTEGER},
      </if>
      <if test="closedComments != null">
        #{closedComments,jdbcType=VARCHAR},
      </if>
      <if test="wenxinOpenId != null">
        #{wenxinOpenId,jdbcType=VARCHAR},
      </if>
      <if test="bussinessLevel != null">
        #{bussinessLevel,jdbcType=INTEGER},
      </if>
      <if test="bussinessStage != null">
        #{bussinessStage,jdbcType=INTEGER},
      </if>
      <if test="enquiryType != null">
        #{enquiryType,jdbcType=INTEGER},
      </if>
      <if test="orderRate != null">
        #{orderRate,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="orderTime != null">
        #{orderTime,jdbcType=BIGINT},
      </if>
      <if test="cancelReason != null">
        #{cancelReason,jdbcType=INTEGER},
      </if>
      <if test="otherReason != null">
        #{otherReason,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="entrances != null">
        #{entrances,jdbcType=INTEGER},
      </if>
      <if test="functions != null">
        #{functions,jdbcType=INTEGER},
      </if>
      <if test="isNew != null">
        #{isNew,jdbcType=BOOLEAN},
      </if>
      <if test="productComments != null">
        #{productComments,jdbcType=VARCHAR},
      </if>
      <if test="mergeStatus != null">
        #{mergeStatus,jdbcType=TINYINT},
      </if>
      <if test="oldChanceNo != null">
        #{oldChanceNo,jdbcType=VARCHAR},
      </if>
      <if test="bussinessParentId != null">
        #{bussinessParentId,jdbcType=INTEGER},
      </if>
      <if test="productCommentsSale != null">
        #{productCommentsSale,jdbcType=VARCHAR},
      </if>
      <if test="closeCheckStatus != null">
        #{closeCheckStatus,jdbcType=BOOLEAN},
      </if>
      <if test="bncLink != null">
        #{bncLink,jdbcType=VARCHAR},
      </if>
      <if test="isLinkBd != null">
        #{isLinkBd,jdbcType=BOOLEAN},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.workbench.model.dto.generate.WorkbenchBussinessChanceDo">
    update T_BUSSINESS_CHANCE
    <set>
      <if test="webBussinessChanceId != null">
        WEB_BUSSINESS_CHANCE_ID = #{webBussinessChanceId,jdbcType=INTEGER},
      </if>
      <if test="bussinessChanceNo != null">
        BUSSINESS_CHANCE_NO = #{bussinessChanceNo,jdbcType=VARCHAR},
      </if>
      <if test="webAccountId != null">
        WEB_ACCOUNT_ID = #{webAccountId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        ORG_ID = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="checkTraderName != null">
        CHECK_TRADER_NAME = #{checkTraderName,jdbcType=VARCHAR},
      </if>
      <if test="checkTraderArea != null">
        CHECK_TRADER_AREA = #{checkTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="checkTraderContactName != null">
        CHECK_TRADER_CONTACT_NAME = #{checkTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="checkTraderContactMobile != null">
        CHECK_TRADER_CONTACT_MOBILE = #{checkTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="checkTraderContactTelephone != null">
        CHECK_TRADER_CONTACT_TELEPHONE = #{checkTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `TYPE` = #{type,jdbcType=INTEGER},
      </if>
      <if test="receiveTime != null">
        RECEIVE_TIME = #{receiveTime,jdbcType=BIGINT},
      </if>
      <if test="source != null">
        `SOURCE` = #{source,jdbcType=INTEGER},
      </if>
      <if test="communication != null">
        COMMUNICATION = #{communication,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        CONTENT = #{content,jdbcType=VARCHAR},
      </if>
      <if test="goodsCategory != null">
        GOODS_CATEGORY = #{goodsCategory,jdbcType=INTEGER},
      </if>
      <if test="goodsBrand != null">
        GOODS_BRAND = #{goodsBrand,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="traderName != null">
        TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null">
        AREA_ID = #{areaId,jdbcType=INTEGER},
      </if>
      <if test="areaIds != null">
        AREA_IDS = #{areaIds,jdbcType=VARCHAR},
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null">
        TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        MOBILE = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        TELEPHONE = #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="otherContact != null">
        OTHER_CONTACT = #{otherContact,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="assignTime != null">
        ASSIGN_TIME = #{assignTime,jdbcType=BIGINT},
      </if>
      <if test="firstViewTime != null">
        FIRST_VIEW_TIME = #{firstViewTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        `STATUS` = #{status,jdbcType=TINYINT},
      </if>
      <if test="statusComments != null">
        STATUS_COMMENTS = #{statusComments,jdbcType=INTEGER},
      </if>
      <if test="closedComments != null">
        CLOSED_COMMENTS = #{closedComments,jdbcType=VARCHAR},
      </if>
      <if test="wenxinOpenId != null">
        WENXIN_OPEN_ID = #{wenxinOpenId,jdbcType=VARCHAR},
      </if>
      <if test="bussinessLevel != null">
        BUSSINESS_LEVEL = #{bussinessLevel,jdbcType=INTEGER},
      </if>
      <if test="bussinessStage != null">
        BUSSINESS_STAGE = #{bussinessStage,jdbcType=INTEGER},
      </if>
      <if test="enquiryType != null">
        ENQUIRY_TYPE = #{enquiryType,jdbcType=INTEGER},
      </if>
      <if test="orderRate != null">
        ORDER_RATE = #{orderRate,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="orderTime != null">
        ORDER_TIME = #{orderTime,jdbcType=BIGINT},
      </if>
      <if test="cancelReason != null">
        CANCEL_REASON = #{cancelReason,jdbcType=INTEGER},
      </if>
      <if test="otherReason != null">
        OTHER_REASON = #{otherReason,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="entrances != null">
        ENTRANCES = #{entrances,jdbcType=INTEGER},
      </if>
      <if test="functions != null">
        FUNCTIONS = #{functions,jdbcType=INTEGER},
      </if>
      <if test="isNew != null">
        IS_NEW = #{isNew,jdbcType=BOOLEAN},
      </if>
      <if test="productComments != null">
        PRODUCT_COMMENTS = #{productComments,jdbcType=VARCHAR},
      </if>
      <if test="mergeStatus != null">
        MERGE_STATUS = #{mergeStatus,jdbcType=TINYINT},
      </if>
      <if test="oldChanceNo != null">
        OLD_CHANCE_NO = #{oldChanceNo,jdbcType=VARCHAR},
      </if>
      <if test="bussinessParentId != null">
        BUSSINESS_PARENT_ID = #{bussinessParentId,jdbcType=INTEGER},
      </if>
      <if test="productCommentsSale != null">
        PRODUCT_COMMENTS_SALE = #{productCommentsSale,jdbcType=VARCHAR},
      </if>
      <if test="closeCheckStatus != null">
        CLOSE_CHECK_STATUS = #{closeCheckStatus,jdbcType=BOOLEAN},
      </if>
      <if test="bncLink != null">
        BNC_LINK = #{bncLink,jdbcType=VARCHAR},
      </if>
      <if test="isLinkBd != null">
        IS_LINK_BD = #{isLinkBd,jdbcType=BOOLEAN},
      </if>
    </set>
    where BUSSINESS_CHANCE_ID = #{bussinessChanceId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.workbench.model.dto.generate.WorkbenchBussinessChanceDo">
    update T_BUSSINESS_CHANCE
    set WEB_BUSSINESS_CHANCE_ID = #{webBussinessChanceId,jdbcType=INTEGER},
      BUSSINESS_CHANCE_NO = #{bussinessChanceNo,jdbcType=VARCHAR},
      WEB_ACCOUNT_ID = #{webAccountId,jdbcType=INTEGER},
      COMPANY_ID = #{companyId,jdbcType=INTEGER},
      ORG_ID = #{orgId,jdbcType=INTEGER},
      USER_ID = #{userId,jdbcType=INTEGER},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      CHECK_TRADER_NAME = #{checkTraderName,jdbcType=VARCHAR},
      CHECK_TRADER_AREA = #{checkTraderArea,jdbcType=VARCHAR},
      CHECK_TRADER_CONTACT_NAME = #{checkTraderContactName,jdbcType=VARCHAR},
      CHECK_TRADER_CONTACT_MOBILE = #{checkTraderContactMobile,jdbcType=VARCHAR},
      CHECK_TRADER_CONTACT_TELEPHONE = #{checkTraderContactTelephone,jdbcType=VARCHAR},
      `TYPE` = #{type,jdbcType=INTEGER},
      RECEIVE_TIME = #{receiveTime,jdbcType=BIGINT},
      `SOURCE` = #{source,jdbcType=INTEGER},
      COMMUNICATION = #{communication,jdbcType=INTEGER},
      CONTENT = #{content,jdbcType=VARCHAR},
      GOODS_CATEGORY = #{goodsCategory,jdbcType=INTEGER},
      GOODS_BRAND = #{goodsBrand,jdbcType=VARCHAR},
      GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      AREA_ID = #{areaId,jdbcType=INTEGER},
      AREA_IDS = #{areaIds,jdbcType=VARCHAR},
      TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      MOBILE = #{mobile,jdbcType=VARCHAR},
      TELEPHONE = #{telephone,jdbcType=VARCHAR},
      OTHER_CONTACT = #{otherContact,jdbcType=VARCHAR},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      ASSIGN_TIME = #{assignTime,jdbcType=BIGINT},
      FIRST_VIEW_TIME = #{firstViewTime,jdbcType=BIGINT},
      `STATUS` = #{status,jdbcType=TINYINT},
      STATUS_COMMENTS = #{statusComments,jdbcType=INTEGER},
      CLOSED_COMMENTS = #{closedComments,jdbcType=VARCHAR},
      WENXIN_OPEN_ID = #{wenxinOpenId,jdbcType=VARCHAR},
      BUSSINESS_LEVEL = #{bussinessLevel,jdbcType=INTEGER},
      BUSSINESS_STAGE = #{bussinessStage,jdbcType=INTEGER},
      ENQUIRY_TYPE = #{enquiryType,jdbcType=INTEGER},
      ORDER_RATE = #{orderRate,jdbcType=INTEGER},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      ORDER_TIME = #{orderTime,jdbcType=BIGINT},
      CANCEL_REASON = #{cancelReason,jdbcType=INTEGER},
      OTHER_REASON = #{otherReason,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      ENTRANCES = #{entrances,jdbcType=INTEGER},
      FUNCTIONS = #{functions,jdbcType=INTEGER},
      IS_NEW = #{isNew,jdbcType=BOOLEAN},
      PRODUCT_COMMENTS = #{productComments,jdbcType=VARCHAR},
      MERGE_STATUS = #{mergeStatus,jdbcType=TINYINT},
      OLD_CHANCE_NO = #{oldChanceNo,jdbcType=VARCHAR},
      BUSSINESS_PARENT_ID = #{bussinessParentId,jdbcType=INTEGER},
      PRODUCT_COMMENTS_SALE = #{productCommentsSale,jdbcType=VARCHAR},
      CLOSE_CHECK_STATUS = #{closeCheckStatus,jdbcType=BOOLEAN},
      BNC_LINK = #{bncLink,jdbcType=VARCHAR},
      IS_LINK_BD = #{isLinkBd,jdbcType=BOOLEAN}
    where BUSSINESS_CHANCE_ID = #{bussinessChanceId,jdbcType=INTEGER}
  </update>
</mapper>