package com.newtask;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.flash.service.warningtask.ExpeditingTaskService;
import com.vedeng.flash.service.warningtask.ExpeditingTicketsService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * @Description 定时任务，扫描预警表处理预警任务的升级
 * <AUTHOR>
 * @Date 16:00 2021/5/27
 * @Param [s]
 * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
 **/

@Component
@JobHandler(value = "updateEarlyWarningTaskTask")
public class UpdateEarlyWarningTaskTask extends AbstractJobHandler {

    public static final Logger logger = LoggerFactory.getLogger(UpdateEarlyWarningTaskTask.class);

    @Autowired
    private ExpeditingTicketsService expeditingTicketsService;

    @Autowired
    private ExpeditingTaskService expeditingTaskService;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {

        expeditingTicketsService.renovateExpeditingTicketStatus();

        expeditingTaskService.renovateExpeditionStatus();

        return SUCCESS;
    }
}
