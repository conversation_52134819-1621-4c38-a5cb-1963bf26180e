package com.vedeng.trader.service.impl;

import com.google.common.base.Preconditions;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.trader.dao.TraderContactGenerateMapper;
import com.vedeng.trader.model.TraderContactGenerate;
import com.vedeng.trader.model.vo.TraderContactVo;
import com.vedeng.trader.service.TraderContactService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Service
public class TraderContactServiceImpl implements TraderContactService {
    @Resource
    private TraderContactGenerateMapper traderContactGenerateMapper;

    @Override
    @Transactional(readOnly = true, rollbackFor = Exception.class)
    public TraderContactGenerate getByTraderIdAndMobileNo(Integer traderId, String mobileNo) {
        Preconditions.checkArgument(traderId != null, "客户编号为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(mobileNo), "手机号为空");
        final List<TraderContactGenerate> traderContactGenerateList = traderContactGenerateMapper.getByTraderIdAndMobileNo(traderId, mobileNo, CommonConstants.ENABLE);
        return traderContactGenerateList.isEmpty() ? null : traderContactGenerateList.get(0);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateByPrimaryKeySelective(TraderContactGenerate contactToUpdate) {
        Preconditions.checkArgument(contactToUpdate != null, "联系人信息为空");
        return traderContactGenerateMapper.updateByPrimaryKeySelective(contactToUpdate) > 0;
    }

    @Transactional(readOnly = true, rollbackFor = Exception.class)
    @Override
    public TraderContactGenerate selectByPrimaryKey(Integer traderContactId) {
        Preconditions.checkArgument(traderContactId != null, "联系人编号为空");
        return traderContactGenerateMapper.selectByPrimaryKey(traderContactId);
    }


    @Override
    public TraderContactGenerate insertSelective(TraderContactGenerate traderContact) {
        Objects.requireNonNull(traderContact, "信息人信息为空");
        traderContactGenerateMapper.insertSelective(traderContact);
        return traderContact;
    }

    @Override
    public List<TraderContactVo> getTraderContactInWebAccountByTraderId(Integer traderId) {
        return traderContactGenerateMapper.getTraderContactListInWebAccountByTraderId(traderId)
                .stream()
                .map(item -> {
                    TraderContactVo vo = new TraderContactVo();
                    vo.setTraderContactId(item.getTraderContactId());
                    vo.setName(item.getName());
                    vo.setMobile(item.getMobile());
                    return vo;
                })
                .collect(Collectors.toList());
    }


}
