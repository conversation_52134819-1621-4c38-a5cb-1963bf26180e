package com.vedeng.erp.broadcast.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * ERP末级部门与通知二级部门关系表
 */
@Getter
@Setter
public class BroadcastDeptRErpDeptEntity extends BaseEntity {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * ERP末级部门ID
     */
    private Integer erpDeptId;

    /**
     * 通知小组ID（二级部门ID）
     */
    private Integer broadcastDeptId;

    /**
     * 是否已删除（0=否，1=是）
     */
    private Integer isDeleted;
}
