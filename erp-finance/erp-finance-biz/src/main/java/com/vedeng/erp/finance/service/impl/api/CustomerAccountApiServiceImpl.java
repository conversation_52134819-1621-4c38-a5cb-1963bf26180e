package com.vedeng.erp.finance.service.impl.api;

import com.vedeng.erp.finance.api.CustomerAccountApiService;
import com.vedeng.erp.finance.dto.RPayApplyJBankReceiptDto;
import com.vedeng.erp.finance.dto.CustomerAccountReqDto;
import com.vedeng.erp.finance.facade.CustomerAccountFacade;
import com.vedeng.erp.trader.dto.CustomerBankAccountApiDto;
import com.vedeng.erp.trader.dto.CustomerBankAccountQueryReqDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class CustomerAccountApiServiceImpl implements CustomerAccountApiService {

    @Autowired
    private CustomerAccountFacade customerAccountFacade;

    @Override
    public void tryCreate(CustomerAccountReqDto customerAccountReqDto) {
        customerAccountFacade.tryCreateCustomerAccount(customerAccountReqDto.getAfterSalesId());
    }

    @Override
    public void createPayApplyRelation(RPayApplyJBankReceiptDto rPayApplyJBankReceiptDto) {
        customerAccountFacade.tryCreateRelation(rPayApplyJBankReceiptDto);
    }

    @Override
    public void tryCreateBankAlias(Integer payApplyId, Integer bankId) {
        customerAccountFacade.tryCreateBankAlias(payApplyId, bankId);
    }


    @Override
    public List<CustomerBankAccountApiDto> getCustomerAccount(CustomerAccountReqDto customerAccountReqDto) {
        return customerAccountFacade.getCustomerBankAccount(customerAccountReqDto);
    }

    @Override
    public void updateLastUseTime(Long customerBankAccountId) {
        customerAccountFacade.updateLastUseTime(customerBankAccountId, new CustomerBankAccountQueryReqDto());
    }


}
