<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>

<div id="app">
    <%--图片上传--%>
    <el-row :gutter="20" class="baseModel1">
        <el-col  >
            <div class="grid-content bg-purple-light" style="overflow: auto">
                <div class="box1">
                    <input type="file" class="el-button el-button--primary el-button--small" multiple @change="handleFileChange">
                    <%--文件上传结果展示--%>
                    <el-table size="small" :data="fileResults" style="width: 100%" :row-style="{ height: '40px' }">
                        <el-table-column size="small" prop="orderById" label="序号" width="50"></el-table-column>
                        <el-table-column size="small" prop="name" label="文件名" width="150"></el-table-column>
                        <el-table-column size="small" label="操作" width="60">
                            <template slot-scope="scope">
                                <el-button type="text" @click="removeFile(scope.$index)" class="custom-text-button">删除</el-button>
                            </template>
                        </el-table-column>
                        <el-table-column size="small" prop="message" label="上传结果" width="230">
                            <template slot-scope="scope">
                                <span v-html="scope.row.message"></span>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div slot="tip" class="box2" style="color: #909399">
                    操作提示：<br/>
                    1、需保证订单的合同、确认单都未上传<br/>
                    2、仅全部发货订单方可批量上传<br/>
                    3、上传文件名需修改为对应京东订单号，方可自动匹配<br/>
                    4、上传格式支持png，jpg，pdf三种<br/>
                    5、单个文件不超过2MB<br/>
                    6、单次最多支持200个文件<br/>
                    7、上传后系统会批量提交审核<br/>
                    <br/>
                    <font style="font-weight: bolder;color: black;">处理进度：</font><br/>
                    <span style="padding-top: 10px;" id="processMessage" v-html="processMessage">
                    </span>

                    <%--按钮--%>
                    <span style="display: block;text-align: center;" class="baseModel">
                        <el-button type="primary" @click="submit" :disabled="disSubmit">提交审核</el-button>
                        <el-button @click="reInit">关闭</el-button>
                    </span>

                </div>
            </div>
        </el-col>
    </el-row>
    <%--<el-divider></el-divider>--%>


</div>

<script type="text/javascript">
    let vm = new Vue({
        el: '#app',
        data() {
            return {
                isIndeterminate: false,
                checkAll: false,
                checkedBatch: [],
                fileList: [],
                fileResults: [],
                disSubmit: false,
                processMessage:'请选择文件'
            }
        },
        methods: {
            handleFileChange(event) {
                this.fileResults = [];
                const files = event.target.files;
                if (files.length >200) {
                    this.$message.error("单次最多支持200个文件");
                    event.target.value = '';
                    this.disSubmit = false;
                    return;
                }
                const allowedTypes = ['application/pdf', 'image/jpeg','image/png', 'image/jpg'];
                const maxSize = 2 * 1024 * 1024; // 2MB in bytes
                let fileTypeCheck =true;//文件类型校验通过
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    if (allowedTypes.includes(file.type)) {
                        if (file.size > maxSize) {
                            this.$message.error("单个文件不超过2MB");
                            fileTypeCheck = false;
                            break;
                        }
                    } else {
                        this.$message.error("上传格式支持png，jpg，pdf三种");
                        //文件类型校验 不通过
                        fileTypeCheck = false;
                        break;
                    }
                }
                if(fileTypeCheck){
                    for (let i = 0; i < files.length; i++) {
                        const file = files[i];
                        this.fileResults.push({
                            orderById: this.fileResults.length + 1,
                            name: file.name,
                            file: file
                        });
                    }
                }
                if(this.fileResults.length>0){
                    this.processMessage = `已选中`+this.fileResults.length+"个附件，请点击“提交审核”开始上传。";
                }
                // 清空 input 的值，以便可以重新选择相同的文件
                event.target.value = '';
                this.disSubmit = false;
            },
            removeFile(index) {
                this.fileResults.splice(index, 1);
                // 重置序号
                this.resetOrderById();
            },
            resetOrderById() {
                this.fileResults.forEach((file, index) => {
                    file.orderById = index + 1;
                });
            },
            submit() {
                if (this.fileResults.length === 0) {
                    this.$message.error("请先选择附件");
                    return;
                }
                if (this.fileResults.length >200) {
                    this.$message.error("单次最多支持200个文件");
                    return;
                }

                this.disSubmit = true;
                this.uploadFilesSequentially(this.fileResults);
            },
            async uploadFilesSequentially(files) {
                var loadIndex =  layer.load(2, {
                    shade: [0.5, '#000'] // 0.5 表示50%透明度，'#000' 表示遮罩层颜色
                });
                let total = files.length;
                let processNum = 0;
                let successNum = 0;
                let failureNum = 0;

                for (const file of files) {
                    const formData = new FormData();
                    formData.append('file', file.file);
                    processNum ++;
                    this.processMessage = `处理中，`+successNum+`个附件上传成功，`+failureNum+`个附件上传失败，剩余`+(total-successNum-failureNum)+'个附件。';
                    try {
                        const res = await axios({
                            url: '/orderstream/saleorder/uploadJdContractReturnSave.do',
                            method: 'post',
                            data: formData,
                            headers: {
                                'Content-Type': 'multipart/form-data'
                            }
                        });

                        if (res.data.code === 0) {
                            // 更新文件上传成功的状态
                            const index = this.fileResults.findIndex(item => item.name === file.name);
                            if (index !== -1) {
                                this.$set(this.fileResults, index, {
                                    ...this.fileResults[index],
                                    status: 'success',
                                    message: ('<font color="green">')+(res.data.message || '上传成功')+'</font>'
                                });
                            }
                            successNum ++;
                        } else {
                            // 更新文件上传失败的状态
                            const index = this.fileResults.findIndex(item => item.name === file.name);
                            if (index !== -1) {
                                this.$set(this.fileResults, index, {
                                    ...this.fileResults[index],
                                    status: 'error',
                                    message: '<font color="red">'+'上传失败，'+res.data.message+'</font>'
                                });
                            }
                            failureNum++;
                        }
                    } catch (error) {

                        // 更新文件上传失败的状态
                        const index = this.fileResults.findIndex(item => item.name === file.name);
                        if (index !== -1) {
                            this.$set(this.fileResults, index, {
                                ...this.fileResults[index],
                                status: 'error',
                                message: '上传失败'
                            });
                        }
                        failureNum++;
                    }
                }
                //this.processMessage = `处理进度：共${total}，正在上传第${processNum}，成功${successNum}个，失败${failureNum}个。`;
                if(total === processNum){
                    if(failureNum >0){
                        this.processMessage = successNum+`个附件上传成功，`+failureNum+`个附件上传失败，可在左侧查看具体原因，确认无误后关闭弹窗。`;
                    }else{
                        this.processMessage = successNum+`个附件上传成功，确认无误后关闭弹窗。`;
                    }

                }
                layer.close(loadIndex);
            },
            reInit() {
                layer.closeAll();
                parent.layer.closeAll();
            },
        }
    });
</script>

<style>
    #app {
        /*min-height: 100vh;*/
        padding-bottom: 30px; /* 根据 .baseModel 的高度调整 */
    }

    .baseModel1 {
        padding-top: 20px;
        padding-left: 10px;
        font-size: 13px;
    }
    .baseModel {
        background-color: transparent; /* 设置背景为透明 */
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        padding: 10px 10px;
        font-size: 13px;
        text-align: center;
        /*box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);  */
        z-index: 1000; /* 确保在其他内容之上 */
    }

    .box1 {
        width: 500px;
        float: left;
    }

    .box2 {
        position: fixed;
        top:0px;
        left: 510px;
        bottom: 0;
        padding: 20px 10px;
        font-size: 13px;
        background-color: white;
        text-align: left;
        /*box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);*/
        z-index: 1000;
    }
     .custom-text-button {
         padding: 0; /* 调整或移除内边距 */
     }
</style>