package com.newtask.sync;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.core.utils.HttpClientUtil;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.system.SyncDataTypeEnum;
import com.vedeng.erp.system.api.SyncDataErpApiService;
import com.vedeng.erp.system.api.SyncOutInRelateApiService;
import com.vedeng.erp.system.dto.BaseCompanyInfoDetailDto;
import com.vedeng.erp.system.dto.BaseCompanyInfoDto;
import com.vedeng.erp.system.dto.SyncDataErpDto;
import com.vedeng.erp.system.dto.SyncOutInRelateDto;
import com.vedeng.erp.system.service.BaseCompanyInfoApiService;
import com.vedeng.erp.trader.domain.dto.TraderCustomerInfoDto;
import com.vedeng.erp.trader.service.TraderCustomerBaseService;
import com.vedeng.erptoxfjx.xfjx.domain.XfjxBuyorderToSaleorderSync;
import com.vedeng.erptoxfjx.xfjx.mapper.XfjxBuyorderToSaleorderSyncMapper;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.order.model.vo.OrderData;
import com.vedeng.order.model.vo.OrderGoodsData;
import com.vedeng.order.service.BuyorderService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 从T_SYNC_DATA_ERP表中捞取待同步到其他ERP的单据
 * SELECT * FROM T_SYNC_DATA_ERP where BUSINESS_TYPE= 'BUYORDER_TO_SALEORDER' and PROCESS_STATUS =0
 */
@JobHandler(value = "erpBuyOrderSyncToSaleOrderTask")
@Component
@Slf4j
public class ErpBuyOrderSyncToSaleOrderTask extends AbstractJobHandler {
	//日志
    Logger logger = LoggerFactory.getLogger(ErpBuyOrderSyncToSaleOrderTask.class);

    @Autowired
    private BuyorderService buyorderService;

    @Value("${vdErpCompanyName:VEDENG}")
    private String vdErpCompanyName;

    @Autowired
    private SyncOutInRelateApiService syncOutInRelateApiService;

    @Autowired
    private BaseCompanyInfoApiService baseCompanyInfoApiService;

    @Autowired
    private SyncDataErpApiService syncDataErpApiService;


    @Autowired
    private TraderCustomerBaseService traderCustomerBaseService;

    private static final Integer ZHIFA =  1;//直发标识


    @Value("${erpCompanyName}")
    private String erpCompanyName;

    @Override
    public ReturnT<String> doExecute(String params) throws Exception  {
        String dataType = SyncDataTypeEnum.BUYORDER_TO_SALEORDER.getDataType();
        List<SyncDataErpDto> syncDataErpDtoList =  syncDataErpApiService.selectByProcessStatus(ErpConst.ZERO,dataType);
        if(CollectionUtils.isEmpty(syncDataErpDtoList)){
            log.info("当前无待同步的采购订单合同需要同步到目标ERP的销售订单下");
            XxlJobLogger.log("当前无待同步的采购订单合同需要同步到目标ERP的销售订单下");
            return ReturnT.SUCCESS;
        }

        BaseCompanyInfoDto vdBaseCompanyInfoDto = baseCompanyInfoApiService.selectBaseCompanyByShortName(vdErpCompanyName);
        if(vdBaseCompanyInfoDto == null){
            log.warn("同步采购订单到目标ERP的销售订单时，贝登ERP的主体详细信息缺失0,{}",vdErpCompanyName);
            XxlJobLogger.log("同步采购订单到目标ERP的销售订单时，贝登ERP的主体详细信息缺失1,{}",vdErpCompanyName);
            return ReturnT.FAIL;
        }

        BaseCompanyInfoDto baseCompanyInfoDto = baseCompanyInfoApiService.selectBaseCompanyByCompanyName(erpCompanyName);
        if(baseCompanyInfoDto == null){
            log.warn("同步采购订单到目标ERP的销售订单时，当前ERP的主体详细信息缺失1,{}",erpCompanyName);
            XxlJobLogger.log("同步采购订单到目标ERP的销售订单时，当前ERP的主体详细信息缺失1,{}",erpCompanyName);
            return ReturnT.FAIL;
        }
        BaseCompanyInfoDetailDto baseCompanyInfoDetailDto = baseCompanyInfoDto.getBaseCompanyInfoDetailDto();
        if(baseCompanyInfoDetailDto == null){
            log.warn("同步采购订单到目标ERP的销售订单时，当前ERP的主体详细信息缺失2,{}",erpCompanyName);
            XxlJobLogger.log("同步采购订单到目标ERP的销售订单时，当前ERP的主体详细信息缺失2,{}",erpCompanyName);
            return ReturnT.FAIL;
        }

        //根据客户ID，获取客户信息
        Integer traderCustomerId = baseCompanyInfoDto.getCustomerTraderId();
        TraderCustomerInfoDto traderCustomerInfoDto = traderCustomerBaseService.getTraderCutomerInfoById(traderCustomerId);
        Integer traderId = traderCustomerInfoDto.getTraderId();

        int currentNum = 0;
        for(SyncDataErpDto syncDataErpDto : syncDataErpDtoList){
            currentNum++;
            XxlJobLogger.log("当前正在同步的订单,{}/{},{}",currentNum,CollectionUtils.size(syncDataErpDtoList),syncDataErpDto.getBusinessNo());
            String vdErpDomain = vdBaseCompanyInfoDto.getErpDomain();
            String j = null;
            String businessNo = syncDataErpDto.getBusinessNo();
            JSONObject jsonObjectCheck = HttpClientUtil.httpPost(vdErpDomain+"/api/flowOrder/queryByBusinessNo.do?businessNo="+businessNo, j);
            String checkResult = jsonObjectCheck.getString("data");
            if("Y".equals(checkResult)){
                log.warn("业务流转单数据，无需同步,{}",syncDataErpDto);
                syncDataErpDto.setRemark("业务流转单数据，无需同步");
                syncDataErpDto.setProcessStatus(ErpConst.TWO);
                syncDataErpDto.setUpdateTime(new Date());
                syncDataErpApiService.updateByPrimaryKeySelective(syncDataErpDto);
                continue;
            }
            String shortName = syncDataErpDto.getTargetErp();
            if(StringUtils.isEmpty(shortName)){
                log.warn("子公司无ERP系统，无需同步,{}",syncDataErpDto);
                syncDataErpDto.setRemark("子公司无ERP系统，无需同步");
                syncDataErpDto.setProcessStatus(ErpConst.TWO);
                syncDataErpDto.setUpdateTime(new Date());
                syncDataErpApiService.updateByPrimaryKeySelective(syncDataErpDto);
                continue;
            }

            BaseCompanyInfoDto targetCompanyInfo = baseCompanyInfoApiService.selectBaseCompanyByShortName(shortName);


//            Integer traderIdSync = baseCompanyInfoDetailDto.getTraderIdSync();
//            String traderNameSync = baseCompanyInfoDetailDto.getTraderNameSync();
            String traderContactNameSync = baseCompanyInfoDetailDto.getTraderContactNameSync();
            String invoiceTraderContactMobileSync = baseCompanyInfoDetailDto.getInvoiceTraderContactMobileSync();
            String diliveryProvinceIdSync = baseCompanyInfoDetailDto.getDiliveryProvinceIdSync();
            String diliveryCityIdSync = baseCompanyInfoDetailDto.getDiliveryCityIdSync();
            String diliveryAreaIdSync = baseCompanyInfoDetailDto.getDiliveryAreaIdSync();
            String diliveryAddressSync = baseCompanyInfoDetailDto.getDiliveryAddressSync();
            //Integer bdSupplyTraderId = baseCompanyInfoDetailDto.getBdSupplyTraderId();


            BuyorderVo buyorderVo = buyorderService.getBuyorderVoByOrderNo(businessNo);
            if(buyorderVo == null){
                log.warn("采购订单未找到{}",businessNo);
                continue;
            }

            OrderData orderData = new OrderData();
            orderData.setCompanyId(traderId);
            orderData.setCompanyName(erpCompanyName);//传入当前ERP的系统名称
            orderData.setInvoiceTraderContactName(traderContactNameSync);
            orderData.setInvoiceTraderContactMobile(invoiceTraderContactMobileSync);
            orderData.setPhone(invoiceTraderContactMobileSync);
            orderData.setUsername(erpCompanyName);//传入当前ERP的系统名称
            orderData.setDeliveryLevel1Id(diliveryProvinceIdSync.split(",")[0]);
            orderData.setDeliveryLevel2Id(diliveryCityIdSync.split(",")[0]);
            orderData.setDeliveryLevel3Id(diliveryAreaIdSync.split(",")[0]);
            orderData.setDeliveryUserAddress(diliveryAddressSync);
            orderData.setDeliveryUserArea(diliveryProvinceIdSync.split(",")[1]+" "+diliveryCityIdSync.split(",")[1]+" "+diliveryAreaIdSync.split(",")[1]);
            orderData.setDeliveryUserName(traderContactNameSync);
            orderData.setDeliveryUserPhone(invoiceTraderContactMobileSync);
            orderData.setDeliveryType(482);
            orderData.setDelayDelivery(0);
            orderData.setIsPrintout(2);
            orderData.setLogisticsComments("");
            orderData.setAdditionalClause("");
            orderData.setComments("");
            orderData.setInvoiceMethod(5);
            orderData.setInvoiceType(972);
            orderData.setIsSendInvoice(0);
            orderData.setIsCoupons(0);
            orderData.setOrderStatus(0);
            orderData.setPaymentMode(1);
            orderData.setTraderId(traderId);
            orderData.setDeliveryDirect( (buyorderVo.getDeliveryDirect() !=null && buyorderVo.getDeliveryDirect()==1)?"Y":"N");
            //开始处理订单明细
            List<BuyorderGoodsVo> erpBuyOrderGoodsList = buyorderVo.getBuyorderGoodsVoList();
            if(CollectionUtils.isEmpty(erpBuyOrderGoodsList)) {
                logger.info("erp采购单号无明细跳过:{}",buyorderVo.getBuyorderNo());
                continue;
            }
            //设置订单明细
            List<OrderGoodsData> goodsList = new ArrayList<>();
            BigDecimal jxSalePrice = BigDecimal.ZERO;
            for (BuyorderGoodsVo erpBuyOrderGoods : erpBuyOrderGoodsList) {
                OrderGoodsData orderGoodsData = new OrderGoodsData();
                orderGoodsData.setIsCoupons(0);
                orderGoodsData.setIsDirectPurchase(0);
                orderGoodsData.setJxSalePrice(erpBuyOrderGoods.getPrice());
                orderGoodsData.setMarketMomey(erpBuyOrderGoods.getPrice());
                orderGoodsData.setProductNum(erpBuyOrderGoods.getNum());
                orderGoodsData.setSkuAmount(erpBuyOrderGoods.getPrice().multiply(new BigDecimal(erpBuyOrderGoods.getNum())));
                orderGoodsData.setSkuNo(erpBuyOrderGoods.getSku());
                orderGoodsData.setHaveInstallation(0);
                orderGoodsData.setDeliveryCycle(erpBuyOrderGoods.getDeliveryCycle());// 由原来的默认3天调整取为贝登采购订单的货期-16. 功能调整：采购单生成的销售订单货期调整
                orderGoodsData.setDeliveryDirect(1);                                 // 由普发改为直发-16. 功能调整：采购单生成的销售订单货期调整
                jxSalePrice = jxSalePrice.add(orderGoodsData.getSkuAmount());
                goodsList.add(orderGoodsData);
            }
            orderData.setJxSalePrice(jxSalePrice);
            orderData.setMarketMomney(jxSalePrice);
            orderData.setTotalCouponedAmount(jxSalePrice);
            orderData.setGoodsList(goodsList);
            orderData.setOriginBuyOrderNo(buyorderVo.getBuyorderNo());

            logger.info("erp的采购单同步到{}销售单,erp采购单号:{}",shortName,buyorderVo.getBuyorderNo());
            XxlJobLogger.log("erp的采购单同步到{}销售单,erp采购单号:{}",shortName,buyorderVo.getBuyorderNo());
            String requestUrl = targetCompanyInfo.getErpDomain()+SyncDataTypeEnum.BUYORDER_TO_SALEORDER.getDataInputUrl();
            try {
                //调用ERP接口进行插入erp销售单
                logger.info("调用ERP接口进行插入erp销售单，入参：{}",JSON.toJSONString(orderData));
                JSONObject jsonObject = HttpClientUtil.httpPost(requestUrl, JSON.toJSONString(orderData));
                logger.info("调用ERP接口进行插入erp销售单，返回:{}",jsonObject);
                if(jsonObject == null) {
                    //未成功发起接口请求待重试、
                    logger.error("未成功发起接口请求:{},参数：{}，待补偿定时任务重试...",requestUrl,JSON.toJSONString(orderData));
                    syncDataErpDto.setUpdateTime(new Date());
                    syncDataErpDto.setRemark("接收方未成功返回");
                    syncDataErpApiService.updateByPrimaryKeySelective(syncDataErpDto);
                    continue;
                }
                if(jsonObject.getInteger("code") == 0){
                    JSONObject data = jsonObject.getJSONObject("data");
                    String saleOrderNo = data.getString("saleorderNo");
                    syncDataErpDto.setRemark("{\"saleOrderNo\":\""+saleOrderNo+"\"}");
                    syncDataErpDto.setProcessStatus(ErpConst.TWO);
                    syncDataErpDto.setUpdateTime(new Date());
                    syncDataErpApiService.updateByPrimaryKeySelective(syncDataErpDto);
                    saveReal(buyorderVo.getBuyorderNo(),saleOrderNo);
                }
            } catch (Exception e1) {
                logger.error("接口请求:{},执行失败,参数：{}...",requestUrl,JSON.toJSONString(orderData));
                syncDataErpDto.setUpdateTime(new Date());
                syncDataErpDto.setRemark("请求失败过程中，异常了，待下次重试");
                syncDataErpApiService.updateByPrimaryKeySelective(syncDataErpDto);
            }

        }
        return SUCCESS;
    };


    @Autowired
    private XfjxBuyorderToSaleorderSyncMapper xfjxBuyorderToSaleorderSyncMapper;

    private void saveReal(String originBuyOrderNo,String saleorderNo){
        XfjxBuyorderToSaleorderSync buyorderToSaleorderSync = new XfjxBuyorderToSaleorderSync();
        buyorderToSaleorderSync.setBuyorderNo(originBuyOrderNo);
        buyorderToSaleorderSync.setSyncDirectTo(2);
        buyorderToSaleorderSync.setSyncStatus(1);
        buyorderToSaleorderSync.setBdSaleorderNo(saleorderNo);
        Date now = new Date();
        buyorderToSaleorderSync.setAddTime(now);
        buyorderToSaleorderSync.setUpdateTime(now);
        xfjxBuyorderToSaleorderSyncMapper.insert(buyorderToSaleorderSync);
    }

}
