package com.newtask;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.vedeng.base.api.dto.kuaidi.KuaiDiReqDTO;
import com.vedeng.base.api.dto.kuaidi.LogisticsDTO;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.putHCutil.service.HcSaleorderService;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.MD5;
import com.vedeng.infrastructure.logistics.api.KuaiDiApi;
import com.vedeng.logistics.dao.ExpressMapper;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.ExpressDetail;
import com.vedeng.logistics.model.LogisticsDetail;
import com.vedeng.logistics.service.ExpressService;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.soap.service.VedengSoapService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import java.util.*;

@JobHandler(value = "logisticsInfoTask")
@Component
public class LogisticsInfoTask extends AbstractJobHandler {
    public static Logger logger = LoggerFactory.getLogger(LogisticsInfoTask.class);


    @Autowired
    private ExpressService expressService;
    @Autowired
    private ExpressMapper expressMapper;

    @Value("${http_url}")
    protected String httpUrl;

    @Value("${client_id}")
    protected String clientId;

    @Value("${client_key}")
    protected String clientKey;

    @Value("${file_url}")
    protected String picUrl;

    @Value("${kuaidi.cutomer}")
    String customer;
    @Value("${kuaidi.key}")
    String key;

    @Autowired
    private KuaiDiApi kuaiDiApi;

    @Value("${baseserver.kuaidi.erp.authorization}")
    String kuaidiAuthorization;

    private static final Integer BUSINESS_TYPE_SALE_ORDER = 496;

    private static final Integer BUSINESS_TYPE_INVOICE = 497;


    /**
     * 同步快递信息
     */
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        if (StringUtils.isBlank(param) && StringUtils.contains(httpUrl, "qa.godbcenter.ivedeng.com")) {
            XxlJobLogger.log("测试环境不允许批量执行");
            return ReturnT.FAIL;
        }
        // 查询未到货的订单的物流单号
        List<Express> epList = expressMapper.getExpressInfoListForSaleorder();
        if (CollectionUtils.isNotEmpty(epList)) {
            execute(param, epList, "销售");
        }
        return SUCCESS;
    }


    private void execute(String param, List<Express> EpList, String type) {
        Set<String> lnoList = Sets.newHashSet();
        for (int i = 0; i < EpList.size(); i++) {
            // 物流单号
            String LNO = EpList.get(i).getLogisticsNo();
            XxlJobLogger.log("开始同步{}快递单状态::{}::{}", type, EpList.size(), LNO);
            logger.info("开始同步{}快递单状态::{}::{}", type, EpList.size(), LNO);

            if (StringUtils.isNotBlank(param) && !StringUtils.equals(LNO, param)) {
                continue;
            }
            if (StringUtils.isNotBlank(param) && !StringUtils.equals(LNO, param)) {
                XxlJobLogger.log("指定快递单同步：" + param);
                continue;
            }
            // 获取快递公司编号
            if (!"".equals(LNO) && !lnoList.contains(LNO)) {
                lnoList.add(LNO);
                // 增加传参收件手机号
                String phone = "";
                List<ExpressDetail> expressDetails = expressMapper.getExpressDetailByExpressId(EpList.get(i).getExpressId());
                Integer businessType = expressDetails.get(0).getBusinessType();
                // 快递业务类型为销售订单
                if (Objects.equals(BUSINESS_TYPE_SALE_ORDER, businessType)) {
                    List<String> phoneList = expressMapper.getPhoneByBusinessTypeSaleOrder(EpList.get(i).getExpressId());
                    if (CollectionUtils.isNotEmpty(phoneList) && phoneList.size() == 1) {
                        XxlJobLogger.log("增加传参收件手机号--业务类型为销售订单--expressId={}--logisticsNo={}--phone={}",
                                EpList.get(i).getExpressId(), LNO, phoneList.get(0));
                        phone = phoneList.get(0);
                    } else {
                        XxlJobLogger.log("增加传参收件手机号--业务类型为销售订单--expressId={}--logisticsNo={},未查询到phone或phone不唯一",
                                EpList.get(i).getExpressId(), LNO);
                    }
                }
                // 快递业务类型为发票寄送
                if (Objects.equals(BUSINESS_TYPE_INVOICE, businessType)) {
                    List<String> phoneList = expressMapper.getPhoneByBusinessTypeInvoice(EpList.get(i).getExpressId());
                    if (CollectionUtils.isNotEmpty(phoneList) && phoneList.size() == 1) {
                        XxlJobLogger.log("增加传参收件手机号--业务类型为发票寄送--expressId={}--logisticsNo={}--phone={}",
                                EpList.get(i).getExpressId(), LNO, phoneList.get(0));
                        phone = phoneList.get(0);
                    } else {
                        XxlJobLogger.log("增加传参收件手机号--业务类型为发票寄送--expressId={}--logisticsNo={},未查询到phone或phone不唯一",
                                EpList.get(i).getExpressId(), LNO);
                    }
                }
                ResultInfo logisticsInfoResult = queryInfo(EpList.get(i).getCode(), LNO, phone);

                if (logisticsInfoResult == null || logisticsInfoResult.getData() == null) {
                    continue;
                }

                JSONObject rd = JSONObject.fromObject(logisticsInfoResult.getData().toString());
                if (rd.isNullObject()) {
                    continue;
                }
                rd.put("expressId", EpList.get(i).getExpressId());
                //更新快递单状态
                updateExpressByKuai100(rd, LNO);
                //设置快递详情
                updateLogisticDetail(logisticsInfoResult.getData().toString(), LNO, EpList.get(i).getLogisticsId());
            }
        }
    }

    private void updateLogisticDetail(String kuaidi100Result, String logisticNo, Integer logisticsId) {
        try {
            List<LogisticsDetail> ldList = new ArrayList<LogisticsDetail>();
            LogisticsDetail ld = new LogisticsDetail();
            ld.setModTime(DateUtil.sysTimeMillis());
            ld.setLogisticsNo(logisticNo);
            ld.setContent(kuaidi100Result);
            ld.setLogisticsId(logisticsId);
            ldList.add(ld);
            expressMapper.batchInsert(ldList);
        } catch (Exception e) {
            logger.error("更新快递信息失败:" + logisticNo,e);
        }
    }

    public void updateExpressByKuai100(net.sf.json.JSONObject rd, String logisticNo) {
        if (rd == null) {
            return;
        }
        if ("ok".equalsIgnoreCase(rd.getString("message"))) {
            Express e = new Express();
            e.setLogisticsNo(rd.getString("nu"));
            e.setExpressId(Integer.parseInt(rd.getString("expressId")));
            JSONArray ja = rd.getJSONArray("data");
            JSONObject jl = new JSONObject();
            if (CollUtil.isNotEmpty(ja)) {
                jl = ja.getJSONObject(0);
            }
            Long arrTime = 0L;
            if (jl.getString("time") != null) {
                arrTime = DateUtil.convertLong(jl.getString("time"), "yyyy-MM-dd HH:mm:ss");
            } else {
                arrTime = DateUtil.sysTimeMillis();
            }
            if ("1".equals(rd.getString("ischeck"))) {
                e.setArrivalStatus(2);
                e.setArrivalTime(arrTime);
                //签收才同步
                try {
                    List<Express> tempList = new ArrayList<>();
                    tempList.add(e);
                    expressService.editExpres(tempList);
                } catch (Exception x) {
                    logger.error("获取快递100成功，但快递更新错误 {} {} ", rd.getString("nu"), logisticNo, x);
                }
            } else {
                e.setArrivalStatus(0);
                e.setArrivalTime(Long.valueOf("0"));
            }
        } else {

            logger.info("快递还未签收:{} {}", logisticNo, rd);
        }
    }

    public ResultInfo queryInfo(String com, String logisticsNo, String phone) {
        if(StringUtils.isBlank(com)){
            return null;
        }
        String param = "{\'com\':\'" + com + "\',\'num\':\'" + logisticsNo + "\',\'phone\':\'" + phone + "\'}";
        String sign = MD5.encode(param + key + customer);
        HashMap<String, String> params = new HashMap<String, String>();
        params.put("param", param);
        params.put("sign", sign);
        params.put("customer", customer);
        KuaiDiReqDTO kuaiDiReqDTO = new KuaiDiReqDTO();
        kuaiDiReqDTO.setAuthorization(kuaidiAuthorization);
        KuaiDiReqDTO.KuaiDiParam kuaiDiParam = new KuaiDiReqDTO.KuaiDiParam();
        kuaiDiParam.setCom(com);
        kuaiDiParam.setNum(logisticsNo);
        kuaiDiParam.setPhone(phone);
        kuaiDiReqDTO.setParam(kuaiDiParam);

        try {
            logger.info("调用快递服务参数信息:{} {}",logisticsNo,phone);
            RestfulResult<LogisticsDTO> waybillState = kuaiDiApi.getWaybillState(kuaiDiReqDTO);
            logger.info("调用快递服务返回信息:{}", JSON.toJSONString(waybillState));
             if (waybillState.isSuccess() && Objects.nonNull(waybillState.getData())) {
                return ResultInfo.success(waybillState.getMessage(), JSON.toJSONString(waybillState.getData()));
            }else{
                 if(!waybillState.isSuccess()) {
                     logger.error("调用快递服务失败:{} {} response:{}", logisticsNo, phone, JSON.toJSONString(waybillState));
                 }
            }
            XxlJobLogger.log("调用快递服务失败:{} {} response:{}",logisticsNo,phone,JSON.toJSONString(waybillState));
            Thread.sleep(5000);
            return null;
        } catch (Exception e) {
            logger.error("调用Base快递服务失败{} {}",logisticsNo,phone, e);
        }
        return null;
    }

}
