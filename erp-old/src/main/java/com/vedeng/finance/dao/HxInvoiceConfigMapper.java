package com.vedeng.finance.dao;

import com.vedeng.finance.model.HxInvoiceConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface HxInvoiceConfigMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(HxInvoiceConfig record);

    int insertSelective(HxInvoiceConfig record);

    HxInvoiceConfig selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(HxInvoiceConfig record);

    int updateByPrimaryKey(HxInvoiceConfig record);

    /**
     * 获取异常票配置信息
     * @return
     */
    List<HxInvoiceConfig> getHxInvoiceConfig();


    /**
     * 根据类型获取配置信息
     * @param configTypes
     * @return
     */
    List<HxInvoiceConfig> getInvoiceConfigByType(@Param("configTypes") List<Integer> configTypes);

    /**
     * 根据类型删除配置信息
     * @param configType
     * @return
     */
    int deleteByConfigType(@Param("configType") Integer configType);

    /**
     * 根据唯一id删除配置信息
     * @param uniqueId
     * @return
     */
    int deleteByUniqueId(@Param("uniqueId") Long uniqueId);


    int updateByUniqueIdSelective(HxInvoiceConfig record);


    /**
     * 根据唯一id获取配置信息
     * @param uniqueId
     * @return
     */
    HxInvoiceConfig getHxInvoiceConfigByUniqueId(@Param("uniqueId") Long uniqueId);


    HxInvoiceConfig getInvoiceConfigByTypeAndContent(@Param("configType") Integer configType, @Param("content") String content);

    int deleteInvoiceConfig();
}