function saveWebAccount(){
	checkLogin();
	if($("#traderCustomerId").val() == '' || $("#traderCustomerId").val() <= 0){
		layer.alert("请添加客户");
		return false;
	}
	if($("#traderContactId").val() == 0){
		layer.alert("联系人不允许为空");
		return false;
	}

	if($("#traderAddressId").val() == 0){
		layer.alert("联系地址不允许为空");
		return false;
	}
	// 验证选择的客户归属销售  与  当前销售是否是同一人
	var traderCustomerId = $("#traderCustomerId").val();
	if(traderCustomerId != ''){
		$.ajax({
			async : false,
			type : "POST",
			url : page_url + "/trader/accountweb/vailTraderUser.do",
			data :{'traderCustomerId': traderCustomerId,'owner':'sale'},
			dataType : 'json',
			success : function(data) {
				if(data.code == -1){
					layer.confirm(data.message, {
						btn: ['确定', '取消'] //按钮
					}, function () {
						$("input[name='optType']").val("saleWebConfirm");
						$('#myform').submit();
						return true;// 确认
					}, function () {
						// 取消
					});
				} else {
					$('#myform').submit();
				}
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});

	}
};

//客户所在地，联系人
function init(id,traderId,area){
	checkLogin();
	if(id > 0){
		$("#area").html(area);
		$.ajax({
			type : "POST",
			url : page_url+"/order/buyorder/getContactsAddress.do",
			data :{'traderId':traderId,'traderType':1},
			dataType : 'json',
			success : function(data) {
				if(data.code == 0){
					$option = "<option value='0' title='请选择'>请选择</option>";
					$.each(data.data,function(i,n){
						$option += "<option value='"+data.data[i]['traderContactId']+"' title='"+data.data[i]['mobile']+"'>"+data.data[i]['name']+"|"+data.data[i]['mobile']+"</option>";
					});
					$("select[name='traderContactId']").html($option);

					$option = "<option value='0'>请选择</option>";
					$.each(data.listData,function(i,n){
						$option += "<option value='"+data.listData[i]['traderAddress']['traderAddressId']+"'>"+data.listData[i]['area']+" "+data.listData[i]['traderAddress']['address']+"</option>";
					});
					$("select[name='traderAddressId']").html($option);
				}else{
					layer.alert("该客户暂无联系人/地址，请先维护客户联系人/地址");
				}
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	}else{
		layer.alert("操作失败");
	}
}

function addContacts() {
	$('#addCustomerContacts').attr('layerparams','{"width":"430px","height":"220px","title":"添加联系人","link":"/order/saleorder/addContact.do?traderId='+$('#traderId').val()+'&traderCustomerId='+$('#traderCustomerId').val()+'&indexId=webId"}');
	$('#addCustomerContacts').click();
}
function addAddress() {
	$('#add_address').attr('layerparams','{"width":"600px","height":"200px","title":"添加地址","link":"/order/saleorder/addAddress.do?traderId='+$('#traderId').val()+'&indexId=webId"}');
	$('#add_address').click();
}

function transferCertificate(erpAccountId,traderId,traderCutomerId,type) {
	var _self=self;
	var index1=layer.confirm("确认覆盖原有资质信息吗？", {
		btn: ['确定','取消'] //按钮
	}, function(){
		$.ajax({
			type: "POST",
			url: page_url+"/aftersales/webaccount/transferCertificate.do",
			data: {'erpAccountId':erpAccountId,'traderId':traderId,'type':type},
			dataType:'json',
			success: function(data){
				if (data.code == 0) {
					layer.close(index1)
					layer.confirm("操作成功", {
						btn: ['返回','维护公司资质'] //按钮
					}, function(){
						window.location.reload();
					}, function(){
						window.location.reload();
						var frontPageId = $(window.parent.parent.document).find('.active').eq(1).attr('id');
						var url=page_url+"/trader/customer/getFinanceAndAptitude.do?traderId="+traderId+"&traderCustomerId="+traderCutomerId;
						var item = { 'id': traderCutomerId, 'name':"财务与资质信息", 'url': url, 'closable': true };
						// _self.parent.parent.closableTab.addTab(item);
						// _self.parent.parent.closableTab.resizeMove();
						// $(window.parent.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);

						if (typeof(_self.parent.parent.closableTab) != 'undefined') {
							_self.parent.parent.closableTab.addTab(item);
							_self.parent.parent.closableTab.resizeMove();
							$(window.parent.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
						}else{
							try{
								var uniqueName = url.replace('./', '').replace(/["&'./:=%?[\]]/gi, '-').replace(/(--)/gi, '');
								window.parent.parent.postMessage({
									from:'ez',
									name: title,
									url:url,
									id:"tab-"+uniqueName
								}, '*');
							}catch (e){}
						}

					});

				} else {
					layer.close(index1)
					layer.alert(data.message);
				}

			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	}, function(){
	});
}

function searchCustomer() {
    $("#searchTrader").click();
}

function unbindWithTrader(webAccountId, traderId) {
    if (webAccountId == null) {
        layer.alert("注册用户未关联客户，无法进行此操作", {icon: 5})
        return
    }
    if (traderId == null || traderId === 0) {
        layer.alert("注册用户未关联客户，无法进行此操作", {icon: 5})
        return
    }

    layer.open({
        type: 1,
        title: '确定解除关联客户吗？',
        area: ['460px', '300px'],
        resize: false,
        content: $('#unbindWebAccountView').html(),
        btn: ['确定', '取消']
        , yes: function (index, layero) {
			if ($('#unbindReason').val() === '0'){
				layer.alert("请选择解除关联客户原因", {icon: 5})
				return
			}
            if ($('#unbindReason').val() === '-1') {
                var remark = $('#unbindRemark').val();
				if (remark.length === 0) {
					layer.alert("请填写备注原因", {icon: 5})
					return
				}
                if (remark.length > 30) {
                    layer.alert("备注原因不超过30字", {icon: 5})
                    return
                }
            }

            $.ajax({
                type: "POST",
                url: page_url + "/aftersales/webaccount/unbindAssociationWithTrader.do",
                data: {
                    'erpAccountId': webAccountId,
                    'traderId': traderId,
                    'reasonType': $('#unbindReason').val(),
                    'remark': $('#unbindRemark').val(),
                },
                dataType: 'json',
                success: function (data) {
                    if (data.code == 0) {
                        layer.alert('操作成功', {icon: 1});
                        window.location.reload();
                    } else {
                        layer.alert(data.message, {icon: 5});
                    }
                },
                error: function (data) {
                    if (data.status == 1001) {
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi", {icon: 5})
                    }
                }
            });

            layer.close(index)
        }
        , btn2: function (index, layero) {
            layer.close(index);
        }
    });

    $('#unbindReason').change(function () {
        if ($('#unbindReason').val() === '-1') {
            $('#unbindRemarkContent').css("display", "block")
        } else {
            $('#unbindRemarkContent').css("display", "none")
            $('#unbindRemark').val(null)
        }
    });
}