package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSONObject;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeeExpressCostDto;
import com.vedeng.erp.kingdee.service.KingDeeExpressCostApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ExpressCostPushAddWrite extends BaseWriter<KingDeeExpressCostDto> {

    @Autowired
    private KingDeeExpressCostApiService kingDeeExpressCostApiService;

    @Override
    public void doWrite(KingDeeExpressCostDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("ExpressCostPushAddWrite.doWrite，item：{}", JSONObject.toJSONString(item));
        item.setKingDeeBizEnums(KingDeeBizEnums.saveExpressCost);
        kingDeeExpressCostApiService.register(item,true);
    }
}
