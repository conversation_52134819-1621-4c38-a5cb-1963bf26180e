package com.vedeng.erp.buyorderexpense.web.api;

import com.common.dto.StepsNodeDto;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;
import com.vedeng.erp.buyorder.service.BuyorderExpenseApiService;
import com.vedeng.erp.buyorderexpense.service.BuyorderExpenseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description 采购费用单 数据接口
 * @date 2022/8/22 13:18
 **/
@ExceptionController
@RestController
@Slf4j
@RequestMapping("/buyorderExpense")
public class BuyorderExpenseApi {

    @Autowired
    private BuyorderExpenseService buyorderExpenseService;

    @Autowired
    private BuyorderExpenseApiService buyorderExpenseApiService;

    /**
     * 采购费用单详情
     *
     * @param buyorderExpenseId 采购费用单id
     * @return R
     */
    @RequestMapping(value = "/getDetail", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<BuyorderExpenseDto> getDetail(@RequestParam Integer buyorderExpenseId) {
        return R.success(buyorderExpenseService.viewDetail(buyorderExpenseId));
    }

    /**
     * 根据待采购订单信息采购费用单数据
     *
     * @param saleOrderGoodsIdList 销售明细id
     * @return R
     */
    @RequestMapping(value = "/byPreBuyorderGetDetail", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<BuyorderExpenseDto> byPreBuyorderGetDetail(@RequestBody List<Integer> saleOrderGoodsIdList) {
        return R.success(buyorderExpenseService.byPreBuyorderGetDetail(saleOrderGoodsIdList));
    }

    @RequestMapping(value = "/checkUser")
    @NoNeedAccessAuthorization
    public R<Integer> checkUser(@RequestParam Integer buyorderExpenseId) {
        return R.success(buyorderExpenseApiService.checkUser(buyorderExpenseId));
    }

    /**
     * 新增采购费用单
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public R<Integer> add(@RequestBody BuyorderExpenseDto buyorderExpenseDto) {
        return R.success(buyorderExpenseApiService.add(buyorderExpenseDto));
    }


    /**
     * 编辑页获取采购费用单详情
     */
    @RequestMapping(value = "/getBuyorderExpenseEditInfo", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<BuyorderExpenseDto> getBuyorderExpenseEditInfo(@RequestParam Integer buyorderExpenseId) {
        return R.success(buyorderExpenseService.getBuyorderExpenseEditInfo(buyorderExpenseId));
    }


    /**
     * 编辑采购费用单提交
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public R<?> update(@RequestBody BuyorderExpenseDto buyorderExpenseDto) {
        buyorderExpenseApiService.update(buyorderExpenseDto);
        return R.success();
    }

    /**
     * 关闭采购费用单
     *
     * @param buyorderExpenseId 采购费用单id
     * @return R
     */
    @RequestMapping(value = "/closeBuyOrderExpense", method = RequestMethod.POST)
    public R<?> closeBuyOrderExpense(Integer buyorderExpenseId) {
        buyorderExpenseApiService.closeBuyOrderExpense(buyorderExpenseId);
        return R.success();
    }

    /**
     * 完结采购费用单
     *
     * @param buyorderExpenseId 采购费用单id
     * @return R
     */
    @RequestMapping(value = "/completeBuyOrderExpense", method = RequestMethod.POST)
    public R<?> completeBuyOrderExpense(Integer buyorderExpenseId) {
        return R.success(buyorderExpenseApiService.completeBuyOrderExpense(buyorderExpenseId));
    }

    /**
     * 电子签章按钮
     *
     * @param buyorderExpenseId 采购费用单
     * @return R
     */
    @ResponseBody
    @RequestMapping(value = "/signature")
    @NoNeedAccessAuthorization
    public R<?> signature(Integer buyorderExpenseId) {
        buyorderExpenseApiService.signature(buyorderExpenseId);
        return R.success();
    }


    /**
     * 主进度
     *
     * @param buyorderExpenseId 费用单id
     * @return 进度节点
     */
    @RequestMapping(value = "/getOrderStepsNode", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<List<StepsNodeDto>> getStepsNode(Integer buyorderExpenseId) {
        return R.success(buyorderExpenseApiService.getOrderStepsNode(buyorderExpenseId));
    }

//    /**
//     * 主进度
//     * @param buyorderExpenseId 费用单id
//     * @return 进度节点
//     */
//    @RequestMapping(value = "/test")
//    @NoNeedAccessAuthorization
//    public R<?> test(Integer buyorderExpenseId) {
//        return R.success(buyorderExpenseApiService.paymentStatus(buyorderExpenseId));
//    }

}
