package com.vedeng.goods.dao;

import com.vedeng.goods.model.SpuDepartmentMappingGenerate;
import com.vedeng.goods.model.SpuDepartmentMappingGenerateExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * @deprecated ERP_LV_2020_86 科室迁移至了三级分类下
 */
@Deprecated
public interface SpuDepartmentMappingGenerateMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_DEPARTMENT_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int countByExample(SpuDepartmentMappingGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_DEPARTMENT_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int deleteByExample(SpuDepartmentMappingGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_DEPARTMENT_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int deleteByPrimaryKey(Integer spuDepartmentId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_DEPARTMENT_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int insert(SpuDepartmentMappingGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_DEPARTMENT_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int insertSelective(SpuDepartmentMappingGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_DEPARTMENT_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    List<SpuDepartmentMappingGenerate> selectByExample(SpuDepartmentMappingGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_DEPARTMENT_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    SpuDepartmentMappingGenerate selectByPrimaryKey(Integer spuDepartmentId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_DEPARTMENT_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByExampleSelective(@Param("record") SpuDepartmentMappingGenerate record, @Param("example") SpuDepartmentMappingGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_DEPARTMENT_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByExample(@Param("record") SpuDepartmentMappingGenerate record, @Param("example") SpuDepartmentMappingGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_DEPARTMENT_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByPrimaryKeySelective(SpuDepartmentMappingGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_DEPARTMENT_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByPrimaryKey(SpuDepartmentMappingGenerate record);
}