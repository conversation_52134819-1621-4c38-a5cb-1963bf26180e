package com.test.stock;

import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.model.OssInfo;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.service.impl.OssUtilsServiceImpl;
import com.wms.service.util.WMSFTPUtil;
import org.apache.commons.net.ftp.FTPClient;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName ImageTest.java
 * @Description TODO
 * @createTime 2021年01月08日 11:05:00
 */
public class ImageTest extends SpeicalWarehouseProcessor {

    @Autowired
    private WMSFTPUtil wmsftpUtil;

    @Autowired
    private OssUtilsServiceImpl ossUtilsService;

    @Resource
    private AttachmentMapper attachmentMapper;

    @Test
    public void test(){
        FTPClient ftpClient = wmsftpUtil.getFTPClient();
        String paths = "2007230000000001.JPG";
//        String paths = "/2020/12/09/V111126_0503_1_20201209191240.JPG;2007230000000001.JPG";

        String[] split = paths.split(";");
        for (String path : split) {
            InputStream inputStream = wmsftpUtil.parsePathDownLoadFile(ftpClient, path);
            OssInfo ossInfo = ossUtilsService.sendFile2Oss(path, inputStream,true);
            System.out.println(ossInfo.toString());
        }

    }

    @Test
    public void repath() throws Exception{
        File file = new File("/Users/<USER>/Downloads/erp1.txt");
        OutputStreamWriter writer = new FileWriter(file);
        BufferedWriter bufferedWriter = new BufferedWriter(writer);

        Map<String, Object> paramMap = new HashMap<>();
        // 附件类型
        paramMap.put("attachmentType", CommonConstants.ATTACHMENT_TYPE_974);
        List<Integer> attachmentFunction = new ArrayList<>();
        // 注册证附件/备案凭证附件
        attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_975);
        paramMap.put("attachmentFunction", attachmentFunction);
        paramMap.put("domain","file1.vedeng.com");
        List<Attachment> attachmentsList = attachmentMapper.getAttachmentsList(paramMap);
        for (Attachment attachment : attachmentsList) {
            String sql = "UPDATE T_ATTACHMENT SET URI = '#{URI}',ORIGINAL_FILEPATH = '#{ORIGINAL_FILEPATH}' WHERE ATTACHMENT_ID = #{ATTACHMENT_ID} ; \r\n";
            sql = sql.replace("#{ORIGINAL_FILEPATH}",attachment.getUri());
            String uri = attachment.getUri();
            String[] urlArray = uri.split("\\.");
            sql = sql.replace("#{URI}",urlArray[0] + "_bf."+urlArray[1]);
            sql = sql.replace("#{ATTACHMENT_ID}",attachment.getAttachmentId().toString());

            bufferedWriter.write(attachment.getAttachmentId()+";"+attachment.getUri()+"\r\n");
            bufferedWriter.flush();
        }
        bufferedWriter.close();
        writer.close();
    }

}
