package com.vedeng.erp.trader.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.trader.domain.entity.TraderCustomerMarketingPrincipalEntity;
import com.vedeng.erp.trader.dto.TraderCustomerMarketingPrincipalDto;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/15 14:45
 **/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface TraderCustomerMarketingPrincipalConvertor extends BaseMapStruct<TraderCustomerMarketingPrincipalEntity, TraderCustomerMarketingPrincipalDto> {
}