package com.vedeng.erp.saleorder.manager.esign;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.eventbus.Subscribe;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.OrderConstant;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.core.config.IObserver;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.listenerEvent.SaleOrderContractReturnEvent;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.common.util.ObjectUtils;
import com.vedeng.common.util.UrlUtils;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.dao.ProcinstMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.service.VerifiesRecordService;
import com.vedeng.trader.dao.RTraderJUserMapper;
import com.vedeng.trader.model.RTraderJUser;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.task.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 销售订单合同回传事件监听器
 * 处理销售业务流转单第一节点的合同回传记录逻辑
 * 参考 SaleOrderElectronicSignHandler 中的 updateRecordInfo 方法实现
 */
@Component
@Slf4j
public class SaleOrderContractReturnEventListener implements IObserver {

    @Autowired
    private AttachmentMapper attachmentMapper;

    @Autowired
    private SaleorderMapper saleorderMapper;

    @Autowired
    private RTraderJUserMapper rTraderJUserMapper;

    @Autowired
    private ProcinstMapper procinstMapper;

    @Autowired
    private VerifiesRecordService verifiesRecordService;

    @Autowired
    private ActionProcdefService actionProcdefService;

    @Autowired
    private SaleorderService saleorderService;

    // ProcessEngine 通过静态方法获取，避免循环依赖
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    /**
     * 处理销售订单合同回传事件
     * 
     * @param event 合同回传事件
     */
    @Subscribe
    public void handleSaleOrderContractReturn(SaleOrderContractReturnEvent event) {
        log.info("接收到销售订单合同回传事件，开始处理合同回传记录，订单ID: {}, 订单编号: {}, 流转单ID: {}", 
                event.getSaleorderId(), event.getSaleorderNo(), event.getFlowOrderId());
        
        try {
            // 参考 SaleOrderElectronicSignHandler 中的 updateRecordInfo 方法实现
            updateRecordInfo(event);
            
            log.info("销售订单合同回传记录处理完成，订单ID: {}, 订单编号: {}", 
                    event.getSaleorderId(), event.getSaleorderNo());
                    
        } catch (Exception e) {
            log.error("处理销售订单合同回传事件失败，订单ID: {}, 订单编号: {}", 
                    event.getSaleorderId(), event.getSaleorderNo(), e);
            // 不抛出异常，避免影响其他事件处理
        }
    }

    /**
     * 新增合同回传记录
     * 参考 SaleOrderElectronicSignHandler 中的 updateRecordInfo 方法实现
     *
     * @param event 合同回传事件
     */
    private void updateRecordInfo(SaleOrderContractReturnEvent event) {
        log.info("开始新增合同回传记录，订单ID: {}, 订单编号: {}, signCallbackDto: {}",
                event.getSaleorderId(), event.getSaleorderNo(), JSON.toJSONString(event));

        try {
            // 1. 解析文件URL
            String[] domainAndUriFromUrl = UrlUtils.getDomainAndUriFromUrl(event.getFileUrl());

            // 2. 创建附件记录
            Attachment attachment = new Attachment();
            attachment.setRelatedId(event.getSaleorderId());
            // 电子签章类型
            attachment.setAttachmentFunction(SysOptionConstant.ID_492);
            // 文件类型
            attachment.setAttachmentType(SysOptionConstant.ID_461);
            attachment.setSuffix("pdf");

            if (ArrayUtil.isNotEmpty(domainAndUriFromUrl)) {
                attachment.setDomain(domainAndUriFromUrl[0]);
                attachment.setUri(domainAndUriFromUrl[1]);
                attachment.setName(event.getFileName());
            }
            attachment.setAddTime(DateUtil.parseDateTime(event.getSignTime()).getTime());
            attachment.setIsDeleted(0);
            attachment.setAlt("合同自动审核");

            // 3. 插入附件记录
            int result = attachmentMapper.insertSelective(attachment);
            log.info("新增合同回传记录成功，relatedId: {}, 附件ID: {}",
                    attachment.getRelatedId(), attachment.getAttachmentId());

            if (ErpConst.ZERO.equals(result)) {
                log.error("新增合同回传记录失败，订单编号: {}", event.getSaleorderNo());
                throw new ServiceException("新增合同回传记录失败");
            }

            // 4. 更新合同回传状态
            saleorderMapper.uptContactStatus(OrderConstant.ORDER_CONTRACT_ISRETURN, attachment.getRelatedId());
            log.info("更新销售订单合同回传状态成功，订单ID: {}", event.getSaleorderId());

            // 5. 查询销售订单信息，发送站内信
            Saleorder saleOrderReturn = saleorderMapper.getSaleOrderById(event.getSaleorderId());
            if (saleOrderReturn != null) {
                RTraderJUser rTraderUser = rTraderJUserMapper.getUserByTraderId(saleOrderReturn.getTraderId());
                // 发送站内信给订单归属销售
                if (rTraderUser != null) {
                    Map<String, String> messageMap = new HashMap<>(1);
                    String url = "./order/saleorder/view.do?saleorderId=";
                    if (ObjectUtils.isEmpty(saleOrderReturn.getIsNew())) {
                        url = "./orderstream/saleorder/detail.do?saleOrderId=";
                    }
                    messageMap.put("saleorderNo", saleOrderReturn.getSaleorderNo());
                    log.info("站内信发送，saleOrderId: {}, saleOrderReturn: {}",
                            event.getSaleorderId(), JSON.toJSONString(saleOrderReturn));
                    MessageUtil.sendMessage(144, Collections.singletonList(rTraderUser.getUserId()),
                            messageMap, url + event.getSaleorderId());
                }
            }

            // 6. 创建审核记录
            addApplyValidContractReturn(event);

            log.info("合同回传记录处理完成，订单ID: {}, 订单编号: {}",
                    event.getSaleorderId(), event.getSaleorderNo());

        } catch (Exception e) {
            log.error("新增合同回传记录失败，订单ID: {}, 订单编号: {}",
                    event.getSaleorderId(), event.getSaleorderNo(), e);
            throw new RuntimeException("新增合同回传记录失败", e);
        }
    }

    /**
     * 新增审核记录
     * 参考 SaleOrderElectronicSignHandler 中的 addApplyValidContractReturn 方法实现
     * 支持多节点自动审核通过功能
     *
     * @param event 合同回传事件
     */
    private void addApplyValidContractReturn(SaleOrderContractReturnEvent event) {
        log.info("开始新增审核记录，订单ID: {}", event.getSaleorderId());

        try {
            Map<String, Object> variableMap = new HashMap<>();

            // 查询销售订单信息
            Saleorder saleOrder = new Saleorder();
            saleOrder.setSaleorderId(event.getSaleorderId());
            saleOrder.setOptType("orderDetail");
            Saleorder saleOrderInfo = saleorderService.getBaseSaleorderInfoNew(saleOrder);

            // 设置流程变量
            variableMap.put("saleorderInfo", saleOrderInfo);
            variableMap.put("currentAssinee", "admin");
            variableMap.put("processDefinitionKey", "contractReturnVerify");
            variableMap.put("businessKey", "contractReturnVerify_" + event.getSaleorderId());
            variableMap.put("relateTableKey", event.getSaleorderId());
            variableMap.put("relateTable", "T_SALEORDER");

            log.info("创建流程实例，流程定义Key: contractReturnVerify, 业务Key: contractReturnVerify_{}, 订单ID: {}",
                    event.getSaleorderId(), event.getSaleorderId());

            // 创建流程实例
            actionProcdefService.createProcessInstance(null, "contractReturnVerify",
                    "contractReturnVerify_" + event.getSaleorderId(), variableMap);

            // 自动完成所有审核节点
            autoCompleteAllApprovalNodes(event);

            // 7. 更新销售订单合同链接字段
            updateSaleOrderContractUrl(event);

            log.info("审核记录创建完成，订单ID: {}", event.getSaleorderId());

        } catch (Exception e) {
            log.error("新增审核记录失败，订单ID: {}", event.getSaleorderId(), e);
            // 参考原始逻辑，这里应该抛出异常以保持一致性
            throw new RuntimeException("新增审核记录失败", e);
        }
    }

    /**
     * 自动完成所有审核节点
     * 循环处理流程实例中的所有活跃任务，实现多节点自动审核通过
     *
     * @param event 合同回传事件
     */
    private void autoCompleteAllApprovalNodes(SaleOrderContractReturnEvent event) {
        String businessKey = "contractReturnVerify_" + event.getSaleorderId();
        log.info("开始自动完成所有审核节点，业务Key: {}", businessKey);

        try {
            // 设置认证用户为admin
            org.activiti.engine.impl.identity.Authentication.setAuthenticatedUserId("admin");

            // 获取TaskService
            org.activiti.engine.TaskService taskService = processEngine.getTaskService();

            // 循环处理所有审核节点，直到流程结束
            int maxIterations = 10; // 防止无限循环，最多处理10个节点
            int currentIteration = 0;

            while (currentIteration < maxIterations) {
                currentIteration++;
                log.info("第{}次循环检查审核节点，业务Key: {}", currentIteration, businessKey);

                // 根据BusinessKey获取当前流程实例的所有活跃任务
                List<org.activiti.engine.task.Task> activeTasks = taskService.createTaskQuery()
                        .processInstanceBusinessKey(businessKey)
                        .list();

                if (activeTasks == null || activeTasks.isEmpty()) {
                    log.info("没有找到活跃的审核任务，流程可能已结束，业务Key: {}", businessKey);
                    break;
                }

                log.info("找到{}个活跃的审核任务，业务Key: {}", activeTasks.size(), businessKey);

                // 处理每个活跃任务
                for (org.activiti.engine.task.Task task : activeTasks) {
                    try {
                        log.info("开始处理审核任务，任务ID: {}, 任务名称: {}, 审核人: {}",
                                task.getId(), task.getName(), task.getAssignee());

                        // 设置任务审核人为admin
                        taskService.setAssignee(task.getId(), "admin");

                        // 准备审核变量
                        Map<String, Object> variables = new HashMap<>();
                        variables.put("pass", true);
                        variables.put("currentAssinee", "admin");

                        // 添加审核备注
                        taskService.addComment(task.getId(), task.getProcessInstanceId(), "电子签章回传自动审核通过");

                        // 完成任务
                        ResultInfo<?> complementStatus = actionProcdefService.complementTask(null, task.getId(),
                                "电子签章回传自动审核通过", "admin", variables);

                        log.info("审核任务完成，任务ID: {}, 完成状态: {}", task.getId(), complementStatus.getData());

                        // 如果未结束，添加审核对应主表的审核状态
                        if (!"endEvent".equals(complementStatus.getData())) {
                            verifiesRecordService.saveVerifiesInfo(task.getId(), ErpConst.ZERO);
                        }

                        // 更新历史记录
                        updateHistoryRecord(task.getId(), event);

                    } catch (Exception e) {
                        log.error("处理审核任务失败，任务ID: {}, 任务名称: {}", task.getId(), task.getName(), e);
                        // 单个任务失败不影响其他任务的处理
                    }
                }

                // 检查流程是否已结束
                Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, businessKey);
                if ("审核完成".equals(historicInfo.get("endStatus"))) {
                    log.info("审核流程已完成，业务Key: {}", businessKey);
                    break;
                }

                // 短暂等待，避免过快循环
                Thread.sleep(1000);
            }

            if (currentIteration >= maxIterations) {
                log.warn("达到最大循环次数限制，可能存在无限循环，业务Key: {}", businessKey);
            }

            log.info("所有审核节点自动完成处理结束，业务Key: {}", businessKey);

        } catch (Exception e) {
            log.error("自动完成审核节点失败，业务Key: {}", businessKey, e);
            throw new RuntimeException("自动完成审核节点失败", e);
        }
    }

    /**
     * 更新销售订单合同链接字段
     * 在合同回传事件处理完成后，更新销售订单表中的CONTRACT_URL字段
     *
     * @param event 合同回传事件
     */
    private void updateSaleOrderContractUrl(SaleOrderContractReturnEvent event) {
        log.info("开始更新销售订单合同链接字段，订单ID: {}, 合同URL: {}",
                event.getSaleorderId(), event.getFileUrl());

        try {
            // 检查合同URL是否有效
            if (event.getFileUrl() == null || event.getFileUrl().trim().isEmpty()) {
                log.warn("合同URL为空，跳过更新操作，订单ID: {}", event.getSaleorderId());
                return;
            }

            // 参考 SaleOrderElectronicSignHandle 中的逻辑，使用现有的更新方法
            // 更新销售订单表中的合同链接字段
            saleorderMapper.updateContractUrlOfSaleorder(event.getSaleorderId(), event.getFileUrl());

            log.info("更新销售订单合同链接字段成功，订单ID: {}, 合同URL: {}",
                    event.getSaleorderId(), event.getFileUrl());

        } catch (Exception e) {
            log.error("更新销售订单合同链接字段失败，订单ID: {}, 合同URL: {}",
                    event.getSaleorderId(), event.getFileUrl(), e);
            // 合同链接更新失败不影响主流程，但需要记录错误日志
            // 不抛出异常，避免影响整个合同回传流程
        }
    }

    /**
     * 更新历史记录
     * 更新电子签章回传的历史记录信息
     *
     * @param taskId 任务ID
     * @param event 合同回传事件
     */
    private void updateHistoryRecord(String taskId, SaleOrderContractReturnEvent event) {
        try {
            Map<String, Object> map = new HashMap<>();
            map.put("taskId", taskId);

            List<Map<String, Object>> resultMap = procinstMapper.getActHiInfoByTaskId(map);
            if (resultMap != null && !resultMap.isEmpty()) {
                Map<String, Object> mapAdd = resultMap.get(0);
                mapAdd.put("ACT_NAME_", "电子签章回传");
                mapAdd.put("ASSIGNEE_", "admin");
                mapAdd.put("NAME_", "电子签章回传");
                mapAdd.put("END_TIME_", event.getSignTime());

                procinstMapper.updateActHiActinstInfoByTaskId(mapAdd);
                procinstMapper.updateActHiTaskinstInfoById(mapAdd);

                log.info("更新历史记录成功，任务ID: {}, 结束时间: {}", taskId, event.getSignTime());
            }
        } catch (Exception e) {
            log.error("更新历史记录失败，任务ID: {}", taskId, e);
            // 历史记录更新失败不影响主流程
        }
    }
}
