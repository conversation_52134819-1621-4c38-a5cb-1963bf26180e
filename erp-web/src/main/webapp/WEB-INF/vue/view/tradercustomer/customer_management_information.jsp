<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
            + path + "/";
%>
<c:set var="path" value="<%=basePath%>" scope="application" />
<link rel="stylesheet" href="<%=basePath%>static/css/general.css?rnd=${resourceVersionKey}" />
<link rel="stylesheet" href="<%=basePath%>static/css/manage.css" />
<%@ include file="../../../jsp/trader/customer/customer_tag.jsp"%>
<style >
    .el-card__header .clearfix{
        font-size: 16px;
        font-weight: 700;
    }
    .el-timeline-item__timestamp {
        width: 130px;
        text-align: right;
    }
    .el-timeline-item__timestamp {
        position: absolute;
        left: -140px; /* 调整时间戳的位置 */
        top: 0%;
        transform: translateY(-50%);
    }
    .el-descriptions-item__container{
        padding-left: 15px;
    }
    .el-descriptions__body{
        padding-top: 10px;
    }
    .el-tag--small {
        margin-left: 10px;
    }
    .el-table .el-table__cell {
        padding: 6px 0;
    }
    .parts{
        padding-top: 10px;
    }
    .main-container {
    	padding: 20px;
	}
	
	.parts-time {
	    display: flex; /* 使用 Flex 布局 */
	    align-items: center; /* 垂直居中 */
	}
	
	.date-container {
	    display: flex; /* 使用 Flex 布局 */
	    align-items: center; /* 垂直居中 */
	}
	
	.query-date {
	    margin-right: 10px; /* 调整查询日期文本和输入框之间的间距 */
	}
	
	.input-smaller96 {
	    width: 120px; /* 调整输入框的宽度，确保它们显示合适 */
	}
	
	.gang {
	    margin: 0 10px; /* 调整两个日期输入框之间的间距 */
	}
    i{
		background: none !important;
	}
	.el-date-editor .el-range-separator {
		width:8%
	}
</style>

<div id="app" style="display: none;">

    <el-card class="box-card" ref="jcbq">
        <el-descriptions title="管理信息" :column="2">
            <el-descriptions-item label="客户启用状态	">{{traderCustomerPortraitDto.traderCustomerIsEnable}}</el-descriptions-item>
        </el-descriptions>
    </el-card>



    <el-card class="box-card" ref="jcbq" v-if="traderCustomerPortraitDto.lifeCycle || traderCustomerPortraitDto.customerGrade != '()'">
        <el-descriptions title="决策标签" :column="2">
            <el-descriptions-item label="生命周期	">{{traderCustomerPortraitDto.lifeCycle}}</el-descriptions-item>
            <el-descriptions-item label="客户等级	">{{traderCustomerPortraitDto.customerGrade}}</el-descriptions-item>
        </el-descriptions>
    </el-card>

    <el-card class="box-card" ref="jylbq" v-if="traderCustomerPortraitDto.historyTransactionNum">
        <el-descriptions title="交易类标签" :column="2">
            <el-descriptions-item label="累计下单">{{traderCustomerPortraitDto.historyTransactionNum}}</el-descriptions-item>
            <el-descriptions-item label="历史交易总额">{{traderCustomerPortraitDto.historyTransactionAmount}}</el-descriptions-item>
            <el-descriptions-item label="首次下单时间">{{traderCustomerPortraitDto.firstOrderTime}}</el-descriptions-item>
            <el-descriptions-item label="最近下单时间">{{traderCustomerPortraitDto.lastOrderTime}}</el-descriptions-item>
            <el-descriptions-item label="近一年平均购买周期">{{traderCustomerPortraitDto.lastYearAveragePurchasePeriod}}</el-descriptions-item>
            <el-descriptions-item label="历史累计客单价">{{traderCustomerPortraitDto.historyCumulativeUnitPrice}}</el-descriptions-item>
            <el-descriptions-item label="订单覆盖科室" :span="2">{{traderCustomerPortraitDto.departmentCover}}</el-descriptions-item>
            <el-descriptions-item label="订单收货地区" :span="2">{{traderCustomerPortraitDto.orderCoverArea}}</el-descriptions-item>
            <el-descriptions-item label="订单装机地区" :span="2">{{traderCustomerPortraitDto.installedArea}}</el-descriptions-item>
            <el-descriptions-item label="交易时间" :span="2">
                <el-select v-model = "traderTimeValue" placeholder="请选择" size="mini" @change="handleChange">
                    <el-option
                            v-for="item in traderTimeOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                    </el-option>
                </el-select>
            </el-descriptions-item>
            <el-descriptions-item label="交易时间内品牌" :span="2">
                <el-tag :v-model="brandTagList" v-for="item in brandTagList" size="small">{{item}}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="交易时间内分类" :span="2">
                <el-tag :v-model="categoryTagList" v-for="item in categoryTagList" size="small">{{item}}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="近一年交易但近一月无交易分类" :span="2">
                <div style="flex-wrap: wrap;">
                    <el-tag v-for="item in specialCategoryTagList" size="small">{{item}}</el-tag>
                </div>
            </el-descriptions-item>

        </el-descriptions>
    </el-card>
    <el-card class="box-card" ref="jcbq" v-if="traderCustomerPortraitDto.latestInquiryContent || traderCustomerPortraitDto.latestInquiryTime != ''">
        <el-descriptions title="询价类标签" :column="2">
            <el-descriptions-item label="最近询价时间	">{{traderCustomerPortraitDto.latestInquiryTime}}</el-descriptions-item>
            <el-descriptions-item label="最近询价商品	">{{traderCustomerPortraitDto.latestInquiryContent}}</el-descriptions-item>
        </el-descriptions>
    </el-card>
    <el-card class="box-card" ref="jcbq" v-if="traderCustomerPortraitDto.historyCommunicationNum || traderCustomerPortraitDto.communicationRecord != ''">
        <el-descriptions title="销售行为标签" :column="2">
            <el-descriptions-item label="沟通记录	">{{traderCustomerPortraitDto.communicationRecord}}</el-descriptions-item>
            <el-descriptions-item label="历史沟通次数	">{{traderCustomerPortraitDto.historyCommunicationNum}}</el-descriptions-item>
        </el-descriptions>
    </el-card>
    
    <el-card class="box-card J-block" data-ref="yhfl" ref="yhfl" v-if="traderCustomerPortraitDto.belongPlatform ==1 &&  this.archiveCards.length > 0 ">
        <div class="parts">
            <div class="title-container title-container-blue">
                <div class="table-title nobor" style="font-size: 16px;">
                    客户档案&用户行为
                </div>
            </div>
            <el-tabs v-model="activeName" type="card" @tab-click="handleClick" >
           		<el-tab-pane label="客户档案" name="first">
           			<div class="main-container">
                		<div class="parts-time">
                			<div class="date-container">
	                			<div class="query-date">查询日期：</div>
	                			
                                <el-date-picker
								    v-model="dateRange"
								    type="daterange"
								    range-separator="至"
								    start-placeholder="开始日期"
								    end-placeholder="结束日期"
								    value-format="yyyy-MM-dd"
								    @change="handleDateChange"
									@blur="queryArchive">
								 </el-date-picker>
                                   
	                            
	                        </div>
                		</div>
                        <div class="parts-time">
                            <div class="activities-wrap" style="display: flex;">
                                <div id="card" class="cards-container" style="">
                                    <div
                                            class="card"
                                            v-for="(card, index) in archiveCards"
                                            :key="index"
                                            :class="{ 'selected': card.selected }"
                                            @click="toggleArchiveCardSelection(card)"
                                    >
                                        <div v-html="card.categoryName"></div>
                                    </div>
                                </div>
                                <%--    提示文案! 用户线上行为数据有1天延迟，今日访问数据正在解析中。--%>

                                <div style="display: flex; flex-direction: column; align-items: flex-start; justify-content: flex-start; width: 80%;   position: relative; padding: 40px 0 0 240px">


                                    <div class="timeline-container">
                                        <el-timeline :reverse="reverse">
                                            <el-timeline-item
                                                    v-for="(activity, index) in archiveActivities"
                                                    :key="index"
                                                    :timestamp="activity.eventTime"
                                            >
                                                <div v-html="activity.archivedFormat"></div>
                                            </el-timeline-item>
                                        </el-timeline>
                                        <div style="font-size: 14px; color: #09f; cursor: pointer;padding-bottom: 40px;" @click="getArchiveTimeLineMore" v-show="isShowTmeArchiveMore"  >更多</div>
                                        <div style="font-size: 14px; color: #8492a6; cursor:text;padding-bottom: 40px;"  v-show="!isShowTmeArchiveMore"  >没有了</div>

                                    </div>
                                </div>
                            </div>
                        </div>
                      </div>
                </el-tab-pane>
            	
                <el-tab-pane label="用户行为" name="second">
                        <div class="parts">
                            <div class="parts">
                                &nbsp; &nbsp;! 用户线上行为数据有1天延迟，今日访问数据正在解析中。
                            </div>
                            <div class="activities-wrap" style="display: flex;">
                                <div id="card" class="cards-container" style="">
                                    <div
                                            class="card"
                                            v-for="(card, index) in cards"
                                            :key="index"
                                            :class="{ 'selected': card.selected }"
                                            @click="toggleCardSelection(card)"
                                    >
                                        <div v-html="card.account"></div>
                                    </div>
                                </div>
                                <%--    提示文案! 用户线上行为数据有1天延迟，今日访问数据正在解析中。--%>

                                <div style="display: flex; flex-direction: column; align-items: flex-start; justify-content: flex-start; width: 80%;   position: relative; padding: 40px 0 0 240px">


                                    <div class="timeline-container">
                                        <el-timeline :reverse="reverse">
                                            <el-timeline-item
                                                    v-for="(activity, index) in activities"
                                                    :key="index"
                                                    :timestamp="activity.userActionTraceTime"
                                            >
                                                <div v-html="activity.userActionTraceDesc"></div>
                                                <a target="_blank" v-if="activity.userActionTracePcLink" :href="activity.userActionTracePcLink">{{activity.userActionTracePcLink}}</a>
                                            </el-timeline-item>
                                        </el-timeline>
                                        <div style="font-size: 14px; color: #09f; cursor: pointer;padding-bottom: 40px;" @click="getTimeLineMore" v-show="isShowTmeMore"  >更多</div>
                                        <div style="font-size: 14px; color: #8492a6; cursor:text;padding-bottom: 40px;"  v-show="!isShowTmeMore"  >没有了</div>

                                    </div>
                                </div>
                            </div>
                        </div>
                </el-tab-pane>
                
            </el-tabs>

        </div>
    </el-card>
    


</div>

<script src="${pageContext.request.contextPath}/static/api/trader/traderAction.js?rnd=${resourceVersionKey}"></script>
<script src="http://d3js.org/d3.v3.min.js" charset="utf-8"></script>

<script type="text/javascript">
    $(document).ready(function(){
        $('.category-wrap').find('.category-item').hide();
        $("#category-wrap-div").hover(function(){
            //在鼠标移动到元素上时要执行的操作
            $('.category-wrap').find('.category-item').slideToggle();
        },function(){
            //在鼠标离开元素时要执行的操作
            $('.category-wrap').find('.category-item').slideToggle();
        });
    });
    const traderId = '${traderId}';
    let vm = new Vue({
        el: '#app',
        data() {
            return {

                cards: [],
                archiveCards: [],
                activities: [],
                archiveActivities: [],
                isShowTmeMore: false,
                isShowTmeArchiveMore: false,
                reverse: false,
                traderCustomerPortraitDto: {},
                categoryList: [{
                    label: '基本信息',
                    ref: 'jbxx'
                },{
                    label: '联系人',
                    ref: 'lxr'
                },{
                    label: '联系地址',
                    ref: 'lxdz'
                },{
                    label: '关联公司',
                    ref: 'glgs'
                },{
                    label: '决策标签',
                    ref: 'jcbq'
                },{
                    label: '交易类标签',
                    ref: 'jylbq'
                },{
                    label: '沟通记录',
                    ref: 'gtjl'
                },
                    {
                        label: '客户档案&用户行为',
                        ref: 'yhfl'
                    }],
                traderTimeValue: "seven",
                traderTimeCategory: "sevenCategory",
                traderTimeBrand: "sevenBrand",
                traderTimeOptions:[
                    {
                        label: '近7天',
                        value: "seven"
                    },
                    {
                        label: '近30天',
                        value: "thirty"
                    },
                    {
                        label: '近60天',
                        value: "sixty"
                    },
                    {
                        label: '近90天',
                        value: "ninety"
                    },
                    {
                        label: '近180天',
                        value: "halfYear"
                    },
                    {
                        label: '近365天',
                        value: "oneYear"
                    },
                    {
                        label: '近730天',
                        value: "twoYear"
                    }
                ],
                categoryTagList: [],
                brandTagList: [],
                specialCategoryTagList: [],
                // 根据客户类型 和 客户性质分类。1： 科研终端；2：科研分销；3：临床终端；4：临床分销
                customerClass: 0,
                customerTypeSpan: 2,
                timeLinePage: 1,
                timeLineArchivePage: 1,
                activeName: 'first',
                dateRange: [],
                archiveCursor:'',
                oldCategory:0,
                oldStartTime:null,
                oldEndTime:null,
                activeArchiveCard:null
            }
        },
        computed: {
        	  startTime() {
     		    if (this.dateRange && this.dateRange.length > 0) {
     		      return this.dateRange[0];
     		    } else {
     		      return null; // 或者设置成默认值
     		    }
     		  },
     		  endTime() {
     		    if (this.dateRange && this.dateRange.length > 1) {
     		      return this.dateRange[1];
     		    } else {
     		      return null; // 或者设置成默认值
     		    }
     		  }
          },
        mounted() {
            loadingApp()
        },

        created() {
            getTraderCustomerAction({"traderId": traderId}).then(res => {
                this.traderCustomerPortraitDto = res.data.data;
                this.categoryTagList = this.traderCustomerPortraitDto.traderTimeTagsMap[this.traderTimeCategory];
                this.brandTagList = this.traderCustomerPortraitDto.traderTimeTagsMap[this.traderTimeBrand];
                this.specialCategoryTagList = this.traderCustomerPortraitDto.traderTimeTagsMap["specialCategory"];
            });
            getCustomerBehaviorTrace({"traderId": traderId}).then(res=> {

                debugger
                if(Object.keys(res.data.data).length !== 0) {
                    var webAccountListByParam = res.data.data["webAccountList"];
                    this.activities = res.data.data["behaviorTrace"].list;
                    this.cards = webAccountListByParam.map(item => {
                        return {...item, selected: false};
                    });
                    this.cards = webAccountListByParam.map((item, index) => {
                        if(index===0){
                            this.onCardSelected(item);
                        }
                        return { ...item, selected: index === 0 }; // 将第一个卡片设置为选中状态
                    });
                }
            });
            
          	//客户档案，初始化
            getCustomerArchiveTrace({"traderId": traderId,"archiveCursor":this.archiveCursor}).then(res=> {
                if(Object.keys(res.data.data).length !== 0) {
    	            var archiveCategoryList = res.data.data["archiveCategoryList"];
    	            this.archiveActivities = res.data.data["traderArchivedResDto"].list;
    	            
    	          	//记录历史查询条件
    	            this.oldCategory = 0,
                    this.oldStartTime = null,
                    this.oldEndTime = null
    	            
    	            this.archiveCards = archiveCategoryList.map(item => {
    	                return {...item, selected: false};
    	            });
    	            this.archiveCards = archiveCategoryList.map((item, index) => {
    	                if(index===0){
    	                this.onAchiveCardSelected(item);
    	            	}
    	            	return { ...item, selected: index === 0 }; // 将第一个卡片设置为选中状态
                	});
            	}
        	});


        },

        methods: {
        	handleClick(tab, event) {
                
            },
            handleDateChange(value) {
       	        
       	    },
       	 	queryArchive(){
            	this.onAchiveCardSelected(vm.activeArchiveCard);
            },
            handleChange(value) {
                traderTimeCategory = value + 'Category';
                traderTimeBrand = value + 'Brand';
                this.categoryTagList = this.traderCustomerPortraitDto.traderTimeTagsMap[traderTimeCategory];
                this.brandTagList = this.traderCustomerPortraitDto.traderTimeTagsMap[traderTimeBrand];
            },

            toggleCardSelection(selectedCard) {
                this.cards.forEach(card => {
                    if (card !== selectedCard) {
                        card.selected = false; // 将其他卡片置为非选中状态
                    }
                });
                selectedCard.selected = !selectedCard.selected;
                if (selectedCard.selected) {
                    this.timeLinePage = 1;
                    this.onCardSelected(selectedCard);
                }
            },

            onCardSelected(card) {
                // 在卡片选中时触发的操作
                //发送aiax请求给后台接口，参数是card的mobile,获取后更换 this.activities的值
                if(card){
                    this.timeLineCard = card;
                }

                $.ajax({
                    url: page_url+"/traderCustomerAction/get/behaviorByMobile.do",
                    type: "POST",
                    data: {
                        mobile: this.timeLineCard.mobile,
                        pageNo: this.timeLinePage || 1
                    },
                    dataType: "json",
                    success: function (data) {
                        if(vm.timeLinePage != 1){
                            var currentDate = vm.lastDate;
                            data.data.list.forEach(timeLineObj => {
                                if (timeLineObj.userActionTraceDay == currentDate && currentDate !="") {
                                    timeLineObj.userActionTraceTime = currentDate==''?(timeLineObj.userActionTraceTime =timeLineObj.userActionTraceTime) :(timeLineObj.userActionTraceTime.substring(11,20));//把日期开头进行一次处理。

                                }else{
                                    currentDate = timeLineObj.userActionTraceDay;
                                }
                            });
                            vm.lastDate = currentDate;
                            vm.activities = vm.activities.concat(data.data.list);
                        }else {
                            var currentDate = "";
                            data.data.list.forEach(timeLineObj => {
                                if (timeLineObj.userActionTraceDay == currentDate && currentDate !="") {
                                    timeLineObj.userActionTraceTime = currentDate==''?(timeLineObj.userActionTraceTime =timeLineObj.userActionTraceTime) :(timeLineObj.userActionTraceTime.substring(11,20));//把日期开头进行一次处理。
                                }else{
                                    currentDate = timeLineObj.userActionTraceDay;
                                }
                            });
                            vm.activities = data.data.list;
                            vm.lastDate = currentDate;
                        }

                        if(data.data.totalPage  > vm.timeLinePage){
                            vm.isShowTmeMore = true;
                        }else {
                            vm.isShowTmeMore = false;
                        }
                    }
                });
            },

            getTimeLineMore(){
                this.timeLinePage++;
                this.onCardSelected();
            },
            
          	//客户档案卡片切换
            toggleArchiveCardSelection(selectedCard) {
            	vm.activeArchiveCard = selectedCard;
            	if(selectedCard.selected == true){
            		return;
            	}
                this.archiveCards.forEach(card => {
                    if (card !== selectedCard) {
                    card.selected = false; // 将其他卡片置为非选中状态
                    }
                });
                selectedCard.selected = !selectedCard.selected;
                if (selectedCard.selected) {
                    this.timeLineArchivePage = 1;
                    this.onAchiveCardSelected(selectedCard);
                    vm.activeArchiveCard = selectedCard;
                }
            },
            
            //客户档案卡片点击查询
            onAchiveCardSelected(archiveCard){
            	// 在卡片选中时触发的操作
                //发送aiax请求给后台接口，参数是card的categoryId,获取后更换 this.archiveActivities的值
                if(archiveCard){
                    this.timeLineArchiveCard = archiveCard;
                }

              	//判断查询条件是否变更,如果变更,archiveCursor 清空,并且更新old条件为最新
            	if(this.oldCategory != this.timeLineArchiveCard.categoryId || this.oldStartTime != this.startTime  || this.oldEndTime != this.endTime){
            		vm.archiveCursor = '';
            		this.oldCategory = this.timeLineArchiveCard.categoryId;
            		this.oldStartTime = this.startTime;
            		this.oldEndTime = this.endTime;
            		vm.archiveActivities = [];
            		vm.timeLineArchivePage = 1;
            	}
            	
                $.ajax({
                    url: page_url+"/traderCustomerAction/get/archiveByTraderId.do",
                    type: "POST",
                    data: {
                    	traderId:traderId,
                        categoryId: this.timeLineArchiveCard.categoryId,
                        pageNo: this.timeLinePage || 1,
                        startTime:this.startTime,
                        endTime:this.endTime,
                        archiveCursor:vm.archiveCursor
                    },
                    dataType: "json",
                    success: function (data) {
                    	debugger;
                        if(vm.timeLineArchivePage != 1){
                            var currentDate = vm.archiveLastDate;
                            data.data.list.forEach(timeLineObj => {
	                           	var eventDay = timeLineObj.eventTime.substring(0,10);
	                            if (eventDay == currentDate && currentDate !="") {
	                               	timeLineObj.eventTime = currentDate==''?(timeLineObj.eventTime = timeLineObj.eventTime) :(timeLineObj.eventTime.substring(11,20));//把日期开头进行一次处理。
	                            }else{
	                                currentDate = eventDay;
	                            }
                        });
                            vm.archiveLastDate = currentDate;
                            vm.archiveActivities = vm.archiveActivities.concat(data.data.list);
                        }else {
                            var currentDate = "";
                            data.data.list.forEach(timeLineObj => {
                            	var eventDay = timeLineObj.eventTime.substring(0,10);
                                if (eventDay == currentDate && currentDate !="") {
                                	timeLineObj.eventTime = currentDate==''?(timeLineObj.eventTime =timeLineObj.eventTime) :(timeLineObj.eventTime.substring(11,20));//把日期开头进行一次处理。
	                            }else{
	                                currentDate = eventDay;
	                            }
                        });
                            vm.archiveActivities = data.data.list;
                            vm.archiveLastDate = currentDate;
                        }
                        vm.archiveCursor = data.data.cursor;
                        if(vm.archiveActivities!=null && vm.archiveCursor!=null){
                            vm.isShowTmeArchiveMore = true;
                        }else {
                            vm.isShowTmeArchiveMore = false;
                        }
                    }
                });
            },
            
            getArchiveTimeLineMore(){
                this.timeLineArchivePage++;
                this.onAchiveCardSelected();
            },

        }
    })
</script>

<style>
    .el-descriptions__title {
        margin-left: 15px;
    }

    .el-descriptions__header {
        margin-bottom: 9px;
        padding-top: 9px;
    }

    .el-descriptions {
        background-color: #c5ddfb;
    }

    .el-descriptions-item__cell.el-descriptions-item__content {
        text-align: center !important;
    }

    .el-descriptions-item__cell.el-descriptions-item__label.is-bordered-label {
        text-align: center;
        width: 10%;
    }

    .el-input--mini .el-input__icon {
        line-height: 0px;
    }

    .el-card__header {
        height: 34px;
        background-color: #c5ddfb;
        box-sizing: border-box;
        padding-left: 15px;
        font-weight: 700;
        padding-top: 9px;
        padding-bottom: 30px;
        color: #303133;
    }

    .el-card__body {
        padding: 0;
    }

    .el-dialog__header {
        background-color: #f3f3f3;
        border-bottom: 1px solid #ddd;
        border-radius: 2px 2px 0 0;
        height: 35px;
        padding: 7px 15px 0;
    }

    .el-dialog__title {
        font-size: 14px;
        margin-left: 0;
    }

    .el-dialog__headerbtn {
        margin-top: -5px;
    }

    .el-dialog__body {
        padding: 20px 20px;
    }

    .my-content {
        width: 40%;
    }

    .category-wrap {
        position: fixed;
        width: 150px;
        background: #fff;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        z-index: 11;
        border: 1px solid #ced3d9;
        padding: 0px 0;
    }

    .category-wrap .category-item {
        padding: 0 20px;
        line-height: 45px;
        height: 45px;
        cursor: pointer;
    }
    .category-wrap .category-item:hover {
        background: #eee;
    }

    .nodetext {
        font-size: 12px ;
        font-family: SimSun;
        fill:#000000;
    }

    .linetext {
        font-size: 12px ;
        font-family: SimSun;
        fill:#1f77b4;
        fill-opacity:1.0;
    }

    .circleImg {
        stroke-width: 1.5px;
    }

    .card {
        width: 200px;
        height: 100px;
        background-color: #a6a6a6;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        margin: 10px;
        user-select: none;
    }

    .selected {
        background-color: #1e80ff;
        color: white;
    }

    .el-timeline-item__node {
        position: absolute;
        background-color: #1e80ff;
        border-radius: 50%;
        display: flex;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
    }
</style>