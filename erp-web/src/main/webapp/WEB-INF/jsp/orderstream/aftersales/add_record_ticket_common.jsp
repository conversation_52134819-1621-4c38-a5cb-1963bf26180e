<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<div class="parts">
    <table class="table">
        <thead>
        <tr>
            <th>发票号</th>
            <th>发票代码</th>
            <th>发票金额</th>
            <th>票种</th>
            <th>红蓝字</th>
            <th>录票人员</th>
            <th>申请日期</th>
            <th>审核日期</th>
            <th>审核人</th>
        </tr>
        </thead>
        <tbody>
        <c:if test="${not empty afterSalesVo.afterSalesInvoiceVoList}">
            <c:forEach items="${afterSalesVo.afterSalesInvoiceVoList}" var="aiil">
                <tr>
                    <td>${aiil.invoiceNo}</td>
                    <td>${aiil.invoiceCode}</td>
                    <td>${aiil.amount}</td>
                    <td>${aiil.invoiceTypeName}</td>
                    <td>
                        <c:choose>
                            <c:when test="${aiil.colorType eq 1}">
                                <c:choose>
                                    <c:when test="${aiil.isEnable eq 0}">
                                        <span style="color: red">红字作废</span>
                                    </c:when>
                                    <c:otherwise>
                                        <span style="color: red">红字有效</span>
                                    </c:otherwise>
                                </c:choose>
                            </c:when>
                            <c:otherwise>
                                <c:choose>
                                    <c:when test="${aiil.isEnable eq 0}">
                                        <span style="color: red">蓝字作废</span>
                                    </c:when>
                                    <c:otherwise>
                                        蓝字有效
                                    </c:otherwise>
                                </c:choose>
                            </c:otherwise>
                        </c:choose>
                    </td>
                    <td>${aiil.creatorName}</td>
                    <td><date:date value="${aiil.addTime}" format="yyyy.MM.dd"/></td>
                    <td><date:date value="${aiil.validTime}" format="yyyy.MM.dd"/></td>
                    <td>${aiil.validUsername}</td>
                </tr>
            </c:forEach>
        </c:if>
        <c:if test="${empty afterSalesVo.afterSalesInvoiceVoList}">
            <!-- 查询无结果弹出 -->
            <tr>
                <td colspan='9'>暂无记录！</td>
            </tr>
        </c:if>
        </tbody>
    </table>
</div>