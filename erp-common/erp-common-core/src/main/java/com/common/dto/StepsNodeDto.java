package com.common.dto;

import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description 进度条节点
 * @date 2022/10/17 13:42
 **/
@Builder
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class StepsNodeDto {

    /**
     * 步骤条名
     */
    private String title;

    /**
     * 步骤条状态 wait / process / finish / error / success
     */
    private String type;

    /**
     * 其他信息 一个一行
     */
    private List<String> descriptions;
}
