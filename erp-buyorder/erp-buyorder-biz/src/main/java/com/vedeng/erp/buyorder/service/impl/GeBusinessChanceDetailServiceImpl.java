package com.vedeng.erp.buyorder.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.erp.buyorder.domain.entity.GeActionLog;
import com.vedeng.erp.buyorder.domain.entity.GeBusinessChance;
import com.vedeng.erp.buyorder.domain.entity.GeBusinessChanceDetail;
import com.vedeng.erp.buyorder.domain.entity.GeBusinessChanceDetailRecord;
import com.vedeng.erp.buyorder.dto.GeBusinessChanceDetailDto;
import com.vedeng.erp.buyorder.mapper.GeActionLogMapper;
import com.vedeng.erp.buyorder.mapper.GeBusinessChanceDetailMapper;
import com.vedeng.erp.buyorder.mapper.GeBusinessChanceDetailRecordMapper;
import com.vedeng.erp.buyorder.mapper.GeBusinessChanceMapper;
import com.vedeng.erp.buyorder.service.GeBusinessChanceDetailService;
import com.vedeng.system.dao.SysOptionDefinitionMapper;
import com.vedeng.system.model.SysOptionDefinition;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.method.P;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

@Service
public class GeBusinessChanceDetailServiceImpl implements GeBusinessChanceDetailService {
    @Resource
    private GeBusinessChanceDetailMapper geBusinessChanceDetailMapper;

    @Resource
    private SysOptionDefinitionMapper sysOptionDefinitionMapper;

    @Resource
    private GeBusinessChanceDetailRecordMapper geBusinessChanceDetailRecordMapper;

    @Resource
    private GeActionLogMapper geActionLogMapper;

    @Resource
    private GeBusinessChanceMapper geBusinessChanceMapper;

    @Override
    public GeBusinessChanceDetailDto queryByGeBusinessChanceId(Integer geBusinessChanceId) {
        //查询GE商机明细信息
        GeBusinessChanceDetail geBusinessChanceDetail = geBusinessChanceDetailMapper.queryByGeBussinessChanceId(geBusinessChanceId);
        SysOptionDefinition sysOptionDefinition = new SysOptionDefinition();
        GeBusinessChanceDetailDto geBusinessChanceDetailDto = new GeBusinessChanceDetailDto();
        if (geBusinessChanceDetail != null) {
            //医院规模字典
            BeanUtils.copyProperties(geBusinessChanceDetail, geBusinessChanceDetailDto);
            if (!ErpConst.ZERO.equals(geBusinessChanceDetail.getHospitalSize())) {
                sysOptionDefinition = sysOptionDefinitionMapper.selectByPrimaryKey(geBusinessChanceDetail.getHospitalSize());
                geBusinessChanceDetailDto.setHospitalSizeName(sysOptionDefinition.getTitle());
            }
            //采购形式字典
            if (!ErpConst.ZERO.equals(geBusinessChanceDetail.getBuyType())) {
                sysOptionDefinition = sysOptionDefinitionMapper.selectByPrimaryKey(geBusinessChanceDetail.getBuyType());
                geBusinessChanceDetailDto.setBuyTypeName(sysOptionDefinition.getTitle());
            }
            //采购形式字典
            if (!ErpConst.ZERO.equals(geBusinessChanceDetail.getProjectPhase())) {
                sysOptionDefinition = sysOptionDefinitionMapper.selectByPrimaryKey(geBusinessChanceDetail.getProjectPhase());
                geBusinessChanceDetailDto.setProjectPhaseName(sysOptionDefinition.getTitle());
            }
            return geBusinessChanceDetailDto;
        }else {
            return null;
        }
    }

    @Override
    @Transactional
    public GeBusinessChanceDetail saveEditGeBusinessChance(GeBusinessChanceDetail geBusinessChanceDetail, User user) {
        //获取ge商机主档信息
        GeBusinessChance geBusinessChance = geBusinessChanceMapper.selectByPrimaryKey(geBusinessChanceDetail.getGeBussinessChanceId());
        //获取原维护信息
        GeBusinessChanceDetail formal = geBusinessChanceDetailMapper.queryByGeBussinessChanceId(geBusinessChanceDetail.getGeBussinessChanceId());
        //获取原维护信息记录
        GeBusinessChanceDetailRecord formalRecord = geBusinessChanceDetailRecordMapper.queryByGeBussinessChanceDetailId(formal.getGeBussinessChanceDetailId());
        //保存最新的维护信息
        geBusinessChanceDetail.setModTime(new Date());
        geBusinessChanceDetail.setUpdater(user.getUserId());
        geBusinessChanceDetail.setUpdaterName(user.getRealName());
        geBusinessChanceDetail.setGeBussinessChanceDetailId(formal.getGeBussinessChanceDetailId());
        geBusinessChanceDetail.setIsDelete(ErpConst.ZERO);
        geBusinessChanceDetailMapper.updateByPrimaryKey(geBusinessChanceDetail);
        //保存最新的维护信息记录
        GeBusinessChanceDetailRecord uptRecorder = new GeBusinessChanceDetailRecord();
        BeanUtils.copyProperties(formal,uptRecorder);
        uptRecorder.setUpdater(user.getUserId());
        uptRecorder.setModTime(new Date());
        uptRecorder.setUpdaterName(user.getRealName());
        if(formalRecord != null){
            uptRecorder.setGeBussinessChanceDetailRecordId(formalRecord.getGeBussinessChanceDetailRecordId());
            uptRecorder.setIsDelete(ErpConst.ZERO);
            geBusinessChanceDetailRecordMapper.updateByPrimaryKey(uptRecorder);
        }else {
            geBusinessChanceDetailRecordMapper.insertSelective(uptRecorder);
        }
        //保存日志信息
        GeActionLog geActionLog = new GeActionLog();
        geActionLog.setRelatedType(ErpConst.TYPE_1);
        geActionLog.setRelatedId(geBusinessChanceDetail.getGeBussinessChanceId());
        geActionLog.setOperation("维护商机");
        String content = judgePart(geBusinessChanceDetail,uptRecorder,geBusinessChance);
        //更新商机主表的是否维护字段信息
        geBusinessChanceMapper.updateIsDefend(geBusinessChance);
        geActionLog.setContent(content);
        geActionLog.setCreator(user.getUserId());
        geActionLog.setCreatorName(user.getRealName());
        geActionLog.setAddTime(new Date());
        geActionLog.setRelatedBody(JSON.toJSONString(geBusinessChanceDetail));
        geActionLogMapper.insertSelective(geActionLog);
        return geBusinessChanceDetail;
    }

    /**
     * <AUTHOR> 
     * @desc 判断模块是否改动
     * @param geBusinessChanceDetail
     * @param geBusinessChanceDetailRecord
     * @param geBusinessChance
     * @return
     */
    private String judgePart(GeBusinessChanceDetail geBusinessChanceDetail,GeBusinessChanceDetailRecord geBusinessChanceDetailRecord,GeBusinessChance geBusinessChance){
        StringBuffer content = new StringBuffer("");
        if(!geBusinessChanceDetail.getBusinessChanceStatus().equals(geBusinessChanceDetailRecord.getBusinessChanceStatus())){
            //基础信息模块修改校验
            content.append("基础信息");
        }
        //判断商机是否维护
        boolean isBusinessChanceDefend = true;
        //终端信息
        boolean isTerminalChange = false;
        if(geBusinessChanceDetail.getSalesAmount() == null){
            if(geBusinessChanceDetailRecord.getSalesAmount() != null){
                isTerminalChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(geBusinessChanceDetailRecord.getSalesAmount() == null){
                isTerminalChange = true;
            }else if(geBusinessChanceDetail.getSalesAmount().compareTo(geBusinessChanceDetailRecord.getSalesAmount()) != 0){
                isTerminalChange = true;
            }
        }
        if(ErpConst.ZERO.equals(geBusinessChanceDetail.getHospitalSize())){
            if(!ErpConst.ZERO.equals(geBusinessChanceDetailRecord.getHospitalSize())){
                isTerminalChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(!geBusinessChanceDetail.getHospitalSize().equals(geBusinessChanceDetailRecord.getHospitalSize())){
                isTerminalChange = true;
            }
        }
        if(geBusinessChanceDetail.getBedNum() == null){
            if(geBusinessChanceDetailRecord.getBedNum() != null){
                isTerminalChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(!geBusinessChanceDetail.getBedNum().equals(geBusinessChanceDetailRecord.getBedNum())){
                isTerminalChange = true;
            }
        }
        if(geBusinessChanceDetail.getYearInSum() == null){
            if(geBusinessChanceDetailRecord.getYearInSum() != null){
                isTerminalChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(geBusinessChanceDetailRecord.getYearInSum() == null){
                isTerminalChange = true;
            }else if(geBusinessChanceDetail.getYearInSum().compareTo(geBusinessChanceDetailRecord.getYearInSum()) != 0){
                isTerminalChange = true;
            }
        }
        if(geBusinessChanceDetail.getCtDailyNum() == null){
            if(geBusinessChanceDetailRecord.getCtDailyNum() != null){
                isTerminalChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(!geBusinessChanceDetail.getCtDailyNum().equals(geBusinessChanceDetailRecord.getCtDailyNum())){
                isTerminalChange = true;
            }
        }
        if(geBusinessChanceDetail.getIsNewHospital() == null){
            if(geBusinessChanceDetailRecord.getIsNewHospital() != null){
                isTerminalChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(!geBusinessChanceDetail.getIsNewHospital().equals(geBusinessChanceDetailRecord.getIsNewHospital())){
                isTerminalChange = true;
            }
        }
        if(ErpConst.ZERO.equals(geBusinessChanceDetail.getNewHospitalPercent())){
            if(!ErpConst.ZERO.equals(geBusinessChanceDetailRecord.getNewHospitalPercent())){
                isTerminalChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(!geBusinessChanceDetail.getNewHospitalPercent().equals(geBusinessChanceDetailRecord.getNewHospitalPercent())){
                isTerminalChange = true;
            }
        }
        if(geBusinessChanceDetail.getExpectBuyTime() == null){
            if(geBusinessChanceDetailRecord.getExpectBuyTime() != null){
                isTerminalChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(!geBusinessChanceDetail.getExpectBuyTime().equals(geBusinessChanceDetailRecord.getExpectBuyTime())){
                isTerminalChange = true;
            }
        }
        //竞品信息
        boolean isCompeteChange = false;
        if(geBusinessChanceDetail.getCompeteName() == null || "".equals(geBusinessChanceDetail.getCompeteName())){
            if(geBusinessChanceDetailRecord.getCompeteName() != null && !"".equals(geBusinessChanceDetailRecord.getCompeteName())){
                isCompeteChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(!geBusinessChanceDetail.getCompeteName().equals(geBusinessChanceDetailRecord.getCompeteName())){
                isCompeteChange = true;
            }
        }
        if(geBusinessChanceDetail.getCompeteSkuName() == null || "".equals(geBusinessChanceDetail.getCompeteSkuName())){
            if(geBusinessChanceDetailRecord.getCompeteSkuName() != null && !"".equals(geBusinessChanceDetailRecord.getCompeteSkuName())){
                isCompeteChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(!geBusinessChanceDetail.getCompeteSkuName().equals(geBusinessChanceDetailRecord.getCompeteSkuName())){
                isCompeteChange = true;
            }
        }
        if(geBusinessChanceDetail.getCompeteSkuPrice() == null){
            if(geBusinessChanceDetailRecord.getCompeteSkuPrice() != null){
                isCompeteChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(geBusinessChanceDetailRecord.getCompeteSkuPrice() == null){
                isCompeteChange = true;
            }else if(geBusinessChanceDetail.getCompeteSkuPrice().compareTo(geBusinessChanceDetailRecord.getCompeteSkuPrice())!= 0){
                isCompeteChange = true;
            }
        }
        //原有装机信息
        boolean isInstallChange = false;
        if(geBusinessChanceDetail.getIsNewInstall() == null ){
            if(geBusinessChanceDetailRecord.getIsNewInstall() != null){
                isInstallChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(!geBusinessChanceDetail.getIsNewInstall().equals(geBusinessChanceDetailRecord.getIsNewInstall())){
                isInstallChange = true;
            }
        }
        if(geBusinessChanceDetail.getOriginSkuBand() == null || "".equals(geBusinessChanceDetail.getOriginSkuBand())){
            if(geBusinessChanceDetailRecord.getOriginSkuBand() != null && !"".equals(geBusinessChanceDetailRecord.getOriginSkuBand())){
                isInstallChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(!geBusinessChanceDetail.getOriginSkuBand().equals(geBusinessChanceDetailRecord.getOriginSkuBand())){
                isInstallChange = true;
            }
        }
        if(geBusinessChanceDetail.getOriginSkuModel() == null || "".equals(geBusinessChanceDetail.getOriginSkuModel())){
            if(geBusinessChanceDetailRecord.getOriginSkuModel() != null && !"".equals(geBusinessChanceDetailRecord.getOriginSkuModel())){
                isInstallChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(!geBusinessChanceDetail.getOriginSkuModel().equals(geBusinessChanceDetailRecord.getOriginSkuModel())){
                isInstallChange = true;
            }
        }
        if(geBusinessChanceDetail.getInstallTime() == null){
            if(geBusinessChanceDetailRecord.getInstallTime() != null){
                isInstallChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(!geBusinessChanceDetail.getInstallTime().equals(geBusinessChanceDetailRecord.getInstallTime())){
                isInstallChange = true;
            }
        }
        if(geBusinessChanceDetail.getSaleCompanyName() == null || "".equals(geBusinessChanceDetail.getSaleCompanyName())){
            if(geBusinessChanceDetailRecord.getSaleCompanyName() != null && !"".equals(geBusinessChanceDetailRecord.getSaleCompanyName())){
                isInstallChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(!geBusinessChanceDetail.getSaleCompanyName().equals(geBusinessChanceDetailRecord.getSaleCompanyName())){
                isInstallChange = true;
            }
        }
        //商机详细情况
        boolean isChanceDetailChange = false;
        if(geBusinessChanceDetail.getWinOrderPercent() == null){
            if(geBusinessChanceDetailRecord.getWinOrderPercent() != null){
                isChanceDetailChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(!geBusinessChanceDetail.getWinOrderPercent().equals(geBusinessChanceDetailRecord.getWinOrderPercent())){
                isChanceDetailChange = true;
            }
        }
        if(geBusinessChanceDetail.getMoneySource() == null || "".equals(geBusinessChanceDetail.getMoneySource())){
            if(geBusinessChanceDetailRecord.getMoneySource() != null && !"".equals(geBusinessChanceDetailRecord.getMoneySource())){
                isChanceDetailChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(!geBusinessChanceDetail.getMoneySource().equals(geBusinessChanceDetailRecord.getMoneySource())){
                isChanceDetailChange = true;
            }
        }
        if(geBusinessChanceDetail.getMoneySituation() == null || "".equals(geBusinessChanceDetail.getMoneySituation())){
            if(geBusinessChanceDetailRecord.getMoneySituation() != null && !"".equals(geBusinessChanceDetailRecord.getMoneySituation())){
                isChanceDetailChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(!geBusinessChanceDetail.getMoneySituation().equals(geBusinessChanceDetailRecord.getMoneySituation())){
                isChanceDetailChange = true;
            }
        }
        if(geBusinessChanceDetail.getPrepareAmount() == null){
            if(geBusinessChanceDetailRecord.getPrepareAmount() != null){
                isChanceDetailChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(geBusinessChanceDetailRecord.getPrepareAmount() == null){
                isChanceDetailChange = true;
            }else if(geBusinessChanceDetail.getPrepareAmount().compareTo(geBusinessChanceDetailRecord.getPrepareAmount()) != 0){
                isChanceDetailChange = true;
            }
        }
        if(ErpConst.ZERO.equals(geBusinessChanceDetail.getBuyType())){
            if(!ErpConst.ZERO.equals(geBusinessChanceDetailRecord.getBuyType())){
                isChanceDetailChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(!geBusinessChanceDetail.getBuyType().equals(geBusinessChanceDetailRecord.getBuyType())){
                isChanceDetailChange = true;
            }
        }
        if(geBusinessChanceDetail.getIsApplyInspect() == null){
            if(geBusinessChanceDetailRecord.getIsApplyInspect() != null){
                isChanceDetailChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(!geBusinessChanceDetail.getIsApplyInspect().equals(geBusinessChanceDetailRecord.getIsApplyInspect())){
                isChanceDetailChange = true;
            }
        }
        if(geBusinessChanceDetail.getIsEngineComplete() == null){
            if(geBusinessChanceDetailRecord.getIsEngineComplete() != null){
                isChanceDetailChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(!geBusinessChanceDetail.getIsEngineComplete().equals(geBusinessChanceDetailRecord.getIsEngineComplete())){
                isChanceDetailChange = true;
            }
        }
        if(geBusinessChanceDetail.getQuoteMethod() == null || "".equals(geBusinessChanceDetail.getQuoteMethod())){
            if(geBusinessChanceDetailRecord.getQuoteMethod() != null && !"".equals(geBusinessChanceDetailRecord.getQuoteMethod())){
                isChanceDetailChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(!geBusinessChanceDetail.getQuoteMethod().equals(geBusinessChanceDetailRecord.getQuoteMethod())){
                isChanceDetailChange = true;
            }
        }
        if(geBusinessChanceDetail.getQuoteMethodPrice() == null){
            if(geBusinessChanceDetailRecord.getQuoteMethodPrice() != null){
                isChanceDetailChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(geBusinessChanceDetailRecord.getQuoteMethodPrice() == null){
                isChanceDetailChange = true;
            }else if(geBusinessChanceDetail.getQuoteMethodPrice().compareTo(geBusinessChanceDetailRecord.getQuoteMethodPrice()) != 0){
                isChanceDetailChange = true;
            }
        }

        //关键问题解决
        boolean isKeyProblemChange = false;
        if(ErpConst.ZERO.equals(geBusinessChanceDetail.getProjectPhase())){
            if(!ErpConst.ZERO.equals(geBusinessChanceDetailRecord.getProjectPhase())){
                isKeyProblemChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(!geBusinessChanceDetail.getProjectPhase().equals(geBusinessChanceDetailRecord.getProjectPhase())){
                isKeyProblemChange = true;
            }
        }
        if(geBusinessChanceDetail.getNeedComplete() == null || "".equals(geBusinessChanceDetail.getNeedComplete())){
            if(geBusinessChanceDetailRecord.getNeedComplete() != null && !"".equals(geBusinessChanceDetailRecord.getNeedComplete())){
                isKeyProblemChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(!geBusinessChanceDetail.getNeedComplete().equals(geBusinessChanceDetailRecord.getNeedComplete())){
                isKeyProblemChange = true;
            }
        }
        if(geBusinessChanceDetail.getNeedTime() == null){
            if(geBusinessChanceDetailRecord.getNeedTime() != null){
                isKeyProblemChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(!geBusinessChanceDetail.getNeedTime().equals(geBusinessChanceDetailRecord.getNeedTime())){
                isKeyProblemChange = true;
            }
        }
        if(geBusinessChanceDetail.getAssistDepartment() == null || "".equals(geBusinessChanceDetail.getAssistDepartment())){
            if(geBusinessChanceDetailRecord.getAssistDepartment() != null && !"".equals(geBusinessChanceDetailRecord.getAssistDepartment())){
                isKeyProblemChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(!geBusinessChanceDetail.getAssistDepartment().equals(geBusinessChanceDetailRecord.getAssistDepartment())){
                isKeyProblemChange = true;
            }
        }
        if(geBusinessChanceDetail.getProjectEvolve() == null || "".equals(geBusinessChanceDetail.getProjectEvolve())){
            if(geBusinessChanceDetailRecord.getProjectEvolve() != null && !"".equals(geBusinessChanceDetailRecord.getProjectEvolve())){
                isKeyProblemChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(!geBusinessChanceDetail.getProjectEvolve().equals(geBusinessChanceDetailRecord.getProjectEvolve())){
                isKeyProblemChange = true;
            }
        }
        if(geBusinessChanceDetail.getEvolveTime() == null){
            if(geBusinessChanceDetailRecord.getEvolveTime() != null){
                isKeyProblemChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(!geBusinessChanceDetail.getEvolveTime().equals(geBusinessChanceDetailRecord.getEvolveTime())){
                isKeyProblemChange = true;
            }
        }
        if(geBusinessChanceDetail.getNextPlan() == null || "".equals(geBusinessChanceDetail.getNextPlan())){
            if(geBusinessChanceDetailRecord.getNextPlan() != null && !"".equals(geBusinessChanceDetailRecord.getNextPlan())){
                isKeyProblemChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(!geBusinessChanceDetail.getNextPlan().equals(geBusinessChanceDetailRecord.getNextPlan())){
                isKeyProblemChange = true;
            }
        }
        if(geBusinessChanceDetail.getNeedSupport() == null || "".equals(geBusinessChanceDetail.getNeedSupport())){
            if(geBusinessChanceDetailRecord.getNeedSupport() != null && !"".equals(geBusinessChanceDetailRecord.getNeedSupport())){
                isKeyProblemChange = true;
            }
        }else {
            isBusinessChanceDefend = false;
            if(!geBusinessChanceDetail.getNeedSupport().equals(geBusinessChanceDetailRecord.getNeedSupport())){
                isKeyProblemChange = true;
            }
        }
        if(isTerminalChange){
            content = setPartContent(content,"终端信息");
        }
        if(isCompeteChange){
            content = setPartContent(content,"竞品信息");
        }
        if(isInstallChange){
            content = setPartContent(content,"原有安装信息");
        }
        if(isChanceDetailChange){
            content = setPartContent(content,"商机详细情况");
        }
        if(isKeyProblemChange){
            content = setPartContent(content,"关键问题解决");
        }
        if(isBusinessChanceDefend){
            geBusinessChance.setIsDefend(ErpConst.ZERO);
        }else {
            geBusinessChance.setIsDefend(ErpConst.ONE);
        }
        return content.toString();
    }

    /**
     * <AUTHOR>
     * @desc 设置变更内容
     * @param content
     * @param partContent
     * @return
     */
    public StringBuffer setPartContent(StringBuffer content,String partContent){
        if(!"".equals(content.toString())){
            partContent = ","+partContent;
        }
        return content.append(partContent);
    }
}
