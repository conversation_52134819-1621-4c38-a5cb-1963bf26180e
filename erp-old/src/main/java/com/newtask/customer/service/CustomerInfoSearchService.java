package com.newtask.customer.service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/30 15:06
 * @describe
 */
public interface CustomerInfoSearchService {
    /**
     *  查询需要更新的有报价记录的客户
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 客户id
     */
    List<Integer> getTraderIdByTime(Long startTime,Long endTime);

    /**
     * 更新报价信息
     * @param countToUpdate 客户Id
     */
    int updateHasQuoted(List<Integer> countToUpdate);

    /**
     * 查询合作的客户
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 客户Id
     */
    List<Integer> getTraderIdByTimeAndValidStatus(Long startTime, Long endTime);

    /**
     * 更新为合作的客户
     * @param countToUpdate 客户ID
     */
    int updateIsCooperated(List<Integer> countToUpdate);
}
