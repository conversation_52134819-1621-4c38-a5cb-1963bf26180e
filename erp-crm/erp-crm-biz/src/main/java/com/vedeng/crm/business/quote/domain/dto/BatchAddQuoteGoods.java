package com.vedeng.crm.business.quote.domain.dto;

import com.vedeng.common.core.utils.validator.group.DefaultGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 批量添加商品
 */
@Data
public class BatchAddQuoteGoods {

    /**
     * 报价单ID
     */
    @NotNull(message = "报价单ID不能为空", groups = DefaultGroup.class)
    private Integer quoteorderId;

    /**
     * 商品文本集合，例如V12345V32343
     */
    @NotBlank(message = "商品SKU不能为空", groups = DefaultGroup.class)
    private String skuNos;
}
