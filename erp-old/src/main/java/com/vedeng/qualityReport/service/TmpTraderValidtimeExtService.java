package com.vedeng.qualityReport.service;


import com.vedeng.common.model.ResultInfo;


public interface TmpTraderValidtimeExtService {
    /**
     * 插入临时表审核通过的供应商相关信息
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/12/31 .
     * @author: <PERSON><PERSON>.
     * @param
     * @return: void.
     * @throws:  .
     */

    ResultInfo saveTraderValidTime();

    void updateTraderCustomerValidTIme(Integer traderCustomerId);

    void updateTraderSupplierValidTIme(Integer traderSupplierId);
}
