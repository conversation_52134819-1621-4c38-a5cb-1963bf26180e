package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchSupplierFinanceDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.kingdee.batch.mapper
 * @Date 2022/12/5 13:17
 */
public interface BatchSupplierFinanceDtoMapper {

    /**
     * 查询审核供应商信息
     *
     * @param queryDto BatchSupplierFinanceDto
     * @return List<BatchSupplierFinanceDto>
     */
    List<BatchSupplierFinanceDto> findByAll(BatchSupplierFinanceDto queryDto);

    /**
     * 更新审计供应商金蝶推送状态
     *
     * @param traderSupplierFinanceId traderSupplierFinanceId
     */
    void updateKingDeePushStatus(@Param("traderSupplierFinanceId") Integer traderSupplierFinanceId);

    /**
     * 根据traderId查询SupplierId
     * @param traderId
     * @return
     */
    BatchSupplierFinanceDto getSupplierIdByTraderId(@Param("traderId") Integer traderId);

    /**
     * 查询供应商补偿数据
     */
    List<BatchSupplierFinanceDto> querySupplierCompensate(BatchSupplierFinanceDto queryDto);
}
