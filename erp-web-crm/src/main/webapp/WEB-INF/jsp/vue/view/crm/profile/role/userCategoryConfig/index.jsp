<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html>
<html>
<head>
    <title>商品分类人员配置</title>
    <link rel="stylesheet" href="/static/css/common/common.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/ui/ui.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/css/pages/userConfig.css?staticResourceVersion=${requestScope.staticResourceVersion}">
</head>

<body>
    <!--隐藏的登录用户信息 -->
    <jsp:include page="../../common/head_import.jsp"></jsp:include>

    <div class="page-wrap" id="page-container">
        <page-header :side="true"></page-header>
        <div class="page-container">
            <div class="page-main" v-if="!loading">
                <div class="list-title">商品分类人员配置</div>
                <div class="list-top-option">
                  <ui-button type="primary" icon="icon-add" @click="addConfig">新建配置</ui-button>
                </div>
                <ui-search-container 
                    ref="listContainer" 
                    :headers="tableHeaders" 
                    url="/crm/role/profile/role-user-category-config/page" 
                    :search-params="searchParams" 
                    :can-choose="true" 
                    :need-custom="false" 
                    :need-setting="false"
                >
                    <template v-slot:filter-list>
                        <ui-search-item label="商品分类">
                            <ui-cascader 
                                :data="filterCategoryData"
                                width="100%"
                                clearable 
                                multiple
                                filterable
                                placeholder="全部"
                                @change="handleFilterCategoryChange"
                                v-if="filterCategoryData && filterCategoryData.length"
                            ></ui-cascader>
                        </ui-search-item>
                        <ui-search-item label="业务人员" :lock="true">
                            <ui-select :remote="true" :avatar="true" placeholder="全部" multiple-type="fixed" clearable :remote-info="filterUserRemoteInfo" v-model="searchParams.businessUserIds"></ui-select>
                        </ui-search-item>
                        <ui-search-item label="在职状态">
                            <ui-select :data="userStatusList" placeholder="全部" v-model="searchParams.employmentStatus" clearable></ui-select>
                        </ui-search-item>
                        <ui-search-item label="部门">
                            <ui-cascader 
                                :data="departmentData"
                                width="100%"
                                clearable 
                                multiple
                                filterable
                                placeholder="全部"
                                @change="handleFilterDepartChange"
                                v-if="departmentData && departmentData.length"
                            ></ui-cascader>
                        </ui-search-item>
                    </template>
                    <template v-slot:list-button>
                      <ui-button @click="multiDelete">删除</ui-button>
                    </template>
                    <template v-slot:userName="{ row }">
                        <ui-user :name="row.userName" :avatar="row.userAvatar" :status="row.userStatus"></ui-user>
                    </template>
                    <template v-slot:department="{ row }">
                        <ui-department :departments="row.userDepartments"></ui-department>
                    </template>
                    <template v-slot:userPositions="{ row }">
                        {{ getPositionStr(row.userPositions) }}
                    </template>
                    <template v-slot:categoryCount="{ row }">
                        <div class="td-link" @click="editConfig(row)">{{ row.categoryCount }}</div>
                    </template>
                    <template v-slot:option="{ row }">
                        <div class="option-wrap">
                            <div class="table-edit" @click="editConfig(row)">编辑</div>
                            <div class="table-edit" @click="deleteItem(row)">删除</div>
                            <div class="table-edit" @click="copyItem(row)">复制</div>
                        </div>
                    </template>
                </ui-search-container>
            </div>
        </div>

        <ui-dialog
            :visible.sync="isShowEditDialog"
            :title="editTitle"
            width="960px"
        >
            <div class="form-wrap form-category-wrap label-width-2" v-if="isShowEditDialog">
                <ui-form-item label="业务人员" :must="true" v-if="!editId">
                  <ui-tree :data="userData" v-model="formUsers" title="选择线上销售人员" valid="AddSalerCategory_formUsers"></ui-tree>
                </ui-form-item>
                <ui-form-item label="业务人员" :must="true" :text="true" v-else>
                    <ui-user :name="editInfo.userName" :avatar="editInfo.userAvatar" :status="editInfo.userStatus"></ui-user>
                </ui-form-item>
                <ui-form-item label="商品分类" :must="true">
                  <ui-tree :data="categoryList" class-type="vertical" width="960px" :avatar="false" v-model="formCategorys" title="选择关联三级分类"  valid="AddSalerCategory_formCategorys"></ui-tree>
                </ui-form-item>
            </div>
            <template slot="footer">
                <div class="dlg-form-footer">
                    <ui-button type="primary" @click="submitCategoryConfig">提交</ui-button>
                    <ui-button @click="isShowEditDialog=false" class="close">取消</ui-button>
                </div>
            </template>
        </ui-dialog>

    </div>
    <script src="/static/js/common/vue.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/axios.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/lodash.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/moment.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/ui/ui.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/layout.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/pages/setting/userCategoryConfig.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
</body>
</html>
