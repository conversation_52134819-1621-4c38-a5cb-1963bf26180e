package com.vedeng.goods.service;

import com.vedeng.goods.model.vo.CoreOperateInfoGenerateVo;
import com.vedeng.goods.model.vo.MlSkuRequestVo;
import com.vedeng.goods.model.vo.MlSkuResponseVo;

/**
 * 马良图解装饰服务层
 * <AUTHOR>
 */
public interface MlFacadeService {

    /**
     * 获取商品马良图文详情
     *
     * @param mlSkuRequestVo
     * @return
     */
    MlSkuResponseVo getSkuGraphicDetailInfo(MlSkuRequestVo mlSkuRequestVo);

    /**
     * 新商品流马良详情处理
     *
     * @param infoGenerateVo
     */
    void mlSkuGraphicDetailHandler(CoreOperateInfoGenerateVo infoGenerateVo);
}
