package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.annotation.KingDeeID;
import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * @see <a href="https://www.yuque.com/manhuo/gf1570/yb73ow">采购入库单</a>
 * <AUTHOR>
 * @version 1.0
 * @description: 预处理金蝶采购入库单 dto  由erp实际业务转换
 * @date
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@WriteBackField(needBackField = {"FID","FInStockEntry.FEntryID", "FInStockEntry.F_QZOK_BDDJHID"})
public class KingDeePurchaseReceiptDto extends KingDeeMqBaseDto {
    /**
     * id
     */
    private Integer id;

    /**
     * 单据内码
     */
    @WriteBackField
    @KingDeeID
    private String fId;
    /**
     * 单据类型
     */
    private String fBillTypeId;
    /**
     * 单据编号
     */
    @BusinessID
    private String fBillNo;
    /**
     * 贝登单据头ID
     */
    private String fQzokBddjtId;
    /**
     * 单据日期
     */
    private String fDate;
    /**
     * 库存组织
     */
    private String fStockOrgId;
    /**
     * 供应商
     */
    private String fSupplierId;
    /**
     * fInStockEntry
     */
    private List<KingDeePurchaseReceiptDetailDto> fInStockEntry;

    /**
     * 过滤推送金蝶的时间
     */
    private Date beginTime;
    /**
     * 过滤推送金蝶的时间
     */
    private Date endTime;

    @Override
    public String getFormId() {
        return KingDeeFormConstant.STK_INSTOCK;
    }

    /**
     * @组合对象@ 是否为期初入库
     */
    private Boolean hasInitInStock = Boolean.FALSE;
}
