package com.vedeng.erp.kingdee.domain.entity;

import java.math.BigDecimal;
import java.util.Date;

/**
    * 资金流水-负向
    */
public class CapitalBillKingDeeCreate {
    private Integer capitalBillKingDeeCreateId;

    /**
    * 来源于流水ID
    */
    private Integer fromCapitalBillId;

    /**
    * 记账编号
    */
    private String capitalBillNo;

    /**
    * 交易时间
    */
    private Long traderTime;

    /**
    * 总额
    */
    private BigDecimal amount;

    /**
    * 订单类型 3售后订单
    */
    private Integer orderType;

    /**
    * 订单单号
    */
    private String orderNo;

    /**
    * 是否删除 0否 1是
    */
    private Integer isDelete;

    /**
    * 创建时间
    */
    private Date addTime;

    /**
    * 添加人ID
    */
    private Integer creator;

    /**
    * 添加人名称
    */
    private String creatorName;

    /**
    * 修改时间
    */
    private Date modTime;

    /**
    * 更新人ID
    */
    private Integer updater;

    /**
    * 更新人名称
    */
    private String updaterName;

    /**
    * 备注
    */
    private String remark;

    /**
    * 更新备注
    */
    private String updateRemark;

    public Integer getCapitalBillKingDeeCreateId() {
        return capitalBillKingDeeCreateId;
    }

    public void setCapitalBillKingDeeCreateId(Integer capitalBillKingDeeCreateId) {
        this.capitalBillKingDeeCreateId = capitalBillKingDeeCreateId;
    }

    public Integer getFromCapitalBillId() {
        return fromCapitalBillId;
    }

    public void setFromCapitalBillId(Integer fromCapitalBillId) {
        this.fromCapitalBillId = fromCapitalBillId;
    }

    public String getCapitalBillNo() {
        return capitalBillNo;
    }

    public void setCapitalBillNo(String capitalBillNo) {
        this.capitalBillNo = capitalBillNo;
    }

    public Long getTraderTime() {
        return traderTime;
    }

    public void setTraderTime(Long traderTime) {
        this.traderTime = traderTime;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public Date getModTime() {
        return modTime;
    }

    public void setModTime(Date modTime) {
        this.modTime = modTime;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    public String getUpdaterName() {
        return updaterName;
    }

    public void setUpdaterName(String updaterName) {
        this.updaterName = updaterName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getUpdateRemark() {
        return updateRemark;
    }

    public void setUpdateRemark(String updateRemark) {
        this.updateRemark = updateRemark;
    }
}