package com.vedeng.goods.dao;

import com.vedeng.goods.model.SkuAttrMappingGenerate;
import com.vedeng.goods.model.SkuAttrMappingGenerateExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SkuAttrMappingGenerateMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SKU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int countByExample(SkuAttrMappingGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SKU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int deleteByExample(SkuAttrMappingGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SKU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int deleteByPrimaryKey(Integer skuAttrId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SKU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int insert(SkuAttrMappingGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SKU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int insertSelective(SkuAttrMappingGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SKU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    List<SkuAttrMappingGenerate> selectByExample(SkuAttrMappingGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SKU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    SkuAttrMappingGenerate selectByPrimaryKey(Integer skuAttrId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SKU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByExampleSelective(@Param("record") SkuAttrMappingGenerate record, @Param("example") SkuAttrMappingGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SKU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByExample(@Param("record") SkuAttrMappingGenerate record, @Param("example") SkuAttrMappingGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SKU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByPrimaryKeySelective(SkuAttrMappingGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SKU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByPrimaryKey(SkuAttrMappingGenerate record);
}