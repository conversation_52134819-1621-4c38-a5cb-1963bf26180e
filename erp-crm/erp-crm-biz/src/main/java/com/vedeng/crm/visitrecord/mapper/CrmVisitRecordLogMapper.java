package com.vedeng.crm.visitrecord.mapper;

import com.vedeng.crm.visitrecord.domain.vo.VisitRecordLogVo;
import com.vedeng.erp.system.dto.OperationLogDto;
import org.apache.ibatis.annotations.Param;
import java.util.List;

public interface CrmVisitRecordLogMapper {

    /**
     * 插入操作日志
     */
    int insert(VisitRecordLogVo log);

    /**
     * 根据拜访记录ID分页查询操作日志
     */
    List<VisitRecordLogVo> selectPageByRecordId(@Param("recordId") Integer recordId);


    List<OperationLogDto> selectPageByVisitRecordId(@Param("recordId") Integer recordId);
}