package com.vedeng.api.standard.adapter.saleorder.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 销售通用响应DTO
 * 支持所有操作类型的响应：创建、更新、删除、查询、详情、审核等
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-27
 */
public class SaleOrderResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    // ========== 基础响应字段 ==========
    /**
     * 操作是否成功
     */
    private Boolean success;

    /**
     * 响应消息
     */
    private String message;



    // ========== 采购单基础信息 ==========
    /**
     * 采购单ID
     */
    private Integer saleorderId;

    /**
     * 采购单编号
     */
    private String saleorderNo;

    /**
     * 供应商ID
     */
    private Integer traderId;

    /**
     * 供应商名称
     */
    private String traderName;

    /**
     * 采购状态
     */
    private Integer status;

    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 总数量
     */
    private Integer totalQuantity;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 总记录数（查询时使用）
     */
    private Long total;

    /**
     * 页码（查询时使用）
     */
    private Integer pageNum;

    /**
     * 页大小（查询时使用）
     */
    private Integer pageSize;

    // ========== 详情响应字段 ==========
    /**
     * 商品列表（详情时使用）
     */
    private List<SaleOrderGoodsRequest> goodsList;

    // ========== 构造函数 ==========
    /**
     * 默认构造函数
     */
    public SaleOrderResponse() {
    }

    /**
     * 基础构造函数
     */
    public SaleOrderResponse(Boolean success, String message) {
        this.success = success;
        this.message = message;
    }
    
    // ========== 静态工厂方法 ==========
    /**
     * 创建成功响应
     */
    public static SaleOrderResponse success(String message) {
        return new SaleOrderResponse(true, message);
    }
    
    /**
     * 创建成功响应（带ID）
     */
    public static SaleOrderResponse success(Integer saleorderId, String message) {
        SaleOrderResponse response = new SaleOrderResponse(true, message);
        response.setSaleorderId(saleorderId);
        return response;
    }
    
    /**
     * 创建失败响应
     */
    public static SaleOrderResponse failure(String message) {
        return new SaleOrderResponse(false, message);
    }
    
    /**
     * 创建查询成功响应
     */
    public static SaleOrderResponse querySuccess(List<SaleOrderGoodsRequest> saleOrderGoodsRequestList, Long total, String message) {
        SaleOrderResponse response = new SaleOrderResponse(true, message);
        response.setGoodsList(saleOrderGoodsRequestList);
        response.setTotal(total);
        return response;
    }
    
    /**
     * 创建详情成功响应
     */
    public static SaleOrderResponse detailSuccess(String message) {
        return new SaleOrderResponse(true, message);
    }

    @Override
    public String toString() {
        return "BuyOrderResponse{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", saleorderId=" + saleorderId +
                ", saleorderNo='" + saleorderNo + '\'' +
                ", total=" + total +
                '}';
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Integer getSaleorderId() {
        return saleorderId;
    }

    public void setSaleorderId(Integer saleorderId) {
        this.saleorderId = saleorderId;
    }

    public String getSaleorderNo() {
        return saleorderNo;
    }

    public void setSaleorderNo(String saleorderNo) {
        this.saleorderNo = saleorderNo;
    }

    public Integer getTraderId() {
        return traderId;
    }

    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }

    public String getTraderName() {
        return traderName;
    }

    public void setTraderName(String traderName) {
        this.traderName = traderName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Integer getTotalQuantity() {
        return totalQuantity;
    }

    public void setTotalQuantity(Integer totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public List<SaleOrderGoodsRequest> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<SaleOrderGoodsRequest> goodsList) {
        this.goodsList = goodsList;
    }
}
