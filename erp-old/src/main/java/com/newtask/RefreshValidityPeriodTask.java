package com.newtask;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.goods.dao.CoreSkuGenerateMapper;
import com.vedeng.logistics.dao.WarehouseGoodsOperateLogMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@JobHandler(value = "refreshValidityPeriodTask")
@Component
public class RefreshValidityPeriodTask extends AbstractJobHandler {

    Logger logger = LoggerFactory.getLogger(RefreshValidityPeriodTask.class);

    @Resource
    private WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

    @Resource
    private CoreSkuGenerateMapper coreSkuGenerateMapper;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        XxlJobLogger.log("刷新效期的定时任务start-----------");
        logger.info("刷新效期的定时任务start-----------");

        if (StringUtils.isNotEmpty(param)) {
            Integer skuId = Integer.valueOf(param);
            coreSkuGenerateMapper.updateSkuValidatePeriod(skuId);
            return SUCCESS;
        }
        //获取5分钟内有变化的SKU列表
        List<Integer> skuIds = warehouseGoodsOperateLogMapper.getHaveChangeSkuNos();

        if(CollectionUtils.isEmpty(skuIds)){
            logger.info("最近五分钟无变化的sku列表 -----------");
            XxlJobLogger.log("最近五分钟无变化的sku列表 -----------");
            return SUCCESS;
        }

        logger.info("最近五分钟变化的sku列表长度:"+skuIds.size()+",列表:" + skuIds);

        skuIds.stream().forEach(skuId ->{

            long startTime = System.currentTimeMillis();

            coreSkuGenerateMapper.updateSkuValidatePeriod(skuId);

            long endTime = System.currentTimeMillis();

            XxlJobLogger.log("刷新SKU:"+skuId+"的效期,耗时:"+ (endTime - startTime) + "ms");
        });

        logger.info("刷新效期的定时任务end -----------");
        XxlJobLogger.log("刷新效期的定时任务end -----------");
        return SUCCESS;
    }
}
