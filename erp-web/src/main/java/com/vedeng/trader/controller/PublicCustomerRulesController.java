package com.vedeng.trader.controller;

import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.dao.OrganizationMapper;
import com.vedeng.authorization.model.Organization;
import com.vedeng.authorization.model.Region;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.model.ResultJSON;
import com.vedeng.common.page.Page;
import com.vedeng.erp.trader.domain.PublicCustomerCalculateRules;
import com.vedeng.erp.trader.domain.PublicCustomerRegionRules;
import com.vedeng.erp.trader.domain.dto.PublicCustomerExemptUsersDto;
import com.vedeng.erp.trader.domain.dto.RegionRulesQueryDto;
import com.vedeng.erp.trader.domain.vo.PublicCustomerRegionRulesVo;
import com.vedeng.erp.trader.mapper.PublicCustomerCalculateRulesMapper;
import com.vedeng.erp.trader.mapper.PublicCustomerRegionRulesMapper;
import com.vedeng.erp.trader.service.PublicCustomerCalculateRulesService;
import com.vedeng.erp.trader.service.PublicCustomerExemptUsersService;
import com.vedeng.erp.trader.service.PublicCustomerRegionRulesService;
import com.vedeng.system.service.OrgService;
import com.vedeng.system.service.OrganizationService;
import com.vedeng.system.service.RegionService;
import com.vedeng.system.service.UserService;
import com.vedeng.trader.dao.TraderCustomerMapper;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/trader/customer")
@Slf4j
public class PublicCustomerRulesController extends BaseController {

    @Autowired
    private PublicCustomerCalculateRulesService publicCustomerCalculateRulesService;

    @Resource
    private PublicCustomerRegionRulesService publicCustomerRegionRulesService;

    @Resource
    private PublicCustomerCalculateRulesMapper publicCustomerCalculateRulesMapper;

    @Autowired
    private RegionService regionService;

    @Autowired
    private OrganizationService organizationService;

    @Autowired
    private PublicCustomerExemptUsersService publicCustomerExemptUsersService;

    @Autowired
    private OrgService orgService;

    @Autowired
    private UserService userService;

    @Resource
    private OrganizationMapper organizationMapper;

    @Resource
    private TraderCustomerMapper traderCustomerMapper;

    @Resource
    private PublicCustomerRegionRulesMapper publicCustomerRegionRulesMapper;

    @Value("${b2b_business_division_id}")
    private Integer b2bBusinessDivisionId;

    /**
     * 公海规则维护页面
     *
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/publicCustomerRules")
    public ModelAndView publicCustomerRules(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView("/trader/customer/public_rules_configuration");
        return mv;
    }

    /**
     * 区域规则维护页面
     *
     * @param request res
     * @param regionRulesQueryDto 查询对象
     * @return MV
     */
    @ResponseBody
    @RequestMapping(value = "/regionRulesList")
    public ModelAndView regionRulesList(HttpServletRequest request, RegionRulesQueryDto regionRulesQueryDto,
                                        @RequestParam(value = "pageNo",defaultValue = "1") Integer pageNo, @RequestParam(value = "pageSize",defaultValue = "10")Integer pageSize,@RequestParam(value = "flag",required = false)Integer flag) {

        ModelAndView mv = new ModelAndView("/trader/customer/region_rules_configuration");
        mv.addObject("flag", flag);
        List<Region> provinceList = regionService.getRegionByParentId(1);
        if (regionRulesQueryDto.getProvince() != null && regionRulesQueryDto.getProvince() != 0) {
            List<Region> cityList = regionService.getRegionByParentId(regionRulesQueryDto.getProvince());
            mv.addObject("cityList", cityList);
        }
        if (regionRulesQueryDto.getCity() != null && regionRulesQueryDto.getCity() != 0) {
            List<Region> zoneList = regionService.getRegionByParentId(regionRulesQueryDto.getCity());
            mv.addObject("zoneList", zoneList);
        }
        mv.addObject("provinceList", provinceList);
        Page page = getPageTagNoFlag(request, pageNo, pageSize);
        mv.addObject("regionRulesQueryDto", regionRulesQueryDto);
        this.bindRegionData(mv,regionRulesQueryDto, page);
        mv.addObject("page", page);

        this.bindUserData(mv);
        return mv;
    }

    /**
     * 绑定销售列表
     * @param mv mv
     */
    private void bindUserData(ModelAndView mv) {
        List<User> users = userService.getAllUserIdList();
        mv.addObject("userList", users);
    }

    private void bindSaleUserData(ModelAndView mv) {
        List<User> users = userService.queryOrganizationUser("B2B");
        mv.addObject("userList", users);
    }

    /**
     * 新增区域规则页面
     *
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/addRegionRules")
    public ModelAndView addRegionRules(HttpServletRequest request) {

        ModelAndView mv = new ModelAndView("/trader/customer/add_region_rules");
        List<Region> provinceList = regionService.getRegionByParentId(1);
        mv.addObject("provinceList", provinceList);
        this.bindSaleUserData(mv);
        return mv;
    }


    /**
     * 编辑区域规则
     *
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/editRegionRules")
    public ModelAndView editRegionRules(HttpServletRequest request,PublicCustomerRegionRules publicCustomerRegionRules) {

        ModelAndView mv = new ModelAndView("/trader/customer/edit_region_rules");
        // 当前区域关系的省市区
        if (publicCustomerRegionRules != null && publicCustomerRegionRules.getPublicCustomerRegionRulesId() != null) {
            publicCustomerRegionRules = publicCustomerRegionRulesMapper.selectByPrimaryKey(publicCustomerRegionRules.getPublicCustomerRegionRulesId());
        }
        // 查找当前地址的上级地址列表
        PublicCustomerRegionRulesVo publicCustomerRegionRulesVo = new PublicCustomerRegionRulesVo();
        if (publicCustomerRegionRules != null && publicCustomerRegionRules.getRegionId() != null) {
            Region zone = regionService.getRegionByRegionId(publicCustomerRegionRules.getRegionId());
            if (zone != null && zone.getParentId() != null) {
                Region city = regionService.getRegionByRegionId(zone.getParentId());
                publicCustomerRegionRulesVo.setZoneId(zone.getRegionId());
                List<Region> zoneList = regionService.getRegionByParentId(zone.getParentId());
                mv.addObject("zoneList", zoneList);
                if (city != null && city.getParentId() != null) {
                    Region province = regionService.getRegionByRegionId(city.getParentId());
                    publicCustomerRegionRulesVo.setCityId(city.getRegionId());
                    List<Region> cityList = regionService.getRegionByParentId(city.getParentId());
                    mv.addObject("cityList", cityList);
                    if (province != null && province.getRegionId() != null) {
                        publicCustomerRegionRulesVo.setProvinceId(province.getRegionId());
                    }
                }
            }
        }
        // 销售的上级以及部门
        if (publicCustomerRegionRules.getUserId() != null) {
            publicCustomerRegionRulesVo.setUserId(publicCustomerRegionRules.getUserId());
            publicCustomerRegionRulesVo.setPublicCustomerRegionRulesId(publicCustomerRegionRules.getPublicCustomerRegionRulesId());

            this.blindUserAndBossData(publicCustomerRegionRulesVo);

        }
        List<Region> provinceList = regionService.getRegionByParentId(1);
        mv.addObject("provinceList", provinceList);
        mv.addObject("publicCustomerRegionRulesVo", publicCustomerRegionRulesVo);
        this.bindSaleUserData(mv);
        return mv;
    }


    /**
     * 销售信息
     *
     * @param request res
     * @return ResultJSON 销售boss 以及部门信息
     */
    @ResponseBody
    @RequestMapping(value = "/getUserInfo")
    public ResultJSON getUserInfo(HttpServletRequest request, PublicCustomerRegionRulesVo publicCustomerRegionRulesVo) {

        if (publicCustomerRegionRulesVo!=null&&publicCustomerRegionRulesVo.getUserId()!=null) {
            blindUserAndBossData(publicCustomerRegionRulesVo);
            if (publicCustomerRegionRulesVo.getUserBossName() == null) {
                publicCustomerRegionRulesVo.setUserBossName("");
            }
            if (publicCustomerRegionRulesVo.getOrgName() == null) {
                publicCustomerRegionRulesVo.setOrgName("");
            }
            return ResultJSON.success().message("操作成功").data(publicCustomerRegionRulesVo);
        }
        return ResultJSON.failed().message("查询销售参数不全");

    }

    /**
     * 更新
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/doUpdate")
    public ResultJSON updateInfo(HttpServletRequest request, PublicCustomerRegionRulesVo publicCustomerRegionRulesVo) {

        User sessionUser = getSessionUser(request);
        if (sessionUser == null && sessionUser.getUserId() == null) {
            throw new ServiceException("请重新登录");
        }
        if (publicCustomerRegionRulesVo != null && publicCustomerRegionRulesVo.getUserId() != null && publicCustomerRegionRulesVo.getPublicCustomerRegionRulesId() != null && publicCustomerRegionRulesVo.getRegionId() != null) {
            // 数据校验
            PublicCustomerRegionRules publicCustomerRegionRules = publicCustomerRegionRulesMapper.selectByPrimaryKey(publicCustomerRegionRulesVo.getPublicCustomerRegionRulesId());
            if (publicCustomerRegionRules != null) {
                if (publicCustomerRegionRules.getRegionId() == publicCustomerRegionRulesVo.getRegionId().intValue() && publicCustomerRegionRules.getUserId().intValue() == publicCustomerRegionRulesVo.getUserId()) {
                    return ResultJSON.success().code(1).message("区域不可重复");
                }
                if (publicCustomerRegionRules.getRegionId() == publicCustomerRegionRulesVo.getRegionId().intValue() && publicCustomerRegionRules.getUserId().intValue() != publicCustomerRegionRulesVo.getUserId()) {
                    // 跟新
                    publicCustomerRegionRulesService.updateInfo(publicCustomerRegionRulesVo,sessionUser.getUserId());
                    return ResultJSON.success();
                }
            }
            PublicCustomerRegionRules hasRule = publicCustomerRegionRulesMapper.selectByRegionId(publicCustomerRegionRulesVo.getRegionId());
            if (hasRule != null) {
                Map<String, Object> map = new HashMap<>();
                RegionRulesQueryDto regionRulesQueryDto = new RegionRulesQueryDto();
                regionRulesQueryDto.setRegionId(publicCustomerRegionRulesVo.getRegionId());
                map.put("regionRulesQueryDto", regionRulesQueryDto);
                log.info("查询条件：{}", JSON.toJSONString(map));
                List<PublicCustomerRegionRulesVo> publicCustomerRegionRulesVosList = publicCustomerRegionRulesMapper.queryListPage(map);
                if (CollectionUtils.isNotEmpty(publicCustomerRegionRulesVosList)) {
                    return ResultJSON.success().code(2).message("").data(publicCustomerRegionRulesVosList.get(0));
                }
                return ResultJSON.success().code(2).message("").data(hasRule);
            }
            publicCustomerRegionRulesService.updateInfo(publicCustomerRegionRulesVo,sessionUser.getUserId());
            return ResultJSON.success().message("操作成功").data(publicCustomerRegionRulesVo);
        }
        return ResultJSON.failed().message("校验参数不全");

    }

    /**
     * 审核信息
     * code 1 区域不可重复 即和原始值相同
     * code 0 success 无问题
     * code 2 地区已有分配
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/doCheckData")
    public ResultJSON doCheckData(HttpServletRequest request, PublicCustomerRegionRulesVo publicCustomerRegionRulesVo) {

        if (publicCustomerRegionRulesVo != null && publicCustomerRegionRulesVo.getUserId() != null  && publicCustomerRegionRulesVo.getRegionId() != null) {
             // 查询已有记录 和提交数据相同则为重复数据
            if (publicCustomerRegionRulesVo.getPublicCustomerRegionRulesId() != null) {
                PublicCustomerRegionRules publicCustomerRegionRules = publicCustomerRegionRulesMapper.selectByPrimaryKey(publicCustomerRegionRulesVo.getPublicCustomerRegionRulesId());
                if (publicCustomerRegionRules != null) {
                    if (publicCustomerRegionRules.getRegionId() == publicCustomerRegionRulesVo.getRegionId().intValue() && publicCustomerRegionRules.getUserId().intValue() == publicCustomerRegionRulesVo.getUserId()) {
                        return ResultJSON.success().code(1).message("区域不可重复");
                    }
                    if (publicCustomerRegionRules.getRegionId() == publicCustomerRegionRulesVo.getRegionId().intValue() && publicCustomerRegionRules.getUserId().intValue() != publicCustomerRegionRulesVo.getUserId()) {
                        return ResultJSON.success();
                    }
                }
            }

            PublicCustomerRegionRules hasRule = publicCustomerRegionRulesMapper.selectByRegionId(publicCustomerRegionRulesVo.getRegionId());
            if (hasRule != null) {
                Map<String, Object> map = new HashMap<>();
                RegionRulesQueryDto regionRulesQueryDto = new RegionRulesQueryDto();
                regionRulesQueryDto.setRegionId(publicCustomerRegionRulesVo.getRegionId());
                map.put("regionRulesQueryDto", regionRulesQueryDto);
                log.info("查询条件：{}", JSON.toJSONString(map));
                List<PublicCustomerRegionRulesVo> publicCustomerRegionRulesVosList = publicCustomerRegionRulesMapper.queryListPage(map);
                if (CollectionUtils.isNotEmpty(publicCustomerRegionRulesVosList)) {
                    return ResultJSON.success().code(2).message("").data(publicCustomerRegionRulesVosList.get(0));
                }
            }

            return ResultJSON.success().message("操作成功").data(publicCustomerRegionRulesVo);
        }
        return ResultJSON.failed().message("校验参数不全");

    }


    /**
     * 获取当前销售的部门以及上级信息
     * @param publicCustomerRegionRulesVo
     */
    private void blindUserAndBossData(PublicCustomerRegionRulesVo publicCustomerRegionRulesVo) {
        if (publicCustomerRegionRulesVo != null && publicCustomerRegionRulesVo.getUserId() != null) {
            User userBoss = userService.getUserParentInfoByUserId(publicCustomerRegionRulesVo.getUserId());
            if (userBoss != null) {
                publicCustomerRegionRulesVo.setUserBossName(userBoss.getpUsername());
                publicCustomerRegionRulesVo.setUserBoss(userBoss.getParentId());
            }
            // 过滤非b2b的部门
            List<Integer> orgIdListByUserId = organizationMapper.getOrgIdListByUserId(publicCustomerRegionRulesVo.getUserId());

            Set<Integer> integers = userService.getChildrenSetOfOrg(b2bBusinessDivisionId,1);
            List<Integer> collect = new ArrayList<>(integers);

            // 去除空
            List<Integer> collect1 = collect.stream().filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect1)) {
                // 去重
                Integer max = orgIdListByUserId.stream().filter(c -> c != null && collect1.contains(c)).max(Comparator.naturalOrder()).orElse(null);
                List<String> names = new ArrayList<>();
                organizationService.queryOrgNameB2B(max, collect1, names);
                StringBuilder nameB2B = new StringBuilder("");
                if (CollectionUtils.isNotEmpty(names)) {
                    for (int i = names.size() - 1; i >= 0; i--) {
                        if (i != names.size() - 1) {
                            nameB2B.append("/");
                        }
                        nameB2B.append(names.get(i));
                    }
                }
                publicCustomerRegionRulesVo.setOrgName(nameB2B.toString());
            }

        }

    }

    /**
     * 保存区域规则
     *
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/saveRegionRules")
    public ResultJSON saveRegionRules(HttpServletRequest request, Integer userId,Integer[] zone,Integer[] province,Integer[] city) {
        try {

            if (userId == null || zone == null || zone.length == 0) {
                throw new ServiceException("销售、以及地址不可为空");
            }
            User sessUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
            if (sessUser == null || sessUser.getUserId() == null) {
                throw new ServiceException("请重新登录");
            }
            long time = System.currentTimeMillis();
            ArrayList<Integer> regions = new ArrayList<>();
            for (int i = 0; i < zone.length; i++) {
                // 加入区
                if (zone[i] != 0) {
                    if (zone[i] == -1) {
                        // 排除掉city为0的
                        if (city[i]!=0) {
                            if (city[i] != -1) {
                                // 区选全部则是拿市
                                List<Region> regionByParentId = regionService.getRegionByParentId(city[i]);
                                if (CollectionUtils.isNotEmpty(regionByParentId)) {
                                    List<Integer> collect = regionByParentId.stream().map(Region::getRegionId).collect(Collectors.toList());
                                    regions.addAll(collect);
                                }
                            }else {
                                // 市也是全部则拿省下的
                                if (province[i] != 0 && province[i] != -1) {
                                    List<Region> cityIds = regionService.getRegionByParentId(province[i]);
                                    if (CollectionUtils.isNotEmpty(cityIds)) {
                                        cityIds.forEach(c->{
                                            List<Region> zoneIds = regionService.getRegionByParentId(c.getRegionId());
                                            if (CollectionUtils.isNotEmpty(zoneIds)) {
                                                List<Integer> collect = zoneIds.stream().map(Region::getRegionId).collect(Collectors.toList());
                                                regions.addAll(collect);
                                            }
                                        });
                                    }
                                }
                            }

                        }

                    } else {
                        // 区地址 不是0且不是1
                        regions.add(zone[i]);

                    }
                }
            }
            log.info("区域新增校验数据：{}",JSON.toJSONString(regions));
            List<PublicCustomerRegionRules> list = new ArrayList<>();
            // 判断
            for (int i = 0; i < regions.size(); i++) {
                PublicCustomerRegionRules hasRule = publicCustomerRegionRulesMapper.selectByRegionId(regions.get(i));
                if (hasRule != null) {
//                Region zone = regionService.getRegionByRegionId(publicCustomerRegionRules.getRegionId());
                    if (hasRule.getUserId().intValue()!=userId) {
                        Map<String, Object> map = new HashMap<>();
                        RegionRulesQueryDto regionRulesQueryDto = new RegionRulesQueryDto();
                        regionRulesQueryDto.setRegionId(regions.get(i));
                        map.put("regionRulesQueryDto", regionRulesQueryDto);
                        log.info("查询条件：{}", JSON.toJSONString(map));
                        List<PublicCustomerRegionRulesVo> publicCustomerRegionRulesVosList = publicCustomerRegionRulesMapper.queryListPage(map);
                        if (CollectionUtils.isNotEmpty(publicCustomerRegionRulesVosList)) {
                            return ResultJSON.success().code(2).message("区域不可重复").data(publicCustomerRegionRulesVosList.get(0));
                        }
                        return ResultJSON.success().code(2).message("区域不可重复").data(publicCustomerRegionRulesVosList.get(0));
                    }else {
                        // 当前销售有去除
                        continue;
                    }

                }
                PublicCustomerRegionRules publicCustomerRegionRules = new PublicCustomerRegionRules();
                publicCustomerRegionRules.setRegionId(regions.get(i));
                publicCustomerRegionRules.setModTime(time);
                publicCustomerRegionRules.setAddTime(time);
                publicCustomerRegionRules.setCreator(sessUser.getUserId());
                publicCustomerRegionRules.setUserId(userId);
                list.add(publicCustomerRegionRules);
            }
            if (CollectionUtils.isNotEmpty(list)) {
                publicCustomerRegionRulesService.batchAddPublicCustomerCalculateRules(list);
            }



        } catch (Exception e) {
            log.error("保存区域规则错误：msg:", e);
            return ResultJSON.failed().message("保存区域规则错误," + e.getMessage());
        }
        return ResultJSON.success().message("操作成功");
    }


    /**
     * 删除区域规则
     *
     * @param request res
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/deleteRegionRule")
    public ResultJSON deleteRegionRule(HttpServletRequest request, PublicCustomerRegionRules publicCustomerRegionRules) {

        try {
            publicCustomerRegionRulesService.deleteRegionRule(publicCustomerRegionRules.getPublicCustomerRegionRulesId());
        } catch (Exception e) {
            log.error("删除区域规则错误：msg:", e);
            return ResultJSON.failed().message("删除区域规则错误," + e.getMessage());
        }
        return ResultJSON.success().message("操作成功");
    }


    /**
     * 批量删除区域规则
     *
     * @param request res
     * @param idList 主键集合
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/batchDeleteRegionRule")
    public ResultJSON deleteRegionRule(HttpServletRequest request, @RequestParam("idList") Integer[] idList) {

        try {
            log.info("批量删除{}", JSON.toJSONString(idList));
            publicCustomerRegionRulesService.batchDeleteRegionRule(idList);
        } catch (Exception e) {
            log.error("删除区域规则错误：msg:", e);
            return ResultJSON.failed().message("删除区域规则错误," + e.getMessage());
        }
        return ResultJSON.success().message("操作成功");
    }

    /**
     * 公海条件详情页面
     *
     * @return mav
     */
    @RequestMapping(value = "/view/calculate/rules")
    public ModelAndView viewCalculateRules() {
        ModelAndView mav = new ModelAndView();
        mav.addObject("ruleDetailVo", publicCustomerCalculateRulesService.getRulesDetail());
        mav.setViewName("trader/public/public_customer_calculate_rules");
        return mav;
    }

    /**
     * 配置公海条件页面
     *
     * @return mav
     */
    @RequestMapping(value = "/config/calculate/rules")
    public ModelAndView configCalculateRules() {
        ModelAndView mav = new ModelAndView();
        mav.addObject("latestRule",publicCustomerCalculateRulesMapper.getRecentlyPublicCustomerCalculateRule());
        mav.setViewName("trader/public/config_calculate_rules");
        return mav;
    }

    /**
     * 查询区域规则列表分页
     * @param mv mv
     * @param regionRulesQueryDto 查询信息
     * @param page 分页信息
     * @return
     */
    public void bindRegionData(ModelAndView mv, RegionRulesQueryDto regionRulesQueryDto, Page page) {

        Map<String, Object> map = new HashMap<>();
        map.put("regionRulesQueryDto", regionRulesQueryDto);
        map.put("page", page);
        log.info("查询条件：{}", JSON.toJSONString(map));
        Set<Integer> integers = userService.getChildrenSetOfOrg(b2bBusinessDivisionId,1);
        List<Integer> collect = new ArrayList<>(integers);

        // 去除空
        List<Integer> b2bOrgIds = collect.stream().filter(Objects::nonNull).collect(Collectors.toList());

        List<PublicCustomerRegionRulesVo> publicCustomerRegionRulesVosList = publicCustomerRegionRulesMapper.queryListPage(map);

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(publicCustomerRegionRulesVosList)) {
            publicCustomerRegionRulesVosList.forEach(c->{
                // 封装部门信息
                List<Integer> orgIdListByUserId = organizationMapper.getOrgIdListByUserId(c.getUserId());
                StringBuilder nameB2B = new StringBuilder("");
                if (CollectionUtils.isNotEmpty(b2bOrgIds)) {
                    Integer max = orgIdListByUserId.stream().filter(a -> a != null && b2bOrgIds.contains(a)).max(Comparator.naturalOrder()).orElse(null);
                    List<String> names = new ArrayList<>();
                    organizationService.queryOrgNameB2B(max, b2bOrgIds, names);

                    if (CollectionUtils.isNotEmpty(names)) {
                        for (int i = names.size() - 1; i >= 0; i--) {
                            if (i != names.size() - 1) {
                                nameB2B.append("/");
                            }
                            nameB2B.append(names.get(i));
                        }
                    }
                    c.setOrgName(nameB2B.toString());
                }
                if (StringUtils.isEmpty(nameB2B.toString())) {
                    c.setOrgName("此销售归属部门发生变化，不再属于B2B部门");
                }
            });
        }
        log.info("查询结果：{}", JSON.toJSONString(publicCustomerRegionRulesVosList));
        mv.addObject("publicCustomerRegionRulesVos", publicCustomerRegionRulesVosList);
    }

    /**
     * 保存公海计算规则
     *
     * @param request request
     * @param publicCustomerCalculateRules 公海计算规则
     * @return
     */
    @RequestMapping(value = "/save/calculate/rules")
    @ResponseBody
    public ResultInfo saveCalculateRules(HttpServletRequest request, PublicCustomerCalculateRules publicCustomerCalculateRules) {
        publicCustomerCalculateRules.setCreator(getSessionUser(request).getUserId());
        publicCustomerCalculateRules.setAddTime(System.currentTimeMillis());
        int count = publicCustomerCalculateRulesService.saveCalculateRule(publicCustomerCalculateRules);
        // 计算规则保存成功，则清空所有客户的 预警次数
        if (count > 0) {
            traderCustomerMapper.resetAllWarningCount();
        }
        return ResultInfo.success();
    }


    /**
     * <b>Description:</b><br>
     * 根据参数生成实体page对象（无记录总数）
     *
     * @param request
     *            请求
     * @param pageNo
     *            当前页
     * @param pageSize
     *            每页条数
     * @return page对象
     * @Note <b>Author:</b> Jerry <br>
     *       <b>Date:</b> 2017年4月25日 上午11:10:52
     */
    protected Page getPageTagNoFlag(HttpServletRequest request, Integer pageNo, Integer pageSize) {
        String path = request.getRequestURL().toString();
        // String web_name = request.getServletContext().getContextPath();
        // return Page.newBuilder(pageNo, pageSize,
        // path.substring(path.indexOf(web_name)+web_name.length()));
        try {
            String str = "";
            Map<?, ?> map = request.getParameterMap();
            if (map != null && (!map.isEmpty())) {
                path += "?";
                for (Object key : map.keySet()) {
                    if (!(key.toString().equals("pageNo") || key.toString().equals("pageSize")||key.toString().equals("flag"))) {
                        str = java.net.URLDecoder.decode(request.getParameter(key.toString()), "UTF-8");
                        str = java.net.URLEncoder.encode(str, "UTF-8");
                        path = path + key + "=" + str + "&";
                    }
                }
                path = path.substring(0, path.length() - 1);
            }
        } catch (Exception e) {
            logger.error("分页初始化失败", e);
            return Page.newBuilder(pageNo, pageSize, path);
        }
        return Page.newBuilder(pageNo, pageSize, path);
    }

    /**
     * 公海条件豁免配置页面
     *
     * @return mav
     */
    @RequestMapping(value = "/view/calculate/rules/exemption")
    public ModelAndView viewCalculateRulesExemption(HttpServletRequest request, @RequestParam(value = "pageNo",defaultValue = "1") Integer pageNo, @RequestParam(value = "pageSize",defaultValue = "10")Integer pageSize, User user) {
        ModelAndView mav = new ModelAndView();
        User sessionUser = getSessionUser(request);
        if (sessionUser == null || sessionUser.getUserId() == null) {
            throw new ServiceException("请重新登录");
        }
        // b2b 所有部门
        List<Integer> userB2B = null;
        Set<Integer> integers = userService.getChildrenSetOfOrg(b2bBusinessDivisionId,1);

        List<Organization> organizations = organizationService.selectOrganizationNameByOrgids(new ArrayList<Integer>(integers));

        // 树形结构
        JSONArray jsonArray = (JSONArray) JSONArray.fromObject(organizations);
        List<Organization> sellist = new ArrayList<Organization>();
        Organization orgByOrgId = orgService.getOrgByOrgId(b2bBusinessDivisionId);
        JSONArray jsonList = treeMenuList(jsonArray,orgByOrgId.getParentId(),"");
        List<Organization> list = resetList(jsonList,sellist,0,true);

        List<Integer> orgIds = userService.getUserOrgIdsByUserId(sessionUser.getUserId());
        if (CollectionUtils.isNotEmpty(integers)) {
            if (CollectionUtils.isNotEmpty(orgIds)) {
                userB2B = orgIds.stream().filter(c -> integers.contains(c)).collect(Collectors.toList());
            }
        }
       mav.addObject("orgs", list);

        if (user != null && user.getOrgId() != null) {
            List<User> users = userService.getPosTypeUserListByOrgId(user.getOrgId(),310);
            mav.addObject("users", users);
        }
        mav.addObject("oldData", user);

        Page page = getPageTag(request, pageNo, pageSize);
        List<PublicCustomerExemptUsersDto> data = publicCustomerExemptUsersService.queryExemptData(page);
        mav.addObject("list", data);
        mav.addObject("page", page);
        mav.setViewName("trader/public/public_customer_calculate_rules_exemption");
        return mav;
    }

    private JSONArray treeMenuList(JSONArray menuList, int parentId,String parentName) {
        JSONArray childMenu = new JSONArray();
        for (Object object : menuList) {
            JSONObject jsonMenu = (JSONObject) JSONObject.fromObject(object);
            int menuId = jsonMenu.getInt("orgId");
            int pid = jsonMenu.getInt("parentId");
            if(parentName != ""){
                jsonMenu.element("nameArr", parentName + "--" + jsonMenu.getString("orgName"));
            }else{
                jsonMenu.element("nameArr", jsonMenu.getString("orgName"));
            }
            if (parentId == pid) {
                JSONArray c_node = treeMenuList(menuList, menuId,jsonMenu.getString("nameArr"));
                jsonMenu.put("childNode", c_node);
                childMenu.add(jsonMenu);
            }
        }
        return childMenu;
    }

    public List<Organization> resetList(JSONArray tasklist,List<Organization> sellist,int num,Boolean joinChar){
        String str = "";
        if(joinChar){
            for(int i=0;i<(num*2);i++){
                str += "-";
            }
        }
        for(Object obj:tasklist){
            JSONObject jsonMenu = (JSONObject) JSONObject.fromObject(obj);
            Organization sm = new Organization();
            sm.setOrgId(Integer.valueOf(jsonMenu.getInt("orgId")));
            String orgName = "";
            if(joinChar){
                orgName = "|-"+str+jsonMenu.getString("orgName");
            }else{
                orgName = jsonMenu.getString("orgName");
            }
            sm.setOrgName(orgName);
            sm.setLevel(jsonMenu.getInt("level"));
            sm.setParentId(jsonMenu.getInt("parentId"));
            sm.setAddTime(jsonMenu.getLong("addTime"));
            sm.setOrgNames(jsonMenu.getString("nameArr"));
            sm.setType(jsonMenu.getInt("type"));
            sm.setIsDeleted(jsonMenu.getInt("isDeleted")+"");
            sellist.add(sm);
            if(jsonMenu.get("childNode")!=null){
                if(JSONArray.fromObject(jsonMenu.get("childNode")).size()>0){
                    num++;
                    resetList(JSONArray.fromObject(jsonMenu.get("childNode")),sellist,num,joinChar);
                    num--;
                }
            }
        }
        return sellist;
    }

    /**
     * 删除公海豁免人员
     *
     * @param request res
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/deleteExemptionUser")
    public ResultJSON deleteExemptionUser(HttpServletRequest request, PublicCustomerExemptUsersDto publicCustomerExemptUsersDto) {
        Integer result = 1;
        User sessionUser = getSessionUser(request);
        if (sessionUser != null && publicCustomerExemptUsersDto != null) {
            publicCustomerExemptUsersDto.setUpdater(sessionUser.getUserId());
        }
        try {
           result= publicCustomerExemptUsersService.deleteExemptionUser(publicCustomerExemptUsersDto);
        } catch (Exception e) {
            log.error("删除公海豁免人员异常：msg:", e);
            return ResultJSON.failed().message("删除公海豁免人员异常," + e.getMessage());
        }
        return ResultJSON.success().message("操作成功").data(result);
    }

    @ResponseBody
    @RequestMapping(value="/getSaleUser")
    public ResultInfo<User> getSaleUser(HttpServletRequest request,Organization organization){
        ResultInfo<User> userResultInfo = new ResultInfo<User>();
        List<User> users = userService.getPosTypeUserListByOrgId(organization.getOrgId(),310);
        if(users != null){
            userResultInfo.setCode(0);
            userResultInfo.setMessage("操作成功");

            userResultInfo.setListData(users);
        }
        return userResultInfo;
    }

    @ResponseBody
    @RequestMapping(value = "/addExemptUser")
    public ResultJSON addExemptUser(HttpServletRequest request, PublicCustomerExemptUsersDto publicCustomerExemptUsersDto) {
        User sessionUser = getSessionUser(request);
        if (sessionUser == null || sessionUser.getUserId() == null) {
            throw new ServiceException("请重新登录");
        }
        if (publicCustomerExemptUsersDto == null || publicCustomerExemptUsersDto.getUserId() == null) {
            throw new ServiceException("参数缺失");
        }
        publicCustomerExemptUsersDto.setCreator(sessionUser.getUserId());
        Integer result = 1;
        try {
            result = publicCustomerExemptUsersService.saveExemptionUser(publicCustomerExemptUsersDto);
        } catch (Exception e) {
            log.error("添加公海豁免人员异常：msg:", e);
            return ResultJSON.failed().message("添加公海豁免人员异常," + e.getMessage());
        }
        return ResultJSON.success().message("操作成功").data(result);
    }


}
