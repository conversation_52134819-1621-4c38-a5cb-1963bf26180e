package com.wms.service.other;

import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.flash.enums.WmsSendOrderTypeEnum;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.BuyorderGoods;
import com.vedeng.order.model.Saleorder;
import com.wms.constant.CancelReasonConstant;
import com.wms.constant.LogicalEnum;
import com.wms.constant.WMSContant;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.dao.WmsSendOrderMapper;
import com.wms.dto.PurchaseDeliveryDirectBatchDetailDto;
import com.wms.dto.PutPurchaseOrderDto;
import com.wms.dto.PutPurchaseOrderGoodsDto;
import com.wms.dto.WmsResponse;
import com.wms.model.po.WmsSendOrder;
import com.wms.service.CancelTypeService;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.WmsInterface;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: daniel
 * @Date: 2022/4/13 17 23
 * @Description: 下发直发采购单的同行单数据
 */
@Service("doPutDeliveryDirectPeerListService")
public class DoPutDeliveryDirectPeerListServiceImpl {

    private static final Logger logger = LoggerFactory.getLogger(DoPutDeliveryDirectPeerListServiceImpl.class);

    @Resource
    private BuyorderMapper buyorderMapper;

    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Resource
    private UserMapper userMapper;

    @Autowired
    private WMSInterfaceFactory wmsInterfaceFactory;

    @Autowired
    private CancelTypeService cancelTypeService;

    @Autowired
    private WmsSendOrderMapper wmsSendOrderMapper;

    /**
     * 直发采购单入库下发WMS
     * @param deliveryDirectBatchDetailDtoList 直发采购单维护的同行单集合
*      如果之前维护的同行单数据中 wmsHandledArrivalCount 小于 arrivalCount，那么之前维护的同行单数据还需再次重新下发，下发数量为 arrivalCount - wmsHandledArrivalCount。
     * @param operator 保存同行单的用户
     * @return 下发WMS结果
     * @throws Exception 下发异常
     */
    public void doPutPurchaseOrderMethod(List<PurchaseDeliveryDirectBatchDetailDto> deliveryDirectBatchDetailDtoList, User operator) throws Exception{

        if (CollectionUtils.isEmpty(deliveryDirectBatchDetailDtoList)){
            return;
        }

        Buyorder buyorder = buyorderMapper.selectByPrimaryKey(deliveryDirectBatchDetailDtoList.get(0).getBuyorderId());
        if (buyorder == null) {
            logger.error("下发直发采购单的同行单数据，采购单：{}不存在",deliveryDirectBatchDetailDtoList.get(0).getBuyorderId());
            return;
        }

        if(buyorder.getDeliveryDirect() == 0){
            logger.info("采购单{}是普发，不下发WMS同行单数据",buyorder.getBuyorderNo());
            return;
        }

        try {
            int wmsSendOrderId = saveWmsSendOrder(buyorder,operator, WmsSendOrderTypeEnum.PUT_DELIVERY_DIRECT_BUYORDER.getCode());

            List<PutPurchaseOrderGoodsDto> details = this.getDetails(deliveryDirectBatchDetailDtoList,buyorder.getBuyorderNo());

            //如果detail为空就不下发
            if(CollectionUtils.isEmpty(details)){
                logger.info("若采购订单中只包含特殊商品，则整个订单不下传WMS, buyOrderNo : {}",buyorder.getBuyorderNo());
                return;
            }

            PutPurchaseOrderDto putPurchaseOrderDto = new PutPurchaseOrderDto();
            putPurchaseOrderDto.setWarehouseId("NJ02");
            putPurchaseOrderDto.setDocNo(buyorder.getBuyorderNo());
            putPurchaseOrderDto.setPoType(WmsInterfaceOrderType.DELIVERY_DIRECT_IN);
            putPurchaseOrderDto.setPoCreationTime(DateUtil.convertString(buyorder.getAddTime(),"yyyy-MM-dd HH:mm:ss"));
            // 预期到货时间，快递时间中的最小 最大值 因为传递过来的就是yyyy-MM-dd 的直接字符比较
            Optional<PurchaseDeliveryDirectBatchDetailDto> min = deliveryDirectBatchDetailDtoList.stream().filter(item->StringUtils.isNotEmpty(item.getExpressDeliveryTime())).min(Comparator.comparing(PurchaseDeliveryDirectBatchDetailDto::getExpressDeliveryTime));
            if (min.isPresent()) {
                putPurchaseOrderDto.setExpectedArriveTime1(min.get().getExpressDeliveryTime() + " 00:00:00");
            } else {
                putPurchaseOrderDto.setExpectedArriveTime1(DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss"));
            }
            putPurchaseOrderDto.setExpectedArriveTime2("9999-12-21 23:59:59");;


            putPurchaseOrderDto.setSupplierId(buyorder.getTraderId());
            putPurchaseOrderDto.setSupplierName(buyorder.getTraderName());
            User user = userMapper.getUserInfoByUserId(buyorder.getCreator());
            putPurchaseOrderDto.setPoReferenceA(StringUtil.isEmpty(user.getOrgName()) ? "/" : user.getOrgName());
            putPurchaseOrderDto.setPoReferenceB(user.getUsername());
            putPurchaseOrderDto.setNotes(buyorder.getLogisticsComments());
            //直发采购入库单的详情
            putPurchaseOrderDto.setDetails(details);

            //取消作业
            if(!cancelTypeService.cancelInputDeliveryDirectPurchaseMethod(buyorder.getBuyorderNo(),"直发采购单维护同行单数据")){
                logger.info("wms不得取消下发失败 单号:{}", buyorder.getBuyorderNo());
                return;
            }

            //wms采购入库单
            WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_PURCHASE_ORDER);

            logger.info("直发采购单:"+buyorder.getBuyorderNo()+",同行单数据开始下发WMS,参数:" + JSON.toJSONString(putPurchaseOrderDto));
            WmsResponse response = wmsInterface.request(putPurchaseOrderDto);

            if("1".equals(response.getReturnFlag())){
                WmsSendOrder updateWmsSendOrder = new WmsSendOrder();
                updateWmsSendOrder.setWmsSendOrderId(wmsSendOrderId);
                updateWmsSendOrder.setSendStatus(ErpConst.ONE);
                wmsSendOrderMapper.updateByPrimaryKeySelective(updateWmsSendOrder);
            }
        } catch (Exception e){
            logger.error("下发销售单失败error 单号：{}", buyorder.getBuyorderNo(),e);
        }


    }

    /**
     * 获取具体的商品信息，计算数量
     * @param deliveryDirectBatchDetailDtoList 直发采购单同行单数据
     * @param buyorderNo 采购单号
     * @return 下发详情
     */
    private List<PutPurchaseOrderGoodsDto> getDetails(List<PurchaseDeliveryDirectBatchDetailDto> deliveryDirectBatchDetailDtoList, String buyorderNo) {

        Map<String, List<BuyorderGoods>> buyorderGoodsMap =
                buyorderGoodsMapper.getBuyorderGoodsByBuyorderId(deliveryDirectBatchDetailDtoList.get(0).getBuyorderId()).stream()
                        .collect(Collectors.groupingBy(BuyorderGoods::getSku));

        return deliveryDirectBatchDetailDtoList.stream()
                .map(item -> {
                    PutPurchaseOrderGoodsDto detail = new PutPurchaseOrderGoodsDto();
                    detail.setLineNo(String.valueOf(item.getPurchaseDeliveryDirectBatchDetailId()));
                    detail.setSku(item.getSku());
                    detail.setOrderedQty(item.getArrivalCount());
                    if (item.getManufactureDateTime() != null) {
                        detail.setLotAtt01(DateUtil.DateToString(item.getManufactureDateTime(),DateUtil.DATE_FORMAT));
                    }
                    if (item.getInvalidDateTime() != null) {
                        detail.setLotAtt02(DateUtil.DateToString(item.getInvalidDateTime(),DateUtil.DATE_FORMAT));
                    }
                    detail.setLotAtt03(item.getExpressDeliveryTime());
//                    if (item.getUnionSequence() == 0){
                    detail.setLotAtt04(item.getBatchNumber());
//                    } else {
//                        detail.setLotAtt012(item.getBatchNumber());
//                    }
                    detail.setLotAtt06(item.getRegisterNumber());
                    detail.setLotAtt07(buyorderNo);
                    detail.setLotAtt08(LogicalEnum.HG.getLogicalWarehouseCode());
                    if (buyorderGoodsMap.containsKey(item.getSku())){
                        detail.setDedi04(String.valueOf(buyorderGoodsMap.get(item.getSku()).get(0).getBuyorderId()));
                    }
                    return detail;
                })
                .collect(Collectors.toList());
    }

    private Integer saveWmsSendOrder(Buyorder buyorder, User user, int orderType) {
        WmsSendOrder wmsSendOrder = new WmsSendOrder();
        try {
            wmsSendOrder.setOrderType(orderType);
            wmsSendOrder.setOrderId(buyorder.getBuyorderId());
            wmsSendOrder.setOrderNo(buyorder.getBuyorderNo());
            WmsSendOrder oldInfo = wmsSendOrderMapper.getWmsSendOrderInfo(wmsSendOrder);
            if(oldInfo != null){
                WmsSendOrder update = new WmsSendOrder();
                update.setSendStatus(0);
                update.setWmsSendOrderId(oldInfo.getWmsSendOrderId());
                wmsSendOrderMapper.updateByPrimaryKeySelective(update);
                return oldInfo.getWmsSendOrderId();
            }
            Integer userId = 0;
            if(user != null && user.getUserId() != null){
                userId = user.getUserId();
            }
            wmsSendOrder.setCreator(userId);
            wmsSendOrder.setUpdater(userId);
            wmsSendOrder.setSendStatus(0);
            wmsSendOrderMapper.insertSelective(wmsSendOrder);
        } catch (Exception e) {
            logger.error("saveWmsSendOrder error，下发直发采购单：{}",buyorder.getBuyorderNo(),e);
        }
        return wmsSendOrder.getWmsSendOrderId();
    }
}
