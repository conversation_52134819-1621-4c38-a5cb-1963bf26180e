package com.test.stock;

import com.vedeng.erp.aftersale.dto.ExecuteRefundDto;
import com.vedeng.erp.aftersale.service.ExpenseAfterSalesService;
import org.junit.Test;

import javax.annotation.Resource;

public class BuyorderExpenseService extends SpeicalWarehouseProcessor{

    @Resource
    private ExpenseAfterSalesService expenseAfterSalesService;

    @Test
    public void test(){
        ExecuteRefundDto expenseAfterSalesDto = new ExecuteRefundDto();
        expenseAfterSalesDto.setBuyorderExpenseId(1002);
        expenseAfterSalesDto.setExpenseAfterSalesId(1001L);
        expenseAfterSalesService.executeRefundOperation(expenseAfterSalesDto);
    }
}
