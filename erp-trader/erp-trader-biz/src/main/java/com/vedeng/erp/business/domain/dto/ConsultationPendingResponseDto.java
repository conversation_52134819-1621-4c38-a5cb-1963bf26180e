package com.vedeng.erp.business.domain.dto;

import lombok.Data;

import java.util.Date;

@Data
public class ConsultationPendingResponseDto {
    /**
     * 主键
     */
    private Long businessChanceSeekHelpId;

    /**
     * 商机编号
     */
    private String bussinessChanceNo;

    /**
     * 商机id
     */
    private Integer bussinessChanceId;

    /**
     * 求助时间
     */
    private Date addTime;

    /**
     * 求助人
     */
    private String creatorName;

    /**
     * 咨询内容
     */
    private String content;

    /**
     * 咨询状态：0未处理 1已处理 2关闭
     */
    private Integer status;
}
