<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
		http://www.springframework.org/schema/tx
		http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
		http://www.springframework.org/schema/aop
		http://www.springframework.org/schema/aop/spring-aop-3.0.xsd">


    <!-- 数据源 -->
    <bean name="dataSourceKingDee" class="com.alibaba.druid.pool.DruidDataSource" init-method="init"
          destroy-method="close">
        <property name="url" value="${jdbc_url}"/>
        <property name="username" value="${jdbc_username}"/>
        <property name="password" value="${jdbc_password}"/>

<!--                <property name="url" value="***************************************************************************************************************************************************" />-->
<!--                <property name="username" value="fatread" />-->
<!--                <property name="password" value="fatread" />-->

        <property name="maxActive" value="100"/>
        <property name="minIdle" value="5"/>
        <property name="maxWait" value="30000"/>
        <property name="removeAbandoned" value="true"/>
        <property name="logAbandoned" value="true"/>
        <property name="testWhileIdle" value="true"/>
        <property name="timeBetweenEvictionRunsMillis" value="60000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>
    </bean>


    <!-- SqlSessionFactory -->
    <bean id="sqlSessionFactoryKingDee" class="org.mybatis.spring.SqlSessionFactoryBean">
        <!-- 引用上面已经配置好的数据库连接池 -->
        <property name="dataSource" ref="dataSourceKingDee"/>
        <property name="configLocation" value="classpath:kingdee-mybatis-config.xml"/>
        <!-- mapper配置路径 -->
        <property name="mapperLocations" value="classpath*:mapping/*/*.xml"/>
        <property name="typeHandlersPackage" value="com.vedeng.common.mybatis.handler"/>
    </bean>

    <!-- Mapper扫描配置 -->
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.vedeng.erp.kingdee.repository,com.vedeng.erp.kingdee.batch.repository"/>
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactoryKingDee"/>
    </bean>

    <!-- Mybatis事务管理配置 -->
    <bean id="transactionManagerKingDee" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dataSourceKingDee"/>
    </bean>

    <!-- 事务控制 -->
    <tx:annotation-driven proxy-target-class="true" transaction-manager="transactionManagerKingDee"/>

    <bean id="sqlSessionTemplate" class="org.mybatis.spring.SqlSessionTemplate">
        <constructor-arg index="0" ref="sqlSessionFactoryKingDee" />
    </bean>

    <bean id="sqlMapper" class="com.vedeng.common.mybatis.jbatis.SqlMapper" >
        <constructor-arg ref="sqlSessionTemplate" />
    </bean>
</beans>

