package com.vedeng.infrastructure.ip.api;

import com.vedeng.base.api.dto.reqParam.IpInfo;
import com.vedeng.base.api.service.AliyunIpApiService;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.common.feign.constants.ServerConstants;
import feign.Headers;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;


@FeignApi(serverName = ServerConstants.BASE_SERVER)
public interface IpApi {
    
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /aliyun/getVedengRegionByIp")
    RestfulResult<IpInfo> getVedengRegionByIp(@RequestBody IpInfo ipInfo);
}
