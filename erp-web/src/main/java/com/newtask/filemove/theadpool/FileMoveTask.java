package com.newtask.filemove.theadpool;

import com.newtask.filemove.OssUploadService;
import com.newtask.filemove.dto.FileMoveDto;
import com.vedeng.common.model.FileInfo;
import org.apache.ibatis.exceptions.PersistenceException;
import org.mybatis.spring.MyBatisSystemException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

public abstract class FileMoveTask implements Runnable{

    private static final Logger LOGGER = LoggerFactory.getLogger(FileMoveTask.class);

    @Autowired
    private OssUploadService ossUploadService;

    private List<FileMoveDto> fileMoveList;

    private volatile AtomicBoolean isRunning = new AtomicBoolean(true);

    public FileMoveTask(List<FileMoveDto> fileMoveList) {

        this.fileMoveList = fileMoveList;

        // 运行时注入service
        WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
        ossUploadService = (OssUploadService) context.getBean("ossUploadService");
    }

    @Override
    public void run() {

        if(CollectionUtils.isEmpty(fileMoveList)) {
            return;
        }

        for(FileMoveDto fileMoveDto : fileMoveList){

            if(!isRunning() || Thread.currentThread().isInterrupted()){
                LOGGER.info("当前线程"+Thread.currentThread().getName()+"被中断,强制退出");
                return;
            }

            try {

                FileInfo fileInfo = ossUploadService.downloadFileAndSendFile2Oss(fileMoveDto);

                if(fileInfo.getCode() == -1){
                    updateErrorInfo(fileMoveDto);
                    LOGGER.error("迁移文件 id="+fileMoveDto.getId()+",到OSS失败：");
                    continue;
                }

                //更新原始表的信息
                updateOriginalFileInfo(fileMoveDto,fileInfo);

            } catch (Exception e){

                if(e instanceof MyBatisSystemException){
                    interupt();
                }

                updateErrorInfo(fileMoveDto);

                LOGGER.error("迁移文件 id="+fileMoveDto.getId()+",到OSS失败：",e);
            }
        }
    }

    public Boolean isRunning(){
        return isRunning.get();
    }

    public void interupt(){
        isRunning.compareAndSet(true,false);
    }

    protected abstract void updateErrorInfo(FileMoveDto fileMoveDto);

    protected abstract void updateOriginalFileInfo(FileMoveDto fileMoveDto, FileInfo fileInfo);
}
