<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="新增区域" scope="application"/>
<%@ include file="../../common/common.jsp" %>

<link rel="stylesheet" href="<%=basePath%>static/css/trader/iconfont.css" />
<script src="${pageContext.request.contextPath}/static/js/trader/add_region_rules.js?rnd=${resourceVersionKey}" charset="utf-8"></script>
<style type="text/css">
    .xx{
        margin-top:-13px;
    }
    .infor_namex{
        float: left;
        width: 80px;
        overflow: hidden;
        margin: 3px 10px 0px 81px;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    .bit-center{
        margin-left: 215px;
        margin-top: 75px;
    }
    .seacher-form{
        margin-left: 98px;
        margin-top: 36px;
    }
</style>
<span style="font-size: 20px !important;font-weight: bold">新增区域</span>
<div class="searchfunc seacher-form">
    <form method="post" id="search" action="/trader/customer/regionRulesList.do">
        <input type="hidden" id="publicCustomerRegionRulesId" value="${publicCustomerRegionRulesVo.publicCustomerRegionRulesId}"/>
        <input type="hidden" id="regionId" value="${publicCustomerRegionRulesVo.regionId}"/>
        <input type="hidden" id = "checkData" value=""/>
        <input type="hidden" id = "index0" value="1"/>
        <ul>
            <%--<li>
                <label class="infor_namex"><span class="font-red">*</span>销售:</label>
                <select class="input-middle f_left" name="userId" id="userId" onchange="getUserData()">
                    <option value="-1">请选择</option>
                    <c:forEach var="list" items="${userList}">
                        <option value="${list.userId}"
                                <c:if test="${publicCustomerRegionRulesVo.userId == list.userId}">selected="selected"</c:if> >${list.username}</option>
                    </c:forEach>
                </select>
            </li>--%>
            <li>
                <label class="infor_namex"><span class="font-red">*</span>归属销售：</label>
                <select class="input-middle f_left" name="userId" id="userId" onchange="getUserData()">
                    <option value="-1">请选择</option>
                    <c:if test="${not empty userList }">
                        <c:forEach items="${userList }" var="user">
                            <option value="${user.userId }"
                                    <c:if test="${publicCustomerRegionRulesVo.userId == user.userId}">selected="selected"</c:if>>${user.username }</option>
                        </c:forEach>
                    </c:if>
                </select>
            </li>
        </ul>

        <ul>
            <li>
                <div class="font-red f_left inputfloat mt3" style="padding-left: 80px;font-size: 15px; margin-bottom: 10px;display: none" id="u-message">请选择销售</div>
            </li>
        </ul>
        <ul>
            <li>
                <div class="font-grey6 f_left inputfloat mt3" style="padding-left: 80px;font-size: 15px;" id="user-message"><%--归属部门：${publicCustomerRegionRulesVo.orgName}&nbsp;&nbsp;主管：${publicCustomerRegionRulesVo.userBossName}--%></div>
            </li>
        </ul>
        <div style="display: inline; margin-top: -20px;">
            <label class="infor_namex"><span class="font-red">*</span>选择区域：</label>
            <div  style="float: left; margin-top:10px;">
                <div  class="addHtml" id="addHtml">
                    <div class=""  style="margin-top: -8px; float: left;" id="conadd1">
                        <span class="font-blue iconfont icon-jiahao cursor-pointer" style="margin-top: 3px;" onclick="con_add('new',0)" >新增区域</span>
                                    <%--<i class="bt-border-style bt-small border-blue mt8"--%>
                                    <%--      onclick="con_add('new',0)"><i></i></i>--%>
                    </div>
                </div>
                <div style="clear: both;"></div>
                <%-- <ul class="addHtml" id="addHtml">
                     <li>
                         <label class="infor_name">&lt;%&ndash;<span class="font-red">*</span>区域：&ndash;%&gt;</label>
                         <ul class="inputfloat f_left">

                             <li>
                                 <div class=""  style="margin-top: -8px;" id="conadd1">
                                             <span class="bt-border-style bt-small border-blue mt8"
                                                   onclick="con_add('new',0)">新增区域</span>
                                 </div>
                             </li>
                         </ul>
                     </li>
                 </ul>--%>
                <ul>
                    <li>
                        <div class="font-red f_left inputfloat mt3" style="font-size: 15px;" id="message"></div>
                    </li>
                </ul>
            </div>
            <div style="clear:both;"></div>
        </div>
        <div class="bit-center mt20">
            <button type="button" class="bt-bg-style bg-light-blue bt-small mr10" id='submit'>提交</button>
            <button type="button" class="bt-bg-style bg-light-red bt-small mr10" onclick="custom_close()" id='cancel0'>取消</button>
        </div>
    </form>
</div>
</body>
<%--<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/region/index.js?rnd=${resourceVersionKey}"></script>--%>

</html>
