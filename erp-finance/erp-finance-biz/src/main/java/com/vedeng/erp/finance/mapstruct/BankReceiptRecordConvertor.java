package com.vedeng.erp.finance.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.finance.domain.dto.BankAliasDto;
import com.vedeng.erp.finance.domain.dto.BankReceiptRecordDto;
import com.vedeng.erp.finance.domain.entity.BankAliasEntity;
import com.vedeng.erp.finance.domain.entity.BankReceiptRecordEntity;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,builder = @Builder(disableBuilder = true))
public interface BankReceiptRecordConvertor extends BaseMapStruct<BankReceiptRecordEntity, BankReceiptRecordDto> {
}
