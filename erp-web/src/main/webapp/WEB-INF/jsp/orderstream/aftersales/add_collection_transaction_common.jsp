<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<div class="parts">
    <c:set var="amountReceivedTable" value="0.00"></c:set>
    <c:choose>
        <c:when test="${not empty afterSalesVo.afterCapitalBillList}">
            <c:forEach items="${afterSalesVo.afterCapitalBillList}" var="ac">
                <c:if test="${ac.capitalBillDetail.bussinessType eq 526}">
                    <c:set value="${ac.amount + amountReceivedTable}" var="amountReceivedTable"></c:set>
                </c:if>
            </c:forEach>
        </c:when>

    </c:choose>
    <table class="table">
        <thead>
            <tr>
                <td colspan="11" style="clear: both;">
                    <div style="float: left;margin-left: 30px;background: orange;clip-path: circle(50%); height: 1em; width: 1em;margin-top: 2px;">
                        <div style="background: white; width: 0.1em;height: 0.5em; margin: 25% 50%;"></div>
                    </div>
                    <div style="float: left;margin-left: 20px;">实际应收金额：<c:if test="${empty afterSalesVo.serviceAmount}">0.00</c:if>
                        <fmt:formatNumber type="number" value="${afterSalesVo.serviceAmount}" pattern="0.00" maxFractionDigits="2" />
                    </div>
                    <div style="float: left;margin-left: 100px;">已收金额：
                        ${amountReceivedTable}
                    </div>
                    <div style="float: left;margin-left: 100px;">收款状态：
                        <c:choose>
                            <%--修复0.00的问题--%>
                            <c:when test="${(afterSalesVo.serviceAmount) le 0.00}">
                                无收款
                            </c:when>
                            <c:when test="${(afterSalesVo.serviceAmount) ne 0.00 && amountReceivedTable le 0.00}">
                                未收款
                            </c:when>
                            <c:when test="${(afterSalesVo.serviceAmount) ne 0.00 && amountReceivedTable gt 0.00 && amountReceivedTable lt afterSalesVo.serviceAmount}">
                                部分收款
                            </c:when>
                            <c:when test="${(afterSalesVo.serviceAmount) ne 0.00 && amountReceivedTable ge afterSalesVo.serviceAmount}">
                                全部收款
                            </c:when>
                        </c:choose>
                    </div>
                </td>
            </tr>
            <tr>
                <td>记账编号</td>
                <td>业务类型</td>
                <td>交易时间</td>
                <td>交易主体</td>
                <td>交易金额</td>
                <td>交易方式</td>
                <td>交易名称</td>
                <td>交易备注</td>
                <td>操作时间</td>
                <td>操作人</td>
                <td>操作</td>
            </tr>
        </thead>
        <tbody>
        <c:choose>
            <c:when test="${not empty afterSalesVo.afterCapitalBillList}">
                <c:forEach items="${afterSalesVo.afterCapitalBillList}" var="acb">
                    <c:if test="${acb.capitalBillDetail.bussinessType eq 526}">
                        <tr>
                            <td>${acb.capitalBillNo}</td>
                            <td>
                                <c:if test="${acb.capitalBillDetail.bussinessType eq 525}">订单付款</c:if>
                                <c:if test="${acb.capitalBillDetail.bussinessType eq 526}">订单收款</c:if>
                                <c:if test="${acb.capitalBillDetail.bussinessType eq 531}">退款</c:if>
                                <c:if test="${acb.capitalBillDetail.bussinessType eq 532}">资金转移</c:if>
                                <c:if test="${acb.capitalBillDetail.bussinessType eq 533}">信用还款</c:if>
                            </td>
                            <td>
                                <c:if test="${acb.traderTime != 0}">
                                    <date:date value="${acb.traderTime}" />
                                </c:if>
                            </td>
                            <td>
                                <c:if test="${acb.traderSubject == 1}">
                                    对公
                                </c:if>
                                <c:if test="${acb.traderSubject == 2}">
                                    对私
                                </c:if>
                            </td>
                            <td><fmt:formatNumber type="number" value="${acb.amount}" pattern="0.00" maxFractionDigits="2" />
                            </td>
                            <td>
                                <c:if test="${acb.traderMode eq 520}">支付宝</c:if>
                                <c:if test="${acb.traderMode eq 521}">银行</c:if>
                                <c:if test="${acb.traderMode eq 522}">微信</c:if>
                                <c:if test="${acb.traderMode eq 523}">现金</c:if>
                                <c:if test="${acb.traderMode eq 527}">信用支付</c:if>
                                <c:if test="${acb.traderMode eq 528}">余额支付</c:if>
                                <c:if test="${acb.traderMode eq 529}">退还信用</c:if>
                                <c:if test="${acb.traderMode eq 530}">退还余额</c:if>
                            </td>
                            <td>${acb.payer}</td>
                            <td>${acb.comments}</td>
                            <td>
                                <c:if test="${acb.addTime != 0}">
                                    <date:date value="${acb.addTime}" />
                                </c:if>
                            </td>
                            <td>
                                <c:choose>
                                    <c:when test="${empty acb.creatorName || acb.creatorName eq ''}">
                                        <c:forEach var="user" items="${afterSalesVo.userList}">
                                            <c:if test="${user.userId eq acb.creator}">${user.username}</c:if>
                                        </c:forEach>
                                    </c:when>
                                    <c:otherwise>
                                        ${acb.creatorName}
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td>
                                <c:choose>
                                    <c:when test="${(acb.traderType == 2 || acb.traderType == 5) && acb.bankBillId != 0}">
                                        <c:when test="${not empty acb.receiptUrl}">
                                            <span class="caozuo-blue" onclick="openFile('${acb.receiptUrl}')">回单</span>
                                        </c:when>
                                        <c:otherwise>
                                            <div class="caozuo">
                                                <span class="caozuo-blue addtitle"   tabTitle='{"num":"credentials${acb.bankBillId}", "link":"<%=basePath%>finance/capitalbill/credentials.do?bankBillId=${acb.bankBillId}","title":"电子回执单"}'>预览</span>
                                            </div>
                                        </c:otherwise>
                                    </c:when>
                                    <c:otherwise>
                                        <button type="button" class="bt-bg-style bg-light-grey bt-small">
                                            预览
                                        </button>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                        </tr>
                    </c:if>
                </c:forEach>
            </c:when>
            <c:otherwise>
                <tr>
                    <td colspan="11">暂无收款交易信息！</td>
                </tr>
            </c:otherwise>
        </c:choose>
        </tbody>
    </table>
</div>
