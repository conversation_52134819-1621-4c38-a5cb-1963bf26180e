package com.vedeng.erp.kingdee.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeePayExpensesCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeePayExpensesEntity;
import com.vedeng.erp.kingdee.dto.KingDeePayExpensesDto;
import com.vedeng.erp.kingdee.dto.result.KingDeePayCommonQueryResultDto;
import com.vedeng.erp.kingdee.dto.result.KingDeePayExpensesQueryResultDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeePayExpensesCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeePayExpensesConvertor;
import com.vedeng.erp.kingdee.repository.KingDeePayExpensesRepository;
import com.vedeng.erp.kingdee.service.KingDeePayExpensesApiService;
import com.vedeng.erp.kingdee.service.KingDeePayExpensesService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryExtParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryFilterDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/3/22 8:54
 **/
@Service
@Slf4j
public class KingDeePayExpensesServiceImpl extends KingDeeBaseServiceImpl<
        KingDeePayExpensesEntity,
        KingDeePayExpensesDto,
        KingDeePayExpensesCommand,
        KingDeePayExpensesRepository,
        KingDeePayExpensesConvertor,
        KingDeePayExpensesCommandConvertor>
        implements KingDeePayExpensesApiService, KingDeePayExpensesService {

    @Override
    public List<KingDeePayExpensesQueryResultDto> getKingDeePayExpenses(Integer invoiceId) {
        KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        queryParam.setFormId(KingDeeFormConstant.PAY_EXPENSES);
        List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        queryFilterDtos.add(KingDeeQueryFilterDto.builder()
                .fieldName("F_QZOK_BDDJTID")
                .value(invoiceId.toString())
                .build());

        queryFilterDtos.add(KingDeeQueryFilterDto.builder()
                .fieldName("fBusinessType")
                .value("FY").build());
        queryParam.setFilterString(queryFilterDtos);
        return kingDeeBaseApi.query(queryParam, KingDeePayExpensesQueryResultDto.class);
    }

    @Override
    public Boolean kingDeeIsExist(KingDeePayExpensesDto kingDeePayExpensesDto) {
        List<KingDeePayExpensesQueryResultDto> kingDeePayExpenses = getKingDeePayExpenses(Integer.valueOf(kingDeePayExpensesDto.getFQzokBddjtId()));
        return CollUtil.isNotEmpty(kingDeePayExpenses);
    }

    @Override
    public Boolean localIsExist(KingDeePayExpensesDto kingDeePayExpensesDto) {
        KingDeePayExpensesEntity query = new KingDeePayExpensesEntity();
        query.setFQzokBddjtId(kingDeePayExpensesDto.getFQzokBddjtId());
        query.setFBusinessType(kingDeePayExpensesDto.getFBusinessType());
        List<KingDeePayExpensesEntity> kingDeePayExpensesEntities = repository.queryByObject(query);
        return CollUtil.isNotEmpty(kingDeePayExpensesEntities);
    }
}
