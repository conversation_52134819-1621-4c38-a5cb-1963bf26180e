package com.vedeng.erp.statistic.mapper;

import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticAedNum;
import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticVdAmount;
import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticVdAmountExample;
import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticVdAmountKey;
import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticVdAmountWithBLOBs;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BroadcastStatisticVdAmountMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_VD_AMOUNT
     *
     * @mbg.generated
     */
    long countByExample(BroadcastStatisticVdAmountExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_VD_AMOUNT
     *
     * @mbg.generated
     */
    int deleteByExample(BroadcastStatisticVdAmountExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_VD_AMOUNT
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(BroadcastStatisticVdAmountKey key);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_VD_AMOUNT
     *
     * @mbg.generated
     */
    int insert(BroadcastStatisticVdAmountWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_VD_AMOUNT
     *
     * @mbg.generated
     */
    int insertSelective(BroadcastStatisticVdAmountWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_VD_AMOUNT
     *
     * @mbg.generated
     */
    List<BroadcastStatisticVdAmountWithBLOBs> selectByExampleWithBLOBs(BroadcastStatisticVdAmountExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_VD_AMOUNT
     *
     * @mbg.generated
     */
    List<BroadcastStatisticVdAmount> selectByExample(BroadcastStatisticVdAmountExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_VD_AMOUNT
     *
     * @mbg.generated
     */
    BroadcastStatisticVdAmountWithBLOBs selectByPrimaryKey(BroadcastStatisticVdAmountKey key);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_VD_AMOUNT
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") BroadcastStatisticVdAmountWithBLOBs record, @Param("example") BroadcastStatisticVdAmountExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_VD_AMOUNT
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("record") BroadcastStatisticVdAmountWithBLOBs record, @Param("example") BroadcastStatisticVdAmountExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_VD_AMOUNT
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") BroadcastStatisticVdAmount record, @Param("example") BroadcastStatisticVdAmountExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_VD_AMOUNT
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(BroadcastStatisticVdAmountWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_VD_AMOUNT
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(BroadcastStatisticVdAmountWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_VD_AMOUNT
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(BroadcastStatisticVdAmount record);
    
    /**
     * 批量插入月自有品牌出库金额
     * @param broadcastStatisticVdAmountList
     */
    void batchInsert(@Param("broadcastStatisticVdAmountList") List<BroadcastStatisticVdAmountWithBLOBs> broadcastStatisticVdAmountList);
}