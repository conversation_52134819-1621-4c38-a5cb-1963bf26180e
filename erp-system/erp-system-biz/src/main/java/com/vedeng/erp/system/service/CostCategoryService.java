package com.vedeng.erp.system.service;

import com.vedeng.erp.system.domain.dto.CostCategoryDto;
import com.vedeng.erp.system.domain.entity.SysCostCategoryKingDee;

import java.util.List;

/**
 *<AUTHOR>
 *@Description com.vedeng.erp.system.service.impl
 *@Date 2022/8/15 13:29
 */
public interface CostCategoryService {

        /**
         * 通过主键删除
         * @param costCategoryId 参数
         * @return int
         */
        int deleteByPrimaryKey(Integer costCategoryId);

        /**
         * 新增
         * @param record 参数
         * @return int
         */
        int insert(CostCategoryDto record);

        /**
         * 选择新增
         * @param record 参数
         * @return int
         */
        void insertSelective(CostCategoryDto record);

        /**
         * 通过主键查询
         * @param costCategoryId 参数
         * @return CostCategoryDto
         */
        CostCategoryDto selectByPrimaryKey(Integer costCategoryId);

        /**
         * 选择更新
         * @param record 参数
         * @return int
         */
        int updateByPrimaryKeySelective(CostCategoryDto record);

        /**
         * 全部更新
         * @param record 参数
         * @return int
         */
        int updateByPrimaryKey(CostCategoryDto record);

        /**
         * 校验费用名称重复
         * @param costCategoryDto 参数
         */
        void checkRepeat(CostCategoryDto costCategoryDto);

        /**
         * 通过编号或名称查找金蝶类别
         * @param category
         * @return
         */
        List<SysCostCategoryKingDee> selectKingDeeCategoryByNameOrId (String category);

        /**
         * 查找所有分类
         * @return
         */
        List<SysCostCategoryKingDee> getKingDeeCategory ();


        /**
         * 批量插入金蝶费用类别信息
         * @param sysCostCategoryKingDeeList
         * @return
         */
        int insertBatchKingDeeCategory(List<SysCostCategoryKingDee> sysCostCategoryKingDeeList);


        /**
         * 通过编号查询金蝶分类
         * @param categoryNo
         * @return
         */
        SysCostCategoryKingDee selectByKingDeeNo(String categoryNo);

        /**
         * 检验金蝶编号重复
         * @param list
         */
        void checkKingDeeRepeat(List<SysCostCategoryKingDee> list);
    }
