package com.vedeng.erp.buyorder.service.impl;

import com.vedeng.erp.buyorder.api.BuyorderInfoQueryApi;
import com.vedeng.erp.buyorder.dto.BuyOrderDto;
import com.vedeng.erp.buyorder.mapper.BuyorderMapper;
import com.vedeng.erp.buyorder.service.BuyorderInfoQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.buyorder.service.impl
 * @Date 2022/1/11 17:52
 */
@Service
public class BuyorderInfoQueryServiceImpl implements BuyorderInfoQueryService {

    @Autowired
    private BuyorderMapper buyorderMapper;

    @Override
    public BuyOrderDto queryInfoByNo(String buyorderNo) {
        BuyOrderDto buyOrderDto = buyorderMapper.queryInfoByNo(buyorderNo);
        return buyOrderDto;
    }

    @Override
    public BigDecimal getBuyOrderGoodsFairValue(Integer goodsId) {
        //  没有符合的采购单,就给0.00
        return Optional.ofNullable(buyorderMapper.getBuyOrderGoodsPriceByGoodsId(goodsId)).orElse(BigDecimal.ZERO);
    }

    @Override
    public List<Map<String, Object>> getCommunicateBuyOrderInfo(List<Integer> buyOrderIdList) {
        return buyorderMapper.getCommunicateBuyOrderInfo(buyOrderIdList);
    }
}
