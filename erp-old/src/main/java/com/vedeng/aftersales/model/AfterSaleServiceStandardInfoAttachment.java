package com.vedeng.aftersales.model;

public class AfterSaleServiceStandardInfoAttachment {
    private Long serviceStandardInfoAttachmentId;

    private Long serviceStandardInfoId;

    private String domain;

    private String uri;

    private String fileName;

    private Integer creator;

    private Integer updator;

    private String addTime;

    private String modTime;

    public Long getServiceStandardInfoAttachmentId() {
        return serviceStandardInfoAttachmentId;
    }

    public void setServiceStandardInfoAttachmentId(Long serviceStandardInfoAttachmentId) {
        this.serviceStandardInfoAttachmentId = serviceStandardInfoAttachmentId;
    }

    public Long getServiceStandardInfoId() {
        return serviceStandardInfoId;
    }

    public void setServiceStandardInfoId(Long serviceStandardInfoId) {
        this.serviceStandardInfoId = serviceStandardInfoId;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain == null ? null : domain.trim();
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri == null ? null : uri.trim();
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName == null ? null : fileName.trim();
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getAddTime() {
        return addTime;
    }

    public void setAddTime(String addTime) {
        this.addTime = addTime == null ? null : addTime.trim();
    }

    public String getModTime() {
        return modTime;
    }

    public void setModTime(String modTime) {
        this.modTime = modTime == null ? null : modTime.trim();
    }
}