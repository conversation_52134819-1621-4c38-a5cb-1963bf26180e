<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="myfn" uri="/WEB-INF/tld/myfn.tld" %>
<c:set var="title" value="" scope="application"/>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../../common/common.jsp" %>

<!DOCTYPE html>

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直发商品同行单数据</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/order/style.css">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/orderstream/buyorder-detail.css">
    <link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">

    <style>
        .block {
            height: 400px;
            background: #ccc;
            margin: 40px 0;
        }
    </style>

    <style>
        a, a:hover, a:focus {
            text-decoration: none;
            outline-style: none;
            color: #3384ef;
            cursor: pointer;
        }
    </style>
    <style>
        .layui-table, .layui-table-view {
            margin: 0px 0;
        }
        .table, .competitive-analysis table {
            border: 1px solid #ddd;
            margin-bottom: 15px;
            border-collapse: collapse;
            border-spacing: 0;
            width: 100%;
            margin: 0 auto 0px auto; !important;
            table-layout: fixed;
            color: #333;
        }
        i {
            display: inline-block;
            height: 12px;
            background: none;
            margin-bottom: -2px;
        }
        .layui-table-cell {
            height: auto ;
            white-space: normal;
        }
        th{
            text-align: center;
        }
        .layui-table th{
            font-size: 13px;
        }
    </style>

    <script>
        //json说明
        var data = [
            {
                label: 'XXX', //标签文字
                jump: '#XXX', //需要跳转的元素的ID
                tip: '', //文字下面的备注
                lock: '', //是否需要上面的锁和文字
                status: 3, //当前的状态，默认是0  0：未进行 1：已进行 2：正在进行 3：正在同步进行
                fail: 0 // 是否驳回，0：否 1：是
            }]
    </script>
</head>

<body>
<form action="" method="post" id="myform">


    <c:forEach items="${expressIds}" var="k" varStatus="index">
        <input type="hidden" name="expressIds" value="${k}"/>
    </c:forEach>

    <input type="hidden" id="buyOrderId" value="${bv.buyorderId}"/>

    <c:if test="${not empty bv.expressList}">
        <c:forEach items="${bv.expressList}" var="express" varStatus="index">
            <div class="layui-card" style="margin: 10px;">
                <div class="layui-card-header" style="background: #FFCC66">物流信息与同行单维护  <span style="float: right;"><a class=" layui-btn layui-btn-primary layui-border-blue layui-btn-xs " href="/order/newBuyorderPeerList/excelTemplateDownload.do?expressId=${express.expressId}&buyorderId=${bv.buyorderId}">下载模板</a><a class="layui-btn layui-btn-primary layui-border-blue layui-btn-xs copyRow" id="btn-${express.expressId}" lay-event="copy" onclick="uploadExcel(${express.expressId})" >导入数据</a>  <span  class=" layui-btn layui-btn-primary layui-border-blue layui-btn-xs  title-click addtitle" style="margin-top: 11px" tabtitle='{"num":"editPeerListView<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
							"link":"/order/newBuyorderPeerList/editPeerListView2.do?buyorderId=${bv.buyorderId}"}'>大数据量模板导入</span></span></div>
                <div class="<%--layui-card-body--%>">
                    <table class="table  table-style6" id="wulb">
                        <thead>
                        <tr>
                            <th class="wid7"></th>
                            <th>快递单号</th>
                            <th>快递公司</th>
                            <th>发货时间</th>
                            <%--<th class="wid8">运费</th>--%>
                            <th>商品</th>
                            <th>快递状态</th>
                            <th class="wid15">备注</th>

                        </tr>
                        </thead>
                        <tbody>

                        <tr>
                            <td><input type="checkbox" name="checkexpress" id="express_${index.count}" value="${express.expressId}" checked></td>
                            <td>${express.logisticsNo}</td>
                            <td>${express.logisticsName}</td>
                            <td> <date:date value="${express.deliveryTime}"/></td>
                            <%--<td>  <fmt:formatNumber type="number" value="${express.expressDetail.stream().map(a->a.amount).sum()}" pattern="0.00" maxFractionDigits="2"/></td>--%>
                            <td class="text-left">
                                <c:forEach items="${express.expressDetail}" var="expressDe">
                                    <div>
                                            ${expressDe.goodName}&nbsp;&nbsp;&nbsp;&nbsp;${expressDe.num}${expressDe.unitName}
                                    </div><br/>
                                </c:forEach>
                            </td>
                            <td>
                                <c:if test="${express.arrivalStatus == 2}">已签收</c:if>
                                <c:if test="${express.arrivalStatus == 0}">未签收</c:if>
                            </td>
                            <td>
                                    ${express.logisticsComments}
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <table class="layui-table<%--table  table-style6--%>" id = "table-${express.expressId}" lay-filter="test">
                            <%--<tr>
                                <td lay-data="{field:'goodsName',style:'font-size: 10px;' ,width:200,align:'center'}">产品名</td>
                                <td lay-data="{field:'sku',align:'center',width:100,style:'font-size: 10px;'}">订货号</td>
                                <td lay-data="{field:'model',align:'center',width:100,style:'font-size: 10px;'}">型号/规格</td>
                                <td lay-data="{field:'unitName',style:'font-size: 10px;',align:'center',width:60}">单位</td>
                                <td lay-data="{field:'manufacturerName',align:'center',style:'font-size: 10px;'}">品牌（生产企业）</td>
                                <td lay-data="{field:'productCompanyLicence',align:'center',style:'font-size: 10px;'}">许可证号</td>
                                <td lay-data="{field:'registrationNumber',align:'center',style:'font-size: 10px;'}">注册证号</td>
                                <td lay-data="{field:'',align:'center',width:180,templet:'#pcEdit'}">生产批号/序列号</td>
                                <td lay-data="{field:'',align:'center',templet:'#dnumEdit'}">收货数量/可收货数量</td>
                                <td lay-data="{field:'',align:'center',width:200,templet:'#productionTime'}">生产日期</th>
                                <td lay-data="{field:'',align:'center',width:200,templet:'#productionTime'}">失效日期</td>
                                <td lay-data="{field:'expressDetailId',align:'center',hide:true}"></td>
                                <td lay-data="{field:'spuType',align:'center',hide:true}"></td>
                                <td lay-data="{fixed: '', width:150, align:'center', toolbar: '#barDemo'}">操作</td>
                            </tr>--%>

                            <%-- <tr>
                                 <th>产品名</th>
                                 <th>订货号</th>
                                 <th>型号/规格</th>
                                 <th>单位</th>
                                 <th>品牌（生产企业）</th>
                                 <th>许可证号</th>
                                 <th>注册证号</th>
                                 <th>生产批号/序列号</th>
                                 <th>收货数量/可收货数量</th>
                                 <th>生产日期</th>
                                 <th>失效日期</th>
                                 <th>操作</th>
                             </tr>
                             </thead>
                             <tbody id="tbody-${express.expressId}">
                              <tr><th colspan="12">无物流信息</th></tr>
                             </tbody>
     --%>
                    </table>
                </div>
            </div>



            <br/>
            <br/>
        </c:forEach>
    </c:if>
    <div class="layui-card">
        <%--<div class="layui-card-header">卡片面板</div>--%>
        <div class="layui-card-body">
            说明<br>
            1、本页面只展示已确认收货的物流信息，若页面无任何物流信息，请先确认签收物流信息<br>
            2、当产品不是医疗器械时，收货数量为必填项，若sku的需要管理贝登追溯码则生产批号/序列号必填，若sku启用效期管理则生效日期、失效日期皆必填<br>
            3、当产品类型为“试剂"、“耗材时，生产批号/序列号、收货数量、生效日期、失效日期皆为必填项<br>
            4、当产品类型为设备"、“配件”时， 生产批号、收货数量为必填项，生效日期、失效日期可不填<br>
            5、每个SKU收货数量必须小于等于当前可收货数量<br>
            6、每一行物流数据默认显示维护时选中的商品数据，若导入模板数据，则会覆盖默认数据<br>
            7、若存在SKU有多个批次号，需多行录入的情况，点击复制即可新增一行<br>
        </div>
    </div>
    <c:if test="${not empty bv.expressList}">
        <div style="text-align: center" >
            <button type="button" class="layui-btn-normal layui-btn layui-btn-sm mybtn" id="sbu-mit" style="width: 90px;">提交</button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <button type="button" class="layui-btn-normal layui-btn layui-btn-sm " onclick="uploadImg(this)" style="width: 90px;">上传同行单</button>
        </div>
    </c:if>




</form>

<br>
<br>
<br>
<br>
<br>
<%--    <div id="file_upload_div" &lt;%&ndash;style="display: none"&ndash;%&gt; class="text-center" style="margin-top: 5px">
        <form class="layui-form">
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 100px;">导入数据:</label>
                    &lt;%&ndash;<input type="text" name="title" required  lay-verify="required" placeholder="请输入标题" autocomplete="off" class="layui-input">&ndash;%&gt;
                        <input type="text" id="chooseName" readonly="readonly" class="layui-input-inline" autocomplete="off"  style="height: 36px;">
                        <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" style="height: 32px;" id="test8">浏览</button>


            </div>
            <div class="layui-card-body" style="text-align: left;color: grey">
                1.只支持导入从系统中下载的Excel表格<br>
                2.上传文件不能超过20MB
            </div>
            <div class="layui-form-item">
                <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" >取消</button>
                <button type="button" class="layui-btn layui-btn-normal layui-btn-sm" id="test9">提交</button>
            </div>
            <div class="layui-btn-container">
                <div class="layui-form-item">

                </div>
            </div>
        </form>

    </div>--%>
</body>


</html>

<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>

<script type="text/html" id="pcEdit">
    {{#
    if(d.batchNumber==null){d.batchNumber=''}
    }}
    <input type="text" style="width: 150px;" name = "pc" maxlength="50" value="{{d.batchNumber}}" onchange="pcChange(this)" >
</script>

<script type="text/html" id="dnumEdit">
    {{#
    if(d.arrivalCount==null){d.arrivalCount=''}
    }}

    {{#if(d.medicalInstrumentCatalogIncluded!=1){ }}

        <input type="hidden" style="width: 45px;" name = "canReceive" class="layui-inline" value="{{d.oldNum}}"/><input type="text" style="width: 75px;text-align: center;" name = "canReceive" maxlength="50" value="{{d.dnum}}" placeholder="请输入整数" onchange="arrivalNum(this)" class="layui-inline">/{{d.dnum}}<input type="hidden" style="width: 45px;" name = "med" class="layui-inline" value="{{d.medicalInstrumentCatalogIncluded}}"/><input type="hidden" style="width: 45px;" name = "mvc" class="layui-inline" value="{{d.isManageVedengCode}}"/><input type="hidden" style="width: 45px;" name = "val" class="layui-inline" value="{{d.isEnableValidityPeriod}}"/>
    {{#}else{}}

    {{#
        if(d.spuType==316){
    }}
    <input type="hidden" style="width: 45px;" name = "canReceive" class="layui-inline" value="{{d.oldNum}}"/><input type="text" style="width: 75px;text-align: center;" name = "canReceive" placeholder="请输入整数" maxlength="50" value="1" disabled onchange="arrivalNum(this)" class="layui-inline">/{{d.dnum}}<input type="hidden" style="width: 45px;" name = "med" class="layui-inline" value="{{d.medicalInstrumentCatalogIncluded}}"/><input type="hidden" style="width: 45px;" name = "mvc" class="layui-inline" value="{{d.isManageVedengCode}}"/><input type="hidden" style="width: 45px;" name = "val" class="layui-inline" value="{{d.isEnableValidityPeriod}}"/>
    {{#
        }else{

    }}
        <input type="hidden" style="width: 45px;" name = "canReceive" class="layui-inline" value="{{d.oldNum}}"/><input type="text" style="width: 75px;text-align: center;" name = "canReceive" maxlength="50" value="{{d.arrivalCount}}" placeholder="请输入整数" onchange="arrivalNum(this)" class="layui-inline">/{{d.dnum}}<input type="hidden" style="width: 45px;" name = "med" class="layui-inline" value="{{d.medicalInstrumentCatalogIncluded}}"/><input type="hidden" style="width: 45px;" name = "mvc" class="layui-inline" value="{{d.isManageVedengCode}}"/><input type="hidden" style="width: 45px;" name = "val" class="layui-inline" value="{{d.isEnableValidityPeriod}}"/>
    {{#
        }
    }}
    {{#}}}
</script>
<script type="text/html" id="dnumEditExcel">
    {{#
    if(d.arrivalCount==null){d.arrivalCount=''}
    }}
    <input type="hidden" style="width: 45px;" name = "canReceive" class="layui-inline" value="{{d.oldNum}}"/><input type="text" style="width: 75px;text-align: center;" name = "canReceive" maxlength="50" placeholder="请输入整数" value="{{d.arrivalCount}}" onchange="arrivalNum(this)" class="layui-inline">/{{d.dnum}}<input type="hidden" style="width: 45px;" name = "med" class="layui-inline" value="{{d.medicalInstrumentCatalogIncluded}}"/><input type="hidden" style="width: 45px;" name = "mvc" class="layui-inline" value="{{d.isManageVedengCode}}"/><input type="hidden" style="width: 45px;" name = "val" class="layui-inline" value="{{d.isEnableValidityPeriod}}"/>
</script>
<script type="text/html" id="productionTime">
    <%--<input type="text" style="width: 100px;height: 28px;"  placeholder="请选择日期" class="layui-input layui-input-date takeDate"/>--%>
    {{#
    if(d.manufactureDateTime==null){d.manufactureDateTime=''}
    }}
    <input class="Wdate  takeDate" style="width: 100px;" type="text" name="checkTimes" id="checkTimes"
           autocomplete="off" placeholder="请选择日期" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})"
           value ="{{d.manufactureDateTime}}" format="yyyy-MM-dd"/>


</script>
<script type="text/html" id="vailTime">
    {{#
    if(d.invalidDateTime==null){d.invalidDateTime=''}
    }}
    <%--<input type="text" style="width: 100px;height: 28px;"  placeholder="请选择日期" class="layui-input layui-input-date takeDate"/>--%>
    <input class="Wdate  takeDate" style="width: 100px;" type="text" name="checkTimes" id="checkTimes"
           autocomplete="off" placeholder="请选择日期" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})"
           value ="{{d.invalidDateTime}}" format="yyyy-MM-dd"/>

</script>
<script type="text/html" id="barDemo">
    {{#
    if(d.spuType==316){
    }}
    <a class="layui-btn layui-btn-normal layui-btn-xs layui-btn-disabled" lay-event="copy" >复制</a>
    <a class="layui-btn  layui-btn-danger layui-btn-xs" lay-event="del"onclick="delFun(this)">删除</a>
    {{#
    }else{
    }}
    <a class="layui-btn layui-btn-normal layui-btn-xs copyRow" lay-event="copy" onclick="copyFun(this)">复制</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"onclick="delFun(this)">删除</a>
    {{# }}}
</script>

<script type="text/html" id="barDemoExcel">
    <a class="layui-btn layui-btn-normal layui-btn-xs copyRow" lay-event="copy" onclick="copyFun(this)">复制</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"onclick="delFun(this)">删除</a>
</script>





<script type="text/javascript" src='${pageContext.request.contextPath}/static/js/jquery.min.js'></script>
<script type="text/javascript" src='${pageContext.request.contextPath}/static/js/orderstream/index.js?rnd=${resourceVersionKey}'></script>

<script type="text/javascript"
        src='${pageContext.request.contextPath}/static/js/order/saleorder/saleorder_common.js?rnd=${resourceVersionKey}'></script>

<script>


    layui.use('form', function(){
        var form = layui.form;

        //监听提交
    });

    layui.use(['table','laydate'], function(){

        $("input[name=expressIds]").each(function (){
            var value = $(this).val();
            var table = layui.table;
            table.render({
                elem: '#table-'+value //指定原始表格元素选择器（推荐id选择器）
                ,method: 'post'
                ,url: '/order/newBuyorderPeerList/getNeedEditBuyGoods.do?expressId='+value
                // ,where :{expressId:}
                ,cols: [[
                    {field:'goodsName',style:'font-size: 10px;' ,width:200,align:'center',title:'产品名',unresize:true},
                    {field:'sku',align:'center',width:100,style:'font-size: 10px;',title:'订货号',unresize:true},
                    {field:'model',align:'center',width:100,style:'font-size: 10px;',title:'型号/规格',unresize:true},
                    {field:'unitName',style:'font-size: 10px;',align:'center',width:60,title:'单位',unresize:true},
                    {field:'manufacturerName',align:'center',width:150,style:'font-size: 10px;',title:'品牌（生产企业）',unresize:true},
                    {field:'productCompanyLicence',align:'center',style:'font-size: 10px;',title:'许可证号',unresize:true},
                    {field:'registrationNumber',align:'center',style:'font-size: 10px;',title:'注册证号',unresize:true},
                    {field:'',align:'center',width:180,templet:'#pcEdit',title:'生产批号/序列号',unresize:true},
                    {field:'',align:'center',width:180,templet:'#dnumEdit',title:'收货数量/可收货数量',unresize:true},
                    {field:'',align:'center',width:130,templet:'#productionTime',title:'生产日期',unresize:true},
                    {field:'',align:'center',width:130,templet:'#vailTime',title:'失效日期',unresize:true},
                    {field:'expressDetailId',align:'center',hide:true,unresize:true},
                    {field:'spuType',align:'center',hide:true,unresize:true},
                    {fixed: '', width:150, align:'center', toolbar: '#barDemo',title:'操作',unresize:true}
                ]] //设置表头
                ,text:{none:'此物流信息下无还需维护同行单数据'}
                ,done: function (res,curr,count){
                    if (count == 0) {
                        $("#btn-"+value).addClass("layui-btn-disabled").attr("disabled",true).removeAttr('onclick');
                    }
                }
            });
        })
        /*table.on('tool(test)', function(obj){ //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
            var data = obj.data; //获得当前行数据
            console.log(obj)
            var layEvent = obj.event; //获得 lay-event 对应的值（也可以是表头的 event 参数对应的值）
            var tr = obj.tr; //获得当前行 tr 的 DOM 对象（如果有的话）
            console.log(tr)
            // if (layEvent === 'myTime') {
            //     var field = $(this).data('field');
            //     layui.laydate.render({
            //         elem: this.firstChild
            //         ,type: 'datetime'
            //         , show: true //直接显示
            //         , closeStop: this
            //         , done: function (value, date) {
            //             data[field] = value;
            //             obj.update(data);
            //         }
            //     });
            // }

            if(layEvent === 'copys'){
                layer.confirm('真的复制当前行吗', function(index){
                // obj.del(); //删除对应行（tr）的DOM结构，并更新缓存
                    console.log(tr);
                    var str = $(tr).clone();
                    $(str).find(".takeDate").each(function (){
                        keyValue =keyValue+1;
                        $(this).attr("lay-key",keyValue)
                        console.log(this)
                    })
                    console.log(str)
                    tr.after(str);
                    // $(obj.parentNode.parentNode.parentNode.).find(".")
                    /!* var parentNode = obj.parentNode.parentNode.parentNode;
                     debugger
                     var find = $(parentNode).find("tr:last");
                     console.log(parentNode)
                     var arr = Array.prototype.slice.call(parentNode);
                     $(parentNode).find("tr:last")
                     $(parentNode).find("tr:last").after(str);*!/
                    setTimeout(function(){
                        $(str).find(".takeDate").each(function (){
                            console.log(11221);
                            layui.laydate.render({
                                elem: this
                                ,type: 'datetime'
                                ,closeStop: this
                                ,zIndex: 99999999
                            });
                        })
                    },2000)
                layer.close(index);
                //向服务端发送删除指令
            });
            } else if(layEvent === 'dels'){ //删除
                layer.confirm('真的删除行吗', function(index){
                    obj.del(); //删除对应行（tr）的DOM结构，并更新缓存
                    layer.close(index);
                    //向服务端发送删除指令
                });
            } else if(layEvent === 'edit'){ //编辑
                //do something

                //同步更新缓存对应的值
                obj.update({
                    username: '123'
                    ,title: 'xxx'
                });
            } else if(layEvent === 'LAYTABLE_TIPS'){
                layer.alert('Hi，头部工具栏扩展的右侧图标。');
            }
        });*/


    });

    layui.use('element', function () {
        var $ = layui.jquery
            , element = layui.element; //Tab的切换功能，切换事件监听等，需要依赖element模块

        //触发事件
        var active = {
            tabChange: function () {
                //切换到指定Tab项
                element.tabChange('demo', '22'); //切换到：用户管理
            }
        };

        $('.site-demo-active').on('click', function () {
            var othis = $(this), type = othis.data('type');
            active[type] ? active[type].call(this, othis) : '';
        });

        //Hash地址的定位
        var layid = location.hash.replace(/^#test=/, '');
        element.tabChange('test', layid);

        element.on('tab(test)', function (elem) {
            location.hash = 'test=' + $(this).attr('lay-id');
        });

    });


</script>
<script type="text/javascript" src='${pageContext.request.contextPath}/static/js/orderstream/buyorder/buyorder_tx_detail.js?rnd=${resourceVersionKey}'></script>

