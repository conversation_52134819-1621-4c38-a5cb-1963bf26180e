package com.wms.service.other;

import com.vedeng.common.constant.ErpConst;
import com.vedeng.system.dao.SysOptionDefinitionMapper;
import com.vedeng.system.service.SysOptionDefinitionService;
import com.wms.constant.WMSContant;
import com.wms.dto.WmsRequest;
import com.wms.model.po.WmsQLA;
import com.wms.service.AbstractWmsInterface;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * 商品资质下传
 */
@Service
public class PutQLAInterface extends AbstractWmsInterface {


    @Value("${api_http}")
    protected String api_http;

    @Override
    protected void setMethodAndMessageId(WmsRequest wmsRequest) {
        wmsRequest.setMethod(WMSContant.PUT_QLA);
        wmsRequest.setMessageId(WMSContant.PUT_QLA);
    }

    @Override
    protected JSONObject getXmlDate(Object... param) {

        if(param == null ||param.length == 0){
            return null;
        }

        JSONArray headArray = new JSONArray();

        Arrays.stream(param).forEach(paramItem -> {

            JSONObject headItem = parseParamToJsonObject(paramItem);

            headArray.add(headItem);
        });

        JSONObject headObject = new JSONObject();
        headObject.put("header",headArray);
        return headObject;
    }

    private JSONObject parseParamToJsonObject(Object param) {

        WmsQLA wmsQLA = (WmsQLA)param;

        JSONObject headItem = new JSONObject();
        headItem.put("CustomerID","VEDENG");
        headItem.put("SKU",wmsQLA.getSkuNO());
        //序号
        headItem.put("FileSEQ",wmsQLA.getAttachmentId());

        //产品注册证、生产企业营业执照、医疗器械生产许可证等
        String originalFileName = getOriginalFileName(wmsQLA);
        headItem.put("OriginalFileName",originalFileName);
        //证照号码 ERP_LV_2020_42 暂时 非必填 先默认
        headItem.put("LicenseNo","VEDENG");
        //图片名称
        headItem.put("ShortFileName", StringUtils.isBlank(wmsQLA.getName()) ? "VEDENG" : wmsQLA.getName());
        //图片格式
        int index = wmsQLA.getUri().indexOf(".");
        headItem.put("FileType",wmsQLA.getUri().substring(index+1));
        //图片路径
        headItem.put("FileDir",api_http+wmsQLA.getDomain()+wmsQLA.getUri());

        return headItem;
    }

    private String getOriginalFileName(WmsQLA wmsQLA) {

        String originalFileName = "VEDENG";

        if(wmsQLA.getAttachmentFunction() == null){
            return originalFileName;
        }

        if(wmsQLA.getAttachmentFunction() == 975){
            originalFileName= ErpConst.QLA_ATTACHMENT_TYPE.CPZCZ;
        }else if (wmsQLA.getAttachmentFunction() == 1306){
            originalFileName=ErpConst.QLA_ATTACHMENT_TYPE.YLQXSCXKZ;
        }else if (wmsQLA.getAttachmentFunction() == 1307){
            originalFileName=ErpConst.QLA_ATTACHMENT_TYPE.SCQYYYZZ;
        }else if(wmsQLA.getAttachmentFunction() == 1301){
            originalFileName= ErpConst.QLA_ATTACHMENT_TYPE.CPZCZ_DZ;
        }else if(wmsQLA.getAttachmentFunction() == 1302){
            originalFileName= ErpConst.QLA_ATTACHMENT_TYPE.SCQYYYZZ_DZ;
        }else if(wmsQLA.getAttachmentFunction() == 1303){
            originalFileName= ErpConst.QLA_ATTACHMENT_TYPE.YLQXSCXKZ_DZ;
        }else if(wmsQLA.getAttachmentFunction() == 1305){
            originalFileName= ErpConst.QLA_ATTACHMENT_TYPE.SCQYSCDJB;
        }else if(wmsQLA.getAttachmentFunction() == 1304){
            originalFileName= ErpConst.QLA_ATTACHMENT_TYPE.SCQYSCDJB_DZ;
        }

        return originalFileName;
    }

    @Override
    protected <T> T parseResponseDate(JSONObject returnObject) {
        return null;
    }
}
