package com.vedeng.erp.finance.service.impl.check.builder;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.utils.ErpSpringBeanUtil;
import com.vedeng.erp.finance.service.AbstractCheckHandler;
import com.vedeng.erp.finance.service.impl.check.handler.OrderCollectionCheckHandler;
import com.vedeng.erp.finance.enums.CheckChainEnum;
import com.vedeng.erp.finance.enums.CheckHandlerEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
@Slf4j
public class CheckChainBuilder {

    // Apollo责任链条配置
    @Value("${invoice.apply.sales.check}")
    private String invoiceApplySalesCheck;
    @Value("${invoice.apply.after.check}")
    private String invoiceApplyAfterCheck;
    @Value("${invoice.open.sales.check}")
    private String invoiceOpenSalesCheck;
    @Value("${invoice.open.after.check}")
    private String invoiceOpenAfterCheck;

    public AbstractCheckHandler buildChain(CheckChainEnum checkChainEnum) {
        log.info("开始构建责任链条，checkChainEnum：{}", JSON.toJSONString(checkChainEnum));
        String apolloConfig = getApolloConfig(checkChainEnum);
        if(StrUtil.isBlank(apolloConfig)) {
            return null;
        }
        log.info("Apollo责任链条配置：{}", apolloConfig);
        AbstractCheckHandler dummyHeadHandler = new OrderCollectionCheckHandler();
        AbstractCheckHandler preHandler = dummyHeadHandler;
        String[] handlerTypeList = apolloConfig.split(",");
        for(String handlerType : handlerTypeList) {
            AbstractCheckHandler handler = ErpSpringBeanUtil.getBean(CheckHandlerEnum.valueOf(handlerType).getClazz());
            if (Objects.isNull(handler)) {
                throw new ServiceException("根据映射未匹配具体实现类，请检查Apollo与CheckHandlerEnum配置");
            }
            preHandler.nextHandler = handler;
            preHandler = handler;
        }
        preHandler.nextHandler = null;
        log.info("责任链条构建完成, AbstractCheckHandler：{}", JSON.toJSONString(dummyHeadHandler.nextHandler));
        return dummyHeadHandler.nextHandler;
    }

    private String getApolloConfig(CheckChainEnum checkChainEnum) {
        switch (checkChainEnum) {
            case INVOICE_APPLY_SALES:
                return invoiceApplySalesCheck;
            case INVOICE_APPLY_AFTER:
                return invoiceApplyAfterCheck;
            case INVOICE_OPEN_SALES:
                return invoiceOpenSalesCheck;
            case INVOICE_OPEN_AFTER:
                return invoiceOpenAfterCheck;
            default:
                return null;
        }
    }
}
