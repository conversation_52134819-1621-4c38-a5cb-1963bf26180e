package com.vedeng.order.dao;

import com.vedeng.authorization.model.User;
import com.vedeng.order.model.OrderAssistantJProductPositionDo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrderAssistantJProductPositionMapper {
    int deleteByPrimaryKey(Integer orderAssistantJProductPositionId);

    int insert(OrderAssistantJProductPositionDo record);

    int insertSelective(OrderAssistantJProductPositionDo record);

    OrderAssistantJProductPositionDo selectByPrimaryKey(Integer orderAssistantJProductPositionId);

    int updateByPrimaryKeySelective(OrderAssistantJProductPositionDo record);

    int updateByPrimaryKey(OrderAssistantJProductPositionDo record);

    List<User> getProductUserByOrderAssistantId(@Param("orderAssitantUserId") Integer orderAssitantUserId, @Param("positionLevel") Integer positionLevel);

    /**
     * 获取所有订单助理绑定的产品经理
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/4/14 8:56.
     * @author: Randy.Xu.
     * @param orderAsssistantUserId
     * @return: java.util.List<com.vedeng.order.model.OrderAssistantJProductPositionDo>.
     * @throws:  .
     */
    List<OrderAssistantJProductPositionDo> getAllProductUserByType(@Param("orderAsssistantUserId") Integer orderAsssistantUserId, @Param("positionLevel") Integer positionLevel);

    /**
     * 批量插入
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/4/16 9:40.
     * @author: Randy.Xu.
     * @param saveProductUserList
     * @return: void.
     * @throws:  .
     */
    void batchInsert(@Param("saveProductUserList") List<OrderAssistantJProductPositionDo> saveProductUserList);

    /**
     * 批量删除
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/4/16 9:41.
     * @author: Randy.Xu.
     * @param orderAsssistantUserId
     * @return: void.
     * @throws:  .
     */
    void batchDeleteByOrderAsssistantUserId(Integer orderAsssistantUserId);
}