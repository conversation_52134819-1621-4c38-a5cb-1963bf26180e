package com.vedeng.erp.finance.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 流水凭证表
 */
@Getter
@Setter
public class TransactionVoucherEntity extends BaseEntity {
    /**
     * 主键
     */
    private Long transactionVoucherId;

    /**
     * 单据编号
     */
    private String billNo;

    /**
     * 凭证号
     */
    private String voucherNo;

    /**
     * 交易类型(1-收款 2-付款 3-付款退款 4-收款退款 5-应付余额调整 6-应收余额调整)
     */
    private Integer transactionType;

    /**
     * 是否删除 0 否 1是
     */
    private Integer isDelete;

    /**
     * 记账日期（yyyy-M）
     */
    private String voucherDate;

    /**
     * 关联银行流水或资金流水的主键id
     */
    private Integer relateId;

    /**
     * 来源（1-银行 2-余额）
     */
    private Integer source;

    /**
     * 凭证URL
     */
    private String voucherUrl;
}