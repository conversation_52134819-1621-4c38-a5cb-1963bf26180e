package com.newtask.trader;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.StringUtil;
import com.vedeng.erp.trader.domain.entity.DwhTraderTagChangeErpEntity;
import com.vedeng.erp.trader.dto.TraderCustomerTagChangeRecordDto;
import com.vedeng.erp.trader.mapper.DwhTraderTagChangeErpMapper;
import com.vedeng.erp.trader.service.DwhTraderTagChangeErpService;
import com.vedeng.erp.trader.service.TraderCustomerTagChangeRecordService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description 大数据客户标签更新记录
 * <AUTHOR>
 * @Date 2023/8/19 10:28
 */
@Component
@JobHandler(value = "DwhBusinessCustomerLabelUpdateTask")
@Slf4j
public class DwhBusinessCustomerLabelUpdateTask extends AbstractJobHandler {

    @Autowired
    private TraderCustomerTagChangeRecordService traderCustomerTagChangeRecordService;
    @Resource
    private DwhTraderTagChangeErpMapper dwhTraderTagChangeErpMapper;
    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        log.info("===================大数据客户标签更新记录开始===================");
        int pageSize = 500;
        if (StringUtil.isNotBlank(s) && StringUtil.isNumeric(s)) {
            pageSize = Integer.parseInt(s);
        }
        int pageNum = 1;
        //获取昨天的时间范围dateTime
        LocalDate yesterday = LocalDate.now().minusDays(1);
        LocalDateTime startOfYesterday = yesterday.atTime(LocalTime.MIN);
        LocalDateTime endOfYesterday = yesterday.atTime(LocalTime.MAX);
        while (true) {
            log.info("分页查询进度 {},{}", pageSize, pageNum);
            PageHelper.startPage(pageNum, pageSize);
            List<DwhTraderTagChangeErpEntity> dwhTraderTagChangeErpEntities = dwhTraderTagChangeErpMapper.selectByOperateTime(startOfYesterday, endOfYesterday);
            List<TraderCustomerTagChangeRecordDto> traderCustomerTagChangeRecordEntities = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(dwhTraderTagChangeErpEntities)) {
                for (DwhTraderTagChangeErpEntity dwhTraderTagChangeErpEntity : dwhTraderTagChangeErpEntities) {
                    TraderCustomerTagChangeRecordDto traderCustomerTagChangeRecordEntity = new TraderCustomerTagChangeRecordDto();
                    traderCustomerTagChangeRecordEntity.setTraderCustomerId(dwhTraderTagChangeErpEntity.getTraderCustomerId());
                    traderCustomerTagChangeRecordEntity.setTagChangeLog(dwhTraderTagChangeErpEntity.getTagChangeLog());
                    traderCustomerTagChangeRecordEntity.setOperTime(dwhTraderTagChangeErpEntity.getOperTime());
                    traderCustomerTagChangeRecordEntity.setTagName(dwhTraderTagChangeErpEntity.getTagName());
                    traderCustomerTagChangeRecordEntity.setOldTagLabel(dwhTraderTagChangeErpEntity.getOldTagLabel());
                    traderCustomerTagChangeRecordEntity.setNewTagLabel(dwhTraderTagChangeErpEntity.getNewTagLabel());
                    traderCustomerTagChangeRecordEntity.setSource(dwhTraderTagChangeErpEntity.getSource());
                    traderCustomerTagChangeRecordEntity.setOperType(dwhTraderTagChangeErpEntity.getOperType());
                    traderCustomerTagChangeRecordEntity.setTagModelName(dwhTraderTagChangeErpEntity.getTagModelName());
                    traderCustomerTagChangeRecordEntity.setTagChangeLog(dwhTraderTagChangeErpEntity.getTagChangeLog());
                    traderCustomerTagChangeRecordEntity.setTraderId(dwhTraderTagChangeErpEntity.getTraderId());
                    //打印日志
                    log.info("traderCustomerTagChangeRecordEntity:{}", JSON.toJSONString(traderCustomerTagChangeRecordEntity));
                    traderCustomerTagChangeRecordEntities.add(traderCustomerTagChangeRecordEntity);
                }
                traderCustomerTagChangeRecordService.batchInsertTraderCustomerTagChangeRecordEntity(traderCustomerTagChangeRecordEntities);
            }
            // 获取分页信息
            PageInfo<DwhTraderTagChangeErpEntity> pageInfo = new PageInfo<>(dwhTraderTagChangeErpEntities);
            // 判断是否还有下一页
            if (!pageInfo.isHasNextPage()) {
                break;
            }
            // 设置下一页页码
            pageNum = pageInfo.getNextPage();

            log.info("分页查询进度 {},{}", pageSize, pageNum);
        }
        log.info("===================大数据客户标签更新记录结束===================");
        return SUCCESS;
    }
}
