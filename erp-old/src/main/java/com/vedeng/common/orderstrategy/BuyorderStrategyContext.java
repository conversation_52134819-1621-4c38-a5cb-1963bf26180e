package com.vedeng.common.orderstrategy;

import com.vedeng.common.constant.OrderDataUpdateConstant;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.Buyorder;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 *  策略上下文对象
 * <AUTHOR>
 * @date $
 */
@Component
public class BuyorderStrategyContext {
    Logger logger = LoggerFactory.getLogger(BuyOrderAmountStrategy.class);

    @Autowired
    private BuyOrderAmountStrategy buyOrderAmountStrategy;

    @Resource
    private BuyorderMapper buyorderMapper;

    public static final Integer BUYORDER_AMOUNT_STRATEGY = 3;

    public List<OrderStrategy> add(Integer businessType, String operateType){
        List<OrderStrategy> result = new ArrayList<>();
         if(BUYORDER_AMOUNT_STRATEGY == businessType){
             logger.info("BuyorderStrategyContext策略：{}",businessType);
            switch (operateType){
                case OrderDataUpdateConstant.BUY_ORDER_VAILD:
                case OrderDataUpdateConstant.BUY_ORDER_PAY:
                case OrderDataUpdateConstant.BUY_ORDER_END:
                case OrderDataUpdateConstant.BUY_ORDER_CLOSE:
                case OrderDataUpdateConstant.BUY_ORDER_INVOICE:
                case OrderDataUpdateConstant.BUY_ORDER_CONFIRM:
                case OrderDataUpdateConstant.BUY_ORDER_AFTERORDER_OPY:
                    result.add(buyOrderAmountStrategy);
                    break;
            }
        }
         return result;
    }


    public void executeAll(List<OrderStrategy> list,Integer orderId){
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        Buyorder buyorder = buyorderMapper.selectByPrimaryKey(orderId);
        if(buyorder == null){
            return;
        }
        list.forEach(item ->{
            item.execute(buyorder);
        });
    }

}
