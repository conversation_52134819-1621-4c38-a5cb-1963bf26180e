package com.vedeng.erp.finance.service;

import com.vedeng.erp.finance.dto.InvoiceDto;

import java.util.List;

public interface InvoiceService {

    /**
     * 新增发票
     *
     * @param invoiceDto
     */
    void createInvoice(InvoiceDto invoiceDto);


    /**
     * 根据开票申请id获取发票
     *
     * @param invoiceApplyId 发票申请id
     * @return List<InvoiceDto>  invoiceDto
     */
    List<InvoiceDto> findByInvoiceApply(Integer invoiceApplyId);

    /**
     * 根据发票号获取数电票信息
     * @param invoiceNo
     * @return
     */
    InvoiceDto findByInvoiceNo(String invoiceNo);


    /**
     * 获取数电发票类型的红票根据发票号
     *
     * @param InvoiceNo 发票号
     * @return 发票
     */
    InvoiceDto getRedInvoice(String InvoiceNo);


}
