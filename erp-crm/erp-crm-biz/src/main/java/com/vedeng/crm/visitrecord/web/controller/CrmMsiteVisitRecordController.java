package com.vedeng.crm.visitrecord.web.controller;

import com.vedeng.common.core.annotation.MenuDesc;
import com.vedeng.common.core.base.BaseController;
import com.vedeng.common.core.base.ExceptionController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/2/27
 */
@ExceptionController
@Controller
@RequestMapping("/crm/visitRecord/m")
@Slf4j
public class CrmMsiteVisitRecordController extends BaseController {


    /**
     * 商机列表
     *
     * @return 页面
     */
//    @MenuDesc(menuValue = "C02",menuDesc = "拜访计划列表")
    @RequestMapping(value = "/index")
    public String index(HttpServletRequest request) {
        return "vue/view/crm/m/visitRecord/index";
    }

    /**
     * 打卡页
     * @param request
     * @return
     */
    @RequestMapping(value = "/card")
    public String card(HttpServletRequest request) {
        return "vue/view/crm/m/visitRecord/card";
    }


    /**
     * 新增拜访计划
     * @return 页面
     */
//    @MenuDesc(menuValue = "C0201,C0102",menuDesc = "新增拜访计划")
    @RequestMapping(value = "/add")
    public ModelAndView add(@RequestParam(value = "visitRecordId",required = false) Integer visitRecordId) {
        ModelAndView view = new ModelAndView("vue/view/crm/m/visitRecord/add");
//        if(leadsid!=null && leadsid>0){
//            boolean checkfollow = followUpRecordService.checkHasFollowUp(leadsid);
//            if(checkfollow){
//                view.addObject("followUp",checkfollow?"Y":"N");
//            }
//        }


        return view;
    }


    /**
     * 编辑拜访计划
     * @return 页面
     */
    @MenuDesc(menuValue = "C0202",menuDesc = "编辑拜访计划")
    @RequestMapping(value = "/edit")
    public ModelAndView edit(@RequestParam(value = "leadsid",required = false) Integer leadsid) {
        ModelAndView view = new ModelAndView("vue/view/crm/m/visitRecord/add");
//        if(leadsid!=null && leadsid>0){
//            boolean checkfollow = followUpRecordService.checkHasFollowUp(leadsid);
//            if(checkfollow){
//                view.addObject("followUp",checkfollow?"Y":"N");
//            }
//        }


        return view;
    }

    /**
     * 拜访计划详情
     *
     * @return 页面
     */
    @RequestMapping(value = "/detail")
    public ModelAndView detail(@RequestParam Integer id) {
        ModelAndView mv = new ModelAndView();
        mv.setViewName("/vue/view/crm/m/visitRecord/detail");
        return mv;
    }



    /**
     * 拜访计划详情
     *
     * @return 页面
     */
    @RequestMapping(value = "/createBusinessChance")
    public ModelAndView createBusinessChance(@RequestParam Integer visitRecordId) {
        ModelAndView mv = new ModelAndView();
        mv.setViewName("/vue/view/crm/m/visitRecord/createBusinessChance");
        return mv;
    }

    /**
     * 添加拜访记录
     * @param visitRecordId
     * @return
     */
    @RequestMapping(value = "/addVisit")
    public ModelAndView addVisit(@RequestParam Integer visitRecordId) {
        ModelAndView mv = new ModelAndView();
        mv.setViewName("/vue/view/crm/m/visitRecord/addVisit");
        return mv;
    }





}
