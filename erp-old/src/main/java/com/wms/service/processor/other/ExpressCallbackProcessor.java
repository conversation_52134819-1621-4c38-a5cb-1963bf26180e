package com.wms.service.processor.other;

import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.service.BaseService;
import com.vedeng.common.trace.data.TrackParamsData;
import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.common.trace.track.TrackStrategy;
import com.vedeng.common.trace.track.factory.TrackStrategyFactory;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.logistics.dao.ExpressMapper;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.ExpressDetail;
import com.vedeng.logistics.model.Logistics;
import com.vedeng.logistics.service.ExpressService;
import com.vedeng.logistics.service.LogisticsService;
import com.vedeng.order.model.BuyorderGoods;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.order.service.BuyorderService;
import com.vedeng.order.service.SaleorderService;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.dao.WmsOutputOrderGoodsMapper;
import com.wms.dao.WmsOutputOrderMapper;
import com.wms.dto.ExpressDetailDto;
import com.wms.dto.ExpressHeaderDto;
import com.wms.model.po.WmsOutputOrder;
import com.wms.model.po.WmsOutputOrderGoods;
import com.wms.service.util.WmsCommonUtil;
import com.wms.service.processor.AbstractWMSCalllBackProcessor;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 出库包裹回传处理器
 */
@Service
public class ExpressCallbackProcessor extends AbstractWMSCalllBackProcessor<ExpressHeaderDto> {

    private static final Logger logger = LoggerFactory.getLogger(ExpressCallbackProcessor.class);

    @Autowired
    private ExpressService expressService;

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private SaleorderService saleorderService;


    @Autowired
    private AfterSalesService afterSalesService;

    @Autowired
    private WmsOutputOrderMapper wmsOutputOrderMapper;

    @Autowired
    private WmsOutputOrderGoodsMapper wmsOutputOrderGoodsMapper;

    @Autowired
    private BuyorderService buyorderService;

    @Resource
    private ExpressMapper expressMapper;

    @Resource
    private BaseService baseService;

    @Override
    protected boolean needIdempotentValidator() {
        return true;
    }

    @Override
    protected String getBusinessKey(ExpressHeaderDto expressHeaderDto) {
        return "express" + expressHeaderDto.getOrderNo();
    }

    @Override
    protected void commonValidator(ExpressHeaderDto expressHeaderDto) throws Exception {
        if (expressHeaderDto == null || CollectionUtils.isEmpty(expressHeaderDto.getDetails())) {
            logger.error("包裹回传单信息异常:{}" , expressHeaderDto.toString());
            throw new Exception("包裹回传单信息异常" + expressHeaderDto.toString());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    protected void doDealWithRequest(ExpressHeaderDto expressHeaderDto) throws Exception {
        logger.info("包裹信息回传开始 expressHeaderDto:{}" , expressHeaderDto.toString());
        expressHeaderDto.getDetails().forEach(expressDetailDto -> {
            //公众号推送信息map
            Map<String,Object> sMap = new HashMap<>();
            Express express = new Express();
            express.setLogisticsNo(expressDetailDto.getDeliveryNo());
            express.setRealWeight(expressDetailDto.getDeliveryWeight());
            sMap.put("logisticNo",expressDetailDto.getDeliveryNo());
            if (CollectionUtils.isEmpty(expressDetailDto.getPackItem())) {
                logger.error("包裹信息不全 expressDetailDto:{}" , expressDetailDto.toString());
                throw new RuntimeException("包裹信息不全 expressDetailDto:{}" + expressDetailDto.toString());
            }
            Integer goodsNum = expressDetailDto.getPackItem().size();
            //件数
            express.setJ_num(goodsNum);
            //计费重量
            express.setAmountWeight(expressDetailDto.getDeliveryWeight());
            //托寄物
            express.setMailGoods("货物");
            express.setMailGoodsNum(goodsNum);
            //运费
            express.setAmount(BigDecimal.ZERO);
            Logistics logistics = logisticsService.getLogisticsByCarrId(expressHeaderDto.getCarrierId());
            if(logistics != null){
                express.setLogisticsId(logistics.getLogisticsId());
                express.setLogisticsName(logistics.getName());
                sMap.put("logisticName",logistics.getName());
            }else {
                //抛送了未维护的快递商
                express.setLogisticsId(0);
                express.setLogisticsName("");
                sMap.put("logisticName","");
                logger.info("wms下发了ERP未维护快递商 deliveryNo:{}， carrierId:{}",
                        expressDetailDto.getDeliveryNo(), expressHeaderDto.getCarrierId());
            }
            express.setLogisticsComments("");
            express.setMailCommtents("节假日正常派送，提前联系");
            express.setWmsOrderNo(expressHeaderDto.getOrderNo());
            String erpOrderNo = WmsCommonUtil.getOriginalOrderNo(expressHeaderDto.getSOReference1());


            Saleorder saleorderInfo = new Saleorder();
            StringBuffer id_num_price = new StringBuffer();
            String flag;
            String identifier ="保存";

            switch (expressHeaderDto.getOrderType()) {
                /**
                 * 包裹类型为采购单
                 */
                case WmsInterfaceOrderType.INPUT_PURCHASE: {
                    flag = setBuyOrderInfo(expressDetailDto, erpOrderNo, saleorderInfo, id_num_price);
                    break;
                }

                /**
                 * 包裹类型为售后单
                 */
                case WmsInterfaceOrderType.INPUT_SALE_RETURN:
                case WmsInterfaceOrderType.OUT_PURCHASE_RETURN:
                case WmsInterfaceOrderType.EXCHANG_SALEORDER:
                case WmsInterfaceOrderType.OUT_PURCHASE_EXG: {
                    flag = setAfterSalesInfo(expressDetailDto, erpOrderNo, saleorderInfo, id_num_price);
                    break;
                }

                /**
                 * 包裹类型为商品外借出库
                 */
                case WmsInterfaceOrderType.SAMPLE_ORDER_OUT:
                case WmsInterfaceOrderType.OUT_LENDOUT: {
                    express.setWmsOrderNo(expressHeaderDto.getSOReference1()+"/"+expressHeaderDto.getOrderNo());
                    flag = setLendOutInfo(expressDetailDto, erpOrderNo, saleorderInfo, id_num_price);
                    break;
                }

                /***
                 * 销售单类型包裹
                 */
                case WmsInterfaceOrderType.OUT_SALE_OUT: {
                    logger.info("包裹回传为销售单信息 售后单号为:{}" , erpOrderNo);
                    saleorderInfo = saleorderService.getsaleorderId(erpOrderNo);
                    if (saleorderInfo == null) {
                        logger.error("包裹回传的销售订单信息不存在 saleorderNo:{}" , erpOrderNo);
                        throw new RuntimeException("包裹回传的订单信息不存在 saleorderNo:{}" + erpOrderNo);
                    }
                    List<SaleorderGoods> saleorderGoods = saleorderService.getSaleorderGoods(saleorderInfo);
                    if (CollectionUtils.isEmpty(saleorderGoods)) {
                        logger.error("包裹回传订单的商品存在异常 saleorderGoods:{}" ,saleorderGoods.toString());
                        throw new RuntimeException("包裹回传订单的商品存在异常 saleorderGoods:{}" + saleorderGoods.toString());
                    }
                   Express oldExpress = expressMapper.getExpressInfoByWmsNo(expressHeaderDto.getOrderNo(),saleorderInfo.getSaleorderId(),express.getLogisticsNo());
                    if(oldExpress != null){
                        identifier = "编辑";
                        express.setExpressId(oldExpress.getExpressId());
                    }
                    express.setWmsOrderNo(expressHeaderDto.getSOReference1()+"/"+expressHeaderDto.getOrderNo());

                    delReviewExpress(express, erpOrderNo, saleorderInfo);

                    flag = "1";

                    HashMap<Integer, SaleorderGoods> saleOrderGoodsHashMap = new HashMap<>(16);
                    expressDetailDto.getPackItem().stream().forEach(expressDataDto -> {
                        saleorderGoods.stream().forEach(saleorderGoodsInfo -> {
                            if (saleorderGoodsInfo.getSaleorderGoodsId().equals(Integer.parseInt(expressDataDto.getUserDefine1()))) {
                                if (saleOrderGoodsHashMap.containsKey(saleorderGoodsInfo.getSaleorderGoodsId())){
                                    SaleorderGoods goodsInfo = saleOrderGoodsHashMap.get(saleorderGoodsInfo.getSaleorderGoodsId());
                                    goodsInfo.setNum(goodsInfo.getNum() + expressDataDto.getQtyShipped().intValue());
                                } else {
                                    SaleorderGoods saleorderGoodsAppendInfo = new SaleorderGoods();
                                    saleorderGoodsAppendInfo.setSaleorderGoodsId(saleorderGoodsInfo.getSaleorderGoodsId());
                                    saleorderGoodsAppendInfo.setSku(saleorderGoodsInfo.getSku());
                                    saleorderGoodsAppendInfo.setGoodsName(saleorderGoodsInfo.getGoodsName());
                                    saleorderGoodsAppendInfo.setNum(expressDataDto.getQtyShipped().intValue());
                                    saleorderGoodsAppendInfo.setPrice(saleorderGoodsInfo.getPrice());
                                    saleOrderGoodsHashMap.put(saleorderGoodsInfo.getSaleorderGoodsId(),saleorderGoodsAppendInfo);
                                }
                            }
                        });
                    });

                    if (MapUtils.isEmpty(saleOrderGoodsHashMap)){
                        logger.error("包裹明细封装信息为空 saleOrderGoodsHashMap:{}", JSON.toJSONString(saleOrderGoodsHashMap));
                        throw new RuntimeException("包裹明细封装信息为空 saleOrderGoodsHashMap:{}"+ JSON.toJSONString(saleOrderGoodsHashMap));
                    }
                    Integer goodTypeSum = 0;
                    String goodName = "";
                    Integer goodNum = 0;
                    for (SaleorderGoods goods : saleOrderGoodsHashMap.values()) {
                        goodTypeSum ++;
                        if(goodName.equals("")){
                            goodName = goods.getGoodsName();
                        }
                        goodNum += goods.getNum();
                        id_num_price.append(goods.getSaleorderGoodsId())
                                .append("|")
                                .append(goods.getNum())
                                .append("|")
                                .append(goods.getPrice())
                                .append("|")
                                .append(goods.getSku())
                                .append("_");
                    }
                    sMap.put("goodTypeSum",goodTypeSum);
                    sMap.put("goodName",goodName);
                    sMap.put("goodNum",goodNum);
                    sMap.put("logisticTime",DateUtil.convertString(System.currentTimeMillis(), DateUtil.DATE_FORMAT));
                    try {
                        //VDERP-8189【科研购公众号】科研购订单发货推送公众号消息提醒客户
                        //baseService.sendKygDeliveryOrder(saleorderInfo, sMap);
                    }catch (Exception e){
                        logger.error("推送科研购发货信息失败，异常,",e);
                    }
                    addTrackDelivery(erpOrderNo,express.getLogisticsNo(),saleorderInfo.getTraderId());
                    break;
                }case WmsInterfaceOrderType.OUT_SCRAPPED:
                case WmsInterfaceOrderType.UNIT_CONVERSION_OUT:
                case WmsInterfaceOrderType.UNIT_CONVERSION_IN:
                case WmsInterfaceOrderType.OUT_RECEIVER:{
                    flag = "";
                    break;
                }
                default: {
                    logger.error("包裹回传订单类型匹配失败 orderType:{}" ,expressHeaderDto.getOrderType());
                    throw new RuntimeException("包裹回传订单类型匹配失败 orderType:{}" + expressHeaderDto.getOrderType());
                }
            }
            if(StringUtil.isBlank(flag)){
                return;
            }

            //若需使用添加ERP的快递信息 在resultInfo中取
            logger.info("开始添加快递信息 express:{},id_num_price:{},saleorderInfo:{}" , JSON.toJSONString(express) ,id_num_price,JSON.toJSONString(saleorderInfo));
            ResultInfo resultInfo = expressService.saveExpressInfo(express, DateUtil.convertString(System.currentTimeMillis(), DateUtil.DATE_FORMAT),
                    BigDecimal.ZERO, id_num_price.toString(), saleorderInfo, flag, saleorderInfo.getSaleorderId(), null,identifier , null);
            logger.info("包裹相关信息添加结果 wms单号:{},erp单号:{},resultInfo:{}" , expressHeaderDto.getOrderNo(), expressHeaderDto.getSOReference1(), resultInfo.toString());

            Express expressDto = (Express) resultInfo.getData();
            if (expressDto == null) {
                logger.error("包裹回传保存失败 wms单号:{},erp单号:{},错误信息:{}" , expressHeaderDto.getOrderNo(),
                        expressHeaderDto.getSOReference1(), resultInfo.getMessage());
                throw new RuntimeException("包裹回传失败 原因:" + resultInfo.getMessage() +
                        ",erp单号" + expressHeaderDto.getSOReference1() +
                        ",wms单号" + expressHeaderDto.getOrderNo());
            }
            String phone = expressService.getPhoneByBusinessType(expressDto.getExpressId(), express.getLogisticsNo());
            //发送物流信息至base
            logger.info("开始推送快递信息至base服务,单号:{}",express.getLogisticsNo());
            logisticsService.pushLogisticsToBase(express.getLogisticsNo()
                                                    ,logisticsService.getLogisticsCodeByLogisticsId(express.getLogisticsId())
                                                    ,phone);
        });
    }

    @Autowired
    private TrackStrategyFactory trackStrategyFactory;

    private void addTrackDelivery( String orderNo,String logisticsNo, Integer traderId) {
        EventTrackingEnum eventTrackingEnum = EventTrackingEnum.SALE_ORDER_DELIVER;
        try {
            TrackParamsData trackParamsData = new TrackParamsData();
            Map<String, Object> trackParams = new HashMap<>();
            trackParams.put("traderId",traderId);
            trackParams.put("orderNo",orderNo);
            trackParams.put("logisticsNo",logisticsNo);
            TrackStrategy trackStrategy = trackStrategyFactory.getStrategyByType(eventTrackingEnum);
            trackParamsData.setEventTrackingEnum(eventTrackingEnum);
            trackParamsData.setTrackParams(trackParams);
            trackParamsData.setTrackResult(ResultInfo.success());
            trackStrategy.track(trackParamsData);
        }catch(Exception e) {
            logger.error("埋点：{}，失败，不影响正常业务",eventTrackingEnum.getArchivedName(),e);
        }
    }

    private void delReviewExpress(Express express, String erpOrderNo, Saleorder saleorderInfo) {
        Express search = new Express();
        search.setWmsOrderNo(express.getWmsOrderNo());
        search.setLogisticsNo(erpOrderNo);
        search.setLogisticsComments(ErpConst.VIRTUAL_EXPRESS_NO);
        logger.info("包裹回传处理虚拟快递单检索条件信息 search:{}", JSON.toJSONString(search));
        Express oldExpress2 = expressMapper.getExpressInfoByLogNoAndComments(search);
        if(oldExpress2 != null){
            oldExpress2.setIsEnable(0);
            oldExpress2.setSaleorderId(saleorderInfo.getSaleorderId());
            logger.info("包裹回传处理虚拟快递单删除信息 oldExpress2:{}", JSON.toJSONString(oldExpress2));
            expressMapper.updateByPrimaryKeySelective(oldExpress2);

        }
    }

    /**
     * 设置外借出库单信息
     *
     * @param expressDetailDto
     * @param erpOrderNo
     * @param saleorderInfo
     * @param id_num_price
     * @return
     */
    private String setLendOutInfo(ExpressDetailDto expressDetailDto, String erpOrderNo, Saleorder saleorderInfo, StringBuffer id_num_price) {
        logger.info("包裹回传为外借单信息 外借单号为:{}" , erpOrderNo);
        String flag;
        WmsOutputOrder wmsOutputOrder = wmsOutputOrderMapper.selectByOrderNo(erpOrderNo);
        if (wmsOutputOrder == null) {
            logger.error("包裹回传外借单信息不存在 外借单号为:{}" , erpOrderNo);
            throw new RuntimeException("包裹回传外借单信息不存在 外借单号为:" + erpOrderNo);
        }
        List<WmsOutputOrderGoods> wmsOutputOrderGoods = wmsOutputOrderGoodsMapper.queryOutputGoodsByLendOutId(wmsOutputOrder.getId());
        if (CollectionUtils.isEmpty(wmsOutputOrderGoods)) {
            logger.error("包裹回传的外借单商品信息不存在 外借单ID:{}" , erpOrderNo);
            throw new RuntimeException("包裹回传的外借单商品信息不存在 外借单ID:" + erpOrderNo);
        }

        flag = "3";
        saleorderInfo.setSaleorderId(wmsOutputOrder.getId().intValue());
        saleorderInfo.setSaleorderNo(wmsOutputOrder.getOrderNo());

        HashMap<Long, WmsOutputOrderGoods> wmsOutputOrderGoodsHashMap = new HashMap<>(16);
        expressDetailDto.getPackItem().stream().forEach(expressDataDto -> {
            wmsOutputOrderGoods.stream().forEach(wmsOutputOrderGoodInfo -> {
                if (((Integer) wmsOutputOrderGoodInfo.getId().intValue())
                        .equals(Integer.parseInt(expressDataDto.getUserDefine1()))) {
                    if (wmsOutputOrderGoodsHashMap.containsKey(wmsOutputOrderGoodInfo.getId())){
                        WmsOutputOrderGoods goodsInfo = wmsOutputOrderGoodsHashMap.get(wmsOutputOrderGoodInfo.getId());
                        goodsInfo.setOutputNum(goodsInfo.getOutputNum() + expressDataDto.getQtyShipped().intValue());
                    } else {
                        WmsOutputOrderGoods wmsOutputOrderGoodsAppendInfo = new WmsOutputOrderGoods();
                        wmsOutputOrderGoodsAppendInfo.setId(wmsOutputOrderGoodInfo.getId());
                        wmsOutputOrderGoodsAppendInfo.setSkuNo(expressDataDto.getSKU());
                        wmsOutputOrderGoodsAppendInfo.setOutputNum(expressDataDto.getQtyShipped().intValue());
                        wmsOutputOrderGoodsHashMap.put(wmsOutputOrderGoodInfo.getId(), wmsOutputOrderGoodsAppendInfo);
                    }
                }
            });
        });

        if (MapUtils.isEmpty(wmsOutputOrderGoodsHashMap)){
            logger.error("包裹明细封装信息为空 wmsOutputOrderGoodsHashMap:{}", JSON.toJSONString(wmsOutputOrderGoodsHashMap));
            throw new RuntimeException("包裹明细封装信息为空 wmsOutputOrderGoodsHashMap:{}"+ JSON.toJSONString(wmsOutputOrderGoodsHashMap));
        }

        for (WmsOutputOrderGoods outputOrderGoods : wmsOutputOrderGoodsHashMap.values()) {
            id_num_price.append(outputOrderGoods.getId())
                    .append("|")
                    .append(outputOrderGoods.getOutputNum())
                    .append("|")
                    .append(0)
                    .append("|")
                    .append(outputOrderGoods.getSkuNo())
                    .append("_");
        }
        return flag;
    }

    /**
     * 设置售后单信息
     *
     * @param expressDetailDto
     * @param erpOrderNo
     * @param saleorderInfo
     * @param id_num_price
     * @return
     */
    private String setAfterSalesInfo(ExpressDetailDto expressDetailDto, String erpOrderNo, Saleorder saleorderInfo, StringBuffer id_num_price) {
        logger.info("包裹回传为售后单信息 售后单号为:{}" , erpOrderNo);
        String flag;
        List<AfterSalesGoods> afterSalesGoods = afterSalesService.getAfterSalesGoodsByAfterSalesNo(erpOrderNo);
        if (CollectionUtils.isEmpty(afterSalesGoods)) {
            logger.error("包裹回传售后单在ERP中售后商品不全 erpOrderNo:{}" , erpOrderNo);
            throw new RuntimeException("包裹回传售后单在ERP中售后商品不全 erpOrderNo:{}" + erpOrderNo);
        }

        flag = "2";

        HashMap<Integer, AfterSalesGoods> afterSalesGoodsHashMap = new HashMap<>(16);
        saleorderInfo.setSaleorderId(afterSalesGoods.get(0).getAfterSalesGoodsId());
        expressDetailDto.getPackItem().stream().forEach(expressDataDto -> {
            afterSalesGoods.stream().forEach(afterSalesGoodInfo -> {
                if (afterSalesGoodInfo.getAfterSalesGoodsId().equals(Integer.parseInt(expressDataDto.getUserDefine1()))) {
                    if (afterSalesGoodsHashMap.containsKey(afterSalesGoodInfo.getAfterSalesGoodsId())){
                        AfterSalesGoods salesGoods = afterSalesGoodsHashMap.get(afterSalesGoodInfo.getAfterSalesGoodsId());
                        salesGoods.setNum(salesGoods.getNum() + expressDataDto.getQtyShipped().intValue());

                    }else {
                        AfterSalesGoods afterSalesGoodsAppendInfo = new AfterSalesGoods();
                        afterSalesGoodsAppendInfo.setAfterSalesGoodsId(afterSalesGoodInfo.getAfterSalesGoodsId());
                        afterSalesGoodsAppendInfo.setSku(afterSalesGoodInfo.getSku());
                        afterSalesGoodsAppendInfo.setNum(expressDataDto.getQtyShipped().intValue());
                        afterSalesGoodsAppendInfo.setPrice(afterSalesGoodInfo.getPrice() == null ? BigDecimal.ZERO : afterSalesGoodInfo.getPrice());
                        afterSalesGoodsHashMap.put(afterSalesGoodInfo.getAfterSalesGoodsId(), afterSalesGoodsAppendInfo);
                    }
                }
            });
        });


        if (MapUtils.isEmpty(afterSalesGoodsHashMap)){
            logger.error("包裹明细封装信息为空 afterSalesGoodsHashMap:{}", JSON.toJSONString(afterSalesGoodsHashMap));
            throw new RuntimeException("包裹明细封装信息为空 afterSalesGoodsHashMap:{}"+ JSON.toJSONString(afterSalesGoodsHashMap));
        }

        for (AfterSalesGoods salesGoods : afterSalesGoodsHashMap.values()) {
            id_num_price.append(salesGoods.getAfterSalesGoodsId())
                    .append("|")
                    .append(salesGoods.getNum())
                    .append("|")
                    .append(salesGoods.getPrice())
                    .append("|")
                    .append(salesGoods.getSku())
                    .append("_");
        }
        return flag;
    }

    /**
     * 设置采购单信息
     *
     * @param expressDetailDto
     * @param erpOrderNo
     * @param saleorderInfo
     * @param id_num_price
     * @return
     */
    private String setBuyOrderInfo(ExpressDetailDto expressDetailDto, String erpOrderNo, Saleorder saleorderInfo, StringBuffer id_num_price) {
        logger.info("包裹回传为采购单信息 采购单号为:{}" ,erpOrderNo);
        String flag;
        BuyorderVo buyorderVo = buyorderService.getBuyorderVoByOrderNo(erpOrderNo);

        if (buyorderVo == null || CollectionUtils.isEmpty(buyorderVo.getBuyorderGoodsVoList())) {
            logger.error("包裹回传采购单信息以及商品信息不全 采购单ID:{}" , erpOrderNo);
            throw new RuntimeException("包裹回传采购单信息以及商品信息不全 采购单ID:{}" + erpOrderNo);
        }

        flag = "4";
        saleorderInfo.setSaleorderId(buyorderVo.getBuyorderId());

        HashMap<Integer, BuyorderGoods> buyorderGoodsVoHashMap = new HashMap<>(16);
        expressDetailDto.getPackItem().stream().forEach(expressDataDto -> {
            buyorderVo.getBuyorderGoodsVoList().stream().forEach(buyorderGoodsVo -> {
                if (buyorderGoodsVo.getBuyorderGoodsId().equals(Integer.parseInt(expressDataDto.getUserDefine1()))) {
                    if (buyorderGoodsVoHashMap.containsKey(buyorderGoodsVo.getBuyorderGoodsId())){
                        BuyorderGoods buyorderGoodsInfo = buyorderGoodsVoHashMap.get(buyorderGoodsVo.getBuyorderGoodsId());
                        buyorderGoodsInfo.setNum(buyorderGoodsInfo.getNum() + expressDataDto.getQtyShipped().intValue());
                    }else {
                        BuyorderGoods buyorderGoods = new BuyorderGoods();
                        buyorderGoods.setBuyorderGoodsId(buyorderGoodsVo.getBuyorderGoodsId());
                        buyorderGoods.setNum(expressDataDto.getQtyShipped().intValue());
                        buyorderGoods.setPrice(buyorderGoodsVo.getPrice() == null ? BigDecimal.ZERO : buyorderGoodsVo.getPrice());
                        buyorderGoods.setSku(buyorderGoodsVo.getSku());
                        buyorderGoodsVoHashMap.put(buyorderGoodsVo.getBuyorderGoodsId(), buyorderGoods);
                    }
                }
            });
        });

        if (MapUtils.isEmpty(buyorderGoodsVoHashMap)){
            logger.error("包裹明细封装信息为空 buyorderGoodsVoHashMap:{}", JSON.toJSONString(buyorderGoodsVoHashMap));
            throw new RuntimeException("包裹明细封装信息为空 buyorderGoodsVoHashMap:{}"+ JSON.toJSONString(buyorderGoodsVoHashMap));
        }

        for (Map.Entry<Integer, BuyorderGoods> entry : buyorderGoodsVoHashMap.entrySet()) {
            id_num_price.append(entry.getValue().getBuyorderGoodsId())
                    .append("|")
                    .append(entry.getValue().getNum())
                    .append("|")
                    .append(entry.getValue().getPrice())
                    .append("|")
                    .append(entry.getValue().getSku())
                    .append("_");
        }
        return flag;
    }

}
