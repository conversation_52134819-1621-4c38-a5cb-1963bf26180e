<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.business.mapper.BusinessChanceSupportRecordMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.business.domain.entity.BusinessChanceSupportRecord">
    <!--@mbg.generated-->
    <!--@Table T_BUSINESS_CHANCE_SUPPORT_RECORD-->
    <id column="BUSINESS_CHANCE_SUPPORT_RECORD_ID" jdbcType="BIGINT" property="businessChanceSupportRecordId" />
    <result column="BUSINESS_CHANCE_ID" jdbcType="INTEGER" property="businessChanceId" />
    <result column="BUSINESS_LEVEL" jdbcType="VARCHAR" property="businessLevel" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="ORDER_DATE" jdbcType="TIMESTAMP" property="orderDate" />
    <result column="NEXT_SUPPORT_DATE" jdbcType="TIMESTAMP" property="nextSupportDate" />
    <result column="CONTENT" jdbcType="VARCHAR" property="content" />
    <result column="DEPARTMENT" jdbcType="VARCHAR" property="department" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    BUSINESS_CHANCE_SUPPORT_RECORD_ID, BUSINESS_CHANCE_ID, BUSINESS_LEVEL, AMOUNT, ORDER_DATE,
    NEXT_SUPPORT_DATE, CONTENT, DEPARTMENT, IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME,
    UPDATER, UPDATER_NAME, UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_BUSINESS_CHANCE_SUPPORT_RECORD
    where BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{businessChanceSupportRecordId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_BUSINESS_CHANCE_SUPPORT_RECORD
    where BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{businessChanceSupportRecordId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="BUSINESS_CHANCE_SUPPORT_RECORD_ID" keyProperty="businessChanceSupportRecordId" parameterType="com.vedeng.erp.business.domain.entity.BusinessChanceSupportRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUSINESS_CHANCE_SUPPORT_RECORD (BUSINESS_CHANCE_ID, BUSINESS_LEVEL, 
      AMOUNT, ORDER_DATE, NEXT_SUPPORT_DATE, 
      CONTENT, DEPARTMENT, IS_DELETE, 
      ADD_TIME, MOD_TIME, CREATOR, 
      CREATOR_NAME, UPDATER, UPDATER_NAME, 
      UPDATE_REMARK)
    values (#{businessChanceId,jdbcType=INTEGER}, #{businessLevel,jdbcType=VARCHAR}, 
      #{amount,jdbcType=DECIMAL}, #{orderDate,jdbcType=TIMESTAMP}, #{nextSupportDate,jdbcType=TIMESTAMP}, 
      #{content,jdbcType=VARCHAR}, #{department,jdbcType=VARCHAR}, #{isDelete,jdbcType=INTEGER}, 
      #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{creatorName,jdbcType=VARCHAR}, #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, 
      #{updateRemark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="BUSINESS_CHANCE_SUPPORT_RECORD_ID" keyProperty="businessChanceSupportRecordId" parameterType="com.vedeng.erp.business.domain.entity.BusinessChanceSupportRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUSINESS_CHANCE_SUPPORT_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessChanceId != null">
        BUSINESS_CHANCE_ID,
      </if>
      <if test="businessLevel != null">
        BUSINESS_LEVEL,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="orderDate != null">
        ORDER_DATE,
      </if>
      <if test="nextSupportDate != null">
        NEXT_SUPPORT_DATE,
      </if>
      <if test="content != null">
        CONTENT,
      </if>
      <if test="department != null">
        DEPARTMENT,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessChanceId != null">
        #{businessChanceId,jdbcType=INTEGER},
      </if>
      <if test="businessLevel != null">
        #{businessLevel,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="orderDate != null">
        #{orderDate,jdbcType=TIMESTAMP},
      </if>
      <if test="nextSupportDate != null">
        #{nextSupportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="department != null">
        #{department,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.business.domain.entity.BusinessChanceSupportRecord">
    <!--@mbg.generated-->
    update T_BUSINESS_CHANCE_SUPPORT_RECORD
    <set>
      <if test="businessChanceId != null">
        BUSINESS_CHANCE_ID = #{businessChanceId,jdbcType=INTEGER},
      </if>
      <if test="businessLevel != null">
        BUSINESS_LEVEL = #{businessLevel,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="orderDate != null">
        ORDER_DATE = #{orderDate,jdbcType=TIMESTAMP},
      </if>
      <if test="nextSupportDate != null">
        NEXT_SUPPORT_DATE = #{nextSupportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="content != null">
        CONTENT = #{content,jdbcType=VARCHAR},
      </if>
      <if test="department != null">
        DEPARTMENT = #{department,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{businessChanceSupportRecordId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.business.domain.entity.BusinessChanceSupportRecord">
    <!--@mbg.generated-->
    update T_BUSINESS_CHANCE_SUPPORT_RECORD
    set BUSINESS_CHANCE_ID = #{businessChanceId,jdbcType=INTEGER},
      BUSINESS_LEVEL = #{businessLevel,jdbcType=VARCHAR},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      ORDER_DATE = #{orderDate,jdbcType=TIMESTAMP},
      NEXT_SUPPORT_DATE = #{nextSupportDate,jdbcType=TIMESTAMP},
      CONTENT = #{content,jdbcType=VARCHAR},
      DEPARTMENT = #{department,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{businessChanceSupportRecordId,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_BUSINESS_CHANCE_SUPPORT_RECORD
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="BUSINESS_CHANCE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{item.businessChanceSupportRecordId,jdbcType=BIGINT} then #{item.businessChanceId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="BUSINESS_LEVEL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{item.businessChanceSupportRecordId,jdbcType=BIGINT} then #{item.businessLevel,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{item.businessChanceSupportRecordId,jdbcType=BIGINT} then #{item.amount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="ORDER_DATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{item.businessChanceSupportRecordId,jdbcType=BIGINT} then #{item.orderDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="NEXT_SUPPORT_DATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{item.businessChanceSupportRecordId,jdbcType=BIGINT} then #{item.nextSupportDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CONTENT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{item.businessChanceSupportRecordId,jdbcType=BIGINT} then #{item.content,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="DEPARTMENT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{item.businessChanceSupportRecordId,jdbcType=BIGINT} then #{item.department,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{item.businessChanceSupportRecordId,jdbcType=BIGINT} then #{item.isDelete,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{item.businessChanceSupportRecordId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{item.businessChanceSupportRecordId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{item.businessChanceSupportRecordId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{item.businessChanceSupportRecordId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{item.businessChanceSupportRecordId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{item.businessChanceSupportRecordId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATE_REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{item.businessChanceSupportRecordId,jdbcType=BIGINT} then #{item.updateRemark,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where BUSINESS_CHANCE_SUPPORT_RECORD_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.businessChanceSupportRecordId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_BUSINESS_CHANCE_SUPPORT_RECORD
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="BUSINESS_CHANCE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.businessChanceId != null">
            when BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{item.businessChanceSupportRecordId,jdbcType=BIGINT} then #{item.businessChanceId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="BUSINESS_LEVEL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.businessLevel != null">
            when BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{item.businessChanceSupportRecordId,jdbcType=BIGINT} then #{item.businessLevel,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.amount != null">
            when BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{item.businessChanceSupportRecordId,jdbcType=BIGINT} then #{item.amount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="ORDER_DATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderDate != null">
            when BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{item.businessChanceSupportRecordId,jdbcType=BIGINT} then #{item.orderDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="NEXT_SUPPORT_DATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.nextSupportDate != null">
            when BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{item.businessChanceSupportRecordId,jdbcType=BIGINT} then #{item.nextSupportDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CONTENT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.content != null">
            when BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{item.businessChanceSupportRecordId,jdbcType=BIGINT} then #{item.content,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="DEPARTMENT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.department != null">
            when BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{item.businessChanceSupportRecordId,jdbcType=BIGINT} then #{item.department,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDelete != null">
            when BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{item.businessChanceSupportRecordId,jdbcType=BIGINT} then #{item.isDelete,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{item.businessChanceSupportRecordId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{item.businessChanceSupportRecordId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{item.businessChanceSupportRecordId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{item.businessChanceSupportRecordId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{item.businessChanceSupportRecordId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{item.businessChanceSupportRecordId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATE_REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateRemark != null">
            when BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{item.businessChanceSupportRecordId,jdbcType=BIGINT} then #{item.updateRemark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where BUSINESS_CHANCE_SUPPORT_RECORD_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.businessChanceSupportRecordId,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="BUSINESS_CHANCE_SUPPORT_RECORD_ID" keyProperty="businessChanceSupportRecordId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUSINESS_CHANCE_SUPPORT_RECORD
    (BUSINESS_CHANCE_ID, BUSINESS_LEVEL, AMOUNT, ORDER_DATE, NEXT_SUPPORT_DATE, CONTENT, 
      DEPARTMENT, IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, 
      UPDATE_REMARK)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.businessChanceId,jdbcType=INTEGER}, #{item.businessLevel,jdbcType=VARCHAR}, 
        #{item.amount,jdbcType=DECIMAL}, #{item.orderDate,jdbcType=TIMESTAMP}, #{item.nextSupportDate,jdbcType=TIMESTAMP}, 
        #{item.content,jdbcType=VARCHAR}, #{item.department,jdbcType=VARCHAR}, #{item.isDelete,jdbcType=INTEGER}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, 
        #{item.creatorName,jdbcType=VARCHAR}, #{item.updater,jdbcType=INTEGER}, #{item.updaterName,jdbcType=VARCHAR}, 
        #{item.updateRemark,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="BUSINESS_CHANCE_SUPPORT_RECORD_ID" keyProperty="businessChanceSupportRecordId" parameterType="com.vedeng.erp.business.domain.entity.BusinessChanceSupportRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUSINESS_CHANCE_SUPPORT_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessChanceSupportRecordId != null">
        BUSINESS_CHANCE_SUPPORT_RECORD_ID,
      </if>
      BUSINESS_CHANCE_ID,
      BUSINESS_LEVEL,
      AMOUNT,
      ORDER_DATE,
      NEXT_SUPPORT_DATE,
      CONTENT,
      DEPARTMENT,
      IS_DELETE,
      ADD_TIME,
      MOD_TIME,
      CREATOR,
      CREATOR_NAME,
      UPDATER,
      UPDATER_NAME,
      UPDATE_REMARK,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessChanceSupportRecordId != null">
        #{businessChanceSupportRecordId,jdbcType=BIGINT},
      </if>
      #{businessChanceId,jdbcType=INTEGER},
      #{businessLevel,jdbcType=VARCHAR},
      #{amount,jdbcType=DECIMAL},
      #{orderDate,jdbcType=TIMESTAMP},
      #{nextSupportDate,jdbcType=TIMESTAMP},
      #{content,jdbcType=VARCHAR},
      #{department,jdbcType=VARCHAR},
      #{isDelete,jdbcType=INTEGER},
      #{addTime,jdbcType=TIMESTAMP},
      #{modTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=INTEGER},
      #{creatorName,jdbcType=VARCHAR},
      #{updater,jdbcType=INTEGER},
      #{updaterName,jdbcType=VARCHAR},
      #{updateRemark,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="businessChanceSupportRecordId != null">
        BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{businessChanceSupportRecordId,jdbcType=BIGINT},
      </if>
      BUSINESS_CHANCE_ID = #{businessChanceId,jdbcType=INTEGER},
      BUSINESS_LEVEL = #{businessLevel,jdbcType=VARCHAR},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      ORDER_DATE = #{orderDate,jdbcType=TIMESTAMP},
      NEXT_SUPPORT_DATE = #{nextSupportDate,jdbcType=TIMESTAMP},
      CONTENT = #{content,jdbcType=VARCHAR},
      DEPARTMENT = #{department,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="BUSINESS_CHANCE_SUPPORT_RECORD_ID" keyProperty="businessChanceSupportRecordId" parameterType="com.vedeng.erp.business.domain.entity.BusinessChanceSupportRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUSINESS_CHANCE_SUPPORT_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessChanceSupportRecordId != null">
        BUSINESS_CHANCE_SUPPORT_RECORD_ID,
      </if>
      <if test="businessChanceId != null">
        BUSINESS_CHANCE_ID,
      </if>
      <if test="businessLevel != null">
        BUSINESS_LEVEL,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="orderDate != null">
        ORDER_DATE,
      </if>
      <if test="nextSupportDate != null">
        NEXT_SUPPORT_DATE,
      </if>
      <if test="content != null">
        CONTENT,
      </if>
      <if test="department != null">
        DEPARTMENT,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessChanceSupportRecordId != null">
        #{businessChanceSupportRecordId,jdbcType=BIGINT},
      </if>
      <if test="businessChanceId != null">
        #{businessChanceId,jdbcType=INTEGER},
      </if>
      <if test="businessLevel != null">
        #{businessLevel,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="orderDate != null">
        #{orderDate,jdbcType=TIMESTAMP},
      </if>
      <if test="nextSupportDate != null">
        #{nextSupportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="department != null">
        #{department,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="businessChanceSupportRecordId != null">
        BUSINESS_CHANCE_SUPPORT_RECORD_ID = #{businessChanceSupportRecordId,jdbcType=BIGINT},
      </if>
      <if test="businessChanceId != null">
        BUSINESS_CHANCE_ID = #{businessChanceId,jdbcType=INTEGER},
      </if>
      <if test="businessLevel != null">
        BUSINESS_LEVEL = #{businessLevel,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="orderDate != null">
        ORDER_DATE = #{orderDate,jdbcType=TIMESTAMP},
      </if>
      <if test="nextSupportDate != null">
        NEXT_SUPPORT_DATE = #{nextSupportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="content != null">
        CONTENT = #{content,jdbcType=VARCHAR},
      </if>
      <if test="department != null">
        DEPARTMENT = #{department,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
    
  <select id="countFollowUpTodayByUserId" resultType="java.lang.Integer">
    select count(distinct TBCSR.BUSINESS_CHANCE_ID)
    from (
    SELECT BUSINESS_CHANCE_ID,
    MAX(BUSINESS_CHANCE_SUPPORT_RECORD_ID) AS MAX_ID
    FROM T_BUSINESS_CHANCE_SUPPORT_RECORD
    WHERE
    CREATOR = #{userId,jdbcType=INTEGER}
    and IS_DELETE = 0
    GROUP BY BUSINESS_CHANCE_ID
    ) max_record
           left join T_BUSINESS_CHANCE_SUPPORT_RECORD TBCSR on max_record.MAX_ID = TBCSR.BUSINESS_CHANCE_SUPPORT_RECORD_ID
           left join T_BUSSINESS_CHANCE TBC on TBC.BUSSINESS_CHANCE_ID = TBCSR.BUSINESS_CHANCE_ID
    where TBCSR.NEXT_SUPPORT_DATE is not null
    and TBCSR.NEXT_SUPPORT_DATE <![CDATA[<=]]> #{currentDate,jdbcType=VARCHAR}
    and TBCSR.CREATOR = #{userId,jdbcType=INTEGER}
    and TBCSR.IS_DELETE = 0
    and TBC.STATUS not in (4, 7)
  </select>

  <select id="countFollowUpTodayByUserIdList" resultType="java.lang.Integer">
    select count(distinct TBCSR.BUSINESS_CHANCE_ID)
    from (
    SELECT BUSINESS_CHANCE_ID,
    MAX(BUSINESS_CHANCE_SUPPORT_RECORD_ID) AS MAX_ID
    FROM T_BUSINESS_CHANCE_SUPPORT_RECORD
    WHERE
    CREATOR   in
    <foreach close=")" collection="userIdList" item="item" open="(" separator=", ">
      #{item,jdbcType=INTEGER}
    </foreach>
    and IS_DELETE = 0
    GROUP BY BUSINESS_CHANCE_ID
    ) max_record
    left join T_BUSINESS_CHANCE_SUPPORT_RECORD TBCSR on max_record.MAX_ID = TBCSR.BUSINESS_CHANCE_SUPPORT_RECORD_ID
    left join T_BUSSINESS_CHANCE TBC on TBC.BUSSINESS_CHANCE_ID = TBCSR.BUSINESS_CHANCE_ID
    where TBCSR.NEXT_SUPPORT_DATE is not null
    and TBCSR.NEXT_SUPPORT_DATE <![CDATA[<=]]> #{currentDate,jdbcType=VARCHAR}
    and TBCSR.CREATOR    in
    <foreach close=")" collection="userIdList" item="item" open="(" separator=", ">
      #{item,jdbcType=INTEGER}
    </foreach>
    and TBCSR.IS_DELETE = 0
    and TBC.STATUS not in (4, 7)
  </select>
  
  <select id="getStatisticalDataList" resultMap="BaseResultMap">
    SELECT a.*
    FROM T_BUSINESS_CHANCE_SUPPORT_RECORD a
           INNER JOIN (SELECT BUSINESS_CHANCE_ID, MAX(BUSINESS_CHANCE_SUPPORT_RECORD_ID) AS MaxRecordID
                       FROM T_BUSINESS_CHANCE_SUPPORT_RECORD
                       WHERE CREATOR = #{userId,jdbcType=INTEGER}
                         AND IS_DELETE = 0
                        <if test="startTime != null and startTime != ''">
                          and ORDER_DATE <![CDATA[>=]]> #{startTime,jdbcType=VARCHAR}
                        </if>
                        <if test="endTime != null and endTime != ''">
                          and ORDER_DATE <![CDATA[<=]]> #{endTime,jdbcType=VARCHAR}
                        </if>
                       GROUP BY BUSINESS_CHANCE_ID) b ON a.BUSINESS_CHANCE_SUPPORT_RECORD_ID = b.MaxRecordID
    WHERE a.CREATOR =  #{userId,jdbcType=INTEGER}
      AND a.IS_DELETE = 0
    <if test="startTime != null and startTime != ''">
      and a.ORDER_DATE <![CDATA[>=]]> #{startTime,jdbcType=VARCHAR}
    </if>
    <if test="endTime != null and endTime != ''">
      and a.ORDER_DATE <![CDATA[<=]]> #{endTime,jdbcType=VARCHAR}
    </if>
  </select>

  <select id="selectByBusinessChanceId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_BUSINESS_CHANCE_SUPPORT_RECORD
        where BUSINESS_CHANCE_ID = #{businessChanceId,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2024-05-23-->
  <select id="findByCreator" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_BUSINESS_CHANCE_SUPPORT_RECORD
    where CREATOR=#{creator,jdbcType=INTEGER} and IS_DELETE=0 and BUSINESS_CHANCE_ID = #{businessChanceId,jdbcType=INTEGER}
  </select>
</mapper>