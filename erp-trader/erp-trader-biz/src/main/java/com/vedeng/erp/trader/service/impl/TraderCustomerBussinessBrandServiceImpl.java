package com.vedeng.erp.trader.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.erp.trader.dto.TraderCustomerBussinessBrandDto;
import com.vedeng.erp.trader.mapper.TraderCustomerBussinessBrandMapper;
import com.vedeng.erp.trader.mapstruct.TraderCustomerBussinessBrandConvertor;
import com.vedeng.erp.trader.service.TraderCustomerBussinessBrandService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/11 17:27
 **/
@Service
@Slf4j
public class TraderCustomerBussinessBrandServiceImpl implements TraderCustomerBussinessBrandService {

    @Autowired
    private TraderCustomerBussinessBrandMapper traderCustomerBussinessBrandMapper;

    @Autowired
    private TraderCustomerBussinessBrandConvertor traderCustomerBussinessBrandConvertor;

    @Override
    public void addAll(List<TraderCustomerBussinessBrandDto> traderCustomerBussinessBrandDtoList) {

        if (CollUtil.isEmpty(traderCustomerBussinessBrandDtoList)) {
            return;
        }
        traderCustomerBussinessBrandMapper.batchInsert(traderCustomerBussinessBrandConvertor.toEntity(traderCustomerBussinessBrandDtoList));
    }

    @Override
    public void deleteByTraderCustomerId(Integer traderCustomerId) {

        if (Objects.isNull(traderCustomerId)) {
            return;
        }
        traderCustomerBussinessBrandMapper.deleteByTraderCustomerId(traderCustomerId);
    }
}
