<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchRBuyorderExpenseJSaleorderDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchRBuyorderExpenseJSaleorderDto">
    <!--@mbg.generated-->
    <!--@Table T_R_BUYORDER_EXPENSE_J_SALEORDER-->
    <id column="T_R_BUYORDER_EXPENSE_J_SALEORDER_ID" jdbcType="INTEGER" property="tRBuyorderExpenseJSaleorderId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="SALEORDER_ID" jdbcType="INTEGER" property="saleorderId" />
    <result column="SALEORDER_GOODS_ID" jdbcType="INTEGER" property="saleorderGoodsId" />
    <result column="BUYORDER_EXPENSE_ID" jdbcType="INTEGER" property="buyorderExpenseId" />
    <result column="BUYORDER_EXPENSE_ITEM_ID" jdbcType="INTEGER" property="buyorderExpenseItemId" />
    <result column="SKU_ID" jdbcType="INTEGER" property="skuId" />
    <result column="SKU_NO" jdbcType="VARCHAR" property="skuNo" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
  </resultMap>
  <resultMap id="BaseResultMapAndOther" type="com.vedeng.erp.kingdee.batch.dto.BatchRBuyorderExpenseJSaleorderDto">
    <!--@mbg.generated-->
    <!--@Table T_R_BUYORDER_EXPENSE_J_SALEORDER-->
    <result column="SALEORDER_ID" jdbcType="INTEGER" property="saleorderId" />
    <result column="BUYORDER_EXPENSE_ID" jdbcType="INTEGER" property="buyorderExpenseId" />
    <result column="BUYORDER_EXPENSE_ITEM_ID" jdbcType="INTEGER" property="buyorderExpenseItemId" />
    <result column="SKU_ID" jdbcType="INTEGER" property="skuId" />
    <result column="SKU_NO" jdbcType="VARCHAR" property="skuNo" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
    <result column="preciseNum" jdbcType="INTEGER" property="preciseNum" />
    <result column="SALEORDER_NO" jdbcType="VARCHAR" property="saleorderNo"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    T_R_BUYORDER_EXPENSE_J_SALEORDER_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME,
    UPDATER_NAME, SALEORDER_ID, SALEORDER_GOODS_ID, BUYORDER_EXPENSE_ID, BUYORDER_EXPENSE_ITEM_ID,
    SKU_ID, SKU_NO, NUM
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_R_BUYORDER_EXPENSE_J_SALEORDER
    where T_R_BUYORDER_EXPENSE_J_SALEORDER_ID = #{tRBuyorderExpenseJSaleorderId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_R_BUYORDER_EXPENSE_J_SALEORDER
    where T_R_BUYORDER_EXPENSE_J_SALEORDER_ID = #{tRBuyorderExpenseJSaleorderId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="T_R_BUYORDER_EXPENSE_J_SALEORDER_ID" keyProperty="tRBuyorderExpenseJSaleorderId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchRBuyorderExpenseJSaleorderDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_BUYORDER_EXPENSE_J_SALEORDER (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      SALEORDER_ID, SALEORDER_GOODS_ID, BUYORDER_EXPENSE_ID, 
      BUYORDER_EXPENSE_ITEM_ID, SKU_ID, SKU_NO, 
      NUM)
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{saleorderId,jdbcType=INTEGER}, #{saleorderGoodsId,jdbcType=INTEGER}, #{buyorderExpenseId,jdbcType=INTEGER}, 
      #{buyorderExpenseItemId,jdbcType=INTEGER}, #{skuId,jdbcType=INTEGER}, #{skuNo,jdbcType=VARCHAR}, 
      #{num,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="T_R_BUYORDER_EXPENSE_J_SALEORDER_ID" keyProperty="tRBuyorderExpenseJSaleorderId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchRBuyorderExpenseJSaleorderDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_BUYORDER_EXPENSE_J_SALEORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="saleorderId != null">
        SALEORDER_ID,
      </if>
      <if test="saleorderGoodsId != null">
        SALEORDER_GOODS_ID,
      </if>
      <if test="buyorderExpenseId != null">
        BUYORDER_EXPENSE_ID,
      </if>
      <if test="buyorderExpenseItemId != null">
        BUYORDER_EXPENSE_ITEM_ID,
      </if>
      <if test="skuId != null">
        SKU_ID,
      </if>
      <if test="skuNo != null">
        SKU_NO,
      </if>
      <if test="num != null">
        NUM,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="saleorderId != null">
        #{saleorderId,jdbcType=INTEGER},
      </if>
      <if test="saleorderGoodsId != null">
        #{saleorderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="buyorderExpenseId != null">
        #{buyorderExpenseId,jdbcType=INTEGER},
      </if>
      <if test="buyorderExpenseItemId != null">
        #{buyorderExpenseItemId,jdbcType=INTEGER},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=INTEGER},
      </if>
      <if test="skuNo != null">
        #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchRBuyorderExpenseJSaleorderDto">
    <!--@mbg.generated-->
    update T_R_BUYORDER_EXPENSE_J_SALEORDER
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="saleorderId != null">
        SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
      </if>
      <if test="saleorderGoodsId != null">
        SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="buyorderExpenseId != null">
        BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER},
      </if>
      <if test="buyorderExpenseItemId != null">
        BUYORDER_EXPENSE_ITEM_ID = #{buyorderExpenseItemId,jdbcType=INTEGER},
      </if>
      <if test="skuId != null">
        SKU_ID = #{skuId,jdbcType=INTEGER},
      </if>
      <if test="skuNo != null">
        SKU_NO = #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
    </set>
    where T_R_BUYORDER_EXPENSE_J_SALEORDER_ID = #{tRBuyorderExpenseJSaleorderId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchRBuyorderExpenseJSaleorderDto">
    <!--@mbg.generated-->
    update T_R_BUYORDER_EXPENSE_J_SALEORDER
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
    MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
    CREATOR = #{creator,jdbcType=INTEGER},
    UPDATER = #{updater,jdbcType=INTEGER},
    CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
    UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
    SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
    SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER},
    BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER},
    BUYORDER_EXPENSE_ITEM_ID = #{buyorderExpenseItemId,jdbcType=INTEGER},
    SKU_ID = #{skuId,jdbcType=INTEGER},
    SKU_NO = #{skuNo,jdbcType=VARCHAR},
    NUM = #{num,jdbcType=INTEGER}
    where T_R_BUYORDER_EXPENSE_J_SALEORDER_ID = #{tRBuyorderExpenseJSaleorderId,jdbcType=INTEGER}
  </update>

  <!--auto generated by MybatisCodeHelper on 2023-03-21-->
  <select id="findByBuyorderExpenseId" resultMap="BaseResultMapAndOther">
    select
    TRBEJS.SALEORDER_ID, BUYORDER_EXPENSE_ID, BUYORDER_EXPENSE_ITEM_ID,
    SKU_ID, SKU_NO, SUM(NUM) as NUM,SUM(NUM) as preciseNum,TS.SALEORDER_NO
    from T_R_BUYORDER_EXPENSE_J_SALEORDER TRBEJS
    left join T_SALEORDER TS on TRBEJS.SALEORDER_ID = TS.SALEORDER_ID
    where BUYORDER_EXPENSE_ID=#{buyorderExpenseId,jdbcType=INTEGER}
    group by SALEORDER_ID,BUYORDER_EXPENSE_ITEM_ID
  </select>
</mapper>