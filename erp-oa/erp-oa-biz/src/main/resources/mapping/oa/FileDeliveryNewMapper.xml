<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.oa.dao.FileDeliveryNewMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.oa.model.FileDeliveryNew">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    <id column="FILE_DELIVERY_ID" jdbcType="INTEGER" property="fileDeliveryId" />
    <result column="FILE_DELIVERY_NO" jdbcType="VARCHAR" property="fileDeliveryNo" />
    <result column="APPLY_USER_ID" jdbcType="INTEGER" property="applyUserId" />
    <result column="APPLY_ORG_NAME" jdbcType="VARCHAR" property="applyOrgName" />
    <result column="APPLY_TIME" jdbcType="TIMESTAMP" property="applyTime" />
    <result column="VERIFY_STATUS" jdbcType="TINYINT" property="verifyStatus" />
    <result column="DELIVERY_STATUS" jdbcType="TINYINT" property="deliveryStatus" />
    <result column="DELIVERY_TIME" jdbcType="TIMESTAMP" property="deliveryTime" />
    <result column="LOGISTICS_NAME" jdbcType="TINYINT" property="logisticsName" />
    <result column="EXPRESS_NOS" jdbcType="VARCHAR" property="expressNos" />
    <result column="DELIVERY_TYPE" jdbcType="TINYINT" property="deliveryType" />
    <result column="DELIVERY_DEPT" jdbcType="TINYINT" property="deliveryDept" />
    <result column="DELIVERY_TOTAL_WEIGHT" jdbcType="DECIMAL" property="deliveryTotalWeight" />
    <result column="DELIVERY_PROD_NAME" jdbcType="VARCHAR" property="deliveryProdName" />
    <result column="DELIVERY_EXPRESS_NUM" jdbcType="INTEGER" property="deliveryExpressNum" />
    <result column="CONTENT" jdbcType="VARCHAR" property="content" />
    <result column="IS_CLOSED" jdbcType="TINYINT" property="isClosed" />
    <result column="CLOSED_COMMENTS" jdbcType="VARCHAR" property="closedComments" />
    <result column="IS_DELETED" jdbcType="TINYINT" property="isDeleted" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
    <result column="SEND_USER_TYPE" jdbcType="INTEGER" property="sendUserType" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.vedeng.oa.model.FileDeliveryNew">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    <result column="TRADER_NAME" jdbcType="LONGVARCHAR" property="traderName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    FILE_DELIVERY_ID, FILE_DELIVERY_NO, APPLY_USER_ID, APPLY_ORG_NAME, APPLY_TIME, VERIFY_STATUS, 
    DELIVERY_STATUS, DELIVERY_TIME, LOGISTICS_NAME, EXPRESS_NOS, DELIVERY_TYPE, DELIVERY_DEPT, 
    DELIVERY_TOTAL_WEIGHT, DELIVERY_PROD_NAME, DELIVERY_EXPRESS_NUM, CONTENT, IS_CLOSED, 
    CLOSED_COMMENTS, IS_DELETED, ADD_TIME, CREATOR, MOD_TIME, UPDATER, UPDATE_REMARK, 
    SEND_USER_TYPE
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    TRADER_NAME
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.vedeng.oa.model.FileDeliveryNewExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from T_FILE_DELIVERY_NEW
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.vedeng.oa.model.FileDeliveryNewExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_FILE_DELIVERY_NEW
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from T_FILE_DELIVERY_NEW
    where FILE_DELIVERY_ID = #{fileDeliveryId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    delete from T_FILE_DELIVERY_NEW
    where FILE_DELIVERY_ID = #{fileDeliveryId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.oa.model.FileDeliveryNewExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    delete from T_FILE_DELIVERY_NEW
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.oa.model.FileDeliveryNew">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    <selectKey keyProperty="fileDeliveryId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_FILE_DELIVERY_NEW (FILE_DELIVERY_NO, APPLY_USER_ID, APPLY_ORG_NAME, 
      APPLY_TIME, VERIFY_STATUS, DELIVERY_STATUS, 
      DELIVERY_TIME, LOGISTICS_NAME, EXPRESS_NOS, 
      DELIVERY_TYPE, DELIVERY_DEPT, DELIVERY_TOTAL_WEIGHT, 
      DELIVERY_PROD_NAME, DELIVERY_EXPRESS_NUM, CONTENT, 
      IS_CLOSED, CLOSED_COMMENTS, IS_DELETED, 
      ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER, UPDATE_REMARK, SEND_USER_TYPE, 
      TRADER_NAME)
    values (#{fileDeliveryNo,jdbcType=VARCHAR}, #{applyUserId,jdbcType=INTEGER}, #{applyOrgName,jdbcType=VARCHAR}, 
      #{applyTime,jdbcType=TIMESTAMP}, #{verifyStatus,jdbcType=TINYINT}, #{deliveryStatus,jdbcType=TINYINT}, 
      #{deliveryTime,jdbcType=TIMESTAMP}, #{logisticsName,jdbcType=TINYINT}, #{expressNos,jdbcType=VARCHAR}, 
      #{deliveryType,jdbcType=TINYINT}, #{deliveryDept,jdbcType=TINYINT}, #{deliveryTotalWeight,jdbcType=DECIMAL}, 
      #{deliveryProdName,jdbcType=VARCHAR}, #{deliveryExpressNum,jdbcType=INTEGER}, #{content,jdbcType=VARCHAR}, 
      #{isClosed,jdbcType=TINYINT}, #{closedComments,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}, 
      #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=TIMESTAMP}, 
      #{updater,jdbcType=INTEGER}, #{updateRemark,jdbcType=VARCHAR}, #{sendUserType,jdbcType=INTEGER}, 
      #{traderName,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.oa.model.FileDeliveryNew">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    <selectKey keyProperty="fileDeliveryId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_FILE_DELIVERY_NEW
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="fileDeliveryNo != null">
        FILE_DELIVERY_NO,
      </if>
      <if test="applyUserId != null">
        APPLY_USER_ID,
      </if>
      <if test="applyOrgName != null">
        APPLY_ORG_NAME,
      </if>
      <if test="applyTime != null">
        APPLY_TIME,
      </if>
      <if test="verifyStatus != null">
        VERIFY_STATUS,
      </if>
      <if test="deliveryStatus != null">
        DELIVERY_STATUS,
      </if>
      <if test="deliveryTime != null">
        DELIVERY_TIME,
      </if>
      <if test="logisticsName != null">
        LOGISTICS_NAME,
      </if>
      <if test="expressNos != null">
        EXPRESS_NOS,
      </if>
      <if test="deliveryType != null">
        DELIVERY_TYPE,
      </if>
      <if test="deliveryDept != null">
        DELIVERY_DEPT,
      </if>
      <if test="deliveryTotalWeight != null">
        DELIVERY_TOTAL_WEIGHT,
      </if>
      <if test="deliveryProdName != null">
        DELIVERY_PROD_NAME,
      </if>
      <if test="deliveryExpressNum != null">
        DELIVERY_EXPRESS_NUM,
      </if>
      <if test="content != null">
        CONTENT,
      </if>
      <if test="isClosed != null">
        IS_CLOSED,
      </if>
      <if test="closedComments != null">
        CLOSED_COMMENTS,
      </if>
      <if test="isDeleted != null">
        IS_DELETED,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
      <if test="sendUserType != null">
        SEND_USER_TYPE,
      </if>
      <if test="traderName != null">
        TRADER_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="fileDeliveryNo != null">
        #{fileDeliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="applyUserId != null">
        #{applyUserId,jdbcType=INTEGER},
      </if>
      <if test="applyOrgName != null">
        #{applyOrgName,jdbcType=VARCHAR},
      </if>
      <if test="applyTime != null">
        #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="verifyStatus != null">
        #{verifyStatus,jdbcType=TINYINT},
      </if>
      <if test="deliveryStatus != null">
        #{deliveryStatus,jdbcType=TINYINT},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="logisticsName != null">
        #{logisticsName,jdbcType=TINYINT},
      </if>
      <if test="expressNos != null">
        #{expressNos,jdbcType=VARCHAR},
      </if>
      <if test="deliveryType != null">
        #{deliveryType,jdbcType=TINYINT},
      </if>
      <if test="deliveryDept != null">
        #{deliveryDept,jdbcType=TINYINT},
      </if>
      <if test="deliveryTotalWeight != null">
        #{deliveryTotalWeight,jdbcType=DECIMAL},
      </if>
      <if test="deliveryProdName != null">
        #{deliveryProdName,jdbcType=VARCHAR},
      </if>
      <if test="deliveryExpressNum != null">
        #{deliveryExpressNum,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="isClosed != null">
        #{isClosed,jdbcType=TINYINT},
      </if>
      <if test="closedComments != null">
        #{closedComments,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
      <if test="sendUserType != null">
        #{sendUserType,jdbcType=INTEGER},
      </if>
      <if test="traderName != null">
        #{traderName,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.oa.model.FileDeliveryNewExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    select count(*) from T_FILE_DELIVERY_NEW
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    update T_FILE_DELIVERY_NEW
    <set>
      <if test="record.fileDeliveryId != null">
        FILE_DELIVERY_ID = #{record.fileDeliveryId,jdbcType=INTEGER},
      </if>
      <if test="record.fileDeliveryNo != null">
        FILE_DELIVERY_NO = #{record.fileDeliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="record.applyUserId != null">
        APPLY_USER_ID = #{record.applyUserId,jdbcType=INTEGER},
      </if>
      <if test="record.applyOrgName != null">
        APPLY_ORG_NAME = #{record.applyOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.applyTime != null">
        APPLY_TIME = #{record.applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.verifyStatus != null">
        VERIFY_STATUS = #{record.verifyStatus,jdbcType=TINYINT},
      </if>
      <if test="record.deliveryStatus != null">
        DELIVERY_STATUS = #{record.deliveryStatus,jdbcType=TINYINT},
      </if>
      <if test="record.deliveryTime != null">
        DELIVERY_TIME = #{record.deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.logisticsName != null">
        LOGISTICS_NAME = #{record.logisticsName,jdbcType=TINYINT},
      </if>
      <if test="record.expressNos != null">
        EXPRESS_NOS = #{record.expressNos,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryType != null">
        DELIVERY_TYPE = #{record.deliveryType,jdbcType=TINYINT},
      </if>
      <if test="record.deliveryDept != null">
        DELIVERY_DEPT = #{record.deliveryDept,jdbcType=TINYINT},
      </if>
      <if test="record.deliveryTotalWeight != null">
        DELIVERY_TOTAL_WEIGHT = #{record.deliveryTotalWeight,jdbcType=DECIMAL},
      </if>
      <if test="record.deliveryProdName != null">
        DELIVERY_PROD_NAME = #{record.deliveryProdName,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryExpressNum != null">
        DELIVERY_EXPRESS_NUM = #{record.deliveryExpressNum,jdbcType=INTEGER},
      </if>
      <if test="record.content != null">
        CONTENT = #{record.content,jdbcType=VARCHAR},
      </if>
      <if test="record.isClosed != null">
        IS_CLOSED = #{record.isClosed,jdbcType=TINYINT},
      </if>
      <if test="record.closedComments != null">
        CLOSED_COMMENTS = #{record.closedComments,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        IS_DELETED = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.addTime != null">
        ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creator != null">
        CREATOR = #{record.creator,jdbcType=INTEGER},
      </if>
      <if test="record.modTime != null">
        MOD_TIME = #{record.modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updater != null">
        UPDATER = #{record.updater,jdbcType=INTEGER},
      </if>
      <if test="record.updateRemark != null">
        UPDATE_REMARK = #{record.updateRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.sendUserType != null">
        SEND_USER_TYPE = #{record.sendUserType,jdbcType=INTEGER},
      </if>
      <if test="record.traderName != null">
        TRADER_NAME = #{record.traderName,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    update T_FILE_DELIVERY_NEW
    set FILE_DELIVERY_ID = #{record.fileDeliveryId,jdbcType=INTEGER},
      FILE_DELIVERY_NO = #{record.fileDeliveryNo,jdbcType=VARCHAR},
      APPLY_USER_ID = #{record.applyUserId,jdbcType=INTEGER},
      APPLY_ORG_NAME = #{record.applyOrgName,jdbcType=VARCHAR},
      APPLY_TIME = #{record.applyTime,jdbcType=TIMESTAMP},
      VERIFY_STATUS = #{record.verifyStatus,jdbcType=TINYINT},
      DELIVERY_STATUS = #{record.deliveryStatus,jdbcType=TINYINT},
      DELIVERY_TIME = #{record.deliveryTime,jdbcType=TIMESTAMP},
      LOGISTICS_NAME = #{record.logisticsName,jdbcType=TINYINT},
      EXPRESS_NOS = #{record.expressNos,jdbcType=VARCHAR},
      DELIVERY_TYPE = #{record.deliveryType,jdbcType=TINYINT},
      DELIVERY_DEPT = #{record.deliveryDept,jdbcType=TINYINT},
      DELIVERY_TOTAL_WEIGHT = #{record.deliveryTotalWeight,jdbcType=DECIMAL},
      DELIVERY_PROD_NAME = #{record.deliveryProdName,jdbcType=VARCHAR},
      DELIVERY_EXPRESS_NUM = #{record.deliveryExpressNum,jdbcType=INTEGER},
      CONTENT = #{record.content,jdbcType=VARCHAR},
      IS_CLOSED = #{record.isClosed,jdbcType=TINYINT},
      CLOSED_COMMENTS = #{record.closedComments,jdbcType=VARCHAR},
      IS_DELETED = #{record.isDeleted,jdbcType=TINYINT},
      ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      CREATOR = #{record.creator,jdbcType=INTEGER},
      MOD_TIME = #{record.modTime,jdbcType=TIMESTAMP},
      UPDATER = #{record.updater,jdbcType=INTEGER},
      UPDATE_REMARK = #{record.updateRemark,jdbcType=VARCHAR},
      SEND_USER_TYPE = #{record.sendUserType,jdbcType=INTEGER},
      TRADER_NAME = #{record.traderName,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    update T_FILE_DELIVERY_NEW
    set FILE_DELIVERY_ID = #{record.fileDeliveryId,jdbcType=INTEGER},
      FILE_DELIVERY_NO = #{record.fileDeliveryNo,jdbcType=VARCHAR},
      APPLY_USER_ID = #{record.applyUserId,jdbcType=INTEGER},
      APPLY_ORG_NAME = #{record.applyOrgName,jdbcType=VARCHAR},
      APPLY_TIME = #{record.applyTime,jdbcType=TIMESTAMP},
      VERIFY_STATUS = #{record.verifyStatus,jdbcType=TINYINT},
      DELIVERY_STATUS = #{record.deliveryStatus,jdbcType=TINYINT},
      DELIVERY_TIME = #{record.deliveryTime,jdbcType=TIMESTAMP},
      LOGISTICS_NAME = #{record.logisticsName,jdbcType=TINYINT},
      EXPRESS_NOS = #{record.expressNos,jdbcType=VARCHAR},
      DELIVERY_TYPE = #{record.deliveryType,jdbcType=TINYINT},
      DELIVERY_DEPT = #{record.deliveryDept,jdbcType=TINYINT},
      DELIVERY_TOTAL_WEIGHT = #{record.deliveryTotalWeight,jdbcType=DECIMAL},
      DELIVERY_PROD_NAME = #{record.deliveryProdName,jdbcType=VARCHAR},
      DELIVERY_EXPRESS_NUM = #{record.deliveryExpressNum,jdbcType=INTEGER},
      CONTENT = #{record.content,jdbcType=VARCHAR},
      IS_CLOSED = #{record.isClosed,jdbcType=TINYINT},
      CLOSED_COMMENTS = #{record.closedComments,jdbcType=VARCHAR},
      IS_DELETED = #{record.isDeleted,jdbcType=TINYINT},
      ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      CREATOR = #{record.creator,jdbcType=INTEGER},
      MOD_TIME = #{record.modTime,jdbcType=TIMESTAMP},
      UPDATER = #{record.updater,jdbcType=INTEGER},
      UPDATE_REMARK = #{record.updateRemark,jdbcType=VARCHAR},
      SEND_USER_TYPE = #{record.sendUserType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.oa.model.FileDeliveryNew">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    update T_FILE_DELIVERY_NEW
    <set>
      <if test="fileDeliveryNo != null">
        FILE_DELIVERY_NO = #{fileDeliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="applyUserId != null">
        APPLY_USER_ID = #{applyUserId,jdbcType=INTEGER},
      </if>
      <if test="applyOrgName != null">
        APPLY_ORG_NAME = #{applyOrgName,jdbcType=VARCHAR},
      </if>
      <if test="applyTime != null">
        APPLY_TIME = #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="verifyStatus != null">
        VERIFY_STATUS = #{verifyStatus,jdbcType=TINYINT},
      </if>
      <if test="deliveryStatus != null">
        DELIVERY_STATUS = #{deliveryStatus,jdbcType=TINYINT},
      </if>
      <if test="deliveryTime != null">
        DELIVERY_TIME = #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="logisticsName != null">
        LOGISTICS_NAME = #{logisticsName,jdbcType=TINYINT},
      </if>
      <if test="expressNos != null">
        EXPRESS_NOS = #{expressNos,jdbcType=VARCHAR},
      </if>
      <if test="deliveryType != null">
        DELIVERY_TYPE = #{deliveryType,jdbcType=TINYINT},
      </if>
      <if test="deliveryDept != null">
        DELIVERY_DEPT = #{deliveryDept,jdbcType=TINYINT},
      </if>
      <if test="deliveryTotalWeight != null">
        DELIVERY_TOTAL_WEIGHT = #{deliveryTotalWeight,jdbcType=DECIMAL},
      </if>
      <if test="deliveryProdName != null">
        DELIVERY_PROD_NAME = #{deliveryProdName,jdbcType=VARCHAR},
      </if>
      <if test="deliveryExpressNum != null">
        DELIVERY_EXPRESS_NUM = #{deliveryExpressNum,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        CONTENT = #{content,jdbcType=VARCHAR},
      </if>
      <if test="isClosed != null">
        IS_CLOSED = #{isClosed,jdbcType=TINYINT},
      </if>
      <if test="closedComments != null">
        CLOSED_COMMENTS = #{closedComments,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        IS_DELETED = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
      <if test="sendUserType != null">
        SEND_USER_TYPE = #{sendUserType,jdbcType=INTEGER},
      </if>
      <if test="traderName != null">
        TRADER_NAME = #{traderName,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where FILE_DELIVERY_ID = #{fileDeliveryId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.vedeng.oa.model.FileDeliveryNew">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    update T_FILE_DELIVERY_NEW
    set FILE_DELIVERY_NO = #{fileDeliveryNo,jdbcType=VARCHAR},
      APPLY_USER_ID = #{applyUserId,jdbcType=INTEGER},
      APPLY_ORG_NAME = #{applyOrgName,jdbcType=VARCHAR},
      APPLY_TIME = #{applyTime,jdbcType=TIMESTAMP},
      VERIFY_STATUS = #{verifyStatus,jdbcType=TINYINT},
      DELIVERY_STATUS = #{deliveryStatus,jdbcType=TINYINT},
      DELIVERY_TIME = #{deliveryTime,jdbcType=TIMESTAMP},
      LOGISTICS_NAME = #{logisticsName,jdbcType=TINYINT},
      EXPRESS_NOS = #{expressNos,jdbcType=VARCHAR},
      DELIVERY_TYPE = #{deliveryType,jdbcType=TINYINT},
      DELIVERY_DEPT = #{deliveryDept,jdbcType=TINYINT},
      DELIVERY_TOTAL_WEIGHT = #{deliveryTotalWeight,jdbcType=DECIMAL},
      DELIVERY_PROD_NAME = #{deliveryProdName,jdbcType=VARCHAR},
      DELIVERY_EXPRESS_NUM = #{deliveryExpressNum,jdbcType=INTEGER},
      CONTENT = #{content,jdbcType=VARCHAR},
      IS_CLOSED = #{isClosed,jdbcType=TINYINT},
      CLOSED_COMMENTS = #{closedComments,jdbcType=VARCHAR},
      IS_DELETED = #{isDeleted,jdbcType=TINYINT},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      SEND_USER_TYPE = #{sendUserType,jdbcType=INTEGER},
      TRADER_NAME = #{traderName,jdbcType=LONGVARCHAR}
    where FILE_DELIVERY_ID = #{fileDeliveryId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.oa.model.FileDeliveryNew">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    update T_FILE_DELIVERY_NEW
    set FILE_DELIVERY_NO = #{fileDeliveryNo,jdbcType=VARCHAR},
      APPLY_USER_ID = #{applyUserId,jdbcType=INTEGER},
      APPLY_ORG_NAME = #{applyOrgName,jdbcType=VARCHAR},
      APPLY_TIME = #{applyTime,jdbcType=TIMESTAMP},
      VERIFY_STATUS = #{verifyStatus,jdbcType=TINYINT},
      DELIVERY_STATUS = #{deliveryStatus,jdbcType=TINYINT},
      DELIVERY_TIME = #{deliveryTime,jdbcType=TIMESTAMP},
      LOGISTICS_NAME = #{logisticsName,jdbcType=TINYINT},
      EXPRESS_NOS = #{expressNos,jdbcType=VARCHAR},
      DELIVERY_TYPE = #{deliveryType,jdbcType=TINYINT},
      DELIVERY_DEPT = #{deliveryDept,jdbcType=TINYINT},
      DELIVERY_TOTAL_WEIGHT = #{deliveryTotalWeight,jdbcType=DECIMAL},
      DELIVERY_PROD_NAME = #{deliveryProdName,jdbcType=VARCHAR},
      DELIVERY_EXPRESS_NUM = #{deliveryExpressNum,jdbcType=INTEGER},
      CONTENT = #{content,jdbcType=VARCHAR},
      IS_CLOSED = #{isClosed,jdbcType=TINYINT},
      CLOSED_COMMENTS = #{closedComments,jdbcType=VARCHAR},
      IS_DELETED = #{isDeleted,jdbcType=TINYINT},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      SEND_USER_TYPE = #{sendUserType,jdbcType=INTEGER}
    where FILE_DELIVERY_ID = #{fileDeliveryId,jdbcType=INTEGER}
  </update>
</mapper>