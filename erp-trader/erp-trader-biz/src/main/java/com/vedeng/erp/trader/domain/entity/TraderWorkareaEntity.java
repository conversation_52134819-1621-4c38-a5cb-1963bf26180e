package com.vedeng.erp.trader.domain.entity;

import java.util.Date;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * @description 客户办公地址
 * <AUTHOR>
 * @date 2024/3/29 9:56
 **/

@Getter
@Setter
@NoArgsConstructor
public class TraderWorkareaEntity {
    /**
     * 客户ID
     */
    private Integer id;

    /**
     * 交易者ID
     */
    private Integer traderId;

    /**
     * 地址ID
     */
    private Integer areaId;

    /**
     * 省市区地址
     */
    private String areaNameAll;

    /**
     * 更新时间
     */
    private Date modTime;

    /**
     * 创建时间
     */
    private Date addTime;
}