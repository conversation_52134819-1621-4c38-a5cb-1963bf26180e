package com.vedeng.temporal.exception;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 错误分类工具类（精简版）
 * 
 * 核心功能：
 * 1. 判断异常是否可重试（技术异常 vs 业务异常）
 * 2. 基于异常类型优先，错误码次之的简化判断逻辑
 * 3. 移除复杂的三重分类，专注核心分类功能
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 3.0 (精简版)
 * @since 2025-01-26
 */
@Component
@Slf4j
public class ErrorClassifier {
    
    // ========== 技术异常前缀（可重试） ==========
    private static final Set<String> RETRYABLE_ERROR_PREFIXES = new HashSet<>(Arrays.asList(
        "NETWORK_", "TIMEOUT_", "SERVER_", "DATABASE_", "CONNECTION_", 
        "SERVICE_", "SYSTEM_", "INFRASTRUCTURE_", "API_CALL_"
    ));
    
    /**
     * 判断异常是否可重试（核心方法）
     * 
     * @param exception 原始异常对象
     * @param errorCode 错误码（可选）
     * @return true表示可重试（技术异常），false表示不可重试（业务异常）
     */
    public boolean isRetryable(Exception exception, String errorCode) {
        // 1. 优先基于异常类型判断
        Boolean retryableByType = isRetryableByExceptionType(exception);
        if (retryableByType != null) {
            log.debug("基于异常类型分类: {} -> {}", 
                exception != null ? exception.getClass().getSimpleName() : "null", retryableByType);
            return retryableByType;
        }
        
        // 2. 其次基于错误码判断
        boolean retryableByCode = isRetryableByErrorCode(errorCode);
        log.debug("基于错误码分类: {} -> {}", errorCode, retryableByCode);
        return retryableByCode;
    }

    /**
     * 基于异常类型判断是否可重试
     */
    private Boolean isRetryableByExceptionType(Exception e) {
        if (e == null) return null;
        
        String className = e.getClass().getName().toLowerCase();
        
        // 网络和连接异常 - 可重试
        if (e instanceof java.net.SocketTimeoutException ||
            e instanceof java.net.ConnectException ||
            e instanceof java.net.SocketException ||
            className.contains("timeout") ||
            className.contains("connect")) {
            return true;
        }
        
        // 数据库异常 - 可重试
        if (className.contains("sql") || 
            className.contains("database") ||
            className.contains("dataaccess")) {
            return true;
        }
        
        // 服务调用异常 - 可重试
        if (className.contains("restclient") ||
            className.contains("httpclient") ||
            className.contains("service")) {
            return true;
        }
        
        // 业务逻辑异常 - 不可重试
        if (e instanceof IllegalArgumentException ||
            e instanceof IllegalStateException ||
            className.contains("validation") ||
            className.contains("business")) {
            return false;
        }
        
        // 无法从异常类型确定
        return null;
    }
    
    /**
     * 基于错误码判断是否可重试
     */
    private boolean isRetryableByErrorCode(String errorCode) {
        if (errorCode == null || errorCode.trim().isEmpty()) {
            log.debug("错误码为空，默认不重试");
            return false;
        }
        
        String upperErrorCode = errorCode.toUpperCase();
        
        // 检查技术异常前缀
        for (String prefix : RETRYABLE_ERROR_PREFIXES) {
            if (upperErrorCode.startsWith(prefix)) {
                return true;
            }
        }
        
        // 特殊可重试错误码
        if (upperErrorCode.contains("TEMPORARY") ||
            upperErrorCode.contains("UNAVAILABLE") ||
            upperErrorCode.equals("UNKNOWN_ERROR")) {
            return true;
        }
        
        // 特殊不可重试错误码
        if (upperErrorCode.contains("VALIDATION_") ||
            upperErrorCode.contains("BUSINESS_RULE_") ||
            upperErrorCode.contains("PERMISSION_") ||
            upperErrorCode.contains("FORBIDDEN") ||
            upperErrorCode.contains("NOT_FOUND")) {
            return false;
        }
        
        // 默认不重试（保守策略）
        return false;
    }

    /**
     * 分类异常并创建BusinessProcessException（简化版）
     * 
     * @param originalException 原始异常
     * @param errorCode 错误码
     * @param businessContext 业务上下文
     * @return 分类后的业务异常
     */
    public BusinessProcessException classifyAndCreateException(Exception originalException, 
                                                             String errorCode, String businessContext) {
        if (originalException instanceof BusinessProcessException) {
            return (BusinessProcessException) originalException;
        }
        
        boolean retryable = isRetryable(originalException, errorCode);
        String finalErrorCode = errorCode != null ? errorCode : generateErrorCode(originalException);
        String message = originalException != null ? originalException.getMessage() : "未知异常";
        
        if (retryable) {
            return BusinessProcessException.retryable(message, finalErrorCode, businessContext);
        } else {
            return BusinessProcessException.nonRetryable(message, finalErrorCode, businessContext);
        }
    }

    /**
     * 生成错误码（简化版）
     */
    private String generateErrorCode(Exception exception) {
        if (exception == null) {
            return "NULL_EXCEPTION";
        }
        
        String className = exception.getClass().getSimpleName().toUpperCase();
        
        // 根据异常类型生成对应的错误码
        if (className.contains("TIMEOUT")) {
            return "NETWORK_TIMEOUT_ERROR";
        } else if (className.contains("CONNECTION")) {
            return "NETWORK_CONNECTION_ERROR";
        } else if (className.contains("SQL")) {
            return "DATABASE_ERROR";
        } else if (className.contains("ILLEGAL")) {
            return "BUSINESS_LOGIC_ERROR";
        } else {
            return "SYSTEM_" + className;
        }
    }

    // ========== 兼容性方法（向后兼容） ==========
    
    /**
     * @deprecated 使用 isRetryable(Exception, String) 方法
     */
    @Deprecated
    public boolean isRetryableError(String errorCode) {
        return isRetryableByErrorCode(errorCode);
    }

    /**
     * @deprecated 使用 classifyAndCreateException 方法
     */
    @Deprecated
    public BusinessProcessException classifyException(Exception originalException, String errorCode, 
                                                    String layerName, String contextName) {
        String context = layerName != null ? layerName + "=" + contextName : contextName;
        return classifyAndCreateException(originalException, errorCode, context);
    }
}