package com.vedeng.erp.kingdee.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import java.util.Date;

import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 金蝶物料表
 */
@Getter
@Setter
@ToString
@Table(name = "KING_DEE_MATERIAL")
public class KingDeeMaterialEntity extends BaseEntity {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer kingDeeMaterialEntityId;

    /**
     * 物料内码
     */
    private String fmaterialid;

    /**
     * 创建组织
     */
    private String fCreateOrgId;

    /**
     * 使用组织
     */
    private String fUseOrgId;

    /**
     * 物料编码
     */
    private String fNumber;

    /**
     * 名称
     */
    private String fName;

    /**
     * 规格型号
     */
    private String fSpecification;

    /**
     * fOldNumber
     */
    private String fOldNumber;

    /**
     * 订货号
     */
    private String fQzokDhh;

    /**
     * 供应商物料编码
     */
    private String fQzokGyswlbm;

    /**
     * 品牌
     */
    private String fQzokPpptext;

    /**
     * 注册证号
     */
    private String fQzokZczhtext;

    /**
     * 是否为医疗器械产品
     */
    private String fQzokYlqxtext;

    /**
     * 医疗器械产线
     */
    private String fQzokYlqxcxtext;

    /**
     * 医疗器械用途
     */
    private String fQzokYlqxyttext;

    /**
     * fQzokYlqxxgjfl
     */
    private String fQzokYlqxxgjfl;

    /**
     * 医疗器械细分类
     */
    private String fQzokYlqxxfltext;

    /**
     * 医疗器械分类
     */
    private String fQzokYlqxfltext;

    /**
     * 商品运营一级分类
     */
    private String fQzokSpyyyjfltext;

    /**
     * 商品运营二级分类
     */
    private String fQzokSpyyejfltext;

    /**
     * 商品运营三级分类
     */
    private String fQzokSpyysjfltext;

    /**
     * 商品运营四级分类
     */
    private String fQzokSpyysijfltext;

    /**
     * 商品运营五级分类
     */
    private String fQzokSpyywjfltext;

    /**
     * 非医疗器械一级分类
     */
    private String fQzokFylqxyjfltext;

    /**
     * 非医疗器械二级分类
     */
    private String fQzokFylqxejfltext;

    /**
     * 主要产品线
     */
    private String fQzokZycpx;

    /**
     * 产线划分类型
     */
    private String fQzokCxfl;

    /**
     * 基本信息 JSON
     */
    private String subheadentity;
}