package com.vedeng.dwh.model.dto;

import com.vedeng.dwh.model.generate.DwdErpOrganizationNjDfDo;
import lombok.Data;

import java.util.List;

/**
 * dwh查询erp组织架构.
 * @jira: .
 * @notes: 该返回对象必须是去重后的数据.
 * @version: 1.0.
 * @date: 2020/11/2 10:31 上午.
 * @author: Tomcat.Hui.
 */
@Data
public class DwhErpOrganizationDto extends DwdErpOrganizationNjDfDo {

    private String orgName;

    /**
     * 当前部门归属用户
     */
    private List<DwhErpUserDto> subUsers;
}
