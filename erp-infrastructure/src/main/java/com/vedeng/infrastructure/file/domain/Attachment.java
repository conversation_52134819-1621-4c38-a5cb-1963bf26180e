package com.vedeng.infrastructure.file.domain;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class Attachment implements Serializable {

    /**
     * 主键
     */
    private Integer attachmentId;

    /**
     * 附件类型
     */
    private Integer attachmentType;

    /**
     * 业务类型
     */
    private Integer attachmentFunction;

    /**
     * 关联业务id
     */
    private Integer relatedId;

    /**
     * 文件名
     */
    private String name;

    /**
     * 域名
     */
    private String domain;

    /**
     * 路径
     */
    private String uri;

    /**
     * 描述
     */
    private String alt;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否默认 0否 1是
     */
    private Integer isDefault;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 文件后缀
     */
    private String suffix;
    /**
     * 操作人名称
     */
    private String username;

    /**
     * 关联业务编号
     */
    private String relatedNo;

    /**
     * 是否有效 0有效 1失效
     */
    private Integer isDeleted;

    /**
     * oss资源标识
     */
    private String ossResourceId;

    /**
     * 原始文件路径
     */
    private String originalFilepath;

    /**
     * 同步状态
     */
    private Integer synSuccess;

    /**
     * 耗时
     */
    private Long costTime;

    /**
     * http路径
     */
    private String httpUrl;

    /**
     * 为了兼容前端上传
     */
    private String filePath;

    /**
     * 协议+域名+路径
     */
    private String wholeUrl;


}