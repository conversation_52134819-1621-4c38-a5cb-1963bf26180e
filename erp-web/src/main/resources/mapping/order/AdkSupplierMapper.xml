<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.order.dao.AdkSupplierMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.order.model.adk.TAdkSupplier">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 11 13:17:53 CST 2019.
    -->
    <id column="adk_supplier_id" jdbcType="INTEGER" property="adkSupplierId" />
    <result column="adk_supplier_code" jdbcType="VARCHAR" property="adkSupplierCode" />
    <result column="adk_supplier_name" jdbcType="VARCHAR" property="adkSupplierName" />
    <result column="add_no" jdbcType="VARCHAR" property="addNo" />
    <result column="add_name" jdbcType="VARCHAR" property="addName" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_no" jdbcType="VARCHAR" property="updateNo" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="status" jdbcType="VARCHAR" property="status" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 11 13:17:53 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 11 13:17:53 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 11 13:17:53 CST 2019.
    -->
    adk_supplier_id, adk_supplier_code, adk_supplier_name, add_no, add_name, add_time, 
    update_no, update_name, update_time, status
  </sql>
  <select id="selectByExample" parameterType="com.vedeng.order.model.adk.TAdkSupplierExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 11 13:17:53 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_ADK_SUPPLIER
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 11 13:17:53 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from T_ADK_SUPPLIER
    where adk_supplier_id = #{adkSupplierId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 11 13:17:53 CST 2019.
    -->
    delete from T_ADK_SUPPLIER
    where adk_supplier_id = #{adkSupplierId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.order.model.adk.TAdkSupplierExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 11 13:17:53 CST 2019.
    -->
    delete from T_ADK_SUPPLIER
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.order.model.adk.TAdkSupplier">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 11 13:17:53 CST 2019.
    -->
    <selectKey keyProperty="adkSupplierId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_ADK_SUPPLIER (adk_supplier_code, adk_supplier_name, 
      add_no, add_name, add_time, 
      update_no, update_name, update_time, 
      status)
    values (#{adkSupplierCode,jdbcType=VARCHAR}, #{adkSupplierName,jdbcType=VARCHAR}, 
      #{addNo,jdbcType=VARCHAR}, #{addName,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateNo,jdbcType=VARCHAR}, #{updateName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.order.model.adk.TAdkSupplier">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 11 13:17:53 CST 2019.
    -->
    <selectKey keyProperty="adkSupplierId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_ADK_SUPPLIER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="adkSupplierCode != null">
        adk_supplier_code,
      </if>
      <if test="adkSupplierName != null">
        adk_supplier_name,
      </if>
      <if test="addNo != null">
        add_no,
      </if>
      <if test="addName != null">
        add_name,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateNo != null">
        update_no,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="status != null">
        status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="adkSupplierCode != null">
        #{adkSupplierCode,jdbcType=VARCHAR},
      </if>
      <if test="adkSupplierName != null">
        #{adkSupplierName,jdbcType=VARCHAR},
      </if>
      <if test="addNo != null">
        #{addNo,jdbcType=VARCHAR},
      </if>
      <if test="addName != null">
        #{addName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateNo != null">
        #{updateNo,jdbcType=VARCHAR},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.order.model.adk.TAdkSupplierExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 11 13:17:53 CST 2019.
    -->
    select count(*) from T_ADK_SUPPLIER
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 11 13:17:53 CST 2019.
    -->
    update T_ADK_SUPPLIER
    <set>
      <if test="record.adkSupplierId != null">
        adk_supplier_id = #{record.adkSupplierId,jdbcType=INTEGER},
      </if>
      <if test="record.adkSupplierCode != null">
        adk_supplier_code = #{record.adkSupplierCode,jdbcType=VARCHAR},
      </if>
      <if test="record.adkSupplierName != null">
        adk_supplier_name = #{record.adkSupplierName,jdbcType=VARCHAR},
      </if>
      <if test="record.addNo != null">
        add_no = #{record.addNo,jdbcType=VARCHAR},
      </if>
      <if test="record.addName != null">
        add_name = #{record.addName,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateNo != null">
        update_no = #{record.updateNo,jdbcType=VARCHAR},
      </if>
      <if test="record.updateName != null">
        update_name = #{record.updateName,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 11 13:17:53 CST 2019.
    -->
    update T_ADK_SUPPLIER
    set adk_supplier_id = #{record.adkSupplierId,jdbcType=INTEGER},
      adk_supplier_code = #{record.adkSupplierCode,jdbcType=VARCHAR},
      adk_supplier_name = #{record.adkSupplierName,jdbcType=VARCHAR},
      add_no = #{record.addNo,jdbcType=VARCHAR},
      add_name = #{record.addName,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_no = #{record.updateNo,jdbcType=VARCHAR},
      update_name = #{record.updateName,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.order.model.adk.TAdkSupplier">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 11 13:17:53 CST 2019.
    -->
    update T_ADK_SUPPLIER
    <set>
      <if test="adkSupplierCode != null">
        adk_supplier_code = #{adkSupplierCode,jdbcType=VARCHAR},
      </if>
      <if test="adkSupplierName != null">
        adk_supplier_name = #{adkSupplierName,jdbcType=VARCHAR},
      </if>
      <if test="addNo != null">
        add_no = #{addNo,jdbcType=VARCHAR},
      </if>
      <if test="addName != null">
        add_name = #{addName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateNo != null">
        update_no = #{updateNo,jdbcType=VARCHAR},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
    </set>
    where adk_supplier_id = #{adkSupplierId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.order.model.adk.TAdkSupplier">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 11 13:17:53 CST 2019.
    -->
    update T_ADK_SUPPLIER
    set adk_supplier_code = #{adkSupplierCode,jdbcType=VARCHAR},
      adk_supplier_name = #{adkSupplierName,jdbcType=VARCHAR},
      add_no = #{addNo,jdbcType=VARCHAR},
      add_name = #{addName,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_no = #{updateNo,jdbcType=VARCHAR},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=VARCHAR}
    where adk_supplier_id = #{adkSupplierId,jdbcType=INTEGER}
  </update>
</mapper>