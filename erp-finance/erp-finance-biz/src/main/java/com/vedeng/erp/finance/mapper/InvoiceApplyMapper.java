package com.vedeng.erp.finance.mapper;
import com.vedeng.erp.finance.dto.InvoiceApplyDto;
import com.vedeng.erp.finance.dto.InvoiceApplyReasonSnapshotDto;
import org.apache.ibatis.annotations.Param;

import com.vedeng.erp.finance.domain.entity.InvoiceApplyEntity;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository("newInvoiceApplyMapper")
public interface InvoiceApplyMapper {
    /**
     * @param invoiceApplyId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer invoiceApplyId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(InvoiceApplyEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(InvoiceApplyEntity record);

    /**
     * select by primary key
     * @param invoiceApplyId primary key
     * @return object by primary key
     */
    InvoiceApplyEntity selectByPrimaryKey(Integer invoiceApplyId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(InvoiceApplyEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(InvoiceApplyEntity record);

    int updateBatchSelective(List<InvoiceApplyEntity> list);



    List<InvoiceApplyDto> getWaitInvoiceApply(@Param(value = "saleOrderNo")String saleOrderNo);

    /**
     * 查询安调待开票申请
     * @return
     */
    List<InvoiceApplyDto> getAtWaitInvoiceApply(@Param("afterSalesNo") String afterSalesNo);

    /**
     * 根据开票申请id
     * @param invoiceApplyId
     * @return
     */
    InvoiceApplyDto getInvoiceApply(Integer invoiceApplyId);

    /**
     * 根据安调开票申请id
     * @param invoiceApplyId
     * @return
     */
    InvoiceApplyDto getAtInvoiceApply(Integer invoiceApplyId);


    /**
     * 查询订单号
     * @param invoiceApplyId
     * @return
     */
    String getSaleOrderNoByInvoiceApplyId(Integer invoiceApplyId);

    /**
     * 查询售后单号
     * @param invoiceApplyId
     * @return
     */
    String getAfterSalesNoByInvoiceApplyId(Integer invoiceApplyId);

    /**
     * 查询售后订单最近一次开票申请记录
     *
     * @param relatedId
     * @return
     */
    InvoiceApplyDto getAftersaleInvoiceApplyByRelatedIdLast(@Param(value = "relatedId") Integer relatedId);

    /**
     * 根据关联id和类型查询开票申请
     *
     * @param relatedId
     * @param type
     * @return
     */
    List<InvoiceApplyDto> getByRelatedIdAndType(@Param("relatedId") Integer relatedId, @Param("type") Integer type);

    List<InvoiceApplyDto> getByAdvanceAndAdvanceValidStatus(@Param("advance") Integer advance, @Param("advanceValidStatus") Integer advanceValidStatus);

    List<InvoiceApplyDto> queryAdvanceInvoiceApply();

    /**
     * 根据entity查询
     * @param entity
     * @return
     */
    List<InvoiceApplyEntity> selectInvoiceApplyByEntity(InvoiceApplyEntity entity);


    List<InvoiceApplyReasonSnapshotDto> getApplyReasonSnapshot(Integer invoiceApplyId);

    BigDecimal getAppliedNum(Integer saleorderGoodsId);
    BigDecimal getInvoicedNum(Integer saleorderGoodsId);

    List<InvoiceApplyDto> getApplyNoPeerBySalesId(Integer saleOrderId);

    List<InvoiceApplyDto> getSaleOrderInvoiceApplyAdvanceData();

    List<InvoiceApplyDto> getAfterSalesInvoiceApplyAdvanceData();

    List<InvoiceApplyDto> getSaleOrderInvoiceApplyData();

    List<InvoiceApplyDto> getSaleOrderInvoiceApplyDataById(@Param("invoiceApplyId") Integer invoiceApplyId);

    List<InvoiceApplyDto> getAfterSalesInvoiceApplyData();

    BigDecimal getInvoicedNumAfter(@Param("detailgoodsId") Integer detailgoodsId);
}