package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesDto;
import org.apache.commons.collections4.Get;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BatchAfterSalesDtoMapper {


    /**
     * 查询售后单
     * @param afterSalesNo 采购单号
     * @param subjectType 类型
     * @return
     */
    BatchAfterSalesDto findByAfterSalesNoAndSubjectType(@Param("afterSalesNo")String afterSalesNo, @Param("subjectType")Integer subjectType);


    BatchAfterSalesDto findByAfterSalesId(@Param("afterSalesId")Integer afterSalesId);

    /**
     * 查询销售售后单
     * @param afterSalesNo 销售单号
     * @param subjectType 类型
     * @return
     */
    BatchAfterSalesDto findSaleByAfterSalesNoAndSubjectType(@Param("afterSalesNo")String afterSalesNo, @Param("subjectType")Integer subjectType);

    BatchAfterSalesDto findByAfterSalesIdAndSubjectType(@Param("afterSalesId")Integer afterSalesId,@Param("subjectType")Integer subjectType);

    /**
     * 查找售后退货且已完结的售后单
     * @param batchAfterSalesDto
     * @return
     */
    List<BatchAfterSalesDto> findSaleBackBySaleorderId(BatchAfterSalesDto batchAfterSalesDto);

    List<BatchAfterSalesDto> findByOrderIdAndTypeAndSubjectType(@Param("orderId")Integer orderId,
                                                                @Param("type")Integer type,
                                                                @Param("subjectType")Integer subjectType);



    BatchAfterSalesDto findByAfterSalesNo(@Param("afterSalesNo")String afterSalesNo);

    /**
     * 获取售后安调供应商
     * @param afterSalesNo
     * @return
     */
    List<BatchAfterSalesDto> getAfterTraderSuplier(String afterSalesNo);

    Integer getTraderCustomerIdByAfterSalesId(Integer afterSalesId);

    /**
     * 逆向销售调整单捞取数据
     */
    List<BatchAfterSalesDto> queryAfterSaleAdjustPriceNew(BatchAfterSalesDto batchAfterSalesDto);

    List<Integer> selectBySubjectTypeAndOrderNo(@Param("subjectType")Integer subjectType,@Param("orderNo")String orderNo);


    List<Integer> getNonGiftAfterOrders(@Param("afterSalesGoodsIds" ) List<Integer> afterSalesGoodsIds);

}
