package com.vedeng.logistics.model;

import lombok.Data;

import java.io.Serializable;

@Data
public class WarehouseGoodsOutInItemSn implements Serializable {

    /**
     * 主键
     */
    private Long warehouseGoodsOutInDetailId;

    /**
     * ERP出入库单号
     */
    private String outInNo;

    /**
     * 关联单号
     */
    private String relateNo;

    /**
     * 出入库类型 1入库 2出库 3销售换货入库 4销售换货出库 5销售退货入库 6采购退货出库 7采购换货出库 8采购换货入库 9外借入库 10外借出库 11调整盘盈入库 12盘盈入库 13报废出库 14领用出库 15 调整盘亏 16 盘亏出库
     */
    private Integer outInType;

    private String sku;

    /**
     * SN码
     */
    private String barcodeFactory;
}
