package com.wms.service.stockcalculate;

import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.wms.dto.StockCalculateDto;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 外接单审核通过
 */
@Service
public class LendAuditPassCaculateImpl extends AbstractStockinfoCaculateInterace{

    @Override
    protected void calculateStockNum(List<StockCalculateDto> details, List<WarehouseDto> warehouseStockList) {

    }

    @Override
    protected void calculateOcupyNum(List<StockCalculateDto> details, List<WarehouseDto> warehouseStockList) {

        /*details.stream().forEach(stockCalculateDto->{

            WarehouseDto warehouseStock = super.getWarehouseStockBySkuAndLogcic(warehouseStockList,
                    stockCalculateDto.getSku(),
                    stockCalculateDto.getLogicalWarehouseId());

            //增加占用库存数量
            warehouseStock.setOccupyNum(Math.abs(stockCalculateDto.getOccupyNum()));
        });*/
        super.calculateStockOrOccupyNumBase(details,warehouseStockList,false,false);

    }
}
