package com.vedeng.activiti.taskassign;

import com.pricecenter.constant.VerifyStatusEnum;
import com.wms.service.WmsSampleOutService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

/**
 * 出库单审核完成时候的监听器
 */
public class SampleOutAuditFinishExecutionListener implements ExecutionListener {
    private static Logger logger = LoggerFactory.getLogger(SampleOutAuditFinishExecutionListener.class);
    // 运行时注入service
    public static WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();

    private WmsSampleOutService wmsSampleOutService;

    public SampleOutAuditFinishExecutionListener() {
        wmsSampleOutService = (WmsSampleOutService) context.getBean(WmsSampleOutService.class);
    }

    @Override
    public void notify(DelegateExecution execution) throws Exception {

        String businessKey = execution.getVariable("businessKey").toString();
        long sampleOrderId = Long.valueOf(businessKey.split("_")[1]);
        boolean pass = Boolean.valueOf(execution.getVariable("pass").toString());

        logger.info("样品出库单监听下发wms sampleOrderId:{},pass:{}", sampleOrderId, pass);

        //更新审核状态
        wmsSampleOutService.updateLendOutAuditStatus(sampleOrderId, pass ? VerifyStatusEnum.Approved.getValue() : VerifyStatusEnum.Reject.getValue());

        //样品申请单审核通过，将单据同步到WMS
        if (pass) {
            logger.info("样品申请单审核通过，将单据同步到WMS sampleOrderId:{}", sampleOrderId);
            wmsSampleOutService.sampleOutOrderAuditPass(sampleOrderId);
        }
    }
}
