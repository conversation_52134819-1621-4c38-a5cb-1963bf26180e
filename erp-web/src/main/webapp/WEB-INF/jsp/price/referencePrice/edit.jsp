<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="编辑" scope="application"/>
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript">

    function checkPrice(tdPrice) {

        if(tdPrice.value == ""){
            return;
        }

        if(!validatorPrice(tdPrice.value, ["价格不能为空","价格不能为0", "价格必须是数字，最多2位小数", "价格不能超过3亿"])){
            return;
        }

        if (!validatorFormat(tdPrice.value,
            ["价格不符合以下定价规范：0<单价＜10，可保留2位小数。【例:6.99】",
                "价格不符合以下定价规范：10≤单价＜100，只可保留1位小数，且小数只能为8或9 【例：69.9】",
                "价格不符合以下定价规范：100≤单价＜1000，只能填写整数，且个位数仅限于0、5、8、9 【例：135】",
                "价格不符合以下定价规范：1000≤单价＜100000，只能填写整数，且个位数仅限于0、8、9 【例：19999】",
                "价格不符合以下定价规范：单价超过100000时，只能填写整数，且个位、十位必须为0 【例：158000】"])) {
            return;
        }
    }

    function addSubmit() {

        if (!validatorPrice($("#referenceTerminalPrice").val(), ["终端参考价不能为0", "终端参考价必须是数字，最多2位小数", "终端参考价不能超过3亿"])) {
            return;
        }

        if (!validatorPrice($("#referenceDistributionPrice").val(), ["经销参考价不能为0","经销参考价必须是数字，最多2位小数", "经销参考价不能超过3亿"])) {
            return;
        }

        if (!validatorPrice($("#referenceGroupPrice").val(), ["集团参考价不能为0","集团参考价必须是数字，最多2位小数", "集团参考价不能超过3亿"])) {
            return;
        }

        if (!validatorFormat($("#referenceTerminalPrice").val(),
            ["终端参考价不符合以下定价规范，是否继续提交？规范：0<单价＜10，可保留2位小数。【例:6.99】",
                "终端参考价不符合以下定价规范，是否继续提交？规范：10≤单价＜100，只可保留1位小数，且小数只能为8或9 【例：69.9】",
                "终端参考价不符合以下定价规范，是否继续提交？规范：100≤单价＜1000，只能填写整数，且个位数仅限于0、5、8、9 【例：135】",
                "终端参考价不符合以下定价规范，是否继续提交？规范：1000≤单价＜100000，只能填写整数，且个位数仅限于0、8、9 【例：19999】",
                "终端参考价不符合以下定价规范，是否继续提交？规范：单价超过100000时，只能填写整数，且个位、十位必须为0 【例：158000】"])) {
            return;
        }
        if (!validatorFormat($("#referenceDistributionPrice").val(),
            ["经销参考价不符合以下定价规范，是否继续提交？规范：0<单价＜10，可保留2位小数。【例:6.99】",
                "经销参考价不符合以下定价规范，是否继续提交？规范：10≤单价＜100，只可保留1位小数，且小数只能为8或9 【例：69.9】",
                "经销参考价不符合以下定价规范，是否继续提交？规范：100≤单价＜1000，只能填写整数，且个位数仅限于0、5、8、9 【例：135】",
                "经销参考价不符合以下定价规范，是否继续提交？规范：1000≤单价＜100000，只能填写整数，且个位数仅限于0、8、9 【例：19999】",
                "经销参考价不符合以下定价规范，是否继续提交？规范：单价超过100000时，只能填写整数，且个位、十位必须为0 【例：158000】"])) {
            return;
        }
        if (!validatorFormat($("#referenceGroupPrice").val(),
            ["集团参考价不符合以下定价规范，是否继续提交？规范：0<单价＜10，可保留2位小数。【例:6.99】",
                "集团参考价不符合以下定价规范，是否继续提交？规范：10≤单价＜100，只可保留1位小数，且小数只能为8或9 【例：69.9】",
                "集团参考价不符合以下定价规范，是否继续提交？规范：100≤单价＜1000，只能填写整数，且个位数仅限于0、5、8、9 【例：135】",
                "集团参考价不符合以下定价规范，是否继续提交？规范：1000≤单价＜100000，只能填写整数，且个位数仅限于0、8、9 【例：19999】",
                "集团参考价不符合以下定价规范，是否继续提交？规范：单价超过100000时，只能填写整数，且个位、十位必须为0 【例：158000】"])) {
            return;
        }

        $("#addForm").submit();
    }

    function validatorPrice(price, errorTips) {

        if (price.length == 0) {
            return true;
        }

        if (price == "0") {
            layer.alert(errorTips[0]);
            return false;
        }

        var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
        if (!reg.test(price)) {
            layer.alert(errorTips[1]);
            return false;
        }

        if (Number(price) > 300000000) {
            layer.alert(errorTips[2]);
            return false;
        }

        return true;

    }

    //校验终端参考价、经销参考价、集团参考价
    function validatorFormat(price, errorTips) {
        debugger;
        var priceNum  = Number(price);
        if (priceNum > 0 && priceNum < 10) {
            if (price.indexOf(".") == -1) {
                return true;
            }
            var index = price.lastIndexOf(".");
            var decimalNumber = price.substring(index + 1, price.length);
            if (decimalNumber.length > 2) {
                layer.alert(errorTips[0]);
                return false;
            }
        } else if (priceNum >= 10 && priceNum < 100) {
            if (price.indexOf(".") == -1) {
                return true;
            }
            var index = price.lastIndexOf(".");
            var decimalNumber = price.substring(index + 1, price.length);
            if (decimalNumber.length > 1) {
                layer.alert(errorTips[1]);
                return false;
            }
            var lastChar = decimalNumber.substr(decimalNumber.length - 1, 1);
            if (lastChar != 8 && lastChar != 9) {
                layer.alert(errorTips[1]);
                return false;
            }
        } else if (priceNum >= 100 && priceNum < 1000) {
            if (price.indexOf(".") == -1) {
                var lastChar = price.substr(price.length - 1, 1);
                if (lastChar != 0 && lastChar != 5 && lastChar != 8 && lastChar != 9) {
                    layer.alert(errorTips[2]);
                    return false;
                } else {
                    return true;
                }
            }
            layer.alert(errorTips[2]);
            return false;
        } else if (priceNum >= 1000 && priceNum < 100000) {
            if (price.indexOf(".") == -1) {
                var lastChar = price.substr(price.length - 1, 1);
                if (lastChar != 0 && lastChar != 8 && lastChar != 9) {
                    layer.alert(errorTips[3]);
                    return false;
                } else {
                    return true;
                }
            }
            layer.alert(errorTips[3]);
            return false;
        } else {
            if (priceNum % 100 == 0) {
                return true;
            }
            layer.alert(errorTips[4]);
            return false;
        }
        return true;
    }


    //显示悬浮层
    function showInform() {
        $("div").css("top",$("#salePrice").css("top"));
        $("#inform").show();
    }

    //隐藏悬浮层
    function hiddenInform() {
        $("#inform").hide();
    }

</script>
<style>
    #inform {
        position: absolute;
        top: 425px;
        left: 50px;
        width: 450px;
        max-height: 250px; /* 设置最大高度，当高度达到此值时出现滚动条 */
        z-index: 10;
        background-color: white;
        overflow: auto; /* 自动添加滚动条 */
        box-shadow: 0px 0px 10px #000; /* 外阴影 */
        display: none; /* 默认隐藏 */
    }

</style>
<div class="content">
    <div class="formtitle"></div>
    <div id="desc_div">
        <form method="post" id="addForm" action="${pageContext.request.contextPath}/price/referencePrice/editReferencePrice.do">

            <input type="hidden" name="skuNo" id="skuNo" value="${skuInfo.skuNo}"/>
            <input type="hidden" name="referencePriceId" id="referencePriceId" value="${referencePriceResponseDto.id}"/>

            <ul class="payplan">

                <li>
                    <div class="title-container title-container-blue">
                        <div class="table-title nobor">基本信息</div>
                    </div>
                    <div class="parts">
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                                <tr>
                                    <th style="width:50px">订货号</th>
                                    <th style="width:80px">商品名称</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td>${skuInfo.skuNo}</td>
                                    <td>${skuInfo.showName}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </li>


                <div>
                    <div class="title-container title-container-blue" id="salePrice">
                        <div class="table-title nobor">参考价</div>
                        &nbsp;&nbsp;
                        <img src="<%= basePath%>static/new/img/wenhao.png" onMouseOver="showInform();" onMouseOut="hiddenInform();" style="margin-top:8px;width: 18px;height: 18px"/>
                    </div>
                    &nbsp;
                    <div id="inform" onMouseOut="hiddenInform()">
                        <p style="margin-left: 10px;margin-top: 5px;font-size:18px;font-family:Arial;font-weight: bolder">参考价填写规范：</p>
                        <p style="margin-left: 20px">0 < 单价 < 10，可保留2位小数。【例:6.99】</p>
                        <p style="margin-left: 20px">10 <= 单价 < 100，只可保留1位小数，且小数只能为8或9 【例:69.9】</p>
                        <p style="margin-left: 20px">100 <= 单价 < 1000，只能填写整数，且个数仅限于0、5、8、9 【例:135】</p>
                        <p style="margin-left: 20px">1000 <= 单价 < 100000，只能填写整数，且个位数仅限于0、8、9 【例:19999】</p>
                        <p style="margin-left: 20px">单价超过100000时，只能填写整数，且个位、十位必须为0 【例:158000】</p>
                    </div>

                    <table class="table  table-bordered table-striped table-condensed table-centered">
                        <thead>
                        <tr>
                            <th style="width:50px">类型</th>
                            <th style="width:80px">价格</th>
                            <th style="width:80px">生效时间</th>
                        </tr>
                        </thead>
                            <tr>
                                <td>终端参考价</td>
                                <td>
                                    <input type="text" style="width:100px;" id="referenceTerminalPrice" name="referenceTerminalPrice" onblur="checkPrice(this)"
                                           value="${referencePriceResponseDto.referenceTerminalPrice == null? "" : myfn:toString(referencePriceResponseDto.referenceTerminalPrice)}"/>&nbsp;元/${unitName}
                                </td>
                                <td>${firstEffectTime}</td>
                            </tr>
                            <tr>
                                <td>经销参考价</td>
                                <td>
                                    <input type="text" style="width:100px;" id="referenceDistributionPrice" name="referenceDistributionPrice" onblur="checkPrice(this)"
                                           value="${referencePriceResponseDto.referenceDistributionPrice == null? "" : myfn:toString(referencePriceResponseDto.referenceDistributionPrice)}"/>&nbsp;元/${unitName}
                                </td>
                                <td>${firstEffectTime}</td>
                            </tr>
                            <tr>
                                <td>集团参考价</td>
                                <td>
                                    <input type="text" style="width:100px;" id="referenceGroupPrice" name="referenceGroupPrice" onblur="checkPrice(this)"
                                           value="${referencePriceResponseDto.referenceGroupPrice == null? "" : myfn:toString(referencePriceResponseDto.referenceGroupPrice)}"/>&nbsp;元/${unitName}
                                </td>

                                <td>${firstEffectTime}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </ul>
            <div class="add-tijiao tcenter mt10">
                <button type="button" class="bt-bg-style bg-deep-green" onclick="addSubmit();">提交</button>
            </div>
        </form>
    </div>
</div>
<%@ include file="../../common/footer.jsp" %>