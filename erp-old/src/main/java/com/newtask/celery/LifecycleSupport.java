package com.newtask.celery;

import org.springframework.context.SmartLifecycle;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: Cherny.chen
 * @Create: 2021/7/12 12:32
 */
abstract class LifecycleSupport implements SmartLifecycle {

    private volatile boolean closed = false;

    private static final Map<Long, Integer> JOB_STATUS_MAP = new ConcurrentHashMap<>();

    @Override
    public boolean isAutoStartup() {
        return true;
    }

    @Override
    public void stop(Runnable callback) {
    }

    @Override
    public void start() {
        if (!isClose()) {
            onStart();
        }
        closed = false;
    }

    @Override
    public void stop() {
        if (!isClose()) {
            onStop();
        }
        closed = true;
    }

    protected boolean isClose() {
        return closed;
    }

    @Override
    public boolean isRunning() {
        return isClose();
    }

    @Override
    public int getPhase() {
        return 0;
    }

    /**
     * 在容器所有bean加载和初始化完毕执行
     */
    protected abstract void onStart();


    /**
     * 在容器关闭时执行
     */
    protected abstract void onStop();


    protected static void putStatus(Long jobId, Integer status) {
        JOB_STATUS_MAP.put(jobId, status);
    }

    protected static Integer getStatus(Long jobId) {
        return JOB_STATUS_MAP.get(jobId);
    }

    protected static void removeStatus(Long jobId) {
        JOB_STATUS_MAP.remove(jobId);
    }


}
