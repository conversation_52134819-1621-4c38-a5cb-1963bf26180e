package com.vedeng.erp.saleorder.dao;

import com.vedeng.erp.saleorder.model.po.RegionJd;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RegionJdMapper {
    int deleteByPrimaryKey(String id);

    int insert(RegionJd record);

    int insertOrUpdate(RegionJd record);

    int insertOrUpdateSelective(RegionJd record);

    int insertSelective(RegionJd record);

    RegionJd selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(RegionJd record);

    int updateByPrimaryKey(RegionJd record);

    int updateBatch(List<RegionJd> list);

    int updateBatchSelective(List<RegionJd> list);

    int batchInsert(@Param("list") List<RegionJd> list);

    List<RegionJd> selectJdToVdRegion(List<String> list);

    RegionJd findFirstByVdRegionId(@Param("vdRegionId")Integer vdRegionId);



}