package com.wms.unitconversionorder.web.controller;

import com.pricecenter.constant.VerifyStatusEnum;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.authorization.model.User;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.StringUtil;
import com.vedeng.erp.aftersale.dto.ExpenseAfterSalesDto;
import com.vedeng.erp.buyorder.dto.AuditRecordDto;
import com.vedeng.system.service.UserService;
import com.vedeng.system.service.VerifiesRecordService;
import com.wms.unitconversionorder.model.dto.WmsUnitConversionOrderDto;
import com.wms.unitconversionorder.service.WmsUnitConversionOrderService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 库存转换换单 页面跳转
 * @date 2023/2/17 11:01
 **/
@Controller
@RequestMapping("/wmsUnitConversion")
@Slf4j
public class WmsUnitConversionOrderController {

    @Autowired
    @Qualifier("actionProcdefService")
    private ActionProcdefService actionProcdefService;

    @Autowired
    @Qualifier("verifiesRecordService")
    private VerifiesRecordService verifiesRecordService;

    @Autowired
    private WmsUnitConversionOrderService wmsUnitConversionOrderService;

    @Autowired
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    /**
     * 新增/编辑页
     *
     * @param wmsUnitConversionOrderId 库存转换换单id
     * @return ModelAndView
     */
    @RequestMapping(value = "/edit")
    public ModelAndView edit(@RequestParam(required = false) Integer wmsUnitConversionOrderId) {
        ModelAndView mv = new ModelAndView("vue/view/wmsunitconversionorder/edit");
        mv.addObject("wmsUnitConversionOrderId", wmsUnitConversionOrderId);
        return mv;
    }

    /**
     * 详情页
     *
     * @param wmsUnitConversionOrderId 库存转换换单id
     * @return ModelAndView
     */
    @RequestMapping(value = "/detail")
    @NoNeedAccessAuthorization
    public ModelAndView detail(Integer wmsUnitConversionOrderId) {
        ModelAndView mv = new ModelAndView("vue/view/wmsunitconversionorder/detail");
        mv.addObject("wmsUnitConversionOrderId", wmsUnitConversionOrderId);
        return mv;
    }

    /**
     * 库存转换单 审核操作
     *
     * @param request                  HttpServletRequest
     * @param taskId                   审核流程taskId
     * @param comment                  审核备注
     * @param pass                     是否通过
     * @param wmsUnitConversionOrderId 库存转换换单id
     * @return ResultInfo
     */
    @ResponseBody
    @RequestMapping(value = "/doComplement")
    public ResultInfo<?> complementAfterSaleTask(HttpServletRequest request, String taskId, String comment, Boolean pass, Integer wmsUnitConversionOrderId) {
        CurrentUser user = CurrentUser.getCurrentUser();
        Map<String, Object> variables = new HashMap<>();
        variables.put("pass", pass);
        ResultInfo backResultInfo = new ResultInfo(0, "操作成功", wmsUnitConversionOrderId);
        //审批操作
        try {
            if (!pass) {
                //如果未结束添加审核对应主表的审核状态
                verifiesRecordService.saveVerifiesInfo(taskId, 2);
                //更新主表审核状态
                wmsUnitConversionOrderService.audit(wmsUnitConversionOrderId, VerifyStatusEnum.Reject.getValue());
            }
            ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, comment, user.getUsername(), variables);
            //如果审核没结束添加审核对应主表的审核状态
            if (!"endEvent".equals(complementStatus.getData())) {
                int status;
                if (pass) {
                    //如果审核通过
                    status = 0;
                } else {
                    //如果审核不通过
                    status = 2;
                }
                verifiesRecordService.saveVerifiesInfo(taskId, status);
            }
            return backResultInfo;
        } catch (Exception e) {
            log.error("库存转换单申请审核失败:" + wmsUnitConversionOrderId, e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }
    }

    /**
     * 获取最近的一次审核记录
     *
     * @param wmsUnitConversionOrderId 库存转换换单id
     * @return R<?>
     */
    @RequestMapping("/getVerifyInfo")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<?> getWmsUnitConversionVerifyInfo(@RequestParam Integer wmsUnitConversionOrderId) {
        CurrentUser user = CurrentUser.getCurrentUser();
        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "wmsUnitConversionVerify" + "_" + wmsUnitConversionOrderId);
        Map commentMap = (Map) historicInfo.get("commentMap");
        List<HistoricActivityInstance> historicActivityInstance = ((List<HistoricActivityInstance>) historicInfo.get("historicActivityInstance")).stream()
                .filter(v -> StringUtil.isNotBlank(v.getActivityName())).collect(Collectors.toList());
        List<AuditRecordDto> auditRecordList = historicActivityInstance.stream().map(item -> {
            AuditRecordDto temp = new AuditRecordDto();
            temp.setOperator(item.getAssignee());
            temp.setOperation(item.getActivityName());
            temp.setOperationTime(item.getEndTime());

            String remark = (String) commentMap.get(item.getTaskId());
            temp.setRemark(remark == null ? "" : remark);
            return temp;
        }).collect(Collectors.toList());
        // 获取候选人组
        Task taskInfoPay = (Task) historicInfo.get("taskInfo");
        String verifyUsersPay = null;
        if (null != taskInfoPay) {
            Map candidateUserMap = (Map) historicInfo.get("candidateUserMap");
            //获取审核人候选组
            List<IdentityLink> candidateUserList = (List<IdentityLink>) candidateUserMap.get(taskInfoPay.getId());
            if (CollectionUtils.isNotEmpty(candidateUserList)) {
                List<String> userNameList = candidateUserList.stream().map(IdentityLink::getUserId).collect(Collectors.toList());
                verifyUsersPay = StringUtils.join(userNameList, ",");
            }
        }
        auditRecordList.get(historicActivityInstance.size() -1).setOperator(verifyUsersPay);
        Map<String, Object> result = new HashMap<>(5);
        result.put("auditRecordList", auditRecordList);
        result.put("taskId", historicActivityInstance.get(historicActivityInstance.size() -1).getTaskId());
        result.put("isAuditUser", verifyUsersPay != null && verifyUsersPay.contains(user.getUsername()));

        WmsUnitConversionOrderDto viewData = wmsUnitConversionOrderService.getViewData(wmsUnitConversionOrderId);
        boolean isOperator = viewData.getCreator().equals(user.getId());
        result.put("isOperator", isOperator);
        return R.success(result);
    }

}
