package com.vedeng.crm.visitrecord.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.Date;

@Data
public class VisitTongxingUserVo {
    /** 序号 */
    private Integer id;
    
    /** 拜访记录ID */
    private Integer recordId;
    
    /** 同行人ID */
    private Integer tongxingUserId;

    private String tongxingJobNumber;
    
    /** 同行人姓名 */
    private String userName;

    /**
     * 同行人的头像
     */
    private String aliasHeadPicture;
    
    /** 添加时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date addTime;
    
    /** 添加人 */
    private Integer addUserId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modTime;

    private Integer modUserId;

} 