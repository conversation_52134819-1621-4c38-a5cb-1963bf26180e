package com.vedeng.erp.confirmrecord.dao;


import com.vedeng.erp.confirmrecord.dto.SignInRecordDto;
import com.vedeng.erp.confirmrecord.model.ConfirmRecord;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface ConfirmRecordDao {
    int deleteByPrimaryKey(Long id);

    int insert(ConfirmRecord record);

    int insertSelective(ConfirmRecord record);

    ConfirmRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ConfirmRecord record);

    int updateByPrimaryKey(ConfirmRecord record);

    List<ConfirmRecord> queryByBusinessNo(@Param("saleorderNo") String saleorderNo);

    int selectTodayNum(@Param("saleorderNo") String saleorderNo,@Param("nowTime") String nowTime);

    int selectSendCount(@Param("saleorderNo")String saleorderNo);

    List<SignInRecordDto> queryCustomerSignature(@Param("saleorderId") Integer saleorderId);

    void updateConfirmTimeAndStatus(ConfirmRecord confirmRecord);

    void updateConfirmInfo(@Param("list") List<ConfirmRecord> confirmRecordList);

    ConfirmRecord queryConfirmRecordInfoByBusinessNo(@Param("businessNo") String businessNo);
}