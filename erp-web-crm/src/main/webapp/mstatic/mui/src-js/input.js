Vue.component('ui-input', {
    template: `<div class="vd-ui-input-wrap">
        <input class="vd-ui-input" 
            :type="inputType" 
            :class="{
                'border': border
            }"
            v-model="inputValue" 
            :placeholder="placeholder" 
            :disabled="disabled"
            :readonly="readonly"
            :maxlength="maxlength"
            @input="handlerInput"
            @change="handlerChange"
            @focus="handlerFocus"
            @blur="handlerBlur"
            @keydown="handlerKeydown"
            @keyup="handlerKeyup"
            @compositionend="commentPressEnd"
            @compositionstart="commentPressStart"
            autocomplete="off"
        />
        <i class="vd-ui_icon icon icon-error2" v-if="clear && isFocus && inputValue" @click="handlerClear"></i>
        <slot></slot>
    </div>`,
    props: {
        border: {
            type: Boolean,
            default: false
        },
        size: {
            type: String,
            default: '' //small
        },
        placeholder: {
            type: String,
            default: ''
        },
        type: {
            type: String,
            default: 'text'
        },
        maxlength: {
            type: String,
            default: ''
        },
        value: {
            type: [String, Number],
            default: ''
        },
        clear: {
            type: Boolean,
            default: false
        },
        disabled: {
            type: <PERSON>olean,
            default: false,
        },
        readonly: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            inputValue: '',
            inputType: '',
            isFocus: false,
            onCompressing: false
        };
    },
    computed: {

    },
    watch: {
        value: {
            handler (newV) {
                this.inputValue = newV;
            },
            immediate: true
        }
    },
    mounted() {
        if (this.type === 'number') {
            this.inputType = 'text';
        } else {
            this.inputType = this.type;
        }
    },
    methods: {
        handlerInput(e) {
            let targetValue = e && e.target && e.target.value || ''
            if (this.type == 'tel') {
                let val = targetValue.replace(/[^\d]/g, '');
                this.inputValue = val;
            }

            if (this.onCompressing) {
                return;
            }

            this.$emit('input', this.inputValue);
            this.$emit('change', this.inputValue);
        },
        handlerChange(e) {
            this.$emit('change', e);
        },
        handlerFocus(e) {
            this.isFocus = true;
            this.$emit('focus', e);
        },
        handlerBlur(e) {
            setTimeout(() => {
                this.isFocus = false;
                this.$emit('blur', e);
            }, 100)
        },
        handlerKeydown(e) {
            this.$emit('keydown', e);
        },
        handlerKeyup(e) {
            this.$emit('keyup', e);
        },
        handlerClear() {
            this.inputValue = "";
            this.handlerInput();
        },
        commentPressStart() {
            this.onCompressing = true;
        },
        commentPressEnd(e) {
            this.onCompressing = false;
            this.handlerInput(e);
        }
    }
})