package com.vedeng.erp.trader.mapper;
import java.util.Collection;

import com.vedeng.erp.trader.domain.dto.CustomerBankAccountDto;
import com.vedeng.erp.trader.domain.entity.CustomerBankAccountEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CustomerBankAccountMapper {
    /**
     * delete by primary key
     * @param customerBankAccountId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long customerBankAccountId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(CustomerBankAccountEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(CustomerBankAccountEntity record);

    /**
     * select by primary key
     * @param customerBankAccountId primary key
     * @return object by primary key
     */
    CustomerBankAccountEntity selectByPrimaryKey(Long customerBankAccountId);

    List<CustomerBankAccountEntity> findByAll(CustomerBankAccountEntity customerBankAccountEntity);


    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(CustomerBankAccountEntity record);

    int updateByPrimaryKeySelectiveWithLock(CustomerBankAccountEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(CustomerBankAccountEntity record);

    int updateBatch(List<CustomerBankAccountEntity> list);

    int updateBatchSelective(List<CustomerBankAccountEntity> list);

    int batchInsert(@Param("list") List<CustomerBankAccountEntity> list);


    List<CustomerBankAccountEntity> findByTraderId(@Param("traderId")Integer traderId);

    List<CustomerBankAccountEntity> findByTraderIdAndAccountType(@Param("traderId")Integer traderId,@Param("accountType")Integer accountType);


    List<CustomerBankAccountDto> findByAllToPage(CustomerBankAccountDto param);

    int updateIsDeleteByCustomerBankAccountId(@Param("updatedIsDelete")Integer updatedIsDelete,@Param("customerBankAccountId")Long customerBankAccountId);

    List<CustomerBankAccountEntity> findByAccountNameAndAccountNo(@Param("accountName")String accountName,@Param("accountNo")String accountNo);

	int updateIsVerifyByCustomerBankAccountIdIn(@Param("updatedIsVerify")Integer updatedIsVerify,@Param("customerBankAccountIdCollection")Collection<Long> customerBankAccountIdCollection);

}
