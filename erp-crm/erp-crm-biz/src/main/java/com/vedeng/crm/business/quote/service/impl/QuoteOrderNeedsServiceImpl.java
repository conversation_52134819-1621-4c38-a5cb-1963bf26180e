package com.vedeng.crm.business.quote.service.impl;

import com.vedeng.crm.business.quote.domain.dto.NeedsImportInfoDto;
import com.vedeng.crm.business.quote.domain.entity.CrmQuoteorderGoods;
import com.vedeng.crm.business.quote.domain.entity.QuoteorderNeedsEntity;
import com.vedeng.crm.business.quote.mapper.QuoteorderNeedsMapper;
import com.vedeng.crm.business.quote.service.QuoteOrderNeedsService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class QuoteOrderNeedsServiceImpl implements QuoteOrderNeedsService {

    @Autowired
    private QuoteorderNeedsMapper quoteOrderNeedsMapper;
    @Override
    public int batchInsert(List<NeedsImportInfoDto> list, QuoteorderNeedsEntity dto) {

        List<QuoteorderNeedsEntity> batchList = list.stream().map(item -> {
            QuoteorderNeedsEntity quoteorderNeedsEntity = new QuoteorderNeedsEntity();
            quoteorderNeedsEntity.setQuoteorderId(dto.getQuoteorderId());
            quoteorderNeedsEntity.setProductNeeds(StringUtils.isBlank(item.getProductNeeds())?StringUtils.EMPTY:item.getProductNeeds().trim() );
            quoteorderNeedsEntity.setNumNeeds( StringUtils.isBlank(item.getNumNeeds())?StringUtils.EMPTY:item.getNumNeeds().trim());
            quoteorderNeedsEntity.setBudgetaryNeeds("");
            quoteorderNeedsEntity.setDistributeBudget(StringUtils.isBlank(item.getDistributeBudget())?StringUtils.EMPTY:item.getDistributeBudget().trim()   );
            quoteorderNeedsEntity.setTerminalBudget(StringUtils.isBlank(item.getTerminalBudget())?StringUtils.EMPTY:item.getTerminalBudget().trim()    );
            quoteorderNeedsEntity.setExtraNeeds(StringUtils.isBlank(item.getExtraNeeds())?StringUtils.EMPTY:item.getExtraNeeds().trim() );
            quoteorderNeedsEntity.setQuoteorderId(dto.getQuoteorderId());
            quoteorderNeedsEntity.setCreator(Objects.isNull(dto.getCreator()) ? 1 : dto.getCreator());
            quoteorderNeedsEntity.setUpdater(Objects.isNull(dto.getUpdater()) ? 1 : dto.getUpdater());
            quoteorderNeedsEntity.setCreatorName(Objects.isNull(dto.getCreatorName()) ? "system" : dto.getCreatorName());
            quoteorderNeedsEntity.setUpdaterName(Objects.isNull(dto.getUpdaterName()) ? "system" : dto.getUpdaterName());
            Date date = new Date();
            quoteorderNeedsEntity.setAddTime(date);
            quoteorderNeedsEntity.setModTime(date);
            return quoteorderNeedsEntity;
        }).collect(Collectors.toList());
        int i = quoteOrderNeedsMapper.batchInsert(batchList);
        return i;
    }

    @Override
    public List<QuoteorderNeedsEntity> selectByQuoteorderId(Integer quoteorderId) {
        List<QuoteorderNeedsEntity> quoteorderNeedEntities = quoteOrderNeedsMapper.selectAllByQuoteorderId(quoteorderId);
        return quoteorderNeedEntities;
    }

    @Override
    public List<QuoteorderNeedsEntity> selectByQuoteNeedsIdList(List<Long> quoteorderNeedsIdList){
        return quoteOrderNeedsMapper.selectByQuoteNeedsIdList(quoteorderNeedsIdList);
    }


    @Override
    public int updateByPrimaryKeySelective(QuoteorderNeedsEntity record) {
        record.setModTime(new Date());
        int i = quoteOrderNeedsMapper.updateByPrimaryKeySelective(record);
        return i;
    }

    @Override
    public void deleteByQuoteorderNeedsId(List<QuoteorderNeedsEntity> extraOldList) {
        quoteOrderNeedsMapper.batchDelete(extraOldList);
    }

    @Override
    public List<CrmQuoteorderGoods> queryQuoteGoodsByNeedsIdAndSkuNos(List<String> skuNos, Long quoteorderNeedsId) {
        List<CrmQuoteorderGoods> result = quoteOrderNeedsMapper.queryQuoteGoodsByNeedsIdAndSkuNos(skuNos,quoteorderNeedsId);
        return result;
    }
}
