package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.annotation.KingDeeID;
import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import lombok.*;

import java.util.List;

/**
 * 其他出库   https://www.yuque.com/manhuo/gf1570/ug85q9
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date
 */
@Getter
@Setter
@WriteBackField(needBackField = {"FID"})
public class KingDeeStorageOutDto extends KingDeeMqBaseDto {

    /**
     * id
     */
    private Integer id;

    /**
     * 单据内码 0：表示新增 非0：云星空系统单据FID值，表示修改
     */
    @KingDeeID
    @WriteBackField
    private String fId;

    /**
     * 单据编号 填写单据编号，若为空则调用系统的编码规则生成
     */
    @BusinessID
    private String fBillNo;

    /**
     * 贝登单据头ID 贝登单据头ID号（预留）
     */
    private String fQzokBddjtId;

    /**
     * 库存方向 如果是入库，则默认值 ：GENERAL
     */
    private String fStockDirect;
    /**
     * 单据日期 格式yyyy-MM-dd
     */
    private String fDate;

    /**
     * 单据类型 填单据类型编码，默认QTCKD01_SYS
     */
    private String fBillTypeId;

    /**
     * 库存组织 填写组织编码 默认101,配置化
     */
    private String fStockOrgId;

    /**
     * 客户 填写客户编码
     */
    private String fCustId;
    /**
     * 部门 填写部门编码，默认值 ：BM9999
     */
    private String fDeptId;

    /**
     * 明细
     */
    private List<KingDeeStorageOutDetailDto> fEntity;


    public KingDeeStorageOutDto() {
        this.fBillTypeId = "QTCKD01_SYS";
        this.fStockOrgId = "101";
        this.fDeptId = "BM9999";
        this.fStockDirect="GENERAL";
    }

    @Override
    public String getFormId() {
        return KingDeeFormConstant.STK_MIS_DELIVERY;
    }
}
