package com.vedeng.infrastructure.file.manager;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.utils.ServletUtils;
import com.vedeng.infrastructure.file.domain.Attachment;
import com.vedeng.infrastructure.file.domain.GlobalFileDto;
import com.vedeng.infrastructure.file.domain.GlobalFileRequest;
import com.vedeng.infrastructure.oss.service.OssUtilsService;
import com.vedeng.infrastructure.oss.service.domain.FileInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 文件
 * @date 2024/9/27 8:51
 */
@Component
public class GlobalFileService {

    public static final int ATTACHMENT_TYPE = 461;

    @Autowired
    private GlobalAttachmentService globalAttachmentService;

    @Autowired
    private OssUtilsService ossUtilsService;

    @Value("${oss_http}")
    private String ossHttp;

    /**
     * 上传文件
     *
     * @param globalFileRequest 文件请求对象
     * @return 文件DTO
     */
    public GlobalFileDto upload(GlobalFileRequest globalFileRequest) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        FileInfo fileInfo = ossUtilsService.upload2OssByOriginalName(ServletUtils.getRequest(), globalFileRequest.getFile());

        if (fileInfo.getCode() != 0) {
            throw new ServiceException("文件上传失败");
        }

        Attachment attachment = createAttachment(globalFileRequest, fileInfo, currentUser);
        globalAttachmentService.save(attachment);

        return createGlobalFileDto(attachment);
    }

    /**
     * 删除文件
     *
     * @param attachmentId 附件ID
     */
    public void delete(Integer attachmentId) {
        globalAttachmentService.delete(attachmentId);
    }

    /**
     * 获取文件列表
     *
     * @param globalFileRequest 文件请求对象
     * @return 文件DTO列表
     */
    public List<GlobalFileDto> get(GlobalFileRequest globalFileRequest) {
        List<Attachment> attachments = globalAttachmentService.get(globalFileRequest.getBizId(), globalFileRequest.mapAttachmentFunction(globalFileRequest.getBizType()));
        return attachments.stream()
                .map(this::createGlobalFileDto)
                .collect(Collectors.toList());
    }

    /**
     * 根据ID获取文件
     *
     * @param attachmentId 附件ID
     * @return 文件DTO
     */
    public GlobalFileDto getById(Integer attachmentId) {
        Attachment attachment = globalAttachmentService.getById(attachmentId);
        return createGlobalFileDto(attachment);
    }

    /**
     * 创建Attachment对象
     *
     * @param globalFileRequest 文件请求对象
     * @param fileInfo          文件信息
     * @param currentUser       当前用户
     * @return Attachment对象
     */
    private Attachment createAttachment(GlobalFileRequest globalFileRequest, FileInfo fileInfo, CurrentUser currentUser) {
        Attachment attachment = new Attachment();
        attachment.setAttachmentType(ATTACHMENT_TYPE);
        attachment.setAttachmentFunction(globalFileRequest.mapAttachmentFunction(globalFileRequest.getBizType()));
        attachment.setRelatedId(globalFileRequest.getBizId());
        attachment.setName(fileInfo.getFileName());
        attachment.setUri(fileInfo.getFilePath());
        attachment.setDomain(fileInfo.getDomain());
        attachment.setSuffix(fileInfo.getPrefix());
        attachment.setOssResourceId(fileInfo.getOssResourceId());
        attachment.setCreator(currentUser.getId());
        attachment.setAddTime(System.currentTimeMillis());
        return attachment;
    }

    /**
     * 创建GlobalFileDto对象
     *
     * @param attachment 附件对象
     * @return GlobalFileDto对象
     */
    private GlobalFileDto createGlobalFileDto(Attachment attachment) {
        GlobalFileDto globalFileDto = new GlobalFileDto();
        BeanUtil.copyProperties(attachment, globalFileDto);
        globalFileDto.setWholeDisplayUrl(ossHttp + globalFileDto.getDomain() + globalFileDto.getUri());
        globalFileDto.setWholeDownloadUrl(globalFileDto.getWholeDisplayUrl().replace("display", "download"));
        return globalFileDto;
    }
}
