package com.vedeng.goods.domain.dto;

import lombok.Data;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description V_CORE_SKU和V_CORE_SKU,联合起来的对象
 * @Date 2022/1/5 13:56
 */
@Data
public class GoodsDto {

    /**
     * 商品id
     */
    private Integer skuId;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 订货号
     */
    private String sku;

    /**
     * 型号/规格
     * 商品类型为设备、配件，显示型号
     * 商品类型为耗材、试剂，显示规格
     */
    private String modelOrSpec;

    /**
     * 终端价
     */
    private BigDecimal terminalPrice;

    /**
     * 经销价
     */
    private BigDecimal distributionPrice;

    /**
     * 单位
     */
    private String unitName;

    /**
     * 可用库存
     */
    private Integer availableStockNum;


    /**
     * 分类ID
     */
    private Integer categoryId;


    /**
     * 分类名称
     */
    private String baseCategoryName;


    /**
     * 品牌ID
     */
    private Integer brandId;

}
