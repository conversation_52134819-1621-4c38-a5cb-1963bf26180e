package com.vedeng.erp.kingdee.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import com.vedeng.common.mybatis.jbatis.annotation.Column;
import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 应付余额调整单记录表
 */
@Getter
@Setter
@ToString
@Table(name = "KING_DEE_NEED_PAY_ADJUST")
public class KingDeeNeedPayAdjustEntity extends BaseEntity {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer kingDeeNeedPayEntityId;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 修改时间
     */
    private Date modTime;

    /**
     * 创建者id
     */
    private Integer creator;

    /**
     * 修改者id
     */
    private Integer updater;

    /**
     * 创建者名称
     */
    private String creatorName;

    /**
     * 修改者名称
     */
    private String updaterName;

    /**
     * 单据内码  0：表示新增 * 非0：云星空系统单据FID值，表示修改
     */
    private String fid;

    /**
     * 单据号
     */
    private String fBillNo;

    /**
     * 单据日期 2022-10-10
     */
    private String fQzokDate;

    /**
     * 组织代码
     */
    private String fQzokJg;

    /**
     * 供应商
     */
    private String fQzokBase;

    /**
     * fEntity json
     */
    @Column(jdbcType = "VARCHAR")
    private String fEntity;

}