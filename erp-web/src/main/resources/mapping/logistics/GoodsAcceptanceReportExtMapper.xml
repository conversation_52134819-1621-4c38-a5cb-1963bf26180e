<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.logistics.dao.ext.GoodsAcceptanceReportExtMapper">

  <resultMap id="reportMap" type="com.vedeng.logistics.model.vo.GoodsAcceptanceReportDto">
    <id column="GOODS_ACCEPTANCE_REPORT_ID" property="goodsAcceptanceReportId"/>
    <result column="ORDER_NO" property="orderNo"/>
    <result column="SKU" property="sku"/>
    <result column="SPU_ID" property="spuId"/>
    <result column="SHOW_NAME" property="showName"/>
    <result column="GOODS_ID" property="goodsId"/>
    <result column="PACKAGING" property="packaging"/>
    <result column="APPEARANCE" property="appearance"/>
    <result column="PERFORMANCE" property="performance"/>
    <result column="BACK_REASON" property="backReason"/>
    <result column="DESCRIPTION" property="description"/>
    <result column="addTimeStr" property="addTimeStr"/>
    <result column="CREATOR_NAME" property="creatorName"/>
    <collection property="attachmentList" ofType="com.vedeng.system.model.Attachment">
      <result column="ATTACHMENT_ID" property="attachmentId"/>
      <result column="NAME" property="name"/>
      <result column="URI" property="uri"/>
      <result column="DOMAIN" property="domain"/>
    </collection>
  </resultMap>
<select id="getAcceptanceReportList" resultMap="reportMap" >
  select
  a.GOODS_ACCEPTANCE_REPORT_ID,
  a.ORDER_NO,
  a.SKU,
  a.GOODS_ID,
  a.PACKAGING,
  a.APPEARANCE,
  a.PERFORMANCE,
  a.BACK_REASON,
  a.DESCRIPTION,
  date_format( a.ADD_TIME,'%Y-%m-%d %H:%i:%s') addTimeStr,
  a.CREATOR_NAME,
  c.SPU_ID,
  c.SHOW_NAME,
  b.ATTACHMENT_ID,
  b.NAME,
  b.URI,
  b.DOMAIN
  from T_GOODS_ACCEPTANCE_REPORT a
  left join T_ATTACHMENT b on a.GOODS_ACCEPTANCE_REPORT_ID = b.RELATED_ID and b.IS_DELETED = 0
  left join V_CORE_SKU c on a.GOODS_ID = c.SKU_ID
  where
  b.ATTACHMENT_TYPE = #{attachmentType,jdbcType=INTEGER}
  and b.ATTACHMENT_FUNCTION = #{attachmentFunction,jdbcType=INTEGER}
  and a.ORDER_NO = #{orderNo,jdbcType=VARCHAR}
  and a.IS_DELETE = 0

</select>
</mapper>