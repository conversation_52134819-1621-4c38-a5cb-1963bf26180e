package com.vedeng.erp.kingdee.task.batch;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.batch.job.PayBankBillJob;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 建设银行已推送金蝶的流水推送回单信息
 */
@JobHandler("BankFileBatchTask")
@Component
@Slf4j
public class BankFileBatchTask extends AbstractJobHandler {

    @Autowired
    private PayBankBillJob payBankBillJob;

    @Autowired
    private JobLauncher jobLauncher;
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
//        JobParameters jobParameters = new TaskBatchHandle().buildJobParameters(param);
//        Job job = payBankBillJob.pushBankFile();
//        jobLauncher.run(job, jobParameters);
        return SUCCESS;
    }
}
