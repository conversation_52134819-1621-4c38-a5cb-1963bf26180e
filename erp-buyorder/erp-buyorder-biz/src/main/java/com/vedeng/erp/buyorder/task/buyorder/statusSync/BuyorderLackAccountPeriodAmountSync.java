package com.vedeng.erp.buyorder.task.buyorder.statusSync;

import com.newtask.data.AbstractDataByTimeSync;
import com.newtask.data.dao.BuyorderDataMapper;
import com.newtask.data.dto.BuyorderDataDto;
import com.vedeng.common.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.buyorder.task.buyorder.statusSync
 * @Date 2021/11/10 16:53
 */
@Component
public class BuyorderLackAccountPeriodAmountSync extends AbstractDataByTimeSync {

    @Resource
    BuyorderDataMapper buyorderDataMapper;

    @Override
    public List<Map<String, Object>> loadBizByTimeData(Long startTime, Long endTime) {
        return buyorderDataMapper.getOrderLackAccountPeriodAmountByTime(startTime, endTime);
    }


    @Override
    public void updateData(List<Map<String, Object>> dataList) {
        long nowTime = System.currentTimeMillis();
        List<BuyorderDataDto> buyorderDataDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dataList)) {
            dataList.forEach(item -> {
                BuyorderDataDto buyorderDataDto = new BuyorderDataDto();
                Integer orderId = Integer.parseInt(item.get("orderId").toString());
                makeExist(orderId, nowTime);
                buyorderDataDto.setBuyorderId(orderId);
                String lackAccountPeriodAmount = item.get("lackAccountPeriodAmount").toString();
                if (StringUtil.isNotBlank(lackAccountPeriodAmount)){
                    buyorderDataDto.setLackAccountPeriodAmount(new BigDecimal(lackAccountPeriodAmount));
                    buyorderDataDtos.add(buyorderDataDto);
                }
            });
            buyorderDataMapper.updateOrderLackAccountPeriodAmount(buyorderDataDtos, nowTime);
        }
    }

    @Override
    public void makeExist(Integer id, Long nowTime) {
        int buyorderDataId = buyorderDataMapper.getBuyorderDataById(id);
        if (buyorderDataId <= 0) {
            buyorderDataMapper.insertBuyorderData(id, nowTime);
        }
    }
}
