
// 需求附件-弹层
Vue.component('file-dialog', {
    template: `
        <div>
            <ui-dialog
                :visible.sync="isShow"
                title="需求附件"
                width="960px"
                align="center"
            >
                <div class="file-dialog-top" v-if="GLOBAL.auth('C0227') || GLOBAL.auth('C0228')">
                    <ui-upload-attachment
                        v-if="GLOBAL.auth('C0227')"
                        class="btn"
                        :biz-id="bizId"
                        @finish="addFile"
                    ></ui-upload-attachment>
                    <ui-button
                        v-if="GLOBAL.auth('C0228')"
                        class="btn" type="primary"
                        @click="deleteFile(selectItems)"
                    >删除</ui-button>
                    <span>已选{{ selectItems.length }}项</span>
                </div>

                <div class="file-dialog-panel" v-if="isShow && showList">
                    <div class="panel-wrap dialog-inner">
                        <div class="file-list">
                            <ui-table 
                                container-height="470px" 
                                :oneline="true" 
                                :width-border="true" 
                                :auto-scroll="false" 
                                :headers="listHeaders" 
                                :list="list" 
                                :custom-empty="true"
                                :can-choose="true"
                                @selectchange="selectchange"
                            >
                                <template v-slot:name="{ row }">
                                    <div class="file-name">
                                        <img :src="'/static/image/upload-icon/' + (typeParse(row.suffix) || 'other') +'.svg'" />
                                        {{ row.name }}
                                    </div>
                                </template>
                                <template v-slot:option="{ row }">
                                    <div class="option-wrap">
                                        <div class="file-deal-btn">
                                            <a class="btn" @click="downloadFile(row)" v-if="GLOBAL.auth('C0229')">下载</a>
                                            <a class="btn" @click="deleteFile([row.attachmentId])" v-if="GLOBAL.auth('C0228')">删除</a>
                                        </div>
                                    </div>
                                </template>
                                <template v-slot:customempty>
                                    <div class="null-data">
                                        <div class="empty-img"></div>
                                        <p class="font">暂无需求附件，可需求附件辅助协作人员更好的了解需求</p>
                                    </div>
                                </template>
                            </ui-table>
                        </div>
                    </div>
                </div>
            </ui-dialog>
        </div>
    `,
    props: {
    },
    data () {
        return {
            isShow: false,
            showList: false,

            // query
            bizId: '', // 业务id
            bizType: '', //  1:商机 2:线索 3:报价
            list: [],
            listHeaders: [
                {
                    key: "name",
                    label: "文件名称",
                    width: "704px",
                },
                {
                    key: "option",
                    label: "操作",
                    width: "160px"
                },
            ],
            selectItems: [], // 选中的文件id
        }
    },
    computed: {
    },
    mounted() {
    },
    methods: {
        close () {
            this.isShow = false;
        },
        open (query) {
            this.bizId = query.bizId || '';
            this.bizType = query.bizType || '';
            this.initData();
        },
        initData () {
            GLOBAL.showGlobalLoading();
            this.showList = false;

            this.$axios.post(`/crm/common/public/getFile?bizId=${this.bizId}&bizType=${this.bizType}`)
                .then(({data}) => {
                    console.log('附件列表:', data);
                    if (data.success) {
                        this.list = data.data || [];
                    } else {
                        this.$message.error(data.message);
                    }
                    this.isShow = true;
                    this.showList = true;
                    GLOBAL.hideGlobalLoading();

                    this.$emit('change', this.list.length)
                });
        },
        selectchange (arr) {
            if (arr.length) {
                let Ids = [];
                arr.forEach(item=> {
                    Ids.push(item.attachmentId)
                })
                this.selectItems = Ids;
            } else {
                this.selectItems = [];
            }
        },

        // 添加附件
        addFile () {
            this.initData();
        },
        // 删除附件
        deleteFile (fileIds) {
            if (!fileIds.length) {
                this.$message.warn('请选择需要删除的文件');
                return;
            }
            const _this = this;
            this.$popup.warn({
                message: '文件删除后不可恢复，确定删除选中的文件？',
                buttons: [{
                    txt: '删除',
                    btnClass: 'delete',
                    callback() {
                        let reqData = [];
                        fileIds.forEach(item => {
                            reqData.push({
                                bizId: _this.bizId || '',
                                bizType: _this.bizType || '',
                                attachmentId: item
                            })
                        });
                        _this.$axios.post(`/crm/common/public/deleteFile`, reqData)
                            .then(({data}) => {
                                if (data.success) {
                                    _this.$message.success("删除成功");
                                    _this.initData();
                                } else {
                                    _this.$message.error(data.message);
                                }
                                GLOBAL.hideGlobalLoading();
                            });

                    }
                }, {
                    txt: '取消'
                }]
            })
        },
        // 下载附件
        downloadFile (item) {
            window.open(item.wholeDownloadUrl);

            this.$axios.post(`/crm/common/public/downloadFile?attachmentId=${item.attachmentId}&bizId=${this.bizId}&bizType=${this.bizType}`)
                .then(({data})=> {
                        console.log('success:', data);
                    }).catch(err=> {})
        },

        typeParse (suffix) {
            suffix = suffix && suffix.toLocaleLowerCase() || '';
            let pic = ['png', 'jpg', 'jpeg', 'psd', 'gif', 'bmp', 'webp', 'pcx', 'tif'];   // 图片后缀
            let video = ['avi', 'mov', 'qt', 'asf', 'rm', 'rmvb', 'navi', 'divx', 'mpeg', 'mkv', 'ogg', 'mod', 'mp4'];   // 视频后缀
            if (pic.indexOf(suffix) > -1 ) {
                return 'pic';
            } else if ( video.indexOf(suffix) > -1 ) {
                return 'video';
            }
            let arr = {
                other: 'other',
                pic: 'pic',
                video: 'video',
                rar: 'rar',
                pdf: 'pdf',
                ppt: 'ppt', pptx: 'ppt',
                doc: 'word', docx: 'word', rtf: 'word',
                xlsx: 'excel', xls: 'excel',
                txt: 'txt',
            }
            
            if (arr[suffix]) {
                return arr[suffix];
            } else {
                return 'other';
            }
        },
    }
})
