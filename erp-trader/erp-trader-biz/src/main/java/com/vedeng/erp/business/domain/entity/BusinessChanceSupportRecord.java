package com.vedeng.erp.business.domain.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Builder;
import lombok.Data;

/**
 * 商机支持记录表
 */
@Data
public class BusinessChanceSupportRecord extends BaseEntity {
    /**
    * 主键
    */
    private Long businessChanceSupportRecordId;

    /**
    * 商机ID
    */
    private Integer businessChanceId;

    /**
    * 商机等级 SABC
    */
    private String businessLevel;

    /**
    * 评估成交金额（元）
    */
    private BigDecimal amount;

    /**
    * 评估成交时间
    */
    private Date orderDate;

    /**
    * 下次支持时间
    */
    private Date nextSupportDate;

    /**
    * 支持内容
    */
    private String content;

    /**
    * 所属部门
    */
    private String department;

    /**
    * 是否删除 0否 1是
    */
    private Integer isDelete;

    /**
    * 更新备注
    */
    private String updateRemark;
}