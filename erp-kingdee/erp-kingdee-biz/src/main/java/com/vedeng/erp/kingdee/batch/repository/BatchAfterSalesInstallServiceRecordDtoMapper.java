package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesInstallServiceRecordDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BatchAfterSalesInstallServiceRecordDtoMapper {
    int deleteByPrimaryKey(Integer afterSalesServiceId);

    int insert(BatchAfterSalesInstallServiceRecordDto record);

    int insertOrUpdate(BatchAfterSalesInstallServiceRecordDto record);

    int insertOrUpdateSelective(BatchAfterSalesInstallServiceRecordDto record);

    int insertSelective(BatchAfterSalesInstallServiceRecordDto record);

    BatchAfterSalesInstallServiceRecordDto selectByPrimaryKey(Integer afterSalesServiceId);

    int updateByPrimaryKeySelective(BatchAfterSalesInstallServiceRecordDto record);

    int updateByPrimaryKey(BatchAfterSalesInstallServiceRecordDto record);

    int updateBatch(List<BatchAfterSalesInstallServiceRecordDto> list);

    int updateBatchSelective(List<BatchAfterSalesInstallServiceRecordDto> list);

    int batchInsert(@Param("list") List<BatchAfterSalesInstallServiceRecordDto> list);
}