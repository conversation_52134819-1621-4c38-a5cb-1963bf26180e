package com.vedeng.dwh.externaldb.dao.generate;

import com.vedeng.dwh.model.generate.DwsBussinessChanceWorkbenchDfDo;

public interface DwsBussinessChanceWorkbenchDfMapper {
    int deleteByPrimaryKey(Integer bussinessChanceId);

    int insert(DwsBussinessChanceWorkbenchDfDo record);

    int insertSelective(DwsBussinessChanceWorkbenchDfDo record);

    DwsBussinessChanceWorkbenchDfDo selectByPrimaryKey(Integer bussinessChanceId);

    int updateByPrimaryKeySelective(DwsBussinessChanceWorkbenchDfDo record);

    int updateByPrimaryKey(DwsBussinessChanceWorkbenchDfDo record);
}