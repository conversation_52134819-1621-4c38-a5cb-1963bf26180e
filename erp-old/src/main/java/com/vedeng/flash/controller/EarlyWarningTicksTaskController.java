package com.vedeng.flash.controller;

import com.vedeng.authorization.model.Organization;
import com.vedeng.authorization.model.User;
import com.vedeng.common.annotation.SystemControllerLog;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.flash.dto.*;
import com.vedeng.flash.service.warningtask.ExpeditingTicketsService;

import com.vedeng.system.service.OrgService;
import com.vedeng.system.service.UserService;
import com.vedeng.todolist.constant.DepartmentType;
import com.vedeng.todolist.constant.UserType;
import com.vedeng.todolist.model.UserInfo;
import com.wms.service.context.ThreadLocalContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 催票任务Controller
 * @date 2021/5/21 15:58
 */
@Controller
@RequestMapping("/flash/earlyWarningTicksTask")
public class EarlyWarningTicksTaskController extends BaseController {

    @Autowired
    private ExpeditingTicketsService expeditingTicketsService;

    @Autowired
    private UserService userService;

    @Autowired
    private OrgService orgService;

    /**
     * @Description 催票任务和跟进记录列表
     * <AUTHOR>
     * @Date 18:50 2021/5/24
     * @Param [request, earlyWarningTicksSearchDto, tabFlag, pageNo, pageSize]
     * @return org.springframework.web.servlet.ModelAndView
     **/
    @RequestMapping("/earlyWarningTicksTask")
    public ModelAndView index(HttpServletRequest request,
                              EarlyWarningTicksSearchDto earlyWarningTicksSearchDto,
                              @RequestParam(defaultValue = "0") Integer tabFlag,
                              @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                              @RequestParam(required = false) Integer pageSize) {
        ModelAndView mav = new ModelAndView("flash/earlyWarningTicksTask/earlyWarningTicks_list");
        Page page = getPageTag(request, pageNo, pageSize);
        List<Integer> userIds = getCanSeeUserIds(earlyWarningTicksSearchDto,request);
        if(userIds.size()>1){//管理员视角
            mav.addObject("isAdmin",1);
            List<TaskDealerDto> delaerUserList = expeditingTicketsService.getAllDealerByUserIds(userIds);
            mav.addObject("delaerUserList",delaerUserList);
        }else{
            mav.addObject("isAdmin",0);
        }
        //催票列表
        if(Integer.valueOf(0).equals(tabFlag)){
            List<EarlyWarningTicksDto> earlyWarningTicksDtoList = expeditingTicketsService.getEarlyWarningTicksByUserId(userIds,earlyWarningTicksSearchDto,page);
            BigDecimal totalPageAmount =  BigDecimal.ZERO;
            for(EarlyWarningTicksDto earlyWarningTicksDto : earlyWarningTicksDtoList){
                if(null!=earlyWarningTicksDto.getUrgingTicketAmount()){
                    totalPageAmount= totalPageAmount.add(earlyWarningTicksDto.getUrgingTicketAmount().subtract(earlyWarningTicksDto.getAlreadyInputAmount()));
                }
            }
            mav.addObject("totalPageAmount",totalPageAmount);
            mav.addObject("earlyWarningTicksList",earlyWarningTicksDtoList);
        }else{//跟进列表
            List<EarlyWarningTicksDto> earlyWarningTicksDtoList = expeditingTicketsService.getTicksFollowUpRecoredByUserId(userIds,earlyWarningTicksSearchDto,page);
            BigDecimal totalPageAmount =  BigDecimal.ZERO;
            BigDecimal totalPageCanTicketAmount = BigDecimal.ZERO;
            for(EarlyWarningTicksDto earlyWarningTicksDto : earlyWarningTicksDtoList){
                if(null != earlyWarningTicksDto.getUrgingTicketAmountFollowUp()){
                    totalPageAmount = totalPageAmount.add(earlyWarningTicksDto.getUrgingTicketAmountFollowUp());
                }
                if(null != earlyWarningTicksDto.getCanTicketAmount()){
                    totalPageCanTicketAmount = totalPageCanTicketAmount.add(earlyWarningTicksDto.getCanTicketAmount());
                }
            }
            mav.addObject("totalPageAmount",totalPageAmount);
            mav.addObject("totalPageCanTicketAmount",totalPageCanTicketAmount);
            mav.addObject("earlyWarningTicksList",earlyWarningTicksDtoList);
        }
        mav.addObject("earlyWarningTicksSearchDto",earlyWarningTicksSearchDto);
        mav.addObject("page",page);
        mav.addObject("tabFlag",tabFlag);
        return mav;
    }

    private List<Integer> getCanSeeUserIds(EarlyWarningTicksSearchDto earlyWarningTicksSearchDto, HttpServletRequest request) {
        if(StringUtil.isEmpty(earlyWarningTicksSearchDto.getUserIds())){//从站内信跳转则自己计算
            User currentUser = (User) request.getSession().getAttribute(Consts.SESSION_USER);
            UserType userType = getCurrentUserType(currentUser);
            if(UserType.Staff.getValue().equals(userType.getValue())){//员工只能看到自己

                return Arrays.asList(currentUser.getUserId());
            }else if(UserType.Admin.getValue().equals(userType.getValue())){//管理者能看到自己和自己的下属
                //获取当前用户可以看的部门
                List<Organization> orgaList = getCurrentUserCanSeeOrga(currentUser);
                Set<Integer> userIdSet = new HashSet<>();

                orgaList.stream().forEach(organization -> {
                    List<Integer> userIdList = userService.getUserIdListByOrgId(organization.getOrgId());
                    userIdSet.addAll(userIdList);
                });
                return new ArrayList<>(userIdSet);
            }
        }else{//从供应链工作台点击跳转会携带UserIds参数
            List<String> userIdsString = Arrays.asList(earlyWarningTicksSearchDto.getUserIds().split(",").clone());
            return userIdsString.stream().map(Integer::parseInt).collect(Collectors.toList());
        }
        return null;
    }

    private UserType getCurrentUserType(User use) {

        UserInfo userInfo = this.userService.getDepartAndPositionInfo(use.getUserId());

        if(userInfo == null || StringUtils.isEmpty(userInfo.getDepartTpyeName())){
            return UserType.Staff;
        }

        //部门类型是采购的且职位级别是员工 那就是员工
        if(DepartmentType.PURCHASE.getValue().equals(userInfo.getDepartTpyeName())){
            return UserType.Staff.getValue().equals(userInfo.getPositionTypeName()) ? UserType.Staff : UserType.Admin;
        }

        return UserType.Staff;
    }
    private List<Organization> getCurrentUserCanSeeOrga(User currentUser) {
        return orgService.getChildrenOrgByParentId(currentUser.getOrgId(),1);
    }
    /**
     * @Description 打开跟进界面
     * <AUTHOR>
     * @Date 19:20 2021/5/24
     * @Param [request, earlyWarningTaskFollowUpVo]
     * @return org.springframework.web.servlet.ModelAndView
     **/
    @RequestMapping(value = "/showFollowUp")
    public ModelAndView showFollowUp(HttpServletRequest request, EarlyWarningTaskFollowUpDto earlyWarningTaskFollowUpVo) {
        ModelAndView mav = new ModelAndView("flash/earlyWarningTicksTask/followUp");
        List<EarlyWarningTicksDto> earlyWarningTicksDtoList = expeditingTicketsService.getEarlyWarningTicksById(earlyWarningTaskFollowUpVo.getEarlyWarningTaskIds());
        mav.addObject("earlyWarningTicksList",earlyWarningTicksDtoList);
        mav.addObject("totalPrice",earlyWarningTaskFollowUpVo.getTotalPrice());
        mav.addObject("earlyWarningTaskIds",earlyWarningTaskFollowUpVo.getEarlyWarningTaskIds());
        return mav;
    }
    @ResponseBody
    @RequestMapping(value = "/saveFollowUp")
    @SystemControllerLog(operationType = "update", desc = "保存跟进记录")
    public ResultInfo<?> followUp(HttpServletRequest request, FollowUpDto followUpDto) {
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        followUpDto.setFollowUpPerson(user.getUserId());
        followUpDto.setFollowUpTime(DateUtil.getNowDate(DateUtil.TIME_FORMAT));
        return expeditingTicketsService.SaveFollowUp(followUpDto);
    }

}
