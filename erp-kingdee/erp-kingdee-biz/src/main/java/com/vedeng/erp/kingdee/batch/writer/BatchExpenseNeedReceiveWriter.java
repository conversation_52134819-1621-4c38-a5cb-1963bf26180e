package com.vedeng.erp.kingdee.batch.writer;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveFeeDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeReceiveFeeCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeReceiveFeeConvertor;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeReceiveFeeMapper;
import com.vedeng.erp.kingdee.service.KingDeeReceiveFeeApiService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * 销售费用应收单
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/12 14:25
 */
@Service
@Slf4j
public class BatchExpenseNeedReceiveWriter extends BaseWriter<KingDeeReceiveFeeDto> {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private KingDeeReceiveFeeCommandConvertor kingDeeReceiveFeeCommandConvertor;

    @Autowired
    private KingDeeReceiveFeeMapper kingDeeReceiveFeeMapper;

    @Autowired
    private KingDeeReceiveFeeConvertor kingDeeReceiveFeeConvertor;

    @Autowired
    private KingDeeReceiveFeeApiService kingDeeReceiveFeeApiService;

    @Override
    public void doWrite(KingDeeReceiveFeeDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("销售费用应收单：{}", JSON.toJSONString(item));
        item.setKingDeeBizEnums(KingDeeBizEnums.saveReceiveFee);
        kingDeeReceiveFeeApiService.register(item,true);
//        ArrayList<String> needReturnFields = new ArrayList<>();
//        needReturnFields.add("FEntityDetail.FEntryID");
//        needReturnFields.add("FEntityDetail.F_QZOK_BDDJHID");
//
//        ArrayList<SuccessEntity> successEntities = kingDeeBaseApi.save(new SaveExtCommand<>(kingDeeReceiveFeeCommandConvertor.toCommand(item), item.getFormId(), needReturnFields));
//        if (CollUtil.isNotEmpty(successEntities)) {
//            SuccessEntity successEntity = CollUtil.getFirst(successEntities);
//            item.setFid(successEntity.getId());
//
//            ArrayList<JsonObject> needReturnData = successEntity.getNeedReturnData();
//            Map<String, String> fInStockEntry2Map = new HashMap<>();
//            if (CollUtil.isNotEmpty(needReturnData)) {
//                JsonArray fInStockEntry = needReturnData.get(0).getAsJsonArray("FEntityDetail");
//                for (JsonElement a : fInStockEntry) {
//                    JsonObject asJsonObject = a.getAsJsonObject();
//                    String fEntryId = asJsonObject.get("FEntryID").getAsString();
//                    String fQzokBddjhId = asJsonObject.get("F_QZOK_BDDJHID").getAsString();
//                    fInStockEntry2Map.put(fQzokBddjhId, fEntryId);
//                }
//            }
//            item.getFEntityDetail().forEach(e -> {
//                e.setFEntryId(fInStockEntry2Map.get(e.getFQzokBddjhid()));
//            });
//            log.info("销售费用应收单推送金蝶ERP落库：{}", JSON.toJSONString(item));
//            kingDeeReceiveFeeMapper.insertSelective(kingDeeReceiveFeeConvertor.toEntity(item));
//        }
    }
}