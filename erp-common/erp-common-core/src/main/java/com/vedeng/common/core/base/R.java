package com.vedeng.common.core.base;


import cn.hutool.core.date.DateUtil;
import com.sun.org.apache.xpath.internal.operations.Bool;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 通用的返回信息， 调用HTTP接口等统一的响应（返回）信息。
 * @date 2021/12/25 17:39
 */
@Data
public class R<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 正常返回码
     */
    public static final int SUCCESS_CODE = BaseResponseCode.SUCCESS.getCode();

    /**
     * 异常返回码
     */
    public static final Integer ERROR_CODE = BaseResponseCode.SYSTEM_BUSY.getCode();

    public static final Integer CONFIRM_CODE = BaseResponseCode.CONFIRM.getCode();

    /**
     * 正常返回信息
     */
    public static final String SUCCESS_MSG = "成功";

    /**
     * 结果编码：0表示成功，其他值表示失败
     */
    private Integer code;

    /**
     * 提示信息
     */
    private String message;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 当前时间
     */
    private String time = DateUtil.now();

    /**
     * 返回数据
     */
    private T data;

    public R() {
        this.message = SUCCESS_MSG;
        this.code = SUCCESS_CODE;
        this.success = true;
    }

    public R(String message) {
        this.message = message;
        this.code = SUCCESS_CODE;
        this.success = true;
    }

    public R(T data) {
        this.message = SUCCESS_MSG;
        this.code = SUCCESS_CODE;
        this.success = true;
        this.data = data;
    }

    public R(Integer code, String message, T data) {
        this.message = message;
        this.code = code;
        this.data = data;
    }

    public static <T> R<T> success() {
        R<T> r = new R<>();
        r.setCode(SUCCESS_CODE);
        r.setMessage(SUCCESS_MSG);
        r.setSuccess(true);
        return r;
    }

    public static <T> R<T> success(String msg) {
        R<T> r = new R<>();
        r.setCode(SUCCESS_CODE);
        r.setSuccess(true);
        r.setMessage(msg);
        return r;
    }

    public static <T> R<T> confirm(String msg) {
        R<T> r = new R<>();
        r.setCode(CONFIRM_CODE);
        r.setSuccess(true);
        r.setMessage(msg);
        return r;
    }

    public static <T> R<T> success(T data) {
        R<T> r = new R<>();
        r.setCode(SUCCESS_CODE);
        r.setMessage(SUCCESS_MSG);
        r.setSuccess(true);
        r.setData(data);
        return r;
    }

    public static <T> R<T> success(String msg, T data) {
        R<T> r = new R<>();
        r.setCode(SUCCESS_CODE);
        r.setSuccess(true);
        r.setMessage(msg);
        r.setData(data);
        return r;
    }

    public static <T> R<T> error(String msg) {
        return error(ERROR_CODE, msg);
    }

    public static <T> R<T> error(int code, String msg) {
        R<T> r = new R<>();
        r.setSuccess(code == SUCCESS_CODE);
        r.setCode(code);
        r.setMessage(msg);
        return r;
    }

    public static <T> R<T> error(BaseResponseCode baseResponseCode) {
        return error(baseResponseCode.getCode(), baseResponseCode.getMsg());
    }

}
