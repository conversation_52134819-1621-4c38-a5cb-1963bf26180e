<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.finance.mapper.TaxClassificationMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.TaxClassificationEntity">
    <id column="TAX_CLASSIFICATION_ID" jdbcType="INTEGER" property="taxClassificationId" />
    <result column="CODE" jdbcType="VARCHAR" property="code" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="SIMPLE_NAME" jdbcType="VARCHAR" property="simpleName" />
    <result column="TAX_RATE" jdbcType="VARCHAR" property="taxRate" />
    <result column="IS_SUMMARY_ITEM" jdbcType="VARCHAR" property="isSummaryItem" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
    <result column="DESCRIPTION" jdbcType="LONGVARCHAR" property="description" />
    <result column="KEYWORDS" jdbcType="LONGVARCHAR" property="keywords" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_TAX_CLASSIFICATION
    where TAX_CLASSIFICATION_ID = #{taxClassificationId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.erp.finance.domain.entity.TaxClassificationEntity">
    insert into T_TAX_CLASSIFICATION (TAX_CLASSIFICATION_ID, CODE, `NAME`, 
      SIMPLE_NAME, TAX_RATE, IS_SUMMARY_ITEM, 
      IS_DELETE, ADD_TIME, MOD_TIME, 
      CREATOR, CREATOR_NAME, UPDATER, 
      UPDATER_NAME, UPDATE_REMARK, DESCRIPTION, 
      KEYWORDS)
    values (#{taxClassificationId,jdbcType=INTEGER}, #{code,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{simpleName,jdbcType=VARCHAR}, #{taxRate,jdbcType=VARCHAR}, #{isSummaryItem,jdbcType=VARCHAR}, 
      #{isDelete,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updater,jdbcType=INTEGER}, 
      #{updaterName,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR}, #{description,jdbcType=LONGVARCHAR}, 
      #{keywords,jdbcType=LONGVARCHAR})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.finance.domain.entity.TaxClassificationEntity">
    update T_TAX_CLASSIFICATION
    set CODE = #{code,jdbcType=VARCHAR},
      `NAME` = #{name,jdbcType=VARCHAR},
      SIMPLE_NAME = #{simpleName,jdbcType=VARCHAR},
      TAX_RATE = #{taxRate,jdbcType=VARCHAR},
      IS_SUMMARY_ITEM = #{isSummaryItem,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      DESCRIPTION = #{description,jdbcType=LONGVARCHAR},
      KEYWORDS = #{keywords,jdbcType=LONGVARCHAR}
    where TAX_CLASSIFICATION_ID = #{taxClassificationId,jdbcType=INTEGER}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select TAX_CLASSIFICATION_ID, CODE, `NAME`, SIMPLE_NAME, TAX_RATE, IS_SUMMARY_ITEM, 
    IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, UPDATE_REMARK, 
    DESCRIPTION, KEYWORDS
    from T_TAX_CLASSIFICATION
    where TAX_CLASSIFICATION_ID = #{taxClassificationId,jdbcType=INTEGER}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select TAX_CLASSIFICATION_ID, CODE, `NAME`, SIMPLE_NAME, TAX_RATE, IS_SUMMARY_ITEM, 
    IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, UPDATE_REMARK, 
    DESCRIPTION, KEYWORDS
    from T_TAX_CLASSIFICATION
  </select>
  <select id="findByCode" resultType="com.vedeng.erp.finance.domain.entity.TaxClassificationEntity">
    select TAX_CLASSIFICATION_ID, CODE, `NAME`, SIMPLE_NAME, TAX_RATE, IS_SUMMARY_ITEM,
           IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, UPDATE_REMARK,
           DESCRIPTION, KEYWORDS
    from T_TAX_CLASSIFICATION
    where CODE = #{code,jdbcType=VARCHAR}
  </select>
  <select id="findByAll" resultType="com.vedeng.erp.finance.dto.TaxClassificationDto">
    select TAX_CLASSIFICATION_ID, CODE, `NAME`, SIMPLE_NAME, TAX_RATE, IS_SUMMARY_ITEM,
    IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, UPDATE_REMARK,
    DESCRIPTION, KEYWORDS
    from T_TAX_CLASSIFICATION
    <where>
        and 1 =1
      <if test="code != null and code != ''">
        and `CODE` LIKE CONCAT('%', #{code,jdbcType=VARCHAR}, '%')
      </if>
      <if test="name != null and name != ''">
        and `NAME` LIKE CONCAT('%', #{name,jdbcType=VARCHAR}, '%')
      </if>
      <if test="simpleName != null and simpleName != ''">
        and SIMPLE_NAME LIKE CONCAT('%', #{simpleName,jdbcType=VARCHAR}, '%')
      </if>
      <if test="description != null and description != ''">
        and `DESCRIPTION` LIKE CONCAT('%', #{description,jdbcType=VARCHAR}, '%')
      </if>
      <if test="keywords != null and keywords != ''">
        and KEYWORDS LIKE CONCAT('%', #{keywords,jdbcType=VARCHAR}, '%')
      </if>
    </where>
  </select>
</mapper>