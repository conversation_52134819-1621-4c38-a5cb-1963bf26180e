package com.wms.service.chain.Build;

import com.wms.service.chain.HandlerStep;
import com.wms.service.chain.step.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * ERP采购换货单审核通过 步骤构造器
 */
@Service
public class StepBuildFactory {

    //发送WMS接口请求处理器
    @Autowired
    private PurchaseExgWmsRequestHandler purchaseExgWmsRequestHandler;

    //同步库存信息处理器
    @Autowired
    private PurchaseExgStockInfoHandler purchaseExgStockInfoHandler;

    //新增逻辑订单商品处理器
    @Autowired
    private PurchaseExgLogicOrderHandler purchaseExgLogicOrderHandler;

    @Autowired
    private PurchaseReturnOutWmsRequestHandler purchaseReturnOutWmsRequestHandler;

    @Autowired
    private PurchaseReturnOutStockInfoHandler purchaseReturnOutStockInfoHandler;

    @Autowired
    private PurchaseReturnOutLogicOrderHandler purchaseReturnOutLogicOrderHandler;


    @Autowired
    private PurchaseReturnOutWmsRequestReverseHandler purchaseReturnOutWmsRequestReverseHandler;

    @Autowired
    private PurchaseReturnOutLogicOrderReverseHandler purchaseReturnOutLogicOrderReverseHandler;

    @Autowired
    private PurchaseReturnOutStockInfoReverseHandler purchaseReturnOutStockInfoReverseHandler;

    @Autowired
    private LendOutOrderHandler lendOutOrderHandler;

    @Autowired
    private LendOutStockInfoHandler lendOutStockInfoHandler;

    @Autowired
    private LendOutWmsRequestHandler lendOutWmsRequestHandler;

    @Autowired
    private InventoryOutOrderHandler inventoryOutOrderHandler;

    @Autowired
    private InventoryOutStockInfoHandler inventoryOutStockInfoHandler;

    @Autowired
    private InventoryOutWmsRequestHandler inventoryOutWmsRequestHandler;

    @Autowired
    private UnitConversionOrderOutHandler unitConversionOrderOutHandler;

    @Autowired
    private UnitConversionOrderOutStockInfoHandler unitConversionOrderOutStockInfoHandler;

    @Autowired
    private UnitConversionOrderOutWmsRequestHandler unitConversionOutOutWmsRequestHandler;

    @Autowired
    private UnitConversionOrderInWmsRequestHandler unitConversionOrderInWmsRequestHandler;

    @Autowired
    private SampleOutOrderHandler sampleOutOrderHandler;

    @Autowired
    private SampleOutWmsRequestHandler sampleOutWmsRequestHandler;

    //构建采购换货步骤
    public HandlerStep buildPurchaseExgStep(){

//        purchaseExgWmsRequestHandler.setNext(purchaseExgStockInfoHandler);
        //调整执行顺序,下发wms应该为最后一步
        purchaseExgStockInfoHandler.setNext(purchaseExgLogicOrderHandler);

        purchaseExgLogicOrderHandler.setNext(purchaseExgWmsRequestHandler);

        return purchaseExgStockInfoHandler;
    }

    //构建采购退货出库步骤
    public HandlerStep buildPurchaseReturnOutStep(){

//        purchaseReturnOutWmsRequestHandler.setNext(purchaseReturnOutStockInfoHandler);
        //调整执行顺序,下发wms应该为最后一步
        purchaseReturnOutStockInfoHandler.setNext(purchaseReturnOutLogicOrderHandler);

        purchaseReturnOutLogicOrderHandler.setNext(purchaseReturnOutWmsRequestHandler);

        return purchaseReturnOutStockInfoHandler;
    }

    //构建采购退货反向步骤
    public HandlerStep buildPurchaseReturnOutReverseStep() {

        purchaseReturnOutWmsRequestReverseHandler.setNext(purchaseReturnOutLogicOrderReverseHandler);

        purchaseReturnOutLogicOrderReverseHandler.setNext(purchaseReturnOutStockInfoReverseHandler);

        return purchaseReturnOutWmsRequestReverseHandler;
    }

    //构建外借出库步骤
    public HandlerStep buildLendOutStep() {

        lendOutOrderHandler.setNext(lendOutWmsRequestHandler);

        return lendOutOrderHandler;
    }

    public HandlerStep buildSampleOutStep() {

        sampleOutOrderHandler.setNext(sampleOutWmsRequestHandler);

        return sampleOutOrderHandler;
    }

    /**
     * 构建盘亏出库
     * @return HandlerStep
     */
    public HandlerStep buildInventoryOutOrder() {

        // 逻辑仓数据，stock服务锁库存，下发wms
        inventoryOutOrderHandler.setNext(inventoryOutStockInfoHandler);

        inventoryOutStockInfoHandler.setNext(inventoryOutWmsRequestHandler);

        return inventoryOutOrderHandler;
    }

    /**
     * 构建库存转换出库
     * @return HandlerStep
     */
    public HandlerStep buildUnitConversionOutOrder() {

        // 逻辑仓数据，stock服务锁库存，下发wms
        unitConversionOrderOutHandler.setNext(unitConversionOrderOutStockInfoHandler);

        unitConversionOrderOutStockInfoHandler.setNext(unitConversionOutOutWmsRequestHandler);

        return unitConversionOrderOutHandler;
    }


    /**
     * 构建库存转换入库
     * @return HandlerStep
     */
    public HandlerStep buildUnitConversionInOrder() {

        // 直接下发
        return unitConversionOrderInWmsRequestHandler;
    }
}
