<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.mobile.system.mapper.WebAccountMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.mobile.system.model.WebAccount">
    <!--@mbg.generated-->
    <!--@Table T_WEB_ACCOUNT-->
    <id column="ERP_ACCOUNT_ID" jdbcType="INTEGER" property="erpAccountId" />
    <result column="WEB_ACCOUNT_ID" jdbcType="INTEGER" property="webAccountId" />
    <result column="SSO_ACCOUNT_ID" jdbcType="INTEGER" property="ssoAccountId" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_CONTACT_ID" jdbcType="INTEGER" property="traderContactId" />
    <result column="TRADER_ADDRESS_ID" jdbcType="INTEGER" property="traderAddressId" />
    <result column="USER_ID" jdbcType="INTEGER" property="userId" />
    <result column="IS_ENABLE" jdbcType="BOOLEAN" property="isEnable" />
    <result column="FROM" jdbcType="TINYINT" property="from" />
    <result column="COMPANY_STATUS" jdbcType="BOOLEAN" property="companyStatus" />
    <result column="INDENTITY_STATUS" jdbcType="BOOLEAN" property="indentityStatus" />
    <result column="IS_OPEN_STORE" jdbcType="BOOLEAN" property="isOpenStore" />
    <result column="IS_VEDENG_JX" jdbcType="BOOLEAN" property="isVedengJx" />
    <result column="ACCOUNT" jdbcType="VARCHAR" property="account" />
    <result column="EMAIL" jdbcType="VARCHAR" property="email" />
    <result column="MOBILE" jdbcType="VARCHAR" property="mobile" />
    <result column="COMPANY_NAME" jdbcType="VARCHAR" property="companyName" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="SEX" jdbcType="BOOLEAN" property="sex" />
    <result column="WEIXIN_OPENID" jdbcType="VARCHAR" property="weixinOpenid" />
    <result column="UUID" jdbcType="VARCHAR" property="uuid" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="LAST_LOGIN_TIME" jdbcType="BIGINT" property="lastLoginTime" />
    <result column="IS_VEDENG_JOIN" jdbcType="TINYINT" property="isVedengJoin" />
    <result column="MOD_TIME_JOIN" jdbcType="BIGINT" property="modTimeJoin" />
    <result column="IS_SEND_MESSAGE" jdbcType="BOOLEAN" property="isSendMessage" />
    <result column="REGISTER_PLATFORM" jdbcType="INTEGER" property="registerPlatform" />
    <result column="BELONG_PLATFORM" jdbcType="INTEGER" property="belongPlatform" />
    <result column="IS_VEDENG_MEMBER" jdbcType="BOOLEAN" property="isVedengMember" />
    <result column="VEDENG_MEMBER_TIME" jdbcType="TIMESTAMP" property="vedengMemberTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="IS_REGIONAL_MALL" jdbcType="INTEGER" property="isRegionalMall" />
    <result column="REGISTER_REGIONAL_MALL" jdbcType="INTEGER" property="registerRegionalMall" />
    <result column="AUTH_TYPE" jdbcType="INTEGER" property="authType" />
    <result column="ACCOUNT_TYPE" jdbcType="BOOLEAN" property="accountType" />
    <result column="ACCOUNT_ROLE" jdbcType="BOOLEAN" property="accountRole" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ERP_ACCOUNT_ID, WEB_ACCOUNT_ID, SSO_ACCOUNT_ID, TRADER_ID, TRADER_CONTACT_ID, TRADER_ADDRESS_ID, 
    USER_ID, IS_ENABLE, `FROM`, COMPANY_STATUS, INDENTITY_STATUS, IS_OPEN_STORE, IS_VEDENG_JX, 
    ACCOUNT, EMAIL, MOBILE, COMPANY_NAME, `NAME`, SEX, WEIXIN_OPENID, UUID, ADD_TIME, 
    LAST_LOGIN_TIME, IS_VEDENG_JOIN, MOD_TIME_JOIN, IS_SEND_MESSAGE, REGISTER_PLATFORM, 
    BELONG_PLATFORM, IS_VEDENG_MEMBER, VEDENG_MEMBER_TIME, MOD_TIME, IS_REGIONAL_MALL, 
    REGISTER_REGIONAL_MALL, AUTH_TYPE, ACCOUNT_TYPE, ACCOUNT_ROLE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_WEB_ACCOUNT
    where ERP_ACCOUNT_ID = #{erpAccountId,jdbcType=INTEGER}
  </select>

  <select id="selectByTraderId" resultType="com.vedeng.mobile.system.model.WebAccount">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_WEB_ACCOUNT
    where TRADER_ID = #{traderId,jdbcType=INTEGER}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_WEB_ACCOUNT
    where ERP_ACCOUNT_ID = #{erpAccountId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ERP_ACCOUNT_ID" keyProperty="erpAccountId" parameterType="com.vedeng.mobile.system.model.WebAccount" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WEB_ACCOUNT (WEB_ACCOUNT_ID, SSO_ACCOUNT_ID, TRADER_ID, 
      TRADER_CONTACT_ID, TRADER_ADDRESS_ID, USER_ID, 
      IS_ENABLE, `FROM`, COMPANY_STATUS, 
      INDENTITY_STATUS, IS_OPEN_STORE, IS_VEDENG_JX, 
      ACCOUNT, EMAIL, MOBILE, 
      COMPANY_NAME, `NAME`, SEX, 
      WEIXIN_OPENID, UUID, ADD_TIME, 
      LAST_LOGIN_TIME, IS_VEDENG_JOIN, MOD_TIME_JOIN, 
      IS_SEND_MESSAGE, REGISTER_PLATFORM, BELONG_PLATFORM, 
      IS_VEDENG_MEMBER, VEDENG_MEMBER_TIME, MOD_TIME, 
      IS_REGIONAL_MALL, REGISTER_REGIONAL_MALL, AUTH_TYPE, 
      ACCOUNT_TYPE, ACCOUNT_ROLE)
    values (#{webAccountId,jdbcType=INTEGER}, #{ssoAccountId,jdbcType=INTEGER}, #{traderId,jdbcType=INTEGER}, 
      #{traderContactId,jdbcType=INTEGER}, #{traderAddressId,jdbcType=INTEGER}, #{userId,jdbcType=INTEGER}, 
      #{isEnable,jdbcType=BOOLEAN}, #{from,jdbcType=TINYINT}, #{companyStatus,jdbcType=BOOLEAN}, 
      #{indentityStatus,jdbcType=BOOLEAN}, #{isOpenStore,jdbcType=BOOLEAN}, #{isVedengJx,jdbcType=BOOLEAN}, 
      #{account,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, 
      #{companyName,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{sex,jdbcType=BOOLEAN}, 
      #{weixinOpenid,jdbcType=VARCHAR}, #{uuid,jdbcType=VARCHAR}, #{addTime,jdbcType=BIGINT}, 
      #{lastLoginTime,jdbcType=BIGINT}, #{isVedengJoin,jdbcType=TINYINT}, #{modTimeJoin,jdbcType=BIGINT}, 
      #{isSendMessage,jdbcType=BOOLEAN}, #{registerPlatform,jdbcType=INTEGER}, #{belongPlatform,jdbcType=INTEGER}, 
      #{isVedengMember,jdbcType=BOOLEAN}, #{vedengMemberTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, 
      #{isRegionalMall,jdbcType=INTEGER}, #{registerRegionalMall,jdbcType=INTEGER}, #{authType,jdbcType=INTEGER}, 
      #{accountType,jdbcType=BOOLEAN}, #{accountRole,jdbcType=BOOLEAN})
  </insert>
  <insert id="insertSelective" keyColumn="ERP_ACCOUNT_ID" keyProperty="erpAccountId" parameterType="com.vedeng.mobile.system.model.WebAccount" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WEB_ACCOUNT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="webAccountId != null">
        WEB_ACCOUNT_ID,
      </if>
      <if test="ssoAccountId != null">
        SSO_ACCOUNT_ID,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID,
      </if>
      <if test="traderAddressId != null">
        TRADER_ADDRESS_ID,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="isEnable != null">
        IS_ENABLE,
      </if>
      <if test="from != null">
        `FROM`,
      </if>
      <if test="companyStatus != null">
        COMPANY_STATUS,
      </if>
      <if test="indentityStatus != null">
        INDENTITY_STATUS,
      </if>
      <if test="isOpenStore != null">
        IS_OPEN_STORE,
      </if>
      <if test="isVedengJx != null">
        IS_VEDENG_JX,
      </if>
      <if test="account != null">
        ACCOUNT,
      </if>
      <if test="email != null">
        EMAIL,
      </if>
      <if test="mobile != null">
        MOBILE,
      </if>
      <if test="companyName != null">
        COMPANY_NAME,
      </if>
      <if test="name != null">
        `NAME`,
      </if>
      <if test="sex != null">
        SEX,
      </if>
      <if test="weixinOpenid != null">
        WEIXIN_OPENID,
      </if>
      <if test="uuid != null">
        UUID,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="lastLoginTime != null">
        LAST_LOGIN_TIME,
      </if>
      <if test="isVedengJoin != null">
        IS_VEDENG_JOIN,
      </if>
      <if test="modTimeJoin != null">
        MOD_TIME_JOIN,
      </if>
      <if test="isSendMessage != null">
        IS_SEND_MESSAGE,
      </if>
      <if test="registerPlatform != null">
        REGISTER_PLATFORM,
      </if>
      <if test="belongPlatform != null">
        BELONG_PLATFORM,
      </if>
      <if test="isVedengMember != null">
        IS_VEDENG_MEMBER,
      </if>
      <if test="vedengMemberTime != null">
        VEDENG_MEMBER_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="isRegionalMall != null">
        IS_REGIONAL_MALL,
      </if>
      <if test="registerRegionalMall != null">
        REGISTER_REGIONAL_MALL,
      </if>
      <if test="authType != null">
        AUTH_TYPE,
      </if>
      <if test="accountType != null">
        ACCOUNT_TYPE,
      </if>
      <if test="accountRole != null">
        ACCOUNT_ROLE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="webAccountId != null">
        #{webAccountId,jdbcType=INTEGER},
      </if>
      <if test="ssoAccountId != null">
        #{ssoAccountId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderContactId != null">
        #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderAddressId != null">
        #{traderAddressId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="isEnable != null">
        #{isEnable,jdbcType=BOOLEAN},
      </if>
      <if test="from != null">
        #{from,jdbcType=TINYINT},
      </if>
      <if test="companyStatus != null">
        #{companyStatus,jdbcType=BOOLEAN},
      </if>
      <if test="indentityStatus != null">
        #{indentityStatus,jdbcType=BOOLEAN},
      </if>
      <if test="isOpenStore != null">
        #{isOpenStore,jdbcType=BOOLEAN},
      </if>
      <if test="isVedengJx != null">
        #{isVedengJx,jdbcType=BOOLEAN},
      </if>
      <if test="account != null">
        #{account,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        #{sex,jdbcType=BOOLEAN},
      </if>
      <if test="weixinOpenid != null">
        #{weixinOpenid,jdbcType=VARCHAR},
      </if>
      <if test="uuid != null">
        #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="lastLoginTime != null">
        #{lastLoginTime,jdbcType=BIGINT},
      </if>
      <if test="isVedengJoin != null">
        #{isVedengJoin,jdbcType=TINYINT},
      </if>
      <if test="modTimeJoin != null">
        #{modTimeJoin,jdbcType=BIGINT},
      </if>
      <if test="isSendMessage != null">
        #{isSendMessage,jdbcType=BOOLEAN},
      </if>
      <if test="registerPlatform != null">
        #{registerPlatform,jdbcType=INTEGER},
      </if>
      <if test="belongPlatform != null">
        #{belongPlatform,jdbcType=INTEGER},
      </if>
      <if test="isVedengMember != null">
        #{isVedengMember,jdbcType=BOOLEAN},
      </if>
      <if test="vedengMemberTime != null">
        #{vedengMemberTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isRegionalMall != null">
        #{isRegionalMall,jdbcType=INTEGER},
      </if>
      <if test="registerRegionalMall != null">
        #{registerRegionalMall,jdbcType=INTEGER},
      </if>
      <if test="authType != null">
        #{authType,jdbcType=INTEGER},
      </if>
      <if test="accountType != null">
        #{accountType,jdbcType=BOOLEAN},
      </if>
      <if test="accountRole != null">
        #{accountRole,jdbcType=BOOLEAN},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.mobile.system.model.WebAccount">
    <!--@mbg.generated-->
    update T_WEB_ACCOUNT
    <set>
      <if test="webAccountId != null">
        WEB_ACCOUNT_ID = #{webAccountId,jdbcType=INTEGER},
      </if>
      <if test="ssoAccountId != null">
        SSO_ACCOUNT_ID = #{ssoAccountId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderAddressId != null">
        TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      <if test="isEnable != null">
        IS_ENABLE = #{isEnable,jdbcType=BOOLEAN},
      </if>
      <if test="from != null">
        `FROM` = #{from,jdbcType=TINYINT},
      </if>
      <if test="companyStatus != null">
        COMPANY_STATUS = #{companyStatus,jdbcType=BOOLEAN},
      </if>
      <if test="indentityStatus != null">
        INDENTITY_STATUS = #{indentityStatus,jdbcType=BOOLEAN},
      </if>
      <if test="isOpenStore != null">
        IS_OPEN_STORE = #{isOpenStore,jdbcType=BOOLEAN},
      </if>
      <if test="isVedengJx != null">
        IS_VEDENG_JX = #{isVedengJx,jdbcType=BOOLEAN},
      </if>
      <if test="account != null">
        ACCOUNT = #{account,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        EMAIL = #{email,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        MOBILE = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `NAME` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        SEX = #{sex,jdbcType=BOOLEAN},
      </if>
      <if test="weixinOpenid != null">
        WEIXIN_OPENID = #{weixinOpenid,jdbcType=VARCHAR},
      </if>
      <if test="uuid != null">
        UUID = #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="lastLoginTime != null">
        LAST_LOGIN_TIME = #{lastLoginTime,jdbcType=BIGINT},
      </if>
      <if test="isVedengJoin != null">
        IS_VEDENG_JOIN = #{isVedengJoin,jdbcType=TINYINT},
      </if>
      <if test="modTimeJoin != null">
        MOD_TIME_JOIN = #{modTimeJoin,jdbcType=BIGINT},
      </if>
      <if test="isSendMessage != null">
        IS_SEND_MESSAGE = #{isSendMessage,jdbcType=BOOLEAN},
      </if>
      <if test="registerPlatform != null">
        REGISTER_PLATFORM = #{registerPlatform,jdbcType=INTEGER},
      </if>
      <if test="belongPlatform != null">
        BELONG_PLATFORM = #{belongPlatform,jdbcType=INTEGER},
      </if>
      <if test="isVedengMember != null">
        IS_VEDENG_MEMBER = #{isVedengMember,jdbcType=BOOLEAN},
      </if>
      <if test="vedengMemberTime != null">
        VEDENG_MEMBER_TIME = #{vedengMemberTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isRegionalMall != null">
        IS_REGIONAL_MALL = #{isRegionalMall,jdbcType=INTEGER},
      </if>
      <if test="registerRegionalMall != null">
        REGISTER_REGIONAL_MALL = #{registerRegionalMall,jdbcType=INTEGER},
      </if>
      <if test="authType != null">
        AUTH_TYPE = #{authType,jdbcType=INTEGER},
      </if>
      <if test="accountType != null">
        ACCOUNT_TYPE = #{accountType,jdbcType=BOOLEAN},
      </if>
      <if test="accountRole != null">
        ACCOUNT_ROLE = #{accountRole,jdbcType=BOOLEAN},
      </if>
    </set>
    where ERP_ACCOUNT_ID = #{erpAccountId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.mobile.system.model.WebAccount">
    <!--@mbg.generated-->
    update T_WEB_ACCOUNT
    set WEB_ACCOUNT_ID = #{webAccountId,jdbcType=INTEGER},
      SSO_ACCOUNT_ID = #{ssoAccountId,jdbcType=INTEGER},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER},
      USER_ID = #{userId,jdbcType=INTEGER},
      IS_ENABLE = #{isEnable,jdbcType=BOOLEAN},
      `FROM` = #{from,jdbcType=TINYINT},
      COMPANY_STATUS = #{companyStatus,jdbcType=BOOLEAN},
      INDENTITY_STATUS = #{indentityStatus,jdbcType=BOOLEAN},
      IS_OPEN_STORE = #{isOpenStore,jdbcType=BOOLEAN},
      IS_VEDENG_JX = #{isVedengJx,jdbcType=BOOLEAN},
      ACCOUNT = #{account,jdbcType=VARCHAR},
      EMAIL = #{email,jdbcType=VARCHAR},
      MOBILE = #{mobile,jdbcType=VARCHAR},
      COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
      `NAME` = #{name,jdbcType=VARCHAR},
      SEX = #{sex,jdbcType=BOOLEAN},
      WEIXIN_OPENID = #{weixinOpenid,jdbcType=VARCHAR},
      UUID = #{uuid,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      LAST_LOGIN_TIME = #{lastLoginTime,jdbcType=BIGINT},
      IS_VEDENG_JOIN = #{isVedengJoin,jdbcType=TINYINT},
      MOD_TIME_JOIN = #{modTimeJoin,jdbcType=BIGINT},
      IS_SEND_MESSAGE = #{isSendMessage,jdbcType=BOOLEAN},
      REGISTER_PLATFORM = #{registerPlatform,jdbcType=INTEGER},
      BELONG_PLATFORM = #{belongPlatform,jdbcType=INTEGER},
      IS_VEDENG_MEMBER = #{isVedengMember,jdbcType=BOOLEAN},
      VEDENG_MEMBER_TIME = #{vedengMemberTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      IS_REGIONAL_MALL = #{isRegionalMall,jdbcType=INTEGER},
      REGISTER_REGIONAL_MALL = #{registerRegionalMall,jdbcType=INTEGER},
      AUTH_TYPE = #{authType,jdbcType=INTEGER},
      ACCOUNT_TYPE = #{accountType,jdbcType=BOOLEAN},
      ACCOUNT_ROLE = #{accountRole,jdbcType=BOOLEAN}
    where ERP_ACCOUNT_ID = #{erpAccountId,jdbcType=INTEGER}
  </update>
</mapper>