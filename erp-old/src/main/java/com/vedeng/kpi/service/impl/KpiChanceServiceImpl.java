package com.vedeng.kpi.service.impl;

import com.google.common.collect.Lists;
import com.vedeng.kpi.model.DO.KpiDailyCountDo;
import com.vedeng.kpi.model.DO.KpiOrderLogDo;
import com.vedeng.kpi.model.DTO.KpiDataQueryDto;
import com.vedeng.kpi.model.DTO.KpiUserInfoDto;
import com.vedeng.kpi.service.KpiBaseService;
import com.vedeng.kpi.service.KpiChanceService;
import com.vedeng.kpi.share.KpiCommonConstant;
import com.vedeng.kpi.share.KpiUtils;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 五行商机处理.
 * @jira: VDERP-2376【五行剑法】规则修改.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/7/9 10:11 上午.
 * @author: Tomcat.Hui.
 */
@Service
public class KpiChanceServiceImpl extends KpiBaseService implements KpiChanceService{

    @Override
    public void dataHandler(KpiUserInfoDto userInfo) {

        String kpiDateStr = getDateStr(userInfo.getKpiDateEnd());
        log.info("开始更新用户 " + userInfo.getUserIdName() + " " + kpiDateStr + " 日 " +
                KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_XJ);

        KpiDataQueryDto query = kpiParamTransService.baseParamsToQuery(userInfo);
        KpiDailyCountDo kpiDailyCount = kpiParamTransService.baseParamsToCount(userInfo);

        //成交/失败数量
        query.setUserIds(kpiLoadingCache.getGroupUserIdsByUserId(userInfo.getUserId()));
        List<KpiDataQueryDto> chanceTransList = getGroupChancesMonth();
        Map<Integer,Integer> userMap = this.getSuccessFailMap(chanceTransList,userInfo.getUserId());
        Long successNum = 0L;
        Long failNum = 0L;
        if (null != userMap) {
            // changed by Tomcat.Hui 2020/8/24 4:15 下午 .Desc: 转化计算有误. start
            // = 0表示商机有转化且有转化失败,不计入转化且不计入失败
            successNum = userMap.entrySet().stream().filter(kv -> kv.getValue() >= 0).count();
            failNum = userMap.entrySet().stream().filter(kv -> kv.getValue() <= 0).count();
        }

        kpiDailyCount.setChanceSuccessNum(successNum.intValue());
        kpiDailyCount.setChanceFailNum(failNum.intValue());
        log.info("用户 " + userInfo.getUserIdName() + " " + kpiDateStr + " 日 " +
                KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_XJ + " 询价成功/失败数量: " + successNum + "/" + failNum);

        //查询整个团队的区间询价新客数
        List<KpiDataQueryDto> chanceRangeList = getGroupRangeNewChanceMonth();
        /** 2020-07-08 要求加入逻辑:
         * 因为是否计入询价转换是以"商机分发时间在180天内"为条件,
         * 但是计算转换率 = 发生在当月的转换 / 当月区间新客户询价数量
         * 所以可能存在非当月分发的商机在当月转化的场景
         * 要求对 区间新客户询价数量 进行扩容 : 区间新客户询价数量 = 当月区间新客户询价数量 + 非当月分发的商机在当月转化的数量
         * */
        //区间新客数
        Long rangeChanceNum = getRangeChanceNum(userInfo.getUserId(),userInfo.getKpiDateEnd(),
                chanceTransList, chanceRangeList);
        kpiDailyCount.setChanceRangeCustomerNum(rangeChanceNum.intValue());

        //个人转化率
        Double proportion = this.getChanceTranceProportion(successNum.intValue(),failNum.intValue(),
                rangeChanceNum, query.getUserId(),userInfo.getKpiDate());
        kpiDailyCount.setChanceTransProportion(new BigDecimal(proportion));
        log.info("用户 " + userInfo.getUserIdName() + " " + kpiDateStr + " 日 " +
                KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_XJ + " 询价转化率: " + proportion);

        //得分 个人得分=（个人询价转化率/团队询价转化率）*加权值（加权值=该项指标的考核比例*100）
        BigDecimal score = new BigDecimal(countCurrentUserScore(userInfo,proportion,chanceTransList,chanceRangeList));
        kpiDailyCount.setChanceTransScore(score.setScale(8, RoundingMode.HALF_UP));
        log.info("用户 " + userInfo.getUserIdName() + " " + kpiDateStr + " 日 " +
                KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_XJ + " 得分: " + score);

        insertOrUpdateCount(kpiDailyCount);
        log.info("用户 " + userInfo.getUserIdName() + " " + kpiDateStr + " 日 " +
                KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_XJ + " 更新结束");
    }


    /**
     * 计算当前用户个人得分.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/9 8:56 上午.
     * @author: Tomcat.Hui.
     * @param userInfo: 用户信息.
     * @param proportion: 转化率.
     * @param chanceTransList: 团队商机转换list.
     * @param rangeNewCustomers: 团队区间新客list.
     * @return: java.lang.Double 当前用户得分.
     * @throws: .
     */
    private Double countCurrentUserScore(KpiUserInfoDto userInfo,
                                         Double proportion,
                                         List<KpiDataQueryDto> chanceTransList,
                                         List<KpiDataQueryDto> rangeNewCustomers){
        try {
            BigDecimal weight = getWeight(userInfo.getGroupId(),KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_XJ);

            log.info("用户 " + userInfo.getUserIdName() + " " + KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_XJ +
                    " 团队 " + userInfo.getGroupName() + " 权重: " + weight);

            /** 获取团队成员平均转化率 */
            Map<Integer, Map<Integer, Integer>> groupChanceMap = this.getGroupTransChance(chanceTransList);

            Double sumTransProportion = groupChanceMap.keySet().stream().map(userId ->
                    getChanceTranceProportion(
                            (int)groupChanceMap.get(userId).values().stream().filter(c -> c >= 0).count(),
                            (int)groupChanceMap.get(userId).values().stream().filter(c -> c <= 0).count(),
                            getRangeChanceNum(userId,userInfo.getKpiDateEnd(),chanceTransList,rangeNewCustomers),
                            userId,userInfo.getKpiDate()
                            )).collect(Collectors.summingDouble(Double::doubleValue));

            double ave = sumTransProportion / kpiLoadingCache.getGroupUserIdsByUserId(userInfo.getUserId()).size();

            log.info("用户 " + userInfo.getUserIdName() + " " + KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_XJ +
                    " 团队 " + userInfo.getGroupName() + " 平均转化率: " + ave + " 人数: " + kpiLoadingCache.getGroupUserIdsByUserId(userInfo.getUserId()).size());

            if (new BigDecimal(ave).compareTo(BigDecimal.ZERO) == 0) {
                return 0.0;
            }
            return proportion / ave * weight.doubleValue();
        }catch (Exception e){
            log.error("用户 " + userInfo.getUserIdName() + " " + KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_XJ + " 计算个人得分出现异常: ",e);
            throw e;
        }
    }

    /**
     * 获取区间客户数.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/9 9:25 上午.
     * @author: Tomcat.Hui.
     * @param userId: 用户ID.
     * @param endDate: 本次计算kpi结束时间.
     * @param chanceTransList: 团队转换list.
     * @param chanceRangeList: 区间新客list.
     * @return: java.lang.Long.
     * @throws: .
     */
    private Long getRangeChanceNum(Integer userId,
                                   Date endDate,
                                   List<KpiDataQueryDto> chanceTransList,
                                   List<KpiDataQueryDto> chanceRangeList) {
        Long rangeChanceNum = chanceRangeList.stream().filter(c -> c.getUserId().equals(userId)).count();
        log.info("用户 " + getIdName(userId) + " 区间新客户询价数量 " + rangeChanceNum);

        Long extChanceNum = chanceTransList.stream()
                .filter(c -> c.getUserId().equals(userId))
                .filter(c -> !KpiUtils.getMonthStart(new Date(c.getChanceAddTime()))
                        .equals(KpiUtils.getMonthStart(endDate)))
                .filter(c -> KpiUtils.getDateDiff(endDate,new Date(c.getChanceAddTime())) <= 180).count();
        log.info("用户 " + getIdName(userId) + " 转化非当月180天内分发询价数量 " + extChanceNum);
        rangeChanceNum = rangeChanceNum + extChanceNum;

        return rangeChanceNum;
    }


    /**
     * 获取用户分配到的总机商机.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/9 8:58 上午.
     * @author: Tomcat.Hui.
     * @param query: .
     * @return: java.util.List<com.vedeng.kpi.model.DTO.KpiDataQueryDto>.
     * @throws: .
     */
    private List<KpiDataQueryDto> getReceivedZjBussinessChances(KpiDataQueryDto query) {
        query.setStartMillisecond(query.getKpiDateStart().getTime());
        query.setEndMillisecond(KpiUtils.getDateEnd(query.getKpiDateEnd()).getTime());
        query.setCompanyId(1);
        return kpiChanceMapper.getReceivedZjBussinessChances(query);
    }

    /**
     * 获取单个用户成功失败数量.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/6/11 3:09 下午.
     * @author: Tomcat.Hui.
     * @param chanceTransList: 团队转化list.
     * @return: java.util.Map<java.lang.Integer,java.util.Map<java.lang.Integer,java.lang.Integer>>.
     * @throws: .
     */
    private Map<Integer, Integer> getSuccessFailMap(List<KpiDataQueryDto> chanceTransList,Integer userId) {
        Map<Integer, Map<Integer, Integer>> groupMap = getGroupTransChance(chanceTransList);
        return groupMap.get(userId);
    }

    /**
     * 获取团队转化商机map.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/15 3:11 下午.
     * @author: Tomcat.Hui.
     * @param chanceTransList: .
     * @return: java.util.Map<java.lang.Integer,java.util.Map<java.lang.Integer,java.lang.Integer>>.
     * @throw: .
     */
    private Map<Integer, Map<Integer, Integer>> getGroupTransChance(List<KpiDataQueryDto> chanceTransList) {
        return chanceTransList.stream()
                        .collect(Collectors.groupingBy(KpiDataQueryDto::getUserId,
                                Collectors.groupingBy(KpiDataQueryDto::getChanceId,
                                        Collectors.summingInt(KpiOrderLogDo::getChanceTransFlag))));
    }

    /**
     * 获取单个用户商机转化率.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/15 4:04 下午.
     * @author: Tomcat.Hui.
     * @param successNum: .
     * @param failNum: .
     * @param rangeChanceNum: .
     * @param userId: .
     * @param kpiDate: .
     * @return: java.lang.Double.
     * @throw: .
     */
    private Double getChanceTranceProportion(Integer successNum,
                                             Integer failNum,
                                             Long rangeChanceNum,
                                             Integer userId,
                                             Date kpiDate){

        if (new BigDecimal(rangeChanceNum).compareTo(BigDecimal.ZERO) == 0) {
           return 0.0;
        }
        Double proportion = new Double(successNum - failNum) / new Double(rangeChanceNum);
        if (proportion > 0.2) {
            KpiDataQueryDto query = new KpiDataQueryDto();
            query.setUserId(userId);
            query.setCompanyId(1);
            query.setStartMillisecond(KpiUtils.getMonthStart(kpiDate).getTime());
            query.setEndMillisecond(KpiUtils.getDateEnd(kpiDate).getTime());
            //VDERP-3094【五行剑法】优化得分排名和转化率逻辑.
            //原询价转化规则中的“个人转化率>20%且总机分配询价数量<=10时，转化率=20%”修改为“个人转化率>20%且总机+自主询价数量（去重后的）<=10时，转化率=20%”
            List<KpiDataQueryDto> receivedChances = kpiChanceMapper.getReceivedZjBussinessChances(query);
            //总机询价数量<=10 且转化率>0.2 时,限制转化率最高为0.2
            if(receivedChances.size() <= 10){
                log.info("用户 " + getIdName(query.getUserId()) + " 分配到的总机+自主询价数量 " + receivedChances.size() + " <= 10");
                proportion = 0.2;
            }
        }
        log.info("用户 " + getIdName(userId) + "成功数量: " + successNum + " 失败数量: " + failNum +
                " 区间新客数: " + rangeChanceNum  + " 计算出转化率 " + proportion);
        return proportion;
    }

    /**
     * 计算单人得分.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/9 9:03 上午.
     * @author: Tomcat.Hui.
     * @param chanceTransList: 团队转化数量.
     * @param rangeNum: 区间新客数量.
     * @param query: .
     * @return: java.lang.Double.
     * @throws: .
     */
    private Double countScoreSingle(List<KpiDataQueryDto> chanceTransList,Long rangeNum,
                                    KpiDataQueryDto query) {
        Map<Integer,Integer> userMap = this.getSuccessFailMap(chanceTransList,query.getUserId());
        Long successNum = 0L;
        Long failNum = 0L;
        if (null != userMap) {
            successNum = userMap.entrySet().stream().filter(kv -> kv.getValue() > 0).count();
            failNum = userMap.entrySet().stream().filter(kv -> kv.getValue() == 0).count();
        }
        return getChanceTranceProportion(successNum.intValue(),failNum.intValue(),rangeNum,query.getUserId(),query.getKpiDate());
    }

    @Override
    public Integer getUserBussinessChancesNum(KpiDataQueryDto query) {
        List<KpiDataQueryDto> result = this.getReceivedZjBussinessChances(query);
        Integer num = (int)Optional.ofNullable(result).orElse(Lists.newArrayList()).stream().count();
//        log.info("查询用户 " + query.getUserId() + " " + query.getKpiDateStart() + "-" + query.getKpiDateEnd()
//                + " 分配到的总机询价数量 " + num);
        return num;
    }
}


