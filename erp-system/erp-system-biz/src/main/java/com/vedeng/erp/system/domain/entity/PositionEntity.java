package com.vedeng.erp.system.domain.entity;

/**
    * 职位
    */
public class PositionEntity {
    /**
    * 职位ID
    */
    private Integer positionId;

    /**
    * 部门ID
    */
    private Integer orgId;

    /**
    * 职位名称
    */
    private String positionName;

    /**
    * 职位类型 字典库ID
    */
    private Integer type;

    /**
    * 职位级别 字典库ID
    */
    private Integer level;

    /**
    * 添加时间
    */
    private Long addTime;

    /**
    * 添加人
    */
    private Integer creator;

    /**
    * 最近一次编辑时间
    */
    private Long modTime;

    /**
    * 最近一次编辑人
    */
    private Integer updater;

    public Integer getPositionId() {
        return positionId;
    }

    public void setPositionId(Integer positionId) {
        this.positionId = positionId;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getPositionName() {
        return positionName;
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Long getModTime() {
        return modTime;
    }

    public void setModTime(Long modTime) {
        this.modTime = modTime;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }
}