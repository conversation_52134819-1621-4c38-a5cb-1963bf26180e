package com.vedeng.erp.kingdee.mapstruct;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveBillEntity;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveBillDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveBillEntryDto;
import org.mapstruct.*;

import java.util.Collections;
import java.util.List;

/**
 * 收款单实体类转换
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,builder = @Builder(disableBuilder = true))
public interface KingDeeReceiveBillConvertor extends BaseMapStruct<KingDeeReceiveBillEntity, KingDeeReceiveBillDto> {

    @Override
    @Mapping(target = "FReceiveBillEntry", source = "FReceiveBillEntry", qualifiedByName = "srcEntryListToJsonArray")
    @Mapping(target = "erpBankBillId",source = "bankBillId")
    KingDeeReceiveBillEntity toEntity(KingDeeReceiveBillDto dto);

    @Override
    @Mapping(target = "FReceiveBillEntry", source = "FReceiveBillEntry", qualifiedByName = "srcEntryJsonArrayToList")
    @Mapping(target = "bankBillId",source = "erpBankBillId")
    KingDeeReceiveBillDto toDto(KingDeeReceiveBillEntity entity);

    /**
     * entity 中JSONArray 转 原对象
     *
     * @param receiveBillEntryDtos JSONArray
     * @return List<KingDeePayBillDto> dto中的对象
     */
    @Named("srcEntryJsonArrayToList")
    default List<KingDeeReceiveBillEntryDto> srcEntryJsonArrayToList(JSONArray receiveBillEntryDtos) {
        if (CollUtil.isEmpty(receiveBillEntryDtos)) {
            return Collections.emptyList();
        }
        return receiveBillEntryDtos.toJavaList(KingDeeReceiveBillEntryDto.class);
    }


    /**
     * dto 原对象中 转 JSONArray
     *
     * @param source 对象
     * @return JSONArray JSONArray
     */
    @Named("srcEntryListToJsonArray")
    default JSONArray srcEntryListToJsonArray(List<KingDeeReceiveBillEntryDto> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSONArray.parseArray(JSON.toJSONString(source));
    }
}
