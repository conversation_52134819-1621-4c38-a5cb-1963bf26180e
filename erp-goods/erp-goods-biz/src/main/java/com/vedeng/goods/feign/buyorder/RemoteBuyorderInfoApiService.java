package com.vedeng.goods.feign.buyorder;


import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.common.feign.constants.ServerConstants;
import com.vedeng.erp.buyorder.dto.BuyOrderDto;
import feign.Param;
import feign.RequestLine;

import java.util.List;

/**
 * 远程调用erp-buyorder
 *
 * <AUTHOR>
 */
@FeignApi(serverName = ServerConstants.ERP_SERVER)
public interface RemoteBuyorderInfoApiService {


    @RequestLine("GET /buyorder/listGoodsOnWayInfo.do?skuId={skuId}")
    List<BuyOrderDto> listGoodsOnwayInfo(@Param("skuId") Integer skuId);
}
