void function () {
    new Vue({
        el: '#page-container',
        data: {
            excludeSaleIds: [],
            excludeSaleList: [],
            aedSkuIds: [],
            aedSkuList: [],
            excludeTraderIds: [],
            excludeTraderList: [],
            remoteUserInfo: {},
            targetProjectList: [{
                label: '日常到款播报',
                value: 1
            }, {
                label: '周度到款Top',
                value: 2
            }, {
                label: '月度到款Top',
                value: 3
            }, {
                label: '月度AED TOP',
                value: 4
            }, {
                label: '月度自有品牌TOP',
                value: 5
            }, {
                label: '自定义播报',
                value: 6
            }],
            tongjiList: [{
                label: '出库数量',
                value: 2
            }, {
                label: '出库金额',
                value: 3
            }],
            timeRangeList: [{
                label: '本日',
                value: 1
            }, {
                label: '本周',
                value: 2
            }, {
                label: '本月',
                value: 3
            }],
            broadTargetList: [{
                label: '个人',
                value: '1'
            }, {
                label: '小组',
                value: '2'
            }, {
                label: '部门',
                value: '3'
            }],
            userRemoteInfo: {
                url: '/system/user/searchUserListForSelectLimit.do',
                paramsMethod: 'get',
                paramsKey: 'username',
            },
            skuRemoteInfo: {
                url: '/goods/vgoods/searchSku.do',
                paramsType: 'url',
                paramsKey: 'keyword',
                showLabel: 'name'
            },
            traderRemoteInfo: {
                url: '/traderCustomerBase/queryWithDefault.do',
                paramsKey: 'name',
                paramsType: 'url'
            },
            brandRemoteInfo: {
                url: '/goods/brand/searchBrand.do',
                paramsKey: 'keyword',
                paramsType: 'url'
            },
            loadingEl: null,
            allCategoryList: [],
            broadcastTitleDay: '',
            broadcastTitleWeek: '',
            broadcastTitleMonth: '',
            broadcastTitleAed: '',
            broadcastTitleZy: '',
            broadcastTitleCustom: '',
            jobTimeStrMap: {},
            topnUser: '',
            topnDept: '',
            deptConfigList: [],
            statType: '',
            statDateRange: '',
            statTargetArr: [],
            statSkuIds: [],
            statSkuList: [],
            statBrandIds: [],
            statBrandList: [],
            statCategoryIds: [],
            pageloading: true,
            broadcastTitleErrorMsg: '',
            amountStepErrorMsg: '',
            broadcastProjectParse: [{
                key: 'dayFlag',
                value: 1
            }, {
                key: 'weekFlag',
                value: 2
            }, {
                key: 'monthFlag',
                value: 3
            }, {
                key: 'aedFlag',
                value: 4
            }, {
                key: 'zyFlag',
                value: 5
            }, {
                key: 'customFlag',
                value: 6
            }],
            cansubmit: true,
        },
        async created() {
            this.showloading();
            this.pageloading = true;

            await this.getAllCategoryList();

            await this.getInitData();

            this.bindFormValid();

            this.hideloading();
            this.pageloading = false;

        },
        methods: {
            getAllCategoryList() {
                return axios.post('/category/base/treeCategory.do?parentId=0').then(({ data }) => {
                    if (data.code === 0) {
                        this.allCategoryList = data.data || [];
                    }
                })
            },
            getInitData() {
                return axios.post('	/broadcast/config/init.do').then(({ data }) => {
                    if (data.code === 0) {
                        let resData = data.data || {};

                        this.excludeSaleIds = resData.excludeSaleIds ? resData.excludeSaleIds.split(',') : [];
                        this.aedSkuIds = resData.aedSkuIds ? resData.aedSkuIds.split(',') : [];
                        this.excludeTraderIds = resData.excludeTraderIds ? resData.excludeTraderIds.split(',') : [];

                        this.broadcastTitleDay = resData.broadcastTitleDay || '';
                        this.broadcastTitleWeek = resData.broadcastTitleWeek || '';
                        this.broadcastTitleMonth = resData.broadcastTitleMonth || '';
                        this.broadcastTitleAed = resData.broadcastTitleAed || '';
                        this.broadcastTitleZy = resData.broadcastTitleZy || '';
                        this.broadcastTitleCustom = resData.broadcastTitleCustom || '';
                        this.jobTimeStrMap = resData.jobTimeStrMap || {};
                        this.topnUser = resData.topnUser || '';
                        this.topnDept = resData.topnDept || '';

                        let deptConfigList = resData.deptConfigList || [];

                        deptConfigList.forEach(item => {
                            let checkValues = [];
                            this.broadcastProjectParse.forEach((parseItem, index) => {
                                if (item[parseItem.key]) {
                                    checkValues.push(parseItem.value);
                                }
                            })

                            item.checkValues = checkValues;
                        });

                        this.deptConfigList = deptConfigList;

                        this.statType = resData.statType || '';
                        this.statDateRange = resData.statDateRange || '';
                        this.statTargetArr = resData.statTarget ? resData.statTarget.split(',') : [];

                        if(resData.excludeSaleIdList && resData.excludeSaleIdList.length) {
                            let excludeSaleIds = [];

                            resData.excludeSaleIdList.forEach(item => {
                                excludeSaleIds.push(item.value);
                            })

                            this.excludeSaleIds = excludeSaleIds;
                            this.excludeSaleList = resData.excludeSaleIdList;
                        }

                        if(resData.aedSkuIdList && resData.aedSkuIdList.length) {
                            let aedSkuIds = [];

                            resData.aedSkuIdList.forEach(item => {
                                aedSkuIds.push(item.value);
                            })

                            this.aedSkuIds = aedSkuIds;
                            this.aedSkuList = resData.aedSkuIdList;
                        }
                        
                        if(resData.excludeTraderIdList && resData.excludeTraderIdList.length) {
                            let excludeTraderIds = [];

                            resData.excludeTraderIdList.forEach(item => {
                                excludeTraderIds.push(item.value);
                            })

                            this.excludeTraderIds = excludeTraderIds;
                            this.excludeTraderList = resData.excludeTraderIdList;
                        }

                        if(resData.statSkuIdList && resData.statSkuIdList.length) {
                            let statSkuIds = [];

                            resData.statSkuIdList.forEach(item => {
                                statSkuIds.push(item.value);
                            })

                            this.statSkuIds = statSkuIds;
                            this.statSkuList = resData.statSkuIdList;
                        }

                        if(resData.statBrandIdList && resData.statBrandIdList.length) {
                            let statBrandIds = [];

                            resData.statBrandIdList.forEach(item => {
                                statBrandIds.push(item.value);
                            })

                            this.statBrandIds = statBrandIds;
                            this.statBrandList = resData.statBrandIdList;
                        }

                        if(resData.statCategoryIdList && resData.statCategoryIdList.length) {
                            let statCategoryIds = [];

                            this.allCategoryList.forEach(lv1 => {
                                lv1.children && lv1.children.forEach(lv2 => {
                                    lv2.children && lv2.children.forEach(lv3 => {
                                        resData.statCategoryIdList.forEach(item => {
                                            if(item.value == lv3.value) {
                                                statCategoryIds.push([lv1.value, lv2.value, lv3.value]);
                                            }
                                        })
                                    })
                                })
                            })

                            console.log(statCategoryIds)

                            this.statCategoryIds = statCategoryIds;
                        }
                    }
                })
            },
            bindFormValid() {
                this.$form && this.$form.rules({
                    topnUser: {
                        required: '请填写个人榜单名次'
                    },
                    topnDept: {
                        required: '请填写团队榜单名次'
                    },
                    statType: {
                        required: '请选择统计维度'
                    },
                    statDateRange: {
                        required: '请选择时间范围'
                    },
                    statTargetArr: {
                        required: '请选择播报对象'
                    },
                }, 'BroadcastForm', this);
            },
            validBroadcastTitle() {
                let flag = true;
                ['broadcastTitleDay', 'broadcastTitleWeek', 'broadcastTitleMonth', 'broadcastTitleAed', 'broadcastTitleZy', 'broadcastTitleCustom'].forEach(item => {
                    if (!this[item].trim()) {
                        flag = false;
                    }
                });

                if (!flag) {
                    this.broadcastTitleErrorMsg = '请填写播报标题';
                } else {
                    this.broadcastTitleErrorMsg = '';
                }

                return flag;
            },
            validAmountStep() {
                let flag = true;

                this.deptConfigList.forEach(item => {
                    if (!item.amountStep) {
                        flag = false;
                    }
                })

                if (!flag) {
                    this.amountStepErrorMsg = '请填写日常到款金额梯度';
                } else {
                    this.amountStepErrorMsg = '';
                }

                return flag;
            },
            save() {
                if (this.$form.validForm('BroadcastForm') && this.validBroadcastTitle() && this.validAmountStep()) {
                    let reqData = {
                        excludeSaleIds: this.excludeSaleIds.join(','),
                        aedSkuIds: this.aedSkuIds.join(','),
                        excludeTraderIds: this.excludeTraderIds.join(','),
                        topnUser: this.topnUser,
                        topnDept: this.topnDept,
                        broadcastTitleDay: this.broadcastTitleDay,
                        broadcastTitleWeek: this.broadcastTitleWeek,
                        broadcastTitleMonth: this.broadcastTitleMonth,
                        broadcastTitleAed: this.broadcastTitleAed,
                        broadcastTitleZy: this.broadcastTitleZy,
                        broadcastTitleCustom: this.broadcastTitleCustom,
                        statType: this.statType,
                        statDateRange: this.statDateRange,
                        statTarget: this.statTargetArr.join(','),
                        statSkuIds: this.statSkuIds.join(','),
                        statBrandIds: this.statBrandIds.join(',')
                    };

                    this.deptConfigList.forEach(item => {
                        this.broadcastProjectParse.forEach(parseItem => {
                            if (item.checkValues.indexOf(parseItem.value) !== -1) {
                                item[parseItem.key] = 1;
                            } else {
                                item[parseItem.key] = 0;
                            }
                        })
                    })

                    reqData.deptConfigList = this.deptConfigList;

                    let statCategoryIds = [];
                    this.statCategoryIds.forEach(item => {
                        statCategoryIds.push(item[2]);
                    })
                    reqData.statCategoryIds = statCategoryIds.join(',');

                    console.log(reqData)
                    this.showloading();
                    this.cansubmit = false;
                    axios.post('/broadcast/config/saveOrUpdate.do', reqData).then(({ data }) => {
                        this.hideloading();
                        this.cansubmit = true;

                        if(data.code === 0) {
                            this.$message({
                                message: '保存成功',
                                type: 'success'
                            })
                        } else {
                            this.$message({
                                message: data.message || '保存失败',
                                type: 'error'
                            })
                        }
                    })
                }
            },
            showloading() {
                this.loadingEl = this.$loading({
                    lock: true,
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
            },
            hideloading() {
                this.loadingEl && this.loadingEl.close();
            }
        }
    })
}.call(this);