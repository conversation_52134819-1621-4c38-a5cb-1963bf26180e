package com.vedeng.erp.system.vo;

import lombok.Data;

import java.util.List;

/**
 * 人员与商品分类配置查询VO
 */
@Data
public class RoleUserCategoryConfigQueryVO {
    /**
     * 商品分类ID列表
     */
    private List<Integer> categoryIdList;

    /**
     * 业务人员ID列表
     */
    private List<Integer> businessUserIds;
    
    /**
     * 人员在职状态（0-在职，1-离职）
     */
    private Integer employmentStatus;
    
    /**
     * 部门列表
     */
    private List<Integer> departments;
    
    /**
     * 用户ID列表
     */
    private List<Integer> userIds;
    
    /**
     * 商品分类关键词
     */
    private String categoryKeyword;
} 