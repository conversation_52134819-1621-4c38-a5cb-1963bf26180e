package com.vedeng.erp.business.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Builder;
import lombok.Data;

/**
 * 咨询记录表
 */
@Data
public class BusinessChanceSeekHelp extends BaseEntity {
    /**
    * 主键
    */
    private Long businessChanceSeekHelpId;

    /**
    * 商机ID
    */
    private Integer businessChanceId;

    /**
    * 咨询方案人员ID
    */
    private Integer userId;

    /**
    * 咨询内容
    */
    private String content;

    /**
    * 咨询状态：0未处理 1已处理 2关闭
    */
    private Integer status;

    /**
    * 是否删除 0否 1是
    */
    private Integer isDelete;

    /**
    * 更新备注
    */
    private String updateRemark;
}