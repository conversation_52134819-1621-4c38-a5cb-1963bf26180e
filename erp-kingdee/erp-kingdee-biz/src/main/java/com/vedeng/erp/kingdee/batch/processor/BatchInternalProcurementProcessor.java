package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.BatchFlowOrderDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeInternalProcurementDto;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 内部采销
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class BatchInternalProcurementProcessor extends BaseProcessor<BatchFlowOrderDto, KingDeeInternalProcurementDto> {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;
    @Autowired
    private BatchFlowOrderDtoMapper batchFlowOrderDtoMapper;

    @Value("${kingdee.vedeng.customerId}")
    private String vedengCustomerId;
    @Value("${kingdee.vedeng.supplierId}")
    private String vedengSupplierId;


    @Override
    public KingDeeInternalProcurementDto doProcess(BatchFlowOrderDto batchFlowOrderDto, JobParameters params, ExecutionContext stepContext) throws Exception {
        // 使用常量替代硬编码值
        final String ORG_ID = "101";

        KingDeeInternalProcurementDto dto = new KingDeeInternalProcurementDto();
        dto.setF_QZOK_BDDJTID(batchFlowOrderDto.getFlowOrderId().toString());

        // 提前返回逻辑优化
        if (kingDeeBaseApi.isExist(dto)) {
            log.info("内部采销,数据已存在:{}", batchFlowOrderDto.getFlowOrderId());
            return null;
        }

        // 提取数据查询逻辑
        List<BatchFlowNodeDto> flowNodes = getFlowNodes(batchFlowOrderDto);
        List<BatchFlowOrderDetailDto> orderDetails = getOrderDetails(batchFlowOrderDto);

        batchFlowOrderDto.setFlowNodeDtoList(flowNodes);
        batchFlowOrderDto.setFlowOrderDetailList(orderDetails);

        // 填充价格信息
        populatePriceDetails(orderDetails);

        // 构建基础信息
        buildBaseInfo(batchFlowOrderDto, dto, ORG_ID);

        // 处理实体列表
        processEntities(batchFlowOrderDto, dto, flowNodes, orderDetails);

        // 处理组织关系
        processOrganizationRelations(batchFlowOrderDto, dto, flowNodes);

        return dto;
    }

    private List<BatchFlowNodeDto> getFlowNodes(BatchFlowOrderDto dto) {
        List<BatchFlowNodeDto> nodes = batchFlowOrderDtoMapper.findByFlowOrderIdGetFlowNodeDto(dto.getFlowOrderId());
        if (CollUtil.isEmpty(nodes)) {
            log.error("内部采销,未查询到节点信息:{}", dto.getFlowOrderId());
            throw new KingDeeException("节点信息不存在");
        }
        nodes.sort(Comparator.comparing(BatchFlowNodeDto::getNodeLevel));
        return nodes;
    }

    private List<BatchFlowOrderDetailDto> getOrderDetails(BatchFlowOrderDto dto) {
        List<BatchFlowOrderDetailDto> details = batchFlowOrderDtoMapper.findByFlowOrderIdGetFlowOrderDetailDto(dto.getFlowOrderId());
        if (CollUtil.isEmpty(details)) {
            log.error("内部采销,未查询到明细信息:{}", dto.getFlowOrderId());
            throw new KingDeeException("订单明细不存在");
        }
        return details;
    }

    private void populatePriceDetails(List<BatchFlowOrderDetailDto> details) {
        details.forEach(detail -> {
            List<BatchFlowNodeOrderDetailPriceDto> prices = batchFlowOrderDtoMapper
                    .findByFlowOrderIdGetFlowNodeOrderDetailPriceDto(detail.getFlowOrderDetailId());
            prices.sort(Comparator.comparing(BatchFlowNodeOrderDetailPriceDto::getNodeLevel));
            detail.setBatchFlowNodeOrderDetailPriceDtoList(prices);
        });
    }

    private void buildBaseInfo(BatchFlowOrderDto source, KingDeeInternalProcurementDto target, String orgId) {
        target.setF_QZOK_GSYWDH(source.getBaseOrderNo());
        target.setF_QZOK_OrgId(orgId);
        target.setF_QZOK_Date(DateUtil.format(source.getAddTime(), "yyyy-MM-dd HH:mm:ss"));
        target.setF_QZOK_YWLX(source.getBaseBusinessType() == 1 ? "采购" : "销售");
    }

    /**
     * 处理价格实体信息（包含采购和销售的金额、税率计算）
     *
     * @param order   订单主数据
     * @param dto     目标DTO
     * @param nodes   流程节点集合
     * @param details 订单明细集合
     */
    private void processEntities(BatchFlowOrderDto order, KingDeeInternalProcurementDto dto,
                                 List<BatchFlowNodeDto> nodes, List<BatchFlowOrderDetailDto> details) {
        // 构建节点快速查询映射表
        Map<Long, BatchFlowNodeDto> nodeMap = nodes.stream()
                .collect(Collectors.toMap(BatchFlowNodeDto::getFlowNodeId, Function.identity()));

        List<KingDeeInternalProcurementDto.F_QZOK_Entity> entities = new ArrayList<>();
        boolean skipLastNode;

        // 先判断末级节点是否需要跳过
        if (nodes.size() > 0) {
            BatchFlowNodeDto lastNode = nodes.get(nodes.size() - 1);
            BatchCompanyDataDto companyData;
            if (order.getBaseBusinessType() == 1) {
                // 采购业务：检查SUPPLIER_TRADER_ID
                companyData = batchFlowOrderDtoMapper.findSupplierTraderIdGetCompanyData(lastNode.getTraderId());
                skipLastNode = companyData == null || companyData.getSupplierTraderId() == null;
            } else {
                // 销售业务：检查CUSTOMER_TRADER_ID
                companyData = batchFlowOrderDtoMapper.findCustomerTraderIdGetCompanyData(lastNode.getTraderId());
                skipLastNode = companyData == null || companyData.getCustomerTraderId() == null;
            }
        } else {
            skipLastNode = false;
        }

        // 遍历所有订单明细
        details.forEach(detail -> {
            List<BatchFlowNodeOrderDetailPriceDto> prices = detail.getBatchFlowNodeOrderDetailPriceDtoList();

            // 逐级处理价格信息
            for (int i = 0; i < prices.size(); i++) {
                // 如果是末级节点且需要跳过，则不处理
                if (skipLastNode && i == prices.size() - 1) {
                    continue;
                }

                BatchFlowNodeOrderDetailPriceDto current = prices.get(i);
                BatchFlowNodeDto currentNode = nodeMap.get(current.getFlowNodeId());

                KingDeeInternalProcurementDto.F_QZOK_Entity entity = new KingDeeInternalProcurementDto.F_QZOK_Entity();
                // 设置基础信息
                entity.setF_QZOK_JDLZID(current.getNodeLevel().toString());  // 节点流转ID
                entity.setF_QZOK_BDLZID(current.getFlowNodeId().toString()); // 流程节点ID
                entity.setF_QZOK_SPDM(detail.getSkuNo());                   // 商品代码
                entity.setF_QZOK_SL(BigDecimal.valueOf(detail.getQuantity())); // 数量

                // 处理相邻节点价格关系（采购当前节点与下一节点，销售当前节点与上一节点）
                if (i < prices.size() - 1) {
                    BatchFlowNodeOrderDetailPriceDto next = prices.get(i + 1);
                    BatchFlowNodeDto nextNode = nodeMap.get(next.getFlowNodeId());
                    processPriceInfo(order.getBaseBusinessType(), entity, currentNode, nextNode, current, next);
                } else {
                    processPriceInfo(order.getBaseBusinessType(), entity, currentNode, new BatchFlowNodeDto(), current, new BatchFlowNodeOrderDetailPriceDto());
                }

                entities.add(entity);
            }
        });

        dto.setF_QZOK_Entity(entities);
    }


    /**
     * 处理价格计算逻辑（采购和销售不同计算方式）
     *
     * @param businessType 业务类型 1-采购 2-销售
     * @param entity       目标实体
     * @param current      当前节点数据
     * @param next         下一节点数据
     * @param currentPrice 当前价格明细
     * @param nextPrice    下一价格明细
     */
    private void processPriceInfo(Integer businessType, KingDeeInternalProcurementDto.F_QZOK_Entity entity,
                                  BatchFlowNodeDto current, BatchFlowNodeDto next,
                                  BatchFlowNodeOrderDetailPriceDto currentPrice,
                                  BatchFlowNodeOrderDetailPriceDto nextPrice) {
        // 采购业务处理逻辑
        if (businessType == 1) {
            // 如果不是最后一个节点，设置下一节点的价格信息
            if (next.getFlowNodeId() != null) {
                entity.setF_QZOK_CGDJ(nextPrice.getPrice());
                entity.setF_QZOK_CGSL(calculateRatio(next.getRatio()));
                entity.setF_QZOK_JSHJ(next.getAmount());
                entity.setF_QZOK_SE(calculateTaxAmount(next));
            }

            // 设置当前节点的价格信息
            entity.setF_QZOK_XSDJ(currentPrice.getPrice());
            entity.setF_QZOK_XSSL(calculateRatio(current.getRatio()));
            entity.setF_QZOK_XSJSHJ(current.getAmount());
            entity.setF_QZOK_XSSE(calculateTaxAmount(current));
        }
        // 销售业务处理逻辑
        else if (businessType == 2) {
            // 设置当前节点的价格信息
            entity.setF_QZOK_CGDJ(currentPrice.getPrice());
            entity.setF_QZOK_CGSL(calculateRatio(current.getRatio()));
            entity.setF_QZOK_JSHJ(current.getAmount());
            entity.setF_QZOK_SE(calculateTaxAmount(current));

            // 如果不是最后一个节点，设置下一节点的价格信息
            if (next.getFlowNodeId() != null) {
                entity.setF_QZOK_XSDJ(nextPrice.getPrice());
                entity.setF_QZOK_XSSL(calculateRatio(next.getRatio()));
                entity.setF_QZOK_XSJSHJ(next.getAmount());
                entity.setF_QZOK_XSSE(calculateTaxAmount(next));
            }
        }
    }

    private BigDecimal calculateRatio(BigDecimal ratio) {
        if (ratio == null || ratio.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return ratio.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
    }

    private BigDecimal calculateTaxAmount(BatchFlowNodeDto node) {
        if (node.getRatio() == null || node.getRatio().compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        if (node.getAmount() == null || node.getAmount().compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return node.getAmount().multiply(node.getRatio())
                .divide(BigDecimal.ONE.add(node.getRatio()), 2, RoundingMode.HALF_UP);
    }

    /**
     * 处理组织关系（供应商和客户信息）
     *
     * @param order 订单数据
     * @param dto   目标DTO
     * @param nodes 流程节点集合
     */
    private void processOrganizationRelations(BatchFlowOrderDto order, KingDeeInternalProcurementDto dto,
                                              List<BatchFlowNodeDto> nodes) {
        List<KingDeeInternalProcurementDto.FEntity> fEntities = new ArrayList<>();
        boolean skipLastNode = false;

        // 先判断末级节点是否需要跳过
        if (nodes.size() > 0) {
            BatchFlowNodeDto lastNode = nodes.get(nodes.size() - 1);
            BatchCompanyDataDto companyData;
            if (order.getBaseBusinessType() == 1) {
                // 采购业务：检查SUPPLIER_TRADER_ID
                companyData = batchFlowOrderDtoMapper.findSupplierTraderIdGetCompanyData(lastNode.getTraderId());
                skipLastNode = companyData == null || companyData.getSupplierTraderId() == null;
            } else {
                // 销售业务：检查CUSTOMER_TRADER_ID
                companyData = batchFlowOrderDtoMapper.findCustomerTraderIdGetCompanyData(lastNode.getTraderId());
                skipLastNode = companyData == null || companyData.getCustomerTraderId() == null;
            }
        }

        // 遍历所有节点构建组织关系
        for (int i = 0; i < nodes.size(); i++) {
            // 如果是末级节点且需要跳过，则不处理
            if (skipLastNode && i == nodes.size() - 1) {
                continue;
            }

            BatchFlowNodeDto current = nodes.get(i);
            KingDeeInternalProcurementDto.FEntity entity = new KingDeeInternalProcurementDto.FEntity();

            // 设置基础信息
            entity.setF_QZOK_YYLZID(current.getNodeLevel().toString());
            entity.setF_QZOK_BLZID(current.getFlowNodeId().toString());
            entity.setF_QZOK_SFKP(current.getOpenInvoice() == 1 ? 1 : 2);

            // 根据业务类型处理供应商/客户关系
            if (order.getBaseBusinessType() == 1) {
                BatchCompanyDataDto companyData = batchFlowOrderDtoMapper.findSupplierTraderIdGetCompanyData(current.getTraderId());
                if (companyData != null) {
                    entity.setF_QZOK_ZZ(companyData.getKingdeeAccountCode());
                }
                handleProcurementRelations(nodes, i, entity);
            } else {
                BatchCompanyDataDto companyData = batchFlowOrderDtoMapper.findCustomerTraderIdGetCompanyData(current.getTraderId());
                if (companyData != null) {
                    entity.setF_QZOK_ZZ(companyData.getKingdeeAccountCode());
                }
                handleSalesRelations(nodes, i, entity);
            }

            fEntities.add(entity);
        }

        dto.setFEntity(fEntities);
    }


    /**
     * 处理采购业务的组织关系
     * 业务规则：
     * - 首级节点客户固定为贝登
     * - 供应商取自下一级节点
     * - 中间节点客户取自上一级节点
     * - 最后一级节点的采购相关字段设置为-1
     */
    private void handleProcurementRelations(List<BatchFlowNodeDto> nodes, int index,
                                            KingDeeInternalProcurementDto.FEntity entity) {
        // 设置供应商（下一节点）
        if (index < nodes.size() - 1) {
            BatchFlowNodeDto next = nodes.get(index + 1);
            List<Integer> supplierTraderId = batchFlowOrderDtoMapper.findSupplierTraderId(next.getTraderId());
            if (CollUtil.isNotEmpty(supplierTraderId)) {
                entity.setF_QZOK_CGGYS(supplierTraderId.get(0).toString());
            }
        }

        // 设置客户（首节点为贝登，其他节点取自上一节点）
        if (index == 0) {
            // 贝登客户ID
            entity.setF_QZOK_XSKH(vedengCustomerId);
        } else {
            BatchFlowNodeDto prev = nodes.get(index - 1);
            BatchCompanyDataDto companyData = batchFlowOrderDtoMapper.findSupplierTraderIdGetCompanyData(prev.getTraderId());
            if (companyData != null) {
                entity.setF_QZOK_XSKH(companyData.getCustomerTraderId().toString());
            }
        }

        // 对于采购业务，最后一级节点的采购相关字段设置为-1
        if (index == nodes.size() - 1) {
            entity.setF_QZOK_CGDD(-1);
            entity.setF_QZOK_CGRK(-1);
            entity.setF_QZOK_CGFK(-1);
            entity.setF_QZOK_CGFP(-1);
        }
    }

    /**
     * 处理销售业务的组织关系
     * 业务规则：
     * - 首级节点供应商固定为贝登
     * - 客户取自下一级节点
     * - 中间节点供应商取自上一级节点
     * - 最后一级节点的销售相关字段设置为-1
     */
    private void handleSalesRelations(List<BatchFlowNodeDto> nodes, int index,
                                      KingDeeInternalProcurementDto.FEntity entity) {
        // 设置客户（下一节点）
        if (index < nodes.size() - 1) {
            BatchFlowNodeDto next = nodes.get(index + 1);
            List<Integer> customerTraderId = batchFlowOrderDtoMapper.findCustomerTraderId(next.getTraderId());
            if (CollUtil.isNotEmpty(customerTraderId)) {
                entity.setF_QZOK_XSKH(customerTraderId.get(0).toString());
            }
        }

        // 设置供应商（首节点为贝登，其他节点取自上一节点）
        if (index == 0) {
            entity.setF_QZOK_CGGYS(vedengSupplierId); // 贝登供应商ID
        } else {
            BatchFlowNodeDto prev = nodes.get(index - 1);
            BatchCompanyDataDto companyData = batchFlowOrderDtoMapper.findCustomerTraderIdGetCompanyData(prev.getTraderId());
            if (companyData != null) {
                entity.setF_QZOK_CGGYS(companyData.getSupplierTraderId().toString());
            }
        }

        // 对于销售业务，最后一级节点的销售相关字段设置为-1
        if (index == nodes.size() - 1) {
            entity.setF_QZOK_XSDD(-1);
            entity.setF_QZOK_XSCK(-1);
            entity.setF_QZOK_XSSK(-1);
            entity.setF_QZOK_XSFP(-1);
        }
    }
}
