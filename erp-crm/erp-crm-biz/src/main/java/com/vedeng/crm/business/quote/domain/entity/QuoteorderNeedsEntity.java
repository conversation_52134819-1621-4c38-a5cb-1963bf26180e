package com.vedeng.crm.business.quote.domain.entity;

import java.util.Date;
import lombok.Data;

/**
    * 报价需求表
    */
@Data
public class QuoteorderNeedsEntity {
    /**
    * 主键
    */
    private Long quoteorderNeedsId;

    /**
    * 报价单ID
    */
    private Integer quoteorderId;

    /**
    * 产品需求
    */
    private String productNeeds;

    /**
    * 数量需求
    */
    private String numNeeds;

    /**
    * 预算需求
    */
    private String budgetaryNeeds;
    /**
     * 经销预算(元)
     */
    private String distributeBudget;

    /**
     * 终端预算(元)
     */
    private String terminalBudget;

    /**
    * 其他需求
    */
    private String extraNeeds;

    /**
     * 负责人ID
     */
    private Integer headUserId;

    /**
    * 是否删除 0否 1是
    */
    private Integer isDelete;

    /**
    * 创建时间
    */
    private Date addTime;

    /**
    * 修改时间
    */
    private Date modTime;

    /**
    * 添加人ID
    */
    private Integer creator;

    /**
    * 添加人名称
    */
    private String creatorName;

    /**
    * 更新人ID
    */
    private Integer updater;

    /**
    * 更新人名称
    */
    private String updaterName;

    /**
    * 更新备注
    */
    private String updateRemark;
}
