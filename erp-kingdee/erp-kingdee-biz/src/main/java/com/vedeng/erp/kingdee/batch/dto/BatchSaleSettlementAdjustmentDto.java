package com.vedeng.erp.kingdee.batch.dto;

import com.vedeng.erp.kingdee.batch.common.bean.BatchBaseDto;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 销售售后调整单
 * <AUTHOR>
 */
@Getter
@Setter
public class BatchSaleSettlementAdjustmentDto extends BatchBaseDto {
    /**
     * 销售售后调整单id
     */
    private Integer saleSettlementAdjustmentId;

    /**
     * 订单id
     */
    private Integer saleorderId;

    /**
     * 订单编号
     */
    private String saleorderNo;

    /**
     * 售后单id
     */
    private Integer afterSaleId;

    /**
     * 售后单编号
     */
    private String afterSaleNo;

    /**
     * 应收总价
     */
    private BigDecimal totalAmount;

    /**
     * 实收总价
     */
    private BigDecimal receivedAmount;

    /**
     * 尾差金额
     */
    private BigDecimal differenceAmount;

    /**
     * 调整单类型 1=销售单调整单 2=售后单调整单 在一个销售订单生命周期中 仅会出现一个销售单调整单，会出现多个售后调整单
     */
    private Integer adjustmentType;

    /**
     * 最大时间
     */
    private Date beginTime;

    /**
     * 最小时间
     */
    private Date endTime;

    /**
     * 是否推送过金蝶
     */
    private Boolean isPushed;
}