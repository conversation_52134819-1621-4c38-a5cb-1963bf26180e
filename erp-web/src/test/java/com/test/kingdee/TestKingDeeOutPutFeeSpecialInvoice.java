package com.test.kingdee;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.domain.command.KingDeeOutPutFeeSpecialInvoiceCommand;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeOutPutFeeSpecialInvoiceMapper;
import com.vedeng.erp.kingdee.mapstruct.*;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.RepoStatus;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


/**
 * 金蝶  销项费用专用发票
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring.xml", "classpath:spring-mybatis.xml"})
public class TestKingDeeOutPutFeeSpecialInvoice {

    @Autowired
    private KingDeeOutPutFeeSpecialInvoiceCommandConvertor specialInvoiceCommandConvertor;

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private KingDeeOutPutFeeSpecialInvoiceMapper specialInvoiceMapper;

    @Autowired
    private KingDeeOutPutFeeSpecialInvoiceConvertor specialInvoiceConvertor;

    @Test
    public void kingDeeSaveMaterial() {

        OutPutFeeSpecialInvoiceDto dto = new OutPutFeeSpecialInvoiceDto();
        dto.setFid("0");
        dto.setFQzokBddjtid("A12345");
        //
        dto.setFinvoiceno("B12345");
        dto.setFQzokFpdm("C12345");
        dto.setFinvoicedate("2022-10-01 00:00:00");
        dto.setFdate("2022-10-01 00:00:00");
        dto.setFcontactunittype("BD_Customer");
        dto.setFcontactunit("10022");

        dto.setFsaleorgid("101");
        dto.setFdocumentstatus("Z");
        dto.setFRedBlue("0");

        List<OutPutFeeSpecialInvoiceDetailDto> FSALEEXINVENTRYList = new ArrayList<>();
        OutPutFeeSpecialInvoiceDetailDto detail = new OutPutFeeSpecialInvoiceDetailDto();

        detail.setFexpenseid("008");
        detail.setFauxtaxprice(new BigDecimal(100));
        detail.setFpriceqty("10");
        detail.setFtaxrate("13");
        detail.setFQzokBddjhid("200");
        detail.setFQzokYsddh("BOF123");
        detail.setFQzokGsywdh("BOF999");
        detail.setFQzokYwlx("ADD_ORDER");
        detail.setFsrcbilltypeid("AR_receivable");

        List<OutPutFeeSpecialInvoiceDetailLinkDto> FSALEEXINVENTRY_LINK = new ArrayList<>();
        OutPutFeeSpecialInvoiceDetailLinkDto detailLink = new OutPutFeeSpecialInvoiceDetailLinkDto();
        detailLink.setFLinkId("0");
        detailLink.setFsaleexinventryLinkFflowid("");
        detailLink.setFsaleexinventryLinkFflowlineid("0");
        detailLink.setFsaleexinventryLinkFruleid("IV_ReceivableToSaleExInv_Entry");
        detailLink.setFsaleexinventryLinkFstableid("0");
        detailLink.setFsaleexinventryLinkFstablename("t_AR_receivableEntry");
        detailLink.setFsaleexinventryLinkFsbillid("102222");
        detailLink.setFsaleexinventryLinkFsid("122333");
        detailLink.setFsaleexinventryLinkFallamountforold(new BigDecimal(200));
        detailLink.setFsaleexinventryLinkFallamountfor(new BigDecimal(500));
        FSALEEXINVENTRY_LINK.add(detailLink);

        detail.setFSALEEXINVENTRY_LINK(FSALEEXINVENTRY_LINK);
        FSALEEXINVENTRYList.add(detail);

        dto.setFSALEEXINVENTRY(FSALEEXINVENTRYList);

        System.out.println("dto-->"+JSON.toJSON(dto));
        //转换为金蝶入参
        KingDeeOutPutFeeSpecialInvoiceCommand command = specialInvoiceCommandConvertor.toCommand(dto);
        System.out.println("入参commad-->"+JSON.toJSON(command));
        //调用金蝶方法
        RepoStatus save = kingDeeBaseApi.save(new SaveExtCommand<>(command, dto.getFormId()));
        ArrayList<SuccessEntity> successEntities = save.getSuccessEntitys();

        System.out.println("金蝶返回-->"+JSON.toJSON(successEntities));

        if (CollUtil.isNotEmpty(successEntities)) {
            SuccessEntity successEntity = CollUtil.getFirst(successEntities);
            dto.setFid(successEntity.getId());
            //金蝶业务数据入表
            specialInvoiceMapper.insertSelective(specialInvoiceConvertor.toEntity(dto));
        System.out.println("finish-->");
        }
    }

   //调用记录 -1 失败
    //[{"parameters":["IV_SALEEXINV","{\"Creator\":0,\"NeedReturnFields\":[],\"NeedUpDateFields\":[],\"isAutoSubmitAndAudit\":true,\"isAutoAdjustField\":true,\"Model\":{\"FID\":\"0\",\"F_QZOK_BDDJTID\":\"A12345\",\"FINVOICENO\":\"B12345\",\"F_QZOK_FPDM\":\"C12345\",\"FINVOICEDATE\":\"2022-10-01 00:00:00\",\"FDATE\":\"2022-10-01 00:00:00\",\"FCONTACTUNITTYPE\":\"BD_Customer\",\"FCONTACTUNIT\":{\"FNumber\":\"10022\"},\"FSALEORGID\":{\"FNumber\":\"101\"},\"FDOCUMENTSTATUS\":\"Z\",\"FRedBlue\":\"0\",\"FSALEEXINVENTRY\":[{\"FEXPENSEID\":{\"FNumber\":\"008\"},\"F_QZOK_BDDJHID\":\"200\",\"F_QZOK_YSDDH\":\"BOF123\",\"F_QZOK_GSYWDH\":\"BOF999\",\"F_QZOK_YWLX\":\"ADD_ORDER\",\"FSALEEXINVENTRY_LINK\":[{\"FLinkId\":\"0\",\"FSALEEXINVENTRY_Link_FFlowId\":\"\",\"FSALEEXINVENTRY_Link_FFlowLineId\":\"0\",\"FSALEEXINVENTRY_Link_FRuleId\":\"IV_ReceivableToSaleExInv_Entry\",\"FSALEEXINVENTRY_Link_FSTableId\":\"0\",\"FSALEEXINVENTRY_Link_FSTableName\":\"t_AR_receivableEntry\",\"FSALEEXINVENTRY_Link_FSBillId\":\"102222\",\"FSALEEXINVENTRY_Link_FSId\":\"122333\",\"FSALEEXINVENTRY_Link_FALLAMOUNTFOROLD\":200,\"FSALEEXINVENTRY_Link_FALLAMOUNTFOR\":500}]}]},\"formId\":\"IV_SALEEXINV\"}"]}]

    //结果  FCONTACTUNIT 选择的是 客户编码 10022
    //调用金蝶接口，返回值[{"Result":{"ResponseStatus":{"ErrorCode":500,"IsSuccess":false,"Errors":[{"FieldName":"FCONTACTUNIT","Message":"字段“往来单位”是必填项","DIndex":0},{"FieldName":"","Message":"蓝字发票价税合计不能小于等于0，红字发票价税合计不能大于等于0！","DIndex":0},{"FieldName":"","Message":"第1行分录，费用发票分录价税合计不允许为0","DIndex":0}],"SuccessEntitys":[],"SuccessMessages":[],"MsgCode":11},"Id":"","Number":"","NeedReturnData":[{}]}}]
    //金蝶返回-->[]


    //调用记录 -2 成功
    //{"fDOCUMENTSTATUS":"Z","fID":"0","f_QZOK_FPDM":"C12345","f_QZOK_BDDJTID":"A12345","fCONTACTUNIT":{"fNumber":"10022"},"fINVOICEDATE":"2022-10-01 00:00:00","fRedBlue":"0","fINVOICENO":"B12345","fSALEORGID":{"fNumber":"101"},"fSALEEXINVENTRY":[{"fPRICEQTY":"10","fSALEEXINVENTRY_LINK":[{"fSALEEXINVENTRY_Link_FFlowLineId":"0","fSALEEXINVENTRY_Link_FSBillId":"102222","fSALEEXINVENTRY_Link_FSTableId":"0","fSALEEXINVENTRY_Link_FSId":"122333","fSALEEXINVENTRY_Link_FALLAMOUNTFOR":500,"fSALEEXINVENTRY_Link_FALLAMOUNTFOROLD":200,"fSALEEXINVENTRY_Link_FSTableName":"t_AR_receivableEntry","fSALEEXINVENTRY_Link_FRuleId":"IV_ReceivableToSaleExInv_Entry","fLinkId":"0","fSALEEXINVENTRY_Link_FFlowId":""}],"fEXPENSEID":{"fNumber":"008"},"f_QZOK_GSYWDH":"BOF999","f_QZOK_YSDDH":"BOF123","f_QZOK_YWLX":"ADD_ORDER","fTAXRATE":"13","fAUXTAXPRICE":100,"f_QZOK_BDDJHID":"200"}],"fDATE":"2022-10-01 00:00:00","fCONTACTUNITTYPE":"BD_Customer"}

    //调用金蝶接口，返回值[{"Result":{"ResponseStatus":{"IsSuccess":true,"Errors":[],"SuccessEntitys":[{"Id":100071,"Number":"SEXVATIN00000003","DIndex":0}],"SuccessMessages":[],"MsgCode":0},"Id":100071,"Number":"SEXVATIN00000003","NeedReturnData":[{}]}}]
    //金蝶返回-->[{"number":"SEXVATIN00000003","dIndex":0,"id":"100071"}]
}
