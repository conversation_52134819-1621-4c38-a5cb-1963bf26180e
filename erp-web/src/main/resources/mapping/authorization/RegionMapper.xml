<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.authorization.dao.RegionMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.authorization.model.Region" >
    <id column="REGION_ID" property="regionId" jdbcType="INTEGER" />
    <result column="PARENT_ID" property="parentId" jdbcType="INTEGER" />
    <result column="REGION_NAME" property="regionName" jdbcType="VARCHAR" />
    <result column="REGION_TYPE" property="regionType" jdbcType="INTEGER" />
    <result column="AGENCY_ID" property="agencyId" jdbcType="INTEGER" />
	<result column="REGION_FULL_NAME" property="regionFullName" jdbcType="VARCHAR" />
	<result column="REGION_CODE" property="regionCode" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    REGION_ID, PARENT_ID, REGION_NAME, REGION_TYPE, AGENCY_ID,REGION_FULL_NAME,REGION_CODE
  </sql>
  
  <!-- getRegionByParentId start -->
  <select id="getRegionByParentId" resultMap="BaseResultMap" parameterType="java.lang.Integer">
	  select
	  	<include refid="Base_Column_List" />
	  from T_REGION
	  where PARENT_ID=#{parentId,jdbcType=INTEGER}
	  AND IS_DELETED = 0
	  order by REGION_ID
  </select>
  
  <!-- getRegionById -->
  <select id="getRegionById" resultMap="BaseResultMap" parameterType="java.lang.Integer">
  select
  <include refid="Base_Column_List" />
  from T_REGION
  where REGION_ID=#{regionId,jdbcType=INTEGER}
  </select>
  
  <select id="getRegion" resultMap="BaseResultMap" parameterType="com.vedeng.authorization.model.Region">
  	select
  <include refid="Base_Column_List" />
  from T_REGION
  where 
  	REGION_NAME = #{regionName,jdbcType=VARCHAR}
  	and
  	PARENT_ID = #{parentId,jdbcType=INTEGER}
	and region_id &gt; 100000
  </select>
  
  <select id="getRegionListPage" resultMap="BaseResultMap" parameterType="Map">
	  select
	  <include refid="Base_Column_List" />
	  from T_REGION
	  where PARENT_ID = 1
	  <if test="region.regionName != null and region.regionName != ''">
	  	and REGION_NAME like CONCAT('%',#{region.regionName},'%' )
	  </if>
	  order by REGION_ID
  </select>
  
  <insert id="insert" parameterType="com.vedeng.authorization.model.Region" useGeneratedKeys="true" keyProperty="regionId">
		insert into T_REGION
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="regionId != null">
				REGION_ID,
			</if>
			<if test="parentId != null">
				PARENT_ID,
			</if>
			<if test="regionName != null">
				REGION_NAME,
			</if>
			<if test="regionType != null">
				REGION_TYPE,
			</if>
			<if test="agencyId != null">
				AGENCY_ID,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="regionId != null">
				#{regionId,jdbcType=INTEGER},
			</if>
			<if test="parentId != null">
				#{parentId,jdbcType=INTEGER},
			</if>
			<if test="regionName != null">
				#{regionName,jdbcType=INTEGER},
			</if>
			<if test="regionType != null">
				#{regionType,jdbcType=INTEGER},
			</if>
			<if test="agencyId != null">
				#{agencyId,jdbcType=INTEGER},
			</if>
		</trim>
	</insert>
	<!-- 修改 -->
	<update id="update" parameterType="com.vedeng.authorization.model.Region">
		update T_REGION
		<set>
			<if test="parentId != null">
				PARENT_ID = #{parentId,jdbcType=INTEGER},
			</if>
			<if test="regionName != null">
				REGION_NAME = #{regionName,jdbcType=INTEGER},
			</if>
			<if test="regionType != null">
				REGION_TYPE = #{regionType,jdbcType=INTEGER},
			</if>
			<if test="agencyId != null">
				AGENCY_ID = #{agencyId,jdbcType=INTEGER},
			</if>
		</set>
		where REGION_ID = #{regionId,jdbcType=INTEGER}
	</update>
	
	<select id="getRegionByParam" resultMap="BaseResultMap" parameterType="com.vedeng.authorization.model.Region">
	  select
	  	<include refid="Base_Column_List" />
	  from T_REGION
	  where PARENT_ID=#{parentId,jdbcType=INTEGER}
	  		and REGION_TYPE = #{regionType,jdbcType=INTEGER}
	  		and REGION_NAME like CONCAT('%',#{regionName},'%' )
  	</select>
  	
  	<select id="getRegionByArea" parameterType="com.vedeng.authorization.model.vo.RegionVo" resultType="com.vedeng.authorization.model.vo.RegionVo">
  		SELECT
			a.REGION_ID AS province,
			a.REGION_NAME AS provinceName
			<if test="null != cityName and cityName != '' ">
				,b.REGION_ID AS city,
				b.REGION_NAME AS cityName
				<if test="null != zone and zone != '' ">
					,c.REGION_ID AS zoneId,
					c.REGION_NAME AS zone
				</if>
			</if>
		FROM
			T_REGION a
			<if test="null != cityName and cityName != '' ">
				LEFT JOIN T_REGION b ON a.REGION_ID = b.PARENT_ID
				<if test="null != zone and zone != '' ">
					LEFT JOIN T_REGION c ON b.REGION_ID = c.PARENT_ID
				</if>
			</if>
		WHERE
			a.REGION_NAME = #{provinceName,jdbcType=VARCHAR}
			<if test="null != cityName and cityName != '' ">
				AND b.REGION_NAME = #{cityName,jdbcType=VARCHAR}
			</if>
			<if test="null != zone and zone != '' ">
				AND c.REGION_NAME = #{zone,jdbcType=VARCHAR}
			</if>
			limit 1
  	</select>
  	
  	<select id="getRegionIdStringByMinRegionId" parameterType="java.lang.Integer" resultType="java.lang.String">
  		SELECT
			CONCAT(IFNULL(c.REGION_ID, ""), ",", IFNULL(b.REGION_ID, ""), ",", IFNULL(a.REGION_ID, "")) as REGION_ID_STR
  		FROM T_REGION a
  		LEFT JOIN T_REGION b
  			ON b.REGION_ID = a.PARENT_ID
  		LEFT JOIN T_REGION c
  			ON c.REGION_ID = b.PARENT_ID
  		WHERE
  			a.REGION_ID = #{minRegionId,jdbcType=INTEGER}
  		LIMIT 1
  	</select>


	<select id="getRegionNameStringByMinRegionId" parameterType="java.lang.Integer" resultType="java.lang.String">
		SELECT
			CONCAT(IFNULL(c.REGION_NAME, ""), " ", IFNULL(b.REGION_NAME, ""), " ", IFNULL(a.REGION_NAME, "")) as REGION_NAME_STR
		FROM T_REGION a
				 INNER JOIN T_REGION b
						   ON b.REGION_ID = a.PARENT_ID
				 INNER JOIN T_REGION c
						   ON c.REGION_ID = b.PARENT_ID
		WHERE
			a.REGION_ID = #{minRegionId,jdbcType=INTEGER}
		LIMIT 1
	</select>

	<select id="getCityList" parameterType="com.vedeng.authorization.model.vo.RegionVo" resultType="com.vedeng.authorization.model.vo.RegionVo">
		 SELECT
		  b.REGION_ID,
		  b.PARENT_ID,
		  b.REGION_NAME,
		  b.REGION_TYPE,
		  b.AGENCY_ID
		FROM
		  T_REGION a
		  LEFT JOIN T_REGION b
			ON  b.PARENT_ID = a.REGION_ID
		WHERE b.PARENT_ID
		<if test="cityStart == 2 ">
			BETWEEN #{cityStart,jdbcType=INTEGER} AND #{cityEnd,jdbcType=INTEGER}
		</if>
		<if test="cityStart == 0">
		    NOT	BETWEEN  #{cityStart,jdbcType=INTEGER} AND #{cityEnd,jdbcType=INTEGER}
		</if>
  	</select>
	<select id="getRegionFullNameById" resultType="com.vedeng.authorization.model.Region">
	SELECT
	A.REGION_ID,
	A.PARENT_ID,
	A.REGION_CODE,
	A.REGION_TYPE,
	IF
	(
		A.REGION_FULL_NAME != '',
		A.REGION_FULL_NAME,
		A.REGION_NAME
	) AS 'REGION_NAME'
	FROM
	T_REGION A
	WHERE
	REGION_ID = #{areaId,jdbcType=INTEGER}
	</select>

	<select id="getRegionByRegionNameAndType" resultType="com.vedeng.authorization.model.Region">
		SELECT
			*
		FROM
			`T_REGION`
		WHERE
			REGION_NAME = #{regionName,jdbcType=VARCHAR}
			AND REGION_TYPE = #{regionType,jdbcType=INTEGER}
			AND REGION_ID > 100000
		LIMIT 1
	</select>

	<select id="getRegionIdStringByMinRegionName" parameterType="java.lang.String" resultType="java.lang.String">
  		SELECT
			CONCAT(IFNULL(c.REGION_ID, ""), ",", IFNULL(b.REGION_ID, ""), ",", IFNULL(a.REGION_ID, "")) as REGION_ID_STR
  		FROM T_REGION a
  		LEFT JOIN T_REGION b
  			ON b.REGION_ID = a.PARENT_ID
  		LEFT JOIN T_REGION c
  			ON c.REGION_ID = b.PARENT_ID
  		WHERE
  			a.REGION_FULL_NAME = #{regionName,jdbcType=VARCHAR}
  	</select>

	<select id="getRegionByReginFullName" resultMap="BaseResultMap">
		select
			<include refid="Base_Column_List" />
		from T_REGION
		where
		REGION_ID >= 100000
		and REGION_FULL_NAME = #{regionName,jdbcType=VARCHAR}
		and REGION_TYPE = #{regionType,jdbcType=INTEGER}
		<if test="parentId != null">
			and PARENT_ID = #{parentId,jdbcType=INTEGER}
		</if>
	</select>

	<select id="getRegionCityAll" resultType="com.vedeng.authorization.model.Region">
		select
		<include refid="Base_Column_List" />
		from T_REGION
		where
		REGION_ID >= 100000 AND
		REGION_TYPE=2 AND
		IS_DELETED = 0

	</select>

	<select id="getRegionByNameParentName" resultType="com.vedeng.authorization.model.Region">
		SELECT
			REGION_ID,
			REGION_NAME,
			REGION_TYPE
		FROM
			T_REGION
		WHERE
			REGION_NAME = #{name,jdbcType=VARCHAR}
			AND PARENT_ID = ( SELECT REGION_ID FROM T_REGION WHERE REGION_NAME = #{parentName,jdbcType=VARCHAR} AND REGION_TYPE = 1)
	</select>
	<select id="selectTypeEqThreeRegionIdListByRegionIdList" resultType="java.lang.Integer">
		select distinct c.REGION_ID
		from T_REGION a
				 left join T_REGION b on a.REGION_ID = b.PARENT_ID
				 left join T_REGION c on b.REGION_ID = c.PARENT_ID
		where a.REGION_ID in (select a.REGION_ID
							  from T_REGION a
									   left join T_REGION b on a.REGION_ID = b.PARENT_ID
									   left join T_REGION c on b.REGION_ID = c.PARENT_ID
							  where
								c.REGION_TYPE = 3
							  	and c.REGION_ID in
								  <foreach collection="regionIdList" item="regionId" index="index" open="(" close=")" separator=",">
									  #{regionId,jdbcType=INTEGER}
								  </foreach> )
		and a.REGION_TYPE = 1
	</select>

</mapper>