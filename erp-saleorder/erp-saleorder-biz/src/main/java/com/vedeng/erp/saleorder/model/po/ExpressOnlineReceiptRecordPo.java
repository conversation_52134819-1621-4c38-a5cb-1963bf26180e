package com.vedeng.erp.saleorder.model.po;

import lombok.Data;

/**
 * 快递在线签收PO
 *
 * <AUTHOR>
 */
@Data
public class ExpressOnlineReceiptRecordPo {

    /**
     * 主键ID
     */
    private Integer expressOnlineReceiptRecordId;

    /**
     * 订单ID
     */
    private Integer orderId;

    /**
     * 交易者名称
     */
    private String traderName;

    /**
     * 客户ID
     */
    private Integer traderId;

    /**
     * 注册手机号
     */
    private String mobile;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 备注
     */
    private String comments;

    /**
     * 是否有效  0:无效  1:有效
     */
    private Integer isEnable;

    /**
     * 签收时间
     */
    private Long signTime;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 最近一次编辑时间
     */
    private Integer modTime;

    /**
     * 最近一次编辑人
     */
    private Integer updater;
}
