package com.test.kingdee;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.domain.command.KingDeeInventoryProfitCommand;
import com.vedeng.erp.kingdee.dto.KingDeeInventoryProfitDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeInventoryProfitDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeInventoryProfitMapper;
import com.vedeng.erp.kingdee.mapstruct.KingDeeInventoryProfitCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeInventoryProfitConvertor;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.RepoStatus;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 盘盈单
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring.xml", "classpath:spring-mybatis.xml"})
public class TestKingDeeInventoryProfit {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Resource
    private KingDeeInventoryProfitCommandConvertor commandConvertor;

    @Resource
    private KingDeeInventoryProfitConvertor inventoryProfitConvertor;

    @Autowired
    private KingDeeInventoryProfitMapper kingDeeInventoryProfitMapper;


    @Test
    public void kingDeePurchaseReceipt() {
    	KingDeeInventoryProfitDto dto = new KingDeeInventoryProfitDto();
        dto.setFId("0");
        dto.setFBillTypeId("PY01_SYS");
        dto.setFBillNo("PYD0001");
        dto.setFQzokBddjtId("贝登erp对应的单据头ID");
        dto.setFStockOrgId("101");
        dto.setFOwnerTypeIdHead("BD_OwnerOrg");
        dto.setFDate("2022-09-15");
        dto.setFDeptId("BM9999");

        List<KingDeeInventoryProfitDetailDto>  FEntityDetail = new ArrayList<>();
        KingDeeInventoryProfitDetailDto detailDto = new KingDeeInventoryProfitDetailDto();
        detailDto.setFMaterialId("sku0001");
        detailDto.setFStockId("CK9999");
        detailDto.setFStockStatusId("KCZT01_SYS");
        detailDto.setFBaseGainQty("2.0");
        detailDto.setFPrice("5.0");
        detailDto.setFAmount("10.0");
        detailDto.setFQzokYsddh("原始订单号");
        detailDto.setFQzokGsywdh("归属业务单号");
        detailDto.setFQzokYwlx("业务类型");
        detailDto.setFQzokPch("批次号");
        detailDto.setFQzokXlh("序列号");
        FEntityDetail.add(detailDto);
        dto.setFBillEntry(FEntityDetail);

        KingDeeInventoryProfitCommand command = commandConvertor.toCommand(dto);


        System.out.println("入参："+JSON.toJSONString(command));
        RepoStatus save = kingDeeBaseApi.save(new SaveExtCommand<>(command, dto.getFormId()));
        ArrayList<SuccessEntity> successEntities = save.getSuccessEntitys();
//        System.out.println("金蝶返回："+JSON.toJSONString(successEntities));
        if (CollUtil.isNotEmpty(successEntities)) {
            SuccessEntity successEntity = CollUtil.getFirst(successEntities);
            System.out.println("数据库入参："+JSON.toJSONString(inventoryProfitConvertor.toEntity(dto)));
            kingDeeInventoryProfitMapper.insertSelective(inventoryProfitConvertor.toEntity(dto));
        }
    }
}
