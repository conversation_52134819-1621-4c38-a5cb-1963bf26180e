package com.vedeng.erp.aftersale.service;

import com.vedeng.erp.aftersale.domain.entity.AfterSalesBillToYxbEntity;

/**
 * 工单下派医修帮流水对接
 */
public interface AfterSalesBillToYxbApiService {
    /**
     * 用于给售后单推送医修帮付款记录，
     * 根据CAPITAL_BILL_ID和AFTER_SALES_ORDER_ID去查询推送成功的数据，
     * 判断是该售后单的这条记录是否推送过
     */
    Integer countAfterSalesBillByAfterSalesIdAndCapitalBillId(Integer afterSalesId, Integer capitalBillId);

    /**
     * 用于插入售后单推送付款交易记录给医修帮的结果
     */
    Integer insertAfterSalesBillToYxb(AfterSalesBillToYxbEntity afterSalesBillToYxbEntity);
}
