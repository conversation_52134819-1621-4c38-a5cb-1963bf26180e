$(function(){
});
// 验证筛选查询
function search() {
    checkLogin();


    var begin = $("#beginValue").val();
    var end =  $("#endValue").val();
    if(eval(begin) > eval(end)){
        layer.alert("筛选项沟通时长开始不允许大于结束");
        return false;
    }
    if($("#communicateRecordId").val().length>10){
        layer.alert("录音ID过长");
        return;
    }
    $("#searchSpan").css("background","#ccc").css("border-color","#ccc").html("查询中").removeAttr('onclick');
    //$("#searchSpan").hide();
    //$(".tcenter").prepend("<span class='bg-light-blue bt-bg-style bt-small' >搜索</span>")
    $("#search").submit();
}

