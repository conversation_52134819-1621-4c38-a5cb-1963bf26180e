package com.vedeng.erp.trader.web.api;

import com.vedeng.common.annotation.MethodLock;
import com.vedeng.common.annotation.MethodLockParam;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.trader.dto.TraderCustomerTerminalDto;
import com.vedeng.erp.trader.dto.TraderCustomerTerminalQuery;
import com.vedeng.erp.trader.service.TraderCustomerTerminalApiService;
import com.vedeng.erp.trader.service.TraderCustomerTerminalAuditRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/9/4 19:13
 **/
@ExceptionController
@RestController
@RequestMapping("/traderCustomerTerminalApi")
@Slf4j
public class TraderCustomerTerminalApi {

    @Autowired
    private TraderCustomerTerminalApiService traderCustomerTerminalApiService;

    @Autowired
    private TraderCustomerTerminalAuditRecordService traderCustomerTerminalAuditRecordService;


    @RequestMapping(value = "/add")
    @NoNeedAccessAuthorization
    public R<?> queryByTraderCustomerId(@RequestBody List<TraderCustomerTerminalDto> traderCustomerTerminalDtoList) {

        try {
            traderCustomerTerminalApiService.addByList(traderCustomerTerminalDtoList);
        } catch (ServiceException e) {
            return R.error(e.getMessage());
        }
        return R.success();
    }

    /**
     * 分页查询接口
     * @param query 查询对象
     * @return R
     */
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> page(@RequestBody PageParam<TraderCustomerTerminalQuery> query) {
        return R.success(traderCustomerTerminalApiService.page(query));
    }


    @RequestMapping(value = "/detail")
    @NoNeedAccessAuthorization
    public R<?> detail(Integer traderId) {

        return R.success(traderCustomerTerminalApiService.getAuditData(traderId));
    }


    @RequestMapping(value = "/getData")
    @NoNeedAccessAuthorization
    public R<?> getData(Integer traderCustomerTerminalId) {
        return R.success(traderCustomerTerminalApiService.getData(traderCustomerTerminalId));
    }

    @RequestMapping(value = "/getAuditRecordList")
    @NoNeedAccessAuthorization
    public R<?> getAuditRecordList(Integer traderCustomerTerminalId) {
        return R.success(traderCustomerTerminalAuditRecordService.getAuditRecordList(traderCustomerTerminalId));
    }

    @RequestMapping(value = "/update")
    @NoNeedAccessAuthorization
    @MethodLock(field = "traderCustomerTerminalId",className = TraderCustomerTerminalDto.class,time = 60000)
    public R<?> update(TraderCustomerTerminalDto traderCustomerTerminalDto) {
        try {
            traderCustomerTerminalApiService.update(traderCustomerTerminalDto);
        } catch (Exception e) {
            log.error("更新终端链路信息异常",e);
            return R.error(e.getMessage());
        }
        return R.success();
    }


    @RequestMapping(value = "/pass")
    @NoNeedAccessAuthorization
    @MethodLock(className = Integer.class,time = 120000)
    public R<?> pass(@MethodLockParam Integer traderCustomerTerminalId) {

        try {
            traderCustomerTerminalApiService.doPass(traderCustomerTerminalId);
        } catch (Exception e) {
            log.error("审核通过终端链路信息异常",e);
            return R.error(e.getMessage());
        }
        return R.success();
    }

    @RequestMapping(value = "/reject")
    @NoNeedAccessAuthorization
    @MethodLock(className = Integer.class,time = 60000)
    public R<?> reject(@MethodLockParam Integer traderCustomerTerminalId, String desc) {

        try {
            traderCustomerTerminalApiService.doReject(traderCustomerTerminalId, desc);
        } catch (Exception e) {
            log.error("驳回终端链路信息异常",e);
            return R.error(e.getMessage());
        }
        return R.success();
    }



}
