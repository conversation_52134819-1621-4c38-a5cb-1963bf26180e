<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>库存转换列表</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="wmsUnitConversionList" datasource="erp-datasourcetarget" empty_show="" fixednumber="0" firstcol="numbers">
<div id="appendHead">
</div>

<div class="layui-fluid">
    <form class="layui-form" id="search">
        <div class="layui-inline ">
            <label>库存转换单号</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder="请输入" oper="like"
                       name="WMS_UNIT_CONVERSION_ORDER_NO" alias="TWUCO" empty_show="-">
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">审核状态</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="VERIFY_STATUS" alias="TWUCO" jdbctype="INTEGER" placeholder="默认全部"
                        data='[{"K":"1","V":"审核中"},{"K":"2","V":"审核通过"},{"K":"3","V":"审核不通过"}]'
                        datatype="json"></select>
            </div>
        </div>

        <div class="layui-inline ">
            <label>创建日期</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" jdbctype="VARCHER" type="search"
                        placeholder="请输入" name="ADD_TIME" alias="TWUCO" oper="BETWEEN"></object>
            </div>
        </div>

        <div class="layui-inline ">
            <label>需转换订货号</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder="请输入" oper="like"
                       name="SOURCE_SKU_NO" alias="TWUCOI" empty_show="-">
            </div>
        </div>
        <div class="layui-inline ">
            <label>目标订货号</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder="请输入" oper="like"
                       name="TARGET_SKU_NO" alias="TWUCOI" empty_show="-">
            </div>
        </div>



        <div class=" layui-inline ">
            <label class="layui-form-label">申请类型</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="ORDER_TYPE"  alias="TWUCO" jdbctype="INTEGER" placeholder="默认全部"
                        data='[{"K":"1","V":"单位转换"},{"K":"2","V":"主机配件"},{"K":"3","V":"组合产品"}]'
                        datatype="json"></select>
            </div>
        </div>





    </form>
    <hr class="layui-border-blue">

    <div class="btn-group bd-highlight" id="tableButton">
        <button type="table" class="layui-btn" url="/wmsUnitConversion/edit.do" opentype="PARENT"  windowname="新增库存转换">+
            新增库存转换
        </button>
    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>

        <tr id="column">
            <th name="WMS_UNIT_CONVERSION_ORDER_NO" body="td-spanlink" url="/wmsUnitConversion/detail.do?wmsUnitConversionOrderId=${WMS_UNIT_CONVERSION_ORDER_ID}" windowname="库存转换单详情" opentype="PARENT">库存转换单号</th>

            <th name="ORDER_TYPE">申请类型</th>
            <th name="CREATOR_NAME">创建人</th>
            <th name="ORG_NAME">归属部门</th>
            <th name="ADD_TIME" jdbctype="DATETIME">创建时间</th>
            <th name="SOURCE_SKU_NO">需转换订货号</th>
            <th name="SOURCE_GOODS_NAME">需转换商品名称</th>
            <th name="SOURCE_UNIT_NAME">需转换单位</th>
            <th name="SOURCE_NUM" jdbctype="NUMBER">需转换数量</th>
            <th name="TARGET_SKU_NO" >目标订货号</th>
            <th name="TARGET_GOODS_NAME">转换后商品名称</th>
            <th name="TARGET_UNIT_NAME">转换后单位</th>
            <th name="TARGET_NUM" jdbctype="NUMBER">转换后数量</th>
            <th name="VERIFY_STATUS">审核状态</th>

        </tr>
        </thead>
        <tbody>
        <tr>
            <td colspan="100">
                express:
                <pre id="express" class="layui-code" orderby="order by TWUCO.WMS_UNIT_CONVERSION_ORDER_ID desc" groupby="">
                   select TWUCO.WMS_UNIT_CONVERSION_ORDER_ID,
                   TWUCO.WMS_UNIT_CONVERSION_ORDER_NO,
                    CASE
                        TWUCO.ORDER_TYPE
                        WHEN 1 THEN
                        '单位转换'
                        WHEN 2 THEN
                        '主机配件'
                        WHEN 3 THEN
                        '组合产品'
                        ELSE ''
                   END ORDER_TYPE,
                    CASE
                        TWUCO.VERIFY_STATUS
                         WHEN 0 THEN
                        '待审核'
                        WHEN 1 THEN
                        '审核中'
                        WHEN 2 THEN
                        '审核通过'
                        WHEN 3 THEN
                        '审核不通过'
                        ELSE ''
                   END VERIFY_STATUS,
                   TWUCO.ORG_NAME,
                   TWUCO.COMMENTS,
                   TWUCO.IS_DELETE,
                   TWUCO.CREATOR,
                   TWUCO.CREATOR_NAME,
                    TWUCO.ADD_TIME,
                   TWUCOI.WMS_UNIT_CONVERSION_ORDER_ITEM_ID,
                   TWUCOI.SOURCE_SKU_ID,
                   TWUCOI.SOURCE_SKU_NO,
                   TWUCOI.SOURCE_GOODS_NAME,
                   TWUCOI.SOURCE_UNIT_NAME,
                   TWUCOI.SOURCE_NUM,
                   TWUCOI.OUT_STATUS,
                   TWUCOI.TARGET_SKU_ID,
                   TWUCOI.TARGET_SKU_NO,
                   TWUCOI.TARGET_GOODS_NAME,
                   TWUCOI.TARGET_UNIT_NAME,
                   TWUCOI.TARGET_NUM
            from T_WMS_UNIT_CONVERSION_ORDER TWUCO
                     left join T_WMS_UNIT_CONVERSION_ORDER_ITEM TWUCOI
                               on TWUCO.WMS_UNIT_CONVERSION_ORDER_ID = TWUCOI.WMS_UNIT_CONVERSION_ORDER_ID
            where TWUCO.IS_DELETE = 0
              and TWUCOI.IS_DELETE = 0
                 </pre>
            </td>
        </tr>
        </tbody>
    </table>
</div>

<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
</body>
</html>