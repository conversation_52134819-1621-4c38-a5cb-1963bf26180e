package com.vedeng.erp.trader.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.dao.OrganizationMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.page.Page;
import com.vedeng.erp.trader.domain.PublicCustomerRegionRules;
import com.vedeng.erp.trader.domain.dto.RegionRulesQueryDto;
import com.vedeng.erp.trader.domain.vo.PublicCustomerRegionRulesVo;
import com.vedeng.erp.trader.mapper.PublicCustomerRegionRulesMapper;
import com.vedeng.erp.trader.service.PublicCustomerRegionRulesService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PublicCustomerRegionRulesServiceImpl implements PublicCustomerRegionRulesService {

    @Resource
    private PublicCustomerRegionRulesMapper publicCustomerRegionRulesMapper;


    @Override
    public void deleteRegionRule(Integer publicCustomerRegionRulesId) {

        if (Objects.isNull(publicCustomerRegionRulesId)){
            throw new IllegalArgumentException("区域规则ID为空");
        }
        publicCustomerRegionRulesMapper.deleteByPrimaryKey(publicCustomerRegionRulesId);
    }

    @Override
    public void batchDeleteRegionRule(Integer[] idList) {

        if (idList != null && idList.length > 0) {
            publicCustomerRegionRulesMapper.batchDeleteByPrimaryKey(idList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateInfo(PublicCustomerRegionRulesVo publicCustomerRegionRulesVo,Integer userId) {

        //删除老数据
        publicCustomerRegionRulesMapper.deleteByPrimaryKey(publicCustomerRegionRulesVo.getPublicCustomerRegionRulesId());
        // 新增新数据
        long time = System.currentTimeMillis();
        PublicCustomerRegionRules newData = new PublicCustomerRegionRules();
        newData.setCreator(userId);
        newData.setUpdater(userId);
        newData.setAddTime(time);
        newData.setModTime(time);
        newData.setRegionId(publicCustomerRegionRulesVo.getRegionId());
        newData.setUserId(publicCustomerRegionRulesVo.getUserId());
        publicCustomerRegionRulesMapper.insertSelective(newData);
    }

    @Override
    public void batchAddPublicCustomerCalculateRules(List<PublicCustomerRegionRules> list) {
        log.info("保存的区域规则信息：{}",JSON.toJSONString(list));
        publicCustomerRegionRulesMapper.batchAddPublicCustomerCalculateRules(list);
    }


}
