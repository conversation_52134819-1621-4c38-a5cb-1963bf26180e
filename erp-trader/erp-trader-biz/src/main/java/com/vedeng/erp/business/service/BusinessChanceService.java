package com.vedeng.erp.business.service;

import com.github.pagehelper.PageInfo;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.business.common.enums.BusinessChanceStageEnum;
import com.vedeng.erp.business.domain.dto.*;
import com.vedeng.erp.business.dto.BusinessCloseDto;
import com.vedeng.erp.business.dto.CloseAuditDto;
import com.vedeng.erp.common.dto.RSalesJBusinessOrderDto;
import com.vedeng.erp.system.dto.UserDto;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 商机业务逻辑处理接口
 * @date 2022/7/12 14:05
 **/
public interface BusinessChanceService {



    /**
     * 计算 系统 成单机率 完整度 商机等级
     *
     * @param businessChanceData 商机对象
     */
    void calc(BusinessChanceDto businessChanceData);

    /**
     * 保存商机，当符合条件的时候 接着转报价单
     *
     * @param businessChanceDto 商机对象
     * @return BusinessChanceDto 对象
     */
    BusinessChanceDto add(BusinessChanceDto businessChanceDto);

    /**
     * 更新商机 当符合条件的时候 接着转报价单
     * @param businessChanceDto
     * @return
     */
    BusinessChanceDto update(BusinessChanceDto businessChanceDto);

    /**
     * 分页查询
     *
     * @param businessChanceDto 分页查询条件
     * @return 分页结果
     */
    PageInfo<BusinessChanceDto> page(PageParam<BusinessChanceDto> businessChanceDto);

    List<UserDto> getBussinessChanceBelongers(String name);
    List<UserDto> getBussinessChanceCreators(String name);

    /**
     * 查询某个商机详情信息
     * @param businessChanceDto 查询对象
     * @return BusinessChanceDto 结果集
     */
    BusinessChanceDto selectOne(BusinessChanceDto businessChanceDto);

    /**
     * 根据商机id查询详情信息
     * @param businessChanceId 商机id
     * @return BusinessChanceDto 商机详情
     */
    BusinessChanceDto viewDetail(Integer businessChanceId);

    BusinessChanceDto findBussinessByBusinessChanceNo(String businessChanceNo);


    /**
     * 导入商机
     * @param file file
     */
    List<Integer> importExcel(MultipartFile file) throws IOException;

    /**
     * 根据商机id查询关闭审核信息
     * @param businessChanceId 商机id
     * @return Map<String, Object>
     */
    List<Map<String, Object>> getCloseVerifyInfo(Integer businessChanceId, CurrentUser currentUser);

    /**
     * 根据商机编号获取合并的商机信息
     * @param businessChanceNo 合并后的商机编号
     * @return List<MergeChanceGoods>
     */
    List<MergeChanceGoods> getChancesByNoAfterMerged(String businessChanceNo);

    /**
     * 更新商机数据 单表
     * @param businessChanceDto businessChanceDto
     */
    void updateSingleData(BusinessChanceDto businessChanceDto);

    /**
     * 商机详情调用CRM查询中标信息失败
     * @param traderId 客户id
     */
    RestfulResult<List<BusinessCluesDetailResultDTO>> getBidInfoByTraderId(Integer traderId);

    /**
     * 更具商机id 查询 未转报价时候是否有通话记录有则转未商机进行中
     * @param bussinessChanceId
     */
    void updateBusinessChanceStatusByCommunication(Integer bussinessChanceId);

    /**
     * 列表更新商机主管指导字段
     * @param businessChanceDto
     * @return
     */
    void updateSupervisorGuidance(BusinessChanceDto businessChanceDto);

    List<BusinessChanceDto> getAccuracyEnum();

    /**
     * 智能询价生成商机和报价
     * @param smartQuoteDto
     * @return SmartQuoteResultDto
     */
    SmartQuoteResultDto smartQuoteAdd(SmartQuoteDto smartQuoteDto);

    /**
     * 报价单pdf
     *
     * @param quoteOrderId
     * @param quoteOrderNo
     */
    String getQuotePdf(Integer quoteOrderId, String quoteOrderNo);

    /**
     * 分享商机
     * @param rSalesJBusinessOrderDto rSalesJBusinessOrderDto
     */
    void shareBusinessChance(RSalesJBusinessOrderDto rSalesJBusinessOrderDto);

    /**
     * 获取分享商机历史
     * @param bussinessChanceId
     * @return List<RSalesJBusinessOrderDto>
     */
    List<RSalesJBusinessOrderDto> getShareBusinessChance(Integer bussinessChanceId);


    /**
     * 分享商机
     * @param bussinessChanceId
     */
    void cancelShareBusinessChance(Integer bussinessChanceId);

    Boolean attention(List<Integer> id, CurrentUser currentUser);

    Boolean cancelAttention(Integer id, CurrentUser currentUser);

    void closeBusinessAudit(CloseAuditDto closeAuditDto);

    void closeBusiness(BusinessCloseDto businessCloseDto);

    /**
     * 获取商机支持工作台数据
     * @param requestDto
     * @return
     */
//    BusinessSupportWorkbenchResponseDto getBusinessSupportWorkbench(User user,BusinessSupportWorkbenchRequestDto requestDto);

    /**
     * 获取咨询待处理列表
     * @param pageParam
     * @return
     */
    PageInfo<ConsultationPendingResponseDto> getConsultationPendingInfo(PageParam<ConsultationPendingRequestDto> pageParam);

    /**
     * 处理或关闭咨询记录
     * @param requestDto
     */
    void processOrCloseSeekHelp(ProcessOrCloseSeekHelpDto requestDto);

    /**
     * 更新商机阶段
     * @param businessChanceId
     * @param stageEnum
     */
    void updateStageAndStageTime(Integer businessChanceId, BusinessChanceStageEnum stageEnum);

    /**
     * 更新商机等级
     * @param businessChanceId
     */
    void updateLevel(Integer businessChanceId);

    /**
     * 查询所有商机共享人
     * @param name
     * @return
     */
    List<UserDto> findAllShareUser(String name);

    /**
     * 查询所有商机归属人
     * @param name
     * @return
     */
    List<UserDto> findAllBelongUser(String name);

    void addTrackQuotation(CurrentUser currentUser,String quotationNo,Integer traderId);

    Integer selectQuoteorderIdByBusinessChanceId(Integer bussinessChanceId);

    void close(Integer quoteorderId);
    
}
