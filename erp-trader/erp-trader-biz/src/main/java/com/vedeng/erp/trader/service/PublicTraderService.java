package com.vedeng.erp.trader.service;

import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/2/15 16:26
 */
public interface PublicTraderService {

    /**
     * 根据公私海区域规则&部分销售查询同省下所有销售对应客户
     *
     * @param userIdList
     * @return
     */
    List<Integer> getAllTraderCustomerInRegionsByUserIdList(List<Integer> userIdList);

    /**
     * 根据公私海区域规则&部分销售查询配置在同省下的所有销售集合
     * @param userIdList 销售集合
     * @return 区域规则，配置在同省下的销售集合
     */
    List<Integer> getAllUserListByRegionRule(List<Integer> userIdList);

    /**
     * 公海列表锁定操作
     *
     * @param publicCustomerRecordId
     * @return
     */
    ResultInfo lockPublicTrader(Integer publicCustomerRecordId) throws Exception;
    ResultInfo cancelLockPublicTrader(Integer publicCustomerRecordId) throws Exception;

    /**
     * <AUTHOR>
     * @desc 公海列表撤销公海操作
     * @param publicCustomerRecordId
     * @return
     */
    ResultInfo cancelPublicTraderJudge(Integer publicCustomerRecordId, Integer dayInput, User user);

    /**
     * 批量撤销公海
     * @param publicCustomerRecordIds
     * @param dayInput
     * @param currentUser
     * @return
     */
    ResultInfo banchCancelPublicTraderJudge(Integer[] publicCustomerRecordIds, Integer dayInput, User currentUser);
}
