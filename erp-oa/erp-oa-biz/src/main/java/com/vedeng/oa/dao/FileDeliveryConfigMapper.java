package com.vedeng.oa.dao;

import com.vedeng.oa.model.FileDeliveryConfig;
import com.vedeng.oa.model.FileDeliveryConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FileDeliveryConfigMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_CONFIG
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    long countByExample(FileDeliveryConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_CONFIG
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    int deleteByExample(FileDeliveryConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_CONFIG
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_CONFIG
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    int insert(FileDeliveryConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_CONFIG
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    int insertSelective(FileDeliveryConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_CONFIG
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    List<FileDeliveryConfig> selectByExampleWithBLOBs(FileDeliveryConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_CONFIG
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    List<FileDeliveryConfig> selectByExample(FileDeliveryConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_CONFIG
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    FileDeliveryConfig selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_CONFIG
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    int updateByExampleSelective(@Param("record") FileDeliveryConfig record, @Param("example") FileDeliveryConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_CONFIG
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    int updateByExampleWithBLOBs(@Param("record") FileDeliveryConfig record, @Param("example") FileDeliveryConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_CONFIG
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    int updateByExample(@Param("record") FileDeliveryConfig record, @Param("example") FileDeliveryConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_CONFIG
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    int updateByPrimaryKeySelective(FileDeliveryConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_CONFIG
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    int updateByPrimaryKeyWithBLOBs(FileDeliveryConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_CONFIG
     *
     * @mbg.generated Thu Nov 14 10:20:59 CST 2024
     */
    int updateByPrimaryKey(FileDeliveryConfig record);
}