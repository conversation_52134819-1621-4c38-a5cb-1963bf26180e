<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>改低价列表</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
</style>
<body id="saleorderGoodsLowerPriceList" datasource="erp-datasourcetarget" empty_show="" fixednumber="0" firstcol="numbers">
<div id="appendHead">
</div>

<div class="layui-fluid">
    <form class="layui-form" id="search">
        <div class="layui-inline ">
            <label>业务单号</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder="请输入" oper="like"
                       name="ORDER_NO" alias="lp" empty_show="-">
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">单据类型</label>
            <div class="layui-input-inline"><select class=" layui-input list-search-item " name="ORDER_TYPE" placeholder="全部" style="" alias="lp" jdbctype="" data="[{&quot;V&quot;:&quot;销售订单&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;报价单&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" oper=""></select>
            </div>
        </div>

        <div class="layui-inline">
            <label>订货号</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder="请输入" oper="like"
                       name="SKU" alias="lp" empty_show="-">
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-inline"><select class=" layui-input list-search-item " name="HANDLE_OPINION" placeholder="全部" style="" alias="IF(lp.HANDLE_OPINION is null,'1','0')" jdbctype="" data="[{&quot;V&quot;:&quot;已处理&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;未处理&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" oper=""></select>
            </div>
        </div>
        <div class="layui-inline ">
            <label>产品名称</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder="请输入" oper="like"
                       name="SKU_NAME" alias="" empty_show="-">
            </div>
        </div>


        <div class="layui-inline ">
            <label>创建人</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder="请输入" oper="like"
                       name="CREATOR_USERNAME" alias="IF(lp.ORDER_TYPE=1,IF(SUBSTR(lp.ORDER_NO, 2, 1) = 'F', TU.USERNAME, TU3.USERNAME),tu2.USERNAME)" empty_show="-">
            </div>
        </div>

        <div class=" layui-inline "><label class="layui-form-label">审核时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="SALE_ORDER_ADD_TIME" placeholder="" style="" alias="case
        when lp.ORDER_TYPE=1 then IF(ts.VALID_TIME=0,'',FROM_UNIXTIME(ts.VALID_TIME/1000,'%Y-%m-%d %H:%i:%s'))
        when lp.ORDER_TYPE=2 then IF(tq.VALID_TIME=0,'',FROM_UNIXTIME(tq.VALID_TIME/1000,'%Y-%m-%d %H:%i:%s'))
    end" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>



    </form>
    <hr class="layui-border-blue">

    <table id="table" class="layui-table" style=" width:100%">
        <thead>

        <tr id="column">
            <th name="ORDER_NO" body="td-spanlink" url="/order/quote/toSaleOrderDetail.do?saleorderId=${ORDER_ID}" opentype="PARENT">业务单号</th>
            <th item_name="ORDER_ID" name="ORDER_ID" body="td-text" url="" style="display: none">订单ID</th>
            <th name="ORDER_STR">单据类型</th>
            <th name="SKU_NAME" body="td-spanlink" url="/goods/goods/viewbaseinfo.do?goodsId=${GOODS_ID}" windowname="产品信息" opentype="PARENT" >产品名称</th>
            <th name="SKU">订货号</th>
            <th name="MODEL">型号/规格</th>
            <th name="NUM">数量</th>
            <th name="PRICE">单价</th>
            <th name="CHECK_PRICE">核价</th>
            <th name="CREATOR_USERNAME">创建人</th>
            <th name="VALID_TIME">审核时间</th>
            <th name="APPROVAL_OPINION">审批意见</th>
            <th item_name="HANDLE_OPINION_STR" name="HANDLE_OPINION_STR" body="td-text" url="">处理意见</th>
            <th item_name="CAN_HANDLE" name="CAN_HANDLE" body="td-text" url="" style="display: none">隐藏</th>
            <th type="rowbutton" id="rowbutton" style="min-width: 150px">
                <button layui-btn-disabled name="CAN_HANDLE" itemname="CAN_HANDLE" id="handle_button" type="single" opentype="MODEL"
                        url="/order/quote/addHandleOpinion.do?goodsLowerPriceId=${GOODS_LOWER_PRICE_ID}"
                        windowname="处理意见" name="MODEL" area="723px,296px" flag="${CAN_HANDLE}">处理
                </button>
            </th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td colspan="100">
                express:
                <pre id="express" class="layui-code" orderby="order by lp.ADD_TIME DESC" groupby="">
select
    lp.GOODS_LOWER_PRICE_ID,
    lp.ORDER_ID,
    lp.ORDER_NO,
    lp.ORDER_TYPE,
    IF(lp.ORDER_TYPE=1,'销售订单','报价单') as ORDER_STR,
    vcs.SKU_NAME,
    IF(lp.ORDER_TYPE=1,tsg.GOODS_ID,tqg.GOODS_ID) as GOODS_ID,
    lp.SKU,
    IF(lp.ORDER_TYPE=1,tsg.MODEL,tqg.MODEL) as MODEL,
    lp.NUM,
    lp.PRICE,
    lp.CHECK_PRICE,
    IF(lp.ORDER_TYPE=1,IF(SUBSTR(lp.ORDER_NO, 2, 1) = 'F', TU.USERNAME, TU3.USERNAME),tu2.USERNAME) as CREATOR_USERNAME,
    case
        when lp.ORDER_TYPE=1 then IF(ts.VALID_TIME=0,'--',FROM_UNIXTIME(ts.VALID_TIME/1000,'%Y-%m-%d %H:%i:%s'))
        when lp.ORDER_TYPE=2 then IF(tq.VALID_TIME=0,'--',FROM_UNIXTIME(tq.VALID_TIME/1000,'%Y-%m-%d %H:%i:%s'))
    end as VALID_TIME,
    IF(lp.APPROVAL_OPINION is null or lp.APPROVAL_OPINION='','--',lp.APPROVAL_OPINION) as APPROVAL_OPINION,
    lp.HANDLE_OPINION,
    IFNULL(lp.HANDLE_OPINION,'--') as HANDLE_OPINION_STR,
    IF(lp.HANDLE_OPINION is null,'处理','已处理') as CAN_HANDLE
from T_ORDER_GOODS_LOWER_PRICE lp
    left join T_SALEORDER ts on lp.ORDER_ID = ts.SALEORDER_ID
    left join T_QUOTEORDER tq on lp.ORDER_ID = tq.QUOTEORDER_ID
    left join T_SALEORDER_GOODS tsg on lp.ORDER_GOODS_ID = tsg.SALEORDER_GOODS_ID
    left join T_QUOTEORDER_GOODS tqg on lp.ORDER_GOODS_ID = tqg.QUOTEORDER_GOODS_ID
    left join V_CORE_SKU vcs on lp.SKU = vcs.SKU_NO
    left join T_USER TU on TU.USER_ID = ts.CREATOR
    left join T_USER tu2 on tu2.USER_ID = tq.CREATOR
    left join T_R_TRADER_J_USER rtju on ts.TRADER_ID = rtju.TRADER_ID and rtju.TRADER_TYPE = 1
    left join T_USER TU3 on rtju.USER_ID = TU3.USER_ID
where lp.IS_DELETE = 0
and IF(lp.ORDER_TYPE=1,ts.VALID_STATUS=1 and ts.STATUS!=3,tq.VALID_STATUS=1)
                 </pre>
            </td>
        </tr>
        </tbody>
    </table>
</div>
<div id="appendFoot">
    <script>
        $(function(){
            $("td[item_name=CAN_HANDLE]").each(function(e){
                if($(this).text().indexOf("已")>=0){
                    $(this).css({
                        "color": "black",
                        "pointer-events": "none",
                        "text-decoration": "none"
                    },"important")
                    $(this).next("td").css("pointer-events", "none")
                    $(this).next("td").find("button").addClass("layui-btn-disabled")
                }
            })
            $("td[item_name=ORDER_NO]").each(function(e){
                if ($(this).text().indexOf("V")>=0){
                    var id = $(this).next("td").text().trim();
                    $(this).attr("url","/order/quote/toQuoteOrderDetail.do?quoteOrderId="+id)
                    $(this).children("a").attr("item_url","/order/quote/toQuoteOrderDetail.do?quoteOrderId="+id)
                }
            })
        })
    </script>
</div>
<script>
    layui.use(function () {

    })
</script>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
</body>
</html>