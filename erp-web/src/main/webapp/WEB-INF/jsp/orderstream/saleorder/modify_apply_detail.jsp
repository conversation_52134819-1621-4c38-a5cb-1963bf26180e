<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="订单修改详情" scope="application" />
<%@ include file="../../common/common.jsp"%>
<%--
<script type="text/javascript" src='<%= basePath %>static/js/order/saleorder/view.js?rnd=${resourceVersionKey}'></script>
--%>
<script src="${pageContext.request.contextPath}/static/js/orderstream/index.js?rnd=${resourceVersionKey}"></script>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/orderstream/style.css">
	<div class="content mt10 ">
		<div style="height: 60px;text-align: center;margin-bottom: 20px;margin-top: 30px;margin-left: 8%;">
			<div class="t-line-wrap J-line-wrap" data-json='${statusStr}'></div>
<%--	<div class="t-line-wrap J-line-wrap" data-json='[{"jump":"#J-test","label":"待审核","status":1,"tip":"申请者:<PERSON>.chen"},{"label":"审核中","status":2},{"label":"审核通过/审核不通过","status":1}]'></div>--%>
		</div>

		<div class="table-buttons">
			<input type="hidden" name="saleorderId" value="${saleorder.saleorderId}">
			<c:choose>
				<c:when test="${saleorderModifyApplyInfo.validStatus eq 0}">

					<c:if test="${(null!=taskInfo and null!=taskInfo.getProcessInstanceId() and null!=taskInfo.assignee) or !empty candidateUserMap[taskInfo.id]}">
						<c:set var="shenhe" value="0"></c:set>
						<c:forEach items="${verifyUserList}" var="verifyUsernameInfo">
							<c:if test="${verifyUsernameInfo == curr_user.username}">
								<c:set var="shenhe" value="1"></c:set>
							</c:if>
						</c:forEach>
						<c:choose>
							<c:when test="${(taskInfo.assignee == curr_user.username or candidateUserMap['belong']) and shenhe!=1}">
								<button type="button" class="bt-bg-style bg-light-green bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?taskId=${taskInfo.id}&pass=true&type=1"}'>审核通过</button>
								<button type="button" class="bt-bg-style bg-light-orange bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?taskId=${taskInfo.id}&pass=false&type=1"}'>审核不通过</button>
							</c:when>
							<c:otherwise>
								<button type="button" class="bt-bg-style bt-small bg-light-greybe mr10">已申请审核</button>
							</c:otherwise>
						</c:choose>
					</c:if>
				</c:when>
				<c:otherwise>
				</c:otherwise>
			</c:choose>
		</div>

		<%--修改单表头信息--%>
		<%@ include file="../../orderstream/saleorder/modify_apply_detail_orderinfo.jsp" %>

        <div class="parts">
            <div class="title-container title-container-blue">
                <div class="table-title nobor">
                    收货信息
                </div>
            </div>
            <table class="table table-bordered table-striped table-condensed table-centered">
                <tbody>
                    <tr>
                        <td class="table-smaller">收货客户</td>
                        <td>
						<a class="addtitle" href="javascript:void(0);"
						   tabTitle='{"num":"viewcustomer${takeCustomerInfo.traderCustomerId}",
										"link":"./trader/customer/baseinfo.do?traderCustomerId=${takeCustomerInfo.traderCustomerId}&traderId=${saleorder.takeTraderId}",
										"title":"客户信息"}'>${saleorderModifyApplyInfo.takeTraderName}</a>
						</td>
                        <td class="table-smaller">收货联系人</td>
                        <td>
                        	<div class="customername pos_rel">
                                <span>${saleorderModifyApplyInfo.takeTraderContactName}
                                
                                <c:if test="${saleorderModifyApplyInfo.takeTraderContactName ne saleorderModifyApplyInfo.oldTakeTraderContactName}">
									<i class="iconbluesigh ml4 contorlIcon"></i></span>
	                                <div class="pos_abs customernameshow">原值：${saleorderModifyApplyInfo.oldTakeTraderContactName}</div>
                                </c:if>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>电话</td>
                        <td>
                        	<div class="customername pos_rel">
								<c:if test="${not empty saleorderModifyApplyInfo.takeTraderContactTelephone}">
									<i class="icontel cursor-pointer" title="点击拨号" onclick="callout('${saleorderModifyApplyInfo.takeTraderContactTelephone}',${saleorder.takeTraderId},1,2,${saleorder.saleorderId},${saleorderModifyApplyInfo.takeTraderContactId});"></i>
								</c:if>
                                <span>${saleorderModifyApplyInfo.takeTraderContactTelephone}
                                
                                <c:if test="${saleorderModifyApplyInfo.takeTraderContactTelephone ne saleorderModifyApplyInfo.oldTakeTraderContactTelephone}">
									<i class="iconbluesigh ml4 contorlIcon"></i></span>
	                                <div class="pos_abs customernameshow">原值：${saleorderModifyApplyInfo.oldTakeTraderContactTelephone}</div>
                                </c:if>
                            </div>
                        </td>
                        <td>手机</td>
                        <td>
                        	<div class="customername pos_rel">
								<c:if test="${not empty saleorderModifyApplyInfo.takeTraderContactMobile}">
									<i class="icontel cursor-pointer" title="点击拨号" onclick="callout('${saleorderModifyApplyInfo.takeTraderContactMobile}',${saleorder.takeTraderId},1,2,${saleorder.saleorderId},${saleorderModifyApplyInfo.takeTraderContactId});"></i>
								</c:if>
                                <span>${saleorderModifyApplyInfo.takeTraderContactMobile}
                                
                                <c:if test="${saleorderModifyApplyInfo.takeTraderContactMobile ne saleorderModifyApplyInfo.oldTakeTraderContactMobile}">
									<i class="iconbluesigh ml4 contorlIcon"></i></span>
	                                <div class="pos_abs customernameshow">原值：${saleorderModifyApplyInfo.oldTakeTraderContactMobile}</div>
                                </c:if>
                            </div>
                        </td>
                    </tr>
					<tr>
						<td>发货方式</td>
						<td>
							<div class="customername pos_rel">
								<span>
									<c:forEach var="list" items="${deliveryTypes}">
										<c:if test="${saleorderModifyApplyInfo.deliveryType == list.sysOptionDefinitionId}">${list.title}</c:if>
									</c:forEach>
								<c:if test="${saleorderModifyApplyInfo.deliveryType ne saleorderModifyApplyInfo.oldDeliveryType}">
									<i class="iconbluesigh ml4 contorlIcon"></i></span>
									<div class="pos_abs customernameshow">原值：
										<c:forEach var="list" items="${deliveryTypes}">
											<c:if test="${saleorderModifyApplyInfo.oldDeliveryType == list.sysOptionDefinitionId}">${list.title}</c:if>
										</c:forEach>
									</div>
								</c:if>
							</div>
						</td>
						<td>发货要求</td>
						<td>
							<div class="customername pos_rel">
								<span>
									<c:choose>
										<c:when test="${saleorderModifyApplyInfo.deliveryClaim eq 0}">立即发货</c:when>
										<c:otherwise>等通知发货 - <date:date value ="${saleorderModifyApplyInfo.deliveryDelayTime}" format="yyyy-MM-dd"/></c:otherwise>
									</c:choose>

                                <c:if test="${saleorderModifyApplyInfo.deliveryClaim ne saleorderModifyApplyInfo.oldDeliveryClaim}">
									<i class="iconbluesigh ml4 contorlIcon"></i></span>
								<div class="pos_abs customernameshow">原值：<c:choose>
									<c:when test="${saleorderModifyApplyInfo.oldDeliveryClaim eq 0}">立即发货</c:when>
									<c:otherwise>等通知发货 - <date:date value ="${saleorderModifyApplyInfo.oldDeliveryDelayTime}" format="yyyy-MM-dd"/></c:otherwise>
								</c:choose></div>
								</c:if>
							</div>
						</td>

					</tr>
                    <tr>
                        <td>收货地区</td>
                        <td colspan="3">
                        	<div class="customername pos_rel">
                                <span>${saleorderModifyApplyInfo.takeTraderArea}
                                
                                <c:if test="${saleorderModifyApplyInfo.takeTraderArea ne saleorderModifyApplyInfo.oldTakeTraderArea}">
									<i class="iconbluesigh ml4 contorlIcon"></i></span>
	                                <div class="pos_abs customernameshow">原值：${saleorderModifyApplyInfo.oldTakeTraderArea}</div>
                                </c:if>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>收货地址</td>
                        <td colspan="3">
                        	<div class="customername pos_rel">
                                <span>${saleorderModifyApplyInfo.takeTraderAddress}
                                
                                <c:if test="${saleorderModifyApplyInfo.takeTraderAddress ne saleorderModifyApplyInfo.oldTakeTraderAddress}">
									<i class="iconbluesigh ml4 contorlIcon"></i></span>
	                                <div class="pos_abs customernameshow">原值：${saleorderModifyApplyInfo.oldTakeTraderAddress}</div>
                                </c:if>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>物流备注</td>
                        <td colspan="3">
                        	<div class="customername pos_rel">
                                <span>${saleorderModifyApplyInfo.logisticsComments}
                                
                                <c:if test="${saleorderModifyApplyInfo.logisticsComments ne saleorderModifyApplyInfo.oldLogisticsComments}">
									<i class="iconbluesigh ml4 contorlIcon"></i></span>
	                                <div class="pos_abs customernameshow">原值：${saleorderModifyApplyInfo.oldLogisticsComments}</div>
                                </c:if>
                            </div>
                        </td>
                    </tr>
<%--				<tr>--%>
<%--					<td>指定物流公司</td>--%>
<%--					<td>--%>
<%--						<div class="customername pos_rel">--%>
<%--								<span>--%>
<%--									<c:forEach var="list" items="${allLogisticsList}">--%>
<%--										<c:if test="${saleorderModifyApplyInfo.logisticsId == list.logisticsId}">${list.name}</c:if>--%>
<%--									</c:forEach>--%>
<%--								<c:if test="${saleorderModifyApplyInfo.logisticsId ne saleorderModifyApplyInfo.oldLogisticsId}">--%>
<%--									<i class="iconbluesigh ml4 contorlIcon"></i></span>--%>
<%--							<div class="pos_abs customernameshow">原值：--%>
<%--								<c:forEach var="list" items="${allLogisticsList}">--%>
<%--									<c:if test="${saleorderModifyApplyInfo.oldLogisticsId == list.logisticsId}">${list.name}</c:if>--%>
<%--								</c:forEach>--%>
<%--							</div>--%>
<%--							</c:if>--%>
<%--						</div>--%>
<%--					</td>--%>
<%--					<td>运费说明</td>--%>
<%--					<td>--%>
<%--						<div class="customername pos_rel">--%>
<%--								<span>--%>
<%--									<c:forEach var="list" items="${freightDescriptionTypes}">--%>
<%--										<c:if test="${saleorderModifyApplyInfo.freightDescription == list.sysOptionDefinitionId}">${list.title}</c:if>--%>
<%--									</c:forEach>--%>
<%--								<c:if test="${saleorderModifyApplyInfo.freightDescription ne saleorderModifyApplyInfo.oldFreightDescription}">--%>
<%--									<i class="iconbluesigh ml4 contorlIcon"></i></span>--%>
<%--							<div class="pos_abs customernameshow">原值：--%>
<%--								<c:forEach var="list" items="${freightDescriptionTypes}">--%>
<%--									<c:if test="${saleorderModifyApplyInfo.oldFreightDescription == list.sysOptionDefinitionId}">${list.title}</c:if>--%>
<%--								</c:forEach>--%>
<%--							</div>--%>
<%--							</c:if>--%>
<%--						</div>--%>
<%--					</td>--%>
<%--				</tr>--%>
				<tr>
					<td>随货出库单</td>
					<td colspan="3">
						<div class="customername pos_rel">
								<span>
									<c:choose>
										<c:when test="${saleorderModifyApplyInfo.isPrintout eq 0}">不打印</c:when>
										<c:when test="${saleorderModifyApplyInfo.isPrintout eq 1}">打印-含价格</c:when>
										<c:when test="${saleorderModifyApplyInfo.isPrintout >= 2}">打印-不含价格</c:when>
										<c:otherwise></c:otherwise>
									</c:choose>

                                <c:if test="${saleorderModifyApplyInfo.isPrintout ne saleorderModifyApplyInfo.oldIsPrintout}">
									<i class="iconbluesigh ml4 contorlIcon"></i></span>
							<div class="pos_abs customernameshow">原值：
								<c:choose>
								<c:when test="${saleorderModifyApplyInfo.oldIsPrintout eq 0}">不打印</c:when>
								<c:when test="${saleorderModifyApplyInfo.oldIsPrintout eq 1}">打印-含价格</c:when>
								<c:when test="${saleorderModifyApplyInfo.oldIsPrintout >= 2}">打印-不含价格</c:when>
								<c:otherwise></c:otherwise>
							</c:choose></div>
							</c:if>
						</div>
					</td>
				</tr>
                </tbody>
            </table>
        </div>
        <div class="parts">
            <div class="title-container title-container-blue">
                <div class="table-title nobor">
                    收票信息
                </div>
            </div>
            <table class="table table-bordered table-striped table-condensed table-centered">
                <tbody>
                    <tr>
                        <td class="table-smaller">收票客户</td>
                        <td>
							<a class="addtitle" href="javascript:void(0);"
							   tabTitle='{"num":"viewcustomer${invoiceCustomerInfo.traderCustomerId}",
										"link":"./trader/customer/baseinfo.do?traderCustomerId=${invoiceCustomerInfo.traderCustomerId}&traderId=${saleorder.invoiceTraderId}",
										"title":"客户信息"}'>${saleorderModifyApplyInfo.invoiceTraderName}</a>
						</td>
                        <td class="table-smaller">收票联系人</td>
                        <td>
                        	<div class="customername pos_rel">
                                <span>${saleorderModifyApplyInfo.invoiceTraderContactName}
                                
                                <c:if test="${saleorderModifyApplyInfo.invoiceTraderContactName ne saleorderModifyApplyInfo.oldInvoiceTraderContactName}">
									<i class="iconbluesigh ml4 contorlIcon"></i></span>
	                                <div class="pos_abs customernameshow">原值：${saleorderModifyApplyInfo.oldInvoiceTraderContactName}</div>
                                </c:if>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>电话</td>
                        <td>
                        	<div class="customername pos_rel">
								<c:if test="${not empty saleorderModifyApplyInfo.invoiceTraderContactTelephone}">
									<i class="icontel cursor-pointer" title="点击拨号" onclick="callout('${saleorderModifyApplyInfo.invoiceTraderContactTelephone}',${saleorder.invoiceTraderId},1,2,${saleorder.saleorderId},${saleorderModifyApplyInfo.invoiceTraderContactId});"></i>
								</c:if>
                                <span>${saleorderModifyApplyInfo.invoiceTraderContactTelephone}
                                
                                <c:if test="${saleorderModifyApplyInfo.invoiceTraderContactTelephone ne saleorderModifyApplyInfo.oldInvoiceTraderContactTelephone}">
									<i class="iconbluesigh ml4 contorlIcon"></i></span>
	                                <div class="pos_abs customernameshow">原值：${saleorderModifyApplyInfo.oldInvoiceTraderContactTelephone}</div>
                                </c:if>
                            </div>
                        </td>
                        <td>手机</td>
                        <td>
                        	<div class="customername pos_rel">
								<c:if test="${not empty saleorderModifyApplyInfo.invoiceTraderContactMobile}">
									<i class="icontel cursor-pointer" title="点击拨号" onclick="callout('${saleorderModifyApplyInfo.invoiceTraderContactMobile}',${saleorder.invoiceTraderId},1,2,${saleorder.saleorderId},${saleorderModifyApplyInfo.invoiceTraderContactId});"></i>
								</c:if>
                                <span>${saleorderModifyApplyInfo.invoiceTraderContactMobile}
                                
                                <c:if test="${saleorderModifyApplyInfo.invoiceTraderContactMobile ne saleorderModifyApplyInfo.oldInvoiceTraderContactMobile}">
									<i class="iconbluesigh ml4 contorlIcon"></i></span>
	                                <div class="pos_abs customernameshow">原值：${saleorderModifyApplyInfo.oldInvoiceTraderContactMobile}</div>
                                </c:if>
                            </div>
                        </td>
                    </tr>
                    <tr>
<%--                        <td>收票地区</td>--%>
<%--                        <td>--%>
<%--                        	<div class="customername pos_rel">--%>
<%--                                <span>${saleorderModifyApplyInfo.invoiceTraderArea}--%>
<%--                                --%>
<%--                                <c:if test="${saleorderModifyApplyInfo.invoiceTraderArea ne saleorderModifyApplyInfo.oldInvoiceTraderArea}">--%>
<%--									<i class="iconbluesigh ml4 contorlIcon"></i></span>--%>
<%--	                                <div class="pos_abs customernameshow">原值：${saleorderModifyApplyInfo.oldInvoiceTraderArea}</div>--%>
<%--                                </c:if>--%>
<%--                            </div>--%>
<%--                        </td>--%>
                        <td>发票类型</td>
                        <td>
                        	<c:if test="${saleorderModifyApplyInfo.oldInvoiceType != 0}">
                        	<div class="customername pos_rel">
                        		<span>
	                        	<c:forEach var="list" items="${invoiceTypes}">
			                    	<c:if test="${saleorderModifyApplyInfo.invoiceType == list.sysOptionDefinitionId}">${list.title}</c:if>
			                    </c:forEach>
		                    	<c:if test="${saleorderModifyApplyInfo.invoiceType ne saleorderModifyApplyInfo.oldInvoiceType}">
		                    		<i class="iconbluesigh ml4 contorlIcon"></i></span>
	                                <div class="pos_abs customernameshow">原值：
	                                <c:forEach var="list" items="${invoiceTypes}">
				                    	<c:if test="${saleorderModifyApplyInfo.oldInvoiceType == list.sysOptionDefinitionId}">${list.title}</c:if>
				                    </c:forEach>
	                                </div>
		                    	</c:if>
		                    </div>
		                    </c:if>
                        </td>
						<td>暂缓开票</td>
						<td>
							<div class="customername pos_rel">
													<span>
													<c:choose>
														<c:when test="${saleorderModifyApplyInfo.isDelayInvoice eq 0}">否</c:when>
														<c:otherwise>是</c:otherwise>
													</c:choose>
													<c:if test="${saleorderModifyApplyInfo.isDelayInvoice ne saleorderModifyApplyInfo.oldIsDelayInvoice}">
														<i class="iconbluesigh ml4 contorlIcon"></i></span>
								<div class="pos_abs customernameshow">原值：
									<c:choose>
										<c:when test="${saleorderModifyApplyInfo.oldIsDelayInvoice eq 0}">否</c:when>
										<c:otherwise>是</c:otherwise>
									</c:choose>
								</div>
								</c:if>
							</div>
						</td>
                    </tr>
					<c:choose>
						<c:when test="${saleorder.orderType eq 8 || saleorder.orderType eq 9 || saleorder.orderType eq 5 || saleorder.orderType eq 7}">
							<tr>
								<td>是否票货同行</td>
								<td colspan="3">
									<div class="customername pos_rel">
										<span>
										${saleorderModifyApplyInfo.isSameAddress eq 0 ?"票货不同行":(saleorderModifyApplyInfo.isSameAddress eq 1 ? "票货同行":"")}
									&nbsp;&nbsp;
										${saleorderModifyApplyInfo.invoiceSendNode eq 0 ? "全部发货时一次寄送":"每次发货时分别寄送"}
										<c:if test="${(saleorderModifyApplyInfo.isSameAddress ne saleorderModifyApplyInfo.oldIsSameAddress)
										|| (saleorderModifyApplyInfo.invoiceSendNode ne saleorderModifyApplyInfo.oldInvoiceSendNode)}">
											<i class="iconbluesigh ml4 contorlIcon"></i></span>
											<div class="pos_abs customernameshow">原值：
													${saleorderModifyApplyInfo.oldIsSameAddress eq 0 ?"票货不同行":(saleorderModifyApplyInfo.oldIsSameAddress eq 1 ? "票货同行":"")}
												&nbsp;&nbsp;
													${saleorderModifyApplyInfo.oldInvoiceSendNode eq 0 ? "全部发货时一次寄送":"每次发货时分别寄送"}
											</div>
										</c:if>
									</div>
								</td>
<%--								<td>收票邮箱</td>--%>
<%--								<td>--%>
<%--									<div class="customername pos_rel">--%>
<%--										<span>--%>
<%--										${saleorderModifyApplyInfo.invoiceEmail}--%>
<%--										<c:if test="${saleorderModifyApplyInfo.invoiceEmail ne saleorderModifyApplyInfo.oldInvoiceEmail}">--%>
<%--											<i class="iconbluesigh ml4 contorlIcon"></i></span>--%>
<%--											<div class="pos_abs customernameshow">原值：--%>
<%--												${saleorderModifyApplyInfo.oldInvoiceEmail}--%>
<%--											</div>--%>
<%--										</c:if>--%>
<%--									</div>--%>
<%--								</td>--%>
							</tr>
						</c:when>
						<c:otherwise></c:otherwise>
					</c:choose>
<%--                    <tr>--%>
<%--                        <td>收票地址</td>--%>
<%--                        <td >--%>
<%--                        	<div class="customername pos_rel">--%>
<%--                                <span>${saleorderModifyApplyInfo.invoiceTraderAddress}--%>
<%--                                --%>
<%--                                <c:if test="${saleorderModifyApplyInfo.invoiceTraderAddress ne saleorderModifyApplyInfo.oldInvoiceTraderAddress}">--%>
<%--									<i class="iconbluesigh ml4 contorlIcon"></i></span>--%>
<%--	                                <div class="pos_abs customernameshow">原值：${saleorderModifyApplyInfo.oldInvoiceTraderAddress}</div>--%>
<%--                                </c:if>--%>
<%--                            </div>--%>
<%--                        </td>--%>
<%--						<td>发票是否寄送</td>--%>
<%--						<td>--%>
<%--							<c:if test="${saleorderModifyApplyInfo.oldIsSendInvoice != -1}">--%>
<%--							<div class="customername pos_rel">--%>
<%--		                    	<span>--%>
<%--			                    <c:choose>--%>
<%--									<c:when test="${saleorderModifyApplyInfo.isSendInvoice eq 0}">不寄送</c:when>--%>
<%--									<c:otherwise>寄送</c:otherwise>--%>
<%--								</c:choose>--%>
<%--			                    <c:if test="${saleorderModifyApplyInfo.isSendInvoice ne saleorderModifyApplyInfo.oldIsSendInvoice}">--%>
<%--			                    	<i class="iconbluesigh ml4 contorlIcon"></i></span>--%>
<%--								<div class="pos_abs customernameshow">原值：--%>
<%--									<c:choose>--%>
<%--										<c:when test="${saleorderModifyApplyInfo.oldIsSendInvoice eq 0}">不寄送</c:when>--%>
<%--										<c:otherwise>寄送</c:otherwise>--%>
<%--									</c:choose>--%>
<%--								</div>--%>
<%--								</c:if>--%>
<%--							</div>--%>
<%--							</c:if>--%>
<%--						</td>--%>
<%--                    </tr>--%>
<%--                    <tr>--%>
<%--                    	<td>开票方式</td>--%>
<%--                    	<td>--%>
<%--		                    <div class="customername pos_rel">--%>
<%--		                    	<span>--%>
<%--			                    <c:choose>--%>
<%--			                    	<c:when test="${saleorderModifyApplyInfo.invoiceMethod eq 1}">手动纸质开票</c:when>--%>
<%--			                    	<c:when test="${saleorderModifyApplyInfo.invoiceMethod eq 2}">自动纸质开票</c:when>--%>
<%--			                    	<c:when test="${saleorderModifyApplyInfo.invoiceMethod eq 3}">自动电子发票</c:when>--%>
<%--									<c:when test="${saleorderModifyApplyInfo.invoiceMethod eq 4}">自动数电发票</c:when>--%>
<%--			                    </c:choose>--%>
<%--			                    <c:if test="${saleorderModifyApplyInfo.invoiceMethod ne saleorderModifyApplyInfo.oldInvoiceMethod}">--%>
<%--			                    	<i class="iconbluesigh ml4 contorlIcon"></i></span>--%>
<%--	                                <div class="pos_abs customernameshow">原值：--%>
<%--	                                <c:choose>--%>
<%--				                    	<c:when test="${saleorderModifyApplyInfo.oldInvoiceMethod eq 1}">手动纸质开票</c:when>--%>
<%--			                    		<c:when test="${saleorderModifyApplyInfo.oldInvoiceMethod eq 2}">自动纸质开票</c:when>--%>
<%--			                    		<c:when test="${saleorderModifyApplyInfo.oldInvoiceMethod eq 3}">自动电子发票</c:when>--%>
<%--										<c:when test="${saleorderModifyApplyInfo.oldInvoiceMethod eq 4}">自动数电发票</c:when>--%>
<%--				                    </c:choose>--%>
<%--	                                </div>--%>
<%--			                    </c:if>--%>
<%--		                    </div>--%>
<%--                    	</td>--%>
<%--                    </tr>--%>
<%--					<tr>--%>
<%--						<td>开票备注</td>--%>
<%--						<td colspan="3">--%>
<%--							<div class="customername pos_rel">--%>
<%--                                <span>${saleorderModifyApplyInfo.invoiceComments}--%>

<%--                                <c:if test="${saleorderModifyApplyInfo.invoiceComments ne saleorderModifyApplyInfo.oldInvoiceComments}">--%>
<%--									<i class="iconbluesigh ml4 contorlIcon"></i></span>--%>
<%--								<div class="pos_abs customernameshow">原值：${saleorderModifyApplyInfo.oldInvoiceComments}</div>--%>
<%--								</c:if>--%>
<%--							</div>--%>
<%--						</td>--%>
<%--					</tr>--%>
				<tr>
					<td>收票邮箱</td>
					<td colspan="3">${saleorder.invoiceEmail}</td>
				</tr>
                </tbody>
            </table>
        </div>

		<%--商品信息--%>
		<%@ include file="../../orderstream/saleorder/modify_apply_detail_goods.jsp" %>

	<div class="parts">
            <div class="title-container">
                <div class="table-title nobor">
                    审核记录
                </div>
            </div>
            <table class="table">
            <thead>
            	    <tr>
                        <th>操作人</th>
                        <th>操作时间</th>
                        <th>操作事项</th>
                        <th>备注</th>
                    </tr>
            </thead>
                <tbody>
                
                    <c:if test="${null!=historicActivityInstance}">
                    <c:forEach var="hi" items="${historicActivityInstance}" varStatus="status">
                    <c:if test="${not empty  hi.activityName}">
                    <tr>
                    	<td>
                    	<c:choose>
							<c:when test="${hi.activityType == 'startEvent'}"> 
							${startUser}
							</c:when>
							<c:when test="${hi.activityType == 'intermediateThrowEvent'}">
							</c:when>
							<c:otherwise>
								<c:if test="${historicActivityInstance.size() == status.count}">
									<c:forEach var="vs" items="${verifyUsersList}">
									 	<c:if test="${fn:contains(verifyUserList, vs)}">
									 		<span class="font-green">${vs}</span>&nbsp;
									 	</c:if>
									 	<c:if test="${!fn:contains(verifyUserList, vs)}">
									 		<span>${vs}</span>&nbsp;
									 	</c:if>
									 </c:forEach>
									 <c:if test="${empty verifyUsersList && empty hi.assignee}">
										${verifyUsers}
									</c:if>
								</c:if>
								<c:if test="${historicActivityInstance.size() != status.count}">
									<c:forEach items="${assigneeVos}" var="assigneeVo">
										<c:if test="${assigneeVo.assignee eq hi.assignee}">
											${assigneeVo.realName}
										</c:if>
									</c:forEach>
								<%--	${hi.assignee}  --%>
								</c:if>   
							</c:otherwise>
						</c:choose>
                    	
                    	
                    	</td>
                        <td><fmt:formatDate value="${hi.endTime}" pattern="yyyy-MM-dd HH:mm:ss" /></td>
                        <td>
                        <c:choose>
									<c:when test="${hi.activityType == 'startEvent'}"> 
							开始
							</c:when>
									<c:when test="${hi.activityType == 'intermediateThrowEvent'}"> 
							结束
							</c:when>
							<c:otherwise>   
							${hi.activityName}  
							</c:otherwise>
						</c:choose>
						</td>
                        <td class="font-red">${commentMap[hi.taskId]}</td>
                    </tr>
                    </c:if>
                    </c:forEach>
                    </c:if>
                    <!-- 查询无结果弹出 -->
           		<c:if test="${empty historicActivityInstance}">
		       			<!-- 查询无结果弹出 -->
		       			<tr>
		       				<td colspan="4">暂无审核记录。</td>
		       			</tr>
		        	</c:if>
                </tbody>
            </table>
        </div>
       
    </div>
    <input type="hidden" value="${saleorderModifyApplyInfo.saleorderModifyApplyId}" id="saleorderModifyApplyId"/>
    <input type="hidden" value="${saleorderModifyApplyInfo.saleorderId}" id="saleorderId"/>

<%--
    <script type="text/javascript" src='<%= basePath %>static/js/order/saleorder/modify_apply_view.js?rnd=${resourceVersionKey}'></script>
--%>
<%--
    <script type="text/javascript" src='<%= basePath %>static/js/logistics/warehouseOut/viewWarehouseOut.js?rnd=${resourceVersionKey}'></script>
--%>
<%@ include file="../../common/footer.jsp"%>