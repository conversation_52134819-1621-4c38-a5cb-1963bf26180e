.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item {
  position: relative;
  margin-bottom: 10px;
  padding-right: 40px;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item:last-child {
  margin-bottom: 0;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .del {
  width: 30px;
  height: 33px;
  margin-left: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  position: absolute;
  right: 0;
  top: 0;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .del:hover {
  color: #f60;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel {
  position: absolute;
  top: 33px;
  left: 0;
  z-index: 10;
  width: 100%;
  box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.1);
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel .search-panel-inner {
  background: #fff;
  border: solid 1px #ddd;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel .search-panel-inner .search-panel-list .label {
  color: #999;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 10px;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel .search-panel-inner .search-panel-list .search-list-content {
  max-height: 352px;
  overflow-y: auto;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel .search-panel-inner .search-panel-list .search-list-content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel .search-panel-inner .search-panel-list .search-list-content::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel .search-panel-inner .search-panel-list .search-list-content::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel .search-panel-inner .search-panel-list .search-list-content::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel .search-panel-inner .search-panel-list .search-list-content::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel .search-panel-inner .search-panel-list .search-list-content .search-panel-item {
  padding: 6px 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel .search-panel-inner .search-panel-list .search-list-content .search-panel-item .num {
  color: #999;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel .search-panel-inner .search-panel-list .search-list-content .search-panel-item:hover {
  color: #f60;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel .search-panel-inner .search-loading {
  padding: 10px;
  display: flex;
  align-items: center;
}
@keyframes loading {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel .search-panel-inner .search-loading > .icon-loading {
  font-size: 16px;
  color: #09f;
  animation: loading 2s linear infinite;
  z-index: 99;
  margin-right: 5px;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel .search-panel-inner .null-data {
  text-align: center;
  padding: 10px;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .ui-related-search-history {
  position: absolute;
  top: 33px;
  left: 0;
  z-index: 10;
  width: 100%;
  box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.1);
  padding: 5px 0;
  background: #fff;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .ui-related-search-history .ui-related-search-history-title {
  color: #999;
  padding: 6px 10px;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .ui-related-search-history .ui-related-search-history-item {
  padding: 6px 10px;
  cursor: pointer;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .ui-related-search-history .ui-related-search-history-item:hover {
  color: #f60;
}
.vd-ui-search-category .vd-ui-search-category-btn {
  margin-top: 10px;
}
.vd-ui-search-category .vd-ui-search-category-btn .btn {
  color: #09f;
  transition: color 0.12s ease-in;
  cursor: pointer;
}
.vd-ui-search-category .vd-ui-search-category-btn .btn:hover {
  color: #f60;
}
.vd-ui-search-category .vd-ui-search-category-btn .btn .icon-add {
  font-size: 16px;
  vertical-align: -2px;
}
