package com.vedeng.erp.finance.domain.entity;

import com.vedeng.common.core.base.BaseDto;
import lombok.Data;

/**
    * 回单解析表
    */
@Data
public class BankReceiptRecordEntity extends BaseDto {
    /**
    * 主键
    */
    private Long bankReceiptAliasId;

    /**
    * 账户类型 1银行 2微信 3支付宝
    */
    private Integer accountType;

    /**
    * 账号
    */
    private String accountNo;

    /**
    * 业务类型 0销售
    */
    private Integer bizType;

    /**
    * 业务ID 业务类型为0=销售单ID
    */
    private Integer bizId;

    /**
    * 回单解析名称，逗号分割
    */
    private String bankNameAlias;

    /**
    * 是否删除 0否 1是
    */
    private Integer isDelete;

    /**
    * 更新备注
    */
    private String updateRemark;
}
