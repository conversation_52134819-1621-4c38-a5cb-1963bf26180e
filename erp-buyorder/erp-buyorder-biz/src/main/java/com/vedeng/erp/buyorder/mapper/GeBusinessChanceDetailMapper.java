package com.vedeng.erp.buyorder.mapper;

import com.vedeng.erp.buyorder.domain.entity.GeBusinessChanceDetail;

public interface GeBusinessChanceDetailMapper {
    int deleteByPrimaryKey(Integer geBussinessChanceDetailId);

    int insert(GeBusinessChanceDetail record);

    int insertSelective(GeBusinessChanceDetail record);

    GeBusinessChanceDetail selectByPrimaryKey(Integer geBussinessChanceDetailId);

    int updateByPrimaryKeySelective(GeBusinessChanceDetail record);

    int updateByPrimaryKey(GeBusinessChanceDetail record);

    GeBusinessChanceDetail queryByGeBussinessChanceId(Integer geBussinessChanceId);
}