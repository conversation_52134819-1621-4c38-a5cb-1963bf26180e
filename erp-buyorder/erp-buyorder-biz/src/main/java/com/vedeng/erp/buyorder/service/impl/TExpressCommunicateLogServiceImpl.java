package com.vedeng.erp.buyorder.service.impl;

import org.springframework.stereotype.Service;

import org.springframework.beans.factory.annotation.Autowired;

import com.vedeng.erp.finance.domain.entity.TExpressCommunicateLog;
import com.vedeng.erp.finance.mapper.TExpressCommunicateLogMapper;
import com.vedeng.erp.buyorder.service.TExpressCommunicateLogService;

import java.util.Collections;
import java.util.List;

@Service
public class TExpressCommunicateLogServiceImpl implements TExpressCommunicateLogService{

    @Autowired
    private TExpressCommunicateLogMapper tExpressCommunicateLogMapper;

    @Override
    public int deleteByPrimaryKey(Integer id) {
        return tExpressCommunicateLogMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(TExpressCommunicateLog record) {
        return tExpressCommunicateLogMapper.insert(record);
    }

    @Override
    public int insertSelective(TExpressCommunicateLog record) {
        return tExpressCommunicateLogMapper.insertSelective(record);
    }

    @Override
    public TExpressCommunicateLog selectByPrimaryKey(Integer id) {
        return tExpressCommunicateLogMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<TExpressCommunicateLog> selectByExpressId(Integer expressId) {
        return tExpressCommunicateLogMapper.selectByExpressId(expressId);
    }

    @Override
    public int updateByPrimaryKeySelective(TExpressCommunicateLog record) {
        return tExpressCommunicateLogMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(TExpressCommunicateLog record) {
        return tExpressCommunicateLogMapper.updateByPrimaryKey(record);
    }

}
