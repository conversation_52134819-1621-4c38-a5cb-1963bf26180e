package com.vedeng.api.standard.validation;

import java.util.Map;

/**
 * 验证规则接口
 * 定义通用的验证规则规范
 * 
 * @param <T> 验证对象类型
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-27
 */
public interface ValidationRule<T> {
    
    /**
     * 执行验证
     * 
     * @param request 待验证的请求对象
     * @param context 验证上下文，包含额外的验证参数
     * @return 验证结果
     */
    ValidationResult validate(T request, Map<String, Object> context);
    
    /**
     * 获取规则名称
     * 
     * @return 规则名称
     */
    default String getRuleName() {
        return this.getClass().getSimpleName();
    }
    
    /**
     * 获取规则描述
     * 
     * @return 规则描述
     */
    default String getRuleDescription() {
        return "验证规则：" + getRuleName();
    }
    
    /**
     * 是否支持指定的请求类型
     * 
     * @param requestClass 请求类型
     * @return 是否支持
     */
    default boolean supports(Class<?> requestClass) {
        return true;
    }
}
