package com.vedeng.todolist.service.visitor;

import com.vedeng.authorization.model.User;
import com.vedeng.flash.dao.EarlyWarningTaskMapper;
import com.vedeng.todolist.dto.EarlyWarningTicksTaskTodoDto;
import com.vedeng.todolist.dto.SupplyChainStaffTodoDtoBuild;
import com.wms.service.context.ThreadLocalContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2021/5/19 17:37
 */
@Service
public class EarlyWarningTicksTaskStaffVisitor implements TodoVisitor<SupplyChainStaffTodoDtoBuild> {
    @Resource
    private EarlyWarningTaskMapper earlyWarningTaskMapper;

    @Override
    public void visitor(SupplyChainStaffTodoDtoBuild supplyChainStaffTodoDtoBuild) {

        User user = ThreadLocalContext.get("userInfo");
        List<Integer> userIds = Arrays.asList(user.getUserId());
        EarlyWarningTicksTaskTodoDto earlyWarningTicksTaskTodoDto = earlyWarningTaskMapper.getEarlyWarningTicksTaskTodoDtoByuserIds(userIds);
        //将当前用户的可视范围传到前端去，这样打开催票任务就不用重复计算了
        earlyWarningTicksTaskTodoDto.setUserIdList(userIds);
        supplyChainStaffTodoDtoBuild.get().setEarlyWarningTicksTaskTodoDto(earlyWarningTicksTaskTodoDto);
    }
}
