package com.vedeng.price.service.impl;

import com.vedeng.common.constant.ErpConst;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.price.api.price.dto.price.PriceInfoResponseDto;
import com.vedeng.price.api.price.dto.price.SkuPriceInfoPurchaseDto;
import com.vedeng.price.dto.ContractPriceInfoDetailResponseDto;
import com.vedeng.price.dto.GoodSalePrice;
import com.vedeng.price.dto.PriceType;
import com.vedeng.price.dto.SkuPriceInfoDetailResponseDto;
import com.vedeng.price.service.BasePriceService;
import com.vedeng.price.service.PriceInfoDealWithService;
import com.vedeng.trader.model.TraderCustomer;
import com.vedeng.trader.service.TraderCustomerService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class PriceInfoDealWithServiceImpl implements PriceInfoDealWithService {

    @Autowired
    private BasePriceService basePriceService;

    @Autowired
    private TraderCustomerService traderCustomerService;

    @Autowired
    private SaleorderGoodsMapper saleorderGoodsMapper;
    
    Logger logger= LoggerFactory.getLogger(PriceInfoDealWithServiceImpl.class);

    @Override
    public Map<String, String> dealWithGoodsSalePrice(Integer traderId, List<GoodSalePrice> goodSalePrices) {

        Map<String,String> skuNoAndPriceMap = new HashMap<>();

        if(CollectionUtils.isEmpty(goodSalePrices)){
            return skuNoAndPriceMap;
        }
        boolean isTraderCustomer = false;
        TraderCustomer traderCustomer = this.traderCustomerService.getTraderCustomerId(traderId);
        if(traderCustomer != null &&  ErpConst.CUSTOME_RNATURE.equals(traderCustomer.getCustomerNature())){
            isTraderCustomer = true;
        }
        ContractPriceInfoDetailResponseDto contractPriceInfoDetail = null;

        for(GoodSalePrice goodSalePrice : goodSalePrices){

            String skuNo = goodSalePrice.getSkuNo();

            contractPriceInfoDetail = this.basePriceService.findSkuContractInfoByCon(traderId,skuNo);

            //有合约价 就设置合约价
            if(contractPriceInfoDetail != null && contractPriceInfoDetail.getContractPrice() != null){
                skuNoAndPriceMap.put(skuNo,contractPriceInfoDetail.getContractPrice().toPlainString()  + "(合约价)");
                skuNoAndPriceMap.put(contractPriceInfoDetail.getSkuNo()+"_tagName",
                        contractPriceInfoDetail.getContractPrice() == null? "" : "合约");
                skuNoAndPriceMap.put(contractPriceInfoDetail.getSkuNo()+"_price",
                        contractPriceInfoDetail.getContractPrice() == null? "" : contractPriceInfoDetail.getContractPrice().toPlainString());
                continue;
            }

            SkuPriceInfoDetailResponseDto skuPriceInfoDetailResponseDto = this.basePriceService.findSkuPriceInfoBySkuNo(skuNo);



            //已经核价就设置成本价
            if (skuPriceInfoDetailResponseDto != null && traderCustomer != null) {

                //分销商
                if(ErpConst.CUSTOME_RNATURE.equals(traderCustomer.getCustomerNature())){
                    skuNoAndPriceMap.put(skuNo,(skuPriceInfoDetailResponseDto.getDistributionPrice() == null ? "0": skuPriceInfoDetailResponseDto.getDistributionPrice().toPlainString())+"(经销价)");
                    skuNoAndPriceMap.put(skuPriceInfoDetailResponseDto.getSkuNo()+"_tagName",
                            skuPriceInfoDetailResponseDto.getDistributionPrice() == null? "" : "经销");
                    skuNoAndPriceMap.put(skuPriceInfoDetailResponseDto.getSkuNo()+"_price",
                            skuPriceInfoDetailResponseDto.getDistributionPrice() == null? "" : skuPriceInfoDetailResponseDto.getDistributionPrice().toPlainString());
                }

                //终端
                if(ErpConst.CUSTOME_INTERMIAL.equals(traderCustomer.getCustomerNature())){
                    skuNoAndPriceMap.put(skuNo,(skuPriceInfoDetailResponseDto.getTerminalPrice() == null? "0" : skuPriceInfoDetailResponseDto.getTerminalPrice().toPlainString())+"(终端价)");
                    skuNoAndPriceMap.put(skuPriceInfoDetailResponseDto.getSkuNo()+"_tagName",
                            skuPriceInfoDetailResponseDto.getTerminalPrice() == null? "" : "终端");
                    skuNoAndPriceMap.put(skuPriceInfoDetailResponseDto.getSkuNo()+"_price",
                            skuPriceInfoDetailResponseDto.getTerminalPrice() == null? "" : skuPriceInfoDetailResponseDto.getTerminalPrice().toPlainString());
                }

                continue;
            }
            String defaultSalePrice = goodSalePrice.getDefaultSalePrice() == null ? "" : goodSalePrice.getDefaultSalePrice().toPlainString();
            //都没有 就设置原来的价格
            skuNoAndPriceMap.put(skuNo,defaultSalePrice);
            skuNoAndPriceMap.put(skuNo+"_tagName",
                    isTraderCustomer?"经销":"终端");
            skuNoAndPriceMap.put(skuNo+"_price",
                    defaultSalePrice);
        }
        return skuNoAndPriceMap;
    }

    /**
     * @param priceInfoResponseDtos
     * @return
     */
    public Map<String,String> dealWithGoodsSalePrice(Integer traderId,List<GoodSalePrice> goodSalePriceList,
                                                     List<PriceInfoResponseDto> priceInfoResponseDtos) {

        Map<String,String> skuNoAndPriceMap = new HashMap<>();

        if(CollectionUtils.isEmpty(goodSalePriceList)||CollectionUtils.isEmpty(priceInfoResponseDtos)){
            return skuNoAndPriceMap;
        }
        boolean isTraderCustomer = false;//是否经销商
        TraderCustomer traderCustomer = this.traderCustomerService.getTraderCustomerId(traderId);
        if(traderCustomer != null &&  ErpConst.CUSTOME_RNATURE.equals(traderCustomer.getCustomerNature())){
            isTraderCustomer = true;
        }

        for(PriceInfoResponseDto priceInfoResponseDto : priceInfoResponseDtos){

            String skuNo = priceInfoResponseDto.getSkuNo();

            //有合约价 就设置合约价
            if(PriceType.CONTRACT_PRICE == priceInfoResponseDto.getPriceType()){
                skuNoAndPriceMap.put(priceInfoResponseDto.getSkuNo(),
                        priceInfoResponseDto.getContractPrice() == null? "" : priceInfoResponseDto.getContractPrice().toPlainString() + "(合约价)");
                skuNoAndPriceMap.put(priceInfoResponseDto.getSkuNo()+"_tagName",
                        priceInfoResponseDto.getContractPrice() == null? "" : "合约");
                skuNoAndPriceMap.put(priceInfoResponseDto.getSkuNo()+"_price",
                        priceInfoResponseDto.getContractPrice() == null? "" : priceInfoResponseDto.getContractPrice().toPlainString());
                continue;
            }

            //已经核价就设置销售价
            if(PriceType.SALE_PRICE == priceInfoResponseDto.getPriceType()){

                //分销商
                if(ErpConst.CUSTOME_RNATURE.equals(traderCustomer.getCustomerNature())){

                    skuNoAndPriceMap.put(skuNo,priceInfoResponseDto.getDistributionPrice().toPlainString()+"(经销价)");
                    skuNoAndPriceMap.put(priceInfoResponseDto.getSkuNo()+"_tagName",
                            priceInfoResponseDto.getDistributionPrice() == null? "" : "经销");
                    skuNoAndPriceMap.put(priceInfoResponseDto.getSkuNo()+"_price",
                            priceInfoResponseDto.getDistributionPrice() == null? "" : priceInfoResponseDto.getDistributionPrice().toPlainString());
                }

                //终端
                if(ErpConst.CUSTOME_INTERMIAL.equals(traderCustomer.getCustomerNature())){
                    skuNoAndPriceMap.put(skuNo,priceInfoResponseDto.getTerminalPrice().toPlainString()+"(终端价)");
                    skuNoAndPriceMap.put(priceInfoResponseDto.getSkuNo()+"_tagName",
                            priceInfoResponseDto.getTerminalPrice() == null? "" : "终端");
                    skuNoAndPriceMap.put(priceInfoResponseDto.getSkuNo()+"_price",
                            priceInfoResponseDto.getTerminalPrice() == null? "" : priceInfoResponseDto.getTerminalPrice().toPlainString());
                }

                continue;
            }

            //都没有 就设置原来的价格
            String defaultSalePrice = findDefalutSalePrice(goodSalePriceList,skuNo);
            skuNoAndPriceMap.put(skuNo,defaultSalePrice);
            skuNoAndPriceMap.put(priceInfoResponseDto.getSkuNo()+"_tagName",
                    isTraderCustomer?"经销":"终端");
            skuNoAndPriceMap.put(priceInfoResponseDto.getSkuNo()+"_price",
                    defaultSalePrice);

        }

        return skuNoAndPriceMap;
    }

    /**
     * 处理销售单的成本价
     * @param saleorderGoodList
     * @param priceInfoResponseDtos
     */
    @Override
    public void dealWithCostPrice(List<SaleorderGoods> saleorderGoodList, List<PriceInfoResponseDto> priceInfoResponseDtos) {

        if(CollectionUtils.isEmpty(saleorderGoodList)){
            return;
        }

        for(SaleorderGoods saleorderGood : saleorderGoodList){

            //对于运费或者参考成本不为0的产品无需判断无需判断
            if("V127063".equals(saleorderGood.getSku()) || BigDecimal.ZERO.compareTo(saleorderGood.getReferenceCostPrice()) != 0){
                continue;
            }

            PriceInfoResponseDto priceInfoResponseDto = findSkuPriceInfoBySkuNo(priceInfoResponseDtos,saleorderGood.getSku());

            //未核价无需判断
            if(priceInfoResponseDto == null || priceInfoResponseDto.getPriceType() != PriceType.SALE_PRICE) {
                continue;
            }

            SkuPriceInfoPurchaseDto skuPriceInfoPurchaseDto = priceInfoResponseDto.getPurchaseList().stream()
                    .max((e1, e2) -> {
                        if(e1.getModTime().compareTo(e2.getModTime()) > 0)
                        {
                            return 1;
                        }else if(e1.getModTime().compareTo(e1.getModTime()) < 0){
                            return -1;
                        }else{
                            if(e1.getPurchasePrice().compareTo(e1.getPurchasePrice()) >= 0){
                                return 1;
                            }else{
                                return -1;
                            }
                        }
                    })
                    .orElseGet(null);

            //没有采购价无需判断
            if(skuPriceInfoPurchaseDto == null){
                continue;
            }

            //设置成本价
            saleorderGood.setReferenceCostPrice(skuPriceInfoPurchaseDto.getPurchasePrice());
        }
    }

    private PriceInfoResponseDto findSkuPriceInfoBySkuNo(List<PriceInfoResponseDto> priceInfoResponseDtos,String skuNo) {
        if(CollectionUtils.isEmpty(priceInfoResponseDtos)){
            return new PriceInfoResponseDto();
        }
        return priceInfoResponseDtos.stream()
                .filter(priceInfoResponseDto -> priceInfoResponseDto.getSkuNo().equals(skuNo))
                .findFirst()
                .orElse(null);
    }

    /**
     * 查询默认的销售价
     * @param goodSalePriceList
     */
    private String findDefalutSalePrice(List<GoodSalePrice> goodSalePriceList,String skuNo){
        try {
            return goodSalePriceList.stream()
                    .filter(goodSalePrice -> goodSalePrice.getSkuNo().equals(skuNo))
                    .findFirst()
                    .map(
                            goodSalePrice -> {
                                return goodSalePrice.getDefaultSalePrice() == null ? "" : goodSalePrice.getDefaultSalePrice().toPlainString();
                            }
                    )
                    .orElse("");
        }catch (Exception e){
            logger.error("",e);
            return "";
        }
    }

}
