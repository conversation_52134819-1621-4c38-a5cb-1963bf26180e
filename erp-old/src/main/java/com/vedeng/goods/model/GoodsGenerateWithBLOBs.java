package com.vedeng.goods.model;

public class GoodsGenerateWithBLOBs extends GoodsGenerate {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.TECHNICAL_PARAMETER
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private String technicalParameter;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.PERFORMANCE_PARAMETER
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private String performanceParameter;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.SPEC_PARAMETER
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private String specParameter;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.TECHNICAL_PARAMETER
     *
     * @return the value of T_GOODS.TECHNICAL_PARAMETER
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public String getTechnicalParameter() {
        return technicalParameter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.TECHNICAL_PARAMETER
     *
     * @param technicalParameter the value for T_GOODS.TECHNICAL_PARAMETER
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setTechnicalParameter(String technicalParameter) {
        this.technicalParameter = technicalParameter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.PERFORMANCE_PARAMETER
     *
     * @return the value of T_GOODS.PERFORMANCE_PARAMETER
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public String getPerformanceParameter() {
        return performanceParameter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.PERFORMANCE_PARAMETER
     *
     * @param performanceParameter the value for T_GOODS.PERFORMANCE_PARAMETER
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setPerformanceParameter(String performanceParameter) {
        this.performanceParameter = performanceParameter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.SPEC_PARAMETER
     *
     * @return the value of T_GOODS.SPEC_PARAMETER
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public String getSpecParameter() {
        return specParameter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.SPEC_PARAMETER
     *
     * @param specParameter the value for T_GOODS.SPEC_PARAMETER
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setSpecParameter(String specParameter) {
        this.specParameter = specParameter;
    }
}